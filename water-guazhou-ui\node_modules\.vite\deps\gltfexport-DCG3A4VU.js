import {
  g,
  i,
  o as o4,
  s as s2
} from "./chunk-MDHXGN24.js";
import {
  t as t3
} from "./chunk-F4KVXA42.js";
import "./chunk-6ZZUUGXX.js";
import {
  c
} from "./chunk-GJMH67CL.js";
import {
  L,
  M
} from "./chunk-ELJWWHWE.js";
import "./chunk-B42W3AOR.js";
import "./chunk-6ESVG4YL.js";
import "./chunk-UQUDWTCY.js";
import "./chunk-IKOX2HGY.js";
import "./chunk-3KCCETWY.js";
import {
  C,
  D
} from "./chunk-4M3AMTD4.js";
import "./chunk-57ER3SHX.js";
import "./chunk-ST2RRB55.js";
import {
  K,
  k
} from "./chunk-ZIKXCGU7.js";
import {
  e as e2,
  o as o3,
  r as r3
} from "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import "./chunk-A7PY25IH.js";
import "./chunk-SROTSYJS.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-XVA5SA7P.js";
import {
  lt
} from "./chunk-U4SVMKOQ.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import {
  F as F2,
  _,
  e,
  o as o2,
  z
} from "./chunk-MQAXMQFG.js";
import {
  f,
  l,
  n,
  t as t2
} from "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import {
  r as r2
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  E
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import {
  F
} from "./chunk-GZGAQUSK.js";
import {
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/glb.js
var t4;
!function(t5) {
  t5[t5.JSON = 1313821514] = "JSON", t5[t5.BIN = 5130562] = "BIN";
}(t4 || (t4 = {}));
var e3 = class _e {
  constructor(i3, r6) {
    if (!i3) throw new Error("GLB requires a JSON gltf chunk");
    this._length = _e.HEADER_SIZE, this._length += _e.CHUNK_HEADER_SIZE;
    const n2 = this._textToArrayBuffer(i3);
    if (this._length += this._alignTo(n2.byteLength, 4), r6 && (this._length += _e.CHUNK_HEADER_SIZE, this._length += r6.byteLength, r6.byteLength % 4)) throw new Error("Expected BIN chunk length to be divisible by 4 at this point");
    this.buffer = new ArrayBuffer(this._length), this._outView = new DataView(this.buffer), this._writeHeader();
    const h2 = this._writeChunk(n2, 12, t4.JSON, 32);
    r6 && this._writeChunk(r6, h2, t4.BIN);
  }
  _writeHeader() {
    this._outView.setUint32(0, _e.MAGIC, true), this._outView.setUint32(4, _e.VERSION, true), this._outView.setUint32(8, this._length, true);
  }
  _writeChunk(t5, e4, i3, r6 = 0) {
    const n2 = this._alignTo(t5.byteLength, 4);
    for (this._outView.setUint32(e4, n2, true), this._outView.setUint32(e4 += 4, i3, true), this._writeArrayBuffer(this._outView.buffer, t5, e4 += 4, 0, t5.byteLength), e4 += t5.byteLength; e4 % 4; ) r6 && this._outView.setUint8(e4, r6), e4++;
    return e4;
  }
  _writeArrayBuffer(t5, e4, i3, r6, n2) {
    new Uint8Array(t5, i3, n2).set(new Uint8Array(e4, r6, n2), 0);
  }
  _textToArrayBuffer(t5) {
    return new TextEncoder().encode(t5).buffer;
  }
  _alignTo(t5, e4) {
    return e4 * Math.ceil(t5 / e4);
  }
};
e3.HEADER_SIZE = 12, e3.CHUNK_HEADER_SIZE = 8, e3.MAGIC = 1179937895, e3.VERSION = 2;

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/types.js
var E2;
var A;
var R;
var L2;
var o5;
var I;
var N;
!function(E3) {
  E3[E3.External = 0] = "External", E3[E3.DataURI = 1] = "DataURI", E3[E3.GLB = 2] = "GLB";
}(E2 || (E2 = {})), function(E3) {
  E3[E3.External = 0] = "External", E3[E3.DataURI = 1] = "DataURI", E3[E3.GLB = 2] = "GLB";
}(A || (A = {})), function(E3) {
  E3[E3.ARRAY_BUFFER = 34962] = "ARRAY_BUFFER", E3[E3.ELEMENT_ARRAY_BUFFER = 34963] = "ELEMENT_ARRAY_BUFFER";
}(R || (R = {})), function(E3) {
  E3.SCALAR = "SCALAR", E3.VEC2 = "VEC2", E3.VEC3 = "VEC3", E3.VEC4 = "VEC4", E3.MAT2 = "MAT2", E3.MAT3 = "MAT3", E3.MAT4 = "MAT4";
}(L2 || (L2 = {})), function(E3) {
  E3[E3.POINTS = 0] = "POINTS", E3[E3.LINES = 1] = "LINES", E3[E3.LINE_LOOP = 2] = "LINE_LOOP", E3[E3.LINE_STRIP = 3] = "LINE_STRIP", E3[E3.TRIANGLES = 4] = "TRIANGLES", E3[E3.TRIANGLE_STRIP = 5] = "TRIANGLE_STRIP", E3[E3.TRIANGLE_FAN = 6] = "TRIANGLE_FAN";
}(o5 || (o5 = {})), function(E3) {
  E3.OPAQUE = "OPAQUE", E3.MASK = "MASK", E3.BLEND = "BLEND";
}(I || (I = {})), function(E3) {
  E3[E3.NoColor = 0] = "NoColor", E3[E3.FaceColor = 1] = "FaceColor", E3[E3.VertexColor = 2] = "VertexColor";
}(N || (N = {}));

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/bufferview.js
var r4 = class {
  constructor(e4, s5, i3, r6, n2) {
    this._buffer = e4, this._componentType = i3, this._dataType = r6, this._data = [], this._isFinalized = false, this._accessorIndex = -1, this._accessorAttribute = null, this._accessorMin = null, this._accessorMax = null, s5.bufferViews || (s5.bufferViews = []), this.index = s5.bufferViews.length, this._bufferView = { buffer: e4.index, byteLength: -1, target: n2 };
    const a = this._getElementSize();
    a >= 4 && n2 !== R.ELEMENT_ARRAY_BUFFER && (this._bufferView.byteStride = a), s5.bufferViews.push(this._bufferView), this._numComponentsForDataType = this._calculateNumComponentsForDataType();
  }
  push(e4) {
    const t5 = this._data.length;
    if (this._data.push(e4), this._accessorIndex >= 0) {
      const s5 = t5 % this._numComponentsForDataType, i3 = this._accessorMin[s5];
      this._accessorMin[s5] = "number" != typeof i3 ? e4 : Math.min(i3, e4);
      const r6 = this._accessorMax[s5];
      this._accessorMax[s5] = "number" != typeof r6 ? e4 : Math.max(r6, e4);
    }
  }
  get dataSize() {
    return this._data.length * this._sizeComponentType();
  }
  get byteSize() {
    function e4(e5, t5) {
      return t5 * Math.ceil(e5 / t5);
    }
    return e4(this.dataSize, 4);
  }
  getByteOffset() {
    if (!this._isFinalized) throw new Error("Cannot get BufferView offset until it is finalized");
    return this._buffer.getByteOffset(this);
  }
  get byteOffset() {
    if (!this._isFinalized) throw new Error("Cannot get BufferView offset until it is finalized");
    return this._buffer.getByteOffset(this);
  }
  _createTypedArray(e4, t5) {
    switch (this._componentType) {
      case C.BYTE:
        return new Int8Array(e4, t5);
      case C.FLOAT:
        return new Float32Array(e4, t5);
      case C.SHORT:
        return new Int16Array(e4, t5);
      case C.UNSIGNED_BYTE:
        return new Uint8Array(e4, t5);
      case C.UNSIGNED_INT:
        return new Uint32Array(e4, t5);
      case C.UNSIGNED_SHORT:
        return new Uint16Array(e4, t5);
    }
  }
  writeOutToBuffer(e4, t5) {
    this._createTypedArray(e4, t5).set(this._data);
  }
  writeAsync(e4) {
    if (this._asyncWritePromise) throw new Error("Can't write multiple bufferView values asynchronously");
    return this._asyncWritePromise = e4.then((e5) => {
      const t5 = new Uint8Array(e5);
      for (let s5 = 0; s5 < t5.length; ++s5) this._data.push(t5[s5]);
      delete this._asyncWritePromise;
    }), this._asyncWritePromise;
  }
  startAccessor(e4) {
    if (this._accessorIndex >= 0) throw new Error("Accessor was started without ending the previous one");
    this._accessorIndex = this._data.length, this._accessorAttribute = e4;
    const t5 = this._numComponentsForDataType;
    this._accessorMin = new Array(t5), this._accessorMax = new Array(t5);
  }
  endAccessor() {
    if (this._accessorIndex < 0) throw new Error("An accessor was not started, but was attempted to be ended");
    const e4 = this._getElementSize(), t5 = this._numComponentsForDataType, s5 = (this._data.length - this._accessorIndex) / t5;
    if (s5 % 1) throw new Error("An accessor was ended with missing component values");
    for (let i3 = 0; i3 < this._accessorMin.length; ++i3) "number" != typeof this._accessorMin[i3] && (this._accessorMin[i3] = 0), "number" != typeof this._accessorMax[i3] && (this._accessorMax[i3] = 0);
    const r6 = { byteOffset: e4 * (this._accessorIndex / t5), componentType: this._componentType, count: s5, type: this._dataType, min: this._accessorMin, max: this._accessorMax, name: this._accessorAttribute };
    switch (this._accessorAttribute) {
      case "TEXCOORD_0":
      case "TEXCOORD_1":
      case "COLOR_0":
      case "WEIGHTS_0":
        switch (this._componentType) {
          case C.UNSIGNED_BYTE:
          case C.UNSIGNED_SHORT:
            r6.normalized = true;
        }
    }
    return this._accessorIndex = -1, this._accessorAttribute = null, this._accessorMin = null, this._accessorMax = null, r6;
  }
  get finalized() {
    return this._finalizedPromise ? this._finalizedPromise : this._isFinalized ? this._finalizedPromise = Promise.resolve() : this._finalizedPromise = new Promise((e4) => this._finalizedPromiseResolve = e4);
  }
  finalize() {
    const t5 = this._bufferView;
    return new Promise((t6) => {
      const s5 = this._buffer.getViewFinalizePromises(this);
      this._asyncWritePromise && s5.push(this._asyncWritePromise), t6(E(s5));
    }).then(() => {
      this._isFinalized = true, t5.byteOffset = this.getByteOffset(), t5.byteLength = this.dataSize, this._finalizedPromiseResolve && this._finalizedPromiseResolve();
    });
  }
  _getElementSize() {
    return this._sizeComponentType() * this._numComponentsForDataType;
  }
  _sizeComponentType() {
    switch (this._componentType) {
      case C.BYTE:
      case C.UNSIGNED_BYTE:
        return 1;
      case C.SHORT:
      case C.UNSIGNED_SHORT:
        return 2;
      case C.UNSIGNED_INT:
      case C.FLOAT:
        return 4;
    }
  }
  _calculateNumComponentsForDataType() {
    switch (this._dataType) {
      case L2.SCALAR:
        return 1;
      case L2.VEC2:
        return 2;
      case L2.VEC3:
        return 3;
      case L2.VEC4:
      case L2.MAT2:
        return 4;
      case L2.MAT3:
        return 9;
      case L2.MAT4:
        return 16;
    }
  }
};

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/buffer.js
var r5 = class {
  constructor(e4) {
    this._gltf = e4, this._bufferViews = [], this._isFinalized = false, e4.buffers || (e4.buffers = []), this.index = e4.buffers.length;
    const i3 = { byteLength: -1 };
    e4.buffers.push(i3), this._buffer = i3;
  }
  addBufferView(e4, r6, t5) {
    if (this._finalizePromise) throw new Error("Cannot add buffer view after fiinalizing buffer");
    const f5 = new r4(this, this._gltf, e4, r6, t5);
    return this._bufferViews.push(f5), f5;
  }
  getByteOffset(e4) {
    let i3 = 0;
    for (const r6 of this._bufferViews) {
      if (r6 === e4) return i3;
      i3 += r6.byteSize;
    }
    throw new Error("Given bufferView was not present in this buffer");
  }
  getViewFinalizePromises(e4) {
    const i3 = [];
    for (const r6 of this._bufferViews) {
      if (e4 && r6 === e4) return i3;
      i3.push(r6.finalized);
    }
    return i3;
  }
  getArrayBuffer() {
    if (!this._isFinalized) throw new Error("Cannot get ArrayBuffer from Buffer before it is finalized");
    const e4 = this._getTotalSize(), i3 = new ArrayBuffer(e4);
    let r6 = 0;
    for (const t5 of this._bufferViews) t5.writeOutToBuffer(i3, r6), r6 += t5.byteSize;
    return i3;
  }
  finalize() {
    var _a;
    if (this._finalizePromise) throw new Error(`Buffer ${this.index} was already finalized`);
    return this._finalizePromise = new Promise((i3) => {
      i3(E(this.getViewFinalizePromises()));
    }).then(() => {
      this._isFinalized = true;
      const e4 = this.getArrayBuffer();
      this._buffer.byteLength = e4.byteLength, this._buffer.uri = e4;
    }), (_a = this._gltf.extras) == null ? void 0 : _a.promises.push(this._finalizePromise), this._finalizePromise;
  }
  _getTotalSize() {
    let e4 = 0;
    for (const i3 of this._bufferViews) e4 += i3.byteSize;
    return e4;
  }
};

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/geometry.js
function f2(t5, n2) {
  if (t5.components) for (const e4 of t5.components) e4.faces && "smooth" === e4.shading && c2(e4, n2);
}
function c2(n2, a) {
  t(a.normal) && (a.normal = new Float32Array(a.position.length));
  const i3 = n2.faces, { position: f5, normal: c3 } = a, m3 = i3.length / 3;
  for (let t5 = 0; t5 < m3; ++t5) {
    const n3 = 3 * i3[3 * t5 + 0], s5 = 3 * i3[3 * t5 + 1], a2 = 3 * i3[3 * t5 + 2], m4 = o2(l2, f5[n3 + 0], f5[n3 + 1], f5[n3 + 2]), h2 = o2(p, f5[s5 + 0], f5[s5 + 1], f5[s5 + 2]), g2 = o2(x, f5[a2 + 0], f5[a2 + 1], f5[a2 + 2]), u2 = e(h2, h2, m4), j = e(g2, g2, m4), y = _(u2, u2, j);
    c3[n3 + 0] += y[0], c3[n3 + 1] += y[1], c3[n3 + 2] += y[2], c3[s5 + 0] += y[0], c3[s5 + 1] += y[1], c3[s5 + 2] += y[2], c3[a2 + 0] += y[0], c3[a2 + 1] += y[1], c3[a2 + 2] += y[2];
  }
  for (let t5 = 0; t5 < c3.length; t5 += 3) o2(h, c3[t5], c3[t5 + 1], c3[t5 + 2]), z(h, h), c3[t5 + 0] = h[0], c3[t5 + 1] = h[1], c3[t5 + 2] = h[2];
}
function m(t5) {
  if (r(t5.transform)) return t5.transform.getOriginPoint(t5.spatialReference);
  const e4 = t5.extent.xmax - t5.extent.width / 2, o6 = t5.extent.ymax - t5.extent.height / 2, r6 = t5.extent.zmin;
  return new w({ x: e4, y: o6, z: r6, spatialReference: t5.extent.spatialReference });
}
var l2 = n();
var p = n();
var x = n();
var h = n();

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/gltf.js
var B = s.getLogger("gltf");
var C2 = class {
  constructor(e4, t5, s5) {
    this.params = {}, this._materialMap = new Array(), this._imageMap = /* @__PURE__ */ new Map(), this._textureMap = /* @__PURE__ */ new Map(), this.gltf = { asset: { version: "2.0", copyright: e4.copyright, generator: e4.generator }, extras: { options: t5, binChunkBuffer: null, promises: [] } }, s5 && (this.params = s5), this._addScenes(e4);
  }
  _addScenes(e4) {
    this.gltf.scene = e4.defaultScene;
    const t5 = this.gltf.extras, s5 = t5.options.bufferOutputType === E2.GLB || t5.options.imageOutputType === A.GLB;
    s5 && (t5.binChunkBuffer = new r5(this.gltf)), e4.forEachScene((e5) => {
      this._addScene(e5);
    }), s5 && t5.binChunkBuffer.finalize();
  }
  _addScene(e4) {
    this.gltf.scenes || (this.gltf.scenes = []);
    const t5 = {};
    e4.name && (t5.name = e4.name), e4.forEachNode((e5) => {
      t5.nodes || (t5.nodes = []);
      const s5 = this._addNode(e5);
      t5.nodes.push(s5);
    }), this.gltf.scenes.push(t5);
  }
  _addNode(e4) {
    this.gltf.nodes || (this.gltf.nodes = []);
    const t5 = {};
    e4.name && (t5.name = e4.name);
    const s5 = e4.translation;
    F2(s5, f) || (t5.translation = t2(s5));
    const r6 = e4.rotation;
    K(r6, o3) || (t5.rotation = r3(r6));
    const i3 = e4.scale;
    F2(i3, l) || (t5.scale = t2(i3)), e4.mesh && e4.mesh.vertexAttributes.position ? t5.mesh = this._addMesh(e4.mesh) : e4.forEachNode((e5) => {
      t5.children || (t5.children = []);
      const s6 = this._addNode(e5);
      t5.children.push(s6);
    });
    const u2 = this.gltf.nodes.length;
    return this.gltf.nodes.push(t5), u2;
  }
  _addMesh(e4) {
    this.gltf.meshes || (this.gltf.meshes = []);
    const t5 = { primitives: [] }, i3 = this.gltf.extras, a = i3.options.bufferOutputType === E2.GLB;
    let o6;
    o6 = a ? i3.binChunkBuffer : new r5(this.gltf), this.params.origin || (this.params.origin = m(e4));
    const n2 = this.params.ignoreLocalTransform ? o(e4.transform, (e5) => new L({ origin: [e5.origin[0], e5.origin[1], e5.origin[2]], geographic: false })) : e4.transform, l3 = M(e4.vertexAttributes, n2, this.params.origin, { geographic: this.params.geographic, unit: "meters" });
    f2(e4, l3), this._flipYZAxis(l3);
    const c3 = o6.addBufferView(C.FLOAT, L2.VEC3, R.ARRAY_BUFFER);
    let h2, f5, u2, A2;
    l3.normal && (h2 = o6.addBufferView(C.FLOAT, L2.VEC3, R.ARRAY_BUFFER)), e4.vertexAttributes.uv && (f5 = o6.addBufferView(C.FLOAT, L2.VEC2, R.ARRAY_BUFFER)), l3.tangent && (u2 = o6.addBufferView(C.FLOAT, L2.VEC4, R.ARRAY_BUFFER)), e4.vertexAttributes.color && (A2 = o6.addBufferView(C.UNSIGNED_BYTE, L2.VEC4, R.ARRAY_BUFFER)), c3.startAccessor("POSITION"), h2 && h2.startAccessor("NORMAL"), f5 && f5.startAccessor("TEXCOORD_0"), u2 && u2.startAccessor("TANGENT"), A2 && A2.startAccessor("COLOR_0");
    const b = l3.position.length / 3, { position: E3, normal: M2, tangent: O } = l3, { color: N2, uv: B2 } = e4.vertexAttributes;
    for (let s5 = 0; s5 < b; ++s5) c3.push(E3[3 * s5 + 0]), c3.push(E3[3 * s5 + 1]), c3.push(E3[3 * s5 + 2]), h2 && r(M2) && (h2.push(M2[3 * s5 + 0]), h2.push(M2[3 * s5 + 1]), h2.push(M2[3 * s5 + 2])), f5 && r(B2) && (f5.push(B2[2 * s5 + 0]), f5.push(B2[2 * s5 + 1])), u2 && r(O) && (u2.push(O[4 * s5 + 0]), u2.push(O[4 * s5 + 1]), u2.push(O[4 * s5 + 2]), u2.push(O[4 * s5 + 3])), A2 && r(N2) && (A2.push(N2[4 * s5 + 0]), A2.push(N2[4 * s5 + 1]), A2.push(N2[4 * s5 + 2]), A2.push(N2[4 * s5 + 3]));
    const C3 = c3.endAccessor(), I2 = this._addAccessor(c3.index, C3);
    let L3, v, y, F3, S;
    if (h2) {
      const e5 = h2.endAccessor();
      L3 = this._addAccessor(h2.index, e5);
    }
    if (f5) {
      const e5 = f5.endAccessor();
      v = this._addAccessor(f5.index, e5);
    }
    if (u2) {
      const e5 = u2.endAccessor();
      y = this._addAccessor(u2.index, e5);
    }
    if (A2) {
      const e5 = A2.endAccessor();
      F3 = this._addAccessor(A2.index, e5);
    }
    e4.components && e4.components.length > 0 && e4.components[0].faces ? (S = o6.addBufferView(C.UNSIGNED_INT, L2.SCALAR, R.ELEMENT_ARRAY_BUFFER), this._addMeshVertexIndexed(S, e4.components, t5, I2, L3, v, y, F3)) : this._addMeshVertexNonIndexed(e4.components, t5, I2, L3, v, y, F3), c3.finalize(), h2 && h2.finalize(), f5 && f5.finalize(), u2 && u2.finalize(), S && S.finalize(), A2 && A2.finalize(), a || o6.finalize();
    const k2 = this.gltf.meshes.length;
    return this.gltf.meshes.push(t5), k2;
  }
  _flipYZAxis({ position: e4, normal: t5, tangent: s5 }) {
    this._flipYZBuffer(e4, 3), this._flipYZBuffer(t5, 3), this._flipYZBuffer(s5, 4);
  }
  _flipYZBuffer(e4, t5) {
    if (!t(e4)) for (let s5 = 1, r6 = 2; s5 < e4.length; s5 += t5, r6 += t5) {
      const t6 = e4[s5], i3 = e4[r6];
      e4[s5] = i3, e4[r6] = -t6;
    }
  }
  _addMaterial(e4) {
    if (null === e4) return;
    const t5 = this._materialMap.indexOf(e4);
    if (-1 !== t5) return t5;
    this.gltf.materials || (this.gltf.materials = []);
    const s5 = {};
    switch (e4.alphaMode) {
      case "mask":
        s5.alphaMode = I.MASK;
        break;
      case "auto":
      case "blend":
        s5.alphaMode = I.BLEND;
    }
    0.5 !== e4.alphaCutoff && (s5.alphaCutoff = e4.alphaCutoff), e4.doubleSided && (s5.doubleSided = e4.doubleSided), s5.pbrMetallicRoughness = {};
    const i3 = (e5) => e5 ** 2.1, a = (e5) => {
      const t6 = e5.toRgba();
      return t6[0] = i3(t6[0] / 255), t6[1] = i3(t6[1] / 255), t6[2] = i3(t6[2] / 255), t6;
    };
    if (r(e4.color) && (s5.pbrMetallicRoughness.baseColorFactor = a(e4.color)), r(e4.colorTexture) && (s5.pbrMetallicRoughness.baseColorTexture = { index: this._addTexture(e4.colorTexture) }), r(e4.normalTexture) && (s5.normalTexture = { index: this._addTexture(e4.normalTexture) }), e4 instanceof c) {
      if (r(e4.emissiveTexture) && (s5.emissiveTexture = { index: this._addTexture(e4.emissiveTexture) }), r(e4.emissiveColor)) {
        const t6 = a(e4.emissiveColor);
        s5.emissiveFactor = [t6[0], t6[1], t6[2]];
      }
      r(e4.occlusionTexture) && (s5.occlusionTexture = { index: this._addTexture(e4.occlusionTexture) }), r(e4.metallicRoughnessTexture) && (s5.pbrMetallicRoughness.metallicRoughnessTexture = { index: this._addTexture(e4.metallicRoughnessTexture) }), s5.pbrMetallicRoughness.metallicFactor = e4.metallic, s5.pbrMetallicRoughness.roughnessFactor = e4.roughness;
    } else s5.pbrMetallicRoughness.metallicFactor = 1, s5.pbrMetallicRoughness.roughnessFactor = 1, B.warnOnce("Meshes exported to GLTF without MeshMaterialMetallicRoughness material will appear different when imported back.");
    const o6 = this.gltf.materials.length;
    return this.gltf.materials.push(s5), this._materialMap.push(e4), o6;
  }
  _addTexture(e4) {
    const s5 = this.gltf.textures ?? [];
    return this.gltf.textures = s5, r2(this._textureMap, e4, () => {
      const t5 = { sampler: this._addSampler(e4), source: this._addImage(e4) }, r6 = s5.length;
      return s5.push(t5), r6;
    });
  }
  _addImage(e4) {
    const t5 = this._imageMap.get(e4);
    if (null != t5) return t5;
    this.gltf.images || (this.gltf.images = []);
    const s5 = {};
    if (e4.url) s5.uri = e4.url;
    else {
      const t6 = e4.data;
      s5.extras = t6;
      for (let e5 = 0; e5 < this.gltf.images.length; ++e5) if (t6 === this.gltf.images[e5].extras) return e5;
      const i4 = this.gltf.extras;
      switch (i4.options.imageOutputType) {
        case A.GLB: {
          const e5 = i4.binChunkBuffer.addBufferView(C.UNSIGNED_BYTE, L2.SCALAR);
          if (t3(t6)) r(t6.data) && e5.writeOutToBuffer(t6.data, 0);
          else {
            const r6 = o4(t6).then(({ data: e6, type: t7 }) => (s5.mimeType = t7, e6));
            e5.writeAsync(r6).then(() => {
              e5.finalize();
            });
          }
          s5.bufferView = e5.index;
          break;
        }
        case A.DataURI:
          if (t3(t6)) {
            B.warnOnce("Image export for basis compressed textures not available.");
            break;
          }
          s5.uri = i(t6);
          break;
        default:
          if (t3(t6)) {
            B.warnOnce("Image export for basis compressed textures not available.");
            break;
          }
          i4.promises.push(o4(t6).then(({ data: e5, type: t7 }) => {
            s5.uri = e5, s5.mimeType = t7;
          }));
      }
    }
    const i3 = this.gltf.images.length;
    return this.gltf.images.push(s5), this._imageMap.set(e4, i3), i3;
  }
  _addSampler(e4) {
    this.gltf.samplers || (this.gltf.samplers = []);
    let t5 = D.REPEAT, s5 = D.REPEAT;
    if ("string" == typeof e4.wrap) switch (e4.wrap) {
      case "clamp":
        t5 = D.CLAMP_TO_EDGE, s5 = D.CLAMP_TO_EDGE;
        break;
      case "mirror":
        t5 = D.MIRRORED_REPEAT, s5 = D.MIRRORED_REPEAT;
    }
    else {
      switch (e4.wrap.vertical) {
        case "clamp":
          s5 = D.CLAMP_TO_EDGE;
          break;
        case "mirror":
          s5 = D.MIRRORED_REPEAT;
      }
      switch (e4.wrap.horizontal) {
        case "clamp":
          t5 = D.CLAMP_TO_EDGE;
          break;
        case "mirror":
          t5 = D.MIRRORED_REPEAT;
      }
    }
    const r6 = { wrapS: t5, wrapT: s5 };
    for (let a = 0; a < this.gltf.samplers.length; ++a) if (JSON.stringify(r6) === JSON.stringify(this.gltf.samplers[a])) return a;
    const i3 = this.gltf.samplers.length;
    return this.gltf.samplers.push(r6), i3;
  }
  _addAccessor(e4, t5) {
    this.gltf.accessors || (this.gltf.accessors = []);
    const s5 = { bufferView: e4, byteOffset: t5.byteOffset, componentType: t5.componentType, count: t5.count, type: t5.type, min: t5.min, max: t5.max, name: t5.name };
    t5.normalized && (s5.normalized = true);
    const r6 = this.gltf.accessors.length;
    return this.gltf.accessors.push(s5), r6;
  }
  _addMeshVertexIndexed(e4, t5, s5, r6, i3, a, o6, n2) {
    for (const l3 of t5) {
      e4.startAccessor("INDICES");
      for (let s6 = 0; s6 < l3.faces.length; ++s6) e4.push(l3.faces[s6]);
      const t6 = e4.endAccessor(), c3 = { attributes: { POSITION: r6 }, indices: this._addAccessor(e4.index, t6), material: this._addMaterial(l3.material) };
      i3 && "flat" !== l3.shading && (c3.attributes.NORMAL = i3), a && (c3.attributes.TEXCOORD_0 = a), o6 && "flat" !== l3.shading && (c3.attributes.TANGENT = o6), n2 && (c3.attributes.COLOR_0 = n2), s5.primitives.push(c3);
    }
  }
  _addMeshVertexNonIndexed(e4, t5, s5, r6, i3, a, o6) {
    const n2 = { attributes: { POSITION: s5 } };
    r6 && (n2.attributes.NORMAL = r6), i3 && (n2.attributes.TEXCOORD_0 = i3), a && (n2.attributes.TANGENT = a), o6 && (n2.attributes.COLOR_0 = o6), e4 && (n2.material = this._addMaterial(e4[0].material)), t5.primitives.push(n2);
  }
};

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/asset.js
var s3 = class {
  constructor() {
    this.copyright = "", this.defaultScene = 0, this.generator = "", this._scenes = [];
  }
  addScene(e4) {
    if (this._scenes.includes(e4)) throw new Error("Scene already added");
    this._scenes.push(e4);
  }
  removeScene(s5) {
    F(this._scenes, s5);
  }
  forEachScene(e4) {
    this._scenes.forEach(e4);
  }
};

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/scene.js
var d = class {
  constructor() {
    this.name = "", this._nodes = [];
  }
  addNode(d2) {
    if (this._nodes.includes(d2)) throw new Error("Node already added");
    this._nodes.push(d2);
  }
  forEachNode(d2) {
    this._nodes.forEach(d2);
  }
};

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/node.js
var i2 = class {
  constructor(s5) {
    this.mesh = s5, this.name = "", this.translation = n(), this.rotation = e2(), this.scale = t2(l), this._nodes = [];
  }
  addNode(s5) {
    if (this._nodes.includes(s5)) throw new Error("Node already added");
    this._nodes.push(s5);
  }
  forEachNode(s5) {
    this._nodes.forEach(s5);
  }
  set rotationAngles(t5) {
    k(this.rotation, t5[0], t5[1], t5[2]);
  }
};

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/index.js
var f3 = "model.gltf";
var u = "model.glb";
function p2(p3, m3, c3) {
  var _a;
  const l3 = new C2(p3, m3 = m3 || {}, c3);
  let g2 = l3.params;
  g2 ? g2.origin || (g2.origin = new w({ x: -1, y: -1, z: -1 })) : g2 = { origin: new w({ x: -1, y: -1, z: -1 }) };
  const y = g2.origin, d2 = l3.gltf, j = ((_a = d2.extras) == null ? void 0 : _a.promises) ?? [];
  let x2 = 1, b = 1, h2 = null;
  return E(j).then(() => {
    const e4 = { origin: y };
    delete d2.extras;
    const t5 = "number" == typeof m3.jsonSpacing ? m3.jsonSpacing : 4, o6 = JSON.stringify(d2, (t6, r6) => {
      if ("extras" !== t6) {
        if (r6 instanceof ArrayBuffer) {
          if (g(r6)) switch (m3.imageOutputType) {
            case A.DataURI:
            case A.GLB:
              break;
            case A.External:
            default: {
              const t7 = `img${b}.png`;
              return b++, e4[t7] = r6, t7;
            }
          }
          switch (m3.bufferOutputType) {
            case E2.DataURI:
              return s2(r6);
            case E2.GLB:
              if (h2) throw new Error("Already encountered an ArrayBuffer, there should only be one in the GLB format.");
              return void (h2 = r6);
            case E2.External:
            default: {
              const t7 = `data${x2}.bin`;
              return x2++, e4[t7] = r6, t7;
            }
          }
        }
        return r6;
      }
    }, t5);
    return m3.bufferOutputType === E2.GLB || m3.imageOutputType === A.GLB ? e4[u] = new e3(o6, h2).buffer : e4[f3] = o6, e4;
  });
}
function m2(e4, t5) {
  return p2(e4, { bufferOutputType: E2.GLB, imageOutputType: A.GLB, jsonSpacing: 0 }, t5);
}

// node_modules/@arcgis/core/geometry/support/meshUtils/exporters/gltf/gltfexport.js
var s4 = class {
  constructor(e4, o6) {
    this._file = { type: "model/gltf-binary", data: e4 }, this.origin = o6;
  }
  buffer() {
    return Promise.resolve(this._file);
  }
  download(o6) {
    lt(new Blob([this._file.data], { type: this._file.type }), o6);
  }
};
function f4(e4, f5) {
  const d2 = new s3(), l3 = new d();
  return d2.addScene(l3), l3.addNode(new i2(e4)), m2(d2, f5).then((e5) => new s4(e5[u], e5.origin));
}
export {
  f4 as toBinaryGLTF
};
//# sourceMappingURL=gltfexport-DCG3A4VU.js.map
