<template>
  <div class="conservation-analysis">
    <!-- 分析控制面板 -->
    <el-card class="analysis-panel" shadow="never">
      <div class="panel-header">
        <h3>智能分析控制面板</h3>
        <p>通过原水液位变化反馈，引入智能化算法，分析地下水涵养水位，给出地下水涵养建议</p>
      </div>
      
      <el-form :model="analysisForm" ref="analysisFormRef" :inline="true" label-width="100px">
        <el-form-item label="选择测点" prop="stationId" required>
          <el-select
            v-model="analysisForm.stationId"
            placeholder="请选择测点"
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="station in stationList"
              :key="station.id"
              :label="station.name"
              :value="station.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分析周期" prop="timeRange" required>
          <el-date-picker
            v-model="analysisTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="x"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            @change="handleAnalysisTimeRangeChange"
            style="width: 350px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            icon="DataAnalysis"
            @click="handleStartAnalysis"
            :loading="analysisLoading"
            :disabled="!analysisForm.stationId || !isAnalysisTimeRangeValid"
          >
            开始分析
          </el-button>
          <el-button icon="Refresh" @click="resetAnalysisForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 分析结果概览 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="6">
        <el-card class="stat-card" shadow="never">
          <div class="stat-content">
            <div class="stat-value">{{ totalAnalysis }}</div>
            <div class="stat-label">总分析次数</div>
          </div>
          <div class="stat-icon">
            <el-icon><DataAnalysis /></el-icon>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card risk-low" shadow="never">
          <div class="stat-content">
            <div class="stat-value">{{ riskStats.low || 0 }}</div>
            <div class="stat-label">低风险</div>
          </div>
          <div class="stat-icon">
            <el-icon><SuccessFilled /></el-icon>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card risk-medium" shadow="never">
          <div class="stat-content">
            <div class="stat-value">{{ riskStats.medium || 0 }}</div>
            <div class="stat-label">中风险</div>
          </div>
          <div class="stat-icon">
            <el-icon><WarningFilled /></el-icon>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card risk-high" shadow="never">
          <div class="stat-content">
            <div class="stat-value">{{ riskStats.high || 0 }}</div>
            <div class="stat-label">高风险</div>
          </div>
          <div class="stat-icon">
            <el-icon><CircleCloseFilled /></el-icon>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="80px">
        <el-form-item label="测点名称" prop="stationId">
          <el-select
            v-model="queryParams.stationId"
            placeholder="请选择测点"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option label="全部测点" value="" />
            <el-option
              v-for="station in stationList"
              :key="station.id"
              :label="station.name"
              :value="station.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分析状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择分析状态" clearable style="width: 150px">
            <el-option label="分析中" :value="1" />
            <el-option label="已完成" :value="2" />
            <el-option label="分析失败" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="风险等级" prop="riskLevel">
          <el-select v-model="queryParams.riskLevel" placeholder="请选择风险等级" clearable style="width: 150px">
            <el-option label="低风险" :value="1" />
            <el-option label="中风险" :value="2" />
            <el-option label="高风险" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="分析时间" prop="createTime">
          <el-date-picker
            v-model="createTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="x"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 分析结果列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <div class="table-title">分析结果列表</div>
      </div>

      <el-table
        v-loading="loading"
        :data="analysisList"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="stationName" label="测点名称" width="120" />
        <el-table-column label="分析周期" width="300">
          <template #default="scope">
            {{ formatDateTime(scope.row.startTime) }} 至 {{ formatDateTime(scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="levelChange" label="水位变化(m)" width="120" align="center">
          <template #default="scope">
            <span :class="getLevelChangeClass(scope.row.levelChange)">
              {{ formatNumber(scope.row.levelChange) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="conservationPotential" label="涵养潜力" width="120" align="center">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.conservationPotential"
              :color="getPotentialColor(scope.row.conservationPotential)"
              :show-text="false"
              style="width: 80px"
            />
            <span style="margin-left: 8px">{{ formatNumber(scope.row.conservationPotential) }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getRiskLevelType(scope.row.riskLevel)">
              {{ getRiskLevelText(scope.row.riskLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="suggestedConservationAmount" label="建议涵养量(m³)" width="140" align="center">
          <template #default="scope">
            {{ formatNumber(scope.row.suggestedConservationAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="分析状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="分析时间" width="160" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="creatorName" label="分析人" width="100" align="center" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" link icon="View" @click="handleViewDetail(scope.row)">详情</el-button>
            <el-button 
              v-if="scope.row.status === 2" 
              type="warning" 
              link 
              icon="Refresh" 
              @click="handleReAnalysis(scope.row)"
            >
              重新分析
            </el-button>
            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 分析详情对话框 -->
    <AnalysisDetailDialog
      v-model:visible="detailVisible"
      :analysis-data="selectedAnalysis"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DataAnalysis, SuccessFilled, WarningFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import {
  performIntelligentAnalysis,
  getAnalysisList,
  deleteAnalysis,
  reAnalysis,
  getRiskLevelStatistics,
  type ConservationAnalysis,
  type AnalysisQueryParams
} from '@/api/waterSource/conservationWaterLevel'
import { getAllStations } from '@/api/station'
import AnalysisDetailDialog from './components/AnalysisDetailDialog.vue'
import { formatDate } from '@/utils/DateFormatter'
import { dwnai } from '@/utils/processNumber'

// 格式化函数
const formatDateTime = (date: string | number | Date | undefined) => {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
}

const formatNumber = (value: number | string | undefined, precision = 2) => {
  if (value === undefined || value === null || value === '') return '--'
  return dwnai(value)
}

// 响应式数据
const loading = ref(false)
const analysisLoading = ref(false)
const analysisList = ref<ConservationAnalysis[]>([])
const total = ref(0)
const totalAnalysis = ref(0)
const detailVisible = ref(false)
const selectedAnalysis = ref<ConservationAnalysis>({} as ConservationAnalysis)
const stationList = ref<any[]>([])
const riskStats = ref<any>({})

// 表单引用
const analysisFormRef = ref<FormInstance>()
const queryFormRef = ref<FormInstance>()

// 分析表单
const analysisForm = reactive({
  stationId: '',
  timeRange: null
})

// 分析时间范围
const analysisTimeRange = ref<[number, number] | []>([])

// 查询参数
const queryParams = reactive<AnalysisQueryParams>({
  pageNum: 1,
  pageSize: 10,
  stationId: '',
  status: undefined,
  riskLevel: undefined
})

// 创建时间范围
const createTimeRange = ref<[number, number] | null>(null)

// 计算属性
const createTimeComputed = computed(() => {
  if (createTimeRange.value) {
    return {
      startTime: createTimeRange.value[0],
      endTime: createTimeRange.value[1]
    }
  }
  return { startTime: undefined, endTime: undefined }
})

// 检查分析时间范围是否有效
const isAnalysisTimeRangeValid = computed(() => {
  return analysisTimeRange.value && Array.isArray(analysisTimeRange.value) && analysisTimeRange.value.length === 2
})

// 生命周期
onMounted(() => {
  loadStationList()
  getList()
  loadRiskStatistics()
})

// 加载测点列表
const loadStationList = async () => {
  try {
    const response = await getAllStations({ type: '水源地' })
    if (response.status === 200) {
      // 后端返回的是PageData格式，数据在data字段中
      stationList.value = response.data.data || []
    }
  } catch (error) {
    console.error('加载测点列表失败:', error)
    ElMessage.error('加载测点列表失败')
  }
}

// 处理分析时间范围变化
const handleAnalysisTimeRangeChange = (value: any) => {
  if (value && Array.isArray(value) && value.length === 2) {
    analysisTimeRange.value = [value[0], value[1]] as [number, number]
  } else {
    analysisTimeRange.value = []
  }
}

// 加载风险统计
const loadRiskStatistics = async () => {
  try {
    const response = await getRiskLevelStatistics()
    if (response.data.code === 200) {
      const stats = response.data.data.reduce((acc: any, item: any) => {
        if (item.riskLevel === 1) acc.low = item.count
        if (item.riskLevel === 2) acc.medium = item.count
        if (item.riskLevel === 3) acc.high = item.count
        return acc
      }, {})
      riskStats.value = stats
    }
  } catch (error) {
    console.error('加载风险统计失败:', error)
  }
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      ...createTimeComputed.value
    }
    const response = await getAnalysisList(params)
    if (response.data.code === 200) {
      analysisList.value = response.data.data.list
      total.value = response.data.data.total
      totalAnalysis.value = response.data.data.total
    }
  } catch (error) {
    console.error('获取分析结果失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 开始分析
const handleStartAnalysis = async () => {
  if (!analysisForm.stationId) {
    ElMessage.warning('请选择测点')
    return
  }

  if (!analysisTimeRange.value || analysisTimeRange.value.length !== 2) {
    ElMessage.warning('请选择完整的分析周期')
    return
  }

  try {
    analysisLoading.value = true
    const [startTime, endTime] = analysisTimeRange.value as [number, number]
    
    const response = await performIntelligentAnalysis(analysisForm.stationId, startTime, endTime)
    if (response.data.code === 200) {
      ElMessage.success('分析任务已启动，请稍后查看结果')
      getList()
      loadRiskStatistics()
    }
  } catch (error) {
    console.error('启动分析失败:', error)
    ElMessage.error('启动分析失败')
  } finally {
    analysisLoading.value = false
  }
}

// 重置分析表单
const resetAnalysisForm = () => {
  analysisFormRef.value?.resetFields()
  analysisTimeRange.value = []
}

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  createTimeRange.value = null
  queryParams.pageNum = 1
  getList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  getList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  getList()
}

// 查看详情
const handleViewDetail = (row: ConservationAnalysis) => {
  selectedAnalysis.value = row
  detailVisible.value = true
}

// 重新分析
const handleReAnalysis = async (row: ConservationAnalysis) => {
  try {
    await ElMessageBox.confirm('确定要重新分析吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await reAnalysis(row.id!)
    if (response.data.code === 200) {
      ElMessage.success('重新分析任务已启动')
      getList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新分析失败:', error)
      ElMessage.error('重新分析失败')
    }
  }
}

// 删除
const handleDelete = async (row: ConservationAnalysis) => {
  try {
    await ElMessageBox.confirm('确定要删除这条分析结果吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteAnalysis(row.id!)
    if (response.data.code === 200) {
      ElMessage.success('删除成功')
      getList()
      loadRiskStatistics()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取液位变化样式类
const getLevelChangeClass = (levelChange: number | undefined) => {
  if (!levelChange) return ''
  if (levelChange > 0) return 'level-increase'
  if (levelChange < 0) return 'level-decrease'
  return ''
}

// 获取涵养潜力颜色
const getPotentialColor = (potential: number | undefined) => {
  if (!potential) return '#e6e6e6'
  if (potential >= 80) return '#67c23a'
  if (potential >= 60) return '#e6a23c'
  if (potential >= 40) return '#f56c6c'
  return '#909399'
}

// 获取风险等级类型
const getRiskLevelType = (riskLevel: number | undefined) => {
  switch (riskLevel) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取风险等级文本
const getRiskLevelText = (riskLevel: number | undefined) => {
  switch (riskLevel) {
    case 1: return '低风险'
    case 2: return '中风险'
    case 3: return '高风险'
    default: return '未知'
  }
}

// 获取状态类型
const getStatusType = (status: number | undefined) => {
  switch (status) {
    case 1: return 'warning'
    case 2: return 'success'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: number | undefined) => {
  switch (status) {
    case 1: return '分析中'
    case 2: return '已完成'
    case 3: return '分析失败'
    default: return '未知'
  }
}
</script>

<style scoped>
.conservation-analysis {
  padding: 20px;
}

.analysis-panel {
  margin-bottom: 20px;
}

.panel-header {
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.panel-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card .stat-content {
  position: relative;
  z-index: 2;
}

.stat-card .stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-card .stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-card .stat-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 40px;
  color: #e6e6e6;
  z-index: 1;
}

.stat-card.risk-low .stat-icon {
  color: #67c23a;
}

.stat-card.risk-medium .stat-icon {
  color: #e6a23c;
}

.stat-card.risk-high .stat-icon {
  color: #f56c6c;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.level-increase {
  color: #67c23a;
  font-weight: 600;
}

.level-decrease {
  color: #f56c6c;
  font-weight: 600;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-progress-bar__outer) {
  border-radius: 10px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 10px;
}
</style>
