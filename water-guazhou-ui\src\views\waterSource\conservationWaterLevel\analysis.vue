<template>
  <div class="conservation-analysis">
    <!-- 分析控制面板 -->
    <el-card class="analysis-panel" shadow="never">
      <div class="panel-header">
        <h3>智能分析控制面板</h3>
      </div>
      
      <el-form :model="analysisForm" ref="analysisFormRef" :inline="true" label-width="100px">
        <el-form-item label="选择测点" prop="stationId" required>
          <el-select
            v-model="analysisForm.stationId"
            placeholder="请选择测点"
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="station in stationList"
              :key="station.id"
              :label="station.name"
              :value="station.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分析周期" prop="timeRange" required>
          <el-date-picker
            v-model="analysisTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="x"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            @change="handleAnalysisTimeRangeChange"
            style="width: 350px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            icon="DataAnalysis"
            @click="handleStartAnalysis"
            :loading="analysisLoading"
            :disabled="!analysisForm.stationId || !isAnalysisTimeRangeValid"
          >
            开始分析
          </el-button>
          <el-button icon="Refresh" @click="resetAnalysisForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 分析结果概览 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="6">
        <el-card class="stat-card" shadow="never">
          <div class="stat-content">
            <div class="stat-value">{{ totalAnalysis }}</div>
            <div class="stat-label">总分析次数</div>
          </div>
          <div class="stat-icon">
            <el-icon><DataAnalysis /></el-icon>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card risk-low" shadow="never">
          <div class="stat-content">
            <div class="stat-value">{{ riskStats.low || 0 }}</div>
            <div class="stat-label">低风险</div>
          </div>
          <div class="stat-icon">
            <el-icon><SuccessFilled /></el-icon>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card risk-medium" shadow="never">
          <div class="stat-content">
            <div class="stat-value">{{ riskStats.medium || 0 }}</div>
            <div class="stat-label">中风险</div>
          </div>
          <div class="stat-icon">
            <el-icon><WarningFilled /></el-icon>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card risk-high" shadow="never">
          <div class="stat-content">
            <div class="stat-value">{{ riskStats.high || 0 }}</div>
            <div class="stat-label">高风险</div>
          </div>
          <div class="stat-icon">
            <el-icon><CircleCloseFilled /></el-icon>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索条件 -->
    <CardSearch
      ref="refSearch"
      :config="searchConfig"
    />

    <!-- 分析结果列表 -->
    <CardTable
      :config="tableConfig"
      class="card-table"
    />

    <!-- 分析详情对话框 -->
    <AnalysisDetailDialog
      v-model:visible="detailVisible"
      :analysis-data="selectedAnalysis"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, shallowRef } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DataAnalysis, SuccessFilled, WarningFilled, CircleCloseFilled, Search, Refresh } from '@element-plus/icons-vue'
import { ICONS } from '@/common/constans/common'
import type { FormInstance } from 'element-plus'
import {
  performIntelligentAnalysis,
  getAnalysisList,
  deleteAnalysis,
  reAnalysis,
  getRiskLevelStatistics,
  type ConservationAnalysis,
  type AnalysisQueryParams
} from '@/api/waterSource/conservationWaterLevel'
import { getAllStations } from '@/api/station'
import CardSearch from '@/components/Form/CardSearch.vue'
import CardTable from '@/components/Form/CardTable.vue'
import AnalysisDetailDialog from './components/AnalysisDetailDialog.vue'
import { formatDate } from '@/utils/DateFormatter'
import { dwnai } from '@/utils/processNumber'

// 格式化函数
const formatDateTime = (date: string | number | Date | undefined) => {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
}

const formatNumber = (value: number | string | undefined, precision = 2) => {
  if (value === undefined || value === null || value === '') return '--'
  return dwnai(value)
}

// 响应式数据
const loading = ref(false)
const analysisLoading = ref(false)
const analysisList = ref<ConservationAnalysis[]>([])
const total = ref(0)
const totalAnalysis = ref(0)
const detailVisible = ref(false)
const selectedAnalysis = ref<ConservationAnalysis>({} as ConservationAnalysis)
const stationList = ref<any[]>([])
const riskStats = ref<any>({})

// 表单引用
const analysisFormRef = ref<FormInstance>()
const refSearch = ref<ICardSearchIns>()

// 分析表单
const analysisForm = reactive({
  stationId: '',
  timeRange: null
})

// 分析时间范围
const analysisTimeRange = ref<[number, number] | []>([])

// 搜索配置
const searchConfig = ref<ISearch>({
  labelWidth: '100px',
  filters: [
    {
      type: 'select',
      label: '测点名称',
      field: 'stationId',
      options: computed(() => [
        { label: '全部测点', value: '' },
        ...stationList.value.map(station => ({
          label: station.name,
          value: station.id
        }))
      ]) as any
    },
    {
      type: 'select',
      label: '分析状态',
      field: 'status',
      options: [
        { label: '分析中', value: 1 },
        { label: '已完成', value: 2 },
        { label: '分析失败', value: 3 }
      ]
    },
    {
      type: 'select',
      label: '风险等级',
      field: 'riskLevel',
      options: [
        { label: '低风险', value: 1 },
        { label: '中风险', value: 2 },
        { label: '高风险', value: 3 }
      ]
    },
    {
      type: 'daterange',
      label: '分析时间',
      field: 'createTime'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', icon: ICONS.QUERY, click: () => getList() },
        { type: 'default', perm: true, text: '重置', svgIcon: shallowRef(Refresh), click: () => resetQuery() }
      ]
    }
  ]
})

// 表格配置
const tableConfig = reactive<ICardTable>({
  title: '分析结果列表',
  columns: [
    {
      label: '测点名称',
      prop: 'stationName',
      width: 120
    },
    {
      label: '分析周期',
      prop: 'period',
      width: 300,
      formatter: (row: ConservationAnalysis) => `${formatDateTime(row.startTime)} 至 ${formatDateTime(row.endTime)}`
    },
    {
      label: '水位变化(m)',
      prop: 'levelChange',
      width: 120,
      align: 'center',
      formatter: (row: ConservationAnalysis) => formatNumber(row.levelChange)
    },
    {
      label: '涵养潜力',
      prop: 'conservationPotential',
      width: 120,
      align: 'center',
      formatter: (row: ConservationAnalysis) => `${formatNumber(row.conservationPotential)}%`
    },
    {
      label: '风险等级',
      prop: 'riskLevel',
      width: 100,
      align: 'center',
      formatter: (row: ConservationAnalysis) => getRiskLevelText(row.riskLevel)
    },
    {
      label: '建议涵养量(m³)',
      prop: 'suggestedConservationAmount',
      width: 140,
      align: 'center',
      formatter: (row: ConservationAnalysis) => formatNumber(row.suggestedConservationAmount)
    },
    {
      label: '分析状态',
      prop: 'status',
      width: 100,
      align: 'center',
      formatter: (row: ConservationAnalysis) => getStatusText(row.status)
    },
    {
      label: '分析时间',
      prop: 'createTime',
      width: 160,
      align: 'center',
      formatter: (row: ConservationAnalysis) => formatDateTime(row.createTime)
    },
    {
      label: '分析人',
      prop: 'creatorName',
      width: 100,
      align: 'center'
    }
  ],
  dataList: analysisList as any,
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '详情',
      icon: 'iconfont icon-chakan',
      click: (row: ConservationAnalysis) => handleViewDetail(row)
    },
    {
      perm: true,
      type: 'warning',
      isTextBtn: true,
      text: '重新分析',
      icon: 'iconfont icon-shuaxin',
      click: (row: ConservationAnalysis) => handleReAnalysis(row),
      show: (row: ConservationAnalysis) => row.status === 2
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      icon: 'iconfont icon-shanchu',
      click: (row: ConservationAnalysis) => handleDelete(row)
    }
  ],
  pagination: {
    total: total.value,
    page: 1,
    limit: 10,
    handlePage: (page: number) => {
      if (tableConfig.pagination) {
        tableConfig.pagination.page = page
        getList()
      }
    },
    handleSize: (size: number) => {
      if (tableConfig.pagination) {
        tableConfig.pagination.limit = size
        getList()
      }
    }
  }
})

// 检查分析时间范围是否有效
const isAnalysisTimeRangeValid = computed(() => {
  return analysisTimeRange.value && Array.isArray(analysisTimeRange.value) && analysisTimeRange.value.length === 2
})

// 生命周期
onMounted(() => {
  loadStationList()
  getList()
  loadRiskStatistics()
})

// 加载测点列表
const loadStationList = async () => {
  try {
    const response = await getAllStations({ type: '水厂' })
    if (response.status === 200) {
      // 后端返回的是PageData格式，数据在data字段中
      stationList.value = response.data.data || []
    }
  } catch (error) {
    console.error('加载测点列表失败:', error)
    ElMessage.error('加载测点列表失败')
  }
}

// 处理分析时间范围变化
const handleAnalysisTimeRangeChange = (value: any) => {
  if (value && Array.isArray(value) && value.length === 2) {
    analysisTimeRange.value = [value[0], value[1]] as [number, number]
  } else {
    analysisTimeRange.value = []
  }
}

// 加载风险统计
const loadRiskStatistics = async () => {
  try {
    const response = await getRiskLevelStatistics()
    if (response.data.code === 200) {
      const stats = response.data.data.reduce((acc: any, item: any) => {
        if (item.riskLevel === 1) acc.low = item.count
        if (item.riskLevel === 2) acc.medium = item.count
        if (item.riskLevel === 3) acc.high = item.count
        return acc
      }, {})
      riskStats.value = stats
    }
  } catch (error) {
    console.error('加载风险统计失败:', error)
  }
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const query = refSearch.value?.queryParams || {}
    const params: AnalysisQueryParams = {
      pageNum: 1,
      pageSize: 10,
      stationId: query.stationId || '',
      status: query.status,
      riskLevel: query.riskLevel,
      startTime: query.createTime?.[0],
      endTime: query.createTime?.[1]
    }
    const response = await getAnalysisList(params)
    if (response.data.code === 200) {
      analysisList.value = response.data.data.list
      total.value = response.data.data.total
      totalAnalysis.value = response.data.data.total
    }
  } catch (error) {
    console.error('获取分析结果失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 开始分析
const handleStartAnalysis = async () => {
  if (!analysisForm.stationId) {
    ElMessage.warning('请选择测点')
    return
  }

  if (!analysisTimeRange.value || analysisTimeRange.value.length !== 2) {
    ElMessage.warning('请选择完整的分析周期')
    return
  }

  try {
    analysisLoading.value = true
    const [startTime, endTime] = analysisTimeRange.value as [number, number]
    
    const response = await performIntelligentAnalysis(analysisForm.stationId, startTime, endTime)
    if (response.data.code === 200) {
      ElMessage.success('分析任务已启动，请稍后查看结果')
      getList()
      loadRiskStatistics()
    }
  } catch (error) {
    console.error('启动分析失败:', error)
    ElMessage.error('启动分析失败')
  } finally {
    analysisLoading.value = false
  }
}

// 重置分析表单
const resetAnalysisForm = () => {
  analysisFormRef.value?.resetFields()
  analysisTimeRange.value = []
}

// 重置查询
const resetQuery = () => {
  refSearch.value?.resetForm()
  getList()
}

// 查看详情
const handleViewDetail = (row: ConservationAnalysis) => {
  selectedAnalysis.value = row
  detailVisible.value = true
}

// 重新分析
const handleReAnalysis = async (row: ConservationAnalysis) => {
  try {
    await ElMessageBox.confirm('确定要重新分析吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await reAnalysis(row.id!)
    if (response.data.code === 200) {
      ElMessage.success('重新分析任务已启动')
      getList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新分析失败:', error)
      ElMessage.error('重新分析失败')
    }
  }
}

// 删除
const handleDelete = async (row: ConservationAnalysis) => {
  try {
    await ElMessageBox.confirm('确定要删除这条分析结果吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteAnalysis(row.id!)
    if (response.data.code === 200) {
      ElMessage.success('删除成功')
      getList()
      loadRiskStatistics()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取液位变化样式类
const getLevelChangeClass = (levelChange: number | undefined) => {
  if (!levelChange) return ''
  if (levelChange > 0) return 'level-increase'
  if (levelChange < 0) return 'level-decrease'
  return ''
}

// 获取涵养潜力颜色
const getPotentialColor = (potential: number | undefined) => {
  if (!potential) return '#e6e6e6'
  if (potential >= 80) return '#67c23a'
  if (potential >= 60) return '#e6a23c'
  if (potential >= 40) return '#f56c6c'
  return '#909399'
}

// 获取风险等级类型
const getRiskLevelType = (riskLevel: number | undefined) => {
  switch (riskLevel) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取风险等级文本
const getRiskLevelText = (riskLevel: number | undefined) => {
  switch (riskLevel) {
    case 1: return '低风险'
    case 2: return '中风险'
    case 3: return '高风险'
    default: return '未知'
  }
}

// 获取状态类型
const getStatusType = (status: number | undefined) => {
  switch (status) {
    case 1: return 'warning'
    case 2: return 'success'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: number | undefined) => {
  switch (status) {
    case 1: return '分析中'
    case 2: return '已完成'
    case 3: return '分析失败'
    default: return '未知'
  }
}
</script>

<style scoped>
.conservation-analysis {
  padding: 20px;
}

.analysis-panel {
  margin-bottom: 20px;
}

.panel-header {
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.panel-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card .stat-content {
  position: relative;
  z-index: 2;
}

.stat-card .stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-card .stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-card .stat-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 40px;
  color: #e6e6e6;
  z-index: 1;
}

.stat-card.risk-low .stat-icon {
  color: #67c23a;
}

.stat-card.risk-medium .stat-icon {
  color: #e6a23c;
}

.stat-card.risk-high .stat-icon {
  color: #f56c6c;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.level-increase {
  color: #67c23a;
  font-weight: 600;
}

.level-decrease {
  color: #f56c6c;
  font-weight: 600;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-progress-bar__outer) {
  border-radius: 10px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 10px;
}
</style>
