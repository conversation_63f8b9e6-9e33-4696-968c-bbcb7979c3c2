{"version": 3, "sources": ["../../@arcgis/core/core/workers/loaderConfig.js", "../../@arcgis/core/core/workers/WorkerFallback.js", "../../@arcgis/core/core/workers/workerFactory.js", "../../@arcgis/core/core/workers/WorkerOwner.js", "../../@arcgis/core/core/workers/workers.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../assets.js\";import\"../has.js\";import\"../urlUtils.js\";let s=null;const a={};function e(s){const e={async:s.async,isDebug:s.isDebug,locale:s.locale,baseUrl:s.baseUrl,has:{...s.has},map:{...s.map},packages:s.packages&&s.packages.concat()||[],paths:{...s.paths}};return s.hasOwnProperty(\"async\")||(e.async=!0),s.hasOwnProperty(\"isDebug\")||(e.isDebug=!1),s.baseUrl||(e.baseUrl=a.baseUrl),e}export{s as DEFAULT_LOADER_URL,e as loaderConfig};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{nextTick as e}from\"../nextTick.js\";import s from\"./RemoteClient.js\";import{MessageType as r,receiveMessage as t}from\"./utils.js\";class n{constructor(){const e=document.createDocumentFragment();[\"addEventListener\",\"dispatchEvent\",\"removeEventListener\"].forEach((s=>{this[s]=(...r)=>e[s](...r)}))}}class a{constructor(){this._dispatcher=new n,this._workerPostMessage({type:r.HANDSHAKE})}terminate(){}get onmessage(){return this._onmessageHandler}set onmessage(e){this._onmessageHandler&&this.removeEventListener(\"message\",this._onmessageHandler),this._onmessageHandler=e,e&&this.addEventListener(\"message\",e)}get onmessageerror(){return this._onmessageerrorHandler}set onmessageerror(e){this._onmessageerrorHandler&&this.removeEventListener(\"messageerror\",this._onmessageerrorHandler),this._onmessageerrorHandler=e,e&&this.addEventListener(\"messageerror\",e)}get onerror(){return this._onerrorHandler}set onerror(e){this._onerrorHandler&&this.removeEventListener(\"error\",this._onerrorHandler),this._onerrorHandler=e,e&&this.addEventListener(\"error\",e)}postMessage(s){e((()=>{this._workerMessageHandler(new MessageEvent(\"message\",{data:s}))}))}dispatchEvent(e){return this._dispatcher.dispatchEvent(e)}addEventListener(e,s,r){this._dispatcher.addEventListener(e,s,r)}removeEventListener(e,s,r){this._dispatcher.removeEventListener(e,s,r)}_workerPostMessage(s){e((()=>{this.dispatchEvent(new MessageEvent(\"message\",{data:s}))}))}async _workerMessageHandler(e){const n=t(e);if(n&&n.type===r.OPEN){const{modulePath:e,jobId:t}=n;let a=await s.loadWorker(e);a||(a=await import(/* @vite-ignore */ /* webpackIgnore: true */e));const o=s.connect(a);this._workerPostMessage({type:r.OPENED,jobId:t,data:o})}}}export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getAssetUrl as e}from\"../../assets.js\";import r from\"../../config.js\";import\"../../intl.js\";import{version as o}from\"../../kernel.js\";import has from\"../has.js\";import t from\"../Logger.js\";import{makeAbsolute as s}from\"../urlUtils.js\";import{loaderConfig as n}from\"./loaderConfig.js\";import{receiveMessage as i,MessageType as a}from\"./utils.js\";import l from\"./WorkerFallback.js\";import{getLocale as f}from\"../../intl/locale.js\";import{buildDate as c,commitHash as d}from\"../../support/revision.js\";const u=t.getLogger(\"esri.core.workers.workerFactory\"),{HANDSHAKE:m}=a,p='let globalId=0;const outgoing=new Map,configuration=JSON.parse(\"{CONFIGURATION}\");self.esriConfig=configuration.esriConfig;const workerPath=self.esriConfig.workers.workerPath,HANDSHAKE=0,OPEN=1,OPENED=2,RESPONSE=3,INVOKE=4,ABORT=5;function createAbortError(){const e=new Error(\"Aborted\");return e.name=\"AbortError\",e}function receiveMessage(e){return e&&e.data?\"string\"==typeof e.data?JSON.parse(e.data):e.data:null}function invokeStaticMessage(e,o,r){const t=r&&r.signal,n=globalId++;return new Promise(((r,i)=>{if(t){if(t.aborted)return i(createAbortError());t.addEventListener(\"abort\",(()=>{outgoing.get(n)&&(outgoing.delete(n),self.postMessage({type:5,jobId:n}),i(createAbortError()))}))}outgoing.set(n,{resolve:r,reject:i}),self.postMessage({type:4,jobId:n,methodName:e,abortable:null!=t,data:o})}))}let workerRevisionChecked=!1;function checkWorkerRevision(e){if(!workerRevisionChecked&&e.kernelInfo){workerRevisionChecked=!0;const{revision:o,version:r}=configuration.kernelInfo,{revision:t,version:n}=e.kernelInfo;esriConfig.assetsPath!==esriConfig.defaultAssetsPath&&o!==t&&console.warn(`Version mismatch detected between ArcGIS API for JavaScript modules and assets. For more information visit https://bit.ly/3QnsuSo.\\\\nModules version: ${r}\\\\nAssets version: ${n}`)}}function messageHandler(e){const o=receiveMessage(e);if(!o)return;const r=o.jobId;switch(o.type){case 1:let n;function t(e){const o=n.connect(e);self.postMessage({type:2,jobId:r,data:o},[o])}\"function\"==typeof define&&define.amd?require([workerPath],(e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||new Promise((e=>{require([o.modulePath],e)})))).then(t)})):\"System\"in self&&\"function\"==typeof System.import?System.import(workerPath).then((e=>(n=e.default,checkWorkerRevision(n),n.loadWorker(o.modulePath)))).then((e=>e||System.import(o.modulePath))).then(t):esriConfig.workers.useDynamicImport?import(workerPath).then((e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||import(o.modulePath))).then(t)})):(self.RemoteClient||importScripts(workerPath),n=self.RemoteClient.default||self.RemoteClient,checkWorkerRevision(n),n.loadWorker(o.modulePath).then(t));break;case 3:if(outgoing.has(r)){const i=outgoing.get(r);outgoing.delete(r),o.error?i.reject(JSON.parse(o.error)):i.resolve(o.data)}}}self.dojoConfig=configuration.loaderConfig,esriConfig.workers.loaderUrl&&(self.importScripts(esriConfig.workers.loaderUrl),\"function\"==typeof require&&\"function\"==typeof require.config&&require.config(configuration.loaderConfig)),self.addEventListener(\"message\",messageHandler),self.postMessage({type:0});';let g,k;const h=\"Failed to create Worker. Fallback to execute module in main thread\";async function w(){if(!has(\"esri-workers\")||(has(\"mozilla\"),0))return y(new l);if(!g&&!k)try{const e=p.split('\"{CONFIGURATION}\"').join(`'${b()}'`);g=URL.createObjectURL(new Blob([e],{type:\"text/javascript\"}))}catch(r){k=r||{}}let e;if(g)try{e=new Worker(g,{name:\"esri-worker-\"+v++})}catch(r){u.warn(h,k),e=new l}else u.warn(h,k),e=new l;return y(e)}async function y(e){return new Promise((r=>{function o(s){const n=i(s);n&&n.type===m&&(e.removeEventListener(\"message\",o),e.removeEventListener(\"error\",t),r(e))}function t(r){r.preventDefault(),e.removeEventListener(\"message\",o),e.removeEventListener(\"error\",t),u.warn(\"Failed to create Worker. Fallback to execute module in main thread\",r),(e=new l).addEventListener(\"message\",o),e.addEventListener(\"error\",t)}e.addEventListener(\"message\",o),e.addEventListener(\"error\",t)}))}function b(){let t;if(null!=r.default){const e={...r};delete e.default,t=JSON.parse(JSON.stringify(e))}else t=JSON.parse(JSON.stringify(r));t.assetsPath=s(t.assetsPath),t.defaultAssetsPath=t.defaultAssetsPath?s(t.defaultAssetsPath):void 0,t.request.interceptors=[],t.log.interceptors=[],t.locale=f(),t.has={\"esri-csp-restrictions\":has(\"esri-csp-restrictions\"),\"esri-2d-debug\":!1,\"esri-2d-update-debug\":has(\"esri-2d-update-debug\"),\"featurelayer-pbf\":has(\"featurelayer-pbf\"),\"featurelayer-simplify-thresholds\":has(\"featurelayer-simplify-thresholds\"),\"featurelayer-simplify-payload-size-factors\":has(\"featurelayer-simplify-payload-size-factors\"),\"featurelayer-simplify-mobile-factor\":has(\"featurelayer-simplify-mobile-factor\"),\"esri-atomics\":has(\"esri-atomics\"),\"esri-shared-array-buffer\":has(\"esri-shared-array-buffer\"),\"esri-tiles-debug\":has(\"esri-tiles-debug\"),\"esri-workers-arraybuffer-transfer\":has(\"esri-workers-arraybuffer-transfer\"),\"feature-polyline-generalization-factor\":has(\"feature-polyline-generalization-factor\"),\"host-webworker\":1,\"polylabel-placement-enabled\":has(\"polylabel-placement-enabled\")},t.workers.loaderUrl&&(t.workers.loaderUrl=s(t.workers.loaderUrl)),t.workers.workerPath?t.workers.workerPath=s(t.workers.workerPath):t.workers.workerPath=s(e(\"esri/core/workers/RemoteClient.js\")),t.workers.useDynamicImport=!1;const i=r.workers.loaderConfig,a=n({baseUrl:i?.baseUrl,locale:f(),has:{\"csp-restrictions\":1,\"dojo-test-sniff\":0,\"host-webworker\":1,...i?.has},map:{...i?.map},paths:{...i?.paths},packages:i?.packages||[]}),l={version:o,buildDate:c,revision:d};return JSON.stringify({esriConfig:t,loaderConfig:a,kernelInfo:l})}let v=0;export{w as createWorker};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{workerMessages as e}from\"../../kernel.js\";import t from\"../Error.js\";import o from\"../Logger.js\";import{removeMaybe as s}from\"../maybe.js\";import{onAbortOrThrow as r,isPromiseLike as n,isAbortError as a}from\"../promiseUtils.js\";import{newJobId as i,receiveMessage as l,toInvokeError as d,postMessage as h,MessageType as c}from\"./utils.js\";import{createWorker as p}from\"./workerFactory.js\";const{ABORT:b,INVOKE:m,OPEN:_,OPENED:g,RESPONSE:u}=c;class j{static async create(e){const t=await p();return new j(t,e)}constructor(e,t){this._outJobs=new Map,this._inJobs=new Map,this.worker=e,this.id=t,e.addEventListener(\"message\",this._onMessage.bind(this)),e.addEventListener(\"error\",(e=>{e.preventDefault(),o.getLogger(\"esri.core.workers.WorkerOwner\").error(e)}))}terminate(){this.worker.terminate()}async open(e,t={}){const{signal:o}=t,s=i();return new Promise(((t,n)=>{const a={resolve:t,reject:n,abortHandle:r(o,(()=>{this._outJobs.delete(s),this._post({type:b,jobId:s})}))};this._outJobs.set(s,a),this._post({type:_,jobId:s,modulePath:e})}))}_onMessage(e){const t=l(e);if(t)switch(t.type){case g:this._onOpenedMessage(t);break;case u:this._onResponseMessage(t);break;case b:this._onAbortMessage(t);break;case m:this._onInvokeMessage(t)}}_onAbortMessage(e){const t=this._inJobs,o=e.jobId,s=t.get(o);s&&(s.controller&&s.controller.abort(),t.delete(o))}_onInvokeMessage(t){const{methodName:o,jobId:s,data:r,abortable:i}=t,l=i?new AbortController:null,h=this._inJobs,c=e[o];let p;try{if(\"function\"!=typeof c)throw new TypeError(`${o} is not a function`);p=c.call(null,r,{signal:l?l.signal:null})}catch(b){return void this._post({type:u,jobId:s,error:d(b)})}n(p)?(h.set(s,{controller:l,promise:p}),p.then((e=>{h.has(s)&&(h.delete(s),this._post({type:u,jobId:s},e))}),(e=>{h.has(s)&&(h.delete(s),e||(e={message:\"Error encountered at method\"+o}),a(e)||this._post({type:u,jobId:s,error:d(e||{message:`Error encountered at method ${o}`})}))}))):this._post({type:u,jobId:s},p)}_onOpenedMessage(e){const{jobId:t,data:o}=e,r=this._outJobs.get(t);r&&(this._outJobs.delete(t),s(r.abortHandle),r.resolve(o))}_onResponseMessage(e){const{jobId:o,error:r,data:n}=e,a=this._outJobs.get(o);a&&(this._outJobs.delete(o),s(a.abortHandle),r?a.reject(t.fromJSON(JSON.parse(r))):a.resolve(n))}_post(e,t,o){return h(this.worker,e,t,o)}}export{j as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"../Error.js\";import has from\"../has.js\";import{throwIfAborted as t}from\"../promiseUtils.js\";import e from\"./Connection.js\";import n from\"./RemoteClient.js\";import o from\"./WorkerOwner.js\";let i=has(\"esri-workers-debug\")?1:has(\"esri-mobile\")?Math.min(navigator.hardwareConcurrency-1,3):has(\"host-browser\")?navigator.hardwareConcurrency-1:0;i||(i=has(\"safari\")&&has(\"mac\")?7:2);let a=0;const s=[];function c(){d()}function l(r,t){return m(r,{client:t})}async function m(r,t){const n=new e;return await n.open(r,t),n}async function u(e,o={}){if(\"string\"!=typeof e)throw new r(\"workers:undefined-module\",\"modulePath is missing\");let c=o.strategy||\"distributed\";if(has(\"host-webworker\")&&!has(\"esri-workers\")&&(c=\"local\"),\"local\"===c){let r=await n.loadWorker(e);r||(r=await import(/* @vite-ignore */ /* webpackIgnore: true */e)),t(o.signal);const i=o.client||r;return m([n.connect(r)],{...o,client:i})}if(await d(),t(o.signal),\"dedicated\"===c){const r=a++%i;return m([await s[r].open(e,o)],o)}if(o.maxNumWorkers&&o.maxNumWorkers>0){const r=Math.min(o.maxNumWorkers,i);if(r<i){const t=new Array(r);for(let n=0;n<r;++n){const r=a++%i;t[n]=s[r].open(e,o)}return m(t,o)}}return m(s.map((r=>r.open(e,o))),o)}function f(){p&&(w.abort(),p=null);for(let r=0;r<s.length;r++)s[r]&&s[r].terminate();s.length=0}let w,p=null;async function d(){if(p)return p;w=new AbortController;const r=[];for(let t=0;t<i;t++){const e=o.create(t).then((r=>(s[t]=r,r)));r.push(e)}return p=Promise.all(r),p}export{e as Connection,n as RemoteClient,c as initialize,u as open,l as openWithPorts,f as terminate};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4E,IAAMA,KAAE,CAAC;AAAE,SAASC,GAAEC,IAAE;AAAC,QAAMD,KAAE,EAAC,OAAMC,GAAE,OAAM,SAAQA,GAAE,SAAQ,QAAOA,GAAE,QAAO,SAAQA,GAAE,SAAQ,KAAI,EAAC,GAAGA,GAAE,IAAG,GAAE,KAAI,EAAC,GAAGA,GAAE,IAAG,GAAE,UAASA,GAAE,YAAUA,GAAE,SAAS,OAAO,KAAG,CAAC,GAAE,OAAM,EAAC,GAAGA,GAAE,MAAK,EAAC;AAAE,SAAOA,GAAE,eAAe,OAAO,MAAID,GAAE,QAAM,OAAIC,GAAE,eAAe,SAAS,MAAID,GAAE,UAAQ,QAAIC,GAAE,YAAUD,GAAE,UAAQD,GAAE,UAASC;AAAC;;;ACApQ,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,UAAME,KAAE,SAAS,uBAAuB;AAAE,KAAC,oBAAmB,iBAAgB,qBAAqB,EAAE,QAAS,CAAAC,OAAG;AAAC,WAAKA,EAAC,IAAE,IAAIC,OAAIF,GAAEC,EAAC,EAAE,GAAGC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,cAAY,IAAI,KAAE,KAAK,mBAAmB,EAAC,MAAKC,GAAE,UAAS,CAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAA,EAAC,IAAI,UAAUJ,IAAE;AAAC,SAAK,qBAAmB,KAAK,oBAAoB,WAAU,KAAK,iBAAiB,GAAE,KAAK,oBAAkBA,IAAEA,MAAG,KAAK,iBAAiB,WAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAsB;AAAA,EAAC,IAAI,eAAeA,IAAE;AAAC,SAAK,0BAAwB,KAAK,oBAAoB,gBAAe,KAAK,sBAAsB,GAAE,KAAK,yBAAuBA,IAAEA,MAAG,KAAK,iBAAiB,gBAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,QAAQA,IAAE;AAAC,SAAK,mBAAiB,KAAK,oBAAoB,SAAQ,KAAK,eAAe,GAAE,KAAK,kBAAgBA,IAAEA,MAAG,KAAK,iBAAiB,SAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,MAAG,MAAI;AAAC,WAAK,sBAAsB,IAAI,aAAa,WAAU,EAAC,MAAKA,GAAC,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAE;AAAC,WAAO,KAAK,YAAY,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEC,IAAEC,IAAE;AAAC,SAAK,YAAY,iBAAiBF,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAEC,IAAEC,IAAE;AAAC,SAAK,YAAY,oBAAoBF,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAE;AAAC,MAAG,MAAI;AAAC,WAAK,cAAc,IAAI,aAAa,WAAU,EAAC,MAAKA,GAAC,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsBD,IAAE;AAAC,UAAMK,KAAEC,GAAEN,EAAC;AAAE,QAAGK,MAAGA,GAAE,SAAOD,GAAE,MAAK;AAAC,YAAK,EAAC,YAAWJ,IAAE,OAAMI,GAAC,IAAEC;AAAE,UAAIF,KAAE,MAAM,EAAE,WAAWH,EAAC;AAAE,MAAAG,OAAIA,KAAE,MAAM;AAAA;AAAA;AAAA,QAAmDH;AAAA;AAAI,YAAMO,KAAE,EAAE,QAAQJ,EAAC;AAAE,WAAK,mBAAmB,EAAC,MAAKC,GAAE,QAAO,OAAMA,IAAE,MAAKG,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACA3qC,IAAM,IAAEC,GAAE,UAAU,iCAAiC;AAArD,IAAuD,EAAC,WAAU,EAAC,IAAEC;AAArE,IAAuEC,KAAE;AAA+kF,IAAI;AAAJ,IAAM;AAAE,IAAMC,KAAE;AAAqE,eAAeC,KAAG;AAAC,MAAG,CAAC,IAAI,cAAc,MAAI,IAAI,SAAS,GAAE,GAAG,QAAO,EAAE,IAAIC,IAAC;AAAE,MAAG,CAAC,KAAG,CAAC,EAAE,KAAG;AAAC,UAAMC,KAAEJ,GAAE,MAAM,mBAAmB,EAAE,KAAK,IAAI,EAAE,CAAC,GAAG;AAAE,QAAE,IAAI,gBAAgB,IAAI,KAAK,CAACI,EAAC,GAAE,EAAC,MAAK,kBAAiB,CAAC,CAAC;AAAA,EAAC,SAAOC,IAAE;AAAC,QAAEA,MAAG,CAAC;AAAA,EAAC;AAAC,MAAID;AAAE,MAAG,EAAE,KAAG;AAAC,IAAAA,KAAE,IAAI,OAAO,GAAE,EAAC,MAAK,iBAAe,IAAG,CAAC;AAAA,EAAC,SAAOC,IAAE;AAAC,MAAE,KAAKJ,IAAE,CAAC,GAAEG,KAAE,IAAID;AAAA,EAAC;AAAA,MAAM,GAAE,KAAKF,IAAE,CAAC,GAAEG,KAAE,IAAID;AAAE,SAAO,EAAEC,EAAC;AAAC;AAAC,eAAe,EAAEA,IAAE;AAAC,SAAO,IAAI,QAAS,CAAAC,OAAG;AAAC,aAASC,GAAER,IAAE;AAAC,YAAMS,KAAEC,GAAEV,EAAC;AAAE,MAAAS,MAAGA,GAAE,SAAO,MAAIH,GAAE,oBAAoB,WAAUE,EAAC,GAAEF,GAAE,oBAAoB,SAAQL,EAAC,GAAEM,GAAED,EAAC;AAAA,IAAE;AAAC,aAASL,GAAEM,IAAE;AAAC,MAAAA,GAAE,eAAe,GAAED,GAAE,oBAAoB,WAAUE,EAAC,GAAEF,GAAE,oBAAoB,SAAQL,EAAC,GAAE,EAAE,KAAK,sEAAqEM,EAAC,IAAGD,KAAE,IAAID,MAAG,iBAAiB,WAAUG,EAAC,GAAEF,GAAE,iBAAiB,SAAQL,EAAC;AAAA,IAAC;AAAC,IAAAK,GAAE,iBAAiB,WAAUE,EAAC,GAAEF,GAAE,iBAAiB,SAAQL,EAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,IAAG;AAAC,MAAIA;AAAE,MAAG,QAAM,EAAE,SAAQ;AAAC,UAAMK,KAAE,EAAC,GAAG,EAAC;AAAE,WAAOA,GAAE,SAAQL,KAAE,KAAK,MAAM,KAAK,UAAUK,EAAC,CAAC;AAAA,EAAC,MAAM,CAAAL,KAAE,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC;AAAE,EAAAA,GAAE,aAAW,EAAEA,GAAE,UAAU,GAAEA,GAAE,oBAAkBA,GAAE,oBAAkB,EAAEA,GAAE,iBAAiB,IAAE,QAAOA,GAAE,QAAQ,eAAa,CAAC,GAAEA,GAAE,IAAI,eAAa,CAAC,GAAEA,GAAE,SAAO,EAAE,GAAEA,GAAE,MAAI,EAAC,yBAAwB,IAAI,uBAAuB,GAAE,iBAAgB,OAAG,wBAAuB,IAAI,sBAAsB,GAAE,oBAAmB,IAAI,kBAAkB,GAAE,oCAAmC,IAAI,kCAAkC,GAAE,8CAA6C,IAAI,4CAA4C,GAAE,uCAAsC,IAAI,qCAAqC,GAAE,gBAAe,IAAI,cAAc,GAAE,4BAA2B,IAAI,0BAA0B,GAAE,oBAAmB,IAAI,kBAAkB,GAAE,qCAAoC,IAAI,mCAAmC,GAAE,0CAAyC,IAAI,wCAAwC,GAAE,kBAAiB,GAAE,+BAA8B,IAAI,6BAA6B,EAAC,GAAEA,GAAE,QAAQ,cAAYA,GAAE,QAAQ,YAAU,EAAEA,GAAE,QAAQ,SAAS,IAAGA,GAAE,QAAQ,aAAWA,GAAE,QAAQ,aAAW,EAAEA,GAAE,QAAQ,UAAU,IAAEA,GAAE,QAAQ,aAAW,EAAE,EAAE,mCAAmC,CAAC,GAAEA,GAAE,QAAQ,mBAAiB;AAAG,QAAMU,KAAE,EAAE,QAAQ,cAAaN,KAAEC,GAAE,EAAC,SAAQK,MAAA,gBAAAA,GAAG,SAAQ,QAAO,EAAE,GAAE,KAAI,EAAC,oBAAmB,GAAE,mBAAkB,GAAE,kBAAiB,GAAE,GAAGA,MAAA,gBAAAA,GAAG,IAAG,GAAE,KAAI,EAAC,GAAGA,MAAA,gBAAAA,GAAG,IAAG,GAAE,OAAM,EAAC,GAAGA,MAAA,gBAAAA,GAAG,MAAK,GAAE,WAASA,MAAA,gBAAAA,GAAG,aAAU,CAAC,EAAC,CAAC,GAAEC,KAAE,EAAC,SAAQP,IAAE,WAAU,GAAE,UAAS,EAAC;AAAE,SAAO,KAAK,UAAU,EAAC,YAAWJ,IAAE,cAAaI,IAAE,YAAWO,GAAC,CAAC;AAAC;AAAC,IAAI,IAAE;;;ACArwK,IAAK,EAAC,OAAMC,IAAE,QAAOC,IAAE,MAAK,GAAE,QAAOC,IAAE,UAASC,GAAC,IAAEC;AAAE,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,aAAa,OAAOC,IAAE;AAAC,UAAMF,KAAE,MAAMG,GAAE;AAAE,WAAO,IAAI,GAAEH,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEF,IAAE;AAAC,SAAK,WAAS,oBAAI,OAAI,KAAK,UAAQ,oBAAI,OAAI,KAAK,SAAOE,IAAE,KAAK,KAAGF,IAAEE,GAAE,iBAAiB,WAAU,KAAK,WAAW,KAAK,IAAI,CAAC,GAAEA,GAAE,iBAAiB,SAAS,CAAAA,OAAG;AAAC,MAAAA,GAAE,eAAe,GAAEE,GAAE,UAAU,+BAA+B,EAAE,MAAMF,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,SAAK,OAAO,UAAU;AAAA,EAAC;AAAA,EAAC,MAAM,KAAKA,IAAEF,KAAE,CAAC,GAAE;AAAC,UAAK,EAAC,QAAOK,GAAC,IAAEL,IAAEI,KAAE,EAAE;AAAE,WAAO,IAAI,QAAS,CAACJ,IAAEM,OAAI;AAAC,YAAMC,KAAE,EAAC,SAAQP,IAAE,QAAOM,IAAE,aAAY,EAAED,IAAG,MAAI;AAAC,aAAK,SAAS,OAAOD,EAAC,GAAE,KAAK,MAAM,EAAC,MAAKR,IAAE,OAAMQ,GAAC,CAAC;AAAA,MAAC,CAAE,EAAC;AAAE,WAAK,SAAS,IAAIA,IAAEG,EAAC,GAAE,KAAK,MAAM,EAAC,MAAK,GAAE,OAAMH,IAAE,YAAWF,GAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAMF,KAAEQ,GAAEN,EAAC;AAAE,QAAGF,GAAE,SAAOA,GAAE,MAAK;AAAA,MAAC,KAAKF;AAAE,aAAK,iBAAiBE,EAAC;AAAE;AAAA,MAAM,KAAKD;AAAE,aAAK,mBAAmBC,EAAC;AAAE;AAAA,MAAM,KAAKJ;AAAE,aAAK,gBAAgBI,EAAC;AAAE;AAAA,MAAM,KAAKH;AAAE,aAAK,iBAAiBG,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBE,IAAE;AAAC,UAAMF,KAAE,KAAK,SAAQK,KAAEH,GAAE,OAAME,KAAEJ,GAAE,IAAIK,EAAC;AAAE,IAAAD,OAAIA,GAAE,cAAYA,GAAE,WAAW,MAAM,GAAEJ,GAAE,OAAOK,EAAC;AAAA,EAAE;AAAA,EAAC,iBAAiBL,IAAE;AAAC,UAAK,EAAC,YAAWK,IAAE,OAAMD,IAAE,MAAKK,IAAE,WAAUC,GAAC,IAAEV,IAAEW,KAAED,KAAE,IAAI,oBAAgB,MAAKE,KAAE,KAAK,SAAQC,KAAET,GAAEC,EAAC;AAAE,QAAIS;AAAE,QAAG;AAAC,UAAG,cAAY,OAAOD,GAAE,OAAM,IAAI,UAAU,GAAGR,EAAC,oBAAoB;AAAE,MAAAS,KAAED,GAAE,KAAK,MAAKJ,IAAE,EAAC,QAAOE,KAAEA,GAAE,SAAO,KAAI,CAAC;AAAA,IAAC,SAAOf,IAAE;AAAC,aAAO,KAAK,KAAK,MAAM,EAAC,MAAKG,IAAE,OAAMK,IAAE,OAAMA,GAAER,EAAC,EAAC,CAAC;AAAA,IAAC;AAAC,MAAEkB,EAAC,KAAGF,GAAE,IAAIR,IAAE,EAAC,YAAWO,IAAE,SAAQG,GAAC,CAAC,GAAEA,GAAE,KAAM,CAAAZ,OAAG;AAAC,MAAAU,GAAE,IAAIR,EAAC,MAAIQ,GAAE,OAAOR,EAAC,GAAE,KAAK,MAAM,EAAC,MAAKL,IAAE,OAAMK,GAAC,GAAEF,EAAC;AAAA,IAAE,GAAI,CAAAA,OAAG;AAAC,MAAAU,GAAE,IAAIR,EAAC,MAAIQ,GAAE,OAAOR,EAAC,GAAEF,OAAIA,KAAE,EAAC,SAAQ,gCAA8BG,GAAC,IAAG,EAAEH,EAAC,KAAG,KAAK,MAAM,EAAC,MAAKH,IAAE,OAAMK,IAAE,OAAMA,GAAEF,MAAG,EAAC,SAAQ,+BAA+BG,EAAC,GAAE,CAAC,EAAC,CAAC;AAAA,IAAE,CAAE,KAAG,KAAK,MAAM,EAAC,MAAKN,IAAE,OAAMK,GAAC,GAAEU,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBZ,IAAE;AAAC,UAAK,EAAC,OAAMF,IAAE,MAAKK,GAAC,IAAEH,IAAEO,KAAE,KAAK,SAAS,IAAIT,EAAC;AAAE,IAAAS,OAAI,KAAK,SAAS,OAAOT,EAAC,GAAE,EAAES,GAAE,WAAW,GAAEA,GAAE,QAAQJ,EAAC;AAAA,EAAE;AAAA,EAAC,mBAAmBH,IAAE;AAAC,UAAK,EAAC,OAAMG,IAAE,OAAMI,IAAE,MAAKH,GAAC,IAAEJ,IAAEK,KAAE,KAAK,SAAS,IAAIF,EAAC;AAAE,IAAAE,OAAI,KAAK,SAAS,OAAOF,EAAC,GAAE,EAAEE,GAAE,WAAW,GAAEE,KAAEF,GAAE,OAAOH,GAAE,SAAS,KAAK,MAAMK,EAAC,CAAC,CAAC,IAAEF,GAAE,QAAQD,EAAC;AAAA,EAAE;AAAA,EAAC,MAAMJ,IAAEF,IAAEK,IAAE;AAAC,WAAO,EAAE,KAAK,QAAOH,IAAEF,IAAEK,EAAC;AAAA,EAAC;AAAC;;;ACA/jE,IAAI,IAAE,IAAI,oBAAoB,IAAE,IAAE,IAAI,aAAa,IAAE,KAAK,IAAI,UAAU,sBAAoB,GAAE,CAAC,IAAE,IAAI,cAAc,IAAE,UAAU,sBAAoB,IAAE;AAAE,MAAI,IAAE,IAAI,QAAQ,KAAG,IAAI,KAAK,IAAE,IAAE;AAAG,IAAIU,KAAE;AAAE,IAAMC,KAAE,CAAC;AAAE,SAASC,KAAG;AAAC,EAAAC,GAAE;AAAC;AAAwC,eAAeC,GAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,IAAI;AAAE,SAAO,MAAMA,GAAE,KAAKF,IAAEC,EAAC,GAAEC;AAAC;AAAC,eAAeC,GAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,MAAG,YAAU,OAAOD,GAAE,OAAM,IAAIE,GAAE,4BAA2B,uBAAuB;AAAE,MAAIC,KAAEF,GAAE,YAAU;AAAc,MAAG,IAAI,gBAAgB,KAAG,CAAC,IAAI,cAAc,MAAIE,KAAE,UAAS,YAAUA,IAAE;AAAC,QAAIP,KAAE,MAAM,EAAE,WAAWI,EAAC;AAAE,IAAAJ,OAAIA,KAAE,MAAM;AAAA;AAAA;AAAA,MAAmDI;AAAA,QAAI,EAAEC,GAAE,MAAM;AAAE,UAAMG,KAAEH,GAAE,UAAQL;AAAE,WAAOD,GAAE,CAAC,EAAE,QAAQC,EAAC,CAAC,GAAE,EAAC,GAAGK,IAAE,QAAOG,GAAC,CAAC;AAAA,EAAC;AAAC,MAAG,MAAMC,GAAE,GAAE,EAAEJ,GAAE,MAAM,GAAE,gBAAcE,IAAE;AAAC,UAAMP,KAAEU,OAAI;AAAE,WAAOX,GAAE,CAAC,MAAMO,GAAEN,EAAC,EAAE,KAAKI,IAAEC,EAAC,CAAC,GAAEA,EAAC;AAAA,EAAC;AAAC,MAAGA,GAAE,iBAAeA,GAAE,gBAAc,GAAE;AAAC,UAAML,KAAE,KAAK,IAAIK,GAAE,eAAc,CAAC;AAAE,QAAGL,KAAE,GAAE;AAAC,YAAMC,KAAE,IAAI,MAAMD,EAAC;AAAE,eAAQE,KAAE,GAAEA,KAAEF,IAAE,EAAEE,IAAE;AAAC,cAAMF,KAAEU,OAAI;AAAE,QAAAT,GAAEC,EAAC,IAAEI,GAAEN,EAAC,EAAE,KAAKI,IAAEC,EAAC;AAAA,MAAC;AAAC,aAAON,GAAEE,IAAEI,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAON,GAAEO,GAAE,IAAK,CAAAN,OAAGA,GAAE,KAAKI,IAAEC,EAAC,CAAE,GAAEA,EAAC;AAAC;AAAiG,IAAIM;AAAJ,IAAMC,KAAE;AAAK,eAAeC,KAAG;AAAC,MAAGD,GAAE,QAAOA;AAAE,EAAAD,KAAE,IAAI;AAAgB,QAAMG,KAAE,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAMC,KAAEC,GAAE,OAAOF,EAAC,EAAE,KAAM,CAAAD,QAAII,GAAEH,EAAC,IAAED,IAAEA,GAAG;AAAE,IAAAA,GAAE,KAAKE,EAAC;AAAA,EAAC;AAAC,SAAOJ,KAAE,QAAQ,IAAIE,EAAC,GAAEF;AAAC;", "names": ["a", "e", "s", "e", "s", "r", "a", "t", "n", "f", "o", "s", "t", "p", "h", "w", "a", "e", "r", "o", "n", "f", "i", "l", "b", "m", "g", "u", "t", "j", "e", "w", "s", "o", "n", "a", "f", "r", "i", "l", "h", "c", "p", "a", "s", "c", "d", "m", "r", "t", "n", "u", "e", "o", "s", "c", "i", "d", "a", "w", "p", "d", "r", "t", "e", "j", "s"]}