import {
  u
} from "./chunk-D7S3BWBP.js";
import {
  S,
  b,
  b2,
  c2 as c,
  f2 as f,
  h,
  k,
  k2,
  x
} from "./chunk-ETY52UBV.js";
import {
  S as S2
} from "./chunk-R3VLALN5.js";
import {
  y
} from "./chunk-VX6YUKFM.js";
import {
  m
} from "./chunk-6ILWLF72.js";
import {
  m3 as m2
} from "./chunk-YD3YIZNH.js";
import {
  n
} from "./chunk-73VUEZR7.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/symbols/support/symbolConversion.js
var a = { retainId: false, ignoreDrivers: false, hasLabelingContext: true };
function S3(S4, c2 = a) {
  var _a, _b;
  if (!S4) return { symbol: null };
  const { retainId: u2 = a.retainId, ignoreDrivers: d = a.ignoreDrivers, hasLabelingContext: j = a.hasLabelingContext, retainCIM: g = a.retainCIM } = c2;
  let D = null;
  if (x(S4) || S4 instanceof f) D = S4.clone();
  else if ("cim" === S4.type) {
    const o = (_b = (_a = S4.data) == null ? void 0 : _a.symbol) == null ? void 0 : _b.type;
    if ("CIMPointSymbol" !== o) return { error: new s("symbol-conversion:unsupported-cim-symbol", `CIM symbol of type '${o || "unknown"}' is unsupported in 3D`, { symbol: S4 }) };
    D = g ? S4.clone() : h.fromCIMSymbol(S4);
  } else if (S4 instanceof m) D = b2.fromSimpleLineSymbol(S4);
  else if (S4 instanceof y) D = h.fromSimpleMarkerSymbol(S4);
  else if (S4 instanceof n) D = h.fromPictureMarkerSymbol(S4);
  else if (S4 instanceof S2) D = c2.geometryType && "mesh" === c2.geometryType ? c.fromSimpleFillSymbol(S4) : S.fromSimpleFillSymbol(S4);
  else {
    if (!(S4 instanceof m2)) return { error: new s("symbol-conversion:unsupported-2d-symbol", `2D symbol of type '${S4.type || S4.declaredClass}' is unsupported in 3D`, { symbol: S4 }) };
    D = j ? b.fromTextSymbol(S4) : h.fromTextSymbol(S4);
  }
  if (u2 && D && "cim" !== D.type && (D.id = S4.id), d && x(D)) for (let o = 0; o < D.symbolLayers.length; ++o) D.symbolLayers.getItemAt(o)._ignoreDrivers = true;
  return { symbol: D };
}

// node_modules/@arcgis/core/symbols/support/jsonUtils.js
function m3(o, e, t2, r2) {
  const s2 = p(o, {}, { context: r2, isLabelSymbol: false });
  r(s2) && (e[t2] = s2);
}
function a2(o, e, t2, r2) {
  const s2 = p(o, {}, { context: r2, isLabelSymbol: true });
  r(s2) && (e[t2] = s2);
}
function b3(o) {
  return o instanceof k || o instanceof f;
}
function p(o, s2, i) {
  if (t(o)) return null;
  const { context: m4, isLabelSymbol: a3 } = i, p2 = m4 == null ? void 0 : m4.origin, y3 = m4 == null ? void 0 : m4.messages;
  if ("web-scene" === p2 && !b3(o)) {
    const t2 = S3(o, { retainCIM: true, hasLabelingContext: a3 });
    return r(t2.symbol) ? t2.symbol.write(s2, m4) : (y3 == null ? void 0 : y3.push(new s("symbol:unsupported", `Symbols of type '${o.declaredClass}' are not supported in scenes. Use 3D symbology instead when working with WebScene and SceneView`, { symbol: o, context: m4, error: t2.error })), null);
  }
  return ("web-map" === p2 || "portal-item" === p2 && !u(m4 == null ? void 0 : m4.layer)) && b3(o) ? (y3 == null ? void 0 : y3.push(new s("symbol:unsupported", `Symbols of type '${o.declaredClass}' are not supported in web maps and portal items. Use 2D symbology and CIMSymbol instead when working with MapView`, { symbol: o, context: m4 })), null) : o.write(s2, m4);
}
function y2(e, n2) {
  return k2(e, null, n2);
}

export {
  a,
  S3 as S,
  m3 as m,
  a2,
  y2 as y
};
//# sourceMappingURL=chunk-PNIF6I3E.js.map
