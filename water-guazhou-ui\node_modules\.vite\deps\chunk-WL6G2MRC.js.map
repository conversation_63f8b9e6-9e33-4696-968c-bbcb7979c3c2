{"version": 3, "sources": ["../../@arcgis/core/layers/support/labelingInfo.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Error.js\";import{clone as r}from\"../../core/lang.js\";import o from\"../../core/Logger.js\";import t from\"./LabelClass.js\";const n=o.getLogger(\"esri.layers.support.labelingInfo\"),l=/\\[([^\\[\\]]+)\\]/gi;function i(e,r,o){return e?e.map((e=>{const n=new t;if(n.read(e,o),n.labelExpression){const e=r.fields||r.layerDefinition&&r.layerDefinition.fields||this.fields;n.labelExpression=n.labelExpression.replace(l,((r,o)=>`[${s(o,e)}]`))}return n})):null}function s(e,r){if(!r)return e;const o=e.toLowerCase();for(let t=0;t<r.length;t++){const e=r[t].name;if(e.toLowerCase()===o)return e}return e}const a={esriGeometryPoint:[\"above-right\",\"above-center\",\"above-left\",\"center-center\",\"center-left\",\"center-right\",\"below-center\",\"below-left\",\"below-right\"],esriGeometryPolygon:[\"always-horizontal\"],esriGeometryPolyline:[\"center-along\"],esriGeometryMultipoint:null};function c(e,o){const t=r(e);return t.some((e=>f(e,o)))?[]:t}function f(r,o){const t=r.labelPlacement,l=a[o];if(!r.symbol)return n.warn(\"No ILabelClass symbol specified.\"),!0;if(!l)return n.error(new e(\"labeling:unsupported-geometry-type\",`Unable to create labels for layer, geometry type '${o}' is not supported`)),!0;if(!l.includes(t)){const e=l[0];t&&n.warn(`Found invalid label placement type ${t} for ${o}. Defaulting to ${e}`),r.labelPlacement=e}return!1}export{i as reader,c as validateLabelingInfo};\n"], "mappings": ";;;;;;;;;;;;;;AAIiJ,IAAM,IAAE,EAAE,UAAU,kCAAkC;AAAtD,IAAwD,IAAE;AAAmB,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,SAAO,IAAE,EAAE,IAAK,CAAAA,OAAG;AAAC,UAAMC,KAAE,IAAI;AAAE,QAAGA,GAAE,KAAKD,IAAE,CAAC,GAAEC,GAAE,iBAAgB;AAAC,YAAMD,KAAE,EAAE,UAAQ,EAAE,mBAAiB,EAAE,gBAAgB,UAAQ,KAAK;AAAO,MAAAC,GAAE,kBAAgBA,GAAE,gBAAgB,QAAQ,GAAG,CAACC,IAAEC,OAAI,IAAIC,GAAED,IAAEH,EAAC,CAAC,GAAI;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC,CAAE,IAAE;AAAI;AAAC,SAASG,GAAE,GAAE,GAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAE,QAAM,IAAE,EAAE,YAAY;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,UAAMJ,KAAE,EAAE,CAAC,EAAE;AAAK,QAAGA,GAAE,YAAY,MAAI,EAAE,QAAOA;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,IAAM,IAAE,EAAC,mBAAkB,CAAC,eAAc,gBAAe,cAAa,iBAAgB,eAAc,gBAAe,gBAAe,cAAa,aAAa,GAAE,qBAAoB,CAAC,mBAAmB,GAAE,sBAAqB,CAAC,cAAc,GAAE,wBAAuB,KAAI;AAAE,SAAS,EAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAE,CAAC;AAAE,SAAO,EAAE,KAAM,CAAAA,OAAG,EAAEA,IAAE,CAAC,CAAE,IAAE,CAAC,IAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAE,gBAAeK,KAAE,EAAE,CAAC;AAAE,MAAG,CAAC,EAAE,OAAO,QAAO,EAAE,KAAK,kCAAkC,GAAE;AAAG,MAAG,CAACA,GAAE,QAAO,EAAE,MAAM,IAAID,GAAE,sCAAqC,qDAAqD,CAAC,oBAAoB,CAAC,GAAE;AAAG,MAAG,CAACC,GAAE,SAAS,CAAC,GAAE;AAAC,UAAM,IAAEA,GAAE,CAAC;AAAE,SAAG,EAAE,KAAK,sCAAsC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,GAAE,EAAE,iBAAe;AAAA,EAAC;AAAC,SAAM;AAAE;", "names": ["e", "n", "r", "o", "s", "l"]}