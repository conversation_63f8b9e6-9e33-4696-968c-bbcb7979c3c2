import request from '@/plugins/axios'

// 获取采样记录列表
export function getSamplingRecordList(params: any) {
  return request({
    url: '/api/sampling/list',
    method: 'get',
    params
  })
}

// 保存采样记录
export function saveSamplingRecord(data: {
  id?: string
  samplingLocation: string
  samplingTime: string
  samplingPerson: string
  samplingMethod: string
  sampleNumber: string
  sampleType: string
  recordFile?: string
  remark?: string
  type?: string
}) {
  return request({
    url: '/api/sampling',
    method: 'post',
    data
  })
}

// 修改采样记录
export function updateSamplingRecord(data: {
  id: string
  samplingLocation: string
  samplingTime: string
  samplingPerson: string
  samplingMethod: string
  sampleNumber: string
  sampleType: string
  recordFile?: string
  remark?: string
  type?: string
}) {
  return request({
    url: '/api/sampling/update',
    method: 'post',
    data
  })
}

// 删除采样记录
export function deleteSamplingRecord(idList: string[]) {
  return request({
    url: '/api/sampling',
    method: 'delete',
    data: idList
  })
}

// 获取单个采样记录
export function getSamplingRecordById(id: string) {
  return request({
    url: `/api/sampling/${id}`,
    method: 'get'
  })
}
