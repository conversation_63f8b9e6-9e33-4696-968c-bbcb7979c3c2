{"version": 3, "sources": ["../../@arcgis/core/geometry/support/coordsUtils.js", "../../@arcgis/core/geometry/support/centroid.js", "../../@arcgis/core/geometry/Polygon.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{equals as t}from\"../../core/arrayUtils.js\";import{getInfo as n}from\"./spatialReferenceUtils.js\";function e(t){if(!t)return null;if(Array.isArray(t))return t;const n=t.hasZ,e=t.hasM;if(\"point\"===t.type)return e&&n?[t.x,t.y,t.z,t.m]:n?[t.x,t.y,t.z]:e?[t.x,t.y,t.m]:[t.x,t.y];if(\"polygon\"===t.type)return t.rings.slice(0);if(\"polyline\"===t.type)return t.paths.slice(0);if(\"multipoint\"===t.type)return t.points.slice(0);if(\"extent\"===t.type){const n=t.clone().normalize();if(!n)return null;let e=!1,r=!1;return n.forEach((t=>{t.hasZ&&(e=!0),t.hasM&&(r=!0)})),n.map((t=>{const n=[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]];if(e&&t.hasZ){const e=.5*(t.zmax-t.zmin);for(let t=0;t<n.length;t++)n[t].push(e)}if(r&&t.hasM){const e=.5*(t.mmax-t.mmin);for(let t=0;t<n.length;t++)n[t].push(e)}return n}))}return null}function r(t,n){const e=n[0]-t[0],r=n[1]-t[1];if(t.length>2&&n.length>2){const i=t[2]-n[2];return Math.sqrt(e*e+r*r+i*i)}return Math.sqrt(e*e+r*r)}function i(t,n,e){const r=t[0]+e*(n[0]-t[0]),i=t[1]+e*(n[1]-t[1]);return t.length>2&&n.length>2?[r,i,t[2]+e*(n[2]-t[2])]:[r,i]}function o(t,n,e,r){const[i,o]=n,[s,l]=e[r],[f,u]=e[r+1],c=f-s,h=u-l,a=c*c+h*h,g=(i-s)*c+(o-l)*h,p=Math.min(1,Math.max(0,g/a));return t[0]=s+c*p,t[1]=l+h*p,t}function s(t,n,e){const r=e.rings;let i,o,s=!1,f=1/0;for(let u=0;u<r.length;u++){const e=r[u];for(let r=0,u=e.length-1;r<e.length;u=r++)i=e[r],o=e[u],i[1]>n!=o[1]>n&&t<(o[0]-i[0])*(n-i[1])/(o[1]-i[1])+i[0]&&(s=!s),f=Math.min(f,l(t,n,i,o))}return 0===f?0:(s?1:-1)*Math.sqrt(f)}function l(t,n,e,r){let i=e[0],o=e[1],s=r[0]-i,l=r[1]-o;if(0!==s||0!==l){const e=((t-i)*s+(n-o)*l)/(s*s+l*l);e>1?(i=r[0],o=r[1]):e>0&&(i+=s*e,o+=l*e)}return s=t-i,l=n-o,s*s+l*l}function f(t,n){return i(t,n,.5)}function u(t){const n=t.length;let e=0;for(let i=0;i<n-1;++i)e+=r(t[i],t[i+1]);return e}function c(t,n){if(n<=0)return t[0];const e=t.length;let o=0;for(let s=0;s<e-1;++s){const e=r(t[s],t[s+1]);if(n-o<e){const r=(n-o)/e;return i(t[s],t[s+1],r)}o+=e}return t[e-1]}function h(t,n,e){const r=t.length;let i=0,o=0,s=0;for(let l=0;l<r;l++){const f=t[l],u=t[(l+1)%r];let c=2;i+=f[0]*u[1]-u[0]*f[1],f.length>2&&u.length>2&&e&&(o+=f[0]*u[2]-u[0]*f[2],c=3),f.length>c&&u.length>c&&n&&(s+=f[0]*u[c]-u[0]*f[c])}return i<=0&&o<=0&&s<=0}function a(n){const e=n.length;return e>2&&t(n[0],n[e-1])}function g(t){if(\"rings\"in t&&(p(t),t.rings.length>0&&!h(t.rings[0],t.hasM??!1,t.hasZ??!1)))for(const n of t.rings)n.reverse()}function p(t){if(\"rings\"in t)for(const n of t.rings)a(n)||n.push(n[0].slice())}function y(t){if(\"polygon\"!==t.type&&\"polyline\"!==t.type)return t;return m(\"polygon\"===t.type?t.rings:t.paths,t.spatialReference),t}function m(t,e){const r=n(e);if(!r)return;const i=r.valid[0],o=r.valid[1],s=o-i;for(const n of t){let t=1/0,e=-1/0;for(const s of n){const n=x(s[0],i,o);t=Math.min(t,n),e=Math.max(e,n),s[0]=n}const r=e-t;s-r<r&&n.forEach((t=>{t[0]<0&&(t[0]+=s)}))}}function x(t,n,e){const r=e-n;return t<n?e-(n-t)%r:t>e?n+(t-n)%r:t}function M(t){if(!t||t.length<3)return 0;let n=0;const e=t.length-1;for(let r=0;r<e;r++)n+=(t[r][0]-t[r+1][0])*(t[r][1]+t[r+1][1]);return n+=(t[e][0]-t[0][0])*(t[e][1]+t[0][1]),-.5*n}function z(t,n){if(t===n)return!0;if(t.type!==n.type)return!1;if(\"point\"===t.type||\"extent\"===t.type)return!0;if(\"multipoint\"===t.type)return t.points.length===n.points.length;const[e,r]=\"polyline\"===t.type?[t.paths,n.paths]:[t.rings,n.rings];return e.length===r.length&&e.every(((t,n)=>t.length===r[n].length))}export{p as closeRings,g as closeRingsAndFixWinding,s as distanceFromPointToPolygon,l as distanceToSegmentSquared,e as geometryToCoordinates,r as getLength,f as getMidpoint,u as getPathLength,c as getPointOnPath,M as getRingArea,z as hasCompatibleTopology,h as isClockwise,a as isClosed,o as projectPointOnLine,y as unnormalizeGeometryOnDatelineCrossing,m as unnormalizeVerticesOnDatelineCrossing,x as unnormalizedCoordinate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getLength as n,getMidpoint as t,getRingArea as l}from\"./coordsUtils.js\";function e(n){return n?n.hasZ?[n.xmax-n.xmin/2,n.ymax-n.ymin/2,n.zmax-n.zmin/2]:[n.xmax-n.xmin/2,n.ymax-n.ymin/2]:null}function r(n){return n?o(n.rings,n.hasZ??!1):null}function o(n,t){if(!n||!n.length)return null;const l=[],e=[],r=t?[1/0,-1/0,1/0,-1/0,1/0,-1/0]:[1/0,-1/0,1/0,-1/0];for(let o=0,i=n.length;o<i;o++){const l=u(n[o],t,r);l&&e.push(l)}if(e.sort(((n,l)=>{let e=n[2]-l[2];return 0===e&&t&&(e=n[4]-l[4]),e})),e.length&&(l[0]=e[0][0],l[1]=e[0][1],t&&(l[2]=e[0][3]),(l[0]<r[0]||l[0]>r[1]||l[1]<r[2]||l[1]>r[3]||t&&(l[2]<r[4]||l[2]>r[5]))&&(l.length=0)),!l.length){const e=n[0]&&n[0].length?i(n[0],t):null;if(!e)return null;l[0]=e[0],l[1]=e[1],t&&e.length>2&&(l[2]=e[2])}return l}function u(n,t,l){let e=0,r=0,o=0,u=0,i=0;const s=n.length?n[0][0]:0,g=n.length?n[0][1]:0,h=n.length&&t?n[0][2]:0;for(let f=0;f<n.length;f++){const c=n[f],m=n[(f+1)%n.length],[x,a,y]=c,p=x-s,z=a-g,[Z,d,j]=m,U=Z-s,b=d-g,k=p*b-U*z;if(u+=k,e+=(p+U)*k,r+=(z+b)*k,t&&c.length>2&&m.length>2){const n=y-h,t=j-h,l=p*t-U*n;o+=(n+t)*l,i+=l}x<l[0]&&(l[0]=x),x>l[1]&&(l[1]=x),a<l[2]&&(l[2]=a),a>l[3]&&(l[3]=a),t&&(y<l[4]&&(l[4]=y),y>l[5]&&(l[5]=y))}if(u>0&&(u*=-1),i>0&&(i*=-1),!u)return null;u*=.5,i*=.5;const c=[e/(6*u)+s,r/(6*u)+g,u];return t&&(l[4]===l[5]||0===i?(c[3]=(l[4]+l[5])/2,c[4]=0):(c[3]=o/(6*i)+h,c[4]=i)),c}function i(l,e){const r=e?[0,0,0]:[0,0],o=e?[0,0,0]:[0,0];let u=0,i=0,s=0,g=0;for(let h=0,c=l.length;h<c-1;h++){const c=l[h],f=l[h+1];if(c&&f){r[0]=c[0],r[1]=c[1],o[0]=f[0],o[1]=f[1],e&&c.length>2&&f.length>2&&(r[2]=c[2],o[2]=f[2]);const l=n(r,o);if(l){u+=l;const n=t(c,f);i+=l*n[0],s+=l*n[1],e&&n.length>2&&(g+=l*n[2])}}}return u>0?e?[i/u,s/u,g/u]:[i/u,s/u]:l.length?l[0]:null}const s=1e-6;function g(n){if(!n||!n.rings)return null;const{rings:t}=n;let e=0;for(let o=0;o<t.length;o++)e+=l(t[o]);if(e<s)return o(t,!1);const r=[0,0],u=t[0][0];for(let l=0;l<t.length;l++)c(r,u,t[l]);return r[0]*=1/e,r[1]*=1/e,r[0]+=u[0],r[1]+=u[1],r}const h=1/3;function c(n,t,e){if(!n||!e||e.length<3)return null;const r=e[0],o=[0,0],u=[e[1][0]-r[0],e[1][1]-r[1]];let i;for(let l=2;l<e.length;l++)o[0]=e[l][0]-r[0],o[1]=e[l][1]-r[1],i=.5*h*(o[0]*u[1]-o[1]*u[0]),n[0]+=i*(u[0]+o[0]),n[1]+=i*(u[1]+o[1]),u[0]=o[0],u[1]=o[1];const s=l(e),g=[r[0],r[1]];return g[0]-=t[0],g[1]-=t[1],g[0]*=s,g[1]*=s,n[0]+=g[0],n[1]+=g[1],n}export{e as extentCentroid,i as lineCentroid,r as polygonCentroid,u as ringCentroid,o as ringsCentroid,g as weightedAreaCentroid};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import{equals as r}from\"../core/arrayUtils.js\";import{clone as e}from\"../core/lang.js\";import{isNone as s,isSome as i}from\"../core/maybe.js\";import{property as n}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{subclass as o}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as a}from\"../core/accessorSupport/decorators/writer.js\";import l from\"./Extent.js\";import h from\"./Geometry.js\";import p from\"./Point.js\";import c from\"./SpatialReference.js\";import{polygonCentroid as u}from\"./support/centroid.js\";import{polygonContainsPoint as m}from\"./support/contains.js\";import{isClockwise as f}from\"./support/coordsUtils.js\";import{getPolygonExtent as g}from\"./support/extentUtils.js\";import{isSelfIntersecting as y}from\"./support/intersectsBase.js\";import{project as d}from\"./support/webMercatorUtils.js\";import{updateSupportFromPoint as R}from\"./support/zmUtils.js\";var x;function j(t){return!Array.isArray(t[0])}let w=x=class extends h{static fromExtent(t){const r=t.clone().normalize(),e=t.spatialReference;let s=!1,i=!1;for(const o of r)o.hasZ&&(s=!0),o.hasM&&(i=!0);const n={rings:r.map((t=>{const r=[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]];if(s&&t.hasZ){const e=t.zmin+.5*(t.zmax-t.zmin);for(let t=0;t<r.length;t++)r[t].push(e)}if(i&&t.hasM){const e=t.mmin+.5*(t.mmax-t.mmin);for(let t=0;t<r.length;t++)r[t].push(e)}return r})),spatialReference:e};return s&&(n.hasZ=!0),i&&(n.hasM=!0),new x(n)}constructor(...t){super(...t),this.rings=[],this.type=\"polygon\"}normalizeCtorArgs(t,r){let e,s,i=null,n=null;return t&&!Array.isArray(t)?(i=t.rings?t.rings:null,r||(t.spatialReference?r=t.spatialReference:t.rings||(r=t)),e=t.hasZ,s=t.hasM):i=t,i=i||[],r=r||c.WGS84,i.length&&i[0]&&null!=i[0][0]&&\"number\"==typeof i[0][0]&&(i=[i]),n=i[0]&&i[0][0],n&&(void 0===e&&void 0===s?(e=n.length>2,s=n.length>3):void 0===e?e=s?n.length>3:n.length>2:void 0===s&&(s=e?n.length>3:n.length>2)),{rings:i,spatialReference:r,hasZ:e,hasM:s}}get cache(){return this.commitProperty(\"rings\"),this.commitProperty(\"hasZ\"),this.commitProperty(\"hasM\"),this.commitProperty(\"spatialReference\"),{}}get centroid(){const t=u(this);if(!t||isNaN(t[0])||isNaN(t[1])||this.hasZ&&isNaN(t[2]))return null;const r=new p;return r.x=t[0],r.y=t[1],r.spatialReference=this.spatialReference,this.hasZ&&(r.z=t[2]),r}get extent(){const{spatialReference:t}=this,r=g(this);if(!r)return null;const e=new l(r);return e.spatialReference=t,e}get isSelfIntersecting(){return y(this.rings)}writeRings(t,r){r.rings=e(this.rings)}addRing(t){if(!t)return;const r=this.rings,e=r.length;if(j(t)){const s=[];for(let r=0,e=t.length;r<e;r++)s[r]=t[r].toArray();r[e]=s}else r[e]=t.concat();return this.notifyChange(\"rings\"),this}clone(){const t=new x;return t.spatialReference=this.spatialReference,t.rings=e(this.rings),t.hasZ=this.hasZ,t.hasM=this.hasM,t}equals(t){if(this===t)return!0;if(s(t))return!1;const e=this.spatialReference,n=t.spatialReference;if(i(e)!==i(n))return!1;if(i(e)&&i(n)&&!e.equals(n))return!1;if(this.rings.length!==t.rings.length)return!1;const o=([t,r,e,s],[i,n,o,a])=>t===i&&r===n&&(null==e&&null==o||e===o)&&(null==s&&null==a||s===a);for(let s=0;s<this.rings.length;s++){const e=this.rings[s],i=t.rings[s];if(!r(e,i,o))return!1}return!0}contains(t){if(!t)return!1;const r=d(t,this.spatialReference);return m(this,i(r)?r:t)}isClockwise(t){let r;return r=j(t)?t.map((t=>this.hasZ?this.hasM?[t.x,t.y,t.z,t.m]:[t.x,t.y,t.z]:[t.x,t.y])):t,f(r,this.hasM,this.hasZ)}getPoint(t,r){if(!this._validateInputs(t,r))return null;const e=this.rings[t][r],s=this.hasZ,i=this.hasM;return s&&!i?new p(e[0],e[1],e[2],void 0,this.spatialReference):i&&!s?new p(e[0],e[1],void 0,e[2],this.spatialReference):s&&i?new p(e[0],e[1],e[2],e[3],this.spatialReference):new p(e[0],e[1],this.spatialReference)}insertPoint(t,r,e){return this._validateInputs(t,r,!0)?(R(this,e),Array.isArray(e)||(e=e.toArray()),this.rings[t].splice(r,0,e),this.notifyChange(\"rings\"),this):this}removePoint(t,r){if(!this._validateInputs(t,r))return null;const e=new p(this.rings[t].splice(r,1)[0],this.spatialReference);return this.notifyChange(\"rings\"),e}removeRing(t){if(!this._validateInputs(t,null))return null;const r=this.rings.splice(t,1)[0],e=this.spatialReference,s=r.map((t=>new p(t,e)));return this.notifyChange(\"rings\"),s}setPoint(t,r,e){return this._validateInputs(t,r)?(R(this,e),Array.isArray(e)||(e=e.toArray()),this.rings[t][r]=e,this.notifyChange(\"rings\"),this):this}_validateInputs(t,r,e=!1){if(null==t||t<0||t>=this.rings.length)return!1;if(null!=r){const s=this.rings[t];if(e&&(r<0||r>s.length))return!1;if(!e&&(r<0||r>=s.length))return!1}return!0}toJSON(t){return this.write({},t)}};t([n({readOnly:!0})],w.prototype,\"cache\",null),t([n({readOnly:!0})],w.prototype,\"centroid\",null),t([n({readOnly:!0})],w.prototype,\"extent\",null),t([n({readOnly:!0})],w.prototype,\"isSelfIntersecting\",null),t([n({type:[[[Number]]],json:{write:{isRequired:!0}}})],w.prototype,\"rings\",void 0),t([a(\"rings\")],w.prototype,\"writeRings\",null),w=x=t([o(\"esri.geometry.Polygon\")],w),w.prototype.toJSON.isDefaultToJSON=!0;const v=w;export{v as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIuG,SAASA,GAAEC,IAAE;AAAC,MAAG,CAACA,GAAE,QAAO;AAAK,MAAG,MAAM,QAAQA,EAAC,EAAE,QAAOA;AAAE,QAAM,IAAEA,GAAE,MAAKD,KAAEC,GAAE;AAAK,MAAG,YAAUA,GAAE,KAAK,QAAOD,MAAG,IAAE,CAACC,GAAE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,IAAE,IAAE,CAACA,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,IAAED,KAAE,CAACC,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,IAAE,CAACA,GAAE,GAAEA,GAAE,CAAC;AAAE,MAAG,cAAYA,GAAE,KAAK,QAAOA,GAAE,MAAM,MAAM,CAAC;AAAE,MAAG,eAAaA,GAAE,KAAK,QAAOA,GAAE,MAAM,MAAM,CAAC;AAAE,MAAG,iBAAeA,GAAE,KAAK,QAAOA,GAAE,OAAO,MAAM,CAAC;AAAE,MAAG,aAAWA,GAAE,MAAK;AAAC,UAAMC,KAAED,GAAE,MAAM,EAAE,UAAU;AAAE,QAAG,CAACC,GAAE,QAAO;AAAK,QAAIF,KAAE,OAAGG,KAAE;AAAG,WAAOD,GAAE,QAAS,CAAAD,OAAG;AAAC,MAAAA,GAAE,SAAOD,KAAE,OAAIC,GAAE,SAAOE,KAAE;AAAA,IAAG,CAAE,GAAED,GAAE,IAAK,CAAAD,OAAG;AAAC,YAAMC,KAAE,CAAC,CAACD,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,CAAC;AAAE,UAAGD,MAAGC,GAAE,MAAK;AAAC,cAAMD,KAAE,OAAIC,GAAE,OAAKA,GAAE;AAAM,iBAAQA,KAAE,GAAEA,KAAEC,GAAE,QAAOD,KAAI,CAAAC,GAAED,EAAC,EAAE,KAAKD,EAAC;AAAA,MAAC;AAAC,UAAGG,MAAGF,GAAE,MAAK;AAAC,cAAMD,KAAE,OAAIC,GAAE,OAAKA,GAAE;AAAM,iBAAQA,KAAE,GAAEA,KAAEC,GAAE,QAAOD,KAAI,CAAAC,GAAED,EAAC,EAAE,KAAKD,EAAC;AAAA,MAAC;AAAC,aAAOE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAASC,GAAEF,IAAE,GAAE;AAAC,QAAMD,KAAE,EAAE,CAAC,IAAEC,GAAE,CAAC,GAAEE,KAAE,EAAE,CAAC,IAAEF,GAAE,CAAC;AAAE,MAAGA,GAAE,SAAO,KAAG,EAAE,SAAO,GAAE;AAAC,UAAMG,KAAEH,GAAE,CAAC,IAAE,EAAE,CAAC;AAAE,WAAO,KAAK,KAAKD,KAAEA,KAAEG,KAAEA,KAAEC,KAAEA,EAAC;AAAA,EAAC;AAAC,SAAO,KAAK,KAAKJ,KAAEA,KAAEG,KAAEA,EAAC;AAAC;AAAC,SAASC,GAAEH,IAAE,GAAED,IAAE;AAAC,QAAMG,KAAEF,GAAE,CAAC,IAAED,MAAG,EAAE,CAAC,IAAEC,GAAE,CAAC,IAAGG,KAAEH,GAAE,CAAC,IAAED,MAAG,EAAE,CAAC,IAAEC,GAAE,CAAC;AAAG,SAAOA,GAAE,SAAO,KAAG,EAAE,SAAO,IAAE,CAACE,IAAEC,IAAEH,GAAE,CAAC,IAAED,MAAG,EAAE,CAAC,IAAEC,GAAE,CAAC,EAAE,IAAE,CAACE,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAE,GAAED,IAAEG,IAAE;AAAC,QAAK,CAACC,IAAEC,EAAC,IAAE,GAAE,CAACC,IAAEC,EAAC,IAAEP,GAAEG,EAAC,GAAE,CAACK,IAAEC,EAAC,IAAET,GAAEG,KAAE,CAAC,GAAEO,KAAEF,KAAEF,IAAEK,KAAEF,KAAEF,IAAEK,KAAEF,KAAEA,KAAEC,KAAEA,IAAEE,MAAGT,KAAEE,MAAGI,MAAGL,KAAEE,MAAGI,IAAEG,KAAE,KAAK,IAAI,GAAE,KAAK,IAAI,GAAED,KAAED,EAAC,CAAC;AAAE,SAAOX,GAAE,CAAC,IAAEK,KAAEI,KAAEI,IAAEb,GAAE,CAAC,IAAEM,KAAEI,KAAEG,IAAEb;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAED,IAAE;AAAC,QAAMG,KAAEH,GAAE;AAAM,MAAII,IAAEC,IAAEC,KAAE,OAAGE,KAAE,IAAE;AAAE,WAAQC,KAAE,GAAEA,KAAEN,GAAE,QAAOM,MAAI;AAAC,UAAMT,KAAEG,GAAEM,EAAC;AAAE,aAAQN,KAAE,GAAEM,KAAET,GAAE,SAAO,GAAEG,KAAEH,GAAE,QAAOS,KAAEN,KAAI,CAAAC,KAAEJ,GAAEG,EAAC,GAAEE,KAAEL,GAAES,EAAC,GAAEL,GAAE,CAAC,IAAE,KAAGC,GAAE,CAAC,IAAE,KAAGJ,MAAGI,GAAE,CAAC,IAAED,GAAE,CAAC,MAAI,IAAEA,GAAE,CAAC,MAAIC,GAAE,CAAC,IAAED,GAAE,CAAC,KAAGA,GAAE,CAAC,MAAIE,KAAE,CAACA,KAAGE,KAAE,KAAK,IAAIA,IAAE,EAAEP,IAAE,GAAEG,IAAEC,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO,MAAIG,KAAE,KAAGF,KAAE,IAAE,MAAI,KAAK,KAAKE,EAAC;AAAC;AAAC,SAAS,EAAEP,IAAE,GAAED,IAAEG,IAAE;AAAC,MAAIC,KAAEJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,GAAEM,KAAEH,GAAE,CAAC,IAAEC,IAAEG,KAAEJ,GAAE,CAAC,IAAEE;AAAE,MAAG,MAAIC,MAAG,MAAIC,IAAE;AAAC,UAAMP,OAAIC,KAAEG,MAAGE,MAAG,IAAED,MAAGE,OAAID,KAAEA,KAAEC,KAAEA;AAAG,IAAAP,KAAE,KAAGI,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,KAAGH,KAAE,MAAII,MAAGE,KAAEN,IAAEK,MAAGE,KAAEP;AAAA,EAAE;AAAC,SAAOM,KAAEL,KAAEG,IAAEG,KAAE,IAAEF,IAAEC,KAAEA,KAAEC,KAAEA;AAAC;AAAC,SAASC,GAAEP,IAAE,GAAE;AAAC,SAAOG,GAAEH,IAAE,GAAE,GAAE;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAM,IAAEA,GAAE;AAAO,MAAID,KAAE;AAAE,WAAQI,KAAE,GAAEA,KAAE,IAAE,GAAE,EAAEA,GAAE,CAAAJ,MAAGG,GAAEF,GAAEG,EAAC,GAAEH,GAAEG,KAAE,CAAC,CAAC;AAAE,SAAOJ;AAAC;AAAC,SAASU,GAAET,IAAE,GAAE;AAAC,MAAG,KAAG,EAAE,QAAOA,GAAE,CAAC;AAAE,QAAMD,KAAEC,GAAE;AAAO,MAAII,KAAE;AAAE,WAAQC,KAAE,GAAEA,KAAEN,KAAE,GAAE,EAAEM,IAAE;AAAC,UAAMN,KAAEG,GAAEF,GAAEK,EAAC,GAAEL,GAAEK,KAAE,CAAC,CAAC;AAAE,QAAG,IAAED,KAAEL,IAAE;AAAC,YAAMG,MAAG,IAAEE,MAAGL;AAAE,aAAOI,GAAEH,GAAEK,EAAC,GAAEL,GAAEK,KAAE,CAAC,GAAEH,EAAC;AAAA,IAAC;AAAC,IAAAE,MAAGL;AAAA,EAAC;AAAC,SAAOC,GAAED,KAAE,CAAC;AAAC;AAAC,SAASW,GAAEV,IAAE,GAAED,IAAE;AAAC,QAAMG,KAAEF,GAAE;AAAO,MAAIG,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAAQC,KAAE,GAAEA,KAAEJ,IAAEI,MAAI;AAAC,UAAMC,KAAEP,GAAEM,EAAC,GAAEE,KAAER,IAAGM,KAAE,KAAGJ,EAAC;AAAE,QAAIO,KAAE;AAAE,IAAAN,MAAGI,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEA,GAAE,SAAO,KAAGC,GAAE,SAAO,KAAGT,OAAIK,MAAGG,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEE,KAAE,IAAGF,GAAE,SAAOE,MAAGD,GAAE,SAAOC,MAAG,MAAIJ,MAAGE,GAAE,CAAC,IAAEC,GAAEC,EAAC,IAAED,GAAE,CAAC,IAAED,GAAEE,EAAC;AAAA,EAAE;AAAC,SAAON,MAAG,KAAGC,MAAG,KAAGC,MAAG;AAAC;AAAC,SAASM,GAAE,GAAE;AAAC,QAAMZ,KAAE,EAAE;AAAO,SAAOA,KAAE,KAAG,EAAE,EAAE,CAAC,GAAE,EAAEA,KAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,MAAG,WAAUA,OAAIa,GAAEb,EAAC,GAAEA,GAAE,MAAM,SAAO,KAAG,CAACU,GAAEV,GAAE,MAAM,CAAC,GAAEA,GAAE,QAAM,OAAGA,GAAE,QAAM,KAAE,GAAG,YAAU,KAAKA,GAAE,MAAM,GAAE,QAAQ;AAAC;AAAC,SAASa,GAAEb,IAAE;AAAC,MAAG,WAAUA,GAAE,YAAU,KAAKA,GAAE,MAAM,CAAAW,GAAE,CAAC,KAAG,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC;AAAC;AAAC,SAASG,GAAEd,IAAE;AAAC,MAAG,cAAYA,GAAE,QAAM,eAAaA,GAAE,KAAK,QAAOA;AAAE,SAAO,EAAE,cAAYA,GAAE,OAAKA,GAAE,QAAMA,GAAE,OAAMA,GAAE,gBAAgB,GAAEA;AAAC;AAAC,SAAS,EAAEA,IAAED,IAAE;AAAC,QAAMG,KAAE,EAAEH,EAAC;AAAE,MAAG,CAACG,GAAE;AAAO,QAAMC,KAAED,GAAE,MAAM,CAAC,GAAEE,KAAEF,GAAE,MAAM,CAAC,GAAEG,KAAED,KAAED;AAAE,aAAU,KAAKH,IAAE;AAAC,QAAIA,KAAE,IAAE,GAAED,KAAE,KAAG;AAAE,eAAUM,MAAK,GAAE;AAAC,YAAMJ,KAAE,EAAEI,GAAE,CAAC,GAAEF,IAAEC,EAAC;AAAE,MAAAJ,KAAE,KAAK,IAAIA,IAAEC,EAAC,GAAEF,KAAE,KAAK,IAAIA,IAAEE,EAAC,GAAEI,GAAE,CAAC,IAAEJ;AAAA,IAAC;AAAC,UAAMC,KAAEH,KAAEC;AAAE,IAAAK,KAAEH,KAAEA,MAAG,EAAE,QAAS,CAAAF,OAAG;AAAC,MAAAA,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC,KAAGK;AAAA,IAAE,CAAE;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEL,IAAE,GAAED,IAAE;AAAC,QAAMG,KAAEH,KAAE;AAAE,SAAOC,KAAE,IAAED,MAAG,IAAEC,MAAGE,KAAEF,KAAED,KAAE,KAAGC,KAAE,KAAGE,KAAEF;AAAC;AAAC,SAASe,GAAEf,IAAE;AAAC,MAAG,CAACA,MAAGA,GAAE,SAAO,EAAE,QAAO;AAAE,MAAI,IAAE;AAAE,QAAMD,KAAEC,GAAE,SAAO;AAAE,WAAQE,KAAE,GAAEA,KAAEH,IAAEG,KAAI,OAAIF,GAAEE,EAAC,EAAE,CAAC,IAAEF,GAAEE,KAAE,CAAC,EAAE,CAAC,MAAIF,GAAEE,EAAC,EAAE,CAAC,IAAEF,GAAEE,KAAE,CAAC,EAAE,CAAC;AAAG,SAAO,MAAIF,GAAED,EAAC,EAAE,CAAC,IAAEC,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAED,EAAC,EAAE,CAAC,IAAEC,GAAE,CAAC,EAAE,CAAC,IAAG,OAAI;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAGA,OAAI,EAAE,QAAM;AAAG,MAAGA,GAAE,SAAO,EAAE,KAAK,QAAM;AAAG,MAAG,YAAUA,GAAE,QAAM,aAAWA,GAAE,KAAK,QAAM;AAAG,MAAG,iBAAeA,GAAE,KAAK,QAAOA,GAAE,OAAO,WAAS,EAAE,OAAO;AAAO,QAAK,CAACD,IAAEG,EAAC,IAAE,eAAaF,GAAE,OAAK,CAACA,GAAE,OAAM,EAAE,KAAK,IAAE,CAACA,GAAE,OAAM,EAAE,KAAK;AAAE,SAAOD,GAAE,WAASG,GAAE,UAAQH,GAAE,MAAO,CAACC,IAAEC,OAAID,GAAE,WAASE,GAAED,EAAC,EAAE,MAAO;AAAC;;;ACA11G,SAASe,GAAE,GAAE;AAAC,SAAO,IAAE,EAAE,OAAK,CAAC,EAAE,OAAK,EAAE,OAAK,GAAE,EAAE,OAAK,EAAE,OAAK,GAAE,EAAE,OAAK,EAAE,OAAK,CAAC,IAAE,CAAC,EAAE,OAAK,EAAE,OAAK,GAAE,EAAE,OAAK,EAAE,OAAK,CAAC,IAAE;AAAI;AAAC,SAASC,GAAE,GAAE;AAAC,SAAO,IAAEC,GAAE,EAAE,OAAM,EAAE,QAAM,KAAE,IAAE;AAAI;AAAC,SAASA,GAAE,GAAEC,IAAE;AAAC,MAAG,CAAC,KAAG,CAAC,EAAE,OAAO,QAAO;AAAK,QAAMC,KAAE,CAAC,GAAEJ,KAAE,CAAC,GAAEC,KAAEE,KAAE,CAAC,IAAE,GAAE,KAAG,GAAE,IAAE,GAAE,KAAG,GAAE,IAAE,GAAE,KAAG,CAAC,IAAE,CAAC,IAAE,GAAE,KAAG,GAAE,IAAE,GAAE,KAAG,CAAC;AAAE,WAAQD,KAAE,GAAEG,KAAE,EAAE,QAAOH,KAAEG,IAAEH,MAAI;AAAC,UAAME,KAAEE,GAAE,EAAEJ,EAAC,GAAEC,IAAEF,EAAC;AAAE,IAAAG,MAAGJ,GAAE,KAAKI,EAAC;AAAA,EAAC;AAAC,MAAGJ,GAAE,KAAM,CAACO,IAAEH,OAAI;AAAC,QAAIJ,KAAEO,GAAE,CAAC,IAAEH,GAAE,CAAC;AAAE,WAAO,MAAIJ,MAAGG,OAAIH,KAAEO,GAAE,CAAC,IAAEH,GAAE,CAAC,IAAGJ;AAAA,EAAC,CAAE,GAAEA,GAAE,WAASI,GAAE,CAAC,IAAEJ,GAAE,CAAC,EAAE,CAAC,GAAEI,GAAE,CAAC,IAAEJ,GAAE,CAAC,EAAE,CAAC,GAAEG,OAAIC,GAAE,CAAC,IAAEJ,GAAE,CAAC,EAAE,CAAC,KAAII,GAAE,CAAC,IAAEH,GAAE,CAAC,KAAGG,GAAE,CAAC,IAAEH,GAAE,CAAC,KAAGG,GAAE,CAAC,IAAEH,GAAE,CAAC,KAAGG,GAAE,CAAC,IAAEH,GAAE,CAAC,KAAGE,OAAIC,GAAE,CAAC,IAAEH,GAAE,CAAC,KAAGG,GAAE,CAAC,IAAEH,GAAE,CAAC,QAAMG,GAAE,SAAO,KAAI,CAACA,GAAE,QAAO;AAAC,UAAMJ,KAAE,EAAE,CAAC,KAAG,EAAE,CAAC,EAAE,SAAOK,GAAE,EAAE,CAAC,GAAEF,EAAC,IAAE;AAAK,QAAG,CAACH,GAAE,QAAO;AAAK,IAAAI,GAAE,CAAC,IAAEJ,GAAE,CAAC,GAAEI,GAAE,CAAC,IAAEJ,GAAE,CAAC,GAAEG,MAAGH,GAAE,SAAO,MAAII,GAAE,CAAC,IAAEJ,GAAE,CAAC;AAAA,EAAE;AAAC,SAAOI;AAAC;AAAC,SAASE,GAAE,GAAEH,IAAEC,IAAE;AAAC,MAAIJ,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEI,KAAE,GAAED,KAAE;AAAE,QAAMG,KAAE,EAAE,SAAO,EAAE,CAAC,EAAE,CAAC,IAAE,GAAEC,KAAE,EAAE,SAAO,EAAE,CAAC,EAAE,CAAC,IAAE,GAAEC,KAAE,EAAE,UAAQP,KAAE,EAAE,CAAC,EAAE,CAAC,IAAE;AAAE,WAAQQ,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMC,KAAE,EAAED,EAAC,GAAEE,KAAE,GAAGF,KAAE,KAAG,EAAE,MAAM,GAAE,CAACG,IAAEC,IAAEC,EAAC,IAAEJ,IAAEK,KAAEH,KAAEN,IAAEU,KAAEH,KAAEN,IAAE,CAAC,GAAE,GAAEU,EAAC,IAAEN,IAAE,IAAE,IAAEL,IAAE,IAAE,IAAEC,IAAE,IAAEQ,KAAE,IAAE,IAAEC;AAAE,QAAGZ,MAAG,GAAEN,OAAIiB,KAAE,KAAG,GAAEhB,OAAIiB,KAAE,KAAG,GAAEf,MAAGS,GAAE,SAAO,KAAGC,GAAE,SAAO,GAAE;AAAC,YAAMN,KAAES,KAAEN,IAAEP,KAAEgB,KAAET,IAAEN,KAAEa,KAAEd,KAAE,IAAEI;AAAE,MAAAL,OAAIK,KAAEJ,MAAGC,IAAEC,MAAGD;AAAA,IAAC;AAAC,IAAAU,KAAEV,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAEU,KAAGA,KAAEV,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAEU,KAAGC,KAAEX,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAEW,KAAGA,KAAEX,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAEW,KAAGZ,OAAIa,KAAEZ,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAEY,KAAGA,KAAEZ,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAEY;AAAA,EAAG;AAAC,MAAGV,KAAE,MAAIA,MAAG,KAAID,KAAE,MAAIA,MAAG,KAAI,CAACC,GAAE,QAAO;AAAK,EAAAA,MAAG,KAAGD,MAAG;AAAG,QAAMO,KAAE,CAACZ,MAAG,IAAEM,MAAGE,IAAEP,MAAG,IAAEK,MAAGG,IAAEH,EAAC;AAAE,SAAOH,OAAIC,GAAE,CAAC,MAAIA,GAAE,CAAC,KAAG,MAAIC,MAAGO,GAAE,CAAC,KAAGR,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAG,GAAEQ,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC,IAAEV,MAAG,IAAEG,MAAGK,IAAEE,GAAE,CAAC,IAAEP,MAAIO;AAAC;AAAC,SAASP,GAAED,IAAEJ,IAAE;AAAC,QAAMC,KAAED,KAAE,CAAC,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,CAAC,GAAEE,KAAEF,KAAE,CAAC,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,CAAC;AAAE,MAAIM,KAAE,GAAED,KAAE,GAAEG,KAAE,GAAEC,KAAE;AAAE,WAAQC,KAAE,GAAEE,KAAER,GAAE,QAAOM,KAAEE,KAAE,GAAEF,MAAI;AAAC,UAAME,KAAER,GAAEM,EAAC,GAAEC,KAAEP,GAAEM,KAAE,CAAC;AAAE,QAAGE,MAAGD,IAAE;AAAC,MAAAV,GAAE,CAAC,IAAEW,GAAE,CAAC,GAAEX,GAAE,CAAC,IAAEW,GAAE,CAAC,GAAEV,GAAE,CAAC,IAAES,GAAE,CAAC,GAAET,GAAE,CAAC,IAAES,GAAE,CAAC,GAAEX,MAAGY,GAAE,SAAO,KAAGD,GAAE,SAAO,MAAIV,GAAE,CAAC,IAAEW,GAAE,CAAC,GAAEV,GAAE,CAAC,IAAES,GAAE,CAAC;AAAG,YAAMP,KAAEH,GAAEA,IAAEC,EAAC;AAAE,UAAGE,IAAE;AAAC,QAAAE,MAAGF;AAAE,cAAM,IAAEO,GAAEC,IAAED,EAAC;AAAE,QAAAN,MAAGD,KAAE,EAAE,CAAC,GAAEI,MAAGJ,KAAE,EAAE,CAAC,GAAEJ,MAAG,EAAE,SAAO,MAAIS,MAAGL,KAAE,EAAE,CAAC;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOE,KAAE,IAAEN,KAAE,CAACK,KAAEC,IAAEE,KAAEF,IAAEG,KAAEH,EAAC,IAAE,CAACD,KAAEC,IAAEE,KAAEF,EAAC,IAAEF,GAAE,SAAOA,GAAE,CAAC,IAAE;AAAI;AAAC,IAAMI,KAAE;AAAK,SAASC,GAAE,GAAE;AAAC,MAAG,CAAC,KAAG,CAAC,EAAE,MAAM,QAAO;AAAK,QAAK,EAAC,OAAMN,GAAC,IAAE;AAAE,MAAIH,KAAE;AAAE,WAAQE,KAAE,GAAEA,KAAEC,GAAE,QAAOD,KAAI,CAAAF,MAAGoB,GAAEjB,GAAED,EAAC,CAAC;AAAE,MAAGF,KAAEQ,GAAE,QAAON,GAAEC,IAAE,KAAE;AAAE,QAAMF,KAAE,CAAC,GAAE,CAAC,GAAEK,KAAEH,GAAE,CAAC,EAAE,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAQ,GAAEX,IAAEK,IAAEH,GAAEC,EAAC,CAAC;AAAE,SAAOH,GAAE,CAAC,KAAG,IAAED,IAAEC,GAAE,CAAC,KAAG,IAAED,IAAEC,GAAE,CAAC,KAAGK,GAAE,CAAC,GAAEL,GAAE,CAAC,KAAGK,GAAE,CAAC,GAAEL;AAAC;AAAC,IAAMS,KAAE,IAAE;AAAE,SAASE,GAAE,GAAET,IAAEH,IAAE;AAAC,MAAG,CAAC,KAAG,CAACA,MAAGA,GAAE,SAAO,EAAE,QAAO;AAAK,QAAMC,KAAED,GAAE,CAAC,GAAEE,KAAE,CAAC,GAAE,CAAC,GAAEI,KAAE,CAACN,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,CAAC,GAAED,GAAE,CAAC,EAAE,CAAC,IAAEC,GAAE,CAAC,CAAC;AAAE,MAAII;AAAE,WAAQD,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,KAAI,CAAAF,GAAE,CAAC,IAAEF,GAAEI,EAAC,EAAE,CAAC,IAAEH,GAAE,CAAC,GAAEC,GAAE,CAAC,IAAEF,GAAEI,EAAC,EAAE,CAAC,IAAEH,GAAE,CAAC,GAAEI,KAAE,MAAGK,MAAGR,GAAE,CAAC,IAAEI,GAAE,CAAC,IAAEJ,GAAE,CAAC,IAAEI,GAAE,CAAC,IAAG,EAAE,CAAC,KAAGD,MAAGC,GAAE,CAAC,IAAEJ,GAAE,CAAC,IAAG,EAAE,CAAC,KAAGG,MAAGC,GAAE,CAAC,IAAEJ,GAAE,CAAC,IAAGI,GAAE,CAAC,IAAEJ,GAAE,CAAC,GAAEI,GAAE,CAAC,IAAEJ,GAAE,CAAC;AAAE,QAAMM,KAAEY,GAAEpB,EAAC,GAAES,KAAE,CAACR,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,SAAOQ,GAAE,CAAC,KAAGN,GAAE,CAAC,GAAEM,GAAE,CAAC,KAAGN,GAAE,CAAC,GAAEM,GAAE,CAAC,KAAGD,IAAEC,GAAE,CAAC,KAAGD,IAAE,EAAE,CAAC,KAAGC,GAAE,CAAC,GAAE,EAAE,CAAC,KAAGA,GAAE,CAAC,GAAE;AAAC;;;ACAl3C,IAAIY;AAAE,SAAS,EAAEC,IAAE;AAAC,SAAM,CAAC,MAAM,QAAQA,GAAE,CAAC,CAAC;AAAC;AAAC,IAAIC,KAAEF,KAAE,cAAcG,GAAC;AAAA,EAAC,OAAO,WAAWF,IAAE;AAAC,UAAMG,KAAEH,GAAE,MAAM,EAAE,UAAU,GAAEI,KAAEJ,GAAE;AAAiB,QAAIK,KAAE,OAAGC,KAAE;AAAG,eAAUC,MAAKJ,GAAE,CAAAI,GAAE,SAAOF,KAAE,OAAIE,GAAE,SAAOD,KAAE;AAAI,UAAM,IAAE,EAAC,OAAMH,GAAE,IAAK,CAAAH,OAAG;AAAC,YAAMG,KAAE,CAAC,CAACH,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,CAAC;AAAE,UAAGK,MAAGL,GAAE,MAAK;AAAC,cAAMI,KAAEJ,GAAE,OAAK,OAAIA,GAAE,OAAKA,GAAE;AAAM,iBAAQA,KAAE,GAAEA,KAAEG,GAAE,QAAOH,KAAI,CAAAG,GAAEH,EAAC,EAAE,KAAKI,EAAC;AAAA,MAAC;AAAC,UAAGE,MAAGN,GAAE,MAAK;AAAC,cAAMI,KAAEJ,GAAE,OAAK,OAAIA,GAAE,OAAKA,GAAE;AAAM,iBAAQA,KAAE,GAAEA,KAAEG,GAAE,QAAOH,KAAI,CAAAG,GAAEH,EAAC,EAAE,KAAKI,EAAC;AAAA,MAAC;AAAC,aAAOD;AAAA,IAAC,CAAE,GAAE,kBAAiBC,GAAC;AAAE,WAAOC,OAAI,EAAE,OAAK,OAAIC,OAAI,EAAE,OAAK,OAAI,IAAIP,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,QAAM,CAAC,GAAE,KAAK,OAAK;AAAA,EAAS;AAAA,EAAC,kBAAkBA,IAAEG,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE,MAAK,IAAE;AAAK,WAAON,MAAG,CAAC,MAAM,QAAQA,EAAC,KAAGM,KAAEN,GAAE,QAAMA,GAAE,QAAM,MAAKG,OAAIH,GAAE,mBAAiBG,KAAEH,GAAE,mBAAiBA,GAAE,UAAQG,KAAEH,MAAII,KAAEJ,GAAE,MAAKK,KAAEL,GAAE,QAAMM,KAAEN,IAAEM,KAAEA,MAAG,CAAC,GAAEH,KAAEA,MAAG,EAAE,OAAMG,GAAE,UAAQA,GAAE,CAAC,KAAG,QAAMA,GAAE,CAAC,EAAE,CAAC,KAAG,YAAU,OAAOA,GAAE,CAAC,EAAE,CAAC,MAAIA,KAAE,CAACA,EAAC,IAAG,IAAEA,GAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,GAAE,MAAI,WAASF,MAAG,WAASC,MAAGD,KAAE,EAAE,SAAO,GAAEC,KAAE,EAAE,SAAO,KAAG,WAASD,KAAEA,KAAEC,KAAE,EAAE,SAAO,IAAE,EAAE,SAAO,IAAE,WAASA,OAAIA,KAAED,KAAE,EAAE,SAAO,IAAE,EAAE,SAAO,KAAI,EAAC,OAAME,IAAE,kBAAiBH,IAAE,MAAKC,IAAE,MAAKC,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,eAAe,OAAO,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,kBAAkB,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,UAAML,KAAEG,GAAE,IAAI;AAAE,QAAG,CAACH,MAAG,MAAMA,GAAE,CAAC,CAAC,KAAG,MAAMA,GAAE,CAAC,CAAC,KAAG,KAAK,QAAM,MAAMA,GAAE,CAAC,CAAC,EAAE,QAAO;AAAK,UAAMG,KAAE,IAAI;AAAE,WAAOA,GAAE,IAAEH,GAAE,CAAC,GAAEG,GAAE,IAAEH,GAAE,CAAC,GAAEG,GAAE,mBAAiB,KAAK,kBAAiB,KAAK,SAAOA,GAAE,IAAEH,GAAE,CAAC,IAAGG;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,UAAK,EAAC,kBAAiBH,GAAC,IAAE,MAAKG,KAAE,EAAE,IAAI;AAAE,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMC,KAAE,IAAIH,GAAEE,EAAC;AAAE,WAAOC,GAAE,mBAAiBJ,IAAEI;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAoB;AAAC,WAAOF,GAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,WAAWF,IAAEG,IAAE;AAAC,IAAAA,GAAE,QAAM,EAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,QAAQH,IAAE;AAAC,QAAG,CAACA,GAAE;AAAO,UAAMG,KAAE,KAAK,OAAMC,KAAED,GAAE;AAAO,QAAG,EAAEH,EAAC,GAAE;AAAC,YAAMK,KAAE,CAAC;AAAE,eAAQF,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAED,KAAI,CAAAE,GAAEF,EAAC,IAAEH,GAAEG,EAAC,EAAE,QAAQ;AAAE,MAAAA,GAAEC,EAAC,IAAEC;AAAA,IAAC,MAAM,CAAAF,GAAEC,EAAC,IAAEJ,GAAE,OAAO;AAAE,WAAO,KAAK,aAAa,OAAO,GAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,IAAID;AAAE,WAAOC,GAAE,mBAAiB,KAAK,kBAAiBA,GAAE,QAAM,EAAE,KAAK,KAAK,GAAEA,GAAE,OAAK,KAAK,MAAKA,GAAE,OAAK,KAAK,MAAKA;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,QAAG,SAAOA,GAAE,QAAM;AAAG,QAAG,EAAEA,EAAC,EAAE,QAAM;AAAG,UAAMI,KAAE,KAAK,kBAAiB,IAAEJ,GAAE;AAAiB,QAAG,EAAEI,EAAC,MAAI,EAAE,CAAC,EAAE,QAAM;AAAG,QAAG,EAAEA,EAAC,KAAG,EAAE,CAAC,KAAG,CAACA,GAAE,OAAO,CAAC,EAAE,QAAM;AAAG,QAAG,KAAK,MAAM,WAASJ,GAAE,MAAM,OAAO,QAAM;AAAG,UAAMO,KAAE,CAAC,CAACP,IAAEG,IAAEC,IAAEC,EAAC,GAAE,CAACC,IAAEE,IAAED,IAAEE,EAAC,MAAIT,OAAIM,MAAGH,OAAIK,OAAI,QAAMJ,MAAG,QAAMG,MAAGH,OAAIG,QAAK,QAAMF,MAAG,QAAMI,MAAGJ,OAAII;AAAG,aAAQJ,KAAE,GAAEA,KAAE,KAAK,MAAM,QAAOA,MAAI;AAAC,YAAMD,KAAE,KAAK,MAAMC,EAAC,GAAEC,KAAEN,GAAE,MAAMK,EAAC;AAAE,UAAG,CAAC,EAAED,IAAEE,IAAEC,EAAC,EAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,SAASP,IAAE;AAAC,QAAG,CAACA,GAAE,QAAM;AAAG,UAAMG,KAAE,EAAEH,IAAE,KAAK,gBAAgB;AAAE,WAAO,EAAE,MAAK,EAAEG,EAAC,IAAEA,KAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,QAAIG;AAAE,WAAOA,KAAE,EAAEH,EAAC,IAAEA,GAAE,IAAK,CAAAA,OAAG,KAAK,OAAK,KAAK,OAAK,CAACA,GAAE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,IAAE,CAACA,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,IAAE,CAACA,GAAE,GAAEA,GAAE,CAAC,CAAE,IAAEA,IAAEU,GAAEP,IAAE,KAAK,MAAK,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,SAASH,IAAEG,IAAE;AAAC,QAAG,CAAC,KAAK,gBAAgBH,IAAEG,EAAC,EAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,MAAMJ,EAAC,EAAEG,EAAC,GAAEE,KAAE,KAAK,MAAKC,KAAE,KAAK;AAAK,WAAOD,MAAG,CAACC,KAAE,IAAI,EAAEF,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,QAAO,KAAK,gBAAgB,IAAEE,MAAG,CAACD,KAAE,IAAI,EAAED,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,GAAE,KAAK,gBAAgB,IAAEC,MAAGC,KAAE,IAAI,EAAEF,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,KAAK,gBAAgB,IAAE,IAAI,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,YAAYJ,IAAEG,IAAEC,IAAE;AAAC,WAAO,KAAK,gBAAgBJ,IAAEG,IAAE,IAAE,KAAG,EAAE,MAAKC,EAAC,GAAE,MAAM,QAAQA,EAAC,MAAIA,KAAEA,GAAE,QAAQ,IAAG,KAAK,MAAMJ,EAAC,EAAE,OAAOG,IAAE,GAAEC,EAAC,GAAE,KAAK,aAAa,OAAO,GAAE,QAAM;AAAA,EAAI;AAAA,EAAC,YAAYJ,IAAEG,IAAE;AAAC,QAAG,CAAC,KAAK,gBAAgBH,IAAEG,EAAC,EAAE,QAAO;AAAK,UAAMC,KAAE,IAAI,EAAE,KAAK,MAAMJ,EAAC,EAAE,OAAOG,IAAE,CAAC,EAAE,CAAC,GAAE,KAAK,gBAAgB;AAAE,WAAO,KAAK,aAAa,OAAO,GAAEC;AAAA,EAAC;AAAA,EAAC,WAAWJ,IAAE;AAAC,QAAG,CAAC,KAAK,gBAAgBA,IAAE,IAAI,EAAE,QAAO;AAAK,UAAMG,KAAE,KAAK,MAAM,OAAOH,IAAE,CAAC,EAAE,CAAC,GAAEI,KAAE,KAAK,kBAAiBC,KAAEF,GAAE,IAAK,CAAAH,OAAG,IAAI,EAAEA,IAAEI,EAAC,CAAE;AAAE,WAAO,KAAK,aAAa,OAAO,GAAEC;AAAA,EAAC;AAAA,EAAC,SAASL,IAAEG,IAAEC,IAAE;AAAC,WAAO,KAAK,gBAAgBJ,IAAEG,EAAC,KAAG,EAAE,MAAKC,EAAC,GAAE,MAAM,QAAQA,EAAC,MAAIA,KAAEA,GAAE,QAAQ,IAAG,KAAK,MAAMJ,EAAC,EAAEG,EAAC,IAAEC,IAAE,KAAK,aAAa,OAAO,GAAE,QAAM;AAAA,EAAI;AAAA,EAAC,gBAAgBJ,IAAEG,IAAEC,KAAE,OAAG;AAAC,QAAG,QAAMJ,MAAGA,KAAE,KAAGA,MAAG,KAAK,MAAM,OAAO,QAAM;AAAG,QAAG,QAAMG,IAAE;AAAC,YAAME,KAAE,KAAK,MAAML,EAAC;AAAE,UAAGI,OAAID,KAAE,KAAGA,KAAEE,GAAE,QAAQ,QAAM;AAAG,UAAG,CAACD,OAAID,KAAE,KAAGA,MAAGE,GAAE,QAAQ,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,OAAOL,IAAE;AAAC,WAAO,KAAK,MAAM,CAAC,GAAEA,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEC,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,sBAAqB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,EAAC,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAACE,GAAE,OAAO,CAAC,GAAEF,GAAE,WAAU,cAAa,IAAI,GAAEA,KAAEF,KAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,GAAEE,EAAC,GAAEA,GAAE,UAAU,OAAO,kBAAgB;AAAG,IAAMU,KAAEV;", "names": ["e", "t", "n", "r", "i", "o", "s", "l", "f", "u", "c", "h", "a", "g", "p", "y", "M", "e", "r", "o", "t", "l", "i", "u", "n", "s", "g", "h", "f", "c", "m", "x", "a", "y", "p", "z", "j", "M", "x", "t", "w", "p", "r", "e", "s", "i", "o", "n", "a", "h", "v"]}