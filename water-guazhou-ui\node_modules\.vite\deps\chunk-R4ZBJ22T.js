import {
  c,
  v
} from "./chunk-ZACBBT3Y.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  b
} from "./chunk-HP475EI3.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/rest/geometryService/units.js
var _ = new s({ MGRS: "mgrs", USNG: "usng", UTM: "utm", GeoRef: "geo-ref", GARS: "gars", DMS: "dms", DDM: "ddm", DD: "dd" });
var N = { UNIT_METER: 9001, UNIT_GERMAN_METER: 9031, UNIT_FOOT: 9002, UNIT_SURVEY_FOOT: 9003, UNIT_CLARKE_FOOT: 9005, UNIT_FATHOM: 9014, UNIT_NAUTICAL_MILE: 9030, UNIT_SURVEY_CHAIN: 9033, UNIT_SURVEY_LINK: 9034, UNIT_SURVEY_MILE: 9035, UNIT_KILOMETER: 9036, UNIT_CLARKE_YARD: 9037, UNIT_CLARKE_CHAIN: 9038, UNIT_CLARKE_LINK: 9039, UNIT_SEARS_YARD: 9040, UNIT_SEARS_FOOT: 9041, UNIT_SEARS_CHAIN: 9042, UNIT_SEARS_LINK: 9043, UNIT_BENOIT_1895A_YARD: 9050, UNIT_BENOIT_1895A_FOOT: 9051, UNIT_BENOIT_1895A_CHAIN: 9052, UNIT_BENOIT_1895A_LINK: 9053, UNIT_BENOIT_1895B_YARD: 9060, UNIT_BENOIT_1895B_FOOT: 9061, UNIT_BENOIT_1895B_CHAIN: 9062, UNIT_BENOIT_1895B_LINK: 9063, UNIT_INDIAN_FOOT: 9080, UNIT_INDIAN_1937_FOOT: 9081, UNIT_INDIAN_1962_FOOT: 9082, UNIT_INDIAN_1975_FOOT: 9083, UNIT_INDIAN_YARD: 9084, UNIT_INDIAN_1937_YARD: 9085, UNIT_INDIAN_1962_YARD: 9086, UNIT_INDIAN_1975_YARD: 9087, UNIT_FOOT_1865: 9070, UNIT_RADIAN: 9101, UNIT_DEGREE: 9102, UNIT_ARCMINUTE: 9103, UNIT_ARCSECOND: 9104, UNIT_GRAD: 9105, UNIT_GON: 9106, UNIT_MICRORADIAN: 9109, UNIT_ARCMINUTE_CENTESIMAL: 9112, UNIT_ARCSECOND_CENTESIMAL: 9113, UNIT_MIL6400: 9114, UNIT_BRITISH_1936_FOOT: 9095, UNIT_GOLDCOAST_FOOT: 9094, UNIT_INTERNATIONAL_CHAIN: 109003, UNIT_INTERNATIONAL_LINK: 109004, UNIT_INTERNATIONAL_YARD: 109001, UNIT_STATUTE_MILE: 9093, UNIT_SURVEY_YARD: 109002, UNIT_50KILOMETER_LENGTH: 109030, UNIT_150KILOMETER_LENGTH: 109031, UNIT_DECIMETER: 109005, UNIT_CENTIMETER: 109006, UNIT_MILLIMETER: 109007, UNIT_INTERNATIONAL_INCH: 109008, UNIT_US_SURVEY_INCH: 109009, UNIT_INTERNATIONAL_ROD: 109010, UNIT_US_SURVEY_ROD: 109011, UNIT_US_NAUTICAL_MILE: 109012, UNIT_UK_NAUTICAL_MILE: 109013, UNIT_SQUARE_INCHES: "esriSquareInches", UNIT_SQUARE_FEET: "esriSquareFeet", UNIT_SQUARE_YARDS: "esriSquareYards", UNIT_ACRES: "esriAcres", UNIT_SQUARE_MILES: "esriSquareMiles", UNIT_SQUARE_MILLIMETERS: "esriSquareMillimeters", UNIT_SQUARE_CENTIMETERS: "esriSquareCentimeters", UNIT_SQUARE_DECIMETERS: "esriSquareDecimeters", UNIT_SQUARE_METERS: "esriSquareMeters", UNIT_ARES: "esriAres", UNIT_HECTARES: "esriHectares", UNIT_SQUARE_KILOMETERS: "esriSquareKilometers" };

// node_modules/@arcgis/core/rest/operations/generalize.js
var i = new s({ 109006: "centimeters", 9102: "decimal-degrees", 109005: "decimeters", 9002: "feet", 109009: "inches", 9036: "kilometers", 9001: "meters", 9035: "miles", 109007: "millimeters", 109012: "nautical-miles", 9096: "yards" });
function r2(e2) {
  const { geometries: r4, deviationUnit: s3, maxDeviation: o4 } = e2.toJSON(), n5 = { maxDeviation: o4 };
  return r4 && r4.length && (n5.geometries = JSON.stringify({ geometryType: c(r4[0]), geometries: r4 }), n5.sr = JSON.stringify(r4[0].spatialReference)), i.write(s3, n5, "deviationUnit"), n5;
}

// node_modules/@arcgis/core/rest/support/GeneralizeParameters.js
var m2 = class extends l {
  constructor(r4) {
    super(r4), this.deviationUnit = null, this.geometries = null, this.maxDeviation = null;
  }
};
e([y({ type: String, json: { write: true } })], m2.prototype, "deviationUnit", void 0), e([y({ json: { read: { reader: (r4) => r4 ? r4.map((r5) => v(r5)).filter(r) : null }, write: { writer: (r4, o4) => {
  o4.geometries = (r4 == null ? void 0 : r4.map((r5) => r5.toJSON())) ?? null;
} } } })], m2.prototype, "geometries", void 0), e([y({ type: Number, json: { write: true } })], m2.prototype, "maxDeviation", void 0), m2 = e([a("esri.rest.support.GeneralizeParameters")], m2), m2.from = b(m2);
var a2 = m2;

// node_modules/@arcgis/core/rest/operations/lengths.js
var n = new s({ preserveShape: "preserve-shape" });
function o(e2) {
  const { polylines: o4, lengthUnit: t3, geodesic: i2, calculationType: s3 } = e2.toJSON(), r4 = {};
  r4.polylines = JSON.stringify(o4);
  const l3 = e2.polylines[0].spatialReference;
  return r4.sr = l3.wkid ? l3.wkid : JSON.stringify(l3.toJSON()), t3 && (r4.lengthUnit = t3), i2 && (r4.geodesic = i2), s3 && (r4.calculationType = n.toJSON(s3)), r4;
}

// node_modules/@arcgis/core/rest/support/LengthsParameters.js
var l2 = class extends l {
  constructor(o4) {
    super(o4), this.calculationType = null, this.geodesic = null, this.lengthUnit = null, this.polylines = null;
  }
};
e([y({ type: String, json: { write: true } })], l2.prototype, "calculationType", void 0), e([y({ type: Boolean, json: { write: true } })], l2.prototype, "geodesic", void 0), e([y({ json: { write: true } })], l2.prototype, "lengthUnit", void 0), e([y({ type: [m], json: { read: { reader: (o4) => o4 ? o4.map((o5) => v(o5)) : null }, write: { writer: (o4, r4) => {
  r4.polylines = o4.map((o5) => o5.toJSON());
} } } })], l2.prototype, "polylines", void 0), l2 = e([a("esri.rest.support.LengthsParameters")], l2), l2.from = b(l2);
var n2 = l2;

// node_modules/@arcgis/core/rest/operations/offset.js
var o2 = new s({ esriGeometryOffsetBevelled: "bevelled", esriGeometryOffsetMitered: "mitered", esriGeometryOffsetRounded: "rounded" });
var s2 = new s({ 9001: "meters", 9002: "feet", 9036: "kilometers", 9093: "miles", 109012: "nautical-miles", 109001: "yards" });
function r3(e2) {
  const { geometries: r4, bevelRatio: i2, offsetDistance: f2, offsetHow: n5, offsetUnit: m6 } = e2.toJSON(), l3 = { bevelRatio: i2, offsetDistance: f2 };
  return r4 && r4.length && (l3.geometries = JSON.stringify({ geometryType: c(r4[0]), geometries: r4 }), l3.sr = JSON.stringify(r4[0].spatialReference)), n5 && (l3.offsetHow = o2.toJSON(n5)), m6 && (l3.offsetUnit = s2.toJSON(m6)), l3;
}

// node_modules/@arcgis/core/rest/support/OffsetParameters.js
var m3 = class extends l {
  constructor(o4) {
    super(o4), this.bevelRatio = null, this.geometries = null, this.offsetDistance = null, this.offsetHow = null, this.offsetUnit = null;
  }
};
e([y({ type: Number, json: { write: true } })], m3.prototype, "bevelRatio", void 0), e([y({ json: { read: { reader: (o4) => o4 ? o4.map((o5) => v(o5)).filter(r) : null }, write: { writer: (o4, t3) => {
  t3.geometries = (o4 == null ? void 0 : o4.map((o5) => o5.toJSON())) ?? null;
} } } })], m3.prototype, "geometries", void 0), e([y({ type: Number, json: { write: true } })], m3.prototype, "offsetDistance", void 0), e([y({ type: String, json: { write: true } })], m3.prototype, "offsetHow", void 0), e([y({ type: String, json: { write: true } })], m3.prototype, "offsetUnit", void 0), m3 = e([a("esri.rest.support.OffsetParameters")], m3), m3.from = b(m3);
var f = m3;

// node_modules/@arcgis/core/rest/operations/relation.js
var t = new s({ esriGeometryRelationCross: "cross", esriGeometryRelationDisjoint: "disjoint", esriGeometryRelationIn: "in", esriGeometryRelationInteriorIntersection: "interior-intersection", esriGeometryRelationIntersection: "intersection", esriGeometryRelationLineCoincidence: "line-coincidence", esriGeometryRelationLineTouch: "line-touch", esriGeometryRelationOverlap: "overlap", esriGeometryRelationPointTouch: "point-touch", esriGeometryRelationTouch: "touch", esriGeometryRelationWithin: "within", esriGeometryRelationRelation: "relation" });
function o3(e2) {
  const { geometries1: o4, geometries2: r4, relation: n5, relationParameter: s3 } = e2.toJSON(), a4 = {};
  if (o4 && o4.length) {
    a4.geometries1 = JSON.stringify({ geometryType: c(o4[0]), geometries: o4 });
    const e3 = o4[0].spatialReference;
    a4.sr = e3.wkid ? e3.wkid : JSON.stringify(e3);
  }
  return r4 && r4.length > 0 && (a4.geometries2 = JSON.stringify({ geometryType: c(r4[0]), geometries: r4 })), n5 && (a4.relation = t.toJSON(n5)), s3 && (a4.relationParam = s3), a4;
}

// node_modules/@arcgis/core/rest/support/RelationParameters.js
var a3 = class extends l {
  constructor(r4) {
    super(r4), this.geometries1 = null, this.geometries2 = null, this.relation = null, this.relationParameter = null;
  }
};
e([y({ json: { read: { reader: (r4) => r4 ? r4.map((r5) => v(r5)).filter(r) : null }, write: { writer: (r4, e2) => {
  e2.geometries1 = (r4 == null ? void 0 : r4.map((r5) => r5.toJSON())) ?? null;
} } } })], a3.prototype, "geometries1", void 0), e([y({ json: { read: { reader: (r4) => r4 ? r4.map((r5) => v(r5)).filter(r) : null }, write: { writer: (r4, e2) => {
  e2.geometries2 = (r4 == null ? void 0 : r4.map((r5) => r5.toJSON())) ?? null;
} } } })], a3.prototype, "geometries2", void 0), e([y({ type: String, json: { write: true } })], a3.prototype, "relation", void 0), e([y({ type: String, json: { write: true } })], a3.prototype, "relationParameter", void 0), a3 = e([a("esri.rest.support.RelationParameters")], a3), a3.from = b(a3);
var m4 = a3;

// node_modules/@arcgis/core/rest/operations/trimExtend.js
var t2 = new s({ 0: "default-curve-extension", 1: "relocate-ends", 2: "keep-end-attributes", 4: "no-end-attributes", 8: "no-extend-at-from", 16: "no-extend-at-to" });
function n3(e2) {
  const { extendHow: n5, polylines: o4, trimExtendTo: r4 } = e2.toJSON(), i2 = {};
  return i2.extendHow = t2.toJSON(n5), o4 && o4.length && (i2.polylines = JSON.stringify(o4), i2.sr = JSON.stringify(o4[0].spatialReference)), r4 && (i2.trimExtendTo = JSON.stringify(r4)), i2;
}

// node_modules/@arcgis/core/rest/support/TrimExtendParameters.js
var m5 = class extends l {
  constructor(r4) {
    super(r4), this.extendHow = "default-curve-extension", this.polylines = null, this.trimExtendTo = null;
  }
};
e([y({ type: String, json: { write: true } })], m5.prototype, "extendHow", void 0), e([y({ type: [m], json: { read: { reader: (r4) => r4 ? r4.map((r5) => v(r5)) : null }, write: { writer: (r4, o4) => {
  o4.polylines = r4.map((r5) => r5.toJSON());
} } } })], m5.prototype, "polylines", void 0), e([y({ json: { read: { reader: (r4) => r4 ? v(r4) : null }, write: { writer: (r4, o4) => {
  o4.trimExtendTo = r4.toJSON();
} } } })], m5.prototype, "trimExtendTo", void 0), m5 = e([a("esri.rest.support.TrimExtendParameters")], m5), m5.from = b(m5);
var n4 = m5;

export {
  _,
  N,
  r2 as r,
  a2 as a,
  o,
  n2 as n,
  r3 as r2,
  f,
  o3 as o2,
  m4 as m,
  n3 as n2,
  n4 as n3
};
//# sourceMappingURL=chunk-R4ZBJ22T.js.map
