{"version": 3, "sources": ["../../@arcgis/core/layers/support/FieldsIndex.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNumericField as e,getFieldDefaultValue as i,normalizeFieldName as t}from\"./fieldUtils.js\";function s(e){return\"date\"===e.type||\"esriFieldTypeDate\"===e.type}function l(e){return\"oid\"===e.type||\"esriFieldTypeOID\"===e.type}function d(e){return\"global-id\"===e.type||\"esriFieldTypeGlobalID\"===e.type}class r{constructor(i=[]){if(this.fields=[],this._fieldsMap=new Map,this._normalizedFieldsMap=new Map,this._dateFieldsSet=new Set,this._numericFieldsSet=new Set,this.dateFields=[],this.numericFields=[],this._requiredFields=null,!i)return;this.fields=i;const t=[];for(const r of i){const i=r?.name,u=a(r?.name);if(i&&u){const a=n(i);this._fieldsMap.set(i,r),this._fieldsMap.set(a,r),this._normalizedFieldsMap.set(u,r),t.push(a),s(r)?(this.dateFields.push(r),this._dateFieldsSet.add(r)):e(r)&&(this._numericFieldsSet.add(r),this.numericFields.push(r)),l(r)||d(r)||(r.editable=null==r.editable||!!r.editable,r.nullable=null==r.nullable||!!r.nullable)}}t.sort(),this.uid=t.join(\",\")}destroy(){this._fieldsMap.clear()}get requiredFields(){if(!this._requiredFields){this._requiredFields=[];for(const e of this.fields)l(e)||d(e)||e.nullable||void 0!==i(e)||this._requiredFields.push(e)}return this._requiredFields}has(e){return null!=this.get(e)}get(e){if(!e)return;let i=this._fieldsMap.get(e);return i||(i=this._fieldsMap.get(n(e))??this._normalizedFieldsMap.get(a(e)),i&&this._fieldsMap.set(e,i),i)}isDateField(e){return this._dateFieldsSet.has(this.get(e))}isNumericField(e){return this._numericFieldsSet.has(this.get(e))}normalizeFieldName(e){const i=this.get(e);if(i)return i.name??void 0}}function n(e){return e.trim().toLowerCase()}function a(e){return t(e)?.toLowerCase()??\"\"}export{r as default};\n"], "mappings": ";;;;;;;AAImG,SAAS,EAAE,GAAE;AAAC,SAAM,WAAS,EAAE,QAAM,wBAAsB,EAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,UAAQ,EAAE,QAAM,uBAAqB,EAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,gBAAc,EAAE,QAAM,4BAA0B,EAAE;AAAI;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAY,IAAE,CAAC,GAAE;AAAC,QAAG,KAAK,SAAO,CAAC,GAAE,KAAK,aAAW,oBAAI,OAAI,KAAK,uBAAqB,oBAAI,OAAI,KAAK,iBAAe,oBAAI,OAAI,KAAK,oBAAkB,oBAAI,OAAI,KAAK,aAAW,CAAC,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,kBAAgB,MAAK,CAAC,EAAE;AAAO,SAAK,SAAO;AAAE,UAAM,IAAE,CAAC;AAAE,eAAUA,MAAK,GAAE;AAAC,YAAMC,KAAED,MAAA,gBAAAA,GAAG,MAAK,IAAE,EAAEA,MAAA,gBAAAA,GAAG,IAAI;AAAE,UAAGC,MAAG,GAAE;AAAC,cAAMC,KAAE,EAAED,EAAC;AAAE,aAAK,WAAW,IAAIA,IAAED,EAAC,GAAE,KAAK,WAAW,IAAIE,IAAEF,EAAC,GAAE,KAAK,qBAAqB,IAAI,GAAEA,EAAC,GAAE,EAAE,KAAKE,EAAC,GAAE,EAAEF,EAAC,KAAG,KAAK,WAAW,KAAKA,EAAC,GAAE,KAAK,eAAe,IAAIA,EAAC,KAAG,GAAEA,EAAC,MAAI,KAAK,kBAAkB,IAAIA,EAAC,GAAE,KAAK,cAAc,KAAKA,EAAC,IAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,MAAIA,GAAE,WAAS,QAAMA,GAAE,YAAU,CAAC,CAACA,GAAE,UAASA,GAAE,WAAS,QAAMA,GAAE,YAAU,CAAC,CAACA,GAAE;AAAA,MAAS;AAAA,IAAC;AAAC,MAAE,KAAK,GAAE,KAAK,MAAI,EAAE,KAAK,GAAG;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,WAAW,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,QAAG,CAAC,KAAK,iBAAgB;AAAC,WAAK,kBAAgB,CAAC;AAAE,iBAAU,KAAK,KAAK,OAAO,GAAE,CAAC,KAAG,EAAE,CAAC,KAAG,EAAE,YAAU,WAAS,EAAE,CAAC,KAAG,KAAK,gBAAgB,KAAK,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,GAAE;AAAC,WAAO,QAAM,KAAK,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,GAAE;AAAC,QAAG,CAAC,EAAE;AAAO,QAAI,IAAE,KAAK,WAAW,IAAI,CAAC;AAAE,WAAO,MAAI,IAAE,KAAK,WAAW,IAAI,EAAE,CAAC,CAAC,KAAG,KAAK,qBAAqB,IAAI,EAAE,CAAC,CAAC,GAAE,KAAG,KAAK,WAAW,IAAI,GAAE,CAAC,GAAE;AAAA,EAAE;AAAA,EAAC,YAAY,GAAE;AAAC,WAAO,KAAK,eAAe,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,eAAe,GAAE;AAAC,WAAO,KAAK,kBAAkB,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmB,GAAE;AAAC,UAAM,IAAE,KAAK,IAAI,CAAC;AAAE,QAAG,EAAE,QAAO,EAAE,QAAM;AAAA,EAAM;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,KAAK,EAAE,YAAY;AAAC;AAAC,SAAS,EAAE,GAAE;AAJlnD;AAImnD,WAAO,OAAE,CAAC,MAAH,mBAAM,kBAAe;AAAE;", "names": ["r", "i", "a"]}