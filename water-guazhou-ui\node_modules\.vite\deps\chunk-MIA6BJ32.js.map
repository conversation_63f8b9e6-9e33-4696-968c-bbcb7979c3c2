{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/BlendLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as o}from\"../../core/accessorSupport/decorators/subclass.js\";import{read as t,write as s}from\"../effects/jsonUtils.js\";const i={read:{reader:t},write:{allowNull:!0,writer:s}},n=t=>{let s=class extends t{constructor(){super(...arguments),this.blendMode=\"normal\",this.effect=null}};return e([r({type:[\"average\",\"color-burn\",\"color-dodge\",\"color\",\"darken\",\"destination-atop\",\"destination-in\",\"destination-out\",\"destination-over\",\"difference\",\"exclusion\",\"hard-light\",\"hue\",\"invert\",\"lighten\",\"lighter\",\"luminosity\",\"minus\",\"multiply\",\"normal\",\"overlay\",\"plus\",\"reflect\",\"saturation\",\"screen\",\"soft-light\",\"source-atop\",\"source-in\",\"source-out\",\"vivid-light\",\"xor\"],nonNullable:!0,json:{read:!1,write:!1,origins:{\"web-map\":{read:!0,write:!0},\"portal-item\":{read:!0,write:!0}}}})],s.prototype,\"blendMode\",void 0),e([r({json:{read:!1,write:!1,origins:{\"web-map\":i,\"portal-item\":i}}})],s.prototype,\"effect\",void 0),s=e([o(\"esri.layers.mixins.BlendLayer\")],s),s};export{n as BlendLayer};\n"], "mappings": ";;;;;;;;;;;;;AAIoV,IAAM,IAAE,EAAC,MAAK,EAAC,QAAO,EAAC,GAAE,OAAM,EAAC,WAAU,MAAG,QAAOA,GAAC,EAAC;AAAtD,IAAwDC,KAAE,OAAG;AAAC,MAAI,IAAE,cAAc,EAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,YAAU,UAAS,KAAK,SAAO;AAAA,IAAI;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,WAAU,cAAa,eAAc,SAAQ,UAAS,oBAAmB,kBAAiB,mBAAkB,oBAAmB,cAAa,aAAY,cAAa,OAAM,UAAS,WAAU,WAAU,cAAa,SAAQ,YAAW,UAAS,WAAU,QAAO,WAAU,cAAa,UAAS,cAAa,eAAc,aAAY,cAAa,eAAc,KAAK,GAAE,aAAY,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,WAAU,EAAC,MAAK,MAAG,OAAM,KAAE,GAAE,eAAc,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,WAAU,GAAE,eAAc,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,+BAA+B,CAAC,GAAE,CAAC,GAAE;AAAC;", "names": ["a", "n"]}