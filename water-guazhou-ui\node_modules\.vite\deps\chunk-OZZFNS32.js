import {
  l
} from "./chunk-5JCRZXRL.js";
import {
  e
} from "./chunk-4CHRJPQP.js";
import {
  b
} from "./chunk-SROTSYJS.js";
import {
  R,
  u
} from "./chunk-I7WHRVHF.js";

// node_modules/@arcgis/core/views/2d/tiling/TileCache.js
var t = class {
  constructor(e2, t2, i2) {
    this.maxSize = e2, this._tileInfoView = t2, this._removedFunc = i2, this._tilePerId = /* @__PURE__ */ new Map(), this._tileKeysPerLevel = [];
  }
  has(e2) {
    return this._tilePerId.has(e2);
  }
  get(e2) {
    return this._tilePerId.get(e2);
  }
  pop(e2) {
    const t2 = this._tilePerId.get(e2);
    if (!t2) return;
    const s = t2.key.level, r2 = this._tileKeysPerLevel[s];
    i(this._tilePerId, e2);
    for (let i2 = 0; i2 < r2.length; i2++) if (r2[i2].id === e2) {
      r2.splice(i2, 1);
      break;
    }
    return t2.visible = true, t2;
  }
  add(e2) {
    e2.visible = false;
    const t2 = e2.key, i2 = t2.id;
    if (this._tilePerId.has(i2)) return;
    this._tilePerId.set(i2, e2);
    const s = t2.level;
    this._tileKeysPerLevel[s] || (this._tileKeysPerLevel[s] = []), this._tileKeysPerLevel[s].push(t2);
  }
  prune(e2, t2, i2) {
    let s = this._tilePerId.size;
    if (s <= this.maxSize) return;
    let r2 = this._tileKeysPerLevel.length - 1;
    for (; s > this.maxSize && r2 >= 0; ) r2 !== e2 && (s = this._pruneAroundCenterTile(s, t2, i2, r2)), r2--;
    s > this.maxSize && (s = this._pruneAroundCenterTile(s, t2, i2, e2));
  }
  _pruneAroundCenterTile(t2, i2, s, r2) {
    const l2 = this._tileKeysPerLevel[r2];
    if (!l2 || 0 === l2.length) return t2;
    const { size: n, origin: h2 } = this._tileInfoView.tileInfo, o2 = s * n[0], _ = s * n[1], d = [0, 0], u2 = [0, 0];
    for (l2.sort((t3, s2) => (d[0] = h2.x + o2 * (t3.col + 0.5), d[1] = h2.y - _ * (t3.row + 0.5), u2[0] = h2.x + o2 * (s2.col + 0.5), u2[1] = h2.y - _ * (s2.row + 0.5), b(d, i2) - b(u2, i2))); l2.length > 0; ) {
      const e2 = l2.pop();
      if (this._removeTile(e2.id), --t2 === this.maxSize) break;
    }
    return t2;
  }
  _removeTile(e2) {
    const t2 = this._tilePerId.get(e2);
    this._removedFunc && t2 && this._removedFunc(t2), i(this._tilePerId, e2);
  }
};
function i(e2, t2) {
  e2.delete(t2);
}

// node_modules/@arcgis/core/views/2d/tiling/TileStrategy.js
var a = new e(0, 0, 0, 0);
var h = /* @__PURE__ */ new Map();
var o = [];
var c = [];
var r = class {
  constructor(e2) {
    this._previousScale = Number.POSITIVE_INFINITY, this.cachePolicy = "keep", this.coveragePolicy = "closest", this.resampling = true, this.tileIndex = /* @__PURE__ */ new Map(), this.tiles = [], this.buffer = 192, this.acquireTile = e2.acquireTile, this.releaseTile = e2.releaseTile, this.tileInfoView = e2.tileInfoView, this.resampling = null == e2.resampling || !!e2.resampling, e2.cachePolicy && (this.cachePolicy = e2.cachePolicy), e2.coveragePolicy && (this.coveragePolicy = e2.coveragePolicy), null != e2.buffer && (this.buffer = e2.buffer), e2.cacheSize && (this._tileCache = new t(e2.cacheSize, this.tileInfoView, (e3) => {
      this.releaseTile(e3);
    }));
  }
  destroy() {
    this.tileIndex.clear();
  }
  update(e2) {
    const { resampling: i2, tileIndex: t2 } = this, l2 = this.tileInfoView.getTileCoverage(e2.state, this.buffer, this.coveragePolicy);
    if (c.length = 0, o.length = 0, h.clear(), !l2) return;
    const { minScale: r2, maxScale: n } = this.tileInfoView.tileInfo, { spans: f, lodInfo: d } = l2, { level: u2 } = d, { scale: p, center: g, resolution: y } = e2.state, I = !e2.stationary && p > this._previousScale;
    if (this._previousScale = p, this.tiles.length = 0, !i2 && (p > r2 || p < n)) return this.tiles.length = 0, h.clear(), t2.forEach((e3) => {
      this.releaseTile(e3);
    }), t2.clear(), c.length = 0, o.length = 0, h.clear(), l.pool.release(l2), true;
    t2.forEach((e3) => e3.visible = true);
    let T = 0, _ = 0;
    if (f.length > 0) for (const { row: s, colFrom: o2, colTo: c2 } of f) for (let e3 = o2; e3 <= c2; e3++) {
      T++;
      const i3 = a.set(u2, s, d.normalizeCol(e3), d.getWorldForColumn(e3)).id;
      if (t2.has(i3)) {
        const e4 = t2.get(i3);
        e4.isReady ? (h.set(i3, e4), _++) : I || this._addParentTile(i3, h);
      } else {
        let e4;
        if (this._tileCache && this._tileCache.has(i3)) {
          if (e4 = this._tileCache.pop(i3), this.tileIndex.set(i3, e4), e4.isReady) {
            h.set(i3, e4), _++;
            continue;
          }
        } else e4 = this.acquireTile(a), this.tileIndex.set(i3, e4);
        I || this._addParentTile(i3, h);
      }
    }
    const m = _ === T;
    t2.forEach((e3, i3) => {
      if (a.set(i3), h.has(i3)) return;
      const t3 = this.tileInfoView.intersects(l2, a), s = "purge" === this.cachePolicy ? a.level !== u2 : a.level > u2;
      !t3 || !I && m ? !s && t3 || o.push(i3) : e3.isReady ? s && "purge" === this.cachePolicy && this._hasReadyAncestor(a, u2) ? o.push(i3) : c.push(i3) : s && o.push(i3);
    });
    for (const s of c) {
      const e3 = t2.get(s);
      e3 && e3.isReady && h.set(s, e3);
    }
    for (const s of o) {
      const e3 = t2.get(s);
      this._tileCache ? this._tileCache.add(e3) : this.releaseTile(e3), t2.delete(s);
    }
    return h.forEach((e3) => this.tiles.push(e3)), t2.forEach((e3) => {
      h.has(e3.key.id) || (e3.visible = false);
    }), this._tileCache && this._tileCache.prune(u2, g, y), l.pool.release(l2), h.clear(), m;
  }
  clear(e2 = true) {
    const { tileIndex: i2 } = this;
    e2 && i2.forEach((e3) => {
      this.releaseTile(e3);
    }), i2.clear();
  }
  updateCacheSize(e2) {
    this._tileCache && (this._tileCache.maxSize = e2);
  }
  _addParentTile(e2, i2) {
    let t2 = e2, s = null;
    for (; t2 = this.tileInfoView.getTileParentId(t2), t2; ) if (this.tileIndex.has(t2)) {
      if (s = this.tileIndex.get(t2), s && s.isReady) {
        i2.has(s.key.id) || i2.set(s.key.id, s);
        break;
      }
    } else if (this._tileCache && this._tileCache.has(t2) && (s = this._tileCache.pop(t2), this.tileIndex.set(t2, s), s && s.isReady)) {
      i2.has(s.key.id) || i2.set(s.key.id, s);
      break;
    }
  }
  _hasReadyAncestor(t2, s) {
    const l2 = u();
    this.tileInfoView.getTileBounds(l2, t2, true);
    for (const a2 of this.tileIndex.values()) if (a2.isReady && a2.key.level >= s && a2.key.level < t2.level) {
      const t3 = u();
      if (this.tileInfoView.getTileBounds(t3, a2.key, true), R(t3, l2)) return true;
    }
    return false;
  }
};

export {
  r
};
//# sourceMappingURL=chunk-OZZFNS32.js.map
