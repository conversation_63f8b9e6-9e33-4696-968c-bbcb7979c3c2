import {
  H as H6,
  V as V4
} from "./chunk-OHIQTOTV.js";
import {
  f as f9
} from "./chunk-LGTBT7TM.js";
import {
  v as v8
} from "./chunk-ILZ6UF5N.js";
import {
  p as p5,
  s as s13
} from "./chunk-7GO2KORS.js";
import {
  A as A5,
  j as j4,
  r as r16,
  s as s12
} from "./chunk-J2CRYUH3.js";
import {
  s as s11
} from "./chunk-A4XIY547.js";
import {
  u as u10
} from "./chunk-5HSEQ7GU.js";
import {
  _ as _8,
  c as c6,
  i as i11
} from "./chunk-V57FPENY.js";
import {
  H as H4,
  U,
  V as V3,
  s as s10,
  u as u9
} from "./chunk-PTYT4A5P.js";
import {
  E as E8,
  N as N4,
  O as O4,
  P as P4,
  S as S3,
  a as a13,
  b as b5,
  e as e10,
  f as f7,
  g as g3,
  i as i8,
  l as l17,
  m as m2,
  n as n13,
  o as o8,
  o2 as o13,
  p as p4,
  r as r11,
  r2 as r14,
  s2 as s8,
  u as u8,
  v as v5,
  y as y4
} from "./chunk-NEOCII5B.js";
import {
  s as s9
} from "./chunk-FQZM46ZM.js";
import {
  d as d2,
  i as i6
} from "./chunk-KETK5W7H.js";
import {
  A as A4,
  E as E7,
  S as S2,
  _ as _7,
  a as a12,
  c as c5,
  l as l16
} from "./chunk-LGZKVOWE.js";
import {
  t as t7
} from "./chunk-SKYLCTPX.js";
import {
  M as M3,
  b as b4,
  k as k3,
  v as v4
} from "./chunk-FQMXSCOG.js";
import {
  v as v6
} from "./chunk-MNPAHSMX.js";
import {
  L as L2,
  P as P5,
  e as e9,
  i as i9,
  o as o12,
  t as t9
} from "./chunk-QLHJUIIZ.js";
import {
  r as r15,
  t as t10
} from "./chunk-KVEZ26WH.js";
import {
  E as E5,
  N as N3,
  c as c4,
  d as d4,
  e as e8,
  f as f5,
  f2 as f6,
  h as h8,
  i as i7,
  o as o10,
  p as p3,
  r as r10,
  r2 as r13,
  u as u7,
  v as v7,
  x as x7
} from "./chunk-TOYJMVHA.js";
import {
  d2 as d3,
  h as h7,
  o2 as o9,
  t as t8
} from "./chunk-XVTFHFM3.js";
import {
  o as o14
} from "./chunk-Y424ZXTG.js";
import {
  h as h6
} from "./chunk-L4Y6W6Y5.js";
import {
  W,
  _ as _6,
  a as a11,
  h as h9,
  l as l15,
  s as s7
} from "./chunk-WKBMFG6J.js";
import {
  o as o11
} from "./chunk-BPRRRPC3.js";
import {
  E2 as E6,
  n as n14,
  u as u6,
  x as x6
} from "./chunk-6G2NLXT7.js";
import {
  s as s5
} from "./chunk-IUNR7SKI.js";
import {
  n as n11,
  r as r8,
  t as t6
} from "./chunk-UHA44FM7.js";
import {
  N as N2,
  i as i5,
  n as n10,
  t as t5
} from "./chunk-6ZZUUGXX.js";
import {
  a as a9,
  f as f2,
  l as l11,
  n as n9
} from "./chunk-OMPEYGMV.js";
import {
  o as o5
} from "./chunk-BWWOCIFU.js";
import {
  T as T2
} from "./chunk-QW426QEA.js";
import {
  O as O3
} from "./chunk-CPQSD22U.js";
import {
  o as o6
} from "./chunk-SKIEIN3S.js";
import {
  i as i4,
  u as u5,
  x as x4
} from "./chunk-3KCCETWY.js";
import {
  H as H5,
  T as T3
} from "./chunk-UYVDPJKH.js";
import {
  a as a10,
  c as c3,
  f as f4,
  h as h5,
  l as l14,
  s as s6
} from "./chunk-EM4JSU7Z.js";
import {
  E as E4
} from "./chunk-FTRLEBHJ.js";
import {
  A as A3,
  C as C2,
  D as D2,
  E as E3,
  F as F2,
  G,
  I,
  L,
  M,
  P as P3,
  R as R2,
  V,
  Y,
  _ as _4
} from "./chunk-4M3AMTD4.js";
import {
  n as n8
} from "./chunk-DUEDINK5.js";
import {
  r as r12
} from "./chunk-QCTKOQ44.js";
import {
  a as a8
} from "./chunk-ST2RRB55.js";
import {
  t as t11
} from "./chunk-WFNEQMBV.js";
import {
  M as M4,
  f as f8,
  m as m3
} from "./chunk-TL5Y53I4.js";
import {
  V as V2,
  Y as Y2,
  _ as _5,
  j as j3,
  l as l12,
  p2,
  x as x5
} from "./chunk-6IU6DQRF.js";
import {
  C as C3,
  R as R3,
  d,
  f2 as f3,
  h as h4,
  k as k2,
  p
} from "./chunk-YELYN22P.js";
import {
  e as e7,
  o as o7
} from "./chunk-QYOAH6AO.js";
import {
  e as e6
} from "./chunk-A7PY25IH.js";
import {
  l as l13
} from "./chunk-JJ3NE6DY.js";
import {
  P as P2,
  R
} from "./chunk-4VO6N7OL.js";
import {
  M as M2
} from "./chunk-VHLK35TF.js";
import {
  A as A2,
  E as E2,
  H as H3,
  a as a7,
  b as b3,
  g as g2,
  j as j2,
  l as l10,
  o as o4,
  r as r6,
  s as s4,
  v as v3
} from "./chunk-SROTSYJS.js";
import {
  n as n12,
  r as r9
} from "./chunk-FOE4ICAJ.js";
import {
  Zn,
  ee,
  pn,
  xn
} from "./chunk-UYAKJRPP.js";
import {
  C,
  H as H2,
  Q,
  _ as _3,
  c as c2,
  h as h3,
  i as i2,
  k,
  l as l9,
  m,
  n as n6,
  o as o3,
  q,
  x as x3
} from "./chunk-YEODPCXQ.js";
import {
  _ as _2,
  l as l8,
  n as n5,
  r as r5,
  t as t4
} from "./chunk-NOZFLZZL.js";
import {
  i as i3,
  s as s3
} from "./chunk-MUYX6GXF.js";
import {
  n as n7,
  r as r7
} from "./chunk-3WUI7ZKG.js";
import {
  a as a6,
  l as l7,
  w as w2
} from "./chunk-QUHG7NMD.js";
import {
  N,
  T,
  l as l6
} from "./chunk-AVKOL7OR.js";
import {
  i as i10
} from "./chunk-57XIOVP5.js";
import {
  a as a5,
  u as u4,
  z as z3
} from "./chunk-I7WHRVHF.js";
import {
  n as n3
} from "./chunk-SGIJIEHB.js";
import {
  c,
  o2 as o,
  u as u2
} from "./chunk-X7FOCGBC.js";
import {
  $,
  O
} from "./chunk-JXLVNWKF.js";
import {
  i,
  x as x2
} from "./chunk-G5KX4JSG.js";
import {
  a as a4,
  h as h2,
  l as l5
} from "./chunk-EIGTETCG.js";
import {
  A,
  F,
  H,
  J,
  O as O2,
  P,
  S,
  X,
  Z,
  _,
  e as e5,
  g,
  j,
  o as o2,
  r as r3,
  s as s2,
  u as u3,
  x,
  z as z2
} from "./chunk-MQAXMQFG.js";
import {
  b,
  e as e4,
  l as l3,
  n as n4,
  r as r2,
  t as t3
} from "./chunk-36FLFRUE.js";
import {
  D,
  E,
  a as a3,
  b as b2,
  l as l4,
  r as r4,
  w
} from "./chunk-RQXGVG3K.js";
import {
  e,
  t2,
  v as v2
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y as y3
} from "./chunk-JN4FSB7Y.js";
import {
  n
} from "./chunk-HP475EI3.js";
import {
  e2 as e3,
  l as l2,
  n2,
  u
} from "./chunk-C5VMWMBD.js";
import {
  f
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  v,
  y as y2,
  z
} from "./chunk-GZGAQUSK.js";
import {
  a,
  e as e2,
  h,
  l,
  r,
  t,
  y
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/UpdatePolicy.js
var C4;
!function(C8) {
  C8[C8.ASYNC = 0] = "ASYNC", C8[C8.SYNC = 1] = "SYNC";
}(C4 || (C4 = {}));

// node_modules/@arcgis/core/views/3d/layers/interfaces.js
var e11;
var a14;
var t12;
!function(e16) {
  e16[e16.RasterImage = 0] = "RasterImage", e16[e16.Features = 1] = "Features";
}(e11 || (e11 = {})), function(e16) {
  e16[e16.MapLayer = 0] = "MapLayer", e16[e16.ViewLayer = 1] = "ViewLayer", e16[e16.Outline = 2] = "Outline", e16[e16.SnappingHint = 3] = "SnappingHint";
}(a14 || (a14 = {})), function(e16) {
  e16[e16.WithRasterImage = 0] = "WithRasterImage", e16[e16.WithoutRasterImage = 1] = "WithoutRasterImage";
}(t12 || (t12 = {}));

// node_modules/@arcgis/core/views/3d/layers/graphics/graphicUtils.js
function A6(t27, e16) {
  if ("point" === t27.type) return P6(t27, e16, false);
  if (m3(t27)) switch (t27.type) {
    case "extent":
      return P6(t27.center, e16, false);
    case "polygon":
      return P6(t27.centroid, e16, false);
    case "polyline":
      return P6(M5(t27), e16, true);
    case "mesh":
      return P6(t27.origin, e16, false);
  }
  else switch (t27.type) {
    case "extent":
      return P6(R4(t27), e16, true);
    case "polygon":
      return P6(F3(t27), e16, true);
    case "polyline":
      return P6(M5(t27), e16, true);
  }
}
function M5(t27) {
  const e16 = t27.paths[0];
  if (!e16 || 0 === e16.length) return null;
  const r28 = c(e16, u2(e16) / 2);
  return M2(r28[0], r28[1], r28[2], t27.spatialReference);
}
function R4(t27) {
  return M2(0.5 * (t27.xmax + t27.xmin), 0.5 * (t27.ymax + t27.ymin), null != t27.zmin && null != t27.zmax && isFinite(t27.zmin) && isFinite(t27.zmax) ? 0.5 * (t27.zmax + t27.zmin) : void 0, t27.spatialReference);
}
function F3(t27) {
  const e16 = t27.rings[0];
  if (!e16 || 0 === e16.length) return null;
  const r28 = o(t27.rings, !!t27.hasZ);
  return M2(r28[0], r28[1], r28[2], t27.spatialReference);
}
function P6(t27, e16, r28) {
  const n28 = r28 ? t27 : M4(t27);
  return e16 && t27 ? pn(t27, n28, e16) ? n28 : null : n28;
}
function k4(t27, e16, r28, n28 = 0) {
  if (t27) {
    e16 || (e16 = u4());
    const o22 = t27;
    let i21 = 0.5 * o22.width * (r28 - 1), s20 = 0.5 * o22.height * (r28 - 1);
    return o22.width < 1e-7 * o22.height ? i21 += s20 / 20 : o22.height < 1e-7 * o22.width && (s20 += i21 / 20), r4(e16, o22.xmin - i21 - n28, o22.ymin - s20 - n28, o22.xmax + i21 + n28, o22.ymax + s20 + n28), e16;
  }
  return null;
}
function U2(t27, e16) {
  for (let r28 = 0; r28 < t27.geometries.length; ++r28) {
    const n28 = t27.geometries[r28].getMutableAttribute(O3.AUXPOS1);
    n28 && n28.data[3] !== e16 && (n28.data[3] = e16, t27.geometryVertexAttrsUpdated(t27.geometries[r28]));
  }
}
function B(e16, r28) {
  const n28 = t4(_2);
  return r(e16) && (n28[0] = e16[0], n28[1] = e16[1], n28[2] = e16[2]), r(r28) ? n28[3] = r28 : r(e16) && e16.length > 3 && (n28[3] = e16[3]), n28;
}
function I2(t27 = l3, r28, n28, o22 = 1) {
  const i21 = new Array(3);
  if (t(r28) || t(n28)) i21[0] = 1, i21[1] = 1, i21[2] = 1;
  else {
    let e16, o23 = 0;
    for (let s20 = 2; s20 >= 0; s20--) {
      const u12 = t27[s20];
      let a25;
      const m9 = null != u12, l22 = 0 === s20 && !e16 && !m9, c15 = n28[s20];
      "symbol-value" === u12 || l22 ? a25 = 0 !== c15 ? r28[s20] / c15 : 1 : m9 && "proportional" !== u12 && isFinite(u12) && (a25 = 0 !== c15 ? u12 / c15 : 1), null != a25 && (i21[s20] = a25, e16 = a25, o23 = Math.max(o23, Math.abs(a25)));
    }
    for (let t28 = 2; t28 >= 0; t28--) null == i21[t28] ? i21[t28] = e16 : 0 === i21[t28] && (i21[t28] = 1e-3 * o23);
  }
  for (let e16 = 2; e16 >= 0; e16--) i21[e16] /= o22;
  return e4(i21);
}
function O5(t27) {
  return null != t27.isPrimitive;
}
function S4(t27) {
  return V5(O5(t27) ? [t27.width, t27.depth, t27.height] : t27) ? null : "Symbol sizes may not be negative values";
}
function V5(t27) {
  if (Array.isArray(t27)) {
    for (const e16 of t27) if (!V5(e16)) return false;
    return true;
  }
  return null == t27 || t27 >= 0;
}
function G2(t27, e16, r28, u12 = e7()) {
  const a25 = t27 || 0, m9 = e16 || 0, l22 = r28 || 0;
  return 0 !== a25 && x3(u12, u12, -a25 / 180 * Math.PI), 0 !== m9 && l9(u12, u12, m9 / 180 * Math.PI), 0 !== l22 && m(u12, u12, l22 / 180 * Math.PI), u12;
}
function X2(t27, e16, n28) {
  if (null != n28.minDemResolution) return n28.minDemResolution;
  const o22 = $(e16), i21 = l6(t27) * o22, s20 = N(t27) * o22, u12 = T(t27) * (e16.isGeographic ? 1 : o22);
  return 0 === i21 && 0 === s20 && 0 === u12 ? n28.minDemResolutionForPoints : 0.01 * Math.max(i21, s20, u12);
}

// node_modules/@arcgis/core/layers/graphics/dehydratedFeatureUtils.js
function t13(t27) {
  return "point" === t27.type;
}

// node_modules/@arcgis/core/views/3d/support/ElevationProvider.js
var t14 = class {
  constructor(e16, r28 = null, t27 = 0) {
    this.array = e16, this.spatialReference = r28, this.offset = t27;
  }
};
function a15(e16) {
  return "array" in e16;
}
function i12(t27, i21, n28 = "ground") {
  if (t13(i21)) return t27.getElevation(i21.x, i21.y, i21.z || 0, i21.spatialReference, n28);
  if (a15(i21)) {
    let r28 = i21.offset;
    return t27.getElevation(i21.array[r28++], i21.array[r28++], i21.array[r28] || 0, l(i21.spatialReference, t27.spatialReference), n28);
  }
  return t27.getElevation(i21[0], i21[1], i21[2] || 0, t27.spatialReference, n28);
}

// node_modules/@arcgis/core/views/3d/layers/graphics/elevationAlignmentUtils.js
function c7(e16, t27, n28, o22, i21, s20, a25, l22, u12, c15, f18) {
  const d11 = x8[f18.mode];
  let m9, p13, g7 = 0;
  if (xn(e16, t27, n28, o22, u12.spatialReference, i21, l22)) return d11.requiresAlignment(f18) ? (g7 = d11.applyElevationAlignmentBuffer(o22, i21, s20, a25, l22, u12, c15, f18), m9 = s20, p13 = a25) : (m9 = o22, p13 = i21), xn(m9, u12.spatialReference, p13, s20, c15.spatialReference, a25, l22) ? g7 : void 0;
}
function f10(t27, n28, o22, r28, i21) {
  const a25 = (t13(t27) ? t27.z : a15(t27) ? t27.array[t27.offset + 2] : t27[2]) || 0;
  switch (o22.mode) {
    case "on-the-ground": {
      const o23 = l(i12(n28, t27, "ground"), 0);
      return i21.verticalDistanceToGround = 0, i21.sampledElevation = o23, void (i21.z = o23);
    }
    case "relative-to-ground": {
      const s20 = l(i12(n28, t27, "ground"), 0), l22 = o22.geometryZWithOffset(a25, r28);
      return i21.verticalDistanceToGround = l22, i21.sampledElevation = s20, void (i21.z = l22 + s20);
    }
    case "relative-to-scene": {
      const s20 = l(i12(n28, t27, "scene"), 0), l22 = o22.geometryZWithOffset(a25, r28);
      return i21.verticalDistanceToGround = l22, i21.sampledElevation = s20, void (i21.z = l22 + s20);
    }
    case "absolute-height": {
      const s20 = o22.geometryZWithOffset(a25, r28), l22 = l(i12(n28, t27, "ground"), 0);
      return i21.verticalDistanceToGround = s20 - l22, i21.sampledElevation = l22, void (i21.z = s20);
    }
    default:
      return void (i21.z = 0);
  }
}
function d5(e16, t27, n28, o22) {
  return f10(e16, t27, n28, o22, z4), z4.z;
}
function m4(e16, t27, n28) {
  return null == t27 || null == n28 ? e16.definedChanged : "on-the-ground" === t27 && "on-the-ground" === n28 ? e16.staysOnTheGround : t27 === n28 || "on-the-ground" !== t27 && "on-the-ground" !== n28 ? j5.UPDATE : e16.onTheGroundChanged;
}
function p6(e16) {
  return "relative-to-ground" === e16 || "relative-to-scene" === e16;
}
function g4(e16) {
  return "absolute-height" !== e16;
}
function v9(e16, n28, o22, r28, s20) {
  f10(n28, o22, s20, r28, z4), U2(e16, z4.verticalDistanceToGround);
  const l22 = z4.sampledElevation, u12 = n6(O6, e16.transformation);
  C5[0] = n28.x, C5[1] = n28.y, C5[2] = z4.z;
  return Zn(n28.spatialReference, C5, u12, r28.spatialReference) ? e16.transformation = u12 : console.warn("Could not locate symbol object properly, it might be misplaced"), l22;
}
function h10(t27, n28, o22, r28, i21, s20) {
  let a25 = 0;
  const l22 = s20.spatialReference;
  n28 *= 3, r28 *= 3;
  for (let u12 = 0; u12 < i21; ++u12) {
    const i22 = t27[n28 + 0], u13 = t27[n28 + 1], c15 = t27[n28 + 2], f18 = l(s20.getElevation(i22, u13, c15, l22, "ground"), 0);
    a25 += f18, o22[r28 + 0] = i22, o22[r28 + 1] = u13, o22[r28 + 2] = f18, n28 += 3, r28 += 3;
  }
  return a25 / i21;
}
function E9(t27, n28, o22, r28, i21, s20, a25, l22) {
  let u12 = 0;
  const c15 = l22.calculateOffsetRenderUnits(a25), f18 = l22.featureExpressionInfoContext, d11 = s20.spatialReference;
  n28 *= 3, r28 *= 3;
  for (let m9 = 0; m9 < i21; ++m9) {
    const i22 = t27[n28 + 0], a26 = t27[n28 + 1], l23 = t27[n28 + 2], m10 = l(s20.getElevation(i22, a26, l23, d11, "ground"), 0);
    u12 += m10, o22[r28 + 0] = i22, o22[r28 + 1] = a26, o22[r28 + 2] = null == f18 ? l23 + m10 + c15 : m10 + c15, n28 += 3, r28 += 3;
  }
  return u12 / i21;
}
function y5(t27, n28, o22, r28, i21, s20, a25, l22) {
  let u12 = 0;
  const c15 = l22.calculateOffsetRenderUnits(a25), f18 = l22.featureExpressionInfoContext, d11 = s20.spatialReference;
  n28 *= 3, r28 *= 3;
  for (let m9 = 0; m9 < i21; ++m9) {
    const i22 = t27[n28 + 0], a26 = t27[n28 + 1], l23 = t27[n28 + 2], m10 = l(s20.getElevation(i22, a26, l23, d11, "scene"), 0);
    u12 += m10, o22[r28 + 0] = i22, o22[r28 + 1] = a26, o22[r28 + 2] = null == f18 ? l23 + m10 + c15 : m10 + c15, n28 += 3, r28 += 3;
  }
  return u12 / i21;
}
function A7(e16) {
  const t27 = e16.meterUnitOffset, n28 = e16.featureExpressionInfoContext;
  return 0 !== t27 || null != n28;
}
function R5(e16, t27, n28, o22, r28, i21, s20, a25) {
  const l22 = a25.calculateOffsetRenderUnits(s20), u12 = a25.featureExpressionInfoContext;
  t27 *= 3, o22 *= 3;
  for (let c15 = 0; c15 < r28; ++c15) {
    const r29 = e16[t27 + 0], i22 = e16[t27 + 1], s21 = e16[t27 + 2];
    n28[o22 + 0] = r29, n28[o22 + 1] = i22, n28[o22 + 2] = null == u12 ? s21 + l22 : l22, t27 += 3, o22 += 3;
  }
  return 0;
}
var T4 = class {
  constructor() {
    this.verticalDistanceToGround = 0, this.sampledElevation = 0, this.z = 0;
  }
};
var j5;
!function(e16) {
  e16[e16.NONE = 0] = "NONE", e16[e16.UPDATE = 1] = "UPDATE", e16[e16.RECREATE = 2] = "RECREATE";
}(j5 || (j5 = {}));
var x8 = { "absolute-height": { applyElevationAlignmentBuffer: R5, requiresAlignment: A7 }, "on-the-ground": { applyElevationAlignmentBuffer: h10, requiresAlignment: () => true }, "relative-to-ground": { applyElevationAlignmentBuffer: E9, requiresAlignment: () => true }, "relative-to-scene": { applyElevationAlignmentBuffer: y5, requiresAlignment: () => true } };
var O6 = e7();
var z4 = new T4();
var C5 = n4();

// node_modules/@arcgis/core/views/3d/layers/graphics/featureExpressionInfoUtils.js
var c8 = s.getLogger("esri.views.3d.layers.graphics.featureExpressionInfoUtils");
function o15(e16) {
  return { cachedResult: e16.cachedResult, arcade: e16.arcade ? { func: e16.arcade.func, context: e16.arcade.modules.arcadeUtils.createExecContext(null, { sr: e16.arcade.context.spatialReference }), modules: e16.arcade.modules } : null };
}
async function a16(e16, r28, c15, o22) {
  const u12 = e16 && e16.expression;
  if ("string" != typeof u12) return null;
  const a25 = m5(u12);
  if (null != a25) return { cachedResult: a25 };
  const s20 = await i10();
  f(c15);
  const l22 = s20.arcadeUtils, i21 = l22.createSyntaxTree(u12);
  return l22.dependsOnView(i21) ? (null != o22 && o22.error("Expressions containing '$view' are not supported on ElevationInfo"), { cachedResult: 0 }) : { arcade: { func: l22.createFunction(i21), context: l22.createExecContext(null, { sr: r28 }), modules: s20 } };
}
function s14(e16, t27, r28) {
  return e16.arcadeUtils.createFeature(t27.attributes, t27.geometry, r28);
}
function l18(e16, t27) {
  if (null != e16 && !p7(e16)) {
    if (!t27 || !e16.arcade) return void c8.errorOncePerTick("Arcade support required but not provided");
    const n28 = t27;
    n28._geometry && (n28._geometry = f8(n28._geometry)), e16.arcade.modules.arcadeUtils.updateExecContext(e16.arcade.context, t27);
  }
}
function i13(e16) {
  if (null != e16) {
    if (p7(e16)) return e16.cachedResult;
    const t27 = e16.arcade;
    let r28 = t27 == null ? void 0 : t27.modules.arcadeUtils.executeFunction(t27.func, t27.context);
    return "number" != typeof r28 && (e16.cachedResult = 0, r28 = 0), r28;
  }
  return 0;
}
function d6(e16, t27 = false) {
  let r28 = e16 && e16.featureExpressionInfo;
  const n28 = r28 && r28.expression;
  return t27 || "0" === n28 || (r28 = null), r28 ?? null;
}
var f11 = { cachedResult: 0 };
function p7(e16) {
  return null != e16.cachedResult;
}
function m5(e16) {
  return "0" === e16 ? 0 : null;
}

// node_modules/@arcgis/core/views/3d/layers/graphics/ElevationContext.js
var h11 = class _h {
  constructor() {
    this._meterUnitOffset = 0, this._renderUnitOffset = 0, this._unit = "meters", this._metersPerElevationInfoUnit = 1, this._featureExpressionInfoContext = null, this.centerPointInElevationSR = null, this.mode = null;
  }
  get featureExpressionInfoContext() {
    return this._featureExpressionInfoContext;
  }
  get meterUnitOffset() {
    return this._meterUnitOffset;
  }
  get unit() {
    return this._unit;
  }
  set unit(t27) {
    this._unit = t27, this._metersPerElevationInfoUnit = r7(t27);
  }
  get requiresSampledElevationInfo() {
    return "absolute-height" !== this.mode;
  }
  reset() {
    this.mode = null, this._meterUnitOffset = 0, this._renderUnitOffset = 0, this._featureExpressionInfoContext = null, this.unit = "meters";
  }
  set offsetMeters(t27) {
    this._meterUnitOffset = t27, this._renderUnitOffset = 0;
  }
  set offsetElevationInfoUnits(t27) {
    this._meterUnitOffset = t27 * this._metersPerElevationInfoUnit, this._renderUnitOffset = 0;
  }
  addOffsetRenderUnits(t27) {
    this._renderUnitOffset += t27;
  }
  geometryZWithOffset(t27, e16) {
    const n28 = this.calculateOffsetRenderUnits(e16);
    return null != this.featureExpressionInfoContext ? n28 : t27 + n28;
  }
  calculateOffsetRenderUnits(t27) {
    let e16 = this._meterUnitOffset;
    const n28 = this.featureExpressionInfoContext;
    return null != n28 && (e16 += i13(n28) * this._metersPerElevationInfoUnit), e16 / t27.unitInMeters + this._renderUnitOffset;
  }
  setFromElevationInfo(e16) {
    this.mode = e16.mode, this.unit = n7(e16.unit) ? e16.unit : "meters", this.offsetElevationInfoUnits = l(e16.offset, 0);
  }
  updateFeatureExpressionInfoContext(t27, s20, i21) {
    if (t(t27)) return void (this._featureExpressionInfoContext = null);
    const r28 = t27 && t27.arcade;
    r28 && r(s20) && r(i21) ? (this._featureExpressionInfoContext = o15(t27), l18(this._featureExpressionInfoContext, s14(r28.modules, s20, i21))) : this._featureExpressionInfoContext = t27;
  }
  static fromElevationInfo(t27) {
    const e16 = new _h();
    return r(t27) && e16.setFromElevationInfo(t27), e16;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/Object3D.js
var x9 = class extends r10 {
  get geometries() {
    return this._geometries;
  }
  get transformation() {
    return this._transformation;
  }
  set transformation(t27) {
    n6(this._transformation, t27), this._invalidateBoundingVolume(), this._emit("objectTransformation", this);
  }
  constructor(e16 = {}) {
    super(), this.type = e8.Object, this._geometries = new Array(), this._transformation = e7(), this._bvObjectSpace = new L3(), this._bvWorldSpace = new L3(), this._bvDirty = true, this._hasVolatileTransformation = false, this._visible = true, this.castShadow = null == e16.castShadow || e16.castShadow, this.metadata = e16.metadata, this.metadata && this.metadata.isElevationSource && (this.metadata.lastValidElevationBB = new M6());
    const i21 = e16.geometries;
    r(i21) && (this._geometries = Array.from(i21));
  }
  dispose() {
    this._geometries.length = 0;
  }
  get parentLayer() {
    return this._parentLayer;
  }
  set parentLayer(t27) {
    s6(null == this._parentLayer || null == t27, "Object3D can only be added to a single Layer"), this._parentLayer = t27;
  }
  addGeometry(t27) {
    t27.visible = this._visible, this._geometries.push(t27), this._hasVolatileTransformation = this._hasVolatileTransformation || t27.hasVolatileTransformation, this._emit("objectGeometryAdded", { object: this, geometry: t27 }), this._invalidateBoundingVolume();
  }
  removeGeometry(t27) {
    const e16 = this._geometries.splice(t27, 1)[0];
    e16 && (this._emit("objectGeometryRemoved", { object: this, geometry: e16 }), this._invalidateBoundingVolume());
  }
  removeAllGeometries() {
    for (; this._geometries.length > 0; ) this.removeGeometry(0);
  }
  geometryVertexAttrsUpdated(t27) {
    this._emit("objectGeometryUpdated", { object: this, geometry: t27 }), this._invalidateBoundingVolume();
  }
  get visible() {
    return this._visible;
  }
  set visible(t27) {
    if (this._visible !== t27) {
      this._visible = t27;
      for (const t28 of this._geometries) t28.visible = this._visible;
      this._emit("visibilityChanged", this);
    }
  }
  maskOccludee() {
    const t27 = new r11(t5.MaskOccludee);
    for (const e16 of this._geometries) e16.occludees = n13(e16.occludees, t27);
    return this._emit("occlusionChanged", this), t27;
  }
  removeOcclude(t27) {
    for (const e16 of this._geometries) e16.occludees = o8(e16.occludees, t27);
    this._emit("occlusionChanged", this);
  }
  highlight() {
    const t27 = new r11(t5.Highlight);
    for (const e16 of this._geometries) e16.highlights = n13(e16.highlights, t27);
    return this._emit("highlightChanged", this), t27;
  }
  removeHighlight(t27) {
    for (const e16 of this._geometries) e16.highlights = o8(e16.highlights, t27);
    this._emit("highlightChanged", this);
  }
  getCombinedStaticTransformation(t27, e16) {
    return c2(e16, this.transformation, t27.transformation);
  }
  _getCombinedShaderTransformation(t27) {
    return c2(e7(), this.transformation, t27.shaderTransformation);
  }
  hasVolativeTransformation() {
    return this._hasVolatileTransformation;
  }
  get boundingVolumeWorldSpace() {
    return this._validateBoundingVolume(), this._bvWorldSpace;
  }
  get boundingVolumeObjectSpace() {
    return this._validateBoundingVolume(), this._bvObjectSpace;
  }
  _validateBoundingVolume() {
    if (!this._bvDirty && !this._hasVolatileTransformation) return;
    this._bvObjectSpace.init(), this._bvWorldSpace.init();
    for (const e16 of this._geometries) {
      const i22 = e16.boundingInfo;
      r(i22) && (O7(i22, this._bvObjectSpace, e16.shaderTransformation), O7(i22, this._bvWorldSpace, this._getCombinedShaderTransformation(e16)));
    }
    A(this._bvObjectSpace.bounds, this._bvObjectSpace.min, this._bvObjectSpace.max, 0.5), A(this._bvWorldSpace.bounds, this._bvWorldSpace.min, this._bvWorldSpace.max, 0.5);
    const i21 = n4(), s20 = n4(), o22 = l12(this.transformation);
    for (const t27 of this._geometries) {
      const r28 = t27.boundingInfo;
      if (t(r28)) continue;
      const a25 = t27.shaderTransformation, m9 = l12(a25);
      O2(i21, r28.center, a25);
      const c15 = x(i21, this._bvObjectSpace.bounds), l22 = r28.radius * m9;
      this._bvObjectSpace.bounds[3] = Math.max(this._bvObjectSpace.bounds[3], c15 + l22), O2(s20, i21, this.transformation);
      const b9 = x(s20, this._bvWorldSpace.bounds), d11 = l22 * o22;
      this._bvWorldSpace.bounds[3] = Math.max(this._bvWorldSpace.bounds[3], b9 + d11);
    }
    this._bvDirty = false;
  }
  _invalidateBoundingVolume() {
    this._bvDirty = true, r(this._parentLayer) && this._parentLayer.notifyObjectBBChanged(this, this._bvWorldSpace.bounds);
  }
  _emit(e16, i21) {
    r(this._parentLayer) && this._parentLayer.events.emit(e16, i21);
  }
  get test() {
    const t27 = this;
    return { hasGeometry: (e16) => t27._geometries.includes(e16), getGeometryIndex: (e16) => t27._geometries.indexOf(e16) };
  }
};
var M6 = class {
  constructor() {
    this.min = r2(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE), this.max = r2(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);
  }
  isEmpty() {
    return this.max[0] < this.min[0] && this.max[1] < this.min[1] && this.max[2] < this.min[2];
  }
};
var L3 = class extends M6 {
  constructor() {
    super(...arguments), this.bounds = R3();
  }
  init() {
    o2(this.min, Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE), o2(this.max, -Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE), C3(this.bounds);
  }
};
function O7(t27, e16, i21) {
  const s20 = t27.bbMin, r28 = t27.bbMax;
  if (H2(i21)) {
    const t28 = o2(U3, i21[12], i21[13], i21[14]);
    u3(E10, s20, t28), u3(T5, r28, t28);
    for (let i22 = 0; i22 < 3; ++i22) e16.min[i22] = Math.min(e16.min[i22], E10[i22]), e16.max[i22] = Math.max(e16.max[i22], T5[i22]);
  } else if (O2(E10, s20, i21), F(s20, r28)) for (let o22 = 0; o22 < 3; ++o22) e16.min[o22] = Math.min(e16.min[o22], E10[o22]), e16.max[o22] = Math.max(e16.max[o22], E10[o22]);
  else {
    O2(T5, r28, i21);
    for (let t28 = 0; t28 < 3; ++t28) e16.min[t28] = Math.min(e16.min[t28], E10[t28], T5[t28]), e16.max[t28] = Math.max(e16.max[t28], E10[t28], T5[t28]);
    for (let t28 = 0; t28 < 3; ++t28) {
      r3(E10, s20), r3(T5, r28), E10[t28] = r28[t28], T5[t28] = s20[t28], O2(E10, E10, i21), O2(T5, T5, i21);
      for (let t29 = 0; t29 < 3; ++t29) e16.min[t29] = Math.min(e16.min[t29], E10[t29], T5[t29]), e16.max[t29] = Math.max(e16.max[t29], E10[t29], T5[t29]);
    }
  }
}
var U3 = n4();
var E10 = n4();
var T5 = n4();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/DirtyEvents.js
var e12 = ["layerObjectAdded", "layerObjectRemoved", "layerObjectsAdded", "layerObjectsRemoved", "shaderTransformationChanged", "objectTransformation", "visibilityChanged", "occlusionChanged", "highlightChanged", "objectGeometryAdded", "objectGeometryRemoved", "objectGeometryUpdated"];

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/WebGLLayer.js
var l19 = class extends r10 {
  get objects() {
    return this._objects;
  }
  constructor(s20, r28 = "") {
    super(), this.apiLayerUid = r28, this.type = e8.Layer, this.events = new n3(), this.sliceable = false, this._objects = new l2(), this._objectsAdded = new l2(), this._stageHandles = new t2(), this.apiLayerUid = r28, this.visible = (s20 == null ? void 0 : s20.visible) ?? true, this.pickable = (s20 == null ? void 0 : s20.pickable) ?? true, this.updatePolicy = (s20 == null ? void 0 : s20.updatePolicy) ?? C4.ASYNC, this._disableOctree = (s20 == null ? void 0 : s20.disableOctree) ?? false;
  }
  destroy() {
    this.detachStage(), this._stage = null;
  }
  attachStage(e16) {
    this.detachStage(), this._stage = e16;
    for (const t27 of e12) this._stageHandles.add(this.events.on(t27, (s20) => e16.handleEvent(t27, s20)));
  }
  detachStage() {
    this._stageHandles.removeAll(), this.invalidateSpatialQueryAccelerator();
  }
  add(e16) {
    this._objects.push(e16), e16.parentLayer = this, this.events.emit("layerObjectAdded", { layer: this, object: e16 }), r(this._octree) && this._objectsAdded.push(e16);
  }
  remove(e16) {
    this._objects.removeUnordered(e16) && (e16.parentLayer = null, this.events.emit("layerObjectRemoved", { layer: this, object: e16 }), r(this._octree) && (this._objectsAdded.removeUnordered(e16) || this._octree.remove([e16])));
  }
  addMany(e16) {
    this._objects.pushArray(e16);
    for (const t27 of e16) t27.parentLayer = this;
    this.events.emit("layerObjectsAdded", { layer: this, objects: e16 }), r(this._octree) && this._objectsAdded.pushArray(e16);
  }
  removeMany(e16) {
    const t27 = new Array();
    if (this._objects.removeUnorderedMany(e16, e16.length, t27), 0 !== t27.length) {
      for (const e17 of t27) e17.parentLayer = null;
      if (this.events.emit("layerObjectsRemoved", { layer: this, objects: t27 }), r(this._octree)) {
        for (let e17 = 0; e17 < t27.length; ) this._objectsAdded.removeUnordered(t27[e17]) ? (t27[e17] = t27[t27.length - 1], t27.length -= 1) : ++e17;
        this._octree.remove(t27);
      }
    }
  }
  sync() {
    r(this._stage) && this.updatePolicy !== C4.SYNC && this._stage.syncLayer(this.id);
  }
  notifyObjectBBChanged(e16, t27) {
    r(this._octree) && !this._objectsAdded.includes(e16) && this._octree.update(e16, t27);
  }
  getSpatialQueryAccelerator() {
    return t(this._octree) && this._objects.length > 50 && !this._disableOctree ? (this._octree = new V3((e16) => e16.boundingVolumeWorldSpace.bounds), this._octree.add(this._objects.data, this._objects.length)) : r(this._octree) && this._objectsAdded.length > 0 && (this._octree.add(this._objectsAdded.data, this._objectsAdded.length), this._objectsAdded.clear()), this._octree;
  }
  shaderTransformationChanged() {
    this.invalidateSpatialQueryAccelerator(), this.events.emit("shaderTransformationChanged", this);
  }
  invalidateSpatialQueryAccelerator() {
    this._octree = a(this._octree), this._objectsAdded.clear();
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/fov.js
function t15(t27, a25, n28) {
  return 2 * Math.atan(Math.sqrt(a25 * a25 + n28 * n28) * Math.tan(0.5 * t27) / a25);
}
function a17(t27, a25, n28) {
  return 2 * Math.atan(Math.sqrt(a25 * a25 + n28 * n28) * Math.tan(0.5 * t27) / n28);
}
function n15(t27, a25, n28) {
  return 2 * Math.atan(a25 * Math.tan(0.5 * t27) / Math.sqrt(a25 * a25 + n28 * n28));
}
function r17(t27, a25, n28) {
  return 2 * Math.atan(n28 * Math.tan(0.5 * t27) / Math.sqrt(a25 * a25 + n28 * n28));
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/Camera.js
var Z2;
var $2 = Z2 = class extends v2 {
  constructor(t27 = {}) {
    super(t27), this._center = n4(), this._up = n4(), this._viewUp = n4(), this._viewForward = n4(), this._viewRight = n4(), this._ray = d(), this._viewport = r5(0, 0, 1, 1), this._padding = r5(0, 0, 0, 0), this._fov = 55 / 180 * Math.PI, this._nearFar = r9(1, 1e3), this._viewDirty = true, this._viewMatrix = e7(), this._viewProjectionDirty = true, this._viewProjectionMatrix = e7(), this._viewInverseTransposeMatrixDirty = true, this._viewInverseTransposeMatrix = e7(), this._frustumDirty = true, this._frustum = H4(), this._fullViewport = n5(), this._pixelRatio = 1, this.relativeElevation = 0;
  }
  get pixelRatio() {
    return this._pixelRatio;
  }
  set pixelRatio(t27) {
    this._pixelRatio = t27 > 0 ? t27 : 1;
  }
  get eye() {
    return this._ray.origin;
  }
  set eye(t27) {
    this._compareAndSetView(t27, this._ray.origin);
  }
  get center() {
    return this._center;
  }
  set center(t27) {
    this._compareAndSetView(t27, this._center, "_center");
  }
  get ray() {
    return e5(this._ray.direction, this.center, this.eye), this._ray;
  }
  get up() {
    return this._up;
  }
  set up(t27) {
    this._compareAndSetView(t27, this._up, "_up");
  }
  get viewMatrix() {
    return this._ensureViewClean(), this._viewMatrix;
  }
  set viewMatrix(t27) {
    n6(this._viewMatrix, t27), this._viewDirty = false, this._viewInverseTransposeMatrixDirty = true, this._viewProjectionDirty = true, this._frustumDirty = true;
  }
  get viewForward() {
    return this._ensureViewClean(), this._viewForward;
  }
  get viewUp() {
    return this._ensureViewClean(), this._viewUp;
  }
  get viewRight() {
    return this._ensureViewClean(), this._viewRight;
  }
  get nearFar() {
    return this._nearFar;
  }
  get near() {
    return this._nearFar[0];
  }
  set near(t27) {
    this._nearFar[0] !== t27 && (this._nearFar[0] = t27, this._viewProjectionDirty = true, this._frustumDirty = true, this.notifyChange("_nearFar"));
  }
  get far() {
    return this._nearFar[1];
  }
  set far(t27) {
    this._nearFar[1] !== t27 && (this._nearFar[1] = t27, this._viewProjectionDirty = true, this._frustumDirty = true, this.notifyChange("_nearFar"));
  }
  get viewport() {
    return this._viewport;
  }
  set viewport(t27) {
    this.x = t27[0], this.y = t27[1], this.width = t27[2], this.height = t27[3];
  }
  get screenViewport() {
    if (1 === this.pixelRatio) return this._viewport;
    const t27 = l4(n5(), this._viewport, 1 / this.pixelRatio), i21 = this._get("screenViewport");
    return i21 && E(t27, i21) ? i21 : t27;
  }
  get x() {
    return this._viewport[0];
  }
  set x(t27) {
    t27 += this._padding[ht.LEFT], this._viewport[0] !== t27 && (this._viewport[0] = t27, this._viewProjectionDirty = true, this._frustumDirty = true, this.notifyChange("_viewport"));
  }
  get y() {
    return this._viewport[1];
  }
  set y(t27) {
    t27 += this._padding[ht.BOTTOM], this._viewport[1] !== t27 && (this._viewport[1] = t27, this._viewProjectionDirty = true, this._frustumDirty = true, this.notifyChange("_viewport"));
  }
  get width() {
    return this._viewport[2];
  }
  set width(t27) {
    this._viewport[2] !== t27 && (this._viewport[2] = t27, this._viewProjectionDirty = true, this._frustumDirty = true, this.notifyChange("_viewport"));
  }
  get height() {
    return this._viewport[3];
  }
  set height(t27) {
    this._viewport[3] !== t27 && (this._viewport[3] = t27, this._viewProjectionDirty = true, this._frustumDirty = true, this.notifyChange("_viewport"));
  }
  get fullWidth() {
    return this._viewport[2] + this._padding[ht.RIGHT] + this._padding[ht.LEFT];
  }
  set fullWidth(t27) {
    this.width = t27 - (this._padding[ht.RIGHT] + this._padding[ht.LEFT]);
  }
  get fullHeight() {
    return this._viewport[3] + this._padding[ht.TOP] + this._padding[ht.BOTTOM];
  }
  set fullHeight(t27) {
    this.height = t27 - (this._padding[ht.TOP] + this._padding[ht.BOTTOM]);
  }
  get fullViewport() {
    return this._fullViewport[0] = this._viewport[0] - this._padding[ht.LEFT], this._fullViewport[1] = this._viewport[1] - this._padding[ht.BOTTOM], this._fullViewport[2] = this.fullWidth, this._fullViewport[3] = this.fullHeight, this._fullViewport;
  }
  get _aspect() {
    return this.width / this.height;
  }
  get padding() {
    return this._padding;
  }
  set padding(t27) {
    D(this._padding, t27) || (this._viewport[0] += t27[ht.LEFT] - this._padding[ht.LEFT], this._viewport[1] += t27[ht.BOTTOM] - this._padding[ht.BOTTOM], this._viewport[2] -= t27[ht.RIGHT] + t27[ht.LEFT] - (this._padding[ht.RIGHT] + this._padding[ht.LEFT]), this._viewport[3] -= t27[ht.TOP] + t27[ht.BOTTOM] - (this._padding[ht.TOP] + this._padding[ht.BOTTOM]), a3(this._padding, t27), this._viewProjectionDirty = true, this._frustumDirty = true, this.notifyChange("_padding"), this.notifyChange("_viewport"));
  }
  get viewProjectionMatrix() {
    return this._viewProjectionDirty && (c2(this._viewProjectionMatrix, this.projectionMatrix, this.viewMatrix), this._viewProjectionDirty = false), this._viewProjectionMatrix;
  }
  get projectionMatrix() {
    const t27 = this.width, i21 = this.height, e16 = this.near * Math.tan(this.fovY / 2), r28 = e16 * this._aspect, s20 = _3(e7(), -r28 * (1 + 2 * this._padding[ht.LEFT] / t27), r28 * (1 + 2 * this._padding[ht.RIGHT] / t27), -e16 * (1 + 2 * this._padding[ht.BOTTOM] / i21), e16 * (1 + 2 * this._padding[ht.TOP] / i21), this.near, this.far), h16 = this._get("projectionMatrix");
    return h16 && C(h16, s20) ? h16 : s20;
  }
  get inverseProjectionMatrix() {
    return h3(e7(), this.projectionMatrix) || this._get("inverseProjectionMatrix") || e7();
  }
  get fov() {
    return this._fov;
  }
  set fov(t27) {
    this._fov = t27, this._viewProjectionDirty = true, this._frustumDirty = true;
  }
  get fovX() {
    return n15(this._fov, this.width, this.height);
  }
  set fovX(t27) {
    this._fov = t15(t27, this.width, this.height), this._viewProjectionDirty = true, this._frustumDirty = true;
  }
  get fovY() {
    return r17(this._fov, this.width, this.height);
  }
  set fovY(t27) {
    this._fov = a17(t27, this.width, this.height), this._viewProjectionDirty = true, this._frustumDirty = true;
  }
  get distance() {
    return x(this.center, this.eye);
  }
  get frustum() {
    return this._recomputeFrustum(), this._frustum;
  }
  get viewInverseTransposeMatrix() {
    return (this._viewInverseTransposeMatrixDirty || this._viewDirty) && (h3(this._viewInverseTransposeMatrix, this.viewMatrix), o3(this._viewInverseTransposeMatrix, this._viewInverseTransposeMatrix), this._viewInverseTransposeMatrixDirty = false), this._viewInverseTransposeMatrix;
  }
  depthNDCToWorld(t27) {
    const i21 = 2 * t27 - 1;
    return 2 * this.near * this.far / (this.far + this.near - i21 * (this.far - this.near));
  }
  get perRenderPixelRatio() {
    return Math.tan(this.fovX / 2) / (this.width / 2);
  }
  get perScreenPixelRatio() {
    return this.perRenderPixelRatio * this.pixelRatio;
  }
  get aboveGround() {
    return null != this.relativeElevation && this.relativeElevation >= 0;
  }
  copyFrom(t27) {
    r3(this._ray.origin, t27.eye), this.center = t27.center, this.up = t27.up, a3(this._viewport, t27.viewport), this.notifyChange("_viewport"), a3(this._padding, t27.padding), this.notifyChange("_padding"), a7(this._nearFar, t27.nearFar), this.notifyChange("_nearFar"), this._fov = t27.fov, this.relativeElevation = t27.relativeElevation;
    const i21 = t27;
    return this._viewDirty = i21._viewDirty, this._viewDirty || (n6(this._viewMatrix, t27.viewMatrix), r3(this._viewRight, t27.viewRight), r3(this._viewUp, t27.viewUp), r3(this._viewForward, t27.viewForward)), this._viewProjectionDirty = true, this._frustumDirty = i21._frustumDirty, this._frustumDirty || (u9(this._frustum, t27.frustum), this._frustumDirty = false), i21._viewInverseTransposeMatrixDirty ? this._viewInverseTransposeMatrixDirty = true : (n6(this._viewInverseTransposeMatrix, t27.viewInverseTransposeMatrix), this._viewInverseTransposeMatrixDirty = false), a3(this._fullViewport, t27.fullViewport), this.pixelRatio = t27.pixelRatio, this;
  }
  copyViewFrom(t27) {
    this.eye = t27.eye, this.center = t27.center, this.up = t27.up;
  }
  clone() {
    return new Z2().copyFrom(this);
  }
  equals(t27) {
    return F(this.eye, t27.eye) && F(this.center, t27.center) && F(this.up, t27.up) && D(this._viewport, t27.viewport) && D(this._padding, t27.padding) && E2(this.nearFar, t27.nearFar) && this._fov === t27.fov && this.pixelRatio === t27.pixelRatio && this.relativeElevation === t27.relativeElevation;
  }
  almostEquals(t27) {
    if (Math.abs(t27.fov - this._fov) >= 1e-3 || b2(t27.padding, this._padding) >= 0.5 || b2(this.screenViewport, t27.screenViewport) >= 0.5) return false;
    J(et, t27.eye, t27.center), J(rt, this.eye, this.center);
    const i21 = P(et, rt), e16 = Z(et), r28 = Z(rt), s20 = 5e-4;
    return i21 * i21 >= (1 - 1e-10) * e16 * r28 && X(t27.eye, this.eye) < Math.max(e16, r28) * s20 * s20;
  }
  computeRenderPixelSizeAt(t27) {
    return this.computeRenderPixelSizeAtDist(this._viewDirectionDistance(t27));
  }
  computeRenderPixelSizeAtDist(t27) {
    return t27 * this.perRenderPixelRatio;
  }
  computeScreenPixelSizeAt(t27) {
    return this.computeScreenPixelSizeAtDist(this._viewDirectionDistance(t27));
  }
  _viewDirectionDistance(t27) {
    return Math.abs(f3(this.viewForward, e5(et, t27, this.eye)));
  }
  computeScreenPixelSizeAtDist(t27) {
    return t27 * this.perScreenPixelRatio;
  }
  computeDistanceFromRadius(t27, i21) {
    return t27 / Math.tan(Math.min(this.fovX, this.fovY) / (2 * (i21 || 1)));
  }
  getScreenCenter(t27 = i()) {
    return t27[0] = (this.padding[ht.LEFT] + this.width / 2) / this.pixelRatio, t27[1] = (this.padding[ht.TOP] + this.height / 2) / this.pixelRatio, t27;
  }
  getRenderCenter(t27, i21 = 0.5, e16 = 0.5) {
    return t27[0] = this.padding[ht.LEFT] + this.width * i21, t27[1] = this.padding[ht.BOTTOM] + this.height * e16, t27[2] = 0.5, t27;
  }
  setGLViewport(t27) {
    const i21 = this.viewport, e16 = this.padding;
    t27.setViewport(i21[0] - e16[3], i21[1] - e16[2], i21[2] + e16[1] + e16[3], i21[3] + e16[0] + e16[2]);
  }
  applyProjection(t27, i21) {
    t27 !== tt && r3(tt, t27), tt[3] = 1, w(tt, tt, this.projectionMatrix);
    const e16 = Math.abs(tt[3]);
    g(tt, tt, 1 / e16);
    const s20 = this.fullViewport;
    i21[0] = h2(0, s20[0] + s20[2], 0.5 + 0.5 * tt[0]), i21[1] = h2(0, s20[1] + s20[3], 0.5 + 0.5 * tt[1]), i21[2] = 0.5 * (tt[2] + 1), i21[3] = e16;
  }
  unapplyProjection(t27, i21) {
    const e16 = this.fullViewport;
    tt[0] = (t27[0] / (e16[0] + e16[2]) * 2 - 1) * t27[3], tt[1] = (t27[1] / (e16[1] + e16[3]) * 2 - 1) * t27[3], tt[2] = (2 * t27[2] - 1) * t27[3], tt[3] = t27[3], null != this.inverseProjectionMatrix && (w(tt, tt, this.inverseProjectionMatrix), i21[0] = tt[0], i21[1] = tt[1], i21[2] = tt[2]);
  }
  projectToScreen(t27, i21) {
    return this.projectToRenderScreen(t27, st), this.renderToScreen(st, i21), i21;
  }
  projectToRenderScreen(t27, i21) {
    if (tt[0] = t27[0], tt[1] = t27[1], tt[2] = t27[2], tt[3] = 1, w(tt, tt, this.viewProjectionMatrix), 0 === tt[3]) return null;
    g(tt, tt, 1 / Math.abs(tt[3]));
    const e16 = this.fullViewport;
    return "x" in i21 ? (i21.x = h2(0, e16[0] + e16[2], 0.5 + 0.5 * tt[0]), i21.y = h2(0, e16[1] + e16[3], 0.5 + 0.5 * tt[1])) : (i21[0] = h2(0, e16[0] + e16[2], 0.5 + 0.5 * tt[0]), i21[1] = h2(0, e16[1] + e16[3], 0.5 + 0.5 * tt[1]), i21.length > 2 && (i21[2] = 0.5 * (tt[2] + 1))), i21;
  }
  unprojectFromScreen(t27, i21) {
    return this.unprojectFromRenderScreen(this.screenToRender(t27, st), i21);
  }
  unprojectFromRenderScreen(t27, i21) {
    if (c2(it, this.projectionMatrix, this.viewMatrix), !h3(it, it)) return null;
    const e16 = this.fullViewport;
    return tt[0] = 2 * (t27[0] - e16[0]) / e16[2] - 1, tt[1] = 2 * (t27[1] - e16[1]) / e16[3] - 1, tt[2] = 2 * t27[2] - 1, tt[3] = 1, w(tt, tt, it), 0 === tt[3] ? null : (i21[0] = tt[0] / tt[3], i21[1] = tt[1] / tt[3], i21[2] = tt[2] / tt[3], i21);
  }
  constrainWindowSize(t27, i21, e16, r28) {
    const s20 = t27 * this.pixelRatio, h16 = i21 * this.pixelRatio, o22 = Math.max(s20 - e16 / 2, 0), n28 = Math.max(this.fullHeight - h16 - r28 / 2, 0), a25 = -Math.min(s20 - e16 / 2, 0), p13 = -Math.min(this.fullHeight - h16 - r28 / 2, 0);
    return [o22, n28, e16 - a25 - -Math.min(this.fullWidth - s20 - e16 / 2, 0), r28 - p13 - -Math.min(h16 - r28 / 2, 0)];
  }
  computeUp(t27) {
    t27 === l13.Global ? this._computeUpGlobal() : this._computeUpLocal();
  }
  screenToRender(t27, i21) {
    const e16 = t27[0] * this.pixelRatio, r28 = this.fullHeight - t27[1] * this.pixelRatio;
    return i21[0] = e16, i21[1] = r28, i21;
  }
  renderToScreen(t27, i21) {
    const e16 = t27[0] / this.pixelRatio, r28 = (this.fullHeight - t27[1]) / this.pixelRatio;
    i21[0] = e16, i21[1] = r28;
  }
  _computeUpGlobal() {
    e5(et, this.center, this.eye);
    const t27 = s2(this.center);
    t27 < 1 ? (o2(this._up, 0, 0, 1), this._markViewDirty(), this.notifyChange("_up")) : Math.abs(P(et, this.center)) > 0.9999 * s2(et) * t27 || (_(this._up, et, this.center), _(this._up, this._up, et), z2(this._up, this._up), this.notifyChange("_up"), this._markViewDirty());
  }
  _computeUpLocal() {
    H(et, this.eye, this.center), Math.abs(et[2]) <= 0.9999 && (g(et, et, et[2]), o2(this._up, -et[0], -et[1], 1 - et[2]), z2(this._up, this._up), this.notifyChange("_up"), this._markViewDirty());
  }
  _compareAndSetView(t27, i21, r28 = "") {
    "number" == typeof t27[0] && isFinite(t27[0]) && "number" == typeof t27[1] && isFinite(t27[1]) && "number" == typeof t27[2] && isFinite(t27[2]) ? F(t27, i21) || (r3(i21, t27), this._markViewDirty(), r28.length && this.notifyChange(r28)) : s.getLogger("esri.views.3d.webgl-engine.lib.Camera").warn("Camera vector contains invalid number, ignoring value");
  }
  _markViewDirty() {
    this._viewDirty = true, this._frustumDirty = true, this._viewProjectionDirty = true;
  }
  _recomputeFrustum() {
    this._frustumDirty && (s10(this.viewMatrix, this.projectionMatrix, this._frustum), this._frustumDirty = false);
  }
  _ensureViewClean() {
    this._viewDirty && (k(this._viewMatrix, this.eye, this.center, this.up), o2(this._viewForward, -this._viewMatrix[2], -this._viewMatrix[6], -this._viewMatrix[10]), o2(this._viewUp, this._viewMatrix[1], this._viewMatrix[5], this._viewMatrix[9]), o2(this._viewRight, this._viewMatrix[0], this._viewMatrix[4], this._viewMatrix[8]), this._viewDirty = false, this._viewInverseTransposeMatrixDirty = true);
  }
};
e([y3()], $2.prototype, "_center", void 0), e([y3()], $2.prototype, "_up", void 0), e([y3()], $2.prototype, "_viewport", void 0), e([y3()], $2.prototype, "_padding", void 0), e([y3()], $2.prototype, "_fov", void 0), e([y3()], $2.prototype, "_nearFar", void 0), e([y3()], $2.prototype, "_pixelRatio", void 0), e([y3()], $2.prototype, "pixelRatio", null), e([y3()], $2.prototype, "eye", null), e([y3()], $2.prototype, "center", null), e([y3()], $2.prototype, "up", null), e([y3({ readOnly: true })], $2.prototype, "nearFar", null), e([y3()], $2.prototype, "near", null), e([y3()], $2.prototype, "far", null), e([y3()], $2.prototype, "viewport", null), e([y3({ readOnly: true })], $2.prototype, "screenViewport", null), e([y3()], $2.prototype, "x", null), e([y3()], $2.prototype, "y", null), e([y3()], $2.prototype, "width", null), e([y3()], $2.prototype, "height", null), e([y3()], $2.prototype, "fullWidth", null), e([y3()], $2.prototype, "fullHeight", null), e([y3({ readOnly: true })], $2.prototype, "_aspect", null), e([y3()], $2.prototype, "padding", null), e([y3({ readOnly: true })], $2.prototype, "projectionMatrix", null), e([y3({ readOnly: true })], $2.prototype, "inverseProjectionMatrix", null), e([y3()], $2.prototype, "fov", null), e([y3()], $2.prototype, "fovX", null), e([y3()], $2.prototype, "fovY", null), $2 = Z2 = e([a2("esri.views.3d.webgl-engine.lib.Camera")], $2);
var tt = n5();
var it = e7();
var et = n4();
var rt = n4();
var st = x2();
var ht;
!function(t27) {
  t27[t27.TOP = 0] = "TOP", t27[t27.RIGHT = 1] = "RIGHT", t27[t27.BOTTOM = 2] = "BOTTOM", t27[t27.LEFT = 3] = "LEFT";
}(ht || (ht = {}));

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/RibbonLineTechnique.js
var I3 = /* @__PURE__ */ new Map([[O3.POSITION, 0], [O3.SUBDIVISIONFACTOR, 1], [O3.UV0, 2], [O3.AUXPOS1, 3], [O3.AUXPOS2, 4], [O3.COLOR, 5], [O3.COLORFEATUREATTRIBUTE, 5], [O3.SIZE, 6], [O3.SIZEFEATUREATTRIBUTE, 6], [O3.OPACITYFEATUREATTRIBUTE, 7], [O3.OBJECTANDLAYERIDCOLOR, 8]]);
var y6 = class _y extends e9 {
  initializeProgram(e16) {
    return new o12(e16.rctx, _y.shader.get().build(this.configuration), I3);
  }
  _makePipelineState(t27, i21) {
    const c15 = this.configuration, a25 = t27 === o11.NONE, d11 = t27 === o11.FrontFace;
    return W({ blending: c15.output === h6.Color || c15.output === h6.Alpha ? a25 ? c5 : A4(t27) : null, depthTest: { func: l16(t27) }, depthWrite: a25 ? c15.writeDepth ? a11 : null : E7(t27), colorWrite: _6, stencilWrite: c15.hasOccludees ? e10 : null, stencilTest: c15.hasOccludees ? i21 ? o13 : f7 : null, polygonOffset: a25 || d11 ? c15.hasPolygonOffset ? _9 : null : _7 });
  }
  initializePipeline() {
    const e16 = this.configuration;
    if (e16.occluder) {
      const t27 = e16.hasPolygonOffset ? _9 : null;
      this._occluderPipelineTransparent = W({ blending: c5, polygonOffset: t27, depthTest: s8, depthWrite: null, colorWrite: _6, stencilWrite: null, stencilTest: m2 }), this._occluderPipelineOpaque = W({ blending: c5, polygonOffset: t27, depthTest: s8, depthWrite: null, colorWrite: _6, stencilWrite: l17, stencilTest: P4 }), this._occluderPipelineMaskWrite = W({ blending: null, polygonOffset: t27, depthTest: i8, depthWrite: null, colorWrite: null, stencilWrite: e10, stencilTest: o13 });
    }
    return this._occludeePipelineState = this._makePipelineState(this.configuration.transparencyPassType, true), this._makePipelineState(this.configuration.transparencyPassType, false);
  }
  get primitiveType() {
    return this.configuration.wireframe ? E3.LINES : E3.TRIANGLE_STRIP;
  }
  getPipelineState(e16, t27) {
    return t27 ? this._occludeePipelineState : this.configuration.occluder ? e16 === E8.TRANSPARENT_OCCLUDER_MATERIAL ? this._occluderPipelineTransparent : e16 === E8.OCCLUDER_MATERIAL ? this._occluderPipelineOpaque : this._occluderPipelineMaskWrite : super.getPipelineState(e16, t27);
  }
};
y6.shader = new t9(A5, () => import("./RibbonLine.glsl-3E3KOKON.js"));
var _9 = { factor: 0, units: -4 };

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/RibbonLineMaterial.js
var B2;
!function(e16) {
  e16[e16.LEFT_JOIN_START = -2] = "LEFT_JOIN_START", e16[e16.LEFT_JOIN_END = -1] = "LEFT_JOIN_END", e16[e16.LEFT_CAP_START = -4] = "LEFT_CAP_START", e16[e16.LEFT_CAP_END = -5] = "LEFT_CAP_END", e16[e16.RIGHT_JOIN_START = 2] = "RIGHT_JOIN_START", e16[e16.RIGHT_JOIN_END = 1] = "RIGHT_JOIN_END", e16[e16.RIGHT_CAP_START = 4] = "RIGHT_CAP_START", e16[e16.RIGHT_CAP_END = 5] = "RIGHT_CAP_END";
}(B2 || (B2 = {}));
var z5 = class extends h8 {
  constructor(e16) {
    super(e16, new H7()), this._configuration = new s12(), this._vertexAttributeLocations = I3, this._layout = this.createLayout();
  }
  isClosed(e16, t27) {
    return Z3(this.parameters, e16, t27);
  }
  getConfiguration(e16, t27) {
    this._configuration.output = e16, this._configuration.draped = t27.slot === E8.DRAPED_MATERIAL;
    const i21 = r(this.parameters.stipplePattern) && e16 !== h6.Highlight;
    return this._configuration.stippleEnabled = i21, this._configuration.stippleOffColorEnabled = i21 && r(this.parameters.stippleOffColor), this._configuration.stippleScaleWithLineWidth = i21 && this.parameters.stippleScaleWithLineWidth, this._configuration.stipplePreferContinuous = i21 && this.parameters.stipplePreferContinuous, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.hasOccludees = this.parameters.hasOccludees, this._configuration.roundJoins = "round" === this.parameters.join, this._configuration.capType = this.parameters.cap, this._configuration.applyMarkerOffset = !!r(this.parameters.markerParameters) && q2(this.parameters.markerParameters), this._configuration.hasPolygonOffset = this.parameters.hasPolygonOffset, this._configuration.writeDepth = this.parameters.writeDepth, this._configuration.vvColor = this.parameters.vvColorEnabled, this._configuration.vvOpacity = this.parameters.vvOpacityEnabled, this._configuration.vvSize = this.parameters.vvSizeEnabled, this._configuration.innerColorEnabled = this.parameters.innerWidth > 0 && r(this.parameters.innerColor), this._configuration.falloffEnabled = this.parameters.falloff > 0, this._configuration.occluder = this.parameters.renderOccluded === c4.OccludeAndTransparentStencil, this._configuration.transparencyPassType = t27.transparencyPassType, this._configuration.hasMultipassTerrain = t27.multipassTerrain.enabled, this._configuration.cullAboveGround = t27.multipassTerrain.cullAboveGround, this._configuration.wireframe = this.parameters.wireframe, this._configuration;
  }
  intersectDraped(e16, r28, i21, s20, a25, n28) {
    if (!i21.options.selectionMode) return;
    const o22 = e16.vertexAttributes.get(O3.POSITION).data, l22 = e16.vertexAttributes.get(O3.SIZE);
    let c15 = this.parameters.width;
    if (this.parameters.vvSizeEnabled) {
      const r29 = e16.vertexAttributes.get(O3.SIZEFEATUREATTRIBUTE).data[0];
      c15 *= a4(this.parameters.vvSizeOffset[0] + r29 * this.parameters.vvSizeFactor[0], this.parameters.vvSizeMinSize[0], this.parameters.vvSizeMaxSize[0]);
    } else l22 && (c15 *= l22.data[0]);
    const h16 = s20[0], u12 = s20[1], p13 = (c15 / 2 + 4) * e16.screenToWorldRatio;
    let m9 = Number.MAX_VALUE, f18 = 0;
    for (let T8 = 0; T8 < o22.length - 5; T8 += 3) {
      const e17 = o22[T8], r29 = o22[T8 + 1], i22 = h16 - e17, s21 = u12 - r29, a26 = o22[T8 + 3] - e17, n29 = o22[T8 + 4] - r29, l23 = a4((a26 * i22 + n29 * s21) / (a26 * a26 + n29 * n29), 0, 1), c16 = a26 * l23 - i22, p14 = n29 * l23 - s21, d11 = c16 * c16 + p14 * p14;
      d11 < m9 && (m9 = d11, f18 = T8 / 3);
    }
    m9 < p13 * p13 && a25(n28.dist, n28.normal, f18, false);
  }
  intersect(r28, i21, m9, f18, T8, _11) {
    if (!m9.options.selectionMode || !r28.visible) return;
    if (!l14(i21)) return void s.getLogger("esri.views.3d.webgl-engine.materials.RibbonLineMaterial").error("intersection assumes a translation-only matrix");
    const R9 = r28.vertexAttributes, O10 = R9.get(O3.POSITION).data;
    let b9 = this.parameters.width;
    if (this.parameters.vvSizeEnabled) {
      const e16 = R9.get(O3.SIZEFEATUREATTRIBUTE).data[0];
      b9 *= a4(this.parameters.vvSizeOffset[0] + e16 * this.parameters.vvSizeFactor[0], this.parameters.vvSizeMinSize[0], this.parameters.vvSizeMaxSize[0]);
    } else R9.has(O3.SIZE) && (b9 *= R9.get(O3.SIZE).data[0]);
    const L5 = m9.camera, P9 = $3;
    a7(P9, m9.point);
    const N7 = b9 * L5.pixelRatio / 2 + 4 * L5.pixelRatio;
    o2(ce[0], P9[0] - N7, P9[1] + N7, 0), o2(ce[1], P9[0] + N7, P9[1] + N7, 0), o2(ce[2], P9[0] + N7, P9[1] - N7, 0), o2(ce[3], P9[0] - N7, P9[1] - N7, 0);
    for (let e16 = 0; e16 < 4; e16++) if (!L5.unprojectFromRenderScreen(ce[e16], he[e16])) return;
    j3(L5.eye, he[0], he[1], ue), j3(L5.eye, he[1], he[2], pe), j3(L5.eye, he[2], he[3], me), j3(L5.eye, he[3], he[0], fe);
    let C8 = Number.MAX_VALUE, D5 = 0;
    const U6 = V6(this.parameters, R9, r28.indices) ? O10.length - 2 : O10.length - 5;
    for (let e16 = 0; e16 < U6; e16 += 3) {
      X3[0] = O10[e16] + i21[12], X3[1] = O10[e16 + 1] + i21[13], X3[2] = O10[e16 + 2] + i21[14];
      const t27 = (e16 + 3) % O10.length;
      if (Y3[0] = O10[t27] + i21[12], Y3[1] = O10[t27 + 1] + i21[13], Y3[2] = O10[t27 + 2] + i21[14], V2(ue, X3) < 0 && V2(ue, Y3) < 0 || V2(pe, X3) < 0 && V2(pe, Y3) < 0 || V2(me, X3) < 0 && V2(me, Y3) < 0 || V2(fe, X3) < 0 && V2(fe, Y3) < 0) continue;
      if (L5.projectToRenderScreen(X3, ee2), L5.projectToRenderScreen(Y3, te), ee2[2] < 0 && te[2] > 0) {
        e5(Q2, X3, Y3);
        const e17 = L5.frustum, t28 = -V2(e17[U.NEAR], X3) / P(Q2, Y2(e17[U.NEAR]));
        g(Q2, Q2, t28), u3(X3, X3, Q2), L5.projectToRenderScreen(X3, ee2);
      } else if (ee2[2] > 0 && te[2] < 0) {
        e5(Q2, Y3, X3);
        const e17 = L5.frustum, t28 = -V2(e17[U.NEAR], Y3) / P(Q2, Y2(e17[U.NEAR]));
        g(Q2, Q2, t28), u3(Y3, Y3, Q2), L5.projectToRenderScreen(Y3, te);
      } else if (ee2[2] < 0 && te[2] < 0) continue;
      ee2[2] = 0, te[2] = 0;
      const r29 = M3(b4(ee2, te, se), P9);
      r29 < C8 && (C8 = r29, r3(re, X3), r3(ie, Y3), D5 = e16 / 3);
    }
    const F6 = m9.rayBegin, x13 = m9.rayEnd;
    if (C8 < N7 * N7) {
      let e16 = Number.MAX_VALUE;
      if (k3(b4(re, ie, se), b4(F6, x13, ae), K)) {
        e5(K, K, F6);
        const t27 = s2(K);
        g(K, K, 1 / t27), e16 = t27 / x(F6, x13);
      }
      _11(e16, K, D5, false);
    }
  }
  createLayout() {
    const e16 = T2().vec3f(O3.POSITION).f32(O3.SUBDIVISIONFACTOR).vec2f(O3.UV0).vec3f(O3.AUXPOS1).vec3f(O3.AUXPOS2);
    return this.parameters.vvSizeEnabled ? e16.f32(O3.SIZEFEATUREATTRIBUTE) : e16.f32(O3.SIZE), this.parameters.vvColorEnabled ? e16.f32(O3.COLORFEATUREATTRIBUTE) : e16.vec4f(O3.COLOR), this.parameters.vvOpacityEnabled && e16.f32(O3.OPACITYFEATUREATTRIBUTE), has("enable-feature:objectAndLayerId-rendering") && e16.vec4u8(O3.OBJECTANDLAYERIDCOLOR), e16;
  }
  createBufferWriter() {
    return new k5(this._layout, this.parameters);
  }
  requiresSlot(e16, t27) {
    if (t27 === h6.Color || t27 === h6.Alpha || t27 === h6.Highlight || t27 === h6.Depth || t27 === h6.ObjectAndLayerIdColor) {
      if (e16 === E8.DRAPED_MATERIAL) return true;
      if (this.parameters.renderOccluded === c4.OccludeAndTransparentStencil) return e16 === E8.OPAQUE_MATERIAL || e16 === E8.OCCLUDER_MATERIAL || e16 === E8.TRANSPARENT_OCCLUDER_MATERIAL;
      if (t27 === h6.Color || t27 === h6.Alpha) {
        return e16 === (this.parameters.writeDepth ? E8.TRANSPARENT_MATERIAL : E8.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL);
      }
      return e16 === E8.OPAQUE_MATERIAL;
    }
    return false;
  }
  createGLMaterial(e16) {
    return new G3(e16);
  }
  validateParameters(e16) {
    "miter" !== e16.join && (e16.miterLimit = 0), r(e16.markerParameters) && (e16.markerScale = e16.markerParameters.width / e16.width);
  }
};
var G3 = class extends t8 {
  constructor() {
    super(...arguments), this._stipplePattern = null;
  }
  dispose() {
    super.dispose(), this._stippleTextureRepository.release(this._stipplePattern), this._stipplePattern = null;
  }
  _updateOccludeeState(e16) {
    e16.hasOccludees !== this._material.parameters.hasOccludees && this._material.setParameters({ hasOccludees: e16.hasOccludees });
  }
  beginSlot(e16) {
    this._output !== h6.Color && this._output !== h6.Alpha || this._updateOccludeeState(e16);
    const t27 = this._material.parameters.stipplePattern;
    return this._stipplePattern !== t27 && (this._material.setParameters(this._stippleTextureRepository.swap(this._stipplePattern, t27)), this._stipplePattern = t27), this.ensureTechnique(y6, e16);
  }
};
var H7 = class extends v7 {
  constructor() {
    super(...arguments), this.width = 0, this.color = _2, this.join = "miter", this.cap = r16.BUTT, this.miterLimit = 5, this.writeDepth = true, this.hasPolygonOffset = false, this.stippleTexture = null, this.stippleScaleWithLineWidth = false, this.stipplePreferContinuous = true, this.markerParameters = null, this.markerScale = 1, this.hasSlicePlane = false, this.vvFastUpdate = false, this.isClosed = false, this.falloff = 0, this.innerWidth = 0, this.hasOccludees = false, this.wireframe = false;
  }
};
var k5 = class {
  constructor(e16, t27) {
    this._parameters = t27, this.numJoinSubdivisions = 0, this.vertexBufferLayout = e16;
    const r28 = t27.stipplePattern ? 1 : 0;
    switch (this._parameters.join) {
      case "miter":
      case "bevel":
        this.numJoinSubdivisions = r28;
        break;
      case "round":
        this.numJoinSubdivisions = j4 + r28;
    }
  }
  _isClosed(e16) {
    return V6(this._parameters, e16.vertexAttributes, e16.indices);
  }
  allocate(e16) {
    return this.vertexBufferLayout.createBuffer(e16);
  }
  elementCount(e16) {
    const t27 = 2, r28 = e16.indices.get(O3.POSITION).length / 2 + 1, i21 = this._isClosed(e16);
    let s20 = i21 ? 2 : 2 * t27;
    return s20 += ((i21 ? r28 : r28 - 1) - (i21 ? 0 : 1)) * (2 * this.numJoinSubdivisions + 4), s20 += 2, this._parameters.wireframe && (s20 = 2 + 4 * (s20 - 2)), s20;
  }
  write(e16, t27, i21, s20, n28) {
    var _a;
    const o22 = ne, l22 = oe, c15 = le, u12 = i21.vertexAttributes.get(O3.POSITION).data, f18 = i21.indices && i21.indices.get(O3.POSITION), T8 = (_a = i21.vertexAttributes.get(O3.DISTANCETOSTART)) == null ? void 0 : _a.data;
    f18 && f18.length !== 2 * (u12.length / 3 - 1) && console.warn("RibbonLineMaterial does not support indices");
    let d11 = 1, _11 = 0;
    this._parameters.vvSizeEnabled ? _11 = i21.vertexAttributes.get(O3.SIZEFEATUREATTRIBUTE).data[0] : i21.vertexAttributes.has(O3.SIZE) && (d11 = i21.vertexAttributes.get(O3.SIZE).data[0]);
    let E14 = [1, 1, 1, 1], A10 = 0;
    this._parameters.vvColorEnabled ? A10 = i21.vertexAttributes.get(O3.COLORFEATUREATTRIBUTE).data[0] : i21.vertexAttributes.has(O3.COLOR) && (E14 = i21.vertexAttributes.get(O3.COLOR).data);
    const v13 = has("enable-feature:objectAndLayerId-rendering") ? i21.objectAndLayerIdColor : null;
    let R9 = 0;
    this._parameters.vvOpacityEnabled && (R9 = i21.vertexAttributes.get(O3.OPACITYFEATUREATTRIBUTE).data[0]);
    const S7 = u12.length / 3, I9 = new Float32Array(s20.buffer), g7 = has("enable-feature:objectAndLayerId-rendering") ? new Uint8Array(s20.buffer) : null, O10 = this.vertexBufferLayout.stride / 4;
    let b9 = n28 * O10;
    const L5 = b9;
    let P9 = 0;
    const N7 = T8 ? (e17, t28, r28) => P9 = T8[r28] : (e17, t28, r28) => P9 += x(e17, t28), C8 = has("enable-feature:objectAndLayerId-rendering"), y9 = (e17, t28, i22, s21, a25, n29, o23) => {
      if (I9[b9++] = t28[0], I9[b9++] = t28[1], I9[b9++] = t28[2], I9[b9++] = s21, I9[b9++] = o23, I9[b9++] = a25, I9[b9++] = e17[0], I9[b9++] = e17[1], I9[b9++] = e17[2], I9[b9++] = i22[0], I9[b9++] = i22[1], I9[b9++] = i22[2], this._parameters.vvSizeEnabled ? I9[b9++] = _11 : I9[b9++] = d11, this._parameters.vvColorEnabled) I9[b9++] = A10;
      else {
        const e18 = Math.min(4 * n29, E14.length - 4);
        I9[b9++] = E14[e18], I9[b9++] = E14[e18 + 1], I9[b9++] = E14[e18 + 2], I9[b9++] = E14[e18 + 3];
      }
      this._parameters.vvOpacityEnabled && (I9[b9++] = R9), C8 && (r(v13) && (g7[4 * b9] = v13[0], g7[4 * b9 + 1] = v13[1], g7[4 * b9 + 2] = v13[2], g7[4 * b9 + 3] = v13[3]), b9++);
    };
    b9 += O10, o2(l22, u12[0], u12[1], u12[2]), e16 && O2(l22, l22, e16);
    const D5 = this._isClosed(i21);
    if (D5) {
      const t28 = u12.length - 3;
      o2(o22, u12[t28], u12[t28 + 1], u12[t28 + 2]), e16 && O2(o22, o22, e16);
    } else o2(c15, u12[3], u12[4], u12[5]), e16 && O2(c15, c15, e16), y9(l22, l22, c15, 1, B2.LEFT_CAP_START, 0, 0), y9(l22, l22, c15, 1, B2.RIGHT_CAP_START, 0, 0), r3(o22, l22), r3(l22, c15);
    const U6 = D5 ? 0 : 1, F6 = D5 ? S7 : S7 - 1;
    for (let r28 = U6; r28 < F6; r28++) {
      const t28 = (r28 + 1) % S7 * 3;
      o2(c15, u12[t28], u12[t28 + 1], u12[t28 + 2]), e16 && O2(c15, c15, e16), N7(o22, l22, r28), y9(o22, l22, c15, 0, B2.LEFT_JOIN_END, r28, P9), y9(o22, l22, c15, 0, B2.RIGHT_JOIN_END, r28, P9);
      const i22 = this.numJoinSubdivisions;
      for (let e17 = 0; e17 < i22; ++e17) {
        const t29 = (e17 + 1) / (i22 + 1);
        y9(o22, l22, c15, t29, B2.LEFT_JOIN_END, r28, P9), y9(o22, l22, c15, t29, B2.RIGHT_JOIN_END, r28, P9);
      }
      y9(o22, l22, c15, 1, B2.LEFT_JOIN_START, r28, P9), y9(o22, l22, c15, 1, B2.RIGHT_JOIN_START, r28, P9), r3(o22, l22), r3(l22, c15);
    }
    D5 ? (o2(c15, u12[3], u12[4], u12[5]), e16 && O2(c15, c15, e16), P9 = N7(o22, l22, F6), y9(o22, l22, c15, 0, B2.LEFT_JOIN_END, U6, P9), y9(o22, l22, c15, 0, B2.RIGHT_JOIN_END, U6, P9)) : (P9 = N7(o22, l22, F6), y9(o22, l22, l22, 0, B2.LEFT_CAP_END, F6, P9), y9(o22, l22, l22, 0, B2.RIGHT_CAP_END, F6, P9)), W2(I9, L5 + O10, I9, L5, O10);
    b9 = W2(I9, b9 - O10, I9, b9, O10), this._parameters.wireframe && this._addWireframeVertices(s20, L5, b9, O10);
  }
  _addWireframeVertices(e16, t27, r28, i21) {
    const s20 = new Float32Array(e16.buffer, r28 * Float32Array.BYTES_PER_ELEMENT), a25 = new Float32Array(e16.buffer, t27 * Float32Array.BYTES_PER_ELEMENT, r28 - t27);
    let n28 = 0;
    const o22 = (e17) => n28 = W2(a25, e17, s20, n28, i21);
    for (let l22 = 0; l22 < a25.length - 1; l22 += 2 * i21) o22(l22), o22(l22 + 2 * i21), o22(l22 + 1 * i21), o22(l22 + 2 * i21), o22(l22 + 1 * i21), o22(l22 + 3 * i21);
  }
};
function W2(e16, t27, r28, i21, s20) {
  for (let a25 = 0; a25 < s20; a25++) r28[i21++] = e16[t27++];
  return i21;
}
function V6(e16, t27, r28) {
  return Z3(e16, t27.get(O3.POSITION).data, r28 ? r28.get(O3.POSITION) : null);
}
function Z3(e16, t27, r28) {
  return !!e16.isClosed && (r28 ? r28.length > 2 : t27.length > 6);
}
function q2(e16) {
  return e16.anchor === s11.Tip && e16.hideOnShortSegments && "begin-end" === e16.placement && e16.worldSpace;
}
var X3 = n4();
var Y3 = n4();
var Q2 = n4();
var K = n4();
var $3 = n4();
var ee2 = x2();
var te = x2();
var re = n4();
var ie = n4();
var se = v4();
var ae = v4();
var ne = n4();
var oe = n4();
var le = n4();
var ce = [x2(), x2(), x2(), x2()];
var he = [n4(), n4(), n4(), n4()];
var ue = p2();
var pe = p2();
var me = p2();
var fe = p2();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/LocalOriginFactory.js
var t16 = class {
  constructor(s20, t27) {
    this.vec3 = s20, this.id = t27;
  }
};
function c9(c15, r28, n28, o22) {
  return new t16(r2(c15, r28, n28), o22);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/testUtils.js
var n16 = { rootOrigin: null };

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/GridLocalOriginFactory.js
var p8 = class {
  constructor(t27) {
    this._originSR = t27, this._origins = /* @__PURE__ */ new Map(), this._objects = /* @__PURE__ */ new Map(), this._gridSize = 5e5, this._rootOriginId = "root/" + e3();
  }
  getOrigin(i21) {
    const o22 = this._origins.get(this._rootOriginId);
    if (null == o22) {
      const r28 = n16.rootOrigin;
      if (r(r28)) return this._origins.set(this._rootOriginId, c9(r28[0], r28[1], r28[2], this._rootOriginId)), this.getOrigin(i21);
      const o23 = c9(i21[0] + Math.random() - 0.5, i21[1] + Math.random() - 0.5, i21[2] + Math.random() - 0.5, this._rootOriginId);
      return this._origins.set(this._rootOriginId, o23), o23;
    }
    const s20 = this._gridSize, e16 = Math.round(i21[0] / s20), n28 = Math.round(i21[1] / s20), a25 = Math.round(i21[2] / s20), h16 = `${e16}/${n28}/${a25}`;
    let c15 = this._origins.get(h16);
    const g7 = 0.5 * s20;
    if (e5(j6, i21, o22.vec3), j6[0] = Math.abs(j6[0]), j6[1] = Math.abs(j6[1]), j6[2] = Math.abs(j6[2]), j6[0] < g7 && j6[1] < g7 && j6[2] < g7) {
      if (c15) {
        const t27 = Math.max(...j6);
        e5(j6, i21, c15.vec3), j6[0] = Math.abs(j6[0]), j6[1] = Math.abs(j6[1]), j6[2] = Math.abs(j6[2]);
        if (Math.max(...j6) < t27) return c15;
      }
      return o22;
    }
    return c15 || (c15 = c9(e16 * s20, n28 * s20, a25 * s20, h16), this._origins.set(h16, c15)), c15;
  }
  _drawOriginBox(t27, i21 = r5(1, 1, 0, 1)) {
    const r28 = window.view, o22 = r28._stage, m9 = i21.toString();
    if (!this._objects.has(m9)) {
      this._material = new z5({ width: 2, color: i21 }), o22.add(this._material);
      const t28 = new l19({ pickable: false }), r29 = new x9({ castShadow: false });
      o22.add(r29), t28.add(r29), o22.add(t28), this._objects.set(m9, r29);
    }
    const d11 = this._objects.get(m9), p13 = [0, 1, 5, 4, 0, 2, 1, 7, 6, 2, 0, 1, 3, 7, 5, 4, 6, 2, 0], j12 = p13.length, b9 = new Array(3 * j12), u12 = new Array(), l22 = 0.5 * this._gridSize;
    for (let s20 = 0; s20 < j12; s20++) b9[3 * s20 + 0] = t27[0] + (1 & p13[s20] ? l22 : -l22), b9[3 * s20 + 1] = t27[1] + (2 & p13[s20] ? l22 : -l22), b9[3 * s20 + 2] = t27[2] + (4 & p13[s20] ? l22 : -l22), s20 > 0 && u12.push(s20 - 1, s20);
    xn(b9, this._originSR, 0, b9, r28.renderSpatialReference, 0, j12);
    const M9 = new v5(this._material, [[O3.POSITION, new s5(b9, 3, true)]], [[O3.POSITION, u12]], null, e8.Line);
    o22.add(M9), d11.addGeometry(M9);
  }
  get test() {
    const t27 = this;
    return { set gridSize(i21) {
      t27._gridSize = i21;
    } };
  }
};
var j6 = n4();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/IntersectorInterfaces.js
var i14;
var t17;
!function(i21) {
  i21[i21.OBJECT = 0] = "OBJECT", i21[i21.HUD = 1] = "HUD", i21[i21.TERRAIN = 2] = "TERRAIN", i21[i21.OVERLAY = 3] = "OVERLAY", i21[i21.I3S = 4] = "I3S", i21[i21.PCL = 5] = "PCL", i21[i21.LOD = 6] = "LOD", i21[i21.VOXEL = 7] = "VOXEL";
}(i14 || (i14 = {}));
var e13 = class {
  constructor() {
    this.verticalOffset = 0, this.selectionMode = false, this.hud = true, this.selectOpaqueTerrainOnly = true, this.invisibleTerrain = false, this.backfacesTerrain = true, this.isFiltered = false, this.filteredLayerUids = [], this.store = t17.ALL;
  }
};
!function(i21) {
  i21[i21.MIN = 0] = "MIN", i21[i21.MINMAX = 1] = "MINMAX", i21[i21.ALL = 2] = "ALL";
}(t17 || (t17 = {}));

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/IntersectorTarget.js
var r18 = class {
  constructor(s20, t27, r28) {
    this.object = s20, this.geometryId = t27, this.triangleNr = r28;
  }
};
var c10 = class extends r18 {
  constructor(r28, c15, e16, o22) {
    super(r28, c15, e16), this.center = r(o22) ? t3(o22) : null;
  }
};
var o16 = class {
  constructor(s20) {
    this.layerUid = s20;
  }
};
var i15 = class extends o16 {
  constructor(s20, t27) {
    super(s20), this.graphicUid = t27;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/intersectorUtils.js
function c11(t27) {
  return r(t27) && r(t27.dist);
}
var f12 = n4();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/Intersector.js
var A8 = 1e-5;
var T6 = class {
  constructor(t27) {
    this.options = new e13(), this._results = new I4(), this.transform = new u8(), this.tolerance = A8, this.verticalOffset = null, this._ray = d(), this._rayEnd = n4(), this._rayBeginTransformed = n4(), this._rayEndTransformed = n4(), this.viewingMode = t27 ?? l13.Global;
  }
  get results() {
    return this._results;
  }
  get ray() {
    return this._ray;
  }
  get rayBegin() {
    return this._ray.origin;
  }
  get rayEnd() {
    return this._rayEnd;
  }
  reset(t27, r28, s20) {
    this.resetWithRay(h4(t27, r28, this._ray), s20);
  }
  resetWithRay(t27, r28) {
    this.camera = r28, t27 !== this._ray && k2(t27, this._ray), 0 !== this.options.verticalOffset ? this.viewingMode === l13.Local ? this._ray.origin[2] -= this.options.verticalOffset : this.verticalOffset = this.options.verticalOffset : this.verticalOffset = null, u3(this._rayEnd, this._ray.origin, this._ray.direction), this._results.init(this._ray);
  }
  intersect(r28 = null, s20, i21, e16, a25) {
    this.point = s20, this.filterPredicate = e16, this.tolerance = i21 ?? A8;
    const n28 = y4(this.verticalOffset);
    if (r(r28) && r28.length > 0) {
      const s21 = a25 ? (t27) => {
        a25(t27) && this.intersectObject(t27);
      } : (t27) => {
        this.intersectObject(t27);
      };
      for (const i22 of r28) {
        const r29 = i22.getSpatialQueryAccelerator && i22.getSpatialQueryAccelerator();
        r(r29) ? (r(n28) ? r29.forEachAlongRayWithVerticalOffset(this._ray.origin, this._ray.direction, s21, n28) : r29.forEachAlongRay(this._ray.origin, this._ray.direction, s21), this.options.selectionMode && this.options.hud && r29.forEachDegenerateObject(s21)) : i22.objects.forAll((t27) => s21(t27));
      }
    }
    this.sortResults();
  }
  intersectObject(r28) {
    const s20 = r28.geometries;
    if (!s20) return;
    const i21 = r28.transformation, a25 = y4(this.verticalOffset);
    for (const o22 of s20) {
      if (!o22.visible) continue;
      const { material: s21, id: h16 } = o22;
      this.transform.setAndInvalidateLazyTransforms(i21, o22.shaderTransformation), O2(this._rayBeginTransformed, this.rayBegin, this.transform.inverse), O2(this._rayEndTransformed, this.rayEnd, this.transform.inverse);
      const d11 = this.transform.transform;
      r(a25) && (a25.objectTransform = this.transform), s21.intersect(o22, this.transform.transform, this, this._rayBeginTransformed, this._rayEndTransformed, (s22, i22, a26, n28, o23, c15) => {
        if (s22 >= 0) {
          if (r(this.filterPredicate) && !this.filterPredicate(this._ray.origin, this._rayEnd, s22)) return;
          const l22 = n28 ? this._results.hud : this._results, m9 = n28 ? (t27) => {
            const n29 = new c10(r28, h16, a26, c15);
            t27.set(i14.HUD, n29, s22, i22, o7, o23);
          } : (t27) => t27.set(i14.OBJECT, { object: r28, geometryId: h16, triangleNr: a26 }, s22, i22, d11, o23);
          if ((null == l22.min.drapedLayerOrder || o23 >= l22.min.drapedLayerOrder) && (null == l22.min.dist || s22 < l22.min.dist) && m9(l22.min), this.options.store !== t17.MIN && (null == l22.max.drapedLayerOrder || o23 < l22.max.drapedLayerOrder) && (null == l22.max.dist || s22 > l22.max.dist) && m9(l22.max), this.options.store === t17.ALL) if (n28) {
            const t27 = new N5(this._ray);
            m9(t27), this._results.hud.all.push(t27);
          } else {
            const t27 = new M7(this._ray);
            m9(t27), this._results.all.push(t27);
          }
        }
      });
    }
  }
  sortResults(t27 = this._results.all) {
    t27.sort((t28, s20) => t28.dist !== s20.dist ? l(t28.dist, 0) - l(s20.dist, 0) : t28.drapedLayerOrder !== s20.drapedLayerOrder ? l(t28.drapedLayerOrder, Number.MAX_VALUE) - l(s20.drapedLayerOrder, Number.MAX_VALUE) : l(s20.drapedLayerGraphicOrder, Number.MIN_VALUE) - l(t28.drapedLayerGraphicOrder, Number.MIN_VALUE));
  }
};
function x10(t27) {
  return new T6(t27);
}
var I4 = class {
  constructor() {
    this.min = new M7(d()), this.max = new M7(d()), this.hud = { min: new N5(d()), max: new N5(d()), all: new Array() }, this.ground = new M7(d()), this.all = [];
  }
  init(t27) {
    this.min.init(t27), this.max.init(t27), this.ground.init(t27), this.all.length = 0, this.hud.min.init(t27), this.hud.max.init(t27), this.hud.all.length = 0;
  }
};
var M7 = class {
  get ray() {
    return this._ray;
  }
  get distanceInRenderSpace() {
    return r(this.dist) ? (g(B3, this.ray.direction, this.dist), s2(B3)) : null;
  }
  getIntersectionPoint(t27) {
    return !!c11(this) && (g(B3, this.ray.direction, this.dist), u3(t27, this.ray.origin, B3), true);
  }
  getTransformedNormal(t27) {
    return r3(G4, this.normal), G4[3] = 0, w(G4, G4, this.transformation), r3(t27, G4), z2(t27, t27);
  }
  constructor(t27) {
    this.intersector = i14.OBJECT, this.normal = n4(), this.transformation = e7(), this._ray = d(), this.init(t27);
  }
  init(t27) {
    this.dist = null, this.target = null, this.drapedLayerOrder = null, this.drapedLayerGraphicOrder = null, this.intersector = i14.OBJECT, k2(t27, this._ray);
  }
  set(t27, i21, a25, n28, o22, h16, c15) {
    this.intersector = t27, this.dist = a25, r3(this.normal, l(n28, b)), n6(this.transformation, l(o22, o7)), this.target = i21, this.drapedLayerOrder = h16, this.drapedLayerGraphicOrder = c15;
  }
  copy(t27) {
    k2(t27.ray, this._ray), this.intersector = t27.intersector, this.dist = t27.dist, this.target = t27.target, this.drapedLayerOrder = t27.drapedLayerOrder, this.drapedLayerGraphicOrder = t27.drapedLayerGraphicOrder, r3(this.normal, t27.normal), n6(this.transformation, t27.transformation);
  }
};
var N5 = class extends M7 {
  constructor() {
    super(...arguments), this.intersector = i14.HUD;
  }
};
function U4(t27) {
  return new M7(t27);
}
var B3 = n4();
var G4 = n5();

// node_modules/@arcgis/core/views/3d/terrain/Overlay.js
var n17;
!function(e16) {
  e16[e16.None = 0] = "None", e16[e16.ColorAndWater = 1] = "ColorAndWater", e16[e16.Highlight = 2] = "Highlight", e16[e16.Occluded = 3] = "Occluded", e16[e16.ObjectAndLayerIdColor = 4] = "ObjectAndLayerIdColor";
}(n17 || (n17 = {}));
var h12 = class {
  get extent() {
    return this._extent;
  }
  constructor(t27, r28) {
    this.index = t27, this.renderTargets = r28, this._extent = u4(), this.resolution = 0, this.renderLocalOrigin = c9(0, 0, 0, "O"), this.pixelRatio = 1, this.mapUnitsPerPixel = 1, this.canvasGeometries = new g5(), this.hasDrapedFeatureSource = false, this.hasDrapedRasterSource = false, this.hasTargetWithoutRasterImage = false, this.index = t27, this.validTargets = new Array(r28.renderTargets.length).fill(false);
  }
  getValidTexture(e16) {
    return this.validTargets[e16] ? this.renderTargets.getTarget(e16).getTexture() : null;
  }
  get _needsColorWithoutRasterImage() {
    return this.hasDrapedRasterSource && this.hasDrapedFeatureSource && this.hasTargetWithoutRasterImage;
  }
  getColorTexture(e16) {
    const t27 = e16 === n17.ColorAndWater ? this.renderTargets.getTarget(H5.Color) : e16 === n17.Highlight ? this.renderTargets.getTarget(H5.Highlight) : e16 === n17.ObjectAndLayerIdColor ? this.renderTargets.getTarget(H5.ObjectAndLayerIdColor) : this.renderTargets.getTarget(H5.Occluded);
    return t27 ? t27.getTexture() : null;
  }
  getColorTextureNoRasterImage() {
    return this._needsColorWithoutRasterImage ? this.getValidTexture(H5.ColorNoRasterImage) : this.hasDrapedFeatureSource ? this.getValidTexture(H5.Color) : null;
  }
  getNormalTexture(e16) {
    const t27 = e16 === n17.ColorAndWater ? this.renderTargets.getTarget(H5.Water) : null;
    return t27 ? t27.getTexture() : null;
  }
  draw(e16, t27) {
    const r28 = this.computeRenderTargetValidityBitfield();
    for (const i21 of this.renderTargets.renderTargets) i21.type !== H5.ColorNoRasterImage || this._needsColorWithoutRasterImage ? this.validTargets[i21.type] = e16.drawTarget(this, i21, t27) : this.validTargets[i21.type] = false;
    return r28 ^ this.computeRenderTargetValidityBitfield() ? i5.CHANGED : i5.UNCHANGED;
  }
  computeRenderTargetValidityBitfield() {
    const e16 = this.validTargets;
    return +e16[H5.Color] | +e16[H5.ColorNoRasterImage] << 1 | +e16[H5.Highlight] << 2 | +e16[H5.Water] << 3 | +e16[H5.Occluded] << 4;
  }
  setupGeometryViewsCyclical(e16) {
    this.setupGeometryViewsDirect();
    const r28 = 1e-3 * e16.range;
    if (this._extent[0] - r28 <= e16.min) {
      const r29 = this.canvasGeometries.extents[this.canvasGeometries.numViews++];
      z3(this._extent, e16.range, 0, r29);
    }
    if (this._extent[2] + r28 >= e16.max) {
      const r29 = this.canvasGeometries.extents[this.canvasGeometries.numViews++];
      z3(this._extent, -e16.range, 0, r29);
    }
  }
  setupGeometryViewsDirect() {
    this.canvasGeometries.numViews = 1, a5(this.canvasGeometries.extents[0], this._extent);
  }
  hasSomeSizedView() {
    for (let e16 = 0; e16 < this.canvasGeometries.numViews; e16++) {
      const t27 = this.canvasGeometries.extents[e16];
      if (t27[0] !== t27[2] && t27[1] !== t27[3]) return true;
    }
    return false;
  }
  applyViewport(e16) {
    e16.setViewport(this.index === T3.INNER ? 0 : this.resolution, 0, this.resolution, this.resolution);
  }
};
var g5 = class {
  constructor() {
    this.extents = [u4(), u4(), u4()], this.numViews = 0;
  }
};

// node_modules/@arcgis/core/views/3d/terrain/OverlayFramebufferObject.js
var f13 = class {
  constructor(e16, f18) {
    this._size = n8(), this._fbo = null, this._fbo = new x6(e16, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE }, { target: M.TEXTURE_2D, pixelFormat: P3.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR_MIPMAP_LINEAR, hasMipmap: f18, maxAnisotropy: 8, width: 0, height: 0 });
  }
  dispose() {
    this._fbo = h(this._fbo);
  }
  getTexture() {
    return this._fbo ? this._fbo.colorTexture : null;
  }
  isValid() {
    return null !== this._fbo;
  }
  resize(e16, t27) {
    this._size[0] = e16, this._size[1] = t27, this._fbo.resize(this._size[0], this._size[1]);
  }
  bind(e16) {
    e16.bindFramebuffer(this._fbo);
  }
  generateMipMap() {
    const e16 = this._fbo.colorTexture;
    e16.descriptor.hasMipmap && e16.generateMipmap();
  }
  disposeRenderTargetMemory() {
    var _a;
    (_a = this._fbo) == null ? void 0 : _a.resize(0, 0);
  }
  get gpuMemoryUsage() {
    var _a;
    return ((_a = this._fbo) == null ? void 0 : _a.gpuMemoryUsage) ?? 0;
  }
};

// node_modules/@arcgis/core/views/3d/terrain/OverlayRenderTarget.js
var s15 = class {
  constructor(e16, t27, s20, o22 = true) {
    this.output = t27, this.type = s20, this.valid = false, this.lastUsed = 1 / 0, this.fbo = new f13(e16, o22);
  }
};
var o17 = class {
  constructor(r28) {
    this.renderTargets = [new s15(r28, h6.Color, H5.Color), new s15(r28, h6.Color, H5.ColorNoRasterImage), new s15(r28, h6.Highlight, H5.Highlight, false), new s15(r28, h6.Normal, H5.Water), new s15(r28, h6.Color, H5.Occluded)], has("enable-feature:objectAndLayerId-rendering") && this.renderTargets.push(new s15(r28, h6.ObjectAndLayerIdColor, H5.ObjectAndLayerIdColor));
  }
  getTarget(e16) {
    return this.renderTargets[e16].fbo;
  }
  dispose() {
    for (const e16 of this.renderTargets) e16.fbo.dispose();
  }
  disposeRenderTargetMemory() {
    for (const e16 of this.renderTargets) e16.fbo.disposeRenderTargetMemory();
  }
  validateUsageForTarget(e16, r28, t27) {
    if (e16) r28.lastUsed = t27;
    else if (t27 - r28.lastUsed > a18) r28.fbo.disposeRenderTargetMemory(), r28.lastUsed = 1 / 0;
    else if (r28.lastUsed < 1 / 0) return true;
    return false;
  }
  get gpuMemoryUsage() {
    return this.renderTargets.reduce((e16, r28) => e16 + r28.fbo.gpuMemoryUsage, 0);
  }
};
var a18 = 1e3;

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderTechnique/ShaderTechniqueRepository.js
var n18 = class {
  constructor(e16) {
    this._context = e16, this._perConstructorInstances = new t7(), this._frameCounter = 0, this._keepAliveFrameCount = c12;
  }
  get viewingMode() {
    return this._context.viewingMode;
  }
  get constructionContext() {
    return this._context;
  }
  destroy() {
    this._perConstructorInstances.forEach((e16) => e16.forEach((e17) => e17.technique.destroy())), this._perConstructorInstances.clear();
  }
  acquire(t27, r28 = i16) {
    const s20 = r28.key;
    let n28 = this._perConstructorInstances.get(t27, s20);
    if (t(n28)) {
      const e16 = new t27(this._context, r28, () => this.release(e16));
      n28 = new o18(e16), this._perConstructorInstances.set(t27, s20, n28);
    }
    return ++n28.refCount, n28.technique;
  }
  releaseAndAcquire(e16, r28, s20) {
    if (r(s20)) {
      if (r28.key === s20.key) return s20;
      this.release(s20);
    }
    return this.acquire(e16, r28);
  }
  release(t27) {
    if (t(t27) || this._perConstructorInstances.empty) return;
    const r28 = this._perConstructorInstances.get(t27.constructor, t27.key);
    t(r28) || (--r28.refCount, 0 === r28.refCount && (r28.refZeroFrame = this._frameCounter));
  }
  frameUpdate() {
    this._frameCounter++, this._keepAliveFrameCount !== c12 && this._perConstructorInstances.forEach((e16, t27) => {
      e16.forEach((e17, r28) => {
        0 === e17.refCount && e17.refZeroFrame + this._keepAliveFrameCount < this._frameCounter && (e17.technique.destroy(), this._perConstructorInstances.delete(t27, r28));
      });
    });
  }
  async reloadAll() {
    const e16 = new Array();
    this._perConstructorInstances.forEach((t27, r28) => {
      const s20 = async (e17, t28) => {
        const r29 = t28.shader;
        r29 && (await r29.reload(), e17.forEach((e18) => e18.technique.reload(this._context)));
      };
      e16.push(s20(t27, r28));
    }), await Promise.all(e16);
  }
};
var o18 = class {
  constructor(e16) {
    this.technique = e16, this.refCount = 0, this.refZeroFrame = 0;
  }
};
var c12 = -1;
var i16 = new t10();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/GLMaterialRepository.js
var a19 = class {
  constructor(e16, t27, r28, i21) {
    this._textureRepository = e16, this._techniqueRepository = t27, this.materialChanged = r28, this.requestRender = i21, this._id2glMaterialRef = new t7();
  }
  dispose() {
    this._textureRepository.destroy();
  }
  acquire(e16, r28, i21) {
    if (this._ownMaterial(e16), !e16.requiresSlot(r28, i21)) return null;
    let s20 = this._id2glMaterialRef.get(i21, e16.id);
    if (t(s20)) {
      const t27 = e16.createGLMaterial({ material: e16, techniqueRep: this._techniqueRepository, textureRep: this._textureRepository, output: i21 });
      s20 = new l20(t27), this._id2glMaterialRef.set(i21, e16.id, s20);
    }
    return s20.ref(), s20.glMaterial;
  }
  release(e16, t27) {
    const s20 = this._id2glMaterialRef.get(t27, e16.id);
    r(s20) && (s20.unref(), s20.referenced || (h(s20.glMaterial), this._id2glMaterialRef.delete(t27, e16.id)));
  }
  _ownMaterial(t27) {
    r(t27.repository) && t27.repository !== this && s.getLogger("esri.views.3d.webgl-engine.lib.GLMaterialRepository").error("Material is already owned by a different material repository"), t27.repository = this;
  }
};
var l20 = class {
  constructor(e16) {
    this.glMaterial = e16, this._refCnt = 0;
  }
  ref() {
    ++this._refCnt;
  }
  unref() {
    --this._refCnt, s6(this._refCnt >= 0);
  }
  get referenced() {
    return this._refCnt > 0;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/BindParameters.js
var o19 = class {
  constructor(o22, l22, m9) {
    this.shadowMap = o22, this.ssaoHelper = l22, this.slicePlane = m9, this.slot = E8.OPAQUE_MATERIAL, this.hasOccludees = false, this.enableFillLights = true, this.transparencyPassType = o11.NONE, this._camera = new $2(), this._inverseViewport = n12(), this.oldLighting = new L2(), this.newLighting = new L2(), this._fadedLighting = new L2(), this._lighting = this.newLighting, this.ssr = new c6(), this.multipassTerrain = new o14(), this.multipassGeometry = new s13(), this.overlays = [], this.cloudsFade = new i11();
  }
  get camera() {
    return this._camera;
  }
  set camera(i21) {
    this._camera = this.ssr.camera = i21, this._inverseViewport[0] = 1 / i21.fullViewport[2], this._inverseViewport[1] = 1 / i21.fullViewport[3];
  }
  get inverseViewport() {
    return this._inverseViewport;
  }
  get lighting() {
    return this._lighting;
  }
  get weatherFading() {
    return this._lighting === this._fadedLighting;
  }
  fadeLighting(i21) {
    const { oldLighting: t27, newLighting: s20 } = this;
    i21 >= 1 ? this._lighting = s20 : (this._fadedLighting.lerpLighting(t27, s20, i21), this._lighting = this._fadedLighting);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/RenderContext.js
var i17 = class {
  constructor(r28, a25, i21, o22 = null) {
    this.rctx = r28, this.sliceHelper = o22, this.lastFrameCamera = new $2(), this.output = h6.Color, this.renderOccludedMask = n20, this.bindParameters = new o19(a25, i21, r(o22) ? o22.plane : null);
  }
  resetRenderOccludedMask() {
    this.renderOccludedMask = n20;
  }
};
var n20 = c4.Occlude | c4.OccludeAndTransparent | c4.OccludeAndTransparentStencil;

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/CascadeCamera.js
var p9 = class extends $2 {
  constructor() {
    super(...arguments), this._projectionMatrix = e7();
  }
  get projectionMatrix() {
    return this._projectionMatrix;
  }
};
e([y3()], p9.prototype, "_projectionMatrix", void 0), e([y3({ readOnly: true })], p9.prototype, "projectionMatrix", null), p9 = e([a2("esri.views.3d.webgl-engine.lib.CascadeCamera")], p9);

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/ShadowMap.js
var W3;
!function(t27) {
  t27[t27.Highlight = 0] = "Highlight", t27[t27.Default = 1] = "Default";
}(W3 || (W3 = {}));
var J2 = class {
  constructor() {
    this.camera = new p9(), this.lightMat = e7();
  }
};
var K2 = class {
  get depthTexture() {
    return this._depthTexture;
  }
  get textureSize() {
    return this._textureSize;
  }
  get numCascades() {
    return this._numCascades;
  }
  get cascadeDistances() {
    return r4(this._usedCascadeDistances, this._cascadeDistances[0], this._numCascades > 1 ? this._cascadeDistances[1] : 1 / 0, this._numCascades > 2 ? this._cascadeDistances[2] : 1 / 0, this._numCascades > 3 ? this._cascadeDistances[3] : 1 / 0);
  }
  constructor(t27, e16) {
    this._rctx = t27, this._viewingMode = e16, this._enabled = false, this._snapshots = new Array(), this._textureSize = 0, this._numCascades = 1, this._maxNumCascades = 4, this._projectionView = e7(), this._projectionViewInverse = e7(), this._modelViewLight = e7(), this._splitSchemeLambda = 0, this._cascadeDistances = [0, 0, 0, 0, 0], this._usedCascadeDistances = n5(), this._cascades = [new J2(), new J2(), new J2(), new J2()], this._lastOrigin = null, this._maxTextureSize = Math.min(has("esri-mobile") ? 2048 : 8192, this._rctx.parameters.maxTextureSize);
  }
  dispose() {
    this.enabled = false, this.disposeOffscreenBuffers();
  }
  disposeOffscreenBuffers() {
    this._discardDepthTexture(), this._discardAllSnapshots();
  }
  set maxCascades(e16) {
    this._maxNumCascades = a4(Math.floor(e16), 1, 4);
  }
  get maxCascades() {
    return this._maxNumCascades;
  }
  set enabled(t27) {
    this._enabled = t27, t27 || (this._discardDepthTexture(), this._discardAllSnapshots());
  }
  get enabled() {
    return this._enabled;
  }
  get ready() {
    return this._enabled && r(this._depthTexture);
  }
  getSnapshot(t27) {
    return this.enabled ? this._snapshots[t27] : null;
  }
  get cascades() {
    for (let t27 = 0; t27 < this._numCascades; ++t27) ht2[t27] = this._cascades[t27];
    return ht2.length = this._numCascades, ht2;
  }
  start(t27, e16, s20) {
    s6(this.enabled), this._textureSize = this._computeTextureSize(t27.fullWidth, t27.fullHeight), this._ensureDepthTexture();
    const { near: a25, far: i21 } = this._clampNearFar(s20);
    this._computeCascadeDistances(i21, a25), this._setupMatrices(t27, e16);
    const { viewMatrix: r28, projectionMatrix: h16 } = t27;
    for (let c15 = 0; c15 < this._numCascades; ++c15) this._constructCascade(c15, h16, r28, e16);
    this._lastOrigin = null, this.clear();
  }
  finish(t27) {
    s6(this.enabled), this._rctx.bindFramebuffer(t27);
  }
  getShadowMapMatrices(t27) {
    if (!this._lastOrigin || !F(t27, this._lastOrigin)) {
      this._lastOrigin = this._lastOrigin || n4(), r3(this._lastOrigin, t27);
      for (let e16 = 0; e16 < this._numCascades; ++e16) {
        i2(ct, this._cascades[e16].lightMat, t27);
        for (let t28 = 0; t28 < 16; ++t28) nt[16 * e16 + t28] = ct[t28];
      }
    }
    return nt;
  }
  takeCascadeSnapshotTo(t27, e16) {
    s6(this.enabled);
    const s20 = this._ensureSnapshot(e16);
    this._bindFbo();
    const a25 = this._rctx, i21 = a25.bindTexture(s20, E4.TEXTURE_UNIT_FOR_UPDATES);
    a25.gl.copyTexSubImage2D(M.TEXTURE_2D, 0, t27.camera.viewport[0], t27.camera.viewport[1], t27.camera.viewport[0], t27.camera.viewport[1], t27.camera.viewport[2], t27.camera.viewport[3]), a25.bindTexture(i21, E4.TEXTURE_UNIT_FOR_UPDATES);
  }
  clear() {
    const t27 = this._rctx;
    this._bindFbo(), t27.setClearColor(1, 1, 1, 1), t27.clearSafe(_4.COLOR_BUFFER_BIT | _4.DEPTH_BUFFER_BIT);
  }
  _computeTextureSize(t27, e16) {
    const s20 = 0.5 * Math.log(t27 * t27 + e16 * e16) * Math.LOG2E, a25 = 0.35, i21 = 2 ** Math.round(s20 + a25);
    return Math.min(this._maxTextureSize, 2 * i21);
  }
  _ensureDepthTexture() {
    if (r(this._depthTexture) && this._depthTexture.descriptor.width === this._textureSize) return;
    this._discardDepthTexture();
    const t27 = { target: M.TEXTURE_2D, pixelFormat: P3.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.NEAREST, flipped: true, width: this._textureSize, height: this._textureSize };
    this._depthTexture = new E4(this._rctx, t27), this._fbo = new x6(this._rctx, { colorTarget: Y.TEXTURE, depthStencilTarget: V.DEPTH_RENDER_BUFFER, width: this._textureSize, height: this._textureSize }, this._depthTexture);
  }
  _ensureSnapshot(t27) {
    let e16 = this._snapshots[t27];
    if (r(e16) && e16.descriptor.width === this._textureSize) return e16;
    this._discardSnapshot(t27);
    const s20 = { target: M.TEXTURE_2D, pixelFormat: P3.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.NEAREST, flipped: true, width: this._textureSize, height: this._textureSize };
    return e16 = new E4(this._rctx, s20), this._snapshots[t27] = e16, e16;
  }
  _discardDepthTexture() {
    this._fbo = h(this._fbo), this._depthTexture = h(this._depthTexture);
  }
  _discardSnapshot(t27) {
    this._snapshots[t27] = h(this._snapshots[t27]);
  }
  _discardAllSnapshots() {
    for (let t27 = 0; t27 < this._snapshots.length; ++t27) this._discardSnapshot(t27);
    this._snapshots.length = 0;
  }
  _bindFbo() {
    const t27 = this._rctx;
    t27.unbindTexture(this._depthTexture), t27.bindFramebuffer(this._fbo);
  }
  _constructCascade(t27, e16, s20, a25) {
    const i21 = this._cascades[t27], r28 = -this._cascadeDistances[t27], n28 = -this._cascadeDistances[t27 + 1], o22 = (e16[10] * r28 + e16[14]) / Math.abs(e16[11] * r28 + e16[15]), _11 = (e16[10] * n28 + e16[14]) / Math.abs(e16[11] * n28 + e16[15]);
    s6(o22 < _11);
    for (let h16 = 0; h16 < 8; ++h16) {
      r4(Z4, h16 % 4 == 0 || h16 % 4 == 3 ? -1 : 1, h16 % 4 == 0 || h16 % 4 == 1 ? -1 : 1, h16 < 4 ? o22 : _11, 1);
      const t28 = $4[h16];
      w(t28, Z4, this._projectionViewInverse), t28[0] /= t28[3], t28[1] /= t28[3], t28[2] /= t28[3];
    }
    j(rt2, $4[0]), i21.camera.viewMatrix = i2(Q3, this._modelViewLight, rt2);
    for (let h16 = 0; h16 < 8; ++h16) O2($4[h16], $4[h16], i21.camera.viewMatrix);
    let d11 = $4[0][2], u12 = $4[0][2];
    for (let h16 = 1; h16 < 8; ++h16) d11 = Math.min(d11, $4[h16][2]), u12 = Math.max(u12, $4[h16][2]);
    d11 -= 200, u12 += 200, i21.camera.near = -u12, i21.camera.far = -d11, Et(s20, a25, d11, u12, i21.camera), c2(i21.lightMat, i21.camera.projectionMatrix, i21.camera.viewMatrix);
    const m9 = this._textureSize / 2;
    i21.camera.viewport = [t27 % 2 == 0 ? 0 : m9, 0 === Math.floor(t27 / 2) ? 0 : m9, m9, m9];
  }
  _setupMatrices(t27, e16) {
    c2(this._projectionView, t27.projectionMatrix, t27.viewMatrix), h3(this._projectionViewInverse, this._projectionView);
    const s20 = this._viewingMode === l13.Global ? t27.eye : o2(rt2, 0, 0, 1);
    k(this._modelViewLight, [0, 0, 0], [-e16[0], -e16[1], -e16[2]], s20);
  }
  _clampNearFar(t27) {
    let { near: e16, far: s20 } = t27;
    return e16 < 2 && (e16 = 2), s20 < 2 && (s20 = 2), e16 >= s20 && (e16 = 2, s20 = 4), { near: e16, far: s20 };
  }
  _computeCascadeDistances(t27, s20) {
    this._numCascades = Math.min(1 + Math.floor(a10(t27 / s20, 4)), this._maxNumCascades);
    const a25 = (t27 - s20) / this._numCascades, i21 = (t27 / s20) ** (1 / this._numCascades);
    let r28 = s20, h16 = s20;
    for (let c15 = 0; c15 < this._numCascades + 1; ++c15) this._cascadeDistances[c15] = h2(r28, h16, this._splitSchemeLambda), r28 *= i21, h16 += a25;
  }
  get gpuMemoryUsage() {
    var _a;
    return this._snapshots.reduce((t27, e16) => t27 + u6(e16), ((_a = this._fbo) == null ? void 0 : _a.gpuMemoryUsage) ?? 0);
  }
  get test() {
    const t27 = this;
    return { maxNumCascades: this._maxNumCascades, cascades: this._cascades, textureSize: this._textureSize, set splitSchemeLambda(e16) {
      t27._splitSchemeLambda = e16;
    }, get splitSchemeLambda() {
      return t27._splitSchemeLambda;
    } };
  }
};
var Q3 = e7();
var Z4 = n5();
var $4 = [];
for (let jt = 0; jt < 8; ++jt) $4.push(n5());
var tt2 = n12();
var et2 = n12();
var st2 = n12();
var at = n12();
var it2 = n12();
var rt2 = n4();
var ht2 = [];
var ct = e7();
var nt = new Float32Array(64);
var ot = n12();
var _t = n12();
var dt = [n12(), n12(), n12(), n12()];
var ut = n12();
var mt = n12();
var lt = n12();
var pt = n12();
var ft = n12();
var xt = n12();
var gt = n12();
function Tt(t27, e16, s20, a25, i21, r28, h16, c15) {
  r6(ot, 0, 0);
  for (let d11 = 0; d11 < 4; ++d11) s4(ot, ot, t27[d11]);
  l10(ot, ot, 0.25), r6(_t, 0, 0);
  for (let d11 = 4; d11 < 8; ++d11) s4(_t, _t, t27[d11]);
  l10(_t, _t, 0.25), A2(dt[0], t27[4], t27[5], 0.5), A2(dt[1], t27[5], t27[6], 0.5), A2(dt[2], t27[6], t27[7], 0.5), A2(dt[3], t27[7], t27[4], 0.5);
  let n28 = 0, o22 = b3(dt[0], ot);
  for (let d11 = 1; d11 < 4; ++d11) {
    const t28 = b3(dt[d11], ot);
    t28 < o22 && (o22 = t28, n28 = d11);
  }
  o4(ut, dt[n28], t27[n28 + 4]);
  const _11 = ut[0];
  let w4, S7;
  ut[0] = -ut[1], ut[1] = _11, o4(mt, _t, ot), j2(mt, ut) < 0 && g2(ut, ut), A2(ut, ut, mt, s20), v3(ut, ut), w4 = S7 = j2(o4(lt, t27[0], ot), ut);
  for (let d11 = 1; d11 < 8; ++d11) {
    const e17 = j2(o4(lt, t27[d11], ot), ut);
    e17 < w4 ? w4 = e17 : e17 > S7 && (S7 = e17);
  }
  a7(a25, ot), l10(lt, ut, w4 - e16), s4(a25, a25, lt);
  let M9 = -1, C8 = 1, D5 = 0, E14 = 0;
  for (let d11 = 0; d11 < 8; ++d11) {
    o4(pt, t27[d11], a25), v3(pt, pt);
    const e17 = ut[0] * pt[1] - ut[1] * pt[0];
    e17 > 0 ? e17 > M9 && (M9 = e17, D5 = d11) : e17 < C8 && (C8 = e17, E14 = d11);
  }
  c3(M9 > 0, "leftArea"), c3(C8 < 0, "rightArea"), l10(ft, ut, w4), s4(ft, ft, ot), l10(xt, ut, S7), s4(xt, xt, ot), gt[0] = -ut[1], gt[1] = ut[0];
  const j12 = f4(a25, t27[E14], xt, s4(lt, xt, gt), 1, i21), v13 = f4(a25, t27[D5], xt, lt, 1, r28), R9 = f4(a25, t27[D5], ft, s4(lt, ft, gt), 1, h16), z9 = f4(a25, t27[E14], ft, lt, 1, c15);
  c3(j12, "rayRay"), c3(v13, "rayRay"), c3(R9, "rayRay"), c3(z9, "rayRay");
}
function bt(t27, e16) {
  return 3 * e16 + t27;
}
var wt = n12();
function St(t27, e16) {
  return r6(wt, t27[e16], t27[e16 + 3]), wt;
}
var Mt = n12();
var Ct = e6();
function Dt(t27, e16, s20, a25, i21) {
  o4(Mt, s20, a25), l10(Mt, Mt, 0.5), Ct[0] = Mt[0], Ct[1] = Mt[1], Ct[2] = 0, Ct[3] = Mt[1], Ct[4] = -Mt[0], Ct[5] = 0, Ct[6] = Mt[0] * Mt[0] + Mt[1] * Mt[1], Ct[7] = Mt[0] * Mt[1] - Mt[1] * Mt[0], Ct[8] = 1, Ct[bt(0, 2)] = -j2(St(Ct, 0), t27), Ct[bt(1, 2)] = -j2(St(Ct, 1), t27);
  let r28 = j2(St(Ct, 0), s20) + Ct[bt(0, 2)], h16 = j2(St(Ct, 1), s20) + Ct[bt(1, 2)], c15 = j2(St(Ct, 0), a25) + Ct[bt(0, 2)], n28 = j2(St(Ct, 1), a25) + Ct[bt(1, 2)];
  r28 = -(r28 + c15) / (h16 + n28), Ct[bt(0, 0)] += Ct[bt(1, 0)] * r28, Ct[bt(0, 1)] += Ct[bt(1, 1)] * r28, Ct[bt(0, 2)] += Ct[bt(1, 2)] * r28, r28 = 1 / (j2(St(Ct, 0), s20) + Ct[bt(0, 2)]), h16 = 1 / (j2(St(Ct, 1), s20) + Ct[bt(1, 2)]), Ct[bt(0, 0)] *= r28, Ct[bt(0, 1)] *= r28, Ct[bt(0, 2)] *= r28, Ct[bt(1, 0)] *= h16, Ct[bt(1, 1)] *= h16, Ct[bt(1, 2)] *= h16, Ct[bt(2, 0)] = Ct[bt(1, 0)], Ct[bt(2, 1)] = Ct[bt(1, 1)], Ct[bt(2, 2)] = Ct[bt(1, 2)], Ct[bt(1, 2)] += 1, r28 = j2(St(Ct, 1), e16) + Ct[bt(1, 2)], h16 = j2(St(Ct, 2), e16) + Ct[bt(2, 2)], c15 = j2(St(Ct, 1), s20) + Ct[bt(1, 2)], n28 = j2(St(Ct, 2), s20) + Ct[bt(2, 2)], r28 = -0.5 * (r28 / h16 + c15 / n28), Ct[bt(1, 0)] += Ct[bt(2, 0)] * r28, Ct[bt(1, 1)] += Ct[bt(2, 1)] * r28, Ct[bt(1, 2)] += Ct[bt(2, 2)] * r28, r28 = j2(St(Ct, 1), e16) + Ct[bt(1, 2)], h16 = j2(St(Ct, 2), e16) + Ct[bt(2, 2)], c15 = -h16 / r28, Ct[bt(1, 0)] *= c15, Ct[bt(1, 1)] *= c15, Ct[bt(1, 2)] *= c15, i21[0] = Ct[0], i21[1] = Ct[1], i21[2] = 0, i21[3] = Ct[2], i21[4] = Ct[3], i21[5] = Ct[4], i21[6] = 0, i21[7] = Ct[5], i21[8] = 0, i21[9] = 0, i21[10] = 1, i21[11] = 0, i21[12] = Ct[6], i21[13] = Ct[7], i21[14] = 0, i21[15] = Ct[8];
}
function Et(t27, e16, a25, i21, r28) {
  const h16 = 1 / $4[0][3], c15 = 1 / $4[4][3];
  s6(h16 < c15);
  let n28 = h16 + Math.sqrt(h16 * c15);
  const o22 = Math.sin(l5(t27[2] * e16[0] + t27[6] * e16[1] + t27[10] * e16[2]));
  n28 /= o22, Tt($4, n28, o22, tt2, et2, st2, at, it2), Dt(tt2, et2, at, it2, r28.projectionMatrix), r28.projectionMatrix[10] = 2 / (a25 - i21), r28.projectionMatrix[14] = -(a25 + i21) / (a25 - i21);
}

// node_modules/@arcgis/core/views/3d/terrain/Intersector.js
var i18 = class extends i15 {
  constructor(e16, r28, t27) {
    super(e16, r28), this.triangleNr = t27;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/ChangeSet.js
var s16 = class {
  constructor() {
    this.adds = new l2(), this.removes = new l2(), this.updates = new l2({ allocator: (e16) => e16 || new r19(), deallocator: (e16) => (e16.renderGeometry = null, e16) });
  }
  clear() {
    this.adds.clear(), this.removes.clear(), this.updates.clear();
  }
  prune() {
    this.adds.prune(), this.removes.prune(), this.updates.prune();
  }
  get empty() {
    return 0 === this.adds.length && 0 === this.removes.length && 0 === this.updates.length;
  }
};
var r19 = class {
};
var t18 = class {
  constructor() {
    this.adds = new Array(), this.removes = new Array(), this.updates = new Array();
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/ModelDirtyTypes.js
var E11;
var I5;
!function(E14) {
  E14[E14.ADD = 0] = "ADD", E14[E14.UPDATE = 1] = "UPDATE", E14[E14.REMOVE = 2] = "REMOVE";
}(E11 || (E11 = {})), function(E14) {
  E14[E14.NONE = 0] = "NONE", E14[E14.VISIBILITY = 1] = "VISIBILITY", E14[E14.GEOMETRY = 2] = "GEOMETRY", E14[E14.TRANSFORMATION = 4] = "TRANSFORMATION", E14[E14.HIGHLIGHT = 8] = "HIGHLIGHT", E14[E14.OCCLUDEE = 16] = "OCCLUDEE";
}(I5 || (I5 = {}));

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/rendererUtils.js
function r20(r28) {
  const n28 = /* @__PURE__ */ new Map(), o22 = (r29) => {
    let t27 = n28.get(r29);
    return t27 || (t27 = new t18(), n28.set(r29, t27)), t27;
  };
  return r28.removes.forAll((e16) => {
    t19(e16) && o22(e16.material).removes.push(e16);
  }), r28.adds.forAll((e16) => {
    t19(e16) && o22(e16.material).adds.push(e16);
  }), r28.updates.forAll((e16) => {
    t19(e16.renderGeometry) && o22(e16.renderGeometry.material).updates.push(e16);
  }), n28;
}
function t19(e16) {
  return e16.geometry.indexCount >= 1;
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/AppleAmdDriverHelper.js
var n21 = class {
  constructor(r28) {
    this._rctx = r28, this._indexBuffer = this._createIndexbuffer(), this._program = this._createProgram();
  }
  _createProgram() {
    const r28 = "\n    void main(void) {\n      gl_Position = vec4(0.0, 0.0, float(gl_VertexID)-2.0, 1.0);\n    }", e16 = "\n    void main(void) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n    }";
    return this._rctx.programCache.acquire(r28, e16, /* @__PURE__ */ new Map([]));
  }
  _createIndexbuffer() {
    return E6.createIndex(this._rctx, F2.STATIC_DRAW, new Uint32Array([0]));
  }
  resetIndicesType() {
    this._program.compiled && this._indexBuffer && (this._rctx.bindVAO(null), this._rctx.useProgram(this._program), this._rctx.bindBuffer(this._indexBuffer, A3.ELEMENT_ARRAY_BUFFER), this._rctx.drawElements(E3.POINTS, 1, C2.UNSIGNED_INT, 0));
  }
  dispose() {
    this._program.dispose(), this._indexBuffer.dispose();
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/GLMaterials.js
var e14 = class {
  constructor(r28, t27) {
    this._material = r28, this._repository = t27, this._map = /* @__PURE__ */ new Map();
  }
  destroy() {
    this._map.forEach((t27, e16) => {
      r(t27) && this._repository.release(this._material, e16);
    });
  }
  load(e16, s20, i21) {
    if (!this._material.requiresSlot(s20, i21)) return null;
    this._map.has(i21) || this._map.set(i21, this._repository.acquire(this._material, s20, i21));
    const a25 = this._map.get(i21);
    if (r(a25)) {
      if (a25.ensureResources(e16) === N2.LOADED) return a25;
      this._repository.requestRender();
    }
    return null;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/DrawParameters.js
var i19 = class extends v6 {
  constructor(s20 = n4()) {
    super(), this.origin = s20, this.slicePlaneLocalOrigin = this.origin;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/DefaultLayouts.js
var I6 = T2().vec3f(O3.POSITION);
var c13 = T2().vec3f(O3.POSITION).vec2f(O3.UV0);
var v10 = T2().vec3f(O3.POSITION).vec4u8(O3.COLOR);
var f14 = T2().vec3f(O3.POSITION).vec4u8(O3.OBJECTANDLAYERIDCOLOR);
var t20 = T2().vec3f(O3.POSITION).vec2f(O3.UV0).vec4u8(O3.OBJECTANDLAYERIDCOLOR);
var r21 = T2().vec3f(O3.POSITION).vec4u8(O3.COLOR).vec4u8(O3.OBJECTANDLAYERIDCOLOR);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/TriangleMaterial.js
var e15 = class extends h8 {
  intersect(r28, e16, i21, a25, l22, o22) {
    return x7(r28, i21, a25, l22, void 0, o22);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/WaterTechnique.js
var P7 = class _P extends e9 {
  initializeConfiguration(e16, o22) {
    o22.hasWebGL2Context = e16.rctx.type === r12.WEBGL2, o22.spherical = e16.viewingMode === l13.Global, o22.doublePrecisionRequiresObfuscation = e16.rctx.driverTest.doublePrecisionRequiresObfuscation.result;
  }
  initializeProgram(e16) {
    return new o12(e16.rctx, _P.shader.get().build(this.configuration), E5);
  }
  _setPipelineState(e16) {
    const t27 = this.configuration, r28 = e16 === o11.NONE, i21 = e16 === o11.FrontFace;
    return W({ blending: t27.output !== h6.Normal && t27.output !== h6.Highlight && t27.transparent ? r28 ? c5 : A4(e16) : null, depthTest: { func: l16(e16) }, depthWrite: r28 ? t27.writeDepth ? a11 : null : E7(e16), colorWrite: _6, polygonOffset: r28 || i21 ? null : a12(t27.enableOffset) });
  }
  initializePipeline() {
    return this._setPipelineState(this.configuration.transparencyPassType);
  }
};
P7.shader = new t9(_8, () => import("./WaterSurface.glsl-RGKQJKUP.js"));
var O8 = class extends s9 {
  constructor() {
    super(...arguments), this.output = h6.Color, this.transparencyPassType = o11.NONE, this.spherical = false, this.receiveShadows = false, this.hasSlicePlane = false, this.transparent = false, this.enableOffset = true, this.writeDepth = false, this.hasScreenSpaceReflections = false, this.doublePrecisionRequiresObfuscation = false, this.hasCloudsReflections = false, this.isDraped = false, this.hasMultipassTerrain = false, this.cullAboveGround = false;
  }
};
e([r15({ count: h6.COUNT })], O8.prototype, "output", void 0), e([r15({ count: o11.COUNT })], O8.prototype, "transparencyPassType", void 0), e([r15()], O8.prototype, "spherical", void 0), e([r15()], O8.prototype, "receiveShadows", void 0), e([r15()], O8.prototype, "hasSlicePlane", void 0), e([r15()], O8.prototype, "transparent", void 0), e([r15()], O8.prototype, "enableOffset", void 0), e([r15()], O8.prototype, "writeDepth", void 0), e([r15()], O8.prototype, "hasScreenSpaceReflections", void 0), e([r15()], O8.prototype, "doublePrecisionRequiresObfuscation", void 0), e([r15()], O8.prototype, "hasCloudsReflections", void 0), e([r15()], O8.prototype, "isDraped", void 0), e([r15()], O8.prototype, "hasMultipassTerrain", void 0), e([r15()], O8.prototype, "cullAboveGround", void 0), e([r15({ constValue: d3.Water })], O8.prototype, "pbrMode", void 0), e([r15({ constValue: true })], O8.prototype, "useCustomDTRExponentForWater", void 0), e([r15({ constValue: true })], O8.prototype, "highStepCount", void 0), e([r15({ constValue: false })], O8.prototype, "useFillLights", void 0);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/WaterGLMaterial.js
var r22 = class extends t8 {
  _updateShadowState(e16) {
    e16.shadowMap.enabled !== this._material.parameters.receiveShadows && this._material.setParameters({ receiveShadows: e16.shadowMap.enabled });
  }
  _updateSSRState(e16) {
    e16.ssr.enabled !== this._material.parameters.hasScreenSpaceReflections && this._material.setParameters({ hasScreenSpaceReflections: e16.ssr.enabled });
  }
  _updateCloudsReflectionState(t27) {
    const a25 = r(t27.cloudsFade.data);
    a25 !== this._material.parameters.hasCloudsReflections && this._material.setParameters({ hasCloudsReflections: a25 });
  }
  ensureResources(e16) {
    return this._techniqueRepository.constructionContext.waterTextureRepository.ensureResources(e16);
  }
  beginSlot(e16) {
    return this._output === h6.Color && (this._updateShadowState(e16), this._updateSSRState(e16), this._updateCloudsReflectionState(e16)), this._material.setParameters(this._techniqueRepository.constructionContext.waterTextureRepository.passParameters), this.ensureTechnique(P7, e16);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/WaterMaterial.js
var u11 = class extends e15 {
  constructor(e16) {
    super(e16, new f15()), this._configuration = new O8(), this.animation = new i7();
  }
  getConfiguration(e16, t27) {
    return this._configuration.output = e16, this._configuration.writeDepth = this.parameters.writeDepth, this._configuration.receiveShadows = this.parameters.receiveShadows, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.transparent = this.parameters.transparent, this._configuration.hasScreenSpaceReflections = this.parameters.hasScreenSpaceReflections, this._configuration.hasCloudsReflections = this.parameters.hasCloudsReflections, this._configuration.isDraped = this.parameters.isDraped, this._configuration.transparencyPassType = t27.transparencyPassType, this._configuration.enableOffset = t27.camera.relativeElevation < S2, this._configuration.hasMultipassTerrain = t27.multipassTerrain.enabled, this._configuration.cullAboveGround = t27.multipassTerrain.cullAboveGround, this._configuration;
  }
  update(t27) {
    const r28 = Math.min(t27.camera.relativeElevation, t27.camera.distance);
    this.animation.enabled = Math.sqrt(this.parameters.waveTextureRepeat / this.parameters.waveStrength) * r28 < d7;
    const i21 = this.animation.advance(t27);
    return this.setParameters({ timeElapsed: u(this.animation.time) * this.parameters.animationSpeed }, false), this.animation.enabled && i21;
  }
  requiresSlot(e16, t27) {
    switch (t27) {
      case h6.Normal:
        return e16 === E8.DRAPED_WATER;
      case h6.Color:
        if (this.parameters.isDraped) return e16 === E8.DRAPED_MATERIAL;
        break;
      case h6.Alpha:
        break;
      case h6.Highlight:
        return e16 === E8.OPAQUE_MATERIAL || e16 === E8.DRAPED_MATERIAL;
      default:
        return false;
    }
    let r28 = E8.OPAQUE_MATERIAL;
    return this.parameters.transparent && (r28 = this.parameters.writeDepth ? E8.TRANSPARENT_MATERIAL : E8.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL), e16 === r28;
  }
  createGLMaterial(e16) {
    return new r22(e16);
  }
  createBufferWriter() {
    return new r14(c13);
  }
};
var f15 = class extends u7 {
  constructor() {
    super(...arguments), this.waveStrength = 0.06, this.waveTextureRepeat = 32, this.waveDirection = r9(1, 0), this.waveVelocity = 0.05, this.flowStrength = 0.015, this.flowOffset = -0.5, this.animationSpeed = 0.35, this.timeElapsed = 0, this.color = r5(0, 0, 0, 0), this.transparent = true, this.writeDepth = true, this.hasSlicePlane = false, this.isDraped = false, this.receiveShadows = true, this.hasScreenSpaceReflections = false, this.hasCloudsReflections = false;
  }
};
var d7 = 35e3;

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/renderers/BufferRange.js
var t21 = class {
  constructor(t27 = 0, o22 = 0) {
    this.from = t27, this.to = o22;
  }
  get numElements() {
    return this.to - this.from;
  }
};
function o20(t27) {
  const o22 = /* @__PURE__ */ new Map();
  t27.forAll((t28) => o22.set(t28.from, t28));
  let e16 = true;
  for (; e16; ) e16 = false, t27.forEach((r28) => {
    const s20 = o22.get(r28.to);
    s20 && (r28.to = s20.to, o22.delete(s20.from), t27.removeUnordered(s20), e16 = true);
  });
}

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/renderers/Instance.js
var t22 = class extends t21 {
  constructor(e16, s20, t27) {
    super(s20, t27), this.geometry = e16;
  }
  get isVisible() {
    return this.geometry.visible;
  }
  get hasHighlights() {
    return r(this.geometry.highlights) && this.isVisible;
  }
  get hasOccludees() {
    return r(this.geometry.occludees);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/renderers/DrawCommand.js
var t23 = class {
  constructor() {
    this.first = 0, this.count = 0;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/renderers/PerBufferData.js
var i20 = class {
  constructor() {
    this._numElements = 0, this._instances = /* @__PURE__ */ new Map(), this.holes = new l2({ allocator: (s20) => s20 || new t21(), deallocator: null }), this.hasHiddenInstances = false, this.hasHighlights = false, this.hasOccludees = false, this.drawCommandsDirty = true, this.drawCommandsDefault = h13(), this.drawCommandsHighlight = h13(), this.drawCommandsOccludees = h13(), this.drawCommandsShadowHighlightRest = h13();
  }
  get numElements() {
    return this._numElements;
  }
  get instances() {
    return this._instances;
  }
  addInstance(s20, t27) {
    this.deleteInstance(s20), this._instances.set(s20, t27), this._numElements += t27.numElements;
  }
  deleteInstance(s20) {
    const t27 = this._instances.get(s20);
    t27 && (this._numElements -= t27.numElements, this._instances.delete(s20));
  }
  updateInstance(s20, t27, e16) {
    const n28 = this._instances.get(s20);
    n28 && (this._numElements -= n28.numElements, n28.from = t27, n28.to = e16, this._numElements += n28.numElements);
  }
  updateDrawState(s20) {
    s20.isVisible ? (s20.hasHighlights && (this.hasHighlights = true), s20.hasOccludees && (this.hasOccludees = true)) : this.hasHiddenInstances = true;
  }
  updateDrawCommands(t27) {
    if (this.drawCommandsDefault.clear(), this.drawCommandsHighlight.clear(), this.drawCommandsOccludees.clear(), this.drawCommandsShadowHighlightRest.clear(), this.drawCommandsDirty = false, 0 === this._instances.size) return;
    if (!this.needsMultipleCommands()) {
      const e17 = this.drawCommandsDefault.pushNew(), n28 = this.holes.front();
      return r(this.vao) && 1 === this.holes.length && n28.to === Math.floor(this.vao.size / t27) ? (e17.first = 0, void (e17.count = n28.from)) : (e17.first = 1 / 0, e17.count = 0, this._instances.forEach((s20) => {
        e17.first = Math.min(e17.first, s20.from), e17.count = Math.max(e17.count, s20.to);
      }), void (e17.count -= e17.first));
    }
    const e16 = Array.from(this._instances.values()).sort((s20, t28) => s20.from === t28.from ? s20.to - t28.to : s20.from - t28.from);
    for (const s20 of e16) s20.isVisible && (r23(s20.hasOccludees ? this.drawCommandsOccludees : this.drawCommandsDefault, s20), r23(s20.hasHighlights ? this.drawCommandsHighlight : this.drawCommandsShadowHighlightRest, s20));
  }
  needsMultipleCommands() {
    return this.hasOccludees || this.hasHighlights || this.hasHiddenInstances;
  }
};
function o21(t27) {
  return r(t27.vao);
}
function h13() {
  return new l2({ allocator: (s20) => s20 || new t23(), deallocator: (s20) => s20 });
}
function r23(s20, t27) {
  const e16 = s20.back();
  if (null == e16) {
    const e17 = s20.pushNew();
    return e17.first = t27.from, void (e17.count = t27.numElements);
  }
  if (m6(e16, t27)) {
    const s21 = t27.from - e16.first + t27.numElements;
    e16.count = s21;
  } else {
    const e17 = s20.pushNew();
    e17.first = t27.from, e17.count = t27.numElements;
  }
}
function m6(s20, t27) {
  return s20.first + s20.count >= t27.from;
}

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/renderers/PerOriginData.js
var s17 = class {
  constructor(s20) {
    this.origin = s20, this.buffers = new Array();
  }
  dispose() {
    this.buffers.forEach((s20) => s20.vao.dispose()), this.buffers.length = 0;
  }
  findBuffer(s20) {
    return this.buffers.find((r28) => r28.instances.has(s20));
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/renderers/VaoCache.js
var n23 = s3 + 1;
var a20 = class {
  constructor(t27, e16, o22) {
    this._rctx = t27, this._locations = e16, this._layout = o22, this._cache = t27.newCache(`VaoCache ${e3()}`, p10);
  }
  dispose() {
    this._cache.destroy();
  }
  newVao(e16) {
    const o22 = e16.toString(), r28 = this._cache.pop(o22);
    if (r(r28)) {
      const t27 = r28.pop();
      return r28.length > 0 && this._cache.put(o22, r28, e16 * r28.length, n23), t27;
    }
    const s20 = new r13(this._rctx, this._locations, { geometry: this._layout }, { geometry: E6.createVertex(this._rctx, F2.STATIC_DRAW) });
    return s20.vertexBuffers.geometry.setSize(e16), s20;
  }
  deleteVao(o22) {
    if (t(o22)) return null;
    const r28 = o22.size, s20 = r28.toString(), c15 = this._cache.pop(s20);
    return r(c15) ? (c15.push(o22), this._cache.put(s20, c15, r28 * c15.length, -1)) : this._cache.put(s20, [o22], r28, -1), null;
  }
};
function p10(t27, e16) {
  if (e16 === i3.ALL) return void t27.forEach((t28) => t28.dispose());
  const r28 = t27.pop(), s20 = t27.length * r28.size;
  return r28.dispose(), s20;
}

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/renderers/MergedRenderer.js
var H8 = class {
  constructor(e16, t27, r28) {
    this._rctx = e16, this._materialRepository = t27, this.material = r28, this._dataByOrigin = /* @__PURE__ */ new Map(), this._appleAmdDriverHelper = null, this._hasHighlights = false, this._hasOccludees = false, this._glMaterials = new e14(this.material, this._materialRepository), this._bufferWriter = r28.createBufferWriter(), this._vaoCache = new a20(e16, r28.vertexAttributeLocations, o5(this._bufferWriter.vertexBufferLayout)), this._rctx.driverTest.drawArraysRequiresIndicesTypeReset.result && (this._appleAmdDriverHelper = new n21(this._rctx));
  }
  dispose() {
    var _a;
    this._glMaterials.destroy(), this._dataByOrigin.forEach((e16) => e16.dispose()), this._dataByOrigin.clear(), this._vaoCache.dispose(), (_a = this._appleAmdDriverHelper) == null ? void 0 : _a.dispose();
  }
  get isEmpty() {
    return 0 === this._dataByOrigin.size;
  }
  get hasHighlights() {
    return this._hasHighlights;
  }
  get hasOccludees() {
    return this._hasOccludees;
  }
  get hasWater() {
    return !this.isEmpty && this.material instanceof u11;
  }
  get rendersOccluded() {
    return !this.isEmpty && this.material.renderOccluded !== c4.Occlude;
  }
  get numGeometries() {
    let e16 = 0;
    return this._dataByOrigin.forEach((t27) => e16 += t27.buffers.reduce((e17, t28) => e17 + t28.instances.size, 0)), e16;
  }
  forEachGeometry(e16) {
    this._dataByOrigin.forEach((t27) => t27.buffers.forEach((t28) => t28.instances.forEach((t29) => e16(t29.geometry))));
  }
  modify(e16) {
    this._updateGeometries(e16.updates), this._addAndRemoveGeometries(e16.adds, e16.removes), this._updateDrawCommands();
  }
  _updateGeometries(e16) {
    var _a;
    const t27 = this._bufferWriter, r28 = t27.vertexBufferLayout.stride / 4;
    for (const i21 of e16) {
      const e17 = i21.renderGeometry, a25 = (_a = this._dataByOrigin.get(e17.localOrigin.id)) == null ? void 0 : _a.findBuffer(e17.id);
      if (t(a25)) return;
      const o22 = a25.instances.get(e17.id);
      if (i21.updateType & (I5.GEOMETRY | I5.TRANSFORMATION)) {
        const s20 = z6(t27.elementCount(o22.geometry.geometry) * r28), i22 = t27.vertexBufferLayout.createView(s20.buffer);
        this._writeGeometry(e17, i22, 0), a25.vao.vertexBuffers.geometry.setSubData(s20, o22.from * r28, 0, o22.numElements * r28);
      }
      i21.updateType & (I5.HIGHLIGHT | I5.OCCLUDEE | I5.VISIBILITY) && (a25.drawCommandsDirty = true);
    }
  }
  _computeDeltas(e16, t27) {
    var _a;
    const r28 = new t7();
    for (const i21 of e16) {
      const e17 = i21.localOrigin;
      if (t(e17)) continue;
      let t28 = r28.get(e17.id, null);
      t(t28) && (t28 = new x11(e17.vec3), r28.set(e17.id, null, t28)), t28.changes.push(i21);
    }
    for (const i21 of t27) {
      const e17 = i21.localOrigin;
      if (t(e17)) continue;
      const t28 = (_a = this._dataByOrigin.get(e17.id)) == null ? void 0 : _a.findBuffer(i21.id);
      if (t(t28)) continue;
      let a25 = r28.get(e17.id, t28);
      t(a25) && (a25 = new x11(e17.vec3), r28.set(e17.id, t28, a25)), a25.changes.push(i21);
    }
    return r28;
  }
  _addAndRemoveGeometries(t27, r28) {
    const { _bufferWriter: a25, _dataByOrigin: o22 } = this, n28 = a25.vertexBufferLayout.stride / 4, l22 = this._computeDeltas(t27, r28);
    l22.forEach((t28, r29) => {
      const h16 = t28.get(null), u12 = r(h16) ? h16.changes : [];
      l22.delete(r29, null);
      let f18 = o22.get(r29);
      if (t28.forEach((t29, i21) => {
        if (l22.delete(r29, i21), t(i21)) return void s6(false, "No VAO for removed geometries");
        if (i21.instances.size === t29.changes.length) return this._vaoCache.deleteVao(i21.vao), v(f18.buffers, i21), void (0 === f18.buffers.length && 0 === u12.length && o22.delete(r29));
        const h17 = i21.numElements, c15 = i21.vao.size / 4, d11 = u12.reduce((e16, t30) => e16 + a25.elementCount(t30.geometry), 0), m9 = t29.changes.reduce((e16, t30) => e16 + a25.elementCount(t30.geometry), 0), g7 = Math.min((h17 + d11 - m9) * n28, W4), p13 = g7 > c15;
        g7 > L4 && g7 < c15 / 2 ? (t29.changes.forEach(({ id: e16 }) => i21.deleteInstance(e16)), i21.instances.forEach(({ geometry: e16 }) => u12.push(e16)), this._vaoCache.deleteVao(i21.vao), v(f18.buffers, i21)) : p13 ? this._applyAndRebuild(i21, u12, t29) : this._applyRemoves(i21, t29);
      }), u12.length > 0) for (t(f18) && (f18 = new s17(h16.origin), o22.set(r29, f18)), f18.buffers.forEach((e16) => this._applyAdds(e16, u12)); u12.length > 0; ) f18.buffers.push(this._applyAndRebuild(new i20(), u12, null));
    });
  }
  _updateDrawCommands() {
    this._hasHighlights = false, this._hasOccludees = false, this._dataByOrigin.forEach((e16) => {
      e16.buffers.forEach((e17) => {
        e17.drawCommandsDirty && (e17.hasHiddenInstances = false, e17.hasHighlights = false, e17.hasOccludees = false, n(e17.instances, (t27) => (e17.updateDrawState(t27), e17.hasHiddenInstances && e17.hasHighlights && e17.hasOccludees)), e17.updateDrawCommands(this._bufferWriter.vertexBufferLayout.stride)), this._hasHighlights = this._hasHighlights || e17.hasHighlights, this._hasOccludees = this._hasOccludees || e17.hasOccludees;
      });
    });
  }
  _applyAndRebuild(e16, t27, r28) {
    if (r(r28)) for (const i21 of r28.changes) e16.deleteInstance(i21.id);
    const s20 = this._bufferWriter, a25 = s20.vertexBufferLayout.stride, o22 = a25 / 4, n28 = Math.floor(W4 / o22);
    let l22 = e16.numElements;
    for (; t27.length > 0; ) {
      const r29 = t27.pop(), i21 = s20.elementCount(r29.geometry);
      if (l22 + i21 > n28 && l22 > 0) {
        t27.push(r29);
        break;
      }
      l22 += i21;
      const a26 = new t22(r29, 0, 0);
      s6(null == e16.instances.get(r29.id)), e16.addInstance(r29.id, a26);
    }
    const h16 = l22 * o22, u12 = z6(h16), f18 = s20.vertexBufferLayout.createView(u12.buffer);
    let c15 = 0;
    e16.hasHiddenInstances = false, e16.hasHighlights = false, e16.hasOccludees = false, e16.instances.forEach((t28, r29) => {
      this._writeGeometry(t28.geometry, f18, c15);
      const i21 = c15;
      c15 += s20.elementCount(t28.geometry.geometry), e16.updateInstance(r29, i21, c15), e16.updateDrawState(t28);
    }), this._vaoCache.deleteVao(e16.vao), e16.vao = this._vaoCache.newVao(N6(h16)), e16.vao.vertexBuffers.geometry.setSubData(u12, 0, 0, c15 * o22), e16.holes.clear();
    const d11 = e16.holes.pushNew();
    return d11.from = c15, d11.to = Math.floor(e16.vao.size / a25), e16.updateDrawCommands(a25), e16;
  }
  _applyRemoves(e16, t27) {
    if (0 === t27.changes.length) return;
    for (const o22 of t27.changes) {
      const t28 = o22.id, r29 = e16.instances.get(t28);
      if (!r29) continue;
      e16.deleteInstance(t28);
      const s21 = R6.back();
      if (s21) {
        if (s21.to === r29.from) {
          s21.to = r29.to;
          continue;
        }
        if (s21.from === r29.to) {
          s21.from = r29.from;
          continue;
        }
      }
      const i22 = R6.pushNew();
      i22.from = r29.from, i22.to = r29.to;
    }
    o20(R6);
    const r28 = this._bufferWriter.vertexBufferLayout.stride / 4, s20 = R6.reduce((e17, t28) => Math.max(e17, t28.numElements), 0) * r28, i21 = z6(s20);
    i21.fill(0, 0, s20);
    const a25 = e16.vao.vertexBuffers.geometry;
    R6.forAll((e17) => a25.setSubData(i21, e17.from * r28, 0, e17.numElements * r28)), e16.holes.pushArray(R6.data, R6.length), R6.forAll((e17, t28) => R6.data[t28] = null), R6.clear(), e16.drawCommandsDirty = true;
  }
  _applyAdds(e16, r28) {
    if (0 === r28.length) return;
    if (!o21(e16)) return void this._applyAndRebuild(e16, r28, null);
    const i21 = this._bufferWriter, a25 = i21.vertexBufferLayout.stride / 4, o22 = e16.numElements, n28 = r28.reduce((e17, t27) => e17 + i21.elementCount(t27.geometry), 0), l22 = Math.min((o22 + n28) * a25, W4), h16 = 4 * l22;
    if (e16.vao.size < N6(W4 - L4) && h16 > e16.vao.size) return void this._applyAndRebuild(e16, r28, null);
    o20(e16.holes);
    const u12 = new Array();
    for (const t27 of r28) {
      const r29 = i21.elementCount(t27.geometry), s20 = M8(e16.holes, r29);
      u12.push(s20);
    }
    const f18 = e16.vao.vertexBuffers.geometry;
    let c15 = 0, d11 = 0, m9 = 0;
    const g7 = z6(l22), p13 = i21.vertexBufferLayout.createView(g7.buffer);
    r28.forEach((t27, r29) => {
      const o23 = u12[r29];
      if (t(o23)) return;
      if (!(m9 === o23)) {
        const e17 = m9 - d11;
        e17 > 0 && f18.setSubData(g7, d11 * a25, 0, e17 * a25), d11 = o23, c15 = 0;
      }
      const n29 = i21.elementCount(t27.geometry);
      this._writeGeometry(t27, p13, c15), c15 += n29, m9 = o23 + n29;
      const l23 = new t22(t27, o23, o23 + n29);
      s6(null == e16.instances.get(t27.id)), e16.addInstance(t27.id, l23), e16.drawCommandsDirty = true;
    });
    const _11 = m9 - d11;
    _11 > 0 && f18.setSubData(g7, d11 * a25, 0, _11 * a25), z(r28, (e17, t27) => t(u12[t27]));
  }
  _writeGeometry(e16, t27, r28) {
    const s20 = e16.localOrigin.vec3;
    h5(j7, -s20[0], -s20[1], -s20[2]);
    const i21 = c2(I7, j7, e16.transformation);
    h3(S5, i21), o3(S5, S5), this._bufferWriter.write(i21, S5, e16.geometry, t27, r28);
  }
  updateAnimation(e16) {
    return this.material.update(e16);
  }
  requiresSlot(e16, t27) {
    return this.material.requiresSlot(e16, t27);
  }
  render(e16, t27) {
    var _a;
    if (!this.requiresSlot(t27.slot, e16)) return false;
    const r28 = e16 === h6.Highlight || e16 === h6.ShadowHighlight;
    if (r28 && !this._hasHighlights) return false;
    const a25 = e16 === h6.ShadowExcludeHighlight, o22 = !(r28 || a25), n28 = this._rctx;
    let l22;
    const h16 = () => {
      if (r(l22)) return l22;
      const r29 = this._glMaterials.load(n28, t27.slot, e16);
      return t(r29) ? null : (l22 = r29.beginSlot(t27), t(l22) ? null : (n28.bindTechnique(l22, this.material.parameters, t27), l22));
    };
    (_a = this._appleAmdDriverHelper) == null ? void 0 : _a.resetIndicesType();
    for (const i21 of this._dataByOrigin.values()) for (const e17 of i21.buffers) {
      if (r28 && !e17.hasHighlights) continue;
      const l23 = (r28 ? e17.drawCommandsHighlight : a25 && e17.needsMultipleCommands() ? e17.drawCommandsShadowHighlightRest : e17.drawCommandsDefault) || null, u12 = o22 && e17.drawCommandsOccludees || null;
      if ((l23 == null ? void 0 : l23.length) || (u12 == null ? void 0 : u12.length)) {
        const r29 = h16();
        if (t(r29)) return false;
        r29.program.bindDraw(new i19(i21.origin), t27, this.material.parameters), r29.ensureAttributeLocations(e17.vao), n28.bindVAO(e17.vao), (l23 == null ? void 0 : l23.length) && (r29.bindPipelineState(n28, t27.slot, false), l23.forAll((e18) => n28.drawArrays(r29.primitiveType, e18.first, e18.count))), (u12 == null ? void 0 : u12.length) && (r29.bindPipelineState(n28, t27.slot, true), u12.forAll((e18) => n28.drawArrays(r29.primitiveType, e18.first, e18.count)));
      }
    }
    return r(l22);
  }
  get test() {
    return { material: this.material, glMaterials: this._glMaterials, dataByOrigin: this._dataByOrigin };
  }
};
var x11 = class {
  constructor(e16) {
    this.origin = e16, this.changes = new Array();
  }
};
function M8(e16, t27) {
  let r28;
  if (!e16.some((e17) => !(e17.numElements < t27) && (r28 = e17, true))) return null;
  const s20 = r28.from;
  return r28.from += t27, r28.from >= r28.to && e16.removeUnordered(r28), s20;
}
var j7 = e7();
var I7 = e7();
var S5 = e7();
var R6 = new l2({ allocator: (e16) => e16 || new t21(), deallocator: null });
var L4 = 65536;
var G5 = 4 * L4;
var T7 = 16777216;
var W4 = T7 / 4;
var V7 = new Float32Array(L4);
function z6(e16) {
  return V7.length < e16 && (V7 = new Float32Array(e16)), V7;
}
function N6(e16) {
  const t27 = 4 * e16;
  return t27 < G5 ? G5 : Math.max(Math.min(Math.ceil(1.5 * t27 / G5) * G5, T7), t27);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/SortedRenderGeometryRenderer.js
var R7 = class extends v2 {
  constructor(e16) {
    super(e16), this._pending = new G6(), this._changes = new s16(), this._materialRenderers = /* @__PURE__ */ new Map(), this._sortedMaterialRenderers = new l2(), this._geometries = /* @__PURE__ */ new Map(), this._hasHighlights = false, this._hasWater = false;
  }
  destroy() {
    this._changes.prune(), this._materialRenderers.forEach((e16) => e16.dispose()), this._materialRenderers.clear(), this._sortedMaterialRenderers.clear(), this._geometries.clear();
  }
  get updating() {
    return !this._pending.empty || this._changes.updates.length > 0;
  }
  get rctx() {
    return this.rendererContext.rctx;
  }
  get _materialRepository() {
    return this.rendererContext.materialRepository;
  }
  get _localOriginFactory() {
    return this.rendererContext.localOriginFactory;
  }
  get hasHighlights() {
    return this._hasHighlights;
  }
  get hasWater() {
    return this._hasWater;
  }
  get rendersOccluded() {
    return n(this._materialRenderers, (e16) => e16.rendersOccluded);
  }
  get isEmpty() {
    return !this.updating && 0 === this._materialRenderers.size && 0 === this._geometries.size;
  }
  commitChanges() {
    if (!this.updating) return false;
    this._processAddsRemoves();
    const e16 = r20(this._changes);
    let r28 = false, s20 = false, i21 = false;
    return e16.forEach((e17, t27) => {
      let n28 = this._materialRenderers.get(t27);
      if (!n28 && e17.adds.length > 0 && (n28 = new H8(this.rctx, this._materialRepository, t27), this._materialRenderers.set(t27, n28), r28 = true, s20 = true, i21 = true), !n28) return;
      const a25 = s20 || n28.hasHighlights, o22 = i21 || n28.hasWater;
      n28.modify(e17), s20 = s20 || a25 !== n28.hasHighlights, i21 = i21 || o22 !== n28.hasWater, n28.isEmpty && (this._materialRenderers.delete(t27), n28.dispose(), r28 = true);
    }), this._changes.clear(), r28 && this._updateSortedMaterialRenderers(), s20 && (this._hasHighlights = n(this._materialRenderers, (e17) => e17.hasHighlights)), i21 && (this._hasWater = n(this._materialRenderers, (e17) => e17.hasWater)), this.notifyChange("updating"), true;
  }
  addGeometries(e16, r28) {
    if (0 === e16.length) return;
    const t27 = this._validateRenderGeometries(e16);
    for (const i21 of t27) this._geometries.set(i21.id, i21);
    const s20 = this._pending.empty;
    for (const i21 of t27) this._pending.adds.add(i21);
    s20 && this.notifyChange("updating"), r28 === E11.UPDATE && this._notifyGraphicGeometryChanged(e16);
  }
  removeGeometries(e16, r28) {
    const t27 = this._pending.empty, i21 = this._pending.adds;
    for (const n28 of e16) i21.has(n28) ? (this._pending.removed.add(n28), i21.delete(n28)) : this._pending.removed.has(n28) || this._pending.removes.add(n28), this._geometries.delete(e2(n28.id));
    t27 && !this._pending.empty && this.notifyChange("updating"), r28 === E11.UPDATE && this._notifyGraphicGeometryChanged(e16);
  }
  modifyGeometries(e16, r28) {
    const t27 = 0 === this._changes.updates.length;
    for (const s20 of e16) {
      const e17 = this._changes.updates.pushNew();
      e17.renderGeometry = this._validateRenderGeometry(s20), e17.updateType = r28;
    }
    switch (t27 && this._changes.updates.length > 0 && this.notifyChange("updating"), r28) {
      case I5.TRANSFORMATION:
      case I5.GEOMETRY:
        return this._notifyGraphicGeometryChanged(e16);
      case I5.VISIBILITY:
        return this._notifyGraphicVisibilityChanged(e16);
    }
  }
  updateAnimation(e16) {
    let r28 = false;
    return this._sortedMaterialRenderers.forAll((t27) => r28 = t27.updateAnimation(e16) || r28), r28;
  }
  render(e16) {
    this._sortedMaterialRenderers.forAll((r28) => {
      r28.material.shouldRender(e16) && r28.render(e16.output, e16.bindParameters);
    });
  }
  intersect(e16, r28, t27, s20, i21) {
    return this._geometries.forEach((n28) => {
      if (s20 && !s20(n28)) return;
      this._intersectRenderGeometry(n28, t27, r28, 0, e16, i21);
      const a25 = this.rendererContext.longitudeCyclical;
      a25 && (n28.boundingSphere[0] - n28.boundingSphere[3] < a25.min && this._intersectRenderGeometry(n28, t27, r28, a25.range, e16, i21), n28.boundingSphere[0] + n28.boundingSphere[3] > a25.max && this._intersectRenderGeometry(n28, t27, r28, -a25.range, e16, i21)), i21++;
    }), i21;
  }
  _updateSortedMaterialRenderers() {
    this._sortedMaterialRenderers.clear();
    let e16 = 0;
    this._materialRenderers.forEach((r28, t27) => {
      t27.insertOrder = e16++, this._sortedMaterialRenderers.push(r28);
    }), this._sortedMaterialRenderers.sort((e17, r28) => {
      const t27 = r28.material.renderPriority - e17.material.renderPriority;
      return 0 !== t27 ? t27 : e17.material.insertOrder - r28.material.insertOrder;
    });
  }
  _processAddsRemoves() {
    this._changes.adds.clear(), this._changes.removes.clear(), this._changes.adds.pushArray(Array.from(this._pending.adds)), this._changes.removes.pushArray(Array.from(this._pending.removes));
    for (let e16 = 0; e16 < this._changes.updates.length; ) {
      const r28 = this._changes.updates.data[e16];
      this._pending.has(r28.renderGeometry) ? this._changes.updates.removeUnorderedIndex(e16) : e16++;
    }
    this._pending.clear();
  }
  _intersectRenderGeometry(e16, r28, t27, s20, i21, n28) {
    if (!e16.visible) return;
    let a25 = 0;
    s20 += e16.transformation[12], a25 = e16.transformation[13], C6[0] = t27[0] - s20, C6[1] = t27[1] - a25, e16.screenToWorldRatio = this.rendererContext.screenToWorldRatio, e16.material.intersectDraped(e16, null, i21, C6, (t28, s21, a26) => {
      v11(r28, a26, e16.material.renderPriority, n28, i21, e16.layerUid, e16.graphicUid);
    }, r28);
  }
  _notifyGraphicGeometryChanged(e16) {
    if (t(this.drapeSource.notifyGraphicGeometryChanged)) return;
    let r28;
    for (const t27 of e16) {
      const e17 = t27.graphicUid;
      r(e17) && e17 !== r28 && (this.drapeSource.notifyGraphicGeometryChanged(e17), r28 = e17);
    }
  }
  _notifyGraphicVisibilityChanged(e16) {
    if (t(this.drapeSource.notifyGraphicVisibilityChanged)) return;
    let r28;
    for (const t27 of e16) {
      const e17 = t27.graphicUid;
      r(e17) && e17 !== r28 && (this.drapeSource.notifyGraphicVisibilityChanged(e17), r28 = e17);
    }
  }
  _validateRenderGeometries(e16) {
    for (const r28 of e16) this._validateRenderGeometry(r28);
    return e16;
  }
  _validateRenderGeometry(e16) {
    return t(e16.localOrigin) && (e16.localOrigin = this._localOriginFactory.getOrigin(e16.boundingSphere)), e16;
  }
  get test() {
    return { sortedMaterialRenderers: this._sortedMaterialRenderers };
  }
};
e([y3()], R7.prototype, "drapeSource", void 0), e([y3()], R7.prototype, "updating", null), e([y3()], R7.prototype, "rctx", null), e([y3()], R7.prototype, "rendererContext", void 0), e([y3()], R7.prototype, "_materialRepository", null), e([y3()], R7.prototype, "_localOriginFactory", null), e([y3({ readOnly: true })], R7.prototype, "isEmpty", null), e([y3()], R7.prototype, "_materialRenderers", void 0), e([y3()], R7.prototype, "_geometries", void 0), R7 = e([a2("esri.views.3d.webgl-engine.lib.SortedRenderGeometryRenderer")], R7);
var G6 = class {
  constructor() {
    this.adds = /* @__PURE__ */ new Set(), this.removes = /* @__PURE__ */ new Set(), this.removed = /* @__PURE__ */ new Set();
  }
  get empty() {
    return 0 === this.adds.size && 0 === this.removes.size && 0 === this.removed.size;
  }
  has(e16) {
    return this.adds.has(e16) || this.removes.has(e16) || this.removed.has(e16);
  }
  clear() {
    this.adds.clear(), this.removes.clear(), this.removed.clear();
  }
};
function v11(e16, r28, t27, s20, i21, n28, a25) {
  const o22 = new i18(n28, a25, r28), d11 = (r29) => {
    r29.set(i14.OVERLAY, o22, e16.dist, e16.normal, e16.transformation, t27, s20);
  };
  if ((null == i21.results.min.drapedLayerOrder || t27 >= i21.results.min.drapedLayerOrder) && (null == i21.results.min.dist || i21.results.ground.dist <= i21.results.min.dist) && d11(i21.results.min), i21.options.store !== t17.MIN && (null == i21.results.max.drapedLayerOrder || t27 < i21.results.max.drapedLayerOrder) && (null == i21.results.max.dist || i21.results.ground.dist > i21.results.max.dist) && d11(i21.results.max), i21.options.store === t17.ALL) {
    const e17 = U4(i21.ray);
    d11(e17), i21.results.all.push(e17);
  }
}
var C6 = n12();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/TextureTechnique.js
var m7 = class _m extends e9 {
  initializeProgram(e16) {
    return new o12(e16.rctx, _m.shader.get().build(), E5);
  }
  initializePipeline() {
    return this.configuration.hasAlpha ? W({ blending: l15(R2.SRC_ALPHA, R2.ONE, R2.ONE_MINUS_SRC_ALPHA, R2.ONE_MINUS_SRC_ALPHA), colorWrite: _6 }) : W({ colorWrite: _6 });
  }
};
m7.shader = new t9(d2, () => import("./TextureOnly.glsl-MEHTK4IF.js"));

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/TextureTechniqueConfiguration.js
var r24 = class extends t10 {
  constructor() {
    super(...arguments), this.hasAlpha = false;
  }
};
e([r15()], r24.prototype, "hasAlpha", void 0);

// node_modules/@arcgis/core/views/3d/terrain/OverlayRenderer.js
var he2 = class extends v2 {
  get _bindParameters() {
    return this._renderContext.bindParameters;
  }
  get rctx() {
    return this._rctx;
  }
  get materialRepository() {
    return this._materialRepository;
  }
  get screenToWorldRatio() {
    return this._screenToWorldRatio;
  }
  get localOriginFactory() {
    return this._localOriginFactory;
  }
  constructor(e16) {
    super(e16), this._overlays = null, this._overlayRenderTarget = null, this._hasHighlights = false, this._rendersOccluded = false, this._hasWater = false, this._handles = new t2(), this._renderers = /* @__PURE__ */ new Map(), this._sortedDrapeSourceRenderersDirty = false, this._sortedRenderers = new l2(), this._passParameters = new i6(), this._rctx = null, this._materialRepository = null, this._screenToWorldRatio = 1, this._localOriginFactory = null, this._camera = new $2(), this.worldToPCSRatio = 1, this.events = new n3(), this.longitudeCyclical = null;
  }
  initialize() {
    const e16 = this.view._stage.renderView;
    this._rctx = e16.renderingContext;
    const r28 = e16.waterTextureRepository;
    this._stippleTextureRepository = new u10(e16.renderingContext), this._shaderTechniqueRepository = new n18({ rctx: this._rctx, viewingMode: l13.Local, stippleTextureRepository: this._stippleTextureRepository, waterTextureRepository: r28 }), this._renderContext = new i17(this._rctx, new K2(this._rctx, this.view.state.viewingMode), new P5(this.view, this._shaderTechniqueRepository, this._rctx, () => {
    })), this._handles.add([l7(() => r28.updating, () => this.events.emit("content-changed"), w2), l7(() => this.spatialReference, (e17) => this._localOriginFactory = new p8(e17), w2), a6(() => this.view.allLayerViews, "after-changes", () => this._sortedDrapeSourceRenderersDirty = true)]), this._materialRepository = new a19(e16.textureRepository, this._shaderTechniqueRepository, (e17) => {
      (e17.renderOccluded & pe2) > 0 !== this._rendersOccluded && this._updateRendersOccluded(), this.events.emit("content-changed"), this.notifyChange("updating"), this.notifyChange("isEmpty");
    }, () => this.events.emit("content-changed")), this._bindParameters.slot = E8.DRAPED_MATERIAL, this._bindParameters.highlightDepthTexture = p3(this._rctx), this._camera.near = 1, this._camera.far = 1e4, this._camera.relativeElevation = null, this._bindParameters.camera = this._camera, this._bindParameters.transparencyPassType = o11.NONE, this._bindParameters.newLighting.noonFactor = 0, this._bindParameters.newLighting.globalFactor = 0, this._bindParameters.newLighting.set([new i9(r2(1, 1, 1))]), this._handles.add(this.view.resourceController.scheduler.registerTask(R.STAGE, this));
  }
  destroy() {
    this._handles.destroy(), this._renderers.forEach((e16) => e16.destroy()), this._renderers.clear(), this._debugTextureTechnique = y(this._debugTextureTechnique), this._passParameters.texture = h(this._passParameters.texture), this._bindParameters.highlightDepthTexture = h(this._bindParameters.highlightDepthTexture), this._shaderTechniqueRepository = a(this._shaderTechniqueRepository), this._temporaryFBO = h(this._temporaryFBO), this._quadVAO = h(this._quadVAO), this.disposeOverlays();
  }
  get updating() {
    return this._sortedDrapeSourceRenderersDirty || n(this._renderers, (e16) => e16.updating);
  }
  get hasOverlays() {
    return r(this._overlays) && r(this._overlayRenderTarget);
  }
  get gpuMemoryUsage() {
    return r(this._overlayRenderTarget) ? this._overlayRenderTarget.gpuMemoryUsage : 0;
  }
  createGeometryDrapeSourceRenderer(e16) {
    return this.createDrapeSourceRenderer(e16, R7);
  }
  createDrapeSourceRenderer(e16, r28, t27) {
    const s20 = this._renderers.get(e16);
    r(s20) && s20.destroy();
    const i21 = new r28({ ...t27, rendererContext: this, drapeSource: e16 });
    return this._renderers.set(e16, i21), this._sortedDrapeSourceRenderersDirty = true, "fullOpacity" in e16 && this._handles.add(l7(() => e16.fullOpacity, () => this.events.emit("content-changed")), e16), i21;
  }
  removeDrapeSourceRenderer(e16) {
    if (t(e16)) return;
    const r28 = this._renderers.get(e16);
    t(r28) || (this._sortedDrapeSourceRenderersDirty = true, this._renderers.delete(e16), this._handles.remove(e16), r28.destroy());
  }
  collectUnusedRenderTargetMemory(e16) {
    let r28 = false;
    if (r(this._overlayRenderTarget)) for (const t27 of this._overlayRenderTarget.renderTargets) {
      const [s20, i21] = this.overlays, o22 = s20.validTargets[t27.type] || !i21.validTargets[t27.type];
      r28 = this._overlayRenderTarget.validateUsageForTarget(o22, t27, e16) || r28;
    }
    return r28;
  }
  get overlays() {
    return l(this._overlays, []);
  }
  ensureDrapeTargets(e16) {
    r(this._overlays) && this._overlays.forEach((r28) => r28.hasTargetWithoutRasterImage = n2(e16, (e17) => e17.drapeTargetType === t12.WithoutRasterImage));
  }
  ensureDrapeSources(e16) {
    r(this._overlays) && this._overlays.forEach((r28) => {
      r28.hasDrapedFeatureSource = n2(e16, (e17) => e17.drapeSourceType === e11.Features), r28.hasDrapedRasterSource = n2(e16, (e17) => e17.drapeSourceType === e11.RasterImage);
    });
  }
  ensureOverlays(e16, r28) {
    t(this._overlays) && (this._overlayRenderTarget = new o17(this._rctx), this._overlays = [new h12(T3.INNER, this._overlayRenderTarget), new h12(T3.OUTER, this._overlayRenderTarget)]), this.ensureDrapeTargets(e16), this.ensureDrapeSources(r28);
  }
  disposeOverlays() {
    this._overlays = null, this._overlayRenderTarget = h(this._overlayRenderTarget), this.events.emit("textures-disposed");
  }
  get running() {
    return this.updating;
  }
  runTask(e16) {
    this._processDrapeSources(e16, () => true);
  }
  _processDrapeSources(e16, r28) {
    let t27 = false;
    for (const [s20, i21] of this._renderers) {
      if (e16.done) break;
      (s20.destroyed || r28(s20)) && (i21.commitChanges() && (t27 = true, e16.madeProgress()));
    }
    this._sortedDrapeSourceRenderersDirty && (this._sortedDrapeSourceRenderersDirty = false, t27 = true, this._updateSortedDrapeSourceRenderers()), t27 && (r(this._overlays) && 0 === this._renderers.size && this.disposeOverlays(), this.notifyChange("updating"), this.notifyChange("isEmpty"), this.events.emit("content-changed"), this._updateHasHighlights(), this._updateRendersOccluded(), this._updateHasWater());
  }
  processSyncDrapeSources() {
    this._processDrapeSources(P2, (e16) => e16.updatePolicy === C4.SYNC);
  }
  get isEmpty() {
    return !t11.OVERLAY_DRAW_DEBUG_TEXTURE && !n(this._renderers, (e16) => !e16.isEmpty);
  }
  get hasHighlights() {
    return this._hasHighlights;
  }
  get hasWater() {
    return this._hasWater;
  }
  get rendersOccluded() {
    return this._rendersOccluded;
  }
  updateAnimation(e16) {
    let r28 = false;
    return this._renderers.forEach((t27) => r28 = t27.updateAnimation(e16) || r28), r28;
  }
  updateDrapeSourceOrder() {
    this._sortedDrapeSourceRenderersDirty = true;
  }
  drawTarget(e16, r28, t27) {
    const s20 = e16.canvasGeometries;
    if (0 === s20.numViews) return false;
    this._screenToWorldRatio = t27 * e16.mapUnitsPerPixel;
    const i21 = r28.output;
    if (this.isEmpty || i21 === h6.Highlight && !this.hasHighlights || i21 === h6.Normal && !this.hasWater || !e16.hasSomeSizedView()) return false;
    const o22 = r28.fbo;
    if (!o22.isValid()) return false;
    const a25 = 2 * e16.resolution, n28 = e16.resolution;
    o22.resize(a25, n28);
    const d11 = this._rctx;
    if (this._camera.pixelRatio = e16.pixelRatio * t27, this._renderContext.output = i21, this._bindParameters.screenToWorldRatio = this._screenToWorldRatio, this._bindParameters.screenToPCSRatio = this._screenToWorldRatio * this.worldToPCSRatio, this._bindParameters.slot = i21 === h6.Normal ? E8.DRAPED_WATER : E8.DRAPED_MATERIAL, e16.applyViewport(this._rctx), o22.bind(d11), e16.index === T3.INNER && (d11.setClearColor(0, 0, 0, 0), d11.clearSafe(_4.COLOR_BUFFER_BIT)), r28.type === H5.Occluded && (this._renderContext.renderOccludedMask = pe2), t11.OVERLAY_DRAW_DEBUG_TEXTURE && r28.type !== H5.Occluded) for (let h16 = 0; h16 < s20.numViews; h16++) this._setViewParameters(s20.extents[h16], e16), this._drawDebugTexture(e16.resolution, le2[e16.index]);
    return this._renderers.size > 0 && this._sortedRenderers.forAll(({ drapeSource: t28, renderer: l22 }) => {
      if (r28.type === H5.ColorNoRasterImage && t28.drapeSourceType === e11.RasterImage) return;
      const { fullOpacity: c15 } = t28, p13 = r(c15) && c15 < 1 && i21 === h6.Color;
      p13 && (this.bindTemporaryFramebuffer(this._rctx, a25, n28), d11.clearSafe(_4.COLOR_BUFFER_BIT));
      for (let r29 = 0; r29 < s20.numViews; r29++) this._setViewParameters(s20.extents[r29], e16), l22.render(this._renderContext);
      p13 && r(this._temporaryFBO) && (o22.bind(d11), this.view._stage.renderView.compositingHelper.compositeOverlay(this._renderContext.bindParameters, this._temporaryFBO.getTexture(), c15, e16.index));
    }), d11.bindFramebuffer(null), o22.generateMipMap(), this._renderContext.resetRenderOccludedMask(), true;
  }
  bindTemporaryFramebuffer(e16, r28, t27) {
    t(this._temporaryFBO) && (this._temporaryFBO = new f13(e16, false)), this._temporaryFBO.resize(r28, t27), this._temporaryFBO.bind(e16);
  }
  async reloadShaders() {
    await this._shaderTechniqueRepository.reloadAll();
  }
  notifyContentChanged() {
    this.events.emit("content-changed");
  }
  intersect(e16, r28, t27, s20) {
    var _a;
    let i21 = 0;
    for (const o22 of this._renderers.values()) i21 = ((_a = o22.intersect) == null ? void 0 : _a.call(o22, e16, r28, t27, s20, i21)) ?? i21;
  }
  _updateSortedDrapeSourceRenderers() {
    if (this._sortedRenderers.clear(), 0 === this._renderers.size) return;
    const e16 = this.view.map.allLayers;
    this._renderers.forEach((r28, t27) => {
      const s20 = e16.indexOf(t27.layer), i21 = s20 >= 0, o22 = this._renderers.size * (t27.renderGroup ?? (i21 ? a14.MapLayer : a14.ViewLayer)) + (i21 ? s20 : 0);
      this._sortedRenderers.push(new de(t27, r28, o22));
    }), this._sortedRenderers.sort((e17, r28) => e17.index - r28.index);
  }
  _setViewParameters(e16, r28) {
    const t27 = this._camera;
    t27.viewport = [0, 0, r28.resolution, r28.resolution], Q(t27.projectionMatrix, 0, e16[2] - e16[0], 0, e16[3] - e16[1], t27.near, t27.far), q(t27.viewMatrix, [-e16[0], -e16[1], 0]);
  }
  _updateHasWater() {
    const e16 = n(this._renderers, (e17) => e17.hasWater);
    e16 !== this._hasWater && (this._hasWater = e16, this.events.emit("has-water", e16));
  }
  _updateHasHighlights() {
    const e16 = n(this._renderers, (e17) => e17.hasHighlights);
    e16 !== this._hasHighlights && (this._hasHighlights = e16, this.events.emit("has-highlights", e16));
  }
  _updateRendersOccluded() {
    const e16 = n(this._renderers, (e17) => e17.rendersOccluded);
    e16 !== this._rendersOccluded && (this._rendersOccluded = e16, this.events.emit("renders-occluded", e16));
  }
  _drawDebugTexture(e16, r28) {
    this._ensureDebugPatternResources(e16, e16, r28);
    const t27 = this._rctx;
    t27.bindTechnique(this._debugTextureTechnique, this._passParameters, null), t27.bindVAO(this._quadVAO), t27.drawArrays(E3.TRIANGLE_STRIP, 0, n14(this._quadVAO, "geometry"));
  }
  _ensureDebugPatternResources(e16, r28, t27) {
    if (o2(this._passParameters.color, t27[0], t27[1], t27[2]), this._passParameters.texture) return;
    const s20 = new Uint8Array(e16 * r28 * 4);
    let i21 = 0;
    for (let a25 = 0; a25 < r28; a25++) for (let t28 = 0; t28 < e16; t28++) {
      const o23 = Math.floor(t28 / 10), n28 = Math.floor(a25 / 10);
      o23 < 2 || n28 < 2 || 10 * o23 > e16 - 20 || 10 * n28 > r28 - 20 ? (s20[i21++] = 255, s20[i21++] = 255, s20[i21++] = 255, s20[i21++] = 255) : (s20[i21++] = 255, s20[i21++] = 255, s20[i21++] = 255, s20[i21++] = 1 & o23 && 1 & n28 ? 1 & t28 ^ 1 & a25 ? 0 : 255 : 1 & o23 ^ 1 & n28 ? 0 : 128);
    }
    this._passParameters.texture = new E4(this._rctx, { target: M.TEXTURE_2D, pixelFormat: P3.RGBA, dataType: G.UNSIGNED_BYTE, samplingMode: L.NEAREST, width: e16, height: r28 }, s20);
    const o22 = new r24();
    o22.hasAlpha = true, this._debugTextureTechnique = this._shaderTechniqueRepository.acquire(m7, o22), this._quadVAO = f5(this._rctx);
  }
  get test() {
    return { drapedRenderers: Array.from(this._renderers.values()), getDrapeSourceRenderer: (e16) => this._renderers.get(e16) };
  }
};
e([y3()], he2.prototype, "_sortedDrapeSourceRenderersDirty", void 0), e([y3({ autoDestroy: true })], he2.prototype, "_shaderTechniqueRepository", void 0), e([y3({ autoDestroy: true })], he2.prototype, "_stippleTextureRepository", void 0), e([y3({ constructOnly: true })], he2.prototype, "view", void 0), e([y3()], he2.prototype, "worldToPCSRatio", void 0), e([y3()], he2.prototype, "spatialReference", void 0), e([y3({ type: Boolean, readOnly: true })], he2.prototype, "updating", null), e([y3()], he2.prototype, "isEmpty", null), he2 = e([a2("esri.views.3d.terrain.OverlayRenderer")], he2);
var de = class {
  constructor(e16, r28, t27) {
    this.drapeSource = e16, this.renderer = r28, this.index = t27;
  }
};
var le2 = [[1, 0.5, 0.5], [0.5, 0.5, 1]];
var ce2 = -2;
var pe2 = c4.OccludeAndTransparent;

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/BufferVectorMath.js
var n24;
!function(n28) {
  function t27(n29, t28) {
    const c16 = n29[t28], o23 = n29[t28 + 1], r29 = n29[t28 + 2];
    return Math.sqrt(c16 * c16 + o23 * o23 + r29 * r29);
  }
  function c15(n29, t28) {
    const c16 = n29[t28], o23 = n29[t28 + 1], r29 = n29[t28 + 2], u13 = 1 / Math.sqrt(c16 * c16 + o23 * o23 + r29 * r29);
    n29[t28] *= u13, n29[t28 + 1] *= u13, n29[t28 + 2] *= u13;
  }
  function o22(n29, t28, c16) {
    n29[t28] *= c16, n29[t28 + 1] *= c16, n29[t28 + 2] *= c16;
  }
  function r28(n29, t28, c16, o23, r29, u13 = t28) {
    (r29 = r29 || n29)[u13] = n29[t28] + c16[o23], r29[u13 + 1] = n29[t28 + 1] + c16[o23 + 1], r29[u13 + 2] = n29[t28 + 2] + c16[o23 + 2];
  }
  function u12(n29, t28, c16, o23, r29, u13 = t28) {
    (r29 = r29 || n29)[u13] = n29[t28] - c16[o23], r29[u13 + 1] = n29[t28 + 1] - c16[o23 + 1], r29[u13 + 2] = n29[t28 + 2] - c16[o23 + 2];
  }
  n28.length = t27, n28.normalize = c15, n28.scale = o22, n28.add = r28, n28.subtract = u12;
}(n24 || (n24 = {}));

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/FloatArray.js
function n25(n28, t27 = false) {
  return n28 <= y2 ? t27 ? new Array(n28).fill(0) : new Array(n28) : new Float32Array(n28);
}
function t24(n28) {
  return length <= y2 ? Array.from(n28) : new Float32Array(n28);
}
function a21(r28, n28, t27) {
  return Array.isArray(r28) ? r28.slice(n28, n28 + t27) : r28.subarray(n28, n28 + t27);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/GeometryUtil.js
var b6 = n24;
var j8 = [[-0.5, -0.5, 0.5], [0.5, -0.5, 0.5], [0.5, 0.5, 0.5], [-0.5, 0.5, 0.5], [-0.5, -0.5, -0.5], [0.5, -0.5, -0.5], [0.5, 0.5, -0.5], [-0.5, 0.5, -0.5]];
var d8 = [0, 0, 1, -1, 0, 0, 1, 0, 0, 0, -1, 0, 0, 1, 0, 0, 0, -1];
var U5 = [0, 0, 1, 0, 1, 1, 0, 1];
var V8 = [0, 1, 2, 2, 3, 0, 4, 0, 3, 3, 7, 4, 1, 5, 6, 6, 2, 1, 1, 0, 4, 4, 5, 1, 3, 2, 6, 6, 7, 3, 5, 4, 7, 7, 6, 5];
var v12 = new Array(36);
for (let gt2 = 0; gt2 < 6; gt2++) for (let t27 = 0; t27 < 6; t27++) v12[6 * gt2 + t27] = gt2;
var x12 = new Array(36);
for (let gt2 = 0; gt2 < 6; gt2++) x12[6 * gt2 + 0] = 0, x12[6 * gt2 + 1] = 1, x12[6 * gt2 + 2] = 2, x12[6 * gt2 + 3] = 2, x12[6 * gt2 + 4] = 3, x12[6 * gt2 + 5] = 0;
function C7(t27, n28) {
  Array.isArray(n28) || (n28 = [n28, n28, n28]);
  const e16 = new Array(24);
  for (let s20 = 0; s20 < 8; s20++) e16[3 * s20] = j8[s20][0] * n28[0], e16[3 * s20 + 1] = j8[s20][1] * n28[1], e16[3 * s20 + 2] = j8[s20][2] * n28[2];
  return new v5(t27, [[O3.POSITION, new s5(e16, 3, true)], [O3.NORMAL, new s5(d8, 3)], [O3.UV0, new s5(U5, 2)]], [[O3.POSITION, V8], [O3.NORMAL, v12], [O3.UV0, x12]]);
}
var F4 = [[-0.5, 0, -0.5], [0.5, 0, -0.5], [0.5, 0, 0.5], [-0.5, 0, 0.5], [0, -0.5, 0], [0, 0.5, 0]];
var G7 = [0, 1, -1, 1, 1, 0, 0, 1, 1, -1, 1, 0, 0, -1, -1, 1, -1, 0, 0, -1, 1, -1, -1, 0];
var k6 = [5, 1, 0, 5, 2, 1, 5, 3, 2, 5, 0, 3, 4, 0, 1, 4, 1, 2, 4, 2, 3, 4, 3, 0];
var E12 = [0, 0, 0, 1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5, 6, 6, 6, 7, 7, 7];
function q3(t27, n28) {
  Array.isArray(n28) || (n28 = [n28, n28, n28]);
  const e16 = new Array(18);
  for (let s20 = 0; s20 < 6; s20++) e16[3 * s20] = F4[s20][0] * n28[0], e16[3 * s20 + 1] = F4[s20][1] * n28[1], e16[3 * s20 + 2] = F4[s20][2] * n28[2];
  return new v5(t27, [[O3.POSITION, new s5(e16, 3, true)], [O3.NORMAL, new s5(G7, 3)]], [[O3.POSITION, k6], [O3.NORMAL, E12]]);
}
var X4 = r8(-0.5, 0, -0.5);
var z7 = r8(0.5, 0, -0.5);
var B4 = r8(0, 0, 0.5);
var D3 = r8(0, 0.5, 0);
var Z5 = n11();
var H9 = n11();
var J3 = n11();
var K3 = n11();
var Q4 = n11();
e5(Z5, X4, D3), e5(H9, X4, z7), _(J3, Z5, H9), z2(J3, J3), e5(Z5, z7, D3), e5(H9, z7, B4), _(K3, Z5, H9), z2(K3, K3), e5(Z5, B4, D3), e5(H9, B4, X4), _(Q4, Z5, H9), z2(Q4, Q4);
var W5 = [X4, z7, B4, D3];
var Y4 = [0, -1, 0, J3[0], J3[1], J3[2], K3[0], K3[1], K3[2], Q4[0], Q4[1], Q4[2]];
var $5 = [0, 1, 2, 3, 1, 0, 3, 2, 1, 3, 0, 2];
var _10 = [0, 0, 0, 1, 1, 1, 2, 2, 2, 3, 3, 3];
function tt3(t27, n28) {
  Array.isArray(n28) || (n28 = [n28, n28, n28]);
  const e16 = new Array(12);
  for (let s20 = 0; s20 < 4; s20++) e16[3 * s20] = W5[s20][0] * n28[0], e16[3 * s20 + 1] = W5[s20][1] * n28[1], e16[3 * s20 + 2] = W5[s20][2] * n28[2];
  return new v5(t27, [[O3.POSITION, new s5(e16, 3, true)], [O3.NORMAL, new s5(Y4, 3)]], [[O3.POSITION, $5], [O3.NORMAL, _10]]);
}
function nt2(t27, n28, e16, s20, o22 = { uv: true }) {
  const r28 = -Math.PI, h16 = 2 * Math.PI, l22 = -Math.PI / 2, u12 = Math.PI, c15 = Math.max(3, Math.floor(e16)), a25 = Math.max(2, Math.floor(s20)), f18 = (c15 + 1) * (a25 + 1), p13 = n25(3 * f18), O10 = n25(3 * f18), i21 = n25(2 * f18), I9 = [];
  let w4 = 0;
  for (let A10 = 0; A10 <= a25; A10++) {
    const t28 = [], e17 = A10 / a25, s21 = l22 + e17 * u12, o23 = Math.cos(s21);
    for (let l23 = 0; l23 <= c15; l23++) {
      const u13 = l23 / c15, a26 = r28 + u13 * h16, f19 = Math.cos(a26) * o23, I10 = Math.sin(s21), M10 = -Math.sin(a26) * o23;
      p13[3 * w4] = f19 * n28, p13[3 * w4 + 1] = I10 * n28, p13[3 * w4 + 2] = M10 * n28, O10[3 * w4] = f19, O10[3 * w4 + 1] = I10, O10[3 * w4 + 2] = M10, i21[2 * w4] = u13, i21[2 * w4 + 1] = e17, t28.push(w4), ++w4;
    }
    I9.push(t28);
  }
  const M9 = new Array();
  for (let A10 = 0; A10 < a25; A10++) for (let t28 = 0; t28 < c15; t28++) {
    const n29 = I9[A10][t28], e17 = I9[A10][t28 + 1], s21 = I9[A10 + 1][t28 + 1], o23 = I9[A10 + 1][t28];
    0 === A10 ? (M9.push(n29), M9.push(s21), M9.push(o23)) : A10 === a25 - 1 ? (M9.push(n29), M9.push(e17), M9.push(s21)) : (M9.push(n29), M9.push(e17), M9.push(s21), M9.push(s21), M9.push(o23), M9.push(n29));
  }
  const g7 = [[O3.POSITION, M9], [O3.NORMAL, M9]], m9 = [[O3.POSITION, new s5(p13, 3, true)], [O3.NORMAL, new s5(O10, 3, true)]];
  return o22.uv && (m9.push([O3.UV0, new s5(i21, 2, true)]), g7.push([O3.UV0, M9])), o22.offset && (g7[0][0] = O3.OFFSET, m9[0][0] = O3.OFFSET, g7.push([O3.POSITION, new Array(M9.length).fill(0)]), m9.push([O3.POSITION, new s5(Float64Array.from(o22.offset), 3, true)])), new v5(t27, m9, g7);
}
function et3(t27, n28, e16, s20) {
  const { vertexAttributes: o22, indices: r28 } = st3(n28, e16, s20);
  return new v5(t27, o22, r28);
}
function st3(t27, n28, e16) {
  const s20 = t27;
  let o22, r28;
  if (e16) o22 = [0, -1, 0, 1, 0, 0, 0, 0, 1, -1, 0, 0, 0, 0, -1, 0, 1, 0], r28 = [0, 1, 2, 0, 2, 3, 0, 3, 4, 0, 4, 1, 1, 5, 2, 2, 5, 3, 3, 5, 4, 4, 5, 1];
  else {
    const t28 = s20 * (1 + Math.sqrt(5)) / 2;
    o22 = [-s20, t28, 0, s20, t28, 0, -s20, -t28, 0, s20, -t28, 0, 0, -s20, t28, 0, s20, t28, 0, -s20, -t28, 0, s20, -t28, t28, 0, -s20, t28, 0, s20, -t28, 0, -s20, -t28, 0, s20], r28 = [0, 11, 5, 0, 5, 1, 0, 1, 7, 0, 7, 10, 0, 10, 11, 1, 5, 9, 5, 11, 4, 11, 10, 2, 10, 7, 6, 7, 1, 8, 3, 9, 4, 3, 4, 2, 3, 2, 6, 3, 6, 8, 3, 8, 9, 4, 9, 5, 2, 4, 11, 6, 2, 10, 8, 6, 7, 9, 8, 1];
  }
  for (let a25 = 0; a25 < o22.length; a25 += 3) b6.scale(o22, a25, t27 / b6.length(o22, a25));
  let h16 = {};
  function l22(n29, e17) {
    n29 > e17 && ([n29, e17] = [e17, n29]);
    const s21 = n29.toString() + "." + e17.toString();
    if (h16[s21]) return h16[s21];
    let r29 = o22.length;
    return o22.length += 3, b6.add(o22, 3 * n29, o22, 3 * e17, o22, r29), b6.scale(o22, r29, t27 / b6.length(o22, r29)), r29 /= 3, h16[s21] = r29, r29;
  }
  for (let a25 = 0; a25 < n28; a25++) {
    const t28 = r28.length, n29 = new Array(4 * t28);
    for (let e17 = 0; e17 < t28; e17 += 3) {
      const t29 = r28[e17], s21 = r28[e17 + 1], o23 = r28[e17 + 2], h17 = l22(t29, s21), u13 = l22(s21, o23), c16 = l22(o23, t29), a26 = 4 * e17;
      n29[a26] = t29, n29[a26 + 1] = h17, n29[a26 + 2] = c16, n29[a26 + 3] = s21, n29[a26 + 4] = u13, n29[a26 + 5] = h17, n29[a26 + 6] = o23, n29[a26 + 7] = c16, n29[a26 + 8] = u13, n29[a26 + 9] = h17, n29[a26 + 10] = u13, n29[a26 + 11] = c16;
    }
    r28 = n29, h16 = {};
  }
  const u12 = t24(o22);
  for (let a25 = 0; a25 < u12.length; a25 += 3) b6.normalize(u12, a25);
  const c15 = [[O3.POSITION, r28], [O3.NORMAL, r28]];
  return { vertexAttributes: [[O3.POSITION, new s5(t24(o22), 3, true)], [O3.NORMAL, new s5(u12, 3, true)]], indices: c15 };
}
function ot2(n28, e16, s20, o22, r28, h16, l22, u12, c15 = null) {
  const a25 = s20 ? [s20[0], s20[1], s20[2]] : [0, 0, 0], f18 = e16 ? [e16[0], e16[1], e16[2]] : [0, 0, 1];
  l22 = l22 || [0, 0];
  const p13 = o22 ? [255 * o22[0], 255 * o22[1], 255 * o22[2], o22.length > 3 ? 255 * o22[3] : 255] : [255, 255, 255, 255], O10 = r(r28) && 2 === r28.length ? r28 : [1, 1], i21 = [[O3.POSITION, new s5(a25, 3, true)], [O3.NORMAL, new s5(f18, 3, true)], [O3.UV0, new s5(l22, l22.length)], [O3.COLOR, new s5(p13, 4, true)], [O3.SIZE, new s5(O10, 2)]];
  if (null != h16) {
    const t27 = [h16[0], h16[1], h16[2], h16[3]];
    i21.push([O3.AUXPOS1, new s5(t27, 4)]);
  }
  if (null != u12) {
    const t27 = [u12[0], u12[1], u12[2], u12[3]];
    i21.push([O3.AUXPOS2, new s5(t27, 4)]);
  }
  return new v5(n28, i21, null, null, e8.Point, c15);
}
function lt2(t27, n28, e16, s20, o22, r28 = true, h16 = true) {
  let l22 = 0;
  const u12 = e16, c15 = n28;
  let f18 = r8(0, l22, 0), p13 = r8(0, l22 + c15, 0), O10 = r8(0, -1, 0), i21 = r8(0, 1, 0);
  o22 && (l22 = c15, p13 = r8(0, 0, 0), f18 = r8(0, l22, 0), O10 = r8(0, 1, 0), i21 = r8(0, -1, 0));
  const I9 = [p13, f18], w4 = [O10, i21], M9 = s20 + 2, g7 = Math.sqrt(c15 * c15 + u12 * u12);
  if (o22) for (let A10 = s20 - 1; A10 >= 0; A10--) {
    const t28 = A10 * (2 * Math.PI / s20), n29 = r8(Math.cos(t28) * u12, l22, Math.sin(t28) * u12);
    I9.push(n29);
    const e17 = r8(c15 * Math.cos(t28) / g7, -u12 / g7, c15 * Math.sin(t28) / g7);
    w4.push(e17);
  }
  else for (let A10 = 0; A10 < s20; A10++) {
    const t28 = A10 * (2 * Math.PI / s20), n29 = r8(Math.cos(t28) * u12, l22, Math.sin(t28) * u12);
    I9.push(n29);
    const e17 = r8(c15 * Math.cos(t28) / g7, u12 / g7, c15 * Math.sin(t28) / g7);
    w4.push(e17);
  }
  const m9 = new Array(), N7 = new Array();
  if (r28) {
    for (let t28 = 3; t28 < I9.length; t28++) m9.push(1), m9.push(t28 - 1), m9.push(t28), N7.push(0), N7.push(0), N7.push(0);
    m9.push(I9.length - 1), m9.push(2), m9.push(1), N7.push(0), N7.push(0), N7.push(0);
  }
  if (h16) {
    for (let t28 = 3; t28 < I9.length; t28++) m9.push(t28), m9.push(t28 - 1), m9.push(0), N7.push(t28), N7.push(t28 - 1), N7.push(1);
    m9.push(0), m9.push(2), m9.push(I9.length - 1), N7.push(1), N7.push(2), N7.push(w4.length - 1);
  }
  const y9 = n25(3 * M9);
  for (let a25 = 0; a25 < M9; a25++) y9[3 * a25] = I9[a25][0], y9[3 * a25 + 1] = I9[a25][1], y9[3 * a25 + 2] = I9[a25][2];
  const T8 = n25(3 * M9);
  for (let a25 = 0; a25 < M9; a25++) T8[3 * a25] = w4[a25][0], T8[3 * a25 + 1] = w4[a25][1], T8[3 * a25 + 2] = w4[a25][2];
  const L5 = [[O3.POSITION, m9], [O3.NORMAL, N7]], b9 = [[O3.POSITION, new s5(y9, 3, true)], [O3.NORMAL, new s5(T8, 3, true)]];
  return new v5(t27, b9, L5);
}
function ut2(t27, n28, u12, c15, O10, i21, I9) {
  const w4 = O10 ? t6(O10) : r8(1, 0, 0), M9 = i21 ? t6(i21) : r8(0, 0, 0);
  I9 = I9 ?? true;
  const g7 = n11();
  z2(g7, w4);
  const m9 = n11();
  g(m9, g7, Math.abs(n28));
  const N7 = n11();
  g(N7, m9, -0.5), u3(N7, N7, M9);
  const y9 = r8(0, 1, 0);
  Math.abs(1 - P(g7, y9)) < 0.2 && o2(y9, 0, 0, 1);
  const T8 = n11();
  _(T8, g7, y9), z2(T8, T8), _(y9, T8, g7);
  const L5 = 2 * c15 + (I9 ? 2 : 0), b9 = c15 + (I9 ? 2 : 0), j12 = n25(3 * L5), d11 = n25(3 * b9), U6 = n25(2 * L5), V9 = new Array(3 * c15 * (I9 ? 4 : 2)), v13 = new Array(3 * c15 * (I9 ? 4 : 2));
  I9 && (j12[3 * (L5 - 2) + 0] = N7[0], j12[3 * (L5 - 2) + 1] = N7[1], j12[3 * (L5 - 2) + 2] = N7[2], U6[2 * (L5 - 2)] = 0, U6[2 * (L5 - 2) + 1] = 0, j12[3 * (L5 - 1) + 0] = j12[3 * (L5 - 2) + 0] + m9[0], j12[3 * (L5 - 1) + 1] = j12[3 * (L5 - 2) + 1] + m9[1], j12[3 * (L5 - 1) + 2] = j12[3 * (L5 - 2) + 2] + m9[2], U6[2 * (L5 - 1)] = 1, U6[2 * (L5 - 1) + 1] = 1, d11[3 * (b9 - 2) + 0] = -g7[0], d11[3 * (b9 - 2) + 1] = -g7[1], d11[3 * (b9 - 2) + 2] = -g7[2], d11[3 * (b9 - 1) + 0] = g7[0], d11[3 * (b9 - 1) + 1] = g7[1], d11[3 * (b9 - 1) + 2] = g7[2]);
  const x13 = (t28, n29, e16) => {
    V9[t28] = n29, v13[t28] = e16;
  };
  let C8 = 0;
  const F6 = n11(), G9 = n11();
  for (let e16 = 0; e16 < c15; e16++) {
    const t28 = e16 * (2 * Math.PI / c15);
    g(F6, y9, Math.sin(t28)), g(G9, T8, Math.cos(t28)), u3(F6, F6, G9), d11[3 * e16 + 0] = F6[0], d11[3 * e16 + 1] = F6[1], d11[3 * e16 + 2] = F6[2], g(F6, F6, u12), u3(F6, F6, N7), j12[3 * e16 + 0] = F6[0], j12[3 * e16 + 1] = F6[1], j12[3 * e16 + 2] = F6[2], U6[2 * e16 + 0] = e16 / c15, U6[2 * e16 + 1] = 0, j12[3 * (e16 + c15) + 0] = j12[3 * e16 + 0] + m9[0], j12[3 * (e16 + c15) + 1] = j12[3 * e16 + 1] + m9[1], j12[3 * (e16 + c15) + 2] = j12[3 * e16 + 2] + m9[2], U6[2 * (e16 + c15) + 0] = e16 / c15, U6[2 * e16 + 1] = 1;
    const n29 = (e16 + 1) % c15;
    x13(C8++, e16, e16), x13(C8++, e16 + c15, e16), x13(C8++, n29, n29), x13(C8++, n29, n29), x13(C8++, e16 + c15, e16), x13(C8++, n29 + c15, n29);
  }
  if (I9) {
    for (let t28 = 0; t28 < c15; t28++) {
      const n29 = (t28 + 1) % c15;
      x13(C8++, L5 - 2, b9 - 2), x13(C8++, t28, b9 - 2), x13(C8++, n29, b9 - 2);
    }
    for (let t28 = 0; t28 < c15; t28++) {
      const n29 = (t28 + 1) % c15;
      x13(C8++, t28 + c15, b9 - 1), x13(C8++, L5 - 1, b9 - 1), x13(C8++, n29 + c15, b9 - 1);
    }
  }
  const k8 = [[O3.POSITION, V9], [O3.NORMAL, v13], [O3.UV0, V9]], E14 = [[O3.POSITION, new s5(j12, 3, true)], [O3.NORMAL, new s5(d11, 3, true)], [O3.UV0, new s5(U6, 2, true)]];
  return new v5(t27, E14, k8);
}
function ct2(t27, n28, e16, s20, o22, r28) {
  s20 = s20 || 10, o22 = null == o22 || o22, s6(n28.length > 1);
  const h16 = [[0, 0, 0]], l22 = [], u12 = [];
  for (let c15 = 0; c15 < s20; c15++) {
    l22.push([0, -c15 - 1, -(c15 + 1) % s20 - 1]);
    const t28 = c15 / s20 * 2 * Math.PI;
    u12.push([Math.cos(t28) * e16, Math.sin(t28) * e16]);
  }
  return at2(t27, u12, n28, h16, l22, o22, r28);
}
function at2(t27, h16, c15, p13, g7, m9, N7 = r8(0, 0, 0)) {
  const y9 = h16.length, T8 = n25(c15.length * y9 * 3 + (6 * p13.length || 0)), L5 = n25(c15.length * y9 * 3 + (p13 ? 6 : 0)), b9 = new Array(), j12 = new Array();
  let d11 = 0, U6 = 0;
  const V9 = n11(), v13 = n11(), x13 = n11(), C8 = n11(), F6 = n11(), G9 = n11(), k8 = n11(), E14 = n4(), q5 = n11(), X5 = n11(), z9 = n11(), B5 = n11(), D5 = n11(), Z7 = p2();
  o2(q5, 0, 1, 0), e5(v13, c15[1], c15[0]), z2(v13, v13), m9 ? (u3(E14, c15[0], N7), z2(x13, E14)) : o2(x13, 0, 0, 1), wt2(v13, x13, q5, q5, F6, x13, Mt2), r3(C8, x13), r3(B5, F6);
  for (let n28 = 0; n28 < p13.length; n28++) g(G9, F6, p13[n28][0]), g(E14, x13, p13[n28][2]), u3(G9, G9, E14), u3(G9, G9, c15[0]), T8[d11++] = G9[0], T8[d11++] = G9[1], T8[d11++] = G9[2];
  L5[U6++] = -v13[0], L5[U6++] = -v13[1], L5[U6++] = -v13[2];
  for (let n28 = 0; n28 < g7.length; n28++) b9.push(g7[n28][0] > 0 ? g7[n28][0] : -g7[n28][0] - 1 + p13.length), b9.push(g7[n28][1] > 0 ? g7[n28][1] : -g7[n28][1] - 1 + p13.length), b9.push(g7[n28][2] > 0 ? g7[n28][2] : -g7[n28][2] - 1 + p13.length), j12.push(0), j12.push(0), j12.push(0);
  let H11 = p13.length;
  const J5 = p13.length - 1;
  for (let l22 = 0; l22 < c15.length; l22++) {
    let t28 = false;
    if (l22 > 0) {
      r3(V9, v13), l22 < c15.length - 1 ? (e5(v13, c15[l22 + 1], c15[l22]), z2(v13, v13)) : t28 = true, u3(X5, V9, v13), z2(X5, X5), u3(z9, c15[l22 - 1], C8), _5(c15[l22], X5, Z7);
      x5(Z7, p(z9, V9), E14) ? (e5(E14, E14, c15[l22]), z2(x13, E14), _(F6, X5, x13), z2(F6, F6)) : wt2(X5, C8, B5, q5, F6, x13, Mt2), r3(C8, x13), r3(B5, F6);
    }
    m9 && (u3(E14, c15[l22], N7), z2(D5, E14));
    for (let n28 = 0; n28 < y9; n28++) if (g(G9, F6, h16[n28][0]), g(E14, x13, h16[n28][1]), u3(G9, G9, E14), z2(k8, G9), L5[U6++] = k8[0], L5[U6++] = k8[1], L5[U6++] = k8[2], u3(G9, G9, c15[l22]), T8[d11++] = G9[0], T8[d11++] = G9[1], T8[d11++] = G9[2], !t28) {
      const t29 = (n28 + 1) % y9;
      b9.push(H11 + n28), b9.push(H11 + y9 + n28), b9.push(H11 + t29), b9.push(H11 + t29), b9.push(H11 + y9 + n28), b9.push(H11 + y9 + t29);
      for (let n29 = 0; n29 < 6; n29++) {
        const t30 = b9.length - 6;
        j12.push(b9[t30 + n29] - J5);
      }
    }
    H11 += y9;
  }
  const K5 = c15[c15.length - 1];
  for (let n28 = 0; n28 < p13.length; n28++) g(G9, F6, p13[n28][0]), g(E14, x13, p13[n28][1]), u3(G9, G9, E14), u3(G9, G9, K5), T8[d11++] = G9[0], T8[d11++] = G9[1], T8[d11++] = G9[2];
  const Q6 = U6 / 3;
  L5[U6++] = v13[0], L5[U6++] = v13[1], L5[U6++] = v13[2];
  const W7 = H11 - y9;
  for (let n28 = 0; n28 < g7.length; n28++) b9.push(g7[n28][0] >= 0 ? H11 + g7[n28][0] : -g7[n28][0] - 1 + W7), b9.push(g7[n28][2] >= 0 ? H11 + g7[n28][2] : -g7[n28][2] - 1 + W7), b9.push(g7[n28][1] >= 0 ? H11 + g7[n28][1] : -g7[n28][1] - 1 + W7), j12.push(Q6), j12.push(Q6), j12.push(Q6);
  const Y6 = [[O3.POSITION, b9], [O3.NORMAL, j12]], $8 = [[O3.POSITION, new s5(T8, 3, true)], [O3.NORMAL, new s5(L5, 3, true)]];
  return new v5(t27, $8, Y6);
}
function ft2(t27, n28, e16, s20) {
  s6(n28.length > 1, "createPolylineGeometry(): polyline needs at least 2 points"), s6(3 === n28[0].length, "createPolylineGeometry(): malformed vertex"), s6(null == e16 || e16.length === n28.length, "createPolylineGeometry: need same number of points and normals"), s6(null == e16 || 3 === e16[0].length, "createPolylineGeometry(): malformed normal");
  const o22 = n9(3 * n28.length), r28 = new Array(2 * (n28.length - 1));
  let h16 = 0, l22 = 0;
  for (let a25 = 0; a25 < n28.length; a25++) {
    for (let t28 = 0; t28 < 3; t28++) o22[h16++] = n28[a25][t28];
    a25 > 0 && (r28[l22++] = a25 - 1, r28[l22++] = a25);
  }
  const u12 = [], c15 = [];
  if (u12.push([O3.POSITION, r28]), c15.push([O3.POSITION, new s5(o22, 3, true)]), e16) {
    const t28 = n25(3 * e16.length);
    let s21 = 0;
    for (let o23 = 0; o23 < n28.length; o23++) for (let n29 = 0; n29 < 3; n29++) t28[s21++] = e16[o23][n29];
    u12.push([O3.NORMAL, r28]), c15.push([O3.NORMAL, new s5(t28, 3, true)]);
  }
  return s20 && (c15.push([O3.COLOR, new s5(s20, 4)]), u12.push([O3.COLOR, o6(s20.length / 4)])), new v5(t27, c15, u12, null, e8.Line);
}
function pt2(t27, n28, e16, s20, o22, r28 = 0) {
  const h16 = new Array(18), l22 = [[-e16, r28, o22 / 2], [s20, r28, o22 / 2], [0, n28 + r28, o22 / 2], [-e16, r28, -o22 / 2], [s20, r28, -o22 / 2], [0, n28 + r28, -o22 / 2]], u12 = [0, 1, 2, 3, 0, 2, 2, 5, 3, 1, 4, 5, 5, 2, 1, 1, 0, 3, 3, 4, 1, 4, 3, 5];
  for (let c15 = 0; c15 < 6; c15++) h16[3 * c15] = l22[c15][0], h16[3 * c15 + 1] = l22[c15][1], h16[3 * c15 + 2] = l22[c15][2];
  return new v5(t27, [[O3.POSITION, new s5(h16, 3, true)]], [[O3.POSITION, u12]]);
}
function Ot(t27, n28) {
  const e16 = t27.getMutableAttribute(O3.POSITION).data;
  for (let s20 = 0; s20 < e16.length; s20 += 3) {
    const t28 = e16[s20], o22 = e16[s20 + 1], r28 = e16[s20 + 2];
    o2(At, t28, o22, r28), O2(At, At, n28), e16[s20] = At[0], e16[s20 + 1] = At[1], e16[s20 + 2] = At[2];
  }
}
function it3(t27, n28 = t27) {
  const e16 = t27.vertexAttributes, s20 = e16.get(O3.POSITION).data, o22 = e16.get(O3.NORMAL).data;
  if (o22) {
    const t28 = n28.getMutableAttribute(O3.NORMAL).data;
    for (let n29 = 0; n29 < o22.length; n29 += 3) {
      const e17 = o22[n29 + 1];
      t28[n29 + 1] = -o22[n29 + 2], t28[n29 + 2] = e17;
    }
  }
  if (s20) {
    const t28 = n28.getMutableAttribute(O3.POSITION).data;
    for (let n29 = 0; n29 < s20.length; n29 += 3) {
      const e17 = s20[n29 + 1];
      t28[n29 + 1] = -s20[n29 + 2], t28[n29 + 2] = e17;
    }
  }
}
function It(t27, n28, o22, r28, l22) {
  return !(Math.abs(P(n28, t27)) > l22) && (_(o22, t27, n28), z2(o22, o22), _(r28, o22, t27), z2(r28, r28), true);
}
function wt2(t27, n28, e16, s20, o22, r28, h16) {
  return It(t27, n28, o22, r28, h16) || It(t27, e16, o22, r28, h16) || It(t27, s20, o22, r28, h16);
}
var Mt2 = 0.99619469809;
var At = n11();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/RenderGeometry.js
var h14 = class {
  constructor(r28, t27 = {}) {
    this.geometry = r28, this.boundingSphere = n5(), this.screenToWorldRatio = 1, this._transformation = e7(), this._shaderTransformationDirty = true, this.id = e3(), this.layerUid = t27.layerUid, this.graphicUid = t27.graphicUid, this.boundingInfo = t27.boundingInfo, this.shaderTransformer = t27.shaderTransformer, this.castShadow = !!t27.castShadow && t27.castShadow;
  }
  get transformation() {
    return this._transformation;
  }
  updateTransformation(r28) {
    r28(this._transformation), this._shaderTransformationDirty = true, this.computeBoundingSphere(this._transformation, this.boundingSphere);
  }
  shaderTransformationChanged() {
    this._shaderTransformationDirty = true;
  }
  computeBoundingSphere(t27, s20, i21 = l12(t27)) {
    t(this.boundingInfo) || (O2(s20, this.boundingInfo.center, t27), s20[3] = this.boundingInfo.radius * i21);
  }
  get hasShaderTransformation() {
    return r(this.shaderTransformer);
  }
  get material() {
    return this.geometry.material;
  }
  get type() {
    return this.geometry.type;
  }
  get shaderTransformation() {
    return t(this.shaderTransformer) ? this.transformation : (this._shaderTransformationDirty && (this._shaderTransformation || (this._shaderTransformation = e7()), n6(this._shaderTransformation, this.shaderTransformer(this.transformation)), this._shaderTransformationDirty = false), this._shaderTransformation);
  }
  get indices() {
    return this.geometry.indices;
  }
  get vertexAttributes() {
    return this.geometry.vertexAttributes;
  }
  get highlights() {
    return this.geometry.highlights;
  }
  get occludees() {
    return this.geometry.occludees;
  }
  get visible() {
    return this.geometry.visible;
  }
  set visible(r28) {
    this.geometry.visible = r28;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/ColorMaterialTechnique.js
var j9 = class _j extends e9 {
  initializeConfiguration(e16, t27) {
    t27.hasWebGL2Context = e16.rctx.type === r12.WEBGL2;
  }
  initializeProgram(e16) {
    return new o12(e16.rctx, _j.shader.get().build(this.configuration), E5);
  }
  _createPipeline(t27, r28) {
    const i21 = this.configuration, a25 = t27 === o11.NONE, f18 = t27 === o11.FrontFace;
    return W({ blending: i21.output !== h6.Color && i21.output !== h6.Alpha || !i21.transparent ? null : a25 ? c5 : A4(t27), culling: h9(i21.cullFace), depthTest: { func: l16(t27) }, depthWrite: (a25 || f18) && i21.writeDepth ? a11 : null, colorWrite: _6, stencilWrite: i21.hasOccludees ? e10 : null, stencilTest: i21.hasOccludees ? r28 ? o13 : f7 : null, polygonOffset: a25 || f18 ? i21.polygonOffset ? y7 : null : a12(i21.enableOffset) });
  }
  initializePipeline() {
    return this._occludeePipelineState = this._createPipeline(this.configuration.transparencyPassType, true), this._createPipeline(this.configuration.transparencyPassType, false);
  }
  getPipelineState(e16, t27) {
    return t27 ? this._occludeePipelineState : super.getPipelineState(e16, t27);
  }
};
j9.shader = new t9(f9, () => import("./ColorMaterial.glsl-4LWJIFOL.js"));
var y7 = { factor: 1, units: 1 };

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/ColorMaterialTechniqueConfiguration.js
var p11 = class extends s9 {
  constructor() {
    super(...arguments), this.output = h6.Color, this.cullFace = n10.None, this.hasSlicePlane = false, this.hasVertexColors = false, this.transparent = false, this.polygonOffset = false, this.enableOffset = true, this.writeDepth = true, this.hasOccludees = false, this.transparencyPassType = o11.NONE, this.hasMultipassTerrain = false, this.cullAboveGround = false, this.objectAndLayerIdColorInstanced = false;
  }
};
e([r15({ count: h6.COUNT })], p11.prototype, "output", void 0), e([r15({ count: n10.COUNT })], p11.prototype, "cullFace", void 0), e([r15()], p11.prototype, "hasSlicePlane", void 0), e([r15()], p11.prototype, "hasVertexColors", void 0), e([r15()], p11.prototype, "transparent", void 0), e([r15()], p11.prototype, "polygonOffset", void 0), e([r15()], p11.prototype, "enableOffset", void 0), e([r15()], p11.prototype, "writeDepth", void 0), e([r15()], p11.prototype, "hasOccludees", void 0), e([r15({ count: o11.COUNT })], p11.prototype, "transparencyPassType", void 0), e([r15()], p11.prototype, "hasMultipassTerrain", void 0), e([r15()], p11.prototype, "cullAboveGround", void 0), e([r15()], p11.prototype, "objectAndLayerIdColorInstanced", void 0);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/ColorMaterial.js
var f16 = class extends e15 {
  constructor(e16) {
    super(e16, new d9()), this.supportsEdges = true, this._configuration = new p11();
  }
  getConfiguration(e16, t27) {
    return this._configuration.output = e16, this._configuration.cullFace = this.parameters.cullFace, this._configuration.hasVertexColors = this.parameters.hasVertexColors, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.transparent = this.parameters.transparent, this._configuration.polygonOffset = this.parameters.polygonOffset, this._configuration.writeDepth = this.parameters.writeDepth, this._configuration.hasOccludees = this.parameters.hasOccludees, this._configuration.transparencyPassType = t27.transparencyPassType, this._configuration.enableOffset = t27.camera.relativeElevation < S2, this._configuration.hasMultipassTerrain = t27.multipassTerrain.enabled, this._configuration.cullAboveGround = t27.multipassTerrain.cullAboveGround, this._configuration;
  }
  requiresSlot(e16, r28) {
    if (r28 === h6.Color || r28 === h6.Alpha || r28 === h6.Highlight || r28 === h6.Depth && this.parameters.writeLinearDepth || r28 === h6.ObjectAndLayerIdColor) {
      if (e16 === E8.DRAPED_MATERIAL) return true;
      if (r28 === h6.Highlight) return e16 === E8.OPAQUE_MATERIAL;
      return e16 === (this.parameters.transparent ? this.parameters.writeDepth ? E8.TRANSPARENT_MATERIAL : E8.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL : E8.OPAQUE_MATERIAL);
    }
    return false;
  }
  createGLMaterial(e16) {
    return new m8(e16);
  }
  createBufferWriter() {
    return new r14(has("enable-feature:objectAndLayerId-rendering") ? r21 : v10);
  }
};
var m8 = class extends t8 {
  _updateOccludeeState(e16) {
    e16.hasOccludees !== this._material.parameters.hasOccludees && this._material.setParameters({ hasOccludees: e16.hasOccludees });
  }
  beginSlot(e16) {
    return this._output !== h6.Color && this._output !== h6.Alpha || this._updateOccludeeState(e16), this.ensureTechnique(j9, e16);
  }
};
var d9 = class extends u7 {
  constructor() {
    super(...arguments), this.color = l8, this.transparent = false, this.writeDepth = true, this.writeLinearDepth = false, this.hasVertexColors = false, this.polygonOffset = false, this.hasSlicePlane = false, this.cullFace = n10.None, this.hasOccludees = false;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/lineStippleUtils.js
var d10 = { dash: [4, 3], dot: [1, 3], "long-dash": [8, 3], "short-dash": [4, 1], "short-dot": [1, 1] };
var s18 = { dash: d10.dash, "dash-dot": [...d10.dash, ...d10.dot], dot: d10.dot, "long-dash": d10["long-dash"], "long-dash-dot": [...d10["long-dash"], ...d10.dot], "long-dash-dot-dot": [...d10["long-dash"], ...d10.dot, ...d10.dot], none: null, "short-dash": d10["short-dash"], "short-dash-dot": [...d10["short-dash"], ...d10["short-dot"]], "short-dash-dot-dot": [...d10["short-dash"], ...d10["short-dot"], ...d10["short-dot"]], "short-dot": d10["short-dot"], solid: null };
var h15 = 8;
function n26(t27, d11 = 2) {
  return t(t27) ? t27 : { pattern: t27.slice(), pixelRatio: d11 };
}
function r25(o22, t27 = 2) {
  return { pattern: [o22, o22], pixelRatio: t27 };
}
function a22(o22) {
  return r(o22) && "style" === o22.type ? l21(o22.style) : null;
}
function l21(o22) {
  return r(o22) ? n26(s18[o22], h15) : null;
}

// node_modules/@arcgis/core/views/3d/support/engineContent/line.js
function b7(t27, e16, n28 = null) {
  const o22 = [], r28 = [], i21 = e16.mapPositions;
  g6(e16, r28, o22);
  const a25 = r28[0][1].data, s20 = o22[0][1].length, u12 = new Array(s20).fill(0);
  return E13(e16, r28, o22, u12), O9(e16, r28, o22, u12), A9(e16, r28, o22, u12), I8(e16, r28, o22, u12), R8(e16, r28, o22, u12), w3(e16, r28, o22, u12), j10(e16, r28, o22, a25), new v5(t27, r28, o22, i21, e8.Line, n28);
}
function g6(t27, e16, n28) {
  const { attributeData: { position: o22 }, removeDuplicateStartEnd: r28 } = t27, i21 = D4(o22) && r28, a25 = o22.length / 3 - (i21 ? 1 : 0), s20 = new Array(2 * (a25 - 1)), u12 = i21 ? o22.slice(0, o22.length - 3) : o22;
  let l22 = 0;
  for (let c15 = 0; c15 < a25 - 1; c15++) s20[l22++] = c15, s20[l22++] = c15 + 1;
  e16.push([O3.POSITION, new s5(u12, 3, i21)]), n28.push([O3.POSITION, s20]);
}
function E13(n28, o22, r28, i21) {
  if (r(n28.attributeData.colorFeature)) return;
  const s20 = n28.attributeData.color;
  o22.push([O3.COLOR, new s5(l(s20, _2), 4)]), r28.push([O3.COLOR, i21]);
}
function A9(e16, n28, o22, r28) {
  if (!r(e16.attributeData.normal)) return;
  const i21 = e16.attributeData.normal;
  n28.push([O3.NORMAL, new s5(i21, 3)]), o22.push([O3.NORMAL, r28]);
}
function I8(t27, e16, o22, r28) {
  const i21 = t27.attributeData.colorFeature;
  t(i21) || (e16.push([O3.COLORFEATUREATTRIBUTE, new s5([i21], 1, true)]), o22.push([O3.COLOR, r28]));
}
function O9(n28, o22, r28, i21) {
  if (r(n28.attributeData.sizeFeature)) return;
  const a25 = n28.attributeData.size;
  o22.push([O3.SIZE, new s5([l(a25, 1)], 1, true)]), r28.push([O3.SIZE, i21]);
}
function R8(t27, e16, o22, r28) {
  const i21 = t27.attributeData.sizeFeature;
  t(i21) || (e16.push([O3.SIZEFEATUREATTRIBUTE, new s5([i21], 1, true)]), o22.push([O3.SIZEFEATUREATTRIBUTE, r28]));
}
function w3(t27, e16, o22, r28) {
  const i21 = t27.attributeData.opacityFeature;
  t(i21) || (e16.push([O3.OPACITYFEATUREATTRIBUTE, new s5([i21], 1, true)]), o22.push([O3.OPACITYFEATUREATTRIBUTE, r28]));
}
function j10(t27, e16, i21, a25) {
  if (t(t27.overlayInfo) || t27.overlayInfo.renderCoordsHelper.viewingMode !== l13.Global || !t27.overlayInfo.spatialReference.isGeographic) return;
  const p13 = n9(a25.length), h16 = O(t27.overlayInfo.spatialReference);
  for (let n28 = 0; n28 < p13.length; n28 += 3) ee(a25, n28, p13, n28, h16);
  const b9 = a25.length / 3, g7 = n25(b9 + 1);
  let E14 = y8, A10 = S6, I9 = 0, O10 = 0;
  o2(E14, p13[O10++], p13[O10++], p13[O10++]), g7[0] = 0;
  for (let n28 = 1; n28 < b9 + 1; ++n28) n28 === b9 && (O10 = 0), o2(A10, p13[O10++], p13[O10++], p13[O10++]), I9 += H3(E14, A10), g7[n28] = I9, [E14, A10] = [A10, E14];
  e16.push([O3.DISTANCETOSTART, new s5(g7, 1, true)]), i21.push([O3.DISTANCETOSTART, i21[0][1]]);
}
function D4(t27) {
  const e16 = t27.length;
  return t27[0] === t27[e16 - 3] && t27[1] === t27[e16 - 2] && t27[2] === t27[e16 - 1];
}
var y8 = n4();
var S6 = n4();

// node_modules/@arcgis/core/views/3d/support/renderInfoUtils/line.js
function p12(o22, e16, i21, l22) {
  const p13 = "polygon" === o22.type ? f2.CCW_IS_HOLE : f2.NONE, c15 = "polygon" === o22.type ? o22.rings : o22.paths, { position: m9, outlines: u12 } = l11(c15, !!o22.hasZ, p13), g7 = n9(m9.length), f18 = c7(m9, o22.spatialReference, 0, g7, 0, m9, 0, m9.length / 3, e16, i21, l22), y9 = null != f18;
  return { lines: y9 ? a23(u12, m9, g7) : [], projectionSuccess: y9, sampledElevation: f18 };
}
function c14(o22, r28) {
  const s20 = "polygon" === o22.type ? f2.CCW_IS_HOLE : f2.NONE, l22 = "polygon" === o22.type ? o22.rings : o22.paths, { position: p13, outlines: c15 } = l11(l22, false, s20), m9 = xn(p13, o22.spatialReference, 0, p13, r28, 0, p13.length / 3);
  for (let e16 = 2; e16 < p13.length; e16 += 3) p13[e16] = ce2;
  return { lines: m9 ? a23(c15, p13) : [], projectionSuccess: m9 };
}
function a23(e16, n28, t27 = null) {
  const r28 = new Array();
  for (const { index: i21, count: s20 } of e16) {
    if (s20 <= 1) continue;
    const e17 = 3 * i21, p13 = 3 * s20;
    r28.push({ position: a9(n28, 3 * i21, 3 * s20), mapPositions: r(t27) ? a9(t27, e17, p13) : void 0 });
  }
  return r28;
}

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/NativeLineTechnique.js
var f17 = class _f extends e9 {
  get _stippleEnabled() {
    return this.configuration.stippleEnabled && this.configuration.output !== h6.Highlight;
  }
  initializeConfiguration(e16, t27) {
    t27.hasWebGL2Context = e16.rctx.type === r12.WEBGL2;
  }
  initializeProgram(e16) {
    return new o12(e16.rctx, _f.shader.get().build(this.configuration), E5);
  }
  initializePipeline() {
    const t27 = this.configuration, i21 = l15(R2.SRC_ALPHA, R2.ONE, R2.ONE_MINUS_SRC_ALPHA, R2.ONE_MINUS_SRC_ALPHA), r28 = (e16, i22 = null, r29 = null) => W({ blending: i22, depthTest: i8, depthWrite: r29, colorWrite: _6, stencilWrite: t27.hasOccludees ? e10 : null, stencilTest: t27.hasOccludees ? e16 ? o13 : f7 : null });
    return t27.output === h6.Color ? (this._occludeePipelineState = r28(true, t27.transparent || this._stippleEnabled ? i21 : null, a11), r28(false, t27.transparent || this._stippleEnabled ? i21 : null, a11)) : r28(false);
  }
  get primitiveType() {
    return E3.LINES;
  }
  getPipelineState(e16, t27) {
    return t27 ? this._occludeePipelineState : super.getPipelineState(e16, t27);
  }
};
f17.shader = new t9(v8, () => import("./NativeLine.glsl-IUU7HHH2.js"));

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/NativeLineTechniqueConfiguration.js
var r26 = class extends s9 {
  constructor() {
    super(...arguments), this.output = h6.Color, this.hasSlicePlane = false, this.hasVertexColors = false, this.transparent = false, this.draped = false, this.stippleEnabled = false, this.stippleOffColorEnabled = false, this.stipplePreferContinuous = true, this.hasOccludees = false;
  }
};
e([r15({ count: h6.COUNT })], r26.prototype, "output", void 0), e([r15()], r26.prototype, "hasSlicePlane", void 0), e([r15()], r26.prototype, "hasVertexColors", void 0), e([r15()], r26.prototype, "transparent", void 0), e([r15()], r26.prototype, "draped", void 0), e([r15()], r26.prototype, "stippleEnabled", void 0), e([r15()], r26.prototype, "stippleOffColorEnabled", void 0), e([r15()], r26.prototype, "stipplePreferContinuous", void 0), e([r15()], r26.prototype, "hasOccludees", void 0), e([r15({ constValue: false })], r26.prototype, "stippleRequiresClamp", void 0), e([r15({ constValue: false })], r26.prototype, "stippleScaleWithLineWidth", void 0), e([r15({ constValue: false })], r26.prototype, "stippleRequiresStretchMeasure", void 0);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/NativeLineMaterial.js
var k7;
!function(e16) {
  e16[e16.START = 0] = "START", e16[e16.END = 1] = "END";
}(k7 || (k7 = {}));
var q4 = class extends h8 {
  constructor(e16) {
    super(e16, new G8()), this._configuration = new r26();
  }
  getConfiguration(e16, t27) {
    this._configuration.output = e16, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.hasVertexColors = this.parameters.hasVertexColors, this._configuration.transparent = this.parameters.color[3] < 1 || this.parameters.width < 1, this._configuration.draped = t27.slot === E8.DRAPED_MATERIAL;
    const s20 = r(this.parameters.stipplePattern);
    return this._configuration.stippleEnabled = s20, this._configuration.stippleOffColorEnabled = s20 && r(this.parameters.stippleOffColor), this._configuration.hasOccludees = this.parameters.hasOccludees, this._configuration.stipplePreferContinuous = this.parameters.stipplePreferContinuous, this._configuration;
  }
  intersect(t27, r28, s20, i21, h16, d11) {
    if (!s20.options.selectionMode || !t27.visible) return;
    if (!l14(r28)) return void s.getLogger("esri.views.3d.webgl-engine.materials.NativeLineMaterial").error("intersection assumes a translation-only matrix");
    const g7 = t27.vertexAttributes.get(O3.POSITION).data, _11 = s20.camera, P9 = re2;
    a7(P9, s20.point);
    const y9 = 2;
    o2(se2[0], P9[0] - y9, P9[1] + y9, 0), o2(se2[1], P9[0] + y9, P9[1] + y9, 0), o2(se2[2], P9[0] + y9, P9[1] - y9, 0), o2(se2[3], P9[0] - y9, P9[1] - y9, 0);
    for (let e16 = 0; e16 < 4; e16++) if (!_11.unprojectFromRenderScreen(se2[e16], ie2[e16])) return;
    j3(_11.eye, ie2[0], ie2[1], oe2), j3(_11.eye, ie2[1], ie2[2], ae2), j3(_11.eye, ie2[2], ie2[3], ne2), j3(_11.eye, ie2[3], ie2[0], ce3);
    let v13 = Number.MAX_VALUE, R9 = 0;
    for (let e16 = 0; e16 < g7.length - 5; e16 += 3) {
      if (H10[0] = g7[e16] + r28[12], H10[1] = g7[e16 + 1] + r28[13], H10[2] = g7[e16 + 2] + r28[14], Q5[0] = g7[e16 + 3] + r28[12], Q5[1] = g7[e16 + 4] + r28[13], Q5[2] = g7[e16 + 5] + r28[14], V2(oe2, H10) < 0 && V2(oe2, Q5) < 0 || V2(ae2, H10) < 0 && V2(ae2, Q5) < 0 || V2(ne2, H10) < 0 && V2(ne2, Q5) < 0 || V2(ce3, H10) < 0 && V2(ce3, Q5) < 0) continue;
      if (_11.projectToRenderScreen(H10, J4), _11.projectToRenderScreen(Q5, K4), J4[2] < 0 && K4[2] > 0) {
        e5(Z6, H10, Q5);
        const e17 = _11.frustum, t29 = -V2(e17[U.NEAR], H10) / P(Z6, Y2(e17[U.NEAR]));
        g(Z6, Z6, t29), u3(H10, H10, Z6), _11.projectToRenderScreen(H10, J4);
      } else if (J4[2] > 0 && K4[2] < 0) {
        e5(Z6, Q5, H10);
        const e17 = _11.frustum, t29 = -V2(e17[U.NEAR], Q5) / P(Z6, Y2(e17[U.NEAR]));
        g(Z6, Z6, t29), u3(Q5, Q5, Z6), _11.projectToRenderScreen(Q5, K4);
      } else if (J4[2] < 0 && K4[2] < 0) continue;
      J4[2] = 0, K4[2] = 0;
      const t28 = M3(b4(J4, K4, ee3), P9);
      t28 < v13 && (v13 = t28, r3(Y5, H10), r3($6, Q5), R9 = e16 / 3);
    }
    const E14 = s20.rayBegin, N7 = s20.rayEnd;
    if (v13 < y9 * y9) {
      let e16 = Number.MAX_VALUE;
      if (k3(b4(Y5, $6, ee3), b4(E14, N7, te2), z8)) {
        e5(z8, z8, E14);
        const t28 = s2(z8);
        g(z8, z8, 1 / t28), e16 = t28 / x(E14, N7);
      }
      d11(e16, z8, R9, false);
    }
  }
  intersectDraped(e16, r28, s20, i21, o22, a25) {
    if (!s20.options.selectionMode) return;
    const n28 = e16.vertexAttributes.get(O3.POSITION).data, c15 = e16.vertexAttributes.get(O3.SIZE), l22 = c15 ? c15.data[0] : 0, u12 = i21[0], p13 = i21[1], f18 = ((l22 + 1) / 2 + 4) * e16.screenToWorldRatio;
    let m9 = Number.MAX_VALUE, h16 = 0;
    for (let d11 = 0; d11 < n28.length - 5; d11 += 3) {
      const e17 = n28[d11], r29 = n28[d11 + 1], s21 = u12 - e17, i22 = p13 - r29, o23 = n28[d11 + 3] - e17, a26 = n28[d11 + 4] - r29, c16 = a4((o23 * s21 + a26 * i22) / (o23 * o23 + a26 * a26), 0, 1), l23 = o23 * c16 - s21, f19 = a26 * c16 - i22, g7 = l23 * l23 + f19 * f19;
      g7 < m9 && (m9 = g7, h16 = d11 / 3);
    }
    m9 < f18 * f18 && o22(a25.dist, a25.normal, h16, false);
  }
  requiresSlot(e16, t27) {
    return !(t27 !== h6.Color && t27 !== h6.Highlight && t27 !== h6.ObjectAndLayerIdColor || e16 !== E8.OPAQUE_MATERIAL && e16 !== E8.DRAPED_MATERIAL);
  }
  createGLMaterial(e16) {
    return new W6(e16);
  }
  createBufferWriter() {
    const e16 = this.parameters.hasVertexColors ? v10 : I6;
    return t(this.parameters.stipplePattern) ? new r14(e16) : new F5(e16.clone().vec3f(O3.AUXPOS1).vec2f(O3.UV0));
  }
};
var W6 = class extends t8 {
  constructor() {
    super(...arguments), this._stipplePattern = null;
  }
  dispose() {
    super.dispose(), this._stippleTextureRepository.release(this._stipplePattern), this._stipplePattern = null;
  }
  _updateOccludeeState(e16) {
    e16.hasOccludees !== this._material.parameters.hasOccludees && this._material.setParameters({ hasOccludees: e16.hasOccludees });
  }
  beginSlot(e16) {
    this._output === h6.Color && this._updateOccludeeState(e16);
    const t27 = this._material.parameters.stipplePattern;
    return this._stipplePattern !== t27 && (this._material.setParameters(this._stippleTextureRepository.swap(this._stipplePattern, t27)), this._stipplePattern = t27), this.ensureTechnique(f17, e16);
  }
};
var F5 = class {
  constructor(e16) {
    this.vertexBufferLayout = e16;
  }
  allocate(e16) {
    return this.vertexBufferLayout.createBuffer(e16);
  }
  elementCount(e16) {
    return e16.indices.get(O3.POSITION).length;
  }
  write(e16, t27, r28, s20, i21) {
    S3(r28, this.vertexBufferLayout, e16, t27, s20, i21), this._writeAuxpos1(e16, r28, s20, i21), this._writeUV0(e16, r28, s20, i21);
  }
  _writeAuxpos1(e16, t27, r28, s20) {
    const i21 = r28.getField(O3.AUXPOS1, i4), o22 = t27.indices.get(O3.POSITION), a25 = t27.vertexAttributes.get(O3.POSITION).data, n28 = e16, c15 = i21.typedBufferStride, l22 = i21.typedBuffer;
    s20 *= c15;
    for (let u12 = 0; u12 < o22.length - 1; u12 += 2) for (const e17 of [1, 0]) {
      const t28 = 3 * o22[u12 + e17], r29 = a25[t28], i22 = a25[t28 + 1], p13 = a25[t28 + 2], f18 = n28[0] * r29 + n28[4] * i22 + n28[8] * p13 + n28[12], m9 = n28[1] * r29 + n28[5] * i22 + n28[9] * p13 + n28[13], h16 = n28[2] * r29 + n28[6] * i22 + n28[10] * p13 + n28[14];
      l22[s20] = f18, l22[s20 + 1] = m9, l22[s20 + 2] = h16, s20 += c15;
    }
  }
  _writeUV0(e16, t27, r28, s20) {
    var _a;
    const i21 = r28.getField(O3.UV0, u5), o22 = t27.indices.get(O3.POSITION), n28 = t27.vertexAttributes.get(O3.POSITION).data, c15 = (_a = t27.vertexAttributes.get(O3.DISTANCETOSTART)) == null ? void 0 : _a.data, l22 = i21.typedBufferStride, u12 = i21.typedBuffer;
    let f18 = 0;
    u12[s20 *= l22] = k7.START, u12[s20 + 1] = f18, s20 += l22;
    const d11 = 3 * o22[0], g7 = o2(H10, n28[d11], n28[d11 + 1], n28[d11 + 2]);
    e16 && O2(g7, g7, e16);
    const A10 = Q5, _11 = o22.length - 1;
    let O10 = 1;
    const S7 = c15 ? (e17, t28, r29) => f18 = c15[r29] : (e17, t28, r29) => f18 += x(e17, t28);
    for (let m9 = 1; m9 < _11; m9 += 2) {
      const t28 = 3 * o22[m9];
      o2(A10, n28[t28], n28[t28 + 1], n28[t28 + 2]), e16 && O2(A10, A10, e16), S7(g7, A10, O10++);
      for (let e17 = 0; e17 < 2; ++e17) u12[s20] = 1 - e17, u12[s20 + 1] = f18, s20 += l22;
      r3(g7, A10);
    }
    const T8 = 3 * o22[_11];
    o2(A10, n28[T8], n28[T8 + 1], n28[T8 + 2]), e16 && O2(A10, A10, e16), S7(g7, A10, O10), u12[s20] = k7.END, u12[s20 + 1] = f18;
  }
};
var G8 = class extends u7 {
  constructor() {
    super(...arguments), this.color = _2, this.hasVertexColors = false, this.hasSlicePlane = false, this.width = 1, this.stipplePreferContinuous = true, this.hasOccludees = false, this.stippleTexture = null;
  }
};
var H10 = n4();
var Q5 = n4();
var Z6 = n4();
var z8 = n4();
var J4 = x2();
var K4 = x2();
var Y5 = n4();
var $6 = n4();
var ee3 = v4();
var te2 = v4();
var re2 = n4();
var se2 = [x2(), x2(), x2(), x2()];
var ie2 = [n4(), n4(), n4(), n4()];
var oe2 = p2();
var ae2 = p2();
var ne2 = p2();
var ce3 = p2();

// node_modules/@arcgis/core/core/libs/gl-matrix-2/types/mat4.js
function n27(n28) {
  return n28 instanceof Float32Array && n28.length >= 16;
}
function r27(n28) {
  return Array.isArray(n28) && n28.length >= 16;
}
function t25(t27) {
  return n27(t27) || r27(t27);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/ScaleInfo.js
var t26 = class {
  constructor() {
    this.factor = new s19(), this.factorAlignment = new s19();
  }
};
var s19 = class {
  constructor() {
    this.scale = 0, this.factor = 0, this.minPixelSize = 0, this.paddingPixels = 0;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/HUDMaterialTechnique.js
var b8 = class _b extends e9 {
  initializeConfiguration(t27, i21) {
    i21.hasWebGL2Context = t27.rctx.type === r12.WEBGL2, i21.spherical = t27.viewingMode === l13.Global;
  }
  initializeProgram(e16) {
    return new o12(e16.rctx, _b.shader.get().build(this.configuration), E5);
  }
  _setPipelineState(e16) {
    const i21 = this.configuration, r28 = e16 === o11.NONE, o22 = e16 === o11.FrontFace, s20 = this.configuration.hasPolygonOffset ? j11 : null, l22 = (r28 || o22) && i21.output !== h6.Highlight && (i21.depthEnabled || i21.occlusionPass) ? a11 : null;
    return W({ blending: i21.output === h6.Color || i21.output === h6.Alpha || i21.output === h6.Highlight ? r28 ? P8 : A4(e16) : null, depthTest: { func: I.LEQUAL }, depthWrite: l22, colorWrite: _6, polygonOffset: s20 });
  }
  initializePipeline() {
    return this._setPipelineState(this.configuration.transparencyPassType);
  }
  get primitiveType() {
    return this.configuration.occlusionPass ? E3.POINTS : E3.TRIANGLES;
  }
};
b8.shader = new t9(V4, () => import("./HUDMaterial.glsl-QVD6VLAG.js"));
var j11 = { factor: 0, units: -4 };
var P8 = s7(R2.ONE, R2.ONE_MINUS_SRC_ALPHA);

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/HUDMaterialTechniqueConfiguration.js
var a24 = class extends s9 {
  constructor() {
    super(...arguments), this.output = h6.Color, this.screenCenterOffsetUnitsEnabled = p5.World, this.transparencyPassType = o11.NONE, this.spherical = false, this.occlusionTestEnabled = true, this.signedDistanceFieldEnabled = false, this.vvSize = false, this.vvColor = false, this.hasVerticalOffset = false, this.hasScreenSizePerspective = false, this.debugDrawLabelBorder = false, this.binaryHighlightOcclusionEnabled = true, this.hasSlicePlane = false, this.hasPolygonOffset = false, this.depthEnabled = true, this.pixelSnappingEnabled = true, this.isDraped = false, this.hasMultipassGeometry = false, this.hasMultipassTerrain = false, this.cullAboveGround = false, this.occlusionPass = false, this.objectAndLayerIdColorInstanced = false;
  }
};
e([r15({ count: h6.COUNT })], a24.prototype, "output", void 0), e([r15({ count: p5.COUNT })], a24.prototype, "screenCenterOffsetUnitsEnabled", void 0), e([r15({ count: o11.COUNT })], a24.prototype, "transparencyPassType", void 0), e([r15()], a24.prototype, "spherical", void 0), e([r15()], a24.prototype, "occlusionTestEnabled", void 0), e([r15()], a24.prototype, "signedDistanceFieldEnabled", void 0), e([r15()], a24.prototype, "vvSize", void 0), e([r15()], a24.prototype, "vvColor", void 0), e([r15()], a24.prototype, "hasVerticalOffset", void 0), e([r15()], a24.prototype, "hasScreenSizePerspective", void 0), e([r15()], a24.prototype, "debugDrawLabelBorder", void 0), e([r15()], a24.prototype, "binaryHighlightOcclusionEnabled", void 0), e([r15()], a24.prototype, "hasSlicePlane", void 0), e([r15()], a24.prototype, "hasPolygonOffset", void 0), e([r15()], a24.prototype, "depthEnabled", void 0), e([r15()], a24.prototype, "pixelSnappingEnabled", void 0), e([r15()], a24.prototype, "isDraped", void 0), e([r15()], a24.prototype, "hasMultipassGeometry", void 0), e([r15()], a24.prototype, "hasMultipassTerrain", void 0), e([r15()], a24.prototype, "cullAboveGround", void 0), e([r15()], a24.prototype, "occlusionPass", void 0), e([r15()], a24.prototype, "objectAndLayerIdColorInstanced", void 0), e([r15({ constValue: true })], a24.prototype, "hasSliceInVertexProgram", void 0), e([r15({ constValue: false })], a24.prototype, "hasVvInstancing", void 0);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/HUDMaterial.js
var $7 = class extends h8 {
  constructor(e16) {
    super(e16, new be()), this._configuration = new a24();
  }
  getConfiguration(e16, t27) {
    return this._configuration.output = e16, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.hasVerticalOffset = !!this.parameters.verticalOffset, this._configuration.hasScreenSizePerspective = !!this.parameters.screenSizePerspective, this._configuration.screenCenterOffsetUnitsEnabled = "screen" === this.parameters.centerOffsetUnits ? p5.Screen : p5.World, this._configuration.hasPolygonOffset = this.parameters.polygonOffset, this._configuration.isDraped = this.parameters.isDraped, this._configuration.occlusionTestEnabled = this.parameters.occlusionTest, this._configuration.pixelSnappingEnabled = this.parameters.pixelSnappingEnabled, this._configuration.signedDistanceFieldEnabled = this.parameters.textureIsSignedDistanceField, this._configuration.vvSize = !!this.parameters.vvSizeEnabled, this._configuration.vvColor = !!this.parameters.vvColorEnabled, this._configuration.occlusionPass = t27.slot === E8.OCCLUSION_PIXELS && this.parameters.occlusionTest && (e16 === h6.Color || e16 === h6.Alpha), e16 === h6.Color && (this._configuration.debugDrawLabelBorder = !!t11.LABELS_SHOW_BORDER), e16 === h6.Highlight && (this._configuration.binaryHighlightOcclusionEnabled = this.parameters.binaryHighlightOcclusion), this._configuration.depthEnabled = this.parameters.depthEnabled, this._configuration.transparencyPassType = t27.transparencyPassType, this._configuration.hasMultipassGeometry = t27.multipassGeometry.enabled, this._configuration.hasMultipassTerrain = t27.multipassTerrain.enabled, this._configuration.cullAboveGround = t27.multipassTerrain.cullAboveGround, this._configuration;
  }
  intersect(e16, r28, i21, n28, o22, c15) {
    if (!i21.options.selectionMode || !i21.options.hud || !e16.visible) return;
    const l22 = this.parameters;
    let S7 = 1, v13 = 1;
    if (a8(ue2, r28), r(e16.shaderTransformer)) {
      const t27 = e16.shaderTransformer(ge);
      S7 = t27[0], v13 = t27[5], re3(ue2);
    }
    const A10 = e16.vertexAttributes.get(O3.POSITION), P9 = e16.vertexAttributes.get(O3.SIZE), x13 = e16.vertexAttributes.get(O3.NORMAL), _11 = e16.vertexAttributes.get(O3.AUXPOS1);
    s6(A10.size >= 3);
    const y9 = i21.point, I9 = i21.camera, T8 = H6(l22);
    S7 *= I9.pixelRatio, v13 *= I9.pixelRatio;
    const C8 = "screen" === this.parameters.centerOffsetUnits;
    for (let t27 = 0; t27 < A10.data.length / A10.size; t27++) {
      const e17 = t27 * A10.size;
      o2(ne3, A10.data[e17], A10.data[e17 + 1], A10.data[e17 + 2]), O2(ne3, ne3, r28);
      const s20 = t27 * P9.size;
      ve[0] = P9.data[s20] * S7, ve[1] = P9.data[s20 + 1] * v13, O2(ne3, ne3, I9.viewMatrix);
      const n29 = t27 * _11.size;
      if (o2(me2, _11.data[n29 + 0], _11.data[n29 + 1], _11.data[n29 + 2]), !C8 && (ne3[0] += me2[0], ne3[1] += me2[1], 0 !== me2[2])) {
        const e18 = me2[2];
        z2(me2, ne3), e5(ne3, ne3, g(me2, me2, e18));
      }
      const o23 = t27 * x13.size;
      if (o2(oe3, x13.data[o23], x13.data[o23 + 1], x13.data[o23 + 2]), this._normalAndViewAngle(oe3, ue2, I9, de2), this._applyVerticalOffsetTransformationView(ne3, de2, I9, ie3), I9.applyProjection(ne3, ce4), ce4[0] > -1) {
        ce4[0] = Math.floor(ce4[0]), ce4[1] = Math.floor(ce4[1]), C8 && (me2[0] || me2[1]) && (ce4[0] += me2[0], 0 !== me2[1] && (ce4[1] += o10(me2[1], ie3.factorAlignment)), I9.unapplyProjection(ce4, ne3)), ce4[0] += this.parameters.screenOffset[0], ce4[1] += this.parameters.screenOffset[1], f6(ve, ie3.factor, ve);
        const e18 = Oe * I9.pixelRatio;
        let t28 = 0;
        if (l22.textureIsSignedDistanceField && (t28 = l22.outlineSize * I9.pixelRatio / 2), y9 && se3(y9, ce4[0], ce4[1], ve, e18, t28, l22, T8)) {
          const e19 = i21.ray;
          if (O2(fe2, ne3, h3(pe3, I9.viewMatrix)), ce4[0] = y9[0], ce4[1] = y9[1], I9.unprojectFromRenderScreen(ce4, ne3)) {
            const t29 = n4();
            r3(t29, e19.direction);
            const r29 = 1 / s2(t29);
            g(t29, t29, r29);
            c15(x(e19.origin, ne3) * r29, t29, -1, true, 1, fe2);
          }
        }
      }
    }
  }
  intersectDraped(e16, r28, s20, i21, a25, n28) {
    const o22 = e16.vertexAttributes.get(O3.POSITION), c15 = e16.vertexAttributes.get(O3.SIZE), l22 = this.parameters, f18 = H6(l22);
    let u12 = 1, h16 = 1;
    if (r(e16.shaderTransformer)) {
      const t27 = e16.shaderTransformer(ge);
      u12 = t27[0], h16 = t27[5];
    }
    u12 *= e16.screenToWorldRatio, h16 *= e16.screenToWorldRatio;
    const p13 = Se * e16.screenToWorldRatio;
    for (let t27 = 0; t27 < o22.data.length / o22.size; t27++) {
      const r29 = t27 * o22.size, s21 = o22.data[r29], m9 = o22.data[r29 + 1], d11 = t27 * c15.size;
      ve[0] = c15.data[d11] * u12, ve[1] = c15.data[d11 + 1] * h16;
      let g7 = 0;
      l22.textureIsSignedDistanceField && (g7 = l22.outlineSize * e16.screenToWorldRatio / 2), se3(i21, s21, m9, ve, p13, g7, l22, f18) && a25(n28.dist, n28.normal, -1, false);
    }
  }
  createBufferWriter() {
    return new _e(this);
  }
  _normalAndViewAngle(e16, t27, r28, i21) {
    return t25(t27) && (t27 = a8(he3, t27)), S(i21.normal, e16, t27), O2(i21.normal, i21.normal, r28.viewInverseTransposeMatrix), i21.cosAngle = P(le3, Ae), i21;
  }
  _updateScaleInfo(e16, r28, s20) {
    const i21 = this.parameters;
    r(i21.screenSizePerspective) ? d4(s20, r28, i21.screenSizePerspective, e16.factor) : (e16.factor.scale = 1, e16.factor.factor = 0, e16.factor.minPixelSize = 0, e16.factor.paddingPixels = 0), r(i21.screenSizePerspectiveAlignment) ? d4(s20, r28, i21.screenSizePerspectiveAlignment, e16.factorAlignment) : (e16.factorAlignment.factor = e16.factor.factor, e16.factorAlignment.scale = e16.factor.scale, e16.factorAlignment.minPixelSize = e16.factor.minPixelSize, e16.factorAlignment.paddingPixels = e16.factor.paddingPixels);
  }
  applyShaderOffsetsView(e16, t27, r28, s20, i21, a25, n28) {
    const o22 = this._normalAndViewAngle(t27, r28, i21, de2);
    return this._applyVerticalGroundOffsetView(e16, o22, i21, n28), this._applyVerticalOffsetTransformationView(n28, o22, i21, a25), this._applyPolygonOffsetView(n28, o22, s20[3], i21, n28), this._applyCenterOffsetView(n28, s20, n28), n28;
  }
  applyShaderOffsetsNDC(e16, r28, s20, i21, a25) {
    return this._applyCenterOffsetNDC(e16, r28, s20, i21), r(a25) && r3(a25, i21), this._applyPolygonOffsetNDC(i21, r28, s20, i21), i21;
  }
  _applyPolygonOffsetView(t27, r28, s20, i21, a25) {
    const n28 = i21.aboveGround ? 1 : -1;
    let o22 = Math.sign(s20);
    0 === o22 && (o22 = n28);
    const c15 = n28 * o22;
    if (this.parameters.shaderPolygonOffset <= 0) return r3(a25, t27);
    const l22 = a4(Math.abs(r28.cosAngle), 0.01, 1), f18 = 1 - Math.sqrt(1 - l22 * l22) / l22 / i21.viewport[2];
    return g(a25, t27, c15 > 0 ? f18 : 1 / f18), a25;
  }
  _applyVerticalGroundOffsetView(e16, t27, r28, s20) {
    const i21 = s2(e16), a25 = r28.aboveGround ? 1 : -1, n28 = 0.5 * r28.computeRenderPixelSizeAtDist(i21), o22 = g(ne3, t27.normal, a25 * n28);
    return u3(s20, e16, o22), s20;
  }
  _applyVerticalOffsetTransformationView(e16, t27, s20, i21) {
    const a25 = this.parameters;
    if (!a25.verticalOffset || !a25.verticalOffset.screenLength) {
      if (a25.screenSizePerspective || a25.screenSizePerspectiveAlignment) {
        const r28 = s2(e16);
        this._updateScaleInfo(i21, r28, t27.cosAngle);
      } else i21.factor.scale = 1, i21.factorAlignment.scale = 1;
      return e16;
    }
    const n28 = s2(e16), o22 = l(a25.screenSizePerspectiveAlignment, a25.screenSizePerspective), c15 = N3(s20, n28, a25.verticalOffset, t27.cosAngle, o22);
    return this._updateScaleInfo(i21, n28, t27.cosAngle), g(t27.normal, t27.normal, c15), u3(e16, e16, t27.normal);
  }
  _applyCenterOffsetView(e16, t27, r28) {
    const s20 = "screen" !== this.parameters.centerOffsetUnits;
    return r28 !== e16 && r3(r28, e16), s20 && (r28[0] += t27[0], r28[1] += t27[1], t27[2] && (z2(oe3, r28), u3(r28, r28, g(oe3, oe3, t27[2])))), r28;
  }
  _applyCenterOffsetNDC(e16, t27, r28, s20) {
    const i21 = "screen" !== this.parameters.centerOffsetUnits;
    return s20 !== e16 && r3(s20, e16), i21 || (s20[0] += t27[0] / r28.fullWidth * 2, s20[1] += t27[1] / r28.fullHeight * 2), s20;
  }
  _applyPolygonOffsetNDC(e16, t27, r28, s20) {
    const i21 = this.parameters.shaderPolygonOffset;
    if (e16 !== s20 && r3(s20, e16), i21) {
      const e17 = r28.aboveGround ? 1 : -1, a25 = e17 * Math.sign(t27[3]);
      s20[2] -= (a25 || e17) * i21;
    }
    return s20;
  }
  requiresSlot(e16, t27) {
    if (t27 === h6.Color || t27 === h6.Alpha || t27 === h6.Highlight || t27 === h6.ObjectAndLayerIdColor) {
      if (e16 === E8.DRAPED_MATERIAL) return true;
      const { drawInSecondSlot: t28, occlusionTest: r28 } = this.parameters;
      return e16 === (t28 ? E8.LABEL_MATERIAL : E8.HUD_MATERIAL) || r28 && e16 === E8.OCCLUSION_PIXELS;
    }
    return false;
  }
  createGLMaterial(e16) {
    return new ee4(e16);
  }
  calculateRelativeScreenBounds(e16, t27, r28 = u4()) {
    return te3(this.parameters, e16, t27, r28), r28[2] = r28[0] + e16[0], r28[3] = r28[1] + e16[1], r28;
  }
};
var ee4 = class extends h7 {
  constructor(e16) {
    super({ ...e16, ...e16.material.parameters });
  }
  selectProgram(e16) {
    return this.ensureTechnique(b8, e16);
  }
  beginSlot(e16) {
    return this.updateTexture(this._material.parameters.textureId), this._material.setParameters(this.textureBindParameters), this.selectProgram(e16);
  }
};
function te3(e16, t27, r28, s20 = ae3) {
  return a7(s20, e16.anchorPosition), s20[0] *= -t27[0], s20[1] *= -t27[1], s20[0] += e16.screenOffset[0] * r28, s20[1] += e16.screenOffset[1] * r28, s20;
}
function re3(e16) {
  const t27 = e16[0], r28 = e16[1], s20 = e16[2], i21 = e16[3], a25 = e16[4], n28 = e16[5], o22 = e16[6], c15 = e16[7], l22 = e16[8], f18 = 1 / Math.sqrt(t27 * t27 + r28 * r28 + s20 * s20), u12 = 1 / Math.sqrt(i21 * i21 + a25 * a25 + n28 * n28), h16 = 1 / Math.sqrt(o22 * o22 + c15 * c15 + l22 * l22);
  return e16[0] = t27 * f18, e16[1] = r28 * f18, e16[2] = s20 * f18, e16[3] = i21 * u12, e16[4] = a25 * u12, e16[5] = n28 * u12, e16[6] = o22 * h16, e16[7] = c15 * h16, e16[8] = l22 * h16, e16;
}
function se3(e16, r28, s20, i21, a25, n28, o22, c15) {
  let l22 = r28 - a25 - (c15[0] > 0 ? i21[0] * c15[0] : 0), f18 = l22 + i21[0] + 2 * a25, u12 = s20 - a25 - (c15[1] > 0 ? i21[1] * c15[1] : 0), h16 = u12 + i21[1] + 2 * a25;
  const p13 = o22.distanceFieldBoundingBox;
  return o22.textureIsSignedDistanceField && r(p13) && (l22 += i21[0] * p13[0], u12 += i21[1] * p13[1], f18 -= i21[0] * (1 - p13[2]), h16 -= i21[1] * (1 - p13[3]), l22 -= n28, f18 += n28, u12 -= n28, h16 += n28), e16[0] > l22 && e16[0] < f18 && e16[1] > u12 && e16[1] < h16;
}
var ie3 = new t26();
var ae3 = n12();
var ne3 = n4();
var oe3 = n4();
var ce4 = n5();
var le3 = n4();
var fe2 = n4();
var ue2 = e6();
var he3 = e6();
var pe3 = e7();
var me2 = n4();
var de2 = { normal: le3, cosAngle: 0 };
var ge = e7();
var Oe = 1;
var Se = 2;
var ve = [0, 0];
var Ae = r2(0, 0, 1);
var be = class extends o9 {
  constructor() {
    super(...arguments), this.renderOccluded = c4.Occlude, this.color = r5(1, 1, 1, 1), this.texCoordScale = [1, 1], this.polygonOffset = false, this.anchorPosition = r9(0.5, 0.5), this.screenOffset = [0, 0], this.shaderPolygonOffset = 1e-5, this.textureIsSignedDistanceField = false, this.outlineColor = r5(1, 1, 1, 1), this.outlineSize = 0, this.vvSizeEnabled = false, this.vvSizeMinSize = [1, 1, 1], this.vvSizeMaxSize = [100, 100, 100], this.vvSizeOffset = [0, 0, 0], this.vvSizeFactor = [1, 1, 1], this.vvColorEnabled = false, this.vvColorValues = [0, 0, 0, 0, 0, 0, 0, 0], this.vvColorColors = [1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0], this.hasSlicePlane = false, this.pixelSnappingEnabled = true, this.occlusionTest = true, this.binaryHighlightOcclusion = true, this.centerOffsetUnits = "world", this.drawInSecondSlot = false, this.depthEnabled = true, this.isDraped = false;
  }
};
var Pe = T2().vec3f(O3.POSITION).vec3f(O3.NORMAL).vec2f(O3.UV0).vec4u8(O3.COLOR).vec2f(O3.SIZE).vec4f(O3.AUXPOS1).vec4f(O3.AUXPOS2);
var xe = Pe.clone().vec4u8(O3.OBJECTANDLAYERIDCOLOR);
var _e = class {
  constructor(e16) {
    this._material = e16, this.vertexBufferLayout = has("enable-feature:objectAndLayerId-rendering") ? xe : Pe;
  }
  allocate(e16) {
    return this.vertexBufferLayout.createBuffer(e16);
  }
  elementCount(e16) {
    return 6 * e16.indices.get(O3.POSITION).length;
  }
  write(e16, r28, s20, i21, a25) {
    g3(s20.indices.get(O3.POSITION), s20.vertexAttributes.get(O3.POSITION).data, e16, i21.position, a25, 6), b5(s20.indices.get(O3.NORMAL), s20.vertexAttributes.get(O3.NORMAL).data, r28, i21.normal, a25, 6);
    const n28 = s20.vertexAttributes.get(O3.UV0).data;
    let o22, c15, l22, f18;
    if (null == n28 || n28.length < 4) {
      const e17 = this._material.parameters;
      o22 = 0, c15 = 0, l22 = e17.texCoordScale[0], f18 = e17.texCoordScale[1];
    } else o22 = n28[0], c15 = n28[1], l22 = n28[2], f18 = n28[3];
    l22 = Math.min(1.99999, l22 + 1), f18 = Math.min(1.99999, f18 + 1);
    let u12 = s20.indices.get(O3.POSITION).length, h16 = a25;
    const p13 = i21.uv0;
    for (let t27 = 0; t27 < u12; ++t27) p13.set(h16, 0, o22), p13.set(h16, 1, c15), h16 += 1, p13.set(h16, 0, l22), p13.set(h16, 1, c15), h16 += 1, p13.set(h16, 0, l22), p13.set(h16, 1, f18), h16 += 1, p13.set(h16, 0, l22), p13.set(h16, 1, f18), h16 += 1, p13.set(h16, 0, o22), p13.set(h16, 1, f18), h16 += 1, p13.set(h16, 0, o22), p13.set(h16, 1, c15), h16 += 1;
    O4(s20.indices.get(O3.COLOR), s20.vertexAttributes.get(O3.COLOR).data, 4, i21.color, a25, 6);
    const m9 = s20.indices.get(O3.SIZE), d11 = s20.vertexAttributes.get(O3.SIZE).data;
    u12 = m9.length;
    const g7 = i21.size;
    h16 = a25;
    for (let t27 = 0; t27 < u12; ++t27) {
      const e17 = d11[2 * m9[t27]], r29 = d11[2 * m9[t27] + 1];
      for (let t28 = 0; t28 < 6; ++t28) g7.set(h16, 0, e17), g7.set(h16, 1, r29), h16 += 1;
    }
    if (s20.indices.get(O3.AUXPOS1) && s20.vertexAttributes.get(O3.AUXPOS1) ? a13(s20.indices.get(O3.AUXPOS1), s20.vertexAttributes.get(O3.AUXPOS1).data, i21.auxpos1, a25, 6) : p4(i21.auxpos1, a25, 6 * u12), s20.indices.get(O3.AUXPOS2) && s20.vertexAttributes.get(O3.AUXPOS2) ? a13(s20.indices.get(O3.AUXPOS2), s20.vertexAttributes.get(O3.AUXPOS2).data, i21.auxpos2, a25, 6) : p4(i21.auxpos2, a25, 6 * u12), r(s20.objectAndLayerIdColor)) {
      if (s20.indices.get(O3.POSITION)) {
        const e17 = s20.indices.get(O3.POSITION).length, t27 = i21.getField(O3.OBJECTANDLAYERIDCOLOR, x4);
        N4(s20.objectAndLayerIdColor, t27, e17, a25, 6);
      }
    }
  }
};

export {
  e11 as e,
  a14 as a,
  E11 as E,
  I5 as I,
  C4 as C,
  x9 as x,
  l19 as l,
  $2 as $,
  z5 as z,
  p8 as p,
  i14 as i,
  t17 as t,
  i15 as i2,
  x10 as x2,
  U4 as U,
  e14 as e2,
  c13 as c,
  e15 as e3,
  u11 as u,
  f15 as f,
  ce2 as ce,
  n25 as n,
  a21 as a2,
  C7 as C2,
  q3 as q,
  tt3 as tt,
  nt2 as nt,
  et3 as et,
  ot2 as ot,
  lt2 as lt,
  ut2 as ut,
  ct2 as ct,
  at2 as at,
  ft2 as ft,
  pt2 as pt,
  Ot,
  it3 as it,
  wt2 as wt,
  h14 as h,
  A6 as A,
  k4 as k,
  U2,
  B,
  I2,
  S4 as S,
  V5 as V,
  G2 as G,
  X2 as X,
  t14 as t2,
  i12 as i3,
  c7 as c2,
  f10 as f2,
  d5 as d,
  m4 as m,
  p6 as p2,
  g4 as g,
  v9 as v,
  T4 as T,
  j5 as j,
  a16 as a3,
  s14 as s,
  l18 as l2,
  d6 as d2,
  f11 as f3,
  h11 as h2,
  $7 as $2,
  f16 as f4,
  r25 as r,
  a22 as a4,
  b7 as b,
  p12 as p3,
  c14 as c3,
  q4 as q2
};
//# sourceMappingURL=chunk-WHHWXCFL.js.map
