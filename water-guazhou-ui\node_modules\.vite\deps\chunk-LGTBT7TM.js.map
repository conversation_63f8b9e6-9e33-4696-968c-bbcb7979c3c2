{"version": 3, "sources": ["../../@arcgis/core/chunks/ColorMaterial.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{addNearFar as e,addLinearDepth as r}from\"../views/3d/webgl-engine/core/shaderLibrary/ForwardLinearDepth.glsl.js\";import{ShaderOutput as o}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as i}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{Transform as t}from\"../views/3d/webgl-engine/core/shaderLibrary/Transform.glsl.js\";import{ObjectAndLayerIdColor as l}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/ObjectAndLayerIdColor.glsl.js\";import{VertexColor as s}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/VertexColor.glsl.js\";import{OutputDepth as a}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputDepth.glsl.js\";import{OutputHighlight as d}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputHighlight.glsl.js\";import{multipassTerrainTest as n}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{symbolAlphaCutoff as p}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{ColorConversion as g}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ColorConversion.glsl.js\";import{addProjViewLocalOrigin as u}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float4PassUniform as c}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{glsl as h}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as b}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{TransparencyPassType as m}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";import{VertexAttribute as w}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";function v(v){const f=new b,{vertex:C,fragment:y}=f,j=v.output===o.Depth,L=v.hasMultipassTerrain&&(v.output===o.Color||v.output===o.Alpha);return u(C,v),f.include(t,v),f.include(s,v),f.include(l,v),f.attributes.add(w.POSITION,\"vec3\"),f.varyings.add(\"vpos\",\"vec3\"),L&&f.varyings.add(\"depth\",\"float\"),j&&(f.include(a,v),e(f),r(f)),C.code.add(h`\n    void main(void) {\n      vpos = position;\n      forwardNormalizedVertexColor();\n      forwardObjectAndLayerIdColor();\n      ${L?\"depth = (view * vec4(vpos, 1.0)).z;\":\"\"}\n      gl_Position = ${j?h`transformPositionWithDepth(proj, view, vpos, nearFar, linearDepth);`:h`transformPosition(proj, view, vpos);`}\n    }\n  `),f.include(i,v),L&&f.include(n,v),y.include(g),y.uniforms.add(new c(\"eColor\",(e=>e.color))),v.output===o.Highlight&&f.include(d,v),y.code.add(h`\n  void main() {\n    discardBySlice(vpos);\n    ${L?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n    vec4 fColor = ${v.hasVertexColors?\"vColor * eColor;\":\"eColor;\"}\n\n    ${v.output===o.ObjectAndLayerIdColor?h`fColor.a = 1.0;`:\"\"}\n\n    if (fColor.a < ${h.float(p)}) {\n      discard;\n    }\n\n    ${v.output===o.Alpha?h`gl_FragColor = vec4(fColor.a);`:\"\"}\n\n    ${v.output===o.Color?h`gl_FragColor = highlightSlice(fColor, vpos); ${v.transparencyPassType===m.Color?\"gl_FragColor = premultiplyAlpha(gl_FragColor);\":\"\"}`:\"\"}\n    ${v.output===o.Highlight?h`outputHighlight();`:\"\"};\n    ${v.output===o.Depth?h`outputDepth(linearDepth);`:\"\"};\n    ${v.output===o.ObjectAndLayerIdColor?h`outputObjectAndLayerIdColor();`:\"\"}\n  }\n  `),f}const f=Object.freeze(Object.defineProperty({__proto__:null,build:v},Symbol.toStringTag,{value:\"Module\"}));export{f as C,v as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0qD,SAASA,GAAEA,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAE,EAAC,QAAO,GAAE,UAAS,EAAC,IAAED,IAAE,IAAED,GAAE,WAAS,EAAE,OAAM,IAAEA,GAAE,wBAAsBA,GAAE,WAAS,EAAE,SAAOA,GAAE,WAAS,EAAE;AAAO,SAAO,EAAE,GAAEA,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQE,IAAEH,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,WAAW,IAAI,EAAE,UAAS,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAE,KAAGA,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAE,MAAIA,GAAE,QAAQC,IAAEF,EAAC,GAAE,EAAEC,EAAC,GAAEG,GAAEH,EAAC,IAAG,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,QAKt/D,IAAE,wCAAsC,EAAE;AAAA,sBAC5B,IAAE,yEAAuE,uCAAuC;AAAA;AAAA,GAEnI,GAAEA,GAAE,QAAQ,GAAED,EAAC,GAAE,KAAGC,GAAE,QAAQ,GAAED,EAAC,GAAE,EAAE,QAAQG,EAAC,GAAE,EAAE,SAAS,IAAI,IAAI,EAAE,UAAU,CAAAA,OAAGA,GAAE,KAAM,CAAC,GAAEH,GAAE,WAAS,EAAE,aAAWC,GAAE,QAAQ,GAAED,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,MAG5I,IAAE,2CAAyC,EAAE;AAAA,oBAC/BA,GAAE,kBAAgB,qBAAmB,SAAS;AAAA;AAAA,MAE5DA,GAAE,WAAS,EAAE,wBAAsB,qBAAmB,EAAE;AAAA;AAAA,qBAEzC,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,MAIzBA,GAAE,WAAS,EAAE,QAAM,oCAAkC,EAAE;AAAA;AAAA,MAEvDA,GAAE,WAAS,EAAE,QAAM,iDAAiDA,GAAE,yBAAuBE,GAAE,QAAM,mDAAiD,EAAE,KAAG,EAAE;AAAA,MAC7JF,GAAE,WAAS,EAAE,YAAU,wBAAsB,EAAE;AAAA,MAC/CA,GAAE,WAAS,EAAE,QAAM,+BAA6B,EAAE;AAAA,MAClDA,GAAE,WAAS,EAAE,wBAAsB,oCAAkC,EAAE;AAAA;AAAA,GAE1E,GAAEC;AAAC;AAAC,IAAM,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAMD,GAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["v", "f", "o", "e", "t"]}