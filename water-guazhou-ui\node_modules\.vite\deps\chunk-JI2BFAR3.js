import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  N
} from "./chunk-HP475EI3.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/DimensionalDefinition.js
var a2;
var n = a2 = class extends l {
  constructor(e2) {
    super(e2), this.variableName = null, this.dimensionName = null, this.values = [], this.isSlice = false;
  }
  clone() {
    return new a2({ variableName: this.variableName, dimensionName: this.dimensionName, values: p(this.values), isSlice: this.isSlice });
  }
};
e([y({ type: String, json: { write: true } })], n.prototype, "variableName", void 0), e([y({ type: String, json: { write: true } })], n.prototype, "dimensionName", void 0), e([y({ type: N.array(N.oneOf([N.native(Number), N.array(N.native(Number))])), json: { write: true } })], n.prototype, "values", void 0), e([y({ type: Boolean, json: { write: true } })], n.prototype, "isSlice", void 0), n = a2 = e([a("esri.layers.support.DimensionalDefinition")], n);
var p2 = n;

// node_modules/@arcgis/core/layers/support/rasterDatasets/multidimensionalUtils.js
function i(e2, n2, t2) {
  var _a;
  const a4 = n2.shift();
  if (0 === t2.length) {
    const e3 = [];
    t2.push({ sliceId: -1, multidimensionalDefinition: e3 });
  }
  const s2 = t2.length;
  for (let i2 = 0; i2 < s2; i2++) {
    const n3 = t2.shift().multidimensionalDefinition;
    (_a = a4.values) == null ? void 0 : _a.forEach((i3) => {
      t2.push({ sliceId: -1, multidimensionalDefinition: [...n3, { variableName: e2, dimensionName: a4.name, values: [i3] }] });
    });
  }
  n2.length && i(e2, n2, t2);
}
function a3(e2, n2) {
  const t2 = [];
  let a4 = 0;
  return (n2 ? e2.variables.filter((e3) => e3.name.toLowerCase() === n2.toLowerCase()) : [...e2.variables].sort((e3, n3) => e3.name > n3.name ? 1 : -1)).forEach((e3) => {
    const n3 = [], s2 = [...e3.dimensions].sort((e4, n4) => e4.name > n4.name ? -1 : 1);
    i(e3.name, s2, n3), n3.forEach((e4) => {
      t2.push({ ...e4, sliceId: a4++ });
    });
  }), t2;
}
function s(n2, t2, i2) {
  let a4 = n2;
  if (t2 && (t2 = [...t2].sort((e2, n3) => e2.dimensionName < n3.dimensionName ? -1 : 1)).forEach(({ dimensionName: e2, values: n3, isSlice: t3 }) => {
    n3.length && (a4 = a4.filter((i3) => {
      const a5 = i3.multidimensionalDefinition.find((n4) => n4.dimensionName === e2);
      if (null == a5) return false;
      const s2 = a5.values[0];
      return "number" == typeof s2 ? "number" == typeof n3[0] ? n3.includes(s2) : n3.some((e3) => e3[0] <= s2 && e3[1] >= s2) : "number" == typeof n3[0] ? n3.some((e3) => s2[0] <= e3 && s2[1] >= e3) : t3 ? n3.some((e3) => e3[0] === s2[0] && e3[0] === s2[1]) : n3.some((e3) => e3[0] >= s2[0] && e3[0] <= s2[1] || e3[1] >= s2[0] && e3[1] <= s2[1] || e3[0] < s2[0] && e3[1] > s2[1]);
    }));
  }), a4.length && i2 && r(i2.start) && r(i2.end)) {
    const e2 = i2.start.getTime(), n3 = i2.end.getTime(), t3 = a4[0].multidimensionalDefinition.findIndex((e3) => "StdTime" === e3.dimensionName);
    t3 > -1 && (a4 = a4.filter((i3) => {
      const a5 = i3.multidimensionalDefinition[t3].values[0];
      return e2 <= a5 && n3 >= a5;
    }));
  }
  return a4.map((e2) => e2.sliceId);
}
function r2(e2, n2) {
  return Array.isArray(e2) ? n2[0] === n2[1] ? e2[0] === n2[0] || e2[1] === n2[0] : e2[0] >= n2[0] && e2[0] <= n2[1] && e2[1] >= n2[0] && e2[1] <= n2[1] : e2 >= n2[0] && e2 <= n2[1];
}
function l2(e2, n2) {
  return e2[0] <= n2[0] && e2[1] >= n2[0] || e2[0] <= n2[1] && e2[1] >= n2[1] || e2[0] >= n2[0] && e2[1] <= n2[1];
}
function o(e2) {
  return 1 === e2.length ? [e2[0], e2[0]] : [e2[0], e2[e2.length - 1]];
}
function u(e2, n2, t2) {
  var _a, _b, _c;
  if (!((_a = n2 == null ? void 0 : n2.subsetDefinitions) == null ? void 0 : _a.length)) return e2;
  let i2;
  if (t2) {
    const { variables: a5 } = n2;
    if (a5.length && !a5.includes(t2)) return null;
    const s3 = n2.subsetDefinitions.find((n3) => n3.dimensionName === e2.name && n3.variableName === t2);
    if (!((_b = s3 == null ? void 0 : s3.values) == null ? void 0 : _b.length)) return e2;
    i2 = o(s3.values);
  } else {
    i2 = (_c = n2.dimensions.find(({ name: n3 }) => n3 === e2.name)) == null ? void 0 : _c.extent;
  }
  const a4 = i2;
  if (!a4 || !(a4 == null ? void 0 : a4.length)) return e2;
  const s2 = e2.values.filter((e3) => r2(e3, a4));
  return { ...e2, extent: [...a4], values: s2 };
}
function m(e2, n2, t2) {
  var _a;
  if (!((_a = n2 == null ? void 0 : n2.subsetDefinitions) == null ? void 0 : _a.length)) return false;
  const { variables: i2 } = n2;
  if (i2.length && e2.some(({ variableName: e3 }) => e3 && !i2.includes(e3))) return true;
  for (let a4 = 0; a4 < e2.length; a4++) {
    const i3 = e2[a4], s2 = n2.subsetDefinitions.find((e3) => ("" === i3.variableName || e3.variableName === i3.variableName) && e3.dimensionName === i3.dimensionName);
    if (s2 == null ? void 0 : s2.values.length) {
      const e3 = o(s2.values);
      if (!i3.isSlice && 2 === i3.values.length && !Array.isArray(i3.values[0]) && i3.values[0] !== i3.values[1] && t2) {
        if (!l2(i3.values, e3)) return true;
      } else if (i3.values.some((n3) => !r2(n3, e3))) return true;
    }
  }
  return false;
}
function c(t2, i2) {
  if (t(t2)) return { isOutside: false };
  const { geometry: a4, timeExtent: s2, multidimensionalDefinition: r3 } = i2;
  let l3 = null;
  if (r(s2) && (l3 = f(t2, s2), t(l3))) return { isOutside: true };
  const { areaOfInterest: o2 } = t2;
  if (o2 && a4) {
    const e2 = "point" === a4.type ? a4 : "extent" === a4.type ? a4.center : "polygon" === a4.type ? a4.centroid : null;
    if (e2 && !o2.contains(e2)) return { isOutside: true };
  }
  return r(r3) && r3.length && m(r3, t2, true) ? { isOutside: true } : { isOutside: false, intersection: { geometry: a4, timeExtent: l3, multidimensionalDefinition: r3 } };
}
function f(e2, i2) {
  const a4 = e2.dimensions.find(({ name: e3 }) => "StdTime" === e3);
  if (null == a4 || t(i2.start) && t(i2.end)) return i2;
  i2 = i2.clone();
  const { start: s2, end: r3 } = i2.toJSON(), l3 = s2 === r3 ? [s2] : null != s2 && null != r3 ? [s2, r3] : [s2 ?? r3];
  if (2 === l3.length && (a4 == null ? void 0 : a4.extent.length) && (l3[0] = Math.max(l3[0], a4.extent[0]), l3[1] = Math.min(l3[1], a4.extent[1] ?? a4.extent[0]), l3[1] < l3[0])) return null;
  return m([new p2({ variableName: "", dimensionName: "StdTime", isSlice: 1 === l3.length, values: l3 })], e2, true) ? null : (i2.start = new Date(l3[0]), i2.end = new Date(l3[1] ?? l3[0]), i2);
}
function d(t2, i2 = {}) {
  var _a, _b;
  const { multidimensionalInfo: a4, keyProperties: s2 } = t2;
  if (t(a4)) return null;
  const { variableName: r3, multidimensionalSubset: l3, multidimensionalDefinition: o2 } = i2, u2 = r(o2) ? (_a = o2[0]) == null ? void 0 : _a.variableName : null, m2 = r3 || u2 || (s2 == null ? void 0 : s2.DefaultVariable);
  let { variables: c2 } = a4;
  ((_b = l3 == null ? void 0 : l3.variables) == null ? void 0 : _b.length) && (c2 = c2.filter(({ name: e2 }) => l3.variables.includes(e2)));
  return m2 ? c2.find(({ name: e2 }) => e2 === m2) ?? c2[0] : c2[0];
}
function h(e2, n2 = {}) {
  const i2 = d(e2, n2);
  if (!i2) return null;
  const a4 = [], { dimensions: s2, name: r3 } = i2;
  if (0 === s2.length) return [new p2({ variableName: r3, dimensionName: "", values: [], isSlice: true })];
  for (let l3 = 0; l3 < s2.length; l3++) {
    const e3 = u(s2[l3], n2.multidimensionalSubset, r3);
    if (!e3) return null;
    const { values: i3, extent: o2 } = e3;
    let m2 = (i3 == null ? void 0 : i3[0]) ?? o2[0];
    "stdz" === e3.name.toLowerCase() && !e3.hasRanges && Math.abs(o2[1]) <= Math.abs(o2[0]) && (m2 = (i3 == null ? void 0 : i3.length) ? i3[i3.length - 1] : o2[1]), a4.push(new p2({ variableName: r3, dimensionName: e3.name, values: [m2], isSlice: !n2.useRangeForRangedDimensionInfo || !!e3.hasRanges }));
  }
  return a4;
}
function g(e2) {
  return !(t(e2) || !e2.length) && e2.some((e3) => {
    if (null == e3.values) return true;
    const n2 = e3.values.length;
    return 0 === n2 || n2 > 1 || !e3.isSlice && Array.isArray(e3.values[0]);
  });
}
function v(t2, i2) {
  var _a;
  if (t(i2) || t(t2)) return null;
  let a4 = i2.variables.map((e2) => ({ ...e2 }));
  return ((_a = t2 == null ? void 0 : t2.variables) == null ? void 0 : _a.length) && (a4 = a4.filter(({ name: e2 }) => t2.variables.includes(e2)), a4.forEach((n2) => {
    n2.dimensions = n2.dimensions.map((e2) => u(e2, t2, n2.name)).filter(r);
  })), a4;
}
function b(e2, n2) {
  var _a;
  const { values: t2 } = n2;
  if (t2 == null ? void 0 : t2.length) return Array.isArray(t2[0]) !== Array.isArray(e2) ? -1 : Array.isArray(t2[0]) ? t2.findIndex((n3) => n3[0] === e2[0] && n3[1] === e2[1]) : t2.indexOf(e2);
  const { extent: i2 } = n2;
  if (Array.isArray(e2) || e2 < i2[0] || e2 > i2[1]) return -1;
  const a4 = n2.interval || 1;
  if ("ISO8601" !== n2.unit) return Math.round((e2 - i2[0]) / a4);
  const s2 = i2[0];
  let r3 = -1;
  switch (((_a = n2.intervalUnit) == null ? void 0 : _a.toLowerCase()) || "seconds") {
    case "seconds":
      r3 = Math.round((e2 - s2) / 1e3 / a4);
      break;
    case "minutes":
      r3 = Math.round((e2 - s2) / 6e4 / a4);
      break;
    case "hours":
      r3 = Math.round((e2 - s2) / 36e5 / a4);
      break;
    case "days":
      r3 = Math.round((e2 - s2) / 864e5 / a4);
      break;
    case "months":
      {
        const n3 = new Date(e2).getUTCFullYear() - new Date(s2).getUTCFullYear(), t3 = new Date(s2).getUTCMonth(), i3 = new Date(e2).getUTCMonth();
        r3 = 0 === n3 ? i3 - t3 : i3 + 11 - t3 + 12 * (n3 - 1);
      }
      break;
    case "years":
      r3 = Math.round((new Date(e2).getUTCFullYear() - new Date(s2).getUTCFullYear()) / a4);
      break;
    case "decades":
      r3 = Math.round((new Date(e2).getUTCFullYear() - new Date(s2).getUTCFullYear()) / 10 / a4);
  }
  return r3;
}
function D(e2) {
  var _a, _b;
  let n2 = (_a = e2.values) == null ? void 0 : _a.length;
  if (n2) return n2;
  const { extent: t2, unit: i2 } = e2, a4 = e2.interval || 1, s2 = t2 ? t2[1] - t2[0] : 0;
  if ("ISO8601" !== i2) return Math.round(s2 / a4);
  switch (((_b = e2.intervalUnit) == null ? void 0 : _b.toLowerCase()) ?? "seconds") {
    case "seconds":
      n2 = Math.round(s2 / 1e3 / a4);
      break;
    case "minutes":
      n2 = Math.round(s2 / 6e4 / a4);
      break;
    case "hours":
      n2 = Math.round(s2 / 36e5 / a4);
      break;
    case "days":
      n2 = Math.round(s2 / 864e5 / a4);
      break;
    case "months":
      {
        const e3 = new Date(t2[1]).getUTCFullYear() - new Date(t2[0]).getUTCFullYear(), i3 = new Date(t2[1][0]).getUTCMonth(), a5 = new Date(t2[1][1]).getUTCMonth();
        n2 = 0 === e3 ? a5 - i3 + 1 : a5 + 11 - i3 + 12 * (e3 - 1) + 1;
      }
      break;
    case "years":
      n2 = Math.round((new Date(t2[1]).getUTCFullYear() - new Date(t2[0]).getUTCFullYear()) / a4);
      break;
    case "decades":
      n2 = Math.round((new Date(t2[1]).getUTCFullYear() - new Date(t2[0]).getUTCFullYear()) / 10 / a4);
      break;
    default:
      n2 = 0;
  }
  return n2;
}
function y2(e2, n2) {
  let t2 = 0;
  const i2 = e2[0].variableName, a4 = [...n2.variables].sort((e3, n3) => e3.name > n3.name ? 1 : -1);
  for (let s2 = 0; s2 < a4.length; s2++) {
    const n3 = a4[s2], r3 = [...n3.dimensions].sort((e3, n4) => e3.name > n4.name ? -1 : 1);
    if (n3.name !== i2) {
      t2 += r3.map((e3) => D(e3)).reduce((e3, n4) => e3 * n4);
      continue;
    }
    const l3 = r3.map((e3) => D(e3)), o2 = r3.length;
    for (let i3 = 0; i3 < o2; i3++) {
      const n4 = e2.find((e3) => e3.dimensionName === r3[i3].name);
      if (null == n4) return null;
      const a5 = b(n4.values[0], r3[i3]);
      if (-1 === a5) return null;
      l3.shift(), t2 += i3 === o2 - 1 ? a5 : a5 * l3.reduce((e3, n5) => e3 * n5);
    }
    break;
  }
  return t2;
}

export {
  p2 as p,
  a3 as a,
  s,
  m,
  c,
  d,
  h,
  g,
  v,
  y2 as y
};
//# sourceMappingURL=chunk-JI2BFAR3.js.map
