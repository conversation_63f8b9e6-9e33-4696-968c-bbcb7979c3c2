import {
  a,
  h,
  x
} from "./chunk-SMSZBVG5.js";
import {
  T
} from "./chunk-WAPZ634R.js";
import {
  has
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/views/2d/engine/webgl/TileContainer.js
var n = (e, r) => e.key.level - r.key.level != 0 ? e.key.level - r.key.level : e.key.row - r.key.row != 0 ? e.key.row - r.key.row : e.key.col - r.key.col;
var i = class extends a {
  constructor(e) {
    super(), this._tileInfoView = e;
  }
  get requiresDedicatedFBO() {
    return false;
  }
  renderChildren(e) {
    this.sortChildren(n), this.setStencilReference(e), super.renderChildren(e);
  }
  createRenderParams(e) {
    const { state: r } = e, s = super.createRenderParams(e);
    return s.requiredLevel = this._tileInfoView.getClosestInfoForScale(r.scale).level, s.displayLevel = this._tileInfoView.tileInfo.scaleToZoom(r.scale), s;
  }
  prepareRenderPasses(r) {
    const n2 = super.prepareRenderPasses(r);
    return n2.push(r.registerRenderPass({ name: "stencil", brushes: [h], drawPhase: T.DEBUG | T.MAP | T.HIGHLIGHT, target: () => this.getStencilTarget() })), has("esri-tiles-debug") && n2.push(r.registerRenderPass({ name: "tileInfo", brushes: [x], drawPhase: T.DEBUG, target: () => this.children })), n2;
  }
  getStencilTarget() {
    return this.children;
  }
  setStencilReference(e) {
    let r = 1;
    for (const s of this.children) s.stencilRef = r++;
  }
};

export {
  i
};
//# sourceMappingURL=chunk-JTTSDQPH.js.map
