{"version": 3, "sources": ["../../@arcgis/core/layers/support/fromPortalItem.js", "../../@arcgis/core/layers/Layer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"../../config.js\";import t from\"../../core/Logger.js\";async function o(o){const a=\"portalItem\"in o?o:{portalItem:o},e=await import(\"../../portal/support/portalLayers.js\");try{return await e.fromItem(a)}catch(p){const o=a&&a.portalItem,e=o&&o.id||\"unset\",l=o&&o.portal&&o.portal.url||r.portalUrl;throw t.getLogger(\"esri.layers.support.fromPortalItem\").error(\"#fromPortalItem()\",\"Failed to create layer from portal item (portal: '\"+l+\"', id: '\"+e+\"')\",p),p}}export{o as fromPortalItem};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import\"../geometry.js\";import e from\"../request.js\";import r from\"../core/Error.js\";import o from\"../core/Evented.js\";import{IdentifiableMixin as i}from\"../core/Identifiable.js\";import s from\"../core/Loadable.js\";import a from\"../core/Logger.js\";import{isAbortError as l}from\"../core/promiseUtils.js\";import{urlToObject as n}from\"../core/urlUtils.js\";import{property as p}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as y}from\"../core/accessorSupport/decorators/subclass.js\";import{fromPortalItem as d}from\"./support/fromPortalItem.js\";import u from\"../geometry/Extent.js\";import c from\"../geometry/SpatialReference.js\";let m=0,h=class extends(o.EventedMixin(i(s))){constructor(){super(...arguments),this.attributionDataUrl=null,this.fullExtent=new u(-180,-90,180,90,c.WGS84),this.id=Date.now().toString(16)+\"-layer-\"+m++,this.legendEnabled=!0,this.listMode=\"show\",this.opacity=1,this.parent=null,this.popupEnabled=!0,this.attributionVisible=!0,this.spatialReference=c.WGS84,this.title=null,this.type=null,this.url=null,this.visible=!0}static async fromArcGISServerUrl(t){const e=\"string\"==typeof t?{url:t}:t;return(await import(\"./support/arcgisLayers.js\")).fromUrl(e)}static fromPortalItem(t){return d(t)}initialize(){this.when().catch((t=>{l(t)||a.getLogger(this.declaredClass).error(\"#load()\",`Failed to load layer (title: '${this.title??\"no title\"}', id: '${this.id??\"no id\"}')`,{error:t})}))}destroy(){if(this.parent){const t=this,e=this.parent;\"layers\"in e&&e.layers.includes(t)?e.layers.remove(t):\"tables\"in e&&e.tables.includes(t)?e.tables.remove(t):\"baseLayers\"in e&&e.baseLayers.includes(t)?e.baseLayers.remove(t):\"baseLayers\"in e&&e.referenceLayers.includes(t)&&e.referenceLayers.remove(t)}}get hasAttributionData(){return null!=this.attributionDataUrl}get parsedUrl(){return n(this.url)}async fetchAttributionData(){const t=this.attributionDataUrl;if(this.hasAttributionData&&t){return(await e(t,{query:{f:\"json\"},responseType:\"json\"})).data}throw new r(\"layer:no-attribution-data\",\"Layer does not have attribution data\")}};t([p({type:String})],h.prototype,\"attributionDataUrl\",void 0),t([p({type:u})],h.prototype,\"fullExtent\",void 0),t([p({readOnly:!0})],h.prototype,\"hasAttributionData\",null),t([p({type:String,clonable:!1})],h.prototype,\"id\",void 0),t([p({type:Boolean,nonNullable:!0})],h.prototype,\"legendEnabled\",void 0),t([p({type:[\"show\",\"hide\",\"hide-children\"]})],h.prototype,\"listMode\",void 0),t([p({type:Number,range:{min:0,max:1},nonNullable:!0})],h.prototype,\"opacity\",void 0),t([p({clonable:!1})],h.prototype,\"parent\",void 0),t([p({readOnly:!0})],h.prototype,\"parsedUrl\",null),t([p({type:Boolean})],h.prototype,\"popupEnabled\",void 0),t([p({type:Boolean})],h.prototype,\"attributionVisible\",void 0),t([p({type:c})],h.prototype,\"spatialReference\",void 0),t([p({type:String})],h.prototype,\"title\",void 0),t([p({readOnly:!0,json:{read:!1}})],h.prototype,\"type\",void 0),t([p()],h.prototype,\"url\",void 0),t([p({type:Boolean,nonNullable:!0})],h.prototype,\"visible\",void 0),h=t([y(\"esri.layers.Layer\")],h);const b=h;export{b as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAImE,eAAe,EAAEA,IAAE;AAAC,QAAMC,KAAE,gBAAeD,KAAEA,KAAE,EAAC,YAAWA,GAAC,GAAEE,KAAE,MAAM,OAAO,4BAAsC;AAAE,MAAG;AAAC,WAAO,MAAMA,GAAE,SAASD,EAAC;AAAA,EAAC,SAAO,GAAE;AAAC,UAAMD,KAAEC,MAAGA,GAAE,YAAWC,KAAEF,MAAGA,GAAE,MAAI,SAAQ,IAAEA,MAAGA,GAAE,UAAQA,GAAE,OAAO,OAAK,EAAE;AAAU,UAAMG,GAAE,UAAU,oCAAoC,EAAE,MAAM,qBAAoB,uDAAqD,IAAE,aAAWD,KAAE,MAAK,CAAC,GAAE;AAAA,EAAC;AAAC;;;ACAuS,IAAIE,KAAE;AAAN,IAAQ,IAAE,cAAc,EAAE,aAAaC,GAAE,CAAC,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,qBAAmB,MAAK,KAAK,aAAW,IAAI,EAAE,MAAK,KAAI,KAAI,IAAG,EAAE,KAAK,GAAE,KAAK,KAAG,KAAK,IAAI,EAAE,SAAS,EAAE,IAAE,YAAUD,MAAI,KAAK,gBAAc,MAAG,KAAK,WAAS,QAAO,KAAK,UAAQ,GAAE,KAAK,SAAO,MAAK,KAAK,eAAa,MAAG,KAAK,qBAAmB,MAAG,KAAK,mBAAiB,EAAE,OAAM,KAAK,QAAM,MAAK,KAAK,OAAK,MAAK,KAAK,MAAI,MAAK,KAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,aAAa,oBAAoB,GAAE;AAAC,UAAME,KAAE,YAAU,OAAO,IAAE,EAAC,KAAI,EAAC,IAAE;AAAE,YAAO,MAAM,OAAO,4BAA2B,GAAG,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,eAAe,GAAE;AAAC,WAAO,EAAE,CAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,KAAK,EAAE,MAAO,OAAG;AAAC,QAAE,CAAC,KAAGD,GAAE,UAAU,KAAK,aAAa,EAAE,MAAM,WAAU,iCAAiC,KAAK,SAAO,UAAU,WAAW,KAAK,MAAI,OAAO,MAAK,EAAC,OAAM,EAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,KAAK,QAAO;AAAC,YAAM,IAAE,MAAKC,KAAE,KAAK;AAAO,kBAAWA,MAAGA,GAAE,OAAO,SAAS,CAAC,IAAEA,GAAE,OAAO,OAAO,CAAC,IAAE,YAAWA,MAAGA,GAAE,OAAO,SAAS,CAAC,IAAEA,GAAE,OAAO,OAAO,CAAC,IAAE,gBAAeA,MAAGA,GAAE,WAAW,SAAS,CAAC,IAAEA,GAAE,WAAW,OAAO,CAAC,IAAE,gBAAeA,MAAGA,GAAE,gBAAgB,SAAS,CAAC,KAAGA,GAAE,gBAAgB,OAAO,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAoB;AAAC,WAAO,QAAM,KAAK;AAAA,EAAkB;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,EAAE,KAAK,GAAG;AAAA,EAAC;AAAA,EAAC,MAAM,uBAAsB;AAAC,UAAM,IAAE,KAAK;AAAmB,QAAG,KAAK,sBAAoB,GAAE;AAAC,cAAO,MAAM,EAAE,GAAE,EAAC,OAAM,EAAC,GAAE,OAAM,GAAE,cAAa,OAAM,CAAC,GAAG;AAAA,IAAI;AAAC,UAAM,IAAID,GAAE,6BAA4B,sCAAsC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,MAAE,CAAC,CAAC,GAAE,EAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,QAAO,eAAe,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,mBAAmB,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["o", "a", "e", "s", "m", "s", "e"]}