{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/VisualVariables.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Float3PassUniform as e}from\"../../shaderModules/Float3PassUniform.js\";import{Float4sPassUniform as o}from\"../../shaderModules/Float4sPassUniform.js\";import{FloatsPassUniform as r}from\"../../shaderModules/FloatsPassUniform.js\";import{glsl as v}from\"../../shaderModules/interfaces.js\";import{Matrix3PassUniform as t}from\"../../shaderModules/Matrix3PassUniform.js\";import{VertexAttribute as a}from\"../../../lib/VertexAttribute.js\";import{vvColorNumber as i}from\"../../../materials/VisualVariablePassParameters.js\";function s(s,n){n.hasVvInstancing&&(n.vvSize||n.vvColor)&&s.attributes.add(a.INSTANCEFEATUREATTRIBUTE,\"vec4\");const l=s.vertex;n.vvSize?(l.uniforms.add(new e(\"vvSizeMinSize\",(e=>e.vvSizeMinSize))),l.uniforms.add(new e(\"vvSizeMaxSize\",(e=>e.vvSizeMaxSize))),l.uniforms.add(new e(\"vvSizeOffset\",(e=>e.vvSizeOffset))),l.uniforms.add(new e(\"vvSizeFactor\",(e=>e.vvSizeFactor))),l.uniforms.add(new t(\"vvSymbolRotationMatrix\",(e=>e.vvSymbolRotationMatrix))),l.uniforms.add(new e(\"vvSymbolAnchor\",(e=>e.vvSymbolAnchor))),l.code.add(v`vec3 vvScale(vec4 _featureAttribute) {\nreturn clamp(vvSizeOffset + _featureAttribute.x * vvSizeFactor, vvSizeMinSize, vvSizeMaxSize);\n}\nvec4 vvTransformPosition(vec3 position, vec4 _featureAttribute) {\nreturn vec4(vvSymbolRotationMatrix * ( vvScale(_featureAttribute) * (position + vvSymbolAnchor)), 1.0);\n}`),l.code.add(v`\n      const float eps = 1.192092896e-07;\n      vec4 vvTransformNormal(vec3 _normal, vec4 _featureAttribute) {\n        vec3 vvScale = clamp(vvSizeOffset + _featureAttribute.x * vvSizeFactor, vvSizeMinSize + eps, vvSizeMaxSize);\n        return vec4(vvSymbolRotationMatrix * _normal / vvScale, 1.0);\n      }\n\n      ${n.hasVvInstancing?v`\n      vec4 vvLocalNormal(vec3 _normal) {\n        return vvTransformNormal(_normal, instanceFeatureAttribute);\n      }\n\n      vec4 localPosition() {\n        return vvTransformPosition(position, instanceFeatureAttribute);\n      }`:\"\"}\n    `)):l.code.add(v`vec4 localPosition() { return vec4(position, 1.0); }\nvec4 vvLocalNormal(vec3 _normal) { return vec4(_normal, 1.0); }`),n.vvColor?(l.constants.add(\"vvColorNumber\",\"int\",i),n.hasVvInstancing&&l.uniforms.add([new r(\"vvColorValues\",(e=>e.vvColorValues),i),new o(\"vvColorColors\",(e=>e.vvColorColors),i)]),l.code.add(v`\n      vec4 vvGetColor(vec4 featureAttribute, float values[vvColorNumber], vec4 colors[vvColorNumber]) {\n        float value = featureAttribute.y;\n        if (value <= values[0]) {\n          return colors[0];\n        }\n\n        for (int i = 1; i < vvColorNumber; ++i) {\n          if (values[i] >= value) {\n            float f = (value - values[i-1]) / (values[i] - values[i-1]);\n            return mix(colors[i-1], colors[i], f);\n          }\n        }\n        return colors[vvColorNumber - 1];\n      }\n\n      ${n.hasVvInstancing?v`\n      vec4 vvColor() {\n        return vvGetColor(instanceFeatureAttribute, vvColorValues, vvColorColors);\n      }`:\"\"}\n    `)):l.code.add(v`vec4 vvColor() { return vec4(1.0); }`)}export{s as VisualVariables};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIsgB,SAAS,EAAEA,IAAE,GAAE;AAAC,IAAE,oBAAkB,EAAE,UAAQ,EAAE,YAAUA,GAAE,WAAW,IAAI,EAAE,0BAAyB,MAAM;AAAE,QAAM,IAAEA,GAAE;AAAO,IAAE,UAAQ,EAAE,SAAS,IAAI,IAAI,EAAE,iBAAiB,CAAAC,OAAGA,GAAE,aAAc,CAAC,GAAE,EAAE,SAAS,IAAI,IAAI,EAAE,iBAAiB,CAAAA,OAAGA,GAAE,aAAc,CAAC,GAAE,EAAE,SAAS,IAAI,IAAI,EAAE,gBAAgB,CAAAA,OAAGA,GAAE,YAAa,CAAC,GAAE,EAAE,SAAS,IAAI,IAAI,EAAE,gBAAgB,CAAAA,OAAGA,GAAE,YAAa,CAAC,GAAE,EAAE,SAAS,IAAI,IAAIA,GAAE,0BAA0B,CAAAA,OAAGA,GAAE,sBAAuB,CAAC,GAAE,EAAE,SAAS,IAAI,IAAI,EAAE,kBAAkB,CAAAA,OAAGA,GAAE,cAAe,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhhC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOP,EAAE,kBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAOjB,EAAE;AAAA,KACN,KAAG,EAAE,KAAK,IAAI;AAAA,gEAC6C,GAAE,EAAE,WAAS,EAAE,UAAU,IAAI,iBAAgB,OAAMC,EAAC,GAAE,EAAE,mBAAiB,EAAE,SAAS,IAAI,CAAC,IAAIA,GAAE,iBAAiB,CAAAD,OAAGA,GAAE,eAAeC,EAAC,GAAE,IAAID,GAAE,iBAAiB,CAAAA,OAAGA,GAAE,eAAeC,EAAC,CAAC,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAgB1P,EAAE,kBAAgB;AAAA;AAAA;AAAA,WAGjB,EAAE;AAAA,KACN,KAAG,EAAE,KAAK,IAAI,uCAAuC;AAAC;", "names": ["s", "e", "o"]}