import {
  a as a2,
  c
} from "./chunk-GJMH67CL.js";
import {
  s as s2
} from "./chunk-7SWS36OI.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  v
} from "./chunk-HP475EI3.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/geometry/support/MeshVertexAttributes.js
var i;
var l2 = "esri.geometry.support.MeshVertexAttributes";
var c2 = s.getLogger(l2);
var p2 = i = class extends l {
  constructor(r) {
    super(r), this.color = null, this.position = new Float64Array(0), this.uv = null, this.normal = null, this.tangent = null;
  }
  castColor(r) {
    return g(r, Uint8Array, [Uint8ClampedArray], { loggerTag: ".color=", stride: 4 }, c2);
  }
  castPosition(r) {
    r && r instanceof Float32Array && c2.warn(".position=", "Setting position attribute from a Float32Array may cause precision problems. Consider storing data in a Float64Array or a regular number array");
    return g(r, Float64Array, [Float32Array], { loggerTag: ".position=", stride: 3 }, c2);
  }
  castUv(r) {
    return g(r, Float32Array, [Float64Array], { loggerTag: ".uv=", stride: 2 }, c2);
  }
  castNormal(r) {
    return g(r, Float32Array, [Float64Array], { loggerTag: ".normal=", stride: 3 }, c2);
  }
  castTangent(r) {
    return g(r, Float32Array, [Float64Array], { loggerTag: ".tangent=", stride: 4 }, c2);
  }
  clone() {
    const r = { position: p(this.position), uv: p(this.uv), normal: p(this.normal), tangent: p(this.tangent), color: p(this.color) };
    return new i(r);
  }
  clonePositional() {
    const r = { position: p(this.position), normal: p(this.normal), tangent: p(this.tangent), uv: this.uv, color: this.color };
    return new i(r);
  }
};
function u(r, o, t, n) {
  const { loggerTag: e2, stride: a3 } = o;
  return r.length % a3 != 0 ? (n.error(e2, `Invalid array length, expected a multiple of ${a3}`), new t([])) : r;
}
function g(r, o, t, n, e2) {
  if (!r) return r;
  if (r instanceof o) return u(r, n, o, e2);
  for (const a3 of t) if (r instanceof a3) return u(new o(r), n, o, e2);
  if (Array.isArray(r)) return u(new o(r), n, o, e2);
  {
    const n2 = t.map((r2) => `'${r2.name}'`);
    return e2.error(`Failed to set property, expected one of ${n2}, but got ${r.constructor.name}`), new o([]);
  }
}
function m(r, o, t) {
  o[t] = y2(r);
}
function y2(r) {
  const o = new Array(r.length);
  for (let t = 0; t < r.length; t++) o[t] = r[t];
  return o;
}
e([y({ json: { write: m } })], p2.prototype, "color", void 0), e([s2("color")], p2.prototype, "castColor", null), e([y({ nonNullable: true, json: { write: m } })], p2.prototype, "position", void 0), e([s2("position")], p2.prototype, "castPosition", null), e([y({ json: { write: m } })], p2.prototype, "uv", void 0), e([s2("uv")], p2.prototype, "castUv", null), e([y({ json: { write: m } })], p2.prototype, "normal", void 0), e([s2("normal")], p2.prototype, "castNormal", null), e([y({ json: { write: m } })], p2.prototype, "tangent", void 0), e([s2("tangent")], p2.prototype, "castTangent", null), p2 = i = e([a(l2)], p2);

// node_modules/@arcgis/core/geometry/support/MeshComponent.js
var u2;
var m2 = "esri.geometry.support.MeshComponent";
var h = s.getLogger(m2);
var f = u2 = class extends l {
  static from(r) {
    return v(u2, r);
  }
  constructor(r) {
    super(r), this.faces = null, this.material = null, this.shading = "source", this.trustSourceNormals = false;
  }
  castFaces(r) {
    return g(r, Uint32Array, [Uint16Array], { loggerTag: ".faces=", stride: 3 }, h);
  }
  castMaterial(r) {
    return v(r && "object" == typeof r && ("metallic" in r || "roughness" in r || "metallicRoughnessTexture" in r) ? c : a2, r);
  }
  clone() {
    return new u2({ faces: p(this.faces), shading: this.shading, material: p(this.material), trustSourceNormals: this.trustSourceNormals });
  }
  cloneWithDeduplication(r, t) {
    const s3 = { faces: p(this.faces), shading: this.shading, material: this.material ? this.material.cloneWithDeduplication(r, t) : null, trustSourceNormals: this.trustSourceNormals };
    return new u2(s3);
  }
};
e([y({ json: { write: true } })], f.prototype, "faces", void 0), e([s2("faces")], f.prototype, "castFaces", null), e([y({ type: a2, json: { write: true } })], f.prototype, "material", void 0), e([s2("material")], f.prototype, "castMaterial", null), e([y({ type: String, json: { write: true } })], f.prototype, "shading", void 0), e([y({ type: Boolean })], f.prototype, "trustSourceNormals", void 0), f = u2 = e([a(m2)], f);
var g2 = f;

export {
  p2 as p,
  g2 as g
};
//# sourceMappingURL=chunk-YGFDIZJP.js.map
