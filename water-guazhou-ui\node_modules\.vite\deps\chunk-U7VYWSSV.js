import {
  A as A2,
  M,
  v as v2
} from "./chunk-FQMXSCOG.js";
import {
  A,
  B,
  E,
  F as F2,
  J,
  L,
  O as O2,
  U,
  Y,
  p2,
  w,
  x as x2
} from "./chunk-6IU6DQRF.js";
import {
  b,
  c,
  f2 as f,
  n as n2,
  s as s3
} from "./chunk-YELYN22P.js";
import {
  e as e2
} from "./chunk-QYOAH6AO.js";
import {
  h,
  o as o2,
  p
} from "./chunk-YEODPCXQ.js";
import {
  l
} from "./chunk-EIGTETCG.js";
import {
  F,
  H,
  O,
  P,
  e,
  g,
  o,
  r as r2,
  s as s2,
  u,
  v,
  x,
  z
} from "./chunk-MQAXMQFG.js";
import {
  n,
  r,
  t
} from "./chunk-36FLFRUE.js";
import {
  s
} from "./chunk-RV4I37UI.js";

// node_modules/@arcgis/core/chunks/boundedPlane.js
var L2 = s.getLogger("esri.views.3d.support.geometryUtils.boundedPlane");
var z2 = class {
  constructor() {
    this.plane = p2(), this.origin = n(), this.basis1 = n(), this.basis2 = n();
  }
};
var G = z2;
function W(s4 = Ns) {
  return { plane: p2(s4.plane), origin: t(s4.origin), basis1: t(s4.basis1), basis2: t(s4.basis2) };
}
function X(s4, t2, i) {
  const n3 = Ts.get();
  return n3.origin = s4, n3.basis1 = t2, n3.basis2 = i, n3.plane = E(0, 0, 0, 0), J2(n3), n3;
}
function Z(s4, t2 = W()) {
  return H2(s4.origin, s4.basis1, s4.basis2, t2);
}
function D(s4, t2) {
  r2(t2.origin, s4.origin), r2(t2.basis1, s4.basis1), r2(t2.basis2, s4.basis2), A(t2.plane, s4.plane);
}
function H2(s4, t2, i, n3 = W()) {
  return r2(n3.origin, s4), r2(n3.basis1, t2), r2(n3.basis2, i), J2(n3), Ps(n3, "fromValues()"), n3;
}
function J2(s4) {
  O2(s4.basis2, s4.basis1, s4.origin, s4.plane);
}
function K(s4, t2, i) {
  s4 !== i && Z(s4, i);
  const n3 = g(c.get(), ds(s4), t2);
  return u(i.origin, i.origin, n3), i.plane[3] -= t2, i;
}
function Q(s4, t2, i) {
  return $(t2, i), K(i, bs(s4, s4.origin), i), i;
}
function $(s4, t2 = W()) {
  const i = (s4[2] - s4[0]) / 2, n3 = (s4[3] - s4[1]) / 2;
  return o(t2.origin, s4[0] + i, s4[1] + n3, 0), o(t2.basis1, i, 0, 0), o(t2.basis2, 0, n3, 0), F2(0, 0, 1, 0, t2.plane), t2;
}
function ss(s4, t2, i) {
  return !!x2(s4.plane, t2, i) && js(s4, i);
}
function ts(s4, t2, i) {
  if (ss(s4, t2, i)) return i;
  const n3 = is(s4, t2, c.get());
  return u(i, t2.origin, g(c.get(), t2.direction, x(t2.origin, n3) / s2(t2.direction))), i;
}
function is(s4, i, n3) {
  const o3 = ws.get();
  vs(s4, i, o3, ws.get());
  let r3 = Number.POSITIVE_INFINITY;
  for (const e3 of As) {
    const c2 = ys(s4, e3, Ms.get()), u2 = c.get();
    if (U(o3, c2, u2)) {
      const s5 = H(c.get(), i.origin, u2), o4 = Math.abs(l(P(i.direction, s5)));
      o4 < r3 && (r3 = o4, r2(n3, u2));
    }
  }
  return r3 === Number.POSITIVE_INFINITY ? ns(s4, i, n3) : n3;
}
function ns(s4, t2, i) {
  if (ss(s4, t2, i)) return i;
  const n3 = ws.get(), o3 = ws.get();
  vs(s4, t2, n3, o3);
  let r3 = Number.POSITIVE_INFINITY;
  for (const e3 of As) {
    const c2 = ys(s4, e3, Ms.get()), u2 = c.get();
    if (w(n3, c2, u2)) {
      const s5 = b(t2, u2);
      if (!B(o3, u2)) continue;
      s5 < r3 && (r3 = s5, r2(i, u2));
    }
  }
  return es(s4, t2.origin) < r3 && os(s4, t2.origin, i), i;
}
function os(s4, t2, i) {
  const n3 = J(s4.plane, t2, c.get()), o3 = A2(Is(s4, s4.basis1), n3, -1, 1, c.get()), r3 = A2(Is(s4, s4.basis2), n3, -1, 1, c.get());
  return e(i, u(c.get(), o3, r3), s4.origin), i;
}
function rs(s4, t2, i) {
  const { origin: n3, basis1: o3, basis2: r3 } = s4, e3 = e(c.get(), t2, n3), a = f(o3, e3), c2 = f(r3, e3), u2 = f(ds(s4), e3);
  return o(i, a, c2, u2);
}
function es(s4, t2) {
  const i = rs(s4, t2, c.get()), { basis1: n3, basis2: o3 } = s4, r3 = s2(n3), e3 = s2(o3), a = Math.max(Math.abs(i[0]) - r3, 0), c2 = Math.max(Math.abs(i[1]) - e3, 0), u2 = i[2];
  return a * a + c2 * c2 + u2 * u2;
}
function as(s4, t2) {
  return Math.sqrt(es(s4, t2));
}
function cs(s4, t2) {
  let i = Number.NEGATIVE_INFINITY;
  for (const n3 of As) {
    const o3 = ys(s4, n3, Ms.get()), r3 = M(o3, t2);
    r3 > i && (i = r3);
  }
  return Math.sqrt(i);
}
function us(s4, t2) {
  return B(s4.plane, t2) && js(s4, t2);
}
function gs(s4, t2, i, n3) {
  return hs(s4, i, n3);
}
function bs(s4, t2) {
  const i = -s4.plane[3];
  return f(ds(s4), t2) - i;
}
function fs(s4, t2, i, n3) {
  const o3 = bs(s4, t2), r3 = g(Ss, ds(s4), i - o3);
  return u(n3, t2, r3), n3;
}
function ps(s4, t2) {
  return F(s4.basis1, t2.basis1) && F(s4.basis2, t2.basis2) && F(s4.origin, t2.origin);
}
function ls(s4, t2, i) {
  return s4 !== i && Z(s4, i), h(xs, t2), o2(xs, xs), O(i.basis1, s4.basis1, xs), O(i.basis2, s4.basis2, xs), O(Y(i.plane), Y(s4.plane), xs), O(i.origin, s4.origin, t2), L(i.plane, i.plane, i.origin), i;
}
function ms(s4, t2, i, n3) {
  return s4 !== n3 && Z(s4, n3), p(Vs, t2, i), O(n3.basis1, s4.basis1, Vs), O(n3.basis2, s4.basis2, Vs), J2(n3), n3;
}
function ds(s4) {
  return Y(s4.plane);
}
function hs(s4, t2, i) {
  switch (t2) {
    case n2.X:
      r2(i, s4.basis1), z(i, i);
      break;
    case n2.Y:
      r2(i, s4.basis2), z(i, i);
      break;
    case n2.Z:
      r2(i, ds(s4));
  }
  return i;
}
function js(s4, t2) {
  const i = e(c.get(), t2, s4.origin), n3 = v(s4.basis1), o3 = v(s4.basis2), r3 = P(s4.basis1, i), e3 = P(s4.basis2, i);
  return -r3 - n3 < 0 && r3 - n3 < 0 && -e3 - o3 < 0 && e3 - o3 < 0;
}
function Is(s4, t2) {
  const i = Ms.get();
  return r2(i.origin, s4.origin), r2(i.vector, t2), i;
}
function ys(s4, t2, i) {
  const { basis1: n3, basis2: o3, origin: r3 } = s4, e3 = g(c.get(), n3, t2.origin[0]), a = g(c.get(), o3, t2.origin[1]);
  u(i.origin, e3, a), u(i.origin, i.origin, r3);
  const g2 = g(c.get(), n3, t2.direction[0]), b2 = g(c.get(), o3, t2.direction[1]);
  return g(i.vector, u(g2, g2, b2), 2), i;
}
function Ps(s4, t2) {
  Math.abs(P(s4.basis1, s4.basis2) / (s2(s4.basis1) * s2(s4.basis2))) > 1e-6 && L2.warn(t2, "Provided basis vectors are not perpendicular"), Math.abs(P(s4.basis1, ds(s4))) > 1e-6 && L2.warn(t2, "Basis vectors and plane normal are not perpendicular"), Math.abs(-P(ds(s4), s4.origin) - s4.plane[3]) > 1e-6 && L2.warn(t2, "Plane offset is not consistent with plane origin");
}
function vs(s4, t2, i, n3) {
  const o3 = ds(s4);
  O2(o3, t2.direction, t2.origin, i), O2(Y(i), o3, t2.origin, n3);
}
var Ns = { plane: p2(), origin: r(0, 0, 0), basis1: r(1, 0, 0), basis2: r(0, 1, 0) };
var ws = new s3(p2);
var Ms = new s3(v2);
var Ss = n();
var Ts = new s3(() => W());
var As = [{ origin: [-1, -1], direction: [1, 0] }, { origin: [1, -1], direction: [0, 1] }, { origin: [1, 1], direction: [-1, 0] }, { origin: [-1, 1], direction: [0, -1] }];
var xs = e2();
var Vs = e2();
var _s = Object.freeze(Object.defineProperty({ __proto__: null, BoundedPlaneClass: G, UP: Ns, altitudeAt: bs, axisAt: gs, closestPoint: ns, closestPointOnSilhouette: is, copy: Z, copyWithoutVerify: D, create: W, distance: as, distance2: es, distanceToSilhouette: cs, elevate: K, equals: ps, extrusionContainsPoint: us, fromAABoundingRect: $, fromValues: H2, intersectRay: ss, intersectRayClosestSilhouette: ts, normal: ds, projectPoint: os, projectPointLocal: rs, rotate: ms, setAltitudeAt: fs, setExtent: Q, transform: ls, updateUnboundedPlane: J2, wrap: X }, Symbol.toStringTag, { value: "Module" }));

export {
  W,
  Z,
  J2 as J,
  $,
  as
};
//# sourceMappingURL=chunk-U7VYWSSV.js.map
