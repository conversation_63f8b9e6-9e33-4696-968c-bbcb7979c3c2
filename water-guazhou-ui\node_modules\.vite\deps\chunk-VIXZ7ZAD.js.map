{"version": 3, "sources": ["../../@arcgis/core/symbols/MarkerSymbol.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import{toPt as e}from\"../core/screenUtils.js\";import{property as s}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as r}from\"../core/accessorSupport/decorators/subclass.js\";import o from\"./Symbol.js\";let p=class extends o{constructor(t){super(t),this.angle=0,this.type=null,this.xoffset=0,this.yoffset=0,this.size=9}hash(){return`${this.type}.${this.angle}.${this.size}.${this.xoffset}.${this.yoffset}`}};t([s({type:Number,json:{read:t=>t&&-1*t,write:(t,e)=>e.angle=t&&-1*t}})],p.prototype,\"angle\",void 0),t([s({type:[\"simple-marker\",\"picture-marker\"],readOnly:!0})],p.prototype,\"type\",void 0),t([s({type:Number,cast:e,json:{write:!0}})],p.prototype,\"xoffset\",void 0),t([s({type:Number,cast:e,json:{write:!0}})],p.prototype,\"yoffset\",void 0),t([s({type:Number,cast:t=>\"auto\"===t?t:e(t),json:{write:!0}})],p.prototype,\"size\",void 0),p=t([r(\"esri.symbols.MarkerSymbol\")],p);const i=p;export{i as default};\n"], "mappings": ";;;;;;;;;;;;;;;AAIoV,IAAI,IAAE,cAAcA,GAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,QAAM,GAAE,KAAK,OAAK,MAAK,KAAK,UAAQ,GAAE,KAAK,UAAQ,GAAE,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAM,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,OAAG,KAAG,KAAG,GAAE,OAAM,CAAC,GAAEC,OAAIA,GAAE,QAAM,KAAG,KAAG,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,iBAAgB,gBAAgB,GAAE,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,OAAG,WAAS,IAAE,IAAE,EAAE,CAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,2BAA2B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["a", "e"]}