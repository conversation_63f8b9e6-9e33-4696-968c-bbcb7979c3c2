{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/TileLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import i from\"../../../core/Logger.js\";import{isAbortError as t}from\"../../../core/promiseUtils.js\";import{watch as r}from\"../../../core/reactiveUtils.js\";import{property as s}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import{equals as l}from\"../../../geometry/support/spatialReferenceUtils.js\";import{GraphicsCollection as h}from\"../../../support/GraphicsCollection.js\";import\"../../../core/Error.js\";import\"../../../core/has.js\";import\"../../../core/scheduling.js\";import\"../../../request.js\";import\"../../../chunks/index2.js\";import\"../../../core/urlUtils.js\";import\"../../../chunks/index3.js\";import\"../../../layers/effects/EffectView.js\";import\"../engine/DisplayObject.js\";import\"../engine/webgl/effects/highlight/HighlightGradient.js\";import\"../engine/webgl/BufferPool.js\";import\"../engine/webgl/enums.js\";import\"../engine/webgl/brushes/BrushBitmap.js\";import\"../../../chunks/vec4f32.js\";import\"../engine/webgl/Utils.js\";import\"../engine/webgl/shaders/BackgroundPrograms.js\";import\"../../webgl/enums.js\";import\"../../webgl/checkWebGLError.js\";import\"../../webgl/context-util.js\";import\"../../../chunks/builtins.js\";import\"../../../core/RandomLCG.js\";import\"../engine/webgl/materialKey/MaterialKey.js\";import\"../engine/webgl/techniques/Technique.js\";import\"../engine/webgl/techniques/dotDensity/TechniqueDotDensity.js\";import\"../engine/webgl/techniques/heatmap/TechniqueHeatmap.js\";import\"../engine/webgl/techniques/pieChart/TechniquePieChart.js\";import\"../../webgl/BufferObject.js\";import\"../../webgl/FramebufferObject.js\";import\"../../webgl/Texture.js\";import\"../../webgl/VertexArrayObject.js\";import\"../engine/webgl/brushes/WGLBrushHeatmap.js\";import\"../engine/webgl/DefaultVertexAttributeLayouts.js\";import\"../engine/webgl/shaders/TileInfoPrograms.js\";import\"../engine/webgl/brushes/WGLGeometryBrushMarker.js\";import\"../../../core/mathUtils.js\";import\"../engine/webgl/number.js\";import\"../engine/vectorTiles/style/StyleDefinition.js\";import\"../../../chunks/vec2f32.js\";import\"../engine/vectorTiles/enums.js\";import\"../engine/vectorTiles/shaders/sources/resolver.js\";import\"../engine/webgl/shaders/BitBlitPrograms.js\";import\"../engine/webgl/shaders/sources/resolver.js\";import\"../engine/webgl/TextureManager.js\";import\"../engine/webgl/shaders/StencilPrograms.js\";import\"../engine/webgl/effects/BlendEffect.js\";import\"../engine/webgl/shaders/HighlightPrograms.js\";import\"../engine/webgl/Profiler.js\";import\"../../webgl/renderState.js\";import\"../../3d/webgl-engine/core/shaderModules/interfaces.js\";import\"../../../core/floatRGBA.js\";import\"../../3d/webgl-engine/lib/OrderIndependentTransparency.js\";import\"../../../chunks/webgl-debug.js\";import\"../LabelManager.js\";import a from\"./graphics/GraphicsView2D.js\";import\"../engine/webgl/AttributeStoreView.js\";import\"../../../chunks/earcut.js\";import\"../../../layers/graphics/featureConversionUtils.js\";import\"../../../core/unitUtils.js\";import\"../../../renderers/support/lengthUtils.js\";import\"../../../chunks/vec3f32.js\";import\"../../../geometry/support/normalizeUtils.js\";import\"../navigation/MapViewNavigation.js\";import\"../../../core/asyncUtils.js\";import\"../engine/webgl/shaders/MagnifierPrograms.js\";import{BitmapTileLayerView2D as n}from\"./BitmapTileLayerView2D.js\";import{LayerView2DMixin as p}from\"./LayerView2D.js\";import g from\"./graphics/HighlightGraphicContainer.js\";import{createBlankImage as c,resampleImage as m}from\"./support/imageUtils.js\";import u from\"../tiling/TileInfoView.js\";import f from\"../tiling/TileKey.js\";import w from\"../tiling/TileQueue.js\";import d from\"../tiling/TileStrategy.js\";import j from\"../../layers/LayerView.js\";import y from\"../../layers/RefreshableLayerView.js\";import{isMapServiceLayerView as b,MapServiceLayerViewHelper as _}from\"../../layers/support/MapServiceLayerViewHelper.js\";import{createQueryGeometry as V}from\"../../support/drapedUtils.js\";const T=[0,0];let v=class extends(y(n(p(j)))){constructor(){super(...arguments),this._fetchQueue=null,this._highlightGraphics=new h,this._highlightView=null,this._popupHighlightHelper=null,this._tileStrategy=null,this.layer=null}get resampling(){return!(\"resampling\"in this.layer)||!1!==this.layer.resampling}update(e){this._fetchQueue.pause(),this._fetchQueue.state=e.state,this._tileStrategy.update(e),this._fetchQueue.resume(),this._highlightView?.processUpdate(e)}attach(){const e=\"tileServers\"in this.layer?this.layer.tileServers:null;if(this._tileInfoView=new u(this.layer.tileInfo,this.layer.fullExtent),this._fetchQueue=new w({tileInfoView:this._tileInfoView,concurrency:e&&10*e.length||10,process:(e,i)=>this.fetchTile(e,i)}),this._tileStrategy=new d({cachePolicy:\"keep\",resampling:this.resampling,acquireTile:e=>this.acquireTile(e),releaseTile:e=>this.releaseTile(e),tileInfoView:this._tileInfoView}),b(this,this.layer)){const e=this._highlightView=new a({view:this.view,graphics:this._highlightGraphics,requestUpdateCallback:()=>this.requestUpdate(),container:new g(this.view.featuresTilingScheme),defaultPointSymbolEnabled:!1});this.container.addChild(this._highlightView.container),this._popupHighlightHelper=new _({createFetchPopupFeaturesQueryGeometry:(e,i)=>V(e,i,this.view),highlightGraphics:this._highlightGraphics,highlightGraphicUpdated:(i,t)=>{e.graphicUpdateHandler({graphic:i,property:t})},layerView:this,updatingHandles:this.updatingHandles})}this.requestUpdate(),this.addAttachHandles(r((()=>this.resampling),(()=>{this.doRefresh()}))),super.attach()}detach(){super.detach(),this._tileStrategy.destroy(),this._fetchQueue.clear(),this.container.removeAllChildren(),this._popupHighlightHelper?.destroy(),this._fetchQueue=this._tileStrategy=this._tileInfoView=this._popupHighlightHelper=null}async fetchPopupFeatures(e,i){return this._popupHighlightHelper?this._popupHighlightHelper.fetchPopupFeatures(e,i):[]}highlight(e){return this._popupHighlightHelper?this._popupHighlightHelper.highlight(e):{remove(){}}}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this.requestUpdate()}supportsSpatialReference(e){return l(this.layer.tileInfo?.spatialReference,e)}async doRefresh(){!this.attached||this.updateRequested||this.suspended||(this._fetchQueue.reset(),this._tileStrategy.tiles.forEach((e=>this._enqueueTileFetch(e))))}isUpdating(){return this._fetchQueue?.updating??!1}acquireTile(e){const i=this._bitmapView.createTile(e),t=i.bitmap;return[t.x,t.y]=this._tileInfoView.getTileCoords(T,i.key),t.resolution=this._tileInfoView.getTileResolution(i.key),[t.width,t.height]=this._tileInfoView.tileInfo.size,this._enqueueTileFetch(i),this._bitmapView.addChild(i),this.requestUpdate(),i}releaseTile(e){this._fetchQueue.abort(e.key.id),this._bitmapView.removeChild(e),e.once(\"detach\",(()=>e.destroy())),this.requestUpdate()}async fetchTile(e,i={}){const r=\"tilemapCache\"in this.layer?this.layer.tilemapCache:null,{signal:s,resamplingLevel:o=0}=i;if(!r)try{return await this._fetchImage(e,s)}catch(a){if(!t(a)&&!this.resampling)return c(this._tileInfoView.tileInfo.size);if(o<3){const t=this._tileInfoView.getTileParentId(e.id);if(t){const r=new f(t),s=await this.fetchTile(r,{...i,resamplingLevel:o+1});return m(this._tileInfoView,s,r,e)}}throw a}const l=new f(0,0,0,0);let h;try{if(await r.fetchAvailabilityUpsample(e.level,e.row,e.col,l,{signal:s}),l.level!==e.level&&!this.resampling)return c(this._tileInfoView.tileInfo.size);h=await this._fetchImage(l,s)}catch(a){if(t(a))throw a;h=await this._fetchImage(e,s)}return this.resampling?m(this._tileInfoView,h,l,e):h}async _enqueueTileFetch(e){if(!this._fetchQueue.has(e.key.id)){try{const i=await this._fetchQueue.push(e.key);e.bitmap.source=i,e.bitmap.width=this._tileInfoView.tileInfo.size[0],e.bitmap.height=this._tileInfoView.tileInfo.size[1],e.once(\"attach\",(()=>this.requestUpdate()))}catch(r){t(r)||i.getLogger(this.declaredClass).error(r)}this.requestUpdate()}}async _fetchImage(e,i){return this.layer.fetchImageBitmapTile(e.level,e.row,e.col,{signal:i})}};e([s()],v.prototype,\"_fetchQueue\",void 0),e([s()],v.prototype,\"resampling\",null),v=e([o(\"esri.views.2d.layers.TileLayerView2D\")],v);const I=v;export{I as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIi/H,IAAM,IAAE,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAcA,GAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,cAAY,MAAK,KAAK,qBAAmB,IAAI,KAAE,KAAK,iBAAe,MAAK,KAAK,wBAAsB,MAAK,KAAK,gBAAc,MAAK,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,IAAI,aAAY;AAAC,WAAM,EAAE,gBAAe,KAAK,UAAQ,UAAK,KAAK,MAAM;AAAA,EAAU;AAAA,EAAC,OAAOC,IAAE;AAJ/yI;AAIgzI,SAAK,YAAY,MAAM,GAAE,KAAK,YAAY,QAAMA,GAAE,OAAM,KAAK,cAAc,OAAOA,EAAC,GAAE,KAAK,YAAY,OAAO,IAAE,UAAK,mBAAL,mBAAqB,cAAcA;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,UAAMA,KAAE,iBAAgB,KAAK,QAAM,KAAK,MAAM,cAAY;AAAK,QAAG,KAAK,gBAAc,IAAI,EAAE,KAAK,MAAM,UAAS,KAAK,MAAM,UAAU,GAAE,KAAK,cAAY,IAAIC,GAAE,EAAC,cAAa,KAAK,eAAc,aAAYD,MAAG,KAAGA,GAAE,UAAQ,IAAG,SAAQ,CAACA,IAAED,OAAI,KAAK,UAAUC,IAAED,EAAC,EAAC,CAAC,GAAE,KAAK,gBAAc,IAAI,EAAE,EAAC,aAAY,QAAO,YAAW,KAAK,YAAW,aAAY,CAAAC,OAAG,KAAK,YAAYA,EAAC,GAAE,aAAY,CAAAA,OAAG,KAAK,YAAYA,EAAC,GAAE,cAAa,KAAK,cAAa,CAAC,GAAE,EAAE,MAAK,KAAK,KAAK,GAAE;AAAC,YAAMA,KAAE,KAAK,iBAAe,IAAI,GAAE,EAAC,MAAK,KAAK,MAAK,UAAS,KAAK,oBAAmB,uBAAsB,MAAI,KAAK,cAAc,GAAE,WAAU,IAAI,EAAE,KAAK,KAAK,oBAAoB,GAAE,2BAA0B,MAAE,CAAC;AAAE,WAAK,UAAU,SAAS,KAAK,eAAe,SAAS,GAAE,KAAK,wBAAsB,IAAI,EAAE,EAAC,uCAAsC,CAACA,IAAED,OAAIG,GAAEF,IAAED,IAAE,KAAK,IAAI,GAAE,mBAAkB,KAAK,oBAAmB,yBAAwB,CAACA,IAAEI,OAAI;AAAC,QAAAH,GAAE,qBAAqB,EAAC,SAAQD,IAAE,UAASI,GAAC,CAAC;AAAA,MAAC,GAAE,WAAU,MAAK,iBAAgB,KAAK,gBAAe,CAAC;AAAA,IAAC;AAAC,SAAK,cAAc,GAAE,KAAK,iBAAiB,EAAG,MAAI,KAAK,YAAa,MAAI;AAAC,WAAK,UAAU;AAAA,IAAC,CAAE,CAAC,GAAE,MAAM,OAAO;AAAA,EAAC;AAAA,EAAC,SAAQ;AAJjiL;AAIkiL,UAAM,OAAO,GAAE,KAAK,cAAc,QAAQ,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,UAAU,kBAAkB,IAAE,UAAK,0BAAL,mBAA4B,WAAU,KAAK,cAAY,KAAK,gBAAc,KAAK,gBAAc,KAAK,wBAAsB;AAAA,EAAI;AAAA,EAAC,MAAM,mBAAmBH,IAAED,IAAE;AAAC,WAAO,KAAK,wBAAsB,KAAK,sBAAsB,mBAAmBC,IAAED,EAAC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUC,IAAE;AAAC,WAAO,KAAK,wBAAsB,KAAK,sBAAsB,UAAUA,EAAC,IAAE,EAAC,SAAQ;AAAA,IAAC,EAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAE;AAJ9lM;AAI+lM,WAAO,GAAE,UAAK,MAAM,aAAX,mBAAqB,kBAAiBA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAW;AAAC,KAAC,KAAK,YAAU,KAAK,mBAAiB,KAAK,cAAY,KAAK,YAAY,MAAM,GAAE,KAAK,cAAc,MAAM,QAAS,CAAAA,OAAG,KAAK,kBAAkBA,EAAC,CAAE;AAAA,EAAE;AAAA,EAAC,aAAY;AAJj0M;AAIk0M,aAAO,UAAK,gBAAL,mBAAkB,aAAU;AAAA,EAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMD,KAAE,KAAK,YAAY,WAAWC,EAAC,GAAEG,KAAEJ,GAAE;AAAO,WAAM,CAACI,GAAE,GAAEA,GAAE,CAAC,IAAE,KAAK,cAAc,cAAc,GAAEJ,GAAE,GAAG,GAAEI,GAAE,aAAW,KAAK,cAAc,kBAAkBJ,GAAE,GAAG,GAAE,CAACI,GAAE,OAAMA,GAAE,MAAM,IAAE,KAAK,cAAc,SAAS,MAAK,KAAK,kBAAkBJ,EAAC,GAAE,KAAK,YAAY,SAASA,EAAC,GAAE,KAAK,cAAc,GAAEA;AAAA,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,YAAY,MAAMA,GAAE,IAAI,EAAE,GAAE,KAAK,YAAY,YAAYA,EAAC,GAAEA,GAAE,KAAK,UAAU,MAAIA,GAAE,QAAQ,CAAE,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,MAAM,UAAUA,IAAED,KAAE,CAAC,GAAE;AAAC,UAAMK,KAAE,kBAAiB,KAAK,QAAM,KAAK,MAAM,eAAa,MAAK,EAAC,QAAOC,IAAE,iBAAgBC,KAAE,EAAC,IAAEP;AAAE,QAAG,CAACK,GAAE,KAAG;AAAC,aAAO,MAAM,KAAK,YAAYJ,IAAEK,EAAC;AAAA,IAAC,SAAOH,IAAE;AAAC,UAAG,CAAC,EAAEA,EAAC,KAAG,CAAC,KAAK,WAAW,QAAO,EAAE,KAAK,cAAc,SAAS,IAAI;AAAE,UAAGI,KAAE,GAAE;AAAC,cAAMH,KAAE,KAAK,cAAc,gBAAgBH,GAAE,EAAE;AAAE,YAAGG,IAAE;AAAC,gBAAMC,KAAE,IAAIJ,GAAEG,EAAC,GAAEE,KAAE,MAAM,KAAK,UAAUD,IAAE,EAAC,GAAGL,IAAE,iBAAgBO,KAAE,EAAC,CAAC;AAAE,iBAAOC,GAAE,KAAK,eAAcF,IAAED,IAAEJ,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,YAAME;AAAA,IAAC;AAAC,UAAMM,KAAE,IAAIR,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,QAAIS;AAAE,QAAG;AAAC,UAAG,MAAML,GAAE,0BAA0BJ,GAAE,OAAMA,GAAE,KAAIA,GAAE,KAAIQ,IAAE,EAAC,QAAOH,GAAC,CAAC,GAAEG,GAAE,UAAQR,GAAE,SAAO,CAAC,KAAK,WAAW,QAAO,EAAE,KAAK,cAAc,SAAS,IAAI;AAAE,MAAAS,KAAE,MAAM,KAAK,YAAYD,IAAEH,EAAC;AAAA,IAAC,SAAOH,IAAE;AAAC,UAAG,EAAEA,EAAC,EAAE,OAAMA;AAAE,MAAAO,KAAE,MAAM,KAAK,YAAYT,IAAEK,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK,aAAWE,GAAE,KAAK,eAAcE,IAAED,IAAER,EAAC,IAAES;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAkBT,IAAE;AAAC,QAAG,CAAC,KAAK,YAAY,IAAIA,GAAE,IAAI,EAAE,GAAE;AAAC,UAAG;AAAC,cAAMD,KAAE,MAAM,KAAK,YAAY,KAAKC,GAAE,GAAG;AAAE,QAAAA,GAAE,OAAO,SAAOD,IAAEC,GAAE,OAAO,QAAM,KAAK,cAAc,SAAS,KAAK,CAAC,GAAEA,GAAE,OAAO,SAAO,KAAK,cAAc,SAAS,KAAK,CAAC,GAAEA,GAAE,KAAK,UAAU,MAAI,KAAK,cAAc,CAAE;AAAA,MAAC,SAAOI,IAAE;AAAC,UAAEA,EAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,EAAC;AAAA,MAAC;AAAC,WAAK,cAAc;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYJ,IAAED,IAAE;AAAC,WAAO,KAAK,MAAM,qBAAqBC,GAAE,OAAMA,GAAE,KAAIA,GAAE,KAAI,EAAC,QAAOD,GAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["i", "e", "y", "a", "t", "r", "s", "o", "n", "l", "h"]}