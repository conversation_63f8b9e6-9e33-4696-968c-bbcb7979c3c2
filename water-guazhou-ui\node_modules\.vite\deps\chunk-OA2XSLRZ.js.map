{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/FeatureEffectLayer.js", "../../@arcgis/core/layers/support/FeatureReduction.js", "../../@arcgis/core/layers/support/FeatureReductionSelection.js", "../../@arcgis/core/layers/support/FeatureReductionBinning.js", "../../@arcgis/core/layers/support/featureReductionUtils.js", "../../@arcgis/core/layers/mixins/FeatureReductionLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as o}from\"../../core/accessorSupport/decorators/subclass.js\";import t from\"../support/FeatureEffect.js\";const s={write:{allowNull:!0}},p=p=>{let c=class extends p{constructor(){super(...arguments),this.featureEffect=null}};return r([e({type:t,json:{origins:{\"web-map\":s,\"portal-item\":s}}})],c.prototype,\"featureEffect\",void 0),c=r([o(\"esri.layers.mixins.FeatureEffectLayer\")],c),c};export{p as FeatureEffectLayer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";let t=class extends o{constructor(){super(...arguments),this.type=null}};r([e({type:[\"selection\",\"cluster\",\"binning\"],readOnly:!0,json:{read:!1,write:!0}})],t.prototype,\"type\",void 0),t=r([s(\"esri.layers.support.FeatureReduction\")],t);export{t as FeatureReduction};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as o}from\"../../core/accessorSupport/decorators/subclass.js\";import{FeatureReduction as s}from\"./FeatureReduction.js\";var t;let c=t=class extends s{constructor(r){super(r),this.type=\"selection\"}clone(){return new t}};r([e({type:[\"selection\"]})],c.prototype,\"type\",void 0),c=t=r([o(\"esri.layers.support.FeatureReductionSelection\")],c);const p=c;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import r from\"../../PopupTemplate.js\";import\"../../renderers/ClassBreaksRenderer.js\";import\"../../renderers/DictionaryRenderer.js\";import\"../../renderers/DotDensityRenderer.js\";import\"../../renderers/HeatmapRenderer.js\";import\"../../renderers/PieChartRenderer.js\";import\"../../renderers/Renderer.js\";import o from\"../../renderers/SimpleRenderer.js\";import t from\"../../renderers/UniqueValueRenderer.js\";import{read as s}from\"../../renderers/support/jsonUtils.js\";import{rendererTypes as i}from\"../../renderers/support/types.js\";import{symbolTypesRenderer as p}from\"../../symbols.js\";import{clone as n}from\"../../core/lang.js\";import{setDeepValue as l}from\"../../core/object.js\";import{property as a}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import{enumeration as d}from\"../../core/accessorSupport/decorators/enumeration.js\";import{reader as m}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as u}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as c}from\"../../core/accessorSupport/decorators/writer.js\";import{createTypeReader as f}from\"../../core/accessorSupport/extensions/serializableProperty/reader.js\";import y from\"./AggregateField.js\";import{labelsVisible as b,popupEnabled as j}from\"./commonProperties.js\";import{FeatureReduction as g}from\"./FeatureReduction.js\";import h from\"./LabelClass.js\";var v;const S=f({types:p}),w=\"esri.layers.support.FeatureReductionBinning\";let I=v=class extends g{constructor(e){super(e),this.type=\"binning\",this.binType=\"geohash\",this.fixedBinLevel=3,this.labelingInfo=null,this.labelsVisible=!0,this.maxScale=0,this.popupEnabled=!0,this.popupTemplate=null,this.fields=[],this.renderer=null}writeFields(e,r,o){const t=e.filter((e=>\"avg_angle\"!==e.statisticType)).map((e=>e.toJSON()));l(o,t,r)}readRenderer(e,r,i){const p=r.drawingInfo?.renderer;return p?s(p,r,i)??void 0:r.defaultSymbol?r.types&&r.types.length?new t({defaultSymbol:S(r.defaultSymbol,r,i),field:r.typeIdField,uniqueValueInfos:r.types.map((e=>({id:e.id,symbol:S(e.symbol,e,i)})))}):new o({symbol:S(r.defaultSymbol,r,i)}):null}clone(){return new v({fields:n(this.fields),fixedBinLevel:this.fixedBinLevel,labelingInfo:n(this.labelingInfo),labelsVisible:this.labelsVisible,maxScale:this.maxScale,popupEnabled:this.popupEnabled,popupTemplate:n(this.popupTemplate),renderer:n(this.renderer)})}};e([d({binning:\"binning\"})],I.prototype,\"type\",void 0),e([d({geohash:\"geohash\"})],I.prototype,\"binType\",void 0),e([a({type:Number,range:{min:1,max:9},json:{write:!0}})],I.prototype,\"fixedBinLevel\",void 0),e([a({type:[h],json:{read:{source:\"drawingInfo.labelingInfo\"},write:{target:\"drawingInfo.labelingInfo\"}}})],I.prototype,\"labelingInfo\",void 0),e([a(b)],I.prototype,\"labelsVisible\",void 0),e([a({type:Number,json:{default:0,name:\"visibilityInfo.maxScale\"}})],I.prototype,\"maxScale\",void 0),e([a(j)],I.prototype,\"popupEnabled\",void 0),e([a({type:r,json:{name:\"popupInfo\",write:!0}})],I.prototype,\"popupTemplate\",void 0),e([a({type:[y],json:{write:!0}})],I.prototype,\"fields\",void 0),e([c(\"fields\")],I.prototype,\"writeFields\",null),e([a({types:i,json:{write:{target:\"drawingInfo.renderer\"}}})],I.prototype,\"renderer\",void 0),e([m(\"renderer\",[\"drawingInfo.renderer\"])],I.prototype,\"readRenderer\",null),I=v=e([u(w)],I);const x=I;export{x as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{FeatureReduction as e}from\"./FeatureReduction.js\";import t from\"./FeatureReductionBinning.js\";import i from\"./FeatureReductionCluster.js\";import n from\"./FeatureReductionSelection.js\";const o={key:\"type\",base:e,typeMap:{cluster:i,binning:t}},r={types:{key:\"type\",base:e,typeMap:{selection:n,cluster:i,binning:t}},json:{name:\"layerDefinition.featureReduction\",write:{allowNull:!0},origins:{\"web-map\":{types:o},\"portal-item\":{types:o},\"web-scene\":{types:{key:\"type\",base:e,typeMap:{selection:n}}}}}};export{r as featureReductionProperty};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";import s from\"../support/AggregateField.js\";import{featureReductionProperty as o}from\"../support/featureReductionUtils.js\";import{createClusterRenderer as i}from\"../../views/2d/layers/support/clusterUtils.js\";const n=n=>{let u=class extends n{constructor(...e){super(...e),this.own(this.watch(\"renderer\",(()=>{if(this.featureReduction){const e=this._normalizeFeatureReduction(this.featureReduction);this._set(\"featureReduction\",e)}}),!0))}set featureReduction(e){const r=this._normalizeFeatureReduction(e);this._set(\"featureReduction\",r)}set renderer(e){}_normalizeFeatureReduction(e){if(\"cluster\"!==e?.type)return e;const r=e.clone(),t=[new s({name:\"cluster_count\",isAutoGenerated:!0,statisticType:\"count\"})],o=(r.fields??[]).filter((e=>!e.isAutoGenerated));if(e.renderer&&!e.renderer.authoringInfo?.isAutoGenerated)return r.fields=[...t,...o],r;if(e.symbol)return r.fields=[...t,...o],r.renderer=null,r;if(!this.renderer)return e;const n=i(t,this.renderer,e,null,!1);return r.fields=[...t,...o],r.renderer=n,r}};return e([r(o)],u.prototype,\"featureReduction\",null),u=e([t(\"esri.layers.mixins.FeatureReductionLayer\")],u),u};export{n as FeatureReductionLayer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIqU,IAAM,IAAE,EAAC,OAAM,EAAC,WAAU,KAAE,EAAC;AAA7B,IAA+BA,KAAE,CAAAA,OAAG;AAAC,MAAIC,KAAE,cAAcD,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,gBAAc;AAAA,IAAI;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,SAAQ,EAAC,WAAU,GAAE,eAAc,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,iBAAgB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,uCAAuC,CAAC,GAAEA,EAAC,GAAEA;AAAC;;;ACAvQ,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,aAAY,WAAU,SAAS,GAAE,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,GAAE,CAAC;;;ACAxO,IAAIC;AAAE,IAAI,IAAEA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK;AAAA,EAAW;AAAA,EAAC,QAAO;AAAC,WAAO,IAAID;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,WAAW,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAEA,KAAE,EAAE,CAAC,EAAE,+CAA+C,CAAC,GAAE,CAAC;AAAE,IAAME,KAAE;;;ACA43B,IAAI;AAAE,IAAM,IAAE,EAAE,EAAC,OAAM,EAAC,CAAC;AAAnB,IAAqBC,KAAE;AAA8C,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,WAAU,KAAK,UAAQ,WAAU,KAAK,gBAAc,GAAE,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAG,KAAK,WAAS,GAAE,KAAK,eAAa,MAAG,KAAK,gBAAc,MAAK,KAAK,SAAO,CAAC,GAAE,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAEH,GAAE,OAAQ,CAAAA,OAAG,gBAAcA,GAAE,aAAc,EAAE,IAAK,CAAAA,OAAGA,GAAE,OAAO,CAAE;AAAE,MAAEE,IAAEC,IAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAEC,IAAE,GAAE;AAJ/2D;AAIg3D,UAAMG,MAAE,KAAAH,GAAE,gBAAF,mBAAe;AAAS,WAAOG,KAAEF,GAAEE,IAAEH,IAAE,CAAC,KAAG,SAAOA,GAAE,gBAAcA,GAAE,SAAOA,GAAE,MAAM,SAAO,IAAI,EAAE,EAAC,eAAc,EAAEA,GAAE,eAAcA,IAAE,CAAC,GAAE,OAAMA,GAAE,aAAY,kBAAiBA,GAAE,MAAM,IAAK,CAAAD,QAAI,EAAC,IAAGA,GAAE,IAAG,QAAO,EAAEA,GAAE,QAAOA,IAAE,CAAC,EAAC,EAAG,EAAC,CAAC,IAAE,IAAII,GAAE,EAAC,QAAO,EAAEH,GAAE,eAAcA,IAAE,CAAC,EAAC,CAAC,IAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,QAAO,EAAE,KAAK,MAAM,GAAE,eAAc,KAAK,eAAc,cAAa,EAAE,KAAK,YAAY,GAAE,eAAc,KAAK,eAAc,UAAS,KAAK,UAAS,cAAa,KAAK,cAAa,eAAc,EAAE,KAAK,aAAa,GAAE,UAAS,EAAE,KAAK,QAAQ,EAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAACC,GAAE,EAAC,SAAQ,UAAS,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAACA,GAAE,EAAC,SAAQ,UAAS,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,2BAA0B,GAAE,OAAM,EAAC,QAAO,2BAA0B,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,GAAE,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAEE,EAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,MAAK,aAAY,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACC,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMD,IAAE,MAAK,EAAC,OAAM,EAAC,QAAO,uBAAsB,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACF,GAAE,YAAW,CAAC,sBAAsB,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,IAAE,IAAE,EAAE,CAAC,EAAEH,EAAC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACA5mG,IAAMO,KAAE,EAAC,KAAI,QAAO,MAAK,GAAE,SAAQ,EAAC,SAAQ,GAAE,SAAQ,EAAC,EAAC;AAAxD,IAA0DC,KAAE,EAAC,OAAM,EAAC,KAAI,QAAO,MAAK,GAAE,SAAQ,EAAC,WAAUC,IAAE,SAAQ,GAAE,SAAQ,EAAC,EAAC,GAAE,MAAK,EAAC,MAAK,oCAAmC,OAAM,EAAC,WAAU,KAAE,GAAE,SAAQ,EAAC,WAAU,EAAC,OAAMF,GAAC,GAAE,eAAc,EAAC,OAAMA,GAAC,GAAE,aAAY,EAAC,OAAM,EAAC,KAAI,QAAO,MAAK,GAAE,SAAQ,EAAC,WAAUE,GAAC,EAAC,EAAC,EAAC,EAAC,EAAC;;;ACAZ,IAAM,IAAE,CAAAC,OAAG;AAAC,MAAIC,KAAE,cAAcD,GAAC;AAAA,IAAC,eAAeE,IAAE;AAAC,YAAM,GAAGA,EAAC,GAAE,KAAK,IAAI,KAAK,MAAM,YAAY,MAAI;AAAC,YAAG,KAAK,kBAAiB;AAAC,gBAAMA,KAAE,KAAK,2BAA2B,KAAK,gBAAgB;AAAE,eAAK,KAAK,oBAAmBA,EAAC;AAAA,QAAC;AAAA,MAAC,GAAG,IAAE,CAAC;AAAA,IAAC;AAAA,IAAC,IAAI,iBAAiBA,IAAE;AAAC,YAAMC,KAAE,KAAK,2BAA2BD,EAAC;AAAE,WAAK,KAAK,oBAAmBC,EAAC;AAAA,IAAC;AAAA,IAAC,IAAI,SAASD,IAAE;AAAA,IAAC;AAAA,IAAC,2BAA2BA,IAAE;AAJl2B;AAIm2B,UAAG,eAAYA,MAAA,gBAAAA,GAAG,MAAK,QAAOA;AAAE,YAAMC,KAAED,GAAE,MAAM,GAAEE,KAAE,CAAC,IAAIC,GAAE,EAAC,MAAK,iBAAgB,iBAAgB,MAAG,eAAc,QAAO,CAAC,CAAC,GAAEC,MAAGH,GAAE,UAAQ,CAAC,GAAG,OAAQ,CAAAD,OAAG,CAACA,GAAE,eAAgB;AAAE,UAAGA,GAAE,YAAU,GAAC,KAAAA,GAAE,SAAS,kBAAX,mBAA0B,iBAAgB,QAAOC,GAAE,SAAO,CAAC,GAAGC,IAAE,GAAGE,EAAC,GAAEH;AAAE,UAAGD,GAAE,OAAO,QAAOC,GAAE,SAAO,CAAC,GAAGC,IAAE,GAAGE,EAAC,GAAEH,GAAE,WAAS,MAAKA;AAAE,UAAG,CAAC,KAAK,SAAS,QAAOD;AAAE,YAAMF,KAAE,EAAEI,IAAE,KAAK,UAASF,IAAE,MAAK,KAAE;AAAE,aAAOC,GAAE,SAAO,CAAC,GAAGC,IAAE,GAAGE,EAAC,GAAEH,GAAE,WAASH,IAAEG;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAEA,EAAC,CAAC,GAAEF,GAAE,WAAU,oBAAmB,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAEA,EAAC,GAAEA;AAAC;", "names": ["p", "c", "t", "r", "p", "w", "e", "r", "o", "t", "p", "a", "o", "r", "p", "n", "u", "e", "r", "t", "a", "o"]}