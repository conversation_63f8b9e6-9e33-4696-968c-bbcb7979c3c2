<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="dialogType === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="测点" prop="stationId" required>
            <el-select
              v-model="form.stationId"
              placeholder="请选择测点"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="station in stationList"
                :key="station.id"
                :label="station.name"
                :value="station.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="记录时间" prop="recordTime" required>
            <el-date-picker
              v-model="form.recordTime"
              type="datetime"
              placeholder="选择记录时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="x"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="原水液位" prop="rawWaterLevel" required>
            <el-input-number
              v-model="form.rawWaterLevel"
              :precision="3"
              :step="0.001"
              :min="0"
              placeholder="请输入原水液位"
              style="width: 100%"
            />
            <span class="unit-text">米</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地下水位" prop="groundwaterLevel" required>
            <el-input-number
              v-model="form.groundwaterLevel"
              :precision="3"
              :step="0.001"
              :min="0"
              placeholder="请输入地下水位"
              style="width: 100%"
            />
            <span class="unit-text">米</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="降雨量" prop="rainfallAmount">
            <el-input-number
              v-model="form.rainfallAmount"
              :precision="2"
              :step="0.1"
              :min="0"
              placeholder="请输入降雨量"
              style="width: 100%"
            />
            <span class="unit-text">毫米</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="蒸发量" prop="evaporationAmount">
            <el-input-number
              v-model="form.evaporationAmount"
              :precision="2"
              :step="0.1"
              :min="0"
              placeholder="请输入蒸发量"
              style="width: 100%"
            />
            <span class="unit-text">毫米</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="地表径流量" prop="surfaceRunoff">
            <el-input-number
              v-model="form.surfaceRunoff"
              :precision="2"
              :step="1"
              :min="0"
              placeholder="请输入地表径流量"
              style="width: 100%"
            />
            <span class="unit-text">立方米</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地下水开采量" prop="extractionAmount">
            <el-input-number
              v-model="form.extractionAmount"
              :precision="2"
              :step="1"
              :min="0"
              placeholder="请输入地下水开采量"
              style="width: 100%"
            />
            <span class="unit-text">立方米</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="土壤含水率" prop="soilMoisture">
            <el-input-number
              v-model="form.soilMoisture"
              :precision="2"
              :step="0.1"
              :min="0"
              :max="100"
              placeholder="请输入土壤含水率"
              style="width: 100%"
            />
            <span class="unit-text">%</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="渗透系数" prop="permeabilityCoefficient">
            <el-input-number
              v-model="form.permeabilityCoefficient"
              :precision="4"
              :step="0.0001"
              :min="0"
              placeholder="请输入渗透系数"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数据来源" prop="dataSource">
            <el-radio-group v-model="form.dataSource">
              <el-radio :label="1">手动录入</el-radio>
              <el-radio :label="2">设备采集</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="dialogType === 'view'">
          <el-form-item label="液位变化" prop="levelChange">
            <el-input
              :model-value="formatLevelChange(form.levelChange)"
              readonly
              style="width: 100%"
            />
            <span class="unit-text">米</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="dialogType !== 'view'" type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  saveWaterLevel,
  updateWaterLevel,
  type ConservationWaterLevel
} from '@/api/waterSource/conservationWaterLevel'
import { getAllStations } from '@/api/station'

// Props
interface Props {
  visible: boolean
  formData: ConservationWaterLevel
  dialogType: 'add' | 'edit' | 'view'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  formData: () => ({} as ConservationWaterLevel),
  dialogType: 'add'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()
const stationList = ref<any[]>([])

// 表单数据
const form = reactive<ConservationWaterLevel>({
  stationId: '',
  rawWaterLevel: 0,
  groundwaterLevel: 0,
  recordTime: '',
  dataSource: 1
} as ConservationWaterLevel)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => {
  const titles = {
    add: '新增涵养水位数据',
    edit: '编辑涵养水位数据',
    view: '查看涵养水位数据'
  }
  return titles[props.dialogType]
})

// 表单验证规则
const rules: FormRules = {
  stationId: [
    { required: true, message: '请选择测点', trigger: 'change' }
  ],
  rawWaterLevel: [
    { required: true, message: '请输入原水液位', trigger: 'blur' }
  ],
  groundwaterLevel: [
    { required: true, message: '请输入地下水位', trigger: 'blur' }
  ],
  recordTime: [
    { required: true, message: '请选择记录时间', trigger: 'change' }
  ]
}

// 监听表单数据变化
watch(() => props.formData, (newData) => {
  Object.assign(form, newData)
}, { deep: true, immediate: true })

// 生命周期
onMounted(() => {
  loadStationList()
})

// 加载测点列表
const loadStationList = async () => {
  try {
    const response = await getAllStations({ type: '水厂' })
    if (response.status === 200) {
      // 后端返回的是PageData格式，数据在data字段中
      stationList.value = response.data.data || []
    }
  } catch (error) {
    console.error('加载测点列表失败:', error)
    ElMessage.error('加载测点列表失败')
  }
}

// 格式化液位变化
const formatLevelChange = (levelChange: number | undefined) => {
  if (levelChange === undefined || levelChange === null) return ''
  const value = Number(levelChange).toFixed(3)
  return levelChange > 0 ? `+${value}` : value
}

// 确认
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = { ...form }
    
    let response
    if (props.dialogType === 'add') {
      response = await saveWaterLevel(submitData)
    } else {
      response = await updateWaterLevel(submitData)
    }
    
    if (response.data.code === 200) {
      ElMessage.success(props.dialogType === 'add' ? '新增成功' : '更新成功')
      emit('confirm')
      handleClose()
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}
</script>

<style scoped>
.unit-text {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

:deep(.el-input-number) {
  flex: 1;
}
</style>
