{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/featureSources/queryEngineUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../core/maybe.js\";import{f as t}from\"../../../../chunks/vec3f64.js\";import{asSnappingPoint as n}from\"../SnappingPoint.js\";import{DrapedEdgeSnappingCandidate as r}from\"../candidates/DrapedEdgeSnappingCandidate.js\";import{EdgeSnappingCandidate as a}from\"../candidates/EdgeSnappingCandidate.js\";import{VertexSnappingCandidate as d}from\"../candidates/VertexSnappingCandidate.js\";function o({x:e,y:r,z:a}){return n(t(e,r,a??0))}function i(e,t){switch(e.type){case\"edge\":return e.draped?new r({edgeStart:o(e.start),edgeEnd:o(e.end),targetPoint:o(e.target),objectId:e.objectId,getGroundElevation:t}):new a({edgeStart:o(e.start),edgeEnd:o(e.end),targetPoint:o(e.target),objectId:e.objectId,isDraped:!1});case\"vertex\":return new d({targetPoint:o(e.target),objectId:e.objectId,isDraped:!1})}}function p(t){return e(t)&&\"3d\"===t.type?(e,n,r)=>t.elevationProvider.getElevation(e,n,r??0,t.spatialReference,\"ground\"):()=>null}export{i as convertSnappingCandidate,p as makeGetGroundElevation};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIwZ,SAAS,EAAE,EAAC,GAAE,GAAE,GAAEA,IAAE,GAAE,EAAC,GAAE;AAAC,SAAO,EAAEA,GAAE,GAAEA,IAAE,KAAG,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,UAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAO,aAAO,EAAE,SAAO,IAAI,EAAE,EAAC,WAAU,EAAE,EAAE,KAAK,GAAE,SAAQ,EAAE,EAAE,GAAG,GAAE,aAAY,EAAE,EAAE,MAAM,GAAE,UAAS,EAAE,UAAS,oBAAmB,EAAC,CAAC,IAAE,IAAIA,GAAE,EAAC,WAAU,EAAE,EAAE,KAAK,GAAE,SAAQ,EAAE,EAAE,GAAG,GAAE,aAAY,EAAE,EAAE,MAAM,GAAE,UAAS,EAAE,UAAS,UAAS,MAAE,CAAC;AAAA,IAAE,KAAI;AAAS,aAAO,IAAIA,GAAE,EAAC,aAAY,EAAE,EAAE,MAAM,GAAE,UAAS,EAAE,UAAS,UAAS,MAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,CAAC,KAAG,SAAO,EAAE,OAAK,CAAC,GAAE,GAAEA,OAAI,EAAE,kBAAkB,aAAa,GAAE,GAAEA,MAAG,GAAE,EAAE,kBAAiB,QAAQ,IAAE,MAAI;AAAI;", "names": ["r"]}