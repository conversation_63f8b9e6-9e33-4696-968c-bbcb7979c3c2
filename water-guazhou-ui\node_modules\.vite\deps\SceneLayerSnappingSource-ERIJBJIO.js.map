{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/lib/edgeRendering/EdgeWorkerHandle.js", "../../@arcgis/core/views/interactive/snapping/featureSources/sceneLayerSource/SceneLayerSnappingSourceWorkerHandle.js", "../../@arcgis/core/views/interactive/snapping/featureSources/I3SSnappingSource.js", "../../@arcgis/core/views/interactive/snapping/featureSources/SceneLayerSnappingSource.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isUint32Array as e}from\"../../../../../core/typedArrayUtil.js\";import{WorkerHandle as t}from\"../../../../../core/workers/WorkerHandle.js\";import{unpackInterleavedBuffer as r}from\"../../../support/buffer/workerHelper.js\";import{extract as s}from\"./edgeProcessing.js\";class n extends t{constructor(e){super(\"EdgeProcessingWorker\",\"extract\",{extract:e=>[e.dataBuffer],extractComponentsEdgeLocations:e=>[e.dataBuffer],extractEdgeLocations:e=>[e.dataBuffer]},e)}async process(e,t,r){if(r)return s(e);const n=await this.invoke(new a(e),t);return this._unpackOutput(n)}async extractEdgeLocations(e,t){const s=await this.invokeMethod(\"extractEdgeLocations\",new a(e),t);return r(s)}async extractComponentsEdgeLocations(e,t){const s=await this.invokeMethod(\"extractComponentsEdgeLocations\",new a(e),t);return r(s)}_unpackOutput(e){return{regular:{instancesData:r(e.regular.instancesData),lodInfo:{lengths:new Float32Array(e.regular.lodInfo.lengths)}},silhouette:{instancesData:r(e.silhouette.instancesData),lodInfo:{lengths:new Float32Array(e.silhouette.lodInfo.lengths)}},averageEdgeLength:e.averageEdgeLength}}}class a{constructor(t){this.dataBuffer=t.data.buffer,this.writerSettings=t.writerSettings,this.skipDeduplicate=t.skipDeduplicate,this.indices=Array.isArray(t.indices)?t.indices:t.indices.buffer,this.indicesType=Array.isArray(t.indices)?\"Array\":e(t.indices)?\"Uint32Array\":\"Uint16Array\",this.indicesLength=t.indicesLength}}export{n as EdgeWorkerHandle,a as PackedInput};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import{HandleOwner as t}from\"../../../../../core/HandleOwner.js\";import{property as r}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as o}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{c as s}from\"../../../../../chunks/vec3f64.js\";import{WorkerHandle as n}from\"../../../../../core/workers/WorkerHandle.js\";import{f as i}from\"../../../../../chunks/sphere.js\";import{asSnappingPoint as a}from\"../../SnappingPoint.js\";import{EdgeSnappingCandidate as d}from\"../../candidates/EdgeSnappingCandidate.js\";import{VertexSnappingCandidate as c}from\"../../candidates/VertexSnappingCandidate.js\";let p=class extends t{constructor(e){super(e),this.availability=0,this._ids=new Set}destroy(){this._workerHandle.destroy(),this._workerHandle=null}initialize(){this._workerHandle=new l(this.schedule,{fetchAllEdgeLocations:(e,t)=>this._fetchAllEdgeLocations(e,t)})}async fetchCandidates(e,t){const r=e.coordinateHelper,{point:o}=e,s=h;this.renderCoordsHelper.toRenderCoords(o,r.spatialReference,s);const n=e.distance,a=\"number\"==typeof n?n:n.distance,d=await this._workerHandle.invoke({bounds:i(s[0],s[1],s[2],a),types:e.types},t);return d.candidates.sort(((e,t)=>e.distance-t.distance)),d.candidates.map((e=>this._convertCandidate(r,e)))}async add(e,t){this._ids.add(e.id),await this._workerHandle.invokeMethod(\"add\",e,t)}async remove(e,t){this._ids.delete(e.id),await this._workerHandle.invokeMethod(\"remove\",e,t)}_convertCandidate(e,t){switch(t.type){case\"edge\":return new d({objectId:t.objectId,targetPoint:this._convertRenderCoordinate(e,t.target),edgeStart:this._convertRenderCoordinate(e,t.start),edgeEnd:this._convertRenderCoordinate(e,t.end),isDraped:!1});case\"vertex\":return new c({objectId:t.objectId,targetPoint:this._convertRenderCoordinate(e,t.target),isDraped:!1})}}_convertRenderCoordinate({spatialReference:e},t){const r=s();return this.renderCoordsHelper.fromRenderCoords(t,r,e),a(r)}async _fetchAllEdgeLocations(e,t){const r=[],o=[];for(const{id:s,uid:n}of e.components)this._ids.has(s)&&r.push((async()=>{const e=await this.fetchEdgeLocations(s,t.signal),r=e.locations.buffer;return o.push(r),{id:s,uid:n,objectIds:e.objectIds,locations:r,origin:e.origin,type:e.type}})());return{result:{components:(await Promise.all(r)).filter((({id:e})=>this._ids.has(e)))},transferList:o}}};e([r({constructOnly:!0})],p.prototype,\"renderCoordsHelper\",void 0),e([r({constructOnly:!0})],p.prototype,\"fetchEdgeLocations\",void 0),e([r({constructOnly:!0})],p.prototype,\"schedule\",void 0),e([r({readOnly:!0})],p.prototype,\"availability\",void 0),p=e([o(\"esri.views.interactive.snapping.featureSources.sceneLayerSource.SceneLayerSnappingSourceWorker\")],p);class l extends n{constructor(e,t){super(\"SceneLayerSnappingSourceWorker\",\"fetchCandidates\",{},e,{strategy:\"dedicated\",client:t})}}const h=s();export{p as SceneLayerSnappingSourceWorkerHandle};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import{HandleOwner as r}from\"../../../../core/HandleOwner.js\";import{destroyHandle as t}from\"../../../../core/handleUtils.js\";import{removeMaybe as o,abortMaybe as i,isNone as s,applySome as a}from\"../../../../core/maybe.js\";import{isAborted as n}from\"../../../../core/promiseUtils.js\";import{property as d}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as c}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{EdgeWorkerHandle as l}from\"../../../3d/webgl-engine/lib/edgeRendering/EdgeWorkerHandle.js\";import{SceneLayerSnappingSourceWorkerHandle as p}from\"./sceneLayerSource/SceneLayerSnappingSourceWorkerHandle.js\";let h=class extends r{get updating(){return this.updatingHandles.updating}constructor(e){super(e),this.availability=1,this._abortController=new AbortController}destroy(){this._tracker=o(this._tracker),this._abortController=i(this._abortController)}initialize(){const{view:e}=this,r=e.resourceController;this._edgeWorker=new l((e=>r.immediate.schedule(e))),this._workerHandle=new p({renderCoordsHelper:this.view.renderCoordsHelper,schedule:e=>r.immediate.schedule(e),fetchEdgeLocations:async(e,r)=>{if(s(this._tracker))throw new Error(\"tracker-not-initialized\");return this._tracker.fetchEdgeLocations(e,this._edgeWorker,r)}}),this.updatingHandles.addPromise(this._setupLayerView()),this.handles.add([t(this._workerHandle),t(this._edgeWorker)])}async fetchCandidates(e,r){return this._workerHandle.fetchCandidates(e,r)}refresh(){}async _setupLayerView(){if(this.destroyed)return;const e=a(this._abortController,(e=>e.signal)),r=await this.getLayerView();s(r)||n(e)||(this._tracker=r.trackSnappingSources({add:(r,t)=>{this.updatingHandles.addPromise(this._workerHandle.add({id:r,bounds:t},e))},remove:r=>{this.updatingHandles.addPromise(this._workerHandle.remove({id:r},e))}}))}};e([d({constructOnly:!0})],h.prototype,\"getLayerView\",void 0),e([d({constructOnly:!0})],h.prototype,\"view\",void 0),e([d({readOnly:!0})],h.prototype,\"updating\",null),e([d({readOnly:!0})],h.prototype,\"availability\",void 0),h=e([c(\"esri.views.interactive.snapping.featureSources.I3SSnappingSource\")],h);export{h as I3SSnappingSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import r from\"../../../../core/Accessor.js\";import{isSome as t}from\"../../../../core/maybe.js\";import{throwIfAborted as s}from\"../../../../core/promiseUtils.js\";import{property as i}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as o}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{I3SSnappingSource as a}from\"./I3SSnappingSource.js\";let c=class extends r{get updating(){return this._i3sSources.some((e=>e.updating))}constructor(e){super(e),this.availability=1,this._i3sSources=[]}destroy(){this._i3sSources.forEach((e=>e.destroy())),this._i3sSources.length=0}initialize(){const{view:e}=this,r=this.layerSource.layer;this._i3sSources=\"building-scene\"===r.type?this._getBuildingSceneI3SSources(e,r):[this._getSceneLayerI3SSource(e,r)]}async fetchCandidates(e,r){const t=await Promise.all(this._i3sSources.map((t=>t.fetchCandidates(e,r))));return s(r),t.flat()}refresh(){this._i3sSources.forEach((e=>e.refresh()))}_getBuildingSceneI3SSources(e,r){return r.allSublayers.toArray().map((t=>\"building-component\"===t.type?new a({getLayerView:async()=>(await e.whenLayerView(r)).whenSublayerView(t),view:e}):null)).filter(t)}_getSceneLayerI3SSource(e,r){return new a({getLayerView:async()=>{const t=await e.whenLayerView(r);return\"scene-layer-graphics-3d\"===t.type?void 0:t},view:e})}};e([i({constructOnly:!0})],c.prototype,\"layerSource\",void 0),e([i({constructOnly:!0})],c.prototype,\"view\",void 0),e([i({readOnly:!0})],c.prototype,\"updating\",null),e([i({readOnly:!0})],c.prototype,\"availability\",void 0),c=e([o(\"esri.views.interactive.snapping.featureSources.SceneLayerSnappingSource\")],c);export{c as SceneLayerSnappingSource};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIiR,IAAMA,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,wBAAuB,WAAU,EAAC,SAAQ,CAAAA,OAAG,CAACA,GAAE,UAAU,GAAE,gCAA+B,CAAAA,OAAG,CAACA,GAAE,UAAU,GAAE,sBAAqB,CAAAA,OAAG,CAACA,GAAE,UAAU,EAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQA,IAAEC,IAAEC,IAAE;AAAC,QAAGA,GAAE,QAAOC,GAAEH,EAAC;AAAE,UAAMD,KAAE,MAAM,KAAK,OAAO,IAAIK,GAAEJ,EAAC,GAAEC,EAAC;AAAE,WAAO,KAAK,cAAcF,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBC,IAAEC,IAAE;AAAC,UAAMI,KAAE,MAAM,KAAK,aAAa,wBAAuB,IAAID,GAAEJ,EAAC,GAAEC,EAAC;AAAE,WAAO,EAAEI,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,+BAA+BL,IAAEC,IAAE;AAAC,UAAMI,KAAE,MAAM,KAAK,aAAa,kCAAiC,IAAID,GAAEJ,EAAC,GAAEC,EAAC;AAAE,WAAO,EAAEI,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcL,IAAE;AAAC,WAAM,EAAC,SAAQ,EAAC,eAAc,EAAEA,GAAE,QAAQ,aAAa,GAAE,SAAQ,EAAC,SAAQ,IAAI,aAAaA,GAAE,QAAQ,QAAQ,OAAO,EAAC,EAAC,GAAE,YAAW,EAAC,eAAc,EAAEA,GAAE,WAAW,aAAa,GAAE,SAAQ,EAAC,SAAQ,IAAI,aAAaA,GAAE,WAAW,QAAQ,OAAO,EAAC,EAAC,GAAE,mBAAkBA,GAAE,kBAAiB;AAAA,EAAC;AAAC;AAAC,IAAMI,KAAN,MAAO;AAAA,EAAC,YAAYH,IAAE;AAAC,SAAK,aAAWA,GAAE,KAAK,QAAO,KAAK,iBAAeA,GAAE,gBAAe,KAAK,kBAAgBA,GAAE,iBAAgB,KAAK,UAAQ,MAAM,QAAQA,GAAE,OAAO,IAAEA,GAAE,UAAQA,GAAE,QAAQ,QAAO,KAAK,cAAY,MAAM,QAAQA,GAAE,OAAO,IAAE,UAAQ,EAAEA,GAAE,OAAO,IAAE,gBAAc,eAAc,KAAK,gBAAcA,GAAE;AAAA,EAAa;AAAC;;;ACAznB,IAAIK,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,GAAE,KAAK,OAAK,oBAAI;AAAA,EAAG;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc,QAAQ,GAAE,KAAK,gBAAc;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,gBAAc,IAAIC,GAAE,KAAK,UAAS,EAAC,uBAAsB,CAACD,IAAEE,OAAI,KAAK,uBAAuBF,IAAEE,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBF,IAAEE,IAAE;AAAC,UAAMC,KAAEH,GAAE,kBAAiB,EAAC,OAAMI,GAAC,IAAEJ,IAAEK,KAAEC;AAAE,SAAK,mBAAmB,eAAeF,IAAED,GAAE,kBAAiBE,EAAC;AAAE,UAAME,KAAEP,GAAE,UAASQ,KAAE,YAAU,OAAOD,KAAEA,KAAEA,GAAE,UAASE,KAAE,MAAM,KAAK,cAAc,OAAO,EAAC,QAAO,EAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEG,EAAC,GAAE,OAAMR,GAAE,MAAK,GAAEE,EAAC;AAAE,WAAOO,GAAE,WAAW,KAAM,CAACT,IAAEE,OAAIF,GAAE,WAASE,GAAE,QAAS,GAAEO,GAAE,WAAW,IAAK,CAAAT,OAAG,KAAK,kBAAkBG,IAAEH,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,IAAIA,IAAEE,IAAE;AAAC,SAAK,KAAK,IAAIF,GAAE,EAAE,GAAE,MAAM,KAAK,cAAc,aAAa,OAAMA,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOF,IAAEE,IAAE;AAAC,SAAK,KAAK,OAAOF,GAAE,EAAE,GAAE,MAAM,KAAK,cAAc,aAAa,UAASA,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAEE,IAAE;AAAC,YAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAO,eAAO,IAAIC,GAAE,EAAC,UAASD,GAAE,UAAS,aAAY,KAAK,yBAAyBF,IAAEE,GAAE,MAAM,GAAE,WAAU,KAAK,yBAAyBF,IAAEE,GAAE,KAAK,GAAE,SAAQ,KAAK,yBAAyBF,IAAEE,GAAE,GAAG,GAAE,UAAS,MAAE,CAAC;AAAA,MAAE,KAAI;AAAS,eAAO,IAAIC,GAAE,EAAC,UAASD,GAAE,UAAS,aAAY,KAAK,yBAAyBF,IAAEE,GAAE,MAAM,GAAE,UAAS,MAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,yBAAyB,EAAC,kBAAiBF,GAAC,GAAEE,IAAE;AAAC,UAAMC,KAAE,EAAE;AAAE,WAAO,KAAK,mBAAmB,iBAAiBD,IAAEC,IAAEH,EAAC,GAAE,EAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,uBAAuBH,IAAEE,IAAE;AAAC,UAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,eAAS,EAAC,IAAGC,IAAE,KAAIE,GAAC,KAAIP,GAAE,WAAW,MAAK,KAAK,IAAIK,EAAC,KAAGF,GAAE,MAAM,YAAS;AAAC,YAAMH,KAAE,MAAM,KAAK,mBAAmBK,IAAEH,GAAE,MAAM,GAAEC,KAAEH,GAAE,UAAU;AAAO,aAAOI,GAAE,KAAKD,EAAC,GAAE,EAAC,IAAGE,IAAE,KAAIE,IAAE,WAAUP,GAAE,WAAU,WAAUG,IAAE,QAAOH,GAAE,QAAO,MAAKA,GAAE,KAAI;AAAA,IAAC,GAAG,CAAC;AAAE,WAAM,EAAC,QAAO,EAAC,aAAY,MAAM,QAAQ,IAAIG,EAAC,GAAG,OAAQ,CAAC,EAAC,IAAGH,GAAC,MAAI,KAAK,KAAK,IAAIA,EAAC,CAAE,EAAC,GAAE,cAAaI,GAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEL,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,gGAAgG,CAAC,GAAEA,EAAC;AAAE,IAAME,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYD,IAAEE,IAAE;AAAC,UAAM,kCAAiC,mBAAkB,CAAC,GAAEF,IAAE,EAAC,UAAS,aAAY,QAAOE,GAAC,CAAC;AAAA,EAAC;AAAC;AAAC,IAAMI,KAAE,EAAE;;;ACAroE,IAAII,KAAE,cAAc,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,gBAAgB;AAAA,EAAQ;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,GAAE,KAAK,mBAAiB,IAAI;AAAA,EAAe;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,mBAAiB,EAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,UAAK,EAAC,MAAKA,GAAC,IAAE,MAAKC,KAAED,GAAE;AAAmB,SAAK,cAAY,IAAIE,GAAG,CAAAF,OAAGC,GAAE,UAAU,SAASD,EAAC,CAAE,GAAE,KAAK,gBAAc,IAAIG,GAAE,EAAC,oBAAmB,KAAK,KAAK,oBAAmB,UAAS,CAAAH,OAAGC,GAAE,UAAU,SAASD,EAAC,GAAE,oBAAmB,OAAMA,IAAEC,OAAI;AAAC,UAAG,EAAE,KAAK,QAAQ,EAAE,OAAM,IAAI,MAAM,yBAAyB;AAAE,aAAO,KAAK,SAAS,mBAAmBD,IAAE,KAAK,aAAYC,EAAC;AAAA,IAAC,EAAC,CAAC,GAAE,KAAK,gBAAgB,WAAW,KAAK,gBAAgB,CAAC,GAAE,KAAK,QAAQ,IAAI,CAACG,GAAE,KAAK,aAAa,GAAEA,GAAE,KAAK,WAAW,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBJ,IAAEC,IAAE;AAAC,WAAO,KAAK,cAAc,gBAAgBD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAiB;AAAC,QAAG,KAAK,UAAU;AAAO,UAAMD,KAAE,EAAE,KAAK,kBAAkB,CAAAA,OAAGA,GAAE,MAAO,GAAEC,KAAE,MAAM,KAAK,aAAa;AAAE,MAAEA,EAAC,KAAGE,GAAEH,EAAC,MAAI,KAAK,WAASC,GAAE,qBAAqB,EAAC,KAAI,CAACA,IAAEG,OAAI;AAAC,WAAK,gBAAgB,WAAW,KAAK,cAAc,IAAI,EAAC,IAAGH,IAAE,QAAOG,GAAC,GAAEJ,EAAC,CAAC;AAAA,IAAC,GAAE,QAAO,CAAAC,OAAG;AAAC,WAAK,gBAAgB,WAAW,KAAK,cAAc,OAAO,EAAC,IAAGA,GAAC,GAAED,EAAC,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAED,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,kEAAkE,CAAC,GAAEA,EAAC;;;ACAztD,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAY,KAAM,CAAAM,OAAGA,GAAE,QAAS;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,GAAE,KAAK,cAAY,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,YAAY,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,YAAY,SAAO;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,UAAK,EAAC,MAAKA,GAAC,IAAE,MAAKC,KAAE,KAAK,YAAY;AAAM,SAAK,cAAY,qBAAmBA,GAAE,OAAK,KAAK,4BAA4BD,IAAEC,EAAC,IAAE,CAAC,KAAK,wBAAwBD,IAAEC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBD,IAAEC,IAAE;AAAC,UAAMC,KAAE,MAAM,QAAQ,IAAI,KAAK,YAAY,IAAK,CAAAA,OAAGA,GAAE,gBAAgBF,IAAEC,EAAC,CAAE,CAAC;AAAE,WAAO,EAAEA,EAAC,GAAEC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,YAAY,QAAS,CAAAF,OAAGA,GAAE,QAAQ,CAAE;AAAA,EAAC;AAAA,EAAC,4BAA4BA,IAAEC,IAAE;AAAC,WAAOA,GAAE,aAAa,QAAQ,EAAE,IAAK,CAAAC,OAAG,yBAAuBA,GAAE,OAAK,IAAIC,GAAE,EAAC,cAAa,aAAU,MAAMH,GAAE,cAAcC,EAAC,GAAG,iBAAiBC,EAAC,GAAE,MAAKF,GAAC,CAAC,IAAE,IAAK,EAAE,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAEC,IAAE;AAAC,WAAO,IAAIE,GAAE,EAAC,cAAa,YAAS;AAAC,YAAMD,KAAE,MAAMF,GAAE,cAAcC,EAAC;AAAE,aAAM,8BAA4BC,GAAE,OAAK,SAAOA;AAAA,IAAC,GAAE,MAAKF,GAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,yEAAyE,CAAC,GAAE,CAAC;", "names": ["n", "e", "t", "r", "f", "a", "s", "p", "e", "l", "t", "r", "o", "s", "h", "n", "a", "d", "h", "e", "r", "n", "p", "t", "e", "r", "t", "h"]}