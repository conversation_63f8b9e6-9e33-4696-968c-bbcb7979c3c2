{"version": 3, "sources": ["../../@arcgis/core/support/featureFlags.js", "../../@arcgis/core/symbols/support/styleUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../core/has.js\";const e=()=>!!has(\"enable-feature:force-wosr\"),a=()=>!!has(\"enable-feature:direct-3d-object-feature-layer-display\"),r=()=>has.cache[\"enable-feature:direct-3d-object-feature-layer-display\"]=!0,t=()=>!!has(\"enable-feature:SceneLayer-editing\"),c=()=>{has.cache[\"enable-feature:SceneLayer-editing\"]=!0,r()};export{a as enableDirect3DObjectFeatureLayerDisplay,c as enableSceneLayerEditing,e as enableWebStyleForceWOSR,t as sceneLayerEditingEnabled,r as turnOnDirect3DObjectFeatureLayerDisplay};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../request.js\";import r from\"../../core/Error.js\";import{isSome as t}from\"../../core/maybe.js\";import{throwIfAbortError as o}from\"../../core/promiseUtils.js\";import{normalize as s,removeFile as n}from\"../../core/urlUtils.js\";import l from\"../../portal/Portal.js\";import a from\"../../portal/PortalQueryParams.js\";import{enableWebStyleForceWOSR as u}from\"../../support/featureFlags.js\";let f={};async function m(e,r){try{return{data:(await j(e,r)).data,baseUrl:n(e),styleUrl:e}}catch(t){return o(t),null}}function i(e,r,o){const s=t(r.portal)?r.portal:l.getDefault();let n;const a=`${s.url} - ${s.user&&s.user.username} - ${e}`;return f[a]||(f[a]=c(e,s,o).then((e=>(n=e,e.fetchData()))).then((r=>({data:r,baseUrl:n.itemUrl??\"\",styleName:e})))),f[a]}function y(){f&&(f={})}function c(e,t,o){return t.load(o).then((()=>{const r=new a({disableExtraQuery:!0,query:`owner:${h} AND type:${w} AND typekeywords:\"${e}\"`});return t.queryItems(r,o)})).then((({results:t})=>{let s=null;const n=e.toLowerCase();if(t&&Array.isArray(t))for(const e of t){const r=e.typeKeywords?.some((e=>e.toLowerCase()===n));if(r&&e.type===w&&e.owner===h){s=e;break}}if(!s)throw new r(\"symbolstyleutils:style-not-found\",`The style '${e}' could not be found`,{styleName:e});return s.load(o)}))}function p(e,o,s){return e&&t(e.styleUrl)?m(e.styleUrl,s):e&&t(e.styleName)?i(e.styleName,o,s):Promise.reject(new r(\"symbolstyleutils:style-url-and-name-missing\",\"Either styleUrl or styleName is required to resolve a style\"))}function d(e){return null===e||\"CIMSymbolReference\"===e.type?e:{type:\"CIMSymbolReference\",symbol:e}}function b(e,r){if(\"cimRef\"===r)return e.cimRef;if(e.formatInfos&&!u())for(const t of e.formatInfos)if(\"gltf\"===t.type)return t.href;return e.webRef}function j(r,t){const o={responseType:\"json\",query:{f:\"json\"},...t};return e(s(r),o)}const h=\"esri_en\",w=\"Style\",U=\"https://cdn.arcgis.com/sharing/rest/content/items/220936cc6ed342c9937abd8f180e7d1e/resources/styles/cim/{SymbolName}.json?f=json\";export{U as Style2DUrlTemplate,f as cachedStyles,y as cleanupStyleUtilsCache,p as fetchStyle,d as makeCIMSymbolRef,j as requestJSON,b as symbolUrlFromStyleItem};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgC,IAAM,IAAE,MAAI,CAAC,CAAC,IAAI,2BAA2B;AAA7C,IAAgM,IAAE,MAAI,CAAC,CAAC,IAAI,mCAAmC;;;ACAkI,IAAI,IAAE,CAAC;AAAE,eAAe,EAAEA,IAAEC,IAAE;AAAC,MAAG;AAAC,WAAM,EAAC,OAAM,MAAM,EAAED,IAAEC,EAAC,GAAG,MAAK,SAAQ,GAAED,EAAC,GAAE,UAASA,GAAC;AAAA,EAAC,SAAOE,IAAE;AAAC,WAAO,EAAEA,EAAC,GAAE;AAAA,EAAI;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAE,GAAE;AAAC,QAAME,KAAE,EAAEF,GAAE,MAAM,IAAEA,GAAE,SAAO,EAAE,WAAW;AAAE,MAAI;AAAE,QAAM,IAAE,GAAGE,GAAE,GAAG,MAAMA,GAAE,QAAMA,GAAE,KAAK,QAAQ,MAAMH,EAAC;AAAG,SAAO,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,EAAEA,IAAEG,IAAE,CAAC,EAAE,KAAM,CAAAH,QAAI,IAAEA,IAAEA,GAAE,UAAU,EAAG,EAAE,KAAM,CAAAC,QAAI,EAAC,MAAKA,IAAE,SAAQ,EAAE,WAAS,IAAG,WAAUD,GAAC,EAAG,IAAG,EAAE,CAAC;AAAC;AAAwB,SAAS,EAAEI,IAAEC,IAAE,GAAE;AAAC,SAAOA,GAAE,KAAK,CAAC,EAAE,KAAM,MAAI;AAAC,UAAMC,KAAE,IAAI,EAAE,EAAC,mBAAkB,MAAG,OAAM,SAAS,CAAC,aAAaC,EAAC,sBAAsBH,EAAC,IAAG,CAAC;AAAE,WAAOC,GAAE,WAAWC,IAAE,CAAC;AAAA,EAAC,CAAE,EAAE,KAAM,CAAC,EAAC,SAAQD,GAAC,MAAI;AAJj9B;AAIk9B,QAAIG,KAAE;AAAK,UAAM,IAAEJ,GAAE,YAAY;AAAE,QAAGC,MAAG,MAAM,QAAQA,EAAC,EAAE,YAAUD,MAAKC,IAAE;AAAC,YAAMC,MAAE,KAAAF,GAAE,iBAAF,mBAAgB,KAAM,CAAAA,OAAGA,GAAE,YAAY,MAAI;AAAI,UAAGE,MAAGF,GAAE,SAAOG,MAAGH,GAAE,UAAQ,GAAE;AAAC,QAAAI,KAAEJ;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,QAAG,CAACI,GAAE,OAAM,IAAI,EAAE,oCAAmC,cAAcJ,EAAC,wBAAuB,EAAC,WAAUA,GAAC,CAAC;AAAE,WAAOI,GAAE,KAAK,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEJ,IAAE,GAAEI,IAAE;AAAC,SAAOJ,MAAG,EAAEA,GAAE,QAAQ,IAAE,EAAEA,GAAE,UAASI,EAAC,IAAEJ,MAAG,EAAEA,GAAE,SAAS,IAAE,EAAEA,GAAE,WAAU,GAAEI,EAAC,IAAE,QAAQ,OAAO,IAAI,EAAE,+CAA8C,6DAA6D,CAAC;AAAC;AAAC,SAASC,GAAEL,IAAE;AAAC,SAAO,SAAOA,MAAG,yBAAuBA,GAAE,OAAKA,KAAE,EAAC,MAAK,sBAAqB,QAAOA,GAAC;AAAC;AAAC,SAASM,GAAEN,IAAEE,IAAE;AAAC,MAAG,aAAWA,GAAE,QAAOF,GAAE;AAAO,MAAGA,GAAE,eAAa,CAAC,EAAE;AAAE,eAAUC,MAAKD,GAAE,YAAY,KAAG,WAASC,GAAE,KAAK,QAAOA,GAAE;AAAA;AAAK,SAAOD,GAAE;AAAM;AAAC,SAAS,EAAEE,IAAED,IAAE;AAAC,QAAM,IAAE,EAAC,cAAa,QAAO,OAAM,EAAC,GAAE,OAAM,GAAE,GAAGA,GAAC;AAAE,SAAO,EAAE,EAAEC,EAAC,GAAE,CAAC;AAAC;AAAC,IAAM,IAAE;AAAR,IAAkBC,KAAE;AAApB,IAA4BI,KAAE;", "names": ["e", "r", "t", "s", "e", "t", "r", "w", "s", "d", "b", "U"]}