import {
  l as l2
} from "./chunk-ZV2P3YGB.js";
import {
  a as a4
} from "./chunk-NMTMUDUY.js";
import {
  R,
  S,
  T
} from "./chunk-KYDW2SHL.js";
import {
  I,
  o
} from "./chunk-ETVSAOWN.js";
import "./chunk-3HDXUZDA.js";
import {
  i as i2
} from "./chunk-WKXLAUK5.js";
import "./chunk-WCRONQ5Z.js";
import {
  ae
} from "./chunk-27CJODAI.js";
import "./chunk-L7J6WAZK.js";
import "./chunk-EJ4BPAYT.js";
import "./chunk-JTTSDQPH.js";
import "./chunk-6DXPU43Z.js";
import "./chunk-3MBH7CQT.js";
import "./chunk-6FMMG4VO.js";
import {
  n
} from "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-PV7C75FF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-3BVSG4LE.js";
import "./chunk-JJZTA23S.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-VXAO6YJP.js";
import "./chunk-BI4P4NAQ.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-YROREPK5.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-EVADT7ME.js";
import "./chunk-T3GGN2P7.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import {
  $,
  C
} from "./chunk-HK26M5VD.js";
import {
  b,
  d,
  g
} from "./chunk-HJ36YGUG.js";
import "./chunk-LGZKVOWE.js";
import "./chunk-SKYLCTPX.js";
import "./chunk-VK7XO5DN.js";
import "./chunk-WKBMFG6J.js";
import "./chunk-BPRRRPC3.js";
import {
  x
} from "./chunk-6G2NLXT7.js";
import "./chunk-RFTQI4ZD.js";
import "./chunk-UHA44FM7.js";
import "./chunk-6ZZUUGXX.js";
import "./chunk-TFWV44LH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-6OFWBRK2.js";
import {
  f as f4
} from "./chunk-NEPFZ7PM.js";
import {
  u
} from "./chunk-HWB4LNSZ.js";
import "./chunk-JSZR3BUH.js";
import "./chunk-QKWIBVLD.js";
import "./chunk-JCXMTMKU.js";
import "./chunk-WAPZ634R.js";
import "./chunk-OHAM27JH.js";
import {
  E
} from "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import {
  D,
  G,
  L,
  P,
  V,
  Y,
  f as f3
} from "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-22FAZXOH.js";
import "./chunk-DFGMRI52.js";
import "./chunk-OZZFNS32.js";
import "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-RURSJOSG.js";
import "./chunk-77E52HT5.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import {
  i
} from "./chunk-56K7OMWB.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import {
  f as f2
} from "./chunk-XBS7QZIQ.js";
import "./chunk-G3QAWKCD.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-WL6G2MRC.js";
import "./chunk-UCWK623G.js";
import "./chunk-3HW44BD3.js";
import {
  rn,
  tn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-KE7SPCM7.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-BS3GJQ77.js";
import "./chunk-IOBN373Z.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-NEJXVYTI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-ZL6CFFJK.js";
import "./chunk-TWFTBWXP.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  a as a3
} from "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w3
} from "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import {
  w as w2
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import {
  A,
  v
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  a,
  r,
  t,
  w
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/support/imageReprojection.js
var b2 = class _b {
  constructor(t2) {
    if (this._ownsRctx = false, t2) this._ownsRctx = false, this._rctx = t2;
    else {
      if (_b._instance) return _b._instanceRefCount++, _b._instance;
      _b._instanceRefCount = 1, _b._instance = this, this._ownsRctx = true;
      const t3 = document.createElement("canvas").getContext("webgl");
      t3.getExtension("OES_texture_float"), this._rctx = new I(t3, {});
    }
    const e2 = { applyProjection: true, bilinear: false, bicubic: false }, r2 = o("raster/reproject", "raster/reproject", /* @__PURE__ */ new Map([["a_position", 0]]), e2);
    this._program = this._rctx.programCache.acquire(r2.shaders.vertexShader, r2.shaders.fragmentShader, r2.attributes), this._rctx.useProgram(this._program), this._program.setUniform1f("u_opacity", 1), this._program.setUniform1i("u_image", 0), this._program.setUniform1i("u_flipY", 0), this._program.setUniform1i("u_transformGrid", 1), this._quad = new n(this._rctx, [0, 0, 1, 0, 0, 1, 1, 1]);
  }
  reprojectTexture(t2, s, n2 = false) {
    const o2 = rn(t2.extent, s), m = new w2({ x: (t2.extent.xmax - t2.extent.xmin) / t2.texture.descriptor.width, y: (t2.extent.ymax - t2.extent.ymin) / t2.texture.descriptor.height, spatialReference: t2.extent.spatialReference }), { x: l3, y: b3 } = C(m, s, t2.extent);
    let T2 = (l3 + b3) / 2;
    const R2 = Math.round((o2.xmax - o2.xmin) / T2), j2 = Math.round((o2.ymax - o2.ymin) / T2);
    T2 = (o2.width / R2 + o2.height / j2) / 2;
    const E2 = new w2({ x: T2, y: T2, spatialReference: o2.spatialReference }), D2 = $({ projectedExtent: o2, srcBufferExtent: t2.extent, pixelSize: E2, hasWrapAround: true, spacing: [16, 16] }), C2 = l2(this._rctx, D2), y2 = new E(this._rctx, { width: R2, height: j2, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D.CLAMP_TO_EDGE, samplingMode: L.LINEAR, hasMipmap: false }), M2 = new x(this._rctx, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE, width: R2, height: j2 }, y2);
    this._rctx.bindFramebuffer(M2), this._rctx.setViewport(0, 0, R2, j2), this._rctx.useProgram(this._program), this._rctx.bindTexture(t2.texture, 0), this._rctx.bindTexture(C2, 1), this._quad.bind();
    const { width: A2 = 0, height: S2 = 0 } = t2.texture.descriptor;
    if (this._program.setUniform2f("u_srcImageSize", A2, S2), this._program.setUniform2fv("u_transformSpacing", D2.spacing), this._program.setUniform2fv("u_transformGridSize", D2.size), this._program.setUniform2f("u_targetImageSize", R2, j2), this._quad.draw(), this._quad.unbind(), this._rctx.useProgram(null), this._rctx.bindFramebuffer(null), C2.dispose(), n2) {
      const { width: t3 = 0, height: e2 = 0 } = M2.descriptor, r2 = new ImageData(t3, e2);
      return M2.readPixels(0, 0, t3, e2, P.RGBA, G.UNSIGNED_BYTE, r2.data), M2.detachColorTexture(f3.COLOR_ATTACHMENT0), M2.dispose(), { texture: y2, extent: o2, imageData: r2 };
    }
    return M2.detachColorTexture(f3.COLOR_ATTACHMENT0), M2.dispose(), { texture: y2, extent: o2 };
  }
  reprojectBitmapData(t2, e2) {
    const r2 = T(t2.bitmapData) ? S(t2.bitmapData) : t2.bitmapData, i3 = new E(this._rctx, { width: t2.bitmapData.width, height: t2.bitmapData.height, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D.CLAMP_TO_EDGE, samplingMode: L.LINEAR, hasMipmap: false }, r2), a5 = this.reprojectTexture({ texture: i3, extent: t2.extent }, e2, true);
    a5.texture.dispose();
    const o2 = document.createElement("canvas"), m = a5.imageData;
    o2.width = m.width, o2.height = m.height;
    return o2.getContext("2d").putImageData(m, 0, 0), { bitmapData: o2, extent: a5.extent };
  }
  async loadAndReprojectBitmapData(e2, r2, i3) {
    const a5 = (await U(e2, { responseType: "image" })).data, s = document.createElement("canvas");
    s.width = a5.width, s.height = a5.height;
    const n2 = s.getContext("2d");
    n2.drawImage(a5, 0, 0);
    const o2 = n2.getImageData(0, 0, s.width, s.height);
    if (r2.spatialReference.equals(i3)) return { bitmapData: o2, extent: r2 };
    const m = this.reprojectBitmapData({ bitmapData: o2, extent: r2 }, i3);
    return { bitmapData: m.bitmapData, extent: m.extent };
  }
  destroy() {
    this._ownsRctx ? (_b._instanceRefCount--, 0 === _b._instanceRefCount && (this._quad.dispose(), this._program.dispose(), this._rctx.dispose(), _b._instance = null)) : (this._quad.dispose(), this._program.dispose());
  }
};
b2._instanceRefCount = 0;

// node_modules/@arcgis/core/views/2d/layers/KMLLayerView2D.js
var k = class {
  constructor() {
    this.allSublayers = /* @__PURE__ */ new Map(), this.allPoints = [], this.allPolylines = [], this.allPolygons = [], this.allMapImages = [];
  }
};
var P2 = class extends f4(u) {
  constructor() {
    super(...arguments), this._bitmapIndex = /* @__PURE__ */ new Map(), this._mapImageContainer = new a4(), this._kmlVisualData = new k(), this._fetchController = null, this.allVisiblePoints = new i(), this.allVisiblePolylines = new i(), this.allVisiblePolygons = new i(), this.allVisibleMapImages = new j();
  }
  async hitTest(e2, i3) {
    var _a, _b, _c;
    const t2 = this.layer;
    return [(_a = this._pointsView) == null ? void 0 : _a.hitTest(e2), (_b = this._polylinesView) == null ? void 0 : _b.hitTest(e2), (_c = this._polygonsView) == null ? void 0 : _c.hitTest(e2)].flat().filter(Boolean).map((i4) => (i4.layer = t2, i4.sourceLayer = t2, { type: "graphic", graphic: i4, layer: t2, mapPoint: e2 }));
  }
  update(e2) {
    this._polygonsView && this._polygonsView.processUpdate(e2), this._polylinesView && this._polylinesView.processUpdate(e2), this._pointsView && this._pointsView.processUpdate(e2);
  }
  attach() {
    this._fetchController = new AbortController(), this.container.addChild(this._mapImageContainer), this._polygonsView = new ae({ view: this.view, graphics: this.allVisiblePolygons, requestUpdateCallback: () => this.requestUpdate(), container: new i2(this.view.featuresTilingScheme) }), this.container.addChild(this._polygonsView.container), this._polylinesView = new ae({ view: this.view, graphics: this.allVisiblePolylines, requestUpdateCallback: () => this.requestUpdate(), container: new i2(this.view.featuresTilingScheme) }), this.container.addChild(this._polylinesView.container), this._pointsView = new ae({ view: this.view, graphics: this.allVisiblePoints, requestUpdateCallback: () => this.requestUpdate(), container: new i2(this.view.featuresTilingScheme) }), this.container.addChild(this._pointsView.container), this.addAttachHandles([this.allVisibleMapImages.on("change", (e2) => {
      e2.added.forEach((e3) => this._addMapImage(e3)), e2.removed.forEach((e3) => this._removeMapImage(e3));
    }), l(() => this.layer.visibleSublayers, (e2) => {
      for (const [i3, t2] of this._kmlVisualData.allSublayers) t2.visibility = 0;
      for (const i3 of e2) {
        const e3 = this._kmlVisualData.allSublayers.get(i3.id);
        e3 && (e3.visibility = 1);
      }
      this._refreshCollections();
    })]), this.updatingHandles.addPromise(this._fetchService(this._fetchController.signal)), this._imageReprojector = new b2();
  }
  detach() {
    this._fetchController = w(this._fetchController), this._mapImageContainer.removeAllChildren(), this.container.removeAllChildren(), this._bitmapIndex.clear(), this._polygonsView = a(this._polygonsView), this._polylinesView = a(this._polylinesView), this._pointsView = a(this._pointsView), this._imageReprojector = a(this._imageReprojector);
  }
  moveStart() {
  }
  viewChange() {
    this._polygonsView.viewChange(), this._polylinesView.viewChange(), this._pointsView.viewChange();
  }
  moveEnd() {
  }
  isUpdating() {
    return this._pointsView.updating || this._polygonsView.updating || this._polylinesView.updating;
  }
  _addMapImage(e2) {
    var _a, _b;
    (((_a = this.view.spatialReference) == null ? void 0 : _a.isWGS84) || ((_b = this.view.spatialReference) == null ? void 0 : _b.isWebMercator)) && this._imageReprojector.loadAndReprojectBitmapData(e2.href, w3.fromJSON(e2.extent), this.view.spatialReference).then((i3) => {
      const t2 = new R(i3.bitmapData, { immutable: false, requestRenderOnSourceChangedEnabled: true });
      t2.x = i3.extent.xmin, t2.y = i3.extent.ymax, t2.resolution = i3.extent.width / i3.bitmapData.width, t2.rotation = e2.rotation, this._mapImageContainer.addChild(t2), this._bitmapIndex.set(e2, t2);
    });
  }
  async _getViewDependentUrl(e2, t2) {
    const { viewFormat: s, viewBoundScale: a5, httpQuery: l3 } = e2;
    if (r(s)) {
      if (t(t2)) throw new Error("Loading this network link requires a view state.");
      let p;
      if (await tn(), r(a5) && 1 !== a5) {
        const e3 = new w3(t2.extent);
        e3.expand(a5), p = e3;
      } else p = t2.extent;
      p = rn(p, f.WGS84);
      const m = rn(p, f.WebMercator), w4 = p.xmin, u2 = p.xmax, _ = p.ymin, V2 = p.ymax, f5 = t2.size[0] * t2.pixelRatio, v2 = t2.size[1] * t2.pixelRatio, S2 = Math.max(m.width, m.height), I2 = { "[bboxWest]": w4.toString(), "[bboxEast]": u2.toString(), "[bboxSouth]": _.toString(), "[bboxNorth]": V2.toString(), "[lookatLon]": p.center.x.toString(), "[lookatLat]": p.center.y.toString(), "[lookatRange]": S2.toString(), "[lookatTilt]": "0", "[lookatHeading]": t2.rotation.toString(), "[lookatTerrainLon]": p.center.x.toString(), "[lookatTerrainLat]": p.center.y.toString(), "[lookatTerrainAlt]": "0", "[cameraLon]": p.center.x.toString(), "[cameraLat]": p.center.y.toString(), "[cameraAlt]": S2.toString(), "[horizFov]": "60", "[vertFov]": "60", "[horizPixels]": f5.toString(), "[vertPixels]": v2.toString(), "[terrainEnabled]": "0", "[clientVersion]": a3, "[kmlVersion]": "2.2", "[clientName]": "ArcGIS API for JavaScript", "[language]": "en-US" }, x2 = (e3) => {
        for (const i3 in e3) for (const t3 in I2) e3[i3] = e3[i3].replace(t3, I2[t3]);
      }, C2 = v(s);
      x2(C2);
      let j2 = {};
      r(l3) && (j2 = v(l3), x2(j2));
      const k2 = f2(e2.href);
      k2.query = { ...k2.query, ...C2, ...j2 };
      return `${k2.path}?${A(C2)}`;
    }
    return e2.href;
  }
  async _fetchService(e2) {
    const i3 = new k();
    await this._loadVisualData(this.layer.url, i3, e2), this._kmlVisualData = i3, this._refreshCollections();
  }
  _refreshCollections() {
    this.allVisiblePoints.removeAll(), this.allVisiblePolylines.removeAll(), this.allVisiblePolygons.removeAll(), this.allVisibleMapImages.removeAll(), this.allVisiblePoints.addMany(this._kmlVisualData.allPoints.filter((e2) => this._isSublayerVisible(e2.sublayerId)).map(({ item: e2 }) => e2)), this.allVisiblePolylines.addMany(this._kmlVisualData.allPolylines.filter((e2) => this._isSublayerVisible(e2.sublayerId)).map(({ item: e2 }) => e2)), this.allVisiblePolygons.addMany(this._kmlVisualData.allPolygons.filter((e2) => this._isSublayerVisible(e2.sublayerId)).map(({ item: e2 }) => e2)), this.allVisibleMapImages.addMany(this._kmlVisualData.allMapImages.filter((e2) => this._isSublayerVisible(e2.sublayerId)).map(({ item: e2 }) => e2));
  }
  _isSublayerVisible(e2) {
    const i3 = this._kmlVisualData.allSublayers.get(e2);
    return !!(i3 == null ? void 0 : i3.visibility) && (-1 === i3.parentFolderId || this._isSublayerVisible(i3.parentFolderId));
  }
  _loadVisualData(e2, i3, t2) {
    return this._fetchParsedKML(e2, t2).then(async (e3) => {
      for (const s of e3.sublayers) {
        i3.allSublayers.set(s.id, s);
        const e4 = s.points ? await b(s.points) : [], a5 = s.polylines ? await b(s.polylines) : [], o2 = s.polygons ? await b(s.polygons) : [], r2 = s.mapImages || [];
        if (i3.allPoints.push(...e4.map((e5) => ({ item: e5, sublayerId: s.id }))), i3.allPolylines.push(...a5.map((e5) => ({ item: e5, sublayerId: s.id }))), i3.allPolygons.push(...o2.map((e5) => ({ item: e5, sublayerId: s.id }))), i3.allMapImages.push(...r2.map((e5) => ({ item: e5, sublayerId: s.id }))), s.networkLink) {
          const e5 = await this._getViewDependentUrl(s.networkLink, this.view.state);
          await this._loadVisualData(e5, i3, t2);
        }
      }
    });
  }
  _fetchParsedKML(e2, i3) {
    return g(e2, this.layer.spatialReference, this.layer.refreshInterval, i3).then((e3) => d(e3.data));
  }
  _removeMapImage(e2) {
    const i3 = this._bitmapIndex.get(e2);
    i3 && (this._mapImageContainer.removeChild(i3), this._bitmapIndex.delete(e2));
  }
};
e([y()], P2.prototype, "_pointsView", void 0), e([y()], P2.prototype, "_polylinesView", void 0), e([y()], P2.prototype, "_polygonsView", void 0), e([y()], P2.prototype, "updating", void 0), P2 = e([a2("esri.views.2d.layers.KMLLayerView2D")], P2);
var M = P2;
export {
  M as default
};
//# sourceMappingURL=KMLLayerView2D-HHH62UTT.js.map
