{"version": 3, "sources": ["../../@arcgis/core/views/3d/interactive/editingTools/transformGraphic/isSupportedGraphic.js", "../../@arcgis/core/views/interactive/snapping/candidates/RightAngleSnappingCandidate.js", "../../@arcgis/core/views/interactive/snapping/FeatureSnappingEngine.js", "../../@arcgis/core/views/interactive/snapping/SnappingAlgorithm.js", "../../@arcgis/core/views/interactive/snapping/candidates/LineSnappingCandidate.js", "../../@arcgis/core/views/interactive/snapping/LineSnapper.js", "../../@arcgis/core/views/interactive/snapping/candidates/ParallelLineSnappingCandidate.js", "../../@arcgis/core/views/interactive/snapping/ParallelLineSnapper.js", "../../@arcgis/core/views/interactive/snapping/RightAngleSnapper.js", "../../@arcgis/core/views/interactive/snapping/candidates/RightAngleTriangleSnappingCandidate.js", "../../@arcgis/core/views/interactive/snapping/RightAngleTriangleSnapper.js", "../../@arcgis/core/views/interactive/snapping/SelfSnappingEngine.js", "../../@arcgis/core/views/interactive/snapping/snappingFactory.js", "../../@arcgis/core/views/interactive/snapping/FeatureSnappingLayerSource.js", "../../@arcgis/core/views/interactive/snapping/SnappingOptions.js", "../../@arcgis/core/views/interactive/snapping/candidates/IntersectionSnappingCandidate.js", "../../@arcgis/core/views/interactive/snapping/SnappingManager.js", "../../@arcgis/core/widgets/Sketch/support/OperationHandle.js", "../../@arcgis/core/widgets/Sketch/SketchViewModel.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../../core/has.js\";import{isNone as e,isSome as t}from\"../../../../../core/maybe.js\";import{getGraphicEffectiveElevationMode as o,hasGraphicFeatureExpressionInfo as r}from\"../../../../../support/elevationInfoUtils.js\";import{SupportedGraphicResult as s}from\"../isSupportedGraphicUtils.js\";function i(i){if(\"graphics\"!==i.layer?.type)return s.GRAPHICS_LAYER_MISSING;if(e(i.geometry))return s.GEOMETRY_MISSING;switch(i.geometry.type){case\"point\":break;case\"polygon\":case\"polyline\":case\"multipoint\":case\"extent\":case\"mesh\":return s.SUPPORTED;default:return s.GEOMETRY_TYPE_UNSUPPORTED}const n=t(i.symbol)&&\"point-3d\"===i.symbol.type&&i.symbol.symbolLayers;if(!(n&&n.length>0&&n.some((e=>\"object\"===e.type))))return s.SYMBOL_TYPE_UNSUPPORTED;return\"on-the-ground\"!==o(i)&&r(i)?s.ELEVATION_MODE_UNSUPPORTED:s.SUPPORTED}export{i as isSupportedGraphic};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{SnappingDomain as t}from\"../SnappingDomain.js\";import{LineSegmentHintType as e}from\"../snappingUtils.js\";import{SnappingCandidate as i}from\"./SnappingCandidate.js\";import{LineSnappingHint as r}from\"../hints/LineSnappingHint.js\";import{RightAngleSnappingHint as s}from\"../hints/RightAngleSnappingHint.js\";class o extends i{constructor({targetPoint:e,constraint:i,previousVertex:r,otherVertex:s,otherVertexType:o,objectId:n,isDraped:h}){super(e,i,h,t.SELF),this.previousVertex=r,this.otherVertex=s,this.otherVertexType=o,this.objectId=n}get hints(){const t=this.previousVertex,i=this.otherVertexType===n.CENTER?this.otherVertex:this.targetPoint,o=this.otherVertexType===n.CENTER?this.targetPoint:this.otherVertex;return[new r(e.TARGET,i,o,this.isDraped,this.domain),new r(e.REFERENCE,t,i,this.isDraped,this.domain),new s(this.previousVertex,i,o,this.isDraped,this.domain)]}}var n;!function(t){t[t.NEXT=0]=\"NEXT\",t[t.CENTER=1]=\"CENTER\"}(n||(n={}));export{n as OtherVertexType,o as RightAngleSnappingCandidate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import{HandleOwner as t}from\"../../../core/HandleOwner.js\";import r from\"../../../core/Handles.js\";import{someMap as s}from\"../../../core/MapUtils.js\";import{isSome as o,isNone as n,unwrap as a}from\"../../../core/maybe.js\";import{eachAlwaysValues as i,throwIfAborted as c}from\"../../../core/promiseUtils.js\";import{sync as u,watch as p,syncAndInitial as d}from\"../../../core/reactiveUtils.js\";import{property as l}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as h}from\"../../../core/accessorSupport/decorators/subclass.js\";import{k as g}from\"../../../chunks/vec2.js\";import{d as m,c as y}from\"../../../chunks/vec3.js\";import{c as S}from\"../../../chunks/vec3f64.js\";import{g as f}from\"../../../chunks/common.js\";import{SnappingTypes as w}from\"../../../layers/graphics/data/QueryEngineResult.js\";import{absoluteHeightElevationInfo as v}from\"../../../support/elevationInfoUtils.js\";import{VerticalHalfPlaneConstraint as _}from\"./SnappingConstraint.js\";import{SnappingDomain as j}from\"./SnappingDomain.js\";import{anyMapPointToSnappingPoint as C}from\"./SnappingPoint.js\";import{sortCandidatesInPlace as F,screenDistance as x}from\"./snappingUtils.js\";import{DrapedEdgeSnappingCandidate as b}from\"./candidates/DrapedEdgeSnappingCandidate.js\";import{EdgeSnappingCandidate as R}from\"./candidates/EdgeSnappingCandidate.js\";import{FeatureSnappingCandidate as L}from\"./candidates/FeatureSnappingCandidate.js\";import{RightAngleSnappingCandidate as M,OtherVertexType as E}from\"./candidates/RightAngleSnappingCandidate.js\";import{vectorToScreenPoint as H}from\"../support/viewUtils.js\";let V=class extends t{get updating(){return s(this.snappingSources,(({snappingSource:e})=>e.updating))||this.updatingHandles.updating}get snappingSources(){const e=this._get(\"snappingSources\")||new Map,t=new Map;if(o(this.options)&&o(this.options.featureSources))for(const r of this.options.featureSources.items){const s=r.layer.uid,n=e.get(s);if(n){e.delete(s),t.set(s,n);continue}if(!r.layer.loaded){this.updatingHandles.addPromise(r.layer.load());continue}const a=this._createSourceInfo(r);o(a)&&t.set(s,a)}for(const[,r]of e)r.destroy();return t}constructor(e){super(e),this.options=null,this._domain=j.FEATURE,this._sourceModules={featureService:{module:null,loader:null},featureCollection:{module:null,loader:null},graphics:{module:null,loader:null},notes:{module:null,loader:null},scene:{module:null,loader:null}}}initialize(){this.updatingHandles.add((()=>this.snappingSources),(()=>this.notifyChange(\"updating\")),u),o(this.view)&&this.handles.add([this.view.on(\"layerview-create\",(e=>this._updateLayerView(e.layer,e.layerView))),this.view.on(\"layerview-destroy\",(e=>this._updateLayerView(e.layer,null)))])}_updateLayerView(e,t){for(const[,r]of this.snappingSources)r.snappingSource.layerSource.layer===e&&(r.layerView=t)}destroy(){this._set(\"options\",null);for(const[,e]of this.snappingSources)e.destroy()}async fetchCandidates(e,t,r,s){if(!(t&this._domain)||n(this.options)||!this.options.effectiveFeatureEnabled)return[];const u=[],p=this._computeScreenSizeDistanceParameters(e,r),d={distance:p,mode:a(this.view)?.type??\"2d\",point:e,coordinateHelper:r.coordinateHelper,types:this._types};for(const[,{snappingSource:n,layerView:a}]of this.snappingSources)!n.layerSource.enabled||o(a)&&a.suspended||u.push(n.fetchCandidates(d,s).then((e=>e.filter((e=>!this._candidateIsExcluded(n,e,r.excludeFeature))))));const l=(await i(u)).flat();return this._addRightAngleCandidates(l,e,p,r),c(s),F(e,l),l}_addRightAngleCandidates(e,t,r,s){const n=o(s.vertexHandle)?s.vertexHandle.rightEdge?.rightVertex?.pos:o(s.editGeometryOperations)&&\"polygon\"===s.editGeometryOperations.data.type?a(s.editGeometryOperations.data.components[0]?.getFirstVertex())?.pos:null,i=o(s.vertexHandle)?s.vertexHandle.leftEdge?.leftVertex?.pos:o(s.editGeometryOperations)?a(s.editGeometryOperations.data.components[0]?.getLastVertex())?.pos:null,{view:c}=this,u=C(n,c,s),p=C(i,c,s),d=e.length;for(let o=0;o<d;o++)this._addRightAngleCandidate(e[o],p,t,r,e),this._addRightAngleCandidate(e[o],u,t,r,e)}_addRightAngleCandidate(e,t,r,s,o){if(n(t)||!G(e))return;const a=e.constraint.closestTo(t),i=(a[0]-r[0])/s.x,c=(a[1]-r[1])/s.y,{start:u,end:p}=e.constraint;if(i*i+c*c<=1){const r=new M({targetPoint:a,otherVertex:t,otherVertexType:E.NEXT,previousVertex:g(a,u)>g(a,p)?u:p,constraint:new _(t,a),objectId:e.objectId,isDraped:e.isDraped});o.push(r)}}_computeScreenSizeDistanceParameters(e,t){let r=o(this.options)?this.options.distance*(\"touch\"===t.pointer?this.options.touchSensitivityMultiplier:1):0;return n(this.view)?{x:r,y:r,z:r,distance:r}:\"2d\"===this.view.type?(r*=this.view.resolution,{x:r,y:r,z:r,distance:r}):this._computeScreenSizeDistanceParameters3D(e,r,this.view,t)}_computeScreenSizeDistanceParameters3D(e,t,r,s){const{spatialReference:o}=s;r.renderCoordsHelper.toRenderCoords(e,o,D);const n=r.state.camera.computeScreenPixelSizeAt(D),a=n*r.renderCoordsHelper.unitInMeters/r.mapCoordsHelper.unitInMeters,i=t*a,c=H(e,o,v,r),u=c?O(c,e,a,0,0,r,s):0,p=c?O(c,e,0,a,0,r,s):0,d=c?O(c,e,0,0,a,r,s):0;return{x:0===u?0:i/u,y:0===p?0:i/p,z:0===d?0:i/d,distance:n*t}}get _types(){return w.EDGE|w.VERTEX}_candidateIsExcluded(e,t,r){if(n(r))return!1;const s=this._getCandidateObjectId(t);if(n(s))return!1;const o=e.layerSource.layer;return\"graphics\"===o.type?r.uid===s:r.sourceLayer===o&&(!(!r.attributes||!(\"objectIdField\"in o))&&r.attributes[o.objectIdField]===s)}_getCandidateObjectId(e){return e instanceof L?e.objectId:null}_createSourceInfo(e){const t=this._createFeatureSnappingSourceType(e);if(n(t))return null;if(\"loading\"in t)return this.updatingHandles.addPromise(t.loading.then((()=>{this.destroyed||this.notifyChange(\"snappingSources\")}))),null;const r=o(this.view)?this.view.allLayerViews.find((t=>t.layer===e.layer)):null;return new I(t.source,r)}_createFeatureSnappingSourceType(e){switch(e.layer.type){case\"feature\":case\"geojson\":case\"csv\":case\"oriented-imagery\":case\"subtype-group\":case\"wfs\":return this._createFeatureSnappingSourceFeatureLayer(e);case\"graphics\":return this._createFeatureSnappingSourceGraphicsLayer(e);case\"map-notes\":return this._createFeatureSnappingSourceMapNotesLayer(e);case\"scene\":case\"building-scene\":return this._createFeatureSnappingSourceSceneLayer(e)}return null}_createFeatureSnappingSourceSceneLayer(e){const{view:t}=this;if(n(t)||\"3d\"!==t.type)return null;const r=this._getSourceModule(\"scene\");return o(r.module)?{source:new r.module.SceneLayerSnappingSource({layerSource:e,view:t})}:{loading:r.loader}}_createFeatureSnappingSourceFeatureLayer(e){switch(e.layer.source?.type){case\"feature-layer\":case\"oriented-imagery\":{const t=this._getSourceModule(\"featureService\");return o(t.module)?{source:new t.module.FeatureServiceSnappingSource({spatialReference:this.spatialReference,view:this.view,layerSource:e})}:{loading:t.loader}}case\"memory\":case\"csv\":case\"geojson\":case\"wfs\":{if(\"mesh\"===e.layer.geometryType)return null;const t=this._getSourceModule(\"featureCollection\");return o(t.module)?{source:new t.module.FeatureCollectionSnappingSource({layerSource:e,view:this.view})}:{loading:t.loader}}}return null}_createFeatureSnappingSourceGraphicsLayer(e){const t=this._getSourceModule(\"graphics\");return o(t.module)?{source:new t.module.GraphicsSnappingSource({getGraphicsLayers:()=>[e.layer],spatialReference:this.spatialReference,view:this.view,layerSource:e})}:{loading:t.loader}}_createFeatureSnappingSourceMapNotesLayer(e){const t=this._getSourceModule(\"notes\");return o(t.module)?{source:new t.module.GraphicsSnappingSource({getGraphicsLayers:()=>o(e.layer.sublayers)?e.layer.sublayers.toArray():[],spatialReference:this.spatialReference,view:this.view,layerSource:e})}:{loading:t.loader}}_getSourceModule(e){const t=this._sourceModules[e];if(n(t.loader)){const r=this._loadSourceModule(e).then((e=>{t.module=e}));return t.loader=r,{module:t.module,loader:r}}return{module:t.module,loader:t.loader}}_loadSourceModule(e){const t=this.updatingHandles;switch(e){case\"featureService\":return t.addPromise(import(\"./featureSources/FeatureServiceSnappingSource.js\"));case\"featureCollection\":return t.addPromise(import(\"./featureSources/FeatureCollectionSnappingSource.js\"));case\"graphics\":case\"notes\":return t.addPromise(import(\"./featureSources/GraphicsSnappingSource.js\"));case\"scene\":return t.addPromise(import(\"./featureSources/SceneLayerSnappingSource.js\"))}}};e([l({constructOnly:!0})],V.prototype,\"spatialReference\",void 0),e([l({constructOnly:!0})],V.prototype,\"view\",void 0),e([l()],V.prototype,\"options\",void 0),e([l({readOnly:!0})],V.prototype,\"updating\",null),e([l({readOnly:!0})],V.prototype,\"snappingSources\",null),V=e([h(\"esri.views.interactive.snapping.FeatureSnappingEngine\")],V);class I{constructor(e,t){this.snappingSource=e,this.layerView=t,this.handles=new r;const s=this.snappingSource.layerSource.layer;if(\"refresh\"in s){const t=s;this.handles.add(t.on(\"refresh\",(()=>e.refresh())))}this.handles.add([p((()=>e.updating),(t=>e.layerSource.updating=t),d),p((()=>e.availability),(t=>e.layerSource.availability=t),d)])}destroy(){this.snappingSource.destroy(),this.handles.destroy()}}function G(e){return(e instanceof R||e instanceof b)&&!P(e)}function P({constraint:{start:e,end:t}}){const r=m(e,t),s=g(e,t);return r<f()||s/r<A}function O(e,t,r,s,o,n,{spatialReference:a}){const i=y(z,t);i[0]+=r,i[1]+=s,i[2]+=o;const c=H(i,a,v,n);return c?x(c,e):1/0}const D=S(),z=S(),A=1e-4;export{V as FeatureSnappingEngine};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../core/maybe.js\";import{k as t}from\"../../../chunks/vec2.js\";import{absoluteHeightElevationInfo as s}from\"../../../support/elevationInfoUtils.js\";import{defaults as r}from\"./Settings.js\";import{anyMapPointToSnappingPoint as i}from\"./SnappingPoint.js\";import{squaredScreenDistance as o}from\"./snappingUtils.js\";import{vectorToScreenPoint as h}from\"../support/viewUtils.js\";class n{constructor(e,t){this.view=e,this.options=t,this.squaredShortLineThreshold=r.shortLineThreshold*r.shortLineThreshold}snap(t,s){return e(s.vertexHandle)?\"vertex\"!==s.vertexHandle.type?[]:this.snapExistingVertex(t,s):this.snapNewVertex(t,s)}edgeExceedsShortLineThreshold(e,t){return this.exceedsShortLineThreshold(i(e.leftVertex.pos,this.view,t),i(e.rightVertex.pos,this.view,t),t)}exceedsShortLineThreshold(e,t,{spatialReference:r}){return 0===this.squaredShortLineThreshold||o(h(t,r,s,this.view),h(e,r,s,this.view))>this.squaredShortLineThreshold}isVertical(e,s){return t(e,s)<r.verticalLineThreshold}squaredProximityThreshold(e){return\"touch\"===e?this._squaredTouchProximityThreshold:this._squaredMouseProximityThreshold}get _squaredMouseProximityThreshold(){return this.options.distance*this.options.distance}get _squaredTouchProximityThreshold(){const{distance:e,touchSensitivityMultiplier:t}=this.options,s=e*t;return s*s}}export{n as SnappingAlgorithm};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{VerticalPlaneConstraint as t}from\"../SnappingConstraint.js\";import{SnappingDomain as n}from\"../SnappingDomain.js\";import{LineSegmentHintType as i}from\"../snappingUtils.js\";import{SnappingCandidate as e}from\"./SnappingCandidate.js\";import{LineSnappingHint as s}from\"../hints/LineSnappingHint.js\";class r extends e{constructor({lineStart:e,lineEnd:r,targetPoint:o,isDraped:a}){super(o,new t(e,r),a,n.SELF),this._referenceLineHint=new s(i.REFERENCE_EXTENSION,e,r,a,this.domain)}get hints(){return[this._referenceLineHint,new s(i.TARGET,this._lineEndClosestToTarget(),this.targetPoint,this.isDraped,this.domain)]}_lineEndClosestToTarget(){return this.constraint.closestEndTo(this.targetPoint)}}export{r as LineSnappingCandidate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrap as e}from\"../../../core/maybe.js\";import{absoluteHeightElevationInfo as t}from\"../../../support/elevationInfoUtils.js\";import{SnappingAlgorithm as s}from\"./SnappingAlgorithm.js\";import{anyMapPointToSnappingPoint as r,asSnappingPoint as i}from\"./SnappingPoint.js\";import{editEdgeToSnappingEdge as o,squaredScreenDistance as n}from\"./snappingUtils.js\";import{LineSnappingCandidate as d}from\"./candidates/LineSnappingCandidate.js\";import{vectorToScreenPoint as p}from\"../support/viewUtils.js\";import{projectPointToLineLike as a}from\"../../support/geometry3dUtils.js\";import{LineType as h}from\"../../support/geometry2dUtils.js\";class l extends s{snapNewVertex(e,s){const r=s.editGeometryOperations.data.components[0],i=r.edges.length,n=[];if(i<1)return n;const{spatialReference:d}=s,a=p(e,d,t,this.view),{view:h}=this,l=r.edges[i-1];let g=l;do{if(this.edgeExceedsShortLineThreshold(g,s)){const t=o(g,h,s);this._processCandidateProposal(t.left,t.right,e,a,s,n)}g=g.leftVertex.leftEdge}while(g&&g!==l);return n}snapExistingVertex(s,i){const n=[],d=e(i.vertexHandle),a=d.component;if(a.edges.length<2)return n;const{view:h}=this,{spatialReference:l}=i,g=p(s,l,t,h),m=d.leftEdge,f=d.rightEdge;m&&f&&this.edgeExceedsShortLineThreshold(m,i)&&this.edgeExceedsShortLineThreshold(f,i)&&this._processCandidateProposal(r(m.leftVertex.pos,h,i),r(f.rightVertex.pos,h,i),s,g,i,n);const c=a.edges[0];let x=c;do{if(x!==d.leftEdge&&x!==d.rightEdge&&this.edgeExceedsShortLineThreshold(x,i)){const e=o(x,h,i);this._processCandidateProposal(e.left,e.right,s,g,i,n)}x=x.rightVertex.rightEdge}while(x&&x!==c);return n}_processCandidateProposal(e,s,r,o,l,g){const{spatialReference:m,pointer:f}=l,c=i(a(r,{start:e,end:s,type:h.LINE}));n(o,p(c,m,t,this.view))<this.squaredProximityThreshold(f)&&g.push(new d({lineStart:e,lineEnd:s,targetPoint:c,isDraped:\"on-the-ground\"===l.elevationInfo?.mode}))}}export{l as LineSnapper};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{b as e,a as t,k as i}from\"../../../../chunks/vec3.js\";import{a as n}from\"../../../../chunks/vec3f64.js\";import{VerticalPlaneConstraint as r}from\"../SnappingConstraint.js\";import{SnappingDomain as s}from\"../SnappingDomain.js\";import{asSnappingPoint as a}from\"../SnappingPoint.js\";import{LineSegmentHintType as f}from\"../snappingUtils.js\";import{SnappingCandidate as o}from\"./SnappingCandidate.js\";import{LineSnappingHint as d}from\"../hints/LineSnappingHint.js\";import{ParallelSnappingHint as h}from\"../hints/ParallelSnappingHint.js\";class g extends o{constructor({referenceLine:i,lineStart:f,targetPoint:o,isDraped:d}){const h=n(f),{left:g,right:p}=i;e(h,t(h,h,p),g),super(o,new r(f,a(h)),d,s.SELF),this._referenceLines=[{edge:i,fadeLeft:!0,fadeRight:!0}]}get hints(){return[new d(f.TARGET,this.constraint.start,this.targetPoint,this.isDraped,this.domain),new h(this.constraint.start,this.targetPoint,this.isDraped,this.domain),...this._referenceLines.map((e=>new d(f.REFERENCE,e.edge.left,e.edge.right,this.isDraped,this.domain,e.fadeLeft,e.fadeRight)))]}addReferenceLine(e){const t={edge:e,fadeLeft:!0,fadeRight:!0};this._referenceLines.forEach((n=>{i(e.right,n.edge.left)&&(n.fadeLeft=!1,t.fadeRight=!1),i(e.right,n.edge.right)&&(n.fadeRight=!1,t.fadeRight=!1),i(e.left,n.edge.right)&&(n.fadeRight=!1,t.fadeLeft=!1),i(e.left,n.edge.left)&&(n.fadeLeft=!1,t.fadeLeft=!1)})),this._referenceLines.push(t)}}export{g as ParallelLineSnappingCandidate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrap as e}from\"../../../core/maybe.js\";import{k as t}from\"../../../chunks/vec2.js\";import{a as r}from\"../../../chunks/vec2f64.js\";import{f as i}from\"../../../chunks/vec3f64.js\";import{absoluteHeightElevationInfo as s}from\"../../../support/elevationInfoUtils.js\";import{defaults as o}from\"./Settings.js\";import{SnappingAlgorithm as n}from\"./SnappingAlgorithm.js\";import{anyMapPointToSnappingPoint as l,asSnappingPoint as a}from\"./SnappingPoint.js\";import{editEdgeToSnappingEdge as c,squaredScreenDistance as h}from\"./snappingUtils.js\";import{ParallelLineSnappingCandidate as p}from\"./candidates/ParallelLineSnappingCandidate.js\";import{vectorToScreenPoint as d}from\"../support/viewUtils.js\";import{projectPointToLine as f}from\"../../support/geometry2dUtils.js\";class g extends n{snapNewVertex(e,t){const r=t.editGeometryOperations.data.components[0],i=r.edges.length,o=r.vertices.length,n=[];if(i<2)return n;const{view:a}=this,h=d(e,t.spatialReference,s,a),p=l(r.vertices[o-1].pos,a,t),f=l(r.vertices[0].pos,a,t),g=r.edges[i-1];let m=g;do{if(this.edgeExceedsShortLineThreshold(m,t)){const r=c(m,a,t);this._checkEdgeForParallelLines(r,p,e,h,t,n),this._checkEdgeForParallelLines(r,f,e,h,t,n)}m=m.leftVertex.leftEdge}while(m&&m!==g);return n}snapExistingVertex(t,r){const i=[],o=e(r.vertexHandle),n=o.component;if(n.edges.length<3)return i;const{view:a}=this,h=d(t,r.spatialReference,s,a),p=o.leftEdge,f=o.rightEdge,g=n.vertices[0],m=l(g.pos,a,r),u=n.vertices.length,v=n.vertices[u-1],E=l(v.pos,a,r),L=n.edges[0];let x=L;do{if(x!==p&&x!==f&&this.edgeExceedsShortLineThreshold(x,r)){const e=c(x,a,r);p&&this._checkEdgeForParallelLines(e,l(p.leftVertex.pos,a,r),t,h,r,i),f&&this._checkEdgeForParallelLines(e,l(f.rightVertex.pos,a,r),t,h,r,i),o===g?this._checkEdgeForParallelLines(e,E,t,h,r,i):o===v&&this._checkEdgeForParallelLines(e,m,t,h,r,i)}x=x.rightVertex.rightEdge}while(x&&x!==L);return i}_checkEdgeForParallelLines(e,r,n,l,c,g){const u=e.left,v=e.right;if(f(m,r,u,v),t(m,r)<o.parallelLineThreshold)return;f(m,n,u,v,r);const{spatialReference:E,pointer:L}=c,x=a(i(m[0],m[1],n[2]));if(h(l,d(x,E,s,this.view))<this.squaredProximityThreshold(L)){if(this.isVertical(x,r)||this.isVertical(u,v))return;if(this._parallelToPreviousCandidate(e,g))return;g.push(new p({referenceLine:e,lineStart:r,targetPoint:x,isDraped:\"on-the-ground\"===c.elevationInfo?.mode}))}}_parallelToPreviousCandidate(e,r){const i=e.left,s=e.right;for(const n of r)if(f(m,s,n.constraint.start,n.constraint.end,i),t(m,s)<o.parallelLineThreshold)return n.addReferenceLine(e),!0;return!1}}const m=r();export{g as ParallelLineSnapper};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrap as e}from\"../../../core/maybe.js\";import{a as t,h as i,v as s,w as r}from\"../../../chunks/vec2.js\";import{a as o}from\"../../../chunks/vec2f64.js\";import{s as n,z as a}from\"../../../chunks/vec3.js\";import{a as p,c as h}from\"../../../chunks/vec3f64.js\";import{absoluteHeightElevationInfo as c}from\"../../../support/elevationInfoUtils.js\";import{SnappingAlgorithm as d}from\"./SnappingAlgorithm.js\";import{VerticalHalfPlaneConstraint as g}from\"./SnappingConstraint.js\";import{anyMapPointToSnappingPoint as f,asSnappingPoint as l}from\"./SnappingPoint.js\";import{squaredScreenDistance as m}from\"./snappingUtils.js\";import{RightAngleSnappingCandidate as x,OtherVertexType as v}from\"./candidates/RightAngleSnappingCandidate.js\";import{vectorToScreenPoint as E}from\"../support/viewUtils.js\";class V extends d{snapNewVertex(e,t){const i=t.editGeometryOperations.data.components[0],s=i.vertices.length,r=[];if(s<2)return r;const{view:o}=this,n=E(e,t.spatialReference,c,o),a=i.vertices[s-1];if(this.edgeExceedsShortLineThreshold(a.leftEdge,t)){const i=f(a.pos,o,t),s=f(a.leftEdge.leftVertex.pos,o,t);this._checkForSnappingCandidate(r,s,i,e,n,t)}const p=i.vertices[0];if(this.edgeExceedsShortLineThreshold(p.rightEdge,t)){const i=f(p.pos,o,t),s=f(p.rightEdge.rightVertex.pos,o,t);this._checkForSnappingCandidate(r,s,i,e,n,t)}return r}snapExistingVertex(t,i){const s=[],r=e(i.vertexHandle);if(r.component.vertices.length<3)return s;const{view:o}=this,n=E(t,i.spatialReference,c,o),a=r.leftEdge,p=r.rightEdge;if(a&&a.leftVertex.leftEdge){const e=a.leftVertex.leftEdge;if(this.edgeExceedsShortLineThreshold(e,i)){const r=f(e.rightVertex.pos,o,i),a=f(e.leftVertex.pos,o,i);this._checkForSnappingCandidate(s,a,r,t,n,i)}}if(p&&p.rightVertex.rightEdge){const e=p.rightVertex.rightEdge;if(this.edgeExceedsShortLineThreshold(e,i)){const r=f(e.leftVertex.pos,o,i),a=f(e.rightVertex.pos,o,i);this._checkForSnappingCandidate(s,a,r,t,n,i)}}return s}_checkForSnappingCandidate(e,o,d,f,V,j){const{spatialReference:k,pointer:w}=j;t(u,d,o);const C=n(S,u[1],-u[0],0),T=i(C,t(u,f,d))/s(C),F=l(r(p(f),d,C,T));if(m(V,E(F,k,c,this.view))<this.squaredProximityThreshold(w)){if(this.isVertical(F,d)||this.isVertical(d,o))return;const t=a(h(),d,C,Math.sign(T));e.push(new x({targetPoint:F,constraint:new g(d,l(t)),previousVertex:o,otherVertex:d,otherVertexType:v.CENTER,isDraped:\"on-the-ground\"===j.elevationInfo?.mode}))}}}const u=o(),S=h();export{V as RightAngleSnapper};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{d as i}from\"../../../../chunks/vec2.js\";import{h as t}from\"../../../../chunks/vec3.js\";import{c as s}from\"../../../../chunks/vec3f64.js\";import{VerticalCylinderConstraint as n}from\"../SnappingConstraint.js\";import{SnappingDomain as p}from\"../SnappingDomain.js\";import{asSnappingPoint as o}from\"../SnappingPoint.js\";import{LineSegmentHintType as r}from\"../snappingUtils.js\";import{SnappingCandidate as a}from\"./SnappingCandidate.js\";import{LineSnappingHint as e}from\"../hints/LineSnappingHint.js\";import{RightAngleSnappingHint as m}from\"../hints/RightAngleSnappingHint.js\";class h extends a{constructor({targetPoint:r,point1:a,point2:e,isDraped:m}){super(r,new n(o(t(s(),a,e,.5)),.5*i(a,e)),m,p.SELF),this._p1=a,this._p2=e}get hints(){return[new e(r.REFERENCE,this.targetPoint,this._p1,this.isDraped,this.domain),new e(r.REFERENCE,this.targetPoint,this._p2,this.isDraped,this.domain),new m(this._p1,this.targetPoint,this._p2,this.isDraped,this.domain)]}}export{h as RightAngleTriangleSnappingCandidate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrap as e}from\"../../../core/maybe.js\";import{l as t,d as i}from\"../../../chunks/vec2.js\";import{a as s}from\"../../../chunks/vec2f64.js\";import{c as r}from\"../../../chunks/vec3f64.js\";import{absoluteHeightElevationInfo as o}from\"../../../support/elevationInfoUtils.js\";import{SnappingAlgorithm as n}from\"./SnappingAlgorithm.js\";import{anyMapPointToSnappingPoint as p,asSnappingPoint as a}from\"./SnappingPoint.js\";import{squaredScreenDistance as d}from\"./snappingUtils.js\";import{RightAngleTriangleSnappingCandidate as m}from\"./candidates/RightAngleTriangleSnappingCandidate.js\";import{vectorToScreenPoint as c}from\"../support/viewUtils.js\";import{projectPointToCircle as l}from\"../../support/geometry2dUtils.js\";class h extends n{snapNewVertex(e,t){const i=t.editGeometryOperations.data.components[0],s=[],r=i.vertices.length;if(\"polygon\"!==t.editGeometryOperations.data.type||r<2)return s;const{view:o}=this,n=i.vertices[0],a=i.vertices[r-1],d=p(n.pos,o,t),m=p(a.pos,o,t);return this._processCandidateProposal(d,m,e,t,s),s}snapExistingVertex(t,i){const s=[],r=e(i.vertexHandle),o=r.component;if(o.edges.length<2)return s;if(\"polyline\"===i.editGeometryOperations.data.type&&(0===r.index||r.index===o.vertices.length-1))return s;const{view:n}=this,a=p(r.leftEdge.leftVertex.pos,n,i),d=p(r.rightEdge.rightVertex.pos,n,i);return this._processCandidateProposal(a,d,t,i,s),s}_processCandidateProposal(e,s,n,p,h){if(!this.exceedsShortLineThreshold(e,s,p))return;const g=t(f,e,s,.5),u=.5*i(e,s),v=r();l(v,n,g,u),v[2]=n[2];const x=a(v),{spatialReference:j,pointer:y}=p,w=c(n,j,o,this.view);if(d(w,c(x,j,o,this.view))<this.squaredProximityThreshold(y)){if(this.isVertical(e,x)||this.isVertical(x,s))return;h.push(new m({targetPoint:x,point1:e,point2:s,isDraped:\"on-the-ground\"===p.elevationInfo?.mode}))}}}const f=s();export{h as RightAngleTriangleSnapper};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as s}from\"../../../chunks/tslib.es6.js\";import o from\"../../../core/Accessor.js\";import t from\"../../../core/Collection.js\";import{property as i}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import{LineSnapper as e}from\"./LineSnapper.js\";import{ParallelLineSnapper as p}from\"./ParallelLineSnapper.js\";import{RightAngleSnapper as n}from\"./RightAngleSnapper.js\";import{RightAngleTriangleSnapper as a}from\"./RightAngleTriangleSnapper.js\";import{SnappingDomain as c}from\"./SnappingDomain.js\";import{sortCandidatesInPlace as m}from\"./snappingUtils.js\";let f=class extends o{constructor(s){super(s),this.updating=!1,this._snappers=new t,this._domain=c.SELF}initialize(){this._snappers.push(new p(this.view,this.options),new e(this.view,this.options),new n(this.view,this.options),new a(this.view,this.options))}set options(s){this._set(\"options\",s);for(const o of this._snappers)o.options=s}async fetchCandidates(s,o,t){if(!(o&this._domain&&this.options.effectiveSelfEnabled))return[];const i=[];for(const r of this._snappers.items)for(const o of r.snap(s,t))i.push(o);return m(s,i),i}};s([i({readOnly:!0})],f.prototype,\"updating\",void 0),s([i({constructOnly:!0})],f.prototype,\"view\",void 0),s([i()],f.prototype,\"options\",null),f=s([r(\"esri.views.interactive.snapping.SelfSnappingEngine\")],f);export{f as SelfSnappingEngine};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{FeatureSnappingEngine as e}from\"./FeatureSnappingEngine.js\";import{SelfSnappingEngine as n}from\"./SelfSnappingEngine.js\";function i(i,p){return[new n({view:i,options:p}),new e({view:i,options:p,spatialReference:i.spatialReference})]}export{i as defaultSnappingEnginesFactory};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import o from\"../../../core/Accessor.js\";import{property as t}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as e}from\"../../../core/accessorSupport/decorators/subclass.js\";let s=class extends o{constructor(r){super(r),this.layer=null,this.enabled=!0,this.updating=!1,this.availability=1}};r([t({constructOnly:!0})],s.prototype,\"layer\",void 0),r([t()],s.prototype,\"enabled\",void 0),r([t()],s.prototype,\"updating\",void 0),r([t()],s.prototype,\"availability\",void 0),s=r([e(\"esri.views.interactive.snapping.FeatureSnappingLayerSource\")],s);const p=s;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import t from\"../../../core/Accessor.js\";import o from\"../../../core/Collection.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as i}from\"../../../core/accessorSupport/decorators/subclass.js\";import s from\"./FeatureSnappingLayerSource.js\";import{defaults as a}from\"./Settings.js\";let l=class extends t{constructor(e){super(e),this.enabled=!1,this.enabledToggled=!1,this.selfEnabled=!0,this.featureEnabled=!0,this.featureSources=new o,this.distance=a.distance,this.touchSensitivityMultiplier=a.touchSensitivityMultiplier}get effectiveEnabled(){return this.enabledToggled?!this.enabled:this.enabled}get effectiveSelfEnabled(){return this.effectiveEnabled&&this.selfEnabled}get effectiveFeatureEnabled(){return this.effectiveEnabled&&this.featureEnabled}};e([r()],l.prototype,\"enabled\",void 0),e([r()],l.prototype,\"enabledToggled\",void 0),e([r()],l.prototype,\"selfEnabled\",void 0),e([r()],l.prototype,\"featureEnabled\",void 0),e([r({type:o.ofType(s)})],l.prototype,\"featureSources\",void 0),e([r()],l.prototype,\"distance\",void 0),e([r()],l.prototype,\"touchSensitivityMultiplier\",void 0),e([r({readOnly:!0})],l.prototype,\"effectiveEnabled\",null),e([r({readOnly:!0})],l.prototype,\"effectiveSelfEnabled\",null),e([r({readOnly:!0})],l.prototype,\"effectiveFeatureEnabled\",null),l=e([i(\"esri.views.interactive.snapping.SnappingOptions\")],l);const n=l;export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{IntersectionConstraint as t}from\"../SnappingConstraint.js\";import{SnappingDomain as i}from\"../SnappingDomain.js\";import{SnappingCandidate as n}from\"./SnappingCandidate.js\";import{IntersectionSnappingHint as s}from\"../hints/IntersectionSnappingHint.js\";class r extends n{constructor(n,s,r,o){super(n,new t(n,s.constraint,r.constraint),o,i.ALL),this.first=s,this.second=r}get hints(){return this.first.targetPoint=this.targetPoint,this.second.targetPoint=this.targetPoint,[...this.first.hints,...this.second.hints,new s(this.targetPoint,this.isDraped,this.domain)]}}export{r as IntersectionSnappingCandidate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import t from\"../../../core/Evented.js\";import{HandleOwner as n}from\"../../../core/HandleOwner.js\";import{isNone as i,isSome as s}from\"../../../core/maybe.js\";import{isAborted as r}from\"../../../core/promiseUtils.js\";import{watch as a,sync as o,syncAndInitial as p}from\"../../../core/reactiveUtils.js\";import{property as c}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as d}from\"../../../core/accessorSupport/decorators/subclass.js\";import{project as h,initializeProjection as l}from\"../../../geometry/projection.js\";import{absoluteHeightElevationInfo as u}from\"../../../support/elevationInfoUtils.js\";import{defaults as f}from\"./Settings.js\";import{SnappingDomain as _}from\"./SnappingDomain.js\";import{defaultSnappingEnginesFactory as g}from\"./snappingFactory.js\";import m from\"./SnappingOptions.js\";import{pointToSnappingPoint as S,snappingPointToSnappingOutput as v}from\"./SnappingPoint.js\";import{sortCandidatesInPlace as y,squaredScreenDistance as C}from\"./snappingUtils.js\";import{IntersectionSnappingCandidate as P}from\"./candidates/IntersectionSnappingCandidate.js\";import{vectorToScreenPoint as T}from\"../support/viewUtils.js\";let w=class extends(t.EventedMixin(n)){constructor(e){super(e),this.options=new m,this.snappingEnginesFactory=g,this._engines=[],this._currentMainCandidate=null,this._currentOtherActiveCandidates=[],this._currentSnappedType=E.MAIN}initialize(){this.handles.add([a((()=>{const{effectiveFeatureEnabled:e,effectiveSelfEnabled:t,touchSensitivityMultiplier:n,distance:i}=this.options;return{effectiveFeatureEnabled:e,effectiveSelfEnabled:t,touchSensitivityMultiplier:n,distance:i}}),(()=>{this.doneSnapping(),this.emit(\"changed\")}),o),a((()=>this.options),(e=>{for(const t of this._engines)t.options=e}),o),a((()=>({viewReady:this.view.ready,viewSpatialReference:this.view.spatialReference,snappingEnginesFactory:this.snappingEnginesFactory})),(({viewReady:e,snappingEnginesFactory:t})=>this._recreateEngines(e,t)),p)])}destroy(){this._destroyEngines()}get updating(){return this._engines.some((e=>e.updating))}_recreateEngines(e,t){if(this._destroyEngines(),!e)return;const{view:n,options:i}=this;this._engines=t(n,i)}_destroyEngines(){for(const e of this._engines)e.destroy();this._engines=[]}get _squaredMouseProximityTreshold(){return this.options.distance*this.options.distance}get _squaredTouchProximityThreshold(){const{distance:e,touchSensitivityMultiplier:t}=this.options,n=e*t;return n*n}get _squaredSatisfiesConstraintThreshold(){return f.satisfiesConstraintScreenThreshold*f.satisfiesConstraintScreenThreshold}async snap(e){return M(e)?this._snapMultiPoint(e):this._snapSinglePoint(e)}update(e){const{point:t,context:n}=e;this._removeVisualization();const r=this._currentMainCandidate;if(i(r))return t;const a=this._selectUpdateInput(e);if(i(a))return t;const{spatialReference:o}=n,p=h(a,o);if(i(p))return t;const{view:c}=this,{elevationInfo:d,visualizer:l}=n,u=[],f=S(p,c,n),_=r.constraint.closestTo(f);if(!this._arePointsWithinScreenThreshold(f,_,n))return this._resetSnappingState(),t;r.targetPoint=_,u.push(...r.hints);for(const i of this._currentOtherActiveCandidates)i.targetPoint=_,u.push(...i.hints);return s(l)&&this.handles.add(l.draw(u,{spatialReference:o,elevationInfo:R(n),view:c,selfSnappingZ:n.selfSnappingZ}),I),v(_,c,{z:t.z,m:t.m,spatialReference:t.spatialReference,elevationInfo:d})}doneSnapping(){this._removeVisualization(),this._resetSnappingState()}_selectUpdateInput({point:e,scenePoint:t}){switch(this._currentSnappedType){case E.MAIN:return e;case E.SCENE:return t}}_resetSnappingState(){this._currentMainCandidate=null,this._currentOtherActiveCandidates=[],this._currentSnappedType=E.MAIN}_removeVisualization(){this.handles.remove(I)}async _snapSinglePoint({point:e,context:t,signal:n}){const{view:i}=this,s=S(e,i,t),r=await this._fetchCandidates(s,_.ALL,t,n);return this._createSnapResult(s,E.MAIN,r,i,t,{z:e.z,m:e.m,spatialReference:e.spatialReference,elevationInfo:t.elevationInfo},n)}async _snapMultiPoint({point:e,scenePoint:t,context:n,signal:i}){const{view:s}=this,{coordinateHelper:r,spatialReference:a}=n;await l(t.spatialReference,a);const o=h(t,a),p=S(o,s,n),c=await this._fetchCandidates(p,_.FEATURE,n,i);if(c.length>0){const e=await this._fetchCandidates(p,_.SELF,n,i);return this._createSnapResult(p,E.SCENE,[...c,...e],s,n,{z:o.z,m:o.m,spatialReference:o.spatialReference,elevationInfo:n.elevationInfo},i)}const d=S(e,s,n),u=await this._fetchCandidates(d,_.SELF,n,i);return this._createSnapResult(d,E.MAIN,u,s,n,{z:r.hasZ()&&e.hasZ?e.z??0:void 0,m:r.hasM()&&e.hasM?e.m??0:void 0,spatialReference:e.spatialReference,elevationInfo:n.elevationInfo},i)}async _fetchCandidates(e,t,n,i){return(await Promise.all(this._engines.map((s=>s.fetchCandidates(e,t,n,i))))).flat()}_createSnapResult(e,t,n,i,a,o,p){return{get valid(){return!r(p)},apply:()=>{const{spatialReference:r}=a,{snappedPoint:p,hints:c}=this._processCandidates(e,t,n,a);return this._removeVisualization(),s(a.visualizer)&&this.handles.add(a.visualizer.draw(c,{spatialReference:r,elevationInfo:u,view:i,selfSnappingZ:a.selfSnappingZ}),I),v(p,i,o)}}}_processCandidates(e,t,n,i){if(n.length<1)return this.doneSnapping(),{snappedPoint:e,hints:[]};this._currentSnappedType!==t&&this._resetSnappingState(),y(e,n);const r=this._currentMainCandidate;if(s(r)){const s=this._findOldConstraintInNewCandidates(r,n);if(s>=0){if(!(n[s]instanceof P))return this._intersectWithOtherCandidates(s,n,e,t,i);if(this._arePointsWithinScreenThreshold(e,r.targetPoint,i))return this._updateSnappingCandidate(r,t,n,i)}}return this._intersectWithOtherCandidates(0,n,e,t,i)}_findOldConstraintInNewCandidates(e,t){return e instanceof P?this._findOldCandidateIndex(t,e.first)>=0&&this._findOldCandidateIndex(t,e.second)>=0?0:-1:this._findOldCandidateIndex(t,e)}_intersectWithOtherCandidates(e,t,n,i,s){const{coordinateHelper:r}=s,a=t[e],o=[];for(let p=0;p<t.length;++p){if(p===e)continue;const i=t[p];for(const e of a.constraint.intersect(i.constraint)){const t=e.closestTo(a.targetPoint);o.push([new P(t,a,i,i.isDraped),this._squaredScreenDistance(n,t,r)])}}return o.length>0&&(o.sort(((e,t)=>e[1]-t[1])),o[0][1]<this._squaredPointProximityThreshold(s.pointer))?this._updateSnappingCandidate(o[0][0],i,t,s):this._updateSnappingCandidate(a,i,t,s)}_updateSnappingCandidate(e,t,n,i){this.doneSnapping(),this._currentMainCandidate=e,this._currentSnappedType=t;const s=this._currentMainCandidate.targetPoint,r=[];r.push(...e.hints);for(const a of n){if(e instanceof P){if(a.constraint.equals(e.first.constraint)||a.constraint.equals(e.second.constraint))continue}else if(a.constraint.equals(e.constraint))continue;const t=a.constraint.closestTo(s);this._squaredScreenDistance(t,s,i.coordinateHelper)<this._squaredSatisfiesConstraintThreshold&&(a.targetPoint=s,this._currentOtherActiveCandidates.push(a),r.push(...a.hints))}return{snappedPoint:s,hints:r}}_squaredPointProximityThreshold(e){return\"touch\"===e?this._squaredTouchProximityThreshold:this._squaredMouseProximityTreshold}_arePointsWithinScreenThreshold(e,t,n){return this._squaredScreenDistance(e,t,n.coordinateHelper)<this._squaredPointProximityThreshold(n.pointer)}_squaredScreenDistance(e,t,n){return C(this._toScreen(e,n),this._toScreen(t,n))}_toScreen(e,t){return T(e,t.spatialReference,u,this.view)}_findOldCandidateIndex(e,t){let n=-1;for(let i=0;i<e.length;++i)if(t.constraint.equals(e[i].constraint)){n=i;break}return n}get test(){return{visualizationsActive:this.handles.has(I),engines:this._engines}}};var E;e([c({constructOnly:!0})],w.prototype,\"view\",void 0),e([c()],w.prototype,\"options\",void 0),e([c({readOnly:!0})],w.prototype,\"updating\",null),e([c()],w.prototype,\"snappingEnginesFactory\",void 0),e([c()],w.prototype,\"_engines\",void 0),e([c()],w.prototype,\"_squaredMouseProximityTreshold\",null),e([c()],w.prototype,\"_squaredTouchProximityThreshold\",null),e([c()],w.prototype,\"_squaredSatisfiesConstraintThreshold\",null),w=e([d(\"esri.views.interactive.snapping.SnappingManager\")],w),function(e){e[e.MAIN=0]=\"MAIN\",e[e.SCENE=1]=\"SCENE\"}(E||(E={}));const I=\"visualization-handle\";function M(e){return s(e.scenePoint)}function R({coordinateHelper:e,elevationInfo:t}){return e.hasZ()?u:t}export{w as SnappingManager};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import{neverReached as t}from\"../../../core/compilerUtils.js\";import o from\"../../../core/Evented.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";let p=class extends o.EventedAccessor{constructor(e){super(e),this.cancelled=!1,this.history={undo:[],redo:[]},this.type=null}get tool(){if(!this.activeComponent)return null;switch(this.activeComponent.type){case\"graphic-mover\":case\"move-3d\":return\"move\";case\"box\":case\"transform-3d\":return\"transform\";case\"reshape\":case\"reshape-3d\":return\"reshape\";case\"draw-2d\":case\"draw-3d\":return this.activeComponent.geometryType;default:t(this.activeComponent)}return null}addToHistory(e){this.history.redo=[],this.history.undo.push(e)}resetHistory(){this.history.redo=[],this.history.undo=[]}canUndo(){return this.history.undo.length>0}canRedo(){return this.history.redo.length>0}complete(){this._reset(),this.onEnd(),this.emit(\"complete\")}cancel(){this.cancelled=!0,this.complete()}_reset(){this.activeComponent?.reset()}refreshComponent(){const e=this.activeComponent;e&&(\"box\"!==e.type&&\"reshape\"!==e.type&&\"graphic-mover\"!==e.type||e.refresh())}set undo(e){this._set(\"undo\",(()=>{this.canUndo()&&e()}))}set redo(e){this._set(\"redo\",(()=>{this.canRedo()&&e()}))}};e([r()],p.prototype,\"activeComponent\",void 0),e([r()],p.prototype,\"cancelled\",void 0),e([r()],p.prototype,\"history\",void 0),e([r()],p.prototype,\"tool\",null),e([r()],p.prototype,\"type\",void 0),e([r()],p.prototype,\"canUndo\",null),e([r()],p.prototype,\"canRedo\",null),e([r()],p.prototype,\"onEnd\",void 0),e([r()],p.prototype,\"undo\",null),e([r()],p.prototype,\"redo\",null),e([r()],p.prototype,\"toggleTool\",void 0),e([r()],p.prototype,\"addToSelection\",void 0),e([r()],p.prototype,\"removeFromSelection\",void 0),p=e([s(\"esri.widgets.Sketch.support.OperationHandle\")],p);let n=class extends p{};e([r()],n.prototype,\"activeComponent\",void 0),n=e([s(\"esri.widgets.Sketch.support.CreateOperationHandle\")],n);let i=class extends p{};e([r()],i.prototype,\"activeComponent\",void 0),i=e([s(\"esri.widgets.Sketch.support.UpdateOperationHandle\")],i);export{n as CreateOperationHandle,p as OperationHandle,i as UpdateOperationHandle};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{symbolTypes as e}from\"../../symbols.js\";import o from\"../../core/Collection.js\";import i from\"../../core/Error.js\";import a from\"../../core/Evented.js\";import r from\"../../core/Handles.js\";import s from\"../../core/Logger.js\";import{destroyMaybe as n,isSome as p,unwrap as l,abortMaybe as h,isNone as c,unwrapOr as d,get as u}from\"../../core/maybe.js\";import{createAbortError as y,whenOrAbort as m}from\"../../core/promiseUtils.js\";import{on as v,watch as g,when as f,whenOnce as _,syncAndInitial as w}from\"../../core/reactiveUtils.js\";import{property as G}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as b}from\"../../core/accessorSupport/decorators/subclass.js\";import{getReferenceEllipsoid as T}from\"../../geometry/ellipsoidUtils.js\";import{canProjectWithoutEngine as O,isLoaded as E,load as S,project as A}from\"../../geometry/projection.js\";import{geometryToCoordinates as C}from\"../../geometry/support/coordsUtils.js\";import{equals as k}from\"../../geometry/support/spatialReferenceUtils.js\";import M from\"../../layers/GraphicsLayer.js\";import{SupportedGraphicResult as H,isSupportedGraphicResultMessage as R}from\"../../views/3d/interactive/editingTools/isSupportedGraphicUtils.js\";import{isSupportedGraphic as I}from\"../../views/3d/interactive/editingTools/moveGraphic/isSupportedGraphic.js\";import{isSupportedGraphic as D}from\"../../views/3d/interactive/editingTools/reshapeGraphic/isSupportedGraphic.js\";import{isSupportedGraphic as U}from\"../../views/3d/interactive/editingTools/transformGraphic/isSupportedGraphic.js\";import{addUniqueLayer as x}from\"../../views/draw/support/layerUtils.js\";import{ViewEventPriorities as P}from\"../../views/input/InputManager.js\";import{SKETCH_KEYS as j}from\"../../views/interactive/keybindings.js\";import L from\"../../views/interactive/sketch/SketchLabelOptions.js\";import F from\"../../views/interactive/sketch/SketchTooltipOptions.js\";import{SnappingManager as V}from\"../../views/interactive/snapping/SnappingManager.js\";import W from\"../../views/interactive/snapping/SnappingOptions.js\";import{setupSnappingToggleHandles as K}from\"../../views/interactive/snapping/snappingUtils.js\";import{findFirstGraphicHit as q}from\"../../views/support/hitTestSelectUtils.js\";import{createScreenPointFromEvent as Z}from\"../../views/support/screenUtils.js\";import{CreateOperationHandle as z,UpdateOperationHandle as N}from\"./support/OperationHandle.js\";import B from\"../../symbols/SimpleMarkerSymbol.js\";import $ from\"../../symbols/SimpleFillSymbol.js\";import Y from\"../../symbols/SimpleLineSymbol.js\";const J={defaultZ:0},Q={reshapeOptions:{edgeOperation:\"split\",shapeOperation:\"move\",vertexOperation:\"move\"},enableMoveAllGraphics:!0,enableRotation:!0,enableScaling:!0,multipleSelectionEnabled:!0,preserveAspectRatio:!1,toggleToolOnClick:!0,enableZ:!0,tool:\"transform\"};let X=class extends a.EventedAccessor{constructor(t){super(t),this._numUpdating=0,this._handles=new r,this._internalGraphicsLayer=new M({listMode:\"hide\",internal:!0,title:\"SVM Internal\"}),this._operationHandle=null,this._viewHandles=new r,this.activeFillSymbol=null,this.activeLineSymbol=null,this.activeVertexSymbol=null,this.allowDeleteKey=!0,this.labelOptions=new L,this.layer=null,this.pointSymbol=new B({style:\"circle\",size:6,color:[255,255,255],outline:{color:[50,50,50],width:1}}),this.polygonSymbol=new $({color:[150,150,150,.2],outline:{color:[50,50,50],width:2}}),this.polylineSymbol=new Y({color:[130,130,130,1],width:2}),this._snappingManager=null,this.tooltipOptions=new F,this.updateGraphics=new o,this.updateOnGraphicClick=!0,this.updatePointSymbol=new B({size:10,color:[0,200,255,.5],outline:{color:\"black\",width:1}}),this.updatePolygonSymbol=new $({color:[12,207,255,.2],outline:{join:\"round\",color:[12,207,255],width:2}}),this.updatePolylineSymbol=new Y({color:[12,207,255],width:2}),this.vertexSymbol=new B({style:\"circle\",size:6,color:[255,255,255],outline:{color:[50,50,50],width:1}}),this._moduleLoaderAbortController=null,this._viewReadyAbortController=null,this._originalAutoOpenEnabled=null,this.defaultCreateOptions=J,this.defaultUpdateOptions=Q,this.snappingOptions=new W}initialize(){this._handles.add([v((()=>this.view?.map?.layers),\"change\",(t=>{t.removed.includes(this.layer)&&this.cancel()})),v((()=>this.layer?.graphics),\"change\",(t=>{if(p(this._operationHandle))for(const e of t.removed)this.updateGraphics.includes(e)&&(this.updateGraphics.length>1?this._operationHandle.removeFromSelection(e):this._operationHandle.cancel())})),g((()=>this.layer?.elevationInfo??null),(t=>{t!==this._internalGraphicsLayer.elevationInfo&&(this.cancel(),this._internalGraphicsLayer.elevationInfo=t)}),w),g((()=>this.view),(t=>{n(this._snappingManager),t&&(this._snappingManager=new V({view:t,options:this.snappingOptions}),\"2d\"===t.type?import(\"../../views/2d/interactive/editingTools.js\"):\"3d\"===t.type&&(import(\"../../views/3d/interactive/editingTools.js\"),import(\"../../views/3d/layers/GraphicsLayerView3D.js\")))}),w),g((()=>this.view?.spatialReference),((t,e)=>{t&&e&&!t.equals(e)&&this.cancel()}))]),K(this)}destroy(){this.cancel(),this._handles=n(this._handles),this._viewHandles=n(this._viewHandles),this._removeDefaultLayer(),this._snappingManager=n(this._snappingManager),this._set(\"view\",null),this.emit(\"destroy\")}get _defaultUpdateTool(){return\"3d\"===this.view?.type?\"move\":\"transform\"}get updating(){return this._numUpdating>0||p(this._snappingManager)&&this._snappingManager.updating}get activeTool(){return this._operationHandle?.tool??null}get activeComponent(){return this._operationHandle?.activeComponent??null}get createGraphic(){return!p(this.activeComponent)||\"draw-3d\"!==this.activeComponent.type&&\"draw-2d\"!==this.activeComponent.type?this._get(\"createGraphic\"):l(this.activeComponent.graphic)}get defaultCreateOptions(){return this._get(\"defaultCreateOptions\")}set defaultCreateOptions(t){this._set(\"defaultCreateOptions\",{...J,...t})}get defaultUpdateOptions(){return this._get(\"defaultUpdateOptions\")}set defaultUpdateOptions(t){this._set(\"defaultUpdateOptions\",{...Q,...t,reshapeOptions:{...Q.reshapeOptions,...t?.reshapeOptions}})}set snappingOptions(t){p(this._snappingManager)&&(this._snappingManager.options=t),this._set(\"snappingOptions\",t)}get state(){const t=!(!this.view?.ready||!this.layer),e=this._operationHandle;return t&&e?\"active\":t?\"ready\":\"disabled\"}get view(){return this._get(\"view\")}set view(t){const e=this._get(\"view\");if(e){const{container:t,map:o}=e;t&&(e.cursor=null),o&&o.remove(this._internalGraphicsLayer),this._viewHandles.removeAll(),this.cancel()}const o=\"view-ready\";this._handles.remove(o),t&&this._handles.add(f((()=>t.ready),(e=>{this._viewHandles.removeAll(),e&&this._viewHandles.add(this._generateViewHandles(t))}),w),o),this._set(\"view\",t)}cancel(){this._moduleLoaderAbortController=h(this._moduleLoaderAbortController),this._viewReadyAbortController=h(this._viewReadyAbortController),this._operationHandle&&this._operationHandle.cancel()}complete(){this._operationHandle&&this._operationHandle.complete()}delete(){const{state:t,updateGraphics:e}=this;if(\"active\"===t&&e.length){const{activeTool:t,layer:o}=this,i=e.toArray();o.removeMany(i),this.cancel(),this._emitDeleteEvent({graphics:i,tool:t})}}duplicate(){if(\"active\"===this.state&&this.updateGraphics.length){const t=this.updateGraphics.map((t=>t.clone())).toArray();return this.layer.addMany(t),this.emit(\"duplicate\",{graphics:t,type:\"duplicate\"}),t}return[]}async create(t,e){this.cancel(),await this._waitViewReady();const{view:o,layer:i}=this;if(!o||\"disabled\"===this.state)throw i||this._logMissingLayer(),y();if(p(o.activeTool)&&(o.activeTool=null),!t)return void this._logError(\"sketch:missing-parameter\",\"Missing parameter 'tool'.\");x(o,this._internalGraphicsLayer);const a=await this._setupCreateOperation(t,e);if(c(a)||this.destroyed)return void o.map.remove(this._internalGraphicsLayer);const r=()=>{if(a===this._operationHandle){const e=this.createGraphic,o=this._operationHandle.cancelled;this._operationHandle.destroy(),this._operationHandle=null,this._set(\"createGraphic\",null),this.view&&this.view.map&&this.view.map.remove(this._internalGraphicsLayer),a.cancelled||null==e||i.add(e),this.emit(\"create\",{graphic:e,state:o?\"cancel\":\"complete\",tool:t,toolEventInfo:null,type:\"create\"})}};a.on(\"complete\",r),this._operationHandle=a,o.ready&&o.focus()}async update(t,e){this.cancel(),await this._waitViewReady();const{layer:o,view:i,state:a}=this;if(!i||\"disabled\"===a)throw o||this._logMissingLayer(),y();p(i.activeTool)&&(i.activeTool=null);const r=Array.isArray(t)?t:[t];if(null==t||!r||!r.length)return void this._logError(\"sketch:missing-parameter\",\"Missing parameter 'graphics'.\");if(r.some((t=>t.layer!==o?(this._logError(\"sketch:invalid-parameter\",\"Parameter 'graphics' contains one or more graphics missing from the supplied GraphicsLayer.\"),!0):!!c(t.geometry)&&(this._logError(\"sketch:invalid-parameter\",\"Parameter 'graphics' contains one or more graphics with an unsupported geometry.\"),!0))))return;const s=await this._setupUpdateOperation(r,e);this.destroyed||c(s)||nt(s)||(x(i,this._internalGraphicsLayer),this._setUpdateOperationHandle(s,e),this.emit(\"update\",{graphics:r,state:\"start\",aborted:!1,tool:s.tool,toolEventInfo:null,type:\"update\"}))}async _updateSpatialReference(t){const e=this.view;if(e){this._beginAsyncOperation(),t=Array.isArray(t)?t:[t];for(const o of t)p(o.geometry)&&\"mesh\"!==o.geometry.type&&!k(o.geometry.spatialReference,e.spatialReference)&&(O(o.geometry.spatialReference,e.spatialReference)||E()||await S(),o.geometry=A(o.geometry,e.spatialReference));this._endAsyncOperation()}else this._logMissingView()}undo(){this.canUndo()&&this._operationHandle?.undo()}redo(){this.canRedo()&&this._operationHandle?.redo()}canUndo(){return!(!this._operationHandle||!this._operationHandle.canUndo())}canRedo(){return!(!this._operationHandle||!this._operationHandle.canRedo())}toggleUpdateTool(){this._operationHandle&&this._operationHandle.toggleTool&&this._operationHandle.toggleTool()}async _getFirstHit(t){const e=this.view;if(!e)return this._logMissingView(),null;if(\"2d\"===e.type){const o=[];e.map.allLayers.forEach((t=>{\"vector-tile\"!==t.type&&\"imagery\"!==t.type||o.push(t)}));const i=await e.hitTest(t,{exclude:o});return q(i.results)}const o=[e.map.ground];e.map.allLayers.forEach((t=>{\"integrated-mesh\"===t.type&&o.push(t)}));const i=await e.hitTest(t,{exclude:o});if(i.results.length>0){const t=i.results[0];if(p(t)&&\"graphic\"===t.type&&t.graphic&&(!i.ground.mapPoint||e.map.ground.opacity<1||i.ground.distance-d(t.distance,0)>-Math.min(3*i.ground.distance,\"global\"===e.viewingMode?T(e.renderCoordsHelper.spatialReference).radius/e.renderCoordsHelper.unitInMeters:Number.POSITIVE_INFINITY)))return t}return null}_generateViewHandles(t){return[t.on(\"immediate-click\",(async e=>{const o=\"active\"===this.state&&\"create\"===this._operationHandle?.type;if(\"disabled\"===this.state||o||!this.updateOnGraphicClick)return;this._beginAsyncOperation();const i=await e.async((()=>this._getFirstHit(Z(e))));let a=null;if(p(i)){const o=i.graphic;this.updateGraphics.includes(o)||o.layer===this.layer?(e.stopPropagation(),a=o):\"2d\"!==t.type||this._isComponentGraphic(o)||\"active\"!==this.state||this.cancel()}else\"active\"===this.state&&this.cancel();p(a)&&!this.updateGraphics.includes(a)&&await this.update([a],{...this.defaultUpdateOptions,reshapeOptions:{...this.defaultUpdateOptions.reshapeOptions}}),this._endAsyncOperation()}),P.WIDGET)]}async _setupCreateOperation(t,e){const o=this.view;if(!o)return this._logMissingView(),null;const i={hasZ:\"3d\"===o.type,...this.defaultCreateOptions,...e},a=await this._setupDrawGraphicTool(t,o,i);return c(a)?null:(o.tools.add(a),o.activeTool=a,this._setupCreateOperationHandle(a))}async _setupDrawGraphicTool(t,e,o){if(\"multipoint\"===t&&\"3d\"===e.type)return this._logError(\"sketch:create\",\"Multipoint geometries are not supported in SceneView.\"),null;if(!e)return this._logMissingView(),null;const i=\"rectangle\"!==t,a=\"rectangle\"!==t,r={view:e,mode:\"rectangle\"===t||\"circle\"===t?\"hybrid\":\"click\",...o,snapToScene:!1,geometryType:t,graphicSymbol:this._getGraphicSymbolFromTool(t),snappingManager:this._snappingManager,forceUniformSize:a,centered:i};return\"2d\"===e.type?this._makeDrawGraphicTool2D(r):this._makeDrawGraphicTool3D(r)}async _makeDrawGraphicTool2D(t){const e=await this._requireModule(import(\"../../views/2d/interactive/editingTools.js\"));return nt(e)||this.destroyed?null:new e.module.DrawGraphicTool2D({...t,activeVertexSymbol:this.activeVertexSymbol,regularVerticesSymbol:this.vertexSymbol,activeLineSymbol:this.activeLineSymbol,activeFillSymbol:ot(t.geometryType)?this.activeFillSymbol:null,tooltipOptions:this.tooltipOptions})}async _makeDrawGraphicTool3D(t){const e=await this._requireModule(import(\"../../views/3d/interactive/editingTools.js\"));if(nt(e)||this.destroyed)return null;const{elevationInfo:o}=this.layer;return new e.module.DrawGraphicTool3D({...t,elevationInfo:o,snapToScene:!p(o)||\"absolute-height\"===o.mode,labelOptions:this.labelOptions,tooltipOptions:this.tooltipOptions})}_setupCreateOperationHandle(t){const e=this.view;if(!e)return this._logMissingView(),null;let o=null;const i=t.forceUniformSize,a=t.centered,r=[e.on(\"key-down\",(e=>{if(e.key===j.pan)e.stopPropagation(),e.repeat||(t.enabled=!1);else if(e.key===j.complete)e.stopPropagation(),t.completeCreateOperation();else if(e.key!==j.vertexAdd||e.repeat)e.key===j.undo?(e.stopPropagation(),s.undo()):e.key===j.redo?(e.stopPropagation(),s.redo()):e.key!==j.constraint||\"rectangle\"!==t.geometryType&&\"circle\"!==t.geometryType||e.repeat?e.key===j.center&&(e.repeat||(t.centered=!a,e.stopPropagation())):(t.forceUniformSize=!i,e.stopPropagation());else{const o=t.drawOperation.geometryType;\"polyline\"!==o&&\"polygon\"!==o&&\"multipoint\"!==o||(e.stopPropagation(),t.drawOperation.commitStagedVertex())}}),P.WIDGET),e.on(\"key-up\",(e=>{e.key===j.pan?t.enabled=!0:e.key!==j.constraint||\"rectangle\"!==t.geometryType&&\"circle\"!==t.geometryType?e.key===j.center&&(t.centered=a,e.stopPropagation()):(t.forceUniformSize=i,e.stopPropagation())}),P.WIDGET),t.on(\"vertex-add\",(e=>{switch(o=c(o)?\"start\":\"active\",e.operation){case\"apply\":this.emit(\"create\",{graphic:l(t.graphic),state:o,tool:this.activeTool,toolEventInfo:e,type:\"create\"});break;case\"undo\":this._emitUndoEvent({graphics:[l(t.graphic)],tool:t.geometryType});break;case\"redo\":this._emitRedoEvent({graphics:[l(t.graphic)],tool:t.geometryType})}})),t.on(\"cursor-update\",(e=>{t.drawOperation.numCommittedVertices>0&&this.emit(\"create\",{graphic:l(t.graphic),state:\"active\",tool:this.activeTool,toolEventInfo:{coordinates:e.vertices[0].coordinates,type:\"cursor-update\"},type:\"create\"})})),t.on(\"vertex-remove\",(e=>{switch(e.operation){case\"apply\":this.emit(\"create\",{graphic:l(t.graphic),state:\"active\",tool:this.activeTool,toolEventInfo:e,type:\"create\"});break;case\"undo\":this._emitUndoEvent({graphics:[l(t.graphic)],tool:t.geometryType});break;case\"redo\":this._emitRedoEvent({graphics:[l(t.graphic)],tool:t.geometryType})}})),t.on(\"complete\",(t=>{this._set(\"createGraphic\",l(t.graphic)),o=\"complete\",t.aborted?s&&s.cancel():s&&s.complete()})),g((()=>this._getGraphicSymbolFromTool(t.geometryType)),(e=>{t.graphicSymbol=e}))],s=new z({activeComponent:t,tool:t.geometryType,type:\"create\",onEnd:()=>{r.forEach((t=>t.remove())),r.length=0,e.tools?.remove(t)},undo:()=>{t.canUndo&&t.undo()},redo:()=>{t.canRedo&&t.redo()},canUndo:()=>t.canUndo,canRedo:()=>t.canRedo});return s}_getGraphicSymbolFromTool(t){switch(t){case\"point\":case\"multipoint\":return this.pointSymbol;case\"polyline\":return this.polylineSymbol;case\"circle\":case\"rectangle\":case\"polygon\":return this.polygonSymbol}}async _setupUpdateOperation(t,e){const{layer:o,view:i}=this;if(!i)return this._logMissingView(),null;const a={tool:this._defaultUpdateTool,...this.defaultUpdateOptions,...e,reshapeOptions:{...this.defaultUpdateOptions.reshapeOptions,...e?.reshapeOptions}};let r=a.tool;for(const s of t)o.remove(s),o.add(s);if(\"3d\"===i.type){if(0===t.length)return null;switch(r){case\"move\":return this._setupMove3DOperation(t,a,i,r);case\"reshape\":{if(t.length>1)return this._logError(\"sketch:reshape-multiple\",\"Reshape operation does not support multiple graphics.\"),null;const e=D(t[0]);return e===H.SUPPORTED?this._setupReshape3DOperation(t[0],a,i):(this._logError(\"sketch:reshape\",`Reshape operation not supported for provided graphic(s) (${R(e)}).`),null)}case\"transform\":return this._setupGraphicTransform3DOperation(t,a,i)}}switch(r){case\"move\":return this._setupMove2DOperation(t,a,i);case\"reshape\":{if(t.length>1)return this._logError(\"sketch:reshape-multiple\",\"Reshape operation does not support multiple graphics.\"),null;const e=D(t[0]);return e===H.SUPPORTED?this._setupTransformOrReshape2DOperation(t,r,a,i):(this._logError(\"sketch:reshape\",`Reshape operation not supported for provided graphic(s) (${R(e)}).`),null)}case\"transform\":if(1===t.length){const e=u(t[0].geometry,\"type\");\"point\"!==e&&\"multipoint\"!==e||(r=\"reshape\")}return this._setupTransformOrReshape2DOperation(t,r,a,i)}}async _setupMove3DOperation(t,e,o,i,a=!1){for(const l of t){const t=I(l);if(t!==H.SUPPORTED)return this._logError(\"sketch:move\",`Move operation not supported for provided graphic(s) (${R(t)}).`),null}const r=await this._requireModule(import(\"../../views/3d/interactive/editingTools.js\"));if(nt(r))return r;const s=new r.module.GraphicMoveTool({view:o,enableZ:e.enableZ,snappingManager:this._snappingManager,tooltipOptions:this.tooltipOptions});o.tools.add(s),s.graphics.addMany(t),a||this.updateGraphics.addMany(t);const n=[],p=new N({activeComponent:s,tool:i,type:\"update\",onEnd:()=>{n.forEach((t=>t.remove())),n.length=0,o.tools?.remove(s),s.destroyed||s.destroy()},undo:()=>{it(p,this.updateGraphics.toArray()),this._emitUndoEvent({graphics:this.updateGraphics.toArray(),tool:i})},redo:()=>{at(p,this.updateGraphics.toArray()),this._emitRedoEvent({graphics:this.updateGraphics.toArray(),tool:i})},addToSelection:t=>{this.updateGraphics.push(t),s.graphics.push(t),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[t],removed:[],type:\"selection-change\"},type:\"update\"})},removeFromSelection:t=>{const e=this.updateGraphics.indexOf(t);p.history.undo.forEach((t=>t.updates.splice(e,1))),p.history.redo.forEach((t=>t.updates.splice(e,1))),this.updateGraphics.remove(t),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[],removed:[t],type:\"selection-change\"},type:\"update\"}),0!==this.updateGraphics.length?s.graphics.remove(t):p.complete()},toggleTool:async()=>{if(1!==this.updateGraphics.length||!1===e.toggleToolOnClick)return;if(\"transform\"!==i)return;const t=this.updateGraphics.getItemAt(0);if(D(t)!==H.SUPPORTED)return;const a=await this._setupReshape3DOperation(t,e,o,!0);nt(a)||(p.onEnd(),p.destroy(),this._setUpdateOperationHandle(a,e))}});return n.push(...this._getHandlesForComponent(p,e),o.on(\"immediate-click\",(t=>this._getCommonUpdateOperationClickHandlers(p,t,e)),P.WIDGET),o.on(\"key-down\",(t=>{this._getCommonUpdateOperationKeyDownHandlers(p,t)}),P.WIDGET)),p}_setupGraphicTransform3DOperation(t,e,o,i=!1){if(1===t.length&&U(t[0])===H.SUPPORTED){const a=t[0],r=a.geometry;if(p(r)&&(\"point\"===r.type||\"mesh\"===r.type))return this._setupPointTransform3DOperation(a,e,o);if(p(r)&&(\"polygon\"===r.type||\"polyline\"===r.type))return this._setupPolyTransform3DOperation(a,e,o,i)}return this._setupMove3DOperation(t,e,o,\"transform\",i)}async _setupPointTransform3DOperation(t,e,o){const i=\"transform\",{enableRotation:a,enableScaling:r,enableZ:s}=e,n=await this._requireModule(import(\"../../views/3d/interactive/editingTools.js\"));if(nt(n))return n;const p=new n.module.GraphicTransformTool({graphic:t,view:o,enableRotation:a,enableScaling:r,enableZ:s,snappingManager:this._snappingManager,tooltipOptions:this.tooltipOptions});o.tools.add(p),this.updateGraphics.add(t);const l=[],h=new N({activeComponent:p,tool:i,type:\"update\",onEnd:()=>{l.forEach((t=>t.remove())),l.length=0,o.tools?.remove(p),p.destroyed||p.destroy()},undo:()=>{it(h,this.updateGraphics.toArray()),this._emitUndoEvent({graphics:this.updateGraphics.toArray(),tool:i})},redo:()=>{at(h,this.updateGraphics.toArray()),this._emitRedoEvent({graphics:this.updateGraphics.toArray(),tool:i})},addToSelection:async t=>{this.updateGraphics.add(t),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[t],removed:[],type:\"selection-change\"},type:\"update\"});const i=await this._setupMove3DOperation(this.updateGraphics.toArray(),e,o,\"transform\",!0);nt(i)||(h.onEnd(),h.destroy(),this._setUpdateOperationHandle(i,e))},removeFromSelection:t=>{this.updateGraphics.remove(t),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[],removed:[t],type:\"selection-change\"},type:\"update\"}),h.complete()},toggleTool:()=>{}});return l.push(...this._getHandlesForComponent(h,e),o.on(\"immediate-click\",(t=>this._getCommonUpdateOperationClickHandlers(h,t,e)),P.WIDGET),o.on(\"key-down\",(t=>{this._getCommonUpdateOperationKeyDownHandlers(h,t)}),P.WIDGET)),h}async _setupPolyTransform3DOperation(t,e,o,i=!1){const a=\"transform\",{enableRotation:r,enableScaling:s,enableZ:n,preserveAspectRatio:p}=e,l=await this._requireModule(import(\"../../views/3d/interactive/editingTools.js\"));if(nt(l))return l;const h=new l.module.ExtentTransformTool({graphic:t,view:o,enableRotation:r,enableScaling:s,enableZ:n,preserveAspectRatio:p,tooltipOptions:this.tooltipOptions});o.tools.add(h),i||this.updateGraphics.add(t);const c=[],d=new N({activeComponent:h,tool:a,type:\"update\",onEnd:()=>{c.forEach((t=>t.remove())),c.length=0,o.tools?.remove(h),h.destroyed||h.destroy()},canUndo:()=>h.canUndo,undo:()=>{h.undo(),this._emitUndoEvent({graphics:this.updateGraphics.toArray(),tool:a})},canRedo:()=>h.canRedo,redo:()=>{h.redo(),this._emitRedoEvent({graphics:this.updateGraphics.toArray(),tool:a})},addToSelection:async t=>{this.updateGraphics.add(t),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[t],removed:[],type:\"selection-change\"},type:\"update\"});const i=await this._setupMove3DOperation(this.updateGraphics.toArray(),e,o,\"transform\",!0);nt(i)||(d.onEnd(),d.destroy(),this._setUpdateOperationHandle(i,e))},removeFromSelection:t=>{this.updateGraphics.remove(t),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[],removed:[t],type:\"selection-change\"},type:\"update\"}),d.complete()},toggleTool:async()=>{if(1!==this.updateGraphics.length||!1===e.toggleToolOnClick)return;const t=this.updateGraphics.getItemAt(0);if(D(t)!==H.SUPPORTED)return;const i=await this._setupReshape3DOperation(t,e,o,!0);nt(i)||(d.onEnd(),d.destroy(),this._setUpdateOperationHandle(i,e))}});return c.push(...this._getHandlesForComponent(d,e),o.on(\"immediate-click\",(t=>this._getCommonUpdateOperationClickHandlers(d,t,e)),P.WIDGET),o.on(\"key-down\",(t=>this._getCommonUpdateOperationKeyDownHandlers(d,t)),P.WIDGET),o.on(\"key-down\",(t=>{t.key!==j.constraint||t.repeat||(h.preserveAspectRatio=!h.preserveAspectRatio,t.stopPropagation())}),P.WIDGET),o.on(\"key-up\",(t=>{t.key===j.constraint&&(h.preserveAspectRatio=!h.preserveAspectRatio,t.stopPropagation())}),P.WIDGET)),d}async _setupMove2DOperation(t,e,o){const i=\"move\";this.updateGraphics.addMany(t),await this._updateSpatialReference(t);const a=await this._getGraphicMover(t,e,o);if(nt(a))return a;const r=new N({activeComponent:a,tool:i,type:\"update\",onEnd:()=>{this._displayDefaultCursor(),p.forEach((t=>t.remove())),n.forEach((t=>t.remove())),p=[],n=[],a.destroy(),this._internalGraphicsLayer?.removeMany([...this.updateGraphics.toArray()])},undo:()=>{const t=this.updateGraphics.toArray();it(r,t),r.refreshComponent(),this._emitUndoEvent({graphics:t,tool:i})},redo:()=>{const t=this.updateGraphics.toArray();at(r,t),r.refreshComponent(),this._emitRedoEvent({graphics:t,tool:i})},addToSelection:async t=>{await this._updateSpatialReference(t),this.updateGraphics.push(t),a.graphics=this.updateGraphics.toArray(),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[t],removed:[],type:\"selection-change\"},type:\"update\"})},removeFromSelection:t=>{const e=this.updateGraphics.indexOf(t);r.history.undo.forEach((t=>t.updates.splice(e,1))),r.history.redo.forEach((t=>t.updates.splice(e,1))),this.updateGraphics.remove(t);const o=this.updateGraphics.toArray();this.emit(\"update\",{graphics:o,state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[],removed:[t],type:\"selection-change\"},type:\"update\"}),0!==this.updateGraphics.length?a.graphics=o:r.complete()}});let s=!1,n=[o.on(\"immediate-click\",(t=>this._getCommonUpdateOperationClickHandlers(r,t,e)),P.WIDGET),o.on(\"key-down\",(t=>{this._getCommonUpdateOperationKeyDownHandlers(r,t),t.key!==j.constraint||t.repeat||(s=!0,a.enableMoveAllGraphics=!a.enableMoveAllGraphics)}),P.WIDGET),o.on(\"key-up\",(t=>{t.key===j.constraint&&s&&(s=!1,a.enableMoveAllGraphics=!a.enableMoveAllGraphics)}),P.WIDGET)],p=this._getHandlesForComponent(r,e);return r}async _setupReshape3DOperation(t,e,o,i=!1){const a=\"reshape\",r=await this._requireModule(import(\"../../views/3d/interactive/editingTools.js\"));if(nt(r))return r;const s=e.reshapeOptions,n=new r.module.GraphicReshapeTool({view:o,graphic:t,enableZVertex:e.enableZ&&\"move\"===s?.vertexOperation,enableZShape:e.enableZ&&\"move\"===s?.shapeOperation,enableMoveGraphic:\"move\"===s?.shapeOperation||\"move-xy\"===s?.shapeOperation,enableMidpoints:\"split\"===s?.edgeOperation,enableEdgeOffset:\"offset\"===s?.edgeOperation,snappingManager:this._snappingManager,labelOptions:this.labelOptions,tooltipOptions:this.tooltipOptions});o.tools.add(n),i||this.updateGraphics.add(t);const p=[],l=new N({activeComponent:n,tool:a,type:\"update\",onEnd:()=>{p.forEach((t=>t.remove())),p.length=0,o.tools?.remove(n),n.destroyed||n.destroy()},canUndo:()=>n.canUndo,undo:()=>{n.undo(),this._emitUndoEvent({graphics:this.updateGraphics.toArray(),tool:a})},canRedo:()=>n.canRedo,redo:()=>{n.redo(),this._emitRedoEvent({graphics:this.updateGraphics.toArray(),tool:a})},addToSelection:async t=>{this.updateGraphics.add(t),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[t],removed:[],type:\"selection-change\"},type:\"update\"});const i=await this._setupMove3DOperation(this.updateGraphics.toArray(),e,o,\"transform\",!0);nt(i)||(l.onEnd(),l.destroy(),this._setUpdateOperationHandle(i,e))},removeFromSelection:t=>{this.updateGraphics.remove(t),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[],removed:[t],type:\"selection-change\"},type:\"update\"}),l.complete()},toggleTool:async()=>{if(!1===e.toggleToolOnClick)return;const t=await this._setupGraphicTransform3DOperation(this.updateGraphics.toArray(),e,o,!0);nt(t)||(l.onEnd(),l.destroy(),this._setUpdateOperationHandle(t,e))}});return p.push(...this._getHandlesForComponent(l,e),o.on(\"immediate-click\",(t=>this._getCommonUpdateOperationClickHandlers(l,t,e)),P.WIDGET),o.on(\"key-down\",(t=>{this._getCommonUpdateOperationKeyDownHandlers(l,t)}),P.WIDGET)),l}async _setupTransformOrReshape2DOperation(t,e,o,i){this.updateGraphics.addMany(t),await this._updateSpatialReference(t);const a=\"transform\"===e?await this._getBox(t,o,i):await this._getReshape(t,o,i);if(nt(a))return a;const r=new N({activeComponent:a,type:\"update\",onEnd:()=>{n.forEach((t=>t.remove())),s.forEach((t=>t.remove())),n=[],s=[],r.activeComponent&&!r.activeComponent.destroyed&&r.activeComponent.destroy(),this._internalGraphicsLayer.removeMany(this.updateGraphics.toArray())},undo:()=>{it(r,this.updateGraphics.toArray()),r.refreshComponent(),this._emitUndoEvent({graphics:this.updateGraphics.toArray(),tool:r.tool})},redo:()=>{at(r,this.updateGraphics.toArray()),r.refreshComponent(),this._emitRedoEvent({graphics:this.updateGraphics.toArray(),tool:r.tool})},addToSelection:async t=>{let e=r.activeComponent;if(\"reshape\"===e?.type){const e=[...this.updateGraphics,t];this.updateGraphics.removeAll();const a=await this._setupMove2DOperation(e,o,i);if(nt(a))return;r.onEnd(),r.destroy(),this._setUpdateOperationHandle(a,o)}else this.updateGraphics.add(t),e.graphics=this.updateGraphics.toArray(),e.refresh(),r.resetHistory();this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[t],removed:[],type:\"selection-change\"},type:\"update\"})},removeFromSelection:async t=>{const e=this.updateGraphics.indexOf(t);r.history.undo.forEach((t=>t.updates.splice(e,1))),r.history.redo.forEach((t=>t.updates.splice(e,1))),this.updateGraphics.remove(t);const o=this.updateGraphics.toArray();if(0===o.length)r.complete();else{const t=o[0].geometry;1!==o.length||!p(t)||\"point\"!==t.type&&\"multipoint\"!==t.type?r.activeComponent.graphics=o:r.toggleTool()}this.emit(\"update\",{graphics:o,state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:[],removed:[t],type:\"selection-change\"},type:\"update\"})},toggleTool:async()=>{if(this.updateGraphics.length>1)return;const t=this.updateGraphics.getItemAt(0),e=t.geometry;if(p(e)&&(\"reshape\"===r.tool&&(\"point\"===e.type||\"multipoint\"===e.type)||\"transform\"===r.tool&&\"extent\"===e.type))return;let a=null;\"transform\"===r.tool?a=await this._getReshape([t],o,i):\"reshape\"===r.tool&&(a=await this._getBox([t],o,i)),nt(a)||(r.activeComponent?.destroy(),r.activeComponent=a,r.activeComponent&&(n.forEach((t=>t.remove())),n=this._getHandlesForComponent(r,o)))}});let s=[i.on(\"immediate-click\",(t=>this._getCommonUpdateOperationClickHandlers(r,t,o)),P.WIDGET),i.on(\"key-down\",(t=>{if(this._getCommonUpdateOperationKeyDownHandlers(r,t),t.key===j.constraint&&!t.repeat&&r){const t=r.activeComponent;t&&\"box\"===t.type&&(t.preserveAspectRatio=!t.preserveAspectRatio)}}),P.WIDGET),i.on(\"key-up\",(t=>{if(t.key===j.constraint&&r){const t=r.activeComponent;t&&\"box\"===t.type&&(t.preserveAspectRatio=!t.preserveAspectRatio)}}),P.WIDGET)],n=this._getHandlesForComponent(r,o);return r}async _getGraphicMover(t,e,o){const{enableMoveAllGraphics:i}=e,a=await this._requireModule(import(\"../../views/draw/support/GraphicMover.js\"));return nt(a)?a:new a.module.default({enableMoveAllGraphics:i,highlightsEnabled:!0,indicatorsEnabled:!1,graphics:t,view:o,callbacks:{onGraphicMoveStart:({dx:t,dy:e,graphic:o})=>{this._displayGrabbingCursor(),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{dx:t,dy:e,mover:o,type:\"move-start\"},type:\"update\"})},onGraphicMove:({dx:t,dy:e,graphic:o})=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{dx:t,dy:e,mover:o,type:\"move\"},type:\"update\"}),onGraphicMoveStop:({dx:t,dy:e,graphic:o})=>{this._displayPointerCursor(),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{dx:t,dy:e,mover:o,type:\"move-stop\"},type:\"update\"})},onGraphicPointerOver:()=>this._displayPointerCursor(),onGraphicPointerOut:()=>this._displayDefaultCursor()}})}async _getBox(t,e,o){const{enableRotation:i,enableScaling:a,preserveAspectRatio:r}=e,s=await this._requireModule(import(\"../../views/draw/support/Box.js\"));return nt(s)?s:new s.module.default({graphics:t,enableRotation:i,enableScaling:a,preserveAspectRatio:r,layer:this._internalGraphicsLayer,view:o,tooltipOptions:this.tooltipOptions,callbacks:{onMoveStart:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onMove:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onMoveStop:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onScaleStart:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onScale:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onScaleStop:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onRotateStart:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onRotate:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onRotateStop:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"})}})}async _getReshape(t,e,o){const i=\"split\"===e.reshapeOptions?.edgeOperation,a=\"move\"===e.reshapeOptions?.shapeOperation,r=await this._requireModule(import(\"../../views/draw/support/Reshape.js\"));return nt(r)?r:new r.module.default({enableMidpoints:i,enableMovement:a,graphic:t[0],layer:this._internalGraphicsLayer,snappingManager:this._snappingManager,tooltipOptions:this.tooltipOptions,view:o,callbacks:{onReshapeStart:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onReshape:t=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{...t},type:\"update\"}),onReshapeStop:({mover:t,type:e})=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t,type:e},type:\"update\"}),onMoveStart:({dx:t,dy:e,mover:o,type:i})=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{dx:t,dy:e,mover:o,type:i},type:\"update\"}),onMove:({dx:t,dy:e,mover:o,type:i})=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{dx:t,dy:e,mover:o,type:i},type:\"update\"}),onMoveStop:({dx:t,dy:e,mover:o,type:i})=>this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{dx:t,dy:e,mover:o,type:i},type:\"update\"}),onVertexAdd:({added:t,type:e,vertices:o})=>{const i=t.map((t=>C(t.geometry)));this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{added:i,vertices:o,type:e},type:\"update\"})},onVertexRemove:({removed:t,type:e,vertices:o})=>{const i=t.map((t=>C(t.geometry)));this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{removed:i,vertices:o,type:e},type:\"update\"})}}})}_getHandlesForComponent(t,e){const o=t.activeComponent;if(!o)return[];switch(o.type){case\"graphic-mover\":return[o.on(\"graphic-click\",(({graphic:e,viewEvent:o})=>{o.native?.shiftKey&&(o.stopPropagation(),t.removeFromSelection(e))})),o.on(\"graphic-move-start\",(e=>t.addToHistory(st(e.allGraphics))))];case\"box\":return[o.on(\"graphic-click\",(o=>this._onTransformOrReshape2DGraphicClick(t,e,o))),o.on(\"move-start\",(e=>t.addToHistory(st(e.graphics)))),o.on(\"rotate-start\",(e=>t.addToHistory(st(e.graphics)))),o.on(\"scale-start\",(e=>t.addToHistory(st(e.graphics))))];case\"reshape\":return[o.on(\"graphic-click\",(o=>this._onTransformOrReshape2DGraphicClick(t,e,o))),o.on(\"move-start\",(e=>t.addToHistory(st([e.mover])))),o.on(\"reshape-start\",(e=>t.addToHistory(st([e.graphic])))),o.on(\"vertex-add\",(e=>t.addToHistory(st([e.oldGraphic])))),o.on(\"vertex-remove\",(e=>t.addToHistory(st([e.oldGraphic]))))];case\"move-3d\":return[o.on(\"graphic-move-start\",(e=>{t.addToHistory(st(e.allGraphics)),this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{dx:0,dy:0,mover:e.allGraphics.length>0?e.allGraphics[0]:null,type:\"move-start\"},type:\"update\"})})),o.on(\"graphic-move\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{dx:t.dx,dy:t.dy,mover:t.allGraphics.length>0?t.allGraphics[0]:null,type:\"move\"},type:\"update\"})})),o.on(\"graphic-move-stop\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{dx:0,dy:0,mover:t.allGraphics.length>0?t.allGraphics[0]:null,type:\"move-stop\"},type:\"update\"})})),o.on(\"immediate-click\",(o=>{o.shiftKey?this._toggleSelection([o.graphic],t,e):t.toggleTool()}))];case\"transform-3d\":return[o.on(\"record-undo\",(({record:e})=>{t.addToHistory({updates:[e]})})),o.on(\"graphic-translate-start\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t.graphic,dx:t.dxScreen,dy:t.dyScreen,type:\"move-start\"},type:\"update\"})})),o.on(\"graphic-translate-stop\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t.graphic,dx:t.dxScreen,dy:t.dyScreen,type:\"move-stop\"},type:\"update\"})})),o.on(\"graphic-rotate-start\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t.graphic,angle:t.angle,type:\"rotate-start\"},type:\"update\"})})),o.on(\"graphic-rotate-stop\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t.graphic,angle:t.angle,type:\"rotate-stop\"},type:\"update\"})})),o.on(\"graphic-scale-start\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t.graphic,xScale:t.xScale,yScale:t.yScale,type:\"scale-start\"},type:\"update\"})})),o.on(\"graphic-scale-stop\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t.graphic,xScale:t.xScale,yScale:t.yScale,type:\"scale-stop\"},type:\"update\"})})),o.on(\"graphic-translate\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t.graphic,dx:t.dxScreen,dy:t.dyScreen,type:\"move\"},type:\"update\"})})),o.on(\"graphic-rotate\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t.graphic,angle:t.angle,type:\"rotate\"},type:\"update\"})})),o.on(\"graphic-scale\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:{mover:t.graphic,xScale:t.xScale,yScale:t.yScale,type:\"scale\"},type:\"update\"})})),o.on(\"immediate-click\",(o=>{o.shiftKey?this._toggleSelection([o.graphic],t,e):t.toggleTool()}))];case\"reshape-3d\":return[o.on(\"reshape\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:t,type:\"update\"})})),o.on(\"move\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:t,type:\"update\"})})),o.on(\"vertex-add\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:t,type:\"update\"})})),o.on(\"vertex-remove\",(t=>{this.emit(\"update\",{graphics:this.updateGraphics.toArray(),state:\"active\",aborted:!1,tool:this.activeTool,toolEventInfo:t,type:\"update\"})})),o.on(\"immediate-click\",(o=>{o.shiftKey?this._toggleSelection([o.graphic],t,e):t.toggleTool()}))]}}_onTransformOrReshape2DGraphicClick(t,e,o){const{graphic:i,viewEvent:a}=o;return a.native?.shiftKey&&i.layer===this.layer?(a.stopPropagation(),t.removeFromSelection(i)):e.toggleToolOnClick?(a.stopPropagation(),t.toggleTool()):void 0}_setUpdateOperationHandle(t,e){this._operationHandle=t;const o=this.view?.map;this._disablePopup(e);const i=()=>{if(t===this._operationHandle){const i=this.updateGraphics.toArray(),a=this._operationHandle.tool;this._operationHandle.destroy(),this._operationHandle=null,this._internalGraphicsLayer.removeMany(this.updateGraphics.toArray()),this.updateGraphics.removeAll(),o&&o.remove(this._internalGraphicsLayer),this._restorePopup(e),this.emit(\"update\",{graphics:i,state:\"complete\",aborted:t.cancelled,tool:a,toolEventInfo:null,type:\"update\"})}};t.on(\"complete\",i)}async _getCommonUpdateOperationClickHandlers(t,e,o){const i=Z(e),a=await e.async((()=>this._getFirstHit(i)));if(c(a))return void t.complete();if(e.native.shiftKey&&this._toggleSelection([a.graphic],t,o))return void e.stopPropagation();this.updateGraphics.includes(a.graphic)?e.stopPropagation():t.complete()}_toggleSelection(t,e,o){const i=!!o.multipleSelectionEnabled;return t.some((t=>null!=t&&(!(!i||t.layer!==this.layer)&&(this.updateGraphics.includes(t)?e.removeFromSelection(t):e.addToSelection(t),!0))))}_getCommonUpdateOperationKeyDownHandlers(t,e){if(!t)return;const o=e.key;o===j.undo&&t.canUndo()?(e.stopPropagation(),t.undo()):o===j.redo&&t.canRedo()?(e.stopPropagation(),t.redo()):o===j.cancel?(e.stopPropagation(),t.cancel()):this.allowDeleteKey&&j.delete.includes(o)&&this._onDeleteKey(e)}_onDeleteKey(t){if(!this._operationHandle||\"update\"!==this._operationHandle.type)return;const e=this.activeComponent,o=this.updateGraphics.toArray();c(e)||\"reshape-3d\"===e.type||(\"reshape\"!==e.type||1===o.length&&\"point\"===u(o[0].geometry,\"type\"))&&(t.stopPropagation(),this.delete())}_removeDefaultLayer(){this._internalGraphicsLayer&&(this.view?.map?.remove(this._internalGraphicsLayer),this._internalGraphicsLayer=n(this._internalGraphicsLayer))}_isComponentGraphic(t){const{activeComponent:e}=this;return!(!t||c(e))&&(t.attributes&&t.attributes.esriSketchTool||\"draw-2d\"===e.type&&e.graphic===t||(\"box\"===e.type||\"reshape\"===e.type)&&e.isUIGraphic(t))}_displayPointerCursor(){this.view&&this.view.container&&\"pointer\"!==this.view.cursor&&(this.view.cursor=\"pointer\")}_displayGrabbingCursor(){this.view&&this.view.container&&\"grabbing\"!==this.view.cursor&&(this.view.cursor=\"grabbing\")}_displayDefaultCursor(){this.view&&this.view.container&&null!==this.view.cursor&&(this.view.cursor=null)}_logError(t,e,o){s.getLogger(this.declaredClass).error(new i(t,e,o))}async _requireModule(t){const e=new AbortController;this._moduleLoaderAbortController=e;const o=await t;return this._moduleLoaderAbortController!==e||e.signal.aborted?{requireError:\"aborted\"}:{module:o}}_emitUndoEvent(t){this.emit(\"undo\",{...t,type:\"undo\"})}_emitRedoEvent(t){this.emit(\"redo\",{...t,type:\"redo\"})}_emitDeleteEvent(t){this.emit(\"delete\",{...t,type:\"delete\"})}get test(){return{operationHandle:this._operationHandle,defaultUpdateOptions:Q}}wait(){return _((()=>!this.updating))}_beginAsyncOperation(){this._numUpdating+=1,this.notifyChange(\"updating\")}_endAsyncOperation(){this._numUpdating-=1,this.notifyChange(\"updating\")}_disablePopupEnabled(t){return\"3d\"!==this.view?.type||this.updateOnGraphicClick||(l(t)?.toggleToolOnClick??!1)}_disablePopup(t){if(!this._disablePopupEnabled(t))return;const e=this.view?.popup;e&&c(this._originalAutoOpenEnabled)&&(this._originalAutoOpenEnabled=e.autoOpenEnabled,e.autoOpenEnabled=!1)}_restorePopup(t){if(!this._disablePopupEnabled(t))return;const e=this.view?.popup;e&&p(this._originalAutoOpenEnabled)&&(e.autoOpenEnabled=this._originalAutoOpenEnabled,this._originalAutoOpenEnabled=null)}async _waitViewReady(){const t=this.view;t?(h(this._viewReadyAbortController),this._viewReadyAbortController=new AbortController,await m(_((()=>t?.ready)),this._viewReadyAbortController.signal)):this._logMissingView()}_logMissingView(){this._logError(\"sketch:missing-property\",et(\"view\"))}_logMissingLayer(){this._logError(tt,et(\"layer\"))}};t([G()],X.prototype,\"updating\",null),t([G()],X.prototype,\"_operationHandle\",void 0),t([G({readOnly:!0})],X.prototype,\"activeTool\",null),t([G()],X.prototype,\"activeFillSymbol\",void 0),t([G()],X.prototype,\"activeLineSymbol\",void 0),t([G()],X.prototype,\"activeVertexSymbol\",void 0),t([G()],X.prototype,\"allowDeleteKey\",void 0),t([G({readOnly:!0})],X.prototype,\"createGraphic\",null),t([G()],X.prototype,\"defaultCreateOptions\",null),t([G()],X.prototype,\"defaultUpdateOptions\",null),t([G({type:L,nonNullable:!0})],X.prototype,\"labelOptions\",void 0),t([G()],X.prototype,\"layer\",void 0),t([G({types:e})],X.prototype,\"pointSymbol\",void 0),t([G({types:e})],X.prototype,\"polygonSymbol\",void 0),t([G({types:e})],X.prototype,\"polylineSymbol\",void 0),t([G({type:W,nonNullable:!0})],X.prototype,\"snappingOptions\",null),t([G()],X.prototype,\"_snappingManager\",void 0),t([G({readOnly:!0})],X.prototype,\"state\",null),t([G({type:F,nonNullable:!0})],X.prototype,\"tooltipOptions\",void 0),t([G({readOnly:!0})],X.prototype,\"updateGraphics\",void 0),t([G()],X.prototype,\"updateOnGraphicClick\",void 0),t([G({types:e})],X.prototype,\"updatePointSymbol\",void 0),t([G({types:e})],X.prototype,\"updatePolygonSymbol\",void 0),t([G({types:e})],X.prototype,\"updatePolylineSymbol\",void 0),t([G({types:e})],X.prototype,\"vertexSymbol\",void 0),t([G({value:null})],X.prototype,\"view\",null),X=t([b(\"esri.widgets.Sketch.SketchViewModel\")],X);const tt=\"sketch:missing-property\",et=t=>`Property '${t}' is missing on SketchViewModel.`;function ot(t){return\"polygon\"===t||\"rectangle\"===t||\"circle\"===t}function it(t,e){rt(\"undo\",t.history.undo,t.history.redo,e)}function at(t,e){rt(\"redo\",t.history.redo,t.history.undo,e)}function rt(t,e,o,i){const a=e.pop();if(!a)return;const r=a.updates,s=[];i.forEach(((e,o)=>{const i=r[o];null!=i&&(\"geometry\"in i&&p(i.geometry)&&(s.push({geometry:e.geometry}),e.geometry=i.geometry),\"symbol\"in i&&p(i.symbol)&&(s.push({symbol:e.symbol}),e.symbol=i.symbol),\"undo\"in i&&(s.push(i),i[t](e)))})),o.push({updates:s})}function st(t){return{updates:t.map((t=>({geometry:t.geometry})))}}function nt(t){return\"requireError\"in t&&\"aborted\"===t.requireError}const pt=X;export{pt as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIiT,SAASA,GAAEA,IAAE;AAJ9T;AAI+T,MAAG,iBAAa,KAAAA,GAAE,UAAF,mBAAS,MAAK,QAAOC,GAAE;AAAuB,MAAG,EAAED,GAAE,QAAQ,EAAE,QAAOC,GAAE;AAAiB,UAAOD,GAAE,SAAS,MAAK;AAAA,IAAC,KAAI;AAAQ;AAAA,IAAM,KAAI;AAAA,IAAU,KAAI;AAAA,IAAW,KAAI;AAAA,IAAa,KAAI;AAAA,IAAS,KAAI;AAAO,aAAOC,GAAE;AAAA,IAAU;AAAQ,aAAOA,GAAE;AAAA,EAAyB;AAAC,QAAMC,MAAE,EAAEF,GAAE,MAAM,KAAG,eAAaA,GAAE,OAAO,QAAMA,GAAE,OAAO;AAAa,MAAG,EAAEE,OAAGA,IAAE,SAAO,KAAGA,IAAE,KAAM,CAAAC,OAAG,aAAWA,GAAE,IAAK,GAAG,QAAOF,GAAE;AAAwB,SAAM,oBAAkBG,GAAEJ,EAAC,KAAG,EAAEA,EAAC,IAAEC,GAAE,6BAA2BA,GAAE;AAAS;;;ACAtgB,IAAMI,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAY,EAAC,aAAYC,IAAE,YAAWC,IAAE,gBAAeC,IAAE,aAAYC,IAAE,iBAAgBL,IAAE,UAASM,KAAE,UAASC,GAAC,GAAE;AAAC,UAAML,IAAEC,IAAEI,IAAEC,GAAE,IAAI,GAAE,KAAK,iBAAeJ,IAAE,KAAK,cAAYC,IAAE,KAAK,kBAAgBL,IAAE,KAAK,WAASM;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,UAAML,KAAE,KAAK,gBAAeE,KAAE,KAAK,oBAAkBG,GAAE,SAAO,KAAK,cAAY,KAAK,aAAYN,KAAE,KAAK,oBAAkBM,GAAE,SAAO,KAAK,cAAY,KAAK;AAAY,WAAM,CAAC,IAAIA,GAAEG,GAAE,QAAON,IAAEH,IAAE,KAAK,UAAS,KAAK,MAAM,GAAE,IAAIM,GAAEG,GAAE,WAAUR,IAAEE,IAAE,KAAK,UAAS,KAAK,MAAM,GAAE,IAAIE,GAAE,KAAK,gBAAeF,IAAEH,IAAE,KAAK,UAAS,KAAK,MAAM,CAAC;AAAA,EAAC;AAAC;AAAC,IAAIM;AAAE,CAAC,SAASL,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE;AAAQ,EAAEK,OAAIA,KAAE,CAAC,EAAE;;;ACAmxB,IAAI,IAAE,cAAcI,GAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,EAAE,KAAK,iBAAiB,CAAC,EAAC,gBAAeC,GAAC,MAAIA,GAAE,QAAS,KAAG,KAAK,gBAAgB;AAAA,EAAQ;AAAA,EAAC,IAAI,kBAAiB;AAAC,UAAMA,KAAE,KAAK,KAAK,iBAAiB,KAAG,oBAAI,OAAIC,KAAE,oBAAI;AAAI,QAAG,EAAE,KAAK,OAAO,KAAG,EAAE,KAAK,QAAQ,cAAc,EAAE,YAAUC,MAAK,KAAK,QAAQ,eAAe,OAAM;AAAC,YAAMC,KAAED,GAAE,MAAM,KAAIE,MAAEJ,GAAE,IAAIG,EAAC;AAAE,UAAGC,KAAE;AAAC,QAAAJ,GAAE,OAAOG,EAAC,GAAEF,GAAE,IAAIE,IAAEC,GAAC;AAAE;AAAA,MAAQ;AAAC,UAAG,CAACF,GAAE,MAAM,QAAO;AAAC,aAAK,gBAAgB,WAAWA,GAAE,MAAM,KAAK,CAAC;AAAE;AAAA,MAAQ;AAAC,YAAMG,KAAE,KAAK,kBAAkBH,EAAC;AAAE,QAAEG,EAAC,KAAGJ,GAAE,IAAIE,IAAEE,EAAC;AAAA,IAAC;AAAC,eAAS,CAAC,EAACH,EAAC,KAAIF,GAAE,CAAAE,GAAE,QAAQ;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,MAAK,KAAK,UAAQM,GAAE,SAAQ,KAAK,iBAAe,EAAC,gBAAe,EAAC,QAAO,MAAK,QAAO,KAAI,GAAE,mBAAkB,EAAC,QAAO,MAAK,QAAO,KAAI,GAAE,UAAS,EAAC,QAAO,MAAK,QAAO,KAAI,GAAE,OAAM,EAAC,QAAO,MAAK,QAAO,KAAI,GAAE,OAAM,EAAC,QAAO,MAAK,QAAO,KAAI,EAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,gBAAgB,IAAK,MAAI,KAAK,iBAAkB,MAAI,KAAK,aAAa,UAAU,GAAG,CAAC,GAAE,EAAE,KAAK,IAAI,KAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,KAAK,GAAG,oBAAoB,CAAAN,OAAG,KAAK,iBAAiBA,GAAE,OAAMA,GAAE,SAAS,CAAE,GAAE,KAAK,KAAK,GAAG,qBAAqB,CAAAA,OAAG,KAAK,iBAAiBA,GAAE,OAAM,IAAI,CAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEC,IAAE;AAAC,eAAS,CAAC,EAACC,EAAC,KAAI,KAAK,gBAAgB,CAAAA,GAAE,eAAe,YAAY,UAAQF,OAAIE,GAAE,YAAUD;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,KAAK,WAAU,IAAI;AAAE,eAAS,CAAC,EAACD,EAAC,KAAI,KAAK,gBAAgB,CAAAA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBA,IAAEC,IAAEC,IAAEC,IAAE;AAJzgG;AAI0gG,QAAG,EAAEF,KAAE,KAAK,YAAU,EAAE,KAAK,OAAO,KAAG,CAAC,KAAK,QAAQ,wBAAwB,QAAM,CAAC;AAAE,UAAMM,KAAE,CAAC,GAAEC,KAAE,KAAK,qCAAqCR,IAAEE,EAAC,GAAEH,KAAE,EAAC,UAASS,IAAE,QAAK,KAAAR,GAAE,KAAK,IAAI,MAAX,mBAAc,SAAM,MAAK,OAAMA,IAAE,kBAAiBE,GAAE,kBAAiB,OAAM,KAAK,OAAM;AAAE,eAAS,CAAC,EAAC,EAAC,gBAAeE,KAAE,WAAUC,GAAC,CAAC,KAAI,KAAK,gBAAgB,EAACD,IAAE,YAAY,WAAS,EAAEC,EAAC,KAAGA,GAAE,aAAWE,GAAE,KAAKH,IAAE,gBAAgBL,IAAEI,EAAC,EAAE,KAAM,CAAAH,OAAGA,GAAE,OAAQ,CAAAA,OAAG,CAAC,KAAK,qBAAqBI,KAAEJ,IAAEE,GAAE,cAAc,CAAE,CAAE,CAAC;AAAE,UAAMO,MAAG,MAAM,EAAEF,EAAC,GAAG,KAAK;AAAE,WAAO,KAAK,yBAAyBE,IAAET,IAAEQ,IAAEN,EAAC,GAAE,EAAEC,EAAC,GAAEJ,GAAEC,IAAES,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,yBAAyBT,IAAEC,IAAEC,IAAEC,IAAE;AAJvlH;AAIwlH,UAAMC,MAAE,EAAED,GAAE,YAAY,KAAE,WAAAA,GAAE,aAAa,cAAf,mBAA0B,gBAA1B,mBAAuC,MAAI,EAAEA,GAAE,sBAAsB,KAAG,cAAYA,GAAE,uBAAuB,KAAK,QAAK,KAAAH,IAAE,KAAAG,GAAE,uBAAuB,KAAK,WAAW,CAAC,MAA1C,mBAA6C,gBAAgB,MAA/D,mBAAkE,MAAI,MAAKO,KAAE,EAAEP,GAAE,YAAY,KAAE,WAAAA,GAAE,aAAa,aAAf,mBAAyB,eAAzB,mBAAqC,MAAI,EAAEA,GAAE,sBAAsB,KAAE,KAAAH,IAAE,KAAAG,GAAE,uBAAuB,KAAK,WAAW,CAAC,MAA1C,mBAA6C,eAAe,MAA9D,mBAAiE,MAAI,MAAK,EAAC,MAAKQ,GAAC,IAAE,MAAKJ,KAAEI,GAAEP,KAAEO,IAAER,EAAC,GAAEK,KAAEG,GAAED,IAAEC,IAAER,EAAC,GAAEJ,KAAEC,GAAE;AAAO,aAAQY,KAAE,GAAEA,KAAEb,IAAEa,KAAI,MAAK,wBAAwBZ,GAAEY,EAAC,GAAEJ,IAAEP,IAAEC,IAAEF,EAAC,GAAE,KAAK,wBAAwBA,GAAEY,EAAC,GAAEL,IAAEN,IAAEC,IAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAEC,IAAEC,IAAEC,IAAES,IAAE;AAAC,QAAG,EAAEX,EAAC,KAAG,CAACY,GAAEb,EAAC,EAAE;AAAO,UAAMK,KAAEL,GAAE,WAAW,UAAUC,EAAC,GAAES,MAAGL,GAAE,CAAC,IAAEH,GAAE,CAAC,KAAGC,GAAE,GAAEQ,MAAGN,GAAE,CAAC,IAAEH,GAAE,CAAC,KAAGC,GAAE,GAAE,EAAC,OAAMI,IAAE,KAAIC,GAAC,IAAER,GAAE;AAAW,QAAGU,KAAEA,KAAEC,KAAEA,MAAG,GAAE;AAAC,YAAMT,KAAE,IAAIU,GAAE,EAAC,aAAYP,IAAE,aAAYJ,IAAE,iBAAgBG,GAAE,MAAK,gBAAe,EAAEC,IAAEE,EAAC,IAAE,EAAEF,IAAEG,EAAC,IAAED,KAAEC,IAAE,YAAW,IAAI,EAAEP,IAAEI,EAAC,GAAE,UAASL,GAAE,UAAS,UAASA,GAAE,SAAQ,CAAC;AAAE,MAAAY,GAAE,KAAKV,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,qCAAqCF,IAAEC,IAAE;AAAC,QAAIC,KAAE,EAAE,KAAK,OAAO,IAAE,KAAK,QAAQ,YAAU,YAAUD,GAAE,UAAQ,KAAK,QAAQ,6BAA2B,KAAG;AAAE,WAAO,EAAE,KAAK,IAAI,IAAE,EAAC,GAAEC,IAAE,GAAEA,IAAE,GAAEA,IAAE,UAASA,GAAC,IAAE,SAAO,KAAK,KAAK,QAAMA,MAAG,KAAK,KAAK,YAAW,EAAC,GAAEA,IAAE,GAAEA,IAAE,GAAEA,IAAE,UAASA,GAAC,KAAG,KAAK,uCAAuCF,IAAEE,IAAE,KAAK,MAAKD,EAAC;AAAA,EAAC;AAAA,EAAC,uCAAuCD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,kBAAiBS,GAAC,IAAET;AAAE,IAAAD,GAAE,mBAAmB,eAAeF,IAAEY,IAAEE,EAAC;AAAE,UAAMV,MAAEF,GAAE,MAAM,OAAO,yBAAyBY,EAAC,GAAET,KAAED,MAAEF,GAAE,mBAAmB,eAAaA,GAAE,gBAAgB,cAAaQ,KAAET,KAAEI,IAAEM,KAAEJ,GAAEP,IAAEY,IAAEN,IAAEJ,EAAC,GAAEK,KAAEI,KAAEI,GAAEJ,IAAEX,IAAEK,IAAE,GAAE,GAAEH,IAAEC,EAAC,IAAE,GAAEK,KAAEG,KAAEI,GAAEJ,IAAEX,IAAE,GAAEK,IAAE,GAAEH,IAAEC,EAAC,IAAE,GAAEJ,KAAEY,KAAEI,GAAEJ,IAAEX,IAAE,GAAE,GAAEK,IAAEH,IAAEC,EAAC,IAAE;AAAE,WAAM,EAAC,GAAE,MAAII,KAAE,IAAEG,KAAEH,IAAE,GAAE,MAAIC,KAAE,IAAEE,KAAEF,IAAE,GAAE,MAAIT,KAAE,IAAEW,KAAEX,IAAE,UAASK,MAAEH,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,EAAE,OAAK,EAAE;AAAA,EAAM;AAAA,EAAC,qBAAqBD,IAAEC,IAAEC,IAAE;AAAC,QAAG,EAAEA,EAAC,EAAE,QAAM;AAAG,UAAMC,KAAE,KAAK,sBAAsBF,EAAC;AAAE,QAAG,EAAEE,EAAC,EAAE,QAAM;AAAG,UAAMS,KAAEZ,GAAE,YAAY;AAAM,WAAM,eAAaY,GAAE,OAAKV,GAAE,QAAMC,KAAED,GAAE,gBAAcU,OAAI,EAAE,CAACV,GAAE,cAAY,EAAE,mBAAkBU,QAAKV,GAAE,WAAWU,GAAE,aAAa,MAAIT;AAAA,EAAE;AAAA,EAAC,sBAAsBH,IAAE;AAAC,WAAOA,cAAaI,KAAEJ,GAAE,WAAS;AAAA,EAAI;AAAA,EAAC,kBAAkBA,IAAE;AAAC,UAAMC,KAAE,KAAK,iCAAiCD,EAAC;AAAE,QAAG,EAAEC,EAAC,EAAE,QAAO;AAAK,QAAG,aAAYA,GAAE,QAAO,KAAK,gBAAgB,WAAWA,GAAE,QAAQ,KAAM,MAAI;AAAC,WAAK,aAAW,KAAK,aAAa,iBAAiB;AAAA,IAAC,CAAE,CAAC,GAAE;AAAK,UAAMC,KAAE,EAAE,KAAK,IAAI,IAAE,KAAK,KAAK,cAAc,KAAM,CAAAD,OAAGA,GAAE,UAAQD,GAAE,KAAM,IAAE;AAAK,WAAO,IAAI,EAAEC,GAAE,QAAOC,EAAC;AAAA,EAAC;AAAA,EAAC,iCAAiCF,IAAE;AAAC,YAAOA,GAAE,MAAM,MAAK;AAAA,MAAC,KAAI;AAAA,MAAU,KAAI;AAAA,MAAU,KAAI;AAAA,MAAM,KAAI;AAAA,MAAmB,KAAI;AAAA,MAAgB,KAAI;AAAM,eAAO,KAAK,yCAAyCA,EAAC;AAAA,MAAE,KAAI;AAAW,eAAO,KAAK,0CAA0CA,EAAC;AAAA,MAAE,KAAI;AAAY,eAAO,KAAK,0CAA0CA,EAAC;AAAA,MAAE,KAAI;AAAA,MAAQ,KAAI;AAAiB,eAAO,KAAK,uCAAuCA,EAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,uCAAuCA,IAAE;AAAC,UAAK,EAAC,MAAKC,GAAC,IAAE;AAAK,QAAG,EAAEA,EAAC,KAAG,SAAOA,GAAE,KAAK,QAAO;AAAK,UAAMC,KAAE,KAAK,iBAAiB,OAAO;AAAE,WAAO,EAAEA,GAAE,MAAM,IAAE,EAAC,QAAO,IAAIA,GAAE,OAAO,yBAAyB,EAAC,aAAYF,IAAE,MAAKC,GAAC,CAAC,EAAC,IAAE,EAAC,SAAQC,GAAE,OAAM;AAAA,EAAC;AAAA,EAAC,yCAAyCF,IAAE;AAJ9iN;AAI+iN,aAAO,KAAAA,GAAE,MAAM,WAAR,mBAAgB,MAAK;AAAA,MAAC,KAAI;AAAA,MAAgB,KAAI,oBAAmB;AAAC,cAAMC,KAAE,KAAK,iBAAiB,gBAAgB;AAAE,eAAO,EAAEA,GAAE,MAAM,IAAE,EAAC,QAAO,IAAIA,GAAE,OAAO,6BAA6B,EAAC,kBAAiB,KAAK,kBAAiB,MAAK,KAAK,MAAK,aAAYD,GAAC,CAAC,EAAC,IAAE,EAAC,SAAQC,GAAE,OAAM;AAAA,MAAC;AAAA,MAAC,KAAI;AAAA,MAAS,KAAI;AAAA,MAAM,KAAI;AAAA,MAAU,KAAI,OAAM;AAAC,YAAG,WAASD,GAAE,MAAM,aAAa,QAAO;AAAK,cAAMC,KAAE,KAAK,iBAAiB,mBAAmB;AAAE,eAAO,EAAEA,GAAE,MAAM,IAAE,EAAC,QAAO,IAAIA,GAAE,OAAO,gCAAgC,EAAC,aAAYD,IAAE,MAAK,KAAK,KAAI,CAAC,EAAC,IAAE,EAAC,SAAQC,GAAE,OAAM;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,0CAA0CD,IAAE;AAAC,UAAMC,KAAE,KAAK,iBAAiB,UAAU;AAAE,WAAO,EAAEA,GAAE,MAAM,IAAE,EAAC,QAAO,IAAIA,GAAE,OAAO,uBAAuB,EAAC,mBAAkB,MAAI,CAACD,GAAE,KAAK,GAAE,kBAAiB,KAAK,kBAAiB,MAAK,KAAK,MAAK,aAAYA,GAAC,CAAC,EAAC,IAAE,EAAC,SAAQC,GAAE,OAAM;AAAA,EAAC;AAAA,EAAC,0CAA0CD,IAAE;AAAC,UAAMC,KAAE,KAAK,iBAAiB,OAAO;AAAE,WAAO,EAAEA,GAAE,MAAM,IAAE,EAAC,QAAO,IAAIA,GAAE,OAAO,uBAAuB,EAAC,mBAAkB,MAAI,EAAED,GAAE,MAAM,SAAS,IAAEA,GAAE,MAAM,UAAU,QAAQ,IAAE,CAAC,GAAE,kBAAiB,KAAK,kBAAiB,MAAK,KAAK,MAAK,aAAYA,GAAC,CAAC,EAAC,IAAE,EAAC,SAAQC,GAAE,OAAM;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAE;AAAC,UAAMC,KAAE,KAAK,eAAeD,EAAC;AAAE,QAAG,EAAEC,GAAE,MAAM,GAAE;AAAC,YAAMC,KAAE,KAAK,kBAAkBF,EAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,QAAAC,GAAE,SAAOD;AAAA,MAAC,CAAE;AAAE,aAAOC,GAAE,SAAOC,IAAE,EAAC,QAAOD,GAAE,QAAO,QAAOC,GAAC;AAAA,IAAC;AAAC,WAAM,EAAC,QAAOD,GAAE,QAAO,QAAOA,GAAE,OAAM;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAgB,YAAOD,IAAE;AAAA,MAAC,KAAI;AAAiB,eAAOC,GAAE,WAAW,OAAO,4CAAkD,CAAC;AAAA,MAAE,KAAI;AAAoB,eAAOA,GAAE,WAAW,OAAO,+CAAqD,CAAC;AAAA,MAAE,KAAI;AAAA,MAAW,KAAI;AAAQ,eAAOA,GAAE,WAAW,OAAO,sCAA4C,CAAC;AAAA,MAAE,KAAI;AAAQ,eAAOA,GAAE,WAAW,OAAO,wCAA8C,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,IAAE,EAAE,CAACI,GAAE,uDAAuD,CAAC,GAAE,CAAC;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYL,IAAEC,IAAE;AAAC,SAAK,iBAAeD,IAAE,KAAK,YAAUC,IAAE,KAAK,UAAQ,IAAIA;AAAE,UAAME,KAAE,KAAK,eAAe,YAAY;AAAM,QAAG,aAAYA,IAAE;AAAC,YAAMF,KAAEE;AAAE,WAAK,QAAQ,IAAIF,GAAE,GAAG,WAAW,MAAID,GAAE,QAAQ,CAAE,CAAC;AAAA,IAAC;AAAC,SAAK,QAAQ,IAAI,CAACS,GAAG,MAAIT,GAAE,UAAW,CAAAC,OAAGD,GAAE,YAAY,WAASC,IAAGe,EAAC,GAAEP,GAAG,MAAIT,GAAE,cAAe,CAAAC,OAAGD,GAAE,YAAY,eAAaC,IAAGe,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,eAAe,QAAQ,GAAE,KAAK,QAAQ,QAAQ;AAAA,EAAC;AAAC;AAAC,SAASH,GAAEb,IAAE;AAAC,UAAOA,cAAaE,MAAGF,cAAaG,OAAI,CAACc,GAAEjB,EAAC;AAAC;AAAC,SAASiB,GAAE,EAAC,YAAW,EAAC,OAAMjB,IAAE,KAAIC,GAAC,EAAC,GAAE;AAAC,QAAMC,KAAEM,GAAER,IAAEC,EAAC,GAAEE,KAAE,EAAEH,IAAEC,EAAC;AAAE,SAAOC,KAAEG,GAAE,KAAGF,KAAED,KAAEgB;AAAC;AAAC,SAASH,GAAEf,IAAEC,IAAEC,IAAEC,IAAES,IAAER,KAAE,EAAC,kBAAiBC,GAAC,GAAE;AAAC,QAAMK,KAAER,GAAE,GAAED,EAAC;AAAE,EAAAS,GAAE,CAAC,KAAGR,IAAEQ,GAAE,CAAC,KAAGP,IAAEO,GAAE,CAAC,KAAGE;AAAE,QAAMD,KAAEJ,GAAEG,IAAEL,IAAEC,IAAEF,GAAC;AAAE,SAAOO,KAAE,EAAEA,IAAEX,EAAC,IAAE,IAAE;AAAC;AAAC,IAAMc,KAAEV,GAAE;AAAV,IAAY,IAAEA,GAAE;AAAhB,IAAkBc,KAAE;;;ACAr7R,IAAMC,MAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,OAAKD,IAAE,KAAK,UAAQC,IAAE,KAAK,4BAA0BC,GAAE,qBAAmBA,GAAE;AAAA,EAAkB;AAAA,EAAC,KAAKD,IAAEE,IAAE;AAAC,WAAO,EAAEA,GAAE,YAAY,IAAE,aAAWA,GAAE,aAAa,OAAK,CAAC,IAAE,KAAK,mBAAmBF,IAAEE,EAAC,IAAE,KAAK,cAAcF,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,8BAA8BH,IAAEC,IAAE;AAAC,WAAO,KAAK,0BAA0BG,GAAEJ,GAAE,WAAW,KAAI,KAAK,MAAKC,EAAC,GAAEG,GAAEJ,GAAE,YAAY,KAAI,KAAK,MAAKC,EAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BD,IAAEC,IAAE,EAAC,kBAAiBI,GAAC,GAAE;AAAC,WAAO,MAAI,KAAK,6BAA2BD,GAAEE,GAAEL,IAAEI,IAAEE,IAAE,KAAK,IAAI,GAAED,GAAEN,IAAEK,IAAEE,IAAE,KAAK,IAAI,CAAC,IAAE,KAAK;AAAA,EAAyB;AAAA,EAAC,WAAWP,IAAEG,IAAE;AAAC,WAAO,EAAEH,IAAEG,EAAC,IAAED,GAAE;AAAA,EAAqB;AAAA,EAAC,0BAA0BF,IAAE;AAAC,WAAM,YAAUA,KAAE,KAAK,kCAAgC,KAAK;AAAA,EAA+B;AAAA,EAAC,IAAI,kCAAiC;AAAC,WAAO,KAAK,QAAQ,WAAS,KAAK,QAAQ;AAAA,EAAQ;AAAA,EAAC,IAAI,kCAAiC;AAAC,UAAK,EAAC,UAASA,IAAE,4BAA2BC,GAAC,IAAE,KAAK,SAAQE,KAAEH,KAAEC;AAAE,WAAOE,KAAEA;AAAA,EAAC;AAAC;;;ACA3gC,IAAMK,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAY,EAAC,WAAUC,IAAE,SAAQF,IAAE,aAAYG,IAAE,UAASC,GAAC,GAAE;AAAC,UAAMD,IAAE,IAAI,EAAED,IAAEF,EAAC,GAAEI,IAAEC,GAAE,IAAI,GAAE,KAAK,qBAAmB,IAAIC,GAAEC,GAAE,qBAAoBL,IAAEF,IAAEI,IAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAM,CAAC,KAAK,oBAAmB,IAAIE,GAAEC,GAAE,QAAO,KAAK,wBAAwB,GAAE,KAAK,aAAY,KAAK,UAAS,KAAK,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,WAAO,KAAK,WAAW,aAAa,KAAK,WAAW;AAAA,EAAC;AAAC;;;ACA1D,IAAMC,KAAN,cAAgBC,IAAC;AAAA,EAAC,cAAcC,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE,uBAAuB,KAAK,WAAW,CAAC,GAAEE,KAAED,GAAE,MAAM,QAAOH,MAAE,CAAC;AAAE,QAAGI,KAAE,EAAE,QAAOJ;AAAE,UAAK,EAAC,kBAAiBK,GAAC,IAAEH,IAAEI,KAAEC,GAAEN,IAAEI,IAAEG,IAAE,KAAK,IAAI,GAAE,EAAC,MAAKC,GAAC,IAAE,MAAKV,KAAEI,GAAE,MAAMC,KAAE,CAAC;AAAE,QAAIM,KAAEX;AAAE,OAAE;AAAC,UAAG,KAAK,8BAA8BW,IAAER,EAAC,GAAE;AAAC,cAAMS,KAAEH,GAAEE,IAAED,IAAEP,EAAC;AAAE,aAAK,0BAA0BS,GAAE,MAAKA,GAAE,OAAMV,IAAEK,IAAEJ,IAAEF,GAAC;AAAA,MAAC;AAAC,MAAAU,KAAEA,GAAE,WAAW;AAAA,IAAQ,SAAOA,MAAGA,OAAIX;AAAG,WAAOC;AAAA,EAAC;AAAA,EAAC,mBAAmBE,IAAEE,IAAE;AAAC,UAAMJ,MAAE,CAAC,GAAEK,KAAEJ,GAAEG,GAAE,YAAY,GAAEE,KAAED,GAAE;AAAU,QAAGC,GAAE,MAAM,SAAO,EAAE,QAAON;AAAE,UAAK,EAAC,MAAKS,GAAC,IAAE,MAAK,EAAC,kBAAiBV,GAAC,IAAEK,IAAEM,KAAEH,GAAEL,IAAEH,IAAES,IAAEC,EAAC,GAAEG,KAAEP,GAAE,UAASQ,KAAER,GAAE;AAAU,IAAAO,MAAGC,MAAG,KAAK,8BAA8BD,IAAER,EAAC,KAAG,KAAK,8BAA8BS,IAAET,EAAC,KAAG,KAAK,0BAA0BU,GAAEF,GAAE,WAAW,KAAIH,IAAEL,EAAC,GAAEU,GAAED,GAAE,YAAY,KAAIJ,IAAEL,EAAC,GAAEF,IAAEQ,IAAEN,IAAEJ,GAAC;AAAE,UAAMc,KAAER,GAAE,MAAM,CAAC;AAAE,QAAIS,KAAED;AAAE,OAAE;AAAC,UAAGC,OAAIV,GAAE,YAAUU,OAAIV,GAAE,aAAW,KAAK,8BAA8BU,IAAEX,EAAC,GAAE;AAAC,cAAMH,KAAEO,GAAEO,IAAEN,IAAEL,EAAC;AAAE,aAAK,0BAA0BH,GAAE,MAAKA,GAAE,OAAMC,IAAEQ,IAAEN,IAAEJ,GAAC;AAAA,MAAC;AAAC,MAAAe,KAAEA,GAAE,YAAY;AAAA,IAAS,SAAOA,MAAGA,OAAID;AAAG,WAAOd;AAAA,EAAC;AAAA,EAAC,0BAA0BC,IAAEC,IAAEC,IAAEa,IAAEjB,IAAEW,IAAE;AAJ5mD;AAI6mD,UAAK,EAAC,kBAAiBE,IAAE,SAAQC,GAAC,IAAEd,IAAEe,KAAEf,GAAE,EAAEI,IAAE,EAAC,OAAMF,IAAE,KAAIC,IAAE,MAAKe,GAAE,KAAI,CAAC,CAAC;AAAE,IAAAH,GAAEE,IAAET,GAAEO,IAAEF,IAAEJ,IAAE,KAAK,IAAI,CAAC,IAAE,KAAK,0BAA0BK,EAAC,KAAGH,GAAE,KAAK,IAAIP,GAAE,EAAC,WAAUF,IAAE,SAAQC,IAAE,aAAYY,IAAE,UAAS,sBAAkB,KAAAf,GAAE,kBAAF,mBAAiB,MAAI,CAAC,CAAC;AAAA,EAAC;AAAC;;;ACA/zC,IAAMmB,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAY,EAAC,eAAcC,IAAE,WAAUC,IAAE,aAAYC,IAAE,UAASC,GAAC,GAAE;AAAC,UAAMC,KAAEL,GAAEE,EAAC,GAAE,EAAC,MAAKH,IAAE,OAAMO,GAAC,IAAEL;AAAE,IAAAM,GAAEF,IAAE,EAAEA,IAAEA,IAAEC,EAAC,GAAEP,EAAC,GAAE,MAAMI,IAAE,IAAI,EAAED,IAAEM,GAAEH,EAAC,CAAC,GAAED,IAAEK,GAAE,IAAI,GAAE,KAAK,kBAAgB,CAAC,EAAC,MAAKR,IAAE,UAAS,MAAG,WAAU,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAM,CAAC,IAAIS,GAAEC,GAAE,QAAO,KAAK,WAAW,OAAM,KAAK,aAAY,KAAK,UAAS,KAAK,MAAM,GAAE,IAAIC,GAAE,KAAK,WAAW,OAAM,KAAK,aAAY,KAAK,UAAS,KAAK,MAAM,GAAE,GAAG,KAAK,gBAAgB,IAAK,CAAAL,OAAG,IAAIG,GAAEC,GAAE,WAAUJ,GAAE,KAAK,MAAKA,GAAE,KAAK,OAAM,KAAK,UAAS,KAAK,QAAOA,GAAE,UAASA,GAAE,SAAS,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,UAAMP,KAAE,EAAC,MAAKO,IAAE,UAAS,MAAG,WAAU,KAAE;AAAE,SAAK,gBAAgB,QAAS,CAAAG,QAAG;AAAC,QAAEH,GAAE,OAAMG,IAAE,KAAK,IAAI,MAAIA,IAAE,WAAS,OAAGV,GAAE,YAAU,QAAI,EAAEO,GAAE,OAAMG,IAAE,KAAK,KAAK,MAAIA,IAAE,YAAU,OAAGV,GAAE,YAAU,QAAI,EAAEO,GAAE,MAAKG,IAAE,KAAK,KAAK,MAAIA,IAAE,YAAU,OAAGV,GAAE,WAAS,QAAI,EAAEO,GAAE,MAAKG,IAAE,KAAK,IAAI,MAAIA,IAAE,WAAS,OAAGV,GAAE,WAAS;AAAA,IAAG,CAAE,GAAE,KAAK,gBAAgB,KAAKA,EAAC;AAAA,EAAC;AAAC;;;ACAjoB,IAAMa,KAAN,cAAgBC,IAAC;AAAA,EAAC,cAAcC,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE,uBAAuB,KAAK,WAAW,CAAC,GAAEE,KAAED,GAAE,MAAM,QAAOE,KAAEF,GAAE,SAAS,QAAOH,MAAE,CAAC;AAAE,QAAGI,KAAE,EAAE,QAAOJ;AAAE,UAAK,EAAC,MAAKM,GAAC,IAAE,MAAKC,KAAEC,GAAEP,IAAEC,GAAE,kBAAiBO,IAAEH,EAAC,GAAEI,KAAEC,GAAER,GAAE,SAASE,KAAE,CAAC,EAAE,KAAIC,IAAEJ,EAAC,GAAEU,KAAED,GAAER,GAAE,SAAS,CAAC,EAAE,KAAIG,IAAEJ,EAAC,GAAEH,KAAEI,GAAE,MAAMC,KAAE,CAAC;AAAE,QAAIS,KAAEd;AAAE,OAAE;AAAC,UAAG,KAAK,8BAA8Bc,IAAEX,EAAC,GAAE;AAAC,cAAMC,KAAEM,GAAEI,IAAEP,IAAEJ,EAAC;AAAE,aAAK,2BAA2BC,IAAEO,IAAET,IAAEM,IAAEL,IAAEF,GAAC,GAAE,KAAK,2BAA2BG,IAAES,IAAEX,IAAEM,IAAEL,IAAEF,GAAC;AAAA,MAAC;AAAC,MAAAa,KAAEA,GAAE,WAAW;AAAA,IAAQ,SAAOA,MAAGA,OAAId;AAAG,WAAOC;AAAA,EAAC;AAAA,EAAC,mBAAmBE,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC,GAAEC,KAAEJ,GAAEE,GAAE,YAAY,GAAEH,MAAEK,GAAE;AAAU,QAAGL,IAAE,MAAM,SAAO,EAAE,QAAOI;AAAE,UAAK,EAAC,MAAKE,GAAC,IAAE,MAAKC,KAAEC,GAAEN,IAAEC,GAAE,kBAAiBM,IAAEH,EAAC,GAAEI,KAAEL,GAAE,UAASO,KAAEP,GAAE,WAAUN,KAAEC,IAAE,SAAS,CAAC,GAAEa,KAAEF,GAAEZ,GAAE,KAAIO,IAAEH,EAAC,GAAEK,KAAER,IAAE,SAAS,QAAOc,KAAEd,IAAE,SAASQ,KAAE,CAAC,GAAEC,KAAEE,GAAEG,GAAE,KAAIR,IAAEH,EAAC,GAAEY,KAAEf,IAAE,MAAM,CAAC;AAAE,QAAIgB,KAAED;AAAE,OAAE;AAAC,UAAGC,OAAIN,MAAGM,OAAIJ,MAAG,KAAK,8BAA8BI,IAAEb,EAAC,GAAE;AAAC,cAAMF,KAAEQ,GAAEO,IAAEV,IAAEH,EAAC;AAAE,QAAAO,MAAG,KAAK,2BAA2BT,IAAEU,GAAED,GAAE,WAAW,KAAIJ,IAAEH,EAAC,GAAED,IAAEK,IAAEJ,IAAEC,EAAC,GAAEQ,MAAG,KAAK,2BAA2BX,IAAEU,GAAEC,GAAE,YAAY,KAAIN,IAAEH,EAAC,GAAED,IAAEK,IAAEJ,IAAEC,EAAC,GAAEC,OAAIN,KAAE,KAAK,2BAA2BE,IAAEQ,IAAEP,IAAEK,IAAEJ,IAAEC,EAAC,IAAEC,OAAIS,MAAG,KAAK,2BAA2Bb,IAAEY,IAAEX,IAAEK,IAAEJ,IAAEC,EAAC;AAAA,MAAC;AAAC,MAAAY,KAAEA,GAAE,YAAY;AAAA,IAAS,SAAOA,MAAGA,OAAID;AAAG,WAAOX;AAAA,EAAC;AAAA,EAAC,2BAA2BH,IAAEE,IAAEH,KAAEiB,IAAEN,IAAEZ,IAAE;AAJl5D;AAIm5D,UAAMS,KAAEP,GAAE,MAAKa,KAAEb,GAAE;AAAM,QAAGc,GAAEF,IAAEV,IAAEK,IAAEM,EAAC,GAAE,EAAED,IAAEV,EAAC,IAAEO,GAAE,sBAAsB;AAAO,IAAAK,GAAEF,IAAEb,KAAEQ,IAAEM,IAAEX,EAAC;AAAE,UAAK,EAAC,kBAAiBM,IAAE,SAAQM,GAAC,IAAEJ,IAAEK,KAAEC,GAAEd,GAAEU,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEb,IAAE,CAAC,CAAC,CAAC;AAAE,QAAGW,GAAEM,IAAET,GAAEQ,IAAEP,IAAEA,IAAE,KAAK,IAAI,CAAC,IAAE,KAAK,0BAA0BM,EAAC,GAAE;AAAC,UAAG,KAAK,WAAWC,IAAEb,EAAC,KAAG,KAAK,WAAWK,IAAEM,EAAC,EAAE;AAAO,UAAG,KAAK,6BAA6Bb,IAAEF,EAAC,EAAE;AAAO,MAAAA,GAAE,KAAK,IAAIA,GAAE,EAAC,eAAcE,IAAE,WAAUE,IAAE,aAAYa,IAAE,UAAS,sBAAkB,KAAAL,GAAE,kBAAF,mBAAiB,MAAI,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BV,IAAEE,IAAE;AAAC,UAAMC,KAAEH,GAAE,MAAKiB,KAAEjB,GAAE;AAAM,eAAUD,OAAKG,GAAE,KAAGY,GAAEF,IAAEK,IAAElB,IAAE,WAAW,OAAMA,IAAE,WAAW,KAAII,EAAC,GAAE,EAAES,IAAEK,EAAC,IAAER,GAAE,sBAAsB,QAAOV,IAAE,iBAAiBC,EAAC,GAAE;AAAG,WAAM;AAAA,EAAE;AAAC;AAAC,IAAMY,KAAEb,GAAE;;;ACA9uD,IAAMmB,KAAN,cAAgBC,IAAC;AAAA,EAAC,cAAcC,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE,uBAAuB,KAAK,WAAW,CAAC,GAAEE,KAAED,GAAE,SAAS,QAAOE,KAAE,CAAC;AAAE,QAAGD,KAAE,EAAE,QAAOC;AAAE,UAAK,EAAC,MAAKC,GAAC,IAAE,MAAKN,MAAEO,GAAEN,IAAEC,GAAE,kBAAiBM,IAAEF,EAAC,GAAEG,KAAEN,GAAE,SAASC,KAAE,CAAC;AAAE,QAAG,KAAK,8BAA8BK,GAAE,UAASP,EAAC,GAAE;AAAC,YAAMC,KAAEO,GAAED,GAAE,KAAIH,IAAEJ,EAAC,GAAEE,KAAEM,GAAED,GAAE,SAAS,WAAW,KAAIH,IAAEJ,EAAC;AAAE,WAAK,2BAA2BG,IAAED,IAAED,IAAEF,IAAED,KAAEE,EAAC;AAAA,IAAC;AAAC,UAAMS,KAAER,GAAE,SAAS,CAAC;AAAE,QAAG,KAAK,8BAA8BQ,GAAE,WAAUT,EAAC,GAAE;AAAC,YAAMC,KAAEO,GAAEC,GAAE,KAAIL,IAAEJ,EAAC,GAAEE,KAAEM,GAAEC,GAAE,UAAU,YAAY,KAAIL,IAAEJ,EAAC;AAAE,WAAK,2BAA2BG,IAAED,IAAED,IAAEF,IAAED,KAAEE,EAAC;AAAA,IAAC;AAAC,WAAOG;AAAA,EAAC;AAAA,EAAC,mBAAmBH,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC,GAAEC,KAAEJ,GAAEE,GAAE,YAAY;AAAE,QAAGE,GAAE,UAAU,SAAS,SAAO,EAAE,QAAOD;AAAE,UAAK,EAAC,MAAKE,GAAC,IAAE,MAAKN,MAAEO,GAAEL,IAAEC,GAAE,kBAAiBK,IAAEF,EAAC,GAAEG,KAAEJ,GAAE,UAASM,KAAEN,GAAE;AAAU,QAAGI,MAAGA,GAAE,WAAW,UAAS;AAAC,YAAMR,KAAEQ,GAAE,WAAW;AAAS,UAAG,KAAK,8BAA8BR,IAAEE,EAAC,GAAE;AAAC,cAAME,KAAEK,GAAET,GAAE,YAAY,KAAIK,IAAEH,EAAC,GAAEM,KAAEC,GAAET,GAAE,WAAW,KAAIK,IAAEH,EAAC;AAAE,aAAK,2BAA2BC,IAAEK,IAAEJ,IAAEH,IAAEF,KAAEG,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAGQ,MAAGA,GAAE,YAAY,WAAU;AAAC,YAAMV,KAAEU,GAAE,YAAY;AAAU,UAAG,KAAK,8BAA8BV,IAAEE,EAAC,GAAE;AAAC,cAAME,KAAEK,GAAET,GAAE,WAAW,KAAIK,IAAEH,EAAC,GAAEM,KAAEC,GAAET,GAAE,YAAY,KAAIK,IAAEH,EAAC;AAAE,aAAK,2BAA2BC,IAAEK,IAAEJ,IAAEH,IAAEF,KAAEG,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,2BAA2BH,IAAEK,IAAEM,IAAEC,IAAEd,IAAEe,IAAE;AAJx7D;AAIy7D,UAAK,EAAC,kBAAiB,GAAE,SAAQC,GAAC,IAAED;AAAE,IAAAR,GAAEC,IAAEK,IAAEN,EAAC;AAAE,UAAM,IAAE,EAAEU,IAAET,GAAE,CAAC,GAAE,CAACA,GAAE,CAAC,GAAE,CAAC,GAAE,IAAEO,GAAE,GAAER,GAAEC,IAAEM,IAAED,EAAC,CAAC,IAAED,GAAE,CAAC,GAAEM,KAAEC,GAAE,EAAEhB,GAAEW,EAAC,GAAED,IAAE,GAAE,CAAC,CAAC;AAAE,QAAGF,GAAEX,IAAEQ,GAAEU,IAAE,GAAET,IAAE,KAAK,IAAI,CAAC,IAAE,KAAK,0BAA0BO,EAAC,GAAE;AAAC,UAAG,KAAK,WAAWE,IAAEL,EAAC,KAAG,KAAK,WAAWA,IAAEN,EAAC,EAAE;AAAO,YAAMJ,KAAE,EAAEF,GAAE,GAAEY,IAAE,GAAE,KAAK,KAAK,CAAC,CAAC;AAAE,MAAAX,GAAE,KAAK,IAAIK,GAAE,EAAC,aAAYW,IAAE,YAAW,IAAI,EAAEL,IAAEM,GAAEhB,EAAC,CAAC,GAAE,gBAAeI,IAAE,aAAYM,IAAE,iBAAgBZ,GAAE,QAAO,UAAS,sBAAkB,KAAAc,GAAE,kBAAF,mBAAiB,MAAI,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAMP,KAAEP,GAAE;AAAV,IAAYgB,KAAEhB,GAAE;;;ACA7yD,IAAMmB,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAY,EAAC,aAAYC,IAAE,QAAOC,IAAE,QAAOC,IAAE,UAASC,GAAC,GAAE;AAAC,UAAMH,IAAE,IAAII,GAAEC,GAAE,EAAEC,GAAE,GAAEL,IAAEC,IAAE,GAAE,CAAC,GAAE,MAAG,EAAED,IAAEC,EAAC,CAAC,GAAEC,IAAEI,GAAE,IAAI,GAAE,KAAK,MAAIN,IAAE,KAAK,MAAIC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAM,CAAC,IAAII,GAAEE,GAAE,WAAU,KAAK,aAAY,KAAK,KAAI,KAAK,UAAS,KAAK,MAAM,GAAE,IAAIF,GAAEE,GAAE,WAAU,KAAK,aAAY,KAAK,KAAI,KAAK,UAAS,KAAK,MAAM,GAAE,IAAIC,GAAE,KAAK,KAAI,KAAK,aAAY,KAAK,KAAI,KAAK,UAAS,KAAK,MAAM,CAAC;AAAA,EAAC;AAAC;;;ACA9O,IAAMC,KAAN,cAAgBC,IAAC;AAAA,EAAC,cAAcC,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE,uBAAuB,KAAK,WAAW,CAAC,GAAEE,KAAE,CAAC,GAAEC,KAAEF,GAAE,SAAS;AAAO,QAAG,cAAYD,GAAE,uBAAuB,KAAK,QAAMG,KAAE,EAAE,QAAOD;AAAE,UAAK,EAAC,MAAKE,GAAC,IAAE,MAAKN,MAAEG,GAAE,SAAS,CAAC,GAAEI,KAAEJ,GAAE,SAASE,KAAE,CAAC,GAAEG,KAAEC,GAAET,IAAE,KAAIM,IAAEJ,EAAC,GAAEQ,KAAED,GAAEF,GAAE,KAAID,IAAEJ,EAAC;AAAE,WAAO,KAAK,0BAA0BM,IAAEE,IAAET,IAAEC,IAAEE,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,mBAAmBF,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC,GAAEC,KAAEJ,GAAEE,GAAE,YAAY,GAAEG,KAAED,GAAE;AAAU,QAAGC,GAAE,MAAM,SAAO,EAAE,QAAOF;AAAE,QAAG,eAAaD,GAAE,uBAAuB,KAAK,SAAO,MAAIE,GAAE,SAAOA,GAAE,UAAQC,GAAE,SAAS,SAAO,GAAG,QAAOF;AAAE,UAAK,EAAC,MAAKJ,IAAC,IAAE,MAAKO,KAAEE,GAAEJ,GAAE,SAAS,WAAW,KAAIL,KAAEG,EAAC,GAAEK,KAAEC,GAAEJ,GAAE,UAAU,YAAY,KAAIL,KAAEG,EAAC;AAAE,WAAO,KAAK,0BAA0BI,IAAEC,IAAEN,IAAEC,IAAEC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,0BAA0BH,IAAEG,IAAEJ,KAAEW,IAAEZ,IAAE;AAJv4C;AAIw4C,QAAG,CAAC,KAAK,0BAA0BE,IAAEG,IAAEO,EAAC,EAAE;AAAO,UAAMC,KAAEC,GAAEC,IAAEb,IAAEG,IAAE,GAAE,GAAEW,KAAE,MAAG,EAAEd,IAAEG,EAAC,GAAEY,KAAEhB,GAAE;AAAE,IAAAU,GAAEM,IAAEhB,KAAEY,IAAEG,EAAC,GAAEC,GAAE,CAAC,IAAEhB,IAAE,CAAC;AAAE,UAAMiB,KAAEC,GAAEF,EAAC,GAAE,EAAC,kBAAiBG,IAAE,SAAQC,GAAC,IAAET,IAAEU,KAAEN,GAAEf,KAAEmB,IAAEG,IAAE,KAAK,IAAI;AAAE,QAAGb,GAAEY,IAAEN,GAAEE,IAAEE,IAAEG,IAAE,KAAK,IAAI,CAAC,IAAE,KAAK,0BAA0BF,EAAC,GAAE;AAAC,UAAG,KAAK,WAAWnB,IAAEgB,EAAC,KAAG,KAAK,WAAWA,IAAEb,EAAC,EAAE;AAAO,MAAAL,GAAE,KAAK,IAAIA,GAAE,EAAC,aAAYkB,IAAE,QAAOhB,IAAE,QAAOG,IAAE,UAAS,sBAAkB,KAAAO,GAAE,kBAAF,mBAAiB,MAAI,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAMG,KAAEd,GAAE;;;ACAvjC,IAAIuB,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,OAAG,KAAK,YAAU,IAAI,KAAE,KAAK,UAAQC,GAAE;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,UAAU,KAAK,IAAIC,GAAE,KAAK,MAAK,KAAK,OAAO,GAAE,IAAIC,GAAE,KAAK,MAAK,KAAK,OAAO,GAAE,IAAIC,GAAE,KAAK,MAAK,KAAK,OAAO,GAAE,IAAIC,GAAE,KAAK,MAAK,KAAK,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAQL,IAAE;AAAC,SAAK,KAAK,WAAUA,EAAC;AAAE,eAAUM,MAAK,KAAK,UAAU,CAAAA,GAAE,UAAQN;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBA,IAAEM,IAAEC,IAAE;AAAC,QAAG,EAAED,KAAE,KAAK,WAAS,KAAK,QAAQ,sBAAsB,QAAM,CAAC;AAAE,UAAME,KAAE,CAAC;AAAE,eAAUC,MAAK,KAAK,UAAU,MAAM,YAAUH,MAAKG,GAAE,KAAKT,IAAEO,EAAC,EAAE,CAAAC,GAAE,KAAKF,EAAC;AAAE,WAAOI,GAAEV,IAAEQ,EAAC,GAAEA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAET,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,IAAI,GAAEA,KAAE,EAAE,CAACY,GAAE,oDAAoD,CAAC,GAAEZ,EAAC;;;ACAn0C,SAASa,GAAEA,IAAEC,IAAE;AAAC,SAAM,CAAC,IAAIC,GAAE,EAAC,MAAKF,IAAE,SAAQC,GAAC,CAAC,GAAE,IAAI,EAAE,EAAC,MAAKD,IAAE,SAAQC,IAAE,kBAAiBD,GAAE,iBAAgB,CAAC,CAAC;AAAC;;;ACAmG,IAAIG,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,QAAM,MAAK,KAAK,UAAQ,MAAG,KAAK,WAAS,OAAG,KAAK,eAAa;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAED,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAEA,KAAE,EAAE,CAACE,GAAE,4DAA4D,CAAC,GAAEF,EAAC;AAAE,IAAMG,KAAEH;;;ACAjP,IAAII,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,OAAG,KAAK,iBAAe,OAAG,KAAK,cAAY,MAAG,KAAK,iBAAe,MAAG,KAAK,iBAAe,IAAI,KAAE,KAAK,WAASC,GAAE,UAAS,KAAK,6BAA2BA,GAAE;AAAA,EAA0B;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,iBAAe,CAAC,KAAK,UAAQ,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,oBAAkB,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,0BAAyB;AAAC,WAAO,KAAK,oBAAkB,KAAK;AAAA,EAAc;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOE,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,8BAA6B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,2BAA0B,IAAI,GAAEA,KAAE,EAAE,CAACG,GAAE,iDAAiD,CAAC,GAAEH,EAAC;AAAE,IAAMI,MAAEJ;;;ACAnvC,IAAMK,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,KAAEC,IAAEH,IAAEI,IAAE;AAAC,UAAMF,KAAE,IAAIG,GAAEH,KAAEC,GAAE,YAAWH,GAAE,UAAU,GAAEI,IAAEE,GAAE,GAAG,GAAE,KAAK,QAAMH,IAAE,KAAK,SAAOH;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,MAAM,cAAY,KAAK,aAAY,KAAK,OAAO,cAAY,KAAK,aAAY,CAAC,GAAG,KAAK,MAAM,OAAM,GAAG,KAAK,OAAO,OAAM,IAAII,GAAE,KAAK,aAAY,KAAK,UAAS,KAAK,MAAM,CAAC;AAAA,EAAC;AAAC;;;ACA4tB,IAAIG,KAAE,cAAcC,GAAE,aAAaC,EAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,IAAIF,OAAE,KAAK,yBAAuBG,IAAE,KAAK,WAAS,CAAC,GAAE,KAAK,wBAAsB,MAAK,KAAK,gCAA8B,CAAC,GAAE,KAAK,sBAAoBC,GAAE;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,QAAQ,IAAI,CAACC,GAAG,MAAI;AAAC,YAAK,EAAC,yBAAwBH,IAAE,sBAAqBI,IAAE,4BAA2BN,KAAE,UAASG,GAAC,IAAE,KAAK;AAAQ,aAAM,EAAC,yBAAwBD,IAAE,sBAAqBI,IAAE,4BAA2BN,KAAE,UAASG,GAAC;AAAA,IAAC,GAAI,MAAI;AAAC,WAAK,aAAa,GAAE,KAAK,KAAK,SAAS;AAAA,IAAC,GAAG,CAAC,GAAEE,GAAG,MAAI,KAAK,SAAU,CAAAH,OAAG;AAAC,iBAAUI,MAAK,KAAK,SAAS,CAAAA,GAAE,UAAQJ;AAAA,IAAC,GAAG,CAAC,GAAEG,GAAG,OAAK,EAAC,WAAU,KAAK,KAAK,OAAM,sBAAqB,KAAK,KAAK,kBAAiB,wBAAuB,KAAK,uBAAsB,IAAK,CAAC,EAAC,WAAUH,IAAE,wBAAuBI,GAAC,MAAI,KAAK,iBAAiBJ,IAAEI,EAAC,GAAGP,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,SAAS,KAAM,CAAAG,OAAGA,GAAE,QAAS;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEI,IAAE;AAAC,QAAG,KAAK,gBAAgB,GAAE,CAACJ,GAAE;AAAO,UAAK,EAAC,MAAKF,KAAE,SAAQG,GAAC,IAAE;AAAK,SAAK,WAASG,GAAEN,KAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,eAAUD,MAAK,KAAK,SAAS,CAAAA,GAAE,QAAQ;AAAE,SAAK,WAAS,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,iCAAgC;AAAC,WAAO,KAAK,QAAQ,WAAS,KAAK,QAAQ;AAAA,EAAQ;AAAA,EAAC,IAAI,kCAAiC;AAAC,UAAK,EAAC,UAASA,IAAE,4BAA2BI,GAAC,IAAE,KAAK,SAAQN,MAAEE,KAAEI;AAAE,WAAON,MAAEA;AAAA,EAAC;AAAA,EAAC,IAAI,uCAAsC;AAAC,WAAOO,GAAE,qCAAmCA,GAAE;AAAA,EAAkC;AAAA,EAAC,MAAM,KAAKL,IAAE;AAAC,WAAO,EAAEA,EAAC,IAAE,KAAK,gBAAgBA,EAAC,IAAE,KAAK,iBAAiBA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAK,EAAC,OAAMI,IAAE,SAAQN,IAAC,IAAEE;AAAE,SAAK,qBAAqB;AAAE,UAAMM,KAAE,KAAK;AAAsB,QAAG,EAAEA,EAAC,EAAE,QAAOF;AAAE,UAAMG,KAAE,KAAK,mBAAmBP,EAAC;AAAE,QAAG,EAAEO,EAAC,EAAE,QAAOH;AAAE,UAAK,EAAC,kBAAiBI,GAAC,IAAEV,KAAEO,KAAE,GAAEE,IAAEC,EAAC;AAAE,QAAG,EAAEH,EAAC,EAAE,QAAOD;AAAE,UAAK,EAAC,MAAKK,GAAC,IAAE,MAAK,EAAC,eAAcV,IAAE,YAAWI,GAAC,IAAEL,KAAEY,KAAE,CAAC,GAAEC,KAAEJ,GAAEF,IAAEI,IAAEX,GAAC,GAAEc,KAAEN,GAAE,WAAW,UAAUK,EAAC;AAAE,QAAG,CAAC,KAAK,gCAAgCA,IAAEC,IAAEd,GAAC,EAAE,QAAO,KAAK,oBAAoB,GAAEM;AAAE,IAAAE,GAAE,cAAYM,IAAEF,GAAE,KAAK,GAAGJ,GAAE,KAAK;AAAE,eAAUL,MAAK,KAAK,8BAA8B,CAAAA,GAAE,cAAYW,IAAEF,GAAE,KAAK,GAAGT,GAAE,KAAK;AAAE,WAAO,EAAEE,EAAC,KAAG,KAAK,QAAQ,IAAIA,GAAE,KAAKO,IAAE,EAAC,kBAAiBF,IAAE,eAAc,EAAEV,GAAC,GAAE,MAAKW,IAAE,eAAcX,IAAE,cAAa,CAAC,GAAEe,EAAC,GAAEC,GAAEF,IAAEH,IAAE,EAAC,GAAEL,GAAE,GAAE,GAAEA,GAAE,GAAE,kBAAiBA,GAAE,kBAAiB,eAAcL,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,SAAK,qBAAqB,GAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,mBAAmB,EAAC,OAAMC,IAAE,YAAWI,GAAC,GAAE;AAAC,YAAO,KAAK,qBAAoB;AAAA,MAAC,KAAKF,GAAE;AAAK,eAAOF;AAAA,MAAE,KAAKE,GAAE;AAAM,eAAOE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,SAAK,wBAAsB,MAAK,KAAK,gCAA8B,CAAC,GAAE,KAAK,sBAAoBF,GAAE;AAAA,EAAI;AAAA,EAAC,uBAAsB;AAAC,SAAK,QAAQ,OAAOW,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiB,EAAC,OAAMb,IAAE,SAAQI,IAAE,QAAON,IAAC,GAAE;AAAC,UAAK,EAAC,MAAKG,GAAC,IAAE,MAAKc,KAAER,GAAEP,IAAEC,IAAEG,EAAC,GAAEE,KAAE,MAAM,KAAK,iBAAiBS,IAAEb,GAAE,KAAIE,IAAEN,GAAC;AAAE,WAAO,KAAK,kBAAkBiB,IAAEb,GAAE,MAAKI,IAAEL,IAAEG,IAAE,EAAC,GAAEJ,GAAE,GAAE,GAAEA,GAAE,GAAE,kBAAiBA,GAAE,kBAAiB,eAAcI,GAAE,cAAa,GAAEN,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgB,EAAC,OAAME,IAAE,YAAWI,IAAE,SAAQN,KAAE,QAAOG,GAAC,GAAE;AAAC,UAAK,EAAC,MAAKc,GAAC,IAAE,MAAK,EAAC,kBAAiBT,IAAE,kBAAiBC,GAAC,IAAET;AAAE,UAAM,GAAEM,GAAE,kBAAiBG,EAAC;AAAE,UAAMC,KAAE,GAAEJ,IAAEG,EAAC,GAAEF,KAAEE,GAAEC,IAAEO,IAAEjB,GAAC,GAAEW,KAAE,MAAM,KAAK,iBAAiBJ,IAAEH,GAAE,SAAQJ,KAAEG,EAAC;AAAE,QAAGQ,GAAE,SAAO,GAAE;AAAC,YAAMT,KAAE,MAAM,KAAK,iBAAiBK,IAAEH,GAAE,MAAKJ,KAAEG,EAAC;AAAE,aAAO,KAAK,kBAAkBI,IAAEH,GAAE,OAAM,CAAC,GAAGO,IAAE,GAAGT,EAAC,GAAEe,IAAEjB,KAAE,EAAC,GAAEU,GAAE,GAAE,GAAEA,GAAE,GAAE,kBAAiBA,GAAE,kBAAiB,eAAcV,IAAE,cAAa,GAAEG,EAAC;AAAA,IAAC;AAAC,UAAMF,KAAEQ,GAAEP,IAAEe,IAAEjB,GAAC,GAAEY,KAAE,MAAM,KAAK,iBAAiBX,IAAEG,GAAE,MAAKJ,KAAEG,EAAC;AAAE,WAAO,KAAK,kBAAkBF,IAAEG,GAAE,MAAKQ,IAAEK,IAAEjB,KAAE,EAAC,GAAEQ,GAAE,KAAK,KAAGN,GAAE,OAAKA,GAAE,KAAG,IAAE,QAAO,GAAEM,GAAE,KAAK,KAAGN,GAAE,OAAKA,GAAE,KAAG,IAAE,QAAO,kBAAiBA,GAAE,kBAAiB,eAAcF,IAAE,cAAa,GAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBD,IAAEI,IAAEN,KAAEG,IAAE;AAAC,YAAO,MAAM,QAAQ,IAAI,KAAK,SAAS,IAAK,CAAAc,OAAGA,GAAE,gBAAgBf,IAAEI,IAAEN,KAAEG,EAAC,CAAE,CAAC,GAAG,KAAK;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAEI,IAAEN,KAAEG,IAAEM,IAAEC,IAAEH,IAAE;AAAC,WAAM,EAAC,IAAI,QAAO;AAAC,aAAM,CAAC,EAAEA,EAAC;AAAA,IAAC,GAAE,OAAM,MAAI;AAAC,YAAK,EAAC,kBAAiBC,GAAC,IAAEC,IAAE,EAAC,cAAaF,IAAE,OAAMI,GAAC,IAAE,KAAK,mBAAmBT,IAAEI,IAAEN,KAAES,EAAC;AAAE,aAAO,KAAK,qBAAqB,GAAE,EAAEA,GAAE,UAAU,KAAG,KAAK,QAAQ,IAAIA,GAAE,WAAW,KAAKE,IAAE,EAAC,kBAAiBH,IAAE,eAAcJ,IAAE,MAAKD,IAAE,eAAcM,GAAE,cAAa,CAAC,GAAEM,EAAC,GAAEC,GAAET,IAAEJ,IAAEO,EAAC;AAAA,IAAC,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBR,IAAEI,IAAEN,KAAEG,IAAE;AAAC,QAAGH,IAAE,SAAO,EAAE,QAAO,KAAK,aAAa,GAAE,EAAC,cAAaE,IAAE,OAAM,CAAC,EAAC;AAAE,SAAK,wBAAsBI,MAAG,KAAK,oBAAoB,GAAEL,GAAEC,IAAEF,GAAC;AAAE,UAAMQ,KAAE,KAAK;AAAsB,QAAG,EAAEA,EAAC,GAAE;AAAC,YAAMS,KAAE,KAAK,kCAAkCT,IAAER,GAAC;AAAE,UAAGiB,MAAG,GAAE;AAAC,YAAG,EAAEjB,IAAEiB,EAAC,aAAYT,IAAG,QAAO,KAAK,8BAA8BS,IAAEjB,KAAEE,IAAEI,IAAEH,EAAC;AAAE,YAAG,KAAK,gCAAgCD,IAAEM,GAAE,aAAYL,EAAC,EAAE,QAAO,KAAK,yBAAyBK,IAAEF,IAAEN,KAAEG,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO,KAAK,8BAA8B,GAAEH,KAAEE,IAAEI,IAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,kCAAkCD,IAAEI,IAAE;AAAC,WAAOJ,cAAaM,KAAE,KAAK,uBAAuBF,IAAEJ,GAAE,KAAK,KAAG,KAAG,KAAK,uBAAuBI,IAAEJ,GAAE,MAAM,KAAG,IAAE,IAAE,KAAG,KAAK,uBAAuBI,IAAEJ,EAAC;AAAA,EAAC;AAAA,EAAC,8BAA8BA,IAAEI,IAAEN,KAAEG,IAAEc,IAAE;AAAC,UAAK,EAAC,kBAAiBT,GAAC,IAAES,IAAER,KAAEH,GAAEJ,EAAC,GAAEQ,KAAE,CAAC;AAAE,aAAQH,KAAE,GAAEA,KAAED,GAAE,QAAO,EAAEC,IAAE;AAAC,UAAGA,OAAIL,GAAE;AAAS,YAAMC,KAAEG,GAAEC,EAAC;AAAE,iBAAUL,MAAKO,GAAE,WAAW,UAAUN,GAAE,UAAU,GAAE;AAAC,cAAMG,KAAEJ,GAAE,UAAUO,GAAE,WAAW;AAAE,QAAAC,GAAE,KAAK,CAAC,IAAIF,GAAEF,IAAEG,IAAEN,IAAEA,GAAE,QAAQ,GAAE,KAAK,uBAAuBH,KAAEM,IAAEE,EAAC,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOE,GAAE,SAAO,MAAIA,GAAE,KAAM,CAACR,IAAEI,OAAIJ,GAAE,CAAC,IAAEI,GAAE,CAAC,CAAE,GAAEI,GAAE,CAAC,EAAE,CAAC,IAAE,KAAK,gCAAgCO,GAAE,OAAO,KAAG,KAAK,yBAAyBP,GAAE,CAAC,EAAE,CAAC,GAAEP,IAAEG,IAAEW,EAAC,IAAE,KAAK,yBAAyBR,IAAEN,IAAEG,IAAEW,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBf,IAAEI,IAAEN,KAAEG,IAAE;AAAC,SAAK,aAAa,GAAE,KAAK,wBAAsBD,IAAE,KAAK,sBAAoBI;AAAE,UAAMW,KAAE,KAAK,sBAAsB,aAAYT,KAAE,CAAC;AAAE,IAAAA,GAAE,KAAK,GAAGN,GAAE,KAAK;AAAE,eAAUO,MAAKT,KAAE;AAAC,UAAGE,cAAaM,IAAE;AAAC,YAAGC,GAAE,WAAW,OAAOP,GAAE,MAAM,UAAU,KAAGO,GAAE,WAAW,OAAOP,GAAE,OAAO,UAAU,EAAE;AAAA,MAAQ,WAASO,GAAE,WAAW,OAAOP,GAAE,UAAU,EAAE;AAAS,YAAMI,KAAEG,GAAE,WAAW,UAAUQ,EAAC;AAAE,WAAK,uBAAuBX,IAAEW,IAAEd,GAAE,gBAAgB,IAAE,KAAK,yCAAuCM,GAAE,cAAYQ,IAAE,KAAK,8BAA8B,KAAKR,EAAC,GAAED,GAAE,KAAK,GAAGC,GAAE,KAAK;AAAA,IAAE;AAAC,WAAM,EAAC,cAAaQ,IAAE,OAAMT,GAAC;AAAA,EAAC;AAAA,EAAC,gCAAgCN,IAAE;AAAC,WAAM,YAAUA,KAAE,KAAK,kCAAgC,KAAK;AAAA,EAA8B;AAAA,EAAC,gCAAgCA,IAAEI,IAAEN,KAAE;AAAC,WAAO,KAAK,uBAAuBE,IAAEI,IAAEN,IAAE,gBAAgB,IAAE,KAAK,gCAAgCA,IAAE,OAAO;AAAA,EAAC;AAAA,EAAC,uBAAuBE,IAAEI,IAAEN,KAAE;AAAC,WAAOW,GAAE,KAAK,UAAUT,IAAEF,GAAC,GAAE,KAAK,UAAUM,IAAEN,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUE,IAAEI,IAAE;AAAC,WAAOM,GAAEV,IAAEI,GAAE,kBAAiBF,IAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAEI,IAAE;AAAC,QAAIN,MAAE;AAAG,aAAQG,KAAE,GAAEA,KAAED,GAAE,QAAO,EAAEC,GAAE,KAAGG,GAAE,WAAW,OAAOJ,GAAEC,EAAC,EAAE,UAAU,GAAE;AAAC,MAAAH,MAAEG;AAAE;AAAA,IAAK;AAAC,WAAOH;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM,EAAC,sBAAqB,KAAK,QAAQ,IAAIe,EAAC,GAAE,SAAQ,KAAK,SAAQ;AAAA,EAAC;AAAC;AAAE,IAAIX;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEL,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kCAAiC,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,mCAAkC,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,wCAAuC,IAAI,GAAEA,KAAE,EAAE,CAACU,GAAE,iDAAiD,CAAC,GAAEV,EAAC,GAAE,SAASG,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,QAAM,CAAC,IAAE;AAAO,EAAEE,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAMW,KAAE;AAAuB,SAAS,EAAEb,IAAE;AAAC,SAAO,EAAEA,GAAE,UAAU;AAAC;AAAC,SAAS,EAAE,EAAC,kBAAiBA,IAAE,eAAcI,GAAC,GAAE;AAAC,SAAOJ,GAAE,KAAK,IAAEE,KAAEE;AAAC;;;ACA9vP,IAAIY,KAAE,cAAcC,GAAE,gBAAe;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,YAAU,OAAG,KAAK,UAAQ,EAAC,MAAK,CAAC,GAAE,MAAK,CAAC,EAAC,GAAE,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,IAAI,OAAM;AAAC,QAAG,CAAC,KAAK,gBAAgB,QAAO;AAAK,YAAO,KAAK,gBAAgB,MAAK;AAAA,MAAC,KAAI;AAAA,MAAgB,KAAI;AAAU,eAAM;AAAA,MAAO,KAAI;AAAA,MAAM,KAAI;AAAe,eAAM;AAAA,MAAY,KAAI;AAAA,MAAU,KAAI;AAAa,eAAM;AAAA,MAAU,KAAI;AAAA,MAAU,KAAI;AAAU,eAAO,KAAK,gBAAgB;AAAA,MAAa;AAAQ,QAAAD,GAAE,KAAK,eAAe;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,aAAaC,IAAE;AAAC,SAAK,QAAQ,OAAK,CAAC,GAAE,KAAK,QAAQ,KAAK,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,SAAK,QAAQ,OAAK,CAAC,GAAE,KAAK,QAAQ,OAAK,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,QAAQ,KAAK,SAAO;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,QAAQ,KAAK,SAAO;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,OAAO,GAAE,KAAK,MAAM,GAAE,KAAK,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,YAAU,MAAG,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,SAAQ;AAJ5pC;AAI6pC,eAAK,oBAAL,mBAAsB;AAAA,EAAO;AAAA,EAAC,mBAAkB;AAAC,UAAMA,KAAE,KAAK;AAAgB,IAAAA,OAAI,UAAQA,GAAE,QAAM,cAAYA,GAAE,QAAM,oBAAkBA,GAAE,QAAMA,GAAE,QAAQ;AAAA,EAAE;AAAA,EAAC,IAAI,KAAKA,IAAE;AAAC,SAAK,KAAK,QAAQ,MAAI;AAAC,WAAK,QAAQ,KAAGA,GAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,KAAKA,IAAE;AAAC,SAAK,KAAK,QAAQ,MAAI;AAAC,WAAK,QAAQ,KAAGA,GAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAEA,KAAE,EAAE,CAACG,GAAE,6CAA6C,CAAC,GAAEH,EAAC;AAAE,IAAIC,MAAE,cAAcD,GAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEC,IAAE,WAAU,mBAAkB,MAAM,GAAEA,MAAE,EAAE,CAACE,GAAE,mDAAmD,CAAC,GAAEF,GAAC;AAAE,IAAIG,KAAE,cAAcJ,GAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEI,GAAE,WAAU,mBAAkB,MAAM,GAAEA,KAAE,EAAE,CAACD,GAAE,mDAAmD,CAAC,GAAEC,EAAC;;;ACAwZ,IAAM,IAAE,EAAC,UAAS,EAAC;AAAnB,IAAqB,IAAE,EAAC,gBAAe,EAAC,eAAc,SAAQ,gBAAe,QAAO,iBAAgB,OAAM,GAAE,uBAAsB,MAAG,gBAAe,MAAG,eAAc,MAAG,0BAAyB,MAAG,qBAAoB,OAAG,mBAAkB,MAAG,SAAQ,MAAG,MAAK,YAAW;AAAE,IAAI,IAAE,cAAcC,GAAE,gBAAe;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,GAAE,KAAK,WAAS,IAAIA,MAAE,KAAK,yBAAuB,IAAI,EAAE,EAAC,UAAS,QAAO,UAAS,MAAG,OAAM,eAAc,CAAC,GAAE,KAAK,mBAAiB,MAAK,KAAK,eAAa,IAAIA,MAAE,KAAK,mBAAiB,MAAK,KAAK,mBAAiB,MAAK,KAAK,qBAAmB,MAAK,KAAK,iBAAe,MAAG,KAAK,eAAa,IAAIC,MAAE,KAAK,QAAM,MAAK,KAAK,cAAY,IAAIC,GAAE,EAAC,OAAM,UAAS,MAAK,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,SAAQ,EAAC,OAAM,CAAC,IAAG,IAAG,EAAE,GAAE,OAAM,EAAC,EAAC,CAAC,GAAE,KAAK,gBAAc,IAAI,EAAE,EAAC,OAAM,CAAC,KAAI,KAAI,KAAI,GAAE,GAAE,SAAQ,EAAC,OAAM,CAAC,IAAG,IAAG,EAAE,GAAE,OAAM,EAAC,EAAC,CAAC,GAAE,KAAK,iBAAe,IAAIC,GAAE,EAAC,OAAM,CAAC,KAAI,KAAI,KAAI,CAAC,GAAE,OAAM,EAAC,CAAC,GAAE,KAAK,mBAAiB,MAAK,KAAK,iBAAe,IAAIC,MAAE,KAAK,iBAAe,IAAI,KAAE,KAAK,uBAAqB,MAAG,KAAK,oBAAkB,IAAIF,GAAE,EAAC,MAAK,IAAG,OAAM,CAAC,GAAE,KAAI,KAAI,GAAE,GAAE,SAAQ,EAAC,OAAM,SAAQ,OAAM,EAAC,EAAC,CAAC,GAAE,KAAK,sBAAoB,IAAI,EAAE,EAAC,OAAM,CAAC,IAAG,KAAI,KAAI,GAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,OAAM,CAAC,IAAG,KAAI,GAAG,GAAE,OAAM,EAAC,EAAC,CAAC,GAAE,KAAK,uBAAqB,IAAIC,GAAE,EAAC,OAAM,CAAC,IAAG,KAAI,GAAG,GAAE,OAAM,EAAC,CAAC,GAAE,KAAK,eAAa,IAAID,GAAE,EAAC,OAAM,UAAS,MAAK,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,SAAQ,EAAC,OAAM,CAAC,IAAG,IAAG,EAAE,GAAE,OAAM,EAAC,EAAC,CAAC,GAAE,KAAK,+BAA6B,MAAK,KAAK,4BAA0B,MAAK,KAAK,2BAAyB,MAAK,KAAK,uBAAqB,GAAE,KAAK,uBAAqB,GAAE,KAAK,kBAAgB,IAAIH;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,SAAS,IAAI,CAACM,GAAG,MAAE;AAJvsI;AAIysI,8BAAK,SAAL,mBAAW,QAAX,mBAAgB;AAAA,OAAQ,UAAU,CAAAL,OAAG;AAAC,MAAAA,GAAE,QAAQ,SAAS,KAAK,KAAK,KAAG,KAAK,OAAO;AAAA,IAAC,CAAE,GAAEK,GAAG,MAAE;AAJryI;AAIuyI,wBAAK,UAAL,mBAAY;AAAA,OAAU,UAAU,CAAAL,OAAG;AAAC,UAAG,EAAE,KAAK,gBAAgB,EAAE,YAAUM,MAAKN,GAAE,QAAQ,MAAK,eAAe,SAASM,EAAC,MAAI,KAAK,eAAe,SAAO,IAAE,KAAK,iBAAiB,oBAAoBA,EAAC,IAAE,KAAK,iBAAiB,OAAO;AAAA,IAAE,CAAE,GAAEC,GAAG,MAAE;AAJphJ;AAIshJ,yBAAK,UAAL,mBAAY,kBAAe;AAAA,OAAO,CAAAP,OAAG;AAAC,MAAAA,OAAI,KAAK,uBAAuB,kBAAgB,KAAK,OAAO,GAAE,KAAK,uBAAuB,gBAAcA;AAAA,IAAE,GAAGQ,EAAC,GAAED,GAAG,MAAI,KAAK,MAAO,CAAAP,OAAG;AAAC,QAAE,KAAK,gBAAgB,GAAEA,OAAI,KAAK,mBAAiB,IAAIQ,GAAE,EAAC,MAAKR,IAAE,SAAQ,KAAK,gBAAe,CAAC,GAAE,SAAOA,GAAE,OAAK,OAAO,4BAA4C,IAAE,SAAOA,GAAE,SAAO,OAAO,4BAA4C,GAAE,OAAO,mCAA8C;AAAA,IAAG,GAAGQ,EAAC,GAAED,GAAG,MAAE;AAJ9+J;AAIg/J,wBAAK,SAAL,mBAAW;AAAA,OAAmB,CAACP,IAAEM,OAAI;AAAC,MAAAN,MAAGM,MAAG,CAACN,GAAE,OAAOM,EAAC,KAAG,KAAK,OAAO;AAAA,IAAC,CAAE,CAAC,CAAC,GAAEH,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,OAAO,GAAE,KAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,eAAa,EAAE,KAAK,YAAY,GAAE,KAAK,oBAAoB,GAAE,KAAK,mBAAiB,EAAE,KAAK,gBAAgB,GAAE,KAAK,KAAK,QAAO,IAAI,GAAE,KAAK,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAoB;AAJjzK;AAIkzK,WAAM,WAAO,UAAK,SAAL,mBAAW,QAAK,SAAO;AAAA,EAAW;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,eAAa,KAAG,EAAE,KAAK,gBAAgB,KAAG,KAAK,iBAAiB;AAAA,EAAQ;AAAA,EAAC,IAAI,aAAY;AAJt9K;AAIu9K,aAAO,UAAK,qBAAL,mBAAuB,SAAM;AAAA,EAAI;AAAA,EAAC,IAAI,kBAAiB;AAJrhL;AAIshL,aAAO,UAAK,qBAAL,mBAAuB,oBAAiB;AAAA,EAAI;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAM,CAAC,EAAE,KAAK,eAAe,KAAG,cAAY,KAAK,gBAAgB,QAAM,cAAY,KAAK,gBAAgB,OAAK,KAAK,KAAK,eAAe,IAAEG,GAAE,KAAK,gBAAgB,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,KAAK,sBAAsB;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAqBN,IAAE;AAAC,SAAK,KAAK,wBAAuB,EAAC,GAAG,GAAE,GAAGA,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,KAAK,sBAAsB;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAqBA,IAAE;AAAC,SAAK,KAAK,wBAAuB,EAAC,GAAG,GAAE,GAAGA,IAAE,gBAAe,EAAC,GAAG,EAAE,gBAAe,GAAGA,MAAA,gBAAAA,GAAG,eAAc,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAgBA,IAAE;AAAC,MAAE,KAAK,gBAAgB,MAAI,KAAK,iBAAiB,UAAQA,KAAG,KAAK,KAAK,mBAAkBA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAJztM;AAI0tM,UAAMA,KAAE,EAAE,GAAC,UAAK,SAAL,mBAAW,UAAO,CAAC,KAAK,QAAOM,KAAE,KAAK;AAAiB,WAAON,MAAGM,KAAE,WAASN,KAAE,UAAQ;AAAA,EAAU;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,KAAKA,IAAE;AAAC,UAAMM,KAAE,KAAK,KAAK,MAAM;AAAE,QAAGA,IAAE;AAAC,YAAK,EAAC,WAAUN,IAAE,KAAIS,GAAC,IAAEH;AAAE,MAAAN,OAAIM,GAAE,SAAO,OAAMG,MAAGA,GAAE,OAAO,KAAK,sBAAsB,GAAE,KAAK,aAAa,UAAU,GAAE,KAAK,OAAO;AAAA,IAAC;AAAC,UAAMA,KAAE;AAAa,SAAK,SAAS,OAAOA,EAAC,GAAET,MAAG,KAAK,SAAS,IAAIU,GAAG,MAAIV,GAAE,OAAQ,CAAAM,OAAG;AAAC,WAAK,aAAa,UAAU,GAAEA,MAAG,KAAK,aAAa,IAAI,KAAK,qBAAqBN,EAAC,CAAC;AAAA,IAAC,GAAGQ,EAAC,GAAEC,EAAC,GAAE,KAAK,KAAK,QAAOT,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,+BAA6B,EAAE,KAAK,4BAA4B,GAAE,KAAK,4BAA0B,EAAE,KAAK,yBAAyB,GAAE,KAAK,oBAAkB,KAAK,iBAAiB,OAAO;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,oBAAkB,KAAK,iBAAiB,SAAS;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAK,EAAC,OAAMA,IAAE,gBAAeM,GAAC,IAAE;AAAK,QAAG,aAAWN,MAAGM,GAAE,QAAO;AAAC,YAAK,EAAC,YAAWN,IAAE,OAAMS,GAAC,IAAE,MAAKE,KAAEL,GAAE,QAAQ;AAAE,MAAAG,GAAE,WAAWE,EAAC,GAAE,KAAK,OAAO,GAAE,KAAK,iBAAiB,EAAC,UAASA,IAAE,MAAKX,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAG,aAAW,KAAK,SAAO,KAAK,eAAe,QAAO;AAAC,YAAMA,KAAE,KAAK,eAAe,IAAK,CAAAA,OAAGA,GAAE,MAAM,CAAE,EAAE,QAAQ;AAAE,aAAO,KAAK,MAAM,QAAQA,EAAC,GAAE,KAAK,KAAK,aAAY,EAAC,UAASA,IAAE,MAAK,YAAW,CAAC,GAAEA;AAAA,IAAC;AAAC,WAAM,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOA,IAAEM,IAAE;AAAC,SAAK,OAAO,GAAE,MAAM,KAAK,eAAe;AAAE,UAAK,EAAC,MAAKG,IAAE,OAAME,GAAC,IAAE;AAAK,QAAG,CAACF,MAAG,eAAa,KAAK,MAAM,OAAME,MAAG,KAAK,iBAAiB,GAAEN,GAAE;AAAE,QAAG,EAAEI,GAAE,UAAU,MAAIA,GAAE,aAAW,OAAM,CAACT,GAAE,QAAO,KAAK,KAAK,UAAU,4BAA2B,2BAA2B;AAAE,IAAAM,GAAEG,IAAE,KAAK,sBAAsB;AAAE,UAAMJ,KAAE,MAAM,KAAK,sBAAsBL,IAAEM,EAAC;AAAE,QAAG,EAAED,EAAC,KAAG,KAAK,UAAU,QAAO,KAAKI,GAAE,IAAI,OAAO,KAAK,sBAAsB;AAAE,UAAMG,KAAE,MAAI;AAAC,UAAGP,OAAI,KAAK,kBAAiB;AAAC,cAAMC,KAAE,KAAK,eAAcG,KAAE,KAAK,iBAAiB;AAAU,aAAK,iBAAiB,QAAQ,GAAE,KAAK,mBAAiB,MAAK,KAAK,KAAK,iBAAgB,IAAI,GAAE,KAAK,QAAM,KAAK,KAAK,OAAK,KAAK,KAAK,IAAI,OAAO,KAAK,sBAAsB,GAAEJ,GAAE,aAAW,QAAMC,MAAGK,GAAE,IAAIL,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,SAAQA,IAAE,OAAMG,KAAE,WAAS,YAAW,MAAKT,IAAE,eAAc,MAAK,MAAK,SAAQ,CAAC;AAAA,MAAC;AAAA,IAAC;AAAE,IAAAK,GAAE,GAAG,YAAWO,EAAC,GAAE,KAAK,mBAAiBP,IAAEI,GAAE,SAAOA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOT,IAAEM,IAAE;AAAC,SAAK,OAAO,GAAE,MAAM,KAAK,eAAe;AAAE,UAAK,EAAC,OAAMG,IAAE,MAAKE,IAAE,OAAMN,GAAC,IAAE;AAAK,QAAG,CAACM,MAAG,eAAaN,GAAE,OAAMI,MAAG,KAAK,iBAAiB,GAAEJ,GAAE;AAAE,MAAEM,GAAE,UAAU,MAAIA,GAAE,aAAW;AAAM,UAAMC,KAAE,MAAM,QAAQZ,EAAC,IAAEA,KAAE,CAACA,EAAC;AAAE,QAAG,QAAMA,MAAG,CAACY,MAAG,CAACA,GAAE,OAAO,QAAO,KAAK,KAAK,UAAU,4BAA2B,+BAA+B;AAAE,QAAGA,GAAE,KAAM,CAAAZ,OAAGA,GAAE,UAAQS,MAAG,KAAK,UAAU,4BAA2B,6FAA6F,GAAE,QAAI,CAAC,CAAC,EAAET,GAAE,QAAQ,MAAI,KAAK,UAAU,4BAA2B,kFAAkF,GAAE,KAAI,EAAE;AAAO,UAAMa,KAAE,MAAM,KAAK,sBAAsBD,IAAEN,EAAC;AAAE,SAAK,aAAW,EAAEO,EAAC,KAAG,GAAGA,EAAC,MAAIP,GAAEK,IAAE,KAAK,sBAAsB,GAAE,KAAK,0BAA0BE,IAAEP,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAASM,IAAE,OAAM,SAAQ,SAAQ,OAAG,MAAKC,GAAE,MAAK,eAAc,MAAK,MAAK,SAAQ,CAAC;AAAA,EAAE;AAAA,EAAC,MAAM,wBAAwBb,IAAE;AAAC,UAAMM,KAAE,KAAK;AAAK,QAAGA,IAAE;AAAC,WAAK,qBAAqB,GAAEN,KAAE,MAAM,QAAQA,EAAC,IAAEA,KAAE,CAACA,EAAC;AAAE,iBAAUS,MAAKT,GAAE,GAAES,GAAE,QAAQ,KAAG,WAASA,GAAE,SAAS,QAAM,CAAC,EAAEA,GAAE,SAAS,kBAAiBH,GAAE,gBAAgB,MAAI,GAAEG,GAAE,SAAS,kBAAiBH,GAAE,gBAAgB,KAAG,GAAE,KAAG,MAAM,GAAE,GAAEG,GAAE,WAAS,GAAEA,GAAE,UAASH,GAAE,gBAAgB;AAAG,WAAK,mBAAmB;AAAA,IAAC,MAAM,MAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,OAAM;AAJniT;AAIoiT,SAAK,QAAQ,OAAG,UAAK,qBAAL,mBAAuB;AAAA,EAAM;AAAA,EAAC,OAAM;AAJxlT;AAIylT,SAAK,QAAQ,OAAG,UAAK,qBAAL,mBAAuB;AAAA,EAAM;AAAA,EAAC,UAAS;AAAC,WAAM,EAAE,CAAC,KAAK,oBAAkB,CAAC,KAAK,iBAAiB,QAAQ;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,WAAM,EAAE,CAAC,KAAK,oBAAkB,CAAC,KAAK,iBAAiB,QAAQ;AAAA,EAAE;AAAA,EAAC,mBAAkB;AAAC,SAAK,oBAAkB,KAAK,iBAAiB,cAAY,KAAK,iBAAiB,WAAW;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaN,IAAE;AAAC,UAAMM,KAAE,KAAK;AAAK,QAAG,CAACA,GAAE,QAAO,KAAK,gBAAgB,GAAE;AAAK,QAAG,SAAOA,GAAE,MAAK;AAAC,YAAMG,KAAE,CAAC;AAAE,MAAAH,GAAE,IAAI,UAAU,QAAS,CAAAN,OAAG;AAAC,0BAAgBA,GAAE,QAAM,cAAYA,GAAE,QAAMS,GAAE,KAAKT,EAAC;AAAA,MAAC,CAAE;AAAE,YAAMW,KAAE,MAAML,GAAE,QAAQN,IAAE,EAAC,SAAQS,GAAC,CAAC;AAAE,aAAOE,GAAEA,GAAE,OAAO;AAAA,IAAC;AAAC,UAAMF,KAAE,CAACH,GAAE,IAAI,MAAM;AAAE,IAAAA,GAAE,IAAI,UAAU,QAAS,CAAAN,OAAG;AAAC,4BAAoBA,GAAE,QAAMS,GAAE,KAAKT,EAAC;AAAA,IAAC,CAAE;AAAE,UAAMW,KAAE,MAAML,GAAE,QAAQN,IAAE,EAAC,SAAQS,GAAC,CAAC;AAAE,QAAGE,GAAE,QAAQ,SAAO,GAAE;AAAC,YAAMX,KAAEW,GAAE,QAAQ,CAAC;AAAE,UAAG,EAAEX,EAAC,KAAG,cAAYA,GAAE,QAAMA,GAAE,YAAU,CAACW,GAAE,OAAO,YAAUL,GAAE,IAAI,OAAO,UAAQ,KAAGK,GAAE,OAAO,WAAS,EAAEX,GAAE,UAAS,CAAC,IAAE,CAAC,KAAK,IAAI,IAAEW,GAAE,OAAO,UAAS,aAAWL,GAAE,cAAY,EAAEA,GAAE,mBAAmB,gBAAgB,EAAE,SAAOA,GAAE,mBAAmB,eAAa,OAAO,iBAAiB,GAAG,QAAON;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,qBAAqBA,IAAE;AAAC,WAAM,CAACA,GAAE,GAAG,mBAAmB,OAAMM,OAAG;AAJ7qV;AAI8qV,YAAMG,KAAE,aAAW,KAAK,SAAO,eAAW,UAAK,qBAAL,mBAAuB;AAAK,UAAG,eAAa,KAAK,SAAOA,MAAG,CAAC,KAAK,qBAAqB;AAAO,WAAK,qBAAqB;AAAE,YAAME,KAAE,MAAML,GAAE,MAAO,MAAI,KAAK,aAAaP,GAAEO,EAAC,CAAC,CAAE;AAAE,UAAID,KAAE;AAAK,UAAG,EAAEM,EAAC,GAAE;AAAC,cAAMF,KAAEE,GAAE;AAAQ,aAAK,eAAe,SAASF,EAAC,KAAGA,GAAE,UAAQ,KAAK,SAAOH,GAAE,gBAAgB,GAAED,KAAEI,MAAG,SAAOT,GAAE,QAAM,KAAK,oBAAoBS,EAAC,KAAG,aAAW,KAAK,SAAO,KAAK,OAAO;AAAA,MAAC,MAAK,cAAW,KAAK,SAAO,KAAK,OAAO;AAAE,QAAEJ,EAAC,KAAG,CAAC,KAAK,eAAe,SAASA,EAAC,KAAG,MAAM,KAAK,OAAO,CAACA,EAAC,GAAE,EAAC,GAAG,KAAK,sBAAqB,gBAAe,EAAC,GAAG,KAAK,qBAAqB,eAAc,EAAC,CAAC,GAAE,KAAK,mBAAmB;AAAA,IAAC,GAAG,EAAE,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsBL,IAAEM,IAAE;AAAC,UAAMG,KAAE,KAAK;AAAK,QAAG,CAACA,GAAE,QAAO,KAAK,gBAAgB,GAAE;AAAK,UAAME,KAAE,EAAC,MAAK,SAAOF,GAAE,MAAK,GAAG,KAAK,sBAAqB,GAAGH,GAAC,GAAED,KAAE,MAAM,KAAK,sBAAsBL,IAAES,IAAEE,EAAC;AAAE,WAAO,EAAEN,EAAC,IAAE,QAAMI,GAAE,MAAM,IAAIJ,EAAC,GAAEI,GAAE,aAAWJ,IAAE,KAAK,4BAA4BA,EAAC;AAAA,EAAE;AAAA,EAAC,MAAM,sBAAsBL,IAAEM,IAAEG,IAAE;AAAC,QAAG,iBAAeT,MAAG,SAAOM,GAAE,KAAK,QAAO,KAAK,UAAU,iBAAgB,uDAAuD,GAAE;AAAK,QAAG,CAACA,GAAE,QAAO,KAAK,gBAAgB,GAAE;AAAK,UAAMK,KAAE,gBAAcX,IAAEK,KAAE,gBAAcL,IAAEY,KAAE,EAAC,MAAKN,IAAE,MAAK,gBAAcN,MAAG,aAAWA,KAAE,WAAS,SAAQ,GAAGS,IAAE,aAAY,OAAG,cAAaT,IAAE,eAAc,KAAK,0BAA0BA,EAAC,GAAE,iBAAgB,KAAK,kBAAiB,kBAAiBK,IAAE,UAASM,GAAC;AAAE,WAAM,SAAOL,GAAE,OAAK,KAAK,uBAAuBM,EAAC,IAAE,KAAK,uBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,uBAAuBZ,IAAE;AAAC,UAAMM,KAAE,MAAM,KAAK,eAAe,OAAO,4BAA4C,CAAC;AAAE,WAAO,GAAGA,EAAC,KAAG,KAAK,YAAU,OAAK,IAAIA,GAAE,OAAO,kBAAkB,EAAC,GAAGN,IAAE,oBAAmB,KAAK,oBAAmB,uBAAsB,KAAK,cAAa,kBAAiB,KAAK,kBAAiB,kBAAiB,GAAGA,GAAE,YAAY,IAAE,KAAK,mBAAiB,MAAK,gBAAe,KAAK,eAAc,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,uBAAuBA,IAAE;AAAC,UAAMM,KAAE,MAAM,KAAK,eAAe,OAAO,4BAA4C,CAAC;AAAE,QAAG,GAAGA,EAAC,KAAG,KAAK,UAAU,QAAO;AAAK,UAAK,EAAC,eAAcG,GAAC,IAAE,KAAK;AAAM,WAAO,IAAIH,GAAE,OAAO,kBAAkB,EAAC,GAAGN,IAAE,eAAcS,IAAE,aAAY,CAAC,EAAEA,EAAC,KAAG,sBAAoBA,GAAE,MAAK,cAAa,KAAK,cAAa,gBAAe,KAAK,eAAc,CAAC;AAAA,EAAC;AAAA,EAAC,4BAA4BT,IAAE;AAAC,UAAMM,KAAE,KAAK;AAAK,QAAG,CAACA,GAAE,QAAO,KAAK,gBAAgB,GAAE;AAAK,QAAIG,KAAE;AAAK,UAAME,KAAEX,GAAE,kBAAiBK,KAAEL,GAAE,UAASY,KAAE,CAACN,GAAE,GAAG,YAAY,CAAAA,OAAG;AAAC,UAAGA,GAAE,QAAMA,GAAE,IAAI,CAAAA,GAAE,gBAAgB,GAAEA,GAAE,WAASN,GAAE,UAAQ;AAAA,eAAYM,GAAE,QAAMA,GAAE,SAAS,CAAAA,GAAE,gBAAgB,GAAEN,GAAE,wBAAwB;AAAA,eAAUM,GAAE,QAAMA,GAAE,aAAWA,GAAE,OAAO,CAAAA,GAAE,QAAMA,GAAE,QAAMA,GAAE,gBAAgB,GAAEO,GAAE,KAAK,KAAGP,GAAE,QAAMA,GAAE,QAAMA,GAAE,gBAAgB,GAAEO,GAAE,KAAK,KAAGP,GAAE,QAAMA,GAAE,cAAY,gBAAcN,GAAE,gBAAc,aAAWA,GAAE,gBAAcM,GAAE,SAAOA,GAAE,QAAMA,GAAE,WAASA,GAAE,WAASN,GAAE,WAAS,CAACK,IAAEC,GAAE,gBAAgB,OAAKN,GAAE,mBAAiB,CAACW,IAAEL,GAAE,gBAAgB;AAAA,WAAO;AAAC,cAAMG,KAAET,GAAE,cAAc;AAAa,uBAAaS,MAAG,cAAYA,MAAG,iBAAeA,OAAIH,GAAE,gBAAgB,GAAEN,GAAE,cAAc,mBAAmB;AAAA,MAAE;AAAA,IAAC,GAAG,EAAE,MAAM,GAAEM,GAAE,GAAG,UAAU,CAAAA,OAAG;AAAC,MAAAA,GAAE,QAAMA,GAAE,MAAIN,GAAE,UAAQ,OAAGM,GAAE,QAAMA,GAAE,cAAY,gBAAcN,GAAE,gBAAc,aAAWA,GAAE,eAAaM,GAAE,QAAMA,GAAE,WAASN,GAAE,WAASK,IAAEC,GAAE,gBAAgB,MAAIN,GAAE,mBAAiBW,IAAEL,GAAE,gBAAgB;AAAA,IAAE,GAAG,EAAE,MAAM,GAAEN,GAAE,GAAG,cAAc,CAAAM,OAAG;AAAC,cAAOG,KAAE,EAAEA,EAAC,IAAE,UAAQ,UAASH,GAAE,WAAU;AAAA,QAAC,KAAI;AAAQ,eAAK,KAAK,UAAS,EAAC,SAAQA,GAAEN,GAAE,OAAO,GAAE,OAAMS,IAAE,MAAK,KAAK,YAAW,eAAcH,IAAE,MAAK,SAAQ,CAAC;AAAE;AAAA,QAAM,KAAI;AAAO,eAAK,eAAe,EAAC,UAAS,CAACA,GAAEN,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,aAAY,CAAC;AAAE;AAAA,QAAM,KAAI;AAAO,eAAK,eAAe,EAAC,UAAS,CAACM,GAAEN,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,aAAY,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAEA,GAAE,GAAG,iBAAiB,CAAAM,OAAG;AAAC,MAAAN,GAAE,cAAc,uBAAqB,KAAG,KAAK,KAAK,UAAS,EAAC,SAAQM,GAAEN,GAAE,OAAO,GAAE,OAAM,UAAS,MAAK,KAAK,YAAW,eAAc,EAAC,aAAYM,GAAE,SAAS,CAAC,EAAE,aAAY,MAAK,gBAAe,GAAE,MAAK,SAAQ,CAAC;AAAA,IAAC,CAAE,GAAEN,GAAE,GAAG,iBAAiB,CAAAM,OAAG;AAAC,cAAOA,GAAE,WAAU;AAAA,QAAC,KAAI;AAAQ,eAAK,KAAK,UAAS,EAAC,SAAQA,GAAEN,GAAE,OAAO,GAAE,OAAM,UAAS,MAAK,KAAK,YAAW,eAAcM,IAAE,MAAK,SAAQ,CAAC;AAAE;AAAA,QAAM,KAAI;AAAO,eAAK,eAAe,EAAC,UAAS,CAACA,GAAEN,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,aAAY,CAAC;AAAE;AAAA,QAAM,KAAI;AAAO,eAAK,eAAe,EAAC,UAAS,CAACM,GAAEN,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,aAAY,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAEA,GAAE,GAAG,YAAY,CAAAA,OAAG;AAAC,WAAK,KAAK,iBAAgBM,GAAEN,GAAE,OAAO,CAAC,GAAES,KAAE,YAAWT,GAAE,UAAQa,MAAGA,GAAE,OAAO,IAAEA,MAAGA,GAAE,SAAS;AAAA,IAAC,CAAE,GAAEN,GAAG,MAAI,KAAK,0BAA0BP,GAAE,YAAY,GAAI,CAAAM,OAAG;AAAC,MAAAN,GAAE,gBAAcM;AAAA,IAAC,CAAE,CAAC,GAAEO,KAAE,IAAId,IAAE,EAAC,iBAAgBC,IAAE,MAAKA,GAAE,cAAa,MAAK,UAAS,OAAM,MAAI;AAJjje;AAIkje,MAAAY,GAAE,QAAS,CAAAZ,OAAGA,GAAE,OAAO,CAAE,GAAEY,GAAE,SAAO,IAAE,KAAAN,GAAE,UAAF,mBAAS,OAAON;AAAA,IAAE,GAAE,MAAK,MAAI;AAAC,MAAAA,GAAE,WAASA,GAAE,KAAK;AAAA,IAAC,GAAE,MAAK,MAAI;AAAC,MAAAA,GAAE,WAASA,GAAE,KAAK;AAAA,IAAC,GAAE,SAAQ,MAAIA,GAAE,SAAQ,SAAQ,MAAIA,GAAE,QAAO,CAAC;AAAE,WAAOa;AAAA,EAAC;AAAA,EAAC,0BAA0Bb,IAAE;AAAC,YAAOA,IAAE;AAAA,MAAC,KAAI;AAAA,MAAQ,KAAI;AAAa,eAAO,KAAK;AAAA,MAAY,KAAI;AAAW,eAAO,KAAK;AAAA,MAAe,KAAI;AAAA,MAAS,KAAI;AAAA,MAAY,KAAI;AAAU,eAAO,KAAK;AAAA,IAAa;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsBA,IAAEM,IAAE;AAAC,UAAK,EAAC,OAAMG,IAAE,MAAKE,GAAC,IAAE;AAAK,QAAG,CAACA,GAAE,QAAO,KAAK,gBAAgB,GAAE;AAAK,UAAMN,KAAE,EAAC,MAAK,KAAK,oBAAmB,GAAG,KAAK,sBAAqB,GAAGC,IAAE,gBAAe,EAAC,GAAG,KAAK,qBAAqB,gBAAe,GAAGA,MAAA,gBAAAA,GAAG,eAAc,EAAC;AAAE,QAAIM,KAAEP,GAAE;AAAK,eAAUQ,MAAKb,GAAE,CAAAS,GAAE,OAAOI,EAAC,GAAEJ,GAAE,IAAII,EAAC;AAAE,QAAG,SAAOF,GAAE,MAAK;AAAC,UAAG,MAAIX,GAAE,OAAO,QAAO;AAAK,cAAOY,IAAE;AAAA,QAAC,KAAI;AAAO,iBAAO,KAAK,sBAAsBZ,IAAEK,IAAEM,IAAEC,EAAC;AAAA,QAAE,KAAI,WAAU;AAAC,cAAGZ,GAAE,SAAO,EAAE,QAAO,KAAK,UAAU,2BAA0B,uDAAuD,GAAE;AAAK,gBAAMM,KAAEC,GAAEP,GAAE,CAAC,CAAC;AAAE,iBAAOM,OAAIQ,GAAE,YAAU,KAAK,yBAAyBd,GAAE,CAAC,GAAEK,IAAEM,EAAC,KAAG,KAAK,UAAU,kBAAiB,4DAA4DI,GAAET,EAAC,CAAC,IAAI,GAAE;AAAA,QAAK;AAAA,QAAC,KAAI;AAAY,iBAAO,KAAK,kCAAkCN,IAAEK,IAAEM,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,YAAOC,IAAE;AAAA,MAAC,KAAI;AAAO,eAAO,KAAK,sBAAsBZ,IAAEK,IAAEM,EAAC;AAAA,MAAE,KAAI,WAAU;AAAC,YAAGX,GAAE,SAAO,EAAE,QAAO,KAAK,UAAU,2BAA0B,uDAAuD,GAAE;AAAK,cAAMM,KAAEC,GAAEP,GAAE,CAAC,CAAC;AAAE,eAAOM,OAAIQ,GAAE,YAAU,KAAK,oCAAoCd,IAAEY,IAAEP,IAAEM,EAAC,KAAG,KAAK,UAAU,kBAAiB,4DAA4DI,GAAET,EAAC,CAAC,IAAI,GAAE;AAAA,MAAK;AAAA,MAAC,KAAI;AAAY,YAAG,MAAIN,GAAE,QAAO;AAAC,gBAAMM,KAAE,EAAEN,GAAE,CAAC,EAAE,UAAS,MAAM;AAAE,sBAAUM,MAAG,iBAAeA,OAAIM,KAAE;AAAA,QAAU;AAAC,eAAO,KAAK,oCAAoCZ,IAAEY,IAAEP,IAAEM,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsBX,IAAEM,IAAEG,IAAEE,IAAEN,KAAE,OAAG;AAAC,eAAUE,MAAKP,IAAE;AAAC,YAAMA,KAAE,EAAEO,EAAC;AAAE,UAAGP,OAAIc,GAAE,UAAU,QAAO,KAAK,UAAU,eAAc,yDAAyDC,GAAEf,EAAC,CAAC,IAAI,GAAE;AAAA,IAAI;AAAC,UAAMY,KAAE,MAAM,KAAK,eAAe,OAAO,4BAA4C,CAAC;AAAE,QAAG,GAAGA,EAAC,EAAE,QAAOA;AAAE,UAAMC,KAAE,IAAID,GAAE,OAAO,gBAAgB,EAAC,MAAKH,IAAE,SAAQH,GAAE,SAAQ,iBAAgB,KAAK,kBAAiB,gBAAe,KAAK,eAAc,CAAC;AAAE,IAAAG,GAAE,MAAM,IAAII,EAAC,GAAEA,GAAE,SAAS,QAAQb,EAAC,GAAEK,MAAG,KAAK,eAAe,QAAQL,EAAC;AAAE,UAAMD,MAAE,CAAC,GAAEK,KAAE,IAAIO,GAAE,EAAC,iBAAgBE,IAAE,MAAKF,IAAE,MAAK,UAAS,OAAM,MAAI;AAJ11iB;AAI21iB,MAAAZ,IAAE,QAAS,CAAAC,OAAGA,GAAE,OAAO,CAAE,GAAED,IAAE,SAAO,IAAE,KAAAU,GAAE,UAAF,mBAAS,OAAOI,KAAGA,GAAE,aAAWA,GAAE,QAAQ;AAAA,IAAC,GAAE,MAAK,MAAI;AAAC,SAAGT,IAAE,KAAK,eAAe,QAAQ,CAAC,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKO,GAAC,CAAC;AAAA,IAAC,GAAE,MAAK,MAAI;AAAC,SAAGP,IAAE,KAAK,eAAe,QAAQ,CAAC,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKO,GAAC,CAAC;AAAA,IAAC,GAAE,gBAAe,CAAAX,OAAG;AAAC,WAAK,eAAe,KAAKA,EAAC,GAAEa,GAAE,SAAS,KAAKb,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAACA,EAAC,GAAE,SAAQ,CAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC;AAAA,IAAC,GAAE,qBAAoB,CAAAA,OAAG;AAAC,YAAMM,KAAE,KAAK,eAAe,QAAQN,EAAC;AAAE,MAAAI,GAAE,QAAQ,KAAK,QAAS,CAAAJ,OAAGA,GAAE,QAAQ,OAAOM,IAAE,CAAC,CAAE,GAAEF,GAAE,QAAQ,KAAK,QAAS,CAAAJ,OAAGA,GAAE,QAAQ,OAAOM,IAAE,CAAC,CAAE,GAAE,KAAK,eAAe,OAAON,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAAC,GAAE,SAAQ,CAACA,EAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC,GAAE,MAAI,KAAK,eAAe,SAAOa,GAAE,SAAS,OAAOb,EAAC,IAAEI,GAAE,SAAS;AAAA,IAAC,GAAE,YAAW,YAAS;AAAC,UAAG,MAAI,KAAK,eAAe,UAAQ,UAAKE,GAAE,kBAAkB;AAAO,UAAG,gBAAcK,GAAE;AAAO,YAAMX,KAAE,KAAK,eAAe,UAAU,CAAC;AAAE,UAAGO,GAAEP,EAAC,MAAIc,GAAE,UAAU;AAAO,YAAMT,KAAE,MAAM,KAAK,yBAAyBL,IAAEM,IAAEG,IAAE,IAAE;AAAE,SAAGJ,EAAC,MAAID,GAAE,MAAM,GAAEA,GAAE,QAAQ,GAAE,KAAK,0BAA0BC,IAAEC,EAAC;AAAA,IAAE,EAAC,CAAC;AAAE,WAAOP,IAAE,KAAK,GAAG,KAAK,wBAAwBK,IAAEE,EAAC,GAAEG,GAAE,GAAG,mBAAmB,CAAAT,OAAG,KAAK,uCAAuCI,IAAEJ,IAAEM,EAAC,GAAG,EAAE,MAAM,GAAEG,GAAE,GAAG,YAAY,CAAAT,OAAG;AAAC,WAAK,yCAAyCI,IAAEJ,EAAC;AAAA,IAAC,GAAG,EAAE,MAAM,CAAC,GAAEI;AAAA,EAAC;AAAA,EAAC,kCAAkCJ,IAAEM,IAAEG,IAAEE,KAAE,OAAG;AAAC,QAAG,MAAIX,GAAE,UAAQW,GAAEX,GAAE,CAAC,CAAC,MAAIc,GAAE,WAAU;AAAC,YAAMT,KAAEL,GAAE,CAAC,GAAEY,KAAEP,GAAE;AAAS,UAAG,EAAEO,EAAC,MAAI,YAAUA,GAAE,QAAM,WAASA,GAAE,MAAM,QAAO,KAAK,gCAAgCP,IAAEC,IAAEG,EAAC;AAAE,UAAG,EAAEG,EAAC,MAAI,cAAYA,GAAE,QAAM,eAAaA,GAAE,MAAM,QAAO,KAAK,+BAA+BP,IAAEC,IAAEG,IAAEE,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK,sBAAsBX,IAAEM,IAAEG,IAAE,aAAYE,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gCAAgCX,IAAEM,IAAEG,IAAE;AAAC,UAAME,KAAE,aAAY,EAAC,gBAAeN,IAAE,eAAcO,IAAE,SAAQC,GAAC,IAAEP,IAAEP,MAAE,MAAM,KAAK,eAAe,OAAO,4BAA4C,CAAC;AAAE,QAAG,GAAGA,GAAC,EAAE,QAAOA;AAAE,UAAMK,KAAE,IAAIL,IAAE,OAAO,qBAAqB,EAAC,SAAQC,IAAE,MAAKS,IAAE,gBAAeJ,IAAE,eAAcO,IAAE,SAAQC,IAAE,iBAAgB,KAAK,kBAAiB,gBAAe,KAAK,eAAc,CAAC;AAAE,IAAAJ,GAAE,MAAM,IAAIL,EAAC,GAAE,KAAK,eAAe,IAAIJ,EAAC;AAAE,UAAMO,KAAE,CAAC,GAAES,KAAE,IAAIL,GAAE,EAAC,iBAAgBP,IAAE,MAAKO,IAAE,MAAK,UAAS,OAAM,MAAI;AAJtsnB;AAIusnB,MAAAJ,GAAE,QAAS,CAAAP,OAAGA,GAAE,OAAO,CAAE,GAAEO,GAAE,SAAO,IAAE,KAAAE,GAAE,UAAF,mBAAS,OAAOL,KAAGA,GAAE,aAAWA,GAAE,QAAQ;AAAA,IAAC,GAAE,MAAK,MAAI;AAAC,SAAGY,IAAE,KAAK,eAAe,QAAQ,CAAC,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKL,GAAC,CAAC;AAAA,IAAC,GAAE,MAAK,MAAI;AAAC,SAAGK,IAAE,KAAK,eAAe,QAAQ,CAAC,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKL,GAAC,CAAC;AAAA,IAAC,GAAE,gBAAe,OAAMX,OAAG;AAAC,WAAK,eAAe,IAAIA,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAACA,EAAC,GAAE,SAAQ,CAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC;AAAE,YAAMW,KAAE,MAAM,KAAK,sBAAsB,KAAK,eAAe,QAAQ,GAAEL,IAAEG,IAAE,aAAY,IAAE;AAAE,SAAGE,EAAC,MAAIK,GAAE,MAAM,GAAEA,GAAE,QAAQ,GAAE,KAAK,0BAA0BL,IAAEL,EAAC;AAAA,IAAE,GAAE,qBAAoB,CAAAN,OAAG;AAAC,WAAK,eAAe,OAAOA,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAAC,GAAE,SAAQ,CAACA,EAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC,GAAEgB,GAAE,SAAS;AAAA,IAAC,GAAE,YAAW,MAAI;AAAA,IAAC,EAAC,CAAC;AAAE,WAAOT,GAAE,KAAK,GAAG,KAAK,wBAAwBS,IAAEV,EAAC,GAAEG,GAAE,GAAG,mBAAmB,CAAAT,OAAG,KAAK,uCAAuCgB,IAAEhB,IAAEM,EAAC,GAAG,EAAE,MAAM,GAAEG,GAAE,GAAG,YAAY,CAAAT,OAAG;AAAC,WAAK,yCAAyCgB,IAAEhB,EAAC;AAAA,IAAC,GAAG,EAAE,MAAM,CAAC,GAAEgB;AAAA,EAAC;AAAA,EAAC,MAAM,+BAA+BhB,IAAEM,IAAEG,IAAEE,KAAE,OAAG;AAAC,UAAMN,KAAE,aAAY,EAAC,gBAAeO,IAAE,eAAcC,IAAE,SAAQd,KAAE,qBAAoBK,GAAC,IAAEE,IAAEC,KAAE,MAAM,KAAK,eAAe,OAAO,4BAA4C,CAAC;AAAE,QAAG,GAAGA,EAAC,EAAE,QAAOA;AAAE,UAAMS,KAAE,IAAIT,GAAE,OAAO,oBAAoB,EAAC,SAAQP,IAAE,MAAKS,IAAE,gBAAeG,IAAE,eAAcC,IAAE,SAAQd,KAAE,qBAAoBK,IAAE,gBAAe,KAAK,eAAc,CAAC;AAAE,IAAAK,GAAE,MAAM,IAAIO,EAAC,GAAEL,MAAG,KAAK,eAAe,IAAIX,EAAC;AAAE,UAAMC,KAAE,CAAC,GAAEgB,KAAE,IAAIN,GAAE,EAAC,iBAAgBK,IAAE,MAAKX,IAAE,MAAK,UAAS,OAAM,MAAI;AAJ/3qB;AAIg4qB,MAAAJ,GAAE,QAAS,CAAAD,OAAGA,GAAE,OAAO,CAAE,GAAEC,GAAE,SAAO,IAAE,KAAAQ,GAAE,UAAF,mBAAS,OAAOO,KAAGA,GAAE,aAAWA,GAAE,QAAQ;AAAA,IAAC,GAAE,SAAQ,MAAIA,GAAE,SAAQ,MAAK,MAAI;AAAC,MAAAA,GAAE,KAAK,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKX,GAAC,CAAC;AAAA,IAAC,GAAE,SAAQ,MAAIW,GAAE,SAAQ,MAAK,MAAI;AAAC,MAAAA,GAAE,KAAK,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKX,GAAC,CAAC;AAAA,IAAC,GAAE,gBAAe,OAAML,OAAG;AAAC,WAAK,eAAe,IAAIA,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAACA,EAAC,GAAE,SAAQ,CAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC;AAAE,YAAMW,KAAE,MAAM,KAAK,sBAAsB,KAAK,eAAe,QAAQ,GAAEL,IAAEG,IAAE,aAAY,IAAE;AAAE,SAAGE,EAAC,MAAIM,GAAE,MAAM,GAAEA,GAAE,QAAQ,GAAE,KAAK,0BAA0BN,IAAEL,EAAC;AAAA,IAAE,GAAE,qBAAoB,CAAAN,OAAG;AAAC,WAAK,eAAe,OAAOA,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAAC,GAAE,SAAQ,CAACA,EAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC,GAAEiB,GAAE,SAAS;AAAA,IAAC,GAAE,YAAW,YAAS;AAAC,UAAG,MAAI,KAAK,eAAe,UAAQ,UAAKX,GAAE,kBAAkB;AAAO,YAAMN,KAAE,KAAK,eAAe,UAAU,CAAC;AAAE,UAAGO,GAAEP,EAAC,MAAIc,GAAE,UAAU;AAAO,YAAMH,KAAE,MAAM,KAAK,yBAAyBX,IAAEM,IAAEG,IAAE,IAAE;AAAE,SAAGE,EAAC,MAAIM,GAAE,MAAM,GAAEA,GAAE,QAAQ,GAAE,KAAK,0BAA0BN,IAAEL,EAAC;AAAA,IAAE,EAAC,CAAC;AAAE,WAAOL,GAAE,KAAK,GAAG,KAAK,wBAAwBgB,IAAEX,EAAC,GAAEG,GAAE,GAAG,mBAAmB,CAAAT,OAAG,KAAK,uCAAuCiB,IAAEjB,IAAEM,EAAC,GAAG,EAAE,MAAM,GAAEG,GAAE,GAAG,YAAY,CAAAT,OAAG,KAAK,yCAAyCiB,IAAEjB,EAAC,GAAG,EAAE,MAAM,GAAES,GAAE,GAAG,YAAY,CAAAT,OAAG;AAAC,MAAAA,GAAE,QAAMM,GAAE,cAAYN,GAAE,WAASgB,GAAE,sBAAoB,CAACA,GAAE,qBAAoBhB,GAAE,gBAAgB;AAAA,IAAE,GAAG,EAAE,MAAM,GAAES,GAAE,GAAG,UAAU,CAAAT,OAAG;AAAC,MAAAA,GAAE,QAAMM,GAAE,eAAaU,GAAE,sBAAoB,CAACA,GAAE,qBAAoBhB,GAAE,gBAAgB;AAAA,IAAE,GAAG,EAAE,MAAM,CAAC,GAAEiB;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsBjB,IAAEM,IAAEG,IAAE;AAAC,UAAME,KAAE;AAAO,SAAK,eAAe,QAAQX,EAAC,GAAE,MAAM,KAAK,wBAAwBA,EAAC;AAAE,UAAMK,KAAE,MAAM,KAAK,iBAAiBL,IAAEM,IAAEG,EAAC;AAAE,QAAG,GAAGJ,EAAC,EAAE,QAAOA;AAAE,UAAMO,KAAE,IAAID,GAAE,EAAC,iBAAgBN,IAAE,MAAKM,IAAE,MAAK,UAAS,OAAM,MAAI;AAJjyuB;AAIkyuB,WAAK,sBAAsB,GAAEP,GAAE,QAAS,CAAAJ,OAAGA,GAAE,OAAO,CAAE,GAAED,IAAE,QAAS,CAAAC,OAAGA,GAAE,OAAO,CAAE,GAAEI,KAAE,CAAC,GAAEL,MAAE,CAAC,GAAEM,GAAE,QAAQ,IAAE,UAAK,2BAAL,mBAA6B,WAAW,CAAC,GAAG,KAAK,eAAe,QAAQ,CAAC;AAAA,IAAE,GAAE,MAAK,MAAI;AAAC,YAAML,KAAE,KAAK,eAAe,QAAQ;AAAE,SAAGY,IAAEZ,EAAC,GAAEY,GAAE,iBAAiB,GAAE,KAAK,eAAe,EAAC,UAASZ,IAAE,MAAKW,GAAC,CAAC;AAAA,IAAC,GAAE,MAAK,MAAI;AAAC,YAAMX,KAAE,KAAK,eAAe,QAAQ;AAAE,SAAGY,IAAEZ,EAAC,GAAEY,GAAE,iBAAiB,GAAE,KAAK,eAAe,EAAC,UAASZ,IAAE,MAAKW,GAAC,CAAC;AAAA,IAAC,GAAE,gBAAe,OAAMX,OAAG;AAAC,YAAM,KAAK,wBAAwBA,EAAC,GAAE,KAAK,eAAe,KAAKA,EAAC,GAAEK,GAAE,WAAS,KAAK,eAAe,QAAQ,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAACL,EAAC,GAAE,SAAQ,CAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC;AAAA,IAAC,GAAE,qBAAoB,CAAAA,OAAG;AAAC,YAAMM,KAAE,KAAK,eAAe,QAAQN,EAAC;AAAE,MAAAY,GAAE,QAAQ,KAAK,QAAS,CAAAZ,OAAGA,GAAE,QAAQ,OAAOM,IAAE,CAAC,CAAE,GAAEM,GAAE,QAAQ,KAAK,QAAS,CAAAZ,OAAGA,GAAE,QAAQ,OAAOM,IAAE,CAAC,CAAE,GAAE,KAAK,eAAe,OAAON,EAAC;AAAE,YAAMS,KAAE,KAAK,eAAe,QAAQ;AAAE,WAAK,KAAK,UAAS,EAAC,UAASA,IAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAAC,GAAE,SAAQ,CAACT,EAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC,GAAE,MAAI,KAAK,eAAe,SAAOK,GAAE,WAASI,KAAEG,GAAE,SAAS;AAAA,IAAC,EAAC,CAAC;AAAE,QAAIC,KAAE,OAAGd,MAAE,CAACU,GAAE,GAAG,mBAAmB,CAAAT,OAAG,KAAK,uCAAuCY,IAAEZ,IAAEM,EAAC,GAAG,EAAE,MAAM,GAAEG,GAAE,GAAG,YAAY,CAAAT,OAAG;AAAC,WAAK,yCAAyCY,IAAEZ,EAAC,GAAEA,GAAE,QAAMM,GAAE,cAAYN,GAAE,WAASa,KAAE,MAAGR,GAAE,wBAAsB,CAACA,GAAE;AAAA,IAAsB,GAAG,EAAE,MAAM,GAAEI,GAAE,GAAG,UAAU,CAAAT,OAAG;AAAC,MAAAA,GAAE,QAAMM,GAAE,cAAYO,OAAIA,KAAE,OAAGR,GAAE,wBAAsB,CAACA,GAAE;AAAA,IAAsB,GAAG,EAAE,MAAM,CAAC,GAAED,KAAE,KAAK,wBAAwBQ,IAAEN,EAAC;AAAE,WAAOM;AAAA,EAAC;AAAA,EAAC,MAAM,yBAAyBZ,IAAEM,IAAEG,IAAEE,KAAE,OAAG;AAAC,UAAMN,KAAE,WAAUO,KAAE,MAAM,KAAK,eAAe,OAAO,4BAA4C,CAAC;AAAE,QAAG,GAAGA,EAAC,EAAE,QAAOA;AAAE,UAAMC,KAAEP,GAAE,gBAAeP,MAAE,IAAIa,GAAE,OAAO,mBAAmB,EAAC,MAAKH,IAAE,SAAQT,IAAE,eAAcM,GAAE,WAAS,YAASO,MAAA,gBAAAA,GAAG,kBAAgB,cAAaP,GAAE,WAAS,YAASO,MAAA,gBAAAA,GAAG,iBAAe,mBAAkB,YAASA,MAAA,gBAAAA,GAAG,mBAAgB,eAAYA,MAAA,gBAAAA,GAAG,iBAAe,iBAAgB,aAAUA,MAAA,gBAAAA,GAAG,gBAAc,kBAAiB,cAAWA,MAAA,gBAAAA,GAAG,gBAAc,iBAAgB,KAAK,kBAAiB,cAAa,KAAK,cAAa,gBAAe,KAAK,eAAc,CAAC;AAAE,IAAAJ,GAAE,MAAM,IAAIV,GAAC,GAAEY,MAAG,KAAK,eAAe,IAAIX,EAAC;AAAE,UAAMI,KAAE,CAAC,GAAEG,KAAE,IAAII,GAAE,EAAC,iBAAgBZ,KAAE,MAAKM,IAAE,MAAK,UAAS,OAAM,MAAI;AAJvkzB;AAIwkzB,MAAAD,GAAE,QAAS,CAAAJ,OAAGA,GAAE,OAAO,CAAE,GAAEI,GAAE,SAAO,IAAE,KAAAK,GAAE,UAAF,mBAAS,OAAOV,MAAGA,IAAE,aAAWA,IAAE,QAAQ;AAAA,IAAC,GAAE,SAAQ,MAAIA,IAAE,SAAQ,MAAK,MAAI;AAAC,MAAAA,IAAE,KAAK,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKM,GAAC,CAAC;AAAA,IAAC,GAAE,SAAQ,MAAIN,IAAE,SAAQ,MAAK,MAAI;AAAC,MAAAA,IAAE,KAAK,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKM,GAAC,CAAC;AAAA,IAAC,GAAE,gBAAe,OAAML,OAAG;AAAC,WAAK,eAAe,IAAIA,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAACA,EAAC,GAAE,SAAQ,CAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC;AAAE,YAAMW,KAAE,MAAM,KAAK,sBAAsB,KAAK,eAAe,QAAQ,GAAEL,IAAEG,IAAE,aAAY,IAAE;AAAE,SAAGE,EAAC,MAAIJ,GAAE,MAAM,GAAEA,GAAE,QAAQ,GAAE,KAAK,0BAA0BI,IAAEL,EAAC;AAAA,IAAE,GAAE,qBAAoB,CAAAN,OAAG;AAAC,WAAK,eAAe,OAAOA,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAAC,GAAE,SAAQ,CAACA,EAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC,GAAEO,GAAE,SAAS;AAAA,IAAC,GAAE,YAAW,YAAS;AAAC,UAAG,UAAKD,GAAE,kBAAkB;AAAO,YAAMN,KAAE,MAAM,KAAK,kCAAkC,KAAK,eAAe,QAAQ,GAAEM,IAAEG,IAAE,IAAE;AAAE,SAAGT,EAAC,MAAIO,GAAE,MAAM,GAAEA,GAAE,QAAQ,GAAE,KAAK,0BAA0BP,IAAEM,EAAC;AAAA,IAAE,EAAC,CAAC;AAAE,WAAOF,GAAE,KAAK,GAAG,KAAK,wBAAwBG,IAAED,EAAC,GAAEG,GAAE,GAAG,mBAAmB,CAAAT,OAAG,KAAK,uCAAuCO,IAAEP,IAAEM,EAAC,GAAG,EAAE,MAAM,GAAEG,GAAE,GAAG,YAAY,CAAAT,OAAG;AAAC,WAAK,yCAAyCO,IAAEP,EAAC;AAAA,IAAC,GAAG,EAAE,MAAM,CAAC,GAAEO;AAAA,EAAC;AAAA,EAAC,MAAM,oCAAoCP,IAAEM,IAAEG,IAAEE,IAAE;AAAC,SAAK,eAAe,QAAQX,EAAC,GAAE,MAAM,KAAK,wBAAwBA,EAAC;AAAE,UAAMK,KAAE,gBAAcC,KAAE,MAAM,KAAK,QAAQN,IAAES,IAAEE,EAAC,IAAE,MAAM,KAAK,YAAYX,IAAES,IAAEE,EAAC;AAAE,QAAG,GAAGN,EAAC,EAAE,QAAOA;AAAE,UAAMO,KAAE,IAAID,GAAE,EAAC,iBAAgBN,IAAE,MAAK,UAAS,OAAM,MAAI;AAAC,MAAAN,IAAE,QAAS,CAAAC,OAAGA,GAAE,OAAO,CAAE,GAAEa,GAAE,QAAS,CAAAb,OAAGA,GAAE,OAAO,CAAE,GAAED,MAAE,CAAC,GAAEc,KAAE,CAAC,GAAED,GAAE,mBAAiB,CAACA,GAAE,gBAAgB,aAAWA,GAAE,gBAAgB,QAAQ,GAAE,KAAK,uBAAuB,WAAW,KAAK,eAAe,QAAQ,CAAC;AAAA,IAAC,GAAE,MAAK,MAAI;AAAC,SAAGA,IAAE,KAAK,eAAe,QAAQ,CAAC,GAAEA,GAAE,iBAAiB,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKA,GAAE,KAAI,CAAC;AAAA,IAAC,GAAE,MAAK,MAAI;AAAC,SAAGA,IAAE,KAAK,eAAe,QAAQ,CAAC,GAAEA,GAAE,iBAAiB,GAAE,KAAK,eAAe,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,MAAKA,GAAE,KAAI,CAAC;AAAA,IAAC,GAAE,gBAAe,OAAMZ,OAAG;AAAC,UAAIM,KAAEM,GAAE;AAAgB,UAAG,eAAYN,MAAA,gBAAAA,GAAG,OAAK;AAAC,cAAMA,KAAE,CAAC,GAAG,KAAK,gBAAeN,EAAC;AAAE,aAAK,eAAe,UAAU;AAAE,cAAMK,KAAE,MAAM,KAAK,sBAAsBC,IAAEG,IAAEE,EAAC;AAAE,YAAG,GAAGN,EAAC,EAAE;AAAO,QAAAO,GAAE,MAAM,GAAEA,GAAE,QAAQ,GAAE,KAAK,0BAA0BP,IAAEI,EAAC;AAAA,MAAC,MAAM,MAAK,eAAe,IAAIT,EAAC,GAAEM,GAAE,WAAS,KAAK,eAAe,QAAQ,GAAEA,GAAE,QAAQ,GAAEM,GAAE,aAAa;AAAE,WAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAACZ,EAAC,GAAE,SAAQ,CAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC;AAAA,IAAC,GAAE,qBAAoB,OAAMA,OAAG;AAAC,YAAMM,KAAE,KAAK,eAAe,QAAQN,EAAC;AAAE,MAAAY,GAAE,QAAQ,KAAK,QAAS,CAAAZ,OAAGA,GAAE,QAAQ,OAAOM,IAAE,CAAC,CAAE,GAAEM,GAAE,QAAQ,KAAK,QAAS,CAAAZ,OAAGA,GAAE,QAAQ,OAAOM,IAAE,CAAC,CAAE,GAAE,KAAK,eAAe,OAAON,EAAC;AAAE,YAAMS,KAAE,KAAK,eAAe,QAAQ;AAAE,UAAG,MAAIA,GAAE,OAAO,CAAAG,GAAE,SAAS;AAAA,WAAM;AAAC,cAAMZ,KAAES,GAAE,CAAC,EAAE;AAAS,cAAIA,GAAE,UAAQ,CAAC,EAAET,EAAC,KAAG,YAAUA,GAAE,QAAM,iBAAeA,GAAE,OAAKY,GAAE,gBAAgB,WAASH,KAAEG,GAAE,WAAW;AAAA,MAAC;AAAC,WAAK,KAAK,UAAS,EAAC,UAASH,IAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAM,CAAC,GAAE,SAAQ,CAACT,EAAC,GAAE,MAAK,mBAAkB,GAAE,MAAK,SAAQ,CAAC;AAAA,IAAC,GAAE,YAAW,YAAS;AAJly5B;AAImy5B,UAAG,KAAK,eAAe,SAAO,EAAE;AAAO,YAAMA,KAAE,KAAK,eAAe,UAAU,CAAC,GAAEM,KAAEN,GAAE;AAAS,UAAG,EAAEM,EAAC,MAAI,cAAYM,GAAE,SAAO,YAAUN,GAAE,QAAM,iBAAeA,GAAE,SAAO,gBAAcM,GAAE,QAAM,aAAWN,GAAE,MAAM;AAAO,UAAID,KAAE;AAAK,sBAAcO,GAAE,OAAKP,KAAE,MAAM,KAAK,YAAY,CAACL,EAAC,GAAES,IAAEE,EAAC,IAAE,cAAYC,GAAE,SAAOP,KAAE,MAAM,KAAK,QAAQ,CAACL,EAAC,GAAES,IAAEE,EAAC,IAAG,GAAGN,EAAC,OAAI,KAAAO,GAAE,oBAAF,mBAAmB,WAAUA,GAAE,kBAAgBP,IAAEO,GAAE,oBAAkBb,IAAE,QAAS,CAAAC,OAAGA,GAAE,OAAO,CAAE,GAAED,MAAE,KAAK,wBAAwBa,IAAEH,EAAC;AAAA,IAAG,EAAC,CAAC;AAAE,QAAII,KAAE,CAACF,GAAE,GAAG,mBAAmB,CAAAX,OAAG,KAAK,uCAAuCY,IAAEZ,IAAES,EAAC,GAAG,EAAE,MAAM,GAAEE,GAAE,GAAG,YAAY,CAAAX,OAAG;AAAC,UAAG,KAAK,yCAAyCY,IAAEZ,EAAC,GAAEA,GAAE,QAAMM,GAAE,cAAY,CAACN,GAAE,UAAQY,IAAE;AAAC,cAAMZ,KAAEY,GAAE;AAAgB,QAAAZ,MAAG,UAAQA,GAAE,SAAOA,GAAE,sBAAoB,CAACA,GAAE;AAAA,MAAoB;AAAA,IAAC,GAAG,EAAE,MAAM,GAAEW,GAAE,GAAG,UAAU,CAAAX,OAAG;AAAC,UAAGA,GAAE,QAAMM,GAAE,cAAYM,IAAE;AAAC,cAAMZ,KAAEY,GAAE;AAAgB,QAAAZ,MAAG,UAAQA,GAAE,SAAOA,GAAE,sBAAoB,CAACA,GAAE;AAAA,MAAoB;AAAA,IAAC,GAAG,EAAE,MAAM,CAAC,GAAED,MAAE,KAAK,wBAAwBa,IAAEH,EAAC;AAAE,WAAOG;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBZ,IAAEM,IAAEG,IAAE;AAAC,UAAK,EAAC,uBAAsBE,GAAC,IAAEL,IAAED,KAAE,MAAM,KAAK,eAAe,OAAO,4BAA0C,CAAC;AAAE,WAAO,GAAGA,EAAC,IAAEA,KAAE,IAAIA,GAAE,OAAO,QAAQ,EAAC,uBAAsBM,IAAE,mBAAkB,MAAG,mBAAkB,OAAG,UAASX,IAAE,MAAKS,IAAE,WAAU,EAAC,oBAAmB,CAAC,EAAC,IAAGT,IAAE,IAAGM,IAAE,SAAQG,GAAC,MAAI;AAAC,WAAK,uBAAuB,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,IAAGT,IAAE,IAAGM,IAAE,OAAMG,IAAE,MAAK,aAAY,GAAE,MAAK,SAAQ,CAAC;AAAA,IAAC,GAAE,eAAc,CAAC,EAAC,IAAGT,IAAE,IAAGM,IAAE,SAAQG,GAAC,MAAI,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,IAAGT,IAAE,IAAGM,IAAE,OAAMG,IAAE,MAAK,OAAM,GAAE,MAAK,SAAQ,CAAC,GAAE,mBAAkB,CAAC,EAAC,IAAGT,IAAE,IAAGM,IAAE,SAAQG,GAAC,MAAI;AAAC,WAAK,sBAAsB,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,IAAGT,IAAE,IAAGM,IAAE,OAAMG,IAAE,MAAK,YAAW,GAAE,MAAK,SAAQ,CAAC;AAAA,IAAC,GAAE,sBAAqB,MAAI,KAAK,sBAAsB,GAAE,qBAAoB,MAAI,KAAK,sBAAsB,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQT,IAAEM,IAAEG,IAAE;AAAC,UAAK,EAAC,gBAAeE,IAAE,eAAcN,IAAE,qBAAoBO,GAAC,IAAEN,IAAEO,KAAE,MAAM,KAAK,eAAe,OAAO,mBAAiC,CAAC;AAAE,WAAO,GAAGA,EAAC,IAAEA,KAAE,IAAIA,GAAE,OAAO,QAAQ,EAAC,UAASb,IAAE,gBAAeW,IAAE,eAAcN,IAAE,qBAAoBO,IAAE,OAAM,KAAK,wBAAuB,MAAKH,IAAE,gBAAe,KAAK,gBAAe,WAAU,EAAC,aAAY,CAAAT,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,QAAO,CAAAA,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,YAAW,CAAAA,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,cAAa,CAAAA,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,SAAQ,CAAAA,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,aAAY,CAAAA,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,eAAc,CAAAA,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,UAAS,CAAAA,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,cAAa,CAAAA,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYA,IAAEM,IAAEG,IAAE;AAJzjhC;AAI0jhC,UAAME,KAAE,cAAU,KAAAL,GAAE,mBAAF,mBAAkB,gBAAcD,KAAE,aAAS,KAAAC,GAAE,mBAAF,mBAAkB,iBAAeM,KAAE,MAAM,KAAK,eAAe,OAAO,uBAAqC,CAAC;AAAE,WAAO,GAAGA,EAAC,IAAEA,KAAE,IAAIA,GAAE,OAAO,QAAQ,EAAC,iBAAgBD,IAAE,gBAAeN,IAAE,SAAQL,GAAE,CAAC,GAAE,OAAM,KAAK,wBAAuB,iBAAgB,KAAK,kBAAiB,gBAAe,KAAK,gBAAe,MAAKS,IAAE,WAAU,EAAC,gBAAe,CAAAT,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,WAAU,CAAAA,OAAG,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,GAAGA,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,eAAc,CAAC,EAAC,OAAMA,IAAE,MAAKM,GAAC,MAAI,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMN,IAAE,MAAKM,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,aAAY,CAAC,EAAC,IAAGN,IAAE,IAAGM,IAAE,OAAMG,IAAE,MAAKE,GAAC,MAAI,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,IAAGX,IAAE,IAAGM,IAAE,OAAMG,IAAE,MAAKE,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,QAAO,CAAC,EAAC,IAAGX,IAAE,IAAGM,IAAE,OAAMG,IAAE,MAAKE,GAAC,MAAI,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,IAAGX,IAAE,IAAGM,IAAE,OAAMG,IAAE,MAAKE,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,YAAW,CAAC,EAAC,IAAGX,IAAE,IAAGM,IAAE,OAAMG,IAAE,MAAKE,GAAC,MAAI,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,IAAGX,IAAE,IAAGM,IAAE,OAAMG,IAAE,MAAKE,GAAC,GAAE,MAAK,SAAQ,CAAC,GAAE,aAAY,CAAC,EAAC,OAAMX,IAAE,MAAKM,IAAE,UAASG,GAAC,MAAI;AAAC,YAAME,KAAEX,GAAE,IAAK,CAAAA,OAAGM,GAAEN,GAAE,QAAQ,CAAE;AAAE,WAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMW,IAAE,UAASF,IAAE,MAAKH,GAAC,GAAE,MAAK,SAAQ,CAAC;AAAA,IAAC,GAAE,gBAAe,CAAC,EAAC,SAAQN,IAAE,MAAKM,IAAE,UAASG,GAAC,MAAI;AAAC,YAAME,KAAEX,GAAE,IAAK,CAAAA,OAAGM,GAAEN,GAAE,QAAQ,CAAE;AAAE,WAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,SAAQW,IAAE,UAASF,IAAE,MAAKH,GAAC,GAAE,MAAK,SAAQ,CAAC;AAAA,IAAC,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBN,IAAEM,IAAE;AAAC,UAAMG,KAAET,GAAE;AAAgB,QAAG,CAACS,GAAE,QAAM,CAAC;AAAE,YAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAgB,eAAM,CAACA,GAAE,GAAG,iBAAiB,CAAC,EAAC,SAAQH,IAAE,WAAUG,GAAC,MAAI;AAJ/plC;AAIgqlC,iBAAAA,GAAE,WAAF,mBAAU,cAAWA,GAAE,gBAAgB,GAAET,GAAE,oBAAoBM,EAAC;AAAA,QAAE,CAAE,GAAEG,GAAE,GAAG,sBAAsB,CAAAH,OAAGN,GAAE,aAAa,GAAGM,GAAE,WAAW,CAAC,CAAE,CAAC;AAAA,MAAE,KAAI;AAAM,eAAM,CAACG,GAAE,GAAG,iBAAiB,CAAAA,OAAG,KAAK,oCAAoCT,IAAEM,IAAEG,EAAC,CAAE,GAAEA,GAAE,GAAG,cAAc,CAAAH,OAAGN,GAAE,aAAa,GAAGM,GAAE,QAAQ,CAAC,CAAE,GAAEG,GAAE,GAAG,gBAAgB,CAAAH,OAAGN,GAAE,aAAa,GAAGM,GAAE,QAAQ,CAAC,CAAE,GAAEG,GAAE,GAAG,eAAe,CAAAH,OAAGN,GAAE,aAAa,GAAGM,GAAE,QAAQ,CAAC,CAAE,CAAC;AAAA,MAAE,KAAI;AAAU,eAAM,CAACG,GAAE,GAAG,iBAAiB,CAAAA,OAAG,KAAK,oCAAoCT,IAAEM,IAAEG,EAAC,CAAE,GAAEA,GAAE,GAAG,cAAc,CAAAH,OAAGN,GAAE,aAAa,GAAG,CAACM,GAAE,KAAK,CAAC,CAAC,CAAE,GAAEG,GAAE,GAAG,iBAAiB,CAAAH,OAAGN,GAAE,aAAa,GAAG,CAACM,GAAE,OAAO,CAAC,CAAC,CAAE,GAAEG,GAAE,GAAG,cAAc,CAAAH,OAAGN,GAAE,aAAa,GAAG,CAACM,GAAE,UAAU,CAAC,CAAC,CAAE,GAAEG,GAAE,GAAG,iBAAiB,CAAAH,OAAGN,GAAE,aAAa,GAAG,CAACM,GAAE,UAAU,CAAC,CAAC,CAAE,CAAC;AAAA,MAAE,KAAI;AAAU,eAAM,CAACG,GAAE,GAAG,sBAAsB,CAAAH,OAAG;AAAC,UAAAN,GAAE,aAAa,GAAGM,GAAE,WAAW,CAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,IAAG,GAAE,IAAG,GAAE,OAAMA,GAAE,YAAY,SAAO,IAAEA,GAAE,YAAY,CAAC,IAAE,MAAK,MAAK,aAAY,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAEG,GAAE,GAAG,gBAAgB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,IAAGA,GAAE,IAAG,IAAGA,GAAE,IAAG,OAAMA,GAAE,YAAY,SAAO,IAAEA,GAAE,YAAY,CAAC,IAAE,MAAK,MAAK,OAAM,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,qBAAqB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,IAAG,GAAE,IAAG,GAAE,OAAMA,GAAE,YAAY,SAAO,IAAEA,GAAE,YAAY,CAAC,IAAE,MAAK,MAAK,YAAW,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,mBAAmB,CAAAA,OAAG;AAAC,UAAAA,GAAE,WAAS,KAAK,iBAAiB,CAACA,GAAE,OAAO,GAAET,IAAEM,EAAC,IAAEN,GAAE,WAAW;AAAA,QAAC,CAAE,CAAC;AAAA,MAAE,KAAI;AAAe,eAAM,CAACS,GAAE,GAAG,eAAe,CAAC,EAAC,QAAOH,GAAC,MAAI;AAAC,UAAAN,GAAE,aAAa,EAAC,SAAQ,CAACM,EAAC,EAAC,CAAC;AAAA,QAAC,CAAE,GAAEG,GAAE,GAAG,2BAA2B,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMA,GAAE,SAAQ,IAAGA,GAAE,UAAS,IAAGA,GAAE,UAAS,MAAK,aAAY,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,0BAA0B,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMA,GAAE,SAAQ,IAAGA,GAAE,UAAS,IAAGA,GAAE,UAAS,MAAK,YAAW,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,wBAAwB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMA,GAAE,SAAQ,OAAMA,GAAE,OAAM,MAAK,eAAc,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,uBAAuB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMA,GAAE,SAAQ,OAAMA,GAAE,OAAM,MAAK,cAAa,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,uBAAuB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMA,GAAE,SAAQ,QAAOA,GAAE,QAAO,QAAOA,GAAE,QAAO,MAAK,cAAa,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,sBAAsB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMA,GAAE,SAAQ,QAAOA,GAAE,QAAO,QAAOA,GAAE,QAAO,MAAK,aAAY,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,qBAAqB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMA,GAAE,SAAQ,IAAGA,GAAE,UAAS,IAAGA,GAAE,UAAS,MAAK,OAAM,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,kBAAkB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMA,GAAE,SAAQ,OAAMA,GAAE,OAAM,MAAK,SAAQ,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,iBAAiB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAc,EAAC,OAAMA,GAAE,SAAQ,QAAOA,GAAE,QAAO,QAAOA,GAAE,QAAO,MAAK,QAAO,GAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,mBAAmB,CAAAA,OAAG;AAAC,UAAAA,GAAE,WAAS,KAAK,iBAAiB,CAACA,GAAE,OAAO,GAAET,IAAEM,EAAC,IAAEN,GAAE,WAAW;AAAA,QAAC,CAAE,CAAC;AAAA,MAAE,KAAI;AAAa,eAAM,CAACS,GAAE,GAAG,WAAW,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAcA,IAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,QAAQ,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAcA,IAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,cAAc,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAcA,IAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,iBAAiB,CAAAT,OAAG;AAAC,eAAK,KAAK,UAAS,EAAC,UAAS,KAAK,eAAe,QAAQ,GAAE,OAAM,UAAS,SAAQ,OAAG,MAAK,KAAK,YAAW,eAAcA,IAAE,MAAK,SAAQ,CAAC;AAAA,QAAC,CAAE,GAAES,GAAE,GAAG,mBAAmB,CAAAA,OAAG;AAAC,UAAAA,GAAE,WAAS,KAAK,iBAAiB,CAACA,GAAE,OAAO,GAAET,IAAEM,EAAC,IAAEN,GAAE,WAAW;AAAA,QAAC,CAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oCAAoCA,IAAEM,IAAEG,IAAE;AAJ1vuC;AAI2vuC,UAAK,EAAC,SAAQE,IAAE,WAAUN,GAAC,IAAEI;AAAE,aAAO,KAAAJ,GAAE,WAAF,mBAAU,aAAUM,GAAE,UAAQ,KAAK,SAAON,GAAE,gBAAgB,GAAEL,GAAE,oBAAoBW,EAAC,KAAGL,GAAE,qBAAmBD,GAAE,gBAAgB,GAAEL,GAAE,WAAW,KAAG;AAAA,EAAM;AAAA,EAAC,0BAA0BA,IAAEM,IAAE;AAJv9uC;AAIw9uC,SAAK,mBAAiBN;AAAE,UAAMS,MAAE,UAAK,SAAL,mBAAW;AAAI,SAAK,cAAcH,EAAC;AAAE,UAAMK,KAAE,MAAI;AAAC,UAAGX,OAAI,KAAK,kBAAiB;AAAC,cAAMW,KAAE,KAAK,eAAe,QAAQ,GAAEN,KAAE,KAAK,iBAAiB;AAAK,aAAK,iBAAiB,QAAQ,GAAE,KAAK,mBAAiB,MAAK,KAAK,uBAAuB,WAAW,KAAK,eAAe,QAAQ,CAAC,GAAE,KAAK,eAAe,UAAU,GAAEI,MAAGA,GAAE,OAAO,KAAK,sBAAsB,GAAE,KAAK,cAAcH,EAAC,GAAE,KAAK,KAAK,UAAS,EAAC,UAASK,IAAE,OAAM,YAAW,SAAQX,GAAE,WAAU,MAAKK,IAAE,eAAc,MAAK,MAAK,SAAQ,CAAC;AAAA,MAAC;AAAA,IAAC;AAAE,IAAAL,GAAE,GAAG,YAAWW,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,uCAAuCX,IAAEM,IAAEG,IAAE;AAAC,UAAME,KAAEZ,GAAEO,EAAC,GAAED,KAAE,MAAMC,GAAE,MAAO,MAAI,KAAK,aAAaK,EAAC,CAAE;AAAE,QAAG,EAAEN,EAAC,EAAE,QAAO,KAAKL,GAAE,SAAS;AAAE,QAAGM,GAAE,OAAO,YAAU,KAAK,iBAAiB,CAACD,GAAE,OAAO,GAAEL,IAAES,EAAC,EAAE,QAAO,KAAKH,GAAE,gBAAgB;AAAE,SAAK,eAAe,SAASD,GAAE,OAAO,IAAEC,GAAE,gBAAgB,IAAEN,GAAE,SAAS;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEM,IAAEG,IAAE;AAAC,UAAME,KAAE,CAAC,CAACF,GAAE;AAAyB,WAAOT,GAAE,KAAM,CAAAA,OAAG,QAAMA,OAAI,EAAE,CAACW,MAAGX,GAAE,UAAQ,KAAK,WAAS,KAAK,eAAe,SAASA,EAAC,IAAEM,GAAE,oBAAoBN,EAAC,IAAEM,GAAE,eAAeN,EAAC,GAAE,MAAK;AAAA,EAAC;AAAA,EAAC,yCAAyCA,IAAEM,IAAE;AAAC,QAAG,CAACN,GAAE;AAAO,UAAMS,KAAEH,GAAE;AAAI,IAAAG,OAAIH,GAAE,QAAMN,GAAE,QAAQ,KAAGM,GAAE,gBAAgB,GAAEN,GAAE,KAAK,KAAGS,OAAIH,GAAE,QAAMN,GAAE,QAAQ,KAAGM,GAAE,gBAAgB,GAAEN,GAAE,KAAK,KAAGS,OAAIH,GAAE,UAAQA,GAAE,gBAAgB,GAAEN,GAAE,OAAO,KAAG,KAAK,kBAAgBM,GAAE,OAAO,SAASG,EAAC,KAAG,KAAK,aAAaH,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaN,IAAE;AAAC,QAAG,CAAC,KAAK,oBAAkB,aAAW,KAAK,iBAAiB,KAAK;AAAO,UAAMM,KAAE,KAAK,iBAAgBG,KAAE,KAAK,eAAe,QAAQ;AAAE,MAAEH,EAAC,KAAG,iBAAeA,GAAE,SAAO,cAAYA,GAAE,QAAM,MAAIG,GAAE,UAAQ,YAAU,EAAEA,GAAE,CAAC,EAAE,UAAS,MAAM,OAAKT,GAAE,gBAAgB,GAAE,KAAK,OAAO;AAAA,EAAE;AAAA,EAAC,sBAAqB;AAJpkyC;AAIqkyC,SAAK,4BAAyB,gBAAK,SAAL,mBAAW,QAAX,mBAAgB,OAAO,KAAK,yBAAwB,KAAK,yBAAuB,EAAE,KAAK,sBAAsB;AAAA,EAAE;AAAA,EAAC,oBAAoBA,IAAE;AAAC,UAAK,EAAC,iBAAgBM,GAAC,IAAE;AAAK,WAAM,EAAE,CAACN,MAAG,EAAEM,EAAC,OAAKN,GAAE,cAAYA,GAAE,WAAW,kBAAgB,cAAYM,GAAE,QAAMA,GAAE,YAAUN,OAAI,UAAQM,GAAE,QAAM,cAAYA,GAAE,SAAOA,GAAE,YAAYN,EAAC;AAAA,EAAE;AAAA,EAAC,wBAAuB;AAAC,SAAK,QAAM,KAAK,KAAK,aAAW,cAAY,KAAK,KAAK,WAAS,KAAK,KAAK,SAAO;AAAA,EAAU;AAAA,EAAC,yBAAwB;AAAC,SAAK,QAAM,KAAK,KAAK,aAAW,eAAa,KAAK,KAAK,WAAS,KAAK,KAAK,SAAO;AAAA,EAAW;AAAA,EAAC,wBAAuB;AAAC,SAAK,QAAM,KAAK,KAAK,aAAW,SAAO,KAAK,KAAK,WAAS,KAAK,KAAK,SAAO;AAAA,EAAK;AAAA,EAAC,UAAUA,IAAEM,IAAEG,IAAE;AAAC,MAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAII,GAAEb,IAAEM,IAAEG,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeT,IAAE;AAAC,UAAMM,KAAE,IAAI;AAAgB,SAAK,+BAA6BA;AAAE,UAAMG,KAAE,MAAMT;AAAE,WAAO,KAAK,iCAA+BM,MAAGA,GAAE,OAAO,UAAQ,EAAC,cAAa,UAAS,IAAE,EAAC,QAAOG,GAAC;AAAA,EAAC;AAAA,EAAC,eAAeT,IAAE;AAAC,SAAK,KAAK,QAAO,EAAC,GAAGA,IAAE,MAAK,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,SAAK,KAAK,QAAO,EAAC,GAAGA,IAAE,MAAK,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,SAAK,KAAK,UAAS,EAAC,GAAGA,IAAE,MAAK,SAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM,EAAC,iBAAgB,KAAK,kBAAiB,sBAAqB,EAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAOkB,GAAG,MAAI,CAAC,KAAK,QAAS;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,SAAK,gBAAc,GAAE,KAAK,aAAa,UAAU;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,SAAK,gBAAc,GAAE,KAAK,aAAa,UAAU;AAAA,EAAC;AAAA,EAAC,qBAAqBlB,IAAE;AAJ980C;AAI+80C,WAAM,WAAO,UAAK,SAAL,mBAAW,SAAM,KAAK,2BAAuB,KAAAM,GAAEN,EAAC,MAAH,mBAAM,sBAAmB;AAAA,EAAG;AAAA,EAAC,cAAcA,IAAE;AAJtj1C;AAIuj1C,QAAG,CAAC,KAAK,qBAAqBA,EAAC,EAAE;AAAO,UAAMM,MAAE,UAAK,SAAL,mBAAW;AAAM,IAAAA,MAAG,EAAE,KAAK,wBAAwB,MAAI,KAAK,2BAAyBA,GAAE,iBAAgBA,GAAE,kBAAgB;AAAA,EAAG;AAAA,EAAC,cAAcN,IAAE;AAJpv1C;AAIqv1C,QAAG,CAAC,KAAK,qBAAqBA,EAAC,EAAE;AAAO,UAAMM,MAAE,UAAK,SAAL,mBAAW;AAAM,IAAAA,MAAG,EAAE,KAAK,wBAAwB,MAAIA,GAAE,kBAAgB,KAAK,0BAAyB,KAAK,2BAAyB;AAAA,EAAK;AAAA,EAAC,MAAM,iBAAgB;AAAC,UAAMN,KAAE,KAAK;AAAK,IAAAA,MAAG,EAAE,KAAK,yBAAyB,GAAE,KAAK,4BAA0B,IAAI,mBAAgB,MAAME,GAAEgB,GAAG,MAAIlB,MAAA,gBAAAA,GAAG,KAAM,GAAE,KAAK,0BAA0B,MAAM,KAAG,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,SAAK,UAAU,2BAA0B,GAAG,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,UAAU,IAAG,GAAG,OAAO,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMiB,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKnB,KAAE,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKK,IAAE,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMc,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,KAAI,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,IAAE,EAAE,CAACb,GAAE,qCAAqC,CAAC,GAAE,CAAC;AAAE,IAAM,KAAG;AAAT,IAAmC,KAAG,CAAAL,OAAG,aAAaA,EAAC;AAAmC,SAAS,GAAGA,IAAE;AAAC,SAAM,cAAYA,MAAG,gBAAcA,MAAG,aAAWA;AAAC;AAAC,SAAS,GAAGA,IAAEM,IAAE;AAAC,KAAG,QAAON,GAAE,QAAQ,MAAKA,GAAE,QAAQ,MAAKM,EAAC;AAAC;AAAC,SAAS,GAAGN,IAAEM,IAAE;AAAC,KAAG,QAAON,GAAE,QAAQ,MAAKA,GAAE,QAAQ,MAAKM,EAAC;AAAC;AAAC,SAAS,GAAGN,IAAEM,IAAEG,IAAEE,IAAE;AAAC,QAAMN,KAAEC,GAAE,IAAI;AAAE,MAAG,CAACD,GAAE;AAAO,QAAMO,KAAEP,GAAE,SAAQQ,KAAE,CAAC;AAAE,EAAAF,GAAE,QAAS,CAACL,IAAEG,OAAI;AAAC,UAAME,KAAEC,GAAEH,EAAC;AAAE,YAAME,OAAI,cAAaA,MAAG,EAAEA,GAAE,QAAQ,MAAIE,GAAE,KAAK,EAAC,UAASP,GAAE,SAAQ,CAAC,GAAEA,GAAE,WAASK,GAAE,WAAU,YAAWA,MAAG,EAAEA,GAAE,MAAM,MAAIE,GAAE,KAAK,EAAC,QAAOP,GAAE,OAAM,CAAC,GAAEA,GAAE,SAAOK,GAAE,SAAQ,UAASA,OAAIE,GAAE,KAAKF,EAAC,GAAEA,GAAEX,EAAC,EAAEM,EAAC;AAAA,EAAG,CAAE,GAAEG,GAAE,KAAK,EAAC,SAAQI,GAAC,CAAC;AAAC;AAAC,SAAS,GAAGb,IAAE;AAAC,SAAM,EAAC,SAAQA,GAAE,IAAK,CAAAA,QAAI,EAAC,UAASA,GAAE,SAAQ,EAAG,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,kBAAiBA,MAAG,cAAYA,GAAE;AAAY;AAAC,IAAM,KAAG;", "names": ["i", "P", "n", "e", "l", "o", "t", "e", "i", "r", "s", "n", "h", "E", "u", "d", "e", "t", "r", "s", "n", "a", "E", "u", "p", "l", "i", "c", "o", "G", "D", "O", "w", "P", "A", "n", "e", "t", "p", "s", "c", "r", "u", "E", "r", "t", "e", "o", "a", "E", "n", "u", "l", "n", "e", "s", "r", "i", "d", "a", "u", "E", "h", "g", "t", "m", "f", "c", "x", "o", "b", "g", "t", "i", "f", "o", "d", "h", "p", "e", "l", "E", "n", "u", "r", "g", "n", "e", "t", "r", "i", "o", "a", "h", "u", "E", "p", "c", "f", "m", "v", "L", "x", "l", "s", "V", "n", "e", "t", "i", "s", "r", "o", "u", "E", "a", "c", "p", "d", "f", "j", "w", "S", "F", "l", "h", "t", "r", "a", "e", "m", "w", "l", "n", "E", "u", "s", "h", "n", "e", "t", "i", "s", "r", "o", "a", "d", "c", "m", "p", "g", "A", "f", "u", "v", "x", "l", "j", "y", "w", "E", "f", "s", "E", "g", "l", "V", "h", "o", "t", "i", "r", "d", "a", "i", "p", "f", "s", "r", "a", "p", "l", "e", "p", "a", "n", "r", "t", "n", "s", "o", "b", "E", "w", "n", "d", "e", "i", "E", "l", "t", "p", "r", "a", "o", "c", "u", "f", "_", "I", "m", "s", "p", "n", "e", "a", "i", "n", "t", "c", "y", "m", "p", "a", "e", "l", "w", "o", "f", "i", "r", "s", "P", "E", "h", "d", "j"]}