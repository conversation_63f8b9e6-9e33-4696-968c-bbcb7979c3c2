{"version": 3, "sources": ["../../@arcgis/core/layers/support/sublayerUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as r}from\"../../core/maybe.js\";import{OriginId as e}from\"../../core/accessorSupport/PropertyOrigin.js\";function n(r,e,n){const i=e.flatten((({sublayers:r})=>r)).length;if(i!==r.length)return!0;return!!r.some((r=>r.originIdOf(\"minScale\")>n||r.originIdOf(\"maxScale\")>n||r.originIdOf(\"renderer\")>n||r.originIdOf(\"labelingInfo\")>n||r.originIdOf(\"opacity\")>n||r.originIdOf(\"labelsVisible\")>n||r.originIdOf(\"source\")>n))||!o(r,e)}function i(n,i,t){return!!n.some((n=>{const i=n.source;return!(!i||\"map-layer\"===i.type&&i.mapLayerId===n.id&&(r(i.gdbVersion)||i.gdbVersion===t))||n.originIdOf(\"renderer\")>e.SERVICE||n.originIdOf(\"labelingInfo\")>e.SERVICE||n.originIdOf(\"opacity\")>e.SERVICE||n.originIdOf(\"labelsVisible\")>e.SERVICE}))||!o(n,i)}function o(e,n){if(!e||!e.length||r(n))return!0;const i=n.slice().reverse().flatten((({sublayers:r})=>r&&r.toArray().reverse())).map((r=>r.id)).toArray();if(e.length>i.length)return!1;let o=0;const t=i.length;for(const{id:r}of e){for(;o<t&&i[o]!==r;)o++;if(o>=t)return!1}return!0}function t(r){return!!r&&r.some((r=>null!=r.minScale||r.layerDefinition&&null!=r.layerDefinition.minScale))}export{i as isExportDynamic,t as isSublayerOverhaul,n as shouldWriteSublayerStructure};\n"], "mappings": ";;;;;;;;AAIqH,SAAS,EAAEA,IAAE,GAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,QAAS,CAAC,EAAC,WAAUF,GAAC,MAAIA,EAAE,EAAE;AAAO,MAAGE,OAAIF,GAAE,OAAO,QAAM;AAAG,SAAM,CAAC,CAACA,GAAE,KAAM,CAAAA,OAAGA,GAAE,WAAW,UAAU,IAAEC,MAAGD,GAAE,WAAW,UAAU,IAAEC,MAAGD,GAAE,WAAW,UAAU,IAAEC,MAAGD,GAAE,WAAW,cAAc,IAAEC,MAAGD,GAAE,WAAW,SAAS,IAAEC,MAAGD,GAAE,WAAW,eAAe,IAAEC,MAAGD,GAAE,WAAW,QAAQ,IAAEC,EAAE,KAAG,CAAC,EAAED,IAAE,CAAC;AAAC;AAAC,SAAS,EAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAM,CAAC,CAACF,GAAE,KAAM,CAAAA,OAAG;AAAC,UAAMC,KAAED,GAAE;AAAO,WAAM,EAAE,CAACC,MAAG,gBAAcA,GAAE,QAAMA,GAAE,eAAaD,GAAE,OAAK,EAAEC,GAAE,UAAU,KAAGA,GAAE,eAAaC,QAAKF,GAAE,WAAW,UAAU,IAAE,EAAE,WAASA,GAAE,WAAW,cAAc,IAAE,EAAE,WAASA,GAAE,WAAW,SAAS,IAAE,EAAE,WAASA,GAAE,WAAW,eAAe,IAAE,EAAE;AAAA,EAAO,CAAE,KAAG,CAAC,EAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAE,GAAED,IAAE;AAAC,MAAG,CAAC,KAAG,CAAC,EAAE,UAAQ,EAAEA,EAAC,EAAE,QAAM;AAAG,QAAMC,KAAED,GAAE,MAAM,EAAE,QAAQ,EAAE,QAAS,CAAC,EAAC,WAAUD,GAAC,MAAIA,MAAGA,GAAE,QAAQ,EAAE,QAAQ,CAAE,EAAE,IAAK,CAAAA,OAAGA,GAAE,EAAG,EAAE,QAAQ;AAAE,MAAG,EAAE,SAAOE,GAAE,OAAO,QAAM;AAAG,MAAIE,KAAE;AAAE,QAAMD,KAAED,GAAE;AAAO,aAAS,EAAC,IAAGF,GAAC,KAAI,GAAE;AAAC,WAAKI,KAAED,MAAGD,GAAEE,EAAC,MAAIJ,KAAG,CAAAI;AAAI,QAAGA,MAAGD,GAAE,QAAM;AAAA,EAAE;AAAC,SAAM;AAAE;AAAC,SAASA,GAAEH,IAAE;AAAC,SAAM,CAAC,CAACA,MAAGA,GAAE,KAAM,CAAAA,OAAG,QAAMA,GAAE,YAAUA,GAAE,mBAAiB,QAAMA,GAAE,gBAAgB,QAAS;AAAC;", "names": ["r", "n", "i", "t", "o"]}