import {
  n
} from "./chunk-TAERZTFZ.js";
import {
  o
} from "./chunk-MYLMEBH6.js";
import "./chunk-KYDW2SHL.js";
import "./chunk-JTTSDQPH.js";
import "./chunk-6DXPU43Z.js";
import "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-53FPJYCC.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-QKWIBVLD.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-RURSJOSG.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  l,
  u
} from "./chunk-2ILOD42U.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/features/tileRenderers/support/HeatmapSource.js
var i = class {
  constructor() {
    this.gradient = null, this.height = 512, this.intensities = null, this.width = 512;
  }
  render(i2) {
    l(i2, 512, this.intensities, this.gradient, this.minDensity, this.maxDensity);
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/tileRenderers/HeatmapTileRenderer.js
var o2 = class extends o {
  constructor(e2) {
    super(e2), this._intensityInfo = { minDensity: 0, maxDensity: 0 }, this.type = "heatmap", this.featuresView = { attributeView: { initialize: () => {
    }, requestUpdate: () => {
    } }, requestRender: () => {
    } }, this._container = new n(e2.tileInfoView);
  }
  createTile(e2) {
    const t = this._container.createTile(e2);
    return this.tileInfoView.getTileCoords(t.bitmap, e2), t.bitmap.resolution = this.tileInfoView.getTileResolution(e2), t;
  }
  onConfigUpdate() {
    const e2 = this.layer.renderer;
    if ("heatmap" === e2.type) {
      const { minDensity: t, maxDensity: r, colorStops: s } = e2;
      this._intensityInfo.minDensity = t, this._intensityInfo.maxDensity = r, this._gradient = u(s), this.tiles.forEach((e3) => {
        const i2 = e3.bitmap.source;
        i2 && (i2.minDensity = t, i2.maxDensity = r, i2.gradient = this._gradient, e3.bitmap.invalidateTexture());
      });
    }
  }
  hitTest() {
    return Promise.resolve([]);
  }
  install(e2) {
    e2.addChild(this._container);
  }
  uninstall(e2) {
    this._container.removeAllChildren(), e2.removeChild(this._container);
  }
  disposeTile(e2) {
    this._container.removeChild(e2), e2.destroy();
  }
  supportsRenderer(e2) {
    return e2 && "heatmap" === e2.type;
  }
  onTileData(e2) {
    const t = this.tiles.get(e2.tileKey);
    if (!t) return;
    const i2 = e2.intensityInfo, { minDensity: r, maxDensity: s } = this._intensityInfo, o3 = t.bitmap.source || new i();
    o3.intensities = i2 && i2.matrix || null, o3.minDensity = r, o3.maxDensity = s, o3.gradient = this._gradient, t.bitmap.source = o3, this._container.addChild(t), this._container.requestRender(), this.requestUpdate();
  }
  onTileError(e2) {
    console.error(e2);
  }
  lockGPUUploads() {
  }
  unlockGPUUploads() {
  }
  fetchResource(e2, t) {
    return console.error(e2), Promise.reject();
  }
};
o2 = e([a("esri.views.2d.layers.features.tileRenderers.HeatmapTileRenderer")], o2);
var a2 = o2;
export {
  a2 as default
};
//# sourceMappingURL=HeatmapTileRenderer-25S4OOV4.js.map
