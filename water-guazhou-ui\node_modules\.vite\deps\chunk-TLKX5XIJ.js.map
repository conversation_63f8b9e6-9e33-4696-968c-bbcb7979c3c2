{"version": 3, "sources": ["../../@arcgis/core/Graphic.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"./chunks/tslib.es6.js\";import{geometryTypes as e}from\"./geometry.js\";import r from\"./PopupTemplate.js\";import{symbolTypes as o}from\"./symbols.js\";import{ClonableMixin as s}from\"./core/Clonable.js\";import{JSONSupport as i}from\"./core/JSONSupport.js\";import{isSome as p}from\"./core/maybe.js\";import{generateUID as a}from\"./core/uid.js\";import{property as l}from\"./core/accessorSupport/decorators/property.js\";import\"./core/accessorSupport/ensureType.js\";import\"./core/arrayUtils.js\";import{subclass as u}from\"./core/accessorSupport/decorators/subclass.js\";import{fromJSON as n}from\"./geometry/support/jsonUtils.js\";function y(t){if(!t)return null;const e={};for(const r in t){const o=n(t[r]);o&&(e[r]=o)}return 0!==Object.keys(e).length?e:null}function m(t){if(!p(t))return null;const e={};for(const r in t){const o=t[r];o&&(e[r]=o.toJSON())}return 0!==Object.keys(e).length?e:null}let h=class extends(s(i)){constructor(...t){super(...t),this.isAggregate=!1,this.layer=null,this.popupTemplate=null,this.sourceLayer=null,Object.defineProperty(this,\"uid\",{value:a(),configurable:!0})}normalizeCtorArgs(t,e,r,o){return t&&!t.declaredClass?t:{geometry:t,symbol:e,attributes:r,popupTemplate:o}}set aggregateGeometries(t){const e=this._get(\"aggregateGeometries\");JSON.stringify(e)!==JSON.stringify(t)&&this._set(\"aggregateGeometries\",t)}set attributes(t){const e=this._get(\"attributes\");e!==t&&(this._set(\"attributes\",t),this._notifyLayer(\"attributes\",e,t))}set geometry(t){const e=this._get(\"geometry\");e!==t&&(this._set(\"geometry\",t),this._notifyLayer(\"geometry\",e,t))}set symbol(t){const e=this._get(\"symbol\");e!==t&&(this._set(\"symbol\",t),this._notifyLayer(\"symbol\",e,t))}set visible(t){const e=this._get(\"visible\");e!==t&&(this._set(\"visible\",t),this._notifyLayer(\"visible\",e,t))}getEffectivePopupTemplate(t=!1){if(this.popupTemplate)return this.popupTemplate;for(const e of[this.sourceLayer,this.layer])if(e){if(\"popupTemplate\"in e&&e.popupTemplate)return e.popupTemplate;if(t&&\"defaultPopupTemplate\"in e&&p(e.defaultPopupTemplate))return e.defaultPopupTemplate}return null}getAttribute(t){return this.attributes?.[t]}setAttribute(t,e){if(this.attributes){const r=this.getAttribute(t);this.attributes[t]=e,this._notifyLayer(\"attributes\",r,e,t)}else this.attributes={[t]:e},this._notifyLayer(\"attributes\",void 0,e,t)}getObjectId(){return this.sourceLayer&&\"objectIdField\"in this.sourceLayer&&this.sourceLayer.objectIdField?this.getAttribute(this.sourceLayer.objectIdField):null}toJSON(){return{aggregateGeometries:m(this.aggregateGeometries),geometry:p(this.geometry)?this.geometry.toJSON():null,symbol:p(this.symbol)?this.symbol.toJSON():null,attributes:{...this.attributes},popupTemplate:this.popupTemplate&&this.popupTemplate.toJSON()}}notifyGeometryChanged(){this._notifyLayer(\"geometry\",this.geometry,this.geometry)}notifyMeshTransformChanged(){p(this.geometry)&&\"mesh\"===this.geometry.type&&this._notifyLayer(\"transform\",this.geometry.transform,this.geometry.transform)}_notifyLayer(t,e,r,o){if(!this.layer||!(\"graphicChanged\"in this.layer))return;const s={graphic:this,property:t,oldValue:e,newValue:r};\"attributes\"===t&&(s.attributeName=o),this.layer.graphicChanged(s)}};t([l({value:null,json:{read:y}})],h.prototype,\"aggregateGeometries\",null),t([l({value:null})],h.prototype,\"attributes\",null),t([l({value:null,types:e,json:{read:n}})],h.prototype,\"geometry\",null),t([l({type:Boolean})],h.prototype,\"isAggregate\",void 0),t([l({clonable:\"reference\"})],h.prototype,\"layer\",void 0),t([l({type:r})],h.prototype,\"popupTemplate\",void 0),t([l({clonable:\"reference\"})],h.prototype,\"sourceLayer\",void 0),t([l({value:null,types:o})],h.prototype,\"symbol\",null),t([l({type:Boolean,value:!0})],h.prototype,\"visible\",null),h=t([u(\"esri.Graphic\")],h),function(t){t.generateUID=a}(h||(h={}));const g=h;export{g as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIwnB,SAASA,GAAE,GAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,QAAMC,KAAE,CAAC;AAAE,aAAUC,MAAK,GAAE;AAAC,UAAM,IAAE,EAAE,EAAEA,EAAC,CAAC;AAAE,UAAID,GAAEC,EAAC,IAAE;AAAA,EAAE;AAAC,SAAO,MAAI,OAAO,KAAKD,EAAC,EAAE,SAAOA,KAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG,CAAC,EAAE,CAAC,EAAE,QAAO;AAAK,QAAMA,KAAE,CAAC;AAAE,aAAUC,MAAK,GAAE;AAAC,UAAM,IAAE,EAAEA,EAAC;AAAE,UAAID,GAAEC,EAAC,IAAE,EAAE,OAAO;AAAA,EAAE;AAAC,SAAO,MAAI,OAAO,KAAKD,EAAC,EAAE,SAAOA,KAAE;AAAI;AAAC,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,eAAe,GAAE;AAAC,UAAM,GAAG,CAAC,GAAE,KAAK,cAAY,OAAG,KAAK,QAAM,MAAK,KAAK,gBAAc,MAAK,KAAK,cAAY,MAAK,OAAO,eAAe,MAAK,OAAM,EAAC,OAAMA,GAAE,GAAE,cAAa,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkB,GAAEA,IAAEC,IAAE,GAAE;AAAC,WAAO,KAAG,CAAC,EAAE,gBAAc,IAAE,EAAC,UAAS,GAAE,QAAOD,IAAE,YAAWC,IAAE,eAAc,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,oBAAoB,GAAE;AAAC,UAAMD,KAAE,KAAK,KAAK,qBAAqB;AAAE,SAAK,UAAUA,EAAC,MAAI,KAAK,UAAU,CAAC,KAAG,KAAK,KAAK,uBAAsB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAW,GAAE;AAAC,UAAMA,KAAE,KAAK,KAAK,YAAY;AAAE,IAAAA,OAAI,MAAI,KAAK,KAAK,cAAa,CAAC,GAAE,KAAK,aAAa,cAAaA,IAAE,CAAC;AAAA,EAAE;AAAA,EAAC,IAAI,SAAS,GAAE;AAAC,UAAMA,KAAE,KAAK,KAAK,UAAU;AAAE,IAAAA,OAAI,MAAI,KAAK,KAAK,YAAW,CAAC,GAAE,KAAK,aAAa,YAAWA,IAAE,CAAC;AAAA,EAAE;AAAA,EAAC,IAAI,OAAO,GAAE;AAAC,UAAMA,KAAE,KAAK,KAAK,QAAQ;AAAE,IAAAA,OAAI,MAAI,KAAK,KAAK,UAAS,CAAC,GAAE,KAAK,aAAa,UAASA,IAAE,CAAC;AAAA,EAAE;AAAA,EAAC,IAAI,QAAQ,GAAE;AAAC,UAAMA,KAAE,KAAK,KAAK,SAAS;AAAE,IAAAA,OAAI,MAAI,KAAK,KAAK,WAAU,CAAC,GAAE,KAAK,aAAa,WAAUA,IAAE,CAAC;AAAA,EAAE;AAAA,EAAC,0BAA0B,IAAE,OAAG;AAAC,QAAG,KAAK,cAAc,QAAO,KAAK;AAAc,eAAUA,MAAI,CAAC,KAAK,aAAY,KAAK,KAAK,EAAE,KAAGA,IAAE;AAAC,UAAG,mBAAkBA,MAAGA,GAAE,cAAc,QAAOA,GAAE;AAAc,UAAG,KAAG,0BAAyBA,MAAG,EAAEA,GAAE,oBAAoB,EAAE,QAAOA,GAAE;AAAA,IAAoB;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,aAAa,GAAE;AAJ1jE;AAI2jE,YAAO,UAAK,eAAL,mBAAkB;AAAA,EAAE;AAAA,EAAC,aAAa,GAAEA,IAAE;AAAC,QAAG,KAAK,YAAW;AAAC,YAAMC,KAAE,KAAK,aAAa,CAAC;AAAE,WAAK,WAAW,CAAC,IAAED,IAAE,KAAK,aAAa,cAAaC,IAAED,IAAE,CAAC;AAAA,IAAC,MAAM,MAAK,aAAW,EAAC,CAAC,CAAC,GAAEA,GAAC,GAAE,KAAK,aAAa,cAAa,QAAOA,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK,eAAa,mBAAkB,KAAK,eAAa,KAAK,YAAY,gBAAc,KAAK,aAAa,KAAK,YAAY,aAAa,IAAE;AAAA,EAAI;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAC,qBAAoB,EAAE,KAAK,mBAAmB,GAAE,UAAS,EAAE,KAAK,QAAQ,IAAE,KAAK,SAAS,OAAO,IAAE,MAAK,QAAO,EAAE,KAAK,MAAM,IAAE,KAAK,OAAO,OAAO,IAAE,MAAK,YAAW,EAAC,GAAG,KAAK,WAAU,GAAE,eAAc,KAAK,iBAAe,KAAK,cAAc,OAAO,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAuB;AAAC,SAAK,aAAa,YAAW,KAAK,UAAS,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,6BAA4B;AAAC,MAAE,KAAK,QAAQ,KAAG,WAAS,KAAK,SAAS,QAAM,KAAK,aAAa,aAAY,KAAK,SAAS,WAAU,KAAK,SAAS,SAAS;AAAA,EAAC;AAAA,EAAC,aAAa,GAAEA,IAAEC,IAAE,GAAE;AAAC,QAAG,CAAC,KAAK,SAAO,EAAE,oBAAmB,KAAK,OAAO;AAAO,UAAM,IAAE,EAAC,SAAQ,MAAK,UAAS,GAAE,UAASD,IAAE,UAASC,GAAC;AAAE,qBAAe,MAAI,EAAE,gBAAc,IAAG,KAAK,MAAM,eAAe,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAK,MAAK,EAAC,MAAKF,GAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,uBAAsB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,KAAI,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAK,OAAM,GAAE,MAAK,EAAC,MAAK,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,YAAW,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,YAAW,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAK,OAAM,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,OAAM,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,cAAc,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE;AAAC,IAAE,cAAYC;AAAC,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAM,IAAE;", "names": ["y", "e", "r"]}