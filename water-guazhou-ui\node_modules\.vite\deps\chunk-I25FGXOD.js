import {
  r as r4
} from "./chunk-3LXNA5FS.js";
import {
  s
} from "./chunk-NMGVJ2ZX.js";
import {
  r as r3
} from "./chunk-INH5JU5P.js";
import {
  l
} from "./chunk-UQWZJZ2S.js";
import {
  r as r2
} from "./chunk-36FLFRUE.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/queryEngineUtils.js
function o({ x: e, y: r5, z: a }) {
  return l(r2(e, r5, a ?? 0));
}
function i(e, t) {
  switch (e.type) {
    case "edge":
      return e.draped ? new s({ edgeStart: o(e.start), edgeEnd: o(e.end), targetPoint: o(e.target), objectId: e.objectId, getGroundElevation: t }) : new r3({ edgeStart: o(e.start), edgeEnd: o(e.end), targetPoint: o(e.target), objectId: e.objectId, isDraped: false });
    case "vertex":
      return new r4({ targetPoint: o(e.target), objectId: e.objectId, isDraped: false });
  }
}
function p(t) {
  return r(t) && "3d" === t.type ? (e, n, r5) => t.elevationProvider.getElevation(e, n, r5 ?? 0, t.spatialReference, "ground") : () => null;
}

export {
  i,
  p
};
//# sourceMappingURL=chunk-I25FGXOD.js.map
