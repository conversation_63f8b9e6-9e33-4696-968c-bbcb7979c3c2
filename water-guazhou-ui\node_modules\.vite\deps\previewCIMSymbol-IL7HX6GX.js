import {
  M,
  z
} from "./chunk-IAHAUX75.js";
import "./chunk-ZBS6USLD.js";
import "./chunk-FYL27KLS.js";
import "./chunk-OBW4AQOU.js";
import {
  ie,
  se
} from "./chunk-ARNVPEMS.js";
import "./chunk-HSGVCYPR.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-N73MYEJE.js";
import "./chunk-MDHXGN24.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-RURSJOSG.js";
import "./chunk-DUEDINK5.js";
import {
  l
} from "./chunk-J6YZZTVC.js";
import {
  t
} from "./chunk-Q7KDWWJV.js";
import "./chunk-VG3PLO3G.js";
import "./chunk-ANH6666P.js";
import {
  O
} from "./chunk-J4YX6DLU.js";
import "./chunk-O2BYTJI4.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-77E52HT5.js";
import "./chunk-HQDK2TLZ.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-SROTSYJS.js";
import "./chunk-IZLLLMFE.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-VYC4DNQO.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-VBAEC53F.js";
import "./chunk-C67OD7TM.js";
import "./chunk-5A4WR2SR.js";
import "./chunk-SJ35WMYN.js";
import "./chunk-UV4E33V4.js";
import "./chunk-PBQFTVHM.js";
import "./chunk-ZJKAJ76S.js";
import "./chunk-46HTCESL.js";
import "./chunk-NE5KC6IQ.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-CV3OR36A.js";
import "./chunk-JX2QAMUH.js";
import "./chunk-DVUUHX3W.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import "./chunk-SJRT3EVN.js";
import "./chunk-RCNP3U5T.js";
import "./chunk-3MWB7OGY.js";
import "./chunk-MURG32WB.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-LZMNPMOO.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-3BEYEFLH.js";
import "./chunk-NM5RTWYY.js";
import {
  e,
  u
} from "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-B2DWQPEO.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-IJ6FZE6K.js";
import "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-R4CPW7J5.js";
import "./chunk-2CM7MIII.js";
import "./chunk-HP475EI3.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/symbols/support/previewCIMSymbol.js
var h = new z(null, true);
var m = e(t.size);
var c = e(t.maxSize);
var u2 = e(t.lineWidth);
var f = 1;
function y(e2) {
  const t2 = e2 == null ? void 0 : e2.size;
  if ("number" == typeof t2) return { width: t2, height: t2 };
  return { width: null != t2 && "object" == typeof t2 && "width" in t2 ? t2.width : null, height: null != t2 && "object" == typeof t2 && "height" in t2 ? t2.height : null };
}
async function d(e2, l2 = {}) {
  var _a;
  const { node: s, opacity: d2, symbolConfig: p } = l2, g = "object" == typeof p && "isSquareFill" in p && p.isSquareFill, w = l2.cimOptions || l2, b = w.geometryType || O((_a = e2 == null ? void 0 : e2.data) == null ? void 0 : _a.symbol), M2 = y(l2), { feature: j, fieldMap: v } = w;
  if (null == M2.width || null == M2.height) {
    const t2 = await se.resolveSymbolOverrides(e2.data, j, null, v, b);
    if (!t2) return null;
    (e2 = e2.clone()).data = { type: "CIMSymbolReference", symbol: t2 }, e2.data.primitiveOverrides = void 0;
    const l3 = [];
    ie.fetchResources(t2, h.resourceManager, l3), l3.length > 0 && await Promise.all(l3);
    const n = ie.getEnvelope(t2, null, h.resourceManager), r = n == null ? void 0 : n.width, s2 = n == null ? void 0 : n.height;
    M2.width = "esriGeometryPolygon" === b ? m : "esriGeometryPolyline" === b ? u2 : null != r && isFinite(r) ? Math.min(r, c) : m, M2.height = "esriGeometryPolygon" === b ? m : null != s2 && isFinite(s2) ? Math.max(Math.min(s2, c), f) : m;
  }
  const S = await h.rasterizeCIMSymbolAsync(e2, j, M2, g || "esriGeometryPolygon" !== b ? M.Preview : M.Legend, v, b);
  if (!S) return null;
  const { width: C, height: I } = S, P = document.createElement("canvas");
  P.width = C, P.height = I;
  P.getContext("2d").putImageData(S, 0, 0);
  const x = u(M2.width), z2 = u(M2.height), F = new Image(x, z2);
  F.src = P.toDataURL(), null != d2 && (F.style.opacity = `${d2}`);
  let G = F;
  if (null != l2.effectView) {
    const e3 = { shape: { type: "image", x: 0, y: 0, width: x, height: z2, src: F.src }, fill: null, stroke: null, offset: [0, 0] };
    G = l([[e3]], [x, z2], { effectView: l2.effectView });
  }
  return s && G && s.appendChild(G), G;
}
export {
  d as previewCIMSymbol
};
//# sourceMappingURL=previewCIMSymbol-IL7HX6GX.js.map
