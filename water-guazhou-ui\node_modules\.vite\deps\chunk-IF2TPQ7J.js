import {
  Ie,
  ae,
  fe,
  ie,
  te
} from "./chunk-VNYCO3JG.js";
import {
  I
} from "./chunk-JXLVNWKF.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/graphics/sources/support/sourceUtils.js
var l = class {
  constructor() {
    this.code = null, this.description = null;
  }
};
var u = class {
  constructor(e) {
    this.error = new l(), this.globalId = null, this.objectId = null, this.success = false, this.uniqueId = null, this.error.description = e;
  }
};
function a(e) {
  return new u(e);
}
var c = class {
  constructor(e) {
    this.globalId = null, this.success = true, this.objectId = this.uniqueId = e;
  }
};
function f(e) {
  return new c(e);
}
var d = /* @__PURE__ */ new Set();
function m(e, t2, r, s = false, o) {
  d.clear();
  for (const l2 in r) {
    const u2 = e.get(l2);
    if (!u2) continue;
    const c2 = r[l2], f2 = p(u2, c2);
    if (f2 !== c2 && o && o.push({ name: "invalid-value-type", message: "attribute value was converted to match the field type", details: { field: u2, originalValue: c2, sanitizedValue: f2 } }), d.add(u2.name), u2 && (s || u2.editable)) {
      const e2 = fe(u2, f2);
      if (e2) return a(Ie(e2, u2, f2));
      t2[u2.name] = f2;
    }
  }
  for (const n of (e == null ? void 0 : e.requiredFields) ?? []) if (!d.has(n.name)) return a(`missing required field "${n.name}"`);
  return null;
}
function p(e, t2) {
  let n = t2;
  return "string" == typeof t2 && ie(e) ? n = parseFloat(t2) : null != t2 && te(e) && "string" != typeof t2 && (n = String(t2)), ae(n);
}
var h;
function g(n, i) {
  if (!n || !I(i)) return n;
  if ("rings" in n || "paths" in n) {
    if (t(h)) throw new TypeError("geometry engine not loaded");
    return h.simplify(i, n);
  }
  return n;
}
async function y() {
  return t(h) && (h = await import("./geometryEngineJSON-2XCCV7A5.js")), h;
}
async function w(e, n) {
  !I(e) || "esriGeometryPolygon" !== n && "esriGeometryPolyline" !== n || await y();
}

export {
  a,
  f,
  m,
  g,
  w
};
//# sourceMappingURL=chunk-IF2TPQ7J.js.map
