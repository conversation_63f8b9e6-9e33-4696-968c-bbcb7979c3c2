{"version": 3, "sources": ["../../@arcgis/core/renderers/SimpleRenderer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import{clone as s}from\"../core/lang.js\";import{property as r}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{enumeration as t}from\"../core/accessorSupport/decorators/enumeration.js\";import{subclass as o}from\"../core/accessorSupport/decorators/subclass.js\";import i from\"./Renderer.js\";import{VisualVariablesMixin as l}from\"./mixins/VisualVariablesMixin.js\";import{rendererSymbolProperty as a}from\"./support/commonProperties.js\";var c;let n=c=class extends(l(i)){constructor(e){super(e),this.description=null,this.label=null,this.symbol=null,this.type=\"simple\"}async collectRequiredFields(e,s){await Promise.all([this.collectSymbolFields(e,s),this.collectVVRequiredFields(e,s)])}async collectSymbolFields(e,s){await Promise.all(this.getSymbols().map((r=>r.collectRequiredFields(e,s))))}getSymbol(e,s){return this.symbol}async getSymbolAsync(e,s){return this.symbol}getSymbols(){return this.symbol?[this.symbol]:[]}getAttributeHash(){return this.visualVariables&&this.visualVariables.reduce(((e,s)=>e+s.getAttributeHash()),\"\")}getMeshHash(){return this.getSymbols().reduce(((e,s)=>e+JSON.stringify(s)),\"\")}get arcadeRequired(){return this.arcadeRequiredForVisualVariables}clone(){return new c({description:this.description,label:this.label,symbol:this.symbol&&this.symbol.clone(),visualVariables:s(this.visualVariables),authoringInfo:this.authoringInfo&&this.authoringInfo.clone()})}};e([r({type:String,json:{write:!0}})],n.prototype,\"description\",void 0),e([r({type:String,json:{write:!0}})],n.prototype,\"label\",void 0),e([r(a)],n.prototype,\"symbol\",void 0),e([t({simple:\"simple\"})],n.prototype,\"type\",void 0),n=c=e([o(\"esri.renderers.SimpleRenderer\")],n);const p=n;export{p as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIihB,IAAI;AAAE,IAAI,IAAE,IAAE,cAAcA,GAAEC,EAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,cAAY,MAAK,KAAK,QAAM,MAAK,KAAK,SAAO,MAAK,KAAK,OAAK;AAAA,EAAQ;AAAA,EAAC,MAAM,sBAAsBA,IAAE,GAAE;AAAC,UAAM,QAAQ,IAAI,CAAC,KAAK,oBAAoBA,IAAE,CAAC,GAAE,KAAK,wBAAwBA,IAAE,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,oBAAoBA,IAAE,GAAE;AAAC,UAAM,QAAQ,IAAI,KAAK,WAAW,EAAE,IAAK,OAAG,EAAE,sBAAsBA,IAAE,CAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE,GAAE;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,MAAM,eAAeA,IAAE,GAAE;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,SAAO,CAAC,KAAK,MAAM,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAO,KAAK,mBAAiB,KAAK,gBAAgB,OAAQ,CAACA,IAAE,MAAIA,KAAE,EAAE,iBAAiB,GAAG,EAAE;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK,WAAW,EAAE,OAAQ,CAACA,IAAE,MAAIA,KAAE,KAAK,UAAU,CAAC,GAAG,EAAE;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAgC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,aAAY,KAAK,aAAY,OAAM,KAAK,OAAM,QAAO,KAAK,UAAQ,KAAK,OAAO,MAAM,GAAE,iBAAgB,EAAE,KAAK,eAAe,GAAE,eAAc,KAAK,iBAAe,KAAK,cAAc,MAAM,EAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,QAAO,SAAQ,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,+BAA+B,CAAC,GAAE,CAAC;AAAE,IAAMD,KAAE;", "names": ["y", "p", "e"]}