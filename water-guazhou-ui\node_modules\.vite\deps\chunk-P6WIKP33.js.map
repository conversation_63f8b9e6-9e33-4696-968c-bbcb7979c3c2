{"version": 3, "sources": ["../../@arcgis/core/views/2d/interactive/SnappingVisualizer2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../geometry.js\";import e from\"../../../Graphic.js\";import\"../../../symbols.js\";import{makeHandle as r}from\"../../../core/handleUtils.js\";import{px2pt as i}from\"../../../core/screenUtils.js\";import{a as t,g as n}from\"../../../chunks/vec2.js\";import{a as o}from\"../../../chunks/vec2f64.js\";import{c as a}from\"../../../chunks/vec3f64.js\";import{LineCapStyle as l,LineJoinStyle as s}from\"../../../symbols/cim/enums.js\";import{colors as m}from\"../../draw/support/settings.js\";import{defaults as p}from\"../../interactive/snapping/Settings.js\";import{SnappingVisualizer as y}from\"../../interactive/snapping/SnappingVisualizer.js\";import c from\"../../../symbols/SimpleMarkerSymbol.js\";import b from\"../../../symbols/SimpleLineSymbol.js\";import S from\"../../../symbols/CIMSymbol.js\";import h from\"../../../geometry/Point.js\";import g from\"../../../geometry/Polyline.js\";class u extends y{constructor(e){super(),this._graphicsLayer=e}visualizeIntersectionPoint(e,r){return this._visualizeSnappingIndicator(new h({x:e.intersectionPoint[0],y:e.intersectionPoint[1],spatialReference:r.spatialReference}),f)}visualizePoint(e,r){return this._visualizeSnappingIndicator(new h({x:e.point[0],y:e.point[1],spatialReference:r.spatialReference}),I)}visualizeLine(e,r){return this._visualizeSnappingIndicator(new g({paths:[[e.lineStart,e.lineEnd]],spatialReference:r.spatialReference}),P)}visualizeParallelSign(e,r){return this._visualizeSnappingIndicator(new g({paths:[[e.lineStart,e.lineEnd]],spatialReference:r.spatialReference}),x)}visualizeRightAngleQuad(e,r){return this._visualizeSnappingIndicator(new g({paths:[[e.previousVertex,e.centerVertex,e.nextVertex]],spatialReference:r.spatialReference}),z(e))}_visualizeSnappingIndicator(i,t){const n=new e({geometry:i,symbol:t});return this._graphicsLayer.add(n),r((()=>{this._graphicsLayer.remove(n)}))}}const M=m.main.toArray(),d=[...m.main.toRgb(),100],f=new c({outline:new b({width:1.5,color:M}),size:15,color:[0,0,0,0]}),I=new c({outline:{width:.5,color:[0,0,0,1]},size:10,color:M}),P=new S({data:{type:\"CIMSymbolReference\",symbol:{type:\"CIMLineSymbol\",symbolLayers:[{type:\"CIMSolidStroke\",enable:!0,capStyle:l.Butt,joinStyle:s.Round,miterLimit:10,width:i(p.lineHintWidthTarget),color:M}]}}}),x=new S({data:{type:\"CIMSymbolReference\",symbol:{type:\"CIMLineSymbol\",symbolLayers:[{type:\"CIMVectorMarker\",enable:!0,anchorPoint:{x:0,y:-1,z:0},anchorPointUnits:\"Relative\",size:5,markerPlacement:{type:\"CIMMarkerPlacementOnLine\",placePerPart:!0,angleToLine:!0,relativeTo:\"LineMiddle\"},frame:{xmin:-5,ymin:-1.5,xmax:5,ymax:1.5},markerGraphics:[{type:\"CIMMarkerGraphic\",geometry:{rings:[[[7,0],[-7,0],[-7,1.5],[7,1.5]]]},symbol:{type:\"CIMPolygonSymbol\",symbolLayers:[{type:\"CIMSolidFill\",enable:!0,color:M}]}}],scaleSymbolsProportionally:!0,respectFrame:!0},{type:\"CIMVectorMarker\",enable:!0,anchorPoint:{x:0,y:1,z:0},anchorPointUnits:\"Relative\",size:5,markerPlacement:{type:\"CIMMarkerPlacementOnLine\",placePerPart:!0,angleToLine:!0,relativeTo:\"LineMiddle\"},frame:{xmin:-5,ymin:-1.5,xmax:5,ymax:1.5},markerGraphics:[{type:\"CIMMarkerGraphic\",geometry:{rings:[[[7,0],[-7,0],[-7,-1.5],[7,-1.5]]]},symbol:{type:\"CIMPolygonSymbol\",symbolLayers:[{type:\"CIMSolidFill\",enable:!0,color:M}]}}],scaleSymbolsProportionally:!0,respectFrame:!0}]}}}),v=e=>new S({data:{type:\"CIMSymbolReference\",symbol:{type:\"CIMLineSymbol\",symbolLayers:[{type:\"CIMVectorMarker\",enable:!0,anchorPoint:{x:.5,y:.5,z:0},anchorPointUnits:\"Relative\",size:i(p.rightAngleHintSize),rotation:e,markerPlacement:{type:\"CIMMarkerPlacementOnVertices\",placePerPart:!0,angleToLine:!0,placeOnEndPoints:!1},frame:{xmin:-5,ymin:-5,xmax:5,ymax:5},markerGraphics:[{type:\"CIMMarkerGraphic\",geometry:{paths:[[[5,-5],[-5,-5],[-5,5],[5,5],[5,-5]]]},symbol:{type:\"CIMLineSymbol\",symbolLayers:[{type:\"CIMSolidStroke\",enable:!0,capStyle:\"Butt\",joinStyle:\"Round\",miterLimit:10,width:i(p.rightAngleHintOutlineSize),color:M},{type:\"CIMSolidFill\",enable:!0,color:d}]}}],scaleSymbolsProportionally:!0,respectFrame:!0}]}}}),C=v(45),L=v(225),z=(()=>{const e=o(),r=o(),i=a();return o=>(t(e,o.centerVertex,o.previousVertex),t(r,o.nextVertex,o.previousVertex),n(i,e,r),i[2]<0?C:L)})();export{u as SnappingVisualizer2D};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI02B,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAM,GAAE,KAAK,iBAAeA;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAEC,IAAE;AAAC,WAAO,KAAK,4BAA4B,IAAI,EAAE,EAAC,GAAED,GAAE,kBAAkB,CAAC,GAAE,GAAEA,GAAE,kBAAkB,CAAC,GAAE,kBAAiBC,GAAE,iBAAgB,CAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAEC,IAAE;AAAC,WAAO,KAAK,4BAA4B,IAAI,EAAE,EAAC,GAAED,GAAE,MAAM,CAAC,GAAE,GAAEA,GAAE,MAAM,CAAC,GAAE,kBAAiBC,GAAE,iBAAgB,CAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,WAAO,KAAK,4BAA4B,IAAI,EAAE,EAAC,OAAM,CAAC,CAACD,GAAE,WAAUA,GAAE,OAAO,CAAC,GAAE,kBAAiBC,GAAE,iBAAgB,CAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAEC,IAAE;AAAC,WAAO,KAAK,4BAA4B,IAAI,EAAE,EAAC,OAAM,CAAC,CAACD,GAAE,WAAUA,GAAE,OAAO,CAAC,GAAE,kBAAiBC,GAAE,iBAAgB,CAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBD,IAAEC,IAAE;AAAC,WAAO,KAAK,4BAA4B,IAAI,EAAE,EAAC,OAAM,CAAC,CAACD,GAAE,gBAAeA,GAAE,cAAaA,GAAE,UAAU,CAAC,GAAE,kBAAiBC,GAAE,iBAAgB,CAAC,GAAE,EAAED,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,4BAA4B,GAAE,GAAE;AAAC,UAAME,KAAE,IAAI,EAAE,EAAC,UAAS,GAAE,QAAO,EAAC,CAAC;AAAE,WAAO,KAAK,eAAe,IAAIA,EAAC,GAAE,EAAG,MAAI;AAAC,WAAK,eAAe,OAAOA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAC,IAAM,IAAEC,GAAE,KAAK,QAAQ;AAAvB,IAAyBC,KAAE,CAAC,GAAGD,GAAE,KAAK,MAAM,GAAE,GAAG;AAAjD,IAAmD,IAAE,IAAIE,GAAE,EAAC,SAAQ,IAAIC,GAAE,EAAC,OAAM,KAAI,OAAM,EAAC,CAAC,GAAE,MAAK,IAAG,OAAM,CAAC,GAAE,GAAE,GAAE,CAAC,EAAC,CAAC;AAAvH,IAAyH,IAAE,IAAID,GAAE,EAAC,SAAQ,EAAC,OAAM,KAAG,OAAM,CAAC,GAAE,GAAE,GAAE,CAAC,EAAC,GAAE,MAAK,IAAG,OAAM,EAAC,CAAC;AAArL,IAAuL,IAAE,IAAI,EAAE,EAAC,MAAK,EAAC,MAAK,sBAAqB,QAAO,EAAC,MAAK,iBAAgB,cAAa,CAAC,EAAC,MAAK,kBAAiB,QAAO,MAAG,UAAS,EAAE,MAAK,WAAUE,GAAE,OAAM,YAAW,IAAG,OAAM,EAAE,EAAE,mBAAmB,GAAE,OAAM,EAAC,CAAC,EAAC,EAAC,EAAC,CAAC;AAAvY,IAAyY,IAAE,IAAI,EAAE,EAAC,MAAK,EAAC,MAAK,sBAAqB,QAAO,EAAC,MAAK,iBAAgB,cAAa,CAAC,EAAC,MAAK,mBAAkB,QAAO,MAAG,aAAY,EAAC,GAAE,GAAE,GAAE,IAAG,GAAE,EAAC,GAAE,kBAAiB,YAAW,MAAK,GAAE,iBAAgB,EAAC,MAAK,4BAA2B,cAAa,MAAG,aAAY,MAAG,YAAW,aAAY,GAAE,OAAM,EAAC,MAAK,IAAG,MAAK,MAAK,MAAK,GAAE,MAAK,IAAG,GAAE,gBAAe,CAAC,EAAC,MAAK,oBAAmB,UAAS,EAAC,OAAM,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,GAAG,CAAC,CAAC,EAAC,GAAE,QAAO,EAAC,MAAK,oBAAmB,cAAa,CAAC,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAM,EAAC,CAAC,EAAC,EAAC,CAAC,GAAE,4BAA2B,MAAG,cAAa,KAAE,GAAE,EAAC,MAAK,mBAAkB,QAAO,MAAG,aAAY,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,kBAAiB,YAAW,MAAK,GAAE,iBAAgB,EAAC,MAAK,4BAA2B,cAAa,MAAG,aAAY,MAAG,YAAW,aAAY,GAAE,OAAM,EAAC,MAAK,IAAG,MAAK,MAAK,MAAK,GAAE,MAAK,IAAG,GAAE,gBAAe,CAAC,EAAC,MAAK,oBAAmB,UAAS,EAAC,OAAM,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,IAAI,GAAE,CAAC,GAAE,IAAI,CAAC,CAAC,EAAC,GAAE,QAAO,EAAC,MAAK,oBAAmB,cAAa,CAAC,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAM,EAAC,CAAC,EAAC,EAAC,CAAC,GAAE,4BAA2B,MAAG,cAAa,KAAE,CAAC,EAAC,EAAC,EAAC,CAAC;AAAj5C,IAAm5C,IAAE,CAAAP,OAAG,IAAI,EAAE,EAAC,MAAK,EAAC,MAAK,sBAAqB,QAAO,EAAC,MAAK,iBAAgB,cAAa,CAAC,EAAC,MAAK,mBAAkB,QAAO,MAAG,aAAY,EAAC,GAAE,KAAG,GAAE,KAAG,GAAE,EAAC,GAAE,kBAAiB,YAAW,MAAK,EAAE,EAAE,kBAAkB,GAAE,UAASA,IAAE,iBAAgB,EAAC,MAAK,gCAA+B,cAAa,MAAG,aAAY,MAAG,kBAAiB,MAAE,GAAE,OAAM,EAAC,MAAK,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK,EAAC,GAAE,gBAAe,CAAC,EAAC,MAAK,oBAAmB,UAAS,EAAC,OAAM,CAAC,CAAC,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,EAAC,GAAE,QAAO,EAAC,MAAK,iBAAgB,cAAa,CAAC,EAAC,MAAK,kBAAiB,QAAO,MAAG,UAAS,QAAO,WAAU,SAAQ,YAAW,IAAG,OAAM,EAAE,EAAE,yBAAyB,GAAE,OAAM,EAAC,GAAE,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAMI,GAAC,CAAC,EAAC,EAAC,CAAC,GAAE,4BAA2B,MAAG,cAAa,KAAE,CAAC,EAAC,EAAC,EAAC,CAAC;AAApmE,IAAsmE,IAAE,EAAE,EAAE;AAA5mE,IAA8mE,IAAE,EAAE,GAAG;AAArnE,IAAunE,KAAG,MAAI;AAAC,QAAMJ,KAAEE,GAAE,GAAED,KAAEC,GAAE,GAAE,IAAEA,GAAE;AAAE,SAAO,CAAAC,QAAI,EAAEH,IAAEG,GAAE,cAAaA,GAAE,cAAc,GAAE,EAAEF,IAAEE,GAAE,YAAWA,GAAE,cAAc,GAAE,EAAE,GAAEH,IAAEC,EAAC,GAAE,EAAE,CAAC,IAAE,IAAE,IAAE;AAAE,GAAG;", "names": ["e", "r", "n", "o", "d", "y", "m", "w"]}