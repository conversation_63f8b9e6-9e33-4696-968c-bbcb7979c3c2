import {
  i as i2
} from "./chunk-I73RJE3H.js";
import "./chunk-OWSDEANX.js";
import {
  o
} from "./chunk-JK6A4R7D.js";
import {
  r as r2
} from "./chunk-WIKVTG73.js";
import {
  He
} from "./chunk-33Z6JDMT.js";
import "./chunk-L7LRY3AT.js";
import "./chunk-ZJC3GHA7.js";
import "./chunk-TVTTDN54.js";
import "./chunk-O4T45CJC.js";
import "./chunk-NNKS4NNY.js";
import "./chunk-LNCHRZJI.js";
import "./chunk-OA2XSLRZ.js";
import "./chunk-CIHGHHEZ.js";
import "./chunk-G3QAWKCD.js";
import "./chunk-FZKLUDSB.js";
import "./chunk-IKGI4J4I.js";
import "./chunk-MNWHGD3K.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-6GKVSPTV.js";
import "./chunk-4FIRBBKR.js";
import "./chunk-EKOSN3EW.js";
import "./chunk-RZCOX454.js";
import "./chunk-2WMCP27R.js";
import "./chunk-UVJUTW2U.js";
import "./chunk-RR74IWZB.js";
import "./chunk-KTB2COPC.js";
import "./chunk-FWXA4I6D.js";
import "./chunk-NQ3OACUM.js";
import {
  d
} from "./chunk-HTXGAKOK.js";
import "./chunk-WL6G2MRC.js";
import "./chunk-OQK7L3JR.js";
import "./chunk-JZKMTUDN.js";
import "./chunk-UCWK623G.js";
import "./chunk-JV6TBH5W.js";
import "./chunk-77ZF73NA.js";
import {
  a,
  c,
  i,
  u
} from "./chunk-55WN4LCX.js";
import "./chunk-VSFGOST3.js";
import "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-ZVU4V5QV.js";
import "./chunk-2AZSZWPE.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import {
  x as x2
} from "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-KE7SPCM7.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-MIA6BJ32.js";
import "./chunk-NGPCXWDX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FBVKALLT.js";
import "./chunk-6PEIQDFP.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-BS3GJQ77.js";
import "./chunk-IOBN373Z.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-NEJXVYTI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-VJW7RCN7.js";
import "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-ZL6CFFJK.js";
import "./chunk-TWFTBWXP.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import {
  f,
  y
} from "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import {
  b
} from "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  E,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s as s3
} from "./chunk-4RZONHOY.js";
import {
  s as s2
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import {
  s
} from "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/save/featureLayerUtils.js
var I = s2.getLogger("esri.layers.FeatureLayer");
var S = "Feature Service";
function g(e, t2) {
  return `Layer (title: ${e.title}, id: ${e.id}) of type '${e.declaredClass}' ${t2}`;
}
function j(e, r3) {
  if (r3.type !== S) throw new s3("feature-layer:portal-item-wrong-type", g(e, `should have portal item of type "${S}"`));
}
async function L(e) {
  if (await e.load(), f(e)) throw new s3("feature-layer:save", g(e, "using an in-memory source cannot be saved to a portal item"));
}
function P(e, r3) {
  let a2 = (e.messages ?? []).filter(({ type: e2 }) => "error" === e2).map(({ name: e2, message: r4, details: a3 }) => new s3(e2, r4, a3));
  if ((r3 == null ? void 0 : r3.ignoreUnsupported) && (a2 = a2.filter(({ name: e2 }) => "layer:unsupported" !== e2 && "symbol:unsupported" !== e2 && "symbol-layer:unsupported" !== e2 && "property:unsupported" !== e2 && "url:unsupported" !== e2)), a2.length > 0) throw new s3("feature-layer:save", "Failed to save feature layer due to unsupported or invalid content. See 'details.errors' for more detailed information", { errors: a2 });
}
async function J(e, t2, r3) {
  "beforeSave" in e && "function" == typeof e.beforeSave && await e.beforeSave();
  const a2 = e.write({}, t2);
  return P(t2, r3), a2;
}
function N(e) {
  const { layer: t2, layerJSON: r3 } = e;
  return t2.isTable ? { layers: [], tables: [r3] } : { layers: [r3], tables: [] };
}
function O(e) {
  i(e, u.JSAPI), e.typeKeywords && (e.typeKeywords = e.typeKeywords.filter((e2, t2, r3) => r3.indexOf(e2) === t2));
}
function E2(e) {
  const r3 = e.portalItem;
  if (!r3) throw I.error("save: requires the portalItem property to be set"), new s3("feature-layer:portal-item-not-set", g(e, "requires the portalItem property to be set"));
  if (!r3.loaded) throw new s3("feature-layer:portal-item-not-loaded", g(e, "cannot be saved to a portal item that does not exist or is inaccessible"));
  j(e, r3);
}
async function T(e, t2) {
  return /\/\d+\/?$/.test(e.url ?? "") ? N(t2[0]) : $(e, t2);
}
async function $(e, t2) {
  const { layer: { url: r3, customParameters: a2, apiKey: o2 } } = t2[0];
  let s4 = await e.fetchData("json");
  s4 && null != s4.layers && null != s4.tables || (s4 = await x3(s4, { url: r3 ?? "", customParameters: a2, apiKey: o2 }, t2.map((e2) => e2.layer.layerId)));
  for (const l of t2) K(l.layer, l.layerJSON, s4);
  return s4;
}
async function x3(e, t2, r3) {
  var a2, o2;
  e || (e = {}), (a2 = e).layers || (a2.layers = []), (o2 = e).tables || (o2.tables = []);
  const { url: s4, customParameters: l, apiKey: i3 } = t2, { serviceJSON: n, layersJSON: u2 } = await r2(s4, { customParameters: l, apiKey: i3 }), c2 = A(e.layers, n.layers, r3), y2 = A(e.tables, n.tables, r3);
  e.layers = c2.itemResources, e.tables = y2.itemResources;
  const d2 = [...c2.added, ...y2.added], m = u2 ? [...u2.layers, ...u2.tables] : [];
  return await U(e, d2, s4, m), e;
}
function A(t2, r3, a2) {
  const o2 = s(t2, r3, (e, t3) => e.id === t3.id);
  t2 = t2.filter((e) => !o2.removed.some((t3) => t3.id === e.id));
  const s4 = o2.added.map(({ id: e }) => ({ id: e }));
  return s4.forEach(({ id: e }) => {
    t2.push({ id: e });
  }), { itemResources: t2, added: s4.filter(({ id: e }) => !a2.includes(e)) };
}
async function U(e, t2, r3, o2) {
  const s4 = t2.map(({ id: e2 }) => new He({ url: r3, layerId: e2, sourceJSON: o2.find(({ id: t3 }) => t3 === e2) }));
  await E(s4.map((e2) => e2.load())), s4.forEach((t3) => {
    const { layerId: r4, loaded: o3, defaultPopupTemplate: s5 } = t3;
    if (!o3 || t(s5)) return;
    K(t3, { id: r4, popupInfo: s5.toJSON() }, e);
  });
}
function K(e, t2, r3) {
  e.isTable ? F(r3.tables, t2) : F(r3.layers, t2);
}
function F(e, t2) {
  if (!e) return;
  const r3 = e.findIndex(({ id: e2 }) => e2 === t2.id);
  -1 === r3 ? e.push(t2) : e[r3] = t2;
}
function R(e) {
  const { portalItem: t2 } = e;
  return y(e) && !e.dynamicDataSource && !!(t2 == null ? void 0 : t2.loaded) && t2.type === S;
}
async function D(e) {
  if (!(e == null ? void 0 : e.length)) throw new s3("feature-layer-utils-saveall:missing-parameters", "'layers' array should contain at least one feature layer");
  await Promise.all(e.map((e2) => e2.load()));
  for (const o2 of e) if (!R(o2)) throw new s3("feature-layer-utils-saveall:invalid-parameters", `'layers' array should only contain layers or tables in a feature service loaded from 'Feature Service' item. ${g(o2, "does not conform")}`, { layer: o2 });
  const r3 = e.map((e2) => e2.portalItem.id);
  if (new Set(r3).size > 1) throw new s3("feature-layer-utils-saveall:invalid-parameters", "All layers in the 'layers' array should be loaded from the same portal item");
  const a2 = e.map((e2) => e2.layerId);
  if (new Set(a2).size !== a2.length) throw new s3("feature-layer-utils-saveall:invalid-parameters", "'layers' array should contain only one instance each of layer or table in a feature service");
}
function q(e, t2) {
  var r3, a2;
  let o2 = x2.from(t2);
  return o2.id && (o2 = o2.clone(), o2.id = null), (r3 = o2).type ?? (r3.type = S), (a2 = o2).portal ?? (a2.portal = b.getDefault()), j(e, o2), o2;
}
async function z(e, t2) {
  const { url: r3, layerId: a2, title: s4, fullExtent: l, isTable: i3 } = e, n = d(r3), p = r(n) && "FeatureServer" === n.serverType;
  t2.url = p ? r3 : `${r3}/${a2}`, t2.title || (t2.title = s4), t2.extent = null, !i3 && r(l) && (t2.extent = await a(l)), c(t2, u.METADATA), c(t2, u.MULTI_LAYER), i(t2, u.SINGLE_LAYER), i3 && i(t2, u.TABLE), O(t2);
}
async function C(e, t2, r3) {
  var _a;
  const a2 = e.portal;
  await (a2 == null ? void 0 : a2.signIn()), await ((_a = a2 == null ? void 0 : a2.user) == null ? void 0 : _a.addItem({ item: e, data: t2, folder: r3 == null ? void 0 : r3.folder }));
}
var M = x(Y);
async function Y(e, t2) {
  await L(e), E2(e);
  const r3 = e.portalItem, a2 = o(r3), o2 = await J(e, a2, t2), s4 = await T(r3, [{ layer: e, layerJSON: o2 }]);
  return O(r3), await r3.update({ data: s4 }), i2(a2), r3;
}
var _ = x(async (e, t2) => {
  await D(e);
  const r3 = e[0].portalItem, a2 = o(r3), o2 = await Promise.all(e.map((e2) => J(e2, a2, t2))), s4 = await T(r3, e.map((e2, t3) => ({ layer: e2, layerJSON: o2[t3] })));
  return O(r3), await r3.update({ data: s4 }), await Promise.all(e.slice(1).map((e2) => e2.portalItem.reload())), i2(a2), r3.clone();
});
var B = x(G);
async function G(e, t2, r3) {
  await L(e);
  const a2 = q(e, t2), o2 = o(a2), s4 = N({ layer: e, layerJSON: await J(e, o2, r3) });
  return await z(e, a2), await C(a2, s4, r3), e.portalItem = a2, i2(o2), a2;
}
export {
  M as save,
  _ as saveAll,
  B as saveAs
};
//# sourceMappingURL=featureLayerUtils-4KBFM3KJ.js.map
