{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/materials/PatternStyle.js", "../../@arcgis/core/chunks/Pattern.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar a;!function(a){a[a.Horizontal=0]=\"Horizontal\",a[a.Vertical=1]=\"Vertical\",a[a.Cross=2]=\"Cross\",a[a.ForwardDiagonal=3]=\"ForwardDiagonal\",a[a.BackwardDiagonal=4]=\"BackwardDiagonal\",a[a.DiagonalCross=5]=\"DiagonalCross\",a[a.COUNT=6]=\"COUNT\"}(a||(a={}));export{a as Style};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{addNearFar as e,addLinearDepth as o}from\"../views/3d/webgl-engine/core/shaderLibrary/ForwardLinearDepth.glsl.js\";import{ShaderOutput as t}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as r}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{Transform as a}from\"../views/3d/webgl-engine/core/shaderLibrary/Transform.glsl.js\";import{VertexColor as i}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/VertexColor.glsl.js\";import{OutputDepth as n}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputDepth.glsl.js\";import{OutputHighlight as l}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputHighlight.glsl.js\";import{multipassTerrainTest as c}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{symbolAlphaCutoff as d}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{ColorConversion as s}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ColorConversion.glsl.js\";import{addProjViewLocalOrigin as p,addCameraPosition as g}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float4PassUniform as u}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{FloatPassUniform as v}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as m}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as f}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{TransparencyPassType as h}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";import{VertexAttribute as w}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";import{Style as b}from\"../views/3d/webgl-engine/materials/PatternStyle.js\";const y=.70710678118,S=y,j=.08715574274;function x(x){const T=new f,C=x.hasMultipassTerrain&&(x.output===t.Color||x.output===t.Alpha);x.draped||T.extensions.add(\"GL_OES_standard_derivatives\");const{vertex:R,fragment:$}=T;p(R,x),T.include(a,x),T.include(i,x),x.draped?R.uniforms.add(new v(\"worldToScreenRatio\",((e,o)=>1/o.screenToPCSRatio))):T.attributes.add(w.BOUNDINGRECT,\"mat3\"),T.attributes.add(w.POSITION,\"vec3\"),T.attributes.add(w.UVMAPSPACE,\"vec4\"),T.varyings.add(\"vpos\",\"vec3\"),T.varyings.add(\"vuv\",\"vec2\"),C&&T.varyings.add(\"depth\",\"float\");const D=x.style===b.ForwardDiagonal||x.style===b.BackwardDiagonal||x.style===b.DiagonalCross;D&&R.code.add(m`\n      const mat2 rotate45 = mat2(${m.float(y)}, ${m.float(-S)},\n                                 ${m.float(S)}, ${m.float(y)});\n    `),x.draped||(g(R,x),R.uniforms.add(new v(\"worldToScreenPerDistanceRatio\",((e,o)=>1/o.camera.perScreenPixelRatio))),R.code.add(m`vec3 projectPointToLineSegment(vec3 center, vec3 halfVector, vec3 point) {\nfloat projectedLength = dot(halfVector, point - center) / dot(halfVector, halfVector);\nreturn center + halfVector * clamp(projectedLength, -1.0, 1.0);\n}`),R.code.add(m`vec3 intersectRayPlane(vec3 rayDir, vec3 rayOrigin, vec3 planeNormal, vec3 planePoint) {\nfloat d = dot(planeNormal, planePoint);\nfloat t = (d - dot(planeNormal, rayOrigin)) / dot(planeNormal, rayDir);\nreturn rayOrigin + t * rayDir;\n}`),R.code.add(m`\n      float boundingRectDistanceToCamera() {\n        vec3 center = vec3(boundingRect[0][0], boundingRect[0][1], boundingRect[0][2]);\n        vec3 halfU = vec3(boundingRect[1][0], boundingRect[1][1], boundingRect[1][2]);\n        vec3 halfV = vec3(boundingRect[2][0], boundingRect[2][1], boundingRect[2][2]);\n        vec3 n = normalize(cross(halfU, halfV));\n\n        vec3 viewDir = - vec3(view[0][2], view[1][2], view[2][2]);\n\n        float viewAngle = dot(viewDir, n);\n        float minViewAngle = ${m.float(j)};\n\n        if (abs(viewAngle) < minViewAngle) {\n          // view direction is (almost) parallel to plane -> clamp it to min angle\n          float normalComponent = sign(viewAngle) * minViewAngle - viewAngle;\n          viewDir = normalize(viewDir + normalComponent * n);\n        }\n\n        // intersect view direction with infinite plane that contains bounding rect\n        vec3 planeProjected = intersectRayPlane(viewDir, cameraPosition, n, center);\n\n        // clip to bounds by projecting to u and v line segments individually\n        vec3 uProjected = projectPointToLineSegment(center, halfU, planeProjected);\n        vec3 vProjected = projectPointToLineSegment(center, halfV, planeProjected);\n\n        // use to calculate the closest point to camera on bounding rect\n        vec3 closestPoint = uProjected + vProjected - center;\n\n        return length(closestPoint - cameraPosition);\n      }\n    `)),R.code.add(m`\n    vec2 scaledUV() {\n      vec2 uv = uvMapSpace.xy ${D?\" * rotate45\":\"\"};\n      vec2 uvCellOrigin = uvMapSpace.zw ${D?\" * rotate45\":\"\"};\n\n      ${x.draped?\"\":m`\n            float distanceToCamera = boundingRectDistanceToCamera();\n            float worldToScreenRatio = worldToScreenPerDistanceRatio / distanceToCamera;\n          `}\n\n      // Logarithmically discretize ratio to avoid jittering\n      float step = 0.1;\n      float discreteWorldToScreenRatio = log(worldToScreenRatio);\n      discreteWorldToScreenRatio = ceil(discreteWorldToScreenRatio / step) * step;\n      discreteWorldToScreenRatio = exp(discreteWorldToScreenRatio);\n\n      vec2 uvOffset = mod(uvCellOrigin * discreteWorldToScreenRatio, ${m.float(x.patternSpacing)});\n      return uvOffset + (uv * discreteWorldToScreenRatio);\n    }\n  `);const V=x.output===t.Depth;return V&&(T.include(n,x),e(T),o(T)),R.code.add(m`\n    void main(void) {\n      vuv = scaledUV();\n      vpos = position;\n      ${C?\"depth = (view * vec4(vpos, 1.0)).z;\":\"\"}\n      forwardNormalizedVertexColor();\n      gl_Position = ${V?m`transformPositionWithDepth(proj, view, vpos, nearFar, linearDepth);`:m`transformPosition(proj, view, vpos);`}\n    }\n  `),T.include(r,x),$.include(s),x.draped&&$.uniforms.add(new v(\"texelSize\",((e,o)=>1/o.camera.pixelRatio))),x.output===t.Highlight&&T.include(l,x),C&&T.include(c,x),x.output!==t.Highlight&&($.code.add(m`\n      const float lineWidth = ${m.float(x.lineWidth)};\n      const float spacing = ${m.float(x.patternSpacing)};\n      const float spacingINV = ${m.float(1/x.patternSpacing)};\n\n      float coverage(float p, float txlSize) {\n        p = mod(p, spacing);\n\n        float halfTxlSize = txlSize / 2.0;\n\n        float start = p - halfTxlSize;\n        float end = p + halfTxlSize;\n\n        float coverage = (ceil(end * spacingINV) - floor(start * spacingINV)) * lineWidth;\n        coverage -= min(lineWidth, mod(start, spacing));\n        coverage -= max(lineWidth - mod(end, spacing), 0.0);\n\n        return coverage / txlSize;\n      }\n    `),x.draped||$.code.add(m`const int maxSamples = 5;\nfloat sample(float p) {\nvec2 dxdy = abs(vec2(dFdx(p), dFdy(p)));\nfloat fwidth = dxdy.x + dxdy.y;\nivec2 samples = 1 + ivec2(clamp(dxdy, 0.0, float(maxSamples - 1)));\nvec2 invSamples = 1.0 / vec2(samples);\nfloat accumulator = 0.0;\nfor (int j = 0; j < maxSamples; j++) {\nif(j >= samples.y) {\nbreak;\n}\nfor (int i = 0; i < maxSamples; i++) {\nif(i >= samples.x) {\nbreak;\n}\nvec2 step = vec2(i,j) * invSamples - 0.5;\naccumulator += coverage(p + step.x * dxdy.x + step.y * dxdy.y, fwidth);\n}\n}\naccumulator /= float(samples.x * samples.y);\nreturn accumulator;\n}`)),$.uniforms.add(new u(\"uColor\",(e=>e.color))),$.code.add(m`\n    void main() {\n      discardBySlice(vpos);\n      ${C?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n      vec4 color = ${x.hasVertexColors?\"vColor * uColor;\":\"uColor;\"}\n      color = highlightSlice(color, vpos);\n\n      ${x.output!==t.Highlight?m`color.a *= ${P(x)};`:\"\"}\n\n      ${x.output===t.ObjectAndLayerIdColor?m`color.a = 1.0;`:\"\"}\n\n      if (color.a < ${m.float(d)}) {\n        discard;\n      }\n\n      ${x.output===t.Alpha?m`gl_FragColor = vec4(color.a);`:\"\"}\n\n      ${x.output===t.Color?m`gl_FragColor = color; ${x.transparencyPassType===h.Color?\"gl_FragColor = premultiplyAlpha(gl_FragColor);\":\"\"}`:\"\"}\n      ${x.output===t.Highlight?m`outputHighlight();`:\"\"}\n      ${x.output===t.Depth?m`outputDepth(linearDepth);`:\"\"};\n    }\n  `),T}function P(e){function o(o){return e.draped?m`coverage(vuv.${o}, texelSize)`:m`sample(vuv.${o})`}switch(e.style){case b.ForwardDiagonal:case b.Horizontal:return o(\"y\");case b.BackwardDiagonal:case b.Vertical:return o(\"x\");case b.DiagonalCross:case b.Cross:return m`\n        1.0 - (1.0 - ${o(\"x\")}) * (1.0 - ${o(\"y\")})\n      `;default:return\"0.0\"}}const T=Object.freeze(Object.defineProperty({__proto__:null,build:x},Symbol.toStringTag,{value:\"Module\"}));export{T as P,x as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAIA;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,kBAAgB,CAAC,IAAE,mBAAkBA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,gBAAc,CAAC,IAAE,iBAAgBA,GAAEA,GAAE,QAAM,CAAC,IAAE;AAAO,EAAEA,OAAIA,KAAE,CAAC,EAAE;;;ACAw/C,IAAM,IAAE;AAAR,IAAqB,IAAE;AAAvB,IAAyB,IAAE;AAAa,SAAS,EAAEC,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAE,IAAEF,GAAE,wBAAsBA,GAAE,WAAS,EAAE,SAAOA,GAAE,WAAS,EAAE;AAAO,EAAAA,GAAE,UAAQC,GAAE,WAAW,IAAI,6BAA6B;AAAE,QAAK,EAAC,QAAO,GAAE,UAAS,EAAC,IAAEA;AAAE,IAAE,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQE,IAAEH,EAAC,GAAEA,GAAE,SAAO,EAAE,SAAS,IAAI,IAAIE,GAAE,sBAAsB,CAACC,IAAED,OAAI,IAAEA,GAAE,gBAAiB,CAAC,IAAED,GAAE,WAAW,IAAI,EAAE,cAAa,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,UAAS,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,YAAW,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAEA,GAAE,SAAS,IAAI,OAAM,MAAM,GAAE,KAAGA,GAAE,SAAS,IAAI,SAAQ,OAAO;AAAE,QAAM,IAAED,GAAE,UAAQI,GAAE,mBAAiBJ,GAAE,UAAQI,GAAE,oBAAkBJ,GAAE,UAAQI,GAAE;AAAc,OAAG,EAAE,KAAK,IAAI;AAAA,mCAC/1E,EAAE,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAAA,mCAC1B,EAAE,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAAA,KACvD,GAAEJ,GAAE,WAAS,EAAE,GAAEA,EAAC,GAAE,EAAE,SAAS,IAAI,IAAIE,GAAE,iCAAiC,CAACC,IAAED,OAAI,IAAEA,GAAE,OAAO,mBAAoB,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAGjI,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,EAIb,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+BAUgB,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAoBpC,IAAG,EAAE,KAAK,IAAI;AAAA;AAAA,gCAEa,IAAE,gBAAc,EAAE;AAAA,0CACR,IAAE,gBAAc,EAAE;AAAA;AAAA,QAEpDF,GAAE,SAAO,KAAG;AAAA;AAAA;AAAA,WAGT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uEAQ4D,EAAE,MAAMA,GAAE,cAAc,CAAC;AAAA;AAAA;AAAA,GAG7F;AAAE,QAAM,IAAEA,GAAE,WAAS,EAAE;AAAM,SAAO,MAAIC,GAAE,QAAQC,IAAEF,EAAC,GAAE,EAAEC,EAAC,GAAEI,GAAEJ,EAAC,IAAG,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,QAIxE,IAAE,wCAAsC,EAAE;AAAA;AAAA,sBAE5B,IAAE,yEAAuE,uCAAuC;AAAA;AAAA,GAEnI,GAAEA,GAAE,QAAQ,GAAED,EAAC,GAAE,EAAE,QAAQG,EAAC,GAAEH,GAAE,UAAQ,EAAE,SAAS,IAAI,IAAIE,GAAE,aAAa,CAACC,IAAED,OAAI,IAAEA,GAAE,OAAO,UAAW,CAAC,GAAEF,GAAE,WAAS,EAAE,aAAWC,GAAE,QAAQ,GAAED,EAAC,GAAE,KAAGC,GAAE,QAAQ,GAAED,EAAC,GAAEA,GAAE,WAAS,EAAE,cAAY,EAAE,KAAK,IAAI;AAAA,gCAC1K,EAAE,MAAMA,GAAE,SAAS,CAAC;AAAA,8BACtB,EAAE,MAAMA,GAAE,cAAc,CAAC;AAAA,iCACtB,EAAE,MAAM,IAAEA,GAAE,cAAc,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAgBvD,GAAEA,GAAE,UAAQ,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqB1B,IAAG,EAAE,SAAS,IAAI,IAAI,EAAE,UAAU,CAAAG,OAAGA,GAAE,KAAM,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,QAGrD,IAAE,2CAAyC,EAAE;AAAA,qBAChCH,GAAE,kBAAgB,qBAAmB,SAAS;AAAA;AAAA;AAAA,QAG3DA,GAAE,WAAS,EAAE,YAAU,eAAe,EAAEA,EAAC,CAAC,MAAI,EAAE;AAAA;AAAA,QAEhDA,GAAE,WAAS,EAAE,wBAAsB,oBAAkB,EAAE;AAAA;AAAA,sBAEzC,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,QAIxBA,GAAE,WAAS,EAAE,QAAM,mCAAiC,EAAE;AAAA;AAAA,QAEtDA,GAAE,WAAS,EAAE,QAAM,0BAA0BA,GAAE,yBAAuBE,GAAE,QAAM,mDAAiD,EAAE,KAAG,EAAE;AAAA,QACtIF,GAAE,WAAS,EAAE,YAAU,wBAAsB,EAAE;AAAA,QAC/CA,GAAE,WAAS,EAAE,QAAM,+BAA6B,EAAE;AAAA;AAAA,GAEvD,GAAEC;AAAC;AAAC,SAAS,EAAEE,IAAE;AAAC,WAASD,GAAEA,IAAE;AAAC,WAAOC,GAAE,SAAO,iBAAiBD,EAAC,iBAAe,eAAeA,EAAC;AAAA,EAAG;AAAC,UAAOC,GAAE,OAAM;AAAA,IAAC,KAAKC,GAAE;AAAA,IAAgB,KAAKA,GAAE;AAAW,aAAOF,GAAE,GAAG;AAAA,IAAE,KAAKE,GAAE;AAAA,IAAiB,KAAKA,GAAE;AAAS,aAAOF,GAAE,GAAG;AAAA,IAAE,KAAKE,GAAE;AAAA,IAAc,KAAKA,GAAE;AAAM,aAAO;AAAA,uBACvPF,GAAE,GAAG,CAAC,cAAcA,GAAE,GAAG,CAAC;AAAA;AAAA,IACzC;AAAQ,aAAM;AAAA,EAAK;AAAC;AAAC,IAAM,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAM,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["a", "x", "T", "o", "e", "a", "t"]}