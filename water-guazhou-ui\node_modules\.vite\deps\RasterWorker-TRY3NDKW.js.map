{"version": 3, "sources": ["../../@arcgis/core/layers/support/RasterWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../geometry.js\";import{isSome as r,unwrap as e}from\"../../core/maybe.js\";import t from\"../../geometry/support/GeographicTransformation.js\";import s from\"./PixelBlock.js\";import{decode as o}from\"./rasterFormats/RasterCodec.js\";import{split as i,mosaic as n,approximateTransform as a,getLocalArithmeticNorthRotations as m}from\"./rasterFunctions/pixelUtils.js\";import{create as l}from\"./rasterFunctions/rasterFunctionHelper.js\";import{requirePE as c,load as f,getProjectionOffsetGrid as p}from\"./rasterFunctions/rasterProjectionHelper.js\";import{estimateStatisticsHistograms as u}from\"./rasterFunctions/stretchUtils.js\";import{convertVectorFieldData as d,convertToLocalDirections as S}from\"./rasterFunctions/vectorFieldUtils.js\";import{readTransform as y}from\"./rasterTransforms/utils.js\";import h from\"../../renderers/support/RasterSymbolizer.js\";import{createFlowMesh as x}from\"../../views/2d/engine/flow/dataUtils.js\";import O from\"../../geometry/Extent.js\";class N{convertVectorFieldData(e){const t=s.fromJSON(e.pixelBlock),o=d(t,e.type);return Promise.resolve(r(o)?o.toJSON():null)}async decode(r){const e=await o(r.data,r.options);return e&&e.toJSON()}symbolize(e){e.pixelBlock=s.fromJSON(e.pixelBlock),e.extent=e.extent?O.fromJSON(e.extent):null;const t=this.symbolizer.symbolize(e);return Promise.resolve(r(t)?t.toJSON():null)}async updateSymbolizer(r){this.symbolizer=h.fromJSON(r.symbolizerJSON),r.histograms&&\"rasterStretch\"===this.symbolizer?.rendererJSON.type&&(this.symbolizer.rendererJSON.histograms=r.histograms)}async updateRasterFunction(r){this.rasterFunction=l(r.rasterFunctionJSON)}async process(e){const t=this.rasterFunction.process({extent:O.fromJSON(e.extent),primaryPixelBlocks:e.primaryPixelBlocks.map((e=>r(e)?s.fromJSON(e):null)),primaryRasterIds:e.primaryRasterIds});return r(t)?t.toJSON():null}stretch(e){const t=this.symbolizer.simpleStretch(s.fromJSON(e.srcPixelBlock),e.stretchParams);return Promise.resolve(r(t)&&t.toJSON())}estimateStatisticsHistograms(r){const e=u(s.fromJSON(r.srcPixelBlock));return Promise.resolve(e)}split(r){const e=i(s.fromJSON(r.srcPixelBlock),r.tileSize,r.maximumPyramidLevel);return e&&e.forEach(((r,t)=>{e.set(t,r?.toJSON())})),Promise.resolve(e)}async mosaicAndTransform(r){const t=r.srcPixelBlocks.map((r=>r?new s(r):null)),o=n(t,r.srcMosaicSize,{blockWidths:r.blockWidths,alignmentInfo:r.alignmentInfo,clipOffset:r.clipOffset,clipSize:r.clipSize});let i,l=o;return r.coefs&&(l=a(o,r.destDimension,r.coefs,r.sampleSpacing,r.interpolation)),r.projectDirections&&r.gcsGrid&&(i=m(r.destDimension,r.gcsGrid),l=e(S(l,r.isUV?\"vector-uv\":\"vector-magdir\",i))),{pixelBlock:l?.toJSON(),localNorthDirections:i}}async createFlowMesh(r,e){const t={data:new Float32Array(r.flowData.buffer),mask:new Uint8Array(r.flowData.maskBuffer),width:r.flowData.width,height:r.flowData.height},{vertexData:s,indexData:o}=await x(r.meshType,r.simulationSettings,t,e.signal);return{result:{vertexBuffer:s.buffer,indexBuffer:o.buffer},transferList:[s.buffer,o.buffer]}}async getProjectionOffsetGrid(r){const e=O.fromJSON(r.projectedExtent),s=O.fromJSON(r.srcBufferExtent);let o=null;r.datumTransformationSteps&&(o=new t({steps:r.datumTransformationSteps})),(r.includeGCSGrid||c(e.spatialReference,s.spatialReference,o))&&await f();const i=r.rasterTransform?y(r.rasterTransform):null;return p({...r,projectedExtent:e,srcBufferExtent:s,datumTransformation:o,rasterTransform:i})}}export{N as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIw8B,IAAM,IAAN,MAAO;AAAA,EAAC,uBAAuBA,IAAE;AAAC,UAAM,IAAE,EAAE,SAASA,GAAE,UAAU,GAAE,IAAE,EAAE,GAAEA,GAAE,IAAI;AAAE,WAAO,QAAQ,QAAQ,EAAE,CAAC,IAAE,EAAE,OAAO,IAAE,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOC,IAAE;AAAC,UAAMD,KAAE,MAAM,EAAEC,GAAE,MAAKA,GAAE,OAAO;AAAE,WAAOD,MAAGA,GAAE,OAAO;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,IAAAA,GAAE,aAAW,EAAE,SAASA,GAAE,UAAU,GAAEA,GAAE,SAAOA,GAAE,SAAO,EAAE,SAASA,GAAE,MAAM,IAAE;AAAK,UAAM,IAAE,KAAK,WAAW,UAAUA,EAAC;AAAE,WAAO,QAAQ,QAAQ,EAAE,CAAC,IAAE,EAAE,OAAO,IAAE,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBC,IAAE;AAJv1C;AAIw1C,SAAK,aAAW,EAAE,SAASA,GAAE,cAAc,GAAEA,GAAE,cAAY,sBAAkB,UAAK,eAAL,mBAAiB,aAAa,UAAO,KAAK,WAAW,aAAa,aAAWA,GAAE;AAAA,EAAW;AAAA,EAAC,MAAM,qBAAqBA,IAAE;AAAC,SAAK,iBAAe,EAAEA,GAAE,kBAAkB;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQD,IAAE;AAAC,UAAM,IAAE,KAAK,eAAe,QAAQ,EAAC,QAAO,EAAE,SAASA,GAAE,MAAM,GAAE,oBAAmBA,GAAE,mBAAmB,IAAK,CAAAA,OAAG,EAAEA,EAAC,IAAE,EAAE,SAASA,EAAC,IAAE,IAAK,GAAE,kBAAiBA,GAAE,iBAAgB,CAAC;AAAE,WAAO,EAAE,CAAC,IAAE,EAAE,OAAO,IAAE;AAAA,EAAI;AAAA,EAAC,QAAQA,IAAE;AAAC,UAAM,IAAE,KAAK,WAAW,cAAc,EAAE,SAASA,GAAE,aAAa,GAAEA,GAAE,aAAa;AAAE,WAAO,QAAQ,QAAQ,EAAE,CAAC,KAAG,EAAE,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BC,IAAE;AAAC,UAAMD,KAAE,EAAE,EAAE,SAASC,GAAE,aAAa,CAAC;AAAE,WAAO,QAAQ,QAAQD,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMC,IAAE;AAAC,UAAMD,KAAE,EAAE,EAAE,SAASC,GAAE,aAAa,GAAEA,GAAE,UAASA,GAAE,mBAAmB;AAAE,WAAOD,MAAGA,GAAE,QAAS,CAACC,IAAE,MAAI;AAAC,MAAAD,GAAE,IAAI,GAAEC,MAAA,gBAAAA,GAAG,QAAQ;AAAA,IAAC,CAAE,GAAE,QAAQ,QAAQD,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBC,IAAE;AAAC,UAAM,IAAEA,GAAE,eAAe,IAAK,CAAAA,OAAGA,KAAE,IAAI,EAAEA,EAAC,IAAE,IAAK,GAAE,IAAE,EAAE,GAAEA,GAAE,eAAc,EAAC,aAAYA,GAAE,aAAY,eAAcA,GAAE,eAAc,YAAWA,GAAE,YAAW,UAASA,GAAE,SAAQ,CAAC;AAAE,QAAIC,IAAE,IAAE;AAAE,WAAOD,GAAE,UAAQ,IAAE,EAAE,GAAEA,GAAE,eAAcA,GAAE,OAAMA,GAAE,eAAcA,GAAE,aAAa,IAAGA,GAAE,qBAAmBA,GAAE,YAAUC,KAAE,EAAED,GAAE,eAAcA,GAAE,OAAO,GAAE,IAAE,EAAEE,GAAE,GAAEF,GAAE,OAAK,cAAY,iBAAgBC,EAAC,CAAC,IAAG,EAAC,YAAW,uBAAG,UAAS,sBAAqBA,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeD,IAAED,IAAE;AAAC,UAAM,IAAE,EAAC,MAAK,IAAI,aAAaC,GAAE,SAAS,MAAM,GAAE,MAAK,IAAI,WAAWA,GAAE,SAAS,UAAU,GAAE,OAAMA,GAAE,SAAS,OAAM,QAAOA,GAAE,SAAS,OAAM,GAAE,EAAC,YAAWG,IAAE,WAAU,EAAC,IAAE,MAAM,EAAEH,GAAE,UAASA,GAAE,oBAAmB,GAAED,GAAE,MAAM;AAAE,WAAM,EAAC,QAAO,EAAC,cAAaI,GAAE,QAAO,aAAY,EAAE,OAAM,GAAE,cAAa,CAACA,GAAE,QAAO,EAAE,MAAM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAwBH,IAAE;AAAC,UAAMD,KAAE,EAAE,SAASC,GAAE,eAAe,GAAEG,KAAE,EAAE,SAASH,GAAE,eAAe;AAAE,QAAI,IAAE;AAAK,IAAAA,GAAE,6BAA2B,IAAE,IAAI,EAAE,EAAC,OAAMA,GAAE,yBAAwB,CAAC,KAAIA,GAAE,kBAAgB,EAAED,GAAE,kBAAiBI,GAAE,kBAAiB,CAAC,MAAI,MAAMC,GAAE;AAAE,UAAMH,KAAED,GAAE,kBAAgB,EAAEA,GAAE,eAAe,IAAE;AAAK,WAAO,EAAE,EAAC,GAAGA,IAAE,iBAAgBD,IAAE,iBAAgBI,IAAE,qBAAoB,GAAE,iBAAgBF,GAAC,CAAC;AAAA,EAAC;AAAC;", "names": ["e", "r", "i", "m", "s", "T"]}