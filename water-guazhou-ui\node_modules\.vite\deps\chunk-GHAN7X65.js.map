{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/vectorTiles/shaders/sources/shaderRepository.js", "../../@arcgis/core/views/2d/engine/vectorTiles/shaders/sources/resolver.js", "../../@arcgis/core/views/2d/engine/webgl/shaders/BitBlitPrograms.js", "../../@arcgis/core/views/2d/engine/webgl/RectangleBinPack.js", "../../@arcgis/core/views/2d/engine/webgl/GlyphMosaic.js", "../../@arcgis/core/views/2d/engine/webgl/GlyphSource.js", "../../@arcgis/core/views/2d/engine/webgl/SDFConverter.js", "../../@arcgis/core/views/2d/engine/webgl/SpriteMosaic.js", "../../@arcgis/core/views/2d/engine/webgl/animatedFormats/utils.js", "../../@arcgis/core/views/2d/engine/webgl/animatedFormats/AnimatableTextureResource.js", "../../@arcgis/core/views/2d/engine/webgl/util/symbolUtils.js", "../../@arcgis/core/views/2d/engine/webgl/TextureManager.js", "../../@arcgis/core/views/2d/engine/webgl/shaders/StencilPrograms.js", "../../@arcgis/core/views/2d/engine/webgl/shaders/BlendPrograms.js", "../../@arcgis/core/views/2d/engine/webgl/effects/BlendEffect.js", "../../@arcgis/core/views/2d/engine/webgl/shaders/HighlightPrograms.js", "../../@arcgis/core/views/2d/engine/webgl/Profiler.js", "../../@arcgis/core/views/2d/engine/webgl/collisions/CollisionGrid.js", "../../@arcgis/core/views/2d/engine/webgl/collisions/visualVariableSimpleUtils.js", "../../@arcgis/core/views/2d/engine/webgl/collisions/CollisionEngine.js", "../../@arcgis/core/views/2d/LabelManager.js", "../../@arcgis/core/views/2d/navigation/ZoomBox.js", "../../@arcgis/core/views/navigation/FilteredFiniteDifference.js", "../../@arcgis/core/views/navigation/Momentum.js", "../../@arcgis/core/views/navigation/PanPlanarMomentumEstimator.js", "../../@arcgis/core/views/2d/navigation/actions/Pan.js", "../../@arcgis/core/views/navigation/MomentumEstimator.js", "../../@arcgis/core/views/navigation/RotationMomentumEstimator.js", "../../@arcgis/core/views/navigation/ZoomMomentumEstimator.js", "../../@arcgis/core/views/2d/navigation/actions/Pinch.js", "../../@arcgis/core/views/2d/navigation/actions/Rotate.js", "../../@arcgis/core/views/2d/navigation/MapViewNavigation.js", "../../@arcgis/core/views/2d/engine/webgl/shaders/MagnifierPrograms.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst e={background:{\"background.frag\":\"#ifdef PATTERN\\nuniform lowp float u_opacity;\\nuniform lowp sampler2D u_texture;\\nvarying mediump vec4 v_tlbr;\\nvarying mediump vec2 v_tileTextureCoord;\\n#else\\nuniform lowp vec4 u_color;\\n#endif\\n#ifdef ID\\nvarying mediump vec4 v_id;\\n#endif\\nvoid main() {\\n#ifdef PATTERN\\nmediump vec2 normalizedTextureCoord = mod(v_tileTextureCoord, 1.0);\\nmediump vec2 samplePos = mix(v_tlbr.xy, v_tlbr.zw, normalizedTextureCoord);\\nlowp vec4 color = texture2D(u_texture, samplePos);\\ngl_FragColor = u_opacity * color;\\n#else\\ngl_FragColor = u_color;\\n#endif\\n#ifdef ID\\nif (gl_FragColor.a < 1.0 / 255.0) {\\ndiscard;\\n}\\ngl_FragColor = v_id;\\n#endif\\n}\",\"background.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\n#ifdef ID\\nuniform mediump vec4 u_id;\\nvarying mediump vec4 v_id;\\n#endif\\nuniform highp mat3 u_dvsMat3;\\nuniform mediump float u_coord_range;\\nuniform mediump float u_depth;\\n#ifdef PATTERN\\nuniform mediump mat3 u_pattern_matrix;\\nvarying mediump vec2 v_tileTextureCoord;\\nuniform mediump vec4 u_tlbr;\\nuniform mediump vec2 u_mosaicSize;\\nvarying mediump vec4 v_tlbr;\\n#endif\\nvoid main() {\\ngl_Position = vec4((u_dvsMat3 * vec3(u_coord_range * a_pos, 1.0)).xy, u_depth, 1.0);\\n#ifdef PATTERN\\nv_tileTextureCoord = (u_pattern_matrix * vec3(a_pos, 1.0)).xy;\\nv_tlbr             = u_tlbr / u_mosaicSize.xyxy;\\n#endif\\n#ifdef ID\\nv_id = u_id / 255.0;\\n#endif\\n}\"},circle:{\"circle.frag\":\"precision lowp float;\\nvarying lowp vec4 v_color;\\nvarying lowp vec4 v_stroke_color;\\nvarying mediump float v_blur;\\nvarying mediump float v_stroke_width;\\nvarying mediump float v_radius;\\nvarying mediump vec2 v_offset;\\n#ifdef ID\\nvarying mediump vec4 v_id;\\n#endif\\nvoid main()\\n{\\nmediump float dist = length(v_offset);\\nmediump float alpha = smoothstep(0.0, -v_blur, dist - 1.0);\\nlowp float color_mix_ratio = v_stroke_width < 0.01 ? 0.0 : smoothstep(-v_blur, 0.0, dist - v_radius / (v_radius + v_stroke_width));\\ngl_FragColor = alpha * mix(v_color, v_stroke_color, color_mix_ratio);\\n#ifdef ID\\nif (gl_FragColor.a < 1.0 / 255.0) {\\ndiscard;\\n}\\ngl_FragColor = v_id;\\n#endif\\n}\",\"circle.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\n#pragma header\\nvarying lowp vec4 v_color;\\nvarying lowp vec4 v_stroke_color;\\nvarying mediump float v_blur;\\nvarying mediump float v_stroke_width;\\nvarying mediump float v_radius;\\nvarying mediump vec2 v_offset;\\n#ifdef ID\\nuniform mediump vec4 u_id;\\nvarying mediump vec4 v_id;\\n#endif\\nuniform highp mat3 u_dvsMat3;\\nuniform highp mat3 u_displayMat3;\\nuniform mediump vec2 u_circleTranslation;\\nuniform mediump float u_depth;\\nuniform mediump float u_antialiasingWidth;\\nvoid main()\\n{\\n#pragma main\\nv_color = color * opacity;\\nv_stroke_color = stroke_color * stroke_opacity;\\nv_stroke_width = stroke_width;\\nv_radius = radius;\\nv_blur = max(blur, u_antialiasingWidth / (radius + stroke_width));\\nmediump vec2 offset = vec2(mod(a_pos, 2.0) * 2.0 - 1.0);\\nv_offset = offset;\\n#ifdef ID\\nv_id = u_id / 255.0;\\n#endif\\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos * 0.5, 1.0) + u_displayMat3 * vec3((v_radius + v_stroke_width) * offset + u_circleTranslation, 0.0);\\ngl_Position = vec4(pos.xy, u_depth, 1.0);\\n}\"},fill:{\"fill.frag\":\"precision lowp float;\\n#ifdef PATTERN\\nuniform lowp sampler2D u_texture;\\nvarying mediump vec2 v_tileTextureCoord;\\nvarying mediump vec4 v_tlbr;\\n#endif\\n#ifdef ID\\nvarying mediump vec4 v_id;\\n#endif\\nvarying lowp vec4 v_color;\\nvec4 mixColors(vec4 color1, vec4 color2) {\\nfloat compositeAlpha = color2.a + color1.a * (1.0 - color2.a);\\nvec3 compositeColor = color2.rgb + color1.rgb * (1.0 - color2.a);\\nreturn vec4(compositeColor, compositeAlpha);\\n}\\nvoid main()\\n{\\n#ifdef PATTERN\\nmediump vec2 normalizedTextureCoord = fract(v_tileTextureCoord);\\nmediump vec2 samplePos = mix(v_tlbr.xy, v_tlbr.zw, normalizedTextureCoord);\\nlowp vec4 color = texture2D(u_texture, samplePos);\\ngl_FragColor = v_color[3] * color;\\n#else\\ngl_FragColor = v_color;\\n#endif\\n#ifdef ID\\nif (gl_FragColor.a < 1.0 / 255.0) {\\ndiscard;\\n}\\ngl_FragColor = v_id;\\n#endif\\n}\",\"fill.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\n#pragma header\\nuniform highp mat3 u_dvsMat3;\\nuniform highp mat3 u_displayMat3;\\nuniform mediump float u_depth;\\nuniform mediump vec2 u_fillTranslation;\\n#ifdef PATTERN\\n#include <util/util.glsl>\\nuniform mediump vec2 u_mosaicSize;\\nuniform mediump float u_patternFactor;\\nvarying mediump vec2 v_tileTextureCoord;\\nvarying mediump vec4 v_tlbr;\\n#endif\\n#ifdef ID\\nuniform mediump vec4 u_id;\\nvarying mediump vec4 v_id;\\n#endif\\nvarying lowp vec4 v_color;\\nvoid main()\\n{\\n#pragma main\\nv_color = color * opacity;\\n#ifdef ID\\nv_id = u_id / 255.0;\\n#endif\\n#ifdef PATTERN\\nfloat patternWidth = nextPOT(tlbr.z - tlbr.x);\\nfloat patternHeight = nextPOT(tlbr.w - tlbr.y);\\nfloat scaleX = 1.0 / (patternWidth * u_patternFactor);\\nfloat scaleY = 1.0 / (patternHeight * u_patternFactor);\\nmat3 patterMat = mat3(scaleX, 0.0,    0.0,\\n0.0,    -scaleY, 0.0,\\n0.0,    0.0,    1.0);\\nv_tileTextureCoord = (patterMat * vec3(a_pos, 1.0)).xy;\\nv_tlbr             = tlbr / u_mosaicSize.xyxy;\\n#endif\\nvec3 pos = u_dvsMat3 * vec3(a_pos, 1.0) + u_displayMat3 * vec3(u_fillTranslation, 0.0);\\ngl_Position = vec4(pos.xy, u_depth, 1.0);\\n}\"},icon:{\"icon.frag\":\"precision mediump float;\\nuniform lowp sampler2D u_texture;\\n#ifdef SDF\\nuniform lowp vec4 u_color;\\nuniform lowp vec4 u_outlineColor;\\n#endif\\nvarying mediump vec2 v_tex;\\nvarying lowp float v_opacity;\\nvarying mediump vec2 v_size;\\nvarying lowp vec4 v_color;\\n#ifdef SDF\\nvarying mediump flaot v_halo_width;\\n#endif\\n#ifdef ID\\nvarying mediump vec4 v_id;\\n#endif\\n#include <util/encoding.glsl>\\nvec4 mixColors(vec4 color1, vec4 color2) {\\nfloat compositeAlpha = color2.a + color1.a * (1.0 - color2.a);\\nvec3 compositeColor = color2.rgb + color1.rgb * (1.0 - color2.a);\\nreturn vec4(compositeColor, compositeAlpha);\\n}\\nvoid main()\\n{\\n#ifdef SDF\\nlowp vec4 fillPixelColor = v_color;\\nfloat d = rgba2float(texture2D(u_texture, v_tex)) - 0.5;\\nconst float softEdgeRatio = 0.248062016;\\nfloat size = max(v_size.x, v_size.y);\\nfloat dist = d * softEdgeRatio * size;\\nfillPixelColor *= clamp(0.5 - dist, 0.0, 1.0);\\nif (v_halo_width > 0.25) {\\nlowp vec4 outlinePixelColor = u_outlineColor;\\nconst float outlineLimitRatio = (16.0 / 86.0);\\nfloat clampedOutlineSize = softEdgeRatio * min(v_halo_width, outlineLimitRatio * max(v_size.x, v_size.y));\\noutlinePixelColor *= clamp(0.5 - (abs(dist) - clampedOutlineSize), 0.0, 1.0);\\ngl_FragColor = v_opacity * mixColors(fillPixelColor, outlinePixelColor);\\n}\\nelse {\\ngl_FragColor = v_opacity * fillPixelColor;\\n}\\n#else\\nlowp vec4 texColor = texture2D(u_texture, v_tex);\\ngl_FragColor = v_opacity * texColor;\\n#endif\\n#ifdef ID\\nif (gl_FragColor.a < 1.0 / 255.0) {\\ndiscard;\\n}\\ngl_FragColor = v_id;\\n#endif\\n}\",\"icon.vert\":\"attribute vec2 a_pos;\\nattribute vec2 a_vertexOffset;\\nattribute vec4 a_texAngleRange;\\nattribute vec4 a_levelInfo;\\nattribute float a_opacityInfo;\\n#pragma header\\n#ifdef ID\\nuniform mediump vec4 u_id;\\nvarying mediump vec4 v_id;\\n#endif\\nvarying lowp vec4 v_color;\\n#ifdef SDF\\nvarying mediump float v_halo_width;\\n#endif\\nuniform highp mat3 u_dvsMat3;\\nuniform highp mat3 u_displayMat3;\\nuniform highp mat3 u_displayViewMat3;\\nuniform mediump vec2 u_iconTranslation;\\nuniform vec2 u_mosaicSize;\\nuniform mediump float u_depth;\\nuniform mediump float u_mapRotation;\\nuniform mediump float u_level;\\nuniform lowp float u_keepUpright;\\nuniform mediump float u_fadeDuration;\\nvarying mediump vec2 v_tex;\\nvarying lowp float v_opacity;\\nvarying mediump vec2 v_size;\\nconst float C_OFFSET_PRECISION = 1.0 / 8.0;\\nconst float C_256_TO_RAD = 3.14159265359 / 128.0;\\nconst float C_DEG_TO_RAD = 3.14159265359 / 180.0;\\nconst float tileCoordRatio = 1.0 / 8.0;\\nuniform highp float u_time;\\nvoid main()\\n{\\n#pragma main\\nv_color = color;\\nv_opacity = opacity;\\n#ifdef SDF\\nv_halo_width = halo_width;\\n#endif\\nfloat modded = mod(a_opacityInfo, 128.0);\\nfloat targetOpacity = (a_opacityInfo - modded) / 128.0;\\nfloat startOpacity = modded / 127.0;\\nfloat interpolatedOpacity = clamp(startOpacity + 2.0 * (targetOpacity - 0.5) * u_time / u_fadeDuration, 0.0, 1.0);\\nv_opacity *= interpolatedOpacity;\\nmediump float a_angle         = a_levelInfo[1];\\nmediump float a_minLevel      = a_levelInfo[2];\\nmediump float a_maxLevel      = a_levelInfo[3];\\nmediump vec2 a_tex            = a_texAngleRange.xy;\\nmediump float delta_z = 0.0;\\nmediump float rotated = mod(a_angle + u_mapRotation, 256.0);\\ndelta_z += (1.0 - step(u_keepUpright, 0.0)) * step(64.0, rotated) * (1.0 - step(192.0, rotated));\\ndelta_z += 1.0 - step(a_minLevel, u_level);\\ndelta_z += step(a_maxLevel, u_level);\\ndelta_z += step(v_opacity, 0.0);\\nvec2 offset = C_OFFSET_PRECISION * a_vertexOffset;\\nv_size = abs(offset);\\n#ifdef SDF\\noffset = (120.0 / 86.0) * offset;\\n#endif\\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos, 1.0) + u_displayViewMat3 * vec3(size * offset, 0.0) + u_displayMat3 * vec3(u_iconTranslation, 0.0);\\ngl_Position = vec4(pos.xy, u_depth + delta_z, 1.0);\\n#ifdef ID\\nv_id = u_id / 255.0;\\n#endif\\nv_tex = a_tex.xy / u_mosaicSize;\\n}\"},line:{\"line.frag\":\"precision lowp float;\\nvarying mediump vec2 v_normal;\\nvarying highp float v_accumulatedDistance;\\nvarying mediump float v_lineHalfWidth;\\nvarying lowp vec4 v_color;\\nvarying mediump float v_blur;\\n#if defined (PATTERN) || defined(SDF)\\nvarying mediump vec4 v_tlbr;\\nvarying mediump vec2 v_patternSize;\\nvarying mediump float v_widthRatio;\\nuniform sampler2D u_texture;\\nuniform mediump float u_antialiasing;\\n#endif\\n#ifdef SDF\\n#include <util/encoding.glsl>\\n#endif\\n#ifdef ID\\nvarying mediump vec4 v_id;\\n#endif\\nvoid main()\\n{\\nmediump float fragDist = length(v_normal) * v_lineHalfWidth;\\nlowp float alpha = clamp((v_lineHalfWidth - fragDist) / v_blur, 0.0, 1.0);\\n#ifdef PATTERN\\nmediump float relativeTexX = fract(v_accumulatedDistance / (v_patternSize.x * v_widthRatio));\\nmediump float relativeTexY = 0.5 + v_normal.y * v_lineHalfWidth / (v_patternSize.y * v_widthRatio);\\nmediump vec2 texCoord = mix(v_tlbr.xy, v_tlbr.zw, vec2(relativeTexX, relativeTexY));\\nlowp vec4 color = texture2D(u_texture, texCoord);\\ngl_FragColor = alpha * v_color[3] * color;\\n#elif defined(SDF)\\nmediump float relativeTexX = fract((v_accumulatedDistance * 0.5) / (v_patternSize.x * v_widthRatio));\\nmediump float relativeTexY =  0.5 + 0.25 * v_normal.y;\\nmediump vec2 texCoord = mix(v_tlbr.xy, v_tlbr.zw, vec2(relativeTexX, relativeTexY));\\nmediump float d = rgba2float(texture2D(u_texture, texCoord)) - 0.5;\\nfloat dist = d * (v_lineHalfWidth + u_antialiasing / 2.0);\\ngl_FragColor = alpha * clamp(0.5 - dist, 0.0, 1.0) * v_color;\\n#else\\ngl_FragColor = alpha * v_color;\\n#endif\\n#ifdef ID\\nif (gl_FragColor.a < 1.0 / 255.0) {\\ndiscard;\\n}\\ngl_FragColor = v_id;\\n#endif\\n}\",\"line.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\nattribute vec4 a_extrude_offset;\\nattribute vec4 a_dir_normal;\\nattribute vec2 a_accumulatedDistance;\\n#pragma header\\nuniform highp mat3 u_dvsMat3;\\nuniform highp mat3 u_displayMat3;\\nuniform highp mat3 u_displayViewMat3;\\nuniform mediump float u_zoomFactor;\\nuniform mediump vec2 u_lineTranslation;\\nuniform mediump float u_antialiasing;\\nuniform mediump float u_depth;\\nvarying mediump vec2 v_normal;\\nvarying highp float v_accumulatedDistance;\\nconst float scale = 1.0 / 31.0;\\nconst mediump float tileCoordRatio = 8.0;\\n#if defined (SDF)\\nconst mediump float sdfPatternHalfWidth = 15.5;\\n#endif\\n#if defined (PATTERN) || defined(SDF)\\nuniform mediump vec2 u_mosaicSize;\\nvarying mediump vec4 v_tlbr;\\nvarying mediump vec2 v_patternSize;\\nvarying mediump float v_widthRatio;\\n#endif\\n#ifdef ID\\nuniform mediump vec4 u_id;\\nvarying mediump vec4 v_id;\\n#endif\\nvarying lowp vec4 v_color;\\nvarying mediump float v_lineHalfWidth;\\nvarying mediump float v_blur;\\nvoid main()\\n{\\n#pragma main\\nv_color = color * opacity;\\nv_blur = blur + u_antialiasing;\\nv_normal = a_dir_normal.zw * scale;\\n#if defined (PATTERN) || defined(SDF)\\nv_tlbr          = tlbr / u_mosaicSize.xyxy;\\nv_patternSize   = vec2(tlbr.z - tlbr.x, tlbr.y - tlbr.w);\\n#if defined (PATTERN)\\nv_widthRatio = width / v_patternSize.y;\\n#else\\nv_widthRatio = width / sdfPatternHalfWidth / 2.0;\\n#endif\\n#endif\\nv_lineHalfWidth = (width + u_antialiasing) * 0.5;\\nmediump vec2 dir = a_dir_normal.xy * scale;\\nmediump vec2 offset_ = a_extrude_offset.zw * scale * offset;\\nmediump vec2 dist = v_lineHalfWidth * scale * a_extrude_offset.xy;\\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos + offset_ * tileCoordRatio / u_zoomFactor, 1.0) + u_displayViewMat3 * vec3(dist, 0.0) + u_displayMat3 * vec3(u_lineTranslation, 0.0);\\ngl_Position = vec4(pos.xy, u_depth, 1.0);\\n#if defined (PATTERN) || defined(SDF)\\nv_accumulatedDistance = a_accumulatedDistance.x * u_zoomFactor / tileCoordRatio + dot(dir, dist + offset_);\\n#endif\\n#ifdef ID\\nv_id = u_id / 255.0;\\n#endif\\n}\"},outline:{\"outline.frag\":\"varying lowp vec4 v_color;\\nvarying mediump vec2 v_normal;\\n#ifdef ID\\nvarying mediump vec4 v_id;\\n#endif\\nvoid main()\\n{\\nlowp float dist = abs(v_normal.y);\\nlowp float alpha = smoothstep(1.0, 0.0, dist);\\ngl_FragColor = alpha * v_color;\\n#ifdef ID\\nif (gl_FragColor.a < 1.0 / 255.0) {\\ndiscard;\\n}\\ngl_FragColor = v_id;\\n#endif\\n}\",\"outline.vert\":\"attribute vec2 a_pos;\\nattribute vec2 a_offset;\\nattribute vec2 a_xnormal;\\n#pragma header\\nvarying lowp vec4 v_color;\\n#ifdef ID\\nuniform mediump vec4 u_id;\\nvarying mediump vec4 v_id;\\n#endif\\nuniform highp mat3 u_dvsMat3;\\nuniform highp mat3 u_displayMat3;\\nuniform mediump vec2 u_fillTranslation;\\nuniform mediump float u_depth;\\nuniform mediump float u_outline_width;\\nvarying lowp vec2 v_normal;\\nconst float scale = 1.0 / 15.0;\\nvoid main()\\n{\\n#pragma main\\nv_color = color * opacity;\\n#ifdef ID\\nv_id = u_id / 255.0;\\n#endif\\nv_normal = a_xnormal;\\nmediump vec2 dist = u_outline_width * scale * a_offset;\\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos, 1.0) + u_displayMat3 * vec3(dist + u_fillTranslation, 0.0);\\ngl_Position = vec4(pos.xy, u_depth, 1.0);\\n}\"},text:{\"text.frag\":\"uniform lowp sampler2D u_texture;\\nvarying lowp vec2 v_tex;\\nvarying lowp vec4 v_color;\\nvarying mediump float v_edgeWidth;\\nvarying mediump float v_edgeDistance;\\n#ifdef ID\\nvarying mediump vec4 v_id;\\n#endif\\nvoid main()\\n{\\nlowp float dist = texture2D(u_texture, v_tex).a;\\nmediump float alpha = smoothstep(v_edgeDistance - v_edgeWidth, v_edgeDistance + v_edgeWidth, dist);\\ngl_FragColor = alpha * v_color;\\n#ifdef ID\\nif (gl_FragColor.a < 1.0 / 255.0) {\\ndiscard;\\n}\\ngl_FragColor = v_id;\\n#endif\\n}\",\"text.vert\":\"attribute vec2 a_pos;\\nattribute vec2 a_vertexOffset;\\nattribute vec4 a_texAngleRange;\\nattribute vec4 a_levelInfo;\\nattribute float a_opacityInfo;\\n#pragma header\\nvarying lowp vec4 v_color;\\n#ifdef ID\\nuniform mediump vec4 u_id;\\nvarying mediump vec4 v_id;\\n#endif\\nuniform highp mat3 u_dvsMat3;\\nuniform highp mat3 u_displayMat3;\\nuniform highp mat3 u_displayViewMat3;\\nuniform mediump vec2 u_textTranslation;\\nuniform vec2 u_mosaicSize;\\nuniform mediump float u_depth;\\nuniform mediump float u_mapRotation;\\nuniform mediump float u_level;\\nuniform lowp float u_keepUpright;\\nuniform mediump float u_fadeDuration;\\nvarying lowp vec2 v_tex;\\nconst float offsetPrecision = 1.0 / 8.0;\\nconst mediump float edgePos = 0.75;\\nuniform mediump float u_antialiasingWidth;\\nvarying mediump float v_edgeDistance;\\nvarying mediump float v_edgeWidth;\\nuniform lowp float u_halo;\\nconst float sdfFontScale = 1.0 / 24.0;\\nconst float sdfPixel = 3.0;\\nuniform highp float u_time;\\nvoid main()\\n{\\n#pragma main\\nif (u_halo > 0.5)\\n{\\nv_color = halo_color * opacity;\\nhalo_width *= sdfPixel;\\nhalo_blur *= sdfPixel;\\n}\\nelse\\n{\\nv_color = color * opacity;\\nhalo_width = 0.0;\\nhalo_blur = 0.0;\\n}\\nfloat modded = mod(a_opacityInfo, 128.0);\\nfloat targetOpacity = (a_opacityInfo - modded) / 128.0;\\nfloat startOpacity = modded / 127.0;\\nfloat interpolatedOpacity = clamp(startOpacity + 2.0 * (targetOpacity - 0.5) * u_time / u_fadeDuration, 0.0, 1.0);\\nv_color *= interpolatedOpacity;\\nmediump float a_angle       = a_levelInfo[1];\\nmediump float a_minLevel    = a_levelInfo[2];\\nmediump float a_maxLevel    = a_levelInfo[3];\\nmediump vec2 a_tex          = a_texAngleRange.xy;\\nmediump float a_visMinAngle    = a_texAngleRange.z;\\nmediump float a_visMaxAngle    = a_texAngleRange.w;\\nmediump float delta_z = 0.0;\\nmediump float angle = mod(a_angle + u_mapRotation, 256.0);\\nif (a_visMinAngle < a_visMaxAngle)\\n{\\ndelta_z += (1.0 - step(u_keepUpright, 0.0)) * (step(a_visMaxAngle, angle) + (1.0 - step(a_visMinAngle, angle)));\\n}\\nelse\\n{\\ndelta_z += (1.0 - step(u_keepUpright, 0.0)) * (step(a_visMaxAngle, angle) * (1.0 - step(a_visMinAngle, angle)));\\n}\\ndelta_z += 1.0 - step(a_minLevel, u_level);\\ndelta_z += step(a_maxLevel, u_level);\\ndelta_z += step(v_color[3], 0.0);\\nv_tex = a_tex.xy / u_mosaicSize;\\n#ifdef ID\\nv_id = u_id / 255.0;\\n#endif\\nv_edgeDistance = edgePos - halo_width / size;\\nv_edgeWidth = (u_antialiasingWidth + halo_blur) / size;\\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos, 1.0) + sdfFontScale * u_displayViewMat3 * vec3(offsetPrecision * size * a_vertexOffset, 0.0) + u_displayMat3 * vec3(u_textTranslation, 0.0);\\ngl_Position = vec4(pos.xy, u_depth + delta_z, 1.0);\\n}\"},util:{\"encoding.glsl\":\"const vec4 rgba2float_factors = vec4(\\n255.0 / (256.0),\\n255.0 / (256.0 * 256.0),\\n255.0 / (256.0 * 256.0 * 256.0),\\n255.0 / (256.0 * 256.0 * 256.0 * 256.0)\\n);\\nfloat rgba2float(vec4 rgba) {\\nreturn dot(rgba, rgba2float_factors);\\n}\",\"util.glsl\":\"float nextPOT(in float x) {\\nreturn pow(2.0, ceil(log2(abs(x))));\\n}\"}};export{e as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"./shaderRepository.js\";import{ShaderCompiler as e}from\"../../../../../webgl/ShaderCompiler.js\";function o(e){let o=r;return e.split(\"/\").forEach((r=>{o&&(o=o[r])})),o}const t=new e(o);function n(r){return t.resolveIncludes(r)}export{n as resolveIncludes};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{resolveIncludes as t}from\"./sources/resolver.js\";const e={shaders:{vertexShader:t(\"bitBlit/bitBlit.vert\"),fragmentShader:t(\"bitBlit/bitBlit.frag\")},attributes:new Map([[\"a_pos\",0],[\"a_tex\",1]])};export{e as bitBlit};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport h from\"./Rect.js\";class t{constructor(t,e){this._width=0,this._height=0,this._free=[],this._width=t,this._height=e,this._free.push(new h(0,0,t,e))}get width(){return this._width}get height(){return this._height}allocate(t,e){if(t>this._width||e>this._height)return new h;let i=null,s=-1;for(let h=0;h<this._free.length;++h){const r=this._free[h];t<=r.width&&e<=r.height&&(null===i||r.y<=i.y&&r.x<=i.x)&&(i=r,s=h)}return null===i?new h:(this._free.splice(s,1),i.width<i.height?(i.width>t&&this._free.push(new h(i.x+t,i.y,i.width-t,e)),i.height>e&&this._free.push(new h(i.x,i.y+e,i.width,i.height-e))):(i.width>t&&this._free.push(new h(i.x+t,i.y,i.width-t,i.height)),i.height>e&&this._free.push(new h(i.x,i.y+e,t,i.height-e))),new h(i.x,i.y,t,e))}release(h){for(let t=0;t<this._free.length;++t){const e=this._free[t];if(e.y===h.y&&e.height===h.height&&e.x+e.width===h.x)e.width+=h.width;else if(e.x===h.x&&e.width===h.width&&e.y+e.height===h.y)e.height+=h.height;else if(h.y===e.y&&h.height===e.height&&h.x+h.width===e.x)e.x=h.x,e.width+=h.width;else{if(h.x!==e.x||h.width!==e.width||h.y+h.height!==e.y)continue;e.y=h.y,e.height+=h.height}this._free.splice(t,1),this.release(h)}this._free.push(h)}}export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../core/has.js\";import{throwIfNotAbortError as t}from\"../../../../core/promiseUtils.js\";import e from\"./Rect.js\";import i from\"./RectangleBinPack.js\";import{PixelFormat as h,PixelType as s}from\"../../../webgl/enums.js\";import{Texture as r}from\"../../../webgl/Texture.js\";const a=256,n=t=>Math.floor(t/256);function c(t){const e=new Set;for(const i of t)e.add(n(i));return e}function o(e,i,h){return e.has(i)||e.set(i,h().then((()=>{e.delete(i)})).catch((h=>{e.delete(i),t(h)}))),e.get(i)}const l=t=>({rect:new e(0,0,0,0),page:0,metrics:{left:0,width:0,height:0,advance:0,top:0},code:t,sdf:!0});class g{constructor(t,e,h){this.width=0,this.height=0,this._dirties=[],this._glyphData=[],this._currentPage=0,this._glyphCache={},this._textures=[],this._rangePromises=new Map,this.width=t,this.height=e,this._glyphSource=h,this._binPack=new i(t-4,e-4),this._glyphData.push(new Uint8Array(t*e)),this._dirties.push(!0),this._textures.push(null),this._initDecorationGlyph()}dispose(){this._binPack=null;for(const t of this._textures)t&&t.dispose();this._textures.length=0,this._glyphData.length=0}_initDecorationGlyph(){const t=[117,149,181,207,207,181,149,117],e=[];for(let h=0;h<t.length;h++){const i=t[h];for(let t=0;t<11;t++)e.push(i)}const i={metrics:{width:5,height:2,left:0,top:0,advance:0},bitmap:new Uint8Array(e)};this._recordGlyph(i)}async getGlyphItems(t,e,i){const h=this._getGlyphCache(t);return await this._fetchRanges(t,e,i),e.map((e=>this._getMosaicItem(h,t,e)))}bind(t,e,i,h){const s=this._getTexture(t,i);s.setSamplingMode(e),this._dirties[i]&&(s.setData(this._glyphData[i]),this._dirties[i]=!1),t.bindTexture(s,h)}_getGlyphCache(t){return this._glyphCache[t]||(this._glyphCache[t]={}),this._glyphCache[t]}_getTexture(t,e){return this._textures[e]||(this._textures[e]=new r(t,{pixelFormat:h.ALPHA,dataType:s.UNSIGNED_BYTE,width:this.width,height:this.height},new Uint8Array(this.width*this.height))),this._textures[e]}_invalidate(){this._dirties[this._currentPage]=!0}async _fetchRanges(t,e,i){const h=c(e),s=[];h.forEach((e=>{s.push(this._fetchRange(t,e,i))})),await Promise.all(s)}async _fetchRange(t,e,i){if(e>a)return;const h=t+e;return o(this._rangePromises,h,(()=>this._glyphSource.getRange(t,e,i)))}_getMosaicItem(t,e,i){if(!t[i]){const h=this._glyphSource.getGlyph(e,i);if(!h||!h.metrics)return l(i);const s=this._recordGlyph(h),r=this._currentPage,a=h.metrics;t[i]={rect:s,page:r,metrics:a,code:i,sdf:!0},this._invalidate()}return t[i]}_recordGlyph(t){const h=t.metrics;let s;if(0===h.width)s=new e(0,0,0,0);else{const e=3,r=h.width+2*e,a=h.height+2*e;s=this._binPack.allocate(r,a),s.isEmpty&&(this._dirties[this._currentPage]||(this._glyphData[this._currentPage]=null),this._currentPage=this._glyphData.length,this._glyphData.push(new Uint8Array(this.width*this.height)),this._dirties.push(!0),this._textures.push(null),this._initDecorationGlyph(),this._binPack=new i(this.width-4,this.height-4),s=this._binPack.allocate(r,a));const n=this._glyphData[this._currentPage],c=t.bitmap;let o,l;if(c)for(let t=0;t<a;t++){o=r*t,l=this.width*(s.y+t)+s.x;for(let t=0;t<r;t++)n[l+t]=c[o+t]}has(\"esri-glyph-debug\")&&this._showDebugPage(n)}return s}_showDebugPage(t){const e=document.createElement(\"canvas\"),i=e.getContext(\"2d\"),h=new ImageData(this.width,this.height),s=h.data;e.width=this.width,e.height=this.height,e.style.border=\"1px solid black\";for(let r=0;r<t.length;++r)s[4*r+0]=t[r],s[4*r+1]=0,s[4*r+2]=0,s[4*r+3]=255;i.putImageData(h,0,0),document.body.appendChild(e)}}export{g as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../request.js\";import e from\"../../../../core/pbf.js\";class s{constructor(t){for(this._metrics=[],this._bitmaps=[];t.next();)switch(t.tag()){case 1:{const e=t.getMessage();for(;e.next();)switch(e.tag()){case 3:{const t=e.getMessage();let s,a,r,n,i,c,g;for(;t.next();)switch(t.tag()){case 1:s=t.getUInt32();break;case 2:a=t.getBytes();break;case 3:r=t.getUInt32();break;case 4:n=t.getUInt32();break;case 5:i=t.getSInt32();break;case 6:c=t.getSInt32();break;case 7:g=t.getUInt32();break;default:t.skip()}t.release(),s&&(this._metrics[s]={width:r,height:n,left:i,top:c,advance:g},this._bitmaps[s]=a);break}default:e.skip()}e.release();break}default:t.skip()}}getMetrics(t){return this._metrics[t]}getBitmap(t){return this._bitmaps[t]}}class a{constructor(){this._ranges=[]}getRange(t){return this._ranges[t]}addRange(t,e){this._ranges[t]=e}}class r{constructor(t){this._glyphInfo={},this._baseURL=t}getRange(a,r,n){const i=this._getFontStack(a);if(i.getRange(r))return Promise.resolve();const c=256*r,g=c+255,o=this._baseURL.replace(\"{fontstack}\",a).replace(\"{range}\",c+\"-\"+g);return t(o,{responseType:\"array-buffer\",...n}).then((t=>{i.addRange(r,new s(new e(new Uint8Array(t.data),new DataView(t.data))))}))}getGlyph(t,e){const s=this._getFontStack(t);if(!s)return;const a=Math.floor(e/256);if(a>256)return;const r=s.getRange(a);return r?{metrics:r.getMetrics(e),bitmap:r.getBitmap(e)}:void 0}_getFontStack(t){let e=this._glyphInfo[t];return e||(e=this._glyphInfo[t]=new a),e}}export{r as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{packFloatRGBA as t}from\"../../../../core/floatRGBA.js\";import{onAbort as e,createAbortError as s}from\"../../../../core/promiseUtils.js\";const i=1e20;class r{constructor(t){this._svg=null,this.size=t;const e=document.createElement(\"canvas\");e.width=e.height=t,this._context=e.getContext(\"2d\"),this._gridOuter=new Float64Array(t*t),this._gridInner=new Float64Array(t*t),this._f=new Float64Array(t),this._d=new Float64Array(t),this._z=new Float64Array(t+1),this._v=new Int16Array(t)}dispose(){this._context=this._gridOuter=this._gridInner=this._f=this._d=this._z=this._v=null,this._svg&&(document.body.removeChild(this._svg),this._svg=null)}draw(r,h,n=31){this._initSVG();const o=this.createSVGString(r);return new Promise(((r,a)=>{const d=new Image;d.src=\"data:image/svg+xml; charset=utf8, \"+encodeURIComponent(o),d.onload=()=>{d.onload=null,this._context.clearRect(0,0,this.size,this.size),this._context.drawImage(d,0,0,this.size,this.size);const e=this._context.getImageData(0,0,this.size,this.size),s=new Uint8Array(this.size*this.size*4);for(let t=0;t<this.size*this.size;t++){const s=e.data[4*t+3]/255;this._gridOuter[t]=1===s?0:0===s?i:Math.max(0,.5-s)**2,this._gridInner[t]=1===s?i:0===s?0:Math.max(0,s-.5)**2}this._edt(this._gridOuter,this.size,this.size),this._edt(this._gridInner,this.size,this.size);for(let i=0;i<this.size*this.size;i++){const e=this._gridOuter[i]-this._gridInner[i];t(.5-e/(2*n),s,4*i)}r(s)};const l=h&&h.signal;l&&e(l,(()=>a(s())))}))}_initSVG(){if(!this._svg){const t=document.createElementNS(\"http://www.w3.org/2000/svg\",\"svg\");t.setAttribute(\"style\",\"position: absolute;\"),t.setAttribute(\"width\",\"0\"),t.setAttribute(\"height\",\"0\"),t.setAttribute(\"aria-hidden\",\"true\"),t.setAttribute(\"role\",\"presentation\"),document.body.appendChild(t),this._svg=t}return this._svg}createSVGString(t){const e=this._initSVG(),s=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\");s.setAttribute(\"d\",t),e.appendChild(s);const i=s.getBBox(),r=i.width/i.height,h=this.size/2;let n,o,a,d;if(r>1){o=n=h/i.width;const t=h*(1/r);a=this.size/4,d=h-t/2}else{n=o=h/i.height;a=h-h*r/2,d=this.size/4}const l=-i.x*n+a,_=-i.y*o+d;s.setAttribute(\"style\",`transform: matrix(${n}, 0, 0, ${o}, ${l}, ${_})`);const g=`<svg style=\"fill:red;\" height=\"${this.size}\" width=\"${this.size}\" xmlns=\"http://www.w3.org/2000/svg\">${e.innerHTML}</svg>`;return e.removeChild(s),g}_edt(t,e,s){const i=this._f,r=this._d,h=this._v,n=this._z;for(let o=0;o<e;o++){for(let r=0;r<s;r++)i[r]=t[r*e+o];this._edt1d(i,r,h,n,s);for(let i=0;i<s;i++)t[i*e+o]=r[i]}for(let o=0;o<s;o++){for(let s=0;s<e;s++)i[s]=t[o*e+s];this._edt1d(i,r,h,n,e);for(let s=0;s<e;s++)t[o*e+s]=Math.sqrt(r[s])}}_edt1d(t,e,s,r,h){s[0]=0,r[0]=-i,r[1]=+i;for(let n=1,o=0;n<h;n++){let e=(t[n]+n*n-(t[s[o]]+s[o]*s[o]))/(2*n-2*s[o]);for(;e<=r[o];)o--,e=(t[n]+n*n-(t[s[o]]+s[o]*s[o]))/(2*n-2*s[o]);o++,s[o]=n,r[o]=e,r[o+1]=+i}for(let i=0,n=0;i<h;i++){for(;r[n+1]<i;)n++;e[i]=(i-s[n])*(i-s[n])+t[s[n]]}}}export{r as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../core/Error.js\";import{SPRITE_PADDING as e}from\"./definitions.js\";import{log2 as i}from\"./GeometryUtils.js\";import s from\"./Rect.js\";import a from\"./RectangleBinPack.js\";import{PixelFormat as r,PixelType as o}from\"../../../webgl/enums.js\";import{Texture as h}from\"../../../webgl/Texture.js\";function c(t){return t&&\"static\"===t.type}class n{constructor(t,e,i=0){this._mosaicPages=[],this._maxItemSize=0,this._currentPage=0,this._pageWidth=0,this._pageHeight=0,this._mosaicRects=new Map,this._spriteCopyQueue=[],this.pixelRatio=1,(t<=0||e<=0)&&console.error(\"Sprites mosaic defaultWidth and defaultHeight must be greater than zero!\"),this._pageWidth=t,this._pageHeight=e,i>0&&(this._maxItemSize=i),this.pixelRatio=window.devicePixelRatio||1,this._binPack=new a(this._pageWidth,this._pageHeight);const s=Math.floor(this._pageWidth),r=Math.floor(this._pageHeight);this._mosaicPages.push({mosaicsData:{type:\"static\",data:new Uint32Array(s*r)},size:[this._pageWidth,this._pageHeight],dirty:!0,texture:void 0})}getWidth(t){return t>=this._mosaicPages.length?-1:this._mosaicPages[t].size[0]}getHeight(t){return t>=this._mosaicPages.length?-1:this._mosaicPages[t].size[1]}getPageTexture(t){return t<this._mosaicPages.length?this._mosaicPages[t].texture:null}has(t){return this._mosaicRects.has(t)}get itemCount(){return this._mosaicRects.size}getSpriteItem(t){return this._mosaicRects.get(t)}addSpriteItem(t,i,a,r,o,h,n=1){if(this._mosaicRects.has(t))return this._mosaicRects.get(t);let g,p,m;if(c(a))[g,p,m]=this._allocateImage(i[0],i[1]);else{g=new s(0,0,i[0],i[1]),p=this._mosaicPages.length;const t=void 0;this._mosaicPages.push({mosaicsData:a,size:[i[0]+2*e,i[1]+2*e],dirty:!0,texture:t})}if(g.width<=0||g.height<=0)return null;const _={rect:g,width:i[0],height:i[1],sdf:o,simplePattern:h,pixelRatio:n,page:p};return this._mosaicRects.set(t,_),c(a)&&this._copy({rect:g,spriteSize:i,spriteData:a.data,page:p,pageSize:m,repeat:r,sdf:o}),_}hasItemsToProcess(){return 0!==this._spriteCopyQueue.length}processNextItem(){const t=this._spriteCopyQueue.pop();t&&this._copy(t)}getSpriteItems(t){const e={};for(const i of t)e[i]=this.getSpriteItem(i);return e}getMosaicItemPosition(t){const i=this.getSpriteItem(t),s=i&&i.rect;if(!s)return null;s.width=i.width,s.height=i.height;const a=i.width,r=i.height,o=e,h=this._mosaicPages[i.page];return{size:[i.width,i.height],tl:[(s.x+o)/h[0],(s.y+o)/h[1]],br:[(s.x+o+a)/h[0],(s.y+o+r)/h[1]],page:i.page}}bind(t,e,i=0,s=0){const a=this._mosaicPages[i],r=a.mosaicsData;let o=a.texture;if(o||(o=g(t,a.size),a.texture=o),o.setSamplingMode(e),c(r))t.bindTexture(o,s),a.dirty&&(o.setData(new Uint8Array(r.data.buffer)),o.generateMipmap());else{r.data.bindFrame(t,o,s),o.generateMipmap()}a.dirty=!1}static _copyBits(t,e,i,s,a,r,o,h,c,n,g){let p=s*e+i,m=h*r+o;if(g){m-=r;for(let o=-1;o<=n;o++,p=((o+n)%n+s)*e+i,m+=r)for(let e=-1;e<=c;e++)a[m+e]=t[p+(e+c)%c]}else for(let _=0;_<n;_++){for(let e=0;e<c;e++)a[m+e]=t[p+e];p+=e,m+=r}}_copy(i){if(i.page>=this._mosaicPages.length)return;const s=this._mosaicPages[i.page],a=s.mosaicsData;if(!c(s.mosaicsData))throw new t(\"mapview-invalid-resource\",\"unsuitable data type!\");const r=i.spriteData,o=a.data;o&&r||console.error(\"Source or target images are uninitialized!\"),n._copyBits(r,i.spriteSize[0],0,0,o,i.pageSize[0],i.rect.x+e,i.rect.y+e,i.spriteSize[0],i.spriteSize[1],i.repeat),s.dirty=!0}_allocateImage(t,r){t+=2*e,r+=2*e;const o=Math.max(t,r);if(this._maxItemSize&&this._maxItemSize<o){const e=2**Math.ceil(i(t)),a=2**Math.ceil(i(r)),o=new s(0,0,t,r);return this._mosaicPages.push({mosaicsData:{type:\"static\",data:new Uint32Array(e*a)},size:[e,a],dirty:!0,texture:void 0}),[o,this._mosaicPages.length-1,[e,a]]}const h=this._binPack.allocate(t,r);if(h.width<=0){const e=this._mosaicPages[this._currentPage];return!e.dirty&&c(e.mosaicsData)&&(e.mosaicsData.data=null),this._currentPage=this._mosaicPages.length,this._mosaicPages.push({mosaicsData:{type:\"static\",data:new Uint32Array(this._pageWidth*this._pageHeight)},size:[this._pageWidth,this._pageHeight],dirty:!0,texture:void 0}),this._binPack=new a(this._pageWidth,this._pageHeight),this._allocateImage(t,r)}return[h,this._currentPage,[this._pageWidth,this._pageHeight]]}dispose(){this._binPack=null;for(const t of this._mosaicPages){const e=t.texture;e&&e.dispose();const i=t.mosaicsData;if(!c(i)){i.data.destroy()}}this._mosaicPages=null,this._mosaicRects.clear()}}function g(t,e){return new h(t,{pixelFormat:r.RGBA,dataType:o.UNSIGNED_BYTE,width:e[0],height:e[1]},null)}export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Milliseconds as t}from\"../../../../../core/time.js\";import{imageTypeToCanvas as e}from\"../../../../../geometry/support/meshUtils/exporters/gltf/imageutils.js\";import{AnimatedSymbolRepeatType as r}from\"../../../../../symbols/cim/enums.js\";import{getMaterialGroup as i,getRandomValue as a}from\"../grouping.js\";function n(e){return t(e.frameDurations.reduce(((t,e)=>t+e),0))}function s(t){const{width:e,height:r}=t;return{frameDurations:t.frameDurations.reverse(),getFrame:e=>{const r=t.frameDurations.length-1-e;return t.getFrame(r)},width:e,height:r}}function o(e,r){const{width:i,height:a,getFrame:s}=e,o=r/n(e);return{frameDurations:e.frameDurations.map((e=>t(e*o))),getFrame:s,width:i,height:a}}function m(e,r){const{width:i,height:a,getFrame:n}=e,s=e.frameDurations.slice(),o=s.shift();return s.unshift(t(o+r)),{frameDurations:s,getFrame:n,width:i,height:a}}function h(e,r){const{width:i,height:a,getFrame:n}=e,s=e.frameDurations.slice(),o=s.pop();return s.push(t(o+r)),{frameDurations:s,getFrame:n,width:i,height:a}}class c{constructor(t,e,r,i){this._animation=t,this._repeatType=r,this._onFrameData=i,this._direction=1,this._currentFrame=0,this.timeToFrame=this._animation.frameDurations[this._currentFrame];let a=0;for(;e>a;)a+=this.timeToFrame,this.nextFrame();const n=this._animation.getFrame(this._currentFrame);this._onFrameData(n)}nextFrame(){if(this._currentFrame+=this._direction,this._direction>0){if(this._currentFrame===this._animation.frameDurations.length)switch(this._repeatType){case r.None:this._currentFrame-=this._direction;break;case r.Loop:this._currentFrame=0;break;case r.Oscillate:this._currentFrame-=this._direction,this._direction=-1}}else if(-1===this._currentFrame)switch(this._repeatType){case r.None:this._currentFrame-=this._direction;break;case r.Loop:this._currentFrame=this._animation.frameDurations.length-1;break;case r.Oscillate:this._currentFrame-=this._direction,this._direction=1}this.timeToFrame=this._animation.frameDurations[this._currentFrame];const t=this._animation.getFrame(this._currentFrame);this._onFrameData(t)}}function u(e,u,f,l){let g,{repeatType:F}=u;if(null==F&&(F=r.Loop),!0===u.reverseAnimation&&(e=s(e)),null!=u.duration&&(e=o(e,t(1e3*u.duration))),null!=u.repeatDelay){const i=1e3*u.repeatDelay;F===r.Loop?e=h(e,t(i)):F===r.Oscillate&&(e=m(h(e,t(i/2)),t(i/2)))}if(null!=u.startTimeOffset)g=t(1e3*u.startTimeOffset);else if(null!=u.randomizeStartTime){const r=i(f),s=82749913,o=null!=u.randomizeStartSeed?u.randomizeStartSeed:s,m=a(r,o);g=t(m*n(e))}else g=t(0);return new c(e,g,F,l)}function f(t,e,r,i){const a=null==e.playAnimation||e.playAnimation,n=u(t,e,r,i);let s,o=n.timeToFrame;function m(){s=a?setTimeout((()=>{n.nextFrame(),o=n.timeToFrame,m()}),o):void 0}return m(),{remove:()=>{a&&clearTimeout(s)}}}const l=document.createElement(\"canvas\"),g=l.getContext(\"2d\");function F(t,r,i){l.width=r,l.height=i;const a=[],n=t.frameDurations.length;for(let s=0;s<n;s++){const n=t.getFrame(s);g.clearRect(0,0,r,i),n instanceof ImageData?g.drawImage(e(n),0,0,r,i):g.drawImage(n,0,0,r,i),a.push(g.getImageData(0,0,r,i))}return{width:r,height:i,frameDurations:t.frameDurations,getFrame:t=>a[t]}}export{c as Player,o as adjustDuration,h as appendDelay,u as createPlayer,n as getDuration,f as play,m as prependDelay,F as resize,s as reverse};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t}from\"../../../../../core/maybe.js\";import{SPRITE_PADDING as a}from\"../definitions.js\";import{play as i}from\"./utils.js\";class e{constructor(t,a,e,s){this._animation=t,this._frameData=null;const h=t=>{this._frameData=t,a.requestRender()};this.frameCount=this._animation.frameDurations.length,this.width=this._animation.width,this.height=this._animation.height,this._playHandle=i(this._animation,e,s,h)}destroy(){this._playHandle.remove()}bindFrame(i,e,s){i.bindTexture(e,s),t(this._frameData)||(e.updateData(0,a,a,this._frameData.width,this._frameData.height,this._frameData),this._frameData=null)}}export{e as AnimatableTextureResource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction e(e){switch(e.type){case\"esriSMS\":return`${e.style}.${e.path}`;case\"esriSLS\":return`${e.style}.${e.cap}`;case\"esriSFS\":return`${e.style}`;case\"esriPFS\":case\"esriPMS\":return e.imageData?`${e.imageData}${e.width}${e.height}`:`${e.url}${e.width}${e.height}`;default:return\"mosaicHash\"in e?e.mosaicHash:JSON.stringify(e)}}export{e as keyFromSymbol};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../config.js\";import t from\"../../../../request.js\";import{bidiText as i}from\"../../../../core/BidiText.js\";import s from\"../../../../core/Error.js\";import{getFullyQualifiedFontName as r}from\"../../../../core/fontUtils.js\";import o from\"../../../../core/Logger.js\";import{nextPowerOfTwo as n}from\"../../../../core/mathUtils.js\";import{isNone as a,isSome as h}from\"../../../../core/maybe.js\";import{throwIfAborted as c,isAbortError as u}from\"../../../../core/promiseUtils.js\";import{pt2px as d}from\"../../../../core/screenUtils.js\";import{s as m}from\"../../../../chunks/vec2.js\";import{c as l}from\"../../../../chunks/vec2f32.js\";import g from\"../../../../symbols/cim/Rasterizer.js\";import{TEXTURE_BINDING_GLYPH_ATLAS as p,TEXTURE_BINDING_SPRITE_ATLAS as f,SPRITE_PADDING as _,PATTERN_FILL_RASTERIZATION_SCALE as w}from\"./definitions.js\";import{MosaicType as y}from\"./enums.js\";import M from\"./GlyphMosaic.js\";import I from\"./GlyphSource.js\";import z from\"./SDFConverter.js\";import T from\"./SpriteMosaic.js\";import{is3D as R,charCodes as v,isSimple as j,isSVGResource as x,isImageResource as S,isMarkerPlacementInsidePolygon as b,shouldRepeat as P,getUrl as U,getPMSResourceSize as B,isGIF as G,isPNG as q,isSVGImage as A}from\"./Utils.js\";import{AnimatableTextureResource as C}from\"./animatedFormats/AnimatableTextureResource.js\";import{resize as E}from\"./animatedFormats/utils.js\";import{ok as F}from\"./util/Result.js\";import{keyFromSymbol as L}from\"./util/symbolUtils.js\";import{QueueProcessor as N}from\"../../../support/QueueProcessor.js\";import{TextureSamplingMode as $}from\"../../../webgl/enums.js\";const k=l(),H=\"arial-unicode-ms-regular\",O=126,Q=o.getLogger(\"esri.views.2d.engine.webgl.TextureManager\");function V(e,t){const i=Math.round(d(t)*window.devicePixelRatio),s=i>=128?2:4;return Math.min(e,i*s)}const Y=(e,t,i)=>Q.error(new s(e,t,i));class D{static fromMosaic(e,t){return new D(e,t.page,t.sdf)}constructor(e,t,i){this.mosaicType=e,this.page=t,this.sdf=i}}class J{constructor(i,r,o){this._requestRender=i,this.resourceManager=r,this._allowNonPowerOfTwo=o,this._invalidFontsMap=new Map,this._sdfConverter=new z(O),this._bindingInfos=new Array,this._hashToBindingIndex=new Map,this._ongoingRasterizations=new Map,this._imageRequestQueue=new N({concurrency:10,process:async(e,i)=>{c(i);try{return await t(e,{responseType:\"image\",signal:i})}catch(r){if(!u(r))throw new s(\"mapview-invalid-resource\",`Could not fetch requested resource at ${e}`,r);throw r}}}),this._spriteMosaic=new T(2048,2048,500),this._glyphSource=new I(`${e.fontsUrl}/{fontstack}/{range}.pbf`),this._glyphMosaic=new M(1024,1024,this._glyphSource),this._rasterizer=new g(r)}dispose(){this._spriteMosaic.dispose(),this._glyphMosaic.dispose(),this._rasterizer.dispose(),this._sdfConverter.dispose(),this._spriteMosaic=null,this._glyphMosaic=null,this._sdfConverter=null,this._hashToBindingIndex.clear(),this._hashToBindingIndex=null,this._bindingInfos=null,this._ongoingRasterizations.clear(),this._ongoingRasterizations=null,this._imageRequestQueue.clear(),this._imageRequestQueue=null}get sprites(){return this._spriteMosaic}get glyphs(){return this._glyphMosaic}async rasterizeItem(e,t,i,s){if(a(e))return Y(\"mapview-null-resource\",\"Unable to rasterize null resource\"),null;switch(e.type){case\"text\":case\"esriTS\":{const t=await this._rasterizeText(e,i,s);return t.forEach((e=>this._setTextureBinding(y.GLYPH,e))),{glyphMosaicItems:t}}default:{if(R(e))return Y(\"mapview-invalid-type\",`MapView does not support symbol type: ${e.type}`,e),null;const i=await this._rasterizeSpriteSymbol(e,t,s);return F(i)&&i&&this._setTextureBinding(y.SPRITE,i),{spriteMosaicItem:i}}}}bindTextures(e,t,i,s=!1){if(0===i.textureBinding)return;const r=this._bindingInfos[i.textureBinding-1],o=r.page,n=s?$.LINEAR_MIPMAP_LINEAR:$.LINEAR;switch(r.mosaicType){case y.SPRITE:{const i=this.sprites.getWidth(o),s=this.sprites.getHeight(o),r=m(k,i,s);return this._spriteMosaic.bind(e,n,o,f),t.setUniform1i(\"u_texture\",f),void t.setUniform2fv(\"u_mosaicSize\",r)}case y.GLYPH:{const i=this.glyphs.width,s=this.glyphs.height,r=m(k,i,s);return this._glyphMosaic.bind(e,n,o,p),t.setUniform1i(\"u_texture\",p),void t.setUniform2fv(\"u_mosaicSize\",r)}default:Q.error(\"mapview-texture-manager\",`Cannot handle unknown type ${r.mosaicType}`)}}_hashMosaic(e,t){return 1|e<<1|(t.sdf?1:0)<<2|t.page<<3}_setTextureBinding(e,t){const i=this._hashMosaic(e,t);if(!this._hashToBindingIndex.has(i)){const s=D.fromMosaic(e,t),r=this._bindingInfos.length+1;this._hashToBindingIndex.set(i,r),this._bindingInfos.push(s)}t.textureBinding=this._hashToBindingIndex.get(i)}async _rasterizeText(e,t,s){let o,n;if(\"cim\"in e){const t=e;o=t.fontName,n=t.text}else{const t=e;o=r(t.font),n=t.text}const a=this._invalidFontsMap.has(o),h=t||v(i(n)[0]);try{return await this._glyphMosaic.getGlyphItems(a?H:o,h,s)}catch(c){return Y(\"mapview-invalid-resource\",`Couldn't find font ${o}. Falling back to Arial Unicode MS Regular`),this._invalidFontsMap.set(o,!0),this._glyphMosaic.getGlyphItems(H,h,s)}}async _rasterizeSpriteSymbol(e,t,i){if(j(e))return;const r=L(e);if(this._spriteMosaic.has(r))return this._spriteMosaic.getSpriteItem(r);if(x(e)||S(e)&&!b(e))return this._handleAsyncResource(r,e,i);const o=w,n=this._rasterizer.rasterizeJSONResource(e,o);if(n){const{size:t,image:i,sdf:s,simplePattern:o,rasterizationScale:a}=n;return this._addItemToMosaic(r,t,{type:\"static\",data:i},P(e),s,o,a)}return new s(\"TextureManager\",\"unrecognized or null rasterized image\")}async _handleAsyncResource(e,t,i){if(this._ongoingRasterizations.has(e))return this._ongoingRasterizations.get(e);let s;s=x(t)?this._handleSVG(t,e,i):this._handleImage(t,e,i),this._ongoingRasterizations.set(e,s);try{await s,this._ongoingRasterizations.delete(e)}catch{this._ongoingRasterizations.delete(e)}return s}async _handleSVG(e,t,i){const s=[O,O],r=await this._sdfConverter.draw(e.path,i);return this._addItemToMosaic(t,s,{type:\"static\",data:new Uint32Array(r.buffer)},!1,!0,!0)}async _handleGIFOrPNG(e,t,i){const r=U(e);await this.resourceManager.fetchResource(r,i);let o=this.resourceManager.getResource(r);if(a(o))return new s(\"mapview-invalid-resource\",`Could not fetch requested resource at ${r}.`);let h=o.width,c=o.height;if(o instanceof HTMLImageElement){\"esriPMS\"===e.type&&(h=Math.round(V(o.width,B(e))),c=Math.round(o.height*(h/o.width)));const i=\"cim\"in e?e.cim.colorSubstitutions:void 0,{size:s,sdf:r,image:n}=this._rasterizer.rasterizeImageResource(h,c,o,i);return this._addItemToMosaic(t,s,{type:\"static\",data:n},P(e),r,!1)}this._allowNonPowerOfTwo||(h=n(o.width+2*_)-2*_,c=n(o.height+2*_)-2*_),h===o.width&&c===o.height||(o=E(o,h,c));const u=e.animatedSymbolProperties||{},d=e.objectId,m=new C(o,this._requestRender,u,d);return this._addItemToMosaic(t,[m.width,m.height],{type:\"animated\",data:m},P(e),!1,!1)}async _handleImage(e,t,i){if(G(e)||q(e))return this._handleGIFOrPNG(e,t,i);const r=U(e);try{let s;const o=this.resourceManager.getResource(r);if(h(o)&&o instanceof HTMLImageElement)s=o;else{const{data:e}=await this._imageRequestQueue.push(r,{...i});s=e}if(A(r))if(\"width\"in e&&\"height\"in e)s.width=d(e.width),s.height=d(e.height);else if(\"cim\"in e){const t=e.cim;s.width=d(t.width??t.scaleX*t.size),s.height=d(t.size)}if(!s.width||!s.height)return null;let n=s.width,a=s.height;\"esriPMS\"===e.type&&(n=Math.round(V(s.width,B(e))),a=Math.round(s.height*(n/s.width)));const c=\"cim\"in e?e.cim.colorSubstitutions:void 0,{size:u,sdf:m,image:l}=this._rasterizer.rasterizeImageResource(n,a,s,c);return this._addItemToMosaic(t,u,{type:\"static\",data:l},P(e),m,!1)}catch(o){if(!u(o))return new s(\"mapview-invalid-resource\",`Could not fetch requested resource at ${r}. ${o.message}`)}}_addItemToMosaic(e,t,i,s,r,o,n){return this._spriteMosaic.addSpriteItem(e,t,i,s,r,o,n)}}export{J as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{resolveIncludes as e}from\"./sources/resolver.js\";const r={shaders:{vertexShader:e(\"stencil/stencil.vert\"),fragmentShader:e(\"stencil/stencil.frag\")},attributes:new Map([[\"a_pos\",0]])};export{r as stencil};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{resolveIncludes as e}from\"./sources/resolver.js\";const r=e=>e.replace(\"-\",\"_\").toUpperCase(),t=e=>`#define ${r(e)}\\n`;function n(r){return{attributes:new Map([[\"a_pos\",0],[\"a_tex\",1]]),shaders:{vertexShader:t(r)+e(\"blend/blend.vert\"),fragmentShader:t(r)+e(\"blend/blend.frag\")}}}export{n as createProgramTemplate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/Error.js\";import r from\"../../../../../core/Logger.js\";import{disposeMaybe as t,isSome as s}from\"../../../../../core/maybe.js\";import{WGLDrawPhase as i}from\"../enums.js\";import a from\"../VertexStream.js\";import{createProgramTemplate as n}from\"../shaders/BlendPrograms.js\";import{BlendFactor as o,TextureType as d,PixelFormat as u,PixelType as f,TextureWrapMode as c,TextureSamplingMode as m}from\"../../../../webgl/enums.js\";import{Texture as h}from\"../../../../webgl/Texture.js\";const l=r.getLogger(\"esri.views.2d.engine.webgl.effects.blendEffects.BlendEffect\");class _{constructor(){this._size=[0,0]}dispose(e){this._backBufferTexture=t(this._backBufferTexture),this._quad=t(this._quad)}draw(r,t,s,a,d){const{context:u,drawPhase:f}=r;if(this._setupShader(u),a&&\"normal\"!==a&&f!==i.LABEL)return void this._drawBlended(r,t,s,a,d);const c=n(\"normal\"),m=u.programCache.acquire(c.shaders.vertexShader,c.shaders.fragmentShader,c.attributes);if(!m)return void l.error(new e(\"mapview-BlendEffect\",'Error creating shader program for blend mode \"normal\"'));u.useProgram(m),t.setSamplingMode(s),u.bindTexture(t,0),m.setUniform1i(\"u_layerTexture\",0),m.setUniform1f(\"u_opacity\",d),u.setBlendingEnabled(!0),u.setBlendFunction(o.ONE,o.ONE_MINUS_SRC_ALPHA);const h=this._quad;h.draw(),h.unbind(),m.dispose()}_drawBlended(r,t,i,a,d){const{context:u,state:f,pixelRatio:c,inFadeTransition:m}=r,{size:h}=f,_=u.getBoundFramebufferObject();let p,b;if(s(_)){const e=_.descriptor;p=e.width,b=e.height}else p=Math.round(c*h[0]),b=Math.round(c*h[1]);this._createOrResizeTexture(r,p,b);const g=this._backBufferTexture;_.copyToTexture(0,0,p,b,0,0,g),u.setStencilTestEnabled(!1),u.setStencilWriteMask(0),u.setBlendingEnabled(!0),u.setDepthTestEnabled(!1),u.setDepthWriteEnabled(!1);const x=n(a),T=u.programCache.acquire(x.shaders.vertexShader,x.shaders.fragmentShader,x.attributes);if(!T)return void l.error(new e(\"mapview-BlendEffect\",`Error creating shader program for blend mode ${a}`));u.useProgram(T),g.setSamplingMode(i),u.bindTexture(g,0),T.setUniform1i(\"u_backbufferTexture\",0),t.setSamplingMode(i),u.bindTexture(t,1),T.setUniform1i(\"u_layerTexture\",1),T.setUniform1f(\"u_opacity\",d),T.setUniform1f(\"u_inFadeOpacity\",m?1:0),u.setBlendFunction(o.ONE,o.ZERO);const E=this._quad;E.draw(),E.unbind(),T.dispose(),u.setBlendFunction(o.ONE,o.ONE_MINUS_SRC_ALPHA)}_setupShader(e){this._quad||(this._quad=new a(e,[-1,-1,1,-1,-1,1,1,1]))}_createOrResizeTexture(e,r,t){const{context:s}=e;null!==this._backBufferTexture&&r===this._size[0]&&t===this._size[1]||(this._backBufferTexture?this._backBufferTexture.resize(r,t):this._backBufferTexture=new h(s,{target:d.TEXTURE_2D,pixelFormat:u.RGBA,internalFormat:u.RGBA,dataType:f.UNSIGNED_BYTE,wrapMode:c.CLAMP_TO_EDGE,samplingMode:m.LINEAR,flipped:!1,width:r,height:t}),this._size[0]=r,this._size[1]=t)}}export{_ as BlendEffect};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{resolveIncludes as e}from\"./sources/resolver.js\";const t={shaders:{vertexShader:e(\"highlight/textured.vert\"),fragmentShader:e(\"highlight/highlight.frag\")},attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])},r={shaders:{vertexShader:e(\"highlight/textured.vert\"),fragmentShader:e(\"highlight/blur.frag\")},attributes:new Map([[\"a_position\",0],[\"a_texcoord\",1]])};export{r as blur,t as highlight};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/CircularArray.js\";import t from\"../../../../core/Evented.js\";import has from\"../../../../core/has.js\";import{isSome as s}from\"../../../../core/maybe.js\";import{createDisjointTimerQuery as r}from\"../../../webgl/capabilities/DisjointTimerQuery.js\";const n=has(\"esri-2d-profiler\");class i{constructor(s,i){if(this._events=new t,this._entries=new Map,this._timings=new e(10),this._currentContainer=null,this._currentPass=null,this._currentBrush=null,this._currentSummary=null,!n)return;this._ext=r(s.gl,{}),this._debugOutput=i;const o=s.gl;if(this.enableCommandLogging)for(const e in o)if(\"function\"==typeof o[e]){const t=o[e],s=e.includes(\"draw\");o[e]=(...r)=>(this._events.emit(\"command\",{container:this._currentContainer,pass:this._currentPass,brush:this._currentBrush,method:e,args:r,isDrawCommand:s}),this._currentSummary&&(this._currentSummary.commands++,s&&this._currentSummary.drawCommands++),t.apply(o,r))}}get enableCommandLogging(){return!(\"object\"==typeof n&&n.disableCommands)}recordContainerStart(e){n&&(this._currentContainer=e)}recordContainerEnd(){n&&(this._currentContainer=null)}recordPassStart(e){n&&(this._currentPass=e,this._initSummary())}recordPassEnd(){n&&(this._currentPass=null,this._emitSummary())}recordBrushStart(e){n&&(this._currentBrush=e)}recordBrushEnd(){n&&(this._currentBrush=null)}recordStart(e){if(n&&s(this._ext)){if(this._entries.has(e)){const t=this._entries.get(e),r=this._ext.resultAvailable(t.query),n=this._ext.disjoint();if(r&&!n){const r=this._ext.getResult(t.query)/1e6;let n=0;if(s(this._timings.enqueue(r))){const e=this._timings.entries,t=e.length;let s=0;for(const r of e)s+=r;n=s/t}const i=r.toFixed(2),o=n?n.toFixed(2):\"--\";this.enableCommandLogging?(console.groupCollapsed(`Frame report for ${e}, ${i} ms (${o} last 10 avg)\\n${t.commandsLen} Commands (${t.drawCommands} draw)`),console.log(\"RenderPass breakdown: \"),console.table(t.summaries),console.log(\"Commands: \",t.commands),console.groupEnd()):console.log(`Frame report for ${e}, ${i} ms (${o} last 10 avg)`),this._debugOutput.innerHTML=`${i} (${o})`}for(const e of t.handles)e.remove();this._ext.deleteQuery(t.query),this._entries.delete(e)}const t={name:e,query:this._ext.createQuery(),commands:[],commandsLen:0,drawCommands:0,summaries:[],handles:[]};this.enableCommandLogging&&(t.handles.push(this._events.on(\"command\",(e=>{t.commandsLen++,t.commands.push(e),e.isDrawCommand&&t.drawCommands++}))),t.handles.push(this._events.on(\"summary\",(e=>{t.summaries.push(e)})))),this._ext.beginTimeElapsed(t.query),this._entries.set(e,t)}}recordEnd(e){n&&s(this._ext)&&this._entries.has(e)&&this._ext.endTimeElapsed()}_initSummary(){this.enableCommandLogging&&(this._currentSummary={container:this._currentContainer,pass:this._currentPass,drawCommands:0,commands:0})}_emitSummary(){this.enableCommandLogging&&this._currentSummary&&this._events.emit(\"summary\",this._currentSummary)}}export{i as Profiler};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clamp as t}from\"../../../../../core/mathUtils.js\";import{StaticBitSet as s}from\"../../../layers/features/support/StaticBitSet.js\";const e=2,i=1,o=0,h=1,r=2;class l{constructor(t,e,o){this._debugMap=new Map,this._width=t*o,this._height=e*o,this._pixelRatio=o;const h=Math.ceil(this._width/i),r=Math.ceil(this._height/i);this._cols=h,this._rows=r,this._cells=s.create(h*r)}insertMetrics(t){const s=this._hasCollision(t);return s===o&&this._markMetrics(t),s}getCellId(t,s){return t+s*this._cols}has(t){return this._cells.has(t)}hasRange(t,s){return this._cells.hasRange(t,s)}set(t){this._cells.set(t)}setRange(t,s){this._cells.setRange(t,s)}_collide(s,e,l,n){const c=s-l/2,a=e-n/2,_=c+l,d=a+n;if(_<0||d<0||c>this._width||a>this._height)return h;const u=t(Math.floor(c/i),0,this._cols),p=t(Math.floor(a/i),0,this._rows),M=t(Math.ceil(_/i),0,this._cols),f=t(Math.ceil(d/i),0,this._rows);for(let t=p;t<=f;t++)for(let s=u;s<=M;s++){const e=this.getCellId(s,t);if(this.has(e))return r}return o}_mark(s,e,o,h,r){const l=s-o/2,n=e-h/2,c=l+o,a=n+h,_=t(Math.floor(l/i),0,this._cols),d=t(Math.floor(n/i),0,this._rows),u=t(Math.ceil(c/i),0,this._cols),p=t(Math.ceil(a/i),0,this._rows);for(let t=d;t<=p;t++)for(let s=_;s<=u;s++){const e=this.getCellId(s,t);this._debugMap.set(e,r),this.set(e)}return!1}_hasCollision(t){const s=t.id;let i=0,l=0;t.save();do{const s=t.boundsCount;i+=s;for(let i=0;i<s;i++){const s=t.boundsComputedAnchorX(i),o=t.boundsComputedAnchorY(i),n=(t.boundsWidth(i)+e)*this._pixelRatio,c=(t.boundsHeight(i)+e)*this._pixelRatio;switch(this._collide(s,o,n,c)){case r:return r;case h:l++}}}while(t.peekId()===s&&t.next());return t.restore(),i===l?h:o}_markMetrics(t){const s=t.id;t.save();do{const s=t.boundsCount;for(let i=0;i<s;i++){const s=t.boundsComputedAnchorX(i),o=t.boundsComputedAnchorY(i),h=(t.boundsWidth(i)+e)*this._pixelRatio,r=(t.boundsHeight(i)+e)*this._pixelRatio;this._mark(s,o,h,r,t.id)}}while(t.peekId()===s&&t.next());t.restore()}}export{l as CollisionBitsetGrid,r as HAS_COLLISION,o as NONE,h as OUTSIDE_EXTENT};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clamp as e}from\"../../../../../core/mathUtils.js\";import{meterIn as t}from\"../../../../../renderers/support/lengthUtils.js\";import{TransformationType as n}from\"../../../../../renderers/visualVariables/support/sizeVariableUtils.js\";const r=Math.PI;function i(e,t){switch(t.transformationType){case n.Additive:return s(e,t);case n.Constant:return o(t,e);case n.ClampedLinear:return u(e,t);case n.Proportional:return l(e,t);case n.Stops:return c(e,t);case n.RealWorldSize:return m(e,t);case n.Identity:return e;case n.Unknown:return null}}function a(e,t){return\"number\"==typeof e?e:i(t,e)}function s(e,t){return e+(a(t.minSize,e)||t.minDataValue)}function o(e,t){const n=e.stops;let r=n&&n.length&&n[0].size;return null==r&&(r=e.minSize),a(r,t)}function u(e,t){const n=t.minDataValue,r=t.maxDataValue,i=(e-n)/(r-n),s=a(t.minSize,e),o=a(t.maxSize,e);return e<=n?s:e>=r?o:s+i*(o-s)}function l(t,n){const r=t/n.minDataValue,i=a(n.minSize,t),s=a(n.maxSize,t);let o=null;return o=r*i,e(o,i,s)}function c(e,t){const[n,r,i]=p(e,t.cache.ipData);if(n===r)return a(t.stops[n].size,e);{const s=a(t.stops[n].size,e);return s+(a(t.stops[r].size,e)-s)*i}}function m(n,i){const s=t[i.valueUnit],o=a(i.minSize,n),u=a(i.maxSize,n),{valueRepresentation:l}=i;let c=null;return c=\"area\"===l?2*Math.sqrt(n/r)/s:\"radius\"===l||\"distance\"===l?2*n/s:n/s,e(c,o,u)}function p(e,t){if(!t)return;let n=0,r=t.length-1;return t.some(((t,i)=>e<t?(r=i,!0):(n=i,!1))),[n,r,(e-t[n])/(t[r]-t[n])]}export{i as getSizeForValueSimple};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e,isSome as t}from\"../../../../../core/maybe.js\";import{pt2px as i}from\"../../../../../core/screenUtils.js\";import{FILTER_FLAG_0 as o,EFFECT_FLAG_0 as r}from\"../definitions.js\";import{CollisionBitsetGrid as n,NONE as s,HAS_COLLISION as l,OUTSIDE_EXTENT as a}from\"./CollisionGrid.js\";import{getSizeForValueSimple as d}from\"./visualVariableSimpleUtils.js\";const c=254,u=255,f=0;function b(e,i){const o=[];e.forEachTile((e=>o.push(e))),o.sort(((e,t)=>e.instanceId-t.instanceId)),o.forEach((e=>{t(e.labelMetrics)&&e.isReady&&i(e,e.labelMetrics.getCursor())}))}function y(e){return e.layer&&(\"feature\"===e.layer.type||\"csv\"===e.layer.type||\"geojson\"===e.layer.type||\"ogc-feature\"===e.layer.type||\"stream\"===e.layer.type||\"subtype-group\"===e.layer.type||\"wfs\"===e.layer.type)}function p(e){return t=>i(d(t,e))}function m(e){const t=null!=e&&\"visualVariables\"in e&&e.visualVariables;if(!t)return null;for(const i of t)if(\"size\"===i.type)return p(i);return null}function h(e){for(const t of e){const e=\"featureReduction\"in t&&t.featureReduction&&\"labelingInfo\"in t.featureReduction?t.featureReduction:void 0,i=[...t.labelingInfo||[],...e?.labelingInfo||[]];if(!t.labelsVisible||!i.length)continue;if(i.some((e=>\"none\"===e.deconflictionStrategy)))return!0}return!1}function M(t,i){if(!y(i))return;const o=\"subtype-group\"===i.layer.type?i.layer.sublayers.items:[i.layer],r=i.layer.geometryType,n=!h(o),s={};if(\"subtype-group\"!==i.layer.type){if(\"heatmap\"===i.tileRenderer?.type)return;const e=m(i.layer.renderer);s[0]=e}const l=i.tileRenderer;if(e(l))return;const a=i.layer.visible&&!i.suspended;t.push({tileRenderer:l,vvEvaluators:s,deconflictionEnabled:n,geometryType:r,visible:a})}class g{run(e,t,i){const o=[];for(let r=e.length-1;r>=0;r--){M(o,e[r])}this._transformMetrics(o),this._runCollision(o,t,i)}_runCollision(e,t,i){const[o,r]=t.state.size,s=new n(o,r,t.pixelRatio);for(const{tileRenderer:n,deconflictionEnabled:l,visible:a}of e){const e=n.featuresView.attributeView;l?a?(this._prepare(n),this._collideVisible(s,n,i),this._collideInvisible(s,n)):b(n,((t,i)=>{for(;i.nextId();)e.setLabelMinZoom(i.id,u)})):b(n,((t,i)=>{for(;i.nextId();)e.setLabelMinZoom(i.id,f),a&&s.insertMetrics(i)}))}}_isFiltered(t,i,n){const s=i.getFilterFlags(t),l=!n.hasFilter||!!(s&o),a=e(n.featureEffect)||n.featureEffect.excludedLabelsVisible||!!(s&r);return!(l&&a)}_prepare(e){const t=e.featuresView.attributeView,i=new Set;b(e,((o,r)=>{for(;r.nextId();){if(i.has(r.id))continue;if(i.add(r.id),this._isFiltered(r.id,t,e.layerView)){t.setLabelMinZoom(r.id,c);continue}t.getLabelMinZoom(r.id)!==f?t.setLabelMinZoom(r.id,u):t.setLabelMinZoom(r.id,f)}}))}_collideVisible(e,t,i){const o=t.featuresView.attributeView,r=new Set;b(t,((t,n)=>{for(;n.nextId();)if(!r.has(n.id))if(t.key.level===i){if(0===o.getLabelMinZoom(n.id)){switch(e.insertMetrics(n)){case a:break;case l:o.setLabelMinZoom(n.id,c),r.add(n.id);break;case s:o.setLabelMinZoom(n.id,f),r.add(n.id)}}}else o.setLabelMinZoom(n.id,c)}))}_collideInvisible(e,t){const i=t.featuresView.attributeView,o=new Set;b(t,((t,r)=>{for(;r.nextId();)if(!o.has(r.id)&&i.getLabelMinZoom(r.id)===u){switch(e.insertMetrics(r)){case a:break;case l:i.setLabelMinZoom(r.id,u),o.add(r.id);break;case s:i.setLabelMinZoom(r.id,f),o.add(r.id)}}}))}_transformMetrics(e){for(const{tileRenderer:i,geometryType:o,vvEvaluators:r}of e)b(i,((e,n)=>{const s=i.featuresView.attributeView,l=e.transforms.labelMat2d;l[4]=Math.round(l[4]),l[5]=Math.round(l[5]);const a=\"polyline\"===o;for(;n.next();){const e=n.boundsCount,i=n.anchorX,o=n.anchorY;let d=n.size;const c=r[0];if(t(c)){const e=c(s.getVVSize(n.id));d=isNaN(e)||null==e||e===1/0?d:e}const u=n.directionX*(d/2),f=n.directionY*(d/2);for(let t=0;t<e;t++){let e=i,r=n.anchorY;if(a){let i=e+n.boundsX(t)+u,o=r+n.boundsY(t)+f;i=l[0]*i+l[2]*o+l[4],o=l[1]*i+l[3]*o+l[5],n.setBoundsComputedAnchorX(t,Math.floor(i)),n.setBoundsComputedAnchorY(t,Math.floor(o))}else{e=l[0]*i+l[2]*o+l[4],r=l[1]*i+l[3]*o+l[5];const s=e+n.boundsX(t)+u,a=r+n.boundsY(t)+f;n.setBoundsComputedAnchorX(t,s),n.setBoundsComputedAnchorY(t,a)}}}}))}}export{g as CollisionEngine};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../core/Accessor.js\";import\"../../core/Error.js\";import{HandleOwnerMixin as s}from\"../../core/HandleOwner.js\";import has from\"../../core/has.js\";import\"../../core/Logger.js\";import{removeMaybe as i}from\"../../core/maybe.js\";import{throttle as o}from\"../../core/throttle.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as a}from\"../../core/accessorSupport/decorators/subclass.js\";import{CollisionEngine as p}from\"./engine/webgl/collisions/CollisionEngine.js\";const l=32;let d=class extends(s(t)){constructor(e){super(e),this._applyVisibilityPassThrottled=o(this._applyVisibilityPass,l,this),this.lastUpdateId=-1,this.updateRequested=!1,this.view=null}initialize(){this.collisionEngine=new p}destroy(){this.collisionEngine=null,this._applyVisibilityPassThrottled=i(this._applyVisibilityPassThrottled)}get updating(){return has(\"esri-2d-log-updating\")&&console.log(`Updating LabelManager ${this.updateRequested}:\\n-> updateRequested: ${this.updateRequested}`),this.updateRequested}update(e){this._applyVisibilityPassThrottled(e)}viewChange(){this.requestUpdate()}requestUpdate(){this.updateRequested||(this.updateRequested=!0,this.view?.requestUpdate())}processUpdate(e){this._set(\"updateParameters\",e),this.updateRequested&&(this.updateRequested=!1,this.update(e))}_applyVisibilityPass(e){const t=this.view;if(t)try{const s=t.featuresTilingScheme.getClosestInfoForScale(e.state.scale).level;this.collisionEngine.run(t.allLayerViews.items,e,s)}catch(s){}}};e([r()],d.prototype,\"updateRequested\",void 0),e([r({readOnly:!0})],d.prototype,\"updateParameters\",void 0),e([r()],d.prototype,\"updating\",null),e([r()],d.prototype,\"view\",void 0),d=e([a(\"esri.views.2d.layers.labels.LabelManager\")],d);const u=d;export{u as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import e from\"../../../core/Accessor.js\";import{createScreenPoint as i}from\"../../../core/screenUtils.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import{ViewEventPriorities as o}from\"../../input/InputManager.js\";const a=\"esri-zoom-box\",n={container:`${a}__container`,overlay:`${a}__overlay`,background:`${a}__overlay-background`,box:`${a}__outline`},h={zoom:\"Shift\",counter:\"Ctrl\"};let l=class extends e{constructor(t){super(t),this._container=null,this._overlay=null,this._backgroundShape=null,this._boxShape=null,this._box={x:0,y:0,width:0,height:0},this._rafId=null,this._handles=null,this._redraw=this._redraw.bind(this)}destroy(){this.view=null}set view(t){this._handles&&this._handles.forEach((t=>{t.remove()})),this._handles=null,this._destroyOverlay(),this._set(\"view\",t),t&&(t.on(\"drag\",[h.zoom],(t=>this._handleDrag(t,1)),o.INTERNAL),t.on(\"drag\",[h.zoom,h.counter],(t=>this._handleDrag(t,-1)),o.INTERNAL))}_start(){this._createContainer(),this._createOverlay(),this.navigation.begin()}_update(t,e,i,r){this._box.x=t,this._box.y=e,this._box.width=i,this._box.height=r,this._rafId||(this._rafId=requestAnimationFrame(this._redraw))}_end(t,e,r,s,o){const a=this.view,n=a.toMap(i(t+.5*r,e+.5*s));let h=Math.max(r/a.width,s/a.height);-1===o&&(h=1/h),this._destroyOverlay(),this.navigation.end(),a.goTo({center:n,scale:a.scale*h})}_updateBox(t,e,i,r){const s=this._boxShape;s.setAttributeNS(null,\"x\",\"\"+t),s.setAttributeNS(null,\"y\",\"\"+e),s.setAttributeNS(null,\"width\",\"\"+i),s.setAttributeNS(null,\"height\",\"\"+r),s.setAttributeNS(null,\"class\",n.box)}_updateBackground(t,e,i,r){this._backgroundShape.setAttributeNS(null,\"d\",this._toSVGPath(t,e,i,r,this.view.width,this.view.height))}_createContainer(){const t=document.createElement(\"div\");t.className=n.container,this.view.root.appendChild(t),this._container=t}_createOverlay(){const t=this.view.width,e=this.view.height,i=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\");i.setAttributeNS(null,\"d\",\"M 0 0 L \"+t+\" 0 L \"+t+\" \"+e+\" L 0 \"+e+\" Z\"),i.setAttributeNS(null,\"class\",n.background);const r=document.createElementNS(\"http://www.w3.org/2000/svg\",\"rect\"),s=document.createElementNS(\"http://www.w3.org/2000/svg\",\"svg\");s.setAttributeNS(\"http://www.w3.org/2000/xmlns/\",\"xmlns:xlink\",\"http://www.w3.org/1999/xlink\"),s.setAttributeNS(null,\"class\",n.overlay),s.appendChild(i),s.appendChild(r),this._container.appendChild(s),this._backgroundShape=i,this._boxShape=r,this._overlay=s}_destroyOverlay(){this._container&&this._container.parentNode&&this._container.parentNode.removeChild(this._container),this._container=this._backgroundShape=this._boxShape=this._overlay=null}_toSVGPath(t,e,i,r,s,o){const a=t+i,n=e+r;return\"M 0 0 L \"+s+\" 0 L \"+s+\" \"+o+\" L 0 \"+o+\" ZM \"+t+\" \"+e+\" L \"+t+\" \"+n+\" L \"+a+\" \"+n+\" L \"+a+\" \"+e+\" Z\"}_handleDrag(t,e){const i=t.x,r=t.y,s=t.origin.x,o=t.origin.y;let a,n,h,l;switch(i>s?(a=s,h=i-s):(a=i,h=s-i),r>o?(n=o,l=r-o):(n=r,l=o-r),t.action){case\"start\":this._start();break;case\"update\":this._update(a,n,h,l);break;case\"end\":this._end(a,n,h,l,e)}t.stopPropagation()}_redraw(){if(!this._rafId)return;if(this._rafId=null,!this._overlay)return;const{x:t,y:e,width:i,height:r}=this._box;this._updateBox(t,e,i,r),this._updateBackground(t,e,i,r),this._rafId=requestAnimationFrame(this._redraw)}};t([r()],l.prototype,\"navigation\",void 0),t([r()],l.prototype,\"view\",null),l=t([s(\"esri.views.2d.navigation.ZoomBox\")],l);const c=l;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t){this._gain=t,this.lastValue=void 0,this.filteredDelta=void 0}update(t){if(this.hasLastValue()){const e=this.computeDelta(t);this._updateDelta(e)}this.lastValue=t}reset(){this.lastValue=void 0,this.filteredDelta=void 0}hasLastValue(){return void 0!==this.lastValue}hasFilteredDelta(){return void 0!==this.filteredDelta}computeDelta(t){return void 0===this.lastValue?NaN:t-this.lastValue}_updateDelta(t){void 0!==this.filteredDelta?this.filteredDelta=(1-this._gain)*this.filteredDelta+this._gain*t:this.filteredDelta=t}}export{t as FilteredFiniteDifference};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t,i,o){this._initialVelocity=t,this._stopVelocity=i,this._friction=o,this._duration=Math.abs(Math.log(Math.abs(this._initialVelocity)/this._stopVelocity)/Math.log(1-this._friction))}get duration(){return this._duration}isFinished(t){return t>this.duration}get friction(){return this._friction}value(t){return this.valueFromInitialVelocity(this._initialVelocity,t)}valueDelta(t,i){const o=this.value(t);return this.value(t+i)-o}valueFromInitialVelocity(t,i){i=Math.min(i,this.duration);const o=1-this.friction;return t*(o**i-1)/Math.log(o)}}export{t as Momentum};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{s as e,l as t,g as i}from\"../../chunks/vec3.js\";import{c as s}from\"../../chunks/vec3f64.js\";import{FilteredFiniteDifference as n}from\"./FilteredFiniteDifference.js\";import{Momentum as r}from\"./Momentum.js\";class c extends r{constructor(e,t,i,s,n){super(e,t,i),this._sceneVelocity=s,this.direction=n}value(e){return super.valueFromInitialVelocity(this._sceneVelocity,e)}}class l{constructor(e=300,t=12,i=.84){this._minimumInitialVelocity=e,this._stopVelocity=t,this._friction=i,this.enabled=!0,this._time=new n(.6),this._screen=[new n(.4),new n(.4)],this._scene=[new n(.6),new n(.6),new n(.6)],this._tmpDirection=s()}add(e,t,i){if(this.enabled){if(this._time.hasLastValue()){if(this._time.computeDelta(i)<.015)return}this._screen[0].update(e[0]),this._screen[1].update(e[1]),this._scene[0].update(t[0]),this._scene[1].update(t[1]),this._scene[2].update(t[2]),this._time.update(i)}}reset(){this._screen[0].reset(),this._screen[1].reset(),this._scene[0].reset(),this._scene[1].reset(),this._scene[2].reset(),this._time.reset()}evaluateMomentum(){if(!this.enabled||!this._screen[0].hasFilteredDelta()||!this._time.hasFilteredDelta())return null;const e=this._screen[0].filteredDelta,t=this._screen[1].filteredDelta,i=null==e||null==t?0:Math.sqrt(e*e+t*t),s=this._time.filteredDelta,n=null==s||null==i?0:i/s;return Math.abs(n)<this._minimumInitialVelocity?null:this.createMomentum(n,this._stopVelocity,this._friction)}createMomentum(s,n,r){e(this._tmpDirection,this._scene[0].filteredDelta??0,this._scene[1].filteredDelta??0,this._scene[2].filteredDelta??0);const l=t(this._tmpDirection);l>0&&i(this._tmpDirection,this._tmpDirection,1/l);const h=this._time.filteredDelta;return new c(s,n,r,null==h?0:l/h,this._tmpDirection)}}export{c as PanPlanarMomentum,l as PanPlanarMomentumEstimator};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../../chunks/tslib.es6.js\";import\"../../../../geometry.js\";import i from\"../../../../Viewpoint.js\";import o from\"../../../../core/Accessor.js\";import{when as e}from\"../../../../core/reactiveUtils.js\";import{createScreenPointArray as s}from\"../../../../core/screenUtils.js\";import{property as n}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as m}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{g as r}from\"../../../../chunks/vec3.js\";import{c as a,f as p}from\"../../../../chunks/vec3f64.js\";import{translateBy as c}from\"../../viewpointUtils.js\";import{PanPlanarMomentumEstimator as u}from\"../../../navigation/PanPlanarMomentumEstimator.js\";import h from\"../../../../geometry/Point.js\";let v=class extends o{constructor(t){super(t),this.animationTime=0,this.momentumEstimator=new u(500,6,.92),this.momentum=null,this.tmpMomentum=a(),this.momentumFinished=!1,this.viewpoint=new i({targetGeometry:new h,scale:0,rotation:0}),this._previousDrag=null,e((()=>this.momentumFinished),(()=>this.navigation.stop()))}begin(t,i){this.navigation.begin(),this.momentumEstimator.reset(),this.addToEstimator(i),this._previousDrag=i}update(t,i){this.addToEstimator(i);let o=i.center.x,e=i.center.y;const s=this._previousDrag;o=s?s.center.x-o:-o,e=s?e-s.center.y:e,t.viewpoint=c(this.viewpoint,t.viewpoint,[o||0,e||0]),this._previousDrag=i}end(t,i){this.addToEstimator(i);const o=t.navigation.momentumEnabled;this.momentum=o?this.momentumEstimator.evaluateMomentum():null,this.animationTime=0,this.momentum&&this.onAnimationUpdate(t),this._previousDrag=null,this.navigation.end()}addToEstimator(t){const i=t.center.x,o=t.center.y,e=s(-i,o),n=p(-i,o,0);this.momentumEstimator.add(e,n,.001*t.timestamp)}onAnimationUpdate(t){this.navigation.animationManager?.animateContinous(t.viewpoint,((i,o)=>{const{momentum:e,animationTime:s,tmpMomentum:n}=this,m=.001*o;if(!(this.momentumFinished=!e||e.isFinished(s))){const o=e.valueDelta(s,m);r(n,e.direction,o),c(i,i,n),t.constraints.constrainByGeometry(i)}this.animationTime+=m}))}stopMomentumNavigation(){this.momentum&&(this.momentumEstimator.reset(),this.momentum=null,this.navigation.stop())}};t([n()],v.prototype,\"momentumFinished\",void 0),t([n()],v.prototype,\"viewpoint\",void 0),t([n()],v.prototype,\"navigation\",void 0),v=t([m(\"esri.views.2d.navigation.actions.Pan\")],v);const d=v;export{d as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clamp as t}from\"../../core/mathUtils.js\";import{FilteredFiniteDifference as e}from\"./FilteredFiniteDifference.js\";import{Momentum as i}from\"./Momentum.js\";class s{constructor(t=2.5,i=.01,s=.95,l=12){this._minimumInitialVelocity=t,this._stopVelocity=i,this._friction=s,this._maxVelocity=l,this.enabled=!0,this.value=new e(.8),this.time=new e(.3)}add(t,e){if(this.enabled&&null!=e){if(this.time.hasLastValue()){if(this.time.computeDelta(e)<.01)return;if(this.value.hasFilteredDelta()){const e=this.value.computeDelta(t);this.value.filteredDelta*e<0&&this.value.reset()}}this.time.update(e),this.value.update(t)}}reset(){this.value.reset(),this.time.reset()}evaluateMomentum(){if(!this.enabled||!this.value.hasFilteredDelta()||!this.time.hasFilteredDelta())return null;let e=this.value.filteredDelta/this.time.filteredDelta;return e=t(e,-this._maxVelocity,this._maxVelocity),Math.abs(e)<this._minimumInitialVelocity?null:this.createMomentum(e,this._stopVelocity,this._friction)}createMomentum(t,e,s){return new i(t,e,s)}}export{s as MomentumEstimator};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{MomentumEstimator as t}from\"./MomentumEstimator.js\";class a extends t{constructor(t=3,a=.01,s=.95,o=12){super(t,a,s,o)}add(t,a){const s=this.value.lastValue;if(null!=s){let a=t-s;for(;a>Math.PI;)a-=2*Math.PI;for(;a<-Math.PI;)a+=2*Math.PI;t=s+a}super.add(t,a)}}export{a as RotationMomentumEstimator};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Momentum as e}from\"./Momentum.js\";import{MomentumEstimator as t}from\"./MomentumEstimator.js\";class r extends e{constructor(e,t,r){super(e,t,r)}value(e){const t=super.value(e);return Math.exp(t)}valueDelta(e,t){const r=super.value(e),s=super.value(e+t)-r;return Math.exp(s)}}class s extends t{constructor(e=2.5,t=.01,r=.95,s=12){super(e,t,r,s)}add(e,t){super.add(Math.log(e),t)}createMomentum(e,t,s){return new r(e,t,s)}}export{r as ZoomMomentum,s as ZoomMomentumEstimator};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../../chunks/tslib.es6.js\";import\"../../../../geometry.js\";import o from\"../../../../Viewpoint.js\";import i from\"../../../../core/Accessor.js\";import{when as s}from\"../../../../core/reactiveUtils.js\";import{property as e}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as n}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{s as m,j as a}from\"../../../../chunks/vec2.js\";import{a as r}from\"../../../../chunks/vec2f64.js\";import{getPaddingScreenTranslation as h,scaleAndRotateBy as u}from\"../../viewpointUtils.js\";import{RotationMomentumEstimator as p}from\"../../../navigation/RotationMomentumEstimator.js\";import{ZoomMomentumEstimator as l}from\"../../../navigation/ZoomMomentumEstimator.js\";import c from\"../../../../geometry/Point.js\";let _=class extends i{constructor(t){super(t),this._animationTime=0,this._momentumFinished=!1,this._previousAngle=0,this._previousRadius=0,this._previousCenter=null,this._rotationMomentumEstimator=new p(.6,.15,.95),this._rotationDirection=1,this._startAngle=0,this._startRadius=0,this._updateTimestamp=null,this._zoomDirection=1,this._zoomMomentumEstimator=new l,this._zoomOnly=null,this.zoomMomentum=null,this.rotateMomentum=null,this.viewpoint=new o({targetGeometry:new c,scale:0,rotation:0}),this.addHandles(s((()=>this._momentumFinished),(()=>this.navigation.stop())))}begin(t,o){this.navigation.begin(),this._rotationMomentumEstimator.reset(),this._zoomMomentumEstimator.reset(),this._zoomOnly=null,this._previousAngle=this._startAngle=o.angle,this._previousRadius=this._startRadius=o.radius,this._previousCenter=o.center,this._updateTimestamp=null,t.constraints.rotationEnabled&&this.addToRotateEstimator(0,o.timestamp),this.addToZoomEstimator(o,1)}update(t,o){null===this._updateTimestamp&&(this._updateTimestamp=o.timestamp);const i=o.angle,s=o.radius,e=o.center,n=Math.abs(180*(i-this._startAngle)/Math.PI),m=Math.abs(s-this._startRadius),a=this._startRadius/s;if(this._previousRadius&&this._previousCenter){const r=s/this._previousRadius;let h=180*(i-this._previousAngle)/Math.PI;this._rotationDirection=h>=0?1:-1,this._zoomDirection=r>=1?1:-1,t.constraints.rotationEnabled?(null===this._zoomOnly&&o.timestamp-this._updateTimestamp>200&&(this._zoomOnly=m-n>0),null===this._zoomOnly||this._zoomOnly?h=0:this.addToRotateEstimator(i-this._startAngle,o.timestamp)):h=0,this.addToZoomEstimator(o,a),this.navigation.setViewpoint([e.x,e.y],1/r,h,[this._previousCenter.x-e.x,e.y-this._previousCenter.y])}this._previousAngle=i,this._previousRadius=s,this._previousCenter=e}end(t){this.rotateMomentum=this._rotationMomentumEstimator.evaluateMomentum(),this.zoomMomentum=this._zoomMomentumEstimator.evaluateMomentum(),this._animationTime=0,(this.rotateMomentum||this.zoomMomentum)&&this.onAnimationUpdate(t),this.navigation.end()}addToRotateEstimator(t,o){this._rotationMomentumEstimator.add(t,.001*o)}addToZoomEstimator(t,o){this._zoomMomentumEstimator.add(o,.001*t.timestamp)}canZoomIn(t){const o=t.scale,i=t.constraints.effectiveMaxScale;return 0===i||o>i}canZoomOut(t){const o=t.scale,i=t.constraints.effectiveMinScale;return 0===i||o<i}onAnimationUpdate(t){this.navigation.animationManager?.animateContinous(t.viewpoint,((o,i)=>{const s=!this.canZoomIn(t)&&this._zoomDirection>1||!this.canZoomOut(t)&&this._zoomDirection<1,e=!this.rotateMomentum||this.rotateMomentum.isFinished(this._animationTime),n=s||!this.zoomMomentum||this.zoomMomentum.isFinished(this._animationTime),p=.001*i;if(this._momentumFinished=e&&n,!this._momentumFinished){const i=this.rotateMomentum?Math.abs(this.rotateMomentum.valueDelta(this._animationTime,p))*this._rotationDirection*180/Math.PI:0;let s=this.zoomMomentum?Math.abs(this.zoomMomentum.valueDelta(this._animationTime,p)):1;const e=r(),n=r();if(this._previousCenter){m(e,this._previousCenter.x,this._previousCenter.y),h(n,t.size,t.padding),a(e,e,n);const{constraints:r,scale:p}=t,l=p*s;s<1&&!r.canZoomInTo(l)?(s=p/r.effectiveMaxScale,this.zoomMomentum=null,this.rotateMomentum=null):s>1&&!r.canZoomOutTo(l)&&(s=p/r.effectiveMinScale,this.zoomMomentum=null,this.rotateMomentum=null),u(o,t.viewpoint,s,i,e,t.size),t.constraints.constrainByGeometry(o)}}this._animationTime+=p}))}stopMomentumNavigation(){(this.rotateMomentum||this.zoomMomentum)&&(this.rotateMomentum&&(this._rotationMomentumEstimator.reset(),this.rotateMomentum=null),this.zoomMomentum&&(this._zoomMomentumEstimator.reset(),this.zoomMomentum=null),this.navigation.stop())}};t([e()],_.prototype,\"_momentumFinished\",void 0),t([e()],_.prototype,\"viewpoint\",void 0),t([e()],_.prototype,\"navigation\",void 0),_=t([n(\"esri.views.2d.navigation.actions.Pinch\")],_);const d=_;export{d as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../../chunks/tslib.es6.js\";import\"../../../../geometry.js\";import e from\"../../../../Viewpoint.js\";import o from\"../../../../core/Accessor.js\";import{property as r}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as s}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{s as i,c as p}from\"../../../../chunks/vec2.js\";import{a as n}from\"../../../../chunks/vec2f64.js\";import{getAnchor as c,rotateBy as a,angleBetween as m}from\"../../viewpointUtils.js\";import v from\"../../../../geometry/Point.js\";const u=n(),d=n();let j=class extends o{constructor(t){super(t),this._previousCenter=n(),this.viewpoint=new e({targetGeometry:new v,scale:0,rotation:0})}begin(t,e){this.navigation.begin(),i(this._previousCenter,e.center.x,e.center.y)}update(t,e){const{state:{size:o,padding:r}}=t;i(u,e.center.x,e.center.y),c(d,o,r),t.viewpoint=a(this.viewpoint,t.state.paddedViewState.viewpoint,m(d,this._previousCenter,u)),p(this._previousCenter,u)}end(){this.navigation.end()}};t([r()],j.prototype,\"viewpoint\",void 0),t([r()],j.prototype,\"navigation\",void 0),j=t([s(\"esri.views.2d.actions.Rotate\")],j);const f=j;export{f as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import i from\"../../../Viewpoint.js\";import o from\"../../../core/Accessor.js\";import{destroyMaybe as n}from\"../../../core/maybe.js\";import{property as e}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import a from\"../../../geometry/Point.js\";import{rotateBy as r,translateBy as c,padAndScaleAndRotateBy as h}from\"../viewpointUtils.js\";import m from\"./ZoomBox.js\";import p from\"./actions/Pan.js\";import u from\"./actions/Pinch.js\";import l from\"./actions/Rotate.js\";const v=10,w=1,g=new i({targetGeometry:new a}),_=[0,0],d=250;let T=class extends o{constructor(t){super(t),this._endTimer=null,this._lastEventTimestamp=null,this.animationManager=null,this.interacting=!1}initialize(){this.pan=new p({navigation:this}),this.rotate=new l({navigation:this}),this.pinch=new u({navigation:this}),this.zoomBox=new m({view:this.view,navigation:this})}destroy(){this.pan=n(this.pan),this.rotate=n(this.rotate),this.pinch=n(this.pinch),this.zoomBox=n(this.zoomBox),this.animationManager=null}begin(){this._set(\"interacting\",!0)}end(){this._lastEventTimestamp=performance.now(),this._startTimer(d)}async zoom(t,i=this._getDefaultAnchor()){if(this.stop(),this.begin(),this.view.constraints.snapToZoom&&this.view.constraints.effectiveLODs)return t<1?this.zoomIn(i):this.zoomOut(i);this.setViewpoint(i,t,0,[0,0])}async zoomIn(t){const i=this.view,o=i.constraints.snapToNextScale(i.scale);return this._zoomToScale(o,t)}async zoomOut(t){const i=this.view,o=i.constraints.snapToPreviousScale(i.scale);return this._zoomToScale(o,t)}setViewpoint(t,i,o,n){this.begin(),this.view.state.viewpoint=this._scaleRotateTranslateViewpoint(this.view.viewpoint,t,i,o,n),this.end()}setViewpointImmediate(t,i=0,o=[0,0],n=this._getDefaultAnchor()){this.view.state.viewpoint=this._scaleRotateTranslateViewpoint(this.view.viewpoint,n,t,i,o)}continousRotateClockwise(){const t=this.get(\"view.viewpoint\");this.animationManager?.animateContinous(t,(t=>{r(t,t,-w)}))}continousRotateCounterclockwise(){const t=this.get(\"view.viewpoint\");this.animationManager?.animateContinous(t,(t=>{r(t,t,w)}))}resetRotation(){this.view.rotation=0}continousPanLeft(){this._continuousPan([-v,0])}continousPanRight(){this._continuousPan([v,0])}continousPanUp(){this._continuousPan([0,v])}continousPanDown(){this._continuousPan([0,-v])}stop(){this.pan.stopMomentumNavigation(),this.animationManager?.stop(),this.end(),null!==this._endTimer&&(clearTimeout(this._endTimer),this._endTimer=null,this._set(\"interacting\",!1))}_continuousPan(t){const i=this.view.viewpoint;this.animationManager?.animateContinous(i,(i=>{c(i,i,t),this.view.constraints.constrainByGeometry(i)}))}_startTimer(t){return null!==this._endTimer||(this._endTimer=setTimeout((()=>{this._endTimer=null;const t=performance.now()-(this._lastEventTimestamp??0);t<d?this._endTimer=this._startTimer(t):this._set(\"interacting\",!1)}),t)),this._endTimer}_getDefaultAnchor(){const{size:t,padding:{left:i,right:o,top:n,bottom:e}}=this.view;return _[0]=.5*(t[0]-o+i),_[1]=.5*(t[1]-e+n),_}async _zoomToScale(t,i=this._getDefaultAnchor()){const{view:o}=this,{constraints:n,scale:e,viewpoint:s,size:a,padding:r}=o,c=n.canZoomInTo(t),m=n.canZoomOutTo(t);if(!(t<e&&!c||t>e&&!m))return h(g,s,t/e,0,i,a,r),n.constrainByGeometry(g),o.goTo(g,{animate:!0})}_scaleRotateTranslateViewpoint(t,i,o,n,e){const{view:s}=this,{size:a,padding:r,constraints:m,scale:p,viewpoint:u}=s,l=p*o,v=m.canZoomInTo(l),w=m.canZoomOutTo(l);return(o<1&&!v||o>1&&!w)&&(o=1),c(u,u,e),h(t,u,o,n,i,a,r),m.constrainByGeometry(t)}};t([e()],T.prototype,\"animationManager\",void 0),t([e({type:Boolean,readOnly:!0})],T.prototype,\"interacting\",void 0),t([e()],T.prototype,\"pan\",void 0),t([e()],T.prototype,\"pinch\",void 0),t([e()],T.prototype,\"rotate\",void 0),t([e()],T.prototype,\"view\",void 0),t([e()],T.prototype,\"zoomBox\",void 0),T=t([s(\"esri.views.2d.navigation.MapViewNavigation\")],T);const y=T;export{y as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{resolveIncludes as r}from\"./sources/resolver.js\";import{createProgram as e}from\"../../../../webgl/ProgramTemplate.js\";const a={shaders:{vertexShader:r(\"magnifier/magnifier.vert\"),fragmentShader:r(\"magnifier/magnifier.frag\")},attributes:new Map([[\"a_pos\",0]])};function t(r){return e(r,a)}export{t as createMagnifierProgram,a as magnifierProgramTemplate};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAMA,KAAE,EAAC,YAAW,EAAC,mBAAkB,ooBAAmoB,mBAAkB,usBAAssB,GAAE,QAAO,EAAC,eAAc,6qBAA4qB,eAAc,oiCAAmiC,GAAE,MAAK,EAAC,aAAY,o1BAAm1B,aAAY,kpCAAipC,GAAE,MAAK,EAAC,aAAY,mhDAAkhD,aAAY,gwEAA+vE,GAAE,MAAK,EAAC,aAAY,goDAA+nD,aAAY,mhEAAkhE,GAAE,SAAQ,EAAC,gBAAe,gVAA+U,gBAAe,8vBAA6vB,GAAE,MAAK,EAAC,aAAY,2fAA0f,aAAY,unFAAsnF,GAAE,MAAK,EAAC,iBAAgB,6OAA4O,aAAY,uEAAsE,EAAC;;;ACA5kiB,SAASC,GAAEC,KAAE;AAAC,MAAID,MAAEC;AAAE,SAAOA,IAAE,MAAM,GAAG,EAAE,QAAS,CAAAC,QAAG;AAAC,IAAAF,QAAIA,MAAEA,IAAEE,GAAC;AAAA,EAAE,CAAE,GAAEF;AAAC;AAAC,IAAMG,KAAE,IAAIF,GAAED,EAAC;AAAE,SAASI,GAAEF,KAAE;AAAC,SAAOC,GAAE,gBAAgBD,GAAC;AAAC;;;ACAvL,IAAMG,KAAE,EAAC,SAAQ,EAAC,cAAaC,GAAE,sBAAsB,GAAE,gBAAeA,GAAE,sBAAsB,EAAC,GAAE,YAAW,oBAAI,IAAI,CAAC,CAAC,SAAQ,CAAC,GAAE,CAAC,SAAQ,CAAC,CAAC,CAAC,EAAC;;;ACA/K,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYA,KAAEC,KAAE;AAAC,SAAK,SAAO,GAAE,KAAK,UAAQ,GAAE,KAAK,QAAM,CAAC,GAAE,KAAK,SAAOD,KAAE,KAAK,UAAQC,KAAE,KAAK,MAAM,KAAK,IAAID,GAAE,GAAE,GAAEA,KAAEC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,SAASD,KAAEC,KAAE;AAAC,QAAGD,MAAE,KAAK,UAAQC,MAAE,KAAK,QAAQ,QAAO,IAAID;AAAE,QAAIE,KAAE,MAAKC,MAAE;AAAG,aAAQC,KAAE,GAAEA,KAAE,KAAK,MAAM,QAAO,EAAEA,IAAE;AAAC,YAAMC,MAAE,KAAK,MAAMD,EAAC;AAAE,MAAAJ,OAAGK,IAAE,SAAOJ,OAAGI,IAAE,WAAS,SAAOH,MAAGG,IAAE,KAAGH,GAAE,KAAGG,IAAE,KAAGH,GAAE,OAAKA,KAAEG,KAAEF,MAAEC;AAAA,IAAE;AAAC,WAAO,SAAOF,KAAE,IAAIF,QAAG,KAAK,MAAM,OAAOG,KAAE,CAAC,GAAED,GAAE,QAAMA,GAAE,UAAQA,GAAE,QAAMF,OAAG,KAAK,MAAM,KAAK,IAAIA,GAAEE,GAAE,IAAEF,KAAEE,GAAE,GAAEA,GAAE,QAAMF,KAAEC,GAAC,CAAC,GAAEC,GAAE,SAAOD,OAAG,KAAK,MAAM,KAAK,IAAID,GAAEE,GAAE,GAAEA,GAAE,IAAED,KAAEC,GAAE,OAAMA,GAAE,SAAOD,GAAC,CAAC,MAAIC,GAAE,QAAMF,OAAG,KAAK,MAAM,KAAK,IAAIA,GAAEE,GAAE,IAAEF,KAAEE,GAAE,GAAEA,GAAE,QAAMF,KAAEE,GAAE,MAAM,CAAC,GAAEA,GAAE,SAAOD,OAAG,KAAK,MAAM,KAAK,IAAID,GAAEE,GAAE,GAAEA,GAAE,IAAED,KAAED,KAAEE,GAAE,SAAOD,GAAC,CAAC,IAAG,IAAID,GAAEE,GAAE,GAAEA,GAAE,GAAEF,KAAEC,GAAC;AAAA,EAAE;AAAA,EAAC,QAAQG,IAAE;AAAC,aAAQJ,MAAE,GAAEA,MAAE,KAAK,MAAM,QAAO,EAAEA,KAAE;AAAC,YAAMC,MAAE,KAAK,MAAMD,GAAC;AAAE,UAAGC,IAAE,MAAIG,GAAE,KAAGH,IAAE,WAASG,GAAE,UAAQH,IAAE,IAAEA,IAAE,UAAQG,GAAE,EAAE,CAAAH,IAAE,SAAOG,GAAE;AAAA,eAAcH,IAAE,MAAIG,GAAE,KAAGH,IAAE,UAAQG,GAAE,SAAOH,IAAE,IAAEA,IAAE,WAASG,GAAE,EAAE,CAAAH,IAAE,UAAQG,GAAE;AAAA,eAAeA,GAAE,MAAIH,IAAE,KAAGG,GAAE,WAASH,IAAE,UAAQG,GAAE,IAAEA,GAAE,UAAQH,IAAE,EAAE,CAAAA,IAAE,IAAEG,GAAE,GAAEH,IAAE,SAAOG,GAAE;AAAA,WAAU;AAAC,YAAGA,GAAE,MAAIH,IAAE,KAAGG,GAAE,UAAQH,IAAE,SAAOG,GAAE,IAAEA,GAAE,WAASH,IAAE,EAAE;AAAS,QAAAA,IAAE,IAAEG,GAAE,GAAEH,IAAE,UAAQG,GAAE;AAAA,MAAM;AAAC,WAAK,MAAM,OAAOJ,KAAE,CAAC,GAAE,KAAK,QAAQI,EAAC;AAAA,IAAC;AAAC,SAAK,MAAM,KAAKA,EAAC;AAAA,EAAC;AAAC;;;ACA14B,IAAME,KAAE;AAAR,IAAYC,MAAE,CAAAC,QAAG,KAAK,MAAMA,MAAE,GAAG;AAAE,SAASC,GAAED,KAAE;AAAC,QAAME,MAAE,oBAAI;AAAI,aAAUC,MAAKH,IAAE,CAAAE,IAAE,IAAIH,IAAEI,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAC,SAASE,GAAEF,KAAEC,IAAEE,IAAE;AAAC,SAAOH,IAAE,IAAIC,EAAC,KAAGD,IAAE,IAAIC,IAAEE,GAAE,EAAE,KAAM,MAAI;AAAC,IAAAH,IAAE,OAAOC,EAAC;AAAA,EAAC,CAAE,EAAE,MAAO,CAAAE,OAAG;AAAC,IAAAH,IAAE,OAAOC,EAAC,GAAE,EAAEE,EAAC;AAAA,EAAC,CAAE,CAAC,GAAEH,IAAE,IAAIC,EAAC;AAAC;AAAC,IAAMG,KAAE,CAAAN,SAAI,EAAC,MAAK,IAAIA,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,MAAK,GAAE,SAAQ,EAAC,MAAK,GAAE,OAAM,GAAE,QAAO,GAAE,SAAQ,GAAE,KAAI,EAAC,GAAE,MAAKA,KAAE,KAAI,KAAE;AAAG,IAAMO,KAAN,MAAO;AAAA,EAAC,YAAYP,KAAEE,KAAEG,IAAE;AAAC,SAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,eAAa,GAAE,KAAK,cAAY,CAAC,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,oBAAI,OAAI,KAAK,QAAML,KAAE,KAAK,SAAOE,KAAE,KAAK,eAAaG,IAAE,KAAK,WAAS,IAAIL,GAAEA,MAAE,GAAEE,MAAE,CAAC,GAAE,KAAK,WAAW,KAAK,IAAI,WAAWF,MAAEE,GAAC,CAAC,GAAE,KAAK,SAAS,KAAK,IAAE,GAAE,KAAK,UAAU,KAAK,IAAI,GAAE,KAAK,qBAAqB;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS;AAAK,eAAUF,OAAK,KAAK,UAAU,CAAAA,OAAGA,IAAE,QAAQ;AAAE,SAAK,UAAU,SAAO,GAAE,KAAK,WAAW,SAAO;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,UAAMA,MAAE,CAAC,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG,GAAEE,MAAE,CAAC;AAAE,aAAQG,KAAE,GAAEA,KAAEL,IAAE,QAAOK,MAAI;AAAC,YAAMF,KAAEH,IAAEK,EAAC;AAAE,eAAQL,MAAE,GAAEA,MAAE,IAAGA,MAAI,CAAAE,IAAE,KAAKC,EAAC;AAAA,IAAC;AAAC,UAAMA,KAAE,EAAC,SAAQ,EAAC,OAAM,GAAE,QAAO,GAAE,MAAK,GAAE,KAAI,GAAE,SAAQ,EAAC,GAAE,QAAO,IAAI,WAAWD,GAAC,EAAC;AAAE,SAAK,aAAaC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcH,KAAEE,KAAEC,IAAE;AAAC,UAAME,KAAE,KAAK,eAAeL,GAAC;AAAE,WAAO,MAAM,KAAK,aAAaA,KAAEE,KAAEC,EAAC,GAAED,IAAE,IAAK,CAAAA,QAAG,KAAK,eAAeG,IAAEL,KAAEE,GAAC,CAAE;AAAA,EAAC;AAAA,EAAC,KAAKF,KAAEE,KAAEC,IAAEE,IAAE;AAAC,UAAMG,MAAE,KAAK,YAAYR,KAAEG,EAAC;AAAE,IAAAK,IAAE,gBAAgBN,GAAC,GAAE,KAAK,SAASC,EAAC,MAAIK,IAAE,QAAQ,KAAK,WAAWL,EAAC,CAAC,GAAE,KAAK,SAASA,EAAC,IAAE,QAAIH,IAAE,YAAYQ,KAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeL,KAAE;AAAC,WAAO,KAAK,YAAYA,GAAC,MAAI,KAAK,YAAYA,GAAC,IAAE,CAAC,IAAG,KAAK,YAAYA,GAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,KAAEE,KAAE;AAAC,WAAO,KAAK,UAAUA,GAAC,MAAI,KAAK,UAAUA,GAAC,IAAE,IAAI,EAAEF,KAAE,EAAC,aAAY,EAAE,OAAM,UAAS,EAAE,eAAc,OAAM,KAAK,OAAM,QAAO,KAAK,OAAM,GAAE,IAAI,WAAW,KAAK,QAAM,KAAK,MAAM,CAAC,IAAG,KAAK,UAAUE,GAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,SAAK,SAAS,KAAK,YAAY,IAAE;AAAA,EAAE;AAAA,EAAC,MAAM,aAAaF,KAAEE,KAAEC,IAAE;AAAC,UAAME,KAAEJ,GAAEC,GAAC,GAAEM,MAAE,CAAC;AAAE,IAAAH,GAAE,QAAS,CAAAH,QAAG;AAAC,MAAAM,IAAE,KAAK,KAAK,YAAYR,KAAEE,KAAEC,EAAC,CAAC;AAAA,IAAC,CAAE,GAAE,MAAM,QAAQ,IAAIK,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYR,KAAEE,KAAEC,IAAE;AAAC,QAAGD,MAAEJ,GAAE;AAAO,UAAMO,KAAEL,MAAEE;AAAE,WAAOE,GAAE,KAAK,gBAAeC,IAAG,MAAI,KAAK,aAAa,SAASL,KAAEE,KAAEC,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeH,KAAEE,KAAEC,IAAE;AAAC,QAAG,CAACH,IAAEG,EAAC,GAAE;AAAC,YAAME,KAAE,KAAK,aAAa,SAASH,KAAEC,EAAC;AAAE,UAAG,CAACE,MAAG,CAACA,GAAE,QAAQ,QAAOC,GAAEH,EAAC;AAAE,YAAMK,MAAE,KAAK,aAAaH,EAAC,GAAEI,MAAE,KAAK,cAAaX,MAAEO,GAAE;AAAQ,MAAAL,IAAEG,EAAC,IAAE,EAAC,MAAKK,KAAE,MAAKC,KAAE,SAAQX,KAAE,MAAKK,IAAE,KAAI,KAAE,GAAE,KAAK,YAAY;AAAA,IAAC;AAAC,WAAOH,IAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaH,KAAE;AAAC,UAAMK,KAAEL,IAAE;AAAQ,QAAIQ;AAAE,QAAG,MAAIH,GAAE,MAAM,CAAAG,MAAE,IAAIR,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,SAAM;AAAC,YAAME,MAAE,GAAEO,MAAEJ,GAAE,QAAM,IAAEH,KAAEJ,MAAEO,GAAE,SAAO,IAAEH;AAAE,MAAAM,MAAE,KAAK,SAAS,SAASC,KAAEX,GAAC,GAAEU,IAAE,YAAU,KAAK,SAAS,KAAK,YAAY,MAAI,KAAK,WAAW,KAAK,YAAY,IAAE,OAAM,KAAK,eAAa,KAAK,WAAW,QAAO,KAAK,WAAW,KAAK,IAAI,WAAW,KAAK,QAAM,KAAK,MAAM,CAAC,GAAE,KAAK,SAAS,KAAK,IAAE,GAAE,KAAK,UAAU,KAAK,IAAI,GAAE,KAAK,qBAAqB,GAAE,KAAK,WAAS,IAAIR,GAAE,KAAK,QAAM,GAAE,KAAK,SAAO,CAAC,GAAEQ,MAAE,KAAK,SAAS,SAASC,KAAEX,GAAC;AAAG,YAAMC,MAAE,KAAK,WAAW,KAAK,YAAY,GAAEE,MAAED,IAAE;AAAO,UAAII,KAAEE;AAAE,UAAGL,IAAE,UAAQD,MAAE,GAAEA,MAAEF,KAAEE,OAAI;AAAC,QAAAI,MAAEK,MAAET,KAAEM,MAAE,KAAK,SAAOE,IAAE,IAAER,OAAGQ,IAAE;AAAE,iBAAQR,MAAE,GAAEA,MAAES,KAAET,MAAI,CAAAD,IAAEO,MAAEN,GAAC,IAAEC,IAAEG,MAAEJ,GAAC;AAAA,MAAC;AAAC,UAAI,kBAAkB,KAAG,KAAK,eAAeD,GAAC;AAAA,IAAC;AAAC,WAAOS;AAAA,EAAC;AAAA,EAAC,eAAeR,KAAE;AAAC,UAAME,MAAE,SAAS,cAAc,QAAQ,GAAEC,KAAED,IAAE,WAAW,IAAI,GAAEG,KAAE,IAAI,UAAU,KAAK,OAAM,KAAK,MAAM,GAAEG,MAAEH,GAAE;AAAK,IAAAH,IAAE,QAAM,KAAK,OAAMA,IAAE,SAAO,KAAK,QAAOA,IAAE,MAAM,SAAO;AAAkB,aAAQO,MAAE,GAAEA,MAAET,IAAE,QAAO,EAAES,IAAE,CAAAD,IAAE,IAAEC,MAAE,CAAC,IAAET,IAAES,GAAC,GAAED,IAAE,IAAEC,MAAE,CAAC,IAAE,GAAED,IAAE,IAAEC,MAAE,CAAC,IAAE,GAAED,IAAE,IAAEC,MAAE,CAAC,IAAE;AAAI,IAAAN,GAAE,aAAaE,IAAE,GAAE,CAAC,GAAE,SAAS,KAAK,YAAYH,GAAC;AAAA,EAAC;AAAC;;;ACA12G,IAAMQ,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAI,KAAK,WAAS,CAAC,GAAE,KAAK,WAAS,CAAC,GAAEA,IAAE,KAAK,IAAG,SAAOA,IAAE,IAAI,GAAE;AAAA,MAAC,KAAK,GAAE;AAAC,cAAMC,MAAED,IAAE,WAAW;AAAE,eAAKC,IAAE,KAAK,IAAG,SAAOA,IAAE,IAAI,GAAE;AAAA,UAAC,KAAK,GAAE;AAAC,kBAAMD,MAAEC,IAAE,WAAW;AAAE,gBAAIF,KAAEG,KAAEC,KAAEC,KAAEC,IAAEC,KAAEC;AAAE,mBAAKP,IAAE,KAAK,IAAG,SAAOA,IAAE,IAAI,GAAE;AAAA,cAAC,KAAK;AAAE,gBAAAD,MAAEC,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAE,MAAEF,IAAE,SAAS;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAG,MAAEH,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAI,MAAEJ,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAK,KAAEL,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAM,MAAEN,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAO,KAAEP,IAAE,UAAU;AAAE;AAAA,cAAM;AAAQ,gBAAAA,IAAE,KAAK;AAAA,YAAC;AAAC,YAAAA,IAAE,QAAQ,GAAED,QAAI,KAAK,SAASA,GAAC,IAAE,EAAC,OAAMI,KAAE,QAAOC,KAAE,MAAKC,IAAE,KAAIC,KAAE,SAAQC,GAAC,GAAE,KAAK,SAASR,GAAC,IAAEG;AAAG;AAAA,UAAK;AAAA,UAAC;AAAQ,YAAAD,IAAE,KAAK;AAAA,QAAC;AAAC,QAAAA,IAAE,QAAQ;AAAE;AAAA,MAAK;AAAA,MAAC;AAAQ,QAAAD,IAAE,KAAK;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,KAAE;AAAC,WAAO,KAAK,SAASA,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,WAAO,KAAK,SAASA,GAAC;AAAA,EAAC;AAAC;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,SAASF,KAAE;AAAC,WAAO,KAAK,QAAQA,GAAC;AAAA,EAAC;AAAA,EAAC,SAASA,KAAEC,KAAE;AAAC,SAAK,QAAQD,GAAC,IAAEC;AAAA,EAAC;AAAC;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,YAAYH,KAAE;AAAC,SAAK,aAAW,CAAC,GAAE,KAAK,WAASA;AAAA,EAAC;AAAA,EAAC,SAASE,KAAEC,KAAEC,KAAE;AAAC,UAAMC,KAAE,KAAK,cAAcH,GAAC;AAAE,QAAGG,GAAE,SAASF,GAAC,EAAE,QAAO,QAAQ,QAAQ;AAAE,UAAMG,MAAE,MAAIH,KAAEI,KAAED,MAAE,KAAIE,MAAE,KAAK,SAAS,QAAQ,eAAcN,GAAC,EAAE,QAAQ,WAAUI,MAAE,MAAIC,EAAC;AAAE,WAAO,EAAEC,KAAE,EAAC,cAAa,gBAAe,GAAGJ,IAAC,CAAC,EAAE,KAAM,CAAAJ,QAAG;AAAC,MAAAK,GAAE,SAASF,KAAE,IAAIJ,GAAE,IAAIK,GAAE,IAAI,WAAWJ,IAAE,IAAI,GAAE,IAAI,SAASA,IAAE,IAAI,CAAC,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,SAASA,KAAEC,KAAE;AAAC,UAAMF,MAAE,KAAK,cAAcC,GAAC;AAAE,QAAG,CAACD,IAAE;AAAO,UAAMG,MAAE,KAAK,MAAMD,MAAE,GAAG;AAAE,QAAGC,MAAE,IAAI;AAAO,UAAMC,MAAEJ,IAAE,SAASG,GAAC;AAAE,WAAOC,MAAE,EAAC,SAAQA,IAAE,WAAWF,GAAC,GAAE,QAAOE,IAAE,UAAUF,GAAC,EAAC,IAAE;AAAA,EAAM;AAAA,EAAC,cAAcD,KAAE;AAAC,QAAIC,MAAE,KAAK,WAAWD,GAAC;AAAE,WAAOC,QAAIA,MAAE,KAAK,WAAWD,GAAC,IAAE,IAAIE,OAAGD;AAAA,EAAC;AAAC;;;ACA10C,IAAMQ,KAAE;AAAK,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAK,OAAK,MAAK,KAAK,OAAKA;AAAE,UAAMC,MAAE,SAAS,cAAc,QAAQ;AAAE,IAAAA,IAAE,QAAMA,IAAE,SAAOD,KAAE,KAAK,WAASC,IAAE,WAAW,IAAI,GAAE,KAAK,aAAW,IAAI,aAAaD,MAAEA,GAAC,GAAE,KAAK,aAAW,IAAI,aAAaA,MAAEA,GAAC,GAAE,KAAK,KAAG,IAAI,aAAaA,GAAC,GAAE,KAAK,KAAG,IAAI,aAAaA,GAAC,GAAE,KAAK,KAAG,IAAI,aAAaA,MAAE,CAAC,GAAE,KAAK,KAAG,IAAI,WAAWA,GAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,KAAK,aAAW,KAAK,aAAW,KAAK,KAAG,KAAK,KAAG,KAAK,KAAG,KAAK,KAAG,MAAK,KAAK,SAAO,SAAS,KAAK,YAAY,KAAK,IAAI,GAAE,KAAK,OAAK;AAAA,EAAK;AAAA,EAAC,KAAKD,KAAEG,IAAEC,MAAE,IAAG;AAAC,SAAK,SAAS;AAAE,UAAMC,MAAE,KAAK,gBAAgBL,GAAC;AAAE,WAAO,IAAI,QAAS,CAACA,KAAEM,QAAI;AAAC,YAAMC,KAAE,IAAI;AAAM,MAAAA,GAAE,MAAI,uCAAqC,mBAAmBF,GAAC,GAAEE,GAAE,SAAO,MAAI;AAAC,QAAAA,GAAE,SAAO,MAAK,KAAK,SAAS,UAAU,GAAE,GAAE,KAAK,MAAK,KAAK,IAAI,GAAE,KAAK,SAAS,UAAUA,IAAE,GAAE,GAAE,KAAK,MAAK,KAAK,IAAI;AAAE,cAAML,MAAE,KAAK,SAAS,aAAa,GAAE,GAAE,KAAK,MAAK,KAAK,IAAI,GAAEM,MAAE,IAAI,WAAW,KAAK,OAAK,KAAK,OAAK,CAAC;AAAE,iBAAQP,MAAE,GAAEA,MAAE,KAAK,OAAK,KAAK,MAAKA,OAAI;AAAC,gBAAMO,MAAEN,IAAE,KAAK,IAAED,MAAE,CAAC,IAAE;AAAI,eAAK,WAAWA,GAAC,IAAE,MAAIO,MAAE,IAAE,MAAIA,MAAET,KAAE,KAAK,IAAI,GAAE,MAAGS,GAAC,KAAG,GAAE,KAAK,WAAWP,GAAC,IAAE,MAAIO,MAAET,KAAE,MAAIS,MAAE,IAAE,KAAK,IAAI,GAAEA,MAAE,GAAE,KAAG;AAAA,QAAC;AAAC,aAAK,KAAK,KAAK,YAAW,KAAK,MAAK,KAAK,IAAI,GAAE,KAAK,KAAK,KAAK,YAAW,KAAK,MAAK,KAAK,IAAI;AAAE,iBAAQT,KAAE,GAAEA,KAAE,KAAK,OAAK,KAAK,MAAKA,MAAI;AAAC,gBAAMG,MAAE,KAAK,WAAWH,EAAC,IAAE,KAAK,WAAWA,EAAC;AAAE,UAAAM,GAAE,MAAGH,OAAG,IAAEE,MAAGI,KAAE,IAAET,EAAC;AAAA,QAAC;AAAC,QAAAC,IAAEQ,GAAC;AAAA,MAAC;AAAE,YAAMC,MAAEN,MAAGA,GAAE;AAAO,MAAAM,OAAG,EAAEA,KAAG,MAAIH,IAAEA,GAAE,CAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,QAAG,CAAC,KAAK,MAAK;AAAC,YAAML,MAAE,SAAS,gBAAgB,8BAA6B,KAAK;AAAE,MAAAA,IAAE,aAAa,SAAQ,qBAAqB,GAAEA,IAAE,aAAa,SAAQ,GAAG,GAAEA,IAAE,aAAa,UAAS,GAAG,GAAEA,IAAE,aAAa,eAAc,MAAM,GAAEA,IAAE,aAAa,QAAO,cAAc,GAAE,SAAS,KAAK,YAAYA,GAAC,GAAE,KAAK,OAAKA;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,gBAAgBA,KAAE;AAAC,UAAMC,MAAE,KAAK,SAAS,GAAEM,MAAE,SAAS,gBAAgB,8BAA6B,MAAM;AAAE,IAAAA,IAAE,aAAa,KAAIP,GAAC,GAAEC,IAAE,YAAYM,GAAC;AAAE,UAAMT,KAAES,IAAE,QAAQ,GAAER,MAAED,GAAE,QAAMA,GAAE,QAAOI,KAAE,KAAK,OAAK;AAAE,QAAIC,KAAEC,KAAEC,KAAEC;AAAE,QAAGP,MAAE,GAAE;AAAC,MAAAK,MAAED,MAAED,KAAEJ,GAAE;AAAM,YAAME,MAAEE,MAAG,IAAEH;AAAG,MAAAM,MAAE,KAAK,OAAK,GAAEC,KAAEJ,KAAEF,MAAE;AAAA,IAAC,OAAK;AAAC,MAAAG,MAAEC,MAAEF,KAAEJ,GAAE;AAAO,MAAAO,MAAEH,KAAEA,KAAEH,MAAE,GAAEO,KAAE,KAAK,OAAK;AAAA,IAAC;AAAC,UAAME,MAAE,CAACV,GAAE,IAAEK,MAAEE,KAAEI,KAAE,CAACX,GAAE,IAAEM,MAAEE;AAAE,IAAAC,IAAE,aAAa,SAAQ,qBAAqBJ,GAAC,WAAWC,GAAC,KAAKI,GAAC,KAAKC,EAAC,GAAG;AAAE,UAAMC,KAAE,kCAAkC,KAAK,IAAI,YAAY,KAAK,IAAI,wCAAwCT,IAAE,SAAS;AAAS,WAAOA,IAAE,YAAYM,GAAC,GAAEG;AAAA,EAAC;AAAA,EAAC,KAAKV,KAAEC,KAAEM,KAAE;AAAC,UAAMT,KAAE,KAAK,IAAGC,MAAE,KAAK,IAAGG,KAAE,KAAK,IAAGC,MAAE,KAAK;AAAG,aAAQC,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,eAAQL,MAAE,GAAEA,MAAEQ,KAAER,MAAI,CAAAD,GAAEC,GAAC,IAAEC,IAAED,MAAEE,MAAEG,GAAC;AAAE,WAAK,OAAON,IAAEC,KAAEG,IAAEC,KAAEI,GAAC;AAAE,eAAQT,KAAE,GAAEA,KAAES,KAAET,KAAI,CAAAE,IAAEF,KAAEG,MAAEG,GAAC,IAAEL,IAAED,EAAC;AAAA,IAAC;AAAC,aAAQM,MAAE,GAAEA,MAAEG,KAAEH,OAAI;AAAC,eAAQG,MAAE,GAAEA,MAAEN,KAAEM,MAAI,CAAAT,GAAES,GAAC,IAAEP,IAAEI,MAAEH,MAAEM,GAAC;AAAE,WAAK,OAAOT,IAAEC,KAAEG,IAAEC,KAAEF,GAAC;AAAE,eAAQM,MAAE,GAAEA,MAAEN,KAAEM,MAAI,CAAAP,IAAEI,MAAEH,MAAEM,GAAC,IAAE,KAAK,KAAKR,IAAEQ,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAOP,KAAEC,KAAEM,KAAER,KAAEG,IAAE;AAAC,IAAAK,IAAE,CAAC,IAAE,GAAER,IAAE,CAAC,IAAE,CAACD,IAAEC,IAAE,CAAC,IAAE,CAACD;AAAE,aAAQK,MAAE,GAAEC,MAAE,GAAED,MAAED,IAAEC,OAAI;AAAC,UAAIF,OAAGD,IAAEG,GAAC,IAAEA,MAAEA,OAAGH,IAAEO,IAAEH,GAAC,CAAC,IAAEG,IAAEH,GAAC,IAAEG,IAAEH,GAAC,OAAK,IAAED,MAAE,IAAEI,IAAEH,GAAC;AAAG,aAAKH,OAAGF,IAAEK,GAAC,IAAG,CAAAA,OAAIH,OAAGD,IAAEG,GAAC,IAAEA,MAAEA,OAAGH,IAAEO,IAAEH,GAAC,CAAC,IAAEG,IAAEH,GAAC,IAAEG,IAAEH,GAAC,OAAK,IAAED,MAAE,IAAEI,IAAEH,GAAC;AAAG,MAAAA,OAAIG,IAAEH,GAAC,IAAED,KAAEJ,IAAEK,GAAC,IAAEH,KAAEF,IAAEK,MAAE,CAAC,IAAE,CAACN;AAAA,IAAC;AAAC,aAAQA,KAAE,GAAEK,MAAE,GAAEL,KAAEI,IAAEJ,MAAI;AAAC,aAAKC,IAAEI,MAAE,CAAC,IAAEL,KAAG,CAAAK;AAAI,MAAAF,IAAEH,EAAC,KAAGA,KAAES,IAAEJ,GAAC,MAAIL,KAAES,IAAEJ,GAAC,KAAGH,IAAEO,IAAEJ,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACA/kF,SAASQ,GAAEC,KAAE;AAAC,SAAOA,OAAG,aAAWA,IAAE;AAAI;AAAC,IAAMC,MAAN,MAAM,GAAC;AAAA,EAAC,YAAYD,KAAEE,KAAEC,KAAE,GAAE;AAAC,SAAK,eAAa,CAAC,GAAE,KAAK,eAAa,GAAE,KAAK,eAAa,GAAE,KAAK,aAAW,GAAE,KAAK,cAAY,GAAE,KAAK,eAAa,oBAAI,OAAI,KAAK,mBAAiB,CAAC,GAAE,KAAK,aAAW,IAAGH,OAAG,KAAGE,OAAG,MAAI,QAAQ,MAAM,0EAA0E,GAAE,KAAK,aAAWF,KAAE,KAAK,cAAYE,KAAEC,KAAE,MAAI,KAAK,eAAaA,KAAG,KAAK,aAAW,OAAO,oBAAkB,GAAE,KAAK,WAAS,IAAIH,GAAE,KAAK,YAAW,KAAK,WAAW;AAAE,UAAMI,MAAE,KAAK,MAAM,KAAK,UAAU,GAAEC,MAAE,KAAK,MAAM,KAAK,WAAW;AAAE,SAAK,aAAa,KAAK,EAAC,aAAY,EAAC,MAAK,UAAS,MAAK,IAAI,YAAYD,MAAEC,GAAC,EAAC,GAAE,MAAK,CAAC,KAAK,YAAW,KAAK,WAAW,GAAE,OAAM,MAAG,SAAQ,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,SAASL,KAAE;AAAC,WAAOA,OAAG,KAAK,aAAa,SAAO,KAAG,KAAK,aAAaA,GAAC,EAAE,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,WAAOA,OAAG,KAAK,aAAa,SAAO,KAAG,KAAK,aAAaA,GAAC,EAAE,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,KAAE;AAAC,WAAOA,MAAE,KAAK,aAAa,SAAO,KAAK,aAAaA,GAAC,EAAE,UAAQ;AAAA,EAAI;AAAA,EAAC,IAAIA,KAAE;AAAC,WAAO,KAAK,aAAa,IAAIA,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,aAAa;AAAA,EAAI;AAAA,EAAC,cAAcA,KAAE;AAAC,WAAO,KAAK,aAAa,IAAIA,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAEG,IAAEG,KAAED,KAAEE,KAAEC,IAAEP,MAAE,GAAE;AAAC,QAAG,KAAK,aAAa,IAAID,GAAC,EAAE,QAAO,KAAK,aAAa,IAAIA,GAAC;AAAE,QAAIS,IAAEC,IAAEC;AAAE,QAAGZ,GAAEO,GAAC,EAAE,EAACG,IAAEC,IAAEC,EAAC,IAAE,KAAK,eAAeR,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,SAAM;AAAC,MAAAM,KAAE,IAAIT,GAAE,GAAE,GAAEG,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEO,KAAE,KAAK,aAAa;AAAO,YAAMV,MAAE;AAAO,WAAK,aAAa,KAAK,EAAC,aAAYM,KAAE,MAAK,CAACH,GAAE,CAAC,IAAE,IAAE,IAAEA,GAAE,CAAC,IAAE,IAAE,EAAC,GAAE,OAAM,MAAG,SAAQH,IAAC,CAAC;AAAA,IAAC;AAAC,QAAGS,GAAE,SAAO,KAAGA,GAAE,UAAQ,EAAE,QAAO;AAAK,UAAMG,KAAE,EAAC,MAAKH,IAAE,OAAMN,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,GAAE,KAAII,KAAE,eAAcC,IAAE,YAAWP,KAAE,MAAKS,GAAC;AAAE,WAAO,KAAK,aAAa,IAAIV,KAAEY,EAAC,GAAEb,GAAEO,GAAC,KAAG,KAAK,MAAM,EAAC,MAAKG,IAAE,YAAWN,IAAE,YAAWG,IAAE,MAAK,MAAKI,IAAE,UAASC,IAAE,QAAON,KAAE,KAAIE,IAAC,CAAC,GAAEK;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAO,MAAI,KAAK,iBAAiB;AAAA,EAAM;AAAA,EAAC,kBAAiB;AAAC,UAAMZ,MAAE,KAAK,iBAAiB,IAAI;AAAE,IAAAA,OAAG,KAAK,MAAMA,GAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,KAAE;AAAC,UAAME,MAAE,CAAC;AAAE,eAAUC,MAAKH,IAAE,CAAAE,IAAEC,EAAC,IAAE,KAAK,cAAcA,EAAC;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,sBAAsBF,KAAE;AAAC,UAAMG,KAAE,KAAK,cAAcH,GAAC,GAAEI,MAAED,MAAGA,GAAE;AAAK,QAAG,CAACC,IAAE,QAAO;AAAK,IAAAA,IAAE,QAAMD,GAAE,OAAMC,IAAE,SAAOD,GAAE;AAAO,UAAMG,MAAEH,GAAE,OAAME,MAAEF,GAAE,QAAOI,MAAE,IAAEC,KAAE,KAAK,aAAaL,GAAE,IAAI;AAAE,WAAM,EAAC,MAAK,CAACA,GAAE,OAAMA,GAAE,MAAM,GAAE,IAAG,EAAEC,IAAE,IAAEG,OAAGC,GAAE,CAAC,IAAGJ,IAAE,IAAEG,OAAGC,GAAE,CAAC,CAAC,GAAE,IAAG,EAAEJ,IAAE,IAAEG,MAAED,OAAGE,GAAE,CAAC,IAAGJ,IAAE,IAAEG,MAAEF,OAAGG,GAAE,CAAC,CAAC,GAAE,MAAKL,GAAE,KAAI;AAAA,EAAC;AAAA,EAAC,KAAKH,KAAEE,KAAEC,KAAE,GAAEC,MAAE,GAAE;AAAC,UAAME,MAAE,KAAK,aAAaH,EAAC,GAAEE,MAAEC,IAAE;AAAY,QAAIC,MAAED,IAAE;AAAQ,QAAGC,QAAIA,MAAEE,GAAET,KAAEM,IAAE,IAAI,GAAEA,IAAE,UAAQC,MAAGA,IAAE,gBAAgBL,GAAC,GAAEH,GAAEM,GAAC,EAAE,CAAAL,IAAE,YAAYO,KAAEH,GAAC,GAAEE,IAAE,UAAQC,IAAE,QAAQ,IAAI,WAAWF,IAAE,KAAK,MAAM,CAAC,GAAEE,IAAE,eAAe;AAAA,SAAO;AAAC,MAAAF,IAAE,KAAK,UAAUL,KAAEO,KAAEH,GAAC,GAAEG,IAAE,eAAe;AAAA,IAAC;AAAC,IAAAD,IAAE,QAAM;AAAA,EAAE;AAAA,EAAC,OAAO,UAAUN,KAAEE,KAAEC,IAAEC,KAAEE,KAAED,KAAEE,KAAEC,IAAET,KAAEE,KAAEQ,IAAE;AAAC,QAAIC,KAAEN,MAAEF,MAAEC,IAAEQ,KAAEH,KAAEH,MAAEE;AAAE,QAAGE,IAAE;AAAC,MAAAE,MAAGN;AAAE,eAAQE,MAAE,IAAGA,OAAGN,KAAEM,OAAIG,OAAIH,MAAEN,OAAGA,MAAEG,OAAGF,MAAEC,IAAEQ,MAAGN,IAAE,UAAQH,MAAE,IAAGA,OAAGH,KAAEG,MAAI,CAAAI,IAAEK,KAAET,GAAC,IAAEF,IAAEU,MAAGR,MAAEH,OAAGA,GAAC;AAAA,IAAC,MAAM,UAAQa,KAAE,GAAEA,KAAEX,KAAEW,MAAI;AAAC,eAAQV,MAAE,GAAEA,MAAEH,KAAEG,MAAI,CAAAI,IAAEK,KAAET,GAAC,IAAEF,IAAEU,KAAER,GAAC;AAAE,MAAAQ,MAAGR,KAAES,MAAGN;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAMF,IAAE;AAAC,QAAGA,GAAE,QAAM,KAAK,aAAa,OAAO;AAAO,UAAMC,MAAE,KAAK,aAAaD,GAAE,IAAI,GAAEG,MAAEF,IAAE;AAAY,QAAG,CAACL,GAAEK,IAAE,WAAW,EAAE,OAAM,IAAIA,GAAE,4BAA2B,uBAAuB;AAAE,UAAMC,MAAEF,GAAE,YAAWI,MAAED,IAAE;AAAK,IAAAC,OAAGF,OAAG,QAAQ,MAAM,4CAA4C,GAAE,GAAE,UAAUA,KAAEF,GAAE,WAAW,CAAC,GAAE,GAAE,GAAEI,KAAEJ,GAAE,SAAS,CAAC,GAAEA,GAAE,KAAK,IAAE,IAAEA,GAAE,KAAK,IAAE,IAAEA,GAAE,WAAW,CAAC,GAAEA,GAAE,WAAW,CAAC,GAAEA,GAAE,MAAM,GAAEC,IAAE,QAAM;AAAA,EAAE;AAAA,EAAC,eAAeJ,KAAEK,KAAE;AAAC,IAAAL,OAAG,IAAE,IAAEK,OAAG,IAAE;AAAE,UAAME,MAAE,KAAK,IAAIP,KAAEK,GAAC;AAAE,QAAG,KAAK,gBAAc,KAAK,eAAaE,KAAE;AAAC,YAAML,MAAE,KAAG,KAAK,KAAKA,GAAEF,GAAC,CAAC,GAAEM,MAAE,KAAG,KAAK,KAAKJ,GAAEG,GAAC,CAAC,GAAEE,MAAE,IAAIP,GAAE,GAAE,GAAEA,KAAEK,GAAC;AAAE,aAAO,KAAK,aAAa,KAAK,EAAC,aAAY,EAAC,MAAK,UAAS,MAAK,IAAI,YAAYH,MAAEI,GAAC,EAAC,GAAE,MAAK,CAACJ,KAAEI,GAAC,GAAE,OAAM,MAAG,SAAQ,OAAM,CAAC,GAAE,CAACC,KAAE,KAAK,aAAa,SAAO,GAAE,CAACL,KAAEI,GAAC,CAAC;AAAA,IAAC;AAAC,UAAME,KAAE,KAAK,SAAS,SAASR,KAAEK,GAAC;AAAE,QAAGG,GAAE,SAAO,GAAE;AAAC,YAAMN,MAAE,KAAK,aAAa,KAAK,YAAY;AAAE,aAAM,CAACA,IAAE,SAAOH,GAAEG,IAAE,WAAW,MAAIA,IAAE,YAAY,OAAK,OAAM,KAAK,eAAa,KAAK,aAAa,QAAO,KAAK,aAAa,KAAK,EAAC,aAAY,EAAC,MAAK,UAAS,MAAK,IAAI,YAAY,KAAK,aAAW,KAAK,WAAW,EAAC,GAAE,MAAK,CAAC,KAAK,YAAW,KAAK,WAAW,GAAE,OAAM,MAAG,SAAQ,OAAM,CAAC,GAAE,KAAK,WAAS,IAAIF,GAAE,KAAK,YAAW,KAAK,WAAW,GAAE,KAAK,eAAeA,KAAEK,GAAC;AAAA,IAAC;AAAC,WAAM,CAACG,IAAE,KAAK,cAAa,CAAC,KAAK,YAAW,KAAK,WAAW,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS;AAAK,eAAUR,OAAK,KAAK,cAAa;AAAC,YAAME,MAAEF,IAAE;AAAQ,MAAAE,OAAGA,IAAE,QAAQ;AAAE,YAAMC,KAAEH,IAAE;AAAY,UAAG,CAACD,GAAEI,EAAC,GAAE;AAAC,QAAAA,GAAE,KAAK,QAAQ;AAAA,MAAC;AAAA,IAAC;AAAC,SAAK,eAAa,MAAK,KAAK,aAAa,MAAM;AAAA,EAAC;AAAC;AAAC,SAASM,GAAET,KAAEE,KAAE;AAAC,SAAO,IAAI,EAAEF,KAAE,EAAC,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,OAAME,IAAE,CAAC,GAAE,QAAOA,IAAE,CAAC,EAAC,GAAE,IAAI;AAAC;;;ACAhnI,SAASW,IAAEC,KAAE;AAAC,SAAO,EAAEA,IAAE,eAAe,OAAQ,CAACC,KAAED,QAAIC,MAAED,KAAG,CAAC,CAAC;AAAC;AAAC,SAASE,GAAED,KAAE;AAAC,QAAK,EAAC,OAAMD,KAAE,QAAOG,IAAC,IAAEF;AAAE,SAAM,EAAC,gBAAeA,IAAE,eAAe,QAAQ,GAAE,UAAS,CAAAD,QAAG;AAAC,UAAMG,MAAEF,IAAE,eAAe,SAAO,IAAED;AAAE,WAAOC,IAAE,SAASE,GAAC;AAAA,EAAC,GAAE,OAAMH,KAAE,QAAOG,IAAC;AAAC;AAAC,SAASC,GAAEJ,KAAEG,KAAE;AAAC,QAAK,EAAC,OAAME,IAAE,QAAOC,KAAE,UAASJ,IAAC,IAAEF,KAAEI,MAAED,MAAEJ,IAAEC,GAAC;AAAE,SAAM,EAAC,gBAAeA,IAAE,eAAe,IAAK,CAAAA,QAAG,EAAEA,MAAEI,GAAC,CAAE,GAAE,UAASF,KAAE,OAAMG,IAAE,QAAOC,IAAC;AAAC;AAAC,SAASC,GAAEP,KAAEG,KAAE;AAAC,QAAK,EAAC,OAAME,IAAE,QAAOC,KAAE,UAASP,IAAC,IAAEC,KAAEE,MAAEF,IAAE,eAAe,MAAM,GAAEI,MAAEF,IAAE,MAAM;AAAE,SAAOA,IAAE,QAAQ,EAAEE,MAAED,GAAC,CAAC,GAAE,EAAC,gBAAeD,KAAE,UAASH,KAAE,OAAMM,IAAE,QAAOC,IAAC;AAAC;AAAC,SAASE,GAAER,KAAEG,KAAE;AAAC,QAAK,EAAC,OAAME,IAAE,QAAOC,KAAE,UAASP,IAAC,IAAEC,KAAEE,MAAEF,IAAE,eAAe,MAAM,GAAEI,MAAEF,IAAE,IAAI;AAAE,SAAOA,IAAE,KAAK,EAAEE,MAAED,GAAC,CAAC,GAAE,EAAC,gBAAeD,KAAE,UAASH,KAAE,OAAMM,IAAE,QAAOC,IAAC;AAAC;AAAC,IAAMG,KAAN,MAAO;AAAA,EAAC,YAAYR,KAAED,KAAEG,KAAEE,IAAE;AAAC,SAAK,aAAWJ,KAAE,KAAK,cAAYE,KAAE,KAAK,eAAaE,IAAE,KAAK,aAAW,GAAE,KAAK,gBAAc,GAAE,KAAK,cAAY,KAAK,WAAW,eAAe,KAAK,aAAa;AAAE,QAAIC,MAAE;AAAE,WAAKN,MAAEM,MAAG,CAAAA,OAAG,KAAK,aAAY,KAAK,UAAU;AAAE,UAAMP,MAAE,KAAK,WAAW,SAAS,KAAK,aAAa;AAAE,SAAK,aAAaA,GAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAG,KAAK,iBAAe,KAAK,YAAW,KAAK,aAAW,GAAE;AAAC,UAAG,KAAK,kBAAgB,KAAK,WAAW,eAAe,OAAO,SAAO,KAAK,aAAY;AAAA,QAAC,KAAK,EAAE;AAAK,eAAK,iBAAe,KAAK;AAAW;AAAA,QAAM,KAAK,EAAE;AAAK,eAAK,gBAAc;AAAE;AAAA,QAAM,KAAK,EAAE;AAAU,eAAK,iBAAe,KAAK,YAAW,KAAK,aAAW;AAAA,MAAE;AAAA,IAAC,WAAS,OAAK,KAAK,cAAc,SAAO,KAAK,aAAY;AAAA,MAAC,KAAK,EAAE;AAAK,aAAK,iBAAe,KAAK;AAAW;AAAA,MAAM,KAAK,EAAE;AAAK,aAAK,gBAAc,KAAK,WAAW,eAAe,SAAO;AAAE;AAAA,MAAM,KAAK,EAAE;AAAU,aAAK,iBAAe,KAAK,YAAW,KAAK,aAAW;AAAA,IAAC;AAAC,SAAK,cAAY,KAAK,WAAW,eAAe,KAAK,aAAa;AAAE,UAAME,MAAE,KAAK,WAAW,SAAS,KAAK,aAAa;AAAE,SAAK,aAAaA,GAAC;AAAA,EAAC;AAAC;AAAC,SAASS,GAAEV,KAAEU,IAAEC,IAAEC,KAAE;AAAC,MAAIC,IAAE,EAAC,YAAWC,GAAC,IAAEJ;AAAE,MAAG,QAAMI,OAAIA,KAAE,EAAE,OAAM,SAAKJ,GAAE,qBAAmBV,MAAEE,GAAEF,GAAC,IAAG,QAAMU,GAAE,aAAWV,MAAEI,GAAEJ,KAAE,EAAE,MAAIU,GAAE,QAAQ,CAAC,IAAG,QAAMA,GAAE,aAAY;AAAC,UAAML,KAAE,MAAIK,GAAE;AAAY,IAAAI,OAAI,EAAE,OAAKd,MAAEQ,GAAER,KAAE,EAAEK,EAAC,CAAC,IAAES,OAAI,EAAE,cAAYd,MAAEO,GAAEC,GAAER,KAAE,EAAEK,KAAE,CAAC,CAAC,GAAE,EAAEA,KAAE,CAAC,CAAC;AAAA,EAAE;AAAC,MAAG,QAAMK,GAAE,gBAAgB,CAAAG,KAAE,EAAE,MAAIH,GAAE,eAAe;AAAA,WAAU,QAAMA,GAAE,oBAAmB;AAAC,UAAMP,MAAEC,GAAEO,EAAC,GAAET,MAAE,UAASE,MAAE,QAAMM,GAAE,qBAAmBA,GAAE,qBAAmBR,KAAEK,KAAEP,GAAEG,KAAEC,GAAC;AAAE,IAAAS,KAAE,EAAEN,KAAER,IAAEC,GAAC,CAAC;AAAA,EAAC,MAAM,CAAAa,KAAE,EAAE,CAAC;AAAE,SAAO,IAAIJ,GAAET,KAAEa,IAAEC,IAAEF,GAAC;AAAC;AAAC,SAASD,GAAEV,KAAED,KAAEG,KAAEE,IAAE;AAAC,QAAMC,MAAE,QAAMN,IAAE,iBAAeA,IAAE,eAAcD,MAAEW,GAAET,KAAED,KAAEG,KAAEE,EAAC;AAAE,MAAIH,KAAEE,MAAEL,IAAE;AAAY,WAASQ,KAAG;AAAC,IAAAL,MAAEI,MAAE,WAAY,MAAI;AAAC,MAAAP,IAAE,UAAU,GAAEK,MAAEL,IAAE,aAAYQ,GAAE;AAAA,IAAC,GAAGH,GAAC,IAAE;AAAA,EAAM;AAAC,SAAOG,GAAE,GAAE,EAAC,QAAO,MAAI;AAAC,IAAAD,OAAG,aAAaJ,GAAC;AAAA,EAAC,EAAC;AAAC;AAAC,IAAMU,KAAE,SAAS,cAAc,QAAQ;AAAvC,IAAyCC,KAAED,GAAE,WAAW,IAAI;AAAE,SAAS,EAAEX,KAAEE,KAAEE,IAAE;AAAC,EAAAO,GAAE,QAAMT,KAAES,GAAE,SAAOP;AAAE,QAAMC,MAAE,CAAC,GAAEP,MAAEE,IAAE,eAAe;AAAO,WAAQC,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,UAAMH,MAAEE,IAAE,SAASC,GAAC;AAAE,IAAAW,GAAE,UAAU,GAAE,GAAEV,KAAEE,EAAC,GAAEN,eAAa,YAAUc,GAAE,UAAUJ,GAAEV,GAAC,GAAE,GAAE,GAAEI,KAAEE,EAAC,IAAEQ,GAAE,UAAUd,KAAE,GAAE,GAAEI,KAAEE,EAAC,GAAEC,IAAE,KAAKO,GAAE,aAAa,GAAE,GAAEV,KAAEE,EAAC,CAAC;AAAA,EAAC;AAAC,SAAM,EAAC,OAAMF,KAAE,QAAOE,IAAE,gBAAeJ,IAAE,gBAAe,UAAS,CAAAA,QAAGK,IAAEL,GAAC,EAAC;AAAC;;;ACA58F,IAAMc,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,KAAEF,KAAEG,KAAE;AAAC,SAAK,aAAWF,KAAE,KAAK,aAAW;AAAK,UAAMG,KAAE,CAAAH,QAAG;AAAC,WAAK,aAAWA,KAAEC,IAAE,cAAc;AAAA,IAAC;AAAE,SAAK,aAAW,KAAK,WAAW,eAAe,QAAO,KAAK,QAAM,KAAK,WAAW,OAAM,KAAK,SAAO,KAAK,WAAW,QAAO,KAAK,cAAYG,GAAE,KAAK,YAAWL,KAAEG,KAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,YAAY,OAAO;AAAA,EAAC;AAAA,EAAC,UAAUE,IAAEN,KAAEG,KAAE;AAAC,IAAAG,GAAE,YAAYN,KAAEG,GAAC,GAAE,EAAE,KAAK,UAAU,MAAIH,IAAE,WAAW,GAAE,IAAE,IAAE,KAAK,WAAW,OAAM,KAAK,WAAW,QAAO,KAAK,UAAU,GAAE,KAAK,aAAW;AAAA,EAAK;AAAC;;;ACAxmB,SAASO,IAAEA,KAAE;AAAC,UAAOA,IAAE,MAAK;AAAA,IAAC,KAAI;AAAU,aAAM,GAAGA,IAAE,KAAK,IAAIA,IAAE,IAAI;AAAA,IAAG,KAAI;AAAU,aAAM,GAAGA,IAAE,KAAK,IAAIA,IAAE,GAAG;AAAA,IAAG,KAAI;AAAU,aAAM,GAAGA,IAAE,KAAK;AAAA,IAAG,KAAI;AAAA,IAAU,KAAI;AAAU,aAAOA,IAAE,YAAU,GAAGA,IAAE,SAAS,GAAGA,IAAE,KAAK,GAAGA,IAAE,MAAM,KAAG,GAAGA,IAAE,GAAG,GAAGA,IAAE,KAAK,GAAGA,IAAE,MAAM;AAAA,IAAG;AAAQ,aAAM,gBAAeA,MAAEA,IAAE,aAAW,KAAK,UAAUA,GAAC;AAAA,EAAC;AAAC;;;ACAmxC,IAAM,IAAEC,GAAE;AAAV,IAAY,IAAE;AAAd,IAAyC,IAAE;AAA3C,IAA+C,IAAEC,GAAE,UAAU,2CAA2C;AAAE,SAASC,GAAEC,KAAEC,KAAE;AAAC,QAAMC,KAAE,KAAK,MAAM,EAAED,GAAC,IAAE,OAAO,gBAAgB,GAAEH,MAAEI,MAAG,MAAI,IAAE;AAAE,SAAO,KAAK,IAAIF,KAAEE,KAAEJ,GAAC;AAAC;AAAC,IAAM,IAAE,CAACE,KAAEC,KAAEC,OAAI,EAAE,MAAM,IAAIJ,GAAEE,KAAEC,KAAEC,EAAC,CAAC;AAAE,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,WAAWH,KAAEC,KAAE;AAAC,WAAO,IAAI,GAAED,KAAEC,IAAE,MAAKA,IAAE,GAAG;AAAA,EAAC;AAAA,EAAC,YAAYD,KAAEC,KAAEC,IAAE;AAAC,SAAK,aAAWF,KAAE,KAAK,OAAKC,KAAE,KAAK,MAAIC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEE,KAAEC,KAAE;AAAC,SAAK,iBAAeH,IAAE,KAAK,kBAAgBE,KAAE,KAAK,sBAAoBC,KAAE,KAAK,mBAAiB,oBAAI,OAAI,KAAK,gBAAc,IAAID,GAAE,CAAC,GAAE,KAAK,gBAAc,IAAI,SAAM,KAAK,sBAAoB,oBAAI,OAAI,KAAK,yBAAuB,oBAAI,OAAI,KAAK,qBAAmB,IAAI,EAAE,EAAC,aAAY,IAAG,SAAQ,OAAMJ,KAAEE,OAAI;AAAC,QAAEA,EAAC;AAAE,UAAG;AAAC,eAAO,MAAM,EAAEF,KAAE,EAAC,cAAa,SAAQ,QAAOE,GAAC,CAAC;AAAA,MAAC,SAAOE,KAAE;AAAC,YAAG,CAAC,EAAEA,GAAC,EAAE,OAAM,IAAIN,GAAE,4BAA2B,yCAAyCE,GAAC,IAAGI,GAAC;AAAE,cAAMA;AAAA,MAAC;AAAA,IAAC,EAAC,CAAC,GAAE,KAAK,gBAAc,IAAIP,IAAE,MAAK,MAAK,GAAG,GAAE,KAAK,eAAa,IAAIO,GAAE,GAAG,EAAE,QAAQ,0BAA0B,GAAE,KAAK,eAAa,IAAIE,GAAE,MAAK,MAAK,KAAK,YAAY,GAAE,KAAK,cAAY,IAAIC,GAAEH,GAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc,QAAQ,GAAE,KAAK,aAAa,QAAQ,GAAE,KAAK,YAAY,QAAQ,GAAE,KAAK,cAAc,QAAQ,GAAE,KAAK,gBAAc,MAAK,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAK,KAAK,oBAAoB,MAAM,GAAE,KAAK,sBAAoB,MAAK,KAAK,gBAAc,MAAK,KAAK,uBAAuB,MAAM,GAAE,KAAK,yBAAuB,MAAK,KAAK,mBAAmB,MAAM,GAAE,KAAK,qBAAmB;AAAA,EAAI;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,MAAM,cAAcJ,KAAEC,KAAEC,IAAEJ,KAAE;AAAC,QAAG,EAAEE,GAAC,EAAE,QAAO,EAAE,yBAAwB,mCAAmC,GAAE;AAAK,YAAOA,IAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAO,KAAI,UAAS;AAAC,cAAMC,MAAE,MAAM,KAAK,eAAeD,KAAEE,IAAEJ,GAAC;AAAE,eAAOG,IAAE,QAAS,CAAAD,QAAG,KAAK,mBAAmB,EAAE,OAAMA,GAAC,CAAE,GAAE,EAAC,kBAAiBC,IAAC;AAAA,MAAC;AAAA,MAAC,SAAQ;AAAC,YAAG,GAAED,GAAC,EAAE,QAAO,EAAE,wBAAuB,yCAAyCA,IAAE,IAAI,IAAGA,GAAC,GAAE;AAAK,cAAME,KAAE,MAAM,KAAK,uBAAuBF,KAAEC,KAAEH,GAAC;AAAE,eAAOE,GAAEE,EAAC,KAAGA,MAAG,KAAK,mBAAmB,EAAE,QAAOA,EAAC,GAAE,EAAC,kBAAiBA,GAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaF,KAAEC,KAAEC,IAAEJ,MAAE,OAAG;AAAC,QAAG,MAAII,GAAE,eAAe;AAAO,UAAME,MAAE,KAAK,cAAcF,GAAE,iBAAe,CAAC,GAAEG,MAAED,IAAE,MAAKP,MAAEC,MAAE,EAAE,uBAAqB,EAAE;AAAO,YAAOM,IAAE,YAAW;AAAA,MAAC,KAAK,EAAE,QAAO;AAAC,cAAMF,KAAE,KAAK,QAAQ,SAASG,GAAC,GAAEP,MAAE,KAAK,QAAQ,UAAUO,GAAC,GAAED,MAAEA,GAAE,GAAEF,IAAEJ,GAAC;AAAE,eAAO,KAAK,cAAc,KAAKE,KAAEH,KAAEQ,KAAEG,EAAC,GAAEP,IAAE,aAAa,aAAYO,EAAC,GAAE,KAAKP,IAAE,cAAc,gBAAeG,GAAC;AAAA,MAAC;AAAA,MAAC,KAAK,EAAE,OAAM;AAAC,cAAMF,KAAE,KAAK,OAAO,OAAMJ,MAAE,KAAK,OAAO,QAAOM,MAAEA,GAAE,GAAEF,IAAEJ,GAAC;AAAE,eAAO,KAAK,aAAa,KAAKE,KAAEH,KAAEQ,KAAE,CAAC,GAAEJ,IAAE,aAAa,aAAY,CAAC,GAAE,KAAKA,IAAE,cAAc,gBAAeG,GAAC;AAAA,MAAC;AAAA,MAAC;AAAQ,UAAE,MAAM,2BAA0B,8BAA8BA,IAAE,UAAU,EAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAYJ,KAAEC,KAAE;AAAC,WAAO,IAAED,OAAG,KAAGC,IAAE,MAAI,IAAE,MAAI,IAAEA,IAAE,QAAM;AAAA,EAAC;AAAA,EAAC,mBAAmBD,KAAEC,KAAE;AAAC,UAAMC,KAAE,KAAK,YAAYF,KAAEC,GAAC;AAAE,QAAG,CAAC,KAAK,oBAAoB,IAAIC,EAAC,GAAE;AAAC,YAAMJ,MAAEK,GAAE,WAAWH,KAAEC,GAAC,GAAEG,MAAE,KAAK,cAAc,SAAO;AAAE,WAAK,oBAAoB,IAAIF,IAAEE,GAAC,GAAE,KAAK,cAAc,KAAKN,GAAC;AAAA,IAAC;AAAC,IAAAG,IAAE,iBAAe,KAAK,oBAAoB,IAAIC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeF,KAAEC,KAAEH,KAAE;AAAC,QAAIO,KAAER;AAAE,QAAG,SAAQG,KAAE;AAAC,YAAMC,MAAED;AAAE,MAAAK,MAAEJ,IAAE,UAASJ,MAAEI,IAAE;AAAA,IAAI,OAAK;AAAC,YAAMA,MAAED;AAAE,MAAAK,MAAEA,GAAEJ,IAAE,IAAI,GAAEJ,MAAEI,IAAE;AAAA,IAAI;AAAC,UAAMQ,MAAE,KAAK,iBAAiB,IAAIJ,GAAC,GAAEK,KAAET,OAAG,GAAEC,GAAEL,GAAC,EAAE,CAAC,CAAC;AAAE,QAAG;AAAC,aAAO,MAAM,KAAK,aAAa,cAAcY,MAAE,IAAEJ,KAAEK,IAAEZ,GAAC;AAAA,IAAC,SAAOS,KAAE;AAAC,aAAO,EAAE,4BAA2B,sBAAsBF,GAAC,4CAA4C,GAAE,KAAK,iBAAiB,IAAIA,KAAE,IAAE,GAAE,KAAK,aAAa,cAAc,GAAEK,IAAEZ,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,uBAAuBE,KAAEC,KAAEC,IAAE;AAAC,QAAG,GAAEF,GAAC,EAAE;AAAO,UAAMI,MAAEJ,IAAEA,GAAC;AAAE,QAAG,KAAK,cAAc,IAAII,GAAC,EAAE,QAAO,KAAK,cAAc,cAAcA,GAAC;AAAE,QAAG,GAAEJ,GAAC,KAAG,GAAEA,GAAC,KAAG,CAAC,GAAEA,GAAC,EAAE,QAAO,KAAK,qBAAqBI,KAAEJ,KAAEE,EAAC;AAAE,UAAMG,MAAE,IAAER,MAAE,KAAK,YAAY,sBAAsBG,KAAEK,GAAC;AAAE,QAAGR,KAAE;AAAC,YAAK,EAAC,MAAKI,KAAE,OAAMC,IAAE,KAAIJ,KAAE,eAAcO,KAAE,oBAAmBI,IAAC,IAAEZ;AAAE,aAAO,KAAK,iBAAiBO,KAAEH,KAAE,EAAC,MAAK,UAAS,MAAKC,GAAC,GAAE,GAAEF,GAAC,GAAEF,KAAEO,KAAEI,GAAC;AAAA,IAAC;AAAC,WAAO,IAAIX,GAAE,kBAAiB,uCAAuC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBE,KAAEC,KAAEC,IAAE;AAAC,QAAG,KAAK,uBAAuB,IAAIF,GAAC,EAAE,QAAO,KAAK,uBAAuB,IAAIA,GAAC;AAAE,QAAIF;AAAE,IAAAA,MAAE,GAAEG,GAAC,IAAE,KAAK,WAAWA,KAAED,KAAEE,EAAC,IAAE,KAAK,aAAaD,KAAED,KAAEE,EAAC,GAAE,KAAK,uBAAuB,IAAIF,KAAEF,GAAC;AAAE,QAAG;AAAC,YAAMA,KAAE,KAAK,uBAAuB,OAAOE,GAAC;AAAA,IAAC,QAAM;AAAC,WAAK,uBAAuB,OAAOA,GAAC;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAA,EAAC,MAAM,WAAWE,KAAEC,KAAEC,IAAE;AAAC,UAAMJ,MAAE,CAAC,GAAE,CAAC,GAAEM,MAAE,MAAM,KAAK,cAAc,KAAKJ,IAAE,MAAKE,EAAC;AAAE,WAAO,KAAK,iBAAiBD,KAAEH,KAAE,EAAC,MAAK,UAAS,MAAK,IAAI,YAAYM,IAAE,MAAM,EAAC,GAAE,OAAG,MAAG,IAAE;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBJ,KAAEC,KAAEC,IAAE;AAAC,UAAME,MAAE,GAAEJ,GAAC;AAAE,UAAM,KAAK,gBAAgB,cAAcI,KAAEF,EAAC;AAAE,QAAIG,MAAE,KAAK,gBAAgB,YAAYD,GAAC;AAAE,QAAG,EAAEC,GAAC,EAAE,QAAO,IAAIP,GAAE,4BAA2B,yCAAyCM,GAAC,GAAG;AAAE,QAAIM,KAAEL,IAAE,OAAME,MAAEF,IAAE;AAAO,QAAGA,eAAa,kBAAiB;AAAC,oBAAYL,IAAE,SAAOU,KAAE,KAAK,MAAMX,GAAEM,IAAE,OAAM,GAAEL,GAAC,CAAC,CAAC,GAAEO,MAAE,KAAK,MAAMF,IAAE,UAAQK,KAAEL,IAAE,MAAM;AAAG,YAAMH,KAAE,SAAQF,MAAEA,IAAE,IAAI,qBAAmB,QAAO,EAAC,MAAKF,KAAE,KAAIM,KAAE,OAAMP,IAAC,IAAE,KAAK,YAAY,uBAAuBa,IAAEH,KAAEF,KAAEH,EAAC;AAAE,aAAO,KAAK,iBAAiBD,KAAEH,KAAE,EAAC,MAAK,UAAS,MAAKD,IAAC,GAAE,GAAEG,GAAC,GAAEI,KAAE,KAAE;AAAA,IAAC;AAAC,SAAK,wBAAsBM,KAAEC,GAAEN,IAAE,QAAM,IAAE,EAAC,IAAE,IAAE,IAAEE,MAAEI,GAAEN,IAAE,SAAO,IAAE,EAAC,IAAE,IAAE,KAAGK,OAAIL,IAAE,SAAOE,QAAIF,IAAE,WAASA,MAAE,EAAEA,KAAEK,IAAEH,GAAC;AAAG,UAAMK,KAAEZ,IAAE,4BAA0B,CAAC,GAAEa,KAAEb,IAAE,UAASc,KAAE,IAAId,IAAEK,KAAE,KAAK,gBAAeO,IAAEC,EAAC;AAAE,WAAO,KAAK,iBAAiBZ,KAAE,CAACa,GAAE,OAAMA,GAAE,MAAM,GAAE,EAAC,MAAK,YAAW,MAAKA,GAAC,GAAE,GAAEd,GAAC,GAAE,OAAG,KAAE;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaA,KAAEC,KAAEC,IAAE;AAAC,QAAG,GAAEF,GAAC,KAAG,GAAEA,GAAC,EAAE,QAAO,KAAK,gBAAgBA,KAAEC,KAAEC,EAAC;AAAE,UAAME,MAAE,GAAEJ,GAAC;AAAE,QAAG;AAAC,UAAIF;AAAE,YAAMO,MAAE,KAAK,gBAAgB,YAAYD,GAAC;AAAE,UAAG,EAAEC,GAAC,KAAGA,eAAa,iBAAiB,CAAAP,MAAEO;AAAA,WAAM;AAAC,cAAK,EAAC,MAAKL,IAAC,IAAE,MAAM,KAAK,mBAAmB,KAAKI,KAAE,EAAC,GAAGF,GAAC,CAAC;AAAE,QAAAJ,MAAEE;AAAA,MAAC;AAAC,UAAG,GAAEI,GAAC;AAAE,YAAG,WAAUJ,OAAG,YAAWA,IAAE,CAAAF,IAAE,QAAM,EAAEE,IAAE,KAAK,GAAEF,IAAE,SAAO,EAAEE,IAAE,MAAM;AAAA,iBAAU,SAAQA,KAAE;AAAC,gBAAMC,MAAED,IAAE;AAAI,UAAAF,IAAE,QAAM,EAAEG,IAAE,SAAOA,IAAE,SAAOA,IAAE,IAAI,GAAEH,IAAE,SAAO,EAAEG,IAAE,IAAI;AAAA,QAAC;AAAA;AAAC,UAAG,CAACH,IAAE,SAAO,CAACA,IAAE,OAAO,QAAO;AAAK,UAAID,MAAEC,IAAE,OAAMW,MAAEX,IAAE;AAAO,oBAAYE,IAAE,SAAOH,MAAE,KAAK,MAAME,GAAED,IAAE,OAAM,GAAEE,GAAC,CAAC,CAAC,GAAES,MAAE,KAAK,MAAMX,IAAE,UAAQD,MAAEC,IAAE,MAAM;AAAG,YAAMS,MAAE,SAAQP,MAAEA,IAAE,IAAI,qBAAmB,QAAO,EAAC,MAAKY,IAAE,KAAIE,IAAE,OAAMC,IAAC,IAAE,KAAK,YAAY,uBAAuBlB,KAAEY,KAAEX,KAAES,GAAC;AAAE,aAAO,KAAK,iBAAiBN,KAAEW,IAAE,EAAC,MAAK,UAAS,MAAKG,IAAC,GAAE,GAAEf,GAAC,GAAEc,IAAE,KAAE;AAAA,IAAC,SAAOT,KAAE;AAAC,UAAG,CAAC,EAAEA,GAAC,EAAE,QAAO,IAAIP,GAAE,4BAA2B,yCAAyCM,GAAC,KAAKC,IAAE,OAAO,EAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBL,KAAEC,KAAEC,IAAEJ,KAAEM,KAAEC,KAAER,KAAE;AAAC,WAAO,KAAK,cAAc,cAAcG,KAAEC,KAAEC,IAAEJ,KAAEM,KAAEC,KAAER,GAAC;AAAA,EAAC;AAAC;;;ACAjjP,IAAMmB,KAAE,EAAC,SAAQ,EAAC,cAAaC,GAAE,sBAAsB,GAAE,gBAAeA,GAAE,sBAAsB,EAAC,GAAE,YAAW,oBAAI,IAAI,CAAC,CAAC,SAAQ,CAAC,CAAC,CAAC,EAAC;;;ACApI,IAAMC,KAAE,CAAAC,QAAGA,IAAE,QAAQ,KAAI,GAAG,EAAE,YAAY;AAA1C,IAA4CC,KAAE,CAAAD,QAAG,WAAWD,GAAEC,GAAC,CAAC;AAAA;AAAK,SAASE,IAAEH,KAAE;AAAC,SAAM,EAAC,YAAW,oBAAI,IAAI,CAAC,CAAC,SAAQ,CAAC,GAAE,CAAC,SAAQ,CAAC,CAAC,CAAC,GAAE,SAAQ,EAAC,cAAaE,GAAEF,GAAC,IAAEG,GAAE,kBAAkB,GAAE,gBAAeD,GAAEF,GAAC,IAAEG,GAAE,kBAAkB,EAAC,EAAC;AAAC;;;ACAqO,IAAMC,KAAEC,GAAE,UAAU,6DAA6D;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,QAAM,CAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQC,KAAE;AAAC,SAAK,qBAAmB,EAAE,KAAK,kBAAkB,GAAE,KAAK,QAAM,EAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,KAAKC,KAAEC,KAAEJ,KAAEK,KAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,WAAUC,GAAC,IAAEL;AAAE,QAAG,KAAK,aAAaI,EAAC,GAAEF,OAAG,aAAWA,OAAGG,OAAI,EAAE,MAAM,QAAO,KAAK,KAAK,aAAaL,KAAEC,KAAEJ,KAAEK,KAAEC,EAAC;AAAE,UAAMG,MAAEC,IAAE,QAAQ,GAAEC,KAAEJ,GAAE,aAAa,QAAQE,IAAE,QAAQ,cAAaA,IAAE,QAAQ,gBAAeA,IAAE,UAAU;AAAE,QAAG,CAACE,GAAE,QAAO,KAAKZ,GAAE,MAAM,IAAIC,GAAE,uBAAsB,uDAAuD,CAAC;AAAE,IAAAO,GAAE,WAAWI,EAAC,GAAEP,IAAE,gBAAgBJ,GAAC,GAAEO,GAAE,YAAYH,KAAE,CAAC,GAAEO,GAAE,aAAa,kBAAiB,CAAC,GAAEA,GAAE,aAAa,aAAYL,EAAC,GAAEC,GAAE,mBAAmB,IAAE,GAAEA,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB;AAAE,UAAMK,KAAE,KAAK;AAAM,IAAAA,GAAE,KAAK,GAAEA,GAAE,OAAO,GAAED,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAaR,KAAEC,KAAES,IAAER,KAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,OAAMC,IAAE,YAAWC,KAAE,kBAAiBE,GAAC,IAAER,KAAE,EAAC,MAAKS,GAAC,IAAEJ,IAAEP,KAAEM,GAAE,0BAA0B;AAAE,QAAIO,IAAEC;AAAE,QAAG,EAAEd,EAAC,GAAE;AAAC,YAAMC,MAAED,GAAE;AAAW,MAAAa,KAAEZ,IAAE,OAAMa,KAAEb,IAAE;AAAA,IAAM,MAAM,CAAAY,KAAE,KAAK,MAAML,MAAEG,GAAE,CAAC,CAAC,GAAEG,KAAE,KAAK,MAAMN,MAAEG,GAAE,CAAC,CAAC;AAAE,SAAK,uBAAuBT,KAAEW,IAAEC,EAAC;AAAE,UAAMC,KAAE,KAAK;AAAmB,IAAAf,GAAE,cAAc,GAAE,GAAEa,IAAEC,IAAE,GAAE,GAAEC,EAAC,GAAET,GAAE,sBAAsB,KAAE,GAAEA,GAAE,oBAAoB,CAAC,GAAEA,GAAE,mBAAmB,IAAE,GAAEA,GAAE,oBAAoB,KAAE,GAAEA,GAAE,qBAAqB,KAAE;AAAE,UAAM,IAAEG,IAAEL,GAAC,GAAEY,KAAEV,GAAE,aAAa,QAAQ,EAAE,QAAQ,cAAa,EAAE,QAAQ,gBAAe,EAAE,UAAU;AAAE,QAAG,CAACU,GAAE,QAAO,KAAKlB,GAAE,MAAM,IAAIC,GAAE,uBAAsB,gDAAgDK,GAAC,EAAE,CAAC;AAAE,IAAAE,GAAE,WAAWU,EAAC,GAAED,GAAE,gBAAgBH,EAAC,GAAEN,GAAE,YAAYS,IAAE,CAAC,GAAEC,GAAE,aAAa,uBAAsB,CAAC,GAAEb,IAAE,gBAAgBS,EAAC,GAAEN,GAAE,YAAYH,KAAE,CAAC,GAAEa,GAAE,aAAa,kBAAiB,CAAC,GAAEA,GAAE,aAAa,aAAYX,EAAC,GAAEW,GAAE,aAAa,mBAAkBN,KAAE,IAAE,CAAC,GAAEJ,GAAE,iBAAiB,EAAE,KAAI,EAAE,IAAI;AAAE,UAAMW,KAAE,KAAK;AAAM,IAAAA,GAAE,KAAK,GAAEA,GAAE,OAAO,GAAED,GAAE,QAAQ,GAAEV,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB;AAAA,EAAC;AAAA,EAAC,aAAaL,KAAE;AAAC,SAAK,UAAQ,KAAK,QAAM,IAAIQ,GAAER,KAAE,CAAC,IAAG,IAAG,GAAE,IAAG,IAAG,GAAE,GAAE,CAAC,CAAC;AAAA,EAAE;AAAA,EAAC,uBAAuBA,KAAEC,KAAEC,KAAE;AAAC,UAAK,EAAC,SAAQJ,IAAC,IAAEE;AAAE,aAAO,KAAK,sBAAoBC,QAAI,KAAK,MAAM,CAAC,KAAGC,QAAI,KAAK,MAAM,CAAC,MAAI,KAAK,qBAAmB,KAAK,mBAAmB,OAAOD,KAAEC,GAAC,IAAE,KAAK,qBAAmB,IAAI,EAAEJ,KAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAAS,EAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAMG,KAAE,QAAOC,IAAC,CAAC,GAAE,KAAK,MAAM,CAAC,IAAED,KAAE,KAAK,MAAM,CAAC,IAAEC;AAAA,EAAE;AAAC;;;ACA1uF,IAAMe,KAAE,EAAC,SAAQ,EAAC,cAAaC,GAAE,yBAAyB,GAAE,gBAAeA,GAAE,0BAA0B,EAAC,GAAE,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAjK,IAAmKC,KAAE,EAAC,SAAQ,EAAC,cAAaD,GAAE,yBAAyB,GAAE,gBAAeA,GAAE,qBAAqB,EAAC,GAAE,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;;;ACA5F,IAAME,MAAE,IAAI,kBAAkB;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAED,IAAE;AAAC,QAAG,KAAK,UAAQ,IAAID,MAAE,KAAK,WAAS,oBAAI,OAAI,KAAK,WAAS,IAAIE,GAAE,EAAE,GAAE,KAAK,oBAAkB,MAAK,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAK,KAAK,kBAAgB,MAAK,CAACF,IAAE;AAAO,SAAK,OAAKG,GAAED,IAAE,IAAG,CAAC,CAAC,GAAE,KAAK,eAAaD;AAAE,UAAMG,MAAEF,IAAE;AAAG,QAAG,KAAK;AAAqB,iBAAUG,OAAKD,IAAE,KAAG,cAAY,OAAOA,IAAEC,GAAC,GAAE;AAAC,cAAMC,MAAEF,IAAEC,GAAC,GAAEH,MAAEG,IAAE,SAAS,MAAM;AAAE,QAAAD,IAAEC,GAAC,IAAE,IAAIE,SAAK,KAAK,QAAQ,KAAK,WAAU,EAAC,WAAU,KAAK,mBAAkB,MAAK,KAAK,cAAa,OAAM,KAAK,eAAc,QAAOF,KAAE,MAAKE,KAAE,eAAcL,IAAC,CAAC,GAAE,KAAK,oBAAkB,KAAK,gBAAgB,YAAWA,OAAG,KAAK,gBAAgB,iBAAgBI,IAAE,MAAMF,KAAEG,GAAC;AAAA,MAAE;AAAA;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAM,EAAE,YAAU,OAAOP,OAAGA,IAAE;AAAA,EAAgB;AAAA,EAAC,qBAAqBK,KAAE;AAAC,IAAAL,QAAI,KAAK,oBAAkBK;AAAA,EAAE;AAAA,EAAC,qBAAoB;AAAC,IAAAL,QAAI,KAAK,oBAAkB;AAAA,EAAK;AAAA,EAAC,gBAAgBK,KAAE;AAAC,IAAAL,QAAI,KAAK,eAAaK,KAAE,KAAK,aAAa;AAAA,EAAE;AAAA,EAAC,gBAAe;AAAC,IAAAL,QAAI,KAAK,eAAa,MAAK,KAAK,aAAa;AAAA,EAAE;AAAA,EAAC,iBAAiBK,KAAE;AAAC,IAAAL,QAAI,KAAK,gBAAcK;AAAA,EAAE;AAAA,EAAC,iBAAgB;AAAC,IAAAL,QAAI,KAAK,gBAAc;AAAA,EAAK;AAAA,EAAC,YAAYK,KAAE;AAAC,QAAGL,OAAG,EAAE,KAAK,IAAI,GAAE;AAAC,UAAG,KAAK,SAAS,IAAIK,GAAC,GAAE;AAAC,cAAMC,MAAE,KAAK,SAAS,IAAID,GAAC,GAAEE,MAAE,KAAK,KAAK,gBAAgBD,IAAE,KAAK,GAAEN,MAAE,KAAK,KAAK,SAAS;AAAE,YAAGO,OAAG,CAACP,KAAE;AAAC,gBAAMO,MAAE,KAAK,KAAK,UAAUD,IAAE,KAAK,IAAE;AAAI,cAAIN,MAAE;AAAE,cAAG,EAAE,KAAK,SAAS,QAAQO,GAAC,CAAC,GAAE;AAAC,kBAAMF,MAAE,KAAK,SAAS,SAAQC,MAAED,IAAE;AAAO,gBAAIH,MAAE;AAAE,uBAAUK,OAAKF,IAAE,CAAAH,OAAGK;AAAE,YAAAP,MAAEE,MAAEI;AAAA,UAAC;AAAC,gBAAML,KAAEM,IAAE,QAAQ,CAAC,GAAEH,MAAEJ,MAAEA,IAAE,QAAQ,CAAC,IAAE;AAAK,eAAK,wBAAsB,QAAQ,eAAe,oBAAoBK,GAAC,KAAKJ,EAAC,QAAQG,GAAC;AAAA,EAAkBE,IAAE,WAAW,cAAcA,IAAE,YAAY,QAAQ,GAAE,QAAQ,IAAI,wBAAwB,GAAE,QAAQ,MAAMA,IAAE,SAAS,GAAE,QAAQ,IAAI,cAAaA,IAAE,QAAQ,GAAE,QAAQ,SAAS,KAAG,QAAQ,IAAI,oBAAoBD,GAAC,KAAKJ,EAAC,QAAQG,GAAC,eAAe,GAAE,KAAK,aAAa,YAAU,GAAGH,EAAC,KAAKG,GAAC;AAAA,QAAG;AAAC,mBAAUC,OAAKC,IAAE,QAAQ,CAAAD,IAAE,OAAO;AAAE,aAAK,KAAK,YAAYC,IAAE,KAAK,GAAE,KAAK,SAAS,OAAOD,GAAC;AAAA,MAAC;AAAC,YAAMC,MAAE,EAAC,MAAKD,KAAE,OAAM,KAAK,KAAK,YAAY,GAAE,UAAS,CAAC,GAAE,aAAY,GAAE,cAAa,GAAE,WAAU,CAAC,GAAE,SAAQ,CAAC,EAAC;AAAE,WAAK,yBAAuBC,IAAE,QAAQ,KAAK,KAAK,QAAQ,GAAG,WAAW,CAAAD,QAAG;AAAC,QAAAC,IAAE,eAAcA,IAAE,SAAS,KAAKD,GAAC,GAAEA,IAAE,iBAAeC,IAAE;AAAA,MAAc,CAAE,CAAC,GAAEA,IAAE,QAAQ,KAAK,KAAK,QAAQ,GAAG,WAAW,CAAAD,QAAG;AAAC,QAAAC,IAAE,UAAU,KAAKD,GAAC;AAAA,MAAC,CAAE,CAAC,IAAG,KAAK,KAAK,iBAAiBC,IAAE,KAAK,GAAE,KAAK,SAAS,IAAID,KAAEC,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAUD,KAAE;AAAC,IAAAL,OAAG,EAAE,KAAK,IAAI,KAAG,KAAK,SAAS,IAAIK,GAAC,KAAG,KAAK,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,SAAK,yBAAuB,KAAK,kBAAgB,EAAC,WAAU,KAAK,mBAAkB,MAAK,KAAK,cAAa,cAAa,GAAE,UAAS,EAAC;AAAA,EAAE;AAAA,EAAC,eAAc;AAAC,SAAK,wBAAsB,KAAK,mBAAiB,KAAK,QAAQ,KAAK,WAAU,KAAK,eAAe;AAAA,EAAC;AAAC;;;ACAvtF,IAAMG,MAAE;AAAR,IAAUC,KAAE;AAAZ,IAAcC,KAAE;AAAhB,IAAkBC,KAAE;AAApB,IAAsBC,KAAE;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAEN,KAAEE,KAAE;AAAC,SAAK,YAAU,oBAAI,OAAI,KAAK,SAAOI,MAAEJ,KAAE,KAAK,UAAQF,MAAEE,KAAE,KAAK,cAAYA;AAAE,UAAMC,KAAE,KAAK,KAAK,KAAK,SAAOF,EAAC,GAAEG,MAAE,KAAK,KAAK,KAAK,UAAQH,EAAC;AAAE,SAAK,QAAME,IAAE,KAAK,QAAMC,KAAE,KAAK,SAAOE,GAAE,OAAOH,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcE,KAAE;AAAC,UAAMC,MAAE,KAAK,cAAcD,GAAC;AAAE,WAAOC,QAAIL,MAAG,KAAK,aAAaI,GAAC,GAAEC;AAAA,EAAC;AAAA,EAAC,UAAUD,KAAEC,KAAE;AAAC,WAAOD,MAAEC,MAAE,KAAK;AAAA,EAAK;AAAA,EAAC,IAAID,KAAE;AAAC,WAAO,KAAK,OAAO,IAAIA,GAAC;AAAA,EAAC;AAAA,EAAC,SAASA,KAAEC,KAAE;AAAC,WAAO,KAAK,OAAO,SAASD,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,IAAID,KAAE;AAAC,SAAK,OAAO,IAAIA,GAAC;AAAA,EAAC;AAAA,EAAC,SAASA,KAAEC,KAAE;AAAC,SAAK,OAAO,SAASD,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,SAASA,KAAEP,KAAEK,KAAEG,KAAE;AAAC,UAAMC,MAAEF,MAAEF,MAAE,GAAEK,MAAEV,MAAEQ,MAAE,GAAEG,KAAEF,MAAEJ,KAAEO,KAAEF,MAAEF;AAAE,QAAGG,KAAE,KAAGC,KAAE,KAAGH,MAAE,KAAK,UAAQC,MAAE,KAAK,QAAQ,QAAOP;AAAE,UAAMU,KAAEH,GAAE,KAAK,MAAMD,MAAER,EAAC,GAAE,GAAE,KAAK,KAAK,GAAEa,KAAEJ,GAAE,KAAK,MAAMA,MAAET,EAAC,GAAE,GAAE,KAAK,KAAK,GAAEc,KAAEL,GAAE,KAAK,KAAKC,KAAEV,EAAC,GAAE,GAAE,KAAK,KAAK,GAAEe,KAAEN,GAAE,KAAK,KAAKE,KAAEX,EAAC,GAAE,GAAE,KAAK,KAAK;AAAE,aAAQK,MAAEQ,IAAER,OAAGU,IAAEV,MAAI,UAAQC,MAAEM,IAAEN,OAAGQ,IAAER,OAAI;AAAC,YAAMP,MAAE,KAAK,UAAUO,KAAED,GAAC;AAAE,UAAG,KAAK,IAAIN,GAAC,EAAE,QAAOI;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAA,EAAC,MAAMK,KAAEP,KAAEE,KAAEC,IAAEC,KAAE;AAAC,UAAMC,MAAEE,MAAEL,MAAE,GAAEM,MAAER,MAAEG,KAAE,GAAEM,MAAEJ,MAAEH,KAAEQ,MAAEF,MAAEL,IAAEQ,KAAED,GAAE,KAAK,MAAML,MAAEJ,EAAC,GAAE,GAAE,KAAK,KAAK,GAAEW,KAAEF,GAAE,KAAK,MAAMF,MAAEP,EAAC,GAAE,GAAE,KAAK,KAAK,GAAEY,KAAEH,GAAE,KAAK,KAAKD,MAAER,EAAC,GAAE,GAAE,KAAK,KAAK,GAAEa,KAAEJ,GAAE,KAAK,KAAKA,MAAET,EAAC,GAAE,GAAE,KAAK,KAAK;AAAE,aAAQK,MAAEM,IAAEN,OAAGQ,IAAER,MAAI,UAAQC,MAAEI,IAAEJ,OAAGM,IAAEN,OAAI;AAAC,YAAMP,MAAE,KAAK,UAAUO,KAAED,GAAC;AAAE,WAAK,UAAU,IAAIN,KAAEI,GAAC,GAAE,KAAK,IAAIJ,GAAC;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,cAAcM,KAAE;AAAC,UAAMC,MAAED,IAAE;AAAG,QAAIL,KAAE,GAAEI,MAAE;AAAE,IAAAC,IAAE,KAAK;AAAE,OAAE;AAAC,YAAMC,MAAED,IAAE;AAAY,MAAAL,MAAGM;AAAE,eAAQN,KAAE,GAAEA,KAAEM,KAAEN,MAAI;AAAC,cAAMM,MAAED,IAAE,sBAAsBL,EAAC,GAAEC,MAAEI,IAAE,sBAAsBL,EAAC,GAAEO,OAAGF,IAAE,YAAYL,EAAC,IAAED,OAAG,KAAK,aAAYS,OAAGH,IAAE,aAAaL,EAAC,IAAED,OAAG,KAAK;AAAY,gBAAO,KAAK,SAASO,KAAEL,KAAEM,KAAEC,GAAC,GAAE;AAAA,UAAC,KAAKL;AAAE,mBAAOA;AAAA,UAAE,KAAKD;AAAE,YAAAE;AAAA,QAAG;AAAA,MAAC;AAAA,IAAC,SAAOC,IAAE,OAAO,MAAIC,OAAGD,IAAE,KAAK;AAAG,WAAOA,IAAE,QAAQ,GAAEL,OAAII,MAAEF,KAAED;AAAA,EAAC;AAAA,EAAC,aAAaI,KAAE;AAAC,UAAMC,MAAED,IAAE;AAAG,IAAAA,IAAE,KAAK;AAAE,OAAE;AAAC,YAAMC,MAAED,IAAE;AAAY,eAAQL,KAAE,GAAEA,KAAEM,KAAEN,MAAI;AAAC,cAAMM,MAAED,IAAE,sBAAsBL,EAAC,GAAEC,MAAEI,IAAE,sBAAsBL,EAAC,GAAEE,MAAGG,IAAE,YAAYL,EAAC,IAAED,OAAG,KAAK,aAAYI,OAAGE,IAAE,aAAaL,EAAC,IAAED,OAAG,KAAK;AAAY,aAAK,MAAMO,KAAEL,KAAEC,IAAEC,KAAEE,IAAE,EAAE;AAAA,MAAC;AAAA,IAAC,SAAOA,IAAE,OAAO,MAAIC,OAAGD,IAAE,KAAK;AAAG,IAAAA,IAAE,QAAQ;AAAA,EAAC;AAAC;;;ACA3rD,IAAMW,MAAE,KAAK;AAAG,SAASC,GAAEC,KAAEC,KAAE;AAAC,UAAOA,IAAE,oBAAmB;AAAA,IAAC,KAAKF,GAAE;AAAS,aAAOG,GAAEF,KAAEC,GAAC;AAAA,IAAE,KAAKF,GAAE;AAAS,aAAOI,GAAEF,KAAED,GAAC;AAAA,IAAE,KAAKD,GAAE;AAAc,aAAOK,GAAEJ,KAAEC,GAAC;AAAA,IAAE,KAAKF,GAAE;AAAa,aAAOM,GAAEL,KAAEC,GAAC;AAAA,IAAE,KAAKF,GAAE;AAAM,aAAOO,GAAEN,KAAEC,GAAC;AAAA,IAAE,KAAKF,GAAE;AAAc,aAAOQ,GAAEP,KAAEC,GAAC;AAAA,IAAE,KAAKF,GAAE;AAAS,aAAOC;AAAA,IAAE,KAAKD,GAAE;AAAQ,aAAO;AAAA,EAAI;AAAC;AAAC,SAASS,GAAER,KAAEC,KAAE;AAAC,SAAM,YAAU,OAAOD,MAAEA,MAAED,GAAEE,KAAED,GAAC;AAAC;AAAC,SAASE,GAAEF,KAAEC,KAAE;AAAC,SAAOD,OAAGQ,GAAEP,IAAE,SAAQD,GAAC,KAAGC,IAAE;AAAa;AAAC,SAASE,GAAEH,KAAEC,KAAE;AAAC,QAAMQ,MAAET,IAAE;AAAM,MAAIF,MAAEW,OAAGA,IAAE,UAAQA,IAAE,CAAC,EAAE;AAAK,SAAO,QAAMX,QAAIA,MAAEE,IAAE,UAASQ,GAAEV,KAAEG,GAAC;AAAC;AAAC,SAASG,GAAEJ,KAAEC,KAAE;AAAC,QAAMQ,MAAER,IAAE,cAAaH,MAAEG,IAAE,cAAaF,MAAGC,MAAES,QAAIX,MAAEW,MAAGP,MAAEM,GAAEP,IAAE,SAAQD,GAAC,GAAEG,MAAEK,GAAEP,IAAE,SAAQD,GAAC;AAAE,SAAOA,OAAGS,MAAEP,MAAEF,OAAGF,MAAEK,MAAED,MAAEH,MAAGI,MAAED;AAAE;AAAC,SAASG,GAAEJ,KAAEQ,KAAE;AAAC,QAAMX,MAAEG,MAAEQ,IAAE,cAAaV,KAAES,GAAEC,IAAE,SAAQR,GAAC,GAAEC,MAAEM,GAAEC,IAAE,SAAQR,GAAC;AAAE,MAAIE,MAAE;AAAK,SAAOA,MAAEL,MAAEC,IAAES,GAAEL,KAAEJ,IAAEG,GAAC;AAAC;AAAC,SAASI,GAAEN,KAAEC,KAAE;AAAC,QAAK,CAACQ,KAAEX,KAAEC,EAAC,IAAEW,GAAEV,KAAEC,IAAE,MAAM,MAAM;AAAE,MAAGQ,QAAIX,IAAE,QAAOU,GAAEP,IAAE,MAAMQ,GAAC,EAAE,MAAKT,GAAC;AAAE;AAAC,UAAME,MAAEM,GAAEP,IAAE,MAAMQ,GAAC,EAAE,MAAKT,GAAC;AAAE,WAAOE,OAAGM,GAAEP,IAAE,MAAMH,GAAC,EAAE,MAAKE,GAAC,IAAEE,OAAGH;AAAA,EAAC;AAAC;AAAC,SAASQ,GAAEE,KAAEV,IAAE;AAAC,QAAMG,MAAE,EAAEH,GAAE,SAAS,GAAEI,MAAEK,GAAET,GAAE,SAAQU,GAAC,GAAEL,KAAEI,GAAET,GAAE,SAAQU,GAAC,GAAE,EAAC,qBAAoBJ,IAAC,IAAEN;AAAE,MAAIO,MAAE;AAAK,SAAOA,MAAE,WAASD,MAAE,IAAE,KAAK,KAAKI,MAAEX,GAAC,IAAEI,MAAE,aAAWG,OAAG,eAAaA,MAAE,IAAEI,MAAEP,MAAEO,MAAEP,KAAEM,GAAEF,KAAEH,KAAEC,EAAC;AAAC;AAAC,SAASM,GAAEV,KAAEC,KAAE;AAAC,MAAG,CAACA,IAAE;AAAO,MAAIQ,MAAE,GAAEX,MAAEG,IAAE,SAAO;AAAE,SAAOA,IAAE,KAAM,CAACA,KAAEF,OAAIC,MAAEC,OAAGH,MAAEC,IAAE,SAAKU,MAAEV,IAAE,MAAI,GAAE,CAACU,KAAEX,MAAGE,MAAEC,IAAEQ,GAAC,MAAIR,IAAEH,GAAC,IAAEG,IAAEQ,GAAC,EAAE;AAAC;;;ACArkC,IAAME,KAAE;AAAR,IAAYC,KAAE;AAAd,IAAkBC,KAAE;AAAE,SAASC,GAAEC,KAAEC,IAAE;AAAC,QAAMC,MAAE,CAAC;AAAE,EAAAF,IAAE,YAAa,CAAAA,QAAGE,IAAE,KAAKF,GAAC,CAAE,GAAEE,IAAE,KAAM,CAACF,KAAEG,QAAIH,IAAE,aAAWG,IAAE,UAAW,GAAED,IAAE,QAAS,CAAAF,QAAG;AAAC,MAAEA,IAAE,YAAY,KAAGA,IAAE,WAASC,GAAED,KAAEA,IAAE,aAAa,UAAU,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAASI,GAAEJ,KAAE;AAAC,SAAOA,IAAE,UAAQ,cAAYA,IAAE,MAAM,QAAM,UAAQA,IAAE,MAAM,QAAM,cAAYA,IAAE,MAAM,QAAM,kBAAgBA,IAAE,MAAM,QAAM,aAAWA,IAAE,MAAM,QAAM,oBAAkBA,IAAE,MAAM,QAAM,UAAQA,IAAE,MAAM;AAAK;AAAC,SAASK,GAAEL,KAAE;AAAC,SAAO,CAAAG,QAAG,EAAEF,GAAEE,KAAEH,GAAC,CAAC;AAAC;AAAC,SAASM,GAAEN,KAAE;AAAC,QAAMG,MAAE,QAAMH,OAAG,qBAAoBA,OAAGA,IAAE;AAAgB,MAAG,CAACG,IAAE,QAAO;AAAK,aAAUF,MAAKE,IAAE,KAAG,WAASF,GAAE,KAAK,QAAOI,GAAEJ,EAAC;AAAE,SAAO;AAAI;AAAC,SAASM,GAAEP,KAAE;AAAC,aAAUG,OAAKH,KAAE;AAAC,UAAMA,MAAE,sBAAqBG,OAAGA,IAAE,oBAAkB,kBAAiBA,IAAE,mBAAiBA,IAAE,mBAAiB,QAAOF,KAAE,CAAC,GAAGE,IAAE,gBAAc,CAAC,GAAE,IAAGH,OAAA,gBAAAA,IAAG,iBAAc,CAAC,CAAC;AAAE,QAAG,CAACG,IAAE,iBAAe,CAACF,GAAE,OAAO;AAAS,QAAGA,GAAE,KAAM,CAAAD,QAAG,WAASA,IAAE,qBAAsB,EAAE,QAAM;AAAA,EAAE;AAAC,SAAM;AAAE;AAAC,SAASQ,GAAEL,KAAEF,IAAE;AAJxwC;AAIywC,MAAG,CAACG,GAAEH,EAAC,EAAE;AAAO,QAAMC,MAAE,oBAAkBD,GAAE,MAAM,OAAKA,GAAE,MAAM,UAAU,QAAM,CAACA,GAAE,KAAK,GAAEQ,MAAER,GAAE,MAAM,cAAaS,MAAE,CAACH,GAAEL,GAAC,GAAES,MAAE,CAAC;AAAE,MAAG,oBAAkBV,GAAE,MAAM,MAAK;AAAC,QAAG,gBAAY,KAAAA,GAAE,iBAAF,mBAAgB,MAAK;AAAO,UAAMD,MAAEM,GAAEL,GAAE,MAAM,QAAQ;AAAE,IAAAU,IAAE,CAAC,IAAEX;AAAA,EAAC;AAAC,QAAMY,MAAEX,GAAE;AAAa,MAAG,EAAEW,GAAC,EAAE;AAAO,QAAMC,MAAEZ,GAAE,MAAM,WAAS,CAACA,GAAE;AAAU,EAAAE,IAAE,KAAK,EAAC,cAAaS,KAAE,cAAaD,KAAE,sBAAqBD,KAAE,cAAaD,KAAE,SAAQI,IAAC,CAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,IAAId,KAAEG,KAAEF,IAAE;AAAC,UAAMC,MAAE,CAAC;AAAE,aAAQO,MAAET,IAAE,SAAO,GAAES,OAAG,GAAEA,OAAI;AAAC,MAAAD,GAAEN,KAAEF,IAAES,GAAC,CAAC;AAAA,IAAC;AAAC,SAAK,kBAAkBP,GAAC,GAAE,KAAK,cAAcA,KAAEC,KAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,KAAEG,KAAEF,IAAE;AAAC,UAAK,CAACC,KAAEO,GAAC,IAAEN,IAAE,MAAM,MAAKQ,MAAE,IAAIC,GAAEV,KAAEO,KAAEN,IAAE,UAAU;AAAE,eAAS,EAAC,cAAaO,KAAE,sBAAqBE,KAAE,SAAQC,IAAC,KAAIb,KAAE;AAAC,YAAMA,MAAEU,IAAE,aAAa;AAAc,MAAAE,MAAEC,OAAG,KAAK,SAASH,GAAC,GAAE,KAAK,gBAAgBC,KAAED,KAAET,EAAC,GAAE,KAAK,kBAAkBU,KAAED,GAAC,KAAGX,GAAEW,KAAG,CAACP,KAAEF,OAAI;AAAC,eAAKA,GAAE,OAAO,IAAG,CAAAD,IAAE,gBAAgBC,GAAE,IAAGJ,EAAC;AAAA,MAAC,CAAE,IAAEE,GAAEW,KAAG,CAACP,KAAEF,OAAI;AAAC,eAAKA,GAAE,OAAO,IAAG,CAAAD,IAAE,gBAAgBC,GAAE,IAAGH,EAAC,GAAEe,OAAGF,IAAE,cAAcV,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAYE,KAAEF,IAAES,KAAE;AAAC,UAAMC,MAAEV,GAAE,eAAeE,GAAC,GAAES,MAAE,CAACF,IAAE,aAAW,CAAC,EAAEC,MAAEI,KAAGF,MAAE,EAAEH,IAAE,aAAa,KAAGA,IAAE,cAAc,yBAAuB,CAAC,EAAEC,MAAE;AAAG,WAAM,EAAEC,OAAGC;AAAA,EAAE;AAAA,EAAC,SAASb,KAAE;AAAC,UAAMG,MAAEH,IAAE,aAAa,eAAcC,KAAE,oBAAI;AAAI,IAAAF,GAAEC,KAAG,CAACE,KAAEO,QAAI;AAAC,aAAKA,IAAE,OAAO,KAAG;AAAC,YAAGR,GAAE,IAAIQ,IAAE,EAAE,EAAE;AAAS,YAAGR,GAAE,IAAIQ,IAAE,EAAE,GAAE,KAAK,YAAYA,IAAE,IAAGN,KAAEH,IAAE,SAAS,GAAE;AAAC,UAAAG,IAAE,gBAAgBM,IAAE,IAAGb,EAAC;AAAE;AAAA,QAAQ;AAAC,QAAAO,IAAE,gBAAgBM,IAAE,EAAE,MAAIX,KAAEK,IAAE,gBAAgBM,IAAE,IAAGZ,EAAC,IAAEM,IAAE,gBAAgBM,IAAE,IAAGX,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBE,KAAEG,KAAEF,IAAE;AAAC,UAAMC,MAAEC,IAAE,aAAa,eAAcM,MAAE,oBAAI;AAAI,IAAAV,GAAEI,KAAG,CAACA,KAAEO,QAAI;AAAC,aAAKA,IAAE,OAAO,IAAG,KAAG,CAACD,IAAE,IAAIC,IAAE,EAAE,EAAE,KAAGP,IAAE,IAAI,UAAQF,IAAE;AAAC,YAAG,MAAIC,IAAE,gBAAgBQ,IAAE,EAAE,GAAE;AAAC,kBAAOV,IAAE,cAAcU,GAAC,GAAE;AAAA,YAAC,KAAKH;AAAE;AAAA,YAAM,KAAKE;AAAE,cAAAP,IAAE,gBAAgBQ,IAAE,IAAGd,EAAC,GAAEa,IAAE,IAAIC,IAAE,EAAE;AAAE;AAAA,YAAM,KAAKR;AAAE,cAAAA,IAAE,gBAAgBQ,IAAE,IAAGZ,EAAC,GAAEW,IAAE,IAAIC,IAAE,EAAE;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,MAAM,CAAAR,IAAE,gBAAgBQ,IAAE,IAAGd,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBI,KAAEG,KAAE;AAAC,UAAMF,KAAEE,IAAE,aAAa,eAAcD,MAAE,oBAAI;AAAI,IAAAH,GAAEI,KAAG,CAACA,KAAEM,QAAI;AAAC,aAAKA,IAAE,OAAO,IAAG,KAAG,CAACP,IAAE,IAAIO,IAAE,EAAE,KAAGR,GAAE,gBAAgBQ,IAAE,EAAE,MAAIZ,IAAE;AAAC,gBAAOG,IAAE,cAAcS,GAAC,GAAE;AAAA,UAAC,KAAKF;AAAE;AAAA,UAAM,KAAKE;AAAE,YAAAR,GAAE,gBAAgBQ,IAAE,IAAGZ,EAAC,GAAEK,IAAE,IAAIO,IAAE,EAAE;AAAE;AAAA,UAAM,KAAKP;AAAE,YAAAD,GAAE,gBAAgBQ,IAAE,IAAGX,EAAC,GAAEI,IAAE,IAAIO,IAAE,EAAE;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBT,KAAE;AAAC,eAAS,EAAC,cAAaC,IAAE,cAAaC,KAAE,cAAaO,IAAC,KAAIT,IAAE,CAAAD,GAAEE,IAAG,CAACD,KAAEU,QAAI;AAAC,YAAMC,MAAEV,GAAE,aAAa,eAAcW,MAAEZ,IAAE,WAAW;AAAW,MAAAY,IAAE,CAAC,IAAE,KAAK,MAAMA,IAAE,CAAC,CAAC,GAAEA,IAAE,CAAC,IAAE,KAAK,MAAMA,IAAE,CAAC,CAAC;AAAE,YAAMC,MAAE,eAAaX;AAAE,aAAKQ,IAAE,KAAK,KAAG;AAAC,cAAMV,MAAEU,IAAE,aAAYT,KAAES,IAAE,SAAQR,MAAEQ,IAAE;AAAQ,YAAIM,KAAEN,IAAE;AAAK,cAAMd,MAAEa,IAAE,CAAC;AAAE,YAAG,EAAEb,GAAC,GAAE;AAAC,gBAAMI,MAAEJ,IAAEe,IAAE,UAAUD,IAAE,EAAE,CAAC;AAAE,UAAAM,KAAE,MAAMhB,GAAC,KAAG,QAAMA,OAAGA,QAAI,IAAE,IAAEgB,KAAEhB;AAAA,QAAC;AAAC,cAAMH,KAAEa,IAAE,cAAYM,KAAE,IAAGlB,KAAEY,IAAE,cAAYM,KAAE;AAAG,iBAAQb,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,cAAIH,MAAEC,IAAEQ,MAAEC,IAAE;AAAQ,cAAGG,KAAE;AAAC,gBAAIZ,MAAED,MAAEU,IAAE,QAAQP,GAAC,IAAEN,IAAEK,MAAEO,MAAEC,IAAE,QAAQP,GAAC,IAAEL;AAAE,YAAAG,MAAEW,IAAE,CAAC,IAAEX,MAAEW,IAAE,CAAC,IAAEV,MAAEU,IAAE,CAAC,GAAEV,MAAEU,IAAE,CAAC,IAAEX,MAAEW,IAAE,CAAC,IAAEV,MAAEU,IAAE,CAAC,GAAEF,IAAE,yBAAyBP,KAAE,KAAK,MAAMF,GAAC,CAAC,GAAES,IAAE,yBAAyBP,KAAE,KAAK,MAAMD,GAAC,CAAC;AAAA,UAAC,OAAK;AAAC,YAAAF,MAAEY,IAAE,CAAC,IAAEX,KAAEW,IAAE,CAAC,IAAEV,MAAEU,IAAE,CAAC,GAAEH,MAAEG,IAAE,CAAC,IAAEX,KAAEW,IAAE,CAAC,IAAEV,MAAEU,IAAE,CAAC;AAAE,kBAAMD,MAAEX,MAAEU,IAAE,QAAQP,GAAC,IAAEN,IAAEgB,MAAEJ,MAAEC,IAAE,QAAQP,GAAC,IAAEL;AAAE,YAAAY,IAAE,yBAAyBP,KAAEQ,GAAC,GAAED,IAAE,yBAAyBP,KAAEU,GAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;;;ACAx2G,IAAMI,KAAE;AAAG,IAAIC,KAAE,cAAcC,GAAEC,EAAC,EAAE;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMA,GAAC,GAAE,KAAK,gCAA8BA,GAAE,KAAK,sBAAqBJ,IAAE,IAAI,GAAE,KAAK,eAAa,IAAG,KAAK,kBAAgB,OAAG,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,kBAAgB,IAAIK;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,kBAAgB,MAAK,KAAK,gCAA8B,EAAE,KAAK,6BAA6B;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,IAAI,sBAAsB,KAAG,QAAQ,IAAI,yBAAyB,KAAK,eAAe;AAAA,sBAA0B,KAAK,eAAe,EAAE,GAAE,KAAK;AAAA,EAAe;AAAA,EAAC,OAAOD,KAAE;AAAC,SAAK,8BAA8BA,GAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,gBAAe;AAJrvC;AAIsvC,SAAK,oBAAkB,KAAK,kBAAgB,OAAG,UAAK,SAAL,mBAAW;AAAA,EAAgB;AAAA,EAAC,cAAcA,KAAE;AAAC,SAAK,KAAK,oBAAmBA,GAAC,GAAE,KAAK,oBAAkB,KAAK,kBAAgB,OAAG,KAAK,OAAOA,GAAC;AAAA,EAAE;AAAA,EAAC,qBAAqBA,KAAE;AAAC,UAAME,MAAE,KAAK;AAAK,QAAGA,IAAE,KAAG;AAAC,YAAMC,MAAED,IAAE,qBAAqB,uBAAuBF,IAAE,MAAM,KAAK,EAAE;AAAM,WAAK,gBAAgB,IAAIE,IAAE,cAAc,OAAMF,KAAEG,GAAC;AAAA,IAAC,SAAOA,KAAE;AAAA,IAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEN,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAACC,GAAE,0CAA0C,CAAC,GAAED,EAAC;AAAE,IAAMO,KAAEP;;;ACA54C,IAAMQ,MAAE;AAAR,IAAwBC,MAAE,EAAC,WAAU,GAAGD,GAAC,eAAc,SAAQ,GAAGA,GAAC,aAAY,YAAW,GAAGA,GAAC,wBAAuB,KAAI,GAAGA,GAAC,YAAW;AAAxI,IAA0IE,KAAE,EAAC,MAAK,SAAQ,SAAQ,OAAM;AAAE,IAAIC,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMA,GAAC,GAAE,KAAK,aAAW,MAAK,KAAK,WAAS,MAAK,KAAK,mBAAiB,MAAK,KAAK,YAAU,MAAK,KAAK,OAAK,EAAC,GAAE,GAAE,GAAE,GAAE,OAAM,GAAE,QAAO,EAAC,GAAE,KAAK,SAAO,MAAK,KAAK,WAAS,MAAK,KAAK,UAAQ,KAAK,QAAQ,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,OAAK;AAAA,EAAI;AAAA,EAAC,IAAI,KAAKA,KAAE;AAAC,SAAK,YAAU,KAAK,SAAS,QAAS,CAAAA,QAAG;AAAC,MAAAA,IAAE,OAAO;AAAA,IAAC,CAAE,GAAE,KAAK,WAAS,MAAK,KAAK,gBAAgB,GAAE,KAAK,KAAK,QAAOA,GAAC,GAAEA,QAAIA,IAAE,GAAG,QAAO,CAACH,GAAE,IAAI,GAAG,CAAAG,QAAG,KAAK,YAAYA,KAAE,CAAC,GAAGC,GAAE,QAAQ,GAAED,IAAE,GAAG,QAAO,CAACH,GAAE,MAAKA,GAAE,OAAO,GAAG,CAAAG,QAAG,KAAK,YAAYA,KAAE,EAAE,GAAGC,GAAE,QAAQ;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,SAAK,iBAAiB,GAAE,KAAK,eAAe,GAAE,KAAK,WAAW,MAAM;AAAA,EAAC;AAAA,EAAC,QAAQD,KAAEE,KAAEC,IAAEC,KAAE;AAAC,SAAK,KAAK,IAAEJ,KAAE,KAAK,KAAK,IAAEE,KAAE,KAAK,KAAK,QAAMC,IAAE,KAAK,KAAK,SAAOC,KAAE,KAAK,WAAS,KAAK,SAAO,sBAAsB,KAAK,OAAO;AAAA,EAAE;AAAA,EAAC,KAAKJ,KAAEE,KAAEE,KAAEC,KAAEC,KAAE;AAAC,UAAMX,MAAE,KAAK,MAAKC,MAAED,IAAE,MAAM,EAAEK,MAAE,MAAGI,KAAEF,MAAE,MAAGG,GAAC,CAAC;AAAE,QAAIR,KAAE,KAAK,IAAIO,MAAET,IAAE,OAAMU,MAAEV,IAAE,MAAM;AAAE,WAAKW,QAAIT,KAAE,IAAEA,KAAG,KAAK,gBAAgB,GAAE,KAAK,WAAW,IAAI,GAAEF,IAAE,KAAK,EAAC,QAAOC,KAAE,OAAMD,IAAE,QAAME,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWG,KAAEE,KAAEC,IAAEC,KAAE;AAAC,UAAMC,MAAE,KAAK;AAAU,IAAAA,IAAE,eAAe,MAAK,KAAI,KAAGL,GAAC,GAAEK,IAAE,eAAe,MAAK,KAAI,KAAGH,GAAC,GAAEG,IAAE,eAAe,MAAK,SAAQ,KAAGF,EAAC,GAAEE,IAAE,eAAe,MAAK,UAAS,KAAGD,GAAC,GAAEC,IAAE,eAAe,MAAK,SAAQT,IAAE,GAAG;AAAA,EAAC;AAAA,EAAC,kBAAkBI,KAAEE,KAAEC,IAAEC,KAAE;AAAC,SAAK,iBAAiB,eAAe,MAAK,KAAI,KAAK,WAAWJ,KAAEE,KAAEC,IAAEC,KAAE,KAAK,KAAK,OAAM,KAAK,KAAK,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,UAAMJ,MAAE,SAAS,cAAc,KAAK;AAAE,IAAAA,IAAE,YAAUJ,IAAE,WAAU,KAAK,KAAK,KAAK,YAAYI,GAAC,GAAE,KAAK,aAAWA;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,UAAMA,MAAE,KAAK,KAAK,OAAME,MAAE,KAAK,KAAK,QAAOC,KAAE,SAAS,gBAAgB,8BAA6B,MAAM;AAAE,IAAAA,GAAE,eAAe,MAAK,KAAI,aAAWH,MAAE,UAAQA,MAAE,MAAIE,MAAE,UAAQA,MAAE,IAAI,GAAEC,GAAE,eAAe,MAAK,SAAQP,IAAE,UAAU;AAAE,UAAMQ,MAAE,SAAS,gBAAgB,8BAA6B,MAAM,GAAEC,MAAE,SAAS,gBAAgB,8BAA6B,KAAK;AAAE,IAAAA,IAAE,eAAe,iCAAgC,eAAc,8BAA8B,GAAEA,IAAE,eAAe,MAAK,SAAQT,IAAE,OAAO,GAAES,IAAE,YAAYF,EAAC,GAAEE,IAAE,YAAYD,GAAC,GAAE,KAAK,WAAW,YAAYC,GAAC,GAAE,KAAK,mBAAiBF,IAAE,KAAK,YAAUC,KAAE,KAAK,WAASC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,SAAK,cAAY,KAAK,WAAW,cAAY,KAAK,WAAW,WAAW,YAAY,KAAK,UAAU,GAAE,KAAK,aAAW,KAAK,mBAAiB,KAAK,YAAU,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,WAAWL,KAAEE,KAAEC,IAAEC,KAAEC,KAAEC,KAAE;AAAC,UAAMX,MAAEK,MAAEG,IAAEP,MAAEM,MAAEE;AAAE,WAAM,aAAWC,MAAE,UAAQA,MAAE,MAAIC,MAAE,UAAQA,MAAE,SAAON,MAAE,MAAIE,MAAE,QAAMF,MAAE,MAAIJ,MAAE,QAAMD,MAAE,MAAIC,MAAE,QAAMD,MAAE,MAAIO,MAAE;AAAA,EAAI;AAAA,EAAC,YAAYF,KAAEE,KAAE;AAAC,UAAMC,KAAEH,IAAE,GAAEI,MAAEJ,IAAE,GAAEK,MAAEL,IAAE,OAAO,GAAEM,MAAEN,IAAE,OAAO;AAAE,QAAIL,KAAEC,KAAEC,IAAEC;AAAE,YAAOK,KAAEE,OAAGV,MAAEU,KAAER,KAAEM,KAAEE,QAAIV,MAAEQ,IAAEN,KAAEQ,MAAEF,KAAGC,MAAEE,OAAGV,MAAEU,KAAER,MAAEM,MAAEE,QAAIV,MAAEQ,KAAEN,MAAEQ,MAAEF,MAAGJ,IAAE,QAAO;AAAA,MAAC,KAAI;AAAQ,aAAK,OAAO;AAAE;AAAA,MAAM,KAAI;AAAS,aAAK,QAAQL,KAAEC,KAAEC,IAAEC,GAAC;AAAE;AAAA,MAAM,KAAI;AAAM,aAAK,KAAKH,KAAEC,KAAEC,IAAEC,KAAEI,GAAC;AAAA,IAAC;AAAC,IAAAF,IAAE,gBAAgB;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,CAAC,KAAK,OAAO;AAAO,QAAG,KAAK,SAAO,MAAK,CAAC,KAAK,SAAS;AAAO,UAAK,EAAC,GAAEA,KAAE,GAAEE,KAAE,OAAMC,IAAE,QAAOC,IAAC,IAAE,KAAK;AAAK,SAAK,WAAWJ,KAAEE,KAAEC,IAAEC,GAAC,GAAE,KAAK,kBAAkBJ,KAAEE,KAAEC,IAAEC,GAAC,GAAE,KAAK,SAAO,sBAAsB,KAAK,OAAO;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEN,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,IAAI,GAAEA,KAAE,EAAE,CAACH,GAAE,kCAAkC,CAAC,GAAEG,EAAC;AAAE,IAAMS,KAAET;;;ACA/kH,IAAMU,KAAN,MAAO;AAAA,EAAC,YAAYA,KAAE;AAAC,SAAK,QAAMA,KAAE,KAAK,YAAU,QAAO,KAAK,gBAAc;AAAA,EAAM;AAAA,EAAC,OAAOA,KAAE;AAAC,QAAG,KAAK,aAAa,GAAE;AAAC,YAAMC,MAAE,KAAK,aAAaD,GAAC;AAAE,WAAK,aAAaC,GAAC;AAAA,IAAC;AAAC,SAAK,YAAUD;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,YAAU,QAAO,KAAK,gBAAc;AAAA,EAAM;AAAA,EAAC,eAAc;AAAC,WAAO,WAAS,KAAK;AAAA,EAAS;AAAA,EAAC,mBAAkB;AAAC,WAAO,WAAS,KAAK;AAAA,EAAa;AAAA,EAAC,aAAaA,KAAE;AAAC,WAAO,WAAS,KAAK,YAAU,MAAIA,MAAE,KAAK;AAAA,EAAS;AAAA,EAAC,aAAaA,KAAE;AAAC,eAAS,KAAK,gBAAc,KAAK,iBAAe,IAAE,KAAK,SAAO,KAAK,gBAAc,KAAK,QAAMA,MAAE,KAAK,gBAAcA;AAAA,EAAC;AAAC;;;ACA5hB,IAAME,KAAN,MAAO;AAAA,EAAC,YAAYA,KAAEC,IAAEC,KAAE;AAAC,SAAK,mBAAiBF,KAAE,KAAK,gBAAcC,IAAE,KAAK,YAAUC,KAAE,KAAK,YAAU,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,gBAAgB,IAAE,KAAK,aAAa,IAAE,KAAK,IAAI,IAAE,KAAK,SAAS,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,WAAWF,KAAE;AAAC,WAAOA,MAAE,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,MAAMA,KAAE;AAAC,WAAO,KAAK,yBAAyB,KAAK,kBAAiBA,GAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,KAAEC,IAAE;AAAC,UAAMC,MAAE,KAAK,MAAMF,GAAC;AAAE,WAAO,KAAK,MAAMA,MAAEC,EAAC,IAAEC;AAAA,EAAC;AAAA,EAAC,yBAAyBF,KAAEC,IAAE;AAAC,IAAAA,KAAE,KAAK,IAAIA,IAAE,KAAK,QAAQ;AAAE,UAAMC,MAAE,IAAE,KAAK;AAAS,WAAOF,OAAGE,OAAGD,KAAE,KAAG,KAAK,IAAIC,GAAC;AAAA,EAAC;AAAC;;;ACA1V,IAAMC,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,KAAED,KAAEE,IAAEC,KAAEC,KAAE;AAAC,UAAMH,KAAED,KAAEE,EAAC,GAAE,KAAK,iBAAeC,KAAE,KAAK,YAAUC;AAAA,EAAC;AAAA,EAAC,MAAMH,KAAE;AAAC,WAAO,MAAM,yBAAyB,KAAK,gBAAeA,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMI,KAAN,MAAO;AAAA,EAAC,YAAYJ,MAAE,KAAID,MAAE,IAAGE,KAAE,MAAI;AAAC,SAAK,0BAAwBD,KAAE,KAAK,gBAAcD,KAAE,KAAK,YAAUE,IAAE,KAAK,UAAQ,MAAG,KAAK,QAAM,IAAIF,GAAE,GAAE,GAAE,KAAK,UAAQ,CAAC,IAAIA,GAAE,GAAE,GAAE,IAAIA,GAAE,GAAE,CAAC,GAAE,KAAK,SAAO,CAAC,IAAIA,GAAE,GAAE,GAAE,IAAIA,GAAE,GAAE,GAAE,IAAIA,GAAE,GAAE,CAAC,GAAE,KAAK,gBAAcI,GAAE;AAAA,EAAC;AAAA,EAAC,IAAIH,KAAED,KAAEE,IAAE;AAAC,QAAG,KAAK,SAAQ;AAAC,UAAG,KAAK,MAAM,aAAa,GAAE;AAAC,YAAG,KAAK,MAAM,aAAaA,EAAC,IAAE,MAAK;AAAA,MAAM;AAAC,WAAK,QAAQ,CAAC,EAAE,OAAOD,IAAE,CAAC,CAAC,GAAE,KAAK,QAAQ,CAAC,EAAE,OAAOA,IAAE,CAAC,CAAC,GAAE,KAAK,OAAO,CAAC,EAAE,OAAOD,IAAE,CAAC,CAAC,GAAE,KAAK,OAAO,CAAC,EAAE,OAAOA,IAAE,CAAC,CAAC,GAAE,KAAK,OAAO,CAAC,EAAE,OAAOA,IAAE,CAAC,CAAC,GAAE,KAAK,MAAM,OAAOE,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,QAAQ,CAAC,EAAE,MAAM,GAAE,KAAK,QAAQ,CAAC,EAAE,MAAM,GAAE,KAAK,OAAO,CAAC,EAAE,MAAM,GAAE,KAAK,OAAO,CAAC,EAAE,MAAM,GAAE,KAAK,OAAO,CAAC,EAAE,MAAM,GAAE,KAAK,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,QAAG,CAAC,KAAK,WAAS,CAAC,KAAK,QAAQ,CAAC,EAAE,iBAAiB,KAAG,CAAC,KAAK,MAAM,iBAAiB,EAAE,QAAO;AAAK,UAAMD,MAAE,KAAK,QAAQ,CAAC,EAAE,eAAcD,MAAE,KAAK,QAAQ,CAAC,EAAE,eAAcE,KAAE,QAAMD,OAAG,QAAMD,MAAE,IAAE,KAAK,KAAKC,MAAEA,MAAED,MAAEA,GAAC,GAAEG,MAAE,KAAK,MAAM,eAAcC,MAAE,QAAMD,OAAG,QAAMD,KAAE,IAAEA,KAAEC;AAAE,WAAO,KAAK,IAAIC,GAAC,IAAE,KAAK,0BAAwB,OAAK,KAAK,eAAeA,KAAE,KAAK,eAAc,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,eAAeD,KAAEC,KAAEE,KAAE;AAAC,MAAE,KAAK,eAAc,KAAK,OAAO,CAAC,EAAE,iBAAe,GAAE,KAAK,OAAO,CAAC,EAAE,iBAAe,GAAE,KAAK,OAAO,CAAC,EAAE,iBAAe,CAAC;AAAE,UAAMD,MAAEF,GAAE,KAAK,aAAa;AAAE,IAAAE,MAAE,KAAG,EAAE,KAAK,eAAc,KAAK,eAAc,IAAEA,GAAC;AAAE,UAAME,KAAE,KAAK,MAAM;AAAc,WAAO,IAAIR,IAAEI,KAAEC,KAAEE,KAAE,QAAMC,KAAE,IAAEF,MAAEE,IAAE,KAAK,aAAa;AAAA,EAAC;AAAC;;;ACAt2B,IAAIC,KAAE,cAAcA,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMA,GAAC,GAAE,KAAK,gBAAc,GAAE,KAAK,oBAAkB,IAAIC,GAAE,KAAI,GAAE,IAAG,GAAE,KAAK,WAAS,MAAK,KAAK,cAAYC,GAAE,GAAE,KAAK,mBAAiB,OAAG,KAAK,YAAU,IAAIC,GAAE,EAAC,gBAAe,IAAI,KAAE,OAAM,GAAE,UAAS,EAAC,CAAC,GAAE,KAAK,gBAAc,MAAKC,GAAG,MAAI,KAAK,kBAAmB,MAAI,KAAK,WAAW,KAAK,CAAE;AAAA,EAAC;AAAA,EAAC,MAAMJ,KAAEK,IAAE;AAAC,SAAK,WAAW,MAAM,GAAE,KAAK,kBAAkB,MAAM,GAAE,KAAK,eAAeA,EAAC,GAAE,KAAK,gBAAcA;AAAA,EAAC;AAAA,EAAC,OAAOL,KAAEK,IAAE;AAAC,SAAK,eAAeA,EAAC;AAAE,QAAIC,MAAED,GAAE,OAAO,GAAEE,MAAEF,GAAE,OAAO;AAAE,UAAMG,MAAE,KAAK;AAAc,IAAAF,MAAEE,MAAEA,IAAE,OAAO,IAAEF,MAAE,CAACA,KAAEC,MAAEC,MAAED,MAAEC,IAAE,OAAO,IAAED,KAAEP,IAAE,YAAU,GAAE,KAAK,WAAUA,IAAE,WAAU,CAACM,OAAG,GAAEC,OAAG,CAAC,CAAC,GAAE,KAAK,gBAAcF;AAAA,EAAC;AAAA,EAAC,IAAIL,KAAEK,IAAE;AAAC,SAAK,eAAeA,EAAC;AAAE,UAAMC,MAAEN,IAAE,WAAW;AAAgB,SAAK,WAASM,MAAE,KAAK,kBAAkB,iBAAiB,IAAE,MAAK,KAAK,gBAAc,GAAE,KAAK,YAAU,KAAK,kBAAkBN,GAAC,GAAE,KAAK,gBAAc,MAAK,KAAK,WAAW,IAAI;AAAA,EAAC;AAAA,EAAC,eAAeA,KAAE;AAAC,UAAMK,KAAEL,IAAE,OAAO,GAAEM,MAAEN,IAAE,OAAO,GAAEO,MAAE,EAAE,CAACF,IAAEC,GAAC,GAAEJ,MAAEO,GAAE,CAACJ,IAAEC,KAAE,CAAC;AAAE,SAAK,kBAAkB,IAAIC,KAAEL,KAAE,OAAKF,IAAE,SAAS;AAAA,EAAC;AAAA,EAAC,kBAAkBA,KAAE;AAJj1D;AAIk1D,eAAK,WAAW,qBAAhB,mBAAkC,iBAAiBA,IAAE,WAAW,CAACK,IAAEC,QAAI;AAAC,YAAK,EAAC,UAASC,KAAE,eAAcC,KAAE,aAAYN,IAAC,IAAE,MAAKQ,KAAE,OAAKJ;AAAE,UAAG,EAAE,KAAK,mBAAiB,CAACC,OAAGA,IAAE,WAAWC,GAAC,IAAG;AAAC,cAAMF,MAAEC,IAAE,WAAWC,KAAEE,EAAC;AAAE,UAAER,KAAEK,IAAE,WAAUD,GAAC,GAAE,GAAED,IAAEA,IAAEH,GAAC,GAAEF,IAAE,YAAY,oBAAoBK,EAAC;AAAA,MAAC;AAAC,WAAK,iBAAeK;AAAA,IAAC;AAAA,EAAG;AAAA,EAAC,yBAAwB;AAAC,SAAK,aAAW,KAAK,kBAAkB,MAAM,GAAE,KAAK,WAAS,MAAK,KAAK,WAAW,KAAK;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEX,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAEA,KAAE,EAAE,CAACY,GAAE,sCAAsC,CAAC,GAAEZ,EAAC;AAAE,IAAMa,KAAEb;;;ACA3wE,IAAMc,MAAN,MAAO;AAAA,EAAC,YAAYC,MAAE,KAAIC,KAAE,MAAIF,MAAE,MAAIG,MAAE,IAAG;AAAC,SAAK,0BAAwBF,KAAE,KAAK,gBAAcC,IAAE,KAAK,YAAUF,KAAE,KAAK,eAAaG,KAAE,KAAK,UAAQ,MAAG,KAAK,QAAM,IAAIF,GAAE,GAAE,GAAE,KAAK,OAAK,IAAIA,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAIA,KAAEG,KAAE;AAAC,QAAG,KAAK,WAAS,QAAMA,KAAE;AAAC,UAAG,KAAK,KAAK,aAAa,GAAE;AAAC,YAAG,KAAK,KAAK,aAAaA,GAAC,IAAE,KAAI;AAAO,YAAG,KAAK,MAAM,iBAAiB,GAAE;AAAC,gBAAMA,MAAE,KAAK,MAAM,aAAaH,GAAC;AAAE,eAAK,MAAM,gBAAcG,MAAE,KAAG,KAAK,MAAM,MAAM;AAAA,QAAC;AAAA,MAAC;AAAC,WAAK,KAAK,OAAOA,GAAC,GAAE,KAAK,MAAM,OAAOH,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,MAAM,MAAM,GAAE,KAAK,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,QAAG,CAAC,KAAK,WAAS,CAAC,KAAK,MAAM,iBAAiB,KAAG,CAAC,KAAK,KAAK,iBAAiB,EAAE,QAAO;AAAK,QAAIG,MAAE,KAAK,MAAM,gBAAc,KAAK,KAAK;AAAc,WAAOA,MAAEC,GAAED,KAAE,CAAC,KAAK,cAAa,KAAK,YAAY,GAAE,KAAK,IAAIA,GAAC,IAAE,KAAK,0BAAwB,OAAK,KAAK,eAAeA,KAAE,KAAK,eAAc,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,eAAeH,KAAEG,KAAEJ,KAAE;AAAC,WAAO,IAAIC,GAAEA,KAAEG,KAAEJ,GAAC;AAAA,EAAC;AAAC;;;ACAr8B,IAAMM,MAAN,cAAgBC,IAAC;AAAA,EAAC,YAAYC,MAAE,GAAEF,MAAE,MAAIC,MAAE,MAAIE,MAAE,IAAG;AAAC,UAAMD,KAAEF,KAAEC,KAAEE,GAAC;AAAA,EAAC;AAAA,EAAC,IAAID,KAAEF,KAAE;AAAC,UAAMC,MAAE,KAAK,MAAM;AAAU,QAAG,QAAMA,KAAE;AAAC,UAAID,MAAEE,MAAED;AAAE,aAAKD,MAAE,KAAK,KAAI,CAAAA,OAAG,IAAE,KAAK;AAAG,aAAKA,MAAE,CAAC,KAAK,KAAI,CAAAA,OAAG,IAAE,KAAK;AAAG,MAAAE,MAAED,MAAED;AAAA,IAAC;AAAC,UAAM,IAAIE,KAAEF,GAAC;AAAA,EAAC;AAAC;;;ACAtK,IAAMI,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,KAAED,KAAED,KAAE;AAAC,UAAME,KAAED,KAAED,GAAC;AAAA,EAAC;AAAA,EAAC,MAAME,KAAE;AAAC,UAAMD,MAAE,MAAM,MAAMC,GAAC;AAAE,WAAO,KAAK,IAAID,GAAC;AAAA,EAAC;AAAA,EAAC,WAAWC,KAAED,KAAE;AAAC,UAAMD,MAAE,MAAM,MAAME,GAAC,GAAEC,MAAE,MAAM,MAAMD,MAAED,GAAC,IAAED;AAAE,WAAO,KAAK,IAAIG,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMA,MAAN,cAAgBA,IAAC;AAAA,EAAC,YAAYD,MAAE,KAAID,MAAE,MAAID,MAAE,MAAIG,MAAE,IAAG;AAAC,UAAMD,KAAED,KAAED,KAAEG,GAAC;AAAA,EAAC;AAAA,EAAC,IAAID,KAAED,KAAE;AAAC,UAAM,IAAI,KAAK,IAAIC,GAAC,GAAED,GAAC;AAAA,EAAC;AAAA,EAAC,eAAeC,KAAED,KAAEE,KAAE;AAAC,WAAO,IAAIH,IAAEE,KAAED,KAAEE,GAAC;AAAA,EAAC;AAAC;;;ACA8d,IAAIC,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMA,GAAC,GAAE,KAAK,iBAAe,GAAE,KAAK,oBAAkB,OAAG,KAAK,iBAAe,GAAE,KAAK,kBAAgB,GAAE,KAAK,kBAAgB,MAAK,KAAK,6BAA2B,IAAIC,IAAE,KAAG,MAAI,IAAG,GAAE,KAAK,qBAAmB,GAAE,KAAK,cAAY,GAAE,KAAK,eAAa,GAAE,KAAK,mBAAiB,MAAK,KAAK,iBAAe,GAAE,KAAK,yBAAuB,IAAIC,OAAE,KAAK,YAAU,MAAK,KAAK,eAAa,MAAK,KAAK,iBAAe,MAAK,KAAK,YAAU,IAAIC,GAAE,EAAC,gBAAe,IAAI,KAAE,OAAM,GAAE,UAAS,EAAC,CAAC,GAAE,KAAK,WAAWC,GAAG,MAAI,KAAK,mBAAoB,MAAI,KAAK,WAAW,KAAK,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,MAAMJ,KAAEK,KAAE;AAAC,SAAK,WAAW,MAAM,GAAE,KAAK,2BAA2B,MAAM,GAAE,KAAK,uBAAuB,MAAM,GAAE,KAAK,YAAU,MAAK,KAAK,iBAAe,KAAK,cAAYA,IAAE,OAAM,KAAK,kBAAgB,KAAK,eAAaA,IAAE,QAAO,KAAK,kBAAgBA,IAAE,QAAO,KAAK,mBAAiB,MAAKL,IAAE,YAAY,mBAAiB,KAAK,qBAAqB,GAAEK,IAAE,SAAS,GAAE,KAAK,mBAAmBA,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOL,KAAEK,KAAE;AAAC,aAAO,KAAK,qBAAmB,KAAK,mBAAiBA,IAAE;AAAW,UAAMC,KAAED,IAAE,OAAMH,MAAEG,IAAE,QAAOE,MAAEF,IAAE,QAAOG,MAAE,KAAK,IAAI,OAAKF,KAAE,KAAK,eAAa,KAAK,EAAE,GAAEG,KAAE,KAAK,IAAIP,MAAE,KAAK,YAAY,GAAED,MAAE,KAAK,eAAaC;AAAE,QAAG,KAAK,mBAAiB,KAAK,iBAAgB;AAAC,YAAMQ,MAAER,MAAE,KAAK;AAAgB,UAAIS,KAAE,OAAKL,KAAE,KAAK,kBAAgB,KAAK;AAAG,WAAK,qBAAmBK,MAAG,IAAE,IAAE,IAAG,KAAK,iBAAeD,OAAG,IAAE,IAAE,IAAGV,IAAE,YAAY,mBAAiB,SAAO,KAAK,aAAWK,IAAE,YAAU,KAAK,mBAAiB,QAAM,KAAK,YAAUI,KAAED,MAAE,IAAG,SAAO,KAAK,aAAW,KAAK,YAAUG,KAAE,IAAE,KAAK,qBAAqBL,KAAE,KAAK,aAAYD,IAAE,SAAS,KAAGM,KAAE,GAAE,KAAK,mBAAmBN,KAAEJ,GAAC,GAAE,KAAK,WAAW,aAAa,CAACM,IAAE,GAAEA,IAAE,CAAC,GAAE,IAAEG,KAAEC,IAAE,CAAC,KAAK,gBAAgB,IAAEJ,IAAE,GAAEA,IAAE,IAAE,KAAK,gBAAgB,CAAC,CAAC;AAAA,IAAC;AAAC,SAAK,iBAAeD,IAAE,KAAK,kBAAgBJ,KAAE,KAAK,kBAAgBK;AAAA,EAAC;AAAA,EAAC,IAAIP,KAAE;AAAC,SAAK,iBAAe,KAAK,2BAA2B,iBAAiB,GAAE,KAAK,eAAa,KAAK,uBAAuB,iBAAiB,GAAE,KAAK,iBAAe,IAAG,KAAK,kBAAgB,KAAK,iBAAe,KAAK,kBAAkBA,GAAC,GAAE,KAAK,WAAW,IAAI;AAAA,EAAC;AAAA,EAAC,qBAAqBA,KAAEK,KAAE;AAAC,SAAK,2BAA2B,IAAIL,KAAE,OAAKK,GAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBL,KAAEK,KAAE;AAAC,SAAK,uBAAuB,IAAIA,KAAE,OAAKL,IAAE,SAAS;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,UAAMK,MAAEL,IAAE,OAAMM,KAAEN,IAAE,YAAY;AAAkB,WAAO,MAAIM,MAAGD,MAAEC;AAAA,EAAC;AAAA,EAAC,WAAWN,KAAE;AAAC,UAAMK,MAAEL,IAAE,OAAMM,KAAEN,IAAE,YAAY;AAAkB,WAAO,MAAIM,MAAGD,MAAEC;AAAA,EAAC;AAAA,EAAC,kBAAkBN,KAAE;AAJhsG;AAIisG,eAAK,WAAW,qBAAhB,mBAAkC,iBAAiBA,IAAE,WAAW,CAACK,KAAEC,OAAI;AAAC,YAAMJ,MAAE,CAAC,KAAK,UAAUF,GAAC,KAAG,KAAK,iBAAe,KAAG,CAAC,KAAK,WAAWA,GAAC,KAAG,KAAK,iBAAe,GAAEO,MAAE,CAAC,KAAK,kBAAgB,KAAK,eAAe,WAAW,KAAK,cAAc,GAAEC,MAAEN,OAAG,CAAC,KAAK,gBAAc,KAAK,aAAa,WAAW,KAAK,cAAc,GAAEU,KAAE,OAAKN;AAAE,UAAG,KAAK,oBAAkBC,OAAGC,KAAE,CAAC,KAAK,mBAAkB;AAAC,cAAMF,KAAE,KAAK,iBAAe,KAAK,IAAI,KAAK,eAAe,WAAW,KAAK,gBAAeM,EAAC,CAAC,IAAE,KAAK,qBAAmB,MAAI,KAAK,KAAG;AAAE,YAAIV,MAAE,KAAK,eAAa,KAAK,IAAI,KAAK,aAAa,WAAW,KAAK,gBAAeU,EAAC,CAAC,IAAE;AAAE,cAAML,MAAEC,GAAE,GAAEA,MAAEA,GAAE;AAAE,YAAG,KAAK,iBAAgB;AAAC,UAAAE,GAAEH,KAAE,KAAK,gBAAgB,GAAE,KAAK,gBAAgB,CAAC,GAAE,GAAEC,KAAER,IAAE,MAAKA,IAAE,OAAO,GAAEE,GAAEK,KAAEA,KAAEC,GAAC;AAAE,gBAAK,EAAC,aAAYE,KAAE,OAAME,GAAC,IAAEZ,KAAEa,MAAED,KAAEV;AAAE,UAAAA,MAAE,KAAG,CAACQ,IAAE,YAAYG,GAAC,KAAGX,MAAEU,KAAEF,IAAE,mBAAkB,KAAK,eAAa,MAAK,KAAK,iBAAe,QAAMR,MAAE,KAAG,CAACQ,IAAE,aAAaG,GAAC,MAAIX,MAAEU,KAAEF,IAAE,mBAAkB,KAAK,eAAa,MAAK,KAAK,iBAAe,OAAM,GAAEL,KAAEL,IAAE,WAAUE,KAAEI,IAAEC,KAAEP,IAAE,IAAI,GAAEA,IAAE,YAAY,oBAAoBK,GAAC;AAAA,QAAC;AAAA,MAAC;AAAC,WAAK,kBAAgBO;AAAA,IAAC;AAAA,EAAG;AAAA,EAAC,yBAAwB;AAAC,KAAC,KAAK,kBAAgB,KAAK,kBAAgB,KAAK,mBAAiB,KAAK,2BAA2B,MAAM,GAAE,KAAK,iBAAe,OAAM,KAAK,iBAAe,KAAK,uBAAuB,MAAM,GAAE,KAAK,eAAa,OAAM,KAAK,WAAW,KAAK;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEd,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAEA,KAAE,EAAE,CAACG,GAAE,wCAAwC,CAAC,GAAEH,EAAC;AAAE,IAAMgB,KAAEhB;;;ACA5gI,IAAMiB,KAAEC,GAAE;AAAV,IAAYC,KAAED,GAAE;AAAE,IAAIE,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMA,GAAC,GAAE,KAAK,kBAAgBJ,GAAE,GAAE,KAAK,YAAU,IAAID,GAAE,EAAC,gBAAe,IAAI,KAAE,OAAM,GAAE,UAAS,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAMK,KAAEC,KAAE;AAAC,SAAK,WAAW,MAAM,GAAEC,GAAE,KAAK,iBAAgBD,IAAE,OAAO,GAAEA,IAAE,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,KAAEC,KAAE;AAAC,UAAK,EAAC,OAAM,EAAC,MAAKE,KAAE,SAAQD,IAAC,EAAC,IAAEF;AAAE,IAAAE,GAAEP,IAAEM,IAAE,OAAO,GAAEA,IAAE,OAAO,CAAC,GAAE,EAAEJ,IAAEM,KAAED,GAAC,GAAEF,IAAE,YAAU,GAAE,KAAK,WAAUA,IAAE,MAAM,gBAAgB,WAAU,GAAEH,IAAE,KAAK,iBAAgBF,EAAC,CAAC,GAAES,GAAE,KAAK,iBAAgBT,EAAC;AAAA,EAAC;AAAA,EAAC,MAAK;AAAC,SAAK,WAAW,IAAI;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEG,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAEA,KAAE,EAAE,CAACM,GAAE,8BAA8B,CAAC,GAAEN,EAAC;AAAE,IAAMO,KAAEP;;;ACApjB,IAAMQ,KAAE;AAAR,IAAWC,KAAE;AAAb,IAAeC,KAAE,IAAIC,GAAE,EAAC,gBAAe,IAAI,IAAC,CAAC;AAA7C,IAA+CC,KAAE,CAAC,GAAE,CAAC;AAArD,IAAuDC,KAAE;AAAI,IAAIC,KAAE,cAAcN,GAAC;AAAA,EAAC,YAAYO,KAAE;AAAC,UAAMA,GAAC,GAAE,KAAK,YAAU,MAAK,KAAK,sBAAoB,MAAK,KAAK,mBAAiB,MAAK,KAAK,cAAY;AAAA,EAAE;AAAA,EAAC,aAAY;AAAC,SAAK,MAAI,IAAIF,GAAE,EAAC,YAAW,KAAI,CAAC,GAAE,KAAK,SAAO,IAAIG,GAAE,EAAC,YAAW,KAAI,CAAC,GAAE,KAAK,QAAM,IAAIH,GAAE,EAAC,YAAW,KAAI,CAAC,GAAE,KAAK,UAAQ,IAAII,GAAE,EAAC,MAAK,KAAK,MAAK,YAAW,KAAI,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,MAAI,EAAE,KAAK,GAAG,GAAE,KAAK,SAAO,EAAE,KAAK,MAAM,GAAE,KAAK,QAAM,EAAE,KAAK,KAAK,GAAE,KAAK,UAAQ,EAAE,KAAK,OAAO,GAAE,KAAK,mBAAiB;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,SAAK,KAAK,eAAc,IAAE;AAAA,EAAC;AAAA,EAAC,MAAK;AAAC,SAAK,sBAAoB,YAAY,IAAI,GAAE,KAAK,YAAYJ,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,KAAKE,KAAEG,KAAE,KAAK,kBAAkB,GAAE;AAAC,QAAG,KAAK,KAAK,GAAE,KAAK,MAAM,GAAE,KAAK,KAAK,YAAY,cAAY,KAAK,KAAK,YAAY,cAAc,QAAOH,MAAE,IAAE,KAAK,OAAOG,EAAC,IAAE,KAAK,QAAQA,EAAC;AAAE,SAAK,aAAaA,IAAEH,KAAE,GAAE,CAAC,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOA,KAAE;AAAC,UAAMG,KAAE,KAAK,MAAKC,MAAED,GAAE,YAAY,gBAAgBA,GAAE,KAAK;AAAE,WAAO,KAAK,aAAaC,KAAEJ,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQA,KAAE;AAAC,UAAMG,KAAE,KAAK,MAAKC,MAAED,GAAE,YAAY,oBAAoBA,GAAE,KAAK;AAAE,WAAO,KAAK,aAAaC,KAAEJ,GAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAEG,IAAEC,KAAEC,KAAE;AAAC,SAAK,MAAM,GAAE,KAAK,KAAK,MAAM,YAAU,KAAK,+BAA+B,KAAK,KAAK,WAAUL,KAAEG,IAAEC,KAAEC,GAAC,GAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,sBAAsBL,KAAEG,KAAE,GAAEC,MAAE,CAAC,GAAE,CAAC,GAAEC,MAAE,KAAK,kBAAkB,GAAE;AAAC,SAAK,KAAK,MAAM,YAAU,KAAK,+BAA+B,KAAK,KAAK,WAAUA,KAAEL,KAAEG,IAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,2BAA0B;AAJ3gE;AAI4gE,UAAMJ,MAAE,KAAK,IAAI,gBAAgB;AAAE,eAAK,qBAAL,mBAAuB,iBAAiBA,KAAG,CAAAA,QAAG;AAAC,SAAEA,KAAEA,KAAE,CAACN,EAAC;AAAA,IAAC;AAAA,EAAG;AAAA,EAAC,kCAAiC;AAJ5oE;AAI6oE,UAAMM,MAAE,KAAK,IAAI,gBAAgB;AAAE,eAAK,qBAAL,mBAAuB,iBAAiBA,KAAG,CAAAA,QAAG;AAAC,SAAEA,KAAEA,KAAEN,EAAC;AAAA,IAAC;AAAA,EAAG;AAAA,EAAC,gBAAe;AAAC,SAAK,KAAK,WAAS;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,eAAe,CAAC,CAACD,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,SAAK,eAAe,CAACA,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,SAAK,eAAe,CAAC,GAAEA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,eAAe,CAAC,GAAE,CAACA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAJ/8E;AAIg9E,SAAK,IAAI,uBAAuB,IAAE,UAAK,qBAAL,mBAAuB,QAAO,KAAK,IAAI,GAAE,SAAO,KAAK,cAAY,aAAa,KAAK,SAAS,GAAE,KAAK,YAAU,MAAK,KAAK,KAAK,eAAc,KAAE;AAAA,EAAE;AAAA,EAAC,eAAeO,KAAE;AAJlpF;AAImpF,UAAMG,KAAE,KAAK,KAAK;AAAU,eAAK,qBAAL,mBAAuB,iBAAiBA,IAAG,CAAAA,OAAG;AAAC,SAAEA,IAAEA,IAAEH,GAAC,GAAE,KAAK,KAAK,YAAY,oBAAoBG,EAAC;AAAA,IAAC;AAAA,EAAG;AAAA,EAAC,YAAYH,KAAE;AAAC,WAAO,SAAO,KAAK,cAAY,KAAK,YAAU,WAAY,MAAI;AAAC,WAAK,YAAU;AAAK,YAAMA,MAAE,YAAY,IAAI,KAAG,KAAK,uBAAqB;AAAG,MAAAA,MAAEF,KAAE,KAAK,YAAU,KAAK,YAAYE,GAAC,IAAE,KAAK,KAAK,eAAc,KAAE;AAAA,IAAC,GAAGA,GAAC,IAAG,KAAK;AAAA,EAAS;AAAA,EAAC,oBAAmB;AAAC,UAAK,EAAC,MAAKA,KAAE,SAAQ,EAAC,MAAKG,IAAE,OAAMC,KAAE,KAAIC,KAAE,QAAOC,IAAC,EAAC,IAAE,KAAK;AAAK,WAAOT,GAAE,CAAC,IAAE,OAAIG,IAAE,CAAC,IAAEI,MAAED,KAAGN,GAAE,CAAC,IAAE,OAAIG,IAAE,CAAC,IAAEM,MAAED,MAAGR;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaG,KAAEG,KAAE,KAAK,kBAAkB,GAAE;AAAC,UAAK,EAAC,MAAKC,IAAC,IAAE,MAAK,EAAC,aAAYC,KAAE,OAAMC,KAAE,WAAUC,KAAE,MAAKC,KAAE,SAAQC,IAAC,IAAEL,KAAEF,MAAEG,IAAE,YAAYL,GAAC,GAAEU,KAAEL,IAAE,aAAaL,GAAC;AAAE,QAAG,EAAEA,MAAEM,OAAG,CAACJ,OAAGF,MAAEM,OAAG,CAACI,IAAG,QAAO,GAAEf,IAAEY,KAAEP,MAAEM,KAAE,GAAEH,IAAEK,KAAEC,GAAC,GAAEJ,IAAE,oBAAoBV,EAAC,GAAES,IAAE,KAAKT,IAAE,EAAC,SAAQ,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,+BAA+BK,KAAEG,IAAEC,KAAEC,KAAEC,KAAE;AAAC,UAAK,EAAC,MAAKC,IAAC,IAAE,MAAK,EAAC,MAAKC,KAAE,SAAQC,KAAE,aAAYC,IAAE,OAAMC,IAAE,WAAUf,GAAC,IAAEW,KAAEK,MAAED,KAAEP,KAAEX,KAAEiB,GAAE,YAAYE,GAAC,GAAElB,KAAEgB,GAAE,aAAaE,GAAC;AAAE,YAAOR,MAAE,KAAG,CAACX,MAAGW,MAAE,KAAG,CAACV,QAAKU,MAAE,IAAG,GAAER,IAAEA,IAAEU,GAAC,GAAE,GAAEN,KAAEJ,IAAEQ,KAAEC,KAAEF,IAAEK,KAAEC,GAAC,GAAEC,GAAE,oBAAoBV,GAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAEA,KAAE,EAAE,CAACS,GAAE,4CAA4C,CAAC,GAAET,EAAC;AAAE,IAAMc,KAAEd;;;ACAh3H,IAAMe,MAAE,EAAC,SAAQ,EAAC,cAAaC,GAAE,0BAA0B,GAAE,gBAAeA,GAAE,0BAA0B,EAAC,GAAE,YAAW,oBAAI,IAAI,CAAC,CAAC,SAAQ,CAAC,CAAC,CAAC,EAAC;AAAE,SAASC,IAAEC,KAAE;AAAC,SAAOC,GAAED,KAAEH,GAAC;AAAC;", "names": ["e", "o", "e", "r", "t", "n", "e", "n", "t", "e", "i", "s", "h", "r", "a", "n", "t", "c", "e", "i", "o", "h", "l", "g", "s", "r", "s", "t", "e", "a", "r", "n", "i", "c", "g", "o", "i", "r", "t", "e", "h", "n", "o", "a", "d", "s", "l", "_", "g", "c", "t", "n", "e", "i", "s", "r", "a", "o", "h", "g", "p", "m", "_", "n", "e", "t", "s", "r", "o", "i", "a", "m", "h", "c", "u", "f", "l", "g", "F", "e", "t", "a", "s", "h", "f", "i", "e", "n", "s", "V", "e", "t", "i", "D", "r", "o", "g", "c", "y", "a", "h", "f", "u", "d", "m", "l", "r", "n", "r", "e", "t", "n", "l", "s", "_", "e", "r", "t", "a", "d", "u", "f", "c", "n", "m", "h", "i", "p", "b", "g", "T", "E", "t", "n", "r", "n", "i", "s", "T", "o", "e", "t", "r", "e", "i", "o", "h", "r", "l", "t", "s", "n", "c", "a", "_", "d", "u", "p", "M", "f", "r", "i", "e", "t", "s", "o", "u", "l", "c", "m", "a", "n", "p", "c", "u", "f", "b", "e", "i", "o", "t", "y", "p", "m", "h", "M", "r", "n", "s", "l", "a", "g", "U", "d", "l", "d", "a", "v", "e", "g", "t", "s", "u", "a", "n", "h", "l", "v", "t", "P", "e", "i", "r", "s", "o", "c", "t", "e", "t", "i", "o", "c", "t", "e", "i", "s", "n", "l", "r", "h", "v", "t", "l", "n", "u", "f", "i", "o", "e", "s", "r", "m", "a", "d", "s", "t", "i", "l", "e", "a", "a", "s", "t", "o", "r", "t", "e", "s", "_", "v", "t", "a", "s", "u", "f", "o", "i", "e", "n", "m", "r", "h", "p", "l", "d", "u", "n", "d", "j", "v", "t", "e", "r", "o", "a", "f", "v", "w", "g", "u", "_", "d", "T", "t", "f", "c", "i", "o", "n", "e", "s", "a", "r", "m", "p", "l", "y", "a", "n", "t", "r", "e"]}