<template>
  <div class="sampling-record">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>采样记录</span>
          <div class="header-buttons">
            <el-button type="primary" @click="handleAdd">新增记录</el-button>
            <el-button type="danger" :disabled="selectedIds.length === 0" @click="handleBatchDelete">批量删除</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
        <el-form-item label="采样地点" prop="samplingLocation">
          <el-input v-model="queryParams.samplingLocation" placeholder="请输入采样地点" clearable />
        </el-form-item>
        <el-form-item label="采样人员" prop="samplingPerson">
          <el-input v-model="queryParams.samplingPerson" placeholder="请输入采样人员" clearable />
        </el-form-item>
        <el-form-item label="样品类型" prop="sampleType">
          <el-select
            v-model="queryParams.sampleType"
            placeholder="请选择样品类型"
            clearable
            style="width: 100%; min-width: 150px;"
          >
            <el-option label="地表水" value="地表水" />
            <el-option label="地下水" value="地下水" />
            <el-option label="原水" value="原水" />
            <el-option label="湖泊水" value="湖泊水" />
          </el-select>
        </el-form-item>
        <el-form-item label="采样时间" prop="samplingTime">
          <el-date-picker
            v-model="queryParams.samplingTime"
            type="date"
            placeholder="选择日期"
            value-format="x"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="samplingLocation" label="采样地点" min-width="120" />
        <el-table-column prop="samplingPerson" label="采样人员" min-width="100" />
        <el-table-column prop="samplingMethod" label="采样方法" min-width="120" />
        <el-table-column prop="sampleNumber" label="样品编号" min-width="120" />
        <el-table-column prop="sampleType" label="样品类型" min-width="100" />
        <el-table-column prop="samplingTime" label="采样时间" min-width="120">
          <template #default="{ row }">
            {{ row.samplingTime ? formatDate(Number(row.samplingTime), 'YYYY-MM-DD') : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="recordFile" label="化验报告" min-width="120">
          <template #default="{ row }">
            <el-button type="text" @click="handleDownload(row)" v-if="row.recordFile">下载</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="采样地点" prop="samplingLocation">
          <el-input v-model="form.samplingLocation" placeholder="请输入采样地点" />
        </el-form-item>
        <el-form-item label="采样人员" prop="samplingPerson">
          <el-input v-model="form.samplingPerson" placeholder="请输入采样人员" />
        </el-form-item>
        <el-form-item label="采样方法" prop="samplingMethod">
          <el-input v-model="form.samplingMethod" placeholder="请输入采样方法" />
        </el-form-item>
        <el-form-item label="样品编号" prop="sampleNumber">
          <el-input v-model="form.sampleNumber" placeholder="请输入样品编号" />
        </el-form-item>
        <el-form-item label="样品类型" prop="sampleType">
          <el-select
            v-model="form.sampleType"
            placeholder="请选择样品类型"
            style="width: 100%; min-width: 150px;"
          >
            <el-option label="地表水" value="地表水" />
            <el-option label="地下水" value="地下水" />
            <el-option label="原水" value="原水" />
            <el-option label="湖泊水" value="湖泊水" />
          </el-select>
        </el-form-item>
        <el-form-item label="采样时间" prop="samplingTime">
          <el-date-picker
            v-model="form.samplingTime"
            type="date"
            placeholder="选择日期"
            value-format="x"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="化验报告" prop="recordFile">
          <el-upload
            class="upload-demo"
            :http-request="handleUpload"
            :before-upload="beforeUpload"
            :limit="1"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请上传化验报告文件
                <span v-if="form.recordFile" style="color: #67C23A;">
                  (已上传)
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { formatDate } from '@/utils/DateFormatter'
import { getSamplingRecordList, saveSamplingRecord, updateSamplingRecord, deleteSamplingRecord } from '@/api/waterSource/sampling'
import { uploadFile } from '@/utils/fileUpload'
import { ElMessage, ElMessageBox } from 'element-plus'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  samplingLocation: '',
  samplingPerson: '',
  sampleType: '',
  samplingTime: undefined,
  type: '1' // 添加 type=1 表示供水厂的采样记录
})

// 表格数据
const tableData = ref([])
const total = ref(0)
const loading = ref(false)

// 选中项管理
const selectedIds = ref<string[]>([])
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 表单数据
const form = reactive({
  id: '',
  samplingLocation: '',
  samplingPerson: '',
  samplingMethod: '',
  sampleNumber: '',
  sampleType: '',
  samplingTime: undefined,
  recordFile: '',
  remark: '',
  type: '1' // 添加 type=1 表示供水厂的采样记录
})

// 表单校验规则
const rules = {
  samplingLocation: [{ required: true, message: '请输入采样地点', trigger: 'blur' }],
  samplingPerson: [{ required: true, message: '请输入采样人员', trigger: 'blur' }],
  samplingMethod: [{ required: true, message: '请输入采样方法', trigger: 'blur' }],
  sampleNumber: [{ required: true, message: '请输入样品编号', trigger: 'blur' }],
  sampleType: [{ required: true, message: '请选择样品类型', trigger: 'change' }],
  samplingTime: [{ required: true, message: '请选择采样时间', trigger: 'change' }]
}

// 对话框控制
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

// 上传相关

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 处理查询参数，确保参数名称与后端期望的一致
    const params: any = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      samplingLocation: queryParams.samplingLocation,
      samplingPerson: queryParams.samplingPerson
    }

    // 处理样品类型参数
    if (queryParams.sampleType) {
      // 添加多个可能的参数名称
      params.type = queryParams.sampleType // 可能的参数名称：type
      params.sampleType = queryParams.sampleType // 原始参数名称
    }

    // 处理采样时间参数
    if (queryParams.samplingTime) {
      // 添加多个可能的参数名称
      params.samplingTime = queryParams.samplingTime // 原始参数名称
      params.time = queryParams.samplingTime // 可能的参数名称：time
    }

    const res = await getSamplingRecordList(params)
    // 确保数据是数组
    if (res.data && res.data.data && res.data.data.data) {
      tableData.value = Array.isArray(res.data.data.data) ? res.data.data.data : []
      total.value = res.data.data.total || 0
    } else {
      console.warn('返回数据格式不正确:', res)
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  selectedIds.value = [] // 清除选中状态
  getList()
}

// 重置搜索
const resetQuery = () => {
  queryParams.samplingLocation = ''
  queryParams.samplingPerson = ''
  queryParams.sampleType = ''
  queryParams.samplingTime = undefined
  selectedIds.value = [] // 清除选中状态
  handleQuery()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增采样记录'
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogTitle.value = '编辑采样记录'
  Object.assign(form, {
    ...row,
    // 确保 samplingTime 是数字类型的时间戳
    samplingTime: row.samplingTime ? Number(row.samplingTime) : undefined
  })
  dialogVisible.value = true
}

// 删除单条记录
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该记录吗？', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      // 将单个ID转换为数组传递给API
      await deleteSamplingRecord([row.id])
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  ElMessageBox.confirm(`确认删除选中的 ${selectedIds.value.length} 条记录吗？`, '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await deleteSamplingRecord(selectedIds.value)
      ElMessage.success('批量删除成功')
      selectedIds.value = []
      getList()
    } catch (error) {
      console.error(error)
      ElMessage.error('批量删除失败')
    }
  })
}

// 下载报告
const handleDownload = (row) => {
  if (row.recordFile) {
    window.open(row.recordFile)
  }
}

// 上传前校验
const beforeUpload = (file: File) => {
  // 这里可以添加文件类型和大小的校验
  const isValidSize = file.size / 1024 / 1024 < 10 // 限制文件大小为10MB
  if (!isValidSize) {
    ElMessage.warning('文件大小不能超过10MB')
    return false
  }
  return true
}

// 自定义上传方法
const handleUpload = async (options: any) => {
  const { file } = options

  try {
    // 直接使用通用的文件上传工具函数
    const fileUrl = await uploadFile(file, 'file')
    // 将文件URL保存到表单中
    form.recordFile = fileUrl
    ElMessage.success('文件上传成功')
  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败')
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 处理日期格式
        const submitData = {
          ...form,
          // 已经使用 value-format="x" 确保是时间戳格式，这里转换为字符串
          // 确保 samplingTime 不为 undefined，如果没有值则使用当前时间
          samplingTime: form.samplingTime ? String(form.samplingTime) : String(Date.now()),
          // 确保 recordFile 是字符串类型
          recordFile: typeof form.recordFile === 'string' ? form.recordFile : ''
        }

        if (form.id) {
          await updateSamplingRecord(submitData)
          ElMessage.success('修改成功')
        } else {
          await saveSamplingRecord(submitData)
          ElMessage.success('新增成功')
        }
        dialogVisible.value = false
        getList()
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  // 清除所有表单数据，包括文件上传状态
  Object.assign(form, {
    id: '',
    samplingLocation: '',
    samplingPerson: '',
    samplingMethod: '',
    sampleNumber: '',
    sampleType: '',
    samplingTime: undefined,
    recordFile: '',
    remark: ''
  })

  // 重置文件上传组件
  const uploadComponent = document.querySelector('.upload-demo .el-upload__input');
  if (uploadComponent) {
    (uploadComponent as HTMLInputElement).value = '';
  }
}

// 分页
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  selectedIds.value = [] // 清除选中状态
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  selectedIds.value = [] // 清除选中状态
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.sampling-record {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .search-form {
    margin-bottom: 20px;

    :deep(.el-select) {
      .el-input__wrapper {
        padding-right: 30px;
      }

      .el-select__tags {
        max-width: calc(100% - 30px);
      }
    }
  }

  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

/* 确保选择框中的内容正确显示 */
:deep(.el-select .el-select__wrapper) {
  width: 100%;
}

:deep(.el-select .el-input__inner) {
  color: var(--el-text-color-primary);
  font-weight: normal;
}
</style>
