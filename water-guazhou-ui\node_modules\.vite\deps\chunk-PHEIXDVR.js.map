{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/Settings.js", "../../@arcgis/core/views/interactive/snapping/hints/IntersectionSnappingHint.js", "../../@arcgis/core/views/interactive/snapping/hints/ParallelSnappingHint.js", "../../@arcgis/core/views/interactive/snapping/hints/RightAngleSnappingHint.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import n from\"../../../Color.js\";import{JSONSupport as t}from\"../../../core/JSONSupport.js\";import{property as i}from\"../../../core/accessorSupport/decorators/property.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";let o=class extends t{constructor(){super(...arguments),this.enabled=!0}};e([i({type:Boolean})],o.prototype,\"enabled\",void 0),o=e([r(\"esri.views.interactive.snapping.Settings.DefaultSnappingAlgorithm\")],o);let l=class extends t{constructor(e){super(e),this.lineSnapper=new o,this.parallelLineSnapper=new o,this.rightAngleSnapper=new o,this.rightAngleTriangleSnapper=new o,this.shortLineThreshold=15,this.distance=5,this.pointThreshold=1e-6,this.intersectionParallelLineThreshold=1e-6,this.parallelLineThreshold=1e-6,this.verticalLineThreshold=.1,this.touchSensitivityMultiplier=1.5,this.pointOnLineThreshold=1e-6,this.orange=new n([255,127,0]),this.orangeTransparent=new n([255,127,0,.5]),this.lineHintWidthReference=3,this.lineHintWidthTarget=3,this.lineHintFadedExtensions=.3,this.parallelLineHintWidth=2,this.parallelLineHintLength=24,this.parallelLineHintOffset=1.5,this.rightAngleHintSize=24,this.rightAngleHintOutlineSize=1.5,this.satisfiesConstraintScreenThreshold=1}};e([i({type:o,constructOnly:!0,nonNullable:!0,json:{write:!0}})],l.prototype,\"lineSnapper\",void 0),e([i({type:o,constructOnly:!0,nonNullable:!0,json:{write:!0}})],l.prototype,\"parallelLineSnapper\",void 0),e([i({type:o,constructOnly:!0,nonNullable:!0,json:{write:!0}})],l.prototype,\"rightAngleSnapper\",void 0),e([i({type:o,constructOnly:!0,nonNullable:!0,json:{write:!0}})],l.prototype,\"rightAngleTriangleSnapper\",void 0),e([i({type:Number,nonNullable:!0,range:{min:-1,max:50,step:1},json:{write:!0}})],l.prototype,\"shortLineThreshold\",void 0),e([i({type:Number,nonNullable:!0,range:{min:-1,max:50,step:1},json:{write:!0}})],l.prototype,\"distance\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:1e-5},json:{write:!0}})],l.prototype,\"pointThreshold\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:1e-5},json:{write:!0}})],l.prototype,\"intersectionParallelLineThreshold\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:1e-5},json:{write:!0}})],l.prototype,\"parallelLineThreshold\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:1},json:{write:!0}})],l.prototype,\"verticalLineThreshold\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:10},json:{write:!0}})],l.prototype,\"touchSensitivityMultiplier\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:1e-5},json:{write:!0}})],l.prototype,\"pointOnLineThreshold\",void 0),e([i({type:n,nonNullable:!0})],l.prototype,\"orange\",void 0),e([i({type:n,nonNullable:!0})],l.prototype,\"orangeTransparent\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:10},json:{write:!0}})],l.prototype,\"lineHintWidthReference\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:10},json:{write:!0}})],l.prototype,\"lineHintWidthTarget\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:1},json:{write:!0}})],l.prototype,\"lineHintFadedExtensions\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:10},json:{write:!0}})],l.prototype,\"parallelLineHintWidth\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:50},json:{write:!0}})],l.prototype,\"parallelLineHintLength\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:5},json:{write:!0}})],l.prototype,\"parallelLineHintOffset\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:46},json:{write:!0}})],l.prototype,\"rightAngleHintSize\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:6},json:{write:!0}})],l.prototype,\"rightAngleHintOutlineSize\",void 0),e([i({type:Number,nonNullable:!0,range:{min:0,max:5},json:{write:!0}})],l.prototype,\"satisfiesConstraintScreenThreshold\",void 0),l=e([r(\"esri.views.interactive.snapping.Settings.Defaults\")],l);const p=new l;export{p as defaults};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{k as n}from\"../../../../chunks/vec3.js\";import{SnappingDomain as t}from\"../SnappingDomain.js\";import{SnappingHint as i}from\"./SnappingHint.js\";class o extends i{constructor(n,i,o=t.ALL){super(i,o),this.intersectionPoint=n}equals(t){return t instanceof o&&n(this.intersectionPoint,t.intersectionPoint)}}export{o as IntersectionSnappingHint};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../core/has.js\";import\"../../../../core/Logger.js\";import{k as t}from\"../../../../chunks/vec3.js\";import{SnappingDomain as n}from\"../SnappingDomain.js\";import{SnappingHint as i}from\"./SnappingHint.js\";class r extends i{constructor(t,i,r,s=n.ALL){super(r,s),this.lineStart=t,this.lineEnd=i}equals(n){return n instanceof r&&(t(this.lineStart,n.lineStart)&&t(this.lineEnd,n.lineEnd))}}export{r as ParallelSnappingHint};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../core/has.js\";import\"../../../../core/Logger.js\";import{k as e}from\"../../../../chunks/vec3.js\";import{SnappingDomain as t}from\"../SnappingDomain.js\";import{SnappingHint as r}from\"./SnappingHint.js\";class s extends r{constructor(e,r,s,i,o=t.ALL){super(i,o),this.previousVertex=e,this.centerVertex=r,this.nextVertex=s}equals(t){return t instanceof s&&(e(this.previousVertex,t.previousVertex)&&e(this.centerVertex,t.centerVertex)&&e(this.nextVertex,t.nextVertex))}}export{s as RightAngleSnappingHint};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAI6S,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,mEAAmE,CAAC,GAAE,CAAC;AAAE,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,cAAY,IAAI,KAAE,KAAK,sBAAoB,IAAI,KAAE,KAAK,oBAAkB,IAAI,KAAE,KAAK,4BAA0B,IAAI,KAAE,KAAK,qBAAmB,IAAG,KAAK,WAAS,GAAE,KAAK,iBAAe,MAAK,KAAK,oCAAkC,MAAK,KAAK,wBAAsB,MAAK,KAAK,wBAAsB,KAAG,KAAK,6BAA2B,KAAI,KAAK,uBAAqB,MAAK,KAAK,SAAO,IAAID,GAAE,CAAC,KAAI,KAAI,CAAC,CAAC,GAAE,KAAK,oBAAkB,IAAIA,GAAE,CAAC,KAAI,KAAI,GAAE,GAAE,CAAC,GAAE,KAAK,yBAAuB,GAAE,KAAK,sBAAoB,GAAE,KAAK,0BAAwB,KAAG,KAAK,wBAAsB,GAAE,KAAK,yBAAuB,IAAG,KAAK,yBAAuB,KAAI,KAAK,qBAAmB,IAAG,KAAK,4BAA0B,KAAI,KAAK,qCAAmC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,eAAc,MAAG,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,eAAc,MAAG,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,eAAc,MAAG,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,eAAc,MAAG,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,IAAG,KAAI,IAAG,MAAK,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,IAAG,KAAI,IAAG,MAAK,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,KAAI,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,KAAI,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qCAAoC,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,KAAI,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,GAAE,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,8BAA6B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,KAAI,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,IAAE,aAAY,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,IAAE,aAAY,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,GAAE,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,GAAE,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,GAAE,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,GAAE,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,GAAE,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,sCAAqC,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,mDAAmD,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAE,IAAIA;;;ACA5rH,IAAME,KAAN,MAAM,WAAU,EAAC;AAAA,EAAC,YAAY,GAAE,GAAEA,KAAE,EAAE,KAAI;AAAC,UAAM,GAAEA,EAAC,GAAE,KAAK,oBAAkB;AAAA,EAAC;AAAA,EAAC,OAAO,GAAE;AAAC,WAAO,aAAa,MAAG,EAAE,KAAK,mBAAkB,EAAE,iBAAiB;AAAA,EAAC;AAAC;;;ACA3F,IAAM,IAAN,MAAM,WAAU,EAAC;AAAA,EAAC,YAAY,GAAE,GAAEC,IAAEC,KAAE,EAAE,KAAI;AAAC,UAAMD,IAAEC,EAAC,GAAE,KAAK,YAAU,GAAE,KAAK,UAAQ;AAAA,EAAC;AAAA,EAAC,OAAO,GAAE;AAAC,WAAO,aAAa,OAAI,EAAE,KAAK,WAAU,EAAE,SAAS,KAAG,EAAE,KAAK,SAAQ,EAAE,OAAO;AAAA,EAAE;AAAC;;;ACApL,IAAMC,KAAN,MAAM,WAAU,EAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEF,IAAE,GAAEG,KAAE,EAAE,KAAI;AAAC,UAAM,GAAEA,EAAC,GAAE,KAAK,iBAAeF,IAAE,KAAK,eAAaC,IAAE,KAAK,aAAWF;AAAA,EAAC;AAAA,EAAC,OAAO,GAAE;AAAC,WAAO,aAAa,OAAI,EAAE,KAAK,gBAAe,EAAE,cAAc,KAAG,EAAE,KAAK,cAAa,EAAE,YAAY,KAAG,EAAE,KAAK,YAAW,EAAE,UAAU;AAAA,EAAE;AAAC;", "names": ["l", "e", "o", "r", "s", "s", "e", "r", "o"]}