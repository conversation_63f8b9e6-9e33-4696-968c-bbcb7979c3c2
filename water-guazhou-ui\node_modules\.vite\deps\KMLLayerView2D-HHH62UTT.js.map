{"version": 3, "sources": ["../../@arcgis/core/views/support/imageReprojection.js", "../../@arcgis/core/views/2d/layers/KMLLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../request.js\";import e from\"../../geometry/Point.js\";import{project as r}from\"../../geometry/projection.js\";import{projectResolution as i,getProjectionOffsetGrid as a}from\"../../layers/support/rasterFunctions/rasterProjectionHelper.js\";import{isImageSource as s,rasterize as n}from\"../2d/engine/Bitmap.js\";import o from\"../2d/engine/webgl/VertexStream.js\";import{createProgramTemplate as m}from\"../2d/engine/webgl/shaders/MaterialPrograms.js\";import{PixelFormat as p,PixelType as h,TextureWrapMode as c,TextureSamplingMode as x,TargetType as d,DepthStencilTargetType as g,ColorAttachment as u}from\"../webgl/enums.js\";import{FramebufferObject as _}from\"../webgl/FramebufferObject.js\";import{createTransformTexture as f}from\"../webgl/rasterUtils.js\";import{RenderingContext as l}from\"../webgl/RenderingContext.js\";import{Texture as w}from\"../webgl/Texture.js\";class b{constructor(t){if(this._ownsRctx=!1,t)this._ownsRctx=!1,this._rctx=t;else{if(b._instance)return b._instanceRefCount++,b._instance;b._instanceRefCount=1,b._instance=this,this._ownsRctx=!0;const t=document.createElement(\"canvas\").getContext(\"webgl\");t.getExtension(\"OES_texture_float\"),this._rctx=new l(t,{})}const e={applyProjection:!0,bilinear:!1,bicubic:!1},r=m(\"raster/reproject\",\"raster/reproject\",new Map([[\"a_position\",0]]),e);this._program=this._rctx.programCache.acquire(r.shaders.vertexShader,r.shaders.fragmentShader,r.attributes),this._rctx.useProgram(this._program),this._program.setUniform1f(\"u_opacity\",1),this._program.setUniform1i(\"u_image\",0),this._program.setUniform1i(\"u_flipY\",0),this._program.setUniform1i(\"u_transformGrid\",1),this._quad=new o(this._rctx,[0,0,1,0,0,1,1,1])}reprojectTexture(t,s,n=!1){const o=r(t.extent,s),m=new e({x:(t.extent.xmax-t.extent.xmin)/t.texture.descriptor.width,y:(t.extent.ymax-t.extent.ymin)/t.texture.descriptor.height,spatialReference:t.extent.spatialReference}),{x:l,y:b}=i(m,s,t.extent);let T=(l+b)/2;const R=Math.round((o.xmax-o.xmin)/T),j=Math.round((o.ymax-o.ymin)/T);T=(o.width/R+o.height/j)/2;const E=new e({x:T,y:T,spatialReference:o.spatialReference}),D=a({projectedExtent:o,srcBufferExtent:t.extent,pixelSize:E,hasWrapAround:!0,spacing:[16,16]}),C=f(this._rctx,D),y=new w(this._rctx,{width:R,height:j,pixelFormat:p.RGBA,dataType:h.UNSIGNED_BYTE,wrapMode:c.CLAMP_TO_EDGE,samplingMode:x.LINEAR,hasMipmap:!1}),M=new _(this._rctx,{colorTarget:d.TEXTURE,depthStencilTarget:g.NONE,width:R,height:j},y);this._rctx.bindFramebuffer(M),this._rctx.setViewport(0,0,R,j),this._rctx.useProgram(this._program),this._rctx.bindTexture(t.texture,0),this._rctx.bindTexture(C,1),this._quad.bind();const{width:A=0,height:S=0}=t.texture.descriptor;if(this._program.setUniform2f(\"u_srcImageSize\",A,S),this._program.setUniform2fv(\"u_transformSpacing\",D.spacing),this._program.setUniform2fv(\"u_transformGridSize\",D.size),this._program.setUniform2f(\"u_targetImageSize\",R,j),this._quad.draw(),this._quad.unbind(),this._rctx.useProgram(null),this._rctx.bindFramebuffer(null),C.dispose(),n){const{width:t=0,height:e=0}=M.descriptor,r=new ImageData(t,e);return M.readPixels(0,0,t,e,p.RGBA,h.UNSIGNED_BYTE,r.data),M.detachColorTexture(u.COLOR_ATTACHMENT0),M.dispose(),{texture:y,extent:o,imageData:r}}return M.detachColorTexture(u.COLOR_ATTACHMENT0),M.dispose(),{texture:y,extent:o}}reprojectBitmapData(t,e){const r=s(t.bitmapData)?n(t.bitmapData):t.bitmapData,i=new w(this._rctx,{width:t.bitmapData.width,height:t.bitmapData.height,pixelFormat:p.RGBA,dataType:h.UNSIGNED_BYTE,wrapMode:c.CLAMP_TO_EDGE,samplingMode:x.LINEAR,hasMipmap:!1},r),a=this.reprojectTexture({texture:i,extent:t.extent},e,!0);a.texture.dispose();const o=document.createElement(\"canvas\"),m=a.imageData;o.width=m.width,o.height=m.height;return o.getContext(\"2d\").putImageData(m,0,0),{bitmapData:o,extent:a.extent}}async loadAndReprojectBitmapData(e,r,i){const a=(await t(e,{responseType:\"image\"})).data,s=document.createElement(\"canvas\");s.width=a.width,s.height=a.height;const n=s.getContext(\"2d\");n.drawImage(a,0,0);const o=n.getImageData(0,0,s.width,s.height);if(r.spatialReference.equals(i))return{bitmapData:o,extent:r};const m=this.reprojectBitmapData({bitmapData:o,extent:r},i);return{bitmapData:m.bitmapData,extent:m.extent}}destroy(){this._ownsRctx?(b._instanceRefCount--,0===b._instanceRefCount&&(this._quad.dispose(),this._program.dispose(),this._rctx.dispose(),b._instance=null)):(this._quad.dispose(),this._program.dispose())}}b._instanceRefCount=0;export{b as ImageReprojector};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import{version as i}from\"../../../kernel.js\";import t from\"../../../core/Collection.js\";import{abortMaybe as s,destroyMaybe as a,isSome as o,isNone as r}from\"../../../core/maybe.js\";import{watch as l}from\"../../../core/reactiveUtils.js\";import{queryToObject as n,objectToQuery as h}from\"../../../core/urlUtils.js\";import{property as p}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as m}from\"../../../core/accessorSupport/decorators/subclass.js\";import c from\"../../../geometry/Extent.js\";import{load as d,project as y}from\"../../../geometry/projection.js\";import g from\"../../../geometry/SpatialReference.js\";import{getGraphics as w,fetchService as u,parseKML as _}from\"../../../layers/support/kmlUtils.js\";import{parseUrl as b}from\"../../../rest/utils.js\";import{GraphicsCollection as V}from\"../../../support/GraphicsCollection.js\";import{Bitmap as f}from\"../engine/Bitmap.js\";import{BitmapContainer as v}from\"../engine/BitmapContainer.js\";import{LayerView2DMixin as S}from\"./LayerView2D.js\";import I from\"./graphics/GraphicContainer.js\";import x from\"./graphics/GraphicsView2D.js\";import C from\"../../layers/LayerView.js\";import{ImageReprojector as j}from\"../../support/imageReprojection.js\";class k{constructor(){this.allSublayers=new Map,this.allPoints=[],this.allPolylines=[],this.allPolygons=[],this.allMapImages=[]}}let P=class extends(S(C)){constructor(){super(...arguments),this._bitmapIndex=new Map,this._mapImageContainer=new v,this._kmlVisualData=new k,this._fetchController=null,this.allVisiblePoints=new V,this.allVisiblePolylines=new V,this.allVisiblePolygons=new V,this.allVisibleMapImages=new t}async hitTest(e,i){const t=this.layer;return[this._pointsView?.hitTest(e),this._polylinesView?.hitTest(e),this._polygonsView?.hitTest(e)].flat().filter(Boolean).map((i=>(i.layer=t,i.sourceLayer=t,{type:\"graphic\",graphic:i,layer:t,mapPoint:e})))}update(e){this._polygonsView&&this._polygonsView.processUpdate(e),this._polylinesView&&this._polylinesView.processUpdate(e),this._pointsView&&this._pointsView.processUpdate(e)}attach(){this._fetchController=new AbortController,this.container.addChild(this._mapImageContainer),this._polygonsView=new x({view:this.view,graphics:this.allVisiblePolygons,requestUpdateCallback:()=>this.requestUpdate(),container:new I(this.view.featuresTilingScheme)}),this.container.addChild(this._polygonsView.container),this._polylinesView=new x({view:this.view,graphics:this.allVisiblePolylines,requestUpdateCallback:()=>this.requestUpdate(),container:new I(this.view.featuresTilingScheme)}),this.container.addChild(this._polylinesView.container),this._pointsView=new x({view:this.view,graphics:this.allVisiblePoints,requestUpdateCallback:()=>this.requestUpdate(),container:new I(this.view.featuresTilingScheme)}),this.container.addChild(this._pointsView.container),this.addAttachHandles([this.allVisibleMapImages.on(\"change\",(e=>{e.added.forEach((e=>this._addMapImage(e))),e.removed.forEach((e=>this._removeMapImage(e)))})),l((()=>this.layer.visibleSublayers),(e=>{for(const[i,t]of this._kmlVisualData.allSublayers)t.visibility=0;for(const i of e){const e=this._kmlVisualData.allSublayers.get(i.id);e&&(e.visibility=1)}this._refreshCollections()}))]),this.updatingHandles.addPromise(this._fetchService(this._fetchController.signal)),this._imageReprojector=new j}detach(){this._fetchController=s(this._fetchController),this._mapImageContainer.removeAllChildren(),this.container.removeAllChildren(),this._bitmapIndex.clear(),this._polygonsView=a(this._polygonsView),this._polylinesView=a(this._polylinesView),this._pointsView=a(this._pointsView),this._imageReprojector=a(this._imageReprojector)}moveStart(){}viewChange(){this._polygonsView.viewChange(),this._polylinesView.viewChange(),this._pointsView.viewChange()}moveEnd(){}isUpdating(){return this._pointsView.updating||this._polygonsView.updating||this._polylinesView.updating}_addMapImage(e){(this.view.spatialReference?.isWGS84||this.view.spatialReference?.isWebMercator)&&this._imageReprojector.loadAndReprojectBitmapData(e.href,c.fromJSON(e.extent),this.view.spatialReference).then((i=>{const t=new f(i.bitmapData,{immutable:!1,requestRenderOnSourceChangedEnabled:!0});t.x=i.extent.xmin,t.y=i.extent.ymax,t.resolution=i.extent.width/i.bitmapData.width,t.rotation=e.rotation,this._mapImageContainer.addChild(t),this._bitmapIndex.set(e,t)}))}async _getViewDependentUrl(e,t){const{viewFormat:s,viewBoundScale:a,httpQuery:l}=e;if(o(s)){if(r(t))throw new Error(\"Loading this network link requires a view state.\");let p;if(await d(),o(a)&&1!==a){const e=new c(t.extent);e.expand(a),p=e}else p=t.extent;p=y(p,g.WGS84);const m=y(p,g.WebMercator),w=p.xmin,u=p.xmax,_=p.ymin,V=p.ymax,f=t.size[0]*t.pixelRatio,v=t.size[1]*t.pixelRatio,S=Math.max(m.width,m.height),I={\"[bboxWest]\":w.toString(),\"[bboxEast]\":u.toString(),\"[bboxSouth]\":_.toString(),\"[bboxNorth]\":V.toString(),\"[lookatLon]\":p.center.x.toString(),\"[lookatLat]\":p.center.y.toString(),\"[lookatRange]\":S.toString(),\"[lookatTilt]\":\"0\",\"[lookatHeading]\":t.rotation.toString(),\"[lookatTerrainLon]\":p.center.x.toString(),\"[lookatTerrainLat]\":p.center.y.toString(),\"[lookatTerrainAlt]\":\"0\",\"[cameraLon]\":p.center.x.toString(),\"[cameraLat]\":p.center.y.toString(),\"[cameraAlt]\":S.toString(),\"[horizFov]\":\"60\",\"[vertFov]\":\"60\",\"[horizPixels]\":f.toString(),\"[vertPixels]\":v.toString(),\"[terrainEnabled]\":\"0\",\"[clientVersion]\":i,\"[kmlVersion]\":\"2.2\",\"[clientName]\":\"ArcGIS API for JavaScript\",\"[language]\":\"en-US\"},x=e=>{for(const i in e)for(const t in I)e[i]=e[i].replace(t,I[t])},C=n(s);x(C);let j={};o(l)&&(j=n(l),x(j));const k=b(e.href);k.query={...k.query,...C,...j};return`${k.path}?${h(C)}`}return e.href}async _fetchService(e){const i=new k;await this._loadVisualData(this.layer.url,i,e),this._kmlVisualData=i,this._refreshCollections()}_refreshCollections(){this.allVisiblePoints.removeAll(),this.allVisiblePolylines.removeAll(),this.allVisiblePolygons.removeAll(),this.allVisibleMapImages.removeAll(),this.allVisiblePoints.addMany(this._kmlVisualData.allPoints.filter((e=>this._isSublayerVisible(e.sublayerId))).map((({item:e})=>e))),this.allVisiblePolylines.addMany(this._kmlVisualData.allPolylines.filter((e=>this._isSublayerVisible(e.sublayerId))).map((({item:e})=>e))),this.allVisiblePolygons.addMany(this._kmlVisualData.allPolygons.filter((e=>this._isSublayerVisible(e.sublayerId))).map((({item:e})=>e))),this.allVisibleMapImages.addMany(this._kmlVisualData.allMapImages.filter((e=>this._isSublayerVisible(e.sublayerId))).map((({item:e})=>e)))}_isSublayerVisible(e){const i=this._kmlVisualData.allSublayers.get(e);return!!i?.visibility&&(-1===i.parentFolderId||this._isSublayerVisible(i.parentFolderId))}_loadVisualData(e,i,t){return this._fetchParsedKML(e,t).then((async e=>{for(const s of e.sublayers){i.allSublayers.set(s.id,s);const e=s.points?await w(s.points):[],a=s.polylines?await w(s.polylines):[],o=s.polygons?await w(s.polygons):[],r=s.mapImages||[];if(i.allPoints.push(...e.map((e=>({item:e,sublayerId:s.id})))),i.allPolylines.push(...a.map((e=>({item:e,sublayerId:s.id})))),i.allPolygons.push(...o.map((e=>({item:e,sublayerId:s.id})))),i.allMapImages.push(...r.map((e=>({item:e,sublayerId:s.id})))),s.networkLink){const e=await this._getViewDependentUrl(s.networkLink,this.view.state);await this._loadVisualData(e,i,t)}}}))}_fetchParsedKML(e,i){return u(e,this.layer.spatialReference,this.layer.refreshInterval,i).then((e=>_(e.data)))}_removeMapImage(e){const i=this._bitmapIndex.get(e);i&&(this._mapImageContainer.removeChild(i),this._bitmapIndex.delete(e))}};e([p()],P.prototype,\"_pointsView\",void 0),e([p()],P.prototype,\"_polylinesView\",void 0),e([p()],P.prototype,\"_polygonsView\",void 0),e([p()],P.prototype,\"updating\",void 0),P=e([m(\"esri.views.2d.layers.KMLLayerView2D\")],P);const M=P;export{M as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI42B,IAAMA,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,QAAG,KAAK,YAAU,OAAGA,GAAE,MAAK,YAAU,OAAG,KAAK,QAAMA;AAAA,SAAM;AAAC,UAAG,GAAE,UAAU,QAAO,GAAE,qBAAoB,GAAE;AAAU,SAAE,oBAAkB,GAAE,GAAE,YAAU,MAAK,KAAK,YAAU;AAAG,YAAMA,KAAE,SAAS,cAAc,QAAQ,EAAE,WAAW,OAAO;AAAE,MAAAA,GAAE,aAAa,mBAAmB,GAAE,KAAK,QAAM,IAAI,EAAEA,IAAE,CAAC,CAAC;AAAA,IAAC;AAAC,UAAMC,KAAE,EAAC,iBAAgB,MAAG,UAAS,OAAG,SAAQ,MAAE,GAAEC,KAAE,EAAE,oBAAmB,oBAAmB,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,GAAED,EAAC;AAAE,SAAK,WAAS,KAAK,MAAM,aAAa,QAAQC,GAAE,QAAQ,cAAaA,GAAE,QAAQ,gBAAeA,GAAE,UAAU,GAAE,KAAK,MAAM,WAAW,KAAK,QAAQ,GAAE,KAAK,SAAS,aAAa,aAAY,CAAC,GAAE,KAAK,SAAS,aAAa,WAAU,CAAC,GAAE,KAAK,SAAS,aAAa,WAAU,CAAC,GAAE,KAAK,SAAS,aAAa,mBAAkB,CAAC,GAAE,KAAK,QAAM,IAAI,EAAE,KAAK,OAAM,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAE,GAAEG,KAAE,OAAG;AAAC,UAAMC,KAAE,GAAEJ,GAAE,QAAO,CAAC,GAAE,IAAE,IAAIK,GAAE,EAAC,IAAGL,GAAE,OAAO,OAAKA,GAAE,OAAO,QAAMA,GAAE,QAAQ,WAAW,OAAM,IAAGA,GAAE,OAAO,OAAKA,GAAE,OAAO,QAAMA,GAAE,QAAQ,WAAW,QAAO,kBAAiBA,GAAE,OAAO,iBAAgB,CAAC,GAAE,EAAC,GAAEM,IAAE,GAAEP,GAAC,IAAE,EAAE,GAAE,GAAEC,GAAE,MAAM;AAAE,QAAIO,MAAGD,KAAEP,MAAG;AAAE,UAAMS,KAAE,KAAK,OAAOJ,GAAE,OAAKA,GAAE,QAAMG,EAAC,GAAEE,KAAE,KAAK,OAAOL,GAAE,OAAKA,GAAE,QAAMG,EAAC;AAAE,IAAAA,MAAGH,GAAE,QAAMI,KAAEJ,GAAE,SAAOK,MAAG;AAAE,UAAMC,KAAE,IAAIL,GAAE,EAAC,GAAEE,IAAE,GAAEA,IAAE,kBAAiBH,GAAE,iBAAgB,CAAC,GAAEO,KAAE,EAAE,EAAC,iBAAgBP,IAAE,iBAAgBJ,GAAE,QAAO,WAAUU,IAAE,eAAc,MAAG,SAAQ,CAAC,IAAG,EAAE,EAAC,CAAC,GAAEE,KAAEN,GAAE,KAAK,OAAMK,EAAC,GAAEE,KAAE,IAAI,EAAE,KAAK,OAAM,EAAC,OAAML,IAAE,QAAOC,IAAE,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,UAAS,EAAE,eAAc,cAAa,EAAE,QAAO,WAAU,MAAE,CAAC,GAAEK,KAAE,IAAI,EAAE,KAAK,OAAM,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,MAAK,OAAMN,IAAE,QAAOC,GAAC,GAAEI,EAAC;AAAE,SAAK,MAAM,gBAAgBC,EAAC,GAAE,KAAK,MAAM,YAAY,GAAE,GAAEN,IAAEC,EAAC,GAAE,KAAK,MAAM,WAAW,KAAK,QAAQ,GAAE,KAAK,MAAM,YAAYT,GAAE,SAAQ,CAAC,GAAE,KAAK,MAAM,YAAYY,IAAE,CAAC,GAAE,KAAK,MAAM,KAAK;AAAE,UAAK,EAAC,OAAMG,KAAE,GAAE,QAAOC,KAAE,EAAC,IAAEhB,GAAE,QAAQ;AAAW,QAAG,KAAK,SAAS,aAAa,kBAAiBe,IAAEC,EAAC,GAAE,KAAK,SAAS,cAAc,sBAAqBL,GAAE,OAAO,GAAE,KAAK,SAAS,cAAc,uBAAsBA,GAAE,IAAI,GAAE,KAAK,SAAS,aAAa,qBAAoBH,IAAEC,EAAC,GAAE,KAAK,MAAM,KAAK,GAAE,KAAK,MAAM,OAAO,GAAE,KAAK,MAAM,WAAW,IAAI,GAAE,KAAK,MAAM,gBAAgB,IAAI,GAAEG,GAAE,QAAQ,GAAET,IAAE;AAAC,YAAK,EAAC,OAAMH,KAAE,GAAE,QAAOC,KAAE,EAAC,IAAEa,GAAE,YAAWZ,KAAE,IAAI,UAAUF,IAAEC,EAAC;AAAE,aAAOa,GAAE,WAAW,GAAE,GAAEd,IAAEC,IAAE,EAAE,MAAK,EAAE,eAAcC,GAAE,IAAI,GAAEY,GAAE,mBAAmBG,GAAE,iBAAiB,GAAEH,GAAE,QAAQ,GAAE,EAAC,SAAQD,IAAE,QAAOT,IAAE,WAAUF,GAAC;AAAA,IAAC;AAAC,WAAOY,GAAE,mBAAmBG,GAAE,iBAAiB,GAAEH,GAAE,QAAQ,GAAE,EAAC,SAAQD,IAAE,QAAOT,GAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBJ,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAEF,GAAE,UAAU,IAAE,EAAEA,GAAE,UAAU,IAAEA,GAAE,YAAWkB,KAAE,IAAI,EAAE,KAAK,OAAM,EAAC,OAAMlB,GAAE,WAAW,OAAM,QAAOA,GAAE,WAAW,QAAO,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,UAAS,EAAE,eAAc,cAAa,EAAE,QAAO,WAAU,MAAE,GAAEE,EAAC,GAAEiB,KAAE,KAAK,iBAAiB,EAAC,SAAQD,IAAE,QAAOlB,GAAE,OAAM,GAAEC,IAAE,IAAE;AAAE,IAAAkB,GAAE,QAAQ,QAAQ;AAAE,UAAMf,KAAE,SAAS,cAAc,QAAQ,GAAE,IAAEe,GAAE;AAAU,IAAAf,GAAE,QAAM,EAAE,OAAMA,GAAE,SAAO,EAAE;AAAO,WAAOA,GAAE,WAAW,IAAI,EAAE,aAAa,GAAE,GAAE,CAAC,GAAE,EAAC,YAAWA,IAAE,QAAOe,GAAE,OAAM;AAAA,EAAC;AAAA,EAAC,MAAM,2BAA2BlB,IAAEC,IAAEgB,IAAE;AAAC,UAAMC,MAAG,MAAM,EAAElB,IAAE,EAAC,cAAa,QAAO,CAAC,GAAG,MAAK,IAAE,SAAS,cAAc,QAAQ;AAAE,MAAE,QAAMkB,GAAE,OAAM,EAAE,SAAOA,GAAE;AAAO,UAAMhB,KAAE,EAAE,WAAW,IAAI;AAAE,IAAAA,GAAE,UAAUgB,IAAE,GAAE,CAAC;AAAE,UAAMf,KAAED,GAAE,aAAa,GAAE,GAAE,EAAE,OAAM,EAAE,MAAM;AAAE,QAAGD,GAAE,iBAAiB,OAAOgB,EAAC,EAAE,QAAM,EAAC,YAAWd,IAAE,QAAOF,GAAC;AAAE,UAAM,IAAE,KAAK,oBAAoB,EAAC,YAAWE,IAAE,QAAOF,GAAC,GAAEgB,EAAC;AAAE,WAAM,EAAC,YAAW,EAAE,YAAW,QAAO,EAAE,OAAM;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,aAAW,GAAE,qBAAoB,MAAI,GAAE,sBAAoB,KAAK,MAAM,QAAQ,GAAE,KAAK,SAAS,QAAQ,GAAE,KAAK,MAAM,QAAQ,GAAE,GAAE,YAAU,UAAQ,KAAK,MAAM,QAAQ,GAAE,KAAK,SAAS,QAAQ;AAAA,EAAE;AAAC;AAACnB,GAAE,oBAAkB;;;ACA/gG,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,eAAa,oBAAI,OAAI,KAAK,YAAU,CAAC,GAAE,KAAK,eAAa,CAAC,GAAE,KAAK,cAAY,CAAC,GAAE,KAAK,eAAa,CAAC;AAAA,EAAC;AAAC;AAAC,IAAIqB,KAAE,cAAcC,GAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,oBAAI,OAAI,KAAK,qBAAmB,IAAIC,MAAE,KAAK,iBAAe,IAAI,KAAE,KAAK,mBAAiB,MAAK,KAAK,mBAAiB,IAAI,KAAE,KAAK,sBAAoB,IAAI,KAAE,KAAK,qBAAmB,IAAI,KAAE,KAAK,sBAAoB,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQC,IAAEC,IAAE;AAJpwD;AAIqwD,UAAMC,KAAE,KAAK;AAAM,WAAM,EAAC,UAAK,gBAAL,mBAAkB,QAAQF,MAAG,UAAK,mBAAL,mBAAqB,QAAQA,MAAG,UAAK,kBAAL,mBAAoB,QAAQA,GAAE,EAAE,KAAK,EAAE,OAAO,OAAO,EAAE,IAAK,CAAAC,QAAIA,GAAE,QAAMC,IAAED,GAAE,cAAYC,IAAE,EAAC,MAAK,WAAU,SAAQD,IAAE,OAAMC,IAAE,UAASF,GAAC,EAAG;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,SAAK,iBAAe,KAAK,cAAc,cAAcA,EAAC,GAAE,KAAK,kBAAgB,KAAK,eAAe,cAAcA,EAAC,GAAE,KAAK,eAAa,KAAK,YAAY,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,mBAAiB,IAAI,mBAAgB,KAAK,UAAU,SAAS,KAAK,kBAAkB,GAAE,KAAK,gBAAc,IAAI,GAAE,EAAC,MAAK,KAAK,MAAK,UAAS,KAAK,oBAAmB,uBAAsB,MAAI,KAAK,cAAc,GAAE,WAAU,IAAIC,GAAE,KAAK,KAAK,oBAAoB,EAAC,CAAC,GAAE,KAAK,UAAU,SAAS,KAAK,cAAc,SAAS,GAAE,KAAK,iBAAe,IAAI,GAAE,EAAC,MAAK,KAAK,MAAK,UAAS,KAAK,qBAAoB,uBAAsB,MAAI,KAAK,cAAc,GAAE,WAAU,IAAIA,GAAE,KAAK,KAAK,oBAAoB,EAAC,CAAC,GAAE,KAAK,UAAU,SAAS,KAAK,eAAe,SAAS,GAAE,KAAK,cAAY,IAAI,GAAE,EAAC,MAAK,KAAK,MAAK,UAAS,KAAK,kBAAiB,uBAAsB,MAAI,KAAK,cAAc,GAAE,WAAU,IAAIA,GAAE,KAAK,KAAK,oBAAoB,EAAC,CAAC,GAAE,KAAK,UAAU,SAAS,KAAK,YAAY,SAAS,GAAE,KAAK,iBAAiB,CAAC,KAAK,oBAAoB,GAAG,UAAU,CAAAD,OAAG;AAAC,MAAAA,GAAE,MAAM,QAAS,CAAAA,OAAG,KAAK,aAAaA,EAAC,CAAE,GAAEA,GAAE,QAAQ,QAAS,CAAAA,OAAG,KAAK,gBAAgBA,EAAC,CAAE;AAAA,IAAC,CAAE,GAAE,EAAG,MAAI,KAAK,MAAM,kBAAmB,CAAAA,OAAG;AAAC,iBAAS,CAACC,IAAEC,EAAC,KAAI,KAAK,eAAe,aAAa,CAAAA,GAAE,aAAW;AAAE,iBAAUD,MAAKD,IAAE;AAAC,cAAMA,KAAE,KAAK,eAAe,aAAa,IAAIC,GAAE,EAAE;AAAE,QAAAD,OAAIA,GAAE,aAAW;AAAA,MAAE;AAAC,WAAK,oBAAoB;AAAA,IAAC,CAAE,CAAC,CAAC,GAAE,KAAK,gBAAgB,WAAW,KAAK,cAAc,KAAK,iBAAiB,MAAM,CAAC,GAAE,KAAK,oBAAkB,IAAIG;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,mBAAiB,EAAE,KAAK,gBAAgB,GAAE,KAAK,mBAAmB,kBAAkB,GAAE,KAAK,UAAU,kBAAkB,GAAE,KAAK,aAAa,MAAM,GAAE,KAAK,gBAAc,EAAE,KAAK,aAAa,GAAE,KAAK,iBAAe,EAAE,KAAK,cAAc,GAAE,KAAK,cAAY,EAAE,KAAK,WAAW,GAAE,KAAK,oBAAkB,EAAE,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,cAAc,WAAW,GAAE,KAAK,eAAe,WAAW,GAAE,KAAK,YAAY,WAAW;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,YAAY,YAAU,KAAK,cAAc,YAAU,KAAK,eAAe;AAAA,EAAQ;AAAA,EAAC,aAAaH,IAAE;AAJn9H;AAIo9H,OAAC,UAAK,KAAK,qBAAV,mBAA4B,cAAS,UAAK,KAAK,qBAAV,mBAA4B,mBAAgB,KAAK,kBAAkB,2BAA2BA,GAAE,MAAKI,GAAE,SAASJ,GAAE,MAAM,GAAE,KAAK,KAAK,gBAAgB,EAAE,KAAM,CAAAC,OAAG;AAAC,YAAMC,KAAE,IAAI,EAAED,GAAE,YAAW,EAAC,WAAU,OAAG,qCAAoC,KAAE,CAAC;AAAE,MAAAC,GAAE,IAAED,GAAE,OAAO,MAAKC,GAAE,IAAED,GAAE,OAAO,MAAKC,GAAE,aAAWD,GAAE,OAAO,QAAMA,GAAE,WAAW,OAAMC,GAAE,WAASF,GAAE,UAAS,KAAK,mBAAmB,SAASE,EAAC,GAAE,KAAK,aAAa,IAAIF,IAAEE,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBF,IAAEE,IAAE;AAAC,UAAK,EAAC,YAAW,GAAE,gBAAeH,IAAE,WAAUM,GAAC,IAAEL;AAAE,QAAG,EAAE,CAAC,GAAE;AAAC,UAAG,EAAEE,EAAC,EAAE,OAAM,IAAI,MAAM,kDAAkD;AAAE,UAAI;AAAE,UAAG,MAAM,GAAE,GAAE,EAAEH,EAAC,KAAG,MAAIA,IAAE;AAAC,cAAMC,KAAE,IAAII,GAAEF,GAAE,MAAM;AAAE,QAAAF,GAAE,OAAOD,EAAC,GAAE,IAAEC;AAAA,MAAC,MAAM,KAAEE,GAAE;AAAO,UAAE,GAAE,GAAE,EAAE,KAAK;AAAE,YAAM,IAAE,GAAE,GAAE,EAAE,WAAW,GAAEE,KAAE,EAAE,MAAKE,KAAE,EAAE,MAAK,IAAE,EAAE,MAAKC,KAAE,EAAE,MAAKT,KAAEI,GAAE,KAAK,CAAC,IAAEA,GAAE,YAAWM,KAAEN,GAAE,KAAK,CAAC,IAAEA,GAAE,YAAWO,KAAE,KAAK,IAAI,EAAE,OAAM,EAAE,MAAM,GAAEC,KAAE,EAAC,cAAaN,GAAE,SAAS,GAAE,cAAaE,GAAE,SAAS,GAAE,eAAc,EAAE,SAAS,GAAE,eAAcC,GAAE,SAAS,GAAE,eAAc,EAAE,OAAO,EAAE,SAAS,GAAE,eAAc,EAAE,OAAO,EAAE,SAAS,GAAE,iBAAgBE,GAAE,SAAS,GAAE,gBAAe,KAAI,mBAAkBP,GAAE,SAAS,SAAS,GAAE,sBAAqB,EAAE,OAAO,EAAE,SAAS,GAAE,sBAAqB,EAAE,OAAO,EAAE,SAAS,GAAE,sBAAqB,KAAI,eAAc,EAAE,OAAO,EAAE,SAAS,GAAE,eAAc,EAAE,OAAO,EAAE,SAAS,GAAE,eAAcO,GAAE,SAAS,GAAE,cAAa,MAAK,aAAY,MAAK,iBAAgBX,GAAE,SAAS,GAAE,gBAAeU,GAAE,SAAS,GAAE,oBAAmB,KAAI,mBAAkBT,IAAE,gBAAe,OAAM,gBAAe,6BAA4B,cAAa,QAAO,GAAEY,KAAE,CAAAX,OAAG;AAAC,mBAAUC,MAAKD,GAAE,YAAUE,MAAKQ,GAAE,CAAAV,GAAEC,EAAC,IAAED,GAAEC,EAAC,EAAE,QAAQC,IAAEQ,GAAER,EAAC,CAAC;AAAA,MAAC,GAAEU,KAAE,EAAE,CAAC;AAAE,MAAAD,GAAEC,EAAC;AAAE,UAAIC,KAAE,CAAC;AAAE,QAAER,EAAC,MAAIQ,KAAE,EAAER,EAAC,GAAEM,GAAEE,EAAC;AAAG,YAAMC,KAAEhB,GAAEE,GAAE,IAAI;AAAE,MAAAc,GAAE,QAAM,EAAC,GAAGA,GAAE,OAAM,GAAGF,IAAE,GAAGC,GAAC;AAAE,aAAM,GAAGC,GAAE,IAAI,IAAI,EAAEF,EAAC,CAAC;AAAA,IAAE;AAAC,WAAOZ,GAAE;AAAA,EAAI;AAAA,EAAC,MAAM,cAAcA,IAAE;AAAC,UAAMC,KAAE,IAAI;AAAE,UAAM,KAAK,gBAAgB,KAAK,MAAM,KAAIA,IAAED,EAAC,GAAE,KAAK,iBAAeC,IAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,SAAK,iBAAiB,UAAU,GAAE,KAAK,oBAAoB,UAAU,GAAE,KAAK,mBAAmB,UAAU,GAAE,KAAK,oBAAoB,UAAU,GAAE,KAAK,iBAAiB,QAAQ,KAAK,eAAe,UAAU,OAAQ,CAAAD,OAAG,KAAK,mBAAmBA,GAAE,UAAU,CAAE,EAAE,IAAK,CAAC,EAAC,MAAKA,GAAC,MAAIA,EAAE,CAAC,GAAE,KAAK,oBAAoB,QAAQ,KAAK,eAAe,aAAa,OAAQ,CAAAA,OAAG,KAAK,mBAAmBA,GAAE,UAAU,CAAE,EAAE,IAAK,CAAC,EAAC,MAAKA,GAAC,MAAIA,EAAE,CAAC,GAAE,KAAK,mBAAmB,QAAQ,KAAK,eAAe,YAAY,OAAQ,CAAAA,OAAG,KAAK,mBAAmBA,GAAE,UAAU,CAAE,EAAE,IAAK,CAAC,EAAC,MAAKA,GAAC,MAAIA,EAAE,CAAC,GAAE,KAAK,oBAAoB,QAAQ,KAAK,eAAe,aAAa,OAAQ,CAAAA,OAAG,KAAK,mBAAmBA,GAAE,UAAU,CAAE,EAAE,IAAK,CAAC,EAAC,MAAKA,GAAC,MAAIA,EAAE,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,UAAMC,KAAE,KAAK,eAAe,aAAa,IAAID,EAAC;AAAE,WAAM,CAAC,EAACC,MAAA,gBAAAA,GAAG,gBAAa,OAAKA,GAAE,kBAAgB,KAAK,mBAAmBA,GAAE,cAAc;AAAA,EAAE;AAAA,EAAC,gBAAgBD,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,gBAAgBF,IAAEE,EAAC,EAAE,KAAM,OAAMF,OAAG;AAAC,iBAAU,KAAKA,GAAE,WAAU;AAAC,QAAAC,GAAE,aAAa,IAAI,EAAE,IAAG,CAAC;AAAE,cAAMD,KAAE,EAAE,SAAO,MAAM,EAAE,EAAE,MAAM,IAAE,CAAC,GAAED,KAAE,EAAE,YAAU,MAAM,EAAE,EAAE,SAAS,IAAE,CAAC,GAAEgB,KAAE,EAAE,WAAS,MAAM,EAAE,EAAE,QAAQ,IAAE,CAAC,GAAEC,KAAE,EAAE,aAAW,CAAC;AAAE,YAAGf,GAAE,UAAU,KAAK,GAAGD,GAAE,IAAK,CAAAA,QAAI,EAAC,MAAKA,IAAE,YAAW,EAAE,GAAE,EAAG,CAAC,GAAEC,GAAE,aAAa,KAAK,GAAGF,GAAE,IAAK,CAAAC,QAAI,EAAC,MAAKA,IAAE,YAAW,EAAE,GAAE,EAAG,CAAC,GAAEC,GAAE,YAAY,KAAK,GAAGc,GAAE,IAAK,CAAAf,QAAI,EAAC,MAAKA,IAAE,YAAW,EAAE,GAAE,EAAG,CAAC,GAAEC,GAAE,aAAa,KAAK,GAAGe,GAAE,IAAK,CAAAhB,QAAI,EAAC,MAAKA,IAAE,YAAW,EAAE,GAAE,EAAG,CAAC,GAAE,EAAE,aAAY;AAAC,gBAAMA,KAAE,MAAM,KAAK,qBAAqB,EAAE,aAAY,KAAK,KAAK,KAAK;AAAE,gBAAM,KAAK,gBAAgBA,IAAEC,IAAEC,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAEC,IAAE;AAAC,WAAO,EAAED,IAAE,KAAK,MAAM,kBAAiB,KAAK,MAAM,iBAAgBC,EAAC,EAAE,KAAM,CAAAD,OAAG,EAAEA,GAAE,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,UAAMC,KAAE,KAAK,aAAa,IAAID,EAAC;AAAE,IAAAC,OAAI,KAAK,mBAAmB,YAAYA,EAAC,GAAE,KAAK,aAAa,OAAOD,EAAC;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAACE,GAAE,qCAAqC,CAAC,GAAEF,EAAC;AAAE,IAAM,IAAEA;", "names": ["b", "t", "e", "r", "n", "o", "w", "l", "T", "R", "j", "E", "D", "C", "y", "M", "A", "S", "f", "i", "a", "P", "f", "a", "e", "i", "t", "b", "w", "l", "u", "V", "v", "S", "I", "x", "C", "j", "k", "o", "r"]}