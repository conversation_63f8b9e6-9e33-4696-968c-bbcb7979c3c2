{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/materials/DefaultTechniqueConfiguration.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import{BindType as o}from\"../core/shaderTechnique/BindType.js\";import{parameter as t,ShaderTechniqueConfiguration as r}from\"../core/shaderTechnique/ShaderTechniqueConfiguration.js\";class s extends r{constructor(){super(...arguments),this.hasWebGL2Context=!1}}e([t({constValue:!0})],s.prototype,\"hasSliceHighlight\",void 0),e([t({constValue:!1})],s.prototype,\"hasSliceInVertexProgram\",void 0),e([t({constValue:!1})],s.prototype,\"instancedDoublePrecision\",void 0),e([t({constValue:!1})],s.prototype,\"useLegacyTerrainShading\",void 0),e([t({constValue:!1})],s.prototype,\"hasModelTransformation\",void 0),e([t({constValue:o.Pass})],s.prototype,\"pbrTextureBindType\",void 0),e([t()],s.prototype,\"hasWebGL2Context\",void 0);export{s as DefaultTechniqueConfiguration};\n"], "mappings": ";;;;;;;;;;;;AAIyO,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,mBAAiB;AAAA,EAAE;AAAC;AAAC,EAAE,CAAC,EAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAE,EAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAE,EAAE,WAAU,4BAA2B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAE,EAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAE,EAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,YAAW,EAAE,KAAI,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM;", "names": []}