import {
  An,
  E,
  O,
  P,
  a,
  en,
  rn,
  s as s2,
  sn,
  tn
} from "./chunk-UYAKJRPP.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  $,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/rasterFunctions/rasterProjectionHelper.js
var d;
function M(e, t2, n) {
  return !An(e, t2, n);
}
function w3(t2, n, o) {
  const i = M(t2, n, o);
  if (i && !en()) throw new s("rasterprojectionhelper-project", "projection engine is not loaded");
  return i;
}
!function(e) {
  e[e.None = 0] = "None", e[e.North = 1] = "North", e[e.South = 2] = "South", e[e.Both = 3] = "Both";
}(d || (d = {}));
var R = (e, t2, n, o = 0) => {
  if (1 === n[0]) return [0, 0];
  let i = 1, r2 = -1, s3 = 1, a2 = -1;
  for (let g = 0; g < e.length; g += 2) isNaN(e[g]) || (i = i > e[g] ? e[g] : i, r2 = r2 > e[g] ? r2 : e[g], s3 = s3 > e[g + 1] ? e[g + 1] : s3, a2 = a2 > e[g + 1] ? a2 : e[g + 1]);
  const { cols: l, rows: c } = t2, f2 = (r2 - i) / l / n[0], u = (a2 - s3) / c / n[1], m = 2 * o;
  let h = 0, x = false, p = [0, 0];
  for (let g = 0; g < l - 3; g++) {
    for (let t3 = 0; t3 < c - 3; t3++) {
      const n2 = g * c * 2 + 2 * t3, o2 = (e[n2] + e[n2 + 4] + e[n2 + 4 * c] + e[n2 + 4 * c + 4]) / 4, i2 = (e[n2 + 1] + e[n2 + 5] + e[n2 + 4 * c + 1] + e[n2 + 4 * c + 5]) / 4, r3 = Math.abs((o2 - e[n2 + 2 * c + 2]) / f2), s4 = Math.abs((i2 - e[n2 + 2 * c + 3]) / u);
      if (r3 + s4 > h && (h = r3 + s4, p = [r3, s4]), m && h > m) {
        x = true;
        break;
      }
    }
    if (x) break;
  }
  return p;
};
var P2 = { 3395: 20037508342789244e-9, 3410: 17334193943686873e-9, 3857: 20037508342788905e-9, 3975: 17367530445161372e-9, 4087: 20037508342789244e-9, 4088: 20015108787169147e-9, 6933: 17367530445161372e-9, 32662: 20037508342789244e-9, 53001: 2001508679602057e-8, 53002: 1000754339801029e-8, 53003: 2001508679602057e-8, 53004: 2001508679602057e-8, 53016: 14152803599503474e-9, 53017: 17333573624304302e-9, 53034: 2001508679602057e-8, 53079: 20015114352186374e-9, 53080: 20015114352186374e-9, 54001: 20037508342789244e-9, 54002: 10018754171394624e-9, 54003: 20037508342789244e-9, 54004: 20037508342789244e-9, 54016: 14168658027268292e-9, 54017: 1736753044516137e-8, 54034: 20037508342789244e-9, 54079: 20037508342789244e-9, 54080: 20037508342789244e-9, 54100: 20037508342789244e-9, 54101: 20037508342789244e-9 };
var S = 32;
var b = 4;
var G = b;
var N = /* @__PURE__ */ new Map();
var E2 = /* @__PURE__ */ new Map();
var k = 500;
async function T() {
  en() || await tn();
}
function v2(e, t2, n) {
  if (!w3(e.spatialReference, t2)) return null;
  return n ? sn(t2, e.spatialReference, e) : sn(e.spatialReference, t2, e);
}
function C(e, i, r2, s3 = null) {
  const a2 = e.spatialReference;
  if (a2.equals(i)) return e;
  w3(a2, i, s3);
  const l = r2.center, c = new w2({ xmin: l.x - e.x / 2, xmax: l.x + e.x / 2, ymin: l.y - e.y / 2, ymax: l.y + e.y / 2, spatialReference: a2 }), f2 = rn(c, i, s3), u = U(i);
  let m;
  if (t(f2) || r(u) && f2.width >= u) {
    const t2 = $(a2) / $(i);
    m = { x: e.x * t2, y: e.y * t2 };
  } else m = { x: f2.width, y: f2.height };
  return m;
}
function _(e, t2 = 0.01) {
  return $(e) ? t2 / $(e) : 0;
}
function j(e, t2, n = null, o = true) {
  const i = e.spatialReference;
  if (i.equals(t2)) return e;
  w3(i, t2, n);
  const r2 = rn(e, t2, n);
  return o && r2 ? (z([e], [r2], i, t2), r2) : r2;
}
function z(e, t2, o, i) {
  const r2 = D(o, true), s3 = D(i, true), a2 = _(o, k), l = _(i, k);
  if (a2 && r(r2) && r(s3)) for (let n = 0; n < e.length; n++) {
    const o2 = t2[n];
    if (!o2) continue;
    const { x: i2 } = e[n], { x: c } = o2;
    c >= s3[1] - l && Math.abs(i2 - r2[0]) < a2 ? o2.x -= s3[1] - s3[0] : c <= s3[0] + l && Math.abs(i2 - r2[1]) < a2 && (o2.x += s3[1] - s3[0]);
  }
}
function W(e) {
  const { inSR: t2, outSR: o, datumTransformation: i, preferPE: r2 } = e;
  if (t2.equals(o)) {
    const { points: t3 } = F(e, null);
    return t3;
  }
  if (t2.isWebMercator && o.isWGS84 || t2.isWGS84 && o.isWebMercator) return I(e);
  if (w3(t2, o, i) && r2) {
    if (t2.isGeographic) return O2(e);
    const o2 = A(t2);
    if (r(o2)) return O2(e);
  }
  return L(e);
}
function L(e) {
  const { points: t2 } = F(e, null), { inSR: n, outSR: o, datumTransformation: i } = e, r2 = t2.map((e2) => new w(e2[0], e2[1], n)), s3 = rn(r2, o, i);
  return i && z(r2, s3, n, o), s3.map((e2) => e2 ? [e2.x, e2.y] : [NaN, NaN]);
}
function O2(e) {
  const { inSR: t2, outSR: o, datumTransformation: l } = e, c = A(t2), { points: f2, mask: u } = F(e, c);
  if (!t2.isGeographic) {
    const e2 = t2.wkid ? E.coordsys(t2.wkid) : E.fromString(t2.isGeographic ? s2.PE_TYPE_GEOGCS : s2.PE_TYPE_PROJCS, t2.wkt);
    P.projToGeog(e2, f2.length, f2);
  }
  if (r(l) && l.steps.length) {
    let e2;
    const t3 = 179.9955;
    if (o.isGeographic && (e2 = f2.map(([e3]) => e3 > t3 ? 1 : e3 < -t3 ? -1 : 0)), l.steps.forEach((e3) => {
      const t4 = e3.wkid ? E.geogtran(e3.wkid) : E.fromString(s2.PE_TYPE_GEOGTRAN, e3.wkt);
      a.geogToGeog(t4, f2.length, f2, null, e3.isInverse ? s2.PE_TRANSFORM_2_TO_1 : s2.PE_TRANSFORM_1_TO_2);
    }), e2) for (let n = 0; n < f2.length; n++) {
      const o2 = e2[n], i = f2[n][0], r2 = i > t3 ? 1 : i < -t3 ? -1 : 0;
      o2 && r2 && o2 !== r2 && (f2[n][0] = o2 > 0 ? i + 360 : i - 360);
    }
  }
  if (!o.isGeographic) {
    const e2 = A(o, true), t3 = r(e2) && e2.isEnvelope ? [e2.bbox[1], e2.bbox[3]] : [-90, 90];
    Y(f2, t3);
    const a2 = o.wkid ? E.coordsys(o.wkid) : E.fromString(o.isGeographic ? s2.PE_TYPE_GEOGCS : s2.PE_TYPE_PROJCS, o.wkt);
    P.geogToProj(a2, f2.length, f2);
  }
  let m = f2;
  if (u && f2.length !== u.length) {
    m = [];
    for (let e2 = 0, t3 = 0; e2 < u.length; e2++) u[e2] ? m.push(f2[t3++]) : m.push([NaN, NaN]);
  }
  return m;
}
function I(e) {
  const { cols: t2, rows: n, xres: o, yres: i, usePixelCenter: r2, inSR: s3, outSR: a2 } = e;
  let { xmin: l, ymax: c } = e;
  r2 && (l += o / 2, c -= i / 2);
  const f2 = [], u = [], m = Math.max(t2, n);
  for (let g = 0; g < m; g++) {
    const e2 = l + o * Math.min(t2, g), r3 = c - i * Math.min(n, g), m2 = rn(new w({ x: e2, y: r3, spatialReference: s3 }), a2);
    g <= t2 && f2.push(m2.x), g <= n && u.push(m2.y);
  }
  const x = [];
  for (let h = 0; h < t2; h++) for (let e2 = 0; e2 < n; e2++) x.push([f2[h], u[e2]]);
  return x;
}
function A(e, t2 = false) {
  let n = e.wkid || e.wkt;
  if (!n || e.isGeographic) return null;
  if (n = String(n), N.has(n)) {
    const e2 = N.get(n);
    return t2 ? e2 == null ? void 0 : e2.gcs : e2 == null ? void 0 : e2.pcs;
  }
  const o = e.wkid ? E.coordsys(e.wkid) : E.fromString(e.isGeographic ? s2.PE_TYPE_GEOGCS : s2.PE_TYPE_PROJCS, e.wkt), s3 = B(o, _(e, 1e-4)), a2 = B(o, 0, true);
  return N.set(n, { pcs: s3, gcs: a2 }), t2 ? a2 : s3;
}
function B(e, t2 = 0, n = false) {
  const o = O.generate(e), i = n ? e.horizonGcsGenerate() : e.horizonPcsGenerate();
  if (!o || !(i == null ? void 0 : i.length)) return null;
  let r2 = false, s3 = i.find((e2) => 1 === e2.getInclusive() && 1 === e2.getKind());
  if (!s3) {
    if (s3 = i.find((e2) => 1 === e2.getInclusive() && 0 === e2.getKind()), !s3) return null;
    r2 = true;
  }
  const a2 = n ? 0 : (2 === o.getNorthPoleLocation() ? 1 : 0) | (2 === o.getSouthPoleLocation() ? 2 : 0), c = o.isPannableRectangle(), f2 = s3.getCoord();
  if (r2) return { isEnvelope: r2, isPannable: c, vertices: f2, coef: null, bbox: [f2[0][0] - t2, f2[0][1] - t2, f2[1][0] + t2, f2[1][1] + t2], poleLocation: a2 };
  let u = 0;
  const m = [];
  let [h, x] = f2[0], [p, g] = f2[0];
  for (let l = 0, y = f2.length; l < y; l++) {
    u++, u === y && (u = 0);
    const [e2, t3] = f2[l], [n2, o2] = f2[u];
    if (o2 === t3) m.push([e2, n2, t3, o2, 2]);
    else {
      const i2 = (n2 - e2) / (o2 - t3 || 1e-4), r3 = e2 - i2 * t3;
      t3 < o2 ? m.push([i2, r3, t3, o2, 0]) : m.push([i2, r3, o2, t3, 1]);
    }
    h = h < e2 ? h : e2, x = x < t3 ? x : t3, p = p > e2 ? p : e2, g = g > t3 ? g : t3;
  }
  return { isEnvelope: false, isPannable: c, vertices: f2, coef: m, bbox: [h, x, p, g], poleLocation: a2 };
}
function F(e, n) {
  const o = [], { cols: i, rows: r2, xres: s3, yres: a2, usePixelCenter: l } = e;
  let { xmin: c, ymax: f2 } = e;
  if (l && (c += s3 / 2, f2 -= a2 / 2), t(n)) {
    for (let e2 = 0; e2 < i; e2++) for (let t2 = 0; t2 < r2; t2++) o.push([c + s3 * e2, f2 - a2 * t2]);
    return { points: o };
  }
  const u = new Uint8Array(i * r2);
  if (n.isEnvelope) {
    const { bbox: [e2, t2, l2, m2] } = n;
    for (let h2 = 0, x = 0; h2 < i; h2++) {
      const i2 = c + s3 * h2, p = n.isPannable || i2 >= e2 && i2 <= l2;
      for (let e3 = 0; e3 < r2; e3++, x++) {
        const n2 = f2 - a2 * e3;
        p && n2 >= t2 && n2 <= m2 && (o.push([i2, n2]), u[x] = 1);
      }
    }
    return { points: o, mask: u };
  }
  const m = n.coef, h = [];
  for (let t2 = 0; t2 < r2; t2++) {
    const e2 = f2 - a2 * t2, n2 = [], o2 = [];
    for (let t3 = 0; t3 < m.length; t3++) {
      const [i3, r3, s4, a3, l2] = m[t3];
      if (e2 === s4 && s4 === a3) n2.push(i3), n2.push(r3), o2.push(2), o2.push(2);
      else if (e2 >= s4 && e2 <= a3) {
        const t4 = i3 * e2 + r3;
        n2.push(t4), o2.push(l2);
      }
    }
    let i2 = n2;
    if (n2.length > 2) {
      let e3 = 2 === o2[0] ? 0 : o2[0], t3 = n2[0];
      i2 = [];
      for (let r3 = 1; r3 < o2.length; r3++) 2 === o2[r3] && r3 !== o2.length - 1 || (o2[r3] !== e3 && (i2.push(0 === e3 ? Math.min(t3, n2[r3 - 1]) : Math.max(t3, n2[r3 - 1])), e3 = o2[r3], t3 = n2[r3]), r3 === o2.length - 1 && i2.push(0 === o2[r3] ? Math.min(t3, n2[r3]) : Math.max(t3, n2[r3])));
      i2.sort((e4, t4) => e4 - t4);
    } else n2[0] > n2[1] && (i2 = [n2[1], n2[0]]);
    h.push(i2);
  }
  for (let t2 = 0, x = 0; t2 < i; t2++) {
    const e2 = c + s3 * t2;
    for (let t3 = 0; t3 < r2; t3++, x++) {
      const n2 = f2 - a2 * t3, i2 = h[t3];
      if (2 === i2.length) e2 >= i2[0] && e2 <= i2[1] && (o.push([e2, n2]), u[x] = 1);
      else if (i2.length > 2) {
        let t4 = false;
        for (let n3 = 0; n3 < i2.length; n3 += 2) if (e2 >= i2[n3] && e2 <= i2[n3 + 1]) {
          t4 = true;
          break;
        }
        t4 && (o.push([e2, n2]), u[x] = 1);
      }
    }
  }
  return { points: o, mask: u };
}
function Y(e, t2) {
  const [n, o] = t2;
  for (let i = 0; i < e.length; i++) {
    const t3 = e[i][1];
    (t3 < n || t3 > o) && (e[i] = [NaN, NaN]);
  }
}
function q(e) {
  const n = U(e[0].spatialReference);
  if (e.length < 2 || t(n)) return e[0];
  let { xmin: o, xmax: i, ymin: r2, ymax: s3 } = e[0];
  for (let t2 = 1; t2 < e.length; t2++) {
    const o2 = e[t2];
    i = o2.xmax + n * t2, r2 = Math.min(r2, o2.ymin), s3 = Math.max(s3, o2.ymax);
  }
  return new w2({ xmin: o, xmax: i, ymin: r2, ymax: s3, spatialReference: e[0].spatialReference });
}
function J(e, o, i = null, r2 = true) {
  const s3 = e.spatialReference;
  if (s3.equals(o)) return e;
  const a2 = Q(e), l = U(s3, true), c = U(o);
  if (0 === a2 || t(l) || t(c)) {
    const a3 = K(e, o, i, r2);
    if (t(l) && r(c) && Math.abs(a3.width - c) < _(o) && en()) {
      const t2 = A(s3);
      if (r(t2) && t2.poleLocation === d.None && e.width < (t2.bbox[2] - t2.bbox[0]) / 2) return X(e, o) || a3;
    }
    return a3;
  }
  const u = e.clone().normalize();
  if (1 === u.length && e.xmax < l && e.xmax - l / 2 > _(s3)) {
    const { xmin: t2, xmax: n } = e;
    for (let o2 = 0; o2 <= a2; o2++) {
      const i2 = 0 === o2 ? t2 : -l / 2, r3 = o2 === a2 ? n - l * o2 : l / 2;
      u[o2] = new w2({ xmin: i2, xmax: r3, ymin: e.ymin, ymax: e.ymax, spatialReference: s3 });
    }
  }
  return q(u.map((e2) => K(e2, o, i, r2)).filter(r));
}
function X(e, n) {
  const o = U(n);
  if (t(o)) return null;
  let { xmin: i, ymin: r2, xmax: s3, ymax: a2 } = e;
  const l = e.spatialReference, c = new v({ spatialReference: l, rings: [[[i, r2], [s3, r2], [s3, a2], [i, a2], [i, r2]]] }), f2 = rn(c, n);
  if (2 !== f2.rings.length || !f2.rings[0].length || !f2.rings[1].length) return null;
  const { rings: u } = f2, m = _(l), p = new w2({ spatialReference: n });
  for (let t2 = 0; t2 < 2; t2++) {
    i = s3 = u[t2][0][0], r2 = a2 = u[t2][0][1];
    for (let e2 = 0; e2 < u[t2].length; e2++) i = i > u[t2][e2][0] ? u[t2][e2][0] : i, s3 = s3 < u[t2][e2][0] ? u[t2][e2][0] : s3, r2 = r2 > u[t2][e2][1] ? u[t2][e2][1] : r2, a2 = a2 < u[t2][e2][1] ? u[t2][e2][1] : a2;
    if (0 === t2) p.ymin = r2, p.ymax = a2, p.xmin = i, p.xmax = s3;
    else if (p.ymin = Math.min(p.ymin, r2), p.ymax = Math.max(p.ymax, a2), Math.abs(s3 - o / 2) < m) p.xmin = i, p.xmax = p.xmax + o;
    else {
      if (!(Math.abs(i + o / 2) < m)) return null;
      p.xmax = s3 + o;
    }
  }
  return p;
}
function K(e, n, o = null, i = true, r2 = true) {
  const s3 = e.spatialReference;
  if (s3.equals(n) || !n) return e;
  w3(s3, n, o);
  const a2 = rn(e, n, o);
  if (r2 && n.isWebMercator && a2 && (a2.ymax = Math.min(20037508342787e-6, a2.ymax), a2.ymin = Math.max(-20037508342787e-6, a2.ymin), a2.ymin >= a2.ymax)) return null;
  if (!i || !a2) return a2;
  const l = D(s3, true), c = D(n, true);
  if (t(l) || t(c)) return a2;
  const f2 = _(s3, 1e-3), u = _(s3, k), m = _(n, 1e-3);
  if (Math.abs(a2.xmin - c[0]) < m && Math.abs(a2.xmax - c[1]) < m) {
    const t2 = Math.abs(e.xmin - l[0]), i2 = Math.abs(l[1] - e.xmax);
    if (t2 < f2 && i2 > u) {
      a2.xmin = c[0];
      const t3 = [];
      t3.push(new w(e.xmax, e.ymin, s3)), t3.push(new w(e.xmax, (e.ymin + e.ymax) / 2, s3)), t3.push(new w(e.xmax, e.ymax, s3));
      const i3 = t3.map((e2) => j(e2, n, o)).filter((e2) => !isNaN(e2 == null ? void 0 : e2.x)).map((e2) => e2.x);
      a2.xmax = Math.max.apply(null, i3);
    }
    if (i2 < f2 && t2 > u) {
      a2.xmax = c[1];
      const t3 = [];
      t3.push(new w(e.xmin, e.ymin, s3)), t3.push(new w(e.xmin, (e.ymin + e.ymax) / 2, s3)), t3.push(new w(e.xmin, e.ymax, s3));
      const i3 = t3.map((e2) => j(e2, n, o)).filter((e2) => !isNaN(e2 == null ? void 0 : e2.x)).map((e2) => e2.x);
      a2.xmin = Math.min.apply(null, i3);
    }
  } else {
    const e2 = _(n, 1e-3);
    Math.abs(a2.xmin - c[0]) < e2 && (a2.xmin = c[0]), Math.abs(a2.xmax - c[1]) < e2 && (a2.xmax = c[1]);
  }
  return a2;
}
function U(e, t2 = false) {
  if (!e) return null;
  const n = t2 ? 20037508342787e-6 : 20037508342788905e-9;
  return e.isWebMercator ? 2 * n : e.wkid && e.isGeographic ? 360 : 2 * P2[e.wkid] || null;
}
function D(e, t2 = false) {
  if (e.isGeographic) return [-180, 180];
  const o = U(e, t2);
  return r(o) ? [-o / 2, o / 2] : null;
}
function H(e, t2, n, o) {
  let i = (e - t2) / n;
  return i - Math.floor(i) != 0 ? i = Math.floor(i) : o && (i -= 1), i;
}
function Q(e, n = false) {
  const o = U(e.spatialReference);
  if (t(o)) return 0;
  const i = n ? 0 : -(o / 2), r2 = _(e.spatialReference), s3 = !n && Math.abs(e.xmax - o / 2) < r2 ? o / 2 : e.xmax, a2 = !n && Math.abs(e.xmin + o / 2) < r2 ? -o / 2 : e.xmin;
  return H(s3, i, o, true) - H(a2, i, o, false);
}
function V(e) {
  const o = e.storageInfo.origin.x, i = U(e.spatialReference, true);
  if (t(i)) return { originX: o, halfWorldWidth: null, pyramidsInfo: null };
  const r2 = i / 2, { nativePixelSize: s3, storageInfo: a2, extent: l } = e, { maximumPyramidLevel: c, blockWidth: f2, pyramidScalingFactor: u } = a2;
  let m = s3.x;
  const h = [], x = r(e.transform) && "gcs-shift" === e.transform.type, p = o + (x ? 0 : r2), g = x ? i - o : r2 - o;
  for (let t2 = 0; t2 <= c; t2++) {
    const e2 = (l.xmax - o) / m / f2, t3 = e2 - Math.floor(e2) == 0 ? e2 : Math.ceil(e2), n = g / m / f2, i2 = n - Math.floor(n) == 0 ? n : Math.ceil(n), r3 = Math.floor(p / m / f2), s4 = Math.round(p / m) % f2, a3 = (f2 - Math.round(g / m) % f2) % f2;
    h.push({ resolutionX: m, blockWidth: f2, datsetColumnCount: t3, worldColumnCountFromOrigin: i2, leftMargin: s4, rightPadding: a3, originColumnOffset: r3 }), m *= u;
  }
  return { originX: o, halfWorldWidth: r2, pyramidsInfo: h, hasGCSSShiftTransform: x };
}
function Z(e) {
  if (!e || e.isGeographic) return e;
  const t2 = String(e.wkid || e.wkt);
  let n;
  if (E2.has(t2)) n = E2.get(t2);
  else {
    n = (e.wkid ? E.coordsys(e.wkid) : E.fromString(s2.PE_TYPE_PROJCS, e.wkt)).getGeogcs().getCode(), E2.set(t2, n);
  }
  return new f({ wkid: n });
}
function $2(e) {
  const t2 = e.isAdaptive && null == e.spacing;
  let o = e.spacing || [S, S], i = ee(e), r2 = { cols: i.size[0] + 1, rows: i.size[1] + 1 };
  const s3 = i.outofBoundPointCount > 0 && i.outofBoundPointCount < i.offsets.length / 2;
  let a2 = i.outofBoundPointCount === i.offsets.length / 2 || t2 && s3 ? [0, 0] : R(i.offsets, r2, o, G);
  const l = (a2[0] + a2[1]) / 2, c = e.projectedExtent.spatialReference, f2 = e.srcBufferExtent.spatialReference;
  if (t2 && (s3 || l > G)) {
    M(c, f2, e.datumTransformation) && (c.isGeographic || r(A(c))), o = [b, b], i = ee({ ...e, spacing: o }), r2 = { cols: i.size[0] + 1, rows: i.size[1] + 1 }, a2 = R(i.offsets, r2, o, G);
  }
  if (i.error = a2, o[0] > 1 && (i.coefficients = te(i.offsets, r2, s3)), e.includeGCSGrid && !c.isGeographic && !c.isWebMercator) if (f2.isGeographic) i.gcsGrid = { offsets: i.offsets, coefficients: i.coefficients, spacing: o };
  else {
    const t3 = A(c);
    if (r(t3) && !t3.isEnvelope) {
      const t4 = Z(c), n = J(e.projectedExtent, t4), { offsets: a3 } = ee({ ...e, srcBufferExtent: n, spacing: o }), l2 = te(a3, r2, s3);
      i.gcsGrid = { offsets: a3, coefficients: l2, spacing: o };
    }
  }
  return i;
}
function ee(e) {
  const { projectedExtent: t2, srcBufferExtent: o, pixelSize: i, datumTransformation: r2, rasterTransform: s3 } = e, a2 = t2.spatialReference, l = o.spatialReference, c = w3(a2, l), { xmin: f2, ymin: u, xmax: m, ymax: h } = t2, x = U(l), g = r(x) && (e.hasWrapAround || "gcs-shift" === (s3 == null ? void 0 : s3.type)), y = e.spacing || [S, S], d2 = y[0] * i.x, M2 = y[1] * i.y, R2 = 1 === y[0], P3 = Math.ceil((m - f2) / d2 - 0.1 / y[0]) + (R2 ? 0 : 1), G2 = Math.ceil((h - u) / M2 - 0.1 / y[1]) + (R2 ? 0 : 1), N2 = W({ cols: P3, rows: G2, xmin: f2, ymax: h, xres: d2, yres: M2, inSR: a2, outSR: l, datumTransformation: r2, preferPE: y[0] <= b, usePixelCenter: R2 }), E3 = [];
  let T2, v3 = 0;
  const C2 = R2 ? -1 : NaN, { xmin: j2, xmax: z2, ymax: L2, width: O3, height: I2 } = o, B2 = _(l, k), F2 = r(x) && j2 > 0 && z2 > x / 2;
  let Y2 = false;
  if (c) {
    const e2 = A(a2);
    Y2 = r(e2) && e2.poleLocation > 0;
  }
  for (let n = 0; n < P3; n++) {
    const e2 = [];
    for (let t3 = 0; t3 < G2; t3++) {
      let o2 = N2[n * G2 + t3];
      if (g && o2[0] > z2 && o2[0] > x / 2 - B2 ? o2[0] -= x : g && 0 === n && o2[0] < 0 && F2 && !s3 && (o2[0] += x), !o2 || isNaN(o2[0]) || isNaN(o2[1])) E3.push(C2), E3.push(C2), e2.push(null), v3++;
      else {
        if (s3) {
          const e3 = s3.inverseTransform(new w({ x: o2[0], y: o2[1], spatialReference: l }));
          o2 = [e3.x, e3.y];
        }
        e2.push(o2), n > 0 && g && T2[t3] && o2[0] < T2[t3][0] && (o2[0] += x, Y2 && o2[0] > z2 && o2[0] > x && (o2[0] -= x)), E3.push((o2[0] - j2) / O3), E3.push((L2 - o2[1]) / I2);
      }
    }
    T2 = e2;
  }
  return { offsets: E3, error: null, coefficients: null, outofBoundPointCount: v3, spacing: y, size: R2 ? [P3, G2] : [P3 - 1, G2 - 1] };
}
function te(e, t2, n) {
  const { cols: o, rows: i } = t2, r2 = new Float32Array((o - 1) * (i - 1) * 2 * 6), s3 = new Float32Array([-0, -1, 1, -1, 1, -0, 1, -0, -0]), a2 = new Float32Array([-1, 1, 0, 0, -1, 1, 1, 0, 0]);
  for (let l = 0; l < o - 1; l++) {
    for (let t3 = 0; t3 < i - 1; t3++) {
      let n2 = l * i * 2 + 2 * t3;
      const c = e[n2], f2 = e[n2 + 1], u = e[n2 + 2], m = e[n2 + 3];
      n2 += 2 * i;
      const h = e[n2], x = e[n2 + 1], p = e[n2 + 2], g = e[n2 + 3];
      let y = 0, d2 = 12 * (t3 * (o - 1) + l);
      for (let e2 = 0; e2 < 3; e2++) r2[d2++] = s3[y++] * c + s3[y++] * u + s3[y++] * p;
      y = 0;
      for (let e2 = 0; e2 < 3; e2++) r2[d2++] = s3[y++] * f2 + s3[y++] * m + s3[y++] * g;
      y = 0;
      for (let e2 = 0; e2 < 3; e2++) r2[d2++] = a2[y++] * c + a2[y++] * h + a2[y++] * p;
      y = 0;
      for (let e2 = 0; e2 < 3; e2++) r2[d2++] = a2[y++] * f2 + a2[y++] * x + a2[y++] * g;
    }
    if (n) for (let e2 = 0; e2 < r2.length; e2++) isNaN(r2[e2]) && (r2[e2] = -1);
  }
  return r2;
}
function ne(e) {
  const t2 = e.clone().normalize();
  return 1 === t2.length ? t2[0] : q(t2);
}
function oe(e, t2, i) {
  const { storageInfo: r2, pixelSize: s3 } = t2;
  let a2 = 0, l = false;
  const { pyramidResolutions: c } = r2;
  if (r(c) && c.length) {
    const n = (e.x + e.y) / 2, r3 = c[c.length - 1], f3 = (r3.x + r3.y) / 2, u2 = (s3.x + s3.y) / 2;
    if (n <= u2) a2 = 0;
    else if (n >= f3) a2 = c.length, l = n / f3 > 8;
    else {
      let e2, t3 = u2;
      for (let o = 1; o <= c.length; o++) {
        if (e2 = (c[o - 1].x + c[o - 1].y) / 2, n <= e2) {
          n === e2 ? a2 = o : "down" === i ? (a2 = o - 1, l = n / t3 > 8) : a2 = "up" === i || n - t3 > e2 - n || n / t3 > 2 ? o : o - 1;
          break;
        }
        t3 = e2;
      }
    }
    const m2 = 0 === a2 ? s3 : c[a2 - 1];
    if (l) {
      Math.min(m2.x, m2.y) * $(t2.spatialReference) > 19567 && (l = false);
    }
    return { pyramidLevel: a2, pyramidResolution: new w({ x: m2.x, y: m2.y, spatialReference: t2.spatialReference }), excessiveReading: l };
  }
  const f2 = Math.log(e.x / s3.x) / Math.LN2, u = Math.log(e.y / s3.y) / Math.LN2, m = t2.storageInfo.maximumPyramidLevel || 0;
  a2 = "down" === i ? Math.floor(Math.min(f2, u)) : "up" === i ? Math.ceil(Math.max(f2, u)) : Math.round((f2 + u) / 2), a2 < 0 ? a2 = 0 : a2 > m && (l = a2 > m + 3, a2 = m);
  const h = 2 ** a2;
  return { pyramidLevel: a2, pyramidResolution: new w({ x: h * t2.nativePixelSize.x, y: h * t2.nativePixelSize.y, spatialReference: t2.spatialReference }), excessiveReading: l };
}
function ie(e, t2, i = 512, r2 = true) {
  var _a;
  const { extent: s3, spatialReference: a2, pixelSize: l } = e, c = C(new w({ x: l.x, y: l.y, spatialReference: a2 }), t2, s3);
  if (null == c) return { projectedPixelSize: null, scales: null, srcResolutions: null, isCustomTilingScheme: false };
  const f2 = (c.x + c.y) / 2, u = $(t2), m = f2 * u * 96 * 39.37, h = t2.isGeographic ? 256 / i * 2958287637958547e-7 : 256 / i * 591657527591555e-6;
  let x = "vector-magdir" === e.dataType || "vector-uv" === e.dataType;
  const g = J(s3, t2), y = Math.min(Math.ceil(Math.log(Math.min(e.width, e.height) / 32) / Math.LN2), Math.ceil(Math.log(h / 2 / m) / Math.LN2));
  if (!x && r2 && (t2.isGeographic || t2.isWebMercator) && (x = g.xmin * g.xmax < 0, !x && y < 3)) {
    const e2 = U(t2);
    if (r(e2)) {
      const t3 = 2 ** y * f2 * i, n = Math.ceil(e2 / t3);
      x = 1 === n || 2 === n && e2 / 2 - g.xmax < t3;
    }
  }
  let d2, M2 = m;
  const w4 = 1.001, R2 = Math.min(2, Math.max(1.414, ((_a = e.storageInfo) == null ? void 0 : _a.pyramidScalingFactor) || 2));
  if (x) {
    M2 = h;
    const e2 = t2.isGeographic ? 1341104507446289e-21 : 0.29858214164761665, n = e2 * (96 * u * 39.37), o = t2.isGeographic ? 4326 : 3857;
    d2 = C(new w({ x: e2, y: e2, spatialReference: { wkid: o } }), a2, g), d2.x *= M2 / n, d2.y *= M2 / n;
  } else {
    d2 = { x: l.x, y: l.y };
    let e2 = 0;
    for (; M2 < h * (w4 / 2) && e2 < y; ) e2++, M2 *= R2, d2.x *= R2, d2.y *= R2;
    Math.max(M2, h) / Math.min(M2, h) <= w4 && (M2 = h);
  }
  const P3 = [M2], S2 = [{ x: d2.x, y: d2.y }], b2 = 70.5310735, G2 = Math.min(b2, m) / w4;
  for (; M2 >= G2; ) M2 /= R2, d2.x /= R2, d2.y /= R2, P3.push(M2), S2.push({ x: d2.x, y: d2.y });
  return { projectedPixelSize: c, scales: P3, srcResolutions: S2, isCustomTilingScheme: !x };
}

export {
  M,
  T,
  v2 as v,
  C,
  j,
  J,
  U,
  Q,
  V,
  $2 as $,
  ne,
  oe,
  ie
};
//# sourceMappingURL=chunk-HK26M5VD.js.map
