import {
  d
} from "./chunk-5G7LCOCX.js";
import {
  f
} from "./chunk-XBS7QZIQ.js";
import {
  v
} from "./chunk-6GKVSPTV.js";
import {
  x
} from "./chunk-KE7SPCM7.js";

// node_modules/@arcgis/core/rest/query/executeTopFeaturesQuery.js
async function s(s2, p, u, a) {
  const m = f(s2), i = { ...a }, { data: f2 } = await d(m, v.from(p), u, i);
  return x.fromJSON(f2);
}

export {
  s
};
//# sourceMappingURL=chunk-YJHRRAXO.js.map
