{"version": 3, "sources": ["../../@arcgis/core/layers/support/ElevationTileData.js", "../../@arcgis/core/layers/support/LercDecoder.js", "../../@arcgis/core/layers/ElevationLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as a,unwrap as t,isSome as s}from\"../../core/maybe.js\";class e{constructor(a,t,s,e){this._hasNoDataValues=null,this._minValue=null,this._maxValue=null,\"pixelData\"in a?(this.values=a.pixelData,this.width=a.width,this.height=a.height,this.noDataValue=a.noDataValue):(this.values=a,this.width=t,this.height=s,this.noDataValue=e)}get hasNoDataValues(){if(a(this._hasNoDataValues)){const a=this.noDataValue;this._hasNoDataValues=this.values.includes(a)}return this._hasNoDataValues}get minValue(){return this._ensureBounds(),t(this._minValue)}get maxValue(){return this._ensureBounds(),t(this._maxValue)}_ensureBounds(){if(s(this._minValue))return;const{noDataValue:a,values:t}=this;let e=1/0,i=-1/0,h=!0;for(const s of t)s===a?this._hasNoDataValues=!0:(e=s<e?s:e,i=s>i?s:i,h=!1);h?(this._minValue=0,this._maxValue=0):(this._minValue=e,this._maxValue=i>-3e38?i:0)}}export{e as ElevationTileData};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrap as e,isSome as r}from\"../../core/maybe.js\";import{WorkerHandle as t}from\"../../core/workers/WorkerHandle.js\";class s extends t{constructor(e=null){super(\"LercWorker\",\"_decode\",{_decode:e=>[e.buffer]},e,{strategy:\"dedicated\"}),this.schedule=e,this.ref=0}decode(e,r,t){return e&&0!==e.byteLength?this.invoke({buffer:e,options:r},t):Promise.resolve(null)}release(){--this.ref<=0&&(o.forEach(((e,r)=>{e===this&&o.delete(r)})),this.destroy())}}const o=new Map;function n(t=null){let n=o.get(e(t));return n||(r(t)?(n=new s((e=>t.immediate.schedule(e))),o.set(t,n)):(n=new s,o.set(null,n))),++n.ref,n}export{n as acquireDecoder};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import r from\"../request.js\";import t from\"../core/Error.js\";import{releaseMaybe as o,isSome as i}from\"../core/maybe.js\";import{MultiOriginJSONMixin as s}from\"../core/MultiOriginJSONSupport.js\";import{throwIfAbortError as a,throwIfAborted as l}from\"../core/promiseUtils.js\";import{objectToQuery as p}from\"../core/urlUtils.js\";import{property as n}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{reader as c}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as d}from\"../core/accessorSupport/decorators/subclass.js\";import y from\"../geometry/HeightModelInfo.js\";import h from\"./Layer.js\";import{ArcGISCachedService as m}from\"./mixins/ArcGISCachedService.js\";import{ArcGISService as u}from\"./mixins/ArcGISService.js\";import{OperationalLayer as v}from\"./mixins/OperationalLayer.js\";import{PortalLayer as f}from\"./mixins/PortalLayer.js\";import{url as g}from\"./support/commonProperties.js\";import{ElevationTileData as j}from\"./support/ElevationTileData.js\";import{acquireDecoder as S}from\"./support/LercDecoder.js\";let w=class extends(m(u(v(f(s(h)))))){constructor(...e){super(...e),this.copyright=null,this.heightModelInfo=null,this.path=null,this.minScale=void 0,this.maxScale=void 0,this.opacity=1,this.operationalLayerType=\"ArcGISTiledElevationServiceLayer\",this.sourceJSON=null,this.type=\"elevation\",this.url=null,this.version=null,this._lercDecoder=S()}normalizeCtorArgs(e,r){return\"string\"==typeof e?{url:e,...r}:e}destroy(){this._lercDecoder=o(this._lercDecoder)}readVersion(e,r){let t=r.currentVersion;return t||(t=9.3),t}load(e){const r=i(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:[\"Image Service\"],supportsData:!1,validateItem:e=>{for(let r=0;r<e.typeKeywords.length;r++)if(\"elevation 3d layer\"===e.typeKeywords[r].toLowerCase())return!0;throw new t(\"portal:invalid-layer-item-type\",\"Invalid layer item type '${type}', expected '${expectedType}' \",{type:\"Image Service\",expectedType:\"Image Service Elevation 3D Layer\"})}},e).catch(a).then((()=>this._fetchImageService(r)))),Promise.resolve(this)}fetchTile(e,t,o,s){const a=i((s=s||{signal:null}).signal)?s.signal:s.signal=(new AbortController).signal,l={responseType:\"array-buffer\",signal:a},p={noDataValue:s.noDataValue,returnFileInfo:!0};return this.load().then((()=>this._fetchTileAvailability(e,t,o,s))).then((()=>r(this.getTileUrl(e,t,o),l))).then((e=>this._lercDecoder.decode(e.data,p,a))).then((e=>new j(e)))}getTileUrl(e,r,t){const o=!this.tilemapCache&&this.supportsBlankTile,i=p({...this.parsedUrl.query,blankTile:!o&&null});return`${this.parsedUrl.path}/tile/${e}/${r}/${t}${i?\"?\"+i:\"\"}`}async queryElevation(e,r){const{ElevationQuery:t}=await import(\"./support/ElevationQuery.js\");l(r);return(new t).query(this,e,r)}async createElevationSampler(e,r){const{ElevationQuery:t}=await import(\"./support/ElevationQuery.js\");l(r);return(new t).createSampler(this,e,r)}_fetchTileAvailability(e,r,t,o){return this.tilemapCache?this.tilemapCache.fetchAvailability(e,r,t,o):Promise.resolve(\"unknown\")}async _fetchImageService(e){if(this.sourceJSON)return this.sourceJSON;const t={query:{f:\"json\",...this.parsedUrl.query},responseType:\"json\",signal:e},o=await r(this.parsedUrl.path,t);o.ssl&&(this.url=this.url?.replace(/^http:/i,\"https:\")),this.sourceJSON=o.data,this.read(o.data,{origin:\"service\",url:this.parsedUrl})}get hasOverriddenFetchTile(){return!this.fetchTile.__isDefault__}};e([n({json:{read:{source:\"copyrightText\"}}})],w.prototype,\"copyright\",void 0),e([n({readOnly:!0,type:y})],w.prototype,\"heightModelInfo\",void 0),e([n({type:String,json:{origins:{\"web-scene\":{read:!0,write:!0}},read:!1}})],w.prototype,\"path\",void 0),e([n({type:[\"show\",\"hide\"]})],w.prototype,\"listMode\",void 0),e([n({json:{read:!1,write:!1,origins:{service:{read:!1,write:!1},\"portal-item\":{read:!1,write:!1},\"web-document\":{read:!1,write:!1}}},readOnly:!0})],w.prototype,\"minScale\",void 0),e([n({json:{read:!1,write:!1,origins:{service:{read:!1,write:!1},\"portal-item\":{read:!1,write:!1},\"web-document\":{read:!1,write:!1}}},readOnly:!0})],w.prototype,\"maxScale\",void 0),e([n({json:{read:!1,write:!1,origins:{\"web-document\":{read:!1,write:!1}}}})],w.prototype,\"opacity\",void 0),e([n({type:[\"ArcGISTiledElevationServiceLayer\"]})],w.prototype,\"operationalLayerType\",void 0),e([n()],w.prototype,\"sourceJSON\",void 0),e([n({json:{read:!1},value:\"elevation\",readOnly:!0})],w.prototype,\"type\",void 0),e([n(g)],w.prototype,\"url\",void 0),e([n()],w.prototype,\"version\",void 0),e([c(\"version\",[\"currentVersion\"])],w.prototype,\"readVersion\",null),w=e([d(\"esri.layers.ElevationLayer\")],w),w.prototype.fetchTile.__isDefault__=!0;const T=w;export{T as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIqE,IAAMA,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEH,IAAE;AAAC,SAAK,mBAAiB,MAAK,KAAK,YAAU,MAAK,KAAK,YAAU,MAAK,eAAcC,MAAG,KAAK,SAAOA,GAAE,WAAU,KAAK,QAAMA,GAAE,OAAM,KAAK,SAAOA,GAAE,QAAO,KAAK,cAAYA,GAAE,gBAAc,KAAK,SAAOA,IAAE,KAAK,QAAMC,IAAE,KAAK,SAAOC,IAAE,KAAK,cAAYH;AAAA,EAAE;AAAA,EAAC,IAAI,kBAAiB;AAAC,QAAG,EAAE,KAAK,gBAAgB,GAAE;AAAC,YAAMC,KAAE,KAAK;AAAY,WAAK,mBAAiB,KAAK,OAAO,SAASA,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAgB;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,cAAc,GAAED,GAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,cAAc,GAAEA,GAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,QAAG,EAAE,KAAK,SAAS,EAAE;AAAO,UAAK,EAAC,aAAYC,IAAE,QAAOC,GAAC,IAAE;AAAK,QAAIF,KAAE,IAAE,GAAE,IAAE,KAAG,GAAEI,KAAE;AAAG,eAAUD,MAAKD,GAAE,CAAAC,OAAIF,KAAE,KAAK,mBAAiB,QAAID,KAAEG,KAAEH,KAAEG,KAAEH,IAAE,IAAEG,KAAE,IAAEA,KAAE,GAAEC,KAAE;AAAI,IAAAA,MAAG,KAAK,YAAU,GAAE,KAAK,YAAU,MAAI,KAAK,YAAUJ,IAAE,KAAK,YAAU,IAAE,QAAM,IAAE;AAAA,EAAE;AAAC;;;ACA9uB,IAAMK,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,KAAE,MAAK;AAAC,UAAM,cAAa,WAAU,EAAC,SAAQ,CAAAA,OAAG,CAACA,GAAE,MAAM,EAAC,GAAEA,IAAE,EAAC,UAAS,YAAW,CAAC,GAAE,KAAK,WAASA,IAAE,KAAK,MAAI;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAEC,IAAEC,IAAE;AAAC,WAAOF,MAAG,MAAIA,GAAE,aAAW,KAAK,OAAO,EAAC,QAAOA,IAAE,SAAQC,GAAC,GAAEC,EAAC,IAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,MAAE,KAAK,OAAK,MAAIC,GAAE,QAAS,CAACH,IAAEC,OAAI;AAAC,MAAAD,OAAI,QAAMG,GAAE,OAAOF,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,QAAQ;AAAA,EAAE;AAAC;AAAC,IAAME,KAAE,oBAAI;AAAI,SAAS,EAAED,KAAE,MAAK;AAAC,MAAIE,KAAED,GAAE,IAAIH,GAAEE,EAAC,CAAC;AAAE,SAAOE,OAAI,EAAEF,EAAC,KAAGE,KAAE,IAAIL,GAAG,CAAAC,OAAGE,GAAE,UAAU,SAASF,EAAC,CAAE,GAAEG,GAAE,IAAID,IAAEE,EAAC,MAAIA,KAAE,IAAIL,MAAEI,GAAE,IAAI,MAAKC,EAAC,KAAI,EAAEA,GAAE,KAAIA;AAAC;;;ACAuiB,IAAIC,KAAE,cAAcC,GAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,YAAU,MAAK,KAAK,kBAAgB,MAAK,KAAK,OAAK,MAAK,KAAK,WAAS,QAAO,KAAK,WAAS,QAAO,KAAK,UAAQ,GAAE,KAAK,uBAAqB,oCAAmC,KAAK,aAAW,MAAK,KAAK,OAAK,aAAY,KAAK,MAAI,MAAK,KAAK,UAAQ,MAAK,KAAK,eAAa,EAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,IAAE;AAAC,WAAM,YAAU,OAAOD,KAAE,EAAC,KAAIA,IAAE,GAAGC,GAAC,IAAED;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,eAAa,EAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAe,WAAOC,OAAIA,KAAE,MAAKA;AAAA,EAAC;AAAA,EAAC,KAAKF,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC,IAAEA,GAAE,SAAO;AAAK,WAAO,KAAK,oBAAoB,KAAK,eAAe,EAAC,gBAAe,CAAC,eAAe,GAAE,cAAa,OAAG,cAAa,CAAAA,OAAG;AAAC,eAAQC,KAAE,GAAEA,KAAED,GAAE,aAAa,QAAOC,KAAI,KAAG,yBAAuBD,GAAE,aAAaC,EAAC,EAAE,YAAY,EAAE,QAAM;AAAG,YAAM,IAAI,EAAE,kCAAiC,kEAAiE,EAAC,MAAK,iBAAgB,cAAa,mCAAkC,CAAC;AAAA,IAAC,EAAC,GAAED,EAAC,EAAE,MAAM,CAAC,EAAE,KAAM,MAAI,KAAK,mBAAmBC,EAAC,CAAE,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,UAAUD,IAAEE,IAAEC,IAAEJ,IAAE;AAAC,UAAMK,KAAE,GAAGL,KAAEA,MAAG,EAAC,QAAO,KAAI,GAAG,MAAM,IAAEA,GAAE,SAAOA,GAAE,SAAQ,IAAI,kBAAiB,QAAO,IAAE,EAAC,cAAa,gBAAe,QAAOK,GAAC,GAAEC,KAAE,EAAC,aAAYN,GAAE,aAAY,gBAAe,KAAE;AAAE,WAAO,KAAK,KAAK,EAAE,KAAM,MAAI,KAAK,uBAAuBC,IAAEE,IAAEC,IAAEJ,EAAC,CAAE,EAAE,KAAM,MAAI,EAAE,KAAK,WAAWC,IAAEE,IAAEC,EAAC,GAAE,CAAC,CAAE,EAAE,KAAM,CAAAH,OAAG,KAAK,aAAa,OAAOA,GAAE,MAAKK,IAAED,EAAC,CAAE,EAAE,KAAM,CAAAJ,OAAG,IAAIA,GAAEA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC,KAAK,gBAAc,KAAK,mBAAkB,IAAE,EAAE,EAAC,GAAG,KAAK,UAAU,OAAM,WAAU,CAACA,MAAG,KAAI,CAAC;AAAE,WAAM,GAAG,KAAK,UAAU,IAAI,SAASH,EAAC,IAAIC,EAAC,IAAIC,EAAC,GAAG,IAAE,MAAI,IAAE,EAAE;AAAA,EAAE;AAAA,EAAC,MAAM,eAAeF,IAAEC,IAAE;AAAC,UAAK,EAAC,gBAAeC,GAAC,IAAE,MAAM,OAAO,8BAA6B;AAAE,MAAED,EAAC;AAAE,WAAO,IAAIC,KAAG,MAAM,MAAKF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,uBAAuBD,IAAEC,IAAE;AAAC,UAAK,EAAC,gBAAeC,GAAC,IAAE,MAAM,OAAO,8BAA6B;AAAE,MAAED,EAAC;AAAE,WAAO,IAAIC,KAAG,cAAc,MAAKF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,eAAa,KAAK,aAAa,kBAAkBH,IAAEC,IAAEC,IAAEC,EAAC,IAAE,QAAQ,QAAQ,SAAS;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBH,IAAE;AAJxmG;AAIymG,QAAG,KAAK,WAAW,QAAO,KAAK;AAAW,UAAME,KAAE,EAAC,OAAM,EAAC,GAAE,QAAO,GAAG,KAAK,UAAU,MAAK,GAAE,cAAa,QAAO,QAAOF,GAAC,GAAEG,KAAE,MAAM,EAAE,KAAK,UAAU,MAAKD,EAAC;AAAE,IAAAC,GAAE,QAAM,KAAK,OAAI,UAAK,QAAL,mBAAU,QAAQ,WAAU,YAAW,KAAK,aAAWA,GAAE,MAAK,KAAK,KAAKA,GAAE,MAAK,EAAC,QAAO,WAAU,KAAI,KAAK,UAAS,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,yBAAwB;AAAC,WAAM,CAAC,KAAK,UAAU;AAAA,EAAa;AAAC;AAAE,EAAE,CAACG,GAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,gBAAe,EAAC,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACQ,GAAE,EAAC,UAAS,MAAG,MAAK,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACQ,GAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,GAAE,MAAK,MAAE,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAACQ,GAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACQ,GAAE,EAAC,MAAK,EAAC,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,SAAQ,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,eAAc,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,gBAAe,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,GAAE,UAAS,KAAE,CAAC,CAAC,GAAER,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACQ,GAAE,EAAC,MAAK,EAAC,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,SAAQ,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,eAAc,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,gBAAe,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,GAAE,UAAS,KAAE,CAAC,CAAC,GAAER,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACQ,GAAE,EAAC,MAAK,EAAC,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,gBAAe,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACQ,GAAE,EAAC,MAAK,CAAC,kCAAkC,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACQ,GAAE,CAAC,GAAER,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAACQ,GAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,OAAM,aAAY,UAAS,KAAE,CAAC,CAAC,GAAER,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAACQ,GAAEC,EAAC,CAAC,GAAET,GAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAACQ,GAAE,CAAC,GAAER,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,CAAC,gBAAgB,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,4BAA4B,CAAC,GAAEA,EAAC,GAAEA,GAAE,UAAU,UAAU,gBAAc;AAAG,IAAM,IAAEA;", "names": ["e", "a", "t", "s", "h", "s", "e", "r", "t", "o", "n", "w", "s", "e", "r", "t", "o", "a", "p", "y", "f"]}