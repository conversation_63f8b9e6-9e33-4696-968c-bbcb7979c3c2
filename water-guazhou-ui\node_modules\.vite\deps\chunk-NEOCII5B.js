import {
  w
} from "./chunk-7GPM2ZU5.js";
import {
  e as e6,
  r as r4
} from "./chunk-TOYJMVHA.js";
import {
  t as t4
} from "./chunk-VK7XO5DN.js";
import {
  n as n4
} from "./chunk-UHA44FM7.js";
import {
  a,
  t as t3
} from "./chunk-6ZZUUGXX.js";
import {
  O as O3
} from "./chunk-CPQSD22U.js";
import {
  n as n3,
  o as o3
} from "./chunk-SKIEIN3S.js";
import {
  c as c2,
  i,
  u as u2,
  x
} from "./chunk-3KCCETWY.js";
import {
  s
} from "./chunk-EM4JSU7Z.js";
import {
  I,
  O as O2
} from "./chunk-4M3AMTD4.js";
import {
  y as y2
} from "./chunk-ST2RRB55.js";
import {
  R
} from "./chunk-YELYN22P.js";
import {
  S
} from "./chunk-ZIKXCGU7.js";
import {
  e as e5
} from "./chunk-XSQFM27N.js";
import {
  e as e4,
  o as o4,
  r as r3
} from "./chunk-QYOAH6AO.js";
import {
  e as e3
} from "./chunk-A7PY25IH.js";
import {
  G,
  H,
  c,
  h,
  n as n2,
  o as o2
} from "./chunk-YEODPCXQ.js";
import {
  A,
  E,
  O,
  U,
  e as e2,
  g,
  o,
  p,
  u
} from "./chunk-MQAXMQFG.js";
import {
  n,
  r as r2,
  t as t2
} from "./chunk-36FLFRUE.js";
import {
  e2 as e,
  l as l2
} from "./chunk-C5VMWMBD.js";
import {
  y
} from "./chunk-GZGAQUSK.js";
import {
  l,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/AttributeArray.js
function t5(t6) {
  if (t6.length < y) return Array.from(t6);
  if (Array.isArray(t6)) return Float64Array.from(t6);
  switch (t6.BYTES_PER_ELEMENT) {
    case 1:
      return Uint8Array.from(t6);
    case 2:
      return Uint16Array.from(t6);
    case 4:
      return Float32Array.from(t6);
    default:
      return Float64Array.from(t6);
  }
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/BoundingInfo.js
var o5 = class _o {
  constructor(i5, e8, o8, c4) {
    this.primitiveIndices = i5, this._numIndexPerPrimitive = e8, this.indices = o8, this.position = c4, this._children = void 0, s(i5.length >= 1), s(o8.length % this._numIndexPerPrimitive == 0), s(o8.length >= i5.length * this._numIndexPerPrimitive), s(3 === c4.size || 4 === c4.size);
    const { data: d3, size: l5 } = c4, m2 = i5.length;
    let u7 = l5 * o8[this._numIndexPerPrimitive * i5[0]];
    a2.clear(), a2.push(u7);
    const f3 = r2(d3[u7], d3[u7 + 1], d3[u7 + 2]), x4 = t2(f3);
    for (let t6 = 0; t6 < m2; ++t6) {
      const e9 = this._numIndexPerPrimitive * i5[t6];
      for (let i6 = 0; i6 < this._numIndexPerPrimitive; ++i6) {
        u7 = l5 * o8[e9 + i6], a2.push(u7);
        let t7 = d3[u7];
        f3[0] = Math.min(t7, f3[0]), x4[0] = Math.max(t7, x4[0]), t7 = d3[u7 + 1], f3[1] = Math.min(t7, f3[1]), x4[1] = Math.max(t7, x4[1]), t7 = d3[u7 + 2], f3[2] = Math.min(t7, f3[2]), x4[2] = Math.max(t7, x4[2]);
      }
    }
    this.bbMin = f3, this.bbMax = x4;
    const P2 = A(n(), this.bbMin, this.bbMax, 0.5);
    this.radius = 0.5 * Math.max(Math.max(x4[0] - f3[0], x4[1] - f3[1]), x4[2] - f3[2]);
    let v3 = this.radius * this.radius;
    for (let t6 = 0; t6 < a2.length; ++t6) {
      u7 = a2.getItemAt(t6);
      const i6 = d3[u7] - P2[0], e9 = d3[u7 + 1] - P2[1], s4 = d3[u7 + 2] - P2[2], n6 = i6 * i6 + e9 * e9 + s4 * s4;
      if (n6 <= v3) continue;
      const r7 = Math.sqrt(n6), h3 = 0.5 * (r7 - this.radius);
      this.radius = this.radius + h3, v3 = this.radius * this.radius;
      const o9 = h3 / r7;
      P2[0] += i6 * o9, P2[1] += e9 * o9, P2[2] += s4 * o9;
    }
    this.center = P2, a2.clear();
  }
  getChildren() {
    if (this._children || p(this.bbMin, this.bbMax) <= 1) return this._children;
    const i5 = A(n(), this.bbMin, this.bbMax, 0.5), s4 = this.primitiveIndices.length, n6 = new Uint8Array(s4), h3 = new Array(8);
    for (let t6 = 0; t6 < 8; ++t6) h3[t6] = 0;
    const { data: a5, size: c4 } = this.position;
    for (let t6 = 0; t6 < s4; ++t6) {
      let e8 = 0;
      const s5 = this._numIndexPerPrimitive * this.primitiveIndices[t6];
      let r7 = c4 * this.indices[s5], o8 = a5[r7], d4 = a5[r7 + 1], l6 = a5[r7 + 2];
      for (let i6 = 1; i6 < this._numIndexPerPrimitive; ++i6) {
        r7 = c4 * this.indices[s5 + i6];
        const t7 = a5[r7], e9 = a5[r7 + 1], n7 = a5[r7 + 2];
        t7 < o8 && (o8 = t7), e9 < d4 && (d4 = e9), n7 < l6 && (l6 = n7);
      }
      o8 < i5[0] && (e8 |= 1), d4 < i5[1] && (e8 |= 2), l6 < i5[2] && (e8 |= 4), n6[t6] = e8, ++h3[e8];
    }
    let d3 = 0;
    for (let t6 = 0; t6 < 8; ++t6) h3[t6] > 0 && ++d3;
    if (d3 < 2) return;
    const l5 = new Array(8);
    for (let t6 = 0; t6 < 8; ++t6) l5[t6] = h3[t6] > 0 ? new Uint32Array(h3[t6]) : void 0;
    for (let t6 = 0; t6 < 8; ++t6) h3[t6] = 0;
    for (let t6 = 0; t6 < s4; ++t6) {
      const i6 = n6[t6];
      l5[i6][h3[i6]++] = this.primitiveIndices[t6];
    }
    this._children = new Array();
    for (let t6 = 0; t6 < 8; ++t6) void 0 !== l5[t6] && this._children.push(new _o(l5[t6], this._numIndexPerPrimitive, this.indices, this.position));
    return this._children;
  }
  static prune() {
    a2.prune();
  }
};
var a2 = new l2({ deallocator: null });

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/geometryDataUtils.js
function c3(r7, o8, c4) {
  if (!r7 || !o8) return false;
  const { size: a5, data: f3 } = r7;
  o(c4, 0, 0, 0), o(g2, 0, 0, 0);
  let m2 = 0, h3 = 0;
  for (let p3 = 0; p3 < o8.length - 2; p3 += 3) {
    const r8 = o8[p3 + 0] * a5, j = o8[p3 + 1] * a5, d3 = o8[p3 + 2] * a5;
    o(i2, f3[r8 + 0], f3[r8 + 1], f3[r8 + 2]), o(u3, f3[j + 0], f3[j + 1], f3[j + 2]), o(l3, f3[d3 + 0], f3[d3 + 1], f3[d3 + 2]);
    const z2 = w(i2, u3, l3);
    z2 ? (u(i2, i2, u3), u(i2, i2, l3), g(i2, i2, 1 / 3 * z2), u(c4, c4, i2), m2 += z2) : (u(g2, g2, i2), u(g2, g2, u3), u(g2, g2, l3), h3 += 3);
  }
  return (0 !== h3 || 0 !== m2) && (0 !== m2 ? (g(c4, c4, 1 / m2), true) : 0 !== h3 && (g(c4, g2, 1 / h3), true));
}
function a3(n6, r7, o8) {
  if (!n6 || !r7) return false;
  const { size: s4, data: c4 } = n6;
  o(o8, 0, 0, 0);
  let a5 = -1, f3 = 0;
  for (let t6 = 0; t6 < r7.length; t6++) {
    const n7 = r7[t6] * s4;
    a5 !== n7 && (o8[0] += c4[n7 + 0], o8[1] += c4[n7 + 1], o8[2] += c4[n7 + 2], f3++), a5 = n7;
  }
  return f3 > 1 && g(o8, o8, 1 / f3), f3 > 0;
}
function f(o8, s4, c4, a5) {
  if (!o8) return false;
  o(a5, 0, 0, 0), o(g2, 0, 0, 0);
  let f3 = 0, l5 = 0;
  const { size: m2, data: h3 } = o8, p3 = s4 ? s4.length - 1 : h3.length / m2 - 1, j = p3 + (c4 ? 2 : 0);
  for (let t6 = 0; t6 < j; t6 += 2) {
    const o9 = t6 < p3 ? t6 : p3, c5 = t6 < p3 ? t6 + 1 : 0, j2 = (s4 ? s4[o9] : o9) * m2, d3 = (s4 ? s4[c5] : c5) * m2;
    i2[0] = h3[j2], i2[1] = h3[j2 + 1], i2[2] = h3[j2 + 2], u3[0] = h3[d3], u3[1] = h3[d3 + 1], u3[2] = h3[d3 + 2], g(i2, u(i2, i2, u3), 0.5);
    const z2 = U(i2, u3);
    z2 > 0 ? (u(a5, a5, g(i2, i2, z2)), f3 += z2) : 0 === f3 && (u(g2, g2, i2), l5++);
  }
  return 0 !== f3 ? (g(a5, a5, 1 / f3), true) : 0 !== l5 && (g(a5, g2, 1 / l5), true);
}
var i2 = n();
var u3 = n();
var l3 = n();
var g2 = n();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/Object3DStateID.js
var r5 = class {
  constructor(r7) {
    this.channel = r7, this.id = e();
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/renderers/utils.js
function n5(e8, r7) {
  return t(e8) && (e8 = []), e8.push(r7), e8;
}
function o6(e8, r7) {
  if (t(e8)) return null;
  const n6 = e8.filter((t6) => t6 !== r7);
  return 0 === n6.length ? null : n6;
}
function s2(t6, e8, n6, o8, s4) {
  i3[0] = t6.get(e8, 0), i3[1] = t6.get(e8, 1), i3[2] = t6.get(e8, 2), t4(i3, u4, 3), n6.set(s4, 0, u4[0]), o8.set(s4, 0, u4[1]), n6.set(s4, 1, u4[2]), o8.set(s4, 1, u4[3]), n6.set(s4, 2, u4[4]), o8.set(s4, 2, u4[5]);
}
var i3 = n();
var u4 = new Float32Array(6);

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/Geometry.js
var v = class _v extends r4 {
  constructor(t6, e8, i5 = [], s4 = null, n6 = e6.Mesh, r7 = null, o8 = -1) {
    super(), this.material = t6, this.mapPositions = s4, this.type = n6, this.objectAndLayerIdColor = r7, this.edgeIndicesLength = o8, this.visible = true, this._vertexAttributes = /* @__PURE__ */ new Map(), this._indices = /* @__PURE__ */ new Map(), this._boundingInfo = null;
    for (const [h3, a5] of e8) a5 && this._vertexAttributes.set(h3, { ...a5 });
    if (null == i5 || 0 === i5.length) {
      const t7 = A2(this._vertexAttributes), e9 = o3(t7);
      this.edgeIndicesLength = this.edgeIndicesLength < 0 ? t7 : this.edgeIndicesLength;
      for (const i6 of this._vertexAttributes.keys()) this._indices.set(i6, e9);
    } else for (const [h3, a5] of i5) a5 && (this._indices.set(h3, n3(a5)), h3 === O3.POSITION && (this.edgeIndicesLength = this.edgeIndicesLength < 0 ? this._indices.get(h3).length : this.edgeIndicesLength));
  }
  instantiate(t6 = {}) {
    const e8 = new _v(t6.material || this.material, [], void 0, this.mapPositions, this.type, this.objectAndLayerIdColor, this.edgeIndicesLength);
    return this._vertexAttributes.forEach((t7, i5) => {
      t7.exclusive = false, e8._vertexAttributes.set(i5, t7);
    }), this._indices.forEach((t7, i5) => e8._indices.set(i5, t7)), e8._boundingInfo = this._boundingInfo, e8.transformation = t6.transformation || this.transformation, e8;
  }
  get vertexAttributes() {
    return this._vertexAttributes;
  }
  getMutableAttribute(t6) {
    let e8 = this._vertexAttributes.get(t6);
    return e8 && !e8.exclusive && (e8 = { ...e8, exclusive: true, data: t5(e8.data) }, this._vertexAttributes.set(t6, e8)), e8;
  }
  get indices() {
    return this._indices;
  }
  get indexCount() {
    const t6 = this._indices.values().next().value;
    return t6 ? t6.length : 0;
  }
  get faceCount() {
    return this.indexCount / 3;
  }
  get boundingInfo() {
    return t(this._boundingInfo) && (this._boundingInfo = this._calculateBoundingInfo()), this._boundingInfo;
  }
  computeAttachmentOrigin(t6) {
    return !!(this.type === e6.Mesh ? this._computeAttachmentOriginTriangles(t6) : this.type === e6.Line ? this._computeAttachmentOriginLines(t6) : this._computeAttachmentOriginPoints(t6)) && (r(this._transformation) && O(t6, t6, this._transformation), true);
  }
  _computeAttachmentOriginTriangles(t6) {
    const e8 = this.indices.get(O3.POSITION), i5 = this.vertexAttributes.get(O3.POSITION);
    return c3(i5, e8, t6);
  }
  _computeAttachmentOriginLines(t6) {
    const e8 = this.vertexAttributes.get(O3.POSITION), i5 = this.indices.get(O3.POSITION);
    return f(e8, i5, i5 && x2(this.material.parameters, e8, i5), t6);
  }
  _computeAttachmentOriginPoints(t6) {
    const e8 = this.indices.get(O3.POSITION), i5 = this.vertexAttributes.get(O3.POSITION);
    return a3(i5, e8, t6);
  }
  invalidateBoundingInfo() {
    this._boundingInfo = null;
  }
  _calculateBoundingInfo() {
    const t6 = this.indices.get(O3.POSITION), e8 = this.vertexAttributes.get(O3.POSITION);
    if (!t6 || 0 === t6.length || !e8) return null;
    const i5 = this.type === e6.Mesh ? 3 : 1;
    s(t6.length % i5 == 0, "Indexing error: " + t6.length + " not divisible by " + i5);
    const s4 = o3(t6.length / i5);
    return new o5(s4, i5, t6, e8);
  }
  get transformation() {
    return l(this._transformation, o4);
  }
  set transformation(t6) {
    this._transformation = t6 && t6 !== o4 ? r3(t6) : null;
  }
  get shaderTransformation() {
    return r(this._shaderTransformer) ? this._shaderTransformer(this.transformation) : this.transformation;
  }
  get shaderTransformer() {
    return this._shaderTransformer;
  }
  set shaderTransformer(t6) {
    this._shaderTransformer = t6;
  }
  get hasVolatileTransformation() {
    return r(this._shaderTransformer);
  }
  addHighlight() {
    const t6 = new r5(t3.Highlight);
    return this.highlights = n5(this.highlights, t6), t6;
  }
  removeHighlight(t6) {
    this.highlights = o6(this.highlights, t6);
  }
};
function A2(t6) {
  const e8 = t6.values().next().value;
  return null == e8 ? 0 : e8.data.length / e8.size;
}
function x2(t6, e8, i5) {
  return !(!("isClosed" in t6) || !t6.isClosed) && (i5 ? i5.length > 2 : e8.data.length > 6);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/RenderSlot.js
var E2;
!function(E3) {
  E3[E3.INTEGRATED_MESH = 0] = "INTEGRATED_MESH", E3[E3.OPAQUE_TERRAIN = 1] = "OPAQUE_TERRAIN", E3[E3.OPAQUE_MATERIAL = 2] = "OPAQUE_MATERIAL", E3[E3.TRANSPARENT_MATERIAL = 3] = "TRANSPARENT_MATERIAL", E3[E3.TRANSPARENT_TERRAIN = 4] = "TRANSPARENT_TERRAIN", E3[E3.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL = 5] = "TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL", E3[E3.OCCLUDED_TERRAIN = 6] = "OCCLUDED_TERRAIN", E3[E3.OCCLUDER_MATERIAL = 7] = "OCCLUDER_MATERIAL", E3[E3.TRANSPARENT_OCCLUDER_MATERIAL = 8] = "TRANSPARENT_OCCLUDER_MATERIAL", E3[E3.OCCLUSION_PIXELS = 9] = "OCCLUSION_PIXELS", E3[E3.POSTPROCESSING_ENVIRONMENT_OPAQUE = 10] = "POSTPROCESSING_ENVIRONMENT_OPAQUE", E3[E3.POSTPROCESSING_ENVIRONMENT_TRANSPARENT = 11] = "POSTPROCESSING_ENVIRONMENT_TRANSPARENT", E3[E3.LASERLINES = 12] = "LASERLINES", E3[E3.LASERLINES_CONTRAST_CONTROL = 13] = "LASERLINES_CONTRAST_CONTROL", E3[E3.HUD_MATERIAL = 14] = "HUD_MATERIAL", E3[E3.LABEL_MATERIAL = 15] = "LABEL_MATERIAL", E3[E3.LINE_CALLOUTS = 16] = "LINE_CALLOUTS", E3[E3.LINE_CALLOUTS_HUD_DEPTH = 17] = "LINE_CALLOUTS_HUD_DEPTH", E3[E3.DRAPED_MATERIAL = 18] = "DRAPED_MATERIAL", E3[E3.DRAPED_WATER = 19] = "DRAPED_WATER", E3[E3.VOXEL = 20] = "VOXEL", E3[E3.MAX_SLOTS = 21] = "MAX_SLOTS";
}(E2 || (E2 = {}));

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/verticalOffsetUtils.js
var u5 = class {
  constructor() {
    this._transform = e4(), this._transformInverse = new M({ value: this._transform }, h, e4), this._transformInverseTranspose = new M(this._transformInverse, o2, e4), this._transformTranspose = new M({ value: this._transform }, o2, e4), this._transformInverseRotation = new M({ value: this._transform }, y2, e3);
  }
  _invalidateLazyTransforms() {
    this._transformInverse.invalidate(), this._transformInverseTranspose.invalidate(), this._transformTranspose.invalidate(), this._transformInverseRotation.invalidate();
  }
  get transform() {
    return this._transform;
  }
  get inverse() {
    return this._transformInverse.value;
  }
  get inverseTranspose() {
    return this._transformInverseTranspose.value;
  }
  get inverseRotation() {
    return this._transformInverseRotation.value;
  }
  get transpose() {
    return this._transformTranspose.value;
  }
  setTransformMatrix(t6) {
    n2(this._transform, t6);
  }
  multiplyTransform(t6) {
    c(this._transform, this._transform, t6);
  }
  set(t6) {
    n2(this._transform, t6), this._invalidateLazyTransforms();
  }
  setAndInvalidateLazyTransforms(t6, s4) {
    this.setTransformMatrix(t6), this.multiplyTransform(s4), this._invalidateLazyTransforms();
  }
};
var M = class {
  constructor(t6, s4, e8) {
    this._original = t6, this._update = s4, this._dirty = true, this._transform = e8();
  }
  invalidate() {
    this._dirty = true;
  }
  get value() {
    return this._dirty && (this._update(this._transform, this._original.value), this._dirty = false), this._transform;
  }
};
var v2 = class {
  constructor(t6 = 0) {
    this.offset = t6, this.tmpVertex = n();
  }
  applyToVertex(t6, s4, e8) {
    const i5 = t6 + this.localOrigin[0], r7 = s4 + this.localOrigin[1], a5 = e8 + this.localOrigin[2], o8 = this.offset / Math.sqrt(i5 * i5 + r7 * r7 + a5 * a5);
    return this.tmpVertex[0] = t6 + i5 * o8, this.tmpVertex[1] = s4 + r7 * o8, this.tmpVertex[2] = e8 + a5 * o8, this.tmpVertex;
  }
  applyToAabb(t6) {
    for (let r7 = 0; r7 < 3; ++r7) O4[r7] = t6[0 + r7] + this.localOrigin[r7], z[r7] = t6[3 + r7] + this.localOrigin[r7], T[r7] = O4[r7];
    const s4 = this.applyToVertex(O4[0], O4[1], O4[2]);
    for (let r7 = 0; r7 < 3; ++r7) t6[r7] = s4[r7], t6[r7 + 3] = s4[r7];
    const e8 = (s5) => {
      const e9 = this.applyToVertex(s5[0], s5[1], s5[2]);
      for (let i6 = 0; i6 < 3; ++i6) t6[i6 + 0] = Math.min(t6[i6 + 0], e9[i6]), t6[i6 + 3] = Math.max(t6[i6 + 3], e9[i6]);
    };
    for (let r7 = 1; r7 < 8; ++r7) {
      for (let t7 = 0; t7 < 3; ++t7) T[t7] = 0 == (r7 & 1 << t7) ? O4[t7] : z[t7];
      e8(T);
    }
    let i5 = 0;
    for (let r7 = 0; r7 < 3; ++r7) {
      O4[r7] * z[r7] < 0 && (i5 |= 1 << r7);
    }
    if (0 !== i5 && 7 !== i5) {
      for (let r7 = 0; r7 < 8; ++r7) if (0 == (i5 & r7)) {
        for (let t7 = 0; t7 < 3; ++t7) i5[t7] ? T[t7] = 0 : T[t7] = 0 != (r7 & 1 << t7) ? O4[t7] : z[t7];
        e8(T);
      }
    }
    for (let r7 = 0; r7 < 3; ++r7) t6[r7 + 0] -= this.localOrigin[r7], t6[r7 + 3] -= this.localOrigin[r7];
    return t6;
  }
};
var O4 = n();
var z = n();
var T = n();
var g3 = class {
  constructor(t6 = 0) {
    this.componentLocalOriginLength = 0, this._tmpVertex = n(), this._mbs = R(), this._obb = { center: n(), halfSize: n4(), quaternion: null }, this._totalOffset = 0, this._offset = 0, this._resetOffset(t6);
  }
  _resetOffset(t6) {
    this._offset = t6, this._totalOffset = t6;
  }
  set offset(t6) {
    this._resetOffset(t6);
  }
  get offset() {
    return this._offset;
  }
  set componentOffset(t6) {
    this._totalOffset = this._offset + t6;
  }
  set localOrigin(t6) {
    this.componentLocalOriginLength = Math.sqrt(t6[0] * t6[0] + t6[1] * t6[1] + t6[2] * t6[2]);
  }
  applyToVertex(t6, s4, e8) {
    const i5 = t6, r7 = s4, a5 = e8 + this.componentLocalOriginLength, o8 = this._totalOffset / Math.sqrt(i5 * i5 + r7 * r7 + a5 * a5);
    return this._tmpVertex[0] = t6 + i5 * o8, this._tmpVertex[1] = s4 + r7 * o8, this._tmpVertex[2] = e8 + a5 * o8, this._tmpVertex;
  }
  applyToAabb(t6) {
    const s4 = t6[0], e8 = t6[1], i5 = t6[2] + this.componentLocalOriginLength, r7 = t6[3], a5 = t6[4], o8 = t6[5] + this.componentLocalOriginLength, h3 = s4 * r7 < 0 ? 0 : Math.min(Math.abs(s4), Math.abs(r7)), n6 = e8 * a5 < 0 ? 0 : Math.min(Math.abs(e8), Math.abs(a5)), f3 = i5 * o8 < 0 ? 0 : Math.min(Math.abs(i5), Math.abs(o8)), l5 = Math.sqrt(h3 * h3 + n6 * n6 + f3 * f3);
    if (l5 < this._totalOffset) return t6[0] -= s4 < 0 ? this._totalOffset : 0, t6[1] -= e8 < 0 ? this._totalOffset : 0, t6[2] -= i5 < 0 ? this._totalOffset : 0, t6[3] += r7 > 0 ? this._totalOffset : 0, t6[4] += a5 > 0 ? this._totalOffset : 0, t6[5] += o8 > 0 ? this._totalOffset : 0, t6;
    const m2 = Math.max(Math.abs(s4), Math.abs(r7)), _ = Math.max(Math.abs(e8), Math.abs(a5)), b2 = Math.max(Math.abs(i5), Math.abs(o8)), c4 = Math.sqrt(m2 * m2 + _ * _ + b2 * b2), p3 = this._totalOffset / c4, u7 = this._totalOffset / l5;
    return t6[0] += s4 * (s4 > 0 ? p3 : u7), t6[1] += e8 * (e8 > 0 ? p3 : u7), t6[2] += i5 * (i5 > 0 ? p3 : u7), t6[3] += r7 * (r7 < 0 ? p3 : u7), t6[4] += a5 * (a5 < 0 ? p3 : u7), t6[5] += o8 * (o8 < 0 ? p3 : u7), t6;
  }
  applyToMbs(t6) {
    const s4 = Math.sqrt(t6[0] * t6[0] + t6[1] * t6[1] + t6[2] * t6[2]), e8 = this._totalOffset / s4;
    return this._mbs[0] = t6[0] + t6[0] * e8, this._mbs[1] = t6[1] + t6[1] * e8, this._mbs[2] = t6[2] + t6[2] * e8, this._mbs[3] = t6[3] + t6[3] * this._totalOffset / s4, this._mbs;
  }
  applyToObb(t6) {
    const s4 = t6.center, e8 = this._totalOffset / Math.sqrt(s4[0] * s4[0] + s4[1] * s4[1] + s4[2] * s4[2]);
    this._obb.center[0] = s4[0] + s4[0] * e8, this._obb.center[1] = s4[1] + s4[1] * e8, this._obb.center[2] = s4[2] + s4[2] * e8, E(this._obb.halfSize, t6.halfSize, t6.quaternion), u(this._obb.halfSize, this._obb.halfSize, t6.center);
    const i5 = this._totalOffset / Math.sqrt(this._obb.halfSize[0] * this._obb.halfSize[0] + this._obb.halfSize[1] * this._obb.halfSize[1] + this._obb.halfSize[2] * this._obb.halfSize[2]);
    return this._obb.halfSize[0] += this._obb.halfSize[0] * i5, this._obb.halfSize[1] += this._obb.halfSize[1] * i5, this._obb.halfSize[2] += this._obb.halfSize[2] * i5, e2(this._obb.halfSize, this._obb.halfSize, t6.center), S(I2, t6.quaternion), E(this._obb.halfSize, this._obb.halfSize, I2), this._obb.halfSize[0] *= this._obb.halfSize[0] < 0 ? -1 : 1, this._obb.halfSize[1] *= this._obb.halfSize[1] < 0 ? -1 : 1, this._obb.halfSize[2] *= this._obb.halfSize[2] < 0 ? -1 : 1, this._obb.quaternion = t6.quaternion, this._obb;
  }
};
var x3 = class {
  constructor(t6 = 0) {
    this.offset = t6, this.sphere = R(), this.tmpVertex = n();
  }
  applyToVertex(t6, s4, e8) {
    const i5 = this.objectTransform.transform;
    let r7 = i5[0] * t6 + i5[4] * s4 + i5[8] * e8 + i5[12], a5 = i5[1] * t6 + i5[5] * s4 + i5[9] * e8 + i5[13], o8 = i5[2] * t6 + i5[6] * s4 + i5[10] * e8 + i5[14];
    const h3 = this.offset / Math.sqrt(r7 * r7 + a5 * a5 + o8 * o8);
    r7 += r7 * h3, a5 += a5 * h3, o8 += o8 * h3;
    const n6 = this.objectTransform.inverse;
    return this.tmpVertex[0] = n6[0] * r7 + n6[4] * a5 + n6[8] * o8 + n6[12], this.tmpVertex[1] = n6[1] * r7 + n6[5] * a5 + n6[9] * o8 + n6[13], this.tmpVertex[2] = n6[2] * r7 + n6[6] * a5 + n6[10] * o8 + n6[14], this.tmpVertex;
  }
  applyToMinMax(t6, s4) {
    const e8 = this.offset / Math.sqrt(t6[0] * t6[0] + t6[1] * t6[1] + t6[2] * t6[2]);
    t6[0] += t6[0] * e8, t6[1] += t6[1] * e8, t6[2] += t6[2] * e8;
    const i5 = this.offset / Math.sqrt(s4[0] * s4[0] + s4[1] * s4[1] + s4[2] * s4[2]);
    s4[0] += s4[0] * i5, s4[1] += s4[1] * i5, s4[2] += s4[2] * i5;
  }
  applyToAabb(t6) {
    const s4 = this.offset / Math.sqrt(t6[0] * t6[0] + t6[1] * t6[1] + t6[2] * t6[2]);
    t6[0] += t6[0] * s4, t6[1] += t6[1] * s4, t6[2] += t6[2] * s4;
    const e8 = this.offset / Math.sqrt(t6[3] * t6[3] + t6[4] * t6[4] + t6[5] * t6[5]);
    return t6[3] += t6[3] * e8, t6[4] += t6[4] * e8, t6[5] += t6[5] * e8, t6;
  }
  applyToBoundingSphere(t6) {
    const s4 = Math.sqrt(t6[0] * t6[0] + t6[1] * t6[1] + t6[2] * t6[2]), e8 = this.offset / s4;
    return this.sphere[0] = t6[0] + t6[0] * e8, this.sphere[1] = t6[1] + t6[1] * e8, this.sphere[2] = t6[2] + t6[2] * e8, this.sphere[3] = t6[3] + t6[3] * this.offset / s4, this.sphere;
  }
};
var S2 = new x3();
function y3(s4) {
  return r(s4) ? (S2.offset = s4, S2) : null;
}
var q = new g3();
var d = new v2();
var I2 = e5();

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/internal/bufferWriterUtils.js
function d2(e8, t6, f3, o8) {
  const r7 = f3.typedBuffer, n6 = f3.typedBufferStride, s4 = e8.length;
  o8 *= n6;
  for (let i5 = 0; i5 < s4; ++i5) {
    const f4 = 2 * e8[i5];
    r7[o8] = t6[f4], r7[o8 + 1] = t6[f4 + 1], o8 += n6;
  }
}
function u6(e8, t6, f3, o8, r7) {
  const n6 = f3.typedBuffer, s4 = f3.typedBufferStride, i5 = e8.length;
  if (o8 *= s4, null == r7 || 1 === r7) for (let c4 = 0; c4 < i5; ++c4) {
    const f4 = 3 * e8[c4];
    n6[o8] = t6[f4], n6[o8 + 1] = t6[f4 + 1], n6[o8 + 2] = t6[f4 + 2], o8 += s4;
  }
  else for (let c4 = 0; c4 < i5; ++c4) {
    const f4 = 3 * e8[c4];
    for (let e9 = 0; e9 < r7; ++e9) n6[o8] = t6[f4], n6[o8 + 1] = t6[f4 + 1], n6[o8 + 2] = t6[f4 + 2], o8 += s4;
  }
}
function a4(e8, t6, f3, o8, r7 = 1) {
  const n6 = f3.typedBuffer, s4 = f3.typedBufferStride, i5 = e8.length;
  if (o8 *= s4, 1 === r7) for (let c4 = 0; c4 < i5; ++c4) {
    const f4 = 4 * e8[c4];
    n6[o8] = t6[f4], n6[o8 + 1] = t6[f4 + 1], n6[o8 + 2] = t6[f4 + 2], n6[o8 + 3] = t6[f4 + 3], o8 += s4;
  }
  else for (let c4 = 0; c4 < i5; ++c4) {
    const f4 = 4 * e8[c4];
    for (let e9 = 0; e9 < r7; ++e9) n6[o8] = t6[f4], n6[o8 + 1] = t6[f4 + 1], n6[o8 + 2] = t6[f4 + 2], n6[o8 + 3] = t6[f4 + 3], o8 += s4;
  }
}
function p2(e8, t6, f3) {
  const o8 = e8.typedBuffer, r7 = e8.typedBufferStride;
  t6 *= r7;
  for (let n6 = 0; n6 < f3; ++n6) o8[t6] = 0, o8[t6 + 1] = 0, o8[t6 + 2] = 0, o8[t6 + 3] = 0, t6 += r7;
}
function g4(e8, f3, o8, r7, n6, s4 = 1) {
  if (!o8) return void u6(e8, f3, r7, n6, s4);
  const i5 = r7.typedBuffer, c4 = r7.typedBufferStride, l5 = e8.length, d3 = o8[0], a5 = o8[1], p3 = o8[2], y4 = o8[4], B = o8[5], g5 = o8[6], b2 = o8[8], h3 = o8[9], O6 = o8[10], N2 = o8[12], S4 = o8[13], m2 = o8[14];
  n6 *= c4;
  let A3 = 0, L = 0, R2 = 0;
  const v3 = H(o8) ? (e9) => {
    A3 = f3[e9] + N2, L = f3[e9 + 1] + S4, R2 = f3[e9 + 2] + m2;
  } : (e9) => {
    const t6 = f3[e9], o9 = f3[e9 + 1], r8 = f3[e9 + 2];
    A3 = d3 * t6 + y4 * o9 + b2 * r8 + N2, L = a5 * t6 + B * o9 + h3 * r8 + S4, R2 = p3 * t6 + g5 * o9 + O6 * r8 + m2;
  };
  if (1 === s4) for (let t6 = 0; t6 < l5; ++t6) v3(3 * e8[t6]), i5[n6] = A3, i5[n6 + 1] = L, i5[n6 + 2] = R2, n6 += c4;
  else for (let t6 = 0; t6 < l5; ++t6) {
    v3(3 * e8[t6]);
    for (let e9 = 0; e9 < s4; ++e9) i5[n6] = A3, i5[n6 + 1] = L, i5[n6 + 2] = R2, n6 += c4;
  }
}
function b(e8, o8, r7, n6, s4, i5 = 1) {
  if (!r7) return void u6(e8, o8, n6, s4, i5);
  const c4 = r7, l5 = n6.typedBuffer, d3 = n6.typedBufferStride, a5 = e8.length, p3 = c4[0], y4 = c4[1], B = c4[2], g5 = c4[4], b2 = c4[5], h3 = c4[6], O6 = c4[8], N2 = c4[9], S4 = c4[10], m2 = !G(c4), A3 = 1e-6, L = 1 - A3;
  s4 *= d3;
  let R2 = 0, v3 = 0, E3 = 0;
  const F = H(c4) ? (e9) => {
    R2 = o8[e9], v3 = o8[e9 + 1], E3 = o8[e9 + 2];
  } : (e9) => {
    const t6 = o8[e9], f3 = o8[e9 + 1], r8 = o8[e9 + 2];
    R2 = p3 * t6 + g5 * f3 + O6 * r8, v3 = y4 * t6 + b2 * f3 + N2 * r8, E3 = B * t6 + h3 * f3 + S4 * r8;
  };
  if (1 === i5) if (m2) for (let t6 = 0; t6 < a5; ++t6) {
    F(3 * e8[t6]);
    const f3 = R2 * R2 + v3 * v3 + E3 * E3;
    if (f3 < L && f3 > A3) {
      const e9 = 1 / Math.sqrt(f3);
      l5[s4] = R2 * e9, l5[s4 + 1] = v3 * e9, l5[s4 + 2] = E3 * e9;
    } else l5[s4] = R2, l5[s4 + 1] = v3, l5[s4 + 2] = E3;
    s4 += d3;
  }
  else for (let t6 = 0; t6 < a5; ++t6) F(3 * e8[t6]), l5[s4] = R2, l5[s4 + 1] = v3, l5[s4 + 2] = E3, s4 += d3;
  else for (let t6 = 0; t6 < a5; ++t6) {
    if (F(3 * e8[t6]), m2) {
      const e9 = R2 * R2 + v3 * v3 + E3 * E3;
      if (e9 < L && e9 > A3) {
        const t7 = 1 / Math.sqrt(e9);
        R2 *= t7, v3 *= t7, E3 *= t7;
      }
    }
    for (let e9 = 0; e9 < i5; ++e9) l5[s4] = R2, l5[s4 + 1] = v3, l5[s4 + 2] = E3, s4 += d3;
  }
}
function h2(e8, t6, o8, r7, n6, s4 = 1) {
  if (!o8) return void a4(e8, t6, r7, n6, s4);
  const i5 = o8, c4 = r7.typedBuffer, l5 = r7.typedBufferStride, d3 = e8.length, u7 = i5[0], p3 = i5[1], y4 = i5[2], B = i5[4], g5 = i5[5], b2 = i5[6], h3 = i5[8], O6 = i5[9], N2 = i5[10], S4 = !G(i5), m2 = 1e-6, A3 = 1 - m2;
  if (n6 *= l5, 1 === s4) for (let f3 = 0; f3 < d3; ++f3) {
    const o9 = 4 * e8[f3], r8 = t6[o9], s5 = t6[o9 + 1], i6 = t6[o9 + 2], d4 = t6[o9 + 3];
    let a5 = u7 * r8 + B * s5 + h3 * i6, L = p3 * r8 + g5 * s5 + O6 * i6, R2 = y4 * r8 + b2 * s5 + N2 * i6;
    if (S4) {
      const e9 = a5 * a5 + L * L + R2 * R2;
      if (e9 < A3 && e9 > m2) {
        const t7 = 1 / Math.sqrt(e9);
        a5 *= t7, L *= t7, R2 *= t7;
      }
    }
    c4[n6] = a5, c4[n6 + 1] = L, c4[n6 + 2] = R2, c4[n6 + 3] = d4, n6 += l5;
  }
  else for (let f3 = 0; f3 < d3; ++f3) {
    const o9 = 4 * e8[f3], r8 = t6[o9], i6 = t6[o9 + 1], d4 = t6[o9 + 2], a5 = t6[o9 + 3];
    let L = u7 * r8 + B * i6 + h3 * d4, R2 = p3 * r8 + g5 * i6 + O6 * d4, v3 = y4 * r8 + b2 * i6 + N2 * d4;
    if (S4) {
      const e9 = L * L + R2 * R2 + v3 * v3;
      if (e9 < A3 && e9 > m2) {
        const t7 = 1 / Math.sqrt(e9);
        L *= t7, R2 *= t7, v3 *= t7;
      }
    }
    for (let e9 = 0; e9 < s4; ++e9) c4[n6] = L, c4[n6 + 1] = R2, c4[n6 + 2] = v3, c4[n6 + 3] = a5, n6 += l5;
  }
}
function O5(e8, t6, f3, o8, r7, n6 = 1) {
  const s4 = o8.typedBuffer, i5 = o8.typedBufferStride, c4 = e8.length;
  if (r7 *= i5, f3 !== t6.length || 4 !== f3) if (1 !== n6) if (4 !== f3) for (let l5 = 0; l5 < c4; ++l5) {
    const f4 = 3 * e8[l5];
    for (let e9 = 0; e9 < n6; ++e9) s4[r7] = t6[f4], s4[r7 + 1] = t6[f4 + 1], s4[r7 + 2] = t6[f4 + 2], s4[r7 + 3] = 255, r7 += i5;
  }
  else for (let l5 = 0; l5 < c4; ++l5) {
    const f4 = 4 * e8[l5];
    for (let e9 = 0; e9 < n6; ++e9) s4[r7] = t6[f4], s4[r7 + 1] = t6[f4 + 1], s4[r7 + 2] = t6[f4 + 2], s4[r7 + 3] = t6[f4 + 3], r7 += i5;
  }
  else {
    if (4 === f3) {
      for (let f4 = 0; f4 < c4; ++f4) {
        const o9 = 4 * e8[f4];
        s4[r7] = t6[o9], s4[r7 + 1] = t6[o9 + 1], s4[r7 + 2] = t6[o9 + 2], s4[r7 + 3] = t6[o9 + 3], r7 += i5;
      }
      return;
    }
    for (let f4 = 0; f4 < c4; ++f4) {
      const o9 = 3 * e8[f4];
      s4[r7] = t6[o9], s4[r7 + 1] = t6[o9 + 1], s4[r7 + 2] = t6[o9 + 2], s4[r7 + 3] = 255, r7 += i5;
    }
  }
  else {
    s4[r7] = t6[0], s4[r7 + 1] = t6[1], s4[r7 + 2] = t6[2], s4[r7 + 3] = t6[3];
    const e9 = new Uint32Array(o8.typedBuffer.buffer, o8.start), f4 = i5 / 4, l5 = e9[r7 /= 4];
    r7 += f4;
    const d3 = c4 * n6;
    for (let t7 = 1; t7 < d3; ++t7) e9[r7] = l5, r7 += f4;
  }
}
function N(e8, t6, f3, o8, r7 = 1) {
  const n6 = t6.typedBuffer, s4 = t6.typedBufferStride;
  if (o8 *= s4, 1 === r7) for (let i5 = 0; i5 < f3; ++i5) n6[o8] = e8[0], n6[o8 + 1] = e8[1], n6[o8 + 2] = e8[2], n6[o8 + 3] = e8[3], o8 += s4;
  else for (let i5 = 0; i5 < f3; ++i5) for (let t7 = 0; t7 < r7; ++t7) n6[o8] = e8[0], n6[o8 + 1] = e8[1], n6[o8 + 2] = e8[2], n6[o8 + 3] = e8[3], o8 += s4;
}
function S3(t6, f3, l5, u7, p3, y4) {
  for (const B of f3.fieldNames) {
    const f4 = t6.vertexAttributes.get(B), S4 = t6.indices.get(B);
    if (f4 && S4) switch (B) {
      case O3.POSITION: {
        s(3 === f4.size);
        const e8 = p3.getField(B, i);
        s(!!e8, `No buffer view for ${B}`), e8 && g4(S4, f4.data, l5, e8, y4);
        break;
      }
      case O3.NORMAL: {
        s(3 === f4.size);
        const e8 = p3.getField(B, i);
        s(!!e8, `No buffer view for ${B}`), e8 && b(S4, f4.data, u7, e8, y4);
        break;
      }
      case O3.UV0: {
        s(2 === f4.size);
        const e8 = p3.getField(B, u2);
        s(!!e8, `No buffer view for ${B}`), e8 && d2(S4, f4.data, e8, y4);
        break;
      }
      case O3.COLOR:
      case O3.SYMBOLCOLOR: {
        s(3 === f4.size || 4 === f4.size);
        const e8 = p3.getField(B, x);
        s(!!e8, `No buffer view for ${B}`), e8 && O5(S4, f4.data, f4.size, e8, y4);
        break;
      }
      case O3.TANGENT: {
        s(4 === f4.size);
        const e8 = p3.getField(B, c2);
        s(!!e8, `No buffer view for ${B}`), e8 && h2(S4, f4.data, u7, e8, y4);
        break;
      }
      case O3.PROFILERIGHT:
      case O3.PROFILEUP:
      case O3.PROFILEVERTEXANDNORMAL:
      case O3.FEATUREVALUE: {
        s(4 === f4.size);
        const e8 = p3.getField(B, c2);
        s(!!e8, `No buffer view for ${B}`), e8 && a4(S4, f4.data, e8, y4);
      }
    }
    else if (B === O3.OBJECTANDLAYERIDCOLOR && r(t6.objectAndLayerIdColor)) {
      const e8 = t6.indices.get(O3.POSITION);
      if (s(!!e8, `No buffer view for ${B}`), e8) {
        const f5 = e8.length, o8 = p3.getField(B, x);
        N(t6.objectAndLayerIdColor, o8, f5, y4);
      }
    }
  }
}

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/DefaultBufferWriter.js
var r6 = class {
  constructor(t6) {
    this.vertexBufferLayout = t6;
  }
  allocate(t6) {
    return this.vertexBufferLayout.createBuffer(t6);
  }
  elementCount(e8) {
    return e8.indices.get(O3.POSITION).length;
  }
  write(t6, r7, i5, u7, f3) {
    S3(i5, this.vertexBufferLayout, t6, r7, u7, f3);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/StencilUtils.js
var i4 = { func: I.LESS };
var s3 = { func: I.ALWAYS };
var e7 = { mask: 255 };
var l4 = { mask: 0 };
var f2 = { function: { func: I.ALWAYS, ref: a.OutlineVisualElementMask, mask: a.OutlineVisualElementMask }, operation: { fail: O2.KEEP, zFail: O2.KEEP, zPass: O2.ZERO } };
var o7 = { function: { func: I.ALWAYS, ref: a.OutlineVisualElementMask, mask: a.OutlineVisualElementMask }, operation: { fail: O2.KEEP, zFail: O2.KEEP, zPass: O2.REPLACE } };
var P = { function: { func: I.EQUAL, ref: a.OutlineVisualElementMask, mask: a.OutlineVisualElementMask }, operation: { fail: O2.KEEP, zFail: O2.KEEP, zPass: O2.KEEP } };
var m = { function: { func: I.NOTEQUAL, ref: a.OutlineVisualElementMask, mask: a.OutlineVisualElementMask }, operation: { fail: O2.KEEP, zFail: O2.KEEP, zPass: O2.KEEP } };

export {
  r5 as r,
  n5 as n,
  o6 as o,
  s2 as s,
  v,
  E2 as E,
  u5 as u,
  y3 as y,
  a4 as a,
  p2 as p,
  g4 as g,
  b,
  O5 as O,
  N,
  S3 as S,
  r6 as r2,
  i4 as i,
  s3 as s2,
  e7 as e,
  l4 as l,
  f2 as f,
  o7 as o2,
  P,
  m
};
//# sourceMappingURL=chunk-NEOCII5B.js.map
