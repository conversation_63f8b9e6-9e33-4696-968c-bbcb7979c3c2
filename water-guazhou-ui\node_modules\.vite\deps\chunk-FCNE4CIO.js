import {
  o as o2,
  s as s3
} from "./chunk-MNZ66MSV.js";
import {
  o
} from "./chunk-YBNKNHCD.js";
import {
  u as u2
} from "./chunk-XBS7QZIQ.js";
import {
  x
} from "./chunk-6NE6A2GD.js";
import {
  b,
  h,
  k,
  m,
  y as y2
} from "./chunk-6NKJB2TO.js";
import {
  W
} from "./chunk-VNYCO3JG.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  r as r2
} from "./chunk-WXFAAYJL.js";
import {
  v as v2
} from "./chunk-X7FOCGBC.js";
import {
  K,
  ot
} from "./chunk-U4SVMKOQ.js";
import {
  c,
  e as e2,
  u
} from "./chunk-G5KX4JSG.js";
import {
  s as s2
} from "./chunk-KUPAGB4V.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/rest/support/fileFormat.js
var p = new s2({ PDF: "pdf", PNG32: "png32", PNG8: "png8", JPG: "jpg", GIF: "gif", EPS: "eps", SVG: "svg", SVGZ: "svgz" });
var n = p.fromJSON.bind(p);
var g = p.toJSON.bind(p);

// node_modules/@arcgis/core/rest/support/layoutTemplate.js
var t = new s2({ MAP_ONLY: "map-only", "A3 Landscape": "a3-landscape", "A3 Portrait": "a3-portrait", "A4 Landscape": "a4-landscape", "A4 Portrait": "a4-portrait", "Letter ANSI A Landscape": "letter-ansi-a-landscape", "Letter ANSI A Portrait": "letter-ansi-a-portrait", "Tabloid ANSI B Landscape": "tabloid-ansi-b-landscape", "Tabloid ANSI B Portrait": "tabloid-ansi-b-portrait" });
var r3 = t.fromJSON.bind(t);
var o3 = t.toJSON.bind(t);

// node_modules/@arcgis/core/rest/support/printTaskUtils.js
var o4 = "simple-marker";
var a2 = "picture-marker";
var l = "simple-line";
var s4 = "simple-fill";
var u3 = "shield-label-symbol";
var f = "text";
function y3(y4, c3) {
  const { graphic: p4, renderer: m3, symbol: d2 } = c3, g3 = d2.type;
  if (g3 === f || g3 === u3 || !("visualVariables" in m3) || !m3.visualVariables) return;
  const b3 = m3.getVisualVariablesForType("size"), h3 = m3.getVisualVariablesForType("color"), V2 = m3.getVisualVariablesForType("opacity"), w2 = m3.getVisualVariablesForType("rotation"), T2 = b3[0], v4 = h3[0], G2 = V2[0], S = w2[0];
  if (T2) {
    const t2 = g3 === o4 ? d2.style : null, i2 = y2(T2, p4, { shape: t2 });
    null != i2 && (g3 === o4 ? y4.size = e2(i2) : g3 === a2 ? (y4.width = e2(i2), y4.height = e2(i2)) : g3 === l ? y4.width = e2(i2) : y4.outline && (y4.outline.width = e2(i2)));
  }
  if (v4) {
    const e3 = b(v4, p4);
    (e3 && g3 === o4 || g3 === l || g3 === s4) && (y4.color = e3 ? e3.toJSON() : void 0);
  }
  if (G2) {
    const e3 = h(G2, p4);
    null != e3 && y4.color && (y4.color[3] = Math.round(255 * e3));
  }
  S && (y4.angle = -m(m3, p4));
}
function c2() {
  return { layerDefinition: { name: "multipointLayer", geometryType: "esriGeometryMultipoint", drawingInfo: { renderer: null } }, featureSet: { geometryType: "esriGeometryMultipoint", features: [] } };
}
function p2() {
  return { layerDefinition: { name: "polygonLayer", geometryType: "esriGeometryPolygon", drawingInfo: { renderer: null } }, featureSet: { geometryType: "esriGeometryPolygon", features: [] } };
}
function m2() {
  return { layerDefinition: { name: "pointLayer", geometryType: "esriGeometryPoint", drawingInfo: { renderer: null } }, featureSet: { geometryType: "esriGeometryPoint", features: [] } };
}
function d() {
  return { layerDefinition: { name: "polylineLayer", geometryType: "esriGeometryPolyline", drawingInfo: { renderer: null } }, featureSet: { geometryType: "esriGeometryPolyline", features: [] } };
}
function g2(e3, r4 = 15) {
  const t2 = e3.canvas.width, i2 = e3.canvas.height, n2 = e3.getImageData(0, 0, t2, i2).data;
  let o5, a3, l2, s5, u4, f2;
  e: for (a3 = i2; a3--; ) for (o5 = t2; o5--; ) if (n2[4 * (t2 * a3 + o5) + 3] > r4) {
    f2 = a3;
    break e;
  }
  if (!f2) return null;
  e: for (o5 = t2; o5--; ) for (a3 = f2 + 1; a3--; ) if (n2[4 * (t2 * a3 + o5) + 3] > r4) {
    u4 = o5;
    break e;
  }
  e: for (o5 = 0; o5 <= u4; ++o5) for (a3 = f2 + 1; a3--; ) if (n2[4 * (t2 * a3 + o5) + 3] > r4) {
    l2 = o5;
    break e;
  }
  e: for (a3 = 0; a3 <= f2; ++a3) for (o5 = l2; o5 <= u4; ++o5) if (n2[4 * (t2 * a3 + o5) + 3] > r4) {
    s5 = a3;
    break e;
  }
  return { x: l2, y: s5, width: u4 - l2, height: f2 - s5 };
}
function b2(e3, r4) {
  const t2 = e3.allLayerViews.items;
  if (r4 === e3.scale) return t2.filter((e4) => !e4.suspended);
  const i2 = new Array();
  for (const n2 of t2) T(n2.parent) && !i2.includes(n2.parent) || !n2.visible || r4 && "isVisibleAtScale" in n2 && !n2.isVisibleAtScale(r4) || i2.push(n2);
  return i2;
}
function h2(e3) {
  return "bing-maps" === (e3 == null ? void 0 : e3.type);
}
function V(e3) {
  return e3 && "blendMode" in e3 && "effect" in e3;
}
function w(e3) {
  return "csv" === (e3 == null ? void 0 : e3.type);
}
function T(e3) {
  return "esri.views.2d.layers.GroupLayerView2D" === (e3 == null ? void 0 : e3.declaredClass);
}
function v3(e3) {
  const r4 = e3.layer;
  if (V(r4)) {
    const t2 = r4.blendMode;
    if ((!t2 || "normal" === t2) && (r4.effect || "featureEffect" in e3 && e3.featureEffect)) return true;
  }
  return false;
}

// node_modules/@arcgis/core/rest/support/PrintTemplate.js
var p3 = class extends v {
  constructor(t2) {
    super(t2), this.attributionVisible = true, this.exportOptions = { width: 800, height: 1100, dpi: 96 }, this.forceFeatureAttributes = false, this.format = "png32", this.includeTables = false, this.label = null, this.layout = "map-only", this.layoutItem = null, this.layoutOptions = null, this.outScale = 0, this.scalePreserved = true, this.showLabels = true;
  }
};
e([y()], p3.prototype, "attributionVisible", void 0), e([y()], p3.prototype, "exportOptions", void 0), e([y()], p3.prototype, "forceFeatureAttributes", void 0), e([y()], p3.prototype, "format", void 0), e([y()], p3.prototype, "label", void 0), e([y()], p3.prototype, "layout", void 0), e([y({ type: x })], p3.prototype, "layoutItem", void 0), e([y()], p3.prototype, "layoutOptions", void 0), e([y()], p3.prototype, "outScale", void 0), e([y()], p3.prototype, "scalePreserved", void 0), e([y()], p3.prototype, "showLabels", void 0), p3 = e([a("esri.rest.support.PrintTemplate")], p3);
var i = p3;

// node_modules/@arcgis/core/rest/print.js
var P = { Feet: "ft", Kilometers: "km", Meters: "m", Miles: "mi" };
var F = new s2({ esriFeet: "Feet", esriKilometers: "Kilometers", esriMeters: "Meters", esriMiles: "Miles" });
var R = new s2({ esriExecutionTypeSynchronous: "sync", esriExecutionTypeAsynchronous: "async" });
var N = /* @__PURE__ */ new Map();
async function J(e3, i2, r4) {
  const a3 = C(e3);
  let n2 = N.get(a3);
  return Promise.resolve().then(() => n2 ? { data: n2.gpMetadata } : (n2 = { gpServerUrl: a3, is11xService: false, legendLayerNameMap: {}, legendLayers: [] }, U(a3, { query: { f: "json" } }))).then((e4) => (n2.gpMetadata = e4.data, n2.cimVersion = n2.gpMetadata.cimVersion, n2.is11xService = !!n2.cimVersion, N.set(a3, n2), j(i2, n2))).then((t2) => {
    const a4 = ye(n2);
    let s5;
    const o5 = (e4) => "sync" === a4 ? e4.results && e4.results[0] && e4.results[0].value : s5.fetchResultData("Output_File", null, r4).then((e5) => e5.value);
    return "async" === a4 ? s3(e3, t2, void 0, r4).then((e4) => (s5 = e4, e4.waitForJobCompletion({ interval: i2.updateDelay }).then(o5))) : o2(e3, t2, void 0, r4).then(o5);
  });
}
async function U2(e3) {
  const t2 = C(e3);
  return ye(N.get(t2));
}
async function j(t2, r4) {
  r4 = r4 || { is11xService: false, legendLayerNameMap: {}, legendLayers: [] };
  const a3 = t2.template || new i();
  null == a3.showLabels && (a3.showLabels = true);
  const n2 = a3.exportOptions;
  let s5;
  const o5 = o3(a3.layout);
  if (n2) {
    if (s5 = { dpi: n2.dpi }, "map_only" === o5.toLowerCase() || "" === o5) {
      const e3 = n2.width, t3 = n2.height;
      s5.outputSize = null != e3 && null != t3 ? [e3, t3] : void 0;
    }
  }
  const l2 = a3.layoutOptions;
  let c3;
  if (l2) {
    let e3, t3;
    "Miles" === l2.scalebarUnit || "Kilometers" === l2.scalebarUnit ? (e3 = "Kilometers", t3 = "Miles") : "Meters" !== l2.scalebarUnit && "Feet" !== l2.scalebarUnit || (e3 = "Meters", t3 = "Feet"), c3 = { titleText: l2.titleText, authorText: l2.authorText, copyrightText: l2.copyrightText, customTextElements: l2.customTextElements, elementOverrides: l2.elementOverrides, scaleBarOptions: { metricUnit: F.toJSON(e3), metricLabel: e3 ? P[e3] : void 0, nonMetricUnit: F.toJSON(t3), nonMetricLabel: t3 ? P[t3] : void 0 } };
  }
  let u4 = null;
  (l2 == null ? void 0 : l2.legendLayers) && (u4 = l2.legendLayers.map((e3) => {
    const t3 = e3.layerId;
    r4.legendLayerNameMap[t3] = e3.title;
    const i2 = { id: t3 };
    return e3.subLayerIds && (i2.subLayerIds = e3.subLayerIds), i2;
  }));
  const y4 = await A(t2, a3, r4);
  if (y4.operationalLayers) {
    const e3 = new RegExp("[\\u4E00-\\u9FFF\\u0E00-\\u0E7F\\u0900-\\u097F\\u3040-\\u309F\\u30A0-\\u30FF\\u31F0-\\u31FF]"), t3 = /[\u0600-\u06FF]/, a4 = (i2) => {
      const r5 = i2.text, a5 = i2.font, n4 = a5 && a5.family && a5.family.toLowerCase();
      r5 && a5 && ("arial" === n4 || "arial unicode ms" === n4) && (a5.family = e3.test(r5) ? "Arial Unicode MS" : "Arial", "normal" !== a5.style && t3.test(r5) && (a5.family = "Arial Unicode MS"));
    }, n3 = () => {
      throw new s("print:cim-symbol-unsupported", "CIMSymbol is not supported by a print service published from ArcMap");
    };
    y4.operationalLayers.forEach((e4) => {
      var _a, _b, _c;
      ((_a = e4.featureCollection) == null ? void 0 : _a.layers) ? e4.featureCollection.layers.forEach((e5) => {
        var _a2, _b2, _c2, _d;
        if ((_c2 = (_b2 = (_a2 = e5.layerDefinition) == null ? void 0 : _a2.drawingInfo) == null ? void 0 : _b2.renderer) == null ? void 0 : _c2.symbol) {
          const t4 = e5.layerDefinition.drawingInfo.renderer;
          "esriTS" === t4.symbol.type ? a4(t4.symbol) : "CIMSymbolReference" !== t4.symbol.type || r4.is11xService || n3();
        }
        ((_d = e5.featureSet) == null ? void 0 : _d.features) && e5.featureSet.features.forEach((e6) => {
          e6.symbol && ("esriTS" === e6.symbol.type ? a4(e6.symbol) : "CIMSymbolReference" !== e6.symbol.type || r4.is11xService || n3());
        });
      }) : !r4.is11xService && ((_c = (_b = e4.layerDefinition) == null ? void 0 : _b.drawingInfo) == null ? void 0 : _c.renderer) && JSON.stringify(e4.layerDefinition.drawingInfo.renderer).includes('"type":"CIMSymbolReference"') && n3();
    });
  }
  t2.outSpatialReference && (y4.mapOptions.spatialReference = t2.outSpatialReference.toJSON()), Object.assign(y4, { exportOptions: s5, layoutOptions: c3 || {} }), Object.assign(y4.layoutOptions, { legendOptions: { operationalLayers: null != u4 ? u4 : r4.legendLayers.slice() } }), r4.legendLayers.length = 0, N.set(r4.gpServerUrl, r4);
  const f2 = { Web_Map_as_JSON: JSON.stringify(y4), Format: g(a3.format), Layout_Template: o5, Layout_Item_ID: void 0 };
  if (a3.layoutItem) {
    delete f2.Layout_Template;
    const t3 = a3.layoutItem;
    await t3.load(), "public" !== t3.access && r2 && await r2.getCredential(r4.gpServerUrl), f2.Layout_Item_ID = JSON.stringify({ id: t3.id });
  }
  return t2.extraParameters && Object.assign(f2, t2.extraParameters), f2;
}
async function A(e3, t2, i2) {
  const r4 = e3.view;
  let n2 = r4.spatialReference;
  const s5 = { operationalLayers: await k2(r4, t2, i2) };
  t2.includeTables && (s5.tables = await te(r4));
  let o5 = i2.ssExtent || e3.extent || r4.extent;
  if (n2 && n2.isWrappable && (o5 = o5.clone()._normalize(true), n2 = o5.spatialReference), s5.mapOptions = { extent: o5 && o5.toJSON(), spatialReference: n2 && n2.toJSON(), showAttribution: t2.attributionVisible }, i2.ssExtent = null, r4.background && (s5.background = r4.background.toJSON()), r4.rotation && (s5.mapOptions.rotation = -r4.rotation), t2.scalePreserved && (s5.mapOptions.scale = t2.outScale || r4.scale), r(r4.timeExtent)) {
    const e4 = r(r4.timeExtent.start) ? r4.timeExtent.start.getTime() : null, t3 = r(r4.timeExtent.end) ? r4.timeExtent.end.getTime() : null;
    s5.mapOptions.time = [e4, t3];
  }
  return s5;
}
function C(e3) {
  let t2 = e3;
  const i2 = t2.lastIndexOf("/GPServer/");
  return i2 > 0 && (t2 = t2.slice(0, i2 + 9)), t2;
}
async function k2(e3, t2, i2) {
  const r4 = [], a3 = { layerView: null, printTemplate: t2, view: e3 };
  let n2 = 0;
  t2.scalePreserved && (n2 = t2.outScale || e3.scale);
  const s5 = b2(e3, n2);
  for (const o5 of s5) {
    const e4 = o5.layer;
    if (!e4.loaded || "group" === (e4 == null ? void 0 : e4.type)) continue;
    let t3;
    a3.layerView = o5, t3 = v3(o5) ? await Z(e4, a3, i2) : h2(e4) ? z(e4) : w(e4) ? await _(e4, a3, i2) : "feature" === (e4 == null ? void 0 : e4.type) ? await q(e4, a3, i2) : "geojson" === (e4 == null ? void 0 : e4.type) ? await K2(e4, a3, i2) : "graphics" === (e4 == null ? void 0 : e4.type) ? await W2(e4, a3, i2) : "imagery" === (e4 == null ? void 0 : e4.type) ? B(e4, i2) : "imagery-tile" === (e4 == null ? void 0 : e4.type) ? await G(e4, a3, i2) : "kml" === (e4 == null ? void 0 : e4.type) ? await Q(e4, a3, i2) : "map-image" === (e4 == null ? void 0 : e4.type) ? H(e4, a3, i2) : "map-notes" === (e4 == null ? void 0 : e4.type) ? await X(a3, i2) : "open-street-map" === (e4 == null ? void 0 : e4.type) ? Y() : "stream" === (e4 == null ? void 0 : e4.type) ? await ee(e4, a3, i2) : "tile" === (e4 == null ? void 0 : e4.type) ? ie(e4, i2) : "vector-tile" === (e4 == null ? void 0 : e4.type) ? await re(e4, a3, i2) : "web-tile" === (e4 == null ? void 0 : e4.type) ? ae(e4) : "wms" === (e4 == null ? void 0 : e4.type) ? ne(e4) : "wmts" === (e4 == null ? void 0 : e4.type) ? se(e4) : await Z(e4, a3, i2), t3 && (Array.isArray(t3) ? r4.push(...t3) : (t3.id = e4.id, t3.title = i2.legendLayerNameMap[e4.id] || e4.title, t3.opacity = e4.opacity, t3.minScale = e4.minScale || 0, t3.maxScale = e4.maxScale || 0, V(e4) && e4.blendMode && "normal" !== e4.blendMode && (t3.blendMode = e4.blendMode), r4.push(t3)));
  }
  if (n2 && r4.forEach((e4) => {
    e4.minScale = 0, e4.maxScale = 0;
  }), e3.graphics && e3.graphics.length) {
    const a4 = await $(null, e3.graphics, t2, i2);
    a4 && r4.push(a4);
  }
  return r4;
}
function z(e3) {
  return { culture: e3.culture, key: e3.key, type: "BingMaps" + ("aerial" === e3.style ? "Aerial" : "hybrid" === e3.style ? "Hybrid" : "Road") };
}
async function _(e3, t2, i2) {
  e3.legendEnabled && i2.legendLayers.push({ id: e3.id });
  const r4 = t2.layerView, a3 = t2.printTemplate;
  let n2;
  if (!i2.is11xService || r4.filter) {
    return $(e3, await ue(r4), a3, i2);
  }
  return n2 = { type: "CSV" }, e3.write(n2, { origin: "web-map" }), delete n2.popupInfo, delete n2.layerType, n2.showLabels = a3.showLabels && e3.labelsVisible, n2;
}
async function $(e3, t2, i2, r4) {
  var _a, _b;
  let a3;
  const n2 = p2(), s5 = d(), o5 = m2(), l2 = c2(), c3 = m2();
  if (c3.layerDefinition.name = "textLayer", delete c3.layerDefinition.drawingInfo, e3) {
    if ("esri.layers.FeatureLayer" === e3.declaredClass || "esri.layers.StreamLayer" === e3.declaredClass ? n2.layerDefinition.name = s5.layerDefinition.name = o5.layerDefinition.name = l2.layerDefinition.name = r4.legendLayerNameMap[e3.id] || e3.get("arcgisProps.title") || e3.title : "esri.layers.GraphicsLayer" === e3.declaredClass && (t2 = e3.graphics.items), e3.renderer) {
      const t3 = e3.renderer.toJSON(), i3 = n2.layerDefinition.drawingInfo;
      i3 && (i3.renderer = t3);
      const r5 = s5.layerDefinition.drawingInfo;
      r5 && (r5.renderer = t3);
      const a4 = o5.layerDefinition.drawingInfo;
      a4 && (a4.renderer = t3);
      const c4 = l2.layerDefinition.drawingInfo;
      c4 && (c4.renderer = t3);
    }
    if (i2.showLabels && e3.labelsVisible && "function" == typeof e3.write) {
      const t3 = (_b = (_a = e3.write({}, { origin: "web-map" }).layerDefinition) == null ? void 0 : _a.drawingInfo) == null ? void 0 : _b.labelingInfo;
      if (t3) {
        a3 = true;
        const e4 = n2.layerDefinition.drawingInfo;
        e4 && (e4.labelingInfo = t3);
        const i3 = s5.layerDefinition.drawingInfo;
        i3 && (i3.labelingInfo = t3);
        const r5 = o5.layerDefinition.drawingInfo;
        r5 && (r5.labelingInfo = t3);
        const c4 = l2.layerDefinition.drawingInfo;
        c4 && (c4.labelingInfo = t3);
      }
    }
  }
  let f2;
  (e3 == null ? void 0 : e3.renderer) || a3 || (delete n2.layerDefinition.drawingInfo, delete s5.layerDefinition.drawingInfo, delete o5.layerDefinition.drawingInfo, delete l2.layerDefinition.drawingInfo);
  const m3 = e3 == null ? void 0 : e3.fieldsIndex, p4 = e3 == null ? void 0 : e3.renderer;
  if (m3) {
    const t3 = /* @__PURE__ */ new Set();
    a3 && await W(t3, e3), p4 && "function" == typeof p4.collectRequiredFields && await p4.collectRequiredFields(t3, m3), f2 = Array.from(t3);
    const i3 = m3.fields.map((e4) => e4.toJSON());
    n2.layerDefinition.fields = i3, s5.layerDefinition.fields = i3, o5.layerDefinition.fields = i3, l2.layerDefinition.fields = i3;
  }
  const d2 = t2 && t2.length;
  let g3;
  for (let y4 = 0; y4 < d2; y4++) {
    const a4 = t2[y4] || t2.getItemAt(y4);
    if (false === a4.visible || !a4.geometry) continue;
    if (g3 = a4.toJSON(), g3.hasOwnProperty("popupTemplate") && delete g3.popupTemplate, g3.geometry && g3.geometry.z && delete g3.geometry.z, g3.symbol && g3.symbol.outline && "esriCLS" === g3.symbol.outline.type && !r4.is11xService) continue;
    !r4.is11xService && g3.symbol && g3.symbol.outline && g3.symbol.outline.color && g3.symbol.outline.color[3] && (g3.symbol.outline.color[3] = 255);
    const m4 = e3 && e3.renderer && ("valueExpression" in e3.renderer && e3.renderer.valueExpression || "hasVisualVariables" in e3.renderer && e3.renderer.hasVisualVariables());
    if (!g3.symbol && e3 && e3.renderer && m4 && !r4.is11xService) {
      const t3 = e3.renderer, i3 = await t3.getSymbolAsync(a4);
      if (!i3) continue;
      g3.symbol = i3.toJSON(), "hasVisualVariables" in t3 && t3.hasVisualVariables() && y3(g3.symbol, { renderer: t3, graphic: a4, symbol: i3 });
    }
    if (g3.symbol && (g3.symbol.angle || delete g3.symbol.angle, fe(g3.symbol) ? g3.symbol = await le(g3.symbol, r4) : g3.symbol.text && delete g3.attributes), (!i2 || !i2.forceFeatureAttributes) && (f2 == null ? void 0 : f2.length)) {
      const e4 = {};
      f2.forEach((t3) => {
        g3.attributes && g3.attributes.hasOwnProperty(t3) && (e4[t3] = g3.attributes[t3]);
      }), g3.attributes = e4;
    }
    "polygon" === a4.geometry.type ? n2.featureSet.features.push(g3) : "polyline" === a4.geometry.type ? s5.featureSet.features.push(g3) : "point" === a4.geometry.type ? g3.symbol && g3.symbol.text ? c3.featureSet.features.push(g3) : o5.featureSet.features.push(g3) : "multipoint" === a4.geometry.type ? l2.featureSet.features.push(g3) : "extent" === a4.geometry.type && (g3.geometry = v2.fromExtent(a4.geometry).toJSON(), n2.featureSet.features.push(g3));
  }
  const b3 = [n2, s5, l2, o5, c3].filter((e4) => e4.featureSet.features.length > 0);
  for (const u4 of b3) {
    const e4 = u4.featureSet.features.every((e5) => e5.symbol);
    !e4 || i2 && i2.forceFeatureAttributes || u4.featureSet.features.forEach((e5) => {
      delete e5.attributes;
    }), e4 && delete u4.layerDefinition.drawingInfo, u4.layerDefinition.drawingInfo && u4.layerDefinition.drawingInfo.renderer && await ce(u4.layerDefinition.drawingInfo.renderer, r4);
  }
  return b3.length ? { featureCollection: { layers: b3 }, showLabels: a3 } : null;
}
async function q(e3, t2, i2) {
  var _a, _b, _c, _d, _e, _f;
  let r4;
  const a3 = e3.renderer, n2 = parseFloat(i2.cimVersion);
  if ("binning" === ((_a = e3.featureReduction) == null ? void 0 : _a.type) || "cluster" === ((_b = e3.featureReduction) == null ? void 0 : _b.type) && (!i2.is11xService || n2 < 2.9) || "pie-chart" === (a3 == null ? void 0 : a3.type) || "dot-density" === (a3 == null ? void 0 : a3.type) && (!i2.is11xService || n2 < 2.6)) return Z(e3, t2, i2);
  e3.legendEnabled && i2.legendLayers.push({ id: e3.id });
  const s5 = t2.layerView, { printTemplate: o5, view: l2 } = t2, c3 = a3 && ("valueExpression" in a3 && a3.valueExpression || "hasVisualVariables" in a3 && a3.hasVisualVariables()), u4 = "feature-layer" !== ((_c = e3.source) == null ? void 0 : _c.type) && "ogc-feature" !== ((_d = e3.source) == null ? void 0 : _d.type);
  if (!i2.is11xService && c3 || s5.filter || u4 || !a3 || "field" in a3 && null != a3.field && ("string" != typeof a3.field || !e3.getField(a3.field))) {
    const t3 = await ue(s5);
    r4 = await $(e3, t3, o5, i2);
  } else {
    if (r4 = { id: (y4 = e3.write()).id, title: y4.title, opacity: y4.opacity, minScale: y4.minScale, maxScale: y4.maxScale, url: y4.url, layerType: y4.layerType, customParameters: y4.customParameters, layerDefinition: y4.layerDefinition }, r4.showLabels = o5.showLabels && e3.labelsVisible, oe(r4, e3), ((_f = (_e = r4.layerDefinition) == null ? void 0 : _e.drawingInfo) == null ? void 0 : _f.renderer) && (delete r4.layerDefinition.minScale, delete r4.layerDefinition.maxScale, await ce(r4.layerDefinition.drawingInfo.renderer, i2), "visualVariables" in a3 && a3.visualVariables && a3.visualVariables[0])) {
      const e4 = a3.visualVariables[0];
      if ("size" === e4.type && e4.maxSize && "number" != typeof e4.maxSize && e4.minSize && "number" != typeof e4.minSize) {
        const t4 = k(e4, l2.scale);
        r4.layerDefinition.drawingInfo.renderer.visualVariables[0].minSize = t4.minSize, r4.layerDefinition.drawingInfo.renderer.visualVariables[0].maxSize = t4.maxSize;
      }
    }
    const t3 = o(s5);
    t3 && (r4.layerDefinition || (r4.layerDefinition = {}), r4.layerDefinition.definitionExpression = r4.layerDefinition.definitionExpression ? `(${r4.layerDefinition.definitionExpression}) AND (${t3})` : t3);
  }
  var y4;
  return r4;
}
async function K2(e3, t2, i2) {
  var _a, _b;
  if ("binning" === ((_a = e3.featureReduction) == null ? void 0 : _a.type) || "cluster" === ((_b = e3.featureReduction) == null ? void 0 : _b.type)) return Z(e3, t2, i2);
  e3.legendEnabled && i2.legendLayers.push({ id: e3.id });
  return $(e3, await ue(t2.layerView), t2.printTemplate, i2);
}
async function W2(e3, { printTemplate: t2 }, i2) {
  return $(e3, null, t2, i2);
}
function B(e3, t2) {
  e3.legendEnabled && t2.legendLayers.push({ id: e3.id });
  const i2 = { layerType: (r4 = e3.write()).layerType, customParameters: r4.customParameters };
  var r4;
  if (i2.bandIds = e3.bandIds, i2.compressionQuality = e3.compressionQuality, i2.format = e3.format, i2.interpolation = e3.interpolation, (e3.mosaicRule || e3.definitionExpression) && (i2.mosaicRule = e3.exportImageServiceParameters.mosaicRule.toJSON()), e3.renderingRule || e3.renderer) if (t2.is11xService) e3.renderingRule && (i2.renderingRule = e3.renderingRule.toJSON()), e3.renderer && (i2.layerDefinition = i2.layerDefinition || {}, i2.layerDefinition.drawingInfo = i2.layerDefinition.drawingInfo || {}, i2.layerDefinition.drawingInfo.renderer = e3.renderer.toJSON());
  else {
    const t3 = e3.exportImageServiceParameters.combineRendererWithRenderingRule();
    t3 && (i2.renderingRule = t3.toJSON());
  }
  return oe(i2, e3), i2;
}
async function G(e3, t2, i2) {
  var _a;
  if ("flow" === ((_a = e3.renderer) == null ? void 0 : _a.type)) return Z(e3, t2, i2);
  e3.legendEnabled && i2.legendLayers.push({ id: e3.id });
  const r4 = { bandIds: (a3 = e3.write() || {}).bandIds, customParameters: a3.customParameters, interpolation: a3.interpolation, layerDefinition: a3.layerDefinition };
  var a3;
  return r4.layerType = "ArcGISImageServiceLayer", oe(r4, e3), r4;
}
async function Q(e3, t2, i2) {
  const r4 = t2.printTemplate;
  if (i2.is11xService) {
    const t3 = { type: "kml" };
    return e3.write(t3, { origin: "web-map" }), delete t3.layerType, t3.url = K(e3.url), t3;
  }
  {
    const a3 = [], n2 = t2.layerView;
    n2.allVisibleMapImages.forEach((t3, i3) => {
      const r5 = { id: `${e3.id}_image${i3}`, type: "image", title: e3.id, minScale: e3.minScale || 0, maxScale: e3.maxScale || 0, opacity: e3.opacity, extent: t3.extent };
      "data:image/png;base64," === t3.href.substr(0, 22) ? r5.imageData = t3.href.substr(22) : r5.url = t3.href, a3.push(r5);
    });
    const s5 = [...n2.allVisiblePoints.items, ...n2.allVisiblePolylines.items, ...n2.allVisiblePolygons.items], o5 = { id: e3.id, ...await $(null, s5, r4, i2) };
    return a3.push(o5), a3;
  }
}
function H(e3, { view: t2 }, i2) {
  var _a, _b;
  let r4;
  const a3 = { id: e3.id, subLayerIds: [] };
  let n2 = [];
  const s5 = t2.scale, o5 = (e4) => {
    const t3 = 0 === s5, i3 = 0 === e4.minScale || s5 <= e4.minScale, r5 = 0 === e4.maxScale || s5 >= e4.maxScale;
    if (e4.visible && (t3 || i3 && r5)) if (e4.sublayers) e4.sublayers.forEach(o5);
    else {
      const t4 = e4.toExportImageJSON(), i4 = { id: e4.id, name: e4.title, layerDefinition: { drawingInfo: t4.drawingInfo, definitionExpression: t4.definitionExpression, source: t4.source } };
      n2.unshift(i4), a3.subLayerIds.push(e4.id);
    }
  };
  var l2;
  return e3.sublayers && e3.sublayers.forEach(o5), n2.length && (n2 = n2.map(({ id: e4, name: t3, layerDefinition: i3 }) => ({ id: e4, name: t3, layerDefinition: i3 })), r4 = { layerType: (l2 = e3.write()).layerType, customParameters: l2.customParameters }, r4.layers = n2, r4.visibleLayers = ((_b = (_a = e3.capabilities) == null ? void 0 : _a.exportMap) == null ? void 0 : _b.supportsDynamicLayers) ? void 0 : a3.subLayerIds, oe(r4, e3), e3.legendEnabled && i2.legendLayers.push(a3)), r4;
}
async function X({ layerView: e3, printTemplate: t2 }, i2) {
  const r4 = [], n2 = e3.layer;
  if (r(n2.featureCollections)) for (const a3 of n2.featureCollections) {
    const e4 = await $(a3, a3.source, t2, i2);
    e4 && r4.push(...e4.featureCollection.layers);
  }
  else if (r(n2.sublayers)) for (const a3 of n2.sublayers) {
    const e4 = await $(null, a3.graphics, t2, i2);
    e4 && r4.push(...e4.featureCollection.layers);
  }
  return { featureCollection: { layers: r4 } };
}
function Y() {
  return { type: "OpenStreetMap" };
}
async function Z(e3, { printTemplate: t2, view: i2 }, r4) {
  var _a;
  const a3 = { type: "image" }, s5 = { format: "png", ignoreBackground: true, layers: [e3], rotation: 0 }, o5 = r4.ssExtent || i2.extent.clone();
  let l2 = 96, u4 = true, y4 = true;
  if (t2.exportOptions) {
    const e4 = t2.exportOptions;
    null != e4.dpi && e4.dpi > 0 && (l2 = e4.dpi), null != e4.width && e4.width > 0 && (u4 = e4.width % 2 == i2.width % 2), null != e4.height && e4.height > 0 && (y4 = e4.height % 2 == i2.height % 2);
  }
  if ("map-only" === t2.layout && t2.scalePreserved && (!t2.outScale || t2.outScale === i2.scale) && 96 === l2 && (!u4 || !y4) && (s5.area = { x: 0, y: 0, width: i2.width, height: i2.height }, u4 || (s5.area.width -= 1), y4 || (s5.area.height -= 1), !r4.ssExtent)) {
    const e4 = i2.toMap(c(s5.area.width, s5.area.height));
    o5.ymin = e4.y, o5.xmax = e4.x, r4.ssExtent = o5;
  }
  a3.extent = o5.clone()._normalize(true).toJSON();
  const f2 = await i2.takeScreenshot(s5);
  return a3.imageData = (_a = ot(f2.dataUrl)) == null ? void 0 : _a.data, a3;
}
async function ee(e3, { layerView: t2, printTemplate: i2 }, r4) {
  e3.legendEnabled && r4.legendLayers.push({ id: e3.id });
  return $(e3, await ue(t2), i2, r4);
}
async function te(e3) {
  var _a, _b;
  const t2 = [], i2 = [];
  for (const a3 of e3.map.allTables) "feature" !== a3.type || a3.loaded || i2.push(a3.load());
  i2.length && await Promise.allSettled(i2);
  for (const a3 of e3.map.allTables) if ("feature" === a3.type && a3.loaded && a3.isTable && "feature-layer" === ((_a = a3.source) == null ? void 0 : _a.type)) {
    const e4 = { id: (r4 = a3.write()).id, title: r4.title, customParameters: r4.customParameters, layerDefinition: { definitionExpression: (_b = r4.layerDefinition) == null ? void 0 : _b.definitionExpression } };
    oe(e4, a3), t2.push(e4);
  }
  var r4;
  return t2.length ? t2 : void 0;
}
function ie(e3, t2) {
  e3.legendEnabled && t2.legendLayers.push({ id: e3.id });
  const i2 = { layerType: (r4 = e3.write()).layerType, customParameters: r4.customParameters };
  var r4;
  return oe(i2, e3), i2;
}
async function re(e3, t2, i2) {
  if (i2.is11xService && e3.serviceUrl && e3.styleUrl) {
    const t3 = u2(e3.styleUrl, e3.apiKey), r4 = u2(e3.serviceUrl, e3.apiKey);
    if (!t3 && !r4 || "2.1.0" !== i2.cimVersion) {
      const i3 = { type: "VectorTileLayer" };
      return i3.styleUrl = K(e3.styleUrl), i3.token = t3, r4 !== t3 && (i3.additionalTokens = [{ url: e3.serviceUrl, token: r4 }]), i3;
    }
  }
  return Z(e3, t2, i2);
}
function ae(e3) {
  var _a;
  const t2 = (_a = e3.urlTemplate) == null ? void 0 : _a.replace(/\${/g, "{"), i2 = { type: "WebTiledLayer", urlTemplate: t2, credits: e3.copyright };
  return e3.subDomains && e3.subDomains.length > 0 && (i2.subDomains = e3.subDomains), i2;
}
function ne(e3) {
  let t2;
  const i2 = [], r4 = (e4) => {
    e4.visible && (e4.sublayers ? e4.sublayers.forEach(r4) : e4.name && i2.unshift(e4.name));
  };
  return e3.sublayers && e3.sublayers.forEach(r4), i2.length && (t2 = { type: "wms", customLayerParameters: e3.customLayerParameters, customParameters: e3.customParameters, transparentBackground: e3.imageTransparency, visibleLayers: i2, url: K(e3.url), version: e3.version }), t2;
}
function se(e3) {
  const t2 = e3.activeLayer;
  return { type: "wmts", customLayerParameters: e3.customLayerParameters, customParameters: e3.customParameters, format: t2.imageFormat, layer: t2.id, style: t2.styleId, tileMatrixSet: t2.tileMatrixSetId, url: K(e3.url) };
}
function oe(e3, t2) {
  t2.url && (e3.url = K(e3.url || t2.url), e3.token = u2(e3.url, t2.apiKey));
}
async function le(e3, i2) {
  var _a;
  i2.canvas || (i2.canvas = document.createElement("canvas"));
  const r4 = 1024;
  i2.canvas.width = r4, i2.canvas.height = r4;
  const a3 = i2.canvas.getContext("2d");
  let n2, l2;
  if (e3.path) {
    const t2 = new Path2D(e3.path);
    t2.closePath(), a3.fillStyle = Array.isArray(e3.color) ? `rgba(${e3.color[0]},${e3.color[1]},${e3.color[2]},${e3.color[3] / 255})` : "rgb(0,0,0)", a3.fill(t2);
    const i3 = g2(a3);
    if (!i3) return null;
    a3.clearRect(0, 0, r4, r4);
    const o5 = u(e3.size) / Math.max(i3.width, i3.height);
    a3.scale(o5, o5);
    const c3 = r4 / o5, u4 = c3 / 2 - i3.width / 2 - i3.x, y4 = c3 / 2 - i3.height / 2 - i3.y;
    if (a3.translate(u4, y4), Array.isArray(e3.color) && a3.fill(t2), ((_a = e3.outline) == null ? void 0 : _a.width) && Array.isArray(e3.outline.color)) {
      const r5 = e3.outline;
      a3.lineWidth = u(r5.width) / o5, a3.lineJoin = "round", a3.strokeStyle = `rgba(${r5.color[0]},${r5.color[1]},${r5.color[2]},${r5.color[3] / 255})`, a3.stroke(t2), i3.width += a3.lineWidth, i3.height += a3.lineWidth;
    }
    i3.width *= o5, i3.height *= o5;
    const f2 = a3.getImageData(r4 / 2 - i3.width / 2, r4 / 2 - i3.height / 2, Math.ceil(i3.width), Math.ceil(i3.height));
    n2 = f2.width, l2 = f2.height, a3.canvas.width = n2, a3.canvas.height = l2, a3.putImageData(f2, 0, 0);
  } else {
    const i3 = "image/svg+xml" === e3.contentType ? "data:image/svg+xml;base64," + e3.imageData : e3.url, r5 = (await U(i3, { responseType: "image" })).data;
    n2 = u(e3.width), l2 = u(e3.height), a3.canvas.width = n2, a3.canvas.height = l2, a3.drawImage(r5, 0, 0, a3.canvas.width, a3.canvas.height);
  }
  return { type: "esriPMS", imageData: a3.canvas.toDataURL("image/png").substr(22), angle: e3.angle, contentType: "image/png", height: e2(l2), width: e2(n2), xoffset: e3.xoffset, yoffset: e3.yoffset };
}
async function ce(e3, t2) {
  const i2 = e3.type;
  if ("simple" === i2 && fe(e3.symbol)) e3.symbol = await le(e3.symbol, t2);
  else if ("uniqueValue" === i2 || "classBreaks" === i2) {
    fe(e3.defaultSymbol) && (e3.defaultSymbol = await le(e3.defaultSymbol, t2));
    const r4 = e3["uniqueValue" === i2 ? "uniqueValueInfos" : "classBreakInfos"];
    if (r4) for (const e4 of r4) fe(e4.symbol) && (e4.symbol = await le(e4.symbol, t2));
  }
}
async function ue(e3) {
  return e3.queryFeatures(e3.createQuery()).then((e4) => e4.features);
}
function ye(e3) {
  return e3.gpMetadata && e3.gpMetadata.executionType ? R.fromJSON(e3.gpMetadata.executionType) : "sync";
}
function fe(e3) {
  return e3 && (e3.path || "image/svg+xml" === e3.contentType || e3.url && e3.url.endsWith(".svg"));
}

export {
  n,
  r3 as r,
  i,
  N,
  J,
  U2 as U,
  j,
  C
};
//# sourceMappingURL=chunk-FCNE4CIO.js.map
