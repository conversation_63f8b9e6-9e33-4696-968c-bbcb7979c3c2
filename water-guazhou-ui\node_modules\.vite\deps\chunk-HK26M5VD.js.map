{"version": 3, "sources": ["../../@arcgis/core/layers/support/rasterFunctions/rasterProjectionHelper.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../geometry.js\";import e from\"../../../core/Error.js\";import{isNone as t,isSome as n}from\"../../../core/maybe.js\";import{getMetersPerUnitForSR as o}from\"../../../core/unitUtils.js\";import{e as i,f as r,g as s,h as a,j as l}from\"../../../chunks/pe.js\";import{canProjectWithoutEngine as c,isLoaded as f,load as u,getTransformation as m,project as h}from\"../../../geometry/projection.js\";import x from\"../../../geometry/Extent.js\";import p from\"../../../geometry/Point.js\";import g from\"../../../geometry/Polygon.js\";import y from\"../../../geometry/SpatialReference.js\";var d;function M(e,t,n){return!c(e,t,n)}function w(t,n,o){const i=M(t,n,o);if(i&&!f())throw new e(\"rasterprojectionhelper-project\",\"projection engine is not loaded\");return i}!function(e){e[e.None=0]=\"None\",e[e.North=1]=\"North\",e[e.South=2]=\"South\",e[e.Both=3]=\"Both\"}(d||(d={}));const R=(e,t,n,o=0)=>{if(1===n[0])return[0,0];let i=1,r=-1,s=1,a=-1;for(let g=0;g<e.length;g+=2)isNaN(e[g])||(i=i>e[g]?e[g]:i,r=r>e[g]?r:e[g],s=s>e[g+1]?e[g+1]:s,a=a>e[g+1]?a:e[g+1]);const{cols:l,rows:c}=t,f=(r-i)/l/n[0],u=(a-s)/c/n[1],m=2*o;let h=0,x=!1,p=[0,0];for(let g=0;g<l-3;g++){for(let t=0;t<c-3;t++){const n=g*c*2+2*t,o=(e[n]+e[n+4]+e[n+4*c]+e[n+4*c+4])/4,i=(e[n+1]+e[n+5]+e[n+4*c+1]+e[n+4*c+5])/4,r=Math.abs((o-e[n+2*c+2])/f),s=Math.abs((i-e[n+2*c+3])/u);if(r+s>h&&(h=r+s,p=[r,s]),m&&h>m){x=!0;break}}if(x)break}return p},P={3395:20037508.342789244,3410:17334193.943686873,3857:20037508.342788905,3975:17367530.445161372,4087:20037508.342789244,4088:20015108.787169147,6933:17367530.445161372,32662:20037508.342789244,53001:20015086.79602057,53002:10007543.39801029,53003:20015086.79602057,53004:20015086.79602057,53016:14152803.599503474,53017:17333573.624304302,53034:20015086.79602057,53079:20015114.352186374,53080:20015114.352186374,54001:20037508.342789244,54002:10018754.171394624,54003:20037508.342789244,54004:20037508.342789244,54016:14168658.027268292,54017:17367530.44516137,54034:20037508.342789244,54079:20037508.342789244,54080:20037508.342789244,54100:20037508.342789244,54101:20037508.342789244},S=32,b=4,G=b,N=new Map,E=new Map,k=500;async function T(){f()||await u()}function v(e,t,n){if(!w(e.spatialReference,t))return null;return n?m(t,e.spatialReference,e):m(e.spatialReference,t,e)}function C(e,i,r,s=null){const a=e.spatialReference;if(a.equals(i))return e;w(a,i,s);const l=r.center,c=new x({xmin:l.x-e.x/2,xmax:l.x+e.x/2,ymin:l.y-e.y/2,ymax:l.y+e.y/2,spatialReference:a}),f=h(c,i,s),u=U(i);let m;if(t(f)||n(u)&&f.width>=u){const t=o(a)/o(i);m={x:e.x*t,y:e.y*t}}else m={x:f.width,y:f.height};return m}function _(e,t=.01){return o(e)?t/o(e):0}function j(e,t,n=null,o=!0){const i=e.spatialReference;if(i.equals(t))return e;w(i,t,n);const r=h(e,t,n);return o&&r?(z([e],[r],i,t),r):r}function z(e,t,o,i){const r=D(o,!0),s=D(i,!0),a=_(o,k),l=_(i,k);if(a&&n(r)&&n(s))for(let n=0;n<e.length;n++){const o=t[n];if(!o)continue;const{x:i}=e[n],{x:c}=o;c>=s[1]-l&&Math.abs(i-r[0])<a?o.x-=s[1]-s[0]:c<=s[0]+l&&Math.abs(i-r[1])<a&&(o.x+=s[1]-s[0])}}function W(e){const{inSR:t,outSR:o,datumTransformation:i,preferPE:r}=e;if(t.equals(o)){const{points:t}=F(e,null);return t}if(t.isWebMercator&&o.isWGS84||t.isWGS84&&o.isWebMercator)return I(e);if(w(t,o,i)&&r){if(t.isGeographic)return O(e);const o=A(t);if(n(o))return O(e)}return L(e)}function L(e){const{points:t}=F(e,null),{inSR:n,outSR:o,datumTransformation:i}=e,r=t.map((e=>new p(e[0],e[1],n))),s=h(r,o,i);return i&&z(r,s,n,o),s.map((e=>e?[e.x,e.y]:[NaN,NaN]))}function O(e){const{inSR:t,outSR:o,datumTransformation:l}=e,c=A(t),{points:f,mask:u}=F(e,c);if(!t.isGeographic){const e=t.wkid?i.coordsys(t.wkid):i.fromString(t.isGeographic?r.PE_TYPE_GEOGCS:r.PE_TYPE_PROJCS,t.wkt);s.projToGeog(e,f.length,f)}if(n(l)&&l.steps.length){let e;const t=179.9955;if(o.isGeographic&&(e=f.map((([e])=>e>t?1:e<-t?-1:0))),l.steps.forEach((e=>{const t=e.wkid?i.geogtran(e.wkid):i.fromString(r.PE_TYPE_GEOGTRAN,e.wkt);a.geogToGeog(t,f.length,f,null,e.isInverse?r.PE_TRANSFORM_2_TO_1:r.PE_TRANSFORM_1_TO_2)})),e)for(let n=0;n<f.length;n++){const o=e[n],i=f[n][0],r=i>t?1:i<-t?-1:0;o&&r&&o!==r&&(f[n][0]=o>0?i+360:i-360)}}if(!o.isGeographic){const e=A(o,!0),t=n(e)&&e.isEnvelope?[e.bbox[1],e.bbox[3]]:[-90,90];Y(f,t);const a=o.wkid?i.coordsys(o.wkid):i.fromString(o.isGeographic?r.PE_TYPE_GEOGCS:r.PE_TYPE_PROJCS,o.wkt);s.geogToProj(a,f.length,f)}let m=f;if(u&&f.length!==u.length){m=[];for(let e=0,t=0;e<u.length;e++)u[e]?m.push(f[t++]):m.push([NaN,NaN])}return m}function I(e){const{cols:t,rows:n,xres:o,yres:i,usePixelCenter:r,inSR:s,outSR:a}=e;let{xmin:l,ymax:c}=e;r&&(l+=o/2,c-=i/2);const f=[],u=[],m=Math.max(t,n);for(let g=0;g<m;g++){const e=l+o*Math.min(t,g),r=c-i*Math.min(n,g),m=h(new p({x:e,y:r,spatialReference:s}),a);g<=t&&f.push(m.x),g<=n&&u.push(m.y)}const x=[];for(let h=0;h<t;h++)for(let e=0;e<n;e++)x.push([f[h],u[e]]);return x}function A(e,t=!1){let n=e.wkid||e.wkt;if(!n||e.isGeographic)return null;if(n=String(n),N.has(n)){const e=N.get(n);return t?e?.gcs:e?.pcs}const o=e.wkid?i.coordsys(e.wkid):i.fromString(e.isGeographic?r.PE_TYPE_GEOGCS:r.PE_TYPE_PROJCS,e.wkt),s=B(o,_(e,1e-4)),a=B(o,0,!0);return N.set(n,{pcs:s,gcs:a}),t?a:s}function B(e,t=0,n=!1){const o=l.generate(e),i=n?e.horizonGcsGenerate():e.horizonPcsGenerate();if(!o||!i?.length)return null;let r=!1,s=i.find((e=>1===e.getInclusive()&&1===e.getKind()));if(!s){if(s=i.find((e=>1===e.getInclusive()&&0===e.getKind())),!s)return null;r=!0}const a=n?0:(2===o.getNorthPoleLocation()?1:0)|(2===o.getSouthPoleLocation()?2:0),c=o.isPannableRectangle(),f=s.getCoord();if(r)return{isEnvelope:r,isPannable:c,vertices:f,coef:null,bbox:[f[0][0]-t,f[0][1]-t,f[1][0]+t,f[1][1]+t],poleLocation:a};let u=0;const m=[];let[h,x]=f[0],[p,g]=f[0];for(let l=0,y=f.length;l<y;l++){u++,u===y&&(u=0);const[e,t]=f[l],[n,o]=f[u];if(o===t)m.push([e,n,t,o,2]);else{const i=(n-e)/(o-t||1e-4),r=e-i*t;t<o?m.push([i,r,t,o,0]):m.push([i,r,o,t,1])}h=h<e?h:e,x=x<t?x:t,p=p>e?p:e,g=g>t?g:t}return{isEnvelope:!1,isPannable:c,vertices:f,coef:m,bbox:[h,x,p,g],poleLocation:a}}function F(e,n){const o=[],{cols:i,rows:r,xres:s,yres:a,usePixelCenter:l}=e;let{xmin:c,ymax:f}=e;if(l&&(c+=s/2,f-=a/2),t(n)){for(let e=0;e<i;e++)for(let t=0;t<r;t++)o.push([c+s*e,f-a*t]);return{points:o}}const u=new Uint8Array(i*r);if(n.isEnvelope){const{bbox:[e,t,l,m]}=n;for(let h=0,x=0;h<i;h++){const i=c+s*h,p=n.isPannable||i>=e&&i<=l;for(let e=0;e<r;e++,x++){const n=f-a*e;p&&n>=t&&n<=m&&(o.push([i,n]),u[x]=1)}}return{points:o,mask:u}}const m=n.coef,h=[];for(let t=0;t<r;t++){const e=f-a*t,n=[],o=[];for(let t=0;t<m.length;t++){const[i,r,s,a,l]=m[t];if(e===s&&s===a)n.push(i),n.push(r),o.push(2),o.push(2);else if(e>=s&&e<=a){const t=i*e+r;n.push(t),o.push(l)}}let i=n;if(n.length>2){let e=2===o[0]?0:o[0],t=n[0];i=[];for(let r=1;r<o.length;r++)2===o[r]&&r!==o.length-1||(o[r]!==e&&(i.push(0===e?Math.min(t,n[r-1]):Math.max(t,n[r-1])),e=o[r],t=n[r]),r===o.length-1&&i.push(0===o[r]?Math.min(t,n[r]):Math.max(t,n[r])));i.sort(((e,t)=>e-t))}else n[0]>n[1]&&(i=[n[1],n[0]]);h.push(i)}for(let t=0,x=0;t<i;t++){const e=c+s*t;for(let t=0;t<r;t++,x++){const n=f-a*t,i=h[t];if(2===i.length)e>=i[0]&&e<=i[1]&&(o.push([e,n]),u[x]=1);else if(i.length>2){let t=!1;for(let n=0;n<i.length;n+=2)if(e>=i[n]&&e<=i[n+1]){t=!0;break}t&&(o.push([e,n]),u[x]=1)}}}return{points:o,mask:u}}function Y(e,t){const[n,o]=t;for(let i=0;i<e.length;i++){const t=e[i][1];(t<n||t>o)&&(e[i]=[NaN,NaN])}}function q(e){const n=U(e[0].spatialReference);if(e.length<2||t(n))return e[0];let{xmin:o,xmax:i,ymin:r,ymax:s}=e[0];for(let t=1;t<e.length;t++){const o=e[t];i=o.xmax+n*t,r=Math.min(r,o.ymin),s=Math.max(s,o.ymax)}return new x({xmin:o,xmax:i,ymin:r,ymax:s,spatialReference:e[0].spatialReference})}function J(e,o,i=null,r=!0){const s=e.spatialReference;if(s.equals(o))return e;const a=Q(e),l=U(s,!0),c=U(o);if(0===a||t(l)||t(c)){const a=K(e,o,i,r);if(t(l)&&n(c)&&Math.abs(a.width-c)<_(o)&&f()){const t=A(s);if(n(t)&&t.poleLocation===d.None&&e.width<(t.bbox[2]-t.bbox[0])/2)return X(e,o)||a}return a}const u=e.clone().normalize();if(1===u.length&&e.xmax<l&&e.xmax-l/2>_(s)){const{xmin:t,xmax:n}=e;for(let o=0;o<=a;o++){const i=0===o?t:-l/2,r=o===a?n-l*o:l/2;u[o]=new x({xmin:i,xmax:r,ymin:e.ymin,ymax:e.ymax,spatialReference:s})}}return q(u.map((e=>K(e,o,i,r))).filter(n))}function X(e,n){const o=U(n);if(t(o))return null;let{xmin:i,ymin:r,xmax:s,ymax:a}=e;const l=e.spatialReference,c=new g({spatialReference:l,rings:[[[i,r],[s,r],[s,a],[i,a],[i,r]]]}),f=h(c,n);if(2!==f.rings.length||!f.rings[0].length||!f.rings[1].length)return null;const{rings:u}=f,m=_(l),p=new x({spatialReference:n});for(let t=0;t<2;t++){i=s=u[t][0][0],r=a=u[t][0][1];for(let e=0;e<u[t].length;e++)i=i>u[t][e][0]?u[t][e][0]:i,s=s<u[t][e][0]?u[t][e][0]:s,r=r>u[t][e][1]?u[t][e][1]:r,a=a<u[t][e][1]?u[t][e][1]:a;if(0===t)p.ymin=r,p.ymax=a,p.xmin=i,p.xmax=s;else if(p.ymin=Math.min(p.ymin,r),p.ymax=Math.max(p.ymax,a),Math.abs(s-o/2)<m)p.xmin=i,p.xmax=p.xmax+o;else{if(!(Math.abs(i+o/2)<m))return null;p.xmax=s+o}}return p}function K(e,n,o=null,i=!0,r=!0){const s=e.spatialReference;if(s.equals(n)||!n)return e;w(s,n,o);const a=h(e,n,o);if(r&&n.isWebMercator&&a&&(a.ymax=Math.min(20037508.342787,a.ymax),a.ymin=Math.max(-20037508.342787,a.ymin),a.ymin>=a.ymax))return null;if(!i||!a)return a;const l=D(s,!0),c=D(n,!0);if(t(l)||t(c))return a;const f=_(s,.001),u=_(s,k),m=_(n,.001);if(Math.abs(a.xmin-c[0])<m&&Math.abs(a.xmax-c[1])<m){const t=Math.abs(e.xmin-l[0]),i=Math.abs(l[1]-e.xmax);if(t<f&&i>u){a.xmin=c[0];const t=[];t.push(new p(e.xmax,e.ymin,s)),t.push(new p(e.xmax,(e.ymin+e.ymax)/2,s)),t.push(new p(e.xmax,e.ymax,s));const i=t.map((e=>j(e,n,o))).filter((e=>!isNaN(e?.x))).map((e=>e.x));a.xmax=Math.max.apply(null,i)}if(i<f&&t>u){a.xmax=c[1];const t=[];t.push(new p(e.xmin,e.ymin,s)),t.push(new p(e.xmin,(e.ymin+e.ymax)/2,s)),t.push(new p(e.xmin,e.ymax,s));const i=t.map((e=>j(e,n,o))).filter((e=>!isNaN(e?.x))).map((e=>e.x));a.xmin=Math.min.apply(null,i)}}else{const e=_(n,.001);Math.abs(a.xmin-c[0])<e&&(a.xmin=c[0]),Math.abs(a.xmax-c[1])<e&&(a.xmax=c[1])}return a}function U(e,t=!1){if(!e)return null;const n=t?20037508.342787:20037508.342788905;return e.isWebMercator?2*n:e.wkid&&e.isGeographic?360:2*P[e.wkid]||null}function D(e,t=!1){if(e.isGeographic)return[-180,180];const o=U(e,t);return n(o)?[-o/2,o/2]:null}function H(e,t,n,o){let i=(e-t)/n;return i-Math.floor(i)!=0?i=Math.floor(i):o&&(i-=1),i}function Q(e,n=!1){const o=U(e.spatialReference);if(t(o))return 0;const i=n?0:-(o/2),r=_(e.spatialReference),s=!n&&Math.abs(e.xmax-o/2)<r?o/2:e.xmax,a=!n&&Math.abs(e.xmin+o/2)<r?-o/2:e.xmin;return H(s,i,o,!0)-H(a,i,o,!1)}function V(e){const o=e.storageInfo.origin.x,i=U(e.spatialReference,!0);if(t(i))return{originX:o,halfWorldWidth:null,pyramidsInfo:null};const r=i/2,{nativePixelSize:s,storageInfo:a,extent:l}=e,{maximumPyramidLevel:c,blockWidth:f,pyramidScalingFactor:u}=a;let m=s.x;const h=[],x=n(e.transform)&&\"gcs-shift\"===e.transform.type,p=o+(x?0:r),g=x?i-o:r-o;for(let t=0;t<=c;t++){const e=(l.xmax-o)/m/f,t=e-Math.floor(e)==0?e:Math.ceil(e),n=g/m/f,i=n-Math.floor(n)==0?n:Math.ceil(n),r=Math.floor(p/m/f),s=Math.round(p/m)%f,a=(f-Math.round(g/m)%f)%f;h.push({resolutionX:m,blockWidth:f,datsetColumnCount:t,worldColumnCountFromOrigin:i,leftMargin:s,rightPadding:a,originColumnOffset:r}),m*=u}return{originX:o,halfWorldWidth:r,pyramidsInfo:h,hasGCSSShiftTransform:x}}function Z(e){if(!e||e.isGeographic)return e;const t=String(e.wkid||e.wkt);let n;if(E.has(t))n=E.get(t);else{n=(e.wkid?i.coordsys(e.wkid):i.fromString(r.PE_TYPE_PROJCS,e.wkt)).getGeogcs().getCode(),E.set(t,n)}return new y({wkid:n})}function $(e){const t=e.isAdaptive&&null==e.spacing;let o=e.spacing||[S,S],i=ee(e),r={cols:i.size[0]+1,rows:i.size[1]+1};const s=i.outofBoundPointCount>0&&i.outofBoundPointCount<i.offsets.length/2;let a=i.outofBoundPointCount===i.offsets.length/2||t&&s?[0,0]:R(i.offsets,r,o,G);const l=(a[0]+a[1])/2,c=e.projectedExtent.spatialReference,f=e.srcBufferExtent.spatialReference;if(t&&(s||l>G)){M(c,f,e.datumTransformation)&&(c.isGeographic||n(A(c))),o=[b,b],i=ee({...e,spacing:o}),r={cols:i.size[0]+1,rows:i.size[1]+1},a=R(i.offsets,r,o,G)}if(i.error=a,o[0]>1&&(i.coefficients=te(i.offsets,r,s)),e.includeGCSGrid&&!c.isGeographic&&!c.isWebMercator)if(f.isGeographic)i.gcsGrid={offsets:i.offsets,coefficients:i.coefficients,spacing:o};else{const t=A(c);if(n(t)&&!t.isEnvelope){const t=Z(c),n=J(e.projectedExtent,t),{offsets:a}=ee({...e,srcBufferExtent:n,spacing:o}),l=te(a,r,s);i.gcsGrid={offsets:a,coefficients:l,spacing:o}}}return i}function ee(e){const{projectedExtent:t,srcBufferExtent:o,pixelSize:i,datumTransformation:r,rasterTransform:s}=e,a=t.spatialReference,l=o.spatialReference,c=w(a,l),{xmin:f,ymin:u,xmax:m,ymax:h}=t,x=U(l),g=n(x)&&(e.hasWrapAround||\"gcs-shift\"===s?.type),y=e.spacing||[S,S],d=y[0]*i.x,M=y[1]*i.y,R=1===y[0],P=Math.ceil((m-f)/d-.1/y[0])+(R?0:1),G=Math.ceil((h-u)/M-.1/y[1])+(R?0:1),N=W({cols:P,rows:G,xmin:f,ymax:h,xres:d,yres:M,inSR:a,outSR:l,datumTransformation:r,preferPE:y[0]<=b,usePixelCenter:R}),E=[];let T,v=0;const C=R?-1:NaN,{xmin:j,xmax:z,ymax:L,width:O,height:I}=o,B=_(l,k),F=n(x)&&j>0&&z>x/2;let Y=!1;if(c){const e=A(a);Y=n(e)&&e.poleLocation>0}for(let n=0;n<P;n++){const e=[];for(let t=0;t<G;t++){let o=N[n*G+t];if(g&&o[0]>z&&o[0]>x/2-B?o[0]-=x:g&&0===n&&o[0]<0&&F&&!s&&(o[0]+=x),!o||isNaN(o[0])||isNaN(o[1]))E.push(C),E.push(C),e.push(null),v++;else{if(s){const e=s.inverseTransform(new p({x:o[0],y:o[1],spatialReference:l}));o=[e.x,e.y]}e.push(o),n>0&&g&&T[t]&&o[0]<T[t][0]&&(o[0]+=x,Y&&o[0]>z&&o[0]>x&&(o[0]-=x)),E.push((o[0]-j)/O),E.push((L-o[1])/I)}}T=e}return{offsets:E,error:null,coefficients:null,outofBoundPointCount:v,spacing:y,size:R?[P,G]:[P-1,G-1]}}function te(e,t,n){const{cols:o,rows:i}=t,r=new Float32Array((o-1)*(i-1)*2*6),s=new Float32Array([-0,-1,1,-1,1,-0,1,-0,-0]),a=new Float32Array([-1,1,0,0,-1,1,1,0,0]);for(let l=0;l<o-1;l++){for(let t=0;t<i-1;t++){let n=l*i*2+2*t;const c=e[n],f=e[n+1],u=e[n+2],m=e[n+3];n+=2*i;const h=e[n],x=e[n+1],p=e[n+2],g=e[n+3];let y=0,d=12*(t*(o-1)+l);for(let e=0;e<3;e++)r[d++]=s[y++]*c+s[y++]*u+s[y++]*p;y=0;for(let e=0;e<3;e++)r[d++]=s[y++]*f+s[y++]*m+s[y++]*g;y=0;for(let e=0;e<3;e++)r[d++]=a[y++]*c+a[y++]*h+a[y++]*p;y=0;for(let e=0;e<3;e++)r[d++]=a[y++]*f+a[y++]*x+a[y++]*g}if(n)for(let e=0;e<r.length;e++)isNaN(r[e])&&(r[e]=-1)}return r}function ne(e){const t=e.clone().normalize();return 1===t.length?t[0]:q(t)}function oe(e,t,i){const{storageInfo:r,pixelSize:s}=t;let a=0,l=!1;const{pyramidResolutions:c}=r;if(n(c)&&c.length){const n=(e.x+e.y)/2,r=c[c.length-1],f=(r.x+r.y)/2,u=(s.x+s.y)/2;if(n<=u)a=0;else if(n>=f)a=c.length,l=n/f>8;else{let e,t=u;for(let o=1;o<=c.length;o++){if(e=(c[o-1].x+c[o-1].y)/2,n<=e){n===e?a=o:\"down\"===i?(a=o-1,l=n/t>8):a=\"up\"===i||n-t>e-n||n/t>2?o:o-1;break}t=e}}const m=0===a?s:c[a-1];if(l){Math.min(m.x,m.y)*o(t.spatialReference)>19567&&(l=!1)}return{pyramidLevel:a,pyramidResolution:new p({x:m.x,y:m.y,spatialReference:t.spatialReference}),excessiveReading:l}}const f=Math.log(e.x/s.x)/Math.LN2,u=Math.log(e.y/s.y)/Math.LN2,m=t.storageInfo.maximumPyramidLevel||0;a=\"down\"===i?Math.floor(Math.min(f,u)):\"up\"===i?Math.ceil(Math.max(f,u)):Math.round((f+u)/2),a<0?a=0:a>m&&(l=a>m+3,a=m);const h=2**a;return{pyramidLevel:a,pyramidResolution:new p({x:h*t.nativePixelSize.x,y:h*t.nativePixelSize.y,spatialReference:t.spatialReference}),excessiveReading:l}}function ie(e,t,i=512,r=!0){const{extent:s,spatialReference:a,pixelSize:l}=e,c=C(new p({x:l.x,y:l.y,spatialReference:a}),t,s);if(null==c)return{projectedPixelSize:null,scales:null,srcResolutions:null,isCustomTilingScheme:!1};const f=(c.x+c.y)/2,u=o(t),m=f*u*96*39.37,h=t.isGeographic?256/i*295828763.7958547:256/i*591657527.591555;let x=\"vector-magdir\"===e.dataType||\"vector-uv\"===e.dataType;const g=J(s,t),y=Math.min(Math.ceil(Math.log(Math.min(e.width,e.height)/32)/Math.LN2),Math.ceil(Math.log(h/2/m)/Math.LN2));if(!x&&r&&(t.isGeographic||t.isWebMercator)&&(x=g.xmin*g.xmax<0,!x&&y<3)){const e=U(t);if(n(e)){const t=2**y*f*i,n=Math.ceil(e/t);x=1===n||2===n&&e/2-g.xmax<t}}let d,M=m;const w=1.001,R=Math.min(2,Math.max(1.414,e.storageInfo?.pyramidScalingFactor||2));if(x){M=h;const e=t.isGeographic?1341104507446289e-21:.29858214164761665,n=e*(96*u*39.37),o=t.isGeographic?4326:3857;d=C(new p({x:e,y:e,spatialReference:{wkid:o}}),a,g),d.x*=M/n,d.y*=M/n}else{d={x:l.x,y:l.y};let e=0;for(;M<h*(w/2)&&e<y;)e++,M*=R,d.x*=R,d.y*=R;Math.max(M,h)/Math.min(M,h)<=w&&(M=h)}const P=[M],S=[{x:d.x,y:d.y}],b=70.5310735,G=Math.min(b,m)/w;for(;M>=G;)M/=R,d.x/=R,d.y/=R,P.push(M),S.push({x:d.x,y:d.y});return{projectedPixelSize:c,scales:P,srcResolutions:S,isCustomTilingScheme:!x}}export{ie as computeProjectedScales,S as defaultGridSpacing,G as defaultProjectionToleranceInPixels,v as getDefaultDatumTransformationForDataset,W as getProjectedGridPoints,$ as getProjectionOffsetGrid,V as getRasterDatasetAlignmentInfo,U as getWorldWidth,Q as getWorldWrapCount,T as load,b as minimumGridSpacing,J as projectExtent,j as projectPoint,C as projectResolution,M as requirePE,ne as snapExtent,oe as snapPyramid};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIokB,IAAI;AAAE,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,SAAM,CAAC,GAAE,GAAEA,IAAE,CAAC;AAAC;AAAC,SAASC,GAAED,IAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAEA,IAAE,GAAE,CAAC;AAAE,MAAG,KAAG,CAAC,GAAE,EAAE,OAAM,IAAI,EAAE,kCAAiC,iCAAiC;AAAE,SAAO;AAAC;AAAC,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,OAAK,CAAC,IAAE,QAAO,EAAE,EAAE,QAAM,CAAC,IAAE,SAAQ,EAAE,EAAE,QAAM,CAAC,IAAE,SAAQ,EAAE,EAAE,OAAK,CAAC,IAAE;AAAM,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAM,IAAE,CAAC,GAAEA,IAAE,GAAE,IAAE,MAAI;AAAC,MAAG,MAAI,EAAE,CAAC,EAAE,QAAM,CAAC,GAAE,CAAC;AAAE,MAAI,IAAE,GAAEE,KAAE,IAAGC,KAAE,GAAEC,KAAE;AAAG,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAG,EAAE,OAAM,EAAE,CAAC,CAAC,MAAI,IAAE,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAEF,KAAEA,KAAE,EAAE,CAAC,IAAEA,KAAE,EAAE,CAAC,GAAEC,KAAEA,KAAE,EAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC,IAAEA,IAAEC,KAAEA,KAAE,EAAE,IAAE,CAAC,IAAEA,KAAE,EAAE,IAAE,CAAC;AAAG,QAAK,EAAC,MAAK,GAAE,MAAK,EAAC,IAAEJ,IAAEK,MAAGH,KAAE,KAAG,IAAE,EAAE,CAAC,GAAE,KAAGE,KAAED,MAAG,IAAE,EAAE,CAAC,GAAE,IAAE,IAAE;AAAE,MAAI,IAAE,GAAE,IAAE,OAAG,IAAE,CAAC,GAAE,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAE,IAAE,GAAE,KAAI;AAAC,aAAQH,KAAE,GAAEA,KAAE,IAAE,GAAEA,MAAI;AAAC,YAAMM,KAAE,IAAE,IAAE,IAAE,IAAEN,IAAEO,MAAG,EAAED,EAAC,IAAE,EAAEA,KAAE,CAAC,IAAE,EAAEA,KAAE,IAAE,CAAC,IAAE,EAAEA,KAAE,IAAE,IAAE,CAAC,KAAG,GAAEE,MAAG,EAAEF,KAAE,CAAC,IAAE,EAAEA,KAAE,CAAC,IAAE,EAAEA,KAAE,IAAE,IAAE,CAAC,IAAE,EAAEA,KAAE,IAAE,IAAE,CAAC,KAAG,GAAEJ,KAAE,KAAK,KAAKK,KAAE,EAAED,KAAE,IAAE,IAAE,CAAC,KAAGD,EAAC,GAAEF,KAAE,KAAK,KAAKK,KAAE,EAAEF,KAAE,IAAE,IAAE,CAAC,KAAG,CAAC;AAAE,UAAGJ,KAAEC,KAAE,MAAI,IAAED,KAAEC,IAAE,IAAE,CAACD,IAAEC,EAAC,IAAG,KAAG,IAAE,GAAE;AAAC,YAAE;AAAG;AAAA,MAAK;AAAA,IAAC;AAAC,QAAG,EAAE;AAAA,EAAK;AAAC,SAAO;AAAC;AAAlhB,IAAohBM,KAAE,EAAC,MAAK,sBAAmB,MAAK,sBAAmB,MAAK,sBAAmB,MAAK,sBAAmB,MAAK,sBAAmB,MAAK,sBAAmB,MAAK,sBAAmB,OAAM,sBAAmB,OAAM,qBAAkB,OAAM,qBAAkB,OAAM,qBAAkB,OAAM,qBAAkB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,qBAAkB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,qBAAkB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,sBAAmB,OAAM,qBAAkB;AAArsC,IAAusC,IAAE;AAAzsC,IAA4sC,IAAE;AAA9sC,IAAgtC,IAAE;AAAltC,IAAotC,IAAE,oBAAI;AAA1tC,IAA8tCC,KAAE,oBAAI;AAApuC,IAAwuC,IAAE;AAAI,eAAe,IAAG;AAAC,KAAE,KAAG,MAAM,GAAE;AAAC;AAAC,SAASC,GAAE,GAAEX,IAAE,GAAE;AAAC,MAAG,CAACC,GAAE,EAAE,kBAAiBD,EAAC,EAAE,QAAO;AAAK,SAAO,IAAE,GAAEA,IAAE,EAAE,kBAAiB,CAAC,IAAE,GAAE,EAAE,kBAAiBA,IAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEE,IAAEC,KAAE,MAAK;AAAC,QAAMC,KAAE,EAAE;AAAiB,MAAGA,GAAE,OAAO,CAAC,EAAE,QAAO;AAAE,EAAAH,GAAEG,IAAE,GAAED,EAAC;AAAE,QAAM,IAAED,GAAE,QAAO,IAAE,IAAID,GAAE,EAAC,MAAK,EAAE,IAAE,EAAE,IAAE,GAAE,MAAK,EAAE,IAAE,EAAE,IAAE,GAAE,MAAK,EAAE,IAAE,EAAE,IAAE,GAAE,MAAK,EAAE,IAAE,EAAE,IAAE,GAAE,kBAAiBG,GAAC,CAAC,GAAEC,KAAE,GAAE,GAAE,GAAEF,EAAC,GAAE,IAAE,EAAE,CAAC;AAAE,MAAI;AAAE,MAAG,EAAEE,EAAC,KAAG,EAAE,CAAC,KAAGA,GAAE,SAAO,GAAE;AAAC,UAAML,KAAE,EAAEI,EAAC,IAAE,EAAE,CAAC;AAAE,QAAE,EAAC,GAAE,EAAE,IAAEJ,IAAE,GAAE,EAAE,IAAEA,GAAC;AAAA,EAAC,MAAM,KAAE,EAAC,GAAEK,GAAE,OAAM,GAAEA,GAAE,OAAM;AAAE,SAAO;AAAC;AAAC,SAAS,EAAE,GAAEL,KAAE,MAAI;AAAC,SAAO,EAAE,CAAC,IAAEA,KAAE,EAAE,CAAC,IAAE;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,IAAE,MAAK,IAAE,MAAG;AAAC,QAAM,IAAE,EAAE;AAAiB,MAAG,EAAE,OAAOA,EAAC,EAAE,QAAO;AAAE,EAAAC,GAAE,GAAED,IAAE,CAAC;AAAE,QAAME,KAAE,GAAE,GAAEF,IAAE,CAAC;AAAE,SAAO,KAAGE,MAAG,EAAE,CAAC,CAAC,GAAE,CAACA,EAAC,GAAE,GAAEF,EAAC,GAAEE,MAAGA;AAAC;AAAC,SAAS,EAAE,GAAEF,IAAE,GAAE,GAAE;AAAC,QAAME,KAAE,EAAE,GAAE,IAAE,GAAEC,KAAE,EAAE,GAAE,IAAE,GAAEC,KAAE,EAAE,GAAE,CAAC,GAAE,IAAE,EAAE,GAAE,CAAC;AAAE,MAAGA,MAAG,EAAEF,EAAC,KAAG,EAAEC,EAAC,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,UAAMI,KAAEP,GAAE,CAAC;AAAE,QAAG,CAACO,GAAE;AAAS,UAAK,EAAC,GAAEC,GAAC,IAAE,EAAE,CAAC,GAAE,EAAC,GAAE,EAAC,IAAED;AAAE,SAAGJ,GAAE,CAAC,IAAE,KAAG,KAAK,IAAIK,KAAEN,GAAE,CAAC,CAAC,IAAEE,KAAEG,GAAE,KAAGJ,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,IAAE,KAAG,KAAK,IAAIK,KAAEN,GAAE,CAAC,CAAC,IAAEE,OAAIG,GAAE,KAAGJ,GAAE,CAAC,IAAEA,GAAE,CAAC;AAAA,EAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,MAAKH,IAAE,OAAM,GAAE,qBAAoB,GAAE,UAASE,GAAC,IAAE;AAAE,MAAGF,GAAE,OAAO,CAAC,GAAE;AAAC,UAAK,EAAC,QAAOA,GAAC,IAAE,EAAE,GAAE,IAAI;AAAE,WAAOA;AAAA,EAAC;AAAC,MAAGA,GAAE,iBAAe,EAAE,WAASA,GAAE,WAAS,EAAE,cAAc,QAAO,EAAE,CAAC;AAAE,MAAGC,GAAED,IAAE,GAAE,CAAC,KAAGE,IAAE;AAAC,QAAGF,GAAE,aAAa,QAAOY,GAAE,CAAC;AAAE,UAAML,KAAE,EAAEP,EAAC;AAAE,QAAG,EAAEO,EAAC,EAAE,QAAOK,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,QAAOZ,GAAC,IAAE,EAAE,GAAE,IAAI,GAAE,EAAC,MAAK,GAAE,OAAM,GAAE,qBAAoB,EAAC,IAAE,GAAEE,KAAEF,GAAE,IAAK,CAAAa,OAAG,IAAI,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,CAAC,CAAE,GAAEV,KAAE,GAAED,IAAE,GAAE,CAAC;AAAE,SAAO,KAAG,EAAEA,IAAEC,IAAE,GAAE,CAAC,GAAEA,GAAE,IAAK,CAAAU,OAAGA,KAAE,CAACA,GAAE,GAAEA,GAAE,CAAC,IAAE,CAAC,KAAI,GAAG,CAAE;AAAC;AAAC,SAASD,GAAE,GAAE;AAAC,QAAK,EAAC,MAAKZ,IAAE,OAAM,GAAE,qBAAoB,EAAC,IAAE,GAAE,IAAE,EAAEA,EAAC,GAAE,EAAC,QAAOK,IAAE,MAAK,EAAC,IAAE,EAAE,GAAE,CAAC;AAAE,MAAG,CAACL,GAAE,cAAa;AAAC,UAAMa,KAAEb,GAAE,OAAK,EAAE,SAASA,GAAE,IAAI,IAAE,EAAE,WAAWA,GAAE,eAAaG,GAAE,iBAAeA,GAAE,gBAAeH,GAAE,GAAG;AAAE,MAAE,WAAWa,IAAER,GAAE,QAAOA,EAAC;AAAA,EAAC;AAAC,MAAG,EAAE,CAAC,KAAG,EAAE,MAAM,QAAO;AAAC,QAAIQ;AAAE,UAAMb,KAAE;AAAS,QAAG,EAAE,iBAAea,KAAER,GAAE,IAAK,CAAC,CAACQ,EAAC,MAAIA,KAAEb,KAAE,IAAEa,KAAE,CAACb,KAAE,KAAG,CAAE,IAAG,EAAE,MAAM,QAAS,CAAAa,OAAG;AAAC,YAAMb,KAAEa,GAAE,OAAK,EAAE,SAASA,GAAE,IAAI,IAAE,EAAE,WAAWV,GAAE,kBAAiBU,GAAE,GAAG;AAAE,QAAE,WAAWb,IAAEK,GAAE,QAAOA,IAAE,MAAKQ,GAAE,YAAUV,GAAE,sBAAoBA,GAAE,mBAAmB;AAAA,IAAC,CAAE,GAAEU,GAAE,UAAQ,IAAE,GAAE,IAAER,GAAE,QAAO,KAAI;AAAC,YAAME,KAAEM,GAAE,CAAC,GAAE,IAAER,GAAE,CAAC,EAAE,CAAC,GAAEH,KAAE,IAAEF,KAAE,IAAE,IAAE,CAACA,KAAE,KAAG;AAAE,MAAAO,MAAGL,MAAGK,OAAIL,OAAIG,GAAE,CAAC,EAAE,CAAC,IAAEE,KAAE,IAAE,IAAE,MAAI,IAAE;AAAA,IAAI;AAAA,EAAC;AAAC,MAAG,CAAC,EAAE,cAAa;AAAC,UAAMM,KAAE,EAAE,GAAE,IAAE,GAAEb,KAAE,EAAEa,EAAC,KAAGA,GAAE,aAAW,CAACA,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,CAAC,IAAE,CAAC,KAAI,EAAE;AAAE,MAAER,IAAEL,EAAC;AAAE,UAAMI,KAAE,EAAE,OAAK,EAAE,SAAS,EAAE,IAAI,IAAE,EAAE,WAAW,EAAE,eAAaD,GAAE,iBAAeA,GAAE,gBAAe,EAAE,GAAG;AAAE,MAAE,WAAWC,IAAEC,GAAE,QAAOA,EAAC;AAAA,EAAC;AAAC,MAAI,IAAEA;AAAE,MAAG,KAAGA,GAAE,WAAS,EAAE,QAAO;AAAC,QAAE,CAAC;AAAE,aAAQQ,KAAE,GAAEb,KAAE,GAAEa,KAAE,EAAE,QAAOA,KAAI,GAAEA,EAAC,IAAE,EAAE,KAAKR,GAAEL,IAAG,CAAC,IAAE,EAAE,KAAK,CAAC,KAAI,GAAG,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,MAAKA,IAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,gBAAeE,IAAE,MAAKC,IAAE,OAAMC,GAAC,IAAE;AAAE,MAAG,EAAC,MAAK,GAAE,MAAK,EAAC,IAAE;AAAE,EAAAF,OAAI,KAAG,IAAE,GAAE,KAAG,IAAE;AAAG,QAAMG,KAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,KAAK,IAAIL,IAAE,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,UAAMa,KAAE,IAAE,IAAE,KAAK,IAAIb,IAAE,CAAC,GAAEE,KAAE,IAAE,IAAE,KAAK,IAAI,GAAE,CAAC,GAAEY,KAAE,GAAE,IAAI,EAAE,EAAC,GAAED,IAAE,GAAEX,IAAE,kBAAiBC,GAAC,CAAC,GAAEC,EAAC;AAAE,SAAGJ,MAAGK,GAAE,KAAKS,GAAE,CAAC,GAAE,KAAG,KAAG,EAAE,KAAKA,GAAE,CAAC;AAAA,EAAC;AAAC,QAAM,IAAE,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAEd,IAAE,IAAI,UAAQa,KAAE,GAAEA,KAAE,GAAEA,KAAI,GAAE,KAAK,CAACR,GAAE,CAAC,GAAE,EAAEQ,EAAC,CAAC,CAAC;AAAE,SAAO;AAAC;AAAC,SAAS,EAAE,GAAEb,KAAE,OAAG;AAAC,MAAI,IAAE,EAAE,QAAM,EAAE;AAAI,MAAG,CAAC,KAAG,EAAE,aAAa,QAAO;AAAK,MAAG,IAAE,OAAO,CAAC,GAAE,EAAE,IAAI,CAAC,GAAE;AAAC,UAAMa,KAAE,EAAE,IAAI,CAAC;AAAE,WAAOb,KAAEa,MAAA,gBAAAA,GAAG,MAAIA,MAAA,gBAAAA,GAAG;AAAA,EAAG;AAAC,QAAM,IAAE,EAAE,OAAK,EAAE,SAAS,EAAE,IAAI,IAAE,EAAE,WAAW,EAAE,eAAaV,GAAE,iBAAeA,GAAE,gBAAe,EAAE,GAAG,GAAEA,KAAE,EAAE,GAAE,EAAE,GAAE,IAAI,CAAC,GAAEC,KAAE,EAAE,GAAE,GAAE,IAAE;AAAE,SAAO,EAAE,IAAI,GAAE,EAAC,KAAID,IAAE,KAAIC,GAAC,CAAC,GAAEJ,KAAEI,KAAED;AAAC;AAAC,SAAS,EAAE,GAAEH,KAAE,GAAE,IAAE,OAAG;AAAC,QAAM,IAAE,EAAE,SAAS,CAAC,GAAE,IAAE,IAAE,EAAE,mBAAmB,IAAE,EAAE,mBAAmB;AAAE,MAAG,CAAC,KAAG,EAAC,uBAAG,QAAO,QAAO;AAAK,MAAIE,KAAE,OAAGC,KAAE,EAAE,KAAM,CAAAU,OAAG,MAAIA,GAAE,aAAa,KAAG,MAAIA,GAAE,QAAQ,CAAE;AAAE,MAAG,CAACV,IAAE;AAAC,QAAGA,KAAE,EAAE,KAAM,CAAAU,OAAG,MAAIA,GAAE,aAAa,KAAG,MAAIA,GAAE,QAAQ,CAAE,GAAE,CAACV,GAAE,QAAO;AAAK,IAAAD,KAAE;AAAA,EAAE;AAAC,QAAME,KAAE,IAAE,KAAG,MAAI,EAAE,qBAAqB,IAAE,IAAE,MAAI,MAAI,EAAE,qBAAqB,IAAE,IAAE,IAAG,IAAE,EAAE,oBAAoB,GAAEC,KAAEF,GAAE,SAAS;AAAE,MAAGD,GAAE,QAAM,EAAC,YAAWA,IAAE,YAAW,GAAE,UAASG,IAAE,MAAK,MAAK,MAAK,CAACA,GAAE,CAAC,EAAE,CAAC,IAAEL,IAAEK,GAAE,CAAC,EAAE,CAAC,IAAEL,IAAEK,GAAE,CAAC,EAAE,CAAC,IAAEL,IAAEK,GAAE,CAAC,EAAE,CAAC,IAAEL,EAAC,GAAE,cAAaI,GAAC;AAAE,MAAI,IAAE;AAAE,QAAM,IAAE,CAAC;AAAE,MAAG,CAAC,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAEA,GAAE,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAEA,GAAE,QAAO,IAAE,GAAE,KAAI;AAAC,SAAI,MAAI,MAAI,IAAE;AAAG,UAAK,CAACQ,IAAEb,EAAC,IAAEK,GAAE,CAAC,GAAE,CAACC,IAAEC,EAAC,IAAEF,GAAE,CAAC;AAAE,QAAGE,OAAIP,GAAE,GAAE,KAAK,CAACa,IAAEP,IAAEN,IAAEO,IAAE,CAAC,CAAC;AAAA,SAAM;AAAC,YAAMC,MAAGF,KAAEO,OAAIN,KAAEP,MAAG,OAAME,KAAEW,KAAEL,KAAER;AAAE,MAAAA,KAAEO,KAAE,EAAE,KAAK,CAACC,IAAEN,IAAEF,IAAEO,IAAE,CAAC,CAAC,IAAE,EAAE,KAAK,CAACC,IAAEN,IAAEK,IAAEP,IAAE,CAAC,CAAC;AAAA,IAAC;AAAC,QAAE,IAAEa,KAAE,IAAEA,IAAE,IAAE,IAAEb,KAAE,IAAEA,IAAE,IAAE,IAAEa,KAAE,IAAEA,IAAE,IAAE,IAAEb,KAAE,IAAEA;AAAA,EAAC;AAAC,SAAM,EAAC,YAAW,OAAG,YAAW,GAAE,UAASK,IAAE,MAAK,GAAE,MAAK,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,cAAaD,GAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAM,IAAE,CAAC,GAAE,EAAC,MAAK,GAAE,MAAKF,IAAE,MAAKC,IAAE,MAAKC,IAAE,gBAAe,EAAC,IAAE;AAAE,MAAG,EAAC,MAAK,GAAE,MAAKC,GAAC,IAAE;AAAE,MAAG,MAAI,KAAGF,KAAE,GAAEE,MAAGD,KAAE,IAAG,EAAE,CAAC,GAAE;AAAC,aAAQS,KAAE,GAAEA,KAAE,GAAEA,KAAI,UAAQb,KAAE,GAAEA,KAAEE,IAAEF,KAAI,GAAE,KAAK,CAAC,IAAEG,KAAEU,IAAER,KAAED,KAAEJ,EAAC,CAAC;AAAE,WAAM,EAAC,QAAO,EAAC;AAAA,EAAC;AAAC,QAAM,IAAE,IAAI,WAAW,IAAEE,EAAC;AAAE,MAAG,EAAE,YAAW;AAAC,UAAK,EAAC,MAAK,CAACW,IAAEb,IAAEe,IAAED,EAAC,EAAC,IAAE;AAAE,aAAQE,KAAE,GAAE,IAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMR,KAAE,IAAEL,KAAEa,IAAE,IAAE,EAAE,cAAYR,MAAGK,MAAGL,MAAGO;AAAE,eAAQF,KAAE,GAAEA,KAAEX,IAAEW,MAAI,KAAI;AAAC,cAAMP,KAAED,KAAED,KAAES;AAAE,aAAGP,MAAGN,MAAGM,MAAGQ,OAAI,EAAE,KAAK,CAACN,IAAEF,EAAC,CAAC,GAAE,EAAE,CAAC,IAAE;AAAA,MAAE;AAAA,IAAC;AAAC,WAAM,EAAC,QAAO,GAAE,MAAK,EAAC;AAAA,EAAC;AAAC,QAAM,IAAE,EAAE,MAAK,IAAE,CAAC;AAAE,WAAQN,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,UAAMa,KAAER,KAAED,KAAEJ,IAAEM,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,aAAQP,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,YAAK,CAACQ,IAAEN,IAAEC,IAAEC,IAAEW,EAAC,IAAE,EAAEf,EAAC;AAAE,UAAGa,OAAIV,MAAGA,OAAIC,GAAE,CAAAE,GAAE,KAAKE,EAAC,GAAEF,GAAE,KAAKJ,EAAC,GAAEK,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAA,eAAUM,MAAGV,MAAGU,MAAGT,IAAE;AAAC,cAAMJ,KAAEQ,KAAEK,KAAEX;AAAE,QAAAI,GAAE,KAAKN,EAAC,GAAEO,GAAE,KAAKQ,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAIP,KAAEF;AAAE,QAAGA,GAAE,SAAO,GAAE;AAAC,UAAIO,KAAE,MAAIN,GAAE,CAAC,IAAE,IAAEA,GAAE,CAAC,GAAEP,KAAEM,GAAE,CAAC;AAAE,MAAAE,KAAE,CAAC;AAAE,eAAQN,KAAE,GAAEA,KAAEK,GAAE,QAAOL,KAAI,OAAIK,GAAEL,EAAC,KAAGA,OAAIK,GAAE,SAAO,MAAIA,GAAEL,EAAC,MAAIW,OAAIL,GAAE,KAAK,MAAIK,KAAE,KAAK,IAAIb,IAAEM,GAAEJ,KAAE,CAAC,CAAC,IAAE,KAAK,IAAIF,IAAEM,GAAEJ,KAAE,CAAC,CAAC,CAAC,GAAEW,KAAEN,GAAEL,EAAC,GAAEF,KAAEM,GAAEJ,EAAC,IAAGA,OAAIK,GAAE,SAAO,KAAGC,GAAE,KAAK,MAAID,GAAEL,EAAC,IAAE,KAAK,IAAIF,IAAEM,GAAEJ,EAAC,CAAC,IAAE,KAAK,IAAIF,IAAEM,GAAEJ,EAAC,CAAC,CAAC;AAAG,MAAAM,GAAE,KAAM,CAACK,IAAEb,OAAIa,KAAEb,EAAE;AAAA,IAAC,MAAM,CAAAM,GAAE,CAAC,IAAEA,GAAE,CAAC,MAAIE,KAAE,CAACF,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAG,MAAE,KAAKE,EAAC;AAAA,EAAC;AAAC,WAAQR,KAAE,GAAE,IAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAMa,KAAE,IAAEV,KAAEH;AAAE,aAAQA,KAAE,GAAEA,KAAEE,IAAEF,MAAI,KAAI;AAAC,YAAMM,KAAED,KAAED,KAAEJ,IAAEQ,KAAE,EAAER,EAAC;AAAE,UAAG,MAAIQ,GAAE,OAAO,CAAAK,MAAGL,GAAE,CAAC,KAAGK,MAAGL,GAAE,CAAC,MAAI,EAAE,KAAK,CAACK,IAAEP,EAAC,CAAC,GAAE,EAAE,CAAC,IAAE;AAAA,eAAWE,GAAE,SAAO,GAAE;AAAC,YAAIR,KAAE;AAAG,iBAAQM,KAAE,GAAEA,KAAEE,GAAE,QAAOF,MAAG,EAAE,KAAGO,MAAGL,GAAEF,EAAC,KAAGO,MAAGL,GAAEF,KAAE,CAAC,GAAE;AAAC,UAAAN,KAAE;AAAG;AAAA,QAAK;AAAC,QAAAA,OAAI,EAAE,KAAK,CAACa,IAAEP,EAAC,CAAC,GAAE,EAAE,CAAC,IAAE;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,QAAO,GAAE,MAAK,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEN,IAAE;AAAC,QAAK,CAAC,GAAE,CAAC,IAAEA;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,UAAMA,KAAE,EAAE,CAAC,EAAE,CAAC;AAAE,KAACA,KAAE,KAAGA,KAAE,OAAK,EAAE,CAAC,IAAE,CAAC,KAAI,GAAG;AAAA,EAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,EAAE,CAAC,EAAE,gBAAgB;AAAE,MAAG,EAAE,SAAO,KAAG,EAAE,CAAC,EAAE,QAAO,EAAE,CAAC;AAAE,MAAG,EAAC,MAAK,GAAE,MAAK,GAAE,MAAKE,IAAE,MAAKC,GAAC,IAAE,EAAE,CAAC;AAAE,WAAQH,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMO,KAAE,EAAEP,EAAC;AAAE,QAAEO,GAAE,OAAK,IAAEP,IAAEE,KAAE,KAAK,IAAIA,IAAEK,GAAE,IAAI,GAAEJ,KAAE,KAAK,IAAIA,IAAEI,GAAE,IAAI;AAAA,EAAC;AAAC,SAAO,IAAIN,GAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAKC,IAAE,MAAKC,IAAE,kBAAiB,EAAE,CAAC,EAAE,iBAAgB,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,IAAE,MAAKD,KAAE,MAAG;AAAC,QAAMC,KAAE,EAAE;AAAiB,MAAGA,GAAE,OAAO,CAAC,EAAE,QAAO;AAAE,QAAMC,KAAE,EAAE,CAAC,GAAE,IAAE,EAAED,IAAE,IAAE,GAAE,IAAE,EAAE,CAAC;AAAE,MAAG,MAAIC,MAAG,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE;AAAC,UAAMA,KAAE,EAAE,GAAE,GAAE,GAAEF,EAAC;AAAE,QAAG,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,KAAK,IAAIE,GAAE,QAAM,CAAC,IAAE,EAAE,CAAC,KAAG,GAAE,GAAE;AAAC,YAAMJ,KAAE,EAAEG,EAAC;AAAE,UAAG,EAAEH,EAAC,KAAGA,GAAE,iBAAe,EAAE,QAAM,EAAE,SAAOA,GAAE,KAAK,CAAC,IAAEA,GAAE,KAAK,CAAC,KAAG,EAAE,QAAO,EAAE,GAAE,CAAC,KAAGI;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAC,QAAM,IAAE,EAAE,MAAM,EAAE,UAAU;AAAE,MAAG,MAAI,EAAE,UAAQ,EAAE,OAAK,KAAG,EAAE,OAAK,IAAE,IAAE,EAAED,EAAC,GAAE;AAAC,UAAK,EAAC,MAAKH,IAAE,MAAK,EAAC,IAAE;AAAE,aAAQO,KAAE,GAAEA,MAAGH,IAAEG,MAAI;AAAC,YAAMC,KAAE,MAAID,KAAEP,KAAE,CAAC,IAAE,GAAEE,KAAEK,OAAIH,KAAE,IAAE,IAAEG,KAAE,IAAE;AAAE,QAAEA,EAAC,IAAE,IAAIN,GAAE,EAAC,MAAKO,IAAE,MAAKN,IAAE,MAAK,EAAE,MAAK,MAAK,EAAE,MAAK,kBAAiBC,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,EAAE,EAAE,IAAK,CAAAU,OAAG,EAAEA,IAAE,GAAE,GAAEX,EAAC,CAAE,EAAE,OAAO,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAE,CAAC;AAAE,MAAG,EAAE,CAAC,EAAE,QAAO;AAAK,MAAG,EAAC,MAAK,GAAE,MAAKA,IAAE,MAAKC,IAAE,MAAKC,GAAC,IAAE;AAAE,QAAM,IAAE,EAAE,kBAAiB,IAAE,IAAI,EAAE,EAAC,kBAAiB,GAAE,OAAM,CAAC,CAAC,CAAC,GAAEF,EAAC,GAAE,CAACC,IAAED,EAAC,GAAE,CAACC,IAAEC,EAAC,GAAE,CAAC,GAAEA,EAAC,GAAE,CAAC,GAAEF,EAAC,CAAC,CAAC,EAAC,CAAC,GAAEG,KAAE,GAAE,GAAE,CAAC;AAAE,MAAG,MAAIA,GAAE,MAAM,UAAQ,CAACA,GAAE,MAAM,CAAC,EAAE,UAAQ,CAACA,GAAE,MAAM,CAAC,EAAE,OAAO,QAAO;AAAK,QAAK,EAAC,OAAM,EAAC,IAAEA,IAAE,IAAE,EAAE,CAAC,GAAE,IAAE,IAAIJ,GAAE,EAAC,kBAAiB,EAAC,CAAC;AAAE,WAAQD,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,QAAEG,KAAE,EAAEH,EAAC,EAAE,CAAC,EAAE,CAAC,GAAEE,KAAEE,KAAE,EAAEJ,EAAC,EAAE,CAAC,EAAE,CAAC;AAAE,aAAQa,KAAE,GAAEA,KAAE,EAAEb,EAAC,EAAE,QAAOa,KAAI,KAAE,IAAE,EAAEb,EAAC,EAAEa,EAAC,EAAE,CAAC,IAAE,EAAEb,EAAC,EAAEa,EAAC,EAAE,CAAC,IAAE,GAAEV,KAAEA,KAAE,EAAEH,EAAC,EAAEa,EAAC,EAAE,CAAC,IAAE,EAAEb,EAAC,EAAEa,EAAC,EAAE,CAAC,IAAEV,IAAED,KAAEA,KAAE,EAAEF,EAAC,EAAEa,EAAC,EAAE,CAAC,IAAE,EAAEb,EAAC,EAAEa,EAAC,EAAE,CAAC,IAAEX,IAAEE,KAAEA,KAAE,EAAEJ,EAAC,EAAEa,EAAC,EAAE,CAAC,IAAE,EAAEb,EAAC,EAAEa,EAAC,EAAE,CAAC,IAAET;AAAE,QAAG,MAAIJ,GAAE,GAAE,OAAKE,IAAE,EAAE,OAAKE,IAAE,EAAE,OAAK,GAAE,EAAE,OAAKD;AAAA,aAAU,EAAE,OAAK,KAAK,IAAI,EAAE,MAAKD,EAAC,GAAE,EAAE,OAAK,KAAK,IAAI,EAAE,MAAKE,EAAC,GAAE,KAAK,IAAID,KAAE,IAAE,CAAC,IAAE,EAAE,GAAE,OAAK,GAAE,EAAE,OAAK,EAAE,OAAK;AAAA,SAAM;AAAC,UAAG,EAAE,KAAK,IAAI,IAAE,IAAE,CAAC,IAAE,GAAG,QAAO;AAAK,QAAE,OAAKA,KAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,IAAE,MAAK,IAAE,MAAGD,KAAE,MAAG;AAAC,QAAMC,KAAE,EAAE;AAAiB,MAAGA,GAAE,OAAO,CAAC,KAAG,CAAC,EAAE,QAAO;AAAE,EAAAF,GAAEE,IAAE,GAAE,CAAC;AAAE,QAAMC,KAAE,GAAE,GAAE,GAAE,CAAC;AAAE,MAAGF,MAAG,EAAE,iBAAeE,OAAIA,GAAE,OAAK,KAAK,IAAI,mBAAgBA,GAAE,IAAI,GAAEA,GAAE,OAAK,KAAK,IAAI,oBAAiBA,GAAE,IAAI,GAAEA,GAAE,QAAMA,GAAE,MAAM,QAAO;AAAK,MAAG,CAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,QAAM,IAAE,EAAED,IAAE,IAAE,GAAE,IAAE,EAAE,GAAE,IAAE;AAAE,MAAG,EAAE,CAAC,KAAG,EAAE,CAAC,EAAE,QAAOC;AAAE,QAAMC,KAAE,EAAEF,IAAE,IAAI,GAAE,IAAE,EAAEA,IAAE,CAAC,GAAE,IAAE,EAAE,GAAE,IAAI;AAAE,MAAG,KAAK,IAAIC,GAAE,OAAK,EAAE,CAAC,CAAC,IAAE,KAAG,KAAK,IAAIA,GAAE,OAAK,EAAE,CAAC,CAAC,IAAE,GAAE;AAAC,UAAMJ,KAAE,KAAK,IAAI,EAAE,OAAK,EAAE,CAAC,CAAC,GAAEQ,KAAE,KAAK,IAAI,EAAE,CAAC,IAAE,EAAE,IAAI;AAAE,QAAGR,KAAEK,MAAGG,KAAE,GAAE;AAAC,MAAAJ,GAAE,OAAK,EAAE,CAAC;AAAE,YAAMJ,KAAE,CAAC;AAAE,MAAAA,GAAE,KAAK,IAAI,EAAE,EAAE,MAAK,EAAE,MAAKG,EAAC,CAAC,GAAEH,GAAE,KAAK,IAAI,EAAE,EAAE,OAAM,EAAE,OAAK,EAAE,QAAM,GAAEG,EAAC,CAAC,GAAEH,GAAE,KAAK,IAAI,EAAE,EAAE,MAAK,EAAE,MAAKG,EAAC,CAAC;AAAE,YAAMK,KAAER,GAAE,IAAK,CAAAa,OAAG,EAAEA,IAAE,GAAE,CAAC,CAAE,EAAE,OAAQ,CAAAA,OAAG,CAAC,MAAMA,MAAA,gBAAAA,GAAG,CAAC,CAAE,EAAE,IAAK,CAAAA,OAAGA,GAAE,CAAE;AAAE,MAAAT,GAAE,OAAK,KAAK,IAAI,MAAM,MAAKI,EAAC;AAAA,IAAC;AAAC,QAAGA,KAAEH,MAAGL,KAAE,GAAE;AAAC,MAAAI,GAAE,OAAK,EAAE,CAAC;AAAE,YAAMJ,KAAE,CAAC;AAAE,MAAAA,GAAE,KAAK,IAAI,EAAE,EAAE,MAAK,EAAE,MAAKG,EAAC,CAAC,GAAEH,GAAE,KAAK,IAAI,EAAE,EAAE,OAAM,EAAE,OAAK,EAAE,QAAM,GAAEG,EAAC,CAAC,GAAEH,GAAE,KAAK,IAAI,EAAE,EAAE,MAAK,EAAE,MAAKG,EAAC,CAAC;AAAE,YAAMK,KAAER,GAAE,IAAK,CAAAa,OAAG,EAAEA,IAAE,GAAE,CAAC,CAAE,EAAE,OAAQ,CAAAA,OAAG,CAAC,MAAMA,MAAA,gBAAAA,GAAG,CAAC,CAAE,EAAE,IAAK,CAAAA,OAAGA,GAAE,CAAE;AAAE,MAAAT,GAAE,OAAK,KAAK,IAAI,MAAM,MAAKI,EAAC;AAAA,IAAC;AAAA,EAAC,OAAK;AAAC,UAAMK,KAAE,EAAE,GAAE,IAAI;AAAE,SAAK,IAAIT,GAAE,OAAK,EAAE,CAAC,CAAC,IAAES,OAAIT,GAAE,OAAK,EAAE,CAAC,IAAG,KAAK,IAAIA,GAAE,OAAK,EAAE,CAAC,CAAC,IAAES,OAAIT,GAAE,OAAK,EAAE,CAAC;AAAA,EAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,EAAE,GAAEJ,KAAE,OAAG;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,QAAM,IAAEA,KAAE,oBAAgB;AAAmB,SAAO,EAAE,gBAAc,IAAE,IAAE,EAAE,QAAM,EAAE,eAAa,MAAI,IAAES,GAAE,EAAE,IAAI,KAAG;AAAI;AAAC,SAAS,EAAE,GAAET,KAAE,OAAG;AAAC,MAAG,EAAE,aAAa,QAAM,CAAC,MAAK,GAAG;AAAE,QAAM,IAAE,EAAE,GAAEA,EAAC;AAAE,SAAO,EAAE,CAAC,IAAE,CAAC,CAAC,IAAE,GAAE,IAAE,CAAC,IAAE;AAAI;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE,GAAE;AAAC,MAAI,KAAG,IAAEA,MAAG;AAAE,SAAO,IAAE,KAAK,MAAM,CAAC,KAAG,IAAE,IAAE,KAAK,MAAM,CAAC,IAAE,MAAI,KAAG,IAAG;AAAC;AAAC,SAAS,EAAE,GAAE,IAAE,OAAG;AAAC,QAAM,IAAE,EAAE,EAAE,gBAAgB;AAAE,MAAG,EAAE,CAAC,EAAE,QAAO;AAAE,QAAM,IAAE,IAAE,IAAE,EAAE,IAAE,IAAGE,KAAE,EAAE,EAAE,gBAAgB,GAAEC,KAAE,CAAC,KAAG,KAAK,IAAI,EAAE,OAAK,IAAE,CAAC,IAAED,KAAE,IAAE,IAAE,EAAE,MAAKE,KAAE,CAAC,KAAG,KAAK,IAAI,EAAE,OAAK,IAAE,CAAC,IAAEF,KAAE,CAAC,IAAE,IAAE,EAAE;AAAK,SAAO,EAAEC,IAAE,GAAE,GAAE,IAAE,IAAE,EAAEC,IAAE,GAAE,GAAE,KAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,YAAY,OAAO,GAAE,IAAE,EAAE,EAAE,kBAAiB,IAAE;AAAE,MAAG,EAAE,CAAC,EAAE,QAAM,EAAC,SAAQ,GAAE,gBAAe,MAAK,cAAa,KAAI;AAAE,QAAMF,KAAE,IAAE,GAAE,EAAC,iBAAgBC,IAAE,aAAYC,IAAE,QAAO,EAAC,IAAE,GAAE,EAAC,qBAAoB,GAAE,YAAWC,IAAE,sBAAqB,EAAC,IAAED;AAAE,MAAI,IAAED,GAAE;AAAE,QAAM,IAAE,CAAC,GAAE,IAAE,EAAE,EAAE,SAAS,KAAG,gBAAc,EAAE,UAAU,MAAK,IAAE,KAAG,IAAE,IAAED,KAAG,IAAE,IAAE,IAAE,IAAEA,KAAE;AAAE,WAAQF,KAAE,GAAEA,MAAG,GAAEA,MAAI;AAAC,UAAMa,MAAG,EAAE,OAAK,KAAG,IAAER,IAAEL,KAAEa,KAAE,KAAK,MAAMA,EAAC,KAAG,IAAEA,KAAE,KAAK,KAAKA,EAAC,GAAE,IAAE,IAAE,IAAER,IAAEG,KAAE,IAAE,KAAK,MAAM,CAAC,KAAG,IAAE,IAAE,KAAK,KAAK,CAAC,GAAEN,KAAE,KAAK,MAAM,IAAE,IAAEG,EAAC,GAAEF,KAAE,KAAK,MAAM,IAAE,CAAC,IAAEE,IAAED,MAAGC,KAAE,KAAK,MAAM,IAAE,CAAC,IAAEA,MAAGA;AAAE,MAAE,KAAK,EAAC,aAAY,GAAE,YAAWA,IAAE,mBAAkBL,IAAE,4BAA2BQ,IAAE,YAAWL,IAAE,cAAaC,IAAE,oBAAmBF,GAAC,CAAC,GAAE,KAAG;AAAA,EAAC;AAAC,SAAM,EAAC,SAAQ,GAAE,gBAAeA,IAAE,cAAa,GAAE,uBAAsB,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG,CAAC,KAAG,EAAE,aAAa,QAAO;AAAE,QAAMF,KAAE,OAAO,EAAE,QAAM,EAAE,GAAG;AAAE,MAAI;AAAE,MAAGU,GAAE,IAAIV,EAAC,EAAE,KAAEU,GAAE,IAAIV,EAAC;AAAA,OAAM;AAAC,SAAG,EAAE,OAAK,EAAE,SAAS,EAAE,IAAI,IAAE,EAAE,WAAWG,GAAE,gBAAe,EAAE,GAAG,GAAG,UAAU,EAAE,QAAQ,GAAEO,GAAE,IAAIV,IAAE,CAAC;AAAA,EAAC;AAAC,SAAO,IAAI,EAAE,EAAC,MAAK,EAAC,CAAC;AAAC;AAAC,SAASiB,GAAE,GAAE;AAAC,QAAMjB,KAAE,EAAE,cAAY,QAAM,EAAE;AAAQ,MAAI,IAAE,EAAE,WAAS,CAAC,GAAE,CAAC,GAAE,IAAE,GAAG,CAAC,GAAEE,KAAE,EAAC,MAAK,EAAE,KAAK,CAAC,IAAE,GAAE,MAAK,EAAE,KAAK,CAAC,IAAE,EAAC;AAAE,QAAMC,KAAE,EAAE,uBAAqB,KAAG,EAAE,uBAAqB,EAAE,QAAQ,SAAO;AAAE,MAAIC,KAAE,EAAE,yBAAuB,EAAE,QAAQ,SAAO,KAAGJ,MAAGG,KAAE,CAAC,GAAE,CAAC,IAAE,EAAE,EAAE,SAAQD,IAAE,GAAE,CAAC;AAAE,QAAM,KAAGE,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAG,GAAE,IAAE,EAAE,gBAAgB,kBAAiBC,KAAE,EAAE,gBAAgB;AAAiB,MAAGL,OAAIG,MAAG,IAAE,IAAG;AAAC,MAAE,GAAEE,IAAE,EAAE,mBAAmB,MAAI,EAAE,gBAAc,EAAE,EAAE,CAAC,CAAC,IAAG,IAAE,CAAC,GAAE,CAAC,GAAE,IAAE,GAAG,EAAC,GAAG,GAAE,SAAQ,EAAC,CAAC,GAAEH,KAAE,EAAC,MAAK,EAAE,KAAK,CAAC,IAAE,GAAE,MAAK,EAAE,KAAK,CAAC,IAAE,EAAC,GAAEE,KAAE,EAAE,EAAE,SAAQF,IAAE,GAAE,CAAC;AAAA,EAAC;AAAC,MAAG,EAAE,QAAME,IAAE,EAAE,CAAC,IAAE,MAAI,EAAE,eAAa,GAAG,EAAE,SAAQF,IAAEC,EAAC,IAAG,EAAE,kBAAgB,CAAC,EAAE,gBAAc,CAAC,EAAE,cAAc,KAAGE,GAAE,aAAa,GAAE,UAAQ,EAAC,SAAQ,EAAE,SAAQ,cAAa,EAAE,cAAa,SAAQ,EAAC;AAAA,OAAM;AAAC,UAAML,KAAE,EAAE,CAAC;AAAE,QAAG,EAAEA,EAAC,KAAG,CAACA,GAAE,YAAW;AAAC,YAAMA,KAAE,EAAE,CAAC,GAAE,IAAE,EAAE,EAAE,iBAAgBA,EAAC,GAAE,EAAC,SAAQI,GAAC,IAAE,GAAG,EAAC,GAAG,GAAE,iBAAgB,GAAE,SAAQ,EAAC,CAAC,GAAEW,KAAE,GAAGX,IAAEF,IAAEC,EAAC;AAAE,QAAE,UAAQ,EAAC,SAAQC,IAAE,cAAaW,IAAE,SAAQ,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,QAAK,EAAC,iBAAgBf,IAAE,iBAAgB,GAAE,WAAU,GAAE,qBAAoBE,IAAE,iBAAgBC,GAAC,IAAE,GAAEC,KAAEJ,GAAE,kBAAiB,IAAE,EAAE,kBAAiB,IAAEC,GAAEG,IAAE,CAAC,GAAE,EAAC,MAAKC,IAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC,IAAEL,IAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,MAAI,EAAE,iBAAe,iBAAcG,MAAA,gBAAAA,GAAG,QAAM,IAAE,EAAE,WAAS,CAAC,GAAE,CAAC,GAAEe,KAAE,EAAE,CAAC,IAAE,EAAE,GAAEC,KAAE,EAAE,CAAC,IAAE,EAAE,GAAEC,KAAE,MAAI,EAAE,CAAC,GAAEX,KAAE,KAAK,MAAM,IAAEJ,MAAGa,KAAE,MAAG,EAAE,CAAC,CAAC,KAAGE,KAAE,IAAE,IAAGC,KAAE,KAAK,MAAM,IAAE,KAAGF,KAAE,MAAG,EAAE,CAAC,CAAC,KAAGC,KAAE,IAAE,IAAGE,KAAE,EAAE,EAAC,MAAKb,IAAE,MAAKY,IAAE,MAAKhB,IAAE,MAAK,GAAE,MAAKa,IAAE,MAAKC,IAAE,MAAKf,IAAE,OAAM,GAAE,qBAAoBF,IAAE,UAAS,EAAE,CAAC,KAAG,GAAE,gBAAekB,GAAC,CAAC,GAAEV,KAAE,CAAC;AAAE,MAAIa,IAAEZ,KAAE;AAAE,QAAMa,KAAEJ,KAAE,KAAG,KAAI,EAAC,MAAKK,IAAE,MAAKC,IAAE,MAAKC,IAAE,OAAMf,IAAE,QAAOgB,GAAC,IAAE,GAAEC,KAAE,EAAE,GAAE,CAAC,GAAEC,KAAE,EAAE,CAAC,KAAGL,KAAE,KAAGC,KAAE,IAAE;AAAE,MAAIK,KAAE;AAAG,MAAG,GAAE;AAAC,UAAMlB,KAAE,EAAET,EAAC;AAAE,IAAA2B,KAAE,EAAElB,EAAC,KAAGA,GAAE,eAAa;AAAA,EAAC;AAAC,WAAQ,IAAE,GAAE,IAAEJ,IAAE,KAAI;AAAC,UAAMI,KAAE,CAAC;AAAE,aAAQb,KAAE,GAAEA,KAAEqB,IAAErB,MAAI;AAAC,UAAIO,KAAEe,GAAE,IAAED,KAAErB,EAAC;AAAE,UAAG,KAAGO,GAAE,CAAC,IAAEmB,MAAGnB,GAAE,CAAC,IAAE,IAAE,IAAEsB,KAAEtB,GAAE,CAAC,KAAG,IAAE,KAAG,MAAI,KAAGA,GAAE,CAAC,IAAE,KAAGuB,MAAG,CAAC3B,OAAII,GAAE,CAAC,KAAG,IAAG,CAACA,MAAG,MAAMA,GAAE,CAAC,CAAC,KAAG,MAAMA,GAAE,CAAC,CAAC,EAAE,CAAAG,GAAE,KAAKc,EAAC,GAAEd,GAAE,KAAKc,EAAC,GAAEX,GAAE,KAAK,IAAI,GAAEF;AAAA,WAAQ;AAAC,YAAGR,IAAE;AAAC,gBAAMU,KAAEV,GAAE,iBAAiB,IAAI,EAAE,EAAC,GAAEI,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiB,EAAC,CAAC,CAAC;AAAE,UAAAA,KAAE,CAACM,GAAE,GAAEA,GAAE,CAAC;AAAA,QAAC;AAAC,QAAAA,GAAE,KAAKN,EAAC,GAAE,IAAE,KAAG,KAAGgB,GAAEvB,EAAC,KAAGO,GAAE,CAAC,IAAEgB,GAAEvB,EAAC,EAAE,CAAC,MAAIO,GAAE,CAAC,KAAG,GAAEwB,MAAGxB,GAAE,CAAC,IAAEmB,MAAGnB,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC,KAAG,KAAIG,GAAE,MAAMH,GAAE,CAAC,IAAEkB,MAAGb,EAAC,GAAEF,GAAE,MAAMiB,KAAEpB,GAAE,CAAC,KAAGqB,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAL,KAAEV;AAAA,EAAC;AAAC,SAAM,EAAC,SAAQH,IAAE,OAAM,MAAK,cAAa,MAAK,sBAAqBC,IAAE,SAAQ,GAAE,MAAKS,KAAE,CAACX,IAAEY,EAAC,IAAE,CAACZ,KAAE,GAAEY,KAAE,CAAC,EAAC;AAAC;AAAC,SAAS,GAAG,GAAErB,IAAE,GAAE;AAAC,QAAK,EAAC,MAAK,GAAE,MAAK,EAAC,IAAEA,IAAEE,KAAE,IAAI,cAAc,IAAE,MAAI,IAAE,KAAG,IAAE,CAAC,GAAEC,KAAE,IAAI,aAAa,CAAC,IAAG,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,EAAE,CAAC,GAAEC,KAAE,IAAI,aAAa,CAAC,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,CAAC,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAE,IAAE,GAAE,KAAI;AAAC,aAAQJ,KAAE,GAAEA,KAAE,IAAE,GAAEA,MAAI;AAAC,UAAIM,KAAE,IAAE,IAAE,IAAE,IAAEN;AAAE,YAAM,IAAE,EAAEM,EAAC,GAAED,KAAE,EAAEC,KAAE,CAAC,GAAE,IAAE,EAAEA,KAAE,CAAC,GAAE,IAAE,EAAEA,KAAE,CAAC;AAAE,MAAAA,MAAG,IAAE;AAAE,YAAM,IAAE,EAAEA,EAAC,GAAE,IAAE,EAAEA,KAAE,CAAC,GAAE,IAAE,EAAEA,KAAE,CAAC,GAAE,IAAE,EAAEA,KAAE,CAAC;AAAE,UAAI,IAAE,GAAEY,KAAE,MAAIlB,MAAG,IAAE,KAAG;AAAG,eAAQa,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAX,GAAEgB,IAAG,IAAEf,GAAE,GAAG,IAAE,IAAEA,GAAE,GAAG,IAAE,IAAEA,GAAE,GAAG,IAAE;AAAE,UAAE;AAAE,eAAQU,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAX,GAAEgB,IAAG,IAAEf,GAAE,GAAG,IAAEE,KAAEF,GAAE,GAAG,IAAE,IAAEA,GAAE,GAAG,IAAE;AAAE,UAAE;AAAE,eAAQU,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAX,GAAEgB,IAAG,IAAEd,GAAE,GAAG,IAAE,IAAEA,GAAE,GAAG,IAAE,IAAEA,GAAE,GAAG,IAAE;AAAE,UAAE;AAAE,eAAQS,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAX,GAAEgB,IAAG,IAAEd,GAAE,GAAG,IAAEC,KAAED,GAAE,GAAG,IAAE,IAAEA,GAAE,GAAG,IAAE;AAAA,IAAC;AAAC,QAAG,EAAE,UAAQS,KAAE,GAAEA,KAAEX,GAAE,QAAOW,KAAI,OAAMX,GAAEW,EAAC,CAAC,MAAIX,GAAEW,EAAC,IAAE;AAAA,EAAG;AAAC,SAAOX;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,QAAMF,KAAE,EAAE,MAAM,EAAE,UAAU;AAAE,SAAO,MAAIA,GAAE,SAAOA,GAAE,CAAC,IAAE,EAAEA,EAAC;AAAC;AAAC,SAAS,GAAG,GAAEA,IAAE,GAAE;AAAC,QAAK,EAAC,aAAYE,IAAE,WAAUC,GAAC,IAAEH;AAAE,MAAII,KAAE,GAAE,IAAE;AAAG,QAAK,EAAC,oBAAmB,EAAC,IAAEF;AAAE,MAAG,EAAE,CAAC,KAAG,EAAE,QAAO;AAAC,UAAM,KAAG,EAAE,IAAE,EAAE,KAAG,GAAEA,KAAE,EAAE,EAAE,SAAO,CAAC,GAAEG,MAAGH,GAAE,IAAEA,GAAE,KAAG,GAAE8B,MAAG7B,GAAE,IAAEA,GAAE,KAAG;AAAE,QAAG,KAAG6B,GAAE,CAAA5B,KAAE;AAAA,aAAU,KAAGC,GAAE,CAAAD,KAAE,EAAE,QAAO,IAAE,IAAEC,KAAE;AAAA,SAAM;AAAC,UAAIQ,IAAEb,KAAEgC;AAAE,eAAQ,IAAE,GAAE,KAAG,EAAE,QAAO,KAAI;AAAC,YAAGnB,MAAG,EAAE,IAAE,CAAC,EAAE,IAAE,EAAE,IAAE,CAAC,EAAE,KAAG,GAAE,KAAGA,IAAE;AAAC,gBAAIA,KAAET,KAAE,IAAE,WAAS,KAAGA,KAAE,IAAE,GAAE,IAAE,IAAEJ,KAAE,KAAGI,KAAE,SAAO,KAAG,IAAEJ,KAAEa,KAAE,KAAG,IAAEb,KAAE,IAAE,IAAE,IAAE;AAAE;AAAA,QAAK;AAAC,QAAAA,KAAEa;AAAA,MAAC;AAAA,IAAC;AAAC,UAAMC,KAAE,MAAIV,KAAED,KAAE,EAAEC,KAAE,CAAC;AAAE,QAAG,GAAE;AAAC,WAAK,IAAIU,GAAE,GAAEA,GAAE,CAAC,IAAE,EAAEd,GAAE,gBAAgB,IAAE,UAAQ,IAAE;AAAA,IAAG;AAAC,WAAM,EAAC,cAAaI,IAAE,mBAAkB,IAAI,EAAE,EAAC,GAAEU,GAAE,GAAE,GAAEA,GAAE,GAAE,kBAAiBd,GAAE,iBAAgB,CAAC,GAAE,kBAAiB,EAAC;AAAA,EAAC;AAAC,QAAMK,KAAE,KAAK,IAAI,EAAE,IAAEF,GAAE,CAAC,IAAE,KAAK,KAAI,IAAE,KAAK,IAAI,EAAE,IAAEA,GAAE,CAAC,IAAE,KAAK,KAAI,IAAEH,GAAE,YAAY,uBAAqB;AAAE,EAAAI,KAAE,WAAS,IAAE,KAAK,MAAM,KAAK,IAAIC,IAAE,CAAC,CAAC,IAAE,SAAO,IAAE,KAAK,KAAK,KAAK,IAAIA,IAAE,CAAC,CAAC,IAAE,KAAK,OAAOA,KAAE,KAAG,CAAC,GAAED,KAAE,IAAEA,KAAE,IAAEA,KAAE,MAAI,IAAEA,KAAE,IAAE,GAAEA,KAAE;AAAG,QAAM,IAAE,KAAGA;AAAE,SAAM,EAAC,cAAaA,IAAE,mBAAkB,IAAI,EAAE,EAAC,GAAE,IAAEJ,GAAE,gBAAgB,GAAE,GAAE,IAAEA,GAAE,gBAAgB,GAAE,kBAAiBA,GAAE,iBAAgB,CAAC,GAAE,kBAAiB,EAAC;AAAC;AAAC,SAAS,GAAG,GAAEA,IAAE,IAAE,KAAIE,KAAE,MAAG;AAJ3ie;AAI4ie,QAAK,EAAC,QAAOC,IAAE,kBAAiBC,IAAE,WAAU,EAAC,IAAE,GAAE,IAAE,EAAE,IAAI,EAAE,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,GAAE,kBAAiBA,GAAC,CAAC,GAAEJ,IAAEG,EAAC;AAAE,MAAG,QAAM,EAAE,QAAM,EAAC,oBAAmB,MAAK,QAAO,MAAK,gBAAe,MAAK,sBAAqB,MAAE;AAAE,QAAME,MAAG,EAAE,IAAE,EAAE,KAAG,GAAE,IAAE,EAAEL,EAAC,GAAE,IAAEK,KAAE,IAAE,KAAG,OAAM,IAAEL,GAAE,eAAa,MAAI,IAAE,sBAAkB,MAAI,IAAE;AAAiB,MAAI,IAAE,oBAAkB,EAAE,YAAU,gBAAc,EAAE;AAAS,QAAM,IAAE,EAAEG,IAAEH,EAAC,GAAE,IAAE,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE,OAAM,EAAE,MAAM,IAAE,EAAE,IAAE,KAAK,GAAG,GAAE,KAAK,KAAK,KAAK,IAAI,IAAE,IAAE,CAAC,IAAE,KAAK,GAAG,CAAC;AAAE,MAAG,CAAC,KAAGE,OAAIF,GAAE,gBAAcA,GAAE,mBAAiB,IAAE,EAAE,OAAK,EAAE,OAAK,GAAE,CAAC,KAAG,IAAE,IAAG;AAAC,UAAMa,KAAE,EAAEb,EAAC;AAAE,QAAG,EAAEa,EAAC,GAAE;AAAC,YAAMb,KAAE,KAAG,IAAEK,KAAE,GAAE,IAAE,KAAK,KAAKQ,KAAEb,EAAC;AAAE,UAAE,MAAI,KAAG,MAAI,KAAGa,KAAE,IAAE,EAAE,OAAKb;AAAA,IAAC;AAAA,EAAC;AAAC,MAAIkB,IAAEC,KAAE;AAAE,QAAMlB,KAAE,OAAMmB,KAAE,KAAK,IAAI,GAAE,KAAK,IAAI,SAAM,OAAE,gBAAF,mBAAe,yBAAsB,CAAC,CAAC;AAAE,MAAG,GAAE;AAAC,IAAAD,KAAE;AAAE,UAAMN,KAAEb,GAAE,eAAa,uBAAqB,qBAAmB,IAAEa,MAAG,KAAG,IAAE,QAAO,IAAEb,GAAE,eAAa,OAAK;AAAK,IAAAkB,KAAE,EAAE,IAAI,EAAE,EAAC,GAAEL,IAAE,GAAEA,IAAE,kBAAiB,EAAC,MAAK,EAAC,EAAC,CAAC,GAAET,IAAE,CAAC,GAAEc,GAAE,KAAGC,KAAE,GAAED,GAAE,KAAGC,KAAE;AAAA,EAAC,OAAK;AAAC,IAAAD,KAAE,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,EAAC;AAAE,QAAIL,KAAE;AAAE,WAAKM,KAAE,KAAGlB,KAAE,MAAIY,KAAE,IAAG,CAAAA,MAAIM,MAAGC,IAAEF,GAAE,KAAGE,IAAEF,GAAE,KAAGE;AAAE,SAAK,IAAID,IAAE,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,KAAGlB,OAAIkB,KAAE;AAAA,EAAE;AAAC,QAAMV,KAAE,CAACU,EAAC,GAAEc,KAAE,CAAC,EAAC,GAAEf,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC,GAAEgB,KAAE,YAAWb,KAAE,KAAK,IAAIa,IAAE,CAAC,IAAEjC;AAAE,SAAKkB,MAAGE,KAAG,CAAAF,MAAGC,IAAEF,GAAE,KAAGE,IAAEF,GAAE,KAAGE,IAAEX,GAAE,KAAKU,EAAC,GAAEc,GAAE,KAAK,EAAC,GAAEf,GAAE,GAAE,GAAEA,GAAE,EAAC,CAAC;AAAE,SAAM,EAAC,oBAAmB,GAAE,QAAOT,IAAE,gBAAewB,IAAE,sBAAqB,CAAC,EAAC;AAAC;", "names": ["t", "w", "r", "s", "a", "f", "n", "o", "i", "P", "E", "v", "O", "e", "m", "l", "h", "$", "d", "M", "R", "G", "N", "T", "C", "j", "z", "L", "I", "B", "F", "Y", "u", "S", "b"]}