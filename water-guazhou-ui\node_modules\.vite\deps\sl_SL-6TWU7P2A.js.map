{"version": 3, "sources": ["../../@arcgis/core/chunks/sl_SL.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{g as e}from\"./_commonjsHelpers.js\";import{c as i}from\"./_commonjs-dynamic-modules.js\";function a(e,i){for(var a=0;a<i.length;a++){const r=i[a];if(\"string\"!=typeof r&&!Array.isArray(r))for(const i in r)if(\"default\"!==i&&!(i in e)){const a=Object.getOwnPropertyDescriptor(r,i);a&&Object.defineProperty(e,i,a.get?a:{enumerable:!0,get:()=>r[i]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}))}var r,o,n={},t={get exports(){return n},set exports(e){n=e}};r=t,void 0!==(o=function(e,i){Object.defineProperty(i,\"__esModule\",{value:!0}),i.default={_decimalSeparator:\",\",_thousandSeparator:\".\",_percentPrefix:null,_percentSuffix:\"%\",_big_number_suffix_3:\"k\",_big_number_suffix_6:\"M\",_big_number_suffix_9:\"G\",_big_number_suffix_12:\"T\",_big_number_suffix_15:\"P\",_big_number_suffix_18:\"E\",_big_number_suffix_21:\"Z\",_big_number_suffix_24:\"Y\",_small_number_suffix_3:\"m\",_small_number_suffix_6:\"μ\",_small_number_suffix_9:\"n\",_small_number_suffix_12:\"p\",_small_number_suffix_15:\"f\",_small_number_suffix_18:\"a\",_small_number_suffix_21:\"z\",_small_number_suffix_24:\"y\",_byte_suffix_B:\"B\",_byte_suffix_KB:\"KB\",_byte_suffix_MB:\"MB\",_byte_suffix_GB:\"GB\",_byte_suffix_TB:\"TB\",_byte_suffix_PB:\"PB\",_date_millisecond:\"mm:ss SSS\",_date_second:\"HH:mm:ss\",_date_minute:\"HH:mm\",_date_hour:\"HH:mm\",_date_day:\"MMM dd\",_date_week:\"ww\",_date_month:\"MMM\",_date_year:\"yyyy\",_duration_millisecond:\"SSS\",_duration_millisecond_second:\"ss.SSS\",_duration_millisecond_minute:\"mm:ss SSS\",_duration_millisecond_hour:\"hh:mm:ss SSS\",_duration_millisecond_day:\"d'd' mm:ss SSS\",_duration_millisecond_week:\"d'd' mm:ss SSS\",_duration_millisecond_month:\"M'm' dd'd' mm:ss SSS\",_duration_millisecond_year:\"y'y' MM'm' dd'd' mm:ss SSS\",_duration_second:\"ss\",_duration_second_minute:\"mm:ss\",_duration_second_hour:\"hh:mm:ss\",_duration_second_day:\"d'd' hh:mm:ss\",_duration_second_week:\"d'd' hh:mm:ss\",_duration_second_month:\"M'm' dd'd' hh:mm:ss\",_duration_second_year:\"y'y' MM'm' dd'd' hh:mm:ss\",_duration_minute:\"mm\",_duration_minute_hour:\"hh:mm\",_duration_minute_day:\"d'd' hh:mm\",_duration_minute_week:\"d'd' hh:mm\",_duration_minute_month:\"M'm' dd'd' hh:mm\",_duration_minute_year:\"y'y' MM'm' dd'd' hh:mm\",_duration_hour:\"hh'h'\",_duration_hour_day:\"d'd' hh'h'\",_duration_hour_week:\"d'd' hh'h'\",_duration_hour_month:\"M'm' dd'd' hh'h'\",_duration_hour_year:\"y'y' MM'm' dd'd' hh'h'\",_duration_day:\"d'd'\",_duration_day_week:\"d'd'\",_duration_day_month:\"M'm' dd'd'\",_duration_day_year:\"y'y' MM'm' dd'd'\",_duration_week:\"w'w'\",_duration_week_month:\"w'w'\",_duration_week_year:\"w'w'\",_duration_month:\"M'm'\",_duration_month_year:\"y'y' MM'm'\",_duration_year:\"y'y'\",_era_ad:\"n. št.\",_era_bc:\"pr. n. št.\",A:\"A\",P:\"P\",AM:\"AM\",PM:\"PM\",\"A.M.\":\"A.M.\",\"P.M.\":\"P.M.\",January:\"Januar\",February:\"Februar\",March:\"Marec\",April:\"April\",May:\"Maj\",June:\"Junij\",July:\"Julij\",August:\"Avgust\",September:\"September\",October:\"Oktober\",November:\"November\",December:\"December\",Jan:\"Jan\",Feb:\"Feb\",Mar:\"Mar\",Apr:\"Apr\",\"May(short)\":\"Maj\",Jun:\"Jun\",Jul:\"Jul\",Aug:\"Avg\",Sep:\"Sep\",Oct:\"Okt\",Nov:\"Nov\",Dec:\"Dec\",Sunday:\"Nedelja\",Monday:\"Ponedeljek\",Tuesday:\"Torek\",Wednesday:\"Sreda\",Thursday:\"Četrtek\",Friday:\"Petek\",Saturday:\"Sobota\",Sun:\"Ned\",Mon:\"Pon\",Tue:\"Tor\",Wed:\"Sre\",Thu:\"Čet\",Fri:\"Pet\",Sat:\"Sob\",_dateOrd:function(e){return\".\"},\"Zoom Out\":\"Oddalji pogled\",Play:\"Zaženi\",Stop:\"Ustavi\",Legend:\"Legenda\",\"Click, tap or press ENTER to toggle\":\"Klikni, tapni ali pritisni ENTER za preklop\",Loading:\"Nalagam\",Home:\"Domov\",Chart:\"Graf\",\"Serial chart\":\"Serijski graf\",\"X/Y chart\":\"X/Y graf\",\"Pie chart\":\"Tortni graf\",\"Gauge chart\":\"Stevčni graf\",\"Radar chart\":\"Radar graf\",\"Sankey diagram\":\"Sankey diagram\",\"Flow diagram\":\"Prikaz poteka\",\"Chord diagram\":\"Kolobarni diagram\",\"TreeMap chart\":\"Drevesi graf\",\"Sliced chart\":\"Sliced graf\",Series:\"Serija\",\"Candlestick Series\":\"Svečna serija\",\"OHLC Series\":\"OHLC serija\",\"Column Series\":\"Stolpičasta serija\",\"Line Series\":\"Črtna serija\",\"Pie Slice Series\":\"Tortna serija\",\"Funnel Series\":\"Lijak serija\",\"Pyramid Series\":\"Piramidna serija\",\"X/Y Series\":\"X/Y serija\",Map:\"Mapa\",\"Press ENTER to zoom in\":\"Pritisni ENTER za približevanje\",\"Press ENTER to zoom out\":\"Pritisni ENTER za oddaljevanje\",\"Use arrow keys to zoom in and out\":\"Uporabi smerne tiple za približevanje in oddaljevanje\",\"Use plus and minus keys on your keyboard to zoom in and out\":\"Uporabi plus in minus tipke na tipkovnici za približevanje in oddaljevanje\",Export:\"Izvozi\",Image:\"Slika\",Data:\"Podatki\",Print:\"Natisni\",\"Click, tap or press ENTER to open\":\"Klikni, tapni ali pritisni ENTER da odpreš.\",\"Click, tap or press ENTER to print.\":\"Klikni, tapni ali pritisni ENTER za tiskanje.\",\"Click, tap or press ENTER to export as %1.\":\"Klikni, tapni ali pritisni ENTER da izvoziš kot %1.\",'To save the image, right-click this link and choose \"Save picture as...\"':'Da shraniš sliko, z desnim gumbom miške klikni to povezavo in izberi \"Shrani sliko kot...\"','To save the image, right-click thumbnail on the left and choose \"Save picture as...\"':'Da shraniš sliko, z desnim gumbom miške klikni sličico na levi in izberi \"Shrani sliko kot...\"',\"(Press ESC to close this message)\":\"(Pritisni ESC da zapreš to sporočilo)\",\"Image Export Complete\":\"Izvoz slike končan\",\"Export operation took longer than expected. Something might have gone wrong.\":\"Operacija izvoza je trajala dlje kot pričakovano. Nekaj je šlo narobe.\",\"Saved from\":\"Shranjeno od\",PNG:\"PNG\",JPG:\"JPG\",GIF:\"GIF\",SVG:\"SVG\",PDF:\"PDF\",JSON:\"JSON\",CSV:\"CSV\",XLSX:\"XLSX\",\"Use TAB to select grip buttons or left and right arrows to change selection\":\"Uporabi TAB za izbiro drsnih gumbov ali levo in desno smerno tipko da spremeniš izbiro\",\"Use left and right arrows to move selection\":\"Uporabi levo in desno smerno tipko za premik izbranega\",\"Use left and right arrows to move left selection\":\"Uporabi levo in desno smerno tipko za premik leve izbire\",\"Use left and right arrows to move right selection\":\"Uporabi levo in desno smerno tipko za premik desne izbire\",\"Use TAB select grip buttons or up and down arrows to change selection\":\"Uporabi TAB za izbiro drsnih gumbov ali gor in dol smerno tipko da spremeniš izbiro\",\"Use up and down arrows to move selection\":\"Uporabi gor in dol smerne tipke za premik izbire\",\"Use up and down arrows to move lower selection\":\"Uporabi gor in dol smerne tipke za premik spodnje izbire\",\"Use up and down arrows to move upper selection\":\"Uporabi gor in dol smerne tipke za premik zgornje izbire\",\"From %1 to %2\":\"Od %1 do %2\",\"From %1\":\"Od %1\",\"To %1\":\"Do %1\",\"No parser available for file: %1\":\"Nobenega parserja ni na voljo za datoteko: %1\",\"Error parsing file: %1\":\"Napaka pri parsanju datoteke: %1\",\"Unable to load file: %1\":\"Ni mogoče naložiti datoteke: %1\",\"Invalid date\":\"Neveljaven datum\"}}(i,n))&&(r.exports=o);const s=a({__proto__:null,default:e(n)},[n]);export{s};\n"], "mappings": ";;;;;;;;;AAI6F,SAAS,EAAE,GAAE,GAAE;AAAC,WAAQA,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,QAAG,YAAU,OAAOC,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUC,MAAKD,GAAE,KAAG,cAAYC,MAAG,EAAEA,MAAK,IAAG;AAAC,cAAMF,KAAE,OAAO,yBAAyBC,IAAEC,EAAC;AAAE,QAAAF,MAAG,OAAO,eAAe,GAAEE,IAAEF,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIC,GAAEC,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAe,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAID;AAAJ,IAAME;AAAN,IAAQ,IAAE,CAAC;AAAX,IAAa,IAAE,EAAC,IAAI,UAAS;AAAC,SAAO;AAAC,GAAE,IAAI,QAAQ,GAAE;AAAC,MAAE;AAAC,EAAC;AAAEF,KAAE,GAAE,YAAUE,KAAE,SAAS,GAAE,GAAE;AAAC,SAAO,eAAe,GAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAE,EAAE,UAAQ,EAAC,mBAAkB,KAAI,oBAAmB,KAAI,gBAAe,MAAK,gBAAe,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,gBAAe,KAAI,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,mBAAkB,aAAY,cAAa,YAAW,cAAa,SAAQ,YAAW,SAAQ,WAAU,UAAS,YAAW,MAAK,aAAY,OAAM,YAAW,QAAO,uBAAsB,OAAM,8BAA6B,UAAS,8BAA6B,aAAY,4BAA2B,gBAAe,2BAA0B,kBAAiB,4BAA2B,kBAAiB,6BAA4B,wBAAuB,4BAA2B,8BAA6B,kBAAiB,MAAK,yBAAwB,SAAQ,uBAAsB,YAAW,sBAAqB,iBAAgB,uBAAsB,iBAAgB,wBAAuB,uBAAsB,uBAAsB,6BAA4B,kBAAiB,MAAK,uBAAsB,SAAQ,sBAAqB,cAAa,uBAAsB,cAAa,wBAAuB,oBAAmB,uBAAsB,0BAAyB,gBAAe,SAAQ,oBAAmB,cAAa,qBAAoB,cAAa,sBAAqB,oBAAmB,qBAAoB,0BAAyB,eAAc,QAAO,oBAAmB,QAAO,qBAAoB,cAAa,oBAAmB,oBAAmB,gBAAe,QAAO,sBAAqB,QAAO,qBAAoB,QAAO,iBAAgB,QAAO,sBAAqB,cAAa,gBAAe,QAAO,SAAQ,UAAS,SAAQ,cAAa,GAAE,KAAI,GAAE,KAAI,IAAG,MAAK,IAAG,MAAK,QAAO,QAAO,QAAO,QAAO,SAAQ,UAAS,UAAS,WAAU,OAAM,SAAQ,OAAM,SAAQ,KAAI,OAAM,MAAK,SAAQ,MAAK,SAAQ,QAAO,UAAS,WAAU,aAAY,SAAQ,WAAU,UAAS,YAAW,UAAS,YAAW,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,cAAa,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,QAAO,WAAU,QAAO,cAAa,SAAQ,SAAQ,WAAU,SAAQ,UAAS,WAAU,QAAO,SAAQ,UAAS,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,UAAS,SAASC,IAAE;AAAC,WAAM;AAAA,EAAG,GAAE,YAAW,kBAAiB,MAAK,UAAS,MAAK,UAAS,QAAO,WAAU,uCAAsC,+CAA8C,SAAQ,WAAU,MAAK,SAAQ,OAAM,QAAO,gBAAe,iBAAgB,aAAY,YAAW,aAAY,eAAc,eAAc,gBAAe,eAAc,cAAa,kBAAiB,kBAAiB,gBAAe,iBAAgB,iBAAgB,qBAAoB,iBAAgB,gBAAe,gBAAe,eAAc,QAAO,UAAS,sBAAqB,iBAAgB,eAAc,eAAc,iBAAgB,sBAAqB,eAAc,gBAAe,oBAAmB,iBAAgB,iBAAgB,gBAAe,kBAAiB,oBAAmB,cAAa,cAAa,KAAI,QAAO,0BAAyB,mCAAkC,2BAA0B,kCAAiC,qCAAoC,yDAAwD,+DAA8D,8EAA6E,QAAO,UAAS,OAAM,SAAQ,MAAK,WAAU,OAAM,WAAU,qCAAoC,+CAA8C,uCAAsC,iDAAgD,8CAA6C,uDAAsD,4EAA2E,8FAA6F,wFAAuF,kGAAiG,qCAAoC,yCAAwC,yBAAwB,sBAAqB,gFAA+E,0EAAyE,cAAa,gBAAe,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,MAAK,QAAO,KAAI,OAAM,MAAK,QAAO,+EAA8E,0FAAyF,+CAA8C,0DAAyD,oDAAmD,4DAA2D,qDAAoD,6DAA4D,yEAAwE,uFAAsF,4CAA2C,oDAAmD,kDAAiD,4DAA2D,kDAAiD,4DAA2D,iBAAgB,eAAc,WAAU,SAAQ,SAAQ,SAAQ,oCAAmC,iDAAgD,0BAAyB,oCAAmC,2BAA0B,mCAAkC,gBAAe,mBAAkB;AAAC,EAAE,GAAE,CAAC,OAAKH,GAAE,UAAQE;AAAG,IAAM,IAAE,EAAE,EAAC,WAAU,MAAK,SAAQ,EAAE,CAAC,EAAC,GAAE,CAAC,CAAC,CAAC;", "names": ["a", "r", "i", "o", "e"]}