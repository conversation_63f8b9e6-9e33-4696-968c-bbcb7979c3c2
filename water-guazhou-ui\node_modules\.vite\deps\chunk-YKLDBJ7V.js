import {
  i,
  n as n2
} from "./chunk-HTESBPT2.js";
import {
  a
} from "./chunk-WL2F66AK.js";
import {
  o as o4
} from "./chunk-TUB4N6LD.js";
import {
  o as o2
} from "./chunk-NLDHTNKF.js";
import {
  e as e2,
  f
} from "./chunk-YV4RKNU4.js";
import {
  o as o3
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  r
} from "./chunk-SROTSYJS.js";
import {
  n
} from "./chunk-FOE4ICAJ.js";
import {
  e
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/chunks/SSAO.glsl.js
var m = 16;
function d() {
  const t = new o3(), d2 = t.fragment;
  return t.include(o2), d2.include(a), t.include(n2), d2.uniforms.add(new o4("radius", (e3, r2) => p(r2.camera))), d2.code.add(o`vec3 sphere[16];
void fillSphere() {
sphere[0] = vec3(0.186937, 0.0, 0.0);
sphere[1] = vec3(0.700542, 0.0, 0.0);
sphere[2] = vec3(-0.864858, -0.481795, -0.111713);
sphere[3] = vec3(-0.624773, 0.102853, -0.730153);
sphere[4] = vec3(-0.387172, 0.260319, 0.007229);
sphere[5] = vec3(-0.222367, -0.642631, -0.707697);
sphere[6] = vec3(-0.01336, -0.014956, 0.169662);
sphere[7] = vec3(0.122575, 0.1544, -0.456944);
sphere[8] = vec3(-0.177141, 0.85997, -0.42346);
sphere[9] = vec3(-0.131631, 0.814545, 0.524355);
sphere[10] = vec3(-0.779469, 0.007991, 0.624833);
sphere[11] = vec3(0.308092, 0.209288,0.35969);
sphere[12] = vec3(0.359331, -0.184533, -0.377458);
sphere[13] = vec3(0.192633, -0.482999, -0.065284);
sphere[14] = vec3(0.233538, 0.293706, -0.055139);
sphere[15] = vec3(0.417709, -0.386701, 0.442449);
}
float fallOffFunction(float vv, float vn, float bias) {
float f = max(radius * radius - vv, 0.0);
return f * f * f * max(vn-bias, 0.0);
}`), d2.code.add(o`float aoValueFromPositionsAndNormal(vec3 C, vec3 n_C, vec3 Q) {
vec3 v = Q - C;
float vv = dot(v, v);
float vn = dot(normalize(v), n_C);
return fallOffFunction(vv, vn, 0.1);
}`), d2.uniforms.add([new e2("nearFar", (e3, r2) => r2.camera.nearFar), new f("normalMap", (e3) => e3.normalTexture), new f("depthMap", (e3) => e3.depthTexture), new e2("zScale", (e3, r2) => i(r2)), new o4("projScale", (e3) => e3.projScale), new f("rnm", (e3) => e3.noiseTexture), new e2("rnmScale", (t2, o5) => r(v, o5.camera.fullWidth / e(t2.noiseTexture).descriptor.width, o5.camera.fullHeight / e(t2.noiseTexture).descriptor.height)), new o4("intensity", (e3) => e3.intensity), new e2("screenSize", (e3, t2) => r(v, t2.camera.fullWidth, t2.camera.fullHeight))]), d2.code.add(o`
    void main(void) {
      fillSphere();
      vec3 fres = normalize((texture2D(rnm, uv * rnmScale).xyz * 2.0) - vec3(1.0));
      float currentPixelDepth = linearDepthFromTexture(depthMap, uv, nearFar);

      if (-currentPixelDepth>nearFar.y || -currentPixelDepth<nearFar.x) {
        gl_FragColor = vec4(0.0);
        return;
      }

      vec3 currentPixelPos = reconstructPosition(gl_FragCoord.xy,currentPixelDepth);

      // get the normal of current fragment
      vec4 norm4 = texture2D(normalMap, uv);
      vec3 norm = vec3(-1.0) + 2.0 * norm4.xyz;
      bool isTerrain = norm4.w<0.5;

      float sum = .0;
      vec3 tapPixelPos;

      // note: the factor 2.0 should not be necessary, but makes ssao much nicer.
      // bug or deviation from CE somewhere else?
      float ps = projScale / (2.0 * currentPixelPos.z * zScale.x + zScale.y);

      for(int i = 0; i < ${o.int(m)}; ++i) {
        vec2 unitOffset = reflect(sphere[i], fres).xy;
        vec2 offset = vec2(-unitOffset * radius * ps);

        //don't use current or very nearby samples
        if ( abs(offset.x)<2.0 || abs(offset.y)<2.0) continue;

        vec2 tc = vec2(gl_FragCoord.xy + offset);
        if (tc.x < 0.0 || tc.y < 0.0 || tc.x > screenSize.x || tc.y > screenSize.y) continue;
        vec2 tcTap = tc / screenSize;
        float occluderFragmentDepth = linearDepthFromTexture(depthMap, tcTap, nearFar);

        if (isTerrain) {
          bool isTerrainTap = texture2D(normalMap, tcTap).w<0.5;
          if (isTerrainTap) {
            continue;
          }
        }

        tapPixelPos = reconstructPosition(tc, occluderFragmentDepth);

        sum+= aoValueFromPositionsAndNormal(currentPixelPos, norm, tapPixelPos);
      }

      // output the result
      float A = max(1.0 - sum * intensity / float(${o.int(m)}),0.0);

      // Anti-tone map to reduce contrast and drag dark region farther: (x^0.2 + 1.2 * x^4)/2.2
      A = (pow(A, 0.2) + 1.2 * A*A*A*A) / 2.2;
      gl_FragColor = vec4(A);
    }
  `), t;
}
function p(e3) {
  return Math.max(10, 20 * e3.computeRenderPixelSizeAtDist(Math.abs(4 * e3.relativeElevation)));
}
var v = n();
var h = Object.freeze(Object.defineProperty({ __proto__: null, build: d, getRadius: p }, Symbol.toStringTag, { value: "Module" }));

export {
  d,
  p,
  h
};
//# sourceMappingURL=chunk-YKLDBJ7V.js.map
