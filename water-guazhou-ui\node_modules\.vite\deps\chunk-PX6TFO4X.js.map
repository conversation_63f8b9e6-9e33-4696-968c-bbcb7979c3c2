{"version": 3, "sources": ["../../@arcgis/core/rest/query/executeForCount.js", "../../@arcgis/core/rest/query/executeForIds.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{parseUrl as o}from\"../utils.js\";import{executeQueryForCount as r}from\"./operations/query.js\";import t from\"../support/Query.js\";async function n(n,s,m){const p=o(n);return r(p,t.from(s),{...m}).then((o=>o.data.count))}export{n as executeForCount};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{parseUrl as o}from\"../utils.js\";import{executeQueryForIds as r}from\"./operations/query.js\";import t from\"../support/Query.js\";async function s(s,e,m){const n=o(s);return r(n,t.from(e),{...m}).then((o=>o.data.objectIds))}export{s as executeForIds};\n"], "mappings": ";;;;;;;;;;;;AAIuI,eAAe,EAAEA,IAAEC,IAAE,GAAE;AAAC,QAAMC,KAAE,EAAEF,EAAC;AAAE,SAAO,EAAEE,IAAE,EAAE,KAAKD,EAAC,GAAE,EAAC,GAAG,EAAC,CAAC,EAAE,KAAM,OAAG,EAAE,KAAK,KAAM;AAAC;;;ACA3F,eAAe,EAAEE,IAAE,GAAE,GAAE;AAAC,QAAMC,KAAE,EAAED,EAAC;AAAE,SAAO,EAAEC,IAAE,EAAE,KAAK,CAAC,GAAE,EAAC,GAAG,EAAC,CAAC,EAAE,KAAM,OAAG,EAAE,KAAK,SAAU;AAAC;", "names": ["n", "s", "p", "s", "n"]}