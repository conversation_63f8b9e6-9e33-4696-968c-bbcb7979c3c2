import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o as o2
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  g,
  o,
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/timeUtils.js
var e2 = { milliseconds: 1, seconds: 1e3, minutes: 6e4, hours: 36e5, days: 864e5, weeks: 6048e5, months: 26784e5, years: 31536e6, decades: 31536e7, centuries: 31536e8 };
var t = { milliseconds: { getter: "getMilliseconds", setter: "setMilliseconds", multiplier: 1 }, seconds: { getter: "getSeconds", setter: "setSeconds", multiplier: 1 }, minutes: { getter: "getMinutes", setter: "setMinutes", multiplier: 1 }, hours: { getter: "getHours", setter: "setHours", multiplier: 1 }, days: { getter: "getDate", setter: "setDate", multiplier: 1 }, weeks: { getter: "getDate", setter: "setDate", multiplier: 7 }, months: { getter: "getMonth", setter: "setMonth", multiplier: 1 }, years: { getter: "getFullYear", setter: "setFullYear", multiplier: 1 }, decades: { getter: "getFullYear", setter: "setFullYear", multiplier: 10 }, centuries: { getter: "getFullYear", setter: "setFullYear", multiplier: 100 } };
function s(e3, t2) {
  const s2 = new Date(e3, t2 + 1, 1);
  return s2.setDate(0), s2.getDate();
}
function n(e3, n2, r3) {
  const l3 = new Date(e3.getTime());
  if (n2 && r3) {
    const e4 = t[r3], { getter: u, setter: i, multiplier: a2 } = e4;
    if ("months" === r3) {
      const e5 = s(l3.getFullYear(), l3.getMonth() + n2);
      l3.getDate() > e5 && l3.setDate(e5);
    }
    l3[i](l3[u]() + n2 * a2);
  }
  return l3;
}
function l2(e3, t2) {
  switch (t2) {
    case "milliseconds":
      return new Date(e3.getTime());
    case "seconds":
      return new Date(e3.getFullYear(), e3.getMonth(), e3.getDate(), e3.getHours(), e3.getMinutes(), e3.getSeconds());
    case "minutes":
      return new Date(e3.getFullYear(), e3.getMonth(), e3.getDate(), e3.getHours(), e3.getMinutes());
    case "hours":
      return new Date(e3.getFullYear(), e3.getMonth(), e3.getDate(), e3.getHours());
    case "days":
      return new Date(e3.getFullYear(), e3.getMonth(), e3.getDate());
    case "weeks":
      return new Date(e3.getFullYear(), e3.getMonth(), e3.getDate() - e3.getDay());
    case "months":
      return new Date(e3.getFullYear(), e3.getMonth(), 1);
    case "years":
      return new Date(e3.getFullYear(), 0, 1);
    case "decades":
      return new Date(e3.getFullYear() - e3.getFullYear() % 10, 0, 1);
    case "centuries":
      return new Date(e3.getFullYear() - e3.getFullYear() % 100, 0, 1);
    default:
      return /* @__PURE__ */ new Date();
  }
}
function g2(t2, s2, n2) {
  if (0 === t2) return 0;
  return t2 * e2[s2] / e2[n2];
}

// node_modules/@arcgis/core/TimeExtent.js
var p;
var d = p = class extends l {
  static get allTime() {
    return c;
  }
  static get empty() {
    return h;
  }
  constructor(t2) {
    super(t2), this.end = null, this.start = null;
  }
  readEnd(t2, e3) {
    return null != e3.end ? new Date(e3.end) : null;
  }
  writeEnd(t2, e3) {
    e3.end = t2 ? t2.getTime() : null;
  }
  get isAllTime() {
    return this.equals(p.allTime);
  }
  get isEmpty() {
    return this.equals(p.empty);
  }
  readStart(t2, e3) {
    return null != e3.start ? new Date(e3.start) : null;
  }
  writeStart(t2, e3) {
    e3.start = t2 ? t2.getTime() : null;
  }
  clone() {
    return new p({ end: this.end, start: this.start });
  }
  equals(t2) {
    if (!t2) return false;
    const e3 = r(this.start) ? this.start.getTime() : this.start, s2 = r(this.end) ? this.end.getTime() : this.end, n2 = r(t2.start) ? t2.start.getTime() : t2.start, i = r(t2.end) ? t2.end.getTime() : t2.end;
    return e3 === n2 && s2 === i;
  }
  expandTo(t2) {
    if (this.isEmpty || this.isAllTime) return this.clone();
    const e3 = o(this.start, (e4) => l2(e4, t2)), r3 = o(this.end, (e4) => {
      const r4 = l2(e4, t2);
      return e4.getTime() === r4.getTime() ? r4 : n(r4, 1, t2);
    });
    return new p({ start: e3, end: r3 });
  }
  intersection(t2) {
    if (!t2) return this.clone();
    if (this.isEmpty || t2.isEmpty) return p.empty;
    if (this.isAllTime) return t2.clone();
    if (t2.isAllTime) return this.clone();
    const e3 = g(this.start, -1 / 0, (t3) => t3.getTime()), r3 = g(this.end, 1 / 0, (t3) => t3.getTime()), s2 = g(t2.start, -1 / 0, (t3) => t3.getTime()), i = g(t2.end, 1 / 0, (t3) => t3.getTime());
    let o3, l3;
    if (s2 >= e3 && s2 <= r3 ? o3 = s2 : e3 >= s2 && e3 <= i && (o3 = e3), r3 >= s2 && r3 <= i ? l3 = r3 : i >= e3 && i <= r3 && (l3 = i), null != o3 && null != l3 && !isNaN(o3) && !isNaN(l3)) {
      const t3 = new p();
      return t3.start = o3 === -1 / 0 ? null : new Date(o3), t3.end = l3 === 1 / 0 ? null : new Date(l3), t3;
    }
    return p.empty;
  }
  offset(t2, e3) {
    if (this.isEmpty || this.isAllTime) return this.clone();
    const s2 = new p(), { start: n2, end: i } = this;
    return r(n2) && (s2.start = n(n2, t2, e3)), r(i) && (s2.end = n(i, t2, e3)), s2;
  }
  union(t2) {
    if (!t2 || t2.isEmpty) return this.clone();
    if (this.isEmpty) return t2.clone();
    if (this.isAllTime || t2.isAllTime) return c.clone();
    const e3 = r(this.start) && r(t2.start) ? new Date(Math.min(this.start.getTime(), t2.start.getTime())) : null, s2 = r(this.end) && r(t2.end) ? new Date(Math.max(this.end.getTime(), t2.end.getTime())) : null;
    return new p({ start: e3, end: s2 });
  }
};
e([y({ type: Date, json: { write: { allowNull: true } } })], d.prototype, "end", void 0), e([o2("end")], d.prototype, "readEnd", null), e([r2("end")], d.prototype, "writeEnd", null), e([y({ readOnly: true, json: { read: false } })], d.prototype, "isAllTime", null), e([y({ readOnly: true, json: { read: false } })], d.prototype, "isEmpty", null), e([y({ type: Date, json: { write: { allowNull: true } } })], d.prototype, "start", void 0), e([o2("start")], d.prototype, "readStart", null), e([r2("start")], d.prototype, "writeStart", null), d = p = e([a("esri.TimeExtent")], d);
var c = new d();
var h = new d({ start: void 0, end: void 0 });
var T = d;

export {
  n,
  g2 as g,
  T
};
//# sourceMappingURL=chunk-N7ADFPOO.js.map
