import "./chunk-GHAN7X65.js";
import "./chunk-3HDXUZDA.js";
import "./chunk-ARVYW5FF.js";
import "./chunk-27CJODAI.js";
import "./chunk-EJ4BPAYT.js";
import "./chunk-6DXPU43Z.js";
import "./chunk-3MBH7CQT.js";
import "./chunk-6FMMG4VO.js";
import {
  a as a3,
  w as w2
} from "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-EDV64J6E.js";
import "./chunk-OBW4AQOU.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-3BVSG4LE.js";
import "./chunk-JJZTA23S.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-VXAO6YJP.js";
import "./chunk-BI4P4NAQ.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-YROREPK5.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-CV76WXPW.js";
import "./chunk-EVADT7ME.js";
import "./chunk-T3GGN2P7.js";
import "./chunk-2B52LX6T.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import {
  j as j3,
  u as u2
} from "./chunk-CQR3NO7C.js";
import "./chunk-LGZKVOWE.js";
import "./chunk-WKBMFG6J.js";
import "./chunk-BPRRRPC3.js";
import {
  E2,
  f as f3
} from "./chunk-6G2NLXT7.js";
import "./chunk-RFTQI4ZD.js";
import {
  r as r7
} from "./chunk-UHA44FM7.js";
import "./chunk-MDHXGN24.js";
import "./chunk-6ZZUUGXX.js";
import "./chunk-TFWV44LH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-6OFWBRK2.js";
import {
  f as f4
} from "./chunk-NEPFZ7PM.js";
import {
  u as u3
} from "./chunk-HWB4LNSZ.js";
import "./chunk-JSZR3BUH.js";
import {
  r as r9
} from "./chunk-QKWIBVLD.js";
import "./chunk-JCXMTMKU.js";
import {
  T
} from "./chunk-WAPZ634R.js";
import "./chunk-OHAM27JH.js";
import {
  E
} from "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import {
  D,
  F,
  G,
  L,
  P
} from "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-2WS4DQ5K.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-22FAZXOH.js";
import {
  mt
} from "./chunk-T7HWQQFI.js";
import "./chunk-DFGMRI52.js";
import {
  r as r6
} from "./chunk-OZZFNS32.js";
import {
  y as y2
} from "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import {
  t as t2
} from "./chunk-DUEDINK5.js";
import {
  e as e3
} from "./chunk-MZ267CZB.js";
import {
  r as r8
} from "./chunk-QCTKOQ44.js";
import {
  M,
  f as f2,
  h as h3,
  i,
  r as r5
} from "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-THUK4WUF.js";
import "./chunk-5ZZCQR67.js";
import {
  e as e2
} from "./chunk-A7PY25IH.js";
import "./chunk-RURSJOSG.js";
import "./chunk-OYGWWPGZ.js";
import "./chunk-77E52HT5.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import {
  r as r4
} from "./chunk-SROTSYJS.js";
import {
  n
} from "./chunk-FOE4ICAJ.js";
import "./chunk-TNP2LXZZ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import {
  U
} from "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import "./chunk-G3QAWKCD.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-WL6G2MRC.js";
import "./chunk-UCWK623G.js";
import "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-BS3GJQ77.js";
import "./chunk-IOBN373Z.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-NEJXVYTI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import {
  a as a2,
  f,
  h as h2,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-ZL6CFFJK.js";
import "./chunk-TWFTBWXP.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import {
  u
} from "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j as j2
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  s as s3,
  w,
  x
} from "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  R
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import {
  c as c2
} from "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import {
  c
} from "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  r as r2
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  j,
  r2 as r3
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  h,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/engine/webgl/Overlay.js
var b = e2();
var A = class extends r9 {
  constructor(s4) {
    super(), this.elementView = s4, this.isWrapAround = false, this.perspectiveTransform = n(), this._vertices = new Float32Array(20), this._handles = [], this._handles.push(l(() => this.elementView.element.opacity, (e4) => this.opacity = e4, h2), l(() => [this.elementView.coords], () => {
      this.requestRender();
    }, h2), f(() => this.elementView.element.loaded, () => {
      const e4 = this.elementView.element;
      this.ready(), "video" === e4.type && r(e4.content) && this._handles.push(r3(e4.content, "play", () => this.requestRender()));
    }, h2)), s4.element.load().catch((t3) => {
      s.getLogger("esri.views.2d.layers.MediaLayerView2D").error(new s2("element-load-error", "Element cannot be displayed", { element: s4, error: t3 }));
    });
  }
  destroy() {
    this._handles.forEach((e4) => e4.remove()), this.texture = h(this.texture);
  }
  get dvsMat3() {
    return this.parent.dvsMat3;
  }
  beforeRender(e4) {
    const { context: t3 } = e4, r10 = this.elementView.element.content;
    if (r(r10)) {
      const e5 = r10 instanceof HTMLImageElement, i2 = r10 instanceof HTMLVideoElement, o = e5 ? r10.naturalWidth : i2 ? r10.videoWidth : r10.width, n2 = e5 ? r10.naturalHeight : i2 ? r10.videoHeight : r10.height;
      this._updatePerspectiveTransform(o, n2), this.texture ? i2 && !r10.paused && (this.texture.setData(r10), this.requestRender(), (t3.type === r8.WEBGL2 || c(o) && c(n2)) && this.texture.generateMipmap()) : (this.texture = new E(t3, { pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, samplingMode: L.LINEAR, wrapMode: D.CLAMP_TO_EDGE, width: o, height: n2, preMultiplyAlpha: true }, r10), (t3.type === r8.WEBGL2 || c(o) && c(n2)) && this.texture.generateMipmap(), i2 && !r10.paused && this.requestRender());
    }
    super.beforeRender(e4);
  }
  _createTransforms() {
    return null;
  }
  updateDrawCoords(e4, t3) {
    const r10 = this.elementView.coords;
    if (t(r10)) return;
    const [s4, i2, o, a4] = r10.rings[0], m = this._vertices, { x: h4, y: c3 } = e4, p = 0 !== t3;
    p ? m.set([i2[0] - h4, i2[1] - c3, s4[0] - h4, s4[1] - c3, o[0] - h4, o[1] - c3, a4[0] - h4, a4[1] - c3, a4[0] - h4, a4[1] - c3, i2[0] + t3 - h4, i2[1] - c3, i2[0] + t3 - h4, i2[1] - c3, s4[0] + t3 - h4, s4[1] - c3, o[0] + t3 - h4, o[1] - c3, a4[0] + t3 - h4, a4[1] - c3]) : m.set([i2[0] - h4, i2[1] - c3, s4[0] - h4, s4[1] - c3, o[0] - h4, o[1] - c3, a4[0] - h4, a4[1] - c3]), this.isWrapAround = p;
  }
  getVAO(e4, t3, r10) {
    if (t(this.elementView.coords)) return null;
    const s4 = this._vertices;
    if (this._vao) this._geometryVbo.setData(s4);
    else {
      this._geometryVbo = E2.createVertex(e4, F.DYNAMIC_DRAW, s4);
      const i2 = E2.createVertex(e4, F.STATIC_DRAW, new Uint16Array([0, 0, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1]));
      this._vao = new f3(e4, r10, t3, { geometry: this._geometryVbo, tex: i2 });
    }
    return this._vao;
  }
  _updatePerspectiveTransform(e4, t3) {
    const r10 = this._vertices;
    j3(b, [0, 0, e4, 0, 0, t3, e4, t3], [r10[0], r10[1], r10[4], r10[5], r10[2], r10[3], r10[6], r10[7]]), r4(this.perspectiveTransform, b[6] / b[8] * e4, b[7] / b[8] * t3);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/OverlayContainer.js
var M2 = class extends a3 {
  constructor() {
    super(...arguments), this._localOrigin = c2(0, 0), this._viewStateId = -1, this._dvsMat3 = e3(), this.requiresDedicatedFBO = false;
  }
  get dvsMat3() {
    return this._dvsMat3;
  }
  beforeRender(t3) {
    this._updateMatrices(t3), this._updateOverlays(t3, this.children);
    for (const e4 of this.children) e4.beforeRender(t3);
  }
  prepareRenderPasses(t3) {
    const e4 = t3.registerRenderPass({ name: "overlay", brushes: [w2.overlay], target: () => this.children, drawPhase: T.MAP });
    return [...super.prepareRenderPasses(t3), e4];
  }
  _updateMatrices(t3) {
    const { state: e4 } = t3, { id: n2, size: h4, pixelRatio: l2, resolution: m, rotation: f5, viewpoint: u4, displayMat3: M3 } = e4;
    if (this._viewStateId === n2) return;
    const v2 = Math.PI / 180 * f5, _2 = l2 * h4[0], w3 = l2 * h4[1], { x: y3, y: g } = u4.targetGeometry, j4 = U(y3, e4.spatialReference);
    this._localOrigin.x = j4, this._localOrigin.y = g;
    const b2 = m * _2, R3 = m * w3, O = r5(this._dvsMat3);
    i(O, O, M3), M(O, O, t2(_2 / 2, w3 / 2)), f2(O, O, r7(_2 / b2, -w3 / R3, 1)), h3(O, O, -v2), this._viewStateId = n2;
  }
  _updateOverlays(e4, s4) {
    const { state: r10 } = e4, { rotation: o, spatialReference: a4, worldScreenWidth: i2, size: n2, viewpoint: c3 } = r10, p = this._localOrigin;
    let d = 0;
    const m = R(a4);
    if (m && a4.isWrappable) {
      const e5 = n2[0], h4 = n2[1], f5 = 180 / Math.PI * o, u4 = Math.abs(Math.cos(f5)), M3 = Math.abs(Math.sin(f5)), v2 = Math.round(e5 * u4 + h4 * M3), [_2, w3] = m.valid, y3 = mt(a4), { x: g, y: j4 } = c3.targetGeometry, b2 = [g, j4], R3 = [0, 0];
      r10.toScreen(R3, b2);
      const O = [0, 0];
      let P2;
      P2 = v2 > i2 ? 0.5 * i2 : 0.5 * v2;
      const x2 = Math.floor((g + 0.5 * y3) / y3), C = _2 + x2 * y3, D2 = w3 + x2 * y3, I = [R3[0] + P2, 0];
      r10.toMap(O, I), O[0] > D2 && (d = y3), I[0] = R3[0] - P2, r10.toMap(O, I), O[0] < C && (d = -y3);
      for (const r11 of s4) {
        const e6 = r11.elementView.bounds;
        if (t(e6)) continue;
        const [s5, , o2] = e6;
        s5 < _2 && o2 > _2 ? r11.updateDrawCoords(p, y3) : o2 > w3 && s5 < w3 ? r11.updateDrawCoords(p, -y3) : r11.updateDrawCoords(p, d);
      }
    } else for (const t3 of s4) t3.updateDrawCoords(p, d);
  }
};

// node_modules/@arcgis/core/views/2d/layers/MediaLayerView2D.js
var _ = class extends f4(u3) {
  constructor() {
    super(...arguments), this._overlayContainer = null, this._fetchQueue = null, this._tileStrategy = null, this._elementReferences = /* @__PURE__ */ new Map(), this._debugGraphicsView = null, this.layer = null, this.elements = new j2();
  }
  attach() {
    this.addAttachHandles([a2(() => this.layer.effectiveSource, "refresh", () => {
      for (const e4 of this._tileStrategy.tiles) this._updateTile(e4);
      this.requestUpdate();
    }), a2(() => this.layer.effectiveSource, "change", ({ element: e4 }) => this._elementUpdateHandler(e4))]), this._overlayContainer = new M2(), this.container.addChild(this._overlayContainer), this._fetchQueue = new y2({ tileInfoView: this.view.featuresTilingScheme, concurrency: 10, process: (e4, t3) => this._queryElements(e4, t3) }), this._tileStrategy = new r6({ cachePolicy: "purge", resampling: true, acquireTile: (e4) => this._acquireTile(e4), releaseTile: (e4) => this._releaseTile(e4), tileInfoView: this.view.featuresTilingScheme }), this.requestUpdate();
  }
  detach() {
    var _a;
    this.elements.removeAll(), this._tileStrategy.destroy(), this._fetchQueue.destroy(), this._overlayContainer.removeAllChildren(), this.container.removeAllChildren(), this._elementReferences.clear(), (_a = this._debugGraphicsView) == null ? void 0 : _a.destroy();
  }
  supportsSpatialReference(e4) {
    return true;
  }
  moveStart() {
    this.requestUpdate();
  }
  viewChange() {
    this.requestUpdate();
  }
  moveEnd() {
    this.requestUpdate();
  }
  update(e4) {
    var _a;
    this._tileStrategy.update(e4), (_a = this._debugGraphicsView) == null ? void 0 : _a.update(e4);
  }
  async hitTest(e4, t3) {
    const r10 = [], s4 = e4.normalize(), n2 = [s4.x, s4.y];
    for (const { projectedElement: { normalizedCoords: o, element: l2 } } of this._elementReferences.values()) r(o) && s3(o.rings, n2) && r10.push({ type: "media", element: l2, layer: this.layer, mapPoint: e4 });
    return r10.reverse();
  }
  canResume() {
    return null != this.layer.source && super.canResume();
  }
  async doRefresh() {
    this._fetchQueue.reset(), this._tileStrategy.tiles.forEach((e4) => this._updateTile(e4));
  }
  _acquireTile(e4) {
    const t3 = new E3(e4.clone());
    return this._updateTile(t3), t3;
  }
  _updateTile(e4) {
    this.updatingHandles.addPromise(this._fetchQueue.push(e4.key).then((t3) => {
      const [r10, s4] = e4.setElements(t3);
      this._referenceElements(e4, r10), this._dereferenceElements(e4, s4), this.requestUpdate();
    }, (e5) => {
      j(e5) || s.getLogger(this.declaredClass).error(e5);
    }));
  }
  _releaseTile(e4) {
    this._fetchQueue.abort(e4.key.id), e4.elements && this._dereferenceElements(e4, e4.elements), this.requestUpdate();
  }
  async _queryElements(e4, t3) {
    const r10 = this.layer.effectiveSource;
    if (t(r10)) return [];
    this.view.featuresTilingScheme.getTileBounds(v, e4, true);
    const s4 = new w({ xmin: v[0], ymin: v[1], xmax: v[2], ymax: v[3], spatialReference: this.view.spatialReference });
    return r10.queryElements(s4, t3);
  }
  _referenceElements(e4, t3) {
    const r10 = this.layer.source;
    if (!t(r10)) for (const s4 of t3) this._referenceElement(e4, s4);
  }
  _referenceElement(e4, t3) {
    r2(this._elementReferences, t3.uid, () => {
      const e5 = new u2({ element: t3, spatialReference: this.view.spatialReference }), r10 = new A(e5);
      this._overlayContainer.addChild(r10), this.elements.add(t3);
      let s4 = null;
      return { tiles: /* @__PURE__ */ new Set(), projectedElement: e5, overlay: r10, debugGraphic: s4 };
    }).tiles.add(e4);
  }
  _dereferenceElements(e4, t3) {
    for (const r10 of t3) this._dereferenceElement(e4, r10);
  }
  _dereferenceElement(e4, t3) {
    var _a;
    const r10 = this._elementReferences.get(t3.uid);
    r10.tiles.delete(e4), r10.tiles.size || (this._overlayContainer.removeChild(r10.overlay), r10.overlay.destroy(), r10.projectedElement.destroy(), this._elementReferences.delete(t3.uid), this.elements.remove(t3), (_a = this._debugGraphicsView) == null ? void 0 : _a.graphics.remove(r10.debugGraphic));
  }
  _elementUpdateHandler(e4) {
    var _a;
    let t3 = this._elementReferences.get(e4.uid);
    if (t3) {
      const r11 = t3.projectedElement.normalizedCoords;
      if (t(r11)) return this._overlayContainer.removeChild(t3.overlay), t3.overlay.destroy(), t3.projectedElement.destroy(), this._elementReferences.delete(e4.uid), this.elements.remove(e4), void ((_a = this._debugGraphicsView) == null ? void 0 : _a.graphics.remove(t3.debugGraphic));
      const s4 = [], i2 = [];
      for (const e5 of this._tileStrategy.tiles) {
        const n2 = R2(this.view.featuresTilingScheme, e5, r11);
        t3.tiles.has(e5) ? n2 || i2.push(e5) : n2 && s4.push(e5);
      }
      for (const t4 of s4) this._referenceElement(t4, e4);
      for (const t4 of i2) this._dereferenceElement(t4, e4);
      return t3 = this._elementReferences.get(e4.uid), void ((t3 == null ? void 0 : t3.debugGraphic) && (t3.debugGraphic.geometry = t3.projectedElement.normalizedCoords, this._debugGraphicsView.graphicUpdateHandler({ graphic: t3.debugGraphic, property: "geometry" })));
    }
    const r10 = new u2({ element: e4, spatialReference: this.view.spatialReference }).normalizedCoords;
    if (r(r10)) for (const s4 of this._tileStrategy.tiles) {
      R2(this.view.featuresTilingScheme, s4, r10) && this._referenceElement(s4, e4);
    }
  }
};
e([y()], _.prototype, "_fetchQueue", void 0), e([y()], _.prototype, "layer", void 0), e([y({ readOnly: true })], _.prototype, "elements", void 0), _ = e([a("esri.views.2d.layers.MediaLayerView2D")], _);
var v = u();
var T2 = { xmin: 0, ymin: 0, xmax: 0, ymax: 0 };
function R2(e4, t3, r10) {
  return e4.getTileBounds(v, t3.key, true), T2.xmin = v[0], T2.ymin = v[1], T2.xmax = v[2], T2.ymax = v[3], x(T2, r10);
}
var E3 = class {
  constructor(e4) {
    this.key = e4, this.elements = null, this.isReady = false, this.visible = true;
  }
  setElements(e4) {
    const t3 = [], r10 = new Set(this.elements);
    this.elements = e4;
    for (const s4 of e4) r10.has(s4) ? r10.delete(s4) : t3.push(s4);
    return this.isReady = true, [t3, Array.from(r10)];
  }
  destroy() {
  }
};
var S = _;
export {
  S as default
};
//# sourceMappingURL=MediaLayerView2D-MXZT5EI4.js.map
