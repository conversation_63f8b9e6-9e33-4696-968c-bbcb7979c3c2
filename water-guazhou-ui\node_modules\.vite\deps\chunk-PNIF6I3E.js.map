{"version": 3, "sources": ["../../@arcgis/core/symbols/support/symbolConversion.js", "../../@arcgis/core/symbols/support/jsonUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSymbol3D as o}from\"../../symbols.js\";import e from\"../../core/Error.js\";import r from\"../WebStyleSymbol.js\";import m from\"../PointSymbol3D.js\";import i from\"../SimpleLineSymbol.js\";import l from\"../LineSymbol3D.js\";import t from\"../SimpleMarkerSymbol.js\";import s from\"../PictureMarkerSymbol.js\";import n from\"../SimpleFillSymbol.js\";import y from\"../MeshSymbol3D.js\";import f from\"../PolygonSymbol3D.js\";import b from\"../TextSymbol.js\";import p from\"../LabelSymbol3D.js\";const a={retainId:!1,ignoreDrivers:!1,hasLabelingContext:!0};function S(S,c=a){if(!S)return{symbol:null};const{retainId:u=a.retainId,ignoreDrivers:d=a.ignoreDrivers,hasLabelingContext:j=a.hasLabelingContext,retainCIM:g=a.retainCIM}=c;let D=null;if(o(S)||S instanceof r)D=S.clone();else if(\"cim\"===S.type){const o=S.data?.symbol?.type;if(\"CIMPointSymbol\"!==o)return{error:new e(\"symbol-conversion:unsupported-cim-symbol\",`CIM symbol of type '${o||\"unknown\"}' is unsupported in 3D`,{symbol:S})};D=g?S.clone():m.fromCIMSymbol(S)}else if(S instanceof i)D=l.fromSimpleLineSymbol(S);else if(S instanceof t)D=m.fromSimpleMarkerSymbol(S);else if(S instanceof s)D=m.fromPictureMarkerSymbol(S);else if(S instanceof n)D=c.geometryType&&\"mesh\"===c.geometryType?y.fromSimpleFillSymbol(S):f.fromSimpleFillSymbol(S);else{if(!(S instanceof b))return{error:new e(\"symbol-conversion:unsupported-2d-symbol\",`2D symbol of type '${S.type||S.declaredClass}' is unsupported in 3D`,{symbol:S})};D=j?p.fromTextSymbol(S):m.fromTextSymbol(S)}if(u&&D&&\"cim\"!==D.type&&(D.id=S.id),d&&o(D))for(let o=0;o<D.symbolLayers.length;++o)D.symbolLayers.getItemAt(o)._ignoreDrivers=!0;return{symbol:D}}export{a as defaultTo3DOptions,S as to3D};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{readSymbol as o}from\"../../symbols.js\";import e from\"../../core/Error.js\";import{isSome as n,isNone as t}from\"../../core/maybe.js\";import{isSceneServiceLayer as r}from\"../../layers/support/layerUtils.js\";import s from\"../Symbol3D.js\";import{to3D as l}from\"./symbolConversion.js\";import i from\"../WebStyleSymbol.js\";function m(o,e,t,r){const s=p(o,{},{context:r,isLabelSymbol:!1});n(s)&&(e[t]=s)}function a(o,e,t,r){const s=p(o,{},{context:r,isLabelSymbol:!0});n(s)&&(e[t]=s)}function b(o){return o instanceof s||o instanceof i}function p(o,s,i){if(t(o))return null;const{context:m,isLabelSymbol:a}=i,p=m?.origin,y=m?.messages;if(\"web-scene\"===p&&!b(o)){const t=l(o,{retainCIM:!0,hasLabelingContext:a});return n(t.symbol)?t.symbol.write(s,m):(y?.push(new e(\"symbol:unsupported\",`Symbols of type '${o.declaredClass}' are not supported in scenes. Use 3D symbology instead when working with WebScene and SceneView`,{symbol:o,context:m,error:t.error})),null)}return(\"web-map\"===p||\"portal-item\"===p&&!r(m?.layer))&&b(o)?(y?.push(new e(\"symbol:unsupported\",`Symbols of type '${o.declaredClass}' are not supported in web maps and portal items. Use 2D symbology and CIMSymbol instead when working with MapView`,{symbol:o,context:m})),null):o.write(s,m)}function y(e,n){return o(e,null,n)}export{y as fromJSON,m as write,a as writeLabelSymbol};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIie,IAAM,IAAE,EAAC,UAAS,OAAG,eAAc,OAAG,oBAAmB,KAAE;AAAE,SAASA,GAAEA,IAAEC,KAAE,GAAE;AAJ/iB;AAIgjB,MAAG,CAACD,GAAE,QAAM,EAAC,QAAO,KAAI;AAAE,QAAK,EAAC,UAASE,KAAE,EAAE,UAAS,eAAc,IAAE,EAAE,eAAc,oBAAmB,IAAE,EAAE,oBAAmB,WAAU,IAAE,EAAE,UAAS,IAAED;AAAE,MAAI,IAAE;AAAK,MAAG,EAAED,EAAC,KAAGA,cAAa,EAAE,KAAEA,GAAE,MAAM;AAAA,WAAU,UAAQA,GAAE,MAAK;AAAC,UAAM,KAAE,WAAAA,GAAE,SAAF,mBAAQ,WAAR,mBAAgB;AAAK,QAAG,qBAAmB,EAAE,QAAM,EAAC,OAAM,IAAI,EAAE,4CAA2C,uBAAuB,KAAG,SAAS,0BAAyB,EAAC,QAAOA,GAAC,CAAC,EAAC;AAAE,QAAE,IAAEA,GAAE,MAAM,IAAE,EAAE,cAAcA,EAAC;AAAA,EAAC,WAASA,cAAa,EAAE,KAAEG,GAAE,qBAAqBH,EAAC;AAAA,WAAUA,cAAa,EAAE,KAAE,EAAE,uBAAuBA,EAAC;AAAA,WAAUA,cAAa,EAAE,KAAE,EAAE,wBAAwBA,EAAC;AAAA,WAAUA,cAAaA,GAAE,KAAEC,GAAE,gBAAc,WAASA,GAAE,eAAa,EAAE,qBAAqBD,EAAC,IAAE,EAAE,qBAAqBA,EAAC;AAAA,OAAM;AAAC,QAAG,EAAEA,cAAaI,IAAG,QAAM,EAAC,OAAM,IAAI,EAAE,2CAA0C,sBAAsBJ,GAAE,QAAMA,GAAE,aAAa,0BAAyB,EAAC,QAAOA,GAAC,CAAC,EAAC;AAAE,QAAE,IAAE,EAAE,eAAeA,EAAC,IAAE,EAAE,eAAeA,EAAC;AAAA,EAAC;AAAC,MAAGE,MAAG,KAAG,UAAQ,EAAE,SAAO,EAAE,KAAGF,GAAE,KAAI,KAAG,EAAE,CAAC,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,aAAa,QAAO,EAAE,EAAE,GAAE,aAAa,UAAU,CAAC,EAAE,iBAAe;AAAG,SAAM,EAAC,QAAO,EAAC;AAAC;;;ACAzyC,SAASK,GAAE,GAAE,GAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,GAAE,CAAC,GAAE,EAAC,SAAQD,IAAE,eAAc,MAAE,CAAC;AAAE,IAAEC,EAAC,MAAI,EAAEF,EAAC,IAAEE;AAAE;AAAC,SAASC,GAAE,GAAE,GAAEH,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,GAAE,CAAC,GAAE,EAAC,SAAQD,IAAE,eAAc,KAAE,CAAC;AAAE,IAAEC,EAAC,MAAI,EAAEF,EAAC,IAAEE;AAAE;AAAC,SAASE,GAAE,GAAE;AAAC,SAAO,aAAa,KAAG,aAAa;AAAC;AAAC,SAAS,EAAE,GAAEF,IAAE,GAAE;AAAC,MAAG,EAAE,CAAC,EAAE,QAAO;AAAK,QAAK,EAAC,SAAQH,IAAE,eAAcI,GAAC,IAAE,GAAEE,KAAEN,MAAA,gBAAAA,GAAG,QAAOO,KAAEP,MAAA,gBAAAA,GAAG;AAAS,MAAG,gBAAcM,MAAG,CAACD,GAAE,CAAC,GAAE;AAAC,UAAMJ,KAAEO,GAAE,GAAE,EAAC,WAAU,MAAG,oBAAmBJ,GAAC,CAAC;AAAE,WAAO,EAAEH,GAAE,MAAM,IAAEA,GAAE,OAAO,MAAME,IAAEH,EAAC,KAAGO,MAAA,gBAAAA,GAAG,KAAK,IAAI,EAAE,sBAAqB,oBAAoB,EAAE,aAAa,oGAAmG,EAAC,QAAO,GAAE,SAAQP,IAAE,OAAMC,GAAE,MAAK,CAAC,IAAG;AAAA,EAAK;AAAC,UAAO,cAAYK,MAAG,kBAAgBA,MAAG,CAAC,EAAEN,MAAA,gBAAAA,GAAG,KAAK,MAAIK,GAAE,CAAC,KAAGE,MAAA,gBAAAA,GAAG,KAAK,IAAI,EAAE,sBAAqB,oBAAoB,EAAE,aAAa,sHAAqH,EAAC,QAAO,GAAE,SAAQP,GAAC,CAAC,IAAG,QAAM,EAAE,MAAMG,IAAEH,EAAC;AAAC;AAAC,SAASO,GAAE,GAAEE,IAAE;AAAC,SAAOC,GAAE,GAAE,MAAKD,EAAC;AAAC;", "names": ["S", "c", "u", "b", "m", "m", "t", "r", "s", "a", "b", "p", "y", "S", "n", "k"]}