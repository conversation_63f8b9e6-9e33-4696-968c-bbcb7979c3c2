{"version": 3, "sources": ["../../@arcgis/core/portal/support/portalLayers.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Error.js\";import{sceneServiceLayerTypeToClassName as r}from\"../../layers/support/layerUtils.js\";import{layerLookupMap as a}from\"../../layers/support/lazyLayerLoader.js\";import t from\"../PortalItem.js\";import{getNumLayersAndTables as s,preprocessFSItemData as n,getSubtypeGroupLayerIds as c,getFirstLayerOrTableId as o}from\"./layersLoader.js\";import{hasTypeKeyword as i}from\"./portalItemUtils.js\";import{fetchArcGISServiceJSON as u}from\"../../support/requestPresets.js\";async function l(e){!e.portalItem||e.portalItem instanceof t||(e={...e,portalItem:new t(e.portalItem)});const r=await y(e.portalItem);return new(0,r.constructor)({portalItem:e.portalItem,...r.properties})}async function y(e){await e.load();return p(await m(e))}async function m(r){switch(r.type){case\"Map Service\":return f(r);case\"Feature Service\":return L(r);case\"Feature Collection\":return w(r);case\"Scene Service\":return N(r);case\"Image Service\":return d(r);case\"Stream Service\":return S();case\"Vector Tile Service\":return I();case\"GeoJson\":return g();case\"CSV\":return T();case\"KML\":return v();case\"WFS\":return j();case\"WMTS\":return G();case\"WMS\":return M();case\"Feed\":return h();default:throw new e(\"portal:unknown-item-type\",\"Unknown item type '${type}'\",{type:r.type})}}async function p(e){const r=a[e.className];return{constructor:await r(),properties:e.properties}}async function f(e){return await b(e)?{className:\"TileLayer\"}:{className:\"MapImageLayer\"}}async function L(e){if(i(e,\"Oriented Imagery Layer\"))return F(e);const r=await C(e);if(\"object\"==typeof r){const e={};return null!=r.id&&(e.layerId=r.id),{className:r.className||\"FeatureLayer\",properties:e}}return{className:\"GroupLayer\"}}async function N(e){const a=await C(e);if(\"object\"==typeof a){const t={};let s;if(null!=a.id?(t.layerId=a.id,s=`${e.url}/layers/${a.id}`):s=e.url,e.typeKeywords?.length)for(const a of Object.keys(r))if(e.typeKeywords.includes(a))return{className:r[a]};const n=await u(s);return{className:r[n?.layerType]||\"SceneLayer\",properties:t}}if(!1===a){return\"Voxel\"===(await u(e.url))?.layerType?{className:\"VoxelLayer\"}:{className:\"GroupLayer\"}}return{className:\"GroupLayer\"}}async function w(e){await e.load();const r=i(e,\"Map Notes\"),a=i(e,\"Markup\");if(r||a)return{className:\"MapNotesLayer\"};if(i(e,\"Route Layer\"))return{className:\"RouteLayer\"};const t=await e.fetchData();return 1===s(t)?{className:\"FeatureLayer\"}:{className:\"GroupLayer\"}}async function d(e){await e.load();const r=e.typeKeywords?.map((e=>e.toLowerCase()))??[];if(r.includes(\"elevation 3d layer\"))return{className:\"ElevationLayer\"};if(r.includes(\"tiled imagery\"))return{className:\"ImageryTileLayer\"};const a=(await e.fetchData())?.layerType;if(\"ArcGISTiledImageServiceLayer\"===a)return{className:\"ImageryTileLayer\"};if(\"ArcGISImageServiceLayer\"===a)return{className:\"ImageryLayer\"};const t=await u(e.url),s=t.cacheType?.toLowerCase(),n=t.capabilities?.toLowerCase().includes(\"tilesonly\");return\"map\"===s||n?{className:\"ImageryTileLayer\"}:{className:\"ImageryLayer\"}}function S(){return{className:\"StreamLayer\"}}function I(){return{className:\"VectorTileLayer\"}}function g(){return{className:\"GeoJSONLayer\"}}function T(){return{className:\"CSVLayer\"}}function v(){return{className:\"KMLLayer\"}}function j(){return{className:\"WFSLayer\"}}function M(){return{className:\"WMSLayer\"}}function G(){return{className:\"WMTSLayer\"}}function h(){return{className:\"StreamLayer\"}}async function F(e){await e.load();const r=await e.fetchData();return r.coverage?{className:\"GroupLayer\"}:{className:\"OrientedImageryLayer\",properties:r}}async function b(e){return(await u(e.url)).tileInfo}async function C(e){const r=e.url;if(!r||r.match(/\\/\\d+$/))return{};await e.load();const a=await e.fetchData();if(\"Feature Service\"===e.type){const e=V(await n(a,r));if(\"object\"==typeof e){const r=c(a);e.className=null!=e.id&&r.includes(e.id)?\"SubtypeGroupLayer\":\"FeatureLayer\"}return e}if(s(a)>0)return V(a);return V(await u(r))}function V(e){return 1===s(e)&&{id:o(e)}}export{l as fromItem,m as selectLayerClassPath};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAI8e,eAAe,EAAE,GAAE;AAAC,GAAC,EAAE,cAAY,EAAE,sBAAsB,MAAI,IAAE,EAAC,GAAG,GAAE,YAAW,IAAI,EAAE,EAAE,UAAU,EAAC;AAAG,QAAM,IAAE,MAAM,EAAE,EAAE,UAAU;AAAE,SAAO,KAAI,GAAE,EAAE,aAAa,EAAC,YAAW,EAAE,YAAW,GAAG,EAAE,WAAU,CAAC;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,QAAM,EAAE,KAAK;AAAE,SAAO,EAAE,MAAM,EAAE,CAAC,CAAC;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,UAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAc,aAAO,EAAE,CAAC;AAAA,IAAE,KAAI;AAAkB,aAAO,EAAE,CAAC;AAAA,IAAE,KAAI;AAAqB,aAAOA,GAAE,CAAC;AAAA,IAAE,KAAI;AAAgB,aAAO,EAAE,CAAC;AAAA,IAAE,KAAI;AAAgB,aAAO,EAAE,CAAC;AAAA,IAAE,KAAI;AAAiB,aAAO,EAAE;AAAA,IAAE,KAAI;AAAsB,aAAOC,GAAE;AAAA,IAAE,KAAI;AAAU,aAAO,EAAE;AAAA,IAAE,KAAI;AAAM,aAAO,EAAE;AAAA,IAAE,KAAI;AAAM,aAAOC,GAAE;AAAA,IAAE,KAAI;AAAM,aAAOC,GAAE;AAAA,IAAE,KAAI;AAAO,aAAO,EAAE;AAAA,IAAE,KAAI;AAAM,aAAO,EAAE;AAAA,IAAE,KAAI;AAAO,aAAO,EAAE;AAAA,IAAE;AAAQ,YAAM,IAAI,EAAE,4BAA2B,+BAA8B,EAAC,MAAK,EAAE,KAAI,CAAC;AAAA,EAAC;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,QAAM,IAAEC,GAAE,EAAE,SAAS;AAAE,SAAM,EAAC,aAAY,MAAM,EAAE,GAAE,YAAW,EAAE,WAAU;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,SAAO,MAAM,EAAE,CAAC,IAAE,EAAC,WAAU,YAAW,IAAE,EAAC,WAAU,gBAAe;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,MAAGC,GAAE,GAAE,wBAAwB,EAAE,QAAO,EAAE,CAAC;AAAE,QAAM,IAAE,MAAM,EAAE,CAAC;AAAE,MAAG,YAAU,OAAO,GAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,WAAO,QAAM,EAAE,OAAKA,GAAE,UAAQ,EAAE,KAAI,EAAC,WAAU,EAAE,aAAW,gBAAe,YAAWA,GAAC;AAAA,EAAC;AAAC,SAAM,EAAC,WAAU,aAAY;AAAC;AAAC,eAAe,EAAE,GAAE;AAJlrD;AAImrD,QAAMF,KAAE,MAAM,EAAE,CAAC;AAAE,MAAG,YAAU,OAAOA,IAAE;AAAC,UAAMG,KAAE,CAAC;AAAE,QAAIF;AAAE,QAAG,QAAMD,GAAE,MAAIG,GAAE,UAAQH,GAAE,IAAGC,KAAE,GAAG,EAAE,GAAG,WAAWD,GAAE,EAAE,MAAIC,KAAE,EAAE,MAAI,OAAE,iBAAF,mBAAgB;AAAO,iBAAUD,MAAK,OAAO,KAAK,CAAC,EAAE,KAAG,EAAE,aAAa,SAASA,EAAC,EAAE,QAAM,EAAC,WAAU,EAAEA,EAAC,EAAC;AAAA;AAAE,UAAM,IAAE,MAAM,EAAEC,EAAC;AAAE,WAAM,EAAC,WAAU,EAAE,uBAAG,SAAS,KAAG,cAAa,YAAWE,GAAC;AAAA,EAAC;AAAC,MAAG,UAAKH,IAAE;AAAC,WAAM,cAAW,WAAM,EAAE,EAAE,GAAG,MAAb,mBAAiB,aAAU,EAAC,WAAU,aAAY,IAAE,EAAC,WAAU,aAAY;AAAA,EAAC;AAAC,SAAM,EAAC,WAAU,aAAY;AAAC;AAAC,eAAeJ,GAAE,GAAE;AAAC,QAAM,EAAE,KAAK;AAAE,QAAM,IAAEK,GAAE,GAAE,WAAW,GAAED,KAAEC,GAAE,GAAE,QAAQ;AAAE,MAAG,KAAGD,GAAE,QAAM,EAAC,WAAU,gBAAe;AAAE,MAAGC,GAAE,GAAE,aAAa,EAAE,QAAM,EAAC,WAAU,aAAY;AAAE,QAAME,KAAE,MAAM,EAAE,UAAU;AAAE,SAAO,MAAI,EAAEA,EAAC,IAAE,EAAC,WAAU,eAAc,IAAE,EAAC,WAAU,aAAY;AAAC;AAAC,eAAe,EAAE,GAAE;AAJj5E;AAIk5E,QAAM,EAAE,KAAK;AAAE,QAAM,MAAE,OAAE,iBAAF,mBAAgB,IAAK,CAAAD,OAAGA,GAAE,YAAY,OAAK,CAAC;AAAE,MAAG,EAAE,SAAS,oBAAoB,EAAE,QAAM,EAAC,WAAU,iBAAgB;AAAE,MAAG,EAAE,SAAS,eAAe,EAAE,QAAM,EAAC,WAAU,mBAAkB;AAAE,QAAMF,MAAG,WAAM,EAAE,UAAU,MAAlB,mBAAsB;AAAU,MAAG,mCAAiCA,GAAE,QAAM,EAAC,WAAU,mBAAkB;AAAE,MAAG,8BAA4BA,GAAE,QAAM,EAAC,WAAU,eAAc;AAAE,QAAMG,KAAE,MAAM,EAAE,EAAE,GAAG,GAAEF,MAAE,KAAAE,GAAE,cAAF,mBAAa,eAAc,KAAE,KAAAA,GAAE,iBAAF,mBAAgB,cAAc,SAAS;AAAa,SAAM,UAAQF,MAAG,IAAE,EAAC,WAAU,mBAAkB,IAAE,EAAC,WAAU,eAAc;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,EAAC,WAAU,cAAa;AAAC;AAAC,SAASJ,KAAG;AAAC,SAAM,EAAC,WAAU,kBAAiB;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,EAAC,WAAU,eAAc;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,EAAC,WAAU,WAAU;AAAC;AAAC,SAASC,KAAG;AAAC,SAAM,EAAC,WAAU,WAAU;AAAC;AAAC,SAASC,KAAG;AAAC,SAAM,EAAC,WAAU,WAAU;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,EAAC,WAAU,WAAU;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,EAAC,WAAU,YAAW;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,EAAC,WAAU,cAAa;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,QAAM,EAAE,KAAK;AAAE,QAAM,IAAE,MAAM,EAAE,UAAU;AAAE,SAAO,EAAE,WAAS,EAAC,WAAU,aAAY,IAAE,EAAC,WAAU,wBAAuB,YAAW,EAAC;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,UAAO,MAAM,EAAE,EAAE,GAAG,GAAG;AAAQ;AAAC,eAAe,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE;AAAI,MAAG,CAAC,KAAG,EAAE,MAAM,QAAQ,EAAE,QAAM,CAAC;AAAE,QAAM,EAAE,KAAK;AAAE,QAAMC,KAAE,MAAM,EAAE,UAAU;AAAE,MAAG,sBAAoB,EAAE,MAAK;AAAC,UAAME,KAAE,EAAE,MAAM,EAAEF,IAAE,CAAC,CAAC;AAAE,QAAG,YAAU,OAAOE,IAAE;AAAC,YAAME,KAAE,EAAEJ,EAAC;AAAE,MAAAE,GAAE,YAAU,QAAMA,GAAE,MAAIE,GAAE,SAASF,GAAE,EAAE,IAAE,sBAAoB;AAAA,IAAc;AAAC,WAAOA;AAAA,EAAC;AAAC,MAAG,EAAEF,EAAC,IAAE,EAAE,QAAO,EAAEA,EAAC;AAAE,SAAO,EAAE,MAAM,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,MAAI,EAAE,CAAC,KAAG,EAAC,IAAG,EAAE,CAAC,EAAC;AAAC;", "names": ["w", "I", "v", "j", "a", "s", "e", "t", "r"]}