import {
  p
} from "./chunk-22HLMDJ5.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-4RZONHOY.js";

// node_modules/@arcgis/core/portal/PortalFolder.js
var p2 = class extends l {
  constructor(t) {
    super(t), this.created = null, this.id = null, this.portal = null, this.title = null, this.username = null;
  }
  get url() {
    const t = this.get("portal.restUrl");
    return t ? `${t}/content/users/${this.username}/${this.id}` : null;
  }
  toJSON() {
    throw new s("internal:not-yet-implemented", "PortalFolder.toJSON is not yet implemented");
  }
};
e([y({ type: Date })], p2.prototype, "created", void 0), e([y()], p2.prototype, "id", void 0), e([y()], p2.prototype, "portal", void 0), e([y()], p2.prototype, "title", void 0), e([y({ readOnly: true })], p2.prototype, "url", null), e([y()], p2.prototype, "username", void 0), p2 = e([a("esri.portal.PortalFolder")], p2);
var l2 = p2;

// node_modules/@arcgis/core/portal/PortalUser.js
var i;
var u = i = class extends l {
  constructor(...t) {
    super(...t), this.access = null, this.created = null, this.culture = null, this.description = null, this.email = null, this.fullName = null, this.modified = null, this.orgId = null, this.portal = null, this.preferredView = null, this.privileges = null, this.region = null, this.role = null, this.roleId = null, this.sourceJSON = null, this.units = null, this.username = null, this.userType = null;
  }
  get thumbnailUrl() {
    const t = this.url, r = this.thumbnail;
    return t && r ? this.portal.normalizeUrl(`${t}/info/${r}?f=json`) : null;
  }
  get userContentUrl() {
    const t = this.get("portal.restUrl");
    return t ? `${t}/content/users/${this.username}` : null;
  }
  get url() {
    const t = this.get("portal.restUrl");
    return t ? `${t}/community/users/${this.username}` : null;
  }
  addItem(t) {
    const r = t && t.item, e2 = t && t.data, o = t && t.folder, l3 = { method: "post" };
    r && (l3.query = r.createPostQuery(), null != e2 && ("string" == typeof e2 ? l3.query.text = e2 : "object" == typeof e2 && (l3.query.text = JSON.stringify(e2))));
    let s2 = this.userContentUrl;
    return o && (s2 += "/" + ("string" == typeof o ? o : o.id)), this.portal.request(s2 + "/addItem", l3).then((t2) => (r.id = t2.id, r.portal = this.portal, r.loaded ? r.reload() : r.load()));
  }
  deleteItem(t) {
    let r = this.userContentUrl;
    return t.ownerFolder && (r += "/" + t.ownerFolder), this.portal.request(r + `/items/${t.id}/delete`, { method: "post" }).then(() => {
      t.id = null, t.portal = null;
    });
  }
  deleteItems(t) {
    const r = this.userContentUrl + "/deleteItems", e2 = t.map((t2) => t2.id);
    if (e2.length) {
      const o = { method: "post", query: { items: e2.join(",") } };
      return this.portal.request(r, o).then(() => {
        t.forEach((t2) => {
          t2.id = null, t2.portal = null;
        });
      });
    }
    return Promise.resolve(void 0);
  }
  fetchFolders() {
    const t = { query: { num: 1 } };
    return this.portal.request(this.userContentUrl ?? "", t).then((t2) => {
      let r;
      return r = t2 && t2.folders ? t2.folders.map((t3) => {
        const r2 = l2.fromJSON(t3);
        return r2.portal = this.portal, r2;
      }) : [], r;
    });
  }
  fetchGroups() {
    return this.portal.request(this.url ?? "").then((t) => {
      let r;
      return r = t && t.groups ? t.groups.map((t2) => {
        const r2 = p.fromJSON(t2);
        return r2.portal = this.portal, r2;
      }) : [], r;
    });
  }
  fetchItems(t) {
    const r = t ?? {};
    let e2, o = this.userContentUrl ?? "";
    return r.folder && (o += "/" + r.folder.id), import("./PortalItem-TUK6YOQD.js").then(({ default: t2 }) => {
      e2 = t2;
      const l3 = { folders: false, num: r.num || 10, start: r.start || 1, sortField: r.sortField || "created", sortOrder: r.sortOrder || "asc" };
      return this.portal.request(o, { query: l3 });
    }).then((t2) => {
      let r2;
      return t2 && t2.items ? (r2 = t2.items.map((t3) => {
        const r3 = e2.fromJSON(t3);
        return r3.portal = this.portal, r3;
      }), Promise.all(r2.map((t3) => t3.load())).catch((t3) => t3).then(() => ({ items: r2, nextStart: t2.nextStart, total: t2.total }))) : { items: [], nextStart: -1, total: 0 };
    });
  }
  fetchTags() {
    return this.portal.request(this.url + "/tags").then((t) => t.tags);
  }
  getThumbnailUrl(t) {
    let r = this.thumbnailUrl;
    return r && t && (r += `&w=${t}`), r;
  }
  queryFavorites(t) {
    return this.favGroupId ? (this._favGroup || (this._favGroup = new p({ id: this.favGroupId, portal: this.portal })), this._favGroup.queryItems(t)) : Promise.reject(new s("internal:unknown", "Unknown internal error", { internalError: "Unknown favGroupId" }));
  }
  toJSON() {
    throw new s("internal:not-yet-implemented", "PortalGroup.toJSON is not yet implemented");
  }
  static fromJSON(t) {
    if (!t) return null;
    if (t.declaredClass) throw new Error("JSON object is already hydrated");
    const r = new i();
    return r.sourceJSON = t, r.read(t), r;
  }
};
e([y()], u.prototype, "access", void 0), e([y({ type: Date })], u.prototype, "created", void 0), e([y()], u.prototype, "culture", void 0), e([y()], u.prototype, "description", void 0), e([y()], u.prototype, "email", void 0), e([y()], u.prototype, "favGroupId", void 0), e([y()], u.prototype, "fullName", void 0), e([y({ type: Date })], u.prototype, "modified", void 0), e([y()], u.prototype, "orgId", void 0), e([y()], u.prototype, "portal", void 0), e([y()], u.prototype, "preferredView", void 0), e([y()], u.prototype, "privileges", void 0), e([y()], u.prototype, "region", void 0), e([y()], u.prototype, "role", void 0), e([y()], u.prototype, "roleId", void 0), e([y()], u.prototype, "sourceJSON", void 0), e([y()], u.prototype, "thumbnail", void 0), e([y({ readOnly: true })], u.prototype, "thumbnailUrl", null), e([y()], u.prototype, "units", void 0), e([y({ readOnly: true })], u.prototype, "userContentUrl", null), e([y({ readOnly: true })], u.prototype, "url", null), e([y()], u.prototype, "username", void 0), e([y()], u.prototype, "userType", void 0), u = i = e([a("esri.portal.PortalUser")], u);
var p3 = u;

export {
  p3 as p
};
//# sourceMappingURL=chunk-TNGL5OFU.js.map
