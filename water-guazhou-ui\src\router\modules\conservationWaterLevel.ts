import type { RouteRecordRaw } from 'vue-router'

const conservationWaterLevelRoutes: RouteRecordRaw[] = [
  {
    path: '/conservation-water-level',
    name: 'ConservationWaterLevel',
    component: () => import('@/layout/index.vue'),
    redirect: '/conservation-water-level/water-level',
    meta: {
      title: '涵养水位管理',
      icon: 'water-drop',
      roles: ['admin', 'user']
    },
    children: [
      {
        path: 'water-level',
        name: 'ConservationWaterLevelData',
        component: () => import('@/views/conservationWaterLevel/waterLevel/index.vue'),
        meta: {
          title: '水位数据管理',
          icon: 'data-line',
          roles: ['admin', 'user']
        }
      },
      {
        path: 'analysis',
        name: 'ConservationWaterLevelAnalysis',
        component: () => import('@/views/conservationWaterLevel/analysis/index.vue'),
        meta: {
          title: '智能分析',
          icon: 'data-analysis',
          roles: ['admin', 'user']
        }
      },
      {
        path: 'dashboard',
        name: 'ConservationWaterLevelDashboard',
        component: () => import('@/views/conservationWaterLevel/dashboard/index.vue'),
        meta: {
          title: '数据大屏',
          icon: 'monitor',
          roles: ['admin', 'user']
        }
      }
    ]
  }
]

export default conservationWaterLevelRoutes
