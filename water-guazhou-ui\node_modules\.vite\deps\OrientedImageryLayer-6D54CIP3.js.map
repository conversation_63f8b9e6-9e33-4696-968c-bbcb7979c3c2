{"version": 3, "sources": ["../../@arcgis/core/layers/OrientedImageryLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import r from\"../core/Error.js\";import{property as o}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as t}from\"../core/accessorSupport/decorators/subclass.js\";import p from\"./FeatureLayer.js\";let i=class extends p{constructor(e){super(e),this.geometryType=\"point\",this.type=\"oriented-imagery\",this.operationalLayerType=\"OrientedImageryLayer\"}_verifySource(){if(super._verifySource(),\"point\"!==this.geometryType)throw new r(\"feature-layer:invalid-geometry-type\",\"OrientedImageryLayer only supports point geometry type\")}};e([o()],i.prototype,\"cameraProperties\",void 0),e([o()],i.prototype,\"coverage\",void 0),e([o()],i.prototype,\"coveragePercent\",void 0),e([o()],i.prototype,\"defaultSearchLocation\",void 0),e([o()],i.prototype,\"depthImage\",void 0),e([o()],i.prototype,\"digitalElevationModel\",void 0),e([o()],i.prototype,\"geometryType\",void 0),e([o()],i.prototype,\"imageProperties\",void 0),e([o()],i.prototype,\"maximumDistance\",void 0),e([o({json:{read:!1},value:\"oriented-imagery\",readOnly:!0})],i.prototype,\"type\",void 0),e([o({type:[\"OrientedImageryLayer\"]})],i.prototype,\"operationalLayerType\",void 0),i=e([t(\"esri.layers.OrientedImageryLayer\")],i);const a=i;export{a as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4U,IAAI,IAAE,cAAc,GAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,SAAQ,KAAK,OAAK,oBAAmB,KAAK,uBAAqB;AAAA,EAAsB;AAAA,EAAC,gBAAe;AAAC,QAAG,MAAM,cAAc,GAAE,YAAU,KAAK,aAAa,OAAM,IAAI,EAAE,uCAAsC,wDAAwD;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,OAAM,oBAAmB,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,sBAAsB,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;", "names": ["e", "a"]}