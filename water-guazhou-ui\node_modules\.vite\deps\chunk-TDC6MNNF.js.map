{"version": 3, "sources": ["../../@arcgis/core/core/CollectionFlattener.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import o from\"./Collection.js\";import{isSome as s,isNone as r}from\"./maybe.js\";import\"./has.js\";import\"./Error.js\";import\"./Logger.js\";import\"./accessorSupport/watch.js\";import{autorun as e}from\"./accessorSupport/trackingUtils.js\";import{property as i}from\"./accessorSupport/decorators/property.js\";import\"./accessorSupport/ensureType.js\";import\"./arrayUtils.js\";import{subclass as n}from\"./accessorSupport/decorators/subclass.js\";let c=class extends o{constructor(t){super(t),this.getCollections=null}initialize(){this.own(e((()=>this._refresh())))}destroy(){this.getCollections=null}_refresh(){const t=s(this.getCollections)?this.getCollections():null;if(r(t))return void this.removeAll();let o=0;for(const r of t)s(r)&&(o=this._processCollection(o,r));this.splice(o,this.length)}_createNewInstance(t){return new o(t)}_processCollection(t,o){if(!o)return t;const s=this.itemFilterFunction?this.itemFilterFunction:t=>!!t;for(const r of o)if(r){if(s(r)){const o=this.indexOf(r,t);o>=0?o!==t&&this.reorder(r,t):this.add(r,t),++t}if(this.getChildrenFunction){const o=this.getChildrenFunction(r);if(Array.isArray(o))for(const s of o)t=this._processCollection(t,s);else t=this._processCollection(t,o)}}return t}};t([i()],c.prototype,\"getCollections\",void 0),t([i()],c.prototype,\"getChildrenFunction\",void 0),t([i()],c.prototype,\"itemFilterFunction\",void 0),c=t([n(\"esri.core.CollectionFlattener\")],c);const l=c;export{l as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAI0d,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,iBAAe;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,IAAI,EAAG,MAAI,KAAK,SAAS,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,iBAAe;AAAA,EAAI;AAAA,EAAC,WAAU;AAAC,UAAMA,KAAE,EAAE,KAAK,cAAc,IAAE,KAAK,eAAe,IAAE;AAAK,QAAG,EAAEA,EAAC,EAAE,QAAO,KAAK,KAAK,UAAU;AAAE,QAAI,IAAE;AAAE,eAAUC,MAAKD,GAAE,GAAEC,EAAC,MAAI,IAAE,KAAK,mBAAmB,GAAEA,EAAC;AAAG,SAAK,OAAO,GAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAE;AAAC,WAAO,IAAI,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE,GAAE;AAAC,QAAG,CAAC,EAAE,QAAOA;AAAE,UAAM,IAAE,KAAK,qBAAmB,KAAK,qBAAmB,CAAAA,OAAG,CAAC,CAACA;AAAE,eAAUC,MAAK,EAAE,KAAGA,IAAE;AAAC,UAAG,EAAEA,EAAC,GAAE;AAAC,cAAMC,KAAE,KAAK,QAAQD,IAAED,EAAC;AAAE,QAAAE,MAAG,IAAEA,OAAIF,MAAG,KAAK,QAAQC,IAAED,EAAC,IAAE,KAAK,IAAIC,IAAED,EAAC,GAAE,EAAEA;AAAA,MAAC;AAAC,UAAG,KAAK,qBAAoB;AAAC,cAAME,KAAE,KAAK,oBAAoBD,EAAC;AAAE,YAAG,MAAM,QAAQC,EAAC,EAAE,YAAUC,MAAKD,GAAE,CAAAF,KAAE,KAAK,mBAAmBA,IAAEG,EAAC;AAAA,YAAO,CAAAH,KAAE,KAAK,mBAAmBA,IAAEE,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,+BAA+B,CAAC,GAAE,CAAC;AAAE,IAAMI,KAAE;", "names": ["t", "r", "o", "s", "l"]}