{"version": 3, "sources": ["../../@arcgis/core/symbols/support/previewSymbol2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../Color.js\";import t from\"../../core/Error.js\";import{loadFont as a}from\"../../core/fontUtils.js\";import{pt2px as n,px2pt as l}from\"../../core/screenUtils.js\";import{getFill as i,getStroke as s,getPatternUrlWithColor as o}from\"./gfxUtils.js\";import{SymbolSizeDefaults as r,shapes as c,adjustColorBrightness as m}from\"./previewUtils.js\";import{renderSymbol as u}from\"./renderUtils.js\";import{getColorLuminance as h}from\"../../views/support/colorUtils.js\";const p=\"picture-fill\",d=\"picture-marker\",f=\"simple-fill\",y=\"simple-line\",w=\"simple-marker\",g=\"text\",v=\"Aa\",b=r.size,x=r.maxSize,k=r.maxOutlineSize,M=r.lineWidth,z=225,L=document.createElement(\"canvas\");function j(e,t){const a=L.getContext(\"2d\"),n=[];return t&&(t.weight&&n.push(t.weight),t.size&&n.push(t.size+\"px\"),t.family&&n.push(t.family)),a.font=n.join(\" \"),a.measureText(e).width}const C=7.2/2.54,S=72/2.54;function F(e){if(0===e.length)return 0;if(e.length>2){const t=l(1),a=parseFloat(e);switch(e.slice(-2)){case\"px\":return a;case\"pt\":return a*t;case\"in\":return 72*a*t;case\"pc\":return 12*a*t;case\"mm\":return a*C*t;case\"cm\":return a*S*t}}return parseFloat(e)}function E(e){const t=e?.size;return{width:null!=t&&\"object\"==typeof t&&\"width\"in t?n(t.width):null,height:null!=t&&\"object\"==typeof t&&\"height\"in t?n(t.height):null}}async function U(e,t){const a=t.fill,n=e.color;if(\"pattern\"===a?.type&&n&&e.type!==p){const e=await o(a.src,n.toCss(!0));a.src=e,t.fill=a}}async function Z(e,t,n,l){if(!(\"font\"in e)||!e.font||\"text\"!==t.shape.type)return;try{await a(e.font)}catch{}const{width:i}=E(l),s=/[\\uE600-\\uE6FF]/.test(t.shape.text);null!=i||s||(n[0]=j(t.shape.text,{weight:t.font?.weight,size:t.font?.size,family:t.font?.family}))}function q(e,t){return e>t?\"dark\":\"light\"}function D(e,t){const a=\"number\"==typeof t?.size?t?.size:null,l=null!=a?n(a):null,o=null!=t?.maxSize?n(t.maxSize):null,r=null!=t?.rotation?t.rotation:\"angle\"in e?e.angle:null,m=i(e);let u=s(e);\"dark\"!==T(e,245)||t?.ignoreWhiteSymbols||(u={width:.75,...u,color:\"#bdc3c7\"});const h={shape:null,fill:m,stroke:u,offset:[0,0]};u?.width&&(u.width=Math.min(u.width,k));const z=u?.width||0;let L=null!=t?.size&&(null==t?.scale||t?.scale),C=0,S=0,U=!1;switch(e.type){case w:{const a=e.style,{width:i,height:s}=E(t),c=i===s&&null!=i?i:null!=l?l:Math.min(n(e.size),o||x);switch(C=c,S=c,a){case\"circle\":h.shape={type:\"circle\",cx:0,cy:0,r:.5*c},L||(C+=z,S+=z);break;case\"cross\":h.shape={type:\"path\",path:[{command:\"M\",values:[0,.5*S]},{command:\"L\",values:[C,.5*S]},{command:\"M\",values:[.5*C,0]},{command:\"L\",values:[.5*C,S]}]};break;case\"diamond\":h.shape={type:\"path\",path:[{command:\"M\",values:[0,.5*S]},{command:\"L\",values:[.5*C,0]},{command:\"L\",values:[C,.5*S]},{command:\"L\",values:[.5*C,S]},{command:\"Z\",values:[]}]},L||(C+=z,S+=z);break;case\"square\":h.shape={type:\"path\",path:[{command:\"M\",values:[0,0]},{command:\"L\",values:[C,0]},{command:\"L\",values:[C,S]},{command:\"L\",values:[0,S]},{command:\"Z\",values:[]}]},L||(C+=z,S+=z),r&&(U=!0);break;case\"triangle\":h.shape={type:\"path\",path:[{command:\"M\",values:[.5*C,0]},{command:\"L\",values:[C,S]},{command:\"L\",values:[0,S]},{command:\"Z\",values:[]}]},L||(C+=z,S+=z),r&&(U=!0);break;case\"x\":h.shape={type:\"path\",path:[{command:\"M\",values:[0,0]},{command:\"L\",values:[C,S]},{command:\"M\",values:[C,0]},{command:\"L\",values:[0,S]}]},r&&(U=!0);break;case\"path\":h.shape={type:\"path\",path:e.path||\"\"},L||(C+=z,S+=z),r&&(U=!0),L=!0}break}case y:{const{width:e,height:a}=E(t),n=null!=a?a:null!=l?l:z,i=null!=e?e:M;u&&(u.width=n),C=i,S=n;const s=h?.stroke?.cap||\"butt\",o=\"round\"===s;L=!0,h.stroke&&(h.stroke.cap=\"butt\"===s?\"square\":s),h.shape={type:\"path\",path:[{command:\"M\",values:[o?n/2:0,S/2]},{command:\"L\",values:[o?C-n/2:C,S/2]}]};break}case p:case f:{const e=\"object\"==typeof t?.symbolConfig&&t?.symbolConfig.isSquareFill,{width:a,height:n}=E(t);C=!e&&a!==n||null==a?null!=l?l:b:a,S=!e&&a!==n||null==n?C:n,L||(C+=z,S+=z),L=!0,h.shape=e?{type:\"path\",path:[{command:\"M\",values:[0,0]},{command:\"L\",values:[C,0]},{command:\"L\",values:[C,S]},{command:\"L\",values:[0,S]},{command:\"L\",values:[0,0]},{command:\"Z\",values:[]}]}:c.fill[0];break}case d:{const a=Math.min(n(e.width),o||x),i=Math.min(n(e.height),o||x),{width:s,height:c}=E(t),m=s===c&&null!=s?s:null!=l?l:Math.max(a,i),u=a/i;C=u<=1?Math.ceil(m*u):m,S=u<=1?m:Math.ceil(m/u),h.shape={type:\"image\",x:-Math.round(C/2),y:-Math.round(S/2),width:C,height:S,src:e.url||\"\"},r&&(U=!0);break}case g:{const a=e,i=t?.overrideText||a.text||v,s=a.font,{width:r,height:c}=E(t),m=null!=c?c:null!=l?l:Math.min(n(s.size),o||x),u=j(i,{weight:s.weight,size:m,family:s.family}),p=/[\\uE600-\\uE6FF]/.test(i);C=r??(p?m:u),S=m;let d=.25*F((s?m:0).toString());p&&(d+=5),h.shape={type:\"text\",text:i,x:a.xoffset||0,y:a.yoffset||d,align:\"middle\",alignBaseline:a.verticalAlignment,decoration:s&&s.decoration,rotated:a.rotated,kerning:a.kerning},h.font=s&&{size:m,style:s.style,decoration:s.decoration,weight:s.weight,family:s.family};break}}return{shapeDescriptor:h,size:[C,S],renderOptions:{node:t?.node,scale:L,opacity:t?.opacity,rotation:r,useRotationSize:U,effectView:t?.effectView}}}async function O(e,a){const{shapeDescriptor:n,size:l,renderOptions:i}=D(e,a);if(!n.shape)throw new t(\"symbolPreview: renderPreviewHTML2D\",\"symbol not supported.\");await U(e,n),await Z(e,n,l,a);const s=[[n]];if(\"object\"==typeof a?.symbolConfig&&a?.symbolConfig.applyColorModulation){const e=.6*l[0];s.unshift([{...n,offset:[-e,0],fill:m(n.fill,-.3)}]),s.push([{...n,offset:[e,0],fill:m(n.fill,.3)}]),l[0]+=2*e,i.scale=!1}return u(s,l,i)}function T(t,a=z){const n=i(t),l=s(t),o=!n||\"type\"in n?null:new e(n),r=l?.color?new e(l?.color):null,c=o?q(h(o),a):null,m=r?q(h(r),a):null;return m?c?c===m?c:a>=z?\"light\":\"dark\":m:c}export{T as getContrastingBackgroundTheme,D as getRenderSymbolParameters,O as previewSymbol2D};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIwd,IAAM,IAAE;AAAR,IAAuBA,KAAE;AAAzB,IAA0CC,KAAE;AAA5C,IAA0DC,KAAE;AAA5D,IAA0E,IAAE;AAA5E,IAA4F,IAAE;AAA9F,IAAqG,IAAE;AAAvG,IAA4G,IAAE,EAAE;AAAhH,IAAqH,IAAE,EAAE;AAAzH,IAAiI,IAAE,EAAE;AAArI,IAAoJ,IAAE,EAAE;AAAxJ,IAAkK,IAAE;AAApK,IAAwK,IAAE,SAAS,cAAc,QAAQ;AAAE,SAAS,EAAEC,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAE,WAAW,IAAI,GAAEC,KAAE,CAAC;AAAE,SAAOD,OAAIA,GAAE,UAAQC,GAAE,KAAKD,GAAE,MAAM,GAAEA,GAAE,QAAMC,GAAE,KAAKD,GAAE,OAAK,IAAI,GAAEA,GAAE,UAAQC,GAAE,KAAKD,GAAE,MAAM,IAAG,EAAE,OAAKC,GAAE,KAAK,GAAG,GAAE,EAAE,YAAYF,EAAC,EAAE;AAAK;AAAC,IAAM,IAAE,MAAI;AAAZ,IAAiB,IAAE,KAAG;AAAK,SAAS,EAAEA,IAAE;AAAC,MAAG,MAAIA,GAAE,OAAO,QAAO;AAAE,MAAGA,GAAE,SAAO,GAAE;AAAC,UAAMC,KAAE,EAAE,CAAC,GAAE,IAAE,WAAWD,EAAC;AAAE,YAAOA,GAAE,MAAM,EAAE,GAAE;AAAA,MAAC,KAAI;AAAK,eAAO;AAAA,MAAE,KAAI;AAAK,eAAO,IAAEC;AAAA,MAAE,KAAI;AAAK,eAAO,KAAG,IAAEA;AAAA,MAAE,KAAI;AAAK,eAAO,KAAG,IAAEA;AAAA,MAAE,KAAI;AAAK,eAAO,IAAE,IAAEA;AAAA,MAAE,KAAI;AAAK,eAAO,IAAE,IAAEA;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,WAAWD,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAED,MAAA,gBAAAA,GAAG;AAAK,SAAM,EAAC,OAAM,QAAMC,MAAG,YAAU,OAAOA,MAAG,WAAUA,KAAE,EAAEA,GAAE,KAAK,IAAE,MAAK,QAAO,QAAMA,MAAG,YAAU,OAAOA,MAAG,YAAWA,KAAE,EAAEA,GAAE,MAAM,IAAE,KAAI;AAAC;AAAC,eAAe,EAAED,IAAEC,IAAE;AAAC,QAAM,IAAEA,GAAE,MAAKC,KAAEF,GAAE;AAAM,MAAG,eAAY,uBAAG,SAAME,MAAGF,GAAE,SAAO,GAAE;AAAC,UAAMA,KAAE,MAAM,EAAE,EAAE,KAAIE,GAAE,MAAM,IAAE,CAAC;AAAE,MAAE,MAAIF,IAAEC,GAAE,OAAK;AAAA,EAAC;AAAC;AAAC,eAAe,EAAED,IAAEC,IAAEC,IAAEC,IAAE;AAJ97C;AAI+7C,MAAG,EAAE,UAASH,OAAI,CAACA,GAAE,QAAM,WAASC,GAAE,MAAM,KAAK;AAAO,MAAG;AAAC,UAAM,EAAED,GAAE,IAAI;AAAA,EAAC,QAAM;AAAA,EAAC;AAAC,QAAK,EAAC,OAAM,EAAC,IAAE,EAAEG,EAAC,GAAEC,KAAE,kBAAkB,KAAKH,GAAE,MAAM,IAAI;AAAE,UAAM,KAAGG,OAAIF,GAAE,CAAC,IAAE,EAAED,GAAE,MAAM,MAAK,EAAC,SAAO,KAAAA,GAAE,SAAF,mBAAQ,QAAO,OAAK,KAAAA,GAAE,SAAF,mBAAQ,MAAK,SAAO,KAAAA,GAAE,SAAF,mBAAQ,OAAM,CAAC;AAAE;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAOD,KAAEC,KAAE,SAAO;AAAO;AAAC,SAAS,EAAED,IAAEC,IAAE;AAJzuD;AAI0uD,QAAM,IAAE,YAAU,QAAOA,MAAA,gBAAAA,GAAG,QAAKA,MAAA,gBAAAA,GAAG,OAAK,MAAKE,KAAE,QAAM,IAAE,EAAE,CAAC,IAAE,MAAK,IAAE,SAAMF,MAAA,gBAAAA,GAAG,WAAQ,EAAEA,GAAE,OAAO,IAAE,MAAK,IAAE,SAAMA,MAAA,gBAAAA,GAAG,YAASA,GAAE,WAAS,WAAUD,KAAEA,GAAE,QAAM,MAAK,IAAE,EAAEA,EAAC;AAAE,MAAIK,KAAE,EAAEL,EAAC;AAAE,aAAS,EAAEA,IAAE,GAAG,MAAGC,MAAA,gBAAAA,GAAG,wBAAqBI,KAAE,EAAC,OAAM,MAAI,GAAGA,IAAE,OAAM,UAAS;AAAG,QAAMC,KAAE,EAAC,OAAM,MAAK,MAAK,GAAE,QAAOD,IAAE,QAAO,CAAC,GAAE,CAAC,EAAC;AAAE,GAAAA,MAAA,gBAAAA,GAAG,WAAQA,GAAE,QAAM,KAAK,IAAIA,GAAE,OAAM,CAAC;AAAG,QAAME,MAAEF,MAAA,gBAAAA,GAAG,UAAO;AAAE,MAAIG,KAAE,SAAMP,MAAA,gBAAAA,GAAG,UAAO,SAAMA,MAAA,gBAAAA,GAAG,WAAOA,MAAA,gBAAAA,GAAG,SAAOQ,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAG,UAAOX,GAAE,MAAK;AAAA,IAAC,KAAK,GAAE;AAAC,YAAMY,KAAEZ,GAAE,OAAM,EAAC,OAAM,GAAE,QAAOI,GAAC,IAAE,EAAEH,EAAC,GAAE,IAAE,MAAIG,MAAG,QAAM,IAAE,IAAE,QAAMD,KAAEA,KAAE,KAAK,IAAI,EAAEH,GAAE,IAAI,GAAE,KAAG,CAAC;AAAE,cAAOS,KAAE,GAAEC,KAAE,GAAEE,IAAE;AAAA,QAAC,KAAI;AAAS,UAAAN,GAAE,QAAM,EAAC,MAAK,UAAS,IAAG,GAAE,IAAG,GAAE,GAAE,MAAG,EAAC,GAAEE,OAAIC,MAAGF,IAAEG,MAAGH;AAAG;AAAA,QAAM,KAAI;AAAQ,UAAAD,GAAE,QAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAE,MAAGI,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACD,IAAE,MAAGC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGD,IAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGA,IAAEC,EAAC,EAAC,CAAC,EAAC;AAAE;AAAA,QAAM,KAAI;AAAU,UAAAJ,GAAE,QAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAE,MAAGI,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGD,IAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACA,IAAE,MAAGC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGD,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,EAAC,CAAC,EAAC,GAAEF,OAAIC,MAAGF,IAAEG,MAAGH;AAAG;AAAA,QAAM,KAAI;AAAS,UAAAD,GAAE,QAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACG,IAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACA,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAEA,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,EAAC,CAAC,EAAC,GAAEF,OAAIC,MAAGF,IAAEG,MAAGH,KAAG,MAAII,KAAE;AAAI;AAAA,QAAM,KAAI;AAAW,UAAAL,GAAE,QAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGG,IAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACA,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAEA,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,EAAC,CAAC,EAAC,GAAEF,OAAIC,MAAGF,IAAEG,MAAGH,KAAG,MAAII,KAAE;AAAI;AAAA,QAAM,KAAI;AAAI,UAAAL,GAAE,QAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACG,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACD,IAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAEC,EAAC,EAAC,CAAC,EAAC,GAAE,MAAIC,KAAE;AAAI;AAAA,QAAM,KAAI;AAAO,UAAAL,GAAE,QAAM,EAAC,MAAK,QAAO,MAAKN,GAAE,QAAM,GAAE,GAAEQ,OAAIC,MAAGF,IAAEG,MAAGH,KAAG,MAAII,KAAE,OAAIH,KAAE;AAAA,MAAE;AAAC;AAAA,IAAK;AAAA,IAAC,KAAKT,IAAE;AAAC,YAAK,EAAC,OAAMC,IAAE,QAAOY,GAAC,IAAE,EAAEX,EAAC,GAAEC,KAAE,QAAMU,KAAEA,KAAE,QAAMT,KAAEA,KAAEI,IAAE,IAAE,QAAMP,KAAEA,KAAE;AAAE,MAAAK,OAAIA,GAAE,QAAMH,KAAGO,KAAE,GAAEC,KAAER;AAAE,YAAME,OAAE,KAAAE,MAAA,gBAAAA,GAAG,WAAH,mBAAW,QAAK,QAAOO,KAAE,YAAUT;AAAE,MAAAI,KAAE,MAAGF,GAAE,WAASA,GAAE,OAAO,MAAI,WAASF,KAAE,WAASA,KAAGE,GAAE,QAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAACO,KAAEX,KAAE,IAAE,GAAEQ,KAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACG,KAAEJ,KAAEP,KAAE,IAAEO,IAAEC,KAAE,CAAC,EAAC,CAAC,EAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAK;AAAA,IAAE,KAAKZ,IAAE;AAAC,YAAME,KAAE,YAAU,QAAOC,MAAA,gBAAAA,GAAG,kBAAcA,MAAA,gBAAAA,GAAG,aAAa,eAAa,EAAC,OAAMW,IAAE,QAAOV,GAAC,IAAE,EAAED,EAAC;AAAE,MAAAQ,KAAE,CAACT,MAAGY,OAAIV,MAAG,QAAMU,KAAE,QAAMT,KAAEA,KAAE,IAAES,IAAEF,KAAE,CAACV,MAAGY,OAAIV,MAAG,QAAMA,KAAEO,KAAEP,IAAEM,OAAIC,MAAGF,IAAEG,MAAGH,KAAGC,KAAE,MAAGF,GAAE,QAAMN,KAAE,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACS,IAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACA,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAEA,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,EAAC,CAAC,EAAC,IAAEV,GAAE,KAAK,CAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAKH,IAAE;AAAC,YAAMe,KAAE,KAAK,IAAI,EAAEZ,GAAE,KAAK,GAAE,KAAG,CAAC,GAAE,IAAE,KAAK,IAAI,EAAEA,GAAE,MAAM,GAAE,KAAG,CAAC,GAAE,EAAC,OAAMI,IAAE,QAAO,EAAC,IAAE,EAAEH,EAAC,GAAEa,KAAEV,OAAI,KAAG,QAAMA,KAAEA,KAAE,QAAMD,KAAEA,KAAE,KAAK,IAAIS,IAAE,CAAC,GAAEP,KAAEO,KAAE;AAAE,MAAAH,KAAEJ,MAAG,IAAE,KAAK,KAAKS,KAAET,EAAC,IAAES,IAAEJ,KAAEL,MAAG,IAAES,KAAE,KAAK,KAAKA,KAAET,EAAC,GAAEC,GAAE,QAAM,EAAC,MAAK,SAAQ,GAAE,CAAC,KAAK,MAAMG,KAAE,CAAC,GAAE,GAAE,CAAC,KAAK,MAAMC,KAAE,CAAC,GAAE,OAAMD,IAAE,QAAOC,IAAE,KAAIV,GAAE,OAAK,GAAE,GAAE,MAAIW,KAAE;AAAI;AAAA,IAAK;AAAA,IAAC,KAAK,GAAE;AAAC,YAAMC,KAAEZ,IAAE,KAAEC,MAAA,gBAAAA,GAAG,iBAAcW,GAAE,QAAM,GAAER,KAAEQ,GAAE,MAAK,EAAC,OAAMG,IAAE,QAAO,EAAC,IAAE,EAAEd,EAAC,GAAEa,KAAE,QAAM,IAAE,IAAE,QAAMX,KAAEA,KAAE,KAAK,IAAI,EAAEC,GAAE,IAAI,GAAE,KAAG,CAAC,GAAEC,KAAE,EAAE,GAAE,EAAC,QAAOD,GAAE,QAAO,MAAKU,IAAE,QAAOV,GAAE,OAAM,CAAC,GAAEY,KAAE,kBAAkB,KAAK,CAAC;AAAE,MAAAP,KAAEM,OAAIC,KAAEF,KAAET,KAAGK,KAAEI;AAAE,UAAIjB,KAAE,OAAI,GAAGO,KAAEU,KAAE,GAAG,SAAS,CAAC;AAAE,MAAAE,OAAInB,MAAG,IAAGS,GAAE,QAAM,EAAC,MAAK,QAAO,MAAK,GAAE,GAAEM,GAAE,WAAS,GAAE,GAAEA,GAAE,WAASf,IAAE,OAAM,UAAS,eAAce,GAAE,mBAAkB,YAAWR,MAAGA,GAAE,YAAW,SAAQQ,GAAE,SAAQ,SAAQA,GAAE,QAAO,GAAEN,GAAE,OAAKF,MAAG,EAAC,MAAKU,IAAE,OAAMV,GAAE,OAAM,YAAWA,GAAE,YAAW,QAAOA,GAAE,QAAO,QAAOA,GAAE,OAAM;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,SAAM,EAAC,iBAAgBE,IAAE,MAAK,CAACG,IAAEC,EAAC,GAAE,eAAc,EAAC,MAAKT,MAAA,gBAAAA,GAAG,MAAK,OAAMO,IAAE,SAAQP,MAAA,gBAAAA,GAAG,SAAQ,UAAS,GAAE,iBAAgBU,IAAE,YAAWV,MAAA,gBAAAA,GAAG,WAAU,EAAC;AAAC;AAAC,eAAe,EAAED,IAAE,GAAE;AAAC,QAAK,EAAC,iBAAgBE,IAAE,MAAKC,IAAE,eAAc,EAAC,IAAE,EAAEH,IAAE,CAAC;AAAE,MAAG,CAACE,GAAE,MAAM,OAAM,IAAI,EAAE,sCAAqC,uBAAuB;AAAE,QAAM,EAAEF,IAAEE,EAAC,GAAE,MAAM,EAAEF,IAAEE,IAAEC,IAAE,CAAC;AAAE,QAAMC,KAAE,CAAC,CAACF,EAAC,CAAC;AAAE,MAAG,YAAU,QAAO,uBAAG,kBAAc,uBAAG,aAAa,uBAAqB;AAAC,UAAMF,KAAE,MAAGG,GAAE,CAAC;AAAE,IAAAC,GAAE,QAAQ,CAAC,EAAC,GAAGF,IAAE,QAAO,CAAC,CAACF,IAAE,CAAC,GAAE,MAAK,EAAEE,GAAE,MAAK,IAAG,EAAC,CAAC,CAAC,GAAEE,GAAE,KAAK,CAAC,EAAC,GAAGF,IAAE,QAAO,CAACF,IAAE,CAAC,GAAE,MAAK,EAAEE,GAAE,MAAK,GAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,CAAC,KAAG,IAAEH,IAAE,EAAE,QAAM;AAAA,EAAE;AAAC,SAAOG,GAAEC,IAAED,IAAE,CAAC;AAAC;AAAC,SAAS,EAAEF,IAAE,IAAE,GAAE;AAAC,QAAMC,KAAE,EAAED,EAAC,GAAEE,KAAE,EAAEF,EAAC,GAAE,IAAE,CAACC,MAAG,UAASA,KAAE,OAAK,IAAI,EAAEA,EAAC,GAAE,KAAEC,MAAA,gBAAAA,GAAG,SAAM,IAAI,EAAEA,MAAA,gBAAAA,GAAG,KAAK,IAAE,MAAK,IAAE,IAAE,EAAEE,GAAE,CAAC,GAAE,CAAC,IAAE,MAAK,IAAE,IAAE,EAAEA,GAAE,CAAC,GAAE,CAAC,IAAE;AAAK,SAAO,IAAE,IAAE,MAAI,IAAE,IAAE,KAAG,IAAE,UAAQ,SAAO,IAAE;AAAC;", "names": ["d", "f", "y", "e", "t", "n", "l", "s", "u", "h", "z", "L", "C", "S", "U", "a", "o", "m", "r", "p"]}