import {
  o
} from "./chunk-PEEUPDEG.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/layers/support/I3SLayerDefinitions.js
var s = class extends l {
  constructor() {
    super(...arguments), this.nodesPerPage = null, this.rootIndex = 0, this.lodSelectionMetricType = null;
  }
};
e([y({ type: Number })], s.prototype, "nodesPerPage", void 0), e([y({ type: Number })], s.prototype, "rootIndex", void 0), e([y({ type: String })], s.prototype, "lodSelectionMetricType", void 0), s = e([a("esri.layer.support.I3SNodePageDefinition")], s);
var i = class extends l {
  constructor() {
    super(...arguments), this.factor = 1;
  }
};
e([y({ type: Number, json: { read: { source: "textureSetDefinitionId" } } })], i.prototype, "id", void 0), e([y({ type: Number })], i.prototype, "factor", void 0), i = e([a("esri.layer.support.I3SMaterialTexture")], i);
var a2 = class extends l {
  constructor() {
    super(...arguments), this.baseColorFactor = [1, 1, 1, 1], this.baseColorTexture = null, this.metallicRoughnessTexture = null, this.metallicFactor = 1, this.roughnessFactor = 1;
  }
};
e([y({ type: [Number] })], a2.prototype, "baseColorFactor", void 0), e([y({ type: i })], a2.prototype, "baseColorTexture", void 0), e([y({ type: i })], a2.prototype, "metallicRoughnessTexture", void 0), e([y({ type: Number })], a2.prototype, "metallicFactor", void 0), e([y({ type: Number })], a2.prototype, "roughnessFactor", void 0), a2 = e([a("esri.layer.support.I3SMaterialPBRMetallicRoughness")], a2);
var l2 = class extends l {
  constructor() {
    super(...arguments), this.alphaMode = "opaque", this.alphaCutoff = 0.25, this.doubleSided = false, this.cullFace = "none", this.normalTexture = null, this.occlusionTexture = null, this.emissiveTexture = null, this.emissiveFactor = null, this.pbrMetallicRoughness = null;
  }
};
e([o({ opaque: "opaque", mask: "mask", blend: "blend" })], l2.prototype, "alphaMode", void 0), e([y({ type: Number })], l2.prototype, "alphaCutoff", void 0), e([y({ type: Boolean })], l2.prototype, "doubleSided", void 0), e([o({ none: "none", back: "back", front: "front" })], l2.prototype, "cullFace", void 0), e([y({ type: i })], l2.prototype, "normalTexture", void 0), e([y({ type: i })], l2.prototype, "occlusionTexture", void 0), e([y({ type: i })], l2.prototype, "emissiveTexture", void 0), e([y({ type: [Number] })], l2.prototype, "emissiveFactor", void 0), e([y({ type: a2 })], l2.prototype, "pbrMetallicRoughness", void 0), l2 = e([a("esri.layer.support.I3SMaterialDefinition")], l2);
var n = class extends l {
};
e([y({ type: String, json: { read: { source: ["name", "index"], reader: (e2, t) => null != e2 ? e2 : `${t.index}` } } })], n.prototype, "name", void 0), e([o({ jpg: "jpg", png: "png", dds: "dds", "ktx-etc2": "ktx-etc2", ktx2: "ktx2", basis: "basis" })], n.prototype, "format", void 0), n = e([a("esri.layer.support.I3STextureFormat")], n);
var u = class extends l {
  constructor() {
    super(...arguments), this.atlas = false;
  }
};
e([y({ type: [n] })], u.prototype, "formats", void 0), e([y({ type: Boolean })], u.prototype, "atlas", void 0), u = e([a("esri.layer.support.I3STextureSetDefinition")], u);
var y2 = class extends l {
};
e([o({ Float32: "Float32", UInt64: "UInt64", UInt32: "UInt32", UInt16: "UInt16", UInt8: "UInt8" })], y2.prototype, "type", void 0), e([y({ type: Number })], y2.prototype, "component", void 0), y2 = e([a("esri.layer.support.I3SGeometryAttribute")], y2);
var d = class extends l {
};
e([o({ draco: "draco" })], d.prototype, "encoding", void 0), e([y({ type: [String] })], d.prototype, "attributes", void 0), d = e([a("esri.layer.support.I3SGeometryCompressedAttributes")], d);
var c = class extends l {
  constructor() {
    super(...arguments), this.offset = 0;
  }
};
e([y({ type: Number })], c.prototype, "offset", void 0), e([y({ type: y2 })], c.prototype, "position", void 0), e([y({ type: y2 })], c.prototype, "normal", void 0), e([y({ type: y2 })], c.prototype, "uv0", void 0), e([y({ type: y2 })], c.prototype, "color", void 0), e([y({ type: y2 })], c.prototype, "uvRegion", void 0), e([y({ type: y2 })], c.prototype, "featureId", void 0), e([y({ type: y2 })], c.prototype, "faceRange", void 0), e([y({ type: d })], c.prototype, "compressedAttributes", void 0), c = e([a("esri.layer.support.I3SGeometryBuffer")], c);
var m = class extends l {
};
e([o({ triangle: "triangle" })], m.prototype, "topology", void 0), e([y()], m.prototype, "geometryBuffers", void 0), m = e([a("esri.layer.support.I3SGeometryDefinition")], m);

export {
  s,
  l2 as l,
  u,
  m
};
//# sourceMappingURL=chunk-WYBCZXCQ.js.map
