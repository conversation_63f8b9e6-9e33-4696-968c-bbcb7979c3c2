import {
  E as E4,
  e as e13,
  f as f4,
  o2 as o8,
  r2 as r10,
  v,
  y
} from "./chunk-NEOCII5B.js";
import {
  s as s5
} from "./chunk-FQZM46ZM.js";
import {
  G
} from "./chunk-DXGQBULN.js";
import {
  t as t7
} from "./chunk-PI5OC2DP.js";
import {
  A as A2,
  S as S3,
  a as a3,
  c as c5,
  l as l2
} from "./chunk-LGZKVOWE.js";
import {
  t as t6
} from "./chunk-SKYLCTPX.js";
import {
  X
} from "./chunk-4ZR6RUFJ.js";
import {
  i as i6
} from "./chunk-TKIS7RZA.js";
import {
  R
} from "./chunk-VVOL4DVY.js";
import {
  a as a4,
  f as f3,
  v as v2
} from "./chunk-MNPAHSMX.js";
import {
  e as e12,
  o as o7,
  t as t8
} from "./chunk-QLHJUIIZ.js";
import {
  r as r11
} from "./chunk-KVEZ26WH.js";
import {
  E as E3,
  N,
  c as c4,
  h as h4,
  x as x3
} from "./chunk-TOYJMVHA.js";
import {
  d,
  d2,
  h as h3
} from "./chunk-XVTFHFM3.js";
import {
  o as o6
} from "./chunk-ND2RJTSZ.js";
import {
  h as h2
} from "./chunk-L4Y6W6Y5.js";
import {
  W,
  _,
  a as a2,
  h as h5
} from "./chunk-WKBMFG6J.js";
import {
  o as o5
} from "./chunk-BPRRRPC3.js";
import {
  s as s4
} from "./chunk-IUNR7SKI.js";
import {
  e as e9,
  f2,
  i as i5,
  m,
  n2,
  n3,
  o as o2,
  o2 as o3,
  o3 as o4,
  r as r6,
  r2 as r7,
  t as t3
} from "./chunk-BLTZUGC7.js";
import {
  t as t4
} from "./chunk-F4KVXA42.js";
import {
  e as e10,
  n as n4,
  u as u2
} from "./chunk-6ZZUUGXX.js";
import {
  T
} from "./chunk-QW426QEA.js";
import {
  O as O3
} from "./chunk-CPQSD22U.js";
import {
  e2 as e7,
  f,
  r as r5,
  t as t2
} from "./chunk-UQUDWTCY.js";
import {
  E,
  L,
  O as O2,
  c as c2,
  i as i2,
  u,
  x as x2
} from "./chunk-3KCCETWY.js";
import {
  D,
  E as E2,
  I
} from "./chunk-4M3AMTD4.js";
import {
  c as c3,
  i as i4
} from "./chunk-DUEDINK5.js";
import {
  e as e11,
  t as t5
} from "./chunk-MZ267CZB.js";
import {
  r as r9
} from "./chunk-QCTKOQ44.js";
import {
  g as g2,
  i as i3
} from "./chunk-ST2RRB55.js";
import {
  a
} from "./chunk-26N6FACI.js";
import {
  e as e6
} from "./chunk-QYOAH6AO.js";
import {
  e as e5
} from "./chunk-A7PY25IH.js";
import {
  l
} from "./chunk-JJ3NE6DY.js";
import {
  e as e8
} from "./chunk-P2G4OGHI.js";
import {
  r as r8
} from "./chunk-J4KDDSED.js";
import {
  b
} from "./chunk-P37TUI4J.js";
import {
  h
} from "./chunk-YEODPCXQ.js";
import {
  r as r4
} from "./chunk-NOZFLZZL.js";
import {
  S as S2,
  c
} from "./chunk-AVKOL7OR.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  A,
  O,
  P,
  S,
  e as e4,
  g,
  i,
  o,
  r as r3,
  s as s3,
  z
} from "./chunk-MQAXMQFG.js";
import {
  e as e3,
  n,
  r as r2
} from "./chunk-36FLFRUE.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  e as e2,
  r,
  t,
  x
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/glTF/internal/TextureTransformUtils.js
function c6(c8) {
  if (t(c8)) return null;
  const m2 = r(c8.offset) ? c8.offset : c3, e14 = r(c8.rotation) ? c8.rotation : 0, i7 = r(c8.scale) ? c8.scale : i4, h6 = t5(1, 0, 0, 0, 1, 0, m2[0], m2[1], 1), u3 = t5(Math.cos(e14), -Math.sin(e14), 0, Math.sin(e14), Math.cos(e14), 0, 0, 0, 1), p = t5(i7[0], 0, 0, 0, i7[1], 0, 0, 0, 1), j3 = e11();
  return i3(j3, u3, p), i3(j3, h6, j3), j3;
}

// node_modules/@arcgis/core/views/3d/layers/graphics/ProcessedObjectResource.js
var s6 = class {
  constructor() {
    this.geometries = new Array(), this.materials = new Array(), this.textures = new Array();
  }
};
var t9 = class {
  constructor(t11, e14, r12) {
    this.name = t11, this.lodThreshold = e14, this.pivotOffset = r12, this.stageResources = new s6(), this.numberOfVertices = 0;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/DefaultMaterialTechnique.js
var k = class extends f3 {
  constructor() {
    super(...arguments), this.isSchematic = false, this.usePBR = false, this.mrrFactors = r2(0, 1, 0.5), this.hasVertexColors = false, this.hasSymbolColors = false, this.doubleSided = false, this.doubleSidedType = "normal", this.cullFace = n4.Back, this.emissiveFactor = r2(0, 0, 0), this.instancedDoublePrecision = false, this.normalType = a4.Attribute, this.receiveSSAO = true, this.receiveShadows = true, this.castShadows = true, this.shadowMappingEnabled = false, this.ambient = r2(0.2, 0.2, 0.2), this.diffuse = r2(0.8, 0.8, 0.8), this.externalColor = r4(1, 1, 1, 1), this.colorMixMode = "multiply", this.opacity = 1, this.layerOpacity = 1, this.origin = n(), this.hasSlicePlane = false, this.hasSliceHighlight = true, this.offsetTransparentBackfaces = false, this.vvSizeEnabled = false, this.vvSizeMinSize = [1, 1, 1], this.vvSizeMaxSize = [100, 100, 100], this.vvSizeOffset = [0, 0, 0], this.vvSizeFactor = [1, 1, 1], this.vvSizeValue = [1, 1, 1], this.vvColorEnabled = false, this.vvColorValues = [0, 0, 0, 0, 0, 0, 0, 0], this.vvColorColors = [1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0], this.vvSymbolAnchor = [0, 0, 0], this.vvSymbolRotationMatrix = e5(), this.vvOpacityEnabled = false, this.vvOpacityValues = [], this.vvOpacityOpacities = [], this.transparent = false, this.writeDepth = true, this.customDepthTest = e10.Less, this.textureAlphaMode = u2.Blend, this.textureAlphaCutoff = o6, this.textureAlphaPremultiplied = false, this.hasOccludees = false, this.renderOccluded = c4.Occlude;
  }
};
var E5 = class extends v2 {
  constructor() {
    super(...arguments), this.origin = n(), this.slicePlaneLocalOrigin = this.origin;
  }
};
var _2 = class __ extends e12 {
  initializeConfiguration(e14, t11) {
    t11.hasWebGL2Context = e14.rctx.type === r9.WEBGL2, t11.spherical = e14.viewingMode === l.Global, t11.doublePrecisionRequiresObfuscation = e14.rctx.driverTest.doublePrecisionRequiresObfuscation.result, t11.textureCoordinateType = t11.hasColorTexture || t11.hasMetallicRoughnessTexture || t11.hasEmissionTexture || t11.hasOcclusionTexture || t11.hasNormalTexture ? d.Default : d.None, t11.objectAndLayerIdColorInstanced = t11.instanced;
  }
  initializeProgram(e14) {
    return this._initializeProgram(e14, __.shader);
  }
  _initializeProgram(e14, t11) {
    return new o7(e14.rctx, t11.get().build(this.configuration), E3);
  }
  _convertDepthTestFunction(e14) {
    return e14 === e10.Lequal ? I.LEQUAL : I.LESS;
  }
  _makePipeline(e14, t11) {
    const i7 = this.configuration, s7 = e14 === o5.NONE, r12 = e14 === o5.FrontFace;
    return W({ blending: i7.output !== h2.Color && i7.output !== h2.Alpha || !i7.transparent ? null : s7 ? c5 : A2(e14), culling: N2(i7) ? h5(i7.cullFace) : null, depthTest: { func: l2(e14, this._convertDepthTestFunction(i7.customDepthTest)) }, depthWrite: (s7 || r12) && i7.writeDepth ? a2 : null, colorWrite: _, stencilWrite: i7.hasOccludees ? e13 : null, stencilTest: i7.hasOccludees ? t11 ? o8 : f4 : null, polygonOffset: s7 || r12 ? null : a3(i7.enableOffset) });
  }
  initializePipeline() {
    return this._occludeePipelineState = this._makePipeline(this.configuration.transparencyPassType, true), this._makePipeline(this.configuration.transparencyPassType, false);
  }
  getPipelineState(e14, t11) {
    return t11 ? this._occludeePipelineState : super.getPipelineState(e14, t11);
  }
};
function N2(e14) {
  return e14.cullFace !== n4.None || !e14.hasSlicePlane && (!e14.transparent && !e14.doubleSidedMode);
}
_2.shader = new t8(X, () => import("./DefaultMaterial.glsl-KIELUJHE.js"));

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/DefaultMaterialTechniqueConfiguration.js
var c7 = class extends s5 {
  constructor() {
    super(...arguments), this.output = h2.Color, this.alphaDiscardMode = u2.Opaque, this.doubleSidedMode = i6.None, this.pbrMode = d2.Disabled, this.cullFace = n4.None, this.transparencyPassType = o5.NONE, this.normalType = a4.Attribute, this.textureCoordinateType = d.None, this.customDepthTest = e10.Less, this.spherical = false, this.hasVertexColors = false, this.hasSymbolColors = false, this.hasVerticalOffset = false, this.hasSlicePlane = false, this.hasSliceHighlight = true, this.hasColorTexture = false, this.hasMetallicRoughnessTexture = false, this.hasEmissionTexture = false, this.hasOcclusionTexture = false, this.hasNormalTexture = false, this.hasScreenSizePerspective = false, this.hasVertexTangents = false, this.hasOccludees = false, this.hasMultipassTerrain = false, this.hasModelTransformation = false, this.offsetBackfaces = false, this.vvSize = false, this.vvColor = false, this.receiveShadows = false, this.receiveAmbientOcclusion = false, this.textureAlphaPremultiplied = false, this.instanced = false, this.instancedColor = false, this.objectAndLayerIdColorInstanced = false, this.instancedDoublePrecision = false, this.doublePrecisionRequiresObfuscation = false, this.writeDepth = true, this.transparent = false, this.enableOffset = true, this.cullAboveGround = false, this.snowCover = false, this.hasColorTextureTransform = false, this.hasEmissionTextureTransform = false, this.hasNormalTextureTransform = false, this.hasOcclusionTextureTransform = false, this.hasMetallicRoughnessTextureTransform = false;
  }
};
e([r11({ count: h2.COUNT })], c7.prototype, "output", void 0), e([r11({ count: u2.COUNT })], c7.prototype, "alphaDiscardMode", void 0), e([r11({ count: i6.COUNT })], c7.prototype, "doubleSidedMode", void 0), e([r11({ count: d2.COUNT })], c7.prototype, "pbrMode", void 0), e([r11({ count: n4.COUNT })], c7.prototype, "cullFace", void 0), e([r11({ count: o5.COUNT })], c7.prototype, "transparencyPassType", void 0), e([r11({ count: a4.COUNT })], c7.prototype, "normalType", void 0), e([r11({ count: d.COUNT })], c7.prototype, "textureCoordinateType", void 0), e([r11({ count: e10.COUNT })], c7.prototype, "customDepthTest", void 0), e([r11()], c7.prototype, "spherical", void 0), e([r11()], c7.prototype, "hasVertexColors", void 0), e([r11()], c7.prototype, "hasSymbolColors", void 0), e([r11()], c7.prototype, "hasVerticalOffset", void 0), e([r11()], c7.prototype, "hasSlicePlane", void 0), e([r11()], c7.prototype, "hasSliceHighlight", void 0), e([r11()], c7.prototype, "hasColorTexture", void 0), e([r11()], c7.prototype, "hasMetallicRoughnessTexture", void 0), e([r11()], c7.prototype, "hasEmissionTexture", void 0), e([r11()], c7.prototype, "hasOcclusionTexture", void 0), e([r11()], c7.prototype, "hasNormalTexture", void 0), e([r11()], c7.prototype, "hasScreenSizePerspective", void 0), e([r11()], c7.prototype, "hasVertexTangents", void 0), e([r11()], c7.prototype, "hasOccludees", void 0), e([r11()], c7.prototype, "hasMultipassTerrain", void 0), e([r11()], c7.prototype, "hasModelTransformation", void 0), e([r11()], c7.prototype, "offsetBackfaces", void 0), e([r11()], c7.prototype, "vvSize", void 0), e([r11()], c7.prototype, "vvColor", void 0), e([r11()], c7.prototype, "receiveShadows", void 0), e([r11()], c7.prototype, "receiveAmbientOcclusion", void 0), e([r11()], c7.prototype, "textureAlphaPremultiplied", void 0), e([r11()], c7.prototype, "instanced", void 0), e([r11()], c7.prototype, "instancedColor", void 0), e([r11()], c7.prototype, "objectAndLayerIdColorInstanced", void 0), e([r11()], c7.prototype, "instancedDoublePrecision", void 0), e([r11()], c7.prototype, "doublePrecisionRequiresObfuscation", void 0), e([r11()], c7.prototype, "writeDepth", void 0), e([r11()], c7.prototype, "transparent", void 0), e([r11()], c7.prototype, "enableOffset", void 0), e([r11()], c7.prototype, "cullAboveGround", void 0), e([r11()], c7.prototype, "snowCover", void 0), e([r11()], c7.prototype, "hasColorTextureTransform", void 0), e([r11()], c7.prototype, "hasEmissionTextureTransform", void 0), e([r11()], c7.prototype, "hasNormalTextureTransform", void 0), e([r11()], c7.prototype, "hasOcclusionTextureTransform", void 0), e([r11()], c7.prototype, "hasMetallicRoughnessTextureTransform", void 0), e([r11({ constValue: true })], c7.prototype, "hasVvInstancing", void 0), e([r11({ constValue: false })], c7.prototype, "useCustomDTRExponentForWater", void 0), e([r11({ constValue: false })], c7.prototype, "supportsTextureAtlas", void 0), e([r11({ constValue: true })], c7.prototype, "useFillLights", void 0);

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/RealisticTreeTechnique.js
var t10 = class _t extends _2 {
  initializeConfiguration(i7, a5) {
    super.initializeConfiguration(i7, a5), a5.hasMetallicRoughnessTexture = false, a5.hasEmissionTexture = false, a5.hasOcclusionTexture = false, a5.hasNormalTexture = false, a5.hasModelTransformation = false, a5.normalType = a4.Attribute, a5.doubleSidedMode = i6.WindingOrder, a5.hasVertexTangents = false;
  }
  initializeProgram(e14) {
    return this._initializeProgram(e14, _t.shader);
  }
};
t10.shader = new t8(R, () => import("./RealisticTree.glsl-JDNIV4HV.js"));

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/DefaultMaterial.js
var E6 = class extends h4 {
  constructor(e14) {
    super(e14, j), this.supportsEdges = true, this._configuration = new c7(), this._vertexBufferLayout = L2(this.parameters);
  }
  isVisibleForOutput(e14) {
    return e14 !== h2.Shadow && e14 !== h2.ShadowExcludeHighlight && e14 !== h2.ShadowHighlight || this.parameters.castShadows;
  }
  isVisible() {
    const t11 = this.parameters;
    if (!super.isVisible() || 0 === t11.layerOpacity) return false;
    const { instanced: r12, hasVertexColors: a5, hasSymbolColors: s7, vvColorEnabled: i7 } = t11, o9 = r(r12) && r12.includes("color"), n5 = "replace" === t11.colorMixMode, h6 = t11.opacity > 0, c8 = t11.externalColor && t11.externalColor[3] > 0;
    return a5 && (o9 || i7 || s7) ? !!n5 || h6 : a5 ? n5 ? c8 : h6 : o9 || i7 || s7 ? !!n5 || h6 : n5 ? c8 : h6;
  }
  getConfiguration(t11, r12) {
    return this._configuration.output = t11, this._configuration.hasNormalTexture = !!this.parameters.normalTextureId, this._configuration.hasColorTexture = !!this.parameters.textureId, this._configuration.hasVertexTangents = this.parameters.hasVertexTangents, this._configuration.instanced = !!this.parameters.instanced, this._configuration.instancedDoublePrecision = this.parameters.instancedDoublePrecision, this._configuration.vvSize = this.parameters.vvSizeEnabled, this._configuration.hasVerticalOffset = r(this.parameters.verticalOffset), this._configuration.hasScreenSizePerspective = r(this.parameters.screenSizePerspective), this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.hasSliceHighlight = this.parameters.hasSliceHighlight, this._configuration.alphaDiscardMode = this.parameters.textureAlphaMode, this._configuration.normalType = this.parameters.normalType, this._configuration.transparent = this.parameters.transparent, this._configuration.writeDepth = this.parameters.writeDepth, r(this.parameters.customDepthTest) && (this._configuration.customDepthTest = this.parameters.customDepthTest), this._configuration.hasOccludees = this.parameters.hasOccludees, this._configuration.cullFace = this.parameters.hasSlicePlane ? n4.None : this.parameters.cullFace, this._configuration.hasMultipassTerrain = r12.multipassTerrain.enabled, this._configuration.cullAboveGround = r12.multipassTerrain.cullAboveGround, this._configuration.hasModelTransformation = r(this.parameters.modelTransformation), t11 !== h2.Color && t11 !== h2.Alpha || (this._configuration.hasVertexColors = this.parameters.hasVertexColors, this._configuration.hasSymbolColors = this.parameters.hasSymbolColors, this.parameters.treeRendering ? this._configuration.doubleSidedMode = i6.WindingOrder : this._configuration.doubleSidedMode = this.parameters.doubleSided && "normal" === this.parameters.doubleSidedType ? i6.View : this.parameters.doubleSided && "winding-order" === this.parameters.doubleSidedType ? i6.WindingOrder : i6.None, this._configuration.instancedColor = r(this.parameters.instanced) && this.parameters.instanced.includes("color"), this._configuration.receiveShadows = this.parameters.receiveShadows && this.parameters.shadowMappingEnabled, this._configuration.receiveAmbientOcclusion = !!r12.ssaoHelper.active && this.parameters.receiveSSAO, this._configuration.vvColor = this.parameters.vvColorEnabled, this._configuration.textureAlphaPremultiplied = !!this.parameters.textureAlphaPremultiplied, this._configuration.pbrMode = this.parameters.usePBR ? this.parameters.isSchematic ? d2.Schematic : d2.Normal : d2.Disabled, this._configuration.hasMetallicRoughnessTexture = !!this.parameters.metallicRoughnessTextureId, this._configuration.hasEmissionTexture = !!this.parameters.emissiveTextureId, this._configuration.hasOcclusionTexture = !!this.parameters.occlusionTextureId, this._configuration.offsetBackfaces = !(!this.parameters.transparent || !this.parameters.offsetTransparentBackfaces), this._configuration.transparencyPassType = r12.transparencyPassType, this._configuration.enableOffset = r12.camera.relativeElevation < S3, this._configuration.snowCover = this.hasSnowCover(r12), this._configuration.hasColorTextureTransform = !!this.parameters.colorTextureTransformMatrix, this._configuration.hasNormalTextureTransform = !!this.parameters.normalTextureTransformMatrix, this._configuration.hasEmissionTextureTransform = !!this.parameters.emissiveTextureTransformMatrix, this._configuration.hasOcclusionTextureTransform = !!this.parameters.occlusionTextureTransformMatrix, this._configuration.hasMetallicRoughnessTextureTransform = !!this.parameters.metallicRoughnessTextureTransformMatrix), this._configuration;
  }
  hasSnowCover(t11) {
    return r(t11.weather) && t11.weatherVisible && "snowy" === t11.weather.type && "enabled" === t11.weather.snowCover;
  }
  intersect(c8, l3, m2, p, d3, f5) {
    if (r(this.parameters.verticalOffset)) {
      const e14 = m2.camera;
      o(z2, l3[12], l3[13], l3[14]);
      let c9 = null;
      switch (m2.viewingMode) {
        case l.Global:
          c9 = z(B, z2);
          break;
        case l.Local:
          c9 = r3(B, N3);
      }
      let f6 = 0;
      const g3 = e4(G2, z2, e14.eye), T3 = s3(g3), _3 = g(g3, g3, 1 / T3);
      let x4 = null;
      this.parameters.screenSizePerspective && (x4 = P(c9, _3)), f6 += N(e14, T3, this.parameters.verticalOffset, x4 ?? 0, this.parameters.screenSizePerspective), g(c9, c9, f6), S(H, c9, m2.transform.inverseRotation), p = e4(D2, p, H), d3 = e4(V, d3, H);
    }
    x3(c8, m2, p, d3, y(m2.verticalOffset), f5);
  }
  requiresSlot(e14, t11) {
    if (t11 === h2.Color || t11 === h2.Alpha || t11 === h2.Depth || t11 === h2.Normal || t11 === h2.Shadow || t11 === h2.ShadowHighlight || t11 === h2.ShadowExcludeHighlight || t11 === h2.Highlight || t11 === h2.ObjectAndLayerIdColor) {
      return e14 === (this.parameters.transparent ? this.parameters.writeDepth ? E4.TRANSPARENT_MATERIAL : E4.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL : E4.OPAQUE_MATERIAL) || e14 === E4.DRAPED_MATERIAL;
    }
    return false;
  }
  createGLMaterial(e14) {
    return new I2(e14);
  }
  createBufferWriter() {
    return new r10(this._vertexBufferLayout);
  }
};
var I2 = class extends h3 {
  constructor(e14) {
    super({ ...e14, ...e14.material.parameters });
  }
  _updateShadowState(e14) {
    e14.shadowMap.enabled !== this._material.parameters.shadowMappingEnabled && this._material.setParameters({ shadowMappingEnabled: e14.shadowMap.enabled });
  }
  _updateOccludeeState(e14) {
    e14.hasOccludees !== this._material.parameters.hasOccludees && this._material.setParameters({ hasOccludees: e14.hasOccludees });
  }
  beginSlot(e14) {
    this._output !== h2.Color && this._output !== h2.Alpha || (this._updateShadowState(e14), this._updateOccludeeState(e14));
    const r12 = this._material.parameters;
    this.updateTexture(r12.textureId);
    const a5 = e14.camera.viewInverseTransposeMatrix;
    return o(r12.origin, a5[3], a5[7], a5[11]), this._material.setParameters(this.textureBindParameters), this.ensureTechnique(r12.treeRendering ? t10 : _2, e14);
  }
};
var P2 = class extends k {
  constructor() {
    super(...arguments), this.initTextureTransparent = false, this.treeRendering = false, this.hasVertexTangents = false;
  }
};
var j = new P2();
function L2(e14) {
  const t11 = T().vec3f(O3.POSITION).vec3f(O3.NORMAL), r12 = e14.textureId || e14.normalTextureId || e14.metallicRoughnessTextureId || e14.emissiveTextureId || e14.occlusionTextureId;
  return e14.hasVertexTangents && t11.vec4f(O3.TANGENT), r12 && t11.vec2f(O3.UV0), e14.hasVertexColors && t11.vec4u8(O3.COLOR), e14.hasSymbolColors && t11.vec4u8(O3.SYMBOLCOLOR), has("enable-feature:objectAndLayerId-rendering") && t11.vec4u8(O3.OBJECTANDLAYERIDCOLOR), t11;
}
var D2 = n();
var V = n();
var N3 = r2(0, 0, 1);
var B = n();
var H = n();
var z2 = n();
var G2 = n();

// node_modules/@arcgis/core/views/3d/layers/graphics/wosrLoader.js
var j2 = s.getLogger("esri.views.3d.layers.graphics.objectResourceUtils");
async function A3(e14, t11) {
  const n5 = await M(e14, t11), s7 = await k2(n5.textureDefinitions ?? {}, t11);
  let o9 = 0;
  for (const r12 in s7) if (s7.hasOwnProperty(r12)) {
    const e15 = s7[r12];
    o9 += (e15 == null ? void 0 : e15.image) ? e15.image.width * e15.image.height * 4 : 0;
  }
  return { resource: n5, textures: s7, size: o9 + e8(n5) };
}
async function M(r12, n5) {
  const s7 = r(n5) && n5.streamDataRequester;
  if (s7) return P3(r12, s7, n5);
  const i7 = await b(U(r12, e2(n5)));
  if (true === i7.ok) return i7.value.data;
  w(i7.error), U2(i7.error);
}
async function P3(e14, r12, n5) {
  const s7 = await b(r12.request(e14, "json", n5));
  if (true === s7.ok) return s7.value;
  w(s7.error), U2(s7.error.details.url);
}
function U2(e14) {
  throw new s2("", `Request for object resource failed: ${e14}`);
}
function E7(e14) {
  const t11 = e14.params, r12 = t11.topology;
  let n5 = true;
  switch (t11.vertexAttributes || (j2.warn("Geometry must specify vertex attributes"), n5 = false), t11.topology) {
    case "PerAttributeArray":
      break;
    case "Indexed":
    case null:
    case void 0: {
      const e15 = t11.faces;
      if (e15) {
        if (t11.vertexAttributes) for (const r13 in t11.vertexAttributes) {
          const t12 = e15[r13];
          t12 && t12.values ? (null != t12.valueType && "UInt32" !== t12.valueType && (j2.warn(`Unsupported indexed geometry indices type '${t12.valueType}', only UInt32 is currently supported`), n5 = false), null != t12.valuesPerElement && 1 !== t12.valuesPerElement && (j2.warn(`Unsupported indexed geometry values per element '${t12.valuesPerElement}', only 1 is currently supported`), n5 = false)) : (j2.warn(`Indexed geometry does not specify face indices for '${r13}' attribute`), n5 = false);
        }
      } else j2.warn("Indexed geometries must specify faces"), n5 = false;
      break;
    }
    default:
      j2.warn(`Unsupported topology '${r12}'`), n5 = false;
  }
  e14.params.material || (j2.warn("Geometry requires material"), n5 = false);
  const s7 = e14.params.vertexAttributes;
  for (const o9 in s7) {
    s7[o9].values || (j2.warn("Geometries with externally defined attributes are not yet supported"), n5 = false);
  }
  return n5;
}
function I3(e14, t11) {
  const r12 = new Array(), n5 = new Array(), s7 = new Array(), a5 = new t6(), l3 = e14.resource, m2 = r8.parse(l3.version || "1.0", "wosr");
  R2.validate(m2);
  const f5 = l3.model.name, d3 = l3.model.geometries, b2 = l3.materialDefinitions ?? {}, v3 = e14.textures;
  let j3 = 0;
  const A4 = /* @__PURE__ */ new Map();
  for (let u3 = 0; u3 < d3.length; u3++) {
    const e15 = d3[u3];
    if (!E7(e15)) continue;
    const l4 = O4(e15), c8 = e15.params.vertexAttributes, m3 = [];
    for (const t12 in c8) {
      const e16 = c8[t12], r13 = e16.values;
      m3.push([t12, new s4(r13, e16.valuesPerElement, true)]);
    }
    const f6 = [];
    if ("PerAttributeArray" !== e15.params.topology) {
      const t12 = e15.params.faces;
      for (const e16 in t12) f6.push([e16, t12[e16].values]);
    }
    const M2 = l4.texture, P4 = v3 && v3[M2];
    if (P4 && !A4.has(M2)) {
      const { image: e16, params: t12 } = P4, r13 = new G(e16, t12);
      n5.push(r13), A4.set(M2, r13);
    }
    const U3 = A4.get(M2), I4 = U3 ? U3.id : void 0, T3 = l4.material;
    let k3 = a5.get(T3, M2);
    if (t(k3)) {
      const e16 = b2[T3.substring(T3.lastIndexOf("/") + 1)].params;
      1 === e16.transparency && (e16.transparency = 0);
      const r13 = P4 && P4.alphaChannelUsage, n6 = e16.transparency > 0 || "transparency" === r13 || "maskAndTransparency" === r13, s8 = P4 ? q(P4.alphaChannelUsage) : void 0, i7 = { ambient: e3(e16.diffuse), diffuse: e3(e16.diffuse), opacity: 1 - (e16.transparency || 0), transparent: n6, textureAlphaMode: s8, textureAlphaCutoff: 0.33, textureId: I4, initTextureTransparent: true, doubleSided: true, cullFace: n4.None, colorMixMode: e16.externalColorMixMode || "tint", textureAlphaPremultiplied: !!P4 && !!P4.params.preMultiplyAlpha };
      r(t11) && t11.materialParamsMixin && Object.assign(i7, t11.materialParamsMixin), k3 = new E6(i7), a5.set(T3, M2, k3);
    }
    s7.push(k3);
    const R3 = new v(k3, m3, f6);
    j3 += f6.position ? f6.position.length : 0, r12.push(R3);
  }
  return { engineResources: [{ name: f5, stageResources: { textures: n5, materials: s7, geometries: r12 }, pivotOffset: l3.model.pivotOffset, numberOfVertices: j3, lodThreshold: null }], referenceBoundingBox: T2(r12) };
}
function T2(e14) {
  const t11 = S2();
  return e14.forEach((e15) => {
    const r12 = e15.boundingInfo;
    r(r12) && (c(t11, r12.bbMin), c(t11, r12.bbMax));
  }), t11;
}
async function k2(e14, t11) {
  const r12 = [];
  for (const a5 in e14) {
    const n6 = e14[a5], s8 = n6.images[0].data;
    if (!s8) {
      j2.warn("Externally referenced texture data is not yet supported");
      continue;
    }
    const i7 = n6.encoding + ";base64," + s8, u3 = "/textureDefinitions/" + a5, l3 = "rgba" === n6.channels ? n6.alphaChannelUsage || "transparency" : "none", c8 = { noUnpackFlip: true, wrap: { s: D.REPEAT, t: D.REPEAT }, preMultiplyAlpha: q(l3) !== u2.Opaque }, p = r(t11) && t11.disableTextures ? Promise.resolve(null) : t7(i7, t11);
    r12.push(p.then((e15) => ({ refId: u3, image: e15, params: c8, alphaChannelUsage: l3 })));
  }
  const n5 = await Promise.all(r12), s7 = {};
  for (const o9 of n5) s7[o9.refId] = o9;
  return s7;
}
function q(e14) {
  switch (e14) {
    case "mask":
      return u2.Mask;
    case "maskAndTransparency":
      return u2.MaskBlend;
    case "none":
      return u2.Opaque;
    default:
      return u2.Blend;
  }
}
function O4(e14) {
  const t11 = e14.params;
  return { id: 1, material: t11.material, texture: t11.texture, region: t11.texture };
}
var R2 = new r8(1, 2, "wosr");

// node_modules/@arcgis/core/views/3d/layers/graphics/objectResourceUtils.js
async function te(o9, s7) {
  const i7 = re(a(o9));
  if ("wosr" === i7.fileType) {
    const e14 = await (s7.cache ? s7.cache.loadWOSR(i7.url, s7) : A3(i7.url, s7)), { engineResources: t11, referenceBoundingBox: r12 } = I3(e14, s7);
    return { lods: t11, referenceBoundingBox: r12, isEsriSymbolResource: false, isWosr: true };
  }
  const n5 = await (s7.cache ? s7.cache.loadGLTF(i7.url, s7, !!s7.usePBR) : m(new n3(s7.streamDataRequester), i7.url, s7, s7.usePBR)), a5 = x(n5.model.meta, "ESRI_proxyEllipsoid"), u3 = n5.meta.isEsriSymbolResource && r(a5) && n5.meta.uri.includes("/RealisticTrees/");
  u3 && !n5.customMeta.esriTreeRendering && (n5.customMeta.esriTreeRendering = true, le(n5, a5));
  const l3 = !!s7.usePBR, c8 = n5.meta.isEsriSymbolResource ? { usePBR: l3, isSchematic: false, treeRendering: u3, mrrFactors: [0, 1, 0.2] } : { usePBR: l3, isSchematic: false, treeRendering: false, mrrFactors: [0, 1, 0.5] }, m2 = { ...s7.materialParamsMixin, treeRendering: u3 }, { engineResources: f5, referenceBoundingBox: d3 } = oe(n5, c8, m2, s7.skipHighLods && null == i7.specifiedLodIndex ? { skipHighLods: true } : { skipHighLods: false, singleLodIndex: i7.specifiedLodIndex });
  return { lods: f5, referenceBoundingBox: d3, isEsriSymbolResource: n5.meta.isEsriSymbolResource, isWosr: false };
}
function re(e14) {
  const t11 = e14.match(/(.*\.(gltf|glb))(\?lod=([0-9]+))?$/);
  if (t11) return { fileType: "gltf", url: t11[1], specifiedLodIndex: null != t11[4] ? Number(t11[4]) : null };
  return e14.match(/(.*\.(json|json\.gz))$/) ? { fileType: "wosr", url: e14, specifiedLodIndex: null } : { fileType: "unknown", url: e14, specifiedLodIndex: null };
}
function oe(e14, t11, o9, s7) {
  const i7 = e14.model, n5 = new Array(), a5 = /* @__PURE__ */ new Map(), u3 = /* @__PURE__ */ new Map(), l3 = i7.lods.length, c8 = S2();
  return i7.lods.forEach((e15, m2) => {
    const f5 = true === s7.skipHighLods && (l3 > 1 && 0 === m2 || l3 > 3 && 1 === m2) || false === s7.skipHighLods && null != s7.singleLodIndex && m2 !== s7.singleLodIndex;
    if (f5 && 0 !== m2) return;
    const d3 = new t9(e15.name, e15.lodThreshold, [0, 0, 0]);
    e15.parts.forEach((e16) => {
      const s8 = f5 ? new E6({}) : se(i7, e16, d3, t11, o9, a5, u3), { geometry: n6, vertexCount: l4 } = ie(e16, r(s8) ? s8 : new E6({})), p = n6.boundingInfo;
      r(p) && 0 === m2 && (c(c8, p.bbMin), c(c8, p.bbMax)), r(s8) && (d3.stageResources.geometries.push(n6), d3.numberOfVertices += l4);
    }), f5 || n5.push(d3);
  }), { engineResources: n5, referenceBoundingBox: c8 };
}
function se(e14, t11, s7, i7, n5, a5, u3) {
  const l3 = t11.material + (t11.attributes.normal ? "_normal" : "") + (t11.attributes.color ? "_color" : "") + (t11.attributes.texCoord0 ? "_texCoord0" : "") + (t11.attributes.tangent ? "_tangent" : ""), c8 = e14.materials.get(t11.material), m2 = r(t11.attributes.texCoord0), f5 = r(t11.attributes.normal);
  if (t(c8)) return null;
  const d3 = ae(c8.alphaMode);
  if (!a5.has(l3)) {
    if (m2) {
      const t12 = (t13, o10 = false) => {
        if (r(t13) && !u3.has(t13)) {
          const s9 = e14.textures.get(t13);
          if (r(s9)) {
            const e15 = s9.data;
            u3.set(t13, new G(t4(e15) ? e15.data : e15, { ...s9.parameters, preMultiplyAlpha: !t4(e15) && o10, encoding: t4(e15) && r(e15.encoding) ? e15.encoding : void 0 }));
          }
        }
      };
      t12(c8.textureColor, d3 !== u2.Opaque), t12(c8.textureNormal), t12(c8.textureOcclusion), t12(c8.textureEmissive), t12(c8.textureMetallicRoughness);
    }
    const o9 = c8.color[0] ** (1 / o4), s8 = c8.color[1] ** (1 / o4), p2 = c8.color[2] ** (1 / o4), g3 = c8.emissiveFactor[0] ** (1 / o4), b2 = c8.emissiveFactor[1] ** (1 / o4), x4 = c8.emissiveFactor[2] ** (1 / o4), h6 = r(c8.textureColor) && m2 ? u3.get(c8.textureColor) : null;
    a5.set(l3, new E6({ ...i7, transparent: d3 === u2.Blend, customDepthTest: e10.Lequal, textureAlphaMode: d3, textureAlphaCutoff: c8.alphaCutoff, diffuse: [o9, s8, p2], ambient: [o9, s8, p2], opacity: c8.opacity, doubleSided: c8.doubleSided, doubleSidedType: "winding-order", cullFace: c8.doubleSided ? n4.None : n4.Back, hasVertexColors: !!t11.attributes.color, hasVertexTangents: !!t11.attributes.tangent, normalType: f5 ? a4.Attribute : a4.ScreenDerivative, castShadows: true, receiveSSAO: true, textureId: r(h6) ? h6.id : void 0, colorMixMode: c8.colorMixMode, normalTextureId: r(c8.textureNormal) && m2 ? u3.get(c8.textureNormal).id : void 0, textureAlphaPremultiplied: r(h6) && !!h6.params.preMultiplyAlpha, occlusionTextureId: r(c8.textureOcclusion) && m2 ? u3.get(c8.textureOcclusion).id : void 0, emissiveTextureId: r(c8.textureEmissive) && m2 ? u3.get(c8.textureEmissive).id : void 0, metallicRoughnessTextureId: r(c8.textureMetallicRoughness) && m2 ? u3.get(c8.textureMetallicRoughness).id : void 0, emissiveFactor: [g3, b2, x4], mrrFactors: [c8.metallicFactor, c8.roughnessFactor, i7.mrrFactors[2]], isSchematic: false, colorTextureTransformMatrix: c6(c8.colorTextureTransform), normalTextureTransformMatrix: c6(c8.normalTextureTransform), occlusionTextureTransformMatrix: c6(c8.occlusionTextureTransform), emissiveTextureTransformMatrix: c6(c8.emissiveTextureTransform), metallicRoughnessTextureTransformMatrix: c6(c8.metallicRoughnessTextureTransform), ...n5 }));
  }
  const p = a5.get(l3);
  if (s7.stageResources.materials.push(p), m2) {
    const e15 = (e16) => {
      r(e16) && s7.stageResources.textures.push(u3.get(e16));
    };
    e15(c8.textureColor), e15(c8.textureNormal), e15(c8.textureOcclusion), e15(c8.textureEmissive), e15(c8.textureMetallicRoughness);
  }
  return p;
}
function ie(e14, t11) {
  const o9 = e14.attributes.position.count, i7 = ue(e14.indices || o9, e14.primitiveType), n5 = r7(i2, o9);
  t2(n5, e14.attributes.position, e14.transform);
  const a5 = [[O3.POSITION, new s4(n5.typedBuffer, n5.elementCount, true)]], u3 = [[O3.POSITION, i7]];
  if (r(e14.attributes.normal)) {
    const t12 = r7(i2, o9);
    g2(ne, e14.transform), r5(t12, e14.attributes.normal, ne), a5.push([O3.NORMAL, new s4(t12.typedBuffer, t12.elementCount, true)]), u3.push([O3.NORMAL, i7]);
  }
  if (r(e14.attributes.tangent)) {
    const t12 = r7(c2, o9);
    g2(ne, e14.transform), r6(t12, e14.attributes.tangent, ne), a5.push([O3.TANGENT, new s4(t12.typedBuffer, t12.elementCount, true)]), u3.push([O3.TANGENT, i7]);
  }
  if (r(e14.attributes.texCoord0)) {
    const t12 = r7(u, o9);
    n2(t12, e14.attributes.texCoord0), a5.push([O3.UV0, new s4(t12.typedBuffer, t12.elementCount, true)]), u3.push([O3.UV0, i7]);
  }
  if (r(e14.attributes.color)) {
    const t12 = r7(x2, o9);
    if (4 === e14.attributes.color.elementCount) e14.attributes.color instanceof c2 ? o2(t12, e14.attributes.color, 255) : e14.attributes.color instanceof x2 ? e9(t12, e14.attributes.color) : e14.attributes.color instanceof L && o2(t12, e14.attributes.color, 1 / 256);
    else {
      t3(t12, 255, 255, 255, 255);
      const r12 = new O2(t12.buffer, 0, 4);
      e14.attributes.color instanceof i2 ? f(r12, e14.attributes.color, 255) : e14.attributes.color instanceof O2 ? e7(r12, e14.attributes.color) : e14.attributes.color instanceof E && f(r12, e14.attributes.color, 1 / 256);
    }
    a5.push([O3.COLOR, new s4(t12.typedBuffer, t12.elementCount, true)]), u3.push([O3.COLOR, i7]);
  }
  return { geometry: new v(t11, a5, u3), vertexCount: o9 };
}
var ne = e5();
function ae(e14) {
  switch (e14) {
    case "BLEND":
      return u2.Blend;
    case "MASK":
      return u2.Mask;
    case "OPAQUE":
    case null:
    case void 0:
      return u2.Opaque;
  }
}
function ue(e14, t11) {
  switch (t11) {
    case E2.TRIANGLES:
      return o3(e14);
    case E2.TRIANGLE_STRIP:
      return f2(e14);
    case E2.TRIANGLE_FAN:
      return i5(e14);
  }
}
function le(e14, t11) {
  for (let r12 = 0; r12 < e14.model.lods.length; ++r12) {
    const s7 = e14.model.lods[r12];
    for (const i7 of s7.parts) {
      const s8 = i7.attributes.normal;
      if (t(s8)) return;
      const g3 = i7.attributes.position, b2 = g3.count, h6 = n(), T3 = n(), w2 = n(), j3 = r7(x2, b2), M2 = r7(i2, b2), v3 = h(e6(), i7.transform);
      for (let o9 = 0; o9 < b2; o9++) {
        g3.getVec(o9, T3), s8.getVec(o9, h6), O(T3, T3, i7.transform), e4(w2, T3, t11.center), i(w2, w2, t11.radius);
        const n5 = w2[2], a5 = s3(w2), p = Math.min(0.45 + 0.55 * a5 * a5, 1);
        i(w2, w2, t11.radius), null !== v3 && O(w2, w2, v3), z(w2, w2), r12 + 1 !== e14.model.lods.length && e14.model.lods.length > 1 && A(w2, w2, h6, n5 > -1 ? 0.2 : Math.min(-4 * n5 - 3.8, 1)), M2.setVec(o9, w2), j3.set(o9, 0, 255 * p), j3.set(o9, 1, 255 * p), j3.set(o9, 2, 255 * p), j3.set(o9, 3, 255);
      }
      i7.attributes.normal = M2, i7.attributes.color = j3;
    }
  }
}

export {
  c6 as c,
  E5 as E,
  E6 as E2,
  te,
  re,
  oe
};
//# sourceMappingURL=chunk-V27ZN6IB.js.map
