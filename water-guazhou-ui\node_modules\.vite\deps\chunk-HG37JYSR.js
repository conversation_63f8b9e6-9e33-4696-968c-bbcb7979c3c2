import {
  i
} from "./chunk-I73RJE3H.js";
import {
  w as w3
} from "./chunk-SJMI5Q5M.js";
import {
  n
} from "./chunk-PCLDCFRI.js";
import {
  v as v2
} from "./chunk-ZJC3GHA7.js";
import {
  C,
  b as b2,
  d,
  w as w2,
  y as y2
} from "./chunk-HTXGAKOK.js";
import {
  b
} from "./chunk-P37TUI4J.js";
import {
  x as x2
} from "./chunk-6NE6A2GD.js";
import {
  f as f3,
  v
} from "./chunk-VJW7RCN7.js";
import {
  b as b3
} from "./chunk-QMNV7QQK.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  f2
} from "./chunk-JXLVNWKF.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  E,
  f,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/I3SIndexInfo.js
async function n2(n5, t, s3, a3, i2, d2) {
  let l = null;
  if (r(s3)) {
    const o2 = `${n5}/nodepages/`, t2 = o2 + Math.floor(s3.rootIndex / s3.nodesPerPage);
    try {
      return { type: "page", rootPage: (await U(t2, { query: { f: "json", token: a3 }, responseType: "json", signal: d2 })).data, rootIndex: s3.rootIndex, pageSize: s3.nodesPerPage, lodMetric: s3.lodSelectionMetricType, urlPrefix: o2 };
    } catch (f4) {
      r(i2) && i2.warn("#fetchIndexInfo()", "Failed to load root node page. Falling back to node documents.", t2, f4), l = f4;
    }
  }
  if (!t) return null;
  const p = `${n5}/nodes/`, c = p + (t && t.split("/").pop());
  try {
    return { type: "node", rootNode: (await U(c, { query: { f: "json", token: a3 }, responseType: "json", signal: d2 })).data, urlPrefix: p };
  } catch (f4) {
    throw new s2("sceneservice:root-node-missing", "Root node missing.", { pageError: l, nodeError: f4, url: c });
  }
}

// node_modules/@arcgis/core/layers/support/schemaValidatorLoader.js
var n3 = null;
function u() {
  return n3;
}

// node_modules/@arcgis/core/webdoc/support/saveUtils.js
async function a2(r3, o2, a3) {
  if (!o2 || !o2.resources) return;
  const h = o2.portalItem === r3.portalItem ? new Set(r3.paths) : /* @__PURE__ */ new Set();
  r3.paths.length = 0, r3.portalItem = o2.portalItem;
  const i2 = new Set(o2.resources.toKeep.map((r4) => r4.resource.path)), m = /* @__PURE__ */ new Set(), f4 = [];
  i2.forEach((e2) => {
    h.delete(e2), r3.paths.push(e2);
  });
  for (const e2 of o2.resources.toUpdate) if (h.delete(e2.resource.path), i2.has(e2.resource.path) || m.has(e2.resource.path)) {
    const { resource: o3, content: t, finish: s3, error: u3 } = e2, h2 = w3(o3, n());
    r3.paths.push(h2.path), f4.push(n4({ resource: h2, content: t, compress: e2.compress, finish: s3, error: u3 }, a3));
  } else r3.paths.push(e2.resource.path), f4.push(u2(e2, a3)), m.add(e2.resource.path);
  for (const e2 of o2.resources.toAdd) f4.push(n4(e2, a3)), r3.paths.push(e2.resource.path);
  if (h.forEach((r4) => {
    if (o2.portalItem) {
      const e2 = o2.portalItem.resourceFromPath(r4);
      f4.push(e2.portalItem.removeResource(e2).catch(() => {
      }));
    }
  }), 0 === f4.length) return;
  const l = await E(f4);
  f(a3);
  const d2 = l.filter((r4) => "error" in r4).map((r4) => r4.error);
  if (d2.length > 0) throw new s2("save:resources", "Failed to save one or more resources", { errors: d2 });
}
async function n4(e2, t) {
  var _a, _b;
  const s3 = { ...r(t) ? t : {}, compress: e2.compress }, c = await b(e2.resource.portalItem.addResource(e2.resource, e2.content, s3));
  if (true !== c.ok) throw (_a = e2.error) == null ? void 0 : _a.call(e2, c.error), c.error;
  (_b = e2.finish) == null ? void 0 : _b.call(e2, e2.resource);
}
async function u2(e2, o2) {
  var _a, _b;
  const t = await b(e2.resource.update(e2.content, o2));
  if (true !== t.ok) throw (_a = e2.error) == null ? void 0 : _a.call(e2, t.error), t.error;
  (_b = e2.finish) == null ? void 0 : _b.call(e2, e2.resource);
}

// node_modules/@arcgis/core/layers/mixins/SceneService.js
var A = "esri.layers.mixins.SceneService";
var N = s.getLogger(A);
var E2 = (o2) => {
  let E3 = class extends o2 {
    constructor() {
      super(...arguments), this.spatialReference = null, this.fullExtent = null, this.heightModelInfo = null, this.minScale = 0, this.maxScale = 0, this.version = { major: Number.NaN, minor: Number.NaN, versionString: "" }, this.copyright = null, this.sublayerTitleMode = "item-title", this.title = null, this.layerId = null, this.indexInfo = null, this._debouncedSaveOperations = x(async (e2, t, r3) => {
        switch (e2) {
          case L2.SAVE:
            return this._save(t);
          case L2.SAVE_AS:
            return this._saveAs(r3, t);
        }
      });
    }
    readSpatialReference(e2, t) {
      return this._readSpatialReference(t);
    }
    _readSpatialReference(e2) {
      if (null != e2.spatialReference) return f2.fromJSON(e2.spatialReference);
      {
        const t = e2.store, r3 = t.indexCRS || t.geographicCRS, o3 = r3 && parseInt(r3.substring(r3.lastIndexOf("/") + 1, r3.length), 10);
        return null != o3 ? new f2(o3) : null;
      }
    }
    readFullExtent(e2, t, r3) {
      if (null != e2 && "object" == typeof e2) {
        const o4 = null == e2.spatialReference ? { ...e2, spatialReference: this._readSpatialReference(t) } : e2;
        return w.fromJSON(o4, r3);
      }
      const o3 = t.store, i2 = this._readSpatialReference(t);
      return null == i2 || null == o3 || null == o3.extent || !Array.isArray(o3.extent) || o3.extent.some((e3) => e3 < U2) ? null : new w({ xmin: o3.extent[0], ymin: o3.extent[1], xmax: o3.extent[2], ymax: o3.extent[3], spatialReference: i2 });
    }
    parseVersionString(e2) {
      const t = { major: Number.NaN, minor: Number.NaN, versionString: e2 }, r3 = e2.split(".");
      return r3.length >= 2 && (t.major = parseInt(r3[0], 10), t.minor = parseInt(r3[1], 10)), t;
    }
    readVersion(e2, t) {
      const r3 = t.store, o3 = null != r3.version ? r3.version.toString() : "";
      return this.parseVersionString(o3);
    }
    readTitlePortalItem(e2) {
      return "item-title" !== this.sublayerTitleMode ? void 0 : e2;
    }
    readTitleService(e2, t) {
      const r3 = this.portalItem && this.portalItem.title;
      if ("item-title" === this.sublayerTitleMode) return y2(this.url, t.name);
      let o3 = t.name;
      if (!o3 && this.url) {
        const e3 = d(this.url);
        r(e3) && (o3 = e3.title);
      }
      return "item-title-and-service-name" === this.sublayerTitleMode && r3 && (o3 = r3 + " - " + o3), w2(o3);
    }
    set url(e2) {
      const t = b2({ layer: this, url: e2, nonStandardUrlAllowed: false, logger: N });
      this._set("url", t.url), null != t.layerId && this._set("layerId", t.layerId);
    }
    writeUrl(e2, t, r3, o3) {
      C(this, e2, "layers", t, o3);
    }
    get parsedUrl() {
      const e2 = this._get("url"), t = L(e2);
      return null != this.layerId && (t.path = `${t.path}/layers/${this.layerId}`), t;
    }
    async _fetchIndexAndUpdateExtent(e2, t) {
      this.indexInfo = n2(this.parsedUrl.path, this.rootNode, e2, this.apiKey, N, t), null == this.fullExtent || this.fullExtent.hasZ || this._updateExtent(await this.indexInfo);
    }
    _updateExtent(e2) {
      var _a, _b, _c;
      if ("page" === (e2 == null ? void 0 : e2.type)) {
        const t = e2.rootIndex % e2.pageSize, o3 = (_b = (_a = e2.rootPage) == null ? void 0 : _a.nodes) == null ? void 0 : _b[t];
        if (null == o3 || null == o3.obb || null == o3.obb.center || null == o3.obb.halfSize) throw new s2("sceneservice:invalid-node-page", "Invalid node page.");
        if (o3.obb.center[0] < U2 || null == this.fullExtent || this.fullExtent.hasZ) return;
        const i2 = o3.obb.halfSize, s3 = o3.obb.center[2], a3 = Math.sqrt(i2[0] * i2[0] + i2[1] * i2[1] + i2[2] * i2[2]);
        this.fullExtent.zmin = s3 - a3, this.fullExtent.zmax = s3 + a3;
      } else if ("node" === (e2 == null ? void 0 : e2.type)) {
        const t = (_c = e2.rootNode) == null ? void 0 : _c.mbs;
        if (!Array.isArray(t) || 4 !== t.length || t[0] < U2) return;
        const r3 = t[2], o3 = t[3], { fullExtent: i2 } = this;
        i2 && (i2.zmin = r3 - o3, i2.zmax = r3 + o3);
      }
    }
    async _fetchService(e2) {
      if (null == this.url) throw new s2("sceneservice:url-not-set", "Scene service can not be loaded without valid portal item or url");
      if (null == this.layerId && /SceneServer\/*$/i.test(this.url)) {
        const t = await this._fetchFirstLayerId(e2);
        null != t && (this.layerId = t);
      }
      return this._fetchServiceLayer(e2);
    }
    async _fetchFirstLayerId(e2) {
      const r3 = await U(this.url, { query: { f: "json", token: this.apiKey }, responseType: "json", signal: e2 });
      if (r3.data && Array.isArray(r3.data.layers) && r3.data.layers.length > 0) return r3.data.layers[0].id;
    }
    async _fetchServiceLayer(e2) {
      var _a;
      const r3 = await U(((_a = this.parsedUrl) == null ? void 0 : _a.path) ?? "", { query: { f: "json", token: this.apiKey }, responseType: "json", signal: e2 });
      r3.ssl && (this.url = this.url.replace(/^http:/i, "https:"));
      let o3 = false;
      if (r3.data.layerType && "Voxel" === r3.data.layerType && (o3 = true), o3) return this._fetchVoxelServiceLayer();
      const i2 = r3.data;
      this.read(i2, this._getServiceContext()), this.validateLayer(i2);
    }
    async _fetchVoxelServiceLayer(e2) {
      var _a;
      const r3 = (await U(((_a = this.parsedUrl) == null ? void 0 : _a.path) + "/layer", { query: { f: "json", token: this.apiKey }, responseType: "json", signal: e2 })).data;
      this.read(r3, this._getServiceContext()), this.validateLayer(r3);
    }
    _getServiceContext() {
      var _a;
      return { origin: "service", portalItem: this.portalItem, portal: (_a = this.portalItem) == null ? void 0 : _a.portal, url: this.parsedUrl };
    }
    async _ensureLoadBeforeSave() {
      await this.load(), "beforeSave" in this && "function" == typeof this.beforeSave && await this.beforeSave();
    }
    validateLayer(e2) {
    }
    _updateTypeKeywords(e2, t, r3) {
      e2.typeKeywords || (e2.typeKeywords = []);
      const o3 = t.getTypeKeywords();
      for (const i2 of o3) e2.typeKeywords.push(i2);
      e2.typeKeywords && (e2.typeKeywords = e2.typeKeywords.filter((e3, t2, r4) => r4.indexOf(e3) === t2), r3 === T.newItem && (e2.typeKeywords = e2.typeKeywords.filter((e3) => "Hosted Service" !== e3)));
    }
    async _saveAs(e2, t) {
      var _a;
      const o3 = { ...K, ...t };
      let i2 = x2.from(e2);
      i2 || (N.error("_saveAs(): requires a portal item parameter"), await Promise.reject(new s2("sceneservice:portal-item-required", "_saveAs() requires a portal item to save to"))), i2.id && (i2 = i2.clone(), i2.id = null);
      const s3 = i2.portal || b3.getDefault();
      await this._ensureLoadBeforeSave(), i2.type = O, i2.portal = s3;
      const a3 = { origin: "portal-item", url: null, messages: [], portal: s3, portalItem: i2, writtenProperties: [], blockedRelativeUrls: [], resources: { toAdd: [], toUpdate: [], toKeep: [], pendingOperations: [] } }, n5 = { layers: [this.write({}, a3)] };
      return await Promise.all(a3.resources.pendingOperations ?? []), await this._validateAgainstJSONSchema(n5, a3, o3), i2.url = this.url, i2.title || (i2.title = this.title), this._updateTypeKeywords(i2, o3, T.newItem), await s3.signIn(), await ((_a = s3.user) == null ? void 0 : _a.addItem({ item: i2, folder: o3 && o3.folder, data: n5 })), await a2(this.resourceReferences, a3, null), this.portalItem = i2, i(a3), a3.portalItem = i2, i2;
    }
    async _save(e2) {
      const t = { ...K, ...e2 };
      if (!this.portalItem) throw N.error("_save(): requires the .portalItem property to be set"), new s2("sceneservice:portal-item-not-set", "Portal item to save to has not been set on this SceneService");
      if (this.portalItem.type !== O) throw N.error("_save(): Non-matching portal item type. Got " + this.portalItem.type + ", expected " + O), new s2("sceneservice:portal-item-wrong-type", `Portal item needs to have type "${O}"`);
      await this._ensureLoadBeforeSave();
      const o3 = { origin: "portal-item", url: this.portalItem.itemUrl && L(this.portalItem.itemUrl), messages: [], portal: this.portalItem.portal || b3.getDefault(), portalItem: this.portalItem, writtenProperties: [], blockedRelativeUrls: [], resources: { toAdd: [], toUpdate: [], toKeep: [], pendingOperations: [] } }, i2 = { layers: [this.write({}, o3)] };
      return await Promise.all(o3.resources.pendingOperations ?? []), await this._validateAgainstJSONSchema(i2, o3, t), this.portalItem.url = this.url, this.portalItem.title || (this.portalItem.title = this.title), this._updateTypeKeywords(this.portalItem, t, T.existingItem), await this.portalItem.update({ data: i2 }), await a2(this.resourceReferences, o3, null), i(o3), this.portalItem;
    }
    async _validateAgainstJSONSchema(e2, t, o3) {
      var _a, _b;
      let i2 = ((_a = t.messages) == null ? void 0 : _a.filter((e3) => "error" === e3.type).map((e3) => new s2(e3.name, e3.message, e3.details))) ?? [];
      ((_b = o3 == null ? void 0 : o3.validationOptions) == null ? void 0 : _b.ignoreUnsupported) && (i2 = i2.filter((e3) => "layer:unsupported" !== e3.name && "symbol:unsupported" !== e3.name && "symbol-layer:unsupported" !== e3.name && "property:unsupported" !== e3.name && "url:unsupported" !== e3.name && "scenemodification:unsupported" !== e3.name));
      const s3 = o3 == null ? void 0 : o3.validationOptions, a3 = s3 == null ? void 0 : s3.enabled, n5 = u();
      if (a3 && n5) {
        const t2 = (await n5()).validate(e2, o3.portalItemLayerType);
        if (t2.length > 0) {
          const e3 = `Layer item did not validate:
${t2.join("\n")}`;
          if (N.error(`_validateAgainstJSONSchema(): ${e3}`), "throw" === s3.failPolicy) {
            const e4 = t2.map((e5) => new s2("sceneservice:schema-validation", e5)).concat(i2);
            throw new s2("sceneservice-validate:error", "Failed to save layer item due to schema validation, see `details.errors`.", { combined: e4 });
          }
        }
      }
      if (i2.length > 0) throw new s2("sceneservice:save", "Failed to save SceneService due to unsupported or invalid content. See 'details.errors' for more detailed information", { errors: i2 });
    }
  };
  return e([y(v)], E3.prototype, "id", void 0), e([y({ type: f2 })], E3.prototype, "spatialReference", void 0), e([o("spatialReference", ["spatialReference", "store.indexCRS", "store.geographicCRS"])], E3.prototype, "readSpatialReference", null), e([y({ type: w })], E3.prototype, "fullExtent", void 0), e([o("fullExtent", ["fullExtent", "store.extent", "spatialReference", "store.indexCRS", "store.geographicCRS"])], E3.prototype, "readFullExtent", null), e([y({ readOnly: true, type: v2 })], E3.prototype, "heightModelInfo", void 0), e([y({ type: Number, json: { name: "layerDefinition.minScale", write: true, origins: { service: { read: { source: "minScale" }, write: false } } } })], E3.prototype, "minScale", void 0), e([y({ type: Number, json: { name: "layerDefinition.maxScale", write: true, origins: { service: { read: { source: "maxScale" }, write: false } } } })], E3.prototype, "maxScale", void 0), e([y({ readOnly: true })], E3.prototype, "version", void 0), e([o("version", ["store.version"])], E3.prototype, "readVersion", null), e([y({ type: String, json: { read: { source: "copyrightText" } } })], E3.prototype, "copyright", void 0), e([y({ type: String, json: { read: false } })], E3.prototype, "sublayerTitleMode", void 0), e([y({ type: String })], E3.prototype, "title", void 0), e([o("portal-item", "title")], E3.prototype, "readTitlePortalItem", null), e([o("service", "title", ["name"])], E3.prototype, "readTitleService", null), e([y({ type: Number, json: { origins: { service: { read: { source: "id" } }, "portal-item": { write: { target: "id", isRequired: true, ignoreOrigin: true }, read: false } } } })], E3.prototype, "layerId", void 0), e([y(f3)], E3.prototype, "url", null), e([r2("url")], E3.prototype, "writeUrl", null), e([y()], E3.prototype, "parsedUrl", null), e([y({ readOnly: true })], E3.prototype, "store", void 0), e([y({ type: String, readOnly: true, json: { read: { source: "store.rootNode" } } })], E3.prototype, "rootNode", void 0), E3 = e([a(A)], E3), E3;
};
var U2 = -1e38;
var T;
!function(e2) {
  e2[e2.existingItem = 0] = "existingItem", e2[e2.newItem = 1] = "newItem";
}(T || (T = {}));
var O = "Scene Service";
var K = { getTypeKeywords: () => [], portalItemLayerType: "unknown", validationOptions: { enabled: true, ignoreUnsupported: false, failPolicy: "throw" } };
var L2;
!function(e2) {
  e2[e2.SAVE = 0] = "SAVE", e2[e2.SAVE_AS = 1] = "SAVE_AS";
}(L2 || (L2 = {}));

export {
  n2 as n,
  E2 as E,
  L2 as L
};
//# sourceMappingURL=chunk-HG37JYSR.js.map
