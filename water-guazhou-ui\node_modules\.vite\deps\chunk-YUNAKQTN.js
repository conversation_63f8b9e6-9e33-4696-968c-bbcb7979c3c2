import {
  f
} from "./chunk-D7S3BWBP.js";
import {
  y2 as y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  p,
  t
} from "./chunk-REW33H3I.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/webdoc/support/writeUtils.js
var l = /* @__PURE__ */ new Set(["bing-maps", "imagery", "imagery-tile", "map-image", "open-street-map", "tile", "unknown", "unsupported", "vector-tile", "web-tile", "wms", "wmts"]);
var a = /* @__PURE__ */ new Set(["csv", "feature", "geo-rss", "geojson", "group", "imagery", "imagery-tile", "kml", "map-image", "map-notes", "media", "ogc-feature", "route", "subtype-group", "tile", "unknown", "unsupported", "vector-tile", "web-tile", "wfs", "wms", "wmts"]);
function p2(e) {
  return "basemap" === e.layerContainerType ? l : "operational-layers" === e.layerContainerType ? a : null;
}
function c(e, t2) {
  if (t2.restrictedWebMapWriting) {
    const n = p2(t2);
    return !r(n) || n.has(e.type) && !f(e);
  }
  return true;
}
function d(e, t2) {
  if (t2) if (f(e)) {
    const i = t("featureCollection.layers", t2), r2 = i && i[0] && i[0].layerDefinition;
    r2 && u(e, r2);
  } else if ("stream" === e.type) {
    u(e, t2.layerDefinition = t2.layerDefinition || {});
  } else "group" !== e.type && u(e, t2);
}
function u(e, t2) {
  "maxScale" in e && (t2.maxScale = y(e.maxScale) ?? void 0), "minScale" in e && (t2.minScale = y(e.minScale) ?? void 0);
}
function f2(e, t2) {
  if (d(e, t2), t2 && ("blendMode" in e && (t2.blendMode = e.blendMode, "normal" === t2.blendMode && delete t2.blendMode), t2.opacity = y(e.opacity) ?? void 0, t2.title = e.title || "Layer", t2.visibility = e.visible, "legendEnabled" in e && "wmts" !== e.type)) if (f(e)) {
    const i = t2.featureCollection;
    i && (i.showLegend = e.legendEnabled);
  } else t2.showLegend = e.legendEnabled;
}
function y2(n, r2, o) {
  if (!("write" in n) || !n.write) return o && o.messages && o.messages.push(new s("layer:unsupported", `Layers (${n.title}, ${n.id}) of type '${n.declaredClass}' cannot be persisted`, { layer: n })), null;
  if (c(n, o)) {
    const e = {};
    return n.write(e, o) ? e : null;
  }
  return r(r2) && f2(n, r2 = p(r2)), r2;
}

export {
  y2 as y
};
//# sourceMappingURL=chunk-YUNAKQTN.js.map
