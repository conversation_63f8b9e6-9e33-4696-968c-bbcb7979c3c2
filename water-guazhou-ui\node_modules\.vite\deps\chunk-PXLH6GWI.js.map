{"version": 3, "sources": ["../../@arcgis/core/analysis/Analysis.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import t from\"../core/Accessor.js\";import{ClonableMixin as r}from\"../core/Clonable.js\";import{IdentifiableMixin as s}from\"../core/Identifiable.js\";import{JSONSupportMixin as o}from\"../core/JSONSupport.js\";import{isSome as i}from\"../core/maybe.js\";import{property as p}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as a}from\"../core/accessorSupport/decorators/subclass.js\";let n=0,l=class extends(o(r(s(t)))){constructor(e){super(e),this.id=`${Date.now().toString(16)}-analysis-${n++}`,this.title=null}get parent(){return this._get(\"parent\")}set parent(e){const t=this.parent;if(i(t))switch(t.type){case\"line-of-sight\":case\"dimension\":t.releaseAnalysis(this);break;case\"2d\":case\"3d\":t.analyses.includes(this)&&t.analyses.remove(this)}this._set(\"parent\",e)}get isEditable(){return this.requiredPropertiesForEditing.every(i)}};e([p({type:String,constructOnly:!0,clonable:!1})],l.prototype,\"id\",void 0),e([p({type:String})],l.prototype,\"title\",void 0),e([p({constructOnly:!0})],l.prototype,\"type\",void 0),e([p({clonable:!1,value:null})],l.prototype,\"parent\",null),e([p({readOnly:!0})],l.prototype,\"isEditable\",null),e([p({readOnly:!0})],l.prototype,\"requiredPropertiesForEditing\",void 0),l=e([a(\"esri.analysis.Analysis\")],l);const c=l;export{c as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAIkgB,IAAI,IAAE;AAAN,IAAQ,IAAE,cAAc,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,KAAG,GAAG,KAAK,IAAI,EAAE,SAAS,EAAE,CAAC,aAAa,GAAG,IAAG,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,OAAOA,IAAE;AAAC,UAAM,IAAE,KAAK;AAAO,QAAG,EAAE,CAAC,EAAE,SAAO,EAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAgB,KAAI;AAAY,UAAE,gBAAgB,IAAI;AAAE;AAAA,MAAM,KAAI;AAAA,MAAK,KAAI;AAAK,UAAE,SAAS,SAAS,IAAI,KAAG,EAAE,SAAS,OAAO,IAAI;AAAA,IAAC;AAAC,SAAK,KAAK,UAASA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,6BAA6B,MAAM,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,eAAc,MAAG,UAAS,MAAE,CAAC,CAAC,GAAE,EAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,OAAG,OAAM,KAAI,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gCAA+B,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,wBAAwB,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["e"]}