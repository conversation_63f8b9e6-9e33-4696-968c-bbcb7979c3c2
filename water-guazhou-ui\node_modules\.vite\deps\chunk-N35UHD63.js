import {
  $,
  f
} from "./chunk-JXLVNWKF.js";

// node_modules/@arcgis/core/geometry/support/scaleUtils.js
var e = 96;
function i(i2, r2) {
  const o = r2 || i2.extent, c = i2.width, d2 = $(o && o.spatialReference);
  return o && c ? o.width / c * d2 * f * e : 0;
}
function r(i2, r2) {
  return i2 / ($(r2) * f * e);
}
function d(t, n) {
  const e2 = t.extent, i2 = t.width - (t.padding ? t.padding.left + t.padding.right : 0), o = r(n, e2.spatialReference);
  return e2.clone().expand(o * i2 / e2.width);
}

export {
  i,
  r,
  d
};
//# sourceMappingURL=chunk-N35UHD63.js.map
