package org.thingsboard.server.dao.model.sql.conservationWaterLevel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 涵养水位分析结果
 */
@Data
@TableName("tb_conservation_analysis")
@ApiModel(value = "涵养水位分析结果", description = "地下水涵养水位智能分析结果和建议")
public class ConservationAnalysis {

    @TableId
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
    
    @ApiModelProperty(value = "测点ID")
    private String stationId;
    
    @ApiModelProperty(value = "测点名称")
    @TableField(exist = false)
    private String stationName;
    
    @ApiModelProperty(value = "分析周期开始时间")
    private Date startTime;
    
    @ApiModelProperty(value = "分析周期结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "期初水位(米)")
    private BigDecimal initialLevel;
    
    @ApiModelProperty(value = "期末水位(米)")
    private BigDecimal finalLevel;
    
    @ApiModelProperty(value = "水位变化量(米)")
    private BigDecimal levelChange;
    
    @ApiModelProperty(value = "平均降雨量(毫米)")
    private BigDecimal avgRainfall;
    
    @ApiModelProperty(value = "平均蒸发量(毫米)")
    private BigDecimal avgEvaporation;
    
    @ApiModelProperty(value = "总开采量(立方米)")
    private BigDecimal totalExtraction;
    
    @ApiModelProperty(value = "涵养系数")
    private BigDecimal conservationCoefficient;
    
    @ApiModelProperty(value = "涵养潜力评分(0-100)")
    private BigDecimal conservationPotential;
    
    @ApiModelProperty(value = "建议涵养量(立方米)")
    private BigDecimal suggestedConservationAmount;
    
    @ApiModelProperty(value = "涵养建议")
    private String conservationSuggestion;
    
    @ApiModelProperty(value = "风险等级(1-低风险,2-中风险,3-高风险)")
    private Integer riskLevel;
    
    @ApiModelProperty(value = "风险描述")
    private String riskDescription;
    
    @ApiModelProperty(value = "分析算法版本")
    private String algorithmVersion;
    
    @ApiModelProperty(value = "分析结果详情(JSON)")
    private String analysisDetails;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    @ApiModelProperty(value = "分析状态(1-分析中,2-已完成,3-分析失败)")
    private Integer status;
    
    @ApiModelProperty(value = "创建人")
    private String creator;
    
    @ApiModelProperty(value = "创建人姓名")
    @TableField(exist = false)
    private String creatorName;
}
