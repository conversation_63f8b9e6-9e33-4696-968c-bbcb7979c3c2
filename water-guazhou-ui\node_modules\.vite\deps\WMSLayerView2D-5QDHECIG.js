import {
  v
} from "./chunk-U56QVIJL.js";
import {
  a as a3
} from "./chunk-NMTMUDUY.js";
import "./chunk-KYDW2SHL.js";
import "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-53FPJYCC.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-IEBU4QQL.js";
import {
  i
} from "./chunk-5JDQNIY4.js";
import {
  f
} from "./chunk-NEPFZ7PM.js";
import {
  u
} from "./chunk-HWB4LNSZ.js";
import "./chunk-QKWIBVLD.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import {
  l as l2
} from "./chunk-NRSKNU6M.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-22FAZXOH.js";
import "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-RURSJOSG.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-2ILOD42U.js";
import {
  b
} from "./chunk-VJW7RCN7.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  a
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/layers/WMSLayerView.js
var i2 = (i3) => {
  let m = class extends i3 {
    initialize() {
      this.exportImageParameters = new l2({ layer: this.layer });
    }
    destroy() {
      this.exportImageParameters = a(this.exportImageParameters);
    }
    get exportImageVersion() {
      var _a;
      return (_a = this.exportImageParameters) == null ? void 0 : _a.commitProperty("version"), this.commitProperty("timeExtent"), (this._get("exportImageVersion") || 0) + 1;
    }
    fetchPopupFeatures(e2) {
      const { layer: t } = this;
      if (!e2) return Promise.reject(new s2("wmslayerview:fetchPopupFeatures", "Nothing to fetch without area", { layer: t }));
      const { popupEnabled: o } = t;
      if (!o) return Promise.reject(new s2("wmslayerview:fetchPopupFeatures", "popupEnabled should be true", { popupEnabled: o }));
      const s3 = this.createFetchPopupFeaturesQuery(e2);
      if (!s3) return Promise.resolve([]);
      const { extent: p, width: a4, height: i4, x: m2, y: n } = s3;
      if (!(p && a4 && i4)) throw new s2("wmslayerview:fetchPopupFeatures", "WMSLayer does not support fetching features.", { extent: p, width: a4, height: i4 });
      return t.fetchFeatureInfo(p, a4, i4, m2, n);
    }
  };
  return e([y()], m.prototype, "exportImageParameters", void 0), e([y({ readOnly: true })], m.prototype, "exportImageVersion", null), e([y()], m.prototype, "layer", void 0), e([y(b)], m.prototype, "timeExtent", void 0), m = e([a2("esri.layers.mixins.WMSLayerView")], m), m;
};

// node_modules/@arcgis/core/views/2d/layers/WMSLayerView2D.js
var y2 = class extends i2(i(f(u))) {
  constructor() {
    super(...arguments), this.bitmapContainer = new a3();
  }
  supportsSpatialReference(e2) {
    return this.layer.serviceSupportsSpatialReference(e2);
  }
  update(e2) {
    this.strategy.update(e2).catch((e3) => {
      j(e3) || s.getLogger(this.declaredClass).error(e3);
    });
  }
  attach() {
    const { layer: e2 } = this, { imageMaxHeight: t, imageMaxWidth: r } = e2;
    this.bitmapContainer = new a3(), this.container.addChild(this.bitmapContainer), this.strategy = new v({ container: this.bitmapContainer, fetchSource: this.fetchImage.bind(this), requestUpdate: this.requestUpdate.bind(this), imageMaxHeight: t, imageMaxWidth: r, imageRotationSupported: false, imageNormalizationSupported: false, hidpi: false }), this.addAttachHandles(l(() => this.exportImageVersion, () => this.requestUpdate()));
  }
  detach() {
    this.strategy = a(this.strategy), this.container.removeAllChildren();
  }
  moveStart() {
  }
  viewChange() {
  }
  moveEnd() {
    this.requestUpdate();
  }
  createFetchPopupFeaturesQuery(e2) {
    const { view: t, bitmapContainer: r } = this, { x: i3, y: s3 } = e2, { spatialReference: a4 } = t;
    let o, n = 0, m = 0;
    if (r.children.some((e3) => {
      const { width: t2, height: r2, resolution: h2, x: c2, y: d2 } = e3, u2 = c2 + h2 * t2, y3 = d2 - h2 * r2;
      return i3 >= c2 && i3 <= u2 && s3 <= d2 && s3 >= y3 && (o = new w({ xmin: c2, ymin: y3, xmax: u2, ymax: d2, spatialReference: a4 }), n = t2, m = r2, true);
    }), !o) return null;
    const h = o.width / n, c = Math.round((i3 - o.xmin) / h), d = Math.round((o.ymax - s3) / h);
    return { extent: o, width: n, height: m, x: c, y: d };
  }
  async doRefresh() {
    this.requestUpdate();
  }
  isUpdating() {
    return this.strategy.updating || this.updateRequested;
  }
  fetchImage(e2, t, r, i3) {
    return this.layer.fetchImageBitmap(e2, t, r, { timeExtent: this.timeExtent, ...i3 });
  }
};
e([y()], y2.prototype, "strategy", void 0), e([y()], y2.prototype, "updating", void 0), y2 = e([a2("esri.views.2d.layers.WMSLayerView2D")], y2);
var l3 = y2;
export {
  l3 as default
};
//# sourceMappingURL=WMSLayerView2D-5QDHECIG.js.map
