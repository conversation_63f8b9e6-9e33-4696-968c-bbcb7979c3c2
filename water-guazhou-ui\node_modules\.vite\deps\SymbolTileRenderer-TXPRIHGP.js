import {
  o as o3
} from "./chunk-MYLMEBH6.js";
import {
  a as a3,
  e as e2,
  i
} from "./chunk-ARVYW5FF.js";
import {
  o as o2
} from "./chunk-L7J6WAZK.js";
import {
  i as i2,
  m
} from "./chunk-EJ4BPAYT.js";
import "./chunk-JTTSDQPH.js";
import "./chunk-6DXPU43Z.js";
import {
  n as n2
} from "./chunk-6FMMG4VO.js";
import {
  w
} from "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-AZEN5UFW.js";
import {
  f as f2
} from "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import {
  te
} from "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import {
  s
} from "./chunk-CV76WXPW.js";
import "./chunk-53FPJYCC.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import {
  E2,
  f
} from "./chunk-6G2NLXT7.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-QKWIBVLD.js";
import {
  E,
  T
} from "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import {
  F
} from "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import {
  n
} from "./chunk-NWZTRS6O.js";
import "./chunk-RURSJOSG.js";
import "./chunk-77E52HT5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-AVKOL7OR.js";
import {
  u
} from "./chunk-I7WHRVHF.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  D,
  j
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  a,
  c,
  l,
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/engine/webgl/collisions/MetricReader.js
var e3 = 6;
var r2 = 4294967296;
var o4 = class _o {
  constructor(t2) {
    this._savedCursor = null, this._savedOffset = null, this._head = t2, this._cursor = t2;
  }
  static from(t2) {
    const s2 = i3.from(new Float32Array(t2));
    return new _o(s2);
  }
  get id() {
    return this._cursor.id;
  }
  get baseZoom() {
    return this._cursor.baseZoom;
  }
  get anchorX() {
    return this._cursor.anchorX;
  }
  get anchorY() {
    return this._cursor.anchorY;
  }
  get directionX() {
    return this._cursor.directionX;
  }
  get directionY() {
    return this._cursor.directionY;
  }
  get size() {
    return this._cursor.size;
  }
  get materialKey() {
    return this._cursor.materialKey;
  }
  get boundsCount() {
    return this._cursor.boundsCount;
  }
  computedMinZoom() {
    return this._cursor.computedMinZoom();
  }
  setComputedMinZoom(t2) {
    return this._cursor.setComputedMinZoom(t2);
  }
  boundsComputedAnchorX(t2) {
    return this._cursor.boundsComputedAnchorX(t2);
  }
  boundsComputedAnchorY(t2) {
    return this._cursor.boundsComputedAnchorY(t2);
  }
  setBoundsComputedAnchorX(t2, s2) {
    return this._cursor.setBoundsComputedAnchorX(t2, s2);
  }
  setBoundsComputedAnchorY(t2, s2) {
    return this._cursor.setBoundsComputedAnchorY(t2, s2);
  }
  boundsX(t2) {
    return this._cursor.boundsX(t2);
  }
  boundsY(t2) {
    return this._cursor.boundsY(t2);
  }
  boundsWidth(t2) {
    return this._cursor.boundsWidth(t2);
  }
  boundsHeight(t2) {
    return this._cursor.boundsHeight(t2);
  }
  link(s2) {
    if (r(s2._head)) return this._cursor.link(s2._head);
  }
  getCursor() {
    return this.copy();
  }
  copy() {
    var _a;
    const t2 = new _o((_a = this._head) == null ? void 0 : _a.copy());
    if (!t2._head) return t2;
    let s2 = t2._head, e4 = t2._head._link;
    for (; e4; ) s2._link = e4.copy(), s2 = e4, e4 = s2._link;
    return t2;
  }
  peekId() {
    return this._cursor.peekId() ?? this._cursor._link.peekId();
  }
  nextId() {
    const t2 = this.id;
    for (; t2 === this.id; ) if (!this.next()) return false;
    return true;
  }
  save() {
    this._savedCursor = this._cursor, this._savedOffset = this._cursor._offset;
  }
  restore() {
    this._savedCursor && (this._cursor = this._savedCursor), null != this._savedOffset && (this._cursor._offset = this._savedOffset);
  }
  next() {
    if (!this._cursor) return false;
    if (!this._cursor.next()) {
      if (!this._cursor._link) return false;
      this._cursor = this._cursor._link, this._cursor._offset = 0;
    }
    return true;
  }
  lookup(t2) {
    for (this._cursor = this._head; this._cursor && !this._cursor.lookup(t2); ) {
      if (!this._cursor._link) return false;
      this._cursor = this._cursor._link;
    }
    return !!this._cursor;
  }
  delete(s2) {
    let e4 = this._head, r4 = null;
    for (; e4; ) {
      if (e4.delete(s2)) return e4.isEmpty() && r(r4) && (r4._link = e4._link), true;
      r4 = e4, e4 = e4._link;
    }
    return false;
  }
};
var i3 = class _i {
  constructor(t2) {
    this._offset = -1, this._link = null, this._count = 0, this._deletedCount = 0, this._offsets = { instance: null }, this._buffer = t2;
  }
  static from(t2) {
    return new _i(new Float32Array(t2));
  }
  isEmpty() {
    return this._deletedCount === this.count;
  }
  get count() {
    return this._count || (this._count = this._computeCount()), this._count;
  }
  get id() {
    return this._buffer[this._offset + 0];
  }
  set id(t2) {
    this._buffer[this._offset + 0] = t2;
  }
  get baseZoom() {
    return this._buffer[this._offset + 1];
  }
  get anchorX() {
    return this._buffer[this._offset + 2];
  }
  get anchorY() {
    return this._buffer[this._offset + 3];
  }
  get directionX() {
    return this._buffer[this._offset + 4];
  }
  get directionY() {
    return this._buffer[this._offset + 5];
  }
  get size() {
    return this._buffer[this._offset + 6];
  }
  get materialKey() {
    return this._buffer[this._offset + 7];
  }
  computedMinZoom() {
    return this._buffer[this._offset + 8];
  }
  setComputedMinZoom(t2) {
    this._buffer[this._offset + 8] = t2;
  }
  get boundsCount() {
    return this._buffer[this._offset + 9];
  }
  boundsComputedAnchorX(t2) {
    return this._buffer[this._offset + 10 + t2 * e3 + 0];
  }
  boundsComputedAnchorY(t2) {
    return this._buffer[this._offset + 10 + t2 * e3 + 1];
  }
  setBoundsComputedAnchorX(t2, s2) {
    this._buffer[this._offset + 10 + t2 * e3 + 0] = s2;
  }
  setBoundsComputedAnchorY(t2, s2) {
    this._buffer[this._offset + 10 + t2 * e3 + 1] = s2;
  }
  boundsX(t2) {
    return this._buffer[this._offset + 10 + t2 * e3 + 2];
  }
  boundsY(t2) {
    return this._buffer[this._offset + 10 + t2 * e3 + 3];
  }
  boundsWidth(t2) {
    return this._buffer[this._offset + 10 + t2 * e3 + 4];
  }
  boundsHeight(t2) {
    return this._buffer[this._offset + 10 + t2 * e3 + 5];
  }
  link(t2) {
    let s2 = this;
    for (; s2._link; ) s2 = s2._link;
    s2._link = t2;
  }
  getCursor() {
    return this.copy();
  }
  copy() {
    const t2 = new _i(this._buffer);
    return t2._link = this._link, t2._offset = this._offset, t2._deletedCount = this._deletedCount, t2._offsets = this._offsets, t2._count = this._count, t2;
  }
  peekId() {
    const t2 = this._offset + 10 + this.boundsCount * e3 + 0;
    return t2 >= this._buffer.length ? 0 : this._buffer[t2];
  }
  next() {
    let t2 = 0;
    for (; this._offset < this._buffer.length && t2++ < 100 && (-1 === this._offset ? this._offset = 0 : this._offset += 10 + this.boundsCount * e3, this.id === r2); ) ;
    return this.id !== r2 && this._offset < this._buffer.length;
  }
  delete(t2) {
    const s2 = this._offset, e4 = this.lookup(t2);
    if (e4) for (this.id = 4294967295, ++this._deletedCount; this.next() && this.id === t2; ) this.id = 4294967295, ++this._deletedCount;
    return this._offset = s2, e4;
  }
  lookup(t2) {
    const e4 = this._offset;
    if (t(this._offsets.instance)) {
      this._offsets.instance = /* @__PURE__ */ new Map();
      const t3 = this.copy();
      t3._offset = -1;
      let s2 = 0;
      for (; t3.next(); ) t3.id !== s2 && (this._offsets.instance.set(t3.id, t3._offset), s2 = t3.id);
    }
    return !!this._offsets.instance.has(t2) && (this._offset = this._offsets.instance.get(t2), this.id !== r2 || (this._offset = e4, false));
  }
  _computeCount() {
    const t2 = this._offset;
    let s2 = 0;
    for (this._offset = -1; this.next(); ) s2++;
    return this._offset = t2, s2;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/FeatureDisplayList.js
var n3 = class {
  constructor(t2, e4, i4, n4, a7) {
    this.target = t2, this.geometryType = e4, this.materialKey = i4, this.indexFrom = n4, this.indexCount = a7;
  }
  get indexEnd() {
    return this.indexFrom + this.indexCount;
  }
  extend(t2) {
    this.indexCount += t2;
  }
};
var a4 = class _a {
  constructor(t2, e4) {
    this.geometryType = 0, this._target = t2, this.geometryType = e4;
  }
  static from(e4, i4, n4, d2) {
    const r4 = new _a(e4, i4);
    if (r(d2)) for (const t2 of d2) n4.seekIndex(t2), r4.addRecord(n4);
    else for (; n4.next(); ) r4.addRecord(n4);
    return r4;
  }
  addRecord(t2) {
    const a7 = this._target, d2 = this.geometryType, r4 = t2.materialKey;
    let s2 = t2.indexFrom, o5 = t2.indexCount;
    const h3 = t2.vertexFrom, x = t2.vertexCount;
    if (o5 || (s2 = h3, o5 = x), t(this._head)) {
      const t3 = new n3(a7, d2, r4, s2, o5);
      return void (this._head = new a3(t3));
    }
    let m2 = null, c4 = this._head;
    for (; c4; ) {
      if (s2 < c4.data.indexFrom) return this._insert(r4, s2, o5, m2, c4);
      m2 = c4, c4 = c4.next;
    }
    this._insert(r4, s2, o5, m2, null);
  }
  forEach(e4) {
    r(this._head) && this._head.forEach(e4);
  }
  *infos() {
    if (r(this._head)) for (const t2 of this._head.values()) yield t2;
  }
  _insert(a7, d2, r4, s2, o5) {
    if (t(s2) && t(o5)) {
      const t2 = new n3(this._target, this.geometryType, a7, d2, r4);
      this._head = new a3(t2);
    }
    return t(s2) && r(o5) ? this._insertAtHead(a7, d2, r4, o5) : r(s2) && t(o5) ? this._insertAtEnd(a7, d2, r4, s2) : r(s2) && r(o5) ? this._insertAtMiddle(a7, d2, r4, s2, o5) : void 0;
  }
  _insertAtHead(t2, e4, a7, d2) {
    const r4 = e4 + a7;
    if (t2 === d2.data.materialKey && r4 === d2.data.indexFrom) d2.data.indexFrom = e4, d2.data.indexCount += a7;
    else {
      const r5 = new n3(this._target, this.geometryType, t2, e4, a7);
      this._head = new a3(r5), this._head.next = d2;
    }
  }
  _insertAtEnd(t2, e4, a7, d2) {
    if (d2.data.materialKey === t2 && d2.data.indexEnd === e4) d2.data.indexCount += a7;
    else {
      const r4 = new n3(this._target, this.geometryType, t2, e4, a7), s2 = new a3(r4);
      d2.next = s2;
    }
  }
  _insertAtMiddle(t2, e4, a7, d2, r4) {
    const s2 = e4 + a7;
    if (d2.data.materialKey === t2 && d2.data.indexEnd === e4) d2.data.indexCount += a7, d2.data.materialKey === r4.data.materialKey && d2.data.indexEnd === r4.data.indexFrom && (d2.data.indexCount += r4.data.indexCount, d2.next = r4.next);
    else if (t2 === r4.data.materialKey && s2 === r4.data.indexFrom) r4.data.indexFrom = e4, r4.data.indexCount += a7;
    else {
      const s3 = new n3(this._target, this.geometryType, t2, e4, a7), o5 = new a3(s3);
      d2.next = o5, o5.next = r4;
    }
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/cpuMapped/Buffer.js
var a5 = 1.25;
var u2 = 32767;
var d = u2 << 16 | u2;
var f3 = class {
  constructor(t2, i4, e4, s2) {
    const h3 = i.create(i4 * e4 * Uint32Array.BYTES_PER_ELEMENT, s2);
    this.size = i4, this.strideInt = e4, this.bufferType = t2, this.dirty = { start: 1 / 0, end: 0 }, this._gpu = null, this._cpu = h3, this.clear();
  }
  get elementSize() {
    return this._cpu.length / this.strideInt;
  }
  get invalidated() {
    return this.bufferSize > 0 && !this._gpu;
  }
  get invalidatedComputeBuffer() {
    return this.bufferSize > 0 && !this._gpuComputeTriangles;
  }
  invalidate() {
    this._invalidateTriangleBuffer(), o(this._gpu, (t2) => t2.dispose()), this._gpu = null;
  }
  _invalidateTriangleBuffer() {
    o(this._gpuComputeTriangles, (t2) => t2.dispose()), this._gpuComputeTriangles = null;
  }
  destroy() {
    o(this._gpu, (t2) => t2.dispose()), o(this._gpuComputeTriangles, (t2) => t2.dispose()), o(this._cpu, (t2) => t2.destroy()), o(this._cpu2, (t2) => t2.destroy());
  }
  clear() {
    this.dirty.start = 1 / 0, this.dirty.end = 0, this.freeList = new e2({ start: 0, end: this._cpu.length / this.strideInt }), this.fillPointer = 0;
  }
  ensure(t2) {
    if (this.maxAvailableSpace() >= t2) return;
    if (t2 * this.strideInt > this._cpu.length - this.fillPointer) {
      this.invalidate();
      const i4 = this._cpu.length / this.strideInt, e4 = Math.round((i4 + t2) * a5), r4 = e4 * this.strideInt;
      this._cpu.expand(r4 * Uint32Array.BYTES_PER_ELEMENT), this.freeList.free(i4, e4 - i4);
    }
  }
  set(t2, i4) {
    this._cpu.array[t2] !== i4 && (this._cpu.array[t2] = i4, this.dirty.start = Math.min(t2, this.dirty.start), this.dirty.end = Math.max(t2, this.dirty.end));
  }
  getGPUBuffer(t2, e4 = false) {
    if (!this.bufferSize) return null;
    if (e4) {
      if ("index" !== this.bufferType) throw new Error("Tired to get triangle buffer, but target is not an index buffer");
      return t(this._gpuComputeTriangles) && (this._gpuComputeTriangles = this._createComputeBuffer(t2)), this._gpuComputeTriangles;
    }
    return t(this._gpu) && (this._gpu = this._createBuffer(t2)), this._gpu;
  }
  getCPUBuffer() {
    if (!this._cpu2) {
      const t2 = this._cpu.slice();
      this._cpu2 = t2;
    }
    return this._cpu2.length !== this._cpu.length && this._cpu2.expand(this._cpu.length * this._cpu.array.BYTES_PER_ELEMENT), this._cpu2.set(this._cpu), this._cpu2;
  }
  get bufferSize() {
    return this._cpu.length / this.strideInt;
  }
  maxAvailableSpace() {
    return this.freeList.maxAvailableSpace();
  }
  insert(t2, i4, r4, s2) {
    const h3 = r4 * this.strideInt;
    if (!h3) return 0;
    const n4 = i4 * this.strideInt * Uint32Array.BYTES_PER_ELEMENT, a7 = new Uint32Array(t2, n4, h3), u5 = c(this.freeList.firstFit(r4), "First fit region must be defined") * this.strideInt, d2 = h3, f4 = u5 / this.strideInt - i4;
    if (0 !== s2) for (let e4 = 0; e4 < a7.length; e4++) a7[e4] += s2;
    return this._cpu.array.set(a7, u5), this.dirty.start = Math.min(this.dirty.start, u5), this.dirty.end = Math.max(this.dirty.end, u5 + d2), this.fillPointer = Math.max(this.fillPointer, u5 + d2), f4;
  }
  free(t2, i4, e4) {
    const r4 = t2 * this.strideInt, s2 = (t2 + i4) * this.strideInt;
    if (true === e4) for (let h3 = t2; h3 !== t2 + i4; h3++) this._cpu.array[h3 * this.strideInt] = d;
    this.dirty.start = Math.min(this.dirty.start, r4), this.dirty.end = Math.max(this.dirty.end, s2), this.freeList.free(t2, i4);
  }
  upload() {
    if (this.dirty.end) {
      if (this._invalidateTriangleBuffer(), t(this._gpu)) return this.dirty.start = 1 / 0, void (this.dirty.end = 0);
      this._gpu.setSubData(this._cpu.array, this.dirty.start, this.dirty.start, this.dirty.end), this.dirty.start = 1 / 0, this.dirty.end = 0;
    }
  }
  _createBuffer(t2) {
    const i4 = F.DYNAMIC_DRAW;
    return "index" === this.bufferType ? E2.createIndex(t2, i4, this._cpu.array) : E2.createVertex(t2, i4, this._cpu.array);
  }
  _createComputeBuffer(t2) {
    const i4 = F.DYNAMIC_DRAW, e4 = new Uint32Array(this.fillPointer / 3);
    for (let r4 = 0; r4 < this.fillPointer; r4 += 3) e4[r4 / 3] = this._cpu.array[r4];
    return E2.createIndex(t2, i4, e4);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/cpuMapped/Geometry.js
var h = 0;
var u3 = 1;
var c2 = class {
  constructor(e4, t2) {
    this._vaos = /* @__PURE__ */ new Map(), this._indicesInvalid = false, this.geometryType = e4, this._stage = t2;
  }
  destroy() {
    for (const [t2, r4] of this._vaos) r(r4) && r4.dispose(false);
    this._indexBuffer = a(this._indexBuffer), this._vertexBuffer = a(this._vertexBuffer);
  }
  insert(e4, t2, s2) {
    if (!e4.records.byteLength) return;
    const i4 = e4.stride;
    if (this._vertexBuffer && this._indexBuffer) {
      const s3 = e4.indices.byteLength / 4, f4 = e4.vertices.byteLength / i4;
      this._indexBuffer.ensure(s3), this._vertexBuffer.ensure(f4);
      const { vertices: n4, indices: d2 } = e4, h3 = i2.from(e4.records), u5 = this._vertexBuffer.insert(n4, 0, n4.byteLength / i4, 0), c4 = this._indexBuffer.insert(d2, 0, d2.byteLength / 4, u5);
      if (h3.forEach((e5) => {
        e5.indexFrom += c4, e5.vertexFrom += u5;
      }), c(this._records, "Expected records to be defined").link(h3), t2) this._indicesInvalid = true;
      else if (this._displayList) {
        const e5 = h3.getCursor();
        for (; e5.next(); ) this._displayList.addRecord(e5);
      }
    } else {
      const r4 = e4.indices.byteLength / 4, s3 = e4.vertices.byteLength / i4, f4 = i4 / Uint32Array.BYTES_PER_ELEMENT, d2 = this._stage.bufferPool;
      this._records = i2.from(e4.records), this._indexBuffer = new f3("index", r4, 1, d2), this._vertexBuffer = new f3("vertex", s3, f4, d2), this._indexBuffer.insert(e4.indices, 0, e4.indices.byteLength / 4, 0), this._vertexBuffer.insert(e4.vertices, 0, e4.vertices.byteLength / i4, 0), t2 && (this._indicesInvalid = true);
    }
  }
  remove(e4) {
    if (!t(this._records)) for (const t2 of e4) {
      const e5 = this._records.getCursor();
      if (!e5.lookup(t2)) continue;
      const r4 = e5.indexFrom, s2 = e5.vertexFrom;
      let i4 = e5.indexCount, f4 = e5.vertexCount;
      for (; e5.next() && e5.id === t2; ) i4 += e5.indexCount, f4 += e5.vertexCount;
      this._indexBuffer.free(r4, i4), this._vertexBuffer.free(s2, f4, true), this._records.delete(t2);
    }
  }
  getVAO(e4, t2, r4, f4) {
    if (!this._vertexBuffer || !this._indexBuffer || t(this._records) || !this._vertexBuffer.bufferSize) return null;
    const n4 = f4 ? u3 : h;
    let o5 = this._vaos.get(n4);
    (this._vertexBuffer.invalidated || this._indexBuffer.invalidated || f4 && this._indexBuffer.invalidatedComputeBuffer) && (o(o5, (e5) => e5.dispose(false)), o5 = null), this._vertexBuffer.upload(), this._indexBuffer.upload();
    const c4 = this._indexBuffer.getGPUBuffer(e4, 1 === n4), _2 = this._vertexBuffer.getGPUBuffer(e4);
    return o5 || (o5 = new f(e4, r4, t2, { geometry: _2 }, c4), this._vaos.set(n4, o5)), o5;
  }
  forEachCommand(e4) {
    if (!t(this._records)) {
      if (this._sortIndices(this._records), !this._displayList) {
        const e5 = this._cursorIndexOrder;
        this._displayList = a4.from(this, this.geometryType, this._records.getCursor(), e5);
      }
      this._displayList.forEach(e4);
    }
  }
  _sortIndices(e4) {
    const t2 = !!this._indexBuffer.bufferSize;
    if (!this._indicesInvalid) return;
    this._indicesInvalid = false;
    let r4 = 0;
    const s2 = e4.getCursor(), i4 = [], f4 = [], n4 = [];
    for (; s2.next(); ) f4.push(s2.index), n4.push(s2.sortKey), i4.push(s2.id);
    f4.sort((e5, t3) => {
      const r5 = n4[t3], s3 = n4[e5];
      return s3 === r5 ? i4[t3] - i4[e5] : r5 - s3;
    });
    const o5 = e4.getCursor(), d2 = t2 ? this._indexBuffer.getCPUBuffer() : this._vertexBuffer.getCPUBuffer();
    for (const h3 of f4) {
      if (!o5.seekIndex(h3)) throw new Error("Expected to find index");
      if (t2) {
        const { indexFrom: e5, indexCount: t3 } = o5;
        o5.indexFrom = r4;
        for (let s3 = 0; s3 < t3; s3++) this._indexBuffer.set(r4++, d2.array[e5 + s3]);
      } else {
        const { vertexFrom: e5, vertexCount: t3 } = o5, s3 = this._vertexBuffer.strideInt, i5 = e5 * s3, f5 = i5 + t3 * s3;
        o5.vertexFrom = r4 / s3;
        for (let n5 = i5; n5 < f5; n5++) this._vertexBuffer.set(r4++, d2.array[n5]);
      }
    }
    this._cursorIndexOrder = f4, this._displayList = null;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/FeatureTile.js
var c3 = 50;
var u4 = 4;
var _ = 100;
var p = 0;
var l2 = class extends m {
  constructor(t2, r4, s2, a7, n4, h3) {
    super(t2, r4, s2, a7), this.instanceId = p++, this.patchCount = 0, this._renderState = { current: { geometry: /* @__PURE__ */ new Map(), metrics: null }, next: null, swap: false, swapFrames: 0, locked: false }, this._patches = new s(_), this._bufferPatches = new s(_), this._lastCommitTime = 0, this.transforms.labelMat2d = n(), this._store = n4, this._requestLabelUpdate = h3;
  }
  destroy() {
    super.destroy(), this._renderState.current.geometry.forEach((e4) => e4.destroy()), r(this._renderState.next) && this._renderState.next.geometry.forEach((e4) => e4.destroy()), this._renderState.current = null, this._renderState.next = null;
  }
  get labelMetrics() {
    return this._renderState.current.metrics;
  }
  get hasData() {
    return !!this._renderState.current.geometry.size;
  }
  getGeometry(e4) {
    return this._renderState.current.geometry.get(e4);
  }
  patch(e4, t2) {
    this.patchCount++, e4.clear && this._patches.size >= c3 && this._dropPatches();
    const r4 = e4, s2 = r4.addOrUpdate && this.key.id !== r4.addOrUpdate.tileKeyOrigin;
    t2 && s2 ? this._bufferPatches.enqueue(r4) : (r4.sort = r4.sort && !t2, this._patches.enqueue(r4)), this.requestRender();
  }
  commit(e4) {
    if (this._lastCommitTime !== e4.time) {
      this._lastCommitTime = e4.time;
      for (let e5 = 0; e5 < u4; e5++) this._updateMesh(), this.isReady && this._updateBufferMesh();
      this._renderState.swap && (this._swapRenderStates(), this.requestRender());
    }
  }
  lock() {
    this._renderState.locked = true;
  }
  unlock() {
    this._renderState.locked = false, this._flushUpdates(), this._swap();
  }
  _swapRenderStates() {
    if (this._renderState.next) {
      if (this._renderState.locked) return this._renderState.swap = true, void this.requestRender();
      this._renderState.swap = true, this._swap();
    }
  }
  _swap() {
    this._renderState.swap && (this._renderState.swap = false, r(this._renderState.next) && (this._renderState.current.geometry.forEach((e4) => e4.destroy()), this._renderState.current = this._renderState.next, this._renderState.next = null, this._requestLabelUpdate()));
  }
  _flushUpdates() {
    let e4 = this._patches.maxSize;
    for (; this._patches.size && e4--; ) this._updateMesh(), this._swap();
  }
  _updateBufferMesh() {
    const e4 = this._bufferPatches.peek();
    if (!r(e4) || !e4.clear || null === this._renderState.next) for (; this._bufferPatches.size; ) {
      const e5 = this._bufferPatches.dequeue();
      r(e5) && this._patchBuffer(e5);
    }
  }
  _updateMesh() {
    var _a, _b, _c, _d;
    const e4 = this._patches.dequeue();
    if (r(e4)) {
      if (has("esri-2d-update-debug")) {
        const t2 = e4, r4 = (_a = t2.addOrUpdate) == null ? void 0 : _a.tileKeyOrigin, s2 = this.key.id === r4 ? "SELF" : r4;
        let i4 = "";
        for (let e5 = 0; e5 < 5; e5++) i4 += ((_d = (_c = (_b = t2.addOrUpdate) == null ? void 0 : _b.data[e5]) == null ? void 0 : _c.records) == null ? void 0 : _d.byteLength) ? 1 : 0;
        console.debug(this.key.id, "FeatureTile:patch", `[clear: ${t2.clear} origin: ${s2}, end:${t2.end} data:${i4}]`);
      }
      true === e4.clear && (r(this._renderState.next) && (this._renderState.next.geometry.forEach((e5) => e5.destroy()), this._renderState.next = null), this._renderState.next = { geometry: /* @__PURE__ */ new Map(), metrics: null }, has("esri-2d-update-debug") && console.debug(this.key.id, "FeatureTile:_updateMesh - Creating new renderState")), this.requestRender(), this._patch(e4), e4.end && (has("esri-2d-update-debug") && console.debug(this.key.id, "FeatureTile:_updateMesh - Encountered end message"), this.ready(), this._swapRenderStates());
    }
  }
  _patch(e4) {
    te((t2) => {
      this._remove(t2, e4.remove), this._insert(t2, e4, false);
    });
  }
  _patchBuffer(e4) {
    te((t2) => {
      this._insert(t2, e4, true);
    });
  }
  _insert(e4, t2, i4) {
    var _a, _b;
    try {
      const n4 = l(this._renderState.next, this._renderState.current), h3 = (_a = t2.addOrUpdate) == null ? void 0 : _a.data[e4], d2 = n4.geometry;
      if (t(h3)) return;
      d2.has(e4) || (has("esri-2d-update-debug") && console.debug(this.key.id, `FeatureTile:_insert - Creating geometry buffer ${e4}`), d2.set(e4, new c2(e4, this.stage))), has("esri-2d-update-debug") && console.debug(this.key.id, `FeatureTile:_insert - Inserting into ${e4}, version=${(_b = t2.addOrUpdate) == null ? void 0 : _b.version} stride=${h3.stride}`), d2.get(e4).insert(h3, t2.sort, i4), e4 === E.LABEL && this._insertLabelMetrics(t2.type, h3.metrics, t2.clear);
    } catch (n4) {
    }
  }
  _insertLabelMetrics(e4, t2, i4) {
    const a7 = l(this._renderState.next, this._renderState.current);
    if (t(t2)) return;
    const n4 = o4.from(t2);
    if (t(a7.metrics)) a7.metrics = n4;
    else {
      if ("update" === e4) {
        const e5 = n4.getCursor();
        for (; e5.next(); ) a7.metrics.delete(e5.id);
      }
      a7.metrics.link(n4);
    }
  }
  _remove(e4, t2) {
    const s2 = l(this._renderState.next, this._renderState.current).geometry.get(e4);
    t2 && t2.length && s2 && (s2.remove(t2), this._removeLabelMetrics(t2));
  }
  _removeLabelMetrics(e4) {
    const { metrics: t2 } = l(this._renderState.next, this._renderState.current);
    if (!t(t2) && e4.length) for (const r4 of e4) for (; t2.delete(r4); ) ;
  }
  _dropPatches() {
    const e4 = new Array();
    let t2 = false;
    for (; this._patches.size; ) {
      const r4 = this._patches.dequeue();
      if (t(r4)) break;
      if (r4.clear) {
        if (t2) break;
        t2 = true;
      }
      e4.push(r4);
    }
    this._patches.clear(), e4.forEach((e5) => this._patches.enqueue(e5));
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/tileRenderers/support/WGLFeatureView.js
var r3 = has("featurelayer-order-by-server-enabled");
var a6 = class extends o2 {
  constructor(e4, t2, s2, i4) {
    super(e4), this._hitTestsRequests = [], this._layer = s2, this._layerView = t2, this._onUpdate = i4;
  }
  renderChildren(e4) {
    if (this.attributeView.update(), this.hasAnimation) {
      e4.painter.effects.integrate.draw(e4, e4.attributeView);
    }
    super.renderChildren(e4);
  }
  hasEmptyAttributeView() {
    return this.attributeView.isEmpty();
  }
  isUpdating() {
    return this.attributeView.isUpdating();
  }
  hitTest(t2) {
    let s2 = this._hitTestsRequests.find(({ x: e4, y: s3 }) => e4 === t2.x && s3 === t2.y);
    const i4 = D();
    return s2 ? s2.resolvers.push(i4) : (s2 = { x: t2.x, y: t2.y, resolvers: [i4] }, this._hitTestsRequests.push(s2)), this.requestRender(), i4.promise;
  }
  onTileData(e4, t2) {
    const s2 = r3 && "orderBy" in this._layer && this._layer.orderBy, i4 = s2 && (s2 == null ? void 0 : s2.length) && !s2[0].valueExpression && s2[0].field, a7 = !!s2 && this._layerView.orderByFields === i4;
    e4.patch(t2, a7), this.contains(e4) || this.addChild(e4), this.requestRender();
  }
  onTileError(e4) {
    this.contains(e4) || this.addChild(e4);
  }
  updateTransitionProperties(e4, t2) {
    super.updateTransitionProperties(e4, t2), this._layerView.featureEffectView.transitionStep(e4, t2), this._layerView.featureEffectView.transitioning && this.requestRender();
  }
  doRender(e4) {
    const { minScale: t2, maxScale: s2 } = this._layer.effectiveScaleRange, i4 = e4.state.scale;
    i4 <= (t2 || 1 / 0) && i4 >= s2 && super.doRender(e4);
  }
  afterRender(e4) {
    super.afterRender(e4), this._hitTestsRequests.length && this.requestRender();
  }
  onAttributeStoreUpdate() {
    this.hasLabels && this._layerView.view.labelManager.requestUpdate(), this._onUpdate();
  }
  get hasAnimation() {
    return this.hasLabels;
  }
  setStencilReference(e4) {
    const { rendererSchema: t2 } = e4.rendererInfo;
    if ("dot-density" === (t2 == null ? void 0 : t2.type) && (t2 == null ? void 0 : t2.dotSize) > 1 || "heatmap" === (t2 == null ? void 0 : t2.type)) {
      const e5 = 1;
      for (const t3 of this.children) t3.stencilRef = t3.key.level + e5;
    } else super.setStencilReference(e4);
  }
  get hasLabels() {
    if ("sublayers" in this._layer) return this._layer.sublayers.some((e5) => {
      var _a2;
      return !!((_a2 = e5.labelingInfo) == null ? void 0 : _a2.length) && e5.labelsVisible;
    });
    const e4 = this._layer.featureReduction, t2 = e4 && "labelingInfo" in e4 && e4.labelsVisible && e4.labelingInfo && e4.labelingInfo.length;
    return this._layer.labelingInfo && this._layer.labelingInfo.length && this._layer.labelsVisible || !!t2;
  }
  prepareRenderPasses(e4) {
    const s2 = e4.registerRenderPass({ name: "label", brushes: [w.label], target: () => this.hasLabels ? this.children : null, drawPhase: T.LABEL | T.LABEL_ALPHA }), r4 = e4.registerRenderPass({ name: "geometry", brushes: [w.fill, w.dotDensity, w.line, w.marker, w.heatmap, w.pieChart, w.text], target: () => this.children, enableDefaultDraw: () => !this._layerView.featureEffectView.hasEffects, effects: [{ apply: e4.effects.outsideEffect, enable: () => this._layerView.featureEffectView.hasEffects, args: () => this._layerView.featureEffectView.excludedEffects }, { apply: e4.effects.insideEffect, enable: () => this._layerView.featureEffectView.hasEffects, args: () => this._layerView.featureEffectView.includedEffects }, { apply: e4.effects.hittest, enable: () => !!this._hitTestsRequests.length, args: () => this._hitTestsRequests }] }), a7 = e4.registerRenderPass({ name: "highlight", brushes: [w.fill, w.dotDensity, w.line, w.marker, w.pieChart, w.text], target: () => this.children, drawPhase: T.HIGHLIGHT, enableDefaultDraw: () => false, effects: [{ apply: e4.effects.highlight, enable: () => !!this._layerView.hasHighlight() }] });
    return [...super.prepareRenderPasses(e4), r4, a7, s2];
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/tileRenderers/SymbolTileRenderer.js
var p2 = class extends o3 {
  constructor() {
    super(...arguments), this.type = "symbol";
  }
  install(e4) {
    const t2 = () => this.notifyChange("updating"), i4 = new a6(this.tileInfoView, this.layerView, this.layer, t2);
    this.featuresView = i4, e4.addChild(i4);
  }
  uninstall(e4) {
    e4.removeChild(this.featuresView), this.featuresView = a(this.featuresView);
  }
  fetchResource(e4, t2) {
    const { url: s2 } = e4, r4 = this.featuresView.stage;
    try {
      return r4.resourceManager.fetchResource(s2, { signal: t2.signal });
    } catch (a7) {
      return j(a7) ? Promise.resolve({ width: 0, height: 0 }) : Promise.reject(a7);
    }
  }
  isUpdating() {
    var _a;
    const e4 = super.isUpdating(), t2 = !this.featuresView || this.featuresView.isUpdating(), i4 = (_a = this.featuresView) == null ? void 0 : _a.hasEmptyAttributeView(), s2 = e4 || t2 || e4 && i4;
    return has("esri-2d-log-updating") && console.log(`Updating SymbolTileRenderer ${s2}
  -> updatingTiles ${e4}
  -> hasFeaturesView ${!!this.featuresView}
  -> updatingFeaturesView ${t2}`), s2;
  }
  hitTest(e4) {
    return this.featuresView.hitTest(e4);
  }
  supportsRenderer(e4) {
    return null != e4 && ["simple", "class-breaks", "unique-value", "dot-density", "dictionary", "heatmap", "pie-chart"].includes(e4.type);
  }
  onConfigUpdate(e4) {
    let t2 = null;
    if (e4 && "visualVariables" in e4) {
      const i4 = (n2(e4).visualVariables || []).map((e5) => {
        const t3 = e5.clone();
        return "normalizationField" in e5 && (t3.normalizationField = null), e5.valueExpression && "$view.scale" !== e5.valueExpression && (t3.valueExpression = null, t3.field = "nop"), t3;
      });
      t2 = f2(i4);
    }
    this.featuresView.setRendererInfo(e4, t2, this.layerView.featureEffect);
  }
  onTileData(e4) {
    const t2 = this.tiles.get(e4.tileKey);
    t2 && e4.data && this.featuresView.onTileData(t2, e4.data), this.layerView.view.labelManager.requestUpdate();
  }
  onTileError(e4) {
    const t2 = this.tiles.get(e4.tileKey);
    t2 && this.featuresView.onTileError(t2);
  }
  forceAttributeTextureUpload() {
    this.featuresView.attributeView.forceTextureUpload();
  }
  lockGPUUploads() {
    this.featuresView.attributeView.lockTextureUpload(), this.tiles.forEach((e4) => e4.lock());
  }
  unlockGPUUploads() {
    this.featuresView.attributeView.unlockTextureUpload(), this.tiles.forEach((e4) => e4.unlock());
  }
  async getMaterialItems(e4) {
    return this.featuresView.getMaterialItems(e4);
  }
  invalidateLabels() {
    this.featuresView.hasLabels && this.layerView.view.labelManager.requestUpdate();
  }
  createTile(e4) {
    const t2 = this.tileInfoView.getTileBounds(u(), e4), i4 = () => this.layerView.view.labelManager.requestUpdate(), s2 = this.tileInfoView.getTileResolution(e4.level), o5 = this.featuresView.attributeView;
    return new l2(e4, s2, t2[0], t2[3], o5, i4);
  }
  disposeTile(e4) {
    this.featuresView.removeChild(e4), e4.destroy(), this.layerView.view.labelManager.requestUpdate();
  }
};
p2 = e([a2("esri.views.2d.layers.features.tileRenderers.SymbolTileRenderer")], p2);
var h2 = p2;
export {
  h2 as default
};
//# sourceMappingURL=SymbolTileRenderer-TXPRIHGP.js.map
