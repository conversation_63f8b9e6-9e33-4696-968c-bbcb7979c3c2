{"version": 3, "sources": ["../../@arcgis/core/support/popupUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../PopupTemplate.js\";import{isRasterPixelValueField as t,getDisplayFieldName as n}from\"../layers/support/fieldUtils.js\";import i from\"../popup/content/AttachmentsContent.js\";import\"../popup/content/Content.js\";import\"../popup/content/CustomContent.js\";import\"../popup/content/ExpressionContent.js\";import o from\"../popup/content/FieldsContent.js\";import\"../popup/content/MediaContent.js\";import\"../popup/content/RelationshipContent.js\";import\"../popup/content/TextContent.js\";import r from\"../popup/FieldInfo.js\";import s from\"../popup/support/FieldInfoFormat.js\";const l=[\"oid\",\"global-id\"],a=[\"oid\",\"global-id\",\"guid\"];function p({displayField:t,editFieldsInfo:n,fields:i,objectIdField:o,title:r},s){if(!i)return null;const l=g({editFieldsInfo:n,fields:i,objectIdField:o},s);if(!l.length)return null;const a=L({titleBase:r,fields:i,displayField:t}),p=I();return new e({title:a,content:p,fieldInfos:l})}function d(t){const{fields:n,featureReduction:i,title:o}=t,r=i.fields;if(!r)return null;const s=y(r,n??[]);if(!s.length)return null;const l=$();return new e({title:o,content:l,fieldInfos:s})}const u=[/^fnode_$/i,/^tnode_$/i,/^lpoly_$/i,/^rpoly_$/i,/^poly_$/i,/^subclass$/i,/^subclass_$/i,/^rings_ok$/i,/^rings_nok$/i,/shape/i,/perimeter/i,/objectid/i,/_i$/i],c=(e,{editFieldsInfo:t,objectIdField:n,visibleFieldNames:i})=>i?i.has(e.name):!F(e.name,t)&&((!n||e.name!==n)&&(!l.includes(e.type)&&!u.some((t=>t.test(e.name)))));function f(e,t){const n=e;return t&&(e=e.filter((e=>!t.includes(e.type)))),e===n&&(e=e.slice()),e.sort(m),e}function m(e,t){return\"oid\"===e.type?-1:\"oid\"===t.type?1:_(e)?-1:_(t)?1:(e.alias||e.name).toLocaleLowerCase().localeCompare((t.alias||t.name).toLocaleLowerCase())}function F(e,t){if(!e||!t)return!1;const{creationDateField:n,creatorField:i,editDateField:o,editorField:r}=t;return[n&&n.toLowerCase(),i&&i.toLowerCase(),o&&o.toLowerCase(),r&&r.toLowerCase()].includes(e.toLowerCase())}function b(e,t){return e.editable&&!a.includes(e.type)&&!F(e.name,t)}function w(e,t){return new o({fieldInfos:g(e,t).filter((e=>e.visible))})}function g({editFieldsInfo:e,fields:t,objectIdField:n},i){return f(t??[],i?.ignoreFieldTypes||h).map((t=>new r({fieldName:t.name,isEditable:b(t,e),label:t.alias,format:j(t),visible:c(t,{editFieldsInfo:e,objectIdField:n,visibleFieldNames:i?.visibleFieldNames})})))}function y(e,t){return e.map((e=>new r({fieldName:e.name,isEditable:!1,label:e.alias,format:C(e,t),visible:!0})))}function C(e,t){const{onStatisticField:n,onStatisticExpression:i,statisticType:o}=e;if(n){const e=t.find((e=>n===e.name));if(e)return j(e)}return\"number\"===i?.returnType?new s({digitSeparator:!0,places:2}):\"count\"===o?new s({digitSeparator:!0,places:0}):null}function j(e){switch(e.type){case\"small-integer\":case\"integer\":case\"single\":return new s({digitSeparator:!0,places:0});case\"double\":return new s({digitSeparator:!0,places:2});case\"date\":return new s({dateFormat:\"long-month-day-year\"});default:return\"string\"===e.type&&t(e.name)?new s({digitSeparator:!0,places:0}):null}}function I(){return[new o,new i]}function $(){return[new o]}function L(e){const t=n(e),{titleBase:i}=e;return t?`${i}: {${t.trim()}}`:i??\"\"}function _(e){if(\"name\"===(e.name&&e.name.toLowerCase()))return!0;return\"name\"===(e.alias&&e.alias.toLowerCase())}const h=[\"geometry\",\"blob\",\"raster\",\"guid\",\"xml\"];export{g as createFieldInfos,w as createFieldsContent,p as createPopupTemplate,d as createPopupTemplateForFeatureReduction};\n"], "mappings": ";;;;;;;;;;;;;AAIkkB,IAAM,IAAE,CAAC,OAAM,WAAW;AAA1B,IAA4B,IAAE,CAAC,OAAM,aAAY,MAAM;AAAE,SAAS,EAAE,EAAC,cAAa,GAAE,gBAAe,GAAE,QAAOA,IAAE,eAAc,GAAE,OAAM,EAAC,GAAE,GAAE;AAAC,MAAG,CAACA,GAAE,QAAO;AAAK,QAAMC,KAAE,EAAE,EAAC,gBAAe,GAAE,QAAOD,IAAE,eAAc,EAAC,GAAE,CAAC;AAAE,MAAG,CAACC,GAAE,OAAO,QAAO;AAAK,QAAMC,KAAE,EAAE,EAAC,WAAU,GAAE,QAAOF,IAAE,cAAa,EAAC,CAAC,GAAEG,KAAE,EAAE;AAAE,SAAO,IAAI,EAAE,EAAC,OAAMD,IAAE,SAAQC,IAAE,YAAWF,GAAC,CAAC;AAAC;AAAgM,IAAMG,KAAE,CAAC,aAAY,aAAY,aAAY,aAAY,YAAW,eAAc,gBAAe,eAAc,gBAAe,UAAS,cAAa,aAAY,MAAM;AAAtK,IAAwKC,KAAE,CAAC,GAAE,EAAC,gBAAe,GAAE,eAAc,GAAE,mBAAkBC,GAAC,MAAIA,KAAEA,GAAE,IAAI,EAAE,IAAI,IAAE,CAAC,EAAE,EAAE,MAAK,CAAC,OAAK,CAAC,KAAG,EAAE,SAAO,OAAK,CAAC,EAAE,SAAS,EAAE,IAAI,KAAG,CAACF,GAAE,KAAM,CAAAG,OAAGA,GAAE,KAAK,EAAE,IAAI,CAAE;AAAI,SAAS,EAAE,GAAE,GAAE;AAAC,QAAM,IAAE;AAAE,SAAO,MAAI,IAAE,EAAE,OAAQ,CAAAC,OAAG,CAAC,EAAE,SAASA,GAAE,IAAI,CAAE,IAAG,MAAI,MAAI,IAAE,EAAE,MAAM,IAAG,EAAE,KAAK,CAAC,GAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAM,UAAQ,EAAE,OAAK,KAAG,UAAQ,EAAE,OAAK,IAAE,EAAE,CAAC,IAAE,KAAG,EAAE,CAAC,IAAE,KAAG,EAAE,SAAO,EAAE,MAAM,kBAAkB,EAAE,eAAe,EAAE,SAAO,EAAE,MAAM,kBAAkB,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,CAAC,KAAG,CAAC,EAAE,QAAM;AAAG,QAAK,EAAC,mBAAkB,GAAE,cAAaF,IAAE,eAAc,GAAE,aAAY,EAAC,IAAE;AAAE,SAAM,CAAC,KAAG,EAAE,YAAY,GAAEA,MAAGA,GAAE,YAAY,GAAE,KAAG,EAAE,YAAY,GAAE,KAAG,EAAE,YAAY,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,EAAE,YAAU,CAAC,EAAE,SAAS,EAAE,IAAI,KAAG,CAAC,EAAE,EAAE,MAAK,CAAC;AAAC;AAA0E,SAAS,EAAE,EAAC,gBAAe,GAAE,QAAO,GAAE,eAAc,EAAC,GAAEG,IAAE;AAAC,SAAO,EAAE,KAAG,CAAC,IAAEA,MAAA,gBAAAA,GAAG,qBAAkB,CAAC,EAAE,IAAK,CAAAC,OAAG,IAAI,EAAE,EAAC,WAAUA,GAAE,MAAK,YAAW,EAAEA,IAAE,CAAC,GAAE,OAAMA,GAAE,OAAM,QAAO,EAAEA,EAAC,GAAE,SAAQC,GAAED,IAAE,EAAC,gBAAe,GAAE,eAAc,GAAE,mBAAkBD,MAAA,gBAAAA,GAAG,kBAAiB,CAAC,EAAC,CAAC,CAAE;AAAC;AAAsX,SAAS,EAAE,GAAE;AAAC,UAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAA,IAAgB,KAAI;AAAA,IAAU,KAAI;AAAS,aAAO,IAAI,EAAE,EAAC,gBAAe,MAAG,QAAO,EAAC,CAAC;AAAA,IAAE,KAAI;AAAS,aAAO,IAAI,EAAE,EAAC,gBAAe,MAAG,QAAO,EAAC,CAAC;AAAA,IAAE,KAAI;AAAO,aAAO,IAAI,EAAE,EAAC,YAAW,sBAAqB,CAAC;AAAA,IAAE;AAAQ,aAAM,aAAW,EAAE,QAAM,GAAE,EAAE,IAAI,IAAE,IAAI,EAAE,EAAC,gBAAe,MAAG,QAAO,EAAC,CAAC,IAAE;AAAA,EAAI;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,CAAC,IAAIG,MAAE,IAAI,GAAC;AAAC;AAA4B,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,CAAC,GAAE,EAAC,WAAUC,GAAC,IAAE;AAAE,SAAO,IAAE,GAAGA,EAAC,MAAM,EAAE,KAAK,CAAC,MAAIA,MAAG;AAAE;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG,YAAU,EAAE,QAAM,EAAE,KAAK,YAAY,GAAG,QAAM;AAAG,SAAM,YAAU,EAAE,SAAO,EAAE,MAAM,YAAY;AAAE;AAAC,IAAM,IAAE,CAAC,YAAW,QAAO,UAAS,QAAO,KAAK;", "names": ["i", "l", "a", "p", "u", "c", "i", "t", "e", "i", "t", "c", "c", "i"]}