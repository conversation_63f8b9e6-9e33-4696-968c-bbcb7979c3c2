import {
  s as s3
} from "./chunk-KMJSEPHA.js";
import {
  E,
  Z,
  y as y2
} from "./chunk-LHPAMQPJ.js";
import {
  e as e2
} from "./chunk-WJWRKQWS.js";
import {
  l
} from "./chunk-NRSKI5BU.js";
import {
  i
} from "./chunk-RR74IWZB.js";
import {
  p as p2
} from "./chunk-KTB2COPC.js";
import {
  d,
  g,
  m
} from "./chunk-HTXGAKOK.js";
import {
  o as o2
} from "./chunk-OQK7L3JR.js";
import {
  p
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import {
  c
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import {
  a as a2
} from "./chunk-Q4VCSCSY.js";
import {
  n
} from "./chunk-MIA6BJ32.js";
import {
  t
} from "./chunk-NGPCXWDX.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import {
  f as f2
} from "./chunk-VJW7RCN7.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  s as s2
} from "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  A,
  F,
  L
} from "./chunk-U4SVMKOQ.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/TileLayer.js
var B = ["Canvas/World_Dark_Gray_Base", "Canvas/World_Dark_Gray_Reference", "Canvas/World_Light_Gray_Base", "Canvas/World_Light_Gray_Reference", "Elevation/World_Hillshade", "Elevation/World_Hillshade_Dark", "Ocean/World_Ocean_Base", "Ocean/World_Ocean_Reference", "Ocean_Basemap", "Reference/World_Boundaries_and_Places", "Reference/World_Boundaries_and_Places_Alternate", "Reference/World_Transportation", "World_Imagery", "World_Street_Map", "World_Topo_Map"];
var C = class extends n(E(t(c(_(s3(y2(p2(O(a2(p(i(o2(b))))))))))))) {
  constructor(...e3) {
    super(...e3), this.listMode = "show", this.isReference = null, this.operationalLayerType = "ArcGISTiledMapServiceLayer", this.resampling = true, this.sourceJSON = null, this.spatialReference = null, this.path = null, this.sublayers = null, this.type = "tile", this.url = null;
  }
  normalizeCtorArgs(e3, r3) {
    return "string" == typeof e3 ? { url: e3, ...r3 } : e3;
  }
  load(e3) {
    const r3 = r(e3) ? e3.signal : null;
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Map Service"] }, e3).catch(w).then(() => this._fetchService(r3))), Promise.resolve(this);
  }
  get attributionDataUrl() {
    var _a;
    const e3 = (_a = this.parsedUrl) == null ? void 0 : _a.path.toLowerCase();
    return e3 ? this._getDefaultAttribution(this._getMapName(e3)) : null;
  }
  readSpatialReference(e3, r3) {
    return (e3 = e3 || r3.tileInfo && r3.tileInfo.spatialReference) && f.fromJSON(e3);
  }
  writeSublayers(e3, r3, t2, s4) {
    if (!this.loaded || !e3) return;
    const i2 = e3.slice().reverse().flatten(({ sublayers: e4 }) => e4 && e4.toArray().reverse()).toArray(), o3 = [], a3 = { writeSublayerStructure: false, ...s4 };
    i2.forEach((e4) => {
      const r4 = e4.write({}, a3);
      o3.push(r4);
    });
    o3.some((e4) => Object.keys(e4).length > 1) && (r3.layers = o3);
  }
  get tileServers() {
    var _a;
    return this._getDefaultTileServers((_a = this.parsedUrl) == null ? void 0 : _a.path);
  }
  castTileServers(e3) {
    return Array.isArray(e3) ? e3.map((e4) => L(e4).path) : null;
  }
  fetchTile(e3, t2, s4, i2 = {}) {
    const { signal: o3 } = i2, a3 = this.getTileUrl(e3, t2, s4), l2 = { responseType: "image", signal: o3, query: { ...this.refreshParameters } };
    return U(a3, l2).then((e4) => e4.data);
  }
  async fetchImageBitmapTile(e3, t2, s4, i2 = {}) {
    const { signal: o3 } = i2, a3 = this.getTileUrl(e3, t2, s4), l2 = { responseType: "blob", signal: o3, query: { ...this.refreshParameters } }, { data: n2 } = await U(a3, l2);
    return e2(n2, a3);
  }
  getTileUrl(e3, r3, t2) {
    var _a, _b;
    const s4 = !this.tilemapCache && this.supportsBlankTile, i2 = A({ ...(_a = this.parsedUrl) == null ? void 0 : _a.query, blankTile: !s4 && null, ...this.customParameters, token: this.apiKey }), o3 = this.tileServers;
    return `${o3 && o3.length ? o3[r3 % o3.length] : (_b = this.parsedUrl) == null ? void 0 : _b.path}/tile/${e3}/${r3}/${t2}${i2 ? "?" + i2 : ""}`;
  }
  loadAll() {
    return l(this, (e3) => {
      e3(this.allSublayers);
    });
  }
  _fetchService(e3) {
    return new Promise((s4, i2) => {
      if (this.sourceJSON) {
        if (null != this.sourceJSON.bandCount && null != this.sourceJSON.pixelSizeX) throw new s("tile-layer:unsupported-url", "use ImageryTileLayer to open a tiled image service");
        return void s4({ data: this.sourceJSON });
      }
      if (!this.parsedUrl) throw new s("tile-layer:undefined-url", "layer's url is not defined");
      const a3 = d(this.parsedUrl.path);
      if (r(a3) && "ImageServer" === a3.serverType) throw new s("tile-layer:unsupported-url", "use ImageryTileLayer to open a tiled image service");
      U(this.parsedUrl.path, { query: { f: "json", ...this.parsedUrl.query, ...this.customParameters, token: this.apiKey }, responseType: "json", signal: e3 }).then(s4, i2);
    }).then((r3) => {
      let t2 = this.url;
      if (r3.ssl && (t2 = this.url = t2.replace(/^http:/i, "https:")), this.sourceJSON = r3.data, this.read(r3.data, { origin: "service", url: this.parsedUrl }), 10.1 === this.version && !g(t2)) return this._fetchServerVersion(t2, e3).then((e4) => {
        this.read({ currentVersion: e4 });
      }).catch(() => {
      });
    });
  }
  _fetchServerVersion(e3, s4) {
    if (!m(e3)) return Promise.reject();
    const i2 = e3.replace(/(.*\/rest)\/.*/i, "$1") + "/info";
    return U(i2, { query: { f: "json", ...this.customParameters, token: this.apiKey }, responseType: "json", signal: s4 }).then((e4) => {
      if (e4.data && e4.data.currentVersion) return e4.data.currentVersion;
      throw new s("tile-layer:version-not-available");
    });
  }
  _getMapName(e3) {
    const r3 = e3.match(/^(?:https?:)?\/\/(server\.arcgisonline\.com|services\.arcgisonline\.com|ibasemaps-api\.arcgis\.com)\/arcgis\/rest\/services\/([^\/]+(\/[^\/]+)*)\/mapserver/i);
    return r3 ? r3[2] : void 0;
  }
  _getDefaultAttribution(e3) {
    if (null == e3) return null;
    let r3;
    e3 = e3.toLowerCase();
    for (let t2 = 0, s4 = B.length; t2 < s4; t2++) if (r3 = B[t2], r3.toLowerCase().includes(e3)) return F("//static.arcgis.com/attribution/" + r3);
    return null;
  }
  _getDefaultTileServers(e3) {
    if (null == e3) return [];
    const r3 = -1 !== e3.search(/^(?:https?:)?\/\/server\.arcgisonline\.com/i), t2 = -1 !== e3.search(/^(?:https?:)?\/\/services\.arcgisonline\.com/i);
    return r3 || t2 ? [e3, e3.replace(r3 ? /server\.arcgisonline/i : /services\.arcgisonline/i, r3 ? "services.arcgisonline" : "server.arcgisonline")] : [];
  }
  get hasOverriddenFetchTile() {
    return !this.fetchTile.__isDefault__;
  }
};
e([y({ readOnly: true })], C.prototype, "attributionDataUrl", null), e([y({ type: ["show", "hide", "hide-children"] })], C.prototype, "listMode", void 0), e([y({ json: { read: true, write: true } })], C.prototype, "blendMode", void 0), e([y({ type: Boolean, json: { read: false, write: { enabled: true, overridePolicy: () => ({ enabled: false }) } } })], C.prototype, "isReference", void 0), e([y({ readOnly: true, type: ["ArcGISTiledMapServiceLayer"] })], C.prototype, "operationalLayerType", void 0), e([y({ type: Boolean })], C.prototype, "resampling", void 0), e([y()], C.prototype, "sourceJSON", void 0), e([y({ type: f })], C.prototype, "spatialReference", void 0), e([o("spatialReference", ["spatialReference", "tileInfo"])], C.prototype, "readSpatialReference", null), e([y({ type: String, json: { origins: { "web-scene": { read: true, write: true } }, read: false } })], C.prototype, "path", void 0), e([y({ readOnly: true })], C.prototype, "sublayers", void 0), e([r2("sublayers", { layers: { type: [Z] } })], C.prototype, "writeSublayers", null), e([y({ json: { read: false, write: false } })], C.prototype, "popupEnabled", void 0), e([y()], C.prototype, "tileServers", null), e([s2("tileServers")], C.prototype, "castTileServers", null), e([y({ readOnly: true, json: { read: false } })], C.prototype, "type", void 0), e([y(f2)], C.prototype, "url", void 0), C = e([a("esri.layers.TileLayer")], C), C.prototype.fetchTile.__isDefault__ = true;
var D = C;

export {
  D
};
//# sourceMappingURL=chunk-HBTP7FQD.js.map
