import {
  s as s5
} from "./chunk-YACF4WM5.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  R
} from "./chunk-JXLVNWKF.js";
import {
  s as s4
} from "./chunk-7SWS36OI.js";
import {
  m
} from "./chunk-EIGTETCG.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s as s3
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  f
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has,
  p
} from "./chunk-REW33H3I.js";
import {
  t as t2
} from "./chunk-GZGAQUSK.js";
import {
  e as e2,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/SimpleBandStatistics.js
var l2 = class {
  constructor(l4 = null, a4 = null, t3 = null) {
    this.minValue = l4, this.maxValue = a4, this.noDataValue = t3;
  }
};

// node_modules/@arcgis/core/layers/support/PixelBlock.js
var u;
var g = u = class extends l {
  static createEmptyBand(t3, e3) {
    return new (u.getPixelArrayConstructor(t3))(e3);
  }
  static getPixelArrayConstructor(t3) {
    let e3;
    switch (t3) {
      case "u1":
      case "u2":
      case "u4":
      case "u8":
        e3 = Uint8Array;
        break;
      case "u16":
        e3 = Uint16Array;
        break;
      case "u32":
        e3 = Uint32Array;
        break;
      case "s8":
        e3 = Int8Array;
        break;
      case "s16":
        e3 = Int16Array;
        break;
      case "s32":
        e3 = Int32Array;
        break;
      case "f32":
      case "c64":
      case "c128":
      case "unknown":
        e3 = Float32Array;
        break;
      case "f64":
        e3 = Float64Array;
    }
    return e3;
  }
  constructor(t3) {
    super(t3), this.width = null, this.height = null, this.pixelType = "f32", this.validPixelCount = null, this.mask = null, this.maskIsAlpha = false, this.premultiplyAlpha = false, this.statistics = null, this.depthCount = 1;
  }
  castPixelType(t3) {
    if (!t3) return "f32";
    let e3 = t3.toLowerCase();
    return ["u1", "u2", "u4"].includes(e3) ? e3 = "u8" : ["unknown", "u8", "s8", "u16", "s16", "u32", "s32", "f32", "f64"].includes(e3) || (e3 = "f32"), e3;
  }
  getPlaneCount() {
    var _a;
    return (_a = this.pixels) == null ? void 0 : _a.length;
  }
  addData(t3) {
    if (!t3.pixels || t3.pixels.length !== this.width * this.height) throw new s2("pixelblock:invalid-or-missing-pixels", "add data requires valid pixels array that has same length defined by pixel block width * height");
    this.pixels || (this.pixels = []), this.statistics || (this.statistics = []), this.pixels.push(t3.pixels), this.statistics.push(t3.statistics ?? new l2());
  }
  getAsRGBA() {
    const t3 = new ArrayBuffer(this.width * this.height * 4);
    switch (this.pixelType) {
      case "s8":
      case "s16":
      case "u16":
      case "s32":
      case "u32":
      case "f32":
      case "f64":
        this._fillFromNon8Bit(t3);
        break;
      default:
        this._fillFrom8Bit(t3);
    }
    return new Uint8ClampedArray(t3);
  }
  getAsRGBAFloat() {
    const t3 = new Float32Array(this.width * this.height * 4);
    return this._fillFrom32Bit(t3), t3;
  }
  updateStatistics() {
    if (!this.pixels) return;
    this.statistics = this.pixels.map((t4) => this._calculateBandStatistics(t4, this.mask));
    const t3 = this.mask;
    let e3 = 0;
    if (r(t3)) for (let s9 = 0; s9 < t3.length; s9++) t3[s9] && e3++;
    else e3 = this.width * this.height;
    this.validPixelCount = e3;
  }
  clamp(t3) {
    if (!t3 || "f64" === t3 || "f32" === t3 || !this.pixels) return;
    const [e3, s9] = s5(t3), i = this.pixels, l4 = this.width * this.height, r3 = i.length;
    let o2, a4, h4;
    const n = [];
    for (let p5 = 0; p5 < r3; p5++) {
      h4 = u.createEmptyBand(t3, l4), o2 = i[p5];
      for (let t4 = 0; t4 < l4; t4++) a4 = o2[t4], h4[t4] = a4 > s9 ? s9 : a4 < e3 ? e3 : a4;
      n.push(h4);
    }
    this.pixels = n, this.pixelType = t3;
  }
  extractBands(t3) {
    const { pixels: e3, statistics: s9 } = this;
    if (t(t3) || 0 === t3.length || !e3 || 0 === e3.length) return this;
    const i = e3.length, l4 = t3.some((t4) => t4 >= e3.length), r3 = i === t3.length && !t3.some((t4, e4) => t4 !== e4);
    return l4 || r3 ? this : new u({ pixelType: this.pixelType, width: this.width, height: this.height, mask: this.mask, validPixelCount: this.validPixelCount, maskIsAlpha: this.maskIsAlpha, pixels: t3.map((t4) => e3[t4]), statistics: s9 && t3.map((t4) => s9[t4]) });
  }
  clone() {
    const t3 = new u({ width: this.width, height: this.height, pixelType: this.pixelType, maskIsAlpha: this.maskIsAlpha, validPixelCount: this.validPixelCount });
    let e3;
    r(this.mask) && (this.mask instanceof Uint8Array ? t3.mask = new Uint8Array(this.mask) : t3.mask = this.mask.slice(0));
    const s9 = u.getPixelArrayConstructor(this.pixelType);
    if (this.pixels && this.pixels.length > 0) {
      t3.pixels = [];
      const i = !!this.pixels[0].slice;
      for (e3 = 0; e3 < this.pixels.length; e3++) t3.pixels[e3] = i ? this.pixels[e3].slice(0, this.pixels[e3].length) : new s9(this.pixels[e3]);
    }
    if (this.statistics) for (t3.statistics = [], e3 = 0; e3 < this.statistics.length; e3++) t3.statistics[e3] = p(this.statistics[e3]);
    return t3.premultiplyAlpha = this.premultiplyAlpha, t3;
  }
  _fillFrom8Bit(t3) {
    const { mask: e3, maskIsAlpha: s9, premultiplyAlpha: i, pixels: o2 } = this;
    if (!t3 || !o2 || !o2.length) return void s.getLogger(this.declaredClass).error("getAsRGBA()", "Unable to convert to RGBA. The input pixel block is empty.");
    let a4, h4, n, p5;
    a4 = h4 = n = o2[0], o2.length >= 3 ? (h4 = o2[1], n = o2[2]) : 2 === o2.length && (h4 = o2[1]);
    const c4 = new Uint32Array(t3), u5 = this.width * this.height;
    if (a4.length === u5) if (r(e3) && e3.length === u5) if (s9) for (p5 = 0; p5 < u5; p5++) {
      const t4 = e3[p5];
      if (t4) {
        const e4 = t4 / 255;
        c4[p5] = i ? t4 << 24 | n[p5] * e4 << 16 | h4[p5] * e4 << 8 | a4[p5] * e4 : t4 << 24 | n[p5] << 16 | h4[p5] << 8 | a4[p5];
      }
    }
    else for (p5 = 0; p5 < u5; p5++) e3[p5] && (c4[p5] = 255 << 24 | n[p5] << 16 | h4[p5] << 8 | a4[p5]);
    else for (p5 = 0; p5 < u5; p5++) c4[p5] = 255 << 24 | n[p5] << 16 | h4[p5] << 8 | a4[p5];
    else s.getLogger(this.declaredClass).error("getAsRGBA()", "Unable to convert to RGBA. The pixelblock is invalid.");
  }
  _fillFromNon8Bit(t3) {
    const { pixels: e3, mask: s9, statistics: i } = this;
    if (!t3 || !e3 || !e3.length) return void s.getLogger(this.declaredClass).error("getAsRGBA()", "Unable to convert to RGBA. The input pixel block is empty.");
    const o2 = this.pixelType;
    let a4 = 1, h4 = 0, n = 1;
    if (i && i.length > 0) {
      for (const t4 of i) if (null != t4.minValue && (h4 = Math.min(h4, t4.minValue)), null != t4.maxValue && null != t4.minValue) {
        const e4 = t4.maxValue - t4.minValue;
        n = Math.max(n, e4);
      }
      a4 = 255 / n;
    } else {
      let t4 = 255;
      "s8" === o2 ? (h4 = -128, t4 = 127) : "u16" === o2 ? t4 = 65535 : "s16" === o2 ? (h4 = -32768, t4 = 32767) : "u32" === o2 ? t4 = 4294967295 : "s32" === o2 ? (h4 = -2147483648, t4 = 2147483647) : "f32" === o2 ? (h4 = -34e38, t4 = 34e38) : "f64" === o2 && (h4 = -Number.MAX_VALUE, t4 = Number.MAX_VALUE), a4 = 255 / (t4 - h4);
    }
    const p5 = new Uint32Array(t3), c4 = this.width * this.height;
    let u5, g5, m6, d4, f5;
    if (u5 = g5 = m6 = e3[0], u5.length !== c4) return s.getLogger(this.declaredClass).error("getAsRGBA()", "Unable to convert to RGBA. The pixelblock is invalid.");
    if (e3.length >= 2) if (g5 = e3[1], e3.length >= 3 && (m6 = e3[2]), r(s9) && s9.length === c4) for (d4 = 0; d4 < c4; d4++) s9[d4] && (p5[d4] = 255 << 24 | (m6[d4] - h4) * a4 << 16 | (g5[d4] - h4) * a4 << 8 | (u5[d4] - h4) * a4);
    else for (d4 = 0; d4 < c4; d4++) p5[d4] = 255 << 24 | (m6[d4] - h4) * a4 << 16 | (g5[d4] - h4) * a4 << 8 | (u5[d4] - h4) * a4;
    else if (r(s9) && s9.length === c4) for (d4 = 0; d4 < c4; d4++) f5 = (u5[d4] - h4) * a4, s9[d4] && (p5[d4] = 255 << 24 | f5 << 16 | f5 << 8 | f5);
    else for (d4 = 0; d4 < c4; d4++) f5 = (u5[d4] - h4) * a4, p5[d4] = 255 << 24 | f5 << 16 | f5 << 8 | f5;
  }
  _fillFrom32Bit(t3) {
    const { pixels: e3, mask: s9 } = this;
    if (!t3 || !e3 || !e3.length) return s.getLogger(this.declaredClass).error("getAsRGBAFloat()", "Unable to convert to RGBA. The input pixel block is empty.");
    let i, o2, a4, h4;
    i = o2 = a4 = e3[0], e3.length >= 3 ? (o2 = e3[1], a4 = e3[2]) : 2 === e3.length && (o2 = e3[1]);
    const n = this.width * this.height;
    if (i.length !== n) return s.getLogger(this.declaredClass).error("getAsRGBAFloat()", "Unable to convert to RGBA. The pixelblock is invalid.");
    let p5 = 0;
    if (r(s9) && s9.length === n) for (h4 = 0; h4 < n; h4++) t3[p5++] = i[h4], t3[p5++] = o2[h4], t3[p5++] = a4[h4], t3[p5++] = 1 & s9[h4];
    else for (h4 = 0; h4 < n; h4++) t3[p5++] = i[h4], t3[p5++] = o2[h4], t3[p5++] = a4[h4], t3[p5++] = 1;
  }
  _calculateBandStatistics(t3, e3) {
    let s9 = 1 / 0, i = -1 / 0;
    const l4 = t3.length;
    let o2, a4 = 0;
    if (r(e3)) for (o2 = 0; o2 < l4; o2++) e3[o2] && (a4 = t3[o2], s9 = a4 < s9 ? a4 : s9, i = a4 > i ? a4 : i);
    else for (o2 = 0; o2 < l4; o2++) a4 = t3[o2], s9 = a4 < s9 ? a4 : s9, i = a4 > i ? a4 : i;
    return new l2(s9, i);
  }
};
e([y({ json: { write: true } })], g.prototype, "width", void 0), e([y({ json: { write: true } })], g.prototype, "height", void 0), e([y({ json: { write: true } })], g.prototype, "pixelType", void 0), e([s4("pixelType")], g.prototype, "castPixelType", null), e([y({ json: { write: true } })], g.prototype, "validPixelCount", void 0), e([y({ json: { write: true } })], g.prototype, "mask", void 0), e([y({ json: { write: true } })], g.prototype, "maskIsAlpha", void 0), e([y({ json: { write: true } })], g.prototype, "pixels", void 0), e([y()], g.prototype, "premultiplyAlpha", void 0), e([y({ json: { write: true } })], g.prototype, "statistics", void 0), e([y({ json: { write: true } })], g.prototype, "depthCount", void 0), e([y({ json: { write: true } })], g.prototype, "noDataValues", void 0), e([y({ json: { write: true } })], g.prototype, "bandMasks", void 0), g = u = e([a("esri.layers.support.PixelBlock")], g);
var m2 = g;

// node_modules/@arcgis/core/layers/support/rasterFunctions/pixelUtils.js
var o;
var s6;
!function(t3) {
  t3[t3.matchAny = 0] = "matchAny", t3[t3.matchAll = 1] = "matchAll";
}(o || (o = {})), function(t3) {
  t3[t3.bestMatch = 0] = "bestMatch", t3[t3.fail = 1] = "fail";
}(s6 || (s6 = {}));
var r2 = 6;
function a2(e3) {
  return r(e3) && "esri.layers.support.PixelBlock" === e3.declaredClass && e3.pixels && e3.pixels.length > 0;
}
function h(t3, e3) {
  if (!(e3 == null ? void 0 : e3.length) || !a2(t3)) return t3;
  const n = t3.pixels.length;
  return e3 && e3.some((t4) => t4 >= n) || 1 === n && 1 === e3.length && 0 === e3[0] ? t3 : n !== e3.length || e3.some((t4, e4) => t4 !== e4) ? new m2({ pixelType: t3.pixelType, width: t3.width, height: t3.height, mask: t3.mask, validPixelCount: t3.validPixelCount, maskIsAlpha: t3.maskIsAlpha, pixels: e3.map((e4) => t3.pixels[e4]), statistics: t3.statistics && e3.map((e4) => t3.statistics[e4]) }) : t3;
}
function f2(e3) {
  if (!(e3 == null ? void 0 : e3.length) || e3.some((t3) => !a2(t3))) return null;
  if (1 === e3.length) return r(e3[0]) ? e3[0].clone() : null;
  const n = e3, { width: i, height: o2, pixelType: s9 } = n[0];
  if (n.some((t3) => t3.width !== i || t3.height !== o2)) return null;
  const r3 = n.map(({ mask: t3 }) => t3).filter((t3) => null != t3);
  let h4 = null;
  r3.length && (h4 = new Uint8Array(i * o2), h4.set(r3[0]), r3.length > 1 && w2(r3.slice(1), h4));
  const f5 = [];
  n.forEach(({ pixels: t3 }) => f5.push(...t3));
  const c4 = n.map(({ statistics: t3 }) => t3).filter((t3) => t3 == null ? void 0 : t3.length), u5 = [];
  return c4.forEach((t3) => u5.push(...t3)), new m2({ pixelType: s9, width: i, height: o2, mask: h4, pixels: f5, statistics: u5.length ? u5 : null });
}
function c(t3) {
  if (!t3) return;
  const e3 = t3.colormap;
  if (!e3 || 0 === e3.length) return;
  const n = e3.sort((t4, e4) => t4[0] - e4[0]);
  let l4 = 0;
  n[0][0] < 0 && (l4 = n[0][0]);
  const i = Math.max(256, n[n.length - 1][0] - l4 + 1), o2 = new Uint8Array(4 * i), s9 = [];
  let r3, a4 = 0, h4 = 0;
  const f5 = 5 === n[0].length;
  if (i > 65536) return n.forEach((t4) => {
    s9[t4[0] - l4] = f5 ? t4.slice(1) : t4.slice(1).concat([255]);
  }), { indexed2DColormap: s9, offset: l4, alphaSpecified: f5 };
  if (t3.fillUnspecified) for (r3 = n[h4], a4 = r3[0] - l4; a4 < i; a4++) o2[4 * a4] = r3[1], o2[4 * a4 + 1] = r3[2], o2[4 * a4 + 2] = r3[3], o2[4 * a4 + 3] = f5 ? r3[4] : 255, a4 === r3[0] - l4 && (r3 = h4 === n.length - 1 ? r3 : n[++h4]);
  else for (a4 = 0; a4 < n.length; a4++) r3 = n[a4], h4 = 4 * (r3[0] - l4), o2[h4] = r3[1], o2[h4 + 1] = r3[2], o2[h4 + 2] = r3[3], o2[h4 + 3] = f5 ? r3[4] : 255;
  return { indexedColormap: o2, offset: l4, alphaSpecified: f5 };
}
function u2(e3, n) {
  if (!a2(e3)) return e3;
  if (!n || !n.indexedColormap && !n.indexed2DColormap) return e3;
  const l4 = e3.clone(), i = l4.pixels;
  let o2 = l4.mask;
  const s9 = l4.width * l4.height;
  if (1 !== i.length) return e3;
  const { indexedColormap: r3, indexed2DColormap: h4, offset: f5, alphaSpecified: c4 } = n;
  let u5 = 0;
  const p5 = i[0], x4 = new Uint8Array(p5.length), m6 = new Uint8Array(p5.length), g5 = new Uint8Array(p5.length);
  let d4, y5 = 0;
  if (r3) {
    const e4 = r3.length - 1;
    if (r(o2)) for (u5 = 0; u5 < s9; u5++) o2[u5] && (y5 = 4 * (p5[u5] - f5), y5 < f5 || y5 > e4 ? o2[u5] = 0 : (x4[u5] = r3[y5], m6[u5] = r3[y5 + 1], g5[u5] = r3[y5 + 2], o2[u5] = r3[y5 + 3]));
    else {
      for (o2 = new Uint8Array(s9), u5 = 0; u5 < s9; u5++) y5 = 4 * (p5[u5] - f5), y5 < f5 || y5 > e4 ? o2[u5] = 0 : (x4[u5] = r3[y5], m6[u5] = r3[y5 + 1], g5[u5] = r3[y5 + 2], o2[u5] = r3[y5 + 3]);
      l4.mask = o2;
    }
  } else if (h4) if (r(o2)) for (u5 = 0; u5 < s9; u5++) o2[u5] && (d4 = h4[p5[u5]], x4[u5] = d4[0], m6[u5] = d4[1], g5[u5] = d4[2], o2[u5] = d4[3]);
  else {
    for (o2 = new Uint8Array(s9), u5 = 0; u5 < s9; u5++) d4 = h4[p5[u5]], x4[u5] = d4[0], m6[u5] = d4[1], g5[u5] = d4[2], o2[u5] = d4[3];
    l4.mask = o2;
  }
  return l4.pixels = [x4, m6, g5], l4.statistics = null, l4.pixelType = "u8", l4.maskIsAlpha = c4, l4;
}
function p2(t3, e3) {
  if (!a2(t3)) return null;
  const { pixels: n, mask: i } = t3, o2 = n.length;
  let s9 = e3.lut;
  const { offset: r3 } = e3;
  s9 && 1 === s9[0].length && (s9 = n.map(() => s9));
  const h4 = [], f5 = e3.outputPixelType || "u8";
  for (let l4 = 0; l4 < o2; l4++) {
    const t4 = x(n[l4], i, s9[l4], r3 || 0, f5);
    h4.push(t4);
  }
  const c4 = new m2({ width: t3.width, height: t3.height, pixels: h4, mask: i, pixelType: f5 });
  return c4.updateStatistics(), c4;
}
function x(t3, e3, n, i, o2) {
  const s9 = t3.length, r3 = m2.createEmptyBand(o2, s9);
  if (e3) for (let l4 = 0; l4 < s9; l4++) e3[l4] && (r3[l4] = n[t3[l4] - i]);
  else for (let l4 = 0; l4 < s9; l4++) r3[l4] = n[t3[l4] - i];
  return r3;
}
function m3(t3, e3) {
  if (!a2(t3)) return null;
  const n = t3.clone(), { pixels: l4 } = n, i = n.width * n.height, o2 = e3.length, s9 = Math.floor(o2 / 2), r3 = e3[Math.floor(s9)], h4 = l4[0];
  let f5, c4, u5, p5, x4, m6, g5 = false;
  const d4 = new Uint8Array(i), y5 = new Uint8Array(i), w5 = new Uint8Array(i);
  let k3 = n.mask;
  const M4 = 4 === e3[0].mappedColor.length;
  for (k3 || (k3 = new Uint8Array(i), k3.fill(M4 ? 255 : 1), n.mask = k3), x4 = 0; x4 < i; x4++) if (k3[x4]) {
    for (f5 = h4[x4], g5 = false, m6 = s9, c4 = r3, u5 = 0, p5 = o2 - 1; p5 - u5 > 1; ) {
      if (f5 === c4.value) {
        g5 = true;
        break;
      }
      f5 > c4.value ? u5 = m6 : p5 = m6, m6 = Math.floor((u5 + p5) / 2), c4 = e3[Math.floor(m6)];
    }
    g5 || (f5 === e3[u5].value ? (c4 = e3[u5], g5 = true) : f5 === e3[p5].value ? (c4 = e3[p5], g5 = true) : f5 < e3[u5].value ? (g5 = false, c4 = null) : f5 > e3[u5].value && (f5 < e3[p5].value ? (c4 = e3[u5], g5 = true) : p5 === o2 - 1 ? (g5 = false, c4 = null) : (c4 = e3[p5], g5 = true))), g5 ? (d4[x4] = c4.mappedColor[0], y5[x4] = c4.mappedColor[1], w5[x4] = c4.mappedColor[2], k3[x4] = c4.mappedColor[3]) : d4[x4] = y5[x4] = w5[x4] = k3[x4] = 0;
  }
  return n.pixels = [d4, y5, w5], n.mask = k3, n.pixelType = "u8", n.maskIsAlpha = M4, n;
}
function g2(t3, e3) {
  if (!a2(t3)) return null;
  const { width: n, height: o2 } = t3, { inputRanges: s9, outputValues: r3, outputPixelType: h4, noDataRanges: f5, allowUnmatched: c4, isLastInputRangeInclusive: u5 } = e3, p5 = t3.pixels[0], x4 = m2.createEmptyBand(h4, p5.length), m6 = t3.mask, g5 = new Uint8Array(n * o2);
  m6 ? g5.set(m6) : g5.fill(255);
  const d4 = t3.pixelType.startsWith("f") ? 1e-6 : 0, y5 = s9.map((t4) => t4 - d4);
  y5[0] = s9[0], y5[y5.length - 1] = s9[s9.length - 1] + (u5 ? 1e-6 : 0);
  const w5 = s9.length / 2, [k3, M4] = s5(h4);
  for (let l4 = 0; l4 < o2; l4++) for (let t4 = 0; t4 < n; t4++) {
    const e4 = l4 * n + t4;
    if (g5[e4]) {
      const t5 = p5[e4];
      let n2 = false;
      for (let l5 = w5 - 1; l5 >= 0; l5--) if (t5 === y5[2 * l5] || t5 > y5[2 * l5] && t5 < y5[2 * l5 + 1]) {
        x4[e4] = r3[l5], n2 = true;
        break;
      }
      n2 || (c4 ? x4[e4] = t5 > M4 ? M4 : t5 < k3 ? k3 : t5 : g5[e4] = 0);
    }
  }
  if (f5 == null ? void 0 : f5.length) for (let l4 = 0; l4 < o2; l4++) for (let t4 = 0; t4 < n; t4++) {
    const e4 = l4 * n + t4;
    if (!m6 || m6[e4]) {
      const t5 = p5[e4];
      for (let n2 = 0; n2 < w5; n2 += 2) if (t5 >= f5[n2] && t5 <= f5[n2 + 1]) {
        x4[e4] = 0, g5[e4] = 0;
        break;
      }
    }
  }
  return new m2({ width: n, height: o2, pixelType: h4, pixels: [x4], mask: g5 });
}
function d(t3, e3, n, l4) {
  const i = null != n && n.length >= 2 ? new Set(n) : null, o2 = 1 === (n == null ? void 0 : n.length) ? n[0] : null, s9 = !!(e3 == null ? void 0 : e3.length);
  for (let r3 = 0; r3 < t3.length; r3++) if (l4[r3]) {
    const n2 = t3[r3];
    if (s9) {
      let t4 = false;
      for (let l5 = 0; l5 < e3.length; l5 += 2) if (n2 >= e3[l5] && n2 <= e3[l5 + 1]) {
        t4 = true;
        break;
      }
      t4 || (l4[r3] = 0);
    }
    l4[r3] && (n2 === o2 || (i == null ? void 0 : i.has(n2))) && (l4[r3] = 0);
  }
}
function y2(t3, e3) {
  const n = t3[0].length;
  for (let l4 = 0; l4 < n; l4++) if (e3[l4]) {
    let n2 = false;
    for (let e4 = 0; e4 < t3.length; e4++) if (t3[e4][l4]) {
      n2 = true;
      break;
    }
    n2 || (e3[l4] = 0);
  }
}
function w2(t3, e3) {
  const n = t3[0].length;
  for (let l4 = 0; l4 < n; l4++) if (e3[l4]) {
    let n2 = false;
    for (let e4 = 0; e4 < t3.length; e4++) if (0 === t3[e4][l4]) {
      n2 = true;
      break;
    }
    n2 && (e3[l4] = 0);
  }
}
function k(t3, e3) {
  if (!a2(t3)) return null;
  const { width: n, height: i, pixels: o2 } = t3, s9 = n * i, r3 = new Uint8Array(s9);
  t3.mask ? r3.set(t3.mask) : r3.fill(255);
  const h4 = o2.length, { includedRanges: f5, noDataValues: c4, outputPixelType: u5, matchAll: p5, lookups: m6 } = e3;
  if (m6) {
    const t4 = [];
    for (let e4 = 0; e4 < h4; e4++) {
      const n2 = m6[e4], l4 = x(o2[e4], r3, n2.lut, n2.offset || 0, "u8");
      t4.push(l4);
    }
    1 === t4.length ? r3.set(t4[0]) : p5 ? y2(t4, r3) : w2(t4, r3);
  } else if (p5) {
    const t4 = [];
    for (let e4 = 0; e4 < h4; e4++) {
      const n2 = new Uint8Array(s9);
      n2.set(r3), d(o2[e4], f5 == null ? void 0 : f5.slice(2 * e4, 2 * e4 + 2), c4 == null ? void 0 : c4[e4], n2), t4.push(n2);
    }
    1 === t4.length ? r3.set(t4[0]) : y2(t4, r3);
  } else for (let l4 = 0; l4 < h4; l4++) d(o2[l4], f5 == null ? void 0 : f5.slice(2 * l4, 2 * l4 + 2), c4 == null ? void 0 : c4[l4], r3);
  return new m2({ width: n, height: i, pixelType: u5, pixels: o2, mask: r3 });
}
function M(t3) {
  const { srcPixelType: e3, inputRanges: n, outputValues: o2, allowUnmatched: s9, noDataRanges: r3, isLastInputRangeInclusive: a4, outputPixelType: h4 } = t3;
  if ("u8" !== e3 && "s8" !== e3 && "u16" !== e3 && "s16" !== e3) return null;
  const f5 = e3.includes("16") ? 65536 : 256, c4 = e3.includes("s") ? -f5 / 2 : 0, u5 = m2.createEmptyBand(h4, f5), p5 = new Uint8Array(f5);
  s9 && p5.fill(255);
  const [x4, m6] = s5(h4);
  if ((n == null ? void 0 : n.length) && (o2 == null ? void 0 : o2.length)) {
    const t4 = 1e-6, e4 = n.map((e5) => e5 - t4);
    e4[0] = n[0], a4 && (e4[e4.length - 1] = n[n.length - 1]);
    for (let n2 = 0; n2 < e4.length; n2++) {
      const t5 = o2[n2] > m6 ? m6 : o2[n2] < x4 ? x4 : o2[n2], l4 = Math.ceil(e4[2 * n2] - c4), i = Math.floor(e4[2 * n2 + 1] - c4);
      for (let e5 = l4; e5 <= i; e5++) u5[e5] = t5, p5[e5] = 255;
    }
  }
  if (r3 == null ? void 0 : r3.length) for (let l4 = 0; l4 < r3.length; l4++) {
    const t4 = Math.ceil(r3[2 * l4] - c4), e4 = Math.floor(r3[2 * l4 + 1] - c4);
    for (let n2 = t4; n2 <= e4; n2++) p5[n2] = 0;
  }
  return { lut: u5, offset: c4, mask: p5 };
}
function A(t3, e3, n) {
  if ("u8" !== t3 && "s8" !== t3 && "u16" !== t3 && "s16" !== t3) return null;
  const l4 = t3.includes("16") ? 65536 : 256, i = t3.includes("s") ? -l4 / 2 : 0, o2 = new Uint8Array(l4);
  if (e3) for (let s9 = 0; s9 < e3.length; s9++) {
    const t4 = Math.ceil(e3[2 * s9] - i), n2 = Math.floor(e3[2 * s9 + 1] - i);
    for (let e4 = t4; e4 <= n2; e4++) o2[e4] = 255;
  }
  else o2.fill(255);
  if (n) for (let s9 = 0; s9 < n.length; s9++) o2[n[s9] - i] = 0;
  return { lut: o2, offset: i };
}
function U(t3, e3, n, l4, i, o2, s9, r3) {
  return { xmin: i <= n * t3 ? 0 : i < n * t3 + t3 ? i - n * t3 : t3, ymin: o2 <= l4 * e3 ? 0 : o2 < l4 * e3 + e3 ? o2 - l4 * e3 : e3, xmax: i + s9 <= n * t3 ? 0 : i + s9 < n * t3 + t3 ? i + s9 - n * t3 : t3, ymax: o2 + r3 <= l4 * e3 ? 0 : o2 + r3 < l4 * e3 + e3 ? o2 + r3 - l4 * e3 : e3 };
}
function T(t3, n) {
  if (!t3 || 0 === t3.length) return null;
  const l4 = t3.find((t4) => t4.pixelBlock);
  if (!l4 || t(l4.pixelBlock)) return null;
  const i = (l4.extent.xmax - l4.extent.xmin) / l4.pixelBlock.width, o2 = (l4.extent.ymax - l4.extent.ymin) / l4.pixelBlock.height, s9 = 0.01 * Math.min(i, o2), r3 = t3.sort((t4, e3) => Math.abs(t4.extent.ymax - e3.extent.ymax) > s9 ? e3.extent.ymax - t4.extent.ymax : Math.abs(t4.extent.xmin - e3.extent.xmin) > s9 ? t4.extent.xmin - e3.extent.xmin : 0), a4 = Math.min.apply(null, r3.map((t4) => t4.extent.xmin)), h4 = Math.min.apply(null, r3.map((t4) => t4.extent.ymin)), f5 = Math.max.apply(null, r3.map((t4) => t4.extent.xmax)), c4 = Math.max.apply(null, r3.map((t4) => t4.extent.ymax)), u5 = { x: Math.round((n.xmin - a4) / i), y: Math.round((c4 - n.ymax) / o2) }, p5 = { width: Math.round((f5 - a4) / i), height: Math.round((c4 - h4) / o2) }, x4 = { width: Math.round((n.xmax - n.xmin) / i), height: Math.round((n.ymax - n.ymin) / o2) };
  if (Math.round(p5.width / l4.pixelBlock.width) * Math.round(p5.height / l4.pixelBlock.height) !== r3.length || u5.x < 0 || u5.y < 0 || p5.width < x4.width || p5.height < x4.height) return null;
  return { extent: n, pixelBlock: b(r3.map((t4) => t4.pixelBlock), p5, { clipOffset: u5, clipSize: x4 }) };
}
function C(t3, e3, n, l4, i, o2) {
  const { width: s9, height: r3 } = n.block, { x: a4, y: h4 } = n.offset, { width: f5, height: c4 } = n.mosaic, u5 = U(s9, r3, l4, i, a4, h4, f5, c4);
  let p5 = 0, x4 = 0;
  if (o2) {
    const t4 = o2.hasGCSSShiftTransform ? 360 : o2.halfWorldWidth ?? 0, e4 = s9 * o2.resolutionX, n2 = o2.startX + l4 * e4;
    n2 < t4 && n2 + e4 > t4 ? x4 = o2.rightPadding : n2 >= t4 && (p5 = o2.leftMargin - o2.rightPadding, x4 = 0);
  }
  if (u5.xmax -= x4, "number" != typeof e3) for (let m6 = u5.ymin; m6 < u5.ymax; m6++) {
    const n2 = (i * r3 + m6 - h4) * f5 + (l4 * s9 - a4) + p5, o3 = m6 * s9;
    for (let l5 = u5.xmin; l5 < u5.xmax; l5++) t3[n2 + l5] = e3[o3 + l5];
  }
  else for (let m6 = u5.ymin; m6 < u5.ymax; m6++) {
    const n2 = (i * r3 + m6 - h4) * f5 + (l4 * s9 - a4) + p5;
    for (let l5 = u5.xmin; l5 < u5.xmax; l5++) t3[n2 + l5] = e3;
  }
}
function b(n, i, o2 = {}) {
  const { clipOffset: s9, clipSize: r3, alignmentInfo: h4, blockWidths: f5 } = o2;
  if (f5) return P(n, i, { blockWidths: f5 });
  const c4 = n.find((t3) => a2(t3));
  if (t(c4)) return null;
  const u5 = r3 ? r3.width : i.width, p5 = r3 ? r3.height : i.height, x4 = c4.width, m6 = c4.height, g5 = i.width / x4, d4 = i.height / m6, y5 = { offset: s9 || { x: 0, y: 0 }, mosaic: r3 || i, block: { width: x4, height: m6 } }, w5 = c4.pixelType, k3 = m2.getPixelArrayConstructor(w5), M4 = c4.pixels.length, A3 = [];
  let U3, T2;
  for (let t3 = 0; t3 < M4; t3++) {
    T2 = new k3(u5 * p5);
    for (let e3 = 0; e3 < d4; e3++) for (let l4 = 0; l4 < g5; l4++) {
      const i2 = n[e3 * g5 + l4];
      a2(i2) && (U3 = i2.pixels[t3], C(T2, U3, y5, l4, e3, h4));
    }
    A3.push(T2);
  }
  let b3;
  if (n.some((n2) => t(n2) || r(n2.mask) && n2.mask.length > 0)) {
    b3 = new Uint8Array(u5 * p5);
    for (let e3 = 0; e3 < d4; e3++) for (let l4 = 0; l4 < g5; l4++) {
      const i2 = n[e3 * g5 + l4], o3 = r(i2) ? i2.mask : null;
      r(o3) ? C(b3, o3, y5, l4, e3, h4) : C(b3, i2 ? 1 : 0, y5, l4, e3, h4);
    }
  }
  const S3 = new m2({ width: u5, height: p5, pixels: A3, pixelType: w5, mask: b3 });
  return S3.updateStatistics(), S3;
}
function P(i, o2, s9) {
  const r3 = i.find((e3) => r(e3));
  if (t(r3)) return null;
  const h4 = i.some((e3) => !r(e3) || !!e3.mask), { width: f5, height: c4 } = o2, u5 = h4 ? new Uint8Array(f5 * c4) : null, { blockWidths: p5 } = s9, x4 = [], m6 = r3.getPlaneCount(), g5 = m2.getPixelArrayConstructor(r3.pixelType);
  if (h4) for (let t3 = 0, e3 = 0; t3 < i.length; e3 += p5[t3], t3++) {
    const l4 = i[t3];
    if (!a2(l4)) continue;
    const o3 = e2(l4.mask);
    for (let n = 0; n < c4; n++) for (let i2 = 0; i2 < p5[t3]; i2++) u5[n * f5 + i2 + e3] = null == o3 ? 255 : o3[n * l4.width + i2];
  }
  for (let t3 = 0; t3 < m6; t3++) {
    const e3 = new g5(f5 * c4);
    for (let n = 0, l4 = 0; n < i.length; l4 += p5[n], n++) {
      const o3 = i[n];
      if (!a2(o3)) continue;
      const s10 = o3.pixels[t3];
      if (null != s10) for (let t4 = 0; t4 < c4; t4++) for (let i2 = 0; i2 < p5[n]; i2++) e3[t4 * f5 + i2 + l4] = s10[t4 * o3.width + i2];
    }
    x4.push(e3);
  }
  const d4 = new m2({ width: f5, height: c4, mask: u5, pixels: x4, pixelType: r3.pixelType });
  return d4.updateStatistics(), d4;
}
function S(t3, e3, n) {
  if (!a2(t3)) return null;
  const { width: l4, height: i } = t3, o2 = e3.x, s9 = e3.y, r3 = n.width + o2, h4 = n.height + s9;
  if (o2 < 0 || s9 < 0 || r3 > l4 || h4 > i) return t3;
  if (0 === o2 && 0 === s9 && r3 === l4 && h4 === i) return t3;
  t3.mask || (t3.mask = new Uint8Array(l4 * i));
  const f5 = t3.mask;
  for (let a4 = 0; a4 < i; a4++) {
    const t4 = a4 * l4;
    for (let e4 = 0; e4 < l4; e4++) f5[t4 + e4] = a4 < s9 || a4 >= h4 || e4 < o2 || e4 >= r3 ? 0 : 1;
  }
  return t3.updateStatistics(), t3;
}
function B(t3) {
  if (!a2(t3)) return null;
  const e3 = t3.clone(), { width: l4, height: i, pixels: o2 } = t3, s9 = o2[0], r3 = e3.pixels[0], h4 = e2(t3.mask);
  for (let n = 2; n < i - 1; n++) {
    const t4 = /* @__PURE__ */ new Map();
    for (let i2 = n - 2; i2 < n + 2; i2++) for (let e5 = 0; e5 < 4; e5++) {
      const n2 = i2 * l4 + e5;
      _(t4, s9[n2], h4 ? h4[n2] : 1);
    }
    r3[n * l4] = v(t4), r3[n * l4 + 1] = r3[n * l4 + 2] = r3[n * l4];
    let e4 = 3;
    for (; e4 < l4 - 1; e4++) {
      let i2 = (n - 2) * l4 + e4 + 1;
      _(t4, s9[i2], h4 ? h4[i2] : 1), i2 = (n - 1) * l4 + e4 + 1, _(t4, s9[i2], h4 ? h4[i2] : 1), i2 = n * l4 + e4 + 1, _(t4, s9[i2], h4 ? h4[i2] : 1), i2 = (n + 1) * l4 + e4 + 1, _(t4, s9[i2], h4 ? h4[i2] : 1), i2 = (n - 2) * l4 + e4 - 3, I(t4, s9[i2], h4 ? h4[i2] : 1), i2 = (n - 1) * l4 + e4 - 3, I(t4, s9[i2], h4 ? h4[i2] : 1), i2 = n * l4 + e4 - 3, I(t4, s9[i2], h4 ? h4[i2] : 1), i2 = (n + 1) * l4 + e4 - 3, I(t4, s9[i2], h4 ? h4[i2] : 1), r3[n * l4 + e4] = v(t4);
    }
    r3[n * l4 + e4 + 1] = r3[n * l4 + e4];
  }
  for (let n = 0; n < l4; n++) r3[n] = r3[l4 + n] = r3[2 * l4 + n], r3[(i - 1) * l4 + n] = r3[(i - 2) * l4 + n];
  return e3.updateStatistics(), e3;
}
function v(t3) {
  if (0 === t3.size) return 0;
  let e3 = 0, n = -1, l4 = 0;
  const i = t3.keys();
  let o2 = i.next();
  for (; !o2.done; ) l4 = t3.get(o2.value), l4 > e3 && (n = o2.value, e3 = l4), o2 = i.next();
  return n;
}
function I(t3, e3, n) {
  if (0 === n) return;
  const l4 = t3.get(e3);
  1 === l4 ? t3.delete(e3) : t3.set(e3, l4 - 1);
}
function _(t3, e3, n) {
  0 !== n && t3.set(e3, t3.has(e3) ? t3.get(e3) + 1 : 1);
}
function W(t3, e3, i) {
  let { x: o2, y: s9 } = e3;
  const { width: r3, height: h4 } = i;
  if (0 === o2 && 0 === s9 && h4 === t3.height && r3 === t3.width) return t3;
  const { width: f5, height: c4 } = t3, u5 = Math.max(0, s9), p5 = Math.max(0, o2), x4 = Math.min(o2 + r3, f5), m6 = Math.min(s9 + h4, c4);
  if (x4 < 0 || m6 < 0 || !a2(t3)) return null;
  o2 = Math.max(0, -o2), s9 = Math.max(0, -s9);
  const { pixels: g5 } = t3, d4 = r3 * h4, y5 = g5.length, w5 = [];
  for (let n = 0; n < y5; n++) {
    const e4 = g5[n], i2 = m2.createEmptyBand(t3.pixelType, d4);
    for (let t4 = u5; t4 < m6; t4++) {
      const n2 = t4 * f5;
      let l4 = (t4 + s9 - u5) * r3 + o2;
      for (let t5 = p5; t5 < x4; t5++) i2[l4++] = e4[n2 + t5];
    }
    w5.push(i2);
  }
  const k3 = new Uint8Array(d4), M4 = e2(t3.mask);
  for (let n = u5; n < m6; n++) {
    const t4 = n * f5;
    let e4 = (n + s9 - u5) * r3 + o2;
    for (let n2 = p5; n2 < x4; n2++) k3[e4++] = M4 ? M4[t4 + n2] : 1;
  }
  const A3 = new m2({ width: i.width, height: i.height, pixelType: t3.pixelType, pixels: w5, mask: k3 });
  return A3.updateStatistics(), A3;
}
function E(e3, n = true) {
  if (!a2(e3)) return null;
  const { pixels: i, width: o2, height: s9, mask: r3, pixelType: h4 } = e3, f5 = [], c4 = Math.round(o2 / 2), u5 = Math.round(s9 / 2), p5 = s9 - 1, x4 = o2 - 1;
  for (let t3 = 0; t3 < i.length; t3++) {
    const e4 = i[t3], r4 = m2.createEmptyBand(h4, c4 * u5);
    let a4 = 0;
    for (let t4 = 0; t4 < s9; t4 += 2) for (let l4 = 0; l4 < o2; l4 += 2) {
      const i2 = e4[t4 * o2 + l4];
      if (n) {
        const n2 = l4 === x4 ? i2 : e4[t4 * o2 + l4 + 1], s10 = t4 === p5 ? i2 : e4[t4 * o2 + l4 + o2], h5 = l4 === x4 ? s10 : t4 === p5 ? n2 : e4[t4 * o2 + l4 + o2 + 1];
        r4[a4++] = (i2 + n2 + s10 + h5) / 4;
      } else r4[a4++] = i2;
    }
    f5.push(r4);
  }
  let m6 = null;
  if (r(r3)) {
    m6 = new Uint8Array(c4 * u5);
    let t3 = 0;
    for (let e4 = 0; e4 < s9; e4 += 2) for (let l4 = 0; l4 < o2; l4 += 2) {
      const i2 = r3[e4 * o2 + l4];
      if (n) {
        const n2 = l4 === x4 ? i2 : r3[e4 * o2 + l4 + 1], s10 = e4 === p5 ? i2 : r3[e4 * o2 + l4 + o2], a4 = l4 === x4 ? s10 : e4 === p5 ? n2 : r3[e4 * o2 + l4 + o2 + 1];
        m6[t3++] = i2 * n2 * s10 * a4 ? 1 : 0;
      } else m6[t3++] = i2;
    }
  }
  return new m2({ width: c4, height: u5, pixelType: h4, pixels: f5, mask: m6 });
}
function R2(t3, e3, n) {
  if (!a2(t3)) return null;
  const { width: l4, height: i } = e3;
  let { width: o2, height: s9 } = t3;
  const r3 = /* @__PURE__ */ new Map(), h4 = { x: 0, y: 0 }, f5 = null == n ? 1 : 1 + n;
  let c4 = t3;
  for (let a4 = 0; a4 < f5; a4++) {
    const t4 = Math.ceil(o2 / l4), n2 = Math.ceil(s9 / i);
    for (let o3 = 0; o3 < n2; o3++) {
      h4.y = o3 * i;
      for (let n3 = 0; n3 < t4; n3++) {
        h4.x = n3 * l4;
        const t5 = W(c4, h4, e3);
        r3.set(`${a4}/${o3}/${n3}`, t5);
      }
    }
    a4 < f5 - 1 && (c4 = E(c4)), o2 = Math.round(o2 / 2), s9 = Math.round(s9 / 2);
  }
  return r3;
}
function j(t3, e3, n, l4, i = 0) {
  const { width: o2, height: s9 } = t3, { width: r3, height: a4 } = e3, h4 = l4.cols, f5 = l4.rows, c4 = Math.ceil(r3 / h4 - 0.1 / h4), u5 = Math.ceil(a4 / f5 - 0.1 / f5);
  let p5, x4, m6, g5, d4, y5, w5;
  const k3 = c4 * h4, M4 = k3 * u5 * f5, A3 = new Float32Array(M4), U3 = new Float32Array(M4), T2 = new Uint32Array(M4), C2 = new Uint32Array(M4);
  let b3, P3, S3 = 0;
  for (let B2 = 0; B2 < u5; B2++) for (let t4 = 0; t4 < c4; t4++) {
    p5 = 12 * (B2 * c4 + t4), x4 = n[p5], m6 = n[p5 + 1], g5 = n[p5 + 2], d4 = n[p5 + 3], y5 = n[p5 + 4], w5 = n[p5 + 5];
    for (let e4 = 0; e4 < f5; e4++) {
      S3 = (B2 * f5 + e4) * k3 + t4 * h4, P3 = (e4 + 0.5) / f5;
      for (let t5 = 0; t5 < e4; t5++) b3 = (t5 + 0.5) / h4, A3[S3 + t5] = (x4 * b3 + m6 * P3 + g5) * o2 + i, U3[S3 + t5] = (d4 * b3 + y5 * P3 + w5) * s9 + i, T2[S3 + t5] = Math.floor(A3[S3 + t5]), C2[S3 + t5] = Math.floor(U3[S3 + t5]);
    }
    p5 += 6, x4 = n[p5], m6 = n[p5 + 1], g5 = n[p5 + 2], d4 = n[p5 + 3], y5 = n[p5 + 4], w5 = n[p5 + 5];
    for (let e4 = 0; e4 < f5; e4++) {
      S3 = (B2 * f5 + e4) * k3 + t4 * h4, P3 = (e4 + 0.5) / f5;
      for (let t5 = e4; t5 < h4; t5++) b3 = (t5 + 0.5) / h4, A3[S3 + t5] = (x4 * b3 + m6 * P3 + g5) * o2 + i, U3[S3 + t5] = (d4 * b3 + y5 * P3 + w5) * s9 + i, T2[S3 + t5] = Math.floor(A3[S3 + t5]), C2[S3 + t5] = Math.floor(U3[S3 + t5]);
    }
  }
  return { offsets_x: A3, offsets_y: U3, offsets_xi: T2, offsets_yi: C2, gridWidth: k3 };
}
function D(t3, e3) {
  const { coefficients: n, spacing: l4 } = e3, { offsets_x: i, offsets_y: o2, gridWidth: s9 } = j(t3, t3, n, { rows: l4[0], cols: l4[1] }), { width: r3, height: a4 } = t3, h4 = new Float32Array(r3 * a4), f5 = 180 / Math.PI;
  for (let c4 = 0; c4 < a4; c4++) for (let t4 = 0; t4 < r3; t4++) {
    const e4 = c4 * s9 + t4, n2 = 0 === c4 ? e4 : e4 - s9, l5 = c4 === a4 - 1 ? e4 : e4 + s9, u5 = i[n2] - i[l5], p5 = o2[l5] - o2[n2];
    if (isNaN(u5) || isNaN(p5)) h4[c4 * r3 + t4] = 90;
    else {
      let e5 = Math.atan2(p5, u5) * f5;
      e5 = (360 + e5) % 360, h4[c4 * r3 + t4] = e5;
    }
  }
  return h4;
}
function F(e3, n, i, o2, s9 = "nearest") {
  if (!a2(e3)) return null;
  "majority" === s9 && (e3 = B(e3));
  const { pixels: r3, mask: h4, pixelType: f5 } = e3, c4 = e3.width, u5 = e3.height, p5 = m2.getPixelArrayConstructor(f5), x4 = r3.length, { width: m6, height: g5 } = n;
  let d4 = false;
  for (let t3 = 0; t3 < i.length; t3 += 3) -1 === i[t3] && -1 === i[t3 + 1] && -1 === i[t3 + 2] && (d4 = true);
  const { offsets_x: y5, offsets_y: w5, offsets_xi: k3, offsets_yi: M4, gridWidth: A3 } = j({ width: c4, height: u5 }, n, i, o2, "majority" === s9 ? 0.5 : 0);
  let U3;
  const T2 = (t3, e4, n2) => {
    const l4 = t3 instanceof Float32Array || t3 instanceof Float64Array ? 0 : 0.5;
    for (let i2 = 0; i2 < g5; i2++) {
      U3 = i2 * A3;
      for (let o3 = 0; o3 < m6; o3++) {
        if (y5[U3] < 0 || w5[U3] < 0) t3[i2 * m6 + o3] = 0;
        else if (n2) t3[i2 * m6 + o3] = e4[k3[U3] + M4[U3] * c4];
        else {
          const n3 = Math.floor(y5[U3]), s10 = Math.floor(w5[U3]), r4 = Math.ceil(y5[U3]), a4 = Math.ceil(w5[U3]), f6 = y5[U3] - n3, u6 = w5[U3] - s10;
          if (!h4 || h4[n3 + s10 * c4] && h4[n3 + s10 * c4] && h4[n3 + a4 * c4] && h4[r4 + a4 * c4]) {
            const h5 = (1 - f6) * e4[n3 + s10 * c4] + f6 * e4[r4 + s10 * c4], p6 = (1 - f6) * e4[n3 + a4 * c4] + f6 * e4[r4 + a4 * c4];
            t3[i2 * m6 + o3] = (1 - u6) * h5 + u6 * p6 + l4;
          } else t3[i2 * m6 + o3] = e4[k3[U3] + M4[U3] * c4];
        }
        U3++;
      }
    }
  }, C2 = [];
  let b3;
  for (let t3 = 0; t3 < x4; t3++) b3 = new p5(m6 * g5), T2(b3, r3[t3], "nearest" === s9 || "majority" === s9), C2.push(b3);
  const P3 = new m2({ width: m6, height: g5, pixelType: f5, pixels: C2 });
  if (r(h4)) P3.mask = new Uint8Array(m6 * g5), T2(P3.mask, h4, true);
  else if (d4) {
    P3.mask = new Uint8Array(m6 * g5);
    for (let t3 = 0; t3 < m6 * g5; t3++) P3.mask[t3] = y5[t3] < 0 || w5[t3] < 0 ? 0 : 1;
  }
  return P3.updateStatistics(), P3;
}

// node_modules/@arcgis/core/layers/support/rasterFunctions/vectorFieldUtils.js
var s7 = /* @__PURE__ */ new Map();
s7.set("meter-per-second", 1), s7.set("kilometer-per-hour", 0.277778), s7.set("knots", 0.514444), s7.set("feet-per-second", 0.3048), s7.set("mile-per-hour", 0.44704);
var a3 = 180 / Math.PI;
var h2 = 5;
var c2 = new s3({ esriMetersPerSecond: "meter-per-second", esriKilometersPerHour: "kilometer-per-hour", esriKnots: "knots", esriFeetPerSecond: "feet-per-second", esriMilesPerHour: "mile-per-hour" });
function l3(t3, e3) {
  return s7.get(t3) / s7.get(e3) || 1;
}
function u3(t3) {
  return (450 - t3) % 360;
}
function f3(t3, e3 = "geographic") {
  const [n, r3] = t3, o2 = Math.sqrt(n * n + r3 * r3);
  let i = Math.atan2(r3, n) * a3;
  return i = (360 + i) % 360, "geographic" === e3 && (i = u3(i)), [o2, i];
}
function p3(t3, e3 = "geographic") {
  let n = t3[1];
  "geographic" === e3 && (n = u3(n)), n %= 360;
  const r3 = t3[0];
  return [r3 * Math.cos(n / a3), r3 * Math.sin(n / a3)];
}
function m4(t3, r3, o2, s9 = "geographic") {
  if (!a2(t3) || t(o2)) return t3;
  const a4 = "vector-magdir" === r3 ? t3.clone() : e2(d2(t3, r3)), h4 = a4.pixels[1];
  for (let e3 = 0; e3 < h4.length; e3++) h4[e3] = "geographic" === s9 ? (h4[e3] + o2[e3] + 270) % 360 : (h4[e3] + 360 - o2[e3]) % 360;
  return "vector-magdir" === r3 ? a4 : d2(a4, "vector-magdir");
}
function d2(t3, e3, n = "geographic", r3 = 1) {
  if (!a2(t3)) return t3;
  const { pixels: s9, width: a4, height: h4 } = t3, c4 = a4 * h4, l4 = s9[0], u5 = s9[1], m6 = t3.pixelType.startsWith("f") ? t3.pixelType : "f32", d4 = m2.createEmptyBand(m6, c4), g5 = m2.createEmptyBand(m6, c4);
  let x4 = 0;
  for (let o2 = 0; o2 < h4; o2++) for (let t4 = 0; t4 < a4; t4++) "vector-uv" === e3 ? ([d4[x4], g5[x4]] = f3([l4[x4], u5[x4]], n), d4[x4] *= r3) : ([d4[x4], g5[x4]] = p3([l4[x4], u5[x4]], n), d4[x4] *= r3, g5[x4] *= r3), x4++;
  const M4 = new m2({ pixelType: m6, width: t3.width, height: t3.height, mask: t3.mask, validPixelCount: t3.validPixelCount, maskIsAlpha: t3.maskIsAlpha, pixels: [d4, g5] });
  return M4.updateStatistics(), M4;
}
function g3(t3, e3, n = 1) {
  if (1 === n || !a2(t3)) return t3;
  const r3 = t3.clone(), { pixels: o2, width: s9, height: a4 } = r3, h4 = o2[0], c4 = o2[1];
  let l4 = 0;
  for (let i = 0; i < a4; i++) for (let t4 = 0; t4 < s9; t4++) "vector-uv" === e3 ? (h4[l4] *= n, c4[l4] *= n) : h4[l4] *= n, l4++;
  return r3.updateStatistics(), r3;
}
function x2(t3, n, r3, o2, i) {
  if (t(i) || !i.spatialReference.equals(t3.spatialReference)) return { extent: t3, width: Math.round(n / o2), height: Math.round(r3 / o2), resolution: t3.width / n };
  const s9 = i.xmin, a4 = i.ymax, h4 = (t3.xmax - t3.xmin) / n * o2, c4 = (t3.ymax - t3.ymin) / r3 * o2, l4 = (h4 + c4) / 2;
  return t3.xmin = s9 + Math.floor((t3.xmin - s9) / h4) * h4, t3.xmax = s9 + Math.ceil((t3.xmax - s9) / h4) * h4, t3.ymin = a4 + Math.floor((t3.ymin - a4) / c4) * c4, t3.ymax = a4 + Math.ceil((t3.ymax - a4) / c4) * c4, { extent: t3, width: Math.round(t3.width / h4), height: Math.round(t3.height / c4), resolution: l4 };
}
var M2 = k2(0, 0, 0);
function k2(t3 = 0, e3 = 0, n = Math.PI, r3 = true) {
  r3 && (n = (2 * Math.PI - n) % (2 * Math.PI));
  const o2 = r3 ? -1 : 1, i = 13 * o2, s9 = -7 * o2, a4 = -2 * o2, h4 = -16 * o2, c4 = 21.75, [l4, u5] = y3(0, e3 + i, n, c4), [f5, p5] = y3(t3 - 5.5, e3 + s9, n, c4), [m6, d4] = y3(t3 + 5.5, e3 + s9, n, c4), [g5, x4] = y3(t3 - 1.5, e3 + a4, n, c4), [M4, k3] = y3(t3 + 1.5, e3 + a4, n, c4), [w5, P3] = y3(t3 - 1.5, e3 + h4, n, c4), [b3, v3] = y3(t3 + 1.5, e3 + h4, n, c4);
  return [l4, u5, f5, p5, g5, x4, M4, k3, m6, d4, w5, P3, b3, v3];
}
function w3(t3 = 0, e3 = Math.PI, n = true) {
  n && (e3 = (2 * Math.PI - e3) % (2 * Math.PI));
  const r3 = 10, o2 = n ? -1 : 1, i = 5 * o2, s9 = 20 * o2, a4 = 25 * o2, c4 = 45, l4 = 0, u5 = 0, f5 = 2, p5 = 0, m6 = f5 * o2, d4 = n ? 1 : -1, g5 = r3 / 2 * d4;
  let [x4, M4] = [l4 + g5, u5 - s9], [k3, w5] = [x4 + f5 * d4, M4], [P3, b3] = [k3 - p5 * d4, w5 + m6], [v3, I3] = [l4 - g5, u5 - a4], [A3, _3] = [v3 + p5 * d4, I3 - m6], U3 = Math.ceil(t3 / h2), S3 = Math.floor(U3 / 10);
  U3 -= 8 * S3;
  const D3 = [], F3 = [];
  for (let h4 = 0; h4 < U3 / 2; h4++, S3--) {
    S3 <= 0 && U3 % 2 == 1 && h4 === (U3 - 1) / 2 && (v3 = l4, A3 = v3 + p5 * d4, I3 = (I3 + M4) / 2, _3 = I3 - m6);
    const [t4, n2] = y3(v3, I3, e3, c4);
    if (S3 > 0) {
      const [r4, o3] = y3(k3, I3, e3, c4), [i2, s10] = y3(x4, M4, e3, c4);
      D3.push(r4), D3.push(o3), D3.push(t4), D3.push(n2), D3.push(i2), D3.push(s10);
    } else {
      const [r4, o3] = y3(k3, w5, e3, c4), [i2, s10] = y3(P3, b3, e3, c4), [a5, h5] = y3(A3, _3, e3, c4);
      F3.push(t4), F3.push(n2), F3.push(a5), F3.push(h5), F3.push(i2), F3.push(s10), F3.push(r4), F3.push(o3);
    }
    I3 += i, M4 += i, w5 += i, b3 += i, _3 += i;
  }
  const [j3, N] = y3(l4 + g5, u5 + s9, e3, c4), J = (r3 / 2 + f5) * d4, [O, q] = y3(l4 + J, u5 + s9, e3, c4), [B2, E2] = y3(l4 + g5, u5 - a4, e3, c4), [T2, C2] = y3(l4 + J, u5 - a4, e3, c4);
  return { pennants: D3, barbs: F3, shaft: [j3, N, O, q, B2, E2, T2, C2] };
}
function y3(t3, e3, n, r3 = 1) {
  const o2 = Math.sqrt(t3 * t3 + e3 * e3) / r3, i = (2 * Math.PI + Math.atan2(e3, t3)) % (2 * Math.PI);
  return [o2, (2 * Math.PI + i - n) % (2 * Math.PI)];
}
var P2 = [0, 1, 3, 6, 10, 16, 21, 27, 33, 40, 47, 55, 63];
var b2 = [0, 0.5, 1, 1.5, 2];
var v2 = [0, 0.25, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4];
function I2(t3, e3, n, r3) {
  const o2 = l3(r3 || "knots", n);
  let i;
  for (i = 1; i < e3.length; i++) if (i === e3.length - 1) {
    if (t3 < e3[i] * o2) break;
  } else if (t3 <= e3[i] * o2) break;
  return Math.min(i - 1, e3.length - 2);
}
function A2(t3, e3, n, r3, o2) {
  let i = 0;
  switch (e3) {
    case "beaufort_kn":
      i = I2(t3, P2, "knots", n);
      break;
    case "beaufort_km":
      i = I2(t3, P2, "kilometer-per-hour", n);
      break;
    case "beaufort_ft":
      i = I2(t3, P2, "feet-per-second", n);
      break;
    case "beaufort_m":
      i = I2(t3, P2, "meter-per-second", n);
      break;
    case "classified_arrow":
      i = I2(t3, o2 ?? [], r3, n);
      break;
    case "ocean_current_m":
      i = I2(t3, b2, "meter-per-second", n);
      break;
    case "ocean_current_kn":
      i = I2(t3, v2, "knots", n);
  }
  return i;
}
function _2(t3, e3) {
  const { style: n, inputUnit: o2, outputUnit: i, breakValues: s9 } = e3, a4 = c2.fromJSON(o2), h4 = c2.fromJSON(i), l4 = 7 * 6, u5 = 15;
  let f5 = 0, p5 = 0;
  const { width: m6, height: d4, mask: g5 } = t3, x4 = t3.pixels[0], w5 = t3.pixels[1], y5 = r(g5) ? g5.filter((t4) => t4 > 0).length : m6 * d4, P3 = new Float32Array(y5 * l4), b3 = new Uint32Array(u5 * y5), v3 = e3.invertDirection ? k2(0, 0, 0, false) : M2;
  for (let r3 = 0; r3 < d4; r3++) for (let t4 = 0; t4 < m6; t4++) {
    const e4 = r3 * m6 + t4;
    if (!g5 || g5[r3 * m6 + t4]) {
      const o3 = (w5[e4] + 360) % 360 / 180 * Math.PI, i2 = A2(x4[e4], n, a4, h4, s9);
      for (let n2 = 0; n2 < v3.length; n2 += 2) P3[f5++] = (t4 + 0.5) / m6, P3[f5++] = (r3 + 0.5) / d4, P3[f5++] = v3[n2], P3[f5++] = v3[n2 + 1] + o3, P3[f5++] = i2, P3[f5++] = x4[e4];
      const c4 = 7 * (f5 / l4 - 1);
      b3[p5++] = c4, b3[p5++] = c4 + 1, b3[p5++] = c4 + 2, b3[p5++] = c4 + 0, b3[p5++] = c4 + 4, b3[p5++] = c4 + 3, b3[p5++] = c4 + 0, b3[p5++] = c4 + 2, b3[p5++] = c4 + 3, b3[p5++] = c4 + 2, b3[p5++] = c4 + 5, b3[p5++] = c4 + 3, b3[p5++] = c4 + 5, b3[p5++] = c4 + 6, b3[p5++] = c4 + 3;
    }
  }
  return { vertexData: P3, indexData: b3 };
}
var U2 = [];
function S2(t3, e3) {
  if (0 === U2.length) for (let h4 = 0; h4 < 30; h4++) U2.push(w3(5 * h4, 0, !e3.invertDirection));
  const n = l3(c2.fromJSON(e3.inputUnit), "knots"), { width: r3, height: o2, mask: i } = t3, s9 = t3.pixels[0], a4 = t3.pixels[1], u5 = 6, f5 = [], p5 = [];
  let m6 = 0, d4 = 0;
  for (let c4 = 0; c4 < o2; c4++) for (let t4 = 0; t4 < r3; t4++) {
    const e4 = c4 * r3 + t4, l4 = s9[e4] * n;
    if ((!i || i[c4 * r3 + t4]) && l4 >= h2) {
      const n2 = (a4[e4] + 360) % 360 / 180 * Math.PI, { pennants: i2, barbs: s10, shaft: h4 } = U2[Math.min(Math.floor(l4 / 5), 29)];
      if (i2.length + s10.length === 0) continue;
      let g5 = f5.length / u5;
      const x4 = (t4 + 0.5) / r3, M4 = (c4 + 0.5) / o2;
      for (let t5 = 0; t5 < i2.length; t5 += 2) f5[m6++] = x4, f5[m6++] = M4, f5[m6++] = i2[t5], f5[m6++] = i2[t5 + 1] + n2, f5[m6++] = 0, f5[m6++] = l4;
      for (let t5 = 0; t5 < s10.length; t5 += 2) f5[m6++] = x4, f5[m6++] = M4, f5[m6++] = s10[t5], f5[m6++] = s10[t5 + 1] + n2, f5[m6++] = 0, f5[m6++] = l4;
      for (let t5 = 0; t5 < h4.length; t5 += 2) f5[m6++] = x4, f5[m6++] = M4, f5[m6++] = h4[t5], f5[m6++] = h4[t5 + 1] + n2, f5[m6++] = 0, f5[m6++] = l4;
      for (let t5 = 0; t5 < i2.length / 6; t5++) p5[d4++] = g5, p5[d4++] = g5 + 1, p5[d4++] = g5 + 2, g5 += 3;
      for (let t5 = 0; t5 < s10.length / 8; t5++) p5[d4++] = g5, p5[d4++] = g5 + 1, p5[d4++] = g5 + 2, p5[d4++] = g5 + 1, p5[d4++] = g5 + 2, p5[d4++] = g5 + 3, g5 += 4;
      p5[d4++] = g5 + 0, p5[d4++] = g5 + 1, p5[d4++] = g5 + 2, p5[d4++] = g5 + 1, p5[d4++] = g5 + 3, p5[d4++] = g5 + 2, g5 += 4;
    }
  }
  return { vertexData: new Float32Array(f5), indexData: new Uint32Array(p5) };
}
function D2(t3, e3) {
  const n = 4 * 6;
  let r3 = 0, o2 = 0;
  const { width: i, height: s9, mask: a4 } = t3, u5 = t3.pixels[0], f5 = [], p5 = [], m6 = l3(c2.fromJSON(e3.inputUnit), "knots"), d4 = "wind_speed" === e3.style ? h2 : Number.MAX_VALUE;
  for (let h4 = 0; h4 < s9; h4++) for (let t4 = 0; t4 < i; t4++) {
    const e4 = u5[h4 * i + t4] * m6;
    if ((!a4 || a4[h4 * i + t4]) && e4 < d4) {
      for (let n2 = 0; n2 < 4; n2++) f5[r3++] = (t4 + 0.5) / i, f5[r3++] = (h4 + 0.5) / s9, f5[r3++] = n2 < 2 ? -0.5 : 0.5, f5[r3++] = n2 % 2 == 0 ? -0.5 : 0.5, f5[r3++] = 0, f5[r3++] = e4;
      const a5 = 4 * (r3 / n - 1);
      p5[o2++] = a5, p5[o2++] = a5 + 1, p5[o2++] = a5 + 2, p5[o2++] = a5 + 1, p5[o2++] = a5 + 2, p5[o2++] = a5 + 3;
    }
  }
  return { vertexData: new Float32Array(f5), indexData: new Uint32Array(p5) };
}
function F2(t3, e3) {
  return "simple_scalar" === e3.style ? D2(t3, e3) : "wind_speed" === e3.style ? S2(t3, e3) : _2(t3, e3);
}
function j2(t3, e3, n, r3 = [0, 0], i = 0.5) {
  const { width: s9, height: a4, mask: h4 } = t3, [c4, l4] = t3.pixels, [u5, m6] = r3, d4 = Math.round((s9 - u5) / n), g5 = Math.round((a4 - m6) / n), x4 = d4 * g5, M4 = new Float32Array(x4), k3 = new Float32Array(x4), w5 = new Uint8Array(x4), y5 = "vector-uv" === e3;
  for (let o2 = 0; o2 < g5; o2++) for (let t4 = 0; t4 < d4; t4++) {
    let e4 = 0;
    const r4 = o2 * d4 + t4, g6 = Math.max(0, o2 * n + m6), x5 = Math.max(0, t4 * n + u5), P4 = Math.min(a4, g6 + n), b3 = Math.min(s9, x5 + n);
    for (let t5 = g6; t5 < P4; t5++) for (let n2 = x5; n2 < b3; n2++) {
      const o3 = t5 * s9 + n2;
      if (!h4 || h4[o3]) {
        e4++;
        const t6 = y5 ? [c4[o3], l4[o3]] : [c4[o3], (360 + l4[o3]) % 360], [n3, i2] = y5 ? t6 : p3(t6);
        M4[r4] += n3, k3[r4] += i2;
      }
    }
    if (e4 >= (P4 - g6) * (b3 - x5) * (1 - i)) {
      w5[r4] = 1;
      const [t5, n2] = f3([M4[r4] / e4, k3[r4] / e4]);
      M4[r4] = t5, k3[r4] = n2;
    } else w5[r4] = 0, M4[r4] = 0, k3[r4] = 0;
  }
  const P3 = new m2({ width: d4, height: g5, pixels: [M4, k3], mask: w5 });
  return P3.updateStatistics(), P3;
}

// node_modules/@arcgis/core/views/2d/engine/flow/dataUtils.js
var s8 = s.getLogger("esri.views.2d.engine.flow.dataUtils");
var c3 = 10;
async function f4(t3, e3, n, r3) {
  const i = performance.now(), l4 = u4(e3, n), a4 = performance.now(), f5 = m5(e3, l4, n.width, n.height), h4 = performance.now(), d4 = w4(f5, true), y5 = performance.now(), x4 = "Streamlines" === t3 ? p4(d4, c3) : g4(d4), M4 = performance.now();
  return has("esri-2d-profiler") && (s8.info("I.1", "_createFlowFieldFromData (ms)", Math.round(a4 - i)), s8.info("I.2", "_getStreamlines (ms)", Math.round(h4 - a4)), s8.info("I.3", "createAnimatedLinesData (ms)", Math.round(y5 - h4)), s8.info("I.4", "create{Streamlines|Particles}Mesh (ms)", Math.round(M4 - y5)), s8.info("I.5", "createFlowMesh (ms)", Math.round(M4 - i)), s8.info("I.6", "Mesh size (bytes)", x4.vertexData.buffer.byteLength + x4.indexData.buffer.byteLength)), await Promise.resolve(), f(r3), x4;
}
function u4(t3, e3) {
  const n = d3(e3.data, e3.width, e3.height, t3.smoothing);
  if (t3.interpolate) {
    return (t4, r3) => {
      const o2 = Math.floor(t4), i = Math.floor(r3);
      if (o2 < 0 || o2 >= e3.width) return [0, 0];
      if (i < 0 || i >= e3.height) return [0, 0];
      const l4 = t4 - o2, a4 = r3 - i, s9 = o2, c4 = i, f5 = o2 < e3.width - 1 ? o2 + 1 : o2, u5 = i < e3.height - 1 ? i + 1 : i, h4 = n[2 * (c4 * e3.width + s9)], m6 = n[2 * (c4 * e3.width + f5)], d4 = n[2 * (u5 * e3.width + s9)], w5 = n[2 * (u5 * e3.width + f5)], p5 = n[2 * (c4 * e3.width + s9) + 1], g5 = n[2 * (c4 * e3.width + f5) + 1];
      return [(h4 * (1 - a4) + d4 * a4) * (1 - l4) + (m6 * (1 - a4) + w5 * a4) * l4, (p5 * (1 - a4) + n[2 * (u5 * e3.width + s9) + 1] * a4) * (1 - l4) + (g5 * (1 - a4) + n[2 * (u5 * e3.width + f5) + 1] * a4) * l4];
    };
  }
  return (t4, r3) => {
    const o2 = Math.round(t4), i = Math.round(r3);
    return o2 < 0 || o2 >= e3.width || i < 0 || i >= e3.height ? [0, 0] : [n[2 * (i * e3.width + o2) + 0], n[2 * (i * e3.width + o2) + 1]];
  };
}
function h3(t3, e3, n, r3, o2, i, l4, a4, s9) {
  const c4 = [];
  let f5 = n, u5 = r3, h4 = 0, [m6, d4] = e3(f5, u5);
  m6 *= t3.velocityScale, d4 *= t3.velocityScale;
  const w5 = Math.sqrt(m6 * m6 + d4 * d4);
  let p5, g5;
  c4.push({ x: f5, y: u5, t: h4, speed: w5 });
  for (let y5 = 0; y5 < t3.verticesPerLine; y5++) {
    let [n2, r4] = e3(f5, u5);
    n2 *= t3.velocityScale, r4 *= t3.velocityScale;
    const m7 = Math.sqrt(n2 * n2 + r4 * r4);
    if (m7 < t3.minSpeedThreshold) return c4;
    const d5 = n2 / m7, w6 = r4 / m7;
    f5 += d5 * t3.segmentLength, u5 += w6 * t3.segmentLength;
    if (h4 += t3.segmentLength / m7, Math.acos(d5 * p5 + w6 * g5) > t3.maxTurnAngle) return c4;
    if (t3.collisions) {
      const t4 = Math.round(f5 * s9), e4 = Math.round(u5 * s9);
      if (t4 < 0 || t4 > l4 - 1 || e4 < 0 || e4 > a4 - 1) return c4;
      const n3 = i[e4 * l4 + t4];
      if (-1 !== n3 && n3 !== o2) return c4;
      i[e4 * l4 + t4] = o2;
    }
    c4.push({ x: f5, y: u5, t: h4, speed: m7 }), p5 = d5, g5 = w6;
  }
  return c4;
}
function m5(t3, e3, n, r3) {
  const o2 = [], l4 = new t2(), a4 = 1 / Math.max(t3.lineCollisionWidth, 1), s9 = Math.round(n * a4), c4 = Math.round(r3 * a4), f5 = new Int32Array(s9 * c4);
  for (let i = 0; i < f5.length; i++) f5[i] = -1;
  const u5 = [];
  for (let i = 0; i < r3; i += t3.lineSpacing) for (let e4 = 0; e4 < n; e4 += t3.lineSpacing) u5.push({ x: e4, y: i, sort: l4.getFloat() });
  u5.sort((t4, e4) => t4.sort - e4.sort);
  for (const { x: i, y: m6 } of u5) if (l4.getFloat() < t3.density) {
    const n2 = h3(t3, e3, i, m6, o2.length, f5, s9, c4, a4);
    if (n2.length < 2) continue;
    o2.push(n2);
  }
  return o2;
}
function d3(t3, e3, n, r3) {
  if (0 === r3) return t3;
  const o2 = Math.round(3 * r3), i = new Array(2 * o2 + 1);
  let l4 = 0;
  for (let c4 = -o2; c4 <= o2; c4++) {
    const t4 = Math.exp(-c4 * c4 / (r3 * r3));
    i[c4 + o2] = t4, l4 += t4;
  }
  for (let c4 = -o2; c4 <= o2; c4++) i[c4 + o2] /= l4;
  const a4 = new Float32Array(t3.length);
  for (let c4 = 0; c4 < n; c4++) for (let n2 = 0; n2 < e3; n2++) {
    let r4 = 0, l5 = 0;
    for (let a5 = -o2; a5 <= o2; a5++) {
      if (n2 + a5 < 0 || n2 + a5 >= e3) continue;
      const s10 = i[a5 + o2];
      r4 += s10 * t3[2 * (c4 * e3 + (n2 + a5)) + 0], l5 += s10 * t3[2 * (c4 * e3 + (n2 + a5)) + 1];
    }
    a4[2 * (c4 * e3 + n2) + 0] = r4, a4[2 * (c4 * e3 + n2) + 1] = l5;
  }
  const s9 = new Float32Array(t3.length);
  for (let c4 = 0; c4 < e3; c4++) for (let t4 = 0; t4 < n; t4++) {
    let r4 = 0, l5 = 0;
    for (let s10 = -o2; s10 <= o2; s10++) {
      if (t4 + s10 < 0 || t4 + s10 >= n) continue;
      const f5 = i[s10 + o2];
      r4 += f5 * a4[2 * ((t4 + s10) * e3 + c4) + 0], l5 += f5 * a4[2 * ((t4 + s10) * e3 + c4) + 1];
    }
    s9[2 * (t4 * e3 + c4) + 0] = r4, s9[2 * (t4 * e3 + c4) + 1] = l5;
  }
  return s9;
}
function w4(t3, e3) {
  const n = new t2(), r3 = t3.reduce((t4, e4) => t4 + e4.length, 0), o2 = new Float32Array(4 * r3), l4 = new Array(t3.length);
  let a4 = 0, s9 = 0;
  for (const i of t3) {
    const t4 = a4;
    for (const e4 of i) o2[4 * a4 + 0] = e4.x, o2[4 * a4 + 1] = e4.y, o2[4 * a4 + 2] = e4.t, o2[4 * a4 + 3] = e4.speed, a4++;
    l4[s9++] = { startVertex: t4, numberOfVertices: i.length, totalTime: i[i.length - 1].t, timeSeed: e3 ? n.getFloat() : 0 };
  }
  return { lineVertices: o2, lineDescriptors: l4 };
}
function p4(t3, e3) {
  const n = 9, { lineVertices: r3, lineDescriptors: o2 } = t3;
  let i = 0, l4 = 0;
  for (const m6 of o2) {
    i += 2 * m6.numberOfVertices;
    l4 += 6 * (m6.numberOfVertices - 1);
  }
  const a4 = new Float32Array(i * n), s9 = new Uint32Array(l4);
  let c4 = 0, f5 = 0;
  function u5() {
    s9[f5++] = c4 - 2, s9[f5++] = c4, s9[f5++] = c4 - 1, s9[f5++] = c4, s9[f5++] = c4 + 1, s9[f5++] = c4 - 1;
  }
  function h4(t4, e4, r4, o3, i2, l5, s10, f6) {
    const u6 = c4 * n;
    let h5 = 0;
    a4[u6 + h5++] = t4, a4[u6 + h5++] = e4, a4[u6 + h5++] = 1, a4[u6 + h5++] = r4, a4[u6 + h5++] = l5, a4[u6 + h5++] = s10, a4[u6 + h5++] = o3 / 2, a4[u6 + h5++] = i2 / 2, a4[u6 + h5++] = f6, c4++, a4[u6 + h5++] = t4, a4[u6 + h5++] = e4, a4[u6 + h5++] = -1, a4[u6 + h5++] = r4, a4[u6 + h5++] = l5, a4[u6 + h5++] = s10, a4[u6 + h5++] = -o3 / 2, a4[u6 + h5++] = -i2 / 2, a4[u6 + h5++] = f6, c4++;
  }
  for (const m6 of o2) {
    const { totalTime: t4, timeSeed: n2 } = m6;
    let o3 = null, i2 = null, l5 = null, a5 = null, s10 = null, c5 = null;
    for (let f6 = 0; f6 < m6.numberOfVertices; f6++) {
      const d4 = r3[4 * (m6.startVertex + f6) + 0], w5 = r3[4 * (m6.startVertex + f6) + 1], p5 = r3[4 * (m6.startVertex + f6) + 2], g5 = r3[4 * (m6.startVertex + f6) + 3];
      let y5 = null, x4 = null, M4 = null, A3 = null;
      if (f6 > 0) {
        y5 = d4 - o3, x4 = w5 - i2;
        const r4 = Math.sqrt(y5 * y5 + x4 * x4);
        if (y5 /= r4, x4 /= r4, f6 > 1) {
          let t5 = y5 + s10, n3 = x4 + c5;
          const r5 = Math.sqrt(t5 * t5 + n3 * n3);
          t5 /= r5, n3 /= r5;
          const o4 = Math.min(1 / (t5 * y5 + n3 * x4), e3);
          t5 *= o4, n3 *= o4, M4 = -n3, A3 = t5;
        } else M4 = -x4, A3 = y5;
        null !== M4 && null !== A3 && (h4(o3, i2, l5, M4, A3, t4, n2, g5), u5());
      }
      o3 = d4, i2 = w5, l5 = p5, s10 = y5, c5 = x4, a5 = g5;
    }
    h4(o3, i2, l5, -c5, s10, t4, n2, a5);
  }
  return { vertexData: a4, indexData: s9 };
}
function g4(t3) {
  const e3 = 16, n = 1, r3 = 2, { lineVertices: o2, lineDescriptors: i } = t3;
  let l4 = 0, a4 = 0;
  for (const j3 of i) {
    const t4 = j3.numberOfVertices - 1;
    l4 += 4 * t4 * 2, a4 += 6 * t4 * 2;
  }
  const s9 = new Float32Array(l4 * e3), c4 = new Uint32Array(a4);
  let f5, u5, h4, m6, d4, w5, p5, g5, y5, x4, M4, A3, I3, V, F3 = 0, D3 = 0;
  function b3() {
    c4[D3++] = F3 - 8, c4[D3++] = F3 - 7, c4[D3++] = F3 - 6, c4[D3++] = F3 - 7, c4[D3++] = F3 - 5, c4[D3++] = F3 - 6, c4[D3++] = F3 - 4, c4[D3++] = F3 - 3, c4[D3++] = F3 - 2, c4[D3++] = F3 - 3, c4[D3++] = F3 - 1, c4[D3++] = F3 - 2;
  }
  function v3(t4, o3, i2, l5, a5, c5, f6, u6, h5, m7, d5, w6, p6, g6) {
    const y6 = F3 * e3;
    let x5 = 0;
    for (const e4 of [n, r3]) for (const n2 of [1, 2, 3, 4]) s9[y6 + x5++] = t4, s9[y6 + x5++] = o3, s9[y6 + x5++] = i2, s9[y6 + x5++] = l5, s9[y6 + x5++] = f6, s9[y6 + x5++] = u6, s9[y6 + x5++] = h5, s9[y6 + x5++] = m7, s9[y6 + x5++] = e4, s9[y6 + x5++] = n2, s9[y6 + x5++] = p6, s9[y6 + x5++] = g6, s9[y6 + x5++] = a5 / 2, s9[y6 + x5++] = c5 / 2, s9[y6 + x5++] = d5 / 2, s9[y6 + x5++] = w6 / 2, F3++;
  }
  function S3(t4, e4) {
    let n2 = y5 + M4, r4 = x4 + A3;
    const o3 = Math.sqrt(n2 * n2 + r4 * r4);
    n2 /= o3, r4 /= o3;
    const i2 = y5 * n2 + x4 * r4;
    n2 /= i2, r4 /= i2;
    let l5 = M4 + I3, a5 = A3 + V;
    const s10 = Math.sqrt(l5 * l5 + a5 * a5);
    l5 /= s10, a5 /= s10;
    const c5 = M4 * l5 + A3 * a5;
    l5 /= c5, a5 /= c5, v3(f5, u5, h4, m6, -r4, n2, d4, w5, p5, g5, -a5, l5, t4, e4), b3();
  }
  function k3(t4, e4, n2, r4, o3, i2) {
    if (y5 = M4, x4 = A3, M4 = I3, A3 = V, null == y5 && null == x4 && (y5 = M4, x4 = A3), null != d4 && null != w5) {
      I3 = t4 - d4, V = e4 - w5;
      const n3 = Math.sqrt(I3 * I3 + V * V);
      I3 /= n3, V /= n3;
    }
    null != y5 && null != x4 && S3(o3, i2), f5 = d4, u5 = w5, h4 = p5, m6 = g5, d4 = t4, w5 = e4, p5 = n2, g5 = r4;
  }
  function L(t4, e4) {
    y5 = M4, x4 = A3, M4 = I3, A3 = V, null == y5 && null == x4 && (y5 = M4, x4 = A3), null != y5 && null != x4 && S3(t4, e4);
  }
  for (const j3 of i) {
    f5 = null, u5 = null, h4 = null, m6 = null, d4 = null, w5 = null, p5 = null, g5 = null, y5 = null, x4 = null, M4 = null, A3 = null, I3 = null, V = null;
    const { totalTime: t4, timeSeed: e4 } = j3;
    for (let n2 = 0; n2 < j3.numberOfVertices; n2++) {
      k3(o2[4 * (j3.startVertex + n2) + 0], o2[4 * (j3.startVertex + n2) + 1], o2[4 * (j3.startVertex + n2) + 2], o2[4 * (j3.startVertex + n2) + 3], t4, e4);
    }
    L(t4, e4);
  }
  return { vertexData: s9, indexData: c4 };
}
function y4(t3, n) {
  const r3 = n.pixels, { width: o2, height: i } = n, l4 = new Float32Array(o2 * i * 2), a4 = n.mask || new Uint8Array(o2 * i * 2);
  if (n.mask || a4.fill(255), "vector-uv" === t3) for (let e3 = 0; e3 < o2 * i; e3++) l4[2 * e3 + 0] = r3[0][e3], l4[2 * e3 + 1] = -r3[1][e3];
  else if ("vector-magdir" === t3) for (let s9 = 0; s9 < o2 * i; s9++) {
    const t4 = r3[0][s9], n2 = m(r3[1][s9]), o3 = Math.cos(n2 - Math.PI / 2), i2 = Math.sin(n2 - Math.PI / 2);
    l4[2 * s9 + 0] = o3 * t4, l4[2 * s9 + 1] = i2 * t4;
  }
  return { data: l4, mask: a4, width: o2, height: i };
}
async function x3(t3, e3, n, r3, o2, i) {
  const c4 = performance.now(), f5 = R(e3.spatialReference);
  if (!f5) {
    const l4 = await M3(t3, e3, n, r3, o2, i);
    return has("esri-2d-profiler") && s8.info("I.7", "loadImagery, early exit (ms)", Math.round(performance.now() - c4)), has("esri-2d-profiler") && s8.info("I.9", "Number of parts", 1), l4;
  }
  const [u5, h4] = f5.valid, m6 = h4 - u5, d4 = Math.ceil(e3.width / m6), w5 = e3.width / d4, p5 = Math.round(n / d4);
  let g5 = e3.xmin;
  const y5 = [], x4 = performance.now();
  for (let l4 = 0; l4 < d4; l4++) {
    const n2 = new w({ xmin: g5, xmax: g5 + w5, ymin: e3.ymin, ymax: e3.ymax, spatialReference: e3.spatialReference });
    y5.push(M3(t3, n2, p5, r3, o2, i)), g5 += w5;
  }
  const A3 = await Promise.all(y5);
  has("esri-2d-profiler") && s8.info("I.8", "All calls to _fetchPart (ms)", Math.round(performance.now() - x4)), has("esri-2d-profiler") && s8.info("I.9", "Number of parts", A3.length);
  const I3 = { data: new Float32Array(n * r3 * 2), mask: new Uint8Array(n * r3), width: n, height: r3 };
  let V = 0;
  for (const l4 of A3) {
    for (let t4 = 0; t4 < l4.height; t4++) for (let e4 = 0; e4 < l4.width; e4++) V + e4 >= n || (I3.data[2 * (t4 * n + V + e4) + 0] = l4.data[2 * (t4 * l4.width + e4) + 0], I3.data[2 * (t4 * n + V + e4) + 1] = l4.data[2 * (t4 * l4.width + e4) + 1], I3.mask[t4 * n + V + e4] = l4.mask[t4 * l4.width + e4]);
    V += l4.width;
  }
  return has("esri-2d-profiler") && s8.info("I.10", "loadImagery, general exit (ms)", Math.round(performance.now() - c4)), I3;
}
async function M3(t3, e3, o2, i, l4, a4) {
  const s9 = { requestProjectedLocalDirections: true, signal: a4 };
  if (r(l4) && (s9.timeExtent = l4), "imagery" === t3.type) {
    await t3.load({ signal: a4 });
    const r3 = t3.rasterInfo.dataType, l5 = await t3.fetchImage(e3, o2, i, s9);
    return !l5 || t(l5.pixelData) || t(l5.pixelData.pixelBlock) ? { data: new Float32Array(o2 * i * 2), mask: new Uint8Array(o2 * i), width: o2, height: i } : y4(r3, l5.pixelData.pixelBlock);
  }
  await t3.load({ signal: a4 });
  const c4 = t3.rasterInfo.dataType, f5 = await t3.fetchPixels(e3, o2, i, s9);
  return !f5 || t(f5.pixelBlock) ? { data: new Float32Array(o2 * i * 2), mask: new Uint8Array(o2 * i), width: o2, height: i } : y4(c4, f5.pixelBlock);
}

export {
  m2 as m,
  o,
  s6 as s,
  r2 as r,
  a2 as a,
  h,
  f2 as f,
  c,
  u2 as u,
  p2 as p,
  x,
  m3 as m2,
  g2 as g,
  k,
  M,
  A,
  T,
  b,
  S,
  R2 as R,
  D,
  F,
  c2,
  l3 as l,
  f3 as f2,
  m4 as m3,
  d2 as d,
  g3 as g2,
  x2,
  D2,
  F2,
  j2 as j,
  f4 as f3,
  x3
};
//# sourceMappingURL=chunk-Z27XYQOC.js.map
