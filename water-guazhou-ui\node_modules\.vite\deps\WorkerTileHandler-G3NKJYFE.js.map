{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/vectorTiles/ScriptUtils.js", "../../@arcgis/core/views/2d/engine/vectorTiles/TextShaping.js", "../../@arcgis/core/views/2d/engine/vectorTiles/Placement.js", "../../@arcgis/core/views/2d/engine/vectorTiles/Feature.js", "../../@arcgis/core/views/2d/engine/vectorTiles/IndexMemoryBuffer.js", "../../@arcgis/core/views/2d/engine/vectorTiles/SourceLayerData.js", "../../@arcgis/core/views/2d/engine/vectorTiles/VertexMemoryBuffer.js", "../../@arcgis/core/views/2d/engine/vectorTiles/buckets/BaseBucket.js", "../../@arcgis/core/views/2d/engine/vectorTiles/buckets/CircleBucket.js", "../../@arcgis/core/views/2d/engine/vectorTiles/buckets/FillBucket.js", "../../@arcgis/core/views/2d/engine/vectorTiles/buckets/LineBucket.js", "../../@arcgis/core/views/2d/engine/vectorTiles/buckets/SymbolBucket.js", "../../@arcgis/core/views/2d/tiling/enums.js", "../../@arcgis/core/views/2d/engine/vectorTiles/TileParser.js", "../../@arcgis/core/views/2d/engine/vectorTiles/WorkerTile.js", "../../@arcgis/core/views/2d/engine/vectorTiles/WorkerTileHandler.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction e(e){return 746===e||747===e||!(e<4352)&&(e>=12704&&e<=12735||(e>=12544&&e<=12591||(e>=65072&&e<=65103&&!(e>=65097&&e<=65103)||(e>=63744&&e<=64255||(e>=13056&&e<=13311||(e>=11904&&e<=12031||(e>=12736&&e<=12783||(e>=12288&&e<=12351&&!(e>=12296&&e<=12305||e>=12308&&e<=12319||12336===e)||(e>=13312&&e<=19903||(e>=19968&&e<=40959||(e>=12800&&e<=13055||(e>=12592&&e<=12687||(e>=43360&&e<=43391||(e>=55216&&e<=55295||(e>=4352&&e<=4607||(e>=44032&&e<=55215||(e>=12352&&e<=12447||(e>=12272&&e<=12287||(e>=12688&&e<=12703||(e>=12032&&e<=12255||(e>=12784&&e<=12799||(e>=12448&&e<=12543&&12540!==e||(e>=65280&&e<=65519&&!(65288===e||65289===e||65293===e||e>=65306&&e<=65310||65339===e||65341===e||65343===e||e>=65371&&e<=65503||65507===e||e>=65512&&e<=65519)||(e>=65104&&e<=65135&&!(e>=65112&&e<=65118||e>=65123&&e<=65126)||(e>=5120&&e<=5759||(e>=6320&&e<=6399||(e>=65040&&e<=65055||(e>=19904&&e<=19967||(e>=40960&&e<=42127||e>=42128&&e<=42191)))))))))))))))))))))))))))))}function c(e){return!(e<11904)&&(e>=12704&&e<=12735||(e>=12544&&e<=12591||(e>=65072&&e<=65103||(e>=63744&&e<=64255||(e>=13056&&e<=13311||(e>=11904&&e<=12031||(e>=12736&&e<=12783||(e>=12288&&e<=12351||(e>=13312&&e<=19903||(e>=19968&&e<=40959||(e>=12800&&e<=13055||(e>=65280&&e<=65519||(e>=12352&&e<=12447||(e>=12272&&e<=12287||(e>=12032&&e<=12255||(e>=12784&&e<=12799||(e>=12448&&e<=12543||(e>=65040&&e<=65055||(e>=42128&&e<=42191||e>=40960&&e<=42127)))))))))))))))))))}function s(e){switch(e){case 10:case 32:case 38:case 40:case 41:case 43:case 45:case 47:case 173:case 183:case 8203:case 8208:case 8211:case 8231:return!0}return!1}function a(e){switch(e){case 9:case 10:case 11:case 12:case 13:case 32:return!0}return!1}export{c as allowsIdeographicBreak,e as hasVerticalOrientation,s as isLineBreak,a as isWhiteSpace};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isLineBreak as t,isWhiteSpace as e,allowsIdeographicBreak as i,hasVerticalOrientation as s}from\"./ScriptUtils.js\";import o from\"../webgl/Rect.js\";const c=24,h=17;class a{constructor(t,e,i,s,o,c,h){this._glyphItems=t,this._maxWidth=e,this._lineHeight=i,this._letterSpacing=s,this._hAnchor=o,this._vAnchor=c,this._justify=h}getShaping(o,c,h){const a=this._letterSpacing,l=this._lineHeight,r=this._justify,n=this._maxWidth,m=[];let f=0,p=0;const g=o.length;for(let t=0;t<g;t++){const e=o.charCodeAt(t),i=h&&s(e);let c;for(const t of this._glyphItems)if(c=t[e],c)break;m.push({codePoint:e,x:f,y:p,vertical:i,glyphMosaicItem:c}),c&&(f+=c.metrics.advance+a)}let y=f;if(n>0){y=f/Math.max(1,Math.ceil(f/n))}const d=o.includes(\"​\"),x=[];for(let e=0;e<g-1;e++){const s=m[e].codePoint,o=i(s);if(t(s)||o){let t=0;if(10===s)t-=1e4;else if(o&&d)t+=150;else{40!==s&&65288!==s||(t+=50);const i=m[e+1].codePoint;41!==i&&65289!==i||(t+=50)}x.push(this._buildBreak(e+1,m[e].x,y,x,t,!1))}}const M=this._optimalBreaks(this._buildBreak(g,f,y,x,0,!0));let u=0;const _=c?-l:l;let I=0;for(let t=0;t<M.length;t++){const i=M[t];let s=I;for(;s<i&&e(m[s].codePoint);)m[s].glyphMosaicItem=null,++s;let o=i-1;for(;o>s&&e(m[o].codePoint);)m[o].glyphMosaicItem=null,--o;if(s<=o){const t=m[s].x;for(let i=s;i<=o;i++)m[i].x-=t,m[i].y=p;let e=m[o].x;m[o].glyphMosaicItem&&(e+=m[o].glyphMosaicItem.metrics.advance),u=Math.max(e,u),r&&this._applyJustification(m,s,o)}I=i,p+=_}if(m.length>0){const t=M.length-1,e=(r-this._hAnchor)*u;let i=(-this._vAnchor*(t+1)+.5)*l;c&&t&&(i+=t*l);for(const s of m)s.x+=e,s.y+=i}return m.filter((t=>t.glyphMosaicItem))}static getTextBox(t,e){if(!t.length)return null;let i=1/0,s=1/0,o=0,c=0;for(const a of t){const t=a.glyphMosaicItem.metrics.advance,l=a.x,r=a.y-h,n=l+t,m=r+e;i=Math.min(i,l),o=Math.max(o,n),s=Math.min(s,r),c=Math.max(c,m)}return{x:i,y:s,width:o-i,height:c-s}}static getBox(t){if(!t.length)return null;let e=1/0,i=1/0,s=0,o=0;for(const c of t){const{height:t,left:h,top:a,width:l}=c.glyphMosaicItem.metrics,r=c.x,n=c.y-(t-Math.abs(a)),m=r+l+h,f=n+t;e=Math.min(e,r),s=Math.max(s,m),i=Math.min(i,n),o=Math.max(o,f)}return{x:e,y:i,width:s-e,height:o-i}}static addDecoration(t,e){const i=t.length;if(0===i)return;const s=3;let c=t[0].x+t[0].glyphMosaicItem.metrics.left,h=t[0].y;for(let l=1;l<i;l++){const i=t[l];if(i.y!==h){const a=t[l-1].x+t[l-1].glyphMosaicItem.metrics.left+t[l-1].glyphMosaicItem.metrics.width;t.push({codePoint:0,x:c,y:h+e-s,vertical:!1,glyphMosaicItem:{sdf:!0,rect:new o(4,0,4,8),metrics:{width:a-c,height:2+2*s,left:0,top:0,advance:0},page:0,code:0}}),h=i.y,c=i.x+i.glyphMosaicItem.metrics.left}}const a=t[i-1].x+t[i-1].glyphMosaicItem.metrics.left+t[i-1].glyphMosaicItem.metrics.width;t.push({codePoint:0,x:c,y:h+e-s,vertical:!1,glyphMosaicItem:{sdf:!0,rect:new o(4,0,4,8),metrics:{width:a-c,height:2+2*s,left:0,top:0,advance:0},page:0,code:0}})}_breakScore(t,e,i,s){const o=(t-e)*(t-e);return s?t<e?o/2:2*o:o+Math.abs(i)*i}_buildBreak(t,e,i,s,o,c){let h=null,a=this._breakScore(e,i,o,c);for(const l of s){const t=e-l.x,s=this._breakScore(t,i,o,c)+l.score;s<=a&&(h=l,a=s)}return{index:t,x:e,score:a,previousBreak:h}}_optimalBreaks(t){return t?this._optimalBreaks(t.previousBreak).concat(t.index):[]}_applyJustification(t,e,i){const s=t[i],o=s.vertical?c:s.glyphMosaicItem?s.glyphMosaicItem.metrics.advance:0,h=(s.x+o)*this._justify;for(let c=e;c<=i;c++)t[c].x-=h}}export{h as SDF_GLYPH_BASELINE,c as SDF_GLYPH_SIZE,a as TextShaping};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Point as e}from\"../../../../geometry/support/TileClipper.js\";import{C_DEG_TO_RAD as t,C_PI as i,C_INFINITY as n,radToByte as s,positiveMod as o,C_2PI as a}from\"./GeometryUtils.js\";import{SDF_GLYPH_SIZE as l,TextShaping as h,SDF_GLYPH_BASELINE as r}from\"./TextShaping.js\";import{DECLUTTER_TILES as c}from\"./decluttering/config.js\";import{RotationAlignment as g,SymbolAnchor as m}from\"./style/StyleDefinition.js\";const x=4096,d=512,p=8,w=.5,T=2;class y{constructor(e,t,i=0,n=-1,s=w){this.x=e,this.y=t,this.angle=i,this.segment=n,this.minzoom=s}}class f{constructor(e,t,i,s,o,a=w,l=n){this.anchor=e,this.labelAngle=t,this.glyphAngle=i,this.page=s,this.alternateVerticalGlyph=o,this.minzoom=a,this.maxzoom=l}}class I{constructor(e,t,i,n,s,o,a,l,h,r,c,g){this.tl=e,this.tr=t,this.bl=i,this.br=n,this.mosaicRect=s,this.labelAngle=o,this.minAngle=a,this.maxAngle=l,this.anchor=h,this.minzoom=r,this.maxzoom=c,this.page=g}}class u{constructor(e){this.shapes=e}}class b{getIconPlacement(n,s,o){const a=new e(n.x,n.y),l=o.rotationAlignment===g.MAP,h=o.keepUpright;let r=o.rotate*t;l&&(r+=n.angle);const m=new u([]);return o.allowOverlap&&o.ignorePlacement||!c||(m.iconColliders=[]),this._addIconPlacement(m,a,s,o,r),l&&h&&this._addIconPlacement(m,a,s,o,r+i),m}_addIconPlacement(t,i,s,o,a){const l=s.pixelRatio,h=s.width/l,r=s.height/l,g=o.offset;let x=g[0],d=g[1];switch(o.anchor){case m.CENTER:x-=h/2,d-=r/2;break;case m.LEFT:d-=r/2;break;case m.RIGHT:x-=h,d-=r/2;break;case m.TOP:x-=h/2;break;case m.BOTTOM:x-=h/2,d-=r;break;case m.TOP_LEFT:break;case m.BOTTOM_LEFT:d-=r;break;case m.TOP_RIGHT:x-=h;break;case m.BOTTOM_RIGHT:x-=h,d-=r}const p=s.rect,T=2/l,y=x-T,f=d-T,u=y+p.width/l,b=f+p.height/l,P=new e(y,f),O=new e(u,b),_=new e(y,b),k=new e(u,f);if(0!==a){const e=Math.cos(a),t=Math.sin(a);P.rotate(e,t),O.rotate(e,t),_.rotate(e,t),k.rotate(e,t)}const M=new I(P,k,_,O,p,a,0,256,i,w,n,0);if(t.shapes.push(M),(!o.allowOverlap||!o.ignorePlacement)&&c){const e=o.size,s=o.padding,l={xTile:i.x,yTile:i.y,dxPixels:x*e-s,dyPixels:d*e-s,hard:!o.optional,partIndex:0,width:h*e+2*s,height:r*e+2*s,angle:a,minLod:w,maxLod:n};t.iconColliders.push(l)}}getTextPlacement(s,o,a,c){const x=new e(s.x,s.y),d=c.rotate*t,y=c.rotationAlignment===g.MAP,b=c.keepUpright,P=c.padding;let O=w;const _=!y?0:s.angle,k=s.segment>=0&&y,M=c.allowOverlap&&c.ignorePlacement?null:[],E=[],G=4,N=!k;let A=Number.POSITIVE_INFINITY,L=Number.NEGATIVE_INFINITY,z=A,F=L;const v=(k||y)&&b,R=c.size/l;let B=!1;for(const e of o)if(e.vertical){B=!0;break}let H,V=0,j=0;if(!k&&B){const e=h.getTextBox(o,c.lineHeight*l);switch(c.anchor){case m.LEFT:V=e.height/2,j=-e.width/2;break;case m.RIGHT:V=-e.height/2,j=e.width/2;break;case m.TOP:V=e.height/2,j=e.width/2;break;case m.BOTTOM:V=-e.height/2,j=-e.width/2;break;case m.TOP_LEFT:V=e.height;break;case m.BOTTOM_LEFT:j=-e.width;break;case m.TOP_RIGHT:j=e.width;break;case m.BOTTOM_RIGHT:V=-e.height}}V+=c.offset[0]*l,j+=c.offset[1]*l;for(const t of o){const o=t.glyphMosaicItem;if(!o||o.rect.isEmpty)continue;const l=o.rect,h=o.metrics,g=o.page;if(M&&N){if(void 0!==H&&H!==t.y){let e,t,i,o;B?(e=-F+V,t=A+j,i=F-z,o=L-A):(e=A+V,t=z+j,i=L-A,o=F-z);const a={xTile:s.x,yTile:s.y,dxPixels:e*R-P,dyPixels:t*R-P,hard:!c.optional,partIndex:1,width:i*R+2*P,height:o*R+2*P,angle:d,minLod:w,maxLod:n};M.push(a),A=Number.POSITIVE_INFINITY,L=Number.NEGATIVE_INFINITY,z=A,F=L}H=t.y}const m=[];if(k){const e=.5*o.metrics.width,i=(t.x+h.left-G+e)*R*p;if(O=this._placeGlyph(s,O,i,a,s.segment,1,t.vertical,g,m),b&&(O=this._placeGlyph(s,O,i,a,s.segment,-1,t.vertical,g,m)),O>=T)break}else m.push(new f(x,_,_,g,!1)),y&&b&&m.push(new f(x,_+i,_+i,g,!1));const u=t.x+h.left,C=t.y-r-h.top,S=u+h.width,Y=C+h.height;let q,U,D,J,K,Q,W,X;if(!k&&B)if(t.vertical){const t=(u+S)/2-h.height/2,i=(C+Y)/2+h.width/2;q=new e(-i-G+V,t-G+j),U=new e(q.x+l.width,q.y+l.height),D=new e(q.x,U.y),J=new e(U.x,q.y)}else q=new e(-C+G+V,u-G+j),U=new e(q.x-l.height,q.y+l.width),D=new e(U.x,q.y),J=new e(q.x,U.y);else q=new e(u-G+V,C-G+j),U=new e(q.x+l.width,q.y+l.height),D=new e(q.x,U.y),J=new e(U.x,q.y);for(const i of m){let n,o,a,r;if(i.alternateVerticalGlyph){if(!K){const t=(C+Y)/2+j;K=new e((u+S)/2+V-h.height/2-G,t+h.width/2+G),Q=new e(K.x+l.height,K.y-l.width),W=new e(Q.x,K.y),X=new e(K.x,Q.y)}n=K,o=W,a=X,r=Q}else n=q,o=D,a=J,r=U;const g=C,m=Y,x=i.glyphAngle+d;if(0!==x){const e=Math.cos(x),t=Math.sin(x);n=n.clone(),o=o?.clone(),a=a?.clone(),r=r?.clone(),n.rotate(e,t),r?.rotate(e,t),o?.rotate(e,t),a?.rotate(e,t)}let p=0,w=256;if(k&&B?t.vertical?i.alternateVerticalGlyph?(p=32,w=96):(p=224,w=32):(p=224,w=96):(p=192,w=64),E.push(new I(n,a,o,r,l,i.labelAngle,p,w,i.anchor,i.minzoom,i.maxzoom,i.page)),M&&(!v||this._legible(i.labelAngle)))if(N)u<A&&(A=u),g<z&&(z=g),S>L&&(L=S),m>F&&(F=m);else if(i.minzoom<T){const e={xTile:s.x,yTile:s.y,dxPixels:(u+V)*R-P,dyPixels:(g+V)*R-P,hard:!c.optional,partIndex:1,width:(S-u)*R+2*P,height:(m-g)*R+2*P,angle:x,minLod:i.minzoom,maxLod:i.maxzoom};M.push(e)}}}if(O>=T)return null;if(M&&N){let e,t,i,o;B?(e=-F+V,t=A+j,i=F-z,o=L-A):(e=A+V,t=z+j,i=L-A,o=F-z);const a={xTile:s.x,yTile:s.y,dxPixels:e*R-P,dyPixels:t*R-P,hard:!c.optional,partIndex:1,width:i*R+2*P,height:o*R+2*P,angle:d,minLod:w,maxLod:n};M.push(a)}const C=new u(E);return M&&M.length>0&&(C.textColliders=M),C}_legible(e){const t=s(e);return t<65||t>=193}_placeGlyph(t,s,l,h,r,c,g,m,x){let d=c;const p=d<0?o(t.angle+i,a):t.angle;let w=0;l<0&&(d*=-1,l*=-1,w=i),d>0&&++r;let T=new e(t.x,t.y),y=h[r],I=n;if(h.length<=r)return I;for(;;){const e=y.x-T.x,t=y.y-T.y,i=Math.sqrt(e*e+t*t),n=Math.max(l/i,s),c=e/i,u=t/i,b=o(Math.atan2(u,c)+w,a);if(x.push(new f(T,p,b,m,!1,n,I)),g&&x.push(new f(T,p,b,m,!0,n,I)),n<=s)return n;T=y.clone();do{if(r+=d,h.length<=r||r<0)return n;y=h[r]}while(T.isEqual(y));let P=y.x-T.x,O=y.y-T.y;const _=Math.sqrt(P*P+O*O);P*=i/_,O*=i/_,T.x-=P,T.y-=O,I=n}}}export{y as Anchor,I as PlacedSymbol,u as Placement,b as PlacementEngine,x as TILE_COORD_SIZE,p as TILE_PIXEL_RATIO,d as TILE_PIXEL_SIZE};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Point as e}from\"../../../../geometry/support/TileClipper.js\";var t;!function(e){e[e.moveTo=1]=\"moveTo\",e[e.lineTo=2]=\"lineTo\",e[e.close=7]=\"close\"}(t||(t={}));class s{constructor(e,t){this.values={},this._geometry=void 0,this._pbfGeometry=null;const s=t.keys,o=t.values,r=e.asUnsafe();for(;r.next();)switch(r.tag()){case 1:this.id=r.getUInt64();break;case 2:{const e=r.getMessage().asUnsafe(),t=this.values;for(;!e.empty();){const r=e.getUInt32(),a=e.getUInt32();t[s[r]]=o[a]}e.release();break}case 3:this.type=r.getUInt32();break;case 4:this._pbfGeometry=r.getMessage();break;default:r.skip()}}getGeometry(s){if(void 0!==this._geometry)return this._geometry;if(!this._pbfGeometry)return null;const o=this._pbfGeometry.asUnsafe();let r,a;this._pbfGeometry=null,s?s.reset(this.type):r=[];let n,i=t.moveTo,l=0,c=0,h=0;for(;!o.empty();){if(0===l){const e=o.getUInt32();i=7&e,l=e>>3}switch(l--,i){case t.moveTo:c+=o.getSInt32(),h+=o.getSInt32(),s?s.moveTo(c,h):r&&(a&&r.push(a),a=[],a.push(new e(c,h)));break;case t.lineTo:c+=o.getSInt32(),h+=o.getSInt32(),s?s.lineTo(c,h):a&&a.push(new e(c,h));break;case t.close:s?s.close():a&&!a[0].equals(c,h)&&a.push(a[0].clone());break;default:throw o.release(),new Error(\"Invalid path operation\")}}return s?n=s.result():r&&(a&&r.push(a),n=r),o.release(),this._geometry=n,n}}export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport s from\"./MemoryBuffer.js\";class r extends s{constructor(){super(12)}add(s,r,t){const e=this.array;e.push(s),e.push(r),e.push(t)}}class t extends s{constructor(){super(4)}add(s){this.array.push(s)}}export{t as PointElementMemoryBuffer,r as TriangleIndexBuffer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass e{constructor(t){this.extent=4096,this.keys=[],this.values=[],this._pbfLayer=t.clone();const s=t.asUnsafe();for(;s.next();)switch(s.tag()){case 1:this.name=s.getString();break;case 3:this.keys.push(s.getString());break;case 4:this.values.push(s.processMessage(e._parseValue));break;case 5:this.extent=s.getUInt32();break;default:s.skip()}}getData(){return this._pbfLayer}static _parseValue(e){for(;e.next();)switch(e.tag()){case 1:return e.getString();case 2:return e.getFloat();case 3:return e.getDouble();case 4:return e.getInt64();case 5:return e.getUInt64();case 6:return e.getSInt64();case 7:return e.getBool();default:e.skip()}return null}}export{e as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{radToByte as t}from\"./GeometryUtils.js\";import s from\"./MemoryBuffer.js\";class o extends s{constructor(t){super(t)}add(t,o,r,u,h,a,n,d,e,p,i,c){const M=this.array;let l=s.i1616to32(t,o);<PERSON><PERSON>push(l);const m=31;l=s.i8888to32(Math.round(m*r),Math.round(m*u),Math.round(m*h),Math.round(m*a)),M.push(l),l=s.i8888to32(Math.round(m*n),Math.round(m*d),Math.round(m*e),Math.round(m*p)),M.push(l),l=s.i1616to32(i,0),<PERSON>.push(l),c&&M.push(...c)}}class r extends s{constructor(t){super(t)}add(t,o,r){const u=this.array;u.push(s.i1616to32(t,o)),r&&u.push(...r)}}class u extends s{constructor(t){super(t)}add(t,o,r,u,h,a,n){const d=this.array,e=this.index;let p=s.i1616to32(t,o);d.push(p);const i=15;return p=s.i8888to32(Math.round(i*r),Math.round(i*u),h,a),d.push(p),n&&d.push(...n),e}}class h extends s{constructor(t){super(t)}add(o,r,u,h,a,n,d,e,p,i,c,M){const l=this.array;let m=s.i1616to32(o,r);l.push(m),m=s.i1616to32(Math.round(8*u),Math.round(8*h)),l.push(m),m=s.i8888to32(a/4,n/4,e,p),l.push(m),m=s.i8888to32(0,t(d),10*i,Math.min(10*c,255)),l.push(m),M&&l.push(...M)}}class a extends s{constructor(t){super(t)}add(t,o,r,u,h){const a=this.array,n=s.i1616to32(2*t+r,2*o+u);a.push(n),h&&a.push(...h)}}export{a as CircleVertexBuffer,r as FillVertexBuffer,o as LineVertexBuffer,u as OutlineVertexBuffer,h as SymbolVertexBuffer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t,e,s){this.layerExtent=4096,this._features=[],this.layer=t,this.zoom=e,this._spriteInfo=s,this._filter=t.getFeatureFilter()}pushFeature(t){this._filter&&!this._filter.filter(t,this.zoom)||this._features.push(t)}hasFeatures(){return this._features.length>0}getResources(t,e,s){}}export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{BucketType as e}from\"../enums.js\";import t from\"./BaseBucket.js\";class r extends t{constructor(t,r,i,c,s){super(t,r,i),this.type=e.CIRCLE,this._circleVertexBuffer=c,this._circleIndexBuffer=s}get circleIndexStart(){return this._circleIndexStart}get circleIndexCount(){return this._circleIndexCount}processFeatures(e){const t=this._circleVertexBuffer,r=this._circleIndexBuffer;this._circleIndexStart=3*r.index,this._circleIndexCount=0;const i=this.layer,c=this.zoom;e&&e.setExtent(this.layerExtent);for(const s of this._features){const n=s.getGeometry(e);if(!n)continue;const l=i.circleMaterial.encodeAttributes(s,c,i);for(const e of n)if(e)for(const i of e){const e=t.index;t.add(i.x,i.y,0,0,l),t.add(i.x,i.y,0,1,l),t.add(i.x,i.y,1,0,l),t.add(i.x,i.y,1,1,l),r.add(e+0,e+1,e+2),r.add(e+1,e+2,e+3),this._circleIndexCount+=6}}}serialize(){let e=6;e+=this.layerUIDs.length,e+=this._circleVertexBuffer.array.length,e+=this._circleIndexBuffer.array.length;const t=new Uint32Array(e),r=new Int32Array(t.buffer);let i=0;t[i++]=this.type,t[i++]=this.layerUIDs.length;for(let c=0;c<this.layerUIDs.length;c++)t[i++]=this.layerUIDs[c];t[i++]=this._circleIndexStart,t[i++]=this._circleIndexCount,t[i++]=this._circleVertexBuffer.array.length;for(let c=0;c<this._circleVertexBuffer.array.length;c++)r[i++]=this._circleVertexBuffer.array[c];t[i++]=this._circleIndexBuffer.array.length;for(let c=0;c<this._circleIndexBuffer.array.length;c++)t[i++]=this._circleIndexBuffer.array[c];return t.buffer}}export{r as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../../core/ArrayPool.js\";import{e}from\"../../../../../chunks/earcut.js\";import{triangulate as i}from\"../../../../../geometry/libtess.js\";import{Point as r}from\"../../../../../geometry/support/TileClipper.js\";import{BucketType as l}from\"../enums.js\";import n from\"./BaseBucket.js\";class s extends n{constructor(t,e,i,r,n,s,o){super(t,e,i),this.type=l.FILL,this._patternMap=new Map,this._fillVertexBuffer=r,this._fillIndexBuffer=n,this._outlineVertexBuffer=s,this._outlineIndexBuffer=o}get fillIndexStart(){return this._fillIndexStart}get fillIndexCount(){return this._fillIndexCount}get outlineIndexStart(){return this._outlineIndexStart}get outlineIndexCount(){return this._outlineIndexCount}getResources(t,e,i){const r=this.layer,l=this.zoom,n=r.getPaintProperty(\"fill-pattern\");if(n)if(n.isDataDriven)for(const s of this._features)e(n.getValue(l,s),!0);else e(n.getValue(l),!0)}processFeatures(t){this._fillIndexStart=3*this._fillIndexBuffer.index,this._fillIndexCount=0,this._outlineIndexStart=3*this._outlineIndexBuffer.index,this._outlineIndexCount=0;const e=this.layer,i=this.zoom,{fillMaterial:r,outlineMaterial:l,hasDataDrivenFill:n,hasDataDrivenOutline:s}=e;t&&t.setExtent(this.layerExtent);const o=e.getPaintProperty(\"fill-pattern\"),a=o?.isDataDriven;let f=!o&&e.getPaintValue(\"fill-antialias\",i);if(e.outlineUsesFillColor){if(f&&!e.hasDataDrivenOpacity){const t=e.getPaintValue(\"fill-opacity\",i),r=e.getPaintValue(\"fill-opacity\",i+1);t<1&&r<1&&(f=!1)}if(f&&!e.hasDataDrivenColor){const t=e.getPaintValue(\"fill-color\",i),r=e.getPaintValue(\"fill-color\",i+1);t[3]<1&&r[3]<1&&(f=!1)}}const u=this._features,d=t?.validateTessellation;if(a){const n=[];for(const a of u){const u=o.getValue(i,a),h=this._spriteInfo[u];if(!h||!h.rect)continue;const x=r.encodeAttributes(a,i,e,h),c=f&&s?l.encodeAttributes(a,i,e):[],_=a.getGeometry(t);n.push({ddFillAttributes:x,ddOutlineAttributes:c,page:h.page,geometry:_}),n.sort(((t,e)=>t.page-e.page));for(const{ddFillAttributes:t,ddOutlineAttributes:i,page:r,geometry:l}of n)this._processFeature(l,f,e.outlineUsesFillColor,t,i,d,r)}}else for(const h of u){const o=n?r.encodeAttributes(h,i,e):null,a=f&&s?l.encodeAttributes(h,i,e):null,u=h.getGeometry(t);this._processFeature(u,f,e.outlineUsesFillColor,o,a,d)}}serialize(){let t=10;t+=this.layerUIDs.length,t+=this._fillVertexBuffer.array.length,t+=this._fillIndexBuffer.array.length,t+=this._outlineVertexBuffer.array.length,t+=this._outlineIndexBuffer.array.length,t+=3*this._patternMap.size+1;const e=new Uint32Array(t),i=new Int32Array(e.buffer);let r=0;e[r++]=this.type,e[r++]=this.layerUIDs.length;for(let s=0;s<this.layerUIDs.length;s++)e[r++]=this.layerUIDs[s];e[r++]=this._fillIndexStart,e[r++]=this._fillIndexCount,e[r++]=this._outlineIndexStart,e[r++]=this._outlineIndexCount;const l=this._patternMap,n=l.size;if(e[r++]=n,n>0)for(const[s,[o,a]]of l)e[r++]=s,e[r++]=o,e[r++]=a;e[r++]=this._fillVertexBuffer.array.length;for(let s=0;s<this._fillVertexBuffer.array.length;s++)i[r++]=this._fillVertexBuffer.array[s];e[r++]=this._fillIndexBuffer.array.length;for(let s=0;s<this._fillIndexBuffer.array.length;s++)e[r++]=this._fillIndexBuffer.array[s];e[r++]=this._outlineVertexBuffer.array.length;for(let s=0;s<this._outlineVertexBuffer.array.length;s++)i[r++]=this._outlineVertexBuffer.array[s];e[r++]=this._outlineIndexBuffer.array.length;for(let s=0;s<this._outlineIndexBuffer.array.length;s++)e[r++]=this._outlineIndexBuffer.array[s];return e.buffer}_processFeature(t,e,i,r,l,n,o){if(!t)return;const a=t.length,f=!l||0===l.length;if(e&&(!i||f))for(let s=0;s<a;s++)this._processOutline(t[s],l);const u=32;let d;for(let h=0;h<a;h++){const e=s._area(t[h]);e>u?(void 0!==d&&this._processFill(t,d,r,n,o),d=[h]):e<-u&&void 0!==d&&d.push(h)}void 0!==d&&this._processFill(t,d,r,n,o)}_processOutline(t,e){const i=this._outlineVertexBuffer,l=this._outlineIndexBuffer,n=l.index;let s,o,a;const f=new r(0,0),u=new r(0,0),d=new r(0,0);let h=-1,x=-1,c=-1,_=-1,y=-1,g=!1;const p=0;let I=t.length;if(I<2)return;const B=t[p];let m=t[I-1];for(;I&&m.isEqual(B);)--I,m=t[I-1];if(!(I-p<2)){for(let r=p;r<I;++r){r===p?(s=t[I-1],o=t[p],a=t[p+1],f.assignSub(o,s),f.normalize(),f.rightPerpendicular()):(s=o,o=a,a=r!==I-1?t[r+1]:t[p],f.assign(u));const n=this._isClipEdge(s,o);-1===_&&(g=n),u.assignSub(a,o),u.normalize(),u.rightPerpendicular();const B=f.x*u.y-f.y*u.x;d.assignAdd(f,u),d.normalize();const m=-d.x*-f.x+-d.y*-f.y;let V=Math.abs(0!==m?1/m:1);V>8&&(V=8),B>=0?(c=i.add(o.x,o.y,f.x,f.y,0,1,e),-1===_&&(_=c),h>=0&&x>=0&&c>=0&&!n&&l.add(h,x,c),x=i.add(o.x,o.y,V*-d.x,V*-d.y,0,-1,e),-1===y&&(y=x),h>=0&&x>=0&&c>=0&&!n&&l.add(h,x,c),h=x,x=c,c=i.add(o.x,o.y,d.x,d.y,0,1,e),h>=0&&x>=0&&c>=0&&!n&&l.add(h,x,c),x=i.add(o.x,o.y,u.x,u.y,0,1,e),h>=0&&x>=0&&c>=0&&!n&&l.add(h,x,c)):(c=i.add(o.x,o.y,V*d.x,V*d.y,0,1,e),-1===_&&(_=c),h>=0&&x>=0&&c>=0&&!n&&l.add(h,x,c),x=i.add(o.x,o.y,-f.x,-f.y,0,-1,e),-1===y&&(y=x),h>=0&&x>=0&&c>=0&&!n&&l.add(h,x,c),h=x,x=c,c=i.add(o.x,o.y,-d.x,-d.y,0,-1,e),h>=0&&x>=0&&c>=0&&!n&&l.add(h,x,c),h=i.add(o.x,o.y,-u.x,-u.y,0,-1,e),h>=0&&x>=0&&c>=0&&!n&&l.add(h,x,c))}h>=0&&x>=0&&_>=0&&!g&&l.add(h,x,_),h>=0&&_>=0&&y>=0&&!g&&l.add(h,y,_),this._outlineIndexCount+=3*(l.index-n)}}_processFill(r,l,n,s,o){s=!0;let a;l.length>1&&(a=[]);let f=0;for(const t of l)0!==f&&a.push(f),f+=r[t].length;const u=2*f,d=t.acquire();for(const t of l){const e=r[t],i=e.length;for(let t=0;t<i;++t)d.push(e[t].x,e[t].y)}const h=e(d,a,2);if(s&&e.deviation(d,a,2,h)>0){const t=l.map((t=>r[t].length)),{buffer:e,vertexCount:s}=i(d,t);if(s>0){const t=this._fillVertexBuffer.index;for(let i=0;i<s;i++)this._fillVertexBuffer.add(e[2*i],e[2*i+1],n);for(let e=0;e<s;e+=3){const i=t+e;this._fillIndexBuffer.add(i,i+1,i+2)}if(void 0!==o){const t=this._patternMap,e=t.get(o);e?e[1]+=s:t.set(o,[this._fillIndexStart+this._fillIndexCount,s])}this._fillIndexCount+=s}}else{const t=h.length;if(t>0){const e=this._fillVertexBuffer.index;let i=0;for(;i<u;)this._fillVertexBuffer.add(d[i++],d[i++],n);let r=0;for(;r<t;)this._fillIndexBuffer.add(e+h[r++],e+h[r++],e+h[r++]);if(void 0!==o){const e=this._patternMap,i=e.get(o);i?i[1]+=t:e.set(o,[this._fillIndexStart+this._fillIndexCount,t])}this._fillIndexCount+=t}}t.release(d)}_isClipEdge(t,e){return t.x===e.x?t.x<=-64||t.x>=4160:t.y===e.y&&(t.y<=-64||t.y>=4160)}static _area(t){let e=0;const i=t.length-1;for(let r=0;r<i;r++)e+=(t[r].x-t[r+1].x)*(t[r].y+t[r+1].y);return e+=(t[i].x-t[0].x)*(t[i].y+t[0].y),.5*e}}export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{BucketType as e}from\"../enums.js\";import t from\"./BaseBucket.js\";import{LineTessellation as i}from\"../../webgl/TurboLine.js\";const s=65535;class n extends t{constructor(t,s,n,a,o){super(t,s,n),this.type=e.LINE,this._tessellationOptions={pixelCoordRatio:8,halfWidth:0,offset:0},this._patternMap=new Map,this.tessellationProperties={_lineVertexBuffer:null,_lineIndexBuffer:null,_ddValues:null},this.tessellationProperties._lineVertexBuffer=a,this.tessellationProperties._lineIndexBuffer=o,this._lineTessellator=new i(r(this.tessellationProperties),l(this.tessellationProperties),t.canUseThinTessellation)}get lineIndexStart(){return this._lineIndexStart}get lineIndexCount(){return this._lineIndexCount}getResources(e,t,i){const s=this.layer,n=this.zoom,r=s.getPaintProperty(\"line-pattern\"),l=s.getPaintProperty(\"line-dasharray\"),a=s.getLayoutProperty(\"line-cap\");if(!r&&!l)return;const o=a?.getValue(n)||0,u=a?.isDataDriven,f=r?.isDataDriven,h=l?.isDataDriven;if(f||h)for(const p of this._features)t(f?r.getValue(n,p):this._getDashArrayKey(p,n,s,l,u,a,o));else if(r)t(r.getValue(n));else if(l){const e=l.getValue(n);t(s.getDashKey(e,o))}}processFeatures(e){this._lineIndexStart=3*this.tessellationProperties._lineIndexBuffer.index,this._lineIndexCount=0;const t=this.layer,i=this.zoom,s=this._features,n=this._tessellationOptions,{hasDataDrivenLine:r,lineMaterial:l}=t;e&&e.setExtent(this.layerExtent);const a=t.getPaintProperty(\"line-pattern\"),o=t.getPaintProperty(\"line-dasharray\"),u=a?.isDataDriven,f=o?.isDataDriven;let h;h=t.getLayoutProperty(\"line-cap\");const p=h?.isDataDriven?h:null,g=p?null:t.getLayoutValue(\"line-cap\",i),y=g||0,d=!!p;h=t.getLayoutProperty(\"line-join\");const c=h?.isDataDriven?h:null,_=c?null:t.getLayoutValue(\"line-join\",i);h=t.getLayoutProperty(\"line-miter-limit\");const x=h?.isDataDriven?h:null,V=x?null:t.getLayoutValue(\"line-miter-limit\",i);h=t.getLayoutProperty(\"line-round-limit\");const m=h?.isDataDriven?h:null,D=m?null:t.getLayoutValue(\"line-round-limit\",i);h=t.getPaintProperty(\"line-width\");const P=h?.isDataDriven?h:null,I=P?null:t.getPaintValue(\"line-width\",i);h=t.getPaintProperty(\"line-offset\");const L=h?.isDataDriven?h:null,B=L?null:t.getPaintValue(\"line-offset\",i);if(u||f){const r=[];for(const n of s){const s=u?a.getValue(i,n):this._getDashArrayKey(n,i,t,o,d,p,y),f=this._spriteInfo[s];if(!f||!f.rect)continue;const h=l.encodeAttributes(n,i,t,f),v=n.getGeometry(e);r.push({ddAttributes:h,page:f.page,cap:p?p.getValue(i,n):g,join:c?c.getValue(i,n):_,miterLimit:x?x.getValue(i,n):V,roundLimit:m?m.getValue(i,n):D,halfWidth:.5*(P?P.getValue(i,n):I),offset:L?L.getValue(i,n):B,geometry:v})}r.sort(((e,t)=>e.page-t.page)),n.textured=!0;for(const{ddAttributes:e,page:t,cap:i,join:s,miterLimit:l,roundLimit:a,halfWidth:o,offset:u,geometry:f}of r)n.capType=i,n.joinType=s,n.miterLimit=l,n.roundLimit=a,n.halfWidth=o,n.offset=u,this._processFeature(f,e,t)}else{if(a){const e=a.getValue(i),t=this._spriteInfo[e];if(!t||!t.rect)return}n.textured=!(!a&&!o),n.capType=g,n.joinType=_,n.miterLimit=V,n.roundLimit=D,n.halfWidth=.5*I,n.offset=B;for(const a of s){const s=r?l.encodeAttributes(a,i,t):null;p&&(n.capType=p.getValue(i,a)),c&&(n.joinType=c.getValue(i,a)),x&&(n.miterLimit=x.getValue(i,a)),m&&(n.roundLimit=m.getValue(i,a)),P&&(n.halfWidth=.5*P.getValue(i,a)),L&&(n.offset=L.getValue(i,a));const o=a.getGeometry(e);this._processFeature(o,s)}}}serialize(){let e=6;e+=this.layerUIDs.length,e+=this.tessellationProperties._lineVertexBuffer.array.length,e+=this.tessellationProperties._lineIndexBuffer.array.length,e+=3*this._patternMap.size+1;const t=new Uint32Array(e),i=new Int32Array(t.buffer);let s=0;t[s++]=this.type,t[s++]=this.layerUIDs.length;for(let l=0;l<this.layerUIDs.length;l++)t[s++]=this.layerUIDs[l];t[s++]=this._lineIndexStart,t[s++]=this._lineIndexCount;const n=this._patternMap,r=n.size;if(t[s++]=r,r>0)for(const[l,[a,o]]of n)t[s++]=l,t[s++]=a,t[s++]=o;t[s++]=this.tessellationProperties._lineVertexBuffer.array.length;for(let l=0;l<this.tessellationProperties._lineVertexBuffer.array.length;l++)i[s++]=this.tessellationProperties._lineVertexBuffer.array[l];t[s++]=this.tessellationProperties._lineIndexBuffer.array.length;for(let l=0;l<this.tessellationProperties._lineIndexBuffer.array.length;l++)t[s++]=this.tessellationProperties._lineIndexBuffer.array[l];return t.buffer}_processFeature(e,t,i){if(!e)return;const s=e.length;for(let n=0;n<s;n++)this._processGeometry(e[n],t,i)}_processGeometry(e,t,i){if(e.length<2)return;const n=.001;let r,l,a=e[0],o=1;for(;o<e.length;)r=e[o].x-a.x,l=e[o].y-a.y,r*r+l*l<n*n?e.splice(o,1):(a=e[o],++o);if(e.length<2)return;const u=this.tessellationProperties._lineIndexBuffer,f=3*u.index;this._tessellationOptions.initialDistance=0,this._tessellationOptions.wrapDistance=s,this.tessellationProperties._ddValues=t,this._lineTessellator.tessellate(e,this._tessellationOptions);const h=3*u.index-f;if(void 0!==i){const e=this._patternMap,t=e.get(i);t?t[1]+=h:e.set(i,[f+this._lineIndexCount,h])}this._lineIndexCount+=h}_getDashArrayKey(e,t,i,s,n,r,l){const a=n?r.getValue(t,e):l,o=s.getValue(t,e);return i.getDashKey(o,a)}}const r=e=>(t,i,s,n,r,l,a,o,u,f,h)=>(e._lineVertexBuffer.add(t,i,a,o,s,n,r,l,u,f,h,e._ddValues),e._lineVertexBuffer.index-1),l=e=>(t,i,s)=>{e._lineIndexBuffer.add(t,i,s)};export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/BidiEngine.js\";import{assertIsSome as t}from\"../../../../../core/maybe.js\";import{numericHash as n}from\"../../../../../core/string.js\";import{GeometryType as i,Point as s}from\"../../../../../geometry/support/TileClipper.js\";import{BucketType as a}from\"../enums.js\";import{C_DEG_TO_RAD as o,log2 as r,interpolate as l}from\"../GeometryUtils.js\";import{TILE_PIXEL_RATIO as h,Anchor as x,TILE_COORD_SIZE as c}from\"../Placement.js\";import{SDF_GLYPH_SIZE as d,TextShaping as g}from\"../TextShaping.js\";import f from\"./BaseBucket.js\";import{TextTransform as y,SymbolPlacement as m,RotationAlignment as u,TextWritingMode as p,SymbolAnchor as _,TextJustification as M}from\"../style/StyleDefinition.js\";import{IconLayout as P,TextLayout as I}from\"../style/StyleLayer.js\";const b=10;function A(e,t){return e.iconMosaicItem&&t.iconMosaicItem?e.iconMosaicItem.page===t.iconMosaicItem.page?0:e.iconMosaicItem.page-t.iconMosaicItem.page:e.iconMosaicItem&&!t.iconMosaicItem?1:!e.iconMosaicItem&&t.iconMosaicItem?-1:0}class L extends f{constructor(e,t,n,i,s,o,r,l){super(e,t,l.getSpriteItems()),this.type=a.SYMBOL,this._markerMap=new Map,this._glyphMap=new Map,this._glyphBufferDataStorage=new Map,this._isIconSDF=!1,this._iconVertexBuffer=n,this._iconIndexBuffer=i,this._textVertexBuffer=s,this._textIndexBuffer=o,this._placementEngine=r,this._workerTileHandler=l}get markerPageMap(){return this._markerMap}get glyphsPageMap(){return this._glyphMap}get symbolInstances(){return this._symbolInstances}getResources(e,t,i){const s=this.layer,a=this.zoom;e&&e.setExtent(this.layerExtent);const o=s.getLayoutProperty(\"icon-image\"),r=s.getLayoutProperty(\"text-field\");let l=s.getLayoutProperty(\"text-transform\"),h=s.getLayoutProperty(\"text-font\");const x=[];let c,d,g,f;o&&!o.isDataDriven&&(c=o.getValue(a)),r&&!r.isDataDriven&&(d=r.getValue(a)),l&&l.isDataDriven||(g=s.getLayoutValue(\"text-transform\",a),l=null),h&&h.isDataDriven||(f=s.getLayoutValue(\"text-font\",a),h=null);for(const m of this._features){const u=m.getGeometry(e);if(!u||0===u.length)continue;let p,_;o&&(p=o.isDataDriven?o.getValue(a,m):this._replaceKeys(c,m.values),p&&t(p));let M=!1;if(r&&(_=r.isDataDriven?r.getValue(a,m):this._replaceKeys(d,m.values),_)){switch(_=_.replace(/\\\\n/g,\"\\n\"),l&&(g=l.getValue(a,m)),g){case y.LOWERCASE:_=_.toLowerCase();break;case y.UPPERCASE:_=_.toUpperCase()}if(L._bidiEngine.hasBidiChar(_)){let e;e=\"rtl\"===L._bidiEngine.checkContextual(_)?\"IDNNN\":\"ICNNN\",_=L._bidiEngine.bidiTransform(_,e,\"VLYSN\"),M=!0}const e=_.length;if(e>0){h&&(f=h.getValue(a,m));for(const t of f){let n=i[t];n||(n=i[t]=new Set);for(let t=0;t<e;t++){const e=_.charCodeAt(t);n.add(e)}}}}if(!p&&!_)continue;const P=s.getLayoutValue(\"symbol-sort-key\",a,m),I={feature:m,sprite:p,label:_,rtl:M,geometry:u,hash:(_?n(_):0)^(p?n(p):0),priority:P,textFont:f};x.push(I)}this._symbolFeatures=x}processFeatures(e){e&&e.setExtent(this.layerExtent);const n=this.layer,s=this.zoom,a=n.getLayoutValue(\"symbol-placement\",s),r=a!==m.POINT,l=n.getLayoutValue(\"symbol-spacing\",s)*h,f=n.getLayoutProperty(\"icon-image\"),y=n.getLayoutProperty(\"text-field\"),b=f?new P(n,s,r):null,V=y?new I(n,s,r):null,T=this._workerTileHandler;let w;f&&(w=T.getSpriteItems()),this._iconIndexStart=3*this._iconIndexBuffer.index,this._textIndexStart=3*this._textIndexBuffer.index,this._iconIndexCount=0,this._textIndexCount=0,this._markerMap.clear(),this._glyphMap.clear();const B=[];let C=1;V&&V.size&&(C=V.size/d);const R=V?V.maxAngle*o:0,D=V?V.size*h:0;for(const o of this._symbolFeatures){let e;b&&w&&o.sprite&&(e=w[o.sprite],e&&e.sdf&&(this._isIconSDF=!0));let n;!!e&&b.update(s,o.feature);let f=0;const y=o.label;if(y){t(V),V.update(s,o.feature);const e=r&&V.rotationAlignment===u.MAP?V.keepUpright:V.writingMode&&V.writingMode.includes(p.VERTICAL);let i=.5;switch(V.anchor){case _.TOP_LEFT:case _.LEFT:case _.BOTTOM_LEFT:i=0;break;case _.TOP_RIGHT:case _.RIGHT:case _.BOTTOM_RIGHT:i=1}let a=.5;switch(V.anchor){case _.TOP_LEFT:case _.TOP:case _.TOP_RIGHT:a=0;break;case _.BOTTOM_LEFT:case _.BOTTOM:case _.BOTTOM_RIGHT:a=1}let l=.5;switch(V.justify){case M.AUTO:l=i;break;case M.LEFT:l=0;break;case M.RIGHT:l=1}const x=V.letterSpacing*d,c=r?0:V.maxWidth*d,m=V.lineHeight*d,P=o.textFont.map((e=>T.getGlyphItems(e)));if(n=new g(P,c,m,x,i,a,l).getShaping(y,o.rtl,e),n&&n.length>0){let e=1e30,t=-1e30;for(const i of n)e=Math.min(e,i.x),t=Math.max(t,i.x);f=(t-e+2*d)*C*h}}for(let t of o.geometry){const s=[];if(a===m.LINE){if(n?.length&&V?.size){const e=V.size*h*(2+Math.min(2,4*Math.abs(V.offset[1])));t=L._smoothVertices(t,e)}L._pushAnchors(s,t,l,f)}else a===m.LINE_CENTER?L._pushCenterAnchor(s,t):o.feature.type===i.Polygon?L._pushCentroid(s,t):s.push(new x(t[0].x,t[0].y));for(const i of s){if(i.x<0||i.x>c||i.y<0||i.y>c)continue;if(r&&f>0&&V?.rotationAlignment===u.MAP&&!L._honorsTextMaxAngle(t,i,f,R,D))continue;const s={shaping:n,line:t,iconMosaicItem:e,anchor:i,symbolFeature:o,textColliders:[],iconColliders:[],textVertexRanges:[],iconVertexRanges:[]};B.push(s),this._processFeature(s,b,V)}}}B.sort(A),this._addPlacedGlyphs(),this._symbolInstances=B}serialize(){let e=11;e+=this.layerUIDs.length,e+=3*this.markerPageMap.size,e+=3*this.glyphsPageMap.size,e+=L._symbolsSerializationLength(this._symbolInstances),e+=this._iconVertexBuffer.array.length,e+=this._iconIndexBuffer.array.length,e+=this._textVertexBuffer.array.length,e+=this._textIndexBuffer.array.length;const t=new Uint32Array(e),n=new Int32Array(t.buffer),i=new Float32Array(t.buffer);let s=0;t[s++]=this.type,t[s++]=this.layerUIDs.length;for(let a=0;a<this.layerUIDs.length;a++)t[s++]=this.layerUIDs[a];t[s++]=this._isIconSDF?1:0,t[s++]=this.markerPageMap.size;for(const[a,[o,r]]of this.markerPageMap)t[s++]=a,t[s++]=o,t[s++]=r;t[s++]=this.glyphsPageMap.size;for(const[a,[o,r]]of this.glyphsPageMap)t[s++]=a,t[s++]=o,t[s++]=r;t[s++]=this._iconVertexBuffer.index/4,t[s++]=this._textVertexBuffer.index/4,s=L.serializeSymbols(t,n,i,s,this._symbolInstances),t[s++]=this._iconVertexBuffer.array.length;for(let a=0;a<this._iconVertexBuffer.array.length;a++)n[s++]=this._iconVertexBuffer.array[a];t[s++]=this._iconIndexBuffer.array.length;for(let a=0;a<this._iconIndexBuffer.array.length;a++)t[s++]=this._iconIndexBuffer.array[a];t[s++]=this._textVertexBuffer.array.length;for(let a=0;a<this._textVertexBuffer.array.length;a++)n[s++]=this._textVertexBuffer.array[a];t[s++]=this._textIndexBuffer.array.length;for(let a=0;a<this._textIndexBuffer.array.length;a++)t[s++]=this._textIndexBuffer.array[a];return t.buffer}static _symbolsSerializationLength(e){let t=0;t+=1;for(const n of e||[]){t+=4,t+=1;for(const e of n.textColliders)t+=b;for(const e of n.iconColliders)t+=b;t+=1,t+=2*n.textVertexRanges.length,t+=1,t+=2*n.iconVertexRanges.length}return t}static serializeSymbols(e,t,n,i,s){s=s||[],t[i++]=s.length;for(const a of s){t[i++]=a.anchor.x,t[i++]=a.anchor.y,t[i++]=a.symbolFeature.hash,t[i++]=a.symbolFeature.priority,t[i++]=a.textColliders.length+a.iconColliders.length;for(const e of a.textColliders)t[i++]=e.xTile,t[i++]=e.yTile,t[i++]=e.dxPixels,t[i++]=e.dyPixels,t[i++]=e.hard?1:0,t[i++]=e.partIndex,n[i++]=e.minLod,n[i++]=e.maxLod,t[i++]=e.width,t[i++]=e.height;for(const e of a.iconColliders)t[i++]=e.xTile,t[i++]=e.yTile,t[i++]=e.dxPixels,t[i++]=e.dyPixels,t[i++]=e.hard?1:0,t[i++]=e.partIndex,n[i++]=e.minLod,n[i++]=e.maxLod,t[i++]=e.width,t[i++]=e.height;t[i++]=a.textVertexRanges.length;for(const[e,n]of a.textVertexRanges)t[i++]=e,t[i++]=n;t[i++]=a.iconVertexRanges.length;for(const[e,n]of a.iconVertexRanges)t[i++]=e,t[i++]=n}return i}_replaceKeys(e,t){return e.replace(/{([^{}]+)}/g,((e,n)=>n in t?t[n]:\"\"))}_processFeature(e,t,n){const{line:i,iconMosaicItem:s,shaping:a,anchor:o}=e,l=this.zoom,h=this.layer,x=!!s;let c=!0;x&&(c=t?.optional||!s);const d=a&&a.length>0,g=!d||n?.optional;let f,y;if(x&&(f=this._placementEngine.getIconPlacement(o,s,t)),(f||c)&&(d&&(y=this._placementEngine.getTextPlacement(o,a,i,n)),y||g)){if(f&&y||(g||c?g||y?c||f||(y=null):f=null:(f=null,y=null)),y){const t=h.hasDataDrivenText?h.textMaterial.encodeAttributes(e.symbolFeature.feature,l,h):null;if(this._storePlacedGlyphs(e,y.shapes,l,n.rotationAlignment,t),y.textColliders){e.textColliders=y.textColliders;for(const e of y.textColliders){e.minLod=Math.max(l+r(e.minLod),0),e.maxLod=Math.min(l+r(e.maxLod),25);const t=e.angle;if(t){const n=Math.cos(t),i=Math.sin(t),s=e.dxPixels*n-e.dyPixels*i,a=e.dxPixels*i+e.dyPixels*n,o=(e.dxPixels+e.width)*n-e.dyPixels*i,r=(e.dxPixels+e.width)*i+e.dyPixels*n,l=e.dxPixels*n-(e.dyPixels+e.height)*i,h=e.dxPixels*i+(e.dyPixels+e.height)*n,x=(e.dxPixels+e.width)*n-(e.dyPixels+e.height)*i,c=(e.dxPixels+e.width)*i+(e.dyPixels+e.height)*n,d=Math.min(s,o,l,x),g=Math.max(s,o,l,x),f=Math.min(a,r,h,c),y=Math.max(a,r,h,c);e.dxPixels=d,e.dyPixels=f,e.width=g-d,e.height=y-f}}}}if(f){const n=h.hasDataDrivenIcon?h.iconMaterial.encodeAttributes(e.symbolFeature.feature,l,h):null;if(this._addPlacedIcons(e,f.shapes,l,s.page,t.rotationAlignment===u.VIEWPORT,n),f.iconColliders){e.iconColliders=f.iconColliders;for(const e of f.iconColliders){e.minLod=Math.max(l+r(e.minLod),0),e.maxLod=Math.min(l+r(e.maxLod),25);const t=e.angle;if(t){const n=Math.cos(t),i=Math.sin(t),s=e.dxPixels*n-e.dyPixels*i,a=e.dxPixels*i+e.dyPixels*n,o=(e.dxPixels+e.width)*n-e.dyPixels*i,r=(e.dxPixels+e.width)*i+e.dyPixels*n,l=e.dxPixels*n-(e.dyPixels+e.height)*i,h=e.dxPixels*i+(e.dyPixels+e.height)*n,x=(e.dxPixels+e.width)*n-(e.dyPixels+e.height)*i,c=(e.dxPixels+e.width)*i+(e.dyPixels+e.height)*n,d=Math.min(s,o,l,x),g=Math.max(s,o,l,x),f=Math.min(a,r,h,c),y=Math.max(a,r,h,c);e.dxPixels=d,e.dyPixels=f,e.width=g-d,e.height=y-f}}}}}}_addPlacedIcons(e,t,n,i,s,a){const o=Math.max(n-1,0),l=this._iconVertexBuffer,h=this._iconIndexBuffer,x=this._markerMap;for(const c of t){const t=s?0:Math.max(n+r(c.minzoom),o),d=s?25:Math.min(n+r(c.maxzoom),25);if(d<=t)continue;const g=c.tl,f=c.tr,y=c.bl,m=c.br,u=c.mosaicRect,p=c.labelAngle,_=c.minAngle,M=c.maxAngle,P=c.anchor,I=l.index,b=u.x,A=u.y,L=b+u.width,V=A+u.height,T=l.index;l.add(P.x,P.y,g.x,g.y,b,A,p,_,M,t,d,a),l.add(P.x,P.y,f.x,f.y,L,A,p,_,M,t,d,a),l.add(P.x,P.y,y.x,y.y,b,V,p,_,M,t,d,a),l.add(P.x,P.y,m.x,m.y,L,V,p,_,M,t,d,a),e.iconVertexRanges.length>0&&e.iconVertexRanges[0][0]+e.iconVertexRanges[0][1]===T?e.iconVertexRanges[0][1]+=4:e.iconVertexRanges.push([T,4]),h.add(I+0,I+1,I+2),h.add(I+1,I+2,I+3),x.has(i)?x.get(i)[1]+=6:x.set(i,[this._iconIndexStart+this._iconIndexCount,6]),this._iconIndexCount+=6}}_addPlacedGlyphs(){const e=this._textVertexBuffer,t=this._textIndexBuffer,n=this._glyphMap;for(const[i,s]of this._glyphBufferDataStorage)for(const a of s){const s=e.index,o=a.symbolInstance,r=a.ddAttributes,l=e.index;e.add(a.glyphAnchor[0],a.glyphAnchor[1],a.tl[0],a.tl[1],a.xmin,a.ymin,a.labelAngle,a.minAngle,a.maxAngle,a.minLod,a.maxLod,r),e.add(a.glyphAnchor[0],a.glyphAnchor[1],a.tr[0],a.tr[1],a.xmax,a.ymin,a.labelAngle,a.minAngle,a.maxAngle,a.minLod,a.maxLod,r),e.add(a.glyphAnchor[0],a.glyphAnchor[1],a.bl[0],a.bl[1],a.xmin,a.ymax,a.labelAngle,a.minAngle,a.maxAngle,a.minLod,a.maxLod,r),e.add(a.glyphAnchor[0],a.glyphAnchor[1],a.br[0],a.br[1],a.xmax,a.ymax,a.labelAngle,a.minAngle,a.maxAngle,a.minLod,a.maxLod,r),o.textVertexRanges.length>0&&o.textVertexRanges[0][0]+o.textVertexRanges[0][1]===l?o.textVertexRanges[0][1]+=4:o.textVertexRanges.push([l,4]),t.add(s+0,s+1,s+2),t.add(s+1,s+2,s+3),n.has(i)?n.get(i)[1]+=6:n.set(i,[this._textIndexStart+this._textIndexCount,6]),this._textIndexCount+=6}this._glyphBufferDataStorage.clear()}_storePlacedGlyphs(e,t,n,i,s){const a=Math.max(n-1,0),o=i===u.VIEWPORT;let l,h,x,c,d,g,f,y,m,p,_;for(const u of t){if(l=o?0:Math.max(n+r(u.minzoom),a),h=o?25:Math.min(n+r(u.maxzoom),25),h<=l)continue;x=u.tl,c=u.tr,d=u.bl,g=u.br,f=u.labelAngle,y=u.minAngle,m=u.maxAngle,p=u.anchor,_=u.mosaicRect,this._glyphBufferDataStorage.has(u.page)||this._glyphBufferDataStorage.set(u.page,[]);this._glyphBufferDataStorage.get(u.page).push({glyphAnchor:[p.x,p.y],tl:[x.x,x.y],tr:[c.x,c.y],bl:[d.x,d.y],br:[g.x,g.y],xmin:_.x,ymin:_.y,xmax:_.x+_.width,ymax:_.y+_.height,labelAngle:f,minAngle:y,maxAngle:m,minLod:l,maxLod:h,placementLod:a,symbolInstance:e,ddAttributes:s})}}static _pushAnchors(e,t,n,i){n+=i;let a=0;const o=t.length-1;for(let l=0;l<o;l++)a+=s.distance(t[l],t[l+1]);let r=i||n;if(r*=.5,a<=r)return;const h=r/a;let c=0,d=-(n=a/Math.max(Math.round(a/n),1))/2;const g=t.length-1;for(let s=0;s<g;s++){const i=t[s],a=t[s+1],o=a.x-i.x,r=a.y-i.y,g=Math.sqrt(o*o+r*r);let f;for(;d+n<c+g;){d+=n;const t=(d-c)/g,y=l(i.x,a.x,t),m=l(i.y,a.y,t);void 0===f&&(f=Math.atan2(r,o)),e.push(new x(y,m,f,s,h))}c+=g}}static _pushCenterAnchor(e,t){let n=0;const i=t.length-1;for(let l=0;l<i;l++)n+=s.distance(t[l],t[l+1]);const a=n/2;let o=0;const r=t.length-1;for(let s=0;s<r;s++){const n=t[s],i=t[s+1],r=i.x-n.x,h=i.y-n.y,c=Math.sqrt(r*r+h*h);if(a<o+c){const t=(a-o)/c,d=l(n.x,i.x,t),g=l(n.y,i.y,t),f=Math.atan2(h,r);return void e.push(new x(d,g,f,s,0))}o+=c}}static _deviation(e,t,n){const i=(t.x-e.x)*(n.x-t.x)+(t.y-e.y)*(n.y-t.y),s=(t.x-e.x)*(n.y-t.y)-(t.y-e.y)*(n.x-t.x);return Math.atan2(s,i)}static _honorsTextMaxAngle(e,t,n,i,a){let o=0;const r=n/2;let l=new s(t.x,t.y),h=t.segment+1;for(;o>-r;){if(--h,h<0)return!1;o-=s.distance(e[h],l),l=e[h]}o+=s.distance(e[h],e[h+1]);const x=[];let c=0;const d=e.length;for(;o<r;){const t=e[h];let n,r=h;do{if(++r,r===d)return!1;n=e[r]}while(n.isEqual(t));let l,g=r;do{if(++g,g===d)return!1;l=e[g]}while(l.isEqual(n));const f=this._deviation(t,n,l);for(x.push({deviation:f,distToAnchor:o}),c+=f;o-x[0].distToAnchor>a;)c-=x.shift().deviation;if(Math.abs(c)>i)return!1;o+=s.distance(n,l),h=r}return!0}static _smoothVertices(e,t){if(t<=0)return e;let n=e.length;if(n<3)return e;const i=[];let a=0,o=0;i.push(0);for(let y=1;y<n;y++){const t=s.distance(e[y],e[y-1]);t>0&&(a+=t,i.push(a),o++,o!==y&&(e[o]=e[y]))}if(n=o+1,n<3)return e;t=Math.min(t,.2*a);const r=e[0].x,l=e[0].y,h=e[n-1].x,x=e[n-1].y,c=s.sub(e[0],e[1]);c.normalize(),e[0].x+=t*c.x,e[0].y+=t*c.y,c.assignSub(e[n-1],e[n-2]),c.normalize(),e[n-1].x+=t*c.x,e[n-1].y+=t*c.y,i[0]-=t,i[n-1]+=t;const d=[];d.push(new s(r,l));const g=1e-6,f=.5*t;for(let y=1;y<n-1;y++){let a=0,o=0,r=0;for(let n=y-1;n>=0;n--){const s=f+i[n+1]-i[y];if(s<0)break;const l=i[n+1]-i[n],h=i[y]-i[n]<f?1:s/l;if(h<g)break;const x=h*h,c=h*s-.5*x*l,d=h*l/t,m=e[n+1],u=e[n].x-m.x,p=e[n].y-m.y;a+=d/c*(m.x*h*s+.5*x*(s*u-l*m.x)-x*h*l*u/3),o+=d/c*(m.y*h*s+.5*x*(s*p-l*m.y)-x*h*l*p/3),r+=d}for(let s=y+1;s<n;s++){const n=f-i[s-1]+i[y];if(n<0)break;const l=i[s]-i[s-1],h=i[s]-i[y]<f?1:n/l;if(h<g)break;const x=h*h,c=h*n-.5*x*l,d=h*l/t,m=e[s-1],u=e[s].x-m.x,p=e[s].y-m.y;a+=d/c*(m.x*h*n+.5*x*(n*u-l*m.x)-x*h*l*u/3),o+=d/c*(m.y*h*n+.5*x*(n*p-l*m.y)-x*h*l*p/3),r+=d}d.push(new s(a/r,o/r))}return d.push(new s(h,x)),e[0].x=r,e[0].y=l,e[n-1].x=h,e[n-1].y=x,d}static _pushCentroid(e,t){const n=0,i=0,s=4096,a=4096,o=t.length-1;let r=0,l=0,h=0,c=t[0].x,d=t[0].y;c>s&&(c=s),c<n&&(c=n),d>a&&(d=a),d<i&&(d=i);for(let x=1;x<o;x++){let e=t[x].x,o=t[x].y,g=t[x+1].x,f=t[x+1].y;e>s&&(e=s),e<n&&(e=n),o>a&&(o=a),o<i&&(o=i),g>s&&(g=s),g<n&&(g=n),f>a&&(f=a),f<i&&(f=i);const y=(e-c)*(f-d)-(g-c)*(o-d);r+=y*(c+e+g),l+=y*(d+o+f),h+=y}r/=3*h,l/=3*h,isNaN(r)||isNaN(l)||e.push(new x(r,l))}}L._bidiEngine=new e;export{L as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar I;!function(I){I[I.INITIALIZED=0]=\"INITIALIZED\",I[I.NO_DATA=1]=\"NO_DATA\",I[I.READY=2]=\"READY\",I[I.MODIFIED=3]=\"MODIFIED\",I[I.INVALID=4]=\"INVALID\"}(I||(I={}));export{I as TileStatus};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/pbf.js\";import{isAborted as t}from\"../../../../core/promiseUtils.js\";import{loadLibtess as r}from\"../../../../geometry/libtess.js\";import{TileClipper as s,SimpleBuilder as i}from\"../../../../geometry/support/TileClipper.js\";import o from\"./Feature.js\";import{TriangleIndexBuffer as n}from\"./IndexMemoryBuffer.js\";import c from\"./SourceLayerData.js\";import{FillVertexBuffer as l,OutlineVertexBuffer as a,LineVertexBuffer as u,CircleVertexBuffer as f,SymbolVertexBuffer as p}from\"./VertexMemoryBuffer.js\";import h from\"./buckets/CircleBucket.js\";import m from\"./buckets/FillBucket.js\";import _ from\"./buckets/LineBucket.js\";import y from\"./buckets/SymbolBucket.js\";import{StyleLayerType as d}from\"./style/StyleDefinition.js\";import{TileStatus as k}from\"../../tiling/enums.js\";const T=8,g=14,w=5;class B{constructor(t,r,o,n,c){if(this._pbfTiles={},this._tileClippers={},this._client=o,this._tile=r,c){this._styleLayerUIDs=new Set;for(const e of c)this._styleLayerUIDs.add(e)}this._styleRepository=n,this._layers=this._styleRepository?.layers??[];const[l,a,u]=r.tileKey.split(\"/\").map(parseFloat);this._level=l;const f=T+Math.max((this._level-g)*w,0);for(const p of Object.keys(t)){const r=t[p];this._pbfTiles[p]=new e(new Uint8Array(r.protobuff),new DataView(r.protobuff));if(r.refKey){const[e]=r.refKey.split(\"/\").map(parseFloat),t=l-e;if(t>0){const e=(1<<t)-1,r=a&e,i=u&e;this._tileClippers[p]=new s(t,r,i,8,f)}}this._tileClippers[p]||(this._tileClippers[p]=new i)}}_canParseStyleLayer(e){return!this._styleLayerUIDs||this._styleLayerUIDs.has(e)}async parse(e){const t=r(),s=this._initialize(e),{returnedBuckets:i}=s;this._processLayers(s),this._linkReferences(s),this._filterFeatures(s);const o=[],n=new Set,c=(e,t)=>{n.has(e)||(o.push({name:e,repeat:t}),n.add(e))},l={};for(const r of i)r.getResources(r.tileClipper,c,l);if(this._tile.status===k.INVALID)return[];const a=this._fetchResources(o,l,e);return Promise.all([...a,t]).then((()=>this._processFeatures(s.returnedBuckets)))}_initialize(e){return{signal:e&&e.signal,sourceNameToTileData:this._parseTileData(this._pbfTiles),layers:this._layers,zoom:this._level,sourceNameToTileClipper:this._tileClippers,sourceNameToUniqueSourceLayerBuckets:{},sourceNameToUniqueSourceLayers:{},returnedBuckets:[],layerIdToBucket:{},referencerUIDToReferencedId:new Map}}_processLayers(e){const{sourceNameToTileData:t,layers:r,zoom:s,sourceNameToTileClipper:i,sourceNameToUniqueSourceLayerBuckets:o,sourceNameToUniqueSourceLayers:n,returnedBuckets:c,layerIdToBucket:l,referencerUIDToReferencedId:a}=e;for(let u=r.length-1;u>=0;u--){const e=r[u];if(!this._canParseStyleLayer(e.uid)||e.minzoom&&s<Math.floor(e.minzoom)||e.maxzoom&&s>=e.maxzoom||e.type===d.BACKGROUND)continue;if(!t[e.source]||!i[e.source])continue;const f=t[e.source],p=i[e.source],h=e.sourceLayer,m=f[h];if(m){let t=n[e.source];if(t||(t=n[e.source]=new Set),t.add(e.sourceLayer),e.refLayerId)a.set(e.uid,e.refLayerId);else{const t=this._createBucket(e);if(t){t.layerUIDs=[e.uid],t.layerExtent=m.extent,t.tileClipper=p;let r=o[e.source];r||(r=o[e.source]={});let s=r[h];s||(s=r[h]=[]),s.push(t),c.push(t),l[e.id]=t}}}}}_linkReferences(e){const{layerIdToBucket:t,referencerUIDToReferencedId:r}=e;r.forEach(((e,r)=>{t[e]&&t[e].layerUIDs.push(r)}))}_filterFeatures(e){const{signal:r,sourceNameToTileData:s,sourceNameToUniqueSourceLayerBuckets:i,sourceNameToUniqueSourceLayers:n}=e,c=10*this._level,l=10*(this._level+1),a=[],u=[];for(const t of Object.keys(n)){n[t].forEach((e=>{a.push(e),u.push(t)}))}for(let f=0;f<a.length;f++){const e=u[f],n=a[f];if(!s[e]||!i[e])continue;const p=s[e][n],h=i[e][n];if(!h||0===h.length)continue;if(t(r))return;const m=p.getData();for(;m.nextTag(2);){const e=m.getMessage(),t=new o(e,p);e.release();const r=t.values;if(r){const e=r._minzoom;if(e&&e>=l)continue;const t=r._maxzoom;if(t&&t<=c)continue}for(const s of h)s.pushFeature(t)}}}_fetchResources(e,t,r){const s=[],i=this._tile.getWorkerTileHandler();let o,n;e.length>0&&(o=i.fetchSprites(e,this._client,r),s.push(o));for(const c in t){const e=t[c];e.size>0&&(n=i.fetchGlyphs(this._tile.tileKey,c,e,this._client,r),s.push(n))}return s}_processFeatures(e){const t=e.filter((e=>e.hasFeatures()||this._canParseStyleLayer(e.layer.uid)));for(const r of t)r.processFeatures(r.tileClipper);return t}_parseTileData(e){const t={};for(const r of Object.keys(e)){const s=e[r],i={};for(;s.next();)switch(s.tag()){case 3:{const e=s.getMessage(),t=new c(e);e.release(),i[t.name]=t;break}default:s.skip()}t[r]=i}return t}_createBucket(e){switch(e.type){case d.BACKGROUND:return null;case d.FILL:return this._createFillBucket(e);case d.LINE:return this._createLineBucket(e);case d.CIRCLE:return this._createCircleBucket(e);case d.SYMBOL:return this._createSymbolBucket(e)}}_createFillBucket(e){return new m(e,this._level,this._tile.getWorkerTileHandler().getSpriteItems(),new l(e.fillMaterial.getStride()),new n,new a(e.outlineMaterial.getStride()),new n)}_createLineBucket(e){return new _(e,this._level,this._tile.getWorkerTileHandler().getSpriteItems(),new u(e.lineMaterial.getStride()),new n)}_createCircleBucket(e){return new h(e,this._level,this._tile.getWorkerTileHandler().getSpriteItems(),new f(e.circleMaterial.getStride()),new n)}_createSymbolBucket(e){const t=this._tile;return new y(e,this._level,new p(e.iconMaterial.getStride()),new n,new p(e.textMaterial.getStride()),new n,t.placementEngine,t.getWorkerTileHandler())}}export{B as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../../core/maybe.js\";import{isAbortError as e}from\"../../../../core/promiseUtils.js\";import{PlacementEngine as s}from\"./Placement.js\";import r from\"./TileParser.js\";import{TileStatus as i}from\"../../tiling/enums.js\";class a{constructor(t,e,r,a){this.status=i.INITIALIZED,this.placementEngine=new s,this.tileKey=t,this.refKeys=e,this._workerTileHandler=r,this._styleRepository=a}release(){this.tileKey=\"\",this.refKeys=null,this.status=i.INITIALIZED,this._workerTileHandler=null}async parse(s,r){const a=r&&r.signal;if(t(a)){const t=()=>{a.removeEventListener(\"abort\",t),this.status=i.INVALID};a.addEventListener(\"abort\",t)}let n;const l={bucketsWithData:[],emptyBuckets:null};try{n=await this._parse(s,r)}catch(y){if(e(y))throw y;return{result:l,transferList:[]}}this.status=i.READY;const o=l.bucketsWithData,u=[];for(const t of n)if(t.hasFeatures()){const e=t.serialize();o.push(e)}else u.push(t.layer.uid);const h=[...o];let c=null;return u.length>0&&(c=Uint32Array.from(u),h.push(c.buffer)),l.emptyBuckets=c,{result:l,transferList:h}}setObsolete(){this.status=i.INVALID}getLayers(){return this._workerTileHandler.getLayers()}getWorkerTileHandler(){return this._workerTileHandler}async _parse(t,e){const s=t.sourceName2DataAndRefKey;if(0===Object.keys(s).length)return[];this.status=i.MODIFIED;return new r(s,this,e.client,this._styleRepository,t.styleLayerUIDs).parse(e)}}export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isAbortError as e}from\"../../../../core/promiseUtils.js\";import{StyleUpdateType as t}from\"./enums.js\";import s from\"./WorkerTile.js\";import r from\"./style/StyleRepository.js\";class o{constructor(){this._spriteInfo={},this._glyphInfo={}}reset(){return this._spriteInfo={},this._glyphInfo={},Promise.resolve()}getLayers(){return this._styleRepository?.layers??[]}async createTileAndParse(t,r){const{key:o}=t,i={};for(const e of Object.keys(t.sourceName2DataAndRefKey)){const s=t.sourceName2DataAndRefKey[e];i[e]=s.refKey}const n=new s(o,i,this,this._styleRepository);try{return await n.parse(t,r)}catch(l){if(n.setObsolete(),n.release(),!e(l))throw l;return null}}updateStyle(e){if(!e||0===e.length||!this._styleRepository)return;const s=this._styleRepository;for(const r of e){const e=r.type,o=r.data;switch(e){case t.PAINTER_CHANGED:s.setPaintProperties(o.layer,o.paint);break;case t.LAYOUT_CHANGED:s.setLayoutProperties(o.layer,o.layout);break;case t.LAYER_REMOVED:s.deleteStyleLayer(o.layer);break;case t.LAYER_CHANGED:s.setStyleLayer(o.layer,o.index);break;case t.SPRITES_CHANGED:this._spriteInfo={}}}}setStyle(e){this._styleRepository=new r(e),this._spriteInfo={},this._glyphInfo={}}fetchSprites(e,t,s){const r=[],o=this._spriteInfo;for(const i of e){void 0===o[i.name]&&r.push(i)}return 0===r.length?Promise.resolve():t.invoke(\"getSprites\",r,{signal:s&&s.signal}).then((e=>{for(const t in e){const s=e[t];o[t]=s}}))}getSpriteItems(){return this._spriteInfo}fetchGlyphs(e,t,s,r,o){const i=[];let n=this._glyphInfo[t];return n?s.forEach((e=>{n[e]||i.push(e)})):(n=this._glyphInfo[t]=[],s.forEach((e=>i.push(e)))),0===i.length?Promise.resolve():r.invoke(\"getGlyphs\",{tileID:e,font:t,codePoints:i},o).then((e=>{for(let t=0;t<e.length;t++)e[t]&&(n[t]=e[t])}))}getGlyphItems(e){return this._glyphInfo[e]}}export{o as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,SAASA,GAAEA,IAAE;AAAC,SAAO,QAAMA,MAAG,QAAMA,MAAG,EAAEA,KAAE,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,SAAO,EAAEA,MAAG,SAAOA,MAAG,WAASA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,SAAO,EAAEA,MAAG,SAAOA,MAAG,SAAOA,MAAG,SAAOA,MAAG,SAAO,UAAQA,QAAKA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,QAAMA,MAAG,SAAOA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,SAAO,UAAQA,OAAIA,MAAG,SAAOA,MAAG,SAAO,EAAE,UAAQA,MAAG,UAAQA,MAAG,UAAQA,MAAGA,MAAG,SAAOA,MAAG,SAAO,UAAQA,MAAG,UAAQA,MAAG,UAAQA,MAAGA,MAAG,SAAOA,MAAG,SAAO,UAAQA,MAAGA,MAAG,SAAOA,MAAG,WAASA,MAAG,SAAOA,MAAG,SAAO,EAAEA,MAAG,SAAOA,MAAG,SAAOA,MAAG,SAAOA,MAAG,WAASA,MAAG,QAAMA,MAAG,SAAOA,MAAG,QAAMA,MAAG,SAAOA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,SAAOA,MAAG,SAAOA,MAAG;AAAkC;AAAC,SAASC,GAAED,IAAE;AAAC,SAAM,EAAEA,KAAE,WAASA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,UAAQA,MAAG,SAAOA,MAAG,SAAOA,MAAG,SAAOA,MAAG;AAAwB;AAAC,SAAS,EAAEA,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAK,KAAK;AAAA,IAAK,KAAK;AAAA,IAAK,KAAK;AAAK,aAAM;AAAA,EAAE;AAAC,SAAM;AAAE;AAAC,SAASE,GAAEF,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAA,IAAE,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAG,aAAM;AAAA,EAAE;AAAC,SAAM;AAAE;;;ACApgD,IAAMG,KAAE;AAAR,IAAWC,KAAE;AAAG,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEP,IAAEC,IAAE;AAAC,SAAK,cAAYE,IAAE,KAAK,YAAUC,IAAE,KAAK,cAAYC,IAAE,KAAK,iBAAeC,IAAE,KAAK,WAASC,IAAE,KAAK,WAASP,IAAE,KAAK,WAASC;AAAA,EAAC;AAAA,EAAC,WAAWM,IAAEP,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,gBAAeM,KAAE,KAAK,aAAYC,KAAE,KAAK,UAASC,KAAE,KAAK,WAAUC,KAAE,CAAC;AAAE,QAAIC,KAAE,GAAEC,KAAE;AAAE,UAAMC,KAAEP,GAAE;AAAO,aAAQJ,KAAE,GAAEA,KAAEW,IAAEX,MAAI;AAAC,YAAMC,KAAEG,GAAE,WAAWJ,EAAC,GAAEE,KAAEJ,MAAGG,GAAEA,EAAC;AAAE,UAAIJ;AAAE,iBAAUG,MAAK,KAAK,YAAY,KAAGH,KAAEG,GAAEC,EAAC,GAAEJ,GAAE;AAAM,MAAAW,GAAE,KAAK,EAAC,WAAUP,IAAE,GAAEQ,IAAE,GAAEC,IAAE,UAASR,IAAE,iBAAgBL,GAAC,CAAC,GAAEA,OAAIY,MAAGZ,GAAE,QAAQ,UAAQE;AAAA,IAAE;AAAC,QAAIa,KAAEH;AAAE,QAAGF,KAAE,GAAE;AAAC,MAAAK,KAAEH,KAAE,KAAK,IAAI,GAAE,KAAK,KAAKA,KAAEF,EAAC,CAAC;AAAA,IAAC;AAAC,UAAM,IAAEH,GAAE,SAAS,GAAG,GAAES,KAAE,CAAC;AAAE,aAAQZ,KAAE,GAAEA,KAAEU,KAAE,GAAEV,MAAI;AAAC,YAAME,KAAEK,GAAEP,EAAC,EAAE,WAAUG,KAAEP,GAAEM,EAAC;AAAE,UAAG,EAAEA,EAAC,KAAGC,IAAE;AAAC,YAAIJ,KAAE;AAAE,YAAG,OAAKG,GAAE,CAAAH,MAAG;AAAA,iBAAYI,MAAG,EAAE,CAAAJ,MAAG;AAAA,aAAQ;AAAC,iBAAKG,MAAG,UAAQA,OAAIH,MAAG;AAAI,gBAAME,KAAEM,GAAEP,KAAE,CAAC,EAAE;AAAU,iBAAKC,MAAG,UAAQA,OAAIF,MAAG;AAAA,QAAG;AAAC,QAAAa,GAAE,KAAK,KAAK,YAAYZ,KAAE,GAAEO,GAAEP,EAAC,EAAE,GAAEW,IAAEC,IAAEb,IAAE,KAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,UAAM,IAAE,KAAK,eAAe,KAAK,YAAYW,IAAEF,IAAEG,IAAEC,IAAE,GAAE,IAAE,CAAC;AAAE,QAAIC,KAAE;AAAE,UAAMC,KAAElB,KAAE,CAACQ,KAAEA;AAAE,QAAIW,KAAE;AAAE,aAAQhB,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,YAAME,KAAE,EAAEF,EAAC;AAAE,UAAIG,KAAEa;AAAE,aAAKb,KAAED,MAAGH,GAAES,GAAEL,EAAC,EAAE,SAAS,IAAG,CAAAK,GAAEL,EAAC,EAAE,kBAAgB,MAAK,EAAEA;AAAE,UAAIC,KAAEF,KAAE;AAAE,aAAKE,KAAED,MAAGJ,GAAES,GAAEJ,EAAC,EAAE,SAAS,IAAG,CAAAI,GAAEJ,EAAC,EAAE,kBAAgB,MAAK,EAAEA;AAAE,UAAGD,MAAGC,IAAE;AAAC,cAAMJ,KAAEQ,GAAEL,EAAC,EAAE;AAAE,iBAAQD,KAAEC,IAAED,MAAGE,IAAEF,KAAI,CAAAM,GAAEN,EAAC,EAAE,KAAGF,IAAEQ,GAAEN,EAAC,EAAE,IAAEQ;AAAE,YAAIT,KAAEO,GAAEJ,EAAC,EAAE;AAAE,QAAAI,GAAEJ,EAAC,EAAE,oBAAkBH,MAAGO,GAAEJ,EAAC,EAAE,gBAAgB,QAAQ,UAASU,KAAE,KAAK,IAAIb,IAAEa,EAAC,GAAER,MAAG,KAAK,oBAAoBE,IAAEL,IAAEC,EAAC;AAAA,MAAC;AAAC,MAAAY,KAAEd,IAAEQ,MAAGK;AAAA,IAAC;AAAC,QAAGP,GAAE,SAAO,GAAE;AAAC,YAAMR,KAAE,EAAE,SAAO,GAAEC,MAAGK,KAAE,KAAK,YAAUQ;AAAE,UAAIZ,MAAG,CAAC,KAAK,YAAUF,KAAE,KAAG,OAAIK;AAAE,MAAAR,MAAGG,OAAIE,MAAGF,KAAEK;AAAG,iBAAUF,MAAKK,GAAE,CAAAL,GAAE,KAAGF,IAAEE,GAAE,KAAGD;AAAA,IAAC;AAAC,WAAOM,GAAE,OAAQ,CAAAR,OAAGA,GAAE,eAAgB;AAAA,EAAC;AAAA,EAAC,OAAO,WAAWA,IAAEC,IAAE;AAAC,QAAG,CAACD,GAAE,OAAO,QAAO;AAAK,QAAIE,KAAE,IAAE,GAAEC,KAAE,IAAE,GAAEC,KAAE,GAAEP,KAAE;AAAE,eAAUE,MAAKC,IAAE;AAAC,YAAMA,KAAED,GAAE,gBAAgB,QAAQ,SAAQM,KAAEN,GAAE,GAAEO,KAAEP,GAAE,IAAED,IAAES,KAAEF,KAAEL,IAAEQ,KAAEF,KAAEL;AAAE,MAAAC,KAAE,KAAK,IAAIA,IAAEG,EAAC,GAAED,KAAE,KAAK,IAAIA,IAAEG,EAAC,GAAEJ,KAAE,KAAK,IAAIA,IAAEG,EAAC,GAAET,KAAE,KAAK,IAAIA,IAAEW,EAAC;AAAA,IAAC;AAAC,WAAM,EAAC,GAAEN,IAAE,GAAEC,IAAE,OAAMC,KAAEF,IAAE,QAAOL,KAAEM,GAAC;AAAA,EAAC;AAAA,EAAC,OAAO,OAAOH,IAAE;AAAC,QAAG,CAACA,GAAE,OAAO,QAAO;AAAK,QAAIC,KAAE,IAAE,GAAEC,KAAE,IAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,eAAUP,MAAKG,IAAE;AAAC,YAAK,EAAC,QAAOA,IAAE,MAAKF,IAAE,KAAIC,IAAE,OAAMM,GAAC,IAAER,GAAE,gBAAgB,SAAQS,KAAET,GAAE,GAAEU,KAAEV,GAAE,KAAGG,KAAE,KAAK,IAAID,EAAC,IAAGS,KAAEF,KAAED,KAAEP,IAAEW,KAAEF,KAAEP;AAAE,MAAAC,KAAE,KAAK,IAAIA,IAAEK,EAAC,GAAEH,KAAE,KAAK,IAAIA,IAAEK,EAAC,GAAEN,KAAE,KAAK,IAAIA,IAAEK,EAAC,GAAEH,KAAE,KAAK,IAAIA,IAAEK,EAAC;AAAA,IAAC;AAAC,WAAM,EAAC,GAAER,IAAE,GAAEC,IAAE,OAAMC,KAAEF,IAAE,QAAOG,KAAEF,GAAC;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcF,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAE;AAAO,QAAG,MAAIE,GAAE;AAAO,UAAMC,KAAE;AAAE,QAAIN,KAAEG,GAAE,CAAC,EAAE,IAAEA,GAAE,CAAC,EAAE,gBAAgB,QAAQ,MAAKF,KAAEE,GAAE,CAAC,EAAE;AAAE,aAAQK,KAAE,GAAEA,KAAEH,IAAEG,MAAI;AAAC,YAAMH,KAAEF,GAAEK,EAAC;AAAE,UAAGH,GAAE,MAAIJ,IAAE;AAAC,cAAMC,KAAEC,GAAEK,KAAE,CAAC,EAAE,IAAEL,GAAEK,KAAE,CAAC,EAAE,gBAAgB,QAAQ,OAAKL,GAAEK,KAAE,CAAC,EAAE,gBAAgB,QAAQ;AAAM,QAAAL,GAAE,KAAK,EAAC,WAAU,GAAE,GAAEH,IAAE,GAAEC,KAAEG,KAAEE,IAAE,UAAS,OAAG,iBAAgB,EAAC,KAAI,MAAG,MAAK,IAAIH,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,SAAQ,EAAC,OAAMD,KAAEF,IAAE,QAAO,IAAE,IAAEM,IAAE,MAAK,GAAE,KAAI,GAAE,SAAQ,EAAC,GAAE,MAAK,GAAE,MAAK,EAAC,EAAC,CAAC,GAAEL,KAAEI,GAAE,GAAEL,KAAEK,GAAE,IAAEA,GAAE,gBAAgB,QAAQ;AAAA,MAAI;AAAA,IAAC;AAAC,UAAMH,KAAEC,GAAEE,KAAE,CAAC,EAAE,IAAEF,GAAEE,KAAE,CAAC,EAAE,gBAAgB,QAAQ,OAAKF,GAAEE,KAAE,CAAC,EAAE,gBAAgB,QAAQ;AAAM,IAAAF,GAAE,KAAK,EAAC,WAAU,GAAE,GAAEH,IAAE,GAAEC,KAAEG,KAAEE,IAAE,UAAS,OAAG,iBAAgB,EAAC,KAAI,MAAG,MAAK,IAAIH,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,SAAQ,EAAC,OAAMD,KAAEF,IAAE,QAAO,IAAE,IAAEM,IAAE,MAAK,GAAE,KAAI,GAAE,SAAQ,EAAC,GAAE,MAAK,GAAE,MAAK,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,MAAGJ,KAAEC,OAAID,KAAEC;AAAG,WAAOE,KAAEH,KAAEC,KAAEG,KAAE,IAAE,IAAEA,KAAEA,KAAE,KAAK,IAAIF,EAAC,IAAEA;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEP,IAAE;AAAC,QAAIC,KAAE,MAAKC,KAAE,KAAK,YAAYE,IAAEC,IAAEE,IAAEP,EAAC;AAAE,eAAUQ,MAAKF,IAAE;AAAC,YAAMH,KAAEC,KAAEI,GAAE,GAAEF,KAAE,KAAK,YAAYH,IAAEE,IAAEE,IAAEP,EAAC,IAAEQ,GAAE;AAAM,MAAAF,MAAGJ,OAAID,KAAEO,IAAEN,KAAEI;AAAA,IAAE;AAAC,WAAM,EAAC,OAAMH,IAAE,GAAEC,IAAE,OAAMF,IAAE,eAAcD,GAAC;AAAA,EAAC;AAAA,EAAC,eAAeE,IAAE;AAAC,WAAOA,KAAE,KAAK,eAAeA,GAAE,aAAa,EAAE,OAAOA,GAAE,KAAK,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAEH,GAAEE,EAAC,GAAEE,KAAED,GAAE,WAASN,KAAEM,GAAE,kBAAgBA,GAAE,gBAAgB,QAAQ,UAAQ,GAAEL,MAAGK,GAAE,IAAEC,MAAG,KAAK;AAAS,aAAQP,KAAEI,IAAEJ,MAAGK,IAAEL,KAAI,CAAAG,GAAEH,EAAC,EAAE,KAAGC;AAAA,EAAC;AAAC;;;ACA16F,IAAM,IAAE;AAAR,IAAmBmB,KAAE;AAArB,IAAuB,IAAE;AAAzB,IAA4BC,KAAE;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,KAAE,GAAEC,KAAE,IAAGC,KAAE,GAAE;AAAC,SAAK,IAAEJ,IAAE,KAAK,IAAEC,IAAE,KAAK,QAAMC,IAAE,KAAK,UAAQC,IAAE,KAAK,UAAQC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYL,IAAEC,IAAEC,IAAEE,IAAEE,IAAEC,KAAE,GAAEC,KAAEL,IAAE;AAAC,SAAK,SAAOH,IAAE,KAAK,aAAWC,IAAE,KAAK,aAAWC,IAAE,KAAK,OAAKE,IAAE,KAAK,yBAAuBE,IAAE,KAAK,UAAQC,IAAE,KAAK,UAAQC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYT,IAAEC,IAAEC,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,KAAGb,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAGC,IAAE,KAAK,aAAWC,IAAE,KAAK,aAAWE,IAAE,KAAK,WAASC,IAAE,KAAK,WAASC,IAAE,KAAK,SAAOE,IAAE,KAAK,UAAQC,IAAE,KAAK,UAAQC,IAAE,KAAK,OAAKC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYd,IAAE;AAAC,SAAK,SAAOA;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,iBAAiBG,IAAEC,IAAEE,IAAE;AAAC,UAAMC,KAAE,IAAI,EAAEJ,GAAE,GAAEA,GAAE,CAAC,GAAEK,KAAEF,GAAE,sBAAoB,EAAE,KAAII,KAAEJ,GAAE;AAAY,QAAIK,KAAEL,GAAE,SAAON;AAAE,IAAAQ,OAAIG,MAAGR,GAAE;AAAO,UAAMY,KAAE,IAAID,GAAE,CAAC,CAAC;AAAE,WAAOR,GAAE,gBAAcA,GAAE,mBAAiB,CAACA,OAAIS,GAAE,gBAAc,CAAC,IAAG,KAAK,kBAAkBA,IAAER,IAAEH,IAAEE,IAAEK,EAAC,GAAEH,MAAGE,MAAG,KAAK,kBAAkBK,IAAER,IAAEH,IAAEE,IAAEK,KAAEV,EAAC,GAAEc;AAAA,EAAC;AAAA,EAAC,kBAAkBd,IAAEC,IAAEE,IAAEE,IAAEC,IAAE;AAAC,UAAMC,KAAEJ,GAAE,YAAWM,KAAEN,GAAE,QAAMI,IAAEG,KAAEP,GAAE,SAAOI,IAAEK,KAAEP,GAAE;AAAO,QAAIU,KAAEH,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC;AAAE,YAAOP,GAAE,QAAO;AAAA,MAAC,KAAK,EAAE;AAAO,QAAAU,MAAGN,KAAE,GAAE,KAAGC,KAAE;AAAE;AAAA,MAAM,KAAK,EAAE;AAAK,aAAGA,KAAE;AAAE;AAAA,MAAM,KAAK,EAAE;AAAM,QAAAK,MAAGN,IAAE,KAAGC,KAAE;AAAE;AAAA,MAAM,KAAK,EAAE;AAAI,QAAAK,MAAGN,KAAE;AAAE;AAAA,MAAM,KAAK,EAAE;AAAO,QAAAM,MAAGN,KAAE,GAAE,KAAGC;AAAE;AAAA,MAAM,KAAK,EAAE;AAAS;AAAA,MAAM,KAAK,EAAE;AAAY,aAAGA;AAAE;AAAA,MAAM,KAAK,EAAE;AAAU,QAAAK,MAAGN;AAAE;AAAA,MAAM,KAAK,EAAE;AAAa,QAAAM,MAAGN,IAAE,KAAGC;AAAA,IAAC;AAAC,UAAMb,KAAEM,GAAE,MAAKL,KAAE,IAAES,IAAES,KAAED,KAAEjB,IAAEM,KAAE,IAAEN,IAAEe,KAAEG,KAAEnB,GAAE,QAAMU,IAAEU,KAAEb,KAAEP,GAAE,SAAOU,IAAE,IAAE,IAAI,EAAES,IAAEZ,EAAC,GAAE,IAAE,IAAI,EAAES,IAAEI,EAAC,GAAEC,KAAE,IAAI,EAAEF,IAAEC,EAAC,GAAE,IAAE,IAAI,EAAEJ,IAAET,EAAC;AAAE,QAAG,MAAIE,IAAE;AAAC,YAAMP,KAAE,KAAK,IAAIO,EAAC,GAAEN,KAAE,KAAK,IAAIM,EAAC;AAAE,QAAE,OAAOP,IAAEC,EAAC,GAAE,EAAE,OAAOD,IAAEC,EAAC,GAAEkB,GAAE,OAAOnB,IAAEC,EAAC,GAAE,EAAE,OAAOD,IAAEC,EAAC;AAAA,IAAC;AAAC,UAAM,IAAE,IAAIQ,GAAE,GAAE,GAAEU,IAAE,GAAErB,IAAES,IAAE,GAAE,KAAIL,IAAE,GAAEC,IAAE,CAAC;AAAE,QAAGF,GAAE,OAAO,KAAK,CAAC,IAAG,CAACK,GAAE,gBAAc,CAACA,GAAE,oBAAkBA,IAAE;AAAC,YAAMN,KAAEM,GAAE,MAAKF,KAAEE,GAAE,SAAQE,KAAE,EAAC,OAAMN,GAAE,GAAE,OAAMA,GAAE,GAAE,UAASc,KAAEhB,KAAEI,IAAE,UAAS,IAAEJ,KAAEI,IAAE,MAAK,CAACE,GAAE,UAAS,WAAU,GAAE,OAAMI,KAAEV,KAAE,IAAEI,IAAE,QAAOO,KAAEX,KAAE,IAAEI,IAAE,OAAMG,IAAE,QAAO,GAAE,QAAOJ,GAAC;AAAE,MAAAF,GAAE,cAAc,KAAKO,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBJ,IAAEE,IAAEC,IAAEK,IAAE;AAAC,UAAMI,KAAE,IAAI,EAAEZ,GAAE,GAAEA,GAAE,CAAC,GAAE,IAAEQ,GAAE,SAAOZ,IAAEiB,KAAEL,GAAE,sBAAoB,EAAE,KAAIM,KAAEN,GAAE,aAAY,IAAEA,GAAE;AAAQ,QAAI,IAAE;AAAE,UAAMO,KAAE,CAACF,KAAE,IAAEb,GAAE,OAAM,IAAEA,GAAE,WAAS,KAAGa,IAAE,IAAEL,GAAE,gBAAcA,GAAE,kBAAgB,OAAK,CAAC,GAAEQ,KAAE,CAAC,GAAE,IAAE,GAAEC,KAAE,CAAC;AAAE,QAAIC,KAAE,OAAO,mBAAkBC,KAAE,OAAO,mBAAkB,IAAED,IAAE,IAAEC;AAAE,UAAM,KAAG,KAAGN,OAAIC,IAAE,IAAEN,GAAE,OAAKA;AAAE,QAAIY,KAAE;AAAG,eAAUxB,MAAKM,GAAE,KAAGN,GAAE,UAAS;AAAC,MAAAwB,KAAE;AAAG;AAAA,IAAK;AAAC,QAAI,GAAE,IAAE,GAAEC,KAAE;AAAE,QAAG,CAAC,KAAGD,IAAE;AAAC,YAAMxB,KAAEO,GAAE,WAAWD,IAAEM,GAAE,aAAWA,EAAC;AAAE,cAAOA,GAAE,QAAO;AAAA,QAAC,KAAK,EAAE;AAAK,cAAEZ,GAAE,SAAO,GAAEyB,KAAE,CAACzB,GAAE,QAAM;AAAE;AAAA,QAAM,KAAK,EAAE;AAAM,cAAE,CAACA,GAAE,SAAO,GAAEyB,KAAEzB,GAAE,QAAM;AAAE;AAAA,QAAM,KAAK,EAAE;AAAI,cAAEA,GAAE,SAAO,GAAEyB,KAAEzB,GAAE,QAAM;AAAE;AAAA,QAAM,KAAK,EAAE;AAAO,cAAE,CAACA,GAAE,SAAO,GAAEyB,KAAE,CAACzB,GAAE,QAAM;AAAE;AAAA,QAAM,KAAK,EAAE;AAAS,cAAEA,GAAE;AAAO;AAAA,QAAM,KAAK,EAAE;AAAY,UAAAyB,KAAE,CAACzB,GAAE;AAAM;AAAA,QAAM,KAAK,EAAE;AAAU,UAAAyB,KAAEzB,GAAE;AAAM;AAAA,QAAM,KAAK,EAAE;AAAa,cAAE,CAACA,GAAE;AAAA,MAAM;AAAA,IAAC;AAAC,SAAGY,GAAE,OAAO,CAAC,IAAEA,IAAEa,MAAGb,GAAE,OAAO,CAAC,IAAEA;AAAE,eAAUX,MAAKK,IAAE;AAAC,YAAMA,KAAEL,GAAE;AAAgB,UAAG,CAACK,MAAGA,GAAE,KAAK,QAAQ;AAAS,YAAME,KAAEF,GAAE,MAAKI,KAAEJ,GAAE,SAAQO,KAAEP,GAAE;AAAK,UAAG,KAAGe,IAAE;AAAC,YAAG,WAAS,KAAG,MAAIpB,GAAE,GAAE;AAAC,cAAID,IAAEC,IAAEC,IAAEI;AAAE,UAAAkB,MAAGxB,KAAE,CAAC,IAAE,GAAEC,KAAEqB,KAAEG,IAAEvB,KAAE,IAAE,GAAEI,KAAEiB,KAAED,OAAItB,KAAEsB,KAAE,GAAErB,KAAE,IAAEwB,IAAEvB,KAAEqB,KAAED,IAAEhB,KAAE,IAAE;AAAG,gBAAMC,KAAE,EAAC,OAAMH,GAAE,GAAE,OAAMA,GAAE,GAAE,UAASJ,KAAE,IAAE,GAAE,UAASC,KAAE,IAAE,GAAE,MAAK,CAACW,GAAE,UAAS,WAAU,GAAE,OAAMV,KAAE,IAAE,IAAE,GAAE,QAAOI,KAAE,IAAE,IAAE,GAAE,OAAM,GAAE,QAAO,GAAE,QAAOH,GAAC;AAAE,YAAE,KAAKI,EAAC,GAAEe,KAAE,OAAO,mBAAkBC,KAAE,OAAO,mBAAkB,IAAED,IAAE,IAAEC;AAAA,QAAC;AAAC,YAAEtB,GAAE;AAAA,MAAC;AAAC,YAAMc,KAAE,CAAC;AAAE,UAAG,GAAE;AAAC,cAAMf,KAAE,MAAGM,GAAE,QAAQ,OAAMJ,MAAGD,GAAE,IAAES,GAAE,OAAK,IAAEV,MAAG,IAAEF;AAAE,YAAG,IAAE,KAAK,YAAYM,IAAE,GAAEF,IAAEK,IAAEH,GAAE,SAAQ,GAAEH,GAAE,UAASY,IAAEE,EAAC,GAAEG,OAAI,IAAE,KAAK,YAAYd,IAAE,GAAEF,IAAEK,IAAEH,GAAE,SAAQ,IAAGH,GAAE,UAASY,IAAEE,EAAC,IAAG,KAAGhB,GAAE;AAAA,MAAK,MAAM,CAAAgB,GAAE,KAAK,IAAIV,GAAEW,IAAEG,IAAEA,IAAEN,IAAE,KAAE,CAAC,GAAEI,MAAGC,MAAGH,GAAE,KAAK,IAAIV,GAAEW,IAAEG,KAAElB,IAAEkB,KAAElB,IAAEY,IAAE,KAAE,CAAC;AAAE,YAAMC,KAAEb,GAAE,IAAES,GAAE,MAAKgB,KAAEzB,GAAE,IAAES,KAAEA,GAAE,KAAI,IAAEI,KAAEJ,GAAE,OAAM,IAAEgB,KAAEhB,GAAE;AAAO,UAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,UAAG,CAAC,KAAGc,GAAE,KAAGvB,GAAE,UAAS;AAAC,cAAMA,MAAGa,KAAE,KAAG,IAAEJ,GAAE,SAAO,GAAER,MAAGwB,KAAE,KAAG,IAAEhB,GAAE,QAAM;AAAE,YAAE,IAAI,EAAE,CAACR,KAAE,IAAE,GAAED,KAAE,IAAEwB,EAAC,GAAE,IAAE,IAAI,EAAE,EAAE,IAAEjB,GAAE,OAAM,EAAE,IAAEA,GAAE,MAAM,GAAE,IAAE,IAAI,EAAE,EAAE,GAAE,EAAE,CAAC,GAAE,IAAE,IAAI,EAAE,EAAE,GAAE,EAAE,CAAC;AAAA,MAAC,MAAM,KAAE,IAAI,EAAE,CAACkB,KAAE,IAAE,GAAEZ,KAAE,IAAEW,EAAC,GAAE,IAAE,IAAI,EAAE,EAAE,IAAEjB,GAAE,QAAO,EAAE,IAAEA,GAAE,KAAK,GAAE,IAAE,IAAI,EAAE,EAAE,GAAE,EAAE,CAAC,GAAE,IAAE,IAAI,EAAE,EAAE,GAAE,EAAE,CAAC;AAAA,UAAO,KAAE,IAAI,EAAEM,KAAE,IAAE,GAAEY,KAAE,IAAED,EAAC,GAAE,IAAE,IAAI,EAAE,EAAE,IAAEjB,GAAE,OAAM,EAAE,IAAEA,GAAE,MAAM,GAAE,IAAE,IAAI,EAAE,EAAE,GAAE,EAAE,CAAC,GAAE,IAAE,IAAI,EAAE,EAAE,GAAE,EAAE,CAAC;AAAE,iBAAUN,MAAKa,IAAE;AAAC,YAAIZ,IAAEG,IAAEC,IAAEI;AAAE,YAAGT,GAAE,wBAAuB;AAAC,cAAG,CAAC,GAAE;AAAC,kBAAMD,MAAGyB,KAAE,KAAG,IAAED;AAAE,gBAAE,IAAI,GAAGX,KAAE,KAAG,IAAE,IAAEJ,GAAE,SAAO,IAAE,GAAET,KAAES,GAAE,QAAM,IAAE,CAAC,GAAE,IAAE,IAAI,EAAE,EAAE,IAAEF,GAAE,QAAO,EAAE,IAAEA,GAAE,KAAK,GAAE,IAAE,IAAI,EAAE,EAAE,GAAE,EAAE,CAAC,GAAE,IAAE,IAAI,EAAE,EAAE,GAAE,EAAE,CAAC;AAAA,UAAC;AAAC,UAAAL,KAAE,GAAEG,KAAE,GAAEC,KAAE,GAAEI,KAAE;AAAA,QAAC,MAAM,CAAAR,KAAE,GAAEG,KAAE,GAAEC,KAAE,GAAEI,KAAE;AAAE,cAAME,KAAEa,IAAEX,KAAE,GAAEC,KAAEd,GAAE,aAAW;AAAE,YAAG,MAAIc,IAAE;AAAC,gBAAMhB,KAAE,KAAK,IAAIgB,EAAC,GAAEf,KAAE,KAAK,IAAIe,EAAC;AAAE,UAAAb,KAAEA,GAAE,MAAM,GAAEG,KAAEA,MAAA,gBAAAA,GAAG,SAAQC,KAAEA,MAAA,gBAAAA,GAAG,SAAQI,KAAEA,MAAA,gBAAAA,GAAG,SAAQR,GAAE,OAAOH,IAAEC,EAAC,GAAEU,MAAA,gBAAAA,GAAG,OAAOX,IAAEC,KAAGK,MAAA,gBAAAA,GAAG,OAAON,IAAEC,KAAGM,MAAA,gBAAAA,GAAG,OAAOP,IAAEC;AAAA,QAAE;AAAC,YAAIH,KAAE,GAAE6B,KAAE;AAAI,YAAG,KAAGH,KAAEvB,GAAE,WAASC,GAAE,0BAAwBJ,KAAE,IAAG6B,KAAE,OAAK7B,KAAE,KAAI6B,KAAE,OAAK7B,KAAE,KAAI6B,KAAE,OAAK7B,KAAE,KAAI6B,KAAE,KAAIP,GAAE,KAAK,IAAIX,GAAEN,IAAEI,IAAED,IAAEK,IAAEH,IAAEN,GAAE,YAAWJ,IAAE6B,IAAEzB,GAAE,QAAOA,GAAE,SAAQA,GAAE,SAAQA,GAAE,IAAI,CAAC,GAAE,MAAI,CAAC,KAAG,KAAK,SAASA,GAAE,UAAU;AAAG,cAAGmB,GAAE,CAAAP,KAAEQ,OAAIA,KAAER,KAAGD,KAAE,MAAI,IAAEA,KAAG,IAAEU,OAAIA,KAAE,IAAGR,KAAE,MAAI,IAAEA;AAAA,mBAAWb,GAAE,UAAQH,IAAE;AAAC,kBAAMC,KAAE,EAAC,OAAMI,GAAE,GAAE,OAAMA,GAAE,GAAE,WAAUU,KAAE,KAAG,IAAE,GAAE,WAAUD,KAAE,KAAG,IAAE,GAAE,MAAK,CAACD,GAAE,UAAS,WAAU,GAAE,QAAO,IAAEE,MAAG,IAAE,IAAE,GAAE,SAAQC,KAAEF,MAAG,IAAE,IAAE,GAAE,OAAMG,IAAE,QAAOd,GAAE,SAAQ,QAAOA,GAAE,QAAO;AAAE,cAAE,KAAKF,EAAC;AAAA,UAAC;AAAA;AAAA,MAAC;AAAA,IAAC;AAAC,QAAG,KAAGD,GAAE,QAAO;AAAK,QAAG,KAAGsB,IAAE;AAAC,UAAIrB,IAAEC,IAAEC,IAAEI;AAAE,MAAAkB,MAAGxB,KAAE,CAAC,IAAE,GAAEC,KAAEqB,KAAEG,IAAEvB,KAAE,IAAE,GAAEI,KAAEiB,KAAED,OAAItB,KAAEsB,KAAE,GAAErB,KAAE,IAAEwB,IAAEvB,KAAEqB,KAAED,IAAEhB,KAAE,IAAE;AAAG,YAAMC,KAAE,EAAC,OAAMH,GAAE,GAAE,OAAMA,GAAE,GAAE,UAASJ,KAAE,IAAE,GAAE,UAASC,KAAE,IAAE,GAAE,MAAK,CAACW,GAAE,UAAS,WAAU,GAAE,OAAMV,KAAE,IAAE,IAAE,GAAE,QAAOI,KAAE,IAAE,IAAE,GAAE,OAAM,GAAE,QAAO,GAAE,QAAOH,GAAC;AAAE,QAAE,KAAKI,EAAC;AAAA,IAAC;AAAC,UAAMmB,KAAE,IAAIZ,GAAEM,EAAC;AAAE,WAAO,KAAG,EAAE,SAAO,MAAIM,GAAE,gBAAc,IAAGA;AAAA,EAAC;AAAA,EAAC,SAAS1B,IAAE;AAAC,UAAMC,KAAEC,GAAEF,EAAC;AAAE,WAAOC,KAAE,MAAIA,MAAG;AAAA,EAAG;AAAA,EAAC,YAAYA,IAAEG,IAAEI,IAAEE,IAAEC,IAAEC,IAAEC,IAAEE,IAAEC,IAAE;AAAC,QAAI,IAAEJ;AAAE,UAAMd,KAAE,IAAE,IAAE,EAAEG,GAAE,QAAMA,IAAEU,EAAC,IAAEV,GAAE;AAAM,QAAI0B,KAAE;AAAE,IAAAnB,KAAE,MAAI,KAAG,IAAGA,MAAG,IAAGmB,KAAE1B,KAAG,IAAE,KAAG,EAAEU;AAAE,QAAIZ,KAAE,IAAI,EAAEE,GAAE,GAAEA,GAAE,CAAC,GAAEgB,KAAEP,GAAEC,EAAC,GAAEF,KAAEN;AAAE,QAAGO,GAAE,UAAQC,GAAE,QAAOF;AAAE,eAAO;AAAC,YAAMT,KAAEiB,GAAE,IAAElB,GAAE,GAAEE,KAAEgB,GAAE,IAAElB,GAAE,GAAEG,KAAE,KAAK,KAAKF,KAAEA,KAAEC,KAAEA,EAAC,GAAEE,KAAE,KAAK,IAAIK,KAAEN,IAAEE,EAAC,GAAEQ,KAAEZ,KAAEE,IAAEY,KAAEb,KAAEC,IAAEgB,KAAE,EAAE,KAAK,MAAMJ,IAAEF,EAAC,IAAEe,IAAEhB,EAAC;AAAE,UAAGK,GAAE,KAAK,IAAIX,GAAEN,IAAED,IAAEoB,IAAEH,IAAE,OAAGZ,IAAEM,EAAC,CAAC,GAAEI,MAAGG,GAAE,KAAK,IAAIX,GAAEN,IAAED,IAAEoB,IAAEH,IAAE,MAAGZ,IAAEM,EAAC,CAAC,GAAEN,MAAGC,GAAE,QAAOD;AAAE,MAAAJ,KAAEkB,GAAE,MAAM;AAAE,SAAE;AAAC,YAAGN,MAAG,GAAED,GAAE,UAAQC,MAAGA,KAAE,EAAE,QAAOR;AAAE,QAAAc,KAAEP,GAAEC,EAAC;AAAA,MAAC,SAAOZ,GAAE,QAAQkB,EAAC;AAAG,UAAI,IAAEA,GAAE,IAAElB,GAAE,GAAE,IAAEkB,GAAE,IAAElB,GAAE;AAAE,YAAMoB,KAAE,KAAK,KAAK,IAAE,IAAE,IAAE,CAAC;AAAE,WAAGjB,KAAEiB,IAAE,KAAGjB,KAAEiB,IAAEpB,GAAE,KAAG,GAAEA,GAAE,KAAG,GAAEU,KAAEN;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACA/oL,IAAIyB;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE;AAAO,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAME,KAAN,MAAO;AAAA,EAAC,YAAYD,IAAED,IAAE;AAAC,SAAK,SAAO,CAAC,GAAE,KAAK,YAAU,QAAO,KAAK,eAAa;AAAK,UAAME,KAAEF,GAAE,MAAKG,KAAEH,GAAE,QAAOI,KAAEH,GAAE,SAAS;AAAE,WAAKG,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,MAAC,KAAK;AAAE,aAAK,KAAGA,GAAE,UAAU;AAAE;AAAA,MAAM,KAAK,GAAE;AAAC,cAAMH,KAAEG,GAAE,WAAW,EAAE,SAAS,GAAEJ,KAAE,KAAK;AAAO,eAAK,CAACC,GAAE,MAAM,KAAG;AAAC,gBAAMG,MAAEH,GAAE,UAAU,GAAEI,KAAEJ,GAAE,UAAU;AAAE,UAAAD,GAAEE,GAAEE,GAAC,CAAC,IAAED,GAAEE,EAAC;AAAA,QAAC;AAAC,QAAAJ,GAAE,QAAQ;AAAE;AAAA,MAAK;AAAA,MAAC,KAAK;AAAE,aAAK,OAAKG,GAAE,UAAU;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,eAAaA,GAAE,WAAW;AAAE;AAAA,MAAM;AAAQ,QAAAA,GAAE,KAAK;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAE;AAAC,QAAG,WAAS,KAAK,UAAU,QAAO,KAAK;AAAU,QAAG,CAAC,KAAK,aAAa,QAAO;AAAK,UAAMC,KAAE,KAAK,aAAa,SAAS;AAAE,QAAIC,IAAEC;AAAE,SAAK,eAAa,MAAKH,KAAEA,GAAE,MAAM,KAAK,IAAI,IAAEE,KAAE,CAAC;AAAE,QAAIE,IAAEC,KAAEP,GAAE,QAAOQ,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAAK,CAACP,GAAE,MAAM,KAAG;AAAC,UAAG,MAAIK,IAAE;AAAC,cAAMP,KAAEE,GAAE,UAAU;AAAE,QAAAI,KAAE,IAAEN,IAAEO,KAAEP,MAAG;AAAA,MAAC;AAAC,cAAOO,MAAID,IAAE;AAAA,QAAC,KAAKP,GAAE;AAAO,UAAAS,MAAGN,GAAE,UAAU,GAAEO,MAAGP,GAAE,UAAU,GAAED,KAAEA,GAAE,OAAOO,IAAEC,EAAC,IAAEN,OAAIC,MAAGD,GAAE,KAAKC,EAAC,GAAEA,KAAE,CAAC,GAAEA,GAAE,KAAK,IAAI,EAAEI,IAAEC,EAAC,CAAC;AAAG;AAAA,QAAM,KAAKV,GAAE;AAAO,UAAAS,MAAGN,GAAE,UAAU,GAAEO,MAAGP,GAAE,UAAU,GAAED,KAAEA,GAAE,OAAOO,IAAEC,EAAC,IAAEL,MAAGA,GAAE,KAAK,IAAI,EAAEI,IAAEC,EAAC,CAAC;AAAE;AAAA,QAAM,KAAKV,GAAE;AAAM,UAAAE,KAAEA,GAAE,MAAM,IAAEG,MAAG,CAACA,GAAE,CAAC,EAAE,OAAOI,IAAEC,EAAC,KAAGL,GAAE,KAAKA,GAAE,CAAC,EAAE,MAAM,CAAC;AAAE;AAAA,QAAM;AAAQ,gBAAMF,GAAE,QAAQ,GAAE,IAAI,MAAM,wBAAwB;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOD,KAAEI,KAAEJ,GAAE,OAAO,IAAEE,OAAIC,MAAGD,GAAE,KAAKC,EAAC,GAAEC,KAAEF,KAAGD,GAAE,QAAQ,GAAE,KAAK,YAAUG,IAAEA;AAAA,EAAC;AAAC;;;ACAnwC,IAAMK,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,EAAE;AAAA,EAAC;AAAA,EAAC,IAAIC,IAAEF,IAAEC,IAAE;AAAC,UAAME,KAAE,KAAK;AAAM,IAAAA,GAAE,KAAKD,EAAC,GAAEC,GAAE,KAAKH,EAAC,GAAEG,GAAE,KAAKF,EAAC;AAAA,EAAC;AAAC;;;ACAvI,IAAMG,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,SAAO,MAAK,KAAK,OAAK,CAAC,GAAE,KAAK,SAAO,CAAC,GAAE,KAAK,YAAUA,GAAE,MAAM;AAAE,UAAMC,KAAED,GAAE,SAAS;AAAE,WAAKC,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,MAAC,KAAK;AAAE,aAAK,OAAKA,GAAE,UAAU;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,KAAK,KAAKA,GAAE,UAAU,CAAC;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,OAAO,KAAKA,GAAE,eAAe,GAAE,WAAW,CAAC;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,SAAOA,GAAE,UAAU;AAAE;AAAA,MAAM;AAAQ,QAAAA,GAAE,KAAK;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,OAAO,YAAYF,IAAE;AAAC,WAAKA,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,MAAC,KAAK;AAAE,eAAOA,GAAE,UAAU;AAAA,MAAE,KAAK;AAAE,eAAOA,GAAE,SAAS;AAAA,MAAE,KAAK;AAAE,eAAOA,GAAE,UAAU;AAAA,MAAE,KAAK;AAAE,eAAOA,GAAE,SAAS;AAAA,MAAE,KAAK;AAAE,eAAOA,GAAE,UAAU;AAAA,MAAE,KAAK;AAAE,eAAOA,GAAE,UAAU;AAAA,MAAE,KAAK;AAAE,eAAOA,GAAE,QAAQ;AAAA,MAAE;AAAQ,QAAAA,GAAE,KAAK;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAC;;;ACA3jB,IAAMG,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAED,IAAEE,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAM,IAAE,KAAK;AAAM,QAAIC,KAAEV,GAAE,UAAUA,IAAED,EAAC;AAAE,MAAE,KAAKW,EAAC;AAAE,UAAMC,KAAE;AAAG,IAAAD,KAAEV,GAAE,UAAU,KAAK,MAAMW,KAAEV,EAAC,GAAE,KAAK,MAAMU,KAAET,EAAC,GAAE,KAAK,MAAMS,KAAER,EAAC,GAAE,KAAK,MAAMQ,KAAEP,EAAC,CAAC,GAAE,EAAE,KAAKM,EAAC,GAAEA,KAAEV,GAAE,UAAU,KAAK,MAAMW,KAAEN,EAAC,GAAE,KAAK,MAAMM,KAAE,CAAC,GAAE,KAAK,MAAMA,KAAEL,EAAC,GAAE,KAAK,MAAMK,KAAEJ,EAAC,CAAC,GAAE,EAAE,KAAKG,EAAC,GAAEA,KAAEV,GAAE,UAAUQ,IAAE,CAAC,GAAE,EAAE,KAAKE,EAAC,GAAED,MAAG,EAAE,KAAK,GAAGA,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMR,KAAN,cAAgBD,GAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAED,IAAEE,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAM,IAAAA,GAAE,KAAKF,GAAE,UAAUA,IAAED,EAAC,CAAC,GAAEE,MAAGC,GAAE,KAAK,GAAGD,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,cAAgBF,GAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAED,IAAEE,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAM,IAAE,KAAK,OAAMC,KAAE,KAAK;AAAM,QAAIC,KAAEP,GAAE,UAAUA,IAAED,EAAC;AAAE,MAAE,KAAKQ,EAAC;AAAE,UAAMC,KAAE;AAAG,WAAOD,KAAEP,GAAE,UAAU,KAAK,MAAMQ,KAAEP,EAAC,GAAE,KAAK,MAAMO,KAAEN,EAAC,GAAEC,IAAEC,EAAC,GAAE,EAAE,KAAKG,EAAC,GAAEF,MAAG,EAAE,KAAK,GAAGA,EAAC,GAAEC;AAAA,EAAC;AAAC;AAAC,IAAMH,KAAN,cAAgBH,GAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAID,IAAEE,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,GAAEC,IAAEC,IAAEC,IAAEC,IAAE,GAAE;AAAC,UAAMC,KAAE,KAAK;AAAM,QAAIC,KAAEX,GAAE,UAAUD,IAAEE,EAAC;AAAE,IAAAS,GAAE,KAAKC,EAAC,GAAEA,KAAEX,GAAE,UAAU,KAAK,MAAM,IAAEE,EAAC,GAAE,KAAK,MAAM,IAAEC,EAAC,CAAC,GAAEO,GAAE,KAAKC,EAAC,GAAEA,KAAEX,GAAE,UAAUI,KAAE,GAAEC,KAAE,GAAEC,IAAEC,EAAC,GAAEG,GAAE,KAAKC,EAAC,GAAEA,KAAEX,GAAE,UAAU,GAAEQ,GAAE,CAAC,GAAE,KAAGA,IAAE,KAAK,IAAI,KAAGC,IAAE,GAAG,CAAC,GAAEC,GAAE,KAAKC,EAAC,GAAE,KAAGD,GAAE,KAAK,GAAG,CAAC;AAAA,EAAC;AAAC;AAAC,IAAMN,KAAN,cAAgBJ,GAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAED,IAAEE,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAMC,KAAEL,GAAE,UAAU,IAAEA,KAAEC,IAAE,IAAEF,KAAEG,EAAC;AAAE,IAAAE,GAAE,KAAKC,EAAC,GAAEF,MAAGC,GAAE,KAAK,GAAGD,EAAC;AAAA,EAAC;AAAC;;;ACA3qC,IAAMS,KAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,SAAK,cAAY,MAAK,KAAK,YAAU,CAAC,GAAE,KAAK,QAAMF,IAAE,KAAK,OAAKC,IAAE,KAAK,cAAYC,IAAE,KAAK,UAAQF,GAAE,iBAAiB;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,WAAS,CAAC,KAAK,QAAQ,OAAOA,IAAE,KAAK,IAAI,KAAG,KAAK,UAAU,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK,UAAU,SAAO;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAEC,IAAE;AAAA,EAAC;AAAC;;;ACAlO,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYA,IAAED,IAAEE,IAAEC,IAAEC,IAAE;AAAC,UAAMH,IAAED,IAAEE,EAAC,GAAE,KAAK,OAAK,EAAE,QAAO,KAAK,sBAAoBC,IAAE,KAAK,qBAAmBC;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAA,EAAC,gBAAgBC,IAAE;AAAC,UAAMJ,KAAE,KAAK,qBAAoBD,KAAE,KAAK;AAAmB,SAAK,oBAAkB,IAAEA,GAAE,OAAM,KAAK,oBAAkB;AAAE,UAAME,KAAE,KAAK,OAAMC,KAAE,KAAK;AAAK,IAAAE,MAAGA,GAAE,UAAU,KAAK,WAAW;AAAE,eAAUD,MAAK,KAAK,WAAU;AAAC,YAAME,KAAEF,GAAE,YAAYC,EAAC;AAAE,UAAG,CAACC,GAAE;AAAS,YAAMC,KAAEL,GAAE,eAAe,iBAAiBE,IAAED,IAAED,EAAC;AAAE,iBAAUG,MAAKC,GAAE,KAAGD,GAAE,YAAUH,MAAKG,IAAE;AAAC,cAAMA,KAAEJ,GAAE;AAAM,QAAAA,GAAE,IAAIC,GAAE,GAAEA,GAAE,GAAE,GAAE,GAAEK,EAAC,GAAEN,GAAE,IAAIC,GAAE,GAAEA,GAAE,GAAE,GAAE,GAAEK,EAAC,GAAEN,GAAE,IAAIC,GAAE,GAAEA,GAAE,GAAE,GAAE,GAAEK,EAAC,GAAEN,GAAE,IAAIC,GAAE,GAAEA,GAAE,GAAE,GAAE,GAAEK,EAAC,GAAEP,GAAE,IAAIK,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAEL,GAAE,IAAIK,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAE,KAAK,qBAAmB;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAIA,KAAE;AAAE,IAAAA,MAAG,KAAK,UAAU,QAAOA,MAAG,KAAK,oBAAoB,MAAM,QAAOA,MAAG,KAAK,mBAAmB,MAAM;AAAO,UAAMJ,KAAE,IAAI,YAAYI,EAAC,GAAEL,KAAE,IAAI,WAAWC,GAAE,MAAM;AAAE,QAAIC,KAAE;AAAE,IAAAD,GAAEC,IAAG,IAAE,KAAK,MAAKD,GAAEC,IAAG,IAAE,KAAK,UAAU;AAAO,aAAQC,KAAE,GAAEA,KAAE,KAAK,UAAU,QAAOA,KAAI,CAAAF,GAAEC,IAAG,IAAE,KAAK,UAAUC,EAAC;AAAE,IAAAF,GAAEC,IAAG,IAAE,KAAK,mBAAkBD,GAAEC,IAAG,IAAE,KAAK,mBAAkBD,GAAEC,IAAG,IAAE,KAAK,oBAAoB,MAAM;AAAO,aAAQC,KAAE,GAAEA,KAAE,KAAK,oBAAoB,MAAM,QAAOA,KAAI,CAAAH,GAAEE,IAAG,IAAE,KAAK,oBAAoB,MAAMC,EAAC;AAAE,IAAAF,GAAEC,IAAG,IAAE,KAAK,mBAAmB,MAAM;AAAO,aAAQC,KAAE,GAAEA,KAAE,KAAK,mBAAmB,MAAM,QAAOA,KAAI,CAAAF,GAAEC,IAAG,IAAE,KAAK,mBAAmB,MAAMC,EAAC;AAAE,WAAOF,GAAE;AAAA,EAAM;AAAC;;;ACA/pC,IAAMO,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEL,IAAEM,IAAE;AAAC,UAAML,IAAEC,IAAEC,EAAC,GAAE,KAAK,OAAK,EAAE,MAAK,KAAK,cAAY,oBAAI,OAAI,KAAK,oBAAkBC,IAAE,KAAK,mBAAiBC,IAAE,KAAK,uBAAqBL,IAAE,KAAK,sBAAoBM;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAO,KAAK;AAAA,EAAkB;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAO,KAAK;AAAA,EAAkB;AAAA,EAAC,aAAaL,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAMG,KAAE,KAAK,MAAKF,KAAED,GAAE,iBAAiB,cAAc;AAAE,QAAGC,GAAE,KAAGA,GAAE,aAAa,YAAUL,MAAK,KAAK,UAAU,CAAAE,GAAEG,GAAE,SAASE,IAAEP,EAAC,GAAE,IAAE;AAAA,QAAO,CAAAE,GAAEG,GAAE,SAASE,EAAC,GAAE,IAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBN,IAAE;AAAC,SAAK,kBAAgB,IAAE,KAAK,iBAAiB,OAAM,KAAK,kBAAgB,GAAE,KAAK,qBAAmB,IAAE,KAAK,oBAAoB,OAAM,KAAK,qBAAmB;AAAE,UAAMC,KAAE,KAAK,OAAMC,KAAE,KAAK,MAAK,EAAC,cAAaC,IAAE,iBAAgBG,IAAE,mBAAkBF,IAAE,sBAAqBL,GAAC,IAAEE;AAAE,IAAAD,MAAGA,GAAE,UAAU,KAAK,WAAW;AAAE,UAAMK,KAAEJ,GAAE,iBAAiB,cAAc,GAAEM,KAAEF,MAAA,gBAAAA,GAAG;AAAa,QAAIG,KAAE,CAACH,MAAGJ,GAAE,cAAc,kBAAiBC,EAAC;AAAE,QAAGD,GAAE,sBAAqB;AAAC,UAAGO,MAAG,CAACP,GAAE,sBAAqB;AAAC,cAAMD,KAAEC,GAAE,cAAc,gBAAeC,EAAC,GAAEC,MAAEF,GAAE,cAAc,gBAAeC,KAAE,CAAC;AAAE,QAAAF,KAAE,KAAGG,MAAE,MAAIK,KAAE;AAAA,MAAG;AAAC,UAAGA,MAAG,CAACP,GAAE,oBAAmB;AAAC,cAAMD,KAAEC,GAAE,cAAc,cAAaC,EAAC,GAAEC,MAAEF,GAAE,cAAc,cAAaC,KAAE,CAAC;AAAE,QAAAF,GAAE,CAAC,IAAE,KAAGG,IAAE,CAAC,IAAE,MAAIK,KAAE;AAAA,MAAG;AAAA,IAAC;AAAC,UAAMC,KAAE,KAAK,WAAU,IAAET,MAAA,gBAAAA,GAAG;AAAqB,QAAGO,IAAE;AAAC,YAAMH,KAAE,CAAC;AAAE,iBAAUG,MAAKE,IAAE;AAAC,cAAMA,KAAEJ,GAAE,SAASH,IAAEK,EAAC,GAAEG,KAAE,KAAK,YAAYD,EAAC;AAAE,YAAG,CAACC,MAAG,CAACA,GAAE,KAAK;AAAS,cAAMC,KAAER,GAAE,iBAAiBI,IAAEL,IAAED,IAAES,EAAC,GAAEE,KAAEJ,MAAGT,KAAEO,GAAE,iBAAiBC,IAAEL,IAAED,EAAC,IAAE,CAAC,GAAEY,KAAEN,GAAE,YAAYP,EAAC;AAAE,QAAAI,GAAE,KAAK,EAAC,kBAAiBO,IAAE,qBAAoBC,IAAE,MAAKF,GAAE,MAAK,UAASG,GAAC,CAAC,GAAET,GAAE,KAAM,CAACJ,IAAEC,OAAID,GAAE,OAAKC,GAAE,IAAK;AAAE,mBAAS,EAAC,kBAAiBD,IAAE,qBAAoBE,IAAE,MAAKC,KAAE,UAASG,GAAC,KAAIF,GAAE,MAAK,gBAAgBE,IAAEE,IAAEP,GAAE,sBAAqBD,IAAEE,IAAE,GAAEC,GAAC;AAAA,MAAC;AAAA,IAAC,MAAM,YAAUO,MAAKD,IAAE;AAAC,YAAMJ,KAAED,KAAED,GAAE,iBAAiBO,IAAER,IAAED,EAAC,IAAE,MAAKM,KAAEC,MAAGT,KAAEO,GAAE,iBAAiBI,IAAER,IAAED,EAAC,IAAE,MAAKQ,KAAEC,GAAE,YAAYV,EAAC;AAAE,WAAK,gBAAgBS,IAAED,IAAEP,GAAE,sBAAqBI,IAAEE,IAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAIP,KAAE;AAAG,IAAAA,MAAG,KAAK,UAAU,QAAOA,MAAG,KAAK,kBAAkB,MAAM,QAAOA,MAAG,KAAK,iBAAiB,MAAM,QAAOA,MAAG,KAAK,qBAAqB,MAAM,QAAOA,MAAG,KAAK,oBAAoB,MAAM,QAAOA,MAAG,IAAE,KAAK,YAAY,OAAK;AAAE,UAAMC,KAAE,IAAI,YAAYD,EAAC,GAAEE,KAAE,IAAI,WAAWD,GAAE,MAAM;AAAE,QAAIE,KAAE;AAAE,IAAAF,GAAEE,IAAG,IAAE,KAAK,MAAKF,GAAEE,IAAG,IAAE,KAAK,UAAU;AAAO,aAAQJ,KAAE,GAAEA,KAAE,KAAK,UAAU,QAAOA,KAAI,CAAAE,GAAEE,IAAG,IAAE,KAAK,UAAUJ,EAAC;AAAE,IAAAE,GAAEE,IAAG,IAAE,KAAK,iBAAgBF,GAAEE,IAAG,IAAE,KAAK,iBAAgBF,GAAEE,IAAG,IAAE,KAAK,oBAAmBF,GAAEE,IAAG,IAAE,KAAK;AAAmB,UAAMG,KAAE,KAAK,aAAYF,KAAEE,GAAE;AAAK,QAAGL,GAAEE,IAAG,IAAEC,IAAEA,KAAE,EAAE,YAAS,CAACL,IAAE,CAACM,IAAEE,EAAC,CAAC,KAAID,GAAE,CAAAL,GAAEE,IAAG,IAAEJ,IAAEE,GAAEE,IAAG,IAAEE,IAAEJ,GAAEE,IAAG,IAAEI;AAAE,IAAAN,GAAEE,IAAG,IAAE,KAAK,kBAAkB,MAAM;AAAO,aAAQJ,KAAE,GAAEA,KAAE,KAAK,kBAAkB,MAAM,QAAOA,KAAI,CAAAG,GAAEC,IAAG,IAAE,KAAK,kBAAkB,MAAMJ,EAAC;AAAE,IAAAE,GAAEE,IAAG,IAAE,KAAK,iBAAiB,MAAM;AAAO,aAAQJ,KAAE,GAAEA,KAAE,KAAK,iBAAiB,MAAM,QAAOA,KAAI,CAAAE,GAAEE,IAAG,IAAE,KAAK,iBAAiB,MAAMJ,EAAC;AAAE,IAAAE,GAAEE,IAAG,IAAE,KAAK,qBAAqB,MAAM;AAAO,aAAQJ,KAAE,GAAEA,KAAE,KAAK,qBAAqB,MAAM,QAAOA,KAAI,CAAAG,GAAEC,IAAG,IAAE,KAAK,qBAAqB,MAAMJ,EAAC;AAAE,IAAAE,GAAEE,IAAG,IAAE,KAAK,oBAAoB,MAAM;AAAO,aAAQJ,KAAE,GAAEA,KAAE,KAAK,oBAAoB,MAAM,QAAOA,KAAI,CAAAE,GAAEE,IAAG,IAAE,KAAK,oBAAoB,MAAMJ,EAAC;AAAE,WAAOE,GAAE;AAAA,EAAM;AAAA,EAAC,gBAAgBD,IAAEC,IAAEC,IAAEC,IAAEG,IAAEF,IAAEC,IAAE;AAAC,QAAG,CAACL,GAAE;AAAO,UAAMO,KAAEP,GAAE,QAAOQ,KAAE,CAACF,MAAG,MAAIA,GAAE;AAAO,QAAGL,OAAI,CAACC,MAAGM,IAAG,UAAQT,KAAE,GAAEA,KAAEQ,IAAER,KAAI,MAAK,gBAAgBC,GAAED,EAAC,GAAEO,EAAC;AAAE,UAAMG,KAAE;AAAG,QAAI;AAAE,aAAQC,KAAE,GAAEA,KAAEH,IAAEG,MAAI;AAAC,YAAMT,KAAE,GAAE,MAAMD,GAAEU,EAAC,CAAC;AAAE,MAAAT,KAAEQ,MAAG,WAAS,KAAG,KAAK,aAAaT,IAAE,GAAEG,IAAEC,IAAEC,EAAC,GAAE,IAAE,CAACK,EAAC,KAAGT,KAAE,CAACQ,MAAG,WAAS,KAAG,EAAE,KAAKC,EAAC;AAAA,IAAC;AAAC,eAAS,KAAG,KAAK,aAAaV,IAAE,GAAEG,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBL,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,sBAAqBI,KAAE,KAAK,qBAAoBF,KAAEE,GAAE;AAAM,QAAIP,IAAEM,IAAEE;AAAE,UAAMC,KAAE,IAAI,EAAE,GAAE,CAAC,GAAEC,KAAE,IAAI,EAAE,GAAE,CAAC,GAAE,IAAE,IAAI,EAAE,GAAE,CAAC;AAAE,QAAIC,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAE;AAAG,UAAMC,KAAE;AAAE,QAAIC,KAAEjB,GAAE;AAAO,QAAGiB,KAAE,EAAE;AAAO,UAAMC,KAAElB,GAAEgB,EAAC;AAAE,QAAIG,KAAEnB,GAAEiB,KAAE,CAAC;AAAE,WAAKA,MAAGE,GAAE,QAAQD,EAAC,IAAG,GAAED,IAAEE,KAAEnB,GAAEiB,KAAE,CAAC;AAAE,QAAG,EAAEA,KAAED,KAAE,IAAG;AAAC,eAAQb,KAAEa,IAAEb,KAAEc,IAAE,EAAEd,IAAE;AAAC,QAAAA,OAAIa,MAAGjB,KAAEC,GAAEiB,KAAE,CAAC,GAAEZ,KAAEL,GAAEgB,EAAC,GAAET,KAAEP,GAAEgB,KAAE,CAAC,GAAER,GAAE,UAAUH,IAAEN,EAAC,GAAES,GAAE,UAAU,GAAEA,GAAE,mBAAmB,MAAIT,KAAEM,IAAEA,KAAEE,IAAEA,KAAEJ,OAAIc,KAAE,IAAEjB,GAAEG,KAAE,CAAC,IAAEH,GAAEgB,EAAC,GAAER,GAAE,OAAOC,EAAC;AAAG,cAAML,KAAE,KAAK,YAAYL,IAAEM,EAAC;AAAE,eAAKQ,OAAIE,KAAEX,KAAGK,GAAE,UAAUF,IAAEF,EAAC,GAAEI,GAAE,UAAU,GAAEA,GAAE,mBAAmB;AAAE,cAAMS,KAAEV,GAAE,IAAEC,GAAE,IAAED,GAAE,IAAEC,GAAE;AAAE,UAAE,UAAUD,IAAEC,EAAC,GAAE,EAAE,UAAU;AAAE,cAAMU,KAAE,CAAC,EAAE,IAAE,CAACX,GAAE,IAAE,CAAC,EAAE,IAAE,CAACA,GAAE;AAAE,YAAI,IAAE,KAAK,IAAI,MAAIW,KAAE,IAAEA,KAAE,CAAC;AAAE,YAAE,MAAI,IAAE,IAAGD,MAAG,KAAGN,KAAEV,GAAE,IAAIG,GAAE,GAAEA,GAAE,GAAEG,GAAE,GAAEA,GAAE,GAAE,GAAE,GAAEP,EAAC,GAAE,OAAKY,OAAIA,KAAED,KAAGF,MAAG,KAAGC,MAAG,KAAGC,MAAG,KAAG,CAACR,MAAGE,GAAE,IAAII,IAAEC,IAAEC,EAAC,GAAED,KAAET,GAAE,IAAIG,GAAE,GAAEA,GAAE,GAAE,IAAE,CAAC,EAAE,GAAE,IAAE,CAAC,EAAE,GAAE,GAAE,IAAGJ,EAAC,GAAE,OAAKa,OAAIA,KAAEH,KAAGD,MAAG,KAAGC,MAAG,KAAGC,MAAG,KAAG,CAACR,MAAGE,GAAE,IAAII,IAAEC,IAAEC,EAAC,GAAEF,KAAEC,IAAEA,KAAEC,IAAEA,KAAEV,GAAE,IAAIG,GAAE,GAAEA,GAAE,GAAE,EAAE,GAAE,EAAE,GAAE,GAAE,GAAEJ,EAAC,GAAES,MAAG,KAAGC,MAAG,KAAGC,MAAG,KAAG,CAACR,MAAGE,GAAE,IAAII,IAAEC,IAAEC,EAAC,GAAED,KAAET,GAAE,IAAIG,GAAE,GAAEA,GAAE,GAAEI,GAAE,GAAEA,GAAE,GAAE,GAAE,GAAER,EAAC,GAAES,MAAG,KAAGC,MAAG,KAAGC,MAAG,KAAG,CAACR,MAAGE,GAAE,IAAII,IAAEC,IAAEC,EAAC,MAAIA,KAAEV,GAAE,IAAIG,GAAE,GAAEA,GAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAEJ,EAAC,GAAE,OAAKY,OAAIA,KAAED,KAAGF,MAAG,KAAGC,MAAG,KAAGC,MAAG,KAAG,CAACR,MAAGE,GAAE,IAAII,IAAEC,IAAEC,EAAC,GAAED,KAAET,GAAE,IAAIG,GAAE,GAAEA,GAAE,GAAE,CAACG,GAAE,GAAE,CAACA,GAAE,GAAE,GAAE,IAAGP,EAAC,GAAE,OAAKa,OAAIA,KAAEH,KAAGD,MAAG,KAAGC,MAAG,KAAGC,MAAG,KAAG,CAACR,MAAGE,GAAE,IAAII,IAAEC,IAAEC,EAAC,GAAEF,KAAEC,IAAEA,KAAEC,IAAEA,KAAEV,GAAE,IAAIG,GAAE,GAAEA,GAAE,GAAE,CAAC,EAAE,GAAE,CAAC,EAAE,GAAE,GAAE,IAAGJ,EAAC,GAAES,MAAG,KAAGC,MAAG,KAAGC,MAAG,KAAG,CAACR,MAAGE,GAAE,IAAII,IAAEC,IAAEC,EAAC,GAAEF,KAAER,GAAE,IAAIG,GAAE,GAAEA,GAAE,GAAE,CAACI,GAAE,GAAE,CAACA,GAAE,GAAE,GAAE,IAAGR,EAAC,GAAES,MAAG,KAAGC,MAAG,KAAGC,MAAG,KAAG,CAACR,MAAGE,GAAE,IAAII,IAAEC,IAAEC,EAAC;AAAA,MAAE;AAAC,MAAAF,MAAG,KAAGC,MAAG,KAAGE,MAAG,KAAG,CAACE,MAAGT,GAAE,IAAII,IAAEC,IAAEE,EAAC,GAAEH,MAAG,KAAGG,MAAG,KAAGC,MAAG,KAAG,CAACC,MAAGT,GAAE,IAAII,IAAEI,IAAED,EAAC,GAAE,KAAK,sBAAoB,KAAGP,GAAE,QAAMF;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAEG,IAAEF,IAAEL,IAAEM,IAAE;AAAC,IAAAN,KAAE;AAAG,QAAIQ;AAAE,IAAAD,GAAE,SAAO,MAAIC,KAAE,CAAC;AAAG,QAAIC,KAAE;AAAE,eAAUR,MAAKM,GAAE,OAAIE,MAAGD,GAAE,KAAKC,EAAC,GAAEA,MAAGL,GAAEH,EAAC,EAAE;AAAO,UAAMS,KAAE,IAAED,IAAE,IAAE,EAAE,QAAQ;AAAE,eAAUR,MAAKM,IAAE;AAAC,YAAML,KAAEE,GAAEH,EAAC,GAAEE,KAAED,GAAE;AAAO,eAAQD,KAAE,GAAEA,KAAEE,IAAE,EAAEF,GAAE,GAAE,KAAKC,GAAED,EAAC,EAAE,GAAEC,GAAED,EAAC,EAAE,CAAC;AAAA,IAAC;AAAC,UAAMU,KAAEP,GAAE,GAAEI,IAAE,CAAC;AAAE,QAAGR,MAAGI,GAAE,UAAU,GAAEI,IAAE,GAAEG,EAAC,IAAE,GAAE;AAAC,YAAMV,KAAEM,GAAE,IAAK,CAAAN,OAAGG,GAAEH,EAAC,EAAE,MAAO,GAAE,EAAC,QAAOC,IAAE,aAAYF,GAAC,IAAEI,GAAE,GAAEH,EAAC;AAAE,UAAGD,KAAE,GAAE;AAAC,cAAMC,KAAE,KAAK,kBAAkB;AAAM,iBAAQE,KAAE,GAAEA,KAAEH,IAAEG,KAAI,MAAK,kBAAkB,IAAID,GAAE,IAAEC,EAAC,GAAED,GAAE,IAAEC,KAAE,CAAC,GAAEE,EAAC;AAAE,iBAAQH,KAAE,GAAEA,KAAEF,IAAEE,MAAG,GAAE;AAAC,gBAAMC,KAAEF,KAAEC;AAAE,eAAK,iBAAiB,IAAIC,IAAEA,KAAE,GAAEA,KAAE,CAAC;AAAA,QAAC;AAAC,YAAG,WAASG,IAAE;AAAC,gBAAML,MAAE,KAAK,aAAYC,KAAED,IAAE,IAAIK,EAAC;AAAE,UAAAJ,KAAEA,GAAE,CAAC,KAAGF,KAAEC,IAAE,IAAIK,IAAE,CAAC,KAAK,kBAAgB,KAAK,iBAAgBN,EAAC,CAAC;AAAA,QAAC;AAAC,aAAK,mBAAiBA;AAAA,MAAC;AAAA,IAAC,OAAK;AAAC,YAAMC,KAAEU,GAAE;AAAO,UAAGV,KAAE,GAAE;AAAC,cAAMC,KAAE,KAAK,kBAAkB;AAAM,YAAIC,KAAE;AAAE,eAAKA,KAAEO,KAAG,MAAK,kBAAkB,IAAI,EAAEP,IAAG,GAAE,EAAEA,IAAG,GAAEE,EAAC;AAAE,YAAID,MAAE;AAAE,eAAKA,MAAEH,KAAG,MAAK,iBAAiB,IAAIC,KAAES,GAAEP,KAAG,GAAEF,KAAES,GAAEP,KAAG,GAAEF,KAAES,GAAEP,KAAG,CAAC;AAAE,YAAG,WAASE,IAAE;AAAC,gBAAMJ,KAAE,KAAK,aAAYC,KAAED,GAAE,IAAII,EAAC;AAAE,UAAAH,KAAEA,GAAE,CAAC,KAAGF,KAAEC,GAAE,IAAII,IAAE,CAAC,KAAK,kBAAgB,KAAK,iBAAgBL,EAAC,CAAC;AAAA,QAAC;AAAC,aAAK,mBAAiBA;AAAA,MAAC;AAAA,IAAC;AAAC,MAAE,QAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,WAAOD,GAAE,MAAIC,GAAE,IAAED,GAAE,KAAG,OAAKA,GAAE,KAAG,OAAKA,GAAE,MAAIC,GAAE,MAAID,GAAE,KAAG,OAAKA,GAAE,KAAG;AAAA,EAAK;AAAA,EAAC,OAAO,MAAMA,IAAE;AAAC,QAAIC,KAAE;AAAE,UAAMC,KAAEF,GAAE,SAAO;AAAE,aAAQG,KAAE,GAAEA,KAAED,IAAEC,KAAI,CAAAF,OAAID,GAAEG,EAAC,EAAE,IAAEH,GAAEG,KAAE,CAAC,EAAE,MAAIH,GAAEG,EAAC,EAAE,IAAEH,GAAEG,KAAE,CAAC,EAAE;AAAG,WAAOF,OAAID,GAAEE,EAAC,EAAE,IAAEF,GAAE,CAAC,EAAE,MAAIA,GAAEE,EAAC,EAAE,IAAEF,GAAE,CAAC,EAAE,IAAG,MAAGC;AAAA,EAAC;AAAC;;;ACA3qM,IAAMmB,KAAE;AAAM,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYA,IAAEF,IAAEC,IAAEE,IAAEC,IAAE;AAAC,UAAMF,IAAEF,IAAEC,EAAC,GAAE,KAAK,OAAK,EAAE,MAAK,KAAK,uBAAqB,EAAC,iBAAgB,GAAE,WAAU,GAAE,QAAO,EAAC,GAAE,KAAK,cAAY,oBAAI,OAAI,KAAK,yBAAuB,EAAC,mBAAkB,MAAK,kBAAiB,MAAK,WAAU,KAAI,GAAE,KAAK,uBAAuB,oBAAkBE,IAAE,KAAK,uBAAuB,mBAAiBC,IAAE,KAAK,mBAAiB,IAAIC,GAAEC,GAAE,KAAK,sBAAsB,GAAEC,GAAE,KAAK,sBAAsB,GAAEL,GAAE,sBAAsB;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,aAAaM,IAAEN,IAAEO,IAAE;AAAC,UAAMT,KAAE,KAAK,OAAMC,KAAE,KAAK,MAAKK,KAAEN,GAAE,iBAAiB,cAAc,GAAEO,KAAEP,GAAE,iBAAiB,gBAAgB,GAAEG,KAAEH,GAAE,kBAAkB,UAAU;AAAE,QAAG,CAACM,MAAG,CAACC,GAAE;AAAO,UAAMH,MAAED,MAAA,gBAAAA,GAAG,SAASF,QAAI,GAAES,KAAEP,MAAA,gBAAAA,GAAG,cAAaQ,KAAEL,MAAA,gBAAAA,GAAG,cAAaM,KAAEL,MAAA,gBAAAA,GAAG;AAAa,QAAGI,MAAGC,GAAE,YAAUC,MAAK,KAAK,UAAU,CAAAX,GAAES,KAAEL,GAAE,SAASL,IAAEY,EAAC,IAAE,KAAK,iBAAiBA,IAAEZ,IAAED,IAAEO,IAAEG,IAAEP,IAAEC,EAAC,CAAC;AAAA,aAAUE,GAAE,CAAAJ,GAAEI,GAAE,SAASL,EAAC,CAAC;AAAA,aAAUM,IAAE;AAAC,YAAMC,KAAED,GAAE,SAASN,EAAC;AAAE,MAAAC,GAAEF,GAAE,WAAWQ,IAAEJ,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBI,IAAE;AAAC,SAAK,kBAAgB,IAAE,KAAK,uBAAuB,iBAAiB,OAAM,KAAK,kBAAgB;AAAE,UAAMN,KAAE,KAAK,OAAMO,KAAE,KAAK,MAAKT,KAAE,KAAK,WAAUC,KAAE,KAAK,sBAAqB,EAAC,mBAAkBK,IAAE,cAAaC,GAAC,IAAEL;AAAE,IAAAM,MAAGA,GAAE,UAAU,KAAK,WAAW;AAAE,UAAML,KAAED,GAAE,iBAAiB,cAAc,GAAEE,KAAEF,GAAE,iBAAiB,gBAAgB,GAAEQ,KAAEP,MAAA,gBAAAA,GAAG,cAAaQ,KAAEP,MAAA,gBAAAA,GAAG;AAAa,QAAIQ;AAAE,IAAAA,KAAEV,GAAE,kBAAkB,UAAU;AAAE,UAAMW,MAAED,MAAA,gBAAAA,GAAG,gBAAaA,KAAE,MAAKE,KAAED,KAAE,OAAKX,GAAE,eAAe,YAAWO,EAAC,GAAEM,KAAED,MAAG,GAAE,IAAE,CAAC,CAACD;AAAE,IAAAD,KAAEV,GAAE,kBAAkB,WAAW;AAAE,UAAMG,MAAEO,MAAA,gBAAAA,GAAG,gBAAaA,KAAE,MAAKI,KAAEX,KAAE,OAAKH,GAAE,eAAe,aAAYO,EAAC;AAAE,IAAAG,KAAEV,GAAE,kBAAkB,kBAAkB;AAAE,UAAMe,MAAEL,MAAA,gBAAAA,GAAG,gBAAaA,KAAE,MAAK,IAAEK,KAAE,OAAKf,GAAE,eAAe,oBAAmBO,EAAC;AAAE,IAAAG,KAAEV,GAAE,kBAAkB,kBAAkB;AAAE,UAAMgB,MAAEN,MAAA,gBAAAA,GAAG,gBAAaA,KAAE,MAAK,IAAEM,KAAE,OAAKhB,GAAE,eAAe,oBAAmBO,EAAC;AAAE,IAAAG,KAAEV,GAAE,iBAAiB,YAAY;AAAE,UAAM,KAAEU,MAAA,gBAAAA,GAAG,gBAAaA,KAAE,MAAKO,KAAE,IAAE,OAAKjB,GAAE,cAAc,cAAaO,EAAC;AAAE,IAAAG,KAAEV,GAAE,iBAAiB,aAAa;AAAE,UAAMkB,MAAER,MAAA,gBAAAA,GAAG,gBAAaA,KAAE,MAAKS,KAAED,KAAE,OAAKlB,GAAE,cAAc,eAAcO,EAAC;AAAE,QAAGC,MAAGC,IAAE;AAAC,YAAML,MAAE,CAAC;AAAE,iBAAUL,MAAKD,IAAE;AAAC,cAAMA,KAAEU,KAAEP,GAAE,SAASM,IAAER,EAAC,IAAE,KAAK,iBAAiBA,IAAEQ,IAAEP,IAAEE,IAAE,GAAES,IAAEE,EAAC,GAAEJ,KAAE,KAAK,YAAYX,EAAC;AAAE,YAAG,CAACW,MAAG,CAACA,GAAE,KAAK;AAAS,cAAMC,KAAEL,GAAE,iBAAiBN,IAAEQ,IAAEP,IAAES,EAAC,GAAE,IAAEV,GAAE,YAAYO,EAAC;AAAE,QAAAF,IAAE,KAAK,EAAC,cAAaM,IAAE,MAAKD,GAAE,MAAK,KAAIE,KAAEA,GAAE,SAASJ,IAAER,EAAC,IAAEa,IAAE,MAAKT,KAAEA,GAAE,SAASI,IAAER,EAAC,IAAEe,IAAE,YAAWC,KAAEA,GAAE,SAASR,IAAER,EAAC,IAAE,GAAE,YAAWiB,KAAEA,GAAE,SAAST,IAAER,EAAC,IAAE,GAAE,WAAU,OAAI,IAAE,EAAE,SAASQ,IAAER,EAAC,IAAEkB,KAAG,QAAOC,KAAEA,GAAE,SAASX,IAAER,EAAC,IAAEoB,IAAE,UAAS,EAAC,CAAC;AAAA,MAAC;AAAC,MAAAf,IAAE,KAAM,CAACE,IAAEN,OAAIM,GAAE,OAAKN,GAAE,IAAK,GAAED,GAAE,WAAS;AAAG,iBAAS,EAAC,cAAaO,IAAE,MAAKN,IAAE,KAAIO,IAAE,MAAKT,IAAE,YAAWO,IAAE,YAAWJ,IAAE,WAAUC,IAAE,QAAOM,IAAE,UAASC,GAAC,KAAIL,IAAE,CAAAL,GAAE,UAAQQ,IAAER,GAAE,WAASD,IAAEC,GAAE,aAAWM,IAAEN,GAAE,aAAWE,IAAEF,GAAE,YAAUG,IAAEH,GAAE,SAAOS,IAAE,KAAK,gBAAgBC,IAAEH,IAAEN,EAAC;AAAA,IAAC,OAAK;AAAC,UAAGC,IAAE;AAAC,cAAMK,KAAEL,GAAE,SAASM,EAAC,GAAEP,KAAE,KAAK,YAAYM,EAAC;AAAE,YAAG,CAACN,MAAG,CAACA,GAAE,KAAK;AAAA,MAAM;AAAC,MAAAD,GAAE,WAAS,EAAE,CAACE,MAAG,CAACC,KAAGH,GAAE,UAAQa,IAAEb,GAAE,WAASe,IAAEf,GAAE,aAAW,GAAEA,GAAE,aAAW,GAAEA,GAAE,YAAU,MAAGkB,IAAElB,GAAE,SAAOoB;AAAE,iBAAUlB,MAAKH,IAAE;AAAC,cAAMA,KAAEM,KAAEC,GAAE,iBAAiBJ,IAAEM,IAAEP,EAAC,IAAE;AAAK,QAAAW,OAAIZ,GAAE,UAAQY,GAAE,SAASJ,IAAEN,EAAC,IAAGE,OAAIJ,GAAE,WAASI,GAAE,SAASI,IAAEN,EAAC,IAAGc,OAAIhB,GAAE,aAAWgB,GAAE,SAASR,IAAEN,EAAC,IAAGe,OAAIjB,GAAE,aAAWiB,GAAE,SAAST,IAAEN,EAAC,IAAG,MAAIF,GAAE,YAAU,MAAG,EAAE,SAASQ,IAAEN,EAAC,IAAGiB,OAAInB,GAAE,SAAOmB,GAAE,SAASX,IAAEN,EAAC;AAAG,cAAMC,KAAED,GAAE,YAAYK,EAAC;AAAE,aAAK,gBAAgBJ,IAAEJ,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAIQ,KAAE;AAAE,IAAAA,MAAG,KAAK,UAAU,QAAOA,MAAG,KAAK,uBAAuB,kBAAkB,MAAM,QAAOA,MAAG,KAAK,uBAAuB,iBAAiB,MAAM,QAAOA,MAAG,IAAE,KAAK,YAAY,OAAK;AAAE,UAAMN,KAAE,IAAI,YAAYM,EAAC,GAAEC,KAAE,IAAI,WAAWP,GAAE,MAAM;AAAE,QAAIF,KAAE;AAAE,IAAAE,GAAEF,IAAG,IAAE,KAAK,MAAKE,GAAEF,IAAG,IAAE,KAAK,UAAU;AAAO,aAAQO,KAAE,GAAEA,KAAE,KAAK,UAAU,QAAOA,KAAI,CAAAL,GAAEF,IAAG,IAAE,KAAK,UAAUO,EAAC;AAAE,IAAAL,GAAEF,IAAG,IAAE,KAAK,iBAAgBE,GAAEF,IAAG,IAAE,KAAK;AAAgB,UAAMC,KAAE,KAAK,aAAYK,KAAEL,GAAE;AAAK,QAAGC,GAAEF,IAAG,IAAEM,IAAEA,KAAE,EAAE,YAAS,CAACC,IAAE,CAACJ,IAAEC,EAAC,CAAC,KAAIH,GAAE,CAAAC,GAAEF,IAAG,IAAEO,IAAEL,GAAEF,IAAG,IAAEG,IAAED,GAAEF,IAAG,IAAEI;AAAE,IAAAF,GAAEF,IAAG,IAAE,KAAK,uBAAuB,kBAAkB,MAAM;AAAO,aAAQO,KAAE,GAAEA,KAAE,KAAK,uBAAuB,kBAAkB,MAAM,QAAOA,KAAI,CAAAE,GAAET,IAAG,IAAE,KAAK,uBAAuB,kBAAkB,MAAMO,EAAC;AAAE,IAAAL,GAAEF,IAAG,IAAE,KAAK,uBAAuB,iBAAiB,MAAM;AAAO,aAAQO,KAAE,GAAEA,KAAE,KAAK,uBAAuB,iBAAiB,MAAM,QAAOA,KAAI,CAAAL,GAAEF,IAAG,IAAE,KAAK,uBAAuB,iBAAiB,MAAMO,EAAC;AAAE,WAAOL,GAAE;AAAA,EAAM;AAAA,EAAC,gBAAgBM,IAAEN,IAAEO,IAAE;AAAC,QAAG,CAACD,GAAE;AAAO,UAAMR,KAAEQ,GAAE;AAAO,aAAQP,KAAE,GAAEA,KAAED,IAAEC,KAAI,MAAK,iBAAiBO,GAAEP,EAAC,GAAEC,IAAEO,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAEN,IAAEO,IAAE;AAAC,QAAGD,GAAE,SAAO,EAAE;AAAO,UAAMP,KAAE;AAAK,QAAIK,IAAEC,IAAEJ,KAAEK,GAAE,CAAC,GAAEJ,KAAE;AAAE,WAAKA,KAAEI,GAAE,SAAQ,CAAAF,KAAEE,GAAEJ,EAAC,EAAE,IAAED,GAAE,GAAEI,KAAEC,GAAEJ,EAAC,EAAE,IAAED,GAAE,GAAEG,KAAEA,KAAEC,KAAEA,KAAEN,KAAEA,KAAEO,GAAE,OAAOJ,IAAE,CAAC,KAAGD,KAAEK,GAAEJ,EAAC,GAAE,EAAEA;AAAG,QAAGI,GAAE,SAAO,EAAE;AAAO,UAAME,KAAE,KAAK,uBAAuB,kBAAiBC,KAAE,IAAED,GAAE;AAAM,SAAK,qBAAqB,kBAAgB,GAAE,KAAK,qBAAqB,eAAaV,IAAE,KAAK,uBAAuB,YAAUE,IAAE,KAAK,iBAAiB,WAAWM,IAAE,KAAK,oBAAoB;AAAE,UAAMI,KAAE,IAAEF,GAAE,QAAMC;AAAE,QAAG,WAASF,IAAE;AAAC,YAAMD,KAAE,KAAK,aAAYN,KAAEM,GAAE,IAAIC,EAAC;AAAE,MAAAP,KAAEA,GAAE,CAAC,KAAGU,KAAEJ,GAAE,IAAIC,IAAE,CAACE,KAAE,KAAK,iBAAgBC,EAAC,CAAC;AAAA,IAAC;AAAC,SAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,iBAAiBJ,IAAEN,IAAEO,IAAET,IAAEC,IAAEK,IAAEC,IAAE;AAAC,UAAMJ,KAAEF,KAAEK,GAAE,SAASJ,IAAEM,EAAC,IAAED,IAAEH,KAAEJ,GAAE,SAASE,IAAEM,EAAC;AAAE,WAAOC,GAAE,WAAWL,IAAED,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMG,KAAE,CAAAE,OAAG,CAACN,IAAEO,IAAET,IAAEC,IAAEK,IAAEC,IAAEJ,IAAEC,IAAEM,IAAEC,IAAEC,QAAKJ,GAAE,kBAAkB,IAAIN,IAAEO,IAAEN,IAAEC,IAAEJ,IAAEC,IAAEK,IAAEC,IAAEG,IAAEC,IAAEC,IAAEJ,GAAE,SAAS,GAAEA,GAAE,kBAAkB,QAAM;AAA1H,IAA6HD,KAAE,CAAAC,OAAG,CAACN,IAAEO,IAAET,OAAI;AAAC,EAAAQ,GAAE,iBAAiB,IAAIN,IAAEO,IAAET,EAAC;AAAC;;;ACA/4I,IAAMsB,KAAE;AAAG,SAAS,EAAEC,IAAEC,IAAE;AAAC,SAAOD,GAAE,kBAAgBC,GAAE,iBAAeD,GAAE,eAAe,SAAOC,GAAE,eAAe,OAAK,IAAED,GAAE,eAAe,OAAKC,GAAE,eAAe,OAAKD,GAAE,kBAAgB,CAACC,GAAE,iBAAe,IAAE,CAACD,GAAE,kBAAgBC,GAAE,iBAAe,KAAG;AAAC;AAAC,IAAM,IAAN,MAAM,WAAUA,GAAC;AAAA,EAAC,YAAYD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMP,IAAEC,IAAEM,GAAE,eAAe,CAAC,GAAE,KAAK,OAAK,EAAE,QAAO,KAAK,aAAW,oBAAI,OAAI,KAAK,YAAU,oBAAI,OAAI,KAAK,0BAAwB,oBAAI,OAAI,KAAK,aAAW,OAAG,KAAK,oBAAkBL,IAAE,KAAK,mBAAiBC,IAAE,KAAK,oBAAkBC,IAAE,KAAK,mBAAiBC,IAAE,KAAK,mBAAiBC,IAAE,KAAK,qBAAmBC;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK;AAAA,EAAgB;AAAA,EAAC,aAAaP,IAAEC,IAAEE,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAMI,KAAE,KAAK;AAAK,IAAAR,MAAGA,GAAE,UAAU,KAAK,WAAW;AAAE,UAAMK,KAAED,GAAE,kBAAkB,YAAY,GAAEE,KAAEF,GAAE,kBAAkB,YAAY;AAAE,QAAIG,KAAEH,GAAE,kBAAkB,gBAAgB,GAAEK,KAAEL,GAAE,kBAAkB,WAAW;AAAE,UAAMM,KAAE,CAAC;AAAE,QAAIC,IAAE,GAAEC,IAAEC;AAAE,IAAAR,MAAG,CAACA,GAAE,iBAAeM,KAAEN,GAAE,SAASG,EAAC,IAAGF,MAAG,CAACA,GAAE,iBAAe,IAAEA,GAAE,SAASE,EAAC,IAAGD,MAAGA,GAAE,iBAAeK,KAAER,GAAE,eAAe,kBAAiBI,EAAC,GAAED,KAAE,OAAME,MAAGA,GAAE,iBAAeI,KAAET,GAAE,eAAe,aAAYI,EAAC,GAAEC,KAAE;AAAM,eAAUK,MAAK,KAAK,WAAU;AAAC,YAAMC,KAAED,GAAE,YAAYd,EAAC;AAAE,UAAG,CAACe,MAAG,MAAIA,GAAE,OAAO;AAAS,UAAIC,IAAEC;AAAE,MAAAZ,OAAIW,KAAEX,GAAE,eAAaA,GAAE,SAASG,IAAEM,EAAC,IAAE,KAAK,aAAaH,IAAEG,GAAE,MAAM,GAAEE,MAAGf,GAAEe,EAAC;AAAG,UAAI,IAAE;AAAG,UAAGV,OAAIW,KAAEX,GAAE,eAAaA,GAAE,SAASE,IAAEM,EAAC,IAAE,KAAK,aAAa,GAAEA,GAAE,MAAM,GAAEG,KAAG;AAAC,gBAAOA,KAAEA,GAAE,QAAQ,QAAO,IAAI,GAAEV,OAAIK,KAAEL,GAAE,SAASC,IAAEM,EAAC,IAAGF,IAAE;AAAA,UAAC,KAAK,EAAE;AAAU,YAAAK,KAAEA,GAAE,YAAY;AAAE;AAAA,UAAM,KAAK,EAAE;AAAU,YAAAA,KAAEA,GAAE,YAAY;AAAA,QAAC;AAAC,YAAG,GAAE,YAAY,YAAYA,EAAC,GAAE;AAAC,cAAIjB;AAAE,UAAAA,KAAE,UAAQ,GAAE,YAAY,gBAAgBiB,EAAC,IAAE,UAAQ,SAAQA,KAAE,GAAE,YAAY,cAAcA,IAAEjB,IAAE,OAAO,GAAE,IAAE;AAAA,QAAE;AAAC,cAAMA,KAAEiB,GAAE;AAAO,YAAGjB,KAAE,GAAE;AAAC,UAAAS,OAAII,KAAEJ,GAAE,SAASD,IAAEM,EAAC;AAAG,qBAAUb,MAAKY,IAAE;AAAC,gBAAIX,KAAEC,GAAEF,EAAC;AAAE,YAAAC,OAAIA,KAAEC,GAAEF,EAAC,IAAE,oBAAI;AAAK,qBAAQA,MAAE,GAAEA,MAAED,IAAEC,OAAI;AAAC,oBAAMD,KAAEiB,GAAE,WAAWhB,GAAC;AAAE,cAAAC,GAAE,IAAIF,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAG,CAACgB,MAAG,CAACC,GAAE;AAAS,YAAM,IAAEb,GAAE,eAAe,mBAAkBI,IAAEM,EAAC,GAAEI,KAAE,EAAC,SAAQJ,IAAE,QAAOE,IAAE,OAAMC,IAAE,KAAI,GAAE,UAASF,IAAE,OAAME,KAAE,EAAEA,EAAC,IAAE,MAAID,KAAE,EAAEA,EAAC,IAAE,IAAG,UAAS,GAAE,UAASH,GAAC;AAAE,MAAAH,GAAE,KAAKQ,EAAC;AAAA,IAAC;AAAC,SAAK,kBAAgBR;AAAA,EAAC;AAAA,EAAC,gBAAgBV,IAAE;AAAC,IAAAA,MAAGA,GAAE,UAAU,KAAK,WAAW;AAAE,UAAME,KAAE,KAAK,OAAME,KAAE,KAAK,MAAKI,KAAEN,GAAE,eAAe,oBAAmBE,EAAC,GAAEE,KAAEE,OAAIN,GAAE,OAAMK,KAAEL,GAAE,eAAe,kBAAiBE,EAAC,IAAEY,IAAEH,KAAEX,GAAE,kBAAkB,YAAY,GAAEiB,KAAEjB,GAAE,kBAAkB,YAAY,GAAEH,KAAEc,KAAE,IAAI,EAAEX,IAAEE,IAAEE,EAAC,IAAE,MAAK,IAAEa,KAAE,IAAIL,GAAEZ,IAAEE,IAAEE,EAAC,IAAE,MAAKc,KAAE,KAAK;AAAmB,QAAIC;AAAE,IAAAR,OAAIQ,KAAED,GAAE,eAAe,IAAG,KAAK,kBAAgB,IAAE,KAAK,iBAAiB,OAAM,KAAK,kBAAgB,IAAE,KAAK,iBAAiB,OAAM,KAAK,kBAAgB,GAAE,KAAK,kBAAgB,GAAE,KAAK,WAAW,MAAM,GAAE,KAAK,UAAU,MAAM;AAAE,UAAME,KAAE,CAAC;AAAE,QAAIC,KAAE;AAAE,SAAG,EAAE,SAAOA,KAAE,EAAE,OAAKZ;AAAG,UAAM,IAAE,IAAE,EAAE,WAASX,KAAE,GAAE,IAAE,IAAE,EAAE,OAAKgB,KAAE;AAAE,eAAUX,MAAK,KAAK,iBAAgB;AAAC,UAAIL;AAAE,MAAAD,MAAGsB,MAAGhB,GAAE,WAASL,KAAEqB,GAAEhB,GAAE,MAAM,GAAEL,MAAGA,GAAE,QAAM,KAAK,aAAW;AAAK,UAAIE;AAAE,OAAC,CAACF,MAAGD,GAAE,OAAOK,IAAEC,GAAE,OAAO;AAAE,UAAIQ,KAAE;AAAE,YAAMM,KAAEd,GAAE;AAAM,UAAGc,IAAE;AAAC,UAAE,CAAC,GAAE,EAAE,OAAOf,IAAEC,GAAE,OAAO;AAAE,cAAML,KAAEM,MAAG,EAAE,sBAAoB,EAAE,MAAI,EAAE,cAAY,EAAE,eAAa,EAAE,YAAY,SAASU,GAAE,QAAQ;AAAE,YAAIb,KAAE;AAAG,gBAAO,EAAE,QAAO;AAAA,UAAC,KAAK,EAAE;AAAA,UAAS,KAAK,EAAE;AAAA,UAAK,KAAK,EAAE;AAAY,YAAAA,KAAE;AAAE;AAAA,UAAM,KAAK,EAAE;AAAA,UAAU,KAAK,EAAE;AAAA,UAAM,KAAK,EAAE;AAAa,YAAAA,KAAE;AAAA,QAAC;AAAC,YAAIK,KAAE;AAAG,gBAAO,EAAE,QAAO;AAAA,UAAC,KAAK,EAAE;AAAA,UAAS,KAAK,EAAE;AAAA,UAAI,KAAK,EAAE;AAAU,YAAAA,KAAE;AAAE;AAAA,UAAM,KAAK,EAAE;AAAA,UAAY,KAAK,EAAE;AAAA,UAAO,KAAK,EAAE;AAAa,YAAAA,KAAE;AAAA,QAAC;AAAC,YAAID,KAAE;AAAG,gBAAO,EAAE,SAAQ;AAAA,UAAC,KAAK,EAAE;AAAK,YAAAA,KAAEJ;AAAE;AAAA,UAAM,KAAK,EAAE;AAAK,YAAAI,KAAE;AAAE;AAAA,UAAM,KAAK,EAAE;AAAM,YAAAA,KAAE;AAAA,QAAC;AAAC,cAAMG,KAAE,EAAE,gBAAcC,IAAEA,KAAEL,KAAE,IAAE,EAAE,WAASK,IAAEG,KAAE,EAAE,aAAWH,IAAE,IAAEN,GAAE,SAAS,IAAK,CAAAL,OAAGoB,GAAE,cAAcpB,EAAC,CAAE;AAAE,YAAGE,KAAE,IAAIM,GAAE,GAAEG,IAAEG,IAAEJ,IAAEP,IAAEK,IAAED,EAAC,EAAE,WAAWY,IAAEd,GAAE,KAAIL,EAAC,GAAEE,MAAGA,GAAE,SAAO,GAAE;AAAC,cAAIF,KAAE,MAAKC,KAAE;AAAM,qBAAUE,MAAKD,GAAE,CAAAF,KAAE,KAAK,IAAIA,IAAEG,GAAE,CAAC,GAAEF,KAAE,KAAK,IAAIA,IAAEE,GAAE,CAAC;AAAE,UAAAU,MAAGZ,KAAED,KAAE,IAAEW,MAAGY,KAAEP;AAAA,QAAC;AAAA,MAAC;AAAC,eAAQf,MAAKI,GAAE,UAAS;AAAC,cAAMD,KAAE,CAAC;AAAE,YAAGI,OAAIN,GAAE,MAAK;AAAC,eAAGA,MAAA,gBAAAA,GAAG,YAAQ,uBAAG,OAAK;AAAC,kBAAMF,KAAE,EAAE,OAAKgB,MAAG,IAAE,KAAK,IAAI,GAAE,IAAE,KAAK,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AAAG,YAAAf,KAAE,GAAE,gBAAgBA,IAAED,EAAC;AAAA,UAAC;AAAC,aAAE,aAAaI,IAAEH,IAAEM,IAAEM,EAAC;AAAA,QAAC,MAAM,CAAAL,OAAIN,GAAE,cAAY,GAAE,kBAAkBE,IAAEH,EAAC,IAAEI,GAAE,QAAQ,SAAOJ,GAAE,UAAQ,GAAE,cAAcG,IAAEH,EAAC,IAAEG,GAAE,KAAK,IAAI,EAAEH,GAAE,CAAC,EAAE,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC;AAAE,mBAAUE,MAAKC,IAAE;AAAC,cAAGD,GAAE,IAAE,KAAGA,GAAE,IAAE,KAAGA,GAAE,IAAE,KAAGA,GAAE,IAAE,EAAE;AAAS,cAAGG,MAAGO,KAAE,MAAG,uBAAG,uBAAoB,EAAE,OAAK,CAAC,GAAE,oBAAoBZ,IAAEE,IAAEU,IAAE,GAAE,CAAC,EAAE;AAAS,gBAAMT,KAAE,EAAC,SAAQF,IAAE,MAAKD,IAAE,gBAAeD,IAAE,QAAOG,IAAE,eAAcE,IAAE,eAAc,CAAC,GAAE,eAAc,CAAC,GAAE,kBAAiB,CAAC,GAAE,kBAAiB,CAAC,EAAC;AAAE,UAAAiB,GAAE,KAAKlB,EAAC,GAAE,KAAK,gBAAgBA,IAAEL,IAAE,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAuB,GAAE,KAAK,CAAC,GAAE,KAAK,iBAAiB,GAAE,KAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAItB,KAAE;AAAG,IAAAA,MAAG,KAAK,UAAU,QAAOA,MAAG,IAAE,KAAK,cAAc,MAAKA,MAAG,IAAE,KAAK,cAAc,MAAKA,MAAG,GAAE,4BAA4B,KAAK,gBAAgB,GAAEA,MAAG,KAAK,kBAAkB,MAAM,QAAOA,MAAG,KAAK,iBAAiB,MAAM,QAAOA,MAAG,KAAK,kBAAkB,MAAM,QAAOA,MAAG,KAAK,iBAAiB,MAAM;AAAO,UAAMC,KAAE,IAAI,YAAYD,EAAC,GAAEE,KAAE,IAAI,WAAWD,GAAE,MAAM,GAAEE,KAAE,IAAI,aAAaF,GAAE,MAAM;AAAE,QAAIG,KAAE;AAAE,IAAAH,GAAEG,IAAG,IAAE,KAAK,MAAKH,GAAEG,IAAG,IAAE,KAAK,UAAU;AAAO,aAAQI,KAAE,GAAEA,KAAE,KAAK,UAAU,QAAOA,KAAI,CAAAP,GAAEG,IAAG,IAAE,KAAK,UAAUI,EAAC;AAAE,IAAAP,GAAEG,IAAG,IAAE,KAAK,aAAW,IAAE,GAAEH,GAAEG,IAAG,IAAE,KAAK,cAAc;AAAK,eAAS,CAACI,IAAE,CAACH,IAAEC,EAAC,CAAC,KAAI,KAAK,cAAc,CAAAL,GAAEG,IAAG,IAAEI,IAAEP,GAAEG,IAAG,IAAEC,IAAEJ,GAAEG,IAAG,IAAEE;AAAE,IAAAL,GAAEG,IAAG,IAAE,KAAK,cAAc;AAAK,eAAS,CAACI,IAAE,CAACH,IAAEC,EAAC,CAAC,KAAI,KAAK,cAAc,CAAAL,GAAEG,IAAG,IAAEI,IAAEP,GAAEG,IAAG,IAAEC,IAAEJ,GAAEG,IAAG,IAAEE;AAAE,IAAAL,GAAEG,IAAG,IAAE,KAAK,kBAAkB,QAAM,GAAEH,GAAEG,IAAG,IAAE,KAAK,kBAAkB,QAAM,GAAEA,KAAE,GAAE,iBAAiBH,IAAEC,IAAEC,IAAEC,IAAE,KAAK,gBAAgB,GAAEH,GAAEG,IAAG,IAAE,KAAK,kBAAkB,MAAM;AAAO,aAAQI,KAAE,GAAEA,KAAE,KAAK,kBAAkB,MAAM,QAAOA,KAAI,CAAAN,GAAEE,IAAG,IAAE,KAAK,kBAAkB,MAAMI,EAAC;AAAE,IAAAP,GAAEG,IAAG,IAAE,KAAK,iBAAiB,MAAM;AAAO,aAAQI,KAAE,GAAEA,KAAE,KAAK,iBAAiB,MAAM,QAAOA,KAAI,CAAAP,GAAEG,IAAG,IAAE,KAAK,iBAAiB,MAAMI,EAAC;AAAE,IAAAP,GAAEG,IAAG,IAAE,KAAK,kBAAkB,MAAM;AAAO,aAAQI,KAAE,GAAEA,KAAE,KAAK,kBAAkB,MAAM,QAAOA,KAAI,CAAAN,GAAEE,IAAG,IAAE,KAAK,kBAAkB,MAAMI,EAAC;AAAE,IAAAP,GAAEG,IAAG,IAAE,KAAK,iBAAiB,MAAM;AAAO,aAAQI,KAAE,GAAEA,KAAE,KAAK,iBAAiB,MAAM,QAAOA,KAAI,CAAAP,GAAEG,IAAG,IAAE,KAAK,iBAAiB,MAAMI,EAAC;AAAE,WAAOP,GAAE;AAAA,EAAM;AAAA,EAAC,OAAO,4BAA4BD,IAAE;AAAC,QAAIC,KAAE;AAAE,IAAAA,MAAG;AAAE,eAAUC,MAAKF,MAAG,CAAC,GAAE;AAAC,MAAAC,MAAG,GAAEA,MAAG;AAAE,iBAAUD,MAAKE,GAAE,cAAc,CAAAD,MAAGF;AAAE,iBAAUC,MAAKE,GAAE,cAAc,CAAAD,MAAGF;AAAE,MAAAE,MAAG,GAAEA,MAAG,IAAEC,GAAE,iBAAiB,QAAOD,MAAG,GAAEA,MAAG,IAAEC,GAAE,iBAAiB;AAAA,IAAM;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiBD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,IAAAA,KAAEA,MAAG,CAAC,GAAEH,GAAEE,IAAG,IAAEC,GAAE;AAAO,eAAUI,MAAKJ,IAAE;AAAC,MAAAH,GAAEE,IAAG,IAAEK,GAAE,OAAO,GAAEP,GAAEE,IAAG,IAAEK,GAAE,OAAO,GAAEP,GAAEE,IAAG,IAAEK,GAAE,cAAc,MAAKP,GAAEE,IAAG,IAAEK,GAAE,cAAc,UAASP,GAAEE,IAAG,IAAEK,GAAE,cAAc,SAAOA,GAAE,cAAc;AAAO,iBAAUR,MAAKQ,GAAE,cAAc,CAAAP,GAAEE,IAAG,IAAEH,GAAE,OAAMC,GAAEE,IAAG,IAAEH,GAAE,OAAMC,GAAEE,IAAG,IAAEH,GAAE,UAASC,GAAEE,IAAG,IAAEH,GAAE,UAASC,GAAEE,IAAG,IAAEH,GAAE,OAAK,IAAE,GAAEC,GAAEE,IAAG,IAAEH,GAAE,WAAUE,GAAEC,IAAG,IAAEH,GAAE,QAAOE,GAAEC,IAAG,IAAEH,GAAE,QAAOC,GAAEE,IAAG,IAAEH,GAAE,OAAMC,GAAEE,IAAG,IAAEH,GAAE;AAAO,iBAAUA,MAAKQ,GAAE,cAAc,CAAAP,GAAEE,IAAG,IAAEH,GAAE,OAAMC,GAAEE,IAAG,IAAEH,GAAE,OAAMC,GAAEE,IAAG,IAAEH,GAAE,UAASC,GAAEE,IAAG,IAAEH,GAAE,UAASC,GAAEE,IAAG,IAAEH,GAAE,OAAK,IAAE,GAAEC,GAAEE,IAAG,IAAEH,GAAE,WAAUE,GAAEC,IAAG,IAAEH,GAAE,QAAOE,GAAEC,IAAG,IAAEH,GAAE,QAAOC,GAAEE,IAAG,IAAEH,GAAE,OAAMC,GAAEE,IAAG,IAAEH,GAAE;AAAO,MAAAC,GAAEE,IAAG,IAAEK,GAAE,iBAAiB;AAAO,iBAAS,CAACR,IAAEE,EAAC,KAAIM,GAAE,iBAAiB,CAAAP,GAAEE,IAAG,IAAEH,IAAEC,GAAEE,IAAG,IAAED;AAAE,MAAAD,GAAEE,IAAG,IAAEK,GAAE,iBAAiB;AAAO,iBAAS,CAACR,IAAEE,EAAC,KAAIM,GAAE,iBAAiB,CAAAP,GAAEE,IAAG,IAAEH,IAAEC,GAAEE,IAAG,IAAED;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,aAAaH,IAAEC,IAAE;AAAC,WAAOD,GAAE,QAAQ,eAAe,CAACA,IAAEE,OAAIA,MAAKD,KAAEA,GAAEC,EAAC,IAAE,EAAG;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,MAAKC,IAAE,gBAAeC,IAAE,SAAQI,IAAE,QAAOH,GAAC,IAAEL,IAAEO,KAAE,KAAK,MAAKE,KAAE,KAAK,OAAMC,KAAE,CAAC,CAACN;AAAE,QAAIO,KAAE;AAAG,IAAAD,OAAIC,MAAEV,MAAA,gBAAAA,GAAG,aAAU,CAACG;AAAG,UAAM,IAAEI,MAAGA,GAAE,SAAO,GAAEI,KAAE,CAAC,MAAGV,MAAA,gBAAAA,GAAG;AAAS,QAAIW,IAAEM;AAAE,QAAGT,OAAIG,KAAE,KAAK,iBAAiB,iBAAiBR,IAAED,IAAEH,EAAC,KAAIY,MAAGF,QAAK,MAAIQ,KAAE,KAAK,iBAAiB,iBAAiBd,IAAEG,IAAEL,IAAED,EAAC,IAAGiB,MAAGP,KAAG;AAAC,UAAGC,MAAGM,OAAIP,MAAGD,KAAEC,MAAGO,KAAER,MAAGE,OAAIM,KAAE,QAAMN,KAAE,QAAMA,KAAE,MAAKM,KAAE,QAAOA,IAAE;AAAC,cAAMlB,KAAEQ,GAAE,oBAAkBA,GAAE,aAAa,iBAAiBT,GAAE,cAAc,SAAQO,IAAEE,EAAC,IAAE;AAAK,YAAG,KAAK,mBAAmBT,IAAEmB,GAAE,QAAOZ,IAAEL,GAAE,mBAAkBD,EAAC,GAAEkB,GAAE,eAAc;AAAC,UAAAnB,GAAE,gBAAcmB,GAAE;AAAc,qBAAUnB,MAAKmB,GAAE,eAAc;AAAC,YAAAnB,GAAE,SAAO,KAAK,IAAIO,KAAE,EAAEP,GAAE,MAAM,GAAE,CAAC,GAAEA,GAAE,SAAO,KAAK,IAAIO,KAAE,EAAEP,GAAE,MAAM,GAAE,EAAE;AAAE,kBAAMC,MAAED,GAAE;AAAM,gBAAGC,KAAE;AAAC,oBAAMC,KAAE,KAAK,IAAID,GAAC,GAAEE,KAAE,KAAK,IAAIF,GAAC,GAAEG,KAAEJ,GAAE,WAASE,KAAEF,GAAE,WAASG,IAAEK,KAAER,GAAE,WAASG,KAAEH,GAAE,WAASE,IAAEG,MAAGL,GAAE,WAASA,GAAE,SAAOE,KAAEF,GAAE,WAASG,IAAEG,MAAGN,GAAE,WAASA,GAAE,SAAOG,KAAEH,GAAE,WAASE,IAAEK,KAAEP,GAAE,WAASE,MAAGF,GAAE,WAASA,GAAE,UAAQG,IAAEM,KAAET,GAAE,WAASG,MAAGH,GAAE,WAASA,GAAE,UAAQE,IAAEQ,MAAGV,GAAE,WAASA,GAAE,SAAOE,MAAGF,GAAE,WAASA,GAAE,UAAQG,IAAEQ,MAAGX,GAAE,WAASA,GAAE,SAAOG,MAAGH,GAAE,WAASA,GAAE,UAAQE,IAAEsB,KAAE,KAAK,IAAIpB,IAAEC,IAAEE,IAAEG,EAAC,GAAEE,KAAE,KAAK,IAAIR,IAAEC,IAAEE,IAAEG,EAAC,GAAEG,KAAE,KAAK,IAAIL,IAAEF,IAAEG,IAAEE,EAAC,GAAEQ,KAAE,KAAK,IAAIX,IAAEF,IAAEG,IAAEE,EAAC;AAAE,cAAAX,GAAE,WAASwB,IAAExB,GAAE,WAASa,IAAEb,GAAE,QAAMY,KAAEY,IAAExB,GAAE,SAAOmB,KAAEN;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAGA,IAAE;AAAC,cAAMX,KAAEO,GAAE,oBAAkBA,GAAE,aAAa,iBAAiBT,GAAE,cAAc,SAAQO,IAAEE,EAAC,IAAE;AAAK,YAAG,KAAK,gBAAgBT,IAAEa,GAAE,QAAON,IAAEH,GAAE,MAAKH,GAAE,sBAAoB,EAAE,UAASC,EAAC,GAAEW,GAAE,eAAc;AAAC,UAAAb,GAAE,gBAAca,GAAE;AAAc,qBAAUb,MAAKa,GAAE,eAAc;AAAC,YAAAb,GAAE,SAAO,KAAK,IAAIO,KAAE,EAAEP,GAAE,MAAM,GAAE,CAAC,GAAEA,GAAE,SAAO,KAAK,IAAIO,KAAE,EAAEP,GAAE,MAAM,GAAE,EAAE;AAAE,kBAAMC,KAAED,GAAE;AAAM,gBAAGC,IAAE;AAAC,oBAAMC,KAAE,KAAK,IAAID,EAAC,GAAEE,KAAE,KAAK,IAAIF,EAAC,GAAEG,KAAEJ,GAAE,WAASE,KAAEF,GAAE,WAASG,IAAEK,KAAER,GAAE,WAASG,KAAEH,GAAE,WAASE,IAAEG,MAAGL,GAAE,WAASA,GAAE,SAAOE,KAAEF,GAAE,WAASG,IAAEG,MAAGN,GAAE,WAASA,GAAE,SAAOG,KAAEH,GAAE,WAASE,IAAEK,KAAEP,GAAE,WAASE,MAAGF,GAAE,WAASA,GAAE,UAAQG,IAAEM,KAAET,GAAE,WAASG,MAAGH,GAAE,WAASA,GAAE,UAAQE,IAAEQ,MAAGV,GAAE,WAASA,GAAE,SAAOE,MAAGF,GAAE,WAASA,GAAE,UAAQG,IAAEQ,MAAGX,GAAE,WAASA,GAAE,SAAOG,MAAGH,GAAE,WAASA,GAAE,UAAQE,IAAEsB,KAAE,KAAK,IAAIpB,IAAEC,IAAEE,IAAEG,EAAC,GAAEE,KAAE,KAAK,IAAIR,IAAEC,IAAEE,IAAEG,EAAC,GAAEG,KAAE,KAAK,IAAIL,IAAEF,IAAEG,IAAEE,EAAC,GAAEQ,KAAE,KAAK,IAAIX,IAAEF,IAAEG,IAAEE,EAAC;AAAE,cAAAX,GAAE,WAASwB,IAAExB,GAAE,WAASa,IAAEb,GAAE,QAAMY,KAAEY,IAAExB,GAAE,SAAOmB,KAAEN;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBb,IAAEC,IAAEC,IAAEC,IAAEC,IAAEI,IAAE;AAAC,UAAMH,KAAE,KAAK,IAAIH,KAAE,GAAE,CAAC,GAAEK,KAAE,KAAK,mBAAkBE,KAAE,KAAK,kBAAiBC,KAAE,KAAK;AAAW,eAAUC,MAAKV,IAAE;AAAC,YAAMA,KAAEG,KAAE,IAAE,KAAK,IAAIF,KAAE,EAAES,GAAE,OAAO,GAAEN,EAAC,GAAE,IAAED,KAAE,KAAG,KAAK,IAAIF,KAAE,EAAES,GAAE,OAAO,GAAE,EAAE;AAAE,UAAG,KAAGV,GAAE;AAAS,YAAMW,KAAED,GAAE,IAAGE,KAAEF,GAAE,IAAGQ,KAAER,GAAE,IAAGG,KAAEH,GAAE,IAAGI,KAAEJ,GAAE,YAAWK,KAAEL,GAAE,YAAWM,KAAEN,GAAE,UAAS,IAAEA,GAAE,UAAS,IAAEA,GAAE,QAAOO,KAAEX,GAAE,OAAMR,KAAEgB,GAAE,GAAEU,KAAEV,GAAE,GAAEW,KAAE3B,KAAEgB,GAAE,OAAM,IAAEU,KAAEV,GAAE,QAAOK,KAAEb,GAAE;AAAM,MAAAA,GAAE,IAAI,EAAE,GAAE,EAAE,GAAEK,GAAE,GAAEA,GAAE,GAAEb,IAAE0B,IAAET,IAAEC,IAAE,GAAEhB,IAAE,GAAEO,EAAC,GAAED,GAAE,IAAI,EAAE,GAAE,EAAE,GAAEM,GAAE,GAAEA,GAAE,GAAEa,IAAED,IAAET,IAAEC,IAAE,GAAEhB,IAAE,GAAEO,EAAC,GAAED,GAAE,IAAI,EAAE,GAAE,EAAE,GAAEY,GAAE,GAAEA,GAAE,GAAEpB,IAAE,GAAEiB,IAAEC,IAAE,GAAEhB,IAAE,GAAEO,EAAC,GAAED,GAAE,IAAI,EAAE,GAAE,EAAE,GAAEO,GAAE,GAAEA,GAAE,GAAEY,IAAE,GAAEV,IAAEC,IAAE,GAAEhB,IAAE,GAAEO,EAAC,GAAER,GAAE,iBAAiB,SAAO,KAAGA,GAAE,iBAAiB,CAAC,EAAE,CAAC,IAAEA,GAAE,iBAAiB,CAAC,EAAE,CAAC,MAAIoB,KAAEpB,GAAE,iBAAiB,CAAC,EAAE,CAAC,KAAG,IAAEA,GAAE,iBAAiB,KAAK,CAACoB,IAAE,CAAC,CAAC,GAAEX,GAAE,IAAIS,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAET,GAAE,IAAIS,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAER,GAAE,IAAIP,EAAC,IAAEO,GAAE,IAAIP,EAAC,EAAE,CAAC,KAAG,IAAEO,GAAE,IAAIP,IAAE,CAAC,KAAK,kBAAgB,KAAK,iBAAgB,CAAC,CAAC,GAAE,KAAK,mBAAiB;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,UAAMH,KAAE,KAAK,mBAAkBC,KAAE,KAAK,kBAAiBC,KAAE,KAAK;AAAU,eAAS,CAACC,IAAEC,EAAC,KAAI,KAAK,wBAAwB,YAAUI,MAAKJ,IAAE;AAAC,YAAMA,KAAEJ,GAAE,OAAMK,KAAEG,GAAE,gBAAeF,KAAEE,GAAE,cAAaD,KAAEP,GAAE;AAAM,MAAAA,GAAE,IAAIQ,GAAE,YAAY,CAAC,GAAEA,GAAE,YAAY,CAAC,GAAEA,GAAE,GAAG,CAAC,GAAEA,GAAE,GAAG,CAAC,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,YAAWA,GAAE,UAASA,GAAE,UAASA,GAAE,QAAOA,GAAE,QAAOF,EAAC,GAAEN,GAAE,IAAIQ,GAAE,YAAY,CAAC,GAAEA,GAAE,YAAY,CAAC,GAAEA,GAAE,GAAG,CAAC,GAAEA,GAAE,GAAG,CAAC,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,YAAWA,GAAE,UAASA,GAAE,UAASA,GAAE,QAAOA,GAAE,QAAOF,EAAC,GAAEN,GAAE,IAAIQ,GAAE,YAAY,CAAC,GAAEA,GAAE,YAAY,CAAC,GAAEA,GAAE,GAAG,CAAC,GAAEA,GAAE,GAAG,CAAC,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,YAAWA,GAAE,UAASA,GAAE,UAASA,GAAE,QAAOA,GAAE,QAAOF,EAAC,GAAEN,GAAE,IAAIQ,GAAE,YAAY,CAAC,GAAEA,GAAE,YAAY,CAAC,GAAEA,GAAE,GAAG,CAAC,GAAEA,GAAE,GAAG,CAAC,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,YAAWA,GAAE,UAASA,GAAE,UAASA,GAAE,QAAOA,GAAE,QAAOF,EAAC,GAAED,GAAE,iBAAiB,SAAO,KAAGA,GAAE,iBAAiB,CAAC,EAAE,CAAC,IAAEA,GAAE,iBAAiB,CAAC,EAAE,CAAC,MAAIE,KAAEF,GAAE,iBAAiB,CAAC,EAAE,CAAC,KAAG,IAAEA,GAAE,iBAAiB,KAAK,CAACE,IAAE,CAAC,CAAC,GAAEN,GAAE,IAAIG,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAEH,GAAE,IAAIG,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAEF,GAAE,IAAIC,EAAC,IAAED,GAAE,IAAIC,EAAC,EAAE,CAAC,KAAG,IAAED,GAAE,IAAIC,IAAE,CAAC,KAAK,kBAAgB,KAAK,iBAAgB,CAAC,CAAC,GAAE,KAAK,mBAAiB;AAAA,IAAC;AAAC,SAAK,wBAAwB,MAAM;AAAA,EAAC;AAAA,EAAC,mBAAmBH,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMI,KAAE,KAAK,IAAIN,KAAE,GAAE,CAAC,GAAEG,KAAEF,OAAI,EAAE;AAAS,QAAII,IAAEE,IAAEC,IAAEC,IAAE,GAAEC,IAAEC,IAAEM,IAAEL,IAAEE,IAAEC;AAAE,eAAUF,MAAKd,IAAE;AAAC,UAAGM,KAAEF,KAAE,IAAE,KAAK,IAAIH,KAAE,EAAEa,GAAE,OAAO,GAAEP,EAAC,GAAEC,KAAEJ,KAAE,KAAG,KAAK,IAAIH,KAAE,EAAEa,GAAE,OAAO,GAAE,EAAE,GAAEN,MAAGF,GAAE;AAAS,MAAAG,KAAEK,GAAE,IAAGJ,KAAEI,GAAE,IAAG,IAAEA,GAAE,IAAGH,KAAEG,GAAE,IAAGF,KAAEE,GAAE,YAAWI,KAAEJ,GAAE,UAASD,KAAEC,GAAE,UAASC,KAAED,GAAE,QAAOE,KAAEF,GAAE,YAAW,KAAK,wBAAwB,IAAIA,GAAE,IAAI,KAAG,KAAK,wBAAwB,IAAIA,GAAE,MAAK,CAAC,CAAC;AAAE,WAAK,wBAAwB,IAAIA,GAAE,IAAI,EAAE,KAAK,EAAC,aAAY,CAACC,GAAE,GAAEA,GAAE,CAAC,GAAE,IAAG,CAACN,GAAE,GAAEA,GAAE,CAAC,GAAE,IAAG,CAACC,GAAE,GAAEA,GAAE,CAAC,GAAE,IAAG,CAAC,EAAE,GAAE,EAAE,CAAC,GAAE,IAAG,CAACC,GAAE,GAAEA,GAAE,CAAC,GAAE,MAAKK,GAAE,GAAE,MAAKA,GAAE,GAAE,MAAKA,GAAE,IAAEA,GAAE,OAAM,MAAKA,GAAE,IAAEA,GAAE,QAAO,YAAWJ,IAAE,UAASM,IAAE,UAASL,IAAE,QAAOP,IAAE,QAAOE,IAAE,cAAaD,IAAE,gBAAeR,IAAE,cAAaI,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,aAAaJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,IAAAD,MAAGC;AAAE,QAAIK,KAAE;AAAE,UAAMH,KAAEJ,GAAE,SAAO;AAAE,aAAQM,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAC,MAAG,EAAE,SAASP,GAAEM,EAAC,GAAEN,GAAEM,KAAE,CAAC,CAAC;AAAE,QAAID,KAAEH,MAAGD;AAAE,QAAGI,MAAG,KAAGE,MAAGF,GAAE;AAAO,UAAMG,KAAEH,KAAEE;AAAE,QAAIG,KAAE,GAAE,IAAE,EAAET,KAAEM,KAAE,KAAK,IAAI,KAAK,MAAMA,KAAEN,EAAC,GAAE,CAAC,KAAG;AAAE,UAAMU,KAAEX,GAAE,SAAO;AAAE,aAAQG,KAAE,GAAEA,KAAEQ,IAAER,MAAI;AAAC,YAAMD,KAAEF,GAAEG,EAAC,GAAEI,KAAEP,GAAEG,KAAE,CAAC,GAAEC,KAAEG,GAAE,IAAEL,GAAE,GAAEG,MAAEE,GAAE,IAAEL,GAAE,GAAES,KAAE,KAAK,KAAKP,KAAEA,KAAEC,MAAEA,GAAC;AAAE,UAAIO;AAAE,aAAK,IAAEX,KAAES,KAAEC,MAAG;AAAC,aAAGV;AAAE,cAAMD,MAAG,IAAEU,MAAGC,IAAEO,KAAEV,GAAEN,GAAE,GAAEK,GAAE,GAAEP,EAAC,GAAEa,KAAEL,GAAEN,GAAE,GAAEK,GAAE,GAAEP,EAAC;AAAE,mBAASY,OAAIA,KAAE,KAAK,MAAMP,KAAED,EAAC,IAAGL,GAAE,KAAK,IAAI,EAAEmB,IAAEL,IAAED,IAAET,IAAEK,EAAC,CAAC;AAAA,MAAC;AAAC,MAAAE,MAAGC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,kBAAkBZ,IAAEC,IAAE;AAAC,QAAIC,KAAE;AAAE,UAAMC,KAAEF,GAAE,SAAO;AAAE,aAAQM,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,CAAAL,MAAG,EAAE,SAASD,GAAEM,EAAC,GAAEN,GAAEM,KAAE,CAAC,CAAC;AAAE,UAAMC,KAAEN,KAAE;AAAE,QAAIG,KAAE;AAAE,UAAMC,KAAEL,GAAE,SAAO;AAAE,aAAQG,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,YAAMF,KAAED,GAAEG,EAAC,GAAED,KAAEF,GAAEG,KAAE,CAAC,GAAEE,MAAEH,GAAE,IAAED,GAAE,GAAEO,KAAEN,GAAE,IAAED,GAAE,GAAES,KAAE,KAAK,KAAKL,MAAEA,MAAEG,KAAEA,EAAC;AAAE,UAAGD,KAAEH,KAAEM,IAAE;AAAC,cAAMV,MAAGO,KAAEH,MAAGM,IAAE,IAAEF,GAAEP,GAAE,GAAEC,GAAE,GAAEF,EAAC,GAAEW,KAAEH,GAAEP,GAAE,GAAEC,GAAE,GAAEF,EAAC,GAAEY,KAAE,KAAK,MAAMJ,IAAEH,GAAC;AAAE,eAAO,KAAKN,GAAE,KAAK,IAAI,EAAE,GAAEY,IAAEC,IAAET,IAAE,CAAC,CAAC;AAAA,MAAC;AAAC,MAAAC,MAAGM;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,WAAWX,IAAEC,IAAEC,IAAE;AAAC,UAAMC,MAAGF,GAAE,IAAED,GAAE,MAAIE,GAAE,IAAED,GAAE,MAAIA,GAAE,IAAED,GAAE,MAAIE,GAAE,IAAED,GAAE,IAAGG,MAAGH,GAAE,IAAED,GAAE,MAAIE,GAAE,IAAED,GAAE,MAAIA,GAAE,IAAED,GAAE,MAAIE,GAAE,IAAED,GAAE;AAAG,WAAO,KAAK,MAAMG,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,oBAAoBH,IAAEC,IAAEC,IAAEC,IAAEK,IAAE;AAAC,QAAIH,KAAE;AAAE,UAAMC,KAAEJ,KAAE;AAAE,QAAIK,KAAE,IAAI,EAAEN,GAAE,GAAEA,GAAE,CAAC,GAAEQ,KAAER,GAAE,UAAQ;AAAE,WAAKI,KAAE,CAACC,MAAG;AAAC,UAAG,EAAEG,IAAEA,KAAE,EAAE,QAAM;AAAG,MAAAJ,MAAG,EAAE,SAASL,GAAES,EAAC,GAAEF,EAAC,GAAEA,KAAEP,GAAES,EAAC;AAAA,IAAC;AAAC,IAAAJ,MAAG,EAAE,SAASL,GAAES,EAAC,GAAET,GAAES,KAAE,CAAC,CAAC;AAAE,UAAMC,KAAE,CAAC;AAAE,QAAIC,KAAE;AAAE,UAAM,IAAEX,GAAE;AAAO,WAAKK,KAAEC,MAAG;AAAC,YAAML,KAAED,GAAES,EAAC;AAAE,UAAIP,IAAEI,MAAEG;AAAE,SAAE;AAAC,YAAG,EAAEH,KAAEA,QAAI,EAAE,QAAM;AAAG,QAAAJ,KAAEF,GAAEM,GAAC;AAAA,MAAC,SAAOJ,GAAE,QAAQD,EAAC;AAAG,UAAIM,IAAEK,KAAEN;AAAE,SAAE;AAAC,YAAG,EAAEM,IAAEA,OAAI,EAAE,QAAM;AAAG,QAAAL,KAAEP,GAAEY,EAAC;AAAA,MAAC,SAAOL,GAAE,QAAQL,EAAC;AAAG,YAAMW,KAAE,KAAK,WAAWZ,IAAEC,IAAEK,EAAC;AAAE,WAAIG,GAAE,KAAK,EAAC,WAAUG,IAAE,cAAaR,GAAC,CAAC,GAAEM,MAAGE,IAAER,KAAEK,GAAE,CAAC,EAAE,eAAaF,KAAG,CAAAG,MAAGD,GAAE,MAAM,EAAE;AAAU,UAAG,KAAK,IAAIC,EAAC,IAAER,GAAE,QAAM;AAAG,MAAAE,MAAG,EAAE,SAASH,IAAEK,EAAC,GAAEE,KAAEH;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,OAAO,gBAAgBN,IAAEC,IAAE;AAAC,QAAGA,MAAG,EAAE,QAAOD;AAAE,QAAIE,KAAEF,GAAE;AAAO,QAAGE,KAAE,EAAE,QAAOF;AAAE,UAAMG,KAAE,CAAC;AAAE,QAAIK,KAAE,GAAEH,KAAE;AAAE,IAAAF,GAAE,KAAK,CAAC;AAAE,aAAQgB,KAAE,GAAEA,KAAEjB,IAAEiB,MAAI;AAAC,YAAMlB,KAAE,EAAE,SAASD,GAAEmB,EAAC,GAAEnB,GAAEmB,KAAE,CAAC,CAAC;AAAE,MAAAlB,KAAE,MAAIO,MAAGP,IAAEE,GAAE,KAAKK,EAAC,GAAEH,MAAIA,OAAIc,OAAInB,GAAEK,EAAC,IAAEL,GAAEmB,EAAC;AAAA,IAAG;AAAC,QAAGjB,KAAEG,KAAE,GAAEH,KAAE,EAAE,QAAOF;AAAE,IAAAC,KAAE,KAAK,IAAIA,IAAE,MAAGO,EAAC;AAAE,UAAMF,KAAEN,GAAE,CAAC,EAAE,GAAEO,KAAEP,GAAE,CAAC,EAAE,GAAES,KAAET,GAAEE,KAAE,CAAC,EAAE,GAAEQ,KAAEV,GAAEE,KAAE,CAAC,EAAE,GAAES,KAAE,EAAE,IAAIX,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,IAAAW,GAAE,UAAU,GAAEX,GAAE,CAAC,EAAE,KAAGC,KAAEU,GAAE,GAAEX,GAAE,CAAC,EAAE,KAAGC,KAAEU,GAAE,GAAEA,GAAE,UAAUX,GAAEE,KAAE,CAAC,GAAEF,GAAEE,KAAE,CAAC,CAAC,GAAES,GAAE,UAAU,GAAEX,GAAEE,KAAE,CAAC,EAAE,KAAGD,KAAEU,GAAE,GAAEX,GAAEE,KAAE,CAAC,EAAE,KAAGD,KAAEU,GAAE,GAAER,GAAE,CAAC,KAAGF,IAAEE,GAAED,KAAE,CAAC,KAAGD;AAAE,UAAM,IAAE,CAAC;AAAE,MAAE,KAAK,IAAI,EAAEK,IAAEC,EAAC,CAAC;AAAE,UAAMK,KAAE,MAAKC,KAAE,MAAGZ;AAAE,aAAQkB,KAAE,GAAEA,KAAEjB,KAAE,GAAEiB,MAAI;AAAC,UAAIX,KAAE,GAAEH,KAAE,GAAEC,MAAE;AAAE,eAAQJ,KAAEiB,KAAE,GAAEjB,MAAG,GAAEA,MAAI;AAAC,cAAME,KAAES,KAAEV,GAAED,KAAE,CAAC,IAAEC,GAAEgB,EAAC;AAAE,YAAGf,KAAE,EAAE;AAAM,cAAMG,KAAEJ,GAAED,KAAE,CAAC,IAAEC,GAAED,EAAC,GAAEO,KAAEN,GAAEgB,EAAC,IAAEhB,GAAED,EAAC,IAAEW,KAAE,IAAET,KAAEG;AAAE,YAAGE,KAAEG,GAAE;AAAM,cAAMF,KAAED,KAAEA,IAAEE,KAAEF,KAAEL,KAAE,MAAGM,KAAEH,IAAEiB,KAAEf,KAAEF,KAAEN,IAAEa,KAAEd,GAAEE,KAAE,CAAC,GAAEa,KAAEf,GAAEE,EAAC,EAAE,IAAEY,GAAE,GAAEE,KAAEhB,GAAEE,EAAC,EAAE,IAAEY,GAAE;AAAE,QAAAN,MAAGgB,KAAEb,MAAGG,GAAE,IAAEL,KAAEL,KAAE,MAAGM,MAAGN,KAAEW,KAAER,KAAEO,GAAE,KAAGJ,KAAED,KAAEF,KAAEQ,KAAE,IAAGV,MAAGmB,KAAEb,MAAGG,GAAE,IAAEL,KAAEL,KAAE,MAAGM,MAAGN,KAAEY,KAAET,KAAEO,GAAE,KAAGJ,KAAED,KAAEF,KAAES,KAAE,IAAGV,OAAGkB;AAAA,MAAC;AAAC,eAAQpB,KAAEe,KAAE,GAAEf,KAAEF,IAAEE,MAAI;AAAC,cAAMF,KAAEW,KAAEV,GAAEC,KAAE,CAAC,IAAED,GAAEgB,EAAC;AAAE,YAAGjB,KAAE,EAAE;AAAM,cAAMK,KAAEJ,GAAEC,EAAC,IAAED,GAAEC,KAAE,CAAC,GAAEK,KAAEN,GAAEC,EAAC,IAAED,GAAEgB,EAAC,IAAEN,KAAE,IAAEX,KAAEK;AAAE,YAAGE,KAAEG,GAAE;AAAM,cAAMF,KAAED,KAAEA,IAAEE,KAAEF,KAAEP,KAAE,MAAGQ,KAAEH,IAAEiB,KAAEf,KAAEF,KAAEN,IAAEa,KAAEd,GAAEI,KAAE,CAAC,GAAEW,KAAEf,GAAEI,EAAC,EAAE,IAAEU,GAAE,GAAEE,KAAEhB,GAAEI,EAAC,EAAE,IAAEU,GAAE;AAAE,QAAAN,MAAGgB,KAAEb,MAAGG,GAAE,IAAEL,KAAEP,KAAE,MAAGQ,MAAGR,KAAEa,KAAER,KAAEO,GAAE,KAAGJ,KAAED,KAAEF,KAAEQ,KAAE,IAAGV,MAAGmB,KAAEb,MAAGG,GAAE,IAAEL,KAAEP,KAAE,MAAGQ,MAAGR,KAAEc,KAAET,KAAEO,GAAE,KAAGJ,KAAED,KAAEF,KAAES,KAAE,IAAGV,OAAGkB;AAAA,MAAC;AAAC,QAAE,KAAK,IAAI,EAAEhB,KAAEF,KAAED,KAAEC,GAAC,CAAC;AAAA,IAAC;AAAC,WAAO,EAAE,KAAK,IAAI,EAAEG,IAAEC,EAAC,CAAC,GAAEV,GAAE,CAAC,EAAE,IAAEM,IAAEN,GAAE,CAAC,EAAE,IAAEO,IAAEP,GAAEE,KAAE,CAAC,EAAE,IAAEO,IAAET,GAAEE,KAAE,CAAC,EAAE,IAAEQ,IAAE;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcV,IAAEC,IAAE;AAAC,UAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,MAAKI,KAAE,MAAKH,KAAEJ,GAAE,SAAO;AAAE,QAAIK,KAAE,GAAEC,KAAE,GAAEE,KAAE,GAAEE,KAAEV,GAAE,CAAC,EAAE,GAAE,IAAEA,GAAE,CAAC,EAAE;AAAE,IAAAU,KAAEP,OAAIO,KAAEP,KAAGO,KAAET,OAAIS,KAAET,KAAG,IAAEM,OAAI,IAAEA,KAAG,IAAEL,OAAI,IAAEA;AAAG,aAAQO,KAAE,GAAEA,KAAEL,IAAEK,MAAI;AAAC,UAAIV,KAAEC,GAAES,EAAC,EAAE,GAAEL,KAAEJ,GAAES,EAAC,EAAE,GAAEE,KAAEX,GAAES,KAAE,CAAC,EAAE,GAAEG,KAAEZ,GAAES,KAAE,CAAC,EAAE;AAAE,MAAAV,KAAEI,OAAIJ,KAAEI,KAAGJ,KAAEE,OAAIF,KAAEE,KAAGG,KAAEG,OAAIH,KAAEG,KAAGH,KAAEF,OAAIE,KAAEF,KAAGS,KAAER,OAAIQ,KAAER,KAAGQ,KAAEV,OAAIU,KAAEV,KAAGW,KAAEL,OAAIK,KAAEL,KAAGK,KAAEV,OAAIU,KAAEV;AAAG,YAAMgB,MAAGnB,KAAEW,OAAIE,KAAE,MAAID,KAAED,OAAIN,KAAE;AAAG,MAAAC,MAAGa,MAAGR,KAAEX,KAAEY,KAAGL,MAAGY,MAAG,IAAEd,KAAEQ,KAAGJ,MAAGU;AAAA,IAAC;AAAC,IAAAb,MAAG,IAAEG,IAAEF,MAAG,IAAEE,IAAE,MAAMH,EAAC,KAAG,MAAMC,EAAC,KAAGP,GAAE,KAAK,IAAI,EAAEM,IAAEC,EAAC,CAAC;AAAA,EAAC;AAAC;AAAC,EAAE,cAAY,IAAI;;;ACA/4d,IAAIoB;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,cAAY,CAAC,IAAE,eAAcA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,UAAQ,CAAC,IAAE;AAAS,EAAEA,OAAIA,KAAE,CAAC,EAAE;;;ACAqoB,IAAMC,KAAE;AAAR,IAAU,IAAE;AAAZ,IAAeC,KAAE;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAJt1B;AAIu1B,QAAG,KAAK,YAAU,CAAC,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,UAAQF,IAAE,KAAK,QAAMD,IAAEG,IAAE;AAAC,WAAK,kBAAgB,oBAAI;AAAI,iBAAUC,MAAKD,GAAE,MAAK,gBAAgB,IAAIC,EAAC;AAAA,IAAC;AAAC,SAAK,mBAAiBF,IAAE,KAAK,YAAQ,UAAK,qBAAL,mBAAuB,WAAQ,CAAC;AAAE,UAAK,CAACG,IAAEC,IAAEC,EAAC,IAAEP,GAAE,QAAQ,MAAM,GAAG,EAAE,IAAI,UAAU;AAAE,SAAK,SAAOK;AAAE,UAAMG,KAAEX,KAAE,KAAK,KAAK,KAAK,SAAO,KAAGC,IAAE,CAAC;AAAE,eAAUW,MAAK,OAAO,KAAKV,EAAC,GAAE;AAAC,YAAMC,MAAED,GAAEU,EAAC;AAAE,WAAK,UAAUA,EAAC,IAAE,IAAI,EAAE,IAAI,WAAWT,IAAE,SAAS,GAAE,IAAI,SAASA,IAAE,SAAS,CAAC;AAAE,UAAGA,IAAE,QAAO;AAAC,cAAK,CAACI,EAAC,IAAEJ,IAAE,OAAO,MAAM,GAAG,EAAE,IAAI,UAAU,GAAED,KAAEM,KAAED;AAAE,YAAGL,KAAE,GAAE;AAAC,gBAAMK,MAAG,KAAGL,MAAG,GAAEC,MAAEM,KAAEF,IAAEM,KAAEH,KAAEH;AAAE,eAAK,cAAcK,EAAC,IAAE,IAAI,EAAEV,IAAEC,KAAEU,IAAE,GAAEF,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,WAAK,cAAcC,EAAC,MAAI,KAAK,cAAcA,EAAC,IAAE,IAAI;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBL,IAAE;AAAC,WAAM,CAAC,KAAK,mBAAiB,KAAK,gBAAgB,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,MAAMA,IAAE;AAAC,UAAML,KAAEW,GAAE,GAAEC,KAAE,KAAK,YAAYP,EAAC,GAAE,EAAC,iBAAgBM,GAAC,IAAEC;AAAE,SAAK,eAAeA,EAAC,GAAE,KAAK,gBAAgBA,EAAC,GAAE,KAAK,gBAAgBA,EAAC;AAAE,UAAMV,KAAE,CAAC,GAAEC,KAAE,oBAAI,OAAIC,KAAE,CAACC,IAAEL,OAAI;AAAC,MAAAG,GAAE,IAAIE,EAAC,MAAIH,GAAE,KAAK,EAAC,MAAKG,IAAE,QAAOL,GAAC,CAAC,GAAEG,GAAE,IAAIE,EAAC;AAAA,IAAE,GAAEC,KAAE,CAAC;AAAE,eAAUL,MAAKU,GAAE,CAAAV,GAAE,aAAaA,GAAE,aAAYG,IAAEE,EAAC;AAAE,QAAG,KAAK,MAAM,WAASO,GAAE,QAAQ,QAAM,CAAC;AAAE,UAAMN,KAAE,KAAK,gBAAgBL,IAAEI,IAAED,EAAC;AAAE,WAAO,QAAQ,IAAI,CAAC,GAAGE,IAAEP,EAAC,CAAC,EAAE,KAAM,MAAI,KAAK,iBAAiBY,GAAE,eAAe,CAAE;AAAA,EAAC;AAAA,EAAC,YAAYP,IAAE;AAAC,WAAM,EAAC,QAAOA,MAAGA,GAAE,QAAO,sBAAqB,KAAK,eAAe,KAAK,SAAS,GAAE,QAAO,KAAK,SAAQ,MAAK,KAAK,QAAO,yBAAwB,KAAK,eAAc,sCAAqC,CAAC,GAAE,gCAA+B,CAAC,GAAE,iBAAgB,CAAC,GAAE,iBAAgB,CAAC,GAAE,6BAA4B,oBAAI,MAAG;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,UAAK,EAAC,sBAAqBL,IAAE,QAAOC,IAAE,MAAKW,IAAE,yBAAwBD,IAAE,sCAAqCT,IAAE,gCAA+BC,IAAE,iBAAgBC,IAAE,iBAAgBE,IAAE,6BAA4BC,GAAC,IAAEF;AAAE,aAAQG,KAAEP,GAAE,SAAO,GAAEO,MAAG,GAAEA,MAAI;AAAC,YAAMH,KAAEJ,GAAEO,EAAC;AAAE,UAAG,CAAC,KAAK,oBAAoBH,GAAE,GAAG,KAAGA,GAAE,WAASO,KAAE,KAAK,MAAMP,GAAE,OAAO,KAAGA,GAAE,WAASO,MAAGP,GAAE,WAASA,GAAE,SAAO,EAAE,WAAW;AAAS,UAAG,CAACL,GAAEK,GAAE,MAAM,KAAG,CAACM,GAAEN,GAAE,MAAM,EAAE;AAAS,YAAMI,KAAET,GAAEK,GAAE,MAAM,GAAEK,KAAEC,GAAEN,GAAE,MAAM,GAAES,KAAET,GAAE,aAAYU,KAAEN,GAAEK,EAAC;AAAE,UAAGC,IAAE;AAAC,YAAIf,KAAEG,GAAEE,GAAE,MAAM;AAAE,YAAGL,OAAIA,KAAEG,GAAEE,GAAE,MAAM,IAAE,oBAAI,QAAKL,GAAE,IAAIK,GAAE,WAAW,GAAEA,GAAE,WAAW,CAAAE,GAAE,IAAIF,GAAE,KAAIA,GAAE,UAAU;AAAA,aAAM;AAAC,gBAAML,MAAE,KAAK,cAAcK,EAAC;AAAE,cAAGL,KAAE;AAAC,YAAAA,IAAE,YAAU,CAACK,GAAE,GAAG,GAAEL,IAAE,cAAYe,GAAE,QAAOf,IAAE,cAAYU;AAAE,gBAAIT,MAAEC,GAAEG,GAAE,MAAM;AAAE,YAAAJ,QAAIA,MAAEC,GAAEG,GAAE,MAAM,IAAE,CAAC;AAAG,gBAAIO,KAAEX,IAAEa,EAAC;AAAE,YAAAF,OAAIA,KAAEX,IAAEa,EAAC,IAAE,CAAC,IAAGF,GAAE,KAAKZ,GAAC,GAAEI,GAAE,KAAKJ,GAAC,GAAEM,GAAED,GAAE,EAAE,IAAEL;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBK,IAAE;AAAC,UAAK,EAAC,iBAAgBL,IAAE,6BAA4BC,GAAC,IAAEI;AAAE,IAAAJ,GAAE,QAAS,CAACI,IAAEJ,QAAI;AAAC,MAAAD,GAAEK,EAAC,KAAGL,GAAEK,EAAC,EAAE,UAAU,KAAKJ,GAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBI,IAAE;AAAC,UAAK,EAAC,QAAOJ,IAAE,sBAAqBW,IAAE,sCAAqCD,IAAE,gCAA+BR,GAAC,IAAEE,IAAED,KAAE,KAAG,KAAK,QAAOE,KAAE,MAAI,KAAK,SAAO,IAAGC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,eAAUR,MAAK,OAAO,KAAKG,EAAC,GAAE;AAAC,MAAAA,GAAEH,EAAC,EAAE,QAAS,CAAAK,OAAG;AAAC,QAAAE,GAAE,KAAKF,EAAC,GAAEG,GAAE,KAAKR,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,aAAQS,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,YAAMJ,KAAEG,GAAEC,EAAC,GAAEN,KAAEI,GAAEE,EAAC;AAAE,UAAG,CAACG,GAAEP,EAAC,KAAG,CAACM,GAAEN,EAAC,EAAE;AAAS,YAAMK,KAAEE,GAAEP,EAAC,EAAEF,EAAC,GAAEW,KAAEH,GAAEN,EAAC,EAAEF,EAAC;AAAE,UAAG,CAACW,MAAG,MAAIA,GAAE,OAAO;AAAS,UAAG,EAAEb,EAAC,EAAE;AAAO,YAAMc,KAAEL,GAAE,QAAQ;AAAE,aAAKK,GAAE,QAAQ,CAAC,KAAG;AAAC,cAAMV,KAAEU,GAAE,WAAW,GAAEf,KAAE,IAAIY,GAAEP,IAAEK,EAAC;AAAE,QAAAL,GAAE,QAAQ;AAAE,cAAMJ,MAAED,GAAE;AAAO,YAAGC,KAAE;AAAC,gBAAMI,KAAEJ,IAAE;AAAS,cAAGI,MAAGA,MAAGC,GAAE;AAAS,gBAAMN,KAAEC,IAAE;AAAS,cAAGD,MAAGA,MAAGI,GAAE;AAAA,QAAQ;AAAC,mBAAUQ,MAAKE,GAAE,CAAAF,GAAE,YAAYZ,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBK,IAAEL,IAAEC,IAAE;AAAC,UAAMW,KAAE,CAAC,GAAED,KAAE,KAAK,MAAM,qBAAqB;AAAE,QAAIT,IAAEC;AAAE,IAAAE,GAAE,SAAO,MAAIH,KAAES,GAAE,aAAaN,IAAE,KAAK,SAAQJ,EAAC,GAAEW,GAAE,KAAKV,EAAC;AAAG,eAAUE,MAAKJ,IAAE;AAAC,YAAMK,KAAEL,GAAEI,EAAC;AAAE,MAAAC,GAAE,OAAK,MAAIF,KAAEQ,GAAE,YAAY,KAAK,MAAM,SAAQP,IAAEC,IAAE,KAAK,SAAQJ,EAAC,GAAEW,GAAE,KAAKT,EAAC;AAAA,IAAE;AAAC,WAAOS;AAAA,EAAC;AAAA,EAAC,iBAAiBP,IAAE;AAAC,UAAML,KAAEK,GAAE,OAAQ,CAAAA,OAAGA,GAAE,YAAY,KAAG,KAAK,oBAAoBA,GAAE,MAAM,GAAG,CAAE;AAAE,eAAUJ,MAAKD,GAAE,CAAAC,GAAE,gBAAgBA,GAAE,WAAW;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,eAAeK,IAAE;AAAC,UAAML,KAAE,CAAC;AAAE,eAAUC,MAAK,OAAO,KAAKI,EAAC,GAAE;AAAC,YAAMO,KAAEP,GAAEJ,EAAC,GAAEU,KAAE,CAAC;AAAE,aAAKC,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,QAAC,KAAK,GAAE;AAAC,gBAAMP,KAAEO,GAAE,WAAW,GAAEZ,KAAE,IAAIK,GAAEA,EAAC;AAAE,UAAAA,GAAE,QAAQ,GAAEM,GAAEX,GAAE,IAAI,IAAEA;AAAE;AAAA,QAAK;AAAA,QAAC;AAAQ,UAAAY,GAAE,KAAK;AAAA,MAAC;AAAC,MAAAZ,GAAEC,EAAC,IAAEU;AAAA,IAAC;AAAC,WAAOX;AAAA,EAAC;AAAA,EAAC,cAAcK,IAAE;AAAC,YAAOA,GAAE,MAAK;AAAA,MAAC,KAAK,EAAE;AAAW,eAAO;AAAA,MAAK,KAAK,EAAE;AAAK,eAAO,KAAK,kBAAkBA,EAAC;AAAA,MAAE,KAAK,EAAE;AAAK,eAAO,KAAK,kBAAkBA,EAAC;AAAA,MAAE,KAAK,EAAE;AAAO,eAAO,KAAK,oBAAoBA,EAAC;AAAA,MAAE,KAAK,EAAE;AAAO,eAAO,KAAK,oBAAoBA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,WAAO,IAAIO,GAAEP,IAAE,KAAK,QAAO,KAAK,MAAM,qBAAqB,EAAE,eAAe,GAAE,IAAIJ,GAAEI,GAAE,aAAa,UAAU,CAAC,GAAE,IAAIJ,MAAE,IAAIO,GAAEH,GAAE,gBAAgB,UAAU,CAAC,GAAE,IAAIJ,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBI,IAAE;AAAC,WAAO,IAAIF,GAAEE,IAAE,KAAK,QAAO,KAAK,MAAM,qBAAqB,EAAE,eAAe,GAAE,IAAIH,GAAEG,GAAE,aAAa,UAAU,CAAC,GAAE,IAAIJ,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBI,IAAE;AAAC,WAAO,IAAIJ,GAAEI,IAAE,KAAK,QAAO,KAAK,MAAM,qBAAqB,EAAE,eAAe,GAAE,IAAIE,GAAEF,GAAE,eAAe,UAAU,CAAC,GAAE,IAAIJ,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBI,IAAE;AAAC,UAAML,KAAE,KAAK;AAAM,WAAO,IAAI,EAAEK,IAAE,KAAK,QAAO,IAAIS,GAAET,GAAE,aAAa,UAAU,CAAC,GAAE,IAAIJ,MAAE,IAAIa,GAAET,GAAE,aAAa,UAAU,CAAC,GAAE,IAAIJ,MAAED,GAAE,iBAAgBA,GAAE,qBAAqB,CAAC;AAAA,EAAC;AAAC;;;ACAtkK,IAAMgB,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEH,IAAE;AAAC,SAAK,SAAOI,GAAE,aAAY,KAAK,kBAAgB,IAAI,KAAE,KAAK,UAAQH,IAAE,KAAK,UAAQC,IAAE,KAAK,qBAAmBC,IAAE,KAAK,mBAAiBH;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,UAAQ,IAAG,KAAK,UAAQ,MAAK,KAAK,SAAOI,GAAE,aAAY,KAAK,qBAAmB;AAAA,EAAI;AAAA,EAAC,MAAM,MAAMC,IAAEF,IAAE;AAAC,UAAMH,KAAEG,MAAGA,GAAE;AAAO,QAAG,EAAEH,EAAC,GAAE;AAAC,YAAMC,KAAE,MAAI;AAAC,QAAAD,GAAE,oBAAoB,SAAQC,EAAC,GAAE,KAAK,SAAOG,GAAE;AAAA,MAAO;AAAE,MAAAJ,GAAE,iBAAiB,SAAQC,EAAC;AAAA,IAAC;AAAC,QAAIK;AAAE,UAAMC,KAAE,EAAC,iBAAgB,CAAC,GAAE,cAAa,KAAI;AAAE,QAAG;AAAC,MAAAD,KAAE,MAAM,KAAK,OAAOD,IAAEF,EAAC;AAAA,IAAC,SAAOK,IAAE;AAAC,UAAG,EAAEA,EAAC,EAAE,OAAMA;AAAE,aAAM,EAAC,QAAOD,IAAE,cAAa,CAAC,EAAC;AAAA,IAAC;AAAC,SAAK,SAAOH,GAAE;AAAM,UAAMK,KAAEF,GAAE,iBAAgBG,KAAE,CAAC;AAAE,eAAUT,MAAKK,GAAE,KAAGL,GAAE,YAAY,GAAE;AAAC,YAAMC,KAAED,GAAE,UAAU;AAAE,MAAAQ,GAAE,KAAKP,EAAC;AAAA,IAAC,MAAM,CAAAQ,GAAE,KAAKT,GAAE,MAAM,GAAG;AAAE,UAAMU,KAAE,CAAC,GAAGF,EAAC;AAAE,QAAIG,KAAE;AAAK,WAAOF,GAAE,SAAO,MAAIE,KAAE,YAAY,KAAKF,EAAC,GAAEC,GAAE,KAAKC,GAAE,MAAM,IAAGL,GAAE,eAAaK,IAAE,EAAC,QAAOL,IAAE,cAAaI,GAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,SAAK,SAAOP,GAAE;AAAA,EAAO;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,mBAAmB,UAAU;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,WAAO,KAAK;AAAA,EAAkB;AAAA,EAAC,MAAM,OAAOH,IAAEC,IAAE;AAAC,UAAMG,KAAEJ,GAAE;AAAyB,QAAG,MAAI,OAAO,KAAKI,EAAC,EAAE,OAAO,QAAM,CAAC;AAAE,SAAK,SAAOD,GAAE;AAAS,WAAO,IAAI,EAAEC,IAAE,MAAKH,GAAE,QAAO,KAAK,kBAAiBD,GAAE,cAAc,EAAE,MAAMC,EAAC;AAAA,EAAC;AAAC;;;ACArsC,IAAMW,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,cAAY,CAAC,GAAE,KAAK,aAAW,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,KAAK,cAAY,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,YAAW;AAJtU;AAIuU,aAAO,UAAK,qBAAL,mBAAuB,WAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBC,IAAEC,IAAE;AAAC,UAAK,EAAC,KAAIF,GAAC,IAAEC,IAAEE,KAAE,CAAC;AAAE,eAAUC,MAAK,OAAO,KAAKH,GAAE,wBAAwB,GAAE;AAAC,YAAMI,KAAEJ,GAAE,yBAAyBG,EAAC;AAAE,MAAAD,GAAEC,EAAC,IAAEC,GAAE;AAAA,IAAM;AAAC,UAAMC,KAAE,IAAIC,GAAEP,IAAEG,IAAE,MAAK,KAAK,gBAAgB;AAAE,QAAG;AAAC,aAAO,MAAMG,GAAE,MAAML,IAAEC,EAAC;AAAA,IAAC,SAAOM,IAAE;AAAC,UAAGF,GAAE,YAAY,GAAEA,GAAE,QAAQ,GAAE,CAAC,EAAEE,EAAC,EAAE,OAAMA;AAAE,aAAO;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,YAAYJ,IAAE;AAAC,QAAG,CAACA,MAAG,MAAIA,GAAE,UAAQ,CAAC,KAAK,iBAAiB;AAAO,UAAMC,KAAE,KAAK;AAAiB,eAAUH,MAAKE,IAAE;AAAC,YAAMA,KAAEF,GAAE,MAAKF,KAAEE,GAAE;AAAK,cAAOE,IAAE;AAAA,QAAC,KAAK,EAAE;AAAgB,UAAAC,GAAE,mBAAmBL,GAAE,OAAMA,GAAE,KAAK;AAAE;AAAA,QAAM,KAAK,EAAE;AAAe,UAAAK,GAAE,oBAAoBL,GAAE,OAAMA,GAAE,MAAM;AAAE;AAAA,QAAM,KAAK,EAAE;AAAc,UAAAK,GAAE,iBAAiBL,GAAE,KAAK;AAAE;AAAA,QAAM,KAAK,EAAE;AAAc,UAAAK,GAAE,cAAcL,GAAE,OAAMA,GAAE,KAAK;AAAE;AAAA,QAAM,KAAK,EAAE;AAAgB,eAAK,cAAY,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,SAASI,IAAE;AAAC,SAAK,mBAAiB,IAAII,GAAEJ,EAAC,GAAE,KAAK,cAAY,CAAC,GAAE,KAAK,aAAW,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEH,IAAEI,IAAE;AAAC,UAAMH,KAAE,CAAC,GAAEF,KAAE,KAAK;AAAY,eAAUG,MAAKC,IAAE;AAAC,iBAASJ,GAAEG,GAAE,IAAI,KAAGD,GAAE,KAAKC,EAAC;AAAA,IAAC;AAAC,WAAO,MAAID,GAAE,SAAO,QAAQ,QAAQ,IAAED,GAAE,OAAO,cAAaC,IAAE,EAAC,QAAOG,MAAGA,GAAE,OAAM,CAAC,EAAE,KAAM,CAAAD,OAAG;AAAC,iBAAUH,MAAKG,IAAE;AAAC,cAAMC,KAAED,GAAEH,EAAC;AAAE,QAAAD,GAAEC,EAAC,IAAEI;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,YAAYD,IAAEH,IAAEI,IAAEH,IAAEF,IAAE;AAAC,UAAMG,KAAE,CAAC;AAAE,QAAIG,KAAE,KAAK,WAAWL,EAAC;AAAE,WAAOK,KAAED,GAAE,QAAS,CAAAD,OAAG;AAAC,MAAAE,GAAEF,EAAC,KAAGD,GAAE,KAAKC,EAAC;AAAA,IAAC,CAAE,KAAGE,KAAE,KAAK,WAAWL,EAAC,IAAE,CAAC,GAAEI,GAAE,QAAS,CAAAD,OAAGD,GAAE,KAAKC,EAAC,CAAE,IAAG,MAAID,GAAE,SAAO,QAAQ,QAAQ,IAAED,GAAE,OAAO,aAAY,EAAC,QAAOE,IAAE,MAAKH,IAAE,YAAWE,GAAC,GAAEH,EAAC,EAAE,KAAM,CAAAI,OAAG;AAAC,eAAQH,KAAE,GAAEA,KAAEG,GAAE,QAAOH,KAAI,CAAAG,GAAEH,EAAC,MAAIK,GAAEL,EAAC,IAAEG,GAAEH,EAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcG,IAAE;AAAC,WAAO,KAAK,WAAWA,EAAC;AAAA,EAAC;AAAC;", "names": ["e", "c", "a", "c", "h", "a", "t", "e", "i", "s", "o", "l", "r", "n", "m", "f", "p", "g", "y", "x", "u", "_", "I", "p", "T", "e", "t", "i", "n", "s", "f", "o", "a", "l", "I", "h", "r", "c", "g", "u", "m", "x", "y", "b", "_", "E", "N", "A", "L", "B", "j", "C", "w", "t", "e", "s", "o", "r", "a", "n", "i", "l", "c", "h", "r", "t", "s", "e", "e", "t", "s", "o", "t", "r", "u", "h", "a", "n", "e", "p", "i", "c", "l", "m", "t", "e", "s", "r", "t", "i", "c", "s", "e", "n", "l", "s", "t", "e", "i", "r", "n", "o", "l", "a", "f", "u", "h", "x", "c", "_", "y", "g", "p", "I", "B", "m", "s", "n", "t", "a", "o", "c", "r", "l", "e", "i", "u", "f", "h", "p", "g", "y", "_", "x", "m", "I", "L", "B", "b", "e", "t", "n", "i", "s", "o", "r", "l", "a", "h", "x", "c", "g", "f", "m", "u", "p", "_", "I", "y", "T", "w", "B", "C", "d", "A", "L", "I", "T", "w", "t", "r", "o", "n", "c", "e", "l", "a", "u", "f", "p", "i", "s", "I", "h", "m", "a", "t", "e", "r", "I", "s", "n", "l", "y", "o", "u", "h", "c", "o", "t", "r", "i", "e", "s", "n", "a", "l"]}