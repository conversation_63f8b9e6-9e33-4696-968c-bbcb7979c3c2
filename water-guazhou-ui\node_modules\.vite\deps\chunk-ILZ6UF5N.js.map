{"version": 3, "sources": ["../../@arcgis/core/chunks/NativeLine.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ShaderOutput as e}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as o}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{Transform as i}from\"../views/3d/webgl-engine/core/shaderLibrary/Transform.glsl.js\";import{VertexColor as r}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/VertexColor.glsl.js\";import{OutputHighlight as t}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputHighlight.glsl.js\";import{LineStipple as l,computePixelSize as s}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/LineStipple.glsl.js\";import{symbol<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as n}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{addProjViewLocalOrigin as a}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float4PassUniform as d}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{FloatPassUniform as p}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as c}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as g}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{VertexAttribute as m}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";function u(u){const v=new g,{vertex:f,fragment:h}=v;return v.include(i,u),v.include(r,u),v.include(l,u),a(f,u),u.stippleEnabled&&(v.attributes.add(m.UV0,\"vec2\"),v.attributes.add(m.AUXPOS1,\"vec3\"),f.uniforms.add(new d(\"viewport\",((e,o)=>o.camera.fullViewport)))),v.attributes.add(m.POSITION,\"vec3\"),v.varyings.add(\"vpos\",\"vec3\"),f.code.add(c`void main(void) {\nvpos = position;\nforwardNormalizedVertexColor();\ngl_Position = transformPosition(proj, view, vpos);`),u.stippleEnabled&&(f.code.add(c`vec4 vpos2 = transformPosition(proj, view, auxpos1);\nvec2 ndcToPixel = viewport.zw * 0.5;\nfloat lineSegmentPixelSize = length((vpos2.xy / vpos2.w - gl_Position.xy / gl_Position.w) * ndcToPixel);`),u.draped?f.uniforms.add(new p(\"worldToScreenRatio\",((e,o)=>1/o.screenToPCSRatio))):f.code.add(c`vec3 segmentCenter = (position + auxpos1) * 0.5;\nfloat worldToScreenRatio = computeWorldToScreenRatio(segmentCenter);`),f.code.add(c`float discreteWorldToScreenRatio = discretizeWorldToScreenRatio(worldToScreenRatio);`),u.draped?f.code.add(c`float startPseudoScreen = uv0.y * discreteWorldToScreenRatio - mix(0.0, lineSegmentPixelSize, uv0.x);\nfloat segmentLengthPseudoScreen = lineSegmentPixelSize;`):f.code.add(c`float segmentLengthRender = length(position - auxpos1);\nfloat startPseudoScreen = mix(uv0.y, uv0.y - segmentLengthRender, uv0.x) * discreteWorldToScreenRatio;\nfloat segmentLengthPseudoScreen = segmentLengthRender * discreteWorldToScreenRatio;`),f.uniforms.add(new p(\"stipplePatternPixelSize\",(e=>s(e)))),f.code.add(c`vec2 stippleDistanceLimits = computeStippleDistanceLimits(startPseudoScreen, segmentLengthPseudoScreen, lineSegmentPixelSize, stipplePatternPixelSize);\nvStippleDistance = mix(stippleDistanceLimits.x, stippleDistanceLimits.y, uv0.x);\nvStippleDistance *= gl_Position.w;`)),f.code.add(c`}`),u.output===e.Highlight&&v.include(t,u),v.include(o,u),h.uniforms.add(new p(\"alphaCoverage\",((e,o)=>Math.min(1,e.width*o.camera.pixelRatio)))),u.hasVertexColors||h.uniforms.add(new d(\"constantColor\",(e=>e.color))),h.code.add(c`\n  void main() {\n    discardBySlice(vpos);\n\n    vec4 color = ${u.hasVertexColors?\"vColor\":\"constantColor\"};\n\n    float stippleAlpha = getStippleAlpha();\n    discardByStippleAlpha(stippleAlpha, stippleAlphaColorDiscard);\n\n    vec4 finalColor = blendStipple(vec4(color.rgb, color.a * alphaCoverage), stippleAlpha);\n\n    ${u.output===e.ObjectAndLayerIdColor?c`finalColor.a = 1.0;`:\"\"}\n\n    if (finalColor.a < ${c.float(n)}) {\n      discard;\n    }\n\n    ${u.output===e.Color?c`gl_FragColor = highlightSlice(finalColor, vpos);`:\"\"}\n    ${u.output===e.Highlight?c`outputHighlight();`:\"\"}\n  }\n  `),v}const v=Object.freeze(Object.defineProperty({__proto__:null,build:u},Symbol.toStringTag,{value:\"Module\"}));export{v as N,u as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgvC,SAASA,GAAEA,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAE,EAAC,QAAO,GAAE,UAASC,GAAC,IAAEF;AAAE,SAAOA,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQG,IAAEJ,EAAC,GAAEC,GAAE,QAAQD,IAAEA,EAAC,GAAE,EAAE,GAAEA,EAAC,GAAEA,GAAE,mBAAiBC,GAAE,WAAW,IAAI,EAAE,KAAI,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,SAAQ,MAAM,GAAE,EAAE,SAAS,IAAI,IAAI,EAAE,YAAY,CAACG,IAAEF,OAAIA,GAAE,OAAO,YAAa,CAAC,IAAGD,GAAE,WAAW,IAAI,EAAE,UAAS,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,mDAGhhD,GAAED,GAAE,mBAAiB,EAAE,KAAK,IAAI;AAAA;AAAA,yGAEsB,GAAEA,GAAE,SAAO,EAAE,SAAS,IAAI,IAAIE,GAAE,sBAAsB,CAACE,IAAEF,OAAI,IAAEA,GAAE,gBAAiB,CAAC,IAAE,EAAE,KAAK,IAAI;AAAA,qEACpI,GAAE,EAAE,KAAK,IAAI,uFAAuF,GAAEF,GAAE,SAAO,EAAE,KAAK,IAAI;AAAA,wDACvI,IAAE,EAAE,KAAK,IAAI;AAAA;AAAA,oFAEe,GAAE,EAAE,SAAS,IAAI,IAAIE,GAAE,2BAA2B,CAAAE,OAAG,EAAEA,EAAC,CAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,mCAEzH,IAAG,EAAE,KAAK,IAAI,IAAI,GAAEJ,GAAE,WAAS,EAAE,aAAWC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEG,GAAE,SAAS,IAAI,IAAID,GAAE,iBAAiB,CAACE,IAAEF,OAAI,KAAK,IAAI,GAAEE,GAAE,QAAMF,GAAE,OAAO,UAAU,CAAE,CAAC,GAAEF,GAAE,mBAAiBG,GAAE,SAAS,IAAI,IAAI,EAAE,iBAAiB,CAAAC,OAAGA,GAAE,KAAM,CAAC,GAAED,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,mBAIpQH,GAAE,kBAAgB,WAAS,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOvDA,GAAE,WAAS,EAAE,wBAAsB,yBAAuB,EAAE;AAAA;AAAA,yBAEzC,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,MAI7BA,GAAE,WAAS,EAAE,QAAM,sDAAoD,EAAE;AAAA,MACzEA,GAAE,WAAS,EAAE,YAAU,wBAAsB,EAAE;AAAA;AAAA,GAElD,GAAEC;AAAC;AAAC,IAAMA,KAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAMD,GAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["u", "v", "o", "h", "e"]}