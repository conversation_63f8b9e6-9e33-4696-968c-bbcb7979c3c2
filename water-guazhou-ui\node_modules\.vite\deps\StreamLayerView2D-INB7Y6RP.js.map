{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/StreamLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import t from\"../../../core/Error.js\";import{handlesGroup as r}from\"../../../core/handleUtils.js\";import{isNone as s,isSome as o}from\"../../../core/maybe.js\";import{watch as i}from\"../../../core/reactiveUtils.js\";import{property as n}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as a}from\"../../../core/accessorSupport/decorators/subclass.js\";import c from\"../../../rest/support/FeatureSet.js\";import p from\"./FeatureLayerView2D.js\";import{toJSONGeometryType as l}from\"./support/util.js\";function u(e,t){if(s(e)&&s(t))return null;const r={};return o(t)&&(r.geometry=t.toJSON()),o(e)&&(r.where=e),r}let m=class extends p{constructor(){super(...arguments),this._enabledEventTypes=new Set,this._isUserPaused=!1,this.errorString=null,this.connectionStatus=\"disconnected\"}initialize(){this.addHandles([i((()=>this.layer.customParameters),(e=>this._proxy.updateCustomParameters(e))),this.layer.on(\"send-message-to-socket\",(e=>this._proxy.sendMessageToSocket(e))),this.layer.on(\"send-message-to-client\",(e=>this._proxy.sendMessageToClient(e))),i((()=>this.layer.purgeOptions),(()=>this._update())),i((()=>this.suspended),(e=>{e?this._proxy.pauseStream():this._isUserPaused||this._proxy.resumeStream()}))],\"constructor\")}get connectionError(){if(this.errorString)return new t(\"stream-controller\",this.errorString)}pause(){this._isUserPaused=!0,this._proxy.pauseStream()}resume(){this._isUserPaused=!1,this._proxy.resumeStream()}on(e,t){if(Array.isArray(e))return r(e.map((e=>this.on(e,t))));const s=[\"data-received\",\"message-received\"].includes(e);s&&(this._enabledEventTypes.add(e),this._proxy.enableEvent(e,!0));const o=super.on(e,t),i=this;return{remove(){o.remove(),s&&(i._proxy.closed||i.hasEventListener(e)||i._proxy.enableEvent(e,!1))}}}queryLatestObservations(e,r){if(!(this.layer.timeInfo?.endField||this.layer.timeInfo?.startField||this.layer.timeInfo?.trackIdField))throw new t(\"streamlayer-no-timeField\",\"queryLatestObservation can only be used with services that define a TrackIdField\");return this._proxy.queryLatestObservations(this._cleanUpQuery(e),r).then((e=>{const t=c.fromJSON(e);return t.features.forEach((e=>{e.layer=this.layer,e.sourceLayer=this.layer})),t}))}detach(){super.detach(),this.connectionStatus=\"disconnected\"}_createClientOptions(){return{...super._createClientOptions(),setProperty:e=>{this.set(e.propertyName,e.value)}}}_createTileRendererHash(e){const t=`${JSON.stringify(this.layer.purgeOptions)}.${JSON.stringify(u(this.layer.definitionExpression,this.layer.geometryDefinition))})`;return super._createTileRendererHash(e)+t}async _createServiceOptions(){const e=this.layer,{objectIdField:t}=e,r=e.fields.map((e=>e.toJSON())),s=l(e.geometryType),o=e.timeInfo&&e.timeInfo.toJSON()||null,i=e.spatialReference?e.spatialReference.toJSON():null;return{type:\"stream\",fields:r,geometryType:s,objectIdField:t,timeInfo:o,source:this.layer.parsedUrl,serviceFilter:u(this.layer.definitionExpression,this.layer.geometryDefinition),purgeOptions:this.layer.purgeOptions.toJSON(),enabledEventTypes:Array.from(this._enabledEventTypes.values()),spatialReference:i,maxReconnectionAttempts:this.layer.maxReconnectionAttempts,maxReconnectionInterval:this.layer.maxReconnectionInterval,updateInterval:this.layer.updateInterval,customParameters:e.customParameters}}};e([n()],m.prototype,\"errorString\",void 0),e([n({readOnly:!0})],m.prototype,\"connectionError\",null),e([n()],m.prototype,\"connectionStatus\",void 0),m=e([a(\"esri.views.2d.layers.StreamLayerView2D\")],m);const y=m;export{y as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+oB,SAAS,EAAEA,IAAEC,IAAE;AAAC,MAAG,EAAED,EAAC,KAAG,EAAEC,EAAC,EAAE,QAAO;AAAK,QAAMC,KAAE,CAAC;AAAE,SAAO,EAAED,EAAC,MAAIC,GAAE,WAASD,GAAE,OAAO,IAAG,EAAED,EAAC,MAAIE,GAAE,QAAMF,KAAGE;AAAC;AAAC,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,qBAAmB,oBAAI,OAAI,KAAK,gBAAc,OAAG,KAAK,cAAY,MAAK,KAAK,mBAAiB;AAAA,EAAc;AAAA,EAAC,aAAY;AAAC,SAAK,WAAW,CAAC,EAAG,MAAI,KAAK,MAAM,kBAAmB,CAAAF,OAAG,KAAK,OAAO,uBAAuBA,EAAC,CAAE,GAAE,KAAK,MAAM,GAAG,0BAA0B,CAAAA,OAAG,KAAK,OAAO,oBAAoBA,EAAC,CAAE,GAAE,KAAK,MAAM,GAAG,0BAA0B,CAAAA,OAAG,KAAK,OAAO,oBAAoBA,EAAC,CAAE,GAAE,EAAG,MAAI,KAAK,MAAM,cAAe,MAAI,KAAK,QAAQ,CAAE,GAAE,EAAG,MAAI,KAAK,WAAY,CAAAA,OAAG;AAAC,MAAAA,KAAE,KAAK,OAAO,YAAY,IAAE,KAAK,iBAAe,KAAK,OAAO,aAAa;AAAA,IAAC,CAAE,CAAC,GAAE,aAAa;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,QAAG,KAAK,YAAY,QAAO,IAAI,EAAE,qBAAoB,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,gBAAc,MAAG,KAAK,OAAO,YAAY;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,gBAAc,OAAG,KAAK,OAAO,aAAa;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,QAAG,MAAM,QAAQD,EAAC,EAAE,QAAOE,GAAEF,GAAE,IAAK,CAAAA,OAAG,KAAK,GAAGA,IAAEC,EAAC,CAAE,CAAC;AAAE,UAAME,KAAE,CAAC,iBAAgB,kBAAkB,EAAE,SAASH,EAAC;AAAE,IAAAG,OAAI,KAAK,mBAAmB,IAAIH,EAAC,GAAE,KAAK,OAAO,YAAYA,IAAE,IAAE;AAAG,UAAM,IAAE,MAAM,GAAGA,IAAEC,EAAC,GAAE,IAAE;AAAK,WAAM,EAAC,SAAQ;AAAC,QAAE,OAAO,GAAEE,OAAI,EAAE,OAAO,UAAQ,EAAE,iBAAiBH,EAAC,KAAG,EAAE,OAAO,YAAYA,IAAE,KAAE;AAAA,IAAE,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAEE,IAAE;AAJ34D;AAI44D,QAAG,IAAE,UAAK,MAAM,aAAX,mBAAqB,eAAU,UAAK,MAAM,aAAX,mBAAqB,iBAAY,UAAK,MAAM,aAAX,mBAAqB,eAAc,OAAM,IAAI,EAAE,4BAA2B,kFAAkF;AAAE,WAAO,KAAK,OAAO,wBAAwB,KAAK,cAAcF,EAAC,GAAEE,EAAC,EAAE,KAAM,CAAAF,OAAG;AAAC,YAAMC,KAAE,EAAE,SAASD,EAAC;AAAE,aAAOC,GAAE,SAAS,QAAS,CAAAD,OAAG;AAAC,QAAAA,GAAE,QAAM,KAAK,OAAMA,GAAE,cAAY,KAAK;AAAA,MAAK,CAAE,GAAEC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAM,OAAO,GAAE,KAAK,mBAAiB;AAAA,EAAc;AAAA,EAAC,uBAAsB;AAAC,WAAM,EAAC,GAAG,MAAM,qBAAqB,GAAE,aAAY,CAAAD,OAAG;AAAC,WAAK,IAAIA,GAAE,cAAaA,GAAE,KAAK;AAAA,IAAC,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAE;AAAC,UAAMC,KAAE,GAAG,KAAK,UAAU,KAAK,MAAM,YAAY,CAAC,IAAI,KAAK,UAAU,EAAE,KAAK,MAAM,sBAAqB,KAAK,MAAM,kBAAkB,CAAC,CAAC;AAAI,WAAO,MAAM,wBAAwBD,EAAC,IAAEC;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAuB;AAAC,UAAMD,KAAE,KAAK,OAAM,EAAC,eAAcC,GAAC,IAAED,IAAEE,KAAEF,GAAE,OAAO,IAAK,CAAAA,OAAGA,GAAE,OAAO,CAAE,GAAEG,KAAEH,GAAEA,GAAE,YAAY,GAAE,IAAEA,GAAE,YAAUA,GAAE,SAAS,OAAO,KAAG,MAAK,IAAEA,GAAE,mBAAiBA,GAAE,iBAAiB,OAAO,IAAE;AAAK,WAAM,EAAC,MAAK,UAAS,QAAOE,IAAE,cAAaC,IAAE,eAAcF,IAAE,UAAS,GAAE,QAAO,KAAK,MAAM,WAAU,eAAc,EAAE,KAAK,MAAM,sBAAqB,KAAK,MAAM,kBAAkB,GAAE,cAAa,KAAK,MAAM,aAAa,OAAO,GAAE,mBAAkB,MAAM,KAAK,KAAK,mBAAmB,OAAO,CAAC,GAAE,kBAAiB,GAAE,yBAAwB,KAAK,MAAM,yBAAwB,yBAAwB,KAAK,MAAM,yBAAwB,gBAAe,KAAK,MAAM,gBAAe,kBAAiBD,GAAE,iBAAgB;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,wCAAwC,CAAC,GAAE,CAAC;AAAE,IAAMI,KAAE;", "names": ["e", "t", "r", "s", "y"]}