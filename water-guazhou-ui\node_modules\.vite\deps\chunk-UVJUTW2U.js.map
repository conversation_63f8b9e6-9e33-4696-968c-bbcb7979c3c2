{"version": 3, "sources": ["../../@arcgis/core/layers/support/LayerFloorInfo.js", "../../@arcgis/core/layers/support/serviceCapabilitiesUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import e from\"../../core/Collection.js\";import{JSONSupport as r}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";var l;let i=l=class extends r{constructor(o){super(o),this.floorField=null,this.viewAllMode=!1,this.viewAllLevelIds=new e}clone(){return new l({floorField:this.floorField,viewAllMode:this.viewAllMode,viewAllLevelIds:this.viewAllLevelIds})}};o([s({type:String,json:{write:!0}})],i.prototype,\"floorField\",void 0),o([s({json:{read:!1,write:!1}})],i.prototype,\"viewAllMode\",void 0),o([s({json:{read:!1,write:!1}})],i.prototype,\"viewAllLevelIds\",void 0),i=l=o([t(\"esri.layers.support.LayerFloorInfo\")],i);const p=i;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../core/has.js\";import{isSome as t}from\"../../core/maybe.js\";import{parse as s,isHostedAgolService as e}from\"./arcgisLayerUrl.js\";const r={name:\"supportsName\",size:\"supportsSize\",contentType:\"supportsContentType\",keywords:\"supportsKeywords\",exifInfo:\"supportsExifInfo\"};function p(t,s,e){return!!(t&&t.hasOwnProperty(s)?t[s]:e)}function o(t,s,e){return t&&t.hasOwnProperty(s)?t[s]:e}function u(t){const s=t?.supportedSpatialAggregationStatistics?.map((t=>t.toLowerCase()));return{envelope:!!s?.includes(\"envelopeaggregate\"),centroid:!!s?.includes(\"centroidaggregate\"),convexHull:!!s?.includes(\"convexhullaggregate\")}}function a(t,s){const e=t?.supportedOperationsWithCacheHint?.map((t=>t.toLowerCase()));return!!e?.includes(s.toLowerCase())}function n(t,s){return{analytics:i(t),attachment:c(t),data:d(t),metadata:l(t),operations:y(t.capabilities,t,s),query:m(t,s),queryRelated:h(t),queryTopFeatures:g(t),editing:C(t)}}function i(t){return{supportsCacheHint:a(t.advancedQueryCapabilities,\"queryAnalytics\")}}function c(t){const s=t.attachmentProperties,e={supportsName:!1,supportsSize:!1,supportsContentType:!1,supportsKeywords:!1,supportsExifInfo:!1,supportsCacheHint:a(t.advancedQueryCapabilities,\"queryAttachments\"),supportsResize:p(t,\"supportsAttachmentsResizing\",!1)};return s&&Array.isArray(s)&&s.forEach((t=>{const s=r[t.name];s&&(e[s]=!!t.isEnabled)})),e}function d(t){return{isVersioned:p(t,\"isDataVersioned\",!1),supportsAttachment:p(t,\"hasAttachments\",!1),supportsM:p(t,\"hasM\",!1),supportsZ:p(t,\"hasZ\",!1)}}function l(t){return{supportsAdvancedFieldProperties:p(t,\"supportsFieldDescriptionProperty\",!1)}}function y(e,r,o){const u=e?e.toLowerCase().split(\",\").map((t=>t.trim())):[],a=o?s(o):null,n=u.includes(t(a)&&\"MapServer\"===a.serverType?\"data\":\"query\"),i=u.includes(\"editing\")&&!r.datesInUnknownTimezone;let c=i&&u.includes(\"create\"),d=i&&u.includes(\"delete\"),l=i&&u.includes(\"update\");const y=u.includes(\"changetracking\"),m=r.advancedQueryCapabilities;return i&&!(c||d||l)&&(c=d=l=!0),{supportsCalculate:p(r,\"supportsCalculate\",!1),supportsTruncate:p(r,\"supportsTruncate\",!1),supportsValidateSql:p(r,\"supportsValidateSql\",!1),supportsAdd:c,supportsDelete:d,supportsEditing:i,supportsChangeTracking:y,supportsQuery:n,supportsQueryAnalytics:p(m,\"supportsQueryAnalytic\",!1),supportsQueryAttachments:p(m,\"supportsQueryAttachments\",!1),supportsQueryTopFeatures:p(m,\"supportsTopFeaturesQuery\",!1),supportsResizeAttachments:p(r,\"supportsAttachmentsResizing\",!1),supportsSync:u.includes(\"sync\"),supportsUpdate:l,supportsExceedsLimitStatistics:p(r,\"supportsExceedsLimitStatistics\",!1)}}function m(t,s){const r=t.advancedQueryCapabilities,n=t.ownershipBasedAccessControlForFeatures,i=t.archivingInfo,c=t.currentVersion,d=s?.includes(\"MapServer\"),l=!d||c>=has(\"mapserver-pbf-version-support\"),y=e(s),m=new Set((t.supportedQueryFormats??\"\").split(\",\").map((t=>t.toLowerCase().trim())));return{supportsStatistics:p(r,\"supportsStatistics\",t.supportsStatistics),supportsPercentileStatistics:p(r,\"supportsPercentileStatistics\",!1),supportsSpatialAggregationStatistics:p(r,\"supportsSpatialAggregationStatistics\",!1),supportedSpatialAggregationStatistics:u(r),supportsCentroid:p(r,\"supportsReturningGeometryCentroid\",!1),supportsDistance:p(r,\"supportsQueryWithDistance\",!1),supportsDistinct:p(r,\"supportsDistinct\",t.supportsAdvancedQueries),supportsExtent:p(r,\"supportsReturningQueryExtent\",!1),supportsGeometryProperties:p(r,\"supportsReturningGeometryProperties\",!1),supportsHavingClause:p(r,\"supportsHavingClause\",!1),supportsOrderBy:p(r,\"supportsOrderBy\",t.supportsAdvancedQueries),supportsPagination:p(r,\"supportsPagination\",!1),supportsQuantization:p(t,\"supportsCoordinatesQuantization\",!1),supportsQuantizationEditMode:p(t,\"supportsQuantizationEditMode\",!1),supportsQueryGeometry:p(t,\"supportsReturningQueryGeometry\",!1),supportsResultType:p(r,\"supportsQueryWithResultType\",!1),supportsMaxRecordCountFactor:p(r,\"supportsMaxRecordCountFactor\",!1),supportsSqlExpression:p(r,\"supportsSqlExpression\",!1),supportsStandardizedQueriesOnly:p(t,\"useStandardizedQueries\",!1),supportsTopFeaturesQuery:p(r,\"supportsTopFeaturesQuery\",!1),supportsQueryByOthers:p(n,\"allowOthersToQuery\",!0),supportsHistoricMoment:p(i,\"supportsQueryWithHistoricMoment\",!1),supportsFormatPBF:l&&m.has(\"pbf\"),supportsDisjointSpatialRelationship:p(r,\"supportsDisjointSpatialRel\",!1),supportsCacheHint:p(r,\"supportsQueryWithCacheHint\",!1)||a(r,\"query\"),supportsDefaultSpatialReference:p(r,\"supportsDefaultSR\",!1),supportsCompactGeometry:y,supportsFullTextSearch:p(r,\"supportsFullTextSearch\",!1),maxRecordCountFactor:o(t,\"maxRecordCountFactor\",void 0),maxRecordCount:o(t,\"maxRecordCount\",void 0),standardMaxRecordCount:o(t,\"standardMaxRecordCount\",void 0),tileMaxRecordCount:o(t,\"tileMaxRecordCount\",void 0)}}function h(t){const s=t.advancedQueryCapabilities,e=p(s,\"supportsAdvancedQueryRelated\",!1);return{supportsPagination:p(s,\"supportsQueryRelatedPagination\",!1),supportsCount:e,supportsOrderBy:e,supportsCacheHint:a(s,\"queryRelated\")}}function g(t){return{supportsCacheHint:a(t.advancedQueryCapabilities,\"queryTopFilter\")}}function C(t){const s=t.ownershipBasedAccessControlForFeatures;return{supportsGeometryUpdate:p(t,\"allowGeometryUpdates\",!0),supportsGlobalId:p(t,\"supportsApplyEditsWithGlobalIds\",!1),supportsReturnServiceEditsInSourceSpatialReference:p(t,\"supportsReturnServiceEditsInSourceSR\",!1),supportsRollbackOnFailure:p(t,\"supportsRollbackOnFailureParameter\",!1),supportsUpdateWithoutM:p(t,\"allowUpdateWithoutMValues\",!1),supportsUploadWithItemId:p(t,\"supportsAttachmentsByUploadId\",!1),supportsDeleteByAnonymous:p(s,\"allowAnonymousToDelete\",!0),supportsDeleteByOthers:p(s,\"allowOthersToDelete\",!0),supportsUpdateByAnonymous:p(s,\"allowAnonymousToUpdate\",!0),supportsUpdateByOthers:p(s,\"allowOthersToUpdate\",!0)}}export{n as getFeatureLayerCapabilities};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAI0X,IAAIA;AAAE,IAAI,IAAEA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,MAAK,KAAK,cAAY,OAAG,KAAK,kBAAgB,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAID,GAAE,EAAC,YAAW,KAAK,YAAW,aAAY,KAAK,aAAY,iBAAgB,KAAK,gBAAe,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,IAAEA,KAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACAjuB,IAAME,KAAE,EAAC,MAAK,gBAAe,MAAK,gBAAe,aAAY,uBAAsB,UAAS,oBAAmB,UAAS,mBAAkB;AAAE,SAASC,GAAE,GAAE,GAAEC,IAAE;AAAC,SAAM,CAAC,EAAE,KAAG,EAAE,eAAe,CAAC,IAAE,EAAE,CAAC,IAAEA;AAAE;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE;AAAC,SAAO,KAAG,EAAE,eAAe,CAAC,IAAE,EAAE,CAAC,IAAEA;AAAC;AAAC,SAAS,EAAE,GAAE;AAJ/Z;AAIga,QAAM,KAAE,4BAAG,0CAAH,mBAA0C,IAAK,CAAAC,OAAGA,GAAE,YAAY;AAAI,SAAM,EAAC,UAAS,CAAC,EAAC,uBAAG,SAAS,uBAAqB,UAAS,CAAC,EAAC,uBAAG,SAAS,uBAAqB,YAAW,CAAC,EAAC,uBAAG,SAAS,wBAAsB;AAAC;AAAC,SAASC,GAAE,GAAE,GAAE;AAJ3oB;AAI4oB,QAAMF,MAAE,4BAAG,qCAAH,mBAAqC,IAAK,CAAAC,OAAGA,GAAE,YAAY;AAAI,SAAM,CAAC,EAACD,MAAA,gBAAAA,GAAG,SAAS,EAAE,YAAY;AAAE;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAM,EAAC,WAAUG,GAAE,CAAC,GAAE,YAAW,EAAE,CAAC,GAAE,MAAKC,GAAE,CAAC,GAAE,UAASC,GAAE,CAAC,GAAE,YAAWC,GAAE,EAAE,cAAa,GAAE,CAAC,GAAE,OAAM,EAAE,GAAE,CAAC,GAAE,cAAa,EAAE,CAAC,GAAE,kBAAiBC,GAAE,CAAC,GAAE,SAAQ,EAAE,CAAC,EAAC;AAAC;AAAC,SAASJ,GAAE,GAAE;AAAC,SAAM,EAAC,mBAAkBD,GAAE,EAAE,2BAA0B,gBAAgB,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,sBAAqBF,KAAE,EAAC,cAAa,OAAG,cAAa,OAAG,qBAAoB,OAAG,kBAAiB,OAAG,kBAAiB,OAAG,mBAAkBE,GAAE,EAAE,2BAA0B,kBAAkB,GAAE,gBAAeH,GAAE,GAAE,+BAA8B,KAAE,EAAC;AAAE,SAAO,KAAG,MAAM,QAAQ,CAAC,KAAG,EAAE,QAAS,CAAAE,OAAG;AAAC,UAAMO,KAAEV,GAAEG,GAAE,IAAI;AAAE,IAAAO,OAAIR,GAAEQ,EAAC,IAAE,CAAC,CAACP,GAAE;AAAA,EAAU,CAAE,GAAED;AAAC;AAAC,SAASI,GAAE,GAAE;AAAC,SAAM,EAAC,aAAYL,GAAE,GAAE,mBAAkB,KAAE,GAAE,oBAAmBA,GAAE,GAAE,kBAAiB,KAAE,GAAE,WAAUA,GAAE,GAAE,QAAO,KAAE,GAAE,WAAUA,GAAE,GAAE,QAAO,KAAE,EAAC;AAAC;AAAC,SAASM,GAAE,GAAE;AAAC,SAAM,EAAC,iCAAgCN,GAAE,GAAE,oCAAmC,KAAE,EAAC;AAAC;AAAC,SAASO,GAAEN,IAAEF,IAAEW,IAAE;AAAC,QAAMC,KAAEV,KAAEA,GAAE,YAAY,EAAE,MAAM,GAAG,EAAE,IAAK,OAAG,EAAE,KAAK,CAAE,IAAE,CAAC,GAAEE,KAAEO,KAAE,EAAEA,EAAC,IAAE,MAAKE,KAAED,GAAE,SAAS,EAAER,EAAC,KAAG,gBAAcA,GAAE,aAAW,SAAO,OAAO,GAAEC,KAAEO,GAAE,SAAS,SAAS,KAAG,CAACZ,GAAE;AAAuB,MAAIc,KAAET,MAAGO,GAAE,SAAS,QAAQ,GAAEN,KAAED,MAAGO,GAAE,SAAS,QAAQ,GAAEL,KAAEF,MAAGO,GAAE,SAAS,QAAQ;AAAE,QAAMJ,KAAEI,GAAE,SAAS,gBAAgB,GAAEG,KAAEf,GAAE;AAA0B,SAAOK,MAAG,EAAES,MAAGR,MAAGC,QAAKO,KAAER,KAAEC,KAAE,OAAI,EAAC,mBAAkBN,GAAED,IAAE,qBAAoB,KAAE,GAAE,kBAAiBC,GAAED,IAAE,oBAAmB,KAAE,GAAE,qBAAoBC,GAAED,IAAE,uBAAsB,KAAE,GAAE,aAAYc,IAAE,gBAAeR,IAAE,iBAAgBD,IAAE,wBAAuBG,IAAE,eAAcK,IAAE,wBAAuBZ,GAAEc,IAAE,yBAAwB,KAAE,GAAE,0BAAyBd,GAAEc,IAAE,4BAA2B,KAAE,GAAE,0BAAyBd,GAAEc,IAAE,4BAA2B,KAAE,GAAE,2BAA0Bd,GAAED,IAAE,+BAA8B,KAAE,GAAE,cAAaY,GAAE,SAAS,MAAM,GAAE,gBAAeL,IAAE,gCAA+BN,GAAED,IAAE,kCAAiC,KAAE,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAMA,KAAE,EAAE,2BAA0Ba,KAAE,EAAE,wCAAuCR,KAAE,EAAE,eAAcS,KAAE,EAAE,gBAAeR,KAAE,uBAAG,SAAS,cAAaC,KAAE,CAACD,MAAGQ,MAAG,IAAI,+BAA+B,GAAEN,KAAE,EAAE,CAAC,GAAEO,KAAE,IAAI,KAAK,EAAE,yBAAuB,IAAI,MAAM,GAAG,EAAE,IAAK,CAAAZ,OAAGA,GAAE,YAAY,EAAE,KAAK,CAAE,CAAC;AAAE,SAAM,EAAC,oBAAmBF,GAAED,IAAE,sBAAqB,EAAE,kBAAkB,GAAE,8BAA6BC,GAAED,IAAE,gCAA+B,KAAE,GAAE,sCAAqCC,GAAED,IAAE,wCAAuC,KAAE,GAAE,uCAAsC,EAAEA,EAAC,GAAE,kBAAiBC,GAAED,IAAE,qCAAoC,KAAE,GAAE,kBAAiBC,GAAED,IAAE,6BAA4B,KAAE,GAAE,kBAAiBC,GAAED,IAAE,oBAAmB,EAAE,uBAAuB,GAAE,gBAAeC,GAAED,IAAE,gCAA+B,KAAE,GAAE,4BAA2BC,GAAED,IAAE,uCAAsC,KAAE,GAAE,sBAAqBC,GAAED,IAAE,wBAAuB,KAAE,GAAE,iBAAgBC,GAAED,IAAE,mBAAkB,EAAE,uBAAuB,GAAE,oBAAmBC,GAAED,IAAE,sBAAqB,KAAE,GAAE,sBAAqBC,GAAE,GAAE,mCAAkC,KAAE,GAAE,8BAA6BA,GAAE,GAAE,gCAA+B,KAAE,GAAE,uBAAsBA,GAAE,GAAE,kCAAiC,KAAE,GAAE,oBAAmBA,GAAED,IAAE,+BAA8B,KAAE,GAAE,8BAA6BC,GAAED,IAAE,gCAA+B,KAAE,GAAE,uBAAsBC,GAAED,IAAE,yBAAwB,KAAE,GAAE,iCAAgCC,GAAE,GAAE,0BAAyB,KAAE,GAAE,0BAAyBA,GAAED,IAAE,4BAA2B,KAAE,GAAE,uBAAsBC,GAAEY,IAAE,sBAAqB,IAAE,GAAE,wBAAuBZ,GAAEI,IAAE,mCAAkC,KAAE,GAAE,mBAAkBE,MAAGQ,GAAE,IAAI,KAAK,GAAE,qCAAoCd,GAAED,IAAE,8BAA6B,KAAE,GAAE,mBAAkBC,GAAED,IAAE,8BAA6B,KAAE,KAAGI,GAAEJ,IAAE,OAAO,GAAE,iCAAgCC,GAAED,IAAE,qBAAoB,KAAE,GAAE,yBAAwBQ,IAAE,wBAAuBP,GAAED,IAAE,0BAAyB,KAAE,GAAE,sBAAqB,EAAE,GAAE,wBAAuB,MAAM,GAAE,gBAAe,EAAE,GAAE,kBAAiB,MAAM,GAAE,wBAAuB,EAAE,GAAE,0BAAyB,MAAM,GAAE,oBAAmB,EAAE,GAAE,sBAAqB,MAAM,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,2BAA0BE,KAAED,GAAE,GAAE,gCAA+B,KAAE;AAAE,SAAM,EAAC,oBAAmBA,GAAE,GAAE,kCAAiC,KAAE,GAAE,eAAcC,IAAE,iBAAgBA,IAAE,mBAAkBE,GAAE,GAAE,cAAc,EAAC;AAAC;AAAC,SAASK,GAAE,GAAE;AAAC,SAAM,EAAC,mBAAkBL,GAAE,EAAE,2BAA0B,gBAAgB,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE;AAAuC,SAAM,EAAC,wBAAuBH,GAAE,GAAE,wBAAuB,IAAE,GAAE,kBAAiBA,GAAE,GAAE,mCAAkC,KAAE,GAAE,oDAAmDA,GAAE,GAAE,wCAAuC,KAAE,GAAE,2BAA0BA,GAAE,GAAE,sCAAqC,KAAE,GAAE,wBAAuBA,GAAE,GAAE,6BAA4B,KAAE,GAAE,0BAAyBA,GAAE,GAAE,iCAAgC,KAAE,GAAE,2BAA0BA,GAAE,GAAE,0BAAyB,IAAE,GAAE,wBAAuBA,GAAE,GAAE,uBAAsB,IAAE,GAAE,2BAA0BA,GAAE,GAAE,0BAAyB,IAAE,GAAE,wBAAuBA,GAAE,GAAE,uBAAsB,IAAE,EAAC;AAAC;", "names": ["l", "o", "r", "p", "e", "t", "a", "i", "d", "l", "y", "g", "s", "o", "u", "n", "c", "m"]}