import {
  S,
  u
} from "./chunk-VJW7RCN7.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  Z
} from "./chunk-U4SVMKOQ.js";
import {
  c,
  d
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-4RZONHOY.js";

// node_modules/@arcgis/core/layers/mixins/operationalLayers.js
var e2 = { "web-scene/operational-layers": { ArcGISDimensionLayer: true, ArcGISFeatureLayer: true, ArcGISImageServiceLayer: true, ArcGISMapServiceLayer: true, ArcGISSceneServiceLayer: true, ArcGISTiledElevationServiceLayer: true, ArcGISTiledImageServiceLayer: true, ArcGISTiledMapServiceLayer: true, BuildingSceneLayer: true, GroupLayer: true, IntegratedMeshLayer: true, OGCFeatureLayer: true, PointCloudLayer: true, WebTiledLayer: true, CSV: true, GeoJSON: true, VectorTileLayer: true, WFS: true, WMS: true, KML: true, RasterDataLayer: true, Voxel: true, LineOfSightLayer: true }, "web-scene/basemap": { ArcGISTiledImageServiceLayer: true, ArcGISTiledMapServiceLayer: true, WebTiledLayer: true, OpenStreetMap: true, VectorTileLayer: true, ArcGISImageServiceLayer: true, WMS: true, ArcGISMapServiceLayer: true, ArcGISSceneServiceLayer: true }, "web-scene/ground": { ArcGISTiledElevationServiceLayer: true, RasterDataElevationLayer: true }, "web-map/operational-layers": { ArcGISAnnotationLayer: true, ArcGISDimensionLayer: true, ArcGISFeatureLayer: true, ArcGISImageServiceLayer: true, ArcGISImageServiceVectorLayer: true, ArcGISMapServiceLayer: true, ArcGISStreamLayer: true, ArcGISTiledImageServiceLayer: true, ArcGISTiledMapServiceLayer: true, BingMapsAerial: true, BingMapsHybrid: true, BingMapsRoad: true, CSV: true, GeoRSS: true, GeoJSON: true, GroupLayer: true, KML: true, MediaLayer: true, OGCFeatureLayer: true, OrientedImageryLayer: true, SubtypeGroupLayer: true, VectorTileLayer: true, WFS: true, WMS: true, WebTiledLayer: true }, "web-map/basemap": { ArcGISImageServiceLayer: true, ArcGISImageServiceVectorLayer: true, ArcGISMapServiceLayer: true, ArcGISTiledImageServiceLayer: true, ArcGISTiledMapServiceLayer: true, OpenStreetMap: true, VectorTileLayer: true, WMS: true, WebTiledLayer: true, BingMapsAerial: true, BingMapsRoad: true, BingMapsHybrid: true }, "web-map/tables": { ArcGISFeatureLayer: true }, "portal-item/operational-layers": { ArcGISFeatureLayer: true, ArcGISSceneServiceLayer: true, PointCloudLayer: true, BuildingSceneLayer: true, IntegratedMeshLayer: true, OrientedImageryLayer: true } };

// node_modules/@arcgis/core/layers/mixins/OperationalLayer.js
var c2 = (c3) => {
  let u2 = class extends c3 {
    constructor() {
      super(...arguments), this.title = null;
    }
    writeListMode(e3, r2, t, o) {
      (o && "ground" === o.layerContainerType || e3 && c(this, t, {}, o)) && (r2[t] = e3);
    }
    writeOperationalLayerType(e3, r2, t, o) {
      !e3 || o && "tables" === o.layerContainerType || (r2.layerType = e3);
    }
    writeTitle(e3, r2) {
      r2.title = e3 ?? "Layer";
    }
    read(e3, r2) {
      r2 && (r2.layer = this), d(this, e3, (r3) => super.read(e3, r3), r2);
    }
    write(e3, o) {
      var _a, _b;
      if (o == null ? void 0 : o.origin) {
        const e4 = `${o.origin}/${o.layerContainerType || "operational-layers"}`, t = e2[e4];
        let i2 = t && t[this.operationalLayerType];
        if ("ArcGISTiledElevationServiceLayer" === this.operationalLayerType && "web-scene/operational-layers" === e4 && (i2 = false), "ArcGISDimensionLayer" === this.operationalLayerType && "web-map/operational-layers" === e4 && (i2 = false), !i2) return (_a = o.messages) == null ? void 0 : _a.push(new s("layer:unsupported", `Layers (${this.title}, ${this.id}) of type '${this.declaredClass}' are not supported in the context of '${e4}'`, { layer: this })), null;
      }
      const i = super.write(e3, { ...o, layer: this }), s2 = !!o && !!o.messages && !!o.messages.filter((e4) => e4 instanceof s && "web-document-write:property-required" === e4.name).length;
      return Z(i == null ? void 0 : i.url) ? ((_b = o == null ? void 0 : o.messages) == null ? void 0 : _b.push(new s("layer:invalid-url", `Layer (${this.title}, ${this.id}) of type '${this.declaredClass}' using a Blob URL cannot be written to web scenes and web maps`, { layer: this })), null) : !this.url && s2 ? null : i;
    }
    beforeSave() {
    }
  };
  return e([y({ type: String, json: { write: { ignoreOrigin: true }, origins: { "web-scene": { write: { isRequired: true, ignoreOrigin: true } }, "portal-item": { write: false } } } })], u2.prototype, "id", void 0), e([y(S)], u2.prototype, "listMode", void 0), e([r("listMode")], u2.prototype, "writeListMode", null), e([y({ type: String, readOnly: true, json: { read: false, write: { target: "layerType", ignoreOrigin: true }, origins: { "portal-item": { write: false } } } })], u2.prototype, "operationalLayerType", void 0), e([r("operationalLayerType")], u2.prototype, "writeOperationalLayerType", null), e([y(u)], u2.prototype, "opacity", void 0), e([y({ type: String, json: { write: { ignoreOrigin: true, writerEnsuresNonNull: true }, origins: { "web-scene": { write: { isRequired: true, ignoreOrigin: true, writerEnsuresNonNull: true } }, "portal-item": { write: false } } }, value: "Layer" })], u2.prototype, "title", void 0), e([r("title"), r(["web-scene"], "title")], u2.prototype, "writeTitle", null), e([y({ type: Boolean, json: { name: "visibility" } })], u2.prototype, "visible", void 0), u2 = e([a("esri.layers.mixins.OperationalLayer")], u2), u2;
};

export {
  c2 as c
};
//# sourceMappingURL=chunk-VSFGOST3.js.map
