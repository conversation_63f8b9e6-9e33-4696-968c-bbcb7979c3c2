{"version": 3, "sources": ["../../@arcgis/core/views/layers/WMSLayerView.js", "../../@arcgis/core/views/2d/layers/WMSLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import r from\"../../core/Error.js\";import{destroyMaybe as t}from\"../../core/maybe.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";import{combinedViewLayerTimeExtentProperty as p}from\"../../layers/support/commonProperties.js\";import{ExportWMSImageParameters as a}from\"../../layers/support/ExportWMSImageParameters.js\";const i=i=>{let m=class extends i{initialize(){this.exportImageParameters=new a({layer:this.layer})}destroy(){this.exportImageParameters=t(this.exportImageParameters)}get exportImageVersion(){return this.exportImageParameters?.commitProperty(\"version\"),this.commitProperty(\"timeExtent\"),(this._get(\"exportImageVersion\")||0)+1}fetchPopupFeatures(e){const{layer:t}=this;if(!e)return Promise.reject(new r(\"wmslayerview:fetchPopupFeatures\",\"Nothing to fetch without area\",{layer:t}));const{popupEnabled:o}=t;if(!o)return Promise.reject(new r(\"wmslayerview:fetchPopupFeatures\",\"popupEnabled should be true\",{popupEnabled:o}));const s=this.createFetchPopupFeaturesQuery(e);if(!s)return Promise.resolve([]);const{extent:p,width:a,height:i,x:m,y:n}=s;if(!(p&&a&&i))throw new r(\"wmslayerview:fetchPopupFeatures\",\"WMSLayer does not support fetching features.\",{extent:p,width:a,height:i});return t.fetchFeatureInfo(p,a,i,m,n)}};return e([o()],m.prototype,\"exportImageParameters\",void 0),e([o({readOnly:!0})],m.prototype,\"exportImageVersion\",null),e([o()],m.prototype,\"layer\",void 0),e([o(p)],m.prototype,\"timeExtent\",void 0),m=e([s(\"esri.layers.mixins.WMSLayerView\")],m),m};export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import t from\"../../../core/Logger.js\";import{destroyMaybe as r}from\"../../../core/maybe.js\";import{isAbortError as i}from\"../../../core/promiseUtils.js\";import{watch as s}from\"../../../core/reactiveUtils.js\";import{property as a}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import p from\"../../../geometry/Extent.js\";import{BitmapContainer as n}from\"../engine/BitmapContainer.js\";import{LayerView2DMixin as m}from\"./LayerView2D.js\";import h from\"./support/ExportStrategy.js\";import c from\"../../layers/LayerView.js\";import d from\"../../layers/RefreshableLayerView.js\";import u from\"../../layers/WMSLayerView.js\";let y=class extends(u(d(m(c)))){constructor(){super(...arguments),this.bitmapContainer=new n}supportsSpatialReference(e){return this.layer.serviceSupportsSpatialReference(e)}update(e){this.strategy.update(e).catch((e=>{i(e)||t.getLogger(this.declaredClass).error(e)}))}attach(){const{layer:e}=this,{imageMaxHeight:t,imageMaxWidth:r}=e;this.bitmapContainer=new n,this.container.addChild(this.bitmapContainer),this.strategy=new h({container:this.bitmapContainer,fetchSource:this.fetchImage.bind(this),requestUpdate:this.requestUpdate.bind(this),imageMaxHeight:t,imageMaxWidth:r,imageRotationSupported:!1,imageNormalizationSupported:!1,hidpi:!1}),this.addAttachHandles(s((()=>this.exportImageVersion),(()=>this.requestUpdate())))}detach(){this.strategy=r(this.strategy),this.container.removeAllChildren()}moveStart(){}viewChange(){}moveEnd(){this.requestUpdate()}createFetchPopupFeaturesQuery(e){const{view:t,bitmapContainer:r}=this,{x:i,y:s}=e,{spatialReference:a}=t;let o,n=0,m=0;if(r.children.some((e=>{const{width:t,height:r,resolution:h,x:c,y:d}=e,u=c+h*t,y=d-h*r;return i>=c&&i<=u&&s<=d&&s>=y&&(o=new p({xmin:c,ymin:y,xmax:u,ymax:d,spatialReference:a}),n=t,m=r,!0)})),!o)return null;const h=o.width/n,c=Math.round((i-o.xmin)/h),d=Math.round((o.ymax-s)/h);return{extent:o,width:n,height:m,x:c,y:d}}async doRefresh(){this.requestUpdate()}isUpdating(){return this.strategy.updating||this.updateRequested}fetchImage(e,t,r,i){return this.layer.fetchImageBitmap(e,t,r,{timeExtent:this.timeExtent,...i})}};e([a()],y.prototype,\"strategy\",void 0),e([a()],y.prototype,\"updating\",void 0),y=e([o(\"esri.views.2d.layers.WMSLayerView2D\")],y);const l=y;export{l as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2iB,IAAMA,KAAE,CAAAA,OAAG;AAAC,MAAI,IAAE,cAAcA,GAAC;AAAA,IAAC,aAAY;AAAC,WAAK,wBAAsB,IAAIC,GAAE,EAAC,OAAM,KAAK,MAAK,CAAC;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,WAAK,wBAAsB,EAAE,KAAK,qBAAqB;AAAA,IAAC;AAAA,IAAC,IAAI,qBAAoB;AAJ1uB;AAI2uB,cAAO,UAAK,0BAAL,mBAA4B,eAAe,YAAW,KAAK,eAAe,YAAY,IAAG,KAAK,KAAK,oBAAoB,KAAG,KAAG;AAAA,IAAC;AAAA,IAAC,mBAAmBC,IAAE;AAAC,YAAK,EAAC,OAAM,EAAC,IAAE;AAAK,UAAG,CAACA,GAAE,QAAO,QAAQ,OAAO,IAAIC,GAAE,mCAAkC,iCAAgC,EAAC,OAAM,EAAC,CAAC,CAAC;AAAE,YAAK,EAAC,cAAa,EAAC,IAAE;AAAE,UAAG,CAAC,EAAE,QAAO,QAAQ,OAAO,IAAIA,GAAE,mCAAkC,+BAA8B,EAAC,cAAa,EAAC,CAAC,CAAC;AAAE,YAAMA,KAAE,KAAK,8BAA8BD,EAAC;AAAE,UAAG,CAACC,GAAE,QAAO,QAAQ,QAAQ,CAAC,CAAC;AAAE,YAAK,EAAC,QAAO,GAAE,OAAMC,IAAE,QAAOJ,IAAE,GAAEK,IAAE,GAAE,EAAC,IAAEF;AAAE,UAAG,EAAE,KAAGC,MAAGJ,IAAG,OAAM,IAAIG,GAAE,mCAAkC,gDAA+C,EAAC,QAAO,GAAE,OAAMC,IAAE,QAAOJ,GAAC,CAAC;AAAE,aAAO,EAAE,iBAAiB,GAAEI,IAAEJ,IAAEK,IAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,IAAE,EAAE,CAACD,GAAE,iCAAiC,CAAC,GAAE,CAAC,GAAE;AAAC;;;ACAz2B,IAAIE,KAAE,cAAcC,GAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,kBAAgB,IAAIC;AAAA,EAAC;AAAA,EAAC,yBAAyBC,IAAE;AAAC,WAAO,KAAK,MAAM,gCAAgCA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,SAAK,SAAS,OAAOA,EAAC,EAAE,MAAO,CAAAA,OAAG;AAAC,QAAEA,EAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAK,EAAC,OAAMA,GAAC,IAAE,MAAK,EAAC,gBAAe,GAAE,eAAc,EAAC,IAAEA;AAAE,SAAK,kBAAgB,IAAID,MAAE,KAAK,UAAU,SAAS,KAAK,eAAe,GAAE,KAAK,WAAS,IAAI,EAAE,EAAC,WAAU,KAAK,iBAAgB,aAAY,KAAK,WAAW,KAAK,IAAI,GAAE,eAAc,KAAK,cAAc,KAAK,IAAI,GAAE,gBAAe,GAAE,eAAc,GAAE,wBAAuB,OAAG,6BAA4B,OAAG,OAAM,MAAE,CAAC,GAAE,KAAK,iBAAiB,EAAG,MAAI,KAAK,oBAAqB,MAAI,KAAK,cAAc,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,UAAU,kBAAkB;AAAA,EAAC;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,aAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,8BAA8BC,IAAE;AAAC,UAAK,EAAC,MAAK,GAAE,iBAAgB,EAAC,IAAE,MAAK,EAAC,GAAEF,IAAE,GAAEG,GAAC,IAAED,IAAE,EAAC,kBAAiBD,GAAC,IAAE;AAAE,QAAI,GAAE,IAAE,GAAE,IAAE;AAAE,QAAG,EAAE,SAAS,KAAM,CAAAC,OAAG;AAAC,YAAK,EAAC,OAAME,IAAE,QAAOC,IAAE,YAAWC,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAEN,IAAEO,KAAEF,KAAED,KAAEF,IAAEL,KAAES,KAAEF,KAAED;AAAE,aAAOL,MAAGO,MAAGP,MAAGS,MAAGN,MAAGK,MAAGL,MAAGJ,OAAI,IAAE,IAAI,EAAE,EAAC,MAAKQ,IAAE,MAAKR,IAAE,MAAKU,IAAE,MAAKD,IAAE,kBAAiBP,GAAC,CAAC,GAAE,IAAEG,IAAE,IAAEC,IAAE;AAAA,IAAG,CAAE,GAAE,CAAC,EAAE,QAAO;AAAK,UAAM,IAAE,EAAE,QAAM,GAAE,IAAE,KAAK,OAAOL,KAAE,EAAE,QAAM,CAAC,GAAE,IAAE,KAAK,OAAO,EAAE,OAAKG,MAAG,CAAC;AAAE,WAAM,EAAC,QAAO,GAAE,OAAM,GAAE,QAAO,GAAE,GAAE,GAAE,GAAE,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAW;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,SAAS,YAAU,KAAK;AAAA,EAAe;AAAA,EAAC,WAAWD,IAAE,GAAE,GAAEF,IAAE;AAAC,WAAO,KAAK,MAAM,iBAAiBE,IAAE,GAAE,GAAE,EAAC,YAAW,KAAK,YAAW,GAAGF,GAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAACE,GAAE,qCAAqC,CAAC,GAAEF,EAAC;AAAE,IAAMW,KAAEX;", "names": ["i", "l", "e", "s", "a", "m", "y", "i", "a", "e", "s", "t", "r", "h", "c", "d", "u", "l"]}