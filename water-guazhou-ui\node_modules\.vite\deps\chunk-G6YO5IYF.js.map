{"version": 3, "sources": ["../../@arcgis/core/views/2d/arcade/callExpressionWithCursor.js", "../../@arcgis/core/views/2d/layers/features/Store2D.js", "../../@arcgis/core/views/2d/layers/features/support/FeatureSetReaderPBFIndirect.js", "../../@arcgis/core/views/2d/layers/features/FeatureStore2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../core/Logger.js\";import{isNone as r}from\"../../../core/maybe.js\";function a(a,t,o){if(r(a))return null;const u=t.readArcadeFeature();try{return a.evaluate({...o,$feature:u})}catch(n){return e.getLogger(\"esri.views.2d.support.arcadeOnDemand\").warn(\"Feature arcade evaluation failed:\",n),null}}export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../core/has.js\";import{isNone as e,isSome as s}from\"../../../../core/maybe.js\";import{diff as t,hasDiff as i}from\"../../../../core/accessorSupport/diffUtils.js\";import{createRendererExpression as r}from\"../../../../support/arcadeOnDemand.js\";import a from\"../../arcade/callExpressionWithCursor.js\";const o=import(\"../../../../layers/support/labelFormatUtils.js\");class c{constructor(e,s){this._canCacheExpressionValue=!1,this._sourceInfo=e,this._storage=s,this._bitsets={computed:s.getBitset(s.createBitset())}}get storage(){return this._storage}invalidate(){this._bitsets.computed.clear()}async updateSchema(r,a){const o=t(this._schema,a);if(this._schema=a,!a||e(o)||!i(o,\"attributes\"))return;has(\"esri-2d-update-debug\")&&console.debug(\"Applying Update - Store:\",o),this._bitsets.computed.clear(),r.targets[a.name]=!0;const c=a.attributes,n=[],p=[];for(const e in c){const s=c[e];switch(s.type){case\"field\":break;case\"expression\":n.push(this._createArcadeComputedField(s));break;case\"label-expression\":n.push(this._createLabelArcadeComputedField(s));break;case\"statistic\":p.push(s)}}this._computedFields=await Promise.all(n),this._canCacheExpressionValue=!this._computedFields.some((e=>\"expression\"===e.type&&s(e.expression)&&e.expression.referencesScale())),this._statisticFields=p}setComputedAttributes(e,s,t,i){const r=this._bitsets.computed;if(!this._canCacheExpressionValue||!r.has(t)){r.set(t);for(const r of this._computedFields){const a=this._evaluateField(s,r,i);switch(r.resultType){case\"numeric\":e.setComputedNumericAtIndex(t,r.fieldIndex,a);break;case\"string\":e.setComputedStringAtIndex(t,r.fieldIndex,a)}}}}async _createArcadeComputedField(e){const s=this._sourceInfo.spatialReference,t=this._sourceInfo.fieldsIndex;return{...e,expression:await r(e.valueExpression,s,t)}}async _createLabelArcadeComputedField(e){const s=this._sourceInfo.spatialReference,t=this._sourceInfo.fieldsIndex,{createLabelFunction:i}=await o,r=await i(e.label,t,s);return{...e,builder:r}}_evaluateField(e,s,t){switch(s.type){case\"label-expression\":{const t=e.readArcadeFeature();return s.builder.evaluate(t)||\"\"}case\"expression\":{const{expression:i}=s;return a(i,e,{$view:{scale:t}})}}}}export{c as Store2D};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{FeatureSetReader as e}from\"./FeatureSetReader.js\";class r extends e{static from(e,t){return new r(e.copy(),t)}constructor(r,t){super(e.createInstance(),r.fullSchema()),this._currentIndex=-1,this._reader=r,this._indices=t}get hasNext(){return this._currentIndex+1<this._indices.length}getSize(){return this._indices.length}getCursor(){return this.copy()}copy(){const e=new r(this._reader.copy(),this._indices);return e._currentIndex=this._currentIndex,e}next(){for(;this._nextIndex()&&!this._reader._getExists(););return this._currentIndex<this._indices.length}_nextIndex(){return++this._currentIndex<this._indices.length&&(this._reader.setIndex(this._indices[this._currentIndex]),!0)}setArcadeSpatialReference(e){this._reader.setArcadeSpatialReference(e)}attachStorage(e){this._reader.attachStorage(e)}get geometryType(){return this._reader.geometryType}get hasFeatures(){return this._reader.hasFeatures}get exceededTransferLimit(){return this._reader.exceededTransferLimit}get hasZ(){return this._reader.hasZ}get hasM(){return this._reader.hasM}getStorage(){return this._reader.getStorage()}getComputedNumeric(e){return this._reader.getComputedNumericAtIndex(0)}setComputedNumeric(e,r){return this._reader.setComputedNumericAtIndex(r,0)}getComputedString(e){return this._reader.getComputedStringAtIndex(0)}setComputedString(e,r){return this._reader.setComputedStringAtIndex(0,r)}getComputedNumericAtIndex(e){return this._reader.getComputedNumericAtIndex(e)}setComputedNumericAtIndex(e,r){this._reader.setComputedNumericAtIndex(e,r)}getComputedStringAtIndex(e){return this._reader.getComputedStringAtIndex(e)}setComputedStringAtIndex(e,r){return this._reader.setComputedStringAtIndex(e,r)}transform(e,r,t,d){const a=this.copy();return a._reader=this._reader.transform(e,r,t,d),a}readAttribute(e,r=!1){return this._reader.readAttribute(e,r)}readAttributes(){return this._reader.readAttributes()}joinAttributes(e){return this._reader.joinAttributes(e)}readArcadeFeature(){return this._reader.readArcadeFeature()}geometry(){return this._reader.geometry()}field(e){return this.readAttribute(e,!0)}hasField(e){return this._reader.hasField(e)}setField(e,r){return this._reader.setField(e,r)}keys(){return this._reader.keys()}castToText(e=!1){return this._reader.castToText(e)}getQuantizationTransform(){return this._reader.getQuantizationTransform()}getFieldNames(){return this._reader.getFieldNames()}getAttributeHash(){return this._reader.getAttributeHash()}getObjectId(){return this._reader.getObjectId()}getDisplayId(){return this._reader.getDisplayId()}setDisplayId(e){return this._reader.setDisplayId(e)}getGroupId(){return this._reader.getGroupId()}setGroupId(e){return this._reader.setGroupId(e)}getXHydrated(){return this._reader.getXHydrated()}getYHydrated(){return this._reader.getYHydrated()}getX(){return this._reader.getX()}getY(){return this._reader.getY()}setIndex(e){return this._reader.setIndex(e)}getIndex(){return this._reader.getIndex()}readLegacyFeature(){return this._reader.readLegacyFeature()}readOptimizedFeature(){return this._reader.readOptimizedFeature()}readLegacyPointGeometry(){return this._reader.readLegacyPointGeometry()}readLegacyGeometry(){return this._reader.readLegacyGeometry()}readLegacyCentroid(){return this._reader.readLegacyCentroid()}readGeometryArea(){return this._reader.readGeometryArea()}readUnquantizedGeometry(){return this._reader.readUnquantizedGeometry()}readHydratedGeometry(){return this._reader.readHydratedGeometry()}readGeometry(){return this._reader.readGeometry()}readCentroid(){return this._reader.readCentroid()}_readAttribute(e,r){throw new Error(\"Error: Should not be called. Underlying _reader should be used instead\")}_readAttributes(){throw new Error(\"Error: Should not be called. Underlying _reader should be used instead\")}}export{r as FeatureSetReaderIndirect};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../core/CircularArray.js\";import e from\"../../../../core/Evented.js\";import\"../../../../core/has.js\";import{isNone as s,isSome as a,unwrap as r}from\"../../../../core/maybe.js\";import{r as n}from\"../../../../chunks/rbush.js\";import{create as i,fromRectValues as o}from\"../../../../geometry/support/aaBoundingBox.js\";import{Store2D as d}from\"./Store2D.js\";import{FeatureSetReaderIndirect as h}from\"./support/FeatureSetReaderPBFIndirect.js\";const c=i();function I(t,e){return t<<16|e}function u(t){return(4294901760&t)>>>16}function l(t){return 65535&t}const p={getObjectId:t=>t.getObjectId(),getAttributes:t=>t.readAttributes(),getAttribute:(t,e)=>t.readAttribute(e),cloneWithGeometry:(t,e)=>t,getGeometry:t=>t.readHydratedGeometry(),getCentroid:(t,e)=>t.readCentroid()};class g extends d{constructor(s,a,r){super(s,a),this.featureAdapter=p,this.events=new e,this._featureSetsByInstance=new Map,this._objectIdToDisplayId=new Map,this._spatialIndexInvalid=!0,this._indexSearchCache=new t(50),this._index=n(9,(t=>({minX:this._storage.getXMin(t),minY:this._storage.getYMin(t),maxX:this._storage.getXMax(t),maxY:this._storage.getYMax(t)}))),this.mode=r}get storeStatistics(){let t=0,e=0,s=0;return this.forEach((a=>{const r=a.readGeometry();r&&(e+=r.isPoint?1:r.lengths.reduce(((t,e)=>t+e),0),s+=r.isPoint?1:r.lengths.length,t+=1)})),{featureCount:t,vertexCount:e,ringCount:s}}hasInstance(t){return this._featureSetsByInstance.has(t)}onTileData(t,e){if(s(e.addOrUpdate))return e;if(e.addOrUpdate.attachStorage(this._storage),\"snapshot\"===this.mode){const s=e.addOrUpdate.getCursor();for(;s.next();){const e=s.getDisplayId();this.setComputedAttributes(this._storage,s,e,t.scale)}return e}this._featureSetsByInstance.set(e.addOrUpdate.instance,e.addOrUpdate);const a=e.addOrUpdate.getCursor();for(;a.next();)this._insertFeature(a,t.scale);return this._spatialIndexInvalid=!0,this.events.emit(\"changed\"),e}search(t){this._rebuildIndex();const e=t.id,s=this._indexSearchCache.find((t=>t.tileId===e));if(a(s))return s.readers;const r=new Map,n=this._searchIndex(t.bounds),i=[];for(const a of n){const t=this._storage.getInstanceId(a),e=u(t),s=l(t);r.has(e)||r.set(e,[]);r.get(e).push(s)}return r.forEach(((t,e)=>{const s=this._featureSetsByInstance.get(e);i.push(h.from(s,t))})),this._indexSearchCache.enqueue({tileId:e,readers:i}),i}insert(t){const e=t.getCursor(),s=this._storage;for(;e.next();){const t=I(e.instance,e.getIndex()),a=e.getObjectId(),r=this._objectIdToDisplayId.get(a)??this._storage.createDisplayId();e.setDisplayId(r),s.setInstanceId(r,t),this._objectIdToDisplayId.set(a,r)}this._featureSetsByInstance.set(t.instance,t),this._spatialIndexInvalid=!0}remove(t){const e=this._objectIdToDisplayId.get(t);if(!e)return;const s=this._storage.getInstanceId(e),a=l(s),r=u(s),n=this._featureSetsByInstance.get(r);this._objectIdToDisplayId.delete(t),this._storage.releaseDisplayId(e),n.removeAtIndex(a),n.isEmpty&&this._featureSetsByInstance.delete(r),this._spatialIndexInvalid=!0}forEach(t){this._objectIdToDisplayId.forEach((e=>{const s=this._storage.getInstanceId(e),a=this._lookupFeature(s);t(a)}))}forEachUnsafe(t){this._objectIdToDisplayId.forEach((e=>{const s=this._storage.getInstanceId(e),a=u(s),r=l(s),n=this._getFeatureSet(a);n.setIndex(r),t(n)}))}forEachInBounds(t,e){const s=this._searchIndex(t);for(const a of s){const t=this.lookupFeatureByDisplayId(a,this._storage);e(r(t))}}forEachBounds(t,e){this._rebuildIndex();for(const s of t){if(!s.readGeometry())continue;const t=s.getDisplayId();o(c,this._storage.getXMin(t),this._storage.getYMin(t),this._storage.getXMax(t),this._storage.getYMax(t)),e(c)}}sweepFeatures(t,e,s){this._spatialIndexInvalid=!0,this._objectIdToDisplayId.forEach(((a,r)=>{t.has(a)||(e.releaseDisplayId(a),s&&s.unsetAttributeData(a),this._objectIdToDisplayId.delete(r))})),this.events.emit(\"changed\")}sweepFeatureSets(t){this._spatialIndexInvalid=!0,this._featureSetsByInstance.forEach(((e,s)=>{t.has(s)||this._featureSetsByInstance.delete(s)}))}lookupObjectId(t,e){const a=this.lookupFeatureByDisplayId(t,e);return s(a)?null:a.getObjectId()}lookupDisplayId(t){return this._objectIdToDisplayId.get(t)}lookupFeatureByDisplayId(t,e){const s=e.getInstanceId(t);return this._lookupFeature(s)}lookupByDisplayIdUnsafe(t){const e=this._storage.getInstanceId(t),s=u(e),a=l(e),r=this._getFeatureSet(s);return r?(r.setIndex(a),r):null}_insertFeature(t,e){const s=this._storage,a=t.getObjectId(),r=I(t.instance,t.getIndex());s.getInstanceId(t.getDisplayId());let n=this._objectIdToDisplayId.get(a);n||(n=s.createDisplayId(),this._objectIdToDisplayId.set(a,n),this._spatialIndexInvalid=!0),t.setDisplayId(n),s.setInstanceId(n,r),this.setComputedAttributes(s,t,n,e)}_searchIndex(t){this._rebuildIndex();const e={minX:t[0],minY:t[1],maxX:t[2],maxY:t[3]};return this._index.search(e)}_rebuildIndex(){if(!this._spatialIndexInvalid)return;const t=[];\"snapshot\"===this.mode?this._featureSetsByInstance.forEach((e=>{const s=e.getCursor();for(;s.next();){const e=s.getDisplayId();this._storage.setBounds(e,s)&&t.push(e)}})):this._objectIdToDisplayId.forEach((e=>{const s=this._storage.getInstanceId(e);this._storage.setBounds(e,this._lookupFeature(s))&&t.push(e)})),this._index.clear(),this._index.load(t),this._indexSearchCache.clear(),this._spatialIndexInvalid=!1}_lookupFeature(t){const e=u(t),s=this._getFeatureSet(e);if(!s)return;const a=s.getCursor(),r=l(t);return a.setIndex(r),a}_getFeatureSet(t){return this._featureSetsByInstance.get(t)}}export{g as FeatureStore2D,p as featureAdapter};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIuF,SAASA,GAAEA,IAAEC,IAAEC,IAAE;AAAC,MAAG,EAAEF,EAAC,EAAE,QAAO;AAAK,QAAMG,KAAEF,GAAE,kBAAkB;AAAE,MAAG;AAAC,WAAOD,GAAE,SAAS,EAAC,GAAGE,IAAE,UAASC,GAAC,CAAC;AAAA,EAAC,SAAOC,IAAE;AAAC,WAAO,EAAE,UAAU,sCAAsC,EAAE,KAAK,qCAAoCA,EAAC,GAAE;AAAA,EAAI;AAAC;;;ACAU,IAAMC,KAAE,OAAO,gCAAgD;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,2BAAyB,OAAG,KAAK,cAAYD,IAAE,KAAK,WAASC,IAAE,KAAK,WAAS,EAAC,UAASA,GAAE,UAAUA,GAAE,aAAa,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,aAAY;AAAC,SAAK,SAAS,SAAS,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaC,IAAEC,IAAE;AAAC,UAAMJ,KAAE,EAAE,KAAK,SAAQI,EAAC;AAAE,QAAG,KAAK,UAAQA,IAAE,CAACA,MAAG,EAAEJ,EAAC,KAAG,CAACI,GAAEJ,IAAE,YAAY,EAAE;AAAO,QAAI,sBAAsB,KAAG,QAAQ,MAAM,4BAA2BA,EAAC,GAAE,KAAK,SAAS,SAAS,MAAM,GAAEG,GAAE,QAAQC,GAAE,IAAI,IAAE;AAAG,UAAMC,KAAED,GAAE,YAAWE,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,eAAUN,MAAKI,IAAE;AAAC,YAAMH,KAAEG,GAAEJ,EAAC;AAAE,cAAOC,GAAE,MAAK;AAAA,QAAC,KAAI;AAAQ;AAAA,QAAM,KAAI;AAAa,UAAAI,GAAE,KAAK,KAAK,2BAA2BJ,EAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAmB,UAAAI,GAAE,KAAK,KAAK,gCAAgCJ,EAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAY,UAAAK,GAAE,KAAKL,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,SAAK,kBAAgB,MAAM,QAAQ,IAAII,EAAC,GAAE,KAAK,2BAAyB,CAAC,KAAK,gBAAgB,KAAM,CAAAL,OAAG,iBAAeA,GAAE,QAAM,EAAEA,GAAE,UAAU,KAAGA,GAAE,WAAW,gBAAgB,CAAE,GAAE,KAAK,mBAAiBM;AAAA,EAAC;AAAA,EAAC,sBAAsBN,IAAEC,IAAEM,IAAEC,IAAE;AAAC,UAAMN,KAAE,KAAK,SAAS;AAAS,QAAG,CAAC,KAAK,4BAA0B,CAACA,GAAE,IAAIK,EAAC,GAAE;AAAC,MAAAL,GAAE,IAAIK,EAAC;AAAE,iBAAUL,MAAK,KAAK,iBAAgB;AAAC,cAAMC,KAAE,KAAK,eAAeF,IAAEC,IAAEM,EAAC;AAAE,gBAAON,GAAE,YAAW;AAAA,UAAC,KAAI;AAAU,YAAAF,GAAE,0BAA0BO,IAAEL,GAAE,YAAWC,EAAC;AAAE;AAAA,UAAM,KAAI;AAAS,YAAAH,GAAE,yBAAyBO,IAAEL,GAAE,YAAWC,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,2BAA2BH,IAAE;AAAC,UAAMC,KAAE,KAAK,YAAY,kBAAiBM,KAAE,KAAK,YAAY;AAAY,WAAM,EAAC,GAAGP,IAAE,YAAW,MAAM,EAAEA,GAAE,iBAAgBC,IAAEM,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gCAAgCP,IAAE;AAAC,UAAMC,KAAE,KAAK,YAAY,kBAAiBM,KAAE,KAAK,YAAY,aAAY,EAAC,qBAAoBC,GAAC,IAAE,MAAMT,IAAEG,KAAE,MAAMM,GAAER,GAAE,OAAMO,IAAEN,EAAC;AAAE,WAAM,EAAC,GAAGD,IAAE,SAAQE,GAAC;AAAA,EAAC;AAAA,EAAC,eAAeF,IAAEC,IAAEM,IAAE;AAAC,YAAON,GAAE,MAAK;AAAA,MAAC,KAAI,oBAAmB;AAAC,cAAMM,KAAEP,GAAE,kBAAkB;AAAE,eAAOC,GAAE,QAAQ,SAASM,EAAC,KAAG;AAAA,MAAE;AAAA,MAAC,KAAI,cAAa;AAAC,cAAK,EAAC,YAAWC,GAAC,IAAEP;AAAE,eAAOE,GAAEK,IAAER,IAAE,EAAC,OAAM,EAAC,OAAMO,GAAC,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACA/lE,IAAME,KAAN,MAAM,WAAU,EAAC;AAAA,EAAC,OAAO,KAAKC,IAAEC,IAAE;AAAC,WAAO,IAAI,GAAED,GAAE,KAAK,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAEE,IAAE;AAAC,UAAM,EAAE,eAAe,GAAEF,GAAE,WAAW,CAAC,GAAE,KAAK,gBAAc,IAAG,KAAK,UAAQA,IAAE,KAAK,WAASE;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,gBAAc,IAAE,KAAK,SAAS;AAAA,EAAM;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,SAAS;AAAA,EAAM;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,UAAMD,KAAE,IAAI,GAAE,KAAK,QAAQ,KAAK,GAAE,KAAK,QAAQ;AAAE,WAAOA,GAAE,gBAAc,KAAK,eAAcA;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAK,KAAK,WAAW,KAAG,CAAC,KAAK,QAAQ,WAAW,IAAG;AAAC,WAAO,KAAK,gBAAc,KAAK,SAAS;AAAA,EAAM;AAAA,EAAC,aAAY;AAAC,WAAM,EAAE,KAAK,gBAAc,KAAK,SAAS,WAAS,KAAK,QAAQ,SAAS,KAAK,SAAS,KAAK,aAAa,CAAC,GAAE;AAAA,EAAG;AAAA,EAAC,0BAA0BA,IAAE;AAAC,SAAK,QAAQ,0BAA0BA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,SAAK,QAAQ,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAY;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAW;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAqB;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAI;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,QAAQ,WAAW;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,WAAO,KAAK,QAAQ,0BAA0B,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAED,IAAE;AAAC,WAAO,KAAK,QAAQ,0BAA0BA,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBC,IAAE;AAAC,WAAO,KAAK,QAAQ,yBAAyB,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAED,IAAE;AAAC,WAAO,KAAK,QAAQ,yBAAyB,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BC,IAAE;AAAC,WAAO,KAAK,QAAQ,0BAA0BA,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAED,IAAE;AAAC,SAAK,QAAQ,0BAA0BC,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBC,IAAE;AAAC,WAAO,KAAK,QAAQ,yBAAyBA,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAED,IAAE;AAAC,WAAO,KAAK,QAAQ,yBAAyBC,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUC,IAAED,IAAEE,IAAE,GAAE;AAAC,UAAMC,KAAE,KAAK,KAAK;AAAE,WAAOA,GAAE,UAAQ,KAAK,QAAQ,UAAUF,IAAED,IAAEE,IAAE,CAAC,GAAEC;AAAA,EAAC;AAAA,EAAC,cAAcF,IAAED,KAAE,OAAG;AAAC,WAAO,KAAK,QAAQ,cAAcC,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,WAAO,KAAK,QAAQ,eAAe;AAAA,EAAC;AAAA,EAAC,eAAeC,IAAE;AAAC,WAAO,KAAK,QAAQ,eAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK,QAAQ,kBAAkB;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK,QAAQ,SAAS;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAE;AAAC,WAAO,KAAK,cAAcA,IAAE,IAAE;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,WAAO,KAAK,QAAQ,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAED,IAAE;AAAC,WAAO,KAAK,QAAQ,SAASC,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,QAAQ,KAAK;AAAA,EAAC;AAAA,EAAC,WAAWC,KAAE,OAAG;AAAC,WAAO,KAAK,QAAQ,WAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,2BAA0B;AAAC,WAAO,KAAK,QAAQ,yBAAyB;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,KAAK,QAAQ,cAAc;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAO,KAAK,QAAQ,iBAAiB;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK,QAAQ,YAAY;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,WAAO,KAAK,QAAQ,aAAa;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,WAAO,KAAK,QAAQ,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,QAAQ,WAAW;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,WAAO,KAAK,QAAQ,WAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,WAAO,KAAK,QAAQ,aAAa;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,WAAO,KAAK,QAAQ,aAAa;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,QAAQ,KAAK;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,QAAQ,KAAK;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,WAAO,KAAK,QAAQ,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK,QAAQ,SAAS;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK,QAAQ,kBAAkB;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,WAAO,KAAK,QAAQ,qBAAqB;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,WAAO,KAAK,QAAQ,wBAAwB;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,KAAK,QAAQ,mBAAmB;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,KAAK,QAAQ,mBAAmB;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAO,KAAK,QAAQ,iBAAiB;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,WAAO,KAAK,QAAQ,wBAAwB;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,WAAO,KAAK,QAAQ,qBAAqB;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,WAAO,KAAK,QAAQ,aAAa;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,WAAO,KAAK,QAAQ,aAAa;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAED,IAAE;AAAC,UAAM,IAAI,MAAM,wEAAwE;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,UAAM,IAAI,MAAM,wEAAwE;AAAA,EAAC;AAAC;;;ACAtwG,IAAMI,KAAE,EAAE;AAAE,SAAS,EAAEC,IAAEC,IAAE;AAAC,SAAOD,MAAG,KAAGC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,UAAO,aAAWA,QAAK;AAAE;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,QAAMA;AAAC;AAAC,IAAM,IAAE,EAAC,aAAY,CAAAA,OAAGA,GAAE,YAAY,GAAE,eAAc,CAAAA,OAAGA,GAAE,eAAe,GAAE,cAAa,CAACA,IAAEC,OAAID,GAAE,cAAcC,EAAC,GAAE,mBAAkB,CAACD,IAAEC,OAAID,IAAE,aAAY,CAAAA,OAAGA,GAAE,qBAAqB,GAAE,aAAY,CAACA,IAAEC,OAAID,GAAE,aAAa,EAAC;AAAE,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYE,IAAEC,IAAEC,IAAE;AAAC,UAAMF,IAAEC,EAAC,GAAE,KAAK,iBAAe,GAAE,KAAK,SAAO,IAAI,KAAE,KAAK,yBAAuB,oBAAI,OAAI,KAAK,uBAAqB,oBAAI,OAAI,KAAK,uBAAqB,MAAG,KAAK,oBAAkB,IAAID,GAAE,EAAE,GAAE,KAAK,SAAO,EAAE,GAAG,CAAAF,QAAI,EAAC,MAAK,KAAK,SAAS,QAAQA,EAAC,GAAE,MAAK,KAAK,SAAS,QAAQA,EAAC,GAAE,MAAK,KAAK,SAAS,QAAQA,EAAC,GAAE,MAAK,KAAK,SAAS,QAAQA,EAAC,EAAC,EAAG,GAAE,KAAK,OAAKI;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,QAAIJ,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAAO,KAAK,QAAS,CAAAC,OAAG;AAAC,YAAMC,KAAED,GAAE,aAAa;AAAE,MAAAC,OAAIH,MAAGG,GAAE,UAAQ,IAAEA,GAAE,QAAQ,OAAQ,CAACJ,IAAEC,OAAID,KAAEC,IAAG,CAAC,GAAEC,MAAGE,GAAE,UAAQ,IAAEA,GAAE,QAAQ,QAAOJ,MAAG;AAAA,IAAE,CAAE,GAAE,EAAC,cAAaA,IAAE,aAAYC,IAAE,WAAUC,GAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAE;AAAC,WAAO,KAAK,uBAAuB,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAEC,IAAE;AAAC,QAAG,EAAEA,GAAE,WAAW,EAAE,QAAOA;AAAE,QAAGA,GAAE,YAAY,cAAc,KAAK,QAAQ,GAAE,eAAa,KAAK,MAAK;AAAC,YAAMC,KAAED,GAAE,YAAY,UAAU;AAAE,aAAKC,GAAE,KAAK,KAAG;AAAC,cAAMD,KAAEC,GAAE,aAAa;AAAE,aAAK,sBAAsB,KAAK,UAASA,IAAED,IAAED,GAAE,KAAK;AAAA,MAAC;AAAC,aAAOC;AAAA,IAAC;AAAC,SAAK,uBAAuB,IAAIA,GAAE,YAAY,UAASA,GAAE,WAAW;AAAE,UAAME,KAAEF,GAAE,YAAY,UAAU;AAAE,WAAKE,GAAE,KAAK,IAAG,MAAK,eAAeA,IAAEH,GAAE,KAAK;AAAE,WAAO,KAAK,uBAAqB,MAAG,KAAK,OAAO,KAAK,SAAS,GAAEC;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,SAAK,cAAc;AAAE,UAAMC,KAAED,GAAE,IAAGE,KAAE,KAAK,kBAAkB,KAAM,CAAAF,OAAGA,GAAE,WAASC,EAAE;AAAE,QAAG,EAAEC,EAAC,EAAE,QAAOA,GAAE;AAAQ,UAAME,KAAE,oBAAI,OAAIC,KAAE,KAAK,aAAaL,GAAE,MAAM,GAAEM,KAAE,CAAC;AAAE,eAAUH,MAAKE,IAAE;AAAC,YAAML,KAAE,KAAK,SAAS,cAAcG,EAAC,GAAEF,KAAE,EAAED,EAAC,GAAEE,KAAE,EAAEF,EAAC;AAAE,MAAAI,GAAE,IAAIH,EAAC,KAAGG,GAAE,IAAIH,IAAE,CAAC,CAAC;AAAE,MAAAG,GAAE,IAAIH,EAAC,EAAE,KAAKC,EAAC;AAAA,IAAC;AAAC,WAAOE,GAAE,QAAS,CAACJ,IAAEC,OAAI;AAAC,YAAMC,KAAE,KAAK,uBAAuB,IAAID,EAAC;AAAE,MAAAK,GAAE,KAAKF,GAAE,KAAKF,IAAEF,EAAC,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK,kBAAkB,QAAQ,EAAC,QAAOC,IAAE,SAAQK,GAAC,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAON,IAAE;AAAC,UAAMC,KAAED,GAAE,UAAU,GAAEE,KAAE,KAAK;AAAS,WAAKD,GAAE,KAAK,KAAG;AAAC,YAAMD,KAAE,EAAEC,GAAE,UAASA,GAAE,SAAS,CAAC,GAAEE,KAAEF,GAAE,YAAY,GAAEG,KAAE,KAAK,qBAAqB,IAAID,EAAC,KAAG,KAAK,SAAS,gBAAgB;AAAE,MAAAF,GAAE,aAAaG,EAAC,GAAEF,GAAE,cAAcE,IAAEJ,EAAC,GAAE,KAAK,qBAAqB,IAAIG,IAAEC,EAAC;AAAA,IAAC;AAAC,SAAK,uBAAuB,IAAIJ,GAAE,UAASA,EAAC,GAAE,KAAK,uBAAqB;AAAA,EAAE;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAMC,KAAE,KAAK,qBAAqB,IAAID,EAAC;AAAE,QAAG,CAACC,GAAE;AAAO,UAAMC,KAAE,KAAK,SAAS,cAAcD,EAAC,GAAEE,KAAE,EAAED,EAAC,GAAEE,KAAE,EAAEF,EAAC,GAAEG,KAAE,KAAK,uBAAuB,IAAID,EAAC;AAAE,SAAK,qBAAqB,OAAOJ,EAAC,GAAE,KAAK,SAAS,iBAAiBC,EAAC,GAAEI,GAAE,cAAcF,EAAC,GAAEE,GAAE,WAAS,KAAK,uBAAuB,OAAOD,EAAC,GAAE,KAAK,uBAAqB;AAAA,EAAE;AAAA,EAAC,QAAQJ,IAAE;AAAC,SAAK,qBAAqB,QAAS,CAAAC,OAAG;AAAC,YAAMC,KAAE,KAAK,SAAS,cAAcD,EAAC,GAAEE,KAAE,KAAK,eAAeD,EAAC;AAAE,MAAAF,GAAEG,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcH,IAAE;AAAC,SAAK,qBAAqB,QAAS,CAAAC,OAAG;AAAC,YAAMC,KAAE,KAAK,SAAS,cAAcD,EAAC,GAAEE,KAAE,EAAED,EAAC,GAAEE,KAAE,EAAEF,EAAC,GAAEG,KAAE,KAAK,eAAeF,EAAC;AAAE,MAAAE,GAAE,SAASD,EAAC,GAAEJ,GAAEK,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBL,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,aAAaF,EAAC;AAAE,eAAUG,MAAKD,IAAE;AAAC,YAAMF,KAAE,KAAK,yBAAyBG,IAAE,KAAK,QAAQ;AAAE,MAAAF,GAAE,EAAED,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAE;AAAC,SAAK,cAAc;AAAE,eAAUC,MAAKF,IAAE;AAAC,UAAG,CAACE,GAAE,aAAa,EAAE;AAAS,YAAMF,KAAEE,GAAE,aAAa;AAAE,QAAEH,IAAE,KAAK,SAAS,QAAQC,EAAC,GAAE,KAAK,SAAS,QAAQA,EAAC,GAAE,KAAK,SAAS,QAAQA,EAAC,GAAE,KAAK,SAAS,QAAQA,EAAC,CAAC,GAAEC,GAAEF,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAcC,IAAEC,IAAEC,IAAE;AAAC,SAAK,uBAAqB,MAAG,KAAK,qBAAqB,QAAS,CAACC,IAAEC,OAAI;AAAC,MAAAJ,GAAE,IAAIG,EAAC,MAAIF,GAAE,iBAAiBE,EAAC,GAAED,MAAGA,GAAE,mBAAmBC,EAAC,GAAE,KAAK,qBAAqB,OAAOC,EAAC;AAAA,IAAE,CAAE,GAAE,KAAK,OAAO,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,iBAAiBJ,IAAE;AAAC,SAAK,uBAAqB,MAAG,KAAK,uBAAuB,QAAS,CAACC,IAAEC,OAAI;AAAC,MAAAF,GAAE,IAAIE,EAAC,KAAG,KAAK,uBAAuB,OAAOA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeF,IAAEC,IAAE;AAAC,UAAME,KAAE,KAAK,yBAAyBH,IAAEC,EAAC;AAAE,WAAO,EAAEE,EAAC,IAAE,OAAKA,GAAE,YAAY;AAAA,EAAC;AAAA,EAAC,gBAAgBH,IAAE;AAAC,WAAO,KAAK,qBAAqB,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE,cAAcD,EAAC;AAAE,WAAO,KAAK,eAAeE,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBF,IAAE;AAAC,UAAMC,KAAE,KAAK,SAAS,cAAcD,EAAC,GAAEE,KAAE,EAAED,EAAC,GAAEE,KAAE,EAAEF,EAAC,GAAEG,KAAE,KAAK,eAAeF,EAAC;AAAE,WAAOE,MAAGA,GAAE,SAASD,EAAC,GAAEC,MAAG;AAAA,EAAI;AAAA,EAAC,eAAeJ,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,UAASC,KAAEH,GAAE,YAAY,GAAEI,KAAE,EAAEJ,GAAE,UAASA,GAAE,SAAS,CAAC;AAAE,IAAAE,GAAE,cAAcF,GAAE,aAAa,CAAC;AAAE,QAAIK,KAAE,KAAK,qBAAqB,IAAIF,EAAC;AAAE,IAAAE,OAAIA,KAAEH,GAAE,gBAAgB,GAAE,KAAK,qBAAqB,IAAIC,IAAEE,EAAC,GAAE,KAAK,uBAAqB,OAAIL,GAAE,aAAaK,EAAC,GAAEH,GAAE,cAAcG,IAAED,EAAC,GAAE,KAAK,sBAAsBF,IAAEF,IAAEK,IAAEJ,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAE;AAAC,SAAK,cAAc;AAAE,UAAMC,KAAE,EAAC,MAAKD,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,EAAC;AAAE,WAAO,KAAK,OAAO,OAAOC,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,QAAG,CAAC,KAAK,qBAAqB;AAAO,UAAMD,KAAE,CAAC;AAAE,mBAAa,KAAK,OAAK,KAAK,uBAAuB,QAAS,CAAAC,OAAG;AAAC,YAAMC,KAAED,GAAE,UAAU;AAAE,aAAKC,GAAE,KAAK,KAAG;AAAC,cAAMD,KAAEC,GAAE,aAAa;AAAE,aAAK,SAAS,UAAUD,IAAEC,EAAC,KAAGF,GAAE,KAAKC,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE,IAAE,KAAK,qBAAqB,QAAS,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,SAAS,cAAcD,EAAC;AAAE,WAAK,SAAS,UAAUA,IAAE,KAAK,eAAeC,EAAC,CAAC,KAAGF,GAAE,KAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,OAAO,MAAM,GAAE,KAAK,OAAO,KAAKD,EAAC,GAAE,KAAK,kBAAkB,MAAM,GAAE,KAAK,uBAAqB;AAAA,EAAE;AAAA,EAAC,eAAeA,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC,GAAEE,KAAE,KAAK,eAAeD,EAAC;AAAE,QAAG,CAACC,GAAE;AAAO,UAAMC,KAAED,GAAE,UAAU,GAAEE,KAAE,EAAEJ,EAAC;AAAE,WAAOG,GAAE,SAASC,EAAC,GAAED;AAAA,EAAC;AAAA,EAAC,eAAeH,IAAE;AAAC,WAAO,KAAK,uBAAuB,IAAIA,EAAC;AAAA,EAAC;AAAC;", "names": ["a", "t", "o", "u", "n", "o", "e", "s", "r", "a", "c", "n", "p", "t", "i", "r", "e", "t", "a", "c", "t", "e", "s", "a", "r", "n", "i"]}