{"version": 3, "sources": ["../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/glb.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/types.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/bufferview.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/buffer.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/geometry.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/gltf.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/asset.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/scene.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/node.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/index.js", "../../@arcgis/core/geometry/support/meshUtils/exporters/gltf/gltfexport.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar t;!function(t){t[t.JSON=1313821514]=\"JSON\",t[t.BIN=5130562]=\"BIN\"}(t||(t={}));class e{constructor(i,r){if(!i)throw new Error(\"GLB requires a JSON gltf chunk\");this._length=e.HEADER_SIZE,this._length+=e.CHUNK_HEADER_SIZE;const n=this._textToArrayBuffer(i);if(this._length+=this._alignTo(n.byteLength,4),r&&(this._length+=e.CHUNK_HEADER_SIZE,this._length+=r.byteLength,r.byteLength%4))throw new Error(\"Expected BIN chunk length to be divisible by 4 at this point\");this.buffer=new ArrayBuffer(this._length),this._outView=new DataView(this.buffer),this._writeHeader();const h=this._writeChunk(n,12,t.JSON,32);r&&this._writeChunk(r,h,t.BIN)}_writeHeader(){this._outView.setUint32(0,e.MAGIC,!0),this._outView.setUint32(4,e.VERSION,!0),this._outView.setUint32(8,this._length,!0)}_writeChunk(t,e,i,r=0){const n=this._alignTo(t.byteLength,4);for(this._outView.setUint32(e,n,!0),this._outView.setUint32(e+=4,i,!0),this._writeArrayBuffer(this._outView.buffer,t,e+=4,0,t.byteLength),e+=t.byteLength;e%4;)r&&this._outView.setUint8(e,r),e++;return e}_writeArrayBuffer(t,e,i,r,n){new Uint8Array(t,i,n).set(new Uint8Array(e,r,n),0)}_textToArrayBuffer(t){return(new TextEncoder).encode(t).buffer}_alignTo(t,e){return e*Math.ceil(t/e)}}e.HEADER_SIZE=12,e.CHUNK_HEADER_SIZE=8,e.MAGIC=1179937895,e.VERSION=2;export{e as GLB};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar E,A,R,L,o,I,N;!function(E){E[E.External=0]=\"External\",E[E.DataURI=1]=\"DataURI\",E[E.GLB=2]=\"GLB\"}(E||(E={})),function(E){E[E.External=0]=\"External\",E[E.DataURI=1]=\"DataURI\",E[E.GLB=2]=\"GLB\"}(A||(A={})),function(E){E[E.ARRAY_BUFFER=34962]=\"ARRAY_BUFFER\",E[E.ELEMENT_ARRAY_BUFFER=34963]=\"ELEMENT_ARRAY_BUFFER\"}(R||(R={})),function(E){E.SCALAR=\"SCALAR\",E.VEC2=\"VEC2\",E.VEC3=\"VEC3\",E.VEC4=\"VEC4\",E.MAT2=\"MAT2\",E.MAT3=\"MAT3\",E.MAT4=\"MAT4\"}(L||(L={})),function(E){E[E.POINTS=0]=\"POINTS\",E[E.LINES=1]=\"LINES\",E[E.LINE_LOOP=2]=\"LINE_LOOP\",E[E.LINE_STRIP=3]=\"LINE_STRIP\",E[E.TRIANGLES=4]=\"TRIANGLES\",E[E.TRIANGLE_STRIP=5]=\"TRIANGLE_STRIP\",E[E.TRIANGLE_FAN=6]=\"TRIANGLE_FAN\"}(o||(o={})),function(E){E.OPAQUE=\"OPAQUE\",E.MASK=\"MASK\",E.BLEND=\"BLEND\"}(I||(I={})),function(E){E[E.NoColor=0]=\"NoColor\",E[E.FaceColor=1]=\"FaceColor\",E[E.VertexColor=2]=\"VertexColor\"}(N||(N={}));export{I as AlphaMode,L as AttributeType,E as BufferOutputType,N as ColorMode,A as ImageOutputType,o as MeshMode,R as TargetBuffer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{eachAlways as e}from\"../../../../../core/promiseUtils.js\";import{TargetBuffer as t,AttributeType as s}from\"./types.js\";import{DataType as i}from\"../../../../../views/webgl/enums.js\";class r{constructor(e,s,i,r,n){this._buffer=e,this._componentType=i,this._dataType=r,this._data=[],this._isFinalized=!1,this._accessorIndex=-1,this._accessorAttribute=null,this._accessorMin=null,this._accessorMax=null,s.bufferViews||(s.bufferViews=[]),this.index=s.bufferViews.length,this._bufferView={buffer:e.index,byteLength:-1,target:n};const a=this._getElementSize();a>=4&&n!==t.ELEMENT_ARRAY_BUFFER&&(this._bufferView.byteStride=a),s.bufferViews.push(this._bufferView),this._numComponentsForDataType=this._calculateNumComponentsForDataType()}push(e){const t=this._data.length;if(this._data.push(e),this._accessorIndex>=0){const s=t%this._numComponentsForDataType,i=this._accessorMin[s];this._accessorMin[s]=\"number\"!=typeof i?e:Math.min(i,e);const r=this._accessorMax[s];this._accessorMax[s]=\"number\"!=typeof r?e:Math.max(r,e)}}get dataSize(){return this._data.length*this._sizeComponentType()}get byteSize(){function e(e,t){return t*Math.ceil(e/t)}return e(this.dataSize,4)}getByteOffset(){if(!this._isFinalized)throw new Error(\"Cannot get BufferView offset until it is finalized\");return this._buffer.getByteOffset(this)}get byteOffset(){if(!this._isFinalized)throw new Error(\"Cannot get BufferView offset until it is finalized\");return this._buffer.getByteOffset(this)}_createTypedArray(e,t){switch(this._componentType){case i.BYTE:return new Int8Array(e,t);case i.FLOAT:return new Float32Array(e,t);case i.SHORT:return new Int16Array(e,t);case i.UNSIGNED_BYTE:return new Uint8Array(e,t);case i.UNSIGNED_INT:return new Uint32Array(e,t);case i.UNSIGNED_SHORT:return new Uint16Array(e,t)}}writeOutToBuffer(e,t){this._createTypedArray(e,t).set(this._data)}writeAsync(e){if(this._asyncWritePromise)throw new Error(\"Can't write multiple bufferView values asynchronously\");return this._asyncWritePromise=e.then((e=>{const t=new Uint8Array(e);for(let s=0;s<t.length;++s)this._data.push(t[s]);delete this._asyncWritePromise})),this._asyncWritePromise}startAccessor(e){if(this._accessorIndex>=0)throw new Error(\"Accessor was started without ending the previous one\");this._accessorIndex=this._data.length,this._accessorAttribute=e;const t=this._numComponentsForDataType;this._accessorMin=new Array(t),this._accessorMax=new Array(t)}endAccessor(){if(this._accessorIndex<0)throw new Error(\"An accessor was not started, but was attempted to be ended\");const e=this._getElementSize(),t=this._numComponentsForDataType,s=(this._data.length-this._accessorIndex)/t;if(s%1)throw new Error(\"An accessor was ended with missing component values\");for(let i=0;i<this._accessorMin.length;++i)\"number\"!=typeof this._accessorMin[i]&&(this._accessorMin[i]=0),\"number\"!=typeof this._accessorMax[i]&&(this._accessorMax[i]=0);const r={byteOffset:e*(this._accessorIndex/t),componentType:this._componentType,count:s,type:this._dataType,min:this._accessorMin,max:this._accessorMax,name:this._accessorAttribute};switch(this._accessorAttribute){case\"TEXCOORD_0\":case\"TEXCOORD_1\":case\"COLOR_0\":case\"WEIGHTS_0\":switch(this._componentType){case i.UNSIGNED_BYTE:case i.UNSIGNED_SHORT:r.normalized=!0}}return this._accessorIndex=-1,this._accessorAttribute=null,this._accessorMin=null,this._accessorMax=null,r}get finalized(){return this._finalizedPromise?this._finalizedPromise:this._isFinalized?this._finalizedPromise=Promise.resolve():this._finalizedPromise=new Promise((e=>this._finalizedPromiseResolve=e))}finalize(){const t=this._bufferView;return new Promise((t=>{const s=this._buffer.getViewFinalizePromises(this);this._asyncWritePromise&&s.push(this._asyncWritePromise),t(e(s))})).then((()=>{this._isFinalized=!0,t.byteOffset=this.getByteOffset(),t.byteLength=this.dataSize,this._finalizedPromiseResolve&&this._finalizedPromiseResolve()}))}_getElementSize(){return this._sizeComponentType()*this._numComponentsForDataType}_sizeComponentType(){switch(this._componentType){case i.BYTE:case i.UNSIGNED_BYTE:return 1;case i.SHORT:case i.UNSIGNED_SHORT:return 2;case i.UNSIGNED_INT:case i.FLOAT:return 4}}_calculateNumComponentsForDataType(){switch(this._dataType){case s.SCALAR:return 1;case s.VEC2:return 2;case s.VEC3:return 3;case s.VEC4:case s.MAT2:return 4;case s.MAT3:return 9;case s.MAT4:return 16}}}export{r as BufferView};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{eachAlways as e}from\"../../../../../core/promiseUtils.js\";import{BufferView as i}from\"./bufferview.js\";class r{constructor(e){this._gltf=e,this._bufferViews=[],this._isFinalized=!1,e.buffers||(e.buffers=[]),this.index=e.buffers.length;const i={byteLength:-1};e.buffers.push(i),this._buffer=i}addBufferView(e,r,t){if(this._finalizePromise)throw new Error(\"Cannot add buffer view after fiinalizing buffer\");const f=new i(this,this._gltf,e,r,t);return this._bufferViews.push(f),f}getByteOffset(e){let i=0;for(const r of this._bufferViews){if(r===e)return i;i+=r.byteSize}throw new Error(\"Given bufferView was not present in this buffer\")}getViewFinalizePromises(e){const i=[];for(const r of this._bufferViews){if(e&&r===e)return i;i.push(r.finalized)}return i}getArrayBuffer(){if(!this._isFinalized)throw new Error(\"Cannot get ArrayBuffer from Buffer before it is finalized\");const e=this._getTotalSize(),i=new ArrayBuffer(e);let r=0;for(const t of this._bufferViews)t.writeOutToBuffer(i,r),r+=t.byteSize;return i}finalize(){if(this._finalizePromise)throw new Error(`Buffer ${this.index} was already finalized`);return this._finalizePromise=new Promise((i=>{i(e(this.getViewFinalizePromises()))})).then((()=>{this._isFinalized=!0;const e=this.getArrayBuffer();this._buffer.byteLength=e.byteLength,this._buffer.uri=e})),this._gltf.extras?.promises.push(this._finalizePromise),this._finalizePromise}_getTotalSize(){let e=0;for(const i of this._bufferViews)e+=i.byteSize;return e}}export{r as Buffer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t,isSome as n}from\"../../../../../core/maybe.js\";import{s as e,b as o,f as r,n as s}from\"../../../../../chunks/vec3.js\";import{c as a}from\"../../../../../chunks/vec3f64.js\";import i from\"../../../../Point.js\";function f(t,n){if(t.components)for(const e of t.components)e.faces&&\"smooth\"===e.shading&&c(e,n)}function c(n,a){t(a.normal)&&(a.normal=new Float32Array(a.position.length));const i=n.faces,{position:f,normal:c}=a,m=i.length/3;for(let t=0;t<m;++t){const n=3*i[3*t+0],s=3*i[3*t+1],a=3*i[3*t+2],m=e(l,f[n+0],f[n+1],f[n+2]),h=e(p,f[s+0],f[s+1],f[s+2]),g=e(x,f[a+0],f[a+1],f[a+2]),u=o(h,h,m),j=o(g,g,m),y=r(u,u,j);c[n+0]+=y[0],c[n+1]+=y[1],c[n+2]+=y[2],c[s+0]+=y[0],c[s+1]+=y[1],c[s+2]+=y[2],c[a+0]+=y[0],c[a+1]+=y[1],c[a+2]+=y[2]}for(let t=0;t<c.length;t+=3)e(h,c[t],c[t+1],c[t+2]),s(h,h),c[t+0]=h[0],c[t+1]=h[1],c[t+2]=h[2]}function m(t){if(n(t.transform))return t.transform.getOriginPoint(t.spatialReference);const e=t.extent.xmax-t.extent.width/2,o=t.extent.ymax-t.extent.height/2,r=t.extent.zmin;return new i({x:e,y:o,z:r,spatialReference:t.extent.spatialReference})}const l=a(),p=a(),x=a(),h=a();export{m as computeOrigin,f as smoothNormals};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/Logger.js\";import{getOrCreateMapValue as t}from\"../../../../../core/MapUtils.js\";import{applySome as s,isSome as r,isNone as i}from\"../../../../../core/maybe.js\";import{j as a}from\"../../../../../chunks/quat.js\";import{I as o,b as n}from\"../../../../../chunks/quatf64.js\";import{k as l}from\"../../../../../chunks/vec3.js\";import{Z as c,a as h,O as f}from\"../../../../../chunks/vec3f64.js\";import u from\"../../../MeshMaterialMetallicRoughness.js\";import p from\"../../../MeshTransform.js\";import{ungeoreferenceByTransform as d}from\"../../georeference.js\";import{Buffer as m}from\"./buffer.js\";import{computeOrigin as g,smoothNormals as _}from\"./geometry.js\";import{BufferOutputType as x,ImageOutputType as A,AttributeType as T,TargetBuffer as R,AlphaMode as b}from\"./types.js\";import{imageToArrayBuffer as E,imageToDataURI as M}from\"./imageutils.js\";import{isEncodedMeshTexture as O}from\"../../../../../views/3d/glTF/internal/resourceUtils.js\";import{DataType as w,TextureWrapMode as N}from\"../../../../../views/webgl/enums.js\";const B=e.getLogger(\"gltf\");class C{constructor(e,t,s){this.params={},this._materialMap=new Array,this._imageMap=new Map,this._textureMap=new Map,this.gltf={asset:{version:\"2.0\",copyright:e.copyright,generator:e.generator},extras:{options:t,binChunkBuffer:null,promises:[]}},s&&(this.params=s),this._addScenes(e)}_addScenes(e){this.gltf.scene=e.defaultScene;const t=this.gltf.extras,s=t.options.bufferOutputType===x.GLB||t.options.imageOutputType===A.GLB;s&&(t.binChunkBuffer=new m(this.gltf)),e.forEachScene((e=>{this._addScene(e)})),s&&t.binChunkBuffer.finalize()}_addScene(e){this.gltf.scenes||(this.gltf.scenes=[]);const t={};e.name&&(t.name=e.name),e.forEachNode((e=>{t.nodes||(t.nodes=[]);const s=this._addNode(e);t.nodes.push(s)})),this.gltf.scenes.push(t)}_addNode(e){this.gltf.nodes||(this.gltf.nodes=[]);const t={};e.name&&(t.name=e.name);const s=e.translation;l(s,c)||(t.translation=h(s));const r=e.rotation;a(r,o)||(t.rotation=n(r));const i=e.scale;l(i,f)||(t.scale=h(i)),e.mesh&&e.mesh.vertexAttributes.position?t.mesh=this._addMesh(e.mesh):e.forEachNode((e=>{t.children||(t.children=[]);const s=this._addNode(e);t.children.push(s)}));const u=this.gltf.nodes.length;return this.gltf.nodes.push(t),u}_addMesh(e){this.gltf.meshes||(this.gltf.meshes=[]);const t={primitives:[]},i=this.gltf.extras,a=i.options.bufferOutputType===x.GLB;let o;o=a?i.binChunkBuffer:new m(this.gltf),this.params.origin||(this.params.origin=g(e));const n=this.params.ignoreLocalTransform?s(e.transform,(e=>new p({origin:[e.origin[0],e.origin[1],e.origin[2]],geographic:!1}))):e.transform,l=d(e.vertexAttributes,n,this.params.origin,{geographic:this.params.geographic,unit:\"meters\"});_(e,l),this._flipYZAxis(l);const c=o.addBufferView(w.FLOAT,T.VEC3,R.ARRAY_BUFFER);let h,f,u,A;l.normal&&(h=o.addBufferView(w.FLOAT,T.VEC3,R.ARRAY_BUFFER)),e.vertexAttributes.uv&&(f=o.addBufferView(w.FLOAT,T.VEC2,R.ARRAY_BUFFER)),l.tangent&&(u=o.addBufferView(w.FLOAT,T.VEC4,R.ARRAY_BUFFER)),e.vertexAttributes.color&&(A=o.addBufferView(w.UNSIGNED_BYTE,T.VEC4,R.ARRAY_BUFFER)),c.startAccessor(\"POSITION\"),h&&h.startAccessor(\"NORMAL\"),f&&f.startAccessor(\"TEXCOORD_0\"),u&&u.startAccessor(\"TANGENT\"),A&&A.startAccessor(\"COLOR_0\");const b=l.position.length/3,{position:E,normal:M,tangent:O}=l,{color:N,uv:B}=e.vertexAttributes;for(let s=0;s<b;++s)c.push(E[3*s+0]),c.push(E[3*s+1]),c.push(E[3*s+2]),h&&r(M)&&(h.push(M[3*s+0]),h.push(M[3*s+1]),h.push(M[3*s+2])),f&&r(B)&&(f.push(B[2*s+0]),f.push(B[2*s+1])),u&&r(O)&&(u.push(O[4*s+0]),u.push(O[4*s+1]),u.push(O[4*s+2]),u.push(O[4*s+3])),A&&r(N)&&(A.push(N[4*s+0]),A.push(N[4*s+1]),A.push(N[4*s+2]),A.push(N[4*s+3]));const C=c.endAccessor(),I=this._addAccessor(c.index,C);let L,v,y,F,S;if(h){const e=h.endAccessor();L=this._addAccessor(h.index,e)}if(f){const e=f.endAccessor();v=this._addAccessor(f.index,e)}if(u){const e=u.endAccessor();y=this._addAccessor(u.index,e)}if(A){const e=A.endAccessor();F=this._addAccessor(A.index,e)}e.components&&e.components.length>0&&e.components[0].faces?(S=o.addBufferView(w.UNSIGNED_INT,T.SCALAR,R.ELEMENT_ARRAY_BUFFER),this._addMeshVertexIndexed(S,e.components,t,I,L,v,y,F)):this._addMeshVertexNonIndexed(e.components,t,I,L,v,y,F),c.finalize(),h&&h.finalize(),f&&f.finalize(),u&&u.finalize(),S&&S.finalize(),A&&A.finalize(),a||o.finalize();const k=this.gltf.meshes.length;return this.gltf.meshes.push(t),k}_flipYZAxis({position:e,normal:t,tangent:s}){this._flipYZBuffer(e,3),this._flipYZBuffer(t,3),this._flipYZBuffer(s,4)}_flipYZBuffer(e,t){if(!i(e))for(let s=1,r=2;s<e.length;s+=t,r+=t){const t=e[s],i=e[r];e[s]=i,e[r]=-t}}_addMaterial(e){if(null===e)return;const t=this._materialMap.indexOf(e);if(-1!==t)return t;this.gltf.materials||(this.gltf.materials=[]);const s={};switch(e.alphaMode){case\"mask\":s.alphaMode=b.MASK;break;case\"auto\":case\"blend\":s.alphaMode=b.BLEND}.5!==e.alphaCutoff&&(s.alphaCutoff=e.alphaCutoff),e.doubleSided&&(s.doubleSided=e.doubleSided),s.pbrMetallicRoughness={};const i=e=>e**2.1,a=e=>{const t=e.toRgba();return t[0]=i(t[0]/255),t[1]=i(t[1]/255),t[2]=i(t[2]/255),t};if(r(e.color)&&(s.pbrMetallicRoughness.baseColorFactor=a(e.color)),r(e.colorTexture)&&(s.pbrMetallicRoughness.baseColorTexture={index:this._addTexture(e.colorTexture)}),r(e.normalTexture)&&(s.normalTexture={index:this._addTexture(e.normalTexture)}),e instanceof u){if(r(e.emissiveTexture)&&(s.emissiveTexture={index:this._addTexture(e.emissiveTexture)}),r(e.emissiveColor)){const t=a(e.emissiveColor);s.emissiveFactor=[t[0],t[1],t[2]]}r(e.occlusionTexture)&&(s.occlusionTexture={index:this._addTexture(e.occlusionTexture)}),r(e.metallicRoughnessTexture)&&(s.pbrMetallicRoughness.metallicRoughnessTexture={index:this._addTexture(e.metallicRoughnessTexture)}),s.pbrMetallicRoughness.metallicFactor=e.metallic,s.pbrMetallicRoughness.roughnessFactor=e.roughness}else s.pbrMetallicRoughness.metallicFactor=1,s.pbrMetallicRoughness.roughnessFactor=1,B.warnOnce(\"Meshes exported to GLTF without MeshMaterialMetallicRoughness material will appear different when imported back.\");const o=this.gltf.materials.length;return this.gltf.materials.push(s),this._materialMap.push(e),o}_addTexture(e){const s=this.gltf.textures??[];return this.gltf.textures=s,t(this._textureMap,e,(()=>{const t={sampler:this._addSampler(e),source:this._addImage(e)},r=s.length;return s.push(t),r}))}_addImage(e){const t=this._imageMap.get(e);if(null!=t)return t;this.gltf.images||(this.gltf.images=[]);const s={};if(e.url)s.uri=e.url;else{const t=e.data;s.extras=t;for(let e=0;e<this.gltf.images.length;++e)if(t===this.gltf.images[e].extras)return e;const i=this.gltf.extras;switch(i.options.imageOutputType){case A.GLB:{const e=i.binChunkBuffer.addBufferView(w.UNSIGNED_BYTE,T.SCALAR);if(O(t))r(t.data)&&e.writeOutToBuffer(t.data,0);else{const r=E(t).then((({data:e,type:t})=>(s.mimeType=t,e)));e.writeAsync(r).then((()=>{e.finalize()}))}s.bufferView=e.index;break}case A.DataURI:if(O(t)){B.warnOnce(\"Image export for basis compressed textures not available.\");break}s.uri=M(t);break;default:if(O(t)){B.warnOnce(\"Image export for basis compressed textures not available.\");break}i.promises.push(E(t).then((({data:e,type:t})=>{s.uri=e,s.mimeType=t})))}}const i=this.gltf.images.length;return this.gltf.images.push(s),this._imageMap.set(e,i),i}_addSampler(e){this.gltf.samplers||(this.gltf.samplers=[]);let t=N.REPEAT,s=N.REPEAT;if(\"string\"==typeof e.wrap)switch(e.wrap){case\"clamp\":t=N.CLAMP_TO_EDGE,s=N.CLAMP_TO_EDGE;break;case\"mirror\":t=N.MIRRORED_REPEAT,s=N.MIRRORED_REPEAT}else{switch(e.wrap.vertical){case\"clamp\":s=N.CLAMP_TO_EDGE;break;case\"mirror\":s=N.MIRRORED_REPEAT}switch(e.wrap.horizontal){case\"clamp\":t=N.CLAMP_TO_EDGE;break;case\"mirror\":t=N.MIRRORED_REPEAT}}const r={wrapS:t,wrapT:s};for(let a=0;a<this.gltf.samplers.length;++a)if(JSON.stringify(r)===JSON.stringify(this.gltf.samplers[a]))return a;const i=this.gltf.samplers.length;return this.gltf.samplers.push(r),i}_addAccessor(e,t){this.gltf.accessors||(this.gltf.accessors=[]);const s={bufferView:e,byteOffset:t.byteOffset,componentType:t.componentType,count:t.count,type:t.type,min:t.min,max:t.max,name:t.name};t.normalized&&(s.normalized=!0);const r=this.gltf.accessors.length;return this.gltf.accessors.push(s),r}_addMeshVertexIndexed(e,t,s,r,i,a,o,n){for(const l of t){e.startAccessor(\"INDICES\");for(let s=0;s<l.faces.length;++s)e.push(l.faces[s]);const t=e.endAccessor(),c={attributes:{POSITION:r},indices:this._addAccessor(e.index,t),material:this._addMaterial(l.material)};i&&\"flat\"!==l.shading&&(c.attributes.NORMAL=i),a&&(c.attributes.TEXCOORD_0=a),o&&\"flat\"!==l.shading&&(c.attributes.TANGENT=o),n&&(c.attributes.COLOR_0=n),s.primitives.push(c)}}_addMeshVertexNonIndexed(e,t,s,r,i,a,o){const n={attributes:{POSITION:s}};r&&(n.attributes.NORMAL=r),i&&(n.attributes.TEXCOORD_0=i),a&&(n.attributes.TANGENT=a),o&&(n.attributes.COLOR_0=o),e&&(n.material=this._addMaterial(e[0].material)),t.primitives.push(n)}}export{C as GLTF};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{remove as e}from\"../../../../../core/arrayUtils.js\";class s{constructor(){this.copyright=\"\",this.defaultScene=0,this.generator=\"\",this._scenes=[]}addScene(e){if(this._scenes.includes(e))throw new Error(\"Scene already added\");this._scenes.push(e)}removeScene(s){e(this._scenes,s)}forEachScene(e){this._scenes.forEach(e)}}export{s as Asset};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass d{constructor(){this.name=\"\",this._nodes=[]}addNode(d){if(this._nodes.includes(d))throw new Error(\"Node already added\");this._nodes.push(d)}forEachNode(d){this._nodes.forEach(d)}}export{d as Scene};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{k as s}from\"../../../../../chunks/quat.js\";import{a as t}from\"../../../../../chunks/quatf64.js\";import{c as o,a,O as r}from\"../../../../../chunks/vec3f64.js\";class i{constructor(s){this.mesh=s,this.name=\"\",this.translation=o(),this.rotation=t(),this.scale=a(r),this._nodes=[]}addNode(s){if(this._nodes.includes(s))throw new Error(\"Node already added\");this._nodes.push(s)}forEachNode(s){this._nodes.forEach(s)}set rotationAngles(t){s(this.rotation,t[0],t[1],t[2])}}export{i as Node};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{eachAlways as e}from\"../../../../../core/promiseUtils.js\";import t from\"../../../../Point.js\";import{GLB as r}from\"./glb.js\";import{GLTF as o}from\"./gltf.js\";import{ImageOutputType as n,BufferOutputType as s}from\"./types.js\";export{AlphaMode,AttributeType,ColorMode,MeshMode}from\"./types.js\";import{isArrayBufferPNG as i,encodeBase64DataUri as a}from\"./imageutils.js\";export{Asset}from\"./asset.js\";export{Scene}from\"./scene.js\";export{Node}from\"./node.js\";const f=\"model.gltf\",u=\"model.glb\";function p(p,m,c){const l=new o(p,m=m||{},c);let g=l.params;g?g.origin||(g.origin=new t({x:-1,y:-1,z:-1})):g={origin:new t({x:-1,y:-1,z:-1})};const y=g.origin,d=l.gltf,j=d.extras?.promises??[];let x=1,b=1,h=null;return e(j).then((()=>{const e={origin:y};delete d.extras;const t=\"number\"==typeof m.jsonSpacing?m.jsonSpacing:4,o=JSON.stringify(d,((t,r)=>{if(\"extras\"!==t){if(r instanceof ArrayBuffer){if(i(r))switch(m.imageOutputType){case n.DataURI:case n.GLB:break;case n.External:default:{const t=`img${b}.png`;return b++,e[t]=r,t}}switch(m.bufferOutputType){case s.DataURI:return a(r);case s.GLB:if(h)throw new Error(\"Already encountered an ArrayBuffer, there should only be one in the GLB format.\");return void(h=r);case s.External:default:{const t=`data${x}.bin`;return x++,e[t]=r,t}}}return r}}),t);return m.bufferOutputType===s.GLB||m.imageOutputType===n.GLB?e[u]=new r(o,h).buffer:e[f]=o,e}))}function m(e,t){return p(e,{bufferOutputType:s.GLB,imageOutputType:n.GLB,jsonSpacing:0},t)}export{s as BufferOutputType,n as ImageOutputType,u as MODEL_NAME_GLB,f as MODEL_NAME_GLTF,m as exportGLB,p as exportGLTF};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{downloadBlobAsFile as e}from\"../../../../../core/urlUtils.js\";import{exportGLB as o,MODEL_NAME_GLB as t}from\"./index.js\";import{Node as r}from\"./node.js\";import{Asset as i}from\"./asset.js\";import{Scene as n}from\"./scene.js\";class s{constructor(e,o){this._file={type:\"model/gltf-binary\",data:e},this.origin=o}buffer(){return Promise.resolve(this._file)}download(o){e(new Blob([this._file.data],{type:this._file.type}),o)}}function f(e,f){const d=new i,l=new n;return d.addScene(l),l.addNode(new r(e)),o(d,f).then((e=>new s(e[t],e.origin)))}export{f as toBinaryGLTF};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAIA;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,UAAU,IAAE,QAAOA,GAAEA,GAAE,MAAI,OAAO,IAAE;AAAK,EAAEA,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,QAAG,CAACD,GAAE,OAAM,IAAI,MAAM,gCAAgC;AAAE,SAAK,UAAQ,GAAE,aAAY,KAAK,WAAS,GAAE;AAAkB,UAAME,KAAE,KAAK,mBAAmBF,EAAC;AAAE,QAAG,KAAK,WAAS,KAAK,SAASE,GAAE,YAAW,CAAC,GAAED,OAAI,KAAK,WAAS,GAAE,mBAAkB,KAAK,WAASA,GAAE,YAAWA,GAAE,aAAW,GAAG,OAAM,IAAI,MAAM,8DAA8D;AAAE,SAAK,SAAO,IAAI,YAAY,KAAK,OAAO,GAAE,KAAK,WAAS,IAAI,SAAS,KAAK,MAAM,GAAE,KAAK,aAAa;AAAE,UAAME,KAAE,KAAK,YAAYD,IAAE,IAAGJ,GAAE,MAAK,EAAE;AAAE,IAAAG,MAAG,KAAK,YAAYA,IAAEE,IAAEL,GAAE,GAAG;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,SAAK,SAAS,UAAU,GAAE,GAAE,OAAM,IAAE,GAAE,KAAK,SAAS,UAAU,GAAE,GAAE,SAAQ,IAAE,GAAE,KAAK,SAAS,UAAU,GAAE,KAAK,SAAQ,IAAE;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAEC,KAAE,GAAE;AAAC,UAAMC,KAAE,KAAK,SAASJ,GAAE,YAAW,CAAC;AAAE,SAAI,KAAK,SAAS,UAAUC,IAAEG,IAAE,IAAE,GAAE,KAAK,SAAS,UAAUH,MAAG,GAAEC,IAAE,IAAE,GAAE,KAAK,kBAAkB,KAAK,SAAS,QAAOF,IAAEC,MAAG,GAAE,GAAED,GAAE,UAAU,GAAEC,MAAGD,GAAE,YAAWC,KAAE,IAAG,CAAAE,MAAG,KAAK,SAAS,SAASF,IAAEE,EAAC,GAAEF;AAAI,WAAOA;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAI,WAAWJ,IAAEE,IAAEE,EAAC,EAAE,IAAI,IAAI,WAAWH,IAAEE,IAAEC,EAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBJ,IAAE;AAAC,WAAO,IAAI,cAAa,OAAOA,EAAC,EAAE;AAAA,EAAM;AAAA,EAAC,SAASA,IAAEC,IAAE;AAAC,WAAOA,KAAE,KAAK,KAAKD,KAAEC,EAAC;AAAA,EAAC;AAAC;AAACA,GAAE,cAAY,IAAGA,GAAE,oBAAkB,GAAEA,GAAE,QAAM,YAAWA,GAAE,UAAQ;;;ACA3wC,IAAIK;AAAJ,IAAM;AAAN,IAAQ;AAAR,IAAUC;AAAV,IAAYC;AAAZ,IAAc;AAAd,IAAgB;AAAE,CAAC,SAASF,IAAE;AAAC,EAAAA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,MAAI,CAAC,IAAE;AAAK,EAAEA,OAAIA,KAAE,CAAC,EAAE,GAAE,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,MAAI,CAAC,IAAE;AAAK,EAAE,MAAI,IAAE,CAAC,EAAE,GAAE,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,eAAa,KAAK,IAAE,gBAAeA,GAAEA,GAAE,uBAAqB,KAAK,IAAE;AAAsB,EAAE,MAAI,IAAE,CAAC,EAAE,GAAE,SAASA,IAAE;AAAC,EAAAA,GAAE,SAAO,UAASA,GAAE,OAAK,QAAOA,GAAE,OAAK,QAAOA,GAAE,OAAK,QAAOA,GAAE,OAAK,QAAOA,GAAE,OAAK,QAAOA,GAAE,OAAK;AAAM,EAAEC,OAAIA,KAAE,CAAC,EAAE,GAAE,SAASD,IAAE;AAAC,EAAAA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,iBAAe,CAAC,IAAE,kBAAiBA,GAAEA,GAAE,eAAa,CAAC,IAAE;AAAc,EAAEE,OAAIA,KAAE,CAAC,EAAE,GAAE,SAASF,IAAE;AAAC,EAAAA,GAAE,SAAO,UAASA,GAAE,OAAK,QAAOA,GAAE,QAAM;AAAO,EAAE,MAAI,IAAE,CAAC,EAAE,GAAE,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,cAAY,CAAC,IAAE;AAAa,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACAhqB,IAAMG,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEH,IAAEI,IAAE;AAAC,SAAK,UAAQH,IAAE,KAAK,iBAAeE,IAAE,KAAK,YAAUH,IAAE,KAAK,QAAM,CAAC,GAAE,KAAK,eAAa,OAAG,KAAK,iBAAe,IAAG,KAAK,qBAAmB,MAAK,KAAK,eAAa,MAAK,KAAK,eAAa,MAAKE,GAAE,gBAAcA,GAAE,cAAY,CAAC,IAAG,KAAK,QAAMA,GAAE,YAAY,QAAO,KAAK,cAAY,EAAC,QAAOD,GAAE,OAAM,YAAW,IAAG,QAAOG,GAAC;AAAE,UAAM,IAAE,KAAK,gBAAgB;AAAE,SAAG,KAAGA,OAAI,EAAE,yBAAuB,KAAK,YAAY,aAAW,IAAGF,GAAE,YAAY,KAAK,KAAK,WAAW,GAAE,KAAK,4BAA0B,KAAK,mCAAmC;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAE;AAAC,UAAMI,KAAE,KAAK,MAAM;AAAO,QAAG,KAAK,MAAM,KAAKJ,EAAC,GAAE,KAAK,kBAAgB,GAAE;AAAC,YAAMC,KAAEG,KAAE,KAAK,2BAA0BF,KAAE,KAAK,aAAaD,EAAC;AAAE,WAAK,aAAaA,EAAC,IAAE,YAAU,OAAOC,KAAEF,KAAE,KAAK,IAAIE,IAAEF,EAAC;AAAE,YAAMD,KAAE,KAAK,aAAaE,EAAC;AAAE,WAAK,aAAaA,EAAC,IAAE,YAAU,OAAOF,KAAEC,KAAE,KAAK,IAAID,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,MAAM,SAAO,KAAK,mBAAmB;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,aAASA,GAAEA,IAAEI,IAAE;AAAC,aAAOA,KAAE,KAAK,KAAKJ,KAAEI,EAAC;AAAA,IAAC;AAAC,WAAOJ,GAAE,KAAK,UAAS,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,QAAG,CAAC,KAAK,aAAa,OAAM,IAAI,MAAM,oDAAoD;AAAE,WAAO,KAAK,QAAQ,cAAc,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,QAAG,CAAC,KAAK,aAAa,OAAM,IAAI,MAAM,oDAAoD;AAAE,WAAO,KAAK,QAAQ,cAAc,IAAI;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEI,IAAE;AAAC,YAAO,KAAK,gBAAe;AAAA,MAAC,KAAK,EAAE;AAAK,eAAO,IAAI,UAAUJ,IAAEI,EAAC;AAAA,MAAE,KAAK,EAAE;AAAM,eAAO,IAAI,aAAaJ,IAAEI,EAAC;AAAA,MAAE,KAAK,EAAE;AAAM,eAAO,IAAI,WAAWJ,IAAEI,EAAC;AAAA,MAAE,KAAK,EAAE;AAAc,eAAO,IAAI,WAAWJ,IAAEI,EAAC;AAAA,MAAE,KAAK,EAAE;AAAa,eAAO,IAAI,YAAYJ,IAAEI,EAAC;AAAA,MAAE,KAAK,EAAE;AAAe,eAAO,IAAI,YAAYJ,IAAEI,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBJ,IAAEI,IAAE;AAAC,SAAK,kBAAkBJ,IAAEI,EAAC,EAAE,IAAI,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,WAAWJ,IAAE;AAAC,QAAG,KAAK,mBAAmB,OAAM,IAAI,MAAM,uDAAuD;AAAE,WAAO,KAAK,qBAAmBA,GAAE,KAAM,CAAAA,OAAG;AAAC,YAAMI,KAAE,IAAI,WAAWJ,EAAC;AAAE,eAAQC,KAAE,GAAEA,KAAEG,GAAE,QAAO,EAAEH,GAAE,MAAK,MAAM,KAAKG,GAAEH,EAAC,CAAC;AAAE,aAAO,KAAK;AAAA,IAAkB,CAAE,GAAE,KAAK;AAAA,EAAkB;AAAA,EAAC,cAAcD,IAAE;AAAC,QAAG,KAAK,kBAAgB,EAAE,OAAM,IAAI,MAAM,sDAAsD;AAAE,SAAK,iBAAe,KAAK,MAAM,QAAO,KAAK,qBAAmBA;AAAE,UAAMI,KAAE,KAAK;AAA0B,SAAK,eAAa,IAAI,MAAMA,EAAC,GAAE,KAAK,eAAa,IAAI,MAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,QAAG,KAAK,iBAAe,EAAE,OAAM,IAAI,MAAM,4DAA4D;AAAE,UAAMJ,KAAE,KAAK,gBAAgB,GAAEI,KAAE,KAAK,2BAA0BH,MAAG,KAAK,MAAM,SAAO,KAAK,kBAAgBG;AAAE,QAAGH,KAAE,EAAE,OAAM,IAAI,MAAM,qDAAqD;AAAE,aAAQC,KAAE,GAAEA,KAAE,KAAK,aAAa,QAAO,EAAEA,GAAE,aAAU,OAAO,KAAK,aAAaA,EAAC,MAAI,KAAK,aAAaA,EAAC,IAAE,IAAG,YAAU,OAAO,KAAK,aAAaA,EAAC,MAAI,KAAK,aAAaA,EAAC,IAAE;AAAG,UAAMH,KAAE,EAAC,YAAWC,MAAG,KAAK,iBAAeI,KAAG,eAAc,KAAK,gBAAe,OAAMH,IAAE,MAAK,KAAK,WAAU,KAAI,KAAK,cAAa,KAAI,KAAK,cAAa,MAAK,KAAK,mBAAkB;AAAE,YAAO,KAAK,oBAAmB;AAAA,MAAC,KAAI;AAAA,MAAa,KAAI;AAAA,MAAa,KAAI;AAAA,MAAU,KAAI;AAAY,gBAAO,KAAK,gBAAe;AAAA,UAAC,KAAK,EAAE;AAAA,UAAc,KAAK,EAAE;AAAe,YAAAF,GAAE,aAAW;AAAA,QAAE;AAAA,IAAC;AAAC,WAAO,KAAK,iBAAe,IAAG,KAAK,qBAAmB,MAAK,KAAK,eAAa,MAAK,KAAK,eAAa,MAAKA;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,oBAAkB,KAAK,oBAAkB,KAAK,eAAa,KAAK,oBAAkB,QAAQ,QAAQ,IAAE,KAAK,oBAAkB,IAAI,QAAS,CAAAC,OAAG,KAAK,2BAAyBA,EAAE;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,UAAMI,KAAE,KAAK;AAAY,WAAO,IAAI,QAAS,CAAAA,OAAG;AAAC,YAAMH,KAAE,KAAK,QAAQ,wBAAwB,IAAI;AAAE,WAAK,sBAAoBA,GAAE,KAAK,KAAK,kBAAkB,GAAEG,GAAE,EAAEH,EAAC,CAAC;AAAA,IAAC,CAAE,EAAE,KAAM,MAAI;AAAC,WAAK,eAAa,MAAGG,GAAE,aAAW,KAAK,cAAc,GAAEA,GAAE,aAAW,KAAK,UAAS,KAAK,4BAA0B,KAAK,yBAAyB;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK,mBAAmB,IAAE,KAAK;AAAA,EAAyB;AAAA,EAAC,qBAAoB;AAAC,YAAO,KAAK,gBAAe;AAAA,MAAC,KAAK,EAAE;AAAA,MAAK,KAAK,EAAE;AAAc,eAAO;AAAA,MAAE,KAAK,EAAE;AAAA,MAAM,KAAK,EAAE;AAAe,eAAO;AAAA,MAAE,KAAK,EAAE;AAAA,MAAa,KAAK,EAAE;AAAM,eAAO;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,qCAAoC;AAAC,YAAO,KAAK,WAAU;AAAA,MAAC,KAAKC,GAAE;AAAO,eAAO;AAAA,MAAE,KAAKA,GAAE;AAAK,eAAO;AAAA,MAAE,KAAKA,GAAE;AAAK,eAAO;AAAA,MAAE,KAAKA,GAAE;AAAA,MAAK,KAAKA,GAAE;AAAK,eAAO;AAAA,MAAE,KAAKA,GAAE;AAAK,eAAO;AAAA,MAAE,KAAKA,GAAE;AAAK,eAAO;AAAA,IAAE;AAAA,EAAC;AAAC;;;ACAnqI,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,QAAMA,IAAE,KAAK,eAAa,CAAC,GAAE,KAAK,eAAa,OAAGA,GAAE,YAAUA,GAAE,UAAQ,CAAC,IAAG,KAAK,QAAMA,GAAE,QAAQ;AAAO,UAAMC,KAAE,EAAC,YAAW,GAAE;AAAE,IAAAD,GAAE,QAAQ,KAAKC,EAAC,GAAE,KAAK,UAAQA;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAED,IAAEG,IAAE;AAAC,QAAG,KAAK,iBAAiB,OAAM,IAAI,MAAM,iDAAiD;AAAE,UAAMC,KAAE,IAAIJ,GAAE,MAAK,KAAK,OAAMC,IAAED,IAAEG,EAAC;AAAE,WAAO,KAAK,aAAa,KAAKC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,cAAcH,IAAE;AAAC,QAAIC,KAAE;AAAE,eAAUF,MAAK,KAAK,cAAa;AAAC,UAAGA,OAAIC,GAAE,QAAOC;AAAE,MAAAA,MAAGF,GAAE;AAAA,IAAQ;AAAC,UAAM,IAAI,MAAM,iDAAiD;AAAA,EAAC;AAAA,EAAC,wBAAwBC,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAUF,MAAK,KAAK,cAAa;AAAC,UAAGC,MAAGD,OAAIC,GAAE,QAAOC;AAAE,MAAAA,GAAE,KAAKF,GAAE,SAAS;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,QAAG,CAAC,KAAK,aAAa,OAAM,IAAI,MAAM,2DAA2D;AAAE,UAAMD,KAAE,KAAK,cAAc,GAAEC,KAAE,IAAI,YAAYD,EAAC;AAAE,QAAID,KAAE;AAAE,eAAUG,MAAK,KAAK,aAAa,CAAAA,GAAE,iBAAiBD,IAAEF,EAAC,GAAEA,MAAGG,GAAE;AAAS,WAAOD;AAAA,EAAC;AAAA,EAAC,WAAU;AAJpgC;AAIqgC,QAAG,KAAK,iBAAiB,OAAM,IAAI,MAAM,UAAU,KAAK,KAAK,wBAAwB;AAAE,WAAO,KAAK,mBAAiB,IAAI,QAAS,CAAAA,OAAG;AAAC,MAAAA,GAAE,EAAE,KAAK,wBAAwB,CAAC,CAAC;AAAA,IAAC,CAAE,EAAE,KAAM,MAAI;AAAC,WAAK,eAAa;AAAG,YAAMD,KAAE,KAAK,eAAe;AAAE,WAAK,QAAQ,aAAWA,GAAE,YAAW,KAAK,QAAQ,MAAIA;AAAA,IAAC,CAAE,IAAE,UAAK,MAAM,WAAX,mBAAmB,SAAS,KAAK,KAAK,mBAAkB,KAAK;AAAA,EAAgB;AAAA,EAAC,gBAAe;AAAC,QAAIA,KAAE;AAAE,eAAUC,MAAK,KAAK,aAAa,CAAAD,MAAGC,GAAE;AAAS,WAAOD;AAAA,EAAC;AAAC;;;ACAvuC,SAASI,GAAEC,IAAEC,IAAE;AAAC,MAAGD,GAAE,WAAW,YAAUE,MAAKF,GAAE,WAAW,CAAAE,GAAE,SAAO,aAAWA,GAAE,WAASC,GAAED,IAAED,EAAC;AAAC;AAAC,SAASE,GAAEF,IAAE,GAAE;AAAC,IAAE,EAAE,MAAM,MAAI,EAAE,SAAO,IAAI,aAAa,EAAE,SAAS,MAAM;AAAG,QAAMG,KAAEH,GAAE,OAAM,EAAC,UAASF,IAAE,QAAOI,GAAC,IAAE,GAAEE,KAAED,GAAE,SAAO;AAAE,WAAQJ,KAAE,GAAEA,KAAEK,IAAE,EAAEL,IAAE;AAAC,UAAMC,KAAE,IAAEG,GAAE,IAAEJ,KAAE,CAAC,GAAEM,KAAE,IAAEF,GAAE,IAAEJ,KAAE,CAAC,GAAEO,KAAE,IAAEH,GAAE,IAAEJ,KAAE,CAAC,GAAEK,KAAEG,GAAEC,IAAEV,GAAEE,KAAE,CAAC,GAAEF,GAAEE,KAAE,CAAC,GAAEF,GAAEE,KAAE,CAAC,CAAC,GAAES,KAAEF,GAAE,GAAET,GAAEO,KAAE,CAAC,GAAEP,GAAEO,KAAE,CAAC,GAAEP,GAAEO,KAAE,CAAC,CAAC,GAAEK,KAAEH,GAAE,GAAET,GAAEQ,KAAE,CAAC,GAAER,GAAEQ,KAAE,CAAC,GAAER,GAAEQ,KAAE,CAAC,CAAC,GAAEK,KAAE,EAAEF,IAAEA,IAAEL,EAAC,GAAE,IAAE,EAAEM,IAAEA,IAAEN,EAAC,GAAE,IAAE,EAAEO,IAAEA,IAAE,CAAC;AAAE,IAAAT,GAAEF,KAAE,CAAC,KAAG,EAAE,CAAC,GAAEE,GAAEF,KAAE,CAAC,KAAG,EAAE,CAAC,GAAEE,GAAEF,KAAE,CAAC,KAAG,EAAE,CAAC,GAAEE,GAAEG,KAAE,CAAC,KAAG,EAAE,CAAC,GAAEH,GAAEG,KAAE,CAAC,KAAG,EAAE,CAAC,GAAEH,GAAEG,KAAE,CAAC,KAAG,EAAE,CAAC,GAAEH,GAAEI,KAAE,CAAC,KAAG,EAAE,CAAC,GAAEJ,GAAEI,KAAE,CAAC,KAAG,EAAE,CAAC,GAAEJ,GAAEI,KAAE,CAAC,KAAG,EAAE,CAAC;AAAA,EAAC;AAAC,WAAQP,KAAE,GAAEA,KAAEG,GAAE,QAAOH,MAAG,EAAE,CAAAQ,GAAE,GAAEL,GAAEH,EAAC,GAAEG,GAAEH,KAAE,CAAC,GAAEG,GAAEH,KAAE,CAAC,CAAC,GAAE,EAAE,GAAE,CAAC,GAAEG,GAAEH,KAAE,CAAC,IAAE,EAAE,CAAC,GAAEG,GAAEH,KAAE,CAAC,IAAE,EAAE,CAAC,GAAEG,GAAEH,KAAE,CAAC,IAAE,EAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAG,EAAEA,GAAE,SAAS,EAAE,QAAOA,GAAE,UAAU,eAAeA,GAAE,gBAAgB;AAAE,QAAME,KAAEF,GAAE,OAAO,OAAKA,GAAE,OAAO,QAAM,GAAEQ,KAAER,GAAE,OAAO,OAAKA,GAAE,OAAO,SAAO,GAAEa,KAAEb,GAAE,OAAO;AAAK,SAAO,IAAI,EAAE,EAAC,GAAEE,IAAE,GAAEM,IAAE,GAAEK,IAAE,kBAAiBb,GAAE,OAAO,iBAAgB,CAAC;AAAC;AAAC,IAAMS,KAAE,EAAE;AAAV,IAAY,IAAE,EAAE;AAAhB,IAAkB,IAAE,EAAE;AAAtB,IAAwB,IAAE,EAAE;;;ACA/D,IAAM,IAAE,EAAE,UAAU,MAAM;AAAE,IAAMK,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAE;AAAC,SAAK,SAAO,CAAC,GAAE,KAAK,eAAa,IAAI,SAAM,KAAK,YAAU,oBAAI,OAAI,KAAK,cAAY,oBAAI,OAAI,KAAK,OAAK,EAAC,OAAM,EAAC,SAAQ,OAAM,WAAUF,GAAE,WAAU,WAAUA,GAAE,UAAS,GAAE,QAAO,EAAC,SAAQC,IAAE,gBAAe,MAAK,UAAS,CAAC,EAAC,EAAC,GAAEC,OAAI,KAAK,SAAOA,KAAG,KAAK,WAAWF,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,SAAK,KAAK,QAAMA,GAAE;AAAa,UAAMC,KAAE,KAAK,KAAK,QAAOC,KAAED,GAAE,QAAQ,qBAAmBE,GAAE,OAAKF,GAAE,QAAQ,oBAAkB,EAAE;AAAI,IAAAC,OAAID,GAAE,iBAAe,IAAIG,GAAE,KAAK,IAAI,IAAGJ,GAAE,aAAc,CAAAA,OAAG;AAAC,WAAK,UAAUA,EAAC;AAAA,IAAC,CAAE,GAAEE,MAAGD,GAAE,eAAe,SAAS;AAAA,EAAC;AAAA,EAAC,UAAUD,IAAE;AAAC,SAAK,KAAK,WAAS,KAAK,KAAK,SAAO,CAAC;AAAG,UAAMC,KAAE,CAAC;AAAE,IAAAD,GAAE,SAAOC,GAAE,OAAKD,GAAE,OAAMA,GAAE,YAAa,CAAAA,OAAG;AAAC,MAAAC,GAAE,UAAQA,GAAE,QAAM,CAAC;AAAG,YAAMC,KAAE,KAAK,SAASF,EAAC;AAAE,MAAAC,GAAE,MAAM,KAAKC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,KAAK,OAAO,KAAKD,EAAC;AAAA,EAAC;AAAA,EAAC,SAASD,IAAE;AAAC,SAAK,KAAK,UAAQ,KAAK,KAAK,QAAM,CAAC;AAAG,UAAMC,KAAE,CAAC;AAAE,IAAAD,GAAE,SAAOC,GAAE,OAAKD,GAAE;AAAM,UAAME,KAAEF,GAAE;AAAY,IAAAK,GAAEH,IAAE,CAAC,MAAID,GAAE,cAAYA,GAAEC,EAAC;AAAG,UAAME,KAAEJ,GAAE;AAAS,MAAEI,IAAEE,EAAC,MAAIL,GAAE,WAASG,GAAEA,EAAC;AAAG,UAAMG,KAAEP,GAAE;AAAM,IAAAK,GAAEE,IAAE,CAAC,MAAIN,GAAE,QAAMA,GAAEM,EAAC,IAAGP,GAAE,QAAMA,GAAE,KAAK,iBAAiB,WAASC,GAAE,OAAK,KAAK,SAASD,GAAE,IAAI,IAAEA,GAAE,YAAa,CAAAA,OAAG;AAAC,MAAAC,GAAE,aAAWA,GAAE,WAAS,CAAC;AAAG,YAAMC,KAAE,KAAK,SAASF,EAAC;AAAE,MAAAC,GAAE,SAAS,KAAKC,EAAC;AAAA,IAAC,CAAE;AAAE,UAAMM,KAAE,KAAK,KAAK,MAAM;AAAO,WAAO,KAAK,KAAK,MAAM,KAAKP,EAAC,GAAEO;AAAA,EAAC;AAAA,EAAC,SAASR,IAAE;AAAC,SAAK,KAAK,WAAS,KAAK,KAAK,SAAO,CAAC;AAAG,UAAMC,KAAE,EAAC,YAAW,CAAC,EAAC,GAAEM,KAAE,KAAK,KAAK,QAAO,IAAEA,GAAE,QAAQ,qBAAmBJ,GAAE;AAAI,QAAIG;AAAE,IAAAA,KAAE,IAAEC,GAAE,iBAAe,IAAIH,GAAE,KAAK,IAAI,GAAE,KAAK,OAAO,WAAS,KAAK,OAAO,SAAO,EAAEJ,EAAC;AAAG,UAAMS,KAAE,KAAK,OAAO,uBAAqB,EAAET,GAAE,WAAW,CAAAA,OAAG,IAAI,EAAE,EAAC,QAAO,CAACA,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC,GAAE,YAAW,MAAE,CAAC,CAAE,IAAEA,GAAE,WAAUU,KAAE,EAAEV,GAAE,kBAAiBS,IAAE,KAAK,OAAO,QAAO,EAAC,YAAW,KAAK,OAAO,YAAW,MAAK,SAAQ,CAAC;AAAE,IAAAE,GAAEX,IAAEU,EAAC,GAAE,KAAK,YAAYA,EAAC;AAAE,UAAME,KAAEN,GAAE,cAAc,EAAE,OAAMO,GAAE,MAAK,EAAE,YAAY;AAAE,QAAIC,IAAEH,IAAEH,IAAEO;AAAE,IAAAL,GAAE,WAASI,KAAER,GAAE,cAAc,EAAE,OAAMO,GAAE,MAAK,EAAE,YAAY,IAAGb,GAAE,iBAAiB,OAAKW,KAAEL,GAAE,cAAc,EAAE,OAAMO,GAAE,MAAK,EAAE,YAAY,IAAGH,GAAE,YAAUF,KAAEF,GAAE,cAAc,EAAE,OAAMO,GAAE,MAAK,EAAE,YAAY,IAAGb,GAAE,iBAAiB,UAAQe,KAAET,GAAE,cAAc,EAAE,eAAcO,GAAE,MAAK,EAAE,YAAY,IAAGD,GAAE,cAAc,UAAU,GAAEE,MAAGA,GAAE,cAAc,QAAQ,GAAEH,MAAGA,GAAE,cAAc,YAAY,GAAEH,MAAGA,GAAE,cAAc,SAAS,GAAEO,MAAGA,GAAE,cAAc,SAAS;AAAE,UAAM,IAAEL,GAAE,SAAS,SAAO,GAAE,EAAC,UAASP,IAAE,QAAOa,IAAE,SAAQ,EAAC,IAAEN,IAAE,EAAC,OAAMO,IAAE,IAAGC,GAAC,IAAElB,GAAE;AAAiB,aAAQE,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAU,GAAE,KAAKT,GAAE,IAAED,KAAE,CAAC,CAAC,GAAEU,GAAE,KAAKT,GAAE,IAAED,KAAE,CAAC,CAAC,GAAEU,GAAE,KAAKT,GAAE,IAAED,KAAE,CAAC,CAAC,GAAEY,MAAG,EAAEE,EAAC,MAAIF,GAAE,KAAKE,GAAE,IAAEd,KAAE,CAAC,CAAC,GAAEY,GAAE,KAAKE,GAAE,IAAEd,KAAE,CAAC,CAAC,GAAEY,GAAE,KAAKE,GAAE,IAAEd,KAAE,CAAC,CAAC,IAAGS,MAAG,EAAEO,EAAC,MAAIP,GAAE,KAAKO,GAAE,IAAEhB,KAAE,CAAC,CAAC,GAAES,GAAE,KAAKO,GAAE,IAAEhB,KAAE,CAAC,CAAC,IAAGM,MAAG,EAAE,CAAC,MAAIA,GAAE,KAAK,EAAE,IAAEN,KAAE,CAAC,CAAC,GAAEM,GAAE,KAAK,EAAE,IAAEN,KAAE,CAAC,CAAC,GAAEM,GAAE,KAAK,EAAE,IAAEN,KAAE,CAAC,CAAC,GAAEM,GAAE,KAAK,EAAE,IAAEN,KAAE,CAAC,CAAC,IAAGa,MAAG,EAAEE,EAAC,MAAIF,GAAE,KAAKE,GAAE,IAAEf,KAAE,CAAC,CAAC,GAAEa,GAAE,KAAKE,GAAE,IAAEf,KAAE,CAAC,CAAC,GAAEa,GAAE,KAAKE,GAAE,IAAEf,KAAE,CAAC,CAAC,GAAEa,GAAE,KAAKE,GAAE,IAAEf,KAAE,CAAC,CAAC;AAAG,UAAMH,KAAEa,GAAE,YAAY,GAAEO,KAAE,KAAK,aAAaP,GAAE,OAAMb,EAAC;AAAE,QAAIc,IAAE,GAAE,GAAER,IAAE;AAAE,QAAGS,IAAE;AAAC,YAAMd,KAAEc,GAAE,YAAY;AAAE,MAAAD,KAAE,KAAK,aAAaC,GAAE,OAAMd,EAAC;AAAA,IAAC;AAAC,QAAGW,IAAE;AAAC,YAAMX,KAAEW,GAAE,YAAY;AAAE,UAAE,KAAK,aAAaA,GAAE,OAAMX,EAAC;AAAA,IAAC;AAAC,QAAGQ,IAAE;AAAC,YAAMR,KAAEQ,GAAE,YAAY;AAAE,UAAE,KAAK,aAAaA,GAAE,OAAMR,EAAC;AAAA,IAAC;AAAC,QAAGe,IAAE;AAAC,YAAMf,KAAEe,GAAE,YAAY;AAAE,MAAAV,KAAE,KAAK,aAAaU,GAAE,OAAMf,EAAC;AAAA,IAAC;AAAC,IAAAA,GAAE,cAAYA,GAAE,WAAW,SAAO,KAAGA,GAAE,WAAW,CAAC,EAAE,SAAO,IAAEM,GAAE,cAAc,EAAE,cAAaO,GAAE,QAAO,EAAE,oBAAoB,GAAE,KAAK,sBAAsB,GAAEb,GAAE,YAAWC,IAAEkB,IAAEN,IAAE,GAAE,GAAER,EAAC,KAAG,KAAK,yBAAyBL,GAAE,YAAWC,IAAEkB,IAAEN,IAAE,GAAE,GAAER,EAAC,GAAEO,GAAE,SAAS,GAAEE,MAAGA,GAAE,SAAS,GAAEH,MAAGA,GAAE,SAAS,GAAEH,MAAGA,GAAE,SAAS,GAAE,KAAG,EAAE,SAAS,GAAEO,MAAGA,GAAE,SAAS,GAAE,KAAGT,GAAE,SAAS;AAAE,UAAMc,KAAE,KAAK,KAAK,OAAO;AAAO,WAAO,KAAK,KAAK,OAAO,KAAKnB,EAAC,GAAEmB;AAAA,EAAC;AAAA,EAAC,YAAY,EAAC,UAASpB,IAAE,QAAOC,IAAE,SAAQC,GAAC,GAAE;AAAC,SAAK,cAAcF,IAAE,CAAC,GAAE,KAAK,cAAcC,IAAE,CAAC,GAAE,KAAK,cAAcC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcF,IAAEC,IAAE;AAAC,QAAG,CAAC,EAAED,EAAC,EAAE,UAAQE,KAAE,GAAEE,KAAE,GAAEF,KAAEF,GAAE,QAAOE,MAAGD,IAAEG,MAAGH,IAAE;AAAC,YAAMA,KAAED,GAAEE,EAAC,GAAEK,KAAEP,GAAEI,EAAC;AAAE,MAAAJ,GAAEE,EAAC,IAAEK,IAAEP,GAAEI,EAAC,IAAE,CAACH;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAE;AAAC,QAAG,SAAOA,GAAE;AAAO,UAAMC,KAAE,KAAK,aAAa,QAAQD,EAAC;AAAE,QAAG,OAAKC,GAAE,QAAOA;AAAE,SAAK,KAAK,cAAY,KAAK,KAAK,YAAU,CAAC;AAAG,UAAMC,KAAE,CAAC;AAAE,YAAOF,GAAE,WAAU;AAAA,MAAC,KAAI;AAAO,QAAAE,GAAE,YAAU,EAAE;AAAK;AAAA,MAAM,KAAI;AAAA,MAAO,KAAI;AAAQ,QAAAA,GAAE,YAAU,EAAE;AAAA,IAAK;AAAC,YAAKF,GAAE,gBAAcE,GAAE,cAAYF,GAAE,cAAaA,GAAE,gBAAcE,GAAE,cAAYF,GAAE,cAAaE,GAAE,uBAAqB,CAAC;AAAE,UAAMK,KAAE,CAAAP,OAAGA,MAAG,KAAI,IAAE,CAAAA,OAAG;AAAC,YAAMC,KAAED,GAAE,OAAO;AAAE,aAAOC,GAAE,CAAC,IAAEM,GAAEN,GAAE,CAAC,IAAE,GAAG,GAAEA,GAAE,CAAC,IAAEM,GAAEN,GAAE,CAAC,IAAE,GAAG,GAAEA,GAAE,CAAC,IAAEM,GAAEN,GAAE,CAAC,IAAE,GAAG,GAAEA;AAAA,IAAC;AAAE,QAAG,EAAED,GAAE,KAAK,MAAIE,GAAE,qBAAqB,kBAAgB,EAAEF,GAAE,KAAK,IAAG,EAAEA,GAAE,YAAY,MAAIE,GAAE,qBAAqB,mBAAiB,EAAC,OAAM,KAAK,YAAYF,GAAE,YAAY,EAAC,IAAG,EAAEA,GAAE,aAAa,MAAIE,GAAE,gBAAc,EAAC,OAAM,KAAK,YAAYF,GAAE,aAAa,EAAC,IAAGA,cAAa,GAAE;AAAC,UAAG,EAAEA,GAAE,eAAe,MAAIE,GAAE,kBAAgB,EAAC,OAAM,KAAK,YAAYF,GAAE,eAAe,EAAC,IAAG,EAAEA,GAAE,aAAa,GAAE;AAAC,cAAMC,KAAE,EAAED,GAAE,aAAa;AAAE,QAAAE,GAAE,iBAAe,CAACD,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,MAAC;AAAC,QAAED,GAAE,gBAAgB,MAAIE,GAAE,mBAAiB,EAAC,OAAM,KAAK,YAAYF,GAAE,gBAAgB,EAAC,IAAG,EAAEA,GAAE,wBAAwB,MAAIE,GAAE,qBAAqB,2BAAyB,EAAC,OAAM,KAAK,YAAYF,GAAE,wBAAwB,EAAC,IAAGE,GAAE,qBAAqB,iBAAeF,GAAE,UAASE,GAAE,qBAAqB,kBAAgBF,GAAE;AAAA,IAAS,MAAM,CAAAE,GAAE,qBAAqB,iBAAe,GAAEA,GAAE,qBAAqB,kBAAgB,GAAE,EAAE,SAAS,kHAAkH;AAAE,UAAMI,KAAE,KAAK,KAAK,UAAU;AAAO,WAAO,KAAK,KAAK,UAAU,KAAKJ,EAAC,GAAE,KAAK,aAAa,KAAKF,EAAC,GAAEM;AAAA,EAAC;AAAA,EAAC,YAAYN,IAAE;AAAC,UAAME,KAAE,KAAK,KAAK,YAAU,CAAC;AAAE,WAAO,KAAK,KAAK,WAASA,IAAEE,GAAE,KAAK,aAAYJ,IAAG,MAAI;AAAC,YAAMC,KAAE,EAAC,SAAQ,KAAK,YAAYD,EAAC,GAAE,QAAO,KAAK,UAAUA,EAAC,EAAC,GAAEI,KAAEF,GAAE;AAAO,aAAOA,GAAE,KAAKD,EAAC,GAAEG;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAUJ,IAAE;AAAC,UAAMC,KAAE,KAAK,UAAU,IAAID,EAAC;AAAE,QAAG,QAAMC,GAAE,QAAOA;AAAE,SAAK,KAAK,WAAS,KAAK,KAAK,SAAO,CAAC;AAAG,UAAMC,KAAE,CAAC;AAAE,QAAGF,GAAE,IAAI,CAAAE,GAAE,MAAIF,GAAE;AAAA,SAAQ;AAAC,YAAMC,KAAED,GAAE;AAAK,MAAAE,GAAE,SAAOD;AAAE,eAAQD,KAAE,GAAEA,KAAE,KAAK,KAAK,OAAO,QAAO,EAAEA,GAAE,KAAGC,OAAI,KAAK,KAAK,OAAOD,EAAC,EAAE,OAAO,QAAOA;AAAE,YAAMO,KAAE,KAAK,KAAK;AAAO,cAAOA,GAAE,QAAQ,iBAAgB;AAAA,QAAC,KAAK,EAAE,KAAI;AAAC,gBAAMP,KAAEO,GAAE,eAAe,cAAc,EAAE,eAAcM,GAAE,MAAM;AAAE,cAAGZ,GAAEA,EAAC,EAAE,GAAEA,GAAE,IAAI,KAAGD,GAAE,iBAAiBC,GAAE,MAAK,CAAC;AAAA,eAAM;AAAC,kBAAMG,KAAEE,GAAEL,EAAC,EAAE,KAAM,CAAC,EAAC,MAAKD,IAAE,MAAKC,GAAC,OAAKC,GAAE,WAASD,IAAED,GAAG;AAAE,YAAAA,GAAE,WAAWI,EAAC,EAAE,KAAM,MAAI;AAAC,cAAAJ,GAAE,SAAS;AAAA,YAAC,CAAE;AAAA,UAAC;AAAC,UAAAE,GAAE,aAAWF,GAAE;AAAM;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE;AAAQ,cAAGC,GAAEA,EAAC,GAAE;AAAC,cAAE,SAAS,2DAA2D;AAAE;AAAA,UAAK;AAAC,UAAAC,GAAE,MAAI,EAAED,EAAC;AAAE;AAAA,QAAM;AAAQ,cAAGA,GAAEA,EAAC,GAAE;AAAC,cAAE,SAAS,2DAA2D;AAAE;AAAA,UAAK;AAAC,UAAAM,GAAE,SAAS,KAAKD,GAAEL,EAAC,EAAE,KAAM,CAAC,EAAC,MAAKD,IAAE,MAAKC,GAAC,MAAI;AAAC,YAAAC,GAAE,MAAIF,IAAEE,GAAE,WAASD;AAAA,UAAC,CAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,UAAMM,KAAE,KAAK,KAAK,OAAO;AAAO,WAAO,KAAK,KAAK,OAAO,KAAKL,EAAC,GAAE,KAAK,UAAU,IAAIF,IAAEO,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,YAAYP,IAAE;AAAC,SAAK,KAAK,aAAW,KAAK,KAAK,WAAS,CAAC;AAAG,QAAIC,KAAE,EAAE,QAAOC,KAAE,EAAE;AAAO,QAAG,YAAU,OAAOF,GAAE,KAAK,SAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAQ,QAAAC,KAAE,EAAE,eAAcC,KAAE,EAAE;AAAc;AAAA,MAAM,KAAI;AAAS,QAAAD,KAAE,EAAE,iBAAgBC,KAAE,EAAE;AAAA,IAAe;AAAA,SAAK;AAAC,cAAOF,GAAE,KAAK,UAAS;AAAA,QAAC,KAAI;AAAQ,UAAAE,KAAE,EAAE;AAAc;AAAA,QAAM,KAAI;AAAS,UAAAA,KAAE,EAAE;AAAA,MAAe;AAAC,cAAOF,GAAE,KAAK,YAAW;AAAA,QAAC,KAAI;AAAQ,UAAAC,KAAE,EAAE;AAAc;AAAA,QAAM,KAAI;AAAS,UAAAA,KAAE,EAAE;AAAA,MAAe;AAAA,IAAC;AAAC,UAAMG,KAAE,EAAC,OAAMH,IAAE,OAAMC,GAAC;AAAE,aAAQ,IAAE,GAAE,IAAE,KAAK,KAAK,SAAS,QAAO,EAAE,EAAE,KAAG,KAAK,UAAUE,EAAC,MAAI,KAAK,UAAU,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE,QAAO;AAAE,UAAMG,KAAE,KAAK,KAAK,SAAS;AAAO,WAAO,KAAK,KAAK,SAAS,KAAKH,EAAC,GAAEG;AAAA,EAAC;AAAA,EAAC,aAAaP,IAAEC,IAAE;AAAC,SAAK,KAAK,cAAY,KAAK,KAAK,YAAU,CAAC;AAAG,UAAMC,KAAE,EAAC,YAAWF,IAAE,YAAWC,GAAE,YAAW,eAAcA,GAAE,eAAc,OAAMA,GAAE,OAAM,MAAKA,GAAE,MAAK,KAAIA,GAAE,KAAI,KAAIA,GAAE,KAAI,MAAKA,GAAE,KAAI;AAAE,IAAAA,GAAE,eAAaC,GAAE,aAAW;AAAI,UAAME,KAAE,KAAK,KAAK,UAAU;AAAO,WAAO,KAAK,KAAK,UAAU,KAAKF,EAAC,GAAEE;AAAA,EAAC;AAAA,EAAC,sBAAsBJ,IAAEC,IAAEC,IAAEE,IAAEG,IAAE,GAAED,IAAEG,IAAE;AAAC,eAAUC,MAAKT,IAAE;AAAC,MAAAD,GAAE,cAAc,SAAS;AAAE,eAAQE,KAAE,GAAEA,KAAEQ,GAAE,MAAM,QAAO,EAAER,GAAE,CAAAF,GAAE,KAAKU,GAAE,MAAMR,EAAC,CAAC;AAAE,YAAMD,KAAED,GAAE,YAAY,GAAEY,KAAE,EAAC,YAAW,EAAC,UAASR,GAAC,GAAE,SAAQ,KAAK,aAAaJ,GAAE,OAAMC,EAAC,GAAE,UAAS,KAAK,aAAaS,GAAE,QAAQ,EAAC;AAAE,MAAAH,MAAG,WAASG,GAAE,YAAUE,GAAE,WAAW,SAAOL,KAAG,MAAIK,GAAE,WAAW,aAAW,IAAGN,MAAG,WAASI,GAAE,YAAUE,GAAE,WAAW,UAAQN,KAAGG,OAAIG,GAAE,WAAW,UAAQH,KAAGP,GAAE,WAAW,KAAKU,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBZ,IAAEC,IAAEC,IAAEE,IAAEG,IAAE,GAAED,IAAE;AAAC,UAAMG,KAAE,EAAC,YAAW,EAAC,UAASP,GAAC,EAAC;AAAE,IAAAE,OAAIK,GAAE,WAAW,SAAOL,KAAGG,OAAIE,GAAE,WAAW,aAAWF,KAAG,MAAIE,GAAE,WAAW,UAAQ,IAAGH,OAAIG,GAAE,WAAW,UAAQH,KAAGN,OAAIS,GAAE,WAAS,KAAK,aAAaT,GAAE,CAAC,EAAE,QAAQ,IAAGC,GAAE,WAAW,KAAKQ,EAAC;AAAA,EAAC;AAAC;;;ACA9rR,IAAMY,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAU,IAAG,KAAK,eAAa,GAAE,KAAK,YAAU,IAAG,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,SAASC,IAAE;AAAC,QAAG,KAAK,QAAQ,SAASA,EAAC,EAAE,OAAM,IAAI,MAAM,qBAAqB;AAAE,SAAK,QAAQ,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,MAAE,KAAK,SAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaC,IAAE;AAAC,SAAK,QAAQ,QAAQA,EAAC;AAAA,EAAC;AAAC;;;ACAtU,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,OAAK,IAAG,KAAK,SAAO,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQC,IAAE;AAAC,QAAG,KAAK,OAAO,SAASA,EAAC,EAAE,OAAM,IAAI,MAAM,oBAAoB;AAAE,SAAK,OAAO,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,OAAO,QAAQA,EAAC;AAAA,EAAC;AAAC;;;ACAnB,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,OAAKA,IAAE,KAAK,OAAK,IAAG,KAAK,cAAY,EAAE,GAAE,KAAK,WAASC,GAAE,GAAE,KAAK,QAAMC,GAAE,CAAC,GAAE,KAAK,SAAO,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQF,IAAE;AAAC,QAAG,KAAK,OAAO,SAASA,EAAC,EAAE,OAAM,IAAI,MAAM,oBAAoB;AAAE,SAAK,OAAO,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,OAAO,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,eAAeE,IAAE;AAAC,MAAE,KAAK,UAASA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,EAAC;AAAC;;;ACAR,IAAMC,KAAE;AAAR,IAAqB,IAAE;AAAY,SAASC,GAAEA,IAAEC,IAAEC,IAAE;AAJngB;AAIogB,QAAMC,KAAE,IAAIC,GAAEJ,IAAEC,KAAEA,MAAG,CAAC,GAAEC,EAAC;AAAE,MAAIG,KAAEF,GAAE;AAAO,EAAAE,KAAEA,GAAE,WAASA,GAAE,SAAO,IAAI,EAAE,EAAC,GAAE,IAAG,GAAE,IAAG,GAAE,GAAE,CAAC,KAAGA,KAAE,EAAC,QAAO,IAAI,EAAE,EAAC,GAAE,IAAG,GAAE,IAAG,GAAE,GAAE,CAAC,EAAC;AAAE,QAAM,IAAEA,GAAE,QAAOC,KAAEH,GAAE,MAAK,MAAE,KAAAG,GAAE,WAAF,mBAAU,aAAU,CAAC;AAAE,MAAIC,KAAE,GAAE,IAAE,GAAEC,KAAE;AAAK,SAAO,EAAE,CAAC,EAAE,KAAM,MAAI;AAAC,UAAMC,KAAE,EAAC,QAAO,EAAC;AAAE,WAAOH,GAAE;AAAO,UAAMI,KAAE,YAAU,OAAOT,GAAE,cAAYA,GAAE,cAAY,GAAEU,KAAE,KAAK,UAAUL,IAAG,CAACI,IAAEE,OAAI;AAAC,UAAG,aAAWF,IAAE;AAAC,YAAGE,cAAa,aAAY;AAAC,cAAG,EAAEA,EAAC,EAAE,SAAOX,GAAE,iBAAgB;AAAA,YAAC,KAAK,EAAE;AAAA,YAAQ,KAAK,EAAE;AAAI;AAAA,YAAM,KAAK,EAAE;AAAA,YAAS,SAAQ;AAAC,oBAAMS,KAAE,MAAM,CAAC;AAAO,qBAAO,KAAID,GAAEC,EAAC,IAAEE,IAAEF;AAAA,YAAC;AAAA,UAAC;AAAC,kBAAOT,GAAE,kBAAiB;AAAA,YAAC,KAAKY,GAAE;AAAQ,qBAAOC,GAAEF,EAAC;AAAA,YAAE,KAAKC,GAAE;AAAI,kBAAGL,GAAE,OAAM,IAAI,MAAM,iFAAiF;AAAE,qBAAO,MAAKA,KAAEI;AAAA,YAAG,KAAKC,GAAE;AAAA,YAAS,SAAQ;AAAC,oBAAMH,KAAE,OAAOH,EAAC;AAAO,qBAAOA,MAAIE,GAAEC,EAAC,IAAEE,IAAEF;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAC,eAAOE;AAAA,MAAC;AAAA,IAAC,GAAGF,EAAC;AAAE,WAAOT,GAAE,qBAAmBY,GAAE,OAAKZ,GAAE,oBAAkB,EAAE,MAAIQ,GAAE,CAAC,IAAE,IAAIA,GAAEE,IAAEH,EAAC,EAAE,SAAOC,GAAEV,EAAC,IAAEY,IAAEF;AAAA,EAAC,CAAE;AAAC;AAAC,SAASR,GAAEQ,IAAEC,IAAE;AAAC,SAAOV,GAAES,IAAE,EAAC,kBAAiBI,GAAE,KAAI,iBAAgB,EAAE,KAAI,aAAY,EAAC,GAAEH,EAAC;AAAC;;;ACAzuC,IAAMK,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,QAAM,EAAC,MAAK,qBAAoB,MAAKD,GAAC,GAAE,KAAK,SAAOC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,QAAQ,QAAQ,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,OAAE,IAAI,KAAK,CAAC,KAAK,MAAM,IAAI,GAAE,EAAC,MAAK,KAAK,MAAM,KAAI,CAAC,GAAEA,EAAC;AAAA,EAAC;AAAC;AAAC,SAASC,GAAEF,IAAEE,IAAE;AAAC,QAAMC,KAAE,IAAIJ,MAAEK,KAAE,IAAI;AAAE,SAAOD,GAAE,SAASC,EAAC,GAAEA,GAAE,QAAQ,IAAIC,GAAEL,EAAC,CAAC,GAAEM,GAAEH,IAAED,EAAC,EAAE,KAAM,CAAAF,OAAG,IAAID,GAAEC,GAAE,CAAC,GAAEA,GAAE,MAAM,CAAE;AAAC;", "names": ["t", "e", "i", "r", "n", "h", "E", "L", "o", "r", "e", "s", "i", "n", "t", "L", "r", "e", "i", "t", "f", "f", "t", "n", "e", "c", "i", "m", "s", "a", "o", "l", "h", "g", "u", "r", "C", "e", "t", "s", "E", "r", "F", "o", "i", "u", "n", "l", "f", "c", "L", "h", "A", "M", "N", "B", "I", "k", "s", "e", "d", "i", "s", "e", "t", "f", "p", "m", "c", "l", "C", "g", "d", "x", "h", "e", "t", "o", "r", "E", "s", "s", "e", "o", "f", "d", "l", "i", "m"]}