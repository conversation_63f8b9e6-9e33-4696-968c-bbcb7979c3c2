import {
  f as f2,
  x as x2
} from "./chunk-XZ2UVSB4.js";
import {
  ut,
  wt
} from "./chunk-B4KDIR4O.js";
import {
  f
} from "./chunk-XBS7QZIQ.js";
import {
  o
} from "./chunk-PTIRBOGQ.js";
import {
  x
} from "./chunk-W3CLOCDX.js";
import {
  x as x3
} from "./chunk-KE7SPCM7.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  c,
  i,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/rest/query/executeForExtent.js
async function m(m2, n3, s2) {
  const p = f(m2);
  return x2(p, x.from(n3), { ...s2 }).then((t2) => ({ count: t2.data.count, extent: w.fromJSON(t2.data.extent) }));
}

// node_modules/@arcgis/core/rest/query/operations/pbfJSONFeatureSet.js
function n(e, t2) {
  return t2;
}
function a(e, t2, r, s2) {
  switch (r) {
    case 0:
      return c2(e, t2 + s2, 0);
    case 1:
      return "lowerLeft" === e.originPosition ? c2(e, t2 + s2, 1) : l(e, t2 + s2, 1);
  }
}
function h(e, t2, r, s2) {
  return 2 === r ? c2(e, t2, 2) : a(e, t2, r, s2);
}
function u(e, t2, r, s2) {
  return 2 === r ? c2(e, t2, 3) : a(e, t2, r, s2);
}
function d(e, t2, r, s2) {
  return 3 === r ? c2(e, t2, 3) : h(e, t2, r, s2);
}
function c2({ translate: e, scale: t2 }, r, s2) {
  return e[s2] + r * t2[s2];
}
function l({ translate: e, scale: t2 }, r, s2) {
  return e[s2] - r * t2[s2];
}
var f3 = class {
  constructor(e) {
    this._options = e, this.geometryTypes = ["esriGeometryPoint", "esriGeometryMultipoint", "esriGeometryPolyline", "esriGeometryPolygon"], this._previousCoordinate = [0, 0], this._transform = null, this._applyTransform = n, this._lengths = [], this._currentLengthIndex = 0, this._toAddInCurrentPath = 0, this._vertexDimension = 0, this._coordinateBuffer = null, this._coordinateBufferPtr = 0, this._attributesConstructor = class {
    };
  }
  createFeatureResult() {
    return { fields: [], features: [] };
  }
  finishFeatureResult(t2) {
    if (this._options.applyTransform && (t2.transform = null), this._attributesConstructor = class {
    }, this._coordinateBuffer = null, this._lengths.length = 0, !t2.hasZ) return;
    const r = o(t2.geometryType, this._options.sourceSpatialReference, t2.spatialReference);
    if (!t(r)) for (const e of t2.features) r(e.geometry);
  }
  createSpatialReference() {
    return {};
  }
  addField(e, r) {
    const s2 = e.fields;
    i(s2), s2.push(r);
    const o2 = s2.map((e2) => e2.name);
    this._attributesConstructor = function() {
      for (const e2 of o2) this[e2] = null;
    };
  }
  addFeature(e, t2) {
    e.features.push(t2);
  }
  prepareFeatures(e) {
    switch (this._transform = e.transform, this._options.applyTransform && e.transform && (this._applyTransform = this._deriveApplyTransform(e)), this._vertexDimension = 2, e.hasZ && this._vertexDimension++, e.hasM && this._vertexDimension++, e.geometryType) {
      case "esriGeometryPoint":
        this.addCoordinate = (e2, t2, r) => this.addCoordinatePoint(e2, t2, r), this.createGeometry = (e2) => this.createPointGeometry(e2);
        break;
      case "esriGeometryPolygon":
        this.addCoordinate = (e2, t2, r) => this._addCoordinatePolygon(e2, t2, r), this.createGeometry = (e2) => this._createPolygonGeometry(e2);
        break;
      case "esriGeometryPolyline":
        this.addCoordinate = (e2, t2, r) => this._addCoordinatePolyline(e2, t2, r), this.createGeometry = (e2) => this._createPolylineGeometry(e2);
        break;
      case "esriGeometryMultipoint":
        this.addCoordinate = (e2, t2, r) => this._addCoordinateMultipoint(e2, t2, r), this.createGeometry = (e2) => this._createMultipointGeometry(e2);
    }
  }
  createFeature() {
    return this._lengths.length = 0, this._currentLengthIndex = 0, this._previousCoordinate[0] = 0, this._previousCoordinate[1] = 0, this._coordinateBuffer = null, this._coordinateBufferPtr = 0, { attributes: new this._attributesConstructor() };
  }
  allocateCoordinates() {
  }
  addLength(e, t2, r) {
    0 === this._lengths.length && (this._toAddInCurrentPath = t2), this._lengths.push(t2);
  }
  addQueryGeometry(e, t2) {
    const { queryGeometry: r, queryGeometryType: s2 } = t2, n3 = wt(r.clone(), r, false, false, this._transform), a2 = ut(n3, s2, false, false);
    e.queryGeometryType = s2, e.queryGeometry = { ...a2 };
  }
  createPointGeometry(e) {
    const t2 = { x: 0, y: 0, spatialReference: e.spatialReference };
    return e.hasZ && (t2.z = 0), e.hasM && (t2.m = 0), t2;
  }
  addCoordinatePoint(e, t2, s2) {
    const o2 = c(this._transform, "transform");
    switch (t2 = this._applyTransform(o2, t2, s2, 0), s2) {
      case 0:
        e.x = t2;
        break;
      case 1:
        e.y = t2;
        break;
      case 2:
        "z" in e ? e.z = t2 : e.m = t2;
        break;
      case 3:
        e.m = t2;
    }
  }
  _transformPathLikeValue(e, t2) {
    let s2 = 0;
    t2 <= 1 && (s2 = this._previousCoordinate[t2], this._previousCoordinate[t2] += e);
    const o2 = c(this._transform, "transform");
    return this._applyTransform(o2, e, t2, s2);
  }
  _addCoordinatePolyline(e, t2, r) {
    this._dehydratedAddPointsCoordinate(e.paths, t2, r);
  }
  _addCoordinatePolygon(e, t2, r) {
    this._dehydratedAddPointsCoordinate(e.rings, t2, r);
  }
  _addCoordinateMultipoint(e, t2, r) {
    0 === r && e.points.push([]);
    const s2 = this._transformPathLikeValue(t2, r);
    e.points[e.points.length - 1].push(s2);
  }
  _createPolygonGeometry(e) {
    return { rings: [[]], spatialReference: e.spatialReference, hasZ: !!e.hasZ, hasM: !!e.hasM };
  }
  _createPolylineGeometry(e) {
    return { paths: [[]], spatialReference: e.spatialReference, hasZ: !!e.hasZ, hasM: !!e.hasM };
  }
  _createMultipointGeometry(e) {
    return { points: [], spatialReference: e.spatialReference, hasZ: !!e.hasZ, hasM: !!e.hasM };
  }
  _dehydratedAddPointsCoordinate(e, t2, r) {
    0 === r && 0 == this._toAddInCurrentPath-- && (e.push([]), this._toAddInCurrentPath = this._lengths[++this._currentLengthIndex] - 1, this._previousCoordinate[0] = 0, this._previousCoordinate[1] = 0);
    const s2 = this._transformPathLikeValue(t2, r), o2 = e[e.length - 1];
    0 === r && (this._coordinateBufferPtr = 0, this._coordinateBuffer = new Array(this._vertexDimension), o2.push(this._coordinateBuffer)), this._coordinateBuffer[this._coordinateBufferPtr++] = s2;
  }
  _deriveApplyTransform(e) {
    const { hasZ: t2, hasM: r } = e;
    return t2 && r ? d : t2 ? h : r ? u : a;
  }
};

// node_modules/@arcgis/core/rest/query/executeQueryPBF.js
async function s(r, e, t2) {
  const s2 = await n2(r, x.from(e), t2);
  return x3.fromJSON(s2);
}
async function n2(o2, s2, n3) {
  const p = f(o2), i2 = { ...n3 }, u2 = x.from(s2), m2 = !u2.quantizationParameters, { data: f4 } = await f2(p, u2, new f3({ sourceSpatialReference: u2.sourceSpatialReference, applyTransform: m2 }), i2);
  return f4;
}

export {
  m,
  s,
  n2 as n
};
//# sourceMappingURL=chunk-F6EHUXJS.js.map
