import {
  o
} from "./chunk-TNGCGN7L.js";
import {
  t as t2
} from "./chunk-ONE6GLG5.js";
import {
  e as e2
} from "./chunk-SY6DBVDS.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  i
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  c,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/input/keys.js
var e3 = has("mac") ? "Meta" : "Ctrl";
var t3 = { 8: "Backspace", 9: "Tab", 13: "Enter", 27: "Escape", 33: "PageUp", 34: "PageDown", 35: "End", 36: "Home", 37: "ArrowLeft", 38: "ArrowUp", 39: "ArrowRight", 40: "ArrowDown", 45: "Insert", 46: "Delete" };
for (let n2 = 48; n2 < 58; n2++) t3[n2] = String.fromCharCode(n2);
for (let n2 = 1; n2 < 25; n2++) t3[111 + n2] = `F${n2}`;
for (let n2 = 65; n2 < 91; n2++) t3[n2] = [String.fromCharCode(n2 + 32), String.fromCharCode(n2)];
function o2(e5) {
  if (void 0 !== e5.key) return i(e5);
  const o3 = t3[e5.keyCode];
  return Array.isArray(o3) ? e5.shiftKey ? o3[1] : o3[0] : o3;
}
function a2(r) {
  switch (r) {
    case "Ctrl":
    case "Alt":
    case "Shift":
    case "Meta":
    case "Primary":
      return true;
  }
  return false;
}

// node_modules/@arcgis/core/views/input/EventMatch.js
var e4 = class {
  constructor(e5, t4 = []) {
    this.eventType = e5, this.keyModifiers = t4;
  }
  matches(e5) {
    if (e5.type !== this.eventType) return false;
    if (0 === this.keyModifiers.length) return true;
    const t4 = e5.modifiers;
    for (const i4 of this.keyModifiers) if (!t4.has(i4)) return false;
    return true;
  }
};

// node_modules/@arcgis/core/views/input/InputHandler.js
var n = s.getLogger("esri.views.input.InputHandler");
var i2 = class {
  constructor(e5) {
    this._manager = null, this._incoming = {}, this._outgoing = {}, this._incomingEventMatches = null, this._incomingEventTypes = null, this._outgoingEventTypes = null, this._hasSideEffects = e5;
  }
  get incomingEventMatches() {
    if (!this._incomingEventMatches) {
      this._incomingEventMatches = [];
      for (const e5 in this._incoming) {
        const t4 = this._incoming[e5];
        for (const e6 of t4) this._incomingEventMatches.push(e6.match);
      }
    }
    return this._incomingEventMatches;
  }
  get incomingEventTypes() {
    return this._incomingEventTypes || (this._incomingEventTypes = this.incomingEventMatches.map((e5) => e5.eventType)), this._incomingEventTypes;
  }
  get outgoingEventTypes() {
    return this._outgoingEventTypes || (this._outgoingEventTypes = Object.keys(this._outgoing)), this._outgoingEventTypes;
  }
  get hasSideEffects() {
    return this._hasSideEffects;
  }
  get hasPendingInputs() {
    return false;
  }
  onInstall(e5) {
    this._manager ? n.error("This InputHandler has already been registered with an InputManager") : (e5.setEventCallback((e6) => this._handleEvent(e6)), e5.setUninstallCallback(() => this._onUninstall()), this._manager = e5);
  }
  onUninstall() {
  }
  registerIncoming(e5, n2, i4) {
    let a4;
    "function" == typeof n2 ? (i4 = n2, a4 = []) : a4 = n2 || [];
    const o3 = "string" == typeof e5 ? new e4(e5, a4) : e5, h = () => {
      this._incomingEventTypes = null, this._incomingEventMatches = null;
    }, r = (e6) => {
      const t4 = this._incoming[e6.match.eventType];
      if (t4) {
        const n3 = t4.indexOf(e6);
        t4.splice(n3, 1), h(), this._manager && this._manager.updateDependencies();
      }
    }, g2 = new s2(o3, i4, { onPause: r, onRemove: r, onResume: (e6) => {
      const t4 = this._incoming[e6.match.eventType];
      t4 && !t4.includes(e6) && (t4.push(e6), h(), this._manager && this._manager.updateDependencies());
    } });
    let c2 = this._incoming[o3.eventType];
    return c2 || (c2 = [], this._incoming[o3.eventType] = c2), c2.push(g2), h(), this._manager && this._manager.updateDependencies(), g2;
  }
  registerOutgoing(e5) {
    if (this._outgoing[e5]) throw new Error("There is already a callback registered for this outgoing InputEvent: " + e5);
    const t4 = new a3(e5, { onEmit: (e6, t5, n2, i4) => {
      var _a;
      (_a = this._manager) == null ? void 0 : _a.emit(e6.eventType, t5, n2, i4);
    }, onRemove: (e6) => {
      var _a;
      delete this._outgoing[e6.eventType], (_a = this._manager) == null ? void 0 : _a.updateDependencies();
    } });
    return this._outgoing[e5] = t4, this._outgoingEventTypes = null, this._manager && this._manager.updateDependencies(), t4;
  }
  startCapturingPointer(e5) {
    var _a;
    (_a = this._manager) == null ? void 0 : _a.setPointerCapture(e5, true);
  }
  stopCapturingPointer(e5) {
    var _a;
    (_a = this._manager) == null ? void 0 : _a.setPointerCapture(e5, false);
  }
  refreshHasPendingInputs() {
    var _a;
    (_a = this._manager) == null ? void 0 : _a.refreshHasPendingInputs();
  }
  _onUninstall() {
    this._manager ? (this.onUninstall(), this._manager = null) : n.error("This InputHandler is not registered with an InputManager");
  }
  _handleEvent(e5) {
    var _a;
    const t4 = this._incoming[e5.type];
    if (t4) {
      for (const n2 of t4) if (n2.match.matches(e5) && ((_a = n2.callback) == null ? void 0 : _a.call(n2, e5), e5.shouldStopPropagation())) break;
    }
  }
};
var s2 = class {
  constructor(e5, t4, n2) {
    this.match = e5, this._callback = t4, this._handler = n2;
  }
  pause() {
    this._handler.onPause(this);
  }
  resume() {
    this._handler.onResume(this);
  }
  remove() {
    this._handler.onRemove(this);
  }
  get callback() {
    return this._callback;
  }
};
var a3 = class {
  constructor(e5, t4) {
    this.eventType = e5, this._removed = false, this._handler = t4;
  }
  emit(e5, t4, n2) {
    this._removed || this._handler.onEmit(this, e5, t4, n2);
  }
  remove() {
    this._removed = true, this._handler.onRemove(this);
  }
};

// node_modules/@arcgis/core/views/input/handlers/LatestPointer.js
var s3 = class extends i2 {
  constructor(t4) {
    super(true), this._onChange = t4, this._value = "mouse", this._x = null, this._y = null, this.registerIncoming("pointer-move", (t5) => {
      this._update(t5.data);
    });
  }
  _update(t4) {
    const s4 = "touch" === t4.native.pointerType ? "touch" : "mouse", { x: e5, y: i4 } = t4;
    s4 === this._value && this._x === e5 && this._y === i4 || (this._value = s4, this._x = e5, this._y = i4, this._onChange(s4, e5, i4));
  }
};

// node_modules/@arcgis/core/views/input/handlers/MultiTouch.js
var i3 = class extends i2 {
  get multiTouchActive() {
    return this._multiTouchActive.get();
  }
  constructor() {
    super(true), this._activeTouchPointerIds = /* @__PURE__ */ new Set(), this._multiTouchActive = new t2(false), this._onPointerAdd = ({ data: t4 }) => {
      "touch" === t4.pointerType && (this._activeTouchPointerIds.add(t4.native.pointerId), this._update());
    }, this._onPointerRemove = ({ data: t4 }) => {
      "touch" === t4.pointerType && (this._activeTouchPointerIds.delete(t4.native.pointerId), this._update());
    }, this.registerIncoming("pointer-down", this._onPointerAdd), this.registerIncoming("pointer-up", this._onPointerRemove), this.registerIncoming("pointer-capture-lost", this._onPointerRemove), this.registerIncoming("pointer-cancel", this._onPointerRemove);
  }
  _update() {
    this._multiTouchActive.set(this._activeTouchPointerIds.size > 1);
  }
};

// node_modules/@arcgis/core/views/input/InputManager.js
var u = class extends v {
  constructor(e5) {
    super(e5), this._pointerCaptures = /* @__PURE__ */ new Map(), this._nameToGroup = {}, this._handlers = [], this._handlersPriority = [], this._currentPropagation = null, this._updateDependenciesAfterPropagation = false, this._sourceEvents = /* @__PURE__ */ new Set(), this._keyModifiers = /* @__PURE__ */ new Set(), this._activeKeyModifiers = /* @__PURE__ */ new Set(), this._stoppedPropagationEventIds = /* @__PURE__ */ new Set(), this.primaryKey = e3, this._latestPointerType = "mouse", this._propertiesPool = new o({ latestPointerLocation: f }, this), this.latestPointerLocation = null, this.test = { timestamp: void 0, hasCurrentPropagation: () => !!this._currentPropagation };
  }
  initialize() {
    this.eventSource.onEventReceived = this._onEventReceived.bind(this), this._installRecognizers();
  }
  destroy() {
    const e5 = Object.keys(this._nameToGroup);
    for (const t4 of e5) this.uninstallHandlers(t4);
    this.eventSource.destroy(), this._currentPropagation = null, this._propertiesPool.destroy();
  }
  get hasPendingInputs() {
    return this._handlers.some((e5) => e5.handler.hasPendingInputs);
  }
  get latestPointerType() {
    return this._latestPointerType;
  }
  get multiTouchActive() {
    return this._multiTouchHandler.multiTouchActive;
  }
  installHandlers(e5, t4, i4 = P.INTERNAL) {
    if (this._nameToGroup[e5]) return void s.getLogger(this.declaredClass).error("There is already an InputHandler group registered under the name `" + e5 + "`");
    if (0 === t4.length) return void s.getLogger(this.declaredClass).error("Can't register a group of zero handlers");
    const n2 = { name: e5, handlers: t4.map((e6) => ({ handler: e6, active: true, removed: false, priorityIndex: 0, groupPriority: i4, eventCallback: null, uninstallCallback: null })) };
    this._nameToGroup[e5] = n2;
    for (let r = n2.handlers.length - 1; r >= 0; r--) {
      const e6 = n2.handlers[r];
      this._handlers.push(e6), e6.handler.onInstall({ updateDependencies: () => {
        this.updateDependencies();
      }, emit: (t5, r2, i5, n3, s4) => {
        this._emitInputEvent(e6.priorityIndex + 1, t5, r2, i5, s4, n3);
      }, setPointerCapture: (t5, r2) => {
        this._setPointerCapture(n2, e6, t5, r2);
      }, setEventCallback: (t5) => {
        e6.eventCallback = t5;
      }, setUninstallCallback: (t5) => {
        e6.uninstallCallback = t5;
      }, refreshHasPendingInputs: () => {
        this.notifyChange("hasPendingInputs");
      } });
    }
    this.updateDependencies();
  }
  uninstallHandlers(e5) {
    const t4 = this._nameToGroup[e5];
    t4 ? (t4.handlers.forEach((e6) => {
      var _a;
      e6.removed = true, (_a = e6.uninstallCallback) == null ? void 0 : _a.call(e6);
    }), delete this._nameToGroup[e5], this._currentPropagation ? this._currentPropagation.needsHandlerGarbageCollect = true : this._garbageCollectRemovedHandlers()) : s.getLogger(this.declaredClass).error("There is no InputHandler group registered under the name `" + e5 + "`");
  }
  hasHandlers(e5) {
    return void 0 !== this._nameToGroup[e5];
  }
  updateDependencies() {
    if (this._currentPropagation) return void (this._updateDependenciesAfterPropagation = true);
    this._updateDependenciesAfterPropagation = false;
    const e5 = /* @__PURE__ */ new Set(), t4 = /* @__PURE__ */ new Set();
    this._handlersPriority = [];
    for (let r = this._handlers.length - 1; r >= 0; r--) {
      const e6 = this._handlers[r];
      e6.priorityIndex = r, this._handlersPriority.push(e6);
    }
    this._handlersPriority = this._sortHandlersPriority(this._handlersPriority);
    for (let r = this._handlersPriority.length - 1; r >= 0; r--) {
      const i4 = this._handlersPriority[r];
      i4.priorityIndex = r;
      let n2 = i4.handler.hasSideEffects;
      if (!n2) {
        for (const t5 of i4.handler.outgoingEventTypes) if (e5.has(t5)) {
          n2 = true;
          break;
        }
      }
      if (n2) for (const r2 of i4.handler.incomingEventMatches) {
        e5.add(r2.eventType);
        for (const e6 of r2.keyModifiers) a2(e6) || t4.add(e6);
      }
      i4.active = n2;
    }
    this._sourceEvents = e5, this._keyModifiers = t4, this._pointerCaptures.size > 0 && this._sourceEvents.add("pointer-capture-lost"), this._keyModifiers.size > 0 && (this._sourceEvents.add("key-down"), this._sourceEvents.add("key-up")), this.eventSource && (this.eventSource.activeEvents = this._sourceEvents);
  }
  _setLatestPointer(e5, t4, r) {
    this._latestPointerType = e5;
    const n2 = this._get("latestPointerLocation");
    if (t(n2) || n2.x !== t4 || n2.y !== r) {
      const e6 = this._propertiesPool.get("latestPointerLocation");
      e6.x = t4, e6.y = r, this._set("latestPointerLocation", e6);
    }
  }
  _onEventReceived(e5, t4) {
    if ("pointer-capture-lost" === e5) {
      const e6 = t4;
      this._pointerCaptures.delete(e6.native.pointerId);
    }
    this._updateKeyModifiers(e5, t4);
    const r = null != this.test.timestamp ? this.test.timestamp : t4.native ? t4.native.timestamp : void 0, i4 = t4.native ? t4.native.cancelable : void 0;
    this._emitInputEventFromSource(e5, t4, r, i4);
  }
  _updateKeyModifiers(e5, t4) {
    if (!t4) return;
    let r = false;
    const i4 = () => {
      if (!r) {
        const e6 = /* @__PURE__ */ new Set();
        this._activeKeyModifiers.forEach((t5) => {
          e6.add(t5);
        }), this._activeKeyModifiers = e6, r = true;
      }
    }, n2 = (e6, t5) => {
      t5 && !this._activeKeyModifiers.has(e6) ? (i4(), this._activeKeyModifiers.add(e6)) : !t5 && this._activeKeyModifiers.has(e6) && (i4(), this._activeKeyModifiers.delete(e6));
    };
    if ("key-down" === e5 || "key-up" === e5) {
      const r2 = t4.key;
      this._keyModifiers.has(r2) && n2(r2, "key-down" === e5);
    }
    const s4 = t4.native;
    n2("Alt", !(!s4 || !s4.altKey)), n2("Ctrl", !(!s4 || !s4.ctrlKey)), n2("Shift", !(!s4 || !s4.shiftKey)), n2("Meta", !(!s4 || !s4.metaKey)), n2("Primary", this._activeKeyModifiers.has(this.primaryKey));
  }
  _installRecognizers() {
    this._latestPointerHandler = new s3((e5, t4, r) => this._setLatestPointer(e5, t4, r)), this._multiTouchHandler = new i3(), this.installHandlers("input-manager-logic", [this._latestPointerHandler, this._multiTouchHandler], P.ALWAYS), this.recognizers.length > 0 && this.installHandlers("default", this.recognizers, P.INTERNAL);
  }
  _setPointerCapture(e5, t4, r, i4) {
    const n2 = e5.name + "-" + t4.priorityIndex, s4 = this._pointerCaptures.get(r.pointerId) || /* @__PURE__ */ new Set();
    this._pointerCaptures.set(r.pointerId, s4), i4 ? (s4.add(n2), 1 === s4.size && this.eventSource && this.eventSource.setPointerCapture(r, true)) : s4.has(n2) && (s4.delete(n2), 0 === s4.size && (this._pointerCaptures.delete(r.pointerId), this.eventSource && this.eventSource.setPointerCapture(r, false)));
  }
  _garbageCollectRemovedHandlers() {
    this._handlers = this._handlers.filter((e5) => !e5.removed), this.updateDependencies();
  }
  _emitInputEventFromSource(e5, t4, r, i4) {
    this._emitInputEvent(0, e5, t4, r, i4);
  }
  _emitInputEvent(e5, t4, r, i4, n2, s4) {
    const o3 = void 0 !== i4 ? i4 : this._currentPropagation ? this._currentPropagation.timestamp : performance.now(), a4 = void 0 !== n2 && n2, l = { event: new _(t4, r, o3, s4 || this._activeKeyModifiers, a4), priorityIndex: e5 };
    this._currentPropagation ? this._currentPropagation.events.push(l) : this._doNewPropagation(l);
  }
  _doNewPropagation(e5) {
    this._currentPropagation = { events: new e2(), currentHandler: null, needsHandlerGarbageCollect: false, timestamp: e5.event.timestamp }, this._currentPropagation.events.push(e5), this._continuePropagation();
  }
  _continuePropagation() {
    var _a, _b;
    const e5 = c(this._currentPropagation);
    for (; e5.events.length > 0; ) {
      const { event: t4, priorityIndex: r } = e5.events.pop(), i4 = t4.data && t4.data.eventId;
      if (!(null != i4 && this._stoppedPropagationEventIds.has(i4))) for (e5.currentHandler = this._handlersPriority[r]; e5.currentHandler; ) {
        if (e5.currentHandler.removed) e5.needsHandlerGarbageCollect = true;
        else {
          if (e5.currentHandler.active && !t4.shouldStopPropagation() && ((_b = (_a = e5.currentHandler).eventCallback) == null ? void 0 : _b.call(_a, t4)), t4.shouldStopPropagation()) {
            null != i4 && this._stoppedPropagationEventIds.add(i4);
            break;
          }
          if (t4.shouldPausePropagation(() => this._continuePropagation())) return void this._pausePropagation({ event: t4, priorityIndex: e5.currentHandler.priorityIndex + 1 });
        }
        e5.currentHandler = this._handlersPriority[e5.currentHandler.priorityIndex + 1];
      }
    }
    e5.needsHandlerGarbageCollect && this._garbageCollectRemovedHandlers(), this.hasPendingInputs || this._stoppedPropagationEventIds.clear(), this._currentPropagation = null, this._updateDependenciesAfterPropagation && this.updateDependencies();
  }
  _pausePropagation(e5) {
    const t4 = new e2();
    t4.push(e5);
    const r = this._currentPropagation;
    if (r) {
      for (; r.events.length; ) t4.push(r.events.pop());
      r.events = t4, r.currentHandler = null;
    }
  }
  _compareHandlerPriority(e5, t4) {
    if (e5.handler.hasSideEffects !== t4.handler.hasSideEffects) return e5.handler.hasSideEffects ? 1 : -1;
    if (e5.groupPriority !== t4.groupPriority) return e5.groupPriority > t4.groupPriority ? -1 : 1;
    for (const r of e5.handler.incomingEventMatches) for (const e6 of t4.handler.incomingEventMatches) {
      if (r.eventType !== e6.eventType) continue;
      const t5 = r.keyModifiers.filter((t6) => e6.keyModifiers.includes(t6));
      if (t5.length === r.keyModifiers.length !== (t5.length === e6.keyModifiers.length)) return r.keyModifiers.length > e6.keyModifiers.length ? -1 : 1;
    }
    return e5.priorityIndex > t4.priorityIndex ? -1 : 1;
  }
  _sortHandlersPriority(e5) {
    const t4 = [];
    for (const r of e5) {
      let e6 = 0;
      for (; e6 < t4.length && this._compareHandlerPriority(r, t4[e6]) >= 0; ) e6++;
      t4.splice(e6, 0, r);
    }
    return t4;
  }
  get debug() {
    const e5 = (e6) => {
      const t4 = this._setPointerCapture;
      this._setPointerCapture = () => {
      }, e6(), this._setPointerCapture = t4;
    };
    return { injectEvent: (t4, r) => {
      e5(() => {
        this._onEventReceived(t4, r);
      });
    }, disablePointerCapture: e5 };
  }
};
e([y({ readOnly: true })], u.prototype, "hasPendingInputs", null), e([y({ constructOnly: true })], u.prototype, "eventSource", void 0), e([y({ constructOnly: true })], u.prototype, "recognizers", void 0), e([y()], u.prototype, "_latestPointerType", void 0), e([y()], u.prototype, "latestPointerType", null), e([y()], u.prototype, "multiTouchActive", null), e([y({ readOnly: true })], u.prototype, "latestPointerLocation", void 0), u = e([a("esri.views.input.InputManager")], u);
var _ = class {
  constructor(e5, t4, r, i4, n2) {
    this.type = e5, this.data = t4, this.timestamp = r, this.modifiers = i4, this.cancelable = n2, this._propagationState = g.NONE, this._resumeCallback = null;
  }
  stopPropagation() {
    this._propagationState |= g.STOPPED;
  }
  shouldStopPropagation() {
    return 0 != (this._propagationState & g.STOPPED);
  }
  async(e5) {
    this._propagationState |= g.PAUSED;
    const t4 = (e6, t5) => {
      this._propagationState &= ~g.PAUSED;
      const r = this._resumeCallback;
      if (this._resumeCallback = null, r && r(), t5) throw e6;
      return e6;
    };
    return ("function" == typeof e5 ? e5() : e5).then((e6) => t4(e6, false), (e6) => t4(e6, true));
  }
  shouldPausePropagation(e5) {
    return !!(this._propagationState & g.PAUSED) && (this._resumeCallback = e5, true);
  }
  preventDefault() {
    this.data.native.preventDefault();
  }
};
var g;
!function(e5) {
  e5[e5.NONE = 0] = "NONE", e5[e5.STOPPED = 1] = "STOPPED", e5[e5.PAUSED = 2] = "PAUSED";
}(g || (g = {}));
var P = { ALWAYS: 1, DEFAULT: 0, TOOL: -1, WIDGET: -2, INTERNAL: -3 };
var v2 = class {
};
var f = v2;

export {
  e3 as e,
  o2 as o,
  i2 as i,
  u,
  P
};
//# sourceMappingURL=chunk-HURTVQSL.js.map
