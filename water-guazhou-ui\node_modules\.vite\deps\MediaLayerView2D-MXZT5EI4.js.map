{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/webgl/Overlay.js", "../../@arcgis/core/views/2d/engine/webgl/OverlayContainer.js", "../../@arcgis/core/views/2d/layers/MediaLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/Error.js\";import{on as t}from\"../../../../core/events.js\";import r from\"../../../../core/Logger.js\";import{isPowerOfTwo as s}from\"../../../../core/mathUtils.js\";import{disposeMaybe as i,isSome as o,isNone as n}from\"../../../../core/maybe.js\";import{getProjectiveTransform as a}from\"../../../../core/perspectiveUtils.js\";import{watch as m,when as h,initial as c}from\"../../../../core/reactiveUtils.js\";import{c as p}from\"../../../../chunks/mat3f64.js\";import{s as l}from\"../../../../chunks/vec2.js\";import{a as d}from\"../../../../chunks/vec2f64.js\";import{DisplayObject as u}from\"../DisplayObject.js\";import{BufferObject as f}from\"../../../webgl/BufferObject.js\";import{ContextType as g}from\"../../../webgl/context-util.js\";import{PixelFormat as w,PixelType as y,TextureSamplingMode as v,TextureWrapMode as _,Usage as j}from\"../../../webgl/enums.js\";import{Texture as x}from\"../../../webgl/Texture.js\";import{VertexArrayObject as V}from\"../../../webgl/VertexArrayObject.js\";const b=p();class A extends u{constructor(s){super(),this.elementView=s,this.isWrapAround=!1,this.perspectiveTransform=d(),this._vertices=new Float32Array(20),this._handles=[],this._handles.push(m((()=>this.elementView.element.opacity),(e=>this.opacity=e),c),m((()=>[this.elementView.coords]),(()=>{this.requestRender()}),c),h((()=>this.elementView.element.loaded),(()=>{const e=this.elementView.element;this.ready(),\"video\"===e.type&&o(e.content)&&this._handles.push(t(e.content,\"play\",(()=>this.requestRender())))}),c)),s.element.load().catch((t=>{r.getLogger(\"esri.views.2d.layers.MediaLayerView2D\").error(new e(\"element-load-error\",\"Element cannot be displayed\",{element:s,error:t}))}))}destroy(){this._handles.forEach((e=>e.remove())),this.texture=i(this.texture)}get dvsMat3(){return this.parent.dvsMat3}beforeRender(e){const{context:t}=e,r=this.elementView.element.content;if(o(r)){const e=r instanceof HTMLImageElement,i=r instanceof HTMLVideoElement,o=e?r.naturalWidth:i?r.videoWidth:r.width,n=e?r.naturalHeight:i?r.videoHeight:r.height;this._updatePerspectiveTransform(o,n),this.texture?i&&!r.paused&&(this.texture.setData(r),this.requestRender(),(t.type===g.WEBGL2||s(o)&&s(n))&&this.texture.generateMipmap()):(this.texture=new x(t,{pixelFormat:w.RGBA,dataType:y.UNSIGNED_BYTE,samplingMode:v.LINEAR,wrapMode:_.CLAMP_TO_EDGE,width:o,height:n,preMultiplyAlpha:!0},r),(t.type===g.WEBGL2||s(o)&&s(n))&&this.texture.generateMipmap(),i&&!r.paused&&this.requestRender())}super.beforeRender(e)}_createTransforms(){return null}updateDrawCoords(e,t){const r=this.elementView.coords;if(n(r))return;const[s,i,o,a]=r.rings[0],m=this._vertices,{x:h,y:c}=e,p=0!==t;p?m.set([i[0]-h,i[1]-c,s[0]-h,s[1]-c,o[0]-h,o[1]-c,a[0]-h,a[1]-c,a[0]-h,a[1]-c,i[0]+t-h,i[1]-c,i[0]+t-h,i[1]-c,s[0]+t-h,s[1]-c,o[0]+t-h,o[1]-c,a[0]+t-h,a[1]-c]):m.set([i[0]-h,i[1]-c,s[0]-h,s[1]-c,o[0]-h,o[1]-c,a[0]-h,a[1]-c]),this.isWrapAround=p}getVAO(e,t,r){if(n(this.elementView.coords))return null;const s=this._vertices;if(this._vao)this._geometryVbo.setData(s);else{this._geometryVbo=f.createVertex(e,j.DYNAMIC_DRAW,s);const i=f.createVertex(e,j.STATIC_DRAW,new Uint16Array([0,0,0,1,1,0,1,1,1,1,0,0,0,0,0,1,1,0,1,1]));this._vao=new V(e,r,t,{geometry:this._geometryVbo,tex:i})}return this._vao}_updatePerspectiveTransform(e,t){const r=this._vertices;a(b,[0,0,e,0,0,t,e,t],[r[0],r[1],r[4],r[5],r[2],r[3],r[6],r[7]]),l(this.perspectiveTransform,b[6]/b[8]*e,b[7]/b[8]*t)}}export{A as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t}from\"../../../../core/maybe.js\";import{createScreenPoint as e}from\"../../../../core/screenUtils.js\";import{g as s,m as r,h as o,d as a,r as i}from\"../../../../chunks/mat3.js\";import{c as n}from\"../../../../chunks/mat3f32.js\";import{f as c}from\"../../../../chunks/vec2f32.js\";import{f as p}from\"../../../../chunks/vec3f32.js\";import{normalizeMapX as d}from\"../../../../geometry/support/normalizeUtils.js\";import{getInfo as h}from\"../../../../geometry/support/spatialReferenceUtils.js\";import{getWorldWidth as l}from\"../../viewpointUtils.js\";import{brushes as m}from\"../brushes.js\";import{WGLDrawPhase as f}from\"./enums.js\";import u from\"./WGLContainer.js\";class M extends u{constructor(){super(...arguments),this._localOrigin=e(0,0),this._viewStateId=-1,this._dvsMat3=n(),this.requiresDedicatedFBO=!1}get dvsMat3(){return this._dvsMat3}beforeRender(t){this._updateMatrices(t),this._updateOverlays(t,this.children);for(const e of this.children)e.beforeRender(t)}prepareRenderPasses(t){const e=t.registerRenderPass({name:\"overlay\",brushes:[m.overlay],target:()=>this.children,drawPhase:f.MAP});return[...super.prepareRenderPasses(t),e]}_updateMatrices(t){const{state:e}=t,{id:n,size:h,pixelRatio:l,resolution:m,rotation:f,viewpoint:u,displayMat3:M}=e;if(this._viewStateId===n)return;const v=Math.PI/180*f,_=l*h[0],w=l*h[1],{x:y,y:g}=u.targetGeometry,j=d(y,e.spatialReference);this._localOrigin.x=j,this._localOrigin.y=g;const b=m*_,R=m*w,O=s(this._dvsMat3);r(O,O,M),o(O,O,c(_/2,w/2)),a(O,O,p(_/b,-w/R,1)),i(O,O,-v),this._viewStateId=n}_updateOverlays(e,s){const{state:r}=e,{rotation:o,spatialReference:a,worldScreenWidth:i,size:n,viewpoint:c}=r,p=this._localOrigin;let d=0;const m=h(a);if(m&&a.isWrappable){const e=n[0],h=n[1],f=180/Math.PI*o,u=Math.abs(Math.cos(f)),M=Math.abs(Math.sin(f)),v=Math.round(e*u+h*M),[_,w]=m.valid,y=l(a),{x:g,y:j}=c.targetGeometry,b=[g,j],R=[0,0];r.toScreen(R,b);const O=[0,0];let P;P=v>i?.5*i:.5*v;const x=Math.floor((g+.5*y)/y),C=_+x*y,D=w+x*y,I=[R[0]+P,0];r.toMap(O,I),O[0]>D&&(d=y),I[0]=R[0]-P,r.toMap(O,I),O[0]<C&&(d=-y);for(const r of s){const e=r.elementView.bounds;if(t(e))continue;const[s,,o]=e;s<_&&o>_?r.updateDrawCoords(p,y):o>w&&s<w?r.updateDrawCoords(p,-y):r.updateDrawCoords(p,d)}}else for(const t of s)t.updateDrawCoords(p,d)}}export{M as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import\"../../../Graphic.js\";import\"../../../renderers/ClassBreaksRenderer.js\";import\"../../../renderers/DictionaryRenderer.js\";import\"../../../renderers/DotDensityRenderer.js\";import\"../../../renderers/HeatmapRenderer.js\";import\"../../../renderers/PieChartRenderer.js\";import\"../../../renderers/Renderer.js\";import\"../../../renderers/SimpleRenderer.js\";import\"../../../renderers/UniqueValueRenderer.js\";import\"../../../renderers/support/jsonUtils.js\";import\"../../../symbols.js\";import t from\"../../../core/Collection.js\";import\"../../../core/has.js\";import r from\"../../../core/Logger.js\";import{getOrCreateMapValue as s}from\"../../../core/MapUtils.js\";import{isSome as i,isNone as n}from\"../../../core/maybe.js\";import{isAbortError as o}from\"../../../core/promiseUtils.js\";import{on as l}from\"../../../core/reactiveUtils.js\";import{property as a}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as m}from\"../../../core/accessorSupport/decorators/subclass.js\";import{create as p}from\"../../../geometry/support/aaBoundingRect.js\";import{ringsContainsCoords as c}from\"../../../geometry/support/contains.js\";import{extentIntersectsPolygon as h}from\"../../../geometry/support/intersectsBase.js\";import{MediaElementView as u}from\"../../../layers/support/MediaElementView.js\";import\"../../../core/Error.js\";import\"../../../core/scheduling.js\";import\"../../../request.js\";import\"../../../chunks/index2.js\";import\"../../../core/urlUtils.js\";import\"../../../chunks/index3.js\";import\"../../../layers/effects/EffectView.js\";import\"../engine/DisplayObject.js\";import\"../engine/webgl/effects/highlight/HighlightGradient.js\";import\"../engine/webgl/BufferPool.js\";import\"../engine/webgl/enums.js\";import\"../engine/webgl/brushes/BrushBitmap.js\";import\"../../../chunks/vec4f32.js\";import\"../engine/webgl/Utils.js\";import\"../engine/webgl/shaders/BackgroundPrograms.js\";import\"../../webgl/enums.js\";import\"../../webgl/checkWebGLError.js\";import\"../../webgl/context-util.js\";import\"../../../chunks/builtins.js\";import\"../../../core/RandomLCG.js\";import\"../engine/webgl/materialKey/MaterialKey.js\";import\"../engine/webgl/techniques/Technique.js\";import\"../engine/webgl/techniques/dotDensity/TechniqueDotDensity.js\";import\"../engine/webgl/techniques/heatmap/TechniqueHeatmap.js\";import\"../engine/webgl/techniques/pieChart/TechniquePieChart.js\";import\"../../webgl/BufferObject.js\";import\"../../webgl/FramebufferObject.js\";import\"../../webgl/Texture.js\";import\"../../webgl/VertexArrayObject.js\";import\"../engine/webgl/brushes/WGLBrushHeatmap.js\";import\"../engine/webgl/DefaultVertexAttributeLayouts.js\";import\"../engine/webgl/shaders/TileInfoPrograms.js\";import\"../engine/webgl/brushes/WGLGeometryBrushMarker.js\";import\"../../../core/mathUtils.js\";import\"../engine/webgl/number.js\";import\"../engine/vectorTiles/style/StyleDefinition.js\";import\"../../../chunks/vec2f32.js\";import\"../engine/vectorTiles/enums.js\";import\"../engine/vectorTiles/shaders/sources/resolver.js\";import\"../engine/webgl/shaders/BitBlitPrograms.js\";import\"../engine/webgl/shaders/sources/resolver.js\";import\"../engine/webgl/TextureManager.js\";import\"../engine/webgl/shaders/StencilPrograms.js\";import\"../engine/webgl/effects/BlendEffect.js\";import\"../engine/webgl/shaders/HighlightPrograms.js\";import\"../engine/webgl/Profiler.js\";import\"../../webgl/renderState.js\";import\"../../3d/webgl-engine/core/shaderModules/interfaces.js\";import\"../../../core/floatRGBA.js\";import\"../../3d/webgl-engine/lib/OrderIndependentTransparency.js\";import\"../../../chunks/webgl-debug.js\";import\"../LabelManager.js\";import\"./graphics/GraphicsView2D.js\";import\"../engine/webgl/AttributeStoreView.js\";import\"../../../chunks/earcut.js\";import\"../../../layers/graphics/featureConversionUtils.js\";import\"../../../core/unitUtils.js\";import\"../../../renderers/support/lengthUtils.js\";import\"../../../chunks/vec3f32.js\";import\"../../../geometry/support/normalizeUtils.js\";import\"../navigation/MapViewNavigation.js\";import\"../../../core/asyncUtils.js\";import\"../engine/webgl/shaders/MagnifierPrograms.js\";import\"../tiling/PagedTileQueue.js\";import\"../tiling/TileInfoView.js\";import\"../tiling/TileKey.js\";import d from\"../tiling/TileQueue.js\";import g from\"../tiling/TileStrategy.js\";import f from\"../engine/webgl/Overlay.js\";import j from\"../engine/webgl/OverlayContainer.js\";import{LayerView2DMixin as y}from\"./LayerView2D.js\";import w from\"../../layers/LayerView.js\";import b from\"../../../geometry/Extent.js\";let _=class extends(y(w)){constructor(){super(...arguments),this._overlayContainer=null,this._fetchQueue=null,this._tileStrategy=null,this._elementReferences=new Map,this._debugGraphicsView=null,this.layer=null,this.elements=new t}attach(){this.addAttachHandles([l((()=>this.layer.effectiveSource),\"refresh\",(()=>{for(const e of this._tileStrategy.tiles)this._updateTile(e);this.requestUpdate()})),l((()=>this.layer.effectiveSource),\"change\",(({element:e})=>this._elementUpdateHandler(e)))]),this._overlayContainer=new j,this.container.addChild(this._overlayContainer),this._fetchQueue=new d({tileInfoView:this.view.featuresTilingScheme,concurrency:10,process:(e,t)=>this._queryElements(e,t)}),this._tileStrategy=new g({cachePolicy:\"purge\",resampling:!0,acquireTile:e=>this._acquireTile(e),releaseTile:e=>this._releaseTile(e),tileInfoView:this.view.featuresTilingScheme}),this.requestUpdate()}detach(){this.elements.removeAll(),this._tileStrategy.destroy(),this._fetchQueue.destroy(),this._overlayContainer.removeAllChildren(),this.container.removeAllChildren(),this._elementReferences.clear(),this._debugGraphicsView?.destroy()}supportsSpatialReference(e){return!0}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this.requestUpdate()}update(e){this._tileStrategy.update(e),this._debugGraphicsView?.update(e)}async hitTest(e,t){const r=[],s=e.normalize(),n=[s.x,s.y];for(const{projectedElement:{normalizedCoords:o,element:l}}of this._elementReferences.values())i(o)&&c(o.rings,n)&&r.push({type:\"media\",element:l,layer:this.layer,mapPoint:e});return r.reverse()}canResume(){return null!=this.layer.source&&super.canResume()}async doRefresh(){this._fetchQueue.reset(),this._tileStrategy.tiles.forEach((e=>this._updateTile(e)))}_acquireTile(e){const t=new E(e.clone());return this._updateTile(t),t}_updateTile(e){this.updatingHandles.addPromise(this._fetchQueue.push(e.key).then((t=>{const[r,s]=e.setElements(t);this._referenceElements(e,r),this._dereferenceElements(e,s),this.requestUpdate()}),(e=>{o(e)||r.getLogger(this.declaredClass).error(e)})))}_releaseTile(e){this._fetchQueue.abort(e.key.id),e.elements&&this._dereferenceElements(e,e.elements),this.requestUpdate()}async _queryElements(e,t){const r=this.layer.effectiveSource;if(n(r))return[];this.view.featuresTilingScheme.getTileBounds(v,e,!0);const s=new b({xmin:v[0],ymin:v[1],xmax:v[2],ymax:v[3],spatialReference:this.view.spatialReference});return r.queryElements(s,t)}_referenceElements(e,t){const r=this.layer.source;if(!n(r))for(const s of t)this._referenceElement(e,s)}_referenceElement(e,t){s(this._elementReferences,t.uid,(()=>{const e=new u({element:t,spatialReference:this.view.spatialReference}),r=new f(e);this._overlayContainer.addChild(r),this.elements.add(t);let s=null;return{tiles:new Set,projectedElement:e,overlay:r,debugGraphic:s}})).tiles.add(e)}_dereferenceElements(e,t){for(const r of t)this._dereferenceElement(e,r)}_dereferenceElement(e,t){const r=this._elementReferences.get(t.uid);r.tiles.delete(e),r.tiles.size||(this._overlayContainer.removeChild(r.overlay),r.overlay.destroy(),r.projectedElement.destroy(),this._elementReferences.delete(t.uid),this.elements.remove(t),this._debugGraphicsView?.graphics.remove(r.debugGraphic))}_elementUpdateHandler(e){let t=this._elementReferences.get(e.uid);if(t){const r=t.projectedElement.normalizedCoords;if(n(r))return this._overlayContainer.removeChild(t.overlay),t.overlay.destroy(),t.projectedElement.destroy(),this._elementReferences.delete(e.uid),this.elements.remove(e),void this._debugGraphicsView?.graphics.remove(t.debugGraphic);const s=[],i=[];for(const e of this._tileStrategy.tiles){const n=R(this.view.featuresTilingScheme,e,r);t.tiles.has(e)?n||i.push(e):n&&s.push(e)}for(const t of s)this._referenceElement(t,e);for(const t of i)this._dereferenceElement(t,e);return t=this._elementReferences.get(e.uid),void(t?.debugGraphic&&(t.debugGraphic.geometry=t.projectedElement.normalizedCoords,this._debugGraphicsView.graphicUpdateHandler({graphic:t.debugGraphic,property:\"geometry\"})))}const r=new u({element:e,spatialReference:this.view.spatialReference}).normalizedCoords;if(i(r))for(const s of this._tileStrategy.tiles){R(this.view.featuresTilingScheme,s,r)&&this._referenceElement(s,e)}}};e([a()],_.prototype,\"_fetchQueue\",void 0),e([a()],_.prototype,\"layer\",void 0),e([a({readOnly:!0})],_.prototype,\"elements\",void 0),_=e([m(\"esri.views.2d.layers.MediaLayerView2D\")],_);const v=p(),T={xmin:0,ymin:0,xmax:0,ymax:0};function R(e,t,r){return e.getTileBounds(v,t.key,!0),T.xmin=v[0],T.ymin=v[1],T.xmax=v[2],T.ymax=v[3],h(T,r)}class E{constructor(e){this.key=e,this.elements=null,this.isReady=!1,this.visible=!0}setElements(e){const t=[],r=new Set(this.elements);this.elements=e;for(const s of e)r.has(s)?r.delete(s):t.push(s);return this.isReady=!0,[t,Array.from(r)]}destroy(){}}const S=_;export{S as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4+B,IAAM,IAAEA,GAAE;AAAE,IAAM,IAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,GAAE,KAAK,cAAYA,IAAE,KAAK,eAAa,OAAG,KAAK,uBAAqB,EAAE,GAAE,KAAK,YAAU,IAAI,aAAa,EAAE,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,SAAS,KAAK,EAAG,MAAI,KAAK,YAAY,QAAQ,SAAU,CAAAF,OAAG,KAAK,UAAQA,IAAGG,EAAC,GAAE,EAAG,MAAI,CAAC,KAAK,YAAY,MAAM,GAAI,MAAI;AAAC,WAAK,cAAc;AAAA,IAAC,GAAGA,EAAC,GAAE,EAAG,MAAI,KAAK,YAAY,QAAQ,QAAS,MAAI;AAAC,YAAMH,KAAE,KAAK,YAAY;AAAQ,WAAK,MAAM,GAAE,YAAUA,GAAE,QAAM,EAAEA,GAAE,OAAO,KAAG,KAAK,SAAS,KAAKC,GAAED,GAAE,SAAQ,QAAQ,MAAI,KAAK,cAAc,CAAE,CAAC;AAAA,IAAC,GAAGG,EAAC,CAAC,GAAED,GAAE,QAAQ,KAAK,EAAE,MAAO,CAAAE,OAAG;AAAC,QAAE,UAAU,uCAAuC,EAAE,MAAM,IAAIF,GAAE,sBAAqB,+BAA8B,EAAC,SAAQA,IAAE,OAAME,GAAC,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAS,QAAS,CAAAJ,OAAGA,GAAE,OAAO,CAAE,GAAE,KAAK,UAAQ,EAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,OAAO;AAAA,EAAO;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAK,EAAC,SAAQI,GAAC,IAAEJ,IAAEC,MAAE,KAAK,YAAY,QAAQ;AAAQ,QAAG,EAAEA,GAAC,GAAE;AAAC,YAAMD,KAAEC,eAAa,kBAAiBI,KAAEJ,eAAa,kBAAiB,IAAED,KAAEC,IAAE,eAAaI,KAAEJ,IAAE,aAAWA,IAAE,OAAMK,KAAEN,KAAEC,IAAE,gBAAcI,KAAEJ,IAAE,cAAYA,IAAE;AAAO,WAAK,4BAA4B,GAAEK,EAAC,GAAE,KAAK,UAAQD,MAAG,CAACJ,IAAE,WAAS,KAAK,QAAQ,QAAQA,GAAC,GAAE,KAAK,cAAc,IAAGG,GAAE,SAAOH,GAAE,UAAQ,EAAE,CAAC,KAAG,EAAEK,EAAC,MAAI,KAAK,QAAQ,eAAe,MAAI,KAAK,UAAQ,IAAI,EAAEF,IAAE,EAAC,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,cAAa,EAAE,QAAO,UAAS,EAAE,eAAc,OAAM,GAAE,QAAOE,IAAE,kBAAiB,KAAE,GAAEL,GAAC,IAAGG,GAAE,SAAOH,GAAE,UAAQ,EAAE,CAAC,KAAG,EAAEK,EAAC,MAAI,KAAK,QAAQ,eAAe,GAAED,MAAG,CAACJ,IAAE,UAAQ,KAAK,cAAc;AAAA,IAAE;AAAC,UAAM,aAAaD,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,iBAAiBA,IAAEI,IAAE;AAAC,UAAMH,MAAE,KAAK,YAAY;AAAO,QAAG,EAAEA,GAAC,EAAE;AAAO,UAAK,CAACC,IAAEG,IAAE,GAAEE,EAAC,IAAEN,IAAE,MAAM,CAAC,GAAE,IAAE,KAAK,WAAU,EAAC,GAAEE,IAAE,GAAEK,GAAC,IAAER,IAAE,IAAE,MAAII;AAAE,QAAE,EAAE,IAAI,CAACC,GAAE,CAAC,IAAEF,IAAEE,GAAE,CAAC,IAAEG,IAAEN,GAAE,CAAC,IAAEC,IAAED,GAAE,CAAC,IAAEM,IAAE,EAAE,CAAC,IAAEL,IAAE,EAAE,CAAC,IAAEK,IAAED,GAAE,CAAC,IAAEJ,IAAEI,GAAE,CAAC,IAAEC,IAAED,GAAE,CAAC,IAAEJ,IAAEI,GAAE,CAAC,IAAEC,IAAEH,GAAE,CAAC,IAAED,KAAED,IAAEE,GAAE,CAAC,IAAEG,IAAEH,GAAE,CAAC,IAAED,KAAED,IAAEE,GAAE,CAAC,IAAEG,IAAEN,GAAE,CAAC,IAAEE,KAAED,IAAED,GAAE,CAAC,IAAEM,IAAE,EAAE,CAAC,IAAEJ,KAAED,IAAE,EAAE,CAAC,IAAEK,IAAED,GAAE,CAAC,IAAEH,KAAED,IAAEI,GAAE,CAAC,IAAEC,EAAC,CAAC,IAAE,EAAE,IAAI,CAACH,GAAE,CAAC,IAAEF,IAAEE,GAAE,CAAC,IAAEG,IAAEN,GAAE,CAAC,IAAEC,IAAED,GAAE,CAAC,IAAEM,IAAE,EAAE,CAAC,IAAEL,IAAE,EAAE,CAAC,IAAEK,IAAED,GAAE,CAAC,IAAEJ,IAAEI,GAAE,CAAC,IAAEC,EAAC,CAAC,GAAE,KAAK,eAAa;AAAA,EAAC;AAAA,EAAC,OAAOR,IAAEI,IAAEH,KAAE;AAAC,QAAG,EAAE,KAAK,YAAY,MAAM,EAAE,QAAO;AAAK,UAAMC,KAAE,KAAK;AAAU,QAAG,KAAK,KAAK,MAAK,aAAa,QAAQA,EAAC;AAAA,SAAM;AAAC,WAAK,eAAaO,GAAE,aAAaT,IAAE,EAAE,cAAaE,EAAC;AAAE,YAAMG,KAAEI,GAAE,aAAaT,IAAE,EAAE,aAAY,IAAI,YAAY,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,WAAK,OAAK,IAAIU,GAAEV,IAAEC,KAAEG,IAAE,EAAC,UAAS,KAAK,cAAa,KAAIC,GAAC,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,4BAA4BL,IAAEI,IAAE;AAAC,UAAMH,MAAE,KAAK;AAAU,IAAAU,GAAE,GAAE,CAAC,GAAE,GAAEX,IAAE,GAAE,GAAEI,IAAEJ,IAAEI,EAAC,GAAE,CAACH,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC,GAAEA,GAAE,KAAK,sBAAqB,EAAE,CAAC,IAAE,EAAE,CAAC,IAAED,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAEI,EAAC;AAAA,EAAC;AAAC;;;ACA5sF,IAAMQ,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAaC,GAAE,GAAE,CAAC,GAAE,KAAK,eAAa,IAAG,KAAK,WAASC,GAAE,GAAE,KAAK,uBAAqB;AAAA,EAAE;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,aAAaC,IAAE;AAAC,SAAK,gBAAgBA,EAAC,GAAE,KAAK,gBAAgBA,IAAE,KAAK,QAAQ;AAAE,eAAUD,MAAK,KAAK,SAAS,CAAAA,GAAE,aAAaC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,UAAMD,KAAEC,GAAE,mBAAmB,EAAC,MAAK,WAAU,SAAQ,CAACC,GAAE,OAAO,GAAE,QAAO,MAAI,KAAK,UAAS,WAAU,EAAE,IAAG,CAAC;AAAE,WAAM,CAAC,GAAG,MAAM,oBAAoBD,EAAC,GAAED,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBC,IAAE;AAAC,UAAK,EAAC,OAAMD,GAAC,IAAEC,IAAE,EAAC,IAAGE,IAAE,MAAKC,IAAE,YAAWC,IAAE,YAAW,GAAE,UAASC,IAAE,WAAUC,IAAE,aAAYV,GAAC,IAAEG;AAAE,QAAG,KAAK,iBAAeG,GAAE;AAAO,UAAMK,KAAE,KAAK,KAAG,MAAIF,IAAEG,KAAEJ,KAAED,GAAE,CAAC,GAAEF,KAAEG,KAAED,GAAE,CAAC,GAAE,EAAC,GAAEM,IAAE,GAAE,EAAC,IAAEH,GAAE,gBAAeI,KAAE,EAAED,IAAEV,GAAE,gBAAgB;AAAE,SAAK,aAAa,IAAEW,IAAE,KAAK,aAAa,IAAE;AAAE,UAAMC,KAAE,IAAEH,IAAEI,KAAE,IAAEX,IAAE,IAAEY,GAAE,KAAK,QAAQ;AAAE,MAAE,GAAE,GAAEjB,EAAC,GAAE,EAAE,GAAE,GAAEI,GAAEQ,KAAE,GAAEP,KAAE,CAAC,CAAC,GAAEI,GAAE,GAAE,GAAEQ,GAAEL,KAAEG,IAAE,CAACV,KAAEW,IAAE,CAAC,CAAC,GAAET,GAAE,GAAE,GAAE,CAACI,EAAC,GAAE,KAAK,eAAaL;AAAA,EAAC;AAAA,EAAC,gBAAgBH,IAAEe,IAAE;AAAC,UAAK,EAAC,OAAMD,IAAC,IAAEd,IAAE,EAAC,UAAS,GAAE,kBAAiBF,IAAE,kBAAiBkB,IAAE,MAAKb,IAAE,WAAUJ,GAAC,IAAEe,KAAE,IAAE,KAAK;AAAa,QAAI,IAAE;AAAE,UAAM,IAAE,EAAEhB,EAAC;AAAE,QAAG,KAAGA,GAAE,aAAY;AAAC,YAAME,KAAEG,GAAE,CAAC,GAAEC,KAAED,GAAE,CAAC,GAAEG,KAAE,MAAI,KAAK,KAAG,GAAEC,KAAE,KAAK,IAAI,KAAK,IAAID,EAAC,CAAC,GAAET,KAAE,KAAK,IAAI,KAAK,IAAIS,EAAC,CAAC,GAAEE,KAAE,KAAK,MAAMR,KAAEO,KAAEH,KAAEP,EAAC,GAAE,CAACY,IAAEP,EAAC,IAAE,EAAE,OAAMQ,KAAE,GAAEZ,EAAC,GAAE,EAAC,GAAE,GAAE,GAAEa,GAAC,IAAEZ,GAAE,gBAAea,KAAE,CAAC,GAAED,EAAC,GAAEE,KAAE,CAAC,GAAE,CAAC;AAAE,MAAAC,IAAE,SAASD,IAAED,EAAC;AAAE,YAAM,IAAE,CAAC,GAAE,CAAC;AAAE,UAAIK;AAAE,MAAAA,KAAET,KAAEQ,KAAE,MAAGA,KAAE,MAAGR;AAAE,YAAMU,KAAE,KAAK,OAAO,IAAE,MAAGR,MAAGA,EAAC,GAAE,IAAED,KAAES,KAAER,IAAES,KAAEjB,KAAEgB,KAAER,IAAE,IAAE,CAACG,GAAE,CAAC,IAAEI,IAAE,CAAC;AAAE,MAAAH,IAAE,MAAM,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEK,OAAI,IAAET,KAAG,EAAE,CAAC,IAAEG,GAAE,CAAC,IAAEI,IAAEH,IAAE,MAAM,GAAE,CAAC,GAAE,EAAE,CAAC,IAAE,MAAI,IAAE,CAACJ;AAAG,iBAAUI,OAAKC,IAAE;AAAC,cAAMf,KAAEc,IAAE,YAAY;AAAO,YAAG,EAAEd,EAAC,EAAE;AAAS,cAAK,CAACe,IAAE,EAACK,EAAC,IAAEpB;AAAE,QAAAe,KAAEN,MAAGW,KAAEX,KAAEK,IAAE,iBAAiB,GAAEJ,EAAC,IAAEU,KAAElB,MAAGa,KAAEb,KAAEY,IAAE,iBAAiB,GAAE,CAACJ,EAAC,IAAEI,IAAE,iBAAiB,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,MAAM,YAAUb,MAAKc,GAAE,CAAAd,GAAE,iBAAiB,GAAE,CAAC;AAAA,EAAC;AAAC;;;ACA8xE,IAAI,IAAE,cAAcoB,GAAEC,EAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,oBAAkB,MAAK,KAAK,cAAY,MAAK,KAAK,gBAAc,MAAK,KAAK,qBAAmB,oBAAI,OAAI,KAAK,qBAAmB,MAAK,KAAK,QAAM,MAAK,KAAK,WAAS,IAAIC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,iBAAiB,CAACC,GAAG,MAAI,KAAK,MAAM,iBAAiB,WAAW,MAAI;AAAC,iBAAUC,MAAK,KAAK,cAAc,MAAM,MAAK,YAAYA,EAAC;AAAE,WAAK,cAAc;AAAA,IAAC,CAAE,GAAED,GAAG,MAAI,KAAK,MAAM,iBAAiB,UAAU,CAAC,EAAC,SAAQC,GAAC,MAAI,KAAK,sBAAsBA,EAAC,CAAE,CAAC,CAAC,GAAE,KAAK,oBAAkB,IAAIC,MAAE,KAAK,UAAU,SAAS,KAAK,iBAAiB,GAAE,KAAK,cAAY,IAAIC,GAAE,EAAC,cAAa,KAAK,KAAK,sBAAqB,aAAY,IAAG,SAAQ,CAACF,IAAEG,OAAI,KAAK,eAAeH,IAAEG,EAAC,EAAC,CAAC,GAAE,KAAK,gBAAc,IAAIC,GAAE,EAAC,aAAY,SAAQ,YAAW,MAAG,aAAY,CAAAJ,OAAG,KAAK,aAAaA,EAAC,GAAE,aAAY,CAAAA,OAAG,KAAK,aAAaA,EAAC,GAAE,cAAa,KAAK,KAAK,qBAAoB,CAAC,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,SAAQ;AAJn5K;AAIo5K,SAAK,SAAS,UAAU,GAAE,KAAK,cAAc,QAAQ,GAAE,KAAK,YAAY,QAAQ,GAAE,KAAK,kBAAkB,kBAAkB,GAAE,KAAK,UAAU,kBAAkB,GAAE,KAAK,mBAAmB,MAAM,IAAE,UAAK,uBAAL,mBAAyB;AAAA,EAAS;AAAA,EAAC,yBAAyBA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,YAAW;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAJvwL;AAIwwL,SAAK,cAAc,OAAOA,EAAC,IAAE,UAAK,uBAAL,mBAAyB,OAAOA;AAAA,EAAE;AAAA,EAAC,MAAM,QAAQA,IAAEG,IAAE;AAAC,UAAMC,MAAE,CAAC,GAAEC,KAAEL,GAAE,UAAU,GAAEM,KAAE,CAACD,GAAE,GAAEA,GAAE,CAAC;AAAE,eAAS,EAAC,kBAAiB,EAAC,kBAAiB,GAAE,SAAQE,GAAC,EAAC,KAAI,KAAK,mBAAmB,OAAO,EAAE,GAAE,CAAC,KAAGF,GAAE,EAAE,OAAMC,EAAC,KAAGF,IAAE,KAAK,EAAC,MAAK,SAAQ,SAAQG,IAAE,OAAM,KAAK,OAAM,UAASP,GAAC,CAAC;AAAE,WAAOI,IAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,QAAM,KAAK,MAAM,UAAQ,MAAM,UAAU;AAAA,EAAC;AAAA,EAAC,MAAM,YAAW;AAAC,SAAK,YAAY,MAAM,GAAE,KAAK,cAAc,MAAM,QAAS,CAAAJ,OAAG,KAAK,YAAYA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAMG,KAAE,IAAIK,GAAER,GAAE,MAAM,CAAC;AAAE,WAAO,KAAK,YAAYG,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAE;AAAC,SAAK,gBAAgB,WAAW,KAAK,YAAY,KAAKA,GAAE,GAAG,EAAE,KAAM,CAAAG,OAAG;AAAC,YAAK,CAACC,KAAEC,EAAC,IAAEL,GAAE,YAAYG,EAAC;AAAE,WAAK,mBAAmBH,IAAEI,GAAC,GAAE,KAAK,qBAAqBJ,IAAEK,EAAC,GAAE,KAAK,cAAc;AAAA,IAAC,GAAI,CAAAL,OAAG;AAAC,QAAEA,EAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,EAAC;AAAA,IAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,SAAK,YAAY,MAAMA,GAAE,IAAI,EAAE,GAAEA,GAAE,YAAU,KAAK,qBAAqBA,IAAEA,GAAE,QAAQ,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeA,IAAEG,IAAE;AAAC,UAAMC,MAAE,KAAK,MAAM;AAAgB,QAAG,EAAEA,GAAC,EAAE,QAAM,CAAC;AAAE,SAAK,KAAK,qBAAqB,cAAc,GAAEJ,IAAE,IAAE;AAAE,UAAMK,KAAE,IAAI,EAAE,EAAC,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,CAAC,GAAE,kBAAiB,KAAK,KAAK,iBAAgB,CAAC;AAAE,WAAOD,IAAE,cAAcC,IAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBH,IAAEG,IAAE;AAAC,UAAMC,MAAE,KAAK,MAAM;AAAO,QAAG,CAAC,EAAEA,GAAC,EAAE,YAAUC,MAAKF,GAAE,MAAK,kBAAkBH,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBL,IAAEG,IAAE;AAAC,IAAAC,GAAE,KAAK,oBAAmBD,GAAE,KAAK,MAAI;AAAC,YAAMH,KAAE,IAAIH,GAAE,EAAC,SAAQM,IAAE,kBAAiB,KAAK,KAAK,iBAAgB,CAAC,GAAEC,MAAE,IAAI,EAAEJ,EAAC;AAAE,WAAK,kBAAkB,SAASI,GAAC,GAAE,KAAK,SAAS,IAAID,EAAC;AAAE,UAAIE,KAAE;AAAK,aAAM,EAAC,OAAM,oBAAI,OAAI,kBAAiBL,IAAE,SAAQI,KAAE,cAAaC,GAAC;AAAA,IAAC,CAAE,EAAE,MAAM,IAAIL,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAEG,IAAE;AAAC,eAAUC,OAAKD,GAAE,MAAK,oBAAoBH,IAAEI,GAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBJ,IAAEG,IAAE;AAJt5O;AAIu5O,UAAMC,MAAE,KAAK,mBAAmB,IAAID,GAAE,GAAG;AAAE,IAAAC,IAAE,MAAM,OAAOJ,EAAC,GAAEI,IAAE,MAAM,SAAO,KAAK,kBAAkB,YAAYA,IAAE,OAAO,GAAEA,IAAE,QAAQ,QAAQ,GAAEA,IAAE,iBAAiB,QAAQ,GAAE,KAAK,mBAAmB,OAAOD,GAAE,GAAG,GAAE,KAAK,SAAS,OAAOA,EAAC,IAAE,UAAK,uBAAL,mBAAyB,SAAS,OAAOC,IAAE;AAAA,EAAc;AAAA,EAAC,sBAAsBJ,IAAE;AAJltP;AAImtP,QAAIG,KAAE,KAAK,mBAAmB,IAAIH,GAAE,GAAG;AAAE,QAAGG,IAAE;AAAC,YAAMC,MAAED,GAAE,iBAAiB;AAAiB,UAAG,EAAEC,GAAC,EAAE,QAAO,KAAK,kBAAkB,YAAYD,GAAE,OAAO,GAAEA,GAAE,QAAQ,QAAQ,GAAEA,GAAE,iBAAiB,QAAQ,GAAE,KAAK,mBAAmB,OAAOH,GAAE,GAAG,GAAE,KAAK,SAAS,OAAOA,EAAC,GAAE,OAAK,UAAK,uBAAL,mBAAyB,SAAS,OAAOG,GAAE;AAAc,YAAME,KAAE,CAAC,GAAEI,KAAE,CAAC;AAAE,iBAAUT,MAAK,KAAK,cAAc,OAAM;AAAC,cAAMM,KAAEI,GAAE,KAAK,KAAK,sBAAqBV,IAAEI,GAAC;AAAE,QAAAD,GAAE,MAAM,IAAIH,EAAC,IAAEM,MAAGG,GAAE,KAAKT,EAAC,IAAEM,MAAGD,GAAE,KAAKL,EAAC;AAAA,MAAC;AAAC,iBAAUG,MAAKE,GAAE,MAAK,kBAAkBF,IAAEH,EAAC;AAAE,iBAAUG,MAAKM,GAAE,MAAK,oBAAoBN,IAAEH,EAAC;AAAE,aAAOG,KAAE,KAAK,mBAAmB,IAAIH,GAAE,GAAG,GAAE,OAAKG,MAAA,gBAAAA,GAAG,kBAAeA,GAAE,aAAa,WAASA,GAAE,iBAAiB,kBAAiB,KAAK,mBAAmB,qBAAqB,EAAC,SAAQA,GAAE,cAAa,UAAS,WAAU,CAAC;AAAA,IAAG;AAAC,UAAMC,MAAE,IAAIP,GAAE,EAAC,SAAQG,IAAE,kBAAiB,KAAK,KAAK,iBAAgB,CAAC,EAAE;AAAiB,QAAG,EAAEI,GAAC,EAAE,YAAUC,MAAK,KAAK,cAAc,OAAM;AAAC,MAAAK,GAAE,KAAK,KAAK,sBAAqBL,IAAED,GAAC,KAAG,KAAK,kBAAkBC,IAAEL,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,uCAAuC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE,EAAE;AAAV,IAAYW,KAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC;AAAE,SAASD,GAAEV,IAAEG,IAAEC,KAAE;AAAC,SAAOJ,GAAE,cAAc,GAAEG,GAAE,KAAI,IAAE,GAAEQ,GAAE,OAAK,EAAE,CAAC,GAAEA,GAAE,OAAK,EAAE,CAAC,GAAEA,GAAE,OAAK,EAAE,CAAC,GAAEA,GAAE,OAAK,EAAE,CAAC,GAAE,EAAEA,IAAEP,GAAC;AAAC;AAAC,IAAMI,KAAN,MAAO;AAAA,EAAC,YAAYR,IAAE;AAAC,SAAK,MAAIA,IAAE,KAAK,WAAS,MAAK,KAAK,UAAQ,OAAG,KAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMG,KAAE,CAAC,GAAEC,MAAE,IAAI,IAAI,KAAK,QAAQ;AAAE,SAAK,WAASJ;AAAE,eAAUK,MAAKL,GAAE,CAAAI,IAAE,IAAIC,EAAC,IAAED,IAAE,OAAOC,EAAC,IAAEF,GAAE,KAAKE,EAAC;AAAE,WAAO,KAAK,UAAQ,MAAG,CAACF,IAAE,MAAM,KAAKC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE;", "names": ["e", "r", "s", "h", "t", "i", "n", "a", "c", "E", "f", "j", "M", "a", "c", "e", "t", "w", "n", "h", "l", "f", "u", "v", "_", "y", "j", "b", "R", "r", "s", "i", "P", "x", "D", "o", "f", "u", "j", "a", "e", "M", "y", "t", "r", "s", "n", "l", "E", "i", "R", "T"]}