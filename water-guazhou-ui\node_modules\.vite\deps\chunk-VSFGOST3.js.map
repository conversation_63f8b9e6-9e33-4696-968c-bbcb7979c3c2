{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/operationalLayers.js", "../../@arcgis/core/layers/mixins/OperationalLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst e={\"web-scene/operational-layers\":{ArcGISDimensionLayer:!0,ArcGISFeatureLayer:!0,ArcGISImageServiceLayer:!0,ArcGISMapServiceLayer:!0,ArcGISSceneServiceLayer:!0,ArcGISTiledElevationServiceLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,BuildingSceneLayer:!0,GroupLayer:!0,IntegratedMeshLayer:!0,OGCFeatureLayer:!0,PointCloudLayer:!0,WebTiledLayer:!0,CSV:!0,GeoJSON:!0,VectorTileLayer:!0,WFS:!0,WMS:!0,KML:!0,RasterDataLayer:!0,Voxel:!0,LineOfSightLayer:!0},\"web-scene/basemap\":{ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,WebTiledLayer:!0,OpenStreetMap:!0,VectorTileLayer:!0,ArcGISImageServiceLayer:!0,WMS:!0,ArcGISMapServiceLayer:!0,ArcGISSceneServiceLayer:!0},\"web-scene/ground\":{ArcGISTiledElevationServiceLayer:!0,RasterDataElevationLayer:!0},\"web-map/operational-layers\":{ArcGISAnnotationLayer:!0,ArcGISDimensionLayer:!0,ArcGISFeatureLayer:!0,ArcGISImageServiceLayer:!0,ArcGISImageServiceVectorLayer:!0,ArcGISMapServiceLayer:!0,ArcGISStreamLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,BingMapsAerial:!0,BingMapsHybrid:!0,BingMapsRoad:!0,CSV:!0,GeoRSS:!0,GeoJSON:!0,GroupLayer:!0,KML:!0,MediaLayer:!0,OGCFeatureLayer:!0,OrientedImageryLayer:!0,SubtypeGroupLayer:!0,VectorTileLayer:!0,WFS:!0,WMS:!0,WebTiledLayer:!0},\"web-map/basemap\":{ArcGISImageServiceLayer:!0,ArcGISImageServiceVectorLayer:!0,ArcGISMapServiceLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,OpenStreetMap:!0,VectorTileLayer:!0,WMS:!0,WebTiledLayer:!0,BingMapsAerial:!0,BingMapsRoad:!0,BingMapsHybrid:!0},\"web-map/tables\":{ArcGISFeatureLayer:!0},\"portal-item/operational-layers\":{ArcGISFeatureLayer:!0,ArcGISSceneServiceLayer:!0,PointCloudLayer:!0,BuildingSceneLayer:!0,IntegratedMeshLayer:!0,OrientedImageryLayer:!0}};export{e as supportedTypes};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import r from\"../../core/Error.js\";import{isBlobProtocol as t}from\"../../core/urlUtils.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as i}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as s}from\"../../core/accessorSupport/decorators/writer.js\";import{readLoadable as a}from\"../../core/accessorSupport/read.js\";import{willPropertyWrite as p}from\"../../core/accessorSupport/write.js\";import{supportedTypes as n}from\"./operationalLayers.js\";import{listMode as l,opacity as y}from\"../support/commonProperties.js\";const c=c=>{let u=class extends c{constructor(){super(...arguments),this.title=null}writeListMode(e,r,t,o){(o&&\"ground\"===o.layerContainerType||e&&p(this,t,{},o))&&(r[t]=e)}writeOperationalLayerType(e,r,t,o){!e||o&&\"tables\"===o.layerContainerType||(r.layerType=e)}writeTitle(e,r){r.title=e??\"Layer\"}read(e,r){r&&(r.layer=this),a(this,e,(r=>super.read(e,r)),r)}write(e,o){if(o?.origin){const e=`${o.origin}/${o.layerContainerType||\"operational-layers\"}`,t=n[e];let i=t&&t[this.operationalLayerType];if(\"ArcGISTiledElevationServiceLayer\"===this.operationalLayerType&&\"web-scene/operational-layers\"===e&&(i=!1),\"ArcGISDimensionLayer\"===this.operationalLayerType&&\"web-map/operational-layers\"===e&&(i=!1),!i)return o.messages?.push(new r(\"layer:unsupported\",`Layers (${this.title}, ${this.id}) of type '${this.declaredClass}' are not supported in the context of '${e}'`,{layer:this})),null}const i=super.write(e,{...o,layer:this}),s=!!o&&!!o.messages&&!!o.messages.filter((e=>e instanceof r&&\"web-document-write:property-required\"===e.name)).length;return t(i?.url)?(o?.messages?.push(new r(\"layer:invalid-url\",`Layer (${this.title}, ${this.id}) of type '${this.declaredClass}' using a Blob URL cannot be written to web scenes and web maps`,{layer:this})),null):!this.url&&s?null:i}beforeSave(){}};return e([o({type:String,json:{write:{ignoreOrigin:!0},origins:{\"web-scene\":{write:{isRequired:!0,ignoreOrigin:!0}},\"portal-item\":{write:!1}}}})],u.prototype,\"id\",void 0),e([o(l)],u.prototype,\"listMode\",void 0),e([s(\"listMode\")],u.prototype,\"writeListMode\",null),e([o({type:String,readOnly:!0,json:{read:!1,write:{target:\"layerType\",ignoreOrigin:!0},origins:{\"portal-item\":{write:!1}}}})],u.prototype,\"operationalLayerType\",void 0),e([s(\"operationalLayerType\")],u.prototype,\"writeOperationalLayerType\",null),e([o(y)],u.prototype,\"opacity\",void 0),e([o({type:String,json:{write:{ignoreOrigin:!0,writerEnsuresNonNull:!0},origins:{\"web-scene\":{write:{isRequired:!0,ignoreOrigin:!0,writerEnsuresNonNull:!0}},\"portal-item\":{write:!1}}},value:\"Layer\"})],u.prototype,\"title\",void 0),e([s(\"title\"),s([\"web-scene\"],\"title\")],u.prototype,\"writeTitle\",null),e([o({type:Boolean,json:{name:\"visibility\"}})],u.prototype,\"visible\",void 0),u=e([i(\"esri.layers.mixins.OperationalLayer\")],u),u};function u(e){return\"operationalLayerType\"in e}export{c as OperationalLayer,u as isOperationalLayer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAMA,KAAE,EAAC,gCAA+B,EAAC,sBAAqB,MAAG,oBAAmB,MAAG,yBAAwB,MAAG,uBAAsB,MAAG,yBAAwB,MAAG,kCAAiC,MAAG,8BAA6B,MAAG,4BAA2B,MAAG,oBAAmB,MAAG,YAAW,MAAG,qBAAoB,MAAG,iBAAgB,MAAG,iBAAgB,MAAG,eAAc,MAAG,KAAI,MAAG,SAAQ,MAAG,iBAAgB,MAAG,KAAI,MAAG,KAAI,MAAG,KAAI,MAAG,iBAAgB,MAAG,OAAM,MAAG,kBAAiB,KAAE,GAAE,qBAAoB,EAAC,8BAA6B,MAAG,4BAA2B,MAAG,eAAc,MAAG,eAAc,MAAG,iBAAgB,MAAG,yBAAwB,MAAG,KAAI,MAAG,uBAAsB,MAAG,yBAAwB,KAAE,GAAE,oBAAmB,EAAC,kCAAiC,MAAG,0BAAyB,KAAE,GAAE,8BAA6B,EAAC,uBAAsB,MAAG,sBAAqB,MAAG,oBAAmB,MAAG,yBAAwB,MAAG,+BAA8B,MAAG,uBAAsB,MAAG,mBAAkB,MAAG,8BAA6B,MAAG,4BAA2B,MAAG,gBAAe,MAAG,gBAAe,MAAG,cAAa,MAAG,KAAI,MAAG,QAAO,MAAG,SAAQ,MAAG,YAAW,MAAG,KAAI,MAAG,YAAW,MAAG,iBAAgB,MAAG,sBAAqB,MAAG,mBAAkB,MAAG,iBAAgB,MAAG,KAAI,MAAG,KAAI,MAAG,eAAc,KAAE,GAAE,mBAAkB,EAAC,yBAAwB,MAAG,+BAA8B,MAAG,uBAAsB,MAAG,8BAA6B,MAAG,4BAA2B,MAAG,eAAc,MAAG,iBAAgB,MAAG,KAAI,MAAG,eAAc,MAAG,gBAAe,MAAG,cAAa,MAAG,gBAAe,KAAE,GAAE,kBAAiB,EAAC,oBAAmB,KAAE,GAAE,kCAAiC,EAAC,oBAAmB,MAAG,yBAAwB,MAAG,iBAAgB,MAAG,oBAAmB,MAAG,qBAAoB,MAAG,sBAAqB,KAAE,EAAC;;;ACAhjC,IAAMC,KAAE,CAAAA,OAAG;AAAC,MAAIC,KAAE,cAAcD,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,QAAM;AAAA,IAAI;AAAA,IAAC,cAAcE,IAAEC,IAAE,GAAE,GAAE;AAAC,OAAC,KAAG,aAAW,EAAE,sBAAoBD,MAAG,EAAE,MAAK,GAAE,CAAC,GAAE,CAAC,OAAKC,GAAE,CAAC,IAAED;AAAA,IAAE;AAAA,IAAC,0BAA0BA,IAAEC,IAAE,GAAE,GAAE;AAAC,OAACD,MAAG,KAAG,aAAW,EAAE,uBAAqBC,GAAE,YAAUD;AAAA,IAAE;AAAA,IAAC,WAAWA,IAAEC,IAAE;AAAC,MAAAA,GAAE,QAAMD,MAAG;AAAA,IAAO;AAAA,IAAC,KAAKA,IAAEC,IAAE;AAAC,MAAAA,OAAIA,GAAE,QAAM,OAAM,EAAE,MAAKD,IAAG,CAAAC,OAAG,MAAM,KAAKD,IAAEC,EAAC,GAAGA,EAAC;AAAA,IAAC;AAAA,IAAC,MAAMD,IAAE,GAAE;AAJzjC;AAI0jC,UAAG,uBAAG,QAAO;AAAC,cAAMA,KAAE,GAAG,EAAE,MAAM,IAAI,EAAE,sBAAoB,oBAAoB,IAAG,IAAEA,GAAEA,EAAC;AAAE,YAAIE,KAAE,KAAG,EAAE,KAAK,oBAAoB;AAAE,YAAG,uCAAqC,KAAK,wBAAsB,mCAAiCF,OAAIE,KAAE,QAAI,2BAAyB,KAAK,wBAAsB,iCAA+BF,OAAIE,KAAE,QAAI,CAACA,GAAE,SAAO,OAAE,aAAF,mBAAY,KAAK,IAAI,EAAE,qBAAoB,WAAW,KAAK,KAAK,KAAK,KAAK,EAAE,cAAc,KAAK,aAAa,0CAA0CF,EAAC,KAAI,EAAC,OAAM,KAAI,CAAC,IAAG;AAAA,MAAI;AAAC,YAAM,IAAE,MAAM,MAAMA,IAAE,EAAC,GAAG,GAAE,OAAM,KAAI,CAAC,GAAEG,KAAE,CAAC,CAAC,KAAG,CAAC,CAAC,EAAE,YAAU,CAAC,CAAC,EAAE,SAAS,OAAQ,CAAAH,OAAGA,cAAa,KAAG,2CAAyCA,GAAE,IAAK,EAAE;AAAO,aAAO,EAAE,uBAAG,GAAG,MAAG,4BAAG,aAAH,mBAAa,KAAK,IAAI,EAAE,qBAAoB,UAAU,KAAK,KAAK,KAAK,KAAK,EAAE,cAAc,KAAK,aAAa,mEAAkE,EAAC,OAAM,KAAI,CAAC,IAAG,QAAM,CAAC,KAAK,OAAKG,KAAE,OAAK;AAAA,IAAC;AAAA,IAAC,aAAY;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,cAAa,KAAE,GAAE,SAAQ,EAAC,aAAY,EAAC,OAAM,EAAC,YAAW,MAAG,cAAa,KAAE,EAAC,GAAE,eAAc,EAAC,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEJ,GAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,UAAU,CAAC,GAAEA,GAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,EAAC,QAAO,aAAY,cAAa,KAAE,GAAE,SAAQ,EAAC,eAAc,EAAC,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,GAAEA,GAAE,WAAU,6BAA4B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,cAAa,MAAG,sBAAqB,KAAE,GAAE,SAAQ,EAAC,aAAY,EAAC,OAAM,EAAC,YAAW,MAAG,cAAa,MAAG,sBAAqB,KAAE,EAAC,GAAE,eAAc,EAAC,OAAM,MAAE,EAAC,EAAC,GAAE,OAAM,QAAO,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,OAAO,GAAE,EAAE,CAAC,WAAW,GAAE,OAAO,CAAC,GAAEA,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,aAAY,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,GAAEA,EAAC,GAAEA;AAAC;", "names": ["e", "c", "u", "e", "r", "i", "s"]}