{"version": 3, "sources": ["../../@arcgis/core/layers/support/FeatureTemplate.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import{ClonableMixin as r}from\"../../core/Clonable.js\";import{JSONMap as e}from\"../../core/jsonMap.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as i}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";const l=new e({esriFeatureEditToolAutoCompletePolygon:\"auto-complete-polygon\",esriFeatureEditToolCircle:\"circle\",esriFeatureEditToolEllipse:\"ellipse\",esriFeatureEditToolFreehand:\"freehand\",esriFeatureEditToolLine:\"line\",esriFeatureEditToolNone:\"none\",esriFeatureEditToolPoint:\"point\",esriFeatureEditToolPolygon:\"polygon\",esriFeatureEditToolRectangle:\"rectangle\",esriFeatureEditToolArrow:\"arrow\",esriFeatureEditToolTriangle:\"triangle\",esriFeatureEditToolLeftArrow:\"left-arrow\",esriFeatureEditToolRightArrow:\"right-arrow\",esriFeatureEditToolUpArrow:\"up-arrow\",esriFeatureEditToolDownArrow:\"down-arrow\"});let a=class extends(r(t)){constructor(o){super(o),this.name=null,this.description=null,this.drawingTool=null,this.prototype=null,this.thumbnail=null}};o([i({json:{write:!0}})],a.prototype,\"name\",void 0),o([i({json:{write:!0}})],a.prototype,\"description\",void 0),o([i({json:{read:l.read,write:l.write}})],a.prototype,\"drawingTool\",void 0),o([i({json:{write:!0}})],a.prototype,\"prototype\",void 0),o([i({json:{write:!0}})],a.prototype,\"thumbnail\",void 0),a=o([s(\"esri.layers.support.FeatureTemplate\")],a);const p=a;export{p as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAIyb,IAAMA,KAAE,IAAI,EAAE,EAAC,wCAAuC,yBAAwB,2BAA0B,UAAS,4BAA2B,WAAU,6BAA4B,YAAW,yBAAwB,QAAO,yBAAwB,QAAO,0BAAyB,SAAQ,4BAA2B,WAAU,8BAA6B,aAAY,0BAAyB,SAAQ,6BAA4B,YAAW,8BAA6B,cAAa,+BAA8B,eAAc,4BAA2B,YAAW,8BAA6B,aAAY,CAAC;AAAE,IAAIC,KAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,OAAK,MAAK,KAAK,cAAY,MAAK,KAAK,cAAY,MAAK,KAAK,YAAU,MAAK,KAAK,YAAU;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAKD,GAAE,MAAK,OAAMA,GAAE,MAAK,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["l", "a"]}