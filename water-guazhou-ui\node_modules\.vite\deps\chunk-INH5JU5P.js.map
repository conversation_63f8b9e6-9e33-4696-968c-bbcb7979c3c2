{"version": 3, "sources": ["../../@arcgis/core/views/support/geometry3dUtils.js", "../../@arcgis/core/views/interactive/snapping/SnappingConstraint.js", "../../@arcgis/core/views/interactive/snapping/candidates/SnappingCandidate.js", "../../@arcgis/core/views/interactive/snapping/candidates/FeatureSnappingCandidate.js", "../../@arcgis/core/views/interactive/snapping/candidates/EdgeSnappingCandidate.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{floatEqualAbsolute as t}from\"../../core/mathUtils.js\";import{a as n,v as s,h as r,w as e,e as o,i as c,d as u,k as a}from\"../../chunks/vec2.js\";import{a as f,d as i}from\"../../chunks/vec2f64.js\";import{s as p,w as A,e as L,z as h,i as E,f as N,u as d,b as m,D as y}from\"../../chunks/vec3.js\";import{c as P,a as I}from\"../../chunks/vec3f64.js\";import{LineType as R}from\"./geometry2dUtils.js\";function Y({start:t,end:o,type:c},u,a){const i=[],p=n(C,o,t),A=n(G,t,u),L=s(p),h=2*r(p,A),E=h*h-4*L*(s(A)-a*a);if(0===E){const n=-h/(2*L);(c===z.PLANE||n>=0)&&i.push(e(f(),t,p,n))}else if(E>0){const n=Math.sqrt(E),s=(-h+n)/(2*L);(c===z.PLANE||s>=0)&&i.push(e(f(),t,p,s));const r=(-h-n)/(2*L);(c===z.PLANE||r>=0)&&i.push(e(f(),t,p,r))}return i}function j(t,s){const e=t.start,c=t.end,u=n(C,c,e),a=p(G,-u[1],u[0],0),f=s.start,i=s.end,E=A(J,i,f),N=L(E,a),d=p(K,e[0],e[1],0),m=A(O,d,f),y=L(m,a);if(Math.abs(N)<B)return Math.abs(y),[];const P=h(Q,f,E,y/N);if(s.type===R.RAY){const t=A(S,P,f);if(L(t,E)<-B)return[]}if(t.type===z.HALF_PLANE){const t=o(S,P,e);if(r(t,u)<-B)return[]}return[I(P)]}function v(t,n){return x(g(T,n[2],t),n)}function k(t,n){const s=0,r=q(g(T,s,t),g(V,s,n)),e=[];for(const o of r)e.push(i(o));return e}function M(t,n){return _(t,g(T,t[2],n))}function l(t,n){return b(g(T,t[2],n),t)}function F(t,s,r){const o=n(C,t,s),u=r/c(o),a=e(P(),s,o,u);return a[2]=t[2],a}function H(t,n,s){return u(t,n)-s}function _(t,{start:n,end:s,type:r}){const e=A(C,t,n),o=A(G,s,n),c=L(e,o)/L(o,o);return h(P(),n,o,r===R.RAY?Math.max(c,0):c)}function b({start:t,end:n,type:s},r){const e=A(C,r,t),o=A(G,n,t);if(s===R.RAY&&L(o,e)<-B)return E(t,r);const c=N(J,o,e);return d(c)/d(o)}function w({start:t,end:e,type:o},c,u){const a=[],f=m(C,e,t),i=n(G,t,c),p=s(f),A=2*r(f,i),L=A*A-4*p*(s(i)-u*u);if(0===L){const n=-A/(2*p);(o===R.LINE||n>=0)&&a.push(h(P(),t,f,n))}else if(L>0){const n=Math.sqrt(L),s=(-A+n)/(2*p);(o===R.LINE||s>=0)&&a.push(h(P(),t,f,s));const r=(-A-n)/(2*p);(o===R.LINE||r>=0)&&a.push(h(P(),t,f,r))}return a}function q(n,s){const r=n.start,e=n.end,o=s.start,c=s.end,u=A(C,e,r),a=A(G,c,o),f=A(J,o,r),i=N(K,u,a),p=L(f,i);if(!t(p,0,B))return[];const E=y(i);if(t(E,0,B))return[];const d=N(O,f,a),m=L(d,i)/E,P=h(Q,r,u,m);if(n.type===R.RAY){const t=A(S,P,r);if(L(u,t)<-B)return[]}if(s.type===R.RAY){const t=A(S,P,o);if(L(a,t)<-B)return[]}return[I(P)]}function x({start:t,end:n,type:s},r){const e=A(C,r,t),o=A(G,n,t),c=N(J,o,e);if(y(c)/y(o)<B)switch(s){case R.LINE:return[I(r)];case R.RAY:return L(o,e)<-B?[]:[I(r)]}return[]}function U(n,s,r){return t(a(r,n),s*s,B)?[I(r)]:[]}function g(t,n,{start:s,end:r,type:e}){return p(t.start,s[0],s[1],n),p(t.end,r[0],r[1],n),t.type=D[e],t}var z;!function(t){t[t.PLANE=0]=\"PLANE\",t[t.HALF_PLANE=1]=\"HALF_PLANE\"}(z||(z={}));const D={[z.PLANE]:R.LINE,[z.HALF_PLANE]:R.RAY},B=1e-6,C=P(),G=P(),J=P(),K=P(),O=P(),Q=P(),S=P(),T={start:P(),end:P(),type:R.LINE},V={start:P(),end:P(),type:R.LINE};export{R as LineType,z as VerticalPlaneType,B as epsilon,x as intersectLineAndPoint,q as intersectLineLike,w as intersectLineLikeAndVerticalCylinder,U as intersectVerticalCylinderAndPoint,k as intersectVerticalPlane,j as intersectVerticalPlaneAndLineLike,v as intersectVerticalPlaneAndPoint,Y as intersectVerticalPlaneAndVerticalCylinder,b as pointToLineLikeDistance,H as pointToVerticalCylinderDistance,l as pointToVerticalPlaneDistance,_ as projectPointToLineLike,F as projectPointToVerticalCylinder,M as projectPointToVerticalPlane};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrapOr as t,toNullable as s,isSome as e}from\"../../../core/maybe.js\";import{h as n,a as r}from\"../../../chunks/vec2.js\";import{a as i}from\"../../../chunks/vec2f64.js\";import{k as o}from\"../../../chunks/vec3.js\";import{f as c}from\"../../../chunks/vec3f64.js\";import{cloneSnappingPoint as u,asSnappingPoint as a}from\"./SnappingPoint.js\";import{projectPointToLineLike as h,projectPointToVerticalCylinder as l,projectPointToVerticalPlane as p,VerticalPlaneType as f,epsilon as d,intersectLineAndPoint as L,intersectVerticalCylinderAndPoint as k,intersectVerticalPlaneAndPoint as m,intersectLineLike as y,intersectLineLikeAndVerticalCylinder as q,intersectVerticalPlaneAndLineLike as x,intersectVerticalPlaneAndVerticalCylinder as T,intersectVerticalPlane as g}from\"../../support/geometry3dUtils.js\";import{LineType as j}from\"../../support/geometry2dUtils.js\";class P{intersect(t){return H(this,t)}}class Z extends P{constructor(t){super(),this.point=t}equals(t){return _(t)&&o(this.point,t.point)}closestTo(){return u(this.point)}}class A extends P{constructor(t,s,e){super(),this.start=t,this.end=s,this.type=e,this.lineLike={start:this.start,end:this.end,type:this.type}}equals(t){return S(t)&&this.type===t.type&&o(this.start,t.start)&&o(this.end,t.end)}closestTo(t){const s=h(t,this.lineLike);return a(s)}}class E extends A{constructor(t,s){super(t,s,j.LINE)}}class b extends P{constructor(t,s,e){super(),this.intersection=t,this.first=s,this.second=e}equals(t){return t instanceof b&&this.first.equals(t.first)&&this.second.equals(t.second)}closestTo(){return u(this.intersection)}}class v extends P{constructor(t,s,e){super(),this.basePoint=t,this.first=s,this.second=e}equals(t){return t instanceof v&&this.first.equals(t.first)&&this.second.equals(t.second)}closestTo(t){const s=this.basePoint;return a(c(s[0],s[1],t[2]))}}class w extends P{constructor(t,s){super(),this.center=t,this.radius=s}equals(t){return z(t)&&this.center[0]===t.center[0]&&this.center[1]===t.center[1]&&this.radius===t.radius}closestTo(t){const s=l(t,this.center,this.radius);return a(s)}}class I extends P{constructor(t,s,e){super(),this.start=t,this.end=s,this.type=e,this.planeLike={start:t,end:s,type:e}}equals(t){return B(t)&&this.type===t.type&&o(this.start,t.start)&&o(this.end,t.end)}closestTo(t){return a(p(t,this.planeLike))}closestEndTo(t){const{start:s,end:e}=this;return Math.sign(n(r(D,e,s),r(J,t,s)))>0?e:s}}class N extends I{constructor(t,s){super(t,s,f.HALF_PLANE)}}class G extends I{constructor(t,s){super(t,s,f.PLANE)}}class O extends P{constructor(t,s,e){super(),this.start=t,this.end=s,this.getZ=e,this.planeLike={start:t,end:s,type:f.HALF_PLANE}}equals(t){return C(t)&&o(this.start,t.start)&&o(this.end,t.end)&&this.getZ===t.getZ}closestTo(t){return F(this,t)}addIfOnTheGround(s,e){for(const n of e){const e=t(this.getZ(n[0],n[1],n[2]),0);Math.abs(n[2]-e)<d&&(n[2]=e,s.push(n))}}}function F(t,e){const n=p(e,t.planeLike);return n[2]=s(t.getZ(e[0],e[1],e[2]))??K,a(n)}function H(t,s){let e=[];if(_(t)){const{point:n}=t;S(s)?e=L(s.lineLike,n):z(s)?e=k(s.center,s.radius,n):B(s)?e=m(s.planeLike,n):C(s)&&(e=M(s,t))}else if(S(t)){const{lineLike:n}=t;_(s)?e=L(n,s.point):S(s)?e=y(n,s.lineLike):z(s)?e=q(n,s.center,s.radius):B(s)?e=x(s.planeLike,n):C(s)&&(e=M(s,t))}else if(z(t)){const{center:n,radius:r}=t;if(S(s))e=q(s.lineLike,n,r);else if(_(s))e=k(n,r,s.point);else{if(B(s))return T(s.planeLike,n,r).map((e=>new v(e,t,s)));C(s)&&(e=M(s,t))}}else if(B(t)){const{planeLike:n}=t;if(B(s))return g(n,s.planeLike).map((e=>new v(e,t,s)));if(_(s))e=m(n,s.point);else if(S(s))e=x(n,s.lineLike);else{if(z(s))return T(n,s.center,s.radius).map((e=>new v(e,t,s)));C(s)&&(e=M(s,t))}}else C(t)&&(e=M(t,s));return U(e,t,s)}function M(t,n){const{planeLike:r,getZ:i}=t,o=[];if(_(n))t.addIfOnTheGround(o,m(r,n.point));else if(S(n))t.addIfOnTheGround(o,x(r,n.lineLike));else if(z(n))for(const[s,u]of T(r,n.center,n.radius)){const t=i(s,u,0);e(t)&&o.push(c(s,u,t))}else if(B(n)||C(n))for(const[e,u]of g(r,n.planeLike)){const t=s(i(e,u,0))??K;o.push(c(e,u,t))}return o}function U(t,s,e){return t.map((t=>new b(a(t),s,e)))}function _(t){return t instanceof Z}function S(t){return t instanceof A}function z(t){return t instanceof w}function B(t){return t instanceof I}function C(t){return t instanceof O}const D=i(),J=i(),K=0;export{O as DrapedLineConstraint,b as IntersectionConstraint,E as LineConstraint,A as LineLikeConstraint,Z as PointConstraint,P as SnappingConstraint,w as VerticalCylinderConstraint,N as VerticalHalfPlaneConstraint,v as VerticalLineIntersectionConstraint,G as VerticalPlaneConstraint,I as VerticalPlaneLikeConstraint};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t,s,i,o){this.targetPoint=t,this.constraint=s,this.isDraped=i,this.domain=o}}export{t as SnappingCandidate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{SnappingDomain as t}from\"../SnappingDomain.js\";import{SnappingCandidate as o}from\"./SnappingCandidate.js\";class n extends o{constructor({targetPoint:o,objectId:n,constraint:r,isDraped:i}){super(o,r,i,t.FEATURE),this.objectId=n}}export{n as FeatureSnappingCandidate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{LineConstraint as t}from\"../SnappingConstraint.js\";import{LineSegmentHintType as n}from\"../snappingUtils.js\";import{FeatureSnappingCandidate as i}from\"./FeatureSnappingCandidate.js\";import{LineSnappingHint as s}from\"../hints/LineSnappingHint.js\";class r extends i{constructor(n){super({...n,constraint:new t(n.edgeStart,n.edgeEnd)})}get hints(){return[new s(n.REFERENCE,this.constraint.start,this.constraint.end,this.isDraped,this.domain)]}}export{r as EdgeSnappingCandidate};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI8Y,SAASA,GAAE,EAAC,OAAMC,IAAE,KAAIC,IAAE,MAAK,EAAC,GAAEC,IAAE,GAAE;AAAC,QAAM,IAAE,CAAC,GAAEC,KAAEF,GAAEG,IAAEH,IAAED,EAAC,GAAEK,KAAEJ,GAAE,GAAED,IAAEE,EAAC,GAAE,IAAE,EAAEC,EAAC,GAAE,IAAE,IAAE,EAAEA,IAAEE,EAAC,GAAEC,KAAE,IAAE,IAAE,IAAE,KAAG,EAAED,EAAC,IAAE,IAAE;AAAG,MAAG,MAAIC,IAAE;AAAC,UAAMC,KAAE,CAAC,KAAG,IAAE;AAAG,KAAC,MAAI,EAAE,SAAOA,MAAG,MAAI,EAAE,KAAK,EAAEA,GAAE,GAAEP,IAAEG,IAAEI,EAAC,CAAC;AAAA,EAAC,WAASD,KAAE,GAAE;AAAC,UAAMC,KAAE,KAAK,KAAKD,EAAC,GAAE,KAAG,CAAC,IAAEC,OAAI,IAAE;AAAG,KAAC,MAAI,EAAE,SAAO,KAAG,MAAI,EAAE,KAAK,EAAEA,GAAE,GAAEP,IAAEG,IAAE,CAAC,CAAC;AAAE,UAAMK,MAAG,CAAC,IAAED,OAAI,IAAE;AAAG,KAAC,MAAI,EAAE,SAAOC,MAAG,MAAI,EAAE,KAAK,EAAED,GAAE,GAAEP,IAAEG,IAAEK,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAASC,GAAET,IAAE,GAAE;AAAC,QAAMU,KAAEV,GAAE,OAAM,IAAEA,GAAE,KAAIE,KAAED,GAAEG,IAAE,GAAEM,EAAC,GAAE,IAAE,EAAE,GAAE,CAACR,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,CAAC,GAAES,KAAE,EAAE,OAAM,IAAE,EAAE,KAAIL,KAAE,EAAEM,IAAE,GAAED,EAAC,GAAEE,KAAE,EAAEP,IAAE,CAAC,GAAEQ,KAAE,EAAE,GAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,CAAC,GAAEK,KAAE,EAAE,GAAED,IAAEH,EAAC,GAAE,IAAE,EAAEI,IAAE,CAAC;AAAE,MAAG,KAAK,IAAIF,EAAC,IAAE,EAAE,QAAO,KAAK,IAAI,CAAC,GAAE,CAAC;AAAE,QAAMG,KAAE,EAAE,GAAEL,IAAEL,IAAE,IAAEO,EAAC;AAAE,MAAG,EAAE,SAAOI,GAAE,KAAI;AAAC,UAAMjB,KAAE,EAAE,GAAEgB,IAAEL,EAAC;AAAE,QAAG,EAAEX,IAAEM,EAAC,IAAE,CAAC,EAAE,QAAM,CAAC;AAAA,EAAC;AAAC,MAAGN,GAAE,SAAO,EAAE,YAAW;AAAC,UAAMA,KAAE,EAAE,GAAEgB,IAAEN,EAAC;AAAE,QAAG,EAAEV,IAAEE,EAAC,IAAE,CAAC,EAAE,QAAM,CAAC;AAAA,EAAC;AAAC,SAAM,CAAC,EAAEc,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEhB,IAAEO,IAAE;AAAC,SAAOW,GAAEC,GAAE,GAAEZ,GAAE,CAAC,GAAEP,EAAC,GAAEO,EAAC;AAAC;AAAC,SAAS,EAAEP,IAAEO,IAAE;AAAC,QAAM,IAAE,GAAEC,KAAEY,GAAED,GAAE,GAAE,GAAEnB,EAAC,GAAEmB,GAAE,GAAE,GAAEZ,EAAC,CAAC,GAAEG,KAAE,CAAC;AAAE,aAAUT,MAAKO,GAAE,CAAAE,GAAE,KAAKV,GAAEC,EAAC,CAAC;AAAE,SAAOS;AAAC;AAAC,SAAS,EAAEV,IAAEO,IAAE;AAAC,SAAOc,GAAErB,IAAEmB,GAAE,GAAEnB,GAAE,CAAC,GAAEO,EAAC,CAAC;AAAC;AAAyC,SAASe,GAAEC,IAAE,GAAEC,IAAE;AAAC,QAAMC,KAAEA,GAAEC,IAAEH,IAAE,CAAC,GAAEI,KAAEH,KAAEI,GAAEH,EAAC,GAAE,IAAE,EAAE,EAAE,GAAE,GAAEA,IAAEE,EAAC;AAAE,SAAO,EAAE,CAAC,IAAEJ,GAAE,CAAC,GAAE;AAAC;AAAmC,SAASM,GAAEC,IAAE,EAAC,OAAMC,IAAE,KAAI,GAAE,MAAKC,GAAC,GAAE;AAAC,QAAMC,KAAE,EAAEC,IAAEJ,IAAEC,EAAC,GAAEI,KAAE,EAAE,GAAE,GAAEJ,EAAC,GAAE,IAAE,EAAEE,IAAEE,EAAC,IAAE,EAAEA,IAAEA,EAAC;AAAE,SAAO,EAAE,EAAE,GAAEJ,IAAEI,IAAEH,OAAII,GAAE,MAAI,KAAK,IAAI,GAAE,CAAC,IAAE,CAAC;AAAC;AAA0I,SAAS,EAAE,EAAC,OAAMC,IAAE,KAAIC,IAAE,MAAKC,GAAC,GAAE,GAAEC,IAAE;AAAC,QAAM,IAAE,CAAC,GAAEC,KAAE,EAAEC,IAAEJ,IAAED,EAAC,GAAE,IAAEE,GAAE,GAAEF,IAAE,CAAC,GAAEM,KAAE,EAAEF,EAAC,GAAEG,KAAE,IAAE,EAAEH,IAAE,CAAC,GAAE,IAAEG,KAAEA,KAAE,IAAED,MAAG,EAAE,CAAC,IAAEH,KAAEA;AAAG,MAAG,MAAI,GAAE;AAAC,UAAMK,KAAE,CAACD,MAAG,IAAED;AAAG,KAACJ,OAAIO,GAAE,QAAMD,MAAG,MAAI,EAAE,KAAK,EAAE,EAAE,GAAER,IAAEI,IAAEI,EAAC,CAAC;AAAA,EAAC,WAAS,IAAE,GAAE;AAAC,UAAMA,KAAE,KAAK,KAAK,CAAC,GAAE,KAAG,CAACD,KAAEC,OAAI,IAAEF;AAAG,KAACJ,OAAIO,GAAE,QAAM,KAAG,MAAI,EAAE,KAAK,EAAE,EAAE,GAAET,IAAEI,IAAE,CAAC,CAAC;AAAE,UAAMM,MAAG,CAACH,KAAEC,OAAI,IAAEF;AAAG,KAACJ,OAAIO,GAAE,QAAMC,MAAG,MAAI,EAAE,KAAK,EAAE,EAAE,GAAEV,IAAEI,IAAEM,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAASC,GAAEH,IAAE,GAAE;AAAC,QAAME,KAAEF,GAAE,OAAMP,KAAEO,GAAE,KAAIN,KAAE,EAAE,OAAM,IAAE,EAAE,KAAIC,KAAE,EAAEE,IAAEJ,IAAES,EAAC,GAAE,IAAE,EAAE,GAAE,GAAER,EAAC,GAAEE,KAAE,EAAEQ,IAAEV,IAAEQ,EAAC,GAAE,IAAE,EAAE,GAAEP,IAAE,CAAC,GAAEG,KAAE,EAAEF,IAAE,CAAC;AAAE,MAAG,CAAC,EAAEE,IAAE,GAAE,CAAC,EAAE,QAAM,CAAC;AAAE,QAAMO,KAAE,EAAE,CAAC;AAAE,MAAG,EAAEA,IAAE,GAAE,CAAC,EAAE,QAAM,CAAC;AAAE,QAAMC,KAAE,EAAE,GAAEV,IAAE,CAAC,GAAEW,KAAE,EAAED,IAAE,CAAC,IAAED,IAAEG,KAAE,EAAE,GAAEN,IAAEP,IAAEY,EAAC;AAAE,MAAGP,GAAE,SAAOC,GAAE,KAAI;AAAC,UAAMT,KAAE,EAAE,GAAEgB,IAAEN,EAAC;AAAE,QAAG,EAAEP,IAAEH,EAAC,IAAE,CAAC,EAAE,QAAM,CAAC;AAAA,EAAC;AAAC,MAAG,EAAE,SAAOS,GAAE,KAAI;AAAC,UAAMT,KAAE,EAAE,GAAEgB,IAAEd,EAAC;AAAE,QAAG,EAAE,GAAEF,EAAC,IAAE,CAAC,EAAE,QAAM,CAAC;AAAA,EAAC;AAAC,SAAM,CAAC,EAAEgB,EAAC,CAAC;AAAC;AAAC,SAASC,GAAE,EAAC,OAAMjB,IAAE,KAAIQ,IAAE,MAAK,EAAC,GAAEE,IAAE;AAAC,QAAMT,KAAE,EAAEI,IAAEK,IAAEV,EAAC,GAAEE,KAAE,EAAE,GAAEM,IAAER,EAAC,GAAE,IAAE,EAAEY,IAAEV,IAAED,EAAC;AAAE,MAAG,EAAE,CAAC,IAAE,EAAEC,EAAC,IAAE,EAAE,SAAO,GAAE;AAAA,IAAC,KAAKO,GAAE;AAAK,aAAM,CAAC,EAAEC,EAAC,CAAC;AAAA,IAAE,KAAKD,GAAE;AAAI,aAAO,EAAEP,IAAED,EAAC,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC,EAAES,EAAC,CAAC;AAAA,EAAC;AAAC,SAAM,CAAC;AAAC;AAAC,SAAS,EAAEF,IAAE,GAAEE,IAAE;AAAC,SAAO,EAAE,EAAEA,IAAEF,EAAC,GAAE,IAAE,GAAE,CAAC,IAAE,CAAC,EAAEE,EAAC,CAAC,IAAE,CAAC;AAAC;AAAC,SAASQ,GAAElB,IAAEQ,IAAE,EAAC,OAAM,GAAE,KAAIE,IAAE,MAAKT,GAAC,GAAE;AAAC,SAAO,EAAED,GAAE,OAAM,EAAE,CAAC,GAAE,EAAE,CAAC,GAAEQ,EAAC,GAAE,EAAER,GAAE,KAAIU,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEF,EAAC,GAAER,GAAE,OAAK,EAAEC,EAAC,GAAED;AAAC;AAAC,IAAI;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,aAAW,CAAC,IAAE;AAAY,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAM,IAAE,EAAC,CAAC,EAAE,KAAK,GAAES,GAAE,MAAK,CAAC,EAAE,UAAU,GAAEA,GAAE,IAAG;AAA9C,IAAgD,IAAE;AAAlD,IAAuDJ,KAAE,EAAE;AAA3D,IAA6D,IAAE,EAAE;AAAjE,IAAmEO,KAAE,EAAE;AAAvE,IAAyE,IAAE,EAAE;AAA7E,IAA+E,IAAE,EAAE;AAAnF,IAAqF,IAAE,EAAE;AAAzF,IAA2F,IAAE,EAAE;AAA/F,IAAiG,IAAE,EAAC,OAAM,EAAE,GAAE,KAAI,EAAE,GAAE,MAAKH,GAAE,KAAI;AAAjI,IAAmI,IAAE,EAAC,OAAM,EAAE,GAAE,KAAI,EAAE,GAAE,MAAKA,GAAE,KAAI;;;ACA3gE,IAAMU,KAAN,MAAO;AAAA,EAAC,UAAUC,IAAE;AAAC,WAAO,EAAE,MAAKA,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,cAAgBF,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,GAAE,KAAK,QAAMA;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,WAAOE,GAAEF,EAAC,KAAG,EAAE,KAAK,OAAMA,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAOG,GAAE,KAAK,KAAK;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgBJ,GAAC;AAAA,EAAC,YAAYC,IAAE,GAAEI,IAAE;AAAC,UAAM,GAAE,KAAK,QAAMJ,IAAE,KAAK,MAAI,GAAE,KAAK,OAAKI,IAAE,KAAK,WAAS,EAAC,OAAM,KAAK,OAAM,KAAI,KAAK,KAAI,MAAK,KAAK,KAAI;AAAA,EAAC;AAAA,EAAC,OAAOJ,IAAE;AAAC,WAAOK,GAAEL,EAAC,KAAG,KAAK,SAAOA,GAAE,QAAM,EAAE,KAAK,OAAMA,GAAE,KAAK,KAAG,EAAE,KAAK,KAAIA,GAAE,GAAG;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,UAAM,IAAEE,GAAEF,IAAE,KAAK,QAAQ;AAAE,WAAOM,GAAE,CAAC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYP,IAAE,GAAE;AAAC,UAAMA,IAAE,GAAEQ,GAAE,IAAI;AAAA,EAAC;AAAC;AAAC,IAAMA,KAAN,MAAM,WAAUT,GAAC;AAAA,EAAC,YAAYC,IAAE,GAAEI,IAAE;AAAC,UAAM,GAAE,KAAK,eAAaJ,IAAE,KAAK,QAAM,GAAE,KAAK,SAAOI;AAAA,EAAC;AAAA,EAAC,OAAOJ,IAAE;AAAC,WAAOA,cAAa,MAAG,KAAK,MAAM,OAAOA,GAAE,KAAK,KAAG,KAAK,OAAO,OAAOA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAOG,GAAE,KAAK,YAAY;AAAA,EAAC;AAAC;AAAC,IAAMM,KAAN,MAAM,WAAUV,GAAC;AAAA,EAAC,YAAYC,IAAE,GAAEI,IAAE;AAAC,UAAM,GAAE,KAAK,YAAUJ,IAAE,KAAK,QAAM,GAAE,KAAK,SAAOI;AAAA,EAAC;AAAA,EAAC,OAAOJ,IAAE;AAAC,WAAOA,cAAa,MAAG,KAAK,MAAM,OAAOA,GAAE,KAAK,KAAG,KAAK,OAAO,OAAOA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,UAAM,IAAE,KAAK;AAAU,WAAOM,GAAEI,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAEV,GAAE,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC;AAAC,IAAMW,KAAN,cAAgBZ,GAAC;AAAA,EAAC,YAAYC,IAAE,GAAE;AAAC,UAAM,GAAE,KAAK,SAAOA,IAAE,KAAK,SAAO;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,WAAOY,GAAEZ,EAAC,KAAG,KAAK,OAAO,CAAC,MAAIA,GAAE,OAAO,CAAC,KAAG,KAAK,OAAO,CAAC,MAAIA,GAAE,OAAO,CAAC,KAAG,KAAK,WAASA,GAAE;AAAA,EAAM;AAAA,EAAC,UAAUA,IAAE;AAAC,UAAM,IAAEa,GAAEb,IAAE,KAAK,QAAO,KAAK,MAAM;AAAE,WAAOM,GAAE,CAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgBP,GAAC;AAAA,EAAC,YAAYC,IAAE,GAAEI,IAAE;AAAC,UAAM,GAAE,KAAK,QAAMJ,IAAE,KAAK,MAAI,GAAE,KAAK,OAAKI,IAAE,KAAK,YAAU,EAAC,OAAMJ,IAAE,KAAI,GAAE,MAAKI,GAAC;AAAA,EAAC;AAAA,EAAC,OAAOJ,IAAE;AAAC,WAAOc,GAAEd,EAAC,KAAG,KAAK,SAAOA,GAAE,QAAM,EAAE,KAAK,OAAMA,GAAE,KAAK,KAAG,EAAE,KAAK,KAAIA,GAAE,GAAG;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,WAAOM,GAAE,EAAEN,IAAE,KAAK,SAAS,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAK,EAAC,OAAM,GAAE,KAAII,GAAC,IAAE;AAAK,WAAO,KAAK,KAAK,EAAEW,GAAEC,IAAEZ,IAAE,CAAC,GAAEW,GAAEE,IAAEjB,IAAE,CAAC,CAAC,CAAC,IAAE,IAAEI,KAAE;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYJ,IAAE,GAAE;AAAC,UAAMA,IAAE,GAAE,EAAE,UAAU;AAAA,EAAC;AAAC;AAAC,IAAMkB,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYlB,IAAE,GAAE;AAAC,UAAMA,IAAE,GAAE,EAAE,KAAK;AAAA,EAAC;AAAC;AAAC,IAAMmB,KAAN,cAAgBpB,GAAC;AAAA,EAAC,YAAYC,IAAE,GAAEI,IAAE;AAAC,UAAM,GAAE,KAAK,QAAMJ,IAAE,KAAK,MAAI,GAAE,KAAK,OAAKI,IAAE,KAAK,YAAU,EAAC,OAAMJ,IAAE,KAAI,GAAE,MAAK,EAAE,WAAU;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,WAAOoB,GAAEpB,EAAC,KAAG,EAAE,KAAK,OAAMA,GAAE,KAAK,KAAG,EAAE,KAAK,KAAIA,GAAE,GAAG,KAAG,KAAK,SAAOA,GAAE;AAAA,EAAI;AAAA,EAAC,UAAUA,IAAE;AAAC,WAAOa,GAAE,MAAKb,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiB,GAAEI,IAAE;AAAC,eAAUiB,MAAKjB,IAAE;AAAC,YAAMA,KAAE,EAAE,KAAK,KAAKiB,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,CAAC;AAAE,WAAK,IAAIA,GAAE,CAAC,IAAEjB,EAAC,IAAE,MAAIiB,GAAE,CAAC,IAAEjB,IAAE,EAAE,KAAKiB,EAAC;AAAA,IAAE;AAAA,EAAC;AAAC;AAAC,SAASR,GAAEb,IAAEI,IAAE;AAAC,QAAMiB,KAAE,EAAEjB,IAAEJ,GAAE,SAAS;AAAE,SAAOqB,GAAE,CAAC,IAAE,EAAErB,GAAE,KAAKI,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC,KAAGkB,IAAEhB,GAAEe,EAAC;AAAC;AAAC,SAAS,EAAErB,IAAE,GAAE;AAAC,MAAII,KAAE,CAAC;AAAE,MAAGF,GAAEF,EAAC,GAAE;AAAC,UAAK,EAAC,OAAMqB,GAAC,IAAErB;AAAE,IAAAK,GAAE,CAAC,IAAED,KAAEmB,GAAE,EAAE,UAASF,EAAC,IAAET,GAAE,CAAC,IAAER,KAAE,EAAE,EAAE,QAAO,EAAE,QAAOiB,EAAC,IAAEP,GAAE,CAAC,IAAEV,KAAE,EAAE,EAAE,WAAUiB,EAAC,IAAED,GAAE,CAAC,MAAIhB,KAAEoB,GAAE,GAAExB,EAAC;AAAA,EAAE,WAASK,GAAEL,EAAC,GAAE;AAAC,UAAK,EAAC,UAASqB,GAAC,IAAErB;AAAE,IAAAE,GAAE,CAAC,IAAEE,KAAEmB,GAAEF,IAAE,EAAE,KAAK,IAAEhB,GAAE,CAAC,IAAED,KAAEqB,GAAEJ,IAAE,EAAE,QAAQ,IAAET,GAAE,CAAC,IAAER,KAAE,EAAEiB,IAAE,EAAE,QAAO,EAAE,MAAM,IAAEP,GAAE,CAAC,IAAEV,KAAEsB,GAAE,EAAE,WAAUL,EAAC,IAAED,GAAE,CAAC,MAAIhB,KAAEoB,GAAE,GAAExB,EAAC;AAAA,EAAE,WAASY,GAAEZ,EAAC,GAAE;AAAC,UAAK,EAAC,QAAOqB,IAAE,QAAOX,GAAC,IAAEV;AAAE,QAAGK,GAAE,CAAC,EAAE,CAAAD,KAAE,EAAE,EAAE,UAASiB,IAAEX,EAAC;AAAA,aAAUR,GAAE,CAAC,EAAE,CAAAE,KAAE,EAAEiB,IAAEX,IAAE,EAAE,KAAK;AAAA,SAAM;AAAC,UAAGI,GAAE,CAAC,EAAE,QAAOa,GAAE,EAAE,WAAUN,IAAEX,EAAC,EAAE,IAAK,CAAAN,OAAG,IAAIK,GAAEL,IAAEJ,IAAE,CAAC,CAAE;AAAE,MAAAoB,GAAE,CAAC,MAAIhB,KAAEoB,GAAE,GAAExB,EAAC;AAAA,IAAE;AAAA,EAAC,WAASc,GAAEd,EAAC,GAAE;AAAC,UAAK,EAAC,WAAUqB,GAAC,IAAErB;AAAE,QAAGc,GAAE,CAAC,EAAE,QAAO,EAAEO,IAAE,EAAE,SAAS,EAAE,IAAK,CAAAjB,OAAG,IAAIK,GAAEL,IAAEJ,IAAE,CAAC,CAAE;AAAE,QAAGE,GAAE,CAAC,EAAE,CAAAE,KAAE,EAAEiB,IAAE,EAAE,KAAK;AAAA,aAAUhB,GAAE,CAAC,EAAE,CAAAD,KAAEsB,GAAEL,IAAE,EAAE,QAAQ;AAAA,SAAM;AAAC,UAAGT,GAAE,CAAC,EAAE,QAAOe,GAAEN,IAAE,EAAE,QAAO,EAAE,MAAM,EAAE,IAAK,CAAAjB,OAAG,IAAIK,GAAEL,IAAEJ,IAAE,CAAC,CAAE;AAAE,MAAAoB,GAAE,CAAC,MAAIhB,KAAEoB,GAAE,GAAExB,EAAC;AAAA,IAAE;AAAA,EAAC,MAAM,CAAAoB,GAAEpB,EAAC,MAAII,KAAEoB,GAAExB,IAAE,CAAC;AAAG,SAAO4B,GAAExB,IAAEJ,IAAE,CAAC;AAAC;AAAC,SAASwB,GAAExB,IAAEqB,IAAE;AAAC,QAAK,EAAC,WAAUX,IAAE,MAAK,EAAC,IAAEV,IAAEe,KAAE,CAAC;AAAE,MAAGb,GAAEmB,EAAC,EAAE,CAAArB,GAAE,iBAAiBe,IAAE,EAAEL,IAAEW,GAAE,KAAK,CAAC;AAAA,WAAUhB,GAAEgB,EAAC,EAAE,CAAArB,GAAE,iBAAiBe,IAAEW,GAAEhB,IAAEW,GAAE,QAAQ,CAAC;AAAA,WAAUT,GAAES,EAAC,EAAE,YAAS,CAAC,GAAEQ,EAAC,KAAIF,GAAEjB,IAAEW,GAAE,QAAOA,GAAE,MAAM,GAAE;AAAC,UAAMrB,KAAE,EAAE,GAAE6B,IAAE,CAAC;AAAE,MAAE7B,EAAC,KAAGe,GAAE,KAAKL,GAAE,GAAEmB,IAAE7B,EAAC,CAAC;AAAA,EAAC;AAAA,WAASc,GAAEO,EAAC,KAAGD,GAAEC,EAAC,EAAE,YAAS,CAACjB,IAAEyB,EAAC,KAAI,EAAEnB,IAAEW,GAAE,SAAS,GAAE;AAAC,UAAMrB,KAAE,EAAE,EAAEI,IAAEyB,IAAE,CAAC,CAAC,KAAGP;AAAE,IAAAP,GAAE,KAAKL,GAAEN,IAAEyB,IAAE7B,EAAC,CAAC;AAAA,EAAC;AAAC,SAAOe;AAAC;AAAC,SAASa,GAAE5B,IAAE,GAAEI,IAAE;AAAC,SAAOJ,GAAE,IAAK,CAAAA,OAAG,IAAIQ,GAAEF,GAAEN,EAAC,GAAE,GAAEI,EAAC,CAAE;AAAC;AAAC,SAASF,GAAEF,IAAE;AAAC,SAAOA,cAAaC;AAAC;AAAC,SAASI,GAAEL,IAAE;AAAC,SAAOA,cAAa;AAAC;AAAC,SAASY,GAAEZ,IAAE;AAAC,SAAOA,cAAaW;AAAC;AAAC,SAASG,GAAEd,IAAE;AAAC,SAAOA,cAAa;AAAC;AAAC,SAASoB,GAAEpB,IAAE;AAAC,SAAOA,cAAamB;AAAC;AAAC,IAAMH,KAAEK,GAAE;AAAV,IAAYJ,KAAEI,GAAE;AAAhB,IAAkBC,KAAE;;;ACAntI,IAAMQ,KAAN,MAAO;AAAA,EAAC,YAAYA,IAAE,GAAE,GAAEC,IAAE;AAAC,SAAK,cAAYD,IAAE,KAAK,aAAW,GAAE,KAAK,WAAS,GAAE,KAAK,SAAOC;AAAA,EAAC;AAAC;;;ACAiB,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAY,EAAC,aAAYC,IAAE,UAASF,IAAE,YAAWG,IAAE,UAAS,EAAC,GAAE;AAAC,UAAMD,IAAEC,IAAE,GAAE,EAAE,OAAO,GAAE,KAAK,WAASH;AAAA,EAAC;AAAC;;;ACAmB,IAAMI,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAM,EAAC,GAAGA,IAAE,YAAW,IAAIC,GAAED,GAAE,WAAUA,GAAE,OAAO,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAM,CAAC,IAAIA,GAAE,EAAE,WAAU,KAAK,WAAW,OAAM,KAAK,WAAW,KAAI,KAAK,UAAS,KAAK,MAAM,CAAC;AAAA,EAAC;AAAC;", "names": ["Y", "t", "o", "u", "p", "C", "A", "E", "n", "r", "j", "e", "f", "J", "N", "d", "m", "P", "b", "x", "g", "q", "_", "F", "t", "r", "o", "C", "u", "q", "_", "t", "n", "r", "e", "C", "o", "b", "t", "e", "o", "u", "f", "C", "p", "A", "n", "b", "r", "q", "J", "E", "d", "m", "P", "x", "g", "P", "t", "Z", "_", "f", "e", "S", "l", "E", "b", "v", "r", "w", "z", "F", "B", "o", "D", "J", "G", "O", "C", "n", "K", "x", "M", "q", "j", "Y", "U", "u", "t", "o", "n", "t", "o", "r", "r", "n", "E"]}