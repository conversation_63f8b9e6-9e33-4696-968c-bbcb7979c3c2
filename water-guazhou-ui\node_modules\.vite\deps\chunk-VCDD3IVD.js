import {
  a as a3,
  c,
  f as f2
} from "./chunk-IKOX2HGY.js";
import {
  q
} from "./chunk-XXXEFHEN.js";
import {
  $,
  B,
  D,
  F,
  M,
  Z as Z2,
  d,
  g,
  h,
  j,
  p as p2,
  w as w2,
  x as x2,
  y as y2
} from "./chunk-OY3C7FMJ.js";
import {
  W,
  e2
} from "./chunk-NQQSL2QK.js";
import {
  f,
  n2,
  p
} from "./chunk-IZLLLMFE.js";
import {
  M as M2,
  b as b2,
  v as v2,
  y as y3
} from "./chunk-4RJYWSAT.js";
import {
  An,
  Un,
  hn,
  jn
} from "./chunk-UYAKJRPP.js";
import {
  l as l2,
  w
} from "./chunk-QUHG7NMD.js";
import {
  m
} from "./chunk-RFYOGM4H.js";
import {
  b
} from "./chunk-QMNV7QQK.js";
import {
  E,
  I3 as I,
  N,
  Z,
  ae,
  ee,
  k,
  o as o2
} from "./chunk-JXLVNWKF.js";
import {
  o as o3,
  x
} from "./chunk-MQAXMQFG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  a,
  l,
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/quantityUtils.js
function r2(t4, u3) {
  return { type: I(u3), value: t4, unit: u3 };
}
function a4(t4, u3) {
  return { type: I(u3), value: t4, unit: u3 };
}
function o4(t4, u3) {
  return { type: I(u3), value: t4, unit: u3 };
}
function c2(t4, u3, e4 = "arithmetic") {
  return { type: I(u3), value: t4, unit: u3, rotationType: e4 };
}
function v3(t4, n7) {
  return r2(N(t4.value, t4.unit, n7), n7);
}
function p3(n7, u3) {
  return t(n7) ? u3 : t(u3) || n7.value > N(u3.value, u3.unit, n7.unit) ? n7 : u3;
}
function y4(n7, u3) {
  return t(n7) ? null : { ...n7, value: n7.value * u3 };
}
var j2 = a4(0, "meters");
var U = o4(0, "square-meters");
var b3 = c2(0, "radians");

// node_modules/@arcgis/core/views/interactive/tooltip/css.js
var e3 = "esri-tooltip";
var t2 = `${e3}-content`;
var o5 = `${e3}-table`;
var s = `${e3}-help-message`;

// node_modules/@arcgis/core/core/quantityFormatUtils.js
function g2(t4, a19, n7, u3 = 2, i4 = "abbr") {
  return g(t4, v3(a19, n7).value, n7, u3, i4);
}
function L(t4, e4, n7, u3 = 2, i4 = "abbr") {
  return p2(t4, v3(e4, n7).value, n7, u3, i4);
}
function M3(t4, r4, e4 = 2, a19 = "abbr") {
  return D(t4, r4.value, r4.unit, e4, a19);
}
function d2(t4, r4, e4 = 2, a19 = "abbr") {
  return x2(t4, r4.value, r4.unit, e4, a19);
}
function w3(t4, r4, e4 = 2, a19 = "abbr") {
  return y2(t4, r4.value, r4.unit, e4, a19);
}
function I2(t4, r4, e4 = 2, a19 = "abbr") {
  return F(t4, r4.value, r4.unit, e4, a19);
}
function R(t4, r4, e4 = 2, a19 = "abbr") {
  return M(t4, r4.value, r4.unit, e4, a19);
}
function y5(t4, r4, e4 = 2, a19 = "abbr") {
  return h(t4, r4.value, r4.unit, e4, a19);
}
function V(t4, r4, e4 = 2, a19 = "abbr") {
  return d(t4, r4.value, r4.unit, e4, a19);
}
function j3(t4, r4, e4 = 2, a19 = "abbr") {
  return j(t4, r4.value, r4.unit, e4, a19);
}
function D2(t4, r4, e4 = 2, a19 = "abbr") {
  return B(t4, r4.value, r4.unit, e4, a19);
}
function A(t4, r4, e4 = 2, a19 = "abbr") {
  return $(t4, r4.value, r4.unit, e4, a19);
}
function U2(t4, r4, e4) {
  return w2(t4.value, t4.unit, t4.rotationType, r4, e4);
}
function q2(t4, r4, e4) {
  return Z2(t4.value, t4.unit, t4.rotationType, r4, e4);
}
function x4(r4, e4, a19, n7, u3 = "abbr") {
  switch (n7 = l(n7, 2), a19) {
    case "imperial":
      return y5(r4, e4, n7, u3);
    case "metric":
      return M3(r4, e4, n7, u3);
    default:
      return g2(r4, e4, a19, n7, u3);
  }
}
function F2(t4, r4, e4, a19 = 2, n7 = "abbr") {
  switch (e4) {
    case "imperial":
      return V(t4, r4, a19, n7);
    case "metric":
      return d2(t4, r4, a19, n7);
    default:
      return L(t4, r4, e4, a19, n7);
  }
}
function S2(t4, r4, e4, a19 = 2, n7 = "abbr") {
  switch (e4) {
    case "imperial":
      return j3(t4, r4, a19, n7);
    case "metric":
      return w3(t4, r4, a19, n7);
    default:
      return g2(t4, r4, e4, a19, n7);
  }
}
function k2(t4, r4, e4, a19 = 2, n7 = "abbr") {
  switch (e4) {
    case "imperial":
      return D2(t4, r4, a19, n7);
    case "metric":
      return I2(t4, r4, a19, n7);
    default:
      return L(t4, r4, e4, a19, n7);
  }
}
function z(t4, r4, e4, a19 = 2, n7 = "abbr") {
  switch (e4) {
    case "imperial":
      return A(t4, r4, a19, n7);
    case "metric":
      return R(t4, r4, a19, n7);
    default:
      return g2(t4, r4, e4, a19, n7);
  }
}

// node_modules/@arcgis/core/support/getDefaultUnitForView.js
function i(i4) {
  var _a, _b;
  const n7 = "metric";
  if (t(i4)) return n7;
  const s2 = i4.map, a19 = (s2 && "portalItem" in s2 ? (_a = s2.portalItem) == null ? void 0 : _a.portal : null) ?? b.getDefault();
  switch (((_b = a19.user) == null ? void 0 : _b.units) ?? a19.units) {
    case n7:
      return n7;
    case "english":
      return "imperial";
  }
  return l(ae(i4.spatialReference), n7);
}

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContent.js
var u = class extends W {
  get _units() {
    const t4 = i(this.tooltip.view);
    return { length: t4, verticalLength: t4, area: t4 };
  }
  _getHelpMessage(t4) {
    var _a, _b, _c, _d;
    const { info: e4 } = this, { tooltipOptions: s2, helpMessage: i4, viewType: r4 } = e4, o6 = (_a = s2 == null ? void 0 : s2.visibleElements) == null ? void 0 : _a.helpMessage, n7 = t4 ?? i4, a19 = "3d" === r4 ? "helpMessages3d" : "helpMessages2d";
    return o6 && n7 ? (_d = (_c = (_b = this._messagesTooltip) == null ? void 0 : _b.sketch) == null ? void 0 : _c[a19]) == null ? void 0 : _d[n7] : void 0;
  }
  _formatScale(t4) {
    return m(t4, { style: "percent", maximumFractionDigits: 0 });
  }
  _formatRelativeOrientation(t4) {
    return m(t4, { maximumFractionDigits: 2, minimumFractionDigits: 2, signDisplay: "exceptZero" });
  }
  _formatLength(t4, i4, r4) {
    return x4(this._messagesUnits, t4, l(i4, this._units.length), r4);
  }
  _formatRelativeLength(t4) {
    return F2(this._messagesUnits, t4, this._units.length);
  }
  _formatVerticalLength(t4) {
    return S2(this._messagesUnits, t4, this._units.verticalLength);
  }
  _formatRelativeVerticalLength(t4) {
    return k2(this._messagesUnits, t4, this._units.verticalLength);
  }
  _formatTotalLength(t4) {
    return x4(this._messagesUnits, t4, this._units.length);
  }
  _formatArea(t4) {
    return z(this._messagesUnits, t4, this._units.area);
  }
  _formatPercentage(t4) {
    return m(t4.value, { style: "percent" });
  }
};
e([y()], u.prototype, "info", void 0), e([y()], u.prototype, "tooltip", void 0), e([y()], u.prototype, "_units", null), e([e2("esri/core/t9n/Units"), y()], u.prototype, "_messagesUnits", void 0), e([e2("esri/views/interactive/tooltip/t9n/Tooltip"), y()], u.prototype, "_messagesTooltip", void 0), u = e([a2("esri.views.interactive.tooltip.content.TooltipContent")], u);

// node_modules/@arcgis/core/views/interactive/tooltip/support/TooltipContentWithHelpMessage.js
function i2({ className: i4, helpMessage: o6 }, ...p8) {
  const a19 = p8.filter((s2) => !!s2);
  return n2("div", { class: p(i4, t2) }, a19.length > 0 ? n2("div", { class: o5 }, ...a19) : null, o6 ? n2("div", { key: "help-message", class: s }, o6) : null);
}

// node_modules/@arcgis/core/views/interactive/tooltip/support/TooltipField.js
var p4 = `${e3}-field`;
var a5 = { base: p4, title: `${p4}__title`, value: `${p4}__value` };
var l3 = class extends W {
  render() {
    return n2("div", { class: a5.base }, n2("div", { class: a5.title }, this.title), n2("div", { class: a5.value }, this.value));
  }
};
e([y()], l3.prototype, "title", void 0), e([y()], l3.prototype, "value", void 0), l3 = e([a2("esri.views.interactive.tooltip.support.TooltipField")], l3);

// node_modules/@arcgis/core/views/interactive/tooltip/support/ValueByValue.js
var p5 = { base: `${e3}-value-by-value` };
var a6 = class extends W {
  constructor() {
    super(...arguments), this.divider = "×";
  }
  render() {
    return n2("div", { class: p5.base }, n2("span", null, this.left), n2("span", null, this.divider), n2("span", null, this.right));
  }
};
e([y()], a6.prototype, "left", void 0), e([y()], a6.prototype, "divider", void 0), e([y()], a6.prototype, "right", void 0), a6 = e([a2("esri.views.interactive.tooltip.support.ValueByValue")], a6);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentDrawCircle.js
var l4 = { base: `${t2} ${`${t2}--draw-rectangle`}` };
var c3 = class extends u {
  render() {
    const { area: t4, radius: s2, xSize: r4, ySize: o6, tooltipOptions: c11 } = this.info, { visibleElements: n7 } = c11, u3 = this._messagesTooltip.sketch;
    return n2(i2, { className: l4.base, helpMessage: this._getHelpMessage() }, n7.radius && r(s2) && n2(l3, { title: u3.radius, value: this._formatLength(s2) }), n7.size && r(r4) && r(o6) && n2(l3, { title: u3.size, value: n2(a6, { left: this._formatLength(r4), right: this._formatLength(o6) }) }), n7.area && n2(l3, { title: u3.area, value: this._formatArea(t4) }));
  }
};
c3 = e([a2("esri.views.interactive.tooltip.content.TooltipContentDrawCircle")], c3);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentDrawPoint.js
var a7 = { base: `${t2}--draw-point` };
var l5 = class extends u {
  render() {
    const { elevation: t4, tooltipOptions: o6 } = this.info, { visibleElements: s2 } = o6, e4 = this._messagesTooltip.sketch;
    return n2(i2, { className: a7.base, helpMessage: this._getHelpMessage() }, s2.elevation && n2(l3, { title: e4.elevation, value: this._formatVerticalLength(t4) }));
  }
};
l5 = e([a2("esri.views.interactive.tooltip.content.TooltipContentDrawPoint")], l5);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentDrawPolygon.js
var a8 = { base: `${t2} ${`${t2}--draw-polygon`}` };
var l6 = class extends u {
  render() {
    const { area: t4, elevation: o6, tooltipOptions: e4, viewType: s2 } = this.info, { visibleElements: l16 } = e4, m8 = this._messagesTooltip.sketch;
    return n2(i2, { className: a8.base, helpMessage: this._getHelpMessage() }, l16.elevation && "2d" !== s2 && n2(l3, { title: m8.elevation, value: this._formatVerticalLength(o6) }), l16.area && n2(l3, { title: m8.area, value: this._formatArea(t4) }));
  }
};
l6 = e([a2("esri.views.interactive.tooltip.content.TooltipContentDrawPolygon")], l6);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentDrawPolyline.js
var l7 = { base: `${t2} ${`${t2}--draw-polyline`}` };
var a9 = class extends u {
  render() {
    const { elevation: t4, totalLength: e4, tooltipOptions: o6, viewType: s2 } = this.info, { visibleElements: a19 } = o6, n7 = this._messagesTooltip.sketch;
    return n2(i2, { className: l7.base, helpMessage: this._getHelpMessage() }, a19.elevation && "2d" !== s2 && n2(l3, { title: n7.elevation, value: this._formatVerticalLength(t4) }), a19.totalLength && n2(l3, { title: n7.totalLength, value: this._formatLength(e4) }));
  }
};
a9 = e([a2("esri.views.interactive.tooltip.content.TooltipContentDrawPolyline")], a9);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentDrawRectangle.js
var l8 = { base: `${t2} ${`${t2}--draw-rectangle`}` };
var c4 = class extends u {
  render() {
    const { area: t4, xSize: s2, ySize: o6, tooltipOptions: r4 } = this.info, { visibleElements: c11 } = r4, n7 = this._messagesTooltip.sketch;
    return n2(i2, { className: l8.base, helpMessage: this._getHelpMessage() }, c11.size && r(s2) && r(o6) && n2(l3, { title: n7.size, value: n2(a6, { left: this._formatLength(s2), right: this._formatLength(o6) }) }), c11.area && n2(l3, { title: n7.area, value: this._formatArea(t4) }));
  }
};
c4 = e([a2("esri.views.interactive.tooltip.content.TooltipContentDrawRectangle")], c4);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentExtentRotate.js
var a10 = { base: `${t2} ${`${t2}--extent-rotate`}` };
var n3 = class extends u {
  render() {
    const { angle: t4, tooltipOptions: o6 } = this.info, { visibleElements: s2 } = o6, e4 = this._messagesTooltip.sketch;
    return n2(i2, { className: a10.base, helpMessage: this._getHelpMessage() }, s2.rotation && n2(l3, { title: e4.rotation, value: this._formatRelativeOrientation(t4) }));
  }
};
n3 = e([a2("esri.views.interactive.tooltip.content.TooltipContentExtentRotate")], n3);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentExtentScale.js
var a11 = { base: `${t2} ${`${t2}--extent-scale`}` };
var c5 = class extends u {
  render() {
    const t4 = this.info, { visibleElements: e4 } = t4.tooltipOptions, s2 = this._messagesTooltip.sketch;
    return n2(i2, { className: a11.base, helpMessage: this._getHelpMessage() }, e4.size && n2(l3, { title: s2.size, value: n2(a6, { left: this._formatLength(t4.xSize), right: this._formatLength(t4.ySize) }) }), e4.scale && n2(l3, { title: s2.scale, value: n2(a6, { left: this._formatScale(t4.xScale), right: this._formatScale(t4.yScale) }) }));
  }
};
c5 = e([a2("esri.views.interactive.tooltip.content.TooltipContentExtentScale")], c5);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentReshapeEdgeOffset.js
var l9 = { base: `${t2} ${`${t2}--reshape-edge-offset`}` };
var m2 = class extends u {
  render() {
    const { area: t4, distance: s2, totalLength: o6, tooltipOptions: r4 } = this.info, { visibleElements: m8 } = r4, n7 = this._messagesTooltip.sketch;
    return n2(i2, { className: l9.base, helpMessage: this._getHelpMessage() }, m8.distance && n2(l3, { title: n7.distance, value: this._formatRelativeLength(s2) }), m8.area && r(t4) && n2(l3, { title: n7.area, value: this._formatArea(t4) }), m8.totalLength && r(o6) && n2(l3, { title: n7.totalLength, value: this._formatLength(o6) }));
  }
};
m2 = e([a2("esri.views.interactive.tooltip.content.TooltipContentReshapeEdgeOffset")], m2);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentTransformAbsolute.js
var a12 = { base: `${t2} ${`${t2}--transform-absolute`}` };
var l10 = class extends u {
  render() {
    const { info: t4 } = this, { visibleElements: s2 } = t4.tooltipOptions, e4 = this._messagesTooltip.sketch;
    return n2(i2, { className: a12.base, helpMessage: this._getHelpMessage() }, s2.orientation && t4.orientationEnabled && n2(l3, { title: e4.orientation, value: U2(t4.orientation, t4.rotationType, t4.orientationPrecision) }), s2.size && t4.sizeEnabled && n2(l3, { title: e4.size, value: this._formatLength(t4.size, t4.sizeUnit, t4.sizePrecision) }));
  }
};
l10 = e([a2("esri.views.interactive.tooltip.content.TooltipContentTransformAbsolute")], l10);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentTransformRotate.js
var m3 = { base: `${t2} ${`${t2}--transform-rotate`}` };
var c6 = class extends u {
  render() {
    const { info: t4 } = this, { visibleElements: s2 } = t4.tooltipOptions, e4 = this._messagesTooltip.sketch;
    return n2(i2, { className: m3.base, helpMessage: this._getHelpMessage() }, s2.rotation && n2(l3, { title: e4.rotation, value: q2(t4.rotation, t4.rotationType, t4.rotationPrecision) }), s2.orientation && n2(l3, { title: e4.orientation, value: U2(t4.orientation, t4.rotationType, t4.orientationPrecision) }));
  }
};
c6 = e([a2("esri.views.interactive.tooltip.content.TooltipContentTransformRotate")], c6);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentTransformScale.js
var a13 = { base: `${t2} ${`${t2}--transform-scale`}` };
var c7 = class extends u {
  render() {
    const { scale: s2, size: t4, sizePrecision: e4, sizeUnit: o6, tooltipOptions: c11 } = this.info, { visibleElements: l16 } = c11, m8 = this._messagesTooltip.sketch;
    return n2(i2, { className: a13.base, helpMessage: this._getHelpMessage() }, l16.scale && n2(l3, { title: m8.scale, value: this._formatPercentage(s2) }), l16.size && n2(l3, { title: m8.size, value: this._formatLength(t4, o6, e4) }));
  }
};
c7 = e([a2("esri.views.interactive.tooltip.content.TooltipContentTransformScale")], c7);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentTranslateGraphic.js
var a14 = { base: `${t2} ${`${t2}--translate-graphic`}` };
var c8 = class extends u {
  render() {
    const { info: s2 } = this, { visibleElements: t4 } = s2.tooltipOptions, o6 = this._messagesTooltip.sketch;
    return n2(i2, { className: a14.base, helpMessage: this._getHelpMessage() }, t4.distance && n2(l3, { title: o6.distance, value: this._formatLength(s2.distance) }));
  }
};
c8 = e([a2("esri.views.interactive.tooltip.content.TooltipContentTranslateGraphic")], c8);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentTranslateGraphicXY.js
var a15 = { base: `${t2} ${`${t2}--translate-graphic-xy`}` };
var c9 = class extends u {
  render() {
    const { info: t4 } = this, { visibleElements: s2 } = t4.tooltipOptions, o6 = this._messagesTooltip.sketch;
    return n2(i2, { className: a15.base, helpMessage: this._getHelpMessage() }, s2.distance && n2(l3, { title: o6.distance, value: this._formatRelativeLength(t4.distance) }));
  }
};
c9 = e([a2("esri.views.interactive.tooltip.content.TooltipContentTranslateGraphicXY")], c9);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentTranslateGraphicZ.js
var a16 = { base: `${t2} ${`${t2}--translate-graphic-z`}` };
var c10 = class extends u {
  render() {
    const { info: t4 } = this, { visibleElements: s2 } = t4.tooltipOptions, e4 = this._messagesTooltip.sketch;
    return n2(i2, { className: a16.base, helpMessage: this._getHelpMessage() }, s2.distance && n2(l3, { title: e4.distance, value: this._formatRelativeVerticalLength(t4.distance) }));
  }
};
c10 = e([a2("esri.views.interactive.tooltip.content.TooltipContentTranslateGraphicZ")], c10);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentTranslateVertex.js
var l11 = { base: `${t2} ${`${t2}--translate-vertex`}` };
var n4 = class extends u {
  render() {
    const { distance: t4, elevation: o6, area: s2, totalLength: r4, tooltipOptions: n7 } = this.info, { visibleElements: m8 } = n7, c11 = this._messagesTooltip.sketch;
    return n2(i2, { className: l11.base, helpMessage: this._getHelpMessage() }, m8.distance && n2(l3, { title: c11.distance, value: this._formatLength(t4) }), m8.elevation && r(o6) && n2(l3, { title: c11.elevation, value: this._formatVerticalLength(o6) }), m8.area && r(s2) && n2(l3, { title: c11.area, value: this._formatArea(s2) }), m8.totalLength && r(r4) && n2(l3, { title: c11.totalLength, value: this._formatLength(r4) }));
  }
};
n4 = e([a2("esri.views.interactive.tooltip.content.TooltipContentTranslateVertex")], n4);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentTranslateVertexXY.js
var l12 = { base: `${t2} ${`${t2}--translate-vertex`}` };
var n5 = class extends u {
  render() {
    const { area: t4, distance: o6, elevation: s2, totalLength: r4, tooltipOptions: n7 } = this.info, { visibleElements: m8 } = n7, c11 = this._messagesTooltip.sketch;
    return n2(i2, { className: l12.base, helpMessage: this._getHelpMessage() }, m8.distance && n2(l3, { title: c11.distance, value: this._formatRelativeLength(o6) }), m8.elevation && r(s2) && n2(l3, { title: c11.elevation, value: this._formatVerticalLength(s2) }), m8.area && r(t4) && n2(l3, { title: c11.area, value: this._formatArea(t4) }), m8.totalLength && r(r4) && n2(l3, { title: c11.totalLength, value: this._formatLength(r4) }));
  }
};
n5 = e([a2("esri.views.interactive.tooltip.content.TooltipContentTranslateVertexXY")], n5);

// node_modules/@arcgis/core/views/interactive/tooltip/content/TooltipContentTranslateVertexZ.js
var l13 = { base: `${t2} ${`${t2}--translate-vertex`}` };
var n6 = class extends u {
  render() {
    const { distance: t4, elevation: s2, tooltipOptions: o6 } = this.info, { visibleElements: r4 } = o6, n7 = this._messagesTooltip.sketch;
    return n2(i2, { className: l13.base, helpMessage: this._getHelpMessage() }, r4.distance && n2(l3, { title: n7.distance, value: this._formatRelativeVerticalLength(t4) }), r4.elevation && r(s2) && n2(l3, { title: n7.elevation, value: this._formatVerticalLength(s2) }));
  }
};
n6 = e([a2("esri.views.interactive.tooltip.content.TooltipContentTranslateVertexZ")], n6);

// node_modules/@arcgis/core/views/interactive/tooltip/content/tooltipContentFactory.js
function x5(x8, d6) {
  if (t(d6)) return null;
  const g5 = document.createElement("div");
  switch (d6.type) {
    case "draw-point":
      return new l5({ tooltip: x8, info: d6, container: g5 });
    case "draw-polygon":
      return new l6({ tooltip: x8, info: d6, container: g5 });
    case "draw-polyline":
      return new a9({ tooltip: x8, info: d6, container: g5 });
    case "draw-rectangle":
      return new c4({ tooltip: x8, info: d6, container: g5 });
    case "draw-circle":
      return new c3({ tooltip: x8, info: d6, container: g5 });
    case "extent-rotate":
      return new n3({ tooltip: x8, info: d6, container: g5 });
    case "extent-scale":
      return new c5({ tooltip: x8, info: d6, container: g5 });
    case "transform-absolute":
      return new l10({ tooltip: x8, info: d6, container: g5 });
    case "transform-rotate":
      return new c6({ tooltip: x8, info: d6, container: g5 });
    case "transform-scale":
      return new c7({ tooltip: x8, info: d6, container: g5 });
    case "translate-graphic":
      return new c8({ tooltip: x8, info: d6, container: g5 });
    case "translate-graphic-z":
      return new c10({ tooltip: x8, info: d6, container: g5 });
    case "translate-graphic-xy":
      return new c9({ tooltip: x8, info: d6, container: g5 });
    case "translate-vertex":
      return new n4({ tooltip: x8, info: d6, container: g5 });
    case "translate-vertex-z":
      return new n6({ tooltip: x8, info: d6, container: g5 });
    case "translate-vertex-xy":
      return new n5({ tooltip: x8, info: d6, container: g5 });
    case "reshape-edge-offset":
      return new m2({ tooltip: x8, info: d6, container: g5 });
  }
}

// node_modules/@arcgis/core/views/interactive/tooltip/Tooltip.js
var h2 = { base: `${e3}` };
var m4 = class extends v {
  constructor(t4) {
    super(t4), this.info = null, this._contentContainer = (() => {
      const t5 = document.createElement("div");
      return t5.classList.add(h2.base), t5;
    })(), this._contentWidget = null;
  }
  initialize() {
    const t4 = this._contentContainer;
    this.addHandles([l2(() => {
      var _a;
      return (_a = this.view.overlay) == null ? void 0 : _a.surface;
    }, (e4) => {
      t4.remove(), o(e4, (e5) => e5.appendChild(t4));
    }, w), l2(() => this.info, (e4, s2) => {
      r(this._contentWidget) && r(e4) && r(s2) && e4.type === s2.type ? this._contentWidget.info = e4 : (this._contentWidget = a(this._contentWidget), o(x5(this, e4), (e5) => {
        this._contentWidget = e5, e5.container && t4.appendChild(e5.container);
      }));
    }, w), l2(() => ({ container: this._contentContainer, contentWidget: this._contentWidget, screenPoint: this._screenPoint }), u2, w)]);
  }
  destroy() {
    this._contentWidget = a(this._contentWidget), this._contentContainer.remove();
  }
  clear() {
    this.info = null;
  }
  get _screenPoint() {
    const t4 = this.view.inputManager;
    return (t4 == null ? void 0 : t4.multiTouchActive) ? null : t4 == null ? void 0 : t4.latestPointerLocation;
  }
  get test() {
    var _a;
    return { contentContainer: this._contentContainer, visible: "none" !== ((_a = this._contentContainer) == null ? void 0 : _a.style.display) };
  }
};
function u2({ container: t4, contentWidget: e4, screenPoint: o6 }) {
  const { style: i4 } = t4;
  if (r(o6) && r(e4)) {
    i4.display = "block";
    const e5 = f(t4), n7 = `translate(${Math.round(o6.x) + _[0] * (e5 ? -1 : 1)}px, ${Math.round(o6.y) + _[1]}px)`;
    i4.transform = e5 ? `translate(-100%, 0) ${n7}` : n7;
  } else i4.display = "none";
}
e([y({ nonNullable: true })], m4.prototype, "view", void 0), e([y()], m4.prototype, "info", void 0), e([y()], m4.prototype, "_contentContainer", void 0), e([y()], m4.prototype, "_contentWidget", void 0), e([y()], m4.prototype, "_screenPoint", null), m4 = e([a2("esri.views.interactive.tooltip.Tooltip")], m4);
var _ = [20, 20];

// node_modules/@arcgis/core/views/support/measurementUtils.js
function i3(i4) {
  const m8 = c(i4), n7 = m8 === f2 ? a3 : m8;
  return An(i4, n7) ? n7 : i4;
}

// node_modules/@arcgis/core/views/support/euclideanLengthMeasurementUtils.js
var f3;
function p6(t4) {
  return m5(t4, f3.Direct);
}
function h3(t4) {
  return m5(t4, f3.Horizontal);
}
function m5(n7, i4) {
  const { hasZ: o6, spatialReference: c11 } = n7, u3 = i3(c11);
  let l16 = 0;
  const a19 = Z(u3);
  if (t(a19)) return null;
  const p8 = i4 === f3.Direct ? j4 : d3;
  for (const r4 of n7.paths) {
    if (r4.length < 2) continue;
    const n8 = r4.length - 1;
    for (let e4 = 0; e4 < n8; ++e4) {
      const n9 = r4[e4];
      U3[0] = n9[0], U3[1] = n9[1], U3[2] = o6 ? n9[2] : 0;
      const i5 = r4[e4 + 1];
      b4[0] = i5[0], b4[1] = i5[1], b4[2] = o6 ? i5[2] : 0;
      const u4 = p8(U3, b4, c11);
      if (t(u4)) return null;
      l16 += u4.value;
    }
  }
  return a4(l16, a19);
}
function R2(t4, n7) {
  const { spatialReference: r4 } = t4;
  return E(r4, n7.spatialReference) ? (U3[0] = t4.x, U3[1] = t4.y, U3[2] = t4.hasZ ? t4.z : 0, b4[0] = n7.x, b4[1] = n7.y, b4[2] = n7.hasZ ? n7.z : 0, d3(U3, b4, r4)) : null;
}
function v4(t4, n7) {
  const { spatialReference: r4 } = t4;
  return E(r4, n7.spatialReference) ? (U3[0] = t4.x, U3[1] = t4.y, U3[2] = t4.hasZ ? t4.z : 0, b4[0] = n7.x, b4[1] = n7.y, b4[2] = n7.hasZ ? n7.z : 0, D3(U3, b4, r4)) : null;
}
function x6(t4) {
  return U3[0] = t4.x, U3[1] = t4.y, U3[2] = t4.hasZ ? t4.z : 0, H(U3, t4.spatialReference);
}
function j4(t4, e4, i4) {
  const o6 = V2(t4, e4, i4, f3.Direct);
  return r(o6) ? a4(o6.direct, o6.unit) : null;
}
function d3(t4, e4, i4) {
  const o6 = V2(t4, e4, i4, f3.Horizontal);
  return r(o6) ? a4(o6.horizontal, o6.unit) : null;
}
function D3(t4, e4, i4) {
  const o6 = V2(t4, e4, i4, f3.Vertical);
  return r(o6) ? a4(o6.verticalSigned, o6.unit) : null;
}
function H(t4, e4) {
  const o6 = ee(e4);
  return r(o6) ? a4(t4[2], o6) : null;
}
function V2(n7, r4, i4, u3) {
  const a19 = i3(i4), p8 = Z(a19);
  if (t(p8)) return null;
  const h6 = r4[2] - n7[2];
  if (u3 === f3.Vertical) return { verticalSigned: h6, unit: p8 };
  if (!jn(n7, i4, S3, a19) || !jn(r4, i4, k3, a19)) return null;
  if (u3 === f3.Direct) {
    return { direct: x(k3, S3), unit: p8 };
  }
  if (o3(M4, n7[0], n7[1], r4[2]), !jn(M4, i4, M4, a19)) return null;
  const m8 = x(M4, k3);
  if (u3 === f3.Horizontal) return { horizontal: m8, unit: p8 };
  return { direct: x(k3, S3), horizontal: m8, vertical: Math.abs(h6), unit: p8 };
}
!function(t4) {
  t4[t4.Direct = 0] = "Direct", t4[t4.Horizontal = 1] = "Horizontal", t4[t4.Vertical = 2] = "Vertical";
}(f3 || (f3 = {}));
var U3 = n();
var b4 = n();
var S3 = n();
var k3 = n();
var M4 = n();

// node_modules/@arcgis/core/views/support/geodesicMeasurementUtils.js
function r3(r4, t4, i4, s2, ...l16) {
  return o2(r4) && M2(r4) ? t4.apply(void 0, l16) : k(r4) ? i4.apply(void 0, l16) : s2.apply(void 0, l16);
}

// node_modules/@arcgis/core/views/support/geodesicLengthMeasurementUtils.js
function m6(e4) {
  const { spatialReference: t4 } = e4;
  return r3(t4, j5, d4, h4, e4);
}
function a17(e4, t4) {
  if (!E(e4.spatialReference, t4.spatialReference)) return null;
  const { spatialReference: r4 } = e4;
  return x7[0] = e4.x, x7[1] = e4.y, x7[2] = e4.hasZ ? e4.z : 0, z2[0] = t4.x, z2[1] = t4.y, z2[2] = t4.hasZ ? t4.z : 0, l14(x7, z2, r4);
}
function l14(e4, t4, r4) {
  return r3(r4, p7, g3, y6, e4, t4, r4);
}
function p7(t4, r4, n7) {
  return a4(b2(U4, t4, r4, n7).distance, "meters");
}
function g3(t4, n7, o6) {
  return a4(q(R3(t4, n7, o6), "meters"), "meters");
}
function y6(t4, r4, o6) {
  return Un(t4, o6, Z3) && Un(r4, o6, k4) ? a4(b2(U4, Z3, k4).distance, "meters") : null;
}
function j5(t4) {
  return a4(y3([t4], "meters")[0], "meters");
}
function d4(t4) {
  return a4(q(t4, "meters"), "meters");
}
function h4(t4) {
  const r4 = [];
  if (!hn(t4, r4)) return null;
  let n7 = 0;
  for (const e4 of r4) {
    let t5 = 0;
    for (let r5 = 1; r5 < e4.length; ++r5) t5 += b2(U4, e4[r5 - 1], e4[r5]).distance;
    n7 += t5;
  }
  return a4(n7, "meters");
}
function R3(e4, t4, r4) {
  return { type: "polyline", spatialReference: r4, paths: [[[...e4], [...t4]]] };
}
var U4 = new v2();
var x7 = n();
var z2 = n();
var Z3 = n();
var k4 = n();

// node_modules/@arcgis/core/views/support/automaticLengthMeasurementUtils.js
function m7(n7, e4) {
  return y8(p6, m6, h3, e4, n7);
}
function d5(n7, r4, t4, e4) {
  return y8(j4, l14, d3, e4, n7, r4, t4);
}
function a18(n7, r4, t4) {
  return y8(R2, a17, R2, t4, n7, r4);
}
function g4(n7, r4, t4, e4) {
  return y8(d3, l14, d3, e4, n7, r4, t4);
}
function l15(n7) {
  return y8(p6, m6, h3, "on-the-ground", n7);
}
function h5(n7, r4) {
  return y8(R2, a17, R2, "on-the-ground", n7, r4);
}
function y8(r4, t4, e4, o6, ...u3) {
  if ("on-the-ground" === o6) {
    const r5 = t4.apply(void 0, u3);
    return r(r5) ? r5 : e4.apply(void 0, u3);
  }
  return r4.apply(void 0, u3);
}

// node_modules/@arcgis/core/views/interactive/tooltip/SketchTooltipInfo.js
var t3 = class extends v {
  constructor(o6) {
    super(o6), this.helpMessage = void 0;
  }
};
e([y()], t3.prototype, "tooltipOptions", void 0), e([y()], t3.prototype, "helpMessage", void 0), t3 = e([a2("esri.views.interactive.tooltip.SketchTooltipInfo")], t3);

export {
  a4 as a,
  o4 as o,
  c2 as c,
  p3 as p,
  y4 as y,
  j2 as j,
  U,
  t3 as t,
  x4 as x,
  i,
  m4 as m,
  i3 as i2,
  r3 as r,
  v4 as v,
  x6 as x2,
  D3 as D,
  l14 as l,
  m7 as m2,
  d5 as d,
  a18 as a2,
  g4 as g,
  l15 as l2,
  h5 as h
};
//# sourceMappingURL=chunk-VCDD3IVD.js.map
