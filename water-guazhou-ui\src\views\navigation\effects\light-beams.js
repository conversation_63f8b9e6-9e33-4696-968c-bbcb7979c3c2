/**
 * 光线束特效方案
 * 实现了从下往上流动的光线束效果
 */

export function initLightBeamsEffect(canvasId) {
  let animationFrameId = null;
  let canvas, ctx;
  let beams = [];
  
  // 主题颜色 - 蓝色调
  const themeColors = [
    { r: 0, g: 170, b: 255 },  // 浅蓝色
    { r: 0, g: 120, b: 255 },  // 中蓝色
    { r: 0, g: 80, b: 220 },   // 深蓝色
    { r: 60, g: 200, b: 255 }  // 青蓝色
  ];

  const init = () => {
    canvas = document.getElementById(canvasId);
    if (!canvas) return;
    
    ctx = canvas.getContext('2d');
    
    // 设置canvas尺寸为窗口大小
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      initBeams();
    };
    
    // 初始化光线束
    const initBeams = () => {
      beams = [];
      const beamCount = 40; // 光线束数量
      
      for (let i = 0; i < beamCount; i++) {
        createBeam();
      }
    };
    
    // 创建单个光线束
    const createBeam = () => {
      const width = Math.random() * 10 + 2; // 宽度2-12像素
      const color = themeColors[Math.floor(Math.random() * themeColors.length)];
      const opacity = Math.random() * 0.4 + 0.2; // 透明度0.2-0.6
      const depth = Math.random() * 0.8 + 0.2; // 深度因子，用于控制速度和大小
      
      beams.push({
        x: Math.random() * canvas.width, // 随机x位置
        y: canvas.height + Math.random() * 200, // 从屏幕下方开始
        height: Math.random() * 150 + 50, // 光束高度50-200像素
        width: width * depth, // 宽度与深度相关
        speed: (Math.random() * 3 + 2) * depth, // 速度2-5，与深度相关
        color: color,
        opacity: opacity * depth, // 透明度与深度相关
        glow: Math.random() * 10 + 5 // 发光强度
      });
    };
    
    // 绘制光线束
    const drawBeams = () => {
      // 清空画布，使用黑色背景
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // 添加光晕效果
      const gradient = ctx.createRadialGradient(
        canvas.width / 2, canvas.height * 0.7, 0,
        canvas.width / 2, canvas.height * 0.7, canvas.width * 0.6
      );
      gradient.addColorStop(0, 'rgba(0, 80, 150, 0.05)');
      gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // 绘制所有光线束
      beams.forEach((beam, index) => {
        // 创建光线束的渐变色
        const beamGradient = ctx.createLinearGradient(
          beam.x, beam.y, 
          beam.x, beam.y - beam.height
        );
        
        // 光线束底部半透明
        beamGradient.addColorStop(0, `rgba(${beam.color.r}, ${beam.color.g}, ${beam.color.b}, 0)`);
        // 光线束中间最亮
        beamGradient.addColorStop(0.4, `rgba(${beam.color.r}, ${beam.color.g}, ${beam.color.b}, ${beam.opacity})`);
        beamGradient.addColorStop(0.6, `rgba(${beam.color.r}, ${beam.color.g}, ${beam.color.b}, ${beam.opacity})`);
        // 光线束顶部渐变消失
        beamGradient.addColorStop(1, `rgba(${beam.color.r}, ${beam.color.g}, ${beam.color.b}, 0)`);
        
        // 设置发光效果
        ctx.shadowColor = `rgba(${beam.color.r}, ${beam.color.g}, ${beam.color.b}, 0.8)`;
        ctx.shadowBlur = beam.glow;
        
        // 绘制光线束
        ctx.fillStyle = beamGradient;
        ctx.beginPath();
        ctx.rect(beam.x - beam.width / 2, beam.y - beam.height, beam.width, beam.height);
        ctx.fill();
        
        // 在光线束顶部添加小光斑
        ctx.beginPath();
        ctx.arc(beam.x, beam.y - beam.height, beam.width * 0.8, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(${beam.color.r}, ${beam.color.g}, ${beam.color.b}, ${beam.opacity * 0.7})`;
        ctx.fill();
        
        // 更新光线束位置 - 向上移动
        beam.y -= beam.speed;
        
        // 如果光线束移出屏幕，重新创建
        if (beam.y < -beam.height) {
          // 随机决定是立即重新创建还是稍等一会
          if (Math.random() > 0.8) {
            // 直接重置这个光束的属性
            const width = Math.random() * 10 + 2;
            const color = themeColors[Math.floor(Math.random() * themeColors.length)];
            const opacity = Math.random() * 0.4 + 0.2;
            const depth = Math.random() * 0.8 + 0.2;
            
            beam.x = Math.random() * canvas.width;
            beam.y = canvas.height + Math.random() * 50;
            beam.height = Math.random() * 150 + 50;
            beam.width = width * depth;
            beam.speed = (Math.random() * 3 + 2) * depth;
            beam.color = color;
            beam.opacity = opacity * depth;
            beam.glow = Math.random() * 10 + 5;
          } else {
            // 移除这个光束，稍后会在下面的代码中添加新的
            beams.splice(index, 1);
          }
        }
      });
      
      // 随机添加新的光线束，保持总数稳定
      if (beams.length < 40 && Math.random() > 0.95) {
        createBeam();
      }
      
      // 绘制下一帧
      animationFrameId = requestAnimationFrame(drawBeams);
    };
    
    // 初始化
    resizeCanvas();
    drawBeams();
    
    // 窗口大小变化时重置canvas
    window.addEventListener('resize', resizeCanvas);
    
    // 返回清理函数
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      window.removeEventListener('resize', resizeCanvas);
    };
  };

  // 初始化并返回清理函数
  return init();
} 