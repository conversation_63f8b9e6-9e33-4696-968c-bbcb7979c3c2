zk.enabled=false
zk.url=localhost:2181
zk.zk_dir=/thingsboard

updates.enabled=false

audit_log.enabled=true
audit_log.by_tenant_partitioning=MONTHS
audit_log.default_query_period=30
audit_log.sink.type=none

cache.type=caffeine
#cache.type=redis

caffeine.specs.relations.timeToLiveInMinutes=1440
caffeine.specs.relations.maxSize=100000

caffeine.specs.deviceCredentials.timeToLiveInMinutes=1440
caffeine.specs.deviceCredentials.maxSize=100000

caffeine.specs.devices.timeToLiveInMinutes=1440
caffeine.specs.devices.maxSize=100000

caffeine.specs.assets.timeToLiveInMinutes=1440
caffeine.specs.assets.maxSize=100000

caffeine.specs.entityViews.timeToLiveInMinutes=1440
caffeine.specs.entityViews.maxSize=100000

redis.connection.host=localhost
redis.connection.port=6379
redis.connection.db=0
redis.connection.password=

security.user_login_case_sensitive=true

database.ts_max_intervals=700