{"version": 3, "sources": ["../../@arcgis/core/layers/support/SimpleBandStatistics.js", "../../@arcgis/core/layers/support/PixelBlock.js", "../../@arcgis/core/layers/support/rasterFunctions/pixelUtils.js", "../../@arcgis/core/layers/support/rasterFunctions/vectorFieldUtils.js", "../../@arcgis/core/views/2d/engine/flow/dataUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass l{constructor(l=null,a=null,t=null){this.minValue=l,this.maxValue=a,this.noDataValue=t}}export{l as SimpleBandStatistics};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import e from\"../../core/Error.js\";import{JSONSupport as s}from\"../../core/JSONSupport.js\";import{clone as i}from\"../../core/lang.js\";import l from\"../../core/Logger.js\";import{isSome as r,isNone as o}from\"../../core/maybe.js\";import{property as a}from\"../../core/accessorSupport/decorators/property.js\";import{cast as h}from\"../../core/accessorSupport/decorators/cast.js\";import{subclass as n}from\"../../core/accessorSupport/decorators/subclass.js\";import{SimpleBandStatistics as p}from\"./SimpleBandStatistics.js\";import{getPixelValueRange as c}from\"./rasterFormats/pixelRangeUtils.js\";var u;let g=u=class extends s{static createEmptyBand(t,e){return new(u.getPixelArrayConstructor(t))(e)}static getPixelArrayConstructor(t){let e;switch(t){case\"u1\":case\"u2\":case\"u4\":case\"u8\":e=Uint8Array;break;case\"u16\":e=Uint16Array;break;case\"u32\":e=Uint32Array;break;case\"s8\":e=Int8Array;break;case\"s16\":e=Int16Array;break;case\"s32\":e=Int32Array;break;case\"f32\":case\"c64\":case\"c128\":case\"unknown\":e=Float32Array;break;case\"f64\":e=Float64Array}return e}constructor(t){super(t),this.width=null,this.height=null,this.pixelType=\"f32\",this.validPixelCount=null,this.mask=null,this.maskIsAlpha=!1,this.premultiplyAlpha=!1,this.statistics=null,this.depthCount=1}castPixelType(t){if(!t)return\"f32\";let e=t.toLowerCase();return[\"u1\",\"u2\",\"u4\"].includes(e)?e=\"u8\":[\"unknown\",\"u8\",\"s8\",\"u16\",\"s16\",\"u32\",\"s32\",\"f32\",\"f64\"].includes(e)||(e=\"f32\"),e}getPlaneCount(){return this.pixels?.length}addData(t){if(!t.pixels||t.pixels.length!==this.width*this.height)throw new e(\"pixelblock:invalid-or-missing-pixels\",\"add data requires valid pixels array that has same length defined by pixel block width * height\");this.pixels||(this.pixels=[]),this.statistics||(this.statistics=[]),this.pixels.push(t.pixels),this.statistics.push(t.statistics??new p)}getAsRGBA(){const t=new ArrayBuffer(this.width*this.height*4);switch(this.pixelType){case\"s8\":case\"s16\":case\"u16\":case\"s32\":case\"u32\":case\"f32\":case\"f64\":this._fillFromNon8Bit(t);break;default:this._fillFrom8Bit(t)}return new Uint8ClampedArray(t)}getAsRGBAFloat(){const t=new Float32Array(this.width*this.height*4);return this._fillFrom32Bit(t),t}updateStatistics(){if(!this.pixels)return;this.statistics=this.pixels.map((t=>this._calculateBandStatistics(t,this.mask)));const t=this.mask;let e=0;if(r(t))for(let s=0;s<t.length;s++)t[s]&&e++;else e=this.width*this.height;this.validPixelCount=e}clamp(t){if(!t||\"f64\"===t||\"f32\"===t||!this.pixels)return;const[e,s]=c(t),i=this.pixels,l=this.width*this.height,r=i.length;let o,a,h;const n=[];for(let p=0;p<r;p++){h=u.createEmptyBand(t,l),o=i[p];for(let t=0;t<l;t++)a=o[t],h[t]=a>s?s:a<e?e:a;n.push(h)}this.pixels=n,this.pixelType=t}extractBands(t){const{pixels:e,statistics:s}=this;if(o(t)||0===t.length||!e||0===e.length)return this;const i=e.length,l=t.some((t=>t>=e.length)),r=i===t.length&&!t.some(((t,e)=>t!==e));return l||r?this:new u({pixelType:this.pixelType,width:this.width,height:this.height,mask:this.mask,validPixelCount:this.validPixelCount,maskIsAlpha:this.maskIsAlpha,pixels:t.map((t=>e[t])),statistics:s&&t.map((t=>s[t]))})}clone(){const t=new u({width:this.width,height:this.height,pixelType:this.pixelType,maskIsAlpha:this.maskIsAlpha,validPixelCount:this.validPixelCount});let e;r(this.mask)&&(this.mask instanceof Uint8Array?t.mask=new Uint8Array(this.mask):t.mask=this.mask.slice(0));const s=u.getPixelArrayConstructor(this.pixelType);if(this.pixels&&this.pixels.length>0){t.pixels=[];const i=!!this.pixels[0].slice;for(e=0;e<this.pixels.length;e++)t.pixels[e]=i?this.pixels[e].slice(0,this.pixels[e].length):new s(this.pixels[e])}if(this.statistics)for(t.statistics=[],e=0;e<this.statistics.length;e++)t.statistics[e]=i(this.statistics[e]);return t.premultiplyAlpha=this.premultiplyAlpha,t}_fillFrom8Bit(t){const{mask:e,maskIsAlpha:s,premultiplyAlpha:i,pixels:o}=this;if(!t||!o||!o.length)return void l.getLogger(this.declaredClass).error(\"getAsRGBA()\",\"Unable to convert to RGBA. The input pixel block is empty.\");let a,h,n,p;a=h=n=o[0],o.length>=3?(h=o[1],n=o[2]):2===o.length&&(h=o[1]);const c=new Uint32Array(t),u=this.width*this.height;if(a.length===u)if(r(e)&&e.length===u)if(s)for(p=0;p<u;p++){const t=e[p];if(t){const e=t/255;c[p]=i?t<<24|n[p]*e<<16|h[p]*e<<8|a[p]*e:t<<24|n[p]<<16|h[p]<<8|a[p]}}else for(p=0;p<u;p++)e[p]&&(c[p]=255<<24|n[p]<<16|h[p]<<8|a[p]);else for(p=0;p<u;p++)c[p]=255<<24|n[p]<<16|h[p]<<8|a[p];else l.getLogger(this.declaredClass).error(\"getAsRGBA()\",\"Unable to convert to RGBA. The pixelblock is invalid.\")}_fillFromNon8Bit(t){const{pixels:e,mask:s,statistics:i}=this;if(!t||!e||!e.length)return void l.getLogger(this.declaredClass).error(\"getAsRGBA()\",\"Unable to convert to RGBA. The input pixel block is empty.\");const o=this.pixelType;let a=1,h=0,n=1;if(i&&i.length>0){for(const t of i)if(null!=t.minValue&&(h=Math.min(h,t.minValue)),null!=t.maxValue&&null!=t.minValue){const e=t.maxValue-t.minValue;n=Math.max(n,e)}a=255/n}else{let t=255;\"s8\"===o?(h=-128,t=127):\"u16\"===o?t=65535:\"s16\"===o?(h=-32768,t=32767):\"u32\"===o?t=4294967295:\"s32\"===o?(h=-2147483648,t=2147483647):\"f32\"===o?(h=-34e38,t=34e38):\"f64\"===o&&(h=-Number.MAX_VALUE,t=Number.MAX_VALUE),a=255/(t-h)}const p=new Uint32Array(t),c=this.width*this.height;let u,g,m,d,f;if(u=g=m=e[0],u.length!==c)return l.getLogger(this.declaredClass).error(\"getAsRGBA()\",\"Unable to convert to RGBA. The pixelblock is invalid.\");if(e.length>=2)if(g=e[1],e.length>=3&&(m=e[2]),r(s)&&s.length===c)for(d=0;d<c;d++)s[d]&&(p[d]=255<<24|(m[d]-h)*a<<16|(g[d]-h)*a<<8|(u[d]-h)*a);else for(d=0;d<c;d++)p[d]=255<<24|(m[d]-h)*a<<16|(g[d]-h)*a<<8|(u[d]-h)*a;else if(r(s)&&s.length===c)for(d=0;d<c;d++)f=(u[d]-h)*a,s[d]&&(p[d]=255<<24|f<<16|f<<8|f);else for(d=0;d<c;d++)f=(u[d]-h)*a,p[d]=255<<24|f<<16|f<<8|f}_fillFrom32Bit(t){const{pixels:e,mask:s}=this;if(!t||!e||!e.length)return l.getLogger(this.declaredClass).error(\"getAsRGBAFloat()\",\"Unable to convert to RGBA. The input pixel block is empty.\");let i,o,a,h;i=o=a=e[0],e.length>=3?(o=e[1],a=e[2]):2===e.length&&(o=e[1]);const n=this.width*this.height;if(i.length!==n)return l.getLogger(this.declaredClass).error(\"getAsRGBAFloat()\",\"Unable to convert to RGBA. The pixelblock is invalid.\");let p=0;if(r(s)&&s.length===n)for(h=0;h<n;h++)t[p++]=i[h],t[p++]=o[h],t[p++]=a[h],t[p++]=1&s[h];else for(h=0;h<n;h++)t[p++]=i[h],t[p++]=o[h],t[p++]=a[h],t[p++]=1}_calculateBandStatistics(t,e){let s=1/0,i=-1/0;const l=t.length;let o,a=0;if(r(e))for(o=0;o<l;o++)e[o]&&(a=t[o],s=a<s?a:s,i=a>i?a:i);else for(o=0;o<l;o++)a=t[o],s=a<s?a:s,i=a>i?a:i;return new p(s,i)}};t([a({json:{write:!0}})],g.prototype,\"width\",void 0),t([a({json:{write:!0}})],g.prototype,\"height\",void 0),t([a({json:{write:!0}})],g.prototype,\"pixelType\",void 0),t([h(\"pixelType\")],g.prototype,\"castPixelType\",null),t([a({json:{write:!0}})],g.prototype,\"validPixelCount\",void 0),t([a({json:{write:!0}})],g.prototype,\"mask\",void 0),t([a({json:{write:!0}})],g.prototype,\"maskIsAlpha\",void 0),t([a({json:{write:!0}})],g.prototype,\"pixels\",void 0),t([a()],g.prototype,\"premultiplyAlpha\",void 0),t([a({json:{write:!0}})],g.prototype,\"statistics\",void 0),t([a({json:{write:!0}})],g.prototype,\"depthCount\",void 0),t([a({json:{write:!0}})],g.prototype,\"noDataValues\",void 0),t([a({json:{write:!0}})],g.prototype,\"bandMasks\",void 0),g=u=t([n(\"esri.layers.support.PixelBlock\")],g);const m=g;export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,isNone as e,unwrap as n}from\"../../../core/maybe.js\";import l from\"../PixelBlock.js\";import{getPixelValueRange as i}from\"../rasterFormats/pixelRangeUtils.js\";var o,s;!function(t){t[t.matchAny=0]=\"matchAny\",t[t.matchAll=1]=\"matchAll\"}(o||(o={})),function(t){t[t.bestMatch=0]=\"bestMatch\",t[t.fail=1]=\"fail\"}(s||(s={}));const r=6;function a(e){return t(e)&&\"esri.layers.support.PixelBlock\"===e.declaredClass&&e.pixels&&e.pixels.length>0}function h(t,e){if(!e?.length||!a(t))return t;const n=t.pixels.length;return e&&e.some((t=>t>=n))||1===n&&1===e.length&&0===e[0]?t:n!==e.length||e.some(((t,e)=>t!==e))?new l({pixelType:t.pixelType,width:t.width,height:t.height,mask:t.mask,validPixelCount:t.validPixelCount,maskIsAlpha:t.maskIsAlpha,pixels:e.map((e=>t.pixels[e])),statistics:t.statistics&&e.map((e=>t.statistics[e]))}):t}function f(e){if(!e?.length||e.some((t=>!a(t))))return null;if(1===e.length)return t(e[0])?e[0].clone():null;const n=e,{width:i,height:o,pixelType:s}=n[0];if(n.some((t=>t.width!==i||t.height!==o)))return null;const r=n.map((({mask:t})=>t)).filter((t=>null!=t));let h=null;r.length&&(h=new Uint8Array(i*o),h.set(r[0]),r.length>1&&w(r.slice(1),h));const f=[];n.forEach((({pixels:t})=>f.push(...t)));const c=n.map((({statistics:t})=>t)).filter((t=>t?.length)),u=[];return c.forEach((t=>u.push(...t))),new l({pixelType:s,width:i,height:o,mask:h,pixels:f,statistics:u.length?u:null})}function c(t){if(!t)return;const e=t.colormap;if(!e||0===e.length)return;const n=e.sort(((t,e)=>t[0]-e[0]));let l=0;n[0][0]<0&&(l=n[0][0]);const i=Math.max(256,n[n.length-1][0]-l+1),o=new Uint8Array(4*i),s=[];let r,a=0,h=0;const f=5===n[0].length;if(i>65536)return n.forEach((t=>{s[t[0]-l]=f?t.slice(1):t.slice(1).concat([255])})),{indexed2DColormap:s,offset:l,alphaSpecified:f};if(t.fillUnspecified)for(r=n[h],a=r[0]-l;a<i;a++)o[4*a]=r[1],o[4*a+1]=r[2],o[4*a+2]=r[3],o[4*a+3]=f?r[4]:255,a===r[0]-l&&(r=h===n.length-1?r:n[++h]);else for(a=0;a<n.length;a++)r=n[a],h=4*(r[0]-l),o[h]=r[1],o[h+1]=r[2],o[h+2]=r[3],o[h+3]=f?r[4]:255;return{indexedColormap:o,offset:l,alphaSpecified:f}}function u(e,n){if(!a(e))return e;if(!n||!n.indexedColormap&&!n.indexed2DColormap)return e;const l=e.clone(),i=l.pixels;let o=l.mask;const s=l.width*l.height;if(1!==i.length)return e;const{indexedColormap:r,indexed2DColormap:h,offset:f,alphaSpecified:c}=n;let u=0;const p=i[0],x=new Uint8Array(p.length),m=new Uint8Array(p.length),g=new Uint8Array(p.length);let d,y=0;if(r){const e=r.length-1;if(t(o))for(u=0;u<s;u++)o[u]&&(y=4*(p[u]-f),y<f||y>e?o[u]=0:(x[u]=r[y],m[u]=r[y+1],g[u]=r[y+2],o[u]=r[y+3]));else{for(o=new Uint8Array(s),u=0;u<s;u++)y=4*(p[u]-f),y<f||y>e?o[u]=0:(x[u]=r[y],m[u]=r[y+1],g[u]=r[y+2],o[u]=r[y+3]);l.mask=o}}else if(h)if(t(o))for(u=0;u<s;u++)o[u]&&(d=h[p[u]],x[u]=d[0],m[u]=d[1],g[u]=d[2],o[u]=d[3]);else{for(o=new Uint8Array(s),u=0;u<s;u++)d=h[p[u]],x[u]=d[0],m[u]=d[1],g[u]=d[2],o[u]=d[3];l.mask=o}return l.pixels=[x,m,g],l.statistics=null,l.pixelType=\"u8\",l.maskIsAlpha=c,l}function p(t,e){if(!a(t))return null;const{pixels:n,mask:i}=t,o=n.length;let s=e.lut;const{offset:r}=e;s&&1===s[0].length&&(s=n.map((()=>s)));const h=[],f=e.outputPixelType||\"u8\";for(let l=0;l<o;l++){const t=x(n[l],i,s[l],r||0,f);h.push(t)}const c=new l({width:t.width,height:t.height,pixels:h,mask:i,pixelType:f});return c.updateStatistics(),c}function x(t,e,n,i,o){const s=t.length,r=l.createEmptyBand(o,s);if(e)for(let l=0;l<s;l++)e[l]&&(r[l]=n[t[l]-i]);else for(let l=0;l<s;l++)r[l]=n[t[l]-i];return r}function m(t,e){if(!a(t))return null;const n=t.clone(),{pixels:l}=n,i=n.width*n.height,o=e.length,s=Math.floor(o/2),r=e[Math.floor(s)],h=l[0];let f,c,u,p,x,m,g=!1;const d=new Uint8Array(i),y=new Uint8Array(i),w=new Uint8Array(i);let k=n.mask;const M=4===e[0].mappedColor.length;for(k||(k=new Uint8Array(i),k.fill(M?255:1),n.mask=k),x=0;x<i;x++)if(k[x]){for(f=h[x],g=!1,m=s,c=r,u=0,p=o-1;p-u>1;){if(f===c.value){g=!0;break}f>c.value?u=m:p=m,m=Math.floor((u+p)/2),c=e[Math.floor(m)]}g||(f===e[u].value?(c=e[u],g=!0):f===e[p].value?(c=e[p],g=!0):f<e[u].value?(g=!1,c=null):f>e[u].value&&(f<e[p].value?(c=e[u],g=!0):p===o-1?(g=!1,c=null):(c=e[p],g=!0))),g?(d[x]=c.mappedColor[0],y[x]=c.mappedColor[1],w[x]=c.mappedColor[2],k[x]=c.mappedColor[3]):d[x]=y[x]=w[x]=k[x]=0}return n.pixels=[d,y,w],n.mask=k,n.pixelType=\"u8\",n.maskIsAlpha=M,n}function g(t,e){if(!a(t))return null;const{width:n,height:o}=t,{inputRanges:s,outputValues:r,outputPixelType:h,noDataRanges:f,allowUnmatched:c,isLastInputRangeInclusive:u}=e,p=t.pixels[0],x=l.createEmptyBand(h,p.length),m=t.mask,g=new Uint8Array(n*o);m?g.set(m):g.fill(255);const d=t.pixelType.startsWith(\"f\")?1e-6:0,y=s.map((t=>t-d));y[0]=s[0],y[y.length-1]=s[s.length-1]+(u?1e-6:0);const w=s.length/2,[k,M]=i(h);for(let l=0;l<o;l++)for(let t=0;t<n;t++){const e=l*n+t;if(g[e]){const t=p[e];let n=!1;for(let l=w-1;l>=0;l--)if(t===y[2*l]||t>y[2*l]&&t<y[2*l+1]){x[e]=r[l],n=!0;break}n||(c?x[e]=t>M?M:t<k?k:t:g[e]=0)}}if(f?.length)for(let l=0;l<o;l++)for(let t=0;t<n;t++){const e=l*n+t;if(!m||m[e]){const t=p[e];for(let n=0;n<w;n+=2)if(t>=f[n]&&t<=f[n+1]){x[e]=0,g[e]=0;break}}}return new l({width:n,height:o,pixelType:h,pixels:[x],mask:g})}function d(t,e,n,l){const i=null!=n&&n.length>=2?new Set(n):null,o=1===n?.length?n[0]:null,s=!!e?.length;for(let r=0;r<t.length;r++)if(l[r]){const n=t[r];if(s){let t=!1;for(let l=0;l<e.length;l+=2)if(n>=e[l]&&n<=e[l+1]){t=!0;break}t||(l[r]=0)}l[r]&&(n===o||i?.has(n))&&(l[r]=0)}}function y(t,e){const n=t[0].length;for(let l=0;l<n;l++)if(e[l]){let n=!1;for(let e=0;e<t.length;e++)if(t[e][l]){n=!0;break}n||(e[l]=0)}}function w(t,e){const n=t[0].length;for(let l=0;l<n;l++)if(e[l]){let n=!1;for(let e=0;e<t.length;e++)if(0===t[e][l]){n=!0;break}n&&(e[l]=0)}}function k(t,e){if(!a(t))return null;const{width:n,height:i,pixels:o}=t,s=n*i,r=new Uint8Array(s);t.mask?r.set(t.mask):r.fill(255);const h=o.length,{includedRanges:f,noDataValues:c,outputPixelType:u,matchAll:p,lookups:m}=e;if(m){const t=[];for(let e=0;e<h;e++){const n=m[e],l=x(o[e],r,n.lut,n.offset||0,\"u8\");t.push(l)}1===t.length?r.set(t[0]):p?y(t,r):w(t,r)}else if(p){const t=[];for(let e=0;e<h;e++){const n=new Uint8Array(s);n.set(r),d(o[e],f?.slice(2*e,2*e+2),c?.[e],n),t.push(n)}1===t.length?r.set(t[0]):y(t,r)}else for(let l=0;l<h;l++)d(o[l],f?.slice(2*l,2*l+2),c?.[l],r);return new l({width:n,height:i,pixelType:u,pixels:o,mask:r})}function M(t){const{srcPixelType:e,inputRanges:n,outputValues:o,allowUnmatched:s,noDataRanges:r,isLastInputRangeInclusive:a,outputPixelType:h}=t;if(\"u8\"!==e&&\"s8\"!==e&&\"u16\"!==e&&\"s16\"!==e)return null;const f=e.includes(\"16\")?65536:256,c=e.includes(\"s\")?-f/2:0,u=l.createEmptyBand(h,f),p=new Uint8Array(f);s&&p.fill(255);const[x,m]=i(h);if(n?.length&&o?.length){const t=1e-6,e=n.map((e=>e-t));e[0]=n[0],a&&(e[e.length-1]=n[n.length-1]);for(let n=0;n<e.length;n++){const t=o[n]>m?m:o[n]<x?x:o[n],l=Math.ceil(e[2*n]-c),i=Math.floor(e[2*n+1]-c);for(let e=l;e<=i;e++)u[e]=t,p[e]=255}}if(r?.length)for(let l=0;l<r.length;l++){const t=Math.ceil(r[2*l]-c),e=Math.floor(r[2*l+1]-c);for(let n=t;n<=e;n++)p[n]=0}return{lut:u,offset:c,mask:p}}function A(t,e,n){if(\"u8\"!==t&&\"s8\"!==t&&\"u16\"!==t&&\"s16\"!==t)return null;const l=t.includes(\"16\")?65536:256,i=t.includes(\"s\")?-l/2:0,o=new Uint8Array(l);if(e)for(let s=0;s<e.length;s++){const t=Math.ceil(e[2*s]-i),n=Math.floor(e[2*s+1]-i);for(let e=t;e<=n;e++)o[e]=255}else o.fill(255);if(n)for(let s=0;s<n.length;s++)o[n[s]-i]=0;return{lut:o,offset:i}}function U(t,e,n,l,i,o,s,r){return{xmin:i<=n*t?0:i<n*t+t?i-n*t:t,ymin:o<=l*e?0:o<l*e+e?o-l*e:e,xmax:i+s<=n*t?0:i+s<n*t+t?i+s-n*t:t,ymax:o+r<=l*e?0:o+r<l*e+e?o+r-l*e:e}}function T(t,n){if(!t||0===t.length)return null;const l=t.find((t=>t.pixelBlock));if(!l||e(l.pixelBlock))return null;const i=(l.extent.xmax-l.extent.xmin)/l.pixelBlock.width,o=(l.extent.ymax-l.extent.ymin)/l.pixelBlock.height,s=.01*Math.min(i,o),r=t.sort(((t,e)=>Math.abs(t.extent.ymax-e.extent.ymax)>s?e.extent.ymax-t.extent.ymax:Math.abs(t.extent.xmin-e.extent.xmin)>s?t.extent.xmin-e.extent.xmin:0)),a=Math.min.apply(null,r.map((t=>t.extent.xmin))),h=Math.min.apply(null,r.map((t=>t.extent.ymin))),f=Math.max.apply(null,r.map((t=>t.extent.xmax))),c=Math.max.apply(null,r.map((t=>t.extent.ymax))),u={x:Math.round((n.xmin-a)/i),y:Math.round((c-n.ymax)/o)},p={width:Math.round((f-a)/i),height:Math.round((c-h)/o)},x={width:Math.round((n.xmax-n.xmin)/i),height:Math.round((n.ymax-n.ymin)/o)};if(Math.round(p.width/l.pixelBlock.width)*Math.round(p.height/l.pixelBlock.height)!==r.length||u.x<0||u.y<0||p.width<x.width||p.height<x.height)return null;return{extent:n,pixelBlock:b(r.map((t=>t.pixelBlock)),p,{clipOffset:u,clipSize:x})}}function C(t,e,n,l,i,o){const{width:s,height:r}=n.block,{x:a,y:h}=n.offset,{width:f,height:c}=n.mosaic,u=U(s,r,l,i,a,h,f,c);let p=0,x=0;if(o){const t=o.hasGCSSShiftTransform?360:o.halfWorldWidth??0,e=s*o.resolutionX,n=o.startX+l*e;n<t&&n+e>t?x=o.rightPadding:n>=t&&(p=o.leftMargin-o.rightPadding,x=0)}if(u.xmax-=x,\"number\"!=typeof e)for(let m=u.ymin;m<u.ymax;m++){const n=(i*r+m-h)*f+(l*s-a)+p,o=m*s;for(let l=u.xmin;l<u.xmax;l++)t[n+l]=e[o+l]}else for(let m=u.ymin;m<u.ymax;m++){const n=(i*r+m-h)*f+(l*s-a)+p;for(let l=u.xmin;l<u.xmax;l++)t[n+l]=e}}function b(n,i,o={}){const{clipOffset:s,clipSize:r,alignmentInfo:h,blockWidths:f}=o;if(f)return P(n,i,{blockWidths:f});const c=n.find((t=>a(t)));if(e(c))return null;const u=r?r.width:i.width,p=r?r.height:i.height,x=c.width,m=c.height,g=i.width/x,d=i.height/m,y={offset:s||{x:0,y:0},mosaic:r||i,block:{width:x,height:m}},w=c.pixelType,k=l.getPixelArrayConstructor(w),M=c.pixels.length,A=[];let U,T;for(let t=0;t<M;t++){T=new k(u*p);for(let e=0;e<d;e++)for(let l=0;l<g;l++){const i=n[e*g+l];a(i)&&(U=i.pixels[t],C(T,U,y,l,e,h))}A.push(T)}let b;if(n.some((n=>e(n)||t(n.mask)&&n.mask.length>0))){b=new Uint8Array(u*p);for(let e=0;e<d;e++)for(let l=0;l<g;l++){const i=n[e*g+l],o=t(i)?i.mask:null;t(o)?C(b,o,y,l,e,h):C(b,i?1:0,y,l,e,h)}}const S=new l({width:u,height:p,pixels:A,pixelType:w,mask:b});return S.updateStatistics(),S}function P(i,o,s){const r=i.find((e=>t(e)));if(e(r))return null;const h=i.some((e=>!t(e)||!!e.mask)),{width:f,height:c}=o,u=h?new Uint8Array(f*c):null,{blockWidths:p}=s,x=[],m=r.getPlaneCount(),g=l.getPixelArrayConstructor(r.pixelType);if(h)for(let t=0,e=0;t<i.length;e+=p[t],t++){const l=i[t];if(!a(l))continue;const o=n(l.mask);for(let n=0;n<c;n++)for(let i=0;i<p[t];i++)u[n*f+i+e]=null==o?255:o[n*l.width+i]}for(let t=0;t<m;t++){const e=new g(f*c);for(let n=0,l=0;n<i.length;l+=p[n],n++){const o=i[n];if(!a(o))continue;const s=o.pixels[t];if(null!=s)for(let t=0;t<c;t++)for(let i=0;i<p[n];i++)e[t*f+i+l]=s[t*o.width+i]}x.push(e)}const d=new l({width:f,height:c,mask:u,pixels:x,pixelType:r.pixelType});return d.updateStatistics(),d}function S(t,e,n){if(!a(t))return null;const{width:l,height:i}=t,o=e.x,s=e.y,r=n.width+o,h=n.height+s;if(o<0||s<0||r>l||h>i)return t;if(0===o&&0===s&&r===l&&h===i)return t;t.mask||(t.mask=new Uint8Array(l*i));const f=t.mask;for(let a=0;a<i;a++){const t=a*l;for(let e=0;e<l;e++)f[t+e]=a<s||a>=h||e<o||e>=r?0:1}return t.updateStatistics(),t}function B(t){if(!a(t))return null;const e=t.clone(),{width:l,height:i,pixels:o}=t,s=o[0],r=e.pixels[0],h=n(t.mask);for(let n=2;n<i-1;n++){const t=new Map;for(let i=n-2;i<n+2;i++)for(let e=0;e<4;e++){const n=i*l+e;_(t,s[n],h?h[n]:1)}r[n*l]=v(t),r[n*l+1]=r[n*l+2]=r[n*l];let e=3;for(;e<l-1;e++){let i=(n-2)*l+e+1;_(t,s[i],h?h[i]:1),i=(n-1)*l+e+1,_(t,s[i],h?h[i]:1),i=n*l+e+1,_(t,s[i],h?h[i]:1),i=(n+1)*l+e+1,_(t,s[i],h?h[i]:1),i=(n-2)*l+e-3,I(t,s[i],h?h[i]:1),i=(n-1)*l+e-3,I(t,s[i],h?h[i]:1),i=n*l+e-3,I(t,s[i],h?h[i]:1),i=(n+1)*l+e-3,I(t,s[i],h?h[i]:1),r[n*l+e]=v(t)}r[n*l+e+1]=r[n*l+e]}for(let n=0;n<l;n++)r[n]=r[l+n]=r[2*l+n],r[(i-1)*l+n]=r[(i-2)*l+n];return e.updateStatistics(),e}function v(t){if(0===t.size)return 0;let e=0,n=-1,l=0;const i=t.keys();let o=i.next();for(;!o.done;)l=t.get(o.value),l>e&&(n=o.value,e=l),o=i.next();return n}function I(t,e,n){if(0===n)return;const l=t.get(e);1===l?t.delete(e):t.set(e,l-1)}function _(t,e,n){0!==n&&t.set(e,t.has(e)?t.get(e)+1:1)}function W(t,e,i){let{x:o,y:s}=e;const{width:r,height:h}=i;if(0===o&&0===s&&h===t.height&&r===t.width)return t;const{width:f,height:c}=t,u=Math.max(0,s),p=Math.max(0,o),x=Math.min(o+r,f),m=Math.min(s+h,c);if(x<0||m<0||!a(t))return null;o=Math.max(0,-o),s=Math.max(0,-s);const{pixels:g}=t,d=r*h,y=g.length,w=[];for(let n=0;n<y;n++){const e=g[n],i=l.createEmptyBand(t.pixelType,d);for(let t=u;t<m;t++){const n=t*f;let l=(t+s-u)*r+o;for(let t=p;t<x;t++)i[l++]=e[n+t]}w.push(i)}const k=new Uint8Array(d),M=n(t.mask);for(let n=u;n<m;n++){const t=n*f;let e=(n+s-u)*r+o;for(let n=p;n<x;n++)k[e++]=M?M[t+n]:1}const A=new l({width:i.width,height:i.height,pixelType:t.pixelType,pixels:w,mask:k});return A.updateStatistics(),A}function E(e,n=!0){if(!a(e))return null;const{pixels:i,width:o,height:s,mask:r,pixelType:h}=e,f=[],c=Math.round(o/2),u=Math.round(s/2),p=s-1,x=o-1;for(let t=0;t<i.length;t++){const e=i[t],r=l.createEmptyBand(h,c*u);let a=0;for(let t=0;t<s;t+=2)for(let l=0;l<o;l+=2){const i=e[t*o+l];if(n){const n=l===x?i:e[t*o+l+1],s=t===p?i:e[t*o+l+o],h=l===x?s:t===p?n:e[t*o+l+o+1];r[a++]=(i+n+s+h)/4}else r[a++]=i}f.push(r)}let m=null;if(t(r)){m=new Uint8Array(c*u);let t=0;for(let e=0;e<s;e+=2)for(let l=0;l<o;l+=2){const i=r[e*o+l];if(n){const n=l===x?i:r[e*o+l+1],s=e===p?i:r[e*o+l+o],a=l===x?s:e===p?n:r[e*o+l+o+1];m[t++]=i*n*s*a?1:0}else m[t++]=i}}return new l({width:c,height:u,pixelType:h,pixels:f,mask:m})}function R(t,e,n){if(!a(t))return null;const{width:l,height:i}=e;let{width:o,height:s}=t;const r=new Map,h={x:0,y:0},f=null==n?1:1+n;let c=t;for(let a=0;a<f;a++){const t=Math.ceil(o/l),n=Math.ceil(s/i);for(let o=0;o<n;o++){h.y=o*i;for(let n=0;n<t;n++){h.x=n*l;const t=W(c,h,e);r.set(`${a}/${o}/${n}`,t)}}a<f-1&&(c=E(c)),o=Math.round(o/2),s=Math.round(s/2)}return r}function j(t,e,n,l,i=0){const{width:o,height:s}=t,{width:r,height:a}=e,h=l.cols,f=l.rows,c=Math.ceil(r/h-.1/h),u=Math.ceil(a/f-.1/f);let p,x,m,g,d,y,w;const k=c*h,M=k*u*f,A=new Float32Array(M),U=new Float32Array(M),T=new Uint32Array(M),C=new Uint32Array(M);let b,P,S=0;for(let B=0;B<u;B++)for(let t=0;t<c;t++){p=12*(B*c+t),x=n[p],m=n[p+1],g=n[p+2],d=n[p+3],y=n[p+4],w=n[p+5];for(let e=0;e<f;e++){S=(B*f+e)*k+t*h,P=(e+.5)/f;for(let t=0;t<e;t++)b=(t+.5)/h,A[S+t]=(x*b+m*P+g)*o+i,U[S+t]=(d*b+y*P+w)*s+i,T[S+t]=Math.floor(A[S+t]),C[S+t]=Math.floor(U[S+t])}p+=6,x=n[p],m=n[p+1],g=n[p+2],d=n[p+3],y=n[p+4],w=n[p+5];for(let e=0;e<f;e++){S=(B*f+e)*k+t*h,P=(e+.5)/f;for(let t=e;t<h;t++)b=(t+.5)/h,A[S+t]=(x*b+m*P+g)*o+i,U[S+t]=(d*b+y*P+w)*s+i,T[S+t]=Math.floor(A[S+t]),C[S+t]=Math.floor(U[S+t])}}return{offsets_x:A,offsets_y:U,offsets_xi:T,offsets_yi:C,gridWidth:k}}function D(t,e){const{coefficients:n,spacing:l}=e,{offsets_x:i,offsets_y:o,gridWidth:s}=j(t,t,n,{rows:l[0],cols:l[1]}),{width:r,height:a}=t,h=new Float32Array(r*a),f=180/Math.PI;for(let c=0;c<a;c++)for(let t=0;t<r;t++){const e=c*s+t,n=0===c?e:e-s,l=c===a-1?e:e+s,u=i[n]-i[l],p=o[l]-o[n];if(isNaN(u)||isNaN(p))h[c*r+t]=90;else{let e=Math.atan2(p,u)*f;e=(360+e)%360,h[c*r+t]=e}}return h}function F(e,n,i,o,s=\"nearest\"){if(!a(e))return null;\"majority\"===s&&(e=B(e));const{pixels:r,mask:h,pixelType:f}=e,c=e.width,u=e.height,p=l.getPixelArrayConstructor(f),x=r.length,{width:m,height:g}=n;let d=!1;for(let t=0;t<i.length;t+=3)-1===i[t]&&-1===i[t+1]&&-1===i[t+2]&&(d=!0);const{offsets_x:y,offsets_y:w,offsets_xi:k,offsets_yi:M,gridWidth:A}=j({width:c,height:u},n,i,o,\"majority\"===s?.5:0);let U;const T=(t,e,n)=>{const l=t instanceof Float32Array||t instanceof Float64Array?0:.5;for(let i=0;i<g;i++){U=i*A;for(let o=0;o<m;o++){if(y[U]<0||w[U]<0)t[i*m+o]=0;else if(n)t[i*m+o]=e[k[U]+M[U]*c];else{const n=Math.floor(y[U]),s=Math.floor(w[U]),r=Math.ceil(y[U]),a=Math.ceil(w[U]),f=y[U]-n,u=w[U]-s;if(!h||h[n+s*c]&&h[n+s*c]&&h[n+a*c]&&h[r+a*c]){const h=(1-f)*e[n+s*c]+f*e[r+s*c],p=(1-f)*e[n+a*c]+f*e[r+a*c];t[i*m+o]=(1-u)*h+u*p+l}else t[i*m+o]=e[k[U]+M[U]*c]}U++}}},C=[];let b;for(let t=0;t<x;t++)b=new p(m*g),T(b,r[t],\"nearest\"===s||\"majority\"===s),C.push(b);const P=new l({width:m,height:g,pixelType:f,pixels:C});if(t(h))P.mask=new Uint8Array(m*g),T(P.mask,h,!0);else if(d){P.mask=new Uint8Array(m*g);for(let t=0;t<m*g;t++)P.mask[t]=y[t]<0||w[t]<0?0:1}return P.updateStatistics(),P}export{r as MAX_MAP_SIZE_GPU,s as MissingBandAction,o as NoDataInterpretation,F as approximateTransform,W as clip,u as colorize,f as compositeBands,c as createColormapLUT,A as createMaskLUT,M as createRemapLUT,h as extractBands,U as getClipBounds,D as getLocalArithmeticNorthRotations,j as interpolateOffsets,a as isValidPixelBlock,x as lookupBandValues,p as lookupPixels,k as mask,b as mosaic,T as mosaicPixelData,g as remap,m as remapColor,B as resampleByMajority,S as setValidBoundary,R as split};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{JSONMap as t}from\"../../../core/jsonMap.js\";import{isNone as e,unwrap as n,isSome as r}from\"../../../core/maybe.js\";import o from\"../PixelBlock.js\";import{isValidPixelBlock as i}from\"./pixelUtils.js\";const s=new Map;s.set(\"meter-per-second\",1),s.set(\"kilometer-per-hour\",.277778),s.set(\"knots\",.514444),s.set(\"feet-per-second\",.3048),s.set(\"mile-per-hour\",.44704);const a=180/Math.PI,h=5,c=new t({esriMetersPerSecond:\"meter-per-second\",esriKilometersPerHour:\"kilometer-per-hour\",esriKnots:\"knots\",esriFeetPerSecond:\"feet-per-second\",esriMilesPerHour:\"mile-per-hour\"});function l(t,e){return s.get(t)/s.get(e)||1}function u(t){return(450-t)%360}function f(t,e=\"geographic\"){const[n,r]=t,o=Math.sqrt(n*n+r*r);let i=Math.atan2(r,n)*a;return i=(360+i)%360,\"geographic\"===e&&(i=u(i)),[o,i]}function p(t,e=\"geographic\"){let n=t[1];\"geographic\"===e&&(n=u(n)),n%=360;const r=t[0];return[r*Math.cos(n/a),r*Math.sin(n/a)]}function m(t,r,o,s=\"geographic\"){if(!i(t)||e(o))return t;const a=\"vector-magdir\"===r?t.clone():n(d(t,r)),h=a.pixels[1];for(let e=0;e<h.length;e++)h[e]=\"geographic\"===s?(h[e]+o[e]+270)%360:(h[e]+360-o[e])%360;return\"vector-magdir\"===r?a:d(a,\"vector-magdir\")}function d(t,e,n=\"geographic\",r=1){if(!i(t))return t;const{pixels:s,width:a,height:h}=t,c=a*h,l=s[0],u=s[1],m=t.pixelType.startsWith(\"f\")?t.pixelType:\"f32\",d=o.createEmptyBand(m,c),g=o.createEmptyBand(m,c);let x=0;for(let o=0;o<h;o++)for(let t=0;t<a;t++)\"vector-uv\"===e?([d[x],g[x]]=f([l[x],u[x]],n),d[x]*=r):([d[x],g[x]]=p([l[x],u[x]],n),d[x]*=r,g[x]*=r),x++;const M=new o({pixelType:m,width:t.width,height:t.height,mask:t.mask,validPixelCount:t.validPixelCount,maskIsAlpha:t.maskIsAlpha,pixels:[d,g]});return M.updateStatistics(),M}function g(t,e,n=1){if(1===n||!i(t))return t;const r=t.clone(),{pixels:o,width:s,height:a}=r,h=o[0],c=o[1];let l=0;for(let i=0;i<a;i++)for(let t=0;t<s;t++)\"vector-uv\"===e?(h[l]*=n,c[l]*=n):h[l]*=n,l++;return r.updateStatistics(),r}function x(t,n,r,o,i){if(e(i)||!i.spatialReference.equals(t.spatialReference))return{extent:t,width:Math.round(n/o),height:Math.round(r/o),resolution:t.width/n};const s=i.xmin,a=i.ymax,h=(t.xmax-t.xmin)/n*o,c=(t.ymax-t.ymin)/r*o,l=(h+c)/2;return t.xmin=s+Math.floor((t.xmin-s)/h)*h,t.xmax=s+Math.ceil((t.xmax-s)/h)*h,t.ymin=a+Math.floor((t.ymin-a)/c)*c,t.ymax=a+Math.ceil((t.ymax-a)/c)*c,{extent:t,width:Math.round(t.width/h),height:Math.round(t.height/c),resolution:l}}const M=k(0,0,0);function k(t=0,e=0,n=Math.PI,r=!0){r&&(n=(2*Math.PI-n)%(2*Math.PI));const o=r?-1:1,i=13*o,s=-7*o,a=-2*o,h=-16*o,c=21.75,[l,u]=y(0,e+i,n,c),[f,p]=y(t-5.5,e+s,n,c),[m,d]=y(t+5.5,e+s,n,c),[g,x]=y(t-1.5,e+a,n,c),[M,k]=y(t+1.5,e+a,n,c),[w,P]=y(t-1.5,e+h,n,c),[b,v]=y(t+1.5,e+h,n,c);return[l,u,f,p,g,x,M,k,m,d,w,P,b,v]}function w(t=0,e=Math.PI,n=!0){n&&(e=(2*Math.PI-e)%(2*Math.PI));const r=10,o=n?-1:1,i=5*o,s=20*o,a=25*o,c=45,l=0,u=0,f=2,p=0,m=f*o,d=n?1:-1,g=r/2*d;let[x,M]=[l+g,u-s],[k,w]=[x+f*d,M],[P,b]=[k-p*d,w+m],[v,I]=[l-g,u-a],[A,_]=[v+p*d,I-m],U=Math.ceil(t/h),S=Math.floor(U/10);U-=8*S;const D=[],F=[];for(let h=0;h<U/2;h++,S--){S<=0&&U%2==1&&h===(U-1)/2&&(v=l,A=v+p*d,I=(I+M)/2,_=I-m);const[t,n]=y(v,I,e,c);if(S>0){const[r,o]=y(k,I,e,c),[i,s]=y(x,M,e,c);D.push(r),D.push(o),D.push(t),D.push(n),D.push(i),D.push(s)}else{const[r,o]=y(k,w,e,c),[i,s]=y(P,b,e,c),[a,h]=y(A,_,e,c);F.push(t),F.push(n),F.push(a),F.push(h),F.push(i),F.push(s),F.push(r),F.push(o)}I+=i,M+=i,w+=i,b+=i,_+=i}const[j,N]=y(l+g,u+s,e,c),J=(r/2+f)*d,[O,q]=y(l+J,u+s,e,c),[B,E]=y(l+g,u-a,e,c),[T,C]=y(l+J,u-a,e,c);return{pennants:D,barbs:F,shaft:[j,N,O,q,B,E,T,C]}}function y(t,e,n,r=1){const o=Math.sqrt(t*t+e*e)/r,i=(2*Math.PI+Math.atan2(e,t))%(2*Math.PI);return[o,(2*Math.PI+i-n)%(2*Math.PI)]}const P=[0,1,3,6,10,16,21,27,33,40,47,55,63],b=[0,.5,1,1.5,2],v=[0,.25,.5,1,1.5,2,2.5,3,3.5,4];function I(t,e,n,r){const o=l(r||\"knots\",n);let i;for(i=1;i<e.length;i++)if(i===e.length-1){if(t<e[i]*o)break}else if(t<=e[i]*o)break;return Math.min(i-1,e.length-2)}function A(t,e,n,r,o){let i=0;switch(e){case\"beaufort_kn\":i=I(t,P,\"knots\",n);break;case\"beaufort_km\":i=I(t,P,\"kilometer-per-hour\",n);break;case\"beaufort_ft\":i=I(t,P,\"feet-per-second\",n);break;case\"beaufort_m\":i=I(t,P,\"meter-per-second\",n);break;case\"classified_arrow\":i=I(t,o??[],r,n);break;case\"ocean_current_m\":i=I(t,b,\"meter-per-second\",n);break;case\"ocean_current_kn\":i=I(t,v,\"knots\",n)}return i}function _(t,e){const{style:n,inputUnit:o,outputUnit:i,breakValues:s}=e,a=c.fromJSON(o),h=c.fromJSON(i),l=7*6,u=15;let f=0,p=0;const{width:m,height:d,mask:g}=t,x=t.pixels[0],w=t.pixels[1],y=r(g)?g.filter((t=>t>0)).length:m*d,P=new Float32Array(y*l),b=new Uint32Array(u*y),v=e.invertDirection?k(0,0,0,!1):M;for(let r=0;r<d;r++)for(let t=0;t<m;t++){const e=r*m+t;if(!g||g[r*m+t]){const o=(w[e]+360)%360/180*Math.PI,i=A(x[e],n,a,h,s);for(let n=0;n<v.length;n+=2)P[f++]=(t+.5)/m,P[f++]=(r+.5)/d,P[f++]=v[n],P[f++]=v[n+1]+o,P[f++]=i,P[f++]=x[e];const c=7*(f/l-1);b[p++]=c,b[p++]=c+1,b[p++]=c+2,b[p++]=c+0,b[p++]=c+4,b[p++]=c+3,b[p++]=c+0,b[p++]=c+2,b[p++]=c+3,b[p++]=c+2,b[p++]=c+5,b[p++]=c+3,b[p++]=c+5,b[p++]=c+6,b[p++]=c+3}}return{vertexData:P,indexData:b}}const U=[];function S(t,e){if(0===U.length)for(let h=0;h<30;h++)U.push(w(5*h,0,!e.invertDirection));const n=l(c.fromJSON(e.inputUnit),\"knots\"),{width:r,height:o,mask:i}=t,s=t.pixels[0],a=t.pixels[1],u=6,f=[],p=[];let m=0,d=0;for(let c=0;c<o;c++)for(let t=0;t<r;t++){const e=c*r+t,l=s[e]*n;if((!i||i[c*r+t])&&l>=h){const n=(a[e]+360)%360/180*Math.PI,{pennants:i,barbs:s,shaft:h}=U[Math.min(Math.floor(l/5),29)];if(i.length+s.length===0)continue;let g=f.length/u;const x=(t+.5)/r,M=(c+.5)/o;for(let t=0;t<i.length;t+=2)f[m++]=x,f[m++]=M,f[m++]=i[t],f[m++]=i[t+1]+n,f[m++]=0,f[m++]=l;for(let t=0;t<s.length;t+=2)f[m++]=x,f[m++]=M,f[m++]=s[t],f[m++]=s[t+1]+n,f[m++]=0,f[m++]=l;for(let t=0;t<h.length;t+=2)f[m++]=x,f[m++]=M,f[m++]=h[t],f[m++]=h[t+1]+n,f[m++]=0,f[m++]=l;for(let t=0;t<i.length/6;t++)p[d++]=g,p[d++]=g+1,p[d++]=g+2,g+=3;for(let t=0;t<s.length/8;t++)p[d++]=g,p[d++]=g+1,p[d++]=g+2,p[d++]=g+1,p[d++]=g+2,p[d++]=g+3,g+=4;p[d++]=g+0,p[d++]=g+1,p[d++]=g+2,p[d++]=g+1,p[d++]=g+3,p[d++]=g+2,g+=4}}return{vertexData:new Float32Array(f),indexData:new Uint32Array(p)}}function D(t,e){const n=4*6;let r=0,o=0;const{width:i,height:s,mask:a}=t,u=t.pixels[0],f=[],p=[],m=l(c.fromJSON(e.inputUnit),\"knots\"),d=\"wind_speed\"===e.style?h:Number.MAX_VALUE;for(let h=0;h<s;h++)for(let t=0;t<i;t++){const e=u[h*i+t]*m;if((!a||a[h*i+t])&&e<d){for(let n=0;n<4;n++)f[r++]=(t+.5)/i,f[r++]=(h+.5)/s,f[r++]=n<2?-.5:.5,f[r++]=n%2==0?-.5:.5,f[r++]=0,f[r++]=e;const a=4*(r/n-1);p[o++]=a,p[o++]=a+1,p[o++]=a+2,p[o++]=a+1,p[o++]=a+2,p[o++]=a+3}}return{vertexData:new Float32Array(f),indexData:new Uint32Array(p)}}function F(t,e){return\"simple_scalar\"===e.style?D(t,e):\"wind_speed\"===e.style?S(t,e):_(t,e)}function j(t,e,n,r=[0,0],i=.5){const{width:s,height:a,mask:h}=t,[c,l]=t.pixels,[u,m]=r,d=Math.round((s-u)/n),g=Math.round((a-m)/n),x=d*g,M=new Float32Array(x),k=new Float32Array(x),w=new Uint8Array(x),y=\"vector-uv\"===e;for(let o=0;o<g;o++)for(let t=0;t<d;t++){let e=0;const r=o*d+t,g=Math.max(0,o*n+m),x=Math.max(0,t*n+u),P=Math.min(a,g+n),b=Math.min(s,x+n);for(let t=g;t<P;t++)for(let n=x;n<b;n++){const o=t*s+n;if(!h||h[o]){e++;const t=y?[c[o],l[o]]:[c[o],(360+l[o])%360],[n,i]=y?t:p(t);M[r]+=n,k[r]+=i}}if(e>=(P-g)*(b-x)*(1-i)){w[r]=1;const[t,n]=f([M[r]/e,k[r]/e]);M[r]=t,k[r]=n}else w[r]=0,M[r]=0,k[r]=0}const P=new o({width:d,height:g,pixels:[M,k],mask:w});return P.updateStatistics(),P}export{m as convertToLocalDirections,d as convertVectorFieldData,g as convertVectorFieldUnit,F as createVFMesh,D as createVFMeshScalar,l as getUnitConversionFactor,j as sampleVectorField,x as snapImageToSymbolTile,c as unitKebabDict,f as uvComponentToVector};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../geometry.js\";import has from\"../../../../core/has.js\";import t from\"../../../../core/Logger.js\";import{deg2rad as e}from\"../../../../core/mathUtils.js\";import{isNone as n,isSome as r}from\"../../../../core/maybe.js\";import{throwIfAborted as o}from\"../../../../core/promiseUtils.js\";import i from\"../../../../core/RandomLCG.js\";import{getInfo as l}from\"../../../../geometry/support/spatialReferenceUtils.js\";import a from\"../../../../geometry/Extent.js\";const s=t.getLogger(\"esri.views.2d.engine.flow.dataUtils\"),c=10;async function f(t,e,n,r){const i=performance.now(),l=u(e,n),a=performance.now(),f=m(e,l,n.width,n.height),h=performance.now(),d=w(f,!0),y=performance.now(),x=\"Streamlines\"===t?p(d,c):g(d),M=performance.now();return has(\"esri-2d-profiler\")&&(s.info(\"I.1\",\"_createFlowFieldFromData (ms)\",Math.round(a-i)),s.info(\"I.2\",\"_getStreamlines (ms)\",Math.round(h-a)),s.info(\"I.3\",\"createAnimatedLinesData (ms)\",Math.round(y-h)),s.info(\"I.4\",\"create{Streamlines|Particles}Mesh (ms)\",Math.round(M-y)),s.info(\"I.5\",\"createFlowMesh (ms)\",Math.round(M-i)),s.info(\"I.6\",\"Mesh size (bytes)\",x.vertexData.buffer.byteLength+x.indexData.buffer.byteLength)),await Promise.resolve(),o(r),x}function u(t,e){const n=d(e.data,e.width,e.height,t.smoothing);if(t.interpolate){return(t,r)=>{const o=Math.floor(t),i=Math.floor(r);if(o<0||o>=e.width)return[0,0];if(i<0||i>=e.height)return[0,0];const l=t-o,a=r-i,s=o,c=i,f=o<e.width-1?o+1:o,u=i<e.height-1?i+1:i,h=n[2*(c*e.width+s)],m=n[2*(c*e.width+f)],d=n[2*(u*e.width+s)],w=n[2*(u*e.width+f)],p=n[2*(c*e.width+s)+1],g=n[2*(c*e.width+f)+1];return[(h*(1-a)+d*a)*(1-l)+(m*(1-a)+w*a)*l,(p*(1-a)+n[2*(u*e.width+s)+1]*a)*(1-l)+(g*(1-a)+n[2*(u*e.width+f)+1]*a)*l]}}return(t,r)=>{const o=Math.round(t),i=Math.round(r);return o<0||o>=e.width||i<0||i>=e.height?[0,0]:[n[2*(i*e.width+o)+0],n[2*(i*e.width+o)+1]]}}function h(t,e,n,r,o,i,l,a,s){const c=[];let f=n,u=r,h=0,[m,d]=e(f,u);m*=t.velocityScale,d*=t.velocityScale;const w=Math.sqrt(m*m+d*d);let p,g;c.push({x:f,y:u,t:h,speed:w});for(let y=0;y<t.verticesPerLine;y++){let[n,r]=e(f,u);n*=t.velocityScale,r*=t.velocityScale;const m=Math.sqrt(n*n+r*r);if(m<t.minSpeedThreshold)return c;const d=n/m,w=r/m;f+=d*t.segmentLength,u+=w*t.segmentLength;if(h+=t.segmentLength/m,Math.acos(d*p+w*g)>t.maxTurnAngle)return c;if(t.collisions){const t=Math.round(f*s),e=Math.round(u*s);if(t<0||t>l-1||e<0||e>a-1)return c;const n=i[e*l+t];if(-1!==n&&n!==o)return c;i[e*l+t]=o}c.push({x:f,y:u,t:h,speed:m}),p=d,g=w}return c}function m(t,e,n,r){const o=[],l=new i,a=1/Math.max(t.lineCollisionWidth,1),s=Math.round(n*a),c=Math.round(r*a),f=new Int32Array(s*c);for(let i=0;i<f.length;i++)f[i]=-1;const u=[];for(let i=0;i<r;i+=t.lineSpacing)for(let e=0;e<n;e+=t.lineSpacing)u.push({x:e,y:i,sort:l.getFloat()});u.sort(((t,e)=>t.sort-e.sort));for(const{x:i,y:m}of u)if(l.getFloat()<t.density){const n=h(t,e,i,m,o.length,f,s,c,a);if(n.length<2)continue;o.push(n)}return o}function d(t,e,n,r){if(0===r)return t;const o=Math.round(3*r),i=new Array(2*o+1);let l=0;for(let c=-o;c<=o;c++){const t=Math.exp(-c*c/(r*r));i[c+o]=t,l+=t}for(let c=-o;c<=o;c++)i[c+o]/=l;const a=new Float32Array(t.length);for(let c=0;c<n;c++)for(let n=0;n<e;n++){let r=0,l=0;for(let a=-o;a<=o;a++){if(n+a<0||n+a>=e)continue;const s=i[a+o];r+=s*t[2*(c*e+(n+a))+0],l+=s*t[2*(c*e+(n+a))+1]}a[2*(c*e+n)+0]=r,a[2*(c*e+n)+1]=l}const s=new Float32Array(t.length);for(let c=0;c<e;c++)for(let t=0;t<n;t++){let r=0,l=0;for(let s=-o;s<=o;s++){if(t+s<0||t+s>=n)continue;const f=i[s+o];r+=f*a[2*((t+s)*e+c)+0],l+=f*a[2*((t+s)*e+c)+1]}s[2*(t*e+c)+0]=r,s[2*(t*e+c)+1]=l}return s}function w(t,e){const n=new i,r=t.reduce(((t,e)=>t+e.length),0),o=new Float32Array(4*r),l=new Array(t.length);let a=0,s=0;for(const i of t){const t=a;for(const e of i)o[4*a+0]=e.x,o[4*a+1]=e.y,o[4*a+2]=e.t,o[4*a+3]=e.speed,a++;l[s++]={startVertex:t,numberOfVertices:i.length,totalTime:i[i.length-1].t,timeSeed:e?n.getFloat():0}}return{lineVertices:o,lineDescriptors:l}}function p(t,e){const n=9,{lineVertices:r,lineDescriptors:o}=t;let i=0,l=0;for(const m of o){i+=2*m.numberOfVertices;l+=6*(m.numberOfVertices-1)}const a=new Float32Array(i*n),s=new Uint32Array(l);let c=0,f=0;function u(){s[f++]=c-2,s[f++]=c,s[f++]=c-1,s[f++]=c,s[f++]=c+1,s[f++]=c-1}function h(t,e,r,o,i,l,s,f){const u=c*n;let h=0;a[u+h++]=t,a[u+h++]=e,a[u+h++]=1,a[u+h++]=r,a[u+h++]=l,a[u+h++]=s,a[u+h++]=o/2,a[u+h++]=i/2,a[u+h++]=f,c++,a[u+h++]=t,a[u+h++]=e,a[u+h++]=-1,a[u+h++]=r,a[u+h++]=l,a[u+h++]=s,a[u+h++]=-o/2,a[u+h++]=-i/2,a[u+h++]=f,c++}for(const m of o){const{totalTime:t,timeSeed:n}=m;let o=null,i=null,l=null,a=null,s=null,c=null;for(let f=0;f<m.numberOfVertices;f++){const d=r[4*(m.startVertex+f)+0],w=r[4*(m.startVertex+f)+1],p=r[4*(m.startVertex+f)+2],g=r[4*(m.startVertex+f)+3];let y=null,x=null,M=null,A=null;if(f>0){y=d-o,x=w-i;const r=Math.sqrt(y*y+x*x);if(y/=r,x/=r,f>1){let t=y+s,n=x+c;const r=Math.sqrt(t*t+n*n);t/=r,n/=r;const o=Math.min(1/(t*y+n*x),e);t*=o,n*=o,M=-n,A=t}else M=-x,A=y;null!==M&&null!==A&&(h(o,i,l,M,A,t,n,g),u())}o=d,i=w,l=p,s=y,c=x,a=g}h(o,i,l,-c,s,t,n,a)}return{vertexData:a,indexData:s}}function g(t){const e=16,n=1,r=2,{lineVertices:o,lineDescriptors:i}=t;let l=0,a=0;for(const j of i){const t=j.numberOfVertices-1;l+=4*t*2,a+=6*t*2}const s=new Float32Array(l*e),c=new Uint32Array(a);let f,u,h,m,d,w,p,g,y,x,M,A,I,V,F=0,D=0;function b(){c[D++]=F-8,c[D++]=F-7,c[D++]=F-6,c[D++]=F-7,c[D++]=F-5,c[D++]=F-6,c[D++]=F-4,c[D++]=F-3,c[D++]=F-2,c[D++]=F-3,c[D++]=F-1,c[D++]=F-2}function v(t,o,i,l,a,c,f,u,h,m,d,w,p,g){const y=F*e;let x=0;for(const e of[n,r])for(const n of[1,2,3,4])s[y+x++]=t,s[y+x++]=o,s[y+x++]=i,s[y+x++]=l,s[y+x++]=f,s[y+x++]=u,s[y+x++]=h,s[y+x++]=m,s[y+x++]=e,s[y+x++]=n,s[y+x++]=p,s[y+x++]=g,s[y+x++]=a/2,s[y+x++]=c/2,s[y+x++]=d/2,s[y+x++]=w/2,F++}function S(t,e){let n=y+M,r=x+A;const o=Math.sqrt(n*n+r*r);n/=o,r/=o;const i=y*n+x*r;n/=i,r/=i;let l=M+I,a=A+V;const s=Math.sqrt(l*l+a*a);l/=s,a/=s;const c=M*l+A*a;l/=c,a/=c,v(f,u,h,m,-r,n,d,w,p,g,-a,l,t,e),b()}function k(t,e,n,r,o,i){if(y=M,x=A,M=I,A=V,null==y&&null==x&&(y=M,x=A),null!=d&&null!=w){I=t-d,V=e-w;const n=Math.sqrt(I*I+V*V);I/=n,V/=n}null!=y&&null!=x&&S(o,i),f=d,u=w,h=p,m=g,d=t,w=e,p=n,g=r}function L(t,e){y=M,x=A,M=I,A=V,null==y&&null==x&&(y=M,x=A),null!=y&&null!=x&&S(t,e)}for(const j of i){f=null,u=null,h=null,m=null,d=null,w=null,p=null,g=null,y=null,x=null,M=null,A=null,I=null,V=null;const{totalTime:t,timeSeed:e}=j;for(let n=0;n<j.numberOfVertices;n++){k(o[4*(j.startVertex+n)+0],o[4*(j.startVertex+n)+1],o[4*(j.startVertex+n)+2],o[4*(j.startVertex+n)+3],t,e)}L(t,e)}return{vertexData:s,indexData:c}}function y(t,n){const r=n.pixels,{width:o,height:i}=n,l=new Float32Array(o*i*2),a=n.mask||new Uint8Array(o*i*2);if(n.mask||a.fill(255),\"vector-uv\"===t)for(let e=0;e<o*i;e++)l[2*e+0]=r[0][e],l[2*e+1]=-r[1][e];else if(\"vector-magdir\"===t)for(let s=0;s<o*i;s++){const t=r[0][s],n=e(r[1][s]),o=Math.cos(n-Math.PI/2),i=Math.sin(n-Math.PI/2);l[2*s+0]=o*t,l[2*s+1]=i*t}return{data:l,mask:a,width:o,height:i}}async function x(t,e,n,r,o,i){const c=performance.now(),f=l(e.spatialReference);if(!f){const l=await M(t,e,n,r,o,i);return has(\"esri-2d-profiler\")&&s.info(\"I.7\",\"loadImagery, early exit (ms)\",Math.round(performance.now()-c)),has(\"esri-2d-profiler\")&&s.info(\"I.9\",\"Number of parts\",1),l}const[u,h]=f.valid,m=h-u,d=Math.ceil(e.width/m),w=e.width/d,p=Math.round(n/d);let g=e.xmin;const y=[],x=performance.now();for(let l=0;l<d;l++){const n=new a({xmin:g,xmax:g+w,ymin:e.ymin,ymax:e.ymax,spatialReference:e.spatialReference});y.push(M(t,n,p,r,o,i)),g+=w}const A=await Promise.all(y);has(\"esri-2d-profiler\")&&s.info(\"I.8\",\"All calls to _fetchPart (ms)\",Math.round(performance.now()-x)),has(\"esri-2d-profiler\")&&s.info(\"I.9\",\"Number of parts\",A.length);const I={data:new Float32Array(n*r*2),mask:new Uint8Array(n*r),width:n,height:r};let V=0;for(const l of A){for(let t=0;t<l.height;t++)for(let e=0;e<l.width;e++)V+e>=n||(I.data[2*(t*n+V+e)+0]=l.data[2*(t*l.width+e)+0],I.data[2*(t*n+V+e)+1]=l.data[2*(t*l.width+e)+1],I.mask[t*n+V+e]=l.mask[t*l.width+e]);V+=l.width}return has(\"esri-2d-profiler\")&&s.info(\"I.10\",\"loadImagery, general exit (ms)\",Math.round(performance.now()-c)),I}async function M(t,e,o,i,l,a){const s={requestProjectedLocalDirections:!0,signal:a};if(r(l)&&(s.timeExtent=l),\"imagery\"===t.type){await t.load({signal:a});const r=t.rasterInfo.dataType,l=await t.fetchImage(e,o,i,s);return!l||n(l.pixelData)||n(l.pixelData.pixelBlock)?{data:new Float32Array(o*i*2),mask:new Uint8Array(o*i),width:o,height:i}:y(r,l.pixelData.pixelBlock)}await t.load({signal:a});const c=t.rasterInfo.dataType,f=await t.fetchPixels(e,o,i,s);return!f||n(f.pixelBlock)?{data:new Float32Array(o*i*2),mask:new Uint8Array(o*i),width:o,height:i}:y(c,f.pixelBlock)}export{w as createAnimatedLinesData,f as createFlowMesh,g as createParticlesMesh,p as createStreamlinesMesh,x as loadImagery};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAMA,KAAN,MAAO;AAAA,EAAC,YAAYA,KAAE,MAAKC,KAAE,MAAKC,KAAE,MAAK;AAAC,SAAK,WAASF,IAAE,KAAK,WAASC,IAAE,KAAK,cAAYC;AAAA,EAAC;AAAC;;;ACA4hB,IAAI;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,OAAO,gBAAgBC,IAAEC,IAAE;AAAC,WAAO,KAAI,EAAE,yBAAyBD,EAAC,GAAGC,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,yBAAyBD,IAAE;AAAC,QAAIC;AAAE,YAAOD,IAAE;AAAA,MAAC,KAAI;AAAA,MAAK,KAAI;AAAA,MAAK,KAAI;AAAA,MAAK,KAAI;AAAK,QAAAC,KAAE;AAAW;AAAA,MAAM,KAAI;AAAM,QAAAA,KAAE;AAAY;AAAA,MAAM,KAAI;AAAM,QAAAA,KAAE;AAAY;AAAA,MAAM,KAAI;AAAK,QAAAA,KAAE;AAAU;AAAA,MAAM,KAAI;AAAM,QAAAA,KAAE;AAAW;AAAA,MAAM,KAAI;AAAM,QAAAA,KAAE;AAAW;AAAA,MAAM,KAAI;AAAA,MAAM,KAAI;AAAA,MAAM,KAAI;AAAA,MAAO,KAAI;AAAU,QAAAA,KAAE;AAAa;AAAA,MAAM,KAAI;AAAM,QAAAA,KAAE;AAAA,IAAY;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,QAAM,MAAK,KAAK,SAAO,MAAK,KAAK,YAAU,OAAM,KAAK,kBAAgB,MAAK,KAAK,OAAK,MAAK,KAAK,cAAY,OAAG,KAAK,mBAAiB,OAAG,KAAK,aAAW,MAAK,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,QAAG,CAACA,GAAE,QAAM;AAAM,QAAIC,KAAED,GAAE,YAAY;AAAE,WAAM,CAAC,MAAK,MAAK,IAAI,EAAE,SAASC,EAAC,IAAEA,KAAE,OAAK,CAAC,WAAU,MAAK,MAAK,OAAM,OAAM,OAAM,OAAM,OAAM,KAAK,EAAE,SAASA,EAAC,MAAIA,KAAE,QAAOA;AAAA,EAAC;AAAA,EAAC,gBAAe;AAJ/8C;AAIg9C,YAAO,UAAK,WAAL,mBAAa;AAAA,EAAM;AAAA,EAAC,QAAQD,IAAE;AAAC,QAAG,CAACA,GAAE,UAAQA,GAAE,OAAO,WAAS,KAAK,QAAM,KAAK,OAAO,OAAM,IAAIE,GAAE,wCAAuC,iGAAiG;AAAE,SAAK,WAAS,KAAK,SAAO,CAAC,IAAG,KAAK,eAAa,KAAK,aAAW,CAAC,IAAG,KAAK,OAAO,KAAKF,GAAE,MAAM,GAAE,KAAK,WAAW,KAAKA,GAAE,cAAY,IAAIG,IAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,UAAMH,KAAE,IAAI,YAAY,KAAK,QAAM,KAAK,SAAO,CAAC;AAAE,YAAO,KAAK,WAAU;AAAA,MAAC,KAAI;AAAA,MAAK,KAAI;AAAA,MAAM,KAAI;AAAA,MAAM,KAAI;AAAA,MAAM,KAAI;AAAA,MAAM,KAAI;AAAA,MAAM,KAAI;AAAM,aAAK,iBAAiBA,EAAC;AAAE;AAAA,MAAM;AAAQ,aAAK,cAAcA,EAAC;AAAA,IAAC;AAAC,WAAO,IAAI,kBAAkBA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,UAAMA,KAAE,IAAI,aAAa,KAAK,QAAM,KAAK,SAAO,CAAC;AAAE,WAAO,KAAK,eAAeA,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,QAAG,CAAC,KAAK,OAAO;AAAO,SAAK,aAAW,KAAK,OAAO,IAAK,CAAAA,OAAG,KAAK,yBAAyBA,IAAE,KAAK,IAAI,CAAE;AAAE,UAAMA,KAAE,KAAK;AAAK,QAAIC,KAAE;AAAE,QAAG,EAAED,EAAC,EAAE,UAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAF,GAAEE,EAAC,KAAGD;AAAA,QAAS,CAAAA,KAAE,KAAK,QAAM,KAAK;AAAO,SAAK,kBAAgBA;AAAA,EAAC;AAAA,EAAC,MAAMD,IAAE;AAAC,QAAG,CAACA,MAAG,UAAQA,MAAG,UAAQA,MAAG,CAAC,KAAK,OAAO;AAAO,UAAK,CAACC,IAAEC,EAAC,IAAEA,GAAEF,EAAC,GAAE,IAAE,KAAK,QAAOG,KAAE,KAAK,QAAM,KAAK,QAAOC,KAAE,EAAE;AAAO,QAAIC,IAAEC,IAAEC;AAAE,UAAM,IAAE,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAEJ,IAAEI,MAAI;AAAC,MAAAD,KAAE,EAAE,gBAAgBP,IAAEG,EAAC,GAAEE,KAAE,EAAEG,EAAC;AAAE,eAAQR,KAAE,GAAEA,KAAEG,IAAEH,KAAI,CAAAM,KAAED,GAAEL,EAAC,GAAEO,GAAEP,EAAC,IAAEM,KAAEJ,KAAEA,KAAEI,KAAEL,KAAEA,KAAEK;AAAE,QAAE,KAAKC,EAAC;AAAA,IAAC;AAAC,SAAK,SAAO,GAAE,KAAK,YAAUP;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAK,EAAC,QAAOC,IAAE,YAAWC,GAAC,IAAE;AAAK,QAAG,EAAEF,EAAC,KAAG,MAAIA,GAAE,UAAQ,CAACC,MAAG,MAAIA,GAAE,OAAO,QAAO;AAAK,UAAM,IAAEA,GAAE,QAAOE,KAAEH,GAAE,KAAM,CAAAA,OAAGA,MAAGC,GAAE,MAAO,GAAEG,KAAE,MAAIJ,GAAE,UAAQ,CAACA,GAAE,KAAM,CAACA,IAAEC,OAAID,OAAIC,EAAE;AAAE,WAAOE,MAAGC,KAAE,OAAK,IAAI,EAAE,EAAC,WAAU,KAAK,WAAU,OAAM,KAAK,OAAM,QAAO,KAAK,QAAO,MAAK,KAAK,MAAK,iBAAgB,KAAK,iBAAgB,aAAY,KAAK,aAAY,QAAOJ,GAAE,IAAK,CAAAA,OAAGC,GAAED,EAAC,CAAE,GAAE,YAAWE,MAAGF,GAAE,IAAK,CAAAA,OAAGE,GAAEF,EAAC,CAAE,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,IAAI,EAAE,EAAC,OAAM,KAAK,OAAM,QAAO,KAAK,QAAO,WAAU,KAAK,WAAU,aAAY,KAAK,aAAY,iBAAgB,KAAK,gBAAe,CAAC;AAAE,QAAIC;AAAE,MAAE,KAAK,IAAI,MAAI,KAAK,gBAAgB,aAAWD,GAAE,OAAK,IAAI,WAAW,KAAK,IAAI,IAAEA,GAAE,OAAK,KAAK,KAAK,MAAM,CAAC;AAAG,UAAME,KAAE,EAAE,yBAAyB,KAAK,SAAS;AAAE,QAAG,KAAK,UAAQ,KAAK,OAAO,SAAO,GAAE;AAAC,MAAAF,GAAE,SAAO,CAAC;AAAE,YAAM,IAAE,CAAC,CAAC,KAAK,OAAO,CAAC,EAAE;AAAM,WAAIC,KAAE,GAAEA,KAAE,KAAK,OAAO,QAAOA,KAAI,CAAAD,GAAE,OAAOC,EAAC,IAAE,IAAE,KAAK,OAAOA,EAAC,EAAE,MAAM,GAAE,KAAK,OAAOA,EAAC,EAAE,MAAM,IAAE,IAAIC,GAAE,KAAK,OAAOD,EAAC,CAAC;AAAA,IAAC;AAAC,QAAG,KAAK,WAAW,MAAID,GAAE,aAAW,CAAC,GAAEC,KAAE,GAAEA,KAAE,KAAK,WAAW,QAAOA,KAAI,CAAAD,GAAE,WAAWC,EAAC,IAAE,EAAE,KAAK,WAAWA,EAAC,CAAC;AAAE,WAAOD,GAAE,mBAAiB,KAAK,kBAAiBA;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,UAAK,EAAC,MAAKC,IAAE,aAAYC,IAAE,kBAAiB,GAAE,QAAOG,GAAC,IAAE;AAAK,QAAG,CAACL,MAAG,CAACK,MAAG,CAACA,GAAE,OAAO,QAAO,KAAK,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,eAAc,4DAA4D;AAAE,QAAIC,IAAEC,IAAE,GAAEC;AAAE,IAAAF,KAAEC,KAAE,IAAEF,GAAE,CAAC,GAAEA,GAAE,UAAQ,KAAGE,KAAEF,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,KAAG,MAAIA,GAAE,WAASE,KAAEF,GAAE,CAAC;AAAG,UAAMI,KAAE,IAAI,YAAYT,EAAC,GAAEU,KAAE,KAAK,QAAM,KAAK;AAAO,QAAGJ,GAAE,WAASI,GAAE,KAAG,EAAET,EAAC,KAAGA,GAAE,WAASS,GAAE,KAAGR,GAAE,MAAIM,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,YAAMR,KAAEC,GAAEO,EAAC;AAAE,UAAGR,IAAE;AAAC,cAAMC,KAAED,KAAE;AAAI,QAAAS,GAAED,EAAC,IAAE,IAAER,MAAG,KAAG,EAAEQ,EAAC,IAAEP,MAAG,KAAGM,GAAEC,EAAC,IAAEP,MAAG,IAAEK,GAAEE,EAAC,IAAEP,KAAED,MAAG,KAAG,EAAEQ,EAAC,KAAG,KAAGD,GAAEC,EAAC,KAAG,IAAEF,GAAEE,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,QAAM,MAAIA,KAAE,GAAEA,KAAEE,IAAEF,KAAI,CAAAP,GAAEO,EAAC,MAAIC,GAAED,EAAC,IAAE,OAAK,KAAG,EAAEA,EAAC,KAAG,KAAGD,GAAEC,EAAC,KAAG,IAAEF,GAAEE,EAAC;AAAA,QAAQ,MAAIA,KAAE,GAAEA,KAAEE,IAAEF,KAAI,CAAAC,GAAED,EAAC,IAAE,OAAK,KAAG,EAAEA,EAAC,KAAG,KAAGD,GAAEC,EAAC,KAAG,IAAEF,GAAEE,EAAC;AAAA,QAAO,GAAE,UAAU,KAAK,aAAa,EAAE,MAAM,eAAc,uDAAuD;AAAA,EAAC;AAAA,EAAC,iBAAiBR,IAAE;AAAC,UAAK,EAAC,QAAOC,IAAE,MAAKC,IAAE,YAAW,EAAC,IAAE;AAAK,QAAG,CAACF,MAAG,CAACC,MAAG,CAACA,GAAE,OAAO,QAAO,KAAK,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,eAAc,4DAA4D;AAAE,UAAMI,KAAE,KAAK;AAAU,QAAIC,KAAE,GAAEC,KAAE,GAAE,IAAE;AAAE,QAAG,KAAG,EAAE,SAAO,GAAE;AAAC,iBAAUP,MAAK,EAAE,KAAG,QAAMA,GAAE,aAAWO,KAAE,KAAK,IAAIA,IAAEP,GAAE,QAAQ,IAAG,QAAMA,GAAE,YAAU,QAAMA,GAAE,UAAS;AAAC,cAAMC,KAAED,GAAE,WAASA,GAAE;AAAS,YAAE,KAAK,IAAI,GAAEC,EAAC;AAAA,MAAC;AAAC,MAAAK,KAAE,MAAI;AAAA,IAAC,OAAK;AAAC,UAAIN,KAAE;AAAI,eAAOK,MAAGE,KAAE,MAAKP,KAAE,OAAK,UAAQK,KAAEL,KAAE,QAAM,UAAQK,MAAGE,KAAE,QAAOP,KAAE,SAAO,UAAQK,KAAEL,KAAE,aAAW,UAAQK,MAAGE,KAAE,aAAYP,KAAE,cAAY,UAAQK,MAAGE,KAAE,QAAOP,KAAE,SAAO,UAAQK,OAAIE,KAAE,CAAC,OAAO,WAAUP,KAAE,OAAO,YAAWM,KAAE,OAAKN,KAAEO;AAAA,IAAE;AAAC,UAAMC,KAAE,IAAI,YAAYR,EAAC,GAAES,KAAE,KAAK,QAAM,KAAK;AAAO,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,QAAGJ,KAAEC,KAAEC,KAAEX,GAAE,CAAC,GAAES,GAAE,WAASD,GAAE,QAAO,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,eAAc,uDAAuD;AAAE,QAAGR,GAAE,UAAQ,EAAE,KAAGU,KAAEV,GAAE,CAAC,GAAEA,GAAE,UAAQ,MAAIW,KAAEX,GAAE,CAAC,IAAG,EAAEC,EAAC,KAAGA,GAAE,WAASO,GAAE,MAAII,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,CAAAX,GAAEW,EAAC,MAAIL,GAAEK,EAAC,IAAE,OAAK,MAAID,GAAEC,EAAC,IAAEN,MAAGD,MAAG,MAAIK,GAAEE,EAAC,IAAEN,MAAGD,MAAG,KAAGI,GAAEG,EAAC,IAAEN,MAAGD;AAAA,QAAQ,MAAIO,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,CAAAL,GAAEK,EAAC,IAAE,OAAK,MAAID,GAAEC,EAAC,IAAEN,MAAGD,MAAG,MAAIK,GAAEE,EAAC,IAAEN,MAAGD,MAAG,KAAGI,GAAEG,EAAC,IAAEN,MAAGD;AAAA,aAAU,EAAEJ,EAAC,KAAGA,GAAE,WAASO,GAAE,MAAII,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,CAAAC,MAAGJ,GAAEG,EAAC,IAAEN,MAAGD,IAAEJ,GAAEW,EAAC,MAAIL,GAAEK,EAAC,IAAE,OAAK,KAAGC,MAAG,KAAGA,MAAG,IAAEA;AAAA,QAAQ,MAAID,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,CAAAC,MAAGJ,GAAEG,EAAC,IAAEN,MAAGD,IAAEE,GAAEK,EAAC,IAAE,OAAK,KAAGC,MAAG,KAAGA,MAAG,IAAEA;AAAA,EAAC;AAAA,EAAC,eAAed,IAAE;AAAC,UAAK,EAAC,QAAOC,IAAE,MAAKC,GAAC,IAAE;AAAK,QAAG,CAACF,MAAG,CAACC,MAAG,CAACA,GAAE,OAAO,QAAO,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,oBAAmB,4DAA4D;AAAE,QAAI,GAAEI,IAAEC,IAAEC;AAAE,QAAEF,KAAEC,KAAEL,GAAE,CAAC,GAAEA,GAAE,UAAQ,KAAGI,KAAEJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,KAAG,MAAIA,GAAE,WAASI,KAAEJ,GAAE,CAAC;AAAG,UAAM,IAAE,KAAK,QAAM,KAAK;AAAO,QAAG,EAAE,WAAS,EAAE,QAAO,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,oBAAmB,uDAAuD;AAAE,QAAIO,KAAE;AAAE,QAAG,EAAEN,EAAC,KAAGA,GAAE,WAAS,EAAE,MAAIK,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAP,GAAEQ,IAAG,IAAE,EAAED,EAAC,GAAEP,GAAEQ,IAAG,IAAEH,GAAEE,EAAC,GAAEP,GAAEQ,IAAG,IAAEF,GAAEC,EAAC,GAAEP,GAAEQ,IAAG,IAAE,IAAEN,GAAEK,EAAC;AAAA,QAAO,MAAIA,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAP,GAAEQ,IAAG,IAAE,EAAED,EAAC,GAAEP,GAAEQ,IAAG,IAAEH,GAAEE,EAAC,GAAEP,GAAEQ,IAAG,IAAEF,GAAEC,EAAC,GAAEP,GAAEQ,IAAG,IAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBR,IAAEC,IAAE;AAAC,QAAIC,KAAE,IAAE,GAAE,IAAE,KAAG;AAAE,UAAMC,KAAEH,GAAE;AAAO,QAAIK,IAAEC,KAAE;AAAE,QAAG,EAAEL,EAAC,EAAE,MAAII,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAJ,GAAEI,EAAC,MAAIC,KAAEN,GAAEK,EAAC,GAAEH,KAAEI,KAAEJ,KAAEI,KAAEJ,IAAE,IAAEI,KAAE,IAAEA,KAAE;AAAA,QAAQ,MAAID,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAC,KAAEN,GAAEK,EAAC,GAAEH,KAAEI,KAAEJ,KAAEI,KAAEJ,IAAE,IAAEI,KAAE,IAAEA,KAAE;AAAE,WAAO,IAAIH,GAAED,IAAE,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACA,GAAE,WAAW,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,gCAAgC,CAAC,GAAE,CAAC;AAAE,IAAMU,KAAE;;;ACA9iO,IAAI;AAAJ,IAAMG;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,WAAS,CAAC,IAAE;AAAU,EAAE,MAAI,IAAE,CAAC,EAAE,GAAE,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,OAAK,CAAC,IAAE;AAAM,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAME,KAAE;AAAE,SAASC,GAAEC,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG,qCAAmCA,GAAE,iBAAeA,GAAE,UAAQA,GAAE,OAAO,SAAO;AAAC;AAAC,SAAS,EAAEH,IAAEG,IAAE;AAAC,MAAG,EAACA,MAAA,gBAAAA,GAAG,WAAQ,CAACD,GAAEF,EAAC,EAAE,QAAOA;AAAE,QAAM,IAAEA,GAAE,OAAO;AAAO,SAAOG,MAAGA,GAAE,KAAM,CAAAH,OAAGA,MAAG,CAAE,KAAG,MAAI,KAAG,MAAIG,GAAE,UAAQ,MAAIA,GAAE,CAAC,IAAEH,KAAE,MAAIG,GAAE,UAAQA,GAAE,KAAM,CAACH,IAAEG,OAAIH,OAAIG,EAAE,IAAE,IAAIC,GAAE,EAAC,WAAUJ,GAAE,WAAU,OAAMA,GAAE,OAAM,QAAOA,GAAE,QAAO,MAAKA,GAAE,MAAK,iBAAgBA,GAAE,iBAAgB,aAAYA,GAAE,aAAY,QAAOG,GAAE,IAAK,CAAAA,OAAGH,GAAE,OAAOG,EAAC,CAAE,GAAE,YAAWH,GAAE,cAAYG,GAAE,IAAK,CAAAA,OAAGH,GAAE,WAAWG,EAAC,CAAE,EAAC,CAAC,IAAEH;AAAC;AAAC,SAASK,GAAEF,IAAE;AAAC,MAAG,EAACA,MAAA,gBAAAA,GAAG,WAAQA,GAAE,KAAM,CAAAH,OAAG,CAACE,GAAEF,EAAC,CAAE,EAAE,QAAO;AAAK,MAAG,MAAIG,GAAE,OAAO,QAAO,EAAEA,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,EAAE,MAAM,IAAE;AAAK,QAAM,IAAEA,IAAE,EAAC,OAAM,GAAE,QAAOG,IAAE,WAAUP,GAAC,IAAE,EAAE,CAAC;AAAE,MAAG,EAAE,KAAM,CAAAC,OAAGA,GAAE,UAAQ,KAAGA,GAAE,WAASM,EAAE,EAAE,QAAO;AAAK,QAAML,KAAE,EAAE,IAAK,CAAC,EAAC,MAAKD,GAAC,MAAIA,EAAE,EAAE,OAAQ,CAAAA,OAAG,QAAMA,EAAE;AAAE,MAAIO,KAAE;AAAK,EAAAN,GAAE,WAASM,KAAE,IAAI,WAAW,IAAED,EAAC,GAAEC,GAAE,IAAIN,GAAE,CAAC,CAAC,GAAEA,GAAE,SAAO,KAAGO,GAAEP,GAAE,MAAM,CAAC,GAAEM,EAAC;AAAG,QAAMF,KAAE,CAAC;AAAE,IAAE,QAAS,CAAC,EAAC,QAAOL,GAAC,MAAIK,GAAE,KAAK,GAAGL,EAAC,CAAE;AAAE,QAAMS,KAAE,EAAE,IAAK,CAAC,EAAC,YAAWT,GAAC,MAAIA,EAAE,EAAE,OAAQ,CAAAA,OAAGA,MAAA,gBAAAA,GAAG,MAAO,GAAEU,KAAE,CAAC;AAAE,SAAOD,GAAE,QAAS,CAAAT,OAAGU,GAAE,KAAK,GAAGV,EAAC,CAAE,GAAE,IAAII,GAAE,EAAC,WAAUL,IAAE,OAAM,GAAE,QAAOO,IAAE,MAAKC,IAAE,QAAOF,IAAE,YAAWK,GAAE,SAAOA,KAAE,KAAI,CAAC;AAAC;AAAC,SAAS,EAAEV,IAAE;AAAC,MAAG,CAACA,GAAE;AAAO,QAAMG,KAAEH,GAAE;AAAS,MAAG,CAACG,MAAG,MAAIA,GAAE,OAAO;AAAO,QAAM,IAAEA,GAAE,KAAM,CAACH,IAAEG,OAAIH,GAAE,CAAC,IAAEG,GAAE,CAAC,CAAE;AAAE,MAAIQ,KAAE;AAAE,IAAE,CAAC,EAAE,CAAC,IAAE,MAAIA,KAAE,EAAE,CAAC,EAAE,CAAC;AAAG,QAAM,IAAE,KAAK,IAAI,KAAI,EAAE,EAAE,SAAO,CAAC,EAAE,CAAC,IAAEA,KAAE,CAAC,GAAEL,KAAE,IAAI,WAAW,IAAE,CAAC,GAAEP,KAAE,CAAC;AAAE,MAAIE,IAAEC,KAAE,GAAEK,KAAE;AAAE,QAAMF,KAAE,MAAI,EAAE,CAAC,EAAE;AAAO,MAAG,IAAE,MAAM,QAAO,EAAE,QAAS,CAAAL,OAAG;AAAC,IAAAD,GAAEC,GAAE,CAAC,IAAEW,EAAC,IAAEN,KAAEL,GAAE,MAAM,CAAC,IAAEA,GAAE,MAAM,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC;AAAA,EAAC,CAAE,GAAE,EAAC,mBAAkBD,IAAE,QAAOY,IAAE,gBAAeN,GAAC;AAAE,MAAGL,GAAE,gBAAgB,MAAIC,KAAE,EAAEM,EAAC,GAAEL,KAAED,GAAE,CAAC,IAAEU,IAAET,KAAE,GAAEA,KAAI,CAAAI,GAAE,IAAEJ,EAAC,IAAED,GAAE,CAAC,GAAEK,GAAE,IAAEJ,KAAE,CAAC,IAAED,GAAE,CAAC,GAAEK,GAAE,IAAEJ,KAAE,CAAC,IAAED,GAAE,CAAC,GAAEK,GAAE,IAAEJ,KAAE,CAAC,IAAEG,KAAEJ,GAAE,CAAC,IAAE,KAAIC,OAAID,GAAE,CAAC,IAAEU,OAAIV,KAAEM,OAAI,EAAE,SAAO,IAAEN,KAAE,EAAE,EAAEM,EAAC;AAAA,MAAQ,MAAIL,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,CAAAD,KAAE,EAAEC,EAAC,GAAEK,KAAE,KAAGN,GAAE,CAAC,IAAEU,KAAGL,GAAEC,EAAC,IAAEN,GAAE,CAAC,GAAEK,GAAEC,KAAE,CAAC,IAAEN,GAAE,CAAC,GAAEK,GAAEC,KAAE,CAAC,IAAEN,GAAE,CAAC,GAAEK,GAAEC,KAAE,CAAC,IAAEF,KAAEJ,GAAE,CAAC,IAAE;AAAI,SAAM,EAAC,iBAAgBK,IAAE,QAAOK,IAAE,gBAAeN,GAAC;AAAC;AAAC,SAASK,GAAEP,IAAE,GAAE;AAAC,MAAG,CAACD,GAAEC,EAAC,EAAE,QAAOA;AAAE,MAAG,CAAC,KAAG,CAAC,EAAE,mBAAiB,CAAC,EAAE,kBAAkB,QAAOA;AAAE,QAAMQ,KAAER,GAAE,MAAM,GAAE,IAAEQ,GAAE;AAAO,MAAIL,KAAEK,GAAE;AAAK,QAAMZ,KAAEY,GAAE,QAAMA,GAAE;AAAO,MAAG,MAAI,EAAE,OAAO,QAAOR;AAAE,QAAK,EAAC,iBAAgBF,IAAE,mBAAkBM,IAAE,QAAOF,IAAE,gBAAeI,GAAC,IAAE;AAAE,MAAIC,KAAE;AAAE,QAAME,KAAE,EAAE,CAAC,GAAEC,KAAE,IAAI,WAAWD,GAAE,MAAM,GAAER,KAAE,IAAI,WAAWQ,GAAE,MAAM,GAAEE,KAAE,IAAI,WAAWF,GAAE,MAAM;AAAE,MAAIG,IAAEC,KAAE;AAAE,MAAGf,IAAE;AAAC,UAAME,KAAEF,GAAE,SAAO;AAAE,QAAG,EAAEK,EAAC,EAAE,MAAII,KAAE,GAAEA,KAAEX,IAAEW,KAAI,CAAAJ,GAAEI,EAAC,MAAIM,KAAE,KAAGJ,GAAEF,EAAC,IAAEL,KAAGW,KAAEX,MAAGW,KAAEb,KAAEG,GAAEI,EAAC,IAAE,KAAGG,GAAEH,EAAC,IAAET,GAAEe,EAAC,GAAEZ,GAAEM,EAAC,IAAET,GAAEe,KAAE,CAAC,GAAEF,GAAEJ,EAAC,IAAET,GAAEe,KAAE,CAAC,GAAEV,GAAEI,EAAC,IAAET,GAAEe,KAAE,CAAC;AAAA,SAAQ;AAAC,WAAIV,KAAE,IAAI,WAAWP,EAAC,GAAEW,KAAE,GAAEA,KAAEX,IAAEW,KAAI,CAAAM,KAAE,KAAGJ,GAAEF,EAAC,IAAEL,KAAGW,KAAEX,MAAGW,KAAEb,KAAEG,GAAEI,EAAC,IAAE,KAAGG,GAAEH,EAAC,IAAET,GAAEe,EAAC,GAAEZ,GAAEM,EAAC,IAAET,GAAEe,KAAE,CAAC,GAAEF,GAAEJ,EAAC,IAAET,GAAEe,KAAE,CAAC,GAAEV,GAAEI,EAAC,IAAET,GAAEe,KAAE,CAAC;AAAG,MAAAL,GAAE,OAAKL;AAAA,IAAC;AAAA,EAAC,WAASC,GAAE,KAAG,EAAED,EAAC,EAAE,MAAII,KAAE,GAAEA,KAAEX,IAAEW,KAAI,CAAAJ,GAAEI,EAAC,MAAIK,KAAER,GAAEK,GAAEF,EAAC,CAAC,GAAEG,GAAEH,EAAC,IAAEK,GAAE,CAAC,GAAEX,GAAEM,EAAC,IAAEK,GAAE,CAAC,GAAED,GAAEJ,EAAC,IAAEK,GAAE,CAAC,GAAET,GAAEI,EAAC,IAAEK,GAAE,CAAC;AAAA,OAAO;AAAC,SAAIT,KAAE,IAAI,WAAWP,EAAC,GAAEW,KAAE,GAAEA,KAAEX,IAAEW,KAAI,CAAAK,KAAER,GAAEK,GAAEF,EAAC,CAAC,GAAEG,GAAEH,EAAC,IAAEK,GAAE,CAAC,GAAEX,GAAEM,EAAC,IAAEK,GAAE,CAAC,GAAED,GAAEJ,EAAC,IAAEK,GAAE,CAAC,GAAET,GAAEI,EAAC,IAAEK,GAAE,CAAC;AAAE,IAAAJ,GAAE,OAAKL;AAAA,EAAC;AAAC,SAAOK,GAAE,SAAO,CAACE,IAAET,IAAEU,EAAC,GAAEH,GAAE,aAAW,MAAKA,GAAE,YAAU,MAAKA,GAAE,cAAYF,IAAEE;AAAC;AAAC,SAASC,GAAEZ,IAAEG,IAAE;AAAC,MAAG,CAACD,GAAEF,EAAC,EAAE,QAAO;AAAK,QAAK,EAAC,QAAO,GAAE,MAAK,EAAC,IAAEA,IAAEM,KAAE,EAAE;AAAO,MAAIP,KAAEI,GAAE;AAAI,QAAK,EAAC,QAAOF,GAAC,IAAEE;AAAE,EAAAJ,MAAG,MAAIA,GAAE,CAAC,EAAE,WAASA,KAAE,EAAE,IAAK,MAAIA,EAAE;AAAG,QAAMQ,KAAE,CAAC,GAAEF,KAAEF,GAAE,mBAAiB;AAAK,WAAQQ,KAAE,GAAEA,KAAEL,IAAEK,MAAI;AAAC,UAAMX,KAAE,EAAE,EAAEW,EAAC,GAAE,GAAEZ,GAAEY,EAAC,GAAEV,MAAG,GAAEI,EAAC;AAAE,IAAAE,GAAE,KAAKP,EAAC;AAAA,EAAC;AAAC,QAAMS,KAAE,IAAIL,GAAE,EAAC,OAAMJ,GAAE,OAAM,QAAOA,GAAE,QAAO,QAAOO,IAAE,MAAK,GAAE,WAAUF,GAAC,CAAC;AAAE,SAAOI,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAAS,EAAET,IAAEG,IAAE,GAAE,GAAEG,IAAE;AAAC,QAAMP,KAAEC,GAAE,QAAOC,KAAEG,GAAE,gBAAgBE,IAAEP,EAAC;AAAE,MAAGI,GAAE,UAAQQ,KAAE,GAAEA,KAAEZ,IAAEY,KAAI,CAAAR,GAAEQ,EAAC,MAAIV,GAAEU,EAAC,IAAE,EAAEX,GAAEW,EAAC,IAAE,CAAC;AAAA,MAAQ,UAAQA,KAAE,GAAEA,KAAEZ,IAAEY,KAAI,CAAAV,GAAEU,EAAC,IAAE,EAAEX,GAAEW,EAAC,IAAE,CAAC;AAAE,SAAOV;AAAC;AAAC,SAASG,GAAEJ,IAAEG,IAAE;AAAC,MAAG,CAACD,GAAEF,EAAC,EAAE,QAAO;AAAK,QAAM,IAAEA,GAAE,MAAM,GAAE,EAAC,QAAOW,GAAC,IAAE,GAAE,IAAE,EAAE,QAAM,EAAE,QAAOL,KAAEH,GAAE,QAAOJ,KAAE,KAAK,MAAMO,KAAE,CAAC,GAAEL,KAAEE,GAAE,KAAK,MAAMJ,EAAC,CAAC,GAAEQ,KAAEI,GAAE,CAAC;AAAE,MAAIN,IAAEI,IAAEC,IAAEE,IAAEC,IAAET,IAAEU,KAAE;AAAG,QAAMC,KAAE,IAAI,WAAW,CAAC,GAAEC,KAAE,IAAI,WAAW,CAAC,GAAER,KAAE,IAAI,WAAW,CAAC;AAAE,MAAIS,KAAE,EAAE;AAAK,QAAMC,KAAE,MAAIf,GAAE,CAAC,EAAE,YAAY;AAAO,OAAIc,OAAIA,KAAE,IAAI,WAAW,CAAC,GAAEA,GAAE,KAAKC,KAAE,MAAI,CAAC,GAAE,EAAE,OAAKD,KAAGJ,KAAE,GAAEA,KAAE,GAAEA,KAAI,KAAGI,GAAEJ,EAAC,GAAE;AAAC,SAAIR,KAAEE,GAAEM,EAAC,GAAEC,KAAE,OAAGV,KAAEL,IAAEU,KAAER,IAAES,KAAE,GAAEE,KAAEN,KAAE,GAAEM,KAAEF,KAAE,KAAG;AAAC,UAAGL,OAAII,GAAE,OAAM;AAAC,QAAAK,KAAE;AAAG;AAAA,MAAK;AAAC,MAAAT,KAAEI,GAAE,QAAMC,KAAEN,KAAEQ,KAAER,IAAEA,KAAE,KAAK,OAAOM,KAAEE,MAAG,CAAC,GAAEH,KAAEN,GAAE,KAAK,MAAMC,EAAC,CAAC;AAAA,IAAC;AAAC,IAAAU,OAAIT,OAAIF,GAAEO,EAAC,EAAE,SAAOD,KAAEN,GAAEO,EAAC,GAAEI,KAAE,QAAIT,OAAIF,GAAES,EAAC,EAAE,SAAOH,KAAEN,GAAES,EAAC,GAAEE,KAAE,QAAIT,KAAEF,GAAEO,EAAC,EAAE,SAAOI,KAAE,OAAGL,KAAE,QAAMJ,KAAEF,GAAEO,EAAC,EAAE,UAAQL,KAAEF,GAAES,EAAC,EAAE,SAAOH,KAAEN,GAAEO,EAAC,GAAEI,KAAE,QAAIF,OAAIN,KAAE,KAAGQ,KAAE,OAAGL,KAAE,SAAOA,KAAEN,GAAES,EAAC,GAAEE,KAAE,SAAMA,MAAGC,GAAEF,EAAC,IAAEJ,GAAE,YAAY,CAAC,GAAEO,GAAEH,EAAC,IAAEJ,GAAE,YAAY,CAAC,GAAED,GAAEK,EAAC,IAAEJ,GAAE,YAAY,CAAC,GAAEQ,GAAEJ,EAAC,IAAEJ,GAAE,YAAY,CAAC,KAAGM,GAAEF,EAAC,IAAEG,GAAEH,EAAC,IAAEL,GAAEK,EAAC,IAAEI,GAAEJ,EAAC,IAAE;AAAA,EAAC;AAAC,SAAO,EAAE,SAAO,CAACE,IAAEC,IAAER,EAAC,GAAE,EAAE,OAAKS,IAAE,EAAE,YAAU,MAAK,EAAE,cAAYC,IAAE;AAAC;AAAC,SAASJ,GAAEd,IAAEG,IAAE;AAAC,MAAG,CAACD,GAAEF,EAAC,EAAE,QAAO;AAAK,QAAK,EAAC,OAAM,GAAE,QAAOM,GAAC,IAAEN,IAAE,EAAC,aAAYD,IAAE,cAAaE,IAAE,iBAAgBM,IAAE,cAAaF,IAAE,gBAAeI,IAAE,2BAA0BC,GAAC,IAAEP,IAAES,KAAEZ,GAAE,OAAO,CAAC,GAAEa,KAAET,GAAE,gBAAgBG,IAAEK,GAAE,MAAM,GAAER,KAAEJ,GAAE,MAAKc,KAAE,IAAI,WAAW,IAAER,EAAC;AAAE,EAAAF,KAAEU,GAAE,IAAIV,EAAC,IAAEU,GAAE,KAAK,GAAG;AAAE,QAAMC,KAAEf,GAAE,UAAU,WAAW,GAAG,IAAE,OAAK,GAAEgB,KAAEjB,GAAE,IAAK,CAAAC,OAAGA,KAAEe,EAAE;AAAE,EAAAC,GAAE,CAAC,IAAEjB,GAAE,CAAC,GAAEiB,GAAEA,GAAE,SAAO,CAAC,IAAEjB,GAAEA,GAAE,SAAO,CAAC,KAAGW,KAAE,OAAK;AAAG,QAAMF,KAAET,GAAE,SAAO,GAAE,CAACkB,IAAEC,EAAC,IAAEnB,GAAEQ,EAAC;AAAE,WAAQI,KAAE,GAAEA,KAAEL,IAAEK,KAAI,UAAQX,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAMG,KAAEQ,KAAE,IAAEX;AAAE,QAAGc,GAAEX,EAAC,GAAE;AAAC,YAAMH,KAAEY,GAAET,EAAC;AAAE,UAAIgB,KAAE;AAAG,eAAQR,KAAEH,KAAE,GAAEG,MAAG,GAAEA,KAAI,KAAGX,OAAIgB,GAAE,IAAEL,EAAC,KAAGX,KAAEgB,GAAE,IAAEL,EAAC,KAAGX,KAAEgB,GAAE,IAAEL,KAAE,CAAC,GAAE;AAAC,QAAAE,GAAEV,EAAC,IAAEF,GAAEU,EAAC,GAAEQ,KAAE;AAAG;AAAA,MAAK;AAAC,MAAAA,OAAIV,KAAEI,GAAEV,EAAC,IAAEH,KAAEkB,KAAEA,KAAElB,KAAEiB,KAAEA,KAAEjB,KAAEc,GAAEX,EAAC,IAAE;AAAA,IAAE;AAAA,EAAC;AAAC,MAAGE,MAAA,gBAAAA,GAAG,OAAO,UAAQM,KAAE,GAAEA,KAAEL,IAAEK,KAAI,UAAQX,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAMG,KAAEQ,KAAE,IAAEX;AAAE,QAAG,CAACI,MAAGA,GAAED,EAAC,GAAE;AAAC,YAAMH,KAAEY,GAAET,EAAC;AAAE,eAAQgB,KAAE,GAAEA,KAAEX,IAAEW,MAAG,EAAE,KAAGnB,MAAGK,GAAEc,EAAC,KAAGnB,MAAGK,GAAEc,KAAE,CAAC,GAAE;AAAC,QAAAN,GAAEV,EAAC,IAAE,GAAEW,GAAEX,EAAC,IAAE;AAAE;AAAA,MAAK;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,IAAIC,GAAE,EAAC,OAAM,GAAE,QAAOE,IAAE,WAAUC,IAAE,QAAO,CAACM,EAAC,GAAE,MAAKC,GAAC,CAAC;AAAC;AAAC,SAAS,EAAEd,IAAEG,IAAE,GAAEQ,IAAE;AAAC,QAAM,IAAE,QAAM,KAAG,EAAE,UAAQ,IAAE,IAAI,IAAI,CAAC,IAAE,MAAKL,KAAE,OAAI,uBAAG,UAAO,EAAE,CAAC,IAAE,MAAKP,KAAE,CAAC,EAACI,MAAA,gBAAAA,GAAG;AAAO,WAAQF,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,KAAGU,GAAEV,EAAC,GAAE;AAAC,UAAMkB,KAAEnB,GAAEC,EAAC;AAAE,QAAGF,IAAE;AAAC,UAAIC,KAAE;AAAG,eAAQW,KAAE,GAAEA,KAAER,GAAE,QAAOQ,MAAG,EAAE,KAAGQ,MAAGhB,GAAEQ,EAAC,KAAGQ,MAAGhB,GAAEQ,KAAE,CAAC,GAAE;AAAC,QAAAX,KAAE;AAAG;AAAA,MAAK;AAAC,MAAAA,OAAIW,GAAEV,EAAC,IAAE;AAAA,IAAE;AAAC,IAAAU,GAAEV,EAAC,MAAIkB,OAAIb,OAAG,uBAAG,IAAIa,UAAMR,GAAEV,EAAC,IAAE;AAAA,EAAE;AAAC;AAAC,SAASe,GAAEhB,IAAEG,IAAE;AAAC,QAAM,IAAEH,GAAE,CAAC,EAAE;AAAO,WAAQW,KAAE,GAAEA,KAAE,GAAEA,KAAI,KAAGR,GAAEQ,EAAC,GAAE;AAAC,QAAIQ,KAAE;AAAG,aAAQhB,KAAE,GAAEA,KAAEH,GAAE,QAAOG,KAAI,KAAGH,GAAEG,EAAC,EAAEQ,EAAC,GAAE;AAAC,MAAAQ,KAAE;AAAG;AAAA,IAAK;AAAC,IAAAA,OAAIhB,GAAEQ,EAAC,IAAE;AAAA,EAAE;AAAC;AAAC,SAASH,GAAER,IAAEG,IAAE;AAAC,QAAM,IAAEH,GAAE,CAAC,EAAE;AAAO,WAAQW,KAAE,GAAEA,KAAE,GAAEA,KAAI,KAAGR,GAAEQ,EAAC,GAAE;AAAC,QAAIQ,KAAE;AAAG,aAAQhB,KAAE,GAAEA,KAAEH,GAAE,QAAOG,KAAI,KAAG,MAAIH,GAAEG,EAAC,EAAEQ,EAAC,GAAE;AAAC,MAAAQ,KAAE;AAAG;AAAA,IAAK;AAAC,IAAAA,OAAIhB,GAAEQ,EAAC,IAAE;AAAA,EAAE;AAAC;AAAC,SAAS,EAAEX,IAAEG,IAAE;AAAC,MAAG,CAACD,GAAEF,EAAC,EAAE,QAAO;AAAK,QAAK,EAAC,OAAM,GAAE,QAAO,GAAE,QAAOM,GAAC,IAAEN,IAAED,KAAE,IAAE,GAAEE,KAAE,IAAI,WAAWF,EAAC;AAAE,EAAAC,GAAE,OAAKC,GAAE,IAAID,GAAE,IAAI,IAAEC,GAAE,KAAK,GAAG;AAAE,QAAMM,KAAED,GAAE,QAAO,EAAC,gBAAeD,IAAE,cAAaI,IAAE,iBAAgBC,IAAE,UAASE,IAAE,SAAQR,GAAC,IAAED;AAAE,MAAGC,IAAE;AAAC,UAAMJ,KAAE,CAAC;AAAE,aAAQG,KAAE,GAAEA,KAAEI,IAAEJ,MAAI;AAAC,YAAMgB,KAAEf,GAAED,EAAC,GAAEQ,KAAE,EAAEL,GAAEH,EAAC,GAAEF,IAAEkB,GAAE,KAAIA,GAAE,UAAQ,GAAE,IAAI;AAAE,MAAAnB,GAAE,KAAKW,EAAC;AAAA,IAAC;AAAC,UAAIX,GAAE,SAAOC,GAAE,IAAID,GAAE,CAAC,CAAC,IAAEY,KAAEI,GAAEhB,IAAEC,EAAC,IAAEO,GAAER,IAAEC,EAAC;AAAA,EAAC,WAASW,IAAE;AAAC,UAAMZ,KAAE,CAAC;AAAE,aAAQG,KAAE,GAAEA,KAAEI,IAAEJ,MAAI;AAAC,YAAMgB,KAAE,IAAI,WAAWpB,EAAC;AAAE,MAAAoB,GAAE,IAAIlB,EAAC,GAAE,EAAEK,GAAEH,EAAC,GAAEE,MAAA,gBAAAA,GAAG,MAAM,IAAEF,IAAE,IAAEA,KAAE,IAAGM,MAAA,gBAAAA,GAAIN,KAAGgB,EAAC,GAAEnB,GAAE,KAAKmB,EAAC;AAAA,IAAC;AAAC,UAAInB,GAAE,SAAOC,GAAE,IAAID,GAAE,CAAC,CAAC,IAAEgB,GAAEhB,IAAEC,EAAC;AAAA,EAAC,MAAM,UAAQU,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,GAAEL,GAAEK,EAAC,GAAEN,MAAA,gBAAAA,GAAG,MAAM,IAAEM,IAAE,IAAEA,KAAE,IAAGF,MAAA,gBAAAA,GAAIE,KAAGV,EAAC;AAAE,SAAO,IAAIG,GAAE,EAAC,OAAM,GAAE,QAAO,GAAE,WAAUM,IAAE,QAAOJ,IAAE,MAAKL,GAAC,CAAC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,QAAK,EAAC,cAAaG,IAAE,aAAY,GAAE,cAAaG,IAAE,gBAAeP,IAAE,cAAaE,IAAE,2BAA0BC,IAAE,iBAAgBK,GAAC,IAAEP;AAAE,MAAG,SAAOG,MAAG,SAAOA,MAAG,UAAQA,MAAG,UAAQA,GAAE,QAAO;AAAK,QAAME,KAAEF,GAAE,SAAS,IAAI,IAAE,QAAM,KAAIM,KAAEN,GAAE,SAAS,GAAG,IAAE,CAACE,KAAE,IAAE,GAAEK,KAAEN,GAAE,gBAAgBG,IAAEF,EAAC,GAAEO,KAAE,IAAI,WAAWP,EAAC;AAAE,EAAAN,MAAGa,GAAE,KAAK,GAAG;AAAE,QAAK,CAACC,IAAET,EAAC,IAAEL,GAAEQ,EAAC;AAAE,OAAG,uBAAG,YAAQD,MAAA,gBAAAA,GAAG,SAAO;AAAC,UAAMN,KAAE,MAAKG,KAAE,EAAE,IAAK,CAAAA,OAAGA,KAAEH,EAAE;AAAE,IAAAG,GAAE,CAAC,IAAE,EAAE,CAAC,GAAED,OAAIC,GAAEA,GAAE,SAAO,CAAC,IAAE,EAAE,EAAE,SAAO,CAAC;AAAG,aAAQgB,KAAE,GAAEA,KAAEhB,GAAE,QAAOgB,MAAI;AAAC,YAAMnB,KAAEM,GAAEa,EAAC,IAAEf,KAAEA,KAAEE,GAAEa,EAAC,IAAEN,KAAEA,KAAEP,GAAEa,EAAC,GAAER,KAAE,KAAK,KAAKR,GAAE,IAAEgB,EAAC,IAAEV,EAAC,GAAE,IAAE,KAAK,MAAMN,GAAE,IAAEgB,KAAE,CAAC,IAAEV,EAAC;AAAE,eAAQN,KAAEQ,IAAER,MAAG,GAAEA,KAAI,CAAAO,GAAEP,EAAC,IAAEH,IAAEY,GAAET,EAAC,IAAE;AAAA,IAAG;AAAA,EAAC;AAAC,MAAGF,MAAA,gBAAAA,GAAG,OAAO,UAAQU,KAAE,GAAEA,KAAEV,GAAE,QAAOU,MAAI;AAAC,UAAMX,KAAE,KAAK,KAAKC,GAAE,IAAEU,EAAC,IAAEF,EAAC,GAAEN,KAAE,KAAK,MAAMF,GAAE,IAAEU,KAAE,CAAC,IAAEF,EAAC;AAAE,aAAQU,KAAEnB,IAAEmB,MAAGhB,IAAEgB,KAAI,CAAAP,GAAEO,EAAC,IAAE;AAAA,EAAC;AAAC,SAAM,EAAC,KAAIT,IAAE,QAAOD,IAAE,MAAKG,GAAC;AAAC;AAAC,SAAS,EAAEZ,IAAEG,IAAE,GAAE;AAAC,MAAG,SAAOH,MAAG,SAAOA,MAAG,UAAQA,MAAG,UAAQA,GAAE,QAAO;AAAK,QAAMW,KAAEX,GAAE,SAAS,IAAI,IAAE,QAAM,KAAI,IAAEA,GAAE,SAAS,GAAG,IAAE,CAACW,KAAE,IAAE,GAAEL,KAAE,IAAI,WAAWK,EAAC;AAAE,MAAGR,GAAE,UAAQJ,KAAE,GAAEA,KAAEI,GAAE,QAAOJ,MAAI;AAAC,UAAMC,KAAE,KAAK,KAAKG,GAAE,IAAEJ,EAAC,IAAE,CAAC,GAAEoB,KAAE,KAAK,MAAMhB,GAAE,IAAEJ,KAAE,CAAC,IAAE,CAAC;AAAE,aAAQI,KAAEH,IAAEG,MAAGgB,IAAEhB,KAAI,CAAAG,GAAEH,EAAC,IAAE;AAAA,EAAG;AAAA,MAAM,CAAAG,GAAE,KAAK,GAAG;AAAE,MAAG,EAAE,UAAQP,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,CAAAO,GAAE,EAAEP,EAAC,IAAE,CAAC,IAAE;AAAE,SAAM,EAAC,KAAIO,IAAE,QAAO,EAAC;AAAC;AAAC,SAAS,EAAEN,IAAEG,IAAE,GAAEQ,IAAE,GAAEL,IAAEP,IAAEE,IAAE;AAAC,SAAM,EAAC,MAAK,KAAG,IAAED,KAAE,IAAE,IAAE,IAAEA,KAAEA,KAAE,IAAE,IAAEA,KAAEA,IAAE,MAAKM,MAAGK,KAAER,KAAE,IAAEG,KAAEK,KAAER,KAAEA,KAAEG,KAAEK,KAAER,KAAEA,IAAE,MAAK,IAAEJ,MAAG,IAAEC,KAAE,IAAE,IAAED,KAAE,IAAEC,KAAEA,KAAE,IAAED,KAAE,IAAEC,KAAEA,IAAE,MAAKM,KAAEL,MAAGU,KAAER,KAAE,IAAEG,KAAEL,KAAEU,KAAER,KAAEA,KAAEG,KAAEL,KAAEU,KAAER,KAAEA,GAAC;AAAC;AAAC,SAAS,EAAEH,IAAE,GAAE;AAAC,MAAG,CAACA,MAAG,MAAIA,GAAE,OAAO,QAAO;AAAK,QAAMW,KAAEX,GAAE,KAAM,CAAAA,OAAGA,GAAE,UAAW;AAAE,MAAG,CAACW,MAAG,EAAEA,GAAE,UAAU,EAAE,QAAO;AAAK,QAAM,KAAGA,GAAE,OAAO,OAAKA,GAAE,OAAO,QAAMA,GAAE,WAAW,OAAML,MAAGK,GAAE,OAAO,OAAKA,GAAE,OAAO,QAAMA,GAAE,WAAW,QAAOZ,KAAE,OAAI,KAAK,IAAI,GAAEO,EAAC,GAAEL,KAAED,GAAE,KAAM,CAACA,IAAEG,OAAI,KAAK,IAAIH,GAAE,OAAO,OAAKG,GAAE,OAAO,IAAI,IAAEJ,KAAEI,GAAE,OAAO,OAAKH,GAAE,OAAO,OAAK,KAAK,IAAIA,GAAE,OAAO,OAAKG,GAAE,OAAO,IAAI,IAAEJ,KAAEC,GAAE,OAAO,OAAKG,GAAE,OAAO,OAAK,CAAE,GAAED,KAAE,KAAK,IAAI,MAAM,MAAKD,GAAE,IAAK,CAAAD,OAAGA,GAAE,OAAO,IAAK,CAAC,GAAEO,KAAE,KAAK,IAAI,MAAM,MAAKN,GAAE,IAAK,CAAAD,OAAGA,GAAE,OAAO,IAAK,CAAC,GAAEK,KAAE,KAAK,IAAI,MAAM,MAAKJ,GAAE,IAAK,CAAAD,OAAGA,GAAE,OAAO,IAAK,CAAC,GAAES,KAAE,KAAK,IAAI,MAAM,MAAKR,GAAE,IAAK,CAAAD,OAAGA,GAAE,OAAO,IAAK,CAAC,GAAEU,KAAE,EAAC,GAAE,KAAK,OAAO,EAAE,OAAKR,MAAG,CAAC,GAAE,GAAE,KAAK,OAAOO,KAAE,EAAE,QAAMH,EAAC,EAAC,GAAEM,KAAE,EAAC,OAAM,KAAK,OAAOP,KAAEH,MAAG,CAAC,GAAE,QAAO,KAAK,OAAOO,KAAEF,MAAGD,EAAC,EAAC,GAAEO,KAAE,EAAC,OAAM,KAAK,OAAO,EAAE,OAAK,EAAE,QAAM,CAAC,GAAE,QAAO,KAAK,OAAO,EAAE,OAAK,EAAE,QAAMP,EAAC,EAAC;AAAE,MAAG,KAAK,MAAMM,GAAE,QAAMD,GAAE,WAAW,KAAK,IAAE,KAAK,MAAMC,GAAE,SAAOD,GAAE,WAAW,MAAM,MAAIV,GAAE,UAAQS,GAAE,IAAE,KAAGA,GAAE,IAAE,KAAGE,GAAE,QAAMC,GAAE,SAAOD,GAAE,SAAOC,GAAE,OAAO,QAAO;AAAK,SAAM,EAAC,QAAO,GAAE,YAAW,EAAEZ,GAAE,IAAK,CAAAD,OAAGA,GAAE,UAAW,GAAEY,IAAE,EAAC,YAAWF,IAAE,UAASG,GAAC,CAAC,EAAC;AAAC;AAAC,SAAS,EAAEb,IAAEG,IAAE,GAAEQ,IAAE,GAAEL,IAAE;AAAC,QAAK,EAAC,OAAMP,IAAE,QAAOE,GAAC,IAAE,EAAE,OAAM,EAAC,GAAEC,IAAE,GAAEK,GAAC,IAAE,EAAE,QAAO,EAAC,OAAMF,IAAE,QAAOI,GAAC,IAAE,EAAE,QAAOC,KAAE,EAAEX,IAAEE,IAAEU,IAAE,GAAET,IAAEK,IAAEF,IAAEI,EAAC;AAAE,MAAIG,KAAE,GAAEC,KAAE;AAAE,MAAGP,IAAE;AAAC,UAAMN,KAAEM,GAAE,wBAAsB,MAAIA,GAAE,kBAAgB,GAAEH,KAAEJ,KAAEO,GAAE,aAAYa,KAAEb,GAAE,SAAOK,KAAER;AAAE,IAAAgB,KAAEnB,MAAGmB,KAAEhB,KAAEH,KAAEa,KAAEP,GAAE,eAAaa,MAAGnB,OAAIY,KAAEN,GAAE,aAAWA,GAAE,cAAaO,KAAE;AAAA,EAAE;AAAC,MAAGH,GAAE,QAAMG,IAAE,YAAU,OAAOV,GAAE,UAAQC,KAAEM,GAAE,MAAKN,KAAEM,GAAE,MAAKN,MAAI;AAAC,UAAMe,MAAG,IAAElB,KAAEG,KAAEG,MAAGF,MAAGM,KAAEZ,KAAEG,MAAGU,IAAEN,KAAEF,KAAEL;AAAE,aAAQY,KAAED,GAAE,MAAKC,KAAED,GAAE,MAAKC,KAAI,CAAAX,GAAEmB,KAAER,EAAC,IAAER,GAAEG,KAAEK,EAAC;AAAA,EAAC;AAAA,MAAM,UAAQP,KAAEM,GAAE,MAAKN,KAAEM,GAAE,MAAKN,MAAI;AAAC,UAAMe,MAAG,IAAElB,KAAEG,KAAEG,MAAGF,MAAGM,KAAEZ,KAAEG,MAAGU;AAAE,aAAQD,KAAED,GAAE,MAAKC,KAAED,GAAE,MAAKC,KAAI,CAAAX,GAAEmB,KAAER,EAAC,IAAER;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEG,KAAE,CAAC,GAAE;AAAC,QAAK,EAAC,YAAWP,IAAE,UAASE,IAAE,eAAcM,IAAE,aAAYF,GAAC,IAAEC;AAAE,MAAGD,GAAE,QAAO,EAAE,GAAE,GAAE,EAAC,aAAYA,GAAC,CAAC;AAAE,QAAMI,KAAE,EAAE,KAAM,CAAAT,OAAGE,GAAEF,EAAC,CAAE;AAAE,MAAG,EAAES,EAAC,EAAE,QAAO;AAAK,QAAMC,KAAET,KAAEA,GAAE,QAAM,EAAE,OAAMW,KAAEX,KAAEA,GAAE,SAAO,EAAE,QAAOY,KAAEJ,GAAE,OAAML,KAAEK,GAAE,QAAOK,KAAE,EAAE,QAAMD,IAAEE,KAAE,EAAE,SAAOX,IAAEY,KAAE,EAAC,QAAOjB,MAAG,EAAC,GAAE,GAAE,GAAE,EAAC,GAAE,QAAOE,MAAG,GAAE,OAAM,EAAC,OAAMY,IAAE,QAAOT,GAAC,EAAC,GAAEI,KAAEC,GAAE,WAAUQ,KAAEb,GAAE,yBAAyBI,EAAC,GAAEU,KAAET,GAAE,OAAO,QAAOW,KAAE,CAAC;AAAE,MAAIC,IAAEC;AAAE,WAAQtB,KAAE,GAAEA,KAAEkB,IAAElB,MAAI;AAAC,IAAAsB,KAAE,IAAIL,GAAEP,KAAEE,EAAC;AAAE,aAAQT,KAAE,GAAEA,KAAEY,IAAEZ,KAAI,UAAQQ,KAAE,GAAEA,KAAEG,IAAEH,MAAI;AAAC,YAAMY,KAAE,EAAEpB,KAAEW,KAAEH,EAAC;AAAE,MAAAT,GAAEqB,EAAC,MAAIF,KAAEE,GAAE,OAAOvB,EAAC,GAAE,EAAEsB,IAAED,IAAEL,IAAEL,IAAER,IAAEI,EAAC;AAAA,IAAE;AAAC,IAAAa,GAAE,KAAKE,EAAC;AAAA,EAAC;AAAC,MAAIE;AAAE,MAAG,EAAE,KAAM,CAAAL,OAAG,EAAEA,EAAC,KAAG,EAAEA,GAAE,IAAI,KAAGA,GAAE,KAAK,SAAO,CAAE,GAAE;AAAC,IAAAK,KAAE,IAAI,WAAWd,KAAEE,EAAC;AAAE,aAAQT,KAAE,GAAEA,KAAEY,IAAEZ,KAAI,UAAQQ,KAAE,GAAEA,KAAEG,IAAEH,MAAI;AAAC,YAAMY,KAAE,EAAEpB,KAAEW,KAAEH,EAAC,GAAEL,KAAE,EAAEiB,EAAC,IAAEA,GAAE,OAAK;AAAK,QAAEjB,EAAC,IAAE,EAAEkB,IAAElB,IAAEU,IAAEL,IAAER,IAAEI,EAAC,IAAE,EAAEiB,IAAED,KAAE,IAAE,GAAEP,IAAEL,IAAER,IAAEI,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,QAAMkB,KAAE,IAAIrB,GAAE,EAAC,OAAMM,IAAE,QAAOE,IAAE,QAAOQ,IAAE,WAAUZ,IAAE,MAAKgB,GAAC,CAAC;AAAE,SAAOC,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAAS,EAAE,GAAEnB,IAAEP,IAAE;AAAC,QAAME,KAAE,EAAE,KAAM,CAAAE,OAAG,EAAEA,EAAC,CAAE;AAAE,MAAG,EAAEF,EAAC,EAAE,QAAO;AAAK,QAAMM,KAAE,EAAE,KAAM,CAAAJ,OAAG,CAAC,EAAEA,EAAC,KAAG,CAAC,CAACA,GAAE,IAAK,GAAE,EAAC,OAAME,IAAE,QAAOI,GAAC,IAAEH,IAAEI,KAAEH,KAAE,IAAI,WAAWF,KAAEI,EAAC,IAAE,MAAK,EAAC,aAAYG,GAAC,IAAEb,IAAEc,KAAE,CAAC,GAAET,KAAEH,GAAE,cAAc,GAAEa,KAAEV,GAAE,yBAAyBH,GAAE,SAAS;AAAE,MAAGM,GAAE,UAAQP,KAAE,GAAEG,KAAE,GAAEH,KAAE,EAAE,QAAOG,MAAGS,GAAEZ,EAAC,GAAEA,MAAI;AAAC,UAAMW,KAAE,EAAEX,EAAC;AAAE,QAAG,CAACE,GAAES,EAAC,EAAE;AAAS,UAAML,KAAEH,GAAEQ,GAAE,IAAI;AAAE,aAAQ,IAAE,GAAE,IAAEF,IAAE,IAAI,UAAQc,KAAE,GAAEA,KAAEX,GAAEZ,EAAC,GAAEuB,KAAI,CAAAb,GAAE,IAAEL,KAAEkB,KAAEpB,EAAC,IAAE,QAAMG,KAAE,MAAIA,GAAE,IAAEK,GAAE,QAAMY,EAAC;AAAA,EAAC;AAAC,WAAQvB,KAAE,GAAEA,KAAEI,IAAEJ,MAAI;AAAC,UAAMG,KAAE,IAAIW,GAAET,KAAEI,EAAC;AAAE,aAAQ,IAAE,GAAEE,KAAE,GAAE,IAAE,EAAE,QAAOA,MAAGC,GAAE,CAAC,GAAE,KAAI;AAAC,YAAMN,KAAE,EAAE,CAAC;AAAE,UAAG,CAACJ,GAAEI,EAAC,EAAE;AAAS,YAAMP,MAAEO,GAAE,OAAON,EAAC;AAAE,UAAG,QAAMD,IAAE,UAAQC,KAAE,GAAEA,KAAES,IAAET,KAAI,UAAQuB,KAAE,GAAEA,KAAEX,GAAE,CAAC,GAAEW,KAAI,CAAApB,GAAEH,KAAEK,KAAEkB,KAAEZ,EAAC,IAAEZ,IAAEC,KAAEM,GAAE,QAAMiB,EAAC;AAAA,IAAC;AAAC,IAAAV,GAAE,KAAKV,EAAC;AAAA,EAAC;AAAC,QAAMY,KAAE,IAAIX,GAAE,EAAC,OAAMC,IAAE,QAAOI,IAAE,MAAKC,IAAE,QAAOG,IAAE,WAAUZ,GAAE,UAAS,CAAC;AAAE,SAAOc,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAAS,EAAEf,IAAEG,IAAE,GAAE;AAAC,MAAG,CAACD,GAAEF,EAAC,EAAE,QAAO;AAAK,QAAK,EAAC,OAAMW,IAAE,QAAO,EAAC,IAAEX,IAAEM,KAAEH,GAAE,GAAEJ,KAAEI,GAAE,GAAEF,KAAE,EAAE,QAAMK,IAAEC,KAAE,EAAE,SAAOR;AAAE,MAAGO,KAAE,KAAGP,KAAE,KAAGE,KAAEU,MAAGJ,KAAE,EAAE,QAAOP;AAAE,MAAG,MAAIM,MAAG,MAAIP,MAAGE,OAAIU,MAAGJ,OAAI,EAAE,QAAOP;AAAE,EAAAA,GAAE,SAAOA,GAAE,OAAK,IAAI,WAAWW,KAAE,CAAC;AAAG,QAAMN,KAAEL,GAAE;AAAK,WAAQE,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAMF,KAAEE,KAAES;AAAE,aAAQR,KAAE,GAAEA,KAAEQ,IAAER,KAAI,CAAAE,GAAEL,KAAEG,EAAC,IAAED,KAAEH,MAAGG,MAAGK,MAAGJ,KAAEG,MAAGH,MAAGF,KAAE,IAAE;AAAA,EAAC;AAAC,SAAOD,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAG,CAACE,GAAEF,EAAC,EAAE,QAAO;AAAK,QAAMG,KAAEH,GAAE,MAAM,GAAE,EAAC,OAAMW,IAAE,QAAO,GAAE,QAAOL,GAAC,IAAEN,IAAED,KAAEO,GAAE,CAAC,GAAEL,KAAEE,GAAE,OAAO,CAAC,GAAEI,KAAEJ,GAAEH,GAAE,IAAI;AAAE,WAAQ,IAAE,GAAE,IAAE,IAAE,GAAE,KAAI;AAAC,UAAMA,KAAE,oBAAI;AAAI,aAAQuB,KAAE,IAAE,GAAEA,KAAE,IAAE,GAAEA,KAAI,UAAQpB,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMgB,KAAEI,KAAEZ,KAAER;AAAE,QAAEH,IAAED,GAAEoB,EAAC,GAAEZ,KAAEA,GAAEY,EAAC,IAAE,CAAC;AAAA,IAAC;AAAC,IAAAlB,GAAE,IAAEU,EAAC,IAAE,EAAEX,EAAC,GAAEC,GAAE,IAAEU,KAAE,CAAC,IAAEV,GAAE,IAAEU,KAAE,CAAC,IAAEV,GAAE,IAAEU,EAAC;AAAE,QAAIR,KAAE;AAAE,WAAKA,KAAEQ,KAAE,GAAER,MAAI;AAAC,UAAIoB,MAAG,IAAE,KAAGZ,KAAER,KAAE;AAAE,QAAEH,IAAED,GAAEwB,EAAC,GAAEhB,KAAEA,GAAEgB,EAAC,IAAE,CAAC,GAAEA,MAAG,IAAE,KAAGZ,KAAER,KAAE,GAAE,EAAEH,IAAED,GAAEwB,EAAC,GAAEhB,KAAEA,GAAEgB,EAAC,IAAE,CAAC,GAAEA,KAAE,IAAEZ,KAAER,KAAE,GAAE,EAAEH,IAAED,GAAEwB,EAAC,GAAEhB,KAAEA,GAAEgB,EAAC,IAAE,CAAC,GAAEA,MAAG,IAAE,KAAGZ,KAAER,KAAE,GAAE,EAAEH,IAAED,GAAEwB,EAAC,GAAEhB,KAAEA,GAAEgB,EAAC,IAAE,CAAC,GAAEA,MAAG,IAAE,KAAGZ,KAAER,KAAE,GAAE,EAAEH,IAAED,GAAEwB,EAAC,GAAEhB,KAAEA,GAAEgB,EAAC,IAAE,CAAC,GAAEA,MAAG,IAAE,KAAGZ,KAAER,KAAE,GAAE,EAAEH,IAAED,GAAEwB,EAAC,GAAEhB,KAAEA,GAAEgB,EAAC,IAAE,CAAC,GAAEA,KAAE,IAAEZ,KAAER,KAAE,GAAE,EAAEH,IAAED,GAAEwB,EAAC,GAAEhB,KAAEA,GAAEgB,EAAC,IAAE,CAAC,GAAEA,MAAG,IAAE,KAAGZ,KAAER,KAAE,GAAE,EAAEH,IAAED,GAAEwB,EAAC,GAAEhB,KAAEA,GAAEgB,EAAC,IAAE,CAAC,GAAEtB,GAAE,IAAEU,KAAER,EAAC,IAAE,EAAEH,EAAC;AAAA,IAAC;AAAC,IAAAC,GAAE,IAAEU,KAAER,KAAE,CAAC,IAAEF,GAAE,IAAEU,KAAER,EAAC;AAAA,EAAC;AAAC,WAAQ,IAAE,GAAE,IAAEQ,IAAE,IAAI,CAAAV,GAAE,CAAC,IAAEA,GAAEU,KAAE,CAAC,IAAEV,GAAE,IAAEU,KAAE,CAAC,GAAEV,IAAG,IAAE,KAAGU,KAAE,CAAC,IAAEV,IAAG,IAAE,KAAGU,KAAE,CAAC;AAAE,SAAOR,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAAS,EAAEH,IAAE;AAAC,MAAG,MAAIA,GAAE,KAAK,QAAO;AAAE,MAAIG,KAAE,GAAE,IAAE,IAAGQ,KAAE;AAAE,QAAM,IAAEX,GAAE,KAAK;AAAE,MAAIM,KAAE,EAAE,KAAK;AAAE,SAAK,CAACA,GAAE,OAAM,CAAAK,KAAEX,GAAE,IAAIM,GAAE,KAAK,GAAEK,KAAER,OAAI,IAAEG,GAAE,OAAMH,KAAEQ,KAAGL,KAAE,EAAE,KAAK;AAAE,SAAO;AAAC;AAAC,SAAS,EAAEN,IAAEG,IAAE,GAAE;AAAC,MAAG,MAAI,EAAE;AAAO,QAAMQ,KAAEX,GAAE,IAAIG,EAAC;AAAE,QAAIQ,KAAEX,GAAE,OAAOG,EAAC,IAAEH,GAAE,IAAIG,IAAEQ,KAAE,CAAC;AAAC;AAAC,SAAS,EAAEX,IAAEG,IAAE,GAAE;AAAC,QAAI,KAAGH,GAAE,IAAIG,IAAEH,GAAE,IAAIG,EAAC,IAAEH,GAAE,IAAIG,EAAC,IAAE,IAAE,CAAC;AAAC;AAAC,SAAS,EAAEH,IAAEG,IAAE,GAAE;AAAC,MAAG,EAAC,GAAEG,IAAE,GAAEP,GAAC,IAAEI;AAAE,QAAK,EAAC,OAAMF,IAAE,QAAOM,GAAC,IAAE;AAAE,MAAG,MAAID,MAAG,MAAIP,MAAGQ,OAAIP,GAAE,UAAQC,OAAID,GAAE,MAAM,QAAOA;AAAE,QAAK,EAAC,OAAMK,IAAE,QAAOI,GAAC,IAAET,IAAEU,KAAE,KAAK,IAAI,GAAEX,EAAC,GAAEa,KAAE,KAAK,IAAI,GAAEN,EAAC,GAAEO,KAAE,KAAK,IAAIP,KAAEL,IAAEI,EAAC,GAAED,KAAE,KAAK,IAAIL,KAAEQ,IAAEE,EAAC;AAAE,MAAGI,KAAE,KAAGT,KAAE,KAAG,CAACF,GAAEF,EAAC,EAAE,QAAO;AAAK,EAAAM,KAAE,KAAK,IAAI,GAAE,CAACA,EAAC,GAAEP,KAAE,KAAK,IAAI,GAAE,CAACA,EAAC;AAAE,QAAK,EAAC,QAAOe,GAAC,IAAEd,IAAEe,KAAEd,KAAEM,IAAES,KAAEF,GAAE,QAAON,KAAE,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAEQ,IAAE,KAAI;AAAC,UAAMb,KAAEW,GAAE,CAAC,GAAES,KAAEnB,GAAE,gBAAgBJ,GAAE,WAAUe,EAAC;AAAE,aAAQf,KAAEU,IAAEV,KAAEI,IAAEJ,MAAI;AAAC,YAAMmB,KAAEnB,KAAEK;AAAE,UAAIM,MAAGX,KAAED,KAAEW,MAAGT,KAAEK;AAAE,eAAQN,KAAEY,IAAEZ,KAAEa,IAAEb,KAAI,CAAAuB,GAAEZ,IAAG,IAAER,GAAEgB,KAAEnB,EAAC;AAAA,IAAC;AAAC,IAAAQ,GAAE,KAAKe,EAAC;AAAA,EAAC;AAAC,QAAMN,KAAE,IAAI,WAAWF,EAAC,GAAEG,KAAEf,GAAEH,GAAE,IAAI;AAAE,WAAQ,IAAEU,IAAE,IAAEN,IAAE,KAAI;AAAC,UAAMJ,KAAE,IAAEK;AAAE,QAAIF,MAAG,IAAEJ,KAAEW,MAAGT,KAAEK;AAAE,aAAQa,KAAEP,IAAEO,KAAEN,IAAEM,KAAI,CAAAF,GAAEd,IAAG,IAAEe,KAAEA,GAAElB,KAAEmB,EAAC,IAAE;AAAA,EAAC;AAAC,QAAMC,KAAE,IAAIhB,GAAE,EAAC,OAAM,EAAE,OAAM,QAAO,EAAE,QAAO,WAAUJ,GAAE,WAAU,QAAOQ,IAAE,MAAKS,GAAC,CAAC;AAAE,SAAOG,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAAS,EAAEjB,IAAE,IAAE,MAAG;AAAC,MAAG,CAACD,GAAEC,EAAC,EAAE,QAAO;AAAK,QAAK,EAAC,QAAO,GAAE,OAAMG,IAAE,QAAOP,IAAE,MAAKE,IAAE,WAAUM,GAAC,IAAEJ,IAAEE,KAAE,CAAC,GAAEI,KAAE,KAAK,MAAMH,KAAE,CAAC,GAAEI,KAAE,KAAK,MAAMX,KAAE,CAAC,GAAEa,KAAEb,KAAE,GAAEc,KAAEP,KAAE;AAAE,WAAQN,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMG,KAAE,EAAEH,EAAC,GAAEC,KAAEG,GAAE,gBAAgBG,IAAEE,KAAEC,EAAC;AAAE,QAAIR,KAAE;AAAE,aAAQF,KAAE,GAAEA,KAAED,IAAEC,MAAG,EAAE,UAAQW,KAAE,GAAEA,KAAEL,IAAEK,MAAG,GAAE;AAAC,YAAMY,KAAEpB,GAAEH,KAAEM,KAAEK,EAAC;AAAE,UAAG,GAAE;AAAC,cAAMQ,KAAER,OAAIE,KAAEU,KAAEpB,GAAEH,KAAEM,KAAEK,KAAE,CAAC,GAAEZ,MAAEC,OAAIY,KAAEW,KAAEpB,GAAEH,KAAEM,KAAEK,KAAEL,EAAC,GAAEC,KAAEI,OAAIE,KAAEd,MAAEC,OAAIY,KAAEO,KAAEhB,GAAEH,KAAEM,KAAEK,KAAEL,KAAE,CAAC;AAAE,QAAAL,GAAEC,IAAG,KAAGqB,KAAEJ,KAAEpB,MAAEQ,MAAG;AAAA,MAAC,MAAM,CAAAN,GAAEC,IAAG,IAAEqB;AAAA,IAAC;AAAC,IAAAlB,GAAE,KAAKJ,EAAC;AAAA,EAAC;AAAC,MAAIG,KAAE;AAAK,MAAG,EAAEH,EAAC,GAAE;AAAC,IAAAG,KAAE,IAAI,WAAWK,KAAEC,EAAC;AAAE,QAAIV,KAAE;AAAE,aAAQG,KAAE,GAAEA,KAAEJ,IAAEI,MAAG,EAAE,UAAQQ,KAAE,GAAEA,KAAEL,IAAEK,MAAG,GAAE;AAAC,YAAMY,KAAEtB,GAAEE,KAAEG,KAAEK,EAAC;AAAE,UAAG,GAAE;AAAC,cAAMQ,KAAER,OAAIE,KAAEU,KAAEtB,GAAEE,KAAEG,KAAEK,KAAE,CAAC,GAAEZ,MAAEI,OAAIS,KAAEW,KAAEtB,GAAEE,KAAEG,KAAEK,KAAEL,EAAC,GAAEJ,KAAES,OAAIE,KAAEd,MAAEI,OAAIS,KAAEO,KAAElB,GAAEE,KAAEG,KAAEK,KAAEL,KAAE,CAAC;AAAE,QAAAF,GAAEJ,IAAG,IAAEuB,KAAEJ,KAAEpB,MAAEG,KAAE,IAAE;AAAA,MAAC,MAAM,CAAAE,GAAEJ,IAAG,IAAEuB;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,IAAInB,GAAE,EAAC,OAAMK,IAAE,QAAOC,IAAE,WAAUH,IAAE,QAAOF,IAAE,MAAKD,GAAC,CAAC;AAAC;AAAC,SAASsB,GAAE1B,IAAEG,IAAE,GAAE;AAAC,MAAG,CAACD,GAAEF,EAAC,EAAE,QAAO;AAAK,QAAK,EAAC,OAAMW,IAAE,QAAO,EAAC,IAAER;AAAE,MAAG,EAAC,OAAMG,IAAE,QAAOP,GAAC,IAAEC;AAAE,QAAMC,KAAE,oBAAI,OAAIM,KAAE,EAAC,GAAE,GAAE,GAAE,EAAC,GAAEF,KAAE,QAAM,IAAE,IAAE,IAAE;AAAE,MAAII,KAAET;AAAE,WAAQE,KAAE,GAAEA,KAAEG,IAAEH,MAAI;AAAC,UAAMF,KAAE,KAAK,KAAKM,KAAEK,EAAC,GAAEQ,KAAE,KAAK,KAAKpB,KAAE,CAAC;AAAE,aAAQO,KAAE,GAAEA,KAAEa,IAAEb,MAAI;AAAC,MAAAC,GAAE,IAAED,KAAE;AAAE,eAAQa,KAAE,GAAEA,KAAEnB,IAAEmB,MAAI;AAAC,QAAAZ,GAAE,IAAEY,KAAER;AAAE,cAAMX,KAAE,EAAES,IAAEF,IAAEJ,EAAC;AAAE,QAAAF,GAAE,IAAI,GAAGC,EAAC,IAAII,EAAC,IAAIa,EAAC,IAAGnB,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAE,KAAEG,KAAE,MAAII,KAAE,EAAEA,EAAC,IAAGH,KAAE,KAAK,MAAMA,KAAE,CAAC,GAAEP,KAAE,KAAK,MAAMA,KAAE,CAAC;AAAA,EAAC;AAAC,SAAOE;AAAC;AAAC,SAAS,EAAED,IAAEG,IAAE,GAAEQ,IAAE,IAAE,GAAE;AAAC,QAAK,EAAC,OAAML,IAAE,QAAOP,GAAC,IAAEC,IAAE,EAAC,OAAMC,IAAE,QAAOC,GAAC,IAAEC,IAAEI,KAAEI,GAAE,MAAKN,KAAEM,GAAE,MAAKF,KAAE,KAAK,KAAKR,KAAEM,KAAE,MAAGA,EAAC,GAAEG,KAAE,KAAK,KAAKR,KAAEG,KAAE,MAAGA,EAAC;AAAE,MAAIO,IAAEC,IAAET,IAAEU,IAAEC,IAAEC,IAAER;AAAE,QAAMS,KAAER,KAAEF,IAAEW,KAAED,KAAEP,KAAEL,IAAEe,KAAE,IAAI,aAAaF,EAAC,GAAEG,KAAE,IAAI,aAAaH,EAAC,GAAEI,KAAE,IAAI,YAAYJ,EAAC,GAAES,KAAE,IAAI,YAAYT,EAAC;AAAE,MAAIM,IAAEI,IAAEH,KAAE;AAAE,WAAQI,KAAE,GAAEA,KAAEnB,IAAEmB,KAAI,UAAQ7B,KAAE,GAAEA,KAAES,IAAET,MAAI;AAAC,IAAAY,KAAE,MAAIiB,KAAEpB,KAAET,KAAGa,KAAE,EAAED,EAAC,GAAER,KAAE,EAAEQ,KAAE,CAAC,GAAEE,KAAE,EAAEF,KAAE,CAAC,GAAEG,KAAE,EAAEH,KAAE,CAAC,GAAEI,KAAE,EAAEJ,KAAE,CAAC,GAAEJ,KAAE,EAAEI,KAAE,CAAC;AAAE,aAAQT,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,MAAAsB,MAAGI,KAAExB,KAAEF,MAAGc,KAAEjB,KAAEO,IAAEqB,MAAGzB,KAAE,OAAIE;AAAE,eAAQL,KAAE,GAAEA,KAAEG,IAAEH,KAAI,CAAAwB,MAAGxB,KAAE,OAAIO,IAAEa,GAAEK,KAAEzB,EAAC,KAAGa,KAAEW,KAAEpB,KAAEwB,KAAEd,MAAGR,KAAE,GAAEe,GAAEI,KAAEzB,EAAC,KAAGe,KAAES,KAAER,KAAEY,KAAEpB,MAAGT,KAAE,GAAEuB,GAAEG,KAAEzB,EAAC,IAAE,KAAK,MAAMoB,GAAEK,KAAEzB,EAAC,CAAC,GAAE2B,GAAEF,KAAEzB,EAAC,IAAE,KAAK,MAAMqB,GAAEI,KAAEzB,EAAC,CAAC;AAAA,IAAC;AAAC,IAAAY,MAAG,GAAEC,KAAE,EAAED,EAAC,GAAER,KAAE,EAAEQ,KAAE,CAAC,GAAEE,KAAE,EAAEF,KAAE,CAAC,GAAEG,KAAE,EAAEH,KAAE,CAAC,GAAEI,KAAE,EAAEJ,KAAE,CAAC,GAAEJ,KAAE,EAAEI,KAAE,CAAC;AAAE,aAAQT,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,MAAAsB,MAAGI,KAAExB,KAAEF,MAAGc,KAAEjB,KAAEO,IAAEqB,MAAGzB,KAAE,OAAIE;AAAE,eAAQL,KAAEG,IAAEH,KAAEO,IAAEP,KAAI,CAAAwB,MAAGxB,KAAE,OAAIO,IAAEa,GAAEK,KAAEzB,EAAC,KAAGa,KAAEW,KAAEpB,KAAEwB,KAAEd,MAAGR,KAAE,GAAEe,GAAEI,KAAEzB,EAAC,KAAGe,KAAES,KAAER,KAAEY,KAAEpB,MAAGT,KAAE,GAAEuB,GAAEG,KAAEzB,EAAC,IAAE,KAAK,MAAMoB,GAAEK,KAAEzB,EAAC,CAAC,GAAE2B,GAAEF,KAAEzB,EAAC,IAAE,KAAK,MAAMqB,GAAEI,KAAEzB,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,WAAUoB,IAAE,WAAUC,IAAE,YAAWC,IAAE,YAAWK,IAAE,WAAUV,GAAC;AAAC;AAAC,SAAS,EAAEjB,IAAEG,IAAE;AAAC,QAAK,EAAC,cAAa,GAAE,SAAQQ,GAAC,IAAER,IAAE,EAAC,WAAU,GAAE,WAAUG,IAAE,WAAUP,GAAC,IAAE,EAAEC,IAAEA,IAAE,GAAE,EAAC,MAAKW,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,EAAC,CAAC,GAAE,EAAC,OAAMV,IAAE,QAAOC,GAAC,IAAEF,IAAEO,KAAE,IAAI,aAAaN,KAAEC,EAAC,GAAEG,KAAE,MAAI,KAAK;AAAG,WAAQI,KAAE,GAAEA,KAAEP,IAAEO,KAAI,UAAQT,KAAE,GAAEA,KAAEC,IAAED,MAAI;AAAC,UAAMG,KAAEM,KAAEV,KAAEC,IAAEmB,KAAE,MAAIV,KAAEN,KAAEA,KAAEJ,IAAEY,KAAEF,OAAIP,KAAE,IAAEC,KAAEA,KAAEJ,IAAEW,KAAE,EAAES,EAAC,IAAE,EAAER,EAAC,GAAEC,KAAEN,GAAEK,EAAC,IAAEL,GAAEa,EAAC;AAAE,QAAG,MAAMT,EAAC,KAAG,MAAME,EAAC,EAAE,CAAAL,GAAEE,KAAER,KAAED,EAAC,IAAE;AAAA,SAAO;AAAC,UAAIG,KAAE,KAAK,MAAMS,IAAEF,EAAC,IAAEL;AAAE,MAAAF,MAAG,MAAIA,MAAG,KAAII,GAAEE,KAAER,KAAED,EAAC,IAAEG;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,SAAS,EAAEJ,IAAE,GAAE,GAAEG,IAAEP,KAAE,WAAU;AAAC,MAAG,CAACG,GAAEC,EAAC,EAAE,QAAO;AAAK,iBAAaJ,OAAII,KAAE,EAAEA,EAAC;AAAG,QAAK,EAAC,QAAOF,IAAE,MAAKM,IAAE,WAAUF,GAAC,IAAEF,IAAEM,KAAEN,GAAE,OAAMO,KAAEP,GAAE,QAAOS,KAAER,GAAE,yBAAyBC,EAAC,GAAEQ,KAAEZ,GAAE,QAAO,EAAC,OAAMG,IAAE,QAAOU,GAAC,IAAE;AAAE,MAAIC,KAAE;AAAG,WAAQf,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAG,EAAE,QAAK,EAAEA,EAAC,KAAG,OAAK,EAAEA,KAAE,CAAC,KAAG,OAAK,EAAEA,KAAE,CAAC,MAAIe,KAAE;AAAI,QAAK,EAAC,WAAUC,IAAE,WAAUR,IAAE,YAAWS,IAAE,YAAWC,IAAE,WAAUE,GAAC,IAAE,EAAE,EAAC,OAAMX,IAAE,QAAOC,GAAC,GAAE,GAAE,GAAEJ,IAAE,eAAaP,KAAE,MAAG,CAAC;AAAE,MAAIsB;AAAE,QAAMC,KAAE,CAACtB,IAAEG,IAAEgB,OAAI;AAAC,UAAMR,KAAEX,cAAa,gBAAcA,cAAa,eAAa,IAAE;AAAG,aAAQuB,KAAE,GAAEA,KAAET,IAAES,MAAI;AAAC,MAAAF,KAAEE,KAAEH;AAAE,eAAQd,KAAE,GAAEA,KAAEF,IAAEE,MAAI;AAAC,YAAGU,GAAEK,EAAC,IAAE,KAAGb,GAAEa,EAAC,IAAE,EAAE,CAAArB,GAAEuB,KAAEnB,KAAEE,EAAC,IAAE;AAAA,iBAAUa,GAAE,CAAAnB,GAAEuB,KAAEnB,KAAEE,EAAC,IAAEH,GAAEc,GAAEI,EAAC,IAAEH,GAAEG,EAAC,IAAEZ,EAAC;AAAA,aAAM;AAAC,gBAAMU,KAAE,KAAK,MAAMH,GAAEK,EAAC,CAAC,GAAEtB,MAAE,KAAK,MAAMS,GAAEa,EAAC,CAAC,GAAEpB,KAAE,KAAK,KAAKe,GAAEK,EAAC,CAAC,GAAEnB,KAAE,KAAK,KAAKM,GAAEa,EAAC,CAAC,GAAEhB,KAAEW,GAAEK,EAAC,IAAEF,IAAET,KAAEF,GAAEa,EAAC,IAAEtB;AAAE,cAAG,CAACQ,MAAGA,GAAEY,KAAEpB,MAAEU,EAAC,KAAGF,GAAEY,KAAEpB,MAAEU,EAAC,KAAGF,GAAEY,KAAEjB,KAAEO,EAAC,KAAGF,GAAEN,KAAEC,KAAEO,EAAC,GAAE;AAAC,kBAAMF,MAAG,IAAEF,MAAGF,GAAEgB,KAAEpB,MAAEU,EAAC,IAAEJ,KAAEF,GAAEF,KAAEF,MAAEU,EAAC,GAAEG,MAAG,IAAEP,MAAGF,GAAEgB,KAAEjB,KAAEO,EAAC,IAAEJ,KAAEF,GAAEF,KAAEC,KAAEO,EAAC;AAAE,YAAAT,GAAEuB,KAAEnB,KAAEE,EAAC,KAAG,IAAEI,MAAGH,KAAEG,KAAEE,KAAED;AAAA,UAAC,MAAM,CAAAX,GAAEuB,KAAEnB,KAAEE,EAAC,IAAEH,GAAEc,GAAEI,EAAC,IAAEH,GAAEG,EAAC,IAAEZ,EAAC;AAAA,QAAC;AAAC,QAAAY;AAAA,MAAG;AAAA,IAAC;AAAA,EAAC,GAAEM,KAAE,CAAC;AAAE,MAAIH;AAAE,WAAQxB,KAAE,GAAEA,KAAEa,IAAEb,KAAI,CAAAwB,KAAE,IAAIZ,GAAER,KAAEU,EAAC,GAAEQ,GAAEE,IAAEvB,GAAED,EAAC,GAAE,cAAYD,MAAG,eAAaA,EAAC,GAAE4B,GAAE,KAAKH,EAAC;AAAE,QAAMI,KAAE,IAAIxB,GAAE,EAAC,OAAMA,IAAE,QAAOU,IAAE,WAAUT,IAAE,QAAOsB,GAAC,CAAC;AAAE,MAAG,EAAEpB,EAAC,EAAE,CAAAqB,GAAE,OAAK,IAAI,WAAWxB,KAAEU,EAAC,GAAEQ,GAAEM,GAAE,MAAKrB,IAAE,IAAE;AAAA,WAAUQ,IAAE;AAAC,IAAAa,GAAE,OAAK,IAAI,WAAWxB,KAAEU,EAAC;AAAE,aAAQd,KAAE,GAAEA,KAAEI,KAAEU,IAAEd,KAAI,CAAA4B,GAAE,KAAK5B,EAAC,IAAEgB,GAAEhB,EAAC,IAAE,KAAGQ,GAAER,EAAC,IAAE,IAAE,IAAE;AAAA,EAAC;AAAC,SAAO4B,GAAE,iBAAiB,GAAEA;AAAC;;;ACAvtf,IAAME,KAAE,oBAAI;AAAIA,GAAE,IAAI,oBAAmB,CAAC,GAAEA,GAAE,IAAI,sBAAqB,QAAO,GAAEA,GAAE,IAAI,SAAQ,QAAO,GAAEA,GAAE,IAAI,mBAAkB,MAAK,GAAEA,GAAE,IAAI,iBAAgB,OAAM;AAAE,IAAMC,KAAE,MAAI,KAAK;AAAjB,IAAoBC,KAAE;AAAtB,IAAwBC,KAAE,IAAIH,GAAE,EAAC,qBAAoB,oBAAmB,uBAAsB,sBAAqB,WAAU,SAAQ,mBAAkB,mBAAkB,kBAAiB,gBAAe,CAAC;AAAE,SAASI,GAAEC,IAAEC,IAAE;AAAC,SAAON,GAAE,IAAIK,EAAC,IAAEL,GAAE,IAAIM,EAAC,KAAG;AAAC;AAAC,SAASC,GAAEF,IAAE;AAAC,UAAO,MAAIA,MAAG;AAAG;AAAC,SAASG,GAAEH,IAAEC,KAAE,cAAa;AAAC,QAAK,CAAC,GAAEG,EAAC,IAAEJ,IAAEK,KAAE,KAAK,KAAK,IAAE,IAAED,KAAEA,EAAC;AAAE,MAAI,IAAE,KAAK,MAAMA,IAAE,CAAC,IAAER;AAAE,SAAO,KAAG,MAAI,KAAG,KAAI,iBAAeK,OAAI,IAAEC,GAAE,CAAC,IAAG,CAACG,IAAE,CAAC;AAAC;AAAC,SAASC,GAAEN,IAAEC,KAAE,cAAa;AAAC,MAAI,IAAED,GAAE,CAAC;AAAE,mBAAeC,OAAI,IAAEC,GAAE,CAAC,IAAG,KAAG;AAAI,QAAME,KAAEJ,GAAE,CAAC;AAAE,SAAM,CAACI,KAAE,KAAK,IAAI,IAAER,EAAC,GAAEQ,KAAE,KAAK,IAAI,IAAER,EAAC,CAAC;AAAC;AAAC,SAASW,GAAEP,IAAEI,IAAEC,IAAEV,KAAE,cAAa;AAAC,MAAG,CAACC,GAAEI,EAAC,KAAG,EAAEK,EAAC,EAAE,QAAOL;AAAE,QAAMJ,KAAE,oBAAkBQ,KAAEJ,GAAE,MAAM,IAAEC,GAAEO,GAAER,IAAEI,EAAC,CAAC,GAAEP,KAAED,GAAE,OAAO,CAAC;AAAE,WAAQK,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,KAAI,CAAAJ,GAAEI,EAAC,IAAE,iBAAeN,MAAGE,GAAEI,EAAC,IAAEI,GAAEJ,EAAC,IAAE,OAAK,OAAKJ,GAAEI,EAAC,IAAE,MAAII,GAAEJ,EAAC,KAAG;AAAI,SAAM,oBAAkBG,KAAER,KAAEY,GAAEZ,IAAE,eAAe;AAAC;AAAC,SAASY,GAAER,IAAEC,IAAE,IAAE,cAAaG,KAAE,GAAE;AAAC,MAAG,CAACR,GAAEI,EAAC,EAAE,QAAOA;AAAE,QAAK,EAAC,QAAOL,IAAE,OAAMC,IAAE,QAAOC,GAAC,IAAEG,IAAEF,KAAEF,KAAEC,IAAEE,KAAEJ,GAAE,CAAC,GAAEO,KAAEP,GAAE,CAAC,GAAEY,KAAEP,GAAE,UAAU,WAAW,GAAG,IAAEA,GAAE,YAAU,OAAMQ,KAAED,GAAE,gBAAgBA,IAAET,EAAC,GAAEW,KAAEF,GAAE,gBAAgBA,IAAET,EAAC;AAAE,MAAIY,KAAE;AAAE,WAAQL,KAAE,GAAEA,KAAER,IAAEQ,KAAI,UAAQL,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,iBAAcC,MAAG,CAACO,GAAEE,EAAC,GAAED,GAAEC,EAAC,CAAC,IAAEP,GAAE,CAACJ,GAAEW,EAAC,GAAER,GAAEQ,EAAC,CAAC,GAAE,CAAC,GAAEF,GAAEE,EAAC,KAAGN,OAAI,CAACI,GAAEE,EAAC,GAAED,GAAEC,EAAC,CAAC,IAAEJ,GAAE,CAACP,GAAEW,EAAC,GAAER,GAAEQ,EAAC,CAAC,GAAE,CAAC,GAAEF,GAAEE,EAAC,KAAGN,IAAEK,GAAEC,EAAC,KAAGN,KAAGM;AAAI,QAAMC,KAAE,IAAIJ,GAAE,EAAC,WAAUA,IAAE,OAAMP,GAAE,OAAM,QAAOA,GAAE,QAAO,MAAKA,GAAE,MAAK,iBAAgBA,GAAE,iBAAgB,aAAYA,GAAE,aAAY,QAAO,CAACQ,IAAEC,EAAC,EAAC,CAAC;AAAE,SAAOE,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAASF,GAAET,IAAEC,IAAE,IAAE,GAAE;AAAC,MAAG,MAAI,KAAG,CAACL,GAAEI,EAAC,EAAE,QAAOA;AAAE,QAAMI,KAAEJ,GAAE,MAAM,GAAE,EAAC,QAAOK,IAAE,OAAMV,IAAE,QAAOC,GAAC,IAAEQ,IAAEP,KAAEQ,GAAE,CAAC,GAAEP,KAAEO,GAAE,CAAC;AAAE,MAAIN,KAAE;AAAE,WAAQ,IAAE,GAAE,IAAEH,IAAE,IAAI,UAAQI,KAAE,GAAEA,KAAEL,IAAEK,KAAI,iBAAcC,MAAGJ,GAAEE,EAAC,KAAG,GAAED,GAAEC,EAAC,KAAG,KAAGF,GAAEE,EAAC,KAAG,GAAEA;AAAI,SAAOK,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAASM,GAAEV,IAAE,GAAEI,IAAEC,IAAE,GAAE;AAAC,MAAG,EAAE,CAAC,KAAG,CAAC,EAAE,iBAAiB,OAAOL,GAAE,gBAAgB,EAAE,QAAM,EAAC,QAAOA,IAAE,OAAM,KAAK,MAAM,IAAEK,EAAC,GAAE,QAAO,KAAK,MAAMD,KAAEC,EAAC,GAAE,YAAWL,GAAE,QAAM,EAAC;AAAE,QAAML,KAAE,EAAE,MAAKC,KAAE,EAAE,MAAKC,MAAGG,GAAE,OAAKA,GAAE,QAAM,IAAEK,IAAEP,MAAGE,GAAE,OAAKA,GAAE,QAAMI,KAAEC,IAAEN,MAAGF,KAAEC,MAAG;AAAE,SAAOE,GAAE,OAAKL,KAAE,KAAK,OAAOK,GAAE,OAAKL,MAAGE,EAAC,IAAEA,IAAEG,GAAE,OAAKL,KAAE,KAAK,MAAMK,GAAE,OAAKL,MAAGE,EAAC,IAAEA,IAAEG,GAAE,OAAKJ,KAAE,KAAK,OAAOI,GAAE,OAAKJ,MAAGE,EAAC,IAAEA,IAAEE,GAAE,OAAKJ,KAAE,KAAK,MAAMI,GAAE,OAAKJ,MAAGE,EAAC,IAAEA,IAAE,EAAC,QAAOE,IAAE,OAAM,KAAK,MAAMA,GAAE,QAAMH,EAAC,GAAE,QAAO,KAAK,MAAMG,GAAE,SAAOF,EAAC,GAAE,YAAWC,GAAC;AAAC;AAAC,IAAMY,KAAEC,GAAE,GAAE,GAAE,CAAC;AAAE,SAASA,GAAEZ,KAAE,GAAEC,KAAE,GAAE,IAAE,KAAK,IAAGG,KAAE,MAAG;AAAC,EAAAA,OAAI,KAAG,IAAE,KAAK,KAAG,MAAI,IAAE,KAAK;AAAK,QAAMC,KAAED,KAAE,KAAG,GAAE,IAAE,KAAGC,IAAEV,KAAE,KAAGU,IAAET,KAAE,KAAGS,IAAER,KAAE,MAAIQ,IAAEP,KAAE,OAAM,CAACC,IAAEG,EAAC,IAAEW,GAAE,GAAEZ,KAAE,GAAE,GAAEH,EAAC,GAAE,CAACK,IAAEG,EAAC,IAAEO,GAAEb,KAAE,KAAIC,KAAEN,IAAE,GAAEG,EAAC,GAAE,CAACS,IAAEC,EAAC,IAAEK,GAAEb,KAAE,KAAIC,KAAEN,IAAE,GAAEG,EAAC,GAAE,CAACW,IAAEC,EAAC,IAAEG,GAAEb,KAAE,KAAIC,KAAEL,IAAE,GAAEE,EAAC,GAAE,CAACa,IAAEC,EAAC,IAAEC,GAAEb,KAAE,KAAIC,KAAEL,IAAE,GAAEE,EAAC,GAAE,CAACgB,IAAEC,EAAC,IAAEF,GAAEb,KAAE,KAAIC,KAAEJ,IAAE,GAAEC,EAAC,GAAE,CAACkB,IAAEC,EAAC,IAAEJ,GAAEb,KAAE,KAAIC,KAAEJ,IAAE,GAAEC,EAAC;AAAE,SAAM,CAACC,IAAEG,IAAEC,IAAEG,IAAEG,IAAEC,IAAEC,IAAEC,IAAEL,IAAEC,IAAEM,IAAEC,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAASH,GAAEd,KAAE,GAAEC,KAAE,KAAK,IAAG,IAAE,MAAG;AAAC,QAAIA,MAAG,IAAE,KAAK,KAAGA,OAAI,IAAE,KAAK;AAAK,QAAMG,KAAE,IAAGC,KAAE,IAAE,KAAG,GAAE,IAAE,IAAEA,IAAEV,KAAE,KAAGU,IAAET,KAAE,KAAGS,IAAEP,KAAE,IAAGC,KAAE,GAAEG,KAAE,GAAEC,KAAE,GAAEG,KAAE,GAAEC,KAAEJ,KAAEE,IAAEG,KAAE,IAAE,IAAE,IAAGC,KAAEL,KAAE,IAAEI;AAAE,MAAG,CAACE,IAAEC,EAAC,IAAE,CAACZ,KAAEU,IAAEP,KAAEP,EAAC,GAAE,CAACiB,IAAEE,EAAC,IAAE,CAACJ,KAAEP,KAAEK,IAAEG,EAAC,GAAE,CAACI,IAAEC,EAAC,IAAE,CAACJ,KAAEN,KAAEE,IAAEM,KAAEP,EAAC,GAAE,CAACU,IAAEC,EAAC,IAAE,CAACnB,KAAEU,IAAEP,KAAEN,EAAC,GAAE,CAACuB,IAAEC,EAAC,IAAE,CAACH,KAAEX,KAAEE,IAAEU,KAAEX,EAAC,GAAEc,KAAE,KAAK,KAAKrB,KAAEH,EAAC,GAAEyB,KAAE,KAAK,MAAMD,KAAE,EAAE;AAAE,EAAAA,MAAG,IAAEC;AAAE,QAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,WAAQ3B,KAAE,GAAEA,KAAEwB,KAAE,GAAExB,MAAIyB,MAAI;AAAC,IAAAA,MAAG,KAAGD,KAAE,KAAG,KAAGxB,QAAKwB,KAAE,KAAG,MAAIJ,KAAElB,IAAEoB,KAAEF,KAAEX,KAAEE,IAAEU,MAAGA,KAAEP,MAAG,GAAES,KAAEF,KAAEX;AAAG,UAAK,CAACP,IAAEyB,EAAC,IAAEZ,GAAEI,IAAEC,IAAEjB,IAAEH,EAAC;AAAE,QAAGwB,KAAE,GAAE;AAAC,YAAK,CAAClB,IAAEC,EAAC,IAAEQ,GAAED,IAAEM,IAAEjB,IAAEH,EAAC,GAAE,CAAC4B,IAAE/B,GAAC,IAAEkB,GAAEH,IAAEC,IAAEV,IAAEH,EAAC;AAAE,MAAAyB,GAAE,KAAKnB,EAAC,GAAEmB,GAAE,KAAKlB,EAAC,GAAEkB,GAAE,KAAKvB,EAAC,GAAEuB,GAAE,KAAKE,EAAC,GAAEF,GAAE,KAAKG,EAAC,GAAEH,GAAE,KAAK5B,GAAC;AAAA,IAAC,OAAK;AAAC,YAAK,CAACS,IAAEC,EAAC,IAAEQ,GAAED,IAAEE,IAAEb,IAAEH,EAAC,GAAE,CAAC4B,IAAE/B,GAAC,IAAEkB,GAAEE,IAAEC,IAAEf,IAAEH,EAAC,GAAE,CAACF,IAAEC,EAAC,IAAEgB,GAAEM,IAAEC,IAAEnB,IAAEH,EAAC;AAAE,MAAA0B,GAAE,KAAKxB,EAAC,GAAEwB,GAAE,KAAKC,EAAC,GAAED,GAAE,KAAK5B,EAAC,GAAE4B,GAAE,KAAK3B,EAAC,GAAE2B,GAAE,KAAKE,EAAC,GAAEF,GAAE,KAAK7B,GAAC,GAAE6B,GAAE,KAAKpB,EAAC,GAAEoB,GAAE,KAAKnB,EAAC;AAAA,IAAC;AAAC,IAAAa,MAAG,GAAEP,MAAG,GAAEG,MAAG,GAAEE,MAAG,GAAEI,MAAG;AAAA,EAAC;AAAC,QAAK,CAACO,IAAE,CAAC,IAAEd,GAAEd,KAAEU,IAAEP,KAAEP,IAAEM,IAAEH,EAAC,GAAE,KAAGM,KAAE,IAAED,MAAGK,IAAE,CAAC,GAAE,CAAC,IAAEK,GAAEd,KAAE,GAAEG,KAAEP,IAAEM,IAAEH,EAAC,GAAE,CAAC8B,IAAEC,EAAC,IAAEhB,GAAEd,KAAEU,IAAEP,KAAEN,IAAEK,IAAEH,EAAC,GAAE,CAACgC,IAAEC,EAAC,IAAElB,GAAEd,KAAE,GAAEG,KAAEN,IAAEK,IAAEH,EAAC;AAAE,SAAM,EAAC,UAASyB,IAAE,OAAMC,IAAE,OAAM,CAACG,IAAE,GAAE,GAAE,GAAEC,IAAEC,IAAEC,IAAEC,EAAC,EAAC;AAAC;AAAC,SAASlB,GAAEb,IAAEC,IAAE,GAAEG,KAAE,GAAE;AAAC,QAAMC,KAAE,KAAK,KAAKL,KAAEA,KAAEC,KAAEA,EAAC,IAAEG,IAAE,KAAG,IAAE,KAAK,KAAG,KAAK,MAAMH,IAAED,EAAC,MAAI,IAAE,KAAK;AAAI,SAAM,CAACK,KAAG,IAAE,KAAK,KAAG,IAAE,MAAI,IAAE,KAAK,GAAG;AAAC;AAAC,IAAMU,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAA3C,IAA6CC,KAAE,CAAC,GAAE,KAAG,GAAE,KAAI,CAAC;AAA5D,IAA8DC,KAAE,CAAC,GAAE,MAAI,KAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,CAAC;AAAE,SAASC,GAAElB,IAAEC,IAAE,GAAEG,IAAE;AAAC,QAAMC,KAAEN,GAAEK,MAAG,SAAQ,CAAC;AAAE,MAAI;AAAE,OAAI,IAAE,GAAE,IAAEH,GAAE,QAAO,IAAI,KAAG,MAAIA,GAAE,SAAO,GAAE;AAAC,QAAGD,KAAEC,GAAE,CAAC,IAAEI,GAAE;AAAA,EAAK,WAASL,MAAGC,GAAE,CAAC,IAAEI,GAAE;AAAM,SAAO,KAAK,IAAI,IAAE,GAAEJ,GAAE,SAAO,CAAC;AAAC;AAAC,SAASkB,GAAEnB,IAAEC,IAAE,GAAEG,IAAEC,IAAE;AAAC,MAAI,IAAE;AAAE,UAAOJ,IAAE;AAAA,IAAC,KAAI;AAAc,UAAEiB,GAAElB,IAAEe,IAAE,SAAQ,CAAC;AAAE;AAAA,IAAM,KAAI;AAAc,UAAEG,GAAElB,IAAEe,IAAE,sBAAqB,CAAC;AAAE;AAAA,IAAM,KAAI;AAAc,UAAEG,GAAElB,IAAEe,IAAE,mBAAkB,CAAC;AAAE;AAAA,IAAM,KAAI;AAAa,UAAEG,GAAElB,IAAEe,IAAE,oBAAmB,CAAC;AAAE;AAAA,IAAM,KAAI;AAAmB,UAAEG,GAAElB,IAAEK,MAAG,CAAC,GAAED,IAAE,CAAC;AAAE;AAAA,IAAM,KAAI;AAAkB,UAAEc,GAAElB,IAAEgB,IAAE,oBAAmB,CAAC;AAAE;AAAA,IAAM,KAAI;AAAmB,UAAEE,GAAElB,IAAEiB,IAAE,SAAQ,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAASG,GAAEpB,IAAEC,IAAE;AAAC,QAAK,EAAC,OAAM,GAAE,WAAUI,IAAE,YAAW,GAAE,aAAYV,GAAC,IAAEM,IAAEL,KAAEE,GAAE,SAASO,EAAC,GAAER,KAAEC,GAAE,SAAS,CAAC,GAAEC,KAAE,IAAE,GAAEG,KAAE;AAAG,MAAIC,KAAE,GAAEG,KAAE;AAAE,QAAK,EAAC,OAAMC,IAAE,QAAOC,IAAE,MAAKC,GAAC,IAAET,IAAEU,KAAEV,GAAE,OAAO,CAAC,GAAEc,KAAEd,GAAE,OAAO,CAAC,GAAEa,KAAE,EAAEJ,EAAC,IAAEA,GAAE,OAAQ,CAAAT,OAAGA,KAAE,CAAE,EAAE,SAAOO,KAAEC,IAAEO,KAAE,IAAI,aAAaF,KAAEd,EAAC,GAAEiB,KAAE,IAAI,YAAYd,KAAEW,EAAC,GAAEI,KAAEhB,GAAE,kBAAgBW,GAAE,GAAE,GAAE,GAAE,KAAE,IAAED;AAAE,WAAQP,KAAE,GAAEA,KAAEI,IAAEJ,KAAI,UAAQJ,KAAE,GAAEA,KAAEO,IAAEP,MAAI;AAAC,UAAMC,KAAEG,KAAEG,KAAEP;AAAE,QAAG,CAACS,MAAGA,GAAEL,KAAEG,KAAEP,EAAC,GAAE;AAAC,YAAMK,MAAGS,GAAEb,EAAC,IAAE,OAAK,MAAI,MAAI,KAAK,IAAGyB,KAAEP,GAAET,GAAET,EAAC,GAAE,GAAEL,IAAEC,IAAEF,EAAC;AAAE,eAAQ8B,KAAE,GAAEA,KAAER,GAAE,QAAOQ,MAAG,EAAE,CAAAV,GAAEZ,IAAG,KAAGH,KAAE,OAAIO,IAAEQ,GAAEZ,IAAG,KAAGC,KAAE,OAAII,IAAEO,GAAEZ,IAAG,IAAEc,GAAEQ,EAAC,GAAEV,GAAEZ,IAAG,IAAEc,GAAEQ,KAAE,CAAC,IAAEpB,IAAEU,GAAEZ,IAAG,IAAEuB,IAAEX,GAAEZ,IAAG,IAAEO,GAAET,EAAC;AAAE,YAAMH,KAAE,KAAGK,KAAEJ,KAAE;AAAG,MAAAiB,GAAEV,IAAG,IAAER,IAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE,GAAEkB,GAAEV,IAAG,IAAER,KAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,YAAWiB,IAAE,WAAUC,GAAC;AAAC;AAAC,IAAMK,KAAE,CAAC;AAAE,SAASC,GAAEtB,IAAEC,IAAE;AAAC,MAAG,MAAIoB,GAAE,OAAO,UAAQxB,KAAE,GAAEA,KAAE,IAAGA,KAAI,CAAAwB,GAAE,KAAKP,GAAE,IAAEjB,IAAE,GAAE,CAACI,GAAE,eAAe,CAAC;AAAE,QAAM,IAAEF,GAAED,GAAE,SAASG,GAAE,SAAS,GAAE,OAAO,GAAE,EAAC,OAAMG,IAAE,QAAOC,IAAE,MAAK,EAAC,IAAEL,IAAEL,KAAEK,GAAE,OAAO,CAAC,GAAEJ,KAAEI,GAAE,OAAO,CAAC,GAAEE,KAAE,GAAEC,KAAE,CAAC,GAAEG,KAAE,CAAC;AAAE,MAAIC,KAAE,GAAEC,KAAE;AAAE,WAAQV,KAAE,GAAEA,KAAEO,IAAEP,KAAI,UAAQE,KAAE,GAAEA,KAAEI,IAAEJ,MAAI;AAAC,UAAMC,KAAEH,KAAEM,KAAEJ,IAAED,KAAEJ,GAAEM,EAAC,IAAE;AAAE,SAAI,CAAC,KAAG,EAAEH,KAAEM,KAAEJ,EAAC,MAAID,MAAGF,IAAE;AAAC,YAAM4B,MAAG7B,GAAEK,EAAC,IAAE,OAAK,MAAI,MAAI,KAAK,IAAG,EAAC,UAASyB,IAAE,OAAM/B,KAAE,OAAME,GAAC,IAAEwB,GAAE,KAAK,IAAI,KAAK,MAAMtB,KAAE,CAAC,GAAE,EAAE,CAAC;AAAE,UAAG2B,GAAE,SAAO/B,IAAE,WAAS,EAAE;AAAS,UAAIc,KAAEN,GAAE,SAAOD;AAAE,YAAMQ,MAAGV,KAAE,OAAII,IAAEO,MAAGb,KAAE,OAAIO;AAAE,eAAQL,KAAE,GAAEA,KAAE0B,GAAE,QAAO1B,MAAG,EAAE,CAAAG,GAAEI,IAAG,IAAEG,IAAEP,GAAEI,IAAG,IAAEI,IAAER,GAAEI,IAAG,IAAEmB,GAAE1B,EAAC,GAAEG,GAAEI,IAAG,IAAEmB,GAAE1B,KAAE,CAAC,IAAEyB,IAAEtB,GAAEI,IAAG,IAAE,GAAEJ,GAAEI,IAAG,IAAER;AAAE,eAAQC,KAAE,GAAEA,KAAEL,IAAE,QAAOK,MAAG,EAAE,CAAAG,GAAEI,IAAG,IAAEG,IAAEP,GAAEI,IAAG,IAAEI,IAAER,GAAEI,IAAG,IAAEZ,IAAEK,EAAC,GAAEG,GAAEI,IAAG,IAAEZ,IAAEK,KAAE,CAAC,IAAEyB,IAAEtB,GAAEI,IAAG,IAAE,GAAEJ,GAAEI,IAAG,IAAER;AAAE,eAAQC,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAG,EAAE,CAAAG,GAAEI,IAAG,IAAEG,IAAEP,GAAEI,IAAG,IAAEI,IAAER,GAAEI,IAAG,IAAEV,GAAEG,EAAC,GAAEG,GAAEI,IAAG,IAAEV,GAAEG,KAAE,CAAC,IAAEyB,IAAEtB,GAAEI,IAAG,IAAE,GAAEJ,GAAEI,IAAG,IAAER;AAAE,eAAQC,KAAE,GAAEA,KAAE0B,GAAE,SAAO,GAAE1B,KAAI,CAAAM,GAAEE,IAAG,IAAEC,IAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEA,MAAG;AAAE,eAAQT,KAAE,GAAEA,KAAEL,IAAE,SAAO,GAAEK,KAAI,CAAAM,GAAEE,IAAG,IAAEC,IAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEA,MAAG;AAAE,MAAAH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEA,MAAG;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,YAAW,IAAI,aAAaN,EAAC,GAAE,WAAU,IAAI,YAAYG,EAAC,EAAC;AAAC;AAAC,SAASiB,GAAEvB,IAAEC,IAAE;AAAC,QAAM,IAAE,IAAE;AAAE,MAAIG,KAAE,GAAEC,KAAE;AAAE,QAAK,EAAC,OAAM,GAAE,QAAOV,IAAE,MAAKC,GAAC,IAAEI,IAAEE,KAAEF,GAAE,OAAO,CAAC,GAAEG,KAAE,CAAC,GAAEG,KAAE,CAAC,GAAEC,KAAER,GAAED,GAAE,SAASG,GAAE,SAAS,GAAE,OAAO,GAAEO,KAAE,iBAAeP,GAAE,QAAMJ,KAAE,OAAO;AAAU,WAAQA,KAAE,GAAEA,KAAEF,IAAEE,KAAI,UAAQG,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAMC,KAAEC,GAAEL,KAAE,IAAEG,EAAC,IAAEO;AAAE,SAAI,CAACX,MAAGA,GAAEC,KAAE,IAAEG,EAAC,MAAIC,KAAEO,IAAE;AAAC,eAAQiB,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAtB,GAAEC,IAAG,KAAGJ,KAAE,OAAI,GAAEG,GAAEC,IAAG,KAAGP,KAAE,OAAIF,IAAEQ,GAAEC,IAAG,IAAEqB,KAAE,IAAE,OAAI,KAAGtB,GAAEC,IAAG,IAAEqB,KAAE,KAAG,IAAE,OAAI,KAAGtB,GAAEC,IAAG,IAAE,GAAED,GAAEC,IAAG,IAAEH;AAAE,YAAML,KAAE,KAAGQ,KAAE,IAAE;AAAG,MAAAE,GAAED,IAAG,IAAET,IAAEU,GAAED,IAAG,IAAET,KAAE,GAAEU,GAAED,IAAG,IAAET,KAAE,GAAEU,GAAED,IAAG,IAAET,KAAE,GAAEU,GAAED,IAAG,IAAET,KAAE,GAAEU,GAAED,IAAG,IAAET,KAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,YAAW,IAAI,aAAaO,EAAC,GAAE,WAAU,IAAI,YAAYG,EAAC,EAAC;AAAC;AAAC,SAASkB,GAAExB,IAAEC,IAAE;AAAC,SAAM,oBAAkBA,GAAE,QAAMsB,GAAEvB,IAAEC,EAAC,IAAE,iBAAeA,GAAE,QAAMqB,GAAEtB,IAAEC,EAAC,IAAEmB,GAAEpB,IAAEC,EAAC;AAAC;AAAC,SAAS0B,GAAE3B,IAAEC,IAAE,GAAEG,KAAE,CAAC,GAAE,CAAC,GAAE,IAAE,KAAG;AAAC,QAAK,EAAC,OAAMT,IAAE,QAAOC,IAAE,MAAKC,GAAC,IAAEG,IAAE,CAACF,IAAEC,EAAC,IAAEC,GAAE,QAAO,CAACE,IAAEK,EAAC,IAAEH,IAAEI,KAAE,KAAK,OAAOb,KAAEO,MAAG,CAAC,GAAEO,KAAE,KAAK,OAAOb,KAAEW,MAAG,CAAC,GAAEG,KAAEF,KAAEC,IAAEE,KAAE,IAAI,aAAaD,EAAC,GAAEE,KAAE,IAAI,aAAaF,EAAC,GAAEI,KAAE,IAAI,WAAWJ,EAAC,GAAEG,KAAE,gBAAcZ;AAAE,WAAQI,KAAE,GAAEA,KAAEI,IAAEJ,KAAI,UAAQL,KAAE,GAAEA,KAAEQ,IAAER,MAAI;AAAC,QAAIC,KAAE;AAAE,UAAMG,KAAEC,KAAEG,KAAER,IAAES,KAAE,KAAK,IAAI,GAAEJ,KAAE,IAAEE,EAAC,GAAEG,KAAE,KAAK,IAAI,GAAEV,KAAE,IAAEE,EAAC,GAAEa,KAAE,KAAK,IAAInB,IAAEa,KAAE,CAAC,GAAEO,KAAE,KAAK,IAAIrB,IAAEe,KAAE,CAAC;AAAE,aAAQV,KAAES,IAAET,KAAEe,IAAEf,KAAI,UAAQyB,KAAEf,IAAEe,KAAET,IAAES,MAAI;AAAC,YAAMpB,KAAEL,KAAEL,KAAE8B;AAAE,UAAG,CAAC5B,MAAGA,GAAEQ,EAAC,GAAE;AAAC,QAAAJ;AAAI,cAAMD,KAAEa,KAAE,CAACf,GAAEO,EAAC,GAAEN,GAAEM,EAAC,CAAC,IAAE,CAACP,GAAEO,EAAC,IAAG,MAAIN,GAAEM,EAAC,KAAG,GAAG,GAAE,CAACoB,IAAEC,EAAC,IAAEb,KAAEb,KAAEM,GAAEN,EAAC;AAAE,QAAAW,GAAEP,EAAC,KAAGqB,IAAEb,GAAER,EAAC,KAAGsB;AAAA,MAAC;AAAA,IAAC;AAAC,QAAGzB,OAAIc,KAAEN,OAAIO,KAAEN,OAAI,IAAE,IAAG;AAAC,MAAAI,GAAEV,EAAC,IAAE;AAAE,YAAK,CAACJ,IAAEyB,EAAC,IAAEtB,GAAE,CAACQ,GAAEP,EAAC,IAAEH,IAAEW,GAAER,EAAC,IAAEH,EAAC,CAAC;AAAE,MAAAU,GAAEP,EAAC,IAAEJ,IAAEY,GAAER,EAAC,IAAEqB;AAAA,IAAC,MAAM,CAAAX,GAAEV,EAAC,IAAE,GAAEO,GAAEP,EAAC,IAAE,GAAEQ,GAAER,EAAC,IAAE;AAAA,EAAC;AAAC,QAAMW,KAAE,IAAIR,GAAE,EAAC,OAAMC,IAAE,QAAOC,IAAE,QAAO,CAACE,IAAEC,EAAC,GAAE,MAAKE,GAAC,CAAC;AAAE,SAAOC,GAAE,iBAAiB,GAAEA;AAAC;;;ACAv2N,IAAMiB,KAAE,EAAE,UAAU,qCAAqC;AAAzD,IAA2DC,KAAE;AAAG,eAAeC,GAAEC,IAAEC,IAAE,GAAEC,IAAE;AAAC,QAAM,IAAE,YAAY,IAAI,GAAEC,KAAEC,GAAEH,IAAE,CAAC,GAAEI,KAAE,YAAY,IAAI,GAAEN,KAAEO,GAAEL,IAAEE,IAAE,EAAE,OAAM,EAAE,MAAM,GAAEI,KAAE,YAAY,IAAI,GAAEC,KAAEC,GAAEV,IAAE,IAAE,GAAEW,KAAE,YAAY,IAAI,GAAEC,KAAE,kBAAgBX,KAAEY,GAAEJ,IAAEV,EAAC,IAAEe,GAAEL,EAAC,GAAEM,KAAE,YAAY,IAAI;AAAE,SAAO,IAAI,kBAAkB,MAAIjB,GAAE,KAAK,OAAM,iCAAgC,KAAK,MAAMQ,KAAE,CAAC,CAAC,GAAER,GAAE,KAAK,OAAM,wBAAuB,KAAK,MAAMU,KAAEF,EAAC,CAAC,GAAER,GAAE,KAAK,OAAM,gCAA+B,KAAK,MAAMa,KAAEH,EAAC,CAAC,GAAEV,GAAE,KAAK,OAAM,0CAAyC,KAAK,MAAMiB,KAAEJ,EAAC,CAAC,GAAEb,GAAE,KAAK,OAAM,uBAAsB,KAAK,MAAMiB,KAAE,CAAC,CAAC,GAAEjB,GAAE,KAAK,OAAM,qBAAoBc,GAAE,WAAW,OAAO,aAAWA,GAAE,UAAU,OAAO,UAAU,IAAG,MAAM,QAAQ,QAAQ,GAAE,EAAET,EAAC,GAAES;AAAC;AAAC,SAASP,GAAEJ,IAAEC,IAAE;AAAC,QAAM,IAAEO,GAAEP,GAAE,MAAKA,GAAE,OAAMA,GAAE,QAAOD,GAAE,SAAS;AAAE,MAAGA,GAAE,aAAY;AAAC,WAAM,CAACA,IAAEE,OAAI;AAAC,YAAMa,KAAE,KAAK,MAAMf,EAAC,GAAE,IAAE,KAAK,MAAME,EAAC;AAAE,UAAGa,KAAE,KAAGA,MAAGd,GAAE,MAAM,QAAM,CAAC,GAAE,CAAC;AAAE,UAAG,IAAE,KAAG,KAAGA,GAAE,OAAO,QAAM,CAAC,GAAE,CAAC;AAAE,YAAME,KAAEH,KAAEe,IAAEV,KAAEH,KAAE,GAAEL,KAAEkB,IAAEjB,KAAE,GAAEC,KAAEgB,KAAEd,GAAE,QAAM,IAAEc,KAAE,IAAEA,IAAEX,KAAE,IAAEH,GAAE,SAAO,IAAE,IAAE,IAAE,GAAEM,KAAE,EAAE,KAAGT,KAAEG,GAAE,QAAMJ,GAAE,GAAES,KAAE,EAAE,KAAGR,KAAEG,GAAE,QAAMF,GAAE,GAAES,KAAE,EAAE,KAAGJ,KAAEH,GAAE,QAAMJ,GAAE,GAAEY,KAAE,EAAE,KAAGL,KAAEH,GAAE,QAAMF,GAAE,GAAEa,KAAE,EAAE,KAAGd,KAAEG,GAAE,QAAMJ,MAAG,CAAC,GAAEgB,KAAE,EAAE,KAAGf,KAAEG,GAAE,QAAMF,MAAG,CAAC;AAAE,aAAM,EAAEQ,MAAG,IAAEF,MAAGG,KAAEH,OAAI,IAAEF,OAAIG,MAAG,IAAED,MAAGI,KAAEJ,MAAGF,KAAGS,MAAG,IAAEP,MAAG,EAAE,KAAGD,KAAEH,GAAE,QAAMJ,MAAG,CAAC,IAAEQ,OAAI,IAAEF,OAAIU,MAAG,IAAER,MAAG,EAAE,KAAGD,KAAEH,GAAE,QAAMF,MAAG,CAAC,IAAEM,MAAGF,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,CAACH,IAAEE,OAAI;AAAC,UAAMa,KAAE,KAAK,MAAMf,EAAC,GAAE,IAAE,KAAK,MAAME,EAAC;AAAE,WAAOa,KAAE,KAAGA,MAAGd,GAAE,SAAO,IAAE,KAAG,KAAGA,GAAE,SAAO,CAAC,GAAE,CAAC,IAAE,CAAC,EAAE,KAAG,IAAEA,GAAE,QAAMc,MAAG,CAAC,GAAE,EAAE,KAAG,IAAEd,GAAE,QAAMc,MAAG,CAAC,CAAC;AAAA,EAAC;AAAC;AAAC,SAASR,GAAEP,IAAEC,IAAE,GAAEC,IAAEa,IAAE,GAAEZ,IAAEE,IAAER,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,MAAIC,KAAE,GAAEK,KAAEF,IAAEK,KAAE,GAAE,CAACD,IAAEE,EAAC,IAAEP,GAAEF,IAAEK,EAAC;AAAE,EAAAE,MAAGN,GAAE,eAAcQ,MAAGR,GAAE;AAAc,QAAMS,KAAE,KAAK,KAAKH,KAAEA,KAAEE,KAAEA,EAAC;AAAE,MAAII,IAAEC;AAAE,EAAAf,GAAE,KAAK,EAAC,GAAEC,IAAE,GAAEK,IAAE,GAAEG,IAAE,OAAME,GAAC,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAEV,GAAE,iBAAgBU,MAAI;AAAC,QAAG,CAACM,IAAEd,EAAC,IAAED,GAAEF,IAAEK,EAAC;AAAE,IAAAY,MAAGhB,GAAE,eAAcE,MAAGF,GAAE;AAAc,UAAMM,KAAE,KAAK,KAAKU,KAAEA,KAAEd,KAAEA,EAAC;AAAE,QAAGI,KAAEN,GAAE,kBAAkB,QAAOF;AAAE,UAAMU,KAAEQ,KAAEV,IAAEG,KAAEP,KAAEI;AAAE,IAAAP,MAAGS,KAAER,GAAE,eAAcI,MAAGK,KAAET,GAAE;AAAc,QAAGO,MAAGP,GAAE,gBAAcM,IAAE,KAAK,KAAKE,KAAEI,KAAEH,KAAEI,EAAC,IAAEb,GAAE,aAAa,QAAOF;AAAE,QAAGE,GAAE,YAAW;AAAC,YAAMA,KAAE,KAAK,MAAMD,KAAEF,EAAC,GAAEI,KAAE,KAAK,MAAMG,KAAEP,EAAC;AAAE,UAAGG,KAAE,KAAGA,KAAEG,KAAE,KAAGF,KAAE,KAAGA,KAAEI,KAAE,EAAE,QAAOP;AAAE,YAAMkB,KAAE,EAAEf,KAAEE,KAAEH,EAAC;AAAE,UAAG,OAAKgB,MAAGA,OAAID,GAAE,QAAOjB;AAAE,QAAEG,KAAEE,KAAEH,EAAC,IAAEe;AAAA,IAAC;AAAC,IAAAjB,GAAE,KAAK,EAAC,GAAEC,IAAE,GAAEK,IAAE,GAAEG,IAAE,OAAMD,GAAC,CAAC,GAAEM,KAAEJ,IAAEK,KAAEJ;AAAA,EAAC;AAAC,SAAOX;AAAC;AAAC,SAASQ,GAAEN,IAAEC,IAAE,GAAEC,IAAE;AAAC,QAAMa,KAAE,CAAC,GAAEZ,KAAE,IAAIH,MAAEK,KAAE,IAAE,KAAK,IAAIL,GAAE,oBAAmB,CAAC,GAAEH,KAAE,KAAK,MAAM,IAAEQ,EAAC,GAAEP,KAAE,KAAK,MAAMI,KAAEG,EAAC,GAAEN,KAAE,IAAI,WAAWF,KAAEC,EAAC;AAAE,WAAQ,IAAE,GAAE,IAAEC,GAAE,QAAO,IAAI,CAAAA,GAAE,CAAC,IAAE;AAAG,QAAMK,KAAE,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAEF,IAAE,KAAGF,GAAE,YAAY,UAAQC,KAAE,GAAEA,KAAE,GAAEA,MAAGD,GAAE,YAAY,CAAAI,GAAE,KAAK,EAAC,GAAEH,IAAE,GAAE,GAAE,MAAKE,GAAE,SAAS,EAAC,CAAC;AAAE,EAAAC,GAAE,KAAM,CAACJ,IAAEC,OAAID,GAAE,OAAKC,GAAE,IAAK;AAAE,aAAS,EAAC,GAAE,GAAE,GAAEK,GAAC,KAAIF,GAAE,KAAGD,GAAE,SAAS,IAAEH,GAAE,SAAQ;AAAC,UAAMgB,KAAET,GAAEP,IAAEC,IAAE,GAAEK,IAAES,GAAE,QAAOhB,IAAEF,IAAEC,IAAEO,EAAC;AAAE,QAAGW,GAAE,SAAO,EAAE;AAAS,IAAAD,GAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAASP,GAAER,IAAEC,IAAE,GAAEC,IAAE;AAAC,MAAG,MAAIA,GAAE,QAAOF;AAAE,QAAMe,KAAE,KAAK,MAAM,IAAEb,EAAC,GAAE,IAAE,IAAI,MAAM,IAAEa,KAAE,CAAC;AAAE,MAAIZ,KAAE;AAAE,WAAQL,KAAE,CAACiB,IAAEjB,MAAGiB,IAAEjB,MAAI;AAAC,UAAME,KAAE,KAAK,IAAI,CAACF,KAAEA,MAAGI,KAAEA,GAAE;AAAE,MAAEJ,KAAEiB,EAAC,IAAEf,IAAEG,MAAGH;AAAA,EAAC;AAAC,WAAQF,KAAE,CAACiB,IAAEjB,MAAGiB,IAAEjB,KAAI,GAAEA,KAAEiB,EAAC,KAAGZ;AAAE,QAAME,KAAE,IAAI,aAAaL,GAAE,MAAM;AAAE,WAAQF,KAAE,GAAEA,KAAE,GAAEA,KAAI,UAAQkB,KAAE,GAAEA,KAAEf,IAAEe,MAAI;AAAC,QAAId,KAAE,GAAEC,KAAE;AAAE,aAAQE,KAAE,CAACU,IAAEV,MAAGU,IAAEV,MAAI;AAAC,UAAGW,KAAEX,KAAE,KAAGW,KAAEX,MAAGJ,GAAE;AAAS,YAAMJ,MAAE,EAAEQ,KAAEU,EAAC;AAAE,MAAAb,MAAGL,MAAEG,GAAE,KAAGF,KAAEG,MAAGe,KAAEX,OAAI,CAAC,GAAEF,MAAGN,MAAEG,GAAE,KAAGF,KAAEG,MAAGe,KAAEX,OAAI,CAAC;AAAA,IAAC;AAAC,IAAAA,GAAE,KAAGP,KAAEG,KAAEe,MAAG,CAAC,IAAEd,IAAEG,GAAE,KAAGP,KAAEG,KAAEe,MAAG,CAAC,IAAEb;AAAA,EAAC;AAAC,QAAMN,KAAE,IAAI,aAAaG,GAAE,MAAM;AAAE,WAAQF,KAAE,GAAEA,KAAEG,IAAEH,KAAI,UAAQE,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,QAAIE,KAAE,GAAEC,KAAE;AAAE,aAAQN,MAAE,CAACkB,IAAElB,OAAGkB,IAAElB,OAAI;AAAC,UAAGG,KAAEH,MAAE,KAAGG,KAAEH,OAAG,EAAE;AAAS,YAAME,KAAE,EAAEF,MAAEkB,EAAC;AAAE,MAAAb,MAAGH,KAAEM,GAAE,MAAIL,KAAEH,OAAGI,KAAEH,MAAG,CAAC,GAAEK,MAAGJ,KAAEM,GAAE,MAAIL,KAAEH,OAAGI,KAAEH,MAAG,CAAC;AAAA,IAAC;AAAC,IAAAD,GAAE,KAAGG,KAAEC,KAAEH,MAAG,CAAC,IAAEI,IAAEL,GAAE,KAAGG,KAAEC,KAAEH,MAAG,CAAC,IAAEK;AAAA,EAAC;AAAC,SAAON;AAAC;AAAC,SAASY,GAAET,IAAEC,IAAE;AAAC,QAAM,IAAE,IAAID,MAAEE,KAAEF,GAAE,OAAQ,CAACA,IAAEC,OAAID,KAAEC,GAAE,QAAQ,CAAC,GAAEc,KAAE,IAAI,aAAa,IAAEb,EAAC,GAAEC,KAAE,IAAI,MAAMH,GAAE,MAAM;AAAE,MAAIK,KAAE,GAAER,KAAE;AAAE,aAAU,KAAKG,IAAE;AAAC,UAAMA,KAAEK;AAAE,eAAUJ,MAAK,EAAE,CAAAc,GAAE,IAAEV,KAAE,CAAC,IAAEJ,GAAE,GAAEc,GAAE,IAAEV,KAAE,CAAC,IAAEJ,GAAE,GAAEc,GAAE,IAAEV,KAAE,CAAC,IAAEJ,GAAE,GAAEc,GAAE,IAAEV,KAAE,CAAC,IAAEJ,GAAE,OAAMI;AAAI,IAAAF,GAAEN,IAAG,IAAE,EAAC,aAAYG,IAAE,kBAAiB,EAAE,QAAO,WAAU,EAAE,EAAE,SAAO,CAAC,EAAE,GAAE,UAASC,KAAE,EAAE,SAAS,IAAE,EAAC;AAAA,EAAC;AAAC,SAAM,EAAC,cAAac,IAAE,iBAAgBZ,GAAC;AAAC;AAAC,SAASS,GAAEZ,IAAEC,IAAE;AAAC,QAAM,IAAE,GAAE,EAAC,cAAaC,IAAE,iBAAgBa,GAAC,IAAEf;AAAE,MAAI,IAAE,GAAEG,KAAE;AAAE,aAAUG,MAAKS,IAAE;AAAC,SAAG,IAAET,GAAE;AAAiB,IAAAH,MAAG,KAAGG,GAAE,mBAAiB;AAAA,EAAE;AAAC,QAAMD,KAAE,IAAI,aAAa,IAAE,CAAC,GAAER,KAAE,IAAI,YAAYM,EAAC;AAAE,MAAIL,KAAE,GAAEC,KAAE;AAAE,WAASK,KAAG;AAAC,IAAAP,GAAEE,IAAG,IAAED,KAAE,GAAED,GAAEE,IAAG,IAAED,IAAED,GAAEE,IAAG,IAAED,KAAE,GAAED,GAAEE,IAAG,IAAED,IAAED,GAAEE,IAAG,IAAED,KAAE,GAAED,GAAEE,IAAG,IAAED,KAAE;AAAA,EAAC;AAAC,WAASS,GAAEP,IAAEC,IAAEC,IAAEa,IAAEE,IAAEd,IAAEN,KAAEE,IAAE;AAAC,UAAMK,KAAEN,KAAE;AAAE,QAAIS,KAAE;AAAE,IAAAF,GAAED,KAAEG,IAAG,IAAEP,IAAEK,GAAED,KAAEG,IAAG,IAAEN,IAAEI,GAAED,KAAEG,IAAG,IAAE,GAAEF,GAAED,KAAEG,IAAG,IAAEL,IAAEG,GAAED,KAAEG,IAAG,IAAEJ,IAAEE,GAAED,KAAEG,IAAG,IAAEV,KAAEQ,GAAED,KAAEG,IAAG,IAAEQ,KAAE,GAAEV,GAAED,KAAEG,IAAG,IAAEU,KAAE,GAAEZ,GAAED,KAAEG,IAAG,IAAER,IAAED,MAAIO,GAAED,KAAEG,IAAG,IAAEP,IAAEK,GAAED,KAAEG,IAAG,IAAEN,IAAEI,GAAED,KAAEG,IAAG,IAAE,IAAGF,GAAED,KAAEG,IAAG,IAAEL,IAAEG,GAAED,KAAEG,IAAG,IAAEJ,IAAEE,GAAED,KAAEG,IAAG,IAAEV,KAAEQ,GAAED,KAAEG,IAAG,IAAE,CAACQ,KAAE,GAAEV,GAAED,KAAEG,IAAG,IAAE,CAACU,KAAE,GAAEZ,GAAED,KAAEG,IAAG,IAAER,IAAED;AAAA,EAAG;AAAC,aAAUQ,MAAKS,IAAE;AAAC,UAAK,EAAC,WAAUf,IAAE,UAASgB,GAAC,IAAEV;AAAE,QAAIS,KAAE,MAAKE,KAAE,MAAKd,KAAE,MAAKE,KAAE,MAAKR,MAAE,MAAKC,KAAE;AAAK,aAAQC,KAAE,GAAEA,KAAEO,GAAE,kBAAiBP,MAAI;AAAC,YAAMS,KAAEN,GAAE,KAAGI,GAAE,cAAYP,MAAG,CAAC,GAAEU,KAAEP,GAAE,KAAGI,GAAE,cAAYP,MAAG,CAAC,GAAEa,KAAEV,GAAE,KAAGI,GAAE,cAAYP,MAAG,CAAC,GAAEc,KAAEX,GAAE,KAAGI,GAAE,cAAYP,MAAG,CAAC;AAAE,UAAIW,KAAE,MAAKC,KAAE,MAAKG,KAAE,MAAKI,KAAE;AAAK,UAAGnB,KAAE,GAAE;AAAC,QAAAW,KAAEF,KAAEO,IAAEJ,KAAEF,KAAEQ;AAAE,cAAMf,KAAE,KAAK,KAAKQ,KAAEA,KAAEC,KAAEA,EAAC;AAAE,YAAGD,MAAGR,IAAES,MAAGT,IAAEH,KAAE,GAAE;AAAC,cAAIC,KAAEU,KAAEb,KAAEmB,KAAEL,KAAEb;AAAE,gBAAMI,KAAE,KAAK,KAAKF,KAAEA,KAAEgB,KAAEA,EAAC;AAAE,UAAAhB,MAAGE,IAAEc,MAAGd;AAAE,gBAAMa,KAAE,KAAK,IAAI,KAAGf,KAAEU,KAAEM,KAAEL,KAAGV,EAAC;AAAE,UAAAD,MAAGe,IAAEC,MAAGD,IAAED,KAAE,CAACE,IAAEE,KAAElB;AAAA,QAAC,MAAM,CAAAc,KAAE,CAACH,IAAEO,KAAER;AAAE,iBAAOI,MAAG,SAAOI,OAAIX,GAAEQ,IAAEE,IAAEd,IAAEW,IAAEI,IAAElB,IAAEgB,IAAEH,EAAC,GAAET,GAAE;AAAA,MAAE;AAAC,MAAAW,KAAEP,IAAES,KAAER,IAAEN,KAAES,IAAEf,MAAEa,IAAEZ,KAAEa,IAAEN,KAAEQ;AAAA,IAAC;AAAC,IAAAN,GAAEQ,IAAEE,IAAEd,IAAE,CAACL,IAAED,KAAEG,IAAEgB,IAAEX,EAAC;AAAA,EAAC;AAAC,SAAM,EAAC,YAAWA,IAAE,WAAUR,GAAC;AAAC;AAAC,SAASgB,GAAEb,IAAE;AAAC,QAAMC,KAAE,IAAG,IAAE,GAAEC,KAAE,GAAE,EAAC,cAAaa,IAAE,iBAAgB,EAAC,IAAEf;AAAE,MAAIG,KAAE,GAAEE,KAAE;AAAE,aAAUc,MAAK,GAAE;AAAC,UAAMnB,KAAEmB,GAAE,mBAAiB;AAAE,IAAAhB,MAAG,IAAEH,KAAE,GAAEK,MAAG,IAAEL,KAAE;AAAA,EAAC;AAAC,QAAMH,KAAE,IAAI,aAAaM,KAAEF,EAAC,GAAEH,KAAE,IAAI,YAAYO,EAAC;AAAE,MAAIN,IAAEK,IAAEG,IAAED,IAAEE,IAAEC,IAAEG,IAAEC,IAAEH,IAAEC,IAAEG,IAAEI,IAAEE,IAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAASC,KAAG;AAAC,IAAAzB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE,GAAEvB,GAAEwB,IAAG,IAAED,KAAE;AAAA,EAAC;AAAC,WAASG,GAAExB,IAAEe,IAAEE,IAAEd,IAAEE,IAAEP,IAAEC,IAAEK,IAAEG,IAAED,IAAEE,IAAEC,IAAEG,IAAEC,IAAE;AAAC,UAAMH,KAAEW,KAAEpB;AAAE,QAAIU,KAAE;AAAE,eAAUV,MAAI,CAAC,GAAEC,EAAC,EAAE,YAAUc,MAAI,CAAC,GAAE,GAAE,GAAE,CAAC,EAAE,CAAAnB,GAAEa,KAAEC,IAAG,IAAEX,IAAEH,GAAEa,KAAEC,IAAG,IAAEI,IAAElB,GAAEa,KAAEC,IAAG,IAAEM,IAAEpB,GAAEa,KAAEC,IAAG,IAAER,IAAEN,GAAEa,KAAEC,IAAG,IAAEZ,IAAEF,GAAEa,KAAEC,IAAG,IAAEP,IAAEP,GAAEa,KAAEC,IAAG,IAAEJ,IAAEV,GAAEa,KAAEC,IAAG,IAAEL,IAAET,GAAEa,KAAEC,IAAG,IAAEV,IAAEJ,GAAEa,KAAEC,IAAG,IAAEK,IAAEnB,GAAEa,KAAEC,IAAG,IAAEC,IAAEf,GAAEa,KAAEC,IAAG,IAAEE,IAAEhB,GAAEa,KAAEC,IAAG,IAAEN,KAAE,GAAER,GAAEa,KAAEC,IAAG,IAAEb,KAAE,GAAED,GAAEa,KAAEC,IAAG,IAAEH,KAAE,GAAEX,GAAEa,KAAEC,IAAG,IAAEF,KAAE,GAAEY;AAAA,EAAG;AAAC,WAASI,GAAEzB,IAAEC,IAAE;AAAC,QAAIe,KAAEN,KAAEI,IAAEZ,KAAES,KAAEO;AAAE,UAAMH,KAAE,KAAK,KAAKC,KAAEA,KAAEd,KAAEA,EAAC;AAAE,IAAAc,MAAGD,IAAEb,MAAGa;AAAE,UAAME,KAAEP,KAAEM,KAAEL,KAAET;AAAE,IAAAc,MAAGC,IAAEf,MAAGe;AAAE,QAAId,KAAEW,KAAEM,IAAEf,KAAEa,KAAE;AAAE,UAAMrB,MAAE,KAAK,KAAKM,KAAEA,KAAEE,KAAEA,EAAC;AAAE,IAAAF,MAAGN,KAAEQ,MAAGR;AAAE,UAAMC,KAAEgB,KAAEX,KAAEe,KAAEb;AAAE,IAAAF,MAAGL,IAAEO,MAAGP,IAAE0B,GAAEzB,IAAEK,IAAEG,IAAED,IAAE,CAACJ,IAAEc,IAAER,IAAEC,IAAEG,IAAEC,IAAE,CAACR,IAAEF,IAAEH,IAAEC,EAAC,GAAEsB,GAAE;AAAA,EAAC;AAAC,WAASG,GAAE1B,IAAEC,IAAEe,IAAEd,IAAEa,IAAEE,IAAE;AAAC,QAAGP,KAAEI,IAAEH,KAAEO,IAAEJ,KAAEM,IAAEF,KAAE,GAAE,QAAMR,MAAG,QAAMC,OAAID,KAAEI,IAAEH,KAAEO,KAAG,QAAMV,MAAG,QAAMC,IAAE;AAAC,MAAAW,KAAEpB,KAAEQ,IAAE,IAAEP,KAAEQ;AAAE,YAAMO,KAAE,KAAK,KAAKI,KAAEA,KAAE,IAAE,CAAC;AAAE,MAAAA,MAAGJ,IAAE,KAAGA;AAAA,IAAC;AAAC,YAAMN,MAAG,QAAMC,MAAGc,GAAEV,IAAEE,EAAC,GAAElB,KAAES,IAAEJ,KAAEK,IAAEF,KAAEK,IAAEN,KAAEO,IAAEL,KAAER,IAAES,KAAER,IAAEW,KAAEI,IAAEH,KAAEX;AAAA,EAAC;AAAC,WAAS,EAAEF,IAAEC,IAAE;AAAC,IAAAS,KAAEI,IAAEH,KAAEO,IAAEJ,KAAEM,IAAEF,KAAE,GAAE,QAAMR,MAAG,QAAMC,OAAID,KAAEI,IAAEH,KAAEO,KAAG,QAAMR,MAAG,QAAMC,MAAGc,GAAEzB,IAAEC,EAAC;AAAA,EAAC;AAAC,aAAUkB,MAAK,GAAE;AAAC,IAAApB,KAAE,MAAKK,KAAE,MAAKG,KAAE,MAAKD,KAAE,MAAKE,KAAE,MAAKC,KAAE,MAAKG,KAAE,MAAKC,KAAE,MAAKH,KAAE,MAAKC,KAAE,MAAKG,KAAE,MAAKI,KAAE,MAAKE,KAAE,MAAK,IAAE;AAAK,UAAK,EAAC,WAAUpB,IAAE,UAASC,GAAC,IAAEkB;AAAE,aAAQH,KAAE,GAAEA,KAAEG,GAAE,kBAAiBH,MAAI;AAAC,MAAAU,GAAEX,GAAE,KAAGI,GAAE,cAAYH,MAAG,CAAC,GAAED,GAAE,KAAGI,GAAE,cAAYH,MAAG,CAAC,GAAED,GAAE,KAAGI,GAAE,cAAYH,MAAG,CAAC,GAAED,GAAE,KAAGI,GAAE,cAAYH,MAAG,CAAC,GAAEhB,IAAEC,EAAC;AAAA,IAAC;AAAC,MAAED,IAAEC,EAAC;AAAA,EAAC;AAAC,SAAM,EAAC,YAAWJ,IAAE,WAAUC,GAAC;AAAC;AAAC,SAASY,GAAEV,IAAE,GAAE;AAAC,QAAME,KAAE,EAAE,QAAO,EAAC,OAAMa,IAAE,QAAO,EAAC,IAAE,GAAEZ,KAAE,IAAI,aAAaY,KAAE,IAAE,CAAC,GAAEV,KAAE,EAAE,QAAM,IAAI,WAAWU,KAAE,IAAE,CAAC;AAAE,MAAG,EAAE,QAAMV,GAAE,KAAK,GAAG,GAAE,gBAAcL,GAAE,UAAQC,KAAE,GAAEA,KAAEc,KAAE,GAAEd,KAAI,CAAAE,GAAE,IAAEF,KAAE,CAAC,IAAEC,GAAE,CAAC,EAAED,EAAC,GAAEE,GAAE,IAAEF,KAAE,CAAC,IAAE,CAACC,GAAE,CAAC,EAAED,EAAC;AAAA,WAAU,oBAAkBD,GAAE,UAAQH,KAAE,GAAEA,KAAEkB,KAAE,GAAElB,MAAI;AAAC,UAAMG,KAAEE,GAAE,CAAC,EAAEL,EAAC,GAAEmB,KAAE,EAAEd,GAAE,CAAC,EAAEL,EAAC,CAAC,GAAEkB,KAAE,KAAK,IAAIC,KAAE,KAAK,KAAG,CAAC,GAAEC,KAAE,KAAK,IAAID,KAAE,KAAK,KAAG,CAAC;AAAE,IAAAb,GAAE,IAAEN,KAAE,CAAC,IAAEkB,KAAEf,IAAEG,GAAE,IAAEN,KAAE,CAAC,IAAEoB,KAAEjB;AAAA,EAAC;AAAC,SAAM,EAAC,MAAKG,IAAE,MAAKE,IAAE,OAAMU,IAAE,QAAO,EAAC;AAAC;AAAC,eAAeJ,GAAEX,IAAEC,IAAE,GAAEC,IAAEa,IAAE,GAAE;AAAC,QAAMjB,KAAE,YAAY,IAAI,GAAEC,KAAE,EAAEE,GAAE,gBAAgB;AAAE,MAAG,CAACF,IAAE;AAAC,UAAMI,KAAE,MAAMW,GAAEd,IAAEC,IAAE,GAAEC,IAAEa,IAAE,CAAC;AAAE,WAAO,IAAI,kBAAkB,KAAGlB,GAAE,KAAK,OAAM,gCAA+B,KAAK,MAAM,YAAY,IAAI,IAAEC,EAAC,CAAC,GAAE,IAAI,kBAAkB,KAAGD,GAAE,KAAK,OAAM,mBAAkB,CAAC,GAAEM;AAAA,EAAC;AAAC,QAAK,CAACC,IAAEG,EAAC,IAAER,GAAE,OAAMO,KAAEC,KAAEH,IAAEI,KAAE,KAAK,KAAKP,GAAE,QAAMK,EAAC,GAAEG,KAAER,GAAE,QAAMO,IAAEI,KAAE,KAAK,MAAM,IAAEJ,EAAC;AAAE,MAAIK,KAAEZ,GAAE;AAAK,QAAMS,KAAE,CAAC,GAAEC,KAAE,YAAY,IAAI;AAAE,WAAQR,KAAE,GAAEA,KAAEK,IAAEL,MAAI;AAAC,UAAMa,KAAE,IAAI,EAAE,EAAC,MAAKH,IAAE,MAAKA,KAAEJ,IAAE,MAAKR,GAAE,MAAK,MAAKA,GAAE,MAAK,kBAAiBA,GAAE,iBAAgB,CAAC;AAAE,IAAAS,GAAE,KAAKI,GAAEd,IAAEgB,IAAEJ,IAAEV,IAAEa,IAAE,CAAC,CAAC,GAAEF,MAAGJ;AAAA,EAAC;AAAC,QAAMS,KAAE,MAAM,QAAQ,IAAIR,EAAC;AAAE,MAAI,kBAAkB,KAAGb,GAAE,KAAK,OAAM,gCAA+B,KAAK,MAAM,YAAY,IAAI,IAAEc,EAAC,CAAC,GAAE,IAAI,kBAAkB,KAAGd,GAAE,KAAK,OAAM,mBAAkBqB,GAAE,MAAM;AAAE,QAAME,KAAE,EAAC,MAAK,IAAI,aAAa,IAAElB,KAAE,CAAC,GAAE,MAAK,IAAI,WAAW,IAAEA,EAAC,GAAE,OAAM,GAAE,QAAOA,GAAC;AAAE,MAAI,IAAE;AAAE,aAAUC,MAAKe,IAAE;AAAC,aAAQlB,KAAE,GAAEA,KAAEG,GAAE,QAAOH,KAAI,UAAQC,KAAE,GAAEA,KAAEE,GAAE,OAAMF,KAAI,KAAEA,MAAG,MAAImB,GAAE,KAAK,KAAGpB,KAAE,IAAE,IAAEC,MAAG,CAAC,IAAEE,GAAE,KAAK,KAAGH,KAAEG,GAAE,QAAMF,MAAG,CAAC,GAAEmB,GAAE,KAAK,KAAGpB,KAAE,IAAE,IAAEC,MAAG,CAAC,IAAEE,GAAE,KAAK,KAAGH,KAAEG,GAAE,QAAMF,MAAG,CAAC,GAAEmB,GAAE,KAAKpB,KAAE,IAAE,IAAEC,EAAC,IAAEE,GAAE,KAAKH,KAAEG,GAAE,QAAMF,EAAC;AAAG,SAAGE,GAAE;AAAA,EAAK;AAAC,SAAO,IAAI,kBAAkB,KAAGN,GAAE,KAAK,QAAO,kCAAiC,KAAK,MAAM,YAAY,IAAI,IAAEC,EAAC,CAAC,GAAEsB;AAAC;AAAC,eAAeN,GAAEd,IAAEC,IAAEc,IAAE,GAAEZ,IAAEE,IAAE;AAAC,QAAMR,KAAE,EAAC,iCAAgC,MAAG,QAAOQ,GAAC;AAAE,MAAG,EAAEF,EAAC,MAAIN,GAAE,aAAWM,KAAG,cAAYH,GAAE,MAAK;AAAC,UAAMA,GAAE,KAAK,EAAC,QAAOK,GAAC,CAAC;AAAE,UAAMH,KAAEF,GAAE,WAAW,UAASG,KAAE,MAAMH,GAAE,WAAWC,IAAEc,IAAE,GAAElB,EAAC;AAAE,WAAM,CAACM,MAAG,EAAEA,GAAE,SAAS,KAAG,EAAEA,GAAE,UAAU,UAAU,IAAE,EAAC,MAAK,IAAI,aAAaY,KAAE,IAAE,CAAC,GAAE,MAAK,IAAI,WAAWA,KAAE,CAAC,GAAE,OAAMA,IAAE,QAAO,EAAC,IAAEL,GAAER,IAAEC,GAAE,UAAU,UAAU;AAAA,EAAC;AAAC,QAAMH,GAAE,KAAK,EAAC,QAAOK,GAAC,CAAC;AAAE,QAAMP,KAAEE,GAAE,WAAW,UAASD,KAAE,MAAMC,GAAE,YAAYC,IAAEc,IAAE,GAAElB,EAAC;AAAE,SAAM,CAACE,MAAG,EAAEA,GAAE,UAAU,IAAE,EAAC,MAAK,IAAI,aAAagB,KAAE,IAAE,CAAC,GAAE,MAAK,IAAI,WAAWA,KAAE,CAAC,GAAE,OAAMA,IAAE,QAAO,EAAC,IAAEL,GAAEZ,IAAEC,GAAE,UAAU;AAAC;", "names": ["l", "a", "t", "t", "e", "s", "l", "r", "o", "a", "h", "p", "c", "u", "g", "m", "d", "f", "s", "t", "r", "a", "e", "m", "f", "o", "h", "w", "c", "u", "l", "p", "x", "g", "d", "y", "k", "M", "n", "A", "U", "T", "i", "b", "S", "R", "C", "P", "B", "s", "a", "h", "c", "l", "t", "e", "u", "f", "r", "o", "p", "m", "d", "g", "x", "M", "k", "y", "w", "P", "b", "v", "I", "A", "_", "U", "S", "D", "F", "n", "i", "j", "B", "E", "T", "C", "s", "c", "f", "t", "e", "r", "l", "u", "a", "m", "h", "d", "w", "y", "x", "p", "g", "M", "o", "n", "i", "A", "j", "I", "F", "D", "b", "v", "S", "k"]}