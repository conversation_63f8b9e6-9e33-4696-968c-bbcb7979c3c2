-- 为采样记录表添加type字段的迁移脚本
-- 用于区分不同类型的采样记录（如：1=供水厂，2=污水厂等）

-- 检查字段是否已存在，如果不存在则添加
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'tb_water_sampling' 
        AND column_name = 'type'
    ) THEN
        ALTER TABLE tb_water_sampling ADD COLUMN type VARCHAR(50);
        
        -- 为现有记录设置默认值（可根据实际需求调整）
        -- 这里暂时设置为NULL，可以后续根据业务需求更新
        COMMENT ON COLUMN tb_water_sampling.type IS '采样记录类型：1=供水厂，2=污水厂';
        
        -- 可选：为现有数据设置默认类型
        -- UPDATE tb_water_sampling SET type = '1' WHERE type IS NULL;
        
    END IF;
END $$;
