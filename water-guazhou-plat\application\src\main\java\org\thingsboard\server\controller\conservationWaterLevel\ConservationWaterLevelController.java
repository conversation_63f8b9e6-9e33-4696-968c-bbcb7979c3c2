package org.thingsboard.server.controller.conservationWaterLevel;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationAnalysis;
import org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationWaterLevel;
import org.thingsboard.server.service.conservationWaterLevel.ConservationWaterLevelService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 涵养水位控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/conservation-water-level")
@Api(tags = "涵养水位管理")
public class ConservationWaterLevelController extends BaseController {

    @Autowired
    private ConservationWaterLevelService conservationWaterLevelService;

    @ApiOperation(value = "保存涵养水位数据")
    @PostMapping("/water-level")
    public IstarResponse saveWaterLevel(@RequestBody ConservationWaterLevel entity) {
        try {
            entity.setTenantId(getCurrentUser().getTenantId().toString());
            entity.setCreator(getCurrentUser().getId().toString());
            ConservationWaterLevel result = conservationWaterLevelService.saveWaterLevel(entity);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("保存涵养水位数据失败", e);
            return IstarResponse.error("保存涵养水位数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新涵养水位数据")
    @PutMapping("/water-level")
    public IstarResponse updateWaterLevel(@RequestBody ConservationWaterLevel entity) {
        try {
            ConservationWaterLevel result = conservationWaterLevelService.updateWaterLevel(entity);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("更新涵养水位数据失败", e);
            return IstarResponse.error("更新涵养水位数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除涵养水位数据")
    @DeleteMapping("/water-level/{id}")
    public IstarResponse deleteWaterLevel(@PathVariable String id) {
        try {
            conservationWaterLevelService.deleteWaterLevel(id);
            return IstarResponse.ok();
        } catch (Exception e) {
            log.error("删除涵养水位数据失败", e);
            return IstarResponse.error("删除涵养水位数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取涵养水位数据详情")
    @GetMapping("/water-level/{id}")
    public IstarResponse getWaterLevelById(@PathVariable String id) {
        try {
            ConservationWaterLevel result = conservationWaterLevelService.getWaterLevelById(id);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取涵养水位数据详情失败", e);
            return IstarResponse.error("获取涵养水位数据详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询涵养水位数据")
    @GetMapping("/water-level/list")
    public IstarResponse getWaterLevelList(
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "测点ID") @RequestParam(required = false) String stationId,
            @ApiParam(value = "测点名称") @RequestParam(required = false) String stationName,
            @ApiParam(value = "数据来源") @RequestParam(required = false) Integer dataSource,
            @ApiParam(value = "开始时间") @RequestParam(required = false) Long startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) Long endTime,
            @ApiParam(value = "创建人") @RequestParam(required = false) String creator) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);
            params.put("tenantId", getCurrentUser().getTenantId().toString());
            params.put("stationId", stationId);
            params.put("stationName", stationName);
            params.put("dataSource", dataSource);
            params.put("creator", creator);
            
            if (startTime != null) {
                params.put("startTime", new Date(startTime));
            }
            if (endTime != null) {
                params.put("endTime", new Date(endTime));
            }

            Map<String, Object> result = conservationWaterLevelService.getWaterLevelList(params);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("分页查询涵养水位数据失败", e);
            return IstarResponse.error("分页查询涵养水位数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取水位变化数据")
    @GetMapping("/water-level/change-data")
    public IstarResponse getWaterLevelChangeData(
            @ApiParam(value = "测点ID") @RequestParam String stationId,
            @ApiParam(value = "开始时间") @RequestParam Long startTime,
            @ApiParam(value = "结束时间") @RequestParam Long endTime) {
        try {
            List<ConservationWaterLevel> data = conservationWaterLevelService.getWaterLevelChangeData(
                    stationId,
                    new Date(startTime),
                    new Date(endTime)
            );
            return IstarResponse.ok(data);
        } catch (Exception e) {
            log.error("获取水位变化数据失败", e);
            return IstarResponse.error("获取水位变化数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取测点最新水位数据")
    @GetMapping("/water-level/latest/{stationId}")
    public IstarResponse getLatestWaterLevel(@PathVariable String stationId) {
        try {
            ConservationWaterLevel result = conservationWaterLevelService.getLatestWaterLevel(stationId);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取测点最新水位数据失败", e);
            return IstarResponse.error("获取测点最新水位数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取统计数据")
    @GetMapping("/water-level/statistics")
    public IstarResponse getStatisticsData(
            @ApiParam(value = "测点ID") @RequestParam String stationId,
            @ApiParam(value = "开始时间") @RequestParam Long startTime,
            @ApiParam(value = "结束时间") @RequestParam Long endTime) {
        try {
            Map<String, Object> result = conservationWaterLevelService.getStatisticsData(
                    stationId,
                    new Date(startTime),
                    new Date(endTime)
            );
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return IstarResponse.error("获取统计数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "批量导入水位数据")
    @PostMapping("/water-level/batch-import")
    public IstarResponse batchImportWaterLevel(@RequestBody List<ConservationWaterLevel> list) {
        try {
            String tenantId = getCurrentUser().getTenantId().toString();
            String creator = getCurrentUser().getId().toString();
            
            for (ConservationWaterLevel entity : list) {
                entity.setTenantId(tenantId);
                entity.setCreator(creator);
            }
            
            conservationWaterLevelService.batchImportWaterLevel(list);
            return IstarResponse.ok();
        } catch (Exception e) {
            log.error("批量导入水位数据失败", e);
            return IstarResponse.error("批量导入水位数据失败: " + e.getMessage());
        }
    }

    // ==================== 智能分析相关接口 ====================

    @ApiOperation(value = "执行智能分析")
    @PostMapping("/analysis/perform")
    public IstarResponse performIntelligentAnalysis(
            @ApiParam(value = "测点ID") @RequestParam String stationId,
            @ApiParam(value = "开始时间") @RequestParam Long startTime,
            @ApiParam(value = "结束时间") @RequestParam Long endTime) {
        try {
            String creator = getCurrentUser().getId().toString();
            ConservationAnalysis result = conservationWaterLevelService.performIntelligentAnalysis(
                    stationId,
                    new Date(startTime),
                    new Date(endTime),
                    creator
            );
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("执行智能分析失败", e);
            return IstarResponse.error("执行智能分析失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询分析结果")
    @GetMapping("/analysis/list")
    public IstarResponse getAnalysisList(
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "测点ID") @RequestParam(required = false) String stationId,
            @ApiParam(value = "测点名称") @RequestParam(required = false) String stationName,
            @ApiParam(value = "分析状态") @RequestParam(required = false) Integer status,
            @ApiParam(value = "风险等级") @RequestParam(required = false) Integer riskLevel,
            @ApiParam(value = "开始时间") @RequestParam(required = false) Long startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) Long endTime,
            @ApiParam(value = "创建人") @RequestParam(required = false) String creator) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);
            params.put("tenantId", getCurrentUser().getTenantId().toString());
            params.put("stationId", stationId);
            params.put("stationName", stationName);
            params.put("status", status);
            params.put("riskLevel", riskLevel);
            params.put("creator", creator);

            if (startTime != null) {
                params.put("startTime", new Date(startTime));
            }
            if (endTime != null) {
                params.put("endTime", new Date(endTime));
            }

            Map<String, Object> result = conservationWaterLevelService.getAnalysisList(params);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("分页查询分析结果失败", e);
            return IstarResponse.error("分页查询分析结果失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取分析结果详情")
    @GetMapping("/analysis/{id}")
    public IstarResponse getAnalysisById(@PathVariable String id) {
        try {
            ConservationAnalysis result = conservationWaterLevelService.getAnalysisById(id);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取分析结果详情失败", e);
            return IstarResponse.error("获取分析结果详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取测点最新分析结果")
    @GetMapping("/analysis/latest/{stationId}")
    public IstarResponse getLatestAnalysis(@PathVariable String stationId) {
        try {
            ConservationAnalysis result = conservationWaterLevelService.getLatestAnalysis(stationId);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取测点最新分析结果失败", e);
            return IstarResponse.error("获取测点最新分析结果失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取风险等级统计")
    @GetMapping("/analysis/risk-statistics")
    public IstarResponse getRiskLevelStatistics() {
        try {
            String tenantId = getCurrentUser().getTenantId().toString();
            List<Map<String, Object>> result = conservationWaterLevelService.getRiskLevelStatistics(tenantId);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取风险等级统计失败", e);
            return IstarResponse.error("获取风险等级统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取涵养潜力趋势")
    @GetMapping("/analysis/potential-trend")
    public IstarResponse getConservationPotentialTrend(
            @ApiParam(value = "测点ID") @RequestParam String stationId,
            @ApiParam(value = "开始时间") @RequestParam Long startTime,
            @ApiParam(value = "结束时间") @RequestParam Long endTime) {
        try {
            List<Map<String, Object>> result = conservationWaterLevelService.getConservationPotentialTrend(
                    stationId,
                    new Date(startTime),
                    new Date(endTime)
            );
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取涵养潜力趋势失败", e);
            return IstarResponse.error("获取涵养潜力趋势失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "重新分析")
    @PostMapping("/analysis/re-analysis/{analysisId}")
    public IstarResponse reAnalysis(@PathVariable String analysisId) {
        try {
            String creator = getCurrentUser().getId().toString();
            ConservationAnalysis result = conservationWaterLevelService.reAnalysis(analysisId, creator);
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("重新分析失败", e);
            return IstarResponse.error("重新分析失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除分析结果")
    @DeleteMapping("/analysis/{id}")
    public IstarResponse deleteAnalysis(@PathVariable String id) {
        try {
            conservationWaterLevelService.deleteAnalysis(id);
            return IstarResponse.ok();
        } catch (Exception e) {
            log.error("删除分析结果失败", e);
            return IstarResponse.error("删除分析结果失败: " + e.getMessage());
        }
    }
}
