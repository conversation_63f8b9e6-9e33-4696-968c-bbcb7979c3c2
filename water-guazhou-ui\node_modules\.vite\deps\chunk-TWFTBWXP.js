import {
  b,
  c,
  j as j2
} from "./chunk-QC5SLERR.js";
import {
  a as a3
} from "./chunk-WFXIWNQB.js";
import {
  m
} from "./chunk-PNIF6I3E.js";
import {
  F,
  M,
  S,
  j3 as j
} from "./chunk-ETY52UBV.js";
import {
  S as S2,
  w
} from "./chunk-VNYCO3JG.js";
import {
  n,
  r as r3
} from "./chunk-NVZMGX2J.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  a as a2
} from "./chunk-QMG7GZIF.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s as s3
} from "./chunk-KUPAGB4V.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  t2 as t,
  u,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T
} from "./chunk-HP475EI3.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/renderers/Renderer.js
var n2 = new s3({ simple: "simple", uniqueValue: "unique-value", classBreaks: "class-breaks", heatmap: "heatmap", dotDensity: "dot-density", dictionary: "dictionary", pieChart: "pie-chart" }, { ignoreUnknown: true });
var a4 = class extends l {
  constructor(r4) {
    super(r4), this.authoringInfo = null, this.type = null;
  }
  async getRequiredFields(r4) {
    if (!this.collectRequiredFields) return [];
    const e3 = /* @__PURE__ */ new Set();
    return await this.collectRequiredFields(e3, r4), Array.from(e3).sort();
  }
  getSymbol(r4, e3) {
  }
  async getSymbolAsync(r4, e3) {
  }
  getSymbols() {
    return [];
  }
  getAttributeHash() {
    return JSON.stringify(this);
  }
  getMeshHash() {
    return JSON.stringify(this);
  }
};
e([y({ type: j2, json: { write: true } })], a4.prototype, "authoringInfo", void 0), e([y({ type: n2.apiValues, readOnly: true, json: { type: n2.jsonValues, read: false, write: { writer: n2.write, ignoreOrigin: true } } })], a4.prototype, "type", void 0), a4 = e([a("esri.renderers.Renderer")], a4);
var p = a4;

// node_modules/@arcgis/core/renderers/support/randomRotationExpression.js
function e2(e3) {
  var _a, _b;
  return ((_b = (_a = e3.match(t2)) == null ? void 0 : _a[1]) == null ? void 0 : _b.replace(/\\'/g, "'")) ?? null;
}
var t2 = /^hash\(\$feature\['((\\'|[^'])+)'\]\) \* 8\.381e-8$/;

// node_modules/@arcgis/core/renderers/visualVariables/ColorVariable.js
var i;
var p2 = i = class extends c {
  constructor(t3) {
    super(t3), this.type = "color", this.normalizationField = null;
  }
  get cache() {
    return { ipData: this._interpolateData(), hasExpression: !!this.valueExpression, compiledFunc: null };
  }
  set stops(t3) {
    t3 && Array.isArray(t3) && (t3 = t3.filter((t4) => !!t4)).sort((t4, s4) => t4.value - s4.value), this._set("stops", t3);
  }
  clone() {
    return new i({ field: this.field, normalizationField: this.normalizationField, valueExpression: this.valueExpression, valueExpressionTitle: this.valueExpressionTitle, stops: this.stops && this.stops.map((t3) => t3.clone()), legendOptions: this.legendOptions && this.legendOptions.clone() });
  }
  getAttributeHash() {
    return `${super.getAttributeHash()}-${this.normalizationField}`;
  }
  _interpolateData() {
    return this.stops && this.stops.map((t3) => t3.value || 0);
  }
};
e([y({ readOnly: true })], p2.prototype, "cache", null), e([y({ type: ["color"], json: { type: ["colorInfo"] } })], p2.prototype, "type", void 0), e([y({ type: String, json: { write: true } })], p2.prototype, "normalizationField", void 0), e([y({ type: [a3], json: { write: true } })], p2.prototype, "stops", null), p2 = i = e([a("esri.renderers.visualVariables.ColorVariable")], p2);
var a5 = p2;

// node_modules/@arcgis/core/renderers/visualVariables/support/OpacityStop.js
var l2;
var u2 = l2 = class extends l {
  constructor(r4) {
    super(r4), this.label = null, this.opacity = null, this.value = null;
  }
  readOpacity(r4, t3) {
    return r3(t3.transparency);
  }
  writeOpacity(r4, t3, o2) {
    t3[o2] = n(r4);
  }
  clone() {
    return new l2({ label: this.label, opacity: this.opacity, value: this.value });
  }
};
e([y({ type: String, json: { write: true } })], u2.prototype, "label", void 0), e([y({ type: Number, json: { type: T, write: { target: "transparency" } } })], u2.prototype, "opacity", void 0), e([o("opacity", ["transparency"])], u2.prototype, "readOpacity", null), e([r2("opacity")], u2.prototype, "writeOpacity", null), e([y({ type: Number, json: { write: true } })], u2.prototype, "value", void 0), u2 = l2 = e([a("esri.renderers.visualVariables.support.OpacityStop")], u2);
var y2 = u2;

// node_modules/@arcgis/core/renderers/visualVariables/OpacityVariable.js
var i2;
var p3 = i2 = class extends c {
  constructor(t3) {
    super(t3), this.type = "opacity", this.normalizationField = null;
  }
  get cache() {
    return { ipData: this._interpolateData(), hasExpression: !!this.valueExpression, compiledFunc: null };
  }
  set stops(t3) {
    t3 && Array.isArray(t3) && (t3 = t3.filter((t4) => !!t4)).sort((t4, s4) => t4.value - s4.value), this._set("stops", t3);
  }
  clone() {
    return new i2({ field: this.field, normalizationField: this.normalizationField, valueExpression: this.valueExpression, valueExpressionTitle: this.valueExpressionTitle, stops: this.stops && this.stops.map((t3) => t3.clone()), legendOptions: this.legendOptions && this.legendOptions.clone() });
  }
  getAttributeHash() {
    return `${super.getAttributeHash()}-${this.normalizationField}`;
  }
  _interpolateData() {
    return this.stops && this.stops.map((t3) => t3.value || 0);
  }
};
e([y({ readOnly: true })], p3.prototype, "cache", null), e([y({ type: ["opacity"], json: { type: ["transparencyInfo"] } })], p3.prototype, "type", void 0), e([y({ type: String, json: { write: true } })], p3.prototype, "normalizationField", void 0), e([y({ type: [y2], json: { write: true } })], p3.prototype, "stops", null), p3 = i2 = e([a("esri.renderers.visualVariables.OpacityVariable")], p3);
var a6 = p3;

// node_modules/@arcgis/core/renderers/visualVariables/RotationVariable.js
var p4;
var a7 = p4 = class extends c {
  constructor(e3) {
    super(e3), this.axis = null, this.type = "rotation", this.rotationType = "geographic", this.valueExpressionTitle = null;
  }
  get cache() {
    return { hasExpression: !!this.valueExpression, compiledFunc: null };
  }
  writeValueExpressionTitleWebScene(e3, s4, o2, r4) {
    if (r4 && r4.messages) {
      const e4 = `visualVariables[${this.index}]`;
      r4.messages.push(new s2("property:unsupported", this.type + "VisualVariable.valueExpressionTitle is not supported in Web Scene. Please remove this property to save the Web Scene.", { instance: this, propertyName: e4 + ".valueExpressionTitle", context: r4 }));
    }
  }
  clone() {
    return new p4({ axis: this.axis, rotationType: this.rotationType, field: this.field, valueExpression: this.valueExpression, valueExpressionTitle: this.valueExpressionTitle, legendOptions: this.legendOptions && this.legendOptions.clone() });
  }
};
e([y({ readOnly: true })], a7.prototype, "cache", null), e([y({ type: ["heading", "tilt", "roll"], json: { origins: { "web-scene": { default: "heading", write: true } } } })], a7.prototype, "axis", void 0), e([y({ type: ["rotation"], json: { type: ["rotationInfo"] } })], a7.prototype, "type", void 0), e([y({ type: ["geographic", "arithmetic"], json: { write: true, origins: { "web-document": { write: true, default: "geographic" } } } })], a7.prototype, "rotationType", void 0), e([y({ type: String, json: { write: true } })], a7.prototype, "valueExpressionTitle", void 0), e([r2("web-scene", "valueExpressionTitle")], a7.prototype, "writeValueExpressionTitleWebScene", null), a7 = p4 = e([a("esri.renderers.visualVariables.RotationVariable")], a7);
var n3 = a7;

// node_modules/@arcgis/core/renderers/visualVariables/VisualVariableFactory.js
var u3 = { color: a5, size: b, opacity: a6, rotation: n3 };
var b2 = new s3({ colorInfo: "color", transparencyInfo: "opacity", rotationInfo: "rotation", sizeInfo: "size" });
var h = /^\[([^\]]+)\]$/i;
var V = class extends v {
  constructor() {
    super(...arguments), this.colorVariables = null, this.opacityVariables = null, this.rotationVariables = null, this.sizeVariables = null;
  }
  set visualVariables(r4) {
    if (this._resetVariables(), (r4 = r4 && r4.filter((r5) => !!r5)) && r4.length) {
      for (const s4 of r4) switch (s4.type) {
        case "color":
          this.colorVariables.push(s4);
          break;
        case "opacity":
          this.opacityVariables.push(s4);
          break;
        case "rotation":
          this.rotationVariables.push(s4);
          break;
        case "size":
          this.sizeVariables.push(s4);
      }
      if (this.sizeVariables.length) {
        this.sizeVariables.some((r5) => !!r5.target) && r4.sort((r5, s4) => {
          let e3 = null;
          return e3 = r5.target === s4.target ? 0 : r5.target ? 1 : -1, e3;
        });
      }
      for (let s4 = 0; s4 < r4.length; s4++) {
        r4[s4].index = s4;
      }
      this._set("visualVariables", r4);
    } else this._set("visualVariables", r4);
  }
  readVariables(r4, s4, e3) {
    const { rotationExpression: a8, rotationType: i3 } = s4, l4 = a8 && a8.match(h), n4 = l4 && l4[1];
    if (n4 && (r4 || (r4 = []), r4.push({ type: "rotationInfo", rotationType: i3, field: n4 })), r4) return r4.map((r5) => {
      const s5 = b2.read(r5.type), a9 = u3[s5];
      a9 || (s.getLogger(this.declaredClass).warn(`Unknown variable type: ${s5}`), e3 && e3.messages && e3.messages.push(new t("visual-variable:unsupported", `visualVariable of type '${s5}' is not supported`, { definition: r5, context: e3 })));
      const i4 = new a9();
      return i4.read(r5, e3), i4;
    });
  }
  writeVariables(r4, s4) {
    const e3 = [];
    for (const o2 of r4) {
      const r5 = o2.toJSON(s4);
      r5 && e3.push(r5);
    }
    return e3;
  }
  _resetVariables() {
    this.colorVariables = [], this.opacityVariables = [], this.rotationVariables = [], this.sizeVariables = [];
  }
};
e([y()], V.prototype, "visualVariables", null), V = e([a("esri.renderers.visualVariables.VisualVariableFactory")], V);
var f = V;

// node_modules/@arcgis/core/renderers/mixins/VisualVariablesMixin.js
var v2 = { base: c, key: "type", typeMap: { opacity: a6, color: a5, rotation: n3, size: b } };
var y3 = (a8) => {
  let u4 = class extends a8 {
    constructor() {
      super(...arguments), this._vvFactory = new f();
    }
    set visualVariables(r4) {
      this._vvFactory.visualVariables = r4, this._set("visualVariables", this._vvFactory.visualVariables);
    }
    readVisualVariables(r4, a9, s4) {
      return this._vvFactory.readVariables(r4, a9, s4);
    }
    writeVisualVariables(r4, a9, s4, i3) {
      a9[s4] = this._vvFactory.writeVariables(r4, i3);
    }
    get arcadeRequiredForVisualVariables() {
      if (!this.visualVariables) return false;
      for (const r4 of this.visualVariables) if (r4.arcadeRequired) return true;
      return false;
    }
    hasVisualVariables(r4, a9) {
      return r4 ? this.getVisualVariablesForType(r4, a9).length > 0 : this.getVisualVariablesForType("size", a9).length > 0 || this.getVisualVariablesForType("color", a9).length > 0 || this.getVisualVariablesForType("opacity", a9).length > 0 || this.getVisualVariablesForType("rotation", a9).length > 0;
    }
    getVisualVariablesForType(r4, a9) {
      const s4 = this.visualVariables;
      return s4 ? s4.filter((s5) => s5.type === r4 && ("string" == typeof a9 ? s5.target === a9 : false !== a9 || !s5.target)) : [];
    }
    async collectVVRequiredFields(r4, a9) {
      let s4 = [];
      this.visualVariables && (s4 = s4.concat(this.visualVariables));
      for (const i3 of s4) i3 && (i3.field && w(r4, a9, i3.field), i3.normalizationField && w(r4, a9, i3.normalizationField), i3.valueExpression && (f2(i3.valueExpression, r4, a9) || await S2(r4, a9, i3.valueExpression)));
    }
  };
  return e([y({ types: [v2], value: null, json: { write: true } })], u4.prototype, "visualVariables", null), e([o("visualVariables", ["visualVariables", "rotationType", "rotationExpression"])], u4.prototype, "readVisualVariables", null), e([r2("visualVariables")], u4.prototype, "writeVisualVariables", null), u4 = e([a("esri.renderers.mixins.VisualVariablesMixin")], u4), u4;
};
function f2(r4, s4, i3) {
  const e3 = e2(r4);
  return !!r(e3) && (w(s4, i3, e3), true);
}

// node_modules/@arcgis/core/renderers/support/commonProperties.js
var l3 = { types: M, json: { write: { writer: m }, origins: { "web-scene": { types: F, write: { writer: m }, read: { reader: u({ types: F }) } } } } };
var y4 = { types: { base: a2, key: "type", typeMap: { "simple-fill": j.typeMap["simple-fill"], "picture-fill": j.typeMap["picture-fill"], "polygon-3d": j.typeMap["polygon-3d"] } }, json: { write: { writer: m }, origins: { "web-scene": { type: S, write: { writer: m } } } } };
var m2 = { cast: (e3) => null == e3 || "string" == typeof e3 || "number" == typeof e3 ? e3 : `${e3}`, json: { type: String, write: { writer: (e3, r4) => {
  r4.value = e3 == null ? void 0 : e3.toString();
} } } };

export {
  p,
  n3 as n,
  y3 as y,
  l3 as l,
  y4 as y2,
  m2 as m
};
//# sourceMappingURL=chunk-TWFTBWXP.js.map
