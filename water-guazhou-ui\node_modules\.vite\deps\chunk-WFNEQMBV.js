import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/views/3d/support/debugFlags.js
var T = class extends v {
  constructor() {
    super(...arguments), this.SCENEVIEW_HITTEST_RETURN_INTERSECTOR = false, this.DECONFLICTOR_SHOW_VISIBLE = false, this.DECONFLICTOR_SHOW_INVISIBLE = false, this.DECONFLICTOR_SHOW_GRID = false, this.LABELS_SHOW_BORDER = false, this.TEXT_SHOW_BASELINE = false, this.TEXT_SHOW_BORDER = false, this.OVERLAY_DRAW_DEBUG_TEXTURE = false, this.OVERLAY_SHOW_CENTER = false, this.SHOW_POI = false, this.TESTS_DISABLE_OPTIMIZATIONS = false, this.TESTS_DISABLE_FAST_UPDATES = false, this.DRAW_MESH_GEOMETRY_NORMALS = false, this.FEATURE_TILE_FETCH_SHOW_TILES = false, this.FEATURE_TILE_TREE_SHOW_TILES = false, this.TERRAIN_TILE_TREE_SHOW_TILES = false, this.I3S_TREE_SHOW_TILES = false, this.I3S_SHOW_MODIFICATIONS = false, this.LOD_INSTANCE_RENDERER_DISABLE_UPDATES = false, this.LOD_INSTANCE_RENDERER_COLORIZE_BY_LEVEL = false, this.EDGES_SHOW_HIDDEN_TRANSPARENT_EDGES = false, this.LINE_WIREFRAMES = false, this.TERRAIN_USE_LEGACY_SHADING = false;
  }
};
e([y()], T.prototype, "SCENEVIEW_HITTEST_RETURN_INTERSECTOR", void 0), e([y()], T.prototype, "DECONFLICTOR_SHOW_VISIBLE", void 0), e([y()], T.prototype, "DECONFLICTOR_SHOW_INVISIBLE", void 0), e([y()], T.prototype, "DECONFLICTOR_SHOW_GRID", void 0), e([y()], T.prototype, "LABELS_SHOW_BORDER", void 0), e([y()], T.prototype, "TEXT_SHOW_BASELINE", void 0), e([y()], T.prototype, "TEXT_SHOW_BORDER", void 0), e([y()], T.prototype, "OVERLAY_DRAW_DEBUG_TEXTURE", void 0), e([y()], T.prototype, "OVERLAY_SHOW_CENTER", void 0), e([y()], T.prototype, "SHOW_POI", void 0), e([y()], T.prototype, "TESTS_DISABLE_OPTIMIZATIONS", void 0), e([y()], T.prototype, "TESTS_DISABLE_FAST_UPDATES", void 0), e([y()], T.prototype, "DRAW_MESH_GEOMETRY_NORMALS", void 0), e([y()], T.prototype, "FEATURE_TILE_FETCH_SHOW_TILES", void 0), e([y()], T.prototype, "FEATURE_TILE_TREE_SHOW_TILES", void 0), e([y()], T.prototype, "TERRAIN_TILE_TREE_SHOW_TILES", void 0), e([y()], T.prototype, "I3S_TREE_SHOW_TILES", void 0), e([y()], T.prototype, "I3S_SHOW_MODIFICATIONS", void 0), e([y()], T.prototype, "LOD_INSTANCE_RENDERER_DISABLE_UPDATES", void 0), e([y()], T.prototype, "LOD_INSTANCE_RENDERER_COLORIZE_BY_LEVEL", void 0), e([y()], T.prototype, "EDGES_SHOW_HIDDEN_TRANSPARENT_EDGES", void 0), e([y()], T.prototype, "LINE_WIREFRAMES", void 0), e([y()], T.prototype, "TERRAIN_USE_LEGACY_SHADING", void 0), T = e([a("esri.views.3d.support.DebugFlags")], T);
var t = new T();

export {
  t
};
//# sourceMappingURL=chunk-WFNEQMBV.js.map
