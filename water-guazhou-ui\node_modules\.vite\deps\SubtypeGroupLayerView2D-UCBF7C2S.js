import {
  D
} from "./chunk-VFUA7AXS.js";
import "./chunk-ZJMR67LT.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-M46W6WAV.js";
import "./chunk-C65HMCEM.js";
import "./chunk-RCQLO7DH.js";
import "./chunk-ARNVPEMS.js";
import "./chunk-HSGVCYPR.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-N73MYEJE.js";
import "./chunk-ES4OMWUC.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-BNOUIPKE.js";
import "./chunk-7TISIISL.js";
import "./chunk-74RIZBAU.js";
import "./chunk-JSZR3BUH.js";
import "./chunk-6W6ECZU2.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-REFSHSQW.js";
import "./chunk-RURSJOSG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-LFDODH6Z.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-77E52HT5.js";
import {
  y
} from "./chunk-ZPQGODVG.js";
import "./chunk-GNVA6PUD.js";
import "./chunk-ERECWYOT.js";
import "./chunk-VBFIXQAN.js";
import "./chunk-LN7EIT6D.js";
import "./chunk-HQDK2TLZ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-HDM6HCKB.js";
import "./chunk-ZONU323N.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-ICW345PU.js";
import "./chunk-WCGNZXE2.js";
import "./chunk-AZTQJ4L4.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-GDMKZBSE.js";
import "./chunk-KCSQWRUD.js";
import "./chunk-2ILOD42U.js";
import "./chunk-6TZFJURO.js";
import "./chunk-T4DDX2RP.js";
import "./chunk-T72XIVTW.js";
import "./chunk-6YK77SK5.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-NOEG2P2J.js";
import "./chunk-WGNECGZE.js";
import "./chunk-YGSDGECR.js";
import "./chunk-JZBEMQMW.js";
import "./chunk-HM62IZSE.js";
import "./chunk-GFEKPAJZ.js";
import "./chunk-4CFWXJIK.js";
import "./chunk-DHWMTT76.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-YBNKNHCD.js";
import "./chunk-C3LWQPIC.js";
import "./chunk-LVWRJMBJ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-72RC7KC7.js";
import "./chunk-PGSBPPQ2.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-V6NQCXYQ.js";
import "./chunk-XAKEPYSQ.js";
import {
  h,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-HRASNGES.js";
import "./chunk-I4BKZ7SD.js";
import "./chunk-S5FBFNAP.js";
import "./chunk-VBAEC53F.js";
import "./chunk-C67OD7TM.js";
import "./chunk-5A4WR2SR.js";
import "./chunk-SJ35WMYN.js";
import "./chunk-UV4E33V4.js";
import "./chunk-PBQFTVHM.js";
import "./chunk-ZJKAJ76S.js";
import "./chunk-46HTCESL.js";
import "./chunk-NE5KC6IQ.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-Q6JATJLO.js";
import "./chunk-CV3OR36A.js";
import "./chunk-JX2QAMUH.js";
import "./chunk-DVUUHX3W.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import "./chunk-SJRT3EVN.js";
import "./chunk-RCNP3U5T.js";
import "./chunk-2CFIAWMM.js";
import "./chunk-3MWB7OGY.js";
import "./chunk-MURG32WB.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-LZMNPMOO.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-3BEYEFLH.js";
import "./chunk-NM5RTWYY.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-B2DWQPEO.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-IJ6FZE6K.js";
import "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2 as a
} from "./chunk-R4CPW7J5.js";
import "./chunk-2CM7MIII.js";
import "./chunk-HP475EI3.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/SubtypeGroupLayerView2D.js
function l2(e2, r2) {
  return !e2.visible || 0 !== e2.minScale && r2 > e2.minScale || 0 !== e2.maxScale && r2 < e2.maxScale;
}
var o = class extends D {
  initialize() {
    this.addHandles([l(() => this.view.scale, () => this._update(), h)], "constructor");
  }
  isUpdating() {
    var _a;
    const e2 = this.layer.sublayers.some((e3) => null != e3.renderer), r2 = this._commandsQueue.updating, s = null != this._updatingRequiredFieldsPromise, i = !this._proxy || !this._proxy.isReady, t = this._pipelineIsUpdating, a2 = null == this.tileRenderer || ((_a = this.tileRenderer) == null ? void 0 : _a.updating), n = e2 && (r2 || s || i || t || a2);
    return has("esri-2d-log-updating") && console.log(`Updating FLV2D: ${n}
  -> hasRenderer ${e2}
  -> hasPendingCommand ${r2}
  -> updatingRequiredFields ${s}
  -> updatingProxy ${i}
  -> updatingPipeline ${t}
  -> updatingTileRenderer ${a2}
`), n;
  }
  _injectOverrides(e2) {
    let s = super._injectOverrides(e2);
    const i = this.view.scale, t = this.layer.sublayers.filter((e3) => l2(e3, i)).map((e3) => e3.subtypeCode);
    if (!t.length) return s;
    s = r(s) ? s : new y().toJSON();
    const n = `NOT ${this.layer.subtypeField} IN (${t.join(",")})`;
    return s.where = s.where ? `(${s.where}) AND (${n})` : n, s;
  }
  _setLayersForFeature(e2) {
    const r2 = this.layer.fieldsIndex.get(this.layer.subtypeField), s = e2.attributes[r2.name], i = this.layer.sublayers.find((e3) => e3.subtypeCode === s);
    e2.layer = e2.sourceLayer = i;
  }
  _createSchemaConfig() {
    const e2 = { subtypeField: this.layer.subtypeField, sublayers: Array.from(this.layer.sublayers).map((e3) => ({ featureReduction: null, geometryType: this.layer.geometryType, labelingInfo: e3.labelingInfo, labelsVisible: e3.labelsVisible, renderer: e3.renderer, subtypeCode: e3.subtypeCode, orderBy: null })) }, r2 = this.layer.sublayers.map((e3) => e3.subtypeCode).join(","), s = this.layer.sublayers.length ? `${this.layer.subtypeField} IN (${r2})` : "1=2";
    let i = this.layer.definitionExpression ? this.layer.definitionExpression + " AND " : "";
    return i += s, { ...super._createSchemaConfig(), ...e2, definitionExpression: i };
  }
};
o = e([a("esri.views.2d.layers.SubtypeGroupLayerView2D")], o);
var u = o;
export {
  u as default
};
//# sourceMappingURL=SubtypeGroupLayerView2D-UCBF7C2S.js.map
