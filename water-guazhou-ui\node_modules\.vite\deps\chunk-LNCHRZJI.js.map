{"version": 3, "sources": ["../../@arcgis/core/layers/support/OrderByInfo.js", "../../@arcgis/core/layers/mixins/OrderedLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONMap as e}from\"../../core/jsonMap.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";var i;const p=new e({asc:\"ascending\",desc:\"descending\"});let n=i=class extends o{constructor(r){super(r),this.field=null,this.valueExpression=null,this.order=\"ascending\"}clone(){return new i({field:this.field,valueExpression:this.valueExpression,order:this.order})}};r([s({type:String,json:{write:!0}})],n.prototype,\"field\",void 0),r([s({type:String,json:{write:!0}})],n.prototype,\"valueExpression\",void 0),r([s({type:p.apiValues,json:{read:p.read,write:p.write}})],n.prototype,\"order\",void 0),n=i=r([t(\"esri.layers.support.OrderByInfo\")],n);const c=n;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{setDeepValue as e}from\"../../core/object.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";import s from\"../support/OrderByInfo.js\";function i(r,e,o){if(!r)return null;const t=r.find((r=>!!r.field));if(!t)return null;const i=new s;return i.read(t,o),[i]}function n(r,o,t,s){const i=r.find((r=>!!r.field));i&&e(t,[i.toJSON()],o)}const c=e=>{let c=class extends e{constructor(){super(...arguments),this.orderBy=null}};return r([o({type:[s],json:{origins:{\"web-scene\":{write:!1,read:!1}},read:{source:\"layerDefinition.orderBy\",reader:i},write:{target:\"layerDefinition.orderBy\",writer:n}}})],c.prototype,\"orderBy\",void 0),c=r([t(\"esri.layers.mixins.OrderedLayer\")],c),c};export{c as OrderedLayer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAIkY,IAAI;AAAE,IAAM,IAAE,IAAI,EAAE,EAAC,KAAI,aAAY,MAAK,aAAY,CAAC;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,QAAM,MAAK,KAAK,kBAAgB,MAAK,KAAK,QAAM;AAAA,EAAW;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,OAAM,KAAK,OAAM,iBAAgB,KAAK,iBAAgB,OAAM,KAAK,MAAK,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,WAAU,MAAK,EAAC,MAAK,EAAE,MAAK,OAAM,EAAE,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,iCAAiC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACAjjB,SAASA,GAAE,GAAEC,IAAEC,IAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,QAAM,IAAE,EAAE,KAAM,CAAAC,OAAG,CAAC,CAACA,GAAE,KAAM;AAAE,MAAG,CAAC,EAAE,QAAO;AAAK,QAAMH,KAAE,IAAI;AAAE,SAAOA,GAAE,KAAK,GAAEE,EAAC,GAAE,CAACF,EAAC;AAAC;AAAC,SAASI,GAAE,GAAEF,IAAE,GAAEG,IAAE;AAAC,QAAML,KAAE,EAAE,KAAM,CAAAG,OAAG,CAAC,CAACA,GAAE,KAAM;AAAE,EAAAH,MAAG,EAAE,GAAE,CAACA,GAAE,OAAO,CAAC,GAAEE,EAAC;AAAC;AAAC,IAAMI,KAAE,CAAAL,OAAG;AAAC,MAAIK,KAAE,cAAcL,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,UAAQ;AAAA,IAAI;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,OAAM,OAAG,MAAK,MAAE,EAAC,GAAE,MAAK,EAAC,QAAO,2BAA0B,QAAOD,GAAC,GAAE,OAAM,EAAC,QAAO,2BAA0B,QAAOI,GAAC,EAAC,EAAC,CAAC,CAAC,GAAEE,GAAE,WAAU,WAAU,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,iCAAiC,CAAC,GAAEA,EAAC,GAAEA;AAAC;", "names": ["i", "e", "o", "r", "n", "s", "c"]}