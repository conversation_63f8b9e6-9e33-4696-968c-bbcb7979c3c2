{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderTechnique/ReloadableShaderModule.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderTechnique/ShaderTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/lib/Program.js", "../../@arcgis/core/views/3d/webgl-engine/lib/SSAOBlurTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/lib/SSAONoiseData.js", "../../@arcgis/core/views/3d/webgl-engine/lib/SSAOTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/SSAOParameters.js", "../../@arcgis/core/views/3d/webgl-engine/lib/SSAOHelper.js", "../../@arcgis/core/views/3d/webgl-engine/lighting/Lightsources.js", "../../@arcgis/core/views/3d/webgl-engine/lib/LongVectorMath.js", "../../@arcgis/core/views/3d/webgl-engine/lighting/SphericalHarmonics.js", "../../@arcgis/core/views/3d/webgl-engine/lighting/SceneLighting.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t,o){this._module=t,this._loadModule=o}get(){return this._module}async reload(){return this._module=await this._loadModule(),this._module}}export{t as ReloadableShaderModule};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{disposeMaybe as i}from\"../../../../../core/maybe.js\";import{PrimitiveType as t}from\"../../../../webgl/enums.js\";class e{constructor(i,t,e){this.release=e,this.initializeConfiguration(i,t),this._configuration=t.snapshot(),this._program=this.initializeProgram(i),this._pipeline=this.initializePipeline(i.rctx.capabilities)}destroy(){this._program=i(this._program),this._pipeline=this._configuration=null}reload(t){i(this._program),this._program=this.initializeProgram(t),this._pipeline=this.initializePipeline(t.rctx.capabilities)}get program(){return this._program}get compiled(){return this.program.compiled}get key(){return this._configuration.key}get configuration(){return this._configuration}bindPipelineState(i,t=null,e){i.setPipelineState(this.getPipelineState(t,e))}ensureAttributeLocations(i){this.program.assertCompatibleVertexAttributeLocations(i)}get primitiveType(){return t.TRIANGLES}getPipelineState(i,t){return this._pipeline}initializeConfiguration(i,t){}}export{e as ShaderTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t,isSome as e}from\"../../../../core/maybe.js\";import r from\"../../../../core/PooledArray.js\";import{BindType as i}from\"../core/shaderTechnique/BindType.js\";import{webglDebugEnabled as s}from\"../../../webgl/checkWebGLError.js\";class o{constructor(t,e,o){this._context=t,this._locations=o,this._textures=new Map,this._freeTextureUnits=new r({deallocator:null}),this._glProgram=t.programCache.acquire(e.generate(\"vertex\"),e.generate(\"fragment\"),o),this._glProgram.stop=()=>{throw new Error(\"Wrapped _glProgram used directly\")},this.bindPass=e.generateBind(i.Pass,this),this.bindDraw=e.generateBind(i.Draw,this),this._fragmentUniforms=s()?e.fragmentUniforms:null}dispose(){this._glProgram.dispose()}get glName(){return this._glProgram.glName}get compiled(){return this._glProgram.compiled}setUniform1b(t,e){this._glProgram.setUniform1i(t,e?1:0)}setUniform1i(t,e){this._glProgram.setUniform1i(t,e)}setUniform1f(t,e){this._glProgram.setUniform1f(t,e)}setUniform2fv(t,e){this._glProgram.setUniform2fv(t,e)}setUniform3fv(t,e){this._glProgram.setUniform3fv(t,e)}setUniform4fv(t,e){this._glProgram.setUniform4fv(t,e)}setUniformMatrix3fv(t,e){this._glProgram.setUniformMatrix3fv(t,e)}setUniformMatrix4fv(t,e){this._glProgram.setUniformMatrix4fv(t,e)}setUniform1fv(t,e){this._glProgram.setUniform1fv(t,e)}setUniform1iv(t,e){this._glProgram.setUniform1iv(t,e)}setUniform2iv(t,e){this._glProgram.setUniform3iv(t,e)}setUniform3iv(t,e){this._glProgram.setUniform3iv(t,e)}setUniform4iv(t,e){this._glProgram.setUniform4iv(t,e)}assertCompatibleVertexAttributeLocations(t){t.locations!==this._locations&&console.error(\"VertexAttributeLocations are incompatible\")}stop(){this._textures.clear(),this._freeTextureUnits.clear()}bindTexture(e,r){if(t(r)||null==r.glName){const t=this._textures.get(e);return t&&(this._context.bindTexture(null,t.unit),this._freeTextureUnit(t),this._textures.delete(e)),null}let i=this._textures.get(e);return null==i?(i=this._allocTextureUnit(r),this._textures.set(e,i)):i.texture=r,this._context.useProgram(this),this.setUniform1i(e,i.unit),this._context.bindTexture(r,i.unit),i.unit}rebindTextures(){this._context.useProgram(this),this._textures.forEach(((t,e)=>{this._context.bindTexture(t.texture,t.unit),this.setUniform1i(e,t.unit)})),e(this._fragmentUniforms)&&this._fragmentUniforms.forEach((t=>{\"sampler2D\"!==t.type&&\"samplerCube\"!==t.type||this._textures.has(t.name)||console.error(`Texture sampler ${t.name} has no bound texture`)}))}_allocTextureUnit(t){return{texture:t,unit:0===this._freeTextureUnits.length?this._textures.size:this._freeTextureUnits.pop()}}_freeTextureUnit(t){this._freeTextureUnits.push(t.unit)}}export{o as Program};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ReloadableShaderModule as r}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{ShaderTechnique as e}from\"../core/shaderTechnique/ShaderTechnique.js\";import{Default3D as i}from\"./DefaultVertexAttributeLocations.js\";import{Program as o}from\"./Program.js\";import{S as t}from\"../../../../chunks/SSAOBlur.glsl.js\";import{makePipelineState as s,defaultColorWriteParams as a}from\"../../../webgl/renderState.js\";class l extends e{initializeProgram(r){return new o(r.rctx,l.shader.get().build(),i)}initializePipeline(){return s({colorWrite:a})}}l.shader=new r(t,(()=>import(\"../shaders/SSAOBlur.glsl.js\")));export{l as SSAOBlurTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst e=\"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\";export{e as noiseData};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ReloadableShaderModule as e}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{ShaderTechnique as r}from\"../core/shaderTechnique/ShaderTechnique.js\";import{Default3D as i}from\"./DefaultVertexAttributeLocations.js\";import{Program as o}from\"./Program.js\";import{S as t}from\"../../../../chunks/SSAO.glsl.js\";import{makePipelineState as s,defaultColorWriteParams as a}from\"../../../webgl/renderState.js\";class l extends r{initializeProgram(e){return new o(e.rctx,l.shader.get().build(),i)}initializePipeline(){return s({colorWrite:a})}}l.shader=new e(t,(()=>import(\"../shaders/SSAO.glsl.js\")));export{l as SSAOTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{a as s}from\"../../../../chunks/vec2f64.js\";import{NoParameters as e}from\"../core/shaderModules/interfaces.js\";class r extends e{constructor(){super(...arguments),this.projScale=1}}class t extends r{constructor(){super(...arguments),this.intensity=1}}class c extends e{}class o extends c{constructor(){super(...arguments),this.blurSize=s()}}export{o as BlurDrawParameters,r as BlurPassParameters,c as SSAODrawParameters,t as SSAOPassParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{disposeMaybe as e,applySome as s,isSome as t,isNone as r}from\"../../../../core/maybe.js\";import{Milliseconds as i}from\"../../../../core/time.js\";import{s as a}from\"../../../../chunks/vec2.js\";import{createQuadVAO as h}from\"./glUtil3D.js\";import{SSAOBlurTechnique as _}from\"./SSAOBlurTechnique.js\";import{noiseData as o}from\"./SSAONoiseData.js\";import{SSAOTechnique as u}from\"./SSAOTechnique.js\";import{g as n}from\"../../../../chunks/SSAO.glsl.js\";import{SSAOPassParameters as l,BlurDrawParameters as m}from\"../shaders/SSAOParameters.js\";import{PrimitiveType as b,TextureType as c,PixelFormat as d,PixelType as p,TextureSamplingMode as T,TextureWrapMode as O,TargetType as B,DepthStencilTargetType as F}from\"../../../webgl/enums.js\";import{FramebufferObject as w}from\"../../../webgl/FramebufferObject.js\";import{Texture as f}from\"../../../webgl/Texture.js\";import{vertexCount as g}from\"../../../webgl/Util.js\";const q=2;class P{constructor(e,s,t,r){this._view=e,this._techniqueRepository=s,this._rctx=t,this._requestRender=r,this._quadVAO=null,this._passParameters=new l,this._drawParameters=new m}dispose(){this.enabled=!1,this._quadVAO=e(this._quadVAO)}disposeOffscreenBuffers(){s(this._ssaoFBO,(e=>e.resize(0,0))),s(this._blur0FBO,(e=>e.resize(0,0))),s(this._blur1FBO,(e=>e.resize(0,0)))}set enabled(e){e?this._enable():this._disable()}get enabled(){return t(this._enableTime)}get active(){return this.enabled&&this._ssaoTechnique.compiled&&this._blurTechnique.compiled}get colorTexture(){return t(this._blur1FBO)?this._blur1FBO.colorTexture:null}render(e,s,t,i){if(r(this._enableTime)||r(t)||r(i)||r(this._ssaoFBO)||r(this._blur0FBO)||r(this._blur1FBO))return;if(!this.active)return this._enableTime=s,void this._requestRender();0===this._enableTime&&(this._enableTime=s);const _=this._rctx,o=e.camera,u=this._view.qualitySettings.fadeDuration,l=u>0?Math.min(u,s-this._enableTime)/u:1;this._passParameters.normalTexture=i,this._passParameters.depthTexture=t,this._passParameters.projScale=1/o.computeRenderPixelSizeAtDist(1),this._passParameters.intensity=4*A/n(o)**6*l;const m=o.fullViewport,c=m[2],d=m[3],p=c/q,T=d/q;this._ssaoFBO.resize(c,d),this._blur0FBO.resize(p,T),this._blur1FBO.resize(p,T),r(this._quadVAO)&&(this._quadVAO=h(this._rctx)),_.bindFramebuffer(this._ssaoFBO),_.setViewport(0,0,c,d);_.bindTechnique(this._ssaoTechnique,this._passParameters,e).bindDraw(this._drawParameters,e,this._passParameters),_.bindVAO(this._quadVAO);const O=g(this._quadVAO,\"geometry\");_.drawArrays(b.TRIANGLE_STRIP,0,O);const B=_.bindTechnique(this._blurTechnique,this._passParameters,e);_.setViewport(0,0,p,T),_.bindFramebuffer(this._blur0FBO),this._drawParameters.colorTexture=this._ssaoFBO.colorTexture,a(this._drawParameters.blurSize,0,q/d),B.bindDraw(this._drawParameters,e,this._passParameters),_.setViewport(0,0,p,T),_.drawArrays(b.TRIANGLE_STRIP,0,O),_.bindFramebuffer(this._blur1FBO),this._drawParameters.colorTexture=this._blur0FBO.colorTexture,a(this._drawParameters.blurSize,q/c,0),B.bindDraw(this._drawParameters,e,this._passParameters),_.drawArrays(b.TRIANGLE_STRIP,0,O),_.setViewport(m[0],m[1],m[2],m[3]),l<1&&this._requestRender()}_enable(){if(t(this._enableTime))return;const e={target:c.TEXTURE_2D,pixelFormat:d.RGBA,dataType:p.UNSIGNED_BYTE,samplingMode:T.LINEAR,wrapMode:O.CLAMP_TO_EDGE,width:0,height:0},s={colorTarget:B.TEXTURE,depthStencilTarget:F.NONE};this._ssaoFBO=new w(this._rctx,s,e),this._blur0FBO=new w(this._rctx,s,e),this._blur1FBO=new w(this._rctx,s,e);const a=Uint8Array.from(atob(o),(e=>e.charCodeAt(0)));this._passParameters.noiseTexture=new f(this._rctx,{target:c.TEXTURE_2D,pixelFormat:d.RGB,dataType:p.UNSIGNED_BYTE,hasMipmap:!0,width:32,height:32},a),r(this._ssaoTechnique)&&(this._ssaoTechnique=this._techniqueRepository.acquire(u)),r(this._blurTechnique)&&(this._blurTechnique=this._techniqueRepository.acquire(_)),this._enableTime=i(0),this._requestRender()}_disable(){this._enableTime=null,this._passParameters.noiseTexture=e(this._passParameters.noiseTexture),this._blur1FBO=e(this._blur1FBO),this._blur0FBO=e(this._blur0FBO),this._ssaoFBO=e(this._ssaoFBO)}get gpuMemoryUsage(){return(t(this._blur0FBO)?this._blur0FBO.gpuMemoryUsage:0)+(t(this._blur1FBO)?this._blur1FBO.gpuMemoryUsage:0)+(t(this._ssaoFBO)?this._ssaoFBO.gpuMemoryUsage:0)}get test(){return{ssao:this._ssaoFBO,blur:this._blur1FBO}}}const A=.5;export{P as SSAOHelper,q as blurSizePixels};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as t,f as s}from\"../../../../chunks/vec3f64.js\";class i{constructor(s=t()){this.intensity=s}}class c{constructor(i=t(),c=s(.57735,.57735,.57735)){this.intensity=i,this.direction=c}}class n{constructor(i=t(),c=s(.57735,.57735,.57735),n=!0,r=1,h=1){this.intensity=i,this.direction=c,this.castShadows=n,this.specularStrength=r,this.environmentStrength=h}}class r{constructor(){this.r=[0],this.g=[0],this.b=[0]}}export{i as AmbientLight,c as FillLight,n as MainLight,r as SphericalHarmonicsAmbientLight};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction t(t,n){let e=0;for(let l=0;l<t.length;l++)e+=t[l]*n[l];return e}function n(t,n,e){(e=e||t).length=t.length;for(let l=0;l<t.length;l++)e[l]=t[l]*n[l];return e}function e(t,n,e){(e=e||t).length=t.length;for(let l=0;l<t.length;l++)e[l]=t[l]*n;return e}function l(t,n,e){(e=e||t).length=t.length;for(let l=0;l<t.length;l++)e[l]=t[l]+n[l];return e}export{l as add,t as dotProduct,n as elementwiseProduct,e as scalarProduct};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clamp as t}from\"../../../../core/mathUtils.js\";import{o as n,s as o,c as r}from\"../../../../chunks/vec3.js\";import{c as e}from\"../../../../chunks/vec3f64.js\";import{elementwiseProduct as s,scalarProduct as i,add as c}from\"../lib/LongVectorMath.js\";import{MainLight as f,FillLight as h,AmbientLight as a,SphericalHarmonicsAmbientLight as g}from\"./Lightsources.js\";function u(t){return(t+1)*(t+1)}function l(n){return t(Math.floor(Math.sqrt(n)-1),0,2)}function m(t,n,o){const r=t[0],e=t[1],s=t[2],i=o||[];return i.length=u(n),n>=0&&(i[0]=.28209479177),n>=1&&(i[1]=.4886025119*r,i[2]=.4886025119*s,i[3]=.4886025119*e),n>=2&&(i[4]=1.09254843059*r*e,i[5]=1.09254843059*e*s,i[6]=.31539156525*(3*s*s-1),i[7]=1.09254843059*r*s,i[8]=.54627421529*(r*r-e*e)),i}function p(t,n){const o=u(t),r=n||{r:[],g:[],b:[]};r.r.length=r.g.length=r.b.length=o;for(let e=0;e<o;e++)r.r[e]=r.g[e]=r.b[e]=0;return r}function y(t,o){const r=l(o.r.length);for(const e of t)n(P,e.direction),m(P,r,v),s(v,k),i(v,e.intensity[0],I),c(o.r,I),i(v,e.intensity[1],I),c(o.g,I),i(v,e.intensity[2],I),c(o.b,I);return o}function b(t,n){m(P,0,v);for(const o of t)n.r[0]+=v[0]*k[0]*o.intensity[0]*4*Math.PI,n.g[0]+=v[0]*k[0]*o.intensity[1]*4*Math.PI,n.b[0]+=v[0]*k[0]*o.intensity[2]*4*Math.PI;return n}function M(t,n,e,s){p(n,s),o(e.intensity,0,0,0);let i=!1;const u=S,l=d,m=j;u.length=0,l.length=0,m.length=0;for(const o of t)o instanceof f&&!i?(r(e.direction,o.direction),r(e.intensity,o.intensity),e.specularStrength=o.specularStrength,e.environmentStrength=o.environmentStrength,e.castShadows=o.castShadows,i=!0):o instanceof f||o instanceof h?u.push(o):o instanceof a?l.push(o):o instanceof g&&m.push(o);y(u,s),b(l,s);for(const o of m)c(s.r,o.r),c(s.g,o.g),c(s.b,o.b)}const S=[],d=[],j=[],v=[0],I=[0],P=e(),k=[3.141593,2.094395,2.094395,2.094395,.785398,.785398,.785398,.785398,.785398];export{M as combineLights,m as computeCoefficients,p as initSHCoefficients,u as numberOfCoefficients,l as orderFromNumberOfCoefficients,b as projectAmbientLights,y as projectFillLights};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{lerp as i}from\"../../../../core/mathUtils.js\";import{c as t,g as s,a as h,h as r}from\"../../../../chunks/vec3.js\";import{c as n,f as a,a as o}from\"../../../../chunks/vec3f64.js\";import{SphericalHarmonicsAmbientLight as e,MainLight as c}from\"./Lightsources.js\";import{combineLights as g}from\"./SphericalHarmonics.js\";class m{constructor(){this.color=n(),this.intensity=1}}class l{constructor(){this.direction=n(),this.ambient=new m,this.diffuse=new m}}const _=.4;class L{constructor(){this._shOrder=2,this._legacy=new l,this.globalFactor=.5,this.noonFactor=.5,this._sphericalHarmonics=new e,this._mainLight=new c(n(),a(1,0,0),!1)}get legacy(){return this._legacy}get sh(){return this._sphericalHarmonics}get mainLight(){return this._mainLight}set(i){g(i,this._shOrder,this._mainLight,this._sphericalHarmonics),t(this._legacy.direction,this._mainLight.direction);const r=1/Math.PI;this._legacy.ambient.color[0]=.282095*this._sphericalHarmonics.r[0]*r,this._legacy.ambient.color[1]=.282095*this._sphericalHarmonics.g[0]*r,this._legacy.ambient.color[2]=.282095*this._sphericalHarmonics.b[0]*r,s(this._legacy.diffuse.color,this._mainLight.intensity,r),t(p,this._legacy.diffuse.color),s(p,p,_*this.globalFactor),h(this._legacy.ambient.color,this._legacy.ambient.color,p)}copyFrom(i){this._sphericalHarmonics.r=Array.from(i.sh.r),this._sphericalHarmonics.g=Array.from(i.sh.g),this._sphericalHarmonics.b=Array.from(i.sh.b),this._mainLight.direction=o(i.mainLight.direction),this._mainLight.intensity=o(i.mainLight.intensity),this._mainLight.castShadows=i.mainLight.castShadows,this._mainLight.specularStrength=i.mainLight.specularStrength,this._mainLight.environmentStrength=i.mainLight.environmentStrength,this.globalFactor=i.globalFactor,this.noonFactor=i.noonFactor}lerpLighting(s,h,n){if(r(this._mainLight.intensity,s.mainLight.intensity,h.mainLight.intensity,n),this._mainLight.environmentStrength=i(s.mainLight.environmentStrength,h.mainLight.environmentStrength,n),this._mainLight.specularStrength=i(s.mainLight.specularStrength,h.mainLight.specularStrength,n),t(this._mainLight.direction,h.mainLight.direction),this._mainLight.castShadows=h.mainLight.castShadows,this.globalFactor=i(s.globalFactor,h.globalFactor,n),this.noonFactor=i(s.noonFactor,h.noonFactor,n),s.sh.r.length===h.sh.r.length)for(let t=0;t<h.sh.r.length;t++)this._sphericalHarmonics.r[t]=i(s.sh.r[t],h.sh.r[t],n),this._sphericalHarmonics.g[t]=i(s.sh.g[t],h.sh.g[t],n),this._sphericalHarmonics.b[t]=i(s.sh.b[t],h.sh.b[t],n);else for(let i=0;i<h.sh.r.length;i++)this._sphericalHarmonics.r[i]=h.sh.r[i],this._sphericalHarmonics.g[i]=h.sh.g[i],this._sphericalHarmonics.b[i]=h.sh.b[i]}}const p=n();export{L as SceneLighting,_ as ambientBoost};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAMA,KAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,UAAQD,IAAE,KAAK,cAAYC;AAAA,EAAC;AAAA,EAAC,MAAK;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,MAAM,SAAQ;AAAC,WAAO,KAAK,UAAQ,MAAM,KAAK,YAAY,GAAE,KAAK;AAAA,EAAO;AAAC;;;ACAvC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQA,IAAE,KAAK,wBAAwBF,IAAEC,EAAC,GAAE,KAAK,iBAAeA,GAAE,SAAS,GAAE,KAAK,WAAS,KAAK,kBAAkBD,EAAC,GAAE,KAAK,YAAU,KAAK,mBAAmBA,GAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,YAAU,KAAK,iBAAe;AAAA,EAAI;AAAA,EAAC,OAAOC,IAAE;AAAC,MAAE,KAAK,QAAQ,GAAE,KAAK,WAAS,KAAK,kBAAkBA,EAAC,GAAE,KAAK,YAAU,KAAK,mBAAmBA,GAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAQ;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,eAAe;AAAA,EAAG;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK;AAAA,EAAc;AAAA,EAAC,kBAAkBD,IAAEC,KAAE,MAAKC,IAAE;AAAC,IAAAF,GAAE,iBAAiB,KAAK,iBAAiBC,IAAEC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBF,IAAE;AAAC,SAAK,QAAQ,yCAAyCA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,EAAE;AAAA,EAAS;AAAA,EAAC,iBAAiBA,IAAEC,IAAE;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,wBAAwBD,IAAEC,IAAE;AAAA,EAAC;AAAC;;;ACA/tB,IAAME,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEF,IAAE;AAAC,SAAK,WAASC,IAAE,KAAK,aAAWD,IAAE,KAAK,YAAU,oBAAI,OAAI,KAAK,oBAAkB,IAAI,EAAE,EAAC,aAAY,KAAI,CAAC,GAAE,KAAK,aAAWC,GAAE,aAAa,QAAQC,GAAE,SAAS,QAAQ,GAAEA,GAAE,SAAS,UAAU,GAAEF,EAAC,GAAE,KAAK,WAAW,OAAK,MAAI;AAAC,YAAM,IAAI,MAAM,kCAAkC;AAAA,IAAC,GAAE,KAAK,WAASE,GAAE,aAAaC,GAAE,MAAK,IAAI,GAAE,KAAK,WAASD,GAAE,aAAaC,GAAE,MAAK,IAAI,GAAE,KAAK,oBAAkBA,GAAE,IAAED,GAAE,mBAAiB;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,SAAK,WAAW,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,WAAW;AAAA,EAAM;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,WAAW;AAAA,EAAQ;AAAA,EAAC,aAAaD,IAAEC,IAAE;AAAC,SAAK,WAAW,aAAaD,IAAEC,KAAE,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAEC,IAAE;AAAC,SAAK,WAAW,aAAaD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAEC,IAAE;AAAC,SAAK,WAAW,aAAaD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,SAAK,WAAW,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,SAAK,WAAW,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,SAAK,WAAW,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAEC,IAAE;AAAC,SAAK,WAAW,oBAAoBD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAEC,IAAE;AAAC,SAAK,WAAW,oBAAoBD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,SAAK,WAAW,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,SAAK,WAAW,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,SAAK,WAAW,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,SAAK,WAAW,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,SAAK,WAAW,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,yCAAyCD,IAAE;AAAC,IAAAA,GAAE,cAAY,KAAK,cAAY,QAAQ,MAAM,2CAA2C;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,UAAU,MAAM,GAAE,KAAK,kBAAkB,MAAM;AAAA,EAAC;AAAA,EAAC,YAAYC,IAAEE,IAAE;AAAC,QAAG,EAAEA,EAAC,KAAG,QAAMA,GAAE,QAAO;AAAC,YAAMH,KAAE,KAAK,UAAU,IAAIC,EAAC;AAAE,aAAOD,OAAI,KAAK,SAAS,YAAY,MAAKA,GAAE,IAAI,GAAE,KAAK,iBAAiBA,EAAC,GAAE,KAAK,UAAU,OAAOC,EAAC,IAAG;AAAA,IAAI;AAAC,QAAIG,KAAE,KAAK,UAAU,IAAIH,EAAC;AAAE,WAAO,QAAMG,MAAGA,KAAE,KAAK,kBAAkBD,EAAC,GAAE,KAAK,UAAU,IAAIF,IAAEG,EAAC,KAAGA,GAAE,UAAQD,IAAE,KAAK,SAAS,WAAW,IAAI,GAAE,KAAK,aAAaF,IAAEG,GAAE,IAAI,GAAE,KAAK,SAAS,YAAYD,IAAEC,GAAE,IAAI,GAAEA,GAAE;AAAA,EAAI;AAAA,EAAC,iBAAgB;AAAC,SAAK,SAAS,WAAW,IAAI,GAAE,KAAK,UAAU,QAAS,CAACJ,IAAEC,OAAI;AAAC,WAAK,SAAS,YAAYD,GAAE,SAAQA,GAAE,IAAI,GAAE,KAAK,aAAaC,IAAED,GAAE,IAAI;AAAA,IAAC,CAAE,GAAE,EAAE,KAAK,iBAAiB,KAAG,KAAK,kBAAkB,QAAS,CAAAA,OAAG;AAAC,sBAAcA,GAAE,QAAM,kBAAgBA,GAAE,QAAM,KAAK,UAAU,IAAIA,GAAE,IAAI,KAAG,QAAQ,MAAM,mBAAmBA,GAAE,IAAI,uBAAuB;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,WAAM,EAAC,SAAQA,IAAE,MAAK,MAAI,KAAK,kBAAkB,SAAO,KAAK,UAAU,OAAK,KAAK,kBAAkB,IAAI,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,SAAK,kBAAkB,KAAKA,GAAE,IAAI;AAAA,EAAC;AAAC;;;ACArrE,IAAMK,KAAN,MAAM,WAAU,EAAC;AAAA,EAAC,kBAAkBC,IAAE;AAAC,WAAO,IAAIC,GAAED,GAAE,MAAK,GAAE,OAAO,IAAI,EAAE,MAAM,GAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,EAAE,EAAC,YAAW,EAAC,CAAC;AAAA,EAAC;AAAC;AAACH,GAAE,SAAO,IAAII,GAAEC,IAAG,MAAI,OAAO,6BAA6B,CAAE;;;ACAvmB,IAAMC,KAAE;;;ACA2Z,IAAMC,KAAN,MAAM,WAAU,EAAC;AAAA,EAAC,kBAAkBC,IAAE;AAAC,WAAO,IAAIC,GAAED,GAAE,MAAK,GAAE,OAAO,IAAI,EAAE,MAAM,GAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,EAAE,EAAC,YAAW,EAAC,CAAC;AAAA,EAAC;AAAC;AAACH,GAAE,SAAO,IAAII,GAAEC,IAAG,MAAI,OAAO,yBAAyB,CAAE;;;ACA1e,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,YAAU;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,cAAgBF,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,YAAU;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgBC,GAAC;AAAC;AAAC,IAAME,KAAN,cAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,WAASF,GAAE;AAAA,EAAC;AAAC;;;ACA0jB,IAAM,IAAE;AAAE,IAAMG,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE,GAAEC,IAAEC,IAAE;AAAC,SAAK,QAAMF,IAAE,KAAK,uBAAqB,GAAE,KAAK,QAAMC,IAAE,KAAK,iBAAeC,IAAE,KAAK,WAAS,MAAK,KAAK,kBAAgB,IAAID,MAAE,KAAK,kBAAgB,IAAIE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,UAAQ,OAAG,KAAK,WAAS,EAAE,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,MAAE,KAAK,UAAU,CAAAH,OAAGA,GAAE,OAAO,GAAE,CAAC,CAAE,GAAE,EAAE,KAAK,WAAW,CAAAA,OAAGA,GAAE,OAAO,GAAE,CAAC,CAAE,GAAE,EAAE,KAAK,WAAW,CAAAA,OAAGA,GAAE,OAAO,GAAE,CAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,QAAQA,IAAE;AAAC,IAAAA,KAAE,KAAK,QAAQ,IAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,EAAE,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,WAAS,KAAK,eAAe,YAAU,KAAK,eAAe;AAAA,EAAQ;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,EAAE,KAAK,SAAS,IAAE,KAAK,UAAU,eAAa;AAAA,EAAI;AAAA,EAAC,OAAOA,IAAE,GAAEC,IAAEG,IAAE;AAAC,QAAG,EAAE,KAAK,WAAW,KAAG,EAAEH,EAAC,KAAG,EAAEG,EAAC,KAAG,EAAE,KAAK,QAAQ,KAAG,EAAE,KAAK,SAAS,KAAG,EAAE,KAAK,SAAS,EAAE;AAAO,QAAG,CAAC,KAAK,OAAO,QAAO,KAAK,cAAY,GAAE,KAAK,KAAK,eAAe;AAAE,UAAI,KAAK,gBAAc,KAAK,cAAY;AAAG,UAAMC,KAAE,KAAK,OAAMF,KAAEH,GAAE,QAAOM,KAAE,KAAK,MAAM,gBAAgB,cAAaC,KAAED,KAAE,IAAE,KAAK,IAAIA,IAAE,IAAE,KAAK,WAAW,IAAEA,KAAE;AAAE,SAAK,gBAAgB,gBAAcF,IAAE,KAAK,gBAAgB,eAAaH,IAAE,KAAK,gBAAgB,YAAU,IAAEE,GAAE,6BAA6B,CAAC,GAAE,KAAK,gBAAgB,YAAU,IAAEK,KAAE,EAAEL,EAAC,KAAG,IAAEI;AAAE,UAAME,KAAEN,GAAE,cAAaO,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,GAAEG,KAAEF,KAAE,GAAE,IAAEC,KAAE;AAAE,SAAK,SAAS,OAAOD,IAAEC,EAAC,GAAE,KAAK,UAAU,OAAOC,IAAE,CAAC,GAAE,KAAK,UAAU,OAAOA,IAAE,CAAC,GAAE,EAAE,KAAK,QAAQ,MAAI,KAAK,WAAS,EAAE,KAAK,KAAK,IAAGP,GAAE,gBAAgB,KAAK,QAAQ,GAAEA,GAAE,YAAY,GAAE,GAAEK,IAAEC,EAAC;AAAE,IAAAN,GAAE,cAAc,KAAK,gBAAe,KAAK,iBAAgBL,EAAC,EAAE,SAAS,KAAK,iBAAgBA,IAAE,KAAK,eAAe,GAAEK,GAAE,QAAQ,KAAK,QAAQ;AAAE,UAAM,IAAEQ,GAAE,KAAK,UAAS,UAAU;AAAE,IAAAR,GAAE,WAAW,EAAE,gBAAe,GAAE,CAAC;AAAE,UAAM,IAAEA,GAAE,cAAc,KAAK,gBAAe,KAAK,iBAAgBL,EAAC;AAAE,IAAAK,GAAE,YAAY,GAAE,GAAEO,IAAE,CAAC,GAAEP,GAAE,gBAAgB,KAAK,SAAS,GAAE,KAAK,gBAAgB,eAAa,KAAK,SAAS,cAAaH,GAAE,KAAK,gBAAgB,UAAS,GAAE,IAAES,EAAC,GAAE,EAAE,SAAS,KAAK,iBAAgBX,IAAE,KAAK,eAAe,GAAEK,GAAE,YAAY,GAAE,GAAEO,IAAE,CAAC,GAAEP,GAAE,WAAW,EAAE,gBAAe,GAAE,CAAC,GAAEA,GAAE,gBAAgB,KAAK,SAAS,GAAE,KAAK,gBAAgB,eAAa,KAAK,UAAU,cAAaH,GAAE,KAAK,gBAAgB,UAAS,IAAEQ,IAAE,CAAC,GAAE,EAAE,SAAS,KAAK,iBAAgBV,IAAE,KAAK,eAAe,GAAEK,GAAE,WAAW,EAAE,gBAAe,GAAE,CAAC,GAAEA,GAAE,YAAYI,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEF,KAAE,KAAG,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,EAAE,KAAK,WAAW,EAAE;AAAO,UAAMP,KAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,cAAa,EAAE,QAAO,UAAS,EAAE,eAAc,OAAM,GAAE,QAAO,EAAC,GAAE,IAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,KAAI;AAAE,SAAK,WAAS,IAAI,EAAE,KAAK,OAAM,GAAEA,EAAC,GAAE,KAAK,YAAU,IAAI,EAAE,KAAK,OAAM,GAAEA,EAAC,GAAE,KAAK,YAAU,IAAI,EAAE,KAAK,OAAM,GAAEA,EAAC;AAAE,UAAMc,KAAE,WAAW,KAAK,KAAKd,EAAC,GAAG,CAAAA,OAAGA,GAAE,WAAW,CAAC,CAAE;AAAE,SAAK,gBAAgB,eAAa,IAAIe,GAAE,KAAK,OAAM,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,KAAI,UAAS,EAAE,eAAc,WAAU,MAAG,OAAM,IAAG,QAAO,GAAE,GAAED,EAAC,GAAE,EAAE,KAAK,cAAc,MAAI,KAAK,iBAAe,KAAK,qBAAqB,QAAQP,EAAC,IAAG,EAAE,KAAK,cAAc,MAAI,KAAK,iBAAe,KAAK,qBAAqB,QAAQA,EAAC,IAAG,KAAK,cAAY,EAAE,CAAC,GAAE,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,cAAY,MAAK,KAAK,gBAAgB,eAAa,EAAE,KAAK,gBAAgB,YAAY,GAAE,KAAK,YAAU,EAAE,KAAK,SAAS,GAAE,KAAK,YAAU,EAAE,KAAK,SAAS,GAAE,KAAK,WAAS,EAAE,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,YAAO,EAAE,KAAK,SAAS,IAAE,KAAK,UAAU,iBAAe,MAAI,EAAE,KAAK,SAAS,IAAE,KAAK,UAAU,iBAAe,MAAI,EAAE,KAAK,QAAQ,IAAE,KAAK,SAAS,iBAAe;AAAA,EAAE;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM,EAAC,MAAK,KAAK,UAAS,MAAK,KAAK,UAAS;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAE;;;ACAzsI,IAAM,IAAN,MAAO;AAAA,EAAC,YAAY,IAAEQ,GAAE,GAAE;AAAC,SAAK,YAAU;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAEF,GAAE,GAAEC,KAAEE,GAAE,SAAO,SAAO,OAAM,GAAE;AAAC,SAAK,YAAUD,IAAE,KAAK,YAAUD;AAAA,EAAC;AAAC;AAAC,IAAMD,KAAN,MAAO;AAAA,EAAC,YAAYE,KAAEF,GAAE,GAAEC,KAAEE,GAAE,SAAO,SAAO,OAAM,GAAEH,KAAE,MAAGG,KAAE,GAAEC,KAAE,GAAE;AAAC,SAAK,YAAUF,IAAE,KAAK,YAAUD,IAAE,KAAK,cAAYD,IAAE,KAAK,mBAAiBG,IAAE,KAAK,sBAAoBC;AAAA,EAAC;AAAC;AAAC,IAAMD,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,IAAE,CAAC,CAAC,GAAE,KAAK,IAAE,CAAC,CAAC,GAAE,KAAK,IAAE,CAAC,CAAC;AAAA,EAAC;AAAC;;;ACAvV,SAASE,GAAEC,IAAED,IAAEE,IAAE;AAAC,GAACA,KAAEA,MAAGD,IAAG,SAAOA,GAAE;AAAO,WAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAD,GAAEC,EAAC,IAAEF,GAAEE,EAAC,IAAEH,GAAEG,EAAC;AAAE,SAAOD;AAAC;AAAC,SAASA,GAAED,IAAED,IAAEE,IAAE;AAAC,GAACA,KAAEA,MAAGD,IAAG,SAAOA,GAAE;AAAO,WAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAD,GAAEC,EAAC,IAAEF,GAAEE,EAAC,IAAEH;AAAE,SAAOE;AAAC;AAAC,SAASC,GAAEF,IAAED,IAAEE,IAAE;AAAC,GAACA,KAAEA,MAAGD,IAAG,SAAOA,GAAE;AAAO,WAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAD,GAAEC,EAAC,IAAEF,GAAEE,EAAC,IAAEH,GAAEG,EAAC;AAAE,SAAOD;AAAC;;;ACAmB,SAASE,GAAEC,IAAE;AAAC,UAAOA,KAAE,MAAIA,KAAE;AAAE;AAAC,SAASC,GAAEC,IAAE;AAAC,SAAO,EAAE,KAAK,MAAM,KAAK,KAAKA,EAAC,IAAE,CAAC,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEF,IAAEE,IAAEC,IAAE;AAAC,QAAMC,KAAEJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAEM,KAAEH,MAAG,CAAC;AAAE,SAAOG,GAAE,SAAOP,GAAEG,EAAC,GAAEA,MAAG,MAAII,GAAE,CAAC,IAAE,gBAAcJ,MAAG,MAAII,GAAE,CAAC,IAAE,eAAYF,IAAEE,GAAE,CAAC,IAAE,eAAY,GAAEA,GAAE,CAAC,IAAE,eAAYD,KAAGH,MAAG,MAAII,GAAE,CAAC,IAAE,gBAAcF,KAAEC,IAAEC,GAAE,CAAC,IAAE,gBAAcD,KAAE,GAAEC,GAAE,CAAC,IAAE,iBAAc,IAAE,IAAE,IAAE,IAAGA,GAAE,CAAC,IAAE,gBAAcF,KAAE,GAAEE,GAAE,CAAC,IAAE,iBAAcF,KAAEA,KAAEC,KAAEA,MAAIC;AAAC;AAAC,SAASC,GAAEP,IAAEE,IAAE;AAAC,QAAMC,KAAEJ,GAAEC,EAAC,GAAEI,KAAEF,MAAG,EAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,EAAC;AAAE,EAAAE,GAAE,EAAE,SAAOA,GAAE,EAAE,SAAOA,GAAE,EAAE,SAAOD;AAAE,WAAQE,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAD,GAAE,EAAEC,EAAC,IAAED,GAAE,EAAEC,EAAC,IAAED,GAAE,EAAEC,EAAC,IAAE;AAAE,SAAOD;AAAC;AAAC,SAAS,EAAEJ,IAAEG,IAAE;AAAC,QAAMC,KAAEH,GAAEE,GAAE,EAAE,MAAM;AAAE,aAAUE,MAAKL,GAAE,GAAEQ,IAAEH,GAAE,SAAS,GAAE,EAAEG,IAAEJ,IAAE,CAAC,GAAEF,GAAE,GAAE,CAAC,GAAEG,GAAE,GAAEA,GAAE,UAAU,CAAC,GAAE,CAAC,GAAEJ,GAAEE,GAAE,GAAE,CAAC,GAAEE,GAAE,GAAEA,GAAE,UAAU,CAAC,GAAE,CAAC,GAAEJ,GAAEE,GAAE,GAAE,CAAC,GAAEE,GAAE,GAAEA,GAAE,UAAU,CAAC,GAAE,CAAC,GAAEJ,GAAEE,GAAE,GAAE,CAAC;AAAE,SAAOA;AAAC;AAAC,SAAS,EAAEH,IAAEE,IAAE;AAAC,IAAEM,IAAE,GAAE,CAAC;AAAE,aAAUL,MAAKH,GAAE,CAAAE,GAAE,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAEC,GAAE,UAAU,CAAC,IAAE,IAAE,KAAK,IAAGD,GAAE,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAEC,GAAE,UAAU,CAAC,IAAE,IAAE,KAAK,IAAGD,GAAE,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAEC,GAAE,UAAU,CAAC,IAAE,IAAE,KAAK;AAAG,SAAOD;AAAC;AAAC,SAASO,GAAET,IAAEE,IAAEG,IAAE,GAAE;AAAC,EAAAE,GAAEL,IAAE,CAAC,GAAEC,GAAEE,GAAE,WAAU,GAAE,GAAE,CAAC;AAAE,MAAIC,KAAE;AAAG,QAAMP,KAAE,GAAEE,KAAE,GAAES,KAAEC;AAAE,EAAAZ,GAAE,SAAO,GAAEE,GAAE,SAAO,GAAES,GAAE,SAAO;AAAE,aAAUP,MAAKH,GAAE,CAAAG,cAAaD,MAAG,CAACI,MAAGF,GAAEC,GAAE,WAAUF,GAAE,SAAS,GAAEC,GAAEC,GAAE,WAAUF,GAAE,SAAS,GAAEE,GAAE,mBAAiBF,GAAE,kBAAiBE,GAAE,sBAAoBF,GAAE,qBAAoBE,GAAE,cAAYF,GAAE,aAAYG,KAAE,QAAIH,cAAaD,MAAGC,cAAaS,KAAEb,GAAE,KAAKI,EAAC,IAAEA,cAAa,IAAEF,GAAE,KAAKE,EAAC,IAAEA,cAAaC,MAAGM,GAAE,KAAKP,EAAC;AAAE,IAAEJ,IAAE,CAAC,GAAE,EAAEE,IAAE,CAAC;AAAE,aAAUE,MAAKO,GAAE,CAAAT,GAAE,EAAE,GAAEE,GAAE,CAAC,GAAEF,GAAE,EAAE,GAAEE,GAAE,CAAC,GAAEF,GAAE,EAAE,GAAEE,GAAE,CAAC;AAAC;AAAC,IAAM,IAAE,CAAC;AAAT,IAAW,IAAE,CAAC;AAAd,IAAgBQ,KAAE,CAAC;AAAnB,IAAqB,IAAE,CAAC,CAAC;AAAzB,IAA2B,IAAE,CAAC,CAAC;AAA/B,IAAiCH,KAAEN,GAAE;AAArC,IAAuC,IAAE,CAAC,UAAS,UAAS,UAAS,UAAS,UAAQ,UAAQ,UAAQ,UAAQ,QAAO;;;ACA1/C,IAAMW,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,QAAMC,GAAE,GAAE,KAAK,YAAU;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAUD,GAAE,GAAE,KAAK,UAAQ,IAAID,MAAE,KAAK,UAAQ,IAAIA;AAAA,EAAC;AAAC;AAAC,IAAMG,KAAE;AAAG,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,WAAS,GAAE,KAAK,UAAQ,IAAIF,MAAE,KAAK,eAAa,KAAG,KAAK,aAAW,KAAG,KAAK,sBAAoB,IAAIG,MAAE,KAAK,aAAW,IAAIJ,GAAEA,GAAE,GAAEI,GAAE,GAAE,GAAE,CAAC,GAAE,KAAE;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,KAAI;AAAC,WAAO,KAAK;AAAA,EAAmB;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAIC,IAAE;AAAC,IAAAC,GAAED,IAAE,KAAK,UAAS,KAAK,YAAW,KAAK,mBAAmB,GAAED,GAAE,KAAK,QAAQ,WAAU,KAAK,WAAW,SAAS;AAAE,UAAMA,KAAE,IAAE,KAAK;AAAG,SAAK,QAAQ,QAAQ,MAAM,CAAC,IAAE,WAAQ,KAAK,oBAAoB,EAAE,CAAC,IAAEA,IAAE,KAAK,QAAQ,QAAQ,MAAM,CAAC,IAAE,WAAQ,KAAK,oBAAoB,EAAE,CAAC,IAAEA,IAAE,KAAK,QAAQ,QAAQ,MAAM,CAAC,IAAE,WAAQ,KAAK,oBAAoB,EAAE,CAAC,IAAEA,IAAE,EAAE,KAAK,QAAQ,QAAQ,OAAM,KAAK,WAAW,WAAUA,EAAC,GAAEA,GAAEG,IAAE,KAAK,QAAQ,QAAQ,KAAK,GAAE,EAAEA,IAAEA,IAAEL,KAAE,KAAK,YAAY,GAAE,EAAE,KAAK,QAAQ,QAAQ,OAAM,KAAK,QAAQ,QAAQ,OAAMK,EAAC;AAAA,EAAC;AAAA,EAAC,SAASF,IAAE;AAAC,SAAK,oBAAoB,IAAE,MAAM,KAAKA,GAAE,GAAG,CAAC,GAAE,KAAK,oBAAoB,IAAE,MAAM,KAAKA,GAAE,GAAG,CAAC,GAAE,KAAK,oBAAoB,IAAE,MAAM,KAAKA,GAAE,GAAG,CAAC,GAAE,KAAK,WAAW,YAAUG,GAAEH,GAAE,UAAU,SAAS,GAAE,KAAK,WAAW,YAAUG,GAAEH,GAAE,UAAU,SAAS,GAAE,KAAK,WAAW,cAAYA,GAAE,UAAU,aAAY,KAAK,WAAW,mBAAiBA,GAAE,UAAU,kBAAiB,KAAK,WAAW,sBAAoBA,GAAE,UAAU,qBAAoB,KAAK,eAAaA,GAAE,cAAa,KAAK,aAAWA,GAAE;AAAA,EAAU;AAAA,EAAC,aAAa,GAAEI,IAAET,IAAE;AAAC,QAAG,EAAE,KAAK,WAAW,WAAU,EAAE,UAAU,WAAUS,GAAE,UAAU,WAAUT,EAAC,GAAE,KAAK,WAAW,sBAAoBS,GAAE,EAAE,UAAU,qBAAoBA,GAAE,UAAU,qBAAoBT,EAAC,GAAE,KAAK,WAAW,mBAAiBS,GAAE,EAAE,UAAU,kBAAiBA,GAAE,UAAU,kBAAiBT,EAAC,GAAEI,GAAE,KAAK,WAAW,WAAUK,GAAE,UAAU,SAAS,GAAE,KAAK,WAAW,cAAYA,GAAE,UAAU,aAAY,KAAK,eAAaA,GAAE,EAAE,cAAaA,GAAE,cAAaT,EAAC,GAAE,KAAK,aAAWS,GAAE,EAAE,YAAWA,GAAE,YAAWT,EAAC,GAAE,EAAE,GAAG,EAAE,WAASS,GAAE,GAAG,EAAE,OAAO,UAAQD,KAAE,GAAEA,KAAEC,GAAE,GAAG,EAAE,QAAOD,KAAI,MAAK,oBAAoB,EAAEA,EAAC,IAAEC,GAAE,EAAE,GAAG,EAAED,EAAC,GAAEC,GAAE,GAAG,EAAED,EAAC,GAAER,EAAC,GAAE,KAAK,oBAAoB,EAAEQ,EAAC,IAAEC,GAAE,EAAE,GAAG,EAAED,EAAC,GAAEC,GAAE,GAAG,EAAED,EAAC,GAAER,EAAC,GAAE,KAAK,oBAAoB,EAAEQ,EAAC,IAAEC,GAAE,EAAE,GAAG,EAAED,EAAC,GAAEC,GAAE,GAAG,EAAED,EAAC,GAAER,EAAC;AAAA,QAAO,UAAQK,KAAE,GAAEA,KAAEI,GAAE,GAAG,EAAE,QAAOJ,KAAI,MAAK,oBAAoB,EAAEA,EAAC,IAAEI,GAAE,GAAG,EAAEJ,EAAC,GAAE,KAAK,oBAAoB,EAAEA,EAAC,IAAEI,GAAE,GAAG,EAAEJ,EAAC,GAAE,KAAK,oBAAoB,EAAEA,EAAC,IAAEI,GAAE,GAAG,EAAEJ,EAAC;AAAA,EAAC;AAAC;AAAC,IAAME,KAAEP,GAAE;", "names": ["t", "o", "i", "t", "e", "o", "t", "e", "a", "r", "i", "l", "r", "o", "E", "t", "u", "e", "l", "e", "o", "E", "t", "h", "r", "n", "t", "o", "P", "e", "t", "r", "o", "i", "_", "u", "l", "A", "m", "c", "d", "p", "n", "a", "E", "n", "c", "i", "r", "h", "n", "t", "e", "l", "u", "t", "l", "n", "o", "r", "e", "i", "p", "P", "M", "m", "j", "c", "m", "n", "l", "_", "L", "r", "i", "M", "p", "t", "h"]}