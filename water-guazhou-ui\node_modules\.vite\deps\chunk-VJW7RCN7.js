import {
  T,
  n
} from "./chunk-N7ADFPOO.js";
import {
  x
} from "./chunk-FSNYK4TH.js";
import {
  r as r2
} from "./chunk-NVZMGX2J.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  f as f2
} from "./chunk-XVA5SA7P.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/support/timeUtils.js
function f3(t2) {
  if (!t2) return t2;
  const { start: i, end: r3 } = t2;
  return new T({ start: r(i) ? n(i, -i.getTimezoneOffset(), "minutes") : i, end: r(r3) ? n(r3, -r3.getTimezoneOffset(), "minutes") : r3 });
}
function a(t2) {
  if (!t2) return t2;
  const { start: i, end: r3 } = t2;
  return new T({ start: r(i) ? n(i, i.getTimezoneOffset(), "minutes") : i, end: r(r3) ? n(r3, r3.getTimezoneOffset(), "minutes") : r3 });
}

// node_modules/@arcgis/core/layers/support/commonProperties.js
var l = { type: Boolean, value: true, json: { origins: { service: { read: false, write: false }, "web-map": { read: false, write: false } }, name: "screenSizePerspective", write: true } };
var p = { type: Boolean, value: true, json: { name: "disablePopup", read: { reader: (e2, r3) => !r3.disablePopup }, write: { enabled: true, writer(e2, r3, i) {
  r3[i] = !e2;
} } } };
var m = { type: Boolean, value: true, nonNullable: true, json: { name: "showLabels", write: true } };
var f4 = { type: String, json: { origins: { "portal-item": { write: false } }, write: { isRequired: true, ignoreOrigin: true, writer: f2 } } };
var c = { type: Boolean, value: true, nonNullable: true, json: { origins: { service: { read: { enabled: false } } }, name: "showLegend", write: true } };
var d = { value: null, type: x, json: { origins: { service: { name: "elevationInfo", write: true } }, name: "layerDefinition.elevationInfo", write: true } };
function y(e2) {
  return { type: e2, readOnly: true, json: { origins: { service: { read: true } }, read: false } };
}
var w2 = { write: true, read: true };
var u = { type: Number, json: { origins: { "web-document": w2, "portal-item": { write: true } } } };
var g = { ...u, json: { ...u.json, origins: { "web-document": { ...w2, write: { enabled: true, target: { opacity: { type: Number }, "layerDefinition.drawingInfo.transparency": { type: Number } } } } }, read: { source: ["layerDefinition.drawingInfo.transparency", "drawingInfo.transparency"], reader: (e2, r3, i) => i && "service" !== i.origin || !r3.drawingInfo || void 0 === r3.drawingInfo.transparency ? r3.layerDefinition && r3.layerDefinition.drawingInfo && void 0 !== r3.layerDefinition.drawingInfo.transparency ? r2(r3.layerDefinition.drawingInfo.transparency) : void 0 : r2(r3.drawingInfo.transparency) } } };
var b = { type: T, readOnly: true, get() {
  var _a, _b;
  if (!((_a = this.layer) == null ? void 0 : _a.timeInfo)) return null;
  const { datesInUnknownTimezone: e2, timeOffset: r3, useViewTime: i } = this.layer, n2 = (_b = this.view) == null ? void 0 : _b.timeExtent;
  let a2 = this.layer.timeExtent;
  e2 && (a2 = a(a2));
  let s = i ? n2 && a2 ? n2.intersection(a2) : n2 || a2 : a2;
  if (!s || s.isEmpty || s.isAllTime) return s;
  r3 && (s = s.offset(-r3.value, r3.unit)), e2 && (s = f3(s));
  const l2 = this._get("timeExtent");
  return s.equals(l2) ? l2 : s;
} };
var j = { type: w, readOnly: true, json: { origins: { service: { read: { source: ["fullExtent", "spatialReference"], reader: (e2, n2) => {
  const t2 = w.fromJSON(e2);
  return null != n2.spatialReference && "object" == typeof n2.spatialReference && (t2.spatialReference = f.fromJSON(n2.spatialReference)), t2;
} } } }, read: false } };
var v = { type: String, json: { origins: { service: { read: false }, "portal-item": { read: false } } } };
var I = { type: Number, json: { origins: { service: { write: { enabled: false } } }, read: { source: "layerDefinition.minScale" }, write: { target: "layerDefinition.minScale" } } };
var D = { type: Number, json: { origins: { service: { write: { enabled: false } } }, read: { source: "layerDefinition.maxScale" }, write: { target: "layerDefinition.maxScale" } } };
var S = { json: { write: { ignoreOrigin: true }, origins: { "web-map": { read: false, write: false } } } };

export {
  l,
  p,
  m,
  f4 as f,
  c,
  d,
  y,
  u,
  g,
  b,
  j,
  v,
  I,
  D,
  S
};
//# sourceMappingURL=chunk-VJW7RCN7.js.map
