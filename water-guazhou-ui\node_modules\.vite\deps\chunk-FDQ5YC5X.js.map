{"version": 3, "sources": ["../../@arcgis/core/layers/ogc/ogcFeatureUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../geometry.js\";import e from\"../../request.js\";import t from\"../../core/Error.js\";import n from\"../../core/Logger.js\";import{isNone as i,unwrapOr as r,isSome as a}from\"../../core/maybe.js\";import{WGS84 as o}from\"../../geometry/support/spatialReferenceUtils.js\";import{project as s}from\"../../geometry/support/webMercatorUtils.js\";import{convertToFeatureSet as l,convertToGeometry as c,convertFromGeometry as d}from\"../graphics/featureConversionUtils.js\";import u from\"../graphics/OptimizedFeatureSet.js\";import{validateGeoJSON as m,inferLayerProperties as p,createOptimizedFeatures as f}from\"../graphics/sources/geojson/geojson.js\";import{createDrawingInfo as g}from\"../graphics/sources/support/clientSideDefaults.js\";import y from\"../support/FieldsIndex.js\";import{kebabDict as w}from\"../support/fieldType.js\";import b from\"../../geometry/SpatialReference.js\";const h=n.getLogger(\"esri.layers.graphics.sources.ogcfeature\"),j=\"http://www.opengis.net/def/crs/\",F=`${j}OGC/1.3/CRS84`;async function I(n,r,a={},o=5){const{links:s}=n,l=D(s,\"items\",\"application/geo+json\")||D(s,\"http://www.opengis.net/def/rel/ogc/1.0/items\",\"application/geo+json\");if(i(l))throw new t(\"ogc-feature-layer:missing-items-page\",\"Missing items url\");const{data:c}=await e(l.href,{signal:a.signal,query:{limit:o,...a.customParameters,token:a.apiKey},headers:{accept:\"application/geo+json\"}});await m(c);const d=p(c,{geometryType:r.geometryType}),u=r.fields||d.fields||[],f=null!=r.hasZ?r.hasZ:d.hasZ,b=d.geometryType,j=r.objectIdField||d.objectIdFieldName||\"OBJECTID\";let F=r.timeInfo;const I=u.find((({name:e})=>e===j));if(I)I.editable=!1,I.nullable=!1;else{if(!d.objectIdFieldType)throw new t(\"ogc-feature-layer:missing-feature-id\",\"Collection geojson require a feature id as a unique identifier\");u.unshift({name:j,alias:j,type:\"number\"===d.objectIdFieldType?\"esriFieldTypeOID\":\"esriFieldTypeString\",editable:!1,nullable:!1})}if(j!==d.objectIdFieldName){const e=u.find((({name:e})=>e===d.objectIdFieldName));e&&(e.type=\"esriFieldTypeInteger\")}u===d.fields&&d.unknownFields.length>0&&h.warn({name:\"ogc-feature-layer:unknown-field-types\",message:\"Some fields types couldn't be inferred from the features and were dropped\",details:{unknownFields:d.unknownFields}});for(const e of u){if(null==e.name&&(e.name=e.alias),null==e.alias&&(e.alias=e.name),\"esriFieldTypeOID\"!==e.type&&\"esriFieldTypeGlobalID\"!==e.type&&(e.editable=null==e.editable||!!e.editable,e.nullable=null==e.nullable||!!e.nullable),!e.name)throw new t(\"ogc-feature-layer:invalid-field-name\",\"field name is missing\",{field:e});if(!w.jsonValues.includes(e.type))throw new t(\"ogc-feature-layer:invalid-field-type\",`invalid type for field \"${e.name}\"`,{field:e})}if(F){const e=new y(u);if(F.startTimeField){const t=e.get(F.startTimeField);t?(F.startTimeField=t.name,t.type=\"esriFieldTypeDate\"):F.startTimeField=null}if(F.endTimeField){const t=e.get(F.endTimeField);t?(F.endTimeField=t.name,t.type=\"esriFieldTypeDate\"):F.endTimeField=null}if(F.trackIdField){const t=e.get(F.trackIdField);t?F.trackIdField=t.name:(F.trackIdField=null,h.warn({name:\"ogc-feature-layer:invalid-timeInfo-trackIdField\",message:\"trackIdField is missing\",details:{timeInfo:F}}))}F.startTimeField||F.endTimeField||(h.warn({name:\"ogc-feature-layer:invalid-timeInfo\",message:\"startTimeField and endTimeField are missing\",details:{timeInfo:F}}),F=null)}return{drawingInfo:b?g(b):null,extent:W(n),geometryType:b,fields:u,hasZ:!!f,objectIdField:j,timeInfo:F}}async function T(n,r={}){const{links:a}=n,o=D(a,\"data\",\"application/json\")||D(a,\"http://www.opengis.net/def/rel/ogc/1.0/data\",\"application/json\");if(i(o))throw new t(\"ogc-feature-layer:missing-collections-page\",\"Missing collections url\");const{apiKey:s,customParameters:l,signal:c}=r,{data:d}=await e(o.href,{signal:c,headers:{accept:\"application/json\"},query:{...l,token:s}});return d}async function k(n,r={}){const{links:a}=n,o=D(a,\"conformance\",\"application/json\")||D(a,\"http://www.opengis.net/def/rel/ogc/1.0/conformance\",\"application/json\");if(i(o))throw new t(\"ogc-feature-layer:missing-conformance-page\",\"Missing conformance url\");const{apiKey:s,customParameters:l,signal:c}=r,{data:d}=await e(o.href,{signal:c,headers:{accept:\"application/json\"},query:{...l,token:s}});return d}async function x(t,n={}){const{apiKey:i,customParameters:r,signal:a}=n,{data:o}=await e(t,{signal:a,headers:{accept:\"application/json\"},query:{...r,token:i}});return o}async function S(t,n={}){const r=\"application/vnd.oai.openapi+json;version=3.0\",a=D(t.links,\"service-desc\",r);if(i(a))return h.warn(\"ogc-feature-layer:missing-openapi-page\",\"The OGC API-Features server does not have an OpenAPI page.\"),null;const{apiKey:o,customParameters:s,signal:l}=n,{data:c}=await e(a.href,{signal:l,headers:{accept:r},query:{...s,token:o}});return c}function v(e){const t=/^http:\\/\\/www\\.opengis.net\\/def\\/crs\\/(?<authority>.*)\\/(?<version>.*)\\/(?<code>.*)$/i.exec(e)?.groups;if(!t)return null;const{authority:n,code:i}=t;switch(n.toLowerCase()){case\"ogc\":switch(i.toLowerCase()){case\"crs27\":return b.GCS_NAD_1927.wkid;case\"crs83\":return 4269;case\"crs84\":case\"crs84h\":return b.WGS84.wkid;default:return null}case\"esri\":case\"epsg\":{const e=Number.parseInt(i,10);return Number.isNaN(e)?null:e}default:return null}}async function N(e,t,n){const i=await q(e,t,n);return l(i)}async function q(n,l,m){const{collection:p,layerDefinition:g,maxRecordCount:y,queryParameters:{apiKey:w,customParameters:h},spatialReference:j,supportedCrs:F}=n,{links:I}=p,T=D(I,\"items\",\"application/geo+json\")||D(I,\"http://www.opengis.net/def/rel/ogc/1.0/items\",\"application/geo+json\");if(i(T))throw new t(\"ogc-feature-layer:missing-items-page\",\"Missing items url\");const{geometry:k,num:x,start:S,timeExtent:v,where:N}=l;if(l.objectIds)throw new t(\"ogc-feature-layer:query-by-objectids-not-supported\",\"Queries with objectids are not supported\");const q=b.fromJSON(j),O=r(l.outSpatialReference,q),C=O.isWGS84?null:R(O,F),W=G(k,F),P=M(v),Z=$(N),K=x??(null!=S&&void 0!==S?10:y),{data:L}=await e(T.href,{...m,query:{...h,...W,crs:C,datetime:P,query:Z,limit:K,startindex:S,token:w},headers:{accept:\"application/geo+json\"}});let J=!1;if(L.links){const e=L.links.find((e=>\"next\"===e.rel));J=!!e}!J&&Number.isInteger(L.numberMatched)&&Number.isInteger(L.numberReturned)&&(J=L.numberReturned<L.numberMatched);const{fields:z,geometryType:A,hasZ:E,objectIdField:U}=g,_=f(L,{geometryType:A,hasZ:E,objectIdField:U});if(!C&&O.isWebMercator)for(const e of _)if(a(e.geometry)&&null!=A){const t=c(e.geometry,A,E,!1);t.spatialReference=b.WGS84,e.geometry=d(s(t,O))}for(const e of _)e.objectId=e.attributes[U];const B=C||!C&&O.isWebMercator?O.toJSON():o,Q=new u;return Q.exceededTransferLimit=J,Q.features=_,Q.fields=z,Q.geometryType=A,Q.hasZ=E,Q.objectIdFieldName=U,Q.spatialReference=B,Q}function O(e){return a(e)&&\"extent\"===e.type}function R(e,t){const{isWebMercator:n,wkid:i}=e;if(!i)return null;const r=n?t[3857]??t[102100]??t[102113]??t[900913]:t[e.wkid];return r?`${j}${r}`:null}function C(e){if(i(e))return\"\";const{xmin:t,ymin:n,xmax:r,ymax:a}=e;return`${t},${n},${r},${a}`}function M(e){if(i(e))return null;const{start:t,end:n}=e;return`${a(t)?t.toISOString():\"..\"}/${a(n)?n.toISOString():\"..\"}`}function $(e){return i(e)||!e||\"1=1\"===e?null:e}function G(e,t){if(!O(e))return null;const{spatialReference:n}=e;if(!n||n.isWGS84)return{bbox:C(e)};const i=R(n,t);return a(i)?{bbox:C(e),\"bbox-crs\":i}:n.isWebMercator?{bbox:C(s(e,b.WGS84))}:null}function W(e){const t=e.extent?.spatial;if(!t)return null;const n=t.bbox[0],i=4===n.length,r=n[0],a=n[1],o=i?void 0:n[2];return{xmin:r,ymin:a,xmax:i?n[2]:n[3],ymax:i?n[3]:n[4],zmin:o,zmax:i?void 0:n[5],spatialReference:b.WGS84.toJSON()}}function D(e,t,n){return e.find((e=>e.rel===t&&e.type===n))||e.find((e=>e.rel===t&&!e.type))}export{F as crsDefault,j as crsPrefix,I as getCollectionDefinition,T as getServerCollections,k as getServerConformance,x as getServerLandingPage,S as getServerOpenApi,v as getSpatialReferenceWkid,N as queryFeatureSetJSON,q as queryOptimizedFeatureSet};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIw2B,IAAM,IAAE,EAAE,UAAU,yCAAyC;AAA7D,IAA+D,IAAE;AAAjE,IAAmG,IAAE,GAAG,CAAC;AAAgB,eAAeA,GAAE,GAAEC,IAAE,IAAE,CAAC,GAAEC,KAAE,GAAE;AAAC,QAAK,EAAC,OAAMC,GAAC,IAAE,GAAEC,KAAE,EAAED,IAAE,SAAQ,sBAAsB,KAAG,EAAEA,IAAE,gDAA+C,sBAAsB;AAAE,MAAG,EAAEC,EAAC,EAAE,OAAM,IAAID,GAAE,wCAAuC,mBAAmB;AAAE,QAAK,EAAC,MAAKE,GAAC,IAAE,MAAM,EAAED,GAAE,MAAK,EAAC,QAAO,EAAE,QAAO,OAAM,EAAC,OAAMF,IAAE,GAAG,EAAE,kBAAiB,OAAM,EAAE,OAAM,GAAE,SAAQ,EAAC,QAAO,uBAAsB,EAAC,CAAC;AAAE,QAAM,EAAEG,EAAC;AAAE,QAAM,IAAE,EAAEA,IAAE,EAAC,cAAaJ,GAAE,aAAY,CAAC,GAAE,IAAEA,GAAE,UAAQ,EAAE,UAAQ,CAAC,GAAEK,KAAE,QAAML,GAAE,OAAKA,GAAE,OAAK,EAAE,MAAK,IAAE,EAAE,cAAaM,KAAEN,GAAE,iBAAe,EAAE,qBAAmB;AAAW,MAAIO,KAAEP,GAAE;AAAS,QAAMD,KAAE,EAAE,KAAM,CAAC,EAAC,MAAKS,GAAC,MAAIA,OAAIF,EAAE;AAAE,MAAGP,GAAE,CAAAA,GAAE,WAAS,OAAGA,GAAE,WAAS;AAAA,OAAO;AAAC,QAAG,CAAC,EAAE,kBAAkB,OAAM,IAAIG,GAAE,wCAAuC,gEAAgE;AAAE,MAAE,QAAQ,EAAC,MAAKI,IAAE,OAAMA,IAAE,MAAK,aAAW,EAAE,oBAAkB,qBAAmB,uBAAsB,UAAS,OAAG,UAAS,MAAE,CAAC;AAAA,EAAC;AAAC,MAAGA,OAAI,EAAE,mBAAkB;AAAC,UAAME,KAAE,EAAE,KAAM,CAAC,EAAC,MAAKA,GAAC,MAAIA,OAAI,EAAE,iBAAkB;AAAE,IAAAA,OAAIA,GAAE,OAAK;AAAA,EAAuB;AAAC,QAAI,EAAE,UAAQ,EAAE,cAAc,SAAO,KAAG,EAAE,KAAK,EAAC,MAAK,yCAAwC,SAAQ,6EAA4E,SAAQ,EAAC,eAAc,EAAE,cAAa,EAAC,CAAC;AAAE,aAAUA,MAAK,GAAE;AAAC,QAAG,QAAMA,GAAE,SAAOA,GAAE,OAAKA,GAAE,QAAO,QAAMA,GAAE,UAAQA,GAAE,QAAMA,GAAE,OAAM,uBAAqBA,GAAE,QAAM,4BAA0BA,GAAE,SAAOA,GAAE,WAAS,QAAMA,GAAE,YAAU,CAAC,CAACA,GAAE,UAASA,GAAE,WAAS,QAAMA,GAAE,YAAU,CAAC,CAACA,GAAE,WAAU,CAACA,GAAE,KAAK,OAAM,IAAIN,GAAE,wCAAuC,yBAAwB,EAAC,OAAMM,GAAC,CAAC;AAAE,QAAG,CAAC,EAAE,WAAW,SAASA,GAAE,IAAI,EAAE,OAAM,IAAIN,GAAE,wCAAuC,2BAA2BM,GAAE,IAAI,KAAI,EAAC,OAAMA,GAAC,CAAC;AAAA,EAAC;AAAC,MAAGD,IAAE;AAAC,UAAMC,KAAE,IAAIR,GAAE,CAAC;AAAE,QAAGO,GAAE,gBAAe;AAAC,YAAME,KAAED,GAAE,IAAID,GAAE,cAAc;AAAE,MAAAE,MAAGF,GAAE,iBAAeE,GAAE,MAAKA,GAAE,OAAK,uBAAqBF,GAAE,iBAAe;AAAA,IAAI;AAAC,QAAGA,GAAE,cAAa;AAAC,YAAME,KAAED,GAAE,IAAID,GAAE,YAAY;AAAE,MAAAE,MAAGF,GAAE,eAAaE,GAAE,MAAKA,GAAE,OAAK,uBAAqBF,GAAE,eAAa;AAAA,IAAI;AAAC,QAAGA,GAAE,cAAa;AAAC,YAAME,KAAED,GAAE,IAAID,GAAE,YAAY;AAAE,MAAAE,KAAEF,GAAE,eAAaE,GAAE,QAAMF,GAAE,eAAa,MAAK,EAAE,KAAK,EAAC,MAAK,mDAAkD,SAAQ,2BAA0B,SAAQ,EAAC,UAASA,GAAC,EAAC,CAAC;AAAA,IAAE;AAAC,IAAAA,GAAE,kBAAgBA,GAAE,iBAAe,EAAE,KAAK,EAAC,MAAK,sCAAqC,SAAQ,+CAA8C,SAAQ,EAAC,UAASA,GAAC,EAAC,CAAC,GAAEA,KAAE;AAAA,EAAK;AAAC,SAAM,EAAC,aAAY,IAAE,EAAE,CAAC,IAAE,MAAK,QAAO,EAAE,CAAC,GAAE,cAAa,GAAE,QAAO,GAAE,MAAK,CAAC,CAACF,IAAE,eAAcC,IAAE,UAASC,GAAC;AAAC;AAAC,eAAeG,GAAE,GAAEV,KAAE,CAAC,GAAE;AAAC,QAAK,EAAC,OAAM,EAAC,IAAE,GAAEC,KAAE,EAAE,GAAE,QAAO,kBAAkB,KAAG,EAAE,GAAE,+CAA8C,kBAAkB;AAAE,MAAG,EAAEA,EAAC,EAAE,OAAM,IAAIC,GAAE,8CAA6C,yBAAyB;AAAE,QAAK,EAAC,QAAOA,IAAE,kBAAiBC,IAAE,QAAOC,GAAC,IAAEJ,IAAE,EAAC,MAAK,EAAC,IAAE,MAAM,EAAEC,GAAE,MAAK,EAAC,QAAOG,IAAE,SAAQ,EAAC,QAAO,mBAAkB,GAAE,OAAM,EAAC,GAAGD,IAAE,OAAMD,GAAC,EAAC,CAAC;AAAE,SAAO;AAAC;AAAC,eAAe,EAAE,GAAEF,KAAE,CAAC,GAAE;AAAC,QAAK,EAAC,OAAM,EAAC,IAAE,GAAEC,KAAE,EAAE,GAAE,eAAc,kBAAkB,KAAG,EAAE,GAAE,sDAAqD,kBAAkB;AAAE,MAAG,EAAEA,EAAC,EAAE,OAAM,IAAIC,GAAE,8CAA6C,yBAAyB;AAAE,QAAK,EAAC,QAAOA,IAAE,kBAAiBC,IAAE,QAAOC,GAAC,IAAEJ,IAAE,EAAC,MAAK,EAAC,IAAE,MAAM,EAAEC,GAAE,MAAK,EAAC,QAAOG,IAAE,SAAQ,EAAC,QAAO,mBAAkB,GAAE,OAAM,EAAC,GAAGD,IAAE,OAAMD,GAAC,EAAC,CAAC;AAAE,SAAO;AAAC;AAAC,eAAe,EAAEO,IAAE,IAAE,CAAC,GAAE;AAAC,QAAK,EAAC,QAAOE,IAAE,kBAAiBX,IAAE,QAAO,EAAC,IAAE,GAAE,EAAC,MAAKC,GAAC,IAAE,MAAM,EAAEQ,IAAE,EAAC,QAAO,GAAE,SAAQ,EAAC,QAAO,mBAAkB,GAAE,OAAM,EAAC,GAAGT,IAAE,OAAMW,GAAC,EAAC,CAAC;AAAE,SAAOV;AAAC;AAAC,eAAe,EAAEQ,IAAE,IAAE,CAAC,GAAE;AAAC,QAAMT,KAAE,gDAA+C,IAAE,EAAES,GAAE,OAAM,gBAAeT,EAAC;AAAE,MAAG,EAAE,CAAC,EAAE,QAAO,EAAE,KAAK,0CAAyC,4DAA4D,GAAE;AAAK,QAAK,EAAC,QAAOC,IAAE,kBAAiBC,IAAE,QAAOC,GAAC,IAAE,GAAE,EAAC,MAAKC,GAAC,IAAE,MAAM,EAAE,EAAE,MAAK,EAAC,QAAOD,IAAE,SAAQ,EAAC,QAAOH,GAAC,GAAE,OAAM,EAAC,GAAGE,IAAE,OAAMD,GAAC,EAAC,CAAC;AAAE,SAAOG;AAAC;AAAC,SAAS,EAAEI,IAAE;AAJ3sJ;AAI4sJ,QAAMC,MAAE,6FAAwF,KAAKD,EAAC,MAA9F,mBAAiG;AAAO,MAAG,CAACC,GAAE,QAAO;AAAK,QAAK,EAAC,WAAU,GAAE,MAAKE,GAAC,IAAEF;AAAE,UAAO,EAAE,YAAY,GAAE;AAAA,IAAC,KAAI;AAAM,cAAOE,GAAE,YAAY,GAAE;AAAA,QAAC,KAAI;AAAQ,iBAAO,EAAE,aAAa;AAAA,QAAK,KAAI;AAAQ,iBAAO;AAAA,QAAK,KAAI;AAAA,QAAQ,KAAI;AAAS,iBAAO,EAAE,MAAM;AAAA,QAAK;AAAQ,iBAAO;AAAA,MAAI;AAAA,IAAC,KAAI;AAAA,IAAO,KAAI,QAAO;AAAC,YAAMH,KAAE,OAAO,SAASG,IAAE,EAAE;AAAE,aAAO,OAAO,MAAMH,EAAC,IAAE,OAAKA;AAAA,IAAC;AAAA,IAAC;AAAQ,aAAO;AAAA,EAAI;AAAC;AAAC,eAAe,EAAEA,IAAEC,IAAE,GAAE;AAAC,QAAME,KAAE,MAAM,EAAEH,IAAEC,IAAE,CAAC;AAAE,SAAO,GAAEE,EAAC;AAAC;AAAC,eAAe,EAAE,GAAER,IAAE,GAAE;AAAC,QAAK,EAAC,YAAW,GAAE,iBAAgB,GAAE,gBAAe,GAAE,iBAAgB,EAAC,QAAO,GAAE,kBAAiBS,GAAC,GAAE,kBAAiBN,IAAE,cAAaC,GAAC,IAAE,GAAE,EAAC,OAAMR,GAAC,IAAE,GAAEW,KAAE,EAAEX,IAAE,SAAQ,sBAAsB,KAAG,EAAEA,IAAE,gDAA+C,sBAAsB;AAAE,MAAG,EAAEW,EAAC,EAAE,OAAM,IAAIR,GAAE,wCAAuC,mBAAmB;AAAE,QAAK,EAAC,UAASW,IAAE,KAAIC,IAAE,OAAMC,IAAE,YAAWC,IAAE,OAAMC,GAAC,IAAEd;AAAE,MAAGA,GAAE,UAAU,OAAM,IAAID,GAAE,sDAAqD,0CAA0C;AAAE,QAAMgB,KAAE,EAAE,SAASZ,EAAC,GAAEa,KAAE,EAAEhB,GAAE,qBAAoBe,EAAC,GAAEE,KAAED,GAAE,UAAQ,OAAK,EAAEA,IAAEZ,EAAC,GAAEc,KAAE,EAAER,IAAEN,EAAC,GAAE,IAAEe,GAAEN,EAAC,GAAE,IAAE,EAAEC,EAAC,GAAE,IAAEH,OAAI,QAAMC,MAAG,WAASA,KAAE,KAAG,IAAG,EAAC,MAAKQ,GAAC,IAAE,MAAM,EAAEb,GAAE,MAAK,EAAC,GAAG,GAAE,OAAM,EAAC,GAAGE,IAAE,GAAGS,IAAE,KAAID,IAAE,UAAS,GAAE,OAAM,GAAE,OAAM,GAAE,YAAWL,IAAE,OAAM,EAAC,GAAE,SAAQ,EAAC,QAAO,uBAAsB,EAAC,CAAC;AAAE,MAAI,IAAE;AAAG,MAAGQ,GAAE,OAAM;AAAC,UAAMf,KAAEe,GAAE,MAAM,KAAM,CAAAf,OAAG,WAASA,GAAE,GAAI;AAAE,QAAE,CAAC,CAACA;AAAA,EAAC;AAAC,GAAC,KAAG,OAAO,UAAUe,GAAE,aAAa,KAAG,OAAO,UAAUA,GAAE,cAAc,MAAI,IAAEA,GAAE,iBAAeA,GAAE;AAAe,QAAK,EAAC,QAAO,GAAE,cAAa,GAAE,MAAK,GAAE,eAAcC,GAAC,IAAE,GAAE,IAAE,EAAED,IAAE,EAAC,cAAa,GAAE,MAAK,GAAE,eAAcC,GAAC,CAAC;AAAE,MAAG,CAACJ,MAAGD,GAAE;AAAc,eAAUX,MAAK,EAAE,KAAG,EAAEA,GAAE,QAAQ,KAAG,QAAM,GAAE;AAAC,YAAMC,KAAE,GAAED,GAAE,UAAS,GAAE,GAAE,KAAE;AAAE,MAAAC,GAAE,mBAAiB,EAAE,OAAMD,GAAE,WAAS,GAAE,EAAEC,IAAEU,EAAC,CAAC;AAAA,IAAC;AAAA;AAAC,aAAUX,MAAK,EAAE,CAAAA,GAAE,WAASA,GAAE,WAAWgB,EAAC;AAAE,QAAM,IAAEJ,MAAG,CAACA,MAAGD,GAAE,gBAAcA,GAAE,OAAO,IAAE,GAAE,IAAE,IAAI;AAAE,SAAO,EAAE,wBAAsB,GAAE,EAAE,WAAS,GAAE,EAAE,SAAO,GAAE,EAAE,eAAa,GAAE,EAAE,OAAK,GAAE,EAAE,oBAAkBK,IAAE,EAAE,mBAAiB,GAAE;AAAC;AAAC,SAAS,EAAEhB,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG,aAAWA,GAAE;AAAI;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,QAAK,EAAC,eAAc,GAAE,MAAKE,GAAC,IAAEH;AAAE,MAAG,CAACG,GAAE,QAAO;AAAK,QAAMX,KAAE,IAAES,GAAE,IAAI,KAAGA,GAAE,MAAM,KAAGA,GAAE,MAAM,KAAGA,GAAE,MAAM,IAAEA,GAAED,GAAE,IAAI;AAAE,SAAOR,KAAE,GAAG,CAAC,GAAGA,EAAC,KAAG;AAAI;AAAC,SAAS,EAAEQ,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAM;AAAG,QAAK,EAAC,MAAKC,IAAE,MAAK,GAAE,MAAKT,IAAE,MAAK,EAAC,IAAEQ;AAAE,SAAM,GAAGC,EAAC,IAAI,CAAC,IAAIT,EAAC,IAAI,CAAC;AAAE;AAAC,SAASsB,GAAEd,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAO;AAAK,QAAK,EAAC,OAAMC,IAAE,KAAI,EAAC,IAAED;AAAE,SAAM,GAAG,EAAEC,EAAC,IAAEA,GAAE,YAAY,IAAE,IAAI,IAAI,EAAE,CAAC,IAAE,EAAE,YAAY,IAAE,IAAI;AAAE;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG,CAACA,MAAG,UAAQA,KAAE,OAAKA;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,MAAG,CAAC,EAAED,EAAC,EAAE,QAAO;AAAK,QAAK,EAAC,kBAAiB,EAAC,IAAEA;AAAE,MAAG,CAAC,KAAG,EAAE,QAAQ,QAAM,EAAC,MAAK,EAAEA,EAAC,EAAC;AAAE,QAAMG,KAAE,EAAE,GAAEF,EAAC;AAAE,SAAO,EAAEE,EAAC,IAAE,EAAC,MAAK,EAAEH,EAAC,GAAE,YAAWG,GAAC,IAAE,EAAE,gBAAc,EAAC,MAAK,EAAE,EAAEH,IAAE,EAAE,KAAK,CAAC,EAAC,IAAE;AAAI;AAAC,SAAS,EAAEA,IAAE;AAJxyO;AAIyyO,QAAMC,MAAE,KAAAD,GAAE,WAAF,mBAAU;AAAQ,MAAG,CAACC,GAAE,QAAO;AAAK,QAAM,IAAEA,GAAE,KAAK,CAAC,GAAEE,KAAE,MAAI,EAAE,QAAOX,KAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAEC,KAAEU,KAAE,SAAO,EAAE,CAAC;AAAE,SAAM,EAAC,MAAKX,IAAE,MAAK,GAAE,MAAKW,KAAE,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,MAAKA,KAAE,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,MAAKV,IAAE,MAAKU,KAAE,SAAO,EAAE,CAAC,GAAE,kBAAiB,EAAE,MAAM,OAAO,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAE,GAAE;AAAC,SAAOD,GAAE,KAAM,CAAAA,OAAGA,GAAE,QAAMC,MAAGD,GAAE,SAAO,CAAE,KAAGA,GAAE,KAAM,CAAAA,OAAGA,GAAE,QAAMC,MAAG,CAACD,GAAE,IAAK;AAAC;", "names": ["I", "r", "o", "s", "l", "c", "f", "j", "F", "e", "t", "T", "i", "h", "k", "x", "S", "v", "N", "q", "O", "C", "W", "M", "L", "U"]}