import {
  a as a2,
  a2 as a3,
  a3 as a4,
  d as d2
} from "./chunk-V434P7FU.js";
import {
  E,
  L
} from "./chunk-HG37JYSR.js";
import "./chunk-I73RJE3H.js";
import "./chunk-OWSDEANX.js";
import "./chunk-SJMI5Q5M.js";
import "./chunk-PCLDCFRI.js";
import "./chunk-ZJC3GHA7.js";
import {
  s as s3
} from "./chunk-FZKLUDSB.js";
import {
  i
} from "./chunk-RR74IWZB.js";
import {
  p as p3
} from "./chunk-KTB2COPC.js";
import "./chunk-HTXGAKOK.js";
import {
  p as p4
} from "./chunk-JZKMTUDN.js";
import "./chunk-UCWK623G.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import "./chunk-55WN4LCX.js";
import {
  c as c2
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  t
} from "./chunk-NGPCXWDX.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import {
  c,
  d,
  p as p2
} from "./chunk-VJW7RCN7.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-3WEGNHPY.js";
import {
  i4 as i2,
  k
} from "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import "./chunk-XVA5SA7P.js";
import {
  V
} from "./chunk-U4SVMKOQ.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import {
  o as o3
} from "./chunk-PEEUPDEG.js";
import {
  o as o2
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  o,
  p
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/pointCloudFilters/PointCloudFilter.js
var t2 = class extends l {
  constructor(r3) {
    super(r3), this.field = null, this.type = null;
  }
  clone() {
    return console.warn(".clone() is not implemented for " + this.declaredClass), null;
  }
};
e([y({ type: String, json: { write: { enabled: true, isRequired: true } } })], t2.prototype, "field", void 0), e([y({ readOnly: true, nonNullable: true, json: { read: false } })], t2.prototype, "type", void 0), t2 = e([a("esri.layers.pointCloudFilters.PointCloudFilter")], t2);
var l2 = t2;

// node_modules/@arcgis/core/layers/pointCloudFilters/PointCloudBitfieldFilter.js
var d3;
var p5 = d3 = class extends l2 {
  constructor(e3) {
    super(e3), this.requiredClearBits = null, this.requiredSetBits = null, this.type = "bitfield";
  }
  clone() {
    return new d3({ field: this.field, requiredClearBits: p(this.requiredClearBits), requiredSetBits: p(this.requiredSetBits) });
  }
};
e([y({ type: [T], json: { write: { enabled: true, overridePolicy() {
  return { enabled: true, isRequired: !this.requiredSetBits };
} } } })], p5.prototype, "requiredClearBits", void 0), e([y({ type: [T], json: { write: { enabled: true, overridePolicy() {
  return { enabled: true, isRequired: !this.requiredClearBits };
} } } })], p5.prototype, "requiredSetBits", void 0), e([o3({ pointCloudBitfieldFilter: "bitfield" })], p5.prototype, "type", void 0), p5 = d3 = e([a("esri.layers.pointCloudFilters.PointCloudBitfieldFilter")], p5);
var u = p5;

// node_modules/@arcgis/core/layers/pointCloudFilters/PointCloudReturnFilter.js
var n;
var p6 = n = class extends l2 {
  constructor(r3) {
    super(r3), this.includedReturns = [], this.type = "return";
  }
  clone() {
    return new n({ field: this.field, includedReturns: p(this.includedReturns) });
  }
};
e([y({ type: [["firstOfMany", "last", "lastOfMany", "single"]], json: { write: { enabled: true, isRequired: true } } })], p6.prototype, "includedReturns", void 0), e([o3({ pointCloudReturnFilter: "return" })], p6.prototype, "type", void 0), p6 = n = e([a("esri.layers.pointCloudFilters.PointCloudReturnFilter")], p6);
var u2 = p6;

// node_modules/@arcgis/core/layers/pointCloudFilters/PointCloudValueFilter.js
var p7;
var l3 = p7 = class extends l2 {
  constructor(e3) {
    super(e3), this.mode = "exclude", this.type = "value", this.values = [];
  }
  clone() {
    return new p7({ field: this.field, mode: this.mode, values: p(this.values) });
  }
};
e([y({ type: ["exclude", "include"], json: { write: { enabled: true, isRequired: true } } })], l3.prototype, "mode", void 0), e([o3({ pointCloudValueFilter: "value" })], l3.prototype, "type", void 0), e([y({ type: [Number], json: { write: { enabled: true, isRequired: true } } })], l3.prototype, "values", void 0), l3 = p7 = e([a("esri.layers.pointCloudFilters.PointCloudValueFilter")], l3);
var u3 = l3;

// node_modules/@arcgis/core/layers/pointCloudFilters/typeUtils.js
var e2 = { key: "type", base: l2, typeMap: { value: u3, bitfield: u, return: u2 } };

// node_modules/@arcgis/core/renderers/PointCloudRGBRenderer.js
var i3;
var c3 = i3 = class extends a2 {
  constructor(r3) {
    super(r3), this.type = "point-cloud-rgb", this.field = null;
  }
  clone() {
    return new i3({ ...this.cloneProperties(), field: p(this.field) });
  }
};
e([o3({ pointCloudRGBRenderer: "point-cloud-rgb" })], c3.prototype, "type", void 0), e([y({ type: String, json: { write: true } })], c3.prototype, "field", void 0), c3 = i3 = e([a("esri.renderers.PointCloudRGBRenderer")], c3);
var n2 = c3;

// node_modules/@arcgis/core/renderers/support/pointCloud/typeUtils.js
var i4 = { key: "type", base: a2, typeMap: { "point-cloud-class-breaks": d2, "point-cloud-rgb": n2, "point-cloud-stretch": a3, "point-cloud-unique-value": a4 }, errorContext: "renderer" };

// node_modules/@arcgis/core/layers/PointCloudLayer.js
var A = s3();
var N = class extends E(p3(c2(_(t(O(i(b))))))) {
  constructor(...e3) {
    super(...e3), this.operationalLayerType = "PointCloudLayer", this.popupEnabled = true, this.popupTemplate = null, this.opacity = 1, this.filters = [], this.fields = null, this.fieldsIndex = null, this.outFields = null, this.path = null, this.legendEnabled = true, this.renderer = null, this.type = "point-cloud";
  }
  normalizeCtorArgs(e3, r3) {
    return "string" == typeof e3 ? { url: e3, ...r3 } : e3;
  }
  get defaultPopupTemplate() {
    return this.attributeStorageInfo ? this.createPopupTemplate() : null;
  }
  getFieldDomain(e3) {
    const r3 = this.fieldsIndex.get(e3);
    return r3 && r3.domain ? r3.domain : null;
  }
  readServiceFields(e3, r3, t3) {
    return Array.isArray(e3) ? e3.map((e4) => {
      const r4 = new y2();
      return "FieldTypeInteger" === e4.type && ((e4 = p(e4)).type = "esriFieldTypeInteger"), r4.read(e4, t3), r4;
    }) : Array.isArray(r3.attributeStorageInfo) ? r3.attributeStorageInfo.map((e4) => new y2({ name: e4.name, type: "ELEVATION" === e4.name ? "double" : "integer" })) : null;
  }
  set elevationInfo(e3) {
    this._set("elevationInfo", e3), this._validateElevationInfo();
  }
  writeRenderer(e3, r3, t3, o4) {
    o("layerDefinition.drawingInfo.renderer", e3.write({}, o4), r3);
  }
  load(e3) {
    const r3 = r(e3) ? e3.signal : null, t3 = this.loadFromPortal({ supportedTypes: ["Scene Service"] }, e3).catch(w).then(() => this._fetchService(r3));
    return this.addResolvingPromise(t3), Promise.resolve(this);
  }
  createPopupTemplate(e3) {
    const r3 = p4(this, e3);
    return r3 && (this._formatPopupTemplateReturnsField(r3), this._formatPopupTemplateRGBField(r3)), r3;
  }
  _formatPopupTemplateReturnsField(e3) {
    var _a;
    const r3 = this.fieldsIndex.get("RETURNS");
    if (!r3) return;
    const t3 = (_a = e3.fieldInfos) == null ? void 0 : _a.find((e4) => e4.fieldName === r3.name);
    if (!t3) return;
    const o4 = new i2({ name: "pcl-returns-decoded", title: r3.alias || r3.name, expression: `
        var returnValue = $feature.${r3.name};
        return (returnValue % 16) + " / " + Floor(returnValue / 16);
      ` });
    e3.expressionInfos = [...e3.expressionInfos || [], o4], t3.fieldName = "expression/pcl-returns-decoded";
  }
  _formatPopupTemplateRGBField(e3) {
    var _a;
    const r3 = this.fieldsIndex.get("RGB");
    if (!r3) return;
    const t3 = (_a = e3.fieldInfos) == null ? void 0 : _a.find((e4) => e4.fieldName === r3.name);
    if (!t3) return;
    const o4 = new i2({ name: "pcl-rgb-decoded", title: r3.alias || r3.name, expression: `
        var rgb = $feature.${r3.name};
        var red = Floor(rgb / 65536, 0);
        var green = Floor((rgb - (red * 65536)) / 256,0);
        var blue = rgb - (red * 65536) - (green * 256);

        return "rgb(" + red + "," + green + "," + blue + ")";
      ` });
    e3.expressionInfos = [...e3.expressionInfos || [], o4], t3.fieldName = "expression/pcl-rgb-decoded";
  }
  async queryCachedStatistics(e3, r3) {
    if (await this.load(r3), !this.attributeStorageInfo) throw new s2("scenelayer:no-cached-statistics", "Cached statistics are not available for this layer");
    const i5 = this.fieldsIndex.get(e3);
    if (!i5) throw new s2("pointcloudlayer:field-unexisting", `Field '${e3}' does not exist on the layer`);
    for (const o4 of this.attributeStorageInfo) if (o4.name === i5.name) {
      const e4 = V(this.parsedUrl.path, `./statistics/${o4.key}`);
      return U(e4, { query: { f: "json", token: this.apiKey }, responseType: "json", signal: r3 ? r3.signal : null }).then((e5) => e5.data);
    }
    throw new s2("pointcloudlayer:no-cached-statistics", "Cached statistics for this attribute are not available");
  }
  async saveAs(e3, r3) {
    return this._debouncedSaveOperations(L.SAVE_AS, { ...r3, getTypeKeywords: () => this._getTypeKeywords(), portalItemLayerType: "point-cloud" }, e3);
  }
  async save() {
    const e3 = { getTypeKeywords: () => this._getTypeKeywords(), portalItemLayerType: "point-cloud" };
    return this._debouncedSaveOperations(L.SAVE, e3);
  }
  validateLayer(e3) {
    if (e3.layerType && "PointCloud" !== e3.layerType) throw new s2("pointcloudlayer:layer-type-not-supported", "PointCloudLayer does not support this layer type", { layerType: e3.layerType });
    if (isNaN(this.version.major) || isNaN(this.version.minor)) throw new s2("layer:service-version-not-supported", "Service version is not supported.", { serviceVersion: this.version.versionString, supportedVersions: "1.x-2.x" });
    if (this.version.major > 2) throw new s2("layer:service-version-too-new", "Service version is too new.", { serviceVersion: this.version.versionString, supportedVersions: "1.x-2.x" });
  }
  hasCachedStatistics(e3) {
    return null != this.attributeStorageInfo && this.attributeStorageInfo.some((r3) => r3.name === e3);
  }
  _getTypeKeywords() {
    return ["PointCloud"];
  }
  _validateElevationInfo() {
    const e3 = this.elevationInfo;
    e3 && ("absolute-height" !== e3.mode && s.getLogger(this.declaredClass).warn(".elevationInfo=", "Point cloud layers only support absolute-height elevation mode"), e3.featureExpressionInfo && "0" !== e3.featureExpressionInfo.expression && s.getLogger(this.declaredClass).warn(".elevationInfo=", "Point cloud layers do not support featureExpressionInfo"));
  }
};
e([y({ type: ["PointCloudLayer"] })], N.prototype, "operationalLayerType", void 0), e([y(p2)], N.prototype, "popupEnabled", void 0), e([y({ type: k, json: { name: "popupInfo", write: true } })], N.prototype, "popupTemplate", void 0), e([y({ readOnly: true, json: { read: false } })], N.prototype, "defaultPopupTemplate", null), e([y({ readOnly: true, json: { write: false, read: false, origins: { "web-document": { write: false, read: false } } } })], N.prototype, "opacity", void 0), e([y({ type: ["show", "hide"] })], N.prototype, "listMode", void 0), e([y({ types: [e2], json: { origins: { service: { read: { source: "filters" } } }, name: "layerDefinition.filters", write: true } })], N.prototype, "filters", void 0), e([y({ type: [y2] })], N.prototype, "fields", void 0), e([y(A.fieldsIndex)], N.prototype, "fieldsIndex", void 0), e([o2("service", "fields", ["fields", "attributeStorageInfo"])], N.prototype, "readServiceFields", null), e([y(A.outFields)], N.prototype, "outFields", void 0), e([y({ readOnly: true })], N.prototype, "attributeStorageInfo", void 0), e([y(d)], N.prototype, "elevationInfo", null), e([y({ type: String, json: { origins: { "web-scene": { read: true, write: true }, "portal-item": { read: true, write: true } }, read: false } })], N.prototype, "path", void 0), e([y(c)], N.prototype, "legendEnabled", void 0), e([y({ types: i4, json: { origins: { service: { read: { source: "drawingInfo.renderer" } } }, name: "layerDefinition.drawingInfo.renderer", write: { target: { "layerDefinition.drawingInfo.renderer": { types: i4 }, "layerDefinition.drawingInfo.transparency": { type: Number } } } } })], N.prototype, "renderer", void 0), e([r2("renderer")], N.prototype, "writeRenderer", null), e([y({ json: { read: false }, readOnly: true })], N.prototype, "type", void 0), N = e([a("esri.layers.PointCloudLayer")], N);
var R = N;
export {
  R as default
};
//# sourceMappingURL=PointCloudLayer-VHBLYRCG.js.map
