{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/features/support/FeatureSetReaderJSON.js", "../../@arcgis/core/views/2d/layers/features/support/AttributeStore.js", "../../@arcgis/core/views/2d/layers/features/support/DisplayIdGenerator.js", "../../@arcgis/core/views/2d/layers/features/support/ComputedAttributeStorage.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t,isSome as e}from\"../../../../../core/maybe.js\";import{convertFromFeatures as r,convertFromFeatureSet as s,convertToFeature as i,convertToGeometry as n,getQuantizedArea as o,unquantizeOptimizedGeometry as a}from\"../../../../../layers/graphics/featureConversionUtils.js\";import{hasGeometry as u}from\"../../../../../layers/graphics/OptimizedFeature.js\";import{FeatureSetReader as h}from\"./FeatureSetReader.js\";function d({coords:t,lengths:e}){let r=0;for(const s of e){for(let e=1;e<s;e++)t[2*(r+e)]+=t[2*(r+e)-2],t[2*(r+e)+1]+=t[2*(r+e)-1];r+=s}}class c extends h{static fromFeatures(t,e){const{objectIdField:s,geometryType:i}=e,n=r([],t,i,!1,!1,s);for(let r=0;r<n.length;r++)n[r].displayId=t[r].displayId;return c.fromOptimizedFeatures(n,e)}static fromFeatureSet(t,e){const r=s(t,e.objectIdField);return c.fromOptimizedFeatureSet(r,e)}static fromOptimizedFeatureSet(t,e){const{features:r}=t,s=c.fromOptimizedFeatures(r,e);s._exceededTransferLimit=t.exceededTransferLimit,s._transform=t.transform;for(const i of t.fields)\"esriFieldTypeDate\"===i.type&&s._dateFields.add(i.name);return s}static fromOptimizedFeatures(t,e,r){const s=h.createInstance(),i=new c(s,t,e);return i._transform=r,i}constructor(t,e,r){super(t,r),this._exceededTransferLimit=!1,this._featureIndex=-1,this._dateFields=new Set,this._geometryType=r?.geometryType,this._features=e}get _current(){return this._features[this._featureIndex]}get geometryType(){return this._geometryType}get hasFeatures(){return!!this._features.length}get hasNext(){return this._featureIndex+1<this._features.length}get exceededTransferLimit(){return this._exceededTransferLimit}get hasZ(){return!1}get hasM(){return!1}removeIds(t){const e=new Set(t);this._features=this._features.filter((t=>!(t.objectId&&e.has(t.objectId))))}append(t){for(const e of t)this._features.push(e)}getSize(){return this._features.length}getCursor(){return this.copy()}getQuantizationTransform(){return this._transform}getAttributeHash(){let t=\"\";for(const e in this._current.attributes)t+=this._current.attributes[e];return t}getIndex(){return this._featureIndex}setIndex(t){this._featureIndex=t}getObjectId(){return this._current?.objectId}getDisplayId(){return this._current.displayId}setDisplayId(t){this._current.displayId=t}getGroupId(){return this._current.groupId}setGroupId(t){this._current.groupId=t}copy(){const t=new c(this.instance,this._features,this.fullSchema());return this.copyInto(t),t}next(){for(;++this._featureIndex<this._features.length&&!this._getExists(););return this._featureIndex<this._features.length}readLegacyFeature(){return i(this._current,this.geometryType,this.hasZ,this.hasM)}readOptimizedFeature(){return this._current}readLegacyPointGeometry(){return this.readGeometry()?{x:this.getX(),y:this.getY()}:null}readLegacyGeometry(){const t=this.readGeometry();return n(t,this.geometryType,this.hasZ,this.hasM)}readLegacyCentroid(){const e=this.readCentroid();return t(e)?null:{x:e.coords[0]*this._sx+this._tx,y:e.coords[1]*this._sy+this._ty}}readGeometryArea(){return u(this._current)?o(this._current.geometry,2):0}readUnquantizedGeometry(){const t=this.readGeometry();if(\"esriGeometryPoint\"===this.geometryType||!t)return t;const e=t.clone();return d(e),e}readHydratedGeometry(){const r=this._current.geometry;if(t(r))return null;const s=r.clone();return e(this._transform)&&a(s,s,this.hasZ,this.hasM,this._transform),s}getXHydrated(){if(!u(this._current))return 0;const e=this._current.geometry.coords[0],r=this.getQuantizationTransform();return t(r)?e:e*r.scale[0]+r.translate[0]}getYHydrated(){if(!u(this._current))return 0;const e=this._current.geometry.coords[1],r=this.getQuantizationTransform();return t(r)?e:r.translate[1]-e*r.scale[1]}getX(){return u(this._current)?this._current.geometry.coords[0]*this._sx+this._tx:0}getY(){return u(this._current)?this._current.geometry.coords[1]*this._sy+this._ty:0}readGeometry(){if(!u(this._current)){if(e(this._current.centroid)){const[t,e]=this._current.centroid.coords;return this.createQuantizedExtrudedQuad(t,e)}return null}const t=this._current.geometry.clone();if(t.isPoint)return t.coords[0]=t.coords[0]*this._sx+this._tx,t.coords[1]=t.coords[1]*this._sy+this._ty,t;let r=0;for(const e of t.lengths)t.coords[2*r]=t.coords[2*r]*this._sx+this._tx,t.coords[2*r+1]=t.coords[2*r+1]*this._sy+this._ty,r+=e;return t}readCentroid(){return u(this._current)?this._computeCentroid():this._current.centroid}hasField(t){if(t in this._current.attributes)return!0;return this.getFieldNames().map((t=>t.toLowerCase())).includes(t.toLowerCase())}getFieldNames(){return Object.keys(this._current.attributes)}_readAttribute(t,e){const r=this._current.attributes[t];if(void 0!==r)return null!=r&&e&&this._dateFields.has(t)?new Date(r):r;const s=this.readAttributes(),i=t?.toLocaleLowerCase().trim();for(const n in s)if(n.toLocaleLowerCase().trim()===i){const t=this._current.attributes[n];return null!=t&&e&&this._dateFields.has(n)?new Date(t):t}}copyInto(t){super.copyInto(t),t._featureIndex=this._featureIndex,t._transform=this._transform,t._dateFields=this._dateFields}_readAttributes(){return this._current.attributes}}export{c as FeatureSetReaderJSON};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../../core/Error.js\";import has from\"../../../../../core/has.js\";import e from\"../../../../../core/Logger.js\";import{clamp as i}from\"../../../../../core/mathUtils.js\";import{isNone as s,isSome as r,mapMany as a,forEachSome as n,applySome as o,unwrap as h}from\"../../../../../core/maybe.js\";import{createResolver as l,isAbortError as u}from\"../../../../../core/promiseUtils.js\";import{diff as d}from\"../../../../../core/accessorSupport/diffUtils.js\";import c from\"../../../../../layers/support/FieldsIndex.js\";import{MAX_FILTERS as p,HIGHLIGHT_FLAG as g,ATTRIBUTE_DATA_VV as _,NAN_MAGIC_NUMBER as f,ATTRIBUTE_DATA_ANIMATION as y,ATTRIBUTE_DATA_GPGPU as m}from\"../../../engine/webgl/definitions.js\";import{DISPLAY_ID_TYPE_AGGREGATE as b,DISPLAY_ID_TYPE_FEATURE as x,getDisplayIdTexel as S,getDisplayIdType as T,getDisplayIdFilterMask as z}from\"../../../engine/webgl/DisplayId.js\";import{getPixelArrayCtor as A}from\"../../../engine/webgl/Utils.js\";import{createDebugLogger as E,DEBUG_ATTR_UPDATES as U}from\"../../../engine/webgl/util/debug.js\";import{getVisualVariableSizeValueRepresentationRatio as w}from\"../tileRenderers/support/visualVariablesUtils.js\";import{PixelType as D}from\"../../../../webgl/enums.js\";const k=e.getLogger(\"esri.views.layers.2d.features.support.AttributeStore\"),F=E(U,k),I={sharedArrayBuffer:has(\"esri-shared-array-buffer\"),atomics:has(\"esri-atomics\")};function B(t,e){return i=>e(t(i))}class C{constructor(t,e,i,s){this.size=0,this.texelSize=4,this.dirtyStart=0,this.dirtyEnd=0;const{pixelType:r,layout:a,textureOnly:n}=s;this.textureOnly=n||!1,this.pixelType=r,this._ctype=e,this.layout=a,this._resetRange(),this._shared=t,this.size=i,n||(this.data=this._initData(r,i,t,e))}get buffer(){return o(this.data,(t=>t.buffer))}unsetComponentAllTexels(t,e){const i=h(this.data);for(let s=0;s<this.size*this.size;s++)i[s*this.texelSize+t]&=~e;this.dirtyStart=0,this.dirtyEnd=this.size*this.size-1}setComponentAllTexels(t,e){const i=h(this.data);for(let s=0;s<this.size*this.size;s++)i[s*this.texelSize+t]|=255&e;this.dirtyStart=0,this.dirtyEnd=this.size*this.size-1}setComponent(t,e,i){const s=h(this.data);for(const r of i)s[r*this.texelSize+t]|=e,this.dirtyStart=Math.min(this.dirtyStart,r),this.dirtyEnd=Math.max(this.dirtyEnd,r)}setComponentTexel(t,e,i){h(this.data)[i*this.texelSize+t]|=e,this.dirtyStart=Math.min(this.dirtyStart,i),this.dirtyEnd=Math.max(this.dirtyEnd,i)}unsetComponentTexel(t,e,i){h(this.data)[i*this.texelSize+t]&=~e,this.dirtyStart=Math.min(this.dirtyStart,i),this.dirtyEnd=Math.max(this.dirtyEnd,i)}getData(t,e){const i=S(t);return h(this.data)[i*this.texelSize+e]}setData(t,e,i){const r=S(t),a=1<<e;0!=(this.layout&a)?s(this.data)||(this.data[r*this.texelSize+e]=i,this.dirtyStart=Math.min(this.dirtyStart,r),this.dirtyEnd=Math.max(this.dirtyEnd,r)):k.error(\"mapview-attributes-store\",\"Tried to set a value for a texel's readonly component\")}lock(){this.pixelType===D.UNSIGNED_BYTE&&this._shared&&I.atomics&&\"local\"!==this._ctype&&Atomics.store(this.data,0,1)}unlock(){this.pixelType===D.UNSIGNED_BYTE&&this._shared&&I.atomics&&\"local\"!==this._ctype&&Atomics.store(this.data,0,0)}expand(t){if(this.size=t,!this.textureOnly){const e=this._initData(this.pixelType,t,this._shared,this._ctype),i=h(this.data);e.set(i),this.data=e}}toMessage(){const t=this.dirtyStart,e=this.dirtyEnd,i=this.texelSize;if(t>e)return null;this._resetRange();const s=!(this._shared||\"local\"===this._ctype),r=this.pixelType,a=this.layout,n=h(this.data);return{start:t,end:e,data:s&&n.slice(t*i,(e+1)*i)||null,pixelType:r,layout:a}}_initData(t,e,i,s){const r=i&&\"local\"!==s?SharedArrayBuffer:ArrayBuffer,a=A(t),n=new a(new r(e*e*4*a.BYTES_PER_ELEMENT));for(let o=0;o<n.length;o+=4)n[o+1]=255;return n}_resetRange(){this.dirtyStart=2147483647,this.dirtyEnd=0}}class M{constructor(t,e,i=(()=>{})){this._client=t,this.config=e,this._notifyChange=i,this._blocks=new Array,this._filters=new Array(p),this._attributeComputeInfo=null,this._targetType=0,this._abortController=new AbortController,this._hasScaleExpr=!1,this._size=32,this._nextUpdate=null,this._currUpdate=null,this._idsToHighlight=new Set;const s=e.supportsTextureFloat?D.FLOAT:D.UNSIGNED_BYTE;F(`Creating AttributeStore ${I.sharedArrayBuffer?\"with\":\"without\"} shared memory`),this._blockDescriptors=[{pixelType:D.UNSIGNED_BYTE,layout:1},{pixelType:D.UNSIGNED_BYTE,layout:15,textureOnly:!0},{pixelType:D.UNSIGNED_BYTE,layout:15,textureOnly:!0},{pixelType:s,layout:15},{pixelType:s,layout:15},{pixelType:s,layout:15},{pixelType:s,layout:15}],this._blocks=this._blockDescriptors.map((()=>null))}destroy(){this._abortController.abort()}get hasScaleExpr(){return this._hasScaleExpr}get _signal(){return this._abortController.signal}get hasHighlight(){return this._idsToHighlight.size>0}isUpdating(){return!!this._currUpdate||!!this._nextUpdate}update(t,e){this.config=e;const i=e.schema.processors[0].storage,a=d(this._schema,i);if((t.targets.feature||t.targets.aggregate)&&(t.storage.data=!0),a&&(has(\"esri-2d-update-debug\")&&console.debug(\"Applying Update - AttributeStore:\",a),t.storage.data=!0,this._schema=i,this._attributeComputeInfo=null,!s(i))){switch(i.target){case\"feature\":this._targetType=x;break;case\"aggregate\":this._targetType=b}if(\"subtype\"===i.type){this._attributeComputeInfo={isSubtype:!0,subtypeField:i.subtypeField,map:new Map};for(const t in i.mapping){const e=i.mapping[t];if(r(e)&&r(e.vvMapping))for(const i of e.vvMapping)this._bindAttribute(i,parseInt(t,10))}}else{if(this._attributeComputeInfo={isSubtype:!1,map:new Map},r(i.vvMapping))for(const t of i.vvMapping)this._bindAttribute(t);if(r(i.attributeMapping))for(const t of i.attributeMapping)this._bindAttribute(t)}}}onTileData(t,e){if(s(e.addOrUpdate))return;const i=e.addOrUpdate.getCursor();for(;i.next();){const t=i.getDisplayId();this.setAttributeData(t,i)}}async setHighlight(t,e){const i=1,s=this._getBlock(0),r=e.map((t=>S(t)));s.lock(),s.unsetComponentAllTexels(0,i),s.setComponent(0,i,r),s.unlock(),this._idsToHighlight.clear();for(const a of t)this._idsToHighlight.add(a);await this.sendUpdates()}async updateFilters(t,e,i){const{service:s,spatialReference:r}=i,{filters:a}=e,n=a.map(((t,e)=>this._updateFilter(t,e,s,r)));(await Promise.all(n)).some((t=>t))&&(t.storage.filters=!0,has(\"esri-2d-update-debug\")&&console.debug(\"Applying Update - AttributeStore:\",\"Filters changed\"))}setData(t,e,i,s){const r=S(t);this._ensureSizeForTexel(r),this._getBlock(e).setData(t,i,s)}getData(t,e,i){return this._getBlock(e).getData(t,i)}getHighlightFlag(t){return this._idsToHighlight.has(t)?g:0}unsetAttributeData(t){const e=S(t);this._getBlock(0).setData(e,0,0)}setAttributeData(t,e){const s=S(t);if(this._ensureSizeForTexel(s),this._getBlock(0).setData(s,0,this.getFilterFlags(e)),this._targetType!==T(t))return;const r=this._attributeComputeInfo,a=this.config.supportsTextureFloat?1:2,n=4;let o=null;r&&(o=r.isSubtype?r.map.get(e.readAttribute(r.subtypeField)):r.map,o&&o.size&&o.forEach(((t,r)=>{const o=r*a%n,h=Math.floor(r*a/n),l=this._getBlock(h+_),u=t(e);if(this.config.supportsTextureFloat)l.setData(s,o,u);else if(u===f)l.setData(s,o,255),l.setData(s,o+1,255);else{const t=i(Math.round(u),-32767,32766)+32768,e=255&t,r=(65280&t)>>8;l.setData(s,o,e),l.setData(s,o+1,r)}})))}sendUpdates(){if(has(\"esri-2d-update-debug\")&&console.debug(\"AttributeStore::sendUpdate\"),this._notifyChange(),this._nextUpdate)return this._nextUpdate.promise;if(this._currUpdate)return this._nextUpdate=l(),this._nextUpdate.promise;const e={blocks:this._blocks.map((t=>r(t)?t.toMessage():null))};return this._currUpdate=this._createResources().then((()=>{const t=()=>{if(this._currUpdate=null,this._nextUpdate){const t=this._nextUpdate;this._nextUpdate=null,this.sendUpdates().then((()=>t.resolve()))}else has(\"esri-2d-update-debug\")&&console.debug(\"AttributeStore::sendUpdate::No additional updates queued\");this._notifyChange()};has(\"esri-2d-update-debug\")&&console.debug(\"AttributeStore::sendUpdate::client.update\");const i=this._client.update(e,this._signal).then(t).catch(t);return this._client.render(this._signal),i})).catch((e=>{if(u(e))return this._createResourcesPromise=null,this._createResources();this._notifyChange(),k.error(new t(\"mapview-attribute-store\",\"Encountered an error during client update\",e))})),this._currUpdate}_ensureSizeForTexel(t){for(;t>=this._size*this._size;)if(this._expand())return}_bindAttribute(t,e){function i(){const{normalizationField:e}=t;return e?i=>{const s=i.readAttribute(e);if(!s)return null;return i.readAttribute(t.field)/s}:e=>e.readAttribute(t.field)}function s(){return t.normalizationField&&k.warn(\"mapview-arcade\",\"Ignoring normalizationField specified with an arcade expression which is not supported.\"),e=>e.getComputedNumericAtIndex(t.fieldIndex)}let r;if(null!=t.fieldIndex)r=s();else{if(!t.field)return;r=i()}const{valueRepresentation:a}=t;if(a){r=B(r,(t=>w(t,a)))}const n=t=>null===t||isNaN(t)||t===1/0||t===-1/0?f:t,o=this._attributeComputeInfo;if(o.isSubtype){const i=o.map.get(e)??new Map;i.set(t.binding,B(r,n)),o.map.set(e,i)}else o.map.set(t.binding,B(r,n))}_createResources(){if(r(this._createResourcesPromise))return this._createResourcesPromise;this._getBlock(y),this._getBlock(m),F(\"Initializing AttributeStore\");const e={shared:I.sharedArrayBuffer&&!(\"local\"===this._client.type),size:this._size,blocks:a(this._blocks,(t=>({textureOnly:t.textureOnly,buffer:t.buffer,pixelType:t.pixelType})))},i=this._client.initialize(e,this._signal).catch((e=>{u(e)?this._createResourcesPromise=null:k.error(new t(\"mapview-attribute-store\",\"Encountered an error during client initialization\",e))}));return this._createResourcesPromise=i,i.then((()=>s(this._createResourcesPromise)?this._createResources():void 0)),i}_getBlock(t){const e=this._blocks[t];if(r(e))return e;F(`Initializing AttributeBlock at index ${t}`);const i=I.sharedArrayBuffer,s=this._client.type,a=new C(i,s,this._size,this._blockDescriptors[t]);return this._blocks[t]=a,this._createResourcesPromise=null,a}_expand(){if(this._size<this.config.maxTextureSize){const t=this._size<<=1;return F(\"Expanding block size to\",t,this._blocks),n(this._blocks,(e=>e.expand(t))),this._createResourcesPromise=null,this._size=t,0}return k.error(new t(\"mapview-limitations\",\"Maximum number of onscreen features exceeded.\")),-1}async _updateFilter(t,e,i,a){const n=this._filters[e],o=r(n)&&n.hash;if(!n&&!t)return!1;if(o===JSON.stringify(t))return!1;if(s(t)){if(!n)return!1;const t=1<<e+1,i=this._getBlock(0);return this._filters[e]=null,i.setComponentAllTexels(0,t),this.sendUpdates(),!0}const h=await this._getFilter(e,i);return await h.update(t,a),!0}async _getFilter(t,e){const i=this._filters[t];if(r(i))return i;const{default:s}=await import(\"./FeatureFilter.js\"),a=new s({geometryType:e.geometryType,hasM:!1,hasZ:!1,timeInfo:e.timeInfo,fieldsIndex:new c(e.fields)});return this._filters[t]=a,a}isVisible(t){return!!(2&this._getBlock(0).getData(t,0))}getFilterFlags(t){let e=0;const i=z(t.getDisplayId());for(let a=0;a<this._filters.length;a++){const r=!!(i&1<<a),n=this._filters[a];e|=(!r||s(n)||n.check(t)?1:0)<<a}let r=0;if(this._idsToHighlight.size){const e=t.getObjectId();r=this.getHighlightFlag(e)}return e<<1|r}}export{M as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{createDisplayId as e}from\"../../../engine/webgl/DisplayId.js\";class r{constructor(){this._freeIds=[],this._idCounter=1}createId(r=!1){return e(this._getFreeId(),r)}releaseId(e){this._freeIds.push(e)}_getFreeId(){return this._freeIds.length?this._freeIds.pop():this._idCounter++}}export{r as DisplayIdGenerator};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{DISPLAY_ID_TEXEL_MASK as t,createDisplayId as e}from\"../../../engine/webgl/DisplayId.js\";import{DisplayIdGenerator as s}from\"./DisplayIdGenerator.js\";import{StaticBitSet as i}from\"./StaticBitSet.js\";function n(t,e,s){if(!(t.length>e))for(;t.length<=e;)t.push(s)}class r{constructor(){this._numerics=[],this._strings=[],this._idGenerator=new s,this._allocatedSize=256,this._bitsets=[],this._instanceIds=[],this._bounds=[]}createBitset(){const e=this._bitsets.length;return this._bitsets.push(i.create(this._allocatedSize,t)),e+1}getBitset(t){return this._bitsets[t-1]}_expand(){this._allocatedSize<<=1;for(const t of this._bitsets)t.resize(this._allocatedSize)}_ensureNumeric(t,e){this._numerics[t]||(this._numerics[t]=[]);n(this._numerics[t],e,0)}_ensureInstanceId(t){n(this._instanceIds,t,0)}_ensureString(t,e){this._strings[t]||(this._strings[t]=[]);n(this._strings[t],e,null)}createDisplayId(t=!1){const s=this._idGenerator.createId();return s>this._allocatedSize&&this._expand(),e(s,t)}releaseDisplayId(e){for(const t of this._bitsets)t.unset(e);return this._idGenerator.releaseId(e&t)}getComputedNumeric(e,s){return this.getComputedNumericAtIndex(e&t,0)}setComputedNumeric(e,s,i){return this.setComputedNumericAtIndex(e&t,i,0)}getComputedString(e,s){return this.getComputedStringAtIndex(e&t,0)}setComputedString(e,s,i){return this.setComputedStringAtIndex(e&t,0,i)}getComputedNumericAtIndex(e,s){const i=e&t;return this._ensureNumeric(s,i),this._numerics[s][i]}setComputedNumericAtIndex(e,s,i){const n=e&t;this._ensureNumeric(s,n),this._numerics[s][n]=i}getInstanceId(e){const s=e&t;return this._ensureInstanceId(s),this._instanceIds[s]}setInstanceId(e,s){const i=e&t;this._ensureInstanceId(i),this._instanceIds[i]=s}getComputedStringAtIndex(e,s){const i=e&t;return this._ensureString(s,i),this._strings[s][i]}setComputedStringAtIndex(e,s,i){const n=e&t;this._ensureString(s,n),this._strings[s][n]=i}getXMin(e){return this._bounds[4*(e&t)]}getYMin(e){return this._bounds[4*(e&t)+1]}getXMax(e){return this._bounds[4*(e&t)+2]}getYMax(e){return this._bounds[4*(e&t)+3]}setBounds(e,s){const i=s.readHydratedGeometry();if(!i||!i.coords.length)return!1;let r=1/0,u=1/0,o=-1/0,h=-1/0;i.forEachVertex(((t,e)=>{r=Math.min(r,t),u=Math.min(u,e),o=Math.max(o,t),h=Math.max(h,e)}));const d=e&t;return n(this._bounds,4*d+4,0),this._bounds[4*d]=r,this._bounds[4*d+1]=u,this._bounds[4*d+2]=o,this._bounds[4*d+3]=h,!0}}export{r as ComputedAttributeStorage};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0a,SAAS,EAAE,EAAC,QAAOA,IAAE,SAAQC,GAAC,GAAE;AAAC,MAAIC,KAAE;AAAE,aAAUC,MAAKF,IAAE;AAAC,aAAQA,KAAE,GAAEA,KAAEE,IAAEF,KAAI,CAAAD,GAAE,KAAGE,KAAED,GAAE,KAAGD,GAAE,KAAGE,KAAED,MAAG,CAAC,GAAED,GAAE,KAAGE,KAAED,MAAG,CAAC,KAAGD,GAAE,KAAGE,KAAED,MAAG,CAAC;AAAE,IAAAC,MAAGC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,OAAO,aAAaL,IAAEC,IAAE;AAAC,UAAK,EAAC,eAAcE,IAAE,cAAaG,GAAC,IAAEL,IAAEM,KAAE,GAAE,CAAC,GAAEP,IAAEM,IAAE,OAAG,OAAGH,EAAC;AAAE,aAAQD,KAAE,GAAEA,KAAEK,GAAE,QAAOL,KAAI,CAAAK,GAAEL,EAAC,EAAE,YAAUF,GAAEE,EAAC,EAAE;AAAU,WAAO,GAAE,sBAAsBK,IAAEN,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,eAAeD,IAAEC,IAAE;AAAC,UAAMC,KAAE,GAAEF,IAAEC,GAAE,aAAa;AAAE,WAAO,GAAE,wBAAwBC,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,wBAAwBD,IAAEC,IAAE;AAAC,UAAK,EAAC,UAASC,GAAC,IAAEF,IAAEG,KAAE,GAAE,sBAAsBD,IAAED,EAAC;AAAE,IAAAE,GAAE,yBAAuBH,GAAE,uBAAsBG,GAAE,aAAWH,GAAE;AAAU,eAAUM,MAAKN,GAAE,OAAO,yBAAsBM,GAAE,QAAMH,GAAE,YAAY,IAAIG,GAAE,IAAI;AAAE,WAAOH;AAAA,EAAC;AAAA,EAAC,OAAO,sBAAsBH,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAEE,GAAE,eAAe,GAAEC,KAAE,IAAI,GAAEH,IAAEH,IAAEC,EAAC;AAAE,WAAOK,GAAE,aAAWJ,IAAEI;AAAA,EAAC;AAAA,EAAC,YAAYN,IAAEC,IAAEC,IAAE;AAAC,UAAMF,IAAEE,EAAC,GAAE,KAAK,yBAAuB,OAAG,KAAK,gBAAc,IAAG,KAAK,cAAY,oBAAI,OAAI,KAAK,gBAAcA,MAAA,gBAAAA,GAAG,cAAa,KAAK,YAAUD;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,UAAU,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,cAAa;AAAC,WAAM,CAAC,CAAC,KAAK,UAAU;AAAA,EAAM;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,gBAAc,IAAE,KAAK,UAAU;AAAA,EAAM;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAO,KAAK;AAAA,EAAsB;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,UAAUD,IAAE;AAAC,UAAMC,KAAE,IAAI,IAAID,EAAC;AAAE,SAAK,YAAU,KAAK,UAAU,OAAQ,CAAAA,OAAG,EAAEA,GAAE,YAAUC,GAAE,IAAID,GAAE,QAAQ,EAAG;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,eAAUC,MAAKD,GAAE,MAAK,UAAU,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,UAAU;AAAA,EAAM;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,2BAA0B;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,mBAAkB;AAAC,QAAID,KAAE;AAAG,eAAUC,MAAK,KAAK,SAAS,WAAW,CAAAD,MAAG,KAAK,SAAS,WAAWC,EAAC;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,SAASA,IAAE;AAAC,SAAK,gBAAcA;AAAA,EAAC;AAAA,EAAC,cAAa;AAJvmE;AAIwmE,YAAO,UAAK,aAAL,mBAAe;AAAA,EAAQ;AAAA,EAAC,eAAc;AAAC,WAAO,KAAK,SAAS;AAAA,EAAS;AAAA,EAAC,aAAaA,IAAE;AAAC,SAAK,SAAS,YAAUA;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,SAAS;AAAA,EAAO;AAAA,EAAC,WAAWA,IAAE;AAAC,SAAK,SAAS,UAAQA;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,UAAMA,KAAE,IAAI,GAAE,KAAK,UAAS,KAAK,WAAU,KAAK,WAAW,CAAC;AAAE,WAAO,KAAK,SAASA,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAK,EAAE,KAAK,gBAAc,KAAK,UAAU,UAAQ,CAAC,KAAK,WAAW,IAAG;AAAC,WAAO,KAAK,gBAAc,KAAK,UAAU;AAAA,EAAM;AAAA,EAAC,oBAAmB;AAAC,WAAO,GAAE,KAAK,UAAS,KAAK,cAAa,KAAK,MAAK,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,0BAAyB;AAAC,WAAO,KAAK,aAAa,IAAE,EAAC,GAAE,KAAK,KAAK,GAAE,GAAE,KAAK,KAAK,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,qBAAoB;AAAC,UAAMA,KAAE,KAAK,aAAa;AAAE,WAAO,GAAEA,IAAE,KAAK,cAAa,KAAK,MAAK,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMC,KAAE,KAAK,aAAa;AAAE,WAAO,EAAEA,EAAC,IAAE,OAAK,EAAC,GAAEA,GAAE,OAAO,CAAC,IAAE,KAAK,MAAI,KAAK,KAAI,GAAEA,GAAE,OAAO,CAAC,IAAE,KAAK,MAAI,KAAK,IAAG;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAOA,GAAE,KAAK,QAAQ,IAAE,GAAE,KAAK,SAAS,UAAS,CAAC,IAAE;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,UAAMD,KAAE,KAAK,aAAa;AAAE,QAAG,wBAAsB,KAAK,gBAAc,CAACA,GAAE,QAAOA;AAAE,UAAMC,KAAED,GAAE,MAAM;AAAE,WAAO,EAAEC,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,UAAMC,KAAE,KAAK,SAAS;AAAS,QAAG,EAAEA,EAAC,EAAE,QAAO;AAAK,UAAMC,KAAED,GAAE,MAAM;AAAE,WAAO,EAAE,KAAK,UAAU,KAAG,GAAEC,IAAEA,IAAE,KAAK,MAAK,KAAK,MAAK,KAAK,UAAU,GAAEA;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,QAAG,CAACF,GAAE,KAAK,QAAQ,EAAE,QAAO;AAAE,UAAMA,KAAE,KAAK,SAAS,SAAS,OAAO,CAAC,GAAEC,KAAE,KAAK,yBAAyB;AAAE,WAAO,EAAEA,EAAC,IAAED,KAAEA,KAAEC,GAAE,MAAM,CAAC,IAAEA,GAAE,UAAU,CAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,QAAG,CAACD,GAAE,KAAK,QAAQ,EAAE,QAAO;AAAE,UAAMA,KAAE,KAAK,SAAS,SAAS,OAAO,CAAC,GAAEC,KAAE,KAAK,yBAAyB;AAAE,WAAO,EAAEA,EAAC,IAAED,KAAEC,GAAE,UAAU,CAAC,IAAED,KAAEC,GAAE,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAOD,GAAE,KAAK,QAAQ,IAAE,KAAK,SAAS,SAAS,OAAO,CAAC,IAAE,KAAK,MAAI,KAAK,MAAI;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAOA,GAAE,KAAK,QAAQ,IAAE,KAAK,SAAS,SAAS,OAAO,CAAC,IAAE,KAAK,MAAI,KAAK,MAAI;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,QAAG,CAACA,GAAE,KAAK,QAAQ,GAAE;AAAC,UAAG,EAAE,KAAK,SAAS,QAAQ,GAAE;AAAC,cAAK,CAACD,IAAEC,EAAC,IAAE,KAAK,SAAS,SAAS;AAAO,eAAO,KAAK,4BAA4BD,IAAEC,EAAC;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI;AAAC,UAAMD,KAAE,KAAK,SAAS,SAAS,MAAM;AAAE,QAAGA,GAAE,QAAQ,QAAOA,GAAE,OAAO,CAAC,IAAEA,GAAE,OAAO,CAAC,IAAE,KAAK,MAAI,KAAK,KAAIA,GAAE,OAAO,CAAC,IAAEA,GAAE,OAAO,CAAC,IAAE,KAAK,MAAI,KAAK,KAAIA;AAAE,QAAIE,KAAE;AAAE,eAAUD,MAAKD,GAAE,QAAQ,CAAAA,GAAE,OAAO,IAAEE,EAAC,IAAEF,GAAE,OAAO,IAAEE,EAAC,IAAE,KAAK,MAAI,KAAK,KAAIF,GAAE,OAAO,IAAEE,KAAE,CAAC,IAAEF,GAAE,OAAO,IAAEE,KAAE,CAAC,IAAE,KAAK,MAAI,KAAK,KAAIA,MAAGD;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,WAAOC,GAAE,KAAK,QAAQ,IAAE,KAAK,iBAAiB,IAAE,KAAK,SAAS;AAAA,EAAQ;AAAA,EAAC,SAASD,IAAE;AAAC,QAAGA,MAAK,KAAK,SAAS,WAAW,QAAM;AAAG,WAAO,KAAK,cAAc,EAAE,IAAK,CAAAA,OAAGA,GAAE,YAAY,CAAE,EAAE,SAASA,GAAE,YAAY,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,OAAO,KAAK,KAAK,SAAS,UAAU;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,SAAS,WAAWF,EAAC;AAAE,QAAG,WAASE,GAAE,QAAO,QAAMA,MAAGD,MAAG,KAAK,YAAY,IAAID,EAAC,IAAE,IAAI,KAAKE,EAAC,IAAEA;AAAE,UAAMC,KAAE,KAAK,eAAe,GAAEG,KAAEN,MAAA,gBAAAA,GAAG,oBAAoB;AAAO,eAAUO,MAAKJ,GAAE,KAAGI,GAAE,kBAAkB,EAAE,KAAK,MAAID,IAAE;AAAC,YAAMN,KAAE,KAAK,SAAS,WAAWO,EAAC;AAAE,aAAO,QAAMP,MAAGC,MAAG,KAAK,YAAY,IAAIM,EAAC,IAAE,IAAI,KAAKP,EAAC,IAAEA;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,UAAM,SAASA,EAAC,GAAEA,GAAE,gBAAc,KAAK,eAAcA,GAAE,aAAW,KAAK,YAAWA,GAAE,cAAY,KAAK;AAAA,EAAW;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK,SAAS;AAAA,EAAU;AAAC;;;ACA7zH,IAAM,IAAE,EAAE,UAAU,sDAAsD;AAA1E,IAA4E,IAAEQ,GAAE,GAAE,CAAC;AAAnF,IAAqF,IAAE,EAAC,mBAAkB,IAAI,0BAA0B,GAAE,SAAQ,IAAI,cAAc,EAAC;AAAE,SAAS,EAAEC,IAAEC,IAAE;AAAC,SAAO,CAAAC,OAAGD,GAAED,GAAEE,EAAC,CAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,OAAK,GAAE,KAAK,YAAU,GAAE,KAAK,aAAW,GAAE,KAAK,WAAS;AAAE,UAAK,EAAC,WAAUC,IAAE,QAAOC,IAAE,aAAYN,GAAC,IAAEI;AAAE,SAAK,cAAYJ,MAAG,OAAG,KAAK,YAAUK,IAAE,KAAK,SAAOH,IAAE,KAAK,SAAOI,IAAE,KAAK,YAAY,GAAE,KAAK,UAAQL,IAAE,KAAK,OAAKE,IAAEH,OAAI,KAAK,OAAK,KAAK,UAAUK,IAAEF,IAAEF,IAAEC,EAAC;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,EAAE,KAAK,MAAM,CAAAD,OAAGA,GAAE,MAAO;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAE,KAAK,IAAI;AAAE,aAAQC,KAAE,GAAEA,KAAE,KAAK,OAAK,KAAK,MAAKA,KAAI,CAAAD,GAAEC,KAAE,KAAK,YAAUH,EAAC,KAAG,CAACC;AAAE,SAAK,aAAW,GAAE,KAAK,WAAS,KAAK,OAAK,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAE,KAAK,IAAI;AAAE,aAAQC,KAAE,GAAEA,KAAE,KAAK,OAAK,KAAK,MAAKA,KAAI,CAAAD,GAAEC,KAAE,KAAK,YAAUH,EAAC,KAAG,MAAIC;AAAE,SAAK,aAAW,GAAE,KAAK,WAAS,KAAK,OAAK,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAE,KAAK,IAAI;AAAE,eAAUC,MAAKF,GAAE,CAAAC,GAAEC,KAAE,KAAK,YAAUJ,EAAC,KAAGC,IAAE,KAAK,aAAW,KAAK,IAAI,KAAK,YAAWG,EAAC,GAAE,KAAK,WAAS,KAAK,IAAI,KAAK,UAASA,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBJ,IAAEC,IAAEC,IAAE;AAAC,MAAE,KAAK,IAAI,EAAEA,KAAE,KAAK,YAAUF,EAAC,KAAGC,IAAE,KAAK,aAAW,KAAK,IAAI,KAAK,YAAWC,EAAC,GAAE,KAAK,WAAS,KAAK,IAAI,KAAK,UAASA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAEC,IAAEC,IAAE;AAAC,MAAE,KAAK,IAAI,EAAEA,KAAE,KAAK,YAAUF,EAAC,KAAG,CAACC,IAAE,KAAK,aAAW,KAAK,IAAI,KAAK,YAAWC,EAAC,GAAE,KAAK,WAAS,KAAK,IAAI,KAAK,UAASA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQF,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAEF,EAAC;AAAE,WAAO,EAAE,KAAK,IAAI,EAAEE,KAAE,KAAK,YAAUD,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQD,IAAEC,IAAEC,IAAE;AAAC,UAAME,KAAE,EAAEJ,EAAC,GAAEK,KAAE,KAAGJ;AAAE,UAAI,KAAK,SAAOI,MAAG,EAAE,KAAK,IAAI,MAAI,KAAK,KAAKD,KAAE,KAAK,YAAUH,EAAC,IAAEC,IAAE,KAAK,aAAW,KAAK,IAAI,KAAK,YAAWE,EAAC,GAAE,KAAK,WAAS,KAAK,IAAI,KAAK,UAASA,EAAC,KAAG,EAAE,MAAM,4BAA2B,uDAAuD;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,cAAY,EAAE,iBAAe,KAAK,WAAS,EAAE,WAAS,YAAU,KAAK,UAAQ,QAAQ,MAAM,KAAK,MAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,cAAY,EAAE,iBAAe,KAAK,WAAS,EAAE,WAAS,YAAU,KAAK,UAAQ,QAAQ,MAAM,KAAK,MAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOJ,IAAE;AAAC,QAAG,KAAK,OAAKA,IAAE,CAAC,KAAK,aAAY;AAAC,YAAMC,KAAE,KAAK,UAAU,KAAK,WAAUD,IAAE,KAAK,SAAQ,KAAK,MAAM,GAAEE,KAAE,EAAE,KAAK,IAAI;AAAE,MAAAD,GAAE,IAAIC,EAAC,GAAE,KAAK,OAAKD;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,UAAMD,KAAE,KAAK,YAAWC,KAAE,KAAK,UAASC,KAAE,KAAK;AAAU,QAAGF,KAAEC,GAAE,QAAO;AAAK,SAAK,YAAY;AAAE,UAAME,KAAE,EAAE,KAAK,WAAS,YAAU,KAAK,SAAQC,KAAE,KAAK,WAAUC,KAAE,KAAK,QAAON,KAAE,EAAE,KAAK,IAAI;AAAE,WAAM,EAAC,OAAMC,IAAE,KAAIC,IAAE,MAAKE,MAAGJ,GAAE,MAAMC,KAAEE,KAAGD,KAAE,KAAGC,EAAC,KAAG,MAAK,WAAUE,IAAE,QAAOC,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAEF,MAAG,YAAUC,KAAE,oBAAkB,aAAYE,KAAE,EAAEL,EAAC,GAAED,KAAE,IAAIM,GAAE,IAAID,GAAEH,KAAEA,KAAE,IAAEI,GAAE,iBAAiB,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAEP,GAAE,QAAOO,MAAG,EAAE,CAAAP,GAAEO,KAAE,CAAC,IAAE;AAAI,WAAOP;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,SAAK,aAAW,YAAW,KAAK,WAAS;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,KAAG,MAAI;AAAA,EAAC,GAAG;AAAC,SAAK,UAAQF,IAAE,KAAK,SAAOC,IAAE,KAAK,gBAAcC,IAAE,KAAK,UAAQ,IAAI,SAAM,KAAK,WAAS,IAAI,MAAM,CAAC,GAAE,KAAK,wBAAsB,MAAK,KAAK,cAAY,GAAE,KAAK,mBAAiB,IAAI,mBAAgB,KAAK,gBAAc,OAAG,KAAK,QAAM,IAAG,KAAK,cAAY,MAAK,KAAK,cAAY,MAAK,KAAK,kBAAgB,oBAAI;AAAI,UAAMC,KAAEF,GAAE,uBAAqB,EAAE,QAAM,EAAE;AAAc,MAAE,2BAA2B,EAAE,oBAAkB,SAAO,SAAS,gBAAgB,GAAE,KAAK,oBAAkB,CAAC,EAAC,WAAU,EAAE,eAAc,QAAO,EAAC,GAAE,EAAC,WAAU,EAAE,eAAc,QAAO,IAAG,aAAY,KAAE,GAAE,EAAC,WAAU,EAAE,eAAc,QAAO,IAAG,aAAY,KAAE,GAAE,EAAC,WAAUE,IAAE,QAAO,GAAE,GAAE,EAAC,WAAUA,IAAE,QAAO,GAAE,GAAE,EAAC,WAAUA,IAAE,QAAO,GAAE,GAAE,EAAC,WAAUA,IAAE,QAAO,GAAE,CAAC,GAAE,KAAK,UAAQ,KAAK,kBAAkB,IAAK,MAAI,IAAK;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,iBAAiB,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,iBAAiB;AAAA,EAAM;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,gBAAgB,OAAK;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAM,CAAC,CAAC,KAAK,eAAa,CAAC,CAAC,KAAK;AAAA,EAAW;AAAA,EAAC,OAAOH,IAAEC,IAAE;AAAC,SAAK,SAAOA;AAAE,UAAMC,KAAED,GAAE,OAAO,WAAW,CAAC,EAAE,SAAQI,KAAE,EAAE,KAAK,SAAQH,EAAC;AAAE,SAAIF,GAAE,QAAQ,WAASA,GAAE,QAAQ,eAAaA,GAAE,QAAQ,OAAK,OAAIK,OAAI,IAAI,sBAAsB,KAAG,QAAQ,MAAM,qCAAoCA,EAAC,GAAEL,GAAE,QAAQ,OAAK,MAAG,KAAK,UAAQE,IAAE,KAAK,wBAAsB,MAAK,CAAC,EAAEA,EAAC,IAAG;AAAC,cAAOA,GAAE,QAAO;AAAA,QAAC,KAAI;AAAU,eAAK,cAAY;AAAE;AAAA,QAAM,KAAI;AAAY,eAAK,cAAYK;AAAA,MAAC;AAAC,UAAG,cAAYL,GAAE,MAAK;AAAC,aAAK,wBAAsB,EAAC,WAAU,MAAG,cAAaA,GAAE,cAAa,KAAI,oBAAI,MAAG;AAAE,mBAAUF,MAAKE,GAAE,SAAQ;AAAC,gBAAMD,KAAEC,GAAE,QAAQF,EAAC;AAAE,cAAG,EAAEC,EAAC,KAAG,EAAEA,GAAE,SAAS,EAAE,YAAUC,MAAKD,GAAE,UAAU,MAAK,eAAeC,IAAE,SAASF,IAAE,EAAE,CAAC;AAAA,QAAC;AAAA,MAAC,OAAK;AAAC,YAAG,KAAK,wBAAsB,EAAC,WAAU,OAAG,KAAI,oBAAI,MAAG,GAAE,EAAEE,GAAE,SAAS,EAAE,YAAUF,MAAKE,GAAE,UAAU,MAAK,eAAeF,EAAC;AAAE,YAAG,EAAEE,GAAE,gBAAgB,EAAE,YAAUF,MAAKE,GAAE,iBAAiB,MAAK,eAAeF,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAEC,IAAE;AAAC,QAAG,EAAEA,GAAE,WAAW,EAAE;AAAO,UAAMC,KAAED,GAAE,YAAY,UAAU;AAAE,WAAKC,GAAE,KAAK,KAAG;AAAC,YAAMF,KAAEE,GAAE,aAAa;AAAE,WAAK,iBAAiBF,IAAEE,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaF,IAAEC,IAAE;AAAC,UAAMC,KAAE,GAAEC,KAAE,KAAK,UAAU,CAAC,GAAEC,KAAEH,GAAE,IAAK,CAAAD,OAAG,EAAEA,EAAC,CAAE;AAAE,IAAAG,GAAE,KAAK,GAAEA,GAAE,wBAAwB,GAAED,EAAC,GAAEC,GAAE,aAAa,GAAED,IAAEE,EAAC,GAAED,GAAE,OAAO,GAAE,KAAK,gBAAgB,MAAM;AAAE,eAAUE,MAAKL,GAAE,MAAK,gBAAgB,IAAIK,EAAC;AAAE,UAAM,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcL,IAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,kBAAiBC,GAAC,IAAEF,IAAE,EAAC,SAAQG,GAAC,IAAEJ,IAAEF,KAAEM,GAAE,IAAK,CAACL,IAAEC,OAAI,KAAK,cAAcD,IAAEC,IAAEE,IAAEC,EAAC,CAAE;AAAE,KAAC,MAAM,QAAQ,IAAIL,EAAC,GAAG,KAAM,CAAAC,OAAGA,EAAE,MAAIA,GAAE,QAAQ,UAAQ,MAAG,IAAI,sBAAsB,KAAG,QAAQ,MAAM,qCAAoC,iBAAiB;AAAA,EAAE;AAAA,EAAC,QAAQA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAEJ,EAAC;AAAE,SAAK,oBAAoBI,EAAC,GAAE,KAAK,UAAUH,EAAC,EAAE,QAAQD,IAAEE,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQH,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,UAAUD,EAAC,EAAE,QAAQD,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAE;AAAC,WAAO,KAAK,gBAAgB,IAAIA,EAAC,IAAE,IAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,SAAK,UAAU,CAAC,EAAE,QAAQC,IAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAEC,IAAE;AAAC,UAAME,KAAE,EAAEH,EAAC;AAAE,QAAG,KAAK,oBAAoBG,EAAC,GAAE,KAAK,UAAU,CAAC,EAAE,QAAQA,IAAE,GAAE,KAAK,eAAeF,EAAC,CAAC,GAAE,KAAK,gBAAcA,GAAED,EAAC,EAAE;AAAO,UAAMI,KAAE,KAAK,uBAAsBC,KAAE,KAAK,OAAO,uBAAqB,IAAE,GAAEN,KAAE;AAAE,QAAIO,KAAE;AAAK,IAAAF,OAAIE,KAAEF,GAAE,YAAUA,GAAE,IAAI,IAAIH,GAAE,cAAcG,GAAE,YAAY,CAAC,IAAEA,GAAE,KAAIE,MAAGA,GAAE,QAAMA,GAAE,QAAS,CAACN,IAAEI,OAAI;AAAC,YAAME,KAAEF,KAAEC,KAAEN,IAAE,IAAE,KAAK,MAAMK,KAAEC,KAAEN,EAAC,GAAES,KAAE,KAAK,UAAU,IAAE,CAAC,GAAEC,KAAET,GAAEC,EAAC;AAAE,UAAG,KAAK,OAAO,qBAAqB,CAAAO,GAAE,QAAQL,IAAEG,IAAEG,EAAC;AAAA,eAAUA,OAAI,EAAE,CAAAD,GAAE,QAAQL,IAAEG,IAAE,GAAG,GAAEE,GAAE,QAAQL,IAAEG,KAAE,GAAE,GAAG;AAAA,WAAM;AAAC,cAAMN,KAAE,EAAE,KAAK,MAAMS,EAAC,GAAE,QAAO,KAAK,IAAE,OAAMR,KAAE,MAAID,IAAEI,MAAG,QAAMJ,OAAI;AAAE,QAAAQ,GAAE,QAAQL,IAAEG,IAAEL,EAAC,GAAEO,GAAE,QAAQL,IAAEG,KAAE,GAAEF,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAE;AAAA,EAAC,cAAa;AAAC,QAAG,IAAI,sBAAsB,KAAG,QAAQ,MAAM,4BAA4B,GAAE,KAAK,cAAc,GAAE,KAAK,YAAY,QAAO,KAAK,YAAY;AAAQ,QAAG,KAAK,YAAY,QAAO,KAAK,cAAY,EAAE,GAAE,KAAK,YAAY;AAAQ,UAAMH,KAAE,EAAC,QAAO,KAAK,QAAQ,IAAK,CAAAD,OAAG,EAAEA,EAAC,IAAEA,GAAE,UAAU,IAAE,IAAK,EAAC;AAAE,WAAO,KAAK,cAAY,KAAK,iBAAiB,EAAE,KAAM,MAAI;AAAC,YAAMA,KAAE,MAAI;AAAC,YAAG,KAAK,cAAY,MAAK,KAAK,aAAY;AAAC,gBAAMA,KAAE,KAAK;AAAY,eAAK,cAAY,MAAK,KAAK,YAAY,EAAE,KAAM,MAAIA,GAAE,QAAQ,CAAE;AAAA,QAAC,MAAM,KAAI,sBAAsB,KAAG,QAAQ,MAAM,0DAA0D;AAAE,aAAK,cAAc;AAAA,MAAC;AAAE,UAAI,sBAAsB,KAAG,QAAQ,MAAM,2CAA2C;AAAE,YAAME,KAAE,KAAK,QAAQ,OAAOD,IAAE,KAAK,OAAO,EAAE,KAAKD,EAAC,EAAE,MAAMA,EAAC;AAAE,aAAO,KAAK,QAAQ,OAAO,KAAK,OAAO,GAAEE;AAAA,IAAC,CAAE,EAAE,MAAO,CAAAD,OAAG;AAAC,UAAG,EAAEA,EAAC,EAAE,QAAO,KAAK,0BAAwB,MAAK,KAAK,iBAAiB;AAAE,WAAK,cAAc,GAAE,EAAE,MAAM,IAAIE,GAAE,2BAA0B,6CAA4CF,EAAC,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK;AAAA,EAAW;AAAA,EAAC,oBAAoBD,IAAE;AAAC,WAAKA,MAAG,KAAK,QAAM,KAAK,QAAO,KAAG,KAAK,QAAQ,EAAE;AAAA,EAAM;AAAA,EAAC,eAAeA,IAAEC,IAAE;AAAC,aAASC,KAAG;AAAC,YAAK,EAAC,oBAAmBD,GAAC,IAAED;AAAE,aAAOC,KAAE,CAAAC,OAAG;AAAC,cAAMC,KAAED,GAAE,cAAcD,EAAC;AAAE,YAAG,CAACE,GAAE,QAAO;AAAK,eAAOD,GAAE,cAAcF,GAAE,KAAK,IAAEG;AAAA,MAAC,IAAE,CAAAF,OAAGA,GAAE,cAAcD,GAAE,KAAK;AAAA,IAAC;AAAC,aAASG,KAAG;AAAC,aAAOH,GAAE,sBAAoB,EAAE,KAAK,kBAAiB,yFAAyF,GAAE,CAAAC,OAAGA,GAAE,0BAA0BD,GAAE,UAAU;AAAA,IAAC;AAAC,QAAII;AAAE,QAAG,QAAMJ,GAAE,WAAW,CAAAI,KAAED,GAAE;AAAA,SAAM;AAAC,UAAG,CAACH,GAAE,MAAM;AAAO,MAAAI,KAAEF,GAAE;AAAA,IAAC;AAAC,UAAK,EAAC,qBAAoBG,GAAC,IAAEL;AAAE,QAAGK,IAAE;AAAC,MAAAD,KAAE,EAAEA,IAAG,CAAAJ,OAAGI,GAAEJ,IAAEK,EAAC,CAAE;AAAA,IAAC;AAAC,UAAMN,KAAE,CAAAC,OAAG,SAAOA,MAAG,MAAMA,EAAC,KAAGA,OAAI,IAAE,KAAGA,OAAI,KAAG,IAAE,IAAEA,IAAEM,KAAE,KAAK;AAAsB,QAAGA,GAAE,WAAU;AAAC,YAAMJ,KAAEI,GAAE,IAAI,IAAIL,EAAC,KAAG,oBAAI;AAAI,MAAAC,GAAE,IAAIF,GAAE,SAAQ,EAAEI,IAAEL,EAAC,CAAC,GAAEO,GAAE,IAAI,IAAIL,IAAEC,EAAC;AAAA,IAAC,MAAM,CAAAI,GAAE,IAAI,IAAIN,GAAE,SAAQ,EAAEI,IAAEL,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,QAAG,EAAE,KAAK,uBAAuB,EAAE,QAAO,KAAK;AAAwB,SAAK,UAAU,CAAC,GAAE,KAAK,UAAU,CAAC,GAAE,EAAE,6BAA6B;AAAE,UAAME,KAAE,EAAC,QAAO,EAAE,qBAAmB,EAAE,YAAU,KAAK,QAAQ,OAAM,MAAK,KAAK,OAAM,QAAO,EAAE,KAAK,SAAS,CAAAD,QAAI,EAAC,aAAYA,GAAE,aAAY,QAAOA,GAAE,QAAO,WAAUA,GAAE,UAAS,EAAG,EAAC,GAAEE,KAAE,KAAK,QAAQ,WAAWD,IAAE,KAAK,OAAO,EAAE,MAAO,CAAAA,OAAG;AAAC,QAAEA,EAAC,IAAE,KAAK,0BAAwB,OAAK,EAAE,MAAM,IAAIE,GAAE,2BAA0B,qDAAoDF,EAAC,CAAC;AAAA,IAAC,CAAE;AAAE,WAAO,KAAK,0BAAwBC,IAAEA,GAAE,KAAM,MAAI,EAAE,KAAK,uBAAuB,IAAE,KAAK,iBAAiB,IAAE,MAAO,GAAEA;AAAA,EAAC;AAAA,EAAC,UAAUF,IAAE;AAAC,UAAMC,KAAE,KAAK,QAAQD,EAAC;AAAE,QAAG,EAAEC,EAAC,EAAE,QAAOA;AAAE,MAAE,wCAAwCD,EAAC,EAAE;AAAE,UAAME,KAAE,EAAE,mBAAkBC,KAAE,KAAK,QAAQ,MAAKE,KAAE,IAAI,EAAEH,IAAEC,IAAE,KAAK,OAAM,KAAK,kBAAkBH,EAAC,CAAC;AAAE,WAAO,KAAK,QAAQA,EAAC,IAAEK,IAAE,KAAK,0BAAwB,MAAKA;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,KAAK,QAAM,KAAK,OAAO,gBAAe;AAAC,YAAML,KAAE,KAAK,UAAQ;AAAE,aAAO,EAAE,2BAA0BA,IAAE,KAAK,OAAO,GAAE,EAAE,KAAK,SAAS,CAAAC,OAAGA,GAAE,OAAOD,EAAC,CAAE,GAAE,KAAK,0BAAwB,MAAK,KAAK,QAAMA,IAAE;AAAA,IAAC;AAAC,WAAO,EAAE,MAAM,IAAIG,GAAE,uBAAsB,+CAA+C,CAAC,GAAE;AAAA,EAAE;AAAA,EAAC,MAAM,cAAcH,IAAEC,IAAEC,IAAEG,IAAE;AAAC,UAAMN,KAAE,KAAK,SAASE,EAAC,GAAEK,KAAE,EAAEP,EAAC,KAAGA,GAAE;AAAK,QAAG,CAACA,MAAG,CAACC,GAAE,QAAM;AAAG,QAAGM,OAAI,KAAK,UAAUN,EAAC,EAAE,QAAM;AAAG,QAAG,EAAEA,EAAC,GAAE;AAAC,UAAG,CAACD,GAAE,QAAM;AAAG,YAAMC,KAAE,KAAGC,KAAE,GAAEC,KAAE,KAAK,UAAU,CAAC;AAAE,aAAO,KAAK,SAASD,EAAC,IAAE,MAAKC,GAAE,sBAAsB,GAAEF,EAAC,GAAE,KAAK,YAAY,GAAE;AAAA,IAAE;AAAC,UAAM,IAAE,MAAM,KAAK,WAAWC,IAAEC,EAAC;AAAE,WAAO,MAAM,EAAE,OAAOF,IAAEK,EAAC,GAAE;AAAA,EAAE;AAAA,EAAC,MAAM,WAAWL,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,SAASF,EAAC;AAAE,QAAG,EAAEE,EAAC,EAAE,QAAOA;AAAE,UAAK,EAAC,SAAQC,GAAC,IAAE,MAAM,OAAO,6BAAoB,GAAEE,KAAE,IAAIF,GAAE,EAAC,cAAaF,GAAE,cAAa,MAAK,OAAG,MAAK,OAAG,UAASA,GAAE,UAAS,aAAY,IAAIG,GAAEH,GAAE,MAAM,EAAC,CAAC;AAAE,WAAO,KAAK,SAASD,EAAC,IAAEK,IAAEA;AAAA,EAAC;AAAA,EAAC,UAAUL,IAAE;AAAC,WAAM,CAAC,EAAE,IAAE,KAAK,UAAU,CAAC,EAAE,QAAQA,IAAE,CAAC;AAAA,EAAE;AAAA,EAAC,eAAeA,IAAE;AAAC,QAAIC,KAAE;AAAE,UAAMC,KAAE,EAAEF,GAAE,aAAa,CAAC;AAAE,aAAQK,KAAE,GAAEA,KAAE,KAAK,SAAS,QAAOA,MAAI;AAAC,YAAMD,KAAE,CAAC,EAAEF,KAAE,KAAGG,KAAGN,KAAE,KAAK,SAASM,EAAC;AAAE,MAAAJ,OAAI,CAACG,MAAG,EAAEL,EAAC,KAAGA,GAAE,MAAMC,EAAC,IAAE,IAAE,MAAIK;AAAA,IAAC;AAAC,QAAID,KAAE;AAAE,QAAG,KAAK,gBAAgB,MAAK;AAAC,YAAMH,KAAED,GAAE,YAAY;AAAE,MAAAI,KAAE,KAAK,iBAAiBH,EAAC;AAAA,IAAC;AAAC,WAAOA,MAAG,IAAEG;AAAA,EAAC;AAAC;;;ACAl3V,IAAMM,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,WAAS,CAAC,GAAE,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,SAASA,KAAE,OAAG;AAAC,WAAOC,GAAE,KAAK,WAAW,GAAED,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUE,IAAE;AAAC,SAAK,SAAS,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,SAAS,SAAO,KAAK,SAAS,IAAI,IAAE,KAAK;AAAA,EAAY;AAAC;;;ACA/E,SAASC,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,EAAEF,GAAE,SAAOC,IAAG,QAAKD,GAAE,UAAQC,KAAG,CAAAD,GAAE,KAAKE,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAU,CAAC,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,eAAa,IAAIA,MAAE,KAAK,iBAAe,KAAI,KAAK,WAAS,CAAC,GAAE,KAAK,eAAa,CAAC,GAAE,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,UAAMF,KAAE,KAAK,SAAS;AAAO,WAAO,KAAK,SAAS,KAAKD,GAAE,OAAO,KAAK,gBAAe,CAAC,CAAC,GAAEC,KAAE;AAAA,EAAC;AAAA,EAAC,UAAUD,IAAE;AAAC,WAAO,KAAK,SAASA,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,mBAAiB;AAAE,eAAUA,MAAK,KAAK,SAAS,CAAAA,GAAE,OAAO,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,IAAE;AAAC,SAAK,UAAUD,EAAC,MAAI,KAAK,UAAUA,EAAC,IAAE,CAAC;AAAG,IAAAD,GAAE,KAAK,UAAUC,EAAC,GAAEC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAE;AAAC,IAAAD,GAAE,KAAK,cAAaC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAE;AAAC,SAAK,SAASD,EAAC,MAAI,KAAK,SAASA,EAAC,IAAE,CAAC;AAAG,IAAAD,GAAE,KAAK,SAASC,EAAC,GAAEC,IAAE,IAAI;AAAA,EAAC;AAAA,EAAC,gBAAgBD,KAAE,OAAG;AAAC,UAAME,KAAE,KAAK,aAAa,SAAS;AAAE,WAAOA,KAAE,KAAK,kBAAgB,KAAK,QAAQ,GAAEA,GAAEA,IAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBC,IAAE;AAAC,eAAUD,MAAK,KAAK,SAAS,CAAAA,GAAE,MAAMC,EAAC;AAAE,WAAO,KAAK,aAAa,UAAUA,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEC,IAAE;AAAC,WAAO,KAAK,0BAA0BD,KAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEC,IAAEE,IAAE;AAAC,WAAO,KAAK,0BAA0BH,KAAE,GAAEG,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAEC,IAAE;AAAC,WAAO,KAAK,yBAAyBD,KAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,IAAEE,IAAE;AAAC,WAAO,KAAK,yBAAyBH,KAAE,GAAE,GAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BH,IAAEC,IAAE;AAAC,UAAME,KAAEH,KAAE;AAAE,WAAO,KAAK,eAAeC,IAAEE,EAAC,GAAE,KAAK,UAAUF,EAAC,EAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BH,IAAEC,IAAEE,IAAE;AAAC,UAAML,KAAEE,KAAE;AAAE,SAAK,eAAeC,IAAEH,EAAC,GAAE,KAAK,UAAUG,EAAC,EAAEH,EAAC,IAAEK;AAAA,EAAC;AAAA,EAAC,cAAcH,IAAE;AAAC,UAAMC,KAAED,KAAE;AAAE,WAAO,KAAK,kBAAkBC,EAAC,GAAE,KAAK,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,UAAME,KAAEH,KAAE;AAAE,SAAK,kBAAkBG,EAAC,GAAE,KAAK,aAAaA,EAAC,IAAEF;AAAA,EAAC;AAAA,EAAC,yBAAyBD,IAAEC,IAAE;AAAC,UAAME,KAAEH,KAAE;AAAE,WAAO,KAAK,cAAcC,IAAEE,EAAC,GAAE,KAAK,SAASF,EAAC,EAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBH,IAAEC,IAAEE,IAAE;AAAC,UAAML,KAAEE,KAAE;AAAE,SAAK,cAAcC,IAAEH,EAAC,GAAE,KAAK,SAASG,EAAC,EAAEH,EAAC,IAAEK;AAAA,EAAC;AAAA,EAAC,QAAQH,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAGA,KAAE,EAAE;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAGA,KAAE,KAAG,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAGA,KAAE,KAAG,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAGA,KAAE,KAAG,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAEC,IAAE;AAAC,UAAME,KAAEF,GAAE,qBAAqB;AAAE,QAAG,CAACE,MAAG,CAACA,GAAE,OAAO,OAAO,QAAM;AAAG,QAAID,KAAE,IAAE,GAAEE,KAAE,IAAE,GAAEC,KAAE,KAAG,GAAE,IAAE,KAAG;AAAE,IAAAF,GAAE,cAAe,CAACJ,IAAEC,OAAI;AAAC,MAAAE,KAAE,KAAK,IAAIA,IAAEH,EAAC,GAAEK,KAAE,KAAK,IAAIA,IAAEJ,EAAC,GAAEK,KAAE,KAAK,IAAIA,IAAEN,EAAC,GAAE,IAAE,KAAK,IAAI,GAAEC,EAAC;AAAA,IAAC,CAAE;AAAE,UAAMM,KAAEN,KAAE;AAAE,WAAOF,GAAE,KAAK,SAAQ,IAAEQ,KAAE,GAAE,CAAC,GAAE,KAAK,QAAQ,IAAEA,EAAC,IAAEJ,IAAE,KAAK,QAAQ,IAAEI,KAAE,CAAC,IAAEF,IAAE,KAAK,QAAQ,IAAEE,KAAE,CAAC,IAAED,IAAE,KAAK,QAAQ,IAAEC,KAAE,CAAC,IAAE,GAAE;AAAA,EAAE;AAAC;", "names": ["t", "e", "r", "s", "c", "b", "i", "n", "n", "t", "e", "i", "s", "r", "a", "o", "c", "l", "u", "r", "s", "e", "n", "t", "e", "s", "r", "i", "u", "o", "d"]}