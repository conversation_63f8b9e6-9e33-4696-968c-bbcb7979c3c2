{"version": 3, "sources": ["../../@arcgis/core/layers/support/ExportWMSImageParameters.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import r from\"../../core/Accessor.js\";import{HandleOwnerMixin as s}from\"../../core/HandleOwner.js\";import{property as t}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as a}from\"../../core/accessorSupport/decorators/subclass.js\";const o={visible:\"visibleSublayers\"};let l=class extends(s(r)){constructor(e){super(e),this.scale=0}set layer(e){this._get(\"layer\")!==e&&(this._set(\"layer\",e),this.handles.remove(\"layer\"),e&&this.handles.add([e.sublayers.on(\"change\",(()=>this.notifyChange(\"visibleSublayers\"))),e.on(\"wms-sublayer-update\",(e=>this.notifyChange(o[e.propertyName])))],\"layer\"))}get layers(){return this.visibleSublayers.filter((({name:e})=>e)).map((({name:e})=>e)).join()}get version(){this.commitProperty(\"layers\");const e=this.layer;return e&&e.commitProperty(\"imageTransparency\"),(this._get(\"version\")||0)+1}get visibleSublayers(){const{layer:e,scale:r}=this,s=e?.sublayers,t=[],a=e=>{const{minScale:s,maxScale:o,sublayers:l,visible:i}=e;i&&(0===r||(0===s||r<=s)&&(0===o||r>=o))&&(l?l.forEach(a):t.push(e))};return s?.forEach(a),t}toJSON(){const{layer:e,layers:r}=this,{imageFormat:s,imageTransparency:t,version:a}=e;return{format:s,request:\"GetMap\",service:\"WMS\",styles:\"\",transparent:t?\"TRUE\":\"FALSE\",version:a,layers:r}}};e([t()],l.prototype,\"layer\",null),e([t({readOnly:!0})],l.prototype,\"layers\",null),e([t({type:Number})],l.prototype,\"scale\",void 0),e([t({readOnly:!0})],l.prototype,\"version\",null),e([t({readOnly:!0})],l.prototype,\"visibleSublayers\",null),l=e([a(\"esri.layers.support.ExportWMSImageParameters\")],l);export{l as ExportWMSImageParameters};\n"], "mappings": ";;;;;;;;;;;;;AAI6X,IAAM,IAAE,EAAC,SAAQ,mBAAkB;AAAE,IAAI,IAAE,cAAcA,GAAE,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,QAAM;AAAA,EAAC;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,KAAK,OAAO,MAAIA,OAAI,KAAK,KAAK,SAAQA,EAAC,GAAE,KAAK,QAAQ,OAAO,OAAO,GAAEA,MAAG,KAAK,QAAQ,IAAI,CAACA,GAAE,UAAU,GAAG,UAAU,MAAI,KAAK,aAAa,kBAAkB,CAAE,GAAEA,GAAE,GAAG,uBAAuB,CAAAA,OAAG,KAAK,aAAa,EAAEA,GAAE,YAAY,CAAC,CAAE,CAAC,GAAE,OAAO;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,iBAAiB,OAAQ,CAAC,EAAC,MAAKA,GAAC,MAAIA,EAAE,EAAE,IAAK,CAAC,EAAC,MAAKA,GAAC,MAAIA,EAAE,EAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,SAAK,eAAe,QAAQ;AAAE,UAAMA,KAAE,KAAK;AAAM,WAAOA,MAAGA,GAAE,eAAe,mBAAmB,IAAG,KAAK,KAAK,SAAS,KAAG,KAAG;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,UAAK,EAAC,OAAMA,IAAE,OAAM,EAAC,IAAE,MAAK,IAAEA,MAAA,gBAAAA,GAAG,WAAU,IAAE,CAAC,GAAED,KAAE,CAAAC,OAAG;AAAC,YAAK,EAAC,UAASC,IAAE,UAASC,IAAE,WAAUC,IAAE,SAAQ,EAAC,IAAEH;AAAE,YAAI,MAAI,MAAI,MAAIC,MAAG,KAAGA,QAAK,MAAIC,MAAG,KAAGA,SAAMC,KAAEA,GAAE,QAAQJ,EAAC,IAAE,EAAE,KAAKC,EAAC;AAAA,IAAE;AAAE,WAAO,uBAAG,QAAQD,KAAG;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAK,EAAC,OAAMC,IAAE,QAAO,EAAC,IAAE,MAAK,EAAC,aAAY,GAAE,mBAAkB,GAAE,SAAQD,GAAC,IAAEC;AAAE,WAAM,EAAC,QAAO,GAAE,SAAQ,UAAS,SAAQ,OAAM,QAAO,IAAG,aAAY,IAAE,SAAO,SAAQ,SAAQD,IAAE,QAAO,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,8CAA8C,CAAC,GAAE,CAAC;", "names": ["a", "e", "s", "o", "l"]}