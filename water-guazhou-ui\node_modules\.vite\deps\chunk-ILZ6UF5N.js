import {
  g,
  u2
} from "./chunk-5HSEQ7GU.js";
import {
  e as e2
} from "./chunk-C2ZE76VJ.js";
import {
  a
} from "./chunk-QB6AUIQ2.js";
import {
  r
} from "./chunk-REGYRSW7.js";
import {
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  v
} from "./chunk-N3S5O3YO.js";
import {
  e
} from "./chunk-GXMOAZWH.js";
import {
  o as o3
} from "./chunk-TUB4N6LD.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";

// node_modules/@arcgis/core/chunks/NativeLine.glsl.js
function u3(u4) {
  const v3 = new o2(), { vertex: f, fragment: h2 } = v3;
  return v3.include(r, u4), v3.include(e2, u4), v3.include(u2, u4), v(f, u4), u4.stippleEnabled && (v3.attributes.add(O.UV0, "vec2"), v3.attributes.add(O.AUXPOS1, "vec3"), f.uniforms.add(new e("viewport", (e3, o4) => o4.camera.fullViewport))), v3.attributes.add(O.POSITION, "vec3"), v3.varyings.add("vpos", "vec3"), f.code.add(o`void main(void) {
vpos = position;
forwardNormalizedVertexColor();
gl_Position = transformPosition(proj, view, vpos);`), u4.stippleEnabled && (f.code.add(o`vec4 vpos2 = transformPosition(proj, view, auxpos1);
vec2 ndcToPixel = viewport.zw * 0.5;
float lineSegmentPixelSize = length((vpos2.xy / vpos2.w - gl_Position.xy / gl_Position.w) * ndcToPixel);`), u4.draped ? f.uniforms.add(new o3("worldToScreenRatio", (e3, o4) => 1 / o4.screenToPCSRatio)) : f.code.add(o`vec3 segmentCenter = (position + auxpos1) * 0.5;
float worldToScreenRatio = computeWorldToScreenRatio(segmentCenter);`), f.code.add(o`float discreteWorldToScreenRatio = discretizeWorldToScreenRatio(worldToScreenRatio);`), u4.draped ? f.code.add(o`float startPseudoScreen = uv0.y * discreteWorldToScreenRatio - mix(0.0, lineSegmentPixelSize, uv0.x);
float segmentLengthPseudoScreen = lineSegmentPixelSize;`) : f.code.add(o`float segmentLengthRender = length(position - auxpos1);
float startPseudoScreen = mix(uv0.y, uv0.y - segmentLengthRender, uv0.x) * discreteWorldToScreenRatio;
float segmentLengthPseudoScreen = segmentLengthRender * discreteWorldToScreenRatio;`), f.uniforms.add(new o3("stipplePatternPixelSize", (e3) => g(e3))), f.code.add(o`vec2 stippleDistanceLimits = computeStippleDistanceLimits(startPseudoScreen, segmentLengthPseudoScreen, lineSegmentPixelSize, stipplePatternPixelSize);
vStippleDistance = mix(stippleDistanceLimits.x, stippleDistanceLimits.y, uv0.x);
vStippleDistance *= gl_Position.w;`)), f.code.add(o`}`), u4.output === h.Highlight && v3.include(a, u4), v3.include(u, u4), h2.uniforms.add(new o3("alphaCoverage", (e3, o4) => Math.min(1, e3.width * o4.camera.pixelRatio))), u4.hasVertexColors || h2.uniforms.add(new e("constantColor", (e3) => e3.color)), h2.code.add(o`
  void main() {
    discardBySlice(vpos);

    vec4 color = ${u4.hasVertexColors ? "vColor" : "constantColor"};

    float stippleAlpha = getStippleAlpha();
    discardByStippleAlpha(stippleAlpha, stippleAlphaColorDiscard);

    vec4 finalColor = blendStipple(vec4(color.rgb, color.a * alphaCoverage), stippleAlpha);

    ${u4.output === h.ObjectAndLayerIdColor ? o`finalColor.a = 1.0;` : ""}

    if (finalColor.a < ${o.float(t)}) {
      discard;
    }

    ${u4.output === h.Color ? o`gl_FragColor = highlightSlice(finalColor, vpos);` : ""}
    ${u4.output === h.Highlight ? o`outputHighlight();` : ""}
  }
  `), v3;
}
var v2 = Object.freeze(Object.defineProperty({ __proto__: null, build: u3 }, Symbol.toStringTag, { value: "Module" }));

export {
  u3 as u,
  v2 as v
};
//# sourceMappingURL=chunk-ILZ6UF5N.js.map
