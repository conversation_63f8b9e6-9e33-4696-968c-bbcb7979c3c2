import {
  c,
  s
} from "./chunk-YELYN22P.js";
import {
  a
} from "./chunk-EIGTETCG.js";
import {
  P,
  e,
  g,
  p,
  r,
  u,
  v
} from "./chunk-MQAXMQFG.js";
import {
  n,
  t
} from "./chunk-36FLFRUE.js";

// node_modules/@arcgis/core/geometry/support/lineSegment.js
function v2(t2) {
  return t2 ? { origin: t(t2.origin), vector: t(t2.vector) } : { origin: n(), vector: n() };
}
function p2(t2, r2) {
  const n2 = x.get();
  return n2.origin = t2, n2.vector = r2, n2;
}
function h(t2, r2 = v2()) {
  return m(t2.origin, t2.vector, r2);
}
function m(t2, r2, o = v2()) {
  return r(o.origin, t2), r(o.vector, r2), o;
}
function b(t2, r2, e2 = v2()) {
  return r(e2.origin, t2), e(e2.vector, r2, t2), e2;
}
function M(r2, n2) {
  const c2 = e(c.get(), n2, r2.origin), s2 = P(r2.vector, c2), u2 = P(r2.vector, r2.vector), a2 = a(s2 / u2, 0, 1), g2 = e(c.get(), g(c.get(), r2.vector, a2), c2);
  return P(g2, g2);
}
function j(t2, r2, n2) {
  return A(t2, r2, 0, 1, n2);
}
function l(t2, r2, n2) {
  return u(n2, t2.origin, g(n2, t2.vector, r2));
}
function A(r2, n2, u2, a2, g2) {
  const { vector: v3, origin: p3 } = r2, h2 = e(c.get(), n2, p3), m2 = P(v3, h2) / v(v3);
  return g(g2, v3, a(m2, u2, a2)), u(g2, g2, r2.origin);
}
function B(t2, r2) {
  if (q(t2, p2(r2.origin, r2.direction), false, w)) {
    const { tA: r3, pB: n2, distance2: o } = w;
    if (r3 >= 0 && r3 <= 1) return o;
    if (r3 < 0) return p(t2.origin, n2);
    if (r3 > 1) return p(u(c.get(), t2.origin, t2.vector), n2);
  }
  return null;
}
function k(t2, r2, o) {
  return !!q(t2, r2, true, w) && (r(o, w.pA), true);
}
function q(r2, n2, o, e2) {
  const i = 1e-6, s2 = r2.origin, a2 = u(c.get(), s2, r2.vector), g2 = n2.origin, v3 = u(c.get(), g2, n2.vector), p3 = c.get(), h2 = c.get();
  if (p3[0] = s2[0] - g2[0], p3[1] = s2[1] - g2[1], p3[2] = s2[2] - g2[2], h2[0] = v3[0] - g2[0], h2[1] = v3[1] - g2[1], h2[2] = v3[2] - g2[2], Math.abs(h2[0]) < i && Math.abs(h2[1]) < i && Math.abs(h2[2]) < i) return false;
  const m2 = c.get();
  if (m2[0] = a2[0] - s2[0], m2[1] = a2[1] - s2[1], m2[2] = a2[2] - s2[2], Math.abs(m2[0]) < i && Math.abs(m2[1]) < i && Math.abs(m2[2]) < i) return false;
  const b2 = p3[0] * h2[0] + p3[1] * h2[1] + p3[2] * h2[2], M2 = h2[0] * m2[0] + h2[1] * m2[1] + h2[2] * m2[2], d = p3[0] * m2[0] + p3[1] * m2[1] + p3[2] * m2[2], j2 = h2[0] * h2[0] + h2[1] * h2[1] + h2[2] * h2[2], l2 = (m2[0] * m2[0] + m2[1] * m2[1] + m2[2] * m2[2]) * j2 - M2 * M2;
  if (Math.abs(l2) < i) return false;
  let A2 = (b2 * M2 - d * j2) / l2, B2 = (b2 + M2 * A2) / j2;
  o && (A2 = a(A2, 0, 1), B2 = a(B2, 0, 1));
  const k2 = c.get(), S = c.get();
  return k2[0] = s2[0] + A2 * m2[0], k2[1] = s2[1] + A2 * m2[1], k2[2] = s2[2] + A2 * m2[2], S[0] = g2[0] + B2 * h2[0], S[1] = g2[1] + B2 * h2[1], S[2] = g2[2] + B2 * h2[2], e2.tA = A2, e2.tB = B2, e2.pA = k2, e2.pB = S, e2.distance2 = p(k2, S), true;
}
var w = { tA: 0, tB: 0, pA: n(), pB: n(), distance2: 0 };
var x = new s(() => v2());

export {
  v2 as v,
  h,
  m,
  b,
  M,
  j,
  l,
  A,
  B,
  k
};
//# sourceMappingURL=chunk-FQMXSCOG.js.map
