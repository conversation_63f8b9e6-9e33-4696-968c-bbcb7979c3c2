import {
  a2 as a,
  a3 as a2,
  d
} from "./chunk-V434P7FU.js";
import {
  I,
  c,
  e,
  l,
  w
} from "./chunk-7QFVH5MN.js";
import {
  n,
  r as r2
} from "./chunk-UHA44FM7.js";
import "./chunk-CPQSD22U.js";
import {
  S
} from "./chunk-ZIKXCGU7.js";
import "./chunk-XSQFM27N.js";
import "./chunk-A7PY25IH.js";
import {
  xn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import {
  E
} from "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import {
  t2
} from "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/3d/layers/i3s/PointCloudWorkerUtil.js
function f2(e2, o, n2, s) {
  const { rendererJSON: i, isRGBRenderer: u } = e2;
  let f3 = null, c4 = null;
  if (o && u) f3 = o;
  else if (o && "pointCloudUniqueValueRenderer" === (i == null ? void 0 : i.type)) {
    c4 = a2.fromJSON(i);
    const e3 = c4.colorUniqueValueInfos;
    f3 = new Uint8Array(3 * s);
    const r3 = b(c4.fieldTransformType);
    for (let t3 = 0; t3 < s; t3++) {
      const l2 = (r3 ? r3(o[t3]) : o[t3]) + "";
      for (let o2 = 0; o2 < e3.length; o2++) if (e3[o2].values.includes(l2)) {
        f3[3 * t3] = e3[o2].color.r, f3[3 * t3 + 1] = e3[o2].color.g, f3[3 * t3 + 2] = e3[o2].color.b;
        break;
      }
    }
  } else if (o && "pointCloudStretchRenderer" === (i == null ? void 0 : i.type)) {
    c4 = a.fromJSON(i);
    const e3 = c4.stops;
    f3 = new Uint8Array(3 * s);
    const r3 = b(c4.fieldTransformType);
    for (let t3 = 0; t3 < s; t3++) {
      const l2 = r3 ? r3(o[t3]) : o[t3], n3 = e3.length - 1;
      if (l2 < e3[0].value) f3[3 * t3] = e3[0].color.r, f3[3 * t3 + 1] = e3[0].color.g, f3[3 * t3 + 2] = e3[0].color.b;
      else if (l2 >= e3[n3].value) f3[3 * t3] = e3[n3].color.r, f3[3 * t3 + 1] = e3[n3].color.g, f3[3 * t3 + 2] = e3[n3].color.b;
      else for (let o2 = 1; o2 < e3.length; o2++) if (l2 < e3[o2].value) {
        const r4 = (l2 - e3[o2 - 1].value) / (e3[o2].value - e3[o2 - 1].value);
        f3[3 * t3] = e3[o2].color.r * r4 + e3[o2 - 1].color.r * (1 - r4), f3[3 * t3 + 1] = e3[o2].color.g * r4 + e3[o2 - 1].color.g * (1 - r4), f3[3 * t3 + 2] = e3[o2].color.b * r4 + e3[o2 - 1].color.b * (1 - r4);
        break;
      }
    }
  } else if (o && "pointCloudClassBreaksRenderer" === (i == null ? void 0 : i.type)) {
    c4 = d.fromJSON(i);
    const e3 = c4.colorClassBreakInfos;
    f3 = new Uint8Array(3 * s);
    const t3 = b(c4.fieldTransformType);
    for (let r3 = 0; r3 < s; r3++) {
      const l2 = t3 ? t3(o[r3]) : o[r3];
      for (let o2 = 0; o2 < e3.length; o2++) if (l2 >= e3[o2].minValue && l2 <= e3[o2].maxValue) {
        f3[3 * r3] = e3[o2].color.r, f3[3 * r3 + 1] = e3[o2].color.g, f3[3 * r3 + 2] = e3[o2].color.b;
        break;
      }
    }
  } else {
    f3 = new Uint8Array(3 * s);
    for (let e3 = 0; e3 < f3.length; e3++) f3[e3] = 255;
  }
  if (n2 && c4 && c4.colorModulation) {
    const e3 = c4.colorModulation.minValue, o2 = c4.colorModulation.maxValue, r3 = 0.3;
    for (let t3 = 0; t3 < s; t3++) {
      const l2 = n2[t3], s2 = l2 >= o2 ? 1 : l2 <= e3 ? r3 : r3 + (1 - r3) * (l2 - e3) / (o2 - e3);
      f3[3 * t3] = s2 * f3[3 * t3], f3[3 * t3 + 1] = s2 * f3[3 * t3 + 1], f3[3 * t3 + 2] = s2 * f3[3 * t3 + 2];
    }
  }
  return f3;
}
function c2(o, r3) {
  if (null == o.encoding || "" === o.encoding) {
    const t3 = w(r3, o);
    if (t(t3.vertexAttributes.position)) return;
    const l2 = l(r3, t3.vertexAttributes.position), i = t3.header.fields, u = [i.offsetX, i.offsetY, i.offsetZ], f3 = [i.scaleX, i.scaleY, i.scaleZ], c4 = l2.length / 3, a4 = new Float64Array(3 * c4);
    for (let e2 = 0; e2 < c4; e2++) a4[3 * e2] = l2[3 * e2] * f3[0] + u[0], a4[3 * e2 + 1] = l2[3 * e2 + 1] * f3[1] + u[1], a4[3 * e2 + 2] = l2[3 * e2 + 2] * f3[2] + u[2];
    return a4;
  }
  if ("lepcc-xyz" === o.encoding) return c(r3).result;
}
function a3(e2, r3, t3) {
  return r(e2) && e2.attributeInfo.useElevation ? r3 ? d2(r3, t3) : null : r(e2) && e2.attributeInfo.storageInfo ? I(e2.attributeInfo.storageInfo, e2.buffer, t3) : null;
}
function d2(e2, o) {
  const r3 = new Float64Array(o);
  for (let t3 = 0; t3 < o; t3++) r3[t3] = e2[3 * t3 + 2];
  return r3;
}
function m(e2, o, r3, t3, l2) {
  const n2 = e2.length / 3;
  let s = 0;
  for (let i = 0; i < n2; i++) {
    let n3 = true;
    for (let e3 = 0; e3 < t3.length && n3; e3++) {
      const { filterJSON: o2 } = t3[e3], r4 = l2[e3].values[i];
      switch (o2.type) {
        case "pointCloudValueFilter": {
          const e4 = "exclude" === o2.mode;
          o2.values.includes(r4) === e4 && (n3 = false);
          break;
        }
        case "pointCloudBitfieldFilter": {
          const e4 = p(o2.requiredSetBits), t4 = p(o2.requiredClearBits);
          (r4 & e4) === e4 && 0 == (r4 & t4) || (n3 = false);
          break;
        }
        case "pointCloudReturnFilter": {
          const e4 = 15 & r4, t4 = r4 >>> 4 & 15, l3 = t4 > 1, s2 = 1 === e4, i2 = e4 === t4;
          let u = false;
          for (const r5 of o2.includedReturns) if ("last" === r5 && i2 || "firstOfMany" === r5 && s2 && l3 || "lastOfMany" === r5 && i2 && l3 || "single" === r5 && !l3) {
            u = true;
            break;
          }
          u || (n3 = false);
          break;
        }
      }
    }
    n3 && (r3[s] = i, e2[3 * s] = e2[3 * i], e2[3 * s + 1] = e2[3 * i + 1], e2[3 * s + 2] = e2[3 * i + 2], o[3 * s] = o[3 * i], o[3 * s + 1] = o[3 * i + 1], o[3 * s + 2] = o[3 * i + 2], s++);
  }
  return s;
}
function b(e2) {
  return null == e2 || "none" === e2 ? null : "low-four-bit" === e2 ? (e3) => 15 & e3 : "high-four-bit" === e2 ? (e3) => (240 & e3) >> 4 : "absolute-value" === e2 ? (e3) => Math.abs(e3) : "modulo-ten" === e2 ? (e3) => e3 % 10 : null;
}
function p(e2) {
  let o = 0;
  for (const r3 of e2 || []) o |= 1 << r3;
  return o;
}

// node_modules/@arcgis/core/views/3d/layers/PointCloudWorker.js
var c3 = class {
  transform(e2) {
    const a4 = this._transform(e2), o = [a4.points.buffer, a4.rgb.buffer];
    r(a4.pointIdFilterMap) && o.push(a4.pointIdFilterMap.buffer);
    for (const t3 of a4.attributes) "buffer" in t3.values && t2(t3.values.buffer) && t3.values.buffer !== a4.rgb.buffer && o.push(t3.values.buffer);
    return Promise.resolve({ result: a4, transferList: o });
  }
  _transform(r3) {
    const e2 = c2(r3.schema, r3.geometryBuffer);
    let a4 = e2.length / 3, o = null;
    const f3 = [], i = a3(r3.primaryAttributeData, e2, a4);
    r(r3.primaryAttributeData) && i && f3.push({ attributeInfo: r3.primaryAttributeData.attributeInfo, values: i });
    const n2 = a3(r3.modulationAttributeData, e2, a4);
    r(r3.modulationAttributeData) && n2 && f3.push({ attributeInfo: r3.modulationAttributeData.attributeInfo, values: n2 });
    let c4 = f2(r3.rendererInfo, i, n2, a4);
    if (r3.filterInfo && r3.filterInfo.length > 0 && r(r3.filterAttributesData)) {
      const i2 = r3.filterAttributesData.filter(r).map((t3) => {
        const r4 = a3(t3, e2, a4), o2 = { attributeInfo: t3.attributeInfo, values: r4 };
        return f3.push(o2), o2;
      });
      o = new Uint32Array(a4), a4 = m(e2, c4, o, r3.filterInfo, i2);
    }
    for (const t3 of r3.userAttributesData) {
      const r4 = a3(t3, e2, a4);
      f3.push({ attributeInfo: t3.attributeInfo, values: r4 });
    }
    3 * a4 < c4.length && (c4 = new Uint8Array(c4.buffer.slice(0, 3 * a4))), this._applyElevationOffsetInPlace(e2, a4, r3.elevationOffset);
    const p3 = this._transformCoordinates(e2, a4, r3.obb, f.fromJSON(r3.inSR), f.fromJSON(r3.outSR));
    return { obb: r3.obb, points: p3, rgb: c4, attributes: f3, pointIdFilterMap: o };
  }
  _transformCoordinates(t3, r3, a4, s, u) {
    if (!xn(t3, s, 0, t3, u, 0, r3)) throw new Error("Can't reproject");
    const l2 = r2(a4.center[0], a4.center[1], a4.center[2]), b2 = n(), m2 = n();
    S(p2, a4.quaternion);
    const c4 = new Float32Array(3 * r3);
    for (let e2 = 0; e2 < r3; e2++) b2[0] = t3[3 * e2] - l2[0], b2[1] = t3[3 * e2 + 1] - l2[1], b2[2] = t3[3 * e2 + 2] - l2[2], E(m2, b2, p2), a4.halfSize[0] = Math.max(a4.halfSize[0], Math.abs(m2[0])), a4.halfSize[1] = Math.max(a4.halfSize[1], Math.abs(m2[1])), a4.halfSize[2] = Math.max(a4.halfSize[2], Math.abs(m2[2])), c4[3 * e2] = b2[0], c4[3 * e2 + 1] = b2[1], c4[3 * e2 + 2] = b2[2];
    return c4;
  }
  _applyElevationOffsetInPlace(t3, r3, e2) {
    if (0 !== e2) for (let a4 = 0; a4 < r3; a4++) t3[3 * a4 + 2] += e2;
  }
};
var p2 = e();
function h() {
  return new c3();
}
export {
  h as default
};
//# sourceMappingURL=PointCloudWorker-DC5JN46X.js.map
