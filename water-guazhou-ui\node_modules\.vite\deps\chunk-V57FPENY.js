import {
  r as r7
} from "./chunk-XDK46HA6.js";
import {
  a2 as a5,
  d2 as d,
  m,
  o3 as o5,
  r as r5,
  r2 as r6,
  s as s2,
  v as v2
} from "./chunk-XVTFHFM3.js";
import {
  a as a3
} from "./chunk-QB6AUIQ2.js";
import {
  r as r4
} from "./chunk-REGYRSW7.js";
import {
  n as n4
} from "./chunk-Y424ZXTG.js";
import {
  e as e6
} from "./chunk-UB5FTTH5.js";
import {
  d as d2
} from "./chunk-6GW7M2AQ.js";
import {
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  c,
  v
} from "./chunk-N3S5O3YO.js";
import {
  e as e5
} from "./chunk-JETZLJ6M.js";
import {
  e as e4
} from "./chunk-32BGXH4N.js";
import {
  o as o3
} from "./chunk-BPRRRPC3.js";
import {
  e as e2
} from "./chunk-GXMOAZWH.js";
import {
  a as a4
} from "./chunk-WL2F66AK.js";
import {
  o as o4
} from "./chunk-TUB4N6LD.js";
import {
  e as e3,
  f
} from "./chunk-YV4RKNU4.js";
import {
  a as a2,
  i,
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";
import {
  e
} from "./chunk-QYOAH6AO.js";
import {
  r as r3
} from "./chunk-SROTSYJS.js";
import {
  n as n3
} from "./chunk-FOE4ICAJ.js";
import {
  n as n2
} from "./chunk-NOZFLZZL.js";
import {
  s
} from "./chunk-LJHVXLBF.js";
import {
  a
} from "./chunk-EIGTETCG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import {
  r as r2
} from "./chunk-RQXGVG3K.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/FoamRendering.glsl.js
function o6(o8) {
  o8.fragment.code.add(o`float normals2FoamIntensity(vec3 n, float waveStrength){
float normalizationFactor =  max(0.015, waveStrength);
return max((n.x + n.y)*0.3303545/normalizationFactor + 0.3303545, 0.0);
}`);
}
function n5(o8) {
  o8.fragment.code.add(o`vec3 foamIntensity2FoamColor(float foamIntensityExternal, float foamPixelIntensity, vec3 skyZenitColor, float dayMod){
return foamIntensityExternal * (0.075 * skyZenitColor * pow(foamPixelIntensity, 4.) +  50.* pow(foamPixelIntensity, 23.0)) * dayMod;
}`);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/WaterDistortion.glsl.js
function i2(t2) {
  t2.fragment.uniforms.add(new f("texWaveNormal", (e8) => e8.waveNormal)), t2.fragment.uniforms.add(new f("texWavePerturbation", (e8) => e8.wavePertubation)), t2.fragment.uniforms.add([new e2("waveParams", (e8) => r2(f2, e8.waveStrength, e8.waveTextureRepeat, e8.flowStrength, e8.flowOffset)), new e3("waveDirection", (t3) => r3(c2, t3.waveDirection[0] * t3.waveVelocity, t3.waveDirection[1] * t3.waveVelocity))]), t2.include(o6), t2.fragment.code.add(o`const vec2  FLOW_JUMP = vec2(6.0/25.0, 5.0/24.0);
vec2 textureDenormalized2D(sampler2D _tex, vec2 _uv) {
return 2.0 * texture2D(_tex, _uv).rg - 1.0;
}
float sampleNoiseTexture(vec2 _uv) {
return texture2D(texWavePerturbation, _uv).b;
}
vec3 textureDenormalized3D(sampler2D _tex, vec2 _uv) {
return 2.0 * texture2D(_tex, _uv).rgb - 1.0;
}
float computeProgress(vec2 uv, float time) {
return fract(time);
}
float computeWeight(vec2 uv, float time) {
float progress = computeProgress(uv, time);
return 1.0 - abs(1.0 - 2.0 * progress);
}
vec3 computeUVPerturbedWeigth(sampler2D texFlow, vec2 uv, float time, float phaseOffset) {
float flowStrength = waveParams[2];
float flowOffset = waveParams[3];
vec2 flowVector = textureDenormalized2D(texFlow, uv) * flowStrength;
float progress = computeProgress(uv, time + phaseOffset);
float weight = computeWeight(uv, time + phaseOffset);
vec2 result = uv;
result -= flowVector * (progress + flowOffset);
result += phaseOffset;
result += (time - progress) * FLOW_JUMP;
return vec3(result, weight);
}
const float TIME_NOISE_TEXTURE_REPEAT = 0.3737;
const float TIME_NOISE_STRENGTH = 7.77;
vec3 getWaveLayer(sampler2D _texNormal, sampler2D _dudv, vec2 _uv, vec2 _waveDir, float time) {
float waveStrength = waveParams[0];
vec2 waveMovement = time * -_waveDir;
float timeNoise = sampleNoiseTexture(_uv * TIME_NOISE_TEXTURE_REPEAT) * TIME_NOISE_STRENGTH;
vec3 uv_A = computeUVPerturbedWeigth(_dudv, _uv + waveMovement, time + timeNoise, 0.0);
vec3 uv_B = computeUVPerturbedWeigth(_dudv, _uv + waveMovement, time + timeNoise, 0.5);
vec3 normal_A = textureDenormalized3D(_texNormal, uv_A.xy) * uv_A.z;
vec3 normal_B = textureDenormalized3D(_texNormal, uv_B.xy) * uv_B.z;
vec3 mixNormal = normalize(normal_A + normal_B);
mixNormal.xy *= waveStrength;
mixNormal.z = sqrt(1.0 - dot(mixNormal.xy, mixNormal.xy));
return mixNormal;
}
vec4 getSurfaceNormalAndFoam(vec2 _uv, float _time) {
float waveTextureRepeat = waveParams[1];
vec3 normal = getWaveLayer(texWaveNormal, texWavePerturbation, _uv * waveTextureRepeat, waveDirection, _time);
float foam  = normals2FoamIntensity(normal, waveParams[0]);
return vec4(normal, foam);
}`);
}
var f2 = n2();
var c2 = n3();

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/Gamma.glsl.js
function e7(e8) {
  e8.fragment.code.add(o`const float GAMMA = 2.2;
const float INV_GAMMA = 0.4545454545;
vec4 delinearizeGamma(vec4 color) {
return vec4(pow(color.rgb, vec3(INV_GAMMA)), color.w);
}
vec3 linearizeGamma(vec3 color) {
return pow(color, vec3(GAMMA));
}`);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/ScreenSpaceReflections.glsl.js
function n7(e8, n9) {
  const c5 = e8.fragment;
  c5.include(a4), c5.uniforms.add(new e3("nearFar", (e9, o8) => o8.camera.nearFar)), c5.uniforms.add(new f("depthMap", (e9, o8) => o8.linearDepthTexture)), c5.uniforms.add(new e5("proj", (e9, o8) => o8.ssr.camera.projectionMatrix)), c5.uniforms.add(new o4("invResolutionHeight", (e9, o8) => 1 / o8.ssr.camera.height)), c5.uniforms.add(new e5("reprojectionMatrix", (e9, o8) => o8.ssr.reprojectionMatrix)), c5.code.add(o`
  vec2 reprojectionCoordinate(vec3 projectionCoordinate)
  {
    vec4 zw = proj * vec4(0.0, 0.0, -projectionCoordinate.z, 1.0);
    vec4 reprojectedCoord = reprojectionMatrix * vec4(zw.w * (projectionCoordinate.xy * 2.0 - 1.0), zw.z, zw.w);
    reprojectedCoord.xy /= reprojectedCoord.w;
    return reprojectedCoord.xy * 0.5 + 0.5;
  }

  const int maxSteps = ${n9.highStepCount ? "150" : "75"};

  vec4 applyProjectionMat(mat4 projectionMat, vec3 x)
  {
    vec4 projectedCoord =  projectionMat * vec4(x, 1.0);
    projectedCoord.xy /= projectedCoord.w;
    projectedCoord.xy = projectedCoord.xy*0.5 + 0.5;
    return projectedCoord;
  }

  vec3 screenSpaceIntersection(vec3 dir, vec3 startPosition, vec3 viewDir, vec3 normal)
  {
    vec3 viewPos = startPosition;
    vec3 viewPosEnd = startPosition;

    // Project the start position to the screen
    vec4 projectedCoordStart = applyProjectionMat(proj, viewPos);
    vec3  Q0 = viewPos / projectedCoordStart.w; // homogeneous camera space
    float k0 = 1.0/ projectedCoordStart.w;

    // advance the position in the direction of the reflection
    viewPos += dir;

    vec4 projectedCoordVanishingPoint = applyProjectionMat(proj, dir);

    // Project the advanced position to the screen
    vec4 projectedCoordEnd = applyProjectionMat(proj, viewPos);
    vec3  Q1 = viewPos / projectedCoordEnd.w; // homogeneous camera space
    float k1 = 1.0/ projectedCoordEnd.w;

    // calculate the reflection direction in the screen space
    vec2 projectedCoordDir = (projectedCoordEnd.xy - projectedCoordStart.xy);
    vec2 projectedCoordDistVanishingPoint = (projectedCoordVanishingPoint.xy - projectedCoordStart.xy);

    float yMod = min(abs(projectedCoordDistVanishingPoint.y), 1.0);

    float projectedCoordDirLength = length(projectedCoordDir);
    float maxSt = float(maxSteps);

    // normalize the projection direction depending on maximum steps
    // this determines how blocky the reflection looks
    vec2 dP = yMod * (projectedCoordDir)/(maxSt * projectedCoordDirLength);

    // Normalize the homogeneous camera space coordinates
    vec3  dQ = yMod * (Q1 - Q0)/(maxSt * projectedCoordDirLength);
    float dk = yMod * (k1 - k0)/(maxSt * projectedCoordDirLength);

    // initialize the variables for ray marching
    vec2 P = projectedCoordStart.xy;
    vec3 Q = Q0;
    float k = k0;
    float rayStartZ = -startPosition.z; // estimated ray start depth value
    float rayEndZ = -startPosition.z;   // estimated ray end depth value
    float prevEstimateZ = -startPosition.z;
    float rayDiffZ = 0.0;
    float dDepth;
    float depth;
    float rayDiffZOld = 0.0;

    // early outs
    if (dot(normal, dir) < 0.0 || dot(-viewDir, normal) < 0.0)
      return vec3(P, 0.0);

    for(int i = 0; i < maxSteps-1; i++)
    {
      depth = -linearDepthFromTexture(depthMap, P, nearFar); // get linear depth from the depth buffer

      // estimate depth of the marching ray
      rayStartZ = prevEstimateZ;
      dDepth = -rayStartZ - depth;
      rayEndZ = (dQ.z * 0.5 + Q.z)/ ((dk * 0.5 + k));
      rayDiffZ = rayEndZ- rayStartZ;
      prevEstimateZ = rayEndZ;

      if(-rayEndZ > nearFar[1] || -rayEndZ < nearFar[0] || P.y < 0.0  || P.y > 1.0 )
      {
        return vec3(P, 0.);
      }

      // If we detect a hit - return the intersection point, two conditions:
      //  - dDepth > 0.0 - sampled point depth is in front of estimated depth
      //  - if difference between dDepth and rayDiffZOld is not too large
      //  - if difference between dDepth and 0.025/abs(k) is not too large
      //  - if the sampled depth is not behind far plane or in front of near plane

      if((dDepth) < 0.025/abs(k) + abs(rayDiffZ) && dDepth > 0.0 && depth > nearFar[0] && depth < nearFar[1] && abs(P.y - projectedCoordStart.y) > invResolutionHeight)
      {
        return vec3(P, depth);
      }

      // continue with ray marching
      P += dP;
      Q.z += dQ.z;
      k += dk;
      rayDiffZOld = rayDiffZ;
    }
    return vec3(P, 0.0);
  }
  `);
}
var c3 = class {
  constructor() {
    this.enabled = false, this.fadeFactor = 1, this.reprojectionMatrix = e();
  }
};

// node_modules/@arcgis/core/views/3d/environment/CloudsData.js
var E;
var I;
!function(N) {
  N[N.RENDERING = 0] = "RENDERING", N[N.FINISHED_RENDERING = 1] = "FINISHED_RENDERING", N[N.FADING_TEXTURE_CHANNELS = 2] = "FADING_TEXTURE_CHANNELS", N[N.SWITCH_CHANNELS = 3] = "SWITCH_CHANNELS", N[N.FINISHED = 4] = "FINISHED";
}(E || (E = {})), function(N) {
  N[N.RG = 0] = "RG", N[N.BA = 1] = "BA";
}(I || (I = {}));

// node_modules/@arcgis/core/views/3d/environment/CloudsCompositionParameters.js
var i3 = class {
  constructor() {
    this.readChannels = I.RG, this.renderingStage = E.FINISHED, this.startTime = 0, this.startTimeHeightFade = 0, this.cameraPositionLastFrame = n(), this.isCameraPositionFinal = true, this.parallax = new o7(), this.parallaxNew = new o7(), this.crossFade = { enabled: false, factor: 1, distanceThresholdFactor: 0.3 }, this.fadeInOut = { stage: I2.FINISHED, factor: 1, distanceThresholdFactor: 0.6 }, this.fadeIn = { stage: r8.FINISHED, factor: 1, distanceThresholdFactor: 2 }, this.fadeInOutHeight = { stage: n8.FINISHED, factor: -1 };
  }
  get isFading() {
    return this.fadeInOut.stage === I2.FADE_OUT || this.fadeInOut.stage === I2.FADE_IN || this.fadeIn.stage === r8.FADE_IN || this.fadeInOutHeight.stage !== n8.FINISHED || this.renderingStage === E.FADING_TEXTURE_CHANNELS;
  }
};
var r8;
var I2;
var n8;
!function(t2) {
  t2[t2.FINISHED = 0] = "FINISHED", t2[t2.CHANGE_ANCHOR = 1] = "CHANGE_ANCHOR", t2[t2.FADE_IN = 2] = "FADE_IN";
}(r8 || (r8 = {})), function(t2) {
  t2[t2.FINISHED = 0] = "FINISHED", t2[t2.FADE_OUT = 1] = "FADE_OUT", t2[t2.SWITCH = 2] = "SWITCH", t2[t2.FADE_IN = 3] = "FADE_IN";
}(I2 || (I2 = {})), function(t2) {
  t2[t2.FINISHED = 0] = "FINISHED", t2[t2.HEIGHT_FADE = 1] = "HEIGHT_FADE";
}(n8 || (n8 = {}));
var o7 = class {
  constructor() {
    this.anchorPointClouds = n(), this.cloudsHeight = 1e5, this.radiusCurvatureCorrectionFactor = 0, this.transform = e();
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderModules/TextureCubePassUniform.js
var s3 = class extends i {
  constructor(e8, s4) {
    super(e8, "samplerCube", a2.Pass, (r9, o8, t2) => r9.bindTexture(e8, s4(o8, t2)));
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/CloudsParallaxShading.glsl.js
function v3(v4) {
  const R = v4.fragment;
  R.uniforms.add([new e5("rotationMatrixClouds", (o8, a6) => a6.cloudsFade.parallax.transform), new e5("rotationMatrixCloudsCrossFade", (o8, a6) => a6.cloudsFade.parallaxNew.transform), new e4("anchorPosition", (o8, a6) => a6.cloudsFade.parallax.anchorPointClouds), new e4("anchorPositionCrossFade", (o8, a6) => a6.cloudsFade.parallaxNew.anchorPointClouds), new o4("cloudsHeight", (o8, a6) => a6.cloudsFade.parallax.cloudsHeight), new o4("radiusCurvatureCorrectionFactor", (o8, a6) => a6.cloudsFade.parallax.radiusCurvatureCorrectionFactor), new o4("totalFadeInOut", (o8, a6) => a6.cloudsFade.fadeInOut.stage === I2.FINISHED ? a6.cloudsFade.fadeInOutHeight.factor + 1 - a6.cloudsFade.fadeIn.factor : a6.cloudsFade.fadeInOutHeight.factor + 1 - a6.cloudsFade.fadeInOut.factor), new o4("crossFadeAnchorFactor", (a6, t2) => a(t2.cloudsFade.crossFade.factor, 0, 1)), new s3("cubeMap", (o8, t2) => r(t2.cloudsFade.data) && r(t2.cloudsFade.data.cubeMap) ? t2.cloudsFade.data.cubeMap.colorTexture : null), new s2("crossFade", (o8, a6) => a6.cloudsFade.crossFade.enabled), new s2("readChannelsRG", (o8, a6) => a6.cloudsFade.readChannels === I.RG), new s2("fadeTextureChannels", (o8, a6) => a6.cloudsFade.renderingStage === E.FADING_TEXTURE_CHANNELS)]), R.constants.add("planetRadius", "float", s.radius), R.code.add(o`vec3 intersectWithCloudLayer(vec3 dir, vec3 cameraPosition, vec3 spherePos)
{
float radiusClouds = planetRadius + cloudsHeight;
float B = 2.0 * dot(cameraPosition, dir);
float C = dot(cameraPosition, cameraPosition) - radiusClouds * radiusClouds;
float det = B * B - 4.0 * C;
float pointIntDist = max(0.0, 0.5 *(-B + sqrt(det)));
vec3 intersectionPont = cameraPosition + dir * pointIntDist;
intersectionPont =  intersectionPont - spherePos;
return intersectionPont;
}`), R.code.add(o`vec3 correctForPlanetCurvature(vec3 dir)
{
dir.z = dir.z*(1.-radiusCurvatureCorrectionFactor) + radiusCurvatureCorrectionFactor;
return dir;
}`), R.code.add(o`vec3 rotateDirectionToAnchorPoint(mat4 rotMat, vec3 inVec)
{
return (rotMat * vec4(inVec, 0.0)).xyz;
}`), o5(R), a5(R), R.code.add(o`const float SUNSET_TRANSITION_FACTOR = 0.3;
const vec3 RIM_COLOR = vec3(0.28, 0.175, 0.035);
const float RIM_SCATTERING_FACTOR = 140.0;
const float BACKLIGHT_FACTOR = 0.2;
const float BACKLIGHT_SCATTERING_FACTOR = 10.0;
const float BACKLIGHT_TRANSITION_FACTOR = 0.3;
vec3 calculateCloudColor(vec3 cameraPosition, vec3 worldSpaceRay, vec4 clouds)
{
float upDotLight = dot(normalize(cameraPosition), normalize(mainLightDirection));
float dirDotLight = max(dot(normalize(-worldSpaceRay), normalize(mainLightDirection)), 0.0);
float sunsetTransition = clamp(pow(max(upDotLight, 0.0), SUNSET_TRANSITION_FACTOR), 0.0, 1.0);
vec3 ambientLight = calculateAmbientIrradiance(normalize(cameraPosition),  0.0);
vec3 mainLight = evaluateMainLighting(normalize(cameraPosition),  0.0);
vec3 combinedLight = clamp((mainLightIntensity + ambientLight )/PI, vec3(0.0), vec3(1.0));
vec3 baseCloudColor = pow(combinedLight * pow(clouds.xyz, vec3(GAMMA)), vec3(INV_GAMMA));
float scatteringMod = max(clouds.a < 0.5 ? clouds.a / 0.5 : - clouds.a / 0.5 + 2.0, 0.0);
float rimLightIntensity = 0.5 + 0.5 *pow(max(upDotLight, 0.0), 0.35);
vec3 directSunScattering = RIM_COLOR * rimLightIntensity * (pow(dirDotLight, RIM_SCATTERING_FACTOR)) * scatteringMod;
float additionalLight = BACKLIGHT_FACTOR * pow(dirDotLight, BACKLIGHT_SCATTERING_FACTOR) * (1. - pow(sunsetTransition, BACKLIGHT_TRANSITION_FACTOR)) ;
return vec3(baseCloudColor * (1. + additionalLight) + directSunScattering);
}`), R.code.add(o`vec4 getCloudData(vec3 rayDir, bool readOtherChannel)
{
vec4 cloudData = textureCube(cubeMap, rayDir);
float mu = dot(rayDir, vec3(0, 0, 1));
bool readChannels = readChannelsRG ^^ readOtherChannel;
if (readChannels) {
cloudData = vec4(vec3(cloudData.r), cloudData.g);
} else {
cloudData = vec4(vec3(cloudData.b), cloudData.a);
}
if (length(cloudData) == 0.0) {
return vec4(cloudData.rgb, 1.0);
}
return cloudData;
}`), R.code.add(o`vec4 renderCloudsNoFade(vec3 worldRay, vec3 cameraPosition)
{
vec3 intersectionPoint = intersectWithCloudLayer(normalize(worldRay), cameraPosition, anchorPosition);
vec3 worldRayRotated = rotateDirectionToAnchorPoint(rotationMatrixClouds, normalize(intersectionPoint));
vec3 worldRayRotatedCorrected = correctForPlanetCurvature(worldRayRotated);
vec4 cloudData = getCloudData(worldRayRotatedCorrected, false);
float totalTransmittance = clamp(cloudData.a * (1.0 - totalFadeInOut) + totalFadeInOut, 0.0 , 1.0);
if (length(cloudData.rgb) == 0.0) {
totalTransmittance = 1.0;
}
return vec4(calculateCloudColor(cameraPosition, normalize(-worldRay), cloudData), totalTransmittance);
}`), R.code.add(o`vec4 renderCloudsCrossFade(vec3 worldRay, vec3 cameraPosition)
{
vec3 intersectionPoint = intersectWithCloudLayer(normalize(worldRay), cameraPosition, anchorPosition);
vec3 worldRayRotated = rotateDirectionToAnchorPoint(rotationMatrixClouds, normalize(intersectionPoint));
vec3 worldRayRotatedCorrected = correctForPlanetCurvature(worldRayRotated);
vec4 cloudData = getCloudData(worldRayRotatedCorrected, false);
vec4 cloudColor = vec4(calculateCloudColor(cameraPosition, normalize(-worldRay), cloudData), cloudData.a);
intersectionPoint = intersectWithCloudLayer(normalize(worldRay), cameraPosition, anchorPositionCrossFade);
worldRayRotated = rotateDirectionToAnchorPoint(rotationMatrixCloudsCrossFade, normalize(intersectionPoint));
worldRayRotatedCorrected = correctForPlanetCurvature(worldRayRotated);
cloudData = getCloudData(worldRayRotatedCorrected, fadeTextureChannels);
vec4 cloudColorCrossFade = vec4(calculateCloudColor(cameraPosition, normalize(-worldRay), cloudData), cloudData.a);
cloudColor = mix(cloudColor, cloudColorCrossFade, crossFadeAnchorFactor);
float totalTransmittance = clamp(cloudColor.a * (1.0 - totalFadeInOut) + totalFadeInOut, 0.0 , 1.0);
if (length(cloudColor.rgb) == 0.0) {
totalTransmittance = 1.0;
}
return vec4(cloudColor.rgb, totalTransmittance);
}`), R.code.add(o`vec4 renderClouds(vec3 worldRay, vec3 cameraPosition)
{
return crossFade ? renderCloudsCrossFade(worldRay, cameraPosition) : renderCloudsNoFade(worldRay, cameraPosition);
}`);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/Water.glsl.js
function c4(c5, s4) {
  c5.include(r6, s4), c5.include(e7), c5.include(n5), s4.hasCloudsReflections && c5.include(v3, s4), s4.hasScreenSpaceReflections && c5.include(n7, s4);
  const f3 = c5.fragment;
  f3.constants.add("fresnelSky", "vec3", [0.02, 1, 15]).add("fresnelMaterial", "vec2", [0.02, 0.1]).add("roughness", "float", 0.015).add("foamIntensityExternal", "float", 1.7).add("ssrIntensity", "float", 0.65).add("ssrHeightFadeStart", "float", 3e5).add("ssrHeightFadeEnd", "float", 5e5).add("waterDiffusion", "float", 0.92).add("waterSeaColorMod", "float", 0.8).add("correctionViewingPowerFactor", "float", 0.4).add("skyZenitColor", "vec3", [0.52, 0.68, 0.9]).add("skyColor", "vec3", [0.67, 0.79, 0.9]).add("cloudFresnelModifier", "vec2", [1.2, 0.01]), f3.code.add(o`PBRShadingWater shadingInfo;
vec3 getSkyGradientColor(in float cosTheta, in vec3 horizon, in vec3 zenit) {
float exponent = pow((1.0 - cosTheta), fresnelSky[2]);
return mix(zenit, horizon, exponent);
}`), f3.uniforms.add([new o4("lightingSpecularStrength", (e8, o8) => o8.lighting.mainLight.specularStrength), new o4("lightingEnvironmentStrength", (e8, o8) => o8.lighting.mainLight.environmentStrength)]), f3.code.add(o`vec3 getSeaColor(in vec3 n, in vec3 v, in vec3 l, vec3 color, in vec3 lightIntensity, in vec3 localUp, in float shadow, float foamIntensity, vec3 viewPosition, vec3 position) {
float reflectionHit = 0.0;
float reflectionHitDiffused = 0.0;
vec3 seaWaterColor = linearizeGamma(color);
vec3 h = normalize(l + v);
shadingInfo.NdotL = clamp(dot(n, l), 0.0, 1.0);
shadingInfo.NdotV = clamp(dot(n, v), 0.001, 1.0);
shadingInfo.VdotN = clamp(dot(v, n), 0.001, 1.0);
shadingInfo.NdotH = clamp(dot(n, h), 0.0, 1.0);
shadingInfo.VdotH = clamp(dot(v, h), 0.0, 1.0);
shadingInfo.LdotH = clamp(dot(l, h), 0.0, 1.0);
float upDotV = max(dot(localUp,v), 0.0);
vec3 skyHorizon = linearizeGamma(skyColor);
vec3 skyZenit = linearizeGamma(skyZenitColor);
vec3 skyColor = getSkyGradientColor(upDotV, skyHorizon, skyZenit );
float upDotL = max(dot(localUp,l),0.0);
float daytimeMod = 0.1 + upDotL * 0.9;
skyColor *= daytimeMod;
float shadowModifier = clamp(shadow, 0.8, 1.0);
vec3 fresnelModifier = fresnelReflection(shadingInfo.VdotN, vec3(fresnelSky[0]), fresnelSky[1]);
vec3 reflSky = lightingEnvironmentStrength * fresnelModifier * skyColor * shadowModifier;
vec3 reflSea = seaWaterColor * mix(skyColor, upDotL * lightIntensity * LIGHT_NORMALIZATION, 2.0 / 3.0) * shadowModifier;
vec3 specular = vec3(0.0);
if(upDotV > 0.0 && upDotL > 0.0) {
vec3 specularSun = brdfSpecularWater(shadingInfo, roughness, vec3(fresnelMaterial[0]), fresnelMaterial[1]);
vec3 incidentLight = lightIntensity * LIGHT_NORMALIZATION * shadow;
specular = lightingSpecularStrength * shadingInfo.NdotL * incidentLight * specularSun;
}
vec3 foam = vec3(0.0);
if(upDotV > 0.0) {
foam = foamIntensity2FoamColor(foamIntensityExternal, foamIntensity, skyZenitColor, daytimeMod);
}
float correctionViewingFactor = pow(max(dot(v, localUp), 0.0), correctionViewingPowerFactor);
vec3 normalCorrectedClouds = mix(localUp, n, correctionViewingFactor);
vec3 reflectedWorld = normalize(reflect(-v, normalCorrectedClouds));`), s4.hasCloudsReflections && f3.code.add(o`vec4 cloudsColor = renderClouds(reflectedWorld, position);
cloudsColor.a = 1.0 - cloudsColor.a;
cloudsColor = pow(cloudsColor, vec4(GAMMA));
cloudsColor *= clamp(fresnelModifier.y*cloudFresnelModifier[0] - cloudFresnelModifier[1], 0.0, 1.0) * clamp((1.0 - totalFadeInOut), 0.0, 1.0);`), s4.hasScreenSpaceReflections ? (f3.uniforms.add([new e5("view", (e8, o8) => o8.ssr.camera.viewMatrix), new f("lastFrameColorTexture", (e8, o8) => o8.ssr.lastFrameColorTexture), new o4("fadeFactor", (e8, o8) => o8.ssr.fadeFactor)]), f3.code.add(o`vec3 viewDir = normalize(viewPosition);
vec4 viewNormalVectorCoordinate = view *vec4(n, 0.0);
vec3 viewNormal = normalize(viewNormalVectorCoordinate.xyz);
vec4 viewUp = view * vec4(localUp, 0.0);
vec3 viewNormalCorrectedSSR = mix(viewUp.xyz, viewNormal, correctionViewingFactor);
vec3 reflected = normalize(reflect(viewDir, viewNormalCorrectedSSR));
vec3 hitCoordinate = screenSpaceIntersection(reflected, viewPosition, viewDir, viewUp.xyz);
vec3 reflectedColor = vec3(0.0);
if (hitCoordinate.z > 0.0)
{
vec2 reprojectedCoordinate = reprojectionCoordinate(hitCoordinate);
vec2 dCoords = smoothstep(0.3, 0.6, abs(vec2(0.5, 0.5) - hitCoordinate.xy));
float heightMod = smoothstep(ssrHeightFadeEnd, ssrHeightFadeStart, -viewPosition.z);
reflectionHit = clamp(1.0 - (1.3 * dCoords.y), 0.0, 1.0) * heightMod * fadeFactor;
reflectionHitDiffused = waterDiffusion * reflectionHit;
reflectedColor = linearizeGamma(texture2D(lastFrameColorTexture, reprojectedCoordinate).xyz) *
reflectionHitDiffused * fresnelModifier.y * ssrIntensity;
}
float seaColorMod =  mix(waterSeaColorMod, waterSeaColorMod * 0.5, reflectionHitDiffused);
vec3 waterRenderedColor = tonemapACES((1.0 - reflectionHitDiffused) * reflSky + reflectedColor +
reflSea * seaColorMod + specular + foam);`)) : f3.code.add(o`vec3 waterRenderedColor = tonemapACES(reflSky + reflSea * waterSeaColorMod + specular + foam);`), s4.hasCloudsReflections ? s4.hasScreenSpaceReflections ? f3.code.add(o`return waterRenderedColor * (1.0 - (1.0 - reflectionHit) * cloudsColor.a) + (1.0 - reflectionHit) * cloudsColor.xyz;
}`) : f3.code.add(o`return waterRenderedColor * (1.0 - cloudsColor.a) + cloudsColor.xyz;
}`) : f3.code.add(o`return waterRenderedColor;
}`);
}

// node_modules/@arcgis/core/chunks/WaterSurface.glsl.js
function P(P2) {
  const _2 = new o2(), { vertex: M, fragment: S } = _2;
  v(M, P2), _2.include(r4, P2), _2.attributes.add(O.POSITION, "vec3"), _2.attributes.add(O.UV0, "vec2");
  const x = new e2("waterColor", (e8) => e8.color);
  if (P2.output === h.Color && P2.isDraped) return _2.varyings.add("vpos", "vec3"), M.uniforms.add(x), M.code.add(o`
        void main(void) {
          if (waterColor.a < ${o.float(t)}) {
            // Discard this vertex
            gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
            return;
          }

          vpos = position;
          gl_Position = transformPosition(proj, view, vpos);
        }
    `), S.uniforms.add(x), S.code.add(o`void main() {
gl_FragColor = waterColor;
}`), _2;
  switch (P2.output !== h.Color && P2.output !== h.Alpha || (_2.include(r7, P2), _2.include(d2, P2), _2.varyings.add("vuv", "vec2"), _2.varyings.add("vpos", "vec3"), _2.varyings.add("vnormal", "vec3"), _2.varyings.add("vtbnMatrix", "mat3"), P2.hasMultipassTerrain && _2.varyings.add("depth", "float"), M.uniforms.add(x), M.code.add(o`
      void main(void) {
        if (waterColor.a < ${o.float(t)}) {
          // Discard this vertex
          gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
          return;
        }

        vuv = uv0;
        vpos = position;

        vnormal = getLocalUp(vpos, localOrigin);
        vtbnMatrix = getTBNMatrix(vnormal);

        ${P2.hasMultipassTerrain ? "depth = (view * vec4(vpos, 1.0)).z;" : ""}

        gl_Position = transformPosition(proj, view, vpos);
        ${P2.output === h.Color ? "forwardLinearDepth();" : ""}
      }
    `)), _2.include(n4, P2), P2.output) {
    case h.Alpha:
      _2.include(u, P2), S.uniforms.add(x), S.code.add(o`
        void main() {
          discardBySlice(vpos);
          ${P2.hasMultipassTerrain ? "terrainDepthTest(gl_FragCoord, depth);" : ""}

          gl_FragColor = vec4(waterColor.a);
        }
      `);
      break;
    case h.Color:
      _2.include(r5, P2), _2.include(m, { pbrMode: d.Disabled, lightingSphericalHarmonicsOrder: 2 }), _2.include(i2), _2.include(u, P2), _2.include(v2, P2), _2.include(c4, P2), S.uniforms.add([x, new o4("timeElapsed", (e8) => e8.timeElapsed), M.uniforms.get("view"), M.uniforms.get("localOrigin")]), c(S, P2), S.include(e6), o5(S), a5(S), S.code.add(o`
      void main() {
        discardBySlice(vpos);
        ${P2.hasMultipassTerrain ? "terrainDepthTest(gl_FragCoord, depth);" : ""}
        vec3 localUp = vnormal;
        // the created normal is in tangent space
        vec4 tangentNormalFoam = getSurfaceNormalAndFoam(vuv, timeElapsed);

        // we rotate the normal according to the tangent-bitangent-normal-Matrix
        vec3 n = normalize(vtbnMatrix * tangentNormalFoam.xyz);
        vec3 v = -normalize(vpos - cameraPosition);
        float shadow = ${P2.receiveShadows ? o`1.0 - readShadowMap(vpos, linearDepth)` : "1.0"};
        vec4 vPosView = view * vec4(vpos, 1.0);
        vec4 final = vec4(getSeaColor(n, v, mainLightDirection, waterColor.rgb, mainLightIntensity, localUp, shadow, tangentNormalFoam.w, vPosView.xyz, vpos + localOrigin), waterColor.w);

        // gamma correction
        gl_FragColor = delinearizeGamma(final);
        gl_FragColor = highlightSlice(gl_FragColor, vpos);
        ${P2.transparencyPassType === o3.Color ? "gl_FragColor = premultiplyAlpha(gl_FragColor);" : ""}
      }
    `);
      break;
    case h.Normal:
      _2.include(r7, P2), _2.include(i2, P2), _2.include(u, P2), _2.varyings.add("vpos", "vec3"), _2.varyings.add("vuv", "vec2"), M.uniforms.add(x), M.code.add(o`
        void main(void) {
          if (waterColor.a < ${o.float(t)}) {
            // Discard this vertex
            gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
            return;
          }

          vuv = uv0;
          vpos = position;

          gl_Position = transformPosition(proj, view, vpos);
        }
    `), S.uniforms.add(new o4("timeElapsed", (e8) => e8.timeElapsed)), S.code.add(o`void main() {
discardBySlice(vpos);
vec4 tangentNormalFoam = getSurfaceNormalAndFoam(vuv, timeElapsed);
tangentNormalFoam.xyz = normalize(tangentNormalFoam.xyz);
gl_FragColor = vec4((tangentNormalFoam.xyz + vec3(1.0)) * 0.5, tangentNormalFoam.w);
}`);
      break;
    case h.Highlight:
      _2.include(a3, P2), _2.varyings.add("vpos", "vec3"), M.uniforms.add(x), M.code.add(o`
      void main(void) {
        if (waterColor.a < ${o.float(t)}) {
          // Discard this vertex
          gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
          return;
        }

        vpos = position;
        gl_Position = transformPosition(proj, view, vpos);
      }
    `), _2.include(u, P2), S.code.add(o`void main() {
discardBySlice(vpos);
outputHighlight();
}`);
  }
  return _2;
}
var _ = Object.freeze(Object.defineProperty({ __proto__: null, build: P }, Symbol.toStringTag, { value: "Module" }));

export {
  i3 as i,
  c3 as c,
  P,
  _
};
//# sourceMappingURL=chunk-V57FPENY.js.map
