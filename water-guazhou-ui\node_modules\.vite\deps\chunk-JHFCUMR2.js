import {
  j,
  t
} from "./chunk-VCDD3IVD.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/views/interactive/tooltip/TranslateTooltipInfos.js
var r = class extends t {
  constructor(t2) {
    super(t2), this.type = "translate-graphic", this.distance = j;
  }
};
e([y()], r.prototype, "type", void 0), e([y()], r.prototype, "distance", void 0), r = e([a("esri.views.interactive.tooltip.TranslateGraphicTooltipInfo")], r);
var p = class extends t {
  constructor(t2) {
    super(t2), this.type = "translate-graphic-z", this.distance = j;
  }
};
e([y()], p.prototype, "type", void 0), e([y()], p.prototype, "distance", void 0), p = e([a("esri.views.interactive.tooltip.TranslateGraphicZTooltipInfo")], p);
var a2 = class extends t {
  constructor(t2) {
    super(t2), this.type = "translate-graphic-xy", this.distance = j;
  }
};
e([y()], a2.prototype, "type", void 0), e([y()], a2.prototype, "distance", void 0), a2 = e([a("esri.views.interactive.tooltip.TranslateGraphicXYTooltipInfo")], a2);
var n = class extends t {
  constructor(t2) {
    super(t2), this.type = "translate-vertex", this.distance = j, this.elevation = null, this.area = null, this.totalLength = null;
  }
};
e([y()], n.prototype, "type", void 0), e([y()], n.prototype, "distance", void 0), e([y()], n.prototype, "elevation", void 0), e([y()], n.prototype, "area", void 0), e([y()], n.prototype, "totalLength", void 0), n = e([a("esri.views.interactive.tooltip.TranslateVertexTooltipInfo")], n);
var l = class extends t {
  constructor(t2) {
    super(t2), this.type = "translate-vertex-z", this.distance = j, this.elevation = null;
  }
};
e([y()], l.prototype, "type", void 0), e([y()], l.prototype, "distance", void 0), e([y()], l.prototype, "elevation", void 0), l = e([a("esri.views.interactive.tooltip.TranslateVertexZTooltipInfo")], l);
var c = class extends t {
  constructor(t2) {
    super(t2), this.type = "translate-vertex-xy", this.distance = j, this.elevation = null, this.area = null, this.totalLength = null;
  }
};
e([y()], c.prototype, "type", void 0), e([y()], c.prototype, "distance", void 0), e([y()], c.prototype, "elevation", void 0), e([y()], c.prototype, "area", void 0), e([y()], c.prototype, "totalLength", void 0), c = e([a("esri.views.interactive.tooltip.TranslateVertexXYTooltipInfo")], c);

export {
  r,
  p,
  a2 as a,
  n,
  l,
  c
};
//# sourceMappingURL=chunk-JHFCUMR2.js.map
