{"version": 3, "sources": ["../../@arcgis/core/rest/support/AttachmentQuery.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{clone as r}from\"../../core/lang.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import{ensureType as s}from\"../../core/accessorSupport/ensureType.js\";import{subclass as i}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as n}from\"../../core/accessorSupport/decorators/writer.js\";var a;let p=a=class extends e{constructor(t){super(t),this.attachmentTypes=null,this.attachmentsWhere=null,this.cacheHint=void 0,this.keywords=null,this.globalIds=null,this.name=null,this.num=null,this.objectIds=null,this.returnMetadata=!1,this.size=null,this.start=null,this.where=null}writeStart(t,e){e.resultOffset=this.start,e.resultRecordCount=this.num||10}clone(){return new a(r({attachmentTypes:this.attachmentTypes,attachmentsWhere:this.attachmentsWhere,cacheHint:this.cacheHint,keywords:this.keywords,where:this.where,globalIds:this.globalIds,name:this.name,num:this.num,objectIds:this.objectIds,returnMetadata:this.returnMetadata,size:this.size,start:this.start}))}};t([o({type:[String],json:{write:!0}})],p.prototype,\"attachmentTypes\",void 0),t([o({type:String,json:{read:{source:\"attachmentsDefinitionExpression\"},write:{target:\"attachmentsDefinitionExpression\"}}})],p.prototype,\"attachmentsWhere\",void 0),t([o({type:Boolean,json:{write:!0}})],p.prototype,\"cacheHint\",void 0),t([o({type:[String],json:{write:!0}})],p.prototype,\"keywords\",void 0),t([o({type:[Number],json:{write:!0}})],p.prototype,\"globalIds\",void 0),t([o({json:{write:!0}})],p.prototype,\"name\",void 0),t([o({type:Number,json:{read:{source:\"resultRecordCount\"}}})],p.prototype,\"num\",void 0),t([o({type:[Number],json:{write:!0}})],p.prototype,\"objectIds\",void 0),t([o({type:Boolean,json:{default:!1,write:!0}})],p.prototype,\"returnMetadata\",void 0),t([o({type:[Number],json:{write:!0}})],p.prototype,\"size\",void 0),t([o({type:Number,json:{read:{source:\"resultOffset\"}}})],p.prototype,\"start\",void 0),t([n(\"start\"),n(\"num\")],p.prototype,\"writeStart\",null),t([o({type:String,json:{read:{source:\"definitionExpression\"},write:{target:\"definitionExpression\"}}})],p.prototype,\"where\",void 0),p=a=t([i(\"esri.rest.support.AttachmentQuery\")],p),p.from=s(p);const c=p;export{c as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAI0b,IAAIA;AAAE,IAAIC,KAAED,KAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,kBAAgB,MAAK,KAAK,mBAAiB,MAAK,KAAK,YAAU,QAAO,KAAK,WAAS,MAAK,KAAK,YAAU,MAAK,KAAK,OAAK,MAAK,KAAK,MAAI,MAAK,KAAK,YAAU,MAAK,KAAK,iBAAe,OAAG,KAAK,OAAK,MAAK,KAAK,QAAM,MAAK,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,WAAW,GAAEE,IAAE;AAAC,IAAAA,GAAE,eAAa,KAAK,OAAMA,GAAE,oBAAkB,KAAK,OAAK;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIF,GAAE,EAAE,EAAC,iBAAgB,KAAK,iBAAgB,kBAAiB,KAAK,kBAAiB,WAAU,KAAK,WAAU,UAAS,KAAK,UAAS,OAAM,KAAK,OAAM,WAAU,KAAK,WAAU,MAAK,KAAK,MAAK,KAAI,KAAK,KAAI,WAAU,KAAK,WAAU,gBAAe,KAAK,gBAAe,MAAK,KAAK,MAAK,OAAM,KAAK,MAAK,CAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAC,QAAO,kCAAiC,GAAE,OAAM,EAAC,QAAO,kCAAiC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAC,QAAO,oBAAmB,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,OAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAC,QAAO,eAAc,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,OAAO,GAAE,EAAE,KAAK,CAAC,GAAEA,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAC,QAAO,uBAAsB,GAAE,OAAM,EAAC,QAAO,uBAAsB,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,mCAAmC,CAAC,GAAEC,EAAC,GAAEA,GAAE,OAAK,EAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["a", "p", "e"]}