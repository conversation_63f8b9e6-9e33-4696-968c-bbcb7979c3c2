{"version": 3, "sources": ["../../@arcgis/core/layers/support/capabilities.js", "../../@arcgis/core/views/3d/layers/i3s/I3SProjectionUtil.js", "../../@arcgis/core/views/3d/support/orientedBoundingBox.js", "../../@arcgis/core/views/3d/layers/i3s/I3SUtil.js", "../../@arcgis/core/layers/support/FetchAssociatedFeatureLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst t={analytics:{supportsCacheHint:!1},attachment:{supportsContentType:!1,supportsExifInfo:!1,supportsKeywords:!1,supportsName:!1,supportsSize:!1,supportsCacheHint:!1,supportsResize:!1},data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:!1},editing:{supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsGeometryUpdate:!1,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1,supportsUpdateWithoutM:!1,supportsUploadWithItemId:!1},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:!1,supportsDelete:!1,supportsEditing:!1,supportsChangeTracking:!1,supportsQuery:!1,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:!1,supportsExceedsLimitStatistics:!1},queryRelated:{supportsCount:!1,supportsOrderBy:!1,supportsPagination:!1,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},query:{maxRecordCount:0,maxRecordCountFactor:0,standardMaxRecordCount:0,supportsCacheHint:!1,supportsCentroid:!1,supportsCompactGeometry:!1,supportsDefaultSpatialReference:!1,supportsFullTextSearch:!1,supportsDisjointSpatialRelationship:!1,supportsDistance:!1,supportsDistinct:!1,supportsExtent:!1,supportsFormatPBF:!1,supportsGeometryProperties:!1,supportsHavingClause:!1,supportsHistoricMoment:!1,supportsMaxRecordCountFactor:!1,supportsOrderBy:!1,supportsPagination:!1,supportsPercentileStatistics:!1,supportsQuantization:!1,supportsQuantizationEditMode:!1,supportsQueryByOthers:!1,supportsQueryGeometry:!1,supportsResultType:!1,supportsSqlExpression:!1,supportsStandardizedQueriesOnly:!1,supportsTopFeaturesQuery:!1,supportsSpatialAggregationStatistics:!1,supportedSpatialAggregationStatistics:{envelope:!1,centroid:!1,convexHull:!1},supportsStatistics:!1,tileMaxRecordCount:0}};export{t as zeroCapabilities};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as t}from\"../../../../chunks/mat4f64.js\";import{c as o}from\"../../../../chunks/vec3f64.js\";import{getReferenceEllipsoid as r}from\"../../../../geometry/ellipsoidUtils.js\";import{computeTranslationToOriginAndRotation as a}from\"../../../../geometry/projection.js\";function n(o,r,n,s){const c=h(o,r,n),i=t();return a(n,c,i,s),i}const s=1,c=5-s;function h(t,a,n){const h=o(),i=t[3],e=2**(Math.ceil(Math.log(i)*Math.LOG2E/c)*c+s);if(n.isGeographic){const o=e/r(n).radius*180/Math.PI,a=Math.round(t[1]/o),s=Math.max(-90,Math.min(90,a*o)),c=o/Math.cos((Math.abs(s)-o/2)/180*Math.PI),i=Math.round(t[0]/c)*c;h[0]=i,h[1]=s}else{const o=Math.round(t[0]/e),r=Math.round(t[1]/e);h[0]=o*e,h[1]=r*e}const M=t[2]+a,m=Math.round(M/e);return h[2]=m*e,h}export{n as computeGlobalTransformation,h as getLocalOrigin};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as e,d as t}from\"../../../chunks/mat3.js\";import{c as a}from\"../../../chunks/mat3f64.js\";import{a as n,c as r}from\"../../../chunks/quat.js\";import{a as s,b as i}from\"../../../chunks/quatf32.js\";import{a as f}from\"../../../chunks/quatf64.js\";import{c as o,b as c,q as h,w as u,s as l,t as z,a as S,u as m,x as b,y as M,l as p,g as q,z as j,A as k,B as g}from\"../../../chunks/vec3.js\";import{a as x,b as y}from\"../../../chunks/vec3f32.js\";import{b as v,a as w,c as d}from\"../../../chunks/vec3f64.js\";import{t as A}from\"../../../chunks/vec4.js\";import{c as B}from\"../../../chunks/vec4f64.js\";import{create as G}from\"../../../geometry/support/aaBoundingBox.js\";import{signedDistance as I,normal as V}from\"../../../geometry/support/plane.js\";import{ViewingMode as C}from\"../../ViewingMode.js\";import{computeOBB as D}from\"./dito.js\";const E=f(),F=d(),H=d(),J=B(),K=a();class L{constructor(e){const t=56,a=0,n=24,r=36,i=e*t;this.buffer=new ArrayBuffer(i),this.obbs=new Array(e);for(let f=0;f<e;f++)this.obbs[f]={center:v(this.buffer,t*f+a),halfSize:x(this.buffer,t*f+n),quaternion:s(this.buffer,t*f+r)}}}function N(e=[0,0,0],t=[-1,-1,-1],a=f()){return{center:w(e),halfSize:y(t),quaternion:i(a)}}function O(e){return N(e.center,e.halfSize,e.quaternion)}function P(e,t){o(t.center,e.center),o(t.halfSize,e.halfSize),n(t.quaternion,e.quaternion)}function Q(e,t){return t=t||N(),D(e,t),t}function R(e,t){const a=I(t,e.center),n=ee(e,V(t));return a>n?1:a<-n?-1:0}function T(t,a){a||(a=G());const n=e(K,t.quaternion),r=t.halfSize[0]*Math.abs(n[0])+t.halfSize[1]*Math.abs(n[3])+t.halfSize[2]*Math.abs(n[6]),s=t.halfSize[0]*Math.abs(n[1])+t.halfSize[1]*Math.abs(n[4])+t.halfSize[2]*Math.abs(n[7]),i=t.halfSize[0]*Math.abs(n[2])+t.halfSize[1]*Math.abs(n[5])+t.halfSize[2]*Math.abs(n[8]);return a[0]=t.center[0]-r,a[1]=t.center[1]-s,a[2]=t.center[2]-i,a[3]=t.center[0]+r,a[4]=t.center[1]+s,a[5]=t.center[2]+i,a}function U(e,t){return I(t,e.center)-ee(e,V(t))}function W(e,t){return I(t,e.center)+ee(e,V(t))}function X(e,t){return R(e,t[0])<=0&&R(e,t[1])<=0&&R(e,t[2])<=0&&R(e,t[3])<=0&&R(e,t[4])<=0&&R(e,t[5])<=0}function Y(e,t,a,n=0){r(E,e.quaternion),c(F,t,e.center);const s=h(F,F,E),i=h(H,a,E);let f=-1/0,o=1/0;for(let r=0;r<3;r++)if(Math.abs(i[r])>1e-6){const t=(n+e.halfSize[r]-s[r])/i[r],a=(-n-e.halfSize[r]-s[r])/i[r];f=Math.max(f,Math.min(t,a)),o=Math.min(o,Math.max(t,a))}else if(s[r]>e.halfSize[r]+n||s[r]<-e.halfSize[r]-n)return!1;return f<=o}function Z(a,n,s,i,f){r(E,a.quaternion),u(F,n,a.center),h(F,F,E);const o=F[0]<-a.halfSize[0]?-1:F[0]>a.halfSize[0]?1:0,c=F[1]<-a.halfSize[1]?-1:F[1]>a.halfSize[1]?1:0,m=F[2]<-a.halfSize[2]?-1:F[2]>a.halfSize[2]?1:0,b=Math.abs(o)+Math.abs(c)+Math.abs(m);if(0===b)return 1/0;const M=1===b?4:6,p=6*(o+3*c+9*m+13);e(K,a.quaternion),t(K,K,a.halfSize);for(let e=0;e<M;e++){const t=_[p+e];l(F,((1&t)<<1)-1,(2&t)-1,((4&t)>>1)-1),z(F,F,K),S(J,a.center,F),J[3]=1,A(J,J,s);const n=1/Math.max(1e-6,J[3]);$[2*e]=J[0]*n,$[2*e+1]=J[1]*n}const q=2*M-2;let j=$[0]*($[3]-$[q+1])+$[q]*($[1]-$[q-1]);for(let e=2;e<q;e+=2)j+=$[e]*($[e+3]-$[e-1]);return Math.abs(j)*i*f*.125}const $=[.1,.2,.3,.4,.5,.6,.7,.8,.9,1,1.1,1.2],_=(()=>{const e=new Int8Array(162);let t=0;const a=a=>{for(let n=0;n<a.length;n++)e[t+n]=a[n];t+=6};return a([6,2,3,1,5,4]),a([0,2,3,1,5,4]),a([0,2,3,7,5,4]),a([0,1,3,2,6,4]),a([0,1,3,2,0,0]),a([0,1,5,7,3,2]),a([0,1,3,7,6,4]),a([0,1,3,7,6,2]),a([0,1,5,7,6,2]),a([0,1,5,4,6,2]),a([0,1,5,4,0,0]),a([0,1,3,7,5,4]),a([0,2,6,4,0,0]),a([0,0,0,0,0,0]),a([1,3,7,5,0,0]),a([2,3,7,6,4,0]),a([2,3,7,6,0,0]),a([2,3,1,5,7,6]),a([0,1,5,7,6,2]),a([0,1,5,7,6,4]),a([0,1,3,7,6,4]),a([4,5,7,6,2,0]),a([4,5,7,6,0,0]),a([4,5,1,3,7,6]),a([0,2,3,7,5,4]),a([6,2,3,7,5,4]),a([6,2,3,1,5,4]),e})();function ee(e,t){r(E,e.quaternion),h(F,t,E);const a=e.halfSize;return Math.abs(F[0]*a[0])+Math.abs(F[1]*a[1])+Math.abs(F[2]*a[2])}function te(e,t){for(let a=0;a<8;++a){const n=t[a];n[0]=1&a?-e.halfSize[0]:e.halfSize[0],n[1]=2&a?-e.halfSize[1]:e.halfSize[1],n[2]=4&a?-e.halfSize[2]:e.halfSize[2],h(n,n,e.quaternion),S(n,n,e.center)}}function ae(e){return m(e.halfSize)}function ne(e,t,a,s,i){if(n(i.quaternion,e.quaternion),s===C.Global){r(fe,e.quaternion),h(re,e.center,fe),b(se,re),M(ie,se,e.halfSize),u(ie,se,ie);const n=p(ie);S(ie,se,e.halfSize);const s=p(ie);if(n<a)o(i.center,e.center),l(re,a,a,a),S(i.halfSize,e.halfSize,re);else{const r=s>0?1+t/s:1,f=n>0?1+a/n:1,o=(f+r)/2,c=(f-r)/2;q(i.halfSize,se,c),j(i.halfSize,i.halfSize,e.halfSize,o),q(i.center,se,o),j(i.center,i.center,e.halfSize,c),k(re,re),g(i.center,i.center,re),h(i.center,i.center,i.quaternion)}}else{const n=l(re,0,0,1);j(i.center,e.center,n,(a+t)/2),r(fe,e.quaternion),h(n,n,fe),b(n,n),j(i.halfSize,e.halfSize,n,(a-t)/2)}return i}const re=d(),se=d(),ie=d(),fe=f();export{L as ObbArray,O as clone,Q as compute,ne as computeOffsetObb,te as corners,N as create,Y as intersectLine,R as intersectPlane,X as isVisible,W as maximumDistancePlane,U as minimumDistancePlane,Z as projectedArea,ee as projectedRadius,ae as radius,P as set,T as toAaBoundingBox};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../request.js\";import{binaryIndexOf as t,splitIntoChunks as r}from\"../../../../core/arrayUtils.js\";import n from\"../../../../core/Error.js\";import has from\"../../../../core/has.js\";import{isSome as o,isNone as a,applySome as i}from\"../../../../core/maybe.js\";import{eachAlways as s}from\"../../../../core/promiseUtils.js\";import{isInt16Array as l,isInt32Array as c}from\"../../../../core/typedArrayUtil.js\";import{c as u}from\"../../../../chunks/mat3.js\";import{c as f}from\"../../../../chunks/mat3f64.js\";import{a as p}from\"../../../../chunks/mat4.js\";import{c as m}from\"../../../../chunks/mat4f64.js\";import{a as h,m as d,c as y}from\"../../../../chunks/quat.js\";import{c as b}from\"../../../../chunks/quatf32.js\";import{s as g,c as S,w,q as T,g as E,a as v,m as M}from\"../../../../chunks/vec3.js\";import{c as I}from\"../../../../chunks/vec3f64.js\";import{getReferenceEllipsoid as R}from\"../../../../geometry/ellipsoidUtils.js\";import{canProjectWithoutEngine as x,projectBuffer as j,computeTranslationToOriginAndRotation as q,projectVectorToVector as U}from\"../../../../geometry/projection.js\";import k from\"../../../../geometry/SpatialReference.js\";import{getSphericalPCPF as C}from\"../../../../geometry/spatialReferenceEllipsoidUtils.js\";import{create as N,empty as z,expandPointInPlace as W,intersects as A}from\"../../../../geometry/support/aaBoundingRect.js\";import{isPlateCarree as D}from\"../../../../geometry/support/spatialReferenceUtils.js\";import F from\"../../../../rest/support/Query.js\";import{readBinaryAttribute as _}from\"./I3SBinaryReader.js\";import{computeGlobalTransformation as O}from\"./I3SProjectionUtil.js\";import{createSolidEdgeMaterial as L,createMaterialFromEdges as G}from\"../support/edgeUtils.js\";import{parseColorMixMode as K,ColorMixModeEnum as B}from\"../support/symbolColorUtils.js\";import{create as P,compute as $,corners as Q}from\"../../support/orientedBoundingBox.js\";import{Attribute as Z}from\"../../webgl-engine/lib/Attribute.js\";function H(e){return e?parseInt(e.substring(e.lastIndexOf(\"/\")+1,e.length),10):void 0}function J(e){if(has(\"disable-feature:i3s-draco\")||!e)return!1;for(const t of e)for(const e of t.geometryBuffers)if(\"draco\"===e.compressedAttributes?.encoding)return!0;return!1}function V(e,t,r,n){r.traverse(t,(t=>{const r=t.mbs;return(null!=r&&ne(e,r))!==re.OUTSIDE&&(n(t),!0)}))}function X(e,t,r){let n=0,o=0;for(let a=0;a<t.length&&n<e.length;a++)e[n]===t[a]&&(r(a)&&(e[o]=e[n],o++),n++);e.length=o}function Y(e,r,n){let o=0,a=0;for(;o<n.length;){t(e,n[o])>=0===r&&(n[a]=n[o],a++),o++}n.length=a}const ee=N();function te(e,t){if(0===t.rotationScale[1]&&0===t.rotationScale[2]&&0===t.rotationScale[3]&&0===t.rotationScale[5]&&0===t.rotationScale[6]&&0===t.rotationScale[7])return ee[0]=(e[0]-t.position[0])/t.rotationScale[0],ee[1]=(e[1]-t.position[1])/t.rotationScale[4],ee[2]=(e[2]-t.position[0])/t.rotationScale[0],ee[3]=(e[3]-t.position[1])/t.rotationScale[4],ee}var re;function ne(e,t){const r=t[0],n=t[1],o=t[3],a=e[0]-r,i=r-e[2],s=e[1]-n,l=n-e[3],c=Math.max(a,i,0),u=Math.max(s,l,0),f=c*c+u*u;if(f>o*o)return re.OUTSIDE;if(f>0)return re.INTERSECTS_CENTER_OUTSIDE;return-Math.max(a,i,s,l)>o?re.INSIDE:re.INTERSECTS_CENTER_INSIDE}function oe(e,t,r){const n=[],o=r&&r.missingFields,a=r&&r.originalFields;for(const i of e){const e=i.toLowerCase();let r=!1;for(const o of t)if(e===o.name.toLowerCase()){n.push(o.name),r=!0,a&&a.push(i);break}!r&&o&&o.push(i)}return n}async function ae(e,t,r,i,s){if(0===t.length)return[];const l=e.attributeStorageInfo;if(o(e.associatedLayer))try{return await se(e.associatedLayer,t,r,i)}catch(c){if(e.associatedLayer.loaded)throw c}if(l){const o=ie(t,r,s);if(a(o))throw new n(\"scenelayer:features-not-loaded\",\"Tried to query attributes for unloaded features\");const c=e.parsedUrl.path;return(await Promise.all(o.map((e=>ce(c,l,e.node,e.indices,i).then((t=>{for(let r=0;r<e.graphics.length;r++){const n=e.graphics[r],o=t[r];if(n.attributes)for(const e in n.attributes)e in o||(o[e]=n.attributes[e]);n.attributes=o}return e.graphics})))))).flat()}throw new n(\"scenelayer:no-attribute-source\",\"This scene layer does not have a source for attributes available\")}function ie(e,t,r){const n=new Map,o=[],a=r();for(const i of e){const e=i.attributes[t];for(let t=0;t<a.length;t++){const r=a[t],s=r.featureIds.indexOf(e);if(s>=0){let e=n.get(r.node);e||(e={node:r.node,indices:[],graphics:[]},o.push(e),n.set(r.node,e)),e.indices.push(s),e.graphics.push(i);for(let r=t;r>0;r--)a[r]=a[r-1];a[0]=r;break}}}return o}async function se(e,t,r,n){t.sort(((e,t)=>e.attributes[r]-t.attributes[r]));const o=t.map((e=>e.attributes[r])),a=[],i=oe(n,e.fields,{originalFields:a}),s=await le(e,o,i);for(let l=0;l<t.length;l++){const e=t[l],r=s[l],n={};if(e.attributes)for(const t in e.attributes)n[t]=e.attributes[t];for(let t=0;t<a.length;t++)n[a[t]]=r[i[t]];e.attributes=n}return t}function le(e,t,o){const a=e.capabilities.query.maxRecordCount;if(null!=a&&t.length>a){const n=r(t,a);return Promise.all(n.map((t=>le(e,t,o)))).then((e=>e.flat()))}const i=new F({objectIds:t,outFields:o,orderByFields:[e.objectIdField]});return e.queryFeatures(i).then((e=>{if(e&&e.features&&e.features.length===t.length)return e.features.map((e=>e.attributes));throw new n(\"scenelayer:feature-not-in-associated-layer\",\"Feature not found in associated feature layer\")}))}function ce(e,t,r,n,o){return ue(e,t,r.resources.attributes,n,o)}function ue(t,r,n,o,a){const i=[];for(const e of r)if(e&&a.includes(e.name)){const r=`${t}/nodes/${n}/attributes/${e.key}/0`;i.push({url:r,storageInfo:e})}return s(i.map((t=>e(t.url,{responseType:\"array-buffer\"}).then((e=>_(t.storageInfo,e.data)))))).then((e=>{const t=[];for(const r of o){const n={};for(let t=0;t<e.length;t++){const o=e[t].value;null!=o&&(n[i[t].storageInfo.name]=me(o,r))}t.push(n)}return t}))}!function(e){e[e.OUTSIDE=0]=\"OUTSIDE\",e[e.INTERSECTS_CENTER_OUTSIDE=1]=\"INTERSECTS_CENTER_OUTSIDE\",e[e.INTERSECTS_CENTER_INSIDE=2]=\"INTERSECTS_CENTER_INSIDE\",e[e.INSIDE=3]=\"INSIDE\"}(re||(re={}));const fe=-32768,pe=-(2**31);function me(e,t){if(!e)return null;const r=e[t];if(l(e))return r===fe?null:r;if(c(e))return r===pe?null:r;return r!=r?null:r}function he(e){const t=e.store,r=t.indexCRS||t.geographicCRS,o=void 0===r?t.indexWKT:void 0;if(o){if(!e.spatialReference)throw new n(\"layerview:no-store-spatial-reference-wkt-index-and-no-layer-spatial-reference\",\"Found indeWKT in the scene layer store but no layer spatial reference\",{});if(o!==e.spatialReference.wkt)throw new n(\"layerview:store-spatial-reference-wkt-index-incompatible\",\"The indeWKT of the scene layer store does not match the WKT of the layer spatial reference\",{})}const a=r?new k(H(r)):e.spatialReference;return a.equals(e.spatialReference)?e.spatialReference:a}function de(e){const t=e.store,r=t.vertexCRS||t.projectedCRS,o=void 0===r?t.vertexWKT:void 0;if(o){if(!e.spatialReference)throw new n(\"layerview:no-store-spatial-reference-wkt-vertex-and-no-layer-spatial-reference\",\"Found vertexWKT in the scene layer store but no layer spatial reference\",{});if(o!==e.spatialReference.wkt)throw new n(\"layerview:store-spatial-reference-wkt-vertex-incompatible\",\"The vertexWKT of the scene layer store does not match the WKT of the layer spatial reference\",{})}const a=r?new k(H(r)):e.spatialReference;return a.equals(e.spatialReference)?e.spatialReference:a}function ye(e,t){return a(t)?\"@null\":t===C(t)?\"@ECEF\":e.equals(t)?\"\":null!=t.wkid?\"@\"+t.wkid:null}function be(e,t,r){if(!x(e,t))throw new n(\"layerview:spatial-reference-incompatible\",\"The spatial reference of this scene layer is incompatible with the spatial reference of the view\",{});if(\"local\"===r&&!Se(e,t))throw new n(\"layerview:spatial-reference-incompatible\",\"The spatial reference of this scene layer is incompatible with the spatial reference of the view\",{})}function ge(e,t,r){if(e.serviceUpdateTimeStamp?.lastUpdate!==t.serviceUpdateTimeStamp?.lastUpdate||!r.isEmpty||i(e.associatedLayer,(e=>e.url))!==i(t.associatedLayer,(e=>e.url)))throw new n(\"layerview:recycle-failed\")}function Se(e,t){return e.equals(t)||e.isWGS84&&t.isWebMercator||e.isWebMercator&&t.isWGS84}function we(e,t,r){const n=he(e),o=de(e);be(n,t,r),be(o,t,r)}function Te(e){return(null==e.geometryType||\"triangles\"===e.geometryType)&&((null==e.topology||\"PerAttributeArray\"===e.topology)&&(null!=e.vertexAttributes&&null!=e.vertexAttributes.position))}function Ee(e){if(null==e.store||null==e.store.defaultGeometrySchema||!Te(e.store.defaultGeometrySchema))throw new n(\"scenelayer:unsupported-geometry-schema\",\"The geometry schema of this scene layer is not supported.\",{url:e.parsedUrl.path})}function ve(e,t){we(e,t.spatialReference,t.viewingMode)}function Me(e){return null!=e.geometryType&&\"points\"===e.geometryType&&((null==e.topology||\"PerAttributeArray\"===e.topology)&&((null==e.encoding||\"\"===e.encoding||\"lepcc-xyz\"===e.encoding)&&(null!=e.vertexAttributes&&null!=e.vertexAttributes.position)))}function Ie(e){if(null==e.store||null==e.store.defaultGeometrySchema||!Me(e.store.defaultGeometrySchema))throw new n(\"pointcloud:unsupported-geometry-schema\",\"The geometry schema of this point cloud scene layer is not supported.\",{})}function Re(e,t){be(e.spatialReference,t.spatialReference,t.viewingMode)}function xe(e){return\"simple\"===e.type||\"class-breaks\"===e.type||\"unique-value\"===e.type}function je(e){return\"mesh-3d\"===e.type}function qe(e){if(null==e||!xe(e))return!0;if((\"unique-value\"===e.type||\"class-breaks\"===e.type)&&null==e.defaultSymbol)return!0;const t=e.getSymbols();if(0===t.length)return!0;for(const r of t){if(!je(r)||0===r.symbolLayers.length)return!0;for(const e of r.symbolLayers.items)if(\"fill\"!==e.type||a(e.material)||a(e.material.color)||\"replace\"!==e.material.colorMixMode)return!0}return!1}const Ue=L({color:[0,0,0,0],opacity:0});class ke{constructor(){this.edgeMaterial=null,this.material=null,this.castShadows=!0}}function Ce(e){const t=new ke;let r=!1,n=!1;for(const a of e.symbolLayers.items)if(\"fill\"===a.type&&a.enabled){const e=a.material,i=a.edges;if(o(e)&&!r){const n=e.color,i=K(e.colorMixMode);o(n)?t.material={color:[n.r/255,n.g/255,n.b/255],alpha:n.a,colorMixMode:i}:t.material={color:[1,1,1],alpha:1,colorMixMode:B.Multiply},t.castShadows=a.castShadows,r=!0}o(i)&&!n&&(t.edgeMaterial=G(i,{}),n=!0)}return t.material||(t.material={color:[1,1,1],alpha:1,colorMixMode:B.Multiply}),t}function Ne(e,t){return(0|e)+(0|t)|0}function ze(e,t,r,n,o=0){n===C(n)?t.isGeographic?Be(e,r,t,o):Ke(e,r,t,o):t.isWGS84&&(n.isWebMercator||D(n))?We(t,e,n,r,o):t.isWebMercator&&D(n)?Le(t,e,n,r,o):e===r?(r.center[2]+=o,j(r.center,t,0,r.center,n,0,1)):(g(r.center,e.center[0],e.center[1],e.center[2]+o),j(r.center,t,0,r.center,n,0,1),h(r.quaternion,e.quaternion),S(r.halfSize,e.halfSize))}function We(e,t,r,n,o){S(_e,t.center),_e[2]+=o;const a=C(r);j(_e,e,0,_e,a,0,1),Ge(a,t,_e,r,n)}const Ae=new Array(24),De=new Z(Ae,3,!0),Fe=I(),_e=I(),Oe=f();function Le(e,t,r,n,o){S(_e,t.center),_e[2]+=o,Ge(e,t,_e,r,n)}function Ge(e,t,r,n,o){const a=u(Oe,t.quaternion);for(let i=0;i<8;++i){for(let e=0;e<3;++e)Fe[e]=t.halfSize[e]*(0!=(i&1<<e)?-1:1);for(let e=0;e<3;++e){let t=r[e];for(let r=0;r<3;++r)t+=Fe[r]*a[3*r+e];Ae[3*i+e]=t}}j(Ae,e,0,Ae,n,0,8),$(De,o)}function Ke(e,t,r,n){Q(e,Xe),g(t.center,e.center[0],e.center[1],e.center[2]+n),q(r,t.center,Je,C(r)),g(t.center,Je[12],Je[13],Je[14]);const o=2*Math.sqrt(1+Je[0]+Je[5]+Je[10]);Ve[0]=(Je[6]-Je[9])/o,Ve[1]=(Je[8]-Je[2])/o,Ve[2]=(Je[1]-Je[4])/o,Ve[3]=.25*o,d(t.quaternion,Ve,e.quaternion),y(Ve,t.quaternion);let a=0,i=0,s=0;for(const l of Xe)l[2]+=n,j(l,r,0,l,C(r),0,1),w(rt,l,t.center),T(rt,rt,Ve),a=Math.max(a,Math.abs(rt[0])),i=Math.max(i,Math.abs(rt[1])),s=Math.max(s,Math.abs(rt[2]));g(t.halfSize,a,i,s)}function Be(e,t,r,n){const o=R(r),a=1+Math.max(0,n)/(o.radius+e.center[2]);g(t.center,e.center[0],e.center[1],e.center[2]+n),j(t.center,r,0,t.center,C(r),0,1),h(t.quaternion,e.quaternion),y(Ve,e.quaternion),g(rt,0,0,1),T(rt,rt,Ve),g(rt,e.halfSize[0]*Math.abs(rt[0]),e.halfSize[1]*Math.abs(rt[1]),e.halfSize[2]*Math.abs(rt[2])),E(rt,rt,o.inverseFlattening),v(t.halfSize,e.halfSize,rt),E(t.halfSize,t.halfSize,a)}function Pe(e,t,r,n,i,s){if(!s||0===s.length||a(t)||!e.mbs)return null;const l=O(e.mbs,i,r,t);p(ot,l);let c=null;const u=()=>{if(!c)if(c=Xe,z(et),o(e.serviceObb)){ze(e.serviceObb,r,tt,t,i),Q(tt,c);for(const e of c)M(e,e,ot),W(et,e)}else{const n=e.mbs;if(!n)return;const o=n[3];U(n,r,rt,t),M(rt,rt,ot),rt[2]+=i;for(let e=0;e<8;++e){const t=1&e?o:-o,r=2&e?o:-o,n=4&e?o:-o,a=c[e];S(a,[rt[0]+t,rt[1]+r,rt[2]+n]),W(et,a)}}};let f=1/0,m=-1/0;const h=e=>{if(\"replace\"!==e.type)return;const r=e.geometry;if(!r?.hasZ)return;z(Ye);const o=r.spatialReference||n,a=r.rings.reduce(((e,r)=>r.reduce(((e,r)=>(U(r,o,rt,t),M(rt,rt,ot),W(Ye,rt),Math.min(rt[2],e))),e)),1/0);u(),A(et,Ye)&&(f=Math.min(f,a),m=Math.max(m,a))};if(s.forEach((e=>h(e))),f===1/0)return null;const d=(e,t,r)=>{M(rt,r,l),e[t+0]=rt[0],e[t+1]=rt[1],e[t+2]=rt[2],t+=24,r[2]=f,M(rt,r,l),e[t+0]=rt[0],e[t+1]=rt[1],e[t+2]=rt[2],t+=24,r[2]=m,M(rt,r,l),e[t+0]=rt[0],e[t+1]=rt[1],e[t+2]=rt[2]};for(let o=0;o<8;++o)d(nt.data,3*o,c[o]);return $(nt)}function $e(e){return o(e)&&e.halfSize[0]>=0}function Qe(e){return e[3]>=0}function Ze(e){o(e)&&(e.halfSize[0]=-1)}function He(e){o(e)&&(e[3]=-1)}const Je=m(),Ve=b(),Xe=[[0,0,0],[0,0,0],[0,0,0],[0,0,0],[0,0,0],[0,0,0],[0,0,0],[0,0,0]],Ye=N(),et=N(),tt=P(),rt=I(),nt={data:new Array(72),size:3,exclusive:!0,stride:3},ot=m();export{re as MbsIntersectResult,ke as SymbolInfo,Ne as addWraparound,Re as checkPointCloudLayerCompatibleWithView,Ie as checkPointCloudLayerValid,ge as checkRecyclable,ve as checkSceneLayerCompatibleWithView,Ee as checkSceneLayerValid,be as checkSpatialReference,we as checkSpatialReferences,Pe as computeVisibilityObb,J as containsDraco,H as extractWkid,X as filterInPlace,oe as findFieldsCaseInsensitive,V as findIntersectingNodes,ye as getCacheKeySuffix,me as getCachedAttributeValue,te as getClipRect,he as getIndexCrs,Ce as getSymbolInfo,de as getVertexCrs,ne as intersectBoundingRectWithMbs,He as invalidateMbs,Ze as invalidateObb,Se as isSupportedLocalModeProjection,Qe as isValidMbs,$e as isValidObb,Y as objectIdFilter,ue as queryAttributesFromCachedAttributesId,qe as rendererNeedsTextures,ze as transformObb,Ue as transparentEdgeMaterial,ae as whenGraphicAttributes};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{id as t}from\"../../kernel.js\";import r from\"../../request.js\";import{isNone as e,isSome as a}from\"../../core/maybe.js\";import{throwIfAbortError as i}from\"../../core/promiseUtils.js\";import s from\"../FeatureLayer.js\";import n from\"../../portal/Portal.js\";import o from\"../../portal/PortalItem.js\";class l{constructor(t,r,e,a){this._parsedUrl=t,this._portalItem=r,this._apiKey=e,this.signal=a,this._rootDocument=null;const i=this._parsedUrl?.path.match(/^(.*)\\/SceneServer\\/layers\\/([\\d]*)\\/?$/i);i&&(this._urlParts={root:i[1],layerId:parseInt(i[2],10)})}async fetch(){if(!this._urlParts)return null;const t=this._portalItem??await this._portalItemFromServiceItemId();if(e(t))return this._loadFromUrl();const r=await this._findAndLoadRelatedPortalItem(t);return e(r)?null:this._loadFeatureLayerFromPortalItem(r)}async fetchPortalItem(){if(!this._urlParts)return null;const t=this._portalItem??await this._portalItemFromServiceItemId();return e(t)?null:this._findAndLoadRelatedPortalItem(t)}async _fetchRootDocument(){if(a(this._rootDocument))return this._rootDocument;if(e(this._urlParts))return this._rootDocument={},{};const t={query:{f:\"json\",token:this._apiKey},responseType:\"json\",signal:this.signal},i=`${this._urlParts.root}/SceneServer`;try{const e=await r(i,t);this._rootDocument=e.data}catch{this._rootDocument={}}return this._rootDocument}async _fetchServiceOwningPortalUrl(){const e=this._parsedUrl?.path,a=e?t?.findServerInfo(e):null;if(a?.owningSystemUrl)return a.owningSystemUrl;const s=e?e.replace(/(.*\\/rest)\\/.*/i,\"$1\")+\"/info\":null;try{const t=(await r(s,{query:{f:\"json\"},responseType:\"json\",signal:this.signal})).data.owningSystemUrl;if(t)return t}catch(n){i(n)}return null}async _findAndLoadRelatedPortalItem(t){try{return(await t.fetchRelatedItems({relationshipType:\"Service2Service\",direction:\"reverse\"},{signal:this.signal})).find((t=>\"Feature Service\"===t.type))||null}catch(r){return i(r),null}}async _loadFeatureLayerFromPortalItem(t){await t.load({signal:this.signal});const r=await this._findMatchingAssociatedSublayerUrl(t.url??\"\");return new s({url:r,portalItem:t}).load({signal:this.signal})}async _loadFromUrl(){const t=await this._findMatchingAssociatedSublayerUrl(`${this._urlParts?.root}/FeatureServer`);return new s({url:t}).load({signal:this.signal})}async _findMatchingAssociatedSublayerUrl(t){const e=t.replace(/^(.*FeatureServer)(\\/[\\d]*\\/?)?$/i,\"$1\"),a={query:{f:\"json\"},responseType:\"json\",authMode:\"no-prompt\",signal:this.signal},i=this._urlParts?.layerId,s=this._fetchRootDocument(),n=r(e,a),[o,l]=await Promise.all([n,s]),c=l&&l.layers,u=o.data&&o.data.layers;if(!Array.isArray(u))throw new Error(\"expected layers array\");if(Array.isArray(c))for(let r=0;r<Math.min(c.length,u.length);r++){if(c[r].id===i)return`${e}/${u[r].id}`}else if(null!=i&&i<u.length)return`${e}/${u[i].id}`;throw new Error(\"could not find matching associated sublayer\")}async _portalItemFromServiceItemId(){const t=(await this._fetchRootDocument()).serviceItemId;if(!t)return null;const r=new o({id:t,apiKey:this._apiKey}),e=await this._fetchServiceOwningPortalUrl();a(e)&&(r.portal=new n({url:e}));try{return r.load({signal:this.signal})}catch(s){return i(s),null}}}export{l as FetchAssociatedFeatureLayer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAMA,KAAE,EAAC,WAAU,EAAC,mBAAkB,MAAE,GAAE,YAAW,EAAC,qBAAoB,OAAG,kBAAiB,OAAG,kBAAiB,OAAG,cAAa,OAAG,cAAa,OAAG,mBAAkB,OAAG,gBAAe,MAAE,GAAE,MAAK,EAAC,aAAY,OAAG,oBAAmB,OAAG,WAAU,OAAG,WAAU,MAAE,GAAE,SAAQ,EAAC,2BAA0B,OAAG,wBAAuB,OAAG,wBAAuB,OAAG,kBAAiB,OAAG,oDAAmD,OAAG,2BAA0B,OAAG,2BAA0B,OAAG,wBAAuB,OAAG,wBAAuB,OAAG,0BAAyB,MAAE,GAAE,UAAS,EAAC,iCAAgC,MAAE,GAAE,YAAW,EAAC,mBAAkB,OAAG,kBAAiB,OAAG,qBAAoB,OAAG,aAAY,OAAG,gBAAe,OAAG,iBAAgB,OAAG,wBAAuB,OAAG,eAAc,OAAG,wBAAuB,OAAG,0BAAyB,OAAG,0BAAyB,OAAG,2BAA0B,OAAG,cAAa,OAAG,gBAAe,OAAG,gCAA+B,MAAE,GAAE,cAAa,EAAC,eAAc,OAAG,iBAAgB,OAAG,oBAAmB,OAAG,mBAAkB,MAAE,GAAE,kBAAiB,EAAC,mBAAkB,MAAE,GAAE,OAAM,EAAC,gBAAe,GAAE,sBAAqB,GAAE,wBAAuB,GAAE,mBAAkB,OAAG,kBAAiB,OAAG,yBAAwB,OAAG,iCAAgC,OAAG,wBAAuB,OAAG,qCAAoC,OAAG,kBAAiB,OAAG,kBAAiB,OAAG,gBAAe,OAAG,mBAAkB,OAAG,4BAA2B,OAAG,sBAAqB,OAAG,wBAAuB,OAAG,8BAA6B,OAAG,iBAAgB,OAAG,oBAAmB,OAAG,8BAA6B,OAAG,sBAAqB,OAAG,8BAA6B,OAAG,uBAAsB,OAAG,uBAAsB,OAAG,oBAAmB,OAAG,uBAAsB,OAAG,iCAAgC,OAAG,0BAAyB,OAAG,sCAAqC,OAAG,uCAAsC,EAAC,UAAS,OAAG,UAAS,OAAG,YAAW,MAAE,GAAE,oBAAmB,OAAG,oBAAmB,EAAC,EAAC;;;ACAzmD,IAAMC,KAAE;AAAR,IAAU,IAAE,IAAEA;;;ACAye,IAAMC,KAAEC,GAAE;AAAV,IAAY,IAAE,EAAE;AAAhB,IAAkB,IAAE,EAAE;AAAtB,IAAwBC,KAAEC,GAAE;AAA5B,IAA8B,IAAE,EAAE;AAA4O,SAAS,EAAEC,KAAE,CAAC,GAAE,GAAE,CAAC,GAAEC,KAAE,CAAC,IAAG,IAAG,EAAE,GAAEC,KAAEF,GAAE,GAAE;AAAC,SAAM,EAAC,QAAOC,GAAED,EAAC,GAAE,UAASC,GAAEA,EAAC,GAAE,YAAWE,GAAED,EAAC,EAAC;AAAC;AAA+2D,IAA+C,KAAG,MAAI;AAAC,QAAME,KAAE,IAAI,UAAU,GAAG;AAAE,MAAIC,KAAE;AAAE,QAAMC,KAAE,CAAAA,OAAG;AAAC,aAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAH,GAAEC,KAAEE,EAAC,IAAED,GAAEC,EAAC;AAAE,IAAAF,MAAG;AAAA,EAAC;AAAE,SAAOC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEF;AAAC,GAAG;AAA4+B,IAAM,KAAG,EAAE;AAAX,IAAa,KAAG,EAAE;AAAlB,IAAoB,KAAG,EAAE;AAAzB,IAA2B,KAAGI,GAAE;;;ACA3oE,IAAM,KAAGC,GAAE;AAAuW,IAAIC;AAA85E,SAAS,GAAGC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAM,IAAE,CAAC;AAAE,aAAUC,MAAKJ,GAAE,KAAGI,MAAGD,GAAE,SAASC,GAAE,IAAI,GAAE;AAAC,UAAMJ,KAAE,GAAGD,EAAC,UAAUE,EAAC,eAAeG,GAAE,GAAG;AAAK,MAAE,KAAK,EAAC,KAAIJ,IAAE,aAAYI,GAAC,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,EAAE,IAAK,CAAAL,OAAG,EAAEA,GAAE,KAAI,EAAC,cAAa,eAAc,CAAC,EAAE,KAAM,CAAAK,OAAG,EAAEL,GAAE,aAAYK,GAAE,IAAI,CAAE,CAAE,CAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,UAAML,KAAE,CAAC;AAAE,eAAUC,MAAKE,IAAE;AAAC,YAAMD,KAAE,CAAC;AAAE,eAAQF,KAAE,GAAEA,KAAEK,GAAE,QAAOL,MAAI;AAAC,cAAMG,KAAEE,GAAEL,EAAC,EAAE;AAAM,gBAAMG,OAAID,GAAE,EAAEF,EAAC,EAAE,YAAY,IAAI,IAAE,GAAGG,IAAEF,EAAC;AAAA,MAAE;AAAC,MAAAD,GAAE,KAAKE,EAAC;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC,CAAE;AAAC;AAAC,CAAC,SAASK,IAAE;AAAC,EAAAA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,4BAA0B,CAAC,IAAE,6BAA4BA,GAAEA,GAAE,2BAAyB,CAAC,IAAE,4BAA2BA,GAAEA,GAAE,SAAO,CAAC,IAAE;AAAQ,EAAEC,QAAKA,MAAG,CAAC,EAAE;AAAE,IAAMC,MAAG;AAAT,IAAgB,KAAG,EAAE,KAAG;AAAI,SAAS,GAAGF,IAAEL,IAAE;AAAC,MAAG,CAACK,GAAE,QAAO;AAAK,QAAMJ,KAAEI,GAAEL,EAAC;AAAE,MAAG,EAAEK,EAAC,EAAE,QAAOJ,OAAIM,MAAG,OAAKN;AAAE,MAAG,EAAEI,EAAC,EAAE,QAAOJ,OAAI,KAAG,OAAKA;AAAE,SAAOA,MAAGA,KAAE,OAAKA;AAAC;AAAigH,IAAM,KAAG,EAAE,EAAC,OAAM,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,SAAQ,EAAC,CAAC;AAAshC,IAAM,KAAG,IAAI,MAAM,EAAE;AAArB,IAAuB,KAAG,IAAI,EAAE,IAAG,GAAE,IAAE;AAAvC,IAAyC,KAAG,EAAE;AAA9C,IAAgD,KAAG,EAAE;AAArD,IAAuD,KAAG,EAAE;AAAyzE,IAAM,KAAGO,GAAE;AAAX,IAAa,KAAGA,GAAE;AAAlB,IAAyF,KAAGC,GAAE;AAA9F,IAAgG,KAAGA,GAAE;AAArG,IAAuG,KAAG,EAAE;AAA5G,IAA8G,KAAG,EAAE;AAAnH,IAAqH,KAAG,EAAC,MAAK,IAAI,MAAM,EAAE,GAAE,MAAK,GAAE,WAAU,MAAG,QAAO,EAAC;AAAxK,IAA0K,KAAGC,GAAE;;;ACAp0Z,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAE;AAJ3U;AAI4U,SAAK,aAAWH,IAAE,KAAK,cAAYC,IAAE,KAAK,UAAQC,IAAE,KAAK,SAAOC,IAAE,KAAK,gBAAc;AAAK,UAAM,KAAE,UAAK,eAAL,mBAAiB,KAAK,MAAM;AAA4C,UAAI,KAAK,YAAU,EAAC,MAAK,EAAE,CAAC,GAAE,SAAQ,SAAS,EAAE,CAAC,GAAE,EAAE,EAAC;AAAA,EAAE;AAAA,EAAC,MAAM,QAAO;AAAC,QAAG,CAAC,KAAK,UAAU,QAAO;AAAK,UAAMH,KAAE,KAAK,eAAa,MAAM,KAAK,6BAA6B;AAAE,QAAG,EAAEA,EAAC,EAAE,QAAO,KAAK,aAAa;AAAE,UAAMC,KAAE,MAAM,KAAK,8BAA8BD,EAAC;AAAE,WAAO,EAAEC,EAAC,IAAE,OAAK,KAAK,gCAAgCA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAiB;AAAC,QAAG,CAAC,KAAK,UAAU,QAAO;AAAK,UAAMD,KAAE,KAAK,eAAa,MAAM,KAAK,6BAA6B;AAAE,WAAO,EAAEA,EAAC,IAAE,OAAK,KAAK,8BAA8BA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAoB;AAAC,QAAG,EAAE,KAAK,aAAa,EAAE,QAAO,KAAK;AAAc,QAAG,EAAE,KAAK,SAAS,EAAE,QAAO,KAAK,gBAAc,CAAC,GAAE,CAAC;AAAE,UAAMA,KAAE,EAAC,OAAM,EAAC,GAAE,QAAO,OAAM,KAAK,QAAO,GAAE,cAAa,QAAO,QAAO,KAAK,OAAM,GAAE,IAAE,GAAG,KAAK,UAAU,IAAI;AAAe,QAAG;AAAC,YAAME,KAAE,MAAM,EAAE,GAAEF,EAAC;AAAE,WAAK,gBAAcE,GAAE;AAAA,IAAI,QAAM;AAAC,WAAK,gBAAc,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,MAAM,+BAA8B;AAJ/2C;AAIg3C,UAAMA,MAAE,UAAK,eAAL,mBAAiB,MAAKC,KAAED,MAAE,KAAAD,OAAA,mBAAG,eAAeC,MAAG;AAAK,QAAGC,MAAA,gBAAAA,GAAG,gBAAgB,QAAOA,GAAE;AAAgB,UAAMC,KAAEF,KAAEA,GAAE,QAAQ,mBAAkB,IAAI,IAAE,UAAQ;AAAK,QAAG;AAAC,YAAMF,MAAG,MAAM,EAAEI,IAAE,EAAC,OAAM,EAAC,GAAE,OAAM,GAAE,cAAa,QAAO,QAAO,KAAK,OAAM,CAAC,GAAG,KAAK;AAAgB,UAAGJ,GAAE,QAAOA;AAAA,IAAC,SAAOK,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,MAAM,8BAA8BL,IAAE;AAAC,QAAG;AAAC,cAAO,MAAMA,GAAE,kBAAkB,EAAC,kBAAiB,mBAAkB,WAAU,UAAS,GAAE,EAAC,QAAO,KAAK,OAAM,CAAC,GAAG,KAAM,CAAAA,OAAG,sBAAoBA,GAAE,IAAK,KAAG;AAAA,IAAI,SAAOC,IAAE;AAAC,aAAO,EAAEA,EAAC,GAAE;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,gCAAgCD,IAAE;AAAC,UAAMA,GAAE,KAAK,EAAC,QAAO,KAAK,OAAM,CAAC;AAAE,UAAMC,KAAE,MAAM,KAAK,mCAAmCD,GAAE,OAAK,EAAE;AAAE,WAAO,IAAI,GAAE,EAAC,KAAIC,IAAE,YAAWD,GAAC,CAAC,EAAE,KAAK,EAAC,QAAO,KAAK,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAc;AAJtmE;AAIumE,UAAMA,KAAE,MAAM,KAAK,mCAAmC,IAAG,UAAK,cAAL,mBAAgB,IAAI,gBAAgB;AAAE,WAAO,IAAI,GAAE,EAAC,KAAIA,GAAC,CAAC,EAAE,KAAK,EAAC,QAAO,KAAK,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,mCAAmCA,IAAE;AAJlyE;AAImyE,UAAME,KAAEF,GAAE,QAAQ,qCAAoC,IAAI,GAAEG,KAAE,EAAC,OAAM,EAAC,GAAE,OAAM,GAAE,cAAa,QAAO,UAAS,aAAY,QAAO,KAAK,OAAM,GAAE,KAAE,UAAK,cAAL,mBAAgB,SAAQC,KAAE,KAAK,mBAAmB,GAAEC,KAAE,EAAEH,IAAEC,EAAC,GAAE,CAACG,IAAEP,EAAC,IAAE,MAAM,QAAQ,IAAI,CAACM,IAAED,EAAC,CAAC,GAAEG,KAAER,MAAGA,GAAE,QAAOS,KAAEF,GAAE,QAAMA,GAAE,KAAK;AAAO,QAAG,CAAC,MAAM,QAAQE,EAAC,EAAE,OAAM,IAAI,MAAM,uBAAuB;AAAE,QAAG,MAAM,QAAQD,EAAC,EAAE,UAAQN,KAAE,GAAEA,KAAE,KAAK,IAAIM,GAAE,QAAOC,GAAE,MAAM,GAAEP,MAAI;AAAC,UAAGM,GAAEN,EAAC,EAAE,OAAK,EAAE,QAAM,GAAGC,EAAC,IAAIM,GAAEP,EAAC,EAAE,EAAE;AAAA,IAAE;AAAA,aAAS,QAAM,KAAG,IAAEO,GAAE,OAAO,QAAM,GAAGN,EAAC,IAAIM,GAAE,CAAC,EAAE,EAAE;AAAG,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAAC;AAAA,EAAC,MAAM,+BAA8B;AAAC,UAAMR,MAAG,MAAM,KAAK,mBAAmB,GAAG;AAAc,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMC,KAAE,IAAI,EAAE,EAAC,IAAGD,IAAE,QAAO,KAAK,QAAO,CAAC,GAAEE,KAAE,MAAM,KAAK,6BAA6B;AAAE,MAAEA,EAAC,MAAID,GAAE,SAAO,IAAI,EAAE,EAAC,KAAIC,GAAC,CAAC;AAAG,QAAG;AAAC,aAAOD,GAAE,KAAK,EAAC,QAAO,KAAK,OAAM,CAAC;AAAA,IAAC,SAAOG,IAAE;AAAC,aAAO,EAAEA,EAAC,GAAE;AAAA,IAAI;AAAA,EAAC;AAAC;", "names": ["t", "s", "E", "e", "J", "n", "e", "t", "a", "r", "e", "t", "a", "n", "e", "u", "re", "t", "r", "n", "o", "a", "e", "re", "fe", "e", "u", "e", "l", "t", "r", "e", "a", "s", "n", "o", "c", "u"]}