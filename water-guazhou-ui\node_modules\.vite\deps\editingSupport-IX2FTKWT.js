import {
  E
} from "./chunk-4AKIERTZ.js";
import {
  n
} from "./chunk-PCLDCFRI.js";
import {
  v
} from "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import {
  d,
  o
} from "./chunk-O4T45CJC.js";
import {
  v as v2,
  w
} from "./chunk-D7S3BWBP.js";
import {
  g
} from "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import {
  ot
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  p
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/infoFor3D.js
var _;
!function(_2) {
  _2.GLTF_BINARY = "3D_glb", _2.GLTF_JSON = "3D_gltf";
}(_ || (_ = {}));

// node_modules/@arcgis/core/layers/graphics/editingSupport.js
function y(e) {
  return e && null != e.applyEdits;
}
async function f(e, t2, a, s2 = {}) {
  var _a;
  let i, o2;
  const n2 = { edits: a, result: new Promise((e2, t3) => {
    i = e2, o2 = t3;
  }) };
  e.emit("apply-edits", n2);
  try {
    const { results: o3, edits: n3 } = await g2(e, t2, a, s2), d2 = (e2) => e2.filter((e3) => !e3.error).map(p), l = { edits: n3, addedFeatures: d2(o3.addFeatureResults), updatedFeatures: d2(o3.updateFeatureResults), deletedFeatures: d2(o3.deleteFeatureResults), addedAttachments: d2(o3.addAttachmentResults), updatedAttachments: d2(o3.updateAttachmentResults), deletedAttachments: d2(o3.deleteAttachmentResults), exceededTransferLimit: false };
    return ((_a = o3.editedFeatureResults) == null ? void 0 : _a.length) && (l.editedFeatures = o3.editedFeatureResults), (l.addedFeatures.length || l.updatedFeatures.length || l.deletedFeatures.length || l.addedAttachments.length || l.updatedAttachments.length || l.deletedAttachments.length) && (e.emit("edits", l), o(e) && d.emit("edits", { layer: e, event: l })), i(l), o3;
  } catch (d2) {
    throw o2(d2), d2;
  }
}
async function g2(e, t2, r2, s2) {
  var _a, _b, _c, _d, _e, _f;
  if (await e.load(), !y(t2)) throw new s(`${e.type}-layer:no-editing-support`, "Layer source does not support applyEdits capability", { layer: e });
  if (!w(e)) throw new s(`${e.type}-layer:editing-disabled`, "Editing is disabled for layer", { layer: e });
  const { edits: i, options: o2 } = await b(e, r2, s2);
  return ((_a = i.addFeatures) == null ? void 0 : _a.length) || ((_b = i.updateFeatures) == null ? void 0 : _b.length) || ((_c = i.deleteFeatures) == null ? void 0 : _c.length) || ((_d = i.addAttachments) == null ? void 0 : _d.length) || ((_e = i.updateAttachments) == null ? void 0 : _e.length) || ((_f = i.deleteAttachments) == null ? void 0 : _f.length) ? { edits: i, results: await t2.applyEdits(i, o2) } : { edits: i, results: { addFeatureResults: [], updateFeatureResults: [], deleteFeatureResults: [], addAttachmentResults: [], updateAttachmentResults: [], deleteAttachmentResults: [] } };
}
async function b(e, r2, i) {
  const o2 = r2 && (r2.addFeatures || r2.updateFeatures || r2.deleteFeatures), n2 = r2 && (r2.addAttachments || r2.updateAttachments || r2.deleteAttachments), d2 = r(e.infoFor3D);
  if (!r2 || !o2 && !n2) throw new s(`${e.type}-layer:missing-parameters`, "'addFeatures', 'updateFeatures', 'deleteFeatures', 'addAttachments', 'updateAttachments' or 'deleteAttachments' parameter is required");
  const l = v2(e);
  if (!l.data.isVersioned && (i == null ? void 0 : i.gdbVersion)) throw new s(`${e.type}-layer:invalid-parameter`, "'gdbVersion' is applicable only if the layer supports versioned data. See: 'capabilities.data.isVersioned'");
  if (!l.editing.supportsRollbackOnFailure && (i == null ? void 0 : i.rollbackOnFailureEnabled)) throw new s(`${e.type}-layer:invalid-parameter`, "This layer does not support 'rollbackOnFailureEnabled' parameter. See: 'capabilities.editing.supportsRollbackOnFailure'");
  if (!l.editing.supportsGlobalId && (i == null ? void 0 : i.globalIdUsed)) throw new s(`${e.type}-layer:invalid-parameter`, "This layer does not support 'globalIdUsed' parameter. See: 'capabilities.editing.supportsGlobalId'");
  if (!l.editing.supportsGlobalId && n2) throw new s(`${e.type}-layer:invalid-parameter`, "'addAttachments', 'updateAttachments' and 'deleteAttachments' are applicable only if the layer supports global ids. See: 'capabilities.editing.supportsGlobalId'");
  if (!(i == null ? void 0 : i.globalIdUsed) && n2) throw new s(`${e.type}-layer:invalid-parameter`, "When 'addAttachments', 'updateAttachments' or 'deleteAttachments' is specified, globalIdUsed should be set to true");
  const u = { ...i };
  if (null != u.rollbackOnFailureEnabled || l.editing.supportsRollbackOnFailure || (u.rollbackOnFailureEnabled = true), false === u.rollbackOnFailureEnabled && "original-and-current-features" === u.returnServiceEditsOption) throw new s(`${e.type}-layer:invalid-parameter`, "'original-and-current-features' is valid for 'returnServiceEditsOption' only when 'rollBackOnFailure' is true.");
  if (!l.editing.supportsReturnServiceEditsInSourceSpatialReference && u.returnServiceEditsInSourceSR) throw new s(`${e.type}-layer:invalid-parameter`, "This layer does not support 'returnServiceEditsInSourceSR' parameter. See: 'capabilities.editing.supportsReturnServiceEditsInSourceSpatialReference'");
  if (u.returnServiceEditsInSourceSR && "original-and-current-features" !== u.returnServiceEditsOption) throw new s(`${e.type}-layer:invalid-parameter`, "'returnServiceEditsInSourceSR' is valid only when 'returnServiceEditsOption' is set to 'original-and-current-features'");
  const p2 = { ...r2 };
  if (p2.addFeatures = r2 && j.isCollection(r2.addFeatures) ? r2.addFeatures.toArray() : p2.addFeatures || [], p2.updateFeatures = r2 && j.isCollection(r2.updateFeatures) ? r2.updateFeatures.toArray() : p2.updateFeatures || [], p2.deleteFeatures = r2 && j.isCollection(r2.deleteFeatures) ? r2.deleteFeatures.toArray() : p2.deleteFeatures || [], p2.addFeatures.length && !l.operations.supportsAdd) throw new s(`${e.type}-layer:unsupported-operation`, "Layer does not support adding features.");
  if (p2.updateFeatures.length && !l.operations.supportsUpdate) throw new s(`${e.type}-layer:unsupported-operation`, "Layer does not support updating features.");
  if (p2.deleteFeatures.length && !l.operations.supportsDelete) throw new s(`${e.type}-layer:unsupported-operation`, "Layer does not support deleting features.");
  p2.addAttachments = p2.addAttachments || [], p2.updateAttachments = p2.updateAttachments || [], p2.deleteAttachments = p2.deleteAttachments || [], p2.addFeatures = p2.addFeatures.map(S), p2.updateFeatures = p2.updateFeatures.map(S), p2.addAssets = [];
  const c = (i == null ? void 0 : i.globalIdUsed) || d2;
  p2.addFeatures.forEach((t2) => F(t2, e, c)), p2.updateFeatures.forEach((t2) => I(t2, e, c)), p2.deleteFeatures.forEach((t2) => A(t2, e, c)), p2.addAttachments.forEach((t2) => v3(t2, e)), p2.updateAttachments.forEach((t2) => v3(t2, e)), d2 && await R(p2, e);
  return { edits: await E2(p2), options: u };
}
function w2(e, t2, r2) {
  var _a, _b;
  if (r2) {
    if ("attributes" in e && !e.attributes[t2.globalIdField]) throw new s(`${t2.type}-layer:invalid-parameter`, "Feature should have 'globalId' when 'globalIdUsed' is true");
    if (!("attributes" in e) && !e.globalId) throw new s(`${t2.type}-layer:invalid-parameter`, "'globalId' of the feature should be passed when 'globalIdUsed' is true");
  }
  if ("geometry" in e && r(e.geometry)) {
    if (e.geometry.hasZ && false === ((_a = t2.capabilities) == null ? void 0 : _a.data.supportsZ)) throw new s(`${t2.type}-layer:z-unsupported`, "Layer does not support z values while feature has z values.");
    if (e.geometry.hasM && false === ((_b = t2.capabilities) == null ? void 0 : _b.data.supportsM)) throw new s(`${t2.type}-layer:m-unsupported`, "Layer does not support m values while feature has m values.");
  }
}
function F(e, t2, a) {
  w2(e, t2, a);
}
function A(e, t2, a) {
  w2(e, t2, a);
}
function I(e, t2, r2) {
  w2(e, t2, r2);
  const i = v2(t2);
  if ("geometry" in e && r(e.geometry) && !(i == null ? void 0 : i.editing.supportsGeometryUpdate)) throw new s(`${t2.type}-layer:unsupported-operation`, "Layer does not support geometry updates.");
}
function v3(e, t2) {
  var _a;
  const { feature: r2, attachment: s2 } = e;
  if (!r2 || "attributes" in r2 && !r2.attributes[t2.globalIdField]) throw new s(`${t2.type}-layer:invalid-parameter`, "Attachment should have reference to a feature with 'globalId'");
  if (!("attributes" in r2) && !r2.globalId) throw new s(`${t2.type}-layer:invalid-parameter`, "Attachment should have reference to 'globalId' of the parent feature");
  if (!s2.globalId) throw new s(`${t2.type}-layer:invalid-parameter`, "Attachment should have 'globalId'");
  if (!s2.data && !s2.uploadId) throw new s(`${t2.type}-layer:invalid-parameter`, "Attachment should have 'data' or 'uploadId'");
  if (!(s2.data instanceof File && !!s2.data.name) && !s2.name) throw new s(`${t2.type}-layer:invalid-parameter`, "'name' is required when attachment is specified as Base64 encoded string using 'data'");
  if (!((_a = t2.capabilities) == null ? void 0 : _a.editing.supportsUploadWithItemId) && s2.uploadId) throw new s(`${t2.type}-layer:invalid-parameter`, "This layer does not support 'uploadId' parameter. See: 'capabilities.editing.supportsUploadWithItemId'");
  if ("string" == typeof s2.data) {
    const e2 = ot(s2.data);
    if (e2 && !e2.isBase64) throw new s(`${t2.type}-layer:invalid-parameter`, "Attachment 'data' should be a Blob, File or Base64 encoded string");
  }
}
async function E2(e) {
  const t2 = e.addFeatures ?? [], a = e.updateFeatures ?? [], r2 = t2.concat(a).map((e2) => e2.geometry), s2 = await v(r2), i = t2.length, o2 = a.length;
  return s2.slice(0, i).forEach((e2, a2) => t2[a2].geometry = e2), s2.slice(i, i + o2).forEach((e2, t3) => a[t3].geometry = e2), e;
}
function S(t2) {
  const a = new g();
  return t2.attributes || (t2.attributes = {}), a.geometry = t2.geometry, a.attributes = t2.attributes, a;
}
async function R(e, t2) {
  if (t(t2.infoFor3D)) return;
  const { infoFor3D: a } = t2;
  let r2 = false;
  for (const i of a.editFormats) if (i === _.GLTF_BINARY) {
    r2 = true;
    break;
  }
  const s2 = [];
  for (const i of e.addFeatures ?? []) s2.push($(i, e, t2, r2));
  for (const i of e.updateFeatures ?? []) s2.push($(i, e, t2, r2));
  const o2 = await Promise.allSettled(s2);
  for (const i of o2) if ("rejected" === i.status) throw i.reason;
}
async function $(e, t2, r2, o2) {
  var _a;
  if (t(e.geometry) || "mesh" !== e.geometry.type) return;
  const d2 = e.geometry, u = r2.globalIdField;
  if (r(r2.parsedUrl) && r(d2.external) && Array.isArray(d2.external.source) && 1 === d2.external.source.length && "source" in d2.external.source[0] && "string" == typeof d2.external.source[0].source && d2.external.source[0].source.startsWith(r2.parsedUrl.path)) return;
  if (!o2) throw new s(`${r2.type}-layer:binary-gltf-asset-not-supported`, "3DObjectFeatureLayer requires binary glTF (.glb) support for updating mesh geometry.");
  const p2 = await d2.toBinaryGLTF({ ignoreLocalTransform: true }), h = await p2.buffer(), m = `{${n()}}`, y2 = `${m}.glb`;
  (_a = t2.addAssets) == null ? void 0 : _a.push({ featureGlobalId: e.getAttribute(u), assetMapGlobalId: m, assetName: y2, flags: r(d2.transform) && d2.transform.geographic ? E.PROJECT_VERTICES : 0, data: h.data, mimeType: h.type, assetType: _.GLTF_BINARY, feature: e });
}
export {
  f as applyEdits
};
//# sourceMappingURL=editingSupport-IX2FTKWT.js.map
