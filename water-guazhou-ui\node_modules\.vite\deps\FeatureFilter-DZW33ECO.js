import {
  p
} from "./chunk-G6YO5IYF.js";
import "./chunk-CV76WXPW.js";
import "./chunk-EVADT7ME.js";
import "./chunk-T3GGN2P7.js";
import {
  n,
  v
} from "./chunk-QYYMKGDW.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-6OFWBRK2.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-FRO3RSRO.js";
import {
  J
} from "./chunk-FUIIMETN.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import "./chunk-2WMCP27R.js";
import "./chunk-UCWK623G.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import {
  x
} from "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import {
  u
} from "./chunk-I7WHRVHF.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import {
  c
} from "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/features/support/whereUtils.js
var t2 = s.getLogger("esri.views.2d.layers.features.support.whereUtils");
var a = { getAttribute: (e, r) => e.field(r) };
async function s3(r, s4) {
  const n2 = await import("./WhereClause-3PEEQH24.js");
  try {
    const o = n2.WhereClause.create(r, s4);
    if (!o.isStandardized) {
      const r2 = new s2("mapview - bad input", "Unable to apply filter's definition expression, as expression is not standardized.", o);
      t2.error(r2);
    }
    return (e) => {
      const r2 = e.readArcadeFeature();
      return o.testFeature(r2, a);
    };
  } catch (o) {
    return t2.warn("mapview-bad-where-clause", "Encountered an error when evaluating where clause", r), (e) => true;
  }
}

// node_modules/@arcgis/core/views/2d/layers/features/support/FeatureFilter.js
var m = 1;
var _ = 2;
var p2 = class {
  constructor(t3) {
    this._geometryBounds = u(), this._idToVisibility = /* @__PURE__ */ new Map(), this._serviceInfo = t3;
  }
  get hash() {
    return this._hash;
  }
  check(t3) {
    return this._applyFilter(t3);
  }
  clear() {
    const t3 = this._resetAllHiddenIds();
    return this.update(), { show: t3, hide: [] };
  }
  invalidate() {
    this._idToVisibility.forEach((t3, e) => {
      this._idToVisibility.set(e, 0);
    });
  }
  setKnownIds(t3) {
    for (const e of t3) this._idToVisibility.set(e, m);
  }
  setTrue(t3) {
    const e = [], i = [], s4 = new Set(t3);
    return this._idToVisibility.forEach((t4, r) => {
      const o = !!(this._idToVisibility.get(r) & m), h = s4.has(r);
      !o && h ? e.push(r) : o && !h && i.push(r), this._idToVisibility.set(r, h ? m | _ : 0);
    }), { show: e, hide: i };
  }
  createQuery() {
    const { geometry: t3, spatialRel: e, where: i, timeExtent: s4, objectIds: r } = this;
    return x.fromJSON({ geometry: t3, spatialRel: e, where: i, timeExtent: s4, objectIds: r });
  }
  async update(t3, e) {
    this._hash = JSON.stringify(t3);
    const i = await J(t3, null, e);
    await Promise.all([this._setGeometryFilter(i), this._setIdFilter(i), this._setAttributeFilter(i), this._setTimeFilter(i)]);
  }
  async _setAttributeFilter(t3) {
    if (!t3 || !t3.where) return this._clause = null, void (this.where = null);
    this._clause = await s3(t3.where, this._serviceInfo.fieldsIndex), this.where = t3.where;
  }
  _setIdFilter(t3) {
    this._idsToShow = t3 && t3.objectIds && new Set(t3.objectIds), this._idsToHide = t3 && t3.hiddenIds && new Set(t3.hiddenIds), this.objectIds = t3 && t3.objectIds;
  }
  async _setGeometryFilter(t3) {
    if (!t3 || !t3.geometry) return this._spatialQueryOperator = null, this.geometry = null, void (this.spatialRel = null);
    const e = t3.geometry, i = t3.spatialRel || "esriSpatialRelIntersects", s4 = await v(i, e, this._serviceInfo.geometryType, this._serviceInfo.hasZ, this._serviceInfo.hasM);
    c(this._geometryBounds, e), this._spatialQueryOperator = s4, this.geometry = e, this.spatialRel = i;
  }
  _setTimeFilter(i) {
    if (this.timeExtent = this._timeOperator = null, i && i.timeExtent) if (this._serviceInfo.timeInfo) this.timeExtent = i.timeExtent, this._timeOperator = n(this._serviceInfo.timeInfo, i.timeExtent, p);
    else {
      const s4 = new s2("feature-layer-view:time-filter-not-available", "Unable to apply time filter, as layer doesn't have time metadata.", i.timeExtent);
      s.getLogger("esri.views.2d.layers.features.controllers.FeatureFilter").error(s4);
    }
  }
  _applyFilter(t3) {
    return this._filterByGeometry(t3) && this._filterById(t3) && this._filterByTime(t3) && this._filterByExpression(t3);
  }
  _filterByExpression(t3) {
    return !this.where || this._clause(t3);
  }
  _filterById(t3) {
    return (!this._idsToHide || !this._idsToHide.size || !this._idsToHide.has(t3.getObjectId())) && (!this._idsToShow || !this._idsToShow.size || this._idsToShow.has(t3.getObjectId()));
  }
  _filterByGeometry(t3) {
    if (!this.geometry) return true;
    const e = t3.readHydratedGeometry();
    return !!e && this._spatialQueryOperator(e);
  }
  _filterByTime(t3) {
    return !!t(this._timeOperator) || this._timeOperator(t3);
  }
  _resetAllHiddenIds() {
    const t3 = [];
    return this._idToVisibility.forEach((e, i) => {
      e & m || (this._idToVisibility.set(i, m), t3.push(i));
    }), t3;
  }
};
export {
  p2 as default
};
//# sourceMappingURL=FeatureFilter-DZW33ECO.js.map
