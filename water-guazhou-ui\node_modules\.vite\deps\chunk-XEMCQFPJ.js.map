{"version": 3, "sources": ["../../@arcgis/core/layers/support/ExportImageParameters.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import r from\"../../core/Accessor.js\";import{HandleOwnerMixin as s}from\"../../core/HandleOwner.js\";import{isSome as t}from\"../../core/maybe.js\";import{sqlAnd as i}from\"../../core/sql.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as a}from\"../../core/accessorSupport/decorators/subclass.js\";import{combinedViewLayerTimeExtentProperty as l}from\"./commonProperties.js\";import{getLayerFloorFilterClause as n}from\"./floorFilterUtils.js\";import{isExportDynamic as y}from\"./sublayerUtils.js\";const m={visible:\"visibleSublayers\",definitionExpression:\"layerDefs\",labelingInfo:\"hasDynamicLayers\",labelsVisible:\"hasDynamicLayers\",opacity:\"hasDynamicLayers\",minScale:\"visibleSublayers\",maxScale:\"visibleSublayers\",renderer:\"hasDynamicLayers\",source:\"hasDynamicLayers\"};let c=class extends(s(r)){constructor(e){super(e),this.floors=null,this.scale=0}destroy(){this.layer=null}get dynamicLayers(){if(!this.hasDynamicLayers)return null;const e=this.visibleSublayers.map((e=>{const r=n(this.floors,e);return e.toExportImageJSON(r)}));return e.length?JSON.stringify(e):null}get hasDynamicLayers(){return this.layer&&y(this.visibleSublayers,this.layer.serviceSublayers,this.layer.gdbVersion)}set layer(e){this._get(\"layer\")!==e&&(this._set(\"layer\",e),this.handles.remove(\"layer\"),e&&this.handles.add([e.allSublayers.on(\"change\",(()=>this.notifyChange(\"visibleSublayers\"))),e.on(\"sublayer-update\",(e=>this.notifyChange(m[e.propertyName])))],\"layer\"))}get layers(){const e=this.visibleSublayers;return e?e.length?\"show:\"+e.map((e=>e.id)).join(\",\"):\"show:-1\":null}get layerDefs(){const e=!!this.floors?.length,r=this.visibleSublayers.filter((r=>null!=r.definitionExpression||e&&null!=r.floorInfo));return r.length?JSON.stringify(r.reduce(((e,r)=>{const s=n(this.floors,r),o=i(s,r.definitionExpression);return t(o)&&(e[r.id]=o),e}),{})):null}get version(){this.commitProperty(\"layers\"),this.commitProperty(\"layerDefs\"),this.commitProperty(\"dynamicLayers\"),this.commitProperty(\"timeExtent\");const e=this.layer;return e&&(e.commitProperty(\"dpi\"),e.commitProperty(\"imageFormat\"),e.commitProperty(\"imageTransparency\"),e.commitProperty(\"gdbVersion\")),(this._get(\"version\")||0)+1}get visibleSublayers(){const e=[];if(!this.layer)return e;const r=this.layer.sublayers,s=r=>{const t=this.scale,i=0===t,o=0===r.minScale||t<=r.minScale,a=0===r.maxScale||t>=r.maxScale;r.visible&&(i||o&&a)&&(r.sublayers?r.sublayers.forEach(s):e.unshift(r))};r&&r.forEach(s);const t=this._get(\"visibleSublayers\");return!t||t.length!==e.length||t.some(((r,s)=>e[s]!==r))?e:t}toJSON(){const e=this.layer;let r={dpi:e.dpi,format:e.imageFormat,transparent:e.imageTransparency,gdbVersion:e.gdbVersion||null};return this.hasDynamicLayers&&this.dynamicLayers?r.dynamicLayers=this.dynamicLayers:r={...r,layers:this.layers,layerDefs:this.layerDefs},r}};e([o({readOnly:!0})],c.prototype,\"dynamicLayers\",null),e([o()],c.prototype,\"floors\",void 0),e([o({readOnly:!0})],c.prototype,\"hasDynamicLayers\",null),e([o()],c.prototype,\"layer\",null),e([o({readOnly:!0})],c.prototype,\"layers\",null),e([o({readOnly:!0})],c.prototype,\"layerDefs\",null),e([o({type:Number})],c.prototype,\"scale\",void 0),e([o(l)],c.prototype,\"timeExtent\",void 0),e([o({readOnly:!0})],c.prototype,\"version\",null),e([o({readOnly:!0})],c.prototype,\"visibleSublayers\",null),c=e([a(\"esri.layers.mixins.ExportImageParameters\")],c);export{c as ExportImageParameters};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIwpB,IAAM,IAAE,EAAC,SAAQ,oBAAmB,sBAAqB,aAAY,cAAa,oBAAmB,eAAc,oBAAmB,SAAQ,oBAAmB,UAAS,oBAAmB,UAAS,oBAAmB,UAAS,oBAAmB,QAAO,mBAAkB;AAAE,IAAI,IAAE,cAAcA,GAAE,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,SAAO,MAAK,KAAK,QAAM;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,QAAM;AAAA,EAAI;AAAA,EAAC,IAAI,gBAAe;AAAC,QAAG,CAAC,KAAK,iBAAiB,QAAO;AAAK,UAAMA,KAAE,KAAK,iBAAiB,IAAK,CAAAA,OAAG;AAAC,YAAMC,KAAE,EAAE,KAAK,QAAOD,EAAC;AAAE,aAAOA,GAAE,kBAAkBC,EAAC;AAAA,IAAC,CAAE;AAAE,WAAOD,GAAE,SAAO,KAAK,UAAUA,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,SAAO,EAAE,KAAK,kBAAiB,KAAK,MAAM,kBAAiB,KAAK,MAAM,UAAU;AAAA,EAAC;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,KAAK,OAAO,MAAIA,OAAI,KAAK,KAAK,SAAQA,EAAC,GAAE,KAAK,QAAQ,OAAO,OAAO,GAAEA,MAAG,KAAK,QAAQ,IAAI,CAACA,GAAE,aAAa,GAAG,UAAU,MAAI,KAAK,aAAa,kBAAkB,CAAE,GAAEA,GAAE,GAAG,mBAAmB,CAAAA,OAAG,KAAK,aAAa,EAAEA,GAAE,YAAY,CAAC,CAAE,CAAC,GAAE,OAAO;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,UAAMA,KAAE,KAAK;AAAiB,WAAOA,KAAEA,GAAE,SAAO,UAAQA,GAAE,IAAK,CAAAA,OAAGA,GAAE,EAAG,EAAE,KAAK,GAAG,IAAE,YAAU;AAAA,EAAI;AAAA,EAAC,IAAI,YAAW;AAJzsD;AAI0sD,UAAMA,KAAE,CAAC,GAAC,UAAK,WAAL,mBAAa,SAAOC,KAAE,KAAK,iBAAiB,OAAQ,CAAAA,OAAG,QAAMA,GAAE,wBAAsBD,MAAG,QAAMC,GAAE,SAAU;AAAE,WAAOA,GAAE,SAAO,KAAK,UAAUA,GAAE,OAAQ,CAACD,IAAEC,OAAI;AAAC,YAAM,IAAE,EAAE,KAAK,QAAOA,EAAC,GAAE,IAAE,EAAE,GAAEA,GAAE,oBAAoB;AAAE,aAAO,EAAE,CAAC,MAAID,GAAEC,GAAE,EAAE,IAAE,IAAGD;AAAA,IAAC,GAAG,CAAC,CAAC,CAAC,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,UAAS;AAAC,SAAK,eAAe,QAAQ,GAAE,KAAK,eAAe,WAAW,GAAE,KAAK,eAAe,eAAe,GAAE,KAAK,eAAe,YAAY;AAAE,UAAMA,KAAE,KAAK;AAAM,WAAOA,OAAIA,GAAE,eAAe,KAAK,GAAEA,GAAE,eAAe,aAAa,GAAEA,GAAE,eAAe,mBAAmB,GAAEA,GAAE,eAAe,YAAY,KAAI,KAAK,KAAK,SAAS,KAAG,KAAG;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,UAAMA,KAAE,CAAC;AAAE,QAAG,CAAC,KAAK,MAAM,QAAOA;AAAE,UAAMC,KAAE,KAAK,MAAM,WAAU,IAAE,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,OAAMC,KAAE,MAAID,IAAE,IAAE,MAAID,GAAE,YAAUC,MAAGD,GAAE,UAASF,KAAE,MAAIE,GAAE,YAAUC,MAAGD,GAAE;AAAS,MAAAA,GAAE,YAAUE,MAAG,KAAGJ,QAAKE,GAAE,YAAUA,GAAE,UAAU,QAAQ,CAAC,IAAED,GAAE,QAAQC,EAAC;AAAA,IAAE;AAAE,IAAAA,MAAGA,GAAE,QAAQ,CAAC;AAAE,UAAMC,KAAE,KAAK,KAAK,kBAAkB;AAAE,WAAM,CAACA,MAAGA,GAAE,WAASF,GAAE,UAAQE,GAAE,KAAM,CAACD,IAAEG,OAAIJ,GAAEI,EAAC,MAAIH,EAAE,IAAED,KAAEE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMF,KAAE,KAAK;AAAM,QAAIC,KAAE,EAAC,KAAID,GAAE,KAAI,QAAOA,GAAE,aAAY,aAAYA,GAAE,mBAAkB,YAAWA,GAAE,cAAY,KAAI;AAAE,WAAO,KAAK,oBAAkB,KAAK,gBAAcC,GAAE,gBAAc,KAAK,gBAAcA,KAAE,EAAC,GAAGA,IAAE,QAAO,KAAK,QAAO,WAAU,KAAK,UAAS,GAAEA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAE,CAAC;", "names": ["a", "e", "r", "t", "i", "s"]}