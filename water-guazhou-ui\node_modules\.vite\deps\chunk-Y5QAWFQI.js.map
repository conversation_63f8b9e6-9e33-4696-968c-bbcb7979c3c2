{"version": 3, "sources": ["../../@arcgis/core/renderers/support/rendererConversion.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../core/has.js\";import r from\"../../core/Error.js\";import{isNone as e,isSome as n}from\"../../core/maybe.js\";import{to3D as o,defaultTo3DOptions as t}from\"../../symbols/support/symbolConversion.js\";function s(r){return e(r)||\"simple\"===r.type||\"unique-value\"===r.type||\"class-breaks\"===r.type||\"dictionary\"===r.type||\"heatmap\"===r.type}function u(n,o){if(e(n))return null;if(!s(n))return new r(\"renderer-conversion-3d:unsupported-renderer\",`Unsupported renderer of type '${n.type||n.declaredClass}'`,{renderer:n});switch(n.type){case\"simple\":return i(n);case\"unique-value\":return a(n,o);case\"class-breaks\":return p(n);case\"dictionary\":case\"heatmap\":return null}return null}function l(e,n){if(!n)return null;let o;if(o=Array.isArray(n)?n:[n],o.length>0){const n=o.map((r=>r.details.symbol.type||r.details.symbol.declaredClass)).filter((r=>!!r));n.sort();const t=[];return n.forEach(((r,e)=>{0!==e&&r===n[e-1]||t.push(r)})),new r(\"renderer-conversion-3d:unsupported-symbols\",`Renderer contains symbols (${t.join(\", \")}) which are not supported in 3D`,{renderer:e,symbolErrors:o})}return null}function i(r){return l(r,o(r.symbol).error)}function a(r,e){const s={...t,...e},u=r.uniqueValueInfos?.map((r=>o(r.symbol,s).error)).filter(n),i=o(r.defaultSymbol,s);return i.error&&u?.unshift(i.error),l(r,u)}function p(r){const e=r.classBreakInfos.map((r=>o(r.symbol).error)).filter(n),t=o(r.defaultSymbol);return t.error&&e.unshift(t.error),l(r,e)}export{s as isSupportedRenderer3D,u as validateTo3D};\n"], "mappings": ";;;;;;;;;;;;;AAI+M,SAASA,GAAEC,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG,aAAWA,GAAE,QAAM,mBAAiBA,GAAE,QAAM,mBAAiBA,GAAE,QAAM,iBAAeA,GAAE,QAAM,cAAYA,GAAE;AAAI;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,EAAE,CAAC,EAAE,QAAO;AAAK,MAAG,CAACD,GAAE,CAAC,EAAE,QAAO,IAAI,EAAE,+CAA8C,iCAAiC,EAAE,QAAM,EAAE,aAAa,KAAI,EAAC,UAAS,EAAC,CAAC;AAAE,UAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAS,aAAO,EAAE,CAAC;AAAA,IAAE,KAAI;AAAe,aAAOE,GAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAe,aAAO,EAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAa,KAAI;AAAU,aAAO;AAAA,EAAI;AAAC,SAAO;AAAI;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,MAAI;AAAE,MAAG,IAAE,MAAM,QAAQ,CAAC,IAAE,IAAE,CAAC,CAAC,GAAE,EAAE,SAAO,GAAE;AAAC,UAAMC,KAAE,EAAE,IAAK,CAAAF,OAAGA,GAAE,QAAQ,OAAO,QAAMA,GAAE,QAAQ,OAAO,aAAc,EAAE,OAAQ,CAAAA,OAAG,CAAC,CAACA,EAAE;AAAE,IAAAE,GAAE,KAAK;AAAE,UAAMC,KAAE,CAAC;AAAE,WAAOD,GAAE,QAAS,CAACF,IAAEI,OAAI;AAAC,YAAIA,MAAGJ,OAAIE,GAAEE,KAAE,CAAC,KAAGD,GAAE,KAAKH,EAAC;AAAA,IAAC,CAAE,GAAE,IAAI,EAAE,8CAA6C,8BAA8BG,GAAE,KAAK,IAAI,CAAC,mCAAkC,EAAC,UAAS,GAAE,cAAa,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAAS,EAAEH,IAAE;AAAC,SAAO,EAAEA,IAAE,EAAEA,GAAE,MAAM,EAAE,KAAK;AAAC;AAAC,SAASC,GAAED,IAAE,GAAE;AAJtoC;AAIuoC,QAAMD,KAAE,EAAC,GAAG,GAAE,GAAG,EAAC,GAAEM,MAAE,KAAAL,GAAE,qBAAF,mBAAoB,IAAK,CAAAA,OAAG,EAAEA,GAAE,QAAOD,EAAC,EAAE,OAAQ,OAAO,IAAGO,KAAE,EAAEN,GAAE,eAAcD,EAAC;AAAE,SAAOO,GAAE,UAAOD,MAAA,gBAAAA,GAAG,QAAQC,GAAE,SAAO,EAAEN,IAAEK,EAAC;AAAC;AAAC,SAAS,EAAEL,IAAE;AAAC,QAAM,IAAEA,GAAE,gBAAgB,IAAK,CAAAA,OAAG,EAAEA,GAAE,MAAM,EAAE,KAAM,EAAE,OAAO,CAAC,GAAEG,KAAE,EAAEH,GAAE,aAAa;AAAE,SAAOG,GAAE,SAAO,EAAE,QAAQA,GAAE,KAAK,GAAE,EAAEH,IAAE,CAAC;AAAC;", "names": ["s", "r", "a", "n", "t", "e", "u", "i"]}