import {
  c
} from "./chunk-XZ2UVSB4.js";
import {
  f
} from "./chunk-XBS7QZIQ.js";
import {
  x
} from "./chunk-W3CLOCDX.js";
import {
  x as x2
} from "./chunk-KE7SPCM7.js";

// node_modules/@arcgis/core/rest/query/executeQueryJSON.js
async function s(r, t, e) {
  const s2 = await a(r, t, e);
  return x2.fromJSON(s2);
}
async function a(o, s2, a2) {
  const n = f(o), i = { ...a2 }, p = x.from(s2), { data: u } = await c(n, p, p.sourceSpatialReference, i);
  return u;
}

export {
  s,
  a
};
//# sourceMappingURL=chunk-UMW4I2EJ.js.map
