{"version": 3, "sources": ["../../@arcgis/core/rest/query/operations/editsZScale.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e}from\"../../../core/maybe.js\";import{getMetersPerVerticalUnitForSR as n}from\"../../../core/unitUtils.js\";import{equals as o}from\"../../../geometry/support/spatialReferenceUtils.js\";function t(e,n,o){if(null==e.hasM||e.hasZ)for(const t of n)for(const e of t)e.length>2&&(e[2]*=o)}function i(e,o,t){if(!e&&!o||!t)return;const i=n(t);s(e,t,i),s(o,t,i)}function s(e,n,o){if(e)for(const t of e)f(t.geometry,n,o)}function f(i,s,f){if(e(i)||!i.spatialReference||o(i.spatialReference,s))return;const r=n(i.spatialReference)/f;if(1!==r)if(\"x\"in i)null!=i.z&&(i.z*=r);else if(\"rings\"in i)t(i,i.rings,r);else if(\"paths\"in i)t(i,i.paths,r);else if(\"points\"in i&&(null==i.hasM||i.hasZ))for(const e of i.points)e.length>2&&(e[2]*=r)}export{i as unapplyEditsZUnitScaling};\n"], "mappings": ";;;;;;;;;AAIuM,SAASA,GAAE,GAAE,GAAE,GAAE;AAAC,MAAG,QAAM,EAAE,QAAM,EAAE,KAAK,YAAUA,MAAK,EAAE,YAAUC,MAAKD,GAAE,CAAAC,GAAE,SAAO,MAAIA,GAAE,CAAC,KAAG;AAAE;AAAC,SAAS,EAAE,GAAE,GAAED,IAAE;AAAC,MAAG,CAAC,KAAG,CAAC,KAAG,CAACA,GAAE;AAAO,QAAME,KAAE,EAAEF,EAAC;AAAE,IAAE,GAAEA,IAAEE,EAAC,GAAE,EAAE,GAAEF,IAAEE,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,MAAG,EAAE,YAAUF,MAAK,EAAE,GAAEA,GAAE,UAAS,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEE,IAAEC,IAAEC,IAAE;AAAC,MAAG,EAAEF,EAAC,KAAG,CAACA,GAAE,oBAAkB,EAAEA,GAAE,kBAAiBC,EAAC,EAAE;AAAO,QAAM,IAAE,EAAED,GAAE,gBAAgB,IAAEE;AAAE,MAAG,MAAI;AAAE,QAAG,OAAMF,GAAE,SAAMA,GAAE,MAAIA,GAAE,KAAG;AAAA,aAAW,WAAUA,GAAE,CAAAF,GAAEE,IAAEA,GAAE,OAAM,CAAC;AAAA,aAAU,WAAUA,GAAE,CAAAF,GAAEE,IAAEA,GAAE,OAAM,CAAC;AAAA,aAAU,YAAWA,OAAI,QAAMA,GAAE,QAAMA,GAAE,MAAM,YAAU,KAAKA,GAAE,OAAO,GAAE,SAAO,MAAI,EAAE,CAAC,KAAG;AAAA;AAAE;", "names": ["t", "e", "i", "s", "f"]}