import {
  s
} from "./chunk-22GGEXM2.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  u2 as u
} from "./chunk-UOKTNY52.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/analysis/Analysis.js
var n = 0;
var l = class extends u(i(s(v))) {
  constructor(e2) {
    super(e2), this.id = `${Date.now().toString(16)}-analysis-${n++}`, this.title = null;
  }
  get parent() {
    return this._get("parent");
  }
  set parent(e2) {
    const t = this.parent;
    if (r(t)) switch (t.type) {
      case "line-of-sight":
      case "dimension":
        t.releaseAnalysis(this);
        break;
      case "2d":
      case "3d":
        t.analyses.includes(this) && t.analyses.remove(this);
    }
    this._set("parent", e2);
  }
  get isEditable() {
    return this.requiredPropertiesForEditing.every(r);
  }
};
e([y({ type: String, constructOnly: true, clonable: false })], l.prototype, "id", void 0), e([y({ type: String })], l.prototype, "title", void 0), e([y({ constructOnly: true })], l.prototype, "type", void 0), e([y({ clonable: false, value: null })], l.prototype, "parent", null), e([y({ readOnly: true })], l.prototype, "isEditable", null), e([y({ readOnly: true })], l.prototype, "requiredPropertiesForEditing", void 0), l = e([a("esri.analysis.Analysis")], l);
var c = l;

export {
  c
};
//# sourceMappingURL=chunk-PXLH6GWI.js.map
