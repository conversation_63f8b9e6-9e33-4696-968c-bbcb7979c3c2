{"version": 3, "sources": ["../../@arcgis/core/intl/substitute.js", "../../@arcgis/core/intl/t9n.js", "../../@arcgis/core/intl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../core/Logger.js\";import{getDeepValue as r}from\"../core/object.js\";import{replace as e}from\"../core/string.js\";import{formatDate as n}from\"./date.js\";import{formatNumber as o}from\"./number.js\";const i=t.getLogger(\"esri.intl.substitute\");function s(t,r,n={}){const{format:o={}}=n;return e(t,(t=>u(t,r,o)))}function u(t,e,n){let o,i;const s=t.indexOf(\":\");if(-1===s?o=t.trim():(o=t.slice(0,s).trim(),i=t.slice(s+1).trim()),!o)return\"\";const u=r(o,e);if(null==u)return\"\";const m=(i?n?.[i]:null)??n?.[o];return m?c(u,m):i?a(u,i):f(u)}function c(t,r){switch(r.type){case\"date\":return n(t,r.intlOptions);case\"number\":return o(t,r.intlOptions);default:return i.warn(\"missing format descriptor for key {key}\"),f(t)}}function a(t,r){switch(r.toLowerCase()){case\"dateformat\":return n(t);case\"numberformat\":return o(t);default:return i.warn(`inline format is unsupported since 4.12: ${r}`),/^(dateformat|datestring)/i.test(r)?n(t):/^numberformat/i.test(r)?o(t):f(t)}}function f(t){switch(typeof t){case\"string\":return t;case\"number\":return o(t);case\"boolean\":return\"\"+t;default:return t instanceof Date?n(t):\"\"}}export{s as substitute};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../request.js\";import t from\"../core/Error.js\";import{isSome as n}from\"../core/maybe.js\";import{normalizeMessageBundleLocale as r}from\"./messages.js\";async function o(e,n,o,i){const a=n.exec(o);if(!a)throw new t(\"esri-intl:invalid-bundle\",`Bundle id \"${o}\" is not compatible with the pattern \"${n}\"`);const c=a[1]?`${a[1]}/`:\"\",l=a[2],w=r(i),h=`${c}${l}.json`,u=w?`${c}${l}_${w}.json`:h;let f;try{f=await s(e(u))}catch(d){if(u===h)throw new t(\"intl:unknown-bundle\",`Bundle \"${o}\" cannot be loaded`,{error:d});try{f=await s(e(h))}catch(d){throw new t(\"intl:unknown-bundle\",`Bundle \"${o}\" cannot be loaded`,{error:d})}}return f}async function s(t){if(n(c.fetchBundleAsset))return c.fetchBundleAsset(t);const r=await e(t,{responseType:\"text\"});return JSON.parse(r.data)}class i{constructor({base:e=\"\",pattern:t,location:n=new URL(window.location.href)}){let r;r=\"string\"==typeof n?e=>new URL(e,new URL(n,window.location.href)).href:n instanceof URL?e=>new URL(e,n).href:n,this.pattern=\"string\"==typeof t?new RegExp(`^${t}`):t,this.getAssetUrl=r,e=e?e.endsWith(\"/\")?e:e+\"/\":\"\",this.matcher=new RegExp(`^${e}(?:(.*)/)?(.*)$`)}fetchMessageBundle(e,t){return o(this.getAssetUrl,this.matcher,e,t)}}function a(e){return new i(e)}const c={};export{i as JSONLoader,a as createJSONLoader,c as test};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nexport{convertDateFormatToIntlOptions,formatDate}from\"./intl/date.js\";export{convertNumberFormatToIntlOptions,formatNumber}from\"./intl/number.js\";export{substitute}from\"./intl/substitute.js\";export{getLocale,onLocaleChange,prefersRTL,setLocale}from\"./intl/locale.js\";import{registerMessageBundleLoader as t}from\"./intl/messages.js\";export{fetchMessageBundle,normalizeMessageBundleLocale}from\"./intl/messages.js\";import{createJSONLoader as e}from\"./intl/t9n.js\";import{getAssetUrl as o}from\"./assets.js\";t(e({pattern:\"esri/\",location:o}));export{e as createJSONLoader,t as registerMessageBundleLoader};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgN,IAAM,IAAE,EAAE,UAAU,sBAAsB;AAAE,SAASA,GAAEC,IAAEC,IAAE,IAAE,CAAC,GAAE;AAAC,QAAK,EAAC,QAAOC,KAAE,CAAC,EAAC,IAAE;AAAE,SAAOD,GAAED,IAAG,CAAAA,OAAG,EAAEA,IAAEC,IAAEC,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEF,IAAE,GAAE,GAAE;AAAC,MAAIE,IAAEC;AAAE,QAAMJ,KAAEC,GAAE,QAAQ,GAAG;AAAE,MAAG,OAAKD,KAAEG,KAAEF,GAAE,KAAK,KAAGE,KAAEF,GAAE,MAAM,GAAED,EAAC,EAAE,KAAK,GAAEI,KAAEH,GAAE,MAAMD,KAAE,CAAC,EAAE,KAAK,IAAG,CAACG,GAAE,QAAM;AAAG,QAAME,KAAE,EAAEF,IAAE,CAAC;AAAE,MAAG,QAAME,GAAE,QAAM;AAAG,QAAMC,MAAGF,KAAE,uBAAIA,MAAG,UAAO,uBAAID;AAAG,SAAOG,KAAE,EAAED,IAAEC,EAAC,IAAEF,KAAEG,GAAEF,IAAED,EAAC,IAAE,EAAEC,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAE;AAAC,UAAOA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAO,aAAO,EAAED,IAAEC,GAAE,WAAW;AAAA,IAAE,KAAI;AAAS,aAAO,EAAED,IAAEC,GAAE,WAAW;AAAA,IAAE;AAAQ,aAAO,EAAE,KAAK,yCAAyC,GAAE,EAAED,EAAC;AAAA,EAAC;AAAC;AAAC,SAASM,GAAEN,IAAEC,IAAE;AAAC,UAAOA,GAAE,YAAY,GAAE;AAAA,IAAC,KAAI;AAAa,aAAO,EAAED,EAAC;AAAA,IAAE,KAAI;AAAe,aAAO,EAAEA,EAAC;AAAA,IAAE;AAAQ,aAAO,EAAE,KAAK,4CAA4CC,EAAC,EAAE,GAAE,4BAA4B,KAAKA,EAAC,IAAE,EAAED,EAAC,IAAE,iBAAiB,KAAKC,EAAC,IAAE,EAAED,EAAC,IAAE,EAAEA,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,UAAO,OAAOA,IAAE;AAAA,IAAC,KAAI;AAAS,aAAOA;AAAA,IAAE,KAAI;AAAS,aAAO,EAAEA,EAAC;AAAA,IAAE,KAAI;AAAU,aAAM,KAAGA;AAAA,IAAE;AAAQ,aAAOA,cAAa,OAAK,EAAEA,EAAC,IAAE;AAAA,EAAE;AAAC;;;ACAv7B,eAAe,EAAE,GAAE,GAAEO,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,KAAKF,EAAC;AAAE,MAAG,CAACE,GAAE,OAAM,IAAIC,GAAE,4BAA2B,cAAcH,EAAC,yCAAyC,CAAC,GAAG;AAAE,QAAMI,KAAEF,GAAE,CAAC,IAAE,GAAGA,GAAE,CAAC,CAAC,MAAI,IAAGG,KAAEH,GAAE,CAAC,GAAE,IAAE,EAAED,EAAC,GAAEK,KAAE,GAAGF,EAAC,GAAGC,EAAC,SAAQE,KAAE,IAAE,GAAGH,EAAC,GAAGC,EAAC,IAAI,CAAC,UAAQC;AAAE,MAAIE;AAAE,MAAG;AAAC,IAAAA,KAAE,MAAML,GAAE,EAAEI,EAAC,CAAC;AAAA,EAAC,SAAO,GAAE;AAAC,QAAGA,OAAID,GAAE,OAAM,IAAIH,GAAE,uBAAsB,WAAWH,EAAC,sBAAqB,EAAC,OAAM,EAAC,CAAC;AAAE,QAAG;AAAC,MAAAQ,KAAE,MAAML,GAAE,EAAEG,EAAC,CAAC;AAAA,IAAC,SAAOG,IAAE;AAAC,YAAM,IAAIN,GAAE,uBAAsB,WAAWH,EAAC,sBAAqB,EAAC,OAAMS,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,eAAeL,GAAEO,IAAE;AAAC,MAAG,EAAEN,GAAE,gBAAgB,EAAE,QAAOA,GAAE,iBAAiBM,EAAC;AAAE,QAAMC,KAAE,MAAM,EAAED,IAAE,EAAC,cAAa,OAAM,CAAC;AAAE,SAAO,KAAK,MAAMC,GAAE,IAAI;AAAC;AAAC,IAAMV,KAAN,MAAO;AAAA,EAAC,YAAY,EAAC,MAAK,IAAE,IAAG,SAAQS,IAAE,UAAS,IAAE,IAAI,IAAI,OAAO,SAAS,IAAI,EAAC,GAAE;AAAC,QAAIC;AAAE,IAAAA,KAAE,YAAU,OAAO,IAAE,CAAAC,OAAG,IAAI,IAAIA,IAAE,IAAI,IAAI,GAAE,OAAO,SAAS,IAAI,CAAC,EAAE,OAAK,aAAa,MAAI,CAAAA,OAAG,IAAI,IAAIA,IAAE,CAAC,EAAE,OAAK,GAAE,KAAK,UAAQ,YAAU,OAAOF,KAAE,IAAI,OAAO,IAAIA,EAAC,EAAE,IAAEA,IAAE,KAAK,cAAYC,IAAE,IAAE,IAAE,EAAE,SAAS,GAAG,IAAE,IAAE,IAAE,MAAI,IAAG,KAAK,UAAQ,IAAI,OAAO,IAAI,CAAC,iBAAiB;AAAA,EAAC;AAAA,EAAC,mBAAmB,GAAED,IAAE;AAAC,WAAO,EAAE,KAAK,aAAY,KAAK,SAAQ,GAAEA,EAAC;AAAA,EAAC;AAAC;AAAC,SAASR,GAAE,GAAE;AAAC,SAAO,IAAID,GAAE,CAAC;AAAC;AAAC,IAAMG,KAAE,CAAC;;;ACApuB,EAAES,GAAE,EAAC,SAAQ,SAAQ,UAAS,EAAC,CAAC,CAAC;", "names": ["s", "t", "r", "o", "i", "u", "m", "a", "o", "i", "a", "s", "c", "l", "h", "u", "f", "d", "t", "r", "e", "a"]}