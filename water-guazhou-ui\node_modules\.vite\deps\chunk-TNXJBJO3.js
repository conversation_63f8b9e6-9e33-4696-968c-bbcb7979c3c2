import {
  e
} from "./chunk-64RWCMSJ.js";
import {
  r as r3
} from "./chunk-3WUI7ZKG.js";
import {
  L
} from "./chunk-JXLVNWKF.js";
import {
  r as r2
} from "./chunk-HP475EI3.js";
import {
  f
} from "./chunk-EKX3LLYN.js";
import {
  c
} from "./chunk-LTKA6OKA.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/snappingCandidateElevationAlignment.js
function r4(e3 = false, t3) {
  if (e3) {
    const { elevationInfo: e4, alignPointsInFeatures: s2, spatialReference: n3 } = t3;
    return new l(e4, s2, n3);
  }
  return new c2();
}
var c2 = class {
  async alignCandidates(e3, t3) {
    return e3;
  }
  notifyElevationSourceChange() {
  }
};
var h = 1024;
var l = class {
  constructor(t3, s2, n3) {
    this._elevationInfo = t3, this._alignPointsInFeatures = s2, this.spatialReference = n3, this._alignmentsCache = new e(h), this._cacheVersion = 0, this._metersPerVerticalUnit = L(n3);
  }
  async alignCandidates(e3, t3) {
    const n3 = this._elevationInfo;
    return r(n3) && "absolute-height" === n3.mode && !n3.featureExpressionInfo ? (this._alignAbsoluteElevationCandidates(e3, n3), e3) : this._alignComputedElevationCandidates(e3, t3);
  }
  notifyElevationSourceChange() {
    this._alignmentsCache.clear(), this._cacheVersion++;
  }
  _alignAbsoluteElevationCandidates(e3, t3) {
    const { offset: s2, unit: o2 } = t3;
    if (t(s2)) return;
    const i3 = s2 * (r3(o2 ?? "meters") / this._metersPerVerticalUnit);
    for (const n3 of e3) switch (n3.type) {
      case "edge":
        n3.start.z += i3, n3.end.z += i3;
        continue;
      case "vertex":
        n3.target.z += i3;
        continue;
    }
  }
  async _alignComputedElevationCandidates(e3, s2) {
    const n3 = /* @__PURE__ */ new Map();
    for (const o2 of e3) r2(n3, o2.objectId, p2).push(o2);
    const [i3, a3, r7] = this._prepareQuery(n3), c5 = await this._alignPointsInFeatures(i3, s2);
    f(s2);
    if (r7 !== this._cacheVersion) return this._alignComputedElevationCandidates(e3, s2);
    this._applyCacheAndResponse(i3, c5, a3);
    const { drapedObjectIds: h2, failedObjectIds: l2 } = c5, d2 = [];
    for (const t3 of e3) {
      const { objectId: e4 } = t3;
      h2.has(e4) && "edge" === t3.type && (t3.draped = true), l2.has(e4) || d2.push(t3);
    }
    return d2;
  }
  _prepareQuery(e3) {
    const t3 = [], s2 = [];
    for (const [n3, o2] of e3) {
      const e4 = [];
      for (const t4 of o2) this._addToQueriesOrCachedResult(n3, t4.target, e4, s2), "edge" === t4.type && (this._addToQueriesOrCachedResult(n3, t4.start, e4, s2), this._addToQueriesOrCachedResult(n3, t4.end, e4, s2));
      0 !== e4.length && t3.push({ objectId: n3, points: e4 });
    }
    return [t3, s2, this._cacheVersion];
  }
  _addToQueriesOrCachedResult(e3, t3, n3, o2) {
    const i3 = u(e3, t3), a3 = this._alignmentsCache.get(i3);
    r(a3) ? o2.push(new d(t3, a3)) : n3.push(t3);
  }
  _applyCacheAndResponse(e3, { elevations: t3, drapedObjectIds: s2, failedObjectIds: n3 }, o2) {
    for (const r7 of o2) r7.apply();
    let i3 = 0;
    const a3 = this._alignmentsCache;
    for (const { objectId: r7, points: c5 } of e3) {
      if (n3.has(r7)) {
        i3 += c5.length;
        continue;
      }
      const e4 = !s2.has(r7);
      for (const s3 of c5) {
        const n4 = u(r7, s3), o3 = t3[i3++];
        s3.z = o3, e4 && a3.put(n4, o3, 1);
      }
    }
  }
};
var d = class {
  constructor(e3, t3) {
    this.point = e3, this.z = t3;
  }
  apply() {
    this.point.z = this.z;
  }
};
function u(e3, { x: t3, y: s2, z: n3 }) {
  return `${e3}-${t3}-${s2}-${n3 ?? 0}}`;
}
function p2() {
  return [];
}

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/snappingCandidateElevationFilter.js
var t2 = class {
  filter(t3, n3) {
    return n3;
  }
  notifyElevationSourceChange() {
  }
};
var n = class {
  filter(t3, n3) {
    const { point: r7, distance: c5 } = t3, { z: i3 } = r7;
    if (!(null != i3)) return n3;
    if (0 === n3.length) return n3;
    const o2 = s(c5), u2 = this._updateCandidatesTo3D(n3, r7, o2).filter(e2);
    return u2.sort(a), u2;
  }
  _updateCandidatesTo3D(t3, n3, e3) {
    for (const r7 of t3) switch (r7.type) {
      case "edge":
        c3(r7, n3, e3);
        continue;
      case "vertex":
        o(r7, n3, e3);
        continue;
    }
    return t3;
  }
};
function e2(t3) {
  return t3.distance <= 1;
}
function r5(e3 = false) {
  return e3 ? new n() : new t2();
}
function c3(t3, n3, { x: e3, y: r7, z: c5 }) {
  const { start: o2, end: s2, target: a3 } = t3;
  t3.draped || i(a3, n3, o2, s2);
  const u2 = (n3.x - a3.x) / e3, d2 = (n3.y - a3.y) / r7, f2 = (n3.z - a3.z) / c5;
  t3.distance = Math.sqrt(u2 * u2 + d2 * d2 + f2 * f2);
}
function i(t3, n3, e3, r7) {
  const c5 = r7.x - e3.x, i3 = r7.y - e3.y, o2 = r7.z - e3.z, s2 = c5 * c5 + i3 * i3 + o2 * o2, a3 = (n3.x - e3.x) * c5 + (n3.y - e3.y) * i3 + o2 * (n3.z - e3.z), u2 = Math.min(1, Math.max(0, a3 / s2)), d2 = e3.x + c5 * u2, f2 = e3.y + i3 * u2, x = e3.z + o2 * u2;
  t3.x = d2, t3.y = f2, t3.z = x;
}
function o(t3, n3, { x: e3, y: r7, z: c5 }) {
  const { target: i3 } = t3, o2 = (n3.x - i3.x) / e3, s2 = (n3.y - i3.y) / r7, a3 = (n3.z - i3.z) / c5, u2 = Math.sqrt(o2 * o2 + s2 * s2 + a3 * a3);
  t3.distance = u2;
}
function s(t3) {
  return "number" == typeof t3 ? { x: t3, y: t3, z: t3 } : t3;
}
function a(t3, n3) {
  return t3.distance - n3.distance;
}

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/symbologySnappingCandidates.js
function n2(t3 = false, e3) {
  return t3 ? new i2(e3) : new c4();
}
var c4 = class {
  async fetch() {
    return [];
  }
  notifySymbologyChange() {
  }
};
var r6 = 1024;
var i2 = class {
  constructor(t3) {
    this._getSymbologyCandidates = t3, this._candidatesCache = new e(r6), this._cacheVersion = 0;
  }
  async fetch(e3, o2) {
    if (0 === e3.length) return [];
    const n3 = [], c5 = [], r7 = this._candidatesCache;
    for (const s2 of e3) {
      const e4 = a2(s2), o3 = r7.get(e4);
      if (o3) for (const s3 of o3) c5.push(p(s3));
      else n3.push(s2), r7.put(e4, [], 1);
    }
    if (0 === n3.length) return c5;
    const i3 = this._cacheVersion, { candidates: h2, sourceCandidateIndices: d2 } = await this._getSymbologyCandidates(n3, o2);
    f(o2);
    if (i3 !== this._cacheVersion) return this.fetch(e3, o2);
    const f2 = [], { length: g } = h2;
    for (let s2 = 0; s2 < g; ++s2) {
      const e4 = h2[s2], o3 = a2(n3[d2[s2]]), c6 = r7.get(o3);
      c6.push(e4), r7.put(o3, c6, c6.length), f2.push(p(e4));
    }
    return c5.concat(f2);
  }
  notifySymbologyChange() {
    this._candidatesCache.clear(), this._cacheVersion++;
  }
};
function a2(t3) {
  switch (t3.type) {
    case "vertex": {
      const { objectId: e3, target: s2 } = t3, n3 = `${e3}-vertex-${s2.x}-${s2.y}-${s2.z ?? 0}`;
      return c(n3).toString();
    }
    case "edge": {
      const { objectId: e3, start: s2, end: n3 } = t3, c5 = `${e3}-edge-${s2.x}-${s2.y}-${s2.z ?? 0}-to-${n3.x}-${n3.y}-${n3.z ?? 0}`;
      return c(c5).toString();
    }
    default:
      return "";
  }
}

export {
  r4 as r,
  r5 as r2,
  n2 as n
};
//# sourceMappingURL=chunk-TNXJBJO3.js.map
