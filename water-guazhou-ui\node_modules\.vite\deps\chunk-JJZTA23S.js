import {
  j
} from "./chunk-VXAO6YJP.js";
import {
  f as f2
} from "./chunk-34BE5ZRD.js";
import {
  E,
  S
} from "./chunk-WAPZ634R.js";
import {
  a,
  c as c2
} from "./chunk-26N6FACI.js";
import {
  U,
  b,
  d,
  j as j2,
  p
} from "./chunk-WJPDYSRI.js";
import {
  b as b2
} from "./chunk-QMNV7QQK.js";
import {
  c
} from "./chunk-XVA5SA7P.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";
import {
  f
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/symbols/cim/ExpandedCIM.js
var s2 = { marker: E.MARKER, fill: E.FILL, line: E.LINE, text: E.TEXT };
var l = class {
  constructor(e, l2, n2, r3) {
    const c3 = { minScale: l2 == null ? void 0 : l2.minScale, maxScale: l2 == null ? void 0 : l2.maxScale }, m = i(c3);
    this.layers = e, this.data = l2, this.hash = this._createHash() + m, this.rendererKey = n2;
    const o = { isOutline: false, placement: null, symbologyType: S.DEFAULT, vvFlags: n2 };
    for (const t of e) {
      const e2 = s2[t.type];
      o.isOutline = "line" === t.type && t.isOutline, t.materialKey = f2(e2, o), t.maxVVSize = r3, t.scaleInfo = c3, t.templateHash += m;
    }
  }
  get type() {
    return "expanded-cim";
  }
  _createHash() {
    let e = "";
    for (const t of this.layers) e += t.templateHash;
    return e;
  }
};
function i(e) {
  return e.minScale || e.maxScale ? e.minScale + "-" + e.maxScale : "";
}

// node_modules/@arcgis/core/views/2d/layers/support/webStyleUtils.js
async function u(t, r3, o) {
  if (!t.name) throw new s("style-symbol-reference-name-missing", "Missing name in style symbol reference");
  if (t.styleName && "Esri2DPointSymbolsStyle" === t.styleName) return p2(t, o);
  try {
    return b3(await p(t, r3, o), t.name, r3, o);
  } catch (s3) {
    return f(s3), null;
  }
}
async function p2(t, r3) {
  const e = U.replace(/\{SymbolName\}/gi, t.name);
  try {
    const t2 = await j2(e, r3);
    return d(t2.data);
  } catch (o) {
    return f(o), null;
  }
}
async function b3(a2, i3, u2, p3) {
  const b4 = a2.data, d2 = { portal: u2 && r(u2.portal) ? u2.portal : b2.getDefault(), url: L(a2.baseUrl), origin: "portal-item" }, j3 = b4.items.find((t) => t.name === i3);
  if (!j3) {
    throw new s("symbolstyleutils:symbol-name-not-found", `The symbol name '${i3}' could not be found`, { symbolName: i3 });
  }
  let h = c(b(j3, "cimRef"), d2);
  c2() && (h = a(h));
  try {
    const t = await j2(h, p3);
    return d(t.data);
  } catch (w) {
    return f(w), null;
  }
}

// node_modules/@arcgis/core/views/2d/layers/support/cimSymbolUtils.js
var r2 = async (a2, r3, i3) => new l(await j(a2.data, r3, i3), a2.data, a2.rendererKey, a2.maxVVSize);
async function i2(e, t, i3, n2) {
  if (!e) return null;
  if ("cim" === e.type) return r2(e, t, i3);
  if ("web-style" === e.type) {
    const l2 = { type: "cim", data: await u(e, null, n2) ?? void 0, rendererKey: e.rendererKey, maxVVSize: e.maxVVSize };
    return r2(l2, t, i3);
  }
  return e;
}
function n(e) {
  if (!e) return null;
  const { avoidSDFRasterization: t, type: a2, cim: r3, url: i3, materialHash: n2 } = e, l2 = { cim: r3, type: a2, mosaicHash: n2, url: i3, size: null, dashTemplate: null, path: null, text: null, fontName: null, animatedSymbolProperties: null, avoidSDFRasterization: t };
  switch (a2) {
    case "marker":
      l2.size = e.size, l2.path = e.path, l2.animatedSymbolProperties = e.animatedSymbolProperties;
      break;
    case "line":
      l2.dashTemplate = e.dashTemplate;
      break;
    case "text":
      l2.text = e.text, l2.fontName = e.fontName;
  }
  return l2;
}

export {
  i2 as i,
  n
};
//# sourceMappingURL=chunk-JJZTA23S.js.map
