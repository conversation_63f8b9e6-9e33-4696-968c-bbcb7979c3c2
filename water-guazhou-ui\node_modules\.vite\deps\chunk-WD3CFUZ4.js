import {
  t as t3
} from "./chunk-RYZGQKBF.js";
import {
  t as t2
} from "./chunk-MI2Z635K.js";
import {
  W,
  e2
} from "./chunk-NQQSL2QK.js";
import {
  n2 as n
} from "./chunk-IZLLLMFE.js";
import {
  h,
  l
} from "./chunk-QUHG7NMD.js";
import {
  e,
  t2 as t,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/widgets/Compass/CompassViewModel.js
var h2 = "esri.widgets.CompassViewModel";
var d = class extends t3(v) {
  constructor(t4) {
    super(t4), this._handles = new t(), this.orientation = { x: 0, y: 0, z: 0 }, this.view = null, this._updateForCamera = this._updateForCamera.bind(this), this._updateForRotation = this._updateForRotation.bind(this), this._updateRotationWatcher = this._updateRotationWatcher.bind(this);
  }
  initialize() {
    this._handles.add(l(() => this.view, this._updateRotationWatcher, h));
  }
  destroy() {
    p(this._handles), this.view = null;
  }
  get canShowNorth() {
    const t4 = this.get("view.spatialReference");
    return !(!t4 || !t4.isWebMercator && !t4.isGeographic);
  }
  get state() {
    return this.get("view.ready") ? this.canShowNorth ? "compass" : "rotation" : "disabled";
  }
  reset() {
    var _a;
    if (!this.get("view.ready")) return;
    const t4 = {};
    "2d" === ((_a = this.view) == null ? void 0 : _a.type) ? t4.rotation = 0 : t4.heading = 0, this.callGoTo({ target: t4 });
  }
  _updateForRotation(t4) {
    null != t4 && (this.orientation = { z: t4 });
  }
  _updateForCamera(t4) {
    if (!t4) return;
    const o = -t4.heading;
    this.orientation = { x: 0, y: 0, z: o };
  }
  _updateRotationWatcher(t4) {
    this._handles.removeAll(), t4 && this._handles.add("2d" === t4.type ? l(() => t4 == null ? void 0 : t4.rotation, this._updateForRotation, h) : l(() => t4 == null ? void 0 : t4.camera, this._updateForCamera, h));
  }
};
e([y({ readOnly: true })], d.prototype, "canShowNorth", null), e([y()], d.prototype, "orientation", void 0), e([y({ readOnly: true })], d.prototype, "state", null), e([y()], d.prototype, "view", void 0), d = e([a(h2)], d);
var c = d;

// node_modules/@arcgis/core/widgets/Compass/css.js
var i = { base: "esri-compass esri-widget--button esri-widget", text: "esri-icon-font-fallback-text", icon: "esri-compass__icon", rotationIcon: "esri-icon-dial", northIcon: "esri-icon-compass", widgetIcon: "esri-icon-locate-circled", interactive: "esri-interactive", disabled: "esri-disabled" };

// node_modules/@arcgis/core/widgets/Compass.js
var n2 = class extends W {
  constructor(e3, s) {
    super(e3, s), this.iconClass = i.widgetIcon, this.messages = null, this.viewModel = new c();
  }
  get goToOverride() {
    return this.viewModel.goToOverride;
  }
  set goToOverride(e3) {
    this.viewModel.goToOverride = e3;
  }
  get label() {
    var _a;
    return ((_a = this.messages) == null ? void 0 : _a.widgetLabel) ?? "";
  }
  set label(e3) {
    this._overrideIfSome("label", e3);
  }
  get view() {
    return this.viewModel.view;
  }
  set view(e3) {
    this.viewModel.view = e3;
  }
  reset() {
    return this.viewModel.reset();
  }
  render() {
    const { orientation: e3, state: s } = this.viewModel, t4 = "disabled" === s, o = "compass" === ("rotation" === s ? "rotation" : "compass"), r = t4 ? -1 : 0, a2 = { [i.disabled]: t4, [i.interactive]: !t4 }, p2 = { [i.northIcon]: o, [i.rotationIcon]: !o }, { messages: n3 } = this;
    return n("div", { bind: this, class: this.classes(i.base, a2), onclick: this._reset, onkeydown: this._reset, role: "button", tabIndex: r, "aria-label": n3.reset, title: n3.reset }, n("span", { "aria-hidden": "true", class: this.classes(i.icon, p2), styles: this._toRotationTransform(e3) }), n("span", { class: i.text }, n3.reset));
  }
  _reset() {
    this.viewModel.reset();
  }
  _toRotationTransform(e3) {
    return { transform: `rotateZ(${e3.z}deg)` };
  }
};
e([y()], n2.prototype, "goToOverride", null), e([y()], n2.prototype, "iconClass", void 0), e([y()], n2.prototype, "label", null), e([y(), e2("esri/widgets/Compass/t9n/Compass")], n2.prototype, "messages", void 0), e([y()], n2.prototype, "view", null), e([y({ type: c })], n2.prototype, "viewModel", void 0), e([t2()], n2.prototype, "_reset", null), n2 = e([a("esri.widgets.Compass")], n2);
var d2 = n2;

export {
  d2 as d
};
//# sourceMappingURL=chunk-WD3CFUZ4.js.map
