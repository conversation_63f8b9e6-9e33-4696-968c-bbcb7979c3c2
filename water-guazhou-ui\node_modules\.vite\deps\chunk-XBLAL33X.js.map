{"version": 3, "sources": ["../../@arcgis/core/arcade/functions/fieldStats.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{V as e,v as t,b as n,c as r,s as a}from\"../../chunks/languageUtils.js\";function s(e){let t=0;for(let n=0;n<e.length;n++)t+=e[n];return t/e.length}function c(e){const t=s(e);let n=0;for(let r=0;r<e.length;r++)n+=(t-e[r])**2;return n/e.length}function u(e){let t=0;for(let n=0;n<e.length;n++)t+=e[n];return t}function i(e,s){const c=[],u={},i=[];for(let o=0;o<e.length;o++){if(void 0!==e[o]&&null!==e[o]&&e[o]!==t){const t=e[o];if(n(t)||r(t))void 0===u[t]&&(c.push(t),u[t]=1);else{let e=!1;for(let n=0;n<i.length;n++)!0===a(i[n],t)&&(e=!0);!1===e&&(i.push(t),c.push(t))}}if(c.length>=s&&-1!==s)return c}return c}function o(e){switch(e.toLowerCase()){case\"distinct\":return\"distinct\";case\"avg\":case\"mean\":return\"avg\";case\"min\":return\"min\";case\"sum\":return\"sum\";case\"max\":return\"max\";case\"stdev\":case\"stddev\":return\"stddev\";case\"var\":case\"variance\":return\"var\";case\"count\":return\"count\"}return\"\"}function l(t,n,r=1e3){switch(t.toLowerCase()){case\"distinct\":return i(n,r);case\"avg\":case\"mean\":return s(e(n));case\"min\":return Math.min.apply(Math,e(n));case\"sum\":return u(e(n));case\"max\":return Math.max.apply(Math,e(n));case\"stdev\":case\"stddev\":return Math.sqrt(c(e(n)));case\"var\":case\"variance\":return c(e(n));case\"count\":return n.length}return 0}export{l as calculateStat,o as decodeStatType};\n"], "mappings": ";;;;;;;;;AAI8E,SAAS,EAAE,GAAE;AAAC,MAAI,IAAE;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,MAAG,EAAE,CAAC;AAAE,SAAO,IAAE,EAAE;AAAM;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,CAAC;AAAE,MAAI,IAAE;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,OAAI,IAAE,EAAE,CAAC,MAAI;AAAE,SAAO,IAAE,EAAE;AAAM;AAAC,SAAS,EAAE,GAAE;AAAC,MAAI,IAAE;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,MAAG,EAAE,CAAC;AAAE,SAAO;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,QAAG,WAAS,EAAE,CAAC,KAAG,SAAO,EAAE,CAAC,KAAG,EAAE,CAAC,MAAI,GAAE;AAAC,YAAM,IAAE,EAAE,CAAC;AAAE,UAAG,EAAE,CAAC,KAAG,EAAE,CAAC,EAAE,YAASD,GAAE,CAAC,MAAID,GAAE,KAAK,CAAC,GAAEC,GAAE,CAAC,IAAE;AAAA,WAAO;AAAC,YAAIE,KAAE;AAAG,iBAAQ,IAAE,GAAE,IAAED,GAAE,QAAO,IAAI,UAAK,GAAEA,GAAE,CAAC,GAAE,CAAC,MAAIC,KAAE;AAAI,kBAAKA,OAAID,GAAE,KAAK,CAAC,GAAEF,GAAE,KAAK,CAAC;AAAA,MAAE;AAAA,IAAC;AAAC,QAAGA,GAAE,UAAQD,MAAG,OAAKA,GAAE,QAAOC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAA0R,SAAS,EAAE,GAAE,GAAE,IAAE,KAAI;AAAC,UAAO,EAAE,YAAY,GAAE;AAAA,IAAC,KAAI;AAAW,aAAO,EAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAM,KAAI;AAAO,aAAO,EAAE,GAAE,CAAC,CAAC;AAAA,IAAE,KAAI;AAAM,aAAO,KAAK,IAAI,MAAM,MAAK,GAAE,CAAC,CAAC;AAAA,IAAE,KAAI;AAAM,aAAO,EAAE,GAAE,CAAC,CAAC;AAAA,IAAE,KAAI;AAAM,aAAO,KAAK,IAAI,MAAM,MAAK,GAAE,CAAC,CAAC;AAAA,IAAE,KAAI;AAAA,IAAQ,KAAI;AAAS,aAAO,KAAK,KAAK,EAAE,GAAE,CAAC,CAAC,CAAC;AAAA,IAAE,KAAI;AAAA,IAAM,KAAI;AAAW,aAAO,EAAE,GAAE,CAAC,CAAC;AAAA,IAAE,KAAI;AAAQ,aAAO,EAAE;AAAA,EAAM;AAAC,SAAO;AAAC;", "names": ["s", "c", "u", "i", "e"]}