{"version": 3, "sources": ["../../@arcgis/core/views/interactive/tooltip/TranslateTooltipInfos.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{zeroMeters as e}from\"../../../core/quantityUtils.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import{subclass as i}from\"../../../core/accessorSupport/decorators/subclass.js\";import{SketchTooltipInfo as s}from\"./SketchTooltipInfo.js\";let r=class extends s{constructor(t){super(t),this.type=\"translate-graphic\",this.distance=e}};t([o()],r.prototype,\"type\",void 0),t([o()],r.prototype,\"distance\",void 0),r=t([i(\"esri.views.interactive.tooltip.TranslateGraphicTooltipInfo\")],r);let p=class extends s{constructor(t){super(t),this.type=\"translate-graphic-z\",this.distance=e}};t([o()],p.prototype,\"type\",void 0),t([o()],p.prototype,\"distance\",void 0),p=t([i(\"esri.views.interactive.tooltip.TranslateGraphicZTooltipInfo\")],p);let a=class extends s{constructor(t){super(t),this.type=\"translate-graphic-xy\",this.distance=e}};t([o()],a.prototype,\"type\",void 0),t([o()],a.prototype,\"distance\",void 0),a=t([i(\"esri.views.interactive.tooltip.TranslateGraphicXYTooltipInfo\")],a);let n=class extends s{constructor(t){super(t),this.type=\"translate-vertex\",this.distance=e,this.elevation=null,this.area=null,this.totalLength=null}};t([o()],n.prototype,\"type\",void 0),t([o()],n.prototype,\"distance\",void 0),t([o()],n.prototype,\"elevation\",void 0),t([o()],n.prototype,\"area\",void 0),t([o()],n.prototype,\"totalLength\",void 0),n=t([i(\"esri.views.interactive.tooltip.TranslateVertexTooltipInfo\")],n);let l=class extends s{constructor(t){super(t),this.type=\"translate-vertex-z\",this.distance=e,this.elevation=null}};t([o()],l.prototype,\"type\",void 0),t([o()],l.prototype,\"distance\",void 0),t([o()],l.prototype,\"elevation\",void 0),l=t([i(\"esri.views.interactive.tooltip.TranslateVertexZTooltipInfo\")],l);let c=class extends s{constructor(t){super(t),this.type=\"translate-vertex-xy\",this.distance=e,this.elevation=null,this.area=null,this.totalLength=null}};t([o()],c.prototype,\"type\",void 0),t([o()],c.prototype,\"distance\",void 0),t([o()],c.prototype,\"elevation\",void 0),t([o()],c.prototype,\"area\",void 0),t([o()],c.prototype,\"totalLength\",void 0),c=t([i(\"esri.views.interactive.tooltip.TranslateVertexXYTooltipInfo\")],c);export{r as TranslateGraphicTooltipInfo,a as TranslateGraphicXYTooltipInfo,p as TranslateGraphicZTooltipInfo,n as TranslateVertexTooltipInfo,c as TranslateVertexXYTooltipInfo,l as TranslateVertexZTooltipInfo};\n"], "mappings": ";;;;;;;;;;;;;AAIwU,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,qBAAoB,KAAK,WAAS;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,4DAA4D,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,uBAAsB,KAAK,WAAS;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,6DAA6D,CAAC,GAAE,CAAC;AAAE,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,wBAAuB,KAAK,WAAS;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEC,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,8DAA8D,CAAC,GAAEA,EAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,oBAAmB,KAAK,WAAS,GAAE,KAAK,YAAU,MAAK,KAAK,OAAK,MAAK,KAAK,cAAY;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,2DAA2D,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,sBAAqB,KAAK,WAAS,GAAE,KAAK,YAAU;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,4DAA4D,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,uBAAsB,KAAK,WAAS,GAAE,KAAK,YAAU,MAAK,KAAK,OAAK,MAAK,KAAK,cAAY;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,6DAA6D,CAAC,GAAE,CAAC;", "names": ["t", "a"]}