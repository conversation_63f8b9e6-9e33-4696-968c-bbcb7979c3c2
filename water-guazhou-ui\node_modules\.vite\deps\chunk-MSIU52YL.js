import {
  l
} from "./chunk-5JCRZXRL.js";
import {
  e
} from "./chunk-4CHRJPQP.js";
import {
  R
} from "./chunk-JXLVNWKF.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/tiling/LODInfo.js
function i(t, r3) {
  return [t, r3];
}
function e2(t, r3, o2) {
  return t[0] = r3, t[1] = o2, t;
}
function s(t, r3, o2, i3, e3) {
  return t[0] = r3, t[1] = o2, t[2] = i3, t[3] = e3, t;
}
var n = new e("0/0/0/0");
var l2 = class _l {
  static create(o2, s2, n3 = null) {
    const h2 = R(o2.spatialReference), a2 = s2.origin || i(o2.origin.x, o2.origin.y), u = i(o2.size[0] * s2.resolution, o2.size[1] * s2.resolution), m = i(-1 / 0, -1 / 0), g = i(1 / 0, 1 / 0), w = i(1 / 0, 1 / 0);
    r(n3) && (e2(m, Math.max(0, Math.floor((n3.xmin - a2[0]) / u[0])), Math.max(0, Math.floor((a2[1] - n3.ymax) / u[1]))), e2(g, Math.max(0, Math.floor((n3.xmax - a2[0]) / u[0])), Math.max(0, Math.floor((a2[1] - n3.ymin) / u[1]))), e2(w, g[0] - m[0] + 1, g[1] - m[1] + 1));
    const { cols: c, rows: d } = s2;
    let f, F, z, p;
    return !n3 && c && d && (e2(m, c[0], d[0]), e2(g, c[1], d[1]), e2(w, c[1] - c[0] + 1, d[1] - d[0] + 1)), o2.isWrappable ? (f = i(Math.ceil(Math.round((h2.valid[1] - h2.valid[0]) / s2.resolution) / o2.size[0]), w[1]), F = i(Math.floor((h2.origin[0] - a2[0]) / u[0]), m[1]), z = i(f[0] + F[0] - 1, g[1]), p = true) : (F = m, z = g, f = w, p = false), new _l(s2.level, s2.resolution, s2.scale, a2, m, g, w, u, F, z, f, p);
  }
  constructor(t, r3, o2, i3, e3, s2, n3, l3, h2, a2, u, m) {
    this.level = t, this.resolution = r3, this.scale = o2, this.origin = i3, this.first = e3, this.last = s2, this.size = n3, this.norm = l3, this.worldStart = h2, this.worldEnd = a2, this.worldSize = u, this.wrap = m;
  }
  normalizeCol(t) {
    if (!this.wrap) return t;
    const r3 = this.worldSize[0];
    return t < 0 ? r3 - 1 - Math.abs((t + 1) % r3) : t % r3;
  }
  denormalizeCol(t, r3) {
    return this.wrap ? this.worldSize[0] * r3 + t : t;
  }
  getWorldForColumn(t) {
    return this.wrap ? Math.floor(t / this.worldSize[0]) : 0;
  }
  getFirstColumnForWorld(t) {
    return t * this.worldSize[0] + this.first[0];
  }
  getLastColumnForWorld(t) {
    return t * this.worldSize[0] + this.first[0] + this.size[0] - 1;
  }
  getColumnForX(t) {
    return (t - this.origin[0]) / this.norm[0];
  }
  getXForColumn(t) {
    return this.origin[0] + t * this.norm[0];
  }
  getRowForY(t) {
    return (this.origin[1] - t) / this.norm[1];
  }
  getYForRow(t) {
    return this.origin[1] - t * this.norm[1];
  }
  getTileBounds(t, r3, o2 = false) {
    n.set(r3);
    const i3 = o2 ? n.col : this.denormalizeCol(n.col, n.world), e3 = n.row;
    return s(t, this.getXForColumn(i3), this.getYForRow(e3 + 1), this.getXForColumn(i3 + 1), this.getYForRow(e3)), t;
  }
  getTileCoords(t, r3, o2 = false) {
    n.set(r3);
    const i3 = o2 ? n.col : this.denormalizeCol(n.col, n.world);
    return Array.isArray(t) ? e2(t, this.getXForColumn(i3), this.getYForRow(n.row)) : (t.x = this.getXForColumn(i3), t.y = this.getYForRow(n.row)), t;
  }
};

// node_modules/@arcgis/core/views/2d/tiling/TileSpan.js
var o = class {
  constructor(o2, s2, t) {
    this.row = o2, this.colFrom = s2, this.colTo = t;
  }
};

// node_modules/@arcgis/core/views/2d/tiling/TileInfoView.js
var i2 = new e("0/0/0/0");
var n2 = class _n {
  static create(e3, t) {
    e3[1] > t[1] && ([e3, t] = [t, e3]);
    const [o2, l3] = e3, [s2, i3] = t, r3 = s2 - o2, a2 = i3 - l3, h2 = 0 !== a2 ? r3 / a2 : 0, c = (Math.ceil(l3) - l3) * h2, f = (Math.floor(l3) - l3) * h2;
    return new _n(o2, Math.floor(l3), Math.ceil(i3), h2, r3 < 0 ? c : f, r3 < 0 ? f : c, r3 < 0 ? s2 : o2, r3 < 0 ? o2 : s2);
  }
  constructor(e3, t, o2, l3, s2, i3, n3, r3) {
    this.x = e3, this.ymin = t, this.ymax = o2, this.invM = l3, this.leftAdjust = s2, this.rightAdjust = i3, this.leftBound = n3, this.rightBound = r3;
  }
  incrRow() {
    this.x += this.invM;
  }
  getLeftCol() {
    return Math.max(this.x + this.leftAdjust, this.leftBound);
  }
  getRightCol() {
    return Math.min(this.x + this.rightAdjust, this.rightBound);
  }
};
var r2 = [[0, 0], [0, 0], [0, 0], [0, 0]];
var a = 1e-6;
var h = class {
  constructor(e3, o2 = null) {
    this.tileInfo = e3, this.fullExtent = o2, this.scales = [], this._infoByScale = {}, this._infoByLevel = {};
    const l3 = e3.lods.slice();
    l3.sort((e4, t) => t.scale - e4.scale);
    const s2 = this._lodInfos = l3.map((l4) => l2.create(e3, l4, o2));
    l3.forEach((e4, t) => {
      this._infoByLevel[e4.level] = s2[t], this._infoByScale[e4.scale] = s2[t], this.scales[t] = e4.scale;
    }, this), this._wrap = e3.isWrappable;
  }
  get spatialReference() {
    return this.tileInfo.spatialReference;
  }
  getLODInfoAt(e3) {
    return this._infoByLevel["number" == typeof e3 ? e3 : e3.level];
  }
  getTileBounds(e3, t, o2 = false) {
    i2.set(t);
    const l3 = this._infoByLevel[i2.level];
    return l3 ? l3.getTileBounds(e3, i2, o2) : e3;
  }
  getTileCoords(e3, t, o2 = false) {
    i2.set(t);
    const l3 = this._infoByLevel[i2.level];
    return l3 ? l3.getTileCoords(e3, i2, o2) : e3;
  }
  getTileCoverage(e3, t = 192, l3 = "closest") {
    const i3 = "closest" === l3 ? this.getClosestInfoForScale(e3.scale) : this.getSmallestInfoForScale(e3.scale), a2 = l.pool.acquire(i3), h2 = this._wrap;
    let c, f, u, m = 1 / 0, g = -1 / 0;
    const d = a2.spans;
    r2[0][0] = r2[0][1] = r2[1][1] = r2[3][0] = -t, r2[1][0] = r2[2][0] = e3.size[0] + t, r2[2][1] = r2[3][1] = e3.size[1] + t;
    for (const o2 of r2) e3.toMap(o2, o2), o2[0] = i3.getColumnForX(o2[0]), o2[1] = i3.getRowForY(o2[1]);
    const y = [];
    let v = 3;
    for (let o2 = 0; o2 < 4; o2++) {
      if (r2[o2][1] === r2[v][1]) {
        v = o2;
        continue;
      }
      const e4 = n2.create(r2[o2], r2[v]);
      m = Math.min(e4.ymin, m), g = Math.max(e4.ymax, g), void 0 === y[e4.ymin] && (y[e4.ymin] = []), y[e4.ymin].push(e4), v = o2;
    }
    if (null == m || null == g || g - m > 100) return null;
    let _ = [];
    for (c = m; c < g; ) {
      null != y[c] && (_ = _.concat(y[c])), f = 1 / 0, u = -1 / 0;
      for (let e4 = _.length - 1; e4 >= 0; e4--) {
        const t2 = _[e4];
        f = Math.min(f, t2.getLeftCol()), u = Math.max(u, t2.getRightCol());
      }
      if (f = Math.floor(f), u = Math.floor(u), c >= i3.first[1] && c <= i3.last[1]) if (h2) if (i3.size[0] < i3.worldSize[0]) {
        const e4 = Math.floor(u / i3.worldSize[0]);
        for (let t2 = Math.floor(f / i3.worldSize[0]); t2 <= e4; t2++) d.push(new o(c, Math.max(i3.getFirstColumnForWorld(t2), f), Math.min(i3.getLastColumnForWorld(t2), u)));
      } else d.push(new o(c, f, u));
      else f > i3.last[0] || u < i3.first[0] || (f = Math.max(f, i3.first[0]), u = Math.min(u, i3.last[0]), d.push(new o(c, f, u)));
      c += 1;
      for (let e4 = _.length - 1; e4 >= 0; e4--) {
        const t2 = _[e4];
        t2.ymax >= c ? t2.incrRow() : _.splice(e4, 1);
      }
    }
    return a2;
  }
  getTileParentId(e3) {
    i2.set(e3);
    const t = this._infoByLevel[i2.level], o2 = this._lodInfos.indexOf(t) - 1;
    return o2 < 0 ? null : (this._getTileIdAtLOD(i2, this._lodInfos[o2], i2), i2.id);
  }
  getTileResolution(e3) {
    const t = this._infoByLevel["object" == typeof e3 ? e3.level : e3];
    return t ? t.resolution : -1;
  }
  getTileScale(e3) {
    const t = this._infoByLevel[e3.level];
    return t ? t.scale : -1;
  }
  intersects(e3, t) {
    i2.set(t);
    const o2 = this._infoByLevel[i2.level], l3 = e3.lodInfo;
    if (l3.resolution > o2.resolution) {
      this._getTileIdAtLOD(i2, l3, i2);
      const t2 = l3.denormalizeCol(i2.col, i2.world);
      for (const o3 of e3.spans) if (o3.row === i2.row && o3.colFrom <= t2 && o3.colTo >= t2) return true;
    }
    if (l3.resolution < o2.resolution) {
      const [t2, s3, n3, r3] = e3.spans.reduce((e4, t3) => (e4[0] = Math.min(e4[0], t3.row), e4[1] = Math.max(e4[1], t3.row), e4[2] = Math.min(e4[2], t3.colFrom), e4[3] = Math.max(e4[3], t3.colTo), e4), [1 / 0, -1 / 0, 1 / 0, -1 / 0]), a2 = o2.denormalizeCol(i2.col, i2.world), h2 = l3.getColumnForX(o2.getXForColumn(a2)), c = l3.getRowForY(o2.getYForRow(i2.row)), f = l3.getColumnForX(o2.getXForColumn(a2 + 1)) - 1, u = l3.getRowForY(o2.getYForRow(i2.row + 1)) - 1;
      return !(h2 > r3 || f < n3 || c > s3 || u < t2);
    }
    const s2 = l3.denormalizeCol(i2.col, i2.world);
    return e3.spans.some((e4) => e4.row === i2.row && e4.colFrom <= s2 && e4.colTo >= s2);
  }
  normalizeBounds(t, o2, l3) {
    if (t[0] = o2[0], t[1] = o2[1], t[2] = o2[2], t[3] = o2[3], this._wrap) {
      const o3 = R(this.tileInfo.spatialReference), s2 = -l3 * (o3.valid[1] - o3.valid[0]);
      t[0] += s2, t[2] += s2;
    }
    return t;
  }
  getSmallestInfoForScale(e3) {
    const t = this.scales;
    if (this._infoByScale[e3]) return this._infoByScale[e3];
    if (e3 > t[0]) return this._infoByScale[t[0]];
    for (let o2 = 1; o2 < t.length - 1; o2++) if (e3 > t[o2] + a) return this._infoByScale[t[o2 - 1]];
    return this._infoByScale[t[t.length - 1]];
  }
  getClosestInfoForScale(e3) {
    const t = this.scales;
    return this._infoByScale[e3] || (e3 = t.reduce((t2, o2) => Math.abs(o2 - e3) < Math.abs(t2 - e3) ? o2 : t2, t[0])), this._infoByScale[e3];
  }
  scaleToLevel(e3) {
    const t = this.scales;
    if (this._infoByScale[e3]) return this._infoByScale[e3].level;
    for (let o2 = t.length - 1; o2 >= 0; o2--) if (e3 < t[o2]) {
      if (o2 === t.length - 1) return this._infoByScale[t[t.length - 1]].level;
      return this._infoByScale[t[o2]].level + (t[o2] - e3) / (t[o2] - t[o2 + 1]);
    }
    return this._infoByScale[t[0]].level;
  }
  scaleToZoom(e3) {
    return this.tileInfo.scaleToZoom(e3);
  }
  _getTileIdAtLOD(e3, t, o2) {
    const l3 = this._infoByLevel[o2.level];
    return e3.set(o2), t.resolution < l3.resolution ? null : (t.resolution === l3.resolution || (e3.level = t.level, e3.col = Math.floor(o2.col * l3.resolution / t.resolution + 0.01), e3.row = Math.floor(o2.row * l3.resolution / t.resolution + 0.01)), e3);
  }
};

export {
  h
};
//# sourceMappingURL=chunk-MSIU52YL.js.map
