import {
  U
} from "./chunk-AW4AS2UW.js";

// node_modules/@arcgis/core/support/requestPresets.js
async function t(t3, o) {
  const { data: r2 } = await U(t3, { responseType: "json", query: { f: "json", ...o == null ? void 0 : o.customParameters, token: o == null ? void 0 : o.apiKey } });
  return r2;
}

// node_modules/@arcgis/core/layers/support/fetchService.js
async function r(r2, s) {
  const a = await t(r2, s);
  a.layers = a.layers.filter(t2);
  const n = { serviceJSON: a };
  if ((a.currentVersion ?? 0) < 10.5) return n;
  const i = await t(r2 + "/layers", s);
  return n.layersJSON = { layers: i.layers.filter(t2), tables: i.tables }, n;
}
function t2(e) {
  return !e.type || "Feature Layer" === e.type;
}

export {
  t,
  r
};
//# sourceMappingURL=chunk-WIKVTG73.js.map
