import {
  j,
  t
} from "./chunk-VCDD3IVD.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/views/interactive/tooltip/ExtentTooltipInfos.js
var i = class extends t {
  constructor(t2) {
    super(t2), this.type = "extent-rotate", this.angle = 0;
  }
};
e([y()], i.prototype, "type", void 0), e([y()], i.prototype, "angle", void 0), i = e([a("esri.views.interactive.tooltip.ExtentRotateTooltipInfo")], i);
var p = class extends t {
  constructor(t2) {
    super(t2), this.type = "extent-scale", this.xScale = 0, this.yScale = 0, this.xSize = j, this.ySize = j;
  }
};
e([y()], p.prototype, "type", void 0), e([y()], p.prototype, "xScale", void 0), e([y()], p.prototype, "yScale", void 0), e([y()], p.prototype, "xSize", void 0), e([y()], p.prototype, "ySize", void 0), p = e([a("esri.views.interactive.tooltip.ExtentScaleTooltipInfo")], p);

export {
  i,
  p
};
//# sourceMappingURL=chunk-KTKCLSEG.js.map
