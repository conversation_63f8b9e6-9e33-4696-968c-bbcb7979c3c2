{"version": 3, "sources": ["../../@arcgis/core/chunks/RealisticTree.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ForwardLinearDepth as e}from\"../views/3d/webgl-engine/core/shaderLibrary/ForwardLinearDepth.glsl.js\";import{Offset as o}from\"../views/3d/webgl-engine/core/shaderLibrary/Offset.glsl.js\";import{ShaderOutput as r}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as i}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{Transform as a}from\"../views/3d/webgl-engine/core/shaderLibrary/Transform.glsl.js\";import{InstancedDoublePrecision as l}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/InstancedDoublePrecision.glsl.js\";import{NormalAttribute as t}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/NormalAttribute.glsl.js\";import{PositionAttribute as s}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/PositionAttribute.glsl.js\";import{SymbolColor as n}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/SymbolColor.glsl.js\";import{TextureCoordinateAttribute as d}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/TextureCoordinateAttribute.glsl.js\";import{VertexColor as c}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/VertexColor.glsl.js\";import{VerticalOffset as g}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/VerticalOffset.glsl.js\";import{DefaultMaterialAuxiliaryPasses as m}from\"../views/3d/webgl-engine/core/shaderLibrary/default/DefaultMaterialAuxiliaryPasses.glsl.js\";import{EvaluateAmbientOcclusion as v}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/EvaluateAmbientOcclusion.glsl.js\";import{EvaluateSceneLighting as p,addAmbientBoostFactor as u,addLightingGlobalFactor as b}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/EvaluateSceneLighting.glsl.js\";import{addMainLightDirection as h,addMainLightIntensity as w}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MainLighting.glsl.js\";import{multipassTerrainTest as f}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{PhysicallyBasedRendering as x}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/PhysicallyBasedRendering.glsl.js\";import{PhysicallyBasedRenderingParameters as y,PBRMode as C}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/PhysicallyBasedRenderingParameters.glsl.js\";import{ReadShadowMapPass as L,ReadShadowMapDraw as j}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/ReadShadowMap.glsl.js\";import{VisualVariables as M}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/VisualVariables.glsl.js\";import{symbolAlphaCutoff as O}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{DiscardOrAdjustAlphaPass as P}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaDiscard.glsl.js\";import{MixExternalColor as A}from\"../views/3d/webgl-engine/core/shaderLibrary/util/MixExternalColor.glsl.js\";import{addProjViewLocalOrigin as T,addCameraPosition as E}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float3PassUniform as S}from\"../views/3d/webgl-engine/core/shaderModules/Float3PassUniform.js\";import{Float4PassUniform as _}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{FloatPassUniform as F}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as $}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as N}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{Texture2DPassUniform as D}from\"../views/3d/webgl-engine/core/shaderModules/Texture2DPassUniform.js\";import{TransparencyPassType as V}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";import{VertexAttribute as B}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";function I(I){const R=new N,{vertex:z,fragment:k,varyings:G}=R;return T(z,I),R.include(s),G.add(\"vpos\",\"vec3\"),R.include(M,I),R.include(l,I),R.include(g,I),I.output!==r.Color&&I.output!==r.Alpha||(E(R.vertex,I),R.include(t,I),R.include(a,I),I.offsetBackfaces&&R.include(o),I.instancedColor&&R.attributes.add(B.INSTANCECOLOR,\"vec4\"),G.add(\"vNormalWorld\",\"vec3\"),G.add(\"localvpos\",\"vec3\"),I.hasMultipassTerrain&&G.add(\"depth\",\"float\"),R.include(d,I),R.include(e,I),R.include(n,I),R.include(c,I),z.uniforms.add(new _(\"externalColor\",(e=>e.externalColor))),G.add(\"vcolorExt\",\"vec4\"),z.code.add($`\n        void main(void) {\n          forwardNormalizedVertexColor();\n          vcolorExt = externalColor;\n          ${I.instancedColor?\"vcolorExt *= instanceColor;\":\"\"}\n          vcolorExt *= vvColor();\n          vcolorExt *= getSymbolColor();\n          forwardColorMixMode();\n\n          if (vcolorExt.a < ${$.float(O)}) {\n            gl_Position = vec4(1e38, 1e38, 1e38, 1.0);\n          } else {\n            vpos = calculateVPos();\n            localvpos = vpos - view[3].xyz;\n            vpos = subtractOrigin(vpos);\n            vNormalWorld = dpNormal(vvLocalNormal(normalModel()));\n            vpos = addVerticalOffset(vpos, localOrigin);\n            gl_Position = transformPosition(proj, view, vpos);\n            ${I.offsetBackfaces?\"gl_Position = offsetBackfacingClipPosition(gl_Position, vpos, vNormalWorld, cameraPosition);\":\"\"}\n          }\n          ${I.hasMultipassTerrain?$`depth = (view * vec4(vpos, 1.0)).z;`:\"\"}\n          forwardLinearDepth();\n          forwardTextureCoordinates();\n        }\n      `)),I.output===r.Alpha&&(R.include(i,I),R.include(P,I),R.include(f,I),k.uniforms.add([new F(\"opacity\",(e=>e.opacity)),new F(\"layerOpacity\",(e=>e.layerOpacity))]),I.hasColorTexture&&k.uniforms.add(new D(\"tex\",(e=>e.texture))),k.include(A),k.code.add($`\n      void main() {\n        discardBySlice(vpos);\n        ${I.hasMultipassTerrain?$`terrainDepthTest(gl_FragCoord, depth);`:\"\"}\n        ${I.hasColorTexture?$`\n                vec4 texColor = texture2D(tex, ${I.hasColorTextureTransform?$`colorUV`:$`vuv0`});\n                ${I.textureAlphaPremultiplied?\"texColor.rgb /= texColor.a;\":\"\"}\n                discardOrAdjustAlpha(texColor);`:$`vec4 texColor = vec4(1.0);`}\n        ${I.hasVertexColors?$`float opacity_ = layerOpacity * mixExternalOpacity(vColor.a * opacity, texColor.a, vcolorExt.a, int(colorMixMode));`:$`float opacity_ = layerOpacity * mixExternalOpacity(opacity, texColor.a, vcolorExt.a, int(colorMixMode));`}\n\n        gl_FragColor = vec4(opacity_);\n      }\n    `)),I.output===r.Color&&(R.include(i,I),R.include(p,I),R.include(v,I),R.include(P,I),R.include(I.instancedDoublePrecision?L:j,I),R.include(f,I),E(R.fragment,I),h(k),u(k),b(k),k.uniforms.add([z.uniforms.get(\"localOrigin\"),z.uniforms.get(\"view\"),new S(\"ambient\",(e=>e.ambient)),new S(\"diffuse\",(e=>e.diffuse)),new F(\"opacity\",(e=>e.opacity)),new F(\"layerOpacity\",(e=>e.layerOpacity))]),I.hasColorTexture&&k.uniforms.add(new D(\"tex\",(e=>e.texture))),R.include(y,I),R.include(x,I),k.include(A),R.extensions.add(\"GL_OES_standard_derivatives\"),w(k),k.code.add($`\n      void main() {\n        discardBySlice(vpos);\n        ${I.hasMultipassTerrain?$`terrainDepthTest(gl_FragCoord, depth);`:\"\"}\n        ${I.hasColorTexture?$`\n                vec4 texColor = texture2D(tex, ${I.hasColorTextureTransform?$`colorUV`:$`vuv0`});\n                ${I.textureAlphaPremultiplied?\"texColor.rgb /= texColor.a;\":\"\"}\n                discardOrAdjustAlpha(texColor);`:$`vec4 texColor = vec4(1.0);`}\n        vec3 viewDirection = normalize(vpos - cameraPosition);\n        ${I.pbrMode===C.Normal?\"applyPBRFactors();\":\"\"}\n        float ssao = evaluateAmbientOcclusionInverse();\n        ssao *= getBakedOcclusion();\n\n        float additionalAmbientScale = additionalDirectedAmbientLight(vpos + localOrigin);\n        vec3 additionalLight = ssao * mainLightIntensity * additionalAmbientScale * ambientBoostFactor * lightingGlobalFactor;\n        ${I.receiveShadows?\"float shadow = readShadowMap(vpos, linearDepth);\":I.spherical?\"float shadow = lightingGlobalFactor * (1.0 - additionalAmbientScale);\":\"float shadow = 0.0;\"}\n        vec3 matColor = max(ambient, diffuse);\n        ${I.hasVertexColors?$`\n                vec3 albedo = mixExternalColor(vColor.rgb * matColor, texColor.rgb, vcolorExt.rgb, int(colorMixMode));\n                float opacity_ = layerOpacity * mixExternalOpacity(vColor.a * opacity, texColor.a, vcolorExt.a, int(colorMixMode));`:$`\n                vec3 albedo = mixExternalColor(matColor, texColor.rgb, vcolorExt.rgb, int(colorMixMode));\n                float opacity_ = layerOpacity * mixExternalOpacity(opacity, texColor.a, vcolorExt.a, int(colorMixMode));`}\n        ${I.snowCover?$`albedo = mix(albedo, vec3(1), 0.9);`:$``}\n        ${$`\n            vec3 shadingNormal = normalize(vNormalWorld);\n            albedo *= 1.2;\n            vec3 viewForward = vec3(view[0][2], view[1][2], view[2][2]);\n            float alignmentLightView = clamp(dot(viewForward, -mainLightDirection), 0.0, 1.0);\n            float transmittance = 1.0 - clamp(dot(viewForward, shadingNormal), 0.0, 1.0);\n            float treeRadialFalloff = vColor.r;\n            float backLightFactor = 0.5 * treeRadialFalloff * alignmentLightView * transmittance * (1.0 - shadow);\n            additionalLight += backLightFactor * mainLightIntensity;`}\n        ${I.pbrMode===C.Normal||I.pbrMode===C.Schematic?I.spherical?$`vec3 normalGround = normalize(vpos + localOrigin);`:$`vec3 normalGround = vec3(0.0, 0.0, 1.0);`:$``}\n        ${I.pbrMode===C.Normal||I.pbrMode===C.Schematic?$`\n                float additionalAmbientIrradiance = additionalAmbientIrradianceFactor * mainLightIntensity[2];\n                ${I.snowCover?$`\n                        mrr = vec3(0.0, 1.0, 0.04);\n                        emission = vec3(0.0);`:\"\"}\n\n                vec3 shadedColor = evaluateSceneLightingPBR(shadingNormal, albedo, shadow, 1.0 - ssao, additionalLight, viewDirection, normalGround, mrr, emission, additionalAmbientIrradiance);`:$`vec3 shadedColor = evaluateSceneLighting(shadingNormal, albedo, shadow, 1.0 - ssao, additionalLight);`}\n        gl_FragColor = highlightSlice(vec4(shadedColor, opacity_), vpos);\n        ${I.transparencyPassType===V.Color?$`gl_FragColor = premultiplyAlpha(gl_FragColor);`:$``}\n      }\n    `)),R.include(m,I),R}const R=Object.freeze(Object.defineProperty({__proto__:null,build:I},Symbol.toStringTag,{value:\"Module\"}));export{R,I as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIsoH,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAE,EAAC,QAAO,GAAE,UAAS,GAAE,UAAS,EAAC,IAAED;AAAE,SAAO,EAAE,GAAED,EAAC,GAAEC,GAAE,QAAQC,EAAC,GAAE,EAAE,IAAI,QAAO,MAAM,GAAED,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEA,GAAE,WAAS,EAAE,SAAOA,GAAE,WAAS,EAAE,UAAQ,EAAEC,GAAE,QAAOD,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEA,GAAE,mBAAiBC,GAAE,QAAQE,EAAC,GAAEH,GAAE,kBAAgBC,GAAE,WAAW,IAAI,EAAE,eAAc,MAAM,GAAE,EAAE,IAAI,gBAAe,MAAM,GAAE,EAAE,IAAI,aAAY,MAAM,GAAED,GAAE,uBAAqB,EAAE,IAAI,SAAQ,OAAO,GAAEC,GAAE,QAAQC,IAAEF,EAAC,GAAEC,GAAE,QAAQG,IAAEJ,EAAC,GAAEC,GAAE,QAAQI,IAAEL,EAAC,GAAEC,GAAE,QAAQE,IAAEH,EAAC,GAAE,EAAE,SAAS,IAAI,IAAI,EAAE,iBAAiB,CAAAG,OAAGA,GAAE,aAAc,CAAC,GAAE,EAAE,IAAI,aAAY,MAAM,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,YAIxsIH,GAAE,iBAAe,gCAA8B,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,8BAK/B,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAS1BA,GAAE,kBAAgB,iGAA+F,EAAE;AAAA;AAAA,YAErHA,GAAE,sBAAoB,yCAAuC,EAAE;AAAA;AAAA;AAAA;AAAA,OAIpE,IAAGA,GAAE,WAAS,EAAE,UAAQC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQK,IAAEN,EAAC,GAAEC,GAAE,QAAQM,IAAEP,EAAC,GAAE,EAAE,SAAS,IAAI,CAAC,IAAIE,GAAE,WAAW,CAAAC,OAAGA,GAAE,OAAQ,GAAE,IAAID,GAAE,gBAAgB,CAAAC,OAAGA,GAAE,YAAa,CAAC,CAAC,GAAEH,GAAE,mBAAiB,EAAE,SAAS,IAAI,IAAI,EAAE,OAAO,CAAAG,OAAGA,GAAE,OAAQ,CAAC,GAAE,EAAE,QAAQE,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,UAGrPL,GAAE,sBAAoB,4CAA0C,EAAE;AAAA,UAClEA,GAAE,kBAAgB;AAAA,iDACqBA,GAAE,2BAAyB,aAAW,OAAO;AAAA,kBAC5EA,GAAE,4BAA0B,gCAA8B,EAAE;AAAA,mDAC7B,6BAA6B;AAAA,UACpEA,GAAE,kBAAgB,yHAAuH,2GAA2G;AAAA;AAAA;AAAA;AAAA,KAIzP,IAAGA,GAAE,WAAS,EAAE,UAAQC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQO,IAAER,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQK,IAAEN,EAAC,GAAEC,GAAE,QAAQD,GAAE,2BAAyBS,KAAEC,IAAEV,EAAC,GAAEC,GAAE,QAAQM,IAAEP,EAAC,GAAE,EAAEC,GAAE,UAASD,EAAC,GAAEE,GAAE,CAAC,GAAEO,GAAE,CAAC,GAAEE,GAAE,CAAC,GAAE,EAAE,SAAS,IAAI,CAAC,EAAE,SAAS,IAAI,aAAa,GAAE,EAAE,SAAS,IAAI,MAAM,GAAE,IAAIR,GAAE,WAAW,CAAAA,OAAGA,GAAE,OAAQ,GAAE,IAAIA,GAAE,WAAW,CAAAA,OAAGA,GAAE,OAAQ,GAAE,IAAID,GAAE,WAAW,CAAAC,OAAGA,GAAE,OAAQ,GAAE,IAAID,GAAE,gBAAgB,CAAAC,OAAGA,GAAE,YAAa,CAAC,CAAC,GAAEH,GAAE,mBAAiB,EAAE,SAAS,IAAI,IAAI,EAAE,OAAO,CAAAG,OAAGA,GAAE,OAAQ,CAAC,GAAEF,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQM,IAAEP,EAAC,GAAE,EAAE,QAAQK,EAAC,GAAEJ,GAAE,WAAW,IAAI,6BAA6B,GAAEW,GAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,UAGpiBZ,GAAE,sBAAoB,4CAA0C,EAAE;AAAA,UAClEA,GAAE,kBAAgB;AAAA,iDACqBA,GAAE,2BAAyB,aAAW,OAAO;AAAA,kBAC5EA,GAAE,4BAA0B,gCAA8B,EAAE;AAAA,mDAC7B,6BAA6B;AAAA;AAAA,UAEpEA,GAAE,YAAU,EAAE,SAAO,uBAAqB,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAM5CA,GAAE,iBAAe,qDAAmDA,GAAE,YAAU,0EAAwE,qBAAqB;AAAA;AAAA,UAE7KA,GAAE,kBAAgB;AAAA;AAAA,uIAEyG;AAAA;AAAA,yHAEZ;AAAA,UAC/GA,GAAE,YAAU,yCAAuC,GAAG;AAAA,UACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qEAQ2D;AAAA,UAC3DA,GAAE,YAAU,EAAE,UAAQA,GAAE,YAAU,EAAE,YAAUA,GAAE,YAAU,wDAAsD,8CAA4C,GAAG;AAAA,UAC/JA,GAAE,YAAU,EAAE,UAAQA,GAAE,YAAU,EAAE,YAAU;AAAA;AAAA,kBAEtCA,GAAE,YAAU;AAAA;AAAA,iDAEiB,EAAE;AAAA;AAAA,qMAEkJ,wGAAwG;AAAA;AAAA,UAEjSA,GAAE,yBAAuBE,GAAE,QAAM,oDAAkD,GAAG;AAAA;AAAA,KAE3F,IAAGD,GAAE,QAAQ,GAAED,EAAC,GAAEC;AAAC;AAAC,IAAM,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAM,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["I", "R", "o", "e", "d", "i", "s", "n", "p", "h", "v", "u", "a"]}