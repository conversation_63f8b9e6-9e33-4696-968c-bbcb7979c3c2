{"version": 3, "sources": ["../../@arcgis/core/core/support/WatchUpdatingTracking.js", "../../@arcgis/core/core/HandleOwner.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import s from\"../Accessor.js\";import t from\"../Handles.js\";import{isNone as i}from\"../maybe.js\";import{watch as n,when as d,on as a,sync as r}from\"../reactiveUtils.js\";import{schedule as h}from\"../scheduling.js\";import{property as o}from\"../accessorSupport/decorators/property.js\";import{subclass as l}from\"../accessorSupport/decorators/subclass.js\";let c=class extends s{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new t,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,s,t={}){return this._installWatch(e,s,t,n)}addWhen(e,s,t={}){return this._installWatch(e,s,t,d)}addOnCollectionChange(e,s,{initial:t=!1,final:i=!1}={}){const n=++this._handleId;return this._handles.add([a(e,\"after-changes\",this._createSyncUpdatingCallback(),r),a(e,\"change\",s,{onListenerAdd:t?e=>s({added:e.toArray(),removed:[]}):void 0,onListenerRemove:i?e=>s({added:[],removed:e.toArray()}):void 0})],n),{remove:()=>this._handles.remove(n)}}addPromise(e){if(i(e))return e;const s=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(_)||this._set(\"updating\",!1))}},s),this._pendingPromises.add(e),this._set(\"updating\",!0);const t=()=>this._handles.remove(s);return e.then(t,t),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set(\"updating\",!1)}_installWatch(e,s,t={},i){const n=++this._handleId;t.sync||this._installSyncUpdatingWatch(e,n);const d=i(e,s,t);return this._handles.add(d,n),{remove:()=>this._handles.remove(n)}}_installSyncUpdatingWatch(e,s){const t=this._createSyncUpdatingCallback(),i=n(e,t,{sync:!0,equals:()=>!1});return this._handles.add(i,s),i}_createSyncUpdatingCallback(){return()=>{this._handles.remove(_),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get(\"updating\")||this._set(\"updating\",!0),this._handles.add(h((()=>{e===this._scheduleHandleId&&(this._set(\"updating\",this._pendingPromises.size>0),this._handles.remove(_))})),_)}}};e([o({readOnly:!0})],c.prototype,\"updating\",void 0),c=e([l(\"esri.core.support.WatchUpdatingTracking\")],c);const _=-42;export{c as WatchUpdatingTracking};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import s from\"./Accessor.js\";import t from\"./Handles.js\";import{property as r}from\"./accessorSupport/decorators/property.js\";import{subclass as o}from\"./accessorSupport/decorators/subclass.js\";import{WatchUpdatingTracking as n}from\"./support/WatchUpdatingTracking.js\";const a=s=>{let a=class extends s{destroy(){this.destroyed||(this._get(\"handles\")?.destroy(),this._get(\"updatingHandles\")?.destroy())}get handles(){return this._get(\"handles\")||new t}get updatingHandles(){return this._get(\"updatingHandles\")||new n}};return e([r({readOnly:!0})],a.prototype,\"handles\",null),e([r({readOnly:!0})],a.prototype,\"updatingHandles\",null),a=e([o(\"esri.core.HandleOwner\")],a),a};let d=class extends(a(s)){};d=e([o(\"esri.core.HandleOwner\")],d);export{d as HandleOwner,a as HandleOwnerMixin};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAI4Y,IAAI,IAAE,cAAcA,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,WAAS,OAAG,KAAK,YAAU,GAAE,KAAK,WAAS,IAAIC,MAAE,KAAK,oBAAkB,GAAE,KAAK,mBAAiB,oBAAI;AAAA,EAAG;AAAA,EAAC,UAAS;AAAC,SAAK,UAAU,GAAE,KAAK,SAAS,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAIC,IAAE,GAAED,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,cAAcC,IAAE,GAAED,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQC,IAAE,GAAED,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,cAAcC,IAAE,GAAED,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBC,IAAE,GAAE,EAAC,SAAQD,KAAE,OAAG,OAAM,IAAE,MAAE,IAAE,CAAC,GAAE;AAAC,UAAM,IAAE,EAAE,KAAK;AAAU,WAAO,KAAK,SAAS,IAAI,CAACE,GAAED,IAAE,iBAAgB,KAAK,4BAA4B,GAAE,CAAC,GAAEC,GAAED,IAAE,UAAS,GAAE,EAAC,eAAcD,KAAE,CAAAC,OAAG,EAAE,EAAC,OAAMA,GAAE,QAAQ,GAAE,SAAQ,CAAC,EAAC,CAAC,IAAE,QAAO,kBAAiB,IAAE,CAAAA,OAAG,EAAE,EAAC,OAAM,CAAC,GAAE,SAAQA,GAAE,QAAQ,EAAC,CAAC,IAAE,OAAM,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,QAAO,MAAI,KAAK,SAAS,OAAO,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,QAAG,EAAEA,EAAC,EAAE,QAAOA;AAAE,UAAM,IAAE,EAAE,KAAK;AAAU,SAAK,SAAS,IAAI,EAAC,QAAO,MAAI;AAAC,WAAK,iBAAiB,OAAOA,EAAC,MAAI,MAAI,KAAK,iBAAiB,QAAM,KAAK,SAAS,IAAI,CAAC,KAAG,KAAK,KAAK,YAAW,KAAE;AAAA,IAAE,EAAC,GAAE,CAAC,GAAE,KAAK,iBAAiB,IAAIA,EAAC,GAAE,KAAK,KAAK,YAAW,IAAE;AAAE,UAAMD,KAAE,MAAI,KAAK,SAAS,OAAO,CAAC;AAAE,WAAOC,GAAE,KAAKD,IAAEA,EAAC,GAAEC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,SAAK,iBAAiB,MAAM,GAAE,KAAK,SAAS,UAAU,GAAE,KAAK,KAAK,YAAW,KAAE;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE,GAAED,KAAE,CAAC,GAAE,GAAE;AAAC,UAAM,IAAE,EAAE,KAAK;AAAU,IAAAA,GAAE,QAAM,KAAK,0BAA0BC,IAAE,CAAC;AAAE,UAAME,KAAE,EAAEF,IAAE,GAAED,EAAC;AAAE,WAAO,KAAK,SAAS,IAAIG,IAAE,CAAC,GAAE,EAAC,QAAO,MAAI,KAAK,SAAS,OAAO,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BF,IAAE,GAAE;AAAC,UAAMD,KAAE,KAAK,4BAA4B,GAAE,IAAE,EAAEC,IAAED,IAAE,EAAC,MAAK,MAAG,QAAO,MAAI,MAAE,CAAC;AAAE,WAAO,KAAK,SAAS,IAAI,GAAE,CAAC,GAAE;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAC,WAAM,MAAI;AAAC,WAAK,SAAS,OAAO,CAAC,GAAE,EAAE,KAAK;AAAkB,YAAMC,KAAE,KAAK;AAAkB,WAAK,KAAK,UAAU,KAAG,KAAK,KAAK,YAAW,IAAE,GAAE,KAAK,SAAS,IAAI,EAAG,MAAI;AAAC,QAAAA,OAAI,KAAK,sBAAoB,KAAK,KAAK,YAAW,KAAK,iBAAiB,OAAK,CAAC,GAAE,KAAK,SAAS,OAAO,CAAC;AAAA,MAAE,CAAE,GAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,yCAAyC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACA72D,IAAMG,KAAE,OAAG;AAAC,MAAIA,KAAE,cAAc,EAAC;AAAA,IAAC,UAAS;AAJlW;AAImW,WAAK,eAAY,UAAK,KAAK,SAAS,MAAnB,mBAAsB,YAAU,UAAK,KAAK,iBAAiB,MAA3B,mBAA8B;AAAA,IAAU;AAAA,IAAC,IAAI,UAAS;AAAC,aAAO,KAAK,KAAK,SAAS,KAAG,IAAIC;AAAA,IAAC;AAAA,IAAC,IAAI,kBAAiB;AAAC,aAAO,KAAK,KAAK,iBAAiB,KAAG,IAAI;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAED,GAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,GAAEA,EAAC,GAAEA;AAAC;AAAE,IAAI,IAAE,cAAcA,GAAEE,EAAC,EAAE;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,GAAE,CAAC;", "names": ["v", "t", "e", "a", "d", "a", "t", "v"]}