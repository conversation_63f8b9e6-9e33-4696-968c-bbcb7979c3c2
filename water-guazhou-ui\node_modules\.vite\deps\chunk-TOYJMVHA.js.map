{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderModules/Float4sPassUniform.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderModules/FloatsPassUniform.js", "../../@arcgis/core/views/3d/webgl-engine/lib/AnimationTimer.js", "../../@arcgis/core/views/3d/webgl-engine/lib/ContentObject.js", "../../@arcgis/core/views/3d/webgl-engine/lib/ContentObjectType.js", "../../@arcgis/core/views/3d/webgl-engine/lib/DefaultVertexAttributeLocations.js", "../../@arcgis/core/views/3d/webgl-engine/lib/screenSizePerspectiveUtils.js", "../../@arcgis/core/views/3d/webgl-engine/materials/internal/MaterialUtil.js", "../../@arcgis/core/views/3d/webgl-engine/lib/Material.js", "../../@arcgis/core/views/3d/webgl-engine/materials/VisualVariablePassParameters.js", "../../@arcgis/core/views/3d/webgl-engine/lib/DefaultVertexBufferLayouts.js", "../../@arcgis/core/views/3d/webgl-engine/lib/VertexArrayObject.js", "../../@arcgis/core/views/3d/webgl-engine/lib/glUtil3D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Uniform as r}from\"./Uniform.js\";import{BindType as s}from\"../shaderTechnique/BindType.js\";class e extends r{constructor(r,e,o){super(r,\"vec4\",s.Pass,((s,o,t)=>s.setUniform4fv(r,e(o,t))),o)}}export{e as Float4sPassUniform};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Uniform as r}from\"./Uniform.js\";import{BindType as s}from\"../shaderTechnique/BindType.js\";class o extends r{constructor(r,o,e){super(r,\"float\",s.Pass,((s,e,t)=>s.setUniform1fv(r,o(e,t))),e)}}export{o as FloatsPassUniform};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../../core/maybe.js\";import{Milliseconds as e}from\"../../../../core/time.js\";class i{constructor(){this.enabled=!0,this._time=e(0)}get time(){return this._time}advance({deltaTime:i,fixedTime:m}){return t(m)?this._time!==m&&(this._time=m,!0):(this._time=e(this._time+i),0!==i)}}class m{constructor(t,e){this.deltaTime=t,this.fixedTime=e}}export{i as AnimationTimer,m as Parameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{generateUID as o}from\"../../../../core/uid.js\";class r{constructor(){this.id=o()}unload(){}}export{r as ContentObject};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar e;!function(e){e[e.Layer=0]=\"Layer\",e[e.Object=1]=\"Object\",e[e.Mesh=2]=\"Mesh\",e[e.Line=3]=\"Line\",e[e.Point=4]=\"Point\",e[e.Material=5]=\"Material\",e[e.Texture=6]=\"Texture\",e[e.COUNT=7]=\"COUNT\"}(e||(e={}));export{e as ContentObjectType};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{VertexAttribute as O}from\"./VertexAttribute.js\";const E=new Map([[<PERSON><PERSON>POSITION,0],[O.NORMAL,1],[O.UV0,2],[O.COLOR,3],[O.SIZE,4],[O.TANGENT,4],[O.AUXPOS1,5],[<PERSON><PERSON>SYMBOLCOLOR,5],[O.AUXPOS2,6],[O<PERSON>FEATUREATTRIBUTE,6],[<PERSON><PERSON>INSTANCEFEATUREATTRIBUTE,6],[<PERSON><PERSON>INSTANCECOLOR,7],[<PERSON><PERSON>LAYERIDCOLOR,7],[O<PERSON>OBJECTANDLAYERIDCOLOR_INSTANCED,7],[<PERSON><PERSON>MODEL,8],[<PERSON><PERSON><PERSON>ELNORMA<PERSON>,12],[<PERSON><PERSON><PERSON><PERSON>I,11],[<PERSON><PERSON><PERSON>ORIGINLO,15]]);export{E as Default3D};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{lerp as e,deg2rad as t,clamp as a}from\"../../../../core/mathUtils.js\";import{ViewingMode as i}from\"../../../ViewingMode.js\";function r(e,t){return new p(e,h,t)}function n(e,t){const{curvatureDependent:a,scaleStart:i,scaleFallOffRange:r}=h;return new p(e,{curvatureDependent:{min:{curvature:a.min.curvature,tiltAngle:a.min.tiltAngle,scaleFallOffFactor:v.curvatureDependent.min.scaleFallOffFactor},max:{curvature:a.max.curvature,tiltAngle:a.max.tiltAngle,scaleFallOffFactor:v.curvatureDependent.max.scaleFallOffFactor}},scaleStart:i,scaleFallOffRange:r,minPixelSize:v.minPixelSize},t)}function l(e){return Math.abs(e*e*e)}function s(e,t,a){const i=a.parameters,r=a.paddingPixelsOverride;return x.scale=Math.min(i.divisor/(t-i.offset),1),x.factor=l(e),x.minPixelSize=i.minPixelSize,x.paddingPixels=r,x}function c(e,t){return 0===e?t.minPixelSize:t.minPixelSize*(1+2*t.paddingPixels/e)}function o(t,a){return Math.max(e(t*a.scale,t,a.factor),c(t,a))}function u(e,t,a){const i=s(e,t,a);return i.minPixelSize=0,i.paddingPixels=0,o(1,i)}function d(e,t,a,i){i.scale=u(e,t,a),i.factor=0,i.minPixelSize=a.parameters.minPixelSize,i.paddingPixels=a.paddingPixelsOverride}function f(e,t,a=[0,0]){const i=Math.min(Math.max(t.scale,c(e[1],t)/Math.max(1e-5,e[1])),1);return a[0]=e[0]*i,a[1]=e[1]*i,a}function m(e,t,a,i){return o(e,s(t,a,i))}class p{get paddingPixelsOverride(){return this._paddingPixelsOverride||this.parameters.paddingPixels}constructor(e,t,a,r=g(),n){this._viewingMode=e,this._description=t,this._ellipsoidRadius=a,this.parameters=r,this._paddingPixelsOverride=n,this._viewingMode===i.Local?(this._coverageCompensation=this._surfaceCoverageCompensationLocal,this._calculateCurvatureDependentParameters=this._calculateCurvatureDependentParametersLocal):(this._coverageCompensation=this._surfaceCoverageCompensationGlobal,this._calculateCurvatureDependentParameters=this._calculateCurvatureDependentParametersGlobal)}update(e){return(!this.parameters||this.parameters.camera.fovY!==e.fovY||this.parameters.camera.distance!==e.distance)&&(this._calculateParameters(e,this._ellipsoidRadius,this.parameters),!0)}overridePadding(e){return e!==this.paddingPixelsOverride?new p(this._viewingMode,this._description,this._ellipsoidRadius,this.parameters,e):this}_calculateParameters(e,t,a){const{scaleStart:i,scaleFallOffRange:r,minPixelSize:n}=this._description,{fovY:l,distance:s}=e,c=this._calculateCurvatureDependentParameters(e,t),o=this._coverageCompensation(e,t,c),{tiltAngle:u,scaleFallOffFactor:d}=c,f=Math.sin(u)*s,m=.5*Math.PI-u-l*(.5-i*o),p=f/Math.cos(m),h=m+l*r*o,v=(p-d*(f/Math.cos(h)))/(1-d);return a.camera.fovY=e.fovY,a.camera.distance=e.distance,a.offset=v,a.divisor=p-v,a.minPixelSize=n,a}_calculateCurvatureDependentParametersLocal(e,t,a=P){return a.tiltAngle=this._description.curvatureDependent.min.tiltAngle,a.scaleFallOffFactor=this._description.curvatureDependent.min.scaleFallOffFactor,a}_calculateCurvatureDependentParametersGlobal(t,i,r=P){const n=this._description.curvatureDependent,l=1+t.distance/i,s=Math.sqrt(l*l-1),[c,o]=[n.min.curvature,n.max.curvature],u=a((s-c)/(o-c),0,1),[d,f]=[n.min,n.max];return r.tiltAngle=e(d.tiltAngle,f.tiltAngle,u),r.scaleFallOffFactor=e(d.scaleFallOffFactor,f.scaleFallOffFactor,u),r}_surfaceCoverageCompensationLocal(e,t,a){return(e.fovY-a.tiltAngle)/e.fovY}_surfaceCoverageCompensationGlobal(e,t,a){const i=t*t,r=a.tiltAngle+.5*Math.PI,{fovY:n,distance:l}=e,s=l*l+i-2*Math.cos(r)*l*t,c=Math.sqrt(s),o=Math.sqrt(s-i);return(Math.acos(o/c)-Math.asin(t/(c/Math.sin(r)))+.5*n)/n}}const h={curvatureDependent:{min:{curvature:t(10),tiltAngle:t(12),scaleFallOffFactor:.5},max:{curvature:t(70),tiltAngle:t(40),scaleFallOffFactor:.8}},scaleStart:.3,scaleFallOffRange:.65,minPixelSize:0},v={curvatureDependent:{min:{scaleFallOffFactor:.7},max:{scaleFallOffFactor:.95}},minPixelSize:14};function g(){return{camera:{distance:0,fovY:0},divisor:0,offset:0,minPixelSize:0,paddingPixels:0}}const x={scale:0,factor:0,minPixelSize:0,paddingPixels:0},P={tiltAngle:0,scaleFallOffFactor:0};export{f as applyPrecomputedScaleFactor,o as applyScaleFactor,n as getLabelSettings,r as getSettings,d as precomputeScaleFactor,m as scale};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{update as t}from\"../../../../../core/arrayUtils.js\";import{clamp as n}from\"../../../../../core/mathUtils.js\";import{isNone as e,isSome as o}from\"../../../../../core/maybe.js\";import{s as i,f as r,n as s}from\"../../../../../chunks/vec3.js\";import{c}from\"../../../../../chunks/vec3f64.js\";import{create as f,setMin as l,setMax as a}from\"../../../../../geometry/support/aaBoundingBox.js\";import{ContentObjectType as u}from\"../../lib/ContentObjectType.js\";import{scale as m}from\"../../lib/screenSizePerspectiveUtils.js\";import{assert as p}from\"../../lib/Util.js\";import{VertexAttribute as h}from\"../../lib/VertexAttribute.js\";const b=f();function x(t,n,e,o,i,r){if(t.visible)if(t.boundingInfo){p(t.type===u.Mesh);const s=n.tolerance;g(t.boundingInfo,e,o,s,i,r)}else{const n=t.indices.get(h.POSITION),s=t.vertexAttributes.get(h.POSITION);y(e,o,0,n.length/3,n,s,void 0,i,r)}}const d=c();function g(t,n,i,r,s,c){if(e(t))return;const f=O(n,i,d);if(l(b,t.bbMin),a(b,t.bbMax),o(s)&&s.applyToAabb(b),V(b,n,f,r)){const{primitiveIndices:e,indices:o,position:f}=t,l=e?e.length:o.length/3;if(l>U){const e=t.getChildren();if(void 0!==e){for(const t of e)g(t,n,i,r,s,c);return}}y(n,i,0,l,o,f,e,s,c)}}const M=c();function y(t,n,e,i,r,s,c,f,l){if(c)return j(t,n,e,i,r,s,c,f,l);const{data:a,stride:u}=s,m=t[0],p=t[1],h=t[2],b=n[0]-m,x=n[1]-p,d=n[2]-h;for(let g=e,y=3*e;g<i;++g){let t=u*r[y++],n=a[t++],e=a[t++],i=a[t];t=u*r[y++];let s=a[t++],c=a[t++],j=a[t];t=u*r[y++];let v=a[t++],T=a[t++],O=a[t];o(f)&&([n,e,i]=f.applyToVertex(n,e,i,g),[s,c,j]=f.applyToVertex(s,c,j,g),[v,T,O]=f.applyToVertex(v,T,O,g));const V=s-n,L=c-e,N=j-i,A=v-n,P=T-e,S=O-i,E=x*S-P*d,U=d*A-S*b,W=b*P-A*x,k=V*E+L*U+N*W;if(Math.abs(k)<=Number.EPSILON)continue;const B=m-n,C=p-e,z=h-i,H=B*E+C*U+z*W;if(k>0){if(H<0||H>k)continue}else if(H>0||H<k)continue;const R=C*N-L*z,Y=z*V-N*B,q=B*L-V*C,w=b*R+x*Y+d*q;if(k>0){if(w<0||H+w>k)continue}else if(w>0||H+w<k)continue;const D=(A*R+P*Y+S*q)/k;if(D>=0){l(D,I(V,L,N,A,P,S,M),g,!1)}}}function j(t,n,e,i,r,s,c,f,l){const{data:a,stride:u}=s,m=t[0],p=t[1],h=t[2],b=n[0]-m,x=n[1]-p,d=n[2]-h;for(let g=e;g<i;++g){const t=c[g];let n=3*t,e=u*r[n++],i=a[e++],s=a[e++],y=a[e];e=u*r[n++];let j=a[e++],v=a[e++],T=a[e];e=u*r[n];let O=a[e++],V=a[e++],L=a[e];o(f)&&([i,s,y]=f.applyToVertex(i,s,y,g),[j,v,T]=f.applyToVertex(j,v,T,g),[O,V,L]=f.applyToVertex(O,V,L,g));const N=j-i,A=v-s,P=T-y,S=O-i,E=V-s,U=L-y,W=x*U-E*d,k=d*S-U*b,B=b*E-S*x,C=N*W+A*k+P*B;if(Math.abs(C)<=Number.EPSILON)continue;const z=m-i,H=p-s,R=h-y,Y=z*W+H*k+R*B;if(C>0){if(Y<0||Y>C)continue}else if(Y>0||Y<C)continue;const q=H*P-A*R,w=R*N-P*z,D=z*A-N*H,F=b*q+x*w+d*D;if(C>0){if(F<0||Y+F>C)continue}else if(F>0||Y+F<C)continue;const G=(S*q+E*w+U*D)/C;if(G>=0){l(G,I(N,A,P,S,E,U,M),t,!1)}}}const v=c(),T=c();function I(t,n,e,o,c,f,l){return i(v,t,n,e),i(T,o,c,f),r(l,v,T),s(l,l),l}function O(t,n,e){return i(e,1/(n[0]-t[0]),1/(n[1]-t[1]),1/(n[2]-t[2]))}function V(t,n,e,o){return L(t,n,e,o,1/0)}function L(t,n,e,o,i){const r=(t[0]-o-n[0])*e[0],s=(t[3]+o-n[0])*e[0];let c=Math.min(r,s),f=Math.max(r,s);const l=(t[1]-o-n[1])*e[1],a=(t[4]+o-n[1])*e[1];if(f=Math.min(f,Math.max(l,a)),f<0)return!1;if(c=Math.max(c,Math.min(l,a)),c>f)return!1;const u=(t[2]-o-n[2])*e[2],m=(t[5]+o-n[2])*e[2];return f=Math.min(f,Math.max(u,m)),!(f<0)&&(c=Math.max(c,Math.min(u,m)),!(c>f)&&c<i)}function N(t,e,i,r,s){let c=(i.screenLength||0)*t.pixelRatio;o(s)&&(c=m(c,r,e,s));const f=c*Math.tan(.5*t.fovY)/(.5*t.fullHeight);return n(f*e,i.minWorldLength||0,null!=i.maxWorldLength?i.maxWorldLength:1/0)}function A(t,n){const e=n?A(n):{};for(const o in t){let n=t[o];n&&n.forEach&&(n=S(n)),null==n&&o in e||(e[o]=n)}return e}function P(n,e){let o=!1;for(const i in e){const r=e[i];void 0!==r&&(Array.isArray(r)?null===n[i]?(n[i]=r.slice(),o=!0):t(n[i],r)&&(o=!0):n[i]!==r&&(o=!0,n[i]=r))}return o}function S(t){const n=[];return t.forEach((t=>n.push(t))),n}const E={multiply:1,ignore:2,replace:3,tint:4},U=1e3;export{E as colorMixModes,O as computeInvDir,I as computeNormal,A as copyParameters,V as intersectAabbInvDir,L as intersectAabbInvDirBefore,x as intersectTriangleGeometry,y as intersectTriangles,P as updateParameters,N as verticalOffsetAtDistance};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../core/maybe.js\";import{f as r}from\"../../../../chunks/vec3f64.js\";import{NoParameters as t}from\"../core/shaderModules/interfaces.js\";import{Parameters as s}from\"./AnimationTimer.js\";import{ContentObject as i}from\"./ContentObject.js\";import{ContentObjectType as a}from\"./ContentObjectType.js\";import{Default3D as n}from\"./DefaultVertexAttributeLocations.js\";import{copyParameters as p,updateParameters as d}from\"../materials/internal/MaterialUtil.js\";class h extends i{constructor(e,t){super(),this.type=a.Material,this.supportsEdges=!1,this._visible=!0,this._renderPriority=0,this._insertOrder=0,this._vertexAttributeLocations=n,this._pp0=r(0,0,1),this._pp1=r(0,0,0),this._parameters=p(e,t),this.validateParameters(this._parameters)}dispose(){}get parameters(){return this._parameters}update(e){return!1}setParameters(e,r=!0){d(this._parameters,e)&&(this.validateParameters(this._parameters),r&&this.parametersChanged())}validateParameters(e){}get visible(){return this._visible}set visible(e){e!==this._visible&&(this._visible=e,this.parametersChanged())}shouldRender(e){return this.isVisible()&&this.isVisibleForOutput(e.output)&&0!=(this.renderOccluded&e.renderOccludedMask)}isVisibleForOutput(e){return!0}get renderOccluded(){return this.parameters.renderOccluded}get renderPriority(){return this._renderPriority}set renderPriority(e){e!==this._renderPriority&&(this._renderPriority=e,this.parametersChanged())}get insertOrder(){return this._insertOrder}set insertOrder(e){e!==this._insertOrder&&(this._insertOrder=e,this.parametersChanged())}get vertexAttributeLocations(){return this._vertexAttributeLocations}isVisible(){return this._visible}parametersChanged(){e(this.repository)&&this.repository.materialChanged(this)}intersectDraped(e,r,t,s,i,a){return this._pp0[0]=this._pp1[0]=s[0],this._pp0[1]=this._pp1[1]=s[1],this.intersect(e,r,t,this._pp0,this._pp1,i)}}class o extends s{constructor(e,r,t){super(r,t),this.camera=e}}var c;!function(e){e[e.Occlude=1]=\"Occlude\",e[e.Transparent=2]=\"Transparent\",e[e.OccludeAndTransparent=4]=\"OccludeAndTransparent\",e[e.OccludeAndTransparentStencil=8]=\"OccludeAndTransparentStencil\",e[e.Opaque=16]=\"Opaque\"}(c||(c={}));class u extends t{constructor(){super(...arguments),this.renderOccluded=c.Occlude}}export{h as Material,u as MaterialParameters,c as RenderOccludedFlag,o as UpdateParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as i}from\"../../../../chunks/mat3f64.js\";import{f as t}from\"../../../../chunks/vec3f64.js\";import{MaterialParameters as s}from\"../lib/Material.js\";class v extends s{constructor(){super(...arguments),this.vvSizeEnabled=!1,this.vvSizeMinSize=t(1,1,1),this.vvSizeMaxSize=t(100,100,100),this.vvSizeOffset=t(0,0,0),this.vvSizeFactor=t(1,1,1),this.vvSizeValue=t(1,1,1),this.vvColorEnabled=!1,this.vvColorValues=[0,0,0,0,0,0,0,0],this.vvColorColors=[1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0],this.vvOpacityEnabled=!1,this.vvOpacityValues=[0,0,0,0,0,0,0,0],this.vvOpacityOpacities=[1,1,1,1,1,1,1,1],this.vvSymbolAnchor=[0,0,0],this.vvSymbolRotationMatrix=i()}}const o=8;export{v as VisualVariablePassParameters,o as vvColorNumber};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{VertexAttribute as O}from\"./VertexAttribute.js\";import{DataType as e}from\"../../../webgl/enums.js\";import{VertexElementDescriptor as T}from\"../../../webgl/VertexElementDescriptor.js\";const n=[new T(O.POSITION,3,e.FLOAT,0,12)],w=[new T(O.POSITION,3,e.FLOAT,0,20),new T(O.UV0,2,e.FLOAT,12,20)],I=[new T(O.POSITION,3,e.FLOAT,0,32),new T(O.NORMAL,3,e.FLOAT,12,32),new T(O.UV0,2,e.FLOAT,24,32)],r=[new T(<PERSON><PERSON>POSITION,3,e.FLOAT,0,16),new T(<PERSON><PERSON>COLOR,4,e.UNSIGNED_BYTE,12,16)],t=[new T(O.POSITION,2,e.FLOAT,0,8)],A=[new T(O.POSITION,2,e.FLOAT,0,16),new T(O.UV0,2,e.FLOAT,8,16)];export{t as Pos2,A as Pos2Tex,n as Pos3,r as Pos3Col,I as Pos3NormalTex,w as Pos3Tex};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{VertexArrayObject as e}from\"../../../webgl/VertexArrayObject.js\";class r extends e{}export{r as VertexArrayObject};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Default3D as e}from\"./DefaultVertexAttributeLocations.js\";import{Pos2 as t,Pos2Tex as r}from\"./DefaultVertexBufferLayouts.js\";import{VertexArrayObject as o}from\"./VertexArrayObject.js\";import{BufferObject as n}from\"../../../webgl/BufferObject.js\";import{Usage as a,TextureType as i,PixelFormat as m,PixelType as T,TextureSamplingMode as l}from\"../../../webgl/enums.js\";import{Texture as u}from\"../../../webgl/Texture.js\";function f(i,m=t,T=e,l=-1,u=1){let f=null;if(m===r)f=new Float32Array([l,l,0,0,u,l,1,0,l,u,0,1,u,u,1,1]);else f=new Float32Array([l,l,u,l,l,u,u,u]);return new o(i,T,{geometry:m},{geometry:n.createVertex(i,a.STATIC_DRAW,f)})}function E(r,i=t,m=e){const T=new Float32Array([-1,-1,3,-1,-1,3]);return new o(r,m,{geometry:i},{geometry:n.createVertex(r,a.STATIC_DRAW,T)})}const g=4;function s(e,t=g){return new u(e,{target:i.TEXTURE_2D,pixelFormat:m.RGBA,dataType:T.UNSIGNED_BYTE,samplingMode:l.NEAREST,width:t,height:t})}function A(e,t,r=g){const o=new Uint8Array(r*r*4);for(let n=0;n<o.length;n+=4)o[n+0]=255*t[0],o[n+1]=255*t[1],o[n+2]=255*t[2],o[n+3]=255*t[3];return new u(e,{target:i.TEXTURE_2D,pixelFormat:m.RGBA,dataType:T.UNSIGNED_BYTE,samplingMode:l.NEAREST,width:r,height:r},o)}function p(e){return new u(e,{target:i.TEXTURE_2D,pixelFormat:m.RGBA,dataType:T.UNSIGNED_BYTE,samplingMode:l.NEAREST,width:1,height:1},new Uint8Array([255,255,255,255]))}export{A as createColorTexture,p as createEmptyDepthTexture,s as createEmptyTexture,f as createQuadVAO,E as createScreenSizeTriangleVAO};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIiG,IAAMA,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAED,IAAEE,IAAE;AAAC,UAAMD,IAAE,QAAOE,GAAE,MAAM,CAACC,IAAEF,IAAEG,OAAID,GAAE,cAAcH,IAAED,GAAEE,IAAEG,EAAC,CAAC,GAAGH,EAAC;AAAA,EAAC;AAAC;;;ACAnG,IAAMI,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAED,IAAEE,IAAE;AAAC,UAAMD,IAAE,SAAQE,GAAE,MAAM,CAACC,IAAEF,IAAEG,OAAID,GAAE,cAAcH,IAAED,GAAEE,IAAEG,EAAC,CAAC,GAAGH,EAAC;AAAA,EAAC;AAAC;;;ACA1F,IAAMI,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,UAAQ,MAAG,KAAK,QAAM,EAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,QAAQ,EAAC,WAAUA,IAAE,WAAUC,GAAC,GAAE;AAAC,WAAO,EAAEA,EAAC,IAAE,KAAK,UAAQA,OAAI,KAAK,QAAMA,IAAE,SAAK,KAAK,QAAM,EAAE,KAAK,QAAMD,EAAC,GAAE,MAAIA;AAAA,EAAE;AAAC;;;ACA5P,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,KAAG,EAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAC;;;ACAlG,IAAIC;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,QAAM,CAAC,IAAE;AAAO,EAAEA,OAAIA,KAAE,CAAC,EAAE;;;ACAtJ,IAAMC,KAAE,oBAAI,IAAI,CAAC,CAACC,GAAE,UAAS,CAAC,GAAE,CAACA,GAAE,QAAO,CAAC,GAAE,CAACA,GAAE,KAAI,CAAC,GAAE,CAACA,GAAE,OAAM,CAAC,GAAE,CAACA,GAAE,MAAK,CAAC,GAAE,CAACA,GAAE,SAAQ,CAAC,GAAE,CAACA,GAAE,SAAQ,CAAC,GAAE,CAACA,GAAE,aAAY,CAAC,GAAE,CAACA,GAAE,SAAQ,CAAC,GAAE,CAACA,GAAE,kBAAiB,CAAC,GAAE,CAACA,GAAE,0BAAyB,CAAC,GAAE,CAACA,GAAE,eAAc,CAAC,GAAE,CAACA,GAAE,uBAAsB,CAAC,GAAE,CAACA,GAAE,iCAAgC,CAAC,GAAE,CAACA,GAAE,OAAM,CAAC,GAAE,CAACA,GAAE,aAAY,EAAE,GAAE,CAACA,GAAE,eAAc,EAAE,GAAE,CAACA,GAAE,eAAc,EAAE,CAAC,CAAC;;;ACAwL,SAASC,GAAEC,IAAE;AAAC,SAAO,KAAK,IAAIA,KAAEA,KAAEA,EAAC;AAAC;AAAC,SAASC,GAAED,IAAEE,IAAEC,IAAE;AAAC,QAAMC,KAAED,GAAE,YAAWE,KAAEF,GAAE;AAAsB,SAAO,EAAE,QAAM,KAAK,IAAIC,GAAE,WAASF,KAAEE,GAAE,SAAQ,CAAC,GAAE,EAAE,SAAOL,GAAEC,EAAC,GAAE,EAAE,eAAaI,GAAE,cAAa,EAAE,gBAAcC,IAAE;AAAC;AAAC,SAASC,GAAEN,IAAEE,IAAE;AAAC,SAAO,MAAIF,KAAEE,GAAE,eAAaA,GAAE,gBAAc,IAAE,IAAEA,GAAE,gBAAcF;AAAE;AAAC,SAASO,GAAEL,IAAEC,IAAE;AAAC,SAAO,KAAK,IAAI,EAAED,KAAEC,GAAE,OAAMD,IAAEC,GAAE,MAAM,GAAEG,GAAEJ,IAAEC,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEH,IAAEE,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAED,IAAEE,IAAEC,EAAC;AAAE,SAAOC,GAAE,eAAa,GAAEA,GAAE,gBAAc,GAAEG,GAAE,GAAEH,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEE,IAAEC,IAAEC,IAAE;AAAC,EAAAA,GAAE,QAAM,EAAEJ,IAAEE,IAAEC,EAAC,GAAEC,GAAE,SAAO,GAAEA,GAAE,eAAaD,GAAE,WAAW,cAAaC,GAAE,gBAAcD,GAAE;AAAqB;AAAC,SAASK,GAAER,IAAEE,IAAEC,KAAE,CAAC,GAAE,CAAC,GAAE;AAAC,QAAMC,KAAE,KAAK,IAAI,KAAK,IAAIF,GAAE,OAAMI,GAAEN,GAAE,CAAC,GAAEE,EAAC,IAAE,KAAK,IAAI,MAAKF,GAAE,CAAC,CAAC,CAAC,GAAE,CAAC;AAAE,SAAOG,GAAE,CAAC,IAAEH,GAAE,CAAC,IAAEI,IAAED,GAAE,CAAC,IAAEH,GAAE,CAAC,IAAEI,IAAED;AAAC;AAAC,SAASM,GAAET,IAAEE,IAAEC,IAAEC,IAAE;AAAC,SAAOG,GAAEP,IAAEC,GAAEC,IAAEC,IAAEC,EAAC,CAAC;AAAC;AAAmqE,IAAMM,KAAE,EAAC,oBAAmB,EAAC,KAAI,EAAC,WAAU,EAAE,EAAE,GAAE,WAAU,EAAE,EAAE,GAAE,oBAAmB,IAAE,GAAE,KAAI,EAAC,WAAU,EAAE,EAAE,GAAE,WAAU,EAAE,EAAE,GAAE,oBAAmB,IAAE,EAAC,GAAE,YAAW,KAAG,mBAAkB,MAAI,cAAa,EAAC;AAAsM,IAAM,IAAE,EAAC,OAAM,GAAE,QAAO,GAAE,cAAa,GAAE,eAAc,EAAC;;;ACAvyG,IAAM,IAAEC,GAAE;AAAE,SAASC,GAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAGL,GAAE,QAAQ,KAAGA,GAAE,cAAa;AAAC,MAAEA,GAAE,SAAOE,GAAE,IAAI;AAAE,UAAMI,KAAEL,GAAE;AAAU,MAAED,GAAE,cAAaE,IAAEC,IAAEG,IAAEF,IAAEC,EAAC;AAAA,EAAC,OAAK;AAAC,UAAMJ,KAAED,GAAE,QAAQ,IAAIO,GAAE,QAAQ,GAAED,KAAEN,GAAE,iBAAiB,IAAIO,GAAE,QAAQ;AAAE,MAAEL,IAAEC,IAAE,GAAEF,GAAE,SAAO,GAAEA,IAAEK,IAAE,QAAOF,IAAEC,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMG,KAAEP,GAAE;AAAE,SAAS,EAAED,IAAEC,IAAEG,IAAEC,IAAEC,IAAEG,IAAE;AAAC,MAAG,EAAET,EAAC,EAAE;AAAO,QAAMU,KAAEH,GAAEN,IAAEG,IAAEI,EAAC;AAAE,MAAG,EAAE,GAAER,GAAE,KAAK,GAAE,EAAE,GAAEA,GAAE,KAAK,GAAE,EAAEM,EAAC,KAAGA,GAAE,YAAY,CAAC,GAAE,EAAE,GAAEL,IAAES,IAAEL,EAAC,GAAE;AAAC,UAAK,EAAC,kBAAiBH,IAAE,SAAQC,IAAE,UAASO,GAAC,IAAEV,IAAEW,KAAET,KAAEA,GAAE,SAAOC,GAAE,SAAO;AAAE,QAAGQ,KAAE,GAAE;AAAC,YAAMT,KAAEF,GAAE,YAAY;AAAE,UAAG,WAASE,IAAE;AAAC,mBAAUF,MAAKE,GAAE,GAAEF,IAAEC,IAAEG,IAAEC,IAAEC,IAAEG,EAAC;AAAE;AAAA,MAAM;AAAA,IAAC;AAAC,MAAER,IAAEG,IAAE,GAAEO,IAAER,IAAEO,IAAER,IAAEI,IAAEG,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMG,KAAEX,GAAE;AAAE,SAAS,EAAED,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,IAAEG,IAAEC,IAAEC,IAAE;AAAC,MAAGF,GAAE,QAAO,EAAET,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,IAAEG,IAAEC,IAAEC,EAAC;AAAE,QAAK,EAAC,MAAKb,IAAE,QAAOe,GAAC,IAAEP,IAAEQ,KAAEd,GAAE,CAAC,GAAEe,KAAEf,GAAE,CAAC,GAAEgB,KAAEhB,GAAE,CAAC,GAAEiB,KAAEhB,GAAE,CAAC,IAAEa,IAAEf,KAAEE,GAAE,CAAC,IAAEc,IAAEP,KAAEP,GAAE,CAAC,IAAEe;AAAE,WAAQE,KAAEhB,IAAEiB,KAAE,IAAEjB,IAAEgB,KAAEd,IAAE,EAAEc,IAAE;AAAC,QAAIlB,KAAEa,KAAER,GAAEc,IAAG,GAAElB,KAAEH,GAAEE,IAAG,GAAEE,KAAEJ,GAAEE,IAAG,GAAEI,KAAEN,GAAEE,EAAC;AAAE,IAAAA,KAAEa,KAAER,GAAEc,IAAG;AAAE,QAAIb,KAAER,GAAEE,IAAG,GAAES,KAAEX,GAAEE,IAAG,GAAEoB,KAAEtB,GAAEE,EAAC;AAAE,IAAAA,KAAEa,KAAER,GAAEc,IAAG;AAAE,QAAIE,KAAEvB,GAAEE,IAAG,GAAEsB,KAAExB,GAAEE,IAAG,GAAEO,KAAET,GAAEE,EAAC;AAAE,MAAEU,EAAC,MAAI,CAACT,IAAEC,IAAEE,EAAC,IAAEM,GAAE,cAAcT,IAAEC,IAAEE,IAAEc,EAAC,GAAE,CAACZ,IAAEG,IAAEW,EAAC,IAAEV,GAAE,cAAcJ,IAAEG,IAAEW,IAAEF,EAAC,GAAE,CAACG,IAAEC,IAAEf,EAAC,IAAEG,GAAE,cAAcW,IAAEC,IAAEf,IAAEW,EAAC;AAAG,UAAMK,KAAEjB,KAAEL,IAAEuB,KAAEf,KAAEP,IAAEuB,KAAEL,KAAEhB,IAAEsB,KAAEL,KAAEpB,IAAE0B,KAAEL,KAAEpB,IAAE0B,KAAErB,KAAEH,IAAEyB,KAAE9B,KAAE6B,KAAED,KAAEnB,IAAEsB,KAAEtB,KAAEkB,KAAEE,KAAEX,IAAE,IAAEA,KAAEU,KAAED,KAAE3B,IAAE,IAAEwB,KAAEM,KAAEL,KAAEM,KAAEL,KAAE;AAAE,QAAG,KAAK,IAAI,CAAC,KAAG,OAAO,QAAQ;AAAS,UAAM,IAAEX,KAAEb,IAAE8B,KAAEhB,KAAEb,IAAE8B,KAAEhB,KAAEZ,IAAE,IAAE,IAAEyB,KAAEE,KAAED,KAAEE,KAAE;AAAE,QAAG,IAAE,GAAE;AAAC,UAAG,IAAE,KAAG,IAAE,EAAE;AAAA,IAAQ,WAAS,IAAE,KAAG,IAAE,EAAE;AAAS,UAAM,IAAED,KAAEN,KAAED,KAAEQ,IAAE,IAAEA,KAAET,KAAEE,KAAE,GAAE,IAAE,IAAED,KAAED,KAAEQ,IAAEE,KAAEhB,KAAE,IAAElB,KAAE,IAAES,KAAE;AAAE,QAAG,IAAE,GAAE;AAAC,UAAGyB,KAAE,KAAG,IAAEA,KAAE,EAAE;AAAA,IAAQ,WAASA,KAAE,KAAG,IAAEA,KAAE,EAAE;AAAS,UAAM,KAAGP,KAAE,IAAEC,KAAE,IAAEC,KAAE,KAAG;AAAE,QAAG,KAAG,GAAE;AAAC,MAAAjB,GAAE,GAAE,EAAEY,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEhB,EAAC,GAAEM,IAAE,KAAE;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAElB,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,IAAEG,IAAEC,IAAEC,IAAE;AAAC,QAAK,EAAC,MAAKb,IAAE,QAAOe,GAAC,IAAEP,IAAEQ,KAAEd,GAAE,CAAC,GAAEe,KAAEf,GAAE,CAAC,GAAEgB,KAAEhB,GAAE,CAAC,GAAEiB,KAAEhB,GAAE,CAAC,IAAEa,IAAEf,KAAEE,GAAE,CAAC,IAAEc,IAAEP,KAAEP,GAAE,CAAC,IAAEe;AAAE,WAAQE,KAAEhB,IAAEgB,KAAEd,IAAE,EAAEc,IAAE;AAAC,UAAMlB,KAAES,GAAES,EAAC;AAAE,QAAIjB,KAAE,IAAED,IAAEE,KAAEW,KAAER,GAAEJ,IAAG,GAAEG,KAAEN,GAAEI,IAAG,GAAEI,KAAER,GAAEI,IAAG,GAAEiB,KAAErB,GAAEI,EAAC;AAAE,IAAAA,KAAEW,KAAER,GAAEJ,IAAG;AAAE,QAAImB,KAAEtB,GAAEI,IAAG,GAAEmB,KAAEvB,GAAEI,IAAG,GAAEoB,KAAExB,GAAEI,EAAC;AAAE,IAAAA,KAAEW,KAAER,GAAEJ,EAAC;AAAE,QAAIM,KAAET,GAAEI,IAAG,GAAEqB,KAAEzB,GAAEI,IAAG,GAAEsB,KAAE1B,GAAEI,EAAC;AAAE,MAAEQ,EAAC,MAAI,CAACN,IAAEE,IAAEa,EAAC,IAAET,GAAE,cAAcN,IAAEE,IAAEa,IAAED,EAAC,GAAE,CAACE,IAAEC,IAAEC,EAAC,IAAEZ,GAAE,cAAcU,IAAEC,IAAEC,IAAEJ,EAAC,GAAE,CAACX,IAAEgB,IAAEC,EAAC,IAAEd,GAAE,cAAcH,IAAEgB,IAAEC,IAAEN,EAAC;AAAG,UAAMO,KAAEL,KAAEhB,IAAEsB,KAAEL,KAAEf,IAAEqB,KAAEL,KAAEH,IAAES,KAAErB,KAAEH,IAAEyB,KAAEN,KAAEjB,IAAEwB,KAAEN,KAAEL,IAAE,IAAEpB,KAAE+B,KAAED,KAAErB,IAAE,IAAEA,KAAEoB,KAAEE,KAAEb,IAAE,IAAEA,KAAEY,KAAED,KAAE7B,IAAEgC,KAAEN,KAAE,IAAEC,KAAE,IAAEC,KAAE;AAAE,QAAG,KAAK,IAAII,EAAC,KAAG,OAAO,QAAQ;AAAS,UAAMC,KAAElB,KAAEV,IAAE,IAAEW,KAAET,IAAE,IAAEU,KAAEG,IAAE,IAAEa,KAAE,IAAE,IAAE,IAAE,IAAE;AAAE,QAAGD,KAAE,GAAE;AAAC,UAAG,IAAE,KAAG,IAAEA,GAAE;AAAA,IAAQ,WAAS,IAAE,KAAG,IAAEA,GAAE;AAAS,UAAM,IAAE,IAAEJ,KAAED,KAAE,GAAEO,KAAE,IAAER,KAAEE,KAAEK,IAAE,IAAEA,KAAEN,KAAED,KAAE,GAAES,KAAEjB,KAAE,IAAElB,KAAEkC,KAAEzB,KAAE;AAAE,QAAGuB,KAAE,GAAE;AAAC,UAAGG,KAAE,KAAG,IAAEA,KAAEH,GAAE;AAAA,IAAQ,WAASG,KAAE,KAAG,IAAEA,KAAEH,GAAE;AAAS,UAAMI,MAAGP,KAAE,IAAEC,KAAEI,KAAEH,KAAE,KAAGC;AAAE,QAAGI,MAAG,GAAE;AAAC,MAAAxB,GAAEwB,IAAE,EAAEV,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAElB,EAAC,GAAEZ,IAAE,KAAE;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAEC,GAAE;AAAV,IAAY,IAAEA,GAAE;AAAE,SAAS,EAAED,IAAEC,IAAEC,IAAEC,IAAEM,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,GAAEX,IAAEC,IAAEC,EAAC,GAAE,EAAE,GAAEC,IAAEM,IAAEC,EAAC,GAAE,EAAEC,IAAE,GAAE,CAAC,GAAE,EAAEA,IAAEA,EAAC,GAAEA;AAAC;AAAC,SAASJ,GAAEP,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAEA,IAAE,KAAGD,GAAE,CAAC,IAAED,GAAE,CAAC,IAAG,KAAGC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAG,KAAGC,GAAE,CAAC,IAAED,GAAE,CAAC,EAAE;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAOqB,GAAExB,IAAEC,IAAEC,IAAEC,IAAE,IAAE,CAAC;AAAC;AAAC,SAASqB,GAAExB,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,MAAGL,GAAE,CAAC,IAAEG,KAAEF,GAAE,CAAC,KAAGC,GAAE,CAAC,GAAEI,MAAGN,GAAE,CAAC,IAAEG,KAAEF,GAAE,CAAC,KAAGC,GAAE,CAAC;AAAE,MAAIO,KAAE,KAAK,IAAIJ,IAAEC,EAAC,GAAEI,KAAE,KAAK,IAAIL,IAAEC,EAAC;AAAE,QAAMK,MAAGX,GAAE,CAAC,IAAEG,KAAEF,GAAE,CAAC,KAAGC,GAAE,CAAC,GAAEJ,MAAGE,GAAE,CAAC,IAAEG,KAAEF,GAAE,CAAC,KAAGC,GAAE,CAAC;AAAE,MAAGQ,KAAE,KAAK,IAAIA,IAAE,KAAK,IAAIC,IAAEb,EAAC,CAAC,GAAEY,KAAE,EAAE,QAAM;AAAG,MAAGD,KAAE,KAAK,IAAIA,IAAE,KAAK,IAAIE,IAAEb,EAAC,CAAC,GAAEW,KAAEC,GAAE,QAAM;AAAG,QAAMG,MAAGb,GAAE,CAAC,IAAEG,KAAEF,GAAE,CAAC,KAAGC,GAAE,CAAC,GAAEY,MAAGd,GAAE,CAAC,IAAEG,KAAEF,GAAE,CAAC,KAAGC,GAAE,CAAC;AAAE,SAAOQ,KAAE,KAAK,IAAIA,IAAE,KAAK,IAAIG,IAAEC,EAAC,CAAC,GAAE,EAAEJ,KAAE,OAAKD,KAAE,KAAK,IAAIA,IAAE,KAAK,IAAII,IAAEC,EAAC,CAAC,GAAE,EAAEL,KAAEC,OAAID,KAAEL;AAAE;AAAC,SAAS,EAAEJ,IAAEE,IAAEE,IAAEC,IAAEC,IAAE;AAAC,MAAIG,MAAGL,GAAE,gBAAc,KAAGJ,GAAE;AAAW,IAAEM,EAAC,MAAIG,KAAEK,GAAEL,IAAEJ,IAAEH,IAAEI,EAAC;AAAG,QAAMI,KAAED,KAAE,KAAK,IAAI,MAAGT,GAAE,IAAI,KAAG,MAAGA,GAAE;AAAY,SAAO,EAAEU,KAAER,IAAEE,GAAE,kBAAgB,GAAE,QAAMA,GAAE,iBAAeA,GAAE,iBAAe,IAAE,CAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAE;AAAC,QAAMC,KAAED,KAAE,EAAEA,EAAC,IAAE,CAAC;AAAE,aAAUE,MAAKH,IAAE;AAAC,QAAIC,KAAED,GAAEG,EAAC;AAAE,IAAAF,MAAGA,GAAE,YAAUA,KAAE,EAAEA,EAAC,IAAG,QAAMA,MAAGE,MAAKD,OAAIA,GAAEC,EAAC,IAAEF;AAAA,EAAE;AAAC,SAAOC;AAAC;AAAC,SAASyB,GAAE1B,IAAEC,IAAE;AAAC,MAAIC,KAAE;AAAG,aAAUC,MAAKF,IAAE;AAAC,UAAMG,KAAEH,GAAEE,EAAC;AAAE,eAASC,OAAI,MAAM,QAAQA,EAAC,IAAE,SAAOJ,GAAEG,EAAC,KAAGH,GAAEG,EAAC,IAAEC,GAAE,MAAM,GAAEF,KAAE,QAAI,EAAEF,GAAEG,EAAC,GAAEC,EAAC,MAAIF,KAAE,QAAIF,GAAEG,EAAC,MAAIC,OAAIF,KAAE,MAAGF,GAAEG,EAAC,IAAEC;AAAA,EAAG;AAAC,SAAOF;AAAC;AAAC,SAAS,EAAEH,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,SAAOD,GAAE,QAAS,CAAAA,OAAGC,GAAE,KAAKD,EAAC,CAAE,GAAEC;AAAC;AAAC,IAAM4B,KAAE,EAAC,UAAS,GAAE,QAAO,GAAE,SAAQ,GAAE,MAAK,EAAC;AAA7C,IAA+C,IAAE;;;ACAr3G,IAAMO,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,UAAM,GAAE,KAAK,OAAKD,GAAE,UAAS,KAAK,gBAAc,OAAG,KAAK,WAAS,MAAG,KAAK,kBAAgB,GAAE,KAAK,eAAa,GAAE,KAAK,4BAA0BE,IAAE,KAAK,OAAKH,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,OAAKA,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,cAAY,EAAEC,IAAEC,EAAC,GAAE,KAAK,mBAAmB,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,OAAOD,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,cAAcA,IAAED,KAAE,MAAG;AAAC,IAAAI,GAAE,KAAK,aAAYH,EAAC,MAAI,KAAK,mBAAmB,KAAK,WAAW,GAAED,MAAG,KAAK,kBAAkB;AAAA,EAAE;AAAA,EAAC,mBAAmBC,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,QAAQA,IAAE;AAAC,IAAAA,OAAI,KAAK,aAAW,KAAK,WAASA,IAAE,KAAK,kBAAkB;AAAA,EAAE;AAAA,EAAC,aAAaA,IAAE;AAAC,WAAO,KAAK,UAAU,KAAG,KAAK,mBAAmBA,GAAE,MAAM,KAAG,MAAI,KAAK,iBAAeA,GAAE;AAAA,EAAmB;AAAA,EAAC,mBAAmBA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,WAAW;AAAA,EAAc;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,eAAeA,IAAE;AAAC,IAAAA,OAAI,KAAK,oBAAkB,KAAK,kBAAgBA,IAAE,KAAK,kBAAkB;AAAA,EAAE;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,IAAAA,OAAI,KAAK,iBAAe,KAAK,eAAaA,IAAE,KAAK,kBAAkB;AAAA,EAAE;AAAA,EAAC,IAAI,2BAA0B;AAAC,WAAO,KAAK;AAAA,EAAyB;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,oBAAmB;AAAC,MAAE,KAAK,UAAU,KAAG,KAAK,WAAW,gBAAgB,IAAI;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAED,IAAEE,IAAEG,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,KAAK,CAAC,IAAE,KAAK,KAAK,CAAC,IAAEF,GAAE,CAAC,GAAE,KAAK,KAAK,CAAC,IAAE,KAAK,KAAK,CAAC,IAAEA,GAAE,CAAC,GAAE,KAAK,UAAUJ,IAAED,IAAEE,IAAE,KAAK,MAAK,KAAK,MAAKI,EAAC;AAAA,EAAC;AAAC;AAAgE,IAAIE;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,cAAY,CAAC,IAAE,eAAcA,GAAEA,GAAE,wBAAsB,CAAC,IAAE,yBAAwBA,GAAEA,GAAE,+BAA6B,CAAC,IAAE,gCAA+BA,GAAEA,GAAE,SAAO,EAAE,IAAE;AAAQ,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAME,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,iBAAeH,GAAE;AAAA,EAAO;AAAC;;;ACA/kE,IAAMI,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,OAAG,KAAK,gBAAcC,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,gBAAcA,GAAE,KAAI,KAAI,GAAG,GAAE,KAAK,eAAaA,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,eAAaA,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,cAAYA,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,iBAAe,OAAG,KAAK,gBAAc,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,gBAAc,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,mBAAiB,OAAG,KAAK,kBAAgB,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,qBAAmB,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,GAAE,CAAC,GAAE,KAAK,yBAAuBC,GAAE;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAE;;;ACAtf,IAAMC,KAAE,CAAC,IAAIC,GAAEC,GAAE,UAAS,GAAE,EAAE,OAAM,GAAE,EAAE,CAAC;AAAzC,IAA2C,IAAE,CAAC,IAAID,GAAEC,GAAE,UAAS,GAAE,EAAE,OAAM,GAAE,EAAE,GAAE,IAAID,GAAEC,GAAE,KAAI,GAAE,EAAE,OAAM,IAAG,EAAE,CAAC;AAA3G,IAA6GC,KAAE,CAAC,IAAIF,GAAEC,GAAE,UAAS,GAAE,EAAE,OAAM,GAAE,EAAE,GAAE,IAAID,GAAEC,GAAE,QAAO,GAAE,EAAE,OAAM,IAAG,EAAE,GAAE,IAAID,GAAEC,GAAE,KAAI,GAAE,EAAE,OAAM,IAAG,EAAE,CAAC;AAA7M,IAA+ME,KAAE,CAAC,IAAIH,GAAEC,GAAE,UAAS,GAAE,EAAE,OAAM,GAAE,EAAE,GAAE,IAAID,GAAEC,GAAE,OAAM,GAAE,EAAE,eAAc,IAAG,EAAE,CAAC;AAAzR,IAA2RD,KAAE,CAAC,IAAIA,GAAEC,GAAE,UAAS,GAAE,EAAE,OAAM,GAAE,CAAC,CAAC;AAA7T,IAA+TG,KAAE,CAAC,IAAIJ,GAAEC,GAAE,UAAS,GAAE,EAAE,OAAM,GAAE,EAAE,GAAE,IAAID,GAAEC,GAAE,KAAI,GAAE,EAAE,OAAM,GAAE,EAAE,CAAC;;;ACApf,IAAMI,KAAN,cAAgB,EAAC;AAAC;;;ACAkV,SAASC,GAAEC,IAAEC,KAAEC,IAAEC,KAAEC,IAAEC,KAAE,IAAGC,KAAE,GAAE;AAAC,MAAIP,KAAE;AAAK,MAAGE,OAAIM,GAAE,CAAAR,KAAE,IAAI,aAAa,CAACM,IAAEA,IAAE,GAAE,GAAEC,IAAED,IAAE,GAAE,GAAEA,IAAEC,IAAE,GAAE,GAAEA,IAAEA,IAAE,GAAE,CAAC,CAAC;AAAA,MAAO,CAAAP,KAAE,IAAI,aAAa,CAACM,IAAEA,IAAEC,IAAED,IAAEA,IAAEC,IAAEA,IAAEA,EAAC,CAAC;AAAE,SAAO,IAAIE,GAAER,IAAEG,IAAE,EAAC,UAASF,GAAC,GAAE,EAAC,UAASG,GAAE,aAAaJ,IAAE,EAAE,aAAYD,EAAC,EAAC,CAAC;AAAC;AAA+iB,SAAS,EAAEU,IAAE;AAAC,SAAO,IAAI,EAAEA,IAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,UAASC,GAAE,eAAc,cAAa,EAAE,SAAQ,OAAM,GAAE,QAAO,EAAC,GAAE,IAAI,WAAW,CAAC,KAAI,KAAI,KAAI,GAAG,CAAC,CAAC;AAAC;", "names": ["e", "r", "o", "a", "s", "t", "o", "r", "e", "a", "s", "t", "i", "m", "r", "e", "E", "O", "l", "e", "s", "t", "a", "i", "r", "c", "o", "f", "m", "h", "a", "x", "t", "n", "e", "o", "i", "r", "s", "O", "d", "c", "f", "l", "M", "u", "m", "p", "h", "b", "g", "y", "j", "v", "T", "V", "L", "N", "A", "P", "S", "E", "U", "C", "z", "w", "F", "G", "h", "r", "e", "t", "E", "P", "s", "i", "a", "c", "e", "u", "n", "v", "u", "r", "e", "o", "n", "t", "O", "I", "r", "A", "r", "f", "i", "m", "t", "T", "E", "l", "u", "A", "r", "e", "G"]}