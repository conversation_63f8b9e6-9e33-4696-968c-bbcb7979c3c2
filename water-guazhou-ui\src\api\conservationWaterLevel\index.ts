import request from '@/utils/request'

// 涵养水位数据接口
export interface ConservationWaterLevel {
  id?: string
  tenantId?: string
  stationId: string
  stationName?: string
  stationLocation?: string
  rawWaterLevel: number
  groundwaterLevel: number
  levelChange?: number
  rainfallAmount?: number
  evaporationAmount?: number
  surfaceRunoff?: number
  extractionAmount?: number
  soilMoisture?: number
  permeabilityCoefficient?: number
  recordTime: string
  createTime?: string
  updateTime?: string
  remark?: string
  dataSource?: number
  creator?: string
  creatorName?: string
}

// 涵养分析结果接口
export interface ConservationAnalysis {
  id?: string
  tenantId?: string
  stationId: string
  stationName?: string
  startTime: string
  endTime: string
  initialLevel?: number
  finalLevel?: number
  levelChange?: number
  avgRainfall?: number
  avgEvaporation?: number
  totalExtraction?: number
  conservationCoefficient?: number
  conservationPotential?: number
  suggestedConservationAmount?: number
  conservationSuggestion?: string
  riskLevel?: number
  riskDescription?: string
  algorithmVersion?: string
  analysisDetails?: string
  createTime?: string
  updateTime?: string
  status?: number
  creator?: string
  creatorName?: string
}

// 查询参数接口
export interface WaterLevelQueryParams {
  pageNum?: number
  pageSize?: number
  stationId?: string
  stationName?: string
  dataSource?: number
  startTime?: number
  endTime?: number
  creator?: string
}

export interface AnalysisQueryParams {
  pageNum?: number
  pageSize?: number
  stationId?: string
  stationName?: string
  status?: number
  riskLevel?: number
  startTime?: number
  endTime?: number
  creator?: string
}

// ==================== 涵养水位数据相关接口 ====================

// 保存涵养水位数据
export function saveWaterLevel(data: ConservationWaterLevel) {
  return request({
    url: '/api/conservation-water-level/water-level',
    method: 'post',
    data
  })
}

// 更新涵养水位数据
export function updateWaterLevel(data: ConservationWaterLevel) {
  return request({
    url: '/api/conservation-water-level/water-level',
    method: 'put',
    data
  })
}

// 删除涵养水位数据
export function deleteWaterLevel(id: string) {
  return request({
    url: `/api/conservation-water-level/water-level/${id}`,
    method: 'delete'
  })
}

// 获取涵养水位数据详情
export function getWaterLevelById(id: string) {
  return request({
    url: `/api/conservation-water-level/water-level/${id}`,
    method: 'get'
  })
}

// 分页查询涵养水位数据
export function getWaterLevelList(params: WaterLevelQueryParams) {
  return request({
    url: '/api/conservation-water-level/water-level/list',
    method: 'get',
    params
  })
}

// 获取水位变化数据
export function getWaterLevelChangeData(stationId: string, startTime: number, endTime: number) {
  return request({
    url: '/api/conservation-water-level/water-level/change-data',
    method: 'get',
    params: { stationId, startTime, endTime }
  })
}

// 获取测点最新水位数据
export function getLatestWaterLevel(stationId: string) {
  return request({
    url: `/api/conservation-water-level/water-level/latest/${stationId}`,
    method: 'get'
  })
}

// 获取统计数据
export function getStatisticsData(stationId: string, startTime: number, endTime: number) {
  return request({
    url: '/api/conservation-water-level/water-level/statistics',
    method: 'get',
    params: { stationId, startTime, endTime }
  })
}

// 批量导入水位数据
export function batchImportWaterLevel(data: ConservationWaterLevel[]) {
  return request({
    url: '/api/conservation-water-level/water-level/batch-import',
    method: 'post',
    data
  })
}

// ==================== 智能分析相关接口 ====================

// 执行智能分析
export function performIntelligentAnalysis(stationId: string, startTime: number, endTime: number) {
  return request({
    url: '/api/conservation-water-level/analysis/perform',
    method: 'post',
    params: { stationId, startTime, endTime }
  })
}

// 分页查询分析结果
export function getAnalysisList(params: AnalysisQueryParams) {
  return request({
    url: '/api/conservation-water-level/analysis/list',
    method: 'get',
    params
  })
}

// 获取分析结果详情
export function getAnalysisById(id: string) {
  return request({
    url: `/api/conservation-water-level/analysis/${id}`,
    method: 'get'
  })
}

// 获取测点最新分析结果
export function getLatestAnalysis(stationId: string) {
  return request({
    url: `/api/conservation-water-level/analysis/latest/${stationId}`,
    method: 'get'
  })
}

// 获取风险等级统计
export function getRiskLevelStatistics() {
  return request({
    url: '/api/conservation-water-level/analysis/risk-statistics',
    method: 'get'
  })
}

// 获取涵养潜力趋势
export function getConservationPotentialTrend(stationId: string, startTime: number, endTime: number) {
  return request({
    url: '/api/conservation-water-level/analysis/potential-trend',
    method: 'get',
    params: { stationId, startTime, endTime }
  })
}

// 重新分析
export function reAnalysis(analysisId: string) {
  return request({
    url: `/api/conservation-water-level/analysis/re-analysis/${analysisId}`,
    method: 'post'
  })
}

// 删除分析结果
export function deleteAnalysis(id: string) {
  return request({
    url: `/api/conservation-water-level/analysis/${id}`,
    method: 'delete'
  })
}
