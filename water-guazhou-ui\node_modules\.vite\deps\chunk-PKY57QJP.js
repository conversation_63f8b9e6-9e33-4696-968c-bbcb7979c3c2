import {
  I,
  j,
  v,
  w
} from "./chunk-OWU4AWBA.js";
import {
  t
} from "./chunk-WIKVTG73.js";
import {
  a as a2
} from "./chunk-OVPAFMSR.js";
import {
  s as s2
} from "./chunk-55WN4LCX.js";
import {
  x
} from "./chunk-6NE6A2GD.js";
import {
  a
} from "./chunk-D7S3BWBP.js";
import {
  s
} from "./chunk-4RZONHOY.js";

// node_modules/@arcgis/core/portal/support/portalLayers.js
async function l(e) {
  !e.portalItem || e.portalItem instanceof x || (e = { ...e, portalItem: new x(e.portalItem) });
  const r = await y(e.portalItem);
  return new (0, r.constructor)({ portalItem: e.portalItem, ...r.properties });
}
async function y(e) {
  await e.load();
  return p(await m(e));
}
async function m(r) {
  switch (r.type) {
    case "Map Service":
      return f(r);
    case "Feature Service":
      return L(r);
    case "Feature Collection":
      return w2(r);
    case "Scene Service":
      return N(r);
    case "Image Service":
      return d(r);
    case "Stream Service":
      return S();
    case "Vector Tile Service":
      return I2();
    case "GeoJson":
      return g();
    case "CSV":
      return T();
    case "KML":
      return v2();
    case "WFS":
      return j2();
    case "WMTS":
      return G();
    case "WMS":
      return M();
    case "Feed":
      return h();
    default:
      throw new s("portal:unknown-item-type", "Unknown item type '${type}'", { type: r.type });
  }
}
async function p(e) {
  const r = a2[e.className];
  return { constructor: await r(), properties: e.properties };
}
async function f(e) {
  return await b(e) ? { className: "TileLayer" } : { className: "MapImageLayer" };
}
async function L(e) {
  if (s2(e, "Oriented Imagery Layer")) return F(e);
  const r = await C(e);
  if ("object" == typeof r) {
    const e2 = {};
    return null != r.id && (e2.layerId = r.id), { className: r.className || "FeatureLayer", properties: e2 };
  }
  return { className: "GroupLayer" };
}
async function N(e) {
  var _a, _b;
  const a3 = await C(e);
  if ("object" == typeof a3) {
    const t2 = {};
    let s3;
    if (null != a3.id ? (t2.layerId = a3.id, s3 = `${e.url}/layers/${a3.id}`) : s3 = e.url, (_a = e.typeKeywords) == null ? void 0 : _a.length) {
      for (const a4 of Object.keys(a)) if (e.typeKeywords.includes(a4)) return { className: a[a4] };
    }
    const n = await t(s3);
    return { className: a[n == null ? void 0 : n.layerType] || "SceneLayer", properties: t2 };
  }
  if (false === a3) {
    return "Voxel" === ((_b = await t(e.url)) == null ? void 0 : _b.layerType) ? { className: "VoxelLayer" } : { className: "GroupLayer" };
  }
  return { className: "GroupLayer" };
}
async function w2(e) {
  await e.load();
  const r = s2(e, "Map Notes"), a3 = s2(e, "Markup");
  if (r || a3) return { className: "MapNotesLayer" };
  if (s2(e, "Route Layer")) return { className: "RouteLayer" };
  const t2 = await e.fetchData();
  return 1 === v(t2) ? { className: "FeatureLayer" } : { className: "GroupLayer" };
}
async function d(e) {
  var _a, _b, _c, _d;
  await e.load();
  const r = ((_a = e.typeKeywords) == null ? void 0 : _a.map((e2) => e2.toLowerCase())) ?? [];
  if (r.includes("elevation 3d layer")) return { className: "ElevationLayer" };
  if (r.includes("tiled imagery")) return { className: "ImageryTileLayer" };
  const a3 = (_b = await e.fetchData()) == null ? void 0 : _b.layerType;
  if ("ArcGISTiledImageServiceLayer" === a3) return { className: "ImageryTileLayer" };
  if ("ArcGISImageServiceLayer" === a3) return { className: "ImageryLayer" };
  const t2 = await t(e.url), s3 = (_c = t2.cacheType) == null ? void 0 : _c.toLowerCase(), n = (_d = t2.capabilities) == null ? void 0 : _d.toLowerCase().includes("tilesonly");
  return "map" === s3 || n ? { className: "ImageryTileLayer" } : { className: "ImageryLayer" };
}
function S() {
  return { className: "StreamLayer" };
}
function I2() {
  return { className: "VectorTileLayer" };
}
function g() {
  return { className: "GeoJSONLayer" };
}
function T() {
  return { className: "CSVLayer" };
}
function v2() {
  return { className: "KMLLayer" };
}
function j2() {
  return { className: "WFSLayer" };
}
function M() {
  return { className: "WMSLayer" };
}
function G() {
  return { className: "WMTSLayer" };
}
function h() {
  return { className: "StreamLayer" };
}
async function F(e) {
  await e.load();
  const r = await e.fetchData();
  return r.coverage ? { className: "GroupLayer" } : { className: "OrientedImageryLayer", properties: r };
}
async function b(e) {
  return (await t(e.url)).tileInfo;
}
async function C(e) {
  const r = e.url;
  if (!r || r.match(/\/\d+$/)) return {};
  await e.load();
  const a3 = await e.fetchData();
  if ("Feature Service" === e.type) {
    const e2 = V(await w(a3, r));
    if ("object" == typeof e2) {
      const r2 = j(a3);
      e2.className = null != e2.id && r2.includes(e2.id) ? "SubtypeGroupLayer" : "FeatureLayer";
    }
    return e2;
  }
  if (v(a3) > 0) return V(a3);
  return V(await t(r));
}
function V(e) {
  return 1 === v(e) && { id: I(e) };
}

export {
  l,
  m
};
//# sourceMappingURL=chunk-PKY57QJP.js.map
