{"version": 3, "sources": ["../../@arcgis/core/views/2d/tiling/LODInfo.js", "../../@arcgis/core/views/2d/tiling/TileSpan.js", "../../@arcgis/core/views/2d/tiling/TileInfoView.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../core/maybe.js\";import{getInfo as r}from\"../../../geometry/support/spatialReferenceUtils.js\";import o from\"./TileKey.js\";function i(t,r){return[t,r]}function e(t,r,o){return t[0]=r,t[1]=o,t}function s(t,r,o,i,e){return t[0]=r,t[1]=o,t[2]=i,t[3]=e,t}const n=new o(\"0/0/0/0\");class l{static create(o,s,n=null){const h=r(o.spatialReference),a=s.origin||i(o.origin.x,o.origin.y),u=i(o.size[0]*s.resolution,o.size[1]*s.resolution),m=i(-1/0,-1/0),g=i(1/0,1/0),w=i(1/0,1/0);t(n)&&(e(m,Math.max(0,Math.floor((n.xmin-a[0])/u[0])),Math.max(0,Math.floor((a[1]-n.ymax)/u[1]))),e(g,Math.max(0,Math.floor((n.xmax-a[0])/u[0])),Math.max(0,Math.floor((a[1]-n.ymin)/u[1]))),e(w,g[0]-m[0]+1,g[1]-m[1]+1));const{cols:c,rows:d}=s;let f,F,z,p;return!n&&c&&d&&(e(m,c[0],d[0]),e(g,c[1],d[1]),e(w,c[1]-c[0]+1,d[1]-d[0]+1)),o.isWrappable?(f=i(Math.ceil(Math.round((h.valid[1]-h.valid[0])/s.resolution)/o.size[0]),w[1]),F=i(Math.floor((h.origin[0]-a[0])/u[0]),m[1]),z=i(f[0]+F[0]-1,g[1]),p=!0):(F=m,z=g,f=w,p=!1),new l(s.level,s.resolution,s.scale,a,m,g,w,u,F,z,f,p)}constructor(t,r,o,i,e,s,n,l,h,a,u,m){this.level=t,this.resolution=r,this.scale=o,this.origin=i,this.first=e,this.last=s,this.size=n,this.norm=l,this.worldStart=h,this.worldEnd=a,this.worldSize=u,this.wrap=m}normalizeCol(t){if(!this.wrap)return t;const r=this.worldSize[0];return t<0?r-1-Math.abs((t+1)%r):t%r}denormalizeCol(t,r){return this.wrap?this.worldSize[0]*r+t:t}getWorldForColumn(t){return this.wrap?Math.floor(t/this.worldSize[0]):0}getFirstColumnForWorld(t){return t*this.worldSize[0]+this.first[0]}getLastColumnForWorld(t){return t*this.worldSize[0]+this.first[0]+this.size[0]-1}getColumnForX(t){return(t-this.origin[0])/this.norm[0]}getXForColumn(t){return this.origin[0]+t*this.norm[0]}getRowForY(t){return(this.origin[1]-t)/this.norm[1]}getYForRow(t){return this.origin[1]-t*this.norm[1]}getTileBounds(t,r,o=!1){n.set(r);const i=o?n.col:this.denormalizeCol(n.col,n.world),e=n.row;return s(t,this.getXForColumn(i),this.getYForRow(e+1),this.getXForColumn(i+1),this.getYForRow(e)),t}getTileCoords(t,r,o=!1){n.set(r);const i=o?n.col:this.denormalizeCol(n.col,n.world);return Array.isArray(t)?e(t,this.getXForColumn(i),this.getYForRow(n.row)):(t.x=this.getXForColumn(i),t.y=this.getYForRow(n.row)),t}}export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass o{constructor(o,s,t){this.row=o,this.colFrom=s,this.colTo=t}}export{o as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getInfo as e}from\"../../../geometry/support/spatialReferenceUtils.js\";import t from\"./LODInfo.js\";import o from\"./TileCoverage.js\";import l from\"./TileKey.js\";import s from\"./TileSpan.js\";const i=new l(\"0/0/0/0\");class n{static create(e,t){e[1]>t[1]&&([e,t]=[t,e]);const[o,l]=e,[s,i]=t,r=s-o,a=i-l,h=0!==a?r/a:0,c=(Math.ceil(l)-l)*h,f=(Math.floor(l)-l)*h;return new n(o,Math.floor(l),Math.ceil(i),h,r<0?c:f,r<0?f:c,r<0?s:o,r<0?o:s)}constructor(e,t,o,l,s,i,n,r){this.x=e,this.ymin=t,this.ymax=o,this.invM=l,this.leftAdjust=s,this.rightAdjust=i,this.leftBound=n,this.rightBound=r}incrRow(){this.x+=this.invM}getLeftCol(){return Math.max(this.x+this.leftAdjust,this.leftBound)}getRightCol(){return Math.min(this.x+this.rightAdjust,this.rightBound)}}const r=[[0,0],[0,0],[0,0],[0,0]],a=1e-6;class h{constructor(e,o=null){this.tileInfo=e,this.fullExtent=o,this.scales=[],this._infoByScale={},this._infoByLevel={};const l=e.lods.slice();l.sort(((e,t)=>t.scale-e.scale));const s=this._lodInfos=l.map((l=>t.create(e,l,o)));l.forEach(((e,t)=>{this._infoByLevel[e.level]=s[t],this._infoByScale[e.scale]=s[t],this.scales[t]=e.scale}),this),this._wrap=e.isWrappable}get spatialReference(){return this.tileInfo.spatialReference}getLODInfoAt(e){return this._infoByLevel[\"number\"==typeof e?e:e.level]}getTileBounds(e,t,o=!1){i.set(t);const l=this._infoByLevel[i.level];return l?l.getTileBounds(e,i,o):e}getTileCoords(e,t,o=!1){i.set(t);const l=this._infoByLevel[i.level];return l?l.getTileCoords(e,i,o):e}getTileCoverage(e,t=192,l=\"closest\"){const i=\"closest\"===l?this.getClosestInfoForScale(e.scale):this.getSmallestInfoForScale(e.scale),a=o.pool.acquire(i),h=this._wrap;let c,f,u,m=1/0,g=-1/0;const d=a.spans;r[0][0]=r[0][1]=r[1][1]=r[3][0]=-t,r[1][0]=r[2][0]=e.size[0]+t,r[2][1]=r[3][1]=e.size[1]+t;for(const o of r)e.toMap(o,o),o[0]=i.getColumnForX(o[0]),o[1]=i.getRowForY(o[1]);const y=[];let v=3;for(let o=0;o<4;o++){if(r[o][1]===r[v][1]){v=o;continue}const e=n.create(r[o],r[v]);m=Math.min(e.ymin,m),g=Math.max(e.ymax,g),void 0===y[e.ymin]&&(y[e.ymin]=[]),y[e.ymin].push(e),v=o}if(null==m||null==g||g-m>100)return null;let _=[];for(c=m;c<g;){null!=y[c]&&(_=_.concat(y[c])),f=1/0,u=-1/0;for(let e=_.length-1;e>=0;e--){const t=_[e];f=Math.min(f,t.getLeftCol()),u=Math.max(u,t.getRightCol())}if(f=Math.floor(f),u=Math.floor(u),c>=i.first[1]&&c<=i.last[1])if(h)if(i.size[0]<i.worldSize[0]){const e=Math.floor(u/i.worldSize[0]);for(let t=Math.floor(f/i.worldSize[0]);t<=e;t++)d.push(new s(c,Math.max(i.getFirstColumnForWorld(t),f),Math.min(i.getLastColumnForWorld(t),u)))}else d.push(new s(c,f,u));else f>i.last[0]||u<i.first[0]||(f=Math.max(f,i.first[0]),u=Math.min(u,i.last[0]),d.push(new s(c,f,u)));c+=1;for(let e=_.length-1;e>=0;e--){const t=_[e];t.ymax>=c?t.incrRow():_.splice(e,1)}}return a}getTileParentId(e){i.set(e);const t=this._infoByLevel[i.level],o=this._lodInfos.indexOf(t)-1;return o<0?null:(this._getTileIdAtLOD(i,this._lodInfos[o],i),i.id)}getTileResolution(e){const t=this._infoByLevel[\"object\"==typeof e?e.level:e];return t?t.resolution:-1}getTileScale(e){const t=this._infoByLevel[e.level];return t?t.scale:-1}intersects(e,t){i.set(t);const o=this._infoByLevel[i.level],l=e.lodInfo;if(l.resolution>o.resolution){this._getTileIdAtLOD(i,l,i);const t=l.denormalizeCol(i.col,i.world);for(const o of e.spans)if(o.row===i.row&&o.colFrom<=t&&o.colTo>=t)return!0}if(l.resolution<o.resolution){const[t,s,n,r]=e.spans.reduce(((e,t)=>(e[0]=Math.min(e[0],t.row),e[1]=Math.max(e[1],t.row),e[2]=Math.min(e[2],t.colFrom),e[3]=Math.max(e[3],t.colTo),e)),[1/0,-1/0,1/0,-1/0]),a=o.denormalizeCol(i.col,i.world),h=l.getColumnForX(o.getXForColumn(a)),c=l.getRowForY(o.getYForRow(i.row)),f=l.getColumnForX(o.getXForColumn(a+1))-1,u=l.getRowForY(o.getYForRow(i.row+1))-1;return!(h>r||f<n||c>s||u<t)}const s=l.denormalizeCol(i.col,i.world);return e.spans.some((e=>e.row===i.row&&e.colFrom<=s&&e.colTo>=s))}normalizeBounds(t,o,l){if(t[0]=o[0],t[1]=o[1],t[2]=o[2],t[3]=o[3],this._wrap){const o=e(this.tileInfo.spatialReference),s=-l*(o.valid[1]-o.valid[0]);t[0]+=s,t[2]+=s}return t}getSmallestInfoForScale(e){const t=this.scales;if(this._infoByScale[e])return this._infoByScale[e];if(e>t[0])return this._infoByScale[t[0]];for(let o=1;o<t.length-1;o++)if(e>t[o]+a)return this._infoByScale[t[o-1]];return this._infoByScale[t[t.length-1]]}getClosestInfoForScale(e){const t=this.scales;return this._infoByScale[e]||(e=t.reduce(((t,o)=>Math.abs(o-e)<Math.abs(t-e)?o:t),t[0])),this._infoByScale[e]}scaleToLevel(e){const t=this.scales;if(this._infoByScale[e])return this._infoByScale[e].level;for(let o=t.length-1;o>=0;o--)if(e<t[o]){if(o===t.length-1)return this._infoByScale[t[t.length-1]].level;return this._infoByScale[t[o]].level+(t[o]-e)/(t[o]-t[o+1])}return this._infoByScale[t[0]].level}scaleToZoom(e){return this.tileInfo.scaleToZoom(e)}_getTileIdAtLOD(e,t,o){const l=this._infoByLevel[o.level];return e.set(o),t.resolution<l.resolution?null:(t.resolution===l.resolution||(e.level=t.level,e.col=Math.floor(o.col*l.resolution/t.resolution+.01),e.row=Math.floor(o.row*l.resolution/t.resolution+.01)),e)}}export{h as default};\n"], "mappings": ";;;;;;;;;;;;;;AAIyJ,SAAS,EAAE,GAAEA,IAAE;AAAC,SAAM,CAAC,GAAEA,EAAC;AAAC;AAAC,SAASC,GAAE,GAAED,IAAEE,IAAE;AAAC,SAAO,EAAE,CAAC,IAAEF,IAAE,EAAE,CAAC,IAAEE,IAAE;AAAC;AAAC,SAAS,EAAE,GAAEF,IAAEE,IAAEC,IAAEF,IAAE;AAAC,SAAO,EAAE,CAAC,IAAED,IAAE,EAAE,CAAC,IAAEE,IAAE,EAAE,CAAC,IAAEC,IAAE,EAAE,CAAC,IAAEF,IAAE;AAAC;AAAC,IAAM,IAAE,IAAI,EAAE,SAAS;AAAE,IAAMG,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,OAAOF,IAAEG,IAAEC,KAAE,MAAK;AAAC,UAAMC,KAAE,EAAEL,GAAE,gBAAgB,GAAEM,KAAEH,GAAE,UAAQ,EAAEH,GAAE,OAAO,GAAEA,GAAE,OAAO,CAAC,GAAE,IAAE,EAAEA,GAAE,KAAK,CAAC,IAAEG,GAAE,YAAWH,GAAE,KAAK,CAAC,IAAEG,GAAE,UAAU,GAAE,IAAE,EAAE,KAAG,GAAE,KAAG,CAAC,GAAE,IAAE,EAAE,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,IAAE,GAAE,IAAE,CAAC;AAAE,MAAEC,EAAC,MAAIL,GAAE,GAAE,KAAK,IAAI,GAAE,KAAK,OAAOK,GAAE,OAAKE,GAAE,CAAC,KAAG,EAAE,CAAC,CAAC,CAAC,GAAE,KAAK,IAAI,GAAE,KAAK,OAAOA,GAAE,CAAC,IAAEF,GAAE,QAAM,EAAE,CAAC,CAAC,CAAC,CAAC,GAAEL,GAAE,GAAE,KAAK,IAAI,GAAE,KAAK,OAAOK,GAAE,OAAKE,GAAE,CAAC,KAAG,EAAE,CAAC,CAAC,CAAC,GAAE,KAAK,IAAI,GAAE,KAAK,OAAOA,GAAE,CAAC,IAAEF,GAAE,QAAM,EAAE,CAAC,CAAC,CAAC,CAAC,GAAEL,GAAE,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,CAAC;AAAG,UAAK,EAAC,MAAK,GAAE,MAAK,EAAC,IAAEI;AAAE,QAAI,GAAE,GAAE,GAAE;AAAE,WAAM,CAACC,MAAG,KAAG,MAAIL,GAAE,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,CAAC,IAAGC,GAAE,eAAa,IAAE,EAAE,KAAK,KAAK,KAAK,OAAOK,GAAE,MAAM,CAAC,IAAEA,GAAE,MAAM,CAAC,KAAGF,GAAE,UAAU,IAAEH,GAAE,KAAK,CAAC,CAAC,GAAE,EAAE,CAAC,CAAC,GAAE,IAAE,EAAE,KAAK,OAAOK,GAAE,OAAO,CAAC,IAAEC,GAAE,CAAC,KAAG,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,CAAC,GAAE,IAAE,EAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,CAAC,GAAE,IAAE,SAAK,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,QAAI,IAAI,GAAEH,GAAE,OAAMA,GAAE,YAAWA,GAAE,OAAMG,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,YAAY,GAAER,IAAEE,IAAEC,IAAEF,IAAEI,IAAEC,IAAEF,IAAEG,IAAEC,IAAE,GAAE,GAAE;AAAC,SAAK,QAAM,GAAE,KAAK,aAAWR,IAAE,KAAK,QAAME,IAAE,KAAK,SAAOC,IAAE,KAAK,QAAMF,IAAE,KAAK,OAAKI,IAAE,KAAK,OAAKC,IAAE,KAAK,OAAKF,IAAE,KAAK,aAAWG,IAAE,KAAK,WAASC,IAAE,KAAK,YAAU,GAAE,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,aAAa,GAAE;AAAC,QAAG,CAAC,KAAK,KAAK,QAAO;AAAE,UAAMR,KAAE,KAAK,UAAU,CAAC;AAAE,WAAO,IAAE,IAAEA,KAAE,IAAE,KAAK,KAAK,IAAE,KAAGA,EAAC,IAAE,IAAEA;AAAA,EAAC;AAAA,EAAC,eAAe,GAAEA,IAAE;AAAC,WAAO,KAAK,OAAK,KAAK,UAAU,CAAC,IAAEA,KAAE,IAAE;AAAA,EAAC;AAAA,EAAC,kBAAkB,GAAE;AAAC,WAAO,KAAK,OAAK,KAAK,MAAM,IAAE,KAAK,UAAU,CAAC,CAAC,IAAE;AAAA,EAAC;AAAA,EAAC,uBAAuB,GAAE;AAAC,WAAO,IAAE,KAAK,UAAU,CAAC,IAAE,KAAK,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsB,GAAE;AAAC,WAAO,IAAE,KAAK,UAAU,CAAC,IAAE,KAAK,MAAM,CAAC,IAAE,KAAK,KAAK,CAAC,IAAE;AAAA,EAAC;AAAA,EAAC,cAAc,GAAE;AAAC,YAAO,IAAE,KAAK,OAAO,CAAC,KAAG,KAAK,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,cAAc,GAAE;AAAC,WAAO,KAAK,OAAO,CAAC,IAAE,IAAE,KAAK,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,WAAW,GAAE;AAAC,YAAO,KAAK,OAAO,CAAC,IAAE,KAAG,KAAK,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,WAAW,GAAE;AAAC,WAAO,KAAK,OAAO,CAAC,IAAE,IAAE,KAAK,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,cAAc,GAAEA,IAAEE,KAAE,OAAG;AAAC,MAAE,IAAIF,EAAC;AAAE,UAAMG,KAAED,KAAE,EAAE,MAAI,KAAK,eAAe,EAAE,KAAI,EAAE,KAAK,GAAED,KAAE,EAAE;AAAI,WAAO,EAAE,GAAE,KAAK,cAAcE,EAAC,GAAE,KAAK,WAAWF,KAAE,CAAC,GAAE,KAAK,cAAcE,KAAE,CAAC,GAAE,KAAK,WAAWF,EAAC,CAAC,GAAE;AAAA,EAAC;AAAA,EAAC,cAAc,GAAED,IAAEE,KAAE,OAAG;AAAC,MAAE,IAAIF,EAAC;AAAE,UAAMG,KAAED,KAAE,EAAE,MAAI,KAAK,eAAe,EAAE,KAAI,EAAE,KAAK;AAAE,WAAO,MAAM,QAAQ,CAAC,IAAED,GAAE,GAAE,KAAK,cAAcE,EAAC,GAAE,KAAK,WAAW,EAAE,GAAG,CAAC,KAAG,EAAE,IAAE,KAAK,cAAcA,EAAC,GAAE,EAAE,IAAE,KAAK,WAAW,EAAE,GAAG,IAAG;AAAA,EAAC;AAAC;;;ACAzuE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYM,IAAEC,IAAE,GAAE;AAAC,SAAK,MAAID,IAAE,KAAK,UAAQC,IAAE,KAAK,QAAM;AAAA,EAAC;AAAC;;;ACAiI,IAAMC,KAAE,IAAI,EAAE,SAAS;AAAE,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,OAAOC,IAAE,GAAE;AAAC,IAAAA,GAAE,CAAC,IAAE,EAAE,CAAC,MAAI,CAACA,IAAE,CAAC,IAAE,CAAC,GAAEA,EAAC;AAAG,UAAK,CAACC,IAAEC,EAAC,IAAEF,IAAE,CAACG,IAAEL,EAAC,IAAE,GAAEM,KAAED,KAAEF,IAAEI,KAAEP,KAAEI,IAAEI,KAAE,MAAID,KAAED,KAAEC,KAAE,GAAE,KAAG,KAAK,KAAKH,EAAC,IAAEA,MAAGI,IAAE,KAAG,KAAK,MAAMJ,EAAC,IAAEA,MAAGI;AAAE,WAAO,IAAI,GAAEL,IAAE,KAAK,MAAMC,EAAC,GAAE,KAAK,KAAKJ,EAAC,GAAEQ,IAAEF,KAAE,IAAE,IAAE,GAAEA,KAAE,IAAE,IAAE,GAAEA,KAAE,IAAED,KAAEF,IAAEG,KAAE,IAAEH,KAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAE,GAAEC,IAAEC,IAAEC,IAAEL,IAAEC,IAAEK,IAAE;AAAC,SAAK,IAAEJ,IAAE,KAAK,OAAK,GAAE,KAAK,OAAKC,IAAE,KAAK,OAAKC,IAAE,KAAK,aAAWC,IAAE,KAAK,cAAYL,IAAE,KAAK,YAAUC,IAAE,KAAK,aAAWK;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,KAAG,KAAK;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,IAAI,KAAK,IAAE,KAAK,YAAW,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK,IAAI,KAAK,IAAE,KAAK,aAAY,KAAK,UAAU;AAAA,EAAC;AAAC;AAAC,IAAMA,KAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAhC,IAAkC,IAAE;AAAK,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYJ,IAAEC,KAAE,MAAK;AAAC,SAAK,WAASD,IAAE,KAAK,aAAWC,IAAE,KAAK,SAAO,CAAC,GAAE,KAAK,eAAa,CAAC,GAAE,KAAK,eAAa,CAAC;AAAE,UAAMC,KAAEF,GAAE,KAAK,MAAM;AAAE,IAAAE,GAAE,KAAM,CAACF,IAAE,MAAI,EAAE,QAAMA,GAAE,KAAM;AAAE,UAAMG,KAAE,KAAK,YAAUD,GAAE,IAAK,CAAAA,OAAGA,GAAE,OAAOF,IAAEE,IAAED,EAAC,CAAE;AAAE,IAAAC,GAAE,QAAS,CAACF,IAAE,MAAI;AAAC,WAAK,aAAaA,GAAE,KAAK,IAAEG,GAAE,CAAC,GAAE,KAAK,aAAaH,GAAE,KAAK,IAAEG,GAAE,CAAC,GAAE,KAAK,OAAO,CAAC,IAAEH,GAAE;AAAA,IAAK,GAAG,IAAI,GAAE,KAAK,QAAMA,GAAE;AAAA,EAAW;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,SAAS;AAAA,EAAgB;AAAA,EAAC,aAAaA,IAAE;AAAC,WAAO,KAAK,aAAa,YAAU,OAAOA,KAAEA,KAAEA,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE,GAAEC,KAAE,OAAG;AAAC,IAAAH,GAAE,IAAI,CAAC;AAAE,UAAMI,KAAE,KAAK,aAAaJ,GAAE,KAAK;AAAE,WAAOI,KAAEA,GAAE,cAAcF,IAAEF,IAAEG,EAAC,IAAED;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE,GAAEC,KAAE,OAAG;AAAC,IAAAH,GAAE,IAAI,CAAC;AAAE,UAAMI,KAAE,KAAK,aAAaJ,GAAE,KAAK;AAAE,WAAOI,KAAEA,GAAE,cAAcF,IAAEF,IAAEG,EAAC,IAAED;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE,IAAE,KAAIE,KAAE,WAAU;AAAC,UAAMJ,KAAE,cAAYI,KAAE,KAAK,uBAAuBF,GAAE,KAAK,IAAE,KAAK,wBAAwBA,GAAE,KAAK,GAAEK,KAAE,EAAE,KAAK,QAAQP,EAAC,GAAEQ,KAAE,KAAK;AAAM,QAAI,GAAE,GAAE,GAAE,IAAE,IAAE,GAAE,IAAE,KAAG;AAAE,UAAM,IAAED,GAAE;AAAM,IAAAD,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC,IAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC,IAAEJ,GAAE,KAAK,CAAC,IAAE,GAAEI,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC,IAAEJ,GAAE,KAAK,CAAC,IAAE;AAAE,eAAUC,MAAKG,GAAE,CAAAJ,GAAE,MAAMC,IAAEA,EAAC,GAAEA,GAAE,CAAC,IAAEH,GAAE,cAAcG,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAEH,GAAE,WAAWG,GAAE,CAAC,CAAC;AAAE,UAAM,IAAE,CAAC;AAAE,QAAI,IAAE;AAAE,aAAQA,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAGG,GAAEH,EAAC,EAAE,CAAC,MAAIG,GAAE,CAAC,EAAE,CAAC,GAAE;AAAC,YAAEH;AAAE;AAAA,MAAQ;AAAC,YAAMD,KAAED,GAAE,OAAOK,GAAEH,EAAC,GAAEG,GAAE,CAAC,CAAC;AAAE,UAAE,KAAK,IAAIJ,GAAE,MAAK,CAAC,GAAE,IAAE,KAAK,IAAIA,GAAE,MAAK,CAAC,GAAE,WAAS,EAAEA,GAAE,IAAI,MAAI,EAAEA,GAAE,IAAI,IAAE,CAAC,IAAG,EAAEA,GAAE,IAAI,EAAE,KAAKA,EAAC,GAAE,IAAEC;AAAA,IAAC;AAAC,QAAG,QAAM,KAAG,QAAM,KAAG,IAAE,IAAE,IAAI,QAAO;AAAK,QAAI,IAAE,CAAC;AAAE,SAAI,IAAE,GAAE,IAAE,KAAG;AAAC,cAAM,EAAE,CAAC,MAAI,IAAE,EAAE,OAAO,EAAE,CAAC,CAAC,IAAG,IAAE,IAAE,GAAE,IAAE,KAAG;AAAE,eAAQD,KAAE,EAAE,SAAO,GAAEA,MAAG,GAAEA,MAAI;AAAC,cAAMO,KAAE,EAAEP,EAAC;AAAE,YAAE,KAAK,IAAI,GAAEO,GAAE,WAAW,CAAC,GAAE,IAAE,KAAK,IAAI,GAAEA,GAAE,YAAY,CAAC;AAAA,MAAC;AAAC,UAAG,IAAE,KAAK,MAAM,CAAC,GAAE,IAAE,KAAK,MAAM,CAAC,GAAE,KAAGT,GAAE,MAAM,CAAC,KAAG,KAAGA,GAAE,KAAK,CAAC,EAAE,KAAGQ,GAAE,KAAGR,GAAE,KAAK,CAAC,IAAEA,GAAE,UAAU,CAAC,GAAE;AAAC,cAAME,KAAE,KAAK,MAAM,IAAEF,GAAE,UAAU,CAAC,CAAC;AAAE,iBAAQS,KAAE,KAAK,MAAM,IAAET,GAAE,UAAU,CAAC,CAAC,GAAES,MAAGP,IAAEO,KAAI,GAAE,KAAK,IAAI,EAAE,GAAE,KAAK,IAAIT,GAAE,uBAAuBS,EAAC,GAAE,CAAC,GAAE,KAAK,IAAIT,GAAE,sBAAsBS,EAAC,GAAE,CAAC,CAAC,CAAC;AAAA,MAAC,MAAM,GAAE,KAAK,IAAI,EAAE,GAAE,GAAE,CAAC,CAAC;AAAA,UAAO,KAAET,GAAE,KAAK,CAAC,KAAG,IAAEA,GAAE,MAAM,CAAC,MAAI,IAAE,KAAK,IAAI,GAAEA,GAAE,MAAM,CAAC,CAAC,GAAE,IAAE,KAAK,IAAI,GAAEA,GAAE,KAAK,CAAC,CAAC,GAAE,EAAE,KAAK,IAAI,EAAE,GAAE,GAAE,CAAC,CAAC;AAAG,WAAG;AAAE,eAAQE,KAAE,EAAE,SAAO,GAAEA,MAAG,GAAEA,MAAI;AAAC,cAAMO,KAAE,EAAEP,EAAC;AAAE,QAAAO,GAAE,QAAM,IAAEA,GAAE,QAAQ,IAAE,EAAE,OAAOP,IAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOK;AAAA,EAAC;AAAA,EAAC,gBAAgBL,IAAE;AAAC,IAAAF,GAAE,IAAIE,EAAC;AAAE,UAAM,IAAE,KAAK,aAAaF,GAAE,KAAK,GAAEG,KAAE,KAAK,UAAU,QAAQ,CAAC,IAAE;AAAE,WAAOA,KAAE,IAAE,QAAM,KAAK,gBAAgBH,IAAE,KAAK,UAAUG,EAAC,GAAEH,EAAC,GAAEA,GAAE;AAAA,EAAG;AAAA,EAAC,kBAAkBE,IAAE;AAAC,UAAM,IAAE,KAAK,aAAa,YAAU,OAAOA,KAAEA,GAAE,QAAMA,EAAC;AAAE,WAAO,IAAE,EAAE,aAAW;AAAA,EAAE;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAM,IAAE,KAAK,aAAaA,GAAE,KAAK;AAAE,WAAO,IAAE,EAAE,QAAM;AAAA,EAAE;AAAA,EAAC,WAAWA,IAAE,GAAE;AAAC,IAAAF,GAAE,IAAI,CAAC;AAAE,UAAMG,KAAE,KAAK,aAAaH,GAAE,KAAK,GAAEI,KAAEF,GAAE;AAAQ,QAAGE,GAAE,aAAWD,GAAE,YAAW;AAAC,WAAK,gBAAgBH,IAAEI,IAAEJ,EAAC;AAAE,YAAMS,KAAEL,GAAE,eAAeJ,GAAE,KAAIA,GAAE,KAAK;AAAE,iBAAUG,MAAKD,GAAE,MAAM,KAAGC,GAAE,QAAMH,GAAE,OAAKG,GAAE,WAASM,MAAGN,GAAE,SAAOM,GAAE,QAAM;AAAA,IAAE;AAAC,QAAGL,GAAE,aAAWD,GAAE,YAAW;AAAC,YAAK,CAACM,IAAEJ,IAAEJ,IAAEK,EAAC,IAAEJ,GAAE,MAAM,OAAQ,CAACA,IAAEO,QAAKP,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAEO,GAAE,GAAG,GAAEP,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAEO,GAAE,GAAG,GAAEP,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAEO,GAAE,OAAO,GAAEP,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAEO,GAAE,KAAK,GAAEP,KAAI,CAAC,IAAE,GAAE,KAAG,GAAE,IAAE,GAAE,KAAG,CAAC,CAAC,GAAEK,KAAEJ,GAAE,eAAeH,GAAE,KAAIA,GAAE,KAAK,GAAEQ,KAAEJ,GAAE,cAAcD,GAAE,cAAcI,EAAC,CAAC,GAAE,IAAEH,GAAE,WAAWD,GAAE,WAAWH,GAAE,GAAG,CAAC,GAAE,IAAEI,GAAE,cAAcD,GAAE,cAAcI,KAAE,CAAC,CAAC,IAAE,GAAE,IAAEH,GAAE,WAAWD,GAAE,WAAWH,GAAE,MAAI,CAAC,CAAC,IAAE;AAAE,aAAM,EAAEQ,KAAEF,MAAG,IAAEL,MAAG,IAAEI,MAAG,IAAEI;AAAA,IAAE;AAAC,UAAMJ,KAAED,GAAE,eAAeJ,GAAE,KAAIA,GAAE,KAAK;AAAE,WAAOE,GAAE,MAAM,KAAM,CAAAA,OAAGA,GAAE,QAAMF,GAAE,OAAKE,GAAE,WAASG,MAAGH,GAAE,SAAOG,EAAE;AAAA,EAAC;AAAA,EAAC,gBAAgB,GAAEF,IAAEC,IAAE;AAAC,QAAG,EAAE,CAAC,IAAED,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,KAAK,OAAM;AAAC,YAAMA,KAAE,EAAE,KAAK,SAAS,gBAAgB,GAAEE,KAAE,CAACD,MAAGD,GAAE,MAAM,CAAC,IAAEA,GAAE,MAAM,CAAC;AAAG,QAAE,CAAC,KAAGE,IAAE,EAAE,CAAC,KAAGA;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,wBAAwBH,IAAE;AAAC,UAAM,IAAE,KAAK;AAAO,QAAG,KAAK,aAAaA,EAAC,EAAE,QAAO,KAAK,aAAaA,EAAC;AAAE,QAAGA,KAAE,EAAE,CAAC,EAAE,QAAO,KAAK,aAAa,EAAE,CAAC,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAE,EAAE,SAAO,GAAEA,KAAI,KAAGD,KAAE,EAAEC,EAAC,IAAE,EAAE,QAAO,KAAK,aAAa,EAAEA,KAAE,CAAC,CAAC;AAAE,WAAO,KAAK,aAAa,EAAE,EAAE,SAAO,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBD,IAAE;AAAC,UAAM,IAAE,KAAK;AAAO,WAAO,KAAK,aAAaA,EAAC,MAAIA,KAAE,EAAE,OAAQ,CAACO,IAAEN,OAAI,KAAK,IAAIA,KAAED,EAAC,IAAE,KAAK,IAAIO,KAAEP,EAAC,IAAEC,KAAEM,IAAG,EAAE,CAAC,CAAC,IAAG,KAAK,aAAaP,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAM,IAAE,KAAK;AAAO,QAAG,KAAK,aAAaA,EAAC,EAAE,QAAO,KAAK,aAAaA,EAAC,EAAE;AAAM,aAAQC,KAAE,EAAE,SAAO,GAAEA,MAAG,GAAEA,KAAI,KAAGD,KAAE,EAAEC,EAAC,GAAE;AAAC,UAAGA,OAAI,EAAE,SAAO,EAAE,QAAO,KAAK,aAAa,EAAE,EAAE,SAAO,CAAC,CAAC,EAAE;AAAM,aAAO,KAAK,aAAa,EAAEA,EAAC,CAAC,EAAE,SAAO,EAAEA,EAAC,IAAED,OAAI,EAAEC,EAAC,IAAE,EAAEA,KAAE,CAAC;AAAA,IAAE;AAAC,WAAO,KAAK,aAAa,EAAE,CAAC,CAAC,EAAE;AAAA,EAAK;AAAA,EAAC,YAAYD,IAAE;AAAC,WAAO,KAAK,SAAS,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE,GAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,aAAaD,GAAE,KAAK;AAAE,WAAOD,GAAE,IAAIC,EAAC,GAAE,EAAE,aAAWC,GAAE,aAAW,QAAM,EAAE,eAAaA,GAAE,eAAaF,GAAE,QAAM,EAAE,OAAMA,GAAE,MAAI,KAAK,MAAMC,GAAE,MAAIC,GAAE,aAAW,EAAE,aAAW,IAAG,GAAEF,GAAE,MAAI,KAAK,MAAMC,GAAE,MAAIC,GAAE,aAAW,EAAE,aAAW,IAAG,IAAGF;AAAA,EAAE;AAAC;", "names": ["r", "e", "o", "i", "l", "s", "n", "h", "a", "o", "s", "i", "n", "e", "o", "l", "s", "r", "a", "h", "t"]}