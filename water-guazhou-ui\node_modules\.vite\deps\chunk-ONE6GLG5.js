import {
  s
} from "./chunk-UVNYHPLJ.js";
import {
  i2 as i
} from "./chunk-2CM7MIII.js";

// node_modules/@arcgis/core/core/accessorSupport/tracking/ObservableValue.js
var t = class {
  constructor(e) {
    this._observable = new s(), this._value = e;
  }
  get() {
    return i(this._observable), this._value;
  }
  set(e) {
    e !== this._value && (this._value = e, this._observable.notify());
  }
};

export {
  t
};
//# sourceMappingURL=chunk-ONE6GLG5.js.map
