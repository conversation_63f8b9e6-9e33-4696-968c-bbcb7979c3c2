import {
  S,
  d
} from "./chunk-HTXGAKOK.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/mixins/ArcGISService.js
var p = (p2) => {
  let c = class extends p2 {
    get title() {
      if (this._get("title") && "defaults" !== this.originOf("title")) return this._get("title");
      if (this.url) {
        const t = d(this.url);
        if (r(t) && t.title) return t.title;
      }
      return this._get("title") || "";
    }
    set title(t) {
      this._set("title", t);
    }
    set url(t) {
      this._set("url", S(t, s.getLogger(this.declaredClass)));
    }
  };
  return e([y()], c.prototype, "title", null), e([y({ type: String })], c.prototype, "url", null), c = e([a("esri.layers.mixins.ArcGISService")], c), c;
};

export {
  p
};
//# sourceMappingURL=chunk-KTB2COPC.js.map
