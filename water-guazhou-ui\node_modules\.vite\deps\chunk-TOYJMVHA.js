import {
  E2,
  f
} from "./chunk-6G2NLXT7.js";
import {
  a as a3,
  i
} from "./chunk-LHO3WKNH.js";
import {
  n as n3
} from "./chunk-RFTQI4ZD.js";
import {
  t as t2
} from "./chunk-IEBU4QQL.js";
import {
  O as O2
} from "./chunk-CPQSD22U.js";
import {
  s
} from "./chunk-EM4JSU7Z.js";
import {
  E
} from "./chunk-FTRLEBHJ.js";
import {
  C,
  F,
  G as G2,
  L,
  M,
  P
} from "./chunk-4M3AMTD4.js";
import {
  e as e2
} from "./chunk-A7PY25IH.js";
import {
  G,
  O,
  a as a2
} from "./chunk-AVKOL7OR.js";
import {
  a,
  h,
  m
} from "./chunk-EIGTETCG.js";
import {
  _,
  o,
  z
} from "./chunk-MQAXMQFG.js";
import {
  n as n2,
  r as r2
} from "./chunk-36FLFRUE.js";
import {
  e2 as e,
  n
} from "./chunk-C5VMWMBD.js";
import {
  c
} from "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderModules/Float4sPassUniform.js
var e3 = class extends i {
  constructor(r6, e5, o5) {
    super(r6, "vec4", a3.Pass, (s3, o6, t4) => s3.setUniform4fv(r6, e5(o6, t4)), o5);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderModules/FloatsPassUniform.js
var o2 = class extends i {
  constructor(r6, o5, e5) {
    super(r6, "float", a3.Pass, (s3, e6, t4) => s3.setUniform1fv(r6, o5(e6, t4)), e5);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/AnimationTimer.js
var i2 = class {
  constructor() {
    this.enabled = true, this._time = n(0);
  }
  get time() {
    return this._time;
  }
  advance({ deltaTime: i3, fixedTime: m4 }) {
    return r(m4) ? this._time !== m4 && (this._time = m4, true) : (this._time = n(this._time + i3), 0 !== i3);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/ContentObject.js
var r3 = class {
  constructor() {
    this.id = e();
  }
  unload() {
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/ContentObjectType.js
var e4;
!function(e5) {
  e5[e5.Layer = 0] = "Layer", e5[e5.Object = 1] = "Object", e5[e5.Mesh = 2] = "Mesh", e5[e5.Line = 3] = "Line", e5[e5.Point = 4] = "Point", e5[e5.Material = 5] = "Material", e5[e5.Texture = 6] = "Texture", e5[e5.COUNT = 7] = "COUNT";
}(e4 || (e4 = {}));

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/DefaultVertexAttributeLocations.js
var E3 = /* @__PURE__ */ new Map([[O2.POSITION, 0], [O2.NORMAL, 1], [O2.UV0, 2], [O2.COLOR, 3], [O2.SIZE, 4], [O2.TANGENT, 4], [O2.AUXPOS1, 5], [O2.SYMBOLCOLOR, 5], [O2.AUXPOS2, 6], [O2.FEATUREATTRIBUTE, 6], [O2.INSTANCEFEATUREATTRIBUTE, 6], [O2.INSTANCECOLOR, 7], [O2.OBJECTANDLAYERIDCOLOR, 7], [O2.OBJECTANDLAYERIDCOLOR_INSTANCED, 7], [O2.MODEL, 8], [O2.MODELNORMAL, 12], [O2.MODELORIGINHI, 11], [O2.MODELORIGINLO, 15]]);

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/screenSizePerspectiveUtils.js
function l2(e5) {
  return Math.abs(e5 * e5 * e5);
}
function s2(e5, t4, a4) {
  const i3 = a4.parameters, r6 = a4.paddingPixelsOverride;
  return x.scale = Math.min(i3.divisor / (t4 - i3.offset), 1), x.factor = l2(e5), x.minPixelSize = i3.minPixelSize, x.paddingPixels = r6, x;
}
function c2(e5, t4) {
  return 0 === e5 ? t4.minPixelSize : t4.minPixelSize * (1 + 2 * t4.paddingPixels / e5);
}
function o3(t4, a4) {
  return Math.max(h(t4 * a4.scale, t4, a4.factor), c2(t4, a4));
}
function u(e5, t4, a4) {
  const i3 = s2(e5, t4, a4);
  return i3.minPixelSize = 0, i3.paddingPixels = 0, o3(1, i3);
}
function d(e5, t4, a4, i3) {
  i3.scale = u(e5, t4, a4), i3.factor = 0, i3.minPixelSize = a4.parameters.minPixelSize, i3.paddingPixels = a4.paddingPixelsOverride;
}
function f2(e5, t4, a4 = [0, 0]) {
  const i3 = Math.min(Math.max(t4.scale, c2(e5[1], t4) / Math.max(1e-5, e5[1])), 1);
  return a4[0] = e5[0] * i3, a4[1] = e5[1] * i3, a4;
}
function m2(e5, t4, a4, i3) {
  return o3(e5, s2(t4, a4, i3));
}
var h2 = { curvatureDependent: { min: { curvature: m(10), tiltAngle: m(12), scaleFallOffFactor: 0.5 }, max: { curvature: m(70), tiltAngle: m(40), scaleFallOffFactor: 0.8 } }, scaleStart: 0.3, scaleFallOffRange: 0.65, minPixelSize: 0 };
var x = { scale: 0, factor: 0, minPixelSize: 0, paddingPixels: 0 };

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/internal/MaterialUtil.js
var b = a2();
function x2(t4, n5, e5, o5, i3, r6) {
  if (t4.visible) if (t4.boundingInfo) {
    s(t4.type === e4.Mesh);
    const s3 = n5.tolerance;
    g(t4.boundingInfo, e5, o5, s3, i3, r6);
  } else {
    const n6 = t4.indices.get(O2.POSITION), s3 = t4.vertexAttributes.get(O2.POSITION);
    y(e5, o5, 0, n6.length / 3, n6, s3, void 0, i3, r6);
  }
}
var d2 = n2();
function g(t4, n5, i3, r6, s3, c4) {
  if (t(t4)) return;
  const f4 = O3(n5, i3, d2);
  if (G(b, t4.bbMin), O(b, t4.bbMax), r(s3) && s3.applyToAabb(b), V(b, n5, f4, r6)) {
    const { primitiveIndices: e5, indices: o5, position: f5 } = t4, l3 = e5 ? e5.length : o5.length / 3;
    if (l3 > U) {
      const e6 = t4.getChildren();
      if (void 0 !== e6) {
        for (const t5 of e6) g(t5, n5, i3, r6, s3, c4);
        return;
      }
    }
    y(n5, i3, 0, l3, o5, f5, e5, s3, c4);
  }
}
var M2 = n2();
function y(t4, n5, e5, i3, r6, s3, c4, f4, l3) {
  if (c4) return j(t4, n5, e5, i3, r6, s3, c4, f4, l3);
  const { data: a4, stride: u3 } = s3, m4 = t4[0], p2 = t4[1], h4 = t4[2], b2 = n5[0] - m4, x3 = n5[1] - p2, d3 = n5[2] - h4;
  for (let g2 = e5, y2 = 3 * e5; g2 < i3; ++g2) {
    let t5 = u3 * r6[y2++], n6 = a4[t5++], e6 = a4[t5++], i4 = a4[t5];
    t5 = u3 * r6[y2++];
    let s4 = a4[t5++], c5 = a4[t5++], j2 = a4[t5];
    t5 = u3 * r6[y2++];
    let v3 = a4[t5++], T2 = a4[t5++], O4 = a4[t5];
    r(f4) && ([n6, e6, i4] = f4.applyToVertex(n6, e6, i4, g2), [s4, c5, j2] = f4.applyToVertex(s4, c5, j2, g2), [v3, T2, O4] = f4.applyToVertex(v3, T2, O4, g2));
    const V2 = s4 - n6, L3 = c5 - e6, N2 = j2 - i4, A3 = v3 - n6, P3 = T2 - e6, S2 = O4 - i4, E5 = x3 * S2 - P3 * d3, U2 = d3 * A3 - S2 * b2, W = b2 * P3 - A3 * x3, k = V2 * E5 + L3 * U2 + N2 * W;
    if (Math.abs(k) <= Number.EPSILON) continue;
    const B = m4 - n6, C2 = p2 - e6, z2 = h4 - i4, H = B * E5 + C2 * U2 + z2 * W;
    if (k > 0) {
      if (H < 0 || H > k) continue;
    } else if (H > 0 || H < k) continue;
    const R = C2 * N2 - L3 * z2, Y = z2 * V2 - N2 * B, q = B * L3 - V2 * C2, w2 = b2 * R + x3 * Y + d3 * q;
    if (k > 0) {
      if (w2 < 0 || H + w2 > k) continue;
    } else if (w2 > 0 || H + w2 < k) continue;
    const D = (A3 * R + P3 * Y + S2 * q) / k;
    if (D >= 0) {
      l3(D, I(V2, L3, N2, A3, P3, S2, M2), g2, false);
    }
  }
}
function j(t4, n5, e5, i3, r6, s3, c4, f4, l3) {
  const { data: a4, stride: u3 } = s3, m4 = t4[0], p2 = t4[1], h4 = t4[2], b2 = n5[0] - m4, x3 = n5[1] - p2, d3 = n5[2] - h4;
  for (let g2 = e5; g2 < i3; ++g2) {
    const t5 = c4[g2];
    let n6 = 3 * t5, e6 = u3 * r6[n6++], i4 = a4[e6++], s4 = a4[e6++], y2 = a4[e6];
    e6 = u3 * r6[n6++];
    let j2 = a4[e6++], v3 = a4[e6++], T2 = a4[e6];
    e6 = u3 * r6[n6];
    let O4 = a4[e6++], V2 = a4[e6++], L3 = a4[e6];
    r(f4) && ([i4, s4, y2] = f4.applyToVertex(i4, s4, y2, g2), [j2, v3, T2] = f4.applyToVertex(j2, v3, T2, g2), [O4, V2, L3] = f4.applyToVertex(O4, V2, L3, g2));
    const N2 = j2 - i4, A3 = v3 - s4, P3 = T2 - y2, S2 = O4 - i4, E5 = V2 - s4, U2 = L3 - y2, W = x3 * U2 - E5 * d3, k = d3 * S2 - U2 * b2, B = b2 * E5 - S2 * x3, C2 = N2 * W + A3 * k + P3 * B;
    if (Math.abs(C2) <= Number.EPSILON) continue;
    const z2 = m4 - i4, H = p2 - s4, R = h4 - y2, Y = z2 * W + H * k + R * B;
    if (C2 > 0) {
      if (Y < 0 || Y > C2) continue;
    } else if (Y > 0 || Y < C2) continue;
    const q = H * P3 - A3 * R, w2 = R * N2 - P3 * z2, D = z2 * A3 - N2 * H, F2 = b2 * q + x3 * w2 + d3 * D;
    if (C2 > 0) {
      if (F2 < 0 || Y + F2 > C2) continue;
    } else if (F2 > 0 || Y + F2 < C2) continue;
    const G3 = (S2 * q + E5 * w2 + U2 * D) / C2;
    if (G3 >= 0) {
      l3(G3, I(N2, A3, P3, S2, E5, U2, M2), t5, false);
    }
  }
}
var v = n2();
var T = n2();
function I(t4, n5, e5, o5, c4, f4, l3) {
  return o(v, t4, n5, e5), o(T, o5, c4, f4), _(l3, v, T), z(l3, l3), l3;
}
function O3(t4, n5, e5) {
  return o(e5, 1 / (n5[0] - t4[0]), 1 / (n5[1] - t4[1]), 1 / (n5[2] - t4[2]));
}
function V(t4, n5, e5, o5) {
  return L2(t4, n5, e5, o5, 1 / 0);
}
function L2(t4, n5, e5, o5, i3) {
  const r6 = (t4[0] - o5 - n5[0]) * e5[0], s3 = (t4[3] + o5 - n5[0]) * e5[0];
  let c4 = Math.min(r6, s3), f4 = Math.max(r6, s3);
  const l3 = (t4[1] - o5 - n5[1]) * e5[1], a4 = (t4[4] + o5 - n5[1]) * e5[1];
  if (f4 = Math.min(f4, Math.max(l3, a4)), f4 < 0) return false;
  if (c4 = Math.max(c4, Math.min(l3, a4)), c4 > f4) return false;
  const u3 = (t4[2] - o5 - n5[2]) * e5[2], m4 = (t4[5] + o5 - n5[2]) * e5[2];
  return f4 = Math.min(f4, Math.max(u3, m4)), !(f4 < 0) && (c4 = Math.max(c4, Math.min(u3, m4)), !(c4 > f4) && c4 < i3);
}
function N(t4, e5, i3, r6, s3) {
  let c4 = (i3.screenLength || 0) * t4.pixelRatio;
  r(s3) && (c4 = m2(c4, r6, e5, s3));
  const f4 = c4 * Math.tan(0.5 * t4.fovY) / (0.5 * t4.fullHeight);
  return a(f4 * e5, i3.minWorldLength || 0, null != i3.maxWorldLength ? i3.maxWorldLength : 1 / 0);
}
function A(t4, n5) {
  const e5 = n5 ? A(n5) : {};
  for (const o5 in t4) {
    let n6 = t4[o5];
    n6 && n6.forEach && (n6 = S(n6)), null == n6 && o5 in e5 || (e5[o5] = n6);
  }
  return e5;
}
function P2(n5, e5) {
  let o5 = false;
  for (const i3 in e5) {
    const r6 = e5[i3];
    void 0 !== r6 && (Array.isArray(r6) ? null === n5[i3] ? (n5[i3] = r6.slice(), o5 = true) : c(n5[i3], r6) && (o5 = true) : n5[i3] !== r6 && (o5 = true, n5[i3] = r6));
  }
  return o5;
}
function S(t4) {
  const n5 = [];
  return t4.forEach((t5) => n5.push(t5)), n5;
}
var E4 = { multiply: 1, ignore: 2, replace: 3, tint: 4 };
var U = 1e3;

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/Material.js
var h3 = class extends r3 {
  constructor(e5, t4) {
    super(), this.type = e4.Material, this.supportsEdges = false, this._visible = true, this._renderPriority = 0, this._insertOrder = 0, this._vertexAttributeLocations = E3, this._pp0 = r2(0, 0, 1), this._pp1 = r2(0, 0, 0), this._parameters = A(e5, t4), this.validateParameters(this._parameters);
  }
  dispose() {
  }
  get parameters() {
    return this._parameters;
  }
  update(e5) {
    return false;
  }
  setParameters(e5, r6 = true) {
    P2(this._parameters, e5) && (this.validateParameters(this._parameters), r6 && this.parametersChanged());
  }
  validateParameters(e5) {
  }
  get visible() {
    return this._visible;
  }
  set visible(e5) {
    e5 !== this._visible && (this._visible = e5, this.parametersChanged());
  }
  shouldRender(e5) {
    return this.isVisible() && this.isVisibleForOutput(e5.output) && 0 != (this.renderOccluded & e5.renderOccludedMask);
  }
  isVisibleForOutput(e5) {
    return true;
  }
  get renderOccluded() {
    return this.parameters.renderOccluded;
  }
  get renderPriority() {
    return this._renderPriority;
  }
  set renderPriority(e5) {
    e5 !== this._renderPriority && (this._renderPriority = e5, this.parametersChanged());
  }
  get insertOrder() {
    return this._insertOrder;
  }
  set insertOrder(e5) {
    e5 !== this._insertOrder && (this._insertOrder = e5, this.parametersChanged());
  }
  get vertexAttributeLocations() {
    return this._vertexAttributeLocations;
  }
  isVisible() {
    return this._visible;
  }
  parametersChanged() {
    r(this.repository) && this.repository.materialChanged(this);
  }
  intersectDraped(e5, r6, t4, s3, i3, a4) {
    return this._pp0[0] = this._pp1[0] = s3[0], this._pp0[1] = this._pp1[1] = s3[1], this.intersect(e5, r6, t4, this._pp0, this._pp1, i3);
  }
};
var c3;
!function(e5) {
  e5[e5.Occlude = 1] = "Occlude", e5[e5.Transparent = 2] = "Transparent", e5[e5.OccludeAndTransparent = 4] = "OccludeAndTransparent", e5[e5.OccludeAndTransparentStencil = 8] = "OccludeAndTransparentStencil", e5[e5.Opaque = 16] = "Opaque";
}(c3 || (c3 = {}));
var u2 = class extends n3 {
  constructor() {
    super(...arguments), this.renderOccluded = c3.Occlude;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/VisualVariablePassParameters.js
var v2 = class extends u2 {
  constructor() {
    super(...arguments), this.vvSizeEnabled = false, this.vvSizeMinSize = r2(1, 1, 1), this.vvSizeMaxSize = r2(100, 100, 100), this.vvSizeOffset = r2(0, 0, 0), this.vvSizeFactor = r2(1, 1, 1), this.vvSizeValue = r2(1, 1, 1), this.vvColorEnabled = false, this.vvColorValues = [0, 0, 0, 0, 0, 0, 0, 0], this.vvColorColors = [1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0], this.vvOpacityEnabled = false, this.vvOpacityValues = [0, 0, 0, 0, 0, 0, 0, 0], this.vvOpacityOpacities = [1, 1, 1, 1, 1, 1, 1, 1], this.vvSymbolAnchor = [0, 0, 0], this.vvSymbolRotationMatrix = e2();
  }
};
var o4 = 8;

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/DefaultVertexBufferLayouts.js
var n4 = [new t2(O2.POSITION, 3, C.FLOAT, 0, 12)];
var w = [new t2(O2.POSITION, 3, C.FLOAT, 0, 20), new t2(O2.UV0, 2, C.FLOAT, 12, 20)];
var I2 = [new t2(O2.POSITION, 3, C.FLOAT, 0, 32), new t2(O2.NORMAL, 3, C.FLOAT, 12, 32), new t2(O2.UV0, 2, C.FLOAT, 24, 32)];
var r4 = [new t2(O2.POSITION, 3, C.FLOAT, 0, 16), new t2(O2.COLOR, 4, C.UNSIGNED_BYTE, 12, 16)];
var t3 = [new t2(O2.POSITION, 2, C.FLOAT, 0, 8)];
var A2 = [new t2(O2.POSITION, 2, C.FLOAT, 0, 16), new t2(O2.UV0, 2, C.FLOAT, 8, 16)];

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/VertexArrayObject.js
var r5 = class extends f {
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/glUtil3D.js
function f3(i3, m4 = t3, T2 = E3, l3 = -1, u3 = 1) {
  let f4 = null;
  if (m4 === A2) f4 = new Float32Array([l3, l3, 0, 0, u3, l3, 1, 0, l3, u3, 0, 1, u3, u3, 1, 1]);
  else f4 = new Float32Array([l3, l3, u3, l3, l3, u3, u3, u3]);
  return new r5(i3, T2, { geometry: m4 }, { geometry: E2.createVertex(i3, F.STATIC_DRAW, f4) });
}
function p(e5) {
  return new E(e5, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, dataType: G2.UNSIGNED_BYTE, samplingMode: L.NEAREST, width: 1, height: 1 }, new Uint8Array([255, 255, 255, 255]));
}

export {
  r3 as r,
  e4 as e,
  E3 as E,
  r5 as r2,
  f3 as f,
  p,
  i2 as i,
  o3 as o,
  d,
  f2,
  x2 as x,
  y,
  V,
  N,
  A,
  P2 as P,
  E4 as E2,
  h3 as h,
  c3 as c,
  u2 as u,
  e3 as e2,
  o2,
  v2 as v,
  o4 as o3
};
//# sourceMappingURL=chunk-TOYJMVHA.js.map
