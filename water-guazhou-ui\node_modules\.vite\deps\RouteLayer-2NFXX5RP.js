import {
  A,
  D,
  D2,
  O as O2,
  R,
  S as S3,
  U as U2,
  a as a4,
  a2 as a5,
  b as b2,
  b2 as b3,
  f as f3,
  g,
  h,
  h2,
  i as i4,
  k,
  l as l2,
  l2 as l3,
  n as n3,
  o as o5,
  r as r3,
  s as s6,
  w as w3,
  y as y3
} from "./chunk-OD5TAVCV.js";
import {
  i as i3
} from "./chunk-I73RJE3H.js";
import "./chunk-OWSDEANX.js";
import {
  t as t4
} from "./chunk-HUA67YB5.js";
import {
  a as a6
} from "./chunk-3MMXGUA5.js";
import {
  v as v3
} from "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import {
  f as f2,
  i as i2
} from "./chunk-XBS7QZIQ.js";
import {
  S
} from "./chunk-HTXGAKOK.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import {
  u as u2
} from "./chunk-55WN4LCX.js";
import {
  c as c2
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import {
  _n,
  rn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import {
  x
} from "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import {
  x as x2
} from "./chunk-KE7SPCM7.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  a as a3
} from "./chunk-Q4VCSCSY.js";
import {
  n as n2
} from "./chunk-MIA6BJ32.js";
import {
  t as t3
} from "./chunk-NGPCXWDX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import {
  o as o4
} from "./chunk-BS3GJQ77.js";
import "./chunk-IOBN373Z.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-NEJXVYTI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-VJW7RCN7.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  a as a2
} from "./chunk-QUHG7NMD.js";
import "./chunk-ZL6CFFJK.js";
import "./chunk-TWFTBWXP.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import {
  g as g2
} from "./chunk-TLKX5XIJ.js";
import {
  k as k2
} from "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import {
  j3
} from "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import {
  S as S2
} from "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import {
  b as b4
} from "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j as j2
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import {
  n
} from "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import {
  v as v2
} from "./chunk-ZACBBT3Y.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  u
} from "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  y as y2
} from "./chunk-VX6YUKFM.js";
import {
  m as m2
} from "./chunk-6ILWLF72.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  N,
  c,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import {
  L,
  jt
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import {
  o as o3
} from "./chunk-PEEUPDEG.js";
import {
  o as o2
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s as s5
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  v
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s4
} from "./chunk-4RZONHOY.js";
import {
  s as s3
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import {
  s as s2
} from "./chunk-XOI5RUBC.js";
import {
  o,
  t as t2
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e as e2,
  r,
  s,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/RouteStopSymbols.js
var p = class extends l {
  constructor(o6) {
    super(o6), this.break = new y2({ color: [255, 255, 255], size: 12, outline: { color: [0, 122, 194], width: 3 } }), this.first = new y2({ color: [0, 255, 0], size: 20, outline: { color: [255, 255, 255], width: 4 } }), this.unlocated = new y2({ color: [255, 0, 0], size: 12, outline: { color: [255, 255, 255], width: 3 } }), this.last = new y2({ color: [255, 0, 0], size: 20, outline: { color: [255, 255, 255], width: 4 } }), this.middle = new y2({ color: [51, 51, 51], size: 12, outline: { color: [0, 122, 194], width: 3 } }), this.waypoint = new y2({ color: [255, 255, 255], size: 12, outline: { color: [0, 122, 194], width: 3 } });
  }
};
e([y({ types: j3 })], p.prototype, "break", void 0), e([y({ types: j3 })], p.prototype, "first", void 0), e([y({ types: j3 })], p.prototype, "unlocated", void 0), e([y({ types: j3 })], p.prototype, "last", void 0), e([y({ types: j3 })], p.prototype, "middle", void 0), e([y({ types: j3 })], p.prototype, "waypoint", void 0), p = e([a("esri.layers.support.RouteStopSymbols")], p);
var l4 = p;

// node_modules/@arcgis/core/layers/support/RouteSymbols.js
var n4 = class extends l {
  constructor(o6) {
    super(o6), this.directionLines = new m2({ color: [0, 122, 194], width: 6 }), this.directionPoints = new y2({ color: [255, 255, 255], size: 6, outline: { color: [0, 122, 194], width: 2 } }), this.pointBarriers = new y2({ style: "x", size: 10, outline: { color: [255, 0, 0], width: 3 } }), this.polygonBarriers = new S2({ color: [255, 170, 0, 0.6], outline: { width: 7.5, color: [255, 0, 0, 0.6] } }), this.polylineBarriers = new m2({ width: 7.5, color: [255, 85, 0, 0.7] }), this.routeInfo = new m2({ width: 8, color: [20, 89, 127] }), this.stops = new l4();
  }
};
e([y({ types: j3 })], n4.prototype, "directionLines", void 0), e([y({ types: j3 })], n4.prototype, "directionPoints", void 0), e([y({ types: j3 })], n4.prototype, "pointBarriers", void 0), e([y({ types: j3 })], n4.prototype, "polygonBarriers", void 0), e([y({ types: j3 })], n4.prototype, "polylineBarriers", void 0), e([y({ types: j3 })], n4.prototype, "routeInfo", void 0), e([y({ type: l4 })], n4.prototype, "stops", void 0), n4 = e([a("esri.layers.support.RouteSymbols")], n4);
var y4 = n4;

// node_modules/@arcgis/core/rest/support/NetworkAttribute.js
var u3 = class extends l {
  constructor(t5) {
    super(t5), this.dataType = null, this.name = null, this.parameterNames = null, this.restrictionUsageParameterName = null, this.timeNeutralAttributeName = null, this.trafficSupport = null, this.units = null, this.usageType = null;
  }
};
e([y({ type: String })], u3.prototype, "dataType", void 0), e([o3(U2, { ignoreUnknown: false })], u3.prototype, "name", void 0), e([y({ type: [String] })], u3.prototype, "parameterNames", void 0), e([y({ type: String })], u3.prototype, "restrictionUsageParameterName", void 0), e([o3(y3, { ignoreUnknown: false })], u3.prototype, "timeNeutralAttributeName", void 0), e([y({ type: String })], u3.prototype, "trafficSupport", void 0), e([o3(s6)], u3.prototype, "units", void 0), e([o3(R)], u3.prototype, "usageType", void 0), u3 = e([a("esri.rest.support.NetworkAttribute")], u3);
var m3 = u3;

// node_modules/@arcgis/core/rest/support/NetworkDataset.js
var p2 = class extends l {
  constructor(t5) {
    super(t5), this.buildTime = null, this.name = null, this.networkAttributes = null, this.networkSources = null, this.state = null;
  }
};
e([y({ type: Number })], p2.prototype, "buildTime", void 0), e([y({ type: String })], p2.prototype, "name", void 0), e([y({ type: [m3] })], p2.prototype, "networkAttributes", void 0), e([y()], p2.prototype, "networkSources", void 0), e([y({ type: String })], p2.prototype, "state", void 0), p2 = e([a("esri.rest.support.NetworkDataset")], p2);
var i5 = p2;

// node_modules/@arcgis/core/rest/support/NetworkServiceDescription.js
var m4 = class extends l {
  constructor(t5) {
    super(t5), this.accumulateAttributeNames = null, this.attributeParameterValues = null, this.currentVersion = null, this.defaultTravelMode = null, this.directionsLanguage = null, this.directionsLengthUnits = null, this.directionsSupportedLanguages = null, this.directionsTimeAttribute = null, this.hasZ = null, this.impedance = null, this.networkDataset = null, this.supportedTravelModes = null;
  }
  readAccumulateAttributes(t5) {
    return t(t5) ? null : t5.map((t6) => U2.fromJSON(t6));
  }
  writeAccumulateAttributes(t5, e3, o6) {
    !t(t5) && t5.length && (e3[o6] = t5.map((t6) => U2.toJSON(t6)));
  }
  readDefaultTravelMode(t5, e3) {
    var _a, _b;
    const r4 = ((_a = e3.supportedTravelModes) == null ? void 0 : _a.find(({ id: t6 }) => t6 === e3.defaultTravelMode)) ?? ((_b = e3.supportedTravelModes) == null ? void 0 : _b.find(({ itemId: t6 }) => t6 === e3.defaultTravelMode));
    return r4 ? A.fromJSON(r4) : null;
  }
};
e([y()], m4.prototype, "accumulateAttributeNames", void 0), e([o2("accumulateAttributeNames")], m4.prototype, "readAccumulateAttributes", null), e([r2("accumulateAttributeNames")], m4.prototype, "writeAccumulateAttributes", null), e([y()], m4.prototype, "attributeParameterValues", void 0), e([y()], m4.prototype, "currentVersion", void 0), e([y()], m4.prototype, "defaultTravelMode", void 0), e([o2("defaultTravelMode", ["defaultTravelMode", "supportedTravelModes"])], m4.prototype, "readDefaultTravelMode", null), e([y()], m4.prototype, "directionsLanguage", void 0), e([o3(r3)], m4.prototype, "directionsLengthUnits", void 0), e([y()], m4.prototype, "directionsSupportedLanguages", void 0), e([o3(y3, { ignoreUnknown: false })], m4.prototype, "directionsTimeAttribute", void 0), e([y()], m4.prototype, "hasZ", void 0), e([o3(U2, { ignoreUnknown: false })], m4.prototype, "impedance", void 0), e([y({ type: i5 })], m4.prototype, "networkDataset", void 0), e([y({ type: [A] })], m4.prototype, "supportedTravelModes", void 0), m4 = e([a("esri.rest.support.NetworkServiceDescription")], m4);
var v4 = m4;

// node_modules/@arcgis/core/rest/networkService.js
var f4 = s3.getLogger("esri.rest.networkService");
function u4(e3, r4, t5, o6) {
  o6[t5] = [r4.length, r4.length + e3.length], e3.forEach((e4) => {
    r4.push(e4.geometry);
  });
}
function c3(e3, r4) {
  for (let t5 = 0; t5 < r4.length; t5++) {
    const o6 = e3[r4[t5]];
    if (o6 && o6.length) for (const e4 of o6) e4.z = void 0;
  }
  f4.warnOnce("The remote Network Analysis service is powered by a network dataset which is not Z-aware.\nZ-coordinates of the input geometry are ignored.");
}
function d(e3, r4) {
  for (let t5 = 0; t5 < r4.length; t5++) {
    const s8 = e3[r4[t5]];
    if (s8 && s8.length) {
      for (const e4 of s8) if (r(e4) && e4.hasZ) return true;
    }
  }
  return false;
}
async function p3(t5, o6, s8) {
  if (!t5) throw new s4("network-service:missing-url", "Url to Network service is missing");
  const a9 = i2({ f: "json", token: o6 }, s8), { data: i7 } = await U(t5, a9), f6 = i7.currentVersion >= 10.4 ? m5(t5, o6, s8) : v5(t5, s8), { defaultTravelMode: u8, supportedTravelModes: c12 } = await f6;
  return i7.defaultTravelMode = u8, i7.supportedTravelModes = c12, v4.fromJSON(i7);
}
async function v5(r4, t5) {
  var _a, _b, _c;
  const o6 = i2({ f: "json" }, t5), { data: l8 } = await U(r4.replace(/\/rest\/.*$/i, "/info"), o6);
  if (!l8 || !l8.owningSystemUrl) return { supportedTravelModes: [], defaultTravelMode: null };
  const { owningSystemUrl: f6 } = l8, u8 = jt(f6) + "/sharing/rest/portals/self", { data: c12 } = await U(u8, o6), d2 = t2("helperServices.routingUtilities.url", c12);
  if (!d2) return { supportedTravelModes: [], defaultTravelMode: null };
  const p7 = f2(f6), v6 = /\/solve$/i.test(p7.path) ? "Route" : /\/solveclosestfacility$/i.test(p7.path) ? "ClosestFacility" : "ServiceAreas", m8 = i2({ f: "json", serviceName: v6 }, t5), h3 = jt(d2) + "/GetTravelModes/execute", g3 = await U(h3, m8), w4 = [];
  let T = null;
  if ((_b = (_a = g3 == null ? void 0 : g3.data) == null ? void 0 : _a.results) == null ? void 0 : _b.length) {
    const e3 = g3.data.results;
    for (const r5 of e3) if ("supportedTravelModes" === r5.paramName) {
      if ((_c = r5.value) == null ? void 0 : _c.features) {
        for (const { attributes: e4 } of r5.value.features) if (e4) {
          const r6 = JSON.parse(e4.TravelMode);
          w4.push(r6);
        }
      }
    } else "defaultTravelMode" === r5.paramName && (T = r5.value);
  }
  return { supportedTravelModes: w4, defaultTravelMode: T };
}
async function m5(t5, o6, s8) {
  try {
    const r4 = i2({ f: "json", token: o6 }, s8), i7 = jt(t5) + "/retrieveTravelModes", { data: { supportedTravelModes: l8, defaultTravelMode: f6 } } = await U(i7, r4);
    return { supportedTravelModes: l8, defaultTravelMode: f6 };
  } catch (i7) {
    throw new s4("network-service:retrieveTravelModes", "Could not get to the NAServer's retrieveTravelModes.", { error: i7 });
  }
}

// node_modules/@arcgis/core/rest/support/NAMessage.js
var p4 = new s5({ 0: "informative", 1: "process-definition", 2: "process-start", 3: "process-stop", 50: "warning", 100: "error", 101: "empty", 200: "abort" });
var c4 = class extends a6 {
  constructor(r4) {
    super(r4), this.type = null;
  }
};
e([y({ type: String, json: { read: p4.read, write: p4.write } })], c4.prototype, "type", void 0), c4 = e([a("esri.rest.support.NAMessage")], c4);
var a7 = c4;

// node_modules/@arcgis/core/rest/support/DirectionsString.js
var c5 = class extends l {
  constructor(r4) {
    super(r4);
  }
};
e([y({ json: { read: { source: "string" } } })], c5.prototype, "text", void 0), e([o3(b2, { name: "stringType" })], c5.prototype, "type", void 0), c5 = e([a("esri.rest.support.DirectionsString")], c5);
var i6 = c5;

// node_modules/@arcgis/core/rest/support/DirectionsEvent.js
var a8 = class extends l {
  constructor(r4) {
    super(r4), this.arriveTime = null, this.arriveTimeOffset = null, this.geometry = null, this.strings = null;
  }
  readArriveTimeOffset(r4, e3) {
    return a5(e3.ETA, e3.arriveTimeUTC);
  }
  readGeometry(r4, e3) {
    return w.fromJSON(e3.point);
  }
};
e([y({ type: Date, json: { read: { source: "arriveTimeUTC" } } })], a8.prototype, "arriveTime", void 0), e([y()], a8.prototype, "arriveTimeOffset", void 0), e([o2("arriveTimeOffset", ["arriveTimeUTC", "ETA"])], a8.prototype, "readArriveTimeOffset", null), e([y({ type: w })], a8.prototype, "geometry", void 0), e([o2("geometry", ["point"])], a8.prototype, "readGeometry", null), e([y({ type: [i6] })], a8.prototype, "strings", void 0), a8 = e([a("esri.rest.support.DirectionsEvent")], a8);
var c6 = a8;

// node_modules/@arcgis/core/rest/support/DirectionsFeature.js
function m6(r4) {
  if (t(r4) || "" === r4) return null;
  let e3 = 0, t5 = 0, o6 = 0, p7 = 0;
  const n7 = [];
  let c12, i7, a9, m8, u8, l8, f6, y7, d2 = 0, h3 = 0, j4 = 0;
  if (u8 = r4.match(/((\+|\-)[^\+\-\|]+|\|)/g), u8 || (u8 = []), 0 === parseInt(u8[d2], 32)) {
    d2 = 2;
    const r5 = parseInt(u8[d2], 32);
    d2++, l8 = parseInt(u8[d2], 32), d2++, 1 & r5 && (h3 = u8.indexOf("|") + 1, f6 = parseInt(u8[h3], 32), h3++), 2 & r5 && (j4 = u8.indexOf("|", h3) + 1, y7 = parseInt(u8[j4], 32), j4++);
  } else l8 = parseInt(u8[d2], 32), d2++;
  for (; d2 < u8.length && "|" !== u8[d2]; ) {
    c12 = parseInt(u8[d2], 32) + e3, d2++, e3 = c12, i7 = parseInt(u8[d2], 32) + t5, d2++, t5 = i7;
    const r5 = [c12 / l8, i7 / l8];
    h3 && (m8 = parseInt(u8[h3], 32) + o6, h3++, o6 = m8, r5.push(m8 / f6)), j4 && (a9 = parseInt(u8[j4], 32) + p7, j4++, p7 = a9, r5.push(a9 / y7)), n7.push(r5);
  }
  return { paths: [n7], hasZ: h3 > 0, hasM: j4 > 0 };
}
var u5 = class extends g2 {
  constructor(r4) {
    super(r4), this.events = null, this.strings = null;
  }
  readGeometry(r4, e3) {
    const s8 = m6(e3.compressedGeometry);
    return r(s8) ? m.fromJSON(s8) : null;
  }
};
e([y({ type: [c6] })], u5.prototype, "events", void 0), e([o2("geometry", ["compressedGeometry"])], u5.prototype, "readGeometry", null), e([y({ type: [i6] })], u5.prototype, "strings", void 0), u5 = e([a("esri.rest.support.DirectionsFeature")], u5);
var l5 = u5;

// node_modules/@arcgis/core/rest/support/DirectionsFeatureSet.js
function u6(e3, t5) {
  if (0 === e3.length) return new m({ spatialReference: t5 });
  const r4 = [];
  for (const n7 of e3) for (const e4 of n7.paths) r4.push(...e4);
  const o6 = [];
  r4.forEach((e4, t6) => {
    0 !== t6 && e4[0] === r4[t6 - 1][0] && e4[1] === r4[t6 - 1][1] || o6.push(e4);
  });
  const { hasM: s8, hasZ: a9 } = e3[0];
  return new m({ hasM: s8, hasZ: a9, paths: [o6], spatialReference: t5 });
}
var c7 = class extends x2 {
  constructor(e3) {
    super(e3), this.extent = null, this.features = [], this.geometryType = "polyline", this.routeId = null, this.routeName = null, this.totalDriveTime = null, this.totalLength = null, this.totalTime = null;
  }
  readFeatures(e3, r4) {
    if (!e3) return [];
    const o6 = r4.summary.envelope.spatialReference ?? r4.spatialReference, s8 = o6 && f.fromJSON(o6);
    return e3.map((e4) => {
      const r5 = l5.fromJSON(e4);
      if (r(r5.geometry) && (r5.geometry.spatialReference = s8), r(r5.events)) for (const o7 of r5.events) r(o7.geometry) && (o7.geometry.spatialReference = s8);
      return r5;
    });
  }
  get mergedGeometry() {
    if (!this.features) return null;
    return u6(this.features.map(({ geometry: e3 }) => e2(e3)), this.extent.spatialReference);
  }
  get strings() {
    return this.features.map(({ strings: e3 }) => e3).flat().filter(r);
  }
};
e([y({ type: w2, json: { read: { source: "summary.envelope" } } })], c7.prototype, "extent", void 0), e([y({ nonNullable: true })], c7.prototype, "features", void 0), e([o2("features")], c7.prototype, "readFeatures", null), e([y()], c7.prototype, "geometryType", void 0), e([y({ readOnly: true })], c7.prototype, "mergedGeometry", null), e([y()], c7.prototype, "routeId", void 0), e([y()], c7.prototype, "routeName", void 0), e([y({ value: null, readOnly: true })], c7.prototype, "strings", null), e([y({ json: { read: { source: "summary.totalDriveTime" } } })], c7.prototype, "totalDriveTime", void 0), e([y({ json: { read: { source: "summary.totalLength" } } })], c7.prototype, "totalLength", void 0), e([y({ json: { read: { source: "summary.totalTime" } } })], c7.prototype, "totalTime", void 0), c7 = e([a("esri.rest.support.DirectionsFeatureSet")], c7);
var f5 = c7;

// node_modules/@arcgis/core/rest/support/RouteResult.js
var n5 = class extends l {
  constructor(t5) {
    super(t5), this.directionLines = null, this.directionPoints = null, this.directions = null, this.route = null, this.routeName = null, this.stops = null, this.traversedEdges = null, this.traversedJunctions = null, this.traversedTurns = null;
  }
};
e([y({ type: x2, json: { write: true } })], n5.prototype, "directionLines", void 0), e([y({ type: x2, json: { write: true } })], n5.prototype, "directionPoints", void 0), e([y({ type: f5, json: { write: true } })], n5.prototype, "directions", void 0), e([y({ type: g2, json: { write: true } })], n5.prototype, "route", void 0), e([y({ type: String, json: { write: true } })], n5.prototype, "routeName", void 0), e([y({ type: [g2], json: { write: true } })], n5.prototype, "stops", void 0), e([y({ type: x2, json: { write: true } })], n5.prototype, "traversedEdges", void 0), e([y({ type: x2, json: { write: true } })], n5.prototype, "traversedJunctions", void 0), e([y({ type: x2, json: { write: true } })], n5.prototype, "traversedTurns", void 0), n5 = e([a("esri.rest.support.RouteResult")], n5);
var u7 = n5;

// node_modules/@arcgis/core/rest/support/RouteSolveResult.js
function n6(r4) {
  return r4 ? x2.fromJSON(r4).features.filter(r) : [];
}
var y5 = class extends l {
  constructor(r4) {
    super(r4), this.messages = null, this.pointBarriers = null, this.polylineBarriers = null, this.polygonBarriers = null, this.routeResults = null;
  }
  readPointBarriers(r4, o6) {
    return n6(o6.barriers);
  }
  readPolylineBarriers(r4) {
    return n6(r4);
  }
  readPolygonBarriers(r4) {
    return n6(r4);
  }
};
e([y({ type: [a7] })], y5.prototype, "messages", void 0), e([y({ type: [g2] })], y5.prototype, "pointBarriers", void 0), e([o2("pointBarriers", ["barriers"])], y5.prototype, "readPointBarriers", null), e([y({ type: [g2] })], y5.prototype, "polylineBarriers", void 0), e([o2("polylineBarriers")], y5.prototype, "readPolylineBarriers", null), e([y({ type: [g2] })], y5.prototype, "polygonBarriers", void 0), e([o2("polygonBarriers")], y5.prototype, "readPolygonBarriers", null), e([y({ type: [u7] })], y5.prototype, "routeResults", void 0), y5 = e([a("esri.rest.support.RouteSolveResult")], y5);
var c8 = y5;

// node_modules/@arcgis/core/rest/route.js
function l6(e3) {
  return e3 instanceof x2;
}
async function m7(r4, t5, p7) {
  const c12 = [], m8 = [], d2 = {}, g3 = {}, h3 = f2(r4), { path: R3 } = h3;
  l6(t5.stops) && u4(t5.stops.features, m8, "stops.features", d2), l6(t5.pointBarriers) && u4(t5.pointBarriers.features, m8, "pointBarriers.features", d2), l6(t5.polylineBarriers) && u4(t5.polylineBarriers.features, m8, "polylineBarriers.features", d2), l6(t5.polygonBarriers) && u4(t5.polygonBarriers.features, m8, "polygonBarriers.features", d2);
  const v6 = await v3(m8);
  for (const e3 in d2) {
    const r5 = d2[e3];
    c12.push(e3), g3[e3] = v6.slice(r5[0], r5[1]);
  }
  if (d(g3, c12)) {
    let e3 = null;
    try {
      e3 = await p3(R3, t5.apiKey, p7);
    } catch {
    }
    e3 && !e3.hasZ && c3(g3, c12);
  }
  for (const e3 in g3) g3[e3].forEach((r5, s8) => {
    t5.get(e3)[s8].geometry = r5;
  });
  const B = { ...p7, query: { ...h3.query, ...n3(t5), f: "json" } }, E = R3.endsWith("/solve") ? R3 : `${R3}/solve`, { data: T } = await U(E, B);
  return y6(T);
}
function y6(e3) {
  const { barriers: s8, directionLines: o6, directionPoints: a9, directions: i7, messages: n7, polygonBarriers: u8, polylineBarriers: f6, routes: p7, stops: l8, traversedEdges: m8, traversedJunctions: y7, traversedTurns: d2 } = e3, g3 = (e4) => {
    const r4 = R3.find((r5) => r5.routeName === e4);
    if (r(r4)) return r4;
    const s9 = { routeId: R3.length + 1, routeName: e4 };
    return R3.push(s9), s9;
  }, h3 = (e4) => {
    const r4 = R3.find((r5) => r5.routeId === e4);
    if (r(r4)) return r4;
    const s9 = { routeId: e4, routeName: null };
    return R3.push(s9), s9;
  }, R3 = [];
  p7 == null ? void 0 : p7.features.forEach((e4, r4) => {
    e4.geometry.spatialReference = p7.spatialReference;
    const t5 = e4.attributes.Name, s9 = r4 + 1;
    R3.push({ routeId: s9, routeName: t5, route: e4 });
  }), i7 == null ? void 0 : i7.forEach((e4) => {
    const { routeName: r4 } = e4;
    g3(r4).directions = e4;
  });
  const v6 = ((l8 == null ? void 0 : l8.features.every((e4) => t(e4.attributes.RouteName))) ?? false) && R3.length > 0 ? R3[0].routeName : null;
  return l8 == null ? void 0 : l8.features.forEach((e4) => {
    var r4;
    e4.geometry && ((r4 = e4.geometry).spatialReference ?? (r4.spatialReference = l8.spatialReference));
    const t5 = v6 ?? e4.attributes.RouteName, s9 = g3(t5);
    s9.stops ?? (s9.stops = []), s9.stops.push(e4);
  }), o6 == null ? void 0 : o6.features.forEach((e4) => {
    const r4 = e4.attributes.RouteID, t5 = h3(r4), { geometryType: s9, spatialReference: a10 } = o6;
    t5.directionLines ?? (t5.directionLines = { features: [], geometryType: s9, spatialReference: a10 }), t5.directionLines.features.push(e4);
  }), a9 == null ? void 0 : a9.features.forEach((e4) => {
    const r4 = e4.attributes.RouteID, t5 = h3(r4), { geometryType: s9, spatialReference: o7 } = a9;
    t5.directionPoints ?? (t5.directionPoints = { features: [], geometryType: s9, spatialReference: o7 }), t5.directionPoints.features.push(e4);
  }), m8 == null ? void 0 : m8.features.forEach((e4) => {
    const r4 = e4.attributes.RouteID, t5 = h3(r4), { geometryType: s9, spatialReference: o7 } = m8;
    t5.traversedEdges ?? (t5.traversedEdges = { features: [], geometryType: s9, spatialReference: o7 }), t5.traversedEdges.features.push(e4);
  }), y7 == null ? void 0 : y7.features.forEach((e4) => {
    const r4 = e4.attributes.RouteID, t5 = h3(r4), { geometryType: s9, spatialReference: o7 } = y7;
    t5.traversedJunctions ?? (t5.traversedJunctions = { features: [], geometryType: s9, spatialReference: o7 }), t5.traversedJunctions.features.push(e4);
  }), d2 == null ? void 0 : d2.features.forEach((e4) => {
    const r4 = e4.attributes.RouteID, t5 = h3(r4);
    t5.traversedTurns ?? (t5.traversedTurns = { features: [] }), t5.traversedTurns.features.push(e4);
  }), c8.fromJSON({ routeResults: R3, barriers: s8, polygonBarriers: u8, polylineBarriers: f6, messages: n7 });
}

// node_modules/@arcgis/core/rest/support/DataLayer.js
var l7 = class extends i(l) {
  constructor(o6) {
    super(o6), this.doNotLocateOnRestrictedElements = null, this.geometry = null, this.geometryType = null, this.name = null, this.spatialRelationship = null, this.type = "layer", this.where = null;
  }
};
e([y({ type: Boolean, json: { write: true } })], l7.prototype, "doNotLocateOnRestrictedElements", void 0), e([y({ types: n, json: { read: v2, write: true } })], l7.prototype, "geometry", void 0), e([o3(w3)], l7.prototype, "geometryType", void 0), e([y({ type: String, json: { name: "layerName", write: true } })], l7.prototype, "name", void 0), e([o3(S3, { name: "spatialRel" })], l7.prototype, "spatialRelationship", void 0), e([y({ type: String, json: { write: true } })], l7.prototype, "type", void 0), e([y({ type: String, json: { write: true } })], l7.prototype, "where", void 0), l7 = e([a("esri.rest.support.DataLayer")], l7);
var c9 = l7;

// node_modules/@arcgis/core/rest/support/NetworkFeatureSet.js
var s7;
var c10 = s7 = class extends x2 {
  constructor(e3) {
    super(e3), this.doNotLocateOnRestrictedElements = null;
  }
  clone() {
    return new s7({ doNotLocateOnRestrictedElements: this.doNotLocateOnRestrictedElements, ...this.cloneProperties() });
  }
};
e([y({ type: Boolean, json: { write: true } })], c10.prototype, "doNotLocateOnRestrictedElements", void 0), c10 = s7 = e([a("esri.rest.support.NetworkFeatureSet")], c10);
var p5 = c10;

// node_modules/@arcgis/core/rest/support/NetworkUrl.js
var p6 = class extends i(l) {
  constructor(o6) {
    super(o6), this.doNotLocateOnRestrictedElements = null, this.url = null;
  }
};
e([y({ type: Boolean, json: { write: true } })], p6.prototype, "doNotLocateOnRestrictedElements", void 0), e([y({ type: String, json: { write: true } })], p6.prototype, "url", void 0), p6 = e([a("esri.rest.support.NetworkUrl")], p6);
var c11 = p6;

// node_modules/@arcgis/core/rest/support/RouteParameters.js
var O3;
function U3(t5) {
  return t5 && "type" in t5;
}
function J(t5) {
  return t5 && "features" in t5 && "doNotLocateOnRestrictedElements" in t5;
}
function L2(t5) {
  return t5 && "url" in t5;
}
function R2(t5) {
  return t5 && "features" in t5;
}
function C(t5) {
  return U3(t5) ? c9.fromJSON(t5) : L2(t5) ? c11.fromJSON(t5) : J(t5) ? p5.fromJSON(t5) : R2(t5) ? x2.fromJSON(t5) : null;
}
function D3(t5, e3, o6) {
  r(t5) && (e3[o6] = j2.isCollection(t5) ? { features: t5.toArray().map((t6) => t6.toJSON()) } : t5.toJSON());
}
var k3 = O3 = class extends i(l) {
  constructor(t5) {
    super(t5), this.accumulateAttributes = null, this.apiKey = null, this.attributeParameterValues = null, this.directionsLanguage = null, this.directionsLengthUnits = null, this.directionsOutputType = null, this.directionsStyleName = null, this.directionsTimeAttribute = null, this.findBestSequence = null, this.geometryPrecision = null, this.geometryPrecisionM = null, this.geometryPrecisionZ = null, this.ignoreInvalidLocations = null, this.impedanceAttribute = null, this.outputGeometryPrecision = null, this.outputGeometryPrecisionUnits = null, this.outputLines = "true-shape", this.outSpatialReference = null, this.overrides = null, this.pointBarriers = null, this.polygonBarriers = null, this.polylineBarriers = null, this.preserveFirstStop = null, this.preserveLastStop = null, this.preserveObjectID = null, this.restrictionAttributes = null, this.restrictUTurns = null, this.returnBarriers = false, this.returnDirections = false, this.returnPolygonBarriers = false, this.returnPolylineBarriers = false, this.returnRoutes = true, this.returnStops = false, this.returnTraversedEdges = null, this.returnTraversedJunctions = null, this.returnTraversedTurns = null, this.returnZ = true, this.startTime = null, this.startTimeIsUTC = true, this.stops = null, this.timeWindowsAreUTC = null, this.travelMode = null, this.useHierarchy = null, this.useTimeWindows = null;
  }
  static from(t5) {
    return v(O3, t5);
  }
  readAccumulateAttributes(t5) {
    return t(t5) ? null : t5.map((t6) => U2.fromJSON(t6));
  }
  writeAccumulateAttributes(t5, e3, r4) {
    !t(t5) && t5.length && (e3[r4] = t5.map((t6) => U2.toJSON(t6)));
  }
  writePointBarriers(t5, e3, r4) {
    D3(t5, e3, r4);
  }
  writePolygonBarrier(t5, e3, r4) {
    D3(t5, e3, r4);
  }
  writePolylineBarrier(t5, e3, r4) {
    D3(t5, e3, r4);
  }
  readRestrictionAttributes(t5) {
    return t(t5) ? null : t5.map((t6) => D.fromJSON(t6));
  }
  writeRestrictionAttributes(t5, e3, r4) {
    !t(t5) && t5.length && (e3[r4] = t5.map((t6) => D.toJSON(t6)));
  }
  readStartTime(t5, e3) {
    const { startTime: r4 } = e3;
    return t(r4) ? null : "now" === r4 ? "now" : new Date(r4);
  }
  writeStartTime(t5, e3) {
    t(t5) || (e3.startTime = "now" === t5 ? "now" : t5.getTime());
  }
  readStops(t5, e3) {
    return C(e3.stops);
  }
  writeStops(t5, e3, r4) {
    D3(t5, e3, r4);
  }
};
e([y({ type: [String], json: { name: "accumulateAttributeNames", write: true } })], k3.prototype, "accumulateAttributes", void 0), e([o2("accumulateAttributes")], k3.prototype, "readAccumulateAttributes", null), e([r2("accumulateAttributes")], k3.prototype, "writeAccumulateAttributes", null), e([y(t4)], k3.prototype, "apiKey", void 0), e([y({ json: { write: true } })], k3.prototype, "attributeParameterValues", void 0), e([y({ type: String, json: { write: true } })], k3.prototype, "directionsLanguage", void 0), e([o3(r3)], k3.prototype, "directionsLengthUnits", void 0), e([o3(a4)], k3.prototype, "directionsOutputType", void 0), e([o3(k)], k3.prototype, "directionsStyleName", void 0), e([o3(y3, { name: "directionsTimeAttributeName", ignoreUnknown: false })], k3.prototype, "directionsTimeAttribute", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "findBestSequence", void 0), e([y({ type: Number, json: { write: true } })], k3.prototype, "geometryPrecision", void 0), e([y({ type: Number, json: { write: true } })], k3.prototype, "geometryPrecisionM", void 0), e([y({ type: Number, json: { write: true } })], k3.prototype, "geometryPrecisionZ", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "ignoreInvalidLocations", void 0), e([o3(U2, { name: "impedanceAttributeName", ignoreUnknown: false })], k3.prototype, "impedanceAttribute", void 0), e([y({ type: Number, json: { write: true } })], k3.prototype, "outputGeometryPrecision", void 0), e([o3(i4)], k3.prototype, "outputGeometryPrecisionUnits", void 0), e([o3(o5)], k3.prototype, "outputLines", void 0), e([y({ type: f, json: { name: "outSR", write: true } })], k3.prototype, "outSpatialReference", void 0), e([y({ json: { write: true } })], k3.prototype, "overrides", void 0), e([y({ json: { name: "barriers", write: true } })], k3.prototype, "pointBarriers", void 0), e([r2("pointBarriers")], k3.prototype, "writePointBarriers", null), e([y({ json: { write: true } })], k3.prototype, "polygonBarriers", void 0), e([r2("polygonBarriers")], k3.prototype, "writePolygonBarrier", null), e([y({ json: { write: true } })], k3.prototype, "polylineBarriers", void 0), e([r2("polylineBarriers")], k3.prototype, "writePolylineBarrier", null), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "preserveFirstStop", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "preserveLastStop", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "preserveObjectID", void 0), e([y({ type: [String], json: { name: "restrictionAttributeNames", write: true } })], k3.prototype, "restrictionAttributes", void 0), e([o2("restrictionAttributes")], k3.prototype, "readRestrictionAttributes", null), e([r2("restrictionAttributes")], k3.prototype, "writeRestrictionAttributes", null), e([o3(l2)], k3.prototype, "restrictUTurns", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnBarriers", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnDirections", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnPolygonBarriers", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnPolylineBarriers", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnRoutes", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnStops", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnTraversedEdges", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnTraversedJunctions", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnTraversedTurns", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "returnZ", void 0), e([y({ type: Date, json: { type: Number, write: true } })], k3.prototype, "startTime", void 0), e([o2("startTime")], k3.prototype, "readStartTime", null), e([r2("startTime")], k3.prototype, "writeStartTime", null), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "startTimeIsUTC", void 0), e([y({ json: { write: true } })], k3.prototype, "stops", void 0), e([o2("stops")], k3.prototype, "readStops", null), e([r2("stops")], k3.prototype, "writeStops", null), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "timeWindowsAreUTC", void 0), e([y({ type: A, json: { write: true } })], k3.prototype, "travelMode", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "useHierarchy", void 0), e([y({ type: Boolean, json: { write: true } })], k3.prototype, "useTimeWindows", void 0), k3 = O3 = e([a("esri.rest.support.RouteParameters")], k3);
var I = k3;

// node_modules/@arcgis/core/layers/RouteLayer.js
function Z(e3) {
  return e3.length ? e3 : null;
}
function $(e3) {
  switch (e3) {
    case "esriGeometryPoint":
      return { type: "esriSMS", style: "esriSMSCircle", size: 12, color: [0, 0, 0, 0], outline: $("esriGeometryPolyline") };
    case "esriGeometryPolyline":
      return { type: "esriSLS", style: "esriSLSSolid", width: 1, color: [0, 0, 0, 0] };
    case "esriGeometryPolygon":
      return { type: "esriSFS", style: "esriSFSNull", outline: $("esriGeometryPolyline") };
  }
}
function X(e3) {
  return "layers" in e3;
}
function ee(e3) {
  return "esri.rest.support.FeatureSet" === e3.declaredClass;
}
function te(e3) {
  return "esri.rest.support.NetworkFeatureSet" === e3.declaredClass;
}
function re(e3, t5, r4) {
  var _a, _b, _c, _d, _e, _f;
  const o6 = (_a = t5.networkDataset) == null ? void 0 : _a.networkAttributes, i7 = (o6 == null ? void 0 : o6.filter(({ usageType: e4 }) => "cost" === e4)) ?? [], s8 = r4.travelMode ?? t5.defaultTravelMode;
  if (t(s8)) return void fe.warn("route-layer:missing-travel-mode", "The routing service must have a default travel mode or one must be specified in the route parameter.");
  const { timeAttributeName: a9, distanceAttributeName: l8 } = s8, m8 = i7.find(({ name: e4 }) => e4 === a9), y7 = i7.find(({ name: e4 }) => e4 === l8), f6 = ((_b = e2(r4.travelMode)) == null ? void 0 : _b.impedanceAttributeName) ?? e2(r4.impedanceAttribute) ?? t5.impedance, d2 = m8 == null ? void 0 : m8.units, h3 = y7 == null ? void 0 : y7.units;
  if (!d2 || !h3) throw new s4("routelayer:unknown-impedance-units", "the units of either the distance or time impedance are unknown");
  const w4 = r4.directionsLanguage ?? t5.directionsLanguage, S4 = e2(r4.accumulateAttributes) ?? t5.accumulateAttributeNames ?? [], g3 = new Set(i7.filter(({ name: e4 }) => e4 === a9 || e4 === l8 || e4 === f6 || null != e4 && S4.includes(e4)).map(({ name: e4 }) => e4)), b5 = (e4) => {
    for (const t6 in e4) g3.has(t6) || delete e4[t6];
  };
  for (const n7 of e3.pointBarriers) r(n7.costs) && (n7.addedCost = n7.costs[f6] ?? 0, b5(n7.costs));
  for (const n7 of e3.polygonBarriers) r(n7.costs) && (n7.scaleFactor = n7.costs[f6] ?? 1, b5(n7.costs));
  for (const n7 of e3.polylineBarriers) r(n7.costs) && (n7.scaleFactor = n7.costs[f6] ?? 1, b5(n7.costs));
  const { routeInfo: v6 } = e3, { findBestSequence: B, preserveFirstStop: P, preserveLastStop: j4, startTimeIsUTC: I2, timeWindowsAreUTC: N2 } = r4;
  v6.analysisSettings = new l3({ accumulateAttributes: S4, directionsLanguage: w4, findBestSequence: B, preserveFirstStop: P, preserveLastStop: j4, startTimeIsUTC: I2, timeWindowsAreUTC: N2, travelMode: s8 }), v6.totalDuration = ie(((_c = v6.totalCosts) == null ? void 0 : _c[a9]) ?? 0, d2), v6.totalDistance = se(((_d = v6.totalCosts) == null ? void 0 : _d[l8]) ?? 0, h3), v6.totalLateDuration = ie(((_e = v6.totalViolations) == null ? void 0 : _e[a9]) ?? 0, d2), v6.totalWaitDuration = ie(((_f = v6.totalWait) == null ? void 0 : _f[a9]) ?? 0, d2), r(v6.totalCosts) && b5(v6.totalCosts), r(v6.totalViolations) && b5(v6.totalViolations), r(v6.totalWait) && b5(v6.totalWait);
  for (const n7 of e3.stops) r(n7.serviceCosts) && (n7.serviceDuration = ie(n7.serviceCosts[a9] ?? 0, d2), n7.serviceDistance = se(n7.serviceCosts[l8] ?? 0, h3), b5(n7.serviceCosts)), r(n7.cumulativeCosts) && (n7.cumulativeDuration = ie(n7.cumulativeCosts[a9] ?? 0, d2), n7.cumulativeDistance = se(n7.cumulativeCosts[l8] ?? 0, h3), b5(n7.cumulativeCosts)), r(n7.violations) && (n7.lateDuration = ie(n7.violations[a9] ?? 0, d2), b5(n7.violations)), r(n7.wait) && (n7.waitDuration = ie(n7.wait[a9] ?? 0, d2), b5(n7.wait));
}
async function oe(e3) {
  const t5 = f.WGS84;
  return await _n(e3.spatialReference, t5), rn(e3, t5);
}
function ie(e3, t5) {
  switch (t5) {
    case "seconds":
      return e3 / 60;
    case "hours":
      return 60 * e3;
    case "days":
      return 60 * e3 * 24;
    default:
      return e3;
  }
}
function se(e3, t5) {
  return "decimal-degrees" === t5 || "points" === t5 || "unknown" === t5 ? e3 : N(e3, t5, "meters");
}
function ne(e3) {
  const { attributes: t5, geometry: r4, popupTemplate: o6, symbol: i7 } = e3.toGraphic().toJSON();
  return { attributes: t5, geometry: r4, popupInfo: o6, symbol: i7 };
}
var ae = j2.ofType(b3);
var le = j2.ofType(h);
var pe = j2.ofType(O2);
var ue = j2.ofType(g);
var ce = j2.ofType(f3);
var me = j2.ofType(D2);
var ye = "esri.layers.RouteLayer";
var fe = s3.getLogger(ye);
var de = class extends n2(t3(c2(_(O(a3(b)))))) {
  constructor(e3) {
    super(e3), this._cachedServiceDescription = null, this._featureCollection = null, this._type = "Feature Collection", this.defaultSymbols = new y4(), this.directionLines = null, this.directionPoints = null, this.featureCollectionType = "route", this.legendEnabled = false, this.maxScale = 0, this.minScale = 0, this.pointBarriers = new pe(), this.polygonBarriers = new ue(), this.polylineBarriers = new ce(), this.routeInfo = null, this.spatialReference = f.WGS84, this.stops = new me(), this.type = "route";
    const t5 = () => {
      this._setStopSymbol(this.stops);
    };
    this.addHandles(a2(() => this.stops, "change", t5, { sync: true, onListenerAdd: t5 }));
  }
  writeFeatureCollectionWebmap(e3, t5, r4, o6) {
    const i7 = [this._writePolygonBarriers(), this._writePolylineBarriers(), this._writePointBarriers(), this._writeRouteInfo(), this._writeDirectionLines(), this._writeDirectionPoints(), this._writeStops()].filter((e4) => !!e4), s8 = i7.map((e4, t6) => t6), n7 = "web-map" === o6.origin ? "featureCollection.layers" : "layers";
    o(n7, i7, t5), t5.opacity = this.opacity, t5.visibility = this.visible, t5.visibleLayers = s8;
  }
  readDirectionLines(e3, t5) {
    return this._getNetworkFeatures(t5, "DirectionLines", (e4) => b3.fromGraphic(e4));
  }
  readDirectionPoints(e3, t5) {
    return this._getNetworkFeatures(t5, "DirectionPoints", (e4) => h.fromGraphic(e4));
  }
  get fullExtent() {
    const e3 = new w2({ xmin: -180, ymin: -90, xmax: 180, ymax: 90, spatialReference: f.WGS84 });
    if (r(this.routeInfo) && r(this.routeInfo.geometry)) return this.routeInfo.geometry.extent ?? e3;
    if (t(this.stops)) return e3;
    const t5 = this.stops.filter((e4) => r(e4.geometry));
    if (t5.length < 2) return e3;
    const { spatialReference: r4 } = t5.getItemAt(0).geometry;
    if (t(r4)) return e3;
    const o6 = t5.toArray().map((e4) => {
      const t6 = e4.geometry;
      return [t6.x, t6.y];
    });
    return new u({ points: o6, spatialReference: r4 }).extent;
  }
  readMaxScale(e3, t5) {
    var _a, _b;
    const r4 = (_b = X(t5) ? t5.layers : (_a = t5.featureCollection) == null ? void 0 : _a.layers) == null ? void 0 : _b.find((e4) => r(e4.layerDefinition.maxScale));
    return (r4 == null ? void 0 : r4.layerDefinition.maxScale) ?? 0;
  }
  readMinScale(e3, t5) {
    var _a, _b;
    const r4 = (_b = X(t5) ? t5.layers : (_a = t5.featureCollection) == null ? void 0 : _a.layers) == null ? void 0 : _b.find((e4) => r(e4.layerDefinition.minScale));
    return (r4 == null ? void 0 : r4.layerDefinition.minScale) ?? 0;
  }
  readPointBarriers(e3, t5) {
    return this._getNetworkFeatures(t5, "Barriers", (e4) => O2.fromGraphic(e4));
  }
  readPolygonBarriers(e3, t5) {
    return this._getNetworkFeatures(t5, "PolygonBarriers", (e4) => g.fromGraphic(e4));
  }
  readPolylineBarriers(e3, t5) {
    return this._getNetworkFeatures(t5, "PolylineBarriers", (e4) => f3.fromGraphic(e4));
  }
  readRouteInfo(e3, t5) {
    const r4 = this._getNetworkFeatures(t5, "RouteInfo", (e4) => h2.fromGraphic(e4));
    return r4.length > 0 ? r4.getItemAt(0) : null;
  }
  readSpatialReference(e3, t5) {
    var _a, _b;
    const r4 = X(t5) ? t5.layers : (_a = t5.featureCollection) == null ? void 0 : _a.layers;
    if (!(r4 == null ? void 0 : r4.length)) return f.WGS84;
    const { layerDefinition: o6, featureSet: i7 } = r4[0], s8 = i7.features[0], n7 = ((_b = e2(s8 == null ? void 0 : s8.geometry)) == null ? void 0 : _b.spatialReference) ?? i7.spatialReference ?? o6.spatialReference ?? o6.extent.spatialReference ?? c;
    return f.fromJSON(n7);
  }
  readStops(e3, t5) {
    return this._getNetworkFeatures(t5, "Stops", (e4) => D2.fromGraphic(e4), (e4) => this._setStopSymbol(e4));
  }
  get title() {
    return r(this.routeInfo) && r(this.routeInfo.name) ? this.routeInfo.name : "Route";
  }
  set title(e3) {
    this._overrideIfSome("title", e3);
  }
  get url() {
    return s2.routeServiceUrl;
  }
  set url(e3) {
    null != e3 ? this._set("url", S(e3, fe)) : this._set("url", s2.routeServiceUrl);
  }
  load(e3) {
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Feature Collection"] }, e3)), Promise.resolve(this);
  }
  removeAll() {
    this.removeResult(), this.pointBarriers.removeAll(), this.polygonBarriers.removeAll(), this.polylineBarriers.removeAll(), this.stops.removeAll();
  }
  removeResult() {
    r(this.directionLines) && (this.directionLines.removeAll(), this._set("directionLines", null)), r(this.directionPoints) && (this.directionPoints.removeAll(), this._set("directionPoints", null)), r(this.routeInfo) && this._set("routeInfo", null);
  }
  async save() {
    await this.load();
    const { fullExtent: e3, portalItem: t5 } = this;
    if (!t5) throw new s4("routelayer:portal-item-not-set", "save() requires to the layer to have a portal item");
    if (!t5.id) throw new s4("routelayer:portal-item-not-saved", "Please use saveAs() first to save the routelayer");
    if ("Feature Collection" !== t5.type) throw new s4("routelayer:portal-item-wrong-type", 'Portal item needs to have type "Feature Collection"');
    if (t(this.routeInfo)) throw new s4("routelayer:route-unsolved", "save() requires a solved route");
    const { portal: r4 } = t5;
    await r4.signIn(), r4.user || await t5.reload();
    const { itemUrl: o6, itemControl: i7 } = t5;
    if ("admin" !== i7 && "update" !== i7) throw new s4("routelayer:insufficient-permissions", "To save this layer, you need to be the owner or an administrator of your organization");
    const s8 = { messages: [], origin: "portal-item", portal: r4, url: o6 ? L(o6) : void 0, writtenProperties: [] }, a9 = this.write(void 0, s8);
    return t5.extent = await oe(e3), t5.title = this.title, await t5.update({ data: a9 }), t5;
  }
  async saveAs(e3, t5 = {}) {
    var _a;
    if (await this.load(), t(this.routeInfo)) throw new s4("routelayer:route-unsolved", "saveAs() requires a solved route");
    const r4 = x.from(e3).clone();
    r4.extent ?? (r4.extent = await oe(this.fullExtent)), r4.id = null, r4.portal ?? (r4.portal = b4.getDefault()), r4.title ?? (r4.title = this.title), r4.type = "Feature Collection", r4.typeKeywords = ["Data", "Feature Collection", u2.MULTI_LAYER, "Route Layer"];
    const { portal: o6 } = r4, i7 = { messages: [], origin: "portal-item", portal: o6, url: null, writtenProperties: [] };
    await o6.signIn();
    const s8 = t5 == null ? void 0 : t5.folder, a9 = this.write(void 0, i7);
    return await ((_a = o6.user) == null ? void 0 : _a.addItem({ item: r4, folder: s8, data: a9 })), this.portalItem = r4, i3(i7), i7.portalItem = r4, r4;
  }
  async solve(e3, t5) {
    const r4 = (e3 == null ? void 0 : e3.stops) ?? this.stops, o6 = (e3 == null ? void 0 : e3.pointBarriers) ?? Z(this.pointBarriers), i7 = (e3 == null ? void 0 : e3.polylineBarriers) ?? Z(this.polylineBarriers), a9 = (e3 == null ? void 0 : e3.polygonBarriers) ?? Z(this.polygonBarriers);
    if (t(r4)) throw new s4("routelayer:undefined-stops", "the route layer must have stops defined in the route parameters.");
    if ((ee(r4) || te(r4)) && r4.features.length < 2 || j2.isCollection(r4) && r4.length < 2) throw new s4("routelayer:insufficent-stops", "the route layer must have two or more stops to solve a route.");
    if (j2.isCollection(r4)) for (const s8 of r4) s8.routeName = null;
    const l8 = e3 == null ? void 0 : e3.apiKey, y7 = this.url, f6 = await this._getServiceDescription(y7, l8, t5), h3 = (e3 == null ? void 0 : e3.travelMode) ?? f6.defaultTravelMode, w4 = e2(e3 == null ? void 0 : e3.accumulateAttributes) ?? [];
    r(h3) && (w4.push(h3.distanceAttributeName), h3.timeAttributeName && w4.push(h3.timeAttributeName));
    const S4 = { startTime: /* @__PURE__ */ new Date() }, g3 = { accumulateAttributes: w4, directionsOutputType: "featuresets", ignoreInvalidLocations: true, pointBarriers: o6, polylineBarriers: i7, polygonBarriers: a9, preserveFirstStop: true, preserveLastStop: true, returnBarriers: !!o6, returnDirections: true, returnPolygonBarriers: !!a9, returnPolylineBarriers: !!i7, returnRoutes: true, returnStops: true, stops: r4 }, b5 = e3 ? I.from(e3) : new I();
    for (const s8 in S4) null == b5[s8] && (b5[s8] = S4[s8]);
    let v6;
    b5.set(g3);
    try {
      v6 = await m7(y7, b5, t5);
    } catch (P) {
      throw j(P) ? P : new s4("routelayer:failed-route-request", "the routing request failed", { error: P });
    }
    const B = this._toRouteLayerSolution(v6);
    return this._isOverridden("title") || (this.title = s(B.routeInfo.name, "Route")), re(B, f6, b5), B;
  }
  update(e3) {
    const { stops: t5, directionLines: r4, directionPoints: o6, pointBarriers: i7, polylineBarriers: s8, polygonBarriers: n7, routeInfo: a9 } = e3;
    this.set({ stops: t5, pointBarriers: i7, polylineBarriers: s8, polygonBarriers: n7 }), this._set("directionLines", r4), this._set("directionPoints", o6), this._set("routeInfo", a9), r(a9.geometry) && (this.spatialReference = a9.geometry.spatialReference);
  }
  _getNetworkFeatures(e3, t5, n7, a9) {
    var _a, _b;
    const l8 = (_b = X(e3) ? e3.layers : (_a = e3.featureCollection) == null ? void 0 : _a.layers) == null ? void 0 : _b.find((e4) => e4.layerDefinition.name === t5);
    if (t(l8)) return new j2();
    const { layerDefinition: c12, popupInfo: m8, featureSet: y7 } = l8, f6 = c12.drawingInfo.renderer, { features: d2 } = y7, h3 = y7.spatialReference ?? c12.spatialReference ?? c12.extent.spatialReference ?? c, w4 = f6 && o4(f6), S4 = f.fromJSON(h3), g3 = d2.map((e4) => {
      const i7 = g2.fromJSON(e4);
      r(i7.geometry) && r(e4.geometry) && t(e4.geometry.spatialReference) && (i7.geometry.spatialReference = S4);
      const s8 = n7(i7);
      return s8.symbol ?? (s8.symbol = (w4 == null ? void 0 : w4.getSymbol(i7)) ?? this._getNetworkSymbol(t5)), s8.popupTemplate ?? (s8.popupTemplate = m8 && k2.fromJSON(m8)), s8;
    });
    return a9 && g3.some((e4) => !e4.symbol) && a9(g3), new j2(g3);
  }
  _getNetworkSymbol(e3) {
    switch (e3) {
      case "Barriers":
        return this.defaultSymbols.pointBarriers;
      case "DirectionPoints":
        return this.defaultSymbols.directionPoints;
      case "DirectionLines":
        return this.defaultSymbols.directionLines;
      case "PolylineBarriers":
        return this.defaultSymbols.polylineBarriers;
      case "PolygonBarriers":
        return this.defaultSymbols.polygonBarriers;
      case "RouteInfo":
        return this.defaultSymbols.routeInfo;
      case "Stops":
        return null;
    }
  }
  async _getServiceDescription(e3, t5, r4) {
    if (r(this._cachedServiceDescription) && this._cachedServiceDescription.url === e3) return this._cachedServiceDescription.serviceDescription;
    const o6 = await p3(e3, t5, r4);
    return this._cachedServiceDescription = { serviceDescription: o6, url: e3 }, o6;
  }
  _setStopSymbol(e3) {
    if (!e3 || 0 === e3.length) return;
    if (t(this.defaultSymbols.stops)) return;
    if (e3.every((e4) => r(e4.symbol))) return;
    const { first: t5, last: r4, middle: o6, unlocated: i7, waypoint: s8, break: n7 } = this.defaultSymbols.stops;
    if (t(this.routeInfo) || 1 === e3.length) return void e3.forEach((i8, s9) => {
      switch (s9) {
        case 0:
          i8.symbol = t5;
          break;
        case e3.length - 1:
          i8.symbol = r4;
          break;
        default:
          i8.symbol = o6;
      }
    });
    const a9 = e3.map((e4) => e4.sequence).filter((e4) => r(e4)), l8 = Math.min(...a9), c12 = Math.max(...a9);
    for (const p7 of e3) p7.sequence !== l8 ? p7.sequence !== c12 ? "ok" === p7.status || "not-located-on-closest" === p7.status ? "waypoint" !== p7.locationType ? "break" !== p7.locationType ? p7.symbol = o6 : p7.symbol = n7 : p7.symbol = s8 : p7.symbol = i7 : p7.symbol = r4 : p7.symbol = t5;
  }
  _toRouteLayerSolution(e3) {
    var _a, _b, _c, _d, _e, _f, _g;
    const t5 = (_a = e3.routeResults[0].stops) == null ? void 0 : _a.map((e4) => D2.fromJSON(e4.toJSON()));
    this._setStopSymbol(t5);
    const r4 = new me(t5), o6 = new ue((_b = e3.polygonBarriers) == null ? void 0 : _b.map((e4) => {
      const t6 = g.fromJSON(e4.toJSON());
      return t6.symbol = this.defaultSymbols.polygonBarriers, t6;
    })), i7 = new ce((_c = e3.polylineBarriers) == null ? void 0 : _c.map((e4) => {
      const t6 = f3.fromJSON(e4.toJSON());
      return t6.symbol = this.defaultSymbols.polylineBarriers, t6;
    })), s8 = new pe((_d = e3.pointBarriers) == null ? void 0 : _d.map((e4) => {
      const t6 = O2.fromJSON(e4.toJSON());
      return t6.symbol = this.defaultSymbols.pointBarriers, t6;
    })), n7 = (_e = e3.routeResults[0].route) == null ? void 0 : _e.toJSON(), a9 = h2.fromJSON(n7);
    a9.symbol = this.defaultSymbols.routeInfo;
    const l8 = new le((_f = e3.routeResults[0].directionPoints) == null ? void 0 : _f.features.map((e4) => {
      const t6 = h.fromJSON(e4.toJSON());
      return t6.symbol = this.defaultSymbols.directionPoints, t6;
    }));
    return { directionLines: new ae((_g = e3.routeResults[0].directionLines) == null ? void 0 : _g.features.map((e4) => {
      const t6 = b3.fromJSON(e4.toJSON());
      return t6.symbol = this.defaultSymbols.directionLines, t6;
    })), directionPoints: l8, pointBarriers: s8, polygonBarriers: o6, polylineBarriers: i7, routeInfo: a9, stops: r4 };
  }
  _writeDirectionLines() {
    return this._writeNetworkFeatures(this.directionLines, this.defaultSymbols.directionLines, "esriGeometryPolyline", b3.fields, b3.popupInfo, "DirectionLines", "Direction Lines");
  }
  _writeDirectionPoints() {
    return this._writeNetworkFeatures(this.directionPoints, this.defaultSymbols.directionPoints, "esriGeometryPoint", h.fields, h.popupInfo, "DirectionPoints", "Direction Points");
  }
  _writeNetworkFeatures(e3, t5, r4, o6, i7, s8, n7) {
    if (t(e3) || !e3.length) return null;
    const a9 = this.spatialReference.toJSON(), { fullExtent: l8, maxScale: c12, minScale: m8 } = this;
    return { featureSet: { features: e3.toArray().map((e4) => ne(e4)), geometryType: r4, spatialReference: a9 }, layerDefinition: { capabilities: "Query,Update,Editing", drawingInfo: { renderer: { type: "simple", symbol: r(t5) ? t5.toJSON() : $(r4) } }, extent: l8.toJSON(), fields: o6, geometryType: r4, hasM: false, hasZ: false, maxScale: c12, minScale: m8, name: s8, objectIdField: "ObjectID", spatialReference: a9, title: n7, type: "Feature Layer", typeIdField: "" }, popupInfo: i7 };
  }
  _writePointBarriers() {
    return this._writeNetworkFeatures(this.pointBarriers, this.defaultSymbols.pointBarriers, "esriGeometryPoint", O2.fields, O2.popupInfo, "Barriers", "Point Barriers");
  }
  _writePolygonBarriers() {
    return this._writeNetworkFeatures(this.polygonBarriers, this.defaultSymbols.polygonBarriers, "esriGeometryPolygon", g.fields, g.popupInfo, "PolygonBarriers", "Polygon Barriers");
  }
  _writePolylineBarriers() {
    return this._writeNetworkFeatures(this.polylineBarriers, this.defaultSymbols.polylineBarriers, "esriGeometryPolyline", f3.fields, f3.popupInfo, "PolylineBarriers", "Line Barriers");
  }
  _writeRouteInfo() {
    return this._writeNetworkFeatures(r(this.routeInfo) ? new j2([this.routeInfo]) : null, this.defaultSymbols.routeInfo, "esriGeometryPolyline", h2.fields, h2.popupInfo, "RouteInfo", "Route Details");
  }
  _writeStops() {
    const e3 = this._writeNetworkFeatures(this.stops, null, "esriGeometryPoint", D2.fields, D2.popupInfo, "Stops", "Stops");
    if (t(e3)) return null;
    const { stops: t5 } = this.defaultSymbols, r4 = r(t5) && r(t5.first) && t5.first.toJSON(), o6 = r(t5) && r(t5.middle) && t5.middle.toJSON(), i7 = r(t5) && r(t5.last) && t5.last.toJSON();
    return e3.layerDefinition.drawingInfo.renderer = { type: "uniqueValue", field1: "Sequence", defaultSymbol: o6, uniqueValueInfos: [{ value: "1", symbol: r4, label: "First Stop" }, { value: `${this.stops.length}`, symbol: i7, label: "Last Stop" }] }, e3;
  }
};
e([y({ readOnly: true, json: { read: false, origins: { "portal-item": { write: { allowNull: true, ignoreOrigin: true } }, "web-map": { write: { overridePolicy() {
  return { allowNull: true, ignoreOrigin: null == this.portalItem };
} } } } } })], de.prototype, "_featureCollection", void 0), e([r2(["web-map", "portal-item"], "_featureCollection")], de.prototype, "writeFeatureCollectionWebmap", null), e([y({ readOnly: true, json: { read: false, origins: { "web-map": { write: { target: "type", overridePolicy() {
  return { ignoreOrigin: null != this.portalItem };
} } } } } })], de.prototype, "_type", void 0), e([y({ nonNullable: true, type: y4 })], de.prototype, "defaultSymbols", void 0), e([y({ readOnly: true })], de.prototype, "directionLines", void 0), e([o2(["web-map", "portal-item"], "directionLines", ["layers", "featureCollection.layers"])], de.prototype, "readDirectionLines", null), e([y({ readOnly: true })], de.prototype, "directionPoints", void 0), e([o2(["web-map", "portal-item"], "directionPoints", ["layers", "featureCollection.layers"])], de.prototype, "readDirectionPoints", null), e([y({ readOnly: true, json: { read: false, origins: { "web-map": { write: { ignoreOrigin: true } } } } })], de.prototype, "featureCollectionType", void 0), e([y({ readOnly: true })], de.prototype, "fullExtent", null), e([y({ json: { origins: { "web-map": { name: "featureCollection.showLegend" } }, write: true } })], de.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide"] })], de.prototype, "listMode", void 0), e([y({ type: Number, nonNullable: true, json: { write: false } })], de.prototype, "maxScale", void 0), e([o2(["web-map", "portal-item"], "maxScale", ["layers", "featureCollection.layers"])], de.prototype, "readMaxScale", null), e([y({ type: Number, nonNullable: true, json: { write: false } })], de.prototype, "minScale", void 0), e([o2(["web-map", "portal-item"], "minScale", ["layers", "featureCollection.layers"])], de.prototype, "readMinScale", null), e([y({ type: ["ArcGISFeatureLayer"], value: "ArcGISFeatureLayer" })], de.prototype, "operationalLayerType", void 0), e([y({ nonNullable: true, type: j2.ofType(O2) })], de.prototype, "pointBarriers", void 0), e([o2(["web-map", "portal-item"], "pointBarriers", ["layers", "featureCollection.layers"])], de.prototype, "readPointBarriers", null), e([y({ nonNullable: true, type: j2.ofType(g) })], de.prototype, "polygonBarriers", void 0), e([o2(["web-map", "portal-item"], "polygonBarriers", ["layers", "featureCollection.layers"])], de.prototype, "readPolygonBarriers", null), e([y({ nonNullable: true, type: j2.ofType(f3) })], de.prototype, "polylineBarriers", void 0), e([o2(["web-map", "portal-item"], "polylineBarriers", ["layers", "featureCollection.layers"])], de.prototype, "readPolylineBarriers", null), e([y({ readOnly: true })], de.prototype, "routeInfo", void 0), e([o2(["web-map", "portal-item"], "routeInfo", ["layers", "featureCollection.layers"])], de.prototype, "readRouteInfo", null), e([y({ type: f })], de.prototype, "spatialReference", void 0), e([o2(["web-map", "portal-item"], "spatialReference", ["layers", "featureCollection.layers"])], de.prototype, "readSpatialReference", null), e([y({ nonNullable: true, type: j2.ofType(D2) })], de.prototype, "stops", void 0), e([o2(["web-map", "portal-item"], "stops", ["layers", "featureCollection.layers"])], de.prototype, "readStops", null), e([y()], de.prototype, "title", null), e([y({ readOnly: true, json: { read: false } })], de.prototype, "type", void 0), e([y()], de.prototype, "url", null), de = e([a(ye)], de);
var he = de;
export {
  he as default
};
//# sourceMappingURL=RouteLayer-2NFXX5RP.js.map
