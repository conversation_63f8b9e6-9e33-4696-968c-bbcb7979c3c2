# 涵养水位测试数据导入说明

## 📊 数据概览

本次为涵养水位功能生成了完整的测试数据，包括：

### 1. 数据库表结构
- **tb_conservation_water_level**: 涵养水位数据表
- **tb_conservation_analysis**: 涵养分析结果表

### 2. 测试数据规模
- **水位数据**: 65条记录（5个测点，每个测点13条记录）
- **分析数据**: 10条记录（包含不同风险等级的分析结果）
- **时间范围**: 2024年1月1日 - 2024年1月15日

## 🗂️ 文件说明

### 1. conservation_water_level_sample_data.sql
- **用途**: 基础测试数据（20条水位数据 + 5条分析数据）
- **特点**: 包含基本的测试场景和数据验证
- **适用**: 功能开发和基础测试

### 2. conservation_water_level_extended_data.sql  
- **用途**: 扩展测试数据（45条水位数据 + 5条分析数据）
- **特点**: 包含更多测点和完整的时间序列
- **适用**: 性能测试和完整功能验证

### 3. conservation_water_level_import_data.csv
- **用途**: Excel导入测试数据
- **特点**: 符合导入模板格式，包含各种数据场景
- **适用**: 批量导入功能测试

## 🏗️ 数据结构说明

### 涵养水位数据表 (tb_conservation_water_level)

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | varchar(32) | 主键ID | cwl_001 |
| tenant_id | varchar(32) | 租户ID | default |
| station_id | varchar(32) | 测点ID | station_001 |
| raw_water_level | decimal(10,3) | 原水液位(米) | 12.450 |
| groundwater_level | decimal(10,3) | 地下水位(米) | 8.320 |
| level_change | decimal(10,3) | 液位变化(米) | -0.150 |
| rainfall_amount | decimal(10,2) | 降雨量(毫米) | 15.2 |
| evaporation_amount | decimal(10,2) | 蒸发量(毫米) | 8.5 |
| surface_runoff | decimal(12,2) | 地表径流量(立方米) | 1200.0 |
| extraction_amount | decimal(12,2) | 开采量(立方米) | 450.0 |
| soil_moisture | decimal(5,2) | 土壤含水率(%) | 32.5 |
| permeability_coefficient | decimal(10,6) | 渗透系数 | 0.0025 |
| record_time | timestamp | 记录时间 | 2024-01-01 08:00:00 |
| create_time | timestamp | 创建时间 | 2024-01-01 08:00:00 |
| update_time | timestamp | 更新时间 | 2024-01-01 08:00:00 |
| remark | varchar(500) | 备注 | 正常监测 |
| data_source | int | 数据来源(1:手动,2:设备) | 1 |
| creator | varchar(50) | 创建人 | admin |

### 涵养分析结果表 (tb_conservation_analysis)

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | varchar(32) | 主键ID | ca_001 |
| tenant_id | varchar(32) | 租户ID | default |
| station_id | varchar(32) | 测点ID | station_001 |
| start_time | timestamp | 分析开始时间 | 2024-01-01 00:00:00 |
| end_time | timestamp | 分析结束时间 | 2024-01-05 23:59:59 |
| initial_level | decimal(10,3) | 期初水位(米) | 8.320 |
| final_level | decimal(10,3) | 期末水位(米) | 8.180 |
| level_change | decimal(10,3) | 水位变化(米) | -0.140 |
| avg_rainfall | decimal(10,2) | 平均降雨量(毫米) | 12.04 |
| avg_evaporation | decimal(10,2) | 平均蒸发量(毫米) | 10.36 |
| total_extraction | decimal(12,2) | 总开采量(立方米) | 2450.0 |
| conservation_coefficient | decimal(10,6) | 涵养系数 | 0.0856 |
| conservation_potential | decimal(5,2) | 涵养潜力(%) | 72.5 |
| suggested_conservation_amount | decimal(12,2) | 建议涵养量(立方米) | 1200.0 |
| conservation_suggestion | text | 涵养建议 | 建议增加人工补给... |
| risk_level | int | 风险等级(1:低,2:中,3:高) | 2 |
| risk_description | varchar(500) | 风险描述 | 中等风险：水位呈下降趋势 |
| algorithm_version | varchar(20) | 算法版本 | v1.2.0 |
| analysis_details | text | 分析详情(JSON) | {"levelTrend":[...]} |
| create_time | timestamp | 创建时间 | 2024-01-06 09:00:00 |
| update_time | timestamp | 更新时间 | 2024-01-06 09:00:00 |
| status | int | 状态(1:分析中,2:完成,3:失败) | 2 |
| creator | varchar(50) | 创建人 | admin |

## 🎯 测点配置

### 测点1 (station_001) - 瓜州县第一水源地
- **位置**: 瓜州县城北5公里
- **特点**: 主要供水源，水位相对稳定
- **基准水位**: 原水液位12.5m，地下水位8.3m
- **渗透系数**: 0.0025
- **土壤类型**: 砂质壤土

### 测点2 (station_002) - 瓜州县第二水源地  
- **位置**: 瓜州县城东8公里
- **特点**: 水位较高，涵养状况良好
- **基准水位**: 原水液位15.7m，地下水位11.4m
- **渗透系数**: 0.0032
- **土壤类型**: 粘质壤土

### 测点3 (station_003) - 瓜州县第三水源地
- **位置**: 瓜州县城南12公里  
- **特点**: 水位偏低，需要重点关注
- **基准水位**: 原水液位9.8m，地下水位6.5m
- **渗透系数**: 0.0018
- **土壤类型**: 砂土

### 测点4 (station_004) - 瓜州县第四水源地
- **位置**: 瓜州县城西6公里
- **特点**: 新启用测点，运行良好
- **基准水位**: 原水液位14.2m，地下水位9.8m
- **渗透系数**: 0.0028
- **土壤类型**: 壤土

### 测点5 (station_005) - 瓜州县第五水源地
- **位置**: 瓜州县城东南10公里
- **特点**: 应急水源地，谨慎使用
- **基准水位**: 原水液位11.8m，地下水位7.9m
- **渗透系数**: 0.0022
- **土壤类型**: 砂质壤土

## 📈 数据特点

### 1. 时间序列完整性
- 每个测点都有连续的时间序列数据
- 包含不同季节和天气条件的数据
- 体现了水位的自然波动规律

### 2. 数据真实性
- 水位变化符合地下水动态规律
- 降雨量与水位变化呈正相关
- 蒸发量与开采量对水位的负面影响

### 3. 风险等级分布
- **低风险**: 测点2、测点4（水位稳定上升）
- **中等风险**: 测点1、测点5（需要关注）
- **高风险**: 测点3（需要紧急干预）

### 4. 数据来源多样性
- **手动录入**: 约75%的数据
- **设备采集**: 约25%的数据
- 体现了实际应用中的数据来源情况

## 🚀 导入步骤

### 1. SQL数据导入
```sql
-- 导入基础数据
source conservation_water_level_sample_data.sql;

-- 或导入扩展数据
source conservation_water_level_extended_data.sql;
```

### 2. Excel批量导入
1. 下载 `conservation_water_level_import_data.csv`
2. 转换为Excel格式(.xlsx)
3. 使用系统的批量导入功能
4. 验证导入结果

### 3. 数据验证
- 检查数据完整性
- 验证约束条件
- 确认时间序列连续性
- 测试分析功能

## ⚠️ 注意事项

1. **数据库表名**: 确保使用正确的表名前缀 `tb_`
2. **字段类型**: 注意decimal字段的精度设置
3. **时间格式**: 统一使用 `YYYY-MM-DD HH:mm:ss` 格式
4. **外键约束**: 确保station_id在相关表中存在
5. **数据范围**: 水位、降雨量等数值应在合理范围内

## 🔧 故障排除

### 常见问题
1. **主键冲突**: 检查ID是否重复
2. **外键约束**: 确保测点数据存在
3. **数据类型错误**: 检查数值字段格式
4. **时间格式错误**: 统一时间格式

### 解决方案
1. 清空现有测试数据后重新导入
2. 检查数据库表结构是否匹配
3. 验证字段长度和精度设置
4. 确认字符编码为UTF-8

通过这些测试数据，可以全面验证涵养水位功能的各个方面，包括数据录入、分析算法、风险评估、报告生成等核心功能。
