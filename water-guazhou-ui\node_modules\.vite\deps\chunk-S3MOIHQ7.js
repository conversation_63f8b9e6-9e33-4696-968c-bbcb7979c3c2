import {
  M as M2,
  k
} from "./chunk-TL5Y53I4.js";
import {
  V,
  e2 as e6,
  g as g4,
  p as p3,
  w as w4
} from "./chunk-PTI7U6FU.js";
import {
  i as i3
} from "./chunk-ZOIBK6WV.js";
import {
  n as n3
} from "./chunk-YELYN22P.js";
import {
  O,
  Q
} from "./chunk-ZIKXCGU7.js";
import {
  e as e4
} from "./chunk-XSQFM27N.js";
import {
  o as o2
} from "./chunk-EM6CPBT6.js";
import {
  l as l3
} from "./chunk-JJ3NE6DY.js";
import {
  D as D2,
  R
} from "./chunk-4VO6N7OL.js";
import {
  e as e5,
  r as r4
} from "./chunk-OYGWWPGZ.js";
import {
  r as r3
} from "./chunk-77E52HT5.js";
import {
  c as c2
} from "./chunk-T6GIT4YI.js";
import {
  p as p4
} from "./chunk-WX7B7OKM.js";
import {
  m2 as m
} from "./chunk-UQWZJZ2S.js";
import {
  d as d3,
  g as g3,
  s as s2
} from "./chunk-CDZ24ELJ.js";
import {
  M,
  i as i2
} from "./chunk-VHLK35TF.js";
import {
  rn
} from "./chunk-UYAKJRPP.js";
import {
  d as d2
} from "./chunk-Q4VCSCSY.js";
import {
  l as l2,
  w as w3
} from "./chunk-QUHG7NMD.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  w as w2
} from "./chunk-63M4K32A.js";
import {
  c,
  i
} from "./chunk-G5KX4JSG.js";
import {
  a as a3
} from "./chunk-EIGTETCG.js";
import {
  E,
  P,
  _,
  e as e3,
  g as g2,
  z
} from "./chunk-MQAXMQFG.js";
import {
  n as n2,
  r as r2
} from "./chunk-36FLFRUE.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  D,
  g,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  p as p2
} from "./chunk-REW33H3I.js";
import {
  a,
  d,
  e as e2,
  f,
  l,
  o,
  p,
  q,
  r,
  t,
  w
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/draw/DrawingMode.js
var c3 = ["freehand", "hybrid", "click"];
var e7 = "click";

// node_modules/@arcgis/core/views/interactive/dragEventPipeline.js
function y2(t3, e9) {
  let a5 = null, o4 = null;
  return (s3) => {
    if ("cancel" === s3.action) return void (r(o4) && (o4.execute({ action: "cancel" }), a5 = null, o4 = null));
    const c5 = { action: s3.action, screenStart: s3.start, screenEnd: s3.screenPoint };
    "start" === s3.action && t(a5) && (a5 = new U(), o4 = new U(), e9(t3, a5, o4, s3.pointerType, c5)), r(a5) && a5.execute(c5), "end" === s3.action && r(a5) && (a5 = null, o4 = null);
  };
}
function x2(t3, e9) {
  return t3.events.on("drag", y2(t3, e9));
}
function g5(t3, e9) {
  const n4 = [t3.x, t3.y, t3.z ?? 0], r5 = e9, a5 = [Math.cos(r5), Math.sin(r5)], o4 = Math.sqrt(a5[0] * a5[0] + a5[1] * a5[1]);
  if (0 === o4) return null;
  a5[0] /= o4, a5[1] /= o4;
  const s3 = (t4) => {
    const e10 = (t4.x - n4[0]) * a5[0] + (t4.y - n4[1]) * a5[1];
    t4.x = n4[0] + e10 * a5[0], t4.y = n4[1] + e10 * a5[1];
  };
  return (t4) => (s3(t4.mapStart), s3(t4.mapEnd), { ...t4, axis: a5 });
}
function E2(t3, e9) {
  let n4 = null;
  return (a5) => {
    if ("start" === a5.action && (n4 = S(t3, a5.mapStart.spatialReference, e9)), t(n4)) return null;
    const o4 = a5.mapEnd.x - a5.mapStart.x, s3 = a5.mapEnd.y - a5.mapStart.y, c5 = a5.mapEnd.z - a5.mapStart.z;
    return n4.move(o4, s3, c5), { ...a5, translationX: o4, translationY: s3, translationZ: c5 };
  };
}
function h(t3, e9) {
  return t(t3) ? null : t3.spatialReference.equals(e9) ? t3.clone() : rn(t3, e9);
}
function S(t3, e9, n4) {
  const a5 = t3.geometry, o4 = k(e9);
  if (t(a5)) return null;
  if ("mesh" === a5.type) return z2(t3, a5, o4, n4);
  const s3 = h(a5, o4), c5 = a5.spatialReference;
  return t(s3) ? null : { move: (e10, n5, r5) => {
    const a6 = i3(s3.clone(), e10, n5, r5);
    a6.spatialReference.equals(c5) ? t3.geometry = a6 : t3.geometry = rn(a6, c5);
  } };
}
function z2(t3, e9, r5, a5) {
  if (r(e9.transform)) return j2(t3, e9, e9.transform, r5);
  if (!e9.spatialReference.equals(r5)) return null;
  let o4 = 0, s3 = 0, c5 = 0;
  return { move: (n4, r6, i5) => {
    const l4 = n4 - o4, u = r6 - s3, m3 = i5 - c5;
    if (l4 || u || m3) {
      const f2 = new w2(e9.origin.x + l4, e9.origin.y + u, e9.origin.z + m3, e9.origin.spatialReference);
      e9.centerAt(f2, { geographic: a5 === l3.Global }), t3.notifyGeometryChanged(), o4 = n4, s3 = r6, c5 = i5;
    }
  } };
}
function j2(t3, e9, a5, o4) {
  const s3 = h(a5.getOriginPoint(e9.spatialReference), o4), l4 = e9.spatialReference;
  return t(s3) ? null : { move: (e10, r5, o5) => {
    const u = i3(s3.clone(), e10, r5, o5);
    if (u.spatialReference.equals(l4)) a5.origin = r2(u.x, u.y, u.z);
    else {
      const t4 = rn(u, l4);
      r(t4) && (a5.origin = r2(t4.x, t4.y, t4.z));
    }
    t3.notifyMeshTransformChanged(), t3.notifyGeometryChanged();
  } };
}
function v2(t3, e9 = null, a5) {
  var _a;
  let o4 = null;
  const s3 = r(e9) && !((_a = t3.spatialReference) == null ? void 0 : _a.equals(e9)) ? (t4) => r(t4) ? rn(t4, e9) : t4 : (t4) => t4, c5 = { exclude: [], ...a5 };
  return (e10) => {
    if ("start" === e10.action && (o4 = s3(t3.toMap(e10.screenStart, c5))), t(o4)) return null;
    const a6 = s3(t3.toMap(e10.screenEnd, c5));
    return r(a6) ? { ...e10, mapStart: o4, mapEnd: a6 } : null;
  };
}
function R2(t3, e9) {
  const r5 = t3.map((t4) => e2(E2(t4, e9))).filter((t4) => r(t4));
  return (t4) => {
    const e10 = t4.mapEnd.x - t4.mapStart.x, n4 = t4.mapEnd.y - t4.mapStart.y, a5 = t4.mapEnd.z - t4.mapStart.z;
    return r5.forEach((e11) => e11(t4)), { ...t4, translationX: e10, translationY: n4, translationZ: a5 };
  };
}
function w5(e9, n4) {
  const r5 = /* @__PURE__ */ new Map();
  for (const a5 of n4) r5.set(a5, p2(e9[a5]));
  return (t3) => (r5.forEach((t4, n5) => {
    e9[n5] = t4;
  }), t3);
}
function M3(t3) {
  return r(t3.geometry) && "mesh" === t3.geometry.type ? P2(t3, t3.geometry) : w5(t3, ["geometry"]);
}
function P2(t3, e9) {
  const r5 = r(e9.transform) ? e9.transform.clone() : null, a5 = e9.vertexAttributes.clonePositional();
  return (n4) => (e9.transform = r5, e9.vertexAttributes = a5, t3.notifyGeometryChanged(), n4);
}
function q2(t3) {
  const e9 = t3.map((t4) => e2(M3(t4))).filter((t4) => r(t4));
  return (t4) => (e9.forEach((e10) => e10(t4)), t4);
}
function D3() {
  let t3 = 0, e9 = 0, n4 = 0;
  return (r5) => {
    "start" === r5.action && (t3 = r5.mapStart.x, e9 = r5.mapStart.y, n4 = r5.mapStart.z);
    const a5 = r5.mapEnd.x - t3, o4 = r5.mapEnd.y - e9, s3 = r5.mapEnd.z - n4;
    return t3 = r5.mapEnd.x, e9 = r5.mapEnd.y, n4 = r5.mapEnd.z, { ...r5, mapDeltaX: a5, mapDeltaY: o4, mapDeltaZ: s3, mapDeltaSpatialReference: r5.mapStart.spatialReference };
  };
}
function b() {
  let t3 = 0, e9 = 0;
  return (n4) => {
    "start" === n4.action && (t3 = n4.screenStart.x, e9 = n4.screenStart.y);
    const r5 = n4.screenEnd.x - t3, a5 = n4.screenEnd.y - e9;
    return t3 = n4.screenEnd.x, e9 = n4.screenEnd.y, { ...n4, screenDeltaX: r5, screenDeltaY: a5 };
  };
}
function C(t3, n4) {
  let r5 = null, a5 = 0, o4 = 0;
  return (c5) => {
    var _a;
    if ("start" === c5.action && (r5 = (_a = t3.toScreen) == null ? void 0 : _a.call(t3, n4), null != r5 && (r5.x < 0 || r5.x > t3.width || r5.y < 0 || r5.y > t3.height ? r5 = null : (a5 = c5.screenStart.x - r5.x, o4 = c5.screenStart.y - r5.y))), null == r5) return null;
    const i5 = a3(c5.screenEnd.x - a5, 0, t3.width), l4 = a3(c5.screenEnd.y - o4, 0, t3.height), u = c(i5, l4);
    return c5.screenStart = r5, c5.screenEnd = u, c5;
  };
}
var G = () => {
};
var U = class _U {
  constructor() {
    this.execute = G;
  }
  next(t3, e9 = new _U()) {
    return r(t3) && (this.execute = (r5) => {
      const a5 = t3(r5);
      r(a5) && e9.execute(a5);
    }), e9;
  }
};
function X(t3, e9, r5 = []) {
  if ("2d" === t3.type) return (t4) => t4;
  let a5 = null;
  return (o4) => {
    "start" === o4.action && (a5 = t3.toMap(o4.screenStart, { exclude: r5 }), r(a5) && (a5.z = d3(a5, t3, e9)));
    const s3 = t3.toMap(o4.screenEnd, { exclude: r5 });
    r(s3) && (s3.z = d3(s3, t3, e9));
    const c5 = r(a5) && r(s3) ? { sceneStart: a5, sceneEnd: s3 } : null;
    return { ...o4, scenePoints: c5 };
  };
}
function Y(t3, e9, n4) {
  const r5 = f(e9.elevationProvider.getElevation(t3.x, t3.y, t3.z ?? 0, t3.spatialReference, "scene")) ?? 0, a5 = M2(t3);
  return a5.z = r5, a5.hasZ = true, a5.z = d3(a5, e9, n4), a5;
}
function Z(t3, e9) {
  if ("2d" === t3.type) return (t4) => t4;
  let r5 = null;
  return (a5) => {
    "start" === a5.action && (r5 = Y(a5.mapStart, t3, e9));
    const o4 = Y(a5.mapEnd, t3, e9), s3 = r(r5) && r(o4) ? { sceneStart: r5, sceneEnd: o4 } : null;
    return { ...a5, scenePoints: s3 };
  };
}

// node_modules/@arcgis/core/views/interactive/snapping/SnappingDragPipelineStep.js
function m2({ predicate: a5 = () => true, snappingManager: c5, snappingContext: l4, updatingHandles: u, useZ: f2 = true }) {
  const d4 = new U();
  if (t(c5)) return { snappingStep: [j3, d4], cancelSnapping: j3 };
  let m3, z3 = null, T = null, k3 = null;
  const w6 = () => {
    z3 = w(z3), c5.doneSnapping(), r(T) && T.frameTask.remove(), T = null, m3 = p(m3), k3 = null;
  }, C2 = g6(c5, f2, d4);
  let E3 = null, I = null, U2 = null;
  return { snappingStep: [(n4) => {
    if (!a5(n4)) return n4;
    const { action: o4 } = n4;
    if ("start" === o4) {
      const { info: e9 } = n4, o5 = x3(c5.view);
      if (T = P3(l4, n4, o5), T.context.selfSnappingZ = null, !f2 && r(e9)) {
        const n5 = Z2(l4.coordinateHelper, e9.handle.component);
        r(n5) && (T.context.selfSnappingZ = { value: n5, elevationInfo: l4.elevationInfo });
      }
    }
    if (r(T)) {
      const { context: t3, originalScenePos: a6, originalPos: l5 } = T, { mapEnd: p6, mapStart: d5, scenePoints: g8 } = n4, x4 = h2(l5, S2(p6, d5)), P4 = S2(d5, l5), Z3 = { ...n4, action: "update" }, j4 = T.context, w7 = v3(a6, g8), b2 = c5.update({ point: x4, scenePoint: w7, context: t3 });
      if (U2 = b2, y3(p6, b2, P4, f2), E3 = x4, I = w7, "end" !== o4) {
        const { frameTask: n5 } = T;
        t(z3) && (z3 = new AbortController()), k3 = (e9) => {
          u.addPromise(g(C2({ frameTask: n5, event: Z3, context: j4, point: x4, scenePoint: w7, delta: P4, getLastState: () => ({ point: E3, scenePoint: I, updatePoint: e9.forceUpdate ? null : U2 }) }, e2(z3).signal)));
        }, k3({ forceUpdate: false }), t(m3) && (m3 = l2(() => c5.options.effectiveEnabled, () => k3 == null ? void 0 : k3({ forceUpdate: true })));
      }
    }
    return "end" === o4 && w6(), n4;
  }, d4], cancelSnapping: (e9) => (w6(), e9) };
}
function g6(n4, o4, i5) {
  return x(async ({ frameTask: r5, point: a5, scenePoint: s3, context: l4, event: p6, delta: u, getLastState: f2 }, d4) => {
    const m3 = await r5.schedule(() => n4.snap({ point: a5, scenePoint: s3, context: l4, signal: d4 }), d4);
    if (m3.valid) {
      let s4 = await r5.schedule(() => m3.apply(), d4);
      const g8 = f2();
      r(g8.point) && a5 !== g8.point && (s4 = n4.update({ point: g8.point, scenePoint: g8.scenePoint, context: l4 })), !t(g8.updatePoint) && i2(s4, g8.updatePoint) || (y3(p6.mapEnd, s4, u, o4), i5.execute(p6));
    }
  });
}
function x3(e9) {
  return "3d" === e9.type ? e9.resourceController.scheduler.registerTask(R.SNAPPING) : D2;
}
function P3(e9, n4, o4) {
  return { context: new e6({ editGeometryOperations: e9.editGeometryOperations, elevationInfo: e9.elevationInfo, pointer: e9.pointer, vertexHandle: r(n4.info) ? n4.info.handle : null, excludeFeature: e9.excludeFeature, visualizer: e9.visualizer }), originalPos: r(n4.snapOrigin) ? e9.coordinateHelper.vectorToDehydratedPoint(n4.snapOrigin) : n4.mapStart, originalScenePos: r(n4.scenePoints) ? n4.scenePoints.sceneStart : null, frameTask: o4 };
}
function h2(e9, [n4, t3, o4]) {
  const i5 = M2(e9);
  return i5.x += n4, i5.y += t3, i5.hasZ && (i5.z += o4), i5;
}
function v3(n4, t3) {
  return t(n4) || t(t3) ? null : h2(n4, S2(t3.sceneEnd, t3.sceneStart));
}
function S2(e9, n4) {
  const t3 = e9.hasZ && n4.hasZ ? e9.z - n4.z : 0;
  return [e9.x - n4.x, e9.y - n4.y, t3];
}
function y3(e9, n4, [t3, o4, i5], r5) {
  e9.x = n4.x + t3, e9.y = n4.y + o4, r5 && e9.hasZ && n4.hasZ && (e9.z = n4.z + i5);
}
function Z2(n4, o4) {
  if (!n4.hasZ()) return null;
  const i5 = o4.vertices;
  let r5 = null;
  for (const a5 of i5) {
    const o5 = n4.getZ(a5.pos);
    if (r(r5) && r(o5) && Math.abs(o5 - r5) > 1e-6) return null;
    t(r5) && (r5 = o5);
  }
  return r5;
}
function j3(e9) {
  return e9;
}

// node_modules/@arcgis/core/views/draw/DrawManipulator.js
var t2 = class {
  constructor({ grabbableForEvent: t3 }) {
    this.events = new n(), this.interactive = true, this.selectable = false, this.cursor = null, this.grabbable = true, this.grabbableForEvent = t3;
  }
  intersectionDistance(e9, t3) {
    return 0;
  }
  attach() {
  }
  detach() {
  }
  onElevationChange() {
  }
  onViewChange() {
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/SnappingOperation.js
var h3 = class extends v {
  constructor(t3) {
    super(t3), this.constrainResult = (t4) => t4, this._snapPoints = null, this._frameTask = null, this._abortController = null, this._stagedPoint = null, this._snap = x(async (t4, s3, r5, e9) => {
      const n4 = this._frameTask;
      if (t(n4)) return;
      const a5 = await n4.schedule(() => s3.snap({ ...t4, context: r5, signal: e9 }), e9);
      a5.valid && await n4.schedule(() => {
        this.stagedPoint = a5.apply(), t4 !== this._snapPoints && r(this._snapPoints) && (this.stagedPoint = s3.update({ ...this._snapPoints, context: r5 }));
      }, e9);
    });
  }
  get stagedPoint() {
    return this._stagedPoint;
  }
  set stagedPoint(t3) {
    this._stagedPoint = this.constrainResult(t3);
  }
  initialize() {
    var _a, _b;
    const t3 = "3d" === this.view.type ? (_b = (_a = this.view) == null ? void 0 : _a.resourceController) == null ? void 0 : _b.scheduler : null;
    this._frameTask = r(t3) ? t3.registerTask(R.SNAPPING) : D2;
  }
  destroy() {
    this._abortController = w(this._abortController), this._frameTask = p(this._frameTask);
  }
  update(t3, s3, o4) {
    this._snapPoints = t3;
    const { point: r5, scenePoint: e9 } = t3, i5 = s3.update({ point: r5, scenePoint: e9, context: o4 });
    return this.stagedPoint = i5, i5;
  }
  async snap(t3, s3, o4) {
    const { point: r5, scenePoint: e9 } = t3;
    return this.stagedPoint = s3.update({ point: r5, scenePoint: e9, context: o4 }), this._snapPoints = t3, t(this._abortController) && (this._abortController = new AbortController()), this._snap(t3, s3, o4, this._abortController.signal);
  }
  async resnap(t3, s3) {
    t(this._snapPoints) || await this.snap(this._snapPoints, t3, s3);
  }
  abort() {
    this._abortController = w(this._abortController), this._snapPoints = null;
  }
};
e([y({ constructOnly: true })], h3.prototype, "view", void 0), e([y()], h3.prototype, "stagedPoint", null), e([y()], h3.prototype, "constrainResult", void 0), e([y()], h3.prototype, "_stagedPoint", void 0), h3 = e([a2("esri.views.interactive.snapping.SnappingOperation")], h3);

// node_modules/@arcgis/core/views/draw/DrawOperation.js
var k2 = class extends n.EventedMixin(d2) {
  constructor(e9) {
    super(e9), this._createOperationCompleted = false, this._pointerDownStates = /* @__PURE__ */ new Set(), this.isDraped = true, this.labelOptions = new c2(), this.tooltipOptions = new p4(), this.snapToSceneEnabled = null, this.lastVertex = null, t(e9.elevationInfo) && (this.elevationInfo = s2(!!e9.hasZ));
  }
  initialize() {
    const { geometryType: e9, view: t3 } = this, i5 = t3.spatialReference, n4 = "viewingMode" in t3.state ? t3.state.viewingMode : l3.Local, s3 = "segment" === e9 || "multipoint" === e9 ? "polyline" : e9;
    this.coordinateHelper = w4(this.hasZ, this.hasM, i5), this._editGeometryOperations = new V(new g4(s3, this.coordinateHelper)), this._snappingOperation = new h3({ view: t3, constrainResult: (e10) => {
      var _a;
      return t(e10) ? e10 : (_a = this._getEffectiveDrawSurface()) == null ? void 0 : _a.constrainZ(e10);
    } }), this.handles.add(l2(() => this.stagedVertex, (e10) => {
      t(e10) || this.emit("cursor-update", { updated: null, vertices: [{ componentIndex: 0, vertexIndex: this._activeComponent.vertices.length, coordinates: this.coordinateHelper.pointToArray(e10) }], operation: "apply", type: "vertex-update" });
    }, { sync: true, equals: (e10, t4) => d(e10, t4, i2) })), this._activeComponent = new p3(i5, n4), this._editGeometryOperations.data.components.push(this._activeComponent);
    const a5 = this.segmentLabels;
    r(a5) && (a5.context = { view: t3, editGeometryOperations: this._editGeometryOperations, elevationInfo: this.elevationInfo, labelOptions: this.labelOptions }, this.handles.add([l2(() => this.labelOptions.enabled, (e10) => {
      a5.visible = e10;
    }, w3), this.on("cursor-update", () => {
      const e10 = this.stagedVertex;
      a5.stagedVertex = r(e10) ? this.coordinateHelper.pointToVector(e10) : null;
    })])), this.handles.add(this._editGeometryOperations.on(["vertex-add", "vertex-update", "vertex-remove"], (e10) => {
      const t4 = e10.vertices.map((e11) => ({ componentIndex: 0, vertexIndex: e11.index, coordinates: this.coordinateHelper.vectorToArray(e11.pos) })), i6 = t4.map((e11) => e11.coordinates);
      switch (e10.type) {
        case "vertex-add":
          this.emit(e10.type, { ...e10, added: i6, vertices: t4 });
          break;
        case "vertex-update":
          this.emit(e10.type, { ...e10, updated: i6, vertices: t4 });
          break;
        case "vertex-remove":
          this.emit(e10.type, { ...e10, removed: i6, vertices: t4 });
      }
      const n5 = this._activeComponent.getLastVertex(), s4 = r(n5) ? this.coordinateHelper.vectorToDehydratedPoint(n5.pos) : null;
      (t(s4) || t(this.lastVertex) || !i2(this.lastVertex, s4)) && (this.lastVertex = s4);
    })), this._manipulator = new t2({ grabbableForEvent: (e10) => "click" !== this.drawingMode || "touch" === e10.pointerType && this._snappingEnabled && 1 === this._pointerDownStates.size }), this.manipulators.add(this._manipulator), this._manipulator.grabbable = "point" !== e9, this.handles.add([this._createManipulatorDragPipeline(this._manipulator), this._manipulator.events.on("immediate-click", (e10) => this._onImmediateClick(e10)), this._manipulator.events.on("immediate-double-click", (e10) => this._onImmediateDoubleClick(e10))]), m(this, () => {
      const e10 = l(this.view.inputManager.latestPointerType, "mouse"), t4 = this._getSnappingContext(e10);
      r(this.snappingManager) && this.updatingHandles.addPromise(g(this._snappingOperation.resnap(this.snappingManager, t4)));
    });
  }
  destroy() {
    a(this.segmentLabels), a(this._snappingOperation), this._editGeometryOperations = a(this._editGeometryOperations);
  }
  get _snappingEnabled() {
    return r(this.snappingManager) && this.snappingManager.options.effectiveEnabled;
  }
  get _requiresScenePoint() {
    const e9 = this._getEffectiveDrawSurface();
    return "3d" === this.view.type && this.drawSurface !== e9;
  }
  get canRedo() {
    return this._editGeometryOperations.canRedo;
  }
  get canUndo() {
    return this._editGeometryOperations.canUndo;
  }
  get committedVertices() {
    return this._activeComponent.vertices.map((e9) => this.coordinateHelper.vectorToArray(e9.pos));
  }
  set drawingMode(e9) {
    this._set("drawingMode", e9 ?? e7);
  }
  get interactive() {
    return this._manipulator.interactive;
  }
  set interactive(e9) {
    this._manipulator.interactive = e9;
  }
  get isCompleted() {
    return this._createOperationCompleted;
  }
  get numCommittedVertices() {
    return this._activeComponent.vertices.length;
  }
  get numVertices() {
    return r(this.stagedVertex) ? this._activeComponent.vertices.length + 1 : this._activeComponent.vertices.length;
  }
  get snappingOptions() {
    return r(this.snappingManager) ? this.snappingManager.options : null;
  }
  get stagedVertex() {
    return this._snappingOperation.stagedPoint;
  }
  set stagedVertex(e9) {
    this._snappingOperation.stagedPoint = p2(e9);
  }
  get updating() {
    return this.updatingHandles.updating;
  }
  get vertices() {
    const e9 = this.committedVertices;
    return r(this.stagedVertex) && e9.push(this.coordinateHelper.pointToArray(this.stagedVertex)), e9;
  }
  cancel() {
    this.complete({ aborted: true });
  }
  commitStagedVertex() {
    if (this._snappingOperation.abort(), r(this.stagedVertex)) {
      const { stagedVertex: e9 } = this;
      this.stagedVertex = null, this._editGeometryOperations.appendVertex(this.coordinateHelper.pointToVector(e9));
    }
  }
  complete(e9) {
    const t3 = e9 && e9.aborted || false;
    this._snappingOperation.abort(), r(this.snappingManager) && this.snappingManager.doneSnapping(), "segment" === this.geometryType || "point" === this.geometryType ? this.commitStagedVertex() : this.stagedVertex = null;
    const i5 = "multipoint" === this.geometryType && 0 === this.numVertices || "polyline" === this.geometryType && this.numVertices < 2 || "polygon" === this.geometryType && this.numVertices < 3;
    this._createOperationCompleted = !i5, (this.isCompleted || t3) && this.emit("complete", { vertices: this.vertices.map((e10, t4) => ({ componentIndex: 0, vertexIndex: t4, coordinates: e10 })), aborted: t3, type: "complete" });
  }
  onInputEvent(e9) {
    switch (e9.type) {
      case "pointer-down":
        this._pointerDownStates.add(e9.pointerId);
        break;
      case "pointer-up":
        this._pointerDownStates.delete(e9.pointerId);
    }
    switch (e9.type) {
      case "pointer-move":
        return this._onPointerMove(e9);
      case "hold":
        return this._onHold(e9);
    }
  }
  redo() {
    this._editGeometryOperations.redo();
  }
  undo() {
    r(this.snappingManager) && this.snappingManager.doneSnapping(), this._editGeometryOperations.undo();
  }
  _closeOnClickVertexIndex(e9) {
    const t3 = this._activeComponent;
    if ("polygon" === this.geometryType && t3.vertices.length > 2) {
      if (this._vertexWithinPointerDistance(t3.vertices[0].pos, e9)) return 0;
      if (this._vertexWithinPointerDistance(t3.vertices[t3.vertices.length - 1].pos, e9)) return t3.vertices.length - 1;
    }
    return null;
  }
  _createManipulatorDragPipeline(e9) {
    switch (e2(this.drawingMode)) {
      case "click":
        return this._createManipulatorDragPipelineClick(e9);
      case "freehand":
        return this._createManipulatorDragPipelineFreehand(e9);
      case "hybrid":
        return this._createManipulatorDragPipelineHybrid(e9);
    }
  }
  _createManipulatorDragPipelineClick(e9) {
    return x2(e9, (e10, t3, i5, n4) => {
      const r5 = "touch" === n4 && this._snappingEnabled;
      if (this.isCompleted || !r5) return;
      const { snappingStep: s3, cancelSnapping: a5 } = m2({ predicate: () => r5, snappingManager: this.snappingManager, snappingContext: new e6({ editGeometryOperations: this._editGeometryOperations, elevationInfo: this.elevationInfo, pointer: n4, visualizer: this.snappingVisualizer }), updatingHandles: this.updatingHandles, useZ: !this._requiresScenePoint });
      i5 = i5.next((e11) => (r5 && r(this.snappingManager) && this.snappingManager.doneSnapping(), e11)).next(a5), t3.next(this._screenToMapDragEventStep()).next((e11) => ("start" === e11.action && (this.stagedVertex = e11.mapStart, ("segment" === this.geometryType || r5 && 0 === this.numVertices) && this.commitStagedVertex()), e11)).next(X(this.view, this.elevationInfo)).next(...s3).next((e11) => (r5 && (this.stagedVertex = e11.mapEnd, "end" === e11.action && this.commitStagedVertex()), e11)).next((e11) => ("end" === e11.action && ("segment" !== this.geometryType && "point" !== this.geometryType || this.complete()), e11));
    });
  }
  _createManipulatorDragPipelineFreehand(e9) {
    return x2(e9, (e10, t3) => {
      this.isCompleted || t3.next(this._screenToMapDragEventStep()).next((e11) => ("start" === e11.action && (t(this.stagedVertex) && (this.stagedVertex = e11.mapStart), "segment" === this.geometryType && this.commitStagedVertex()), e11)).next((e11) => {
        switch (e11.action) {
          case "start":
          case "update":
            this.stagedVertex = e11.mapEnd, "polygon" !== this.geometryType && "polyline" !== this.geometryType || this.commitStagedVertex();
            break;
          case "end":
            this.complete();
        }
        return e11;
      });
    });
  }
  _createManipulatorDragPipelineHybrid(e9) {
    return x2(e9, (e10, t3) => {
      this.isCompleted || t3.next(this._screenToMapDragEventStep()).next((e11) => ("start" === e11.action && (t(this.stagedVertex) && (this.stagedVertex = e11.mapStart), this.commitStagedVertex()), e11)).next((e11) => {
        switch (e11.action) {
          case "start":
          case "update":
            this.stagedVertex = e11.mapEnd, "polygon" !== this.geometryType && "polyline" !== this.geometryType || this.commitStagedVertex();
            break;
          case "end":
            "segment" !== this.geometryType && "point" !== this.geometryType || this.complete();
        }
        return e11;
      });
    });
  }
  get _drawAtFixedElevation() {
    return ("segment" === this.geometryType || "polygon" === this.geometryType) && this.numCommittedVertices > 0;
  }
  _getEffectiveDrawSurface() {
    if (t(this.elevationDrawSurface)) return this.drawSurface;
    if (!this.coordinateHelper.hasZ()) return this.elevationDrawSurface.defaultZ = null, this.elevationDrawSurface;
    let e9 = this.defaultZ, t3 = false;
    return r(this.elevationInfo) && "absolute-height" === this.elevationInfo.mode && (t3 = true), r(this.snapToSceneEnabled) && (t3 = this.snapToSceneEnabled), r(this.elevationInfo) && "on-the-ground" === this.elevationInfo.mode && (t3 = false), this._drawAtFixedElevation && (e9 = this.coordinateHelper.getZ(this._activeComponent.vertices[0].pos), t3 = false), t3 ? this.drawSurface : (this.elevationDrawSurface.defaultZ = e9, this.elevationDrawSurface);
  }
  _mapToScreen(e9) {
    var _a;
    return (_a = this._getEffectiveDrawSurface()) == null ? void 0 : _a.mapToScreen(e9);
  }
  _onHold(e9) {
    this._snappingOperation.abort(), "click" === this.drawingMode && "touch" === e9.pointerType && this._snappingEnabled && (this.stagedVertex = e9.mapPoint), e9.stopPropagation();
  }
  _onImmediateClick(e9) {
    if ("mouse" === e9.pointerType && 2 === e9.button || this._manipulator.dragging) return;
    const t3 = this._activeComponent, i5 = this._closeOnClickVertexIndex(e9.screenPoint);
    if (r(i5)) return e9.stopPropagation(), void this.complete();
    const n4 = this._screenToMap(e9.screenPoint);
    if (r(n4)) switch (this.drawingMode) {
      case "freehand":
        "point" === this.geometryType && (r(this.stagedVertex) ? this.commitStagedVertex() : this._editGeometryOperations.appendVertex(this.coordinateHelper.pointToVector(n4)), this.complete());
        break;
      case "click":
      case "hybrid":
        this._snappingOperation.abort(), r(this.stagedVertex) ? this.commitStagedVertex() : this._editGeometryOperations.appendVertex(this.coordinateHelper.pointToVector(n4)), ("point" === this.geometryType || "segment" === this.geometryType && 2 === t3.vertices.length || "segment" === this.geometryType && "hybrid" === this.drawingMode && 1 === t3.vertices.length) && this.complete();
    }
    e9.stopPropagation();
  }
  _onImmediateDoubleClick(e9) {
    this._manipulator.dragging || "point" === this.geometryType || (this.complete(), e9.stopPropagation());
  }
  _onPointerMove(e9) {
    var _a;
    const t3 = c(e9.x, e9.y), i5 = this._snappingOperation;
    if (this._manipulator.dragging || this._pointerDownStates.has(e9.pointerId) || this._manipulator.grabbing || !this._manipulator.interactive) return void i5.abort();
    e9.stopPropagation();
    const n4 = this._closeOnClickVertexIndex(t3);
    if (r(n4)) return this._closeOnVertex(n4), void i5.abort();
    const s3 = this._screenToMap(t3), a5 = this._requiresScenePoint ? (_a = this.drawSurface) == null ? void 0 : _a.screenToMap(t3) : null;
    if (this._manipulator.cursor = r(s3) ? "crosshair" : null, t(s3)) return void i5.abort();
    const p6 = this.snappingManager;
    if (t(p6)) return this.stagedVertex = s3, void i5.abort();
    const c5 = this._getSnappingContext(e9.pointerType);
    this.updatingHandles.addPromise(g(i5.snap({ point: s3, scenePoint: a5 }, p6, c5)));
  }
  _closeOnVertex(e9) {
    this.stagedVertex = null;
    const t3 = { componentIndex: 0, vertexIndex: e9, coordinates: this.coordinateHelper.vectorToArray(this._activeComponent.vertices[e9].pos) };
    this.emit("cursor-update", { updated: null, vertices: [t3], operation: "apply", type: "vertex-update" });
  }
  _screenToMap(e9) {
    var _a;
    return (_a = this._getEffectiveDrawSurface()) == null ? void 0 : _a.screenToMap(e9);
  }
  _screenToMapDragEventStep() {
    let e9 = null;
    return (t3) => {
      if ("start" === t3.action && (e9 = this._screenToMap(t3.screenStart)), t(e9)) return null;
      const i5 = this._screenToMap(t3.screenEnd);
      return r(i5) ? { ...t3, mapStart: e9, mapEnd: i5 } : null;
    };
  }
  _vertexWithinPointerDistance(e9, t3) {
    const i5 = 25, n4 = this._mapToScreen(this.coordinateHelper.vectorToDehydratedPoint(e9));
    return !!r(n4) && H(n4, t3, i5);
  }
  _getSnappingContext(e9) {
    const t3 = this._drawAtFixedElevation ? o(this.elevationDrawSurface, ({ defaultZ: e10 }) => e10) : null;
    return new e6({ editGeometryOperations: this._editGeometryOperations, elevationInfo: this.elevationInfo, pointer: e9, visualizer: this.snappingVisualizer, selfSnappingZ: r(t3) ? { value: t3, elevationInfo: this.elevationInfo } : null });
  }
};
function H(e9, t3, i5) {
  const n4 = e9.x - t3.x, r5 = e9.y - t3.y;
  return n4 * n4 + r5 * r5 <= i5;
}
e([y()], k2.prototype, "_snappingEnabled", null), e([y()], k2.prototype, "defaultZ", void 0), e([y()], k2.prototype, "isDraped", void 0), e([y({ value: e7 })], k2.prototype, "drawingMode", null), e([y({ constructOnly: true })], k2.prototype, "elevationDrawSurface", void 0), e([y({ constructOnly: true })], k2.prototype, "elevationInfo", void 0), e([y({ constructOnly: true, type: c2 })], k2.prototype, "labelOptions", void 0), e([y({ constructOnly: true, type: p4 })], k2.prototype, "tooltipOptions", void 0), e([y({ constructOnly: true })], k2.prototype, "geometryType", void 0), e([y({ constructOnly: true })], k2.prototype, "hasM", void 0), e([y({ constructOnly: true })], k2.prototype, "hasZ", void 0), e([y({ constructOnly: true })], k2.prototype, "manipulators", void 0), e([y({ constructOnly: true })], k2.prototype, "drawSurface", void 0), e([y({ constructOnly: true })], k2.prototype, "segmentLabels", void 0), e([y({ constructOnly: true })], k2.prototype, "snappingManager", void 0), e([y({ constructOnly: true })], k2.prototype, "snappingVisualizer", void 0), e([y()], k2.prototype, "snapToSceneEnabled", void 0), e([y()], k2.prototype, "_snappingOperation", void 0), e([y()], k2.prototype, "stagedVertex", null), e([y()], k2.prototype, "lastVertex", void 0), e([y()], k2.prototype, "updating", null), e([y({ constructOnly: true })], k2.prototype, "view", void 0), k2 = e([a2("esri.views.draw.DrawOperation")], k2);

// node_modules/@arcgis/core/views/draw/drawSurfaces.js
var o3 = class {
  constructor(e9, t3, s3, i5 = null) {
    this._elevationInfo = e9, this.defaultZ = t3, this._view = s3, this._excludeGraphics = i5;
  }
  screenToMap(s3) {
    if (r(this.defaultZ)) return this._view.sceneIntersectionHelper.intersectElevationFromScreen(i(s3.x, s3.y), this._elevationInfo, this.defaultZ, this._excludeGraphics);
    const i5 = this._view.sceneIntersectionHelper.intersectElevationFromScreen(i(s3.x, s3.y), this._elevationInfo, 0, this._excludeGraphics);
    return r(i5) && (i5.z = void 0), i5;
  }
  mapToScreen(e9) {
    const t3 = M(e9.x, e9.y, g3(this._view, e9, this._elevationInfo), e9.spatialReference);
    return this._view.toScreen(t3);
  }
  constrainZ(t3) {
    const { defaultZ: s3 } = this;
    return r(s3) && t3.z !== s3 && ((t3 = M2(t3)).z = s3), t3;
  }
};
var c4 = class {
  constructor(e9, t3, s3 = []) {
    this.view = e9, this.elevationInfo = t3, this.exclude = s3;
  }
  screenToMap(t3) {
    const s3 = this.view.toMap(t3, { exclude: this.exclude });
    return r(s3) && (s3.z = d3(s3, this.view, this.elevationInfo)), s3;
  }
  mapToScreen(t3) {
    let i5 = t3;
    return r(this.elevationInfo) && (i5 = M(t3.x, t3.y, g3(this.view, t3, this.elevationInfo), t3.spatialReference)), this.view.toScreen(i5);
  }
  constrainZ(e9) {
    return e9;
  }
};
var a4 = class {
  constructor(e9, t3 = false, s3 = 0) {
    this.view = e9, this.hasZ = t3, this.defaultZ = s3, this.mapToScreen = (t4) => e9.toScreen(t4), this.screenToMap = t3 ? (t4) => {
      const i5 = e9.toMap(t4);
      return i5.z = s3, i5;
    } : (t4) => e9.toMap(t4);
  }
  constrainZ(e9) {
    const { defaultZ: t3 } = this;
    return this.hasZ && e9.z !== t3 && ((e9 = M2(e9)).z = t3), e9;
  }
};

// node_modules/@arcgis/core/views/interactive/ManipulatorCollection.js
var i4;
!function(t3) {
  t3[t3.WhenToolEditable = 0] = "WhenToolEditable", t3[t3.WhenToolNotEditable = 1] = "WhenToolNotEditable", t3[t3.Always = 2] = "Always";
}(i4 || (i4 = {}));
var e8 = class {
  constructor() {
    this._isToolEditable = true, this._manipulators = new j(), this._resourceContexts = { manipulator3D: {} }, this._attached = false;
  }
  set isToolEditable(t3) {
    this._isToolEditable = t3;
  }
  get length() {
    return this._manipulators.length;
  }
  add(t3, a5 = i4.WhenToolEditable) {
    this.addMany([t3], a5);
  }
  addMany(t3, a5 = i4.WhenToolEditable) {
    for (const i5 of t3) {
      const t4 = { manipulator: i5, visibilityPredicate: a5, attached: false };
      this._manipulators.add(t4), this._attached && this._updateManipulatorAttachment(t4);
    }
  }
  remove(t3) {
    for (let a5 = 0; a5 < this._manipulators.length; a5++) if (this._manipulators.getItemAt(a5).manipulator === t3) {
      const t4 = this._manipulators.splice(a5, 1)[0];
      this._detachManipulator(t4);
      break;
    }
  }
  removeAll() {
    this._manipulators.forEach((t3) => {
      this._detachManipulator(t3);
    }), this._manipulators.removeAll();
  }
  attach() {
    this._manipulators.forEach((t3) => {
      this._updateManipulatorAttachment(t3);
    }), this._attached = true;
  }
  detach() {
    this._manipulators.forEach((t3) => {
      this._detachManipulator(t3);
    }), this._attached = false;
  }
  destroy() {
    this.detach(), this._manipulators.forEach(({ manipulator: t3 }) => {
      t3.destroy && t3.destroy();
    }), this._manipulators.destroy(), this._resourceContexts = null;
  }
  on(t3, a5) {
    return this._manipulators.on(t3, (t4) => {
      a5(t4);
    });
  }
  forEach(t3) {
    for (const a5 of this._manipulators.items) t3(a5);
  }
  some(t3) {
    return this._manipulators.items.some(t3);
  }
  toArray() {
    const t3 = [];
    return this.forEach((a5) => t3.push(a5.manipulator)), t3;
  }
  intersect(t3, i5) {
    let e9 = null, o4 = Number.MAX_VALUE;
    return this._manipulators.forEach(({ manipulator: s3, attached: r5 }) => {
      if (!r5 || !s3.interactive) return;
      const n4 = s3.intersectionDistance(t3, i5);
      r(n4) && n4 < o4 && (o4 = n4, e9 = s3);
    }), e9;
  }
  _updateManipulatorAttachment(t3) {
    this._isManipulatorItemVisible(t3) ? this._attachManipulator(t3) : this._detachManipulator(t3);
  }
  _attachManipulator(t3) {
    t3.attached || (t3.manipulator.attach && t3.manipulator.attach(this._resourceContexts), t3.attached = true);
  }
  _detachManipulator(t3) {
    if (!t3.attached) return;
    const a5 = t3.manipulator;
    a5.grabbing = false, a5.dragging = false, a5.hovering = false, a5.selected = false, a5.detach && a5.detach(this._resourceContexts), t3.attached = false;
  }
  _isManipulatorItemVisible(t3) {
    return t3.visibilityPredicate === i4.Always || (this._isToolEditable ? t3.visibilityPredicate === i4.WhenToolEditable : t3.visibilityPredicate === i4.WhenToolNotEditable);
  }
};

// node_modules/@arcgis/core/views/interactive/InteractiveToolBase.js
var p5 = class extends v {
  constructor(t3) {
    super(t3), this.manipulators = new e8(), this.automaticManipulatorSelection = true, this.hasGrabbedManipulators = false, this.hasHoveredManipulators = false, this.firstGrabbedManipulator = null, this.created = false, this.removeIncompleteOnCancel = true, this._editableFlags = /* @__PURE__ */ new Map([[o2.MANAGER, true], [o2.USER, true]]), this._creationFinishedResolver = D();
  }
  get active() {
    return null != this.view && this.view.activeTool === this;
  }
  set visible(t3) {
    this._get("visible") !== t3 && (this._set("visible", t3), this._syncVisible());
  }
  get editable() {
    return this.getEditableFlag(o2.USER);
  }
  set editable(t3) {
    this.setEditableFlag(o2.USER, t3);
  }
  get updating() {
    return false;
  }
  get cursor() {
    return null;
  }
  get hasFocusedManipulators() {
    return this.hasGrabbedManipulators || this.hasHoveredManipulators;
  }
  destroy() {
    this.manipulators.destroy(), this._set("view", null);
  }
  onAdd() {
    this._syncVisible();
  }
  activate() {
    t(this.view) ? s.getLogger(this.declaredClass).error("Can't activate tool if view is not defined.") : (this.view.focus(), this.onActivate());
  }
  deactivate() {
    this.onDeactivate();
  }
  handleInputEvent(t3) {
    this.onInputEvent(t3);
  }
  handleInputEventAfter(t3) {
    this.onInputEventAfter(t3);
  }
  setEditableFlag(t3, e9) {
    this._editableFlags.set(t3, e9), this.manipulators.isToolEditable = this.internallyEditable, this._updateManipulatorAttachment(), t3 === o2.USER && this.notifyChange("editable"), this.onEditableChange(), this.onManipulatorSelectionChanged();
  }
  getEditableFlag(t3) {
    return this._editableFlags.get(t3) ?? false;
  }
  whenCreated() {
    return this._creationFinishedResolver.promise;
  }
  onManipulatorSelectionChanged() {
  }
  onActivate() {
  }
  onDeactivate() {
  }
  onShow() {
  }
  onHide() {
  }
  onEditableChange() {
  }
  onInputEvent(t3) {
  }
  onInputEventAfter(t3) {
  }
  get internallyEditable() {
    return this.getEditableFlag(o2.USER) && this.getEditableFlag(o2.MANAGER);
  }
  finishToolCreation() {
    this.created || this._creationFinishedResolver.resolve(this), this._set("created", true);
  }
  _syncVisible() {
    if (this.initialized) {
      if (this.visible) this._show();
      else if (this._hide(), this.active) return void (this.view.activeTool = null);
    }
  }
  _show() {
    this._updateManipulatorAttachment(), this.onShow();
  }
  _hide() {
    this._updateManipulatorAttachment(), this.onHide();
  }
  _updateManipulatorAttachment() {
    this.visible ? this.manipulators.attach() : this.manipulators.detach();
  }
};
e([y({ constructOnly: true })], p5.prototype, "view", void 0), e([y({ readOnly: true })], p5.prototype, "active", null), e([y({ value: true })], p5.prototype, "visible", null), e([y({ value: true })], p5.prototype, "editable", null), e([y({ readOnly: true })], p5.prototype, "manipulators", void 0), e([y({ readOnly: true })], p5.prototype, "updating", null), e([y()], p5.prototype, "cursor", null), e([y({ readOnly: true })], p5.prototype, "automaticManipulatorSelection", void 0), e([y()], p5.prototype, "hasFocusedManipulators", null), e([y()], p5.prototype, "hasGrabbedManipulators", void 0), e([y()], p5.prototype, "hasHoveredManipulators", void 0), e([y()], p5.prototype, "firstGrabbedManipulator", void 0), e([y({ readOnly: true })], p5.prototype, "created", void 0), e([y({ readOnly: true })], p5.prototype, "removeIncompleteOnCancel", void 0), p5 = e([a2("esri.views.interactive.InteractiveToolBase")], p5);

// node_modules/@arcgis/core/views/draw/support/surfaceCoordinateSystems.js
function g7(r5, e9, s3 = null) {
  return r(s3) ? [r5, e9, s3] : [r5, e9];
}
function R3(r5, e9, s3 = null) {
  return r(s3) ? { x: r5, y: e9, z: s3 } : { x: r5, y: e9 };
}
var W = class {
  constructor(t3) {
    this.spatialReference = t3;
  }
  mapToLocalMultiple(t3) {
    return q(t3.map((t4) => this.mapToLocal(t4)));
  }
  get doUnnormalization() {
    return false;
  }
};
var v4 = class extends W {
  constructor(t3, r5, o4 = null) {
    super(r5), this._defaultZ = o4, this.transform = e5(), this.transformInv = e5(), this.transform = r4(t3), r3(this.transformInv, this.transform);
  }
  makeMapPoint(t3, r5) {
    return g7(t3, r5, this._defaultZ);
  }
  mapToLocal(t3) {
    return R3(this.transform[0] * t3[0] + this.transform[2] * t3[1] + this.transform[4], this.transform[1] * t3[0] + this.transform[3] * t3[1] + this.transform[5]);
  }
  localToMap(t3) {
    return g7(this.transformInv[0] * t3.x + this.transformInv[2] * t3.y + this.transformInv[4], this.transformInv[1] * t3.x + this.transformInv[3] * t3.y + this.transformInv[5], this._defaultZ);
  }
};
var F = class extends W {
  constructor(t3, r5) {
    super(t3.spatialReference), this.view = t3, this.defaultZ = null, this.pWS = n2(), this.tangentFrameUpWS = n2(), this.tangentFrameRightWS = n2(), this.tangentFrameForwardWS = n2(), this.localFrameRightWS = n2(), this.localFrameUpWS = n2(), this.worldToLocalTransform = e4(), this.localToWorldTransform = e4(), this.scale = 1, this.scale = t3.resolution, this.referenceMapPoint = r5, this.defaultZ = r5.hasZ ? r5.z : null;
    const e9 = t3.state.camera.viewRight;
    this.view.renderCoordsHelper.toRenderCoords(this.referenceMapPoint, this.pWS), this.view.renderCoordsHelper.worldBasisAtPosition(this.pWS, n3.X, this.tangentFrameRightWS), this.view.renderCoordsHelper.worldBasisAtPosition(this.pWS, n3.Y, this.tangentFrameUpWS), this.view.renderCoordsHelper.worldBasisAtPosition(this.pWS, n3.Z, this.tangentFrameForwardWS);
    const s3 = n2();
    g2(s3, this.tangentFrameForwardWS, P(e9, this.tangentFrameForwardWS)), e3(this.localFrameRightWS, e9, s3), z(this.localFrameRightWS, this.localFrameRightWS), _(this.localFrameUpWS, this.tangentFrameForwardWS, this.localFrameRightWS), Q(this.worldToLocalTransform, this.localFrameRightWS, this.tangentFrameRightWS), O(this.localToWorldTransform, this.worldToLocalTransform);
  }
  get doUnnormalization() {
    return "global" === this.view.viewingMode;
  }
  makeMapPoint(t3, r5) {
    return g7(t3, r5, this.defaultZ);
  }
  mapToLocal(r5) {
    const e9 = n2();
    this.view.renderCoordsHelper.toRenderCoords(new w2({ x: r5[0], y: r5[1], spatialReference: this.spatialReference }), e9), E(e9, e9, this.worldToLocalTransform);
    const s3 = this.view.renderCoordsHelper.fromRenderCoords(e9, this.view.spatialReference);
    return r(s3) ? R3(s3.x / this.scale, s3.y / this.scale) : null;
  }
  localToMap(r5) {
    const e9 = n2();
    this.view.renderCoordsHelper.toRenderCoords(new w2({ x: r5.x * this.scale, y: r5.y * this.scale, spatialReference: this.spatialReference }), e9), E(e9, e9, this.localToWorldTransform);
    const s3 = this.view.renderCoordsHelper.fromRenderCoords(e9, this.view.spatialReference);
    return r(s3) ? g7(s3.x, s3.y, this.defaultZ) : null;
  }
};
function S3(t3, r5) {
  if ("2d" === t3.type) return new v4(t3.state.transform, t3.spatialReference, r5.length > 2 ? r5[2] : null);
  if ("3d" === t3.type) {
    const e9 = r5.length > 2 ? new w2({ x: r5[0], y: r5[1], z: r5[2], spatialReference: t3.spatialReference }) : new w2({ x: r5[0], y: r5[1], spatialReference: t3.spatialReference });
    return new F(t3, e9);
  }
  return null;
}

export {
  h3 as h,
  c3 as c,
  e7 as e,
  x2 as x,
  g5 as g,
  E2 as E,
  v2 as v,
  R2 as R,
  M3 as M,
  q2 as q,
  D3 as D,
  b,
  C,
  U,
  X,
  Z,
  m2 as m,
  k2 as k,
  o3 as o,
  c4 as c2,
  a4 as a,
  p5 as p,
  R3 as R2,
  S3 as S
};
//# sourceMappingURL=chunk-S3MOIHQ7.js.map
