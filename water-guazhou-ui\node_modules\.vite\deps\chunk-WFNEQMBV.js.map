{"version": 3, "sources": ["../../@arcgis/core/views/3d/support/debugFlags.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as E}from\"../../../chunks/tslib.es6.js\";import _ from\"../../../core/Accessor.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as S}from\"../../../core/accessorSupport/decorators/subclass.js\";let T=class extends _{constructor(){super(...arguments),this.SCENEVIEW_HITTEST_RETURN_INTERSECTOR=!1,this.DECONFLICTOR_SHOW_VISIBLE=!1,this.DECONFLICTOR_SHOW_INVISIBLE=!1,this.DECONFLICTOR_SHOW_GRID=!1,this.LABELS_SHOW_BORDER=!1,this.TEXT_SHOW_BASELINE=!1,this.TEXT_SHOW_BORDER=!1,this.OVERLAY_DRAW_DEBUG_TEXTURE=!1,this.OVERLAY_SHOW_CENTER=!1,this.SHOW_POI=!1,this.TESTS_DISABLE_OPTIMIZATIONS=!1,this.TESTS_DISABLE_FAST_UPDATES=!1,this.DRAW_MESH_GEOMETRY_NORMALS=!1,this.FEATURE_TILE_FETCH_SHOW_TILES=!1,this.FEATURE_TILE_TREE_SHOW_TILES=!1,this.TERRAIN_TILE_TREE_SHOW_TILES=!1,this.I3S_TREE_SHOW_TILES=!1,this.I3S_SHOW_MODIFICATIONS=!1,this.LOD_INSTANCE_RENDERER_DISABLE_UPDATES=!1,this.LOD_INSTANCE_RENDERER_COLORIZE_BY_LEVEL=!1,this.EDGES_SHOW_HIDDEN_TRANSPARENT_EDGES=!1,this.LINE_WIREFRAMES=!1,this.TERRAIN_USE_LEGACY_SHADING=!1}};E([o()],T.prototype,\"SCENEVIEW_HITTEST_RETURN_INTERSECTOR\",void 0),E([o()],T.prototype,\"DECONFLICTOR_SHOW_VISIBLE\",void 0),E([o()],T.prototype,\"DECONFLICTOR_SHOW_INVISIBLE\",void 0),E([o()],T.prototype,\"DECONFLICTOR_SHOW_GRID\",void 0),E([o()],T.prototype,\"LABELS_SHOW_BORDER\",void 0),E([o()],T.prototype,\"TEXT_SHOW_BASELINE\",void 0),E([o()],T.prototype,\"TEXT_SHOW_BORDER\",void 0),E([o()],T.prototype,\"OVERLAY_DRAW_DEBUG_TEXTURE\",void 0),E([o()],T.prototype,\"OVERLAY_SHOW_CENTER\",void 0),E([o()],T.prototype,\"SHOW_POI\",void 0),E([o()],T.prototype,\"TESTS_DISABLE_OPTIMIZATIONS\",void 0),E([o()],T.prototype,\"TESTS_DISABLE_FAST_UPDATES\",void 0),E([o()],T.prototype,\"DRAW_MESH_GEOMETRY_NORMALS\",void 0),E([o()],T.prototype,\"FEATURE_TILE_FETCH_SHOW_TILES\",void 0),E([o()],T.prototype,\"FEATURE_TILE_TREE_SHOW_TILES\",void 0),E([o()],T.prototype,\"TERRAIN_TILE_TREE_SHOW_TILES\",void 0),E([o()],T.prototype,\"I3S_TREE_SHOW_TILES\",void 0),E([o()],T.prototype,\"I3S_SHOW_MODIFICATIONS\",void 0),E([o()],T.prototype,\"LOD_INSTANCE_RENDERER_DISABLE_UPDATES\",void 0),E([o()],T.prototype,\"LOD_INSTANCE_RENDERER_COLORIZE_BY_LEVEL\",void 0),E([o()],T.prototype,\"EDGES_SHOW_HIDDEN_TRANSPARENT_EDGES\",void 0),E([o()],T.prototype,\"LINE_WIREFRAMES\",void 0),E([o()],T.prototype,\"TERRAIN_USE_LEGACY_SHADING\",void 0),T=E([S(\"esri.views.3d.support.DebugFlags\")],T);const t=new T;export{t as default};\n"], "mappings": ";;;;;;;;;;AAIkV,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,uCAAqC,OAAG,KAAK,4BAA0B,OAAG,KAAK,8BAA4B,OAAG,KAAK,yBAAuB,OAAG,KAAK,qBAAmB,OAAG,KAAK,qBAAmB,OAAG,KAAK,mBAAiB,OAAG,KAAK,6BAA2B,OAAG,KAAK,sBAAoB,OAAG,KAAK,WAAS,OAAG,KAAK,8BAA4B,OAAG,KAAK,6BAA2B,OAAG,KAAK,6BAA2B,OAAG,KAAK,gCAA8B,OAAG,KAAK,+BAA6B,OAAG,KAAK,+BAA6B,OAAG,KAAK,sBAAoB,OAAG,KAAK,yBAAuB,OAAG,KAAK,wCAAsC,OAAG,KAAK,0CAAwC,OAAG,KAAK,sCAAoC,OAAG,KAAK,kBAAgB,OAAG,KAAK,6BAA2B;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,wCAAuC,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,+BAA8B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,8BAA6B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,+BAA8B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,8BAA6B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,8BAA6B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,iCAAgC,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,gCAA+B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,gCAA+B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,yCAAwC,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,2CAA0C,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,uCAAsC,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,8BAA6B,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE,IAAI;", "names": []}