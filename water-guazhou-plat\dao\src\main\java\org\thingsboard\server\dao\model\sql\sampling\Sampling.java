package org.thingsboard.server.dao.model.sql.sampling;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 水质采样记录
 */
@Data
@TableName("tb_water_sampling")
@ApiModel(value = "水质采样记录", description = "水质采样记录实体类")
public class Sampling {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建人ID")
    private String creator;

    @ApiModelProperty(value = "采样时间")
    private Date samplingTime;

    @ApiModelProperty(value = "采样地点")
    private String samplingLocation;

    @ApiModelProperty(value = "采样人员")
    private String samplingPerson;

    @ApiModelProperty(value = "采样记录文件")
    private String recordFile;

    @ApiModelProperty(value = "采样方法")
    private String samplingMethod;

    @ApiModelProperty(value = "样品编号")
    private String sampleNumber;

    @ApiModelProperty(value = "样品类型")
    private String sampleType;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "类型")
    private String type;
}
