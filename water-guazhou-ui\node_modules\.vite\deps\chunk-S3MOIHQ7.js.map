{"version": 3, "sources": ["../../@arcgis/core/views/draw/DrawingMode.js", "../../@arcgis/core/views/interactive/dragEventPipeline.js", "../../@arcgis/core/views/interactive/snapping/SnappingDragPipelineStep.js", "../../@arcgis/core/views/draw/DrawManipulator.js", "../../@arcgis/core/views/interactive/snapping/SnappingOperation.js", "../../@arcgis/core/views/draw/DrawOperation.js", "../../@arcgis/core/views/draw/drawSurfaces.js", "../../@arcgis/core/views/interactive/ManipulatorCollection.js", "../../@arcgis/core/views/interactive/InteractiveToolBase.js", "../../@arcgis/core/views/draw/support/surfaceCoordinateSystems.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst c=[\"freehand\",\"hybrid\",\"click\"],e=\"click\";export{e as defaultDrawingMode,c as drawingModes};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../geometry.js\";import\"../../core/has.js\";import{clone as t}from\"../../core/lang.js\";import{clamp as e}from\"../../core/mathUtils.js\";import{isSome as n,isNone as r,unwrap as a,toNullable as o}from\"../../core/maybe.js\";import{createScreenPoint as s}from\"../../core/screenUtils.js\";import{f as c}from\"../../chunks/vec3f64.js\";import{project as i}from\"../../geometry/projection.js\";import{hydratedSpatialReference as l,clonePoint as u}from\"../../layers/graphics/hydratedFeatures.js\";import{getZForElevationMode as m}from\"../../support/elevationInfoUtils.js\";import{ViewingMode as p}from\"../ViewingMode.js\";import{move as f}from\"../draw/support/drawUtils.js\";import d from\"../../geometry/Point.js\";function y(t,e){let a=null,o=null;return s=>{if(\"cancel\"===s.action)return void(n(o)&&(o.execute({action:\"cancel\"}),a=null,o=null));const c={action:s.action,screenStart:s.start,screenEnd:s.screenPoint};\"start\"===s.action&&r(a)&&(a=new U,o=new U,e(t,a,o,s.pointerType,c)),n(a)&&a.execute(c),\"end\"===s.action&&n(a)&&(a=null,o=null)}}function x(t,e){return t.events.on(\"drag\",y(t,e))}function g(t,e){const n=[t.x,t.y,t.z??0],r=e,a=[Math.cos(r),Math.sin(r)],o=Math.sqrt(a[0]*a[0]+a[1]*a[1]);if(0===o)return null;a[0]/=o,a[1]/=o;const s=t=>{const e=(t.x-n[0])*a[0]+(t.y-n[1])*a[1];t.x=n[0]+e*a[0],t.y=n[1]+e*a[1]};return t=>(s(t.mapStart),s(t.mapEnd),{...t,axis:a})}function E(t,e){let n=null;return a=>{if(\"start\"===a.action&&(n=S(t,a.mapStart.spatialReference,e)),r(n))return null;const o=a.mapEnd.x-a.mapStart.x,s=a.mapEnd.y-a.mapStart.y,c=a.mapEnd.z-a.mapStart.z;return n.move(o,s,c),{...a,translationX:o,translationY:s,translationZ:c}}}function h(t,e){return r(t)?null:t.spatialReference.equals(e)?t.clone():i(t,e)}function S(t,e,n){const a=t.geometry,o=l(e);if(r(a))return null;if(\"mesh\"===a.type)return z(t,a,o,n);const s=h(a,o),c=a.spatialReference;return r(s)?null:{move:(e,n,r)=>{const a=f(s.clone(),e,n,r);a.spatialReference.equals(c)?t.geometry=a:t.geometry=i(a,c)}}}function z(t,e,r,a){if(n(e.transform))return j(t,e,e.transform,r);if(!e.spatialReference.equals(r))return null;let o=0,s=0,c=0;return{move:(n,r,i)=>{const l=n-o,u=r-s,m=i-c;if(l||u||m){const f=new d(e.origin.x+l,e.origin.y+u,e.origin.z+m,e.origin.spatialReference);e.centerAt(f,{geographic:a===p.Global}),t.notifyGeometryChanged(),o=n,s=r,c=i}}}}function j(t,e,a,o){const s=h(a.getOriginPoint(e.spatialReference),o),l=e.spatialReference;return r(s)?null:{move:(e,r,o)=>{const u=f(s.clone(),e,r,o);if(u.spatialReference.equals(l))a.origin=c(u.x,u.y,u.z);else{const t=i(u,l);n(t)&&(a.origin=c(t.x,t.y,t.z))}t.notifyMeshTransformChanged(),t.notifyGeometryChanged()}}}function v(t,e=null,a){let o=null;const s=n(e)&&!t.spatialReference?.equals(e)?t=>n(t)?i(t,e):t:t=>t,c={exclude:[],...a};return e=>{if(\"start\"===e.action&&(o=s(t.toMap(e.screenStart,c))),r(o))return null;const a=s(t.toMap(e.screenEnd,c));return n(a)?{...e,mapStart:o,mapEnd:a}:null}}function R(t,e){const r=t.map((t=>a(E(t,e)))).filter((t=>n(t)));return t=>{const e=t.mapEnd.x-t.mapStart.x,n=t.mapEnd.y-t.mapStart.y,a=t.mapEnd.z-t.mapStart.z;return r.forEach((e=>e(t))),{...t,translationX:e,translationY:n,translationZ:a}}}function w(e,n){const r=new Map;for(const a of n)r.set(a,t(e[a]));return t=>(r.forEach(((t,n)=>{e[n]=t})),t)}function M(t){return n(t.geometry)&&\"mesh\"===t.geometry.type?P(t,t.geometry):w(t,[\"geometry\"])}function P(t,e){const r=n(e.transform)?e.transform.clone():null,a=e.vertexAttributes.clonePositional();return n=>(e.transform=r,e.vertexAttributes=a,t.notifyGeometryChanged(),n)}function q(t){const e=t.map((t=>a(M(t)))).filter((t=>n(t)));return t=>(e.forEach((e=>e(t))),t)}function D(){let t=0,e=0,n=0;return r=>{\"start\"===r.action&&(t=r.mapStart.x,e=r.mapStart.y,n=r.mapStart.z);const a=r.mapEnd.x-t,o=r.mapEnd.y-e,s=r.mapEnd.z-n;return t=r.mapEnd.x,e=r.mapEnd.y,n=r.mapEnd.z,{...r,mapDeltaX:a,mapDeltaY:o,mapDeltaZ:s,mapDeltaSpatialReference:r.mapStart.spatialReference}}}function b(){let t=0,e=0;return n=>{\"start\"===n.action&&(t=n.screenStart.x,e=n.screenStart.y);const r=n.screenEnd.x-t,a=n.screenEnd.y-e;return t=n.screenEnd.x,e=n.screenEnd.y,{...n,screenDeltaX:r,screenDeltaY:a}}}function C(t,n){let r=null,a=0,o=0;return c=>{if(\"start\"===c.action&&(r=t.toScreen?.(n),null!=r&&(r.x<0||r.x>t.width||r.y<0||r.y>t.height?r=null:(a=c.screenStart.x-r.x,o=c.screenStart.y-r.y))),null==r)return null;const i=e(c.screenEnd.x-a,0,t.width),l=e(c.screenEnd.y-o,0,t.height),u=s(i,l);return c.screenStart=r,c.screenEnd=u,c}}const G=()=>{};class U{constructor(){this.execute=G}next(t,e=new U){return n(t)&&(this.execute=r=>{const a=t(r);n(a)&&e.execute(a)}),e}}function X(t,e,r=[]){if(\"2d\"===t.type)return t=>t;let a=null;return o=>{\"start\"===o.action&&(a=t.toMap(o.screenStart,{exclude:r}),n(a)&&(a.z=m(a,t,e)));const s=t.toMap(o.screenEnd,{exclude:r});n(s)&&(s.z=m(s,t,e));const c=n(a)&&n(s)?{sceneStart:a,sceneEnd:s}:null;return{...o,scenePoints:c}}}function Y(t,e,n){const r=o(e.elevationProvider.getElevation(t.x,t.y,t.z??0,t.spatialReference,\"scene\"))??0,a=u(t);return a.z=r,a.hasZ=!0,a.z=m(a,e,n),a}function Z(t,e){if(\"2d\"===t.type)return t=>t;let r=null;return a=>{\"start\"===a.action&&(r=Y(a.mapStart,t,e));const o=Y(a.mapEnd,t,e),s=n(r)&&n(o)?{sceneStart:r,sceneEnd:o}:null;return{...a,scenePoints:s}}}export{U as EventPipeline,D as addMapDelta,b as addScreenDelta,g as constrainToMapAxis,y as createDragEventPipelineCallback,x as createManipulatorDragEventPipeline,C as dragAtLocation,E as dragGraphic,R as dragGraphicMany,M as resetGraphic,q as resetGraphicMany,w as resetProperties,X as sceneSnappingAtLocation,Z as sceneSnappingAtProjectedLocation,v as screenToMap};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e,abortMaybe as n,isSome as t,removeMaybe as o,unwrap as i}from\"../../../core/maybe.js\";import{ignoreAbortErrors as r,debounce as a}from\"../../../core/promiseUtils.js\";import{watch as s}from\"../../../core/reactiveUtils.js\";import{pointEquals as c}from\"../../../layers/graphics/dehydratedFeatureComparison.js\";import{clonePoint as l}from\"../../../layers/graphics/hydratedFeatures.js\";import{EventPipeline as p}from\"../dragEventPipeline.js\";import{SnappingContext as u}from\"./SnappingContext.js\";import{TaskPriority as f,ImmediateTask as d}from\"../../support/Scheduler.js\";function m({predicate:a=(()=>!0),snappingManager:c,snappingContext:l,updatingHandles:u,useZ:f=!0}){const d=new p;if(e(c))return{snappingStep:[j,d],cancelSnapping:j};let m,z=null,T=null,k=null;const w=()=>{z=n(z),c.doneSnapping(),t(T)&&T.frameTask.remove(),T=null,m=o(m),k=null},C=g(c,f,d);let E=null,I=null,U=null;return{snappingStep:[n=>{if(!a(n))return n;const{action:o}=n;if(\"start\"===o){const{info:e}=n,o=x(c.view);if(T=P(l,n,o),T.context.selfSnappingZ=null,!f&&t(e)){const n=Z(l.coordinateHelper,e.handle.component);t(n)&&(T.context.selfSnappingZ={value:n,elevationInfo:l.elevationInfo})}}if(t(T)){const{context:t,originalScenePos:a,originalPos:l}=T,{mapEnd:p,mapStart:d,scenePoints:g}=n,x=h(l,S(p,d)),P=S(d,l),Z={...n,action:\"update\"},j=T.context,w=v(a,g),b=c.update({point:x,scenePoint:w,context:t});if(U=b,y(p,b,P,f),E=x,I=w,\"end\"!==o){const{frameTask:n}=T;e(z)&&(z=new AbortController),k=e=>{u.addPromise(r(C({frameTask:n,event:Z,context:j,point:x,scenePoint:w,delta:P,getLastState:()=>({point:E,scenePoint:I,updatePoint:e.forceUpdate?null:U})},i(z).signal)))},k({forceUpdate:!1}),e(m)&&(m=s((()=>c.options.effectiveEnabled),(()=>k?.({forceUpdate:!0}))))}}return\"end\"===o&&w(),n},d],cancelSnapping:e=>(w(),e)}}function g(n,o,i){return a((async({frameTask:r,point:a,scenePoint:s,context:l,event:p,delta:u,getLastState:f},d)=>{const m=await r.schedule((()=>n.snap({point:a,scenePoint:s,context:l,signal:d})),d);if(m.valid){let s=await r.schedule((()=>m.apply()),d);const g=f();t(g.point)&&a!==g.point&&(s=n.update({point:g.point,scenePoint:g.scenePoint,context:l})),!e(g.updatePoint)&&c(s,g.updatePoint)||(y(p.mapEnd,s,u,o),i.execute(p))}}))}function x(e){return\"3d\"===e.type?e.resourceController.scheduler.registerTask(f.SNAPPING):d}function P(e,n,o){return{context:new u({editGeometryOperations:e.editGeometryOperations,elevationInfo:e.elevationInfo,pointer:e.pointer,vertexHandle:t(n.info)?n.info.handle:null,excludeFeature:e.excludeFeature,visualizer:e.visualizer}),originalPos:t(n.snapOrigin)?e.coordinateHelper.vectorToDehydratedPoint(n.snapOrigin):n.mapStart,originalScenePos:t(n.scenePoints)?n.scenePoints.sceneStart:null,frameTask:o}}function h(e,[n,t,o]){const i=l(e);return i.x+=n,i.y+=t,i.hasZ&&(i.z+=o),i}function v(n,t){return e(n)||e(t)?null:h(n,S(t.sceneEnd,t.sceneStart))}function S(e,n){const t=e.hasZ&&n.hasZ?e.z-n.z:0;return[e.x-n.x,e.y-n.y,t]}function y(e,n,[t,o,i],r){e.x=n.x+t,e.y=n.y+o,r&&e.hasZ&&n.hasZ&&(e.z=n.z+i)}function Z(n,o){if(!n.hasZ())return null;const i=o.vertices;let r=null;for(const a of i){const o=n.getZ(a.pos);if(t(r)&&t(o)&&Math.abs(o-r)>1e-6)return null;e(r)&&(r=o)}return r}function j(e){return e}export{m as createSnapDragEventPipelineStep};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Evented.js\";class t{constructor({grabbableForEvent:t}){this.events=new e,this.interactive=!0,this.selectable=!1,this.cursor=null,this.grabbable=!0,this.grabbableForEvent=t}intersectionDistance(e,t){return 0}attach(){}detach(){}onElevationChange(){}onViewChange(){}}export{t as DrawManipulator};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import s from\"../../../core/Accessor.js\";import{isSome as o,abortMaybe as r,removeMaybe as e,isNone as i}from\"../../../core/maybe.js\";import{debounce as n}from\"../../../core/promiseUtils.js\";import{property as a}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as p}from\"../../../core/accessorSupport/decorators/subclass.js\";import{TaskPriority as l,ImmediateTask as c}from\"../../support/Scheduler.js\";let h=class extends s{constructor(t){super(t),this.constrainResult=t=>t,this._snapPoints=null,this._frameTask=null,this._abortController=null,this._stagedPoint=null,this._snap=n((async(t,s,r,e)=>{const n=this._frameTask;if(i(n))return;const a=await n.schedule((()=>s.snap({...t,context:r,signal:e})),e);a.valid&&await n.schedule((()=>{this.stagedPoint=a.apply(),t!==this._snapPoints&&o(this._snapPoints)&&(this.stagedPoint=s.update({...this._snapPoints,context:r}))}),e)}))}get stagedPoint(){return this._stagedPoint}set stagedPoint(t){this._stagedPoint=this.constrainResult(t)}initialize(){const t=\"3d\"===this.view.type?this.view?.resourceController?.scheduler:null;this._frameTask=o(t)?t.registerTask(l.SNAPPING):c}destroy(){this._abortController=r(this._abortController),this._frameTask=e(this._frameTask)}update(t,s,o){this._snapPoints=t;const{point:r,scenePoint:e}=t,i=s.update({point:r,scenePoint:e,context:o});return this.stagedPoint=i,i}async snap(t,s,o){const{point:r,scenePoint:e}=t;return this.stagedPoint=s.update({point:r,scenePoint:e,context:o}),this._snapPoints=t,i(this._abortController)&&(this._abortController=new AbortController),this._snap(t,s,o,this._abortController.signal)}async resnap(t,s){i(this._snapPoints)||await this.snap(this._snapPoints,t,s)}abort(){this._abortController=r(this._abortController),this._snapPoints=null}};t([a({constructOnly:!0})],h.prototype,\"view\",void 0),t([a()],h.prototype,\"stagedPoint\",null),t([a()],h.prototype,\"constrainResult\",void 0),t([a()],h.prototype,\"_stagedPoint\",void 0),h=t([p(\"esri.views.interactive.snapping.SnappingOperation\")],h);export{h as SnappingOperation};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../core/Evented.js\";import{HandleOwner as i}from\"../../core/HandleOwner.js\";import{clone as n}from\"../../core/lang.js\";import{isNone as r,isSome as o,destroyMaybe as s,unwrap as a,applySome as p,equalsMaybe as c,unwrapOr as h}from\"../../core/maybe.js\";import{ignoreAbortErrors as d}from\"../../core/promiseUtils.js\";import{watch as l,syncAndInitial as m}from\"../../core/reactiveUtils.js\";import{createScreenPoint as g}from\"../../core/screenUtils.js\";import{property as u}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import{subclass as v}from\"../../core/accessorSupport/decorators/subclass.js\";import{pointEquals as y}from\"../../layers/graphics/dehydratedFeatureComparison.js\";import{getEffectiveElevationInfo as _}from\"../../support/elevationInfoUtils.js\";import{ViewingMode as x}from\"../ViewingMode.js\";import{defaultDrawingMode as f}from\"./DrawingMode.js\";import{DrawManipulator as S}from\"./DrawManipulator.js\";import{createCoordinateHelper as w}from\"../interactive/coordinateHelper.js\";import{createManipulatorDragEventPipeline as V,sceneSnappingAtLocation as O}from\"../interactive/dragEventPipeline.js\";import{EditGeometry as T,Component as b}from\"../interactive/editGeometry/EditGeometry.js\";import{EditGeometryOperations as M}from\"../interactive/editGeometry/EditGeometryOperations.js\";import D from\"../interactive/sketch/SketchLabelOptions.js\";import C from\"../interactive/sketch/SketchTooltipOptions.js\";import{SnappingContext as I}from\"../interactive/snapping/SnappingContext.js\";import{createSnapDragEventPipelineStep as E}from\"../interactive/snapping/SnappingDragPipelineStep.js\";import{SnappingOperation as P}from\"../interactive/snapping/SnappingOperation.js\";import{setupSnappingToggleHandles as j}from\"../interactive/snapping/snappingUtils.js\";let k=class extends(t.EventedMixin(i)){constructor(e){super(e),this._createOperationCompleted=!1,this._pointerDownStates=new Set,this.isDraped=!0,this.labelOptions=new D,this.tooltipOptions=new C,this.snapToSceneEnabled=null,this.lastVertex=null,r(e.elevationInfo)&&(this.elevationInfo=_(!!e.hasZ))}initialize(){const{geometryType:e,view:t}=this,i=t.spatialReference,n=\"viewingMode\"in t.state?t.state.viewingMode:x.Local,s=\"segment\"===e||\"multipoint\"===e?\"polyline\":e;this.coordinateHelper=w(this.hasZ,this.hasM,i),this._editGeometryOperations=new M(new T(s,this.coordinateHelper)),this._snappingOperation=new P({view:t,constrainResult:e=>r(e)?e:this._getEffectiveDrawSurface()?.constrainZ(e)}),this.handles.add(l((()=>this.stagedVertex),(e=>{r(e)||this.emit(\"cursor-update\",{updated:null,vertices:[{componentIndex:0,vertexIndex:this._activeComponent.vertices.length,coordinates:this.coordinateHelper.pointToArray(e)}],operation:\"apply\",type:\"vertex-update\"})}),{sync:!0,equals:(e,t)=>c(e,t,y)})),this._activeComponent=new b(i,n),this._editGeometryOperations.data.components.push(this._activeComponent);const a=this.segmentLabels;o(a)&&(a.context={view:t,editGeometryOperations:this._editGeometryOperations,elevationInfo:this.elevationInfo,labelOptions:this.labelOptions},this.handles.add([l((()=>this.labelOptions.enabled),(e=>{a.visible=e}),m),this.on(\"cursor-update\",(()=>{const e=this.stagedVertex;a.stagedVertex=o(e)?this.coordinateHelper.pointToVector(e):null}))])),this.handles.add(this._editGeometryOperations.on([\"vertex-add\",\"vertex-update\",\"vertex-remove\"],(e=>{const t=e.vertices.map((e=>({componentIndex:0,vertexIndex:e.index,coordinates:this.coordinateHelper.vectorToArray(e.pos)}))),i=t.map((e=>e.coordinates));switch(e.type){case\"vertex-add\":this.emit(e.type,{...e,added:i,vertices:t});break;case\"vertex-update\":this.emit(e.type,{...e,updated:i,vertices:t});break;case\"vertex-remove\":this.emit(e.type,{...e,removed:i,vertices:t})}const n=this._activeComponent.getLastVertex(),s=o(n)?this.coordinateHelper.vectorToDehydratedPoint(n.pos):null;(r(s)||r(this.lastVertex)||!y(this.lastVertex,s))&&(this.lastVertex=s)}))),this._manipulator=new S({grabbableForEvent:e=>\"click\"!==this.drawingMode||\"touch\"===e.pointerType&&this._snappingEnabled&&1===this._pointerDownStates.size}),this.manipulators.add(this._manipulator),this._manipulator.grabbable=\"point\"!==e,this.handles.add([this._createManipulatorDragPipeline(this._manipulator),this._manipulator.events.on(\"immediate-click\",(e=>this._onImmediateClick(e))),this._manipulator.events.on(\"immediate-double-click\",(e=>this._onImmediateDoubleClick(e)))]),j(this,(()=>{const e=h(this.view.inputManager.latestPointerType,\"mouse\"),t=this._getSnappingContext(e);o(this.snappingManager)&&this.updatingHandles.addPromise(d(this._snappingOperation.resnap(this.snappingManager,t)))}))}destroy(){s(this.segmentLabels),s(this._snappingOperation),this._editGeometryOperations=s(this._editGeometryOperations)}get _snappingEnabled(){return o(this.snappingManager)&&this.snappingManager.options.effectiveEnabled}get _requiresScenePoint(){const e=this._getEffectiveDrawSurface();return\"3d\"===this.view.type&&this.drawSurface!==e}get canRedo(){return this._editGeometryOperations.canRedo}get canUndo(){return this._editGeometryOperations.canUndo}get committedVertices(){return this._activeComponent.vertices.map((e=>this.coordinateHelper.vectorToArray(e.pos)))}set drawingMode(e){this._set(\"drawingMode\",e??f)}get interactive(){return this._manipulator.interactive}set interactive(e){this._manipulator.interactive=e}get isCompleted(){return this._createOperationCompleted}get numCommittedVertices(){return this._activeComponent.vertices.length}get numVertices(){return o(this.stagedVertex)?this._activeComponent.vertices.length+1:this._activeComponent.vertices.length}get snappingOptions(){return o(this.snappingManager)?this.snappingManager.options:null}get stagedVertex(){return this._snappingOperation.stagedPoint}set stagedVertex(e){this._snappingOperation.stagedPoint=n(e)}get updating(){return this.updatingHandles.updating}get vertices(){const e=this.committedVertices;return o(this.stagedVertex)&&e.push(this.coordinateHelper.pointToArray(this.stagedVertex)),e}cancel(){this.complete({aborted:!0})}commitStagedVertex(){if(this._snappingOperation.abort(),o(this.stagedVertex)){const{stagedVertex:e}=this;this.stagedVertex=null,this._editGeometryOperations.appendVertex(this.coordinateHelper.pointToVector(e))}}complete(e){const t=e&&e.aborted||!1;this._snappingOperation.abort(),o(this.snappingManager)&&this.snappingManager.doneSnapping(),\"segment\"===this.geometryType||\"point\"===this.geometryType?this.commitStagedVertex():this.stagedVertex=null;const i=\"multipoint\"===this.geometryType&&0===this.numVertices||\"polyline\"===this.geometryType&&this.numVertices<2||\"polygon\"===this.geometryType&&this.numVertices<3;this._createOperationCompleted=!i,(this.isCompleted||t)&&this.emit(\"complete\",{vertices:this.vertices.map(((e,t)=>({componentIndex:0,vertexIndex:t,coordinates:e}))),aborted:t,type:\"complete\"})}onInputEvent(e){switch(e.type){case\"pointer-down\":this._pointerDownStates.add(e.pointerId);break;case\"pointer-up\":this._pointerDownStates.delete(e.pointerId)}switch(e.type){case\"pointer-move\":return this._onPointerMove(e);case\"hold\":return this._onHold(e)}}redo(){this._editGeometryOperations.redo()}undo(){o(this.snappingManager)&&this.snappingManager.doneSnapping(),this._editGeometryOperations.undo()}_closeOnClickVertexIndex(e){const t=this._activeComponent;if(\"polygon\"===this.geometryType&&t.vertices.length>2){if(this._vertexWithinPointerDistance(t.vertices[0].pos,e))return 0;if(this._vertexWithinPointerDistance(t.vertices[t.vertices.length-1].pos,e))return t.vertices.length-1}return null}_createManipulatorDragPipeline(e){switch(a(this.drawingMode)){case\"click\":return this._createManipulatorDragPipelineClick(e);case\"freehand\":return this._createManipulatorDragPipelineFreehand(e);case\"hybrid\":return this._createManipulatorDragPipelineHybrid(e)}}_createManipulatorDragPipelineClick(e){return V(e,((e,t,i,n)=>{const r=\"touch\"===n&&this._snappingEnabled;if(this.isCompleted||!r)return;const{snappingStep:s,cancelSnapping:a}=E({predicate:()=>r,snappingManager:this.snappingManager,snappingContext:new I({editGeometryOperations:this._editGeometryOperations,elevationInfo:this.elevationInfo,pointer:n,visualizer:this.snappingVisualizer}),updatingHandles:this.updatingHandles,useZ:!this._requiresScenePoint});i=i.next((e=>(r&&o(this.snappingManager)&&this.snappingManager.doneSnapping(),e))).next(a),t.next(this._screenToMapDragEventStep()).next((e=>(\"start\"===e.action&&(this.stagedVertex=e.mapStart,(\"segment\"===this.geometryType||r&&0===this.numVertices)&&this.commitStagedVertex()),e))).next(O(this.view,this.elevationInfo)).next(...s).next((e=>(r&&(this.stagedVertex=e.mapEnd,\"end\"===e.action&&this.commitStagedVertex()),e))).next((e=>(\"end\"===e.action&&(\"segment\"!==this.geometryType&&\"point\"!==this.geometryType||this.complete()),e)))}))}_createManipulatorDragPipelineFreehand(e){return V(e,((e,t)=>{this.isCompleted||t.next(this._screenToMapDragEventStep()).next((e=>(\"start\"===e.action&&(r(this.stagedVertex)&&(this.stagedVertex=e.mapStart),\"segment\"===this.geometryType&&this.commitStagedVertex()),e))).next((e=>{switch(e.action){case\"start\":case\"update\":this.stagedVertex=e.mapEnd,\"polygon\"!==this.geometryType&&\"polyline\"!==this.geometryType||this.commitStagedVertex();break;case\"end\":this.complete()}return e}))}))}_createManipulatorDragPipelineHybrid(e){return V(e,((e,t)=>{this.isCompleted||t.next(this._screenToMapDragEventStep()).next((e=>(\"start\"===e.action&&(r(this.stagedVertex)&&(this.stagedVertex=e.mapStart),this.commitStagedVertex()),e))).next((e=>{switch(e.action){case\"start\":case\"update\":this.stagedVertex=e.mapEnd,\"polygon\"!==this.geometryType&&\"polyline\"!==this.geometryType||this.commitStagedVertex();break;case\"end\":\"segment\"!==this.geometryType&&\"point\"!==this.geometryType||this.complete()}return e}))}))}get _drawAtFixedElevation(){return(\"segment\"===this.geometryType||\"polygon\"===this.geometryType)&&this.numCommittedVertices>0}_getEffectiveDrawSurface(){if(r(this.elevationDrawSurface))return this.drawSurface;if(!this.coordinateHelper.hasZ())return this.elevationDrawSurface.defaultZ=null,this.elevationDrawSurface;let e=this.defaultZ,t=!1;return o(this.elevationInfo)&&\"absolute-height\"===this.elevationInfo.mode&&(t=!0),o(this.snapToSceneEnabled)&&(t=this.snapToSceneEnabled),o(this.elevationInfo)&&\"on-the-ground\"===this.elevationInfo.mode&&(t=!1),this._drawAtFixedElevation&&(e=this.coordinateHelper.getZ(this._activeComponent.vertices[0].pos),t=!1),t?this.drawSurface:(this.elevationDrawSurface.defaultZ=e,this.elevationDrawSurface)}_mapToScreen(e){return this._getEffectiveDrawSurface()?.mapToScreen(e)}_onHold(e){this._snappingOperation.abort(),\"click\"===this.drawingMode&&\"touch\"===e.pointerType&&this._snappingEnabled&&(this.stagedVertex=e.mapPoint),e.stopPropagation()}_onImmediateClick(e){if(\"mouse\"===e.pointerType&&2===e.button||this._manipulator.dragging)return;const t=this._activeComponent,i=this._closeOnClickVertexIndex(e.screenPoint);if(o(i))return e.stopPropagation(),void this.complete();const n=this._screenToMap(e.screenPoint);if(o(n))switch(this.drawingMode){case\"freehand\":\"point\"===this.geometryType&&(o(this.stagedVertex)?this.commitStagedVertex():this._editGeometryOperations.appendVertex(this.coordinateHelper.pointToVector(n)),this.complete());break;case\"click\":case\"hybrid\":this._snappingOperation.abort(),o(this.stagedVertex)?this.commitStagedVertex():this._editGeometryOperations.appendVertex(this.coordinateHelper.pointToVector(n)),(\"point\"===this.geometryType||\"segment\"===this.geometryType&&2===t.vertices.length||\"segment\"===this.geometryType&&\"hybrid\"===this.drawingMode&&1===t.vertices.length)&&this.complete()}e.stopPropagation()}_onImmediateDoubleClick(e){this._manipulator.dragging||\"point\"===this.geometryType||(this.complete(),e.stopPropagation())}_onPointerMove(e){const t=g(e.x,e.y),i=this._snappingOperation;if(this._manipulator.dragging||this._pointerDownStates.has(e.pointerId)||this._manipulator.grabbing||!this._manipulator.interactive)return void i.abort();e.stopPropagation();const n=this._closeOnClickVertexIndex(t);if(o(n))return this._closeOnVertex(n),void i.abort();const s=this._screenToMap(t),a=this._requiresScenePoint?this.drawSurface?.screenToMap(t):null;if(this._manipulator.cursor=o(s)?\"crosshair\":null,r(s))return void i.abort();const p=this.snappingManager;if(r(p))return this.stagedVertex=s,void i.abort();const c=this._getSnappingContext(e.pointerType);this.updatingHandles.addPromise(d(i.snap({point:s,scenePoint:a},p,c)))}_closeOnVertex(e){this.stagedVertex=null;const t={componentIndex:0,vertexIndex:e,coordinates:this.coordinateHelper.vectorToArray(this._activeComponent.vertices[e].pos)};this.emit(\"cursor-update\",{updated:null,vertices:[t],operation:\"apply\",type:\"vertex-update\"})}_screenToMap(e){return this._getEffectiveDrawSurface()?.screenToMap(e)}_screenToMapDragEventStep(){let e=null;return t=>{if(\"start\"===t.action&&(e=this._screenToMap(t.screenStart)),r(e))return null;const i=this._screenToMap(t.screenEnd);return o(i)?{...t,mapStart:e,mapEnd:i}:null}}_vertexWithinPointerDistance(e,t){const i=25,n=this._mapToScreen(this.coordinateHelper.vectorToDehydratedPoint(e));return!!o(n)&&H(n,t,i)}_getSnappingContext(e){const t=this._drawAtFixedElevation?p(this.elevationDrawSurface,(({defaultZ:e})=>e)):null;return new I({editGeometryOperations:this._editGeometryOperations,elevationInfo:this.elevationInfo,pointer:e,visualizer:this.snappingVisualizer,selfSnappingZ:o(t)?{value:t,elevationInfo:this.elevationInfo}:null})}};function H(e,t,i){const n=e.x-t.x,r=e.y-t.y;return n*n+r*r<=i}e([u()],k.prototype,\"_snappingEnabled\",null),e([u()],k.prototype,\"defaultZ\",void 0),e([u()],k.prototype,\"isDraped\",void 0),e([u({value:f})],k.prototype,\"drawingMode\",null),e([u({constructOnly:!0})],k.prototype,\"elevationDrawSurface\",void 0),e([u({constructOnly:!0})],k.prototype,\"elevationInfo\",void 0),e([u({constructOnly:!0,type:D})],k.prototype,\"labelOptions\",void 0),e([u({constructOnly:!0,type:C})],k.prototype,\"tooltipOptions\",void 0),e([u({constructOnly:!0})],k.prototype,\"geometryType\",void 0),e([u({constructOnly:!0})],k.prototype,\"hasM\",void 0),e([u({constructOnly:!0})],k.prototype,\"hasZ\",void 0),e([u({constructOnly:!0})],k.prototype,\"manipulators\",void 0),e([u({constructOnly:!0})],k.prototype,\"drawSurface\",void 0),e([u({constructOnly:!0})],k.prototype,\"segmentLabels\",void 0),e([u({constructOnly:!0})],k.prototype,\"snappingManager\",void 0),e([u({constructOnly:!0})],k.prototype,\"snappingVisualizer\",void 0),e([u()],k.prototype,\"snapToSceneEnabled\",void 0),e([u()],k.prototype,\"_snappingOperation\",void 0),e([u()],k.prototype,\"stagedVertex\",null),e([u()],k.prototype,\"lastVertex\",void 0),e([u()],k.prototype,\"updating\",null),e([u({constructOnly:!0})],k.prototype,\"view\",void 0),k=e([v(\"esri.views.draw.DrawOperation\")],k);export{k as DrawOperation};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../core/maybe.js\";import{createScreenPointArray as t}from\"../../core/screenUtils.js\";import{makeDehydratedPoint as s}from\"../../layers/graphics/dehydratedFeatures.js\";import{clonePoint as i}from\"../../layers/graphics/hydratedFeatures.js\";import{getConvertedElevation as r,getZForElevationMode as n}from\"../../support/elevationInfoUtils.js\";class o{constructor(e,t,s,i=null){this._elevationInfo=e,this.defaultZ=t,this._view=s,this._excludeGraphics=i}screenToMap(s){if(e(this.defaultZ))return this._view.sceneIntersectionHelper.intersectElevationFromScreen(t(s.x,s.y),this._elevationInfo,this.defaultZ,this._excludeGraphics);const i=this._view.sceneIntersectionHelper.intersectElevationFromScreen(t(s.x,s.y),this._elevationInfo,0,this._excludeGraphics);return e(i)&&(i.z=void 0),i}mapToScreen(e){const t=s(e.x,e.y,r(this._view,e,this._elevationInfo),e.spatialReference);return this._view.toScreen(t)}constrainZ(t){const{defaultZ:s}=this;return e(s)&&t.z!==s&&((t=i(t)).z=s),t}}class c{constructor(e,t,s=[]){this.view=e,this.elevationInfo=t,this.exclude=s}screenToMap(t){const s=this.view.toMap(t,{exclude:this.exclude});return e(s)&&(s.z=n(s,this.view,this.elevationInfo)),s}mapToScreen(t){let i=t;return e(this.elevationInfo)&&(i=s(t.x,t.y,r(this.view,t,this.elevationInfo),t.spatialReference)),this.view.toScreen(i)}constrainZ(e){return e}}class a{constructor(e,t=!1,s=0){this.view=e,this.hasZ=t,this.defaultZ=s,this.mapToScreen=t=>e.toScreen(t),this.screenToMap=t?t=>{const i=e.toMap(t);return i.z=s,i}:t=>e.toMap(t)}constrainZ(e){const{defaultZ:t}=this;return this.hasZ&&e.z!==t&&((e=i(e)).z=t),e}}export{o as ElevationDrawSurface,a as MapDrawSurface,c as SceneDrawSurface};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../core/Collection.js\";import{isSome as a}from\"../../core/maybe.js\";var i;!function(t){t[t.WhenToolEditable=0]=\"WhenToolEditable\",t[t.WhenToolNotEditable=1]=\"WhenToolNotEditable\",t[t.Always=2]=\"Always\"}(i||(i={}));class e{constructor(){this._isToolEditable=!0,this._manipulators=new t,this._resourceContexts={manipulator3D:{}},this._attached=!1}set isToolEditable(t){this._isToolEditable=t}get length(){return this._manipulators.length}add(t,a=i.WhenToolEditable){this.addMany([t],a)}addMany(t,a=i.WhenToolEditable){for(const i of t){const t={manipulator:i,visibilityPredicate:a,attached:!1};this._manipulators.add(t),this._attached&&this._updateManipulatorAttachment(t)}}remove(t){for(let a=0;a<this._manipulators.length;a++)if(this._manipulators.getItemAt(a).manipulator===t){const t=this._manipulators.splice(a,1)[0];this._detachManipulator(t);break}}removeAll(){this._manipulators.forEach((t=>{this._detachManipulator(t)})),this._manipulators.removeAll()}attach(){this._manipulators.forEach((t=>{this._updateManipulatorAttachment(t)})),this._attached=!0}detach(){this._manipulators.forEach((t=>{this._detachManipulator(t)})),this._attached=!1}destroy(){this.detach(),this._manipulators.forEach((({manipulator:t})=>{t.destroy&&t.destroy()})),this._manipulators.destroy(),this._resourceContexts=null}on(t,a){return this._manipulators.on(t,(t=>{a(t)}))}forEach(t){for(const a of this._manipulators.items)t(a)}some(t){return this._manipulators.items.some(t)}toArray(){const t=[];return this.forEach((a=>t.push(a.manipulator))),t}intersect(t,i){let e=null,o=Number.MAX_VALUE;return this._manipulators.forEach((({manipulator:s,attached:r})=>{if(!r||!s.interactive)return;const n=s.intersectionDistance(t,i);a(n)&&n<o&&(o=n,e=s)})),e}_updateManipulatorAttachment(t){this._isManipulatorItemVisible(t)?this._attachManipulator(t):this._detachManipulator(t)}_attachManipulator(t){t.attached||(t.manipulator.attach&&t.manipulator.attach(this._resourceContexts),t.attached=!0)}_detachManipulator(t){if(!t.attached)return;const a=t.manipulator;a.grabbing=!1,a.dragging=!1,a.hovering=!1,a.selected=!1,a.detach&&a.detach(this._resourceContexts),t.attached=!1}_isManipulatorItemVisible(t){return t.visibilityPredicate===i.Always||(this._isToolEditable?t.visibilityPredicate===i.WhenToolEditable:t.visibilityPredicate===i.WhenToolNotEditable)}}export{e as ManipulatorCollection,i as ManipulatorVisibilityPredicate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import e from\"../../core/Accessor.js\";import i from\"../../core/Logger.js\";import{isNone as o}from\"../../core/maybe.js\";import{createResolver as a}from\"../../core/promiseUtils.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as r}from\"../../core/accessorSupport/decorators/subclass.js\";import{EditableFlag as n}from\"./interfaces.js\";import{ManipulatorCollection as l}from\"./ManipulatorCollection.js\";let p=class extends e{constructor(t){super(t),this.manipulators=new l,this.automaticManipulatorSelection=!0,this.hasGrabbedManipulators=!1,this.hasHoveredManipulators=!1,this.firstGrabbedManipulator=null,this.created=!1,this.removeIncompleteOnCancel=!0,this._editableFlags=new Map([[n.MANAGER,!0],[n.USER,!0]]),this._creationFinishedResolver=a()}get active(){return null!=this.view&&this.view.activeTool===this}set visible(t){this._get(\"visible\")!==t&&(this._set(\"visible\",t),this._syncVisible())}get editable(){return this.getEditableFlag(n.USER)}set editable(t){this.setEditableFlag(n.USER,t)}get updating(){return!1}get cursor(){return null}get hasFocusedManipulators(){return this.hasGrabbedManipulators||this.hasHoveredManipulators}destroy(){this.manipulators.destroy(),this._set(\"view\",null)}onAdd(){this._syncVisible()}activate(){o(this.view)?i.getLogger(this.declaredClass).error(\"Can't activate tool if view is not defined.\"):(this.view.focus(),this.onActivate())}deactivate(){this.onDeactivate()}handleInputEvent(t){this.onInputEvent(t)}handleInputEventAfter(t){this.onInputEventAfter(t)}setEditableFlag(t,e){this._editableFlags.set(t,e),this.manipulators.isToolEditable=this.internallyEditable,this._updateManipulatorAttachment(),t===n.USER&&this.notifyChange(\"editable\"),this.onEditableChange(),this.onManipulatorSelectionChanged()}getEditableFlag(t){return this._editableFlags.get(t)??!1}whenCreated(){return this._creationFinishedResolver.promise}onManipulatorSelectionChanged(){}onActivate(){}onDeactivate(){}onShow(){}onHide(){}onEditableChange(){}onInputEvent(t){}onInputEventAfter(t){}get internallyEditable(){return this.getEditableFlag(n.USER)&&this.getEditableFlag(n.MANAGER)}finishToolCreation(){this.created||this._creationFinishedResolver.resolve(this),this._set(\"created\",!0)}_syncVisible(){if(this.initialized)if(this.visible)this._show();else if(this._hide(),this.active)return void(this.view.activeTool=null)}_show(){this._updateManipulatorAttachment(),this.onShow()}_hide(){this._updateManipulatorAttachment(),this.onHide()}_updateManipulatorAttachment(){this.visible?this.manipulators.attach():this.manipulators.detach()}};t([s({constructOnly:!0})],p.prototype,\"view\",void 0),t([s({readOnly:!0})],p.prototype,\"active\",null),t([s({value:!0})],p.prototype,\"visible\",null),t([s({value:!0})],p.prototype,\"editable\",null),t([s({readOnly:!0})],p.prototype,\"manipulators\",void 0),t([s({readOnly:!0})],p.prototype,\"updating\",null),t([s()],p.prototype,\"cursor\",null),t([s({readOnly:!0})],p.prototype,\"automaticManipulatorSelection\",void 0),t([s()],p.prototype,\"hasFocusedManipulators\",null),t([s()],p.prototype,\"hasGrabbedManipulators\",void 0),t([s()],p.prototype,\"hasHoveredManipulators\",void 0),t([s()],p.prototype,\"firstGrabbedManipulator\",void 0),t([s({readOnly:!0})],p.prototype,\"created\",void 0),t([s({readOnly:!0})],p.prototype,\"removeIncompleteOnCancel\",void 0),p=t([r(\"esri.views.interactive.InteractiveToolBase\")],p);export{p as InteractiveToolBase};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../geometry.js\";import{isSome as t,filterNones as r}from\"../../../core/maybe.js\";import{c as e}from\"../../../chunks/mat2d.js\";import{c as s,a}from\"../../../chunks/mat2df64.js\";import{r as o,i}from\"../../../chunks/quat.js\";import{a as n}from\"../../../chunks/quatf64.js\";import{g as l,e as h,b as m,n as c,f,q as p}from\"../../../chunks/vec3.js\";import{c as d}from\"../../../chunks/vec3f64.js\";import{Axis as u}from\"../../../geometry/support/Axis.js\";import w from\"../../../geometry/Point.js\";function g(r,e,s=null){return t(s)?[r,e,s]:[r,e]}function R(r,e,s=null){return t(s)?{x:r,y:e,z:s}:{x:r,y:e}}class W{constructor(t){this.spatialReference=t}mapToLocalMultiple(t){return r(t.map((t=>this.mapToLocal(t))))}get doUnnormalization(){return!1}}class v extends W{constructor(t,r,o=null){super(r),this._defaultZ=o,this.transform=s(),this.transformInv=s(),this.transform=a(t),e(this.transformInv,this.transform)}makeMapPoint(t,r){return g(t,r,this._defaultZ)}mapToLocal(t){return R(this.transform[0]*t[0]+this.transform[2]*t[1]+this.transform[4],this.transform[1]*t[0]+this.transform[3]*t[1]+this.transform[5])}localToMap(t){return g(this.transformInv[0]*t.x+this.transformInv[2]*t.y+this.transformInv[4],this.transformInv[1]*t.x+this.transformInv[3]*t.y+this.transformInv[5],this._defaultZ)}}class F extends W{constructor(t,r){super(t.spatialReference),this.view=t,this.defaultZ=null,this.pWS=d(),this.tangentFrameUpWS=d(),this.tangentFrameRightWS=d(),this.tangentFrameForwardWS=d(),this.localFrameRightWS=d(),this.localFrameUpWS=d(),this.worldToLocalTransform=n(),this.localToWorldTransform=n(),this.scale=1,this.scale=t.resolution,this.referenceMapPoint=r,this.defaultZ=r.hasZ?r.z:null;const e=t.state.camera.viewRight;this.view.renderCoordsHelper.toRenderCoords(this.referenceMapPoint,this.pWS),this.view.renderCoordsHelper.worldBasisAtPosition(this.pWS,u.X,this.tangentFrameRightWS),this.view.renderCoordsHelper.worldBasisAtPosition(this.pWS,u.Y,this.tangentFrameUpWS),this.view.renderCoordsHelper.worldBasisAtPosition(this.pWS,u.Z,this.tangentFrameForwardWS);const s=d();l(s,this.tangentFrameForwardWS,h(e,this.tangentFrameForwardWS)),m(this.localFrameRightWS,e,s),c(this.localFrameRightWS,this.localFrameRightWS),f(this.localFrameUpWS,this.tangentFrameForwardWS,this.localFrameRightWS),o(this.worldToLocalTransform,this.localFrameRightWS,this.tangentFrameRightWS),i(this.localToWorldTransform,this.worldToLocalTransform)}get doUnnormalization(){return\"global\"===this.view.viewingMode}makeMapPoint(t,r){return g(t,r,this.defaultZ)}mapToLocal(r){const e=d();this.view.renderCoordsHelper.toRenderCoords(new w({x:r[0],y:r[1],spatialReference:this.spatialReference}),e),p(e,e,this.worldToLocalTransform);const s=this.view.renderCoordsHelper.fromRenderCoords(e,this.view.spatialReference);return t(s)?R(s.x/this.scale,s.y/this.scale):null}localToMap(r){const e=d();this.view.renderCoordsHelper.toRenderCoords(new w({x:r.x*this.scale,y:r.y*this.scale,spatialReference:this.spatialReference}),e),p(e,e,this.localToWorldTransform);const s=this.view.renderCoordsHelper.fromRenderCoords(e,this.view.spatialReference);return t(s)?g(s.x,s.y,this.defaultZ):null}}function S(t,r){if(\"2d\"===t.type)return new v(t.state.transform,t.spatialReference,r.length>2?r[2]:null);if(\"3d\"===t.type){const e=r.length>2?new w({x:r[0],y:r[1],z:r[2],spatialReference:t.spatialReference}):new w({x:r[0],y:r[1],spatialReference:t.spatialReference});return new F(t,e)}return null}export{v as AffineCoordinateSystem,F as SceneViewCoordinateSystem,W as SurfaceCoordinateSystem,S as createViewAlignedCoordinateSystem,g as makeMapPoint,R as makeSurfacePoint};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAMA,KAAE,CAAC,YAAW,UAAS,OAAO;AAApC,IAAsCC,KAAE;;;ACAwpB,SAASC,GAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,MAAKC,KAAE;AAAK,SAAO,CAAAC,OAAG;AAAC,QAAG,aAAWA,GAAE,OAAO,QAAO,MAAK,EAAED,EAAC,MAAIA,GAAE,QAAQ,EAAC,QAAO,SAAQ,CAAC,GAAED,KAAE,MAAKC,KAAE;AAAO,UAAME,KAAE,EAAC,QAAOD,GAAE,QAAO,aAAYA,GAAE,OAAM,WAAUA,GAAE,YAAW;AAAE,gBAAUA,GAAE,UAAQ,EAAEF,EAAC,MAAIA,KAAE,IAAI,KAAEC,KAAE,IAAI,KAAEF,GAAED,IAAEE,IAAEC,IAAEC,GAAE,aAAYC,EAAC,IAAG,EAAEH,EAAC,KAAGA,GAAE,QAAQG,EAAC,GAAE,UAAQD,GAAE,UAAQ,EAAEF,EAAC,MAAIA,KAAE,MAAKC,KAAE;AAAA,EAAK;AAAC;AAAC,SAASG,GAAEN,IAAEC,IAAE;AAAC,SAAOD,GAAE,OAAO,GAAG,QAAOD,GAAEC,IAAEC,EAAC,CAAC;AAAC;AAAC,SAASM,GAAEP,IAAEC,IAAE;AAAC,QAAMO,KAAE,CAACR,GAAE,GAAEA,GAAE,GAAEA,GAAE,KAAG,CAAC,GAAES,KAAER,IAAEC,KAAE,CAAC,KAAK,IAAIO,EAAC,GAAE,KAAK,IAAIA,EAAC,CAAC,GAAEN,KAAE,KAAK,KAAKD,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAE,MAAG,MAAIC,GAAE,QAAO;AAAK,EAAAD,GAAE,CAAC,KAAGC,IAAED,GAAE,CAAC,KAAGC;AAAE,QAAMC,KAAE,CAAAJ,OAAG;AAAC,UAAMC,OAAGD,GAAE,IAAEQ,GAAE,CAAC,KAAGN,GAAE,CAAC,KAAGF,GAAE,IAAEQ,GAAE,CAAC,KAAGN,GAAE,CAAC;AAAE,IAAAF,GAAE,IAAEQ,GAAE,CAAC,IAAEP,MAAEC,GAAE,CAAC,GAAEF,GAAE,IAAEQ,GAAE,CAAC,IAAEP,MAAEC,GAAE,CAAC;AAAA,EAAC;AAAE,SAAO,CAAAF,QAAII,GAAEJ,GAAE,QAAQ,GAAEI,GAAEJ,GAAE,MAAM,GAAE,EAAC,GAAGA,IAAE,MAAKE,GAAC;AAAE;AAAC,SAASQ,GAAEV,IAAEC,IAAE;AAAC,MAAIO,KAAE;AAAK,SAAO,CAAAN,OAAG;AAAC,QAAG,YAAUA,GAAE,WAASM,KAAE,EAAER,IAAEE,GAAE,SAAS,kBAAiBD,EAAC,IAAG,EAAEO,EAAC,EAAE,QAAO;AAAK,UAAML,KAAED,GAAE,OAAO,IAAEA,GAAE,SAAS,GAAEE,KAAEF,GAAE,OAAO,IAAEA,GAAE,SAAS,GAAEG,KAAEH,GAAE,OAAO,IAAEA,GAAE,SAAS;AAAE,WAAOM,GAAE,KAAKL,IAAEC,IAAEC,EAAC,GAAE,EAAC,GAAGH,IAAE,cAAaC,IAAE,cAAaC,IAAE,cAAaC,GAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEL,IAAEC,IAAE;AAAC,SAAO,EAAED,EAAC,IAAE,OAAKA,GAAE,iBAAiB,OAAOC,EAAC,IAAED,GAAE,MAAM,IAAE,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEO,IAAE;AAAC,QAAMN,KAAEF,GAAE,UAASG,KAAE,EAAEF,EAAC;AAAE,MAAG,EAAEC,EAAC,EAAE,QAAO;AAAK,MAAG,WAASA,GAAE,KAAK,QAAOS,GAAEX,IAAEE,IAAEC,IAAEK,EAAC;AAAE,QAAMJ,KAAE,EAAEF,IAAEC,EAAC,GAAEE,KAAEH,GAAE;AAAiB,SAAO,EAAEE,EAAC,IAAE,OAAK,EAAC,MAAK,CAACH,KAAEO,IAAEC,OAAI;AAAC,UAAMP,KAAEU,GAAER,GAAE,MAAM,GAAEH,KAAEO,IAAEC,EAAC;AAAE,IAAAP,GAAE,iBAAiB,OAAOG,EAAC,IAAEL,GAAE,WAASE,KAAEF,GAAE,WAAS,GAAEE,IAAEG,EAAC;AAAA,EAAC,EAAC;AAAC;AAAC,SAASM,GAAEX,IAAEC,IAAEQ,IAAEP,IAAE;AAAC,MAAG,EAAED,GAAE,SAAS,EAAE,QAAOY,GAAEb,IAAEC,IAAEA,GAAE,WAAUQ,EAAC;AAAE,MAAG,CAACR,GAAE,iBAAiB,OAAOQ,EAAC,EAAE,QAAO;AAAK,MAAIN,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,SAAM,EAAC,MAAK,CAACG,IAAEC,IAAEG,OAAI;AAAC,UAAME,KAAEN,KAAEL,IAAE,IAAEM,KAAEL,IAAEW,KAAEH,KAAEP;AAAE,QAAGS,MAAG,KAAGC,IAAE;AAAC,YAAMC,KAAE,IAAIC,GAAEhB,GAAE,OAAO,IAAEa,IAAEb,GAAE,OAAO,IAAE,GAAEA,GAAE,OAAO,IAAEc,IAAEd,GAAE,OAAO,gBAAgB;AAAE,MAAAA,GAAE,SAASe,IAAE,EAAC,YAAWd,OAAIY,GAAE,OAAM,CAAC,GAAEd,GAAE,sBAAsB,GAAEG,KAAEK,IAAEJ,KAAEK,IAAEJ,KAAEO;AAAA,IAAC;AAAA,EAAC,EAAC;AAAC;AAAC,SAASC,GAAEb,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEF,GAAE,eAAeD,GAAE,gBAAgB,GAAEE,EAAC,GAAEW,KAAEb,GAAE;AAAiB,SAAO,EAAEG,EAAC,IAAE,OAAK,EAAC,MAAK,CAACH,KAAEQ,IAAEN,OAAI;AAAC,UAAM,IAAES,GAAER,GAAE,MAAM,GAAEH,KAAEQ,IAAEN,EAAC;AAAE,QAAG,EAAE,iBAAiB,OAAOW,EAAC,EAAE,CAAAZ,GAAE,SAAOO,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC;AAAA,SAAM;AAAC,YAAMT,KAAE,GAAE,GAAEc,EAAC;AAAE,QAAEd,EAAC,MAAIE,GAAE,SAAOO,GAAET,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC;AAAA,IAAE;AAAC,IAAAA,GAAE,2BAA2B,GAAEA,GAAE,sBAAsB;AAAA,EAAC,EAAC;AAAC;AAAC,SAASkB,GAAElB,IAAEC,KAAE,MAAKC,IAAE;AAJxmF;AAIymF,MAAIC,KAAE;AAAK,QAAMC,KAAE,EAAEH,EAAC,KAAG,GAAC,KAAAD,GAAE,qBAAF,mBAAoB,OAAOC,OAAG,CAAAD,OAAG,EAAEA,EAAC,IAAE,GAAEA,IAAEC,EAAC,IAAED,KAAE,CAAAA,OAAGA,IAAEK,KAAE,EAAC,SAAQ,CAAC,GAAE,GAAGH,GAAC;AAAE,SAAO,CAAAD,QAAG;AAAC,QAAG,YAAUA,IAAE,WAASE,KAAEC,GAAEJ,GAAE,MAAMC,IAAE,aAAYI,EAAC,CAAC,IAAG,EAAEF,EAAC,EAAE,QAAO;AAAK,UAAMD,KAAEE,GAAEJ,GAAE,MAAMC,IAAE,WAAUI,EAAC,CAAC;AAAE,WAAO,EAAEH,EAAC,IAAE,EAAC,GAAGD,KAAE,UAASE,IAAE,QAAOD,GAAC,IAAE;AAAA,EAAI;AAAC;AAAC,SAASiB,GAAEnB,IAAEC,IAAE;AAAC,QAAMQ,KAAET,GAAE,IAAK,CAAAA,OAAGC,GAAES,GAAEV,IAAEC,EAAC,CAAC,CAAE,EAAE,OAAQ,CAAAD,OAAG,EAAEA,EAAC,CAAE;AAAE,SAAO,CAAAA,OAAG;AAAC,UAAMC,MAAED,GAAE,OAAO,IAAEA,GAAE,SAAS,GAAEQ,KAAER,GAAE,OAAO,IAAEA,GAAE,SAAS,GAAEE,KAAEF,GAAE,OAAO,IAAEA,GAAE,SAAS;AAAE,WAAOS,GAAE,QAAS,CAAAR,QAAGA,IAAED,EAAC,CAAE,GAAE,EAAC,GAAGA,IAAE,cAAaC,KAAE,cAAaO,IAAE,cAAaN,GAAC;AAAA,EAAC;AAAC;AAAC,SAASe,GAAEhB,IAAEO,IAAE;AAAC,QAAMC,KAAE,oBAAI;AAAI,aAAUP,MAAKM,GAAE,CAAAC,GAAE,IAAIP,IAAEkB,GAAEnB,GAAEC,EAAC,CAAC,CAAC;AAAE,SAAO,CAAAF,QAAIS,GAAE,QAAS,CAACT,IAAEQ,OAAI;AAAC,IAAAP,GAAEO,EAAC,IAAER;AAAA,EAAC,CAAE,GAAEA;AAAE;AAAC,SAASqB,GAAErB,IAAE;AAAC,SAAO,EAAEA,GAAE,QAAQ,KAAG,WAASA,GAAE,SAAS,OAAKsB,GAAEtB,IAAEA,GAAE,QAAQ,IAAEiB,GAAEjB,IAAE,CAAC,UAAU,CAAC;AAAC;AAAC,SAASsB,GAAEtB,IAAEC,IAAE;AAAC,QAAMQ,KAAE,EAAER,GAAE,SAAS,IAAEA,GAAE,UAAU,MAAM,IAAE,MAAKC,KAAED,GAAE,iBAAiB,gBAAgB;AAAE,SAAO,CAAAO,QAAIP,GAAE,YAAUQ,IAAER,GAAE,mBAAiBC,IAAEF,GAAE,sBAAsB,GAAEQ;AAAE;AAAC,SAASe,GAAEvB,IAAE;AAAC,QAAMC,KAAED,GAAE,IAAK,CAAAA,OAAGC,GAAEoB,GAAErB,EAAC,CAAC,CAAE,EAAE,OAAQ,CAAAA,OAAG,EAAEA,EAAC,CAAE;AAAE,SAAO,CAAAA,QAAIC,GAAE,QAAS,CAAAA,QAAGA,IAAED,EAAC,CAAE,GAAEA;AAAE;AAAC,SAASwB,KAAG;AAAC,MAAIxB,KAAE,GAAEC,KAAE,GAAEO,KAAE;AAAE,SAAO,CAAAC,OAAG;AAAC,gBAAUA,GAAE,WAAST,KAAES,GAAE,SAAS,GAAER,KAAEQ,GAAE,SAAS,GAAED,KAAEC,GAAE,SAAS;AAAG,UAAMP,KAAEO,GAAE,OAAO,IAAET,IAAEG,KAAEM,GAAE,OAAO,IAAER,IAAEG,KAAEK,GAAE,OAAO,IAAED;AAAE,WAAOR,KAAES,GAAE,OAAO,GAAER,KAAEQ,GAAE,OAAO,GAAED,KAAEC,GAAE,OAAO,GAAE,EAAC,GAAGA,IAAE,WAAUP,IAAE,WAAUC,IAAE,WAAUC,IAAE,0BAAyBK,GAAE,SAAS,iBAAgB;AAAA,EAAC;AAAC;AAAC,SAAS,IAAG;AAAC,MAAIT,KAAE,GAAEC,KAAE;AAAE,SAAO,CAAAO,OAAG;AAAC,gBAAUA,GAAE,WAASR,KAAEQ,GAAE,YAAY,GAAEP,KAAEO,GAAE,YAAY;AAAG,UAAMC,KAAED,GAAE,UAAU,IAAER,IAAEE,KAAEM,GAAE,UAAU,IAAEP;AAAE,WAAOD,KAAEQ,GAAE,UAAU,GAAEP,KAAEO,GAAE,UAAU,GAAE,EAAC,GAAGA,IAAE,cAAaC,IAAE,cAAaP,GAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEQ,IAAE;AAAC,MAAIC,KAAE,MAAKP,KAAE,GAAEC,KAAE;AAAE,SAAO,CAAAE,OAAG;AAJzmI;AAI0mI,QAAG,YAAUA,GAAE,WAASI,MAAE,KAAAT,GAAE,aAAF,wBAAAA,IAAaQ,KAAG,QAAMC,OAAIA,GAAE,IAAE,KAAGA,GAAE,IAAET,GAAE,SAAOS,GAAE,IAAE,KAAGA,GAAE,IAAET,GAAE,SAAOS,KAAE,QAAMP,KAAEG,GAAE,YAAY,IAAEI,GAAE,GAAEN,KAAEE,GAAE,YAAY,IAAEI,GAAE,MAAK,QAAMA,GAAE,QAAO;AAAK,UAAMG,KAAEV,GAAEG,GAAE,UAAU,IAAEH,IAAE,GAAEF,GAAE,KAAK,GAAEc,KAAEZ,GAAEG,GAAE,UAAU,IAAEF,IAAE,GAAEH,GAAE,MAAM,GAAE,IAAE,EAAEY,IAAEE,EAAC;AAAE,WAAOT,GAAE,cAAYI,IAAEJ,GAAE,YAAU,GAAEA;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,MAAI;AAAC;AAAE,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,cAAa;AAAC,SAAK,UAAQ;AAAA,EAAC;AAAA,EAAC,KAAKL,IAAEC,KAAE,IAAI,MAAE;AAAC,WAAO,EAAED,EAAC,MAAI,KAAK,UAAQ,CAAAS,OAAG;AAAC,YAAMP,KAAEF,GAAES,EAAC;AAAE,QAAEP,EAAC,KAAGD,GAAE,QAAQC,EAAC;AAAA,IAAC,IAAGD;AAAA,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEQ,KAAE,CAAC,GAAE;AAAC,MAAG,SAAOT,GAAE,KAAK,QAAO,CAAAA,OAAGA;AAAE,MAAIE,KAAE;AAAK,SAAO,CAAAC,OAAG;AAAC,gBAAUA,GAAE,WAASD,KAAEF,GAAE,MAAMG,GAAE,aAAY,EAAC,SAAQM,GAAC,CAAC,GAAE,EAAEP,EAAC,MAAIA,GAAE,IAAEuB,GAAEvB,IAAEF,IAAEC,EAAC;AAAI,UAAMG,KAAEJ,GAAE,MAAMG,GAAE,WAAU,EAAC,SAAQM,GAAC,CAAC;AAAE,MAAEL,EAAC,MAAIA,GAAE,IAAEqB,GAAErB,IAAEJ,IAAEC,EAAC;AAAG,UAAMI,KAAE,EAAEH,EAAC,KAAG,EAAEE,EAAC,IAAE,EAAC,YAAWF,IAAE,UAASE,GAAC,IAAE;AAAK,WAAM,EAAC,GAAGD,IAAE,aAAYE,GAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEL,IAAEC,IAAEO,IAAE;AAAC,QAAMC,KAAE,EAAER,GAAE,kBAAkB,aAAaD,GAAE,GAAEA,GAAE,GAAEA,GAAE,KAAG,GAAEA,GAAE,kBAAiB,OAAO,CAAC,KAAG,GAAEE,KAAEmB,GAAErB,EAAC;AAAE,SAAOE,GAAE,IAAEO,IAAEP,GAAE,OAAK,MAAGA,GAAE,IAAEuB,GAAEvB,IAAED,IAAEO,EAAC,GAAEN;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAE;AAAC,MAAG,SAAOD,GAAE,KAAK,QAAO,CAAAA,OAAGA;AAAE,MAAIS,KAAE;AAAK,SAAO,CAAAP,OAAG;AAAC,gBAAUA,GAAE,WAASO,KAAE,EAAEP,GAAE,UAASF,IAAEC,EAAC;AAAG,UAAME,KAAE,EAAED,GAAE,QAAOF,IAAEC,EAAC,GAAEG,KAAE,EAAEK,EAAC,KAAG,EAAEN,EAAC,IAAE,EAAC,YAAWM,IAAE,UAASN,GAAC,IAAE;AAAK,WAAM,EAAC,GAAGD,IAAE,aAAYE,GAAC;AAAA,EAAC;AAAC;;;ACA5kJ,SAASsB,GAAE,EAAC,WAAUC,KAAG,MAAI,MAAI,iBAAgBC,IAAE,iBAAgBC,IAAE,iBAAgB,GAAE,MAAKC,KAAE,KAAE,GAAE;AAAC,QAAMC,KAAE,IAAI;AAAE,MAAG,EAAEH,EAAC,EAAE,QAAM,EAAC,cAAa,CAACI,IAAED,EAAC,GAAE,gBAAeC,GAAC;AAAE,MAAIN,IAAEO,KAAE,MAAK,IAAE,MAAKC,KAAE;AAAK,QAAMC,KAAE,MAAI;AAAC,IAAAF,KAAE,EAAEA,EAAC,GAAEL,GAAE,aAAa,GAAE,EAAE,CAAC,KAAG,EAAE,UAAU,OAAO,GAAE,IAAE,MAAKF,KAAE,EAAEA,EAAC,GAAEQ,KAAE;AAAA,EAAI,GAAEE,KAAEC,GAAET,IAAEE,IAAEC,EAAC;AAAE,MAAIO,KAAE,MAAK,IAAE,MAAKC,KAAE;AAAK,SAAM,EAAC,cAAa,CAAC,CAAAC,OAAG;AAAC,QAAG,CAACb,GAAEa,EAAC,EAAE,QAAOA;AAAE,UAAK,EAAC,QAAOC,GAAC,IAAED;AAAE,QAAG,YAAUC,IAAE;AAAC,YAAK,EAAC,MAAKC,GAAC,IAAEF,IAAEC,KAAEE,GAAEf,GAAE,IAAI;AAAE,UAAG,IAAEgB,GAAEf,IAAEW,IAAEC,EAAC,GAAE,EAAE,QAAQ,gBAAc,MAAK,CAACX,MAAG,EAAEY,EAAC,GAAE;AAAC,cAAMF,KAAEK,GAAEhB,GAAE,kBAAiBa,GAAE,OAAO,SAAS;AAAE,UAAEF,EAAC,MAAI,EAAE,QAAQ,gBAAc,EAAC,OAAMA,IAAE,eAAcX,GAAE,cAAa;AAAA,MAAE;AAAA,IAAC;AAAC,QAAG,EAAE,CAAC,GAAE;AAAC,YAAK,EAAC,SAAQiB,IAAE,kBAAiBnB,IAAE,aAAYE,GAAC,IAAE,GAAE,EAAC,QAAOkB,IAAE,UAAShB,IAAE,aAAYM,GAAC,IAAEG,IAAEG,KAAEK,GAAEnB,IAAEoB,GAAEF,IAAEhB,EAAC,CAAC,GAAEa,KAAEK,GAAElB,IAAEF,EAAC,GAAEgB,KAAE,EAAC,GAAGL,IAAE,QAAO,SAAQ,GAAER,KAAE,EAAE,SAAQG,KAAEe,GAAEvB,IAAEU,EAAC,GAAEc,KAAEvB,GAAE,OAAO,EAAC,OAAMe,IAAE,YAAWR,IAAE,SAAQW,GAAC,CAAC;AAAE,UAAGP,KAAEY,IAAEC,GAAEL,IAAEI,IAAEP,IAAEd,EAAC,GAAEQ,KAAEK,IAAE,IAAER,IAAE,UAAQM,IAAE;AAAC,cAAK,EAAC,WAAUD,GAAC,IAAE;AAAE,UAAEP,EAAC,MAAIA,KAAE,IAAI,oBAAiBC,KAAE,CAAAQ,OAAG;AAAC,YAAE,WAAW,EAAEN,GAAE,EAAC,WAAUI,IAAE,OAAMK,IAAE,SAAQb,IAAE,OAAMW,IAAE,YAAWR,IAAE,OAAMS,IAAE,cAAa,OAAK,EAAC,OAAMN,IAAE,YAAW,GAAE,aAAYI,GAAE,cAAY,OAAKH,GAAC,GAAE,GAAEG,GAAET,EAAC,EAAE,MAAM,CAAC,CAAC;AAAA,QAAC,GAAEC,GAAE,EAAC,aAAY,MAAE,CAAC,GAAE,EAAER,EAAC,MAAIA,KAAEG,GAAG,MAAID,GAAE,QAAQ,kBAAmB,MAAIM,MAAA,gBAAAA,GAAI,EAAC,aAAY,KAAE,EAAG;AAAA,MAAE;AAAA,IAAC;AAAC,WAAM,UAAQO,MAAGN,GAAE,GAAEK;AAAA,EAAC,GAAET,EAAC,GAAE,gBAAe,CAAAW,QAAIP,GAAE,GAAEO,IAAE;AAAC;AAAC,SAASL,GAAEG,IAAEC,IAAEY,IAAE;AAAC,SAAO,EAAG,OAAM,EAAC,WAAUC,IAAE,OAAM3B,IAAE,YAAW4B,IAAE,SAAQ1B,IAAE,OAAMkB,IAAE,OAAM,GAAE,cAAajB,GAAC,GAAEC,OAAI;AAAC,UAAML,KAAE,MAAM4B,GAAE,SAAU,MAAId,GAAE,KAAK,EAAC,OAAMb,IAAE,YAAW4B,IAAE,SAAQ1B,IAAE,QAAOE,GAAC,CAAC,GAAGA,EAAC;AAAE,QAAGL,GAAE,OAAM;AAAC,UAAI6B,KAAE,MAAMD,GAAE,SAAU,MAAI5B,GAAE,MAAM,GAAGK,EAAC;AAAE,YAAMM,KAAEP,GAAE;AAAE,QAAEO,GAAE,KAAK,KAAGV,OAAIU,GAAE,UAAQkB,KAAEf,GAAE,OAAO,EAAC,OAAMH,GAAE,OAAM,YAAWA,GAAE,YAAW,SAAQR,GAAC,CAAC,IAAG,CAAC,EAAEQ,GAAE,WAAW,KAAGgB,GAAEE,IAAElB,GAAE,WAAW,MAAIe,GAAEL,GAAE,QAAOQ,IAAE,GAAEd,EAAC,GAAEY,GAAE,QAAQN,EAAC;AAAA,IAAE;AAAA,EAAC,CAAE;AAAC;AAAC,SAASJ,GAAED,IAAE;AAAC,SAAM,SAAOA,GAAE,OAAKA,GAAE,mBAAmB,UAAU,aAAa,EAAE,QAAQ,IAAEc;AAAC;AAAC,SAASZ,GAAEF,IAAEF,IAAEC,IAAE;AAAC,SAAM,EAAC,SAAQ,IAAIC,GAAE,EAAC,wBAAuBA,GAAE,wBAAuB,eAAcA,GAAE,eAAc,SAAQA,GAAE,SAAQ,cAAa,EAAEF,GAAE,IAAI,IAAEA,GAAE,KAAK,SAAO,MAAK,gBAAeE,GAAE,gBAAe,YAAWA,GAAE,WAAU,CAAC,GAAE,aAAY,EAAEF,GAAE,UAAU,IAAEE,GAAE,iBAAiB,wBAAwBF,GAAE,UAAU,IAAEA,GAAE,UAAS,kBAAiB,EAAEA,GAAE,WAAW,IAAEA,GAAE,YAAY,aAAW,MAAK,WAAUC,GAAC;AAAC;AAAC,SAASO,GAAEN,IAAE,CAACF,IAAEM,IAAEL,EAAC,GAAE;AAAC,QAAMY,KAAEI,GAAEf,EAAC;AAAE,SAAOW,GAAE,KAAGb,IAAEa,GAAE,KAAGP,IAAEO,GAAE,SAAOA,GAAE,KAAGZ,KAAGY;AAAC;AAAC,SAASH,GAAEV,IAAEM,IAAE;AAAC,SAAO,EAAEN,EAAC,KAAG,EAAEM,EAAC,IAAE,OAAKE,GAAER,IAAES,GAAEH,GAAE,UAASA,GAAE,UAAU,CAAC;AAAC;AAAC,SAASG,GAAEP,IAAEF,IAAE;AAAC,QAAMM,KAAEJ,GAAE,QAAMF,GAAE,OAAKE,GAAE,IAAEF,GAAE,IAAE;AAAE,SAAM,CAACE,GAAE,IAAEF,GAAE,GAAEE,GAAE,IAAEF,GAAE,GAAEM,EAAC;AAAC;AAAC,SAASM,GAAEV,IAAEF,IAAE,CAACM,IAAEL,IAAEY,EAAC,GAAEC,IAAE;AAAC,EAAAZ,GAAE,IAAEF,GAAE,IAAEM,IAAEJ,GAAE,IAAEF,GAAE,IAAEC,IAAEa,MAAGZ,GAAE,QAAMF,GAAE,SAAOE,GAAE,IAAEF,GAAE,IAAEa;AAAE;AAAC,SAASR,GAAEL,IAAEC,IAAE;AAAC,MAAG,CAACD,GAAE,KAAK,EAAE,QAAO;AAAK,QAAMa,KAAEZ,GAAE;AAAS,MAAIa,KAAE;AAAK,aAAU3B,MAAK0B,IAAE;AAAC,UAAMZ,KAAED,GAAE,KAAKb,GAAE,GAAG;AAAE,QAAG,EAAE2B,EAAC,KAAG,EAAEb,EAAC,KAAG,KAAK,IAAIA,KAAEa,EAAC,IAAE,KAAK,QAAO;AAAK,MAAEA,EAAC,MAAIA,KAAEb;AAAA,EAAE;AAAC,SAAOa;AAAC;AAAC,SAAStB,GAAEU,IAAE;AAAC,SAAOA;AAAC;;;ACA/nG,IAAMgB,KAAN,MAAO;AAAA,EAAC,YAAY,EAAC,mBAAkBA,GAAC,GAAE;AAAC,SAAK,SAAO,IAAI,KAAE,KAAK,cAAY,MAAG,KAAK,aAAW,OAAG,KAAK,SAAO,MAAK,KAAK,YAAU,MAAG,KAAK,oBAAkBA;AAAA,EAAC;AAAA,EAAC,qBAAqBC,IAAED,IAAE;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAA,EAAC;AAAA,EAAC,eAAc;AAAA,EAAC;AAAC;;;ACAoR,IAAIE,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,kBAAgB,CAAAA,OAAGA,IAAE,KAAK,cAAY,MAAK,KAAK,aAAW,MAAK,KAAK,mBAAiB,MAAK,KAAK,eAAa,MAAK,KAAK,QAAM,EAAG,OAAMA,IAAEC,IAAEC,IAAEC,OAAI;AAAC,YAAMC,KAAE,KAAK;AAAW,UAAG,EAAEA,EAAC,EAAE;AAAO,YAAMC,KAAE,MAAMD,GAAE,SAAU,MAAIH,GAAE,KAAK,EAAC,GAAGD,IAAE,SAAQE,IAAE,QAAOC,GAAC,CAAC,GAAGA,EAAC;AAAE,MAAAE,GAAE,SAAO,MAAMD,GAAE,SAAU,MAAI;AAAC,aAAK,cAAYC,GAAE,MAAM,GAAEL,OAAI,KAAK,eAAa,EAAE,KAAK,WAAW,MAAI,KAAK,cAAYC,GAAE,OAAO,EAAC,GAAG,KAAK,aAAY,SAAQC,GAAC,CAAC;AAAA,MAAE,GAAGC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,YAAYH,IAAE;AAAC,SAAK,eAAa,KAAK,gBAAgBA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAJnoC;AAIooC,UAAMA,KAAE,SAAO,KAAK,KAAK,QAAK,gBAAK,SAAL,mBAAW,uBAAX,mBAA+B,YAAU;AAAK,SAAK,aAAW,EAAEA,EAAC,IAAEA,GAAE,aAAa,EAAE,QAAQ,IAAEM;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,mBAAiB,EAAE,KAAK,gBAAgB,GAAE,KAAK,aAAW,EAAE,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,OAAON,IAAEC,IAAEM,IAAE;AAAC,SAAK,cAAYP;AAAE,UAAK,EAAC,OAAME,IAAE,YAAWC,GAAC,IAAEH,IAAEQ,KAAEP,GAAE,OAAO,EAAC,OAAMC,IAAE,YAAWC,IAAE,SAAQI,GAAC,CAAC;AAAE,WAAO,KAAK,cAAYC,IAAEA;AAAA,EAAC;AAAA,EAAC,MAAM,KAAKR,IAAEC,IAAEM,IAAE;AAAC,UAAK,EAAC,OAAML,IAAE,YAAWC,GAAC,IAAEH;AAAE,WAAO,KAAK,cAAYC,GAAE,OAAO,EAAC,OAAMC,IAAE,YAAWC,IAAE,SAAQI,GAAC,CAAC,GAAE,KAAK,cAAYP,IAAE,EAAE,KAAK,gBAAgB,MAAI,KAAK,mBAAiB,IAAI,oBAAiB,KAAK,MAAMA,IAAEC,IAAEM,IAAE,KAAK,iBAAiB,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOP,IAAEC,IAAE;AAAC,MAAE,KAAK,WAAW,KAAG,MAAM,KAAK,KAAK,KAAK,aAAYD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,mBAAiB,EAAE,KAAK,gBAAgB,GAAE,KAAK,cAAY;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEF,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAEA,KAAE,EAAE,CAACM,GAAE,mDAAmD,CAAC,GAAEN,EAAC;;;ACAzR,IAAIU,KAAE,cAAc,EAAE,aAAaC,EAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,4BAA0B,OAAG,KAAK,qBAAmB,oBAAI,OAAI,KAAK,WAAS,MAAG,KAAK,eAAa,IAAIC,MAAE,KAAK,iBAAe,IAAIC,MAAE,KAAK,qBAAmB,MAAK,KAAK,aAAW,MAAK,EAAEF,GAAE,aAAa,MAAI,KAAK,gBAAcG,GAAE,CAAC,CAACH,GAAE,IAAI;AAAA,EAAE;AAAA,EAAC,aAAY;AAAC,UAAK,EAAC,cAAaA,IAAE,MAAKI,GAAC,IAAE,MAAKC,KAAED,GAAE,kBAAiBE,KAAE,iBAAgBF,GAAE,QAAMA,GAAE,MAAM,cAAYG,GAAE,OAAMJ,KAAE,cAAYH,MAAG,iBAAeA,KAAE,aAAWA;AAAE,SAAK,mBAAiBQ,GAAE,KAAK,MAAK,KAAK,MAAKH,EAAC,GAAE,KAAK,0BAAwB,IAAI,EAAE,IAAII,GAAEN,IAAE,KAAK,gBAAgB,CAAC,GAAE,KAAK,qBAAmB,IAAIO,GAAE,EAAC,MAAKN,IAAE,iBAAgB,CAAAJ,QAAC;AAJr9E;AAIu9E,eAAEA,GAAC,IAAEA,OAAE,UAAK,yBAAyB,MAA9B,mBAAiC,WAAWA;AAAA,MAAE,CAAC,GAAE,KAAK,QAAQ,IAAIO,GAAG,MAAI,KAAK,cAAe,CAAAP,QAAG;AAAC,QAAEA,GAAC,KAAG,KAAK,KAAK,iBAAgB,EAAC,SAAQ,MAAK,UAAS,CAAC,EAAC,gBAAe,GAAE,aAAY,KAAK,iBAAiB,SAAS,QAAO,aAAY,KAAK,iBAAiB,aAAaA,GAAC,EAAC,CAAC,GAAE,WAAU,SAAQ,MAAK,gBAAe,CAAC;AAAA,IAAC,GAAG,EAAC,MAAK,MAAG,QAAO,CAACA,KAAEI,OAAI,EAAEJ,KAAEI,IAAEC,EAAC,EAAC,CAAC,CAAC,GAAE,KAAK,mBAAiB,IAAIH,GAAEG,IAAEC,EAAC,GAAE,KAAK,wBAAwB,KAAK,WAAW,KAAK,KAAK,gBAAgB;AAAE,UAAMK,KAAE,KAAK;AAAc,MAAEA,EAAC,MAAIA,GAAE,UAAQ,EAAC,MAAKP,IAAE,wBAAuB,KAAK,yBAAwB,eAAc,KAAK,eAAc,cAAa,KAAK,aAAY,GAAE,KAAK,QAAQ,IAAI,CAACG,GAAG,MAAI,KAAK,aAAa,SAAU,CAAAP,QAAG;AAAC,MAAAW,GAAE,UAAQX;AAAA,IAAC,GAAGQ,EAAC,GAAE,KAAK,GAAG,iBAAiB,MAAI;AAAC,YAAMR,MAAE,KAAK;AAAa,MAAAW,GAAE,eAAa,EAAEX,GAAC,IAAE,KAAK,iBAAiB,cAAcA,GAAC,IAAE;AAAA,IAAI,CAAE,CAAC,CAAC,IAAG,KAAK,QAAQ,IAAI,KAAK,wBAAwB,GAAG,CAAC,cAAa,iBAAgB,eAAe,GAAG,CAAAA,QAAG;AAAC,YAAMI,KAAEJ,IAAE,SAAS,IAAK,CAAAA,SAAI,EAAC,gBAAe,GAAE,aAAYA,IAAE,OAAM,aAAY,KAAK,iBAAiB,cAAcA,IAAE,GAAG,EAAC,EAAG,GAAEK,KAAED,GAAE,IAAK,CAAAJ,QAAGA,IAAE,WAAY;AAAE,cAAOA,IAAE,MAAK;AAAA,QAAC,KAAI;AAAa,eAAK,KAAKA,IAAE,MAAK,EAAC,GAAGA,KAAE,OAAMK,IAAE,UAASD,GAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAgB,eAAK,KAAKJ,IAAE,MAAK,EAAC,GAAGA,KAAE,SAAQK,IAAE,UAASD,GAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAgB,eAAK,KAAKJ,IAAE,MAAK,EAAC,GAAGA,KAAE,SAAQK,IAAE,UAASD,GAAC,CAAC;AAAA,MAAC;AAAC,YAAME,KAAE,KAAK,iBAAiB,cAAc,GAAEH,KAAE,EAAEG,EAAC,IAAE,KAAK,iBAAiB,wBAAwBA,GAAE,GAAG,IAAE;AAAK,OAAC,EAAEH,EAAC,KAAG,EAAE,KAAK,UAAU,KAAG,CAACE,GAAE,KAAK,YAAWF,EAAC,OAAK,KAAK,aAAWA;AAAA,IAAE,CAAE,CAAC,GAAE,KAAK,eAAa,IAAIC,GAAE,EAAC,mBAAkB,CAAAJ,QAAG,YAAU,KAAK,eAAa,YAAUA,IAAE,eAAa,KAAK,oBAAkB,MAAI,KAAK,mBAAmB,KAAI,CAAC,GAAE,KAAK,aAAa,IAAI,KAAK,YAAY,GAAE,KAAK,aAAa,YAAU,YAAUA,IAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,+BAA+B,KAAK,YAAY,GAAE,KAAK,aAAa,OAAO,GAAG,mBAAmB,CAAAA,QAAG,KAAK,kBAAkBA,GAAC,CAAE,GAAE,KAAK,aAAa,OAAO,GAAG,0BAA0B,CAAAA,QAAG,KAAK,wBAAwBA,GAAC,CAAE,CAAC,CAAC,GAAE,EAAE,MAAM,MAAI;AAAC,YAAMA,MAAE,EAAE,KAAK,KAAK,aAAa,mBAAkB,OAAO,GAAEI,KAAE,KAAK,oBAAoBJ,GAAC;AAAE,QAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,WAAW,EAAE,KAAK,mBAAmB,OAAO,KAAK,iBAAgBI,EAAC,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,MAAE,KAAK,aAAa,GAAE,EAAE,KAAK,kBAAkB,GAAE,KAAK,0BAAwB,EAAE,KAAK,uBAAuB;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,EAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,QAAQ;AAAA,EAAgB;AAAA,EAAC,IAAI,sBAAqB;AAAC,UAAMJ,KAAE,KAAK,yBAAyB;AAAE,WAAM,SAAO,KAAK,KAAK,QAAM,KAAK,gBAAcA;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,wBAAwB;AAAA,EAAO;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,wBAAwB;AAAA,EAAO;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAO,KAAK,iBAAiB,SAAS,IAAK,CAAAA,OAAG,KAAK,iBAAiB,cAAcA,GAAE,GAAG,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,SAAK,KAAK,eAAcA,MAAGA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,aAAa;AAAA,EAAW;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,SAAK,aAAa,cAAYA;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAyB;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,iBAAiB,SAAS;AAAA,EAAM;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,EAAE,KAAK,YAAY,IAAE,KAAK,iBAAiB,SAAS,SAAO,IAAE,KAAK,iBAAiB,SAAS;AAAA,EAAM;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,EAAE,KAAK,eAAe,IAAE,KAAK,gBAAgB,UAAQ;AAAA,EAAI;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,mBAAmB;AAAA,EAAW;AAAA,EAAC,IAAI,aAAaA,IAAE;AAAC,SAAK,mBAAmB,cAAYE,GAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,gBAAgB;AAAA,EAAQ;AAAA,EAAC,IAAI,WAAU;AAAC,UAAMA,KAAE,KAAK;AAAkB,WAAO,EAAE,KAAK,YAAY,KAAGA,GAAE,KAAK,KAAK,iBAAiB,aAAa,KAAK,YAAY,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,SAAS,EAAC,SAAQ,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,QAAG,KAAK,mBAAmB,MAAM,GAAE,EAAE,KAAK,YAAY,GAAE;AAAC,YAAK,EAAC,cAAaA,GAAC,IAAE;AAAK,WAAK,eAAa,MAAK,KAAK,wBAAwB,aAAa,KAAK,iBAAiB,cAAcA,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,UAAMI,KAAEJ,MAAGA,GAAE,WAAS;AAAG,SAAK,mBAAmB,MAAM,GAAE,EAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,aAAa,GAAE,cAAY,KAAK,gBAAc,YAAU,KAAK,eAAa,KAAK,mBAAmB,IAAE,KAAK,eAAa;AAAK,UAAMK,KAAE,iBAAe,KAAK,gBAAc,MAAI,KAAK,eAAa,eAAa,KAAK,gBAAc,KAAK,cAAY,KAAG,cAAY,KAAK,gBAAc,KAAK,cAAY;AAAE,SAAK,4BAA0B,CAACA,KAAG,KAAK,eAAaD,OAAI,KAAK,KAAK,YAAW,EAAC,UAAS,KAAK,SAAS,IAAK,CAACJ,KAAEI,QAAK,EAAC,gBAAe,GAAE,aAAYA,IAAE,aAAYJ,IAAC,EAAG,GAAE,SAAQI,IAAE,MAAK,WAAU,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaJ,IAAE;AAAC,YAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAe,aAAK,mBAAmB,IAAIA,GAAE,SAAS;AAAE;AAAA,MAAM,KAAI;AAAa,aAAK,mBAAmB,OAAOA,GAAE,SAAS;AAAA,IAAC;AAAC,YAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAe,eAAO,KAAK,eAAeA,EAAC;AAAA,MAAE,KAAI;AAAO,eAAO,KAAK,QAAQA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,wBAAwB,KAAK;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,MAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,aAAa,GAAE,KAAK,wBAAwB,KAAK;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAE;AAAC,UAAMI,KAAE,KAAK;AAAiB,QAAG,cAAY,KAAK,gBAAcA,GAAE,SAAS,SAAO,GAAE;AAAC,UAAG,KAAK,6BAA6BA,GAAE,SAAS,CAAC,EAAE,KAAIJ,EAAC,EAAE,QAAO;AAAE,UAAG,KAAK,6BAA6BI,GAAE,SAASA,GAAE,SAAS,SAAO,CAAC,EAAE,KAAIJ,EAAC,EAAE,QAAOI,GAAE,SAAS,SAAO;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,+BAA+BJ,IAAE;AAAC,YAAOA,GAAE,KAAK,WAAW,GAAE;AAAA,MAAC,KAAI;AAAQ,eAAO,KAAK,oCAAoCA,EAAC;AAAA,MAAE,KAAI;AAAW,eAAO,KAAK,uCAAuCA,EAAC;AAAA,MAAE,KAAI;AAAS,eAAO,KAAK,qCAAqCA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oCAAoCA,IAAE;AAAC,WAAOY,GAAEZ,IAAG,CAACA,KAAEI,IAAEC,IAAEC,OAAI;AAAC,YAAMO,KAAE,YAAUP,MAAG,KAAK;AAAiB,UAAG,KAAK,eAAa,CAACO,GAAE;AAAO,YAAK,EAAC,cAAaV,IAAE,gBAAeQ,GAAC,IAAEG,GAAE,EAAC,WAAU,MAAID,IAAE,iBAAgB,KAAK,iBAAgB,iBAAgB,IAAIb,GAAE,EAAC,wBAAuB,KAAK,yBAAwB,eAAc,KAAK,eAAc,SAAQM,IAAE,YAAW,KAAK,mBAAkB,CAAC,GAAE,iBAAgB,KAAK,iBAAgB,MAAK,CAAC,KAAK,oBAAmB,CAAC;AAAE,MAAAD,KAAEA,GAAE,KAAM,CAAAL,SAAIa,MAAG,EAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,aAAa,GAAEb,IAAG,EAAE,KAAKW,EAAC,GAAEP,GAAE,KAAK,KAAK,0BAA0B,CAAC,EAAE,KAAM,CAAAJ,SAAI,YAAUA,IAAE,WAAS,KAAK,eAAaA,IAAE,WAAU,cAAY,KAAK,gBAAca,MAAG,MAAI,KAAK,gBAAc,KAAK,mBAAmB,IAAGb,IAAG,EAAE,KAAK,EAAE,KAAK,MAAK,KAAK,aAAa,CAAC,EAAE,KAAK,GAAGG,EAAC,EAAE,KAAM,CAAAH,SAAIa,OAAI,KAAK,eAAab,IAAE,QAAO,UAAQA,IAAE,UAAQ,KAAK,mBAAmB,IAAGA,IAAG,EAAE,KAAM,CAAAA,SAAI,UAAQA,IAAE,WAAS,cAAY,KAAK,gBAAc,YAAU,KAAK,gBAAc,KAAK,SAAS,IAAGA,IAAG;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,uCAAuCA,IAAE;AAAC,WAAOY,GAAEZ,IAAG,CAACA,KAAEI,OAAI;AAAC,WAAK,eAAaA,GAAE,KAAK,KAAK,0BAA0B,CAAC,EAAE,KAAM,CAAAJ,SAAI,YAAUA,IAAE,WAAS,EAAE,KAAK,YAAY,MAAI,KAAK,eAAaA,IAAE,WAAU,cAAY,KAAK,gBAAc,KAAK,mBAAmB,IAAGA,IAAG,EAAE,KAAM,CAAAA,QAAG;AAAC,gBAAOA,IAAE,QAAO;AAAA,UAAC,KAAI;AAAA,UAAQ,KAAI;AAAS,iBAAK,eAAaA,IAAE,QAAO,cAAY,KAAK,gBAAc,eAAa,KAAK,gBAAc,KAAK,mBAAmB;AAAE;AAAA,UAAM,KAAI;AAAM,iBAAK,SAAS;AAAA,QAAC;AAAC,eAAOA;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qCAAqCA,IAAE;AAAC,WAAOY,GAAEZ,IAAG,CAACA,KAAEI,OAAI;AAAC,WAAK,eAAaA,GAAE,KAAK,KAAK,0BAA0B,CAAC,EAAE,KAAM,CAAAJ,SAAI,YAAUA,IAAE,WAAS,EAAE,KAAK,YAAY,MAAI,KAAK,eAAaA,IAAE,WAAU,KAAK,mBAAmB,IAAGA,IAAG,EAAE,KAAM,CAAAA,QAAG;AAAC,gBAAOA,IAAE,QAAO;AAAA,UAAC,KAAI;AAAA,UAAQ,KAAI;AAAS,iBAAK,eAAaA,IAAE,QAAO,cAAY,KAAK,gBAAc,eAAa,KAAK,gBAAc,KAAK,mBAAmB;AAAE;AAAA,UAAM,KAAI;AAAM,0BAAY,KAAK,gBAAc,YAAU,KAAK,gBAAc,KAAK,SAAS;AAAA,QAAC;AAAC,eAAOA;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,wBAAuB;AAAC,YAAO,cAAY,KAAK,gBAAc,cAAY,KAAK,iBAAe,KAAK,uBAAqB;AAAA,EAAC;AAAA,EAAC,2BAA0B;AAAC,QAAG,EAAE,KAAK,oBAAoB,EAAE,QAAO,KAAK;AAAY,QAAG,CAAC,KAAK,iBAAiB,KAAK,EAAE,QAAO,KAAK,qBAAqB,WAAS,MAAK,KAAK;AAAqB,QAAIA,KAAE,KAAK,UAASI,KAAE;AAAG,WAAO,EAAE,KAAK,aAAa,KAAG,sBAAoB,KAAK,cAAc,SAAOA,KAAE,OAAI,EAAE,KAAK,kBAAkB,MAAIA,KAAE,KAAK,qBAAoB,EAAE,KAAK,aAAa,KAAG,oBAAkB,KAAK,cAAc,SAAOA,KAAE,QAAI,KAAK,0BAAwBJ,KAAE,KAAK,iBAAiB,KAAK,KAAK,iBAAiB,SAAS,CAAC,EAAE,GAAG,GAAEI,KAAE,QAAIA,KAAE,KAAK,eAAa,KAAK,qBAAqB,WAASJ,IAAE,KAAK;AAAA,EAAqB;AAAA,EAAC,aAAaA,IAAE;AAJ34U;AAI44U,YAAO,UAAK,yBAAyB,MAA9B,mBAAiC,YAAYA;AAAA,EAAE;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,mBAAmB,MAAM,GAAE,YAAU,KAAK,eAAa,YAAUA,GAAE,eAAa,KAAK,qBAAmB,KAAK,eAAaA,GAAE,WAAUA,GAAE,gBAAgB;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,QAAG,YAAUA,GAAE,eAAa,MAAIA,GAAE,UAAQ,KAAK,aAAa,SAAS;AAAO,UAAMI,KAAE,KAAK,kBAAiBC,KAAE,KAAK,yBAAyBL,GAAE,WAAW;AAAE,QAAG,EAAEK,EAAC,EAAE,QAAOL,GAAE,gBAAgB,GAAE,KAAK,KAAK,SAAS;AAAE,UAAMM,KAAE,KAAK,aAAaN,GAAE,WAAW;AAAE,QAAG,EAAEM,EAAC,EAAE,SAAO,KAAK,aAAY;AAAA,MAAC,KAAI;AAAW,oBAAU,KAAK,iBAAe,EAAE,KAAK,YAAY,IAAE,KAAK,mBAAmB,IAAE,KAAK,wBAAwB,aAAa,KAAK,iBAAiB,cAAcA,EAAC,CAAC,GAAE,KAAK,SAAS;AAAG;AAAA,MAAM,KAAI;AAAA,MAAQ,KAAI;AAAS,aAAK,mBAAmB,MAAM,GAAE,EAAE,KAAK,YAAY,IAAE,KAAK,mBAAmB,IAAE,KAAK,wBAAwB,aAAa,KAAK,iBAAiB,cAAcA,EAAC,CAAC,IAAG,YAAU,KAAK,gBAAc,cAAY,KAAK,gBAAc,MAAIF,GAAE,SAAS,UAAQ,cAAY,KAAK,gBAAc,aAAW,KAAK,eAAa,MAAIA,GAAE,SAAS,WAAS,KAAK,SAAS;AAAA,IAAC;AAAC,IAAAJ,GAAE,gBAAgB;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAE;AAAC,SAAK,aAAa,YAAU,YAAU,KAAK,iBAAe,KAAK,SAAS,GAAEA,GAAE,gBAAgB;AAAA,EAAE;AAAA,EAAC,eAAeA,IAAE;AAJnnX;AAIonX,UAAMI,KAAE,EAAEJ,GAAE,GAAEA,GAAE,CAAC,GAAEK,KAAE,KAAK;AAAmB,QAAG,KAAK,aAAa,YAAU,KAAK,mBAAmB,IAAIL,GAAE,SAAS,KAAG,KAAK,aAAa,YAAU,CAAC,KAAK,aAAa,YAAY,QAAO,KAAKK,GAAE,MAAM;AAAE,IAAAL,GAAE,gBAAgB;AAAE,UAAMM,KAAE,KAAK,yBAAyBF,EAAC;AAAE,QAAG,EAAEE,EAAC,EAAE,QAAO,KAAK,eAAeA,EAAC,GAAE,KAAKD,GAAE,MAAM;AAAE,UAAMF,KAAE,KAAK,aAAaC,EAAC,GAAEO,KAAE,KAAK,uBAAoB,UAAK,gBAAL,mBAAkB,YAAYP,MAAG;AAAK,QAAG,KAAK,aAAa,SAAO,EAAED,EAAC,IAAE,cAAY,MAAK,EAAEA,EAAC,EAAE,QAAO,KAAKE,GAAE,MAAM;AAAE,UAAMH,KAAE,KAAK;AAAgB,QAAG,EAAEA,EAAC,EAAE,QAAO,KAAK,eAAaC,IAAE,KAAKE,GAAE,MAAM;AAAE,UAAMJ,KAAE,KAAK,oBAAoBD,GAAE,WAAW;AAAE,SAAK,gBAAgB,WAAW,EAAEK,GAAE,KAAK,EAAC,OAAMF,IAAE,YAAWQ,GAAC,GAAET,IAAED,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAE;AAAC,SAAK,eAAa;AAAK,UAAMI,KAAE,EAAC,gBAAe,GAAE,aAAYJ,IAAE,aAAY,KAAK,iBAAiB,cAAc,KAAK,iBAAiB,SAASA,EAAC,EAAE,GAAG,EAAC;AAAE,SAAK,KAAK,iBAAgB,EAAC,SAAQ,MAAK,UAAS,CAACI,EAAC,GAAE,WAAU,SAAQ,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaJ,IAAE;AAJpjZ;AAIqjZ,YAAO,UAAK,yBAAyB,MAA9B,mBAAiC,YAAYA;AAAA,EAAE;AAAA,EAAC,4BAA2B;AAAC,QAAIA,KAAE;AAAK,WAAO,CAAAI,OAAG;AAAC,UAAG,YAAUA,GAAE,WAASJ,KAAE,KAAK,aAAaI,GAAE,WAAW,IAAG,EAAEJ,EAAC,EAAE,QAAO;AAAK,YAAMK,KAAE,KAAK,aAAaD,GAAE,SAAS;AAAE,aAAO,EAAEC,EAAC,IAAE,EAAC,GAAGD,IAAE,UAASJ,IAAE,QAAOK,GAAC,IAAE;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,6BAA6BL,IAAEI,IAAE;AAAC,UAAMC,KAAE,IAAGC,KAAE,KAAK,aAAa,KAAK,iBAAiB,wBAAwBN,EAAC,CAAC;AAAE,WAAM,CAAC,CAAC,EAAEM,EAAC,KAAG,EAAEA,IAAEF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBL,IAAE;AAAC,UAAMI,KAAE,KAAK,wBAAsB,EAAE,KAAK,sBAAsB,CAAC,EAAC,UAASJ,IAAC,MAAIA,GAAE,IAAE;AAAK,WAAO,IAAIA,GAAE,EAAC,wBAAuB,KAAK,yBAAwB,eAAc,KAAK,eAAc,SAAQA,IAAE,YAAW,KAAK,oBAAmB,eAAc,EAAEI,EAAC,IAAE,EAAC,OAAMA,IAAE,eAAc,KAAK,cAAa,IAAE,KAAI,CAAC;AAAA,EAAC;AAAC;AAAE,SAAS,EAAEJ,IAAEI,IAAEC,IAAE;AAAC,QAAMC,KAAEN,GAAE,IAAEI,GAAE,GAAES,KAAEb,GAAE,IAAEI,GAAE;AAAE,SAAOE,KAAEA,KAAEO,KAAEA,MAAGR;AAAC;AAAC,EAAE,CAAC,EAAE,CAAC,GAAEP,GAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAME,GAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,MAAG,MAAKG,GAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,MAAG,MAAKI,GAAC,CAAC,CAAC,GAAEJ,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAACa,GAAE,+BAA+B,CAAC,GAAEb,EAAC;;;ACAjrc,IAAMiB,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,KAAE,MAAK;AAAC,SAAK,iBAAeH,IAAE,KAAK,WAASC,IAAE,KAAK,QAAMC,IAAE,KAAK,mBAAiBC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,QAAG,EAAE,KAAK,QAAQ,EAAE,QAAO,KAAK,MAAM,wBAAwB,6BAA6B,EAAEA,GAAE,GAAEA,GAAE,CAAC,GAAE,KAAK,gBAAe,KAAK,UAAS,KAAK,gBAAgB;AAAE,UAAMC,KAAE,KAAK,MAAM,wBAAwB,6BAA6B,EAAED,GAAE,GAAEA,GAAE,CAAC,GAAE,KAAK,gBAAe,GAAE,KAAK,gBAAgB;AAAE,WAAO,EAAEC,EAAC,MAAIA,GAAE,IAAE,SAAQA;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAE;AAAC,UAAMC,KAAE,EAAED,GAAE,GAAEA,GAAE,GAAEI,GAAE,KAAK,OAAMJ,IAAE,KAAK,cAAc,GAAEA,GAAE,gBAAgB;AAAE,WAAO,KAAK,MAAM,SAASC,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAK,EAAC,UAASC,GAAC,IAAE;AAAK,WAAO,EAAEA,EAAC,KAAGD,GAAE,MAAIC,QAAKD,KAAEI,GAAEJ,EAAC,GAAG,IAAEC,KAAGD;AAAA,EAAC;AAAC;AAAC,IAAMK,KAAN,MAAO;AAAA,EAAC,YAAYN,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,SAAK,OAAKF,IAAE,KAAK,gBAAcC,IAAE,KAAK,UAAQC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMC,KAAE,KAAK,KAAK,MAAMD,IAAE,EAAC,SAAQ,KAAK,QAAO,CAAC;AAAE,WAAO,EAAEC,EAAC,MAAIA,GAAE,IAAEK,GAAEL,IAAE,KAAK,MAAK,KAAK,aAAa,IAAGA;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,QAAIE,KAAEF;AAAE,WAAO,EAAE,KAAK,aAAa,MAAIE,KAAE,EAAEF,GAAE,GAAEA,GAAE,GAAEG,GAAE,KAAK,MAAKH,IAAE,KAAK,aAAa,GAAEA,GAAE,gBAAgB,IAAG,KAAK,KAAK,SAASE,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWH,IAAE;AAAC,WAAOA;AAAA,EAAC;AAAC;AAAC,IAAMQ,KAAN,MAAO;AAAA,EAAC,YAAYR,IAAEC,KAAE,OAAGC,KAAE,GAAE;AAAC,SAAK,OAAKF,IAAE,KAAK,OAAKC,IAAE,KAAK,WAASC,IAAE,KAAK,cAAY,CAAAD,OAAGD,GAAE,SAASC,EAAC,GAAE,KAAK,cAAYA,KAAE,CAAAA,OAAG;AAAC,YAAME,KAAEH,GAAE,MAAMC,EAAC;AAAE,aAAOE,GAAE,IAAED,IAAEC;AAAA,IAAC,IAAE,CAAAF,OAAGD,GAAE,MAAMC,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWD,IAAE;AAAC,UAAK,EAAC,UAASC,GAAC,IAAE;AAAK,WAAO,KAAK,QAAMD,GAAE,MAAIC,QAAKD,KAAEK,GAAEL,EAAC,GAAG,IAAEC,KAAGD;AAAA,EAAC;AAAC;;;ACArgD,IAAIS;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,sBAAoB,CAAC,IAAE,uBAAsBA,GAAEA,GAAE,SAAO,CAAC,IAAE;AAAQ,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,kBAAgB,MAAG,KAAK,gBAAc,IAAI,KAAE,KAAK,oBAAkB,EAAC,eAAc,CAAC,EAAC,GAAE,KAAK,YAAU;AAAA,EAAE;AAAA,EAAC,IAAI,eAAeD,IAAE;AAAC,SAAK,kBAAgBA;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,cAAc;AAAA,EAAM;AAAA,EAAC,IAAIA,IAAEE,KAAEH,GAAE,kBAAiB;AAAC,SAAK,QAAQ,CAACC,EAAC,GAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQF,IAAEE,KAAEH,GAAE,kBAAiB;AAAC,eAAUA,MAAKC,IAAE;AAAC,YAAMA,KAAE,EAAC,aAAYD,IAAE,qBAAoBG,IAAE,UAAS,MAAE;AAAE,WAAK,cAAc,IAAIF,EAAC,GAAE,KAAK,aAAW,KAAK,6BAA6BA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,aAAQE,KAAE,GAAEA,KAAE,KAAK,cAAc,QAAOA,KAAI,KAAG,KAAK,cAAc,UAAUA,EAAC,EAAE,gBAAcF,IAAE;AAAC,YAAMA,KAAE,KAAK,cAAc,OAAOE,IAAE,CAAC,EAAE,CAAC;AAAE,WAAK,mBAAmBF,EAAC;AAAE;AAAA,IAAK;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,SAAK,cAAc,QAAS,CAAAA,OAAG;AAAC,WAAK,mBAAmBA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,cAAc,UAAU;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,cAAc,QAAS,CAAAA,OAAG;AAAC,WAAK,6BAA6BA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,YAAU;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,SAAK,cAAc,QAAS,CAAAA,OAAG;AAAC,WAAK,mBAAmBA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,YAAU;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,OAAO,GAAE,KAAK,cAAc,QAAS,CAAC,EAAC,aAAYA,GAAC,MAAI;AAAC,MAAAA,GAAE,WAASA,GAAE,QAAQ;AAAA,IAAC,CAAE,GAAE,KAAK,cAAc,QAAQ,GAAE,KAAK,oBAAkB;AAAA,EAAI;AAAA,EAAC,GAAGA,IAAEE,IAAE;AAAC,WAAO,KAAK,cAAc,GAAGF,IAAG,CAAAA,OAAG;AAAC,MAAAE,GAAEF,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,eAAUE,MAAK,KAAK,cAAc,MAAM,CAAAF,GAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKF,IAAE;AAAC,WAAO,KAAK,cAAc,MAAM,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAMA,KAAE,CAAC;AAAE,WAAO,KAAK,QAAS,CAAAE,OAAGF,GAAE,KAAKE,GAAE,WAAW,CAAE,GAAEF;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAED,IAAE;AAAC,QAAIE,KAAE,MAAKE,KAAE,OAAO;AAAU,WAAO,KAAK,cAAc,QAAS,CAAC,EAAC,aAAYC,IAAE,UAASC,GAAC,MAAI;AAAC,UAAG,CAACA,MAAG,CAACD,GAAE,YAAY;AAAO,YAAME,KAAEF,GAAE,qBAAqBJ,IAAED,EAAC;AAAE,QAAEO,EAAC,KAAGA,KAAEH,OAAIA,KAAEG,IAAEL,KAAEG;AAAA,IAAE,CAAE,GAAEH;AAAA,EAAC;AAAA,EAAC,6BAA6BD,IAAE;AAAC,SAAK,0BAA0BA,EAAC,IAAE,KAAK,mBAAmBA,EAAC,IAAE,KAAK,mBAAmBA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,IAAAA,GAAE,aAAWA,GAAE,YAAY,UAAQA,GAAE,YAAY,OAAO,KAAK,iBAAiB,GAAEA,GAAE,WAAS;AAAA,EAAG;AAAA,EAAC,mBAAmBA,IAAE;AAAC,QAAG,CAACA,GAAE,SAAS;AAAO,UAAME,KAAEF,GAAE;AAAY,IAAAE,GAAE,WAAS,OAAGA,GAAE,WAAS,OAAGA,GAAE,WAAS,OAAGA,GAAE,WAAS,OAAGA,GAAE,UAAQA,GAAE,OAAO,KAAK,iBAAiB,GAAEF,GAAE,WAAS;AAAA,EAAE;AAAA,EAAC,0BAA0BA,IAAE;AAAC,WAAOA,GAAE,wBAAsBD,GAAE,WAAS,KAAK,kBAAgBC,GAAE,wBAAsBD,GAAE,mBAAiBC,GAAE,wBAAsBD,GAAE;AAAA,EAAoB;AAAC;;;ACA3uD,IAAIQ,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,IAAIC,MAAE,KAAK,gCAA8B,MAAG,KAAK,yBAAuB,OAAG,KAAK,yBAAuB,OAAG,KAAK,0BAAwB,MAAK,KAAK,UAAQ,OAAG,KAAK,2BAAyB,MAAG,KAAK,iBAAe,oBAAI,IAAI,CAAC,CAACC,GAAE,SAAQ,IAAE,GAAE,CAACA,GAAE,MAAK,IAAE,CAAC,CAAC,GAAE,KAAK,4BAA0B,EAAE;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,QAAM,KAAK,QAAM,KAAK,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,IAAI,QAAQF,IAAE;AAAC,SAAK,KAAK,SAAS,MAAIA,OAAI,KAAK,KAAK,WAAUA,EAAC,GAAE,KAAK,aAAa;AAAA,EAAE;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,gBAAgBE,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,SAASF,IAAE;AAAC,SAAK,gBAAgBE,GAAE,MAAKF,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,IAAI,yBAAwB;AAAC,WAAO,KAAK,0BAAwB,KAAK;AAAA,EAAsB;AAAA,EAAC,UAAS;AAAC,SAAK,aAAa,QAAQ,GAAE,KAAK,KAAK,QAAO,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,aAAa;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,MAAE,KAAK,IAAI,IAAE,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,6CAA6C,KAAG,KAAK,KAAK,MAAM,GAAE,KAAK,WAAW;AAAA,EAAE;AAAA,EAAC,aAAY;AAAC,SAAK,aAAa;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,SAAK,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,SAAK,kBAAkBA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEC,IAAE;AAAC,SAAK,eAAe,IAAID,IAAEC,EAAC,GAAE,KAAK,aAAa,iBAAe,KAAK,oBAAmB,KAAK,6BAA6B,GAAED,OAAIE,GAAE,QAAM,KAAK,aAAa,UAAU,GAAE,KAAK,iBAAiB,GAAE,KAAK,8BAA8B;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAE;AAAC,WAAO,KAAK,eAAe,IAAIA,EAAC,KAAG;AAAA,EAAE;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK,0BAA0B;AAAA,EAAO;AAAA,EAAC,gCAA+B;AAAA,EAAC;AAAA,EAAC,aAAY;AAAA,EAAC;AAAA,EAAC,eAAc;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAoB;AAAC,WAAO,KAAK,gBAAgBE,GAAE,IAAI,KAAG,KAAK,gBAAgBA,GAAE,OAAO;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,SAAK,WAAS,KAAK,0BAA0B,QAAQ,IAAI,GAAE,KAAK,KAAK,WAAU,IAAE;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,QAAG,KAAK;AAAY,UAAG,KAAK,QAAQ,MAAK,MAAM;AAAA,eAAU,KAAK,MAAM,GAAE,KAAK,OAAO,QAAO,MAAK,KAAK,KAAK,aAAW;AAAA;AAAA,EAAK;AAAA,EAAC,QAAO;AAAC,SAAK,6BAA6B,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,6BAA6B,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,+BAA8B;AAAC,SAAK,UAAQ,KAAK,aAAa,OAAO,IAAE,KAAK,aAAa,OAAO;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEH,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,iCAAgC,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,0BAAyB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,4BAA2B,MAAM,GAAEA,KAAE,EAAE,CAACI,GAAE,4CAA4C,CAAC,GAAEJ,EAAC;;;ACA38F,SAASK,GAAEC,IAAEC,IAAEC,KAAE,MAAK;AAAC,SAAO,EAAEA,EAAC,IAAE,CAACF,IAAEC,IAAEC,EAAC,IAAE,CAACF,IAAEC,EAAC;AAAC;AAAC,SAASE,GAAEH,IAAEC,IAAEC,KAAE,MAAK;AAAC,SAAO,EAAEA,EAAC,IAAE,EAAC,GAAEF,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAE,EAAC,GAAEF,IAAE,GAAEC,GAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYG,IAAE;AAAC,SAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,WAAO,EAAEA,GAAE,IAAK,CAAAA,OAAG,KAAK,WAAWA,EAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAM;AAAA,EAAE;AAAC;AAAC,IAAMC,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYD,IAAEJ,IAAEM,KAAE,MAAK;AAAC,UAAMN,EAAC,GAAE,KAAK,YAAUM,IAAE,KAAK,YAAUL,GAAE,GAAE,KAAK,eAAaA,GAAE,GAAE,KAAK,YAAUD,GAAEI,EAAC,GAAEJ,GAAE,KAAK,cAAa,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,aAAaI,IAAEJ,IAAE;AAAC,WAAOD,GAAEK,IAAEJ,IAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,WAAWI,IAAE;AAAC,WAAOD,GAAE,KAAK,UAAU,CAAC,IAAEC,GAAE,CAAC,IAAE,KAAK,UAAU,CAAC,IAAEA,GAAE,CAAC,IAAE,KAAK,UAAU,CAAC,GAAE,KAAK,UAAU,CAAC,IAAEA,GAAE,CAAC,IAAE,KAAK,UAAU,CAAC,IAAEA,GAAE,CAAC,IAAE,KAAK,UAAU,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,WAAOL,GAAE,KAAK,aAAa,CAAC,IAAEK,GAAE,IAAE,KAAK,aAAa,CAAC,IAAEA,GAAE,IAAE,KAAK,aAAa,CAAC,GAAE,KAAK,aAAa,CAAC,IAAEA,GAAE,IAAE,KAAK,aAAa,CAAC,IAAEA,GAAE,IAAE,KAAK,aAAa,CAAC,GAAE,KAAK,SAAS;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYA,IAAEJ,IAAE;AAAC,UAAMI,GAAE,gBAAgB,GAAE,KAAK,OAAKA,IAAE,KAAK,WAAS,MAAK,KAAK,MAAIG,GAAE,GAAE,KAAK,mBAAiBA,GAAE,GAAE,KAAK,sBAAoBA,GAAE,GAAE,KAAK,wBAAsBA,GAAE,GAAE,KAAK,oBAAkBA,GAAE,GAAE,KAAK,iBAAeA,GAAE,GAAE,KAAK,wBAAsBN,GAAE,GAAE,KAAK,wBAAsBA,GAAE,GAAE,KAAK,QAAM,GAAE,KAAK,QAAMG,GAAE,YAAW,KAAK,oBAAkBJ,IAAE,KAAK,WAASA,GAAE,OAAKA,GAAE,IAAE;AAAK,UAAMC,KAAEG,GAAE,MAAM,OAAO;AAAU,SAAK,KAAK,mBAAmB,eAAe,KAAK,mBAAkB,KAAK,GAAG,GAAE,KAAK,KAAK,mBAAmB,qBAAqB,KAAK,KAAIG,GAAE,GAAE,KAAK,mBAAmB,GAAE,KAAK,KAAK,mBAAmB,qBAAqB,KAAK,KAAIA,GAAE,GAAE,KAAK,gBAAgB,GAAE,KAAK,KAAK,mBAAmB,qBAAqB,KAAK,KAAIA,GAAE,GAAE,KAAK,qBAAqB;AAAE,UAAML,KAAEK,GAAE;AAAE,IAAAR,GAAEG,IAAE,KAAK,uBAAsB,EAAED,IAAE,KAAK,qBAAqB,CAAC,GAAEA,GAAE,KAAK,mBAAkBA,IAAEC,EAAC,GAAE,EAAE,KAAK,mBAAkB,KAAK,iBAAiB,GAAE,EAAE,KAAK,gBAAe,KAAK,uBAAsB,KAAK,iBAAiB,GAAE,EAAE,KAAK,uBAAsB,KAAK,mBAAkB,KAAK,mBAAmB,GAAE,EAAE,KAAK,uBAAsB,KAAK,qBAAqB;AAAA,EAAC;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAM,aAAW,KAAK,KAAK;AAAA,EAAW;AAAA,EAAC,aAAaE,IAAEJ,IAAE;AAAC,WAAOD,GAAEK,IAAEJ,IAAE,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAMC,KAAEM,GAAE;AAAE,SAAK,KAAK,mBAAmB,eAAe,IAAIC,GAAE,EAAC,GAAER,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiB,KAAK,iBAAgB,CAAC,GAAEC,EAAC,GAAE,EAAEA,IAAEA,IAAE,KAAK,qBAAqB;AAAE,UAAMC,KAAE,KAAK,KAAK,mBAAmB,iBAAiBD,IAAE,KAAK,KAAK,gBAAgB;AAAE,WAAO,EAAEC,EAAC,IAAEC,GAAED,GAAE,IAAE,KAAK,OAAMA,GAAE,IAAE,KAAK,KAAK,IAAE;AAAA,EAAI;AAAA,EAAC,WAAWF,IAAE;AAAC,UAAMC,KAAEM,GAAE;AAAE,SAAK,KAAK,mBAAmB,eAAe,IAAIC,GAAE,EAAC,GAAER,GAAE,IAAE,KAAK,OAAM,GAAEA,GAAE,IAAE,KAAK,OAAM,kBAAiB,KAAK,iBAAgB,CAAC,GAAEC,EAAC,GAAE,EAAEA,IAAEA,IAAE,KAAK,qBAAqB;AAAE,UAAMC,KAAE,KAAK,KAAK,mBAAmB,iBAAiBD,IAAE,KAAK,KAAK,gBAAgB;AAAE,WAAO,EAAEC,EAAC,IAAEH,GAAEG,GAAE,GAAEA,GAAE,GAAE,KAAK,QAAQ,IAAE;AAAA,EAAI;AAAC;AAAC,SAASO,GAAEL,IAAEJ,IAAE;AAAC,MAAG,SAAOI,GAAE,KAAK,QAAO,IAAIC,GAAED,GAAE,MAAM,WAAUA,GAAE,kBAAiBJ,GAAE,SAAO,IAAEA,GAAE,CAAC,IAAE,IAAI;AAAE,MAAG,SAAOI,GAAE,MAAK;AAAC,UAAMH,KAAED,GAAE,SAAO,IAAE,IAAIQ,GAAE,EAAC,GAAER,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBI,GAAE,iBAAgB,CAAC,IAAE,IAAII,GAAE,EAAC,GAAER,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBI,GAAE,iBAAgB,CAAC;AAAE,WAAO,IAAI,EAAEA,IAAEH,EAAC;AAAA,EAAC;AAAC,SAAO;AAAI;", "names": ["c", "e", "y", "t", "e", "a", "o", "s", "c", "x", "g", "n", "r", "E", "z", "i", "j", "l", "m", "f", "w", "v", "R", "p", "M", "P", "q", "D", "d", "m", "a", "c", "l", "f", "d", "j", "z", "k", "w", "C", "g", "E", "U", "n", "o", "e", "x", "P", "Z", "t", "p", "h", "S", "v", "b", "y", "i", "r", "s", "D", "M", "t", "e", "h", "t", "s", "r", "e", "n", "a", "D", "o", "i", "k", "d", "e", "c", "p", "s", "t", "i", "n", "l", "w", "g", "h", "a", "x", "r", "m", "o", "e", "t", "s", "i", "g", "M", "c", "d", "a", "i", "t", "e", "a", "o", "s", "r", "n", "p", "t", "e", "o", "a", "g", "r", "e", "s", "R", "t", "v", "o", "n", "w", "S"]}