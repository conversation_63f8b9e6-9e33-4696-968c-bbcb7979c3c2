{"version": 3, "sources": ["../../@arcgis/core/layers/ogc/dateUtils.js", "../../@arcgis/core/layers/ogc/wfsUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction e(e){return t(e)??n(e)}function n(e){const n=new Date(e).getTime();return Number.isNaN(n)?null:n}function t(e){const n=s.exec(e);if(!n?.groups)return null;const t=n.groups,u=+t.year,o=+t.month-1,r=+t.day,f=+(t.hours??\"0\"),i=+(t.minutes??\"0\"),d=+(t.seconds??\"0\");if(f>23)return null;if(i>59)return null;if(d>59)return null;const l=t.ms??\"0\",c=l?+l.padEnd(3,\"0\").substring(0,3):0;let a;if(t.isUTC)a=Date.UTC(u,o,r,f,i,d,c);else if(t.offsetSign){const e=+t.offsetHours,n=+t.offsetMinutes;a=6e4*(\"+\"===t.offsetSign?-1:1)*(60*e+n)+Date.UTC(u,o,r,f,i,d,c)}else a=new Date(u,o,r,f,i,d,c).getTime();return Number.isNaN(a)?null:a}const s=/^(?:(?<year>-?\\d{4,})-(?<month>\\d{2})-(?<day>\\d{2}))(?:T(?<hours>\\d{2}):(?<minutes>\\d{2}):(?<seconds>\\d{2})(?:\\.(?<ms>\\d+))?)?(?:(?<isUTC>Z)|(?:(?<offsetSign>\\+|-)(?<offsetHours>\\d{2}):(?<offsetMinutes>\\d{2})))?$/;export{e as parseDate,n as parseJSDate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../geometry.js\";import e from\"../../request.js\";import t from\"../../core/Error.js\";import{cache as r,find as n}from\"../../core/iteratorUtils.js\";import{isNone as a,isSome as o}from\"../../core/maybe.js\";import{eachAlways as s}from\"../../core/promiseUtils.js\";import{isHTTPSProtocol as i,hasSameOrigin as p,toHTTPS as u}from\"../../core/urlUtils.js\";import{initializeProjection as c,project as l}from\"../../geometry/projection.js\";import{WGS84 as m,equals as y}from\"../../geometry/support/spatialReferenceUtils.js\";import{featureGeometryTypeKebabDictionary as f}from\"../../geometry/support/typeUtils.js\";import{getGeometryType as d}from\"../graphics/sources/geojson/geojson.js\";import{parseDate as g}from\"./dateUtils.js\";import{visitXML as w,iterateXML as b}from\"./xmlUtils.js\";import h from\"../support/Field.js\";import T from\"../../geometry/SpatialReference.js\";import F from\"../../geometry/Extent.js\";const S=\"xlink:href\",x=\"2.0.0\",C=\"__esri_wfs_id__\",E=\"wfs-layer:getWFSLayerTypeInfo-error\",N=\"wfs-layer:empty-service\",P=\"wfs-layer:feature-type-not-found\",R=\"wfs-layer:geojson-not-supported\",j=\"wfs-layer:kvp-encoding-not-supported\",A=\"wfs-layer:malformed-json\",G=\"wfs-layer:unknown-geometry-type\",k=\"wfs-layer:unknown-field-type\",v=\"wfs-layer:unsupported-spatial-reference\",U=\"wfs-layer:unsupported-wfs-version\";async function D(t,r){const n=I((await e(t,{responseType:\"text\",query:{SERVICE:\"WFS\",REQUEST:\"GetCapabilities\",VERSION:x,...r?.customParameters},signal:r?.signal})).data);return $(t,n),n}function I(e){const t=te(e);ne(t),ae(t);const n=t.firstElementChild,a=r(M(n));return{operations:L(n),get featureTypes(){return Array.from(a())},readFeatureTypes:a}}const O=new Set([\"json\",\"application/json\",\"geojson\",\"application/json; subtype=geojson\"]);function L(e){let r=!1;const n={GetCapabilities:{url:\"\"},DescribeFeatureType:{url:\"\"},GetFeature:{url:\"\",outputFormat:null,supportsPagination:!1}};if(w(e,{OperationsMetadata:{Operation:e=>{switch(e.getAttribute(\"name\")){case\"GetCapabilities\":return{DCP:{HTTP:{Get:e=>{n.GetCapabilities.url=e.getAttribute(S)}}}};case\"DescribeFeatureType\":return{DCP:{HTTP:{Get:e=>{n.DescribeFeatureType.url=e.getAttribute(S)}}}};case\"GetFeature\":return{DCP:{HTTP:{Get:e=>{n.GetFeature.url=e.getAttribute(S)}}},Parameter:e=>{if(\"outputFormat\"===e.getAttribute(\"name\"))return{AllowedValues:{Value:e=>{const t=e.textContent;t&&O.has(t.toLowerCase())&&(n.GetFeature.outputFormat=t)}}}}}}},Constraint:e=>{switch(e.getAttribute(\"name\")){case\"KVPEncoding\":return{DefaultValue:e=>{r=\"true\"===e.textContent.toLowerCase()}};case\"ImplementsResultPaging\":return{DefaultValue:e=>{n.GetFeature.supportsPagination=\"true\"===e.textContent.toLowerCase()}}}}}}),!r)throw new t(j,\"WFS service doesn't support key/value pair (KVP) encoding\");if(a(n.GetFeature.outputFormat))throw new t(R,\"WFS service doesn't support GeoJSON output format\");return n}function $(e,t){i(e)&&(p(e,t.operations.DescribeFeatureType.url,!0)&&(t.operations.DescribeFeatureType.url=u(t.operations.DescribeFeatureType.url)),p(e,t.operations.GetFeature.url,!0)&&(t.operations.GetFeature.url=u(t.operations.GetFeature.url)))}function M(e){return b(e,{FeatureTypeList:{FeatureType:e=>{const t={typeName:\"undefined:undefined\",name:\"\",title:\"\",description:\"\",extent:null,namespacePrefix:\"\",namespaceUri:\"\",supportedSpatialReferences:[]},r=new Set([4326]),n=e=>{const t=parseInt(e.textContent?.match(/(?<wkid>\\d+$)/i)?.groups?.wkid??\"\",10);Number.isNaN(t)||r.add(t)};return w(e,{Name:e=>{const{name:r,prefix:n}=re(e.textContent);t.typeName=`${n}:${r}`,t.name=r,t.namespacePrefix=n,t.namespaceUri=e.lookupNamespaceURI(n)},Abstract:e=>{t.description=e.textContent},Title:e=>{t.title=e.textContent},WGS84BoundingBox:e=>{t.extent=V(e)},DefaultSRS:n,DefaultCRS:n,OtherSRS:n,OtherCRS:n}),t.title||(t.title=t.name),t.supportedSpatialReferences.push(...r),t}}})}function V(e){let t,r,n,a;for(const o of e.children)switch(o.localName){case\"LowerCorner\":[t,r]=o.textContent.split(\" \").map((e=>Number.parseFloat(e)));break;case\"UpperCorner\":[n,a]=o.textContent.split(\" \").map((e=>Number.parseFloat(e)))}return{xmin:t,ymin:r,xmax:n,ymax:a,spatialReference:m}}function W(e,t,r){return n(e,(e=>r?e.name===t&&e.namespaceUri===r:e.typeName===t||e.name===t))}async function X(e,t,r,n={}){const{featureType:a,extent:o}=await Y(e,t,r,n),{fields:s,geometryType:i,swapXY:p,objectIdField:u,geometryField:c}=await q(e,a.typeName,n);return{url:e.operations.GetCapabilities.url,name:a.name,namespaceUri:a.namespaceUri,fields:s,geometryField:c,geometryType:i,objectIdField:u,spatialReference:n.spatialReference??T.WGS84,extent:o,swapXY:p,wfsCapabilities:e,customParameters:n.customParameters}}async function Y(e,r,n,o={}){const{spatialReference:s=T.WGS84}=o,i=e.readFeatureTypes(),p=r?W(i,r,n):i.next().value;if(a(p))throw r?new t(P,`The type '${r}' could not be found in the service`):new t(N,\"The service is empty\");let u=new F({...p.extent,spatialReference:s});if(!y(s,m))try{await c(m,s,void 0,o),u=l(u,m)}catch{throw new t(v,\"Projection not supported\")}return{extent:u,spatialReference:s,featureType:p}}async function q(e,r,n={}){const[o,i]=await s([J(e.operations.DescribeFeatureType.url,r,n),_(e,r,n)]);if(o.error||i.error)throw new t(E,`An error occurred while getting info about the feature type '${r}'`,{error:o.error||i.error});const{fields:p,errors:u}=o.value??{},c=o.value?.geometryType||i.value?.geometryType,l=i.value?.swapXY??!1;if(a(c))throw new t(G,`The geometry type could not be determined for type '${r}`,{typeName:r,geometryType:c,fields:p,errors:u});return{...z(p??[]),geometryType:c,swapXY:l}}function z(e){const t=e.find((e=>\"geometry\"===e.type));let r=e.find((e=>\"oid\"===e.type));return e=e.filter((e=>\"geometry\"!==e.type)),r||(r=new h({name:C,type:\"oid\",alias:C}),e.unshift(r)),{geometryField:t?.name??null,objectIdField:r.name,fields:e}}async function _(t,r,n={}){let a,o=!1;const[s,i]=await Promise.all([K(t.operations.GetFeature.url,r,t.operations.GetFeature.outputFormat,{...n,count:1}),e(t.operations.GetFeature.url,{responseType:\"text\",query:Z(r,void 0,{...n,count:1}),signal:n?.signal})]),p=\"FeatureCollection\"===s.type&&s.features[0]?.geometry;if(p){let e;switch(a=f.fromJSON(d(p.type)),p.type){case\"Point\":e=p.coordinates;break;case\"LineString\":case\"MultiPoint\":e=p.coordinates[0];break;case\"MultiLineString\":case\"Polygon\":e=p.coordinates[0][0];break;case\"MultiPolygon\":e=p.coordinates[0][0][0]}const t=/<[^>]*pos[^>]*> *(-?\\d+(?:\\.\\d+)?) (-?\\d+(?:\\.\\d+)?)/.exec(i.data);if(t){const r=e[0].toFixed(3),n=e[1].toFixed(3),a=parseFloat(t[1]).toFixed(3);r===parseFloat(t[2]).toFixed(3)&&n===a&&(o=!0)}}return{geometryType:a,swapXY:o}}async function J(t,r,n){return Q(r,(await e(t,{responseType:\"text\",query:{SERVICE:\"WFS\",REQUEST:\"DescribeFeatureType\",VERSION:x,TYPENAME:r,...n?.customParameters},signal:n?.signal})).data)}function Q(e,r){const{name:a}=re(e),s=te(r);ae(s);const i=n(b(s.firstElementChild,{element:e=>({name:e.getAttribute(\"name\"),typeName:re(e.getAttribute(\"type\")).name})}),(({name:e})=>e===a));if(o(i)){const e=n(b(s.firstElementChild,{complexType:e=>e}),(e=>e.getAttribute(\"name\")===i.typeName));if(o(e))return B(e)}throw new t(P,`Type '${e}' not found in document`,{document:(new XMLSerializer).serializeToString(s)})}const H=new Set([\"objectid\",\"fid\"]);function B(e){const r=[],n=[];let a;const o=b(e,{complexContent:{extension:{sequence:{element:e=>e}}}});for(const s of o){const o=s.getAttribute(\"name\");if(!o)continue;let i,p;if(s.hasAttribute(\"type\")?i=re(s.getAttribute(\"type\")).name:w(s,{simpleType:{restriction:e=>(i=re(e.getAttribute(\"base\")).name,{maxLength:e=>{p=+e.getAttribute(\"value\")}})}}),!i)continue;const u=\"true\"===s.getAttribute(\"nillable\");let c=!1;switch(i.toLowerCase()){case\"integer\":case\"nonpositiveinteger\":case\"negativeinteger\":case\"long\":case\"int\":case\"short\":case\"byte\":case\"nonnegativeinteger\":case\"unsignedlong\":case\"unsignedint\":case\"unsignedshort\":case\"unsignedbyte\":case\"positiveinteger\":n.push(new h({name:o,alias:o,type:\"integer\",nullable:u}));break;case\"float\":case\"double\":case\"decimal\":n.push(new h({name:o,alias:o,type:\"double\",nullable:u}));break;case\"boolean\":case\"string\":case\"gyearmonth\":case\"gyear\":case\"gmonthday\":case\"gday\":case\"gmonth\":case\"anyuri\":case\"qname\":case\"notation\":case\"normalizedstring\":case\"token\":case\"language\":case\"idrefs\":case\"entities\":case\"nmtoken\":case\"nmtokens\":case\"name\":case\"ncname\":case\"id\":case\"idref\":case\"entity\":case\"duration\":case\"time\":n.push(new h({name:o,alias:o,type:\"string\",nullable:u,length:p??255}));break;case\"datetime\":case\"date\":n.push(new h({name:o,alias:o,type:\"date\",nullable:u,length:p??36}));break;case\"pointpropertytype\":a=\"point\",c=!0;break;case\"multipointpropertytype\":a=\"multipoint\",c=!0;break;case\"curvepropertytype\":case\"multicurvepropertytype\":case\"multilinestringpropertytype\":a=\"polyline\",c=!0;break;case\"surfacepropertytype\":case\"multisurfacepropertytype\":case\"multipolygonpropertytype\":a=\"polygon\",c=!0;break;case\"geometrypropertytype\":case\"multigeometrypropertytype\":c=!0,r.push(new t(G,`geometry type '${i}' is not supported`,{type:(new XMLSerializer).serializeToString(e)}));break;default:r.push(new t(k,`Unknown field type '${i}'`,{type:(new XMLSerializer).serializeToString(e)}))}c&&n.push(new h({name:o,alias:o,type:\"geometry\",nullable:u}))}for(const t of n)if(\"integer\"===t.type&&!t.nullable&&H.has(t.name.toLowerCase())){t.type=\"oid\";break}return{geometryType:a,fields:n,errors:r}}async function K(r,n,a,o){let{data:s}=await e(r,{responseType:\"text\",query:Z(n,a,o),signal:o?.signal});s=s.replace(/\": +(-?\\d+),(\\d+)(,)?/g,'\": $1.$2$3');try{if(o?.dateFields?.length){const e=new Set(o.dateFields);return JSON.parse(s,((t,r)=>e.has(t)?g(r):r))}return JSON.parse(s)}catch(i){throw new t(A,\"Error while parsing the response\",{response:s,error:i})}}function Z(e,t,r){return{SERVICE:\"WFS\",REQUEST:\"GetFeature\",VERSION:x,TYPENAMES:e,OUTPUTFORMAT:t,SRSNAME:\"EPSG:4326\",STARTINDEX:r?.startIndex,COUNT:r?.count,...r?.customParameters}}async function ee(t,r,n){const a=te((await e(t,{responseType:\"text\",query:{SERVICE:\"WFS\",REQUEST:\"GetFeature\",VERSION:x,TYPENAMES:r,RESULTTYPE:\"hits\",...n?.customParameters},signal:n?.signal})).data);ae(a);const o=Number.parseFloat(a.firstElementChild.getAttribute(\"numberMatched\"));return Number.isNaN(o)?0:o}function te(e){return(new DOMParser).parseFromString(e.trim(),\"text/xml\")}function re(e){const[t,r]=e.split(\":\");return{prefix:r?t:\"\",name:r??t}}function ne(e){const r=e.firstElementChild?.getAttribute(\"version\");if(r&&r!==x)throw new t(U,`Unsupported WFS version ${r}. Supported version: ${x}`)}function ae(e){let r=\"\",n=\"\";if(w(e.firstElementChild,{Exception:e=>(r=e.getAttribute(\"exceptionCode\"),{ExceptionText:e=>{n=e.textContent}})}),r)throw new t(`wfs-layer:${r}`,n)}export{C as WFS_OID_FIELD_NAME,J as describeFeatureType,W as findFeatureType,D as getCapabilities,K as getFeature,ee as getFeatureCount,Y as getFeatureTypeInfo,X as getWFSLayerInfo,Q as parseDescribeFeatureTypeResponse,I as parseGetCapabilitiesResponse,z as prepareWFSLayerFields};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,SAAS,EAAEA,IAAE;AAAC,SAAOC,GAAED,EAAC,KAAGE,GAAEF,EAAC;AAAC;AAAC,SAASE,GAAEF,IAAE;AAAC,QAAME,KAAE,IAAI,KAAKF,EAAC,EAAE,QAAQ;AAAE,SAAO,OAAO,MAAME,EAAC,IAAE,OAAKA;AAAC;AAAC,SAASD,GAAED,IAAE;AAAC,QAAME,KAAEC,GAAE,KAAKH,EAAC;AAAE,MAAG,EAACE,MAAA,gBAAAA,GAAG,QAAO,QAAO;AAAK,QAAMD,KAAEC,GAAE,QAAO,IAAE,CAACD,GAAE,MAAKG,KAAE,CAACH,GAAE,QAAM,GAAEI,KAAE,CAACJ,GAAE,KAAIK,KAAE,EAAEL,GAAE,SAAO,MAAK,IAAE,EAAEA,GAAE,WAAS,MAAK,IAAE,EAAEA,GAAE,WAAS;AAAK,MAAGK,KAAE,GAAG,QAAO;AAAK,MAAG,IAAE,GAAG,QAAO;AAAK,MAAG,IAAE,GAAG,QAAO;AAAK,QAAM,IAAEL,GAAE,MAAI,KAAIM,KAAE,IAAE,CAAC,EAAE,OAAO,GAAE,GAAG,EAAE,UAAU,GAAE,CAAC,IAAE;AAAE,MAAI;AAAE,MAAGN,GAAE,MAAM,KAAE,KAAK,IAAI,GAAEG,IAAEC,IAAEC,IAAE,GAAE,GAAEC,EAAC;AAAA,WAAUN,GAAE,YAAW;AAAC,UAAMD,KAAE,CAACC,GAAE,aAAYC,KAAE,CAACD,GAAE;AAAc,QAAE,OAAK,QAAMA,GAAE,aAAW,KAAG,MAAI,KAAGD,KAAEE,MAAG,KAAK,IAAI,GAAEE,IAAEC,IAAEC,IAAE,GAAE,GAAEC,EAAC;AAAA,EAAC,MAAM,KAAE,IAAI,KAAK,GAAEH,IAAEC,IAAEC,IAAE,GAAE,GAAEC,EAAC,EAAE,QAAQ;AAAE,SAAO,OAAO,MAAM,CAAC,IAAE,OAAK;AAAC;AAAC,IAAMJ,KAAE;;;ACA+Q,IAAM,IAAE;AAAR,IAAqB,IAAE;AAAvB,IAA+B,IAAE;AAAjC,IAAmDK,KAAE;AAArD,IAA2F,IAAE;AAA7F,IAAuH,IAAE;AAAzH,IAA4J,IAAE;AAA9J,IAAgM,IAAE;AAAlM,IAAyO,IAAE;AAA3O,IAAsQ,IAAE;AAAxQ,IAA0S,IAAE;AAA5S,IAA2U,IAAE;AAA7U,IAAuXC,KAAE;AAAoC,eAAe,EAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAG,MAAM,EAAEF,IAAE,EAAC,cAAa,QAAO,OAAM,EAAC,SAAQ,OAAM,SAAQ,mBAAkB,SAAQ,GAAE,GAAGC,MAAA,gBAAAA,GAAG,iBAAgB,GAAE,QAAOA,MAAA,gBAAAA,GAAG,OAAM,CAAC,GAAG,IAAI;AAAE,SAAO,EAAED,IAAEE,EAAC,GAAEA;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,QAAMH,KAAE,GAAGG,EAAC;AAAE,KAAGH,EAAC,GAAE,GAAGA,EAAC;AAAE,QAAME,KAAEF,GAAE,mBAAkB,IAAE,EAAEI,GAAEF,EAAC,CAAC;AAAE,SAAM,EAAC,YAAW,EAAEA,EAAC,GAAE,IAAI,eAAc;AAAC,WAAO,MAAM,KAAK,EAAE,CAAC;AAAA,EAAC,GAAE,kBAAiB,EAAC;AAAC;AAAC,IAAM,IAAE,oBAAI,IAAI,CAAC,QAAO,oBAAmB,WAAU,mCAAmC,CAAC;AAAE,SAAS,EAAEC,IAAE;AAAC,MAAIF,KAAE;AAAG,QAAMC,KAAE,EAAC,iBAAgB,EAAC,KAAI,GAAE,GAAE,qBAAoB,EAAC,KAAI,GAAE,GAAE,YAAW,EAAC,KAAI,IAAG,cAAa,MAAK,oBAAmB,MAAE,EAAC;AAAE,MAAGG,GAAEF,IAAE,EAAC,oBAAmB,EAAC,WAAU,CAAAA,OAAG;AAAC,YAAOA,GAAE,aAAa,MAAM,GAAE;AAAA,MAAC,KAAI;AAAkB,eAAM,EAAC,KAAI,EAAC,MAAK,EAAC,KAAI,CAAAA,OAAG;AAAC,UAAAD,GAAE,gBAAgB,MAAIC,GAAE,aAAa,CAAC;AAAA,QAAC,EAAC,EAAC,EAAC;AAAA,MAAE,KAAI;AAAsB,eAAM,EAAC,KAAI,EAAC,MAAK,EAAC,KAAI,CAAAA,OAAG;AAAC,UAAAD,GAAE,oBAAoB,MAAIC,GAAE,aAAa,CAAC;AAAA,QAAC,EAAC,EAAC,EAAC;AAAA,MAAE,KAAI;AAAa,eAAM,EAAC,KAAI,EAAC,MAAK,EAAC,KAAI,CAAAA,OAAG;AAAC,UAAAD,GAAE,WAAW,MAAIC,GAAE,aAAa,CAAC;AAAA,QAAC,EAAC,EAAC,GAAE,WAAU,CAAAA,OAAG;AAAC,cAAG,mBAAiBA,GAAE,aAAa,MAAM,EAAE,QAAM,EAAC,eAAc,EAAC,OAAM,CAAAA,OAAG;AAAC,kBAAMH,KAAEG,GAAE;AAAY,YAAAH,MAAG,EAAE,IAAIA,GAAE,YAAY,CAAC,MAAIE,GAAE,WAAW,eAAaF;AAAA,UAAE,EAAC,EAAC;AAAA,QAAC,EAAC;AAAA,IAAC;AAAA,EAAC,GAAE,YAAW,CAAAG,OAAG;AAAC,YAAOA,GAAE,aAAa,MAAM,GAAE;AAAA,MAAC,KAAI;AAAc,eAAM,EAAC,cAAa,CAAAA,OAAG;AAAC,UAAAF,KAAE,WAASE,GAAE,YAAY,YAAY;AAAA,QAAC,EAAC;AAAA,MAAE,KAAI;AAAyB,eAAM,EAAC,cAAa,CAAAA,OAAG;AAAC,UAAAD,GAAE,WAAW,qBAAmB,WAASC,GAAE,YAAY,YAAY;AAAA,QAAC,EAAC;AAAA,IAAC;AAAA,EAAC,EAAC,EAAC,CAAC,GAAE,CAACF,GAAE,OAAM,IAAI,EAAE,GAAE,2DAA2D;AAAE,MAAG,EAAEC,GAAE,WAAW,YAAY,EAAE,OAAM,IAAI,EAAE,GAAE,mDAAmD;AAAE,SAAOA;AAAC;AAAC,SAAS,EAAEC,IAAEH,IAAE;AAAC,KAAEG,EAAC,MAAI,EAAEA,IAAEH,GAAE,WAAW,oBAAoB,KAAI,IAAE,MAAIA,GAAE,WAAW,oBAAoB,MAAI,GAAEA,GAAE,WAAW,oBAAoB,GAAG,IAAG,EAAEG,IAAEH,GAAE,WAAW,WAAW,KAAI,IAAE,MAAIA,GAAE,WAAW,WAAW,MAAI,GAAEA,GAAE,WAAW,WAAW,GAAG;AAAG;AAAC,SAASI,GAAED,IAAE;AAAC,SAAOD,GAAEC,IAAE,EAAC,iBAAgB,EAAC,aAAY,CAAAA,OAAG;AAAC,UAAMH,KAAE,EAAC,UAAS,uBAAsB,MAAK,IAAG,OAAM,IAAG,aAAY,IAAG,QAAO,MAAK,iBAAgB,IAAG,cAAa,IAAG,4BAA2B,CAAC,EAAC,GAAEC,KAAE,oBAAI,IAAI,CAAC,IAAI,CAAC,GAAEC,KAAE,CAAAC,OAAG;AAJ3xG;AAI4xG,YAAMH,KAAE,WAAS,iBAAAG,GAAE,gBAAF,mBAAe,MAAM,sBAArB,mBAAwC,WAAxC,mBAAgD,SAAM,IAAG,EAAE;AAAE,aAAO,MAAMH,EAAC,KAAGC,GAAE,IAAID,EAAC;AAAA,IAAC;AAAE,WAAOK,GAAEF,IAAE,EAAC,MAAK,CAAAA,OAAG;AAAC,YAAK,EAAC,MAAKF,IAAE,QAAOC,GAAC,IAAE,GAAGC,GAAE,WAAW;AAAE,MAAAH,GAAE,WAAS,GAAGE,EAAC,IAAID,EAAC,IAAGD,GAAE,OAAKC,IAAED,GAAE,kBAAgBE,IAAEF,GAAE,eAAaG,GAAE,mBAAmBD,EAAC;AAAA,IAAC,GAAE,UAAS,CAAAC,OAAG;AAAC,MAAAH,GAAE,cAAYG,GAAE;AAAA,IAAW,GAAE,OAAM,CAAAA,OAAG;AAAC,MAAAH,GAAE,QAAMG,GAAE;AAAA,IAAW,GAAE,kBAAiB,CAAAA,OAAG;AAAC,MAAAH,GAAE,SAAO,EAAEG,EAAC;AAAA,IAAC,GAAE,YAAWD,IAAE,YAAWA,IAAE,UAASA,IAAE,UAASA,GAAC,CAAC,GAAEF,GAAE,UAAQA,GAAE,QAAMA,GAAE,OAAMA,GAAE,2BAA2B,KAAK,GAAGC,EAAC,GAAED;AAAA,EAAC,EAAC,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEG,IAAE;AAAC,MAAIH,IAAEC,IAAEC,IAAE;AAAE,aAAUG,MAAKF,GAAE,SAAS,SAAOE,GAAE,WAAU;AAAA,IAAC,KAAI;AAAc,OAACL,IAAEC,EAAC,IAAEI,GAAE,YAAY,MAAM,GAAG,EAAE,IAAK,CAAAF,OAAG,OAAO,WAAWA,EAAC,CAAE;AAAE;AAAA,IAAM,KAAI;AAAc,OAACD,IAAE,CAAC,IAAEG,GAAE,YAAY,MAAM,GAAG,EAAE,IAAK,CAAAF,OAAG,OAAO,WAAWA,EAAC,CAAE;AAAA,EAAC;AAAC,SAAM,EAAC,MAAKH,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAK,GAAE,kBAAiB,EAAC;AAAC;AAAC,SAAS,EAAEC,IAAEH,IAAEC,IAAE;AAAC,SAAO,EAAEE,IAAG,CAAAA,OAAGF,KAAEE,GAAE,SAAOH,MAAGG,GAAE,iBAAeF,KAAEE,GAAE,aAAWH,MAAGG,GAAE,SAAOH,EAAE;AAAC;AAAC,eAAe,EAAEG,IAAEH,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,QAAK,EAAC,aAAY,GAAE,QAAOG,GAAC,IAAE,MAAM,EAAEF,IAAEH,IAAEC,IAAEC,EAAC,GAAE,EAAC,QAAOI,IAAE,cAAa,GAAE,QAAO,GAAE,eAAc,GAAE,eAAcC,GAAC,IAAE,MAAM,EAAEJ,IAAE,EAAE,UAASD,EAAC;AAAE,SAAM,EAAC,KAAIC,GAAE,WAAW,gBAAgB,KAAI,MAAK,EAAE,MAAK,cAAa,EAAE,cAAa,QAAOG,IAAE,eAAcC,IAAE,cAAa,GAAE,eAAc,GAAE,kBAAiBL,GAAE,oBAAkB,EAAE,OAAM,QAAOG,IAAE,QAAO,GAAE,iBAAgBF,IAAE,kBAAiBD,GAAE,iBAAgB;AAAC;AAAC,eAAe,EAAEC,IAAEF,IAAEC,IAAEG,KAAE,CAAC,GAAE;AAAC,QAAK,EAAC,kBAAiBC,KAAE,EAAE,MAAK,IAAED,IAAE,IAAEF,GAAE,iBAAiB,GAAE,IAAEF,KAAE,EAAE,GAAEA,IAAEC,EAAC,IAAE,EAAE,KAAK,EAAE;AAAM,MAAG,EAAE,CAAC,EAAE,OAAMD,KAAE,IAAI,EAAE,GAAE,aAAaA,EAAC,qCAAqC,IAAE,IAAI,EAAE,GAAE,sBAAsB;AAAE,MAAI,IAAE,IAAI,EAAE,EAAC,GAAG,EAAE,QAAO,kBAAiBK,GAAC,CAAC;AAAE,MAAG,CAACR,GAAEQ,IAAE,CAAC,EAAE,KAAG;AAAC,UAAM,GAAE,GAAEA,IAAE,QAAOD,EAAC,GAAE,IAAE,GAAE,GAAE,CAAC;AAAA,EAAC,QAAM;AAAC,UAAM,IAAI,EAAE,GAAE,0BAA0B;AAAA,EAAC;AAAC,SAAM,EAAC,QAAO,GAAE,kBAAiBC,IAAE,aAAY,EAAC;AAAC;AAAC,eAAe,EAAEH,IAAEF,IAAEC,KAAE,CAAC,GAAE;AAJ9+J;AAI++J,QAAK,CAACG,IAAE,CAAC,IAAE,MAAM,EAAE,CAAC,EAAEF,GAAE,WAAW,oBAAoB,KAAIF,IAAEC,EAAC,GAAE,EAAEC,IAAEF,IAAEC,EAAC,CAAC,CAAC;AAAE,MAAGG,GAAE,SAAO,EAAE,MAAM,OAAM,IAAI,EAAEP,IAAE,gEAAgEG,EAAC,KAAI,EAAC,OAAMI,GAAE,SAAO,EAAE,MAAK,CAAC;AAAE,QAAK,EAAC,QAAO,GAAE,QAAO,EAAC,IAAEA,GAAE,SAAO,CAAC,GAAEE,OAAE,KAAAF,GAAE,UAAF,mBAAS,mBAAc,OAAE,UAAF,mBAAS,eAAa,MAAE,OAAE,UAAF,mBAAS,WAAQ;AAAG,MAAG,EAAEE,EAAC,EAAE,OAAM,IAAI,EAAE,GAAE,uDAAuDN,EAAC,IAAG,EAAC,UAASA,IAAE,cAAaM,IAAE,QAAO,GAAE,QAAO,EAAC,CAAC;AAAE,SAAM,EAAC,GAAG,EAAE,KAAG,CAAC,CAAC,GAAE,cAAaA,IAAE,QAAO,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAE;AAAC,QAAMH,KAAEG,GAAE,KAAM,CAAAA,OAAG,eAAaA,GAAE,IAAK;AAAE,MAAIF,KAAEE,GAAE,KAAM,CAAAA,OAAG,UAAQA,GAAE,IAAK;AAAE,SAAOA,KAAEA,GAAE,OAAQ,CAAAA,OAAG,eAAaA,GAAE,IAAK,GAAEF,OAAIA,KAAE,IAAI,EAAE,EAAC,MAAK,GAAE,MAAK,OAAM,OAAM,EAAC,CAAC,GAAEE,GAAE,QAAQF,EAAC,IAAG,EAAC,gBAAcD,MAAA,gBAAAA,GAAG,SAAM,MAAK,eAAcC,GAAE,MAAK,QAAOE,GAAC;AAAC;AAAC,eAAe,EAAEH,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAJnuL;AAIouL,MAAI,GAAEG,KAAE;AAAG,QAAK,CAACC,IAAE,CAAC,IAAE,MAAM,QAAQ,IAAI,CAAC,EAAEN,GAAE,WAAW,WAAW,KAAIC,IAAED,GAAE,WAAW,WAAW,cAAa,EAAC,GAAGE,IAAE,OAAM,EAAC,CAAC,GAAE,EAAEF,GAAE,WAAW,WAAW,KAAI,EAAC,cAAa,QAAO,OAAM,EAAEC,IAAE,QAAO,EAAC,GAAGC,IAAE,OAAM,EAAC,CAAC,GAAE,QAAOA,MAAA,gBAAAA,GAAG,OAAM,CAAC,CAAC,CAAC,GAAE,IAAE,wBAAsBI,GAAE,UAAM,KAAAA,GAAE,SAAS,CAAC,MAAZ,mBAAe;AAAS,MAAG,GAAE;AAAC,QAAIH;AAAE,YAAO,IAAEE,GAAE,SAASC,GAAE,EAAE,IAAI,CAAC,GAAE,EAAE,MAAK;AAAA,MAAC,KAAI;AAAQ,QAAAH,KAAE,EAAE;AAAY;AAAA,MAAM,KAAI;AAAA,MAAa,KAAI;AAAa,QAAAA,KAAE,EAAE,YAAY,CAAC;AAAE;AAAA,MAAM,KAAI;AAAA,MAAkB,KAAI;AAAU,QAAAA,KAAE,EAAE,YAAY,CAAC,EAAE,CAAC;AAAE;AAAA,MAAM,KAAI;AAAe,QAAAA,KAAE,EAAE,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,IAAC;AAAC,UAAMH,KAAE,uDAAuD,KAAK,EAAE,IAAI;AAAE,QAAGA,IAAE;AAAC,YAAMC,KAAEE,GAAE,CAAC,EAAE,QAAQ,CAAC,GAAED,KAAEC,GAAE,CAAC,EAAE,QAAQ,CAAC,GAAEK,KAAE,WAAWR,GAAE,CAAC,CAAC,EAAE,QAAQ,CAAC;AAAE,MAAAC,OAAI,WAAWD,GAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAGE,OAAIM,OAAIH,KAAE;AAAA,IAAG;AAAA,EAAC;AAAC,SAAM,EAAC,cAAa,GAAE,QAAOA,GAAC;AAAC;AAAC,eAAe,EAAEL,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAED,KAAG,MAAM,EAAED,IAAE,EAAC,cAAa,QAAO,OAAM,EAAC,SAAQ,OAAM,SAAQ,uBAAsB,SAAQ,GAAE,UAASC,IAAE,GAAGC,MAAA,gBAAAA,GAAG,iBAAgB,GAAE,QAAOA,MAAA,gBAAAA,GAAG,OAAM,CAAC,GAAG,IAAI;AAAC;AAAC,SAAS,EAAEC,IAAEF,IAAE;AAAC,QAAK,EAAC,MAAK,EAAC,IAAE,GAAGE,EAAC,GAAEG,KAAE,GAAGL,EAAC;AAAE,KAAGK,EAAC;AAAE,QAAM,IAAE,EAAEJ,GAAEI,GAAE,mBAAkB,EAAC,SAAQ,CAAAH,QAAI,EAAC,MAAKA,GAAE,aAAa,MAAM,GAAE,UAAS,GAAGA,GAAE,aAAa,MAAM,CAAC,EAAE,KAAI,GAAE,CAAC,GAAG,CAAC,EAAC,MAAKA,GAAC,MAAIA,OAAI,CAAE;AAAE,MAAG,EAAE,CAAC,GAAE;AAAC,UAAMA,KAAE,EAAED,GAAEI,GAAE,mBAAkB,EAAC,aAAY,CAAAH,OAAGA,GAAC,CAAC,GAAG,CAAAA,OAAGA,GAAE,aAAa,MAAM,MAAI,EAAE,QAAS;AAAE,QAAG,EAAEA,EAAC,EAAE,QAAO,EAAEA,EAAC;AAAA,EAAC;AAAC,QAAM,IAAI,EAAE,GAAE,SAASA,EAAC,2BAA0B,EAAC,UAAU,IAAI,gBAAe,kBAAkBG,EAAC,EAAC,CAAC;AAAC;AAAC,IAAM,IAAE,oBAAI,IAAI,CAAC,YAAW,KAAK,CAAC;AAAE,SAAS,EAAEH,IAAE;AAAC,QAAMF,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,MAAI;AAAE,QAAMG,KAAEH,GAAEC,IAAE,EAAC,gBAAe,EAAC,WAAU,EAAC,UAAS,EAAC,SAAQ,CAAAA,OAAGA,GAAC,EAAC,EAAC,EAAC,CAAC;AAAE,aAAUG,MAAKD,IAAE;AAAC,UAAMA,KAAEC,GAAE,aAAa,MAAM;AAAE,QAAG,CAACD,GAAE;AAAS,QAAI,GAAE;AAAE,QAAGC,GAAE,aAAa,MAAM,IAAE,IAAE,GAAGA,GAAE,aAAa,MAAM,CAAC,EAAE,OAAKD,GAAEC,IAAE,EAAC,YAAW,EAAC,aAAY,CAAAH,QAAI,IAAE,GAAGA,GAAE,aAAa,MAAM,CAAC,EAAE,MAAK,EAAC,WAAU,CAAAA,OAAG;AAAC,UAAE,CAACA,GAAE,aAAa,OAAO;AAAA,IAAC,EAAC,GAAE,EAAC,CAAC,GAAE,CAAC,EAAE;AAAS,UAAM,IAAE,WAASG,GAAE,aAAa,UAAU;AAAE,QAAIC,KAAE;AAAG,YAAO,EAAE,YAAY,GAAE;AAAA,MAAC,KAAI;AAAA,MAAU,KAAI;AAAA,MAAqB,KAAI;AAAA,MAAkB,KAAI;AAAA,MAAO,KAAI;AAAA,MAAM,KAAI;AAAA,MAAQ,KAAI;AAAA,MAAO,KAAI;AAAA,MAAqB,KAAI;AAAA,MAAe,KAAI;AAAA,MAAc,KAAI;AAAA,MAAgB,KAAI;AAAA,MAAe,KAAI;AAAkB,QAAAL,GAAE,KAAK,IAAI,EAAE,EAAC,MAAKG,IAAE,OAAMA,IAAE,MAAK,WAAU,UAAS,EAAC,CAAC,CAAC;AAAE;AAAA,MAAM,KAAI;AAAA,MAAQ,KAAI;AAAA,MAAS,KAAI;AAAU,QAAAH,GAAE,KAAK,IAAI,EAAE,EAAC,MAAKG,IAAE,OAAMA,IAAE,MAAK,UAAS,UAAS,EAAC,CAAC,CAAC;AAAE;AAAA,MAAM,KAAI;AAAA,MAAU,KAAI;AAAA,MAAS,KAAI;AAAA,MAAa,KAAI;AAAA,MAAQ,KAAI;AAAA,MAAY,KAAI;AAAA,MAAO,KAAI;AAAA,MAAS,KAAI;AAAA,MAAS,KAAI;AAAA,MAAQ,KAAI;AAAA,MAAW,KAAI;AAAA,MAAmB,KAAI;AAAA,MAAQ,KAAI;AAAA,MAAW,KAAI;AAAA,MAAS,KAAI;AAAA,MAAW,KAAI;AAAA,MAAU,KAAI;AAAA,MAAW,KAAI;AAAA,MAAO,KAAI;AAAA,MAAS,KAAI;AAAA,MAAK,KAAI;AAAA,MAAQ,KAAI;AAAA,MAAS,KAAI;AAAA,MAAW,KAAI;AAAO,QAAAH,GAAE,KAAK,IAAI,EAAE,EAAC,MAAKG,IAAE,OAAMA,IAAE,MAAK,UAAS,UAAS,GAAE,QAAO,KAAG,IAAG,CAAC,CAAC;AAAE;AAAA,MAAM,KAAI;AAAA,MAAW,KAAI;AAAO,QAAAH,GAAE,KAAK,IAAI,EAAE,EAAC,MAAKG,IAAE,OAAMA,IAAE,MAAK,QAAO,UAAS,GAAE,QAAO,KAAG,GAAE,CAAC,CAAC;AAAE;AAAA,MAAM,KAAI;AAAoB,YAAE,SAAQE,KAAE;AAAG;AAAA,MAAM,KAAI;AAAyB,YAAE,cAAaA,KAAE;AAAG;AAAA,MAAM,KAAI;AAAA,MAAoB,KAAI;AAAA,MAAyB,KAAI;AAA8B,YAAE,YAAWA,KAAE;AAAG;AAAA,MAAM,KAAI;AAAA,MAAsB,KAAI;AAAA,MAA2B,KAAI;AAA2B,YAAE,WAAUA,KAAE;AAAG;AAAA,MAAM,KAAI;AAAA,MAAuB,KAAI;AAA4B,QAAAA,KAAE,MAAGN,GAAE,KAAK,IAAI,EAAE,GAAE,kBAAkB,CAAC,sBAAqB,EAAC,MAAM,IAAI,gBAAe,kBAAkBE,EAAC,EAAC,CAAC,CAAC;AAAE;AAAA,MAAM;AAAQ,QAAAF,GAAE,KAAK,IAAI,EAAE,GAAE,uBAAuB,CAAC,KAAI,EAAC,MAAM,IAAI,gBAAe,kBAAkBE,EAAC,EAAC,CAAC,CAAC;AAAA,IAAC;AAAC,IAAAI,MAAGL,GAAE,KAAK,IAAI,EAAE,EAAC,MAAKG,IAAE,OAAMA,IAAE,MAAK,YAAW,UAAS,EAAC,CAAC,CAAC;AAAA,EAAC;AAAC,aAAUL,MAAKE,GAAE,KAAG,cAAYF,GAAE,QAAM,CAACA,GAAE,YAAU,EAAE,IAAIA,GAAE,KAAK,YAAY,CAAC,GAAE;AAAC,IAAAA,GAAE,OAAK;AAAM;AAAA,EAAK;AAAC,SAAM,EAAC,cAAa,GAAE,QAAOE,IAAE,QAAOD,GAAC;AAAC;AAAC,eAAe,EAAEA,IAAEC,IAAE,GAAEG,IAAE;AAJ/tS;AAIguS,MAAG,EAAC,MAAKC,GAAC,IAAE,MAAM,EAAEL,IAAE,EAAC,cAAa,QAAO,OAAM,EAAEC,IAAE,GAAEG,EAAC,GAAE,QAAOA,MAAA,gBAAAA,GAAG,OAAM,CAAC;AAAE,EAAAC,KAAEA,GAAE,QAAQ,0BAAyB,YAAY;AAAE,MAAG;AAAC,SAAG,KAAAD,MAAA,gBAAAA,GAAG,eAAH,mBAAe,QAAO;AAAC,YAAMF,KAAE,IAAI,IAAIE,GAAE,UAAU;AAAE,aAAO,KAAK,MAAMC,IAAG,CAACN,IAAEC,OAAIE,GAAE,IAAIH,EAAC,IAAE,EAAEC,EAAC,IAAEA,EAAE;AAAA,IAAC;AAAC,WAAO,KAAK,MAAMK,EAAC;AAAA,EAAC,SAAO,GAAE;AAAC,UAAM,IAAI,EAAE,GAAE,oCAAmC,EAAC,UAASA,IAAE,OAAM,EAAC,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAEH,IAAEC,IAAE;AAAC,SAAM,EAAC,SAAQ,OAAM,SAAQ,cAAa,SAAQ,GAAE,WAAUE,IAAE,cAAaH,IAAE,SAAQ,aAAY,YAAWC,MAAA,gBAAAA,GAAG,YAAW,OAAMA,MAAA,gBAAAA,GAAG,OAAM,GAAGA,MAAA,gBAAAA,GAAG,iBAAgB;AAAC;AAAuT,SAAS,GAAGQ,IAAE;AAAC,SAAO,IAAI,YAAW,gBAAgBA,GAAE,KAAK,GAAE,UAAU;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAK,CAACC,IAAEC,EAAC,IAAEF,GAAE,MAAM,GAAG;AAAE,SAAM,EAAC,QAAOE,KAAED,KAAE,IAAG,MAAKC,MAAGD,GAAC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAJ1rU;AAI2rU,QAAME,MAAE,KAAAF,GAAE,sBAAF,mBAAqB,aAAa;AAAW,MAAGE,MAAGA,OAAI,EAAE,OAAM,IAAI,EAAEC,IAAE,2BAA2BD,EAAC,wBAAwB,CAAC,EAAE;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,MAAIE,KAAE,IAAGE,KAAE;AAAG,MAAGC,GAAEL,GAAE,mBAAkB,EAAC,WAAU,CAAAA,QAAIE,KAAEF,GAAE,aAAa,eAAe,GAAE,EAAC,eAAc,CAAAA,OAAG;AAAC,IAAAI,KAAEJ,GAAE;AAAA,EAAW,EAAC,GAAE,CAAC,GAAEE,GAAE,OAAM,IAAI,EAAE,aAAaA,EAAC,IAAGE,EAAC;AAAC;", "names": ["e", "t", "n", "s", "o", "r", "f", "c", "E", "U", "t", "r", "n", "e", "M", "o", "s", "c", "a", "e", "t", "r", "U", "n", "o"]}