{"version": 3, "sources": ["../../@arcgis/core/core/accessorSupport/decorators/persistable.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isMultiOriginJSONMixin as t}from\"../../multiOriginJSONSupportUtils.js\";import{isAbsolute as r,isBlobProtocol as e,join as o,getPathExtension as s}from\"../../urlUtils.js\";import{generateUUID as n}from\"../../uuid.js\";import{getPropertyMetadata as i}from\"../metadata.js\";import{nameToId as a}from\"../PropertyOrigin.js\";import{propertyJSONMeta as p}from\"./property.js\";import{getResourceContentExtension as u}from\"../../../portal/support/resourceExtension.js\";import{r as c,t as l,M as m,i as f,p as y,a as d}from\"../../../chunks/persistableUrlUtils.js\";function g(t){const r=t?.origins??[void 0];return(e,o)=>{const s=h(t,e,o);for(const t of r){const r=p(e,t,o);for(const t in s)r[t]=s[t]}}}function h(t,r,e){if(\"resource\"===t?.type)return v(t,r,e);switch(t?.type??\"other\"){case\"other\":return{read:!0,write:!0};case\"url\":{const{read:t,write:r}=d;return{read:t,write:r}}}}function v(o,s,n){const p=i(s,n);return{type:String,read:(t,r,e)=>{const o=c(t,r,e);return p.type===String?o:\"function\"==typeof p.type?new p.type({url:o}):void 0},write:{writer(s,i,u,c){if(!c||!c.resources)return\"string\"==typeof s?void(i[u]=l(s,c)):void(i[u]=s.write({},c));const y=x(s),d=l(y,{...c,verifyItemRelativeUrls:c&&c.verifyItemRelativeUrls?{writtenUrls:c.verifyItemRelativeUrls.writtenUrls,rootPath:void 0}:void 0},m.NO),g=p.type!==String&&(!t(this)||c&&c.origin&&this.originIdOf(n)>a(c.origin)),h={object:this,propertyName:n,value:s,targetUrl:d,dest:i,targetPropertyName:u,context:c,params:o};c&&c.portalItem&&d&&!r(d)?g?w(h):j(h):c&&c.portalItem&&(null==d||null!=f(d)||e(d)||g)?U(h):i[u]=d}}}}function U(t){const{targetUrl:r,params:s,value:i,context:a,dest:p,targetPropertyName:c}=t;if(!a.portalItem)return;const l=y(r),m=l?.filename??n(),f=s?.prefix??l?.prefix,d=N(i,r,a),g=o(f,m),h=`${g}.${u(d)}`,v=a.portalItem.resourceFromPath(h);e(r)&&a.resources&&a.resources.pendingOperations.push(P(r).then((t=>{v.path=`${g}.${u(t)}`,p[c]=v.itemRelativeUrl})).catch((()=>{})));const U=s?.compress??!1;a.resources&&I({...t,resource:v,content:d,compress:U,updates:a.resources.toAdd}),p[c]=v.itemRelativeUrl}function w(t){const{context:r,targetUrl:e,params:o,value:n,dest:i,targetPropertyName:a}=t;if(!r.portalItem)return;const p=r.portalItem.resourceFromPath(e),c=N(n,e,r),l=u(c),m=s(p.path),f=o?.compress??!1;l===m?(r.resources&&I({...t,resource:p,content:c,compress:f,updates:r.resources.toUpdate}),i[a]=e):U(t)}function j({context:t,targetUrl:r,dest:e,targetPropertyName:o}){t.portalItem&&t.resources&&(t.resources.toKeep.push({resource:t.portalItem.resourceFromPath(r),compress:!1}),e[o]=r)}function I({object:t,propertyName:r,updates:e,resource:o,content:s,compress:n}){e.push({resource:o,content:s,compress:n,finish:e=>{O(t,r,e)}})}function N(t,r,e){return\"string\"==typeof t?{url:r}:new Blob([JSON.stringify(t.toJSON(e))],{type:\"application/json\"})}async function P(t){const r=(await import(\"../../../request.js\")).default,{data:e}=await r(t,{responseType:\"blob\"});return e}function x(t){return null==t?null:\"string\"==typeof t?t:t.url}function O(t,r,e){\"string\"==typeof t[r]?t[r]=e.url:t[r].url=e.url}export{g as persistable};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI6iB,SAAS,EAAEA,IAAE;AAAC,QAAM,KAAEA,MAAA,gBAAAA,GAAG,YAAS,CAAC,MAAM;AAAE,SAAM,CAAC,GAAE,MAAI;AAAC,UAAM,IAAE,EAAEA,IAAE,GAAE,CAAC;AAAE,eAAUA,MAAK,GAAE;AAAC,YAAMC,KAAE,EAAE,GAAED,IAAE,CAAC;AAAE,iBAAUA,MAAK,EAAE,CAAAC,GAAED,EAAC,IAAE,EAAEA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE;AAAC,MAAG,gBAAaA,MAAA,gBAAAA,GAAG,MAAK,QAAO,EAAEA,IAAE,GAAE,CAAC;AAAE,WAAOA,MAAA,gBAAAA,GAAG,SAAM,SAAQ;AAAA,IAAC,KAAI;AAAQ,aAAM,EAAC,MAAK,MAAG,OAAM,KAAE;AAAA,IAAE,KAAI,OAAM;AAAC,YAAK,EAAC,MAAKA,IAAE,OAAMC,GAAC,IAAE;AAAE,aAAM,EAAC,MAAKD,IAAE,OAAMC,GAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,GAAED,EAAC;AAAE,SAAM,EAAC,MAAK,QAAO,MAAK,CAACF,IAAE,GAAE,MAAI;AAAC,UAAMI,KAAE,EAAEJ,IAAE,GAAE,CAAC;AAAE,WAAOG,GAAE,SAAO,SAAOC,KAAE,cAAY,OAAOD,GAAE,OAAK,IAAIA,GAAE,KAAK,EAAC,KAAIC,GAAC,CAAC,IAAE;AAAA,EAAM,GAAE,OAAM,EAAC,OAAOC,IAAEC,IAAE,GAAEC,IAAE;AAAC,QAAG,CAACA,MAAG,CAACA,GAAE,UAAU,QAAM,YAAU,OAAOF,KAAE,MAAKC,GAAE,CAAC,IAAE,EAAED,IAAEE,EAAC,KAAG,MAAKD,GAAE,CAAC,IAAED,GAAE,MAAM,CAAC,GAAEE,EAAC;AAAG,UAAM,IAAEC,GAAEH,EAAC,GAAEI,KAAE,EAAE,GAAE,EAAC,GAAGF,IAAE,wBAAuBA,MAAGA,GAAE,yBAAuB,EAAC,aAAYA,GAAE,uBAAuB,aAAY,UAAS,OAAM,IAAE,OAAM,GAAE,EAAE,EAAE,GAAEG,KAAEP,GAAE,SAAO,WAAS,CAAC,EAAE,IAAI,KAAGI,MAAGA,GAAE,UAAQ,KAAK,WAAWL,EAAC,IAAE,EAAEK,GAAE,MAAM,IAAGI,KAAE,EAAC,QAAO,MAAK,cAAaT,IAAE,OAAMG,IAAE,WAAUI,IAAE,MAAKH,IAAE,oBAAmB,GAAE,SAAQC,IAAE,QAAO,EAAC;AAAE,IAAAA,MAAGA,GAAE,cAAYE,MAAG,CAAC,EAAEA,EAAC,IAAEC,KAAE,EAAEC,EAAC,IAAE,EAAEA,EAAC,IAAEJ,MAAGA,GAAE,eAAa,QAAME,MAAG,QAAM,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAGC,MAAGE,GAAED,EAAC,IAAEL,GAAE,CAAC,IAAEG;AAAA,EAAC,EAAC,EAAC;AAAC;AAAC,SAASG,GAAEZ,IAAE;AAAC,QAAK,EAAC,WAAU,GAAE,QAAO,GAAE,OAAMM,IAAE,SAAQ,GAAE,MAAKH,IAAE,oBAAmBI,GAAC,IAAEP;AAAE,MAAG,CAAC,EAAE,WAAW;AAAO,QAAM,IAAES,GAAE,CAAC,GAAEI,MAAE,uBAAG,aAAU,EAAE,GAAE,KAAE,uBAAG,YAAQ,uBAAG,SAAOJ,KAAE,EAAEH,IAAE,GAAE,CAAC,GAAEI,KAAE,EAAE,GAAEG,EAAC,GAAEF,KAAE,GAAGD,EAAC,IAAIV,GAAES,EAAC,CAAC,IAAGK,KAAE,EAAE,WAAW,iBAAiBH,EAAC;AAAE,IAAE,CAAC,KAAG,EAAE,aAAW,EAAE,UAAU,kBAAkB,KAAK,EAAE,CAAC,EAAE,KAAM,CAAAX,OAAG;AAAC,IAAAc,GAAE,OAAK,GAAGJ,EAAC,IAAIV,GAAEA,EAAC,CAAC,IAAGG,GAAEI,EAAC,IAAEO,GAAE;AAAA,EAAe,CAAE,EAAE,MAAO,MAAI;AAAA,EAAC,CAAE,CAAC;AAAE,QAAMF,MAAE,uBAAG,aAAU;AAAG,IAAE,aAAW,EAAE,EAAC,GAAGZ,IAAE,UAASc,IAAE,SAAQL,IAAE,UAASG,IAAE,SAAQ,EAAE,UAAU,MAAK,CAAC,GAAET,GAAEI,EAAC,IAAEO,GAAE;AAAe;AAAC,SAAS,EAAEd,IAAE;AAAC,QAAK,EAAC,SAAQ,GAAE,WAAU,GAAE,QAAO,GAAE,OAAME,IAAE,MAAKI,IAAE,oBAAmB,EAAC,IAAEN;AAAE,MAAG,CAAC,EAAE,WAAW;AAAO,QAAMG,KAAE,EAAE,WAAW,iBAAiB,CAAC,GAAEI,KAAE,EAAEL,IAAE,GAAE,CAAC,GAAE,IAAEF,GAAEO,EAAC,GAAEM,KAAE,GAAEV,GAAE,IAAI,GAAE,KAAE,uBAAG,aAAU;AAAG,QAAIU,MAAG,EAAE,aAAW,EAAE,EAAC,GAAGb,IAAE,UAASG,IAAE,SAAQI,IAAE,UAAS,GAAE,SAAQ,EAAE,UAAU,SAAQ,CAAC,GAAED,GAAE,CAAC,IAAE,KAAGM,GAAEZ,EAAC;AAAC;AAAC,SAAS,EAAE,EAAC,SAAQA,IAAE,WAAU,GAAE,MAAK,GAAE,oBAAmB,EAAC,GAAE;AAAC,EAAAA,GAAE,cAAYA,GAAE,cAAYA,GAAE,UAAU,OAAO,KAAK,EAAC,UAASA,GAAE,WAAW,iBAAiB,CAAC,GAAE,UAAS,MAAE,CAAC,GAAE,EAAE,CAAC,IAAE;AAAE;AAAC,SAAS,EAAE,EAAC,QAAOA,IAAE,cAAa,GAAE,SAAQ,GAAE,UAAS,GAAE,SAAQ,GAAE,UAASE,GAAC,GAAE;AAAC,IAAE,KAAK,EAAC,UAAS,GAAE,SAAQ,GAAE,UAASA,IAAE,QAAO,CAAAa,OAAG;AAAC,MAAEf,IAAE,GAAEe,EAAC;AAAA,EAAC,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEf,IAAE,GAAE,GAAE;AAAC,SAAM,YAAU,OAAOA,KAAE,EAAC,KAAI,EAAC,IAAE,IAAI,KAAK,CAAC,KAAK,UAAUA,GAAE,OAAO,CAAC,CAAC,CAAC,GAAE,EAAC,MAAK,mBAAkB,CAAC;AAAC;AAAC,eAAe,EAAEA,IAAE;AAAC,QAAM,KAAG,MAAM,OAAO,+BAAqB,GAAG,SAAQ,EAAC,MAAK,EAAC,IAAE,MAAM,EAAEA,IAAE,EAAC,cAAa,OAAM,CAAC;AAAE,SAAO;AAAC;AAAC,SAASQ,GAAER,IAAE;AAAC,SAAO,QAAMA,KAAE,OAAK,YAAU,OAAOA,KAAEA,KAAEA,GAAE;AAAG;AAAC,SAAS,EAAEA,IAAE,GAAE,GAAE;AAAC,cAAU,OAAOA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,EAAE,MAAIA,GAAE,CAAC,EAAE,MAAI,EAAE;AAAG;", "names": ["t", "r", "n", "p", "o", "s", "i", "c", "x", "d", "g", "h", "U", "m", "v", "e"]}