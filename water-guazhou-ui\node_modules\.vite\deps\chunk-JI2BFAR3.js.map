{"version": 3, "sources": ["../../@arcgis/core/layers/support/DimensionalDefinition.js", "../../@arcgis/core/layers/support/rasterDatasets/multidimensionalUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{JSONSupport as r}from\"../../core/JSONSupport.js\";import{clone as o}from\"../../core/lang.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import{types as i}from\"../../core/accessorSupport/ensureType.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";var a;let n=a=class extends r{constructor(e){super(e),this.variableName=null,this.dimensionName=null,this.values=[],this.isSlice=!1}clone(){return new a({variableName:this.variableName,dimensionName:this.dimensionName,values:o(this.values),isSlice:this.isSlice})}};e([s({type:String,json:{write:!0}})],n.prototype,\"variableName\",void 0),e([s({type:String,json:{write:!0}})],n.prototype,\"dimensionName\",void 0),e([s({type:i.array(i.oneOf([i.native(Number),i.array(i.native(Number))])),json:{write:!0}})],n.prototype,\"values\",void 0),e([s({type:Boolean,json:{write:!0}})],n.prototype,\"isSlice\",void 0),n=a=e([t(\"esri.layers.support.DimensionalDefinition\")],n);const p=n;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e,isNone as n}from\"../../../core/maybe.js\";import t from\"../DimensionalDefinition.js\";function i(e,n,t){const a=n.shift();if(0===t.length){const e=[];t.push({sliceId:-1,multidimensionalDefinition:e})}const s=t.length;for(let i=0;i<s;i++){const n=t.shift().multidimensionalDefinition;a.values?.forEach((i=>{t.push({sliceId:-1,multidimensionalDefinition:[...n,{variableName:e,dimensionName:a.name,values:[i]}]})}))}n.length&&i(e,n,t)}function a(e,n){const t=[];let a=0;return(n?e.variables.filter((e=>e.name.toLowerCase()===n.toLowerCase())):[...e.variables].sort(((e,n)=>e.name>n.name?1:-1))).forEach((e=>{const n=[],s=[...e.dimensions].sort(((e,n)=>e.name>n.name?-1:1));i(e.name,s,n),n.forEach((e=>{t.push({...e,sliceId:a++})}))})),t}function s(n,t,i){let a=n;if(t&&(t=[...t].sort(((e,n)=>e.dimensionName<n.dimensionName?-1:1))).forEach((({dimensionName:e,values:n,isSlice:t})=>{n.length&&(a=a.filter((i=>{const a=i.multidimensionalDefinition.find((n=>n.dimensionName===e));if(null==a)return!1;const s=a.values[0];return\"number\"==typeof s?\"number\"==typeof n[0]?n.includes(s):n.some((e=>e[0]<=s&&e[1]>=s)):\"number\"==typeof n[0]?n.some((e=>s[0]<=e&&s[1]>=e)):t?n.some((e=>e[0]===s[0]&&e[0]===s[1])):n.some((e=>e[0]>=s[0]&&e[0]<=s[1]||e[1]>=s[0]&&e[1]<=s[1]||e[0]<s[0]&&e[1]>s[1]))})))})),a.length&&i&&e(i.start)&&e(i.end)){const e=i.start.getTime(),n=i.end.getTime(),t=a[0].multidimensionalDefinition.findIndex((e=>\"StdTime\"===e.dimensionName));t>-1&&(a=a.filter((i=>{const a=i.multidimensionalDefinition[t].values[0];return e<=a&&n>=a})))}return a.map((e=>e.sliceId))}function r(e,n){return Array.isArray(e)?n[0]===n[1]?e[0]===n[0]||e[1]===n[0]:e[0]>=n[0]&&e[0]<=n[1]&&e[1]>=n[0]&&e[1]<=n[1]:e>=n[0]&&e<=n[1]}function l(e,n){return e[0]<=n[0]&&e[1]>=n[0]||e[0]<=n[1]&&e[1]>=n[1]||e[0]>=n[0]&&e[1]<=n[1]}function o(e){return 1===e.length?[e[0],e[0]]:[e[0],e[e.length-1]]}function u(e,n,t){if(!n?.subsetDefinitions?.length)return e;let i;if(t){const{variables:a}=n;if(a.length&&!a.includes(t))return null;const s=n.subsetDefinitions.find((n=>n.dimensionName===e.name&&n.variableName===t));if(!s?.values?.length)return e;i=o(s.values)}else{i=n.dimensions.find((({name:n})=>n===e.name))?.extent}const a=i;if(!a||!a?.length)return e;const s=e.values.filter((e=>r(e,a)));return{...e,extent:[...a],values:s}}function m(e,n,t){if(!n?.subsetDefinitions?.length)return!1;const{variables:i}=n;if(i.length&&e.some((({variableName:e})=>e&&!i.includes(e))))return!0;for(let a=0;a<e.length;a++){const i=e[a],s=n.subsetDefinitions.find((e=>(\"\"===i.variableName||e.variableName===i.variableName)&&e.dimensionName===i.dimensionName));if(s?.values.length){const e=o(s.values);if(!i.isSlice&&2===i.values.length&&!Array.isArray(i.values[0])&&i.values[0]!==i.values[1]&&t){if(!l(i.values,e))return!0}else if(i.values.some((n=>!r(n,e))))return!0}}return!1}function c(t,i){if(n(t))return{isOutside:!1};const{geometry:a,timeExtent:s,multidimensionalDefinition:r}=i;let l=null;if(e(s)&&(l=f(t,s),n(l)))return{isOutside:!0};const{areaOfInterest:o}=t;if(o&&a){const e=\"point\"===a.type?a:\"extent\"===a.type?a.center:\"polygon\"===a.type?a.centroid:null;if(e&&!o.contains(e))return{isOutside:!0}}return e(r)&&r.length&&m(r,t,!0)?{isOutside:!0}:{isOutside:!1,intersection:{geometry:a,timeExtent:l,multidimensionalDefinition:r}}}function f(e,i){const a=e.dimensions.find((({name:e})=>\"StdTime\"===e));if(null==a||n(i.start)&&n(i.end))return i;i=i.clone();const{start:s,end:r}=i.toJSON(),l=s===r?[s]:null!=s&&null!=r?[s,r]:[s??r];if(2===l.length&&a?.extent.length&&(l[0]=Math.max(l[0],a.extent[0]),l[1]=Math.min(l[1],a.extent[1]??a.extent[0]),l[1]<l[0]))return null;return m([new t({variableName:\"\",dimensionName:\"StdTime\",isSlice:1===l.length,values:l})],e,!0)?null:(i.start=new Date(l[0]),i.end=new Date(l[1]??l[0]),i)}function d(t,i={}){const{multidimensionalInfo:a,keyProperties:s}=t;if(n(a))return null;const{variableName:r,multidimensionalSubset:l,multidimensionalDefinition:o}=i,u=e(o)?o[0]?.variableName:null,m=r||u||s?.DefaultVariable;let{variables:c}=a;l?.variables?.length&&(c=c.filter((({name:e})=>l.variables.includes(e))));return m?c.find((({name:e})=>e===m))??c[0]:c[0]}function h(e,n={}){const i=d(e,n);if(!i)return null;const a=[],{dimensions:s,name:r}=i;if(0===s.length)return[new t({variableName:r,dimensionName:\"\",values:[],isSlice:!0})];for(let l=0;l<s.length;l++){const e=u(s[l],n.multidimensionalSubset,r);if(!e)return null;const{values:i,extent:o}=e;let m=i?.[0]??o[0];\"stdz\"===e.name.toLowerCase()&&!e.hasRanges&&Math.abs(o[1])<=Math.abs(o[0])&&(m=i?.length?i[i.length-1]:o[1]),a.push(new t({variableName:r,dimensionName:e.name,values:[m],isSlice:!n.useRangeForRangedDimensionInfo||!!e.hasRanges}))}return a}function g(e){return!(n(e)||!e.length)&&e.some((e=>{if(null==e.values)return!0;const n=e.values.length;return 0===n||n>1||!e.isSlice&&Array.isArray(e.values[0])}))}function v(t,i){if(n(i)||n(t))return null;let a=i.variables.map((e=>({...e})));return t?.variables?.length&&(a=a.filter((({name:e})=>t.variables.includes(e))),a.forEach((n=>{n.dimensions=n.dimensions.map((e=>u(e,t,n.name))).filter(e)}))),a}function b(e,n){const{values:t}=n;if(t?.length)return Array.isArray(t[0])!==Array.isArray(e)?-1:Array.isArray(t[0])?t.findIndex((n=>n[0]===e[0]&&n[1]===e[1])):t.indexOf(e);const{extent:i}=n;if(Array.isArray(e)||e<i[0]||e>i[1])return-1;const a=n.interval||1;if(\"ISO8601\"!==n.unit)return Math.round((e-i[0])/a);const s=i[0];let r=-1;switch(n.intervalUnit?.toLowerCase()||\"seconds\"){case\"seconds\":r=Math.round((e-s)/1e3/a);break;case\"minutes\":r=Math.round((e-s)/6e4/a);break;case\"hours\":r=Math.round((e-s)/36e5/a);break;case\"days\":r=Math.round((e-s)/864e5/a);break;case\"months\":{const n=new Date(e).getUTCFullYear()-new Date(s).getUTCFullYear(),t=new Date(s).getUTCMonth(),i=new Date(e).getUTCMonth();r=0===n?i-t:i+11-t+12*(n-1)}break;case\"years\":r=Math.round((new Date(e).getUTCFullYear()-new Date(s).getUTCFullYear())/a);break;case\"decades\":r=Math.round((new Date(e).getUTCFullYear()-new Date(s).getUTCFullYear())/10/a)}return r}function D(e){let n=e.values?.length;if(n)return n;const{extent:t,unit:i}=e,a=e.interval||1,s=t?t[1]-t[0]:0;if(\"ISO8601\"!==i)return Math.round(s/a);switch(e.intervalUnit?.toLowerCase()??\"seconds\"){case\"seconds\":n=Math.round(s/1e3/a);break;case\"minutes\":n=Math.round(s/6e4/a);break;case\"hours\":n=Math.round(s/36e5/a);break;case\"days\":n=Math.round(s/864e5/a);break;case\"months\":{const e=new Date(t[1]).getUTCFullYear()-new Date(t[0]).getUTCFullYear(),i=new Date(t[1][0]).getUTCMonth(),a=new Date(t[1][1]).getUTCMonth();n=0===e?a-i+1:a+11-i+12*(e-1)+1}break;case\"years\":n=Math.round((new Date(t[1]).getUTCFullYear()-new Date(t[0]).getUTCFullYear())/a);break;case\"decades\":n=Math.round((new Date(t[1]).getUTCFullYear()-new Date(t[0]).getUTCFullYear())/10/a);break;default:n=0}return n}function y(e,n){let t=0;const i=e[0].variableName,a=[...n.variables].sort(((e,n)=>e.name>n.name?1:-1));for(let s=0;s<a.length;s++){const n=a[s],r=[...n.dimensions].sort(((e,n)=>e.name>n.name?-1:1));if(n.name!==i){t+=r.map((e=>D(e))).reduce(((e,n)=>e*n));continue}const l=r.map((e=>D(e))),o=r.length;for(let i=0;i<o;i++){const n=e.find((e=>e.dimensionName===r[i].name));if(null==n)return null;const a=b(n.values[0],r[i]);if(-1===a)return null;l.shift(),t+=i===o-1?a:a*l.reduce(((e,n)=>e*n))}break}return t}export{a as createSlices,h as getDefaultMultidimensionalDefinition,d as getDefaultVariablInfo,s as getSliceIds,y as getSliceIndex,v as getSubsetVariablesFromMdInfo,m as hasExcludedVariableOrDimension,c as intersectMultimensionalSubset,g as isMultiSliceOrRangeDefinition};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAI4W,IAAIA;AAAE,IAAI,IAAEA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAK,KAAK,SAAO,CAAC,GAAE,KAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,WAAO,IAAID,GAAE,EAAC,cAAa,KAAK,cAAa,eAAc,KAAK,eAAc,QAAO,EAAE,KAAK,MAAM,GAAE,SAAQ,KAAK,QAAO,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,MAAM,GAAE,EAAE,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,IAAEA,KAAE,EAAE,CAAC,EAAE,2CAA2C,CAAC,GAAE,CAAC;AAAE,IAAME,KAAE;;;ACA/5B,SAAS,EAAEC,IAAEC,IAAEC,IAAE;AAJxH;AAIyH,QAAMC,KAAEF,GAAE,MAAM;AAAE,MAAG,MAAIC,GAAE,QAAO;AAAC,UAAMF,KAAE,CAAC;AAAE,IAAAE,GAAE,KAAK,EAAC,SAAQ,IAAG,4BAA2BF,GAAC,CAAC;AAAA,EAAC;AAAC,QAAMI,KAAEF,GAAE;AAAO,WAAQG,KAAE,GAAEA,KAAED,IAAEC,MAAI;AAAC,UAAMJ,KAAEC,GAAE,MAAM,EAAE;AAA2B,UAAAC,GAAE,WAAF,mBAAU,QAAS,CAAAE,OAAG;AAAC,MAAAH,GAAE,KAAK,EAAC,SAAQ,IAAG,4BAA2B,CAAC,GAAGD,IAAE,EAAC,cAAaD,IAAE,eAAcG,GAAE,MAAK,QAAO,CAACE,EAAC,EAAC,CAAC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAG;AAAC,EAAAJ,GAAE,UAAQ,EAAED,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAASC,GAAEH,IAAEC,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,MAAIC,KAAE;AAAE,UAAOF,KAAED,GAAE,UAAU,OAAQ,CAAAA,OAAGA,GAAE,KAAK,YAAY,MAAIC,GAAE,YAAY,CAAE,IAAE,CAAC,GAAGD,GAAE,SAAS,EAAE,KAAM,CAACA,IAAEC,OAAID,GAAE,OAAKC,GAAE,OAAK,IAAE,EAAG,GAAG,QAAS,CAAAD,OAAG;AAAC,UAAMC,KAAE,CAAC,GAAEG,KAAE,CAAC,GAAGJ,GAAE,UAAU,EAAE,KAAM,CAACA,IAAEC,OAAID,GAAE,OAAKC,GAAE,OAAK,KAAG,CAAE;AAAE,MAAED,GAAE,MAAKI,IAAEH,EAAC,GAAEA,GAAE,QAAS,CAAAD,OAAG;AAAC,MAAAE,GAAE,KAAK,EAAC,GAAGF,IAAE,SAAQG,KAAG,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE,GAAED;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEG,IAAE;AAAC,MAAIF,KAAEF;AAAE,MAAGC,OAAIA,KAAE,CAAC,GAAGA,EAAC,EAAE,KAAM,CAACF,IAAEC,OAAID,GAAE,gBAAcC,GAAE,gBAAc,KAAG,CAAE,GAAG,QAAS,CAAC,EAAC,eAAcD,IAAE,QAAOC,IAAE,SAAQC,GAAC,MAAI;AAAC,IAAAD,GAAE,WAASE,KAAEA,GAAE,OAAQ,CAAAE,OAAG;AAAC,YAAMF,KAAEE,GAAE,2BAA2B,KAAM,CAAAJ,OAAGA,GAAE,kBAAgBD,EAAE;AAAE,UAAG,QAAMG,GAAE,QAAM;AAAG,YAAMC,KAAED,GAAE,OAAO,CAAC;AAAE,aAAM,YAAU,OAAOC,KAAE,YAAU,OAAOH,GAAE,CAAC,IAAEA,GAAE,SAASG,EAAC,IAAEH,GAAE,KAAM,CAAAD,OAAGA,GAAE,CAAC,KAAGI,MAAGJ,GAAE,CAAC,KAAGI,EAAE,IAAE,YAAU,OAAOH,GAAE,CAAC,IAAEA,GAAE,KAAM,CAAAD,OAAGI,GAAE,CAAC,KAAGJ,MAAGI,GAAE,CAAC,KAAGJ,EAAE,IAAEE,KAAED,GAAE,KAAM,CAAAD,OAAGA,GAAE,CAAC,MAAII,GAAE,CAAC,KAAGJ,GAAE,CAAC,MAAII,GAAE,CAAC,CAAE,IAAEH,GAAE,KAAM,CAAAD,OAAGA,GAAE,CAAC,KAAGI,GAAE,CAAC,KAAGJ,GAAE,CAAC,KAAGI,GAAE,CAAC,KAAGJ,GAAE,CAAC,KAAGI,GAAE,CAAC,KAAGJ,GAAE,CAAC,KAAGI,GAAE,CAAC,KAAGJ,GAAE,CAAC,IAAEI,GAAE,CAAC,KAAGJ,GAAE,CAAC,IAAEI,GAAE,CAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAE,CAAE,GAAED,GAAE,UAAQE,MAAG,EAAEA,GAAE,KAAK,KAAG,EAAEA,GAAE,GAAG,GAAE;AAAC,UAAML,KAAEK,GAAE,MAAM,QAAQ,GAAEJ,KAAEI,GAAE,IAAI,QAAQ,GAAEH,KAAEC,GAAE,CAAC,EAAE,2BAA2B,UAAW,CAAAH,OAAG,cAAYA,GAAE,aAAc;AAAE,IAAAE,KAAE,OAAKC,KAAEA,GAAE,OAAQ,CAAAE,OAAG;AAAC,YAAMF,KAAEE,GAAE,2BAA2BH,EAAC,EAAE,OAAO,CAAC;AAAE,aAAOF,MAAGG,MAAGF,MAAGE;AAAA,IAAC,CAAE;AAAA,EAAE;AAAC,SAAOA,GAAE,IAAK,CAAAH,OAAGA,GAAE,OAAQ;AAAC;AAAC,SAASM,GAAEN,IAAEC,IAAE;AAAC,SAAO,MAAM,QAAQD,EAAC,IAAEC,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAED,GAAE,CAAC,MAAIC,GAAE,CAAC,KAAGD,GAAE,CAAC,MAAIC,GAAE,CAAC,IAAED,GAAE,CAAC,KAAGC,GAAE,CAAC,KAAGD,GAAE,CAAC,KAAGC,GAAE,CAAC,KAAGD,GAAE,CAAC,KAAGC,GAAE,CAAC,KAAGD,GAAE,CAAC,KAAGC,GAAE,CAAC,IAAED,MAAGC,GAAE,CAAC,KAAGD,MAAGC,GAAE,CAAC;AAAC;AAAC,SAASM,GAAEP,IAAEC,IAAE;AAAC,SAAOD,GAAE,CAAC,KAAGC,GAAE,CAAC,KAAGD,GAAE,CAAC,KAAGC,GAAE,CAAC,KAAGD,GAAE,CAAC,KAAGC,GAAE,CAAC,KAAGD,GAAE,CAAC,KAAGC,GAAE,CAAC,KAAGD,GAAE,CAAC,KAAGC,GAAE,CAAC,KAAGD,GAAE,CAAC,KAAGC,GAAE,CAAC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,MAAIA,GAAE,SAAO,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,IAAE,CAACA,GAAE,CAAC,GAAEA,GAAEA,GAAE,SAAO,CAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAEC,IAAE;AAJ/2D;AAIg3D,MAAG,GAAC,KAAAD,MAAA,gBAAAA,GAAG,sBAAH,mBAAsB,QAAO,QAAOD;AAAE,MAAIK;AAAE,MAAGH,IAAE;AAAC,UAAK,EAAC,WAAUC,GAAC,IAAEF;AAAE,QAAGE,GAAE,UAAQ,CAACA,GAAE,SAASD,EAAC,EAAE,QAAO;AAAK,UAAME,KAAEH,GAAE,kBAAkB,KAAM,CAAAA,OAAGA,GAAE,kBAAgBD,GAAE,QAAMC,GAAE,iBAAeC,EAAE;AAAE,QAAG,GAAC,KAAAE,MAAA,gBAAAA,GAAG,WAAH,mBAAW,QAAO,QAAOJ;AAAE,IAAAK,KAAE,EAAED,GAAE,MAAM;AAAA,EAAC,OAAK;AAAC,IAAAC,MAAE,KAAAJ,GAAE,WAAW,KAAM,CAAC,EAAC,MAAKA,GAAC,MAAIA,OAAID,GAAE,IAAK,MAA1C,mBAA6C;AAAA,EAAM;AAAC,QAAMG,KAAEE;AAAE,MAAG,CAACF,MAAG,EAACA,MAAA,gBAAAA,GAAG,QAAO,QAAOH;AAAE,QAAMI,KAAEJ,GAAE,OAAO,OAAQ,CAAAA,OAAGM,GAAEN,IAAEG,EAAC,CAAE;AAAE,SAAM,EAAC,GAAGH,IAAE,QAAO,CAAC,GAAGG,EAAC,GAAE,QAAOC,GAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAEC,IAAE;AAJ9xE;AAI+xE,MAAG,GAAC,KAAAD,MAAA,gBAAAA,GAAG,sBAAH,mBAAsB,QAAO,QAAM;AAAG,QAAK,EAAC,WAAUI,GAAC,IAAEJ;AAAE,MAAGI,GAAE,UAAQL,GAAE,KAAM,CAAC,EAAC,cAAaA,GAAC,MAAIA,MAAG,CAACK,GAAE,SAASL,EAAC,CAAE,EAAE,QAAM;AAAG,WAAQG,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAI;AAAC,UAAME,KAAEL,GAAEG,EAAC,GAAEC,KAAEH,GAAE,kBAAkB,KAAM,CAAAD,QAAI,OAAKK,GAAE,gBAAcL,GAAE,iBAAeK,GAAE,iBAAeL,GAAE,kBAAgBK,GAAE,aAAc;AAAE,QAAGD,MAAA,gBAAAA,GAAG,OAAO,QAAO;AAAC,YAAMJ,KAAE,EAAEI,GAAE,MAAM;AAAE,UAAG,CAACC,GAAE,WAAS,MAAIA,GAAE,OAAO,UAAQ,CAAC,MAAM,QAAQA,GAAE,OAAO,CAAC,CAAC,KAAGA,GAAE,OAAO,CAAC,MAAIA,GAAE,OAAO,CAAC,KAAGH,IAAE;AAAC,YAAG,CAACK,GAAEF,GAAE,QAAOL,EAAC,EAAE,QAAM;AAAA,MAAE,WAASK,GAAE,OAAO,KAAM,CAAAJ,OAAG,CAACK,GAAEL,IAAED,EAAC,CAAE,EAAE,QAAM;AAAA,IAAE;AAAA,EAAC;AAAC,SAAM;AAAE;AAAC,SAAS,EAAEE,IAAEG,IAAE;AAAC,MAAG,EAAEH,EAAC,EAAE,QAAM,EAAC,WAAU,MAAE;AAAE,QAAK,EAAC,UAASC,IAAE,YAAWC,IAAE,4BAA2BE,GAAC,IAAED;AAAE,MAAIE,KAAE;AAAK,MAAG,EAAEH,EAAC,MAAIG,KAAE,EAAEL,IAAEE,EAAC,GAAE,EAAEG,EAAC,GAAG,QAAM,EAAC,WAAU,KAAE;AAAE,QAAK,EAAC,gBAAeC,GAAC,IAAEN;AAAE,MAAGM,MAAGL,IAAE;AAAC,UAAMH,KAAE,YAAUG,GAAE,OAAKA,KAAE,aAAWA,GAAE,OAAKA,GAAE,SAAO,cAAYA,GAAE,OAAKA,GAAE,WAAS;AAAK,QAAGH,MAAG,CAACQ,GAAE,SAASR,EAAC,EAAE,QAAM,EAAC,WAAU,KAAE;AAAA,EAAC;AAAC,SAAO,EAAEM,EAAC,KAAGA,GAAE,UAAQ,EAAEA,IAAEJ,IAAE,IAAE,IAAE,EAAC,WAAU,KAAE,IAAE,EAAC,WAAU,OAAG,cAAa,EAAC,UAASC,IAAE,YAAWI,IAAE,4BAA2BD,GAAC,EAAC;AAAC;AAAC,SAAS,EAAEN,IAAEK,IAAE;AAAC,QAAMF,KAAEH,GAAE,WAAW,KAAM,CAAC,EAAC,MAAKA,GAAC,MAAI,cAAYA,EAAE;AAAE,MAAG,QAAMG,MAAG,EAAEE,GAAE,KAAK,KAAG,EAAEA,GAAE,GAAG,EAAE,QAAOA;AAAE,EAAAA,KAAEA,GAAE,MAAM;AAAE,QAAK,EAAC,OAAMD,IAAE,KAAIE,GAAC,IAAED,GAAE,OAAO,GAAEE,KAAEH,OAAIE,KAAE,CAACF,EAAC,IAAE,QAAMA,MAAG,QAAME,KAAE,CAACF,IAAEE,EAAC,IAAE,CAACF,MAAGE,EAAC;AAAE,MAAG,MAAIC,GAAE,WAAQJ,MAAA,gBAAAA,GAAG,OAAO,YAASI,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAEJ,GAAE,OAAO,CAAC,CAAC,GAAEI,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAEJ,GAAE,OAAO,CAAC,KAAGA,GAAE,OAAO,CAAC,CAAC,GAAEI,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAG,QAAO;AAAK,SAAO,EAAE,CAAC,IAAIE,GAAE,EAAC,cAAa,IAAG,eAAc,WAAU,SAAQ,MAAIF,GAAE,QAAO,QAAOA,GAAC,CAAC,CAAC,GAAEP,IAAE,IAAE,IAAE,QAAMK,GAAE,QAAM,IAAI,KAAKE,GAAE,CAAC,CAAC,GAAEF,GAAE,MAAI,IAAI,KAAKE,GAAE,CAAC,KAAGA,GAAE,CAAC,CAAC,GAAEF;AAAE;AAAC,SAAS,EAAEH,IAAEG,KAAE,CAAC,GAAE;AAJ3uH;AAI4uH,QAAK,EAAC,sBAAqBF,IAAE,eAAcC,GAAC,IAAEF;AAAE,MAAG,EAAEC,EAAC,EAAE,QAAO;AAAK,QAAK,EAAC,cAAaG,IAAE,wBAAuBC,IAAE,4BAA2BC,GAAC,IAAEH,IAAEK,KAAE,EAAEF,EAAC,KAAE,KAAAA,GAAE,CAAC,MAAH,mBAAM,eAAa,MAAKG,KAAEL,MAAGI,OAAGN,MAAA,gBAAAA,GAAG;AAAgB,MAAG,EAAC,WAAUQ,GAAC,IAAET;AAAE,SAAAI,MAAA,gBAAAA,GAAG,cAAH,mBAAc,YAASK,KAAEA,GAAE,OAAQ,CAAC,EAAC,MAAKZ,GAAC,MAAIO,GAAE,UAAU,SAASP,EAAC,CAAE;AAAG,SAAOW,KAAEC,GAAE,KAAM,CAAC,EAAC,MAAKZ,GAAC,MAAIA,OAAIW,EAAE,KAAGC,GAAE,CAAC,IAAEA,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEZ,IAAEC,KAAE,CAAC,GAAE;AAAC,QAAMI,KAAE,EAAEL,IAAEC,EAAC;AAAE,MAAG,CAACI,GAAE,QAAO;AAAK,QAAMF,KAAE,CAAC,GAAE,EAAC,YAAWC,IAAE,MAAKE,GAAC,IAAED;AAAE,MAAG,MAAID,GAAE,OAAO,QAAM,CAAC,IAAIK,GAAE,EAAC,cAAaH,IAAE,eAAc,IAAG,QAAO,CAAC,GAAE,SAAQ,KAAE,CAAC,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAI;AAAC,UAAMP,KAAE,EAAEI,GAAEG,EAAC,GAAEN,GAAE,wBAAuBK,EAAC;AAAE,QAAG,CAACN,GAAE,QAAO;AAAK,UAAK,EAAC,QAAOK,IAAE,QAAOG,GAAC,IAAER;AAAE,QAAIW,MAAEN,MAAA,gBAAAA,GAAI,OAAIG,GAAE,CAAC;AAAE,eAASR,GAAE,KAAK,YAAY,KAAG,CAACA,GAAE,aAAW,KAAK,IAAIQ,GAAE,CAAC,CAAC,KAAG,KAAK,IAAIA,GAAE,CAAC,CAAC,MAAIG,MAAEN,MAAA,gBAAAA,GAAG,UAAOA,GAAEA,GAAE,SAAO,CAAC,IAAEG,GAAE,CAAC,IAAGL,GAAE,KAAK,IAAIM,GAAE,EAAC,cAAaH,IAAE,eAAcN,GAAE,MAAK,QAAO,CAACW,EAAC,GAAE,SAAQ,CAACV,GAAE,kCAAgC,CAAC,CAACD,GAAE,UAAS,CAAC,CAAC;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,SAAS,EAAEH,IAAE;AAAC,SAAM,EAAE,EAAEA,EAAC,KAAG,CAACA,GAAE,WAASA,GAAE,KAAM,CAAAA,OAAG;AAAC,QAAG,QAAMA,GAAE,OAAO,QAAM;AAAG,UAAMC,KAAED,GAAE,OAAO;AAAO,WAAO,MAAIC,MAAGA,KAAE,KAAG,CAACD,GAAE,WAAS,MAAM,QAAQA,GAAE,OAAO,CAAC,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEE,IAAEG,IAAE;AAJ5xJ;AAI6xJ,MAAG,EAAEA,EAAC,KAAG,EAAEH,EAAC,EAAE,QAAO;AAAK,MAAIC,KAAEE,GAAE,UAAU,IAAK,CAAAL,QAAI,EAAC,GAAGA,GAAC,EAAG;AAAE,WAAO,KAAAE,MAAA,gBAAAA,GAAG,cAAH,mBAAc,YAASC,KAAEA,GAAE,OAAQ,CAAC,EAAC,MAAKH,GAAC,MAAIE,GAAE,UAAU,SAASF,EAAC,CAAE,GAAEG,GAAE,QAAS,CAAAF,OAAG;AAAC,IAAAA,GAAE,aAAWA,GAAE,WAAW,IAAK,CAAAD,OAAG,EAAEA,IAAEE,IAAED,GAAE,IAAI,CAAE,EAAE,OAAO,CAAC;AAAA,EAAC,CAAE,IAAGE;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAE;AAJ5gK;AAI6gK,QAAK,EAAC,QAAOC,GAAC,IAAED;AAAE,MAAGC,MAAA,gBAAAA,GAAG,OAAO,QAAO,MAAM,QAAQA,GAAE,CAAC,CAAC,MAAI,MAAM,QAAQF,EAAC,IAAE,KAAG,MAAM,QAAQE,GAAE,CAAC,CAAC,IAAEA,GAAE,UAAW,CAAAD,OAAGA,GAAE,CAAC,MAAID,GAAE,CAAC,KAAGC,GAAE,CAAC,MAAID,GAAE,CAAC,CAAE,IAAEE,GAAE,QAAQF,EAAC;AAAE,QAAK,EAAC,QAAOK,GAAC,IAAEJ;AAAE,MAAG,MAAM,QAAQD,EAAC,KAAGA,KAAEK,GAAE,CAAC,KAAGL,KAAEK,GAAE,CAAC,EAAE,QAAM;AAAG,QAAMF,KAAEF,GAAE,YAAU;AAAE,MAAG,cAAYA,GAAE,KAAK,QAAO,KAAK,OAAOD,KAAEK,GAAE,CAAC,KAAGF,EAAC;AAAE,QAAMC,KAAEC,GAAE,CAAC;AAAE,MAAIC,KAAE;AAAG,YAAO,KAAAL,GAAE,iBAAF,mBAAgB,kBAAe,WAAU;AAAA,IAAC,KAAI;AAAU,MAAAK,KAAE,KAAK,OAAON,KAAEI,MAAG,MAAID,EAAC;AAAE;AAAA,IAAM,KAAI;AAAU,MAAAG,KAAE,KAAK,OAAON,KAAEI,MAAG,MAAID,EAAC;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAG,KAAE,KAAK,OAAON,KAAEI,MAAG,OAAKD,EAAC;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAG,KAAE,KAAK,OAAON,KAAEI,MAAG,QAAMD,EAAC;AAAE;AAAA,IAAM,KAAI;AAAS;AAAC,cAAMF,KAAE,IAAI,KAAKD,EAAC,EAAE,eAAe,IAAE,IAAI,KAAKI,EAAC,EAAE,eAAe,GAAEF,KAAE,IAAI,KAAKE,EAAC,EAAE,YAAY,GAAEC,KAAE,IAAI,KAAKL,EAAC,EAAE,YAAY;AAAE,QAAAM,KAAE,MAAIL,KAAEI,KAAEH,KAAEG,KAAE,KAAGH,KAAE,MAAID,KAAE;AAAA,MAAE;AAAC;AAAA,IAAM,KAAI;AAAQ,MAAAK,KAAE,KAAK,OAAO,IAAI,KAAKN,EAAC,EAAE,eAAe,IAAE,IAAI,KAAKI,EAAC,EAAE,eAAe,KAAGD,EAAC;AAAE;AAAA,IAAM,KAAI;AAAU,MAAAG,KAAE,KAAK,OAAO,IAAI,KAAKN,EAAC,EAAE,eAAe,IAAE,IAAI,KAAKI,EAAC,EAAE,eAAe,KAAG,KAAGD,EAAC;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,SAAS,EAAEN,IAAE;AAJ16L;AAI26L,MAAIC,MAAE,KAAAD,GAAE,WAAF,mBAAU;AAAO,MAAGC,GAAE,QAAOA;AAAE,QAAK,EAAC,QAAOC,IAAE,MAAKG,GAAC,IAAEL,IAAEG,KAAEH,GAAE,YAAU,GAAEI,KAAEF,KAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE;AAAE,MAAG,cAAYG,GAAE,QAAO,KAAK,MAAMD,KAAED,EAAC;AAAE,YAAO,KAAAH,GAAE,iBAAF,mBAAgB,kBAAe,WAAU;AAAA,IAAC,KAAI;AAAU,MAAAC,KAAE,KAAK,MAAMG,KAAE,MAAID,EAAC;AAAE;AAAA,IAAM,KAAI;AAAU,MAAAF,KAAE,KAAK,MAAMG,KAAE,MAAID,EAAC;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAF,KAAE,KAAK,MAAMG,KAAE,OAAKD,EAAC;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAF,KAAE,KAAK,MAAMG,KAAE,QAAMD,EAAC;AAAE;AAAA,IAAM,KAAI;AAAS;AAAC,cAAMH,KAAE,IAAI,KAAKE,GAAE,CAAC,CAAC,EAAE,eAAe,IAAE,IAAI,KAAKA,GAAE,CAAC,CAAC,EAAE,eAAe,GAAEG,KAAE,IAAI,KAAKH,GAAE,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,GAAEC,KAAE,IAAI,KAAKD,GAAE,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY;AAAE,QAAAD,KAAE,MAAID,KAAEG,KAAEE,KAAE,IAAEF,KAAE,KAAGE,KAAE,MAAIL,KAAE,KAAG;AAAA,MAAC;AAAC;AAAA,IAAM,KAAI;AAAQ,MAAAC,KAAE,KAAK,OAAO,IAAI,KAAKC,GAAE,CAAC,CAAC,EAAE,eAAe,IAAE,IAAI,KAAKA,GAAE,CAAC,CAAC,EAAE,eAAe,KAAGC,EAAC;AAAE;AAAA,IAAM,KAAI;AAAU,MAAAF,KAAE,KAAK,OAAO,IAAI,KAAKC,GAAE,CAAC,CAAC,EAAE,eAAe,IAAE,IAAI,KAAKA,GAAE,CAAC,CAAC,EAAE,eAAe,KAAG,KAAGC,EAAC;AAAE;AAAA,IAAM;AAAQ,MAAAF,KAAE;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAASY,GAAEb,IAAEC,IAAE;AAAC,MAAIC,KAAE;AAAE,QAAMG,KAAEL,GAAE,CAAC,EAAE,cAAaG,KAAE,CAAC,GAAGF,GAAE,SAAS,EAAE,KAAM,CAACD,IAAEC,OAAID,GAAE,OAAKC,GAAE,OAAK,IAAE,EAAG;AAAE,WAAQG,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,UAAMH,KAAEE,GAAEC,EAAC,GAAEE,KAAE,CAAC,GAAGL,GAAE,UAAU,EAAE,KAAM,CAACD,IAAEC,OAAID,GAAE,OAAKC,GAAE,OAAK,KAAG,CAAE;AAAE,QAAGA,GAAE,SAAOI,IAAE;AAAC,MAAAH,MAAGI,GAAE,IAAK,CAAAN,OAAG,EAAEA,EAAC,CAAE,EAAE,OAAQ,CAACA,IAAEC,OAAID,KAAEC,EAAE;AAAE;AAAA,IAAQ;AAAC,UAAMM,KAAED,GAAE,IAAK,CAAAN,OAAG,EAAEA,EAAC,CAAE,GAAEQ,KAAEF,GAAE;AAAO,aAAQD,KAAE,GAAEA,KAAEG,IAAEH,MAAI;AAAC,YAAMJ,KAAED,GAAE,KAAM,CAAAA,OAAGA,GAAE,kBAAgBM,GAAED,EAAC,EAAE,IAAK;AAAE,UAAG,QAAMJ,GAAE,QAAO;AAAK,YAAME,KAAE,EAAEF,GAAE,OAAO,CAAC,GAAEK,GAAED,EAAC,CAAC;AAAE,UAAG,OAAKF,GAAE,QAAO;AAAK,MAAAI,GAAE,MAAM,GAAEL,MAAGG,OAAIG,KAAE,IAAEL,KAAEA,KAAEI,GAAE,OAAQ,CAACP,IAAEC,OAAID,KAAEC,EAAE;AAAA,IAAC;AAAC;AAAA,EAAK;AAAC,SAAOC;AAAC;", "names": ["a", "e", "p", "e", "n", "t", "a", "s", "i", "r", "l", "o", "p", "u", "m", "c", "y"]}