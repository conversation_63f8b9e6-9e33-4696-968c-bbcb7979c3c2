import {
  d,
  e as e2,
  n
} from "./chunk-SX465FPD.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has,
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/effects/EffectView.js
var l = -1;
var a2 = class extends v {
  constructor(t) {
    super(t), this._from = null, this._to = null, this._final = null, this._current = [], this._time = 0, this.duration = has("mapview-transitions-duration"), this.effects = [];
  }
  set effect(t) {
    if (this._get("effect") !== (t = t || "")) {
      this._set("effect", t);
      try {
        this._transitionTo(h(t));
      } catch (e3) {
        this._transitionTo([]), s.getLogger(this.declaredClass).warn("Invalid Effect", { effect: t, error: e3 });
      }
    }
  }
  get hasEffects() {
    return this.transitioning || !!this.effects.length;
  }
  set scale(t) {
    this._updateForScale(t);
  }
  get transitioning() {
    return null !== this._to;
  }
  canTransitionTo(t) {
    try {
      return this.scale > 0 && u(this._current, h(t), this.scale);
    } catch {
      return false;
    }
  }
  transitionStep(t, e3) {
    this._applyTimeTransition(t), this._updateForScale(e3);
  }
  endTransitions() {
    this._applyTimeTransition(this.duration);
  }
  _transitionTo(t) {
    this.scale > 0 && u(this._current, t, this.scale) ? (this._final = t, this._to = p(t), _(this._current, this._to, this.scale), this._from = p(this._current), this._time = 0) : (this._from = this._to = this._final = null, this._current = t), this._set("effects", this._current[0] ? p(this._current[0].effects) : []);
  }
  _applyTimeTransition(t) {
    if (!(this._to && this._from && this._current && this._final)) return;
    this._time += t;
    const e3 = Math.min(1, this._time / this.duration);
    for (let s2 = 0; s2 < this._current.length; s2++) {
      const t2 = this._current[s2], r = this._from[s2], i = this._to[s2];
      t2.scale = p2(r.scale, i.scale, e3);
      for (let s3 = 0; s3 < t2.effects.length; s3++) {
        const n2 = t2.effects[s3], c = r.effects[s3], o = i.effects[s3];
        n2.interpolate(c, o, e3);
      }
    }
    1 === e3 && (this._current = this._final, this._set("effects", this._current[0] ? p(this._current[0].effects) : []), this._from = this._to = this._final = null);
  }
  _updateForScale(t) {
    if (this._set("scale", t), 0 === this._current.length) return;
    const e3 = this._current, s2 = this._current.length - 1;
    let r, i, n2 = 1;
    if (1 === e3.length || t >= e3[0].scale) i = r = e3[0].effects;
    else if (t <= e3[s2].scale) i = r = e3[s2].effects;
    else for (let c = 0; c < s2; c++) {
      const s3 = e3[c], o = e3[c + 1];
      if (s3.scale >= t && o.scale <= t) {
        n2 = (t - s3.scale) / (o.scale - s3.scale), r = s3.effects, i = o.effects;
        break;
      }
    }
    for (let c = 0; c < this.effects.length; c++) {
      this.effects[c].interpolate(r[c], i[c], n2);
    }
  }
};
function h(t) {
  const e3 = d(t) || [];
  return m(e3) ? [{ scale: l, effects: e3 }] : e3;
}
function u(t, e3, s2) {
  var _a, _b, _c, _d;
  if (!((_a = t[0]) == null ? void 0 : _a.effects) || !((_b = e3[0]) == null ? void 0 : _b.effects)) return true;
  return !((((_c = t[0]) == null ? void 0 : _c.scale) === l || ((_d = e3[0]) == null ? void 0 : _d.scale) === l) && (t.length > 1 || e3.length > 1) && s2 <= 0) && n(t[0].effects, e3[0].effects);
}
function _(t, e3, s2) {
  const r = t.length > e3.length ? t : e3, i = t.length > e3.length ? e3 : t, n2 = i[i.length - 1], c = (n2 == null ? void 0 : n2.scale) ?? s2, o = (n2 == null ? void 0 : n2.effects) ?? [];
  for (let f = i.length; f < r.length; f++) i.push({ scale: c, effects: [...o] });
  for (let a3 = 0; a3 < r.length; a3++) i[a3].scale = i[a3].scale === l ? s2 : i[a3].scale, r[a3].scale = r[a3].scale === l ? s2 : r[a3].scale, e2(i[a3].effects, r[a3].effects);
}
function p2(t, e3, s2) {
  return t + (e3 - t) * s2;
}
function m(t) {
  const e3 = t[0];
  return !!e3 && "type" in e3;
}
e([y()], a2.prototype, "_to", void 0), e([y()], a2.prototype, "duration", void 0), e([y({ value: "" })], a2.prototype, "effect", null), e([y({ readOnly: true })], a2.prototype, "effects", void 0), e([y({ readOnly: true })], a2.prototype, "hasEffects", null), e([y({ value: 0 })], a2.prototype, "scale", null), e([y({ readOnly: true })], a2.prototype, "transitioning", null), a2 = e([a("esri.layers.effects.EffectView")], a2);

export {
  a2 as a
};
//# sourceMappingURL=chunk-HXJOBP6R.js.map
