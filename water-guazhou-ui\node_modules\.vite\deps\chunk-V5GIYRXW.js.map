{"version": 3, "sources": ["../../@arcgis/core/geometry/Polyline.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import{clone as e}from\"../core/lang.js\";import{property as s}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{subclass as r}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as i}from\"../core/accessorSupport/decorators/writer.js\";import a from\"./Extent.js\";import n from\"./Geometry.js\";import o from\"./Point.js\";import h from\"./SpatialReference.js\";import{getPolylineExtent as p}from\"./support/extentUtils.js\";import{updateSupportFromPoint as l}from\"./support/zmUtils.js\";var c;function u(t){return!Array.isArray(t[0])}let f=c=class extends n{constructor(...t){super(...t),this.paths=[],this.type=\"polyline\"}normalizeCtorArgs(t,e){let s,r,i=null,a=null;return t&&!Array.isArray(t)?(i=t.paths?t.paths:null,e||(t.spatialReference?e=t.spatialReference:t.paths||(e=t)),s=t.hasZ,r=t.hasM):i=t,i=i||[],e=e||h.WGS84,i.length&&i[0]&&null!=i[0][0]&&\"number\"==typeof i[0][0]&&(i=[i]),a=i[0]&&i[0][0],a&&(void 0===s&&void 0===r?(s=a.length>2,r=!1):void 0===s?s=!r&&a.length>3:void 0===r&&(r=!s&&a.length>3)),{paths:i,spatialReference:e,hasZ:s,hasM:r}}get cache(){return this.commitProperty(\"paths\"),this.commitProperty(\"hasZ\"),this.commitProperty(\"hasM\"),this.commitProperty(\"spatialReference\"),{}}get extent(){const{spatialReference:t}=this,e=p(this);if(!e)return null;const s=new a(e);return s.spatialReference=t,s}writePaths(t,s){s.paths=e(this.paths)}addPath(t){if(!t)return;const e=this.paths,s=e.length;if(u(t)){const r=[];for(let e=0,s=t.length;e<s;e++)r[e]=t[e].toArray();e[s]=r}else e[s]=t.concat();return this.notifyChange(\"paths\"),this}clone(){const t=new c;return t.spatialReference=this.spatialReference,t.paths=e(this.paths),t.hasZ=this.hasZ,t.hasM=this.hasM,t}getPoint(t,e){if(!this._validateInputs(t,e))return null;const s=this.paths[t][e],r=this.hasZ,i=this.hasM;return r&&!i?new o(s[0],s[1],s[2],void 0,this.spatialReference):i&&!r?new o(s[0],s[1],void 0,s[2],this.spatialReference):r&&i?new o(s[0],s[1],s[2],s[3],this.spatialReference):new o(s[0],s[1],this.spatialReference)}insertPoint(t,e,s){return this._validateInputs(t,e,!0)?(l(this,s),Array.isArray(s)||(s=s.toArray()),this.paths[t].splice(e,0,s),this.notifyChange(\"paths\"),this):this}removePath(t){if(!this._validateInputs(t,null))return null;const e=this.paths.splice(t,1)[0],s=this.spatialReference,r=e.map((t=>new o(t,s)));return this.notifyChange(\"paths\"),r}removePoint(t,e){if(!this._validateInputs(t,e))return null;const s=new o(this.paths[t].splice(e,1)[0],this.spatialReference);return this.notifyChange(\"paths\"),s}setPoint(t,e,s){return this._validateInputs(t,e)?(l(this,s),Array.isArray(s)||(s=s.toArray()),this.paths[t][e]=s,this.notifyChange(\"paths\"),this):this}_validateInputs(t,e,s=!1){if(null==t||t<0||t>=this.paths.length)return!1;if(null!=e){const r=this.paths[t];if(s&&(e<0||e>r.length))return!1;if(!s&&(e<0||e>=r.length))return!1}return!0}toJSON(t){return this.write({},t)}};t([s({readOnly:!0})],f.prototype,\"cache\",null),t([s({readOnly:!0})],f.prototype,\"extent\",null),t([s({type:[[[Number]]],json:{write:{isRequired:!0}}})],f.prototype,\"paths\",void 0),t([i(\"paths\")],f.prototype,\"writePaths\",null),f=c=t([r(\"esri.geometry.Polyline\")],f),f.prototype.toJSON.isDefaultToJSON=!0;const m=f;export{m as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI6kB,IAAI;AAAE,SAAS,EAAE,GAAE;AAAC,SAAM,CAAC,MAAM,QAAQ,EAAE,CAAC,CAAC;AAAC;AAAC,IAAIA,KAAE,IAAE,cAAcC,GAAC;AAAA,EAAC,eAAe,GAAE;AAAC,UAAM,GAAG,CAAC,GAAE,KAAK,QAAM,CAAC,GAAE,KAAK,OAAK;AAAA,EAAU;AAAA,EAAC,kBAAkB,GAAEC,IAAE;AAAC,QAAI,GAAEC,IAAE,IAAE,MAAKC,KAAE;AAAK,WAAO,KAAG,CAAC,MAAM,QAAQ,CAAC,KAAG,IAAE,EAAE,QAAM,EAAE,QAAM,MAAKF,OAAI,EAAE,mBAAiBA,KAAE,EAAE,mBAAiB,EAAE,UAAQA,KAAE,KAAI,IAAE,EAAE,MAAKC,KAAE,EAAE,QAAM,IAAE,GAAE,IAAE,KAAG,CAAC,GAAED,KAAEA,MAAG,EAAE,OAAM,EAAE,UAAQ,EAAE,CAAC,KAAG,QAAM,EAAE,CAAC,EAAE,CAAC,KAAG,YAAU,OAAO,EAAE,CAAC,EAAE,CAAC,MAAI,IAAE,CAAC,CAAC,IAAGE,KAAE,EAAE,CAAC,KAAG,EAAE,CAAC,EAAE,CAAC,GAAEA,OAAI,WAAS,KAAG,WAASD,MAAG,IAAEC,GAAE,SAAO,GAAED,KAAE,SAAI,WAAS,IAAE,IAAE,CAACA,MAAGC,GAAE,SAAO,IAAE,WAASD,OAAIA,KAAE,CAAC,KAAGC,GAAE,SAAO,KAAI,EAAC,OAAM,GAAE,kBAAiBF,IAAE,MAAK,GAAE,MAAKC,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,eAAe,OAAO,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,kBAAkB,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,UAAK,EAAC,kBAAiB,EAAC,IAAE,MAAKD,KAAEG,GAAE,IAAI;AAAE,QAAG,CAACH,GAAE,QAAO;AAAK,UAAM,IAAE,IAAII,GAAEJ,EAAC;AAAE,WAAO,EAAE,mBAAiB,GAAE;AAAA,EAAC;AAAA,EAAC,WAAW,GAAE,GAAE;AAAC,MAAE,QAAM,EAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,QAAQ,GAAE;AAAC,QAAG,CAAC,EAAE;AAAO,UAAMA,KAAE,KAAK,OAAM,IAAEA,GAAE;AAAO,QAAG,EAAE,CAAC,GAAE;AAAC,YAAMC,KAAE,CAAC;AAAE,eAAQD,KAAE,GAAEK,KAAE,EAAE,QAAOL,KAAEK,IAAEL,KAAI,CAAAC,GAAED,EAAC,IAAE,EAAEA,EAAC,EAAE,QAAQ;AAAE,MAAAA,GAAE,CAAC,IAAEC;AAAA,IAAC,MAAM,CAAAD,GAAE,CAAC,IAAE,EAAE,OAAO;AAAE,WAAO,KAAK,aAAa,OAAO,GAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,UAAM,IAAE,IAAI;AAAE,WAAO,EAAE,mBAAiB,KAAK,kBAAiB,EAAE,QAAM,EAAE,KAAK,KAAK,GAAE,EAAE,OAAK,KAAK,MAAK,EAAE,OAAK,KAAK,MAAK;AAAA,EAAC;AAAA,EAAC,SAAS,GAAEA,IAAE;AAAC,QAAG,CAAC,KAAK,gBAAgB,GAAEA,EAAC,EAAE,QAAO;AAAK,UAAM,IAAE,KAAK,MAAM,CAAC,EAAEA,EAAC,GAAEC,KAAE,KAAK,MAAK,IAAE,KAAK;AAAK,WAAOA,MAAG,CAAC,IAAE,IAAI,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,QAAO,KAAK,gBAAgB,IAAE,KAAG,CAACA,KAAE,IAAI,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,QAAO,EAAE,CAAC,GAAE,KAAK,gBAAgB,IAAEA,MAAG,IAAE,IAAI,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,KAAK,gBAAgB,IAAE,IAAI,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,YAAY,GAAED,IAAE,GAAE;AAAC,WAAO,KAAK,gBAAgB,GAAEA,IAAE,IAAE,KAAG,EAAE,MAAK,CAAC,GAAE,MAAM,QAAQ,CAAC,MAAI,IAAE,EAAE,QAAQ,IAAG,KAAK,MAAM,CAAC,EAAE,OAAOA,IAAE,GAAE,CAAC,GAAE,KAAK,aAAa,OAAO,GAAE,QAAM;AAAA,EAAI;AAAA,EAAC,WAAW,GAAE;AAAC,QAAG,CAAC,KAAK,gBAAgB,GAAE,IAAI,EAAE,QAAO;AAAK,UAAMA,KAAE,KAAK,MAAM,OAAO,GAAE,CAAC,EAAE,CAAC,GAAE,IAAE,KAAK,kBAAiBC,KAAED,GAAE,IAAK,CAAAM,OAAG,IAAI,EAAEA,IAAE,CAAC,CAAE;AAAE,WAAO,KAAK,aAAa,OAAO,GAAEL;AAAA,EAAC;AAAA,EAAC,YAAY,GAAED,IAAE;AAAC,QAAG,CAAC,KAAK,gBAAgB,GAAEA,EAAC,EAAE,QAAO;AAAK,UAAM,IAAE,IAAI,EAAE,KAAK,MAAM,CAAC,EAAE,OAAOA,IAAE,CAAC,EAAE,CAAC,GAAE,KAAK,gBAAgB;AAAE,WAAO,KAAK,aAAa,OAAO,GAAE;AAAA,EAAC;AAAA,EAAC,SAAS,GAAEA,IAAE,GAAE;AAAC,WAAO,KAAK,gBAAgB,GAAEA,EAAC,KAAG,EAAE,MAAK,CAAC,GAAE,MAAM,QAAQ,CAAC,MAAI,IAAE,EAAE,QAAQ,IAAG,KAAK,MAAM,CAAC,EAAEA,EAAC,IAAE,GAAE,KAAK,aAAa,OAAO,GAAE,QAAM;AAAA,EAAI;AAAA,EAAC,gBAAgB,GAAEA,IAAE,IAAE,OAAG;AAAC,QAAG,QAAM,KAAG,IAAE,KAAG,KAAG,KAAK,MAAM,OAAO,QAAM;AAAG,QAAG,QAAMA,IAAE;AAAC,YAAMC,KAAE,KAAK,MAAM,CAAC;AAAE,UAAG,MAAID,KAAE,KAAGA,KAAEC,GAAE,QAAQ,QAAM;AAAG,UAAG,CAAC,MAAID,KAAE,KAAGA,MAAGC,GAAE,QAAQ,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,OAAO,GAAE;AAAC,WAAO,KAAK,MAAM,CAAC,GAAE,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEH,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,EAAC,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,OAAO,CAAC,GAAEA,GAAE,WAAU,cAAa,IAAI,GAAEA,KAAE,IAAE,EAAE,CAAC,EAAE,wBAAwB,CAAC,GAAEA,EAAC,GAAEA,GAAE,UAAU,OAAO,kBAAgB;AAAG,IAAM,IAAEA;", "names": ["f", "p", "e", "r", "a", "h", "w", "s", "t"]}