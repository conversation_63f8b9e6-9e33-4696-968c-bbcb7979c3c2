{"version": 3, "sources": ["../../@arcgis/core/views/3d/support/engineContent/sdfPrimitives.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../core/has.js\";import{packFloatRGBA as t}from\"../../../../core/floatRGBA.js\";import{Texture as n}from\"../../webgl-engine/lib/Texture.js\";import{TextureWrapMode as r}from\"../../../webgl/enums.js\";const e=128,a=.5;function o(t,o=e,c=o*a,s=0){const i=u(t,o,c,s);return new n(i,{mipmap:!1,wrap:{s:r.CLAMP_TO_EDGE,t:r.CLAMP_TO_EDGE},width:o,height:o,components:4,noUnpackFlip:!0})}function u(t,n=e,r=n*a,o=0){switch(t){case\"circle\":default:return c(n,r);case\"square\":return s(n,r);case\"cross\":return h(n,r,o);case\"x\":return f(n,r,o);case\"kite\":return i(n,r);case\"triangle\":return M(n,r);case\"arrow\":return m(n,r)}}function c(t,n){const r=t/2-.5;return w(t,T(r,r,n/2))}function s(t,n){return l(t,n,!1)}function i(t,n){return l(t,n,!0)}function h(t,n,r=0){return p(t,n,!1,r)}function f(t,n,r=0){return p(t,n,!0,r)}function M(t,n){return w(t,b(t/2,n,n/2))}function m(t,n){const r=n,e=n/2,a=t/2,o=.8*r,u=T(a,(t-n)/2-o,Math.sqrt(o*o+e*e)),c=b(a,r,e);return w(t,((t,n)=>Math.max(c(t,n),-u(t,n))))}function l(t,n,r){return r&&(n/=Math.SQRT2),w(t,((e,a)=>{let o=e-.5*t+.25,u=.5*t-a-.75;if(r){const t=(o+u)/Math.SQRT2;u=(u-o)/Math.SQRT2,o=t}return Math.max(Math.abs(o),Math.abs(u))-.5*n}))}function p(t,n,r,e=0){n-=e,r&&(n*=Math.SQRT2);const a=.5*n;return w(t,((n,o)=>{let u,c=n-.5*t,s=.5*t-o-1;if(r){const t=(c+s)/Math.SQRT2;s=(s-c)/Math.SQRT2,c=t}return c=Math.abs(c),s=Math.abs(s),u=c>s?c>a?Math.sqrt((c-a)*(c-a)+s*s):s:s>a?Math.sqrt(c*c+(s-a)*(s-a)):c,u-=e/2,u}))}function T(t,n,r){return(e,a)=>{const o=e-t,u=a-n;return Math.sqrt(o*o+u*u)-r}}function b(t,n,r){const e=Math.sqrt(n*n+r*r);return(a,o)=>{const u=Math.abs(a-t)-r,c=o-t+n/2+.75,s=(n*u+r*c)/e,i=-c;return Math.max(s,i)}}function w(n,r){const e=new Uint8Array(4*n*n);for(let a=0;a<n;a++)for(let o=0;o<n;o++){const u=o+n*a;let c=r(o,a);c=c/n+.5,t(c,e,4*u)}return e}export{a as DEFAULT_SYMBOL_SIZE_RATIO,e as DEFAULT_TEX_SIZE,m as createArrow,c as createCircle,h as createCross,i as createKite,s as createSquare,o as createTexture,M as createTriangle,f as createX};\n"], "mappings": ";;;;;;;;;;;AAIoN,IAAM,IAAE;AAAR,IAAY,IAAE;AAAG,SAASA,GAAE,GAAEA,KAAE,GAAEC,KAAED,KAAE,GAAEE,KAAE,GAAE;AAAC,QAAMC,KAAE,EAAE,GAAEH,IAAEC,IAAEC,EAAC;AAAE,SAAO,IAAI,EAAEC,IAAE,EAAC,QAAO,OAAG,MAAK,EAAC,GAAE,EAAE,eAAc,GAAE,EAAE,cAAa,GAAE,OAAMH,IAAE,QAAOA,IAAE,YAAW,GAAE,cAAa,KAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,IAAE,GAAE,IAAE,IAAE,GAAEA,KAAE,GAAE;AAAC,UAAO,GAAE;AAAA,IAAC,KAAI;AAAA,IAAS;AAAQ,aAAO,EAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAS,aAAO,EAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAQ,aAAO,EAAE,GAAE,GAAEA,EAAC;AAAA,IAAE,KAAI;AAAI,aAAO,EAAE,GAAE,GAAEA,EAAC;AAAA,IAAE,KAAI;AAAO,aAAO,EAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAW,aAAO,EAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAQ,aAAO,EAAE,GAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAM,IAAE,IAAE,IAAE;AAAG,SAAO,EAAE,GAAE,EAAE,GAAE,GAAE,IAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,EAAE,GAAE,GAAE,KAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,EAAE,GAAE,GAAE,IAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,IAAE,GAAE;AAAC,SAAO,EAAE,GAAE,GAAE,OAAG,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,IAAE,GAAE;AAAC,SAAO,EAAE,GAAE,GAAE,MAAG,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,EAAE,GAAE,EAAE,IAAE,GAAE,GAAE,IAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAM,IAAE,GAAEI,KAAE,IAAE,GAAEC,KAAE,IAAE,GAAEL,KAAE,MAAG,GAAEM,KAAE,EAAED,KAAG,IAAE,KAAG,IAAEL,IAAE,KAAK,KAAKA,KAAEA,KAAEI,KAAEA,EAAC,CAAC,GAAEH,KAAE,EAAEI,IAAE,GAAED,EAAC;AAAE,SAAO,EAAE,GAAG,CAACG,IAAEC,OAAI,KAAK,IAAIP,GAAEM,IAAEC,EAAC,GAAE,CAACF,GAAEC,IAAEC,EAAC,CAAC,CAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,SAAO,MAAI,KAAG,KAAK,QAAO,EAAE,GAAG,CAACJ,IAAEC,OAAI;AAAC,QAAIL,KAAEI,KAAE,MAAG,IAAE,MAAIE,KAAE,MAAG,IAAED,KAAE;AAAI,QAAG,GAAE;AAAC,YAAME,MAAGP,KAAEM,MAAG,KAAK;AAAM,MAAAA,MAAGA,KAAEN,MAAG,KAAK,OAAMA,KAAEO;AAAA,IAAC;AAAC,WAAO,KAAK,IAAI,KAAK,IAAIP,EAAC,GAAE,KAAK,IAAIM,EAAC,CAAC,IAAE,MAAG;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAEF,KAAE,GAAE;AAAC,OAAGA,IAAE,MAAI,KAAG,KAAK;AAAO,QAAMC,KAAE,MAAG;AAAE,SAAO,EAAE,GAAG,CAACG,IAAER,OAAI;AAAC,QAAIM,IAAEL,KAAEO,KAAE,MAAG,GAAEN,KAAE,MAAG,IAAEF,KAAE;AAAE,QAAG,GAAE;AAAC,YAAMO,MAAGN,KAAEC,MAAG,KAAK;AAAM,MAAAA,MAAGA,KAAED,MAAG,KAAK,OAAMA,KAAEM;AAAA,IAAC;AAAC,WAAON,KAAE,KAAK,IAAIA,EAAC,GAAEC,KAAE,KAAK,IAAIA,EAAC,GAAEI,KAAEL,KAAEC,KAAED,KAAEI,KAAE,KAAK,MAAMJ,KAAEI,OAAIJ,KAAEI,MAAGH,KAAEA,EAAC,IAAEA,KAAEA,KAAEG,KAAE,KAAK,KAAKJ,KAAEA,MAAGC,KAAEG,OAAIH,KAAEG,GAAE,IAAEJ,IAAEK,MAAGF,KAAE,GAAEE;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,SAAM,CAACF,IAAEC,OAAI;AAAC,UAAML,KAAEI,KAAE,GAAEE,KAAED,KAAE;AAAE,WAAO,KAAK,KAAKL,KAAEA,KAAEM,KAAEA,EAAC,IAAE;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,QAAMF,KAAE,KAAK,KAAK,IAAE,IAAE,IAAE,CAAC;AAAE,SAAM,CAACC,IAAEL,OAAI;AAAC,UAAMM,KAAE,KAAK,IAAID,KAAE,CAAC,IAAE,GAAEJ,KAAED,KAAE,IAAE,IAAE,IAAE,MAAIE,MAAG,IAAEI,KAAE,IAAEL,MAAGG,IAAED,KAAE,CAACF;AAAE,WAAO,KAAK,IAAIC,IAAEC,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAMC,KAAE,IAAI,WAAW,IAAE,IAAE,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAE,GAAEA,KAAI,UAAQL,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAMM,KAAEN,KAAE,IAAEK;AAAE,QAAIJ,KAAE,EAAED,IAAEK,EAAC;AAAE,IAAAJ,KAAEA,KAAE,IAAE,KAAG,EAAEA,IAAEG,IAAE,IAAEE,EAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;", "names": ["o", "c", "s", "i", "e", "a", "u", "t", "n"]}