{"version": 3, "sources": ["../../@arcgis/core/views/layers/support/ClipArea.js", "../../@arcgis/core/views/layers/support/ClipRect.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{JSONSupport as s}from\"../../../core/JSONSupport.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as e}from\"../../../core/accessorSupport/decorators/subclass.js\";let t=class extends s{get version(){return this.commitVersionProperties(),(this._get(\"version\")||0)+1}};r([o({readOnly:!0})],t.prototype,\"version\",null),t=r([e(\"esri.views.layers.support.ClipArea\")],t);const p=t;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import e from\"./ClipArea.js\";var s;let i=s=class extends e{constructor(t){super(t),this.type=\"rect\",this.left=null,this.right=null,this.top=null,this.bottom=null}clone(){return new s({left:this.left,right:this.right,top:this.top,bottom:this.bottom})}commitVersionProperties(){this.commitProperty(\"left\"),this.commitProperty(\"right\"),this.commitProperty(\"top\"),this.commitProperty(\"bottom\")}};t([o({type:[Number,String],json:{write:!0}})],i.prototype,\"left\",void 0),t([o({type:[Number,String],json:{write:!0}})],i.prototype,\"right\",void 0),t([o({type:[Number,String],json:{write:!0}})],i.prototype,\"top\",void 0),t([o({type:[Number,String],json:{write:!0}})],i.prototype,\"bottom\",void 0),i=s=t([r(\"esri.views.layers.support.ClipRect\")],i);const p=i;export{p as default};\n"], "mappings": ";;;;;;;;;;;;AAIoW,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,wBAAwB,IAAG,KAAK,KAAK,SAAS,KAAG,KAAG;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACAhP,IAAI;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,QAAO,KAAK,OAAK,MAAK,KAAK,QAAM,MAAK,KAAK,MAAI,MAAK,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,MAAK,KAAK,MAAK,OAAM,KAAK,OAAM,KAAI,KAAK,KAAI,QAAO,KAAK,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,SAAK,eAAe,MAAM,GAAE,KAAK,eAAe,OAAO,GAAE,KAAK,eAAe,KAAK,GAAE,KAAK,eAAe,QAAQ;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;", "names": ["t", "p"]}