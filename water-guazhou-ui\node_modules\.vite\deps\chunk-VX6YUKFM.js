import {
  m
} from "./chunk-6ILWLF72.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  i
} from "./chunk-VIXZ7ZAD.js";
import {
  o as o2
} from "./chunk-G5KX4JSG.js";
import {
  l
} from "./chunk-T23PB46T.js";
import {
  o
} from "./chunk-PEEUPDEG.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/symbols/SimpleMarkerSymbol.js
var h;
var m2 = new s({ esriSMSCircle: "circle", esriSMSSquare: "square", esriSMSCross: "cross", esriSMSX: "x", esriSMSDiamond: "diamond", esriSMSTriangle: "triangle", esriSMSPath: "path" });
var u = h = class extends i {
  constructor(...e2) {
    super(...e2), this.color = new l([255, 255, 255, 0.25]), this.type = "simple-marker", this.size = 12, this.style = "circle", this.outline = new m();
  }
  normalizeCtorArgs(e2, o3, r2, t) {
    if (e2 && "string" != typeof e2) return e2;
    const i2 = {};
    return e2 && (i2.style = e2), null != o3 && (i2.size = o2(o3)), r2 && (i2.outline = r2), t && (i2.color = t), i2;
  }
  writeColor(e2, o3) {
    e2 && "x" !== this.style && "cross" !== this.style && (o3.color = e2.toJSON()), null === e2 && (o3.color = null);
  }
  set path(e2) {
    this.style = "path", this._set("path", e2);
  }
  clone() {
    return new h({ angle: this.angle, color: p(this.color), outline: this.outline && this.outline.clone(), path: this.path, size: this.size, style: this.style, xoffset: this.xoffset, yoffset: this.yoffset });
  }
  hash() {
    var _a;
    return `${super.hash()}.${this.color && this.color.hash()}.${this.path}.${this.style}.${(_a = this.outline) == null ? void 0 : _a.hash()}`;
  }
};
e([y()], u.prototype, "color", void 0), e([r("color")], u.prototype, "writeColor", null), e([o({ esriSMS: "simple-marker" }, { readOnly: true })], u.prototype, "type", void 0), e([y()], u.prototype, "size", void 0), e([y({ type: m2.apiValues, json: { read: m2.read, write: m2.write } })], u.prototype, "style", void 0), e([y({ type: String, json: { write: true } })], u.prototype, "path", null), e([y({ types: { key: "type", base: null, defaultKeyValue: "simple-line", typeMap: { "simple-line": m } }, json: { default: null, write: true } })], u.prototype, "outline", void 0), u = h = e([a("esri.symbols.SimpleMarkerSymbol")], u);
var y2 = u;

export {
  y2 as y
};
//# sourceMappingURL=chunk-VX6YUKFM.js.map
