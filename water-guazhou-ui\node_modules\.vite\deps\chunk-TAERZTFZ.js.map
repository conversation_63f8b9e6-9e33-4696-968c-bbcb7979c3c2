{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/BitmapTile.js", "../../@arcgis/core/views/2d/engine/BitmapTileContainer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as e}from\"../../../chunks/mat3f32.js\";import{Bitmap as t}from\"./Bitmap.js\";import{TiledDisplayObject as s}from\"./webgl/TiledDisplayObject.js\";class r extends s{constructor(e,s,r,i,a,n,m=null){super(e,s,r,i,a,n),this.bitmap=new t(m,{immutable:!1,requestRenderOnSourceChangedEnabled:!1}),this.bitmap.coordScale=[a,n],this.bitmap.once(\"isReady\",(()=>this.ready()))}destroy(){super.destroy(),this.bitmap.destroy()}beforeRender(e){super.beforeRender(e),this.bitmap.beforeRender(e)}afterRender(e){super.afterRender(e),this.bitmap.afterRender(e)}set stencilRef(e){this.bitmap.stencilRef=e}get stencilRef(){return this.bitmap.stencilRef}_createTransforms(){return{dvs:e(),tileMat3:e()}}setTransform(e){super.setTransform(e),this.bitmap.transforms.dvs=this.transforms.dvs}onAttach(){this.bitmap.stage=this.stage}onDetach(){this.bitmap&&(this.bitmap.stage=null)}}export{r as BitmapTile};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{create as e}from\"../../../geometry/support/aaBoundingRect.js\";import{BitmapTile as i}from\"./BitmapTile.js\";import{brushes as r}from\"./brushes.js\";import{WGLDrawPhase as t}from\"./webgl/enums.js\";import s from\"./webgl/TileContainer.js\";class n extends s{get requiresDedicatedFBO(){return this.children.some((e=>\"additive\"===e.bitmap.blendFunction))}createTile(r){const t=this._tileInfoView.getTileBounds(e(),r),s=this._tileInfoView.getTileResolution(r.level),[n,o]=this._tileInfoView.tileInfo.size;return new i(r,s,t[0],t[3],n,o)}prepareRenderPasses(e){const i=e.registerRenderPass({name:\"bitmap (tile)\",brushes:[r.bitmap],target:()=>this.children.map((e=>e.bitmap)),drawPhase:t.MAP});return[...super.prepareRenderPasses(e),i]}doRender(e){this.visible&&e.drawPhase===t.MAP&&super.doRender(e)}}export{n as BitmapTileContainer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAIuJ,IAAMA,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAE,GAAED,IAAEE,IAAE,GAAEC,IAAE,IAAE,MAAK;AAAC,UAAMF,IAAE,GAAED,IAAEE,IAAE,GAAEC,EAAC,GAAE,KAAK,SAAO,IAAI,EAAE,GAAE,EAAC,WAAU,OAAG,qCAAoC,MAAE,CAAC,GAAE,KAAK,OAAO,aAAW,CAAC,GAAEA,EAAC,GAAE,KAAK,OAAO,KAAK,WAAW,MAAI,KAAK,MAAM,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,OAAO,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAaF,IAAE;AAAC,UAAM,aAAaA,EAAC,GAAE,KAAK,OAAO,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAM,YAAYA,EAAC,GAAE,KAAK,OAAO,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAWA,IAAE;AAAC,SAAK,OAAO,aAAWA;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,OAAO;AAAA,EAAU;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAI,EAAE,GAAE,UAAS,EAAE,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAM,aAAaA,EAAC,GAAE,KAAK,OAAO,WAAW,MAAI,KAAK,WAAW;AAAA,EAAG;AAAA,EAAC,WAAU;AAAC,SAAK,OAAO,QAAM,KAAK;AAAA,EAAK;AAAA,EAAC,WAAU;AAAC,SAAK,WAAS,KAAK,OAAO,QAAM;AAAA,EAAK;AAAC;;;ACA5mB,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,SAAS,KAAM,CAAAG,OAAG,eAAaA,GAAE,OAAO,aAAc;AAAA,EAAC;AAAA,EAAC,WAAWC,IAAE;AAAC,UAAM,IAAE,KAAK,cAAc,cAAc,EAAE,GAAEA,EAAC,GAAE,IAAE,KAAK,cAAc,kBAAkBA,GAAE,KAAK,GAAE,CAACC,IAAE,CAAC,IAAE,KAAK,cAAc,SAAS;AAAK,WAAO,IAAID,GAAEA,IAAE,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAEC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAE;AAAC,UAAMG,KAAEH,GAAE,mBAAmB,EAAC,MAAK,iBAAgB,SAAQ,CAAC,EAAE,MAAM,GAAE,QAAO,MAAI,KAAK,SAAS,IAAK,CAAAA,OAAGA,GAAE,MAAO,GAAE,WAAU,EAAE,IAAG,CAAC;AAAE,WAAM,CAAC,GAAG,MAAM,oBAAoBA,EAAC,GAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,SAASH,IAAE;AAAC,SAAK,WAASA,GAAE,cAAY,EAAE,OAAK,MAAM,SAASA,EAAC;AAAA,EAAC;AAAC;", "names": ["r", "e", "i", "n", "e", "r", "n", "i"]}