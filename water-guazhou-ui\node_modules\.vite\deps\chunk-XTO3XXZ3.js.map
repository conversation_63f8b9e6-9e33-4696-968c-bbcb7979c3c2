{"version": 3, "sources": ["../../@arcgis/core/geometry/support/contains.js", "../../@arcgis/core/geometry/support/intersectsBase.js", "../../@arcgis/core/geometry/Extent.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as n}from\"../../core/maybe.js\";const t=[0,0];function r(t,r){return!!n(r)&&f(t,r.x,r.y,r.z)}function i(n,t){if(!t.points||t.points.length)return!1;for(const r of t.points)if(!u(n,r))return!1;return!0}function o(n,t){const{xmin:r,ymin:i,zmin:o,xmax:u,ymax:e,zmax:c}=t;return n.hasZ&&t.hasZ?f(n,r,i,o)&&f(n,r,e,o)&&f(n,u,e,o)&&f(n,u,i,o)&&f(n,r,i,c)&&f(n,r,e,c)&&f(n,u,e,c)&&f(n,u,i,c):f(n,r,i)&&f(n,r,e)&&f(n,u,e)&&f(n,u,i)}function u(n,t){return f(n,t[0],t[1])}function e(n,t){return f(n,t[0],t[1],t[2])}function f(n,t,r,i){return t>=n.xmin&&t<=n.xmax&&r>=n.ymin&&r<=n.ymax&&(null==i||!n.hasZ||i>=n.zmin&&i<=n.zmax)}function c(n,r){return t[1]=r.y,t[0]=r.x,m(n,t)}function m(n,t){return s(n.rings,t)}function s(n,t){if(!n)return!1;if(x(n))return a(!1,n,t);let r=!1;for(let i=0,o=n.length;i<o;i++)r=a(r,n[i],t);return r}function x(n){return!Array.isArray(n[0][0])}function a(n,t,r){const[i,o]=r;let u=n,e=0;for(let f=0,c=t.length;f<c;f++){e++,e===c&&(e=0);const[n,r]=t[f],[m,s]=t[e];(r<o&&s>=o||s<o&&r>=o)&&n+(o-r)/(s-r)*(m-n)<i&&(u=!u)}return u}export{u as extentContainsCoords2D,e as extentContainsCoords3D,o as extentContainsExtent,i as extentContainsMultipoint,r as extentContainsPoint,f as extentContainsXYZ,m as polygonContainsCoords,c as polygonContainsPoint,s as ringsContainsCoords};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{extentContainsPoint as n,ringsContainsCoords as t,extentContainsCoords2D as e,extentContainsCoords3D as r}from\"./contains.js\";function i(t,e){return n(t,e)}function o(n,t){const e=n.hasZ&&t.hasZ;let r,i,o;if(n.xmin<=t.xmin){if(r=t.xmin,n.xmax<r)return!1}else if(r=n.xmin,t.xmax<r)return!1;if(n.ymin<=t.ymin){if(i=t.ymin,n.ymax<i)return!1}else if(i=n.ymin,t.ymax<i)return!1;if(e&&t.hasZ)if(n.zmin<=t.zmin){if(o=t.zmin,n.zmax<o)return!1}else if(o=n.zmin,t.zmax<o)return!1;return!0}function f(n,t){const{points:i,hasZ:o}=t,f=o?r:e;for(const e of i)if(f(n,e))return!0;return!1}const s=[0,0],u=[0,0],c=[0,0],m=[0,0],l=[s,u,c,m],a=[[c,s],[s,u],[u,m],[m,c]];function x(n,t){return y(n,t.rings)}function y(n,r){s[0]=n.xmin,s[1]=n.ymax,u[0]=n.xmax,u[1]=n.ymax,c[0]=n.xmin,c[1]=n.ymin,m[0]=n.xmax,m[1]=n.ymin;for(const e of l)if(t(r,e))return!0;for(const t of r){if(!t.length)continue;let r=t[0];if(e(n,r))return!0;for(let i=1;i<t.length;i++){const o=t[i];if(e(n,o)||z(r,o,a))return!0;r=o}}return!1}function h(n,t){s[0]=n.xmin,s[1]=n.ymax,u[0]=n.xmax,u[1]=n.ymax,c[0]=n.xmin,c[1]=n.ymin,m[0]=n.xmax,m[1]=n.ymin;const r=t.paths;for(const i of r){if(!r.length)continue;let t=i[0];if(e(n,t))return!0;for(let r=1;r<i.length;r++){const o=i[r];if(e(n,o)||z(t,o,a))return!0;t=o}}return!1}const g=[0,0];function p(n){for(let t=0;t<n.length;t++){const e=n[t];for(let i=0;i<e.length-1;i++){const r=e[i],o=e[i+1];for(let e=t+1;e<n.length;e++)for(let t=0;t<n[e].length-1;t++){const i=n[e][t],f=n[e][t+1];if(G(r,o,i,f,g)&&!(g[0]===r[0]&&g[1]===r[1]||g[0]===i[0]&&g[1]===i[1]||g[0]===o[0]&&g[1]===o[1]||g[0]===f[0]&&g[1]===f[1]))return!0}}const r=e.length;if(!(r<=4))for(let n=0;n<r-3;n++){let t=r-1;0===n&&(t=r-2);const i=e[n],o=e[n+1];for(let r=n+2;r<t;r++){const n=e[r],t=e[r+1];if(G(i,o,n,t,g)&&!(g[0]===i[0]&&g[1]===i[1]||g[0]===n[0]&&g[1]===n[1]||g[0]===o[0]&&g[1]===o[1]||g[0]===t[0]&&g[1]===t[1]))return!0}}}return!1}function z(n,t,e){for(let r=0;r<e.length;r++)if(G(n,t,e[r][0],e[r][1]))return!0;return!1}function G(n,t,e,r,i){const[o,f]=n,[s,u]=t,[c,m]=e,[l,a]=r,x=l-c,y=o-c,h=s-o,g=a-m,p=f-m,z=u-f,G=g*h-x*z;if(0===G)return!1;const Z=(x*p-g*y)/G,P=(h*p-z*y)/G;return Z>=0&&Z<=1&&P>=0&&P<=1&&(i&&(i[0]=o+Z*(s-o),i[1]=f+Z*(u-f)),!0)}function Z(n){switch(n){case\"esriGeometryEnvelope\":case\"extent\":return o;case\"esriGeometryMultipoint\":case\"multipoint\":return f;case\"esriGeometryPoint\":case\"point\":return i;case\"esriGeometryPolygon\":case\"polygon\":return x;case\"esriGeometryPolyline\":case\"polyline\":return h}}export{o as extentIntersectsExtent,f as extentIntersectsMultipoint,i as extentIntersectsPoint,x as extentIntersectsPolygon,h as extentIntersectsPolyline,y as extentIntersectsRings,Z as getFeatureExtentIntersector,p as isSelfIntersecting,G as segmentIntersects};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import{isNone as i}from\"../core/maybe.js\";import{replace as e}from\"../core/string.js\";import{property as s}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as n}from\"../core/accessorSupport/decorators/subclass.js\";import m from\"./Geometry.js\";import r from\"./Point.js\";import a from\"./SpatialReference.js\";import{extentContainsPoint as h,extentContainsExtent as o}from\"./support/contains.js\";import{getFeatureExtentIntersector as x}from\"./support/intersectsBase.js\";import{equals as l,getInfo as p}from\"./support/spatialReferenceUtils.js\";import{canProject as c,geographicToWebMercator as y,webMercatorToGeographic as u}from\"./support/webMercatorUtils.js\";var f;function d(t){return t&&(\"esri.geometry.SpatialReference\"===t.declaredClass||null!=t.wkid)}function z(t,i,e){return null==i?e:null==e?i:t(i,e)}let M=f=class extends m{constructor(...t){super(...t),this.type=\"extent\",this.xmin=0,this.ymin=0,this.mmin=void 0,this.zmin=void 0,this.xmax=0,this.ymax=0,this.mmax=void 0,this.zmax=void 0}normalizeCtorArgs(t,i,e,s,n){return d(t)?{spatialReference:t,xmin:0,ymin:0,xmax:0,ymax:0}:\"object\"==typeof t?(t.spatialReference=null==t.spatialReference?a.WGS84:t.spatialReference,t):{xmin:t,ymin:i,xmax:e,ymax:s,spatialReference:n??a.WGS84}}static fromBounds(t,i){return new f({xmin:t[0],ymin:t[1],xmax:t[2],ymax:t[3],spatialReference:i})}static fromPoint(t){return new f({xmin:t.x,ymin:t.y,zmin:t.z,xmax:t.x,ymax:t.y,zmax:t.z,spatialReference:t.spatialReference})}get cache(){return this.commitProperty(\"xmin\"),this.commitProperty(\"ymin\"),this.commitProperty(\"zmin\"),this.commitProperty(\"mmin\"),this.commitProperty(\"xmax\"),this.commitProperty(\"ymax\"),this.commitProperty(\"zmax\"),this.commitProperty(\"mmax\"),this.commitProperty(\"spatialReference\"),{}}get center(){const t=new r({x:.5*(this.xmin+this.xmax),y:.5*(this.ymin+this.ymax),spatialReference:this.spatialReference});return this.hasZ&&(t.z=.5*(this.zmin+this.zmax)),this.hasM&&(t.m=.5*(this.mmin+this.mmax)),t}get extent(){return this.clone()}get hasM(){return null!=this.mmin&&null!=this.mmax}get hasZ(){return null!=this.zmin&&null!=this.zmax}get height(){return Math.abs(this.ymax-this.ymin)}get width(){return Math.abs(this.xmax-this.xmin)}centerAt(t){const i=this.center;return null!=t.z&&this.hasZ?this.offset(t.x-i.x,t.y-i.y,t.z-i.z):this.offset(t.x-i.x,t.y-i.y)}clone(){const t=new f;return t.xmin=this.xmin,t.ymin=this.ymin,t.xmax=this.xmax,t.ymax=this.ymax,t.spatialReference=this.spatialReference,null!=this.zmin&&(t.zmin=this.zmin,t.zmax=this.zmax),null!=this.mmin&&(t.mmin=this.mmin,t.mmax=this.mmax),t}contains(t){if(!t)return!1;const i=this.spatialReference,e=t.spatialReference;return i&&e&&!i.equals(e)&&c(i,e)&&(t=i.isWebMercator?y(t):u(t,!0)),\"point\"===t.type?h(this,t):\"extent\"===t.type&&o(this,t)}equals(t){if(this===t)return!0;if(i(t))return!1;const e=this.spatialReference,s=t.spatialReference;return e&&s&&!e.equals(s)&&c(e,s)&&(t=e.isWebMercator?y(t):u(t,!0)),this.xmin===t.xmin&&this.ymin===t.ymin&&this.zmin===t.zmin&&this.mmin===t.mmin&&this.xmax===t.xmax&&this.ymax===t.ymax&&this.zmax===t.zmax&&this.mmax===t.mmax}expand(t){const i=.5*(1-t),e=this.width*i,s=this.height*i;if(this.xmin+=e,this.ymin+=s,this.xmax-=e,this.ymax-=s,this.hasZ){const t=(this.zmax-this.zmin)*i;this.zmin+=t,this.zmax-=t}if(this.hasM){const t=(this.mmax-this.mmin)*i;this.mmin+=t,this.mmax-=t}return this}intersects(t){if(i(t))return!1;\"mesh\"===t.type&&(t=t.extent);const e=this.spatialReference,s=t.spatialReference;e&&s&&!l(e,s)&&c(e,s)&&(t=e.isWebMercator?y(t):u(t,!0));return x(t.type)(this,t)}normalize(){const t=this._normalize(!1,!0);return Array.isArray(t)?t:[t]}offset(t,i,e){return this.xmin+=t,this.ymin+=i,this.xmax+=t,this.ymax+=i,null!=e&&(this.zmin+=e,this.zmax+=e),this}shiftCentralMeridian(){return this._normalize(!0)}union(t){return this===t||(this.xmin=Math.min(this.xmin,t.xmin),this.ymin=Math.min(this.ymin,t.ymin),this.xmax=Math.max(this.xmax,t.xmax),this.ymax=Math.max(this.ymax,t.ymax),(this.hasZ||t.hasZ)&&(this.zmin=z(Math.min,this.zmin,t.zmin),this.zmax=z(Math.max,this.zmax,t.zmax)),(this.hasM||t.hasM)&&(this.mmin=z(Math.min,this.mmin,t.mmin),this.mmax=z(Math.max,this.mmax,t.mmax))),this}intersection(t){return this===t?this:i(t)||!this.intersects(t)?null:(this.xmin=Math.max(this.xmin,t.xmin),this.ymin=Math.max(this.ymin,t.ymin),this.xmax=Math.min(this.xmax,t.xmax),this.ymax=Math.min(this.ymax,t.ymax),(this.hasZ||t.hasZ)&&(this.zmin=z(Math.max,this.zmin,t.zmin),this.zmax=z(Math.min,this.zmax,t.zmax)),(this.hasM||t.hasM)&&(this.mmin=z(Math.max,this.mmin,t.mmin),this.mmax=z(Math.min,this.mmax,t.mmax)),this)}toJSON(t){return this.write({},t)}_shiftCM(t=p(this.spatialReference)){if(!t||!this.spatialReference)return this;const i=this.spatialReference,s=this._getCM(t);if(s){const n=i.isWebMercator?u(s):s;this.xmin-=s.x,this.xmax-=s.x,i.isWebMercator||(n.x=this._normalizeX(n.x,t).x),this.spatialReference=new a(e((i.isWGS84?t.altTemplate:null)??t.wkTemplate,{Central_Meridian:n.x}))}return this}_getCM(t){let i=null;const[e,s]=t.valid,n=this.xmin,m=this.xmax;return n>=e&&n<=s&&(m>=e&&m<=s)||(i=this.center),i}_normalize(t,i,e){const s=this.spatialReference;if(!s)return this;const n=e??p(s);if(null==n)return this;const m=this._getParts(n).map((t=>t.extent));if(m.length<2)return m[0]||this;if(m.length>2)return t?this._shiftCM(n):this.set({xmin:n.valid[0],xmax:n.valid[1]});if(t)return this._shiftCM(n);if(i)return m;let r=!0,a=!0;return m.forEach((t=>{t.hasZ||(r=!1),t.hasM||(a=!1)})),{rings:m.map((t=>{const i=[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]];if(r){const e=(t.zmax-t.zmin)/2;for(let t=0;t<i.length;t++)i[t].push(e)}if(a){const e=(t.mmax-t.mmin)/2;for(let t=0;t<i.length;t++)i[t].push(e)}return i})),hasZ:r,hasM:a,spatialReference:s}}_getParts(t){let i=this.cache._parts;if(!i){i=[];const{ymin:e,ymax:s,spatialReference:n}=this,m=this.width,r=this.xmin,a=this.xmax;let h;t=t||p(n);const[o,x]=t.valid;h=this._normalizeX(this.xmin,t);const l=h.x,c=h.frameId;h=this._normalizeX(this.xmax,t);const y=h.x,u=h.frameId,d=l===y&&m>0;if(m>2*x){const t=new f(r<a?l:y,e,x,s,n),m=new f(o,e,r<a?y:l,s,n),h=new f(0,e,x,s,n),p=new f(o,e,0,s,n),d=[],z=[];t.contains(h)&&d.push(c),t.contains(p)&&z.push(c),m.contains(h)&&d.push(u),m.contains(p)&&z.push(u);for(let i=c+1;i<u;i++)d.push(i),z.push(i);i.push({extent:t,frameIds:[c]},{extent:m,frameIds:[u]},{extent:h,frameIds:d},{extent:p,frameIds:z})}else l>y||d?i.push({extent:new f(l,e,x,s,n),frameIds:[c]},{extent:new f(o,e,y,s,n),frameIds:[u]}):i.push({extent:new f(l,e,y,s,n),frameIds:[c]});this.cache._parts=i}const e=this.hasZ,s=this.hasM;if(e||s){const t={};e&&(t.zmin=this.zmin,t.zmax=this.zmax),s&&(t.mmin=this.mmin,t.mmax=this.mmax);for(let e=0;e<i.length;e++)i[e].extent.set(t)}return i}_normalizeX(t,i){const[e,s]=i.valid,n=2*s;let m,r=0;return t>s?(m=Math.ceil(Math.abs(t-s)/n),t-=m*n,r=m):t<e&&(m=Math.ceil(Math.abs(t-e)/n),t+=m*n,r=-m),{x:t,frameId:r}}};t([s({readOnly:!0})],M.prototype,\"cache\",null),t([s({readOnly:!0})],M.prototype,\"center\",null),t([s({readOnly:!0})],M.prototype,\"extent\",null),t([s({readOnly:!0,json:{write:{enabled:!1,overridePolicy:null}}})],M.prototype,\"hasM\",null),t([s({readOnly:!0,json:{write:{enabled:!1,overridePolicy:null}}})],M.prototype,\"hasZ\",null),t([s({readOnly:!0})],M.prototype,\"height\",null),t([s({readOnly:!0})],M.prototype,\"width\",null),t([s({type:Number,json:{type:[Number,String],write:{enabled:!0,allowNull:!0}}})],M.prototype,\"xmin\",void 0),t([s({type:Number,json:{write:!0}})],M.prototype,\"ymin\",void 0),t([s({type:Number,json:{origins:{\"web-scene\":{write:!1}},write:{overridePolicy(){return{enabled:this.hasM}}}}})],M.prototype,\"mmin\",void 0),t([s({type:Number,json:{origins:{\"web-scene\":{write:!1}},write:{overridePolicy(){return{enabled:this.hasZ}}}}})],M.prototype,\"zmin\",void 0),t([s({type:Number,json:{write:!0}})],M.prototype,\"xmax\",void 0),t([s({type:Number,json:{write:!0}})],M.prototype,\"ymax\",void 0),t([s({type:Number,json:{origins:{\"web-scene\":{write:!1}},write:{overridePolicy(){return{enabled:this.hasM}}}}})],M.prototype,\"mmax\",void 0),t([s({type:Number,json:{origins:{\"web-scene\":{write:!1}},write:{overridePolicy(){return{enabled:this.hasZ}}}}})],M.prototype,\"zmax\",void 0),M=f=t([n(\"esri.geometry.Extent\")],M),M.prototype.toJSON.isDefaultToJSON=!0;const w=M;export{w as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI6C,IAAMA,KAAE,CAAC,GAAE,CAAC;AAAE,SAASC,GAAED,IAAEC,IAAE;AAAC,SAAM,CAAC,CAAC,EAAEA,EAAC,KAAGC,GAAEF,IAAEC,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAED,IAAE;AAAC,MAAG,CAACA,GAAE,UAAQA,GAAE,OAAO,OAAO,QAAM;AAAG,aAAUC,MAAKD,GAAE,OAAO,KAAG,CAAC,EAAE,GAAEC,EAAC,EAAE,QAAM;AAAG,SAAM;AAAE;AAAC,SAAS,EAAE,GAAED,IAAE;AAAC,QAAK,EAAC,MAAKC,IAAE,MAAKE,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKC,GAAC,IAAEP;AAAE,SAAO,EAAE,QAAMA,GAAE,OAAKE,GAAE,GAAED,IAAEE,IAAEC,EAAC,KAAGF,GAAE,GAAED,IAAEK,IAAEF,EAAC,KAAGF,GAAE,GAAEG,IAAEC,IAAEF,EAAC,KAAGF,GAAE,GAAEG,IAAEF,IAAEC,EAAC,KAAGF,GAAE,GAAED,IAAEE,IAAEI,EAAC,KAAGL,GAAE,GAAED,IAAEK,IAAEC,EAAC,KAAGL,GAAE,GAAEG,IAAEC,IAAEC,EAAC,KAAGL,GAAE,GAAEG,IAAEF,IAAEI,EAAC,IAAEL,GAAE,GAAED,IAAEE,EAAC,KAAGD,GAAE,GAAED,IAAEK,EAAC,KAAGJ,GAAE,GAAEG,IAAEC,EAAC,KAAGJ,GAAE,GAAEG,IAAEF,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEH,IAAE;AAAC,SAAOE,GAAE,GAAEF,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAC;AAAC,SAASM,GAAE,GAAEN,IAAE;AAAC,SAAOE,GAAE,GAAEF,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAC;AAAC,SAASE,GAAE,GAAEF,IAAEC,IAAEE,IAAE;AAAC,SAAOH,MAAG,EAAE,QAAMA,MAAG,EAAE,QAAMC,MAAG,EAAE,QAAMA,MAAG,EAAE,SAAO,QAAME,MAAG,CAAC,EAAE,QAAMA,MAAG,EAAE,QAAMA,MAAG,EAAE;AAAK;AAAC,SAAS,EAAE,GAAEF,IAAE;AAAC,SAAOD,GAAE,CAAC,IAAEC,GAAE,GAAED,GAAE,CAAC,IAAEC,GAAE,GAAE,EAAE,GAAED,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,SAAO,EAAE,EAAE,OAAMA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAG,CAAC,EAAE,QAAM;AAAG,MAAG,EAAE,CAAC,EAAE,QAAOQ,GAAE,OAAG,GAAER,EAAC;AAAE,MAAIC,KAAE;AAAG,WAAQE,KAAE,GAAEC,KAAE,EAAE,QAAOD,KAAEC,IAAED,KAAI,CAAAF,KAAEO,GAAEP,IAAE,EAAEE,EAAC,GAAEH,EAAC;AAAE,SAAOC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,CAAC,MAAM,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAAC;AAAC,SAASO,GAAE,GAAER,IAAEC,IAAE;AAAC,QAAK,CAACE,IAAEC,EAAC,IAAEH;AAAE,MAAII,KAAE,GAAEC,KAAE;AAAE,WAAQJ,KAAE,GAAEK,KAAEP,GAAE,QAAOE,KAAEK,IAAEL,MAAI;AAAC,IAAAI,MAAIA,OAAIC,OAAID,KAAE;AAAG,UAAK,CAACG,IAAER,EAAC,IAAED,GAAEE,EAAC,GAAE,CAACQ,IAAEC,EAAC,IAAEX,GAAEM,EAAC;AAAE,KAACL,KAAEG,MAAGO,MAAGP,MAAGO,KAAEP,MAAGH,MAAGG,OAAIK,MAAGL,KAAEH,OAAIU,KAAEV,OAAIS,KAAED,MAAGN,OAAIE,KAAE,CAACA;AAAA,EAAE;AAAC,SAAOA;AAAC;;;ACA75B,SAASO,GAAEC,IAAEC,IAAE;AAAC,SAAOC,GAAEF,IAAEC,EAAC;AAAC;AAAC,SAASE,GAAE,GAAEH,IAAE;AAAC,QAAMC,KAAE,EAAE,QAAMD,GAAE;AAAK,MAAIE,IAAEH,IAAEI;AAAE,MAAG,EAAE,QAAMH,GAAE,MAAK;AAAC,QAAGE,KAAEF,GAAE,MAAK,EAAE,OAAKE,GAAE,QAAM;AAAA,EAAE,WAASA,KAAE,EAAE,MAAKF,GAAE,OAAKE,GAAE,QAAM;AAAG,MAAG,EAAE,QAAMF,GAAE,MAAK;AAAC,QAAGD,KAAEC,GAAE,MAAK,EAAE,OAAKD,GAAE,QAAM;AAAA,EAAE,WAASA,KAAE,EAAE,MAAKC,GAAE,OAAKD,GAAE,QAAM;AAAG,MAAGE,MAAGD,GAAE;AAAK,QAAG,EAAE,QAAMA,GAAE,MAAK;AAAC,UAAGG,KAAEH,GAAE,MAAK,EAAE,OAAKG,GAAE,QAAM;AAAA,IAAE,WAASA,KAAE,EAAE,MAAKH,GAAE,OAAKG,GAAE,QAAM;AAAA;AAAG,SAAM;AAAE;AAAC,SAASC,GAAE,GAAEJ,IAAE;AAAC,QAAK,EAAC,QAAOD,IAAE,MAAKI,GAAC,IAAEH,IAAEI,KAAED,KAAEF,KAAE;AAAE,aAAUA,MAAKF,GAAE,KAAGK,GAAE,GAAEH,EAAC,EAAE,QAAM;AAAG,SAAM;AAAE;AAAC,IAAMI,KAAE,CAAC,GAAE,CAAC;AAAZ,IAAcC,KAAE,CAAC,GAAE,CAAC;AAApB,IAAsBC,KAAE,CAAC,GAAE,CAAC;AAA5B,IAA8BC,KAAE,CAAC,GAAE,CAAC;AAApC,IAAsC,IAAE,CAACH,IAAEC,IAAEC,IAAEC,EAAC;AAAhD,IAAkDC,KAAE,CAAC,CAACF,IAAEF,EAAC,GAAE,CAACA,IAAEC,EAAC,GAAE,CAACA,IAAEE,EAAC,GAAE,CAACA,IAAED,EAAC,CAAC;AAAE,SAASG,GAAE,GAAEV,IAAE;AAAC,SAAOW,GAAE,GAAEX,GAAE,KAAK;AAAC;AAAC,SAASW,GAAE,GAAET,IAAE;AAAC,EAAAG,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE,MAAKC,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE,MAAKC,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE,MAAKC,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE;AAAK,aAAUP,MAAK,EAAE,KAAG,EAAEC,IAAED,EAAC,EAAE,QAAM;AAAG,aAAUD,MAAKE,IAAE;AAAC,QAAG,CAACF,GAAE,OAAO;AAAS,QAAIE,KAAEF,GAAE,CAAC;AAAE,QAAG,EAAE,GAAEE,EAAC,EAAE,QAAM;AAAG,aAAQH,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAI;AAAC,YAAMI,KAAEH,GAAED,EAAC;AAAE,UAAG,EAAE,GAAEI,EAAC,KAAG,EAAED,IAAEC,IAAEM,EAAC,EAAE,QAAM;AAAG,MAAAP,KAAEC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM;AAAE;AAAC,SAAS,EAAE,GAAEH,IAAE;AAAC,EAAAK,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE,MAAKC,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE,MAAKC,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE,MAAKC,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE;AAAK,QAAMN,KAAEF,GAAE;AAAM,aAAUD,MAAKG,IAAE;AAAC,QAAG,CAACA,GAAE,OAAO;AAAS,QAAIF,KAAED,GAAE,CAAC;AAAE,QAAG,EAAE,GAAEC,EAAC,EAAE,QAAM;AAAG,aAAQE,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAI;AAAC,YAAMC,KAAEJ,GAAEG,EAAC;AAAE,UAAG,EAAE,GAAEC,EAAC,KAAG,EAAEH,IAAEG,IAAEM,EAAC,EAAE,QAAM;AAAG,MAAAT,KAAEG;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM;AAAE;AAAC,IAAMS,KAAE,CAAC,GAAE,CAAC;AAAE,SAASC,GAAE,GAAE;AAAC,WAAQb,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,aAAQD,KAAE,GAAEA,KAAEE,GAAE,SAAO,GAAEF,MAAI;AAAC,YAAMG,KAAED,GAAEF,EAAC,GAAEI,KAAEF,GAAEF,KAAE,CAAC;AAAE,eAAQE,KAAED,KAAE,GAAEC,KAAE,EAAE,QAAOA,KAAI,UAAQD,KAAE,GAAEA,KAAE,EAAEC,EAAC,EAAE,SAAO,GAAED,MAAI;AAAC,cAAMD,KAAE,EAAEE,EAAC,EAAED,EAAC,GAAEI,KAAE,EAAEH,EAAC,EAAED,KAAE,CAAC;AAAE,YAAG,EAAEE,IAAEC,IAAEJ,IAAEK,IAAEQ,EAAC,KAAG,EAAEA,GAAE,CAAC,MAAIV,GAAE,CAAC,KAAGU,GAAE,CAAC,MAAIV,GAAE,CAAC,KAAGU,GAAE,CAAC,MAAIb,GAAE,CAAC,KAAGa,GAAE,CAAC,MAAIb,GAAE,CAAC,KAAGa,GAAE,CAAC,MAAIT,GAAE,CAAC,KAAGS,GAAE,CAAC,MAAIT,GAAE,CAAC,KAAGS,GAAE,CAAC,MAAIR,GAAE,CAAC,KAAGQ,GAAE,CAAC,MAAIR,GAAE,CAAC,GAAG,QAAM;AAAA,MAAE;AAAA,IAAC;AAAC,UAAMF,KAAED,GAAE;AAAO,QAAG,EAAEC,MAAG,GAAG,UAAQY,KAAE,GAAEA,KAAEZ,KAAE,GAAEY,MAAI;AAAC,UAAId,KAAEE,KAAE;AAAE,YAAIY,OAAId,KAAEE,KAAE;AAAG,YAAMH,KAAEE,GAAEa,EAAC,GAAEX,KAAEF,GAAEa,KAAE,CAAC;AAAE,eAAQZ,KAAEY,KAAE,GAAEZ,KAAEF,IAAEE,MAAI;AAAC,cAAMY,KAAEb,GAAEC,EAAC,GAAEF,KAAEC,GAAEC,KAAE,CAAC;AAAE,YAAG,EAAEH,IAAEI,IAAEW,IAAEd,IAAEY,EAAC,KAAG,EAAEA,GAAE,CAAC,MAAIb,GAAE,CAAC,KAAGa,GAAE,CAAC,MAAIb,GAAE,CAAC,KAAGa,GAAE,CAAC,MAAIE,GAAE,CAAC,KAAGF,GAAE,CAAC,MAAIE,GAAE,CAAC,KAAGF,GAAE,CAAC,MAAIT,GAAE,CAAC,KAAGS,GAAE,CAAC,MAAIT,GAAE,CAAC,KAAGS,GAAE,CAAC,MAAIZ,GAAE,CAAC,KAAGY,GAAE,CAAC,MAAIZ,GAAE,CAAC,GAAG,QAAM;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM;AAAE;AAAC,SAAS,EAAE,GAAEA,IAAEC,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,KAAG,EAAE,GAAEF,IAAEC,GAAEC,EAAC,EAAE,CAAC,GAAED,GAAEC,EAAC,EAAE,CAAC,CAAC,EAAE,QAAM;AAAG,SAAM;AAAE;AAAC,SAAS,EAAE,GAAEF,IAAEC,IAAEC,IAAEH,IAAE;AAAC,QAAK,CAACI,IAAEC,EAAC,IAAE,GAAE,CAACC,IAAEC,EAAC,IAAEN,IAAE,CAACO,IAAEC,EAAC,IAAEP,IAAE,CAACc,IAAEN,EAAC,IAAEP,IAAEQ,KAAEK,KAAER,IAAEI,KAAER,KAAEI,IAAES,KAAEX,KAAEF,IAAES,KAAEH,KAAED,IAAEK,KAAET,KAAEI,IAAES,KAAEX,KAAEF,IAAEc,KAAEN,KAAEI,KAAEN,KAAEO;AAAE,MAAG,MAAIC,GAAE,QAAM;AAAG,QAAMC,MAAGT,KAAEG,KAAED,KAAED,MAAGO,IAAE,KAAGF,KAAEH,KAAEI,KAAEN,MAAGO;AAAE,SAAOC,MAAG,KAAGA,MAAG,KAAG,KAAG,KAAG,KAAG,MAAIpB,OAAIA,GAAE,CAAC,IAAEI,KAAEgB,MAAGd,KAAEF,KAAGJ,GAAE,CAAC,IAAEK,KAAEe,MAAGb,KAAEF,MAAI;AAAG;AAAC,SAAS,EAAE,GAAE;AAAC,UAAO,GAAE;AAAA,IAAC,KAAI;AAAA,IAAuB,KAAI;AAAS,aAAOD;AAAA,IAAE,KAAI;AAAA,IAAyB,KAAI;AAAa,aAAOC;AAAA,IAAE,KAAI;AAAA,IAAoB,KAAI;AAAQ,aAAOL;AAAA,IAAE,KAAI;AAAA,IAAsB,KAAI;AAAU,aAAOW;AAAA,IAAE,KAAI;AAAA,IAAuB,KAAI;AAAW,aAAO;AAAA,EAAC;AAAC;;;ACAvqD,IAAIU;AAAE,SAAS,EAAEC,IAAE;AAAC,SAAOA,OAAI,qCAAmCA,GAAE,iBAAe,QAAMA,GAAE;AAAK;AAAC,SAASC,GAAED,IAAEE,IAAEC,IAAE;AAAC,SAAO,QAAMD,KAAEC,KAAE,QAAMA,KAAED,KAAEF,GAAEE,IAAEC,EAAC;AAAC;AAAC,IAAI,IAAEJ,KAAE,cAAc,EAAC;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,OAAK,UAAS,KAAK,OAAK,GAAE,KAAK,OAAK,GAAE,KAAK,OAAK,QAAO,KAAK,OAAK,QAAO,KAAK,OAAK,GAAE,KAAK,OAAK,GAAE,KAAK,OAAK,QAAO,KAAK,OAAK;AAAA,EAAM;AAAA,EAAC,kBAAkBA,IAAEE,IAAEC,IAAEC,IAAE,GAAE;AAAC,WAAO,EAAEJ,EAAC,IAAE,EAAC,kBAAiBA,IAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC,IAAE,YAAU,OAAOA,MAAGA,GAAE,mBAAiB,QAAMA,GAAE,mBAAiB,EAAE,QAAMA,GAAE,kBAAiBA,MAAG,EAAC,MAAKA,IAAE,MAAKE,IAAE,MAAKC,IAAE,MAAKC,IAAE,kBAAiB,KAAG,EAAE,MAAK;AAAA,EAAC;AAAA,EAAC,OAAO,WAAWJ,IAAEE,IAAE;AAAC,WAAO,IAAIH,GAAE,EAAC,MAAKC,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,kBAAiBE,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,UAAUF,IAAE;AAAC,WAAO,IAAID,GAAE,EAAC,MAAKC,GAAE,GAAE,MAAKA,GAAE,GAAE,MAAKA,GAAE,GAAE,MAAKA,GAAE,GAAE,MAAKA,GAAE,GAAE,MAAKA,GAAE,GAAE,kBAAiBA,GAAE,iBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,MAAM,GAAE,KAAK,eAAe,kBAAkB,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,UAAMA,KAAE,IAAI,EAAE,EAAC,GAAE,OAAI,KAAK,OAAK,KAAK,OAAM,GAAE,OAAI,KAAK,OAAK,KAAK,OAAM,kBAAiB,KAAK,iBAAgB,CAAC;AAAE,WAAO,KAAK,SAAOA,GAAE,IAAE,OAAI,KAAK,OAAK,KAAK,QAAO,KAAK,SAAOA,GAAE,IAAE,OAAI,KAAK,OAAK,KAAK,QAAOA;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,QAAM,KAAK,QAAM,QAAM,KAAK;AAAA,EAAI;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,QAAM,KAAK,QAAM,QAAM,KAAK;AAAA,EAAI;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,IAAI,KAAK,OAAK,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,IAAI,KAAK,OAAK,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,UAAME,KAAE,KAAK;AAAO,WAAO,QAAMF,GAAE,KAAG,KAAK,OAAK,KAAK,OAAOA,GAAE,IAAEE,GAAE,GAAEF,GAAE,IAAEE,GAAE,GAAEF,GAAE,IAAEE,GAAE,CAAC,IAAE,KAAK,OAAOF,GAAE,IAAEE,GAAE,GAAEF,GAAE,IAAEE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMF,KAAE,IAAID;AAAE,WAAOC,GAAE,OAAK,KAAK,MAAKA,GAAE,OAAK,KAAK,MAAKA,GAAE,OAAK,KAAK,MAAKA,GAAE,OAAK,KAAK,MAAKA,GAAE,mBAAiB,KAAK,kBAAiB,QAAM,KAAK,SAAOA,GAAE,OAAK,KAAK,MAAKA,GAAE,OAAK,KAAK,OAAM,QAAM,KAAK,SAAOA,GAAE,OAAK,KAAK,MAAKA,GAAE,OAAK,KAAK,OAAMA;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,QAAG,CAACA,GAAE,QAAM;AAAG,UAAME,KAAE,KAAK,kBAAiBC,KAAEH,GAAE;AAAiB,WAAOE,MAAGC,MAAG,CAACD,GAAE,OAAOC,EAAC,KAAG,EAAED,IAAEC,EAAC,MAAIH,KAAEE,GAAE,gBAAcG,GAAEL,EAAC,IAAE,EAAEA,IAAE,IAAE,IAAG,YAAUA,GAAE,OAAKM,GAAE,MAAKN,EAAC,IAAE,aAAWA,GAAE,QAAM,EAAE,MAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,QAAG,SAAOA,GAAE,QAAM;AAAG,QAAG,EAAEA,EAAC,EAAE,QAAM;AAAG,UAAMG,KAAE,KAAK,kBAAiBC,KAAEJ,GAAE;AAAiB,WAAOG,MAAGC,MAAG,CAACD,GAAE,OAAOC,EAAC,KAAG,EAAED,IAAEC,EAAC,MAAIJ,KAAEG,GAAE,gBAAcE,GAAEL,EAAC,IAAE,EAAEA,IAAE,IAAE,IAAG,KAAK,SAAOA,GAAE,QAAM,KAAK,SAAOA,GAAE,QAAM,KAAK,SAAOA,GAAE,QAAM,KAAK,SAAOA,GAAE,QAAM,KAAK,SAAOA,GAAE,QAAM,KAAK,SAAOA,GAAE,QAAM,KAAK,SAAOA,GAAE,QAAM,KAAK,SAAOA,GAAE;AAAA,EAAI;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAME,KAAE,OAAI,IAAEF,KAAGG,KAAE,KAAK,QAAMD,IAAEE,KAAE,KAAK,SAAOF;AAAE,QAAG,KAAK,QAAMC,IAAE,KAAK,QAAMC,IAAE,KAAK,QAAMD,IAAE,KAAK,QAAMC,IAAE,KAAK,MAAK;AAAC,YAAMJ,MAAG,KAAK,OAAK,KAAK,QAAME;AAAE,WAAK,QAAMF,IAAE,KAAK,QAAMA;AAAA,IAAC;AAAC,QAAG,KAAK,MAAK;AAAC,YAAMA,MAAG,KAAK,OAAK,KAAK,QAAME;AAAE,WAAK,QAAMF,IAAE,KAAK,QAAMA;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,WAAWA,IAAE;AAAC,QAAG,EAAEA,EAAC,EAAE,QAAM;AAAG,eAASA,GAAE,SAAOA,KAAEA,GAAE;AAAQ,UAAMG,KAAE,KAAK,kBAAiBC,KAAEJ,GAAE;AAAiB,IAAAG,MAAGC,MAAG,CAAC,EAAED,IAAEC,EAAC,KAAG,EAAED,IAAEC,EAAC,MAAIJ,KAAEG,GAAE,gBAAcE,GAAEL,EAAC,IAAE,EAAEA,IAAE,IAAE;AAAG,WAAO,EAAEA,GAAE,IAAI,EAAE,MAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,UAAMA,KAAE,KAAK,WAAW,OAAG,IAAE;AAAE,WAAO,MAAM,QAAQA,EAAC,IAAEA,KAAE,CAACA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAEE,IAAEC,IAAE;AAAC,WAAO,KAAK,QAAMH,IAAE,KAAK,QAAME,IAAE,KAAK,QAAMF,IAAE,KAAK,QAAME,IAAE,QAAMC,OAAI,KAAK,QAAMA,IAAE,KAAK,QAAMA,KAAG;AAAA,EAAI;AAAA,EAAC,uBAAsB;AAAC,WAAO,KAAK,WAAW,IAAE;AAAA,EAAC;AAAA,EAAC,MAAMH,IAAE;AAAC,WAAO,SAAOA,OAAI,KAAK,OAAK,KAAK,IAAI,KAAK,MAAKA,GAAE,IAAI,GAAE,KAAK,OAAK,KAAK,IAAI,KAAK,MAAKA,GAAE,IAAI,GAAE,KAAK,OAAK,KAAK,IAAI,KAAK,MAAKA,GAAE,IAAI,GAAE,KAAK,OAAK,KAAK,IAAI,KAAK,MAAKA,GAAE,IAAI,IAAG,KAAK,QAAMA,GAAE,UAAQ,KAAK,OAAKC,GAAE,KAAK,KAAI,KAAK,MAAKD,GAAE,IAAI,GAAE,KAAK,OAAKC,GAAE,KAAK,KAAI,KAAK,MAAKD,GAAE,IAAI,KAAI,KAAK,QAAMA,GAAE,UAAQ,KAAK,OAAKC,GAAE,KAAK,KAAI,KAAK,MAAKD,GAAE,IAAI,GAAE,KAAK,OAAKC,GAAE,KAAK,KAAI,KAAK,MAAKD,GAAE,IAAI,KAAI;AAAA,EAAI;AAAA,EAAC,aAAaA,IAAE;AAAC,WAAO,SAAOA,KAAE,OAAK,EAAEA,EAAC,KAAG,CAAC,KAAK,WAAWA,EAAC,IAAE,QAAM,KAAK,OAAK,KAAK,IAAI,KAAK,MAAKA,GAAE,IAAI,GAAE,KAAK,OAAK,KAAK,IAAI,KAAK,MAAKA,GAAE,IAAI,GAAE,KAAK,OAAK,KAAK,IAAI,KAAK,MAAKA,GAAE,IAAI,GAAE,KAAK,OAAK,KAAK,IAAI,KAAK,MAAKA,GAAE,IAAI,IAAG,KAAK,QAAMA,GAAE,UAAQ,KAAK,OAAKC,GAAE,KAAK,KAAI,KAAK,MAAKD,GAAE,IAAI,GAAE,KAAK,OAAKC,GAAE,KAAK,KAAI,KAAK,MAAKD,GAAE,IAAI,KAAI,KAAK,QAAMA,GAAE,UAAQ,KAAK,OAAKC,GAAE,KAAK,KAAI,KAAK,MAAKD,GAAE,IAAI,GAAE,KAAK,OAAKC,GAAE,KAAK,KAAI,KAAK,MAAKD,GAAE,IAAI,IAAG;AAAA,EAAK;AAAA,EAAC,OAAOA,IAAE;AAAC,WAAO,KAAK,MAAM,CAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,SAASA,KAAE,EAAE,KAAK,gBAAgB,GAAE;AAAC,QAAG,CAACA,MAAG,CAAC,KAAK,iBAAiB,QAAO;AAAK,UAAME,KAAE,KAAK,kBAAiBE,KAAE,KAAK,OAAOJ,EAAC;AAAE,QAAGI,IAAE;AAAC,YAAM,IAAEF,GAAE,gBAAc,EAAEE,EAAC,IAAEA;AAAE,WAAK,QAAMA,GAAE,GAAE,KAAK,QAAMA,GAAE,GAAEF,GAAE,kBAAgB,EAAE,IAAE,KAAK,YAAY,EAAE,GAAEF,EAAC,EAAE,IAAG,KAAK,mBAAiB,IAAI,EAAEM,IAAGJ,GAAE,UAAQF,GAAE,cAAY,SAAOA,GAAE,YAAW,EAAC,kBAAiB,EAAE,EAAC,CAAC,CAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,OAAOA,IAAE;AAAC,QAAIE,KAAE;AAAK,UAAK,CAACC,IAAEC,EAAC,IAAEJ,GAAE,OAAM,IAAE,KAAK,MAAKO,KAAE,KAAK;AAAK,WAAO,KAAGJ,MAAG,KAAGC,OAAIG,MAAGJ,MAAGI,MAAGH,QAAKF,KAAE,KAAK,SAAQA;AAAA,EAAC;AAAA,EAAC,WAAWF,IAAEE,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAiB,QAAG,CAACA,GAAE,QAAO;AAAK,UAAM,IAAED,MAAG,EAAEC,EAAC;AAAE,QAAG,QAAM,EAAE,QAAO;AAAK,UAAMG,KAAE,KAAK,UAAU,CAAC,EAAE,IAAK,CAAAP,OAAGA,GAAE,MAAO;AAAE,QAAGO,GAAE,SAAO,EAAE,QAAOA,GAAE,CAAC,KAAG;AAAK,QAAGA,GAAE,SAAO,EAAE,QAAOP,KAAE,KAAK,SAAS,CAAC,IAAE,KAAK,IAAI,EAAC,MAAK,EAAE,MAAM,CAAC,GAAE,MAAK,EAAE,MAAM,CAAC,EAAC,CAAC;AAAE,QAAGA,GAAE,QAAO,KAAK,SAAS,CAAC;AAAE,QAAGE,GAAE,QAAOK;AAAE,QAAID,KAAE,MAAGE,KAAE;AAAG,WAAOD,GAAE,QAAS,CAAAP,OAAG;AAAC,MAAAA,GAAE,SAAOM,KAAE,QAAIN,GAAE,SAAOQ,KAAE;AAAA,IAAG,CAAE,GAAE,EAAC,OAAMD,GAAE,IAAK,CAAAP,OAAG;AAAC,YAAME,KAAE,CAAC,CAACF,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,CAAC;AAAE,UAAGM,IAAE;AAAC,cAAMH,MAAGH,GAAE,OAAKA,GAAE,QAAM;AAAE,iBAAQA,KAAE,GAAEA,KAAEE,GAAE,QAAOF,KAAI,CAAAE,GAAEF,EAAC,EAAE,KAAKG,EAAC;AAAA,MAAC;AAAC,UAAGK,IAAE;AAAC,cAAML,MAAGH,GAAE,OAAKA,GAAE,QAAM;AAAE,iBAAQA,KAAE,GAAEA,KAAEE,GAAE,QAAOF,KAAI,CAAAE,GAAEF,EAAC,EAAE,KAAKG,EAAC;AAAA,MAAC;AAAC,aAAOD;AAAA,IAAC,CAAE,GAAE,MAAKI,IAAE,MAAKE,IAAE,kBAAiBJ,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUJ,IAAE;AAAC,QAAIE,KAAE,KAAK,MAAM;AAAO,QAAG,CAACA,IAAE;AAAC,MAAAA,KAAE,CAAC;AAAE,YAAK,EAAC,MAAKC,IAAE,MAAKC,IAAE,kBAAiB,EAAC,IAAE,MAAKG,KAAE,KAAK,OAAMD,KAAE,KAAK,MAAKE,KAAE,KAAK;AAAK,UAAIC;AAAE,MAAAT,KAAEA,MAAG,EAAE,CAAC;AAAE,YAAK,CAACU,IAAEC,EAAC,IAAEX,GAAE;AAAM,MAAAS,KAAE,KAAK,YAAY,KAAK,MAAKT,EAAC;AAAE,YAAMY,KAAEH,GAAE,GAAEI,KAAEJ,GAAE;AAAQ,MAAAA,KAAE,KAAK,YAAY,KAAK,MAAKT,EAAC;AAAE,YAAMc,KAAEL,GAAE,GAAEM,KAAEN,GAAE,SAAQO,KAAEJ,OAAIE,MAAGP,KAAE;AAAE,UAAGA,KAAE,IAAEI,IAAE;AAAC,cAAMX,KAAE,IAAID,GAAEO,KAAEE,KAAEI,KAAEE,IAAEX,IAAEQ,IAAEP,IAAE,CAAC,GAAEG,KAAE,IAAIR,GAAEW,IAAEP,IAAEG,KAAEE,KAAEM,KAAEF,IAAER,IAAE,CAAC,GAAEK,KAAE,IAAIV,GAAE,GAAEI,IAAEQ,IAAEP,IAAE,CAAC,GAAEa,KAAE,IAAIlB,GAAEW,IAAEP,IAAE,GAAEC,IAAE,CAAC,GAAEY,KAAE,CAAC,GAAEf,KAAE,CAAC;AAAE,QAAAD,GAAE,SAASS,EAAC,KAAGO,GAAE,KAAKH,EAAC,GAAEb,GAAE,SAASiB,EAAC,KAAGhB,GAAE,KAAKY,EAAC,GAAEN,GAAE,SAASE,EAAC,KAAGO,GAAE,KAAKD,EAAC,GAAER,GAAE,SAASU,EAAC,KAAGhB,GAAE,KAAKc,EAAC;AAAE,iBAAQb,KAAEW,KAAE,GAAEX,KAAEa,IAAEb,KAAI,CAAAc,GAAE,KAAKd,EAAC,GAAED,GAAE,KAAKC,EAAC;AAAE,QAAAA,GAAE,KAAK,EAAC,QAAOF,IAAE,UAAS,CAACa,EAAC,EAAC,GAAE,EAAC,QAAON,IAAE,UAAS,CAACQ,EAAC,EAAC,GAAE,EAAC,QAAON,IAAE,UAASO,GAAC,GAAE,EAAC,QAAOC,IAAE,UAAShB,GAAC,CAAC;AAAA,MAAC,MAAM,CAAAW,KAAEE,MAAGE,KAAEd,GAAE,KAAK,EAAC,QAAO,IAAIH,GAAEa,IAAET,IAAEQ,IAAEP,IAAE,CAAC,GAAE,UAAS,CAACS,EAAC,EAAC,GAAE,EAAC,QAAO,IAAId,GAAEW,IAAEP,IAAEW,IAAEV,IAAE,CAAC,GAAE,UAAS,CAACW,EAAC,EAAC,CAAC,IAAEb,GAAE,KAAK,EAAC,QAAO,IAAIH,GAAEa,IAAET,IAAEW,IAAEV,IAAE,CAAC,GAAE,UAAS,CAACS,EAAC,EAAC,CAAC;AAAE,WAAK,MAAM,SAAOX;AAAA,IAAC;AAAC,UAAMC,KAAE,KAAK,MAAKC,KAAE,KAAK;AAAK,QAAGD,MAAGC,IAAE;AAAC,YAAMJ,KAAE,CAAC;AAAE,MAAAG,OAAIH,GAAE,OAAK,KAAK,MAAKA,GAAE,OAAK,KAAK,OAAMI,OAAIJ,GAAE,OAAK,KAAK,MAAKA,GAAE,OAAK,KAAK;AAAM,eAAQG,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAD,GAAEC,EAAC,EAAE,OAAO,IAAIH,EAAC;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAEE,IAAE;AAAC,UAAK,CAACC,IAAEC,EAAC,IAAEF,GAAE,OAAM,IAAE,IAAEE;AAAE,QAAIG,IAAED,KAAE;AAAE,WAAON,KAAEI,MAAGG,KAAE,KAAK,KAAK,KAAK,IAAIP,KAAEI,EAAC,IAAE,CAAC,GAAEJ,MAAGO,KAAE,GAAED,KAAEC,MAAGP,KAAEG,OAAII,KAAE,KAAK,KAAK,KAAK,IAAIP,KAAEG,EAAC,IAAE,CAAC,GAAEH,MAAGO,KAAE,GAAED,KAAE,CAACC,KAAG,EAAC,GAAEP,IAAE,SAAQM,GAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,OAAM,EAAC,SAAQ,OAAG,gBAAe,KAAI,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,OAAM,EAAC,SAAQ,OAAG,gBAAe,KAAI,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,OAAM,EAAC,SAAQ,MAAG,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,OAAM,MAAE,EAAC,GAAE,OAAM,EAAC,iBAAgB;AAAC,SAAM,EAAC,SAAQ,KAAK,KAAI;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,OAAM,MAAE,EAAC,GAAE,OAAM,EAAC,iBAAgB;AAAC,SAAM,EAAC,SAAQ,KAAK,KAAI;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,OAAM,MAAE,EAAC,GAAE,OAAM,EAAC,iBAAgB;AAAC,SAAM,EAAC,SAAQ,KAAK,KAAI;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,OAAM,MAAE,EAAC,GAAE,OAAM,EAAC,iBAAgB;AAAC,SAAM,EAAC,SAAQ,KAAK,KAAI;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAEP,KAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,GAAE,CAAC,GAAE,EAAE,UAAU,OAAO,kBAAgB;AAAG,IAAMmB,KAAE;", "names": ["t", "r", "f", "i", "o", "u", "e", "c", "a", "n", "m", "s", "i", "t", "e", "r", "o", "f", "s", "u", "c", "m", "a", "x", "y", "g", "p", "n", "l", "h", "z", "G", "Z", "f", "t", "z", "i", "e", "s", "R", "r", "m", "a", "h", "o", "x", "l", "c", "y", "u", "d", "p", "w"]}