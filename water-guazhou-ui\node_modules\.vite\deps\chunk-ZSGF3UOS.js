import {
  a,
  c as c2,
  i
} from "./chunk-MNPAHSMX.js";
import {
  s
} from "./chunk-TDGDXUFC.js";
import {
  r as r5,
  t as t2
} from "./chunk-KVEZ26WH.js";
import {
  E2 as E
} from "./chunk-TOYJMVHA.js";
import {
  o as o7
} from "./chunk-Q6BEUTMN.js";
import {
  d as d2
} from "./chunk-BOT4BSSB.js";
import {
  e as e3,
  o as o5
} from "./chunk-XVTFHFM3.js";
import {
  a as a2
} from "./chunk-QB6AUIQ2.js";
import {
  r as r4
} from "./chunk-REGYRSW7.js";
import {
  e as e4
} from "./chunk-UB5FTTH5.js";
import {
  c,
  i as i2
} from "./chunk-6GW7M2AQ.js";
import {
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u as u2
} from "./chunk-IRHOIB3A.js";
import {
  d,
  o as o6,
  v
} from "./chunk-N3S5O3YO.js";
import {
  e as e2
} from "./chunk-JETZLJ6M.js";
import {
  o as o3,
  r as r3
} from "./chunk-VK7XO5DN.js";
import {
  o as o8
} from "./chunk-TUB4N6LD.js";
import {
  f
} from "./chunk-YV4RKNU4.js";
import {
  o as o4
} from "./chunk-RFTQI4ZD.js";
import {
  r as r2
} from "./chunk-3CEVKZPD.js";
import {
  u
} from "./chunk-6ZZUUGXX.js";
import {
  O
} from "./chunk-CPQSD22U.js";
import {
  o as o2
} from "./chunk-QYOAH6AO.js";
import {
  o
} from "./chunk-MQAXMQFG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/Offset.glsl.js
function e5(e8) {
  e8.vertex.code.add(o4`vec4 offsetBackfacingClipPosition(vec4 posClip, vec3 posWorld, vec3 normalWorld, vec3 camPosWorld) {
vec3 camToVert = posWorld - camPosWorld;
bool isBackface = dot(camToVert, normalWorld) > 0.0;
if (isBackface) {
posClip.z += 0.0000003 * posClip.w;
}
return posClip;
}`);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/InstancedDoublePrecision.glsl.js
var v2 = class extends t2 {
  constructor() {
    super(...arguments), this.instancedDoublePrecision = false;
  }
};
function p(e8, i6) {
  i6.instanced && i6.instancedDoublePrecision && (e8.attributes.add(O.MODELORIGINHI, "vec3"), e8.attributes.add(O.MODELORIGINLO, "vec3"), e8.attributes.add(O.MODEL, "mat3"), e8.attributes.add(O.MODELNORMAL, "mat3"));
  const c3 = e8.vertex;
  i6.instancedDoublePrecision && (c3.include(c, i6), c3.uniforms.add(new o6("viewOriginHi", (e9, i7) => o3(o(b, i7.camera.viewInverseTransposeMatrix[3], i7.camera.viewInverseTransposeMatrix[7], i7.camera.viewInverseTransposeMatrix[11]), b))), c3.uniforms.add(new o6("viewOriginLo", (e9, i7) => r3(o(b, i7.camera.viewInverseTransposeMatrix[3], i7.camera.viewInverseTransposeMatrix[7], i7.camera.viewInverseTransposeMatrix[11]), b)))), c3.code.add(o4`
    vec3 calculateVPos() {
      ${i6.instancedDoublePrecision ? "return model * localPosition().xyz;" : "return localPosition().xyz;"}
    }
    `), c3.code.add(o4`
    vec3 subtractOrigin(vec3 _pos) {
      ${i6.instancedDoublePrecision ? o4`
          vec3 originDelta = dpAdd(viewOriginHi, viewOriginLo, -modelOriginHi, -modelOriginLo);
          return _pos - originDelta;` : "return vpos;"}
    }
    `), c3.code.add(o4`
    vec3 dpNormal(vec4 _normal) {
      ${i6.instancedDoublePrecision ? "return normalize(modelNormal * _normal.xyz);" : "return normalize(_normal.xyz);"}
    }
    `), i6.output === h.Normal && (d(c3), c3.code.add(o4`
    vec3 dpNormalView(vec4 _normal) {
      ${i6.instancedDoublePrecision ? "return normalize((viewNormal * vec4(modelNormal * _normal.xyz, 1.0)).xyz);" : "return normalize((viewNormal * _normal).xyz);"}
    }
    `)), i6.hasVertexTangents && c3.code.add(o4`
    vec4 dpTransformVertexTangent(vec4 _tangent) {
      ${i6.instancedDoublePrecision ? "return vec4(modelNormal * _tangent.xyz, _tangent.w);" : "return _tangent;"}

    }
    `);
}
e([r5()], v2.prototype, "instancedDoublePrecision", void 0);
var b = n();

// node_modules/@arcgis/core/views/3d/webgl-engine/collections/Component/Material/shader/DecodeSymbolColor.glsl.js
function e6(e8) {
  e8.vertex.code.add(o4`
    vec4 decodeSymbolColor(vec4 symbolColor, out int colorMixMode) {
      float symbolAlpha = 0.0;

      const float maxTint = 85.0;
      const float maxReplace = 170.0;
      const float scaleAlpha = 3.0;

      if (symbolColor.a > maxReplace) {
        colorMixMode = ${o4.int(r2.Multiply)};
        symbolAlpha = scaleAlpha * (symbolColor.a - maxReplace);
      } else if (symbolColor.a > maxTint) {
        colorMixMode = ${o4.int(r2.Replace)};
        symbolAlpha = scaleAlpha * (symbolColor.a - maxTint);
      } else if (symbolColor.a > 0.0) {
        colorMixMode = ${o4.int(r2.Tint)};
        symbolAlpha = scaleAlpha * symbolColor.a;
      } else {
        colorMixMode = ${o4.int(r2.Multiply)};
        symbolAlpha = 0.0;
      }

      return vec4(symbolColor.r, symbolColor.g, symbolColor.b, symbolAlpha);
    }
  `);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/SymbolColor.glsl.js
function i3(i6, t3) {
  t3.hasSymbolColors ? (i6.include(e6), i6.attributes.add(O.SYMBOLCOLOR, "vec4"), i6.varyings.add("colorMixMode", "mediump float"), i6.vertex.code.add(o4`int symbolColorMixMode;
vec4 getSymbolColor() {
return decodeSymbolColor(symbolColor, symbolColorMixMode) * 0.003921568627451;
}
void forwardColorMixMode() {
colorMixMode = float(symbolColorMixMode) + 0.5;
}`)) : (i6.fragment.uniforms.add(new e3("colorMixMode", (o9) => E[o9.colorMixMode])), i6.vertex.code.add(o4`vec4 getSymbolColor() { return vec4(1.0); }
void forwardColorMixMode() {}`));
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/DiscardOrAdjustAlphaBlend.glsl.js
function d3(d4) {
  d4.fragment.code.add(o4`
    #define discardOrAdjustAlpha(color) { if (color.a < ${o4.float(t)}) { discard; } }
  `);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/AlphaDiscard.glsl.js
function s2(a4, e8) {
  l(a4, e8, new o8("textureAlphaCutoff", (a5) => a5.textureAlphaCutoff));
}
function l(e8, r6, s3) {
  const t3 = e8.fragment;
  switch (r6.alphaDiscardMode !== u.Mask && r6.alphaDiscardMode !== u.MaskBlend || t3.uniforms.add(s3), r6.alphaDiscardMode) {
    case u.Blend:
      return e8.include(d3);
    case u.Opaque:
      t3.code.add(o4`void discardOrAdjustAlpha(inout vec4 color) {
color.a = 1.0;
}`);
      break;
    case u.Mask:
      t3.code.add(o4`#define discardOrAdjustAlpha(color) { if (color.a < textureAlphaCutoff) { discard; } else { color.a = 1.0; } }`);
      break;
    case u.MaskBlend:
      e8.fragment.code.add(o4`#define discardOrAdjustAlpha(color) { if (color.a < textureAlphaCutoff) { discard; } }`);
  }
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/default/DefaultMaterialAuxiliaryPasses.glsl.js
function b2(b3, O2) {
  const { vertex: w, fragment: C } = b3, T = O2.hasModelTransformation;
  T && w.uniforms.add(new e2("model", (e8) => r(e8.modelTransformation) ? e8.modelTransformation : o2));
  const V = O2.hasColorTexture && O2.alphaDiscardMode !== u.Opaque;
  switch (O2.output) {
    case h.Depth:
    case h.Shadow:
    case h.ShadowHighlight:
    case h.ShadowExcludeHighlight:
    case h.ObjectAndLayerIdColor:
      v(w, O2), b3.include(r4, O2), b3.include(o5, O2), b3.include(s, O2), b3.include(o7, O2), b3.include(u2, O2), b3.include(d2, O2), i2(b3), b3.varyings.add("depth", "float"), V && C.uniforms.add(new f("tex", (o9) => o9.texture)), w.code.add(o4`
          void main(void) {
            vpos = calculateVPos();
            vpos = subtractOrigin(vpos);
            vpos = addVerticalOffset(vpos, localOrigin);
            gl_Position = transformPositionWithDepth(proj, view, ${T ? "model," : ""} vpos, nearFar, depth);
            forwardTextureCoordinates();
            forwardObjectAndLayerIdColor();
          }
        `), b3.include(s2, O2), C.code.add(o4`
          void main(void) {
            discardBySlice(vpos);
            ${V ? o4`
                    vec4 texColor = texture2D(tex, ${O2.hasColorTextureTransform ? o4`colorUV` : o4`vuv0`});
                    discardOrAdjustAlpha(texColor);` : ""}
            ${O2.output === h.ObjectAndLayerIdColor ? o4`outputObjectAndLayerIdColor();` : o4`outputDepth(depth);`}
          }
        `);
      break;
    case h.Normal:
      v(w, O2), b3.include(r4, O2), b3.include(i, O2), b3.include(c2, O2), b3.include(o5, O2), b3.include(s, O2), V && C.uniforms.add(new f("tex", (o9) => o9.texture)), b3.varyings.add("vPositionView", "vec3"), w.code.add(o4`
          void main(void) {
            vpos = calculateVPos();
            vpos = subtractOrigin(vpos);
            ${O2.normalType === a.Attribute ? o4`
            vNormalWorld = dpNormalView(vvLocalNormal(normalModel()));` : ""}
            vpos = addVerticalOffset(vpos, localOrigin);
            gl_Position = transformPosition(proj, view, ${T ? "model," : ""} vpos);
            forwardTextureCoordinates();
          }
        `), b3.include(u2, O2), b3.include(s2, O2), C.code.add(o4`
          void main() {
            discardBySlice(vpos);
            ${V ? o4`
                    vec4 texColor = texture2D(tex, ${O2.hasColorTextureTransform ? o4`colorUV` : o4`vuv0`});
                    discardOrAdjustAlpha(texColor);` : ""}

            ${O2.normalType === a.ScreenDerivative ? o4`
                vec3 normal = screenDerivativeNormal(vPositionView);` : o4`
                vec3 normal = normalize(vNormalWorld);
                if (gl_FrontFacing == false) normal = -normal;`}
            gl_FragColor = vec4(vec3(0.5) + 0.5 * normal, 1.0);
          }
        `);
      break;
    case h.Highlight:
      v(w, O2), b3.include(r4, O2), b3.include(o5, O2), b3.include(s, O2), V && C.uniforms.add(new f("tex", (o9) => o9.texture)), w.code.add(o4`
          void main(void) {
            vpos = calculateVPos();
            vpos = subtractOrigin(vpos);
            vpos = addVerticalOffset(vpos, localOrigin);
            gl_Position = transformPosition(proj, view, ${T ? "model," : ""} vpos);
            forwardTextureCoordinates();
          }
        `), b3.include(u2, O2), b3.include(s2, O2), b3.include(a2, O2), C.code.add(o4`
          void main() {
            discardBySlice(vpos);
            ${V ? o4`
                    vec4 texColor = texture2D(tex, ${O2.hasColorTextureTransform ? o4`colorUV` : o4`vuv0`});
                    discardOrAdjustAlpha(texColor);` : ""}
            outputHighlight();
          }
        `);
  }
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/MixExternalColor.glsl.js
function i5(i6) {
  i6.include(e4), i6.code.add(o4`
    vec3 mixExternalColor(vec3 internalColor, vec3 textureColor, vec3 externalColor, int mode) {
      // workaround for artifacts in OSX using Intel Iris Pro
      // see: https://devtopia.esri.com/WebGIS/arcgis-js-api/issues/10475
      vec3 internalMixed = internalColor * textureColor;
      vec3 allMixed = internalMixed * externalColor;

      if (mode == ${o4.int(r2.Multiply)}) {
        return allMixed;
      }
      if (mode == ${o4.int(r2.Ignore)}) {
        return internalMixed;
      }
      if (mode == ${o4.int(r2.Replace)}) {
        return externalColor;
      }

      // tint (or something invalid)
      float vIn = rgb2v(internalMixed);
      vec3 hsvTint = rgb2hsv(externalColor);
      vec3 hsvOut = vec3(hsvTint.x, hsvTint.y, vIn * hsvTint.z);
      return hsv2rgb(hsvOut);
    }

    float mixExternalOpacity(float internalOpacity, float textureOpacity, float externalOpacity, int mode) {
      // workaround for artifacts in OSX using Intel Iris Pro
      // see: https://devtopia.esri.com/WebGIS/arcgis-js-api/issues/10475
      float internalMixed = internalOpacity * textureOpacity;
      float allMixed = internalMixed * externalOpacity;

      if (mode == ${o4.int(r2.Ignore)}) {
        return internalMixed;
      }
      if (mode == ${o4.int(r2.Replace)}) {
        return externalOpacity;
      }

      // multiply or tint (or something invalid)
      return allMixed;
    }
  `);
}

export {
  e5 as e,
  p,
  i3 as i,
  s2 as s,
  b2 as b,
  i5 as i2
};
//# sourceMappingURL=chunk-ZSGF3UOS.js.map
