import {
  T
} from "./chunk-VX64NK2J.js";

// node_modules/@arcgis/core/arcade/Attachment.js
var e = class extends T {
  constructor(t, e2, s, i, a, l) {
    super(), this.attachmentUrl = a, this.declaredClass = "esri.arcade.Attachment", this.immutable = false, this.setField("id", t), this.setField("name", e2), this.setField("contenttype", s), this.setField("size", i), this.setField("exifinfo", l), this.immutable = true;
  }
};

export {
  e
};
//# sourceMappingURL=chunk-YU2YCWN6.js.map
