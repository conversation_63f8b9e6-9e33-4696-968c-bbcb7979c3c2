import {
  r as r3
} from "./chunk-57ER3SHX.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  f,
  m
} from "./chunk-XVA5SA7P.js";
import {
  l as l2
} from "./chunk-T23PB46T.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  v
} from "./chunk-HP475EI3.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/MeshTexture.js
var p;
var d = /* @__PURE__ */ new WeakMap();
var h = 0;
var u = p = class extends l {
  constructor(t) {
    super(t), this.wrap = "repeat";
  }
  get url() {
    return this._get("url") || null;
  }
  set url(t) {
    this._set("url", t), t && this._set("data", null);
  }
  get data() {
    return this._get("data") || null;
  }
  set data(t) {
    this._set("data", t), t && this._set("url", null);
  }
  writeData(t, e2, a3, r4) {
    if (t instanceof HTMLImageElement) {
      const s = { type: "image-element", src: m(t.src, r4), crossOrigin: t.crossOrigin };
      e2[a3] = s;
    } else if (t instanceof HTMLCanvasElement) {
      const r5 = t.getContext("2d").getImageData(0, 0, t.width, t.height), s = { type: "canvas-element", imageData: this._encodeImageData(r5) };
      e2[a3] = s;
    } else if (t instanceof HTMLVideoElement) {
      const s = { type: "video-element", src: m(t.src, r4), autoplay: t.autoplay, loop: t.loop, muted: t.muted, crossOrigin: t.crossOrigin, preload: t.preload };
      e2[a3] = s;
    } else if (t instanceof ImageData) {
      const r5 = { type: "image-data", imageData: this._encodeImageData(t) };
      e2[a3] = r5;
    }
  }
  readData(t) {
    switch (t.type) {
      case "image-element": {
        const e2 = new Image();
        return e2.src = t.src, e2.crossOrigin = t.crossOrigin, e2;
      }
      case "canvas-element": {
        const e2 = this._decodeImageData(t.imageData), a3 = document.createElement("canvas");
        return a3.width = e2.width, a3.height = e2.height, a3.getContext("2d").putImageData(e2, 0, 0), a3;
      }
      case "image-data":
        return this._decodeImageData(t.imageData);
      case "video-element": {
        const e2 = document.createElement("video");
        return e2.src = t.src, e2.crossOrigin = t.crossOrigin, e2.autoplay = t.autoplay, e2.loop = t.loop, e2.muted = t.muted, e2.preload = t.preload, e2;
      }
      default:
        return;
    }
  }
  get transparent() {
    const t = this.data, e2 = this.url;
    if (t instanceof HTMLCanvasElement) return this._imageDataContainsTransparent(t.getContext("2d").getImageData(0, 0, t.width, t.height));
    if (t instanceof ImageData) return this._imageDataContainsTransparent(t);
    if (e2) {
      const t2 = e2.substr(e2.length - 4, 4).toLowerCase(), a3 = e2.substr(0, 15).toLocaleLowerCase();
      if (".png" === t2 || "data:image/png;" === a3) return true;
    }
    return false;
  }
  set transparent(t) {
    this._overrideIfSome("transparent", t);
  }
  get contentHash() {
    const t = "string" == typeof this.wrap ? this.wrap : "object" == typeof this.wrap ? `${this.wrap.horizontal}/${this.wrap.vertical}` : "", e2 = (e3 = "") => `d:${e3},t:${this.transparent},w:${t}`;
    return null != this.url ? e2(this.url) : null != this.data ? this.data instanceof HTMLImageElement || this.data instanceof HTMLVideoElement ? e2(this.data.src) : (d.has(this.data) || d.set(this.data, ++h), e2(d.get(this.data))) : e2();
  }
  clone() {
    const t = { url: this.url, data: this.data, wrap: this._cloneWrap() };
    return new p(t);
  }
  cloneWithDeduplication(t) {
    const e2 = t.get(this);
    if (e2) return e2;
    const a3 = this.clone();
    return t.set(this, a3), a3;
  }
  _cloneWrap() {
    return "string" == typeof this.wrap ? this.wrap : { horizontal: this.wrap.horizontal, vertical: this.wrap.vertical };
  }
  _encodeImageData(t) {
    let e2 = "";
    for (let a3 = 0; a3 < t.data.length; a3++) e2 += String.fromCharCode(t.data[a3]);
    return { data: btoa(e2), width: t.width, height: t.height };
  }
  _decodeImageData(t) {
    const a3 = atob(t.data), r4 = new Uint8ClampedArray(a3.length);
    for (let e2 = 0; e2 < a3.length; e2++) r4[e2] = a3.charCodeAt(e2);
    return r3(r4, t.width, t.height);
  }
  _imageDataContainsTransparent(t) {
    for (let e2 = 3; e2 < t.data.length; e2 += 4) if (255 !== t.data[e2]) return true;
    return false;
  }
  static from(t) {
    return "string" == typeof t ? new p({ url: t }) : t instanceof HTMLImageElement || t instanceof HTMLCanvasElement || t instanceof ImageData || t instanceof HTMLVideoElement ? new p({ data: t }) : v(p, t);
  }
};
e([y({ type: String, json: { write: f } })], u.prototype, "url", null), e([y({ json: { write: { overridePolicy() {
  return { enabled: !this.url };
} } } }), y()], u.prototype, "data", null), e([r2("data")], u.prototype, "writeData", null), e([o("data")], u.prototype, "readData", null), e([y({ type: Boolean, json: { write: { overridePolicy() {
  return { enabled: this._isOverridden("transparent") };
} } } })], u.prototype, "transparent", null), e([y({ json: { write: true } })], u.prototype, "wrap", void 0), e([y({ readOnly: true })], u.prototype, "contentHash", null), u = p = e([a("esri.geometry.support.MeshTexture")], u);
var m2 = u;

// node_modules/@arcgis/core/geometry/support/MeshMaterial.js
var n;
var u2 = n = class extends l {
  constructor(o2) {
    super(o2), this.color = null, this.colorTexture = null, this.normalTexture = null, this.alphaMode = "auto", this.alphaCutoff = 0.5, this.doubleSided = true;
  }
  clone() {
    return this.cloneWithDeduplication(null, /* @__PURE__ */ new Map());
  }
  cloneWithDeduplication(o2, r4) {
    const e2 = r(o2) ? o2.get(this) : null;
    if (e2) return e2;
    const l3 = new n(this.clonePropertiesWithDeduplication(r4));
    return r(o2) && o2.set(this, l3), l3;
  }
  clonePropertiesWithDeduplication(o2) {
    return { color: r(this.color) ? this.color.clone() : null, colorTexture: r(this.colorTexture) ? this.colorTexture.cloneWithDeduplication(o2) : null, normalTexture: r(this.normalTexture) ? this.normalTexture.cloneWithDeduplication(o2) : null, alphaMode: this.alphaMode, alphaCutoff: this.alphaCutoff, doubleSided: this.doubleSided, colorTextureTransform: r(this.colorTextureTransform) ? this.colorTextureTransform : null, normalTextureTransform: r(this.normalTextureTransform) ? this.normalTextureTransform : null };
  }
};
e([y({ type: l2, json: { write: true } })], u2.prototype, "color", void 0), e([y({ type: m2, json: { write: true } })], u2.prototype, "colorTexture", void 0), e([y({ type: m2, json: { write: true } })], u2.prototype, "normalTexture", void 0), e([y({ nonNullable: true, json: { write: true } })], u2.prototype, "alphaMode", void 0), e([y({ nonNullable: true, json: { write: true } })], u2.prototype, "alphaCutoff", void 0), e([y({ nonNullable: true, json: { write: true } })], u2.prototype, "doubleSided", void 0), e([y()], u2.prototype, "colorTextureTransform", void 0), e([y()], u2.prototype, "normalTextureTransform", void 0), u2 = n = e([a("esri.geometry.support.MeshMaterial")], u2);
var a2 = u2;

// node_modules/@arcgis/core/geometry/support/MeshMaterialMetallicRoughness.js
var n2;
var u3 = n2 = class extends a2 {
  constructor(e2) {
    super(e2), this.emissiveColor = null, this.emissiveTexture = null, this.occlusionTexture = null, this.metallic = 1, this.roughness = 1, this.metallicRoughnessTexture = null;
  }
  clone() {
    return this.cloneWithDeduplication(null, /* @__PURE__ */ new Map());
  }
  cloneWithDeduplication(e2, s) {
    const o2 = r(e2) ? e2.get(this) : null;
    if (o2) return o2;
    const r4 = new n2(this.clonePropertiesWithDeduplication(s));
    return r(e2) && e2.set(this, r4), r4;
  }
  clonePropertiesWithDeduplication(e2) {
    return { ...super.clonePropertiesWithDeduplication(e2), emissiveColor: r(this.emissiveColor) ? this.emissiveColor.clone() : null, emissiveTexture: r(this.emissiveTexture) ? this.emissiveTexture.cloneWithDeduplication(e2) : null, occlusionTexture: r(this.occlusionTexture) ? this.occlusionTexture.cloneWithDeduplication(e2) : null, metallic: this.metallic, roughness: this.roughness, metallicRoughnessTexture: r(this.metallicRoughnessTexture) ? this.metallicRoughnessTexture.cloneWithDeduplication(e2) : null, occlusionTextureTransform: r(this.occlusionTextureTransform) ? this.occlusionTextureTransform : null, emissiveTextureTransform: r(this.emissiveTextureTransform) ? this.emissiveTextureTransform : null, metallicRoughnessTextureTransform: r(this.metallicRoughnessTextureTransform) ? this.metallicRoughnessTextureTransform : null };
  }
};
e([y({ type: l2, json: { write: true } })], u3.prototype, "emissiveColor", void 0), e([y({ type: m2, json: { write: true } })], u3.prototype, "emissiveTexture", void 0), e([y({ type: m2, json: { write: true } })], u3.prototype, "occlusionTexture", void 0), e([y({ type: Number, nonNullable: true, json: { write: true }, range: { min: 0, max: 1 } })], u3.prototype, "metallic", void 0), e([y({ type: Number, nonNullable: true, json: { write: true }, range: { min: 0, max: 1 } })], u3.prototype, "roughness", void 0), e([y({ type: m2, json: { write: true } })], u3.prototype, "metallicRoughnessTexture", void 0), e([y()], u3.prototype, "occlusionTextureTransform", void 0), e([y()], u3.prototype, "emissiveTextureTransform", void 0), e([y()], u3.prototype, "metallicRoughnessTextureTransform", void 0), u3 = n2 = e([a("esri.geometry.support.MeshMaterialMetallicRoughness")], u3);
var c = u3;

export {
  m2 as m,
  a2 as a,
  c
};
//# sourceMappingURL=chunk-GJMH67CL.js.map
