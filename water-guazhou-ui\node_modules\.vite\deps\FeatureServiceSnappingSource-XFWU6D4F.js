import {
  $,
  W,
  as
} from "./chunk-U7VYWSSV.js";
import {
  i,
  p as p3
} from "./chunk-I25FGXOD.js";
import "./chunk-3LXNA5FS.js";
import "./chunk-FQMXSCOG.js";
import {
  h as h4
} from "./chunk-UGCLDPAC.js";
import {
  o as o5
} from "./chunk-RVGLVPCD.js";
import {
  j,
  p as p2
} from "./chunk-IEIKQ72S.js";
import {
  t as t5
} from "./chunk-3IDKVHSA.js";
import "./chunk-FAMLZKHJ.js";
import "./chunk-6IU6DQRF.js";
import "./chunk-YELYN22P.js";
import "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import "./chunk-A7PY25IH.js";
import {
  p
} from "./chunk-7VXHHPI3.js";
import "./chunk-NMGVJ2ZX.js";
import "./chunk-INH5JU5P.js";
import "./chunk-UQWZJZ2S.js";
import "./chunk-5S4W3ME5.js";
import {
  y as y3
} from "./chunk-CDZ24ELJ.js";
import {
  M
} from "./chunk-VHLK35TF.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import {
  t as t4
} from "./chunk-ONE6GLG5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import "./chunk-IKGI4J4I.js";
import "./chunk-MNWHGD3K.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  a as a3,
  d
} from "./chunk-Q4VCSCSY.js";
import "./chunk-YEODPCXQ.js";
import {
  n as n2
} from "./chunk-NOZFLZZL.js";
import "./chunk-3WUI7ZKG.js";
import {
  U,
  a as a2,
  h as h2,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-3WEGNHPY.js";
import {
  g as g3
} from "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import {
  h as h3,
  j2
} from "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import {
  f as f3,
  o as o4
} from "./chunk-I7WHRVHF.js";
import {
  S
} from "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import {
  o as o2
} from "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import {
  v as v2
} from "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import {
  m3 as m
} from "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import {
  l as l2
} from "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import {
  h
} from "./chunk-EIGTETCG.js";
import {
  o as o3
} from "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  t2 as t3,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  n,
  t as t2
} from "./chunk-2CM7MIII.js";
import {
  f as f2,
  g as g2,
  y as y2
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  f,
  g,
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/tileUtils.js
function c(n3, o6) {
  return $(o6.extent, e2), as(e2, o3(a4, n3.x, n3.y, 0));
}
var e2 = W();
var a4 = n2();

// node_modules/@arcgis/core/views/2d/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTiles2D.js
var u = class extends v {
  get tiles() {
    const e3 = this.tilesCoveringView, t6 = r(this.pointOfInterest) ? this.pointOfInterest : this.view.center;
    return e3.sort((e4, r2) => c(t6, e4) - c(t6, r2)), e3;
  }
  _scaleEnabled() {
    return o5(this.view.scale, this.layer.minScale || 0, this.layer.maxScale || 0);
  }
  get tilesCoveringView() {
    if (!this.view.ready || !this.view.featuresTilingScheme || !this.view.state || t(this.tileInfo)) return [];
    if (!this._scaleEnabled) return [];
    const { spans: e3, lodInfo: t6 } = this.view.featuresTilingScheme.getTileCoverage(this.view.state, 0), { level: r2 } = t6, o6 = [];
    for (const { row: i2, colFrom: s2, colTo: l3 } of e3) for (let e4 = s2; e4 <= l3; e4++) {
      const s3 = t6.normalizeCol(e4), l4 = new t5(null, r2, i2, s3);
      this.tileInfo.updateTileInfo(l4), o6.push(l4);
    }
    return o6;
  }
  get tileInfo() {
    var _a;
    return ((_a = this.view.featuresTilingScheme) == null ? void 0 : _a.tileInfo) ?? null;
  }
  get tileSize() {
    return r(this.tileInfo) ? this.tileInfo.size[0] : 256;
  }
  constructor(e3) {
    super(e3), this.pointOfInterest = null;
  }
  initialize() {
    this.addHandles(l(() => {
      var _a, _b;
      return (_b = (_a = this.view) == null ? void 0 : _a.state) == null ? void 0 : _b.viewpoint;
    }, () => this.notifyChange("tilesCoveringView"), U));
  }
};
e([y({ readOnly: true })], u.prototype, "tiles", null), e([y({ readOnly: true })], u.prototype, "_scaleEnabled", null), e([y({ readOnly: true })], u.prototype, "tilesCoveringView", null), e([y({ readOnly: true })], u.prototype, "tileInfo", null), e([y({ readOnly: true })], u.prototype, "tileSize", null), e([y({ constructOnly: true })], u.prototype, "view", void 0), e([y({ constructOnly: true })], u.prototype, "layer", void 0), e([y()], u.prototype, "pointOfInterest", void 0), u = e([a("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiles2D")], u);

// node_modules/@arcgis/core/views/3d/layers/support/FeatureTileDescriptor3D.js
var s;
!function(i2) {
  i2[i2.INVISIBLE = 0] = "INVISIBLE", i2[i2.VISIBLE_WHEN_EXTENDED = 1] = "VISIBLE_WHEN_EXTENDED", i2[i2.VISIBLE_ON_SURFACE = 2] = "VISIBLE_ON_SURFACE";
}(s || (s = {}));

// node_modules/@arcgis/core/views/3d/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTiles3D.js
var u3 = class extends d {
  get tiles() {
    const e3 = this.tilesCoveringView, t6 = this._effectivePointOfInterest;
    if (r(t6)) {
      const r2 = e3.map((e4) => c(t6, e4));
      for (let i2 = 1; i2 < r2.length; i2++) if (r2[i2 - 1] > r2[i2]) return e3.sort((e4, r3) => c(t6, e4) - c(t6, r3)), e3.slice();
    }
    return e3;
  }
  get tilesCoveringView() {
    var _a, _b;
    return this._filterTiles((_b = (_a = this.view.featureTiles) == null ? void 0 : _a.tiles) == null ? void 0 : _b.toArray()).map(f4);
  }
  get tileInfo() {
    var _a;
    return ((_a = this.view.featureTiles) == null ? void 0 : _a.tilingScheme.toTileInfo()) ?? null;
  }
  get tileSize() {
    var _a;
    return ((_a = this.view.featureTiles) == null ? void 0 : _a.tileSize) ?? 256;
  }
  get _effectivePointOfInterest() {
    var _a;
    const e3 = this.pointOfInterest;
    return r(e3) ? e3 : (_a = this.view.pointsOfInterest) == null ? void 0 : _a.focus.location;
  }
  constructor(e3) {
    super(e3), this.pointOfInterest = null;
  }
  initialize() {
    this.handles.add(l(() => this.view.featureTiles, (e3) => {
      this.handles.remove(v3), e3 && this.handles.add(e3.addClient(), v3);
    }, h2));
  }
  _filterTiles(e3) {
    if (t(e3)) return [];
    return e3.filter((e4) => Math.abs(e4.measures.screenRect[3] - e4.measures.screenRect[1]) > m2 && e4.measures.visibility === s.VISIBLE_ON_SURFACE);
  }
};
function f4({ lij: [e3, t6, r2], extent: i2 }) {
  return new t5(`${e3}/${t6}/${r2}`, e3, t6, r2, i2);
}
e([y({ readOnly: true })], u3.prototype, "tiles", null), e([y({ readOnly: true })], u3.prototype, "tilesCoveringView", null), e([y({ readOnly: true })], u3.prototype, "tileInfo", null), e([y({ readOnly: true })], u3.prototype, "tileSize", null), e([y({ constructOnly: true })], u3.prototype, "view", void 0), e([y()], u3.prototype, "pointOfInterest", void 0), e([y()], u3.prototype, "_effectivePointOfInterest", null), u3 = e([a("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiles3D")], u3);
var m2 = 50;
var v3 = "feature-tiles";

// node_modules/@arcgis/core/views/support/TileTreeDebugger.js
var m3 = [[0, 179, 255], [117, 62, 128], [0, 104, 255], [215, 189, 166], [32, 0, 193], [98, 162, 206], [102, 112, 129], [52, 125, 0], [142, 118, 246], [138, 83, 0], [92, 122, 255], [122, 55, 83], [0, 142, 255], [81, 40, 179], [0, 200, 244], [13, 24, 127], [0, 170, 147], [19, 58, 241], [22, 44, 35]];
var b = class extends v {
  constructor(e3) {
    super(e3), this.updating = false, this.enablePolygons = true, this.enableLabels = true, this._polygons = /* @__PURE__ */ new Map(), this._labels = /* @__PURE__ */ new Map(), this._enabled = true;
  }
  initialize() {
    this._symbols = m3.map((e3) => new S({ color: [e3[0], e3[1], e3[2], 0.6], outline: { color: "black", width: 1 } })), this.update();
  }
  destroy() {
    this._enabled = false, this.clear();
  }
  get enabled() {
    return this._enabled;
  }
  set enabled(e3) {
    this._enabled !== e3 && (this._enabled = e3, this.update());
  }
  update() {
    if (!this._enabled) return void this.clear();
    const e3 = (e4) => {
      if (r(e4.label)) return e4.label;
      let s2 = e4.lij.toString();
      return r(e4.loadPriority) && (s2 += ` (${e4.loadPriority})`), s2;
    }, t6 = this.getTiles(), a7 = new Array(), n3 = new Set((this._labels.size, this._labels.keys()));
    t6.forEach((c4, m4) => {
      const b2 = c4.lij.toString();
      n3.delete(b2);
      const d3 = c4.lij[0], g4 = c4.geometry;
      if (this.enablePolygons && !this._polygons.has(b2)) {
        const e4 = new g3({ geometry: g4, symbol: this._symbols[d3 % this._symbols.length] });
        this._polygons.set(b2, e4), a7.push(e4);
      }
      if (this.enableLabels) {
        const n4 = e3(c4), d4 = m4 / (t6.length - 1), u4 = h(0, 200, d4), _ = h(20, 6, d4) / 0.75, f5 = r(c4.loadPriority) && c4.loadPriority >= t6.length, w2 = new l2([u4, f5 ? 0 : u4, f5 ? 0 : u4]), j3 = "3d" === this.view.type ? () => new h3({ verticalOffset: { screenLength: 40 / 0.75 }, callout: { type: "line", color: "white", border: { color: "black" } }, symbolLayers: [new j2({ text: n4, halo: { color: "white", size: 1 / 0.75 }, material: { color: w2 }, size: _ })] }) : () => new m({ text: n4, haloColor: "white", haloSize: 1 / 0.75, color: w2, size: _ }), v4 = this._labels.get(b2);
        if (v4) {
          const e4 = j3();
          (t(v4.symbol) || JSON.stringify(e4) !== JSON.stringify(v4.symbol)) && (v4.symbol = e4);
        } else {
          const e4 = new g3({ geometry: g4.extent.center, symbol: j3() });
          this._labels.set(b2, e4), a7.push(e4);
        }
      }
    });
    const c3 = new Array();
    n3.forEach((e4) => {
      const s2 = this._polygons.get(e4);
      null != s2 && (c3.push(s2), this._polygons.delete(e4));
      const o6 = this._labels.get(e4);
      null != o6 && (c3.push(o6), this._labels.delete(e4));
    }), this.view.graphics.removeMany(c3), this.view.graphics.addMany(a7);
  }
  clear() {
    this.view.graphics.removeMany(Array.from(this._polygons.values())), this.view.graphics.removeMany(Array.from(this._labels.values())), this._polygons.clear(), this._labels.clear();
  }
};
e([y({ constructOnly: true })], b.prototype, "view", void 0), e([y({ readOnly: true })], b.prototype, "updating", void 0), e([y()], b.prototype, "enabled", null), b = e([a("esri.views.support.TileTreeDebugger")], b);

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/WorkerTileTreeDebugger.js
var d2 = class extends b {
  constructor(e3) {
    super(e3), this._handles = new t3();
  }
  initialize() {
    const e3 = setInterval(() => this._fetchDebugInfo(), 2e3);
    this._handles.add(n(() => clearInterval(e3)));
  }
  destroy() {
    this._handles.destroy();
  }
  getTiles() {
    if (!this._debugInfo) return [];
    const e3 = /* @__PURE__ */ new Map(), t6 = /* @__PURE__ */ new Map();
    this._debugInfo.storedTiles.forEach((t7) => {
      e3.set(t7.data.id, t7.featureCount);
    }), this._debugInfo.pendingTiles.forEach((r3) => {
      e3.set(r3.data.id, r3.featureCount), t6.set(r3.data.id, r3.state);
    });
    const r2 = (r3) => {
      const o7 = t6.get(r3), s2 = e3.get(r3) ?? "?";
      return o7 ? `${o7}:${s2}
${r3}` : `store:${s2}
${r3}`;
    }, o6 = /* @__PURE__ */ new Map();
    return this._debugInfo.storedTiles.forEach((e4) => {
      o6.set(e4.data.id, e4.data);
    }), this._debugInfo.pendingTiles.forEach((e4) => {
      o6.set(e4.data.id, e4.data);
    }), Array.from(o6.values()).map((e4) => ({ lij: [e4.level, e4.row, e4.col], geometry: v2.fromExtent(f3(e4.extent, this.view.spatialReference)), label: r2(e4.id) }));
  }
  _fetchDebugInfo() {
    this.handle.getDebugInfo(null).then((e3) => {
      this._debugInfo = e3, this.update();
    });
  }
};
e([y({ constructOnly: true })], d2.prototype, "handle", void 0), d2 = e([a("esri.views.interactive.snapping.featureSources.WorkerTileTreeDebugger")], d2);

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceSnappingSourceWorkerHandle.js
var p4 = class extends d {
  get updating() {
    return this.updatingHandles.updating || this._workerHandleUpdating;
  }
  constructor(e3) {
    super(e3), this.schedule = null, this.hasZ = false, this.elevationAlignPointsInFeatures = async (e4) => {
      const t6 = [];
      for (const { points: i2 } of e4) for (const { z: e5 } of i2) t6.push(e5);
      return { elevations: t6, drapedObjectIds: /* @__PURE__ */ new Set(), failedObjectIds: /* @__PURE__ */ new Set() };
    }, this.queryForSymbologySnapping = async () => ({ candidates: [], sourceCandidateIndices: [] }), this.availability = 0, this._workerHandleUpdating = true, this._editId = 0;
  }
  destroy() {
    this._workerHandle.destroy();
  }
  initialize() {
    this._workerHandle = new c2(this.schedule, { alignElevation: async (e3, { signal: t6 }) => ({ result: await this.elevationAlignPointsInFeatures(e3.points, t6) }), getSymbologyCandidates: async (e3, { signal: t6 }) => ({ result: await this.queryForSymbologySnapping(e3, t6) }) }), this.handles.add([this._workerHandle.on("notify-updating", ({ updating: e3 }) => this._workerHandleUpdating = e3), this._workerHandle.on("notify-availability", ({ availability: e3 }) => this._set("availability", e3))]);
  }
  async setup(e3, t6) {
    var _a;
    const o6 = this._serviceInfoFromLayer(e3.layer);
    if (t(o6)) return;
    const a7 = { configuration: this._convertConfiguration(e3.configuration), serviceInfo: o6, spatialReference: e3.spatialReference.toJSON(), hasZ: this.hasZ, elevationInfo: (_a = e3.layer.elevationInfo) == null ? void 0 : _a.toJSON() };
    await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("setup", a7, t6)), this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating", {}, t6));
  }
  async configure(e3, t6) {
    const i2 = this._convertConfiguration(e3);
    await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("configure", i2, t6)), this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating", {}, t6));
  }
  async refresh(e3) {
    await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("refresh", {}, e3)), this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating", {}, e3));
  }
  async fetchCandidates(e3, t6) {
    const i2 = e3.point, a7 = { distance: e3.distance, mode: e3.mode, point: M(i2[0], i2[1], i2[2], e3.coordinateHelper.spatialReference.toJSON()), types: e3.types, filter: r(e3.filter) ? e3.filter.toJSON() : null };
    return this._workerHandle.invoke(a7, t6);
  }
  async updateTiles(e3, t6) {
    const i2 = { tiles: e3.tiles, tileInfo: r(e3.tileInfo) ? e3.tileInfo.toJSON() : null, tileSize: e3.tileSize };
    await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("updateTiles", i2, t6)), this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating", {}, t6));
  }
  async applyEdits(e3, t6) {
    var _a, _b, _c;
    const i2 = this._editId++, n3 = { id: i2 };
    await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("beginApplyEdits", n3, t6));
    const r2 = await this.updatingHandles.addPromise(y2(e3.result, t6)), s2 = { id: i2, edits: { addedFeatures: ((_a = r2.addedFeatures) == null ? void 0 : _a.map(({ objectId: e4 }) => e4).filter(r)) ?? [], deletedFeatures: ((_b = r2.deletedFeatures) == null ? void 0 : _b.map(({ objectId: e4, globalId: t7 }) => ({ objectId: e4, globalId: t7 }))) ?? [], updatedFeatures: ((_c = r2.updatedFeatures) == null ? void 0 : _c.map(({ objectId: e4 }) => e4).filter(r)) ?? [] } };
    await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("endApplyEdits", s2, t6)), this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating", {}, t6));
  }
  getDebugInfo(e3) {
    return this._workerHandle.invokeMethod("getDebugInfo", {}, e3);
  }
  async notifyElevationSourceChange() {
    await this._workerHandle.invokeMethod("notifyElevationSourceChange", {});
  }
  async notifySymbologyChange() {
    await this._workerHandle.invokeMethod("notifySymbologyChange", {});
  }
  async setSymbologySnappingSupported(e3) {
    await this._workerHandle.invokeMethod("setSymbologySnappingSupported", e3);
  }
  _convertConfiguration(e3) {
    return { filter: r(e3.filter) ? e3.filter.toJSON() : null, customParameters: e3.customParameters, viewType: e3.viewType };
  }
  _serviceInfoFromLayer(e3) {
    var _a, _b;
    return "multipatch" === e3.geometryType || "mesh" === e3.geometryType ? null : { url: ((_a = e3.parsedUrl) == null ? void 0 : _a.path) ?? "", fields: e3.fields.map((e4) => e4.toJSON()), geometryType: o2.toJSON(e3.geometryType), capabilities: e3.capabilities, objectIdField: e3.objectIdField, globalIdField: e3.globalIdField, spatialReference: e3.spatialReference.toJSON(), timeInfo: (_b = e3.timeInfo) == null ? void 0 : _b.toJSON() };
  }
};
e([y({ constructOnly: true })], p4.prototype, "schedule", void 0), e([y({ constructOnly: true })], p4.prototype, "hasZ", void 0), e([y({ constructOnly: true })], p4.prototype, "elevationAlignPointsInFeatures", void 0), e([y({ constructOnly: true })], p4.prototype, "queryForSymbologySnapping", void 0), e([y({ readOnly: true })], p4.prototype, "updating", null), e([y({ readOnly: true })], p4.prototype, "availability", void 0), e([y()], p4.prototype, "_workerHandleUpdating", void 0), p4 = e([a("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceSnappingSourceWorkerHandle")], p4);
var c2 = class extends h4 {
  constructor(e3, t6) {
    super("FeatureServiceSnappingSourceWorker", "fetchCandidates", {}, e3, { strategy: "dedicated", client: t6 });
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTilesSimple.js
var a6 = class extends v {
  get tiles() {
    return [new t5("0/0/0", 0, 0, 0, o4(-1e8, -1e8, 1e8, 1e8))];
  }
  get tileInfo() {
    return new j({ origin: new w({ x: -1e8, y: 1e8, spatialReference: this.layer.spatialReference }), size: [512, 512], lods: [new p2({ level: 0, scale: 1, resolution: 390625 })], spatialReference: this.layer.spatialReference });
  }
  get tileSize() {
    return this.tileInfo.size[0];
  }
  constructor(e3) {
    super(e3), this.pointOfInterest = null;
  }
};
e([y({ readOnly: true })], a6.prototype, "tiles", null), e([y({ readOnly: true })], a6.prototype, "tileInfo", null), e([y({ readOnly: true })], a6.prototype, "tileSize", null), e([y({ constructOnly: true })], a6.prototype, "layer", void 0), e([y()], a6.prototype, "pointOfInterest", void 0), a6 = e([a("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTilesSimple")], a6);

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/FeatureServiceSnappingSource.js
var T = class extends a3(v) {
  get _updateTilesParameters() {
    return { tiles: this._tilesOfInterest.tiles, tileInfo: this._tilesOfInterest.tileInfo, tileSize: this._tilesOfInterest.tileSize };
  }
  get updating() {
    var _a;
    return ((_a = this._workerHandle) == null ? void 0 : _a.updating) || this.updatingHandles.updating;
  }
  get configuration() {
    const { view: e3 } = this, t6 = r(e3) ? e3.type : "2d";
    return { filter: this._layer.createQuery(), customParameters: this._layer.customParameters, viewType: t6 };
  }
  get availability() {
    var _a;
    return ((_a = this._workerHandle) == null ? void 0 : _a.availability) ?? 0;
  }
  get _layer() {
    return this.layerSource.layer;
  }
  constructor(e3) {
    super(e3), this._workerHandle = null, this._debug = null;
  }
  initialize() {
    let e3;
    const t6 = this.view;
    if (r(t6)) switch (t6.type) {
      case "2d":
        this._tilesOfInterest = new u({ view: t6, layer: this._layer }), e3 = this._workerHandle = new p4();
        break;
      case "3d": {
        const { resourceController: r2 } = t6, i2 = this._layer, s2 = t6.whenLayerView(i2);
        this._tilesOfInterest = new u3({ view: t6 }), e3 = this._workerHandle = new p4({ schedule: (e4) => r2.immediate.schedule(e4), hasZ: this._layer.hasZ && (this._layer.returnZ ?? true), elevationAlignPointsInFeatures: async (e4, t7) => {
          const r3 = await s2;
          return f2(t7), r3.elevationAlignPointsInFeatures(e4, t7);
        }, queryForSymbologySnapping: async (e4, t7) => {
          const r3 = await s2;
          return f2(t7), r3.queryForSymbologySnapping(e4, t7);
        } });
        const h5 = new t4(null);
        s2.then((e4) => h5.set(e4)), this.addHandles([t6.elevationProvider.on("elevation-change", ({ context: t7 }) => {
          const { elevationInfo: r3 } = i2;
          y3(t7, r3) && g2(e3.notifyElevationSourceChange());
        }), l(() => i2.elevationInfo, () => g2(e3.notifyElevationSourceChange()), h2), l(() => o(h5.get(), ({ processor: e4 }) => e4 == null ? void 0 : e4.renderer), () => g2(e3.notifySymbologyChange()), h2), l(() => g(h5.get(), false, (e4) => e4.symbologySnappingSupported), (t7) => g2(e3.setSymbologySnappingSupported(t7)), h2), a2(() => {
          var _a;
          return (_a = f(h5.get())) == null ? void 0 : _a.layer;
        }, ["edits", "apply-edits", "graphic-update"], () => e3.notifySymbologyChange())]);
        break;
      }
    }
    else this._tilesOfInterest = new a6({ layer: this._layer }), e3 = this._workerHandle = new p4();
    this.handles.add([t2(e3)]), g2(e3.setup({ layer: this._layer, spatialReference: this.spatialReference, configuration: this.configuration }, null)), this.updatingHandles.add(() => this._updateTilesParameters, () => g2(e3.updateTiles(this._updateTilesParameters, null)), h2), this.handles.add([l(() => this.configuration, (t7) => g2(e3.configure(t7, null)), U)]), r(t6) && this.handles.add(l(() => p.FEATURE_SERVICE_SNAPPING_SOURCE_TILE_TREE_SHOW_TILES, (r2) => {
      r2 && !this._debug ? (this._debug = new d2({ view: t6, handle: e3 }), this.handles.add(t2(this._debug), "debug")) : !r2 && this._debug && this.handles.remove("debug");
    }, h2)), this.handles.add(this.layerSource.layer.on("apply-edits", (t7) => {
      g2(e3.applyEdits(t7, null));
    }));
  }
  refresh() {
    var _a;
    (_a = this._workerHandle) == null ? void 0 : _a.refresh(null);
  }
  async fetchCandidates(e3, t6) {
    const { coordinateHelper: r2, point: i2 } = e3;
    this._tilesOfInterest.pointOfInterest = r2.arrayToPoint(i2);
    const s2 = this._getGroundElevation;
    return (await this._workerHandle.fetchCandidates({ ...e3 }, t6)).candidates.map((e4) => i(e4, s2));
  }
  getDebugInfo(e3) {
    return this._workerHandle.getDebugInfo(e3);
  }
  get _getGroundElevation() {
    return p3(this.view);
  }
};
e([y({ constructOnly: true })], T.prototype, "spatialReference", void 0), e([y({ constructOnly: true })], T.prototype, "layerSource", void 0), e([y({ constructOnly: true })], T.prototype, "view", void 0), e([y()], T.prototype, "_tilesOfInterest", void 0), e([y({ readOnly: true })], T.prototype, "_updateTilesParameters", null), e([y({ readOnly: true })], T.prototype, "updating", null), e([y({ readOnly: true })], T.prototype, "configuration", null), e([y({ readOnly: true })], T.prototype, "availability", null), e([y()], T.prototype, "_getGroundElevation", null), T = e([a("esri.views.interactive.snapping.featureSources.FeatureServiceSnappingSource")], T);
export {
  T as FeatureServiceSnappingSource
};
//# sourceMappingURL=FeatureServiceSnappingSource-XFWU6D4F.js.map
