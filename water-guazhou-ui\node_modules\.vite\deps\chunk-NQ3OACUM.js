import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/layers/support/FeatureTemplate.js
var l2 = new s({ esriFeatureEditToolAutoCompletePolygon: "auto-complete-polygon", esriFeatureEditToolCircle: "circle", esriFeatureEditToolEllipse: "ellipse", esriFeatureEditToolFreehand: "freehand", esriFeatureEditToolLine: "line", esriFeatureEditToolNone: "none", esriFeatureEditToolPoint: "point", esriFeatureEditToolPolygon: "polygon", esriFeatureEditToolRectangle: "rectangle", esriFeatureEditToolArrow: "arrow", esriFeatureEditToolTriangle: "triangle", esriFeatureEditToolLeftArrow: "left-arrow", esriFeatureEditToolRightArrow: "right-arrow", esriFeatureEditToolUpArrow: "up-arrow", esriFeatureEditToolDownArrow: "down-arrow" });
var a2 = class extends i(l) {
  constructor(o) {
    super(o), this.name = null, this.description = null, this.drawingTool = null, this.prototype = null, this.thumbnail = null;
  }
};
e([y({ json: { write: true } })], a2.prototype, "name", void 0), e([y({ json: { write: true } })], a2.prototype, "description", void 0), e([y({ json: { read: l2.read, write: l2.write } })], a2.prototype, "drawingTool", void 0), e([y({ json: { write: true } })], a2.prototype, "prototype", void 0), e([y({ json: { write: true } })], a2.prototype, "thumbnail", void 0), a2 = e([a("esri.layers.support.FeatureTemplate")], a2);
var p = a2;

export {
  p
};
//# sourceMappingURL=chunk-NQ3OACUM.js.map
