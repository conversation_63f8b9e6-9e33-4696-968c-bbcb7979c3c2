{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/CustomParametersMixin.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as e}from\"../../core/accessorSupport/decorators/subclass.js\";const o=o=>{let t=class extends o{constructor(){super(...arguments),this.customParameters=null}};return r([s({type:Object,json:{write:{overridePolicy:r=>({enabled:!!(r&&Object.keys(r).length>0)})}}})],t.prototype,\"customParameters\",void 0),t=r([e(\"esri.layers.mixins.CustomParametersMixin\")],t),t};export{o as CustomParametersMixin};\n"], "mappings": ";;;;;;;;;AAI0R,IAAM,IAAE,CAAAA,OAAG;AAAC,MAAI,IAAE,cAAcA,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,mBAAiB;AAAA,IAAI;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,gBAAe,QAAI,EAAC,SAAQ,CAAC,EAAE,KAAG,OAAO,KAAK,CAAC,EAAE,SAAO,GAAE,GAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAE,CAAC,GAAE;AAAC;", "names": ["o"]}