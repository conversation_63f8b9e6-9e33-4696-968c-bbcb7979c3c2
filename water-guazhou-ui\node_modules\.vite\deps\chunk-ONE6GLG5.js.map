{"version": 3, "sources": ["../../@arcgis/core/core/accessorSupport/tracking/ObservableValue.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{trackAccess as e}from\"../tracking.js\";import{SimpleObservable as s}from\"./SimpleObservable.js\";class t{constructor(e){this._observable=new s,this._value=e}get(){return e(this._observable),this._value}set(e){e!==this._value&&(this._value=e,this._observable.notify())}}export{t as ObservableValue};\n"], "mappings": ";;;;;;;;AAIsG,IAAM,IAAN,MAAO;AAAA,EAAC,YAAY,GAAE;AAAC,SAAK,cAAY,IAAI,KAAE,KAAK,SAAO;AAAA,EAAC;AAAA,EAAC,MAAK;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,GAAE;AAAC,UAAI,KAAK,WAAS,KAAK,SAAO,GAAE,KAAK,YAAY,OAAO;AAAA,EAAE;AAAC;", "names": []}