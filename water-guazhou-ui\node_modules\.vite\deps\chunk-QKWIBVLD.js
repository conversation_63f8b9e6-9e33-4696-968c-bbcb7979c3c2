import {
  E
} from "./chunk-FTRLEBHJ.js";
import {
  J
} from "./chunk-RRNRSHX3.js";
import {
  D as D2,
  G,
  L,
  M,
  P
} from "./chunk-4M3AMTD4.js";
import {
  a
} from "./chunk-HXJOBP6R.js";
import {
  e
} from "./chunk-MZ267CZB.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  D
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/engine/DisplayObject.js
var i = 1 / has("mapview-transitions-duration");
var r = class extends n {
  constructor() {
    super(...arguments), this._fadeOutResolver = null, this._fadeInResolver = null, this._clips = null, this.computedVisible = true, this.computedOpacity = 1, this.fadeTransitionEnabled = false, this.inFadeTransition = false, this._isReady = false, this._opacity = 1, this.parent = null, this._stage = null, this._visible = true;
  }
  get clips() {
    return this._clips;
  }
  set clips(e3) {
    this._clips = e3, this.requestRender();
  }
  get isReady() {
    return this._isReady;
  }
  get opacity() {
    return this._opacity;
  }
  set opacity(e3) {
    this._opacity !== e3 && (this._opacity = Math.min(1, Math.max(e3, 0)), this.requestRender());
  }
  get stage() {
    return this._stage;
  }
  set stage(e3) {
    var _a;
    if (this._stage === e3) return;
    const t3 = this._stage;
    this._stage = e3, e3 ? ((_a = this._stage) == null ? void 0 : _a.untrashDisplayObject(this)) || (this.onAttach(), this.emit("attach")) : t3 == null ? void 0 : t3.trashDisplayObject(this);
  }
  get transforms() {
    return this._getTransforms();
  }
  _getTransforms() {
    return t(this._transforms) && (this._transforms = this._createTransforms()), this._transforms;
  }
  get visible() {
    return this._visible;
  }
  set visible(e3) {
    this._visible !== e3 && (this._visible = e3, this.requestRender());
  }
  fadeIn() {
    return this._fadeInResolver || (this._fadeOutResolver && (this._fadeOutResolver(), this._fadeOutResolver = null), this.opacity = 1, this.computedOpacity = 0, this.fadeTransitionEnabled = true, this._fadeInResolver = D(), this.requestRender()), this._fadeInResolver.promise;
  }
  fadeOut() {
    return this._fadeOutResolver || (this.opacity = 0, this._fadeInResolver && (this._fadeInResolver(), this._fadeInResolver = null), this.fadeTransitionEnabled = true, this._fadeOutResolver = D(), this.requestRender()), this._fadeOutResolver.promise;
  }
  endTransitions() {
    var _a, _b;
    (_a = this._fadeInResolver) == null ? void 0 : _a.call(this), this._fadeInResolver = null, (_b = this._fadeOutResolver) == null ? void 0 : _b.call(this), this._fadeOutResolver = null, this.computedOpacity = this.visible ? this.opacity : 0, this.requestRender();
  }
  beforeRender(e3) {
    this.updateTransitionProperties(e3.deltaTime, e3.state.scale);
  }
  afterRender(e3) {
    this._fadeInResolver && this.computedOpacity === this.opacity ? (this._fadeInResolver(), this._fadeInResolver = null) : this._fadeOutResolver && 0 === this.computedOpacity && (this._fadeOutResolver(), this._fadeOutResolver = null);
  }
  remove() {
    var _a;
    (_a = this.parent) == null ? void 0 : _a.removeChild(this);
  }
  setTransform(e3) {
  }
  processRender(e3) {
    this.stage && this.computedVisible && this.doRender(e3);
  }
  requestRender() {
    this.stage && this.stage.requestRender();
  }
  processDetach() {
    this._fadeInResolver && (this._fadeInResolver(), this._fadeInResolver = null), this._fadeOutResolver && (this._fadeOutResolver(), this._fadeOutResolver = null), this.onDetach(), this.emit("detach");
  }
  updateTransitionProperties(e3, t3) {
    if (this.fadeTransitionEnabled) {
      const t4 = this._fadeOutResolver || !this.visible ? 0 : this.opacity, s2 = this.computedOpacity;
      if (s2 === t4) this.computedVisible = this.visible;
      else {
        const r2 = e3 * i;
        this.computedOpacity = s2 > t4 ? Math.max(t4, s2 - r2) : Math.min(t4, s2 + r2), this.computedVisible = this.computedOpacity > 0;
        const a2 = t4 === this.computedOpacity;
        this.inFadeTransition = !a2, a2 || this.requestRender();
      }
    } else this.computedOpacity = this.opacity, this.computedVisible = this.visible;
  }
  onAttach() {
  }
  onDetach() {
  }
  doRender(e3) {
  }
  ready() {
    this._isReady || (this._isReady = true, this.emit("isReady"), this.requestRender());
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/highlight/parameters.js
var o = 1;
var t2 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1];
var i2 = [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1];
var n2 = 256;
var e2 = { outlineWidth: 1.3, outerHaloWidth: 0.4, innerHaloWidth: 0.4, outlinePosition: 0 };

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/highlight/HighlightGradient.js
var u = s.getLogger("esri.views.2d.engine.webgl.painter.highlight.HighlightGradient");
function C(o2, i3) {
  i3.fillColor[0] = o2.color.r / 255, i3.fillColor[1] = o2.color.g / 255, i3.fillColor[2] = o2.color.b / 255, i3.fillColor[3] = o2.color.a, o2.haloColor ? (i3.outlineColor[0] = o2.haloColor.r / 255, i3.outlineColor[1] = o2.haloColor.g / 255, i3.outlineColor[2] = o2.haloColor.b / 255, i3.outlineColor[3] = o2.haloColor.a) : (i3.outlineColor[0] = i3.fillColor[0], i3.outlineColor[1] = i3.fillColor[1], i3.outlineColor[2] = i3.fillColor[2], i3.outlineColor[3] = i3.fillColor[3]), i3.fillColor[3] *= o2.fillOpacity, i3.outlineColor[3] *= o2.haloOpacity, i3.fillColor[0] *= i3.fillColor[3], i3.fillColor[1] *= i3.fillColor[3], i3.fillColor[2] *= i3.fillColor[3], i3.outlineColor[0] *= i3.outlineColor[3], i3.outlineColor[1] *= i3.outlineColor[3], i3.outlineColor[2] *= i3.outlineColor[3], i3.outlineWidth = e2.outlineWidth, i3.outerHaloWidth = e2.outerHaloWidth, i3.innerHaloWidth = e2.innerHaloWidth, i3.outlinePosition = e2.outlinePosition;
}
var g = [0, 0, 0, 0];
var f = class {
  constructor() {
    this._convertedHighlightOptions = { fillColor: [0.2 * 0.75, 0.6 * 0.75, 0.675, 0.75], outlineColor: [0.2 * 0.9, 0.54, 0.81, 0.9], outlinePosition: e2.outlinePosition, outlineWidth: e2.outlineWidth, innerHaloWidth: e2.innerHaloWidth, outerHaloWidth: e2.outerHaloWidth }, this._shadeTexChanged = true, this._texelData = new Uint8Array(4 * n2), this._minMaxDistance = [0, 0];
  }
  setHighlightOptions(o2) {
    const i3 = this._convertedHighlightOptions;
    C(o2, i3);
    const t3 = i3.outlinePosition - i3.outlineWidth / 2 - i3.outerHaloWidth, r2 = i3.outlinePosition - i3.outlineWidth / 2, n3 = i3.outlinePosition + i3.outlineWidth / 2, h2 = i3.outlinePosition + i3.outlineWidth / 2 + i3.innerHaloWidth, a2 = Math.sqrt(Math.PI / 2) * o, s2 = Math.abs(t3) > a2 ? Math.round(10 * (Math.abs(t3) - a2)) / 10 : 0, d = Math.abs(h2) > a2 ? Math.round(10 * (Math.abs(h2) - a2)) / 10 : 0;
    let f2;
    s2 && !d ? u.error("The outer rim of the highlight is " + s2 + "px away from the edge of the feature; consider reducing some width values or shifting the outline position towards positive values (inwards).") : !s2 && d ? u.error("The inner rim of the highlight is " + d + "px away from the edge of the feature; consider reducing some width values or shifting the outline position towards negative values (outwards).") : s2 && d && u.error("The highlight is " + Math.max(s2, d) + "px away from the edge of the feature; consider reducing some width values.");
    const c = [void 0, void 0, void 0, void 0];
    function m(o3, i4, t4) {
      c[0] = (1 - t4) * o3[0] + t4 * i4[0], c[1] = (1 - t4) * o3[1] + t4 * i4[1], c[2] = (1 - t4) * o3[2] + t4 * i4[2], c[3] = (1 - t4) * o3[3] + t4 * i4[3];
    }
    const { _texelData: p } = this;
    for (let l = 0; l < n2; ++l) f2 = t3 + l / (n2 - 1) * (h2 - t3), f2 < t3 ? (c[4 * l + 0] = 0, c[4 * l + 1] = 0, c[4 * l + 2] = 0, c[4 * l + 3] = 0) : f2 < r2 ? m(g, i3.outlineColor, (f2 - t3) / (r2 - t3)) : f2 < n3 ? [c[0], c[1], c[2], c[3]] = i3.outlineColor : f2 < h2 ? m(i3.outlineColor, i3.fillColor, (f2 - n3) / (h2 - n3)) : [c[4 * l + 0], c[4 * l + 1], c[4 * l + 2], c[4 * l + 3]] = i3.fillColor, p[4 * l + 0] = 255 * c[0], p[4 * l + 1] = 255 * c[1], p[4 * l + 2] = 255 * c[2], p[4 * l + 3] = 255 * c[3];
    this._minMaxDistance[0] = t3, this._minMaxDistance[1] = h2, this._shadeTexChanged = true;
  }
  applyHighlightOptions(o2, t3) {
    this._shadeTex || (this._shadeTex = new E(o2, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, width: n2, height: 1, samplingMode: L.LINEAR })), this._shadeTexChanged && (this._shadeTex.updateData(0, 0, 0, n2, 1, this._texelData), this._shadeTexChanged = false), o2.bindTexture(this._shadeTex, J), t3.setUniform2fv("u_minMaxDistance", this._minMaxDistance);
  }
  destroy() {
    var _a;
    (_a = this._shadeTex) == null ? void 0 : _a.dispose(), this._shadeTex = null;
  }
};

// node_modules/@arcgis/core/views/2d/engine/Container.js
var h = class extends r {
  constructor() {
    super(...arguments), this._childrenSet = /* @__PURE__ */ new Set(), this._needsSort = false, this.children = [], this._effectView = null, this._highlightOptions = null, this._highlightGradient = null;
  }
  get blendMode() {
    return this._blendMode;
  }
  set blendMode(e3) {
    this._blendMode = e3, this.requestRender();
  }
  get clips() {
    return this._clips;
  }
  set clips(e3) {
    this._clips = e3, this.children.forEach((t3) => t3.clips = e3);
  }
  get computedEffects() {
    var _a;
    return ((_a = this._effectView) == null ? void 0 : _a.effects) ?? null;
  }
  get effect() {
    var _a;
    return ((_a = this._effectView) == null ? void 0 : _a.effect) ?? "";
  }
  set effect(e3) {
    (this._effectView || e3) && (this._effectView || (this._effectView = new a()), this._effectView.effect = e3, this.requestRender());
  }
  get highlightOptions() {
    return this._highlightOptions;
  }
  set highlightOptions(e3) {
    if (!e3) return this._highlightOptions = null, void (this._highlightGradient && (this._highlightGradient.destroy(), this._highlightGradient = null, this.requestRender()));
    this._highlightOptions && this._highlightOptions.equals(e3) || (this._highlightOptions = e3, this._highlightGradient || (this._highlightGradient = new f()), this._highlightGradient.setHighlightOptions(e3), this.requestRender());
  }
  updateTransitionProperties(e3, t3) {
    super.updateTransitionProperties(e3, t3), this._effectView && (this._effectView.transitionStep(e3, t3), this._effectView.transitioning && this.requestRender());
  }
  doRender(e3) {
    const t3 = this.createRenderParams(e3);
    this.renderChildren(t3);
  }
  addChild(e3) {
    return this.addChildAt(e3, this.children.length);
  }
  addChildAt(e3, t3 = this.children.length) {
    if (!e3) return e3;
    if (this.contains(e3)) return e3;
    this._needsSort = true;
    const i3 = e3.parent;
    return i3 && i3 !== this && i3.removeChild(e3), t3 >= this.children.length ? this.children.push(e3) : this.children.splice(t3, 0, e3), this._childrenSet.add(e3), e3.parent = this, e3.stage = this.stage, this !== this.stage && (e3.clips = this.clips), this.requestRender(), e3;
  }
  contains(e3) {
    return this._childrenSet.has(e3);
  }
  endTransitions() {
    super.endTransitions(), this._effectView && (this._effectView.endTransitions(), this.requestRender());
  }
  removeAllChildren() {
    this._childrenSet.clear(), this._needsSort = true;
    for (const e3 of this.children) this !== this.stage && (e3.clips = null), e3.stage = null, e3.parent = null;
    this.children.length = 0;
  }
  removeChild(e3) {
    return this.contains(e3) ? this.removeChildAt(this.children.indexOf(e3)) : e3;
  }
  removeChildAt(e3) {
    if (e3 < 0 || e3 >= this.children.length) return null;
    this._needsSort = true;
    const t3 = this.children.splice(e3, 1)[0];
    return this._childrenSet.delete(t3), this !== this.stage && (t3.clips = null), t3.stage = null, t3.parent = null, t3;
  }
  sortChildren(e3) {
    this._needsSort && (this.children.sort(e3), this._needsSort = false);
  }
  beforeRender(e3) {
    super.beforeRender(e3);
    for (const t3 of this.children) t3.beforeRender(e3);
  }
  afterRender(e3) {
    super.afterRender(e3);
    for (const t3 of this.children) t3.afterRender(e3);
  }
  _createTransforms() {
    return { dvs: e() };
  }
  onAttach() {
    super.onAttach();
    const e3 = this.stage;
    for (const t3 of this.children) t3.stage = e3;
  }
  onDetach() {
    super.onDetach();
    for (const e3 of this.children) e3.stage = null;
  }
  renderChildren(e3) {
    for (const t3 of this.children) t3.processRender(e3);
  }
  createRenderParams(e3) {
    return { ...e3, blendMode: this.blendMode, effects: this.computedEffects, globalOpacity: e3.globalOpacity * this.computedOpacity, inFadeTransition: this.inFadeTransition, highlightGradient: this._highlightGradient || e3.highlightGradient };
  }
};

export {
  r,
  o,
  t2 as t,
  i2 as i,
  h
};
//# sourceMappingURL=chunk-QKWIBVLD.js.map
