import {
  e as e2
} from "./chunk-SY6DBVDS.js";
import {
  a as a2,
  c,
  e
} from "./chunk-WXFAAYJL.js";
import {
  r as r2
} from "./chunk-2CM7MIII.js";
import {
  A,
  C,
  a,
  f,
  j,
  p as p2,
  r2 as r3,
  v
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  p,
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/workers/utils.js
var t;
!function(t2) {
  t2[t2.HANDSHAKE = 0] = "HANDSHAKE", t2[t2.OPEN = 1] = "OPEN", t2[t2.OPENED = 2] = "OPENED", t2[t2.RESPONSE = 3] = "RESPONSE", t2[t2.INVOKE = 4] = "INVOKE", t2[t2.ABORT = 5] = "ABORT", t2[t2.CLOSE = 6] = "CLOSE", t2[t2.OPEN_PORT = 7] = "OPEN_PORT", t2[t2.ON = 8] = "ON";
}(t || (t = {}));
var e3 = 0;
function r4() {
  return e3++;
}
function n(t2) {
  return t2 && "object" == typeof t2 && ("result" in t2 || "transferList" in t2);
}
function s3(t2) {
  return t2 ? "string" == typeof t2 ? JSON.stringify({ name: "message", message: t2 }) : t2.toJSON ? JSON.stringify(t2) : JSON.stringify({ name: t2.name, message: t2.message, details: t2.details || { stack: t2.stack } }) : null;
}
function o(e4, r5, f4, a4) {
  if (r5.type === t.OPEN_PORT) return void e4.postMessage(r5, [r5.port]);
  if (r5.type !== t.INVOKE && r5.type !== t.RESPONSE) return void e4.postMessage(r5);
  let u;
  if (n(f4) ? (u = i(f4.transferList), r5.data = f4.result) : (u = i(a4), r5.data = f4), u) {
    if (has("ff")) {
      for (const n2 of u) if ("byteLength" in n2 && n2.byteLength > 267386880) {
        const n3 = "Worker call with large ArrayBuffer would crash Firefox";
        switch (r5.type) {
          case t.INVOKE:
            throw n3;
          case t.RESPONSE:
            return void o(e4, { type: t.RESPONSE, jobId: r5.jobId, error: s3(n3) });
        }
      }
    }
    e4.postMessage(r5, u);
  } else e4.postMessage(r5);
}
function f2(t2) {
  if (!t2) return null;
  const e4 = t2.data;
  return e4 ? "string" == typeof e4 ? JSON.parse(e4) : e4 : null;
}
function i(t2) {
  if (!t2 || !t2.length) return null;
  if (has("esri-workers-arraybuffer-transfer")) return t2;
  const e4 = t2.filter((t3) => !a3(t3));
  return e4.length ? e4 : null;
}
function a3(t2) {
  return t2 instanceof ArrayBuffer || t2 && t2.constructor && "ArrayBuffer" === t2.constructor.name;
}

// node_modules/@arcgis/core/core/workers/RemoteClient.js
var k = { statsWorker: () => import("./statsWorker-7G2L65Y6.js"), geometryEngineWorker: () => import("./geometryEngineWorker-66VUWNYZ.js"), CSVSourceWorker: () => import("./CSVSourceWorker-UUZH2NBH.js"), EdgeProcessingWorker: () => import("./EdgeProcessingWorker-OZMG2WV4.js"), ElevationSamplerWorker: () => import("./ElevationSamplerWorker-M2MGTZSV.js"), FeatureServiceSnappingSourceWorker: () => import("./FeatureServiceSnappingSourceWorker-BD5TAARB.js"), GeoJSONSourceWorker: () => import("./GeoJSONSourceWorker-3W5BCI56.js"), LercWorker: () => import("./LercWorker-2BL4MJWC.js"), MemorySourceWorker: () => import("./MemorySourceWorker-L5LXJQMM.js"), PBFDecoderWorker: () => import("./PBFDecoderWorker-NCR2U5LA.js"), Pipeline: () => import("./Pipeline-SRLY5P2Y.js"), PointCloudWorker: () => import("./PointCloudWorker-DC5JN46X.js"), RasterWorker: () => import("./RasterWorker-TRY3NDKW.js"), SceneLayerSnappingSourceWorker: () => import("./SceneLayerSnappingSourceWorker-SXUQF3SF.js"), SceneLayerWorker: () => import("./SceneLayerWorker-CFRUNABD.js"), WFSSourceWorker: () => import("./WFSSourceWorker-2L7Z7V4X.js"), WorkerTileHandler: () => import("./WorkerTileHandler-G3NKJYFE.js") };
var { CLOSE: b, ABORT: v2, INVOKE: y, RESPONSE: j2, OPEN_PORT: S, ON: f3 } = t;
var W = 2;
var M = class {
  constructor(e4) {
    this._timer = null, this._cancelledJobIds = /* @__PURE__ */ new Set(), this._invokeMessages = [], this._invoke = e4, this._timer = null, this._process = this._process.bind(this);
  }
  push(e4) {
    e4.type === t.ABORT ? this._cancelledJobIds.add(e4.jobId) : (this._invokeMessages.push(e4), null === this._timer && (this._timer = setTimeout(this._process, 0)));
  }
  clear() {
    this._invokeMessages.length = 0, this._cancelledJobIds.clear(), this._timer = null;
  }
  _process() {
    this._timer = null;
    for (const e4 of this._invokeMessages) this._cancelledJobIds.has(e4.jobId) || this._invoke(e4);
    this._cancelledJobIds.clear(), this._invokeMessages.length = 0;
  }
};
var w = class _w {
  static connect(e4) {
    const s4 = new MessageChannel();
    let t2;
    t2 = "function" == typeof e4 ? new e4() : "default" in e4 && "function" == typeof e4.default ? new e4.default() : e4;
    const o2 = new _w(s4.port1, { channel: s4, client: t2 }, () => null);
    return "object" == typeof t2 && "remoteClient" in t2 && (t2.remoteClient = o2), _w.clients.set(o2, t2), s4.port2;
  }
  static loadWorker(e4) {
    const s4 = k[e4];
    return s4 ? s4() : Promise.resolve(null);
  }
  constructor(e4, s4, t2) {
    this._port = e4, this._getNextJob = t2, this._outJobs = /* @__PURE__ */ new Map(), this._inJobs = /* @__PURE__ */ new Map(), this._invokeQueue = new M((e5) => this._onInvokeMessage(e5)), this._client = s4.client, this._onMessage = this._onMessage.bind(this), this._channel = s4.channel, this._schedule = s4.schedule, this._port.addEventListener("message", this._onMessage), this._port.start();
  }
  close() {
    this._post({ type: b }), this._close();
  }
  isBusy() {
    return this._outJobs.size > 0;
  }
  invoke(e4, t2, r5) {
    const c2 = r5 && r5.signal, l = r5 && r5.transferList;
    if (!this._port) return Promise.reject(new s2("worker:port-closed", `Cannot call invoke('${e4}'), port is closed`, { methodName: e4, data: t2 }));
    const h2 = r4();
    return new Promise((s4, r6) => {
      if (p2(c2)) return this._processWork(), void r6(a());
      const p3 = v(c2, () => {
        const e5 = this._outJobs.get(h2);
        e5 && (this._outJobs.delete(h2), this._processWork(), p(e5.abortHandle), this._post({ type: v2, jobId: h2 }), r6(a()));
      }), u = { resolve: s4, reject: r6, abortHandle: p3, debugInfo: e4 };
      this._outJobs.set(h2, u), this._post({ type: y, jobId: h2, methodName: e4, abortable: null != c2 }, t2, l);
    });
  }
  on(e4, s4) {
    const t2 = new MessageChannel();
    function o2(e5) {
      s4(e5.data);
    }
    return this._port.postMessage({ type: t.ON, eventType: e4, port: t2.port2 }, [t2.port2]), t2.port1.addEventListener("message", o2), t2.port1.start(), { remove() {
      t2.port1.postMessage({ type: t.CLOSE }), t2.port1.close(), t2.port1.removeEventListener("message", o2);
    } };
  }
  jobAdded() {
    this._processWork();
  }
  openPort() {
    const e4 = new MessageChannel();
    return this._post({ type: S, port: e4.port2 }), e4.port1;
  }
  _processWork() {
    if (this._outJobs.size >= W) return;
    const e4 = this._getNextJob();
    if (!e4) return;
    const { methodName: s4, data: t2, invokeOptions: o2, deferred: r5 } = e4;
    this.invoke(s4, t2, o2).then((e5) => r5.resolve(e5)).catch((e5) => r5.reject(e5));
  }
  _close() {
    this._channel && (this._channel = void 0), this._port.removeEventListener("message", this._onMessage), this._port.close(), this._outJobs.forEach((e4) => {
      p(e4.abortHandle), e4.reject(a(`Worker closing, aborting job calling '${e4.debugInfo}'`));
    }), this._inJobs.clear(), this._outJobs.clear(), this._invokeQueue.clear(), this._port = this._client = this._schedule = null;
  }
  _onMessage(e4) {
    r(this._schedule) ? this._schedule(() => this._processMessage(e4)) : this._processMessage(e4);
  }
  _processMessage(e4) {
    const s4 = f2(e4);
    if (s4) switch (s4.type) {
      case j2:
        this._onResponseMessage(s4);
        break;
      case y:
        this._invokeQueue.push(s4);
        break;
      case v2:
        this._onAbortMessage(s4);
        break;
      case b:
        this._onCloseMessage();
        break;
      case S:
        this._onOpenPortMessage(s4);
        break;
      case f3:
        this._onOnMessage(s4);
    }
  }
  _onAbortMessage(e4) {
    const s4 = this._inJobs, t2 = e4.jobId, o2 = s4.get(t2);
    this._invokeQueue.push(e4), o2 && (o2.controller && o2.controller.abort(), s4.delete(t2));
  }
  _onCloseMessage() {
    const e4 = this._client;
    this._close(), e4 && "destroy" in e4 && _w.clients.get(this) === e4 && e4.destroy(), _w.clients.delete(this), e4 && e4.remoteClient && (e4.remoteClient = null);
  }
  _onInvokeMessage(e4) {
    const { methodName: s4, jobId: t2, data: o2, abortable: r5 } = e4, i2 = r5 ? new AbortController() : null, n2 = this._inJobs;
    let a4, p3 = this._client, h2 = p3[s4];
    try {
      if (!h2 && s4 && s4.includes(".")) {
        const e5 = s4.split(".");
        for (let s5 = 0; s5 < e5.length - 1; s5++) p3 = p3[e5[s5]], h2 = p3[e5[s5 + 1]];
      }
      if ("function" != typeof h2) throw new TypeError(`${s4} is not a function`);
      a4 = h2.call(p3, o2, { client: this, signal: i2 ? i2.signal : null });
    } catch (u) {
      return void this._post({ type: j2, jobId: t2, error: s3(u) });
    }
    C(a4) ? (n2.set(t2, { controller: i2, promise: a4 }), a4.then((e5) => {
      n2.has(t2) && (n2.delete(t2), this._post({ type: j2, jobId: t2 }, e5));
    }, (e5) => {
      n2.has(t2) && (n2.delete(t2), j(e5) || this._post({ type: j2, jobId: t2, error: s3(e5 || { message: `Error encountered at method ${s4}` }) }));
    })) : this._post({ type: j2, jobId: t2 }, a4);
  }
  _onOpenPortMessage(e4) {
    new _w(e4.port, { client: this._client }, () => null);
  }
  _onOnMessage(e4) {
    const { port: s4 } = e4, o2 = this._client.on(e4.eventType, (e5) => {
      s4.postMessage(e5);
    }), r5 = r3(e4.port, "message", (e5) => {
      var _a;
      ((_a = f2(e5)) == null ? void 0 : _a.type) === t.CLOSE && (r5.remove(), o2.remove(), s4.close());
    });
  }
  _onResponseMessage(e4) {
    const { jobId: t2, error: r5, data: i2 } = e4, n2 = this._outJobs;
    if (!n2.has(t2)) return;
    const a4 = n2.get(t2);
    n2.delete(t2), this._processWork(), p(a4.abortHandle), r5 ? a4.reject(s2.fromJSON(JSON.parse(r5))) : a4.resolve(i2);
  }
  _post(e4, s4, t2) {
    return o(this._port, e4, s4, t2);
  }
};
w.kernelInfo = { revision: e, version: a2, buildDate: c }, w.clients = /* @__PURE__ */ new Map();

// node_modules/@arcgis/core/core/workers/Connection.js
var h = class {
  constructor() {
    this._inUseClients = new Array(), this._clients = new Array(), this._clientPromises = new Array(), this._ongoingJobsQueue = new e2();
  }
  destroy() {
    this.close();
  }
  get closed() {
    return !this._clients || !this._clients.length;
  }
  open(e4, t2) {
    return new Promise((n2, i2) => {
      let r5 = true;
      const h2 = (e5) => {
        f(t2.signal), r5 && (r5 = false, e5());
      };
      this._clients.length = e4.length, this._clientPromises.length = e4.length, this._inUseClients.length = e4.length;
      for (let o2 = 0; o2 < e4.length; ++o2) {
        const r6 = e4[o2];
        C(r6) ? this._clientPromises[o2] = r6.then((e5) => (this._clients[o2] = new w(e5, t2, () => this._ongoingJobsQueue.pop() ?? null), h2(n2), this._clients[o2]), () => (h2(i2), null)) : (this._clients[o2] = new w(r6, t2, () => this._ongoingJobsQueue.pop() ?? null), this._clientPromises[o2] = Promise.resolve(this._clients[o2]), h2(n2));
      }
    });
  }
  broadcast(e4, t2, s4) {
    const n2 = new Array(this._clientPromises.length);
    for (let i2 = 0; i2 < this._clientPromises.length; ++i2) {
      const o2 = this._clientPromises[i2];
      n2[i2] = o2.then((n3) => n3 == null ? void 0 : n3.invoke(e4, t2, s4));
    }
    return n2;
  }
  close() {
    let e4;
    for (; e4 = this._ongoingJobsQueue.pop(); ) e4.deferred.reject(a(`Worker closing, aborting job calling '${e4.methodName}'`));
    for (const t2 of this._clientPromises) t2.then((e5) => e5 == null ? void 0 : e5.close());
    this._clients.length = 0, this._clientPromises.length = 0;
  }
  invoke(e4, s4, n2) {
    let o2;
    Array.isArray(n2) ? (s.getLogger("esri.core.workers.Connection").warn("invoke()", "The transferList parameter is deprecated, use the options object instead"), o2 = { transferList: n2 }) : o2 = n2;
    const r5 = A();
    this._ongoingJobsQueue.push({ methodName: e4, data: s4, invokeOptions: o2, deferred: r5 });
    for (let t2 = 0; t2 < this._clientPromises.length; t2++) {
      const e5 = this._clients[t2];
      e5 ? e5.jobAdded() : this._clientPromises[t2].then((e6) => e6 == null ? void 0 : e6.jobAdded());
    }
    return r5.promise;
  }
  on(t2, s4) {
    return Promise.all(this._clientPromises).then(() => r2(this._clients.map((e4) => e4.on(t2, s4))));
  }
  openPorts() {
    return new Promise((e4) => {
      const t2 = new Array(this._clientPromises.length);
      let s4 = t2.length;
      for (let n2 = 0; n2 < this._clientPromises.length; ++n2) {
        this._clientPromises[n2].then((i2) => {
          i2 && (t2[n2] = i2.openPort()), 0 == --s4 && e4(t2);
        });
      }
    });
  }
  get test() {
    return { numClients: this._clients.length };
  }
};

export {
  t,
  r4 as r,
  s3 as s,
  o,
  f2 as f,
  w,
  h
};
//# sourceMappingURL=chunk-MNWHGD3K.js.map
