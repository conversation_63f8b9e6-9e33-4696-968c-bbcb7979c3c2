{"version": 3, "sources": ["../../@arcgis/core/chunks/SSAO.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrap as e}from\"../core/maybe.js\";import{s as r}from\"./vec2.js\";import{a as t}from\"./vec2f64.js\";import{ScreenSpacePass as o}from\"../views/3d/webgl-engine/core/shaderLibrary/ScreenSpacePass.glsl.js\";import{ReadLinearDepth as a}from\"../views/3d/webgl-engine/core/shaderLibrary/output/ReadLinearDepth.glsl.js\";import{CameraSpace as n,getZScale as i}from\"../views/3d/webgl-engine/core/shaderLibrary/util/CameraSpace.glsl.js\";import{Float2PassUniform as s}from\"../views/3d/webgl-engine/core/shaderModules/Float2PassUniform.js\";import{FloatPassUniform as c}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as l}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as u}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{Texture2DPassUniform as f}from\"../views/3d/webgl-engine/core/shaderModules/Texture2DPassUniform.js\";const m=16;function d(){const t=new u,d=t.fragment;return t.include(o),d.include(a),t.include(n),d.uniforms.add(new c(\"radius\",((e,r)=>p(r.camera)))),d.code.add(l`vec3 sphere[16];\nvoid fillSphere() {\nsphere[0] = vec3(0.186937, 0.0, 0.0);\nsphere[1] = vec3(0.700542, 0.0, 0.0);\nsphere[2] = vec3(-0.864858, -0.481795, -0.111713);\nsphere[3] = vec3(-0.624773, 0.102853, -0.730153);\nsphere[4] = vec3(-0.387172, 0.260319, 0.007229);\nsphere[5] = vec3(-0.222367, -0.642631, -0.707697);\nsphere[6] = vec3(-0.01336, -0.014956, 0.169662);\nsphere[7] = vec3(0.122575, 0.1544, -0.456944);\nsphere[8] = vec3(-0.177141, 0.85997, -0.42346);\nsphere[9] = vec3(-0.131631, 0.814545, 0.524355);\nsphere[10] = vec3(-0.779469, 0.007991, 0.624833);\nsphere[11] = vec3(0.308092, 0.209288,0.35969);\nsphere[12] = vec3(0.359331, -0.184533, -0.377458);\nsphere[13] = vec3(0.192633, -0.482999, -0.065284);\nsphere[14] = vec3(0.233538, 0.293706, -0.055139);\nsphere[15] = vec3(0.417709, -0.386701, 0.442449);\n}\nfloat fallOffFunction(float vv, float vn, float bias) {\nfloat f = max(radius * radius - vv, 0.0);\nreturn f * f * f * max(vn-bias, 0.0);\n}`),d.code.add(l`float aoValueFromPositionsAndNormal(vec3 C, vec3 n_C, vec3 Q) {\nvec3 v = Q - C;\nfloat vv = dot(v, v);\nfloat vn = dot(normalize(v), n_C);\nreturn fallOffFunction(vv, vn, 0.1);\n}`),d.uniforms.add([new s(\"nearFar\",((e,r)=>r.camera.nearFar)),new f(\"normalMap\",(e=>e.normalTexture)),new f(\"depthMap\",(e=>e.depthTexture)),new s(\"zScale\",((e,r)=>i(r))),new c(\"projScale\",(e=>e.projScale)),new f(\"rnm\",(e=>e.noiseTexture)),new s(\"rnmScale\",((t,o)=>r(v,o.camera.fullWidth/e(t.noiseTexture).descriptor.width,o.camera.fullHeight/e(t.noiseTexture).descriptor.height))),new c(\"intensity\",(e=>e.intensity)),new s(\"screenSize\",((e,t)=>r(v,t.camera.fullWidth,t.camera.fullHeight)))]),d.code.add(l`\n    void main(void) {\n      fillSphere();\n      vec3 fres = normalize((texture2D(rnm, uv * rnmScale).xyz * 2.0) - vec3(1.0));\n      float currentPixelDepth = linearDepthFromTexture(depthMap, uv, nearFar);\n\n      if (-currentPixelDepth>nearFar.y || -currentPixelDepth<nearFar.x) {\n        gl_FragColor = vec4(0.0);\n        return;\n      }\n\n      vec3 currentPixelPos = reconstructPosition(gl_FragCoord.xy,currentPixelDepth);\n\n      // get the normal of current fragment\n      vec4 norm4 = texture2D(normalMap, uv);\n      vec3 norm = vec3(-1.0) + 2.0 * norm4.xyz;\n      bool isTerrain = norm4.w<0.5;\n\n      float sum = .0;\n      vec3 tapPixelPos;\n\n      // note: the factor 2.0 should not be necessary, but makes ssao much nicer.\n      // bug or deviation from CE somewhere else?\n      float ps = projScale / (2.0 * currentPixelPos.z * zScale.x + zScale.y);\n\n      for(int i = 0; i < ${l.int(m)}; ++i) {\n        vec2 unitOffset = reflect(sphere[i], fres).xy;\n        vec2 offset = vec2(-unitOffset * radius * ps);\n\n        //don't use current or very nearby samples\n        if ( abs(offset.x)<2.0 || abs(offset.y)<2.0) continue;\n\n        vec2 tc = vec2(gl_FragCoord.xy + offset);\n        if (tc.x < 0.0 || tc.y < 0.0 || tc.x > screenSize.x || tc.y > screenSize.y) continue;\n        vec2 tcTap = tc / screenSize;\n        float occluderFragmentDepth = linearDepthFromTexture(depthMap, tcTap, nearFar);\n\n        if (isTerrain) {\n          bool isTerrainTap = texture2D(normalMap, tcTap).w<0.5;\n          if (isTerrainTap) {\n            continue;\n          }\n        }\n\n        tapPixelPos = reconstructPosition(tc, occluderFragmentDepth);\n\n        sum+= aoValueFromPositionsAndNormal(currentPixelPos, norm, tapPixelPos);\n      }\n\n      // output the result\n      float A = max(1.0 - sum * intensity / float(${l.int(m)}),0.0);\n\n      // Anti-tone map to reduce contrast and drag dark region farther: (x^0.2 + 1.2 * x^4)/2.2\n      A = (pow(A, 0.2) + 1.2 * A*A*A*A) / 2.2;\n      gl_FragColor = vec4(A);\n    }\n  `),t}function p(e){return Math.max(10,20*e.computeRenderPixelSizeAtDist(Math.abs(4*e.relativeElevation)))}const v=t(),h=Object.freeze(Object.defineProperty({__proto__:null,build:d,getRadius:p},Symbol.toStringTag,{value:\"Module\"}));export{h as S,d as b,p as g};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+4B,IAAM,IAAE;AAAG,SAAS,IAAG;AAAC,QAAM,IAAE,IAAIA,MAAEC,KAAE,EAAE;AAAS,SAAO,EAAE,QAAQD,EAAC,GAAEC,GAAE,QAAQ,CAAC,GAAE,EAAE,QAAQC,EAAC,GAAED,GAAE,SAAS,IAAI,IAAID,GAAE,UAAU,CAACG,IAAEC,OAAI,EAAEA,GAAE,MAAM,CAAE,CAAC,GAAEH,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsB9iC,GAAEA,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,GAAEA,GAAE,SAAS,IAAI,CAAC,IAAIE,GAAE,WAAW,CAACA,IAAEC,OAAIA,GAAE,OAAO,OAAQ,GAAE,IAAI,EAAE,aAAa,CAAAD,OAAGA,GAAE,aAAc,GAAE,IAAI,EAAE,YAAY,CAAAA,OAAGA,GAAE,YAAa,GAAE,IAAIA,GAAE,UAAU,CAACA,IAAEC,OAAI,EAAEA,EAAC,CAAE,GAAE,IAAIJ,GAAE,aAAa,CAAAG,OAAGA,GAAE,SAAU,GAAE,IAAI,EAAE,OAAO,CAAAA,OAAGA,GAAE,YAAa,GAAE,IAAIA,GAAE,YAAY,CAACE,IAAEL,OAAI,EAAE,GAAEA,GAAE,OAAO,YAAU,EAAEK,GAAE,YAAY,EAAE,WAAW,OAAML,GAAE,OAAO,aAAW,EAAEK,GAAE,YAAY,EAAE,WAAW,MAAM,CAAE,GAAE,IAAIL,GAAE,aAAa,CAAAG,OAAGA,GAAE,SAAU,GAAE,IAAIA,GAAE,cAAc,CAACA,IAAEE,OAAI,EAAE,GAAEA,GAAE,OAAO,WAAUA,GAAE,OAAO,UAAU,CAAE,CAAC,CAAC,GAAEJ,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAyB7d,EAAE,IAAI,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oDAyBiB,EAAE,IAAI,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAMzD,GAAE;AAAC;AAAC,SAAS,EAAEE,IAAE;AAAC,SAAO,KAAK,IAAI,IAAG,KAAGA,GAAE,6BAA6B,KAAK,IAAI,IAAEA,GAAE,iBAAiB,CAAC,CAAC;AAAC;AAAC,IAAM,IAAE,EAAE;AAAV,IAAY,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAM,GAAE,WAAU,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["o", "d", "n", "e", "r", "t"]}