{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/lib/AttributeArray.js", "../../@arcgis/core/views/3d/webgl-engine/lib/BoundingInfo.js", "../../@arcgis/core/views/3d/webgl-engine/lib/geometryDataUtils.js", "../../@arcgis/core/views/3d/webgl-engine/lib/Object3DStateID.js", "../../@arcgis/core/views/3d/webgl-engine/materials/renderers/utils.js", "../../@arcgis/core/views/3d/webgl-engine/lib/Geometry.js", "../../@arcgis/core/views/3d/webgl-engine/lib/RenderSlot.js", "../../@arcgis/core/views/3d/webgl-engine/lib/verticalOffsetUtils.js", "../../@arcgis/core/views/3d/webgl-engine/materials/internal/bufferWriterUtils.js", "../../@arcgis/core/views/3d/webgl-engine/materials/DefaultBufferWriter.js", "../../@arcgis/core/views/3d/webgl-engine/lib/StencilUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{NATIVE_ARRAY_MAX_SIZE as r}from\"../../../../core/typedArrayUtil.js\";function t(t){if(t.length<r)return Array.from(t);if(Array.isArray(t))return Float64Array.from(t);switch(t.BYTES_PER_ELEMENT){case 1:return Uint8Array.from(t);case 2:return Uint16Array.from(t);case 4:return Float32Array.from(t);default:return Float64Array.from(t)}}export{t as cloneAttributeData};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport i from\"../../../../core/PooledArray.js\";import{h as t,d as e}from\"../../../../chunks/vec3.js\";import{f as s,a as n,c as r}from\"../../../../chunks/vec3f64.js\";import{assert as h}from\"./Util.js\";class o{constructor(i,e,o,c){this.primitiveIndices=i,this._numIndexPerPrimitive=e,this.indices=o,this.position=c,this._children=void 0,h(i.length>=1),h(o.length%this._numIndexPerPrimitive==0),h(o.length>=i.length*this._numIndexPerPrimitive),h(3===c.size||4===c.size);const{data:d,size:l}=c,m=i.length;let u=l*o[this._numIndexPerPrimitive*i[0]];a.clear(),a.push(u);const f=s(d[u],d[u+1],d[u+2]),x=n(f);for(let t=0;t<m;++t){const e=this._numIndexPerPrimitive*i[t];for(let i=0;i<this._numIndexPerPrimitive;++i){u=l*o[e+i],a.push(u);let t=d[u];f[0]=Math.min(t,f[0]),x[0]=Math.max(t,x[0]),t=d[u+1],f[1]=Math.min(t,f[1]),x[1]=Math.max(t,x[1]),t=d[u+2],f[2]=Math.min(t,f[2]),x[2]=Math.max(t,x[2])}}this.bbMin=f,this.bbMax=x;const P=t(r(),this.bbMin,this.bbMax,.5);this.radius=.5*Math.max(Math.max(x[0]-f[0],x[1]-f[1]),x[2]-f[2]);let v=this.radius*this.radius;for(let t=0;t<a.length;++t){u=a.getItemAt(t);const i=d[u]-P[0],e=d[u+1]-P[1],s=d[u+2]-P[2],n=i*i+e*e+s*s;if(n<=v)continue;const r=Math.sqrt(n),h=.5*(r-this.radius);this.radius=this.radius+h,v=this.radius*this.radius;const o=h/r;P[0]+=i*o,P[1]+=e*o,P[2]+=s*o}this.center=P,a.clear()}getChildren(){if(this._children||e(this.bbMin,this.bbMax)<=1)return this._children;const i=t(r(),this.bbMin,this.bbMax,.5),s=this.primitiveIndices.length,n=new Uint8Array(s),h=new Array(8);for(let t=0;t<8;++t)h[t]=0;const{data:a,size:c}=this.position;for(let t=0;t<s;++t){let e=0;const s=this._numIndexPerPrimitive*this.primitiveIndices[t];let r=c*this.indices[s],o=a[r],d=a[r+1],l=a[r+2];for(let i=1;i<this._numIndexPerPrimitive;++i){r=c*this.indices[s+i];const t=a[r],e=a[r+1],n=a[r+2];t<o&&(o=t),e<d&&(d=e),n<l&&(l=n)}o<i[0]&&(e|=1),d<i[1]&&(e|=2),l<i[2]&&(e|=4),n[t]=e,++h[e]}let d=0;for(let t=0;t<8;++t)h[t]>0&&++d;if(d<2)return;const l=new Array(8);for(let t=0;t<8;++t)l[t]=h[t]>0?new Uint32Array(h[t]):void 0;for(let t=0;t<8;++t)h[t]=0;for(let t=0;t<s;++t){const i=n[t];l[i][h[i]++]=this.primitiveIndices[t]}this._children=new Array;for(let t=0;t<8;++t)void 0!==l[t]&&this._children.push(new o(l[t],this._numIndexPerPrimitive,this.indices,this.position));return this._children}static prune(){a.prune()}}const a=new i({deallocator:null});export{o as BoundingInfo};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{s as t,a as n,g as e,j as r}from\"../../../../chunks/vec3.js\";import{c as o}from\"../../../../chunks/vec3f64.js\";import{areaPoints3d as s}from\"../../../../geometry/support/triangle.js\";function c(r,o,c){if(!r||!o)return!1;const{size:a,data:f}=r;t(c,0,0,0),t(g,0,0,0);let m=0,h=0;for(let p=0;p<o.length-2;p+=3){const r=o[p+0]*a,j=o[p+1]*a,d=o[p+2]*a;t(i,f[r+0],f[r+1],f[r+2]),t(u,f[j+0],f[j+1],f[j+2]),t(l,f[d+0],f[d+1],f[d+2]);const z=s(i,u,l);z?(n(i,i,u),n(i,i,l),e(i,i,1/3*z),n(c,c,i),m+=z):(n(g,g,i),n(g,g,u),n(g,g,l),h+=3)}return(0!==h||0!==m)&&(0!==m?(e(c,c,1/m),!0):0!==h&&(e(c,g,1/h),!0))}function a(n,r,o){if(!n||!r)return!1;const{size:s,data:c}=n;t(o,0,0,0);let a=-1,f=0;for(let t=0;t<r.length;t++){const n=r[t]*s;a!==n&&(o[0]+=c[n+0],o[1]+=c[n+1],o[2]+=c[n+2],f++),a=n}return f>1&&e(o,o,1/f),f>0}function f(o,s,c,a){if(!o)return!1;t(a,0,0,0),t(g,0,0,0);let f=0,l=0;const{size:m,data:h}=o,p=s?s.length-1:h.length/m-1,j=p+(c?2:0);for(let t=0;t<j;t+=2){const o=t<p?t:p,c=t<p?t+1:0,j=(s?s[o]:o)*m,d=(s?s[c]:c)*m;i[0]=h[j],i[1]=h[j+1],i[2]=h[j+2],u[0]=h[d],u[1]=h[d+1],u[2]=h[d+2],e(i,n(i,i,u),.5);const z=r(i,u);z>0?(n(a,a,e(i,i,z)),f+=z):0===f&&(n(g,g,i),l++)}return 0!==f?(e(a,a,1/f),!0):0!==l&&(e(a,g,1/l),!0)}const i=o(),u=o(),l=o(),g=o();export{f as computeAttachmentOriginLines,a as computeAttachmentOriginPoints,c as computeAttachmentOriginTriangles};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{generateUID as o}from\"../../../../core/uid.js\";class r{constructor(r){this.channel=r,this.id=o()}}export{r as Object3DStateID};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t}from\"../../../../../core/maybe.js\";import{c as e}from\"../../../../../chunks/vec3f64.js\";import{encodeDoubleArray as r}from\"../../../../webgl/doublePrecisionUtils.js\";function n(e,r){return t(e)&&(e=[]),e.push(r),e}function o(e,r){if(t(e))return null;const n=e.filter((t=>t!==r));return 0===n.length?null:n}function s(t,e,n,o,s){i[0]=t.get(e,0),i[1]=t.get(e,1),i[2]=t.get(e,2),r(i,u,3),n.set(s,0,u[0]),o.set(s,0,u[1]),n.set(s,1,u[2]),o.set(s,1,u[3]),n.set(s,2,u[4]),o.set(s,2,u[5])}const i=e(),u=new Float32Array(6);export{n as addObject3DStateID,s as encodeDoubleVec3,o as removeObject3DStateID};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t,isSome as e,unwrapOr as i}from\"../../../../core/maybe.js\";import{I as s,b as n}from\"../../../../chunks/mat4f64.js\";import{m as r}from\"../../../../chunks/vec3.js\";import{cloneAttributeData as o}from\"./AttributeArray.js\";import{Object3DState as h}from\"./basicInterfaces.js\";import{BoundingInfo as a}from\"./BoundingInfo.js\";import{ContentObject as u}from\"./ContentObject.js\";import{ContentObjectType as g}from\"./ContentObjectType.js\";import{computeAttachmentOriginTriangles as c,computeAttachmentOriginLines as m,computeAttachmentOriginPoints as d}from\"./geometryDataUtils.js\";import{generateDefaultIndexArray as l,compactIndices as f}from\"./Indices.js\";import{Object3DStateID as I}from\"./Object3DStateID.js\";import{assert as _}from\"./Util.js\";import{VertexAttribute as b}from\"./VertexAttribute.js\";import{addObject3DStateID as p,removeObject3DStateID as O}from\"../materials/renderers/utils.js\";class v extends u{constructor(t,e,i=[],s=null,n=g.Mesh,r=null,o=-1){super(),this.material=t,this.mapPositions=s,this.type=n,this.objectAndLayerIdColor=r,this.edgeIndicesLength=o,this.visible=!0,this._vertexAttributes=new Map,this._indices=new Map,this._boundingInfo=null;for(const[h,a]of e)a&&this._vertexAttributes.set(h,{...a});if(null==i||0===i.length){const t=A(this._vertexAttributes),e=l(t);this.edgeIndicesLength=this.edgeIndicesLength<0?t:this.edgeIndicesLength;for(const i of this._vertexAttributes.keys())this._indices.set(i,e)}else for(const[h,a]of i)a&&(this._indices.set(h,f(a)),h===b.POSITION&&(this.edgeIndicesLength=this.edgeIndicesLength<0?this._indices.get(h).length:this.edgeIndicesLength))}instantiate(t={}){const e=new v(t.material||this.material,[],void 0,this.mapPositions,this.type,this.objectAndLayerIdColor,this.edgeIndicesLength);return this._vertexAttributes.forEach(((t,i)=>{t.exclusive=!1,e._vertexAttributes.set(i,t)})),this._indices.forEach(((t,i)=>e._indices.set(i,t))),e._boundingInfo=this._boundingInfo,e.transformation=t.transformation||this.transformation,e}get vertexAttributes(){return this._vertexAttributes}getMutableAttribute(t){let e=this._vertexAttributes.get(t);return e&&!e.exclusive&&(e={...e,exclusive:!0,data:o(e.data)},this._vertexAttributes.set(t,e)),e}get indices(){return this._indices}get indexCount(){const t=this._indices.values().next().value;return t?t.length:0}get faceCount(){return this.indexCount/3}get boundingInfo(){return t(this._boundingInfo)&&(this._boundingInfo=this._calculateBoundingInfo()),this._boundingInfo}computeAttachmentOrigin(t){return!!(this.type===g.Mesh?this._computeAttachmentOriginTriangles(t):this.type===g.Line?this._computeAttachmentOriginLines(t):this._computeAttachmentOriginPoints(t))&&(e(this._transformation)&&r(t,t,this._transformation),!0)}_computeAttachmentOriginTriangles(t){const e=this.indices.get(b.POSITION),i=this.vertexAttributes.get(b.POSITION);return c(i,e,t)}_computeAttachmentOriginLines(t){const e=this.vertexAttributes.get(b.POSITION),i=this.indices.get(b.POSITION);return m(e,i,i&&x(this.material.parameters,e,i),t)}_computeAttachmentOriginPoints(t){const e=this.indices.get(b.POSITION),i=this.vertexAttributes.get(b.POSITION);return d(i,e,t)}invalidateBoundingInfo(){this._boundingInfo=null}_calculateBoundingInfo(){const t=this.indices.get(b.POSITION),e=this.vertexAttributes.get(b.POSITION);if(!t||0===t.length||!e)return null;const i=this.type===g.Mesh?3:1;_(t.length%i==0,\"Indexing error: \"+t.length+\" not divisible by \"+i);const s=l(t.length/i);return new a(s,i,t,e)}get transformation(){return i(this._transformation,s)}set transformation(t){this._transformation=t&&t!==s?n(t):null}get shaderTransformation(){return e(this._shaderTransformer)?this._shaderTransformer(this.transformation):this.transformation}get shaderTransformer(){return this._shaderTransformer}set shaderTransformer(t){this._shaderTransformer=t}get hasVolatileTransformation(){return e(this._shaderTransformer)}addHighlight(){const t=new I(h.Highlight);return this.highlights=p(this.highlights,t),t}removeHighlight(t){this.highlights=O(this.highlights,t)}}function A(t){const e=t.values().next().value;return null==e?0:e.data.length/e.size}function x(t,e,i){return!(!(\"isClosed\"in t)||!t.isClosed)&&(i?i.length>2:e.data.length>6)}export{v as Geometry};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar E;!function(E){E[E.INTEGRATED_MESH=0]=\"INTEGRATED_MESH\",E[E.OPAQUE_TERRAIN=1]=\"OPAQUE_TERRAIN\",E[E.OPAQUE_MATERIAL=2]=\"OPAQUE_MATERIAL\",E[E.TRANSPARENT_MATERIAL=3]=\"TRANSPARENT_MATERIAL\",E[E.TRANSPARENT_TERRAIN=4]=\"TRANSPARENT_TERRAIN\",E[E.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL=5]=\"TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL\",E[E.OCCLUDED_TERRAIN=6]=\"OCCLUDED_TERRAIN\",E[<PERSON>.OCCLUDER_MATERIAL=7]=\"OCCLUDER_MATERIAL\",E[E.TRANSPARENT_OCCLUDER_MATERIAL=8]=\"TRANSPARENT_OCCLUDER_MATERIAL\",E[E.OCCLUSION_PIXELS=9]=\"OCCLUSION_PIXELS\",E[E.POSTPROCESSING_ENVIRONMENT_OPAQUE=10]=\"POSTPROCESSING_ENVIRONMENT_OPAQUE\",E[E.POSTPROCESSING_ENVIRONMENT_TRANSPARENT=11]=\"POSTPROCESSING_ENVIRONMENT_TRANSPARENT\",E[E.LASERLINES=12]=\"LASERLINES\",E[E.LASERLINES_CONTRAST_CONTROL=13]=\"LASERLINES_CONTRAST_CONTROL\",E[E.HUD_MATERIAL=14]=\"HUD_MATERIAL\",E[E.LABEL_MATERIAL=15]=\"LABEL_MATERIAL\",E[E.LINE_CALLOUTS=16]=\"LINE_CALLOUTS\",E[E.LINE_CALLOUTS_HUD_DEPTH=17]=\"LINE_CALLOUTS_HUD_DEPTH\",E[E.DRAPED_MATERIAL=18]=\"DRAPED_MATERIAL\",E[E.DRAPED_WATER=19]=\"DRAPED_WATER\",E[E.VOXEL=20]=\"VOXEL\",E[E.MAX_SLOTS=21]=\"MAX_SLOTS\"}(E||(E={}));export{E as RenderSlot};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../../core/maybe.js\";import{n as s}from\"../../../../chunks/mat3.js\";import{c as e}from\"../../../../chunks/mat3f64.js\";import{a as i,t as r,c as a,m as o}from\"../../../../chunks/mat4.js\";import{c as h}from\"../../../../chunks/mat4f64.js\";import{c as n}from\"../../../../chunks/quat.js\";import{a as f}from\"../../../../chunks/quatf64.js\";import{q as l,a as m,b as _}from\"../../../../chunks/vec3.js\";import{c as b}from\"../../../../chunks/vec3f32.js\";import{c}from\"../../../../chunks/vec3f64.js\";import{c as p}from\"../../../../chunks/sphere.js\";class u{constructor(){this._transform=h(),this._transformInverse=new M({value:this._transform},i,h),this._transformInverseTranspose=new M(this._transformInverse,r,h),this._transformTranspose=new M({value:this._transform},r,h),this._transformInverseRotation=new M({value:this._transform},s,e)}_invalidateLazyTransforms(){this._transformInverse.invalidate(),this._transformInverseTranspose.invalidate(),this._transformTranspose.invalidate(),this._transformInverseRotation.invalidate()}get transform(){return this._transform}get inverse(){return this._transformInverse.value}get inverseTranspose(){return this._transformInverseTranspose.value}get inverseRotation(){return this._transformInverseRotation.value}get transpose(){return this._transformTranspose.value}setTransformMatrix(t){a(this._transform,t)}multiplyTransform(t){o(this._transform,this._transform,t)}set(t){a(this._transform,t),this._invalidateLazyTransforms()}setAndInvalidateLazyTransforms(t,s){this.setTransformMatrix(t),this.multiplyTransform(s),this._invalidateLazyTransforms()}}class M{constructor(t,s,e){this._original=t,this._update=s,this._dirty=!0,this._transform=e()}invalidate(){this._dirty=!0}get value(){return this._dirty&&(this._update(this._transform,this._original.value),this._dirty=!1),this._transform}}class v{constructor(t=0){this.offset=t,this.tmpVertex=c()}applyToVertex(t,s,e){const i=t+this.localOrigin[0],r=s+this.localOrigin[1],a=e+this.localOrigin[2],o=this.offset/Math.sqrt(i*i+r*r+a*a);return this.tmpVertex[0]=t+i*o,this.tmpVertex[1]=s+r*o,this.tmpVertex[2]=e+a*o,this.tmpVertex}applyToAabb(t){for(let r=0;r<3;++r)O[r]=t[0+r]+this.localOrigin[r],z[r]=t[3+r]+this.localOrigin[r],T[r]=O[r];const s=this.applyToVertex(O[0],O[1],O[2]);for(let r=0;r<3;++r)t[r]=s[r],t[r+3]=s[r];const e=s=>{const e=this.applyToVertex(s[0],s[1],s[2]);for(let i=0;i<3;++i)t[i+0]=Math.min(t[i+0],e[i]),t[i+3]=Math.max(t[i+3],e[i])};for(let r=1;r<8;++r){for(let t=0;t<3;++t)T[t]=0==(r&1<<t)?O[t]:z[t];e(T)}let i=0;for(let r=0;r<3;++r){O[r]*z[r]<0&&(i|=1<<r)}if(0!==i&&7!==i)for(let r=0;r<8;++r)if(0==(i&r)){for(let t=0;t<3;++t)i[t]?T[t]=0:T[t]=0!=(r&1<<t)?O[t]:z[t];e(T)}for(let r=0;r<3;++r)t[r+0]-=this.localOrigin[r],t[r+3]-=this.localOrigin[r];return t}}const O=c(),z=c(),T=c();class g{constructor(t=0){this.componentLocalOriginLength=0,this._tmpVertex=c(),this._mbs=p(),this._obb={center:c(),halfSize:b(),quaternion:null},this._totalOffset=0,this._offset=0,this._resetOffset(t)}_resetOffset(t){this._offset=t,this._totalOffset=t}set offset(t){this._resetOffset(t)}get offset(){return this._offset}set componentOffset(t){this._totalOffset=this._offset+t}set localOrigin(t){this.componentLocalOriginLength=Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2])}applyToVertex(t,s,e){const i=t,r=s,a=e+this.componentLocalOriginLength,o=this._totalOffset/Math.sqrt(i*i+r*r+a*a);return this._tmpVertex[0]=t+i*o,this._tmpVertex[1]=s+r*o,this._tmpVertex[2]=e+a*o,this._tmpVertex}applyToAabb(t){const s=t[0],e=t[1],i=t[2]+this.componentLocalOriginLength,r=t[3],a=t[4],o=t[5]+this.componentLocalOriginLength,h=s*r<0?0:Math.min(Math.abs(s),Math.abs(r)),n=e*a<0?0:Math.min(Math.abs(e),Math.abs(a)),f=i*o<0?0:Math.min(Math.abs(i),Math.abs(o)),l=Math.sqrt(h*h+n*n+f*f);if(l<this._totalOffset)return t[0]-=s<0?this._totalOffset:0,t[1]-=e<0?this._totalOffset:0,t[2]-=i<0?this._totalOffset:0,t[3]+=r>0?this._totalOffset:0,t[4]+=a>0?this._totalOffset:0,t[5]+=o>0?this._totalOffset:0,t;const m=Math.max(Math.abs(s),Math.abs(r)),_=Math.max(Math.abs(e),Math.abs(a)),b=Math.max(Math.abs(i),Math.abs(o)),c=Math.sqrt(m*m+_*_+b*b),p=this._totalOffset/c,u=this._totalOffset/l;return t[0]+=s*(s>0?p:u),t[1]+=e*(e>0?p:u),t[2]+=i*(i>0?p:u),t[3]+=r*(r<0?p:u),t[4]+=a*(a<0?p:u),t[5]+=o*(o<0?p:u),t}applyToMbs(t){const s=Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]),e=this._totalOffset/s;return this._mbs[0]=t[0]+t[0]*e,this._mbs[1]=t[1]+t[1]*e,this._mbs[2]=t[2]+t[2]*e,this._mbs[3]=t[3]+t[3]*this._totalOffset/s,this._mbs}applyToObb(t){const s=t.center,e=this._totalOffset/Math.sqrt(s[0]*s[0]+s[1]*s[1]+s[2]*s[2]);this._obb.center[0]=s[0]+s[0]*e,this._obb.center[1]=s[1]+s[1]*e,this._obb.center[2]=s[2]+s[2]*e,l(this._obb.halfSize,t.halfSize,t.quaternion),m(this._obb.halfSize,this._obb.halfSize,t.center);const i=this._totalOffset/Math.sqrt(this._obb.halfSize[0]*this._obb.halfSize[0]+this._obb.halfSize[1]*this._obb.halfSize[1]+this._obb.halfSize[2]*this._obb.halfSize[2]);return this._obb.halfSize[0]+=this._obb.halfSize[0]*i,this._obb.halfSize[1]+=this._obb.halfSize[1]*i,this._obb.halfSize[2]+=this._obb.halfSize[2]*i,_(this._obb.halfSize,this._obb.halfSize,t.center),n(I,t.quaternion),l(this._obb.halfSize,this._obb.halfSize,I),this._obb.halfSize[0]*=this._obb.halfSize[0]<0?-1:1,this._obb.halfSize[1]*=this._obb.halfSize[1]<0?-1:1,this._obb.halfSize[2]*=this._obb.halfSize[2]<0?-1:1,this._obb.quaternion=t.quaternion,this._obb}}class x{constructor(t=0){this.offset=t,this.sphere=p(),this.tmpVertex=c()}applyToVertex(t,s,e){const i=this.objectTransform.transform;let r=i[0]*t+i[4]*s+i[8]*e+i[12],a=i[1]*t+i[5]*s+i[9]*e+i[13],o=i[2]*t+i[6]*s+i[10]*e+i[14];const h=this.offset/Math.sqrt(r*r+a*a+o*o);r+=r*h,a+=a*h,o+=o*h;const n=this.objectTransform.inverse;return this.tmpVertex[0]=n[0]*r+n[4]*a+n[8]*o+n[12],this.tmpVertex[1]=n[1]*r+n[5]*a+n[9]*o+n[13],this.tmpVertex[2]=n[2]*r+n[6]*a+n[10]*o+n[14],this.tmpVertex}applyToMinMax(t,s){const e=this.offset/Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]+=t[0]*e,t[1]+=t[1]*e,t[2]+=t[2]*e;const i=this.offset/Math.sqrt(s[0]*s[0]+s[1]*s[1]+s[2]*s[2]);s[0]+=s[0]*i,s[1]+=s[1]*i,s[2]+=s[2]*i}applyToAabb(t){const s=this.offset/Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]+=t[0]*s,t[1]+=t[1]*s,t[2]+=t[2]*s;const e=this.offset/Math.sqrt(t[3]*t[3]+t[4]*t[4]+t[5]*t[5]);return t[3]+=t[3]*e,t[4]+=t[4]*e,t[5]+=t[5]*e,t}applyToBoundingSphere(t){const s=Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]),e=this.offset/s;return this.sphere[0]=t[0]+t[0]*e,this.sphere[1]=t[1]+t[1]*e,this.sphere[2]=t[2]+t[2]*e,this.sphere[3]=t[3]+t[3]*this.offset/s,this.sphere}}const S=new x;function y(s){return t(s)?(S.offset=s,S):null}const q=new g;function V(s){return t(s)?(q.offset=s,q):null}const d=new v;function L(s){return t(s)?(d.offset=s,d):null}const j=\"terrain\",I=f();export{g as I3SVerticalOffsetGlobalViewingMode,u as IntersectorTransform,x as Object3DVerticalOffsetGlobalViewingMode,j as TERRAIN_ID,v as TerrainVerticalOffsetGlobalViewingMode,V as getVerticalOffsetI3S,y as getVerticalOffsetObject3D,L as getVerticalOffsetTerrain};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../../core/maybe.js\";import{y as t,z as f}from\"../../../../../chunks/mat4.js\";import{BufferViewVec4f as o,BufferViewVec4u8 as r,BufferViewVec2f as n,BufferViewVec3f as s}from\"../../../../../geometry/support/buffer/BufferView.js\";import{assert as i}from\"../../lib/Util.js\";import{VertexAttribute as c}from\"../../lib/VertexAttribute.js\";function l(e,t,f,o,r=1){const n=f.typedBuffer,s=f.typedBufferStride,i=e.length;if(o*=s,1===r)for(let c=0;c<i;++c)n[o]=t[e[c]],o+=s;else for(let c=0;c<i;++c){const f=t[e[c]];for(let e=0;e<r;e++)n[o]=f,o+=s}}function d(e,t,f,o){const r=f.typedBuffer,n=f.typedBufferStride,s=e.length;o*=n;for(let i=0;i<s;++i){const f=2*e[i];r[o]=t[f],r[o+1]=t[f+1],o+=n}}function u(e,t,f,o,r){const n=f.typedBuffer,s=f.typedBufferStride,i=e.length;if(o*=s,null==r||1===r)for(let c=0;c<i;++c){const f=3*e[c];n[o]=t[f],n[o+1]=t[f+1],n[o+2]=t[f+2],o+=s}else for(let c=0;c<i;++c){const f=3*e[c];for(let e=0;e<r;++e)n[o]=t[f],n[o+1]=t[f+1],n[o+2]=t[f+2],o+=s}}function a(e,t,f,o,r=1){const n=f.typedBuffer,s=f.typedBufferStride,i=e.length;if(o*=s,1===r)for(let c=0;c<i;++c){const f=4*e[c];n[o]=t[f],n[o+1]=t[f+1],n[o+2]=t[f+2],n[o+3]=t[f+3],o+=s}else for(let c=0;c<i;++c){const f=4*e[c];for(let e=0;e<r;++e)n[o]=t[f],n[o+1]=t[f+1],n[o+2]=t[f+2],n[o+3]=t[f+3],o+=s}}function p(e,t,f){const o=e.typedBuffer,r=e.typedBufferStride;t*=r;for(let n=0;n<f;++n)o[t]=0,o[t+1]=0,o[t+2]=0,o[t+3]=0,t+=r}function y(e,t,f,o){const r=f.typedBuffer,n=f.typedBufferStride,s=e.length;o*=n;for(let i=0;i<s;++i){const f=9*e[i];for(let e=0;e<9;++e)r[o+e]=t[f+e];o+=n}}function B(e,t,f,o){const r=f.typedBuffer,n=f.typedBufferStride,s=e.length;o*=n;for(let i=0;i<s;++i){const f=16*e[i];for(let e=0;e<16;++e)r[o+e]=t[f+e];o+=n}}function g(e,f,o,r,n,s=1){if(!o)return void u(e,f,r,n,s);const i=r.typedBuffer,c=r.typedBufferStride,l=e.length,d=o[0],a=o[1],p=o[2],y=o[4],B=o[5],g=o[6],b=o[8],h=o[9],O=o[10],N=o[12],S=o[13],m=o[14];n*=c;let A=0,L=0,R=0;const v=t(o)?e=>{A=f[e]+N,L=f[e+1]+S,R=f[e+2]+m}:e=>{const t=f[e],o=f[e+1],r=f[e+2];A=d*t+y*o+b*r+N,L=a*t+B*o+h*r+S,R=p*t+g*o+O*r+m};if(1===s)for(let t=0;t<l;++t)v(3*e[t]),i[n]=A,i[n+1]=L,i[n+2]=R,n+=c;else for(let t=0;t<l;++t){v(3*e[t]);for(let e=0;e<s;++e)i[n]=A,i[n+1]=L,i[n+2]=R,n+=c}}function b(e,o,r,n,s,i=1){if(!r)return void u(e,o,n,s,i);const c=r,l=n.typedBuffer,d=n.typedBufferStride,a=e.length,p=c[0],y=c[1],B=c[2],g=c[4],b=c[5],h=c[6],O=c[8],N=c[9],S=c[10],m=!f(c),A=1e-6,L=1-A;s*=d;let R=0,v=0,E=0;const F=t(c)?e=>{R=o[e],v=o[e+1],E=o[e+2]}:e=>{const t=o[e],f=o[e+1],r=o[e+2];R=p*t+g*f+O*r,v=y*t+b*f+N*r,E=B*t+h*f+S*r};if(1===i)if(m)for(let t=0;t<a;++t){F(3*e[t]);const f=R*R+v*v+E*E;if(f<L&&f>A){const e=1/Math.sqrt(f);l[s]=R*e,l[s+1]=v*e,l[s+2]=E*e}else l[s]=R,l[s+1]=v,l[s+2]=E;s+=d}else for(let t=0;t<a;++t)F(3*e[t]),l[s]=R,l[s+1]=v,l[s+2]=E,s+=d;else for(let t=0;t<a;++t){if(F(3*e[t]),m){const e=R*R+v*v+E*E;if(e<L&&e>A){const t=1/Math.sqrt(e);R*=t,v*=t,E*=t}}for(let e=0;e<i;++e)l[s]=R,l[s+1]=v,l[s+2]=E,s+=d}}function h(e,t,o,r,n,s=1){if(!o)return void a(e,t,r,n,s);const i=o,c=r.typedBuffer,l=r.typedBufferStride,d=e.length,u=i[0],p=i[1],y=i[2],B=i[4],g=i[5],b=i[6],h=i[8],O=i[9],N=i[10],S=!f(i),m=1e-6,A=1-m;if(n*=l,1===s)for(let f=0;f<d;++f){const o=4*e[f],r=t[o],s=t[o+1],i=t[o+2],d=t[o+3];let a=u*r+B*s+h*i,L=p*r+g*s+O*i,R=y*r+b*s+N*i;if(S){const e=a*a+L*L+R*R;if(e<A&&e>m){const t=1/Math.sqrt(e);a*=t,L*=t,R*=t}}c[n]=a,c[n+1]=L,c[n+2]=R,c[n+3]=d,n+=l}else for(let f=0;f<d;++f){const o=4*e[f],r=t[o],i=t[o+1],d=t[o+2],a=t[o+3];let L=u*r+B*i+h*d,R=p*r+g*i+O*d,v=y*r+b*i+N*d;if(S){const e=L*L+R*R+v*v;if(e<A&&e>m){const t=1/Math.sqrt(e);L*=t,R*=t,v*=t}}for(let e=0;e<s;++e)c[n]=L,c[n+1]=R,c[n+2]=v,c[n+3]=a,n+=l}}function O(e,t,f,o,r,n=1){const s=o.typedBuffer,i=o.typedBufferStride,c=e.length;if(r*=i,f!==t.length||4!==f)if(1!==n)if(4!==f)for(let l=0;l<c;++l){const f=3*e[l];for(let e=0;e<n;++e)s[r]=t[f],s[r+1]=t[f+1],s[r+2]=t[f+2],s[r+3]=255,r+=i}else for(let l=0;l<c;++l){const f=4*e[l];for(let e=0;e<n;++e)s[r]=t[f],s[r+1]=t[f+1],s[r+2]=t[f+2],s[r+3]=t[f+3],r+=i}else{if(4===f){for(let f=0;f<c;++f){const o=4*e[f];s[r]=t[o],s[r+1]=t[o+1],s[r+2]=t[o+2],s[r+3]=t[o+3],r+=i}return}for(let f=0;f<c;++f){const o=3*e[f];s[r]=t[o],s[r+1]=t[o+1],s[r+2]=t[o+2],s[r+3]=255,r+=i}}else{s[r]=t[0],s[r+1]=t[1],s[r+2]=t[2],s[r+3]=t[3];const e=new Uint32Array(o.typedBuffer.buffer,o.start),f=i/4,l=e[r/=4];r+=f;const d=c*n;for(let t=1;t<d;++t)e[r]=l,r+=f}}function N(e,t,f,o,r=1){const n=t.typedBuffer,s=t.typedBufferStride;if(o*=s,1===r)for(let i=0;i<f;++i)n[o]=e[0],n[o+1]=e[1],n[o+2]=e[2],n[o+3]=e[3],o+=s;else for(let i=0;i<f;++i)for(let t=0;t<r;++t)n[o]=e[0],n[o+1]=e[1],n[o+2]=e[2],n[o+3]=e[3],o+=s}function S(t,f,l,u,p,y){for(const B of f.fieldNames){const f=t.vertexAttributes.get(B),S=t.indices.get(B);if(f&&S)switch(B){case c.POSITION:{i(3===f.size);const e=p.getField(B,s);i(!!e,`No buffer view for ${B}`),e&&g(S,f.data,l,e,y);break}case c.NORMAL:{i(3===f.size);const e=p.getField(B,s);i(!!e,`No buffer view for ${B}`),e&&b(S,f.data,u,e,y);break}case c.UV0:{i(2===f.size);const e=p.getField(B,n);i(!!e,`No buffer view for ${B}`),e&&d(S,f.data,e,y);break}case c.COLOR:case c.SYMBOLCOLOR:{i(3===f.size||4===f.size);const e=p.getField(B,r);i(!!e,`No buffer view for ${B}`),e&&O(S,f.data,f.size,e,y);break}case c.TANGENT:{i(4===f.size);const e=p.getField(B,o);i(!!e,`No buffer view for ${B}`),e&&h(S,f.data,u,e,y);break}case c.PROFILERIGHT:case c.PROFILEUP:case c.PROFILEVERTEXANDNORMAL:case c.FEATUREVALUE:{i(4===f.size);const e=p.getField(B,o);i(!!e,`No buffer view for ${B}`),e&&a(S,f.data,e,y)}}else if(B===c.OBJECTANDLAYERIDCOLOR&&e(t.objectAndLayerIdColor)){const e=t.indices.get(c.POSITION);if(i(!!e,`No buffer view for ${B}`),e){const f=e.length,o=p.getField(B,r);N(t.objectAndLayerIdColor,o,f,y)}}}}export{l as writeBufferFloat,y as writeBufferMat3f,B as writeBufferMat4f,d as writeBufferVec2,u as writeBufferVec3,a as writeBufferVec4,p as writeBufferVec4Zeros,O as writeColor,S as writeDefaultAttributes,b as writeNormal,N as writeObjectAndLayerIdColor,g as writePosition,h as writeTangent};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{VertexAttribute as t}from\"../lib/VertexAttribute.js\";import{writeDefaultAttributes as e}from\"./internal/bufferWriterUtils.js\";class r{constructor(t){this.vertexBufferLayout=t}allocate(t){return this.vertexBufferLayout.createBuffer(t)}elementCount(e){return e.indices.get(t.POSITION).length}write(t,r,i,u,f){e(i,this.vertexBufferLayout,t,r,u,f)}}export{r as DefaultBufferWriter};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{StencilBits as a}from\"./basicInterfaces.js\";import{CompareFunction as E,StencilOperation as n}from\"../../../webgl/enums.js\";const i={func:E.LESS},s={func:E.ALWAYS},e={mask:255},l={mask:0},t=a=>({function:{func:E.NOTEQUAL,ref:a,mask:a},operation:{fail:n.KEEP,zFail:n.KEEP,zPass:n.KEEP}}),u=a=>({function:{func:E.ALWAYS,ref:a,mask:a},operation:{fail:n.KEEP,zFail:n.KEEP,zPass:n.REPLACE}}),f={function:{func:E.ALWAYS,ref:a.OutlineVisualElementMask,mask:a.OutlineVisualElementMask},operation:{fail:n.KEEP,zFail:n.KEEP,zPass:n.ZERO}},o={function:{func:E.ALWAYS,ref:a.OutlineVisualElementMask,mask:a.OutlineVisualElementMask},operation:{fail:n.KEEP,zFail:n.KEEP,zPass:n.REPLACE}},P={function:{func:E.EQUAL,ref:a.OutlineVisualElementMask,mask:a.OutlineVisualElementMask},operation:{fail:n.KEEP,zFail:n.KEEP,zPass:n.KEEP}},m={function:{func:E.NOTEQUAL,ref:a.OutlineVisualElementMask,mask:a.OutlineVisualElementMask},operation:{fail:n.KEEP,zFail:n.KEEP,zPass:n.KEEP}};export{s as depthCompareAlways,i as depthCompareLess,t as renderWhenBitIsNotSet,u as replaceBitWhenDepthTestPasses,f as stencilBaseAllZerosParams,o as stencilToolMaskBaseParams,P as stencilToolMaskOccluderParams,m as stencilToolTransparentOccluderParams,l as stencilWriteMaskOff,e as stencilWriteMaskOn};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2E,SAASA,GAAEA,IAAE;AAAC,MAAGA,GAAE,SAAO,EAAE,QAAO,MAAM,KAAKA,EAAC;AAAE,MAAG,MAAM,QAAQA,EAAC,EAAE,QAAO,aAAa,KAAKA,EAAC;AAAE,UAAOA,GAAE,mBAAkB;AAAA,IAAC,KAAK;AAAE,aAAO,WAAW,KAAKA,EAAC;AAAA,IAAE,KAAK;AAAE,aAAO,YAAY,KAAKA,EAAC;AAAA,IAAE,KAAK;AAAE,aAAO,aAAa,KAAKA,EAAC;AAAA,IAAE;AAAQ,aAAO,aAAa,KAAKA,EAAC;AAAA,EAAC;AAAC;;;ACA1I,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEF,IAAEG,IAAE;AAAC,SAAK,mBAAiBF,IAAE,KAAK,wBAAsBC,IAAE,KAAK,UAAQF,IAAE,KAAK,WAASG,IAAE,KAAK,YAAU,QAAO,EAAEF,GAAE,UAAQ,CAAC,GAAE,EAAED,GAAE,SAAO,KAAK,yBAAuB,CAAC,GAAE,EAAEA,GAAE,UAAQC,GAAE,SAAO,KAAK,qBAAqB,GAAE,EAAE,MAAIE,GAAE,QAAM,MAAIA,GAAE,IAAI;AAAE,UAAK,EAAC,MAAKC,IAAE,MAAKC,GAAC,IAAEF,IAAEG,KAAEL,GAAE;AAAO,QAAIM,KAAEF,KAAEL,GAAE,KAAK,wBAAsBC,GAAE,CAAC,CAAC;AAAE,IAAAO,GAAE,MAAM,GAAEA,GAAE,KAAKD,EAAC;AAAE,UAAME,KAAEC,GAAEN,GAAEG,EAAC,GAAEH,GAAEG,KAAE,CAAC,GAAEH,GAAEG,KAAE,CAAC,CAAC,GAAEI,KAAEC,GAAEH,EAAC;AAAE,aAAQG,KAAE,GAAEA,KAAEN,IAAE,EAAEM,IAAE;AAAC,YAAMV,KAAE,KAAK,wBAAsBD,GAAEW,EAAC;AAAE,eAAQX,KAAE,GAAEA,KAAE,KAAK,uBAAsB,EAAEA,IAAE;AAAC,QAAAM,KAAEF,KAAEL,GAAEE,KAAED,EAAC,GAAEO,GAAE,KAAKD,EAAC;AAAE,YAAIK,KAAER,GAAEG,EAAC;AAAE,QAAAE,GAAE,CAAC,IAAE,KAAK,IAAIG,IAAEH,GAAE,CAAC,CAAC,GAAEE,GAAE,CAAC,IAAE,KAAK,IAAIC,IAAED,GAAE,CAAC,CAAC,GAAEC,KAAER,GAAEG,KAAE,CAAC,GAAEE,GAAE,CAAC,IAAE,KAAK,IAAIG,IAAEH,GAAE,CAAC,CAAC,GAAEE,GAAE,CAAC,IAAE,KAAK,IAAIC,IAAED,GAAE,CAAC,CAAC,GAAEC,KAAER,GAAEG,KAAE,CAAC,GAAEE,GAAE,CAAC,IAAE,KAAK,IAAIG,IAAEH,GAAE,CAAC,CAAC,GAAEE,GAAE,CAAC,IAAE,KAAK,IAAIC,IAAED,GAAE,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,SAAK,QAAMF,IAAE,KAAK,QAAME;AAAE,UAAME,KAAE,EAAE,EAAE,GAAE,KAAK,OAAM,KAAK,OAAM,GAAE;AAAE,SAAK,SAAO,MAAG,KAAK,IAAI,KAAK,IAAIF,GAAE,CAAC,IAAEF,GAAE,CAAC,GAAEE,GAAE,CAAC,IAAEF,GAAE,CAAC,CAAC,GAAEE,GAAE,CAAC,IAAEF,GAAE,CAAC,CAAC;AAAE,QAAIK,KAAE,KAAK,SAAO,KAAK;AAAO,aAAQF,KAAE,GAAEA,KAAEJ,GAAE,QAAO,EAAEI,IAAE;AAAC,MAAAL,KAAEC,GAAE,UAAUI,EAAC;AAAE,YAAMX,KAAEG,GAAEG,EAAC,IAAEM,GAAE,CAAC,GAAEX,KAAEE,GAAEG,KAAE,CAAC,IAAEM,GAAE,CAAC,GAAEE,KAAEX,GAAEG,KAAE,CAAC,IAAEM,GAAE,CAAC,GAAEG,KAAEf,KAAEA,KAAEC,KAAEA,KAAEa,KAAEA;AAAE,UAAGC,MAAGF,GAAE;AAAS,YAAMJ,KAAE,KAAK,KAAKM,EAAC,GAAEC,KAAE,OAAIP,KAAE,KAAK;AAAQ,WAAK,SAAO,KAAK,SAAOO,IAAEH,KAAE,KAAK,SAAO,KAAK;AAAO,YAAMd,KAAEiB,KAAEP;AAAE,MAAAG,GAAE,CAAC,KAAGZ,KAAED,IAAEa,GAAE,CAAC,KAAGX,KAAEF,IAAEa,GAAE,CAAC,KAAGE,KAAEf;AAAA,IAAC;AAAC,SAAK,SAAOa,IAAEL,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,QAAG,KAAK,aAAW,EAAE,KAAK,OAAM,KAAK,KAAK,KAAG,EAAE,QAAO,KAAK;AAAU,UAAMP,KAAE,EAAE,EAAE,GAAE,KAAK,OAAM,KAAK,OAAM,GAAE,GAAEc,KAAE,KAAK,iBAAiB,QAAOC,KAAE,IAAI,WAAWD,EAAC,GAAEE,KAAE,IAAI,MAAM,CAAC;AAAE,aAAQL,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAK,GAAEL,EAAC,IAAE;AAAE,UAAK,EAAC,MAAKJ,IAAE,MAAKL,GAAC,IAAE,KAAK;AAAS,aAAQS,KAAE,GAAEA,KAAEG,IAAE,EAAEH,IAAE;AAAC,UAAIV,KAAE;AAAE,YAAMa,KAAE,KAAK,wBAAsB,KAAK,iBAAiBH,EAAC;AAAE,UAAIF,KAAEP,KAAE,KAAK,QAAQY,EAAC,GAAEf,KAAEQ,GAAEE,EAAC,GAAEN,KAAEI,GAAEE,KAAE,CAAC,GAAEL,KAAEG,GAAEE,KAAE,CAAC;AAAE,eAAQT,KAAE,GAAEA,KAAE,KAAK,uBAAsB,EAAEA,IAAE;AAAC,QAAAS,KAAEP,KAAE,KAAK,QAAQY,KAAEd,EAAC;AAAE,cAAMW,KAAEJ,GAAEE,EAAC,GAAER,KAAEM,GAAEE,KAAE,CAAC,GAAEM,KAAER,GAAEE,KAAE,CAAC;AAAE,QAAAE,KAAEZ,OAAIA,KAAEY,KAAGV,KAAEE,OAAIA,KAAEF,KAAGc,KAAEX,OAAIA,KAAEW;AAAA,MAAE;AAAC,MAAAhB,KAAEC,GAAE,CAAC,MAAIC,MAAG,IAAGE,KAAEH,GAAE,CAAC,MAAIC,MAAG,IAAGG,KAAEJ,GAAE,CAAC,MAAIC,MAAG,IAAGc,GAAEJ,EAAC,IAAEV,IAAE,EAAEe,GAAEf,EAAC;AAAA,IAAC;AAAC,QAAIE,KAAE;AAAE,aAAQQ,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAK,GAAEL,EAAC,IAAE,KAAG,EAAER;AAAE,QAAGA,KAAE,EAAE;AAAO,UAAMC,KAAE,IAAI,MAAM,CAAC;AAAE,aAAQO,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAP,GAAEO,EAAC,IAAEK,GAAEL,EAAC,IAAE,IAAE,IAAI,YAAYK,GAAEL,EAAC,CAAC,IAAE;AAAO,aAAQA,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAK,GAAEL,EAAC,IAAE;AAAE,aAAQA,KAAE,GAAEA,KAAEG,IAAE,EAAEH,IAAE;AAAC,YAAMX,KAAEe,GAAEJ,EAAC;AAAE,MAAAP,GAAEJ,EAAC,EAAEgB,GAAEhB,EAAC,GAAG,IAAE,KAAK,iBAAiBW,EAAC;AAAA,IAAC;AAAC,SAAK,YAAU,IAAI;AAAM,aAAQA,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,YAASP,GAAEO,EAAC,KAAG,KAAK,UAAU,KAAK,IAAI,GAAEP,GAAEO,EAAC,GAAE,KAAK,uBAAsB,KAAK,SAAQ,KAAK,QAAQ,CAAC;AAAE,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,OAAO,QAAO;AAAC,IAAAJ,GAAE,MAAM;AAAA,EAAC;AAAC;AAAC,IAAMA,KAAE,IAAIH,GAAE,EAAC,aAAY,KAAI,CAAC;;;ACA1oE,SAASa,GAAEC,IAAEC,IAAEF,IAAE;AAAC,MAAG,CAACC,MAAG,CAACC,GAAE,QAAM;AAAG,QAAK,EAAC,MAAKC,IAAE,MAAKC,GAAC,IAAEH;AAAE,IAAED,IAAE,GAAE,GAAE,CAAC,GAAE,EAAEK,IAAE,GAAE,GAAE,CAAC;AAAE,MAAIC,KAAE,GAAEC,KAAE;AAAE,WAAQC,KAAE,GAAEA,KAAEN,GAAE,SAAO,GAAEM,MAAG,GAAE;AAAC,UAAMP,KAAEC,GAAEM,KAAE,CAAC,IAAEL,IAAE,IAAED,GAAEM,KAAE,CAAC,IAAEL,IAAEM,KAAEP,GAAEM,KAAE,CAAC,IAAEL;AAAE,MAAEO,IAAEN,GAAEH,KAAE,CAAC,GAAEG,GAAEH,KAAE,CAAC,GAAEG,GAAEH,KAAE,CAAC,CAAC,GAAE,EAAEU,IAAEP,GAAE,IAAE,CAAC,GAAEA,GAAE,IAAE,CAAC,GAAEA,GAAE,IAAE,CAAC,CAAC,GAAE,EAAEQ,IAAER,GAAEK,KAAE,CAAC,GAAEL,GAAEK,KAAE,CAAC,GAAEL,GAAEK,KAAE,CAAC,CAAC;AAAE,UAAMI,KAAE,EAAEH,IAAEC,IAAEC,EAAC;AAAE,IAAAC,MAAG,EAAEH,IAAEA,IAAEC,EAAC,GAAE,EAAED,IAAEA,IAAEE,EAAC,GAAE,EAAEF,IAAEA,IAAE,IAAE,IAAEG,EAAC,GAAE,EAAEb,IAAEA,IAAEU,EAAC,GAAEJ,MAAGO,OAAI,EAAER,IAAEA,IAAEK,EAAC,GAAE,EAAEL,IAAEA,IAAEM,EAAC,GAAE,EAAEN,IAAEA,IAAEO,EAAC,GAAEL,MAAG;AAAA,EAAE;AAAC,UAAO,MAAIA,MAAG,MAAID,QAAK,MAAIA,MAAG,EAAEN,IAAEA,IAAE,IAAEM,EAAC,GAAE,QAAI,MAAIC,OAAI,EAAEP,IAAEK,IAAE,IAAEE,EAAC,GAAE;AAAI;AAAC,SAASJ,GAAEW,IAAEb,IAAEC,IAAE;AAAC,MAAG,CAACY,MAAG,CAACb,GAAE,QAAM;AAAG,QAAK,EAAC,MAAKc,IAAE,MAAKf,GAAC,IAAEc;AAAE,IAAEZ,IAAE,GAAE,GAAE,CAAC;AAAE,MAAIC,KAAE,IAAGC,KAAE;AAAE,WAAQY,KAAE,GAAEA,KAAEf,GAAE,QAAOe,MAAI;AAAC,UAAMF,KAAEb,GAAEe,EAAC,IAAED;AAAE,IAAAZ,OAAIW,OAAIZ,GAAE,CAAC,KAAGF,GAAEc,KAAE,CAAC,GAAEZ,GAAE,CAAC,KAAGF,GAAEc,KAAE,CAAC,GAAEZ,GAAE,CAAC,KAAGF,GAAEc,KAAE,CAAC,GAAEV,OAAKD,KAAEW;AAAA,EAAC;AAAC,SAAOV,KAAE,KAAG,EAAEF,IAAEA,IAAE,IAAEE,EAAC,GAAEA,KAAE;AAAC;AAAC,SAAS,EAAEF,IAAEa,IAAEf,IAAEG,IAAE;AAAC,MAAG,CAACD,GAAE,QAAM;AAAG,IAAEC,IAAE,GAAE,GAAE,CAAC,GAAE,EAAEE,IAAE,GAAE,GAAE,CAAC;AAAE,MAAID,KAAE,GAAEQ,KAAE;AAAE,QAAK,EAAC,MAAKN,IAAE,MAAKC,GAAC,IAAEL,IAAEM,KAAEO,KAAEA,GAAE,SAAO,IAAER,GAAE,SAAOD,KAAE,GAAE,IAAEE,MAAGR,KAAE,IAAE;AAAG,WAAQgB,KAAE,GAAEA,KAAE,GAAEA,MAAG,GAAE;AAAC,UAAMd,KAAEc,KAAER,KAAEQ,KAAER,IAAER,KAAEgB,KAAER,KAAEQ,KAAE,IAAE,GAAEC,MAAGF,KAAEA,GAAEb,EAAC,IAAEA,MAAGI,IAAEG,MAAGM,KAAEA,GAAEf,EAAC,IAAEA,MAAGM;AAAE,IAAAI,GAAE,CAAC,IAAEH,GAAEU,EAAC,GAAEP,GAAE,CAAC,IAAEH,GAAEU,KAAE,CAAC,GAAEP,GAAE,CAAC,IAAEH,GAAEU,KAAE,CAAC,GAAEN,GAAE,CAAC,IAAEJ,GAAEE,EAAC,GAAEE,GAAE,CAAC,IAAEJ,GAAEE,KAAE,CAAC,GAAEE,GAAE,CAAC,IAAEJ,GAAEE,KAAE,CAAC,GAAE,EAAEC,IAAE,EAAEA,IAAEA,IAAEC,EAAC,GAAE,GAAE;AAAE,UAAME,KAAE,EAAEH,IAAEC,EAAC;AAAE,IAAAE,KAAE,KAAG,EAAEV,IAAEA,IAAE,EAAEO,IAAEA,IAAEG,EAAC,CAAC,GAAET,MAAGS,MAAG,MAAIT,OAAI,EAAEC,IAAEA,IAAEK,EAAC,GAAEE;AAAA,EAAI;AAAC,SAAO,MAAIR,MAAG,EAAED,IAAEA,IAAE,IAAEC,EAAC,GAAE,QAAI,MAAIQ,OAAI,EAAET,IAAEE,IAAE,IAAEO,EAAC,GAAE;AAAG;AAAC,IAAMF,KAAE,EAAE;AAAV,IAAYC,KAAE,EAAE;AAAhB,IAAkBC,KAAE,EAAE;AAAtB,IAAwBP,KAAE,EAAE;;;ACA9qC,IAAMa,KAAN,MAAO;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,UAAQA,IAAE,KAAK,KAAG,EAAE;AAAA,EAAC;AAAC;;;ACAiF,SAASC,GAAEC,IAAEC,IAAE;AAAC,SAAO,EAAED,EAAC,MAAIA,KAAE,CAAC,IAAGA,GAAE,KAAKC,EAAC,GAAED;AAAC;AAAC,SAASE,GAAEF,IAAEC,IAAE;AAAC,MAAG,EAAED,EAAC,EAAE,QAAO;AAAK,QAAMD,KAAEC,GAAE,OAAQ,CAAAG,OAAGA,OAAIF,EAAE;AAAE,SAAO,MAAIF,GAAE,SAAO,OAAKA;AAAC;AAAC,SAASK,GAAED,IAAEH,IAAED,IAAEG,IAAEE,IAAE;AAAC,EAAAC,GAAE,CAAC,IAAEF,GAAE,IAAIH,IAAE,CAAC,GAAEK,GAAE,CAAC,IAAEF,GAAE,IAAIH,IAAE,CAAC,GAAEK,GAAE,CAAC,IAAEF,GAAE,IAAIH,IAAE,CAAC,GAAEG,GAAEE,IAAEC,IAAE,CAAC,GAAEP,GAAE,IAAIK,IAAE,GAAEE,GAAE,CAAC,CAAC,GAAEJ,GAAE,IAAIE,IAAE,GAAEE,GAAE,CAAC,CAAC,GAAEP,GAAE,IAAIK,IAAE,GAAEE,GAAE,CAAC,CAAC,GAAEJ,GAAE,IAAIE,IAAE,GAAEE,GAAE,CAAC,CAAC,GAAEP,GAAE,IAAIK,IAAE,GAAEE,GAAE,CAAC,CAAC,GAAEJ,GAAE,IAAIE,IAAE,GAAEE,GAAE,CAAC,CAAC;AAAC;AAAC,IAAMD,KAAE,EAAE;AAAV,IAAYC,KAAE,IAAI,aAAa,CAAC;;;ACA2X,IAAM,IAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEC,KAAE,CAAC,GAAEC,KAAE,MAAKC,KAAEH,GAAE,MAAKF,KAAE,MAAKM,KAAE,IAAG;AAAC,UAAM,GAAE,KAAK,WAASL,IAAE,KAAK,eAAaG,IAAE,KAAK,OAAKC,IAAE,KAAK,wBAAsBL,IAAE,KAAK,oBAAkBM,IAAE,KAAK,UAAQ,MAAG,KAAK,oBAAkB,oBAAI,OAAI,KAAK,WAAS,oBAAI,OAAI,KAAK,gBAAc;AAAK,eAAS,CAACC,IAAEC,EAAC,KAAIN,GAAE,CAAAM,MAAG,KAAK,kBAAkB,IAAID,IAAE,EAAC,GAAGC,GAAC,CAAC;AAAE,QAAG,QAAML,MAAG,MAAIA,GAAE,QAAO;AAAC,YAAMF,KAAEQ,GAAE,KAAK,iBAAiB,GAAEP,KAAEI,GAAEL,EAAC;AAAE,WAAK,oBAAkB,KAAK,oBAAkB,IAAEA,KAAE,KAAK;AAAkB,iBAAUE,MAAK,KAAK,kBAAkB,KAAK,EAAE,MAAK,SAAS,IAAIA,IAAED,EAAC;AAAA,IAAC,MAAM,YAAS,CAACK,IAAEC,EAAC,KAAIL,GAAE,CAAAK,OAAI,KAAK,SAAS,IAAID,IAAEF,GAAEG,EAAC,CAAC,GAAED,OAAIG,GAAE,aAAW,KAAK,oBAAkB,KAAK,oBAAkB,IAAE,KAAK,SAAS,IAAIH,EAAC,EAAE,SAAO,KAAK;AAAA,EAAmB;AAAA,EAAC,YAAYN,KAAE,CAAC,GAAE;AAAC,UAAMC,KAAE,IAAI,GAAED,GAAE,YAAU,KAAK,UAAS,CAAC,GAAE,QAAO,KAAK,cAAa,KAAK,MAAK,KAAK,uBAAsB,KAAK,iBAAiB;AAAE,WAAO,KAAK,kBAAkB,QAAS,CAACA,IAAEE,OAAI;AAAC,MAAAF,GAAE,YAAU,OAAGC,GAAE,kBAAkB,IAAIC,IAAEF,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,SAAS,QAAS,CAACA,IAAEE,OAAID,GAAE,SAAS,IAAIC,IAAEF,EAAC,CAAE,GAAEC,GAAE,gBAAc,KAAK,eAAcA,GAAE,iBAAeD,GAAE,kBAAgB,KAAK,gBAAeC;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAA,EAAC,oBAAoBD,IAAE;AAAC,QAAIC,KAAE,KAAK,kBAAkB,IAAID,EAAC;AAAE,WAAOC,MAAG,CAACA,GAAE,cAAYA,KAAE,EAAC,GAAGA,IAAE,WAAU,MAAG,MAAKD,GAAEC,GAAE,IAAI,EAAC,GAAE,KAAK,kBAAkB,IAAID,IAAEC,EAAC,IAAGA;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,aAAY;AAAC,UAAMD,KAAE,KAAK,SAAS,OAAO,EAAE,KAAK,EAAE;AAAM,WAAOA,KAAEA,GAAE,SAAO;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,EAAE,KAAK,aAAa,MAAI,KAAK,gBAAc,KAAK,uBAAuB,IAAG,KAAK;AAAA,EAAa;AAAA,EAAC,wBAAwBA,IAAE;AAAC,WAAM,CAAC,EAAE,KAAK,SAAOC,GAAE,OAAK,KAAK,kCAAkCD,EAAC,IAAE,KAAK,SAAOC,GAAE,OAAK,KAAK,8BAA8BD,EAAC,IAAE,KAAK,+BAA+BA,EAAC,OAAK,EAAE,KAAK,eAAe,KAAG,EAAEA,IAAEA,IAAE,KAAK,eAAe,GAAE;AAAA,EAAG;AAAA,EAAC,kCAAkCA,IAAE;AAAC,UAAMC,KAAE,KAAK,QAAQ,IAAIQ,GAAE,QAAQ,GAAEP,KAAE,KAAK,iBAAiB,IAAIO,GAAE,QAAQ;AAAE,WAAOC,GAAER,IAAED,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,8BAA8BA,IAAE;AAAC,UAAMC,KAAE,KAAK,iBAAiB,IAAIQ,GAAE,QAAQ,GAAEP,KAAE,KAAK,QAAQ,IAAIO,GAAE,QAAQ;AAAE,WAAO,EAAER,IAAEC,IAAEA,MAAGS,GAAE,KAAK,SAAS,YAAWV,IAAEC,EAAC,GAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,+BAA+BA,IAAE;AAAC,UAAMC,KAAE,KAAK,QAAQ,IAAIQ,GAAE,QAAQ,GAAEP,KAAE,KAAK,iBAAiB,IAAIO,GAAE,QAAQ;AAAE,WAAOF,GAAEL,IAAED,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,SAAK,gBAAc;AAAA,EAAI;AAAA,EAAC,yBAAwB;AAAC,UAAMA,KAAE,KAAK,QAAQ,IAAIS,GAAE,QAAQ,GAAER,KAAE,KAAK,iBAAiB,IAAIQ,GAAE,QAAQ;AAAE,QAAG,CAACT,MAAG,MAAIA,GAAE,UAAQ,CAACC,GAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,SAAOD,GAAE,OAAK,IAAE;AAAE,MAAED,GAAE,SAAOE,MAAG,GAAE,qBAAmBF,GAAE,SAAO,uBAAqBE,EAAC;AAAE,UAAMC,KAAEE,GAAEL,GAAE,SAAOE,EAAC;AAAE,WAAO,IAAIG,GAAEF,IAAED,IAAEF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,EAAE,KAAK,iBAAgBI,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,eAAeL,IAAE;AAAC,SAAK,kBAAgBA,MAAGA,OAAIK,KAAEN,GAAEC,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,EAAE,KAAK,kBAAkB,IAAE,KAAK,mBAAmB,KAAK,cAAc,IAAE,KAAK;AAAA,EAAc;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAO,KAAK;AAAA,EAAkB;AAAA,EAAC,IAAI,kBAAkBA,IAAE;AAAC,SAAK,qBAAmBA;AAAA,EAAC;AAAA,EAAC,IAAI,4BAA2B;AAAC,WAAO,EAAE,KAAK,kBAAkB;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,UAAMA,KAAE,IAAID,GAAEC,GAAE,SAAS;AAAE,WAAO,KAAK,aAAWI,GAAE,KAAK,YAAWJ,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,SAAK,aAAWK,GAAE,KAAK,YAAWL,EAAC;AAAA,EAAC;AAAC;AAAC,SAASQ,GAAER,IAAE;AAAC,QAAMC,KAAED,GAAE,OAAO,EAAE,KAAK,EAAE;AAAM,SAAO,QAAMC,KAAE,IAAEA,GAAE,KAAK,SAAOA,GAAE;AAAI;AAAC,SAASU,GAAEX,IAAEC,IAAEC,IAAE;AAAC,SAAM,EAAE,EAAE,cAAaF,OAAI,CAACA,GAAE,cAAYE,KAAEA,GAAE,SAAO,IAAED,GAAE,KAAK,SAAO;AAAE;;;ACAhoI,IAAIW;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,kBAAgB,CAAC,IAAE,mBAAkBA,GAAEA,GAAE,iBAAe,CAAC,IAAE,kBAAiBA,GAAEA,GAAE,kBAAgB,CAAC,IAAE,mBAAkBA,GAAEA,GAAE,uBAAqB,CAAC,IAAE,wBAAuBA,GAAEA,GAAE,sBAAoB,CAAC,IAAE,uBAAsBA,GAAEA,GAAE,4CAA0C,CAAC,IAAE,6CAA4CA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,oBAAkB,CAAC,IAAE,qBAAoBA,GAAEA,GAAE,gCAA8B,CAAC,IAAE,iCAAgCA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,oCAAkC,EAAE,IAAE,qCAAoCA,GAAEA,GAAE,yCAAuC,EAAE,IAAE,0CAAyCA,GAAEA,GAAE,aAAW,EAAE,IAAE,cAAaA,GAAEA,GAAE,8BAA4B,EAAE,IAAE,+BAA8BA,GAAEA,GAAE,eAAa,EAAE,IAAE,gBAAeA,GAAEA,GAAE,iBAAe,EAAE,IAAE,kBAAiBA,GAAEA,GAAE,gBAAc,EAAE,IAAE,iBAAgBA,GAAEA,GAAE,0BAAwB,EAAE,IAAE,2BAA0BA,GAAEA,GAAE,kBAAgB,EAAE,IAAE,mBAAkBA,GAAEA,GAAE,eAAa,EAAE,IAAE,gBAAeA,GAAEA,GAAE,QAAM,EAAE,IAAE,SAAQA,GAAEA,GAAE,YAAU,EAAE,IAAE;AAAW,EAAEA,OAAIA,KAAE,CAAC,EAAE;;;ACA7hB,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,aAAWC,GAAE,GAAE,KAAK,oBAAkB,IAAI,EAAE,EAAC,OAAM,KAAK,WAAU,GAAE,GAAEA,EAAC,GAAE,KAAK,6BAA2B,IAAI,EAAE,KAAK,mBAAkBC,IAAED,EAAC,GAAE,KAAK,sBAAoB,IAAI,EAAE,EAAC,OAAM,KAAK,WAAU,GAAEC,IAAED,EAAC,GAAE,KAAK,4BAA0B,IAAI,EAAE,EAAC,OAAM,KAAK,WAAU,GAAEE,IAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,4BAA2B;AAAC,SAAK,kBAAkB,WAAW,GAAE,KAAK,2BAA2B,WAAW,GAAE,KAAK,oBAAoB,WAAW,GAAE,KAAK,0BAA0B,WAAW;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,kBAAkB;AAAA,EAAK;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,2BAA2B;AAAA,EAAK;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK,0BAA0B;AAAA,EAAK;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,oBAAoB;AAAA,EAAK;AAAA,EAAC,mBAAmBG,IAAE;AAAC,IAAAC,GAAE,KAAK,YAAWD,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,MAAE,KAAK,YAAW,KAAK,YAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,IAAAC,GAAE,KAAK,YAAWD,EAAC,GAAE,KAAK,0BAA0B;AAAA,EAAC;AAAA,EAAC,+BAA+BA,IAAEE,IAAE;AAAC,SAAK,mBAAmBF,EAAC,GAAE,KAAK,kBAAkBE,EAAC,GAAE,KAAK,0BAA0B;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYF,IAAEE,IAAEL,IAAE;AAAC,SAAK,YAAUG,IAAE,KAAK,UAAQE,IAAE,KAAK,SAAO,MAAG,KAAK,aAAWL,GAAE;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,SAAO;AAAA,EAAE;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,WAAS,KAAK,QAAQ,KAAK,YAAW,KAAK,UAAU,KAAK,GAAE,KAAK,SAAO,QAAI,KAAK;AAAA,EAAU;AAAC;AAAC,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYH,KAAE,GAAE;AAAC,SAAK,SAAOA,IAAE,KAAK,YAAU,EAAE;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEE,IAAEL,IAAE;AAAC,UAAMO,KAAEJ,KAAE,KAAK,YAAY,CAAC,GAAEK,KAAEH,KAAE,KAAK,YAAY,CAAC,GAAEI,KAAET,KAAE,KAAK,YAAY,CAAC,GAAEC,KAAE,KAAK,SAAO,KAAK,KAAKM,KAAEA,KAAEC,KAAEA,KAAEC,KAAEA,EAAC;AAAE,WAAO,KAAK,UAAU,CAAC,IAAEN,KAAEI,KAAEN,IAAE,KAAK,UAAU,CAAC,IAAEI,KAAEG,KAAEP,IAAE,KAAK,UAAU,CAAC,IAAED,KAAES,KAAER,IAAE,KAAK;AAAA,EAAS;AAAA,EAAC,YAAYE,IAAE;AAAC,aAAQK,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAE,GAAEF,EAAC,IAAEL,GAAE,IAAEK,EAAC,IAAE,KAAK,YAAYA,EAAC,GAAE,EAAEA,EAAC,IAAEL,GAAE,IAAEK,EAAC,IAAE,KAAK,YAAYA,EAAC,GAAE,EAAEA,EAAC,IAAEE,GAAEF,EAAC;AAAE,UAAMH,KAAE,KAAK,cAAcK,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,aAAQF,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAL,GAAEK,EAAC,IAAEH,GAAEG,EAAC,GAAEL,GAAEK,KAAE,CAAC,IAAEH,GAAEG,EAAC;AAAE,UAAMR,KAAE,CAAAK,OAAG;AAAC,YAAML,KAAE,KAAK,cAAcK,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,eAAQE,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAJ,GAAEI,KAAE,CAAC,IAAE,KAAK,IAAIJ,GAAEI,KAAE,CAAC,GAAEP,GAAEO,EAAC,CAAC,GAAEJ,GAAEI,KAAE,CAAC,IAAE,KAAK,IAAIJ,GAAEI,KAAE,CAAC,GAAEP,GAAEO,EAAC,CAAC;AAAA,IAAC;AAAE,aAAQC,KAAE,GAAEA,KAAE,GAAE,EAAEA,IAAE;AAAC,eAAQL,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,GAAEA,EAAC,IAAE,MAAIK,KAAE,KAAGL,MAAGO,GAAEP,EAAC,IAAE,EAAEA,EAAC;AAAE,MAAAH,GAAE,CAAC;AAAA,IAAC;AAAC,QAAIO,KAAE;AAAE,aAAQC,KAAE,GAAEA,KAAE,GAAE,EAAEA,IAAE;AAAC,MAAAE,GAAEF,EAAC,IAAE,EAAEA,EAAC,IAAE,MAAID,MAAG,KAAGC;AAAA,IAAE;AAAC,QAAG,MAAID,MAAG,MAAIA;AAAE,eAAQC,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,KAAG,MAAID,KAAEC,KAAG;AAAC,iBAAQL,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAI,GAAEJ,EAAC,IAAE,EAAEA,EAAC,IAAE,IAAE,EAAEA,EAAC,IAAE,MAAIK,KAAE,KAAGL,MAAGO,GAAEP,EAAC,IAAE,EAAEA,EAAC;AAAE,QAAAH,GAAE,CAAC;AAAA,MAAC;AAAA;AAAC,aAAQQ,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAL,GAAEK,KAAE,CAAC,KAAG,KAAK,YAAYA,EAAC,GAAEL,GAAEK,KAAE,CAAC,KAAG,KAAK,YAAYA,EAAC;AAAE,WAAOL;AAAA,EAAC;AAAC;AAAC,IAAMO,KAAE,EAAE;AAAV,IAAY,IAAE,EAAE;AAAhB,IAAkB,IAAE,EAAE;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYR,KAAE,GAAE;AAAC,SAAK,6BAA2B,GAAE,KAAK,aAAW,EAAE,GAAE,KAAK,OAAK,EAAE,GAAE,KAAK,OAAK,EAAC,QAAO,EAAE,GAAE,UAASC,GAAE,GAAE,YAAW,KAAI,GAAE,KAAK,eAAa,GAAE,KAAK,UAAQ,GAAE,KAAK,aAAaD,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,SAAK,UAAQA,IAAE,KAAK,eAAaA;AAAA,EAAC;AAAA,EAAC,IAAI,OAAOA,IAAE;AAAC,SAAK,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,gBAAgBA,IAAE;AAAC,SAAK,eAAa,KAAK,UAAQA;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,SAAK,6BAA2B,KAAK,KAAKA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEE,IAAEL,IAAE;AAAC,UAAMO,KAAEJ,IAAEK,KAAEH,IAAEI,KAAET,KAAE,KAAK,4BAA2BC,KAAE,KAAK,eAAa,KAAK,KAAKM,KAAEA,KAAEC,KAAEA,KAAEC,KAAEA,EAAC;AAAE,WAAO,KAAK,WAAW,CAAC,IAAEN,KAAEI,KAAEN,IAAE,KAAK,WAAW,CAAC,IAAEI,KAAEG,KAAEP,IAAE,KAAK,WAAW,CAAC,IAAED,KAAES,KAAER,IAAE,KAAK;AAAA,EAAU;AAAA,EAAC,YAAYE,IAAE;AAAC,UAAME,KAAEF,GAAE,CAAC,GAAEH,KAAEG,GAAE,CAAC,GAAEI,KAAEJ,GAAE,CAAC,IAAE,KAAK,4BAA2BK,KAAEL,GAAE,CAAC,GAAEM,KAAEN,GAAE,CAAC,GAAEF,KAAEE,GAAE,CAAC,IAAE,KAAK,4BAA2BS,KAAEP,KAAEG,KAAE,IAAE,IAAE,KAAK,IAAI,KAAK,IAAIH,EAAC,GAAE,KAAK,IAAIG,EAAC,CAAC,GAAEJ,KAAEJ,KAAES,KAAE,IAAE,IAAE,KAAK,IAAI,KAAK,IAAIT,EAAC,GAAE,KAAK,IAAIS,EAAC,CAAC,GAAEI,KAAEN,KAAEN,KAAE,IAAE,IAAE,KAAK,IAAI,KAAK,IAAIM,EAAC,GAAE,KAAK,IAAIN,EAAC,CAAC,GAAEa,KAAE,KAAK,KAAKF,KAAEA,KAAER,KAAEA,KAAES,KAAEA,EAAC;AAAE,QAAGC,KAAE,KAAK,aAAa,QAAOX,GAAE,CAAC,KAAGE,KAAE,IAAE,KAAK,eAAa,GAAEF,GAAE,CAAC,KAAGH,KAAE,IAAE,KAAK,eAAa,GAAEG,GAAE,CAAC,KAAGI,KAAE,IAAE,KAAK,eAAa,GAAEJ,GAAE,CAAC,KAAGK,KAAE,IAAE,KAAK,eAAa,GAAEL,GAAE,CAAC,KAAGM,KAAE,IAAE,KAAK,eAAa,GAAEN,GAAE,CAAC,KAAGF,KAAE,IAAE,KAAK,eAAa,GAAEE;AAAE,UAAMY,KAAE,KAAK,IAAI,KAAK,IAAIV,EAAC,GAAE,KAAK,IAAIG,EAAC,CAAC,GAAE,IAAE,KAAK,IAAI,KAAK,IAAIR,EAAC,GAAE,KAAK,IAAIS,EAAC,CAAC,GAAEO,KAAE,KAAK,IAAI,KAAK,IAAIT,EAAC,GAAE,KAAK,IAAIN,EAAC,CAAC,GAAEgB,KAAE,KAAK,KAAKF,KAAEA,KAAE,IAAE,IAAEC,KAAEA,EAAC,GAAEE,KAAE,KAAK,eAAaD,IAAElB,KAAE,KAAK,eAAae;AAAE,WAAOX,GAAE,CAAC,KAAGE,MAAGA,KAAE,IAAEa,KAAEnB,KAAGI,GAAE,CAAC,KAAGH,MAAGA,KAAE,IAAEkB,KAAEnB,KAAGI,GAAE,CAAC,KAAGI,MAAGA,KAAE,IAAEW,KAAEnB,KAAGI,GAAE,CAAC,KAAGK,MAAGA,KAAE,IAAEU,KAAEnB,KAAGI,GAAE,CAAC,KAAGM,MAAGA,KAAE,IAAES,KAAEnB,KAAGI,GAAE,CAAC,KAAGF,MAAGA,KAAE,IAAEiB,KAAEnB,KAAGI;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAME,KAAE,KAAK,KAAKF,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC,GAAEH,KAAE,KAAK,eAAaK;AAAE,WAAO,KAAK,KAAK,CAAC,IAAEF,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEH,IAAE,KAAK,KAAK,CAAC,IAAEG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEH,IAAE,KAAK,KAAK,CAAC,IAAEG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEH,IAAE,KAAK,KAAK,CAAC,IAAEG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,KAAK,eAAaE,IAAE,KAAK;AAAA,EAAI;AAAA,EAAC,WAAWF,IAAE;AAAC,UAAME,KAAEF,GAAE,QAAOH,KAAE,KAAK,eAAa,KAAK,KAAKK,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAE,SAAK,KAAK,OAAO,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEL,IAAE,KAAK,KAAK,OAAO,CAAC,IAAEK,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEL,IAAE,KAAK,KAAK,OAAO,CAAC,IAAEK,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEL,IAAE,EAAE,KAAK,KAAK,UAASG,GAAE,UAASA,GAAE,UAAU,GAAE,EAAE,KAAK,KAAK,UAAS,KAAK,KAAK,UAASA,GAAE,MAAM;AAAE,UAAMI,KAAE,KAAK,eAAa,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,IAAE,KAAK,KAAK,SAAS,CAAC,IAAE,KAAK,KAAK,SAAS,CAAC,IAAE,KAAK,KAAK,SAAS,CAAC,IAAE,KAAK,KAAK,SAAS,CAAC,IAAE,KAAK,KAAK,SAAS,CAAC,CAAC;AAAE,WAAO,KAAK,KAAK,SAAS,CAAC,KAAG,KAAK,KAAK,SAAS,CAAC,IAAEA,IAAE,KAAK,KAAK,SAAS,CAAC,KAAG,KAAK,KAAK,SAAS,CAAC,IAAEA,IAAE,KAAK,KAAK,SAAS,CAAC,KAAG,KAAK,KAAK,SAAS,CAAC,IAAEA,IAAEP,GAAE,KAAK,KAAK,UAAS,KAAK,KAAK,UAASG,GAAE,MAAM,GAAE,EAAEgB,IAAEhB,GAAE,UAAU,GAAE,EAAE,KAAK,KAAK,UAAS,KAAK,KAAK,UAASgB,EAAC,GAAE,KAAK,KAAK,SAAS,CAAC,KAAG,KAAK,KAAK,SAAS,CAAC,IAAE,IAAE,KAAG,GAAE,KAAK,KAAK,SAAS,CAAC,KAAG,KAAK,KAAK,SAAS,CAAC,IAAE,IAAE,KAAG,GAAE,KAAK,KAAK,SAAS,CAAC,KAAG,KAAK,KAAK,SAAS,CAAC,IAAE,IAAE,KAAG,GAAE,KAAK,KAAK,aAAWhB,GAAE,YAAW,KAAK;AAAA,EAAI;AAAC;AAAC,IAAMiB,KAAN,MAAO;AAAA,EAAC,YAAYjB,KAAE,GAAE;AAAC,SAAK,SAAOA,IAAE,KAAK,SAAO,EAAE,GAAE,KAAK,YAAU,EAAE;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEE,IAAEL,IAAE;AAAC,UAAMO,KAAE,KAAK,gBAAgB;AAAU,QAAIC,KAAED,GAAE,CAAC,IAAEJ,KAAEI,GAAE,CAAC,IAAEF,KAAEE,GAAE,CAAC,IAAEP,KAAEO,GAAE,EAAE,GAAEE,KAAEF,GAAE,CAAC,IAAEJ,KAAEI,GAAE,CAAC,IAAEF,KAAEE,GAAE,CAAC,IAAEP,KAAEO,GAAE,EAAE,GAAEN,KAAEM,GAAE,CAAC,IAAEJ,KAAEI,GAAE,CAAC,IAAEF,KAAEE,GAAE,EAAE,IAAEP,KAAEO,GAAE,EAAE;AAAE,UAAMK,KAAE,KAAK,SAAO,KAAK,KAAKJ,KAAEA,KAAEC,KAAEA,KAAER,KAAEA,EAAC;AAAE,IAAAO,MAAGA,KAAEI,IAAEH,MAAGA,KAAEG,IAAEX,MAAGA,KAAEW;AAAE,UAAMR,KAAE,KAAK,gBAAgB;AAAQ,WAAO,KAAK,UAAU,CAAC,IAAEA,GAAE,CAAC,IAAEI,KAAEJ,GAAE,CAAC,IAAEK,KAAEL,GAAE,CAAC,IAAEH,KAAEG,GAAE,EAAE,GAAE,KAAK,UAAU,CAAC,IAAEA,GAAE,CAAC,IAAEI,KAAEJ,GAAE,CAAC,IAAEK,KAAEL,GAAE,CAAC,IAAEH,KAAEG,GAAE,EAAE,GAAE,KAAK,UAAU,CAAC,IAAEA,GAAE,CAAC,IAAEI,KAAEJ,GAAE,CAAC,IAAEK,KAAEL,GAAE,EAAE,IAAEH,KAAEG,GAAE,EAAE,GAAE,KAAK;AAAA,EAAS;AAAA,EAAC,cAAcD,IAAEE,IAAE;AAAC,UAAML,KAAE,KAAK,SAAO,KAAK,KAAKG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAE,IAAAA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEH,IAAEG,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEH,IAAEG,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEH;AAAE,UAAMO,KAAE,KAAK,SAAO,KAAK,KAAKF,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAE,IAAAA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEE,IAAEF,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEE,IAAEF,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEE;AAAA,EAAC;AAAA,EAAC,YAAYJ,IAAE;AAAC,UAAME,KAAE,KAAK,SAAO,KAAK,KAAKF,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAE,IAAAA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEE,IAAEF,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEE,IAAEF,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEE;AAAE,UAAML,KAAE,KAAK,SAAO,KAAK,KAAKG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAE,WAAOA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEH,IAAEG,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEH,IAAEG,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAEH,IAAEG;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,UAAME,KAAE,KAAK,KAAKF,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC,GAAEH,KAAE,KAAK,SAAOK;AAAE,WAAO,KAAK,OAAO,CAAC,IAAEF,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEH,IAAE,KAAK,OAAO,CAAC,IAAEG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEH,IAAE,KAAK,OAAO,CAAC,IAAEG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEH,IAAE,KAAK,OAAO,CAAC,IAAEG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,KAAK,SAAOE,IAAE,KAAK;AAAA,EAAM;AAAC;AAAC,IAAMgB,KAAE,IAAID;AAAE,SAASlB,GAAEG,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAGgB,GAAE,SAAOhB,IAAEgB,MAAG;AAAI;AAAC,IAAM,IAAE,IAAIV;AAAgD,IAAM,IAAE,IAAIW;AAAgD,IAAkBC,KAAEC,GAAE;;;ACAnkM,SAASC,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEF,GAAE,aAAYG,KAAEH,GAAE,mBAAkBI,KAAEN,GAAE;AAAO,EAAAG,MAAGE;AAAE,WAAQE,KAAE,GAAEA,KAAED,IAAE,EAAEC,IAAE;AAAC,UAAML,KAAE,IAAEF,GAAEO,EAAC;AAAE,IAAAH,GAAED,EAAC,IAAEF,GAAEC,EAAC,GAAEE,GAAED,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEC,MAAGE;AAAA,EAAC;AAAC;AAAC,SAASG,GAAER,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE,aAAYI,KAAEJ,GAAE,mBAAkBK,KAAEP,GAAE;AAAO,MAAGG,MAAGG,IAAE,QAAMF,MAAG,MAAIA,GAAE,UAAQK,KAAE,GAAEA,KAAEF,IAAE,EAAEE,IAAE;AAAC,UAAMP,KAAE,IAAEF,GAAES,EAAC;AAAE,IAAAJ,GAAEF,EAAC,IAAEF,GAAEC,EAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEC,MAAGG;AAAA,EAAC;AAAA,MAAM,UAAQG,KAAE,GAAEA,KAAEF,IAAE,EAAEE,IAAE;AAAC,UAAMP,KAAE,IAAEF,GAAES,EAAC;AAAE,aAAQT,KAAE,GAAEA,KAAEI,IAAE,EAAEJ,GAAE,CAAAK,GAAEF,EAAC,IAAEF,GAAEC,EAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEC,MAAGG;AAAA,EAAC;AAAC;AAAC,SAASI,GAAEV,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,GAAE;AAAC,QAAMC,KAAEH,GAAE,aAAYI,KAAEJ,GAAE,mBAAkBK,KAAEP,GAAE;AAAO,MAAGG,MAAGG,IAAE,MAAIF,GAAE,UAAQK,KAAE,GAAEA,KAAEF,IAAE,EAAEE,IAAE;AAAC,UAAMP,KAAE,IAAEF,GAAES,EAAC;AAAE,IAAAJ,GAAEF,EAAC,IAAEF,GAAEC,EAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEC,MAAGG;AAAA,EAAC;AAAA,MAAM,UAAQG,KAAE,GAAEA,KAAEF,IAAE,EAAEE,IAAE;AAAC,UAAMP,KAAE,IAAEF,GAAES,EAAC;AAAE,aAAQT,KAAE,GAAEA,KAAEI,IAAE,EAAEJ,GAAE,CAAAK,GAAEF,EAAC,IAAEF,GAAEC,EAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEF,GAAEC,KAAE,CAAC,GAAEC,MAAGG;AAAA,EAAC;AAAC;AAAC,SAASK,GAAEX,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE,aAAYI,KAAEJ,GAAE;AAAkB,EAAAC,MAAGG;AAAE,WAAQC,KAAE,GAAEA,KAAEH,IAAE,EAAEG,GAAE,CAAAF,GAAEF,EAAC,IAAE,GAAEE,GAAEF,KAAE,CAAC,IAAE,GAAEE,GAAEF,KAAE,CAAC,IAAE,GAAEE,GAAEF,KAAE,CAAC,IAAE,GAAEA,MAAGG;AAAC;AAA2T,SAASQ,GAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,GAAE;AAAC,MAAG,CAACH,GAAE,QAAO,KAAKI,GAAEN,IAAEC,IAAEE,IAAEC,IAAEC,EAAC;AAAE,QAAME,KAAEJ,GAAE,aAAYK,KAAEL,GAAE,mBAAkBM,KAAET,GAAE,QAAOU,KAAER,GAAE,CAAC,GAAES,KAAET,GAAE,CAAC,GAAEU,KAAEV,GAAE,CAAC,GAAEW,KAAEX,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAEH,KAAEG,GAAE,CAAC,GAAEY,KAAEZ,GAAE,CAAC,GAAEa,KAAEb,GAAE,CAAC,GAAEc,KAAEd,GAAE,EAAE,GAAEe,KAAEf,GAAE,EAAE,GAAEgB,KAAEhB,GAAE,EAAE,GAAEiB,KAAEjB,GAAE,EAAE;AAAE,EAAAE,MAAGI;AAAE,MAAIY,KAAE,GAAE,IAAE,GAAEC,KAAE;AAAE,QAAMC,KAAE,EAAEpB,EAAC,IAAE,CAAAF,OAAG;AAAC,IAAAoB,KAAEnB,GAAED,EAAC,IAAEiB,IAAE,IAAEhB,GAAED,KAAE,CAAC,IAAEkB,IAAEG,KAAEpB,GAAED,KAAE,CAAC,IAAEmB;AAAA,EAAC,IAAE,CAAAnB,OAAG;AAAC,UAAMuB,KAAEtB,GAAED,EAAC,GAAEE,KAAED,GAAED,KAAE,CAAC,GAAEG,KAAEF,GAAED,KAAE,CAAC;AAAE,IAAAoB,KAAEV,KAAEa,KAAEV,KAAEX,KAAEY,KAAEX,KAAEc,IAAE,IAAEN,KAAEY,KAAE,IAAErB,KAAEa,KAAEZ,KAAEe,IAAEG,KAAET,KAAEW,KAAExB,KAAEG,KAAEc,KAAEb,KAAEgB;AAAA,EAAC;AAAE,MAAG,MAAId,GAAE,UAAQkB,KAAE,GAAEA,KAAEd,IAAE,EAAEc,GAAE,CAAAD,GAAE,IAAEtB,GAAEuB,EAAC,CAAC,GAAEhB,GAAEH,EAAC,IAAEgB,IAAEb,GAAEH,KAAE,CAAC,IAAE,GAAEG,GAAEH,KAAE,CAAC,IAAEiB,IAAEjB,MAAGI;AAAA,MAAO,UAAQe,KAAE,GAAEA,KAAEd,IAAE,EAAEc,IAAE;AAAC,IAAAD,GAAE,IAAEtB,GAAEuB,EAAC,CAAC;AAAE,aAAQvB,KAAE,GAAEA,KAAEK,IAAE,EAAEL,GAAE,CAAAO,GAAEH,EAAC,IAAEgB,IAAEb,GAAEH,KAAE,CAAC,IAAE,GAAEG,GAAEH,KAAE,CAAC,IAAEiB,IAAEjB,MAAGI;AAAA,EAAC;AAAC;AAAC,SAAS,EAAER,IAAEE,IAAEC,IAAEC,IAAEC,IAAEE,KAAE,GAAE;AAAC,MAAG,CAACJ,GAAE,QAAO,KAAKG,GAAEN,IAAEE,IAAEE,IAAEC,IAAEE,EAAC;AAAE,QAAMC,KAAEL,IAAEM,KAAEL,GAAE,aAAYM,KAAEN,GAAE,mBAAkBO,KAAEX,GAAE,QAAOY,KAAEJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAET,KAAES,GAAE,CAAC,GAAEM,KAAEN,GAAE,CAAC,GAAEO,KAAEP,GAAE,CAAC,GAAEQ,KAAER,GAAE,CAAC,GAAES,KAAET,GAAE,CAAC,GAAEU,KAAEV,GAAE,EAAE,GAAEW,KAAE,CAAC,EAAEX,EAAC,GAAEY,KAAE,MAAK,IAAE,IAAEA;AAAE,EAAAf,MAAGK;AAAE,MAAIW,KAAE,GAAEC,KAAE,GAAEE,KAAE;AAAE,QAAM,IAAE,EAAEhB,EAAC,IAAE,CAAAR,OAAG;AAAC,IAAAqB,KAAEnB,GAAEF,EAAC,GAAEsB,KAAEpB,GAAEF,KAAE,CAAC,GAAEwB,KAAEtB,GAAEF,KAAE,CAAC;AAAA,EAAC,IAAE,CAAAA,OAAG;AAAC,UAAMuB,KAAErB,GAAEF,EAAC,GAAEC,KAAEC,GAAEF,KAAE,CAAC,GAAEG,KAAED,GAAEF,KAAE,CAAC;AAAE,IAAAqB,KAAET,KAAEW,KAAExB,KAAEE,KAAEe,KAAEb,IAAEmB,KAAET,KAAEU,KAAET,KAAEb,KAAEgB,KAAEd,IAAEqB,KAAE,IAAED,KAAER,KAAEd,KAAEiB,KAAEf;AAAA,EAAC;AAAE,MAAG,MAAII,GAAE,KAAGY,GAAE,UAAQI,KAAE,GAAEA,KAAEZ,IAAE,EAAEY,IAAE;AAAC,MAAE,IAAEvB,GAAEuB,EAAC,CAAC;AAAE,UAAMtB,KAAEoB,KAAEA,KAAEC,KAAEA,KAAEE,KAAEA;AAAE,QAAGvB,KAAE,KAAGA,KAAEmB,IAAE;AAAC,YAAMpB,KAAE,IAAE,KAAK,KAAKC,EAAC;AAAE,MAAAQ,GAAEJ,EAAC,IAAEgB,KAAErB,IAAES,GAAEJ,KAAE,CAAC,IAAEiB,KAAEtB,IAAES,GAAEJ,KAAE,CAAC,IAAEmB,KAAExB;AAAA,IAAC,MAAM,CAAAS,GAAEJ,EAAC,IAAEgB,IAAEZ,GAAEJ,KAAE,CAAC,IAAEiB,IAAEb,GAAEJ,KAAE,CAAC,IAAEmB;AAAE,IAAAnB,MAAGK;AAAA,EAAC;AAAA,MAAM,UAAQa,KAAE,GAAEA,KAAEZ,IAAE,EAAEY,GAAE,GAAE,IAAEvB,GAAEuB,EAAC,CAAC,GAAEd,GAAEJ,EAAC,IAAEgB,IAAEZ,GAAEJ,KAAE,CAAC,IAAEiB,IAAEb,GAAEJ,KAAE,CAAC,IAAEmB,IAAEnB,MAAGK;AAAA,MAAO,UAAQa,KAAE,GAAEA,KAAEZ,IAAE,EAAEY,IAAE;AAAC,QAAG,EAAE,IAAEvB,GAAEuB,EAAC,CAAC,GAAEJ,IAAE;AAAC,YAAMnB,KAAEqB,KAAEA,KAAEC,KAAEA,KAAEE,KAAEA;AAAE,UAAGxB,KAAE,KAAGA,KAAEoB,IAAE;AAAC,cAAMG,KAAE,IAAE,KAAK,KAAKvB,EAAC;AAAE,QAAAqB,MAAGE,IAAED,MAAGC,IAAEC,MAAGD;AAAA,MAAC;AAAA,IAAC;AAAC,aAAQvB,KAAE,GAAEA,KAAEO,IAAE,EAAEP,GAAE,CAAAS,GAAEJ,EAAC,IAAEgB,IAAEZ,GAAEJ,KAAE,CAAC,IAAEiB,IAAEb,GAAEJ,KAAE,CAAC,IAAEmB,IAAEnB,MAAGK;AAAA,EAAC;AAAC;AAAC,SAASK,GAAEf,IAAEuB,IAAErB,IAAEC,IAAEC,IAAEC,KAAE,GAAE;AAAC,MAAG,CAACH,GAAE,QAAO,KAAKS,GAAEX,IAAEuB,IAAEpB,IAAEC,IAAEC,EAAC;AAAE,QAAME,KAAEL,IAAEM,KAAEL,GAAE,aAAYM,KAAEN,GAAE,mBAAkBO,KAAEV,GAAE,QAAOM,KAAEC,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,GAAEM,KAAEN,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAER,KAAEQ,GAAE,CAAC,GAAEO,KAAEP,GAAE,CAAC,GAAEQ,KAAER,GAAE,CAAC,GAAES,KAAET,GAAE,CAAC,GAAEU,KAAEV,GAAE,EAAE,GAAEW,KAAE,CAAC,EAAEX,EAAC,GAAEY,KAAE,MAAKC,KAAE,IAAED;AAAE,MAAGf,MAAGK,IAAE,MAAIJ,GAAE,UAAQJ,KAAE,GAAEA,KAAES,IAAE,EAAET,IAAE;AAAC,UAAMC,KAAE,IAAEF,GAAEC,EAAC,GAAEE,KAAEoB,GAAErB,EAAC,GAAEG,KAAEkB,GAAErB,KAAE,CAAC,GAAEK,KAAEgB,GAAErB,KAAE,CAAC,GAAEQ,KAAEa,GAAErB,KAAE,CAAC;AAAE,QAAIS,KAAEL,KAAEH,KAAE,IAAEE,KAAEU,KAAER,IAAE,IAAEK,KAAET,KAAEJ,KAAEM,KAAEW,KAAET,IAAEc,KAAER,KAAEV,KAAEW,KAAET,KAAEY,KAAEV;AAAE,QAAGW,IAAE;AAAC,YAAMlB,KAAEW,KAAEA,KAAE,IAAE,IAAEU,KAAEA;AAAE,UAAGrB,KAAEoB,MAAGpB,KAAEmB,IAAE;AAAC,cAAMI,KAAE,IAAE,KAAK,KAAKvB,EAAC;AAAE,QAAAW,MAAGY,IAAE,KAAGA,IAAEF,MAAGE;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAf,GAAEJ,EAAC,IAAEO,IAAEH,GAAEJ,KAAE,CAAC,IAAE,GAAEI,GAAEJ,KAAE,CAAC,IAAEiB,IAAEb,GAAEJ,KAAE,CAAC,IAAEM,IAAEN,MAAGK;AAAA,EAAC;AAAA,MAAM,UAAQR,KAAE,GAAEA,KAAES,IAAE,EAAET,IAAE;AAAC,UAAMC,KAAE,IAAEF,GAAEC,EAAC,GAAEE,KAAEoB,GAAErB,EAAC,GAAEK,KAAEgB,GAAErB,KAAE,CAAC,GAAEQ,KAAEa,GAAErB,KAAE,CAAC,GAAES,KAAEY,GAAErB,KAAE,CAAC;AAAE,QAAI,IAAEI,KAAEH,KAAE,IAAEI,KAAEQ,KAAEL,IAAEW,KAAET,KAAET,KAAEJ,KAAEQ,KAAES,KAAEN,IAAEY,KAAET,KAAEV,KAAEW,KAAEP,KAAEU,KAAEP;AAAE,QAAGQ,IAAE;AAAC,YAAMlB,KAAE,IAAE,IAAEqB,KAAEA,KAAEC,KAAEA;AAAE,UAAGtB,KAAEoB,MAAGpB,KAAEmB,IAAE;AAAC,cAAMI,KAAE,IAAE,KAAK,KAAKvB,EAAC;AAAE,aAAGuB,IAAEF,MAAGE,IAAED,MAAGC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAQvB,KAAE,GAAEA,KAAEK,IAAE,EAAEL,GAAE,CAAAQ,GAAEJ,EAAC,IAAE,GAAEI,GAAEJ,KAAE,CAAC,IAAEiB,IAAEb,GAAEJ,KAAE,CAAC,IAAEkB,IAAEd,GAAEJ,KAAE,CAAC,IAAEO,IAAEP,MAAGK;AAAA,EAAC;AAAC;AAAC,SAASO,GAAEhB,IAAEuB,IAAEtB,IAAEC,IAAEC,IAAEC,KAAE,GAAE;AAAC,QAAMC,KAAEH,GAAE,aAAYK,KAAEL,GAAE,mBAAkBM,KAAER,GAAE;AAAO,MAAGG,MAAGI,IAAEN,OAAIsB,GAAE,UAAQ,MAAItB,GAAE,KAAG,MAAIG,GAAE,KAAG,MAAIH,GAAE,UAAQQ,KAAE,GAAEA,KAAED,IAAE,EAAEC,IAAE;AAAC,UAAMR,KAAE,IAAED,GAAES,EAAC;AAAE,aAAQT,KAAE,GAAEA,KAAEI,IAAE,EAAEJ,GAAE,CAAAK,GAAEF,EAAC,IAAEoB,GAAEtB,EAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEoB,GAAEtB,KAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEoB,GAAEtB,KAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAE,KAAIA,MAAGI;AAAA,EAAC;AAAA,MAAM,UAAQE,KAAE,GAAEA,KAAED,IAAE,EAAEC,IAAE;AAAC,UAAMR,KAAE,IAAED,GAAES,EAAC;AAAE,aAAQT,KAAE,GAAEA,KAAEI,IAAE,EAAEJ,GAAE,CAAAK,GAAEF,EAAC,IAAEoB,GAAEtB,EAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEoB,GAAEtB,KAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEoB,GAAEtB,KAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEoB,GAAEtB,KAAE,CAAC,GAAEE,MAAGI;AAAA,EAAC;AAAA,OAAK;AAAC,QAAG,MAAIN,IAAE;AAAC,eAAQA,KAAE,GAAEA,KAAEO,IAAE,EAAEP,IAAE;AAAC,cAAMC,KAAE,IAAEF,GAAEC,EAAC;AAAE,QAAAI,GAAEF,EAAC,IAAEoB,GAAErB,EAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEoB,GAAErB,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEoB,GAAErB,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEoB,GAAErB,KAAE,CAAC,GAAEC,MAAGI;AAAA,MAAC;AAAC;AAAA,IAAM;AAAC,aAAQN,KAAE,GAAEA,KAAEO,IAAE,EAAEP,IAAE;AAAC,YAAMC,KAAE,IAAEF,GAAEC,EAAC;AAAE,MAAAI,GAAEF,EAAC,IAAEoB,GAAErB,EAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEoB,GAAErB,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAEoB,GAAErB,KAAE,CAAC,GAAEG,GAAEF,KAAE,CAAC,IAAE,KAAIA,MAAGI;AAAA,IAAC;AAAA,EAAC;AAAA,OAAK;AAAC,IAAAF,GAAEF,EAAC,IAAEoB,GAAE,CAAC,GAAElB,GAAEF,KAAE,CAAC,IAAEoB,GAAE,CAAC,GAAElB,GAAEF,KAAE,CAAC,IAAEoB,GAAE,CAAC,GAAElB,GAAEF,KAAE,CAAC,IAAEoB,GAAE,CAAC;AAAE,UAAMvB,KAAE,IAAI,YAAYE,GAAE,YAAY,QAAOA,GAAE,KAAK,GAAED,KAAEM,KAAE,GAAEE,KAAET,GAAEG,MAAG,CAAC;AAAE,IAAAA,MAAGF;AAAE,UAAMS,KAAEF,KAAEJ;AAAE,aAAQmB,KAAE,GAAEA,KAAEb,IAAE,EAAEa,GAAE,CAAAvB,GAAEG,EAAC,IAAEM,IAAEN,MAAGF;AAAA,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEuB,IAAEtB,IAAEC,IAAEC,KAAE,GAAE;AAAC,QAAMC,KAAEmB,GAAE,aAAYlB,KAAEkB,GAAE;AAAkB,MAAGrB,MAAGG,IAAE,MAAIF,GAAE,UAAQI,KAAE,GAAEA,KAAEN,IAAE,EAAEM,GAAE,CAAAH,GAAEF,EAAC,IAAEF,GAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEE,MAAGG;AAAA,MAAO,UAAQE,KAAE,GAAEA,KAAEN,IAAE,EAAEM,GAAE,UAAQgB,KAAE,GAAEA,KAAEpB,IAAE,EAAEoB,GAAE,CAAAnB,GAAEF,EAAC,IAAEF,GAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEI,GAAEF,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEE,MAAGG;AAAC;AAAC,SAASa,GAAEK,IAAEtB,IAAEQ,IAAEH,IAAEM,IAAEC,IAAE;AAAC,aAAU,KAAKZ,GAAE,YAAW;AAAC,UAAMA,KAAEsB,GAAE,iBAAiB,IAAI,CAAC,GAAEL,KAAEK,GAAE,QAAQ,IAAI,CAAC;AAAE,QAAGtB,MAAGiB,GAAE,SAAO,GAAE;AAAA,MAAC,KAAKF,GAAE,UAAS;AAAC,UAAE,MAAIf,GAAE,IAAI;AAAE,cAAMD,KAAEY,GAAE,SAAS,GAAE,CAAC;AAAE,UAAE,CAAC,CAACZ,IAAE,sBAAsB,CAAC,EAAE,GAAEA,MAAGD,GAAEmB,IAAEjB,GAAE,MAAKQ,IAAET,IAAEa,EAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAKG,GAAE,QAAO;AAAC,UAAE,MAAIf,GAAE,IAAI;AAAE,cAAMD,KAAEY,GAAE,SAAS,GAAE,CAAC;AAAE,UAAE,CAAC,CAACZ,IAAE,sBAAsB,CAAC,EAAE,GAAEA,MAAG,EAAEkB,IAAEjB,GAAE,MAAKK,IAAEN,IAAEa,EAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAKG,GAAE,KAAI;AAAC,UAAE,MAAIf,GAAE,IAAI;AAAE,cAAMD,KAAEY,GAAE,SAAS,GAAEN,EAAC;AAAE,UAAE,CAAC,CAACN,IAAE,sBAAsB,CAAC,EAAE,GAAEA,MAAGU,GAAEQ,IAAEjB,GAAE,MAAKD,IAAEa,EAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAKG,GAAE;AAAA,MAAM,KAAKA,GAAE,aAAY;AAAC,UAAE,MAAIf,GAAE,QAAM,MAAIA,GAAE,IAAI;AAAE,cAAMD,KAAEY,GAAE,SAAS,GAAE,CAAC;AAAE,UAAE,CAAC,CAACZ,IAAE,sBAAsB,CAAC,EAAE,GAAEA,MAAGgB,GAAEE,IAAEjB,GAAE,MAAKA,GAAE,MAAKD,IAAEa,EAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAKG,GAAE,SAAQ;AAAC,UAAE,MAAIf,GAAE,IAAI;AAAE,cAAMD,KAAEY,GAAE,SAAS,GAAEJ,EAAC;AAAE,UAAE,CAAC,CAACR,IAAE,sBAAsB,CAAC,EAAE,GAAEA,MAAGe,GAAEG,IAAEjB,GAAE,MAAKK,IAAEN,IAAEa,EAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAKG,GAAE;AAAA,MAAa,KAAKA,GAAE;AAAA,MAAU,KAAKA,GAAE;AAAA,MAAuB,KAAKA,GAAE,cAAa;AAAC,UAAE,MAAIf,GAAE,IAAI;AAAE,cAAMD,KAAEY,GAAE,SAAS,GAAEJ,EAAC;AAAE,UAAE,CAAC,CAACR,IAAE,sBAAsB,CAAC,EAAE,GAAEA,MAAGW,GAAEO,IAAEjB,GAAE,MAAKD,IAAEa,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,aAAS,MAAIG,GAAE,yBAAuB,EAAEO,GAAE,qBAAqB,GAAE;AAAC,YAAMvB,KAAEuB,GAAE,QAAQ,IAAIP,GAAE,QAAQ;AAAE,UAAG,EAAE,CAAC,CAAChB,IAAE,sBAAsB,CAAC,EAAE,GAAEA,IAAE;AAAC,cAAMC,KAAED,GAAE,QAAOE,KAAEU,GAAE,SAAS,GAAE,CAAC;AAAE,UAAEW,GAAE,uBAAsBrB,IAAED,IAAEY,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACA/hL,IAAMY,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,qBAAmBA;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,WAAO,KAAK,mBAAmB,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaC,IAAE;AAAC,WAAOA,GAAE,QAAQ,IAAIC,GAAE,QAAQ,EAAE;AAAA,EAAM;AAAA,EAAC,MAAMF,IAAED,IAAEI,IAAEC,IAAEC,IAAE;AAAC,IAAAC,GAAEH,IAAE,KAAK,oBAAmBH,IAAED,IAAEK,IAAEC,EAAC;AAAA,EAAC;AAAC;;;ACA5N,IAAME,KAAE,EAAC,MAAK,EAAE,KAAI;AAApB,IAAsBC,KAAE,EAAC,MAAK,EAAE,OAAM;AAAtC,IAAwCC,KAAE,EAAC,MAAK,IAAG;AAAnD,IAAqDC,KAAE,EAAC,MAAK,EAAC;AAA9D,IAAuQC,KAAE,EAAC,UAAS,EAAC,MAAK,EAAE,QAAO,KAAI,EAAE,0BAAyB,MAAK,EAAE,yBAAwB,GAAE,WAAU,EAAC,MAAKC,GAAE,MAAK,OAAMA,GAAE,MAAK,OAAMA,GAAE,KAAI,EAAC;AAAnZ,IAAqZC,KAAE,EAAC,UAAS,EAAC,MAAK,EAAE,QAAO,KAAI,EAAE,0BAAyB,MAAK,EAAE,yBAAwB,GAAE,WAAU,EAAC,MAAKD,GAAE,MAAK,OAAMA,GAAE,MAAK,OAAMA,GAAE,QAAO,EAAC;AAApiB,IAAsiB,IAAE,EAAC,UAAS,EAAC,MAAK,EAAE,OAAM,KAAI,EAAE,0BAAyB,MAAK,EAAE,yBAAwB,GAAE,WAAU,EAAC,MAAKA,GAAE,MAAK,OAAMA,GAAE,MAAK,OAAMA,GAAE,KAAI,EAAC;AAAjrB,IAAmrB,IAAE,EAAC,UAAS,EAAC,MAAK,EAAE,UAAS,KAAI,EAAE,0BAAyB,MAAK,EAAE,yBAAwB,GAAE,WAAU,EAAC,MAAKA,GAAE,MAAK,OAAMA,GAAE,MAAK,OAAMA,GAAE,KAAI,EAAC;", "names": ["t", "o", "i", "e", "c", "d", "l", "m", "u", "a", "f", "r", "x", "t", "P", "v", "s", "n", "h", "c", "r", "o", "a", "f", "g", "m", "h", "p", "d", "i", "u", "l", "z", "n", "s", "t", "j", "r", "n", "e", "r", "o", "t", "s", "i", "u", "r", "t", "e", "i", "s", "n", "o", "h", "a", "A", "O", "c", "x", "E", "u", "e", "o", "y", "t", "n", "s", "v", "i", "r", "a", "O", "g", "h", "f", "l", "m", "b", "c", "p", "I", "x", "S", "v", "I", "e", "d", "e", "t", "f", "o", "r", "n", "s", "i", "u", "c", "a", "p", "g", "e", "f", "o", "r", "n", "s", "u", "i", "c", "l", "d", "a", "p", "y", "b", "h", "O", "N", "S", "m", "A", "R", "v", "t", "E", "r", "t", "e", "O", "i", "u", "f", "S", "i", "s", "e", "l", "f", "O", "o"]}