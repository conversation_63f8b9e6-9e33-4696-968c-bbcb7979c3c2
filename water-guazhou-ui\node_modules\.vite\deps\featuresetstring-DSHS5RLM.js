import {
  T
} from "./chunk-VX64NK2J.js";
import {
  B,
  <PERSON>,
  <PERSON>,
  Ie,
  Oe,
  Z,
  Ze,
  _e,
  b,
  de,
  ie,
  le,
  pe,
  re,
  we,
  ye,
  z
} from "./chunk-REVHHZEO.js";
import "./chunk-6OFWBRK2.js";
import "./chunk-WC4DQSYX.js";
import {
  e,
  t
} from "./chunk-YFVPK4WM.js";
import "./chunk-3HW44BD3.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/arcade/functions/featuresetstring.js
function A(a, e2) {
  return a && a.domain ? "coded-value" === a.domain.type || "codedValue" === a.domain.type ? T.convertObjectToArcadeDictionary({ type: "codedValue", name: a.domain.name, dataType: b[a.field.type], codedValues: a.domain.codedValues.map((n) => ({ name: n.name, code: n.code })) }, Ze(e2)) : T.convertObjectToArcadeDictionary({ type: "range", name: a.domain.name, dataType: b[a.field.type], min: a.domain.min, max: a.domain.max }, Ze(e2)) : null;
}
function b2(h) {
  "async" === h.mode && (h.functions.domain = function(n, u) {
    return h.standardFunctionAsync(n, u, async (m, f, l) => {
      if (B(l, 2, 3, n, u), z(l[0])) {
        return A(Ie(l[0], re(l[1]), void 0 === l[2] ? void 0 : le(l[2])), n);
      }
      if (G(l[0])) {
        await l[0]._ensureLoaded();
        return A(pe(re(l[1]), l[0], null, void 0 === l[2] ? void 0 : le(l[2])), n);
      }
      throw new t(n, e.InvalidParameter, u);
    });
  }, h.functions.subtypes = function(o, i) {
    return h.standardFunctionAsync(o, i, async (s, d, f) => {
      if (B(f, 1, 1, o, i), z(f[0])) {
        const a = Ce(f[0]);
        return a ? T.convertObjectToArcadeDictionary(a, Ze(o)) : null;
      }
      if (G(f[0])) {
        await f[0]._ensureLoaded();
        const a = f[0].subtypes();
        return a ? T.convertObjectToArcadeDictionary(a, Ze(o)) : null;
      }
      throw new t(o, e.InvalidParameter, i);
    });
  }, h.functions.domainname = function(n, o) {
    return h.standardFunctionAsync(n, o, async (u, m, y) => {
      if (B(y, 2, 4, n, o), z(y[0])) return we(y[0], re(y[1]), y[2], void 0 === y[3] ? void 0 : le(y[3]));
      if (G(y[0])) {
        await y[0]._ensureLoaded();
        const n2 = pe(re(y[1]), y[0], null, void 0 === y[3] ? void 0 : le(y[3]));
        return de(n2, y[2]);
      }
      throw new t(n, e.InvalidParameter, o);
    });
  }, h.signatures.push({ name: "domainname", min: 2, max: 4 }), h.functions.domaincode = function(n, o) {
    return h.standardFunctionAsync(n, o, async (u, m, f) => {
      if (B(f, 2, 4, n, o), z(f[0])) return Oe(f[0], re(f[1]), f[2], void 0 === f[3] ? void 0 : le(f[3]));
      if (G(f[0])) {
        await f[0]._ensureLoaded();
        const n2 = pe(re(f[1]), f[0], null, void 0 === f[3] ? void 0 : le(f[3]));
        return ye(n2, f[2]);
      }
      throw new t(n, e.InvalidParameter, o);
    });
  }, h.signatures.push({ name: "domaincode", min: 2, max: 4 })), h.functions.text = function(n, a) {
    return h.standardFunctionAsync(n, a, (e2, r, o) => {
      if (B(o, 1, 2, n, a), !G(o[0])) return ie(o[0], o[1]);
      {
        const n2 = Z(o[1], "");
        if ("" === n2) return o[0].castToText();
        if ("schema" === n2.toLowerCase()) return o[0].convertToText("schema", e2.abortSignal);
        if ("featureset" === n2.toLowerCase()) return o[0].convertToText("featureset", e2.abortSignal);
      }
    });
  }, h.functions.gdbversion = function(n, o) {
    return h.standardFunctionAsync(n, o, async (i, s, d) => {
      if (B(d, 1, 1, n, o), z(d[0])) return d[0].gdbVersion();
      if (G(d[0])) {
        return (await d[0].load()).gdbVersion;
      }
      throw new t(n, e.InvalidParameter, o);
    });
  }, h.functions.schema = function(o, i) {
    return h.standardFunctionAsync(o, i, async (s, d, u) => {
      if (B(u, 1, 1, o, i), G(u[0])) return await u[0].load(), T.convertObjectToArcadeDictionary(u[0].schema(), Ze(o));
      if (z(u[0])) {
        const a = _e(u[0]);
        return a ? T.convertObjectToArcadeDictionary(a, Ze(o)) : null;
      }
      throw new t(o, e.InvalidParameter, i);
    });
  };
}
export {
  b2 as registerFunctions
};
//# sourceMappingURL=featuresetstring-DSHS5RLM.js.map
