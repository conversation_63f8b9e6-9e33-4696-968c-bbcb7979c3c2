import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/support/ExpressionInfo.js
var p2 = class extends i(l) {
  constructor(r) {
    super(r), this.expression = null, this.title = null, this.returnType = null;
  }
};
e([y({ type: String, json: { write: true } })], p2.prototype, "expression", void 0), e([y({ type: String, json: { write: true } })], p2.prototype, "title", void 0), e([y({ type: String, json: { write: true } })], p2.prototype, "returnType", void 0), p2 = e([a("esri.layers.support.ExpressionInfo")], p2);
var i2 = p2;

// node_modules/@arcgis/core/layers/support/AggregateField.js
var p3;
var n = p3 = class extends l {
  constructor(t) {
    super(t), this.isAutoGenerated = false, this.name = null, this.alias = null, this.onStatisticField = null, this.onStatisticExpression = null, this.statisticType = null;
  }
  clone() {
    return new p3({ name: this.name, alias: this.alias, isAutoGenerated: this.isAutoGenerated, onStatisticExpression: p(this.onStatisticExpression), onStatisticField: this.onStatisticField, statisticType: this.statisticType });
  }
};
e([y({ type: Boolean, json: { write: true } })], n.prototype, "isAutoGenerated", void 0), e([y({ type: String, json: { write: true } })], n.prototype, "name", void 0), e([y({ type: String, json: { write: true } })], n.prototype, "alias", void 0), e([y({ type: String, json: { write: true } })], n.prototype, "onStatisticField", void 0), e([y({ type: i2, json: { write: true } })], n.prototype, "onStatisticExpression", void 0), e([y({ type: String, json: { write: true } })], n.prototype, "statisticType", void 0), n = p3 = e([a("esri.layers.support.AggregateField")], n);
var a2 = n;

export {
  i2 as i,
  a2 as a
};
//# sourceMappingURL=chunk-ORU3OGKZ.js.map
