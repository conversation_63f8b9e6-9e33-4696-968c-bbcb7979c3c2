{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/webgl/VertexStream.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrush.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/BrushBitmap.js", "../../@arcgis/core/views/2d/engine/webgl/shaders/sources/shaderRepository.js", "../../@arcgis/core/views/2d/engine/webgl/shaders/sources/resolver.js", "../../@arcgis/core/views/2d/engine/webgl/shaders/BackgroundPrograms.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrush.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushMarker.js", "../../@arcgis/core/views/2d/engine/webgl/effects/Effect.js", "../../@arcgis/core/views/webgl/heatmapTextureUtils.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushHeatmap.js", "../../@arcgis/core/views/2d/engine/webgl/DefaultVertexAttributeLayouts.js", "../../@arcgis/core/views/2d/engine/webgl/shaders/TileInfoPrograms.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/BrushClip.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/BrushOverlay.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushFill.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushDotDensity.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushInfo.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushPieChart.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushStencil.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLBackground.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLCircle.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLFill.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLLine.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLSymbol.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushLabel.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushLine.js", "../../@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushText.js", "../../@arcgis/core/views/2d/engine/brushes.js", "../../@arcgis/core/views/2d/engine/webgl/Mesh2D.js", "../../@arcgis/core/views/2d/engine/webgl/ClippingInfo.js", "../../@arcgis/core/views/2d/engine/webgl/WGLContainer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{BufferObject as t}from\"../../../webgl/BufferObject.js\";import{Usage as e,DataType as r,PrimitiveType as s}from\"../../../webgl/enums.js\";import{VertexArrayObject as i}from\"../../../webgl/VertexArrayObject.js\";import{VertexElementDescriptor as o}from\"../../../webgl/VertexElementDescriptor.js\";class n{constructor(s,n){this._rctx=s,this._vertexBuffer=t.createVertex(s,e.STATIC_DRAW,new Uint16Array(n)),this._vao=new i(s,new Map([[\"a_position\",0]]),{geometry:[new o(\"a_position\",2,r.SHORT,0,4)]},{geometry:this._vertexBuffer}),this._count=n.length/2}bind(){this._rctx.bindVAO(this._vao)}unbind(){this._rctx.bindVAO(null)}dispose(){this._vao.dispose(!1),this._vertexBuffer.dispose()}draw(){this._rctx.bindVAO(this._vao),this._rctx.drawArrays(s.TRIANGLE_STRIP,0,this._count)}}export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(){this.name=this.constructor.name||\"UnnamedBrush\",this.brushEffect=null}prepareState(t,r){}draw(t,r,s){}drawMany(t,r,s){for(const a of r)a.visible&&this.draw(t,a,s)}}export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../../core/maybe.js\";import{TEXTURE_BINDING_BITMAP as t}from\"../definitions.js\";import i from\"../VertexStream.js\";import s from\"./WGLBrush.js\";import{TextureSamplingMode as n,BlendFactor as r,CompareFunction as a}from\"../../../../webgl/enums.js\";const o={nearest:{defines:[],samplingMode:n.NEAREST,mips:!1},bilinear:{defines:[],samplingMode:n.LINEAR,mips:!1},bicubic:{defines:[\"bicubic\"],samplingMode:n.LINEAR,mips:!1},trilinear:{defines:[],samplingMode:n.LINEAR_MIPMAP_LINEAR,mips:!0}},d=(e,t,i)=>{if(\"dynamic\"===i.samplingMode){const{state:i}=e,s=t.resolution/t.pixelRatio/i.resolution,n=Math.round(e.pixelRatio)!==e.pixelRatio,r=s>1.05||s<.95;return i.rotation||r||n||t.isSourceScaled||t.rotation?o.bilinear:o.nearest}return o[i.samplingMode]};class m extends s{constructor(){super(...arguments),this._desc={vsPath:\"raster/bitmap\",fsPath:\"raster/bitmap\",attributes:new Map([[\"a_pos\",0]])}}dispose(){this._quad&&this._quad.dispose()}prepareState({context:e}){e.setBlendingEnabled(!0),e.setColorMask(!0,!0,!0,!0),e.setStencilWriteMask(0),e.setStencilTestEnabled(!0)}draw(s,n){const{context:o,renderingOptions:m,painter:l,requestRender:c,allowDelayedRender:p}=s;if(!n.source||!n.isReady)return;const u=d(s,n,m),f=l.materialManager.getProgram(this._desc,u.defines);if(p&&e(c)&&!f.compiled)return void c();s.timeline.begin(this.name),\"additive\"===n.blendFunction?o.setBlendFunctionSeparate(r.ONE,r.ONE,r.ONE,r.ONE):o.setBlendFunctionSeparate(r.ONE,r.ONE_MINUS_SRC_ALPHA,r.ONE,r.ONE_MINUS_SRC_ALPHA),o.setStencilFunction(a.EQUAL,n.stencilRef,255),this._quad||(this._quad=new i(o,[0,0,1,0,0,1,1,1]));const{coordScale:_,computedOpacity:E,transforms:M}=n;n.setSamplingProfile(u),n.bind(s.context,t),o.useProgram(f),f.setUniformMatrix3fv(\"u_dvsMat3\",M.dvs),f.setUniform1i(\"u_texture\",t),f.setUniform2fv(\"u_coordScale\",_),f.setUniform1f(\"u_opacity\",E),this._quad.draw(),s.timeline.end(this.name)}}export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst e={background:{\"background.frag\":\"uniform lowp vec4 u_color;\\nvoid main() {\\ngl_FragColor = u_color;\\n}\",\"background.vert\":\"attribute vec2 a_pos;\\nuniform highp mat3 u_dvsMat3;\\nuniform mediump vec2 u_coord_range;\\nuniform mediump float u_depth;\\nvoid main() {\\nvec3 v_pos = u_dvsMat3 * vec3(u_coord_range * a_pos, 1.0);\\ngl_Position = vec4(v_pos.xy, 0.0, 1.0);\\n}\"},bitBlit:{\"bitBlit.frag\":\"uniform lowp sampler2D u_tex;\\nuniform lowp float u_opacity;\\nvarying mediump vec2 v_uv;\\nvoid main() {\\nlowp vec4 color = texture2D(u_tex, v_uv);\\ngl_FragColor = color *  u_opacity;\\n}\",\"bitBlit.vert\":\"attribute vec2 a_pos;\\nattribute vec2 a_tex;\\nvarying mediump vec2 v_uv;\\nvoid main(void) {\\ngl_Position = vec4(a_pos, 0.0, 1.0);\\nv_uv = a_tex;\\n}\"},blend:{\"blend.frag\":\"precision mediump float;\\nuniform sampler2D u_layerTexture;\\nuniform lowp float u_opacity;\\nuniform lowp float u_inFadeOpacity;\\n#ifndef NORMAL\\nuniform sampler2D u_backbufferTexture;\\n#endif\\nvarying mediump vec2 v_uv;\\nfloat rgb2v(in vec3 c) {\\nreturn max(c.x, max(c.y, c.z));\\n}\\nvec3 rgb2hsv(in vec3 c) {\\nvec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);\\nvec4 p = c.g < c.b ? vec4(c.bg, K.wz) : vec4(c.gb, K.xy);\\nvec4 q = c.r < p.x ? vec4(p.xyw, c.r) : vec4(c.r, p.yzx);\\nfloat d = q.x - min(q.w, q.y);\\nfloat e = 1.0e-10;\\nreturn vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), min(d / (q.x + e), 1.0), q.x);\\n}\\nvec3 hsv2rgb(in vec3 c) {\\nvec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);\\nvec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);\\nreturn c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);\\n}\\nvec3 tint(in vec3 Cb, in vec3 Cs) {\\nfloat vIn = rgb2v(Cb);\\nvec3 hsvTint = rgb2hsv(Cs);\\nvec3 hsvOut = vec3(hsvTint.x, hsvTint.y, vIn * hsvTint.z);\\nreturn hsv2rgb(hsvOut);\\n}\\nfloat overlay(in float Cb, in float Cs) {\\nreturn (1.0 - step(0.5, Cs)) * (1.0 - 2.0 * (1.0 - Cs ) * (1.0 - Cb)) + step(0.5, Cs) * (2.0 * Cs * Cb);\\n}\\nfloat colorDodge(in float Cb, in float Cs) {\\nreturn (Cb == 0.0) ? 0.0 : (Cs == 1.0) ? 1.0 : min(1.0, Cb / (1.0 - Cs));\\n}\\nfloat colorBurn(in float Cb, in float Cs) {\\nreturn (Cb == 1.0) ? 1.0 : (Cs == 0.0) ? 0.0 : 1.0 - min(1.0, (1.0 - Cb) / Cs);\\n}\\nfloat hardLight(in float Cb, in float Cs) {\\nreturn (1.0 - step(0.5, Cs)) * (2.0 * Cs * Cb) + step(0.5, Cs) * (1.0 - 2.0 * (1.0 - Cs) * (1.0 - Cb));\\n}\\nfloat reflectBlend(in float Cb, in float Cs) {\\nreturn (Cs == 1.0) ? Cs : min(Cb * Cb / (1.0 - Cs), 1.0);\\n}\\nfloat softLight(in float Cb, in float Cs) {\\nif (Cs <= 0.5) {\\nreturn Cb - (1.0 - 2.0 * Cs) * Cb * (1.0 - Cb);\\n}\\nif (Cb <= 0.25) {\\nreturn Cb + (2.0 * Cs - 1.0) * Cb * ((16.0 * Cb - 12.0) * Cb + 3.0);\\n}\\nreturn Cb + (2.0 * Cs - 1.0) * (sqrt(Cb) - Cb);\\n}\\nfloat vividLight(in float Cb, in float Cs) {\\nreturn (1.0 - step(0.5, Cs)) * colorBurn(Cb, 2.0 * Cs) + step(0.5, Cs) * colorDodge(Cb, (2.0 * (Cs - 0.5)));\\n}\\nfloat minv3(in vec3 c) {\\nreturn min(min(c.r, c.g), c.b);\\n}\\nfloat maxv3(in vec3 c) {\\nreturn max(max(c.r, c.g), c.b);\\n}\\nfloat lumv3(in vec3 c) {\\nreturn dot(c, vec3(0.3, 0.59, 0.11));\\n}\\nfloat satv3(vec3 c) {\\nreturn maxv3(c) - minv3(c);\\n}\\nvec3 clipColor(vec3 color) {\\nfloat lum = lumv3(color);\\nfloat mincol = minv3(color);\\nfloat maxcol = maxv3(color);\\nif (mincol < 0.0) {\\ncolor = lum + ((color - lum) * lum) / (lum - mincol);\\n}\\nif (maxcol > 1.0) {\\ncolor = lum + ((color - lum) * (1.0 - lum)) / (maxcol - lum);\\n}\\nreturn color;\\n}\\nvec3 setLum(vec3 cbase, vec3 clum) {\\nfloat lbase = lumv3(cbase);\\nfloat llum = lumv3(clum);\\nfloat ldiff = llum - lbase;\\nvec3 color = cbase + vec3(ldiff);\\nreturn clipColor(color);\\n}\\nvec3 setLumSat(vec3 cbase, vec3 csat, vec3 clum)\\n{\\nfloat minbase = minv3(cbase);\\nfloat sbase = satv3(cbase);\\nfloat ssat = satv3(csat);\\nvec3 color;\\nif (sbase > 0.0) {\\ncolor = (cbase - minbase) * ssat / sbase;\\n} else {\\ncolor = vec3(0.0);\\n}\\nreturn setLum(color, clum);\\n}\\nvoid main() {\\nvec4 src = texture2D(u_layerTexture, v_uv);\\n#ifdef NORMAL\\ngl_FragColor = src *  u_opacity;\\n#else\\nvec4 dst = texture2D(u_backbufferTexture, v_uv);\\nvec3 Cs = src.a == 0.0 ? src.rgb : vec3(src.rgb / src.a);\\nvec3 Cb = dst.a == 0.0 ? dst.rgb : vec3(dst.rgb / dst.a);\\nfloat as = u_opacity * src.a;\\nfloat ab = dst.a;\\n#ifdef DESTINATION_OVER\\ngl_FragColor = vec4(as * Cs * (1.0 - ab) + ab * Cb, as + ab - as * ab);\\n#endif\\n#ifdef SOURCE_IN\\nvec4 color = vec4(as * Cs * ab, as * ab);\\nvec4 fadeColor = (1.0 - u_opacity) * u_inFadeOpacity * vec4(ab * Cb, ab);\\ngl_FragColor = color + fadeColor;\\n#endif\\n#ifdef DESTINATION_IN\\nvec4 color = vec4(ab * Cb * as, ab * as);\\nvec4 fadeColor = (1.0 - u_opacity) * u_inFadeOpacity * vec4(ab * Cb, ab);\\ngl_FragColor = color + fadeColor;\\n#endif\\n#ifdef SOURCE_OUT\\ngl_FragColor = vec4(as * Cs * (1.0 - ab), as * (1.0 - ab));\\n#endif\\n#ifdef DESTINATION_OUT\\ngl_FragColor = vec4(ab * Cb * (1.0 - as), ab * (1.0 - as));\\n#endif\\n#ifdef SOURCE_ATOP\\ngl_FragColor = vec4(as * Cs * ab + ab * Cb * (1.0 - as), ab);\\n#endif\\n#ifdef DESTINATION_ATOP\\ngl_FragColor = vec4(as * Cs * (1.0 - ab) + ab * Cb * as, as);\\n#endif\\n#ifdef XOR\\ngl_FragColor = vec4(as * Cs * (1.0 - ab) + ab * Cb * (1.0 - as),\\nas * (1.0 - ab) + ab * (1.0 - as));\\n#endif\\n#ifdef MULTIPLY\\ngl_FragColor = vec4(as * Cs * ab * Cb + (1.0 - ab) * as * Cs + (1.0 - as) * ab * Cb,\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef SCREEN\\ngl_FragColor = vec4((Cs + Cb - Cs * Cb) * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef OVERLAY\\nvec3 f = vec3(overlay(Cb.r, Cs.r), overlay(Cb.g, Cs.g), overlay(Cb.b, Cs.b));\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef DARKEN\\ngl_FragColor = vec4(min(Cs, Cb) * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef LIGHTER\\ngl_FragColor = vec4(as * Cs + ab * Cb, as + ab);\\n#endif\\n#ifdef LIGHTEN\\ngl_FragColor = vec4(max(Cs, Cb) * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef COLOR_DODGE\\nvec3 f = clamp(vec3(colorDodge(Cb.r, Cs.r), colorDodge(Cb.g, Cs.g), colorDodge(Cb.b, Cs.b)), vec3(0.0), vec3(1.0));\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef COLOR_BURN\\nvec3 f = vec3(colorBurn(Cb.r, Cs.r), colorBurn(Cb.g, Cs.g), colorBurn(Cb.b, Cs.b));\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef HARD_LIGHT\\nvec3 f = vec3(hardLight(Cb.r, Cs.r), hardLight(Cb.g, Cs.g), hardLight(Cb.b, Cs.b));\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef SOFT_LIGHT\\nvec3 f = vec3(softLight(Cb.r, Cs.r), softLight(Cb.g, Cs.g), softLight(Cb.b, Cs.b));\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef DIFFERENCE\\ngl_FragColor = vec4(abs(Cb - Cs) * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef EXCLUSION\\nvec3 f = Cs + Cb - 2.0 * Cs * Cb;\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef INVERT\\ngl_FragColor = vec4((1.0 - Cb) * as * ab + Cb * ab * (1.0 - as), ab);\\n#endif\\n#ifdef VIVID_LIGHT\\nvec3 f = vec3(clamp(vividLight(Cb.r, Cs.r), 0.0, 1.0),\\nclamp(vividLight(Cb.g, Cs.g), 0.0, 1.0),\\nclamp(vividLight(Cb.b, Cs.b), 0.0, 1.0));\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef HUE\\nvec3 f = setLumSat(Cs,Cb,Cb);\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef SATURATION\\nvec3 f = setLumSat(Cb,Cs,Cb);\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef COLOR\\nvec3 f = setLum(Cs,Cb);\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef LUMINOSITY\\nvec3 f = setLum(Cb,Cs);\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef PLUS\\ngl_FragColor = clamp(vec4(src.r + Cb.r, src.g + Cb.g, src.b + Cb.b, as + ab), 0.0, 1.0);\\n#endif\\n#ifdef MINUS\\ngl_FragColor = vec4(clamp(vec3(Cb.r - src.r, Cb.g - src.g, Cb.b - src.b), 0.0, 1.0), ab * as);\\n#endif\\n#ifdef AVERAGE\\nvec3 f = (Cb + Cs) / 2.0;\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#ifdef REFLECT\\nvec3 f = clamp(vec3(reflectBlend(Cb.r, Cs.r),\\nreflectBlend(Cb.g, Cs.g),\\nreflectBlend(Cb.b, Cs.b)), vec3(0.0), vec3(1.0));\\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\\nas + ab * (1.0 - as));\\n#endif\\n#endif\\n}\",\"blend.vert\":\"attribute vec2 a_position;\\nvarying mediump vec2 v_uv;\\nvoid main(void) {\\ngl_Position = vec4(a_position , 0.0, 1.0);\\nv_uv = (a_position + 1.0) / 2.0;\\n}\"},debug:{overlay:{\"overlay.frag\":\"precision mediump float;\\nvarying vec4 v_color;\\nvoid main(void) {\\ngl_FragColor = v_color;\\n}\",\"overlay.vert\":\"attribute vec3 a_PositionAndFlags;\\nuniform mat3 u_dvsMat3;\\nuniform vec4 u_colors[4];\\nuniform float u_opacities[4];\\nvarying vec4 v_color;\\nvoid main(void) {\\nvec2 position = a_PositionAndFlags.xy;\\nfloat flags = a_PositionAndFlags.z;\\nint colorIndex = int(mod(flags, 4.0));\\nvec4 color;\\nfor (int i = 0; i < 4; i++) {\\ncolor = u_colors[i];\\nif (i == colorIndex) {\\nbreak;\\n}\\n}\\nint opacityIndex = int(mod(floor(flags / 4.0), 4.0));\\nfloat opacity;\\nfor (int i = 0; i < 4; i++) {\\nopacity = u_opacities[i];\\nif (i == opacityIndex) {\\nbreak;\\n}\\n}\\nv_color = color * opacity;\\ngl_Position = vec4((u_dvsMat3 * vec3(position, 1.0)).xy, 0.0, 1.0);\\n}\"}},dot:{dot:{\"dot.frag\":\"precision mediump float;\\nvarying vec4 v_color;\\nvarying float v_dotRatio;\\nvarying float v_invEdgeRatio;\\nuniform highp float u_tileZoomFactor;\\nvoid main()\\n{\\nfloat dist = length(gl_PointCoord - vec2(.5, .5)) * 2.;\\nfloat alpha = smoothstep(0., 1., v_invEdgeRatio * (dist - v_dotRatio) + 1.);\\ngl_FragColor = v_color * alpha;\\n}\",\"dot.vert\":\"precision highp float;\\nattribute vec2 a_pos;\\nuniform sampler2D u_texture;\\nuniform highp mat3 u_dvsMat3;\\nuniform highp float u_tileZoomFactor;\\nuniform highp float u_dotSize;\\nuniform highp float u_pixelRatio;\\nvarying vec2 v_pos;\\nvarying vec4 v_color;\\nvarying float v_dotRatio;\\nvarying float v_invEdgeRatio;\\nconst float EPSILON = 0.000001;\\nvoid main()\\n{\\nmat3 tileToTileTexture = mat3(  1., 0., 0.,\\n0., -1., 0.,\\n0., 1., 1.  );\\nvec3 texCoords = tileToTileTexture * vec3(a_pos.xy / 512., 1.);\\nv_color = texture2D(u_texture, texCoords.xy);\\nfloat smoothEdgeWidth = max(u_dotSize / 2., 1.) ;\\nfloat z = 0.;\\nz += 2.0 * step(v_color.a, EPSILON);\\ngl_PointSize = (smoothEdgeWidth + u_dotSize);\\ngl_Position = vec4((u_dvsMat3 * vec3(a_pos + .5, 1.)).xy, z, 1.);\\nv_dotRatio = u_dotSize / gl_PointSize;\\nv_invEdgeRatio = -1. / ( smoothEdgeWidth / gl_PointSize );\\ngl_PointSize  *= (u_pixelRatio * u_tileZoomFactor);\\n}\"}},filtering:{\"bicubic.glsl\":\"vec4 computeWeights(float v) {\\nfloat b = 1.0 / 6.0;\\nfloat v2 = v * v;\\nfloat v3 = v2 * v;\\nfloat w0 = b * (-v3 + 3.0 * v2 - 3.0 * v + 1.0);\\nfloat w1 = b * (3.0 * v3  - 6.0 * v2 + 4.0);\\nfloat w2 = b * (-3.0 * v3 + 3.0 * v2 + 3.0 * v + 1.0);\\nfloat w3 = b * v3;\\nreturn vec4(w0, w1, w2, w3);\\n}\\nvec4 bicubicOffsetsAndWeights(float v) {\\nvec4 w = computeWeights(v);\\nfloat g0 = w.x + w.y;\\nfloat g1 = w.z + w.w;\\nfloat h0 = 1.0 - (w.y / g0) + v;\\nfloat h1 = 1.0 + (w.w / g1) - v;\\nreturn vec4(h0, h1, g0, g1);\\n}\\nvec4 sampleBicubicBSpline(sampler2D sampler, vec2 coords, vec2 texSize) {\\nvec2 eX = vec2(1.0 / texSize.x, 0.0);\\nvec2 eY = vec2(0.0, 1.0 / texSize.y);\\nvec2 texel = coords * texSize - 0.5;\\nvec3 hgX = bicubicOffsetsAndWeights(fract(texel).x).xyz;\\nvec3 hgY = bicubicOffsetsAndWeights(fract(texel).y).xyz;\\nvec2 coords10 = coords + hgX.x * eX;\\nvec2 coords00 = coords - hgX.y * eX;\\nvec2 coords11 = coords10 + hgY.x * eY;\\nvec2 coords01 = coords00 + hgY.x * eY;\\ncoords10 = coords10 - hgY.y * eY;\\ncoords00 = coords00 - hgY.y * eY;\\nvec4 color00 = texture2D(sampler, coords00);\\nvec4 color10 = texture2D(sampler, coords10);\\nvec4 color01 = texture2D(sampler, coords01);\\nvec4 color11 = texture2D(sampler, coords11);\\ncolor00 = mix(color00, color01, hgY.z);\\ncolor10 = mix(color10, color11, hgY.z);\\ncolor00 = mix(color00, color10, hgX.z);\\nreturn color00;\\n}\",\"bilinear.glsl\":\"vec4 sampleBilinear(sampler2D sampler, vec2 coords, vec2 texSize) {\\nvec2 texelStart = floor(coords * texSize);\\nvec2 coord0 = texelStart / texSize;\\nvec2 coord1 = (texelStart +  vec2(1.0, 0.0)) / texSize;\\nvec2 coord2 = (texelStart +  vec2(0.0, 1.0)) / texSize;\\nvec2 coord3 = (texelStart +  vec2(1.0, 1.0)) / texSize;\\nvec4 color0 = texture2D(sampler, coord0);\\nvec4 color1 = texture2D(sampler, coord1);\\nvec4 color2 = texture2D(sampler, coord2);\\nvec4 color3 = texture2D(sampler, coord3);\\nvec2 blend = fract(coords * texSize);\\nvec4 color01 = mix(color0, color1, blend.x);\\nvec4 color23 = mix(color2, color3, blend.x);\\nvec4 color = mix(color01, color23, blend.y);\\n#ifdef NNEDGE\\nfloat alpha = floor(color0.a * color1.a * color2.a * color3.a + 0.5);\\ncolor = color * alpha + (1.0 - alpha) * texture2D(sampler, coords);\\n#endif\\nreturn color;\\n}\",\"epx.glsl\":\"vec4 sampleEPX(sampler2D sampler, float size, vec2 coords, vec2 texSize) {\\nvec2 invSize = 1.0 / texSize;\\nvec2 texel = coords * texSize;\\nvec2 texel_i = floor(texel);\\nvec2 texel_frac = fract(texel);\\nvec4 colorP = texture2D(sampler, texel_i * invSize);\\nvec4 colorP1 = vec4(colorP);\\nvec4 colorP2 = vec4(colorP);\\nvec4 colorP3 = vec4(colorP);\\nvec4 colorP4 = vec4(colorP);\\nvec4 colorA = texture2D(sampler, (texel_i - vec2(0.0, 1.0)) * invSize);\\nvec4 colorB = texture2D(sampler, (texel_i + vec2(1.0, 0.0)) * invSize);\\nvec4 colorC = texture2D(sampler, (texel_i - vec2(1.0, 0.0)) * invSize);\\nvec4 colorD = texture2D(sampler, (texel_i + vec2(0.0, 1.0)) * invSize);\\nif (colorC == colorA && colorC != colorD && colorA != colorB) {\\ncolorP1 = colorA;\\n}\\nif (colorA == colorB && colorA != colorC && colorB != colorD) {\\ncolorP2 = colorB;\\n}\\nif (colorD == colorC && colorD != colorB && colorC != colorA) {\\ncolorP3 = colorC;\\n}\\nif (colorB == colorD && colorB != colorA && colorD != colorC) {\\ncolorP4 = colorD;\\n}\\nvec4 colorP12 = mix(colorP1, colorP2, texel_frac.x);\\nvec4 colorP34 = mix(colorP1, colorP2, texel_frac.x);\\nreturn mix(colorP12, colorP34, texel_frac.y);\\n}\"},fx:{integrate:{\"integrate.frag\":\"precision mediump float;\\nuniform lowp sampler2D u_sourceTexture;\\nuniform lowp sampler2D u_maskTexture;\\nuniform mediump float u_zoomLevel;\\nuniform highp float u_timeDelta;\\nuniform highp float u_animationTime;\\nvarying highp vec2 v_texcoord;\\n#include <materials/utils.glsl>\\nvoid main()\\n{\\n#ifdef DELTA\\nvec4 texel = texture2D(u_sourceTexture, v_texcoord);\\nvec4 data0 = texture2D(u_maskTexture, v_texcoord);\\nfloat flags = data0.r * 255.0;\\nfloat groupMinZoom = data0.g * 255.0;\\nfloat isVisible = getFilterBit(flags, 0);\\nfloat wouldClip = step(groupMinZoom, u_zoomLevel);\\nfloat direction = wouldClip * 1.0 + (1.0 - wouldClip) * -1.0;\\nfloat dt = u_timeDelta / max(u_animationTime, 0.0001);\\nvec4 nextState = vec4(texel + direction * dt);\\ngl_FragColor =  vec4(nextState);\\n#elif defined(UPDATE)\\nvec4 texel = texture2D(u_sourceTexture, v_texcoord);\\ngl_FragColor = texel;\\n#endif\\n}\",\"integrate.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\nvarying highp vec2 v_texcoord;\\nvoid main()\\n{\\nv_texcoord = a_pos;\\ngl_Position = vec4(a_pos * 2.0 - 1.0, 0.0, 1.0);\\n}\"}},heatmap:{heatmapResolve:{\"heatmapResolve.frag\":\"precision highp float;\\n#ifdef HEATMAP_PRECISION_HALF_FLOAT\\n#define COMPRESSION_FACTOR 4.0\\n#else\\n#define COMPRESSION_FACTOR 1.0\\n#endif\\nuniform sampler2D u_texture;\\nuniform sampler2D u_gradient;\\nuniform vec2 u_densityMinAndInvRange;\\nuniform float u_densityNormalization;\\nvarying vec2 v_uv;\\nvoid main() {\\nvec4 data = texture2D(u_texture, v_uv);\\nfloat density = data.r * COMPRESSION_FACTOR;\\ndensity *= u_densityNormalization;\\ndensity = (density - u_densityMinAndInvRange.x) * u_densityMinAndInvRange.y;\\nvec4 color = texture2D(u_gradient, vec2(density, 0.5));\\ngl_FragColor = vec4(color.rgb * color.a, color.a);\\n}\",\"heatmapResolve.vert\":\"precision highp float;\\nattribute vec2 a_pos;\\nvarying vec2 v_uv;\\nvoid main() {\\nv_uv = a_pos;\\ngl_Position = vec4(a_pos * 2.0 - 1.0, 1., 1.);\\n}\"}},highlight:{\"blur.frag\":\"varying mediump vec2 v_texcoord;\\nuniform mediump vec4 u_direction;\\nuniform mediump mat4 u_channelSelector;\\nuniform mediump float u_sigma;\\nuniform sampler2D u_texture;\\nmediump float gauss1(mediump vec2 dir) {\\nreturn exp(-dot(dir, dir) / (2.0 * u_sigma * u_sigma));\\n}\\nmediump vec4 selectChannel(mediump vec4 sample) {\\nreturn u_channelSelector * sample;\\n}\\nvoid accumGauss1(mediump float i, inout mediump float tot, inout mediump float weight) {\\nmediump float w = gauss1(i * u_direction.xy);\\ntot += selectChannel(texture2D(u_texture, v_texcoord + i * u_direction.zw))[3] * w;\\nweight += w;\\n}\\nvoid main(void) {\\nmediump float tot = 0.0;\\nmediump float weight = 0.0;\\naccumGauss1(-5.0, tot, weight);\\naccumGauss1(-4.0, tot, weight);\\naccumGauss1(-3.0, tot, weight);\\naccumGauss1(-2.0, tot, weight);\\naccumGauss1(-1.0, tot, weight);\\naccumGauss1(0.0, tot, weight);\\naccumGauss1(1.0, tot, weight);\\naccumGauss1(2.0, tot, weight);\\naccumGauss1(3.0, tot, weight);\\naccumGauss1(4.0, tot, weight);\\naccumGauss1(5.0, tot, weight);\\ngl_FragColor = vec4(0.0, 0.0, 0.0, tot / weight);\\n}\",\"highlight.frag\":\"varying mediump vec2 v_texcoord;\\nuniform sampler2D u_texture;\\nuniform mediump float u_sigma;\\nuniform sampler2D u_shade;\\nuniform mediump vec2 u_minMaxDistance;\\nmediump float estimateDistance() {\\nmediump float y = texture2D(u_texture, v_texcoord)[3];\\nconst mediump float y0 = 0.5;\\nmediump float m0 = 1.0 / (sqrt(2.0 * 3.1415) * u_sigma);\\nmediump float d = (y - y0) / m0;\\nreturn d;\\n}\\nmediump vec4 shade(mediump float d) {\\nmediump float mappedDistance = (d - u_minMaxDistance.x) / (u_minMaxDistance.y - u_minMaxDistance.x);\\nmappedDistance = clamp(mappedDistance, 0.0, 1.0);\\nreturn texture2D(u_shade, vec2(mappedDistance, 0.5));\\n}\\nvoid main(void) {\\nmediump float d = estimateDistance();\\ngl_FragColor = shade(d);\\n}\",\"textured.vert\":\"attribute mediump vec2 a_position;\\nattribute mediump vec2 a_texcoord;\\nvarying mediump vec2 v_texcoord;\\nvoid main(void) {\\ngl_Position = vec4(a_position, 0.0, 1.0);\\nv_texcoord = a_texcoord;\\n}\"},magnifier:{\"magnifier.frag\":\"uniform lowp vec4 u_background;\\nuniform mediump sampler2D u_readbackTexture;\\nuniform mediump sampler2D u_maskTexture;\\nuniform mediump sampler2D u_overlayTexture;\\nuniform bool u_maskEnabled;\\nuniform bool u_overlayEnabled;\\nvarying mediump vec2 v_texCoord;\\nconst lowp float barrelFactor = 1.1;\\nlowp vec2 barrel(lowp vec2 uv) {\\nlowp vec2 uvn = uv * 2.0 - 1.0;\\nif (uvn.x == 0.0 && uvn.y == 0.0) {\\nreturn vec2(0.5, 0.5);\\n}\\nlowp float theta = atan(uvn.y, uvn.x);\\nlowp float r = pow(length(uvn), barrelFactor);\\nreturn r * vec2(cos(theta), sin(theta)) * 0.5 + 0.5;\\n}\\nvoid main(void)\\n{\\nlowp vec4 color = texture2D(u_readbackTexture, barrel(v_texCoord));\\ncolor = (color + (1.0 - color.a) * u_background);\\nlowp float mask = u_maskEnabled ? texture2D(u_maskTexture, v_texCoord).a : 1.0;\\ncolor *= mask;\\nlowp vec4 overlayColor = u_overlayEnabled ? texture2D(u_overlayTexture, v_texCoord) : vec4(0);\\ngl_FragColor = overlayColor + (1.0 - overlayColor.a) * color;\\n}\",\"magnifier.vert\":\"precision mediump float;\\nattribute mediump vec2 a_pos;\\nuniform mediump vec4 u_drawPos;\\nvarying mediump vec2 v_texCoord;\\nvoid main(void)\\n{\\nv_texCoord = a_pos;\\ngl_Position = vec4(u_drawPos.xy + vec2(a_pos - 0.5) * u_drawPos.zw, 0.0, 1.0);\\n}\"},materials:{\"attributeData.glsl\":\"uniform highp sampler2D u_attributeData0;\\nuniform highp sampler2D u_attributeData1;\\nuniform highp sampler2D u_attributeData2;\\nuniform highp sampler2D u_attributeData3;\\nuniform highp sampler2D u_attributeData4;\\nuniform highp sampler2D u_attributeData5;\\nuniform highp int u_attributeTextureSize;\\nhighp vec2 getAttributeDataCoords(in highp vec3 id) {\\nhighp vec3  texel = unpackDisplayIdTexel(id);\\nhighp float size = float(u_attributeTextureSize);\\nhighp float u32 = float(int(texel.r) + int(texel.g) * 256 + int(texel.b) * 256 * 256);\\nhighp float col = mod(u32, size);\\nhighp float row = (u32 - col) / size;\\nhighp float u = col / size;\\nhighp float v = row / size;\\nreturn vec2(u, v);\\n}\\nhighp vec2 getAttributeDataTextureCoords(in highp vec3 id) {\\nreturn (getAttributeDataCoords(id) * 2.0) - 1.0 + (.5 / vec2(u_attributeTextureSize));\\n}\\nhighp vec4 getAttributeData0(in highp vec3 id) {\\nvec2 coords = getAttributeDataCoords(id);\\nreturn texture2D(u_attributeData0, coords);\\n}\\nhighp vec4 getAttributeData1(in highp vec3 id) {\\nhighp vec2 coords = getAttributeDataCoords(id);\\nreturn texture2D(u_attributeData1, coords);\\n}\\nhighp vec4 getAttributeData2(in highp vec3 id) {\\nhighp vec2 coords = getAttributeDataCoords(id);\\nreturn texture2D(u_attributeData2, coords);\\n}\\nhighp vec4 getAttributeData3(in highp vec3 id) {\\nhighp vec2 coords = getAttributeDataCoords(id);\\nreturn texture2D(u_attributeData3, coords);\\n}\\nhighp vec4 getAttributeData4(in highp vec3 id) {\\nhighp vec2 coords = getAttributeDataCoords(id);\\nreturn texture2D(u_attributeData4, coords);\\n}\\nhighp vec4 getAttributeData5(in highp vec3 id) {\\nhighp vec2 coords = getAttributeDataCoords(id);\\nreturn texture2D(u_attributeData5, coords);\\n}\\nfloat u88VVToFloat(in vec2 v) {\\nbool isMagic = v.x == 255.0 && v.y == 255.0;\\nif (isMagic) {\\nreturn NAN_MAGIC_NUMBER;\\n}\\nreturn (v.x + v.y * float(0x100)) - 32768.0;\\n}\",\"barycentric.glsl\":\"float inTriangle(vec3 bary) {\\nvec3 absBary = abs(bary);\\nreturn step((absBary.x + absBary.y + absBary.z), 1.05);\\n}\\nvec3 xyToBarycentric(in vec2 pos, in vec2 v0,  in vec2 v1, in vec2 v2) {\\nmat3 xyToBarycentricMat3 = mat3(\\nv1.x * v2.y - v2.x * v1.y, v2.x * v0.y - v0.x * v2.y, v0.x * v1.y - v1.x * v0.y,\\nv1.y - v2.y, v2.y - v0.y, v0.y - v1.y,\\nv2.x - v1.x, v0.x - v2.x, v1.x - v0.x\\n);\\nfloat A2 = v0.x * (v1.y - v2.y) + v1.x * (v2.y - v0.y) + v2.x * (v0.y - v1.y);\\nreturn (1. / A2) * xyToBarycentricMat3 * vec3(1., pos);\\n}\",\"constants.glsl\":\"const float C_DEG_TO_RAD = 3.14159265359 / 180.0;\\nconst float C_256_TO_RAD = 3.14159265359 / 128.0;\\nconst float C_RAD_TO_DEG = 180.0 / 3.141592654;\\nconst float POSITION_PRECISION = 1.0 / 8.0;\\nconst float FILL_POSITION_PRECISION = 1.0 / 1.0;\\nconst float SOFT_EDGE_RATIO = 1.0;\\nconst float THIN_LINE_WIDTH_FACTOR = 1.1;\\nconst float THIN_LINE_HALF_WIDTH = 1.0;\\nconst float EXTRUDE_SCALE_PLACEMENT_PADDING = 1.0 / 4.0;\\nconst float OFFSET_PRECISION = 1.0 / 8.0;\\nconst float OUTLINE_SCALE = 1.0 / 5.0;\\nconst float SDF_FONT_SIZE = 24.0;\\nconst float MAX_SDF_DISTANCE = 8.0;\\nconst float PLACEMENT_PADDING = 8.0;\\nconst float EPSILON = 0.00001;\\nconst float EPSILON_HITTEST = 0.05;\\nconst int MAX_FILTER_COUNT = 2;\\nconst int ATTR_VV_SIZE = 0;\\nconst int ATTR_VV_COLOR = 1;\\nconst int ATTR_VV_OPACITY = 2;\\nconst int ATTR_VV_ROTATION = 3;\\nconst highp float NAN_MAGIC_NUMBER = 1e-30;\\nconst int BITSET_GENERIC_LOCK_COLOR = 1;\\nconst int BITSET_GENERIC_CONSIDER_ALPHA_ONLY = 4;\\nconst int BITSET_MARKER_ALIGNMENT_MAP = 0;\\nconst int BITSET_MARKER_OUTLINE_ALLOW_COLOR_OVERRIDE = 2;\\nconst int BITSET_MARKER_SCALE_SYMBOLS_PROPORTIONALLY = 3;\\nconst int BITSET_TYPE_FILL_OUTLINE = 0;\\nconst int BITSET_FILL_RANDOM_PATTERN_OFFSET = 2;\\nconst int BITSET_FILL_HAS_UNRESOLVED_REPLACEMENT_COLOR = 3;\\nconst int BITSET_LINE_SCALE_DASH = 2;\",fill:{\"common.glsl\":\"#include <materials/symbologyTypeUtils.glsl>\\n#ifdef PATTERN\\nuniform mediump vec2 u_mosaicSize;\\nvarying mediump float v_sampleAlphaOnly;\\n#endif\\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\\nuniform lowp vec4 u_isActive[ 2 ];\\nuniform highp float u_dotValue;\\nuniform highp float u_tileDotsOverArea;\\nuniform highp float u_dotTextureDotCount;\\nuniform mediump float u_tileZoomFactor;\\n#endif\\nvarying highp vec3 v_id;\\nvarying lowp vec4 v_color;\\nvarying lowp float v_opacity;\\nvarying mediump vec4 v_aux1;\\n#ifdef PATTERN\\nvarying mediump vec2 v_tileTextureCoord;\\n#endif\\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\nvarying lowp float v_isOutline;\\n#endif\\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\\nvarying highp vec2 v_dotTextureCoords;\\nvarying highp vec4 v_dotThresholds[ 2 ];\\n#endif\",\"fill.frag\":\"precision highp float;\\n#include <materials/constants.glsl>\\n#include <materials/utils.glsl>\\n#include <materials/fill/common.glsl>\\n#ifdef PATTERN\\nuniform lowp sampler2D u_texture;\\n#endif\\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\\nuniform mediump mat4 u_dotColors[ 2 ];\\nuniform sampler2D u_dotTextures[ 2 ];\\nuniform vec4 u_dotBackgroundColor;\\n#endif\\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\n#include <materials/shared/line/common.glsl>\\n#include <materials/shared/line/line.frag>\\nlowp vec4 drawLine() {\\nfloat v_lineWidth = v_aux1.x;\\nvec2  v_normal    = v_aux1.yz;\\nLineData inputs = LineData(\\nv_color,\\nv_normal,\\nv_lineWidth,\\nv_opacity,\\nv_id\\n);\\nreturn shadeLine(inputs);\\n}\\n#endif\\nlowp vec4 drawFill() {\\nlowp vec4 out_color = vec4(0.);\\n#ifdef HITTEST\\nout_color = v_color;\\n#elif defined(PATTERN)\\nmediump vec4 v_tlbr = v_aux1;\\nmediump vec2 normalizedTextureCoord = mod(v_tileTextureCoord, 1.0);\\nmediump vec2 samplePos = mix(v_tlbr.xy, v_tlbr.zw, normalizedTextureCoord);\\nlowp vec4 color = texture2D(u_texture, samplePos);\\nif (v_sampleAlphaOnly > 0.5) {\\ncolor.rgb = vec3(color.a);\\n}\\nout_color = v_opacity * v_color * color;\\n#elif SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY && !defined(HIGHLIGHT)\\nvec4 textureThresholds0 = texture2D(u_dotTextures[0], v_dotTextureCoords);\\nvec4 textureThresholds1 = texture2D(u_dotTextures[1], v_dotTextureCoords);\\nvec4 difference0 = v_dotThresholds[0] - textureThresholds0;\\nvec4 difference1 = v_dotThresholds[1] - textureThresholds1;\\n#ifdef DD_DOT_BLENDING\\nvec4 isPositive0 = step(0.0, difference0);\\nvec4 isPositive1 = step(0.0, difference1);\\nfloat weightSum = dot(isPositive0, difference0) + dot(isPositive1, difference1);\\nfloat lessThanEqZero = step(weightSum, 0.0);\\nfloat greaterThanZero = 1.0 - lessThanEqZero ;\\nfloat divisor = (weightSum + lessThanEqZero);\\nvec4 weights0 = difference0 * isPositive0 / divisor;\\nvec4 weights1 = difference1 * isPositive1 / divisor;\\nvec4 dotColor = u_dotColors[0] * weights0 + u_dotColors[1] * weights1;\\nvec4 preEffectColor = greaterThanZero * dotColor + lessThanEqZero * u_dotBackgroundColor;\\n#else\\nfloat diffMax = max(max4(difference0), max4(difference1));\\nfloat lessThanZero = step(diffMax, 0.0);\\nfloat greaterOrEqZero = 1.0 - lessThanZero;\\nvec4 isMax0 = step(diffMax, difference0);\\nvec4 isMax1 = step(diffMax, difference1);\\nvec4 dotColor = u_dotColors[0] * isMax0 + u_dotColors[1] * isMax1;\\nvec4 preEffectColor = greaterOrEqZero * dotColor + lessThanZero * u_dotBackgroundColor;\\n#endif\\nout_color = preEffectColor;\\n#else\\nout_color = v_opacity * v_color;\\n#endif\\n#ifdef HIGHLIGHT\\nout_color.a = 1.0;\\n#endif\\nreturn out_color;\\n}\\nvoid main() {\\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\nif (v_isOutline > 0.5) {\\ngl_FragColor = drawLine();\\n} else {\\ngl_FragColor = drawFill();\\n}\\n#else\\ngl_FragColor = drawFill();\\n#endif\\n}\",\"fill.vert\":\"#include <materials/symbologyTypeUtils.glsl>\\n#define PACKED_LINE\\nprecision highp float;\\nattribute float a_bitset;\\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\\nattribute float a_inverseArea;\\nvec4 a_color = vec4(0.0, 0.0, 0.0, 1.0);\\nvec2 a_zoomRange = vec2(0.0, 10000.0);\\n#else\\nattribute vec4 a_color;\\nattribute vec4 a_aux2;\\nattribute vec4 a_aux3;\\n#ifndef SYMBOLOGY_TYPE_IS_SIMPLE_LIKE\\nattribute vec4 a_aux1;\\nattribute vec2 a_zoomRange;\\n#else\\nvec2 a_zoomRange = vec2(0.0, 10000.0);\\n#endif\\n#endif\\nuniform vec2 u_tileOffset;\\n#include <util/encoding.glsl>\\n#include <materials/vcommon.glsl>\\n#include <materials/fill/common.glsl>\\n#include <materials/fill/hittest.glsl>\\nconst float INV_SCALE_COMPRESSION_FACTOR = 1.0 / 128.0;\\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\\nvec4 dotThreshold(vec4 featureAttrOverFeatureArea, float dotValue, float tileDotsOverArea) {\\nreturn featureAttrOverFeatureArea * (1.0 / dotValue)  * (1.0 / tileDotsOverArea);\\n}\\n#endif\\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\n#include <materials/shared/line/common.glsl>\\n#include <materials/shared/line/line.vert>\\nvoid drawLine(out lowp vec4 out_color, out highp vec3 out_pos) {\\nLineData outputs = buildLine(\\nout_pos,\\na_id,\\na_pos,\\na_color,\\n(a_aux3.xy - 128.) / 16.,\\n(a_aux3.zw - 128.) / 16.,\\n0.,\\na_aux2.z / 16.,\\na_bitset,\\nvec4(0.),\\nvec2(0.),\\na_aux2.w / 16.\\n);\\nv_id      = outputs.id;\\nv_opacity = outputs.opacity;\\nv_aux1    = vec4(outputs.lineHalfWidth, outputs.normal, 0.);\\nout_color = outputs.color;\\n}\\n#endif\\nvoid drawFill(out lowp vec4 out_color, out highp vec3 out_pos) {\\nfloat a_bitSet = a_bitset;\\nout_color = getColor(a_color, a_bitSet, BITSET_GENERIC_LOCK_COLOR);\\nv_opacity = getOpacity();\\nv_id      = norm(a_id);\\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\\nmat3 tileToTileNormalized = mat3(  2. / 512.,  0.,  0.,\\n0., -2. / 512.,  0.,\\n-1.,  1.,  1.  );\\nout_pos   = tileToTileNormalized * vec3((a_pos * FILL_POSITION_PRECISION), 1.);\\n#else\\nout_pos   = u_dvsMat3 * vec3(a_pos * FILL_POSITION_PRECISION, 1.);\\n#endif\\n#ifdef PATTERN\\nvec4  a_tlbr   = a_aux1;\\nfloat a_width  = a_aux2.x;\\nfloat a_height = a_aux2.y;\\nvec2  a_offset = a_aux2.zw;\\nvec2  a_scale  = a_aux3.xy;\\nfloat a_angle  = a_aux3.z;\\nvec2 scale = INV_SCALE_COMPRESSION_FACTOR * a_scale;\\nfloat width = u_zoomFactor * a_width * scale.x;\\nfloat height = u_zoomFactor * a_height * scale.y;\\nfloat angle = C_256_TO_RAD * a_angle;\\nfloat sinA = sin(angle);\\nfloat cosA = cos(angle);\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nif (getBit(a_bitset, BITSET_FILL_RANDOM_PATTERN_OFFSET) > 0.5) {\\nfloat id = rgba2float(vec4(a_id, 0.0));\\ndx = rand(vec2(id, 0.0));\\ndy = rand(vec2(0.0, id));\\n}\\nmat3 patternMatrix = mat3(cosA / width, sinA / height, 0,\\n-sinA / width, cosA / height, 0,\\ndx,            dy,           1);\\nvec2 tileOffset = vec2(u_tileOffset.x * cosA - u_tileOffset.y * sinA, u_tileOffset.x * sinA + u_tileOffset.y * cosA);\\ntileOffset = mod(tileOffset, vec2(a_aux2.x, a_aux2.y));\\nvec2 symbolOffset = (a_offset - tileOffset) / vec2(width, height);\\nv_tileTextureCoord = (patternMatrix * vec3(a_pos * FILL_POSITION_PRECISION, 1.0)).xy - symbolOffset;\\nv_aux1 = a_tlbr / u_mosaicSize.xyxy;\\nv_sampleAlphaOnly = getBit(a_bitset, BITSET_GENERIC_CONSIDER_ALPHA_ONLY);\\nif (getBit(a_bitSet, BITSET_FILL_HAS_UNRESOLVED_REPLACEMENT_COLOR) > 0.5) {\\n#ifdef VV_COLOR\\nv_sampleAlphaOnly *= 1.0 - getBit(a_bitSet, BITSET_GENERIC_LOCK_COLOR);\\n#else\\nv_sampleAlphaOnly = 0.0;\\n#endif\\n}\\n#elif SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\\nvec4 ddAttributeData0 = getAttributeData2(a_id) * u_isActive[0] * a_inverseArea;\\nvec4 ddAttributeData1 = getAttributeData3(a_id) * u_isActive[1] * a_inverseArea;\\nfloat size = u_tileZoomFactor * 512.0 * 1.0 / u_pixelRatio;\\nv_dotThresholds[0] = dotThreshold(ddAttributeData0, u_dotValue, u_tileDotsOverArea);\\nv_dotThresholds[1] = dotThreshold(ddAttributeData1, u_dotValue, u_tileDotsOverArea);\\nv_dotTextureCoords = (a_pos * FILL_POSITION_PRECISION + 0.5) / size;\\n#endif\\n}\\n#ifdef HITTEST\\nvoid draw(out lowp vec4 out_color, out highp vec3 out_pos) {\\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\nif (getBit(a_bitset, BITSET_TYPE_FILL_OUTLINE) > 0.5) {\\nout_pos = vec3(0., 0., 2.);\\nreturn;\\n}\\n#endif\\nhittestFill(out_color, out_pos);\\ngl_PointSize = 1.0;\\n}\\n#elif defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE)\\nvoid draw(out lowp vec4 out_color, out highp vec3 out_pos) {\\nv_isOutline = getBit(a_bitset, BITSET_TYPE_FILL_OUTLINE);\\nif (v_isOutline > 0.5) {\\ndrawLine(out_color, out_pos);\\n} else {\\ndrawFill(out_color, out_pos);\\n}\\n}\\n#else\\n#define draw drawFill\\n#endif\\nvoid main()\\n{\\nINIT;\\nhighp vec3 pos  = vec3(0.);\\nhighp vec4 color  = vec4(0.);\\ndraw(color, pos);\\nv_color = color;\\ngl_Position = vec4(clip(v_color, pos, getFilterFlags(), a_zoomRange), 1.0);\\n}\",\"hittest.glsl\":\"#ifdef HITTEST\\n#include <materials/hittest/common.glsl>\\nattribute vec2 a_pos1;\\nattribute vec2 a_pos2;\\nvoid hittestFill(\\nout lowp vec4 out_color,\\nout highp vec3 out_pos\\n) {\\nvec3 pos        = u_viewMat3 * u_tileMat3 * vec3(a_pos  * FILL_POSITION_PRECISION, 1.);\\nvec3 pos1       = u_viewMat3 * u_tileMat3 * vec3(a_pos1 * FILL_POSITION_PRECISION, 1.);\\nvec3 pos2       = u_viewMat3 * u_tileMat3 * vec3(a_pos2 * FILL_POSITION_PRECISION, 1.);\\nfloat hittestDist = u_hittestDist;\\nfloat dist = distPointTriangle(u_hittestPos, pos.xy, pos1.xy, pos2.xy);\\nout_pos = vec3(getAttributeDataTextureCoords(a_id), 0.0);\\nif (dist < 0. || dist >= hittestDist) {\\nout_pos.z += 2.0;\\n}\\nout_color = vec4(1. / 255., 0, 0, dist == 0. ? (1. / 255.) : 0.);\\n}\\n#endif\"},hittest:{\"common.glsl\":\"#ifdef HITTEST\\nuniform float u_hittestDist;\\nuniform highp vec2 u_hittestPos;\\nfloat projectScalar(vec2 a, vec2 b) {\\nreturn dot(a, normalize(b));\\n}\\nfloat distPointSegment(vec2 p0, vec2 p1, vec2 p2) {\\nvec2 L = p2 - p1;\\nvec2 A = p0 - p1;\\nfloat projAL = projectScalar(A, L);\\nfloat t = clamp(projAL / length(L), 0., 1.);\\nreturn distance(p0, p1 + t * (p2 - p1));\\n}\\nvoid hittestMarker(out lowp vec4 out_color, out highp vec3 out_pos, in highp vec3 pos, float size) {\\nfloat dist = distance(pos, vec3(u_hittestPos, 1.));\\nout_pos = vec3(getAttributeDataTextureCoords(a_id), 0.0);\\nif ((dist - size) > u_hittestDist) {\\nout_pos.z += 2.0;\\n}\\nout_color = vec4(1. / 255., 0, 0, (dist - size) < 0. ? (1. / 255.) : 0.);\\n}\\nfloat intersectPointTriangleBary(vec2 p, vec2 a, vec2 b, vec2 c) {\\nreturn inTriangle(xyToBarycentric(p, a, b, c));\\n}\\nfloat distPointTriangle(vec2 p, vec2 a, vec2 b, vec2 c) {\\nvec2 ba = b - a;\\nvec2 ca = c - a;\\nfloat crossProduct = ba.x * ca.y - ca.x * ba.y;\\nbool isParallel = crossProduct < EPSILON_HITTEST && crossProduct > -EPSILON_HITTEST;\\nif (isParallel) {\\nreturn -1.;\\n}\\nif (intersectPointTriangleBary(p.xy, a, b, c) == 1.) {\\nreturn 0.;\\n}\\nfloat distAB = distPointSegment(p, a, b);\\nfloat distBC = distPointSegment(p, b, c);\\nfloat distCA = distPointSegment(p, c, a);\\nreturn min(min(distAB, distBC), distCA);\\n}\\n#endif\"},icon:{\"common.glsl\":\"#include <util/encoding.glsl>\\nuniform lowp vec2 u_mosaicSize;\\nvarying lowp vec4 v_color;\\nvarying highp vec3 v_id;\\nvarying highp vec4 v_sizeTex;\\nvarying mediump vec3 v_pos;\\nvarying lowp float v_opacity;\\nuniform lowp sampler2D u_texture;\\n#ifdef SDF\\nvarying lowp vec4 v_outlineColor;\\nvarying mediump float v_outlineWidth;\\nvarying mediump float v_distRatio;\\nvarying mediump float v_overridingOutlineColor;\\nvarying mediump float v_isThin;\\n#endif\\n#ifdef SDF\\nvec4 getColor(vec2 v_size, vec2 v_tex) {\\nlowp vec4 fillPixelColor = v_color;\\nfloat d = 0.5 - rgba2float(texture2D(u_texture, v_tex));\\nfloat size = max(v_size.x, v_size.y);\\nfloat dist = d * size * SOFT_EDGE_RATIO * v_distRatio;\\nfillPixelColor *= clamp(0.5 - dist, 0.0, 1.0);\\nfloat outlineWidth = v_outlineWidth;\\n#ifdef HIGHLIGHT\\noutlineWidth = max(outlineWidth, 4.0 * v_isThin);\\n#endif\\nif (outlineWidth > 0.25) {\\nlowp vec4 outlinePixelColor = v_overridingOutlineColor * v_color + (1.0 - v_overridingOutlineColor) * v_outlineColor;\\nfloat clampedOutlineSize = min(outlineWidth, size);\\noutlinePixelColor *= clamp(0.5 - abs(dist) + clampedOutlineSize * 0.5, 0.0, 1.0);\\nreturn v_opacity * ((1.0 - outlinePixelColor.a) * fillPixelColor + outlinePixelColor);\\n}\\nreturn v_opacity * fillPixelColor;\\n}\\n#else\\nvec4 getColor(vec2 _v_size, vec2 v_tex) {\\nlowp vec4 texColor = texture2D(u_texture, v_tex);\\nreturn v_opacity * texColor * v_color;\\n}\\n#endif\",heatmapAccumulate:{\"common.glsl\":\"varying lowp vec4 v_hittestResult;\\nvarying mediump vec2 v_offsetFromCenter;\\nvarying highp float v_fieldValue;\",\"heatmapAccumulate.frag\":\"precision mediump float;\\n#include <materials/icon/heatmapAccumulate/common.glsl>\\n#ifdef HEATMAP_PRECISION_HALF_FLOAT\\n#define COMPRESSION_FACTOR 0.25\\n#else\\n#define COMPRESSION_FACTOR 1.0\\n#endif\\nuniform lowp sampler2D u_texture;\\nvoid main() {\\n#ifdef HITTEST\\ngl_FragColor = v_hittestResult;\\n#else\\nfloat radius = length(v_offsetFromCenter);\\nfloat shapeWeight = step(radius, 1.0);\\nfloat oneMinusRadiusSquared = 1.0 - radius * radius;\\nfloat kernelWeight = oneMinusRadiusSquared * oneMinusRadiusSquared;\\ngl_FragColor = vec4(shapeWeight * kernelWeight * v_fieldValue * COMPRESSION_FACTOR);\\n#endif\\n}\",\"heatmapAccumulate.vert\":\"precision highp float;\\nattribute vec2 a_vertexOffset;\\nvec4 a_color = vec4(0.0);\\nvec2 a_zoomRange = vec2(0.0, 10000.0);\\nuniform float u_radius;\\nuniform float u_isFieldActive;\\n#include <materials/vcommon.glsl>\\n#include <materials/hittest/common.glsl>\\n#include <materials/icon/heatmapAccumulate/common.glsl>\\nvoid main() {\\nfloat filterFlags = getFilterFlags();\\n#ifdef HITTEST\\nhighp vec4 out_hittestResult = vec4(0.);\\nhighp vec3 out_pos = vec3(0.);\\nvec3 pos = u_viewMat3 * u_tileMat3 * vec3(a_pos * POSITION_PRECISION, 1.0);\\nhittestMarker(out_hittestResult, out_pos, pos, u_radius);\\nv_hittestResult = out_hittestResult;\\ngl_PointSize = 1.;\\ngl_Position = vec4(clip(a_color, out_pos, filterFlags, a_zoomRange), 1.0);\\n#else\\nv_offsetFromCenter = sign(a_vertexOffset);\\nv_fieldValue = getAttributeData2(a_id).x * u_isFieldActive + 1.0 - u_isFieldActive;\\nvec3 centerPos = u_dvsMat3 * vec3(a_pos * POSITION_PRECISION, 1.0);\\nvec3 vertexPos = centerPos + u_displayViewMat3 * vec3(v_offsetFromCenter, 0.0) * u_radius;\\ngl_Position = vec4(clip(a_color, vertexPos, filterFlags, a_zoomRange), 1.0);\\n#endif\\n}\"},\"hittest.glsl\":\"#ifdef HITTEST\\n#include <materials/hittest/common.glsl>\\nattribute vec2 a_vertexOffset1;\\nattribute vec2 a_vertexOffset2;\\nattribute vec2 a_texCoords1;\\nattribute vec2 a_texCoords2;\\nvec2 getTextureCoords(in vec3 bary, in vec2 texCoords0, in vec2 texCoords1, in vec2 texCoords2) {\\nreturn texCoords0 * bary.x + texCoords1 * bary.y + texCoords2 * bary.z;\\n}\\nvoid hittestIcon(\\ninout lowp vec4 out_color,\\nout highp vec3 out_pos,\\nin vec3 pos,\\nin vec3 offset,\\nin vec2 size,\\nin float scaleFactor,\\nin float isMapAligned\\n) {\\nout_pos = vec3(getAttributeDataTextureCoords(a_id), 0.0);\\nvec3 posBase = u_viewMat3 * u_tileMat3  * pos;\\nvec3 offset1 = scaleFactor * vec3(a_vertexOffset1 / 16.0, 0.);\\nvec3 offset2 = scaleFactor * vec3(a_vertexOffset2 / 16.0, 0.);\\nvec2 pos0    = (posBase + getMatrixNoDisplay(isMapAligned) * offset).xy;\\nvec2 pos1    = (posBase + getMatrixNoDisplay(isMapAligned) * offset1).xy;\\nvec2 pos2    = (posBase + getMatrixNoDisplay(isMapAligned) * offset2).xy;\\nvec3 bary0 = xyToBarycentric(u_hittestPos + vec2(-u_hittestDist, -u_hittestDist), pos0, pos1, pos2);\\nvec3 bary1 = xyToBarycentric(u_hittestPos + vec2(0., -u_hittestDist), pos0, pos1, pos2);\\nvec3 bary2 = xyToBarycentric(u_hittestPos + vec2(u_hittestDist, -u_hittestDist), pos0, pos1, pos2);\\nvec3 bary3 = xyToBarycentric(u_hittestPos + vec2(-u_hittestDist, 0.), pos0, pos1, pos2);\\nvec3 bary4 = xyToBarycentric(u_hittestPos, pos0, pos1, pos2);\\nvec3 bary5 = xyToBarycentric(u_hittestPos + vec2(u_hittestDist, 0.), pos0, pos1, pos2);\\nvec3 bary6 = xyToBarycentric(u_hittestPos + vec2(-u_hittestDist, u_hittestDist), pos0, pos1, pos2);\\nvec3 bary7 = xyToBarycentric(u_hittestPos + vec2(0., u_hittestDist), pos0, pos1, pos2);\\nvec3 bary8 = xyToBarycentric(u_hittestPos + vec2(u_hittestDist, u_hittestDist), pos0, pos1, pos2);\\nvec2 tex0 = a_texCoords  / u_mosaicSize;\\nvec2 tex1 = a_texCoords1 / u_mosaicSize;\\nvec2 tex2 = a_texCoords2 / u_mosaicSize;\\nfloat alphaSum = 0.;\\nalphaSum += inTriangle(bary0) * getColor(size, getTextureCoords(bary0, tex0, tex1, tex2)).a;\\nalphaSum += inTriangle(bary1) * getColor(size, getTextureCoords(bary1, tex0, tex1, tex2)).a;\\nalphaSum += inTriangle(bary2) * getColor(size, getTextureCoords(bary2, tex0, tex1, tex2)).a;\\nalphaSum += inTriangle(bary3) * getColor(size, getTextureCoords(bary3, tex0, tex1, tex2)).a;\\nalphaSum += inTriangle(bary4) * getColor(size, getTextureCoords(bary4, tex0, tex1, tex2)).a;\\nalphaSum += inTriangle(bary5) * getColor(size, getTextureCoords(bary5, tex0, tex1, tex2)).a;\\nalphaSum += inTriangle(bary6) * getColor(size, getTextureCoords(bary6, tex0, tex1, tex2)).a;\\nalphaSum += inTriangle(bary7) * getColor(size, getTextureCoords(bary7, tex0, tex1, tex2)).a;\\nout_pos.z += step(alphaSum, .05) * 2.0;\\nout_color = vec4(1. / 255., 0., 0., alphaSum / 255.);\\n}\\n#endif\",\"icon.frag\":\"precision mediump float;\\n#include <materials/constants.glsl>\\n#include <materials/utils.glsl>\\n#include <materials/icon/common.glsl>\\nvoid main()\\n{\\n#ifdef HITTEST\\nvec4 color = v_color;\\n#else\\nvec4 color = getColor(v_sizeTex.xy, v_sizeTex.zw);\\n#endif\\n#ifdef HIGHLIGHT\\ncolor.a = step(1.0 / 255.0, color.a);\\n#endif\\ngl_FragColor = color;\\n}\",\"icon.vert\":\"precision highp float;\\nattribute vec4 a_color;\\nattribute vec4 a_outlineColor;\\nattribute vec4 a_sizeAndOutlineWidth;\\nattribute vec2 a_vertexOffset;\\nattribute vec2 a_texCoords;\\nattribute vec2 a_bitSetAndDistRatio;\\nattribute vec2 a_zoomRange;\\n#include <materials/vcommon.glsl>\\n#include <materials/icon/common.glsl>\\n#include <materials/icon/hittest.glsl>\\nfloat getMarkerScaleFactor(inout vec2 size, in float referenceSize) {\\n#ifdef VV_SIZE\\nfloat f = getSize(size.y) / size.y;\\nfloat sizeFactor = size.y / referenceSize;\\nreturn getSize(referenceSize) / referenceSize;\\n#else\\nreturn 1.;\\n#endif\\n}\\nvoid main()\\n{\\nINIT;\\nfloat a_bitSet = a_bitSetAndDistRatio.x;\\nvec3  pos           = vec3(a_pos * POSITION_PRECISION, 1.0);\\nvec2  size          = a_sizeAndOutlineWidth.xy * a_sizeAndOutlineWidth.xy / 128.0;\\nvec3  offset        = vec3(a_vertexOffset / 16.0, 0.);\\nfloat outlineSize   = a_sizeAndOutlineWidth.z * a_sizeAndOutlineWidth.z / 128.0;\\nfloat isMapAligned  = getBit(a_bitSet, BITSET_MARKER_ALIGNMENT_MAP);\\nfloat referenceSize = a_sizeAndOutlineWidth.w * a_sizeAndOutlineWidth.w / 128.0;\\nfloat scaleSymbolProportionally = getBit(a_bitSet, BITSET_MARKER_SCALE_SYMBOLS_PROPORTIONALLY);\\nfloat scaleFactor               = getMarkerScaleFactor(size, referenceSize);\\nsize.xy     *= scaleFactor;\\noffset.xy   *= scaleFactor;\\noutlineSize *= scaleSymbolProportionally * (scaleFactor - 1.0) + 1.0;\\nvec2 v_tex   = a_texCoords / u_mosaicSize;\\nfloat filterFlags = getFilterFlags();\\nv_color    = getColor(a_color, a_bitSet, BITSET_GENERIC_LOCK_COLOR);\\nv_opacity  = getOpacity();\\nv_id       = norm(a_id);\\nv_pos      = u_dvsMat3 * pos + getMatrix(isMapAligned) * getRotation()  * offset;\\nv_sizeTex  = vec4(size.xy, v_tex.xy);\\n#ifdef SDF\\nv_isThin   = getBit(a_bitSet, BITSET_MARKER_OUTLINE_ALLOW_COLOR_OVERRIDE);\\n#ifdef VV_COLOR\\nv_overridingOutlineColor = v_isThin;\\n#else\\nv_overridingOutlineColor = 0.0;\\n#endif\\nv_outlineWidth = min(outlineSize, max(max(size.x, size.y) - 0.99, 0.0));\\nv_outlineColor = a_outlineColor;\\nv_distRatio = a_bitSetAndDistRatio.y / 126.0;\\n#endif\\n#ifdef HITTEST\\nhighp vec4 out_color = vec4(0.);\\nhighp vec3 out_pos   = vec3(0.);\\nhittestIcon(out_color, out_pos, pos, offset, size, scaleFactor, isMapAligned);\\nv_color = out_color;\\ngl_PointSize = 1.;\\ngl_Position = vec4(clip(v_color, out_pos, filterFlags, a_zoomRange), 1.0);\\n#else\\ngl_Position = vec4(clip(v_color, v_pos, filterFlags, a_zoomRange), 1.0);\\n#endif\\n}\"},label:{\"common.glsl\":\"uniform mediump float u_zoomLevel;\\nuniform mediump float u_mapRotation;\\nuniform mediump float u_mapAligned;\\nuniform mediump vec2 u_mosaicSize;\\nvarying mediump float v_antialiasingWidth;\\nvarying mediump float v_edgeDistanceOffset;\\nvarying mediump vec2 v_tex;\\nvarying mediump vec4 v_color;\\nvarying lowp vec4 v_animation;\",\"label.frag\":\"#include <materials/text/text.frag>\",\"label.vert\":\"precision highp float;\\n#include <materials/vcommon.glsl>\\n#include <materials/text/common.glsl>\\nattribute vec4 a_color;\\nattribute vec4 a_haloColor;\\nattribute vec4 a_texAndSize;\\nattribute vec4 a_refSymbolAndPlacementOffset;\\nattribute vec4 a_glyphData;\\nattribute vec2 a_vertexOffset;\\nattribute vec2 a_texCoords;\\nuniform float u_isHaloPass;\\nuniform float u_isBackgroundPass;\\nuniform float u_mapRotation;\\nuniform float u_mapAligned;\\nfloat getZ(in float minZoom, in float maxZoom, in float angle) {\\nfloat glyphAngle = angle * 360.0 / 254.0;\\nfloat mapAngle = u_mapRotation * 360.0 / 254.0;\\nfloat diffAngle = min(360.0 - abs(mapAngle - glyphAngle), abs(mapAngle - glyphAngle));\\nfloat z = 0.0;\\nz += u_mapAligned * (2.0 * (1.0 - step(minZoom, u_currentZoom)));\\nz += u_mapAligned * 2.0 * step(90.0, diffAngle);\\nz += 2.0 * (1.0 - step(u_currentZoom, maxZoom));\\nreturn z;\\n}\\nvoid main()\\n{\\nINIT;\\nfloat groupMinZoom    = getMinZoom();\\nfloat glyphMinZoom    = a_glyphData.x;\\nfloat glyphMaxZoom    = a_glyphData.y;\\nfloat glyphAngle      = a_glyphData.z;\\nfloat a_isBackground  = a_glyphData.w;\\nfloat a_minZoom          = max(groupMinZoom, glyphMinZoom);\\nfloat a_placementPadding = a_refSymbolAndPlacementOffset.x * EXTRUDE_SCALE_PLACEMENT_PADDING;\\nvec2  a_placementDir     = unpack_u8_nf32(a_refSymbolAndPlacementOffset.zw);\\nfloat a_refSymbolSize    = a_refSymbolAndPlacementOffset.y;\\nfloat fontSize           = a_texAndSize.z;\\nfloat haloSize           = a_texAndSize.w * OUTLINE_SCALE;\\nvec2  vertexOffset = a_vertexOffset * OFFSET_PRECISION;\\nvec3  pos          = vec3(a_pos * POSITION_PRECISION, 1.0);\\nfloat z            = getZ(a_minZoom, glyphMaxZoom, glyphAngle);\\nfloat fontScale    = fontSize / SDF_FONT_SIZE;\\nfloat halfSize     = getSize(a_refSymbolSize) / 2.0;\\nfloat animation    = pow(getAnimationState(), vec4(2.0)).r;\\nfloat isText = 1.0 - a_isBackground;\\nfloat isBackground = u_isBackgroundPass * a_isBackground;\\nvec4  nonHaloColor = (isBackground + isText) * a_color;\\nv_color     = animation * ((1.0 - u_isHaloPass) * nonHaloColor + (u_isHaloPass * a_haloColor));\\nv_opacity   = 1.0;\\nv_tex       = a_texCoords / u_mosaicSize;\\nv_edgeDistanceOffset = u_isHaloPass * haloSize / fontScale / MAX_SDF_DISTANCE;\\nv_antialiasingWidth  = 0.105 * SDF_FONT_SIZE / fontSize / u_pixelRatio;\\nvec2 placementOffset = a_placementDir * (halfSize + a_placementPadding);\\nvec3 glyphOffset     = u_displayMat3 * vec3(vertexOffset + placementOffset, 0.0);\\nvec3 v_pos           = vec3((u_dvsMat3 * pos + glyphOffset).xy, z);\\nfloat isHidden = u_isBackgroundPass * isText + (1.0 - u_isBackgroundPass) * a_isBackground;\\nv_pos.z += 2.0 * isHidden;\\ngl_Position = vec4(v_pos, 1.0);\\n#ifdef DEBUG\\nv_color = vec4(a_color.rgb, z == 0.0 ? 1.0 : 0.645);\\n#endif\\n}\"},line:{\"common.glsl\":\"varying lowp vec4 v_color;\\nvarying highp vec3 v_id;\\nvarying mediump vec2 v_normal;\\nvarying mediump float v_lineHalfWidth;\\nvarying lowp float v_opacity;\\n#ifdef PATTERN\\nvarying mediump vec4 v_tlbr;\\nvarying mediump vec2 v_patternSize;\\n#endif\\n#if defined(PATTERN) || defined(SDF)\\nvarying highp float v_accumulatedDistance;\\n#endif\\n#ifdef SDF\\nvarying mediump float v_lineWidthRatio;\\n#endif\",\"hittest.glsl\":\"#include <materials/hittest/common.glsl>\\n#ifdef HITTEST\\nattribute vec2 a_pos1;\\nattribute vec2 a_pos2;\\nvoid hittestLine(out lowp vec4 out_color, out highp vec3 out_pos, float halfWidth) {\\nvec3 pos        = u_viewMat3 * u_tileMat3 * vec3(a_pos  * POSITION_PRECISION, 1.);\\nvec3 pos1       = u_viewMat3 * u_tileMat3 * vec3(a_pos1 * POSITION_PRECISION, 1.);\\nvec3 pos2       = u_viewMat3 * u_tileMat3 * vec3(a_pos2 * POSITION_PRECISION, 1.);\\nvec3 outTextureCoords = vec3(getAttributeDataTextureCoords(a_id), 0.0);\\nfloat dist = min(distPointSegment(u_hittestPos, pos.xy, pos1.xy),\\ndistPointSegment(u_hittestPos, pos.xy, pos2.xy)) - halfWidth;\\nout_pos = vec3(getAttributeDataTextureCoords(a_id), 0.0);\\nif (dist >= u_hittestDist) {\\nout_pos.z += 2.0;\\n}\\nout_color = vec4(1. / 255., 0, 0, dist <= 0. ? (1. / 255.) : 0.);\\n}\\n#endif\",\"line.frag\":\"precision lowp float;\\n#include <util/encoding.glsl>\\n#include <materials/constants.glsl>\\n#include <materials/symbologyTypeUtils.glsl>\\n#include <materials/line/common.glsl>\\n#include <materials/shared/line/common.glsl>\\n#include <materials/shared/line/line.frag>\\n#ifdef HITTEST\\nvoid main() {\\ngl_FragColor = v_color;\\n}\\n#else\\nvoid main() {\\nLineData inputs = LineData(\\nv_color,\\nv_normal,\\nv_lineHalfWidth,\\nv_opacity,\\n#ifndef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\n#ifdef PATTERN\\nv_tlbr,\\nv_patternSize,\\n#endif\\n#ifdef SDF\\nv_lineWidthRatio,\\n#endif\\n#if defined(PATTERN) || defined(SDF)\\nv_accumulatedDistance,\\n#endif\\n#endif\\nv_id\\n);\\ngl_FragColor = shadeLine(inputs);\\n}\\n#endif\",\"line.vert\":\"precision highp float;\\nattribute vec4 a_color;\\nattribute vec4 a_offsetAndNormal;\\nattribute vec2 a_accumulatedDistanceAndHalfWidth;\\nattribute vec4 a_tlbr;\\nattribute vec4 a_segmentDirection;\\nattribute vec2 a_aux;\\nattribute vec2 a_zoomRange;\\n#include <materials/vcommon.glsl>\\n#include <materials/symbologyTypeUtils.glsl>\\n#include <materials/line/common.glsl>\\n#include <materials/line/hittest.glsl>\\n#include <materials/shared/line/common.glsl>\\n#include <materials/shared/line/line.vert>\\n#ifdef HITTEST\\nvoid draw() {\\nfloat aa        = 0.5 * u_antialiasing;\\nfloat a_halfWidth = a_accumulatedDistanceAndHalfWidth.y / 16.;\\nfloat a_cimHalfWidth = a_aux.x / 16. ;\\nvec2  a_offset = a_offsetAndNormal.xy / 16.;\\nfloat baseWidth = getBaseLineHalfWidth(a_halfWidth, a_cimHalfWidth);\\nfloat halfWidth = getLineHalfWidth(baseWidth, aa);\\nhighp vec3 pos  = vec3(0.);\\nv_color = vec4(0.);\\nhittestLine(v_color, pos, halfWidth);\\ngl_PointSize = 1.;\\ngl_Position = vec4(clip(v_color, pos, getFilterFlags(), a_zoomRange), 1.0);\\n}\\n#else\\nvoid draw()\\n{\\nhighp vec3 pos = vec3(0.);\\nLineData outputs = buildLine(\\npos,\\na_id,\\na_pos,\\na_color,\\na_offsetAndNormal.xy / 16.,\\na_offsetAndNormal.zw / 16.,\\na_accumulatedDistanceAndHalfWidth.x,\\na_accumulatedDistanceAndHalfWidth.y / 16.,\\na_segmentDirection.w,\\na_tlbr,\\na_segmentDirection.xy / 16.,\\na_aux.x / 16.\\n);\\nv_id              = outputs.id;\\nv_color           = outputs.color;\\nv_normal          = outputs.normal;\\nv_lineHalfWidth   = outputs.lineHalfWidth;\\nv_opacity         = outputs.opacity;\\n#ifndef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\n#ifdef PATTERN\\nv_tlbr          = outputs.tlbr;\\nv_patternSize   = outputs.patternSize;\\n#endif\\n#ifdef SDF\\nv_lineWidthRatio = outputs.lineWidthRatio;\\n#endif\\n#if defined(PATTERN) || defined(SDF)\\nv_accumulatedDistance = outputs.accumulatedDistance;\\n#endif\\n#endif\\ngl_Position = vec4(clip(outputs.color, pos, getFilterFlags(), a_zoomRange), 1.0);\\n}\\n#endif\\nvoid main() {\\nINIT;\\ndraw();\\n}\"},pie:{\"pie.frag\":\"precision mediump float;\\n#include <util/atan2.glsl>\\n#include <materials/constants.glsl>\\n#include <materials/utils.glsl>\\n#include <materials/icon/common.glsl>\\nvarying float v_size;\\nvarying vec2 v_offset;\\nvarying vec2 v_filteredSectorToColorId[NUMBER_OF_FIELDS];\\nvarying float v_numOfEntries;\\nvarying float v_maxSectorAngle;\\nuniform lowp vec4 u_colors[NUMBER_OF_FIELDS];\\nuniform lowp vec4 u_defaultColor;\\nuniform lowp vec4 u_othersColor;\\nuniform lowp vec4 u_outlineColor;\\nuniform float u_donutRatio;\\nuniform float u_sectorThreshold;\\nstruct FilteredChartInfo {\\nfloat endSectorAngle;\\nint colorId;\\n};\\nlowp vec4 getSectorColor(in int index, in vec2 filteredSectorToColorId[NUMBER_OF_FIELDS]) {\\n#if __VERSION__ == 300\\nmediump int colorIndex = int(filteredSectorToColorId[index].y);\\nreturn u_colors[colorIndex];\\n#else\\nmediump int colorIndex;\\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\\nif (i == index) {\\ncolorIndex = int(filteredSectorToColorId[i].y);\\n}\\n}\\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\\nif (i == colorIndex) {\\nreturn u_colors[i];\\n}\\n}\\nreturn u_colors[NUMBER_OF_FIELDS - 1];\\n#endif\\n}\\nconst int OTHER_SECTOR_ID = 255;\\n#ifdef HITTEST\\nvec4 getColor() {\\nreturn v_color;\\n}\\n#else\\nvec4 getColor() {\\nfloat angle = 90.0 - C_RAD_TO_DEG * atan2(v_offset.y, v_offset.x);\\nif (angle < 0.0) {\\nangle += 360.0;\\n} else if (angle > 360.0) {\\nangle = mod(angle, 360.0);\\n}\\nint numOfEntries = int(v_numOfEntries);\\nfloat maxSectorAngle = v_maxSectorAngle;\\nlowp vec4 fillColor = (maxSectorAngle > 0.0 || u_sectorThreshold > 0.0) ? u_othersColor : u_defaultColor;\\nlowp vec4 prevColor = vec4(0.0);\\nlowp vec4 nextColor = vec4(0.0);\\nfloat startSectorAngle = 0.0;\\nfloat endSectorAngle = 0.0;\\nif (angle < maxSectorAngle) {\\nfor (int index = 0; index < NUMBER_OF_FIELDS; ++index) {\\nstartSectorAngle = endSectorAngle;\\nendSectorAngle = v_filteredSectorToColorId[index].x;\\nif (endSectorAngle > angle) {\\nfillColor = getSectorColor(index, v_filteredSectorToColorId);\\nprevColor = u_sectorThreshold != 0.0 && index == 0 && abs(360.0 - maxSectorAngle) < EPSILON ? u_othersColor :\\ngetSectorColor(index > 0 ? index - 1 : numOfEntries - 1, v_filteredSectorToColorId);\\nnextColor = u_sectorThreshold != 0.0 && abs(endSectorAngle - maxSectorAngle) < EPSILON ? u_othersColor :\\ngetSectorColor(index < numOfEntries - 1 ? index + 1 : 0, v_filteredSectorToColorId);\\nbreak;\\n}\\nif (index == numOfEntries - 1) {\\nbreak;\\n}\\n}\\n} else {\\nprevColor = getSectorColor(numOfEntries - 1, v_filteredSectorToColorId);\\nnextColor = getSectorColor(0, v_filteredSectorToColorId);\\nstartSectorAngle = maxSectorAngle;\\nendSectorAngle = 360.0;\\n}\\nlowp vec4 outlineColor = u_outlineColor;\\nfloat offset = length(v_offset);\\nfloat distanceSize = offset * v_size;\\nif (startSectorAngle != 0.0 || endSectorAngle != 360.0) {\\nfloat distanceToStartSector = (angle - startSectorAngle);\\nfloat distanceToEndSector = (endSectorAngle - angle);\\nfloat sectorThreshold = 0.6;\\nfloat beginSectorAlpha = smoothstep(0.0, sectorThreshold, distanceToStartSector * offset);\\nfloat endSectorAlpha = smoothstep(0.0, sectorThreshold, distanceToEndSector * offset);\\nif (endSectorAlpha > 0.0) {\\nfillColor = mix(nextColor, fillColor, endSectorAlpha);\\n} else if (beginSectorAlpha > 0.0) {\\nfillColor = mix(prevColor, fillColor, beginSectorAlpha);\\n}\\n}\\nfloat donutSize = u_donutRatio * (v_size - v_outlineWidth);\\nfloat endOfDonut = donutSize - v_outlineWidth;\\nfloat aaThreshold = 0.75;\\nfloat innerCircleAlpha = endOfDonut - aaThreshold > 0.0 ? smoothstep(endOfDonut - aaThreshold, endOfDonut + aaThreshold, distanceSize) : 1.0;\\nfloat outerCircleAlpha = 1.0 - smoothstep(v_size - aaThreshold, v_size + aaThreshold , distanceSize);\\nfloat circleAlpha = innerCircleAlpha * outerCircleAlpha;\\nfloat startOfOutline = v_size - v_outlineWidth;\\nif (startOfOutline > 0.0 && v_outlineWidth > 0.25) {\\nfloat outlineFactor = smoothstep(startOfOutline - aaThreshold, startOfOutline + aaThreshold, distanceSize);\\nfloat innerLineFactor = donutSize - aaThreshold > 0.0 ? 1.0 - smoothstep(donutSize - aaThreshold, donutSize + aaThreshold , distanceSize) : 0.0;\\nfillColor = mix(fillColor, outlineColor, innerLineFactor + outlineFactor);\\n}\\nreturn v_opacity * circleAlpha * fillColor;\\n}\\n#endif\\nvoid main()\\n{\\nvec4 color = getColor();\\n#ifdef HIGHLIGHT\\ncolor.a = step(1.0 / 255.0, color.a);\\n#endif\\ngl_FragColor = color;\\n}\",\"pie.vert\":\"precision highp float;\\nattribute vec4 a_color;\\nattribute vec4 a_outlineColor;\\nattribute vec4 a_sizeAndOutlineWidth;\\nattribute vec2 a_vertexOffset;\\nattribute vec2 a_texCoords;\\nattribute vec2 a_bitSetAndDistRatio;\\nattribute vec2 a_zoomRange;\\nuniform float u_outlineWidth;\\nuniform mediump float u_sectorThreshold;\\nvarying float v_size;\\nvarying vec2 v_offset;\\nvarying vec2 v_filteredSectorToColorId[NUMBER_OF_FIELDS];\\nvarying float v_numOfEntries;\\nvarying float v_maxSectorAngle;\\nstruct FilteredChartInfo {\\nfloat endSectorAngle;\\nint colorId;\\n};\\nint filter(in float sectorAngle,\\nin int currentIndex,\\ninout FilteredChartInfo filteredInfo,\\ninout vec2 filteredSectorToColorId[NUMBER_OF_FIELDS]) {\\nif (sectorAngle > u_sectorThreshold * 360.0) {\\nfilteredInfo.endSectorAngle += sectorAngle;\\n#if __VERSION__ == 300\\nfilteredSectorToColorId[filteredInfo.colorId] = vec2(filteredInfo.endSectorAngle, currentIndex);\\n#else\\nfor (int i = 0; i < NUMBER_OF_FIELDS; i++) {\\nif (i == filteredInfo.colorId) {\\nfilteredSectorToColorId[i] = vec2(filteredInfo.endSectorAngle, currentIndex);\\n}\\n}\\n#endif\\n++filteredInfo.colorId;\\n}\\nreturn 0;\\n}\\nint filterValues(inout vec2 filteredSectorToColorId[NUMBER_OF_FIELDS],\\ninout FilteredChartInfo filteredInfo,\\nin float sectorAngles[NUMBER_OF_FIELDS]) {\\nfor (int index = 0; index < NUMBER_OF_FIELDS; ++index) {\\nfloat sectorValue = sectorAngles[index];\\nfilter(sectorValue, index, filteredInfo, filteredSectorToColorId);\\n}\\nreturn filteredInfo.colorId;\\n}\\n#include <materials/vcommon.glsl>\\n#include <materials/icon/common.glsl>\\n#include <materials/hittest/common.glsl>\\nvec2 getMarkerSize(inout vec2 offset, inout vec2 baseSize, inout float outlineSize, in float referenceSize, in float bitSet) {\\nvec2 outSize = baseSize;\\n#ifdef VV_SIZE\\nfloat r = 0.5 * getSize(referenceSize) / referenceSize;\\noutSize.xy *= r;\\noffset.xy *= r;\\nfloat scaleSymbolProportionally = getBit(bitSet, BITSET_MARKER_SCALE_SYMBOLS_PROPORTIONALLY);\\noutlineSize *= scaleSymbolProportionally * (r - 1.0) + 1.0;\\n#endif\\nreturn outSize;\\n}\\nvec3 getOffset(in vec2 in_offset, float a_bitSet) {\\nfloat isMapAligned = getBit(a_bitSet, BITSET_MARKER_ALIGNMENT_MAP);\\nvec3  offset       = vec3(in_offset, 0.0);\\nreturn getMatrix(isMapAligned) * offset;\\n}\\nfloat filterNaNValues(in float value) {\\nreturn value != NAN_MAGIC_NUMBER && value > 0.0 ? value : 0.0;\\n}\\nvoid main()\\n{\\nINIT;\\nvec2  a_size   = a_sizeAndOutlineWidth.xy * a_sizeAndOutlineWidth.xy / 128.0;\\nvec2  a_offset = a_vertexOffset / 16.0;\\nfloat outlineSize = u_outlineWidth;\\nfloat a_bitSet = a_bitSetAndDistRatio.x;\\nvec2 size = getMarkerSize(a_offset, a_size, outlineSize, a_sizeAndOutlineWidth.w * a_sizeAndOutlineWidth.w / 128.0, a_bitSet);\\nfloat filterFlags = getFilterFlags();\\nvec3  pos         = vec3(a_pos * POSITION_PRECISION, 1.0);\\nv_opacity      = getOpacity();\\nv_id           = norm(a_id);\\nv_pos          = u_dvsMat3 * pos + getOffset(a_offset, a_bitSet);\\nv_offset       = sign(a_texCoords - 0.5);\\nv_size         = max(size.x, size.y);\\nv_outlineWidth = outlineSize;\\nfloat attributeData[10];\\nvec4 attributeData0 = getAttributeData3(a_id);\\nattributeData[0] = filterNaNValues(attributeData0.x);\\nattributeData[1] = filterNaNValues(attributeData0.y);\\nattributeData[2] = filterNaNValues(attributeData0.z);\\nattributeData[3] = filterNaNValues(attributeData0.w);\\n#if (NUMBER_OF_FIELDS > 4)\\nvec4 attributeData1 = getAttributeData4(a_id);\\nattributeData[4] = filterNaNValues(attributeData1.x);\\nattributeData[5] = filterNaNValues(attributeData1.y);\\nattributeData[6] = filterNaNValues(attributeData1.z);\\nattributeData[7] = filterNaNValues(attributeData1.w);\\n#endif\\n#if (NUMBER_OF_FIELDS > 8)\\nvec4 attributeData2 = getAttributeData5(a_id);\\nattributeData[8] = filterNaNValues(attributeData2.x);\\nattributeData[9] = filterNaNValues(attributeData2.y);\\n#endif\\nfloat sum = 0.0;\\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\\nsum += attributeData[i];\\n}\\nfloat sectorAngles[NUMBER_OF_FIELDS];\\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\\nsectorAngles[i] = 360.0 * attributeData[i] / sum;\\n}\\nvec2 filteredSectorToColorId[NUMBER_OF_FIELDS];\\nFilteredChartInfo filteredInfo = FilteredChartInfo(0.0, 0);\\nint numOfEntries = filterValues(filteredSectorToColorId, filteredInfo, sectorAngles);\\nv_numOfEntries = float(numOfEntries);\\nv_maxSectorAngle = filteredInfo.endSectorAngle;\\n#if __VERSION__ == 300\\nv_filteredSectorToColorId = filteredSectorToColorId;\\n#else\\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\\nif (i == numOfEntries) {\\nbreak;\\n}\\nv_filteredSectorToColorId[i] = filteredSectorToColorId[i];\\n}\\n#endif\\n#ifdef HITTEST\\nhighp vec3 out_pos = vec3(0.0);\\nv_color            = vec4(0.0);\\nhittestMarker(v_color, out_pos, u_viewMat3 * u_tileMat3 *  pos, v_size);\\ngl_PointSize = 1.0;\\ngl_Position = vec4(clip(v_color, out_pos, filterFlags, a_zoomRange), 1.0);\\n#else\\ngl_Position = vec4(clip(v_color, v_pos, filterFlags, a_zoomRange), 1.0);\\n#endif\\n}\"},shared:{line:{\"common.glsl\":\"#if !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE) && defined(PATTERN)\\nuniform mediump vec2 u_mosaicSize;\\nvarying mediump float v_sampleAlphaOnly;\\n#endif\\nstruct LineData {\\nlowp vec4 color;\\nmediump vec2 normal;\\nmediump float lineHalfWidth;\\nlowp float opacity;\\n#ifndef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\n#ifdef PATTERN\\nmediump vec4 tlbr;\\nmediump vec2 patternSize;\\n#endif\\n#ifdef SDF\\nmediump float lineWidthRatio;\\n#endif\\n#if defined(PATTERN) || defined(SDF)\\nhighp float accumulatedDistance;\\n#endif\\n#endif\\nhighp vec3 id;\\n};\",\"line.frag\":\"uniform lowp float u_blur;\\n#if !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE) && !defined(HIGHLIGHT)\\n#if defined(PATTERN) || defined(SDF)\\nuniform sampler2D u_texture;\\nuniform highp float u_pixelRatio;\\n#endif\\n#endif\\n#if defined(SDF) && !defined(HIGHLIGHT) && !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE)\\nlowp vec4 getLineColor(LineData line) {\\nmediump float adjustedPatternWidth = line.patternSize.x * 2.0 * line.lineWidthRatio;\\nmediump float relativeTexX = fract(line.accumulatedDistance / adjustedPatternWidth);\\nmediump float relativeTexY = 0.5 + 0.25 * line.normal.y;\\nmediump vec2 texCoord = mix(line.tlbr.xy, line.tlbr.zw, vec2(relativeTexX, relativeTexY));\\nmediump float d = rgba2float(texture2D(u_texture, texCoord)) - 0.5;\\nfloat dist = d * line.lineHalfWidth;\\nreturn line.opacity * clamp(0.5 - dist, 0.0, 1.0) * line.color;\\n}\\n#elif defined(PATTERN) && !defined(HIGHLIGHT) && !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE)\\nlowp vec4 getLineColor(LineData line) {\\nmediump float lineHalfWidth = line.lineHalfWidth;\\nmediump float adjustedPatternHeight = line.patternSize.y * 2.0 * lineHalfWidth / line.patternSize.x;\\nmediump float relativeTexY = fract(line.accumulatedDistance / adjustedPatternHeight);\\nmediump float relativeTexX = 0.5 + 0.5 * line.normal.y;\\nmediump vec2 texCoord = mix(line.tlbr.xy, line.tlbr.zw, vec2(relativeTexX, relativeTexY));\\nlowp vec4 color = texture2D(u_texture, texCoord);\\n#ifdef VV_COLOR\\nif (v_sampleAlphaOnly > 0.5) {\\ncolor.rgb = vec3(color.a);\\n}\\n#endif\\nreturn line.opacity * line.color * color;\\n}\\n#else\\nlowp vec4 getLineColor(LineData line) {\\nreturn line.opacity * line.color;\\n}\\n#endif\\nvec4 shadeLine(LineData line)\\n{\\nmediump float thinLineFactor = max(THIN_LINE_WIDTH_FACTOR * step(line.lineHalfWidth, THIN_LINE_HALF_WIDTH), 1.0);\\nmediump float fragDist = length(line.normal) * line.lineHalfWidth;\\nlowp float alpha = clamp(thinLineFactor * (line.lineHalfWidth - fragDist) / (u_blur + thinLineFactor - 1.0), 0.0, 1.0);\\nlowp vec4 out_color = getLineColor(line) * alpha;\\n#ifdef HIGHLIGHT\\nout_color.a = step(1.0 / 255.0, out_color.a);\\n#endif\\n#ifdef ID\\nif (out_color.a < 1.0 / 255.0) {\\ndiscard;\\n}\\nout_color = vec4(line.id, 0.0);\\n#endif\\nreturn out_color;\\n}\",\"line.vert\":\"float getBaseLineHalfWidth(in float lineHalfWidth, in float referenceHalfWidth) {\\n#ifdef VV_SIZE\\nfloat refLineWidth = 2.0 * referenceHalfWidth;\\nreturn 0.5 * (lineHalfWidth / max(referenceHalfWidth, EPSILON)) * getSize(refLineWidth);\\n#else\\nreturn lineHalfWidth;\\n#endif\\n}\\nfloat getLineHalfWidth(in float baseWidth, in float aa) {\\nfloat halfWidth = max(baseWidth + aa, 0.45) + 0.1 * aa;\\n#ifdef HIGHLIGHT\\nhalfWidth = max(halfWidth, 2.0);\\n#endif\\nreturn halfWidth;\\n}\\nvec2 getDist(in vec2 offset, in float halfWidth) {\\nfloat thinLineFactor = max(THIN_LINE_WIDTH_FACTOR * step(halfWidth, THIN_LINE_HALF_WIDTH), 1.0);\\nreturn thinLineFactor * halfWidth * offset;\\n}\\nLineData buildLine(\\nout vec3 out_pos,\\nin vec3 in_id,\\nin vec2 in_pos,\\nin vec4 in_color,\\nin vec2 in_offset,\\nin vec2 in_normal,\\nin float in_accumulatedDist,\\nin float in_lineHalfWidth,\\nin float in_bitSet,\\nin vec4 in_tlbr,\\nin vec2 in_segmentDirection,\\nin float in_referenceHalfWidth\\n)\\n{\\nfloat aa        = 0.5 * u_antialiasing;\\nfloat baseWidth = getBaseLineHalfWidth(in_lineHalfWidth, in_referenceHalfWidth);\\nfloat halfWidth = getLineHalfWidth(baseWidth, aa);\\nfloat z         = 2.0 * step(baseWidth, 0.0);\\nvec2  dist      = getDist(in_offset, halfWidth);\\nvec3  offset    = u_displayViewMat3 * vec3(dist, 0.0);\\nvec3  pos       = u_dvsMat3 * vec3(in_pos * POSITION_PRECISION, 1.0) + offset;\\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\nvec4  color     = in_color;\\nfloat opacity   = 1.0;\\n#else\\nvec4  color     = getColor(in_color, in_bitSet, BITSET_GENERIC_LOCK_COLOR);\\nfloat opacity   = getOpacity();\\n#ifdef SDF\\nconst float SDF_PATTERN_HALF_WIDTH = 15.5;\\nfloat scaleDash = getBit(in_bitSet, BITSET_LINE_SCALE_DASH);\\nfloat lineWidthRatio = (scaleDash * max(halfWidth - 0.55 * u_antialiasing, 0.25) + (1.0 - scaleDash)) / SDF_PATTERN_HALF_WIDTH;\\n#endif\\n#endif\\n#if !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE) && defined(PATTERN)\\nv_sampleAlphaOnly = getBit(in_bitSet, BITSET_GENERIC_CONSIDER_ALPHA_ONLY);\\n#endif\\nout_pos = vec3(pos.xy, z);\\nreturn LineData(\\ncolor,\\nin_normal,\\nhalfWidth,\\nopacity,\\n#ifndef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\n#ifdef PATTERN\\nin_tlbr / u_mosaicSize.xyxy,\\nvec2(in_tlbr.z - in_tlbr.x, in_tlbr.w - in_tlbr.y),\\n#endif\\n#ifdef SDF\\nlineWidthRatio,\\n#endif\\n#if defined(PATTERN) || defined(SDF)\\nin_accumulatedDist * u_zoomFactor + dot(in_segmentDirection, dist),\\n#endif\\n#endif\\nnorm(in_id)\\n);\\n}\"}},\"symbologyTypeUtils.glsl\":\"#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_OUTLINE_FILL || SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_OUTLINE_FILL_SIMPLE\\n#define SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\\n#endif\\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_SIMPLE || SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_OUTLINE_FILL_SIMPLE\\n#define SYMBOLOGY_TYPE_IS_SIMPLE_LIKE\\n#endif\",text:{\"common.glsl\":\"uniform highp vec2 u_mosaicSize;\\nvarying highp vec3 v_id;\\nvarying mediump vec3 v_pos;\\nvarying lowp float v_opacity;\\nvarying lowp vec4 v_color;\\nvarying highp vec2 v_tex;\\nvarying mediump float v_antialiasingWidth;\\nvarying mediump float v_edgeDistanceOffset;\\nvarying lowp float v_transparency;\",\"hittest.glsl\":\"#include <materials/hittest/common.glsl>\",\"text.frag\":\"precision mediump float;\\n#include <materials/text/common.glsl>\\nuniform lowp sampler2D u_texture;\\n#ifdef HITTEST\\nvec4 getColor() {\\nreturn v_color;\\n}\\n#else\\nvec4 getColor()\\n{\\nfloat SDF_CUTOFF = (2.0 / 8.0);\\nfloat SDF_BASE_EDGE_DIST = 1.0 - SDF_CUTOFF;\\nlowp float dist = texture2D(u_texture, v_tex).a;\\nmediump float edge = SDF_BASE_EDGE_DIST - v_edgeDistanceOffset;\\n#ifdef HIGHLIGHT\\nedge /= 2.0;\\n#endif\\nlowp float aa = v_antialiasingWidth;\\nlowp float alpha = smoothstep(edge - aa, edge + aa, dist);\\nreturn alpha * v_color * v_opacity;\\n}\\n#endif\\nvoid main()\\n{\\ngl_FragColor = getColor();\\n}\",\"text.vert\":\"precision highp float;\\n#include <materials/utils.glsl>\\n#include <materials/vcommon.glsl>\\n#include <materials/text/common.glsl>\\n#include <materials/text/hittest.glsl>\\nattribute vec4 a_color;\\nattribute vec4 a_haloColor;\\nattribute vec4 a_texFontSize;\\nattribute vec4 a_aux;\\nattribute vec2 a_zoomRange;\\nattribute vec2 a_vertexOffset;\\nattribute vec2 a_texCoords;\\nuniform float u_isHaloPass;\\nuniform float u_isBackgroundPass;\\nfloat getTextSize(inout vec2 offset, inout float baseSize, in float referenceSize) {\\n#ifdef VV_SIZE\\nfloat r = getSize(referenceSize) / referenceSize;\\nbaseSize *= r;\\noffset.xy *= r;\\nreturn baseSize;\\n#endif\\nreturn baseSize;\\n}\\nvoid main()\\n{\\nINIT;\\nfloat a_isBackground  = a_aux.y;\\nfloat a_referenceSize = a_aux.z * a_aux.z / 256.0;\\nfloat a_bitSet        = a_aux.w;\\nfloat a_fontSize      = a_texFontSize.z;\\nvec2  a_offset        = a_vertexOffset * OFFSET_PRECISION;\\nvec3  in_pos        = vec3(a_pos * POSITION_PRECISION, 1.0);\\nfloat fontSize      = getTextSize(a_offset, a_fontSize, a_referenceSize);\\nfloat fontScale     = fontSize / SDF_FONT_SIZE;\\nvec3  offset        = getRotation() * vec3(a_offset, 0.0);\\nmat3  extrudeMatrix = getBit(a_bitSet, 0) == 1.0 ? u_displayViewMat3 : u_displayMat3;\\nfloat isText = 1.0 - a_isBackground;\\nfloat isBackground = u_isBackgroundPass * a_isBackground;\\nvec4  nonHaloColor  = (isBackground * a_color) + (isText * getColor(a_color, a_bitSet, 1));\\nv_color   = u_isHaloPass * a_haloColor + (1.0 - u_isHaloPass) * nonHaloColor;\\nv_opacity = getOpacity();\\nv_id      = norm(a_id);\\nv_tex     = a_texCoords / u_mosaicSize;\\nv_pos     = u_dvsMat3 * in_pos + extrudeMatrix * offset;\\nfloat isHidden = u_isBackgroundPass * isText + (1.0 - u_isBackgroundPass) * a_isBackground;\\nv_pos.z += 2.0 * isHidden;\\nv_edgeDistanceOffset = u_isHaloPass * OUTLINE_SCALE * a_texFontSize.w / fontScale / MAX_SDF_DISTANCE;\\nv_antialiasingWidth  = 0.105 * SDF_FONT_SIZE / fontSize / u_pixelRatio;\\n#ifdef HITTEST\\nhighp vec3 out_pos  = vec3(0.);\\nv_color = vec4(0.);\\nhittestMarker(v_color, out_pos, u_viewMat3 * u_tileMat3 *  vec3(a_pos * POSITION_PRECISION, 1.0)\\n+ u_tileMat3 * offset, fontSize / 2.);\\ngl_PointSize = 1.;\\ngl_Position = vec4(clip(v_color, out_pos, getFilterFlags(), a_zoomRange), 1.0);\\n#else\\ngl_Position =  vec4(clip(v_color, v_pos, getFilterFlags(), a_zoomRange), 1.0);\\n#endif\\n}\"},\"utils.glsl\":\"float rshift(in float u32, in int amount) {\\nreturn floor(u32 / pow(2.0, float(amount)));\\n}\\nfloat getBit(in float bitset, in int bitIndex) {\\nfloat offset = pow(2.0, float(bitIndex));\\nreturn mod(floor(bitset / offset), 2.0);\\n}\\nfloat getFilterBit(in float bitset, in int bitIndex) {\\nreturn getBit(bitset, bitIndex + 1);\\n}\\nfloat getHighlightBit(in float bitset) {\\nreturn getBit(bitset, 0);\\n}\\nhighp vec3 unpackDisplayIdTexel(in highp vec3 bitset) {\\nfloat isAggregate = getBit(bitset.b, 7);\\nreturn (1.0 - isAggregate) * bitset + isAggregate * (vec3(bitset.rgb) - vec3(0.0, 0.0, float(0x80)));\\n}\\nvec4 unpack(in float u32) {\\nfloat r = mod(rshift(u32, 0), 255.0);\\nfloat g = mod(rshift(u32, 8), 255.0);\\nfloat b = mod(rshift(u32, 16), 255.0);\\nfloat a = mod(rshift(u32, 24), 255.0);\\nreturn vec4(r, g, b, a);\\n}\\nvec3 norm(in vec3 v) {\\nreturn v /= 255.0;\\n}\\nvec4 norm(in vec4 v) {\\nreturn v /= 255.0;\\n}\\nfloat max4(vec4 target) {\\nreturn max(max(max(target.x, target.y), target.z), target.w);\\n}\\nvec2 unpack_u8_nf32(vec2 bytes) {\\nreturn (bytes - 127.0) / 127.0;\\n}\\nhighp float rand(in vec2 co) {\\nhighp float a = 12.9898;\\nhighp float b = 78.233;\\nhighp float c = 43758.5453;\\nhighp float dt = dot(co, vec2(a,b));\\nhighp float sn = mod(dt, 3.14);\\nreturn fract(sin(sn) * c);\\n}\",\"vcommon.glsl\":\"#include <materials/constants.glsl>\\n#include <materials/utils.glsl>\\n#include <materials/attributeData.glsl>\\n#include <materials/vv.glsl>\\n#include <materials/barycentric.glsl>\\nattribute vec2 a_pos;\\nattribute highp vec3 a_id;\\nuniform highp mat3 u_dvsMat3;\\nuniform highp mat3 u_displayMat3;\\nuniform highp mat3 u_displayViewMat3;\\nuniform highp mat3 u_tileMat3;\\nuniform highp mat3 u_viewMat3;\\nuniform highp float u_pixelRatio;\\nuniform mediump float u_zoomFactor;\\nuniform mediump float u_antialiasing;\\nuniform mediump float u_currentZoom;\\nvec4 VV_ADATA = vec4(0.0);\\nvoid loadVisualVariableData(inout vec4 target) {\\n#ifdef SUPPORTS_TEXTURE_FLOAT\\ntarget.rgba = getAttributeData2(a_id);\\n#else\\nvec4 data0 = getAttributeData2(a_id);\\nvec4 data1 = getAttributeData3(a_id);\\ntarget.r = u88VVToFloat(data0.rg * 255.0);\\ntarget.g = u88VVToFloat(data0.ba * 255.0);\\ntarget.b = u88VVToFloat(data1.rg * 255.0);\\ntarget.a = u88VVToFloat(data1.ba * 255.0);\\n#endif\\n}\\n#ifdef VV\\n#define INIT loadVisualVariableData(VV_ADATA)\\n#else\\n#define INIT\\n#endif\\nvec4 getColor(in vec4 a_color, in float a_bitSet, int index) {\\n#ifdef VV_COLOR\\nfloat isColorLocked   = getBit(a_bitSet, index);\\nreturn getVVColor(VV_ADATA[ATTR_VV_COLOR], a_color, isColorLocked);\\n#else\\nreturn a_color;\\n#endif\\n}\\nfloat getOpacity() {\\n#ifdef VV_OPACITY\\nreturn getVVOpacity(VV_ADATA[ATTR_VV_OPACITY]);\\n#else\\nreturn 1.0;\\n#endif\\n}\\nfloat getSize(in float in_size) {\\n#ifdef VV_SIZE\\nreturn getVVSize(in_size, VV_ADATA[ATTR_VV_SIZE]);\\n#else\\nreturn in_size;\\n#endif\\n}\\nmat3 getRotation() {\\n#ifdef VV_ROTATION\\nreturn getVVRotationMat3(mod(VV_ADATA[ATTR_VV_ROTATION], 360.0));\\n#else\\nreturn mat3(1.0);\\n#endif\\n}\\nfloat getFilterFlags() {\\n#ifdef IGNORES_SAMPLER_PRECISION\\nreturn ceil(getAttributeData0(a_id).x * 255.0);\\n#else\\nreturn getAttributeData0(a_id).x * 255.0;\\n#endif\\n}\\nvec4 getAnimationState() {\\nreturn getAttributeData1(a_id);\\n}\\nfloat getMinZoom() {\\nvec4 data0 = getAttributeData0(a_id) * 255.0;\\nreturn data0.g;\\n}\\nmat3 getMatrixNoDisplay(float isMapAligned) {\\nreturn isMapAligned * u_viewMat3 * u_tileMat3 + (1.0 - isMapAligned) * u_tileMat3;\\n}\\nmat3 getMatrix(float isMapAligned) {\\nreturn isMapAligned * u_displayViewMat3 + (1.0 - isMapAligned) * u_displayMat3;\\n}\\nvec3 clip(inout vec4 color, inout vec3 pos, in float filterFlags, in vec2 minMaxZoom) {\\npos.z += 2.0 * (1.0 - getFilterBit(filterFlags, 0));\\n#ifdef INSIDE\\npos.z += 2.0 * (1.0 - getFilterBit(filterFlags, 1));\\n#elif defined(OUTSIDE)\\npos.z += 2.0 * getFilterBit(filterFlags, 1);\\n#elif defined(HIGHLIGHT)\\n#if !defined(HIGHLIGHT_ALL)\\npos.z += 2.0 * (1.0 - getHighlightBit(filterFlags));\\n#endif\\n#endif\\npos.z += 2.0 * (step(minMaxZoom.y, u_currentZoom) + (1.0 - step(minMaxZoom.x, u_currentZoom)));\\nreturn pos;\\n}\",\"vv.glsl\":\"#if defined(VV_SIZE_MIN_MAX_VALUE) || defined(VV_SIZE_SCALE_STOPS) || defined(VV_SIZE_FIELD_STOPS) || defined(VV_SIZE_UNIT_VALUE)\\n#define VV_SIZE\\n#endif\\n#if defined(VV_COLOR) || defined(VV_SIZE) || defined(VV_OPACITY) || defined(VV_ROTATION)\\n#define VV\\n#endif\\n#ifdef VV_COLOR\\nuniform highp float u_vvColorValues[8];\\nuniform vec4 u_vvColors[8];\\n#endif\\n#ifdef VV_SIZE_MIN_MAX_VALUE\\nuniform highp vec4 u_vvSizeMinMaxValue;\\n#endif\\n#ifdef VV_SIZE_SCALE_STOPS\\nuniform highp float u_vvSizeScaleStopsValue;\\n#endif\\n#ifdef VV_SIZE_FIELD_STOPS\\nuniform highp float u_vvSizeFieldStopsValues[6];\\nuniform float u_vvSizeFieldStopsSizes[6];\\n#endif\\n#ifdef VV_SIZE_UNIT_VALUE\\nuniform highp float u_vvSizeUnitValueWorldToPixelsRatio;\\n#endif\\n#ifdef VV_OPACITY\\nuniform highp float u_vvOpacityValues[8];\\nuniform float u_vvOpacities[8];\\n#endif\\n#ifdef VV_ROTATION\\nuniform lowp float u_vvRotationType;\\n#endif\\nbool isNan(float val) {\\nreturn (val == NAN_MAGIC_NUMBER);\\n}\\n#ifdef VV_SIZE_MIN_MAX_VALUE\\nfloat getVVMinMaxSize(float sizeValue, float fallback) {\\nif (isNan(sizeValue)) {\\nreturn fallback;\\n}\\nfloat interpolationRatio = (sizeValue  - u_vvSizeMinMaxValue.x) / (u_vvSizeMinMaxValue.y - u_vvSizeMinMaxValue.x);\\ninterpolationRatio = clamp(interpolationRatio, 0.0, 1.0);\\nreturn u_vvSizeMinMaxValue.z + interpolationRatio * (u_vvSizeMinMaxValue.w - u_vvSizeMinMaxValue.z);\\n}\\n#endif\\n#ifdef VV_SIZE_FIELD_STOPS\\nconst int VV_SIZE_N = 6;\\nfloat getVVStopsSize(float sizeValue, float fallback) {\\nif (isNan(sizeValue)) {\\nreturn fallback;\\n}\\nif (sizeValue <= u_vvSizeFieldStopsValues[0]) {\\nreturn u_vvSizeFieldStopsSizes[0];\\n}\\nfor (int i = 1; i < VV_SIZE_N; ++i) {\\nif (u_vvSizeFieldStopsValues[i] >= sizeValue) {\\nfloat f = (sizeValue - u_vvSizeFieldStopsValues[i-1]) / (u_vvSizeFieldStopsValues[i] - u_vvSizeFieldStopsValues[i-1]);\\nreturn mix(u_vvSizeFieldStopsSizes[i-1], u_vvSizeFieldStopsSizes[i], f);\\n}\\n}\\nreturn u_vvSizeFieldStopsSizes[VV_SIZE_N - 1];\\n}\\n#endif\\n#ifdef VV_SIZE_UNIT_VALUE\\nfloat getVVUnitValue(float sizeValue, float fallback) {\\nif (isNan(sizeValue)) {\\nreturn fallback;\\n}\\nreturn u_vvSizeUnitValueWorldToPixelsRatio * sizeValue;\\n}\\n#endif\\n#ifdef VV_OPACITY\\nconst int VV_OPACITY_N = 8;\\nfloat getVVOpacity(float opacityValue) {\\nif (isNan(opacityValue)) {\\nreturn 1.0;\\n}\\nif (opacityValue <= u_vvOpacityValues[0]) {\\nreturn u_vvOpacities[0];\\n}\\nfor (int i = 1; i < VV_OPACITY_N; ++i) {\\nif (u_vvOpacityValues[i] >= opacityValue) {\\nfloat f = (opacityValue - u_vvOpacityValues[i-1]) / (u_vvOpacityValues[i] - u_vvOpacityValues[i-1]);\\nreturn mix(u_vvOpacities[i-1], u_vvOpacities[i], f);\\n}\\n}\\nreturn u_vvOpacities[VV_OPACITY_N - 1];\\n}\\n#endif\\n#ifdef VV_ROTATION\\nmat4 getVVRotation(float rotationValue) {\\nif (isNan(rotationValue)) {\\nreturn mat4(1, 0, 0, 0,\\n0, 1, 0, 0,\\n0, 0, 1, 0,\\n0, 0, 0, 1);\\n}\\nfloat rotation = rotationValue;\\nif (u_vvRotationType == 1.0) {\\nrotation = 90.0 - rotation;\\n}\\nfloat angle = C_DEG_TO_RAD * rotation;\\nfloat sinA = sin(angle);\\nfloat cosA = cos(angle);\\nreturn mat4(cosA, sinA, 0, 0,\\n-sinA,  cosA, 0, 0,\\n0,     0, 1, 0,\\n0,     0, 0, 1);\\n}\\nmat3 getVVRotationMat3(float rotationValue) {\\nif (isNan(rotationValue)) {\\nreturn mat3(1, 0, 0,\\n0, 1, 0,\\n0, 0, 1);\\n}\\nfloat rotation = rotationValue;\\nif (u_vvRotationType == 1.0) {\\nrotation = 90.0 - rotation;\\n}\\nfloat angle = C_DEG_TO_RAD * -rotation;\\nfloat sinA = sin(angle);\\nfloat cosA = cos(angle);\\nreturn mat3(cosA, -sinA, 0,\\nsinA, cosA, 0,\\n0,    0,    1);\\n}\\n#endif\\n#ifdef VV_COLOR\\nconst int VV_COLOR_N = 8;\\nvec4 getVVColor(float colorValue, vec4 fallback, float isColorLocked) {\\nif (isNan(colorValue) || isColorLocked == 1.0) {\\nreturn fallback;\\n}\\nif (colorValue <= u_vvColorValues[0]) {\\nreturn u_vvColors[0];\\n}\\nfor (int i = 1; i < VV_COLOR_N; ++i) {\\nif (u_vvColorValues[i] >= colorValue) {\\nfloat f = (colorValue - u_vvColorValues[i-1]) / (u_vvColorValues[i] - u_vvColorValues[i-1]);\\nreturn mix(u_vvColors[i-1], u_vvColors[i], f);\\n}\\n}\\nreturn u_vvColors[VV_COLOR_N - 1];\\n}\\n#endif\\nfloat getVVSize(in float size, in float vvSize)  {\\n#ifdef VV_SIZE_MIN_MAX_VALUE\\nreturn getVVMinMaxSize(vvSize, size);\\n#elif defined(VV_SIZE_SCALE_STOPS)\\nreturn u_vvSizeScaleStopsValue;\\n#elif defined(VV_SIZE_FIELD_STOPS)\\nfloat outSize = getVVStopsSize(vvSize, size);\\nreturn isNan(outSize) ? size : outSize;\\n#elif defined(VV_SIZE_UNIT_VALUE)\\nreturn getVVUnitValue(vvSize, size);\\n#else\\nreturn size;\\n#endif\\n}\"},overlay:{overlay:{\"overlay.frag\":\"precision lowp float;\\nuniform lowp sampler2D u_texture;\\nuniform lowp float u_opacity;\\nvarying mediump vec2 v_uv;\\nvoid main() {\\nvec4 color = texture2D(u_texture, v_uv);\\ngl_FragColor = color *  u_opacity;\\n}\",\"overlay.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\nattribute vec2 a_uv;\\nuniform highp mat3 u_dvsMat3;\\nuniform mediump vec2 u_perspective;\\nvarying mediump vec2 v_uv;\\nvoid main(void) {\\nv_uv = a_uv;\\nfloat w = 1.0 + dot(a_uv, u_perspective);\\nvec3 pos = u_dvsMat3 * vec3(a_pos, 1.0);\\ngl_Position = vec4(w * pos.xy, 0.0, w);\\n}\"}},\"post-processing\":{blit:{\"blit.frag\":\"precision mediump float;\\nuniform sampler2D u_texture;\\nvarying vec2 v_uv;\\nvoid main() {\\ngl_FragColor = texture2D(u_texture, v_uv);\\n}\"},bloom:{composite:{\"composite.frag\":\"precision mediump float;\\nvarying vec2 v_uv;\\nuniform sampler2D u_blurTexture1;\\nuniform sampler2D u_blurTexture2;\\nuniform sampler2D u_blurTexture3;\\nuniform sampler2D u_blurTexture4;\\nuniform sampler2D u_blurTexture5;\\nuniform float u_bloomStrength;\\nuniform float u_bloomRadius;\\nuniform float u_bloomFactors[NUMMIPS];\\nuniform vec3 u_bloomTintColors[NUMMIPS];\\nfloat lerpBloomFactor(const in float factor) {\\nfloat mirrorFactor = 1.2 - factor;\\nreturn mix(factor, mirrorFactor, u_bloomRadius);\\n}\\nvoid main() {\\nvec4 color = u_bloomStrength * (\\nlerpBloomFactor(u_bloomFactors[0]) * vec4(u_bloomTintColors[0], 1.0) * texture2D(u_blurTexture1, v_uv) +\\nlerpBloomFactor(u_bloomFactors[1]) * vec4(u_bloomTintColors[1], 1.0) * texture2D(u_blurTexture2, v_uv) +\\nlerpBloomFactor(u_bloomFactors[2]) * vec4(u_bloomTintColors[2], 1.0) * texture2D(u_blurTexture3, v_uv) +\\nlerpBloomFactor(u_bloomFactors[3]) * vec4(u_bloomTintColors[3], 1.0) * texture2D(u_blurTexture4, v_uv) +\\nlerpBloomFactor(u_bloomFactors[4]) * vec4(u_bloomTintColors[4], 1.0) * texture2D(u_blurTexture5, v_uv)\\n);\\ngl_FragColor = clamp(color, 0.0, 1.0);\\n}\"},gaussianBlur:{\"gaussianBlur.frag\":\"precision mediump float;\\nuniform sampler2D u_colorTexture;\\nuniform vec2 u_texSize;\\nuniform vec2 u_direction;\\nvarying vec2 v_uv;\\n#define KERNEL_RADIUS RADIUS\\n#define SIGMA RADIUS\\nfloat gaussianPdf(in float x, in float sigma) {\\nreturn 0.39894 * exp(-0.5 * x * x / ( sigma * sigma)) / sigma;\\n}\\nvoid main() {\\nvec2 invSize = 1.0 / u_texSize;\\nfloat fSigma = float(SIGMA);\\nfloat weightSum = gaussianPdf(0.0, fSigma);\\nvec4 pixelColorSum = texture2D(u_colorTexture, v_uv) * weightSum;\\nfor (int i = 1; i < KERNEL_RADIUS; i ++) {\\nfloat x = float(i);\\nfloat w = gaussianPdf(x, fSigma);\\nvec2 uvOffset = u_direction * invSize * x;\\nvec4 sample1 = texture2D(u_colorTexture, v_uv + uvOffset);\\nvec4 sample2 = texture2D(u_colorTexture, v_uv - uvOffset);\\npixelColorSum += (sample1 + sample2) * w;\\nweightSum += 2.0 * w;\\n}\\ngl_FragColor = pixelColorSum /weightSum;\\n}\"},luminosityHighPass:{\"luminosityHighPass.frag\":\"precision mediump float;\\nuniform sampler2D u_texture;\\nuniform vec3 u_defaultColor;\\nuniform float u_defaultOpacity;\\nuniform float u_luminosityThreshold;\\nuniform float u_smoothWidth;\\nvarying vec2 v_uv;\\nvoid main() {\\nvec4 texel = texture2D(u_texture, v_uv);\\nvec3 luma = vec3(0.299, 0.587, 0.114);\\nfloat v = dot(texel.xyz, luma);\\nvec4 outputColor = vec4(u_defaultColor.rgb, u_defaultOpacity);\\nfloat alpha = smoothstep(u_luminosityThreshold, u_luminosityThreshold + u_smoothWidth, v);\\ngl_FragColor = mix(outputColor, texel, alpha);\\n}\"}},blur:{gaussianBlur:{\"gaussianBlur.frag\":\"precision mediump float;\\nuniform sampler2D u_colorTexture;\\nuniform vec2 u_texSize;\\nuniform vec2 u_direction;\\nuniform float u_sigma;\\nvarying vec2 v_uv;\\n#define KERNEL_RADIUS RADIUS\\nfloat gaussianPdf(in float x, in float sigma) {\\nreturn 0.39894 * exp(-0.5 * x * x / ( sigma * sigma)) / sigma;\\n}\\nvoid main() {\\nvec2 invSize = 1.0 / u_texSize;\\nfloat fSigma = u_sigma;\\nfloat weightSum = gaussianPdf(0.0, fSigma);\\nvec4 pixelColorSum = texture2D(u_colorTexture, v_uv) * weightSum;\\nfor (int i = 1; i < KERNEL_RADIUS; i ++) {\\nfloat x = float(i);\\nfloat w = gaussianPdf(x, fSigma);\\nvec2 uvOffset = u_direction * invSize * x;\\nvec4 sample1 = texture2D(u_colorTexture, v_uv + uvOffset);\\nvec4 sample2 = texture2D(u_colorTexture, v_uv - uvOffset);\\npixelColorSum += (sample1 + sample2) * w;\\nweightSum += 2.0 * w;\\n}\\ngl_FragColor = pixelColorSum /weightSum;\\n}\"},\"radial-blur\":{\"radial-blur.frag\":\"precision mediump float;\\nuniform sampler2D u_colorTexture;\\nvarying vec2 v_uv;\\nconst float sampleDist = 1.0;\\nconst float sampleStrength = 2.2;\\nvoid main(void) {\\nfloat samples[10];\\nsamples[0] = -0.08;\\nsamples[1] = -0.05;\\nsamples[2] = -0.03;\\nsamples[3] = -0.02;\\nsamples[4] = -0.01;\\nsamples[5] =  0.01;\\nsamples[6] =  0.02;\\nsamples[7] =  0.03;\\nsamples[8] =  0.05;\\nsamples[9] =  0.08;\\nvec2 dir = 0.5 - v_uv;\\nfloat dist = sqrt(dir.x * dir.x + dir.y * dir.y);\\ndir = dir / dist;\\nvec4 color = texture2D(u_colorTexture,v_uv);\\nvec4 sum = color;\\nfor (int i = 0; i < 10; i++) {\\nsum += texture2D(u_colorTexture, v_uv + dir * samples[i] * sampleDist);\\n}\\nsum *= 1.0 / 11.0;\\nfloat t = dist * sampleStrength;\\nt = clamp(t, 0.0, 1.0);\\ngl_FragColor = mix(color, sum, t);\\n}\"}},dra:{\"dra.frag\":\"precision mediump float;\\nuniform sampler2D u_minColor;\\nuniform sampler2D u_maxColor;\\nuniform sampler2D u_texture;\\nvarying vec2 v_uv;\\nvoid main() {\\nvec4 minColor = texture2D(u_minColor, vec2(0.5));\\nvec4 maxColor = texture2D(u_maxColor, vec2(0.5));\\nvec4 color = texture2D(u_texture, v_uv);\\nvec3 minColorUnpremultiply = minColor.rgb / minColor.a;\\nvec3 maxColorUnpremultiply = maxColor.rgb / maxColor.a;\\nvec3 colorUnpremultiply = color.rgb / color.a;\\nvec3 range = maxColorUnpremultiply - minColorUnpremultiply;\\ngl_FragColor = vec4(color.a * (colorUnpremultiply - minColorUnpremultiply) / range, color.a);\\n}\",\"min-max\":{\"min-max.frag\":\"#extension GL_EXT_draw_buffers : require\\nprecision mediump float;\\n#define CELL_SIZE 2\\nuniform sampler2D u_minTexture;\\nuniform sampler2D u_maxTexture;\\nuniform vec2 u_srcResolution;\\nuniform vec2 u_dstResolution;\\nvarying vec2 v_uv;\\nvoid main() {\\nvec2 srcPixel = floor(gl_FragCoord.xy) * float(CELL_SIZE);\\nvec2 onePixel = vec2(1.0) / u_srcResolution;\\nvec2 uv = (srcPixel + 0.5) / u_srcResolution;\\nvec4 minColor = vec4(1.0);\\nvec4 maxColor = vec4(0.0);\\nfor (int y = 0; y < CELL_SIZE; ++y) {\\nfor (int x = 0; x < CELL_SIZE; ++x) {\\nvec2 offset = uv + vec2(x, y) * onePixel;\\nminColor = min(minColor, texture2D(u_minTexture, offset));\\nmaxColor = max(maxColor, texture2D(u_maxTexture, offset));\\n}\\n}\\ngl_FragData[0] = minColor;\\ngl_FragData[1] = maxColor;\\n}\"}},\"drop-shadow\":{composite:{\"composite.frag\":\"precision mediump float;\\nuniform sampler2D u_layerFBOTexture;\\nuniform sampler2D u_blurTexture;\\nuniform vec4 u_shadowColor;\\nuniform vec2 u_shadowOffset;\\nuniform highp mat3 u_displayViewMat3;\\nvarying vec2 v_uv;\\nvoid main() {\\nvec3 offset = u_displayViewMat3 * vec3(u_shadowOffset, 0.0);\\nvec4 layerColor = texture2D(u_layerFBOTexture, v_uv);\\nvec4 blurColor = texture2D(u_blurTexture, v_uv - offset.xy / 2.0);\\ngl_FragColor = ((1.0 - layerColor.a) * blurColor.a * u_shadowColor + layerColor);\\n}\"}},\"edge-detect\":{\"frei-chen\":{\"frei-chen.frag\":\"precision mediump float;\\nuniform sampler2D u_colorTexture;\\nuniform vec2 u_texSize;\\nvarying vec2 v_uv;\\nvec2 texel = vec2(1.0 / u_texSize.x, 1.0 / u_texSize.y);\\nmat3 G[9];\\nconst mat3 g0 = mat3( 0.3535533845424652, 0, -0.3535533845424652, 0.5, 0, -0.5, 0.3535533845424652, 0, -0.3535533845424652 );\\nconst mat3 g1 = mat3( 0.3535533845424652, 0.5, 0.3535533845424652, 0, 0, 0, -0.3535533845424652, -0.5, -0.3535533845424652 );\\nconst mat3 g2 = mat3( 0, 0.3535533845424652, -0.5, -0.3535533845424652, 0, 0.3535533845424652, 0.5, -0.3535533845424652, 0 );\\nconst mat3 g3 = mat3( 0.5, -0.3535533845424652, 0, -0.3535533845424652, 0, 0.3535533845424652, 0, 0.3535533845424652, -0.5 );\\nconst mat3 g4 = mat3( 0, -0.5, 0, 0.5, 0, 0.5, 0, -0.5, 0 );\\nconst mat3 g5 = mat3( -0.5, 0, 0.5, 0, 0, 0, 0.5, 0, -0.5 );\\nconst mat3 g6 = mat3( 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.6666666865348816, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204 );\\nconst mat3 g7 = mat3( -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, 0.6666666865348816, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408 );\\nconst mat3 g8 = mat3( 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408 );\\nvoid main() {\\nG[0] = g0,\\nG[1] = g1,\\nG[2] = g2,\\nG[3] = g3,\\nG[4] = g4,\\nG[5] = g5,\\nG[6] = g6,\\nG[7] = g7,\\nG[8] = g8;\\nmat3 I;\\nfloat cnv[9];\\nvec3 sample;\\nfor (float i = 0.0; i < 3.0; i++) {\\nfor (float j = 0.0; j < 3.0; j++) {\\nsample = texture2D(u_colorTexture, v_uv + texel * vec2(i - 1.0,j - 1.0)).rgb;\\nI[int(i)][int(j)] = length(sample);\\n}\\n}\\nfor (int i = 0; i < 9; i++) {\\nfloat dp3 = dot(G[i][0], I[0]) + dot(G[i][1], I[1]) + dot(G[i][2], I[2]);\\ncnv[i] = dp3 * dp3;\\n}\\nfloat M = (cnv[0] + cnv[1]) + (cnv[2] + cnv[3]);\\nfloat S = (cnv[4] + cnv[5]) + (cnv[6] + cnv[7]) + (cnv[8] + M);\\ngl_FragColor = vec4(vec3(sqrt(M / S)), texture2D(u_colorTexture, v_uv).a);\\n}\"},sobel:{\"sobel.frag\":\"precision mediump float;\\nuniform sampler2D u_colorTexture;\\nvarying vec2 v_uv;\\nuniform vec2 u_texSize;\\nvec2 texel = vec2(1.0 / u_texSize.x, 1.0 / u_texSize.y);\\nmat3 G[2];\\nconst mat3 g0 = mat3( 1.0, 2.0, 1.0, 0.0, 0.0, 0.0, -1.0, -2.0, -1.0 );\\nconst mat3 g1 = mat3( 1.0, 0.0, -1.0, 2.0, 0.0, -2.0, 1.0, 0.0, -1.0 );\\nvoid main() {\\nmat3 I;\\nfloat cnv[2];\\nvec3 sample;\\nG[0] = g0;\\nG[1] = g1;\\nfor (float i = 0.0; i < 3.0; i++) {\\nfor (float j = 0.0; j < 3.0; j++) {\\nsample = texture2D( u_colorTexture, v_uv + texel * vec2(i-1.0,j-1.0) ).rgb;\\nI[int(i)][int(j)] = length(sample);\\n}\\n}\\nfor (int i = 0; i < 2; i++) {\\nfloat dp3 = dot(G[i][0], I[0]) + dot(G[i][1], I[1]) + dot(G[i][2], I[2]);\\ncnv[i] = dp3 * dp3;\\n}\\ngl_FragColor = vec4(vec3(0.5 * sqrt(cnv[0] * cnv[0] + cnv[1] * cnv[1])), texture2D(u_colorTexture, v_uv).a);\\n}\"}},\"edge-enhance\":{\"edge-enhance.frag\":\"precision mediump float;\\nuniform sampler2D u_colorTexture;\\nvarying vec2 v_uv;\\nuniform vec2 u_texSize;\\nvec2 texel = vec2(1.0 / u_texSize.x, 1.0 / u_texSize.y);\\nmat3 G[2];\\nconst mat3 g0 = mat3( 1.0, 0.0, -1.0, 1.0, 0.0, -1.0, 1.0, 0.0, -1.0 );\\nconst mat3 g1 = mat3( 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, -1.0, -1.0, -1.0 );\\nvoid main() {\\nmat3 I;\\nfloat cnv[2];\\nvec3 sample;\\nG[0] = g0;\\nG[1] = g1;\\nfor (float i = 0.0; i < 3.0; i++) {\\nfor (float j = 0.0; j < 3.0; j++) {\\nsample = texture2D( u_colorTexture, v_uv + texel * vec2(i-1.0,j-1.0) ).rgb;\\nI[int(i)][int(j)] = length(sample);\\n}\\n}\\nfor (int i = 0; i < 2; i++) {\\nfloat dp3 = dot(G[i][0], I[0]) + dot(G[i][1], I[1]) + dot(G[i][2], I[2]);\\ncnv[i] = dp3 * dp3;\\n}\\nvec4 color = texture2D(u_colorTexture, v_uv);\\ngl_FragColor = vec4(0.5 * sqrt(cnv[0] * cnv[0] + cnv[1] * cnv[1]) * color);\\n}\"},filterEffect:{\"filterEffect.frag\":\"precision mediump float;\\nuniform sampler2D u_colorTexture;\\nuniform mat4 u_coefficients;\\nvarying vec2 v_uv;\\nvoid main() {\\nvec4 color = texture2D(u_colorTexture, v_uv);\\nvec4 rgbw = u_coefficients * vec4(color.a > 0.0 ? color.rgb / color.a : vec3(0.0), 1.0);\\nfloat a = color.a;\\ngl_FragColor = vec4(a * rgbw.rgb, a);\\n}\"},pp:{\"pp.vert\":\"precision mediump float;\\nattribute vec2 a_position;\\nvarying vec2 v_uv;\\nvoid main() {\\ngl_Position = vec4(a_position, 0.0, 1.0);\\nv_uv = (a_position + 1.0) / 2.0;\\n}\"}},raster:{bitmap:{\"bitmap.frag\":\"precision mediump float;\\nvarying highp vec2 v_texcoord;\\nuniform sampler2D u_texture;\\nuniform highp vec2 u_coordScale;\\nuniform lowp float u_opacity;\\n#include <filtering/bicubic.glsl>\\nvoid main() {\\n#ifdef BICUBIC\\nvec4 color = sampleBicubicBSpline(u_texture, v_texcoord, u_coordScale);\\n#else\\nvec4 color = texture2D(u_texture, v_texcoord);\\n#endif\\ngl_FragColor = vec4(color.rgb * u_opacity, color.a * u_opacity);\\n}\",\"bitmap.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\nuniform highp mat3 u_dvsMat3;\\nuniform highp vec2 u_coordScale;\\nvarying highp vec2 v_texcoord;\\nvoid main()\\n{\\nv_texcoord = a_pos;\\ngl_Position = vec4(u_dvsMat3 * vec3(a_pos * u_coordScale, 1.0), 1.0);\\n}\"},common:{\"common.glsl\":\"uniform sampler2D u_image;\\nuniform int u_bandCount;\\nuniform bool u_flipY;\\nuniform float u_opacity;\\nuniform int u_resampling;\\nuniform vec2 u_srcImageSize;\\n#ifdef APPLY_PROJECTION\\n#include <raster/common/projection.glsl>\\n#endif\\n#ifdef BICUBIC\\n#include <filtering/bicubic.glsl>\\n#endif\\n#ifdef BILINEAR\\n#include <filtering/bilinear.glsl>\\n#endif\\nvec2 getPixelLocation(vec2 coords) {\\nvec2 targetLocation = u_flipY ? vec2(coords.s, 1.0 - coords.t) : coords;\\n#ifdef APPLY_PROJECTION\\ntargetLocation = projectPixelLocation(targetLocation);\\n#endif\\nreturn targetLocation;\\n}\\nbool isOutside(vec2 coords){\\nif (coords.t>1.00001 ||coords.t<-0.00001 || coords.s>1.00001 ||coords.s<-0.00001) {\\nreturn true;\\n} else {\\nreturn false;\\n}\\n}\\nvec4 getPixel(vec2 pixelLocation) {\\n#ifdef BICUBIC\\nvec4 color = sampleBicubicBSpline(u_image, pixelLocation, u_srcImageSize);\\n#elif defined(BILINEAR)\\nvec4 color = sampleBilinear(u_image, pixelLocation, u_srcImageSize);\\n#else\\nvec4 color = texture2D(u_image, pixelLocation);\\n#endif\\nreturn color;\\n}\",\"common.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\nuniform highp mat3 u_dvsMat3;\\nuniform highp vec2 u_coordScale;\\nuniform highp float u_scale;\\nuniform highp vec2 u_offset;\\nvarying highp vec2 v_texcoord;\\nvoid main()\\n{\\nv_texcoord = a_pos * u_scale + u_offset;\\ngl_Position = vec4(u_dvsMat3 * vec3(a_pos * u_coordScale, 1.0), 1.0);\\n}\",\"contrastBrightness.glsl\":\"uniform float u_contrastOffset;\\nuniform float u_brightnessOffset;\\nvec4 adjustContrastBrightness(vec4 currentPixel, bool isFloat) {\\nvec4 pixelValue = isFloat ? currentPixel * 255.0 : currentPixel;\\nfloat maxI = 255.0;\\nfloat mid = 128.0;\\nfloat c = u_contrastOffset;\\nfloat b = u_brightnessOffset;\\nvec4 v;\\nif (c > 0.0 && c < 100.0) {\\nv = (200.0 * pixelValue - 100.0 * maxI + 2.0 * maxI * b) / (2.0 * (100.0 - c)) + mid;\\n} else if (c <= 0.0 && c > -100.0) {\\nv = (200.0 * pixelValue - 100.0 * maxI + 2.0 * maxI * b) * (100.0 + c) / 20000.0 + mid;\\n} else if (c == 100.0) {\\nv = (200.0 * pixelValue - 100.0 * maxI + (maxI + 1.0) * (100.0 - c) + 2.0 * maxI * b);\\nv = (sign(v) + 1.0) / 2.0;\\n} else if (c == -100.0) {\\nv = vec4(mid, mid, mid, currentPixel.a);\\n}\\nreturn vec4(v.r / 255.0, v.g / 255.0, v.b / 255.0, currentPixel.a);\\n}\",\"inverse.glsl\":\"float invertValue(float value) {\\nfloat s = sign(value);\\nreturn (s * s) / (value + abs(s) - 1.0);\\n}\",\"mirror.glsl\":\"vec2 mirror(vec2 pos) {\\nvec2 pos1 = abs(pos);\\nreturn step(pos1, vec2(1.0, 1.0)) * pos1 + step(1.0, pos1) * (2.0 - pos1);\\n}\",\"projection.glsl\":\"uniform sampler2D u_transformGrid;\\nuniform vec2 u_transformSpacing;\\nuniform vec2 u_transformGridSize;\\nuniform vec2 u_targetImageSize;\\nvec2 projectPixelLocation(vec2 coords) {\\n#ifdef LOOKUP_PROJECTION\\nvec4 pv = texture2D(u_transformGrid, coords);\\nreturn vec2(pv.r, pv.g);\\n#endif\\nvec2 index_image = floor(coords * u_targetImageSize);\\nvec2 oneTransformPixel = vec2(0.25 / u_transformGridSize.s, 1.0 / u_transformGridSize.t);\\nvec2 index_transform = floor(index_image / u_transformSpacing) / u_transformGridSize;\\nvec2 pos = fract((index_image + vec2(0.5, 0.5)) / u_transformSpacing);\\nvec2 srcLocation;\\nvec2 transform_location = index_transform + oneTransformPixel * 0.5;\\nif (pos.s <= pos.t) {\\nvec4 ll_abc = texture2D(u_transformGrid, vec2(transform_location.s, transform_location.t));\\nvec4 ll_def = texture2D(u_transformGrid, vec2(transform_location.s + oneTransformPixel.s, transform_location.t));\\nsrcLocation.s = dot(ll_abc.rgb, vec3(pos, 1.0));\\nsrcLocation.t = dot(ll_def.rgb, vec3(pos, 1.0));\\n} else {\\nvec4 ur_abc = texture2D(u_transformGrid, vec2(transform_location.s + 2.0 * oneTransformPixel.s, transform_location.t));\\nvec4 ur_def = texture2D(u_transformGrid, vec2(transform_location.s + 3.0 * oneTransformPixel.s, transform_location.t));\\nsrcLocation.s = dot(ur_abc.rgb, vec3(pos, 1.0));\\nsrcLocation.t = dot(ur_def.rgb, vec3(pos, 1.0));\\n}\\nreturn srcLocation;\\n}\"},flow:{\"getFadeOpacity.glsl\":\"uniform float u_decayRate;\\nuniform float u_fadeToZero;\\nfloat getFadeOpacity(float x) {\\nfloat cutOff = mix(0.0, exp(-u_decayRate), u_fadeToZero);\\nreturn (exp(-u_decayRate * x) - cutOff) / (1.0 - cutOff);\\n}\",\"getFragmentColor.glsl\":\"vec4 getFragmentColor(vec4 color, float dist, float size, float featheringSize) {\\nfloat featheringStart = clamp(0.5 - featheringSize / size, 0.0, 0.5);\\nif (dist > featheringStart) {\\ncolor *= 1.0 - (dist - featheringStart) / (0.5 - featheringStart);\\n}\\nreturn color;\\n}\",imagery:{\"imagery.frag\":\"precision highp float;\\nvarying vec2 v_texcoord;\\nuniform sampler2D u_texture;\\nuniform float u_Min;\\nuniform float u_Max;\\nuniform float u_featheringSize;\\n#include <raster/flow/vv.glsl>\\nfloat getIntensity(float v) {\\nreturn u_Min + v * (u_Max - u_Min);\\n}\\nvoid main(void) {\\nvec4 sampled = texture2D(u_texture, v_texcoord);\\nfloat intensity = getIntensity(sampled.r);\\ngl_FragColor = getColor(intensity);\\ngl_FragColor.a *= getOpacity(sampled.r);\\ngl_FragColor.a *= sampled.a;\\ngl_FragColor.rgb *= gl_FragColor.a;\\n}\",\"imagery.vert\":\"attribute vec2 a_position;\\nattribute vec2 a_texcoord;\\nuniform mat3 u_dvsMat3;\\nvarying vec2 v_texcoord;\\nvoid main(void) {\\nvec2 xy = (u_dvsMat3 * vec3(a_position, 1.0)).xy;\\ngl_Position = vec4(xy, 0.0, 1.0);\\nv_texcoord = a_texcoord;\\n}\"},particles:{\"particles.frag\":\"precision highp float;\\nvarying vec4 v_color;\\nvarying vec2 v_texcoord;\\nvarying float v_size;\\nuniform float u_featheringSize;\\n#include <raster/flow/getFragmentColor.glsl>\\nvoid main(void) {\\ngl_FragColor = getFragmentColor(v_color, length(v_texcoord - 0.5), v_size, u_featheringSize);\\n}\",\"particles.vert\":\"attribute vec4 a_xyts0;\\nattribute vec4 a_xyts1;\\nattribute vec4 a_typeIdDurationSeed;\\nattribute vec4 a_extrudeInfo;\\nuniform mat3 u_dvsMat3;\\nuniform mat3 u_displayViewMat3;\\nuniform float u_time;\\nuniform float u_trailLength;\\nuniform float u_flowSpeed;\\nvarying vec4 v_color;\\nvarying vec2 v_texcoord;\\nvarying float v_size;\\nuniform float u_featheringSize;\\nuniform float u_introFade;\\n#include <raster/flow/vv.glsl>\\n#include <raster/flow/getFadeOpacity.glsl>\\nvoid main(void) {\\nvec2 position0 = a_xyts0.xy;\\nfloat t0 = a_xyts0.z;\\nfloat speed0 = a_xyts0.w;\\nvec2 position1 = a_xyts1.xy;\\nfloat t1 = a_xyts1.z;\\nfloat speed1 = a_xyts1.w;\\nfloat type = a_typeIdDurationSeed.x;\\nfloat id = a_typeIdDurationSeed.y;\\nfloat duration = a_typeIdDurationSeed.z;\\nfloat seed = a_typeIdDurationSeed.w;\\nvec2 e0 = a_extrudeInfo.xy;\\nvec2 e1 = a_extrudeInfo.zw;\\nfloat animationPeriod = duration + u_trailLength;\\nfloat scaledTime = u_time * u_flowSpeed;\\nfloat randomizedTime = scaledTime + seed * animationPeriod;\\nfloat t = mod(randomizedTime, animationPeriod);\\nfloat fUnclamped = (t - t0) / (t1 - t0);\\nfloat f = clamp(fUnclamped, 0.0, 1.0);\\nfloat clampedTime = mix(t0, t1, f);\\nfloat speed = mix(speed0, speed1, f);\\nvec2 extrude;\\nvec2 position;\\nfloat fadeOpacity;\\nfloat introOpacity;\\nif (type == 2.0) {\\nif (fUnclamped < 0.0 || (fUnclamped > 1.0 && t1 != duration)) {\\ngl_Position = vec4(0.0, 0.0, -2.0, 1.0);\\nreturn;\\n}\\nvec2 ortho = mix(e0, e1, f);\\nvec2 parallel;\\nparallel = normalize(position1 - position0) * 0.5;\\nif (id == 1.0) {\\nextrude = ortho;\\nv_texcoord = vec2(0.5, 0.0);\\n} else if (id == 2.0) {\\nextrude = -ortho;\\nv_texcoord = vec2(0.5, 1.0);\\n} else if (id == 3.0) {\\nextrude = ortho + parallel;\\nv_texcoord = vec2(1.0, 0.0);\\n} else if (id == 4.0) {\\nextrude = -ortho + parallel;\\nv_texcoord = vec2(1.0, 1.0);\\n}\\nfadeOpacity = getFadeOpacity((t - clampedTime) / u_trailLength);\\nintroOpacity = 1.0 - exp(-clampedTime);\\nv_size = getSize(speed);\\nv_color = getColor(speed);\\nv_color.a *= getOpacity(speed);\\nposition = mix(position0, position1, f);\\n} else {\\nif (fUnclamped < 0.0) {\\ngl_Position = vec4(0.0, 0.0, -2.0, 1.0);\\nreturn;\\n}\\nif (id == 1.0) {\\nextrude = e0;\\nv_texcoord = vec2(0.5, 0.0);\\nfadeOpacity = getFadeOpacity((t - t0) / u_trailLength);\\nintroOpacity = 1.0 - exp(-t0);\\nv_size = getSize(speed0);\\nv_color = getColor(speed0);\\nv_color.a *= getOpacity(speed0);\\nposition = position0;\\n} else if (id == 2.0) {\\nextrude = -e0;\\nv_texcoord = vec2(0.5, 1.0);\\nfadeOpacity = getFadeOpacity((t - t0) / u_trailLength);\\nintroOpacity = 1.0 - exp(-t0);\\nv_size = getSize(speed0);\\nv_color = getColor(speed0);\\nv_color.a *= getOpacity(speed0);\\nposition = position0;\\n} else if (id == 3.0) {\\nextrude = mix(e0, e1, f);\\nv_texcoord = vec2(0.5, 0.0);\\nfadeOpacity = getFadeOpacity((t - clampedTime) / u_trailLength);\\nintroOpacity = 1.0 - exp(-clampedTime);\\nv_size = getSize(speed);\\nv_color = getColor(speed);\\nv_color.a *= getOpacity(speed);\\nposition = mix(position0, position1, f);\\n} else if (id == 4.0) {\\nextrude = -mix(e0, e1, f);\\nv_texcoord = vec2(0.5, 1.0);\\nfadeOpacity = getFadeOpacity((t - clampedTime) / u_trailLength);\\nintroOpacity = 1.0 - exp(-clampedTime);\\nv_size = getSize(speed);\\nv_color = getColor(speed);\\nv_color.a *= getOpacity(speed);\\nposition = mix(position0, position1, f);\\n}\\n}\\nvec2 xy = (u_dvsMat3 * vec3(position, 1.0) + u_displayViewMat3 * vec3(extrude * v_size, 0.0)).xy;\\ngl_Position = vec4(xy, 0.0, 1.0);\\nv_color.a *= fadeOpacity;\\nv_color.a *= mix(1.0, introOpacity, u_introFade);\\nv_color.rgb *= v_color.a;\\n}\"},streamlines:{\"streamlines.frag\":\"precision highp float;\\nvarying float v_side;\\nvarying float v_time;\\nvarying float v_totalTime;\\nvarying float v_timeSeed;\\nvarying vec4 v_color;\\nvarying float v_size;\\nuniform float u_time;\\nuniform float u_trailLength;\\nuniform float u_flowSpeed;\\nuniform float u_featheringSize;\\nuniform float u_introFade;\\n#include <raster/flow/getFragmentColor.glsl>\\n#include <raster/flow/getFadeOpacity.glsl>\\nvoid main(void) {\\nfloat t = mod(v_timeSeed * (v_totalTime + u_trailLength) + u_time * u_flowSpeed, v_totalTime + u_trailLength) - v_time;\\nvec4 color = v_color * step(0.0, t) * getFadeOpacity(t / u_trailLength);\\ncolor *= mix(1.0, 1.0 - exp(-v_time), u_introFade);\\ngl_FragColor = getFragmentColor(color, length((v_side + 1.0) / 2.0 - 0.5), v_size, u_featheringSize);\\n}\",\"streamlines.vert\":\"attribute vec3 a_positionAndSide;\\nattribute vec3 a_timeInfo;\\nattribute vec2 a_extrude;\\nattribute float a_speed;\\nuniform mat3 u_dvsMat3;\\nuniform mat3 u_displayViewMat3;\\nvarying float v_time;\\nvarying float v_totalTime;\\nvarying float v_timeSeed;\\nvarying vec4 v_color;\\nvarying float v_side;\\nvarying float v_size;\\nuniform float u_featheringSize;\\n#include <raster/flow/vv.glsl>\\nvoid main(void) {\\nvec4 lineColor = getColor(a_speed);\\nfloat lineOpacity = getOpacity(a_speed);\\nfloat lineSize = getSize(a_speed);\\nvec2 position = a_positionAndSide.xy;\\nv_side = a_positionAndSide.z;\\nvec2 xy = (u_dvsMat3 * vec3(position, 1.0) + u_displayViewMat3 * vec3(a_extrude * lineSize, 0.0)).xy;\\ngl_Position = vec4(xy, 0.0, 1.0);\\nv_time = a_timeInfo.x;\\nv_totalTime = a_timeInfo.y;\\nv_timeSeed = a_timeInfo.z;\\nv_color = lineColor;\\nv_color.a *= lineOpacity;\\nv_color.rgb *= v_color.a;\\nv_size = lineSize;\\n}\"},\"vv.glsl\":\"#define MAX_STOPS 8\\n#ifdef VV_COLOR\\nuniform float u_color_stops[MAX_STOPS];\\nuniform vec4 u_color_values[MAX_STOPS];\\nuniform int u_color_count;\\n#else\\nuniform vec4 u_color;\\n#endif\\n#ifdef VV_OPACITY\\nuniform float u_opacity_stops[MAX_STOPS];\\nuniform float u_opacity_values[MAX_STOPS];\\nuniform int u_opacity_count;\\n#else\\nuniform float u_opacity;\\n#endif\\n#ifdef VV_SIZE\\nuniform float u_size_stops[MAX_STOPS];\\nuniform float u_size_values[MAX_STOPS];\\nuniform int u_size_count;\\n#else\\nuniform float u_size;\\n#endif\\nuniform float u_featheringOffset;\\nvec4 getColor(float x) {\\n#ifdef VV_COLOR\\nvec4 color = u_color_values[0];\\n{\\nfor (int i = 1; i < MAX_STOPS; i++) {\\nif (i >= u_color_count) {\\nbreak;\\n}\\nfloat x1 = u_color_stops[i - 1];\\nif (x < x1) {\\nbreak;\\n}\\nfloat x2 = u_color_stops[i];\\nvec4 y2 = u_color_values[i];\\nif (x < x2) {\\nvec4 y1 = u_color_values[i - 1];\\ncolor = y1 + (y2 - y1) * (x - x1) / (x2 - x1);\\n} else {\\ncolor = y2;\\n}\\n}\\n}\\n#else\\nvec4 color = u_color;\\n#endif\\nreturn color;\\n}\\nfloat getOpacity(float x) {\\n#ifdef VV_OPACITY\\nfloat opacity = u_opacity_values[0];\\n{\\nfor (int i = 1; i < MAX_STOPS; i++) {\\nif (i >= u_opacity_count) {\\nbreak;\\n}\\nfloat x1 = u_opacity_stops[i - 1];\\nif (x < x1) {\\nbreak;\\n}\\nfloat x2 = u_opacity_stops[i];\\nfloat y2 = u_opacity_values[i];\\nif (x < x2) {\\nfloat y1 = u_opacity_values[i - 1];\\nopacity = y1 + (y2 - y1) * (x - x1) / (x2 - x1);\\n} else {\\nopacity = y2;\\n}\\n}\\n}\\n#else\\nfloat opacity = u_opacity;\\n#endif\\nreturn opacity;\\n}\\nfloat getSize(float x) {\\n#ifdef VV_SIZE\\nfloat size = u_size_values[0];\\n{\\nfor (int i = 1; i < MAX_STOPS; i++) {\\nif (i >= u_size_count) {\\nbreak;\\n}\\nfloat x1 = u_size_stops[i - 1];\\nif (x < x1) {\\nbreak;\\n}\\nfloat x2 = u_size_stops[i];\\nfloat y2 = u_size_values[i];\\nif (x < x2) {\\nfloat y1 = u_size_values[i - 1];\\nsize = y1 + (y2 - y1) * (x - x1) / (x2 - x1);\\n} else {\\nsize = y2;\\n}\\n}\\n}\\n#else\\nfloat size = u_size;\\n#endif\\nreturn size + 2.0 * u_featheringSize * u_featheringOffset;\\n}\"},hillshade:{\"hillshade.frag\":\"precision mediump float;\\nvarying highp vec2 v_texcoord;\\n#include <raster/common/common.glsl>\\nuniform int u_hillshadeType;\\nuniform float u_sinZcosAs[6];\\nuniform float u_sinZsinAs[6];\\nuniform float u_cosZs[6];\\nuniform float u_weights[6];\\nuniform vec2 u_factor;\\nuniform float u_minValue;\\nuniform float u_maxValue;\\n#include <raster/lut/colorize.glsl>\\nfloat getNeighborHoodAlpha(float a, float b, float c, float d, float e, float f, float g, float h, float i){\\nif (a == 0.0 || a == 0.0 || a==0.0 || a == 0.0 || a == 0.0 || a==0.0 || a == 0.0 || a == 0.0 || a==0.0) {\\nreturn 0.0;\\n}\\nelse {\\nreturn e;\\n}\\n}\\nvec3 rgb2hsv(vec3 c) {\\nvec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);\\nvec4 p = c.g < c.b ? vec4(c.bg, K.wz) : vec4(c.gb, K.xy);\\nvec4 q = c.r < p.x ? vec4(p.xyw, c.r) : vec4(c.r, p.yzx);\\nfloat d = q.x - min(q.w, q.y);\\nfloat e = 1.0e-10;\\nreturn vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), min(d / (q.x + e), 1.0), q.x);\\n}\\nvec3 hsv2rgb(vec3 c) {\\nvec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);\\nvec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);\\nreturn c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);\\n}\\nvec4 overlay(float val, float minValue, float maxValue, float hillshade) {\\nval = clamp((val - minValue) / (maxValue - minValue), 0.0, 1.0);\\nvec4 rgb = colorize(vec4(val, val, val, 1.0), 255.0);\\nvec3 hsv = rgb2hsv(rgb.xyz);\\nhsv.z = hillshade;\\nreturn vec4(hsv2rgb(hsv), 1.0) * rgb.a;\\n}\\nvoid main() {\\nvec2 pixelLocation = getPixelLocation(v_texcoord);\\nif (isOutside(pixelLocation)) {\\ngl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\\nreturn;\\n}\\nvec4 currentPixel = getPixel(pixelLocation);\\nif (currentPixel.a == 0.0) {\\ngl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\\nreturn;\\n}\\nvec2 axy = vec2(-1.0, -1.0);\\nvec2 bxy = vec2(0.0, -1.0);\\nvec2 cxy = vec2(1.0, -1.0);\\nvec2 dxy = vec2(-1.0, 0.0);\\nvec2 fxy = vec2(1.0, 0.0);\\nvec2 gxy = vec2(-1.0, 1.0);\\nvec2 hxy = vec2(0.0, 1.0);\\nvec2 ixy = vec2(1.0, 1.0);\\nvec2 onePixel = 1.0 / u_srcImageSize;\\nif (pixelLocation.s < onePixel.s) {\\naxy[0] = 1.0;\\ndxy[0] = 1.0;\\ngxy[0] = 1.0;\\n}\\nif (pixelLocation.t < onePixel.t) {\\naxy[1] = 1.0;\\nbxy[1] = 1.0;\\ncxy[1] = 1.0;\\n}\\nif (pixelLocation.s > 1.0 - onePixel.s) {\\ncxy[0] = -1.0;\\nfxy[0] = -1.0;\\nixy[0] = -1.0;\\n}\\nif (pixelLocation.t > 1.0 - onePixel.t) {\\ngxy[1] = -1.0;\\nhxy[1] = -1.0;\\nixy[1] = -1.0;\\n}\\nvec4 va = texture2D(u_image, pixelLocation + onePixel * axy);\\nvec4 vb = texture2D(u_image, pixelLocation + onePixel * bxy);\\nvec4 vc = texture2D(u_image, pixelLocation + onePixel * cxy);\\nvec4 vd = texture2D(u_image, pixelLocation + onePixel * dxy);\\nvec4 ve = texture2D(u_image, pixelLocation);\\nvec4 vf = texture2D(u_image, pixelLocation + onePixel * fxy);\\nvec4 vg = texture2D(u_image, pixelLocation + onePixel * gxy);\\nvec4 vh = texture2D(u_image, pixelLocation + onePixel * hxy);\\nvec4 vi = texture2D(u_image, pixelLocation + onePixel * ixy);\\nfloat dzx = (vc + 2.0 * vf + vi - va - 2.0 * vd - vg).r * u_factor.s;\\nfloat dzy = (vg + 2.0 * vh + vi - va - 2.0 * vb - vc).r * u_factor.t;\\nfloat dzd = sqrt(1.0 + dzx * dzx + dzy * dzy);\\nfloat hillshade = 0.0;\\nif (u_hillshadeType == 0){\\nfloat cosDelta = u_sinZsinAs[0] * dzy - u_sinZcosAs[0] * dzx;\\nfloat z = (u_cosZs[0] + cosDelta) / dzd;\\nif (z < 0.0)  z = 0.0;\\nhillshade = z;\\n} else {\\nfor (int k = 0; k < 6; k++) {\\nfloat cosDelta = u_sinZsinAs[k] * dzy - u_sinZcosAs[k] * dzx;\\nfloat z = (u_cosZs[k] + cosDelta) / dzd;\\nif (z < 0.0) z = 0.0;\\nhillshade = hillshade + z * u_weights[k];\\nif (k == 5) break;\\n}\\n}\\nfloat alpha = getNeighborHoodAlpha(va.a, vb.a, vc.a, vd.a, ve.a, vf.a, vg.a, vh.a, vi.a);\\n#ifdef APPLY_COLORMAP\\ngl_FragColor = overlay(ve.r, u_minValue, u_maxValue, hillshade) * alpha * u_opacity;\\n#else\\ngl_FragColor = vec4(hillshade, hillshade, hillshade, 1.0) * alpha * u_opacity;\\n#endif\\n}\"},lut:{\"colorize.glsl\":\"uniform sampler2D u_colormap;\\nuniform float u_colormapOffset;\\nuniform float u_colormapMaxIndex;\\nvec4 colorize(vec4 currentPixel, float scaleFactor) {\\nfloat clrIndex = clamp(currentPixel.r * scaleFactor - u_colormapOffset, 0.0, u_colormapMaxIndex);\\nvec2 clrPosition = vec2((clrIndex + 0.5) / (u_colormapMaxIndex + 1.0), 0.0);\\nvec4 color = texture2D(u_colormap, clrPosition);\\nvec4 result = vec4(color.rgb, color.a * currentPixel.a);\\nreturn result;\\n}\",\"lut.frag\":\"precision mediump float;\\nvarying highp vec2 v_texcoord;\\n#include <raster/common/common.glsl>\\n#include <raster/lut/colorize.glsl>\\nvoid main() {\\nvec2 pixelLocation = getPixelLocation(v_texcoord);\\nif (isOutside(pixelLocation)) {\\ngl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\\nreturn;\\n}\\nvec4 currentPixel = getPixel(pixelLocation);\\nvec4 result = colorize(currentPixel, 1.0);\\ngl_FragColor = vec4(result.xyz, 1.0) * result.a * u_opacity;\\n}\"},magdir:{\"magdir.frag\":\"precision mediump float;\\nvarying vec4 v_color;\\nuniform lowp float u_opacity;\\nvoid main() {\\ngl_FragColor = v_color * u_opacity;\\n}\",\"magdir.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\nattribute vec2 a_offset;\\nattribute vec2 a_vv;\\nuniform highp mat3 u_dvsMat3;\\nuniform highp vec2 u_coordScale;\\nuniform vec2 u_symbolSize;\\nuniform vec2 u_symbolPercentRange;\\nuniform vec2 u_dataRange;\\nuniform float u_rotation;\\nuniform vec4 u_colors[12];\\nvarying vec4 v_color;\\nvoid main()\\n{\\nfloat angle = a_offset.y + u_rotation;\\n#ifndef ROTATION_GEOGRAPHIC\\nangle = 3.14159265359 * 2.0 - angle - 3.14159265359 / 2.0;\\n#endif\\nvec2 offset = vec2(cos(angle), sin(angle)) * a_offset.x;\\n#ifdef DATA_RANGE\\nfloat valuePercentage = clamp((a_vv.y - u_dataRange.x) / (u_dataRange.y - u_dataRange.x), 0.0, 1.0);\\nfloat sizeRatio = u_symbolPercentRange.x + valuePercentage * (u_symbolPercentRange.y - u_symbolPercentRange.x);\\nfloat sizePercentage = clamp(sizeRatio, u_symbolPercentRange.x, u_symbolPercentRange.y);\\n#else\\nfloat sizePercentage = (u_symbolPercentRange.x + u_symbolPercentRange.y) / 2.0;\\n#endif\\nvec2 pos = a_pos + offset * sizePercentage * u_symbolSize;\\nv_color = u_colors[int(a_vv.x)];\\ngl_Position = vec4(u_dvsMat3 * vec3(pos * u_coordScale, 1.0), 1.0);\\n}\"},reproject:{\"reproject.frag\":\"precision mediump float;\\nvarying vec2 v_texcoord;\\n#include <raster/common/common.glsl>\\nvoid main() {\\nvec2 pixelLocation = getPixelLocation(v_texcoord);\\nif (isOutside(pixelLocation)) {\\ngl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\\nreturn;\\n}\\nvec4 currentPixel = getPixel(pixelLocation);\\ngl_FragColor = vec4(currentPixel.rgb, 1.0) * currentPixel.a * u_opacity;\\n}\",\"reproject.vert\":\"precision mediump float;\\nattribute vec2 a_position;\\nvarying highp vec2 v_texcoord;\\nvoid main()\\n{\\nv_texcoord = a_position;\\ngl_Position = vec4(2.0 * (a_position - 0.5), 0.0, 1.0);\\n}\"},rfx:{aspect:{\"aspect.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nvarying vec2 v_texcoord;\\nuniform vec2 u_cellSize;\\nuniform vec2 u_srcImageSize;\\n#include <raster/common/mirror.glsl>\\nconst float pi = 3.14159265359;\\nvoid main() {\\nvec2 axy = vec2(-1.0, -1.0);\\nvec2 bxy = vec2(0.0, -1.0);\\nvec2 cxy = vec2(1.0, -1.0);\\nvec2 dxy = vec2(-1.0, 0.0);\\nvec2 fxy = vec2(1.0, 0.0);\\nvec2 gxy = vec2(-1.0, 1.0);\\nvec2 hxy = vec2(0.0, 1.0);\\nvec2 ixy = vec2(1.0, 1.0);\\nvec2 onePixel = 1.0 / u_srcImageSize;\\nvec4 va = texture2D(u_image, mirror(v_texcoord + onePixel * axy));\\nvec4 vb = texture2D(u_image, mirror(v_texcoord + onePixel * bxy));\\nvec4 vc = texture2D(u_image, mirror(v_texcoord + onePixel * cxy));\\nvec4 vd = texture2D(u_image, mirror(v_texcoord + onePixel * dxy));\\nvec4 ve = texture2D(u_image, mirror(v_texcoord + onePixel * vec2(0, 0)));\\nvec4 vf = texture2D(u_image, mirror(v_texcoord + onePixel * fxy));\\nvec4 vg = texture2D(u_image, mirror(v_texcoord + onePixel * gxy));\\nvec4 vh = texture2D(u_image, mirror(v_texcoord + onePixel * hxy));\\nvec4 vi = texture2D(u_image, mirror(v_texcoord + onePixel * ixy));\\nfloat dzx = (vc + 2.0 * vf + vi - va - 2.0 * vd - vg).r / (8.0 * u_cellSize[0]);\\nfloat dzy = -(vg + 2.0 * vh + vi - va - 2.0 * vb - vc).r / (8.0 * u_cellSize[1]);\\nfloat alpha = va.a * vb.a * vc.a * vd.a * ve.a * vf.a * vg.a * vh.a * vi.a * sign(abs(dzx) + abs(dzy));\\nfloat aspect_rad = (dzx == 0.0) ? (step(0.0, dzy) * 0.5 * pi + step(dzy, 0.0) * 1.5 * pi) : mod((2.5 * pi + atan(dzy, -dzx)), 2.0 * pi);\\nfloat aspect = aspect_rad * 180.0 / pi;\\ngl_FragColor = vec4(aspect, aspect, aspect, 1.0) * alpha;\\n}\"},bandarithmetic:{\"bandarithmetic.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nvarying vec2 v_texcoord;\\nuniform mediump mat3 u_bandIndexMat3;\\nuniform float u_adjustments[3];\\n#include <raster/common/inverse.glsl>\\nvoid main() {\\nvec4 pv = texture2D(u_image, v_texcoord);\\nvec3 pv2 = u_bandIndexMat3 * pv.rgb;\\nfloat nir = pv2.r;\\nfloat red = pv2.g;\\nfloat index;\\n#ifdef NDXI\\nindex = (nir - red) * invertValue(nir + red);\\n#elif defined(SR)\\nindex = nir * invertValue(red);\\n#elif defined(CI)\\nindex = nir * invertValue(red) - 1.0;\\n#elif defined(SAVI)\\nindex = (nir - red) * invertValue(nir + red + u_adjustments[0]) * (1.0 + u_adjustments[0]);\\n#elif defined(TSAVI)\\nfloat s = u_adjustments[0];\\nfloat a = u_adjustments[1];\\nfloat x = u_adjustments[2];\\nfloat y = -a * s + x * (1.0 + s * s);\\nindex = (s * (nir - s * red - a)) * invertValue(a * nir + red + y);\\n#elif defined(MAVI)\\nindex = 0.5 * (2.0 * (nir + 1.0) - sqrt(pow((2.0 * nir + 1.0), 2.0) - 8.0 * (nir - red)));\\n#elif defined(GEMI)\\nfloat eta = (2.0 * (nir * nir - red * red) + 1.5 * nir + 0.5 * red) * invertValue(nir + red + 0.5);\\nindex = eta * (1.0 - 0.25 * eta) - (red - 0.125) * invertValue(1.0 - red);\\n#elif defined(PVI)\\nfloat a = u_adjustments[0];\\nfloat b = u_adjustments[1];\\nfloat y = sqrt(1.0 + a * a);\\nindex = (nir - a * red - b) * invertValue(y);\\n#elif defined(VARI)\\nindex = (pv2.g - pv2.r) * invertValue(pv2.g + pv2.r - pv2.b);\\n#elif defined(MTVI2)\\nfloat green = pv2.b;\\nfloat v = pow(sqrt((2.0 * nir + 1.0), 2.0) - 6.0 * nir - 5.0 * sqrt(red) - 0.5);\\nindex = 1.5 * (1.2 * (nir - green) - 2.5 * (red - green)) * v;\\n#elif defined(RTVICORE)\\nfloat green = pv2.b;\\nindex = 100.0 * (nir - red) - 10.0 * (nir - green);\\n#elif defined(EVI)\\nfloat blue = pv2.b;\\nfloat denom = nir + 6.0 * red - 7.5 * blue + 1.0;\\nindex =  (2.5 * (nir - red)) * invertValue(denom);\\n#elif defined(WNDWI)\\nfloat g = pv2.r;\\nfloat n = pv2.g;\\nfloat s = pv2.s;\\nfloat a = u_adjustments[0];\\nfloat denom = g + a * n + (1.0 - a) * s;\\nindex = (g - a * n - (1 - a) * s) * invertValue(denom);\\n#elif defined(BAI)\\nindex = invertValue(pow((0.1 - red), 2.0) + pow((0.06 - nir), 2.0));\\n#else\\ngl_FragColor = pv;\\nreturn;\\n#endif\\ngl_FragColor = vec4(index, index, index, pv.a);\\n}\"},compositeband:{\"compositeband.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nuniform sampler2D u_image1;\\nuniform sampler2D u_image2;\\nvarying vec2 v_texcoord;\\nvoid main() {\\nvec4 p0 = texture2D(u_image, v_texcoord);\\nvec4 p1 = texture2D(u_image1, v_texcoord);\\nvec4 p2 = texture2D(u_image2, v_texcoord);\\ngl_FragColor = vec4(p0.r, p1.r, p2.r, p0.a * p1.a * p2.a);\\n}\"},convolution:{\"convolution.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nvarying vec2 v_texcoord;\\nuniform vec2 u_srcImageSize;\\n#define KERNEL_SIZE_ROWS ROWS\\n#define KERNEL_SIZE_COLS COLS\\nuniform vec2 u_clampRange;\\nuniform float u_kernel[25];\\n#include <raster/common/mirror.glsl>\\nvoid main() {\\nvec3 rgb = vec3(0.0, 0.0, 0.0);\\nvec2 resolution = 1.0 / u_srcImageSize;\\nfloat rowOffset = -float(floor(float(KERNEL_SIZE_ROWS) / 2.0));\\nfloat colOffset = -float(floor(float(KERNEL_SIZE_COLS) / 2.0));\\nfloat alpha = 1.0;\\nfor (int row = 0; row < KERNEL_SIZE_ROWS; row++) {\\nfloat pos_row = rowOffset + float(row);\\nfor (int col = 0; col < KERNEL_SIZE_COLS; col++) {\\nvec2 pos = v_texcoord + vec2(colOffset + float(col), pos_row) * resolution;\\nvec4 pv = texture2D(u_image, mirror(pos));\\nrgb += pv.rgb * u_kernel[row * KERNEL_SIZE_COLS + col];\\nalpha *= pv.a;\\n}\\n}\\nrgb = clamp(rgb, u_clampRange.s, u_clampRange.t);\\ngl_FragColor = vec4(rgb * alpha, alpha);\\n}\"},extractband:{\"extractband.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nvarying vec2 v_texcoord;\\nuniform mediump mat3 u_bandIndexMat3;\\nvoid main() {\\nvec4 pv = texture2D(u_image, v_texcoord);\\nvec3 pv2 = u_bandIndexMat3 * pv.rgb;\\ngl_FragColor = vec4(pv2, pv.a);\\n}\"},local:{\"local.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nuniform sampler2D u_image1;\\n#ifdef ONE_CONSTANT\\nuniform float u_image1Const;\\n#ifdef TWO_CONSTANT\\nuniform float u_image2Const;\\n#endif\\nuniform mat3 u_imageSwap;\\n#endif\\nvarying vec2 v_texcoord;\\nuniform vec2 u_domainRange;\\n#include <raster/common/inverse.glsl>\\nvoid main() {\\nvec4 pv0 = texture2D(u_image, v_texcoord);\\nfloat a = pv0.r;\\n#ifdef TWO_IMAGES\\n#ifdef ONE_CONSTANT\\nfloat b = u_image1Const;\\nvec3 abc = u_imageSwap * vec3(a, b, 0);\\na = abc.s;\\nb = abc.t;\\n#else\\nvec4 pv1 = texture2D(u_image1, v_texcoord);\\nfloat b = pv1.r;\\n#endif\\n#elif defined(CONDITIONAL)\\n#ifdef TWO_CONSTANT\\nfloat b = u_image1Const;\\nfloat c = u_image2Const;\\nvec3 abc = u_imageSwap * vec3(a, b, c);\\na = abc.s;\\nb = abc.t;\\nc = abc.p;\\n#elif defined(ONE_CONSTANT)\\nvec4 pv1 = texture2D(u_image1, v_texcoord);\\nfloat b = pv1.r;\\nfloat c = u_image1Const;\\nvec3 abc = u_imageSwap * vec3(a, b, c);\\na = abc.s;\\nb = abc.t;\\nc = abc.p;\\n#else\\nvec4 pv1 = texture2D(u_image1, v_texcoord);\\nvec4 pv2 = texture2D(u_image2, v_texcoord);\\nfloat b = pv1.r;\\nfloat c = pv2.r;\\n#endif\\n#endif\\nfloat result = a;\\nfloat alpha = pv0.a;\\n#ifdef PLUS\\nresult = a + b;\\n#elif defined(MINUS)\\nresult = a - b;\\n#elif defined(TIMES)\\nresult = a * b;\\n#elif defined(DIVIDE)\\nresult = a * invertValue(b);\\nalpha *= float(abs(sign(b)));\\n#elif defined(FLOATDIVIDE)\\nresult = a * invertValue(b);\\nalpha *= float(abs(sign(b)));\\n#elif defined(FLOORDIVIDE)\\nresult = floor(a * invertValue(b));\\nalpha *= float(abs(sign(b)));\\n#elif defined(SQUARE)\\nresult = a * a;\\n#elif defined(SQRT)\\nresult = sqrt(a);\\n#elif defined(POWER)\\nresult = pow(a, b);\\n#elif defined(LN)\\nresult = a <= 0.0 ? 0.0: log(a);\\nalpha *= float(a > 0.0);\\n#elif defined(LOG_1_0)\\nresult = a <= 0.0 ? 0.0: log2(a) * invertValue(log2(10.0));\\nalpha *= float(a > 0.0);\\n#elif defined(LOG_2)\\nresult = a <= 0.0 ? 0.0: log2(a);\\nalpha *= float(a > 0.0);\\n#elif defined(EXP)\\nresult = exp(a);\\n#elif defined(EXP_1_0)\\nresult = pow(10.0, a);\\n#elif defined(EXP_2)\\nresult = pow(2.0, a);\\n#elif defined(ROUNDDOWN)\\nresult = floor(a);\\n#elif defined(ROUNDUP)\\nresult = ceil(a);\\n#elif defined(INT)\\nresult = float(sign(a)) * floor(abs(a));\\n#elif defined(MOD)\\nresult = mod(a, b);\\n#elif defined(NEGATE)\\nresult = -a;\\n#elif defined(ABS)\\nresult = abs(a);\\n#elif defined(ACOS)\\nresult = abs(a) > 1.0 ? 0.0: acos(a);\\nalpha *= step(abs(a), 1.00001);\\n#elif defined(ACOSH)\\nresult = acosh(a);\\n#elif defined(POLYFILLACOSH)\\nresult = log(a + sqrt(a * a - 1.0));\\n#elif defined(ASIN)\\nresult = abs(a) > 1.0 ? 0.0: asin(a);\\nalpha *= step(abs(a), 1.00001);\\n#elif defined(ASINH)\\nresult = asinh(a);\\n#elif defined(POLYFILLASINH)\\nresult = log(a + sqrt(a * a + 1.0));\\n#elif defined(ATAN)\\nresult = atan(a);\\n#elif defined(ATANH)\\nresult = abs(a) > 1.0 ? 0.0: atanh(a);\\nalpha *= step(abs(a), 1.0);\\n#elif defined(POLYFILLATANH)\\nresult = a == 1.0 ? 0.0 : 0.5 * log((1.0 + a)/(1.0 -a));\\n#elif defined(ATAN_2)\\nresult = atan(a, b);\\n#elif defined(COS)\\nresult = cos(a);\\n#elif defined(COSH)\\nresult = cosh(a);\\n#elif defined(POLYFILLCOSH)\\nfloat halfexp = exp(a) / 2.0;\\nresult = halfexp + 1.0 / halfexp;\\n#elif defined(SIN)\\nresult = sin(a);\\n#elif defined(SINH)\\nresult = sinh(a);\\n#elif defined(POLYFILLSINH)\\nfloat halfexp = exp(a) / 2.0;\\nresult = halfexp - 1.0 / halfexp;\\n#elif defined(TAN)\\nresult = tan(a);\\n#elif defined(TANH)\\nresult = tanh(a);\\n#elif defined(POLYFILLTANH)\\nfloat expx = exp(a);\\nresult = (expx - 1.0 / expx) / (expx + 1.0 / expx);\\n#elif defined(BITWISEAND)\\nresult = a & b;\\n#elif defined(BITWISEOR)\\nresult = a | b;\\n#elif defined(BITWISELEFTSHIFT)\\nresult = a << b;\\n#elif defined(BITWISERIGHTSHIFT)\\nresult = a >> b;\\n#elif defined(BITWISENOT)\\nresult = ~a;\\n#elif defined(BITWISEXOR)\\nresult = a ^ b;\\n#elif defined(BOOLEANAND)\\nresult = float((a != 0.0) && (b != 0.0));\\n#elif defined(BOOLEANNOT)\\nresult = float(a == 0.0);\\n#elif defined(BOOLEANOR)\\nresult = float((a != 0.0) || (b != 0.0));\\n#elif defined(BOOLEANXOR)\\nresult = float((a != 0.0) ^^ (b != 0.0));\\n#elif defined(GREATERTHAN)\\nresult = float(a > b);\\n#elif defined(GREATERTHANEQUAL)\\nresult = float(a >= b);\\n#elif defined(LESSTHAN)\\nresult = float(a < b);\\n#elif defined(LESSTHANEQUAL)\\nresult = float(a <= b);\\n#elif defined(EQUALTO)\\nresult = float(a == b);\\n#elif defined(NOTEQUAL)\\nresult = float(a != b);\\n#elif defined(ISNULL)\\nresult = float(alpha == 0.0);\\nalpha = 1.0;\\n#elif defined(SETNULL)\\nfloat maskValue = float(a == 0.0);\\nresult = maskValue * b;\\nalpha *= maskValue;\\n#elif defined(CONDITIONAL)\\nfloat weight = float(abs(sign(a)));\\nresult = weight * b + (1.0 - weight) * c;\\n#endif\\nbool isInvalid = result < u_domainRange.s || result > u_domainRange.t;\\nresult = isInvalid ? 0.0 : result;\\nalpha *= float(!isInvalid);\\n#ifdef ROUND_OUTPUT\\nresult = floor(result + 0.5);\\n#endif\\ngl_FragColor = vec4(result, result, result, alpha);\\n}\"},mask:{\"mask.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nvarying vec2 v_texcoord;\\n#define LEN_INCLUDED_RANGES 6\\n#define LEN_NODATA_VALUES 6\\nuniform highp float u_includedRanges[6];\\nuniform highp float u_noDataValues[6];\\nfloat maskFactor(float bandValue, float fromValue, float to) {\\nfloat factor = 1.0;\\nfor (int i = 0; i < LEN_NODATA_VALUES; i++) {\\nfactor *= float(u_noDataValues[i] != bandValue);\\n}\\nfactor *= step(fromValue, bandValue) * step(bandValue, to);\\nreturn factor;\\n}\\nvoid main() {\\nvec4 pv = texture2D(u_image, v_texcoord);\\nfloat redFactor = maskFactor(pv.r, u_includedRanges[0], u_includedRanges[1]);\\n#ifdef MULTI_BAND\\nfloat greenFactor = maskFactor(pv.g, u_includedRanges[2], u_includedRanges[3]);\\nfloat blueFactor = maskFactor(pv.b, u_includedRanges[4], u_includedRanges[5]);\\nfloat maskFactor = redFactor * greenFactor * blueFactor;\\ngl_FragColor = pv * maskFactor;\\n#else\\ngl_FragColor = pv * redFactor;\\n#endif\\n}\"},ndvi:{\"ndvi.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nvarying vec2 v_texcoord;\\nuniform mediump mat3 u_bandIndexMat3;\\n#include <raster/common/inverse.glsl>\\nvoid main() {\\nvec4 pv = texture2D(u_image, v_texcoord);\\nvec3 pv2 = u_bandIndexMat3 * pv.rgb;\\nfloat nir = pv2.r;\\nfloat red = pv2.g;\\nfloat index = (nir - red) * invertValue(nir + red);\\n#ifdef SCALED\\nindex = floor((index + 1.0) * 100.0 + 0.5);\\n#endif\\ngl_FragColor = vec4(index, index, index, pv.a);\\n}\"},remap:{\"remap.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nvarying vec2 v_texcoord;\\n#define LEN_REMAP_RANGES 18\\n#define LEN_NODATA_RANGES 12\\nuniform highp float u_rangeMaps[18];\\nuniform highp float u_noDataRanges[12];\\nuniform highp float u_unmatchMask;\\nuniform vec2 u_clampRange;\\nvoid main() {\\nvec4 pv = texture2D(u_image, v_texcoord);\\nfloat factor = 1.0;\\nfloat bandValue = pv.r;\\nfor (int i = 0; i < LEN_NODATA_RANGES; i+=2) {\\nfloat inside = 1.0 - step(u_noDataRanges[i], bandValue) * step(bandValue, u_noDataRanges[i+1]);\\nfactor *= inside;\\n}\\nfloat mapValue = 0.0;\\nfloat includeMask = 0.0;\\nfor (int i = 0; i < LEN_REMAP_RANGES; i+=3) {\\nfloat stepMask = step(u_rangeMaps[i], bandValue) * step(bandValue, u_rangeMaps[i+1]);\\nincludeMask = (1.0 - stepMask) * includeMask + stepMask;\\nmapValue = (1.0 - stepMask) * mapValue + stepMask * u_rangeMaps[i+2];\\n}\\nbandValue = factor * (mapValue + (1.0 - includeMask) * u_unmatchMask * pv.r);\\nfloat bandMask = factor * max(u_unmatchMask, includeMask);\\nbandValue = clamp(bandValue, u_clampRange.s, u_clampRange.t);\\ngl_FragColor = vec4(bandValue, bandValue, bandValue, bandMask * pv.a);\\n}\"},slope:{\"slope.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nvarying vec2 v_texcoord;\\nuniform vec2 u_cellSize;\\nuniform float u_zFactor;\\nuniform vec2 u_srcImageSize;\\nuniform float u_pixelSizePower;\\nuniform float u_pixelSizeFactor;\\n#include <raster/common/mirror.glsl>\\nvoid main() {\\nvec2 axy = vec2(-1.0, -1.0);\\nvec2 bxy = vec2(0.0, -1.0);\\nvec2 cxy = vec2(1.0, -1.0);\\nvec2 dxy = vec2(-1.0, 0.0);\\nvec2 fxy = vec2(1.0, 0.0);\\nvec2 gxy = vec2(-1.0, 1.0);\\nvec2 hxy = vec2(0.0, 1.0);\\nvec2 ixy = vec2(1.0, 1.0);\\nvec2 onePixel = 1.0 / u_srcImageSize;\\nvec4 va = texture2D(u_image, mirror(v_texcoord + onePixel * axy));\\nvec4 vb = texture2D(u_image, mirror(v_texcoord + onePixel * bxy));\\nvec4 vc = texture2D(u_image, mirror(v_texcoord + onePixel * cxy));\\nvec4 vd = texture2D(u_image, mirror(v_texcoord + onePixel * dxy));\\nvec4 ve = texture2D(u_image, mirror(v_texcoord + onePixel * vec2(0, 0)));\\nvec4 vf = texture2D(u_image, mirror(v_texcoord + onePixel * fxy));\\nvec4 vg = texture2D(u_image, mirror(v_texcoord + onePixel * gxy));\\nvec4 vh = texture2D(u_image, mirror(v_texcoord + onePixel * hxy));\\nvec4 vi = texture2D(u_image, mirror(v_texcoord + onePixel * ixy));\\nfloat xf = (u_zFactor + pow(u_cellSize[0], u_pixelSizePower) * u_pixelSizeFactor) / (8.0 * u_cellSize[0]);\\nfloat yf = (u_zFactor + pow(u_cellSize[1], u_pixelSizePower) * u_pixelSizeFactor) / (8.0 * u_cellSize[1]);\\nfloat dzx = (vc + 2.0 * vf + vi - va - 2.0 * vd - vg).r * xf;\\nfloat dzy = -(vg + 2.0 * vh + vi - va - 2.0 * vb - vc).r * yf;\\nfloat alpha = va.a * vb.a * vc.a * vd.a * ve.a * vf.a * vg.a * vh.a * vi.a;\\nfloat rise2run = sqrt(dzx * dzx + dzy * dzy);\\n#ifdef PERCENT_RISE\\nfloat percentRise = rise2run * 100.0;\\ngl_FragColor = vec4(percentRise, percentRise, percentRise, alpha);\\n#else\\nfloat degree = atan(rise2run) * 57.2957795;\\ngl_FragColor = vec4(degree, degree, degree, alpha);\\n#endif\\n}\"},stretch:{\"stretch.frag\":\"precision mediump float;\\nuniform sampler2D u_image;\\nvarying highp vec2 v_texcoord;\\nuniform float u_minCutOff[3];\\nuniform float u_maxCutOff[3];\\nuniform float u_minOutput;\\nuniform float u_maxOutput;\\nuniform float u_factor[3];\\nuniform float u_gamma[3];\\nuniform float u_gammaCorrection[3];\\nfloat stretchOneValue(float val, float minCutOff, float maxCutOff, float minOutput, float maxOutput, float factor, float gamma, float gammaCorrection) {\\nval = clamp(val, minCutOff, maxCutOff);\\nfloat stretchedVal;\\n#ifdef USE_GAMMA\\nfloat tempf = 1.0;\\nfloat outRange = maxOutput - minOutput;\\nfloat relativeVal = (val - minCutOff) / (maxCutOff - minCutOff);\\ntempf -= step(1.0, gamma) * sign(gamma - 1.0) * pow(1.0 / outRange, relativeVal * gammaCorrection);\\nstretchedVal = tempf * outRange * pow(relativeVal, 1.0 / gamma) + minOutput;\\nstretchedVal = clamp(stretchedVal, minOutput, maxOutput);\\n#else\\nstretchedVal = minOutput + (val - minCutOff) * factor;\\n#endif\\n#ifdef ROUND_OUTPUT\\nstretchedVal = floor(stretchedVal + 0.5);\\n#endif\\nreturn stretchedVal;\\n}\\nvoid main() {\\nvec4 currentPixel = texture2D(u_image, v_texcoord);\\nfloat redVal = stretchOneValue(currentPixel.r, u_minCutOff[0], u_maxCutOff[0], u_minOutput, u_maxOutput, u_factor[0], u_gamma[0], u_gammaCorrection[0]);\\n#ifdef MULTI_BAND\\nfloat greenVal = stretchOneValue(currentPixel.g, u_minCutOff[1], u_maxCutOff[1], u_minOutput, u_maxOutput, u_factor[1], u_gamma[1], u_gammaCorrection[1]);\\nfloat blueVal = stretchOneValue(currentPixel.b, u_minCutOff[2], u_maxCutOff[2], u_minOutput, u_maxOutput, u_factor[2], u_gamma[2], u_gammaCorrection[2]);\\ngl_FragColor = vec4(redVal, greenVal, blueVal, currentPixel.a);\\n#else\\ngl_FragColor = vec4(redVal, redVal, redVal, currentPixel.a);\\n#endif\\n}\"},vs:{\"vs.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\nuniform highp mat3 u_dvsMat3;\\nuniform highp vec2 u_coordScale;\\nvarying highp vec2 v_texcoord;\\nvoid main()\\n{\\nv_texcoord = a_pos;\\ngl_Position = vec4(u_dvsMat3 * vec3(a_pos * u_coordScale, 1.0), 1.0);\\n}\"}},scalar:{\"scalar.frag\":\"precision mediump float;\\nuniform lowp float u_opacity;\\nvarying vec2 v_pos;\\nconst vec4 outlineColor = vec4(0.2, 0.2, 0.2, 1.0);\\nconst float outlineSize = 0.02;\\nconst float innerRadius = 0.25;\\nconst float outerRadius = 0.42;\\nconst float innerSquareLength = 0.15;\\nvoid main() {\\nmediump float dist = length(v_pos);\\nmediump float fillalpha1 = smoothstep(outerRadius, outerRadius + outlineSize, dist);\\nfillalpha1 *= (1.0-smoothstep(outerRadius + outlineSize, outerRadius + 0.1 + outlineSize, dist));\\n#ifdef INNER_CIRCLE\\nmediump float fillalpha2 = smoothstep(innerRadius, innerRadius + outlineSize, dist);\\nfillalpha2 *= (1.0-smoothstep(innerRadius + outlineSize, innerRadius + 0.1 + outlineSize, dist));\\n#else\\nmediump float fillalpha2 = (abs(v_pos.x) < innerSquareLength ? 1.0 : 0.0) * (abs(v_pos.y) < innerSquareLength ? 1.0 : 0.0);\\n#endif\\ngl_FragColor = (fillalpha2 + fillalpha1) * outlineColor * u_opacity;\\n}\",\"scalar.vert\":\"precision mediump float;\\nattribute vec2 a_pos;\\nattribute vec2 a_offset;\\nattribute vec2 a_vv;\\nuniform highp mat3 u_dvsMat3;\\nuniform highp vec2 u_coordScale;\\nuniform vec2 u_symbolSize;\\nuniform vec2 u_symbolPercentRange;\\nuniform vec2 u_dataRange;\\nvarying vec2 v_pos;\\nvoid main()\\n{\\n#ifdef DATA_RANGE\\nfloat valuePercentage = clamp((a_vv.y - u_dataRange.x) / (u_dataRange.y - u_dataRange.x), 0.0, 1.0);\\nfloat sizeRatio = u_symbolPercentRange.x + valuePercentage * (u_symbolPercentRange.y - u_symbolPercentRange.x);\\nfloat sizePercentage = clamp(sizeRatio, u_symbolPercentRange.x, u_symbolPercentRange.y);\\n#else\\nfloat sizePercentage = (u_symbolPercentRange.x + u_symbolPercentRange.y) / 2.0;\\n#endif\\nvec2 size = u_symbolSize * sizePercentage;\\nvec2 pos = a_pos + a_offset * size;\\nv_pos = a_offset;\\ngl_Position = vec4(u_dvsMat3 * vec3(pos * u_coordScale, 1.0), 1.0);\\n}\"},stretch:{\"stretch.frag\":\"precision mediump float;\\nvarying highp vec2 v_texcoord;\\n#include <raster/common/common.glsl>\\nuniform float u_minCutOff[3];\\nuniform float u_maxCutOff[3];\\nuniform float u_minOutput;\\nuniform float u_maxOutput;\\nuniform float u_factor[3];\\nuniform bool u_useGamma;\\nuniform float u_gamma[3];\\nuniform float u_gammaCorrection[3];\\n#include <raster/lut/colorize.glsl>\\nfloat stretchOneValue(float val, float minCutOff, float maxCutOff, float minOutput, float maxOutput, float factor, bool useGamma, float gamma, float gammaCorrection) {\\nif (val >= maxCutOff) {\\nreturn maxOutput;\\n} else if (val <= minCutOff) {\\nreturn minOutput;\\n}\\nfloat stretchedVal;\\nif (useGamma) {\\nfloat tempf = 1.0;\\nfloat outRange = maxOutput - minOutput;\\nfloat relativeVal = (val - minCutOff) / (maxCutOff - minCutOff);\\nif (gamma > 1.0) {\\ntempf -= pow(1.0 / outRange, relativeVal * gammaCorrection);\\n}\\nstretchedVal = (tempf * outRange * pow(relativeVal, 1.0 / gamma) + minOutput) / 255.0;\\n} else {\\nstretchedVal = minOutput + (val - minCutOff) * factor;\\n}\\nreturn stretchedVal;\\n}\\nvoid main() {\\nvec2 pixelLocation = getPixelLocation(v_texcoord);\\nif (isOutside(pixelLocation)) {\\ngl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\\nreturn;\\n}\\nvec4 currentPixel = getPixel(pixelLocation);\\n#ifdef NOOP\\ngl_FragColor = vec4(currentPixel.rgb, 1.0) * currentPixel.a * u_opacity;\\nreturn;\\n#endif\\nif (u_bandCount == 1) {\\nfloat grayVal = stretchOneValue(currentPixel.r, u_minCutOff[0], u_maxCutOff[0], u_minOutput, u_maxOutput, u_factor[0], u_useGamma, u_gamma[0], u_gammaCorrection[0]);\\n#ifdef APPLY_COLORMAP\\nvec4 result = colorize(vec4(grayVal, grayVal, grayVal, 1.0), u_useGamma ? 255.0 : 1.0);\\ngl_FragColor = vec4(result.xyz, 1.0) * result.a * currentPixel.a * u_opacity;\\n#else\\ngl_FragColor = vec4(grayVal, grayVal, grayVal, 1.0) * currentPixel.a * u_opacity;\\n#endif\\n} else {\\nfloat redVal = stretchOneValue(currentPixel.r, u_minCutOff[0], u_maxCutOff[0], u_minOutput, u_maxOutput, u_factor[0], u_useGamma, u_gamma[0], u_gammaCorrection[0]);\\nfloat greenVal = stretchOneValue(currentPixel.g, u_minCutOff[1], u_maxCutOff[1], u_minOutput, u_maxOutput, u_factor[1], u_useGamma, u_gamma[1], u_gammaCorrection[1]);\\nfloat blueVal = stretchOneValue(currentPixel.b, u_minCutOff[2], u_maxCutOff[2], u_minOutput, u_maxOutput, u_factor[2], u_useGamma, u_gamma[2], u_gammaCorrection[2]);\\ngl_FragColor = vec4(redVal, greenVal, blueVal, 1.0) * currentPixel.a * u_opacity;\\n}\\n}\"}},stencil:{\"stencil.frag\":\"void main() {\\ngl_FragColor = vec4(1.0, 1.0, 1.0, 1.0);\\n}\",\"stencil.vert\":\"attribute vec2 a_pos;\\nuniform mat3 u_worldExtent;\\nvoid main() {\\ngl_Position = vec4(u_worldExtent * vec3(a_pos, 1.0), 1.0);\\n}\"},tileInfo:{\"tileInfo.frag\":\"uniform mediump sampler2D u_texture;\\nvarying mediump vec2 v_tex;\\nvoid main(void) {\\nlowp vec4 color = texture2D(u_texture, v_tex);\\ngl_FragColor = 0.75 * color;\\n}\",\"tileInfo.vert\":\"attribute vec2 a_pos;\\nuniform highp mat3 u_dvsMat3;\\nuniform mediump float u_depth;\\nuniform mediump vec2 u_coord_ratio;\\nuniform mediump vec2 u_delta;\\nuniform mediump vec2 u_dimensions;\\nvarying mediump vec2 v_tex;\\nvoid main() {\\nmediump vec2 offset = u_coord_ratio * vec2(u_delta + a_pos * u_dimensions);\\nvec3 v_pos = u_dvsMat3 * vec3(offset, 1.0);\\ngl_Position = vec4(v_pos.xy, 0.0, 1.0);\\nv_tex = a_pos;\\n}\"},util:{\"atan2.glsl\":\"float atan2(in float y, in float x) {\\nfloat t0, t1, t2, t3, t4;\\nt3 = abs(x);\\nt1 = abs(y);\\nt0 = max(t3, t1);\\nt1 = min(t3, t1);\\nt3 = 1.0 / t0;\\nt3 = t1 * t3;\\nt4 = t3 * t3;\\nt0 =         - 0.013480470;\\nt0 = t0 * t4 + 0.057477314;\\nt0 = t0 * t4 - 0.121239071;\\nt0 = t0 * t4 + 0.195635925;\\nt0 = t0 * t4 - 0.332994597;\\nt0 = t0 * t4 + 0.999995630;\\nt3 = t0 * t3;\\nt3 = (abs(y) > abs(x)) ? 1.570796327 - t3 : t3;\\nt3 = x < 0.0 ?  3.141592654 - t3 : t3;\\nt3 = y < 0.0 ? -t3 : t3;\\nreturn t3;\\n}\",\"encoding.glsl\":\"const vec4 rgba2float_factors = vec4(\\n255.0 / (256.0),\\n255.0 / (256.0 * 256.0),\\n255.0 / (256.0 * 256.0 * 256.0),\\n255.0 / (256.0 * 256.0 * 256.0 * 256.0)\\n);\\nfloat rgba2float(vec4 rgba) {\\nreturn dot(rgba, rgba2float_factors);\\n}\"}};export{e as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"./shaderRepository.js\";import{ShaderCompiler as e}from\"../../../../../webgl/ShaderCompiler.js\";function o(e){let o=r;return e.split(\"/\").forEach((r=>{o&&(o=o[r])})),o}const t=new e(o);function n(r){return t.resolveIncludes(r)}export{n as resolveIncludes};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{resolveIncludes as r}from\"./sources/resolver.js\";const e={shaders:{vertexShader:r(\"background/background.vert\"),fragmentShader:r(\"background/background.frag\")},attributes:new Map([[\"a_pos\",0]])};export{e as background};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t,isSome as e}from\"../../../../../core/maybe.js\";import{MIN_MAX_ZOOM_PRECISION_FACTOR as i,TEXTURE_BINDING_ATTRIBUTE_DATA_0 as s,TEXTURE_BINDING_ATTRIBUTE_DATA_1 as o,TEXTURE_BINDING_ATTRIBUTE_DATA_2 as a,TEXTURE_BINDING_ATTRIBUTE_DATA_3 as r,TEXTURE_BINDING_ATTRIBUTE_DATA_4 as n,TEXTURE_BINDING_ATTRIBUTE_DATA_5 as u}from\"../definitions.js\";import m from\"./WGLBrush.js\";import{MaterialKeyBase as v}from\"../materialKey/MaterialKey.js\";import{BlendFactor as f,CompareFunction as l}from\"../../../../webgl/enums.js\";class p extends m{constructor(){super(...arguments),this._computeDesc=new Map}prepareState({context:t},e){e&&e.includes(\"hittest\")?t.setBlendFunctionSeparate(f.ONE,f.ONE,f.ONE,f.ONE):t.setBlendFunctionSeparate(f.ONE,f.ONE_MINUS_SRC_ALPHA,f.ONE,f.ONE_MINUS_SRC_ALPHA),t.setBlendingEnabled(!0),t.setColorMask(!0,!0,!0,!0),t.setStencilWriteMask(0),t.setStencilTestEnabled(!0)}draw(e,i,s){const o=this.getGeometryType();i.commit(e);const a=i.getGeometry(o);t(a)||(e.timeline.begin(this.name),e.attributeView.bindTextures(e.context),e.context.setStencilFunction(l.EQUAL,i.stencilRef,255),a.forEachCommand((t=>{const o=v.load(t.materialKey).symbologyType;this.supportsSymbology(o)&&this.drawGeometry(e,i,t,s)})))}_setSharedUniforms(t,m,v){const{displayLevel:f,pixelRatio:l,state:p,passOptions:c}=m;e(c)&&\"hittest\"===c.type&&(t.setUniform2fv(\"u_hittestPos\",c.position),t.setUniform1f(\"u_hittestDist\",c.distance)),t.setUniform1f(\"u_pixelRatio\",l),t.setUniformMatrix3fv(\"u_tileMat3\",v.transforms.tileMat3),t.setUniformMatrix3fv(\"u_viewMat3\",p.viewMat3),t.setUniformMatrix3fv(\"u_dvsMat3\",v.transforms.dvs),t.setUniformMatrix3fv(\"u_displayViewMat3\",p.displayViewMat3),t.setUniform1f(\"u_currentZoom\",Math.round(f*i)),t.setUniform1i(\"u_attributeTextureSize\",m.attributeView.size),t.setUniform1i(\"u_attributeData0\",s),t.setUniform1i(\"u_attributeData1\",o),t.setUniform1i(\"u_attributeData2\",a),t.setUniform1i(\"u_attributeData3\",r),t.setUniform1i(\"u_attributeData4\",n),t.setUniform1i(\"u_attributeData5\",u)}_setSizeVVUniforms(t,e,i,s){if(t.vvSizeMinMaxValue&&e.setUniform4fv(\"u_vvSizeMinMaxValue\",i.vvSizeMinMaxValue),t.vvSizeScaleStops&&e.setUniform1f(\"u_vvSizeScaleStopsValue\",i.vvSizeScaleStopsValue),t.vvSizeFieldStops){const t=i.getSizeVVFieldStops(s.key.level);null!=t&&(e.setUniform1fv(\"u_vvSizeFieldStopsValues\",t.values),e.setUniform1fv(\"u_vvSizeFieldStopsSizes\",t.sizes))}t.vvSizeUnitValue&&e.setUniform1f(\"u_vvSizeUnitValueWorldToPixelsRatio\",i.vvSizeUnitValueToPixelsRatio)}_setColorAndOpacityVVUniforms(t,e,i){t.vvColor&&(e.setUniform1fv(\"u_vvColorValues\",i.vvColorValues),e.setUniform4fv(\"u_vvColors\",i.vvColors)),t.vvOpacity&&(e.setUniform1fv(\"u_vvOpacityValues\",i.vvOpacityValues),e.setUniform1fv(\"u_vvOpacities\",i.vvOpacities))}_setRotationVVUniforms(t,e,i){t.vvRotation&&e.setUniform1f(\"u_vvRotationType\",\"geographic\"===i.vvMaterialParameters.vvRotationType?0:1)}_getTriangleDesc(t,e,i=[\"a_pos\"]){const s=e.bufferLayouts.geometry,o=i.map((t=>s.findIndex((e=>e.name===t)))),a=`${t}-${o.join(\"-\")}`;let r=this._computeDesc.get(a);if(!r){const t=e.strides,i=e.strides.geometry,n=new Map(e.attributes),u=s.map((t=>({...t}))),m=Math.max(...e.attributes.values()),v={geometry:u};let f=0;for(const e of o){const t=s[e];v.geometry.push({count:t.count,name:t.name+\"1\",divisor:t.divisor,normalized:t.normalized,offset:i+t.offset,stride:i,type:t.type}),v.geometry.push({count:t.count,name:t.name+\"2\",divisor:t.divisor,normalized:t.normalized,offset:2*i+t.offset,stride:i,type:t.type}),n.set(t.name+\"1\",m+ ++f),n.set(t.name+\"2\",m+ ++f)}r={bufferLayouts:v,attributes:n,strides:t},this._computeDesc.set(a,r)}return r}}export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,unwrapOr as e}from\"../../../../../core/maybe.js\";import{WGLGeometryType as o,WGLSymbologyType as a}from\"../enums.js\";import{createProgramDescriptor as r}from\"../Utils.js\";import i from\"./WGLGeometryBrush.js\";import{MarkerMaterialKey as n}from\"../materialKey/MaterialKey.js\";import{getTechniqueFromMaterialKey as s}from\"../techniques/utils.js\";import{DataType as m,PrimitiveType as l}from\"../../../../webgl/enums.js\";const d={shader:\"materials/icon\",vertexLayout:{geometry:[{location:0,name:\"a_pos\",count:2,type:m.SHORT},{location:1,name:\"a_vertexOffset\",count:2,type:m.SHORT},{location:2,name:\"a_texCoords\",count:2,type:m.UNSIGNED_SHORT},{location:3,name:\"a_bitSetAndDistRatio\",count:2,type:m.UNSIGNED_SHORT},{location:4,name:\"a_id\",count:4,type:m.UNSIGNED_BYTE},{location:5,name:\"a_color\",count:4,type:m.UNSIGNED_BYTE,normalized:!0},{location:6,name:\"a_outlineColor\",count:4,type:m.UNSIGNED_BYTE,normalized:!0},{location:7,name:\"a_sizeAndOutlineWidth\",count:4,type:m.UNSIGNED_BYTE},{location:8,name:\"a_zoomRange\",count:2,type:m.UNSIGNED_SHORT}]},hittestAttributes:[\"a_vertexOffset\",\"a_texCoords\"]};class u extends i{dispose(){}getGeometryType(){return o.MARKER}supportsSymbology(t){return t!==a.HEATMAP&&t!==a.PIE_CHART}drawGeometry(o,a,i,m){const{context:u,painter:c,rendererInfo:p,state:y,passOptions:_,requestRender:f,allowDelayedRender:E}=o,S=n.load(i.materialKey),N=s(S.data),T=t(_)&&\"hittest\"===_.type,{shader:g,vertexLayout:x,hittestAttributes:R}=e(N.programSpec,d);let h=l.TRIANGLES,U=r(S.data,x);T&&(U=this._getTriangleDesc(i.materialKey,U,R),h=l.POINTS);const{attributes:A,bufferLayouts:O}=U,G=c.materialManager.getMaterialProgram(o,S,g,A,m);if(E&&t(f)&&!G.compiled)return void f();u.useProgram(G),S.textureBinding&&c.textureManager.bindTextures(u,G,S,!0),this._setSharedUniforms(G,o,a);const I=S.vvRotation?y.displayViewMat3:y.displayMat3;G.setUniformMatrix3fv(\"u_displayMat3\",I),this._setSizeVVUniforms(S,G,p,a),this._setColorAndOpacityVVUniforms(S,G,p),this._setRotationVVUniforms(S,G,p);const M=i.target.getVAO(u,O,A,T);let b=i.indexCount,D=i.indexFrom*Uint32Array.BYTES_PER_ELEMENT;T&&(b/=3,D/=3),u.bindVAO(M),this._drawMarkers(o,a,G,h,b,D,T),u.bindVAO(null)}_drawMarkers(t,e,o,a,r,i,n){t.context.drawElements(a,r,m.UNSIGNED_INT,i)}}export{u as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(){this.name=this.constructor.name}createOptions(t,r){return null}}export{t as Effect};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Error.js\";import{unwrapOrThrow as t}from\"../../core/maybe.js\";import{PixelType as r,TextureSamplingMode as a,PixelFormat as o,SizedPixelFormat as l}from\"./enums.js\";function n(n,i){const{textureFloat:f,colorBufferFloat:s}=n.capabilities,u=f?.textureFloat,m=f?.textureFloatLinear,_=f?.textureHalfFloat,p=f?.textureHalfFloatLinear,d=f?.HALF_FLOAT,x=s?.textureFloat,b=s?.textureHalfFloat,c=s?.floatBlend,h=t(n.driverTest).floatBufferBlend.result;if(!u&&!_)throw new e(\"heatmap:missing-texture-float\",\"HeatmapRenderer requires WebGL2 or the WebGL1 extension OES_texture_float or OES_texture_half_float.\");if(!x&&!b)throw new e(\"heatmap:missing-color-buffer-float\",\"HeatmapRenderer requires the WebGL extension EXT_color_buffer_float or EXT_color_buffer_half_float or WEBGL_color_buffer_float.\");if(!(c&&h||b))throw new e(\"heatmap:missing-float-blend\",\"HeatmapRenderer requires the WebGL extension EXT_float_blend or EXT_color_buffer_half_float.\"+(h?\"\":\" This device claims support for EXT_float_blend, but does not actually support it.\"));const E=u&&x&&c&&h,F=_&&b,R=m,L=p,T=!!s?.R32F,w=!!s?.R16F;if(E&&(R||!L))return R||i.warnOnce(\"Missing WebGL extension OES_texture_float_linear. Heatmap quality may be reduced.\"),{dataType:r.FLOAT,samplingMode:R?a.LINEAR:a.NEAREST,pixelFormat:T?o.RED:o.RGBA,internalFormat:T?l.R32F:o.RGBA};if(F)return L||i.warnOnce(\"Missing WebGL extension OES_texture_half_float_linear. Heatmap quality may be reduced.\"),{dataType:d,samplingMode:L?a.LINEAR:a.NEAREST,pixelFormat:w?o.RED:o.RGBA,internalFormat:w?l.R16F:o.RGBA};throw new e(\"heatmap:missing-hardware-support\",\"HeatmapRenderer requires WebGL extensions that allow it to render and blend to float or half float textures.\")}export{n as loadHeatmapTextureConfiguration};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/Logger.js\";import{disposeMaybe as t,isSome as r,isNone as i}from\"../../../../../core/maybe.js\";import{WGLSymbologyType as a}from\"../enums.js\";import s from\"../VertexStream.js\";import u from\"./WGLGeometryBrushMarker.js\";import{Effect as n}from\"../effects/Effect.js\";import{assertRendererSchema as o}from\"../techniques/utils.js\";import{ContextType as h}from\"../../../../webgl/context-util.js\";import{CompareFunction as c,DataType as l,BlendFactor as f,ClearBufferBit as m,TextureType as d,TextureWrapMode as p,RenderbufferFormat as _,PixelFormat as b,PixelType as g,TextureSamplingMode as F}from\"../../../../webgl/enums.js\";import{FramebufferObject as w}from\"../../../../webgl/FramebufferObject.js\";import{loadHeatmapTextureConfiguration as T}from\"../../../../webgl/heatmapTextureUtils.js\";import{Renderbuffer as E}from\"../../../../webgl/Renderbuffer.js\";import{Texture as O}from\"../../../../webgl/Texture.js\";const S=e.getLogger(\"esri.views.2d.engine.webgl.brushes.WGLBrushHeatmap\");function x(e){return\"heatmap\"===e.type}class B extends u{constructor(){super(...arguments),this.brushEffect=new y}supportsSymbology(e){return e===a.HEATMAP}dispose(){super.dispose(),this.brushEffect.dispose(),this.brushEffect=null}prepareState(){}drawGeometry(e,t,r,i){const{defines:a}=this.brushEffect.loadQualityProfile(e.context);super.drawGeometry(e,t,r,i?[...i,...a]:a)}_drawMarkers(e,t,r,i,a,s,u){const{context:n,rendererInfo:h,state:f}=e,{rendererSchema:m}=h;o(m,\"heatmap\");const{referenceScale:d,radius:p,isFieldActive:_}=m,b=p*(0!==d?d/f.scale:1);r.setUniform1f(\"u_radius\",b),u||(r.setUniform1f(\"u_isFieldActive\",_),n.setStencilFunction(c.GEQUAL,t.stencilRef,255)),n.drawElements(i,a,l.UNSIGNED_INT,s)}}const v={vsPath:\"heatmap/heatmapResolve\",fsPath:\"heatmap/heatmapResolve\",attributes:new Map([[\"a_position\",0]])};class y extends n{constructor(){super(...arguments),this.name=this.constructor.name}createOptions({passOptions:e}){return e}dispose(){this._prevFBO=null,this._accumulateOutputTexture=t(this._accumulateOutputTexture),r(this._accumulateFramebuffer)&&this._accumulateFramebuffer.detachDepthStencilBuffer(),this._accumulateOutputStencilBuffer=t(this._accumulateOutputStencilBuffer),this._accumulateFramebuffer=t(this._accumulateFramebuffer),this._resolveGradientTexture=t(this._resolveGradientTexture),this._tileQuad=t(this._tileQuad)}bind(e){const{context:t,rendererInfo:i,passOptions:a}=e,{rendererSchema:s}=i;!(r(a)&&\"hittest\"===a.type)&&x(s)&&(this._prevFBO=t.getBoundFramebufferObject(),this._prevViewport=t.getViewport(),o(s,\"heatmap\"),this._loadResources(e),this._updateResources(t,s),t.bindFramebuffer(this._accumulateFramebuffer),t.setViewport(0,0,this._accumulateFramebuffer.width,this._accumulateFramebuffer.height),t.setStencilTestEnabled(!0),t.setBlendingEnabled(!0),t.setBlendFunction(f.ONE,f.ONE),t.setClearColor(0,0,0,0),t.clear(m.COLOR_BUFFER_BIT))}unbind(){this._prevFBO=null,this._prevViewport=null}draw(e){const{context:t,painter:i,rendererInfo:a,passOptions:s}=e,{rendererSchema:u}=a;if(r(s)&&\"hittest\"===s.type||!x(u))return;const{defines:n}=this.loadQualityProfile(t),o=i.materialManager.getProgram(v,n);t.useProgram(o),t.bindFramebuffer(this._prevFBO),t.setViewport(0,0,this._prevViewport.width,this._prevViewport.height),t.setBlendFunction(f.ONE,f.ONE_MINUS_SRC_ALPHA),t.setStencilTestEnabled(!1);const{radius:h,minDensity:c,densityRange:l}=u;t.bindTexture(this._accumulateOutputTexture,8),t.bindTexture(this._resolveGradientTexture,9),o.setUniform1i(\"u_texture\",8),o.setUniform1i(\"u_gradient\",9),o.setUniform2f(\"u_densityMinAndInvRange\",c,1/l),o.setUniform1f(\"u_densityNormalization\",3/(h*h*Math.PI)),this._tileQuad.draw()}_loadResources({context:e,painter:t}){const{dataType:r,samplingMode:i,pixelFormat:a,internalFormat:u,shadingRate:n,requiresSharedStencilBuffer:o}=this.loadQualityProfile(e),{width:h,height:c}=this._prevViewport,l=h*n,f=c*n;this._accumulateOutputTexture??(this._accumulateOutputTexture=new O(e,{target:d.TEXTURE_2D,pixelFormat:a,internalFormat:u,dataType:r,samplingMode:i,wrapMode:p.CLAMP_TO_EDGE,width:l,height:f})),o||(this._accumulateOutputStencilBuffer??(this._accumulateOutputStencilBuffer=new E(e,{width:l,height:f,internalFormat:_.DEPTH_STENCIL}))),this._accumulateFramebuffer??(this._accumulateFramebuffer=new w(e,{},this._accumulateOutputTexture,o?t.getSharedStencilBuffer():this._accumulateOutputStencilBuffer)),this._resolveGradientTexture??(this._resolveGradientTexture=new O(e,{target:d.TEXTURE_2D,pixelFormat:b.RGBA,dataType:g.UNSIGNED_BYTE,samplingMode:F.LINEAR,wrapMode:p.CLAMP_TO_EDGE})),this._tileQuad??(this._tileQuad=new s(e,[0,0,1,0,0,1,1,1]))}_updateResources(e,t){const{gradientHash:i,gradient:a}=t;this._prevGradientHash!==i&&(this._resolveGradientTexture.resize(a.length/4,1),this._resolveGradientTexture.setData(a),this._prevGradientHash=i);const{shadingRate:s,requiresSharedStencilBuffer:u}=this.loadQualityProfile(e),{width:n,height:o}=this._prevViewport,h=n*s,c=o*s,{width:l,height:f}=this._accumulateFramebuffer;if(l!==h||f!==c){const e=this._accumulateFramebuffer.depthStencilAttachment;if(u&&r(e)){const{width:t,height:r}=e.descriptor;t===h&&r===c||(S.errorOnce(\"Attempted to resize shared stencil buffer! Detaching instead.\"),this._accumulateFramebuffer.detachDepthStencilBuffer())}this._accumulateFramebuffer.resize(h,c)}u||e.blitFramebuffer(this._prevFBO,this._accumulateFramebuffer,0,0,this._prevFBO.width,this._prevFBO.height,0,0,this._accumulateFramebuffer.width,this._accumulateFramebuffer.height,m.STENCIL_BUFFER_BIT,F.NEAREST)}loadQualityProfile(e){if(i(this._qualityProfile)){const t=T(e,S),r=e.type===h.WEBGL1;this._qualityProfile={...t,requiresSharedStencilBuffer:r,shadingRate:r?1:.25,defines:t.dataType!==g.FLOAT?[\"heatmapPrecisionHalfFloat\"]:[]}}return this._qualityProfile}}export{B as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{DataType as e}from\"../../../webgl/enums.js\";import{VertexElementDescriptor as o}from\"../../../webgl/VertexElementDescriptor.js\";const r={geometry:[new o(\"a_pos\",2,e.BYTE,0,2)]},t={geometry:[new o(\"a_pos\",2,e.BYTE,0,4),new o(\"a_tex\",2,e.BYTE,2,4)]},m={geometry:[new o(\"a_pos\",2,e.UNSIGNED_SHORT,0,4)]};export{r as Pos2b,m as Pos2us,t as PosTex2b};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{resolveIncludes as e}from\"./sources/resolver.js\";const r={shaders:{vertexShader:e(\"tileInfo/tileInfo.vert\"),fragmentShader:e(\"tileInfo/tileInfo.frag\")},attributes:new Map([[\"a_pos\",0]])};export{r as tileInfo};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as r,isSome as e}from\"../../../../../core/maybe.js\";import{f as t}from\"../../../../../chunks/vec4f32.js\";import{createProgramDescriptor as s}from\"../Utils.js\";import o from\"./WGLBrush.js\";import{background as a}from\"../shaders/BackgroundPrograms.js\";import{StencilOperation as i,CompareFunction as m,PrimitiveType as n,DataType as p}from\"../../../../webgl/enums.js\";import{createProgram as l}from\"../../../../webgl/ProgramTemplate.js\";const c=()=>s(\"clip\",{geometry:[{location:0,name:\"a_pos\",count:2,type:p.SHORT}]});class d extends o{constructor(){super(...arguments),this._color=t(0,1,0,1)}dispose(){this._program&&this._program.dispose()}prepareState({context:r}){r.setStencilTestEnabled(!0),r.setBlendingEnabled(!1),r.setFaceCullingEnabled(!1),r.setColorMask(!1,!1,!1,!1),r.setStencilOp(i.KEEP,i.KEEP,i.REPLACE),r.setStencilWriteMask(255),r.setStencilFunction(m.ALWAYS,0,255)}draw(t,s){const{context:o,state:i,requestRender:m,allowDelayedRender:d}=t,f=c(),g=s.getVAO(o,i,f.attributes,f.bufferLayouts);r(g.indexBuffer)||(this._program||(this._program=l(o,a)),d&&e(m)&&!this._program.compiled?m():(o.useProgram(this._program),this._program.setUniform2fv(\"u_coord_range\",[1,1]),this._program.setUniform4fv(\"u_color\",this._color),this._program.setUniformMatrix3fv(\"u_dvsMat3\",i.displayMat3),o.bindVAO(g),o.drawElements(n.TRIANGLES,g.indexBuffer.size,p.UNSIGNED_INT,0),o.bindVAO()))}}export{d as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../../core/maybe.js\";import{TEXTURE_BINDING_BITMAP as t}from\"../definitions.js\";import{createProgramDescriptor as r}from\"../Utils.js\";import s from\"./WGLBrush.js\";import{BlendFactor as o,CompareFunction as n,PrimitiveType as a,DataType as i}from\"../../../../webgl/enums.js\";const c=()=>r(\"overlay\",{geometry:[{location:0,name:\"a_pos\",count:2,type:i.FLOAT}],tex:[{location:1,name:\"a_uv\",count:2,type:i.UNSIGNED_SHORT}]});class m extends s{constructor(){super(...arguments),this._desc={vsPath:\"overlay/overlay\",fsPath:\"overlay/overlay\",attributes:new Map([[\"a_pos\",0],[\"a_uv\",1]])}}dispose(){}prepareState({context:e}){e.setBlendingEnabled(!0),e.setColorMask(!0,!0,!0,!0),e.setBlendFunctionSeparate(o.ONE,o.ONE_MINUS_SRC_ALPHA,o.ONE,o.ONE_MINUS_SRC_ALPHA),e.setStencilWriteMask(0),e.setStencilTestEnabled(!0),e.setStencilFunction(n.GREATER,255,255)}draw(r,s){const{context:o,painter:n,requestRender:i,allowDelayedRender:m}=r;if(!s.isReady)return;const{computedOpacity:u,dvsMat3:d,isWrapAround:l,perspectiveTransform:p,texture:f}=s;r.timeline.begin(this.name);const _=n.materialManager.getProgram(this._desc);if(m&&e(i)&&!_.compiled)return void i();const v=c(),y=s.getVAO(o,v.bufferLayouts,v.attributes);if(!y)return;o.bindVAO(y),o.useProgram(_),o.bindTexture(f,t),_.setUniformMatrix3fv(\"u_dvsMat3\",d),_.setUniform1i(\"u_texture\",t),_.setUniform1f(\"u_opacity\",u),_.setUniform2fv(\"u_perspective\",p);const A=l?10:4;o.drawArrays(a.TRIANGLE_STRIP,0,A),o.bindVAO(),r.timeline.end(this.name)}}export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e,unwrapOr as t}from\"../../../../../core/maybe.js\";import{WGLGeometryType as o,WGLSymbologyType as r}from\"../enums.js\";import{createProgramDescriptor as a}from\"../Utils.js\";import i from\"./WGLGeometryBrush.js\";import{FillMaterialKey as n}from\"../materialKey/MaterialKey.js\";import{getTechniqueFromMaterialKey as s}from\"../techniques/utils.js\";import{PrimitiveType as m,DataType as l}from\"../../../../webgl/enums.js\";function u(e){const t={geometry:[{location:0,name:\"a_pos\",count:2,type:l.SHORT},{location:1,name:\"a_id\",count:3,type:l.UNSIGNED_BYTE},{location:2,name:\"a_bitset\",count:1,type:l.UNSIGNED_BYTE},{location:3,name:\"a_color\",count:4,type:l.UNSIGNED_BYTE,normalized:!0},{location:4,name:\"a_aux1\",count:4,type:l.UNSIGNED_SHORT},{location:5,name:\"a_aux2\",count:4,type:l.SHORT},{location:6,name:\"a_aux3\",count:4,type:l.UNSIGNED_BYTE},{location:7,name:\"a_zoomRange\",count:2,type:l.UNSIGNED_SHORT}]};switch(e.symbologyType){case r.SIMPLE:case r.OUTLINE_FILL_SIMPLE:t.geometry.splice(7,1),t.geometry.splice(4,1)}return{shader:\"materials/fill\",vertexLayout:t}}class c extends i{dispose(){}getGeometryType(){return o.FILL}supportsSymbology(e){return e!==r.DOT_DENSITY}drawGeometry(o,r,i,l){const{context:c,painter:y,rendererInfo:p,requiredLevel:_,passOptions:d,requestRender:f,allowDelayedRender:E}=o,S=n.load(i.materialKey),g=s(S.data),N=e(d)&&\"hittest\"===d.type,T=y.materialManager,{shader:I,vertexLayout:U,hittestAttributes:x}=t(g.programSpec,u(S));let L=m.TRIANGLES,h=a(S.data,U);N&&(h=this._getTriangleDesc(i.materialKey,h,x),L=m.POINTS);const{attributes:G,bufferLayouts:O}=h,b=T.getMaterialProgram(o,S,I,G,l);if(E&&e(f)&&!b.compiled)return void f();if(c.useProgram(b),this._setSharedUniforms(b,o,r),b.setUniform2f(\"u_tileOffset\",512*r.key.col,512*r.key.row),S.textureBinding){y.textureManager.bindTextures(c,b,S);const e=1/2**(_-r.key.level);b.setUniform1f(\"u_zoomFactor\",e)}const D=1/o.pixelRatio;b.setUniform1f(\"u_blur\",D),b.setUniform1f(\"u_antialiasing\",D),this._setSizeVVUniforms(S,b,p,r),this._setColorAndOpacityVVUniforms(S,b,p);const R=i.target.getVAO(c,O,G,N);let w=i.indexCount,j=i.indexFrom*Uint32Array.BYTES_PER_ELEMENT;N&&(w/=3,j/=3),c.bindVAO(R),this._drawFills(o,r,b,L,w,j)}_drawFills(e,t,o,r,a,i){e.context.drawElements(r,a,l.UNSIGNED_INT,i)}}export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{dispose<PERSON>aybe as e,isSome as t,isNone as o}from\"../../../../../core/maybe.js\";import r from\"../../../../../core/RandomLCG.js\";import{TEXTURE_BINDING_RENDERER_0 as s,TEXTURE_BINDING_RENDERER_1 as i,TILE_SIZE as n}from\"../definitions.js\";import{WGLGeometryType as d,WGLSymbologyType as a}from\"../enums.js\";import l from\"./WGLGeometryBrushFill.js\";import{assertRendererSchema as _}from\"../techniques/utils.js\";import{BufferObject as u}from\"../../../../webgl/BufferObject.js\";import{DataType as m,PrimitiveType as h,TextureType as c,PixelFormat as f,PixelType as T,TextureSamplingMode as p,TextureWrapMode as x,TargetType as w,DepthStencilTargetType as D,RenderbufferFormat as y,Usage as g}from\"../../../../webgl/enums.js\";import{FramebufferObject as S}from\"../../../../webgl/FramebufferObject.js\";import{Renderbuffer as b}from\"../../../../webgl/Renderbuffer.js\";import{Texture as E}from\"../../../../webgl/Texture.js\";import{VertexArrayObject as F}from\"../../../../webgl/VertexArrayObject.js\";class O extends l{constructor(){super(...arguments),this._dotTextureSize=0,this._dotTextures=null,this._dotSamplers=new Int32Array([s,i]),this._dotVAO=null,this._dotDesc={vsPath:\"dot/dot\",fsPath:\"dot/dot\",attributes:new Map([[\"a_pos\",0]])}}dispose(){super.dispose(),this._disposeTextures(),this._dotFBO=e(this._dotFBO),this._dotVAO=e(this._dotVAO)}getGeometryType(){return d.FILL}supportsSymbology(e){return e===a.DOT_DENSITY}_drawFills(e,o,r,s,i,n){const{passOptions:d}=e;if(t(d)&&\"hittest\"===d.type)super._drawFills(e,o,r,s,i,n);else{const t=this._drawDotLocations(e,o,r,i,n);this._drawDotDensity(e,o,t)}}_drawDotDensity(e,o,r){const{context:s,painter:i,rendererInfo:n,requestRender:d,allowDelayedRender:a}=e,l=i.materialManager.getProgram(this._dotDesc);if(a&&t(d)&&!l.compiled)return void d();const{rendererSchema:u}=n;_(u,\"dot-density\");const c=this._createDotDensityMesh(s,this._dotDesc.attributes,{geometry:[{name:\"a_pos\",count:2,type:m.SHORT,divisor:0,normalized:!1,offset:0,stride:4}]});s.setStencilTestEnabled(!0),s.useProgram(l),l.setUniform1f(\"u_tileZoomFactor\",1),l.setUniform1i(\"u_texture\",this._dotSamplers[0]),l.setUniform1f(\"u_dotSize\",Math.max(u.dotSize,1)),l.setUniform1f(\"u_pixelRatio\",window.devicePixelRatio),this._setSharedUniforms(l,e,o),s.bindTexture(r,this._dotSamplers[0]),s.bindVAO(c),s.drawArrays(h.POINTS,0,262144)}_drawDotLocations(e,t,o,r,s){const{context:i,rendererInfo:d,requiredLevel:a}=e,l=i.getViewport(),{rendererSchema:u}=d;_(u,\"dot-density\");const{dotScale:c,colors:f,activeDots:T,backgroundColor:p,dotValue:x}=u;i.setViewport(0,0,512,512);const w=i.getBoundFramebufferObject(),D=this._createFBO(i);i.bindFramebuffer(D),i.setClearColor(0,0,0,0),i.clear(i.gl.COLOR_BUFFER_BIT|i.gl.STENCIL_BUFFER_BIT),i.setStencilTestEnabled(!1);const y=1/2**(a-t.key.level),g=n,S=g*window.devicePixelRatio*g*window.devicePixelRatio,b=1/y*(1/y),E=c?e.state.scale/c:1;return o.setUniform1f(\"u_tileZoomFactor\",y),o.setUniform1f(\"u_tileDotsOverArea\",S/(n*window.devicePixelRatio*n*window.devicePixelRatio)),o.setUniformMatrix4fv(\"u_dotColors\",f),o.setUniform4fv(\"u_isActive\",T),o.setUniform4fv(\"u_dotBackgroundColor\",p),o.setUniform1f(\"u_dotValue\",Math.max(1,x*E*b)),this._bindDotDensityTextures(i,o,d,g),i.drawElements(h.TRIANGLES,r,m.UNSIGNED_INT,s),i.setViewport(l.x,l.y,l.width,l.height),i.bindFramebuffer(w),D.colorTexture}_createFBO(e){if(o(this._dotFBO)){const t=512,o=512,r={target:c.TEXTURE_2D,pixelFormat:f.RGBA,dataType:T.UNSIGNED_BYTE,samplingMode:p.NEAREST,wrapMode:x.CLAMP_TO_EDGE,width:t,height:o},s={colorTarget:w.TEXTURE,depthStencilTarget:D.DEPTH_STENCIL_RENDER_BUFFER},i=new b(e,{width:t,height:o,internalFormat:y.DEPTH_STENCIL});this._dotFBO=new S(e,s,r,i)}return this._dotFBO}_disposeTextures(){if(this._dotTextures){for(let e=0;e<this._dotTextures.length;e++)this._dotTextures[e].dispose();this._dotTextures=null}}_bindDotDensityTextures(e,t,o,r){const{rendererSchema:s}=o;_(s,\"dot-density\");const i=this._createDotDensityTextures(e,r,s.seed);t.setUniform1iv(\"u_dotTextures\",this._dotSamplers);for(let n=0;n<i.length;n++)e.bindTexture(i[n],this._dotSamplers[n])}_createDotDensityMesh(e,t,r){if(o(this._dotVAO)){const o=2,s=new Int16Array(262144*o);for(let e=0;e<512;e++)for(let t=0;t<512;t++)s[o*(t+512*e)]=t,s[o*(t+512*e)+1]=e;const i=u.createVertex(e,g.STATIC_DRAW,s);this._dotVAO=new F(e,t,r,{geometry:i},null)}return this._dotVAO}_createDotDensityTextures(e,t,o){if(this._dotTextureSize===t&&this._seed===o||(this._disposeTextures(),this._dotTextureSize=t,this._seed=o),null===this._dotTextures){const s=new r(o);this._dotTextures=[this._allocDotDensityTexture(e,t,s),this._allocDotDensityTexture(e,t,s)]}return this._dotTextures}_allocDotDensityTexture(e,t,o){const r=new Float32Array(t*t*4);for(let s=0;s<r.length;s++)r[s]=o.getFloat();return new E(e,{wrapMode:x.REPEAT,pixelFormat:f.RGBA,dataType:T.FLOAT,samplingMode:p.NEAREST,width:t,height:t},r)}}export{O as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../../../core/maybe.js\";import{f as e}from\"../../../../../chunks/vec4f32.js\";import{Pos2b as r}from\"../DefaultVertexAttributeLayouts.js\";import i from\"./WGLBrush.js\";import{background as o}from\"../shaders/BackgroundPrograms.js\";import{tileInfo as s}from\"../shaders/TileInfoPrograms.js\";import{BufferObject as n}from\"../../../../webgl/BufferObject.js\";import{BlendFactor as a,PrimitiveType as l,Usage as u,TextureType as m,PixelFormat as _,PixelType as f,TextureSamplingMode as c,TextureWrapMode as d}from\"../../../../webgl/enums.js\";import{createProgram as g}from\"../../../../webgl/ProgramTemplate.js\";import{Texture as h}from\"../../../../webgl/Texture.js\";import{VertexArrayObject as p}from\"../../../../webgl/VertexArrayObject.js\";const A=300,b=32;class x extends i{constructor(){super(...arguments),this._color=e(1,0,0,1)}dispose(){this._outlineProgram?.dispose(),this._outlineProgram=null,this._tileInfoProgram?.dispose(),this._tileInfoProgram=null,this._outlineVertexArrayObject?.dispose(),this._outlineVertexArrayObject=null,this._tileInfoVertexArrayObject?.dispose(),this._tileInfoVertexArrayObject=null,this._canvas=null}prepareState({context:t}){t.setBlendingEnabled(!0),t.setBlendFunctionSeparate(a.ONE,a.ONE_MINUS_SRC_ALPHA,a.ONE,a.ONE_MINUS_SRC_ALPHA),t.setColorMask(!0,!0,!0,!0),t.setStencilWriteMask(0),t.setStencilTestEnabled(!1)}draw(e,r){const{context:i,requestRender:o,allowDelayedRender:s}=e;if(!r.isReady)return;if(this._loadWGLResources(i),s&&t(o)&&(!this._outlineProgram.compiled||!this._tileInfoProgram.compiled))return void o();i.bindVAO(this._outlineVertexArrayObject),i.useProgram(this._outlineProgram),this._outlineProgram.setUniformMatrix3fv(\"u_dvsMat3\",r.transforms.dvs),this._outlineProgram.setUniform2f(\"u_coord_range\",r.rangeX,r.rangeY),this._outlineProgram.setUniform1f(\"u_depth\",0),this._outlineProgram.setUniform4fv(\"u_color\",this._color),i.drawArrays(l.LINE_STRIP,0,4);const n=this._getTexture(i,r);n?(i.bindVAO(this._tileInfoVertexArrayObject),i.useProgram(this._tileInfoProgram),i.bindTexture(n,0),this._tileInfoProgram.setUniformMatrix3fv(\"u_dvsMat3\",r.transforms.dvs),this._tileInfoProgram.setUniform1f(\"u_depth\",0),this._tileInfoProgram.setUniform2f(\"u_coord_ratio\",r.rangeX/r.width,r.rangeY/r.height),this._tileInfoProgram.setUniform2f(\"u_delta\",8,8),this._tileInfoProgram.setUniform2f(\"u_dimensions\",n.descriptor.width,n.descriptor.height),i.drawArrays(l.TRIANGLE_STRIP,0,4),i.bindVAO()):i.bindVAO()}_loadWGLResources(t){if(this._outlineProgram&&this._tileInfoProgram)return;const e=g(t,o),i=g(t,s),a=new Int8Array([0,0,1,0,1,1,0,1]),l=n.createVertex(t,u.STATIC_DRAW,a),m=new p(t,o.attributes,r,{geometry:l}),_=new Int8Array([0,0,1,0,0,1,1,1]),f=n.createVertex(t,u.STATIC_DRAW,_),c=new p(t,s.attributes,r,{geometry:f});this._outlineProgram=e,this._tileInfoProgram=i,this._outlineVertexArrayObject=m,this._tileInfoVertexArrayObject=c}_getTexture(t,e){if(e.texture&&e.triangleCountReportedInDebug===e.triangleCount)return e.texture;e.triangleCountReportedInDebug=e.triangleCount,this._canvas||(this._canvas=document.createElement(\"canvas\"),this._canvas.setAttribute(\"id\",\"canvas2d\"),this._canvas.setAttribute(\"width\",`${A}`),this._canvas.setAttribute(\"height\",`${b}`),this._canvas.setAttribute(\"style\",\"display:none\"));const r=e.triangleCount;let i=e.key.id;e.triangleCount>0&&(i+=`, ${r}`);const o=this._canvas,s=o.getContext(\"2d\");return s.font=\"24px sans-serif\",s.textAlign=\"left\",s.textBaseline=\"top\",s.clearRect(0,0,A,b),r>1e5?(s.fillStyle=\"red\",s.fillRect(0,0,A,b),s.fillStyle=\"black\"):(s.clearRect(0,0,A,b),s.fillStyle=\"blue\"),s.fillText(i,0,0),e.texture=new h(t,{target:m.TEXTURE_2D,pixelFormat:_.RGBA,dataType:f.UNSIGNED_BYTE,samplingMode:c.NEAREST,wrapMode:d.CLAMP_TO_EDGE},o),e.texture}}export{x as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{WGLSymbologyType as o}from\"../enums.js\";import r from\"./WGLGeometryBrushMarker.js\";import{assertRendererSchema as e}from\"../techniques/utils.js\";import{DataType as t}from\"../../../../webgl/enums.js\";class s extends r{supportsSymbology(r){return r===o.PIE_CHART}_drawMarkers(o,r,s,n,l,f,u){const{context:i}=o,{rendererInfo:m}=o,{rendererSchema:a}=m;e(a,\"pie-chart\"),s.setUniform4fv(\"u_colors\",a.colors),s.setUniform4fv(\"u_defaultColor\",a.defaultColor),s.setUniform4fv(\"u_othersColor\",a.othersColor),s.setUniform4fv(\"u_outlineColor\",a.outlineColor),s.setUniform1f(\"u_donutRatio\",a.holePercentage),s.setUniform1f(\"u_sectorThreshold\",a.sectorThreshold),s.setUniform1f(\"u_outlineWidth\",a.outlineWidth),i.drawElements(n,l,t.UNSIGNED_INT,f)}}export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as r}from\"../../../../../core/maybe.js\";import{f as e}from\"../../../../../chunks/vec4f32.js\";import{Pos2b as t}from\"../DefaultVertexAttributeLayouts.js\";import s from\"./WGLBrush.js\";import{background as i}from\"../shaders/BackgroundPrograms.js\";import{BufferObject as o}from\"../../../../webgl/BufferObject.js\";import{StencilOperation as a,Face as l,CompareFunction as n,PrimitiveType as d,Usage as m}from\"../../../../webgl/enums.js\";import{createProgram as c}from\"../../../../webgl/ProgramTemplate.js\";import{VertexArrayObject as _}from\"../../../../webgl/VertexArrayObject.js\";class h extends s{constructor(){super(...arguments),this._color=e(1,0,0,1),this._initialized=!1}dispose(){this._solidProgram&&(this._solidProgram.dispose(),this._solidProgram=null),this._solidVertexArrayObject&&(this._solidVertexArrayObject.dispose(),this._solidVertexArrayObject=null)}prepareState({context:r}){r.setDepthWriteEnabled(!1),r.setDepthTestEnabled(!1),r.setStencilTestEnabled(!0),r.setBlendingEnabled(!1),r.setColorMask(!1,!1,!1,!1),r.setStencilOp(a.KEEP,a.KEEP,a.REPLACE),r.setStencilWriteMask(255)}draw(e,t){const{context:s,requestRender:i,allowDelayedRender:o}=e;this._initialized||this._initialize(s),o&&r(i)&&!this._solidProgram.compiled?i():(s.setStencilFunctionSeparate(l.FRONT_AND_BACK,n.GREATER,t.stencilRef,255),s.bindVAO(this._solidVertexArrayObject),s.useProgram(this._solidProgram),this._solidProgram.setUniformMatrix3fv(\"u_dvsMat3\",t.transforms.dvs),this._solidProgram.setUniform2fv(\"u_coord_range\",[t.rangeX,t.rangeY]),this._solidProgram.setUniform1f(\"u_depth\",0),this._solidProgram.setUniform4fv(\"u_color\",this._color),s.drawArrays(d.TRIANGLE_STRIP,0,4),s.bindVAO())}_initialize(r){if(this._initialized)return!0;const e=c(r,i);if(!e)return!1;const s=new Int8Array([0,0,1,0,0,1,1,1]),a=o.createVertex(r,m.STATIC_DRAW,s),l=new _(r,i.attributes,t,{geometry:a});return this._solidProgram=e,this._solidVertexArrayObject=l,this._initialized=!0,!0}}export{h as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{nextPowerOfTwo as t}from\"../../../../../core/mathUtils.js\";import{isSome as r}from\"../../../../../core/maybe.js\";import{c as e}from\"../../../../../chunks/mat3f32.js\";import{f as o}from\"../../../../../chunks/vec4f32.js\";import{VTL_TEXTURE_BINDING_UNIT_SPRITES as i,VTL_HIGH_RES_CUTOFF as s}from\"../definitions.js\";import{WGLDrawPhase as a}from\"../enums.js\";import{u32to4Xu8 as n}from\"../number.js\";import c from\"./WGLBrush.js\";import{BufferObject as m}from\"../../../../webgl/BufferObject.js\";import{TextureSamplingMode as f,CompareFunction as l,PrimitiveType as u,Usage as p}from\"../../../../webgl/enums.js\";import{VertexArrayObject as _}from\"../../../../webgl/VertexArrayObject.js\";class d extends c{constructor(){super(...arguments),this._color=o(1,0,0,1),this._patternMatrix=e(),this._programOptions={id:!1,pattern:!1}}dispose(){this._vao&&(this._vao.dispose(),this._vao=null)}drawMany(e,o){const{context:c,painter:m,styleLayerUID:p,requestRender:_,allowDelayedRender:d}=e;this._loadWGLResources(e);const h=e.displayLevel,g=e.styleLayer,v=g.backgroundMaterial,y=m.vectorTilesMaterialManager,b=g.getPaintValue(\"background-color\",h),x=g.getPaintValue(\"background-opacity\",h),M=g.getPaintValue(\"background-pattern\",h),j=void 0!==M,U=b[3]*x,w=1|window.devicePixelRatio,L=e.spriteMosaic;let A,P;const I=w>s?2:1,R=e.drawPhase===a.HITTEST,k=this._programOptions;k.id=R,k.pattern=j;const T=y.getMaterialProgram(c,v,k);if(d&&r(_)&&!T.compiled)_();else{if(c.bindVAO(this._vao),c.useProgram(T),j){const t=L.getMosaicItemPosition(M,!0);if(r(t)){const{tl:e,br:o,page:s}=t;A=o[0]-e[0],P=o[1]-e[1];const a=L.getPageSize(s);r(a)&&(L.bind(c,f.LINEAR,s,i),T.setUniform4f(\"u_tlbr\",e[0],e[1],o[0],o[1]),T.setUniform2fv(\"u_mosaicSize\",a),T.setUniform1i(\"u_texture\",i))}T.setUniform1f(\"u_opacity\",x)}else this._color[0]=U*b[0],this._color[1]=U*b[1],this._color[2]=U*b[2],this._color[3]=U,T.setUniform4fv(\"u_color\",this._color);if(T.setUniform1f(\"u_depth\",g.z||0),R){const t=n(p+1);T.setUniform4fv(\"u_id\",t)}for(const r of o){if(T.setUniform1f(\"u_coord_range\",r.rangeX),T.setUniformMatrix3fv(\"u_dvsMat3\",r.transforms.dvs),j){const e=Math.max(2**(Math.round(h)-r.key.level),1),o=I*r.width*e,i=o/t(A),s=o/t(P);this._patternMatrix[0]=i,this._patternMatrix[4]=s,T.setUniformMatrix3fv(\"u_pattern_matrix\",this._patternMatrix)}c.setStencilFunction(l.EQUAL,0,255),c.drawArrays(u.TRIANGLE_STRIP,0,4)}}}_loadWGLResources(t){if(this._vao)return;const{context:r,styleLayer:e}=t,o=e.backgroundMaterial,i=new Int8Array([0,0,1,0,0,1,1,1]),s=m.createVertex(r,p.STATIC_DRAW,i),a=new _(r,o.getAttributeLocations(),o.getLayoutInfo(),{geometry:s});this._vao=a}}export{d as WGLBrushVTLBackground};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e,isNone as t}from\"../../../../../core/maybe.js\";import{TranslateAnchor as r}from\"../../vectorTiles/style/StyleDefinition.js\";import{WGLDrawPhase as i}from\"../enums.js\";import{u32to4Xu8 as a}from\"../number.js\";import n from\"./WGLBrush.js\";import{CompareFunction as s,PrimitiveType as o,DataType as l}from\"../../../../webgl/enums.js\";class c extends n{constructor(){super(...arguments),this._programOptions={id:!1}}dispose(){}drawMany(n,c){const{context:m,displayLevel:d,requiredLevel:f,state:u,drawPhase:p,painter:y,spriteMosaic:g,styleLayerUID:v,requestRender:E,allowDelayedRender:M}=n;if(!c.some((e=>e.layerData.get(v)?.circleIndexCount??!1)))return;const T=n.styleLayer,x=T.circleMaterial,I=y.vectorTilesMaterialManager,U=1.2,_=T.getPaintValue(\"circle-translate\",d),R=T.getPaintValue(\"circle-translate-anchor\",d),h=p===i.HITTEST,D=this._programOptions;D.id=h;const L=I.getMaterialProgram(m,x,D);if(M&&e(E)&&!L.compiled)return void E();m.useProgram(L),L.setUniformMatrix3fv(\"u_displayMat3\",R===r.VIEWPORT?u.displayMat3:u.displayViewMat3),L.setUniform2fv(\"u_circleTranslation\",_),L.setUniform1f(\"u_depth\",T.z),L.setUniform1f(\"u_antialiasingWidth\",U);let S=-1;if(h){const e=a(v+1);L.setUniform4fv(\"u_id\",e)}for(const e of c){if(!e.layerData.has(v))continue;e.key.level!==S&&(S=e.key.level,x.setDataUniforms(L,d,T,S,g));const r=e.layerData.get(v);if(!r.circleIndexCount)continue;r.prepareForRendering(m);const i=r.circleVertexArrayObject;t(i)||(m.bindVAO(i),L.setUniformMatrix3fv(\"u_dvsMat3\",e.transforms.dvs),f!==e.key.level?m.setStencilFunction(s.EQUAL,e.stencilRef,255):m.setStencilFunction(s.GREATER,255,255),m.drawElements(o.TRIANGLES,r.circleIndexCount,l.UNSIGNED_INT,Uint32Array.BYTES_PER_ELEMENT*r.circleIndexStart),e.triangleCount+=r.circleIndexCount/3)}}}export{c as WGLBrushVTLCircle};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t,isSome as e}from\"../../../../../core/maybe.js\";import{TranslateAnchor as i}from\"../../vectorTiles/style/StyleDefinition.js\";import{VTL_TEXTURE_BINDING_UNIT_SPRITES as r,VTL_HIGH_RES_CUTOFF as a}from\"../definitions.js\";import{WGLDrawPhase as n}from\"../enums.js\";import{u32to4Xu8 as o}from\"../number.js\";import l from\"./WGLBrush.js\";import{TextureSamplingMode as s,CompareFunction as f,PrimitiveType as u,DataType as d}from\"../../../../webgl/enums.js\";const c=1/65536;class m extends l{constructor(){super(...arguments),this._fillProgramOptions={id:!1,pattern:!1},this._outlineProgramOptions={id:!1}}dispose(){}drawMany(t,e){const{displayLevel:i,drawPhase:r,renderPass:a,spriteMosaic:l,styleLayerUID:s}=t;let f=!1;for(const n of e)if(n.layerData.has(s)){const t=n.layerData.get(s);if(t.fillIndexCount>0||t.outlineIndexCount>0){f=!0;break}}if(!f)return;const u=t.styleLayer,d=u.getPaintProperty(\"fill-pattern\"),c=void 0!==d,m=c&&d.isDataDriven;let p;if(c&&!m){const t=d.getValue(i);p=l.getMosaicItemPosition(t,!0)}const y=!c&&u.getPaintValue(\"fill-antialias\",i);let g=!0,_=1;if(!c){const t=u.getPaintProperty(\"fill-color\"),e=u.getPaintProperty(\"fill-opacity\");if(!t?.isDataDriven&&!e?.isDataDriven){const t=u.getPaintValue(\"fill-color\",i);_=u.getPaintValue(\"fill-opacity\",i)*t[3],_>=1&&(g=!1)}}if(g&&\"opaque\"===a)return;let E;r===n.HITTEST&&(E=o(s+1));const v=u.getPaintValue(\"fill-translate\",i),M=u.getPaintValue(\"fill-translate-anchor\",i);(g||\"translucent\"!==a)&&this._drawFill(t,s,u,e,v,M,c,p,m,E);const P=!u.hasDataDrivenOutlineColor&&u.outlineUsesFillColor&&_<1;y&&\"opaque\"!==a&&!P&&this._drawOutline(t,s,u,e,v,M,E)}_drawFill(o,l,m,p,y,g,_,E,v,M){if(_&&!v&&t(E))return;const{context:P,displayLevel:I,state:T,drawPhase:U,painter:x,pixelRatio:D,spriteMosaic:h,requestRender:S,allowDelayedRender:R}=o,w=m.fillMaterial,N=x.vectorTilesMaterialManager,L=D>a?2:1,A=U===n.HITTEST,V=this._fillProgramOptions;V.id=A,V.pattern=_;const O=N.getMaterialProgram(P,w,V);if(R&&e(S)&&!O.compiled)return void S();if(P.useProgram(O),e(E)){const{page:t}=E,i=h.getPageSize(t);e(i)&&(h.bind(P,s.LINEAR,t,r),O.setUniform2fv(\"u_mosaicSize\",i),O.setUniform1i(\"u_texture\",r))}O.setUniformMatrix3fv(\"u_displayMat3\",g===i.VIEWPORT?T.displayMat3:T.displayViewMat3),O.setUniform2fv(\"u_fillTranslation\",y),O.setUniform1f(\"u_depth\",m.z+c),A&&O.setUniform4fv(\"u_id\",M);let C=-1;for(const i of p){if(!i.layerData.has(l))continue;i.key.level!==C&&(C=i.key.level,w.setDataUniforms(O,I,m,C,h));const a=i.layerData.get(l);if(!a.fillIndexCount)continue;a.prepareForRendering(P);const n=a.fillVertexArrayObject;if(!t(n)){if(P.bindVAO(n),O.setUniformMatrix3fv(\"u_dvsMat3\",i.transforms.dvs),P.setStencilFunction(f.EQUAL,i.stencilRef,255),_){const t=Math.max(2**(Math.round(I)-i.key.level),1),e=i.rangeX/(L*i.width*t);O.setUniform1f(\"u_patternFactor\",e)}if(v){const t=a.patternMap;if(!t)continue;for(const[i,a]of t){const t=h.getPageSize(i);e(t)&&(h.bind(P,s.LINEAR,i,r),O.setUniform2fv(\"u_mosaicSize\",t),O.setUniform1i(\"u_texture\",r),P.drawElements(u.TRIANGLES,a[1],d.UNSIGNED_INT,Uint32Array.BYTES_PER_ELEMENT*a[0]))}}else P.drawElements(u.TRIANGLES,a.fillIndexCount,d.UNSIGNED_INT,Uint32Array.BYTES_PER_ELEMENT*a.fillIndexStart);i.triangleCount+=a.fillIndexCount/3}}}_drawOutline(r,a,o,l,s,m,p){const{context:y,displayLevel:g,state:_,drawPhase:E,painter:v,pixelRatio:M,spriteMosaic:P,requestRender:I,allowDelayedRender:T}=r,U=o.outlineMaterial,x=v.vectorTilesMaterialManager,D=.75/M,h=E===n.HITTEST,S=this._outlineProgramOptions;S.id=h;const R=x.getMaterialProgram(y,U,S);if(T&&e(I)&&!R.compiled)return void I();y.useProgram(R),R.setUniformMatrix3fv(\"u_displayMat3\",m===i.VIEWPORT?_.displayMat3:_.displayViewMat3),R.setUniform2fv(\"u_fillTranslation\",s),R.setUniform1f(\"u_depth\",o.z+c),R.setUniform1f(\"u_outline_width\",D),h&&R.setUniform4fv(\"u_id\",p);let w=-1;for(const e of l){if(!e.layerData.has(a))continue;e.key.level!==w&&(w=e.key.level,U.setDataUniforms(R,g,o,w,P));const i=e.layerData.get(a);if(i.prepareForRendering(y),!i.outlineIndexCount)continue;const r=i.outlineVertexArrayObject;t(r)||(y.bindVAO(r),R.setUniformMatrix3fv(\"u_dvsMat3\",e.transforms.dvs),y.setStencilFunction(f.EQUAL,e.stencilRef,255),y.drawElements(u.TRIANGLES,i.outlineIndexCount,d.UNSIGNED_INT,Uint32Array.BYTES_PER_ELEMENT*i.outlineIndexStart),e.triangleCount+=i.outlineIndexCount/3)}}}export{m as WGLBrushVTLFill};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e,isNone as t}from\"../../../../../core/maybe.js\";import{TranslateAnchor as i}from\"../../vectorTiles/style/StyleDefinition.js\";import{VTL_TEXTURE_BINDING_UNIT_SPRITES as n}from\"../definitions.js\";import{WGLDrawPhase as r}from\"../enums.js\";import{u32to4Xu8 as a}from\"../number.js\";import o from\"./WGLBrush.js\";import{TextureSamplingMode as s,CompareFunction as l,PrimitiveType as f,DataType as m}from\"../../../../webgl/enums.js\";class c extends o{constructor(){super(...arguments),this._programOptions={id:!1,pattern:!1,sdf:!1}}dispose(){}drawMany(o,c){const{context:u,displayLevel:d,state:p,drawPhase:y,painter:g,pixelRatio:E,spriteMosaic:M,styleLayerUID:_,requestRender:v,allowDelayedRender:U}=o;if(!c.some((e=>e.layerData.get(_)?.lineIndexCount??!1)))return;const I=o.styleLayer,P=I.lineMaterial,x=g.vectorTilesMaterialManager,T=I.getPaintValue(\"line-translate\",d),D=I.getPaintValue(\"line-translate-anchor\",d),S=I.getPaintProperty(\"line-pattern\"),L=void 0!==S,N=L&&S.isDataDriven;let R,V;if(L&&!N){const e=S.getValue(d);R=M.getMosaicItemPosition(e)}let h=!1;if(!L){const e=I.getPaintProperty(\"line-dasharray\");if(V=void 0!==e,h=V&&e.isDataDriven,V&&!h){const t=e.getValue(d),i=I.getDashKey(t,I.getLayoutValue(\"line-cap\",d));R=M.getMosaicItemPosition(i)}}const w=1/E,A=y===r.HITTEST,j=this._programOptions;j.id=A,j.pattern=L,j.sdf=V;const b=x.getMaterialProgram(u,P,j);if(U&&e(v)&&!b.compiled)return void v();if(u.useProgram(b),b.setUniformMatrix3fv(\"u_displayViewMat3\",p.displayViewMat3),b.setUniformMatrix3fv(\"u_displayMat3\",D===i.VIEWPORT?p.displayMat3:p.displayViewMat3),b.setUniform2fv(\"u_lineTranslation\",T),b.setUniform1f(\"u_depth\",I.z),b.setUniform1f(\"u_antialiasing\",w),A){const e=a(_+1);b.setUniform4fv(\"u_id\",e)}if(R&&e(R)){const{page:t}=R,i=M.getPageSize(t);e(i)&&(M.bind(u,s.LINEAR,t,n),b.setUniform2fv(\"u_mosaicSize\",i),b.setUniform1i(\"u_texture\",n))}let z=-1;for(const i of c){if(!i.layerData.has(_))continue;i.key.level!==z&&(z=i.key.level,P.setDataUniforms(b,d,I,z,M));const r=2**(d-z)/E;b.setUniform1f(\"u_zoomFactor\",r);const a=i.layerData.get(_);if(!a.lineIndexCount)continue;a.prepareForRendering(u);const o=a.lineVertexArrayObject;if(!t(o)){if(u.bindVAO(o),b.setUniformMatrix3fv(\"u_dvsMat3\",i.transforms.dvs),u.setStencilFunction(l.EQUAL,i.stencilRef,255),N||h){const t=a.patternMap;if(!t)continue;for(const[i,r]of t){const t=M.getPageSize(i);e(t)&&(M.bind(u,s.LINEAR,i,n),b.setUniform2fv(\"u_mosaicSize\",t),b.setUniform1i(\"u_texture\",n),u.drawElements(f.TRIANGLES,r[1],m.UNSIGNED_INT,Uint32Array.BYTES_PER_ELEMENT*r[0]))}}else u.drawElements(f.TRIANGLES,a.lineIndexCount,m.UNSIGNED_INT,Uint32Array.BYTES_PER_ELEMENT*a.lineIndexStart);i.triangleCount+=a.lineIndexCount/3}}}}export{c as WGLBrushVTLLine};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e,isNone as t}from\"../../../../../core/maybe.js\";import{c as i,f as a}from\"../../../../../chunks/vec2f32.js\";import{FADE_DURATION as r}from\"../../vectorTiles/decluttering/config.js\";import{RotationAlignment as n,SymbolPlacement as s,TranslateAnchor as o}from\"../../vectorTiles/style/StyleDefinition.js\";import{VTL_TEXTURE_BINDING_UNIT_SPRITES as l,VTL_TEXTURE_BINDING_UNIT_GLYPHS as f}from\"../definitions.js\";import{WGLDrawPhase as c}from\"../enums.js\";import{degToByte as u}from\"../GeometryUtils.js\";import{u32to4Xu8 as m}from\"../number.js\";import p from\"./WGLBrush.js\";import{TextureSamplingMode as d,CompareFunction as y,PrimitiveType as g,DataType as _}from\"../../../../webgl/enums.js\";const h=1/65536;class M extends p{constructor(){super(...arguments),this._iconProgramOptions={id:!1,sdf:!1},this._sdfProgramOptions={id:!1},this._spritesTextureSize=i()}dispose(){}drawMany(e,t){const{drawPhase:i,styleLayerUID:a}=e,r=e.styleLayer;let n;i===c.HITTEST&&(n=m(a+1)),this._drawIcons(e,r,t,n),this._drawText(e,r,t,n)}_drawIcons(i,a,f,m){const{context:p,displayLevel:d,drawPhase:y,painter:g,spriteMosaic:_,state:h,styleLayerUID:M,requestRender:P,allowDelayedRender:T}=i,U=a.iconMaterial,E=g.vectorTilesMaterialManager;let x,v=!1;for(const e of f)if(e.layerData.has(M)&&(x=e.layerData.get(M),x.iconPerPageElementsMap.size>0)){v=!0;break}if(!v)return;const D=a.getPaintValue(\"icon-translate\",d),I=a.getPaintValue(\"icon-translate-anchor\",d);let R=a.getLayoutValue(\"icon-rotation-alignment\",d);R===n.AUTO&&(R=a.getLayoutValue(\"symbol-placement\",d)===s.POINT?n.VIEWPORT:n.MAP);const S=R===n.MAP,V=a.getLayoutValue(\"icon-keep-upright\",d)&&S,w=x.isIconSDF,A=y===c.HITTEST,L=this._iconProgramOptions;L.id=A,L.sdf=w;const O=E.getMaterialProgram(p,U,L);if(T&&e(P)&&!O.compiled)return void P();p.useProgram(O),O.setUniformMatrix3fv(\"u_displayViewMat3\",R===n.MAP?h.displayViewMat3:h.displayMat3),O.setUniformMatrix3fv(\"u_displayMat3\",I===o.VIEWPORT?h.displayMat3:h.displayViewMat3),O.setUniform2fv(\"u_iconTranslation\",D),O.setUniform1f(\"u_depth\",a.z),O.setUniform1f(\"u_mapRotation\",u(h.rotation)),O.setUniform1f(\"u_keepUpright\",V?1:0),O.setUniform1f(\"u_level\",10*d),O.setUniform1i(\"u_texture\",l),O.setUniform1f(\"u_fadeDuration\",r/1e3),A&&O.setUniform4fv(\"u_id\",m);let N=-1;for(const e of f){if(!e.layerData.has(M))continue;if(e.key.level!==N&&(N=e.key.level,U.setDataUniforms(O,d,a,N,_)),x=e.layerData.get(M),0===x.iconPerPageElementsMap.size)continue;x.prepareForRendering(p),x.updateOpacityInfo();const r=x.iconVertexArrayObject;if(!t(r)){p.bindVAO(r),O.setUniformMatrix3fv(\"u_dvsMat3\",e.transforms.dvs),O.setUniform1f(\"u_time\",(performance.now()-x.lastOpacityUpdate)/1e3);for(const[t,a]of x.iconPerPageElementsMap)this._renderIconRange(i,O,a,t,e)}}}_renderIconRange(e,t,i,a,r){const{context:n,spriteMosaic:s}=e;this._spritesTextureSize[0]=s.getWidth(a)/4,this._spritesTextureSize[1]=s.getHeight(a)/4,t.setUniform2fv(\"u_mosaicSize\",this._spritesTextureSize),s.bind(n,d.LINEAR,a,l),n.setStencilTestEnabled(!0),n.setStencilFunction(y.GREATER,255,255),n.setStencilWriteMask(0),n.drawElements(g.TRIANGLES,i[1],_.UNSIGNED_INT,Uint32Array.BYTES_PER_ELEMENT*i[0]),r.triangleCount+=i[1]/3}_drawText(i,l,m,p){const{context:d,displayLevel:g,drawPhase:_,glyphMosaic:M,painter:P,pixelRatio:T,spriteMosaic:U,state:E,styleLayerUID:x,requestRender:v,allowDelayedRender:D}=i,I=l.textMaterial,R=P.vectorTilesMaterialManager;let S,V=!1;for(const e of m)if(e.layerData.has(x)&&(S=e.layerData.get(x),S.glyphPerPageElementsMap.size>0)){V=!0;break}if(!V)return;const w=l.getPaintProperty(\"text-opacity\");if(w&&!w.isDataDriven&&0===w.getValue(g))return;const A=l.getPaintProperty(\"text-color\"),L=!A||A.isDataDriven||A.getValue(g)[3]>0,O=l.getPaintProperty(\"text-halo-width\"),N=l.getPaintProperty(\"text-halo-color\"),b=(!O||O.isDataDriven||O.getValue(g)>0)&&(!N||N.isDataDriven||N.getValue(g)[3]>0);if(!L&&!b)return;const z=24/8;let k=l.getLayoutValue(\"text-rotation-alignment\",g);k===n.AUTO&&(k=l.getLayoutValue(\"symbol-placement\",g)===s.POINT?n.VIEWPORT:n.MAP);const j=k===n.MAP,G=l.getLayoutValue(\"text-keep-upright\",g)&&j,W=_===c.HITTEST,F=.8*z/T;this._glyphTextureSize||(this._glyphTextureSize=a(M.width/4,M.height/4));const B=l.getPaintValue(\"text-translate\",g),H=l.getPaintValue(\"text-translate-anchor\",g),C=this._sdfProgramOptions;C.id=W;const Y=R.getMaterialProgram(d,I,C);if(D&&e(v)&&!Y.compiled)return void v();d.useProgram(Y),Y.setUniformMatrix3fv(\"u_displayViewMat3\",k===n.MAP?E.displayViewMat3:E.displayMat3),Y.setUniformMatrix3fv(\"u_displayMat3\",H===o.VIEWPORT?E.displayMat3:E.displayViewMat3),Y.setUniform2fv(\"u_textTranslation\",B),Y.setUniform1f(\"u_depth\",l.z+h),Y.setUniform2fv(\"u_mosaicSize\",this._glyphTextureSize),Y.setUniform1f(\"u_mapRotation\",u(E.rotation)),Y.setUniform1f(\"u_keepUpright\",G?1:0),Y.setUniform1f(\"u_level\",10*g),Y.setUniform1i(\"u_texture\",f),Y.setUniform1f(\"u_antialiasingWidth\",F),Y.setUniform1f(\"u_fadeDuration\",r/1e3),W&&Y.setUniform4fv(\"u_id\",p);let q=-1;for(const e of m){if(!e.layerData.has(x))continue;if(e.key.level!==q&&(q=e.key.level,I.setDataUniforms(Y,g,l,q,U)),S=e.layerData.get(x),0===S.glyphPerPageElementsMap.size)continue;S.prepareForRendering(d),S.updateOpacityInfo();const i=S.textVertexArrayObject;if(t(i))continue;d.bindVAO(i),Y.setUniformMatrix3fv(\"u_dvsMat3\",e.transforms.dvs),d.setStencilTestEnabled(!0),d.setStencilFunction(y.GREATER,255,255),d.setStencilWriteMask(0);const a=(performance.now()-S.lastOpacityUpdate)/1e3;Y.setUniform1f(\"u_time\",a),S.glyphPerPageElementsMap.forEach(((t,i)=>{this._renderGlyphRange(d,t,i,M,Y,b,L,e)}))}}_renderGlyphRange(e,t,i,a,r,n,s,o){a.bind(e,d.LINEAR,i,f),n&&(r.setUniform1f(\"u_halo\",1),e.drawElements(g.TRIANGLES,t[1],_.UNSIGNED_INT,Uint32Array.BYTES_PER_ELEMENT*t[0]),o.triangleCount+=t[1]/3),s&&(r.setUniform1f(\"u_halo\",0),e.drawElements(g.TRIANGLES,t[1],_.UNSIGNED_INT,Uint32Array.BYTES_PER_ELEMENT*t[0]),o.triangleCount+=t[1]/3)}}export{M as WGLBrushVTLSymbol};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../../core/maybe.js\";import{WGLGeometryType as t}from\"../enums.js\";import{createProgramDescriptor as o}from\"../Utils.js\";import a from\"./WGLGeometryBrush.js\";import{LabelMaterialKey as n}from\"../materialKey/MaterialKey.js\";import{CompareFunction as r,PrimitiveType as s,DataType as i}from\"../../../../webgl/enums.js\";const m=e=>o(e.data,{geometry:[{location:0,name:\"a_pos\",count:2,type:i.SHORT},{location:1,name:\"a_id\",count:4,type:i.UNSIGNED_BYTE},{location:2,name:\"a_color\",count:4,type:i.UNSIGNED_BYTE,normalized:!0},{location:3,name:\"a_haloColor\",count:4,type:i.UNSIGNED_BYTE,normalized:!0},{location:4,name:\"a_texAndSize\",count:4,type:i.UNSIGNED_BYTE},{location:5,name:\"a_refSymbolAndPlacementOffset\",count:4,type:i.UNSIGNED_BYTE},{location:6,name:\"a_glyphData\",count:4,type:i.UNSIGNED_BYTE},{location:7,name:\"a_vertexOffset\",count:2,type:i.SHORT},{location:8,name:\"a_texCoords\",count:2,type:i.UNSIGNED_SHORT}]});class l extends a{dispose(){}getGeometryType(){return t.LABEL}supportsSymbology(e){return!0}drawGeometry(t,o,a,l){const{context:u,painter:f,state:d,rendererInfo:c,requestRender:_,allowDelayedRender:p}=t,y=n.load(a.materialKey),E=y.mapAligned?1:0;if(!E&&Math.abs(o.key.level-Math.round(100*t.displayLevel)/100)>=1)return;const{bufferLayouts:N,attributes:U}=m(y),S=f.materialManager.getMaterialProgram(t,y,\"materials/label\",U,l);if(p&&e(_)&&!S.compiled)return void _();t.context.setStencilFunction(r.EQUAL,0,255),u.useProgram(S),this._setSharedUniforms(S,t,o),f.textureManager.bindTextures(u,S,y);const T=1===E?d.displayViewMat3:d.displayMat3;this._setSizeVVUniforms(y,S,c,o),S.setUniform1f(\"u_mapRotation\",Math.floor(d.rotation/360*254)),S.setUniform1f(\"u_mapAligned\",E),S.setUniformMatrix3fv(\"u_displayMat3\",T),S.setUniform1f(\"u_opacity\",1),S.setUniform2fv(\"u_screenSize\",t.state.size);const g=a.target.getVAO(u,N,U),G=a.indexFrom*Uint32Array.BYTES_PER_ELEMENT;u.bindVAO(g),S.setUniform1f(\"u_isHaloPass\",0),S.setUniform1f(\"u_isBackgroundPass\",1),u.drawElements(s.TRIANGLES,a.indexCount,i.UNSIGNED_INT,G),S.setUniform1f(\"u_isHaloPass\",1),S.setUniform1f(\"u_isBackgroundPass\",0),u.drawElements(s.TRIANGLES,a.indexCount,i.UNSIGNED_INT,G),S.setUniform1f(\"u_isHaloPass\",0),S.setUniform1f(\"u_isBackgroundPass\",0),u.drawElements(s.TRIANGLES,a.indexCount,i.UNSIGNED_INT,G),u.setStencilTestEnabled(!0),u.setBlendingEnabled(!0)}}export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../../core/maybe.js\";import{WGLGeometryType as t}from\"../enums.js\";import{createProgramDescriptor as o}from\"../Utils.js\";import a from\"./WGLGeometryBrush.js\";import{LineMaterialKey as n}from\"../materialKey/MaterialKey.js\";import{PrimitiveType as r,DataType as i}from\"../../../../webgl/enums.js\";const s=e=>o(e.data,{geometry:[{location:0,name:\"a_pos\",count:2,type:i.SHORT},{location:1,name:\"a_id\",count:4,type:i.UNSIGNED_BYTE},{location:2,name:\"a_color\",count:4,type:i.UNSIGNED_BYTE,normalized:!0},{location:3,name:\"a_offsetAndNormal\",count:4,type:i.BYTE},{location:4,name:\"a_accumulatedDistanceAndHalfWidth\",count:2,type:i.UNSIGNED_SHORT},{location:5,name:\"a_tlbr\",count:4,type:i.UNSIGNED_SHORT},{location:6,name:\"a_segmentDirection\",count:4,type:i.BYTE},{location:7,name:\"a_aux\",count:2,type:i.UNSIGNED_SHORT},{location:8,name:\"a_zoomRange\",count:2,type:i.UNSIGNED_SHORT}]});class m extends a{dispose(){}getGeometryType(){return t.LINE}supportsSymbology(e){return!0}drawGeometry(t,o,a,m){const{context:l,painter:c,rendererInfo:u,displayLevel:p,passOptions:d,requestRender:y,allowDelayedRender:_}=t,f=n.load(a.materialKey),E=e(d)&&\"hittest\"===d.type;let N=s(f),g=r.TRIANGLES;E&&(N=this._getTriangleDesc(a.materialKey,N),g=r.POINTS);const{attributes:S,bufferLayouts:T}=N,U=c.materialManager.getMaterialProgram(t,f,\"materials/line\",S,m);if(_&&e(y)&&!U.compiled)return void y();const G=1/t.pixelRatio,I=0;l.useProgram(U),this._setSharedUniforms(U,t,o),f.textureBinding&&c.textureManager.bindTextures(l,U,f);const D=2**(p-o.key.level);U.setUniform1f(\"u_zoomFactor\",D),U.setUniform1f(\"u_blur\",I+G),U.setUniform1f(\"u_antialiasing\",G),this._setSizeVVUniforms(f,U,u,o),this._setColorAndOpacityVVUniforms(f,U,u),l.setFaceCullingEnabled(!1);const R=a.target.getVAO(l,T,S,E);let b=a.indexCount,x=a.indexFrom*Uint32Array.BYTES_PER_ELEMENT;E&&(b/=3,x/=3),l.bindVAO(R),l.drawElements(g,b,i.UNSIGNED_INT,x)}}export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../../../core/maybe.js\";import{WGLGeometryType as e}from\"../enums.js\";import{createProgramDescriptor as o}from\"../Utils.js\";import a from\"./WGLGeometryBrush.js\";import{TextMaterialKey as r}from\"../materialKey/MaterialKey.js\";import{PrimitiveType as s,DataType as n}from\"../../../../webgl/enums.js\";const i=t=>o(t.data,{geometry:[{location:0,name:\"a_pos\",count:2,type:n.SHORT},{location:1,name:\"a_id\",count:4,type:n.UNSIGNED_BYTE},{location:2,name:\"a_color\",count:4,type:n.UNSIGNED_BYTE,normalized:!0},{location:3,name:\"a_haloColor\",count:4,type:n.UNSIGNED_BYTE,normalized:!0},{location:4,name:\"a_texFontSize\",count:4,type:n.UNSIGNED_BYTE},{location:5,name:\"a_aux\",count:4,type:n.BYTE},{location:6,name:\"a_zoomRange\",count:2,type:n.UNSIGNED_SHORT},{location:7,name:\"a_vertexOffset\",count:2,type:n.SHORT},{location:8,name:\"a_texCoords\",count:2,type:n.UNSIGNED_SHORT}]});class m extends a{dispose(){}getGeometryType(){return e.TEXT}supportsSymbology(t){return!0}drawGeometry(e,o,a,m){const{context:l,painter:u,rendererInfo:d,state:f,passOptions:_,requestRender:p,allowDelayedRender:c}=e,y=r.load(a.materialKey),E=t(_)&&\"hittest\"===_.type,{bufferLayouts:N,attributes:U}=i(y),S=u.materialManager.getMaterialProgram(e,y,\"materials/text\",U,m);if(c&&t(p)&&!S.compiled)return void p();l.useProgram(S);let T=s.TRIANGLES;E&&(T=s.POINTS),this._setSharedUniforms(S,e,o),u.textureManager.bindTextures(l,S,y),S.setUniformMatrix3fv(\"u_displayMat3\",f.displayMat3),S.setUniformMatrix3fv(\"u_displayViewMat3\",f.displayViewMat3),this._setSizeVVUniforms(y,S,d,o),this._setColorAndOpacityVVUniforms(y,S,d),this._setRotationVVUniforms(y,S,d);const x=a.target.getVAO(l,N,U),I=a.indexFrom*Uint32Array.BYTES_PER_ELEMENT;S.setUniform1f(\"u_isHaloPass\",0),S.setUniform1f(\"u_isBackgroundPass\",1),l.bindVAO(x),l.drawElements(T,a.indexCount,n.UNSIGNED_INT,I),S.setUniform1f(\"u_isHaloPass\",1),S.setUniform1f(\"u_isBackgroundPass\",0),l.drawElements(s.TRIANGLES,a.indexCount,n.UNSIGNED_INT,I),S.setUniform1f(\"u_isHaloPass\",0),S.setUniform1f(\"u_isBackgroundPass\",0),l.drawElements(T,a.indexCount,n.UNSIGNED_INT,I)}}export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"./webgl/brushes/BrushBitmap.js\";import s from\"./webgl/brushes/BrushClip.js\";import e from\"./webgl/brushes/BrushOverlay.js\";import o from\"./webgl/brushes/WGLBrushDotDensity.js\";import m from\"./webgl/brushes/WGLBrushHeatmap.js\";import l from\"./webgl/brushes/WGLBrushInfo.js\";import t from\"./webgl/brushes/WGLBrushPieChart.js\";import b from\"./webgl/brushes/WGLBrushStencil.js\";import{WGLBrushVTLBackground as h}from\"./webgl/brushes/WGLBrushVTLBackground.js\";import{WGLBrushVTLCircle as i}from\"./webgl/brushes/WGLBrushVTLCircle.js\";import{WGLBrushVTLFill as u}from\"./webgl/brushes/WGLBrushVTLFill.js\";import{WGLBrushVTLLine as p}from\"./webgl/brushes/WGLBrushVTLLine.js\";import{WGLBrushVTLSymbol as L}from\"./webgl/brushes/WGLBrushVTLSymbol.js\";import f from\"./webgl/brushes/WGLGeometryBrushFill.js\";import B from\"./webgl/brushes/WGLGeometryBrushLabel.js\";import g from\"./webgl/brushes/WGLGeometryBrushLine.js\";import G from\"./webgl/brushes/WGLGeometryBrushMarker.js\";import j from\"./webgl/brushes/WGLGeometryBrushText.js\";const w={marker:G,line:g,fill:f,text:j,label:B,clip:s,stencil:b,bitmap:r,overlay:e,tileInfo:l,vtlBackground:h,vtlFill:u,vtlLine:p,vtlCircle:i,vtlSymbol:L,dotDensity:o,heatmap:m,pieChart:t};export{w as brushes};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/Error.js\";import t from\"../../../../core/Logger.js\";import{isSome as r}from\"../../../../core/maybe.js\";import{e as s}from\"../../../../chunks/earcut.js\";import{s as o}from\"../../../../chunks/vec2.js\";import{a as n}from\"../../../../chunks/vec2f64.js\";import{convertFromNestedArray as i,convertFromPolygon as c}from\"../../../../layers/graphics/featureConversionUtils.js\";import a from\"../../../../layers/graphics/OptimizedGeometry.js\";import{i1616to32 as f}from\"./number.js\";import{BufferObject as h}from\"../../../webgl/BufferObject.js\";import{PrimitiveType as m,Usage as u,DataType as y}from\"../../../webgl/enums.js\";const x=t=>{switch(t.BYTES_PER_ELEMENT){case 1:return y.UNSIGNED_BYTE;case 2:return y.UNSIGNED_SHORT;case 4:return y.UNSIGNED_INT;default:throw new e(\"Cannot get DataType of array\")}},g=(e,t,r,s)=>{let o=0;for(let n=1;n<r;n++){const r=e[2*(t+n-1)],s=e[2*(t+n-1)+1];o+=(e[2*(t+n)]-r)*(e[2*(t+n)+1]+s)}return s?o>0:o<0},p=({coords:e,lengths:t},r)=>{const o=[];for(let n=0,i=0;n<t.length;i+=t[n],n+=1){const c=i,a=[];for(;n<t.length-1&&g(e,i+t[n],t[n+1],r);n+=1,i+=t[n])a.push(i+t[n]-c);const f=e.slice(2*c,2*(i+t[n])),h=s(f,a,2);for(const e of h)o.push(e+c)}return o};class l{constructor(e,t,r,s=!1){this._cache={},this.vertices=e,this.indices=t,this.primitiveType=r,this.isMapSpace=s}static fromRect({x:e,y:t,width:r,height:s}){const o=e,n=t,i=o+r,c=n+s;return l.fromScreenExtent({xmin:o,ymin:n,xmax:i,ymax:c})}static fromPath(e){const t=i(new a,e.path,!1,!1),r=t.coords,s=new Uint32Array(p(t,!0)),o=new Uint32Array(r.length/2);for(let n=0;n<o.length;n++)o[n]=f(Math.floor(r[2*n]),Math.floor(r[2*n+1]));return new l({geometry:o},s,m.TRIANGLES)}static fromGeometry(r,s){const o=s.geometry?.type;switch(o){case\"polygon\":return l.fromPolygon(r,s.geometry);case\"extent\":return l.fromMapExtent(r,s.geometry);default:return t.getLogger(\"esri.views.2d.engine.webgl.Mesh2D\").error(new e(\"mapview-bad-type\",`Unable to create a mesh from type ${o}`,s)),l.fromRect({x:0,y:0,width:1,height:1})}}static fromPolygon(e,t){const r=c(new a,t,!1,!1),s=r.coords,i=new Uint32Array(p(r,!1)),h=new Uint32Array(s.length/2),u=n(),y=n();for(let n=0;n<h.length;n++)o(u,s[2*n],s[2*n+1]),e.toScreen(y,u),h[n]=f(Math.floor(y[0]),Math.floor(y[1]));return new l({geometry:h},i,m.TRIANGLES,!0)}static fromScreenExtent({xmin:e,xmax:t,ymin:r,ymax:s}){const o={geometry:new Uint32Array([f(e,r),f(t,r),f(e,s),f(e,s),f(t,r),f(t,s)])},n=new Uint32Array([0,1,2,3,4,5]);return new l(o,n,m.TRIANGLES)}static fromMapExtent(e,t){const[r,s]=e.toScreen([0,0],[t.xmin,t.ymin]),[o,n]=e.toScreen([0,0],[t.xmax,t.ymax]),i={geometry:new Uint32Array([f(r,s),f(o,s),f(r,n),f(r,n),f(o,s),f(o,n)])},c=new Uint32Array([0,1,2,3,4,5]);return new l(i,c,m.TRIANGLES)}destroy(){r(this._cache.indexBuffer)&&this._cache.indexBuffer.dispose();for(const e in this._cache.vertexBuffers)r(this._cache.vertexBuffers[e])&&this._cache.vertexBuffers[e].dispose()}get elementType(){return x(this.indices)}getIndexBuffer(e,t=u.STATIC_DRAW){return this._cache.indexBuffer||(this._cache.indexBuffer=h.createIndex(e,t,this.indices)),this._cache.indexBuffer}getVertexBuffers(e,t=u.STATIC_DRAW){return this._cache.vertexBuffers||(this._cache.vertexBuffers=Object.keys(this.vertices).reduce(((r,s)=>({...r,[s]:h.createVertex(e,t,this.vertices[s])})),{})),this._cache.vertexBuffers}}export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../core/Error.js\";import e from\"../../../../core/Logger.js\";import{isSome as r,isNone as s}from\"../../../../core/maybe.js\";import{watch as i}from\"../../../../core/reactiveUtils.js\";import{c as o}from\"../../../../chunks/mat3f32.js\";import{DisplayObject as h}from\"../DisplayObject.js\";import a from\"./Mesh2D.js\";import{VertexArrayObject as c}from\"../../../webgl/VertexArrayObject.js\";const n=t=>parseFloat(t)/100;class m extends h{constructor(t,e){super(),this._clip=e,this._cache={},this.stage=t,this._handle=i((()=>e.version),(()=>this._invalidate())),this.ready()}static fromClipArea(t,e){return new m(t,e)}_destroyGL(){r(this._cache.mesh)&&(this._cache.mesh.destroy(),this._cache.mesh=null),r(this._cache.vao)&&(this._cache.vao.dispose(),this._cache.vao=null)}destroy(){this._destroyGL(),this._handle.remove()}getVAO(t,e,r,i){const[o,h]=e.size;if(\"geometry\"!==this._clip.type&&this._lastWidth===o&&this._lastHeight===h||(this._lastWidth=o,this._lastHeight=h,this._destroyGL()),s(this._cache.vao)){const s=this._createMesh(e,this._clip),o=s.getIndexBuffer(t),h=s.getVertexBuffers(t);this._cache.mesh=s,this._cache.vao=new c(t,r,i,h,o)}return this._cache.vao}_createTransforms(){return{dvs:o()}}_invalidate(){this._destroyGL(),this.requestRender()}_createScreenRect(t,e){const[r,s]=t.size,i=\"string\"==typeof e.left?n(e.left)*r:e.left,o=\"string\"==typeof e.right?n(e.right)*r:e.right,h=\"string\"==typeof e.top?n(e.top)*s:e.top,a=\"string\"==typeof e.bottom?n(e.bottom)*s:e.bottom,c=i,m=h;return{x:c,y:m,width:Math.max(r-o-c,0),height:Math.max(s-a-m,0)}}_createMesh(r,s){switch(s.type){case\"rect\":return a.fromRect(this._createScreenRect(r,s));case\"path\":return a.fromPath(s);case\"geometry\":return a.fromGeometry(r,s);default:return e.getLogger(\"esri.views.2d.engine.webgl.ClippingInfo\").error(new t(\"mapview-bad-type\",\"Unable to create ClippingInfo mesh from clip of type: ${clip.type}\")),a.fromRect({x:0,y:0,width:1,height:1})}}}export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../core/has.js\";import{isNone as e,isSome as r}from\"../../../../core/maybe.js\";import{c as s}from\"../../../../chunks/mat3f32.js\";import{brushes as t}from\"../brushes.js\";import{Container as i}from\"../Container.js\";import n from\"./ClippingInfo.js\";import{WGLDrawPhase as o}from\"./enums.js\";class a extends i{constructor(){super(...arguments),this.name=this.constructor.name}set clips(e){this._clips=e,this.children.forEach((r=>r.clips=e)),this._updateClippingInfo()}beforeRender(e){super.beforeRender(e),this.updateTransforms(e.state)}_createTransforms(){return{dvs:s()}}doRender(e){const r=this.createRenderParams(e),{painter:s,globalOpacity:t,profiler:i,drawPhase:n}=r,a=n===o.LABEL||n===o.HIGHLIGHT?1:t*this.computedOpacity;i.recordContainerStart(this.name),s.beforeRenderLayer(r,this._clippingInfos?255:0,a),this.renderChildren(r),s.compositeLayer(r,a),i.recordContainerEnd()}renderChildren(r){e(this._renderPasses)&&(this._renderPasses=this.prepareRenderPasses(r.painter));for(const e of this._renderPasses)try{e.render(r)}catch(s){}}createRenderParams(e){return e.requireFBO=this.requiresDedicatedFBO,e}prepareRenderPasses(e){return[e.registerRenderPass({name:\"clip\",brushes:[t.clip],target:()=>this._clippingInfos,drawPhase:o.MAP|o.LABEL|o.LABEL_ALPHA|o.DEBUG|o.HIGHLIGHT})]}updateTransforms(e){for(const r of this.children)r.setTransform(e)}onAttach(){super.onAttach(),this._updateClippingInfo()}onDetach(){super.onDetach(),this._updateClippingInfo()}_updateClippingInfo(){r(this._clippingInfos)&&(this._clippingInfos.forEach((e=>e.destroy())),this._clippingInfos=null);const e=this.stage;if(!e)return;const s=this._clips;r(s)&&s.length&&(this._clippingInfos=s.items.map((r=>n.fromClipArea(e,r)))),this.requestRender()}}export{a as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2S,IAAMA,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAED,IAAE;AAAC,SAAK,QAAMC,IAAE,KAAK,gBAAcC,GAAE,aAAaD,IAAE,EAAE,aAAY,IAAI,YAAYD,EAAC,CAAC,GAAE,KAAK,OAAK,IAAIG,GAAEF,IAAE,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,GAAE,EAAC,UAAS,CAAC,IAAIG,GAAE,cAAa,GAAE,EAAE,OAAM,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,UAAS,KAAK,cAAa,CAAC,GAAE,KAAK,SAAOJ,GAAE,SAAO;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,MAAM,QAAQ,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,MAAM,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,KAAK,QAAQ,KAAE,GAAE,KAAK,cAAc,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,MAAM,QAAQ,KAAK,IAAI,GAAE,KAAK,MAAM,WAAW,EAAE,gBAAe,GAAE,KAAK,MAAM;AAAA,EAAC;AAAC;;;ACAzwB,IAAMK,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,OAAK,KAAK,YAAY,QAAM,gBAAe,KAAK,cAAY;AAAA,EAAI;AAAA,EAAC,aAAaA,KAAEC,KAAE;AAAA,EAAC;AAAA,EAAC,KAAKD,KAAEC,KAAEC,IAAE;AAAA,EAAC;AAAA,EAAC,SAASF,KAAEC,KAAEC,IAAE;AAAC,eAAUC,MAAKF,IAAE,CAAAE,GAAE,WAAS,KAAK,KAAKH,KAAEG,IAAED,EAAC;AAAA,EAAC;AAAC;;;ACA8F,IAAME,KAAE,EAAC,SAAQ,EAAC,SAAQ,CAAC,GAAE,cAAa,EAAE,SAAQ,MAAK,MAAE,GAAE,UAAS,EAAC,SAAQ,CAAC,GAAE,cAAa,EAAE,QAAO,MAAK,MAAE,GAAE,SAAQ,EAAC,SAAQ,CAAC,SAAS,GAAE,cAAa,EAAE,QAAO,MAAK,MAAE,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,cAAa,EAAE,sBAAqB,MAAK,KAAE,EAAC;AAA/O,IAAiP,IAAE,CAACC,IAAEC,KAAEC,OAAI;AAAC,MAAG,cAAYA,GAAE,cAAa;AAAC,UAAK,EAAC,OAAMA,GAAC,IAAEF,IAAEG,KAAEF,IAAE,aAAWA,IAAE,aAAWC,GAAE,YAAWE,KAAE,KAAK,MAAMJ,GAAE,UAAU,MAAIA,GAAE,YAAWK,MAAEF,KAAE,QAAMA,KAAE;AAAI,WAAOD,GAAE,YAAUG,OAAGD,MAAGH,IAAE,kBAAgBA,IAAE,WAASF,GAAE,WAASA,GAAE;AAAA,EAAO;AAAC,SAAOA,GAAEG,GAAE,YAAY;AAAC;AAAE,IAAM,IAAN,cAAgBD,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAM,EAAC,QAAO,iBAAgB,QAAO,iBAAgB,YAAW,oBAAI,IAAI,CAAC,CAAC,SAAQ,CAAC,CAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAO,KAAK,MAAM,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAa,EAAC,SAAQD,GAAC,GAAE;AAAC,IAAAA,GAAE,mBAAmB,IAAE,GAAEA,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,GAAE,oBAAoB,CAAC,GAAEA,GAAE,sBAAsB,IAAE;AAAA,EAAC;AAAA,EAAC,KAAKG,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQL,IAAE,kBAAiBO,IAAE,SAAQC,IAAE,eAAcC,IAAE,oBAAmBC,GAAC,IAAEN;AAAE,QAAG,CAACC,GAAE,UAAQ,CAACA,GAAE,QAAQ;AAAO,UAAMM,KAAE,EAAEP,IAAEC,IAAEE,EAAC,GAAEK,KAAEJ,GAAE,gBAAgB,WAAW,KAAK,OAAMG,GAAE,OAAO;AAAE,QAAGD,MAAG,EAAED,EAAC,KAAG,CAACG,GAAE,SAAS,QAAO,KAAKH,GAAE;AAAE,IAAAL,GAAE,SAAS,MAAM,KAAK,IAAI,GAAE,eAAaC,GAAE,gBAAcL,GAAE,yBAAyB,EAAE,KAAI,EAAE,KAAI,EAAE,KAAI,EAAE,GAAG,IAAEA,GAAE,yBAAyB,EAAE,KAAI,EAAE,qBAAoB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,mBAAmB,EAAE,OAAMK,GAAE,YAAW,GAAG,GAAE,KAAK,UAAQ,KAAK,QAAM,IAAIA,GAAEL,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAG,UAAK,EAAC,YAAWa,IAAE,iBAAgBC,IAAE,YAAWC,GAAC,IAAEV;AAAE,IAAAA,GAAE,mBAAmBM,EAAC,GAAEN,GAAE,KAAKD,GAAE,SAAQ,CAAC,GAAEJ,GAAE,WAAWY,EAAC,GAAEA,GAAE,oBAAoB,aAAYG,GAAE,GAAG,GAAEH,GAAE,aAAa,aAAY,CAAC,GAAEA,GAAE,cAAc,gBAAeC,EAAC,GAAED,GAAE,aAAa,aAAYE,EAAC,GAAE,KAAK,MAAM,KAAK,GAAEV,GAAE,SAAS,IAAI,KAAK,IAAI;AAAA,EAAC;AAAC;;;ACAj4D,IAAMY,KAAE,EAAC,YAAW,EAAC,mBAAkB,yEAAwE,mBAAkB,mPAAkP,GAAE,SAAQ,EAAC,gBAAe,6LAA4L,gBAAe,sJAAqJ,GAAE,OAAM,EAAC,cAAa,22PAA02P,cAAa,6JAA4J,GAAE,OAAM,EAAC,SAAQ,EAAC,gBAAe,kGAAiG,gBAAe,4oBAA2oB,EAAC,GAAE,KAAI,EAAC,KAAI,EAAC,YAAW,+UAA8U,YAAW,+5BAA85B,EAAC,GAAE,WAAU,EAAC,gBAAe,k2CAAi2C,iBAAgB,q1BAAo1B,YAAW,upCAAspC,GAAE,IAAG,EAAC,WAAU,EAAC,kBAAiB,+3BAA83B,kBAAiB,4KAA2K,EAAC,GAAE,SAAQ,EAAC,gBAAe,EAAC,uBAAsB,qnBAAonB,uBAAsB,qJAAoJ,EAAC,GAAE,WAAU,EAAC,aAAY,kkCAAikC,kBAAiB,4tBAA2tB,iBAAgB,sMAAqM,GAAE,WAAU,EAAC,kBAAiB,g9BAA+8B,kBAAiB,yPAAwP,GAAE,WAAU,EAAC,sBAAqB,62DAA42D,oBAAmB,qhBAAohB,kBAAiB,wzCAAuzC,MAAK,EAAC,eAAc,wyBAAuyB,aAAY,uzFAAszF,aAAY,otJAAmtJ,gBAAe,qvBAAovB,GAAE,SAAQ,EAAC,eAAc,k1CAAi1C,GAAE,MAAK,EAAC,eAAc,s5CAAq5C,mBAAkB,EAAC,eAAc,mHAAkH,0BAAyB,omBAAmmB,0BAAyB,2lCAA0lC,GAAE,gBAAe,qwFAAowF,aAAY,8VAA6V,aAAY,u6EAAs6E,GAAE,OAAM,EAAC,eAAc,0UAAyU,cAAa,uCAAsC,cAAa,2tFAA0tF,GAAE,MAAK,EAAC,eAAc,iZAAgZ,gBAAe,s0BAAq0B,aAAY,wrBAAurB,aAAY,28DAA08D,GAAE,KAAI,EAAC,YAAW,uzIAAszI,YAAW,82JAA62J,GAAE,QAAO,EAAC,MAAK,EAAC,eAAc,giBAA+hB,aAAY,+rEAA8rE,aAAY,g4EAA+3E,EAAC,GAAE,2BAA0B,sTAAqT,MAAK,EAAC,eAAc,8SAA6S,gBAAe,4CAA2C,aAAY,mmBAAkmB,aAAY,i0EAAg0E,GAAE,cAAa,gxCAA+wC,gBAAe,8uFAA6uF,WAAU,w3IAAu3I,GAAE,SAAQ,EAAC,SAAQ,EAAC,gBAAe,uNAAsN,gBAAe,0UAAyU,EAAC,GAAE,mBAAkB,EAAC,MAAK,EAAC,aAAY,2IAA0I,GAAE,OAAM,EAAC,WAAU,EAAC,kBAAiB,umCAAsmC,GAAE,cAAa,EAAC,qBAAoB,s2BAAq2B,GAAE,oBAAmB,EAAC,2BAA0B,iiBAAgiB,EAAC,GAAE,MAAK,EAAC,cAAa,EAAC,qBAAoB,m2BAAk2B,GAAE,eAAc,EAAC,oBAAmB,8wBAA6wB,EAAC,GAAE,KAAI,EAAC,YAAW,4mBAA2mB,WAAU,EAAC,gBAAe,gwBAA+vB,EAAC,GAAE,eAAc,EAAC,WAAU,EAAC,kBAAiB,ufAAsf,EAAC,GAAE,eAAc,EAAC,aAAY,EAAC,kBAAiB,ikEAAgkE,GAAE,OAAM,EAAC,cAAa,q0BAAo0B,EAAC,GAAE,gBAAe,EAAC,qBAAoB,m1BAAk1B,GAAE,cAAa,EAAC,qBAAoB,sUAAqU,GAAE,IAAG,EAAC,WAAU,0KAAyK,EAAC,GAAE,QAAO,EAAC,QAAO,EAAC,eAAc,0aAAya,eAAc,kQAAiQ,GAAE,QAAO,EAAC,eAAc,2hCAA0hC,eAAc,oVAAmV,2BAA0B,y0BAAw0B,gBAAe,yGAAwG,eAAc,iIAAgI,mBAAkB,g3CAA+2C,GAAE,MAAK,EAAC,uBAAsB,qNAAoN,yBAAwB,oRAAmR,SAAQ,EAAC,gBAAe,4gBAA2gB,gBAAe,kPAAiP,GAAE,WAAU,EAAC,kBAAiB,sSAAqS,kBAAiB,qgHAAogH,GAAE,aAAY,EAAC,oBAAmB,0wBAAywB,oBAAmB,64BAA44B,GAAE,WAAU,89DAA69D,GAAE,WAAU,EAAC,kBAAiB,iuHAAguH,GAAE,KAAI,EAAC,iBAAgB,4cAA2c,YAAW,2bAA0b,GAAE,QAAO,EAAC,eAAc,yIAAwI,eAAc,ymCAAwmC,GAAE,WAAU,EAAC,kBAAiB,iXAAgX,kBAAiB,6LAA4L,GAAE,KAAI,EAAC,QAAO,EAAC,eAAc,slDAAqlD,GAAE,gBAAe,EAAC,uBAAsB,yqEAAwqE,GAAE,eAAc,EAAC,sBAAqB,4VAA2V,GAAE,aAAY,EAAC,oBAAmB,o7BAAm7B,GAAE,aAAY,EAAC,oBAAmB,4PAA2P,GAAE,OAAM,EAAC,cAAa,u0JAAs0J,GAAE,MAAK,EAAC,aAAY,k7BAAi7B,GAAE,MAAK,EAAC,aAAY,odAAmd,GAAE,OAAM,EAAC,cAAa,0nCAAynC,GAAE,OAAM,EAAC,cAAa,01DAAy1D,GAAE,SAAQ,EAAC,gBAAe,iuDAAguD,GAAE,IAAG,EAAC,WAAU,kQAAiQ,EAAC,GAAE,QAAO,EAAC,eAAc,+5BAA85B,eAAc,m3BAAk3B,GAAE,SAAQ,EAAC,gBAAe,s5EAAq5E,EAAC,GAAE,SAAQ,EAAC,gBAAe,8DAA6D,gBAAe,mIAAkI,GAAE,UAAS,EAAC,iBAAgB,yKAAwK,iBAAgB,iaAAga,GAAE,MAAK,EAAC,cAAa,mfAAkf,iBAAgB,4OAA2O,EAAC;;;ACA/7iI,SAASC,GAAEC,IAAE;AAAC,MAAID,KAAEC;AAAE,SAAOA,GAAE,MAAM,GAAG,EAAE,QAAS,CAAAC,QAAG;AAAC,IAAAF,OAAIA,KAAEA,GAAEE,GAAC;AAAA,EAAE,CAAE,GAAEF;AAAC;AAAC,IAAMG,KAAE,IAAIF,GAAED,EAAC;AAAE,SAASI,GAAEF,KAAE;AAAC,SAAOC,GAAE,gBAAgBD,GAAC;AAAC;;;ACAvL,IAAMG,KAAE,EAAC,SAAQ,EAAC,cAAaC,GAAE,4BAA4B,GAAE,gBAAeA,GAAE,4BAA4B,EAAC,GAAE,YAAW,oBAAI,IAAI,CAAC,CAAC,SAAQ,CAAC,CAAC,CAAC,EAAC;;;ACA2U,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,oBAAI;AAAA,EAAG;AAAA,EAAC,aAAa,EAAC,SAAQA,IAAC,GAAEC,IAAE;AAAC,IAAAA,MAAGA,GAAE,SAAS,SAAS,IAAED,IAAE,yBAAyB,EAAE,KAAI,EAAE,KAAI,EAAE,KAAI,EAAE,GAAG,IAAEA,IAAE,yBAAyB,EAAE,KAAI,EAAE,qBAAoB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,IAAE,mBAAmB,IAAE,GAAEA,IAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,IAAE,oBAAoB,CAAC,GAAEA,IAAE,sBAAsB,IAAE;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,gBAAgB;AAAE,IAAAF,GAAE,OAAOD,EAAC;AAAE,UAAMI,KAAEH,GAAE,YAAYE,EAAC;AAAE,MAAEC,EAAC,MAAIJ,GAAE,SAAS,MAAM,KAAK,IAAI,GAAEA,GAAE,cAAc,aAAaA,GAAE,OAAO,GAAEA,GAAE,QAAQ,mBAAmB,EAAE,OAAMC,GAAE,YAAW,GAAG,GAAEG,GAAE,eAAgB,CAAAL,QAAG;AAAC,YAAMI,KAAEE,GAAE,KAAKN,IAAE,WAAW,EAAE;AAAc,WAAK,kBAAkBI,EAAC,KAAG,KAAK,aAAaH,IAAEC,IAAEF,KAAEG,EAAC;AAAA,IAAC,CAAE;AAAA,EAAE;AAAA,EAAC,mBAAmBH,KAAEO,IAAEC,IAAE;AAAC,UAAK,EAAC,cAAaC,IAAE,YAAWC,IAAE,OAAMX,IAAE,aAAYY,GAAC,IAAEJ;AAAE,MAAEI,EAAC,KAAG,cAAYA,GAAE,SAAOX,IAAE,cAAc,gBAAeW,GAAE,QAAQ,GAAEX,IAAE,aAAa,iBAAgBW,GAAE,QAAQ,IAAGX,IAAE,aAAa,gBAAeU,EAAC,GAAEV,IAAE,oBAAoB,cAAaQ,GAAE,WAAW,QAAQ,GAAER,IAAE,oBAAoB,cAAaD,GAAE,QAAQ,GAAEC,IAAE,oBAAoB,aAAYQ,GAAE,WAAW,GAAG,GAAER,IAAE,oBAAoB,qBAAoBD,GAAE,eAAe,GAAEC,IAAE,aAAa,iBAAgB,KAAK,MAAMS,KAAE,EAAC,CAAC,GAAET,IAAE,aAAa,0BAAyBO,GAAE,cAAc,IAAI,GAAEP,IAAE,aAAa,oBAAmBY,EAAC,GAAEZ,IAAE,aAAa,oBAAmBa,EAAC,GAAEb,IAAE,aAAa,oBAAmBc,EAAC,GAAEd,IAAE,aAAa,oBAAmBe,EAAC,GAAEf,IAAE,aAAa,oBAAmBgB,EAAC,GAAEhB,IAAE,aAAa,oBAAmBiB,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBjB,KAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAGH,IAAE,qBAAmBC,GAAE,cAAc,uBAAsBC,GAAE,iBAAiB,GAAEF,IAAE,oBAAkBC,GAAE,aAAa,2BAA0BC,GAAE,qBAAqB,GAAEF,IAAE,kBAAiB;AAAC,YAAMA,MAAEE,GAAE,oBAAoBC,GAAE,IAAI,KAAK;AAAE,cAAMH,QAAIC,GAAE,cAAc,4BAA2BD,IAAE,MAAM,GAAEC,GAAE,cAAc,2BAA0BD,IAAE,KAAK;AAAA,IAAE;AAAC,IAAAA,IAAE,mBAAiBC,GAAE,aAAa,uCAAsCC,GAAE,4BAA4B;AAAA,EAAC;AAAA,EAAC,8BAA8BF,KAAEC,IAAEC,IAAE;AAAC,IAAAF,IAAE,YAAUC,GAAE,cAAc,mBAAkBC,GAAE,aAAa,GAAED,GAAE,cAAc,cAAaC,GAAE,QAAQ,IAAGF,IAAE,cAAYC,GAAE,cAAc,qBAAoBC,GAAE,eAAe,GAAED,GAAE,cAAc,iBAAgBC,GAAE,WAAW;AAAA,EAAE;AAAA,EAAC,uBAAuBF,KAAEC,IAAEC,IAAE;AAAC,IAAAF,IAAE,cAAYC,GAAE,aAAa,oBAAmB,iBAAeC,GAAE,qBAAqB,iBAAe,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBF,KAAEC,IAAEC,KAAE,CAAC,OAAO,GAAE;AAAC,UAAMC,KAAEF,GAAE,cAAc,UAASG,KAAEF,GAAE,IAAK,CAAAF,QAAGG,GAAE,UAAW,CAAAF,OAAGA,GAAE,SAAOD,GAAE,CAAE,GAAEK,KAAE,GAAGL,GAAC,IAAII,GAAE,KAAK,GAAG,CAAC;AAAG,QAAIc,MAAE,KAAK,aAAa,IAAIb,EAAC;AAAE,QAAG,CAACa,KAAE;AAAC,YAAMlB,MAAEC,GAAE,SAAQC,KAAED,GAAE,QAAQ,UAASkB,KAAE,IAAI,IAAIlB,GAAE,UAAU,GAAEmB,KAAEjB,GAAE,IAAK,CAAAH,SAAI,EAAC,GAAGA,IAAC,EAAG,GAAEO,KAAE,KAAK,IAAI,GAAGN,GAAE,WAAW,OAAO,CAAC,GAAEO,KAAE,EAAC,UAASY,GAAC;AAAE,UAAIX,KAAE;AAAE,iBAAUR,MAAKG,IAAE;AAAC,cAAMJ,MAAEG,GAAEF,EAAC;AAAE,QAAAO,GAAE,SAAS,KAAK,EAAC,OAAMR,IAAE,OAAM,MAAKA,IAAE,OAAK,KAAI,SAAQA,IAAE,SAAQ,YAAWA,IAAE,YAAW,QAAOE,KAAEF,IAAE,QAAO,QAAOE,IAAE,MAAKF,IAAE,KAAI,CAAC,GAAEQ,GAAE,SAAS,KAAK,EAAC,OAAMR,IAAE,OAAM,MAAKA,IAAE,OAAK,KAAI,SAAQA,IAAE,SAAQ,YAAWA,IAAE,YAAW,QAAO,IAAEE,KAAEF,IAAE,QAAO,QAAOE,IAAE,MAAKF,IAAE,KAAI,CAAC,GAAEmB,GAAE,IAAInB,IAAE,OAAK,KAAIO,KAAG,EAAEE,EAAC,GAAEU,GAAE,IAAInB,IAAE,OAAK,KAAIO,KAAG,EAAEE,EAAC;AAAA,MAAC;AAAC,MAAAS,MAAE,EAAC,eAAcV,IAAE,YAAWW,IAAE,SAAQnB,IAAC,GAAE,KAAK,aAAa,IAAIK,IAAEa,GAAC;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAC;;;ACA/nG,IAAMG,KAAE,EAAC,QAAO,kBAAiB,cAAa,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,kBAAiB,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,GAAE,EAAC,UAAS,GAAE,MAAK,wBAAuB,OAAM,GAAE,MAAK,EAAE,eAAc,GAAE,EAAC,UAAS,GAAE,MAAK,QAAO,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,WAAU,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,kBAAiB,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,yBAAwB,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,CAAC,EAAC,GAAE,mBAAkB,CAAC,kBAAiB,aAAa,EAAC;AAAE,IAAM,IAAN,cAAgBC,GAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAOC,GAAE;AAAA,EAAM;AAAA,EAAC,kBAAkBC,KAAE;AAAC,WAAOA,QAAI,EAAE,WAASA,QAAI,EAAE;AAAA,EAAS;AAAA,EAAC,aAAaC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,SAAQC,IAAE,cAAaR,IAAE,OAAMS,IAAE,aAAYC,IAAE,eAAcC,IAAE,oBAAmBV,GAAC,IAAEE,IAAES,KAAEC,GAAE,KAAKR,GAAE,WAAW,GAAEQ,KAAE,EAAED,GAAE,IAAI,GAAEE,KAAE,EAAEJ,EAAC,KAAG,cAAYA,GAAE,MAAK,EAAC,QAAOK,IAAE,cAAaC,IAAE,mBAAkBC,GAAC,IAAE,EAAEJ,GAAE,aAAYd,EAAC;AAAE,QAAImB,KAAE,EAAE,WAAUC,KAAE,GAAEP,GAAE,MAAKI,EAAC;AAAE,IAAAF,OAAIK,KAAE,KAAK,iBAAiBd,GAAE,aAAYc,IAAEF,EAAC,GAAEC,KAAE,EAAE;AAAQ,UAAK,EAAC,YAAWE,IAAE,eAAcC,GAAC,IAAEF,IAAEG,KAAEd,GAAE,gBAAgB,mBAAmBL,IAAES,IAAEG,IAAEK,IAAEd,EAAC;AAAE,QAAGL,MAAG,EAAEU,EAAC,KAAG,CAACW,GAAE,SAAS,QAAO,KAAKX,GAAE;AAAE,IAAAJ,GAAE,WAAWe,EAAC,GAAEV,GAAE,kBAAgBJ,GAAE,eAAe,aAAaD,IAAEe,IAAEV,IAAE,IAAE,GAAE,KAAK,mBAAmBU,IAAEnB,IAAEC,EAAC;AAAE,UAAMmB,KAAEX,GAAE,aAAWH,GAAE,kBAAgBA,GAAE;AAAY,IAAAa,GAAE,oBAAoB,iBAAgBC,EAAC,GAAE,KAAK,mBAAmBX,IAAEU,IAAEtB,IAAEI,EAAC,GAAE,KAAK,8BAA8BQ,IAAEU,IAAEtB,EAAC,GAAE,KAAK,uBAAuBY,IAAEU,IAAEtB,EAAC;AAAE,UAAMwB,KAAEnB,GAAE,OAAO,OAAOE,IAAEc,IAAED,IAAEN,EAAC;AAAE,QAAIW,KAAEpB,GAAE,YAAWqB,KAAErB,GAAE,YAAU,YAAY;AAAkB,IAAAS,OAAIW,MAAG,GAAEC,MAAG,IAAGnB,GAAE,QAAQiB,EAAC,GAAE,KAAK,aAAarB,IAAEC,IAAEkB,IAAEJ,IAAEO,IAAEC,IAAEZ,EAAC,GAAEP,GAAE,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,aAAaL,KAAEyB,IAAExB,IAAEC,IAAEwB,KAAEvB,IAAEwB,IAAE;AAAC,IAAA3B,IAAE,QAAQ,aAAaE,IAAEwB,KAAE,EAAE,cAAavB,EAAC;AAAA,EAAC;AAAC;;;ACA3tE,IAAMyB,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,OAAK,KAAK,YAAY;AAAA,EAAI;AAAA,EAAC,cAAcA,KAAEC,KAAE;AAAC,WAAO;AAAA,EAAI;AAAC;;;ACAyG,SAASC,GAAEA,IAAEC,IAAE;AAAC,QAAK,EAAC,cAAaC,IAAE,kBAAiBC,GAAC,IAAEH,GAAE,cAAaI,KAAEF,MAAA,gBAAAA,GAAG,cAAaG,KAAEH,MAAA,gBAAAA,GAAG,oBAAmBI,KAAEJ,MAAA,gBAAAA,GAAG,kBAAiBK,KAAEL,MAAA,gBAAAA,GAAG,wBAAuBM,KAAEN,MAAA,gBAAAA,GAAG,YAAWO,KAAEN,MAAA,gBAAAA,GAAG,cAAaO,KAAEP,MAAA,gBAAAA,GAAG,kBAAiBQ,KAAER,MAAA,gBAAAA,GAAG,YAAWS,KAAE,EAAEZ,GAAE,UAAU,EAAE,iBAAiB;AAAO,MAAG,CAACI,MAAG,CAACE,GAAE,OAAM,IAAIH,GAAE,iCAAgC,sGAAsG;AAAE,MAAG,CAACM,MAAG,CAACC,GAAE,OAAM,IAAIP,GAAE,sCAAqC,iIAAiI;AAAE,MAAG,EAAEQ,MAAGC,MAAGF,IAAG,OAAM,IAAIP,GAAE,+BAA8B,kGAAgGS,KAAE,KAAG,qFAAqF;AAAE,QAAMC,KAAET,MAAGK,MAAGE,MAAGC,IAAEE,KAAER,MAAGI,IAAEK,KAAEV,IAAEW,KAAET,IAAEU,KAAE,CAAC,EAACd,MAAA,gBAAAA,GAAG,OAAKe,KAAE,CAAC,EAACf,MAAA,gBAAAA,GAAG;AAAK,MAAGU,OAAIE,MAAG,CAACC,IAAG,QAAOD,MAAGd,GAAE,SAAS,mFAAmF,GAAE,EAAC,UAAS,EAAE,OAAM,cAAac,KAAE,EAAE,SAAO,EAAE,SAAQ,aAAYE,KAAE,EAAE,MAAI,EAAE,MAAK,gBAAeA,KAAE,EAAE,OAAK,EAAE,KAAI;AAAE,MAAGH,GAAE,QAAOE,MAAGf,GAAE,SAAS,wFAAwF,GAAE,EAAC,UAASO,IAAE,cAAaQ,KAAE,EAAE,SAAO,EAAE,SAAQ,aAAYE,KAAE,EAAE,MAAI,EAAE,MAAK,gBAAeA,KAAE,EAAE,OAAK,EAAE,KAAI;AAAE,QAAM,IAAIf,GAAE,oCAAmC,8GAA8G;AAAC;;;ACAlxB,IAAMgB,KAAE,EAAE,UAAU,oDAAoD;AAAE,SAASC,GAAEC,IAAE;AAAC,SAAM,cAAYA,GAAE;AAAI;AAAC,IAAMC,KAAN,cAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,cAAY,IAAI;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAE;AAAC,WAAOA,OAAI,EAAE;AAAA,EAAO;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,YAAY,QAAQ,GAAE,KAAK,cAAY;AAAA,EAAI;AAAA,EAAC,eAAc;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEE,KAAEC,KAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,GAAC,IAAE,KAAK,YAAY,mBAAmBL,GAAE,OAAO;AAAE,UAAM,aAAaA,IAAEE,KAAEC,KAAEC,KAAE,CAAC,GAAGA,IAAE,GAAGC,EAAC,IAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaL,IAAEE,KAAEC,KAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,cAAaC,IAAE,OAAMC,GAAC,IAAEV,IAAE,EAAC,gBAAeW,GAAC,IAAEF;AAAE,IAAAH,GAAEK,IAAE,SAAS;AAAE,UAAK,EAAC,gBAAeC,IAAE,QAAOC,IAAE,eAAcC,GAAC,IAAEH,IAAEI,KAAEF,MAAG,MAAID,KAAEA,KAAEF,GAAE,QAAM;AAAG,IAAAP,IAAE,aAAa,YAAWY,EAAC,GAAER,OAAIJ,IAAE,aAAa,mBAAkBW,EAAC,GAAEN,GAAE,mBAAmB,EAAE,QAAON,IAAE,YAAW,GAAG,IAAGM,GAAE,aAAaJ,IAAEC,IAAE,EAAE,cAAaC,EAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,EAAC,QAAO,0BAAyB,QAAO,0BAAyB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAE,IAAM,IAAN,cAAgBJ,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,KAAK,YAAY;AAAA,EAAI;AAAA,EAAC,cAAc,EAAC,aAAYF,GAAC,GAAE;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,MAAK,KAAK,2BAAyB,EAAE,KAAK,wBAAwB,GAAE,EAAE,KAAK,sBAAsB,KAAG,KAAK,uBAAuB,yBAAyB,GAAE,KAAK,iCAA+B,EAAE,KAAK,8BAA8B,GAAE,KAAK,yBAAuB,EAAE,KAAK,sBAAsB,GAAE,KAAK,0BAAwB,EAAE,KAAK,uBAAuB,GAAE,KAAK,YAAU,EAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,UAAK,EAAC,SAAQE,KAAE,cAAaE,IAAE,aAAYC,GAAC,IAAEL,IAAE,EAAC,gBAAeM,GAAC,IAAEF;AAAE,MAAE,EAAEC,EAAC,KAAG,cAAYA,GAAE,SAAON,GAAEO,EAAC,MAAI,KAAK,WAASJ,IAAE,0BAA0B,GAAE,KAAK,gBAAcA,IAAE,YAAY,GAAEI,GAAEA,IAAE,SAAS,GAAE,KAAK,eAAeN,EAAC,GAAE,KAAK,iBAAiBE,KAAEI,EAAC,GAAEJ,IAAE,gBAAgB,KAAK,sBAAsB,GAAEA,IAAE,YAAY,GAAE,GAAE,KAAK,uBAAuB,OAAM,KAAK,uBAAuB,MAAM,GAAEA,IAAE,sBAAsB,IAAE,GAAEA,IAAE,mBAAmB,IAAE,GAAEA,IAAE,iBAAiB,EAAE,KAAI,EAAE,GAAG,GAAEA,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,IAAE,MAAM,EAAE,gBAAgB;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,SAAK,WAAS,MAAK,KAAK,gBAAc;AAAA,EAAI;AAAA,EAAC,KAAKF,IAAE;AAAC,UAAK,EAAC,SAAQE,KAAE,SAAQE,IAAE,cAAaC,IAAE,aAAYC,GAAC,IAAEN,IAAE,EAAC,gBAAeO,GAAC,IAAEF;AAAE,QAAG,EAAEC,EAAC,KAAG,cAAYA,GAAE,QAAM,CAACP,GAAEQ,EAAC,EAAE;AAAO,UAAK,EAAC,SAAQC,GAAC,IAAE,KAAK,mBAAmBN,GAAC,GAAEc,KAAEZ,GAAE,gBAAgB,WAAW,GAAEI,EAAC;AAAE,IAAAN,IAAE,WAAWc,EAAC,GAAEd,IAAE,gBAAgB,KAAK,QAAQ,GAAEA,IAAE,YAAY,GAAE,GAAE,KAAK,cAAc,OAAM,KAAK,cAAc,MAAM,GAAEA,IAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,IAAE,sBAAsB,KAAE;AAAE,UAAK,EAAC,QAAOO,IAAE,YAAWQ,IAAE,cAAaC,GAAC,IAAEX;AAAE,IAAAL,IAAE,YAAY,KAAK,0BAAyB,CAAC,GAAEA,IAAE,YAAY,KAAK,yBAAwB,CAAC,GAAEc,GAAE,aAAa,aAAY,CAAC,GAAEA,GAAE,aAAa,cAAa,CAAC,GAAEA,GAAE,aAAa,2BAA0BC,IAAE,IAAEC,EAAC,GAAEF,GAAE,aAAa,0BAAyB,KAAGP,KAAEA,KAAE,KAAK,GAAG,GAAE,KAAK,UAAU,KAAK;AAAA,EAAC;AAAA,EAAC,eAAe,EAAC,SAAQT,IAAE,SAAQE,IAAC,GAAE;AAAC,UAAK,EAAC,UAASC,KAAE,cAAaC,IAAE,aAAYC,IAAE,gBAAeE,IAAE,aAAYC,IAAE,6BAA4BQ,GAAC,IAAE,KAAK,mBAAmBhB,EAAC,GAAE,EAAC,OAAMS,IAAE,QAAOQ,GAAC,IAAE,KAAK,eAAcC,KAAET,KAAED,IAAEE,KAAEO,KAAET;AAAE,SAAK,6BAA2B,KAAK,2BAAyB,IAAIW,GAAEnB,IAAE,EAAC,QAAO,EAAE,YAAW,aAAYK,IAAE,gBAAeE,IAAE,UAASJ,KAAE,cAAaC,IAAE,UAAS,EAAE,eAAc,OAAMc,IAAE,QAAOR,GAAC,CAAC,IAAGM,OAAI,KAAK,mCAAiC,KAAK,iCAA+B,IAAIV,GAAEN,IAAE,EAAC,OAAMkB,IAAE,QAAOR,IAAE,gBAAe,EAAE,cAAa,CAAC,KAAI,KAAK,2BAAyB,KAAK,yBAAuB,IAAI,EAAEV,IAAE,CAAC,GAAE,KAAK,0BAAyBgB,KAAEd,IAAE,uBAAuB,IAAE,KAAK,8BAA8B,IAAG,KAAK,4BAA0B,KAAK,0BAAwB,IAAIiB,GAAEnB,IAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,cAAa,EAAE,QAAO,UAAS,EAAE,cAAa,CAAC,IAAG,KAAK,cAAY,KAAK,YAAU,IAAIQ,GAAER,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,EAAE;AAAA,EAAC,iBAAiBA,IAAEE,KAAE;AAAC,UAAK,EAAC,cAAaE,IAAE,UAASC,GAAC,IAAEH;AAAE,SAAK,sBAAoBE,OAAI,KAAK,wBAAwB,OAAOC,GAAE,SAAO,GAAE,CAAC,GAAE,KAAK,wBAAwB,QAAQA,EAAC,GAAE,KAAK,oBAAkBD;AAAG,UAAK,EAAC,aAAYE,IAAE,6BAA4BC,GAAC,IAAE,KAAK,mBAAmBP,EAAC,GAAE,EAAC,OAAMQ,IAAE,QAAOQ,GAAC,IAAE,KAAK,eAAcP,KAAED,KAAEF,IAAEW,KAAED,KAAEV,IAAE,EAAC,OAAMY,IAAE,QAAOR,GAAC,IAAE,KAAK;AAAuB,QAAGQ,OAAIT,MAAGC,OAAIO,IAAE;AAAC,YAAMjB,KAAE,KAAK,uBAAuB;AAAuB,UAAGO,MAAG,EAAEP,EAAC,GAAE;AAAC,cAAK,EAAC,OAAME,KAAE,QAAOC,IAAC,IAAEH,GAAE;AAAW,QAAAE,QAAIO,MAAGN,QAAIc,OAAInB,GAAE,UAAU,+DAA+D,GAAE,KAAK,uBAAuB,yBAAyB;AAAA,MAAE;AAAC,WAAK,uBAAuB,OAAOW,IAAEQ,EAAC;AAAA,IAAC;AAAC,IAAAV,MAAGP,GAAE,gBAAgB,KAAK,UAAS,KAAK,wBAAuB,GAAE,GAAE,KAAK,SAAS,OAAM,KAAK,SAAS,QAAO,GAAE,GAAE,KAAK,uBAAuB,OAAM,KAAK,uBAAuB,QAAO,EAAE,oBAAmB,EAAE,OAAO;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,QAAG,EAAE,KAAK,eAAe,GAAE;AAAC,YAAME,MAAEM,GAAER,IAAEF,EAAC,GAAEK,MAAEH,GAAE,SAAOG,GAAE;AAAO,WAAK,kBAAgB,EAAC,GAAGD,KAAE,6BAA4BC,KAAE,aAAYA,MAAE,IAAE,MAAI,SAAQD,IAAE,aAAW,EAAE,QAAM,CAAC,2BAA2B,IAAE,CAAC,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAe;AAAC;;;ACAviL,IAAMkB,KAAE,EAAC,UAAS,CAAC,IAAIC,GAAE,SAAQ,GAAE,EAAE,MAAK,GAAE,CAAC,CAAC,EAAC;AAA/C,IAAiDA,KAAE,EAAC,UAAS,CAAC,IAAIA,GAAE,SAAQ,GAAE,EAAE,MAAK,GAAE,CAAC,GAAE,IAAIA,GAAE,SAAQ,GAAE,EAAE,MAAK,GAAE,CAAC,CAAC,EAAC;AAAtH,IAAwHC,KAAE,EAAC,UAAS,CAAC,IAAID,GAAE,SAAQ,GAAE,EAAE,gBAAe,GAAE,CAAC,CAAC,EAAC;;;ACA1P,IAAME,KAAE,EAAC,SAAQ,EAAC,cAAaC,GAAE,wBAAwB,GAAE,gBAAeA,GAAE,wBAAwB,EAAC,GAAE,YAAW,oBAAI,IAAI,CAAC,CAAC,SAAQ,CAAC,CAAC,CAAC,EAAC;;;ACAiQ,IAAMC,KAAE,MAAI,GAAE,QAAO,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,CAAC,EAAC,CAAC;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAOC,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,YAAU,KAAK,SAAS,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAa,EAAC,SAAQA,IAAC,GAAE;AAAC,IAAAA,IAAE,sBAAsB,IAAE,GAAEA,IAAE,mBAAmB,KAAE,GAAEA,IAAE,sBAAsB,KAAE,GAAEA,IAAE,aAAa,OAAG,OAAG,OAAG,KAAE,GAAEA,IAAE,aAAa,EAAE,MAAK,EAAE,MAAK,EAAE,OAAO,GAAEA,IAAE,oBAAoB,GAAG,GAAEA,IAAE,mBAAmB,EAAE,QAAO,GAAE,GAAG;AAAA,EAAC;AAAA,EAAC,KAAKD,KAAEE,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,OAAMC,IAAE,eAAcC,IAAE,oBAAmBN,GAAC,IAAEC,KAAEM,KAAER,GAAE,GAAES,KAAEL,GAAE,OAAOC,IAAEC,IAAEE,GAAE,YAAWA,GAAE,aAAa;AAAE,MAAEC,GAAE,WAAW,MAAI,KAAK,aAAW,KAAK,WAASC,GAAEL,IAAEK,EAAC,IAAGT,MAAG,EAAEM,EAAC,KAAG,CAAC,KAAK,SAAS,WAASA,GAAE,KAAGF,GAAE,WAAW,KAAK,QAAQ,GAAE,KAAK,SAAS,cAAc,iBAAgB,CAAC,GAAE,CAAC,CAAC,GAAE,KAAK,SAAS,cAAc,WAAU,KAAK,MAAM,GAAE,KAAK,SAAS,oBAAoB,aAAYC,GAAE,WAAW,GAAED,GAAE,QAAQI,EAAC,GAAEJ,GAAE,aAAa,EAAE,WAAUI,GAAE,YAAY,MAAK,EAAE,cAAa,CAAC,GAAEJ,GAAE,QAAQ;AAAA,EAAG;AAAC;;;ACAjkC,IAAMM,KAAE,MAAI,GAAE,WAAU,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,CAAC,GAAE,KAAI,CAAC,EAAC,UAAS,GAAE,MAAK,QAAO,OAAM,GAAE,MAAK,EAAE,eAAc,CAAC,EAAC,CAAC;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAM,EAAC,QAAO,mBAAkB,QAAO,mBAAkB,YAAW,oBAAI,IAAI,CAAC,CAAC,SAAQ,CAAC,GAAE,CAAC,QAAO,CAAC,CAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,aAAa,EAAC,SAAQC,GAAC,GAAE;AAAC,IAAAA,GAAE,mBAAmB,IAAE,GAAEA,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,GAAE,yBAAyB,EAAE,KAAI,EAAE,qBAAoB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,oBAAoB,CAAC,GAAEA,GAAE,sBAAsB,IAAE,GAAEA,GAAE,mBAAmB,EAAE,SAAQ,KAAI,GAAG;AAAA,EAAC;AAAA,EAAC,KAAKC,KAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,SAAQC,IAAE,eAAcC,IAAE,oBAAmBP,GAAC,IAAEG;AAAE,QAAG,CAACC,GAAE,QAAQ;AAAO,UAAK,EAAC,iBAAgBI,IAAE,SAAQC,IAAE,cAAaC,IAAE,sBAAqBC,IAAE,SAAQC,GAAC,IAAER;AAAE,IAAAD,IAAE,SAAS,MAAM,KAAK,IAAI;AAAE,UAAMU,KAAEP,GAAE,gBAAgB,WAAW,KAAK,KAAK;AAAE,QAAGN,MAAG,EAAEO,EAAC,KAAG,CAACM,GAAE,SAAS,QAAO,KAAKN,GAAE;AAAE,UAAMO,KAAEf,GAAE,GAAEgB,KAAEX,GAAE,OAAOC,IAAES,GAAE,eAAcA,GAAE,UAAU;AAAE,QAAG,CAACC,GAAE;AAAO,IAAAV,GAAE,QAAQU,EAAC,GAAEV,GAAE,WAAWQ,EAAC,GAAER,GAAE,YAAYO,IAAE,CAAC,GAAEC,GAAE,oBAAoB,aAAYJ,EAAC,GAAEI,GAAE,aAAa,aAAY,CAAC,GAAEA,GAAE,aAAa,aAAYL,EAAC,GAAEK,GAAE,cAAc,iBAAgBF,EAAC;AAAE,UAAMK,KAAEN,KAAE,KAAG;AAAE,IAAAL,GAAE,WAAW,EAAE,gBAAe,GAAEW,EAAC,GAAEX,GAAE,QAAQ,GAAEF,IAAE,SAAS,IAAI,KAAK,IAAI;AAAA,EAAC;AAAC;;;ACA1jC,SAASc,GAAEC,IAAE;AAAC,QAAMC,MAAE,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,QAAO,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,YAAW,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,WAAU,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,UAAS,OAAM,GAAE,MAAK,EAAE,eAAc,GAAE,EAAC,UAAS,GAAE,MAAK,UAAS,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,UAAS,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,CAAC,EAAC;AAAE,UAAOD,GAAE,eAAc;AAAA,IAAC,KAAK,EAAE;AAAA,IAAO,KAAK,EAAE;AAAoB,MAAAC,IAAE,SAAS,OAAO,GAAE,CAAC,GAAEA,IAAE,SAAS,OAAO,GAAE,CAAC;AAAA,EAAC;AAAC,SAAM,EAAC,QAAO,kBAAiB,cAAaA,IAAC;AAAC;AAAC,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAOC,GAAE;AAAA,EAAI;AAAA,EAAC,kBAAkBJ,IAAE;AAAC,WAAOA,OAAI,EAAE;AAAA,EAAW;AAAA,EAAC,aAAaK,IAAEC,KAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQN,IAAE,SAAQO,IAAE,cAAaN,IAAE,eAAcO,IAAE,aAAYC,IAAE,eAAcC,IAAE,oBAAmBR,GAAC,IAAEC,IAAEQ,KAAEC,GAAE,KAAKP,GAAE,WAAW,GAAEQ,KAAE,EAAEF,GAAE,IAAI,GAAEG,KAAE,EAAEL,EAAC,KAAG,cAAYA,GAAE,MAAKM,KAAER,GAAE,iBAAgB,EAAC,QAAOS,IAAE,cAAaC,IAAE,mBAAkBC,GAAC,IAAE,EAAEL,GAAE,aAAYhB,GAAEc,EAAC,CAAC;AAAE,QAAIQ,KAAE,EAAE,WAAUC,KAAE,GAAET,GAAE,MAAKM,EAAC;AAAE,IAAAH,OAAIM,KAAE,KAAK,iBAAiBf,GAAE,aAAYe,IAAEF,EAAC,GAAEC,KAAE,EAAE;AAAQ,UAAK,EAAC,YAAWE,IAAE,eAAcC,GAAC,IAAEF,IAAEG,KAAER,GAAE,mBAAmBZ,IAAEQ,IAAEK,IAAEK,IAAEf,EAAC;AAAE,QAAGJ,MAAG,EAAEQ,EAAC,KAAG,CAACa,GAAE,SAAS,QAAO,KAAKb,GAAE;AAAE,QAAGV,GAAE,WAAWuB,EAAC,GAAE,KAAK,mBAAmBA,IAAEpB,IAAEC,GAAC,GAAEmB,GAAE,aAAa,gBAAe,MAAInB,IAAE,IAAI,KAAI,MAAIA,IAAE,IAAI,GAAG,GAAEO,GAAE,gBAAe;AAAC,MAAAJ,GAAE,eAAe,aAAaP,IAAEuB,IAAEZ,EAAC;AAAE,YAAMb,KAAE,IAAE,MAAIU,KAAEJ,IAAE,IAAI;AAAO,MAAAmB,GAAE,aAAa,gBAAezB,EAAC;AAAA,IAAC;AAAC,UAAM0B,KAAE,IAAErB,GAAE;AAAW,IAAAoB,GAAE,aAAa,UAASC,EAAC,GAAED,GAAE,aAAa,kBAAiBC,EAAC,GAAE,KAAK,mBAAmBb,IAAEY,IAAEtB,IAAEG,GAAC,GAAE,KAAK,8BAA8BO,IAAEY,IAAEtB,EAAC;AAAE,UAAMwB,KAAEpB,GAAE,OAAO,OAAOL,IAAEsB,IAAED,IAAEP,EAAC;AAAE,QAAIF,KAAEP,GAAE,YAAW,IAAEA,GAAE,YAAU,YAAY;AAAkB,IAAAS,OAAIF,MAAG,GAAE,KAAG,IAAGZ,GAAE,QAAQyB,EAAC,GAAE,KAAK,WAAWtB,IAAEC,KAAEmB,IAAEJ,IAAEP,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWd,IAAEC,KAAEI,IAAEC,KAAEsB,IAAErB,IAAE;AAAC,IAAAP,GAAE,QAAQ,aAAaM,KAAEsB,IAAE,EAAE,cAAarB,EAAC;AAAA,EAAC;AAAC;;;ACAtwC,IAAMsB,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,kBAAgB,GAAE,KAAK,eAAa,MAAK,KAAK,eAAa,IAAI,WAAW,CAAC,GAAEC,EAAC,CAAC,GAAE,KAAK,UAAQ,MAAK,KAAK,WAAS,EAAC,QAAO,WAAU,QAAO,WAAU,YAAW,oBAAI,IAAI,CAAC,CAAC,SAAQ,CAAC,CAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,iBAAiB,GAAE,KAAK,UAAQ,EAAE,KAAK,OAAO,GAAE,KAAK,UAAQ,EAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAOC,GAAE;AAAA,EAAI;AAAA,EAAC,kBAAkBC,IAAE;AAAC,WAAOA,OAAI,EAAE;AAAA,EAAW;AAAA,EAAC,WAAWA,IAAEC,IAAEC,KAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,aAAYC,GAAC,IAAEN;AAAE,QAAG,EAAEM,EAAC,KAAG,cAAYA,GAAE,KAAK,OAAM,WAAWN,IAAEC,IAAEC,KAAEC,IAAEC,IAAEC,EAAC;AAAA,SAAM;AAAC,YAAME,MAAE,KAAK,kBAAkBP,IAAEC,IAAEC,KAAEE,IAAEC,EAAC;AAAE,WAAK,gBAAgBL,IAAEC,IAAEM,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBP,IAAEC,IAAEC,KAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,SAAQC,IAAE,cAAaC,IAAE,eAAcC,IAAE,oBAAmBE,GAAC,IAAER,IAAES,KAAEL,GAAE,gBAAgB,WAAW,KAAK,QAAQ;AAAE,QAAGI,MAAG,EAAEF,EAAC,KAAG,CAACG,GAAE,SAAS,QAAO,KAAKH,GAAE;AAAE,UAAK,EAAC,gBAAeI,GAAC,IAAEL;AAAE,IAAAF,GAAEO,IAAE,aAAa;AAAE,UAAMb,KAAE,KAAK,sBAAsBM,IAAE,KAAK,SAAS,YAAW,EAAC,UAAS,CAAC,EAAC,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,OAAM,SAAQ,GAAE,YAAW,OAAG,QAAO,GAAE,QAAO,EAAC,CAAC,EAAC,CAAC;AAAE,IAAAA,GAAE,sBAAsB,IAAE,GAAEA,GAAE,WAAWM,EAAC,GAAEA,GAAE,aAAa,oBAAmB,CAAC,GAAEA,GAAE,aAAa,aAAY,KAAK,aAAa,CAAC,CAAC,GAAEA,GAAE,aAAa,aAAY,KAAK,IAAIC,GAAE,SAAQ,CAAC,CAAC,GAAED,GAAE,aAAa,gBAAe,OAAO,gBAAgB,GAAE,KAAK,mBAAmBA,IAAET,IAAEC,EAAC,GAAEE,GAAE,YAAYD,KAAE,KAAK,aAAa,CAAC,CAAC,GAAEC,GAAE,QAAQN,EAAC,GAAEM,GAAE,WAAW,EAAE,QAAO,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAEO,KAAEN,IAAEC,KAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,cAAaE,IAAE,eAAcE,GAAC,IAAER,IAAES,KAAEL,GAAE,YAAY,GAAE,EAAC,gBAAeM,GAAC,IAAEJ;AAAE,IAAAH,GAAEO,IAAE,aAAa;AAAE,UAAK,EAAC,UAASb,IAAE,QAAOc,IAAE,YAAWC,IAAE,iBAAgBC,IAAE,UAASC,GAAC,IAAEJ;AAAE,IAAAN,GAAE,YAAY,GAAE,GAAE,KAAI,GAAG;AAAE,UAAMW,KAAEX,GAAE,0BAA0B,GAAEY,KAAE,KAAK,WAAWZ,EAAC;AAAE,IAAAA,GAAE,gBAAgBY,EAAC,GAAEZ,GAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,GAAG,mBAAiBA,GAAE,GAAG,kBAAkB,GAAEA,GAAE,sBAAsB,KAAE;AAAE,UAAMa,KAAE,IAAE,MAAIT,KAAED,IAAE,IAAI,QAAOW,KAAE,GAAEC,KAAED,KAAE,OAAO,mBAAiBA,KAAE,OAAO,kBAAiBE,KAAE,IAAEH,MAAG,IAAEA,KAAGlB,KAAEF,KAAEG,GAAE,MAAM,QAAMH,KAAE;AAAE,WAAOI,GAAE,aAAa,oBAAmBgB,EAAC,GAAEhB,GAAE,aAAa,sBAAqBkB,MAAG,IAAE,OAAO,mBAAiB,IAAE,OAAO,iBAAiB,GAAElB,GAAE,oBAAoB,eAAcU,EAAC,GAAEV,GAAE,cAAc,cAAaW,EAAC,GAAEX,GAAE,cAAc,wBAAuBY,EAAC,GAAEZ,GAAE,aAAa,cAAa,KAAK,IAAI,GAAEa,KAAEf,KAAEqB,EAAC,CAAC,GAAE,KAAK,wBAAwBhB,IAAEH,IAAEK,IAAEY,EAAC,GAAEd,GAAE,aAAa,EAAE,WAAUF,KAAE,EAAE,cAAaC,EAAC,GAAEC,GAAE,YAAYK,GAAE,GAAEA,GAAE,GAAEA,GAAE,OAAMA,GAAE,MAAM,GAAEL,GAAE,gBAAgBW,EAAC,GAAEC,GAAE;AAAA,EAAY;AAAA,EAAC,WAAWhB,IAAE;AAAC,QAAG,EAAE,KAAK,OAAO,GAAE;AAAC,YAAMO,MAAE,KAAIN,KAAE,KAAIC,MAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,cAAa,EAAE,SAAQ,UAAS,EAAE,eAAc,OAAMK,KAAE,QAAON,GAAC,GAAEE,KAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,4BAA2B,GAAEC,KAAE,IAAID,GAAEH,IAAE,EAAC,OAAMO,KAAE,QAAON,IAAE,gBAAe,EAAE,cAAa,CAAC;AAAE,WAAK,UAAQ,IAAI,EAAED,IAAEG,IAAED,KAAEE,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,mBAAkB;AAAC,QAAG,KAAK,cAAa;AAAC,eAAQJ,KAAE,GAAEA,KAAE,KAAK,aAAa,QAAOA,KAAI,MAAK,aAAaA,EAAC,EAAE,QAAQ;AAAE,WAAK,eAAa;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAEO,KAAEN,IAAEC,KAAE;AAAC,UAAK,EAAC,gBAAeC,GAAC,IAAEF;AAAE,IAAAE,GAAEA,IAAE,aAAa;AAAE,UAAMC,KAAE,KAAK,0BAA0BJ,IAAEE,KAAEC,GAAE,IAAI;AAAE,IAAAI,IAAE,cAAc,iBAAgB,KAAK,YAAY;AAAE,aAAQF,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAL,GAAE,YAAYI,GAAEC,EAAC,GAAE,KAAK,aAAaA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBL,IAAEO,KAAEL,KAAE;AAAC,QAAG,EAAE,KAAK,OAAO,GAAE;AAAC,YAAMD,KAAE,GAAEE,KAAE,IAAI,WAAW,SAAOF,EAAC;AAAE,eAAQD,KAAE,GAAEA,KAAE,KAAIA,KAAI,UAAQO,MAAE,GAAEA,MAAE,KAAIA,MAAI,CAAAJ,GAAEF,MAAGM,MAAE,MAAIP,GAAE,IAAEO,KAAEJ,GAAEF,MAAGM,MAAE,MAAIP,MAAG,CAAC,IAAEA;AAAE,YAAMI,KAAEL,GAAE,aAAaC,IAAE,EAAE,aAAYG,EAAC;AAAE,WAAK,UAAQ,IAAIQ,GAAEX,IAAEO,KAAEL,KAAE,EAAC,UAASE,GAAC,GAAE,IAAI;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,0BAA0BJ,IAAEO,KAAEN,IAAE;AAAC,QAAG,KAAK,oBAAkBM,OAAG,KAAK,UAAQN,OAAI,KAAK,iBAAiB,GAAE,KAAK,kBAAgBM,KAAE,KAAK,QAAMN,KAAG,SAAO,KAAK,cAAa;AAAC,YAAME,KAAE,IAAII,GAAEN,EAAC;AAAE,WAAK,eAAa,CAAC,KAAK,wBAAwBD,IAAEO,KAAEJ,EAAC,GAAE,KAAK,wBAAwBH,IAAEO,KAAEJ,EAAC,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,wBAAwBH,IAAEO,KAAEN,IAAE;AAAC,UAAMC,MAAE,IAAI,aAAaK,MAAEA,MAAE,CAAC;AAAE,aAAQJ,KAAE,GAAEA,KAAED,IAAE,QAAOC,KAAI,CAAAD,IAAEC,EAAC,IAAEF,GAAE,SAAS;AAAE,WAAO,IAAIF,GAAEC,IAAE,EAAC,UAAS,EAAE,QAAO,aAAY,EAAE,MAAK,UAAS,EAAE,OAAM,cAAa,EAAE,SAAQ,OAAMO,KAAE,QAAOA,IAAC,GAAEL,GAAC;AAAA,EAAC;AAAC;;;ACAliI,IAAMmB,KAAE;AAAR,IAAY,IAAE;AAAG,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAOC,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJ/1B;AAIg2B,eAAK,oBAAL,mBAAsB,WAAU,KAAK,kBAAgB,OAAK,UAAK,qBAAL,mBAAuB,WAAU,KAAK,mBAAiB,OAAK,UAAK,8BAAL,mBAAgC,WAAU,KAAK,4BAA0B,OAAK,UAAK,+BAAL,mBAAiC,WAAU,KAAK,6BAA2B,MAAK,KAAK,UAAQ;AAAA,EAAI;AAAA,EAAC,aAAa,EAAC,SAAQD,IAAC,GAAE;AAAC,IAAAA,IAAE,mBAAmB,IAAE,GAAEA,IAAE,yBAAyB,EAAE,KAAI,EAAE,qBAAoB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,IAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,IAAE,oBAAoB,CAAC,GAAEA,IAAE,sBAAsB,KAAE;AAAA,EAAC;AAAA,EAAC,KAAKE,IAAED,KAAE;AAAC,UAAK,EAAC,SAAQE,IAAE,eAAcC,IAAE,oBAAmBC,GAAC,IAAEH;AAAE,QAAG,CAACD,IAAE,QAAQ;AAAO,QAAG,KAAK,kBAAkBE,EAAC,GAAEE,MAAG,EAAED,EAAC,MAAI,CAAC,KAAK,gBAAgB,YAAU,CAAC,KAAK,iBAAiB,UAAU,QAAO,KAAKA,GAAE;AAAE,IAAAD,GAAE,QAAQ,KAAK,yBAAyB,GAAEA,GAAE,WAAW,KAAK,eAAe,GAAE,KAAK,gBAAgB,oBAAoB,aAAYF,IAAE,WAAW,GAAG,GAAE,KAAK,gBAAgB,aAAa,iBAAgBA,IAAE,QAAOA,IAAE,MAAM,GAAE,KAAK,gBAAgB,aAAa,WAAU,CAAC,GAAE,KAAK,gBAAgB,cAAc,WAAU,KAAK,MAAM,GAAEE,GAAE,WAAW,EAAE,YAAW,GAAE,CAAC;AAAE,UAAMG,KAAE,KAAK,YAAYH,IAAEF,GAAC;AAAE,IAAAK,MAAGH,GAAE,QAAQ,KAAK,0BAA0B,GAAEA,GAAE,WAAW,KAAK,gBAAgB,GAAEA,GAAE,YAAYG,IAAE,CAAC,GAAE,KAAK,iBAAiB,oBAAoB,aAAYL,IAAE,WAAW,GAAG,GAAE,KAAK,iBAAiB,aAAa,WAAU,CAAC,GAAE,KAAK,iBAAiB,aAAa,iBAAgBA,IAAE,SAAOA,IAAE,OAAMA,IAAE,SAAOA,IAAE,MAAM,GAAE,KAAK,iBAAiB,aAAa,WAAU,GAAE,CAAC,GAAE,KAAK,iBAAiB,aAAa,gBAAeK,GAAE,WAAW,OAAMA,GAAE,WAAW,MAAM,GAAEH,GAAE,WAAW,EAAE,gBAAe,GAAE,CAAC,GAAEA,GAAE,QAAQ,KAAGA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,kBAAkBH,KAAE;AAAC,QAAG,KAAK,mBAAiB,KAAK,iBAAiB;AAAO,UAAME,KAAEA,GAAEF,KAAEE,EAAC,GAAEC,KAAED,GAAEF,KAAEC,EAAC,GAAEM,KAAE,IAAI,UAAU,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEC,KAAEC,GAAE,aAAaT,KAAE,EAAE,aAAYO,EAAC,GAAEG,KAAE,IAAIC,GAAEX,KAAEE,GAAE,YAAWD,IAAE,EAAC,UAASO,GAAC,CAAC,GAAEI,KAAE,IAAI,UAAU,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAED,KAAEF,GAAE,aAAaT,KAAE,EAAE,aAAYY,EAAC,GAAEC,KAAE,IAAIF,GAAEX,KAAEC,GAAE,YAAWA,IAAE,EAAC,UAASU,GAAC,CAAC;AAAE,SAAK,kBAAgBT,IAAE,KAAK,mBAAiBC,IAAE,KAAK,4BAA0BO,IAAE,KAAK,6BAA2BG;AAAA,EAAC;AAAA,EAAC,YAAYb,KAAEE,IAAE;AAAC,QAAGA,GAAE,WAASA,GAAE,iCAA+BA,GAAE,cAAc,QAAOA,GAAE;AAAQ,IAAAA,GAAE,+BAA6BA,GAAE,eAAc,KAAK,YAAU,KAAK,UAAQ,SAAS,cAAc,QAAQ,GAAE,KAAK,QAAQ,aAAa,MAAK,UAAU,GAAE,KAAK,QAAQ,aAAa,SAAQ,GAAGJ,EAAC,EAAE,GAAE,KAAK,QAAQ,aAAa,UAAS,GAAG,CAAC,EAAE,GAAE,KAAK,QAAQ,aAAa,SAAQ,cAAc;AAAG,UAAMG,MAAEC,GAAE;AAAc,QAAIC,KAAED,GAAE,IAAI;AAAG,IAAAA,GAAE,gBAAc,MAAIC,MAAG,KAAKF,GAAC;AAAI,UAAMG,KAAE,KAAK,SAAQC,KAAED,GAAE,WAAW,IAAI;AAAE,WAAOC,GAAE,OAAK,mBAAkBA,GAAE,YAAU,QAAOA,GAAE,eAAa,OAAMA,GAAE,UAAU,GAAE,GAAEP,IAAE,CAAC,GAAEG,MAAE,OAAKI,GAAE,YAAU,OAAMA,GAAE,SAAS,GAAE,GAAEP,IAAE,CAAC,GAAEO,GAAE,YAAU,YAAUA,GAAE,UAAU,GAAE,GAAEP,IAAE,CAAC,GAAEO,GAAE,YAAU,SAAQA,GAAE,SAASF,IAAE,GAAE,CAAC,GAAED,GAAE,UAAQ,IAAIO,GAAET,KAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,cAAa,EAAE,SAAQ,UAAS,EAAE,cAAa,GAAEI,EAAC,GAAEF,GAAE;AAAA,EAAO;AAAC;;;ACAz+G,IAAMY,KAAN,cAAgB,EAAC;AAAA,EAAC,kBAAkBC,KAAE;AAAC,WAAOA,QAAI,EAAE;AAAA,EAAS;AAAA,EAAC,aAAaC,IAAED,KAAED,IAAEG,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,GAAC,IAAEL,IAAE,EAAC,cAAaM,GAAC,IAAEN,IAAE,EAAC,gBAAeO,GAAC,IAAED;AAAE,IAAAR,GAAES,IAAE,WAAW,GAAET,GAAE,cAAc,YAAWS,GAAE,MAAM,GAAET,GAAE,cAAc,kBAAiBS,GAAE,YAAY,GAAET,GAAE,cAAc,iBAAgBS,GAAE,WAAW,GAAET,GAAE,cAAc,kBAAiBS,GAAE,YAAY,GAAET,GAAE,aAAa,gBAAeS,GAAE,cAAc,GAAET,GAAE,aAAa,qBAAoBS,GAAE,eAAe,GAAET,GAAE,aAAa,kBAAiBS,GAAE,YAAY,GAAEF,GAAE,aAAaJ,IAAEC,IAAE,EAAE,cAAaC,EAAC;AAAA,EAAC;AAAC;;;ACAvJ,IAAMK,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAOC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,eAAa;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,kBAAgB,KAAK,cAAc,QAAQ,GAAE,KAAK,gBAAc,OAAM,KAAK,4BAA0B,KAAK,wBAAwB,QAAQ,GAAE,KAAK,0BAAwB;AAAA,EAAK;AAAA,EAAC,aAAa,EAAC,SAAQA,IAAC,GAAE;AAAC,IAAAA,IAAE,qBAAqB,KAAE,GAAEA,IAAE,oBAAoB,KAAE,GAAEA,IAAE,sBAAsB,IAAE,GAAEA,IAAE,mBAAmB,KAAE,GAAEA,IAAE,aAAa,OAAG,OAAG,OAAG,KAAE,GAAEA,IAAE,aAAa,EAAE,MAAK,EAAE,MAAK,EAAE,OAAO,GAAEA,IAAE,oBAAoB,GAAG;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAEF,KAAE;AAAC,UAAK,EAAC,SAAQG,IAAE,eAAcC,IAAE,oBAAmBC,GAAC,IAAEH;AAAE,SAAK,gBAAc,KAAK,YAAYC,EAAC,GAAEE,MAAG,EAAED,EAAC,KAAG,CAAC,KAAK,cAAc,WAASA,GAAE,KAAGD,GAAE,2BAA2B,EAAE,gBAAe,EAAE,SAAQH,IAAE,YAAW,GAAG,GAAEG,GAAE,QAAQ,KAAK,uBAAuB,GAAEA,GAAE,WAAW,KAAK,aAAa,GAAE,KAAK,cAAc,oBAAoB,aAAYH,IAAE,WAAW,GAAG,GAAE,KAAK,cAAc,cAAc,iBAAgB,CAACA,IAAE,QAAOA,IAAE,MAAM,CAAC,GAAE,KAAK,cAAc,aAAa,WAAU,CAAC,GAAE,KAAK,cAAc,cAAc,WAAU,KAAK,MAAM,GAAEG,GAAE,WAAW,EAAE,gBAAe,GAAE,CAAC,GAAEA,GAAE,QAAQ;AAAA,EAAE;AAAA,EAAC,YAAYF,KAAE;AAAC,QAAG,KAAK,aAAa,QAAM;AAAG,UAAMC,KAAEA,GAAED,KAAEC,EAAC;AAAE,QAAG,CAACA,GAAE,QAAM;AAAG,UAAMC,KAAE,IAAI,UAAU,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEG,KAAEC,GAAE,aAAaN,KAAE,EAAE,aAAYE,EAAC,GAAEK,KAAE,IAAIC,GAAER,KAAEC,GAAE,YAAWD,IAAE,EAAC,UAASK,GAAC,CAAC;AAAE,WAAO,KAAK,gBAAcJ,IAAE,KAAK,0BAAwBM,IAAE,KAAK,eAAa,MAAG;AAAA,EAAE;AAAC;;;ACAvvC,IAAME,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAOC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,iBAAe,EAAE,GAAE,KAAK,kBAAgB,EAAC,IAAG,OAAG,SAAQ,MAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAO,KAAK,KAAK,QAAQ,GAAE,KAAK,OAAK;AAAA,EAAK;AAAA,EAAC,SAASC,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,SAAQC,IAAE,eAAcC,IAAE,eAAcC,IAAE,oBAAmBR,GAAC,IAAEG;AAAE,SAAK,kBAAkBA,EAAC;AAAE,UAAMM,KAAEN,GAAE,cAAaO,KAAEP,GAAE,YAAWQ,KAAED,GAAE,oBAAmBE,KAAEN,GAAE,4BAA2BO,KAAEH,GAAE,cAAc,oBAAmBD,EAAC,GAAEK,KAAEJ,GAAE,cAAc,sBAAqBD,EAAC,GAAEM,KAAEL,GAAE,cAAc,sBAAqBD,EAAC,GAAE,IAAE,WAASM,IAAEC,KAAEH,GAAE,CAAC,IAAEC,IAAEG,KAAE,IAAE,OAAO,kBAAiBC,KAAEf,GAAE;AAAa,QAAIgB,IAAEC;AAAE,UAAMC,KAAEJ,KAAET,KAAE,IAAE,GAAEc,KAAEnB,GAAE,cAAY,EAAE,SAAQ,IAAE,KAAK;AAAgB,MAAE,KAAGmB,IAAE,EAAE,UAAQ;AAAE,UAAMC,KAAEX,GAAE,mBAAmBP,IAAEM,IAAE,CAAC;AAAE,QAAGX,MAAG,EAAEQ,EAAC,KAAG,CAACe,GAAE,SAAS,CAAAf,GAAE;AAAA,SAAM;AAAC,UAAGH,GAAE,QAAQ,KAAK,IAAI,GAAEA,GAAE,WAAWkB,EAAC,GAAE,GAAE;AAAC,cAAMtB,MAAEiB,GAAE,sBAAsBH,IAAE,IAAE;AAAE,YAAG,EAAEd,GAAC,GAAE;AAAC,gBAAK,EAAC,IAAGE,IAAE,IAAGC,IAAE,MAAKoB,GAAC,IAAEvB;AAAE,UAAAkB,KAAEf,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEiB,KAAEhB,GAAE,CAAC,IAAED,GAAE,CAAC;AAAE,gBAAMsB,KAAEP,GAAE,YAAYM,EAAC;AAAE,YAAEC,EAAC,MAAIP,GAAE,KAAKb,IAAE,EAAE,QAAOmB,IAAE,CAAC,GAAED,GAAE,aAAa,UAASpB,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEmB,GAAE,cAAc,gBAAeE,EAAC,GAAEF,GAAE,aAAa,aAAY,CAAC;AAAA,QAAE;AAAC,QAAAA,GAAE,aAAa,aAAYT,EAAC;AAAA,MAAC,MAAM,MAAK,OAAO,CAAC,IAAEE,KAAEH,GAAE,CAAC,GAAE,KAAK,OAAO,CAAC,IAAEG,KAAEH,GAAE,CAAC,GAAE,KAAK,OAAO,CAAC,IAAEG,KAAEH,GAAE,CAAC,GAAE,KAAK,OAAO,CAAC,IAAEG,IAAEO,GAAE,cAAc,WAAU,KAAK,MAAM;AAAE,UAAGA,GAAE,aAAa,WAAUb,GAAE,KAAG,CAAC,GAAEY,IAAE;AAAC,cAAMrB,MAAEc,GAAER,KAAE,CAAC;AAAE,QAAAgB,GAAE,cAAc,QAAOtB,GAAC;AAAA,MAAC;AAAC,iBAAUC,OAAKE,IAAE;AAAC,YAAGmB,GAAE,aAAa,iBAAgBrB,IAAE,MAAM,GAAEqB,GAAE,oBAAoB,aAAYrB,IAAE,WAAW,GAAG,GAAE,GAAE;AAAC,gBAAMC,KAAE,KAAK,IAAI,MAAI,KAAK,MAAMM,EAAC,IAAEP,IAAE,IAAI,QAAO,CAAC,GAAEE,KAAEiB,KAAEnB,IAAE,QAAMC,IAAEuB,KAAEtB,KAAE,EAAEe,EAAC,GAAEK,KAAEpB,KAAE,EAAEgB,EAAC;AAAE,eAAK,eAAe,CAAC,IAAEM,IAAE,KAAK,eAAe,CAAC,IAAEF,IAAED,GAAE,oBAAoB,oBAAmB,KAAK,cAAc;AAAA,QAAC;AAAC,QAAAlB,GAAE,mBAAmB,EAAE,OAAM,GAAE,GAAG,GAAEA,GAAE,WAAW,EAAE,gBAAe,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBJ,KAAE;AAAC,QAAG,KAAK,KAAK;AAAO,UAAK,EAAC,SAAQC,KAAE,YAAWC,GAAC,IAAEF,KAAEG,KAAED,GAAE,oBAAmBuB,KAAE,IAAI,UAAU,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEF,KAAEG,GAAE,aAAazB,KAAE,EAAE,aAAYwB,EAAC,GAAED,KAAE,IAAIG,GAAE1B,KAAEE,GAAE,sBAAsB,GAAEA,GAAE,cAAc,GAAE,EAAC,UAASoB,GAAC,CAAC;AAAE,SAAK,OAAKC;AAAA,EAAC;AAAC;;;ACApuE,IAAMI,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,kBAAgB,EAAC,IAAG,MAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,SAASC,IAAEF,IAAE;AAAC,UAAK,EAAC,SAAQG,IAAE,cAAaC,IAAE,eAAcC,IAAE,OAAMC,IAAE,WAAUC,IAAE,SAAQC,IAAE,cAAaC,IAAE,eAAcC,IAAE,eAAcC,IAAE,oBAAmBC,GAAC,IAAEV;AAAE,QAAG,CAACF,GAAE,KAAM,CAAAa,OAAC;AAJzmB;AAI2mB,oBAAAA,GAAE,UAAU,IAAIH,EAAC,MAAjB,mBAAoB,qBAAkB;AAAA,KAAG,EAAE;AAAO,UAAMI,KAAEZ,GAAE,YAAWa,KAAED,GAAE,gBAAeE,KAAER,GAAE,4BAA2BS,KAAE,KAAIC,KAAEJ,GAAE,cAAc,oBAAmBV,EAAC,GAAEe,KAAEL,GAAE,cAAc,2BAA0BV,EAAC,GAAEgB,KAAEb,OAAI,EAAE,SAAQc,KAAE,KAAK;AAAgB,IAAAA,GAAE,KAAGD;AAAE,UAAME,KAAEN,GAAE,mBAAmBb,IAAEY,IAAEM,EAAC;AAAE,QAAGT,MAAG,EAAED,EAAC,KAAG,CAACW,GAAE,SAAS,QAAO,KAAKX,GAAE;AAAE,IAAAR,GAAE,WAAWmB,EAAC,GAAEA,GAAE,oBAAoB,iBAAgBH,OAAII,GAAE,WAASjB,GAAE,cAAYA,GAAE,eAAe,GAAEgB,GAAE,cAAc,uBAAsBJ,EAAC,GAAEI,GAAE,aAAa,WAAUR,GAAE,CAAC,GAAEQ,GAAE,aAAa,uBAAsBL,EAAC;AAAE,QAAIO,KAAE;AAAG,QAAGJ,IAAE;AAAC,YAAMP,KAAED,GAAEF,KAAE,CAAC;AAAE,MAAAY,GAAE,cAAc,QAAOT,EAAC;AAAA,IAAC;AAAC,eAAUA,MAAKb,IAAE;AAAC,UAAG,CAACa,GAAE,UAAU,IAAIH,EAAC,EAAE;AAAS,MAAAG,GAAE,IAAI,UAAQW,OAAIA,KAAEX,GAAE,IAAI,OAAME,GAAE,gBAAgBO,IAAElB,IAAEU,IAAEU,IAAEf,EAAC;AAAG,YAAMc,MAAEV,GAAE,UAAU,IAAIH,EAAC;AAAE,UAAG,CAACa,IAAE,iBAAiB;AAAS,MAAAA,IAAE,oBAAoBpB,EAAC;AAAE,YAAMsB,KAAEF,IAAE;AAAwB,QAAEE,EAAC,MAAItB,GAAE,QAAQsB,EAAC,GAAEH,GAAE,oBAAoB,aAAYT,GAAE,WAAW,GAAG,GAAER,OAAIQ,GAAE,IAAI,QAAMV,GAAE,mBAAmB,EAAE,OAAMU,GAAE,YAAW,GAAG,IAAEV,GAAE,mBAAmB,EAAE,SAAQ,KAAI,GAAG,GAAEA,GAAE,aAAa,EAAE,WAAUoB,IAAE,kBAAiB,EAAE,cAAa,YAAY,oBAAkBA,IAAE,gBAAgB,GAAEV,GAAE,iBAAeU,IAAE,mBAAiB;AAAA,IAAE;AAAA,EAAC;AAAC;;;ACA/xC,IAAMG,KAAE,IAAE;AAAM,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,sBAAoB,EAAC,IAAG,OAAG,SAAQ,MAAE,GAAE,KAAK,yBAAuB,EAAC,IAAG,MAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,SAASA,KAAEC,IAAE;AAAC,UAAK,EAAC,cAAaC,IAAE,WAAUC,KAAE,YAAWC,IAAE,cAAaC,IAAE,eAAcC,GAAC,IAAEN;AAAE,QAAIO,KAAE;AAAG,eAAUC,MAAKP,GAAE,KAAGO,GAAE,UAAU,IAAIF,EAAC,GAAE;AAAC,YAAMN,MAAEQ,GAAE,UAAU,IAAIF,EAAC;AAAE,UAAGN,IAAE,iBAAe,KAAGA,IAAE,oBAAkB,GAAE;AAAC,QAAAO,KAAE;AAAG;AAAA,MAAK;AAAA,IAAC;AAAC,QAAG,CAACA,GAAE;AAAO,UAAME,KAAET,IAAE,YAAWU,KAAED,GAAE,iBAAiB,cAAc,GAAEX,KAAE,WAASY,IAAEX,KAAED,MAAGY,GAAE;AAAa,QAAIC;AAAE,QAAGb,MAAG,CAACC,IAAE;AAAC,YAAMC,MAAEU,GAAE,SAASR,EAAC;AAAE,MAAAS,KAAEN,GAAE,sBAAsBL,KAAE,IAAE;AAAA,IAAC;AAAC,UAAMY,KAAE,CAACd,MAAGW,GAAE,cAAc,kBAAiBP,EAAC;AAAE,QAAIW,KAAE,MAAGC,KAAE;AAAE,QAAG,CAAChB,IAAE;AAAC,YAAME,MAAES,GAAE,iBAAiB,YAAY,GAAER,KAAEQ,GAAE,iBAAiB,cAAc;AAAE,UAAG,EAACT,OAAA,gBAAAA,IAAG,iBAAc,EAACC,MAAA,gBAAAA,GAAG,eAAa;AAAC,cAAMD,MAAES,GAAE,cAAc,cAAaP,EAAC;AAAE,QAAAY,KAAEL,GAAE,cAAc,gBAAeP,EAAC,IAAEF,IAAE,CAAC,GAAEc,MAAG,MAAID,KAAE;AAAA,MAAG;AAAA,IAAC;AAAC,QAAGA,MAAG,aAAWT,GAAE;AAAO,QAAIW;AAAE,IAAAZ,QAAI,EAAE,YAAUY,KAAEC,GAAEV,KAAE,CAAC;AAAG,UAAMW,KAAER,GAAE,cAAc,kBAAiBP,EAAC,GAAEc,KAAEP,GAAE,cAAc,yBAAwBP,EAAC;AAAE,KAACW,MAAG,kBAAgBT,OAAI,KAAK,UAAUJ,KAAEM,IAAEG,IAAER,IAAEgB,IAAED,IAAElB,IAAEa,IAAEZ,IAAEgB,EAAC;AAAE,UAAMG,KAAE,CAACT,GAAE,6BAA2BA,GAAE,wBAAsBK,KAAE;AAAE,IAAAF,MAAG,aAAWR,MAAG,CAACc,MAAG,KAAK,aAAalB,KAAEM,IAAEG,IAAER,IAAEgB,IAAED,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUI,IAAEd,IAAEN,IAAEY,IAAEC,IAAEC,IAAEC,IAAEC,IAAEE,IAAED,IAAE;AAAC,QAAGF,MAAG,CAACG,MAAG,EAAEF,EAAC,EAAE;AAAO,UAAK,EAAC,SAAQG,IAAE,cAAaE,IAAE,OAAMC,IAAE,WAAUC,IAAE,SAAQC,IAAE,YAAWC,IAAE,cAAaC,IAAE,eAAcC,IAAE,oBAAmBC,GAAC,IAAER,IAAES,KAAE7B,GAAE,cAAa8B,KAAEN,GAAE,4BAA2BO,KAAEN,KAAEV,KAAE,IAAE,GAAEiB,KAAET,OAAI,EAAE,SAAQU,KAAE,KAAK;AAAoB,IAAAA,GAAE,KAAGD,IAAEC,GAAE,UAAQlB;AAAE,UAAMmB,KAAEJ,GAAE,mBAAmBX,IAAEU,IAAEI,EAAC;AAAE,QAAGL,MAAG,EAAED,EAAC,KAAG,CAACO,GAAE,SAAS,QAAO,KAAKP,GAAE;AAAE,QAAGR,GAAE,WAAWe,EAAC,GAAE,EAAElB,EAAC,GAAE;AAAC,YAAK,EAAC,MAAKf,IAAC,IAAEe,IAAEb,KAAEuB,GAAE,YAAYzB,GAAC;AAAE,QAAEE,EAAC,MAAIuB,GAAE,KAAKP,IAAE,EAAE,QAAOlB,KAAE,CAAC,GAAEiC,GAAE,cAAc,gBAAe/B,EAAC,GAAE+B,GAAE,aAAa,aAAY,CAAC;AAAA,IAAE;AAAC,IAAAA,GAAE,oBAAoB,iBAAgBpB,OAAIV,GAAE,WAASkB,GAAE,cAAYA,GAAE,eAAe,GAAEY,GAAE,cAAc,qBAAoBrB,EAAC,GAAEqB,GAAE,aAAa,WAAUlC,GAAE,IAAED,EAAC,GAAEiC,MAAGE,GAAE,cAAc,QAAOjB,EAAC;AAAE,QAAIkB,KAAE;AAAG,eAAUhC,MAAKS,IAAE;AAAC,UAAG,CAACT,GAAE,UAAU,IAAIG,EAAC,EAAE;AAAS,MAAAH,GAAE,IAAI,UAAQgC,OAAIA,KAAEhC,GAAE,IAAI,OAAM0B,GAAE,gBAAgBK,IAAEb,IAAErB,IAAEmC,IAAET,EAAC;AAAG,YAAMrB,KAAEF,GAAE,UAAU,IAAIG,EAAC;AAAE,UAAG,CAACD,GAAE,eAAe;AAAS,MAAAA,GAAE,oBAAoBc,EAAC;AAAE,YAAMV,KAAEJ,GAAE;AAAsB,UAAG,CAAC,EAAEI,EAAC,GAAE;AAAC,YAAGU,GAAE,QAAQV,EAAC,GAAEyB,GAAE,oBAAoB,aAAY/B,GAAE,WAAW,GAAG,GAAEgB,GAAE,mBAAmB,EAAE,OAAMhB,GAAE,YAAW,GAAG,GAAEY,IAAE;AAAC,gBAAMd,MAAE,KAAK,IAAI,MAAI,KAAK,MAAMoB,EAAC,IAAElB,GAAE,IAAI,QAAO,CAAC,GAAED,KAAEC,GAAE,UAAQ4B,KAAE5B,GAAE,QAAMF;AAAG,UAAAiC,GAAE,aAAa,mBAAkBhC,EAAC;AAAA,QAAC;AAAC,YAAGgB,IAAE;AAAC,gBAAMjB,MAAEI,GAAE;AAAW,cAAG,CAACJ,IAAE;AAAS,qBAAS,CAACE,IAAEE,EAAC,KAAIJ,KAAE;AAAC,kBAAMA,MAAEyB,GAAE,YAAYvB,EAAC;AAAE,cAAEF,GAAC,MAAIyB,GAAE,KAAKP,IAAE,EAAE,QAAOhB,IAAE,CAAC,GAAE+B,GAAE,cAAc,gBAAejC,GAAC,GAAEiC,GAAE,aAAa,aAAY,CAAC,GAAEf,GAAE,aAAa,EAAE,WAAUd,GAAE,CAAC,GAAE,EAAE,cAAa,YAAY,oBAAkBA,GAAE,CAAC,CAAC;AAAA,UAAE;AAAA,QAAC,MAAM,CAAAc,GAAE,aAAa,EAAE,WAAUd,GAAE,gBAAe,EAAE,cAAa,YAAY,oBAAkBA,GAAE,cAAc;AAAE,QAAAF,GAAE,iBAAeE,GAAE,iBAAe;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,KAAEC,IAAEe,IAAEd,IAAEC,IAAEP,IAAEY,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,cAAaC,IAAE,OAAMC,IAAE,WAAUC,IAAE,SAAQE,IAAE,YAAWD,IAAE,cAAaE,IAAE,eAAcE,IAAE,oBAAmBC,GAAC,IAAElB,KAAEmB,KAAEH,GAAE,iBAAgBI,KAAEN,GAAE,4BAA2BO,KAAE,OAAIR,IAAES,KAAEV,OAAI,EAAE,SAAQW,KAAE,KAAK;AAAuB,IAAAA,GAAE,KAAGD;AAAE,UAAME,KAAEJ,GAAE,mBAAmBX,IAAEU,IAAEI,EAAC;AAAE,QAAGL,MAAG,EAAED,EAAC,KAAG,CAACO,GAAE,SAAS,QAAO,KAAKP,GAAE;AAAE,IAAAR,GAAE,WAAWe,EAAC,GAAEA,GAAE,oBAAoB,iBAAgB5B,OAAII,GAAE,WAASW,GAAE,cAAYA,GAAE,eAAe,GAAEa,GAAE,cAAc,qBAAoBrB,EAAC,GAAEqB,GAAE,aAAa,WAAUR,GAAE,IAAErB,EAAC,GAAE6B,GAAE,aAAa,mBAAkBH,EAAC,GAAEC,MAAGE,GAAE,cAAc,QAAOhB,EAAC;AAAE,QAAIiB,KAAE;AAAG,eAAU3B,MAAKI,IAAE;AAAC,UAAG,CAACJ,GAAE,UAAU,IAAIG,EAAC,EAAE;AAAS,MAAAH,GAAE,IAAI,UAAQ2B,OAAIA,KAAE3B,GAAE,IAAI,OAAMqB,GAAE,gBAAgBK,IAAEd,IAAEM,IAAES,IAAEV,EAAC;AAAG,YAAMhB,KAAED,GAAE,UAAU,IAAIG,EAAC;AAAE,UAAGF,GAAE,oBAAoBU,EAAC,GAAE,CAACV,GAAE,kBAAkB;AAAS,YAAMC,MAAED,GAAE;AAAyB,QAAEC,GAAC,MAAIS,GAAE,QAAQT,GAAC,GAAEwB,GAAE,oBAAoB,aAAY1B,GAAE,WAAW,GAAG,GAAEW,GAAE,mBAAmB,EAAE,OAAMX,GAAE,YAAW,GAAG,GAAEW,GAAE,aAAa,EAAE,WAAUV,GAAE,mBAAkB,EAAE,cAAa,YAAY,oBAAkBA,GAAE,iBAAiB,GAAED,GAAE,iBAAeC,GAAE,oBAAkB;AAAA,IAAE;AAAA,EAAC;AAAC;;;ACA5zH,IAAMiC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,kBAAgB,EAAC,IAAG,OAAG,SAAQ,OAAG,KAAI,MAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,SAASC,IAAEF,IAAE;AAAC,UAAK,EAAC,SAAQG,IAAE,cAAaC,IAAE,OAAMC,IAAE,WAAUC,IAAE,SAAQC,IAAE,YAAWC,IAAE,cAAaC,IAAE,eAAcC,IAAE,eAAcC,IAAE,oBAAmBC,GAAC,IAAEV;AAAE,QAAG,CAACF,GAAE,KAAM,CAAAa,OAAC;AAJttB;AAIwtB,oBAAAA,GAAE,UAAU,IAAIH,EAAC,MAAjB,mBAAoB,mBAAgB;AAAA,KAAG,EAAE;AAAO,UAAMI,KAAEZ,GAAE,YAAWa,KAAED,GAAE,cAAaE,KAAET,GAAE,4BAA2BU,KAAEH,GAAE,cAAc,kBAAiBV,EAAC,GAAEc,KAAEJ,GAAE,cAAc,yBAAwBV,EAAC,GAAEe,KAAEL,GAAE,iBAAiB,cAAc,GAAEM,KAAE,WAASD,IAAEE,KAAED,MAAGD,GAAE;AAAa,QAAIG,IAAEC;AAAE,QAAGH,MAAG,CAACC,IAAE;AAAC,YAAMR,KAAEM,GAAE,SAASf,EAAC;AAAE,MAAAkB,KAAEb,GAAE,sBAAsBI,EAAC;AAAA,IAAC;AAAC,QAAIW,KAAE;AAAG,QAAG,CAACJ,IAAE;AAAC,YAAMP,KAAEC,GAAE,iBAAiB,gBAAgB;AAAE,UAAGS,KAAE,WAASV,IAAEW,KAAED,MAAGV,GAAE,cAAaU,MAAG,CAACC,IAAE;AAAC,cAAMvB,MAAEY,GAAE,SAAST,EAAC,GAAEqB,KAAEX,GAAE,WAAWb,KAAEa,GAAE,eAAe,YAAWV,EAAC,CAAC;AAAE,QAAAkB,KAAEb,GAAE,sBAAsBgB,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,UAAMC,KAAE,IAAElB,IAAEmB,KAAErB,OAAI,EAAE,SAAQ,IAAE,KAAK;AAAgB,MAAE,KAAGqB,IAAE,EAAE,UAAQP,IAAE,EAAE,MAAIG;AAAE,UAAMK,KAAEZ,GAAE,mBAAmBb,IAAEY,IAAE,CAAC;AAAE,QAAGH,MAAG,EAAED,EAAC,KAAG,CAACiB,GAAE,SAAS,QAAO,KAAKjB,GAAE;AAAE,QAAGR,GAAE,WAAWyB,EAAC,GAAEA,GAAE,oBAAoB,qBAAoBvB,GAAE,eAAe,GAAEuB,GAAE,oBAAoB,iBAAgBV,OAAIW,GAAE,WAASxB,GAAE,cAAYA,GAAE,eAAe,GAAEuB,GAAE,cAAc,qBAAoBX,EAAC,GAAEW,GAAE,aAAa,WAAUd,GAAE,CAAC,GAAEc,GAAE,aAAa,kBAAiBF,EAAC,GAAEC,IAAE;AAAC,YAAMd,KAAEJ,GAAEC,KAAE,CAAC;AAAE,MAAAkB,GAAE,cAAc,QAAOf,EAAC;AAAA,IAAC;AAAC,QAAGS,MAAG,EAAEA,EAAC,GAAE;AAAC,YAAK,EAAC,MAAKrB,IAAC,IAAEqB,IAAEG,KAAEhB,GAAE,YAAYR,GAAC;AAAE,QAAEwB,EAAC,MAAIhB,GAAE,KAAKN,IAAE,EAAE,QAAOF,KAAE,CAAC,GAAE2B,GAAE,cAAc,gBAAeH,EAAC,GAAEG,GAAE,aAAa,aAAY,CAAC;AAAA,IAAE;AAAC,QAAI,IAAE;AAAG,eAAUH,MAAKzB,IAAE;AAAC,UAAG,CAACyB,GAAE,UAAU,IAAIf,EAAC,EAAE;AAAS,MAAAe,GAAE,IAAI,UAAQ,MAAI,IAAEA,GAAE,IAAI,OAAMV,GAAE,gBAAgBa,IAAExB,IAAEU,IAAE,GAAEL,EAAC;AAAG,YAAMoB,MAAE,MAAIzB,KAAE,KAAGI;AAAE,MAAAoB,GAAE,aAAa,gBAAeC,GAAC;AAAE,YAAMC,KAAEL,GAAE,UAAU,IAAIf,EAAC;AAAE,UAAG,CAACoB,GAAE,eAAe;AAAS,MAAAA,GAAE,oBAAoB3B,EAAC;AAAE,YAAMD,KAAE4B,GAAE;AAAsB,UAAG,CAAC,EAAE5B,EAAC,GAAE;AAAC,YAAGC,GAAE,QAAQD,EAAC,GAAE0B,GAAE,oBAAoB,aAAYH,GAAE,WAAW,GAAG,GAAEtB,GAAE,mBAAmB,EAAE,OAAMsB,GAAE,YAAW,GAAG,GAAEJ,MAAGG,IAAE;AAAC,gBAAMvB,MAAE6B,GAAE;AAAW,cAAG,CAAC7B,IAAE;AAAS,qBAAS,CAACwB,IAAEI,GAAC,KAAI5B,KAAE;AAAC,kBAAMA,MAAEQ,GAAE,YAAYgB,EAAC;AAAE,cAAExB,GAAC,MAAIQ,GAAE,KAAKN,IAAE,EAAE,QAAOsB,IAAE,CAAC,GAAEG,GAAE,cAAc,gBAAe3B,GAAC,GAAE2B,GAAE,aAAa,aAAY,CAAC,GAAEzB,GAAE,aAAa,EAAE,WAAU0B,IAAE,CAAC,GAAE,EAAE,cAAa,YAAY,oBAAkBA,IAAE,CAAC,CAAC;AAAA,UAAE;AAAA,QAAC,MAAM,CAAA1B,GAAE,aAAa,EAAE,WAAU2B,GAAE,gBAAe,EAAE,cAAa,YAAY,oBAAkBA,GAAE,cAAc;AAAE,QAAAL,GAAE,iBAAeK,GAAE,iBAAe;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACAp9D,IAAMC,KAAE,IAAE;AAAM,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,sBAAoB,EAAC,IAAG,OAAG,KAAI,MAAE,GAAE,KAAK,qBAAmB,EAAC,IAAG,MAAE,GAAE,KAAK,sBAAoB,EAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,SAASC,IAAED,KAAE;AAAC,UAAK,EAAC,WAAUE,IAAE,eAAcC,GAAC,IAAEF,IAAEG,MAAEH,GAAE;AAAW,QAAII;AAAE,IAAAH,OAAI,EAAE,YAAUG,KAAEN,GAAEI,KAAE,CAAC,IAAG,KAAK,WAAWF,IAAEG,KAAEJ,KAAEK,EAAC,GAAE,KAAK,UAAUJ,IAAEG,KAAEJ,KAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWH,IAAEC,IAAEG,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,cAAaC,IAAE,WAAUC,IAAE,SAAQC,IAAE,cAAaC,IAAE,OAAMd,IAAE,eAAcC,IAAE,eAAcc,IAAE,oBAAmBC,GAAC,IAAEZ,IAAEa,KAAEZ,GAAE,cAAaa,KAAEL,GAAE;AAA2B,QAAIM,IAAEC,KAAE;AAAG,eAAUjB,MAAKK,GAAE,KAAGL,GAAE,UAAU,IAAIF,EAAC,MAAIkB,KAAEhB,GAAE,UAAU,IAAIF,EAAC,GAAEkB,GAAE,uBAAuB,OAAK,IAAG;AAAC,MAAAC,KAAE;AAAG;AAAA,IAAK;AAAC,QAAG,CAACA,GAAE;AAAO,UAAMC,KAAEhB,GAAE,cAAc,kBAAiBM,EAAC,GAAEW,KAAEjB,GAAE,cAAc,yBAAwBM,EAAC;AAAE,QAAIY,KAAElB,GAAE,eAAe,2BAA0BM,EAAC;AAAE,IAAAY,OAAIC,GAAE,SAAOD,KAAElB,GAAE,eAAe,oBAAmBM,EAAC,MAAIJ,GAAE,QAAMiB,GAAE,WAASA,GAAE;AAAK,UAAMC,KAAEF,OAAIC,GAAE,KAAIE,KAAErB,GAAE,eAAe,qBAAoBM,EAAC,KAAGc,IAAEE,KAAER,GAAE,WAAUS,KAAEhB,OAAI,EAAE,SAAQiB,KAAE,KAAK;AAAoB,IAAAA,GAAE,KAAGD,IAAEC,GAAE,MAAIF;AAAE,UAAMG,KAAEZ,GAAE,mBAAmBR,IAAEO,IAAEY,EAAC;AAAE,QAAGb,MAAG,EAAED,EAAC,KAAG,CAACe,GAAE,SAAS,QAAO,KAAKf,GAAE;AAAE,IAAAL,GAAE,WAAWoB,EAAC,GAAEA,GAAE,oBAAoB,qBAAoBP,OAAIC,GAAE,MAAIxB,GAAE,kBAAgBA,GAAE,WAAW,GAAE8B,GAAE,oBAAoB,iBAAgBR,OAAIhB,GAAE,WAASN,GAAE,cAAYA,GAAE,eAAe,GAAE8B,GAAE,cAAc,qBAAoBT,EAAC,GAAES,GAAE,aAAa,WAAUzB,GAAE,CAAC,GAAEyB,GAAE,aAAa,iBAAgBC,GAAE/B,GAAE,QAAQ,CAAC,GAAE8B,GAAE,aAAa,iBAAgBJ,KAAE,IAAE,CAAC,GAAEI,GAAE,aAAa,WAAU,KAAGnB,EAAC,GAAEmB,GAAE,aAAa,aAAY,CAAC,GAAEA,GAAE,aAAa,kBAAiB3B,KAAE,GAAG,GAAEyB,MAAGE,GAAE,cAAc,QAAOrB,EAAC;AAAE,QAAIuB,KAAE;AAAG,eAAU7B,MAAKK,IAAE;AAAC,UAAG,CAACL,GAAE,UAAU,IAAIF,EAAC,EAAE;AAAS,UAAGE,GAAE,IAAI,UAAQ6B,OAAIA,KAAE7B,GAAE,IAAI,OAAMc,GAAE,gBAAgBa,IAAEnB,IAAEN,IAAE2B,IAAElB,EAAC,IAAGK,KAAEhB,GAAE,UAAU,IAAIF,EAAC,GAAE,MAAIkB,GAAE,uBAAuB,KAAK;AAAS,MAAAA,GAAE,oBAAoBT,EAAC,GAAES,GAAE,kBAAkB;AAAE,YAAMb,MAAEa,GAAE;AAAsB,UAAG,CAAC,EAAEb,GAAC,GAAE;AAAC,QAAAI,GAAE,QAAQJ,GAAC,GAAEwB,GAAE,oBAAoB,aAAY3B,GAAE,WAAW,GAAG,GAAE2B,GAAE,aAAa,WAAU,YAAY,IAAI,IAAEX,GAAE,qBAAmB,GAAG;AAAE,mBAAS,CAACjB,KAAEG,EAAC,KAAIc,GAAE,uBAAuB,MAAK,iBAAiBf,IAAE0B,IAAEzB,IAAEH,KAAEC,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAED,KAAEE,IAAEC,IAAEC,KAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,cAAa0B,GAAC,IAAE9B;AAAE,SAAK,oBAAoB,CAAC,IAAE8B,GAAE,SAAS5B,EAAC,IAAE,GAAE,KAAK,oBAAoB,CAAC,IAAE4B,GAAE,UAAU5B,EAAC,IAAE,GAAEH,IAAE,cAAc,gBAAe,KAAK,mBAAmB,GAAE+B,GAAE,KAAK1B,IAAE,EAAE,QAAOF,IAAE,CAAC,GAAEE,GAAE,sBAAsB,IAAE,GAAEA,GAAE,mBAAmB,EAAE,SAAQ,KAAI,GAAG,GAAEA,GAAE,oBAAoB,CAAC,GAAEA,GAAE,aAAa,EAAE,WAAUH,GAAE,CAAC,GAAE,EAAE,cAAa,YAAY,oBAAkBA,GAAE,CAAC,CAAC,GAAEE,IAAE,iBAAeF,GAAE,CAAC,IAAE;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAEoB,IAAEf,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,cAAaE,IAAE,WAAUC,IAAE,aAAYb,IAAE,SAAQc,IAAE,YAAWC,IAAE,cAAaC,IAAE,OAAMC,IAAE,eAAcC,IAAE,eAAcC,IAAE,oBAAmBC,GAAC,IAAEjB,IAAEkB,KAAEE,GAAE,cAAaD,KAAER,GAAE;AAA2B,QAAIU,IAAEC,KAAE;AAAG,eAAUvB,MAAKM,GAAE,KAAGN,GAAE,UAAU,IAAIgB,EAAC,MAAIM,KAAEtB,GAAE,UAAU,IAAIgB,EAAC,GAAEM,GAAE,wBAAwB,OAAK,IAAG;AAAC,MAAAC,KAAE;AAAG;AAAA,IAAK;AAAC,QAAG,CAACA,GAAE;AAAO,UAAMC,KAAEH,GAAE,iBAAiB,cAAc;AAAE,QAAGG,MAAG,CAACA,GAAE,gBAAc,MAAIA,GAAE,SAASd,EAAC,EAAE;AAAO,UAAMe,KAAEJ,GAAE,iBAAiB,YAAY,GAAEK,KAAE,CAACD,MAAGA,GAAE,gBAAcA,GAAE,SAASf,EAAC,EAAE,CAAC,IAAE,GAAEiB,KAAEN,GAAE,iBAAiB,iBAAiB,GAAEQ,KAAER,GAAE,iBAAiB,iBAAiB,GAAEU,MAAG,CAACJ,MAAGA,GAAE,gBAAcA,GAAE,SAASjB,EAAC,IAAE,OAAK,CAACmB,MAAGA,GAAE,gBAAcA,GAAE,SAASnB,EAAC,EAAE,CAAC,IAAE;AAAG,QAAG,CAACgB,MAAG,CAACK,GAAE;AAAO,UAAM,IAAE,KAAG;AAAE,QAAI,IAAEV,GAAE,eAAe,2BAA0BX,EAAC;AAAE,UAAIW,GAAE,SAAO,IAAEA,GAAE,eAAe,oBAAmBX,EAAC,MAAIN,GAAE,QAAMiB,GAAE,WAASA,GAAE;AAAK,UAAM,IAAE,MAAIA,GAAE,KAAIW,KAAEX,GAAE,eAAe,qBAAoBX,EAAC,KAAG,GAAEuB,KAAEtB,OAAI,EAAE,SAAQuB,KAAE,MAAG,IAAErB;AAAE,SAAK,sBAAoB,KAAK,oBAAkBd,GAAED,GAAE,QAAM,GAAEA,GAAE,SAAO,CAAC;AAAG,UAAMqC,KAAEd,GAAE,cAAc,kBAAiBX,EAAC,GAAE,IAAEW,GAAE,cAAc,yBAAwBX,EAAC,GAAE0B,KAAE,KAAK;AAAmB,IAAAA,GAAE,KAAGH;AAAE,UAAMI,KAAEjB,GAAE,mBAAmBZ,IAAEW,IAAEiB,EAAC;AAAE,QAAGlB,MAAG,EAAED,EAAC,KAAG,CAACoB,GAAE,SAAS,QAAO,KAAKpB,GAAE;AAAE,IAAAT,GAAE,WAAW6B,EAAC,GAAEA,GAAE,oBAAoB,qBAAoB,MAAIhB,GAAE,MAAIN,GAAE,kBAAgBA,GAAE,WAAW,GAAEsB,GAAE,oBAAoB,iBAAgB,MAAIlC,GAAE,WAASY,GAAE,cAAYA,GAAE,eAAe,GAAEsB,GAAE,cAAc,qBAAoBF,EAAC,GAAEE,GAAE,aAAa,WAAUhB,GAAE,IAAExB,EAAC,GAAEwC,GAAE,cAAc,gBAAe,KAAK,iBAAiB,GAAEA,GAAE,aAAa,iBAAgBT,GAAEb,GAAE,QAAQ,CAAC,GAAEsB,GAAE,aAAa,iBAAgBL,KAAE,IAAE,CAAC,GAAEK,GAAE,aAAa,WAAU,KAAG3B,EAAC,GAAE2B,GAAE,aAAa,aAAY,CAAC,GAAEA,GAAE,aAAa,uBAAsBH,EAAC,GAAEG,GAAE,aAAa,kBAAiBrC,KAAE,GAAG,GAAEiC,MAAGI,GAAE,cAAc,QAAO9B,EAAC;AAAE,QAAI,IAAE;AAAG,eAAUP,MAAKM,IAAE;AAAC,UAAG,CAACN,GAAE,UAAU,IAAIgB,EAAC,EAAE;AAAS,UAAGhB,GAAE,IAAI,UAAQ,MAAI,IAAEA,GAAE,IAAI,OAAMmB,GAAE,gBAAgBkB,IAAE3B,IAAEW,IAAE,GAAEP,EAAC,IAAGQ,KAAEtB,GAAE,UAAU,IAAIgB,EAAC,GAAE,MAAIM,GAAE,wBAAwB,KAAK;AAAS,MAAAA,GAAE,oBAAoBd,EAAC,GAAEc,GAAE,kBAAkB;AAAE,YAAMrB,KAAEqB,GAAE;AAAsB,UAAG,EAAErB,EAAC,EAAE;AAAS,MAAAO,GAAE,QAAQP,EAAC,GAAEoC,GAAE,oBAAoB,aAAYrC,GAAE,WAAW,GAAG,GAAEQ,GAAE,sBAAsB,IAAE,GAAEA,GAAE,mBAAmB,EAAE,SAAQ,KAAI,GAAG,GAAEA,GAAE,oBAAoB,CAAC;AAAE,YAAMN,MAAG,YAAY,IAAI,IAAEoB,GAAE,qBAAmB;AAAI,MAAAe,GAAE,aAAa,UAASnC,EAAC,GAAEoB,GAAE,wBAAwB,QAAS,CAACvB,KAAEE,OAAI;AAAC,aAAK,kBAAkBO,IAAET,KAAEE,IAAEH,IAAEuC,IAAEN,IAAEL,IAAE1B,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAED,KAAEE,IAAEC,IAAEC,KAAEC,IAAE0B,IAAEQ,IAAE;AAAC,IAAApC,GAAE,KAAKF,IAAE,EAAE,QAAOC,IAAE,CAAC,GAAEG,OAAID,IAAE,aAAa,UAAS,CAAC,GAAEH,GAAE,aAAa,EAAE,WAAUD,IAAE,CAAC,GAAE,EAAE,cAAa,YAAY,oBAAkBA,IAAE,CAAC,CAAC,GAAEuC,GAAE,iBAAevC,IAAE,CAAC,IAAE,IAAG+B,OAAI3B,IAAE,aAAa,UAAS,CAAC,GAAEH,GAAE,aAAa,EAAE,WAAUD,IAAE,CAAC,GAAE,EAAE,cAAa,YAAY,oBAAkBA,IAAE,CAAC,CAAC,GAAEuC,GAAE,iBAAevC,IAAE,CAAC,IAAE;AAAA,EAAE;AAAC;;;ACA37K,IAAMwC,KAAE,CAAAC,OAAG,GAAEA,GAAE,MAAK,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,QAAO,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,WAAU,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,gBAAe,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,iCAAgC,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,kBAAiB,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,CAAC,EAAC,CAAC;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAOC,GAAE;AAAA,EAAK;AAAA,EAAC,kBAAkBH,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,aAAaI,KAAEC,IAAEC,IAAEL,IAAE;AAAC,UAAK,EAAC,SAAQM,IAAE,SAAQC,IAAE,OAAMC,IAAE,cAAaC,IAAE,eAAcC,IAAE,oBAAmBT,GAAC,IAAEE,KAAEQ,KAAEC,GAAE,KAAKP,GAAE,WAAW,GAAEH,KAAES,GAAE,aAAW,IAAE;AAAE,QAAG,CAACT,MAAG,KAAK,IAAIE,GAAE,IAAI,QAAM,KAAK,MAAM,MAAID,IAAE,YAAY,IAAE,GAAG,KAAG,EAAE;AAAO,UAAK,EAAC,eAAcU,IAAE,YAAWC,GAAC,IAAEhB,GAAEa,EAAC,GAAEI,KAAER,GAAE,gBAAgB,mBAAmBJ,KAAEQ,IAAE,mBAAkBG,IAAEd,EAAC;AAAE,QAAGC,MAAG,EAAES,EAAC,KAAG,CAACK,GAAE,SAAS,QAAO,KAAKL,GAAE;AAAE,IAAAP,IAAE,QAAQ,mBAAmB,EAAE,OAAM,GAAE,GAAG,GAAEG,GAAE,WAAWS,EAAC,GAAE,KAAK,mBAAmBA,IAAEZ,KAAEC,EAAC,GAAEG,GAAE,eAAe,aAAaD,IAAES,IAAEJ,EAAC;AAAE,UAAMK,KAAE,MAAId,KAAEM,GAAE,kBAAgBA,GAAE;AAAY,SAAK,mBAAmBG,IAAEI,IAAEN,IAAEL,EAAC,GAAEW,GAAE,aAAa,iBAAgB,KAAK,MAAMP,GAAE,WAAS,MAAI,GAAG,CAAC,GAAEO,GAAE,aAAa,gBAAeb,EAAC,GAAEa,GAAE,oBAAoB,iBAAgBC,EAAC,GAAED,GAAE,aAAa,aAAY,CAAC,GAAEA,GAAE,cAAc,gBAAeZ,IAAE,MAAM,IAAI;AAAE,UAAMc,KAAEZ,GAAE,OAAO,OAAOC,IAAEO,IAAEC,EAAC,GAAEI,KAAEb,GAAE,YAAU,YAAY;AAAkB,IAAAC,GAAE,QAAQW,EAAC,GAAEF,GAAE,aAAa,gBAAe,CAAC,GAAEA,GAAE,aAAa,sBAAqB,CAAC,GAAET,GAAE,aAAa,EAAE,WAAUD,GAAE,YAAW,EAAE,cAAaa,EAAC,GAAEH,GAAE,aAAa,gBAAe,CAAC,GAAEA,GAAE,aAAa,sBAAqB,CAAC,GAAET,GAAE,aAAa,EAAE,WAAUD,GAAE,YAAW,EAAE,cAAaa,EAAC,GAAEH,GAAE,aAAa,gBAAe,CAAC,GAAEA,GAAE,aAAa,sBAAqB,CAAC,GAAET,GAAE,aAAa,EAAE,WAAUD,GAAE,YAAW,EAAE,cAAaa,EAAC,GAAEZ,GAAE,sBAAsB,IAAE,GAAEA,GAAE,mBAAmB,IAAE;AAAA,EAAC;AAAC;;;ACAx/D,IAAMa,KAAE,CAAAC,OAAG,GAAEA,GAAE,MAAK,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,QAAO,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,WAAU,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,qBAAoB,OAAM,GAAE,MAAK,EAAE,KAAI,GAAE,EAAC,UAAS,GAAE,MAAK,qCAAoC,OAAM,GAAE,MAAK,EAAE,eAAc,GAAE,EAAC,UAAS,GAAE,MAAK,UAAS,OAAM,GAAE,MAAK,EAAE,eAAc,GAAE,EAAC,UAAS,GAAE,MAAK,sBAAqB,OAAM,GAAE,MAAK,EAAE,KAAI,GAAE,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,eAAc,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,CAAC,EAAC,CAAC;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAOC,GAAE;AAAA,EAAI;AAAA,EAAC,kBAAkBH,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,aAAaI,KAAEC,IAAEC,IAAEL,IAAE;AAAC,UAAK,EAAC,SAAQM,IAAE,SAAQC,IAAE,cAAaC,IAAE,cAAaP,IAAE,aAAYQ,IAAE,eAAcC,IAAE,oBAAmBC,GAAC,IAAER,KAAES,KAAEC,GAAE,KAAKR,GAAE,WAAW,GAAEH,KAAE,EAAEO,EAAC,KAAG,cAAYA,GAAE;AAAK,QAAIK,KAAEhB,GAAEc,EAAC,GAAEG,KAAE,EAAE;AAAU,IAAAb,OAAIY,KAAE,KAAK,iBAAiBT,GAAE,aAAYS,EAAC,GAAEC,KAAE,EAAE;AAAQ,UAAK,EAAC,YAAWC,IAAE,eAAcC,GAAC,IAAEH,IAAEI,KAAEX,GAAE,gBAAgB,mBAAmBJ,KAAES,IAAE,kBAAiBI,IAAEhB,EAAC;AAAE,QAAGW,MAAG,EAAED,EAAC,KAAG,CAACQ,GAAE,SAAS,QAAO,KAAKR,GAAE;AAAE,UAAMS,KAAE,IAAEhB,IAAE,YAAWiB,KAAE;AAAE,IAAAd,GAAE,WAAWY,EAAC,GAAE,KAAK,mBAAmBA,IAAEf,KAAEC,EAAC,GAAEQ,GAAE,kBAAgBL,GAAE,eAAe,aAAaD,IAAEY,IAAEN,EAAC;AAAE,UAAMS,KAAE,MAAIpB,KAAEG,GAAE,IAAI;AAAO,IAAAc,GAAE,aAAa,gBAAeG,EAAC,GAAEH,GAAE,aAAa,UAASE,KAAED,EAAC,GAAED,GAAE,aAAa,kBAAiBC,EAAC,GAAE,KAAK,mBAAmBP,IAAEM,IAAEV,IAAEJ,EAAC,GAAE,KAAK,8BAA8BQ,IAAEM,IAAEV,EAAC,GAAEF,GAAE,sBAAsB,KAAE;AAAE,UAAMgB,KAAEjB,GAAE,OAAO,OAAOC,IAAEW,IAAED,IAAEd,EAAC;AAAE,QAAIqB,KAAElB,GAAE,YAAWmB,KAAEnB,GAAE,YAAU,YAAY;AAAkB,IAAAH,OAAIqB,MAAG,GAAEC,MAAG,IAAGlB,GAAE,QAAQgB,EAAC,GAAEhB,GAAE,aAAaS,IAAEQ,IAAE,EAAE,cAAaC,EAAC;AAAA,EAAC;AAAC;;;ACA9jD,IAAM,IAAE,CAAAC,QAAG,GAAEA,IAAE,MAAK,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,QAAO,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,WAAU,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,iBAAgB,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,KAAI,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,GAAE,EAAC,UAAS,GAAE,MAAK,kBAAiB,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,CAAC,EAAC,CAAC;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAOC,GAAE;AAAA,EAAI;AAAA,EAAC,kBAAkBH,KAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,aAAaI,IAAEC,IAAEC,IAAEL,IAAE;AAAC,UAAK,EAAC,SAAQM,IAAE,SAAQC,IAAE,cAAaC,IAAE,OAAMC,IAAE,aAAYC,IAAE,eAAcT,IAAE,oBAAmBU,GAAC,IAAER,IAAES,KAAEC,GAAE,KAAKR,GAAE,WAAW,GAAEH,KAAE,EAAEQ,EAAC,KAAG,cAAYA,GAAE,MAAK,EAAC,eAAcI,IAAE,YAAWC,GAAC,IAAE,EAAEH,EAAC,GAAEI,KAAET,GAAE,gBAAgB,mBAAmBJ,IAAES,IAAE,kBAAiBG,IAAEf,EAAC;AAAE,QAAGW,MAAG,EAAEV,EAAC,KAAG,CAACe,GAAE,SAAS,QAAO,KAAKf,GAAE;AAAE,IAAAK,GAAE,WAAWU,EAAC;AAAE,QAAIC,KAAE,EAAE;AAAU,IAAAf,OAAIe,KAAE,EAAE,SAAQ,KAAK,mBAAmBD,IAAEb,IAAEC,EAAC,GAAEG,GAAE,eAAe,aAAaD,IAAEU,IAAEJ,EAAC,GAAEI,GAAE,oBAAoB,iBAAgBP,GAAE,WAAW,GAAEO,GAAE,oBAAoB,qBAAoBP,GAAE,eAAe,GAAE,KAAK,mBAAmBG,IAAEI,IAAER,IAAEJ,EAAC,GAAE,KAAK,8BAA8BQ,IAAEI,IAAER,EAAC,GAAE,KAAK,uBAAuBI,IAAEI,IAAER,EAAC;AAAE,UAAMU,KAAEb,GAAE,OAAO,OAAOC,IAAEQ,IAAEC,EAAC,GAAEI,KAAEd,GAAE,YAAU,YAAY;AAAkB,IAAAW,GAAE,aAAa,gBAAe,CAAC,GAAEA,GAAE,aAAa,sBAAqB,CAAC,GAAEV,GAAE,QAAQY,EAAC,GAAEZ,GAAE,aAAaW,IAAEZ,GAAE,YAAW,EAAE,cAAac,EAAC,GAAEH,GAAE,aAAa,gBAAe,CAAC,GAAEA,GAAE,aAAa,sBAAqB,CAAC,GAAEV,GAAE,aAAa,EAAE,WAAUD,GAAE,YAAW,EAAE,cAAac,EAAC,GAAEH,GAAE,aAAa,gBAAe,CAAC,GAAEA,GAAE,aAAa,sBAAqB,CAAC,GAAEV,GAAE,aAAaW,IAAEZ,GAAE,YAAW,EAAE,cAAac,EAAC;AAAA,EAAC;AAAC;;;ACAnjC,IAAMC,KAAE,EAAC,QAAO,GAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKD,IAAE,OAAME,IAAE,MAAKC,IAAE,SAAQC,IAAE,QAAO,GAAE,SAAQJ,IAAE,UAASK,IAAE,eAAcF,IAAE,SAAQH,IAAE,SAAQC,IAAE,WAAUA,IAAE,WAAUK,IAAE,YAAWC,IAAE,SAAQC,IAAE,UAASC,GAAC;;;ACA5jB,IAAMC,KAAE,CAAAC,QAAG;AAAC,UAAOA,IAAE,mBAAkB;AAAA,IAAC,KAAK;AAAE,aAAO,EAAE;AAAA,IAAc,KAAK;AAAE,aAAO,EAAE;AAAA,IAAe,KAAK;AAAE,aAAO,EAAE;AAAA,IAAa;AAAQ,YAAM,IAAIC,GAAE,8BAA8B;AAAA,EAAC;AAAC;AAAtL,IAAwL,IAAE,CAACC,IAAEF,KAAEG,KAAEF,OAAI;AAAC,MAAIG,KAAE;AAAE,WAAQC,KAAE,GAAEA,KAAEF,KAAEE,MAAI;AAAC,UAAMF,MAAED,GAAE,KAAGF,MAAEK,KAAE,EAAE,GAAEJ,KAAEC,GAAE,KAAGF,MAAEK,KAAE,KAAG,CAAC;AAAE,IAAAD,OAAIF,GAAE,KAAGF,MAAEK,GAAE,IAAEF,QAAID,GAAE,KAAGF,MAAEK,MAAG,CAAC,IAAEJ;AAAA,EAAE;AAAC,SAAOA,KAAEG,KAAE,IAAEA,KAAE;AAAC;AAA5T,IAA8TE,KAAE,CAAC,EAAC,QAAOJ,IAAE,SAAQF,IAAC,GAAEG,QAAI;AAAC,QAAMC,KAAE,CAAC;AAAE,WAAQC,KAAE,GAAEE,KAAE,GAAEF,KAAEL,IAAE,QAAOO,MAAGP,IAAEK,EAAC,GAAEA,MAAG,GAAE;AAAC,UAAMG,KAAED,IAAEE,KAAE,CAAC;AAAE,WAAKJ,KAAEL,IAAE,SAAO,KAAG,EAAEE,IAAEK,KAAEP,IAAEK,EAAC,GAAEL,IAAEK,KAAE,CAAC,GAAEF,GAAC,GAAEE,MAAG,GAAEE,MAAGP,IAAEK,EAAC,EAAE,CAAAI,GAAE,KAAKF,KAAEP,IAAEK,EAAC,IAAEG,EAAC;AAAE,UAAME,KAAER,GAAE,MAAM,IAAEM,IAAE,KAAGD,KAAEP,IAAEK,EAAC,EAAE,GAAEM,KAAER,GAAEO,IAAED,IAAE,CAAC;AAAE,eAAUP,MAAKS,GAAE,CAAAP,GAAE,KAAKF,KAAEM,EAAC;AAAA,EAAC;AAAC,SAAOJ;AAAC;AAAE,IAAMQ,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYV,IAAEF,KAAEG,KAAEF,KAAE,OAAG;AAAC,SAAK,SAAO,CAAC,GAAE,KAAK,WAASC,IAAE,KAAK,UAAQF,KAAE,KAAK,gBAAcG,KAAE,KAAK,aAAWF;AAAA,EAAC;AAAA,EAAC,OAAO,SAAS,EAAC,GAAEC,IAAE,GAAEF,KAAE,OAAMG,KAAE,QAAOF,GAAC,GAAE;AAAC,UAAMG,KAAEF,IAAEG,KAAEL,KAAEO,KAAEH,KAAED,KAAEK,KAAEH,KAAEJ;AAAE,WAAO,GAAE,iBAAiB,EAAC,MAAKG,IAAE,MAAKC,IAAE,MAAKE,IAAE,MAAKC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,SAASN,IAAE;AAAC,UAAMF,MAAE,EAAE,IAAIA,MAAEE,GAAE,MAAK,OAAG,KAAE,GAAEC,MAAEH,IAAE,QAAOC,KAAE,IAAI,YAAYK,GAAEN,KAAE,IAAE,CAAC,GAAEI,KAAE,IAAI,YAAYD,IAAE,SAAO,CAAC;AAAE,aAAQE,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAD,GAAEC,EAAC,IAAE,EAAE,KAAK,MAAMF,IAAE,IAAEE,EAAC,CAAC,GAAE,KAAK,MAAMF,IAAE,IAAEE,KAAE,CAAC,CAAC,CAAC;AAAE,WAAO,IAAI,GAAE,EAAC,UAASD,GAAC,GAAEH,IAAE,EAAE,SAAS;AAAA,EAAC;AAAA,EAAC,OAAO,aAAaE,KAAEF,IAAE;AAJjrD;AAIkrD,UAAMG,MAAE,KAAAH,GAAE,aAAF,mBAAY;AAAK,YAAOG,IAAE;AAAA,MAAC,KAAI;AAAU,eAAO,GAAE,YAAYD,KAAEF,GAAE,QAAQ;AAAA,MAAE,KAAI;AAAS,eAAO,GAAE,cAAcE,KAAEF,GAAE,QAAQ;AAAA,MAAE;AAAQ,eAAO,EAAE,UAAU,mCAAmC,EAAE,MAAM,IAAIA,GAAE,oBAAmB,qCAAqCG,EAAC,IAAGH,EAAC,CAAC,GAAE,GAAE,SAAS,EAAC,GAAE,GAAE,GAAE,GAAE,OAAM,GAAE,QAAO,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,YAAYC,IAAEF,KAAE;AAAC,UAAMG,MAAE,EAAE,IAAIH,MAAEA,KAAE,OAAG,KAAE,GAAEC,KAAEE,IAAE,QAAOI,KAAE,IAAI,YAAYD,GAAEH,KAAE,KAAE,CAAC,GAAEQ,KAAE,IAAI,YAAYV,GAAE,SAAO,CAAC,GAAEY,KAAER,GAAE,GAAES,KAAET,GAAE;AAAE,aAAQA,KAAE,GAAEA,KAAEM,GAAE,QAAON,KAAI,CAAAF,GAAEU,IAAEZ,GAAE,IAAEI,EAAC,GAAEJ,GAAE,IAAEI,KAAE,CAAC,CAAC,GAAEH,GAAE,SAASY,IAAED,EAAC,GAAEF,GAAEN,EAAC,IAAE,EAAE,KAAK,MAAMS,GAAE,CAAC,CAAC,GAAE,KAAK,MAAMA,GAAE,CAAC,CAAC,CAAC;AAAE,WAAO,IAAI,GAAE,EAAC,UAASH,GAAC,GAAEJ,IAAE,EAAE,WAAU,IAAE;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiB,EAAC,MAAKL,IAAE,MAAKF,KAAE,MAAKG,KAAE,MAAKF,GAAC,GAAE;AAAC,UAAMG,KAAE,EAAC,UAAS,IAAI,YAAY,CAAC,EAAEF,IAAEC,GAAC,GAAE,EAAEH,KAAEG,GAAC,GAAE,EAAED,IAAED,EAAC,GAAE,EAAEC,IAAED,EAAC,GAAE,EAAED,KAAEG,GAAC,GAAE,EAAEH,KAAEC,EAAC,CAAC,CAAC,EAAC,GAAEI,KAAE,IAAI,YAAY,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAE,WAAO,IAAI,GAAED,IAAEC,IAAE,EAAE,SAAS;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcH,IAAEF,KAAE;AAAC,UAAK,CAACG,KAAEF,EAAC,IAAEC,GAAE,SAAS,CAAC,GAAE,CAAC,GAAE,CAACF,IAAE,MAAKA,IAAE,IAAI,CAAC,GAAE,CAACI,IAAEC,EAAC,IAAEH,GAAE,SAAS,CAAC,GAAE,CAAC,GAAE,CAACF,IAAE,MAAKA,IAAE,IAAI,CAAC,GAAEO,KAAE,EAAC,UAAS,IAAI,YAAY,CAAC,EAAEJ,KAAEF,EAAC,GAAE,EAAEG,IAAEH,EAAC,GAAE,EAAEE,KAAEE,EAAC,GAAE,EAAEF,KAAEE,EAAC,GAAE,EAAED,IAAEH,EAAC,GAAE,EAAEG,IAAEC,EAAC,CAAC,CAAC,EAAC,GAAEG,KAAE,IAAI,YAAY,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAE,WAAO,IAAI,GAAED,IAAEC,IAAE,EAAE,SAAS;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,MAAE,KAAK,OAAO,WAAW,KAAG,KAAK,OAAO,YAAY,QAAQ;AAAE,eAAUN,MAAK,KAAK,OAAO,cAAc,GAAE,KAAK,OAAO,cAAcA,EAAC,CAAC,KAAG,KAAK,OAAO,cAAcA,EAAC,EAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAOH,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,eAAeG,IAAEF,MAAE,EAAE,aAAY;AAAC,WAAO,KAAK,OAAO,gBAAc,KAAK,OAAO,cAAYe,GAAE,YAAYb,IAAEF,KAAE,KAAK,OAAO,IAAG,KAAK,OAAO;AAAA,EAAW;AAAA,EAAC,iBAAiBE,IAAEF,MAAE,EAAE,aAAY;AAAC,WAAO,KAAK,OAAO,kBAAgB,KAAK,OAAO,gBAAc,OAAO,KAAK,KAAK,QAAQ,EAAE,OAAQ,CAACG,KAAEF,QAAK,EAAC,GAAGE,KAAE,CAACF,EAAC,GAAEc,GAAE,aAAab,IAAEF,KAAE,KAAK,SAASC,EAAC,CAAC,EAAC,IAAI,CAAC,CAAC,IAAG,KAAK,OAAO;AAAA,EAAa;AAAC;;;ACA/3F,IAAMe,KAAE,CAAAC,QAAG,WAAWA,GAAC,IAAE;AAAI,IAAMC,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,YAAYF,KAAEG,IAAE;AAAC,UAAM,GAAE,KAAK,QAAMA,IAAE,KAAK,SAAO,CAAC,GAAE,KAAK,QAAMH,KAAE,KAAK,UAAQI,GAAG,MAAID,GAAE,SAAU,MAAI,KAAK,YAAY,CAAE,GAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,OAAO,aAAaH,KAAEG,IAAE;AAAC,WAAO,IAAI,GAAEH,KAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,MAAE,KAAK,OAAO,IAAI,MAAI,KAAK,OAAO,KAAK,QAAQ,GAAE,KAAK,OAAO,OAAK,OAAM,EAAE,KAAK,OAAO,GAAG,MAAI,KAAK,OAAO,IAAI,QAAQ,GAAE,KAAK,OAAO,MAAI;AAAA,EAAK;AAAA,EAAC,UAAS;AAAC,SAAK,WAAW,GAAE,KAAK,QAAQ,OAAO;AAAA,EAAC;AAAA,EAAC,OAAOH,KAAEG,IAAED,KAAEG,IAAE;AAAC,UAAK,CAACC,IAAEC,EAAC,IAAEJ,GAAE;AAAK,QAAG,eAAa,KAAK,MAAM,QAAM,KAAK,eAAaG,MAAG,KAAK,gBAAcC,OAAI,KAAK,aAAWD,IAAE,KAAK,cAAYC,IAAE,KAAK,WAAW,IAAG,EAAE,KAAK,OAAO,GAAG,GAAE;AAAC,YAAMC,KAAE,KAAK,YAAYL,IAAE,KAAK,KAAK,GAAEG,KAAEE,GAAE,eAAeR,GAAC,GAAEO,KAAEC,GAAE,iBAAiBR,GAAC;AAAE,WAAK,OAAO,OAAKQ,IAAE,KAAK,OAAO,MAAI,IAAIC,GAAET,KAAEE,KAAEG,IAAEE,IAAED,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK,OAAO;AAAA,EAAG;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAI,EAAE,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,SAAK,WAAW,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,kBAAkBN,KAAEG,IAAE;AAAC,UAAK,CAACD,KAAEM,EAAC,IAAER,IAAE,MAAKK,KAAE,YAAU,OAAOF,GAAE,OAAKJ,GAAEI,GAAE,IAAI,IAAED,MAAEC,GAAE,MAAKG,KAAE,YAAU,OAAOH,GAAE,QAAMJ,GAAEI,GAAE,KAAK,IAAED,MAAEC,GAAE,OAAMI,KAAE,YAAU,OAAOJ,GAAE,MAAIJ,GAAEI,GAAE,GAAG,IAAEK,KAAEL,GAAE,KAAIO,KAAE,YAAU,OAAOP,GAAE,SAAOJ,GAAEI,GAAE,MAAM,IAAEK,KAAEL,GAAE,QAAOQ,KAAEN,IAAEJ,KAAEM;AAAE,WAAM,EAAC,GAAEI,IAAE,GAAEV,IAAE,OAAM,KAAK,IAAIC,MAAEI,KAAEK,IAAE,CAAC,GAAE,QAAO,KAAK,IAAIH,KAAEE,KAAET,IAAE,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYC,KAAEM,IAAE;AAAC,YAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAO,eAAOJ,GAAE,SAAS,KAAK,kBAAkBF,KAAEM,EAAC,CAAC;AAAA,MAAE,KAAI;AAAO,eAAOJ,GAAE,SAASI,EAAC;AAAA,MAAE,KAAI;AAAW,eAAOJ,GAAE,aAAaF,KAAEM,EAAC;AAAA,MAAE;AAAQ,eAAO,EAAE,UAAU,yCAAyC,EAAE,MAAM,IAAIA,GAAE,oBAAmB,oEAAoE,CAAC,GAAEJ,GAAE,SAAS,EAAC,GAAE,GAAE,GAAE,GAAE,OAAM,GAAE,QAAO,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACA3mD,IAAM,IAAN,cAAgBQ,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,KAAK,YAAY;AAAA,EAAI;AAAA,EAAC,IAAI,MAAMC,IAAE;AAAC,SAAK,SAAOA,IAAE,KAAK,SAAS,QAAS,CAAAC,QAAGA,IAAE,QAAMD,EAAE,GAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAM,aAAaA,EAAC,GAAE,KAAK,iBAAiBA,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAI,EAAE,EAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,UAAMC,MAAE,KAAK,mBAAmBD,EAAC,GAAE,EAAC,SAAQE,IAAE,eAAcC,KAAE,UAASC,IAAE,WAAUC,GAAC,IAAEJ,KAAEK,KAAED,OAAI,EAAE,SAAOA,OAAI,EAAE,YAAU,IAAEF,MAAE,KAAK;AAAgB,IAAAC,GAAE,qBAAqB,KAAK,IAAI,GAAEF,GAAE,kBAAkBD,KAAE,KAAK,iBAAe,MAAI,GAAEK,EAAC,GAAE,KAAK,eAAeL,GAAC,GAAEC,GAAE,eAAeD,KAAEK,EAAC,GAAEF,GAAE,mBAAmB;AAAA,EAAC;AAAA,EAAC,eAAeH,KAAE;AAAC,MAAE,KAAK,aAAa,MAAI,KAAK,gBAAc,KAAK,oBAAoBA,IAAE,OAAO;AAAG,eAAUD,MAAK,KAAK,cAAc,KAAG;AAAC,MAAAA,GAAE,OAAOC,GAAC;AAAA,IAAC,SAAOC,IAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBF,IAAE;AAAC,WAAOA,GAAE,aAAW,KAAK,sBAAqBA;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,WAAM,CAACA,GAAE,mBAAmB,EAAC,MAAK,QAAO,SAAQ,CAACO,GAAE,IAAI,GAAE,QAAO,MAAI,KAAK,gBAAe,WAAU,EAAE,MAAI,EAAE,QAAM,EAAE,cAAY,EAAE,QAAM,EAAE,UAAS,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBP,IAAE;AAAC,eAAUC,OAAK,KAAK,SAAS,CAAAA,IAAE,aAAaD,EAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,UAAM,SAAS,GAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,UAAM,SAAS,GAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,MAAE,KAAK,cAAc,MAAI,KAAK,eAAe,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,iBAAe;AAAM,UAAMA,KAAE,KAAK;AAAM,QAAG,CAACA,GAAE;AAAO,UAAME,KAAE,KAAK;AAAO,MAAEA,EAAC,KAAGA,GAAE,WAAS,KAAK,iBAAeA,GAAE,MAAM,IAAK,CAAAD,QAAGO,GAAE,aAAaR,IAAEC,GAAC,CAAE,IAAG,KAAK,cAAc;AAAA,EAAC;AAAC;", "names": ["n", "s", "E", "f", "t", "t", "r", "s", "a", "o", "e", "t", "i", "s", "n", "r", "m", "l", "c", "p", "u", "f", "_", "E", "M", "e", "o", "e", "r", "t", "n", "e", "n", "p", "t", "e", "i", "s", "o", "a", "U", "m", "v", "f", "l", "c", "B", "C", "D", "E", "F", "G", "r", "n", "u", "d", "p", "E", "t", "o", "a", "i", "m", "u", "c", "y", "_", "f", "S", "N", "T", "g", "x", "R", "h", "U", "A", "O", "G", "I", "M", "b", "D", "e", "r", "n", "t", "r", "n", "i", "f", "s", "u", "m", "_", "p", "d", "x", "b", "c", "h", "E", "F", "R", "L", "T", "w", "S", "x", "e", "B", "t", "r", "i", "a", "s", "u", "n", "h", "f", "m", "d", "p", "_", "b", "o", "c", "l", "E", "r", "t", "m", "r", "n", "c", "d", "t", "r", "s", "o", "i", "m", "f", "g", "e", "c", "m", "t", "e", "r", "s", "o", "n", "i", "u", "d", "l", "p", "f", "_", "v", "y", "A", "u", "e", "t", "c", "p", "E", "o", "r", "i", "l", "y", "_", "d", "f", "S", "w", "g", "N", "T", "I", "U", "x", "L", "h", "G", "O", "b", "D", "R", "a", "O", "c", "L", "E", "e", "o", "r", "s", "i", "n", "d", "t", "a", "l", "u", "f", "T", "p", "x", "w", "D", "y", "g", "S", "b", "A", "x", "t", "r", "e", "i", "o", "s", "n", "a", "l", "E", "m", "f", "_", "c", "s", "r", "o", "n", "l", "f", "u", "i", "m", "a", "h", "t", "r", "e", "s", "i", "o", "a", "E", "l", "f", "d", "t", "r", "e", "o", "c", "m", "p", "_", "h", "g", "v", "y", "b", "x", "M", "U", "w", "L", "A", "P", "I", "R", "T", "s", "a", "i", "E", "f", "c", "t", "n", "m", "d", "f", "u", "p", "y", "g", "v", "E", "M", "e", "T", "x", "I", "U", "_", "R", "h", "D", "L", "r", "S", "i", "c", "m", "t", "e", "i", "r", "a", "l", "s", "f", "n", "u", "d", "p", "y", "g", "_", "E", "M", "v", "P", "o", "I", "T", "U", "x", "D", "h", "S", "R", "w", "N", "L", "A", "V", "O", "C", "c", "t", "o", "u", "d", "p", "y", "g", "E", "M", "_", "v", "U", "e", "I", "P", "x", "T", "D", "S", "L", "N", "R", "V", "h", "i", "w", "A", "b", "r", "a", "h", "M", "t", "e", "i", "a", "r", "n", "f", "m", "p", "d", "y", "g", "_", "P", "T", "U", "E", "x", "v", "D", "I", "R", "l", "S", "V", "w", "A", "L", "O", "c", "N", "s", "b", "G", "W", "F", "B", "C", "Y", "o", "m", "e", "l", "p", "E", "t", "o", "a", "u", "f", "d", "c", "_", "y", "Z", "N", "U", "S", "T", "g", "G", "s", "e", "m", "p", "E", "t", "o", "a", "l", "c", "u", "d", "y", "_", "f", "C", "N", "g", "S", "T", "U", "G", "I", "D", "R", "b", "x", "t", "m", "p", "E", "e", "o", "a", "l", "u", "d", "f", "_", "c", "y", "P", "N", "U", "S", "T", "x", "I", "w", "m", "c", "l", "d", "h", "x", "M", "O", "B", "s", "x", "t", "s", "e", "r", "o", "n", "p", "i", "c", "a", "f", "h", "l", "u", "y", "E", "n", "t", "m", "r", "e", "l", "i", "o", "h", "s", "f", "a", "c", "h", "e", "r", "s", "t", "i", "n", "a", "w", "m"]}