{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/sources/support/sourceUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e}from\"../../../../core/maybe.js\";import{isValid as t}from\"../../../../geometry/support/spatialReferenceUtils.js\";import{validateFieldValue as n,validationErrorToString as i,isNumericField as r,isStringField as s,sanitizeNullFieldValue as o}from\"../../../support/fieldUtils.js\";class l{constructor(){this.code=null,this.description=null}}class u{constructor(e){this.error=new l,this.globalId=null,this.objectId=null,this.success=!1,this.uniqueId=null,this.error.description=e}}function a(e){return new u(e)}class c{constructor(e){this.globalId=null,this.success=!0,this.objectId=this.uniqueId=e}}function f(e){return new c(e)}const d=new Set;function m(e,t,r,s=!1,o){d.clear();for(const l in r){const u=e.get(l);if(!u)continue;const c=r[l],f=p(u,c);if(f!==c&&o&&o.push({name:\"invalid-value-type\",message:\"attribute value was converted to match the field type\",details:{field:u,originalValue:c,sanitizedValue:f}}),d.add(u.name),u&&(s||u.editable)){const e=n(u,f);if(e)return a(i(e,u,f));t[u.name]=f}}for(const n of e?.requiredFields??[])if(!d.has(n.name))return a(`missing required field \"${n.name}\"`);return null}function p(e,t){let n=t;return\"string\"==typeof t&&r(e)?n=parseFloat(t):null!=t&&s(e)&&\"string\"!=typeof t&&(n=String(t)),o(n)}let h;function g(n,i){if(!n||!t(i))return n;if(\"rings\"in n||\"paths\"in n){if(e(h))throw new TypeError(\"geometry engine not loaded\");return h.simplify(i,n)}return n}async function y(){return e(h)&&(h=await import(\"../../../../geometry/geometryEngineJSON.js\")),h}async function w(e,n){!t(e)||\"esriGeometryPolygon\"!==n&&\"esriGeometryPolyline\"!==n||await y()}export{a as createFeatureEditErrorResult,f as createFeatureEditSuccessResult,w as loadGeometryEngineForSimplify,m as mixAttributes,g as simplify};\n"], "mappings": ";;;;;;;;;;;;;;;AAIuS,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,OAAK,MAAK,KAAK,cAAY;AAAA,EAAI;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAY,GAAE;AAAC,SAAK,QAAM,IAAI,KAAE,KAAK,WAAS,MAAK,KAAK,WAAS,MAAK,KAAK,UAAQ,OAAG,KAAK,WAAS,MAAK,KAAK,MAAM,cAAY;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,IAAI,EAAE,CAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAY,GAAE;AAAC,SAAK,WAAS,MAAK,KAAK,UAAQ,MAAG,KAAK,WAAS,KAAK,WAAS;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,IAAI,EAAE,CAAC;AAAC;AAAC,IAAM,IAAE,oBAAI;AAAI,SAAS,EAAE,GAAEA,IAAE,GAAE,IAAE,OAAG,GAAE;AAAC,IAAE,MAAM;AAAE,aAAUC,MAAK,GAAE;AAAC,UAAMC,KAAE,EAAE,IAAID,EAAC;AAAE,QAAG,CAACC,GAAE;AAAS,UAAMC,KAAE,EAAEF,EAAC,GAAEG,KAAE,EAAEF,IAAEC,EAAC;AAAE,QAAGC,OAAID,MAAG,KAAG,EAAE,KAAK,EAAC,MAAK,sBAAqB,SAAQ,yDAAwD,SAAQ,EAAC,OAAMD,IAAE,eAAcC,IAAE,gBAAeC,GAAC,EAAC,CAAC,GAAE,EAAE,IAAIF,GAAE,IAAI,GAAEA,OAAI,KAAGA,GAAE,WAAU;AAAC,YAAMG,KAAE,GAAEH,IAAEE,EAAC;AAAE,UAAGC,GAAE,QAAO,EAAE,GAAEA,IAAEH,IAAEE,EAAC,CAAC;AAAE,MAAAJ,GAAEE,GAAE,IAAI,IAAEE;AAAA,IAAC;AAAA,EAAC;AAAC,aAAU,MAAK,uBAAG,mBAAgB,CAAC,EAAE,KAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,QAAO,EAAE,2BAA2B,EAAE,IAAI,GAAG;AAAE,SAAO;AAAI;AAAC,SAAS,EAAE,GAAEJ,IAAE;AAAC,MAAI,IAAEA;AAAE,SAAM,YAAU,OAAOA,MAAG,GAAE,CAAC,IAAE,IAAE,WAAWA,EAAC,IAAE,QAAMA,MAAG,GAAE,CAAC,KAAG,YAAU,OAAOA,OAAI,IAAE,OAAOA,EAAC,IAAG,GAAE,CAAC;AAAC;AAAC,IAAI;AAAE,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,CAAC,KAAG,CAAC,EAAE,CAAC,EAAE,QAAO;AAAE,MAAG,WAAU,KAAG,WAAU,GAAE;AAAC,QAAG,EAAE,CAAC,EAAE,OAAM,IAAI,UAAU,4BAA4B;AAAE,WAAO,EAAE,SAAS,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,eAAe,IAAG;AAAC,SAAO,EAAE,CAAC,MAAI,IAAE,MAAM,OAAO,kCAA4C,IAAG;AAAC;AAAC,eAAe,EAAE,GAAE,GAAE;AAAC,GAAC,EAAE,CAAC,KAAG,0BAAwB,KAAG,2BAAyB,KAAG,MAAM,EAAE;AAAC;", "names": ["t", "l", "u", "c", "f", "e"]}