import {
  l,
  p as p2,
  y as y2
} from "./chunk-TWFTBWXP.js";
import {
  o
} from "./chunk-PEEUPDEG.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/renderers/SimpleRenderer.js
var c;
var n = c = class extends y2(p2) {
  constructor(e2) {
    super(e2), this.description = null, this.label = null, this.symbol = null, this.type = "simple";
  }
  async collectRequiredFields(e2, s) {
    await Promise.all([this.collectSymbolFields(e2, s), this.collectVVRequiredFields(e2, s)]);
  }
  async collectSymbolFields(e2, s) {
    await Promise.all(this.getSymbols().map((r) => r.collectRequiredFields(e2, s)));
  }
  getSymbol(e2, s) {
    return this.symbol;
  }
  async getSymbolAsync(e2, s) {
    return this.symbol;
  }
  getSymbols() {
    return this.symbol ? [this.symbol] : [];
  }
  getAttributeHash() {
    return this.visualVariables && this.visualVariables.reduce((e2, s) => e2 + s.getAttributeHash(), "");
  }
  getMeshHash() {
    return this.getSymbols().reduce((e2, s) => e2 + JSON.stringify(s), "");
  }
  get arcadeRequired() {
    return this.arcadeRequiredForVisualVariables;
  }
  clone() {
    return new c({ description: this.description, label: this.label, symbol: this.symbol && this.symbol.clone(), visualVariables: p(this.visualVariables), authoringInfo: this.authoringInfo && this.authoringInfo.clone() });
  }
};
e([y({ type: String, json: { write: true } })], n.prototype, "description", void 0), e([y({ type: String, json: { write: true } })], n.prototype, "label", void 0), e([y(l)], n.prototype, "symbol", void 0), e([o({ simple: "simple" })], n.prototype, "type", void 0), n = c = e([a("esri.renderers.SimpleRenderer")], n);
var p3 = n;

export {
  p3 as p
};
//# sourceMappingURL=chunk-ZL6CFFJK.js.map
