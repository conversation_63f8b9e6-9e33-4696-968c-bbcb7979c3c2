import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/views/interactive/sketch/SketchTooltipElevationOptions.js
var t = class extends v {
  constructor(o) {
    super(o), this.mode = "absolute-height";
  }
  toJSON() {
    return { mode: this.mode };
  }
};
e([y({ type: String, nonNullable: true })], t.prototype, "mode", void 0), t = e([a("esri.widgets.Sketch.SketchTooltipOptions.ElevationOptions")], t);

// node_modules/@arcgis/core/views/interactive/sketch/SketchTooltipVisibleElements.js
var i = class extends v {
  constructor() {
    super(...arguments), this.distance = true, this.elevation = true, this.size = true, this.scale = true, this.rotation = true, this.orientation = true, this.totalLength = true, this.area = true, this.radius = true, this.helpMessage = false;
  }
  toJSON() {
    return { distance: this.distance, size: this.size, scale: this.scale, rotation: this.rotation, orientation: this.orientation, totalLength: this.totalLength, area: this.area, radius: this.radius };
  }
};
e([y({ type: Boolean, nonNullable: true })], i.prototype, "distance", void 0), e([y({ type: Boolean, nonNullable: true })], i.prototype, "elevation", void 0), e([y({ type: Boolean, nonNullable: true })], i.prototype, "size", void 0), e([y({ type: Boolean, nonNullable: true })], i.prototype, "scale", void 0), e([y({ type: Boolean, nonNullable: true })], i.prototype, "rotation", void 0), e([y({ type: Boolean, nonNullable: true })], i.prototype, "orientation", void 0), e([y({ type: Boolean, nonNullable: true })], i.prototype, "totalLength", void 0), e([y({ type: Boolean, nonNullable: true })], i.prototype, "area", void 0), e([y({ type: Boolean, nonNullable: true })], i.prototype, "radius", void 0), e([y({ type: Boolean, nonNullable: true })], i.prototype, "helpMessage", void 0), i = e([a("esri.widgets.Sketch.SketchTooltipOptions.VisibleElements")], i);

// node_modules/@arcgis/core/views/interactive/sketch/SketchTooltipOptions.js
var l = class extends v {
  constructor(e2) {
    super(e2), this.enabled = false, this.elevation = new t(), this.visibleElements = new i(), this.visualVariables = null;
  }
  toJSON() {
    return { enabled: this.enabled, elevation: this.elevation, visibleElements: this.visibleElements };
  }
};
e([y({ type: Boolean, nonNullable: true })], l.prototype, "enabled", void 0), e([y({ type: t, nonNullable: true })], l.prototype, "elevation", void 0), e([y({ type: i, nonNullable: true })], l.prototype, "visibleElements", void 0), e([y()], l.prototype, "visualVariables", void 0), l = e([a("esri.widgets.Sketch.SketchTooltipOptions")], l);
var p = l;

export {
  p
};
//# sourceMappingURL=chunk-WX7B7OKM.js.map
