import {
  c,
  e,
  r,
  t3 as t2,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a,
  a2,
  s as s4,
  t3 as t
} from "./chunk-JN4FSB7Y.js";
import {
  e as e2,
  o2 as o,
  s as s2,
  u
} from "./chunk-2CM7MIII.js";
import {
  s as s3
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  i
} from "./chunk-GZGAQUSK.js";
import {
  N
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/accessorSupport/write.js
function a3(r2, e4, t4, i3, o2) {
  var _a, _b;
  const n2 = {};
  return (_b = (_a = e4.write) == null ? void 0 : _a.writer) == null ? void 0 : _b.call(r2, i3, n2, t4, o2), n2;
}
function f(r2, i3, s7, u4, l2, a5) {
  if (!u4 || !u4.write) return false;
  const f3 = r2.get(s7);
  if (!l2 && u4.write.overridePolicy) {
    const e4 = u4.write.overridePolicy.call(r2, f3, s7, a5);
    void 0 !== e4 && (l2 = e4);
  }
  if (l2 || (l2 = u4.write), !l2 || false === l2.enabled) return false;
  if ((null === f3 && !l2.allowNull && !l2.writerEnsuresNonNull || void 0 === f3) && l2.isRequired) {
    const i4 = new s3("web-document-write:property-required", `Missing value for required property '${s7}' on '${r2.declaredClass}'`, { propertyName: s7, target: r2 });
    return i4 && a5 && a5.messages ? a5.messages.push(i4) : i4 && !a5 && s.getLogger("esri.core.accessorSupport.write").error(i4.name, i4.message), false;
  }
  if (void 0 === f3) return false;
  if (null === f3 && !l2.allowNull && !l2.writerEnsuresNonNull) return false;
  if ((!i3.store.multipleOriginsSupported || i3.store.originOf(s7) === r.DEFAULTS) && p2(r2, s7, a5, u4, f3)) return false;
  if (!l2.ignoreOrigin && a5 && a5.origin && i3.store.multipleOriginsSupported) {
    if (i3.store.originOf(s7) < t2(a5.origin)) return false;
  }
  return true;
}
function p2(e4, t4, i3, o2, n2) {
  const s7 = o2.default;
  if (void 0 === s7) return false;
  if (null != o2.defaultEquals) return o2.defaultEquals(n2);
  if ("function" == typeof s7) {
    if (Array.isArray(n2)) {
      const o3 = s7.call(e4, t4, i3);
      return i(o3, n2);
    }
    return false;
  }
  return s7 === n2;
}
function c2(r2, e4, t4, i3) {
  const o2 = e2(r2), n2 = o2.metadatas, u4 = s4(n2[e4], i3);
  return !!u4 && f(r2, o2, e4, u4, t4, i3);
}
function g(r2, e4, t4) {
  var _a, _b;
  if (r2 && "function" == typeof r2.toJSON && (!r2.toJSON.isDefaultToJSON || !r2.write)) return o(e4, r2.toJSON(t4));
  const o2 = e2(r2), n2 = o2.metadatas;
  for (const s7 in n2) {
    const p3 = s4(n2[s7], t4);
    if (!f(r2, o2, s7, p3, void 0, t4)) continue;
    const c4 = r2.get(s7), g2 = a3(r2, p3, p3.write && "string" == typeof p3.write.target ? p3.write.target : s7, c4, t4);
    Object.keys(g2).length > 0 && (e4 = o(e4, g2), ((_b = (_a = t4 == null ? void 0 : t4.resources) == null ? void 0 : _a.pendingOperations) == null ? void 0 : _b.length) && t4.resources.pendingOperations.push(Promise.all(t4.resources.pendingOperations).then(() => o(e4, g2, () => "replace-arrays"))), t4 && t4.writtenProperties && t4.writtenProperties.push({ target: r2, propName: s7, oldOrigin: c(o2.store.originOf(s7)), newOrigin: t4.origin }));
  }
  return e4;
}

// node_modules/@arcgis/core/core/accessorSupport/DefaultsStore.js
var s5 = class _s {
  constructor() {
    this._values = /* @__PURE__ */ new Map(), this.multipleOriginsSupported = false;
  }
  clone(e4) {
    const t4 = new _s();
    return this._values.forEach((s7, r2) => {
      e4 && e4.has(r2) || t4.set(r2, p(s7.value), s7.origin);
    }), t4;
  }
  get(i3, e4) {
    e4 = this._normalizeOrigin(e4);
    const s7 = this._values.get(i3);
    return null == e4 || (s7 == null ? void 0 : s7.origin) === e4 ? s7 == null ? void 0 : s7.value : void 0;
  }
  originOf(i3) {
    var _a;
    return ((_a = this._values.get(i3)) == null ? void 0 : _a.origin) ?? r.USER;
  }
  keys(i3) {
    i3 = this._normalizeOrigin(i3);
    const e4 = [...this._values.keys()];
    return null == i3 ? e4 : e4.filter((e5) => {
      var _a;
      return ((_a = this._values.get(e5)) == null ? void 0 : _a.origin) === i3;
    });
  }
  set(i3, s7, r2) {
    if ((r2 = this._normalizeOrigin(r2)) === r.DEFAULTS) {
      const e4 = this._values.get(i3);
      if (e4 && null != e4.origin && e4.origin > r2) return;
    }
    this._values.set(i3, new t3(s7, r2));
  }
  delete(i3, e4) {
    var _a;
    null != (e4 = this._normalizeOrigin(e4)) && ((_a = this._values.get(i3)) == null ? void 0 : _a.origin) !== e4 || this._values.delete(i3);
  }
  has(i3, e4) {
    var _a;
    return null != (e4 = this._normalizeOrigin(e4)) ? ((_a = this._values.get(i3)) == null ? void 0 : _a.origin) === e4 : this._values.has(i3);
  }
  forEach(i3) {
    this._values.forEach(({ value: e4 }, s7) => i3(e4, s7));
  }
  _normalizeOrigin(i3) {
    if (null != i3) return i3 === r.DEFAULTS ? i3 : r.USER;
  }
};
var t3 = class {
  constructor(i3, e4) {
    this.value = i3, this.origin = e4;
  }
};

// node_modules/@arcgis/core/core/accessorSupport/defaultsStoreUtils.js
function e3(e4, r2, n2) {
  r2.keys().forEach((e5) => {
    n2.set(e5, r2.get(e5), r.DEFAULTS);
  });
  const o2 = e4.metadatas;
  Object.keys(o2).forEach((r3) => {
    e4.internalGet(r3) && n2.set(r3, e4.internalGet(r3), r.DEFAULTS);
  });
}

// node_modules/@arcgis/core/core/accessorSupport/read.js
function s6(e4, r2, n2) {
  if (!e4 || !e4.read || false === e4.read.enabled || !e4.read.source) return false;
  const o2 = e4.read.source;
  if ("string" == typeof o2) {
    if (o2 === r2) return true;
    if (o2.includes(".") && 0 === o2.indexOf(r2) && s2(o2, n2)) return true;
  } else for (const s7 of o2) {
    if (s7 === r2) return true;
    if (s7.includes(".") && 0 === s7.indexOf(r2) && s2(s7, n2)) return true;
  }
  return false;
}
function i2(e4) {
  return e4 && (!e4.read || false !== e4.read.enabled && !e4.read.source);
}
function a4(e4, t4, r2, o2, a5) {
  let f3 = t(t4[r2], a5);
  i2(f3) && (e4[r2] = true);
  for (const i3 of Object.getOwnPropertyNames(t4)) f3 = t(t4[i3], a5), s6(f3, r2, o2) && (e4[i3] = true);
}
function f2(e4, t4, r2, n2) {
  const s7 = r2.metadatas, i3 = a(s7[t4], "any", n2), a5 = i3 && i3.default;
  if (void 0 === a5) return;
  const f3 = "function" == typeof a5 ? a5.call(e4, t4, n2) : a5;
  void 0 !== f3 && r2.set(t4, f3);
}
var c3 = { origin: "service" };
function u2(t4, o2, s7 = c3) {
  if (!o2 || "object" != typeof o2) return;
  const i3 = e2(t4), u4 = i3.metadatas, d2 = {};
  for (const e4 of Object.getOwnPropertyNames(o2)) a4(d2, u4, e4, o2, s7);
  i3.setDefaultOrigin(s7.origin);
  for (const r2 of Object.getOwnPropertyNames(d2)) {
    const a5 = t(u4[r2], s7).read, f3 = a5 && a5.source;
    let c4;
    c4 = f3 && "string" == typeof f3 ? u(o2, f3) : o2[r2], a5 && a5.reader && (c4 = a5.reader.call(t4, c4, o2, s7)), void 0 !== c4 && i3.set(r2, c4);
  }
  if (!s7 || !s7.ignoreDefaults) {
    i3.setDefaultOrigin("defaults");
    for (const e4 of Object.getOwnPropertyNames(u4)) d2[e4] || f2(t4, e4, i3, s7);
  }
  i3.setDefaultOrigin("user");
}
function d(e4, t4, r2, n2 = c3) {
  var _a;
  const o2 = { ...n2, messages: [] };
  r2(o2), (_a = o2.messages) == null ? void 0 : _a.forEach((t5) => {
    "warning" !== t5.type || e4.loaded ? n2 && n2.messages && n2.messages.push(t5) : e4.loadWarnings.push(t5);
  });
}

// node_modules/@arcgis/core/core/JSONSupport.js
var u3 = (t4) => {
  let u4 = class extends t4 {
    constructor(...r2) {
      super(...r2);
      const t5 = N(e2(this)), i3 = t5.store, p3 = new s5();
      t5.store = p3, e3(t5, i3, p3);
    }
    read(r2, t5) {
      u2(this, r2, t5);
    }
    write(r2 = {}, t5) {
      return g(this, r2, t5);
    }
    toJSON(r2) {
      return this.write({}, r2);
    }
    static fromJSON(r2, t5) {
      return n.call(this, r2, t5);
    }
  };
  return u4 = e([a2("esri.core.JSONSupport")], u4), u4.prototype.toJSON.isDefaultToJSON = true, u4;
};
function n(r2, t4) {
  if (!r2) return null;
  if (r2.declaredClass) throw new Error("JSON object is already hydrated");
  const s7 = new this();
  return s7.read(r2, t4), s7;
}
var l = class extends u3(v) {
};
l = e([a2("esri.core.JSONSupport")], l);

export {
  e3 as e,
  u2 as u,
  d,
  c2 as c,
  g,
  u3 as u2,
  l
};
//# sourceMappingURL=chunk-UOKTNY52.js.map
