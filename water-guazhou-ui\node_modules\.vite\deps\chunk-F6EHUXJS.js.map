{"version": 3, "sources": ["../../@arcgis/core/rest/query/executeForExtent.js", "../../@arcgis/core/rest/query/operations/pbfJSONFeatureSet.js", "../../@arcgis/core/rest/query/executeQueryPBF.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../geometry.js\";import{parseUrl as t}from\"../utils.js\";import{executeQueryForExtent as o}from\"./operations/query.js\";import r from\"../support/Query.js\";import e from\"../../geometry/Extent.js\";async function m(m,n,s){const p=t(m);return o(p,r.from(n),{...s}).then((t=>({count:t.data.count,extent:e.fromJSON(t.data.extent)})))}export{m as executeForExtent};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e,assertIsSome as t,unwrapOrThrow as r}from\"../../../core/maybe.js\";import{getGeometryZScaler as s}from\"../../../geometry/support/zscale.js\";import{unquantizeOptimizedGeometry as o,convertToGeometry as i}from\"../../../layers/graphics/featureConversionUtils.js\";function n(e,t){return t}function a(e,t,r,s){switch(r){case 0:return c(e,t+s,0);case 1:return\"lowerLeft\"===e.originPosition?c(e,t+s,1):l(e,t+s,1)}}function h(e,t,r,s){return 2===r?c(e,t,2):a(e,t,r,s)}function u(e,t,r,s){return 2===r?c(e,t,3):a(e,t,r,s)}function d(e,t,r,s){return 3===r?c(e,t,3):h(e,t,r,s)}function c({translate:e,scale:t},r,s){return e[s]+r*t[s]}function l({translate:e,scale:t},r,s){return e[s]-r*t[s]}class f{constructor(e){this._options=e,this.geometryTypes=[\"esriGeometryPoint\",\"esriGeometryMultipoint\",\"esriGeometryPolyline\",\"esriGeometryPolygon\"],this._previousCoordinate=[0,0],this._transform=null,this._applyTransform=n,this._lengths=[],this._currentLengthIndex=0,this._toAddInCurrentPath=0,this._vertexDimension=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,this._attributesConstructor=class{}}createFeatureResult(){return{fields:[],features:[]}}finishFeatureResult(t){if(this._options.applyTransform&&(t.transform=null),this._attributesConstructor=class{},this._coordinateBuffer=null,this._lengths.length=0,!t.hasZ)return;const r=s(t.geometryType,this._options.sourceSpatialReference,t.spatialReference);if(!e(r))for(const e of t.features)r(e.geometry)}createSpatialReference(){return{}}addField(e,r){const s=e.fields;t(s),s.push(r);const o=s.map((e=>e.name));this._attributesConstructor=function(){for(const e of o)this[e]=null}}addFeature(e,t){e.features.push(t)}prepareFeatures(e){switch(this._transform=e.transform,this._options.applyTransform&&e.transform&&(this._applyTransform=this._deriveApplyTransform(e)),this._vertexDimension=2,e.hasZ&&this._vertexDimension++,e.hasM&&this._vertexDimension++,e.geometryType){case\"esriGeometryPoint\":this.addCoordinate=(e,t,r)=>this.addCoordinatePoint(e,t,r),this.createGeometry=e=>this.createPointGeometry(e);break;case\"esriGeometryPolygon\":this.addCoordinate=(e,t,r)=>this._addCoordinatePolygon(e,t,r),this.createGeometry=e=>this._createPolygonGeometry(e);break;case\"esriGeometryPolyline\":this.addCoordinate=(e,t,r)=>this._addCoordinatePolyline(e,t,r),this.createGeometry=e=>this._createPolylineGeometry(e);break;case\"esriGeometryMultipoint\":this.addCoordinate=(e,t,r)=>this._addCoordinateMultipoint(e,t,r),this.createGeometry=e=>this._createMultipointGeometry(e)}}createFeature(){return this._lengths.length=0,this._currentLengthIndex=0,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,{attributes:new this._attributesConstructor}}allocateCoordinates(){}addLength(e,t,r){0===this._lengths.length&&(this._toAddInCurrentPath=t),this._lengths.push(t)}addQueryGeometry(e,t){const{queryGeometry:r,queryGeometryType:s}=t,n=o(r.clone(),r,!1,!1,this._transform),a=i(n,s,!1,!1);e.queryGeometryType=s,e.queryGeometry={...a}}createPointGeometry(e){const t={x:0,y:0,spatialReference:e.spatialReference};return e.hasZ&&(t.z=0),e.hasM&&(t.m=0),t}addCoordinatePoint(e,t,s){const o=r(this._transform,\"transform\");switch(t=this._applyTransform(o,t,s,0),s){case 0:e.x=t;break;case 1:e.y=t;break;case 2:\"z\"in e?e.z=t:e.m=t;break;case 3:e.m=t}}_transformPathLikeValue(e,t){let s=0;t<=1&&(s=this._previousCoordinate[t],this._previousCoordinate[t]+=e);const o=r(this._transform,\"transform\");return this._applyTransform(o,e,t,s)}_addCoordinatePolyline(e,t,r){this._dehydratedAddPointsCoordinate(e.paths,t,r)}_addCoordinatePolygon(e,t,r){this._dehydratedAddPointsCoordinate(e.rings,t,r)}_addCoordinateMultipoint(e,t,r){0===r&&e.points.push([]);const s=this._transformPathLikeValue(t,r);e.points[e.points.length-1].push(s)}_createPolygonGeometry(e){return{rings:[[]],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_createPolylineGeometry(e){return{paths:[[]],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_createMultipointGeometry(e){return{points:[],spatialReference:e.spatialReference,hasZ:!!e.hasZ,hasM:!!e.hasM}}_dehydratedAddPointsCoordinate(e,t,r){0===r&&0==this._toAddInCurrentPath--&&(e.push([]),this._toAddInCurrentPath=this._lengths[++this._currentLengthIndex]-1,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0);const s=this._transformPathLikeValue(t,r),o=e[e.length-1];0===r&&(this._coordinateBufferPtr=0,this._coordinateBuffer=new Array(this._vertexDimension),o.push(this._coordinateBuffer)),this._coordinateBuffer[this._coordinateBufferPtr++]=s}_deriveApplyTransform(e){const{hasZ:t,hasM:r}=e;return t&&r?d:t?h:r?u:a}}export{f as JSONFeatureSetParserContext};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{parseUrl as r}from\"../utils.js\";import{JSONFeatureSetParserContext as e}from\"./operations/pbfJSONFeatureSet.js\";import{executeQueryPBF as t}from\"./operations/query.js\";import o from\"../support/FeatureSet.js\";import a from\"../support/Query.js\";async function s(r,e,t){const s=await n(r,a.from(e),t);return o.fromJSON(s)}async function n(o,s,n){const p=r(o),i={...n},u=a.from(s),m=!u.quantizationParameters,{data:f}=await t(p,u,new e({sourceSpatialReference:u.sourceSpatialReference,applyTransform:m}),i);return f}export{s as executeQueryPBF,n as executeRawQueryPBF};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0M,eAAe,EAAEA,IAAEC,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAEF,EAAC;AAAE,SAAOG,GAAE,GAAE,EAAE,KAAKF,EAAC,GAAE,EAAC,GAAGC,GAAC,CAAC,EAAE,KAAM,CAAAE,QAAI,EAAC,OAAMA,GAAE,KAAK,OAAM,QAAO,EAAE,SAASA,GAAE,KAAK,MAAM,EAAC,EAAG;AAAC;;;ACAxD,SAAS,EAAE,GAAEC,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAEC,IAAE;AAAC,UAAO,GAAE;AAAA,IAAC,KAAK;AAAE,aAAOC,GAAE,GAAEF,KAAEC,IAAE,CAAC;AAAA,IAAE,KAAK;AAAE,aAAM,gBAAc,EAAE,iBAAeC,GAAE,GAAEF,KAAEC,IAAE,CAAC,IAAE,EAAE,GAAED,KAAEC,IAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAED,IAAE,GAAEC,IAAE;AAAC,SAAO,MAAI,IAAEC,GAAE,GAAEF,IAAE,CAAC,IAAE,EAAE,GAAEA,IAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,EAAE,GAAED,IAAE,GAAEC,IAAE;AAAC,SAAO,MAAI,IAAEC,GAAE,GAAEF,IAAE,CAAC,IAAE,EAAE,GAAEA,IAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,EAAE,GAAED,IAAE,GAAEC,IAAE;AAAC,SAAO,MAAI,IAAEC,GAAE,GAAEF,IAAE,CAAC,IAAE,EAAE,GAAEA,IAAE,GAAEC,EAAC;AAAC;AAAC,SAASC,GAAE,EAAC,WAAU,GAAE,OAAMF,GAAC,GAAE,GAAEC,IAAE;AAAC,SAAO,EAAEA,EAAC,IAAE,IAAED,GAAEC,EAAC;AAAC;AAAC,SAAS,EAAE,EAAC,WAAU,GAAE,OAAMD,GAAC,GAAE,GAAEC,IAAE;AAAC,SAAO,EAAEA,EAAC,IAAE,IAAED,GAAEC,EAAC;AAAC;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,YAAY,GAAE;AAAC,SAAK,WAAS,GAAE,KAAK,gBAAc,CAAC,qBAAoB,0BAAyB,wBAAuB,qBAAqB,GAAE,KAAK,sBAAoB,CAAC,GAAE,CAAC,GAAE,KAAK,aAAW,MAAK,KAAK,kBAAgB,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,sBAAoB,GAAE,KAAK,sBAAoB,GAAE,KAAK,mBAAiB,GAAE,KAAK,oBAAkB,MAAK,KAAK,uBAAqB,GAAE,KAAK,yBAAuB,MAAK;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,WAAM,EAAC,QAAO,CAAC,GAAE,UAAS,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBH,IAAE;AAAC,QAAG,KAAK,SAAS,mBAAiBA,GAAE,YAAU,OAAM,KAAK,yBAAuB,MAAK;AAAA,IAAC,GAAE,KAAK,oBAAkB,MAAK,KAAK,SAAS,SAAO,GAAE,CAACA,GAAE,KAAK;AAAO,UAAM,IAAE,EAAEA,GAAE,cAAa,KAAK,SAAS,wBAAuBA,GAAE,gBAAgB;AAAE,QAAG,CAAC,EAAE,CAAC,EAAE,YAAU,KAAKA,GAAE,SAAS,GAAE,EAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,WAAM,CAAC;AAAA,EAAC;AAAA,EAAC,SAAS,GAAE,GAAE;AAAC,UAAMC,KAAE,EAAE;AAAO,MAAEA,EAAC,GAAEA,GAAE,KAAK,CAAC;AAAE,UAAMG,KAAEH,GAAE,IAAK,CAAAI,OAAGA,GAAE,IAAK;AAAE,SAAK,yBAAuB,WAAU;AAAC,iBAAUA,MAAKD,GAAE,MAAKC,EAAC,IAAE;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,WAAW,GAAEL,IAAE;AAAC,MAAE,SAAS,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgB,GAAE;AAAC,YAAO,KAAK,aAAW,EAAE,WAAU,KAAK,SAAS,kBAAgB,EAAE,cAAY,KAAK,kBAAgB,KAAK,sBAAsB,CAAC,IAAG,KAAK,mBAAiB,GAAE,EAAE,QAAM,KAAK,oBAAmB,EAAE,QAAM,KAAK,oBAAmB,EAAE,cAAa;AAAA,MAAC,KAAI;AAAoB,aAAK,gBAAc,CAACK,IAAEL,IAAE,MAAI,KAAK,mBAAmBK,IAAEL,IAAE,CAAC,GAAE,KAAK,iBAAe,CAAAK,OAAG,KAAK,oBAAoBA,EAAC;AAAE;AAAA,MAAM,KAAI;AAAsB,aAAK,gBAAc,CAACA,IAAEL,IAAE,MAAI,KAAK,sBAAsBK,IAAEL,IAAE,CAAC,GAAE,KAAK,iBAAe,CAAAK,OAAG,KAAK,uBAAuBA,EAAC;AAAE;AAAA,MAAM,KAAI;AAAuB,aAAK,gBAAc,CAACA,IAAEL,IAAE,MAAI,KAAK,uBAAuBK,IAAEL,IAAE,CAAC,GAAE,KAAK,iBAAe,CAAAK,OAAG,KAAK,wBAAwBA,EAAC;AAAE;AAAA,MAAM,KAAI;AAAyB,aAAK,gBAAc,CAACA,IAAEL,IAAE,MAAI,KAAK,yBAAyBK,IAAEL,IAAE,CAAC,GAAE,KAAK,iBAAe,CAAAK,OAAG,KAAK,0BAA0BA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,KAAK,SAAS,SAAO,GAAE,KAAK,sBAAoB,GAAE,KAAK,oBAAoB,CAAC,IAAE,GAAE,KAAK,oBAAoB,CAAC,IAAE,GAAE,KAAK,oBAAkB,MAAK,KAAK,uBAAqB,GAAE,EAAC,YAAW,IAAI,KAAK,yBAAsB;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAA,EAAC;AAAA,EAAC,UAAU,GAAEL,IAAE,GAAE;AAAC,UAAI,KAAK,SAAS,WAAS,KAAK,sBAAoBA,KAAG,KAAK,SAAS,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiB,GAAEA,IAAE;AAAC,UAAK,EAAC,eAAc,GAAE,mBAAkBC,GAAC,IAAED,IAAEM,KAAE,GAAE,EAAE,MAAM,GAAE,GAAE,OAAG,OAAG,KAAK,UAAU,GAAEC,KAAE,GAAED,IAAEL,IAAE,OAAG,KAAE;AAAE,MAAE,oBAAkBA,IAAE,EAAE,gBAAc,EAAC,GAAGM,GAAC;AAAA,EAAC;AAAA,EAAC,oBAAoB,GAAE;AAAC,UAAMP,KAAE,EAAC,GAAE,GAAE,GAAE,GAAE,kBAAiB,EAAE,iBAAgB;AAAE,WAAO,EAAE,SAAOA,GAAE,IAAE,IAAG,EAAE,SAAOA,GAAE,IAAE,IAAGA;AAAA,EAAC;AAAA,EAAC,mBAAmB,GAAEA,IAAEC,IAAE;AAAC,UAAMG,KAAE,EAAE,KAAK,YAAW,WAAW;AAAE,YAAOJ,KAAE,KAAK,gBAAgBI,IAAEJ,IAAEC,IAAE,CAAC,GAAEA,IAAE;AAAA,MAAC,KAAK;AAAE,UAAE,IAAED;AAAE;AAAA,MAAM,KAAK;AAAE,UAAE,IAAEA;AAAE;AAAA,MAAM,KAAK;AAAE,eAAM,IAAE,EAAE,IAAEA,KAAE,EAAE,IAAEA;AAAE;AAAA,MAAM,KAAK;AAAE,UAAE,IAAEA;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,wBAAwB,GAAEA,IAAE;AAAC,QAAIC,KAAE;AAAE,IAAAD,MAAG,MAAIC,KAAE,KAAK,oBAAoBD,EAAC,GAAE,KAAK,oBAAoBA,EAAC,KAAG;AAAG,UAAMI,KAAE,EAAE,KAAK,YAAW,WAAW;AAAE,WAAO,KAAK,gBAAgBA,IAAE,GAAEJ,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuB,GAAED,IAAE,GAAE;AAAC,SAAK,+BAA+B,EAAE,OAAMA,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsB,GAAEA,IAAE,GAAE;AAAC,SAAK,+BAA+B,EAAE,OAAMA,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,yBAAyB,GAAEA,IAAE,GAAE;AAAC,UAAI,KAAG,EAAE,OAAO,KAAK,CAAC,CAAC;AAAE,UAAMC,KAAE,KAAK,wBAAwBD,IAAE,CAAC;AAAE,MAAE,OAAO,EAAE,OAAO,SAAO,CAAC,EAAE,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuB,GAAE;AAAC,WAAM,EAAC,OAAM,CAAC,CAAC,CAAC,GAAE,kBAAiB,EAAE,kBAAiB,MAAK,CAAC,CAAC,EAAE,MAAK,MAAK,CAAC,CAAC,EAAE,KAAI;AAAA,EAAC;AAAA,EAAC,wBAAwB,GAAE;AAAC,WAAM,EAAC,OAAM,CAAC,CAAC,CAAC,GAAE,kBAAiB,EAAE,kBAAiB,MAAK,CAAC,CAAC,EAAE,MAAK,MAAK,CAAC,CAAC,EAAE,KAAI;AAAA,EAAC;AAAA,EAAC,0BAA0B,GAAE;AAAC,WAAM,EAAC,QAAO,CAAC,GAAE,kBAAiB,EAAE,kBAAiB,MAAK,CAAC,CAAC,EAAE,MAAK,MAAK,CAAC,CAAC,EAAE,KAAI;AAAA,EAAC;AAAA,EAAC,+BAA+B,GAAED,IAAE,GAAE;AAAC,UAAI,KAAG,KAAG,KAAK,0BAAwB,EAAE,KAAK,CAAC,CAAC,GAAE,KAAK,sBAAoB,KAAK,SAAS,EAAE,KAAK,mBAAmB,IAAE,GAAE,KAAK,oBAAoB,CAAC,IAAE,GAAE,KAAK,oBAAoB,CAAC,IAAE;AAAG,UAAMC,KAAE,KAAK,wBAAwBD,IAAE,CAAC,GAAEI,KAAE,EAAE,EAAE,SAAO,CAAC;AAAE,UAAI,MAAI,KAAK,uBAAqB,GAAE,KAAK,oBAAkB,IAAI,MAAM,KAAK,gBAAgB,GAAEA,GAAE,KAAK,KAAK,iBAAiB,IAAG,KAAK,kBAAkB,KAAK,sBAAsB,IAAEH;AAAA,EAAC;AAAA,EAAC,sBAAsB,GAAE;AAAC,UAAK,EAAC,MAAKD,IAAE,MAAK,EAAC,IAAE;AAAE,WAAOA,MAAG,IAAE,IAAEA,KAAE,IAAE,IAAE,IAAE;AAAA,EAAC;AAAC;;;ACAl1I,eAAe,EAAE,GAAE,GAAEQ,IAAE;AAAC,QAAMC,KAAE,MAAMC,GAAE,GAAE,EAAE,KAAK,CAAC,GAAEF,EAAC;AAAE,SAAOG,GAAE,SAASF,EAAC;AAAC;AAAC,eAAeC,GAAEE,IAAEH,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAEE,EAAC,GAAEC,KAAE,EAAC,GAAGH,GAAC,GAAEI,KAAE,EAAE,KAAKL,EAAC,GAAEM,KAAE,CAACD,GAAE,wBAAuB,EAAC,MAAKE,GAAC,IAAE,MAAMA,GAAE,GAAEF,IAAE,IAAIE,GAAE,EAAC,wBAAuBF,GAAE,wBAAuB,gBAAeC,GAAC,CAAC,GAAEF,EAAC;AAAE,SAAOG;AAAC;", "names": ["m", "n", "s", "x", "t", "t", "s", "c", "f", "o", "e", "n", "a", "t", "s", "n", "x", "o", "i", "u", "m", "f"]}