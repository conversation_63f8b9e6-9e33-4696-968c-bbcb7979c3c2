{"version": 3, "sources": ["../../@arcgis/core/geometry/support/scaleUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getMetersPerUnitForSR as t,inchesPerMeter as n}from\"../../core/unitUtils.js\";const e=96;function i(i,r){const o=r||i.extent,c=i.width,d=t(o&&o.spatialReference);return o&&c?o.width/c*d*n*e:0}function r(i,r){return i/(t(r)*n*e)}function o(t){return t/(n*e)}function c(i,r){return i*(t(r)*n*e)}function d(t,n){const e=t.extent,i=t.width-(t.padding?t.padding.left+t.padding.right:0),o=r(n,e.spatialReference);return e.clone().expand(o*i/e.width)}export{d as getExtentForScale,r as getResolutionForScale,o as getResolutionInMetersForScale,i as getScale,c as getScaleForResolution};\n"], "mappings": ";;;;;;AAIoF,IAAM,IAAE;AAAG,SAAS,EAAEA,IAAEC,IAAE;AAAC,QAAM,IAAEA,MAAGD,GAAE,QAAO,IAAEA,GAAE,OAAME,KAAE,EAAE,KAAG,EAAE,gBAAgB;AAAE,SAAO,KAAG,IAAE,EAAE,QAAM,IAAEA,KAAE,IAAE,IAAE;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAE;AAAC,SAAOD,MAAG,EAAEC,EAAC,IAAE,IAAE;AAAE;AAAkE,SAAS,EAAE,GAAE,GAAE;AAAC,QAAME,KAAE,EAAE,QAAOC,KAAE,EAAE,SAAO,EAAE,UAAQ,EAAE,QAAQ,OAAK,EAAE,QAAQ,QAAM,IAAG,IAAE,EAAE,GAAED,GAAE,gBAAgB;AAAE,SAAOA,GAAE,MAAM,EAAE,OAAO,IAAEC,KAAED,GAAE,KAAK;AAAC;", "names": ["i", "r", "d", "e", "i"]}