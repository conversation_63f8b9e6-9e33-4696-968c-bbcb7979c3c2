import {
  o as o2
} from "./chunk-TBHJZ2TU.js";
import {
  r
} from "./chunk-PTI7U6FU.js";
import {
  U,
  w as w2
} from "./chunk-RURSJOSG.js";
import {
  p
} from "./chunk-PHEIXDVR.js";
import {
  o,
  y
} from "./chunk-SROTSYJS.js";
import {
  n as n3
} from "./chunk-FOE4ICAJ.js";
import {
  g
} from "./chunk-TLKX5XIJ.js";
import {
  d
} from "./chunk-ADTC77YB.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  y as y2
} from "./chunk-VX6YUKFM.js";
import {
  m as m2
} from "./chunk-6ILWLF72.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  e
} from "./chunk-G5KX4JSG.js";
import {
  n as n2
} from "./chunk-36FLFRUE.js";
import {
  n
} from "./chunk-2CM7MIII.js";

// node_modules/@arcgis/core/views/2d/interactive/SnappingVisualizer2D.js
var u = class extends r {
  constructor(e2) {
    super(), this._graphicsLayer = e2;
  }
  visualizeIntersectionPoint(e2, r2) {
    return this._visualizeSnappingIndicator(new w({ x: e2.intersectionPoint[0], y: e2.intersectionPoint[1], spatialReference: r2.spatialReference }), f);
  }
  visualizePoint(e2, r2) {
    return this._visualizeSnappingIndicator(new w({ x: e2.point[0], y: e2.point[1], spatialReference: r2.spatialReference }), I);
  }
  visualizeLine(e2, r2) {
    return this._visualizeSnappingIndicator(new m({ paths: [[e2.lineStart, e2.lineEnd]], spatialReference: r2.spatialReference }), P);
  }
  visualizeParallelSign(e2, r2) {
    return this._visualizeSnappingIndicator(new m({ paths: [[e2.lineStart, e2.lineEnd]], spatialReference: r2.spatialReference }), x);
  }
  visualizeRightAngleQuad(e2, r2) {
    return this._visualizeSnappingIndicator(new m({ paths: [[e2.previousVertex, e2.centerVertex, e2.nextVertex]], spatialReference: r2.spatialReference }), z(e2));
  }
  _visualizeSnappingIndicator(i, t) {
    const n4 = new g({ geometry: i, symbol: t });
    return this._graphicsLayer.add(n4), n(() => {
      this._graphicsLayer.remove(n4);
    });
  }
};
var M = o2.main.toArray();
var d2 = [...o2.main.toRgb(), 100];
var f = new y2({ outline: new m2({ width: 1.5, color: M }), size: 15, color: [0, 0, 0, 0] });
var I = new y2({ outline: { width: 0.5, color: [0, 0, 0, 1] }, size: 10, color: M });
var P = new d({ data: { type: "CIMSymbolReference", symbol: { type: "CIMLineSymbol", symbolLayers: [{ type: "CIMSolidStroke", enable: true, capStyle: U.Butt, joinStyle: w2.Round, miterLimit: 10, width: e(p.lineHintWidthTarget), color: M }] } } });
var x = new d({ data: { type: "CIMSymbolReference", symbol: { type: "CIMLineSymbol", symbolLayers: [{ type: "CIMVectorMarker", enable: true, anchorPoint: { x: 0, y: -1, z: 0 }, anchorPointUnits: "Relative", size: 5, markerPlacement: { type: "CIMMarkerPlacementOnLine", placePerPart: true, angleToLine: true, relativeTo: "LineMiddle" }, frame: { xmin: -5, ymin: -1.5, xmax: 5, ymax: 1.5 }, markerGraphics: [{ type: "CIMMarkerGraphic", geometry: { rings: [[[7, 0], [-7, 0], [-7, 1.5], [7, 1.5]]] }, symbol: { type: "CIMPolygonSymbol", symbolLayers: [{ type: "CIMSolidFill", enable: true, color: M }] } }], scaleSymbolsProportionally: true, respectFrame: true }, { type: "CIMVectorMarker", enable: true, anchorPoint: { x: 0, y: 1, z: 0 }, anchorPointUnits: "Relative", size: 5, markerPlacement: { type: "CIMMarkerPlacementOnLine", placePerPart: true, angleToLine: true, relativeTo: "LineMiddle" }, frame: { xmin: -5, ymin: -1.5, xmax: 5, ymax: 1.5 }, markerGraphics: [{ type: "CIMMarkerGraphic", geometry: { rings: [[[7, 0], [-7, 0], [-7, -1.5], [7, -1.5]]] }, symbol: { type: "CIMPolygonSymbol", symbolLayers: [{ type: "CIMSolidFill", enable: true, color: M }] } }], scaleSymbolsProportionally: true, respectFrame: true }] } } });
var v = (e2) => new d({ data: { type: "CIMSymbolReference", symbol: { type: "CIMLineSymbol", symbolLayers: [{ type: "CIMVectorMarker", enable: true, anchorPoint: { x: 0.5, y: 0.5, z: 0 }, anchorPointUnits: "Relative", size: e(p.rightAngleHintSize), rotation: e2, markerPlacement: { type: "CIMMarkerPlacementOnVertices", placePerPart: true, angleToLine: true, placeOnEndPoints: false }, frame: { xmin: -5, ymin: -5, xmax: 5, ymax: 5 }, markerGraphics: [{ type: "CIMMarkerGraphic", geometry: { paths: [[[5, -5], [-5, -5], [-5, 5], [5, 5], [5, -5]]] }, symbol: { type: "CIMLineSymbol", symbolLayers: [{ type: "CIMSolidStroke", enable: true, capStyle: "Butt", joinStyle: "Round", miterLimit: 10, width: e(p.rightAngleHintOutlineSize), color: M }, { type: "CIMSolidFill", enable: true, color: d2 }] } }], scaleSymbolsProportionally: true, respectFrame: true }] } } });
var C = v(45);
var L = v(225);
var z = (() => {
  const e2 = n3(), r2 = n3(), i = n2();
  return (o3) => (o(e2, o3.centerVertex, o3.previousVertex), o(r2, o3.nextVertex, o3.previousVertex), y(i, e2, r2), i[2] < 0 ? C : L);
})();

export {
  u
};
//# sourceMappingURL=chunk-P6WIKP33.js.map
