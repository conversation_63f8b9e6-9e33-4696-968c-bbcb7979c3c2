{"version": 3, "sources": ["../../@arcgis/core/layers/support/fieldProperties.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Logger.js\";import i from\"./Field.js\";import t from\"./FieldsIndex.js\";import{fixFields as r}from\"./fieldUtils.js\";function s(){return{fields:{type:[i],value:null},fieldsIndex:{readOnly:!0,get(){return new t(this.fields||[])}},outFields:{type:[String],json:{read:!1},set:function(e){this._userOutFields=e,this.notifyChange(\"outFields\")},get:function(){const i=this._userOutFields;if(!i||!i.length)return null;if(i.includes(\"*\"))return[\"*\"];if(!this.fields)return i;for(const t of i){const r=this.fieldsIndex?.has(t);r||e.getLogger(\"esri.layers.support.fieldProperties\").error(\"field-attributes-layer:invalid-field\",`Invalid field ${t} found in outFields`,{layer:this,outFields:i})}return r(this.fieldsIndex,i)}}}}export{s as defineFieldProperties};\n"], "mappings": ";;;;;;;;;;;;;;AAI0I,SAASA,KAAG;AAAC,SAAM,EAAC,QAAO,EAAC,MAAK,CAAC,CAAC,GAAE,OAAM,KAAI,GAAE,aAAY,EAAC,UAAS,MAAG,MAAK;AAAC,WAAO,IAAI,EAAE,KAAK,UAAQ,CAAC,CAAC;AAAA,EAAC,EAAC,GAAE,WAAU,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,MAAK,MAAE,GAAE,KAAI,SAAS,GAAE;AAAC,SAAK,iBAAe,GAAE,KAAK,aAAa,WAAW;AAAA,EAAC,GAAE,KAAI,WAAU;AAJtX;AAIuX,UAAM,IAAE,KAAK;AAAe,QAAG,CAAC,KAAG,CAAC,EAAE,OAAO,QAAO;AAAK,QAAG,EAAE,SAAS,GAAG,EAAE,QAAM,CAAC,GAAG;AAAE,QAAG,CAAC,KAAK,OAAO,QAAO;AAAE,eAAU,KAAK,GAAE;AAAC,YAAMC,MAAE,UAAK,gBAAL,mBAAkB,IAAI;AAAG,MAAAA,MAAG,EAAE,UAAU,qCAAqC,EAAE,MAAM,wCAAuC,iBAAiB,CAAC,uBAAsB,EAAC,OAAM,MAAK,WAAU,EAAC,CAAC;AAAA,IAAC;AAAC,WAAO,EAAE,KAAK,aAAY,CAAC;AAAA,EAAC,EAAC,EAAC;AAAC;", "names": ["s", "r"]}