import {
  o
} from "./chunk-PEEUPDEG.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s as s2
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-RV4I37UI.js";

// node_modules/@arcgis/core/rest/support/ClassBreaksDefinition.js
var a2 = new s2({ esriClassifyEqualInterval: "equal-interval", esriClassifyManual: "manual", esriClassifyNaturalBreaks: "natural-breaks", esriClassifyQuantile: "quantile", esriClassifyStandardDeviation: "standard-deviation", esriClassifyDefinedInterval: "defined-interval" });
var n = new s2({ esriNormalizeByLog: "log", esriNormalizeByPercentOfTotal: "percent-of-total", esriNormalizeByField: "field" });
var l2 = class extends l {
  constructor(e2) {
    super(e2), this.type = "class-breaks-definition", this.breakCount = null, this.classificationField = null, this.classificationMethod = null, this.normalizationField = null, this.normalizationType = null;
  }
  set standardDeviationInterval(e2) {
    "standard-deviation" === this.classificationMethod && this._set("standardDeviationInterval", e2);
  }
  set definedInterval(e2) {
    "defined-interval" === this.classificationMethod && this._set("definedInterval", e2);
  }
};
e([o({ classBreaksDef: "class-breaks-definition" })], l2.prototype, "type", void 0), e([y({ json: { write: true } })], l2.prototype, "breakCount", void 0), e([y({ json: { write: true } })], l2.prototype, "classificationField", void 0), e([y({ type: String, json: { read: a2.read, write: a2.write } })], l2.prototype, "classificationMethod", void 0), e([y({ json: { write: true } })], l2.prototype, "normalizationField", void 0), e([y({ json: { read: n.read, write: n.write } })], l2.prototype, "normalizationType", void 0), e([y({ value: null, json: { write: true } })], l2.prototype, "standardDeviationInterval", null), e([y({ value: null, json: { write: true } })], l2.prototype, "definedInterval", null), l2 = e([a("esri.rest.support.ClassBreaksDefinition")], l2);
var d = l2;

// node_modules/@arcgis/core/rest/support/generateRendererUtils.js
var t = s.getLogger("esri.rest.support.generateRendererUtils");
function l3(e2, t2) {
  return Number(e2.toFixed(t2));
}
function a3(e2) {
  const { normalizationTotal: t2 } = e2;
  return { classBreaks: u(e2), normalizationTotal: t2 };
}
function u(e2) {
  const t2 = e2.definition, { classificationMethod: n2, normalizationType: a4, definedInterval: u2 } = t2, i2 = t2.breakCount ?? 1, c2 = [];
  let b2 = e2.values;
  if (0 === b2.length) return [];
  b2 = b2.sort((e3, t3) => e3 - t3);
  const V = b2[0], p = b2[b2.length - 1];
  if ("equal-interval" === n2) if (b2.length >= i2) {
    const e3 = (p - V) / i2;
    let t3 = V;
    for (let n3 = 1; n3 < i2; n3++) {
      const u3 = l3(V + n3 * e3, 6);
      c2.push({ minValue: t3, maxValue: u3, label: s3(t3, u3, a4) }), t3 = u3;
    }
    c2.push({ minValue: t3, maxValue: p, label: s3(t3, p, a4) });
  } else b2.forEach((e3) => {
    c2.push({ minValue: e3, maxValue: e3, label: s3(e3, e3, a4) });
  });
  else if ("natural-breaks" === n2) {
    const t3 = o2(b2), n3 = e2.valueFrequency || t3.valueFrequency, u3 = r(t3.uniqueValues, n3, i2);
    let f2 = V;
    for (let e3 = 1; e3 < i2; e3++) if (t3.uniqueValues.length > e3) {
      const n4 = l3(t3.uniqueValues[u3[e3]], 6);
      c2.push({ minValue: f2, maxValue: n4, label: s3(f2, n4, a4) }), f2 = n4;
    }
    c2.push({ minValue: f2, maxValue: p, label: s3(f2, p, a4) });
  } else if ("quantile" === n2) if (b2.length >= i2 && V !== p) {
    let e3 = V, t3 = Math.ceil(b2.length / i2), l4 = 0;
    for (let n3 = 1; n3 < i2; n3++) {
      let u3 = t3 + l4 - 1;
      u3 > b2.length && (u3 = b2.length - 1), u3 < 0 && (u3 = 0), c2.push({ minValue: e3, maxValue: b2[u3], label: s3(e3, b2[u3], a4) }), e3 = b2[u3], l4 += t3, t3 = Math.ceil((b2.length - l4) / (i2 - n3));
    }
    c2.push({ minValue: e3, maxValue: p, label: s3(e3, p, a4) });
  } else {
    let e3 = -1;
    for (let t3 = 0; t3 < b2.length; t3++) {
      const l4 = b2[t3];
      l4 !== e3 && (e3 = l4, c2.push({ minValue: e3, maxValue: l4, label: s3(e3, l4, a4) }), e3 = l4);
    }
  }
  else if ("standard-deviation" === n2) {
    const e3 = h(b2), t3 = m(b2, e3);
    if (0 === t3) c2.push({ minValue: b2[0], maxValue: b2[0], label: s3(b2[0], b2[0], a4) });
    else {
      const n3 = f(V, p, i2, e3, t3) * t3;
      let u3 = 0, o3 = V;
      for (let t4 = i2; t4 >= 1; t4--) {
        const r3 = l3(e3 - (t4 - 0.5) * n3, 6);
        c2.push({ minValue: o3, maxValue: r3, label: s3(o3, r3, a4) }), o3 = r3, u3++;
      }
      let r2 = l3(e3 + 0.5 * n3, 6);
      c2.push({ minValue: o3, maxValue: r2, label: s3(o3, r2, a4) }), o3 = r2, u3++;
      for (let t4 = 1; t4 <= i2; t4++) r2 = u3 === 2 * i2 ? p : l3(e3 + (t4 + 0.5) * n3, 6), c2.push({ minValue: o3, maxValue: r2, label: s3(o3, r2, a4) }), o3 = r2, u3++;
    }
  } else if ("defined-interval" === n2) {
    if (!u2) return c2;
    const e3 = b2[0], t3 = b2[b2.length - 1], n3 = Math.ceil((t3 - e3) / u2);
    let o3 = e3;
    for (let r2 = 1; r2 < n3; r2++) {
      const t4 = l3(e3 + r2 * u2, 6);
      c2.push({ minValue: o3, maxValue: t4, label: s3(o3, t4, a4) }), o3 = t4;
    }
    c2.push({ minValue: o3, maxValue: t3, label: s3(o3, t3, a4) });
  }
  return c2;
}
function s3(e2, t2, l4) {
  let n2 = null;
  return n2 = e2 === t2 ? l4 && "percent-of-total" === l4 ? e2 + "%" : e2.toString() : l4 && "percent-of-total" === l4 ? e2 + "% - " + t2 + "%" : e2 + " - " + t2, n2;
}
function o2(e2) {
  const t2 = [], l4 = [];
  let n2 = Number.MIN_VALUE, a4 = 1, u2 = -1;
  for (let s4 = 0; s4 < e2.length; s4++) {
    const o3 = e2[s4];
    o3 === n2 ? (a4++, l4[u2] = a4) : null !== o3 && (t2.push(o3), n2 = o3, a4 = 1, l4.push(a4), u2++);
  }
  return { uniqueValues: t2, valueFrequency: l4 };
}
function r(e2, t2, l4) {
  const n2 = e2.length, a4 = [];
  l4 > n2 && (l4 = n2);
  for (let s4 = 0; s4 < l4; s4++) a4.push(Math.round(s4 * n2 / l4 - 1));
  a4.push(n2 - 1);
  let u2 = i(a4, e2, t2, l4);
  return c(u2.mean, u2.sdcm, a4, e2, t2, l4) && (u2 = i(a4, e2, t2, l4)), a4;
}
function i(e2, t2, l4, n2) {
  let a4 = [], u2 = [], s4 = [], o3 = 0;
  const r2 = [], i2 = [];
  for (let m2 = 0; m2 < n2; m2++) {
    const n3 = b(m2, e2, t2, l4);
    r2.push(n3.sbMean), i2.push(n3.sbSdcm), o3 += i2[m2];
  }
  let c2, f2 = o3, h2 = true;
  for (; h2 || o3 < f2; ) {
    h2 = false, a4 = [];
    for (let t3 = 0; t3 < n2; t3++) a4.push(e2[t3]);
    for (let l5 = 0; l5 < n2; l5++) for (let a5 = e2[l5] + 1; a5 <= e2[l5 + 1]; a5++) if (c2 = t2[a5], l5 > 0 && a5 !== e2[l5 + 1] && Math.abs(c2 - r2[l5]) > Math.abs(c2 - r2[l5 - 1])) e2[l5] = a5;
    else if (l5 < n2 - 1 && e2[l5] !== a5 - 1 && Math.abs(c2 - r2[l5]) > Math.abs(c2 - r2[l5 + 1])) {
      e2[l5 + 1] = a5 - 1;
      break;
    }
    f2 = o3, o3 = 0, u2 = [], s4 = [];
    for (let a5 = 0; a5 < n2; a5++) {
      u2.push(r2[a5]), s4.push(i2[a5]);
      const n3 = b(a5, e2, t2, l4);
      r2[a5] = n3.sbMean, i2[a5] = n3.sbSdcm, o3 += i2[a5];
    }
  }
  if (o3 > f2) {
    for (let t3 = 0; t3 < n2; t3++) e2[t3] = a4[t3], r2[t3] = u2[t3], i2[t3] = s4[t3];
    o3 = f2;
  }
  return { mean: r2, sdcm: i2 };
}
function c(e2, t2, l4, n2, a4, u2) {
  let s4 = 0, o3 = 0, r2 = 0, i2 = 0, c2 = true;
  for (let f2 = 0; f2 < 2 && c2; f2++) {
    0 === f2 && (c2 = false);
    for (let f3 = 0; f3 < u2 - 1; f3++) for (; l4[f3 + 1] + 1 !== l4[f3 + 2]; ) {
      l4[f3 + 1] = l4[f3 + 1] + 1;
      const u3 = b(f3, l4, n2, a4);
      r2 = u3.sbMean, s4 = u3.sbSdcm;
      const h2 = b(f3 + 1, l4, n2, a4);
      if (i2 = h2.sbMean, o3 = h2.sbSdcm, !(s4 + o3 < t2[f3] + t2[f3 + 1])) {
        l4[f3 + 1] = l4[f3 + 1] - 1;
        break;
      }
      t2[f3] = s4, t2[f3 + 1] = o3, e2[f3] = r2, e2[f3 + 1] = i2, c2 = true;
    }
    for (let f3 = u2 - 1; f3 > 0; f3--) for (; l4[f3] !== l4[f3 - 1] + 1; ) {
      l4[f3] = l4[f3] - 1;
      const u3 = b(f3 - 1, l4, n2, a4);
      r2 = u3.sbMean, s4 = u3.sbSdcm;
      const h2 = b(f3, l4, n2, a4);
      if (i2 = h2.sbMean, o3 = h2.sbSdcm, !(s4 + o3 < t2[f3 - 1] + t2[f3])) {
        l4[f3] = l4[f3] + 1;
        break;
      }
      t2[f3 - 1] = s4, t2[f3] = o3, e2[f3 - 1] = r2, e2[f3] = i2, c2 = true;
    }
  }
  return c2;
}
function f(e2, t2, l4, n2, a4) {
  let u2 = Math.max(n2 - e2, t2 - n2) / a4 / l4;
  return u2 = u2 >= 1 ? 1 : u2 >= 0.5 ? 0.5 : 0.25, u2;
}
function h(e2) {
  let t2 = 0;
  for (let l4 = 0; l4 < e2.length; l4++) t2 += e2[l4];
  return t2 /= e2.length, t2;
}
function m(e2, t2) {
  let l4 = 0;
  for (let n2 = 0; n2 < e2.length; n2++) {
    const a4 = e2[n2];
    l4 += (a4 - t2) * (a4 - t2);
  }
  l4 /= e2.length;
  return Math.sqrt(l4);
}
function b(e2, l4, n2, a4) {
  let u2 = 0, s4 = 0;
  for (let t2 = l4[e2] + 1; t2 <= l4[e2 + 1]; t2++) {
    const e3 = a4[t2];
    u2 += n2[t2] * e3, s4 += e3;
  }
  s4 <= 0 && t.warn("Exception in Natural Breaks calculation");
  const o3 = u2 / s4;
  let r2 = 0;
  for (let t2 = l4[e2] + 1; t2 <= l4[e2 + 1]; t2++) r2 += a4[t2] * (n2[t2] - o3) ** 2;
  return { sbMean: o3, sbSdcm: r2 };
}

export {
  d,
  a3 as a
};
//# sourceMappingURL=chunk-WJKHSSMC.js.map
