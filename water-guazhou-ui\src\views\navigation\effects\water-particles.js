/**
 * 水面粒子特效方案
 * 实现了动态水波纹效果和缓慢闪烁的光点效果
 */

export function initWaterParticlesEffect(canvasId) {
  let animationFrameId = null;
  let canvas, ctx;
  let lightPoints = []; // 存储光点位置

  const init = () => {
    canvas = document.getElementById(canvasId);
    if (!canvas) return;
    
    ctx = canvas.getContext('2d');
    
    // 设置canvas尺寸为窗口大小
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      // 重新初始化光点
      initLightPoints();
    };
    
    // 水波纹效果参数
    const waves = [];
    const waveCount = 3; // 多层波纹
    
    for (let i = 0; i < waveCount; i++) {
      waves.push({
        wavelength: 200 + i * 100, // 波长
        amplitude: 30 + i * 10, // 振幅
        speed: 0.01 + i * 0.005, // 速度
        phase: 0, // 相位
        color: `rgba(0, 150, 255, ${0.15 - i * 0.03})` // 蓝色，透明度降低
      });
    }
    
    // 初始化固定的光点
    const initLightPoints = () => {
      lightPoints = [];
      for (let i = 0; i < 50; i++) {
        lightPoints.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 3 + 1,
          opacity: Math.random() * 0.5,
          speed: Math.random() * 0.002 + 0.001 // 非常缓慢的闪烁速度
        });
      }
    };
    
    // 绘制水波纹
    const drawWaves = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      waves.forEach(wave => {
        // 更新相位
        wave.phase += wave.speed;
        if (wave.phase > Math.PI * 2) {
          wave.phase = 0;
        }
        
        // 绘制波浪，调整水面基准线位置到屏幕2/3处
        const baseY = canvas.height * 0.7; // 将水面基准线下移到屏幕70%的位置
        
        ctx.beginPath();
        ctx.moveTo(0, baseY);
        
        for (let x = 0; x < canvas.width; x += 5) {
          // 计算y值，使波浪从上到下移动，使用多重正弦函数增加复杂度
          const y = baseY + 
                   Math.sin(x / wave.wavelength + wave.phase) * wave.amplitude + 
                   Math.sin(x / (wave.wavelength * 0.7) + wave.phase * 1.3) * (wave.amplitude * 0.6) +
                   Math.sin(x / (wave.wavelength * 0.5) + wave.phase * 0.7) * (wave.amplitude * 0.3);
          
          ctx.lineTo(x, y);
        }
        
        // 闭合路径到底部
        ctx.lineTo(canvas.width, canvas.height);
        ctx.lineTo(0, canvas.height);
        ctx.closePath();
        
        // 填充渐变色
        const gradient = ctx.createLinearGradient(0, baseY, 0, canvas.height);
        gradient.addColorStop(0, wave.color);
        gradient.addColorStop(1, 'rgba(0, 50, 100, 0.05)');
        
        ctx.fillStyle = gradient;
        ctx.fill();
      });
      
      // 绘制光点效果 - 使用固定位置的光点，只改变透明度
      lightPoints.forEach(point => {
        // 只在水面以下的区域显示光点
        if (point.y > canvas.height * 0.7) {
          // 缓慢变化透明度
          point.opacity += Math.sin(Date.now() * point.speed) * 0.01;
          point.opacity = Math.max(0.1, Math.min(0.6, point.opacity)); // 限制透明度范围
          
          ctx.beginPath();
          ctx.arc(point.x, point.y, point.size, 0, Math.PI * 2);
          ctx.fillStyle = `rgba(255, 255, 255, ${point.opacity})`;
          ctx.fill();
        }
      });
      
      animationFrameId = requestAnimationFrame(drawWaves);
    };
    
    // 初始化
    resizeCanvas();
    initLightPoints();
    drawWaves();
    
    // 窗口大小变化时重置canvas
    window.addEventListener('resize', resizeCanvas);
    
    // 返回清理函数
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      window.removeEventListener('resize', resizeCanvas);
    };
  };

  // 初始化并返回清理函数
  return init();
} 