import {
  S,
  p
} from "./chunk-XZ2UVSB4.js";
import {
  f
} from "./chunk-XBS7QZIQ.js";
import {
  x
} from "./chunk-W3CLOCDX.js";

// node_modules/@arcgis/core/rest/query/executeForCount.js
async function n(n2, s2, m) {
  const p2 = f(n2);
  return S(p2, x.from(s2), { ...m }).then((o) => o.data.count);
}

// node_modules/@arcgis/core/rest/query/executeForIds.js
async function s(s2, e, m) {
  const n2 = f(s2);
  return p(n2, x.from(e), { ...m }).then((o) => o.data.objectIds);
}

export {
  n,
  s
};
//# sourceMappingURL=chunk-PX6TFO4X.js.map
