{"version": 3, "sources": ["../../@arcgis/core/rest/support/FindParameters.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import\"../../geometry.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import{ensureType as o}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";import i from\"../../geometry/SpatialReference.js\";let p=class extends t{constructor(e){super(e),this.contains=!0,this.dynamicLayerInfos=null,this.gdbVersion=null,this.geometryPrecision=null,this.layerDefinitions=null,this.layerIds=null,this.maxAllowableOffset=null,this.outSpatialReference=null,this.returnGeometry=!1,this.searchFields=null,this.searchText=null}};e([r({type:Boolean,json:{write:{enabled:!0,isRequired:!0}}})],p.prototype,\"contains\",void 0),e([r({type:[Object],json:{read:{source:\"dynamicLayers\"},write:{target:\"dynamicLayers\"}}})],p.prototype,\"dynamicLayerInfos\",void 0),e([r({type:String,json:{write:!0}})],p.prototype,\"gdbVersion\",void 0),e([r({type:Number,json:{write:!0}})],p.prototype,\"geometryPrecision\",void 0),e([r({type:[Object],json:{write:!0}})],p.prototype,\"layerDefinitions\",void 0),e([r({type:[Number],json:{write:!0}})],p.prototype,\"layerIds\",void 0),e([r({type:Number,json:{write:!0}})],p.prototype,\"maxAllowableOffset\",void 0),e([r({type:i,json:{read:{source:\"outSR\"},write:{target:\"outSR\"}}})],p.prototype,\"outSpatialReference\",void 0),e([r({type:Boolean,json:{write:{enabled:!0,isRequired:!0}}})],p.prototype,\"returnGeometry\",void 0),e([r({type:[String],json:{write:!0}})],p.prototype,\"searchFields\",void 0),e([r({type:String,json:{write:!0}})],p.prototype,\"searchText\",void 0),p=e([s(\"esri.rest.support.FindParameters\")],p),p.from=o(p);const n=p;export{n as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAImb,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,MAAG,KAAK,oBAAkB,MAAK,KAAK,aAAW,MAAK,KAAK,oBAAkB,MAAK,KAAK,mBAAiB,MAAK,KAAK,WAAS,MAAK,KAAK,qBAAmB,MAAK,KAAK,sBAAoB,MAAK,KAAK,iBAAe,OAAG,KAAK,eAAa,MAAK,KAAK,aAAW;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,gBAAe,GAAE,OAAM,EAAC,QAAO,gBAAe,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,QAAO,GAAE,OAAM,EAAC,QAAO,QAAO,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAE,CAAC,GAAE,EAAE,OAAK,EAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["e"]}