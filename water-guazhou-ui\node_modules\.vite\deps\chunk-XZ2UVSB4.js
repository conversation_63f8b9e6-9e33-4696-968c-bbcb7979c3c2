import {
  t
} from "./chunk-NSJUSNRV.js";
import {
  t as t2
} from "./chunk-YMY3DTA5.js";
import {
  v
} from "./chunk-Z2LHI3D7.js";
import {
  r as r2
} from "./chunk-2AZSZWPE.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  c
} from "./chunk-ZACBBT3Y.js";
import {
  L,
  V
} from "./chunk-U4SVMKOQ.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/rest/query/operations/query.js
var l = "Layer does not support extent calculation.";
function m(e, t3) {
  if (t3 && "extent" === e.type) return `${e.xmin},${e.ymin},${e.xmax},${e.ymax}`;
  if (t3 && "point" === e.type) return `${e.x},${e.y}`;
  const r3 = e.toJSON();
  return delete r3.spatialReference, JSON.stringify(r3);
}
function y(e, r3) {
  const n = e.geometry, o = e.toJSON();
  delete o.compactGeometryEnabled, delete o.defaultSpatialReferenceEnabled;
  const a = o;
  let s, u, l2;
  if (r(n) && (u = n.spatialReference, l2 = n.spatialReference.wkid || JSON.stringify(n.spatialReference), a.geometryType = c(n), a.geometry = m(n, e.compactGeometryEnabled), a.inSR = l2), o.groupByFieldsForStatistics && (a.groupByFieldsForStatistics = o.groupByFieldsForStatistics.join(",")), o.objectIds && (a.objectIds = o.objectIds.join(",")), o.orderByFields && (a.orderByFields = o.orderByFields.join(",")), !o.outFields || !o.returnDistinctValues && ((r3 == null ? void 0 : r3.returnCountOnly) || (r3 == null ? void 0 : r3.returnExtentOnly) || (r3 == null ? void 0 : r3.returnIdsOnly)) ? delete a.outFields : o.outFields.includes("*") ? a.outFields = "*" : a.outFields = o.outFields.join(","), o.outSR ? (a.outSR = o.outSR.wkid || JSON.stringify(o.outSR), s = e.outSpatialReference) : n && (o.returnGeometry || o.returnCentroid) && (a.outSR = a.inSR, s = u), o.returnGeometry && delete o.returnGeometry, o.outStatistics && (a.outStatistics = JSON.stringify(o.outStatistics)), o.fullText && (a.fullText = JSON.stringify(o.fullText)), o.pixelSize && (a.pixelSize = JSON.stringify(o.pixelSize)), o.quantizationParameters && (e.defaultSpatialReferenceEnabled && r(u) && r(e.quantizationParameters) && r(e.quantizationParameters.extent) && u.equals(e.quantizationParameters.extent.spatialReference) && delete o.quantizationParameters.extent.spatialReference, a.quantizationParameters = JSON.stringify(o.quantizationParameters)), o.parameterValues && (a.parameterValues = JSON.stringify(o.parameterValues)), o.rangeValues && (a.rangeValues = JSON.stringify(o.rangeValues)), o.dynamicDataSource && (a.layer = JSON.stringify({ source: o.dynamicDataSource }), delete o.dynamicDataSource), o.timeExtent) {
    const e2 = o.timeExtent, { start: t3, end: r4 } = e2;
    null == t3 && null == r4 || (a.time = t3 === r4 ? t3 : `${t3 ?? "null"},${r4 ?? "null"}`), delete o.timeExtent;
  }
  return e.defaultSpatialReferenceEnabled && r(u) && r(s) && u.equals(s) && (a.defaultSR = a.inSR, delete a.inSR, delete a.outSR), a;
}
async function c2(e, r3, n, i) {
  const o = r(r3.timeExtent) && r3.timeExtent.isEmpty ? { data: { features: [] } } : await E(e, r3, "json", i);
  return r2(r3, n, o.data), o;
}
async function f(e, r3, n, i) {
  if (r(r3.timeExtent) && r3.timeExtent.isEmpty) return { data: n.createFeatureResult() };
  const o = await d(e, r3, i), a = o;
  return a.data = t2(o.data, n), a;
}
function d(e, t3, r3) {
  return E(e, t3, "pbf", r3);
}
function p(e, r3, n) {
  return r(r3.timeExtent) && r3.timeExtent.isEmpty ? Promise.resolve({ data: { objectIds: [] } }) : E(e, r3, "json", n, { returnIdsOnly: true });
}
function S(e, r3, n) {
  return r(r3.timeExtent) && r3.timeExtent.isEmpty ? Promise.resolve({ data: { count: 0 } }) : E(e, r3, "json", n, { returnIdsOnly: true, returnCountOnly: true });
}
function x(e, r3, n) {
  return r(r3.timeExtent) && r3.timeExtent.isEmpty ? Promise.resolve({ data: { count: 0, extent: null } }) : E(e, r3, "json", n, { returnExtentOnly: true, returnCountOnly: true }).then((e2) => {
    const t3 = e2.data;
    if (t3.hasOwnProperty("extent")) return e2;
    if (t3.features) throw new Error(l);
    if (t3.hasOwnProperty("count")) throw new Error(l);
    return e2;
  });
}
function E(i, s, u, l2 = {}, m2 = {}) {
  const c3 = "string" == typeof i ? L(i) : i, f2 = s.geometry ? [s.geometry] : [];
  return l2.responseType = "pbf" === u ? "array-buffer" : "json", v(f2, null, l2).then((r3) => {
    const i2 = r3 && r3[0];
    r(i2) && ((s = s.clone()).geometry = i2);
    const o = t({ ...c3.query, f: u, ...m2, ...y(s, m2) });
    return U(V(c3.path, "query"), { ...l2, query: { ...o, ...l2.query } });
  });
}

export {
  c2 as c,
  f,
  d,
  p,
  S,
  x
};
//# sourceMappingURL=chunk-XZ2UVSB4.js.map
