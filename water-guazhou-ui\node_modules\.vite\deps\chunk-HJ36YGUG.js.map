{"version": 3, "sources": ["../../@arcgis/core/layers/support/kmlUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../config.js\";import{id as o}from\"../../kernel.js\";import t from\"../../PopupTemplate.js\";import r from\"../../request.js\";import{clone as s}from\"../../core/lang.js\";import{addQueryParameters as n}from\"../../core/urlUtils.js\";import i from\"../../geometry/SpatialReference.js\";import{create as f,NEGATIVE_INFINITY as a,expandWithAABB as l,equals as u}from\"../../geometry/support/aaBoundingBox.js\";import{getBoundsXYZ as p}from\"../../geometry/support/boundsUtils.js\";import{fromJSON as m}from\"../../renderers/support/jsonUtils.js\";import y from\"../../rest/support/FeatureSet.js\";const c={esriGeometryPoint:\"points\",esriGeometryPolyline:\"polylines\",esriGeometryPolygon:\"polygons\"};function d(e){const o=e.folders||[],t=o.slice(),r=new Map,n=new Map,i=new Map,f=new Map,a=new Map,l={esriGeometryPoint:n,esriGeometryPolyline:i,esriGeometryPolygon:f};(e.featureCollection&&e.featureCollection.layers||[]).forEach((e=>{const o=s(e);o.featureSet.features=[];const t=e.featureSet.geometryType;r.set(t,o);const a=e.layerDefinition.objectIdField;\"esriGeometryPoint\"===t?G(n,a,e.featureSet.features):\"esriGeometryPolyline\"===t?G(i,a,e.featureSet.features):\"esriGeometryPolygon\"===t&&G(f,a,e.featureSet.features)})),e.groundOverlays&&e.groundOverlays.forEach((e=>{a.set(e.id,e)})),o.forEach((o=>{o.networkLinkIds.forEach((r=>{const s=P(r,o.id,e.networkLinks);s&&t.push(s)}))})),t.forEach((e=>{if(e.featureInfos){e.points=s(r.get(\"esriGeometryPoint\")),e.polylines=s(r.get(\"esriGeometryPolyline\")),e.polygons=s(r.get(\"esriGeometryPolygon\")),e.mapImages=[];for(const o of e.featureInfos)switch(o.type){case\"esriGeometryPoint\":case\"esriGeometryPolyline\":case\"esriGeometryPolygon\":{const t=l[o.type].get(o.id);t&&e[c[o.type]].featureSet.features.push(t);break}case\"GroundOverlay\":{const t=a.get(o.id);t&&e.mapImages.push(t);break}}e.fullExtent=j([e])}}));const u=j(t);return{folders:o,sublayers:t,extent:u}}function g(t,s,i,f){const a=o&&o.findCredential(t);t=n(t,{token:a&&a.token});const l=e.kmlServiceUrl;return r(l,{query:{url:t,model:\"simple\",folders:\"\",refresh:0!==i||void 0,outSR:JSON.stringify(s)},responseType:\"json\",signal:f})}function S(e,o,t=null,r=[]){const s=[],n={},i=o.sublayers,f=o.folders.map((e=>e.id));return i.forEach((o=>{const i=new e;if(t?i.read(o,t):i.read(o),r.length&&f.includes(i.id)&&(i.visible=r.includes(i.id)),n[o.id]=i,null!=o.parentFolderId&&-1!==o.parentFolderId){const e=n[o.parentFolderId];e.sublayers||(e.sublayers=[]),e.sublayers?.unshift(i)}else s.unshift(i)})),s}function G(e,o,t){t.forEach((t=>{e.set(t.attributes[o],t)}))}function h(e,o){let t;return o.some((o=>o.id===e&&(t=o,!0))),t}function P(e,o,t){const r=h(e,t);return r&&(r.parentFolderId=o,r.networkLink=r),r}async function b(e){const o=y.fromJSON(e.featureSet).features,r=e.layerDefinition,s=m(r.drawingInfo.renderer),n=t.fromJSON(e.popupInfo),i=[];for(const t of o){const e=await s.getSymbolAsync(t);t.symbol=e,t.popupTemplate=n,t.visible=!0,i.push(t)}return i}function j(e){const o=f(a),t=f(a);for(const r of e){if(r.polygons&&r.polygons.featureSet&&r.polygons.featureSet.features)for(const e of r.polygons.featureSet.features)p(o,e.geometry),l(t,o);if(r.polylines&&r.polylines.featureSet&&r.polylines.featureSet.features)for(const e of r.polylines.featureSet.features)p(o,e.geometry),l(t,o);if(r.points&&r.points.featureSet&&r.points.featureSet.features)for(const e of r.points.featureSet.features)p(o,e.geometry),l(t,o);if(r.mapImages)for(const e of r.mapImages)p(o,e.extent),l(t,o)}return u(t,a)?void 0:{xmin:t[0],ymin:t[1],zmin:t[2],xmax:t[3],ymax:t[4],zmax:t[5],spatialReference:i.WGS84}}export{j as computeExtent,g as fetchService,b as getGraphics,d as parseKML,S as sublayersFromJSON};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIglB,IAAM,IAAE,EAAC,mBAAkB,UAAS,sBAAqB,aAAY,qBAAoB,WAAU;AAAE,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,WAAS,CAAC,GAAEA,KAAE,EAAE,MAAM,GAAEC,KAAE,oBAAI,OAAI,IAAE,oBAAI,OAAI,IAAE,oBAAI,OAAIC,KAAE,oBAAI,OAAIC,KAAE,oBAAI,OAAI,IAAE,EAAC,mBAAkB,GAAE,sBAAqB,GAAE,qBAAoBD,GAAC;AAAE,GAAC,EAAE,qBAAmB,EAAE,kBAAkB,UAAQ,CAAC,GAAG,QAAS,CAAAE,OAAG;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,IAAAC,GAAE,WAAW,WAAS,CAAC;AAAE,UAAML,KAAEI,GAAE,WAAW;AAAa,IAAAH,GAAE,IAAID,IAAEK,EAAC;AAAE,UAAMF,KAAEC,GAAE,gBAAgB;AAAc,4BAAsBJ,KAAE,EAAE,GAAEG,IAAEC,GAAE,WAAW,QAAQ,IAAE,2BAAyBJ,KAAE,EAAE,GAAEG,IAAEC,GAAE,WAAW,QAAQ,IAAE,0BAAwBJ,MAAG,EAAEE,IAAEC,IAAEC,GAAE,WAAW,QAAQ;AAAA,EAAC,CAAE,GAAE,EAAE,kBAAgB,EAAE,eAAe,QAAS,CAAAA,OAAG;AAAC,IAAAD,GAAE,IAAIC,GAAE,IAAGA,EAAC;AAAA,EAAC,CAAE,GAAE,EAAE,QAAS,CAAAC,OAAG;AAAC,IAAAA,GAAE,eAAe,QAAS,CAAAJ,OAAG;AAAC,YAAMK,KAAE,EAAEL,IAAEI,GAAE,IAAG,EAAE,YAAY;AAAE,MAAAC,MAAGN,GAAE,KAAKM,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE,GAAEN,GAAE,QAAS,CAAAI,OAAG;AAAC,QAAGA,GAAE,cAAa;AAAC,MAAAA,GAAE,SAAO,EAAEH,GAAE,IAAI,mBAAmB,CAAC,GAAEG,GAAE,YAAU,EAAEH,GAAE,IAAI,sBAAsB,CAAC,GAAEG,GAAE,WAAS,EAAEH,GAAE,IAAI,qBAAqB,CAAC,GAAEG,GAAE,YAAU,CAAC;AAAE,iBAAUC,MAAKD,GAAE,aAAa,SAAOC,GAAE,MAAK;AAAA,QAAC,KAAI;AAAA,QAAoB,KAAI;AAAA,QAAuB,KAAI,uBAAsB;AAAC,gBAAML,KAAE,EAAEK,GAAE,IAAI,EAAE,IAAIA,GAAE,EAAE;AAAE,UAAAL,MAAGI,GAAE,EAAEC,GAAE,IAAI,CAAC,EAAE,WAAW,SAAS,KAAKL,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,iBAAgB;AAAC,gBAAMA,KAAEG,GAAE,IAAIE,GAAE,EAAE;AAAE,UAAAL,MAAGI,GAAE,UAAU,KAAKJ,EAAC;AAAE;AAAA,QAAK;AAAA,MAAC;AAAC,MAAAI,GAAE,aAAW,EAAE,CAACA,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,CAAE;AAAE,QAAM,IAAE,EAAEJ,EAAC;AAAE,SAAM,EAAC,SAAQ,GAAE,WAAUA,IAAE,QAAO,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEM,IAAE,GAAEJ,IAAE;AAAC,QAAMC,KAAE,KAAG,EAAE,eAAeH,EAAC;AAAE,EAAAA,KAAE,GAAEA,IAAE,EAAC,OAAMG,MAAGA,GAAE,MAAK,CAAC;AAAE,QAAM,IAAE,EAAE;AAAc,SAAO,EAAE,GAAE,EAAC,OAAM,EAAC,KAAIH,IAAE,OAAM,UAAS,SAAQ,IAAG,SAAQ,MAAI,KAAG,QAAO,OAAM,KAAK,UAAUM,EAAC,EAAC,GAAE,cAAa,QAAO,QAAOJ,GAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEF,KAAE,MAAKC,KAAE,CAAC,GAAE;AAAC,QAAMK,KAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,WAAUJ,KAAE,EAAE,QAAQ,IAAK,CAAAE,OAAGA,GAAE,EAAG;AAAE,SAAO,EAAE,QAAS,CAAAC,OAAG;AAJhsE;AAIisE,UAAME,KAAE,IAAI;AAAE,QAAGP,KAAEO,GAAE,KAAKF,IAAEL,EAAC,IAAEO,GAAE,KAAKF,EAAC,GAAEJ,GAAE,UAAQC,GAAE,SAASK,GAAE,EAAE,MAAIA,GAAE,UAAQN,GAAE,SAASM,GAAE,EAAE,IAAG,EAAEF,GAAE,EAAE,IAAEE,IAAE,QAAMF,GAAE,kBAAgB,OAAKA,GAAE,gBAAe;AAAC,YAAMD,KAAE,EAAEC,GAAE,cAAc;AAAE,MAAAD,GAAE,cAAYA,GAAE,YAAU,CAAC,KAAG,KAAAA,GAAE,cAAF,mBAAa,QAAQG;AAAA,IAAE,MAAM,CAAAD,GAAE,QAAQC,EAAC;AAAA,EAAC,CAAE,GAAED;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEN,IAAE;AAAC,EAAAA,GAAE,QAAS,CAAAA,OAAG;AAAC,MAAE,IAAIA,GAAE,WAAW,CAAC,GAAEA,EAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAIA;AAAE,SAAO,EAAE,KAAM,CAAAK,OAAGA,GAAE,OAAK,MAAIL,KAAEK,IAAE,KAAI,GAAEL;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE;AAAC,QAAMC,KAAE,EAAE,GAAED,EAAC;AAAE,SAAOC,OAAIA,GAAE,iBAAe,GAAEA,GAAE,cAAYA,KAAGA;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,SAAS,EAAE,UAAU,EAAE,UAASA,KAAE,EAAE,iBAAgBK,KAAE,EAAEL,GAAE,YAAY,QAAQ,GAAE,IAAEO,GAAE,SAAS,EAAE,SAAS,GAAE,IAAE,CAAC;AAAE,aAAUR,MAAK,GAAE;AAAC,UAAMI,KAAE,MAAME,GAAE,eAAeN,EAAC;AAAE,IAAAA,GAAE,SAAOI,IAAEJ,GAAE,gBAAc,GAAEA,GAAE,UAAQ,MAAG,EAAE,KAAKA,EAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,CAAC,GAAEA,KAAE,EAAE,CAAC;AAAE,aAAUC,MAAK,GAAE;AAAC,QAAGA,GAAE,YAAUA,GAAE,SAAS,cAAYA,GAAE,SAAS,WAAW,SAAS,YAAUG,MAAKH,GAAE,SAAS,WAAW,SAAS,CAAAC,GAAE,GAAEE,GAAE,QAAQ,GAAEF,GAAEF,IAAE,CAAC;AAAE,QAAGC,GAAE,aAAWA,GAAE,UAAU,cAAYA,GAAE,UAAU,WAAW,SAAS,YAAUG,MAAKH,GAAE,UAAU,WAAW,SAAS,CAAAC,GAAE,GAAEE,GAAE,QAAQ,GAAEF,GAAEF,IAAE,CAAC;AAAE,QAAGC,GAAE,UAAQA,GAAE,OAAO,cAAYA,GAAE,OAAO,WAAW,SAAS,YAAUG,MAAKH,GAAE,OAAO,WAAW,SAAS,CAAAC,GAAE,GAAEE,GAAE,QAAQ,GAAEF,GAAEF,IAAE,CAAC;AAAE,QAAGC,GAAE,UAAU,YAAUG,MAAKH,GAAE,UAAU,CAAAC,GAAE,GAAEE,GAAE,MAAM,GAAEF,GAAEF,IAAE,CAAC;AAAA,EAAC;AAAC,SAAO,EAAEA,IAAE,CAAC,IAAE,SAAO,EAAC,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,kBAAiB,EAAE,MAAK;AAAC;", "names": ["t", "r", "f", "a", "e", "o", "s", "i", "k"]}