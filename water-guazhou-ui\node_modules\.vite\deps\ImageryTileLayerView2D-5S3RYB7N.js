import {
  h as h6,
  m as m3,
  y as y3
} from "./chunk-5J6PSRTB.js";
import {
  A,
  E as E3,
  O,
  T as T2,
  _,
  c,
  d as d2,
  g as g3,
  h as h7,
  l as l3,
  p
} from "./chunk-ZV2P3YGB.js";
import {
  i as i2
} from "./chunk-JTTSDQPH.js";
import {
  r as r6
} from "./chunk-6DXPU43Z.js";
import {
  n,
  t as t3
} from "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-53FPJYCC.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import {
  a as a2,
  f as f2,
  g,
  u as u2
} from "./chunk-3MY6MF4F.js";
import {
  J,
  U as U3,
  ie,
  v
} from "./chunk-HK26M5VD.js";
import {
  x as x2
} from "./chunk-6G2NLXT7.js";
import {
  m as m2
} from "./chunk-JI2BFAR3.js";
import {
  h as h5,
  j as j3,
  m
} from "./chunk-Z27XYQOC.js";
import {
  s as s4
} from "./chunk-6NIKJYUX.js";
import "./chunk-IEBU4QQL.js";
import {
  i as i3
} from "./chunk-5JDQNIY4.js";
import {
  f as f3
} from "./chunk-NEPFZ7PM.js";
import {
  u as u3
} from "./chunk-HWB4LNSZ.js";
import {
  r as r7
} from "./chunk-JSZR3BUH.js";
import {
  r as r5
} from "./chunk-QKWIBVLD.js";
import {
  T
} from "./chunk-WAPZ634R.js";
import {
  E as E2
} from "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import {
  o
} from "./chunk-RRNRSHX3.js";
import {
  D,
  G,
  I,
  L,
  M,
  P,
  R,
  U as U2,
  V,
  Y
} from "./chunk-4M3AMTD4.js";
import "./chunk-YACF4WM5.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-YDRLAXYR.js";
import {
  l as l2
} from "./chunk-WZNPTIYX.js";
import "./chunk-22FAZXOH.js";
import {
  mt
} from "./chunk-T7HWQQFI.js";
import "./chunk-DFGMRI52.js";
import {
  r as r3
} from "./chunk-OZZFNS32.js";
import {
  y as y2
} from "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import {
  h as h4
} from "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import {
  t as t2
} from "./chunk-DUEDINK5.js";
import {
  e as e3
} from "./chunk-MZ267CZB.js";
import {
  r as r4
} from "./chunk-QCTKOQ44.js";
import {
  M as M2,
  b,
  h as h3,
  i,
  r as r2,
  s as s3
} from "./chunk-ST2RRB55.js";
import "./chunk-THUK4WUF.js";
import "./chunk-5ZZCQR67.js";
import {
  j as j2
} from "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-RURSJOSG.js";
import "./chunk-OYGWWPGZ.js";
import "./chunk-77E52HT5.js";
import "./chunk-SROTSYJS.js";
import {
  f
} from "./chunk-FOE4ICAJ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import {
  d
} from "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-2ILOD42U.js";
import {
  b as b2
} from "./chunk-VJW7RCN7.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  U,
  h as h2,
  l,
  w as w3
} from "./chunk-QUHG7NMD.js";
import "./chunk-3WEGNHPY.js";
import {
  g as g2
} from "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import {
  u
} from "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  E,
  j,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e as e2,
  h,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/engine/imagery/RasterBitmap.js
var c2 = { bandCount: 3, outMin: 0, outMax: 1, minCutOff: [0, 0, 0], maxCutOff: [255, 255, 255], factor: [1 / 255, 1 / 255, 1 / 255], useGamma: false, gamma: [1, 1, 1], gammaCorrection: [1, 1, 1], colormap: null, colormapOffset: null, stretchType: "none", type: "stretch" };
var m4 = class extends r5 {
  constructor(e14 = null, t7 = null, s16 = null) {
    super(), this._textureInvalidated = true, this._colormapTextureInvalidated = true, this._rasterTexture = null, this._rasterTextureBandIds = null, this._transformGridTexture = null, this._colormapTexture = null, this._colormap = null, this._supportsBilinearTexture = true, this._processedTexture = null, this.functionTextures = [], this.projected = false, this.stencilRef = 0, this.coordScale = [1, 1], this._processed = false, this._symbolizerParameters = null, this.height = null, this.isRendereredSource = false, this.pixelRatio = 1, this.resolution = 0, this.rotation = 0, this._source = null, this.rawPixelData = null, this._suspended = false, this._bandIds = null, this._interpolation = null, this._transformGrid = null, this.width = null, this.x = 0, this.y = 0, this.source = e14, this.transformGrid = t7, this.interpolation = s16;
  }
  destroy() {
    this._disposeTextures();
  }
  get processedTexture() {
    return this._processedTexture;
  }
  set processedTexture(e14) {
    this._processedTexture !== e14 && (this._disposeTextures(true), this._processedTexture = e14);
  }
  get rasterTexture() {
    return this._rasterTexture;
  }
  set rasterTexture(e14) {
    var _a;
    this._rasterTexture !== e14 && ((_a = this._rasterTexture) == null ? void 0 : _a.dispose(), this._rasterTexture = e14);
  }
  get processed() {
    return this._processed;
  }
  set processed(t7) {
    this._processed = t7, t7 || (h(this.processedTexture), this.invalidateTexture());
  }
  get symbolizerParameters() {
    return this._symbolizerParameters || c2;
  }
  set symbolizerParameters(e14) {
    this._symbolizerParameters !== e14 && (this._symbolizerParameters = e14, this._colormapTextureInvalidated = true, this.commonUniforms = null);
  }
  get source() {
    return this._source;
  }
  set source(e14) {
    this._source !== e14 && (this._source = e14, this._rasterTexture && (this._rasterTexture.dispose(), this._rasterTexture = null, this._rasterTextureBandIds = null), this.projected = false, this.invalidateTexture());
  }
  get suspended() {
    return this._suspended;
  }
  set suspended(e14) {
    this._suspended && !e14 && this.stage && (this.ready(), this.requestRender()), this._suspended = e14;
  }
  get bandIds() {
    return this._bandIds;
  }
  set bandIds(e14) {
    this._bandIds = e14, this._isBandIdschanged(e14) && (this.projected = false, this.invalidateTexture());
  }
  get interpolation() {
    return this._interpolation || "nearest";
  }
  set interpolation(e14) {
    this._interpolation = e14, this._rasterTexture && this._rasterTexture.setSamplingMode("bilinear" === this._getTextureSamplingMethod(e14 || "nearest") ? L.LINEAR : L.NEAREST);
  }
  get transformGrid() {
    return this._transformGrid;
  }
  set transformGrid(t7) {
    this._transformGrid = t7, this._transformGridTexture = h(this._transformGridTexture);
  }
  invalidateTexture() {
    this._textureInvalidated || (this._textureInvalidated = true, this.requestRender());
  }
  _createTransforms() {
    return { dvs: e3() };
  }
  setTransform(e14) {
    const t7 = r2(this.transforms.dvs), [u5, n16] = e14.toScreenNoRotation([0, 0], [this.x, this.y]), l6 = this.resolution / this.pixelRatio / e14.resolution, d3 = l6 * this.width, p2 = l6 * this.height, _2 = Math.PI * this.rotation / 180;
    M2(t7, t7, t2(u5, n16)), M2(t7, t7, t2(d3 / 2, p2 / 2)), h3(t7, t7, -_2), M2(t7, t7, t2(-d3 / 2, -p2 / 2)), b(t7, t7, t2(d3, p2)), i(this.transforms.dvs, e14.displayViewMat3, t7);
  }
  getTextures({ forProcessing: e14 = false, useProcessedTexture: t7 = false } = {}) {
    const s16 = t7 ? this._processedTexture ?? this._rasterTexture : this._rasterTexture, r13 = [], i11 = [];
    return s16 ? t7 ? (i11.push(s16), r13.push("u_image"), this._colormapTexture && (i11.push(this._colormapTexture), r13.push("u_colormap")), { names: r13, textures: i11 }) : (this._transformGridTexture && (i11.push(this._transformGridTexture), r13.push("u_transformGrid")), i11.push(s16), r13.push("u_image"), this._colormapTexture && !e14 && (i11.push(this._colormapTexture), r13.push("u_colormap")), { names: r13, textures: i11 }) : { names: r13, textures: i11 };
  }
  onAttach() {
    this.invalidateTexture();
  }
  onDetach() {
    this.invalidateTexture();
  }
  updateTexture({ context: e14 }) {
    if (!this.stage) return void this._disposeTextures();
    const t7 = this._isValidSource(this.source);
    t7 && this._colormapTextureInvalidated && (this._colormapTextureInvalidated = false, this._updateColormapTexture(e14)), this._textureInvalidated && (this._textureInvalidated = false, this._createOrDestroyRasterTexture(e14), this._rasterTexture && (t7 ? this.transformGrid && !this._transformGridTexture && (this._transformGridTexture = l3(e14, this.transformGrid)) : this._rasterTexture.setData(null)), this.suspended || (this.ready(), this.requestRender()));
  }
  updateProcessedTexture() {
    const { functionTextures: e14 } = this;
    0 !== e14.length && (this.processedTexture = e14.shift(), e14.forEach((e15) => e15 == null ? void 0 : e15.dispose()), e14.length = 0);
  }
  _createOrDestroyRasterTexture(e14) {
    var _a, _b;
    const s16 = r(this.source) ? h5(this.source, this.bandIds) : null;
    if (!this._isValidSource(s16)) return void (this._rasterTexture && (this._rasterTexture.dispose(), this._rasterTextureBandIds = null, this._rasterTexture = null));
    const r13 = !this._isBandIdschanged(this.bandIds);
    if (this._rasterTexture) {
      if (r13) return;
      this._rasterTexture.dispose(), this._rasterTextureBandIds = null, this._rasterTexture = null;
    }
    this._supportsBilinearTexture = !!((_a = e14.capabilities.textureFloat) == null ? void 0 : _a.textureFloatLinear);
    const i11 = this._getTextureSamplingMethod(this.interpolation), o12 = this.isRendereredSource || !((_b = e14.capabilities.textureFloat) == null ? void 0 : _b.textureFloat);
    this._rasterTexture = c(e14, s16, i11, o12), this.projected = false, this._processed = false, this._rasterTextureBandIds = this.bandIds ? [...this.bandIds] : null;
  }
  _isBandIdschanged(e14) {
    const t7 = this._rasterTextureBandIds;
    return !(null == t7 && null == e14 || t7 && e14 && t7.join("") === e14.join(""));
  }
  _isValidSource(e14) {
    var _a;
    return r(e14) && ((_a = e14.pixels) == null ? void 0 : _a.length) > 0;
  }
  _getTextureSamplingMethod(e14) {
    const { type: s16, colormap: r13 } = this.symbolizerParameters, i11 = "lut" === s16 || "stretch" === s16 && r(r13);
    return !this._supportsBilinearTexture || i11 || "bilinear" !== e14 && "cubic" !== e14 ? "nearest" : "bilinear";
  }
  _updateColormapTexture(e14) {
    const t7 = this._colormap, s16 = this.symbolizerParameters.colormap;
    return s16 ? t7 ? s16.length !== t7.length || s16.some((e15, s17) => e15 !== t7[s17]) ? (this._colormapTexture && (this._colormapTexture.dispose(), this._colormapTexture = null), this._colormapTexture = _(e14, s16), void (this._colormap = s16)) : void 0 : (this._colormapTexture = _(e14, s16), void (this._colormap = s16)) : (this._colormapTexture && (this._colormapTexture.dispose(), this._colormapTexture = null), void (this._colormap = null));
  }
  _disposeTextures(e14 = false) {
    this._transformGridTexture && (this._transformGridTexture.dispose(), this._transformGridTexture = null), !e14 && this._colormapTexture && (this._colormapTexture.dispose(), this._colormapTexture = null, this._colormap = null, this._colormapTextureInvalidated = true), !e14 && this._rasterTexture && (this._rasterTexture.dispose(), this._rasterTexture = null, this._rasterTextureBandIds = null), this._processedTexture && (this._processedTexture.dispose(), this._processedTexture = null);
  }
};
function f4(e14) {
  return r(e14.source);
}

// node_modules/@arcgis/core/views/2d/engine/imagery/colorizer/utils.js
function i4(i11) {
  const n16 = [];
  return i11 && (n16.push("applyProjection"), 1 === i11.spacing[0] && n16.push("lookupProjection")), n16;
}
function n2(i11, n16, e14) {
  var _a;
  const t7 = !((_a = e14.capabilities.textureFloat) == null ? void 0 : _a.textureFloatLinear), u5 = [];
  return "cubic" === i11 ? u5.push("bicubic") : "bilinear" === i11 && (n16 ? (u5.push("bilinear"), u5.push("nnedge")) : t7 && u5.push("bilinear")), u5;
}

// node_modules/@arcgis/core/views/2d/engine/imagery/colorizer/lut.js
var s5 = { vsPath: "raster/common", fsPath: "raster/lut", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function a3(t7, e14, o12) {
  const a13 = o12 ? [] : i4(e14.transformGrid);
  return { defines: a13, program: t7.painter.materialManager.getProgram(s5, a13) };
}
function n3(r13, s16, a13, n16, m8 = false) {
  const { names: i11, textures: c5 } = a13.getTextures({ useProcessedTexture: m8 });
  d2(r13.context, s16, i11, c5), O(s16, n16, a13.commonUniforms), s16.setUniformMatrix3fv("u_dvsMat3", a13.transforms.dvs);
  const { colormap: f7, colormapOffset: u5 } = a13.symbolizerParameters, d3 = g3(f7, u5);
  O(s16, n16, d3);
}
var m5 = { createProgram: a3, bindTextureAndUniforms: n3 };

// node_modules/@arcgis/core/views/2d/engine/imagery/colorizer/shadedrelief.js
var n4 = { vsPath: "raster/common", fsPath: "raster/hillshade", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function m6(o12, e14, s16) {
  const { colormap: a13 } = e14.symbolizerParameters, m8 = [...s16 ? [] : i4(e14.transformGrid), ...n2(e14.interpolation, null != a13, o12.context)];
  null != a13 && m8.push("applyColormap");
  return { defines: m8, program: o12.painter.materialManager.getProgram(n4, m8) };
}
function i5(r13, t7, n16, m8, i11 = false) {
  const { names: l6, textures: c5 } = n16.getTextures({ useProcessedTexture: i11 });
  d2(r13.context, t7, l6, c5), O(t7, m8, n16.commonUniforms), t7.setUniformMatrix3fv("u_dvsMat3", n16.transforms.dvs);
  const f7 = n16.symbolizerParameters, { colormap: u5, colormapOffset: p2 } = f7;
  if (null != u5) {
    const r14 = g3(u5, p2);
    O(t7, m8, r14);
  }
  const d3 = A(f7);
  O(t7, m8, d3);
}
var l4 = { createProgram: m6, bindTextureAndUniforms: i5 };

// node_modules/@arcgis/core/views/2d/engine/imagery/colorizer/stretch.js
var a4 = { vsPath: "raster/common", fsPath: "raster/stretch", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function m7(e14, o12, s16) {
  const { colormap: n16 } = o12.symbolizerParameters, m8 = [...s16 ? [] : i4(o12.transformGrid), ...n2(o12.interpolation, null != n16, e14.context)];
  o12.isRendereredSource && !s16 ? m8.push("noop") : null != n16 && m8.push("applyColormap");
  return { defines: m8, program: e14.painter.materialManager.getProgram(a4, m8) };
}
function i6(r13, t7, a13, m8, i11 = false) {
  const { names: c5, textures: l6 } = a13.getTextures({ useProcessedTexture: i11 });
  d2(r13.context, t7, c5, l6), O(t7, m8, a13.commonUniforms), t7.setUniformMatrix3fv("u_dvsMat3", a13.transforms.dvs);
  const u5 = a13.symbolizerParameters, { colormap: p2, colormapOffset: f7 } = u5;
  if (null != p2) {
    const r14 = g3(p2, f7);
    O(t7, m8, r14);
  }
  const d3 = E3(u5);
  O(t7, m8, d3);
}
var c3 = { createProgram: m7, bindTextureAndUniforms: i6 };

// node_modules/@arcgis/core/views/2d/engine/imagery/colorizer/rasterColorizer.js
var s6 = /* @__PURE__ */ new Map();
function o2(t7) {
  return s6.get(t7);
}
s6.set("lut", m5), s6.set("hillshade", l4), s6.set("stretch", c3);

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/utils.js
var t4 = [1, 1];
var n5 = [2, 0, 0, 0, 2, 0, -1, -1, 0];
function a5(t7, n16, a13) {
  const { context: r13, rasterFunction: s16, hasBranches: i11 } = t7, { raster: o12 } = s16.parameters, f7 = i11 ? (o12 == null ? void 0 : o12.id) ?? -1 : 0, m8 = a13.functionTextures[f7] ?? a13.rasterTexture;
  d2(r13, n16, ["u_image"], [m8]);
}
function r8(t7, n16, r13) {
  const { rasters: s16 } = t7.rasterFunction.parameters;
  if (!s16) return;
  if (s16.length < 2) return a5(t7, n16, r13);
  const i11 = s16.filter((e14) => "Constant" !== e14.name).map((e14) => null != e14.id && "Identity" !== e14.name ? r13.functionTextures[e14.id] : r13.rasterTexture);
  if (d2(t7.context, n16, ["u_image", "u_image1", "u_image2"].slice(0, i11.length), i11), i11.length !== s16.length) {
    if (2 === s16.length) {
      const e14 = s16.findIndex((e15) => "Constant" === e15.name), t8 = 0 === e14 ? [0, 1, 0, 1, 0, 0, 0, 0, 0] : [1, 0, 0, 0, 1, 0, 0, 0, 0], { value: a13 } = s16[e14].parameters;
      n16.setUniform1f("u_image1Const", a13), n16.setUniformMatrix3fv("u_imageSwap", t8);
    } else if (3 === s16.length) {
      const e14 = [];
      if (s16.forEach((t8, n17) => "Constant" === t8.name && e14.push(n17)), 1 === e14.length) {
        const { value: t8 } = s16[e14[0]].parameters;
        n16.setUniform1f("u_image1Const", t8);
        const a13 = 0 === e14[0] ? [0, 1, 0, 0, 0, 1, 1, 0, 0] : 1 === e14[0] ? [1, 0, 0, 0, 0, 1, 0, 1, 0] : [1, 0, 0, 0, 1, 0, 0, 0, 1];
        n16.setUniformMatrix3fv("u_imageSwap", a13);
      } else if (2 === e14.length) {
        const { value: t8 } = s16[e14[0]].parameters;
        n16.setUniform1f("u_image1Const", t8);
        const { value: a13 } = s16[e14[1]].parameters;
        n16.setUniform1f("u_image2Const", a13);
        const r14 = s16.findIndex((e15) => "Constant" !== e15.name), i12 = 0 === r14 ? [1, 0, 0, 0, 1, 0, 0, 0, 1] : 1 === r14 ? [0, 1, 0, 1, 0, 0, 0, 0, 1] : [0, 0, 1, 1, 0, 0, 0, 1, 0];
        n16.setUniformMatrix3fv("u_imageSwap", i12);
      }
    }
  }
}
function s7(e14) {
  e14.setUniform2fv("u_coordScale", t4), e14.setUniformMatrix3fv("u_dvsMat3", n5);
}

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/aspectProcessor.js
var e4 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/aspect", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function a6(t7, r13) {
  return t7.painter.materialManager.getProgram(e4, []);
}
function o3(e14, a13, o12) {
  a5(e14, a13, o12), s7(a13);
  const { width: s16, height: i11, resolution: n16 } = o12;
  a13.setUniform2fv("u_srcImageSize", [s16, i11]), a13.setUniform2fv("u_cellSize", [n16, n16]);
}
var s8 = { createProgram: a6, bindTextureAndUniforms: o3 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/bandArithmeticProcessor.js
var a7 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/bandarithmetic", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function e5(t7, r13) {
  const { painter: e14, rasterFunction: n16 } = t7, { indexType: s16 } = n16.parameters;
  return e14.materialManager.getProgram(a7, [s16]);
}
function n6(a13, e14, n16) {
  a5(a13, e14, n16), s7(e14);
  const { bandIndexMat3: s16 } = a13.rasterFunction.parameters;
  e14.setUniformMatrix3fv("u_bandIndexMat3", s16);
}
var s9 = { createProgram: e5, bindTextureAndUniforms: n6 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/compositeBandProcessor.js
var a8 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/compositeband", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function e6(t7, r13) {
  return t7.painter.materialManager.getProgram(a8, []);
}
function o4(a13, e14, o12) {
  r8(a13, e14, o12), s7(e14);
}
var n7 = { createProgram: e6, bindTextureAndUniforms: o4 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/convolutionProcessor.js
var t5 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/convolution", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function a9(e14, r13) {
  const { painter: a13, rasterFunction: n16 } = e14, { kernelRows: o12, kernelCols: s16 } = n16.parameters, i11 = [{ name: "rows", value: o12 }, { name: "cols", value: s16 }];
  return a13.materialManager.getProgram(t5, i11);
}
function n8(t7, a13, n16) {
  a5(t7, a13, n16), s7(a13), a13.setUniform2fv("u_srcImageSize", [n16.width, n16.height]);
  const { kernel: o12, clampRange: s16 } = t7.rasterFunction.parameters;
  a13.setUniform1fv("u_kernel", o12), a13.setUniform2fv("u_clampRange", s16);
}
var o5 = { createProgram: a9, bindTextureAndUniforms: n8 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/extractBandProcessor.js
var a10 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/extractband", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function e7(t7, r13) {
  return t7.painter.materialManager.getProgram(a10, []);
}
function n9(a13, e14, n16) {
  a5(a13, e14, n16), s7(e14);
  const { bandIndexMat3: o12 } = a13.rasterFunction.parameters;
  e14.setUniformMatrix3fv("u_bandIndexMat3", o12);
}
var o6 = { createProgram: e7, bindTextureAndUniforms: n9 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/localProcessor.js
var e8 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/local", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
var o7 = /* @__PURE__ */ new Set(["sinh", "cosh", "tanh", "asinh", "acosh", "atanh"]);
function r9(t7) {
  const { painter: n16, rasterFunction: r13 } = t7, { imageCount: s16, operationName: i11, rasters: u5, isOutputRounded: c5 } = r13.parameters;
  let m8 = i11.toLowerCase();
  t7.context.type === r4.WEBGL1 && o7.has(m8) && (m8 = `polyfill${m8}`);
  const p2 = [m8];
  2 === s16 && p2.push("twoImages");
  const h8 = u5.filter((t8) => "Constant" === t8.name);
  return h8.length && (p2.push("oneConstant"), 2 === h8.length && p2.push("twoConstant")), c5 && p2.push("roundOutput"), n16.materialManager.getProgram(e8, p2);
}
function s10(a13, e14, o12) {
  r8(a13, e14, o12), s7(e14);
  const { domainRange: r13 } = a13.rasterFunction.parameters;
  e14.setUniform2fv("u_domainRange", r13);
}
var i7 = { createProgram: r9, bindTextureAndUniforms: s10 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/maskProcessor.js
var r10 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/mask", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function n10(t7, a13) {
  const { painter: n16, rasterFunction: e14 } = t7, s16 = e14.parameters.bandCount > 1 ? ["multiBand"] : [];
  return n16.materialManager.getProgram(r10, s16);
}
function e9(r13, n16, e14) {
  a5(r13, n16, e14), s7(n16);
  const { includedRanges: s16, noDataValues: o12 } = r13.rasterFunction.parameters;
  n16.setUniform1fv("u_includedRanges", s16), n16.setUniform1fv("u_noDataValues", o12);
}
var s11 = { createProgram: n10, bindTextureAndUniforms: e9 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/ndviProcessor.js
var a11 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/ndvi", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function e10(t7, r13) {
  const { painter: e14, rasterFunction: n16 } = t7, s16 = n16.parameters.scaled ? ["scaled"] : [];
  return e14.materialManager.getProgram(a11, s16);
}
function n11(a13, e14, n16) {
  a5(a13, e14, n16), s7(e14);
  const { bandIndexMat3: s16 } = a13.rasterFunction.parameters;
  e14.setUniformMatrix3fv("u_bandIndexMat3", s16);
}
var s12 = { createProgram: e10, bindTextureAndUniforms: n11 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/remapProcessor.js
var r11 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/remap", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function e11(a13, t7) {
  return a13.painter.materialManager.getProgram(r11, []);
}
function n12(r13, e14, n16) {
  a5(r13, e14, n16), s7(e14);
  const { noDataRanges: s16, rangeMaps: o12, allowUnmatched: f7, clampRange: i11 } = r13.rasterFunction.parameters;
  e14.setUniform1fv("u_noDataRanges", s16), e14.setUniform1fv("u_rangeMaps", o12), e14.setUniform1f("u_unmatchMask", f7 ? 1 : 0), e14.setUniform2fv("u_clampRange", i11);
}
var s13 = { createProgram: e11, bindTextureAndUniforms: n12 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/reprojectProcessor.js
var e12 = { vsPath: "raster/common", fsPath: "raster/reproject", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function i8(t7, r13) {
  var _a;
  const { painter: i11 } = t7, o12 = [], s16 = !((_a = t7.context.capabilities.textureFloat) == null ? void 0 : _a.textureFloatLinear), { interpolation: n16, transformGrid: a13 } = r13;
  return "cubic" === n16 ? o12.push("bicubic") : "bilinear" === n16 && s16 && o12.push("bilinear"), a13 && (o12.push("applyProjection"), 1 === a13.spacing[0] && o12.push("lookupProjection")), i11.materialManager.getProgram(e12, o12);
}
function o8(e14, i11, o12) {
  const { names: s16, textures: n16 } = o12.getTextures({ forProcessing: true });
  d2(e14.context, i11, s16, n16), i11.setUniform1f("u_scale", 1), i11.setUniform2fv("u_offset", [0, 0]), i11.setUniform2fv("u_coordScale", [1, 1]), i11.setUniformMatrix3fv("u_dvsMat3", [2, 0, 0, 0, 2, 0, -1, -1, 0]), i11.setUniform1i("u_flipY", 0), i11.setUniform1f("u_opacity", 1);
  const { width: a13, height: f7, source: c5, transformGrid: u5 } = o12;
  i11.setUniform2fv("u_srcImageSize", [c5.width, c5.height]), i11.setUniform2fv("u_targetImageSize", [a13, f7]), i11.setUniform2fv("u_transformSpacing", u5 ? u5.spacing : f), i11.setUniform2fv("u_transformGridSize", u5 ? u5.size : f);
}
var s14 = { createProgram: i8, bindTextureAndUniforms: o8 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/slopeProcessor.js
var t6 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/slope", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function o9(e14, r13) {
  const { painter: o12, rasterFunction: i11 } = e14, { slopeType: s16 } = i11.parameters, a13 = "percent-rise" === s16 ? ["percentRise"] : [];
  return o12.materialManager.getProgram(t6, a13);
}
function i9(t7, o12, i11) {
  a5(t7, o12, i11), s7(o12);
  const { width: s16, height: a13, resolution: n16 } = i11, { zFactor: c5, slopeType: f7, pixelSizePower: p2, pixelSizeFactor: u5 } = t7.rasterFunction.parameters;
  o12.setUniform2fv("u_srcImageSize", [s16, a13]), o12.setUniform2fv("u_cellSize", [n16, n16]), o12.setUniform1f("u_zFactor", c5), o12.setUniform1f("u_pixelSizePower", "adjusted" === f7 ? p2 : 0), o12.setUniform1f("u_pixelSizeFactor", "adjusted" === f7 ? u5 : 0);
}
var s15 = { createProgram: o9, bindTextureAndUniforms: i9 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/stretchProcessor.js
var a12 = { vsPath: "raster/rfx/vs", fsPath: "raster/rfx/stretch", attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
function u4(t7, r13) {
  const { useGamma: u5, bandCount: e14, isOutputRounded: n16 } = t7.rasterFunction.parameters, o12 = [];
  return u5 && o12.push("useGamma"), e14 > 1 && o12.push("multiBand"), n16 && o12.push("roundOutput"), t7.painter.materialManager.getProgram(a12, o12);
}
function e13(a13, u5, e14) {
  a5(a13, u5, e14), s7(u5);
  const { width: n16, height: o12 } = e14, m8 = a13.rasterFunction.parameters;
  u5.setUniform2fv("u_srcImageSize", [n16, o12]), u5.setUniform1f("u_minOutput", m8.outMin), u5.setUniform1f("u_maxOutput", m8.outMax), u5.setUniform1fv("u_factor", m8.factor), u5.setUniform1fv("u_minCutOff", m8.minCutOff), u5.setUniform1fv("u_maxCutOff", m8.maxCutOff), u5.setUniform1fv("u_gamma", m8.gamma), u5.setUniform1fv("u_gammaCorrection", m8.gammaCorrection);
}
var n13 = { createProgram: u4, bindTextureAndUniforms: e13 };

// node_modules/@arcgis/core/views/2d/engine/imagery/processor/rasterProcessor.js
var b3 = /* @__PURE__ */ new Map();
function g4(r13, e14, o12) {
  const t7 = { width: e14, height: o12, target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: r13.type === r4.WEBGL2 ? U2.RGBA32F : P.RGBA, samplingMode: L.NEAREST, dataType: G.FLOAT, isImmutable: r13.type === r4.WEBGL2, wrapMode: D.CLAMP_TO_EDGE, flipped: false };
  return new E2(r13, t7);
}
function A2(e14, o12, t7, s16) {
  const { context: n16, requestRender: i11, allowDelayedRender: c5 } = e14, m8 = s16.createProgram(e14, t7);
  if (c5 && r(i11) && !m8.compiled) return i11(), null;
  const { width: a13, height: p2 } = t7;
  return n16.bindFramebuffer(o12), n16.setViewport(0, 0, a13, p2), n16.useProgram(m8), m8;
}
function B(r13) {
  return b3.get(r13.toLowerCase());
}
function E4(r13, e14, o12, t7) {
  const s16 = r13.rasterFunction.name.toLowerCase(), n16 = "reproject" === s16 ? s14 : B(s16);
  if (null == n16) return;
  const i11 = A2(r13, o12, t7, n16);
  if (!i11) return;
  n16.bindTextureAndUniforms(r13, i11, t7), e14.draw();
  const { width: c5, height: m8 } = t7, a13 = g4(r13.context, c5, m8);
  if (o12.copyToTexture(0, 0, c5, m8, 0, 0, a13), "reproject" === s16) t7.rasterTexture = a13, t7.projected = true;
  else {
    const e15 = r13.hasBranches ? r13.rasterFunction.id : 0;
    t7.functionTextures[e15] = a13;
  }
}
b3.set("aspect", s8), b3.set("bandarithmetic", s9), b3.set("compositeband", n7), b3.set("convolution", o5), b3.set("extractband", o6), b3.set("local", i7), b3.set("mask", s11), b3.set("ndvi", s12), b3.set("remap", s13), b3.set("slope", s15), b3.set("stretch", n13);

// node_modules/@arcgis/core/views/2d/engine/imagery/BrushRasterBitmap.js
var b4 = class extends t3 {
  constructor() {
    super(...arguments), this.name = "raster", this._quad = null, this._rendererUniformInfos = /* @__PURE__ */ new Map(), this._fbo = null;
  }
  dispose() {
    h(this._quad), h(this._fbo);
  }
  prepareState(e14) {
    const { context: t7, renderPass: r13 } = e14, s16 = "raster" === r13;
    t7.setBlendingEnabled(!s16), t7.setBlendFunctionSeparate(R.ONE, R.ONE_MINUS_SRC_ALPHA, R.ONE, R.ONE_MINUS_SRC_ALPHA), t7.setColorMask(true, true, true, true), t7.setStencilWriteMask(0), t7.setStencilTestEnabled(!s16);
  }
  draw(e14, t7) {
    if (!f4(t7) || t7.suspended) return;
    const { renderPass: s16 } = e14;
    if ("raster-bitmap" !== s16) return "raster" === s16 ? this._process(e14, t7) : void this._drawBitmap(e14, t7, true);
    this._drawBitmap(e14, t7);
  }
  _process(t7, r13) {
    const { rasterFunction: s16 } = t7, o12 = "Reproject" === s16.name;
    if (!(o12 ? !(r13.rasterTexture && r13.projected) : !r13.processed)) return;
    const { timeline: n16, context: a13 } = t7;
    n16.begin(this.name);
    const d3 = a13.getBoundFramebufferObject(), m8 = a13.getViewport();
    o12 || (r13.processedTexture = h(r13.processedTexture)), a13.setStencilFunction(I.EQUAL, r13.stencilRef, 255), r13.updateTexture(t7), this._initQuad(a13);
    const { isStandardRasterTileSize: f7, fbo: u5 } = this._getRasterFBO(a13, r13.width, r13.height);
    E4(t7, this._quad, u5, r13), f7 || u5.dispose(), a13.bindFramebuffer(d3), a13.setViewport(m8.x, m8.y, m8.width, m8.height), n16.end(this.name);
  }
  _drawBitmap(e14, r13, i11 = false) {
    const { timeline: o12, context: n16 } = e14;
    if (o12.begin(this.name), n16.setStencilFunction(I.EQUAL, r13.stencilRef, 255), r13.updateTexture(e14), i11 && !r13.processedTexture) {
      if (r13.updateProcessedTexture(), !r13.processedTexture) return void o12.end(this.name);
      r13.processed = true;
    }
    this._initBitmapCommonUniforms(r13);
    const d3 = r13.symbolizerParameters.type, m8 = o2(d3), { requestRender: f7, allowDelayedRender: u5 } = e14, { defines: h8, program: p2 } = m8.createProgram(e14, r13, i11);
    if (u5 && r(f7) && !p2.compiled) return void f7();
    n16.useProgram(p2);
    const l6 = this._getUniformInfos(d3, n16, p2, h8);
    this._quad || (this._quad = new n(n16, [0, 0, 1, 0, 0, 1, 1, 1])), m8.bindTextureAndUniforms(e14, p2, r13, l6, i11), this._quad.draw(), o12.end(this.name);
  }
  _initBitmapCommonUniforms(e14) {
    if (!e14.commonUniforms) {
      const t7 = T2(1, [0, 0]), { transformGrid: r13, width: s16, height: i11 } = e14, o12 = p(r13, [s16, i11], [e14.source.width, e14.source.height], 1, false);
      e14.commonUniforms = { ...t7, ...o12, u_coordScale: e14.coordScale };
    }
  }
  _getRasterFBO(e14, t7, r13) {
    const s16 = t7 === o || r13 === o;
    return s16 ? (this._fbo || (this._fbo = this._createNewFBO(e14, t7, r13)), { isStandardRasterTileSize: s16, fbo: this._fbo }) : { isStandardRasterTileSize: s16, fbo: this._createNewFBO(e14, t7, r13) };
  }
  _createNewFBO(e14, t7, r13) {
    const s16 = g4(e14, t7, r13);
    return new x2(e14, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE, width: t7, height: r13 }, s16);
  }
  _initQuad(e14) {
    this._quad || (this._quad = new n(e14, [0, 0, 1, 0, 0, 1, 1, 1]));
  }
  _getUniformInfos(e14, t7, r13, s16) {
    const i11 = s16.length > 0 ? e14 + "-" + s16.join("-") : e14;
    if (this._rendererUniformInfos.has(i11)) return this._rendererUniformInfos.get(i11);
    const o12 = h7(t7, r13);
    return this._rendererUniformInfos.set(i11, o12), o12;
  }
};

// node_modules/@arcgis/core/views/2d/engine/imagery/RasterTile.js
var i10 = class extends r6 {
  constructor(t7, e14, i11, a13, r13, n16, l6 = null) {
    super(t7, e14, i11, a13, r13, n16), this.bitmap = null, this.bitmap = new m4(l6, null, null), this.bitmap.coordScale = [r13, n16], this.bitmap.once("isReady", () => this.ready());
  }
  destroy() {
    super.destroy(), this.bitmap.destroy(), this.bitmap = null, this.stage = null;
  }
  set stencilRef(t7) {
    this.bitmap.stencilRef = t7;
  }
  get stencilRef() {
    return this.bitmap.stencilRef;
  }
  setTransform(t7) {
    super.setTransform(t7), this.bitmap.transforms.dvs = this.transforms.dvs;
  }
  _createTransforms() {
    return { dvs: e3(), tileMat3: e3() };
  }
  onAttach() {
    this.bitmap.stage = this.stage;
  }
  onDetach() {
    this.bitmap.stage = null;
  }
};

// node_modules/@arcgis/core/views/2d/engine/imagery/RasterTileContainer.js
var o10 = class extends i2 {
  constructor() {
    super(...arguments), this.isCustomTilingScheme = false;
  }
  createTile(e14) {
    const s16 = this._getTileBounds(e14), [t7, i11] = this._tileInfoView.tileInfo.size, n16 = this._tileInfoView.getTileResolution(e14.level);
    return new i10(e14, n16, s16[0], s16[3], t7, i11);
  }
  prepareRenderPasses(e14) {
    const s16 = e14.registerRenderPass({ name: "imagery (tile)", brushes: [b4], target: () => this.children.map((e15) => e15.bitmap), drawPhase: T.MAP });
    return [...super.prepareRenderPasses(e14), s16];
  }
  doRender(e14) {
    if (!this.visible || e14.drawPhase !== T.MAP) return;
    const { rasterFunctionChain: s16 } = this;
    if (!s16) return e14.renderPass = "raster-bitmap", void super.doRender(e14);
    const [t7, r13] = this._tileInfoView.tileInfo.size;
    if (e14.renderPass = "raster", e14.rasterFunction = { name: "Reproject", parameters: { targetImageSize: [t7, r13] }, pixelType: "f32", id: 0, isNoopProcess: false }, super.doRender(e14), s16 == null ? void 0 : s16.functions.length) {
      const { functions: t8, hasBranches: r14 } = s16;
      for (let s17 = 0; s17 < t8.length; s17++) {
        const i11 = t8[s17];
        "Constant" !== i11.name && "Identity" !== i11.name && (e14.renderPass = "raster", e14.rasterFunction = i11, e14.hasBranches = r14, super.doRender(e14));
      }
    }
    e14.rasterFunction = null, e14.renderPass = "bitmap", super.doRender(e14);
  }
  _getTileBounds(t7) {
    const r13 = this._tileInfoView.getTileBounds(u(), t7);
    if (this.isCustomTilingScheme && t7.world) {
      const { tileInfo: e14 } = this._tileInfoView, i11 = mt(e14.spatialReference);
      if (i11) {
        const s16 = e14.lodAt(t7.level);
        if (!s16) return r13;
        const { resolution: n16 } = s16, o12 = i11 / n16 % e14.size[0], a13 = o12 ? (e14.size[0] - o12) * n16 : 0;
        r13[0] -= a13 * t7.world, r13[2] -= a13 * t7.world;
      }
    }
    return r13;
  }
};

// node_modules/@arcgis/core/views/2d/layers/imagery/BaseImageryTileSubView2D.js
var T3 = [0, 0];
var v2 = class extends d {
  constructor() {
    super(...arguments), this._emptyTilePixelBlock = null, this._tileStrategy = null, this._tileInfoView = null, this._fetchQueue = null, this._blockCacheRegistryUrl = null, this._blockCacheRegistryId = null, this._srcResolutions = [], this.previousLOD = null, this._needBlockCacheUpdate = false, this._globalSymbolizerParams = null, this._symbolizerParams = null, this._abortController = null, this._isCustomTilingScheme = false, this._rasterFunctionState = "na", this._globalUpdateRequested = false, this.attached = false, this.timeExtent = null, this.redrawOrRefetch = x(async (e14 = {}) => {
      if (!this.previousLOD || this.layerView.suspended) return;
      const t7 = this._rasterFunctionState;
      e14.reprocess && (await this.updatingHandles.addPromise(this.layer.updateRasterFunction()), this.updateRasterFunctionParameters());
      const i11 = this._rasterFunctionState, { type: s16 } = this;
      return e14.refetch || "raster" !== s16 && !!e14.reprocess || "cpu" === i11 || "cpu" === t7 ? this.updatingHandles.addPromise(this.doRefresh()) : this.updatingHandles.addPromise(this._redrawImage(e14.signal));
    });
  }
  get useWebGLForProcessing() {
    return this._get("useWebGLForProcessing") ?? true;
  }
  set useWebGLForProcessing(e14) {
    this._set("useWebGLForProcessing", e14);
  }
  get useProgressiveUpdate() {
    return null == this._get("useProgressiveUpdate") || this._get("useProgressiveUpdate");
  }
  set useProgressiveUpdate(e14) {
    if (this._tileStrategy && this.useProgressiveUpdate !== e14) {
      this._tileStrategy.destroy(), this.container.removeAllChildren();
      const t7 = this._getCacheSize(e14);
      this._tileStrategy = new r3({ cachePolicy: "purge", acquireTile: (e15) => this.acquireTile(e15), releaseTile: (e15) => this.releaseTile(e15), cacheSize: t7, tileInfoView: this._tileInfoView }), this._set("useProgressiveUpdate", e14), this.layerView.requestUpdate();
    }
  }
  update(e14) {
    var _a;
    this._fetchQueue.pause(), this._fetchQueue.state = e14.state, this._tileStrategy.update(e14), this._fetchQueue.resume();
    const { extent: t7, resolution: i11, scale: s16 } = e14.state, r13 = this._tileInfoView.getClosestInfoForScale(s16);
    if (this.layer.raster) {
      if (!this.useProgressiveUpdate || this._needBlockCacheUpdate) {
        const e15 = this._srcResolutions[r13.level], s17 = t7.toJSON ? t7 : w2.fromJSON(t7);
        g(this._blockCacheRegistryUrl, this._blockCacheRegistryId, s17, i11, e15, this.layer.raster.ioConfig.sampling);
      }
      this._needBlockCacheUpdate = false, ((_a = this.previousLOD) == null ? void 0 : _a.level) !== r13.level && (this.previousLOD = r13, null == this._symbolizerParams || this.layerView.hasTilingEffects || this._updateSymbolizerParams(), this._tileStrategy.updateCacheSize(0));
    }
  }
  moveEnd() {
    !this.layerView.hasTilingEffects && this.useProgressiveUpdate || (this._abortController && this._abortController.abort(), this._abortController = new AbortController(), 0 === this._fetchQueue.length && this._redrawImage(this._abortController.signal).then(() => {
      this._globalUpdateRequested = false, this.layerView.requestUpdate();
    }));
    const e14 = this._getCacheSize(this.useProgressiveUpdate);
    this._tileStrategy.updateCacheSize(e14), this.layerView.requestUpdate();
  }
  get updating() {
    var _a;
    return ((_a = this._fetchQueue) == null ? void 0 : _a.updating) || this._globalUpdateRequested || !(!this.updatingHandles || !this.updatingHandles.updating);
  }
  attach() {
    l2("2d").supportsTextureFloat || (this.useWebGLForProcessing = false), this._initializeTileInfo(), this._tileInfoView = new h4(this.layerView.tileInfo, this.layerView.fullExtent);
    const e14 = this._computeFetchConcurrency();
    this._fetchQueue = new y2({ tileInfoView: this._tileInfoView, concurrency: e14, process: (e15, t8) => this._fetchTile1(e15, t8) });
    const t7 = this._getCacheSize(this.useProgressiveUpdate);
    this._tileStrategy = new r3({ cachePolicy: "purge", acquireTile: (e15) => this.acquireTile(e15), releaseTile: (e15) => this.releaseTile(e15), cacheSize: t7, tileInfoView: this._tileInfoView }), this._updateBlockCacheRegistry();
  }
  detach() {
    this._tileStrategy.destroy(), this._fetchQueue.clear(), this.container.removeAllChildren(), this._fetchQueue = this._tileStrategy = this._tileInfoView = null, f2(this._blockCacheRegistryUrl, this._blockCacheRegistryId), this._blockCacheRegistryUrl = this._blockCacheRegistryId = null;
  }
  acquireTile(e14) {
    const t7 = this.container.createTile(e14);
    return this._enqueueTileFetch(t7), this.layerView.requestUpdate(), this._needBlockCacheUpdate = true, this._globalUpdateRequested = this.layerView.hasTilingEffects || !this.useProgressiveUpdate, t7;
  }
  releaseTile(e14) {
    this._fetchQueue.abort(e14.key.id), this.container.removeChild(e14), e14.once("detach", () => {
      e14.destroy(), this.layerView.requestUpdate();
    }), this.layerView.requestUpdate();
  }
  createEmptyTilePixelBlock(e14 = null) {
    const t7 = null == e14 || e14.join(",") === this._tileInfoView.tileInfo.size.join(",");
    if (t7 && r(this._emptyTilePixelBlock)) return this._emptyTilePixelBlock;
    e14 = e14 || this._tileInfoView.tileInfo.size;
    const [i11, r13] = e14, a13 = new m({ width: i11, height: r13, pixels: [new Uint8Array(i11 * r13)], mask: new Uint8Array(i11 * r13), pixelType: "u8" });
    return t7 && (this._emptyTilePixelBlock = a13), a13;
  }
  _getBandIds() {
    if (!("rasterFunctionChain" in this.container) || !this.container.rasterFunctionChain) return this.layer.bandIds;
    const { bandIds: e14, raster: t7 } = this.layer, i11 = "rasterFunction" in t7 ? t7.rasterFunction.rawInputBandIds : null;
    return (e14 == null ? void 0 : e14.length) && (i11 == null ? void 0 : i11.length) && 1 !== t7.rasterInfo.bandCount ? e14.map((e15) => i11[Math.min(e15, i11.length - 1)]) : e14 || i11;
  }
  updateRasterFunctionParameters() {
  }
  _fetchTile1(e14, t7) {
    const i11 = r(t7) ? t7.signal : null, a13 = this.canUseWebGLForProcessing(), { layerView: o12 } = this, { tileInfo: l6 } = o12, n16 = !l6.isWrappable && r(U3(o12.view.spatialReference)), h8 = a13 && this.layer.raster.hasUniqueSourceStorageInfo, c5 = { allowPartialFill: true, datumTransformation: o12.datumTransformation, interpolation: a13 ? "nearest" : this.layer.interpolation, registryId: this._blockCacheRegistryId, requestRawData: h8, skipRasterFunction: "raster" === this.type && null != this.container.rasterFunctionChain, signal: e2(i11), srcResolution: this._srcResolutions[e14.level], timeExtent: o12.timeExtent, tileInfo: l6, disableWrapAround: n16 };
    return this.fetchTile(e14, c5);
  }
  _getCacheSize(e14) {
    return e14 ? 40 : 0;
  }
  _initializeTileInfo() {
    const { layerView: e14 } = this, t7 = e14.view.spatialReference, i11 = new w({ x: e14.fullExtent.xmin, y: e14.fullExtent.ymax, spatialReference: t7 });
    if (this._canUseLayerLODs()) {
      const { lods: s17 } = this.layer.tileInfo, r14 = s17.map(({ scale: e15 }) => e15), a14 = j2.create({ spatialReference: t7, size: o, scales: r14 }), o13 = t7.isGeographic ? 0.01 * 1e-5 : 0.01;
      if (this._isCustomTilingScheme = Math.abs(a14.origin.x - i11.x) > o13, (0 === a14.origin.x || a14.origin.x > i11.x) && (a14.origin = i11), !this._isCustomTilingScheme) {
        const e15 = j2.create({ spatialReference: t7, size: o }).lods.map(({ scale: e16 }) => e16);
        this._isCustomTilingScheme = r14.some((t8) => !e15.some((e16) => Math.abs(e16 - t8) < 1e-3));
      }
      return e14.set("tileInfo", a14), void (this._srcResolutions = s17.map(({ resolution: e15 }) => ({ x: e15, y: e15 })));
    }
    const { scales: s16, srcResolutions: r13, isCustomTilingScheme: a13 } = ie(this.layer.rasterInfo, t7, o), o12 = j2.create({ spatialReference: t7, size: o, scales: s16 });
    (0 === o12.origin.x || o12.origin.x > i11.x) && (o12.origin = i11), this._isCustomTilingScheme = a13, e14.set("tileInfo", o12), this._srcResolutions = r13 ?? [];
  }
  _canUseLayerLODs() {
    var _a;
    const { layer: e14, layerView: t7 } = this;
    if ("Map" !== e14.raster.tileType) return false;
    const { lods: i11 } = e14.tileInfo, s16 = (_a = t7.view.constraints) == null ? void 0 : _a.effectiveLODs;
    if (!((s16 == null ? void 0 : s16.length) === i11.length && s16.every(({ scale: e15 }, t8) => Math.abs(e15 - i11[t8].scale) < 1e-3))) return false;
    const r13 = [];
    for (let a13 = 0; a13 < i11.length - 1; a13++) r13.push(Math.round(10 * i11[a13].resolution / i11[a13 + 1].resolution) / 10);
    return r13.some((e15) => e15 !== e15[0]);
  }
  _computeFetchConcurrency() {
    const { blockBoundary: e14 } = this.layer.rasterInfo.storageInfo, t7 = e14[e14.length - 1];
    return (t7.maxCol - t7.minCol + 1) * (t7.maxRow - t7.minRow + 1) > 64 ? 2 : 10;
  }
  async _enqueueTileFetch(e14, t7) {
    this.updatingHandles.addPromise(this._enqueueTileFetch1(e14, t7));
  }
  async _enqueueTileFetch1(e14, t7) {
    var _a;
    if (!this._fetchQueue.has(e14.key.id)) {
      try {
        const t8 = await this._fetchQueue.push(e14.key), r13 = this._getBandIds();
        let a13 = !this.useProgressiveUpdate || this.layerView.hasTilingEffects && !this._globalSymbolizerParams;
        if (this._globalUpdateRequested && !this.layerView.moving && 0 === this._fetchQueue.length) {
          a13 = false;
          try {
            await this._redrawImage((_a = this._abortController) == null ? void 0 : _a.signal);
          } catch (s16) {
            j(s16) && s.getLogger(this.declaredClass).error(s16);
          }
          this._globalUpdateRequested = false;
        }
        !this.canUseWebGLForProcessing() && "rasterVF" !== this.type || this.layerView.hasTilingEffects || null != this._symbolizerParams || this._updateSymbolizerParams();
        const l6 = this._tileInfoView.getTileCoords(T3, e14.key), n16 = this._tileInfoView.getTileResolution(e14.key);
        await this.updateTileSource(e14, { source: t8, symbolizerParams: this._symbolizerParams, globalSymbolizerParams: this._globalSymbolizerParams, suspended: a13, bandIds: r13, coords: l6, resolution: n16 }), e14.once("attach", () => this.layerView.requestUpdate()), this.container.addChild(e14);
      } catch (s16) {
        j(s16) || s.getLogger(this.declaredClass).error(s16);
      }
      this.layerView.requestUpdate();
    }
  }
  async _redrawImage(e14) {
    if (0 === this.container.children.length) return;
    await this.layer.updateRenderer(), this.layerView.hasTilingEffects ? await this._updateGlobalSymbolizerParams(e14) : (this._updateSymbolizerParams(), this._globalSymbolizerParams = null);
    const t7 = this.container.children.map(async (e15) => this.updateTileSymbolizerParameters(e15, { local: this._symbolizerParams, global: this._globalSymbolizerParams }));
    await E(t7), this.container.requestRender();
  }
  async _updateGlobalSymbolizerParams(e14) {
    const t7 = { srcResolution: this._srcResolutions[this.previousLOD.level], registryId: this._blockCacheRegistryId, signal: e14 }, i11 = await this.layer.fetchPixels(this.layerView.view.extent, this.layerView.view.width, this.layerView.view.height, t7);
    if (!i11 || !i11.pixelBlock) return;
    const { resolution: s16 } = this.previousLOD, r13 = this._getBandIds(), a13 = this.layer.symbolizer.generateWebGLParameters({ pixelBlock: h5(i11.pixelBlock, r13), isGCS: this.layerView.view.spatialReference.isGeographic, resolution: { x: s16, y: s16 }, bandIds: r13 });
    !this.canUseWebGLForProcessing() && a13 && "stretch" === a13.type && this.layer.renderer && "raster-stretch" === this.layer.renderer.type && (a13.factor = a13.factor.map((e15) => 255 * e15), a13.outMin = Math.round(255 * a13.outMin), a13.outMax = Math.round(255 * a13.outMax)), this._globalSymbolizerParams = a13;
  }
  _updateSymbolizerParams() {
    const { resolution: e14 } = this.previousLOD, t7 = this._getBandIds();
    this._symbolizerParams = this.layer.symbolizer.generateWebGLParameters({ pixelBlock: null, isGCS: this.layerView.view.spatialReference.isGeographic, resolution: { x: e14, y: e14 }, bandIds: t7 });
  }
  _updateBlockCacheRegistry(e14 = false) {
    const { layer: t7, layerView: i11 } = this, { url: s16, raster: r13 } = t7, { multidimensionalDefinition: a13 } = t7.normalizeRasterFetchOptions({ multidimensionalDefinition: t7.multidimensionalDefinition, timeExtent: i11.timeExtent }), o12 = r13.rasterInfo.multidimensionalInfo ? r13.getSliceIndex(a13) : null, l6 = a2(s16, o12);
    if (l6 !== this._blockCacheRegistryUrl) {
      if (null != this._blockCacheRegistryUrl && f2(this._blockCacheRegistryUrl, this._blockCacheRegistryId), this._blockCacheRegistryId = u2(l6, r13.rasterInfo), e14) {
        const { view: e15 } = i11, t8 = this._tileInfoView.getClosestInfoForScale(e15.scale), s17 = this._srcResolutions[t8.level];
        g(l6, this._blockCacheRegistryId, e15.extent, e15.resolution, s17, r13.ioConfig.sampling);
      }
      this._blockCacheRegistryUrl = l6;
    }
  }
  async doRefresh() {
    if (!this.attached) return;
    await this.layer.updateRenderer(), this.layerView.hasTilingEffects || this._updateSymbolizerParams(), this._updateBlockCacheRegistry(true), this._fetchQueue.reset();
    const e14 = [];
    this._globalUpdateRequested = this.layerView.hasTilingEffects || !this.useProgressiveUpdate, this._tileStrategy.tiles.forEach((t7) => e14.push(this._enqueueTileFetch(t7))), await E(e14);
  }
};
e([y()], v2.prototype, "_fetchQueue", void 0), e([y()], v2.prototype, "_globalUpdateRequested", void 0), e([y()], v2.prototype, "attached", void 0), e([y()], v2.prototype, "container", void 0), e([y()], v2.prototype, "layer", void 0), e([y()], v2.prototype, "layerView", void 0), e([y()], v2.prototype, "type", void 0), e([y()], v2.prototype, "useWebGLForProcessing", null), e([y()], v2.prototype, "useProgressiveUpdate", null), e([y()], v2.prototype, "timeExtent", void 0), e([y()], v2.prototype, "updating", null), v2 = e([a("esri.views.2d.layers.imagery.BaseImageryTileSubView2D")], v2);

// node_modules/@arcgis/core/views/2d/layers/imagery/ImageryTileView2D.js
var n14 = class extends v2 {
  constructor() {
    super(...arguments), this.type = "raster";
  }
  attach() {
    super.attach(), this.container = new o10(this._tileInfoView), this.container.isCustomTilingScheme = this._isCustomTilingScheme, this.updateRasterFunctionParameters();
  }
  detach() {
    super.detach(), this.container.removeAllChildren(), this.container = null;
  }
  canUseWebGLForProcessing() {
    return this.useWebGLForProcessing && this.layer.symbolizer.canRenderInWebGL && !("majority" === this.layer.interpolation && r7(this.layer));
  }
  fetchTile(e14, r13) {
    return this.layer.fetchTile(e14.level, e14.row, e14.col, r13);
  }
  updateRasterFunctionParameters() {
    const { raster: e14, type: r13 } = this.layer, { container: t7 } = this;
    if ("Function" !== e14.datasetFormat || "wcs" === r13) return t7.rasterFunctionChain = null, t7.children.forEach((e15) => {
      const { bitmap: r14 } = e15;
      r14 && (r14.suspended = true, r14.processed = false, r14.projected && (r14.invalidateTexture(), r14.rasterTexture = null));
    }), void (this._rasterFunctionState = "na");
    const s16 = this._rasterFunctionState, { rasterFunction: i11, primaryRasters: o12 } = e14, a13 = i11.supportsGPU && (!o12 || o12.rasters.length <= 1), n16 = a13 ? i11.getFlatWebGLFunctionChain() : null, { renderer: l6 } = this.layer, c5 = !a13 || !(n16 == null ? void 0 : n16.functions.length) || "raster-stretch" === l6.type && l6.dynamicRangeAdjustment || !this.canUseWebGLForProcessing();
    t7.rasterFunctionChain = c5 ? null : n16;
    const u5 = null == i11 ? "na" : t7.rasterFunctionChain ? "gpu" : "cpu";
    t7.children.forEach((e15) => {
      const { bitmap: r14 } = e15;
      r14 && (r14.suspended = s16 !== u5, r14.processed = false, r14.processedTexture = null);
    }), this._rasterFunctionState = u5;
  }
  async updateTileSource(e14, t7) {
    const s16 = this._getBandIds(), i11 = this._getLayerInterpolation(), o12 = this.canUseWebGLForProcessing(), { source: a13, globalSymbolizerParams: n16, suspended: l6, coords: c5, resolution: u5 } = t7, p2 = this.layerView.hasTilingEffects ? n16 : t7.symbolizerParams, { bitmap: d3 } = e14;
    if ([d3.x, d3.y] = c5, d3.resolution = u5, a13 && r(a13) && r(a13.pixelBlock)) {
      const e15 = { extent: a13.extent, pixelBlock: a13.pixelBlock };
      if (d3.rawPixelData = e15, o12) d3.source = a13.pixelBlock, d3.isRendereredSource = false;
      else {
        const r13 = await this.layer.applyRenderer(e15, "stretch" === (n16 == null ? void 0 : n16.type) ? n16 : void 0);
        d3.source = r13, d3.isRendereredSource = true;
      }
      d3.symbolizerParameters = o12 ? p2 : null, o12 ? d3.transformGrid || (d3.transformGrid = a13.transformGrid) : d3.transformGrid = null;
    } else {
      const e15 = this.createEmptyTilePixelBlock();
      d3.source = e15, d3.symbolizerParameters = o12 ? p2 : null, d3.transformGrid = null;
    }
    d3.bandIds = o12 ? s16 : null, d3.width = this._tileInfoView.tileInfo.size[0], d3.height = this._tileInfoView.tileInfo.size[1], d3.interpolation = i11, d3.suspended = l6, d3.invalidateTexture();
  }
  async updateTileSymbolizerParameters(e14, t7) {
    const { local: s16, global: i11 } = t7, o12 = this._getBandIds(), a13 = this._getLayerInterpolation(), n16 = this.canUseWebGLForProcessing(), { bitmap: l6 } = e14, { rawPixelData: c5 } = l6;
    !n16 && r(c5) ? (l6.source = await this.layer.applyRenderer(c5, "stretch" === (i11 == null ? void 0 : i11.type) ? i11 : void 0), l6.isRendereredSource = true) : (l6.isRendereredSource && r(c5) && (l6.source = c5.pixelBlock), l6.isRendereredSource = false), l6.symbolizerParameters = n16 ? this.layerView.hasTilingEffects ? i11 : s16 : null, l6.bandIds = n16 ? o12 : null, l6.interpolation = a13, l6.suspended = false;
  }
  _getLayerInterpolation() {
    const e14 = this.layer.renderer.type;
    if ("raster-colormap" === e14 || "unique-value" === e14 || "class-breaks" === e14) return "nearest";
    const { interpolation: r13 } = this.layer, { renderer: t7 } = this.layer;
    return "raster-stretch" === t7.type && null != t7.colorRamp ? "bilinear" === r13 || "cubic" === r13 ? "bilinear" : "nearest" : r13;
  }
};
e([y()], n14.prototype, "container", void 0), e([y()], n14.prototype, "layer", void 0), e([y()], n14.prototype, "type", void 0), n14 = e([a("esri.views.2d.layers.imagery.ImageryTileView2D")], n14);
var l5 = n14;

// node_modules/@arcgis/core/views/2d/engine/imagery/RasterVFTile.js
var r12 = class extends r6 {
  constructor(t7, s16, e14, a13, r13, o12, l6 = null) {
    super(t7, s16, e14, a13, r13, o12), this.tileData = new y3(l6), this.tileData.coordScale = [r13, o12], this.tileData.once("isReady", () => this.ready());
  }
  destroy() {
    super.destroy(), this.tileData.destroy(), this.tileData = null, this.stage = null;
  }
  set stencilRef(t7) {
    this.tileData.stencilRef = t7;
  }
  get stencilRef() {
    return this.tileData.stencilRef;
  }
  _createTransforms() {
    return { dvs: e3(), tileMat3: e3() };
  }
  setTransform(e14) {
    super.setTransform(e14);
    const i11 = this.resolution / (e14.resolution * e14.pixelRatio), a13 = this.transforms.tileMat3, [r13, o12] = this.tileData.offset, l6 = [this.x + r13 * this.resolution, this.y - o12 * this.resolution], [h8, n16] = e14.toScreenNoRotation([0, 0], l6), { symbolTileSize: f7 } = this.tileData.symbolizerParameters, m8 = Math.round((this.width - this.tileData.offset[0]) / f7) * f7, c5 = Math.round((this.height - this.tileData.offset[1]) / f7) * f7, u5 = m8 / this.rangeX * i11, D2 = c5 / this.rangeY * i11;
    s3(a13, u5, 0, 0, 0, D2, 0, h8, n16, 1), i(this.transforms.dvs, e14.displayViewMat3, a13), this.tileData.transforms.dvs = this.transforms.dvs;
  }
  onAttach() {
    this.tileData.stage = this.stage;
  }
  onDetach() {
    this.tileData.stage = null;
  }
};

// node_modules/@arcgis/core/views/2d/engine/imagery/RasterVFTileContainer.js
var o11 = class extends i2 {
  constructor() {
    super(...arguments), this.isCustomTilingScheme = false, this.symbolTypes = ["triangle"];
  }
  createTile(s16) {
    const t7 = this._tileInfoView.getTileBounds(u(), s16), [i11, o12] = this._tileInfoView.tileInfo.size, n16 = this._tileInfoView.getTileResolution(s16.level);
    return new r12(s16, n16, t7[0], t7[3], i11, o12);
  }
  prepareRenderPasses(e14) {
    const r13 = e14.registerRenderPass({ name: "imagery (vf tile)", brushes: [m3], target: () => this.children.map((e15) => e15.tileData), drawPhase: T.MAP });
    return [...super.prepareRenderPasses(e14), r13];
  }
  doRender(e14) {
    this.visible && e14.drawPhase === T.MAP && this.symbolTypes.forEach((s16) => {
      e14.renderPass = s16, super.doRender(e14);
    });
  }
};

// node_modules/@arcgis/core/views/2d/layers/imagery/VectorFieldTileView2D.js
var c4 = class extends v2 {
  constructor() {
    super(...arguments), this._handle = null, this.type = "rasterVF";
  }
  canUseWebGLForProcessing() {
    return false;
  }
  async fetchTile(e14, t7) {
    t7 = { ...t7, interpolation: "nearest", requestProjectedLocalDirections: true };
    const i11 = await this.layer.fetchTile(e14.level, e14.row, e14.col, t7);
    return "vector-magdir" === this.layer.rasterInfo.dataType && (i11 == null ? void 0 : i11.pixelBlock) && (i11.pixelBlock = await this.layer.convertVectorFieldData(i11.pixelBlock, t7)), i11;
  }
  updateTileSource(e14, i11) {
    const r13 = i11.symbolizerParams, { tileData: o12 } = e14;
    o12.key = e14.key, o12.width = this._tileInfoView.tileInfo.size[0], o12.height = this._tileInfoView.tileInfo.size[1];
    const { symbolTileSize: s16 } = r13, { source: l6 } = i11;
    if (o12.offset = this._getTileSymbolOffset(o12.key, s16), r(l6) && r(l6.pixelBlock)) {
      const e15 = { extent: l6.extent, pixelBlock: l6.pixelBlock };
      o12.rawPixelData = e15, o12.symbolizerParameters = r13, o12.source = this._sampleVectorFieldData(l6.pixelBlock, r13, o12.offset);
    } else {
      const e15 = [Math.round((this._tileInfoView.tileInfo[0] - o12.offset[0]) / s16), Math.round((this._tileInfoView.tileInfo[1] - o12.offset[1]) / s16)], t7 = this.createEmptyTilePixelBlock(e15);
      o12.source = t7, o12.symbolizerParameters = r13;
    }
    return o12.invalidateVAO(), Promise.resolve();
  }
  updateTileSymbolizerParameters(e14, i11) {
    var _a;
    const r13 = i11.local, { symbolTileSize: o12 } = r13, { tileData: s16 } = e14;
    s16.offset = this._getTileSymbolOffset(s16.key, o12);
    const l6 = s16.symbolizerParameters.symbolTileSize;
    s16.symbolizerParameters = r13;
    const a13 = (_a = s16.rawPixelData) == null ? void 0 : _a.pixelBlock;
    return r(a13) && l6 !== o12 && (s16.source = this._sampleVectorFieldData(a13, s16.symbolizerParameters, s16.offset)), Promise.resolve();
  }
  attach() {
    super.attach(), this.container = new o11(this._tileInfoView), this.container.isCustomTilingScheme = this._isCustomTilingScheme, this._updateSymbolType(this.layer.renderer), this._handle = l(() => this.layer.renderer, (e14) => this._updateSymbolType(e14));
  }
  detach() {
    var _a;
    super.detach(), this.container.removeAllChildren(), (_a = this._handle) == null ? void 0 : _a.remove(), this._handle = null, this.container = null;
  }
  _getTileSymbolOffset(e14, t7) {
    const i11 = e14.col * this._tileInfoView.tileInfo.size[0] % t7, r13 = e14.row * this._tileInfoView.tileInfo.size[1] % t7;
    return [i11 > t7 / 2 ? t7 - i11 : -i11, r13 > t7 / 2 ? t7 - r13 : -r13];
  }
  _sampleVectorFieldData(e14, t7, i11) {
    const { symbolTileSize: r13 } = t7;
    return j3(e14, "vector-uv", r13, i11);
  }
  _updateSymbolType(e14) {
    "vector-field" === e14.type && (this.container.symbolTypes = "wind-barb" === e14.style ? ["scalar", "triangle"] : "simple-scalar" === e14.style ? ["scalar"] : ["triangle"]);
  }
};
e([y()], c4.prototype, "container", void 0), e([y()], c4.prototype, "layer", void 0), e([y()], c4.prototype, "type", void 0), c4 = e([a("esri.views.2d.layers.imagery.VectorFieldTileView2D")], c4);
var n15 = c4;

// node_modules/@arcgis/core/views/layers/ImageryTileLayerView.js
var f5 = (f7) => {
  let m8 = class extends f7 {
    constructor() {
      super(...arguments), this._rasterFieldPrefix = "Raster.", this.layer = null, this.view = null, this.tileInfo = null;
    }
    get fullExtent() {
      return this._getfullExtent();
    }
    _getfullExtent() {
      return this.projectFullExtent(this.view.spatialReference);
    }
    get hasTilingEffects() {
      return this.layer.renderer && "dynamicRangeAdjustment" in this.layer.renderer && this.layer.renderer.dynamicRangeAdjustment;
    }
    get datumTransformation() {
      return v(e2(this.layer.fullExtent), this.view.spatialReference, true);
    }
    supportsSpatialReference(e14) {
      return !!this.projectFullExtent(e14);
    }
    projectFullExtent(e14) {
      const t7 = e2(this.layer.fullExtent), r13 = v(t7, e14, false);
      return J(t7, e14, r13);
    }
    async fetchPopupFeatures(e14, o12) {
      const { layer: a13 } = this;
      if (!e14) throw new s2("imageryTileLayerView:fetchPopupFeatures", "Nothing to fetch without area", { layer: a13 });
      const { popupEnabled: n16 } = a13, l6 = s4(a13, o12);
      if (!n16 || t(l6)) throw new s2("imageryTileLayerView:fetchPopupFeatures", "Missing required popupTemplate or popupEnabled", { popupEnabled: n16, popupTemplate: l6 });
      const p2 = [], { value: u5, magdirValue: f8 } = await a13.identify(e14, { timeExtent: this.timeExtent });
      let m9 = "";
      if (u5 && u5.length) {
        m9 = "imagery-tile" === a13.type && a13.hasStandardTime() && null != u5[0] ? u5.map((e16) => a13.getStandardTimeValue(e16)).join(", ") : u5.join(", ");
        const e15 = { ObjectId: 0 };
        e15["Raster.ServicePixelValue"] = m9;
        const r13 = a13.rasterInfo.attributeTable;
        if (r(r13)) {
          const { fields: t7, features: o14 } = r13, s17 = t7.find(({ name: e16 }) => "value" === e16.toLowerCase()), i11 = s17 ? o14.find((e16) => String(e16.attributes[s17.name]) === m9) : null;
          if (i11) {
            for (const r14 in i11.attributes) if (i11.attributes.hasOwnProperty(r14)) {
              e15[this._rasterFieldPrefix + r14] = i11.attributes[r14];
            }
          }
        }
        const o13 = a13.rasterInfo.dataType;
        "vector-magdir" !== o13 && "vector-uv" !== o13 || (e15["Raster.Magnitude"] = f8 == null ? void 0 : f8[0], e15["Raster.Direction"] = f8 == null ? void 0 : f8[1]);
        const s16 = new g2(this.fullExtent.clone(), null, e15);
        s16.layer = a13, s16.sourceLayer = s16.layer, p2.push(s16);
      }
      return p2;
    }
  };
  return e([y()], m8.prototype, "layer", void 0), e([y(b2)], m8.prototype, "timeExtent", void 0), e([y()], m8.prototype, "view", void 0), e([y()], m8.prototype, "fullExtent", null), e([y()], m8.prototype, "tileInfo", void 0), e([y({ readOnly: true })], m8.prototype, "hasTilingEffects", null), e([y()], m8.prototype, "datumTransformation", null), m8 = e([a("esri.views.layers.ImageryTileLayerView")], m8), m8;
};

// node_modules/@arcgis/core/views/2d/layers/ImageryTileLayerView2D.js
var f6 = class extends f5(i3(f3(u3))) {
  constructor() {
    super(...arguments), this._useWebGLForProcessing = true, this._useProgressiveUpdate = true, this.subview = null;
  }
  get useWebGLForProcessing() {
    return this._useWebGLForProcessing;
  }
  set useWebGLForProcessing(e14) {
    this._useWebGLForProcessing = e14, this.subview && "useWebGLForProcessing" in this.subview && (this.subview.useWebGLForProcessing = e14);
  }
  get useProgressiveUpdate() {
    return this._useWebGLForProcessing;
  }
  set useProgressiveUpdate(e14) {
    this._useProgressiveUpdate = e14, this.subview && "useProgressiveUpdate" in this.subview && (this.subview.useProgressiveUpdate = e14);
  }
  update(e14) {
    var _a;
    (_a = this.subview) == null ? void 0 : _a.update(e14), this.notifyChange("updating");
  }
  isUpdating() {
    return !this.subview || this.subview.updating;
  }
  attach() {
    this.layer.increaseRasterJobHandlerUsage(), this._updateSubview(), this.addAttachHandles([l(() => {
      const { layer: e14 } = this;
      return { bandIds: e14.bandIds, renderer: e14.renderer, interpolation: e14.interpolation, multidimensionalDefinition: e14.multidimensionalDefinition, rasterFunction: "imagery-tile" === e14.type ? e14.rasterFunction : null };
    }, (e14, s16) => {
      var _a, _b;
      const i11 = e14.interpolation !== (s16 == null ? void 0 : s16.interpolation) && ("majority" === e14.interpolation || "majority" === (s16 == null ? void 0 : s16.interpolation)) && r7(this.layer), o12 = e14.renderer !== (s16 == null ? void 0 : s16.renderer) && ((_a = s16 == null ? void 0 : s16.renderer) == null ? void 0 : _a.type) !== ((_b = e14.renderer) == null ? void 0 : _b.type);
      o12 && this._updateSubview();
      const a13 = e14.multidimensionalDefinition !== (s16 == null ? void 0 : s16.multidimensionalDefinition), n16 = e14.rasterFunction !== (s16 == null ? void 0 : s16.rasterFunction), u5 = n16 && !this._useWebGLForProcessing, h8 = a13 || i11 || o12 || u5;
      this.subview.redrawOrRefetch({ refetch: h8, reprocess: n16 }).catch((e15) => {
        j(e15) || s.getLogger(this.declaredClass).error(e15);
      }), this.notifyChange("updating");
    }), l(() => this.layer.blendMode ?? "normal", (e14) => {
      this.subview.container.blendMode = e14;
    }, w3), l(() => this.layer.effect ?? null, (e14) => {
      this.subview.container.effect = e14;
    }, w3), l(() => this.layer.multidimensionalSubset ?? null, (e14, s16) => {
      const { multidimensionalDefinition: o12 } = this.layer;
      r(o12) && m2(o12, e14) !== m2(o12, s16) && (this.subview.redrawOrRefetch({ refetch: true }).catch((e15) => {
        j(e15) || s.getLogger(this.declaredClass).error(e15);
      }), this.notifyChange("updating"));
    }, U), l(() => this.timeExtent, () => {
      this.subview.timeExtent = this.timeExtent, this.subview.redrawOrRefetch({ refetch: true }).catch((e14) => {
        j(e14) || s.getLogger(this.declaredClass).error(e14);
      });
    }, h2)]);
  }
  detach() {
    var _a;
    this.layer.decreaseRasterJobHandlerUsage(), this._detachSubview(this.subview), (_a = this.subview) == null ? void 0 : _a.destroy(), this.subview = null;
  }
  moveStart() {
    this.requestUpdate();
  }
  viewChange() {
    this.requestUpdate();
  }
  moveEnd() {
    this.subview.moveEnd();
  }
  async hitTest(e14, t7) {
    return [{ type: "graphic", layer: this.layer, mapPoint: e14, graphic: new g2({ attributes: {}, geometry: e14.clone() }) }];
  }
  doRefresh() {
    return this.subview ? this.subview.doRefresh() : Promise.resolve();
  }
  _updateSubview() {
    var _a;
    const e14 = "vector-field" === this.layer.renderer.type ? "rasterVF" : "flow" === this.layer.renderer.type ? "flow" : "raster";
    if (this.subview) {
      if (this.subview.type === e14) return void this._attachSubview(this.subview);
      this._detachSubview(this.subview), (_a = this.subview) == null ? void 0 : _a.destroy(), this.subview = null;
    }
    const { layer: s16 } = this;
    let t7;
    if (t7 = "rasterVF" === e14 ? new n15({ layer: s16, layerView: this }) : "flow" === e14 ? new h6({ layer: s16, layerView: this }) : new l5({ layer: s16, layerView: this }), "useWebGLForProcessing" in t7 && (t7.useWebGLForProcessing = this._useWebGLForProcessing), "useProgressiveUpdate" in t7 && (t7.useProgressiveUpdate = this._useProgressiveUpdate), "previousLOD" in t7) {
      const { subview: e15 } = this;
      t7.previousLOD = e15 && "previousLOD" in e15 ? e15.previousLOD : null;
    }
    this._attachSubview(t7), this.subview = t7, this.requestUpdate();
  }
  _attachSubview(e14) {
    e14 && !e14.attached && (e14.attach(), e14.attached = true, this.container.addChildAt(e14.container, 0), e14.container.blendMode = this.layer.blendMode, e14.container.effect = this.layer.effect);
  }
  _detachSubview(e14) {
    (e14 == null ? void 0 : e14.attached) && (this.container.removeChild(e14.container), e14.detach(), e14.attached = false);
  }
};
e([y()], f6.prototype, "subview", void 0), e([y()], f6.prototype, "useWebGLForProcessing", null), e([y()], f6.prototype, "useProgressiveUpdate", null), f6 = e([a("esri.views.2d.layers.ImageryTileLayerView2D")], f6);
var L2 = f6;
export {
  L2 as default
};
//# sourceMappingURL=ImageryTileLayerView2D-5S3RYB7N.js.map
