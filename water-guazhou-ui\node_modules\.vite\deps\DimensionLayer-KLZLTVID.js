import {
  c as c2
} from "./chunk-PXLH6GWI.js";
import {
  s as s2
} from "./chunk-5ZZCQR67.js";
import {
  c
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import {
  on
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import "./chunk-VJW7RCN7.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  l as l2,
  w as w3
} from "./chunk-QUHG7NMD.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-37DYRJVQ.js";
import {
  n,
  t as t2
} from "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import {
  l
} from "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import {
  s
} from "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import {
  e as e3,
  o
} from "./chunk-G5KX4JSG.js";
import {
  l as l3
} from "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import {
  u2 as u
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T,
  a
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e as e2,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/analysis/DimensionSimpleStyle.js
var c3 = class extends u(l) {
  constructor(e4) {
    super(e4), this.type = "simple", this.color = new l3("black"), this.lineSize = 2, this.fontSize = 10, this.textColor = new l3("black"), this.textBackgroundColor = new l3([255, 255, 255, 0.6]);
  }
};
e([y({ type: ["simple"], readOnly: true, json: { write: { isRequired: true } } })], c3.prototype, "type", void 0), e([y({ type: l3, nonNullable: true, json: { type: [T], write: { isRequired: true } } })], c3.prototype, "color", void 0), e([y({ type: Number, cast: o, nonNullable: true, range: { min: e3(1) }, json: { write: { isRequired: true } } })], c3.prototype, "lineSize", void 0), e([y({ type: Number, cast: o, nonNullable: true, json: { write: { isRequired: true } } })], c3.prototype, "fontSize", void 0), e([y({ type: l3, nonNullable: true, json: { type: [T], write: { isRequired: true } } })], c3.prototype, "textColor", void 0), e([y({ type: l3, nonNullable: true, json: { type: [T], write: { isRequired: true } } })], c3.prototype, "textBackgroundColor", void 0), c3 = e([a2("esri.analysis.DimensionSimpleStyle")], c3);
var a3 = c3;

// node_modules/@arcgis/core/analysis/dimensionUtils.js
var t3;
!function(t4) {
  t4.Horizontal = "horizontal", t4.Vertical = "vertical", t4.Direct = "direct";
}(t3 || (t3 = {}));
var r3 = [t3.Horizontal, t3.Vertical, t3.Direct];

// node_modules/@arcgis/core/analysis/LengthDimension.js
var l4 = class extends u(l) {
  constructor(o2) {
    super(o2), this.type = "length", this.startPoint = null, this.endPoint = null, this.measureType = t3.Direct, this.offset = 0, this.orientation = 0;
  }
};
e([y({ type: ["length"], json: { write: { isRequired: true } } })], l4.prototype, "type", void 0), e([y({ type: w, json: { write: true } })], l4.prototype, "startPoint", void 0), e([y({ type: w, json: { write: true } })], l4.prototype, "endPoint", void 0), e([y({ type: r3, nonNullable: true, json: { write: { isRequired: true } } })], l4.prototype, "measureType", void 0), e([y({ type: Number, nonNullable: true, json: { write: { isRequired: true } } })], l4.prototype, "offset", void 0), e([y({ type: Number, nonNullable: true, json: { write: { isRequired: true } } }), s((o2) => s2.normalize(a(o2), 0, true))], l4.prototype, "orientation", void 0), l4 = e([a2("esri.analysis.LengthDimension")], l4);
var u2 = l4;

// node_modules/@arcgis/core/analysis/DimensionAnalysis.js
var h = j.ofType(u2);
var g = class extends c2 {
  constructor(e4) {
    super(e4), this.type = "dimension", this.style = new a3(), this.extent = null;
  }
  initialize() {
    this.addHandles(l2(() => this._computeExtent(), (e4) => {
      (t(e4) || t(e4.pending)) && this._set("extent", r(e4) ? e4.extent : null);
    }, w3));
  }
  get dimensions() {
    return this._get("dimensions") || new h();
  }
  set dimensions(e4) {
    this._set("dimensions", n(e4, this.dimensions, h));
  }
  get spatialReference() {
    for (const e4 of this.dimensions) {
      if (r(e4.startPoint)) return e4.startPoint.spatialReference;
      if (r(e4.endPoint)) return e4.endPoint.spatialReference;
    }
    return null;
  }
  get requiredPropertiesForEditing() {
    return this.dimensions.reduce((e4, t4) => (e4.push(t4.startPoint, t4.endPoint), e4), []);
  }
  async waitComputeExtent() {
    const e4 = this._computeExtent();
    return r(e4) ? e2(e4.pending) : Promise.resolve();
  }
  _computeExtent() {
    const e4 = this.spatialReference;
    if (t(e4)) return { pending: null, extent: null };
    const t4 = [];
    for (const s3 of this.dimensions) r(s3.startPoint) && t4.push(s3.startPoint), r(s3.endPoint) && t4.push(s3.endPoint);
    const n2 = on(t4, e4);
    if (r(n2.pending)) return { pending: n2.pending, extent: null };
    let o2 = null;
    return r(n2.geometries) && (o2 = n2.geometries.reduce((e5, t5) => t(e5) ? r(t5) ? w2.fromPoint(t5) : null : r(t5) ? e5.union(w2.fromPoint(t5)) : e5, null)), { pending: null, extent: o2 };
  }
  clear() {
    this.dimensions.removeAll();
  }
};
e([y({ type: ["dimension"] })], g.prototype, "type", void 0), e([y({ cast: t2, type: h, nonNullable: true })], g.prototype, "dimensions", null), e([y({ readOnly: true })], g.prototype, "spatialReference", null), e([y({ types: { key: "type", base: null, typeMap: { simple: a3 } }, nonNullable: true })], g.prototype, "style", void 0), e([y({ value: null, readOnly: true })], g.prototype, "extent", void 0), e([y({ readOnly: true })], g.prototype, "requiredPropertiesForEditing", null), g = e([a2("esri.analysis.DimensionAnalysis")], g);
var j2 = g;

// node_modules/@arcgis/core/layers/DimensionLayer.js
var h2 = class extends c(O(b)) {
  constructor(e4) {
    if (super(e4), this.type = "dimension", this.operationalLayerType = "ArcGISDimensionLayer", this.source = new j2(), this.opacity = 1, e4) {
      const { source: s3, style: r4 } = e4;
      s3 && r4 && (s3.style = r4);
    }
  }
  initialize() {
    this.addHandles([l2(() => this.source, (e4, s3) => {
      r(s3) && s3.parent === this && (s3.parent = null), r(e4) && (e4.parent = this);
    }, w3)]);
  }
  async load() {
    return this.addResolvingPromise(this.source.waitComputeExtent()), this;
  }
  get spatialReference() {
    return e2(this.source.spatialReference);
  }
  get style() {
    return this.source.style;
  }
  set style(e4) {
    this.source.style = e4;
  }
  get fullExtent() {
    return this.source.extent;
  }
  releaseAnalysis(e4) {
    this.source === e4 && (this.source = new j2());
  }
  get analysis() {
    return this.source;
  }
  set analysis(e4) {
    this.source = e4;
  }
  get dimensions() {
    return this.source.dimensions;
  }
  set dimensions(e4) {
    this.source.dimensions = e4;
  }
  writeDimensions(e4, s3, r4, t4) {
    s3.dimensions = e4.filter(({ startPoint: e5, endPoint: s4 }) => r(e5) && r(s4)).map((e5) => e5.toJSON(t4)).toJSON();
  }
};
e([y({ json: { read: false }, readOnly: true })], h2.prototype, "type", void 0), e([y({ type: ["ArcGISDimensionLayer"] })], h2.prototype, "operationalLayerType", void 0), e([y({ nonNullable: true })], h2.prototype, "source", void 0), e([y({ readOnly: true })], h2.prototype, "spatialReference", null), e([y({ types: { key: "type", base: null, typeMap: { simple: a3 } }, json: { write: { ignoreOrigin: true } } })], h2.prototype, "style", null), e([y({ readOnly: true })], h2.prototype, "fullExtent", null), e([y({ readOnly: true, json: { read: false, write: false, origins: { service: { read: false, write: false }, "portal-item": { read: false, write: false }, "web-document": { read: false, write: false } } } })], h2.prototype, "opacity", void 0), e([y({ type: ["show", "hide"] })], h2.prototype, "listMode", void 0), e([y({ type: j.ofType(u2), json: { write: { ignoreOrigin: true }, origins: { "web-scene": { write: { ignoreOrigin: true } } } } })], h2.prototype, "dimensions", null), e([r2("web-scene", "dimensions")], h2.prototype, "writeDimensions", null), h2 = e([a2("esri.layers.DimensionLayer")], h2);
var f = h2;
export {
  f as default
};
//# sourceMappingURL=DimensionLayer-KLZLTVID.js.map
