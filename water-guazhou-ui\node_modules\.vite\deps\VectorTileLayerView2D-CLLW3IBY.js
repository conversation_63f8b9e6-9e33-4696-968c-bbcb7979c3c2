import {
  i as i2
} from "./chunk-JTTSDQPH.js";
import {
  r as r6
} from "./chunk-6DXPU43Z.js";
import "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import {
  e as e7
} from "./chunk-OBW4AQOU.js";
import {
  c,
  e as e6,
  t as t4
} from "./chunk-J6VS6FXY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-VYWZHTOQ.js";
import {
  t as t3
} from "./chunk-5SYMUP5B.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import {
  l as l4
} from "./chunk-EGZXDK64.js";
import {
  E as E6,
  I as I2
} from "./chunk-2B52LX6T.js";
import {
  a as a4,
  i,
  l as l3,
  n as n3,
  r as r4
} from "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import {
  E2 as E5,
  f as f2
} from "./chunk-6G2NLXT7.js";
import {
  t as t2
} from "./chunk-IEBU4QQL.js";
import {
  f as f3
} from "./chunk-NEPFZ7PM.js";
import {
  u as u3
} from "./chunk-HWB4LNSZ.js";
import {
  r as r5
} from "./chunk-QKWIBVLD.js";
import {
  T
} from "./chunk-WAPZ634R.js";
import {
  E as E4
} from "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import {
  C,
  D as D2,
  E as E3,
  F,
  G,
  I,
  O,
  P,
  R
} from "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-VCFT2OOI.js";
import "./chunk-22FAZXOH.js";
import {
  y as y2
} from "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import {
  h as h4
} from "./chunk-MSIU52YL.js";
import {
  l as l2
} from "./chunk-5JCRZXRL.js";
import {
  e as e4
} from "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import {
  e as e5,
  r as r3
} from "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import {
  M,
  f,
  h as h3,
  r as r2
} from "./chunk-ST2RRB55.js";
import "./chunk-O2BYTJI4.js";
import {
  j as j2
} from "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-RURSJOSG.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import {
  n as n2
} from "./chunk-TNP2LXZZ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import {
  u as u2
} from "./chunk-IKGI4J4I.js";
import "./chunk-MNWHGD3K.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  e as e3
} from "./chunk-64RWCMSJ.js";
import {
  a as a3,
  m
} from "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import {
  h as h2,
  l
} from "./chunk-QUHG7NMD.js";
import {
  g
} from "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import {
  u
} from "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  E as E2
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import {
  Et
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  D,
  E,
  b,
  j
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  p
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  a,
  e as e2,
  h,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/RectangleBinPack.js
var e8 = class {
  constructor(e9, t9) {
    this._width = 0, this._height = 0, this._free = [], this._width = e9, this._height = t9, this._free.push(new t3(0, 0, e9, t9));
  }
  get width() {
    return this._width;
  }
  get height() {
    return this._height;
  }
  allocate(e9, t9) {
    if (e9 > this._width || t9 > this._height) return new t3();
    let i7 = null, s6 = -1;
    for (let h7 = 0; h7 < this._free.length; ++h7) {
      const w = this._free[h7];
      e9 <= w.width && t9 <= w.height && (null === i7 || w.y <= i7.y && w.x <= i7.x) && (i7 = w, s6 = h7);
    }
    return null === i7 ? new t3() : (this._free.splice(s6, 1), i7.width < i7.height ? (i7.width > e9 && this._free.push(new t3(i7.x + e9, i7.y, i7.width - e9, t9)), i7.height > t9 && this._free.push(new t3(i7.x, i7.y + t9, i7.width, i7.height - t9))) : (i7.width > e9 && this._free.push(new t3(i7.x + e9, i7.y, i7.width - e9, i7.height)), i7.height > t9 && this._free.push(new t3(i7.x, i7.y + t9, e9, i7.height - t9))), new t3(i7.x, i7.y, e9, t9));
  }
  release(h7) {
    for (let e9 = 0; e9 < this._free.length; ++e9) {
      const t9 = this._free[e9];
      if (t9.y === h7.y && t9.height === h7.height && t9.x + t9.width === h7.x) t9.width += h7.width;
      else if (t9.x === h7.x && t9.width === h7.width && t9.y + t9.height === h7.y) t9.height += h7.height;
      else if (h7.y === t9.y && h7.height === t9.height && h7.x + h7.width === t9.x) t9.x = h7.x, t9.width += h7.width;
      else {
        if (h7.x !== t9.x || h7.width !== t9.width || h7.y + h7.height !== t9.y) continue;
        t9.y = h7.y, t9.height += h7.height;
      }
      this._free.splice(e9, 1), this.release(h7);
    }
    this._free.push(h7);
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/GlyphMosaic.js
var r7 = class {
  constructor(e9, i7, s6) {
    this.width = 0, this.height = 0, this._dirties = [], this._glyphData = [], this._currentPage = 0, this._glyphIndex = {}, this._textures = [], this._rangePromises = /* @__PURE__ */ new Map(), this.width = e9, this.height = i7, this._glyphSource = s6, this._binPack = new e8(e9 - 4, i7 - 4), this._glyphData.push(new Uint8Array(e9 * i7)), this._dirties.push(true), this._textures.push(void 0);
  }
  getGlyphItems(i7, s6) {
    const h7 = [], r12 = this._glyphSource, n9 = /* @__PURE__ */ new Set(), a7 = 1 / 256;
    for (const t9 of s6) {
      const e9 = Math.floor(t9 * a7);
      n9.add(e9);
    }
    const o5 = [];
    return n9.forEach((t9) => {
      if (t9 <= 256) {
        const e9 = i7 + t9;
        if (this._rangePromises.has(e9)) o5.push(this._rangePromises.get(e9));
        else {
          const s7 = r12.getRange(i7, t9).then(() => {
            this._rangePromises.delete(e9);
          }, () => {
            this._rangePromises.delete(e9);
          });
          this._rangePromises.set(e9, s7), o5.push(s7);
        }
      }
    }), Promise.all(o5).then(() => {
      let n10 = this._glyphIndex[i7];
      n10 || (n10 = {}, this._glyphIndex[i7] = n10);
      for (const a8 of s6) {
        const s7 = n10[a8];
        if (s7) {
          h7[a8] = { sdf: true, rect: s7.rect, metrics: s7.metrics, page: s7.page, code: a8 };
          continue;
        }
        const o6 = r12.getGlyph(i7, a8);
        if (!o6 || !o6.metrics) continue;
        const l10 = o6.metrics;
        let c4;
        if (0 === l10.width) c4 = new t3(0, 0, 0, 0);
        else {
          const e9 = 3, i8 = l10.width + 2 * e9, s8 = l10.height + 2 * e9;
          let h8 = i8 % 4 ? 4 - i8 % 4 : 4, r13 = s8 % 4 ? 4 - s8 % 4 : 4;
          1 === h8 && (h8 = 5), 1 === r13 && (r13 = 5), c4 = this._binPack.allocate(i8 + h8, s8 + r13), c4.isEmpty && (this._dirties[this._currentPage] || (this._glyphData[this._currentPage] = null), this._currentPage = this._glyphData.length, this._glyphData.push(new Uint8Array(this.width * this.height)), this._dirties.push(true), this._textures.push(void 0), this._binPack = new e8(this.width - 4, this.height - 4), c4 = this._binPack.allocate(i8 + h8, s8 + r13));
          const n11 = this._glyphData[this._currentPage], a9 = o6.bitmap;
          let g3, _3;
          if (a9) for (let t9 = 0; t9 < s8; t9++) {
            g3 = i8 * t9, _3 = this.width * (c4.y + t9 + 1) + c4.x;
            for (let t10 = 0; t10 < i8; t10++) n11[_3 + t10 + 1] = a9[g3 + t10];
          }
        }
        n10[a8] = { rect: c4, metrics: l10, tileIDs: null, page: this._currentPage }, h7[a8] = { sdf: true, rect: c4, metrics: l10, page: this._currentPage, code: a8 }, this._dirties[this._currentPage] = true;
      }
      return h7;
    });
  }
  removeGlyphs(t9) {
    for (const e9 in this._glyphIndex) {
      const i7 = this._glyphIndex[e9];
      if (!i7) continue;
      let s6;
      for (const e10 in i7) if (s6 = i7[e10], s6.tileIDs.delete(t9), 0 === s6.tileIDs.size) {
        const t10 = this._glyphData[s6.page], h7 = s6.rect;
        let r12, n9;
        for (let e11 = 0; e11 < h7.height; e11++) for (r12 = this.width * (h7.y + e11) + h7.x, n9 = 0; n9 < h7.width; n9++) t10[r12 + n9] = 0;
        delete i7[e10], this._dirties[s6.page] = true;
      }
    }
  }
  bind(t9, e9, r12, n9 = 0) {
    this._textures[r12] || (this._textures[r12] = new E4(t9, { pixelFormat: P.ALPHA, dataType: G.UNSIGNED_BYTE, width: this.width, height: this.height }, new Uint8Array(this.width * this.height)));
    const a7 = this._textures[r12];
    a7.setSamplingMode(e9), this._dirties[r12] && a7.setData(this._glyphData[r12]), t9.bindTexture(a7, n9), this._dirties[r12] = false;
  }
  dispose() {
    this._binPack = null;
    for (const t9 of this._textures) t9 && t9.dispose();
    this._textures.length = 0;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/GlyphSource.js
var s2 = class {
  constructor(e9) {
    if (this._metrics = [], this._bitmaps = [], e9) for (; e9.next(); ) switch (e9.tag()) {
      case 1: {
        const t9 = e9.getMessage();
        for (; t9.next(); ) switch (t9.tag()) {
          case 3: {
            const e10 = t9.getMessage();
            let s6, a7, r12, n9, i7, c4, g3;
            for (; e10.next(); ) switch (e10.tag()) {
              case 1:
                s6 = e10.getUInt32();
                break;
              case 2:
                a7 = e10.getBytes();
                break;
              case 3:
                r12 = e10.getUInt32();
                break;
              case 4:
                n9 = e10.getUInt32();
                break;
              case 5:
                i7 = e10.getSInt32();
                break;
              case 6:
                c4 = e10.getSInt32();
                break;
              case 7:
                g3 = e10.getUInt32();
                break;
              default:
                e10.skip();
            }
            e10.release(), s6 && (this._metrics[s6] = { width: r12, height: n9, left: i7, top: c4, advance: g3 }, this._bitmaps[s6] = a7);
            break;
          }
          default:
            t9.skip();
        }
        t9.release();
        break;
      }
      default:
        e9.skip();
    }
  }
  getMetrics(e9) {
    return this._metrics[e9];
  }
  getBitmap(e9) {
    return this._bitmaps[e9];
  }
};
var a5 = class {
  constructor() {
    this._ranges = [];
  }
  getRange(e9) {
    return this._ranges[e9];
  }
  addRange(e9, t9) {
    this._ranges[e9] = t9;
  }
};
var r8 = class {
  constructor(e9) {
    this._glyphInfo = {}, this._baseURL = e9;
  }
  getRange(a7, r12) {
    const n9 = this._getFontStack(a7);
    if (n9.getRange(r12)) return Promise.resolve();
    const i7 = 256 * r12, c4 = i7 + 255;
    if (this._baseURL) {
      const g3 = this._baseURL.replace("{fontstack}", a7).replace("{range}", i7 + "-" + c4);
      return U(g3, { responseType: "array-buffer" }).then((e9) => {
        n9.addRange(r12, new s2(new n2(new Uint8Array(e9.data), new DataView(e9.data))));
      }).catch(() => {
        n9.addRange(r12, new s2());
      });
    }
    return n9.addRange(r12, new s2()), Promise.resolve();
  }
  getGlyph(e9, t9) {
    const s6 = this._getFontStack(e9);
    if (!s6) return;
    const a7 = Math.floor(t9 / 256);
    if (a7 > 256) return;
    const r12 = s6.getRange(a7);
    return r12 ? { metrics: r12.getMetrics(t9), bitmap: r12.getBitmap(t9) } : void 0;
  }
  _getFontStack(e9) {
    let t9 = this._glyphInfo[e9];
    return t9 || (t9 = this._glyphInfo[e9] = new a5()), t9;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/SpriteMosaic.js
var o = "dasharray-";
var n4 = class _n {
  constructor(t9, e9, s6 = 0) {
    this._size = [], this._mosaicsData = [], this._textures = [], this._dirties = [], this._maxItemSize = 0, this._currentPage = 0, this._pageWidth = 0, this._pageHeight = 0, this._mosaicRects = {}, this.pixelRatio = 1, (t9 <= 0 || e9 <= 0) && console.error("Sprites mosaic defaultWidth and defaultHeight must be greater than zero!"), this._pageWidth = t9, this._pageHeight = e9, s6 > 0 && (this._maxItemSize = s6), this._binPack = new e8(t9 - 4, e9 - 4);
  }
  dispose() {
    this._binPack = null, this._mosaicRects = {};
    for (const t9 of this._textures) t9 && t9.dispose();
    this._textures.length = 0;
  }
  getWidth(t9) {
    return t9 >= this._size.length ? -1 : this._size[t9][0];
  }
  getHeight(t9) {
    return t9 >= this._size.length ? -1 : this._size[t9][1];
  }
  getPageSize(t9) {
    return t9 >= this._size.length ? null : this._size[t9];
  }
  setSpriteSource(t9) {
    if (this.dispose(), this.pixelRatio = t9.devicePixelRatio, 0 === this._mosaicsData.length) {
      this._binPack = new e8(this._pageWidth - 4, this._pageHeight - 4);
      const t10 = Math.floor(this._pageWidth), e9 = Math.floor(this._pageHeight), s6 = new Uint32Array(t10 * e9);
      this._mosaicsData[0] = s6, this._dirties.push(true), this._size.push([this._pageWidth, this._pageHeight]), this._textures.push(void 0);
    }
    this._sprites = t9;
  }
  getSpriteItem(t9, i7 = false) {
    let e9, s6, h7 = this._mosaicRects[t9];
    if (h7) return h7;
    if (!this._sprites || "loaded" !== this._sprites.loadStatus) return null;
    if (t9 && t9.startsWith(o) ? ([e9, s6] = this._rasterizeDash(t9), i7 = true) : e9 = this._sprites.getSpriteInfo(t9), !e9 || !e9.width || !e9.height || e9.width < 0 || e9.height < 0) return null;
    const a7 = e9.width, r12 = e9.height, [n9, _3, g3] = this._allocateImage(a7, r12);
    return n9.width <= 0 ? null : (this._copy(n9, e9, _3, g3, i7, s6), h7 = { rect: n9, width: a7, height: r12, sdf: e9.sdf, simplePattern: false, pixelRatio: e9.pixelRatio, page: _3 }, this._mosaicRects[t9] = h7, h7);
  }
  getSpriteItems(t9) {
    const i7 = {};
    for (const e9 of t9) i7[e9.name] = this.getSpriteItem(e9.name, e9.repeat);
    return i7;
  }
  getMosaicItemPosition(t9, i7) {
    const e9 = this.getSpriteItem(t9, i7), s6 = e9 && e9.rect;
    if (!s6) return null;
    s6.width = e9.width, s6.height = e9.height;
    const h7 = e9.width, a7 = e9.height, r12 = 2;
    return { tl: [s6.x + r12, s6.y + r12], br: [s6.x + r12 + h7, s6.y + r12 + a7], page: e9.page };
  }
  bind(t9, i7, e9 = 0, o5 = 0) {
    if (e9 >= this._size.length || e9 >= this._mosaicsData.length) return;
    this._textures[e9] || (this._textures[e9] = new E4(t9, { pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, width: this._size[e9][0], height: this._size[e9][1] }, new Uint8Array(this._mosaicsData[e9].buffer)));
    const n9 = this._textures[e9];
    n9.setSamplingMode(i7), this._dirties[e9] && n9.setData(new Uint8Array(this._mosaicsData[e9].buffer)), t9.bindTexture(n9, o5), this._dirties[e9] = false;
  }
  static _copyBits(t9, i7, e9, s6, h7, a7, r12, o5, n9, _3, g3) {
    let c4 = s6 * i7 + e9, l10 = o5 * a7 + r12;
    if (g3) {
      l10 -= a7;
      for (let r13 = -1; r13 <= _3; r13++, c4 = ((r13 + _3) % _3 + s6) * i7 + e9, l10 += a7) for (let i8 = -1; i8 <= n9; i8++) h7[l10 + i8] = t9[c4 + (i8 + n9) % n9];
    } else for (let p2 = 0; p2 < _3; p2++) {
      for (let i8 = 0; i8 < n9; i8++) h7[l10 + i8] = t9[c4 + i8];
      c4 += i7, l10 += a7;
    }
  }
  _copy(t9, i7, e9, s6, h7, a7) {
    if (!this._sprites || "loaded" !== this._sprites.loadStatus || e9 >= this._mosaicsData.length) return;
    const r12 = new Uint32Array(a7 ? a7.buffer : this._sprites.image.buffer), o5 = this._mosaicsData[e9];
    o5 && r12 || console.error("Source or target images are uninitialized!");
    const _3 = 2, g3 = a7 ? i7.width : this._sprites.width;
    _n._copyBits(r12, g3, i7.x, i7.y, o5, s6[0], t9.x + _3, t9.y + _3, i7.width, i7.height, h7), this._dirties[e9] = true;
  }
  _allocateImage(t9, s6) {
    t9 += 2, s6 += 2;
    const h7 = Math.max(t9, s6);
    if (this._maxItemSize && this._maxItemSize < h7) {
      const i7 = new t3(0, 0, t9, s6);
      return this._mosaicsData.push(new Uint32Array(t9 * s6)), this._dirties.push(true), this._size.push([t9, s6]), this._textures.push(void 0), [i7, this._mosaicsData.length - 1, [t9, s6]];
    }
    let a7 = t9 % 4 ? 4 - t9 % 4 : 4, r12 = s6 % 4 ? 4 - s6 % 4 : 4;
    1 === a7 && (a7 = 5), 1 === r12 && (r12 = 5);
    const o5 = this._binPack.allocate(t9 + a7, s6 + r12);
    return o5.width <= 0 ? (this._dirties[this._currentPage] || (this._mosaicsData[this._currentPage] = null), this._currentPage = this._mosaicsData.length, this._mosaicsData.push(new Uint32Array(this._pageWidth * this._pageHeight)), this._dirties.push(true), this._size.push([this._pageWidth, this._pageHeight]), this._textures.push(void 0), this._binPack = new e8(this._pageWidth - 4, this._pageHeight - 4), this._allocateImage(t9, s6)) : [o5, this._currentPage, [this._pageWidth, this._pageHeight]];
  }
  _rasterizeDash(i7) {
    const e9 = /\[(.*?)\]/, s6 = i7.match(e9);
    if (!s6) return null;
    const h7 = s6[1].split(",").map(Number), a7 = i7.slice(i7.lastIndexOf("-") + 1), [r12, o5, n9] = e7(h7, a7);
    return [{ x: 0, y: 0, width: o5, height: n9, sdf: true, pixelRatio: 1 }, new Uint8Array(r12.buffer)];
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/TileHandler.js
var n5 = class {
  constructor(e9, t9, s6) {
    this._layer = e9, this._styleRepository = t9, this.devicePixelRatio = s6, this._spriteMosaic = null, this._glyphMosaic = null, this._connection = null;
  }
  destroy() {
    var _a;
    (_a = this._connection) == null ? void 0 : _a.close(), this._connection = null, this._styleRepository = null, this._layer = null, this._spriteMosaic = null, this._glyphMosaic = null;
  }
  get spriteMosaic() {
    return this._spriteSourcePromise.then(() => this._spriteMosaic);
  }
  get glyphMosaic() {
    return this._glyphMosaic;
  }
  async start(t9) {
    this._spriteSourcePromise = this._layer.loadSpriteSource(this.devicePixelRatio, t9), this._spriteSourcePromise.then((e9) => {
      this._spriteMosaic = new n4(1024, 1024, 250), this._spriteMosaic.setSpriteSource(e9);
    });
    const s6 = this._layer.currentStyleInfo.glyphsUrl, c4 = new r8(s6 ? Et(s6, { ...this._layer.customParameters, token: this._layer.apiKey }) : null);
    this._glyphMosaic = new r7(1024, 1024, c4), this._broadcastPromise = u2("WorkerTileHandler", { client: this, schedule: t9.schedule, signal: t9.signal }).then((s7) => {
      if (this._connection = s7, this._layer && !this._connection.closed) {
        const r12 = s7.broadcast("setStyle", this._layer.currentStyleInfo.style, t9);
        Promise.all(r12).catch((t10) => b(t10));
      }
    });
  }
  async updateStyle(e9) {
    return await this._broadcastPromise, this._broadcastPromise = Promise.all(this._connection.broadcast("updateStyle", e9)), this._broadcastPromise;
  }
  setSpriteSource(e9) {
    const t9 = new n4(1024, 1024, 250);
    return t9.setSpriteSource(e9), this._spriteMosaic = t9, this._spriteSourcePromise = Promise.resolve(e9), t9;
  }
  async setStyle(e9, t9) {
    await this._broadcastPromise, this._styleRepository = e9, this._spriteSourcePromise = this._layer.loadSpriteSource(this.devicePixelRatio, null), this._spriteSourcePromise.then((e10) => {
      this._spriteMosaic = new n4(1024, 1024, 250), this._spriteMosaic.setSpriteSource(e10);
    });
    const s6 = new r8(this._layer.currentStyleInfo.glyphsUrl ? Et(this._layer.currentStyleInfo.glyphsUrl, { ...this._layer.customParameters, token: this._layer.apiKey }) : null);
    return this._glyphMosaic = new r7(1024, 1024, s6), this._broadcastPromise = Promise.all(this._connection.broadcast("setStyle", t9)), this._broadcastPromise;
  }
  fetchTileData(e9, t9) {
    return this._getRefKeys(e9, t9).then((e10) => {
      const s6 = this._layer.sourceNameToSource, r12 = [];
      for (const t10 in s6) r12.push(t10);
      return this._getSourcesData(r12, e10, t9);
    });
  }
  parseTileData(e9, t9) {
    const s6 = e9 && e9.data;
    if (!s6) return Promise.resolve(null);
    const { sourceName2DataAndRefKey: r12, transferList: i7 } = s6;
    return 0 === Object.keys(r12).length ? Promise.resolve(null) : this._broadcastPromise.then(() => this._connection.invoke("createTileAndParse", { key: e9.key.id, sourceName2DataAndRefKey: r12, styleLayerUIDs: e9.styleLayerUIDs }, { ...t9, transferList: i7 }));
  }
  async getSprites(e9) {
    return await this._spriteSourcePromise, this._spriteMosaic.getSpriteItems(e9);
  }
  getGlyphs(e9) {
    return this._glyphMosaic.getGlyphItems(e9.font, e9.codePoints);
  }
  async _getTilePayload(e9, s6, r12) {
    const i7 = e4.pool.acquire(e9.id), o5 = this._layer.sourceNameToSource[s6], { level: a7, row: l10, col: n9 } = i7;
    e4.pool.release(i7);
    try {
      return { protobuff: await o5.requestTile(a7, l10, n9, r12), sourceName: s6 };
    } catch (h7) {
      if (j(h7)) throw h7;
      return { protobuff: null, sourceName: s6 };
    }
  }
  _getRefKeys(e9, t9) {
    const r12 = this._layer.sourceNameToSource, i7 = new Array();
    for (const s6 in r12) {
      const o5 = r12[s6].getRefKey(e9, t9);
      i7.push(o5);
    }
    return E(i7);
  }
  _getSourcesData(e9, t9, r12) {
    const i7 = [];
    for (let s6 = 0; s6 < t9.length; s6++) if (null == t9[s6].value || null == e9[s6]) i7.push(null);
    else {
      const o5 = this._getTilePayload(t9[s6].value, e9[s6], r12);
      i7.push(o5);
    }
    return E(i7).then((e10) => {
      const s6 = {}, r13 = [];
      for (let i8 = 0; i8 < e10.length; i8++) {
        const o5 = e10[i8].value;
        if (o5 && (o5.protobuff && o5.protobuff.byteLength > 0)) {
          const e11 = t9[i8].value.id;
          s6[o5.sourceName] = { refKey: e11, protobuff: o5.protobuff }, r13.push(o5.protobuff);
        }
      }
      return { sourceName2DataAndRefKey: s6, transferList: r13 };
    });
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/TileManager.js
var r9 = 512;
var o2 = 1e-6;
var n6 = (e9, i7) => e9 + 1 / (1 << 2 * i7);
var a6 = class {
  constructor(i7, t9) {
    this._tiles = /* @__PURE__ */ new Map(), this._tileCache = new e3(40, (e9) => e9.dispose()), this._viewSize = [0, 0], this._visibleTiles = /* @__PURE__ */ new Map(), this.acquireTile = i7.acquireTile, this.releaseTile = i7.releaseTile, this.tileInfoView = i7.tileInfoView, this._container = t9;
  }
  destroy() {
    for (const [e9, i7] of this._tiles) i7.dispose();
    this._tiles = null, this._tileCache.clear(), this._tileCache = null;
  }
  update(e9) {
    this._updateCacheSize(e9);
    const i7 = this.tileInfoView, t9 = i7.getTileCoverage(e9.state, 0, "smallest"), { spans: r12, lodInfo: o5 } = t9, { level: n9 } = o5, a7 = this._tiles, c4 = /* @__PURE__ */ new Set(), h7 = /* @__PURE__ */ new Set();
    for (const { row: s6, colFrom: _3, colTo: f5 } of r12) for (let e10 = _3; e10 <= f5; e10++) {
      const i8 = e4.getId(n9, s6, o5.normalizeCol(e10), o5.getWorldForColumn(e10)), t10 = this._getOrAcquireTile(i8);
      c4.add(i8), t10.processed() ? this._addToContainer(t10) : h7.add(new e4(i8));
    }
    for (const [s6, l10] of a7) l10.isCoverage = c4.has(s6);
    for (const s6 of h7) this._findPlaceholdersForMissingTiles(s6, c4);
    let d2 = false;
    for (const [s6, l10] of a7) l10.neededForCoverage = c4.has(s6), l10.neededForCoverage || l10.isHoldingForFade && i7.intersects(t9, l10.key) && c4.add(s6), l10.isFading && (d2 = true);
    for (const [s6, l10] of this._tiles) c4.has(s6) || this._releaseTile(s6);
    return l2.pool.release(t9), !d2;
  }
  clear() {
    this._tiles.clear(), this._tileCache.clear(), this._visibleTiles.clear();
  }
  clearCache() {
    this._tileCache.clear();
  }
  _findPlaceholdersForMissingTiles(e9, i7) {
    const t9 = [];
    for (const [l10, r12] of this._tiles) this._addPlaceholderChild(t9, r12, e9, i7);
    const s6 = t9.reduce(n6, 0);
    Math.abs(1 - s6) < o2 || this._addPlaceholderParent(e9.id, i7);
  }
  _addPlaceholderChild(e9, i7, t9, s6) {
    i7.key.level <= t9.level || !i7.hasData() || h5(t9, i7.key) && (this._addToContainer(i7), s6.add(i7.id), e9.push(i7.key.level - t9.level));
  }
  _addPlaceholderParent(e9, i7) {
    const t9 = this._tiles;
    let s6 = e9;
    for (; ; ) {
      if (s6 = c2(s6), !s6 || i7.has(s6)) return;
      const e10 = t9.get(s6);
      if (e10 && e10.hasData()) return this._addToContainer(e10), void i7.add(e10.id);
    }
  }
  _getOrAcquireTile(e9) {
    let i7 = this._tiles.get(e9);
    return i7 || (i7 = this._tileCache.pop(e9), i7 || (i7 = this.acquireTile(new e4(e9))), this._tiles.set(e9, i7), i7);
  }
  _releaseTile(e9) {
    const i7 = this._tiles.get(e9);
    this.releaseTile(i7), this._removeFromContainer(i7), this._tiles.delete(e9), i7.hasData() ? this._tileCache.put(e9, i7, 1) : i7.dispose();
  }
  _addToContainer(e9) {
    let s6;
    const l10 = [], r12 = this._container;
    if (r12.contains(e9)) return;
    const o5 = this._visibleTiles;
    for (const [t9, n9] of o5) this._canConnectDirectly(e9, n9) && l10.push(n9), t(s6) && this._canConnectDirectly(n9, e9) && (s6 = n9);
    if (r(s6)) {
      for (const i7 of l10) s6.childrenTiles.delete(i7), e9.childrenTiles.add(i7), i7.parentTile = e9;
      s6.childrenTiles.add(e9), e9.parentTile = s6;
    } else for (const i7 of l10) e9.childrenTiles.add(i7), i7.parentTile = e9;
    o5.set(e9.id, e9), r12.addChild(e9);
  }
  _removeFromContainer(e9) {
    if (this._visibleTiles.delete(e9.id), this._container.removeChild(e9), r(e9.parentTile)) {
      e9.parentTile.childrenTiles.delete(e9);
      for (const i7 of e9.childrenTiles) r(e9.parentTile) && e9.parentTile.childrenTiles.add(i7);
    }
    for (const i7 of e9.childrenTiles) i7.parentTile = e9.parentTile;
    e9.parentTile = null, e9.childrenTiles.clear();
  }
  _canConnectDirectly(e9, i7) {
    const t9 = e9.key;
    let { level: s6, row: l10, col: r12, world: o5 } = i7.key;
    const n9 = this._visibleTiles;
    for (; s6 > 0; ) {
      if (s6--, l10 >>= 1, r12 >>= 1, t9.level === s6 && t9.row === l10 && t9.col === r12 && t9.world === o5) return true;
      if (n9.has(`${s6}/${l10}/${r12}/${o5}`)) return false;
    }
    return false;
  }
  _updateCacheSize(e9) {
    const i7 = e9.state.size;
    if (i7[0] === this._viewSize[0] && i7[1] === this._viewSize[1]) return;
    const t9 = Math.ceil(i7[0] / r9) + 1, s6 = Math.ceil(i7[1] / r9) + 1;
    this._viewSize[0] = i7[0], this._viewSize[1] = i7[1], this._tileCache.maxSize = 5 * t9 * s6;
  }
};
function c2(e9) {
  const [i7, t9, s6, l10] = e9.split("/"), r12 = parseInt(i7, 10);
  return 0 === r12 ? null : `${r12 - 1}/${parseInt(t9, 10) >> 1}/${parseInt(s6, 10) >> 1}/${parseInt(l10, 10)}`;
}
function h5(e9, i7) {
  const t9 = i7.level - e9.level;
  return e9.row === i7.row >> t9 && e9.col === i7.col >> t9 && e9.world === i7.world;
}

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/decluttering/core.js
var t5 = class {
  constructor(t9) {
    this.xTile = 0, this.yTile = 0, this.hash = 0, this.priority = 1, this.colliders = [], this.textVertexRanges = [], this.iconVertexRanges = [], this.tile = t9;
  }
};
var s3 = class {
  constructor() {
    this.tileSymbols = [], this.parts = [{ startTime: 0, startOpacity: 0, targetOpacity: 0, show: false }, { startTime: 0, startOpacity: 0, targetOpacity: 0, show: false }], this.show = false;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/decluttering/util.js
function s4(t9, e9, s6, o5, l10, i7) {
  const r12 = s6 - l10;
  if (r12 >= 0) return (e9 >> r12) + (o5 - (i7 << r12)) * (t9 >> r12);
  const n9 = -r12;
  return e9 - (i7 - (o5 << n9)) * (t9 >> n9) << n9;
}
var o3 = class {
  constructor(t9, e9, s6) {
    this._rows = Math.ceil(e9 / s6), this._columns = Math.ceil(t9 / s6), this._cellSize = s6, this.cells = new Array(this._rows);
    for (let o5 = 0; o5 < this._rows; o5++) {
      this.cells[o5] = new Array(this._columns);
      for (let t10 = 0; t10 < this._columns; t10++) this.cells[o5][t10] = [];
    }
  }
  getCell(t9, e9) {
    const s6 = Math.min(Math.max(Math.floor(e9 / this._cellSize), 0), this._rows - 1), o5 = Math.min(Math.max(Math.floor(t9 / this._cellSize), 0), this._columns - 1);
    return this.cells[s6] && this.cells[s6][o5] || null;
  }
  getCellSpan(t9, e9, s6, o5) {
    return [Math.min(Math.max(Math.floor(t9 / this._cellSize), 0), this.columns - 1), Math.min(Math.max(Math.floor(e9 / this._cellSize), 0), this.rows - 1), Math.min(Math.max(Math.floor(s6 / this._cellSize), 0), this.columns - 1), Math.min(Math.max(Math.floor(o5 / this._cellSize), 0), this.rows - 1)];
  }
  get cellSize() {
    return this._cellSize;
  }
  get columns() {
    return this._columns;
  }
  get rows() {
    return this._rows;
  }
};
function l5(t9, s6, o5, l10, i7, r12) {
  const n9 = s6[l10++];
  for (let a7 = 0; a7 < n9; a7++) {
    const n10 = new t5(r12);
    n10.xTile = s6[l10++], n10.yTile = s6[l10++], n10.hash = s6[l10++], n10.priority = s6[l10++];
    const a8 = s6[l10++];
    for (let t10 = 0; t10 < a8; t10++) {
      const t11 = s6[l10++], e9 = s6[l10++], i8 = s6[l10++], r13 = s6[l10++], a9 = !!s6[l10++], c5 = s6[l10++], h8 = o5[l10++], f5 = o5[l10++], u7 = s6[l10++], m4 = s6[l10++];
      n10.colliders.push({ xTile: t11, yTile: e9, dxPixels: i8, dyPixels: r13, hard: a9, partIndex: c5, width: u7, height: m4, minLod: h8, maxLod: f5 });
    }
    const c4 = t9[l10++];
    for (let e9 = 0; e9 < c4; e9++) n10.textVertexRanges.push([t9[l10++], t9[l10++]]);
    const h7 = t9[l10++];
    for (let e9 = 0; e9 < h7; e9++) n10.iconVertexRanges.push([t9[l10++], t9[l10++]]);
    i7.push(n10);
  }
  return l10;
}
function i3(t9, e9, s6) {
  for (const [o5, l10] of t9.symbols) r10(t9, e9, s6, l10, o5);
}
function r10(e9, s6, o5, l10, i7) {
  const r12 = e9.layerData.get(i7);
  if (r12.type === E6.SYMBOL) {
    for (const t9 of l10) {
      const s7 = t9.unique;
      let l11;
      if (t9.selectedForRendering) {
        const t10 = s7.parts[0], i8 = t10.startOpacity, r13 = t10.targetOpacity;
        e9.allSymbolsFadingOut = e9.allSymbolsFadingOut && 0 === r13;
        const n9 = o5 ? Math.floor(127 * i8) | r13 << 7 : r13 ? 255 : 0;
        l11 = n9 << 24 | n9 << 16 | n9 << 8 | n9;
      } else l11 = 0;
      for (const [e10, o6] of t9.iconVertexRanges) for (let t10 = e10; t10 < e10 + o6; t10 += 4) r12.iconOpacity[t10 / 4] = l11;
      if (t9.selectedForRendering) {
        const t10 = s7.parts[1], i8 = t10.startOpacity, r13 = t10.targetOpacity;
        e9.allSymbolsFadingOut = e9.allSymbolsFadingOut && 0 === r13;
        const n9 = o5 ? Math.floor(127 * i8) | r13 << 7 : r13 ? 255 : 0;
        l11 = n9 << 24 | n9 << 16 | n9 << 8 | n9;
      } else l11 = 0;
      for (const [e10, o6] of t9.textVertexRanges) for (let t10 = e10; t10 < e10 + o6; t10 += 4) r12.textOpacity[t10 / 4] = l11;
    }
    r12.lastOpacityUpdate = s6, r12.opacityChanged = true;
  }
}

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/RenderBucket.js
var o4 = class {
  constructor(e9, t9) {
    this.layerUIDs = [], this.isDestroyed = false, this._data = e9, this.memoryUsed = e9.byteLength;
    let r12 = 1;
    const i7 = new Uint32Array(e9);
    this.layerUIDs = [];
    const s6 = i7[r12++];
    for (let n9 = 0; n9 < s6; n9++) this.layerUIDs[n9] = i7[r12++];
    this.bufferDataOffset = r12, t9 && (this.layer = t9.getStyleLayerByUID(this.layerUIDs[0]));
  }
  get isPreparedForRendering() {
    return t(this._data);
  }
  get offset() {
    return this.bufferDataOffset;
  }
  destroy() {
    this.isDestroyed || (this.doDestroy(), this.isDestroyed = true);
  }
  prepareForRendering(t9) {
    t(this._data) || (this.doPrepareForRendering(t9, this._data, this.bufferDataOffset), this._data = null);
  }
};
var l6 = class extends o4 {
  constructor(e9, t9) {
    super(e9, t9), this.type = E6.LINE, this.lineIndexStart = 0, this.lineIndexCount = 0;
    const r12 = new Uint32Array(e9);
    let s6 = this.bufferDataOffset;
    this.lineIndexStart = r12[s6++], this.lineIndexCount = r12[s6++];
    const n9 = r12[s6++];
    if (n9 > 0) {
      const e10 = /* @__PURE__ */ new Map();
      for (let t10 = 0; t10 < n9; t10++) {
        const t11 = r12[s6++], i7 = r12[s6++], n10 = r12[s6++];
        e10.set(t11, [i7, n10]);
      }
      this.patternMap = e10;
    }
    this.bufferDataOffset = s6;
  }
  hasData() {
    return this.lineIndexCount > 0;
  }
  triangleCount() {
    return this.lineIndexCount / 3;
  }
  doDestroy() {
    r(this.lineVertexArrayObject) && this.lineVertexArrayObject.dispose(), r(this.lineVertexBuffer) && this.lineVertexBuffer.dispose(), r(this.lineIndexBuffer) && this.lineIndexBuffer.dispose(), this.lineVertexArrayObject = null, this.lineVertexBuffer = null, this.lineIndexBuffer = null, this.memoryUsed = 0;
  }
  doPrepareForRendering(e9, t9, r12) {
    const i7 = new Uint32Array(t9), s6 = new Int32Array(i7.buffer), o5 = i7[r12++];
    this.lineVertexBuffer = E5.createVertex(e9, F.STATIC_DRAW, new Int32Array(s6.buffer, 4 * r12, o5)), r12 += o5;
    const l10 = i7[r12++];
    this.lineIndexBuffer = E5.createIndex(e9, F.STATIC_DRAW, new Uint32Array(i7.buffer, 4 * r12, l10)), r12 += l10;
    const u7 = this.layer.lineMaterial;
    this.lineVertexArrayObject = new f2(e9, u7.getAttributeLocations(), u7.getLayoutInfo(), { geometry: this.lineVertexBuffer }, this.lineIndexBuffer);
  }
};
var u4 = class extends o4 {
  constructor(e9, t9) {
    super(e9, t9), this.type = E6.FILL, this.fillIndexStart = 0, this.fillIndexCount = 0, this.outlineIndexStart = 0, this.outlineIndexCount = 0;
    const r12 = new Uint32Array(e9);
    let s6 = this.bufferDataOffset;
    this.fillIndexStart = r12[s6++], this.fillIndexCount = r12[s6++], this.outlineIndexStart = r12[s6++], this.outlineIndexCount = r12[s6++];
    const n9 = r12[s6++];
    if (n9 > 0) {
      const e10 = /* @__PURE__ */ new Map();
      for (let t10 = 0; t10 < n9; t10++) {
        const t11 = r12[s6++], i7 = r12[s6++], n10 = r12[s6++];
        e10.set(t11, [i7, n10]);
      }
      this.patternMap = e10;
    }
    this.bufferDataOffset = s6;
  }
  hasData() {
    return this.fillIndexCount > 0 || this.outlineIndexCount > 0;
  }
  triangleCount() {
    return (this.fillIndexCount + this.outlineIndexCount) / 3;
  }
  doDestroy() {
    r(this.fillVertexArrayObject) && this.fillVertexArrayObject.dispose(), r(this.fillVertexBuffer) && this.fillVertexBuffer.dispose(), r(this.fillIndexBuffer) && this.fillIndexBuffer.dispose(), this.fillVertexArrayObject = null, this.fillVertexBuffer = null, this.fillIndexBuffer = null, r(this.outlineVertexArrayObject) && this.outlineVertexArrayObject.dispose(), r(this.outlineVertexBuffer) && this.outlineVertexBuffer.dispose(), r(this.outlineIndexBuffer) && this.outlineIndexBuffer.dispose(), this.outlineVertexArrayObject = null, this.outlineVertexBuffer = null, this.outlineIndexBuffer = null, this.memoryUsed = 0;
  }
  doPrepareForRendering(e9, t9, r12) {
    const i7 = new Uint32Array(t9), s6 = new Int32Array(i7.buffer), o5 = i7[r12++];
    this.fillVertexBuffer = E5.createVertex(e9, F.STATIC_DRAW, new Int32Array(s6.buffer, 4 * r12, o5)), r12 += o5;
    const l10 = i7[r12++];
    this.fillIndexBuffer = E5.createIndex(e9, F.STATIC_DRAW, new Uint32Array(i7.buffer, 4 * r12, l10)), r12 += l10;
    const u7 = i7[r12++];
    this.outlineVertexBuffer = E5.createVertex(e9, F.STATIC_DRAW, new Int32Array(s6.buffer, 4 * r12, u7)), r12 += u7;
    const h7 = i7[r12++];
    this.outlineIndexBuffer = E5.createIndex(e9, F.STATIC_DRAW, new Uint32Array(i7.buffer, 4 * r12, h7)), r12 += h7;
    const c4 = this.layer, x = c4.fillMaterial, y3 = c4.outlineMaterial;
    this.fillVertexArrayObject = new f2(e9, x.getAttributeLocations(), x.getLayoutInfo(), { geometry: this.fillVertexBuffer }, this.fillIndexBuffer), this.outlineVertexArrayObject = new f2(e9, y3.getAttributeLocations(), y3.getLayoutInfo(), { geometry: this.outlineVertexBuffer }, this.outlineIndexBuffer);
  }
};
var h6 = class extends o4 {
  constructor(e9, t9, r12) {
    super(e9, t9), this.type = E6.SYMBOL, this.iconPerPageElementsMap = /* @__PURE__ */ new Map(), this.glyphPerPageElementsMap = /* @__PURE__ */ new Map(), this.symbolInstances = [], this.isIconSDF = false, this.opacityChanged = false, this.lastOpacityUpdate = 0, this.symbols = [];
    const n9 = new Uint32Array(e9), f5 = new Int32Array(e9), a7 = new Float32Array(e9);
    let o5 = this.bufferDataOffset;
    this.isIconSDF = !!n9[o5++];
    const l10 = n9[o5++];
    for (let i7 = 0; i7 < l10; i7++) {
      const e10 = n9[o5++], t10 = n9[o5++], r13 = n9[o5++];
      this.iconPerPageElementsMap.set(e10, [t10, r13]);
    }
    const u7 = n9[o5++];
    for (let i7 = 0; i7 < u7; i7++) {
      const e10 = n9[o5++], t10 = n9[o5++], r13 = n9[o5++];
      this.glyphPerPageElementsMap.set(e10, [t10, r13]);
    }
    const h7 = n9[o5++], c4 = n9[o5++];
    this.iconOpacity = new Int32Array(h7), this.textOpacity = new Int32Array(c4), o5 = l5(n9, f5, a7, o5, this.symbols, r12), this.bufferDataOffset = o5;
  }
  hasData() {
    return this.iconPerPageElementsMap.size > 0 || this.glyphPerPageElementsMap.size > 0;
  }
  triangleCount() {
    let e9 = 0;
    for (const [t9, r12] of this.iconPerPageElementsMap) e9 += r12[1];
    for (const [t9, r12] of this.glyphPerPageElementsMap) e9 += r12[1];
    return e9 / 3;
  }
  doDestroy() {
    r(this.iconVertexArrayObject) && this.iconVertexArrayObject.dispose(), r(this.iconVertexBuffer) && this.iconVertexBuffer.dispose(), r(this.iconOpacityBuffer) && this.iconOpacityBuffer.dispose(), r(this.iconIndexBuffer) && this.iconIndexBuffer.dispose(), this.iconVertexArrayObject = null, this.iconVertexBuffer = null, this.iconOpacityBuffer = null, this.iconIndexBuffer = null, r(this.textVertexArrayObject) && this.textVertexArrayObject.dispose(), r(this.textVertexBuffer) && this.textVertexBuffer.dispose(), r(this.textOpacityBuffer) && this.textOpacityBuffer.dispose(), r(this.textIndexBuffer) && this.textIndexBuffer.dispose(), this.textVertexArrayObject = null, this.textVertexBuffer = null, this.textOpacityBuffer = null, this.textIndexBuffer = null, this.memoryUsed = 0;
  }
  updateOpacityInfo() {
    if (!this.opacityChanged) return;
    this.opacityChanged = false;
    const e9 = e2(this.iconOpacity), t9 = e2(this.iconOpacityBuffer);
    e9.length > 0 && e9.byteLength === t9.size && t9.setSubData(e9, 0, 0, e9.length);
    const i7 = e2(this.textOpacity), s6 = e2(this.textOpacityBuffer);
    i7.length > 0 && i7.byteLength === s6.size && s6.setSubData(i7, 0, 0, i7.length);
  }
  doPrepareForRendering(e9, t9, i7) {
    const s6 = new Uint32Array(t9), o5 = new Int32Array(s6.buffer), l10 = s6[i7++];
    this.iconVertexBuffer = E5.createVertex(e9, F.STATIC_DRAW, new Int32Array(o5.buffer, 4 * i7, l10)), i7 += l10;
    const u7 = s6[i7++];
    this.iconIndexBuffer = E5.createIndex(e9, F.STATIC_DRAW, new Uint32Array(s6.buffer, 4 * i7, u7)), i7 += u7;
    const h7 = s6[i7++];
    this.textVertexBuffer = E5.createVertex(e9, F.STATIC_DRAW, new Int32Array(o5.buffer, 4 * i7, h7)), i7 += h7;
    const c4 = s6[i7++];
    this.textIndexBuffer = E5.createIndex(e9, F.STATIC_DRAW, new Uint32Array(s6.buffer, 4 * i7, c4)), i7 += c4, this.iconOpacityBuffer = E5.createVertex(e9, F.STATIC_DRAW, e2(this.iconOpacity).buffer), this.textOpacityBuffer = E5.createVertex(e9, F.STATIC_DRAW, e2(this.textOpacity).buffer);
    const x = this.layer, y3 = x.iconMaterial, d2 = x.textMaterial;
    this.iconVertexArrayObject = new f2(e9, y3.getAttributeLocations(), y3.getLayoutInfo(), { geometry: this.iconVertexBuffer, opacity: this.iconOpacityBuffer }, this.iconIndexBuffer), this.textVertexArrayObject = new f2(e9, d2.getAttributeLocations(), d2.getLayoutInfo(), { geometry: this.textVertexBuffer, opacity: this.textOpacityBuffer }, this.textIndexBuffer);
  }
};
var c3 = class extends o4 {
  constructor(e9, t9) {
    super(e9, t9), this.type = E6.CIRCLE, this.circleIndexStart = 0, this.circleIndexCount = 0;
    const r12 = new Uint32Array(e9);
    let s6 = this.bufferDataOffset;
    this.circleIndexStart = r12[s6++], this.circleIndexCount = r12[s6++], this.bufferDataOffset = s6;
  }
  hasData() {
    return this.circleIndexCount > 0;
  }
  triangleCount() {
    return this.circleIndexCount / 3;
  }
  doDestroy() {
    r(this.circleVertexArrayObject) && this.circleVertexArrayObject.dispose(), r(this.circleVertexBuffer) && this.circleVertexBuffer.dispose(), r(this.circleIndexBuffer) && this.circleIndexBuffer.dispose(), this.circleVertexArrayObject = null, this.circleVertexBuffer = null, this.circleIndexBuffer = null, this.memoryUsed = 0;
  }
  doPrepareForRendering(e9, t9, r12) {
    const i7 = new Uint32Array(t9), s6 = new Int32Array(i7.buffer), o5 = i7[r12++];
    this.circleVertexBuffer = E5.createVertex(e9, F.STATIC_DRAW, new Int32Array(s6.buffer, 4 * r12, o5)), r12 += o5;
    const l10 = i7[r12++];
    this.circleIndexBuffer = E5.createIndex(e9, F.STATIC_DRAW, new Uint32Array(i7.buffer, 4 * r12, l10)), r12 += l10;
    const u7 = this.layer.circleMaterial;
    this.circleVertexArrayObject = new f2(e9, u7.getAttributeLocations(), u7.getLayoutInfo(), { geometry: this.circleVertexBuffer }, this.circleIndexBuffer);
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/VectorTile.js
var m2 = class _m extends r6 {
  constructor(e9, t9, s6, a7, r12, i7, o5, h7 = null) {
    super(e9, t9, s6, a7, r12, i7, 4096, 4096), this._memCache = h7, this.type = "vector-tile", this._referenced = 0, this._hasSymbolBuckets = false, this._memoryUsedByLayerData = 0, this.layerData = /* @__PURE__ */ new Map(), this.layerCount = 0, this.status = "loading", this.allSymbolsFadingOut = false, this.lastOpacityUpdate = 0, this.symbols = /* @__PURE__ */ new Map(), this.isCoverage = false, this.neededForCoverage = false, this.decluttered = false, this.invalidating = false, this.parentTile = null, this.childrenTiles = /* @__PURE__ */ new Set(), this._processed = false, this._referenced = 1, this.styleRepository = o5, this.id = e9.id;
  }
  get hasSymbolBuckets() {
    return this._hasSymbolBuckets;
  }
  get isFading() {
    return this._hasSymbolBuckets && performance.now() - this.lastOpacityUpdate < e6;
  }
  get isHoldingForFade() {
    return this._hasSymbolBuckets && (!this.allSymbolsFadingOut || performance.now() - this.lastOpacityUpdate < e6);
  }
  get wasRequested() {
    return "errored" === this.status || "loaded" === this.status || "reloading" === this.status;
  }
  setData(e9) {
    this.changeDataImpl(e9), this.requestRender(), this.ready(), this.invalidating = false, this._processed = true;
  }
  deleteLayerData(t9) {
    let s6 = false;
    for (const e9 of t9) if (this.layerData.has(e9)) {
      const t10 = this.layerData.get(e9);
      this._memoryUsedByLayerData -= t10.memoryUsed, t10.type === E6.SYMBOL && this.symbols.has(e9) && (this.symbols.delete(e9), s6 = true), t10.destroy(), this.layerData.delete(e9), this.layerCount--;
    }
    r(this._memCache) && this._memCache.updateSize(this.key.id, this, this._memoryUsedByLayerData), s6 && this.emit("symbols-changed"), this.requestRender();
  }
  processed() {
    return this._processed;
  }
  hasData() {
    return this.layerCount > 0;
  }
  dispose() {
    "unloaded" !== this.status && (u5.delete(this), _m._destroyRenderBuckets(this.layerData), this.layerData = null, this.layerCount = 0, this._memoryUsedByLayerData = 0, this.destroy(), this.status = "unloaded");
  }
  release() {
    return 0 == --this._referenced && (this.dispose(), this.stage = null, true);
  }
  retain() {
    ++this._referenced;
  }
  get referenced() {
    return this._referenced;
  }
  get memoryUsage() {
    return (this._memoryUsedByLayerData + 256) / (this._referenced || 1);
  }
  changeDataImpl(t9) {
    let s6 = false;
    if (t9) {
      const { bucketsWithData: a7, emptyBuckets: r12 } = t9, i7 = this._createRenderBuckets(a7);
      if (r12 && r12.byteLength > 0) {
        const e9 = new Uint32Array(r12);
        for (const t10 of e9) this._deleteLayerData(t10);
      }
      for (const [e9, t10] of i7) this._deleteLayerData(e9), t10.type === E6.SYMBOL && (this.symbols.set(e9, t10.symbols), s6 = true), this._memoryUsedByLayerData += t10.memoryUsed, this.layerData.set(e9, t10), this.layerCount++;
      r(this._memCache) && this._memCache.updateSize(this.key.id, this, this._memoryUsedByLayerData);
    }
    this._hasSymbolBuckets = false;
    for (const [e9, a7] of this.layerData) a7.type === E6.SYMBOL && (this._hasSymbolBuckets = true);
    s6 && this.emit("symbols-changed");
  }
  attachWithContext(e9) {
    this.stage = { context: e9, trashDisplayObject(e10) {
      e10.processDetach();
    }, untrashDisplayObject: () => false };
  }
  setTransform(e9) {
    super.setTransform(e9);
    const i7 = this.resolution / (e9.resolution * e9.pixelRatio), o5 = this.width / this.rangeX * i7, h7 = this.height / this.rangeY * i7, n9 = [0, 0];
    e9.toScreen(n9, [this.x, this.y]);
    const l10 = this.transforms.tileUnitsToPixels;
    r2(l10), M(l10, l10, n9), h3(l10, l10, Math.PI * e9.rotation / 180), f(l10, l10, [o5, h7, 1]);
  }
  _createTransforms() {
    return { dvs: e5(), tileMat3: e5(), tileUnitsToPixels: e5() };
  }
  static _destroyRenderBuckets(e9) {
    if (!e9) return;
    const t9 = /* @__PURE__ */ new Set();
    e9.forEach((e10) => {
      t9.has(e10) || (e10.destroy(), t9.add(e10));
    }), e9.clear();
  }
  _createRenderBuckets(e9) {
    const t9 = /* @__PURE__ */ new Map(), s6 = /* @__PURE__ */ new Map();
    for (const a7 of e9) {
      const e10 = this._deserializeBucket(a7, s6);
      for (const s7 of e10.layerUIDs) t9.set(s7, e10);
    }
    return t9;
  }
  _deserializeBucket(e9, t9) {
    let s6 = t9.get(e9);
    if (s6) return s6;
    switch (new Uint32Array(e9)[0]) {
      case E6.FILL:
        s6 = new u4(e9, this.styleRepository);
        break;
      case E6.LINE:
        s6 = new l6(e9, this.styleRepository);
        break;
      case E6.SYMBOL:
        s6 = new h6(e9, this.styleRepository, this);
        break;
      case E6.CIRCLE:
        s6 = new c3(e9, this.styleRepository);
    }
    return t9.set(e9, s6), s6;
  }
  _deleteLayerData(e9) {
    if (!this.layerData.has(e9)) return;
    const t9 = this.layerData.get(e9);
    this._memoryUsedByLayerData -= t9.memoryUsed, t9.destroy(), this.layerData.delete(e9), this.layerCount--;
  }
};
var u5 = /* @__PURE__ */ new Map();

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/decluttering/jobs.js
function i4(e9, t9, n9, o5, i7, l10) {
  const { iconRotationAlignment: a7, textRotationAlignment: c4, iconTranslate: h7, iconTranslateAnchor: u7, textTranslate: d2, textTranslateAnchor: y3 } = o5;
  let x = 0;
  for (const g3 of e9.colliders) {
    const [e10, o6] = 0 === g3.partIndex ? h7 : d2, m4 = 0 === g3.partIndex ? u7 : y3, f5 = g3.minLod <= l10 && l10 <= g3.maxLod;
    x += f5 ? 0 : 1, g3.enabled = f5, g3.xScreen = g3.xTile * i7[0] + g3.yTile * i7[3] + i7[6], g3.yScreen = g3.xTile * i7[1] + g3.yTile * i7[4] + i7[7], m4 === r4.MAP ? (g3.xScreen += n9 * e10 - t9 * o6, g3.yScreen += t9 * e10 + n9 * o6) : (g3.xScreen += e10, g3.yScreen += o6), l3.VIEWPORT === (0 === g3.partIndex ? a7 : c4) ? (g3.dxScreen = g3.dxPixels, g3.dyScreen = g3.dyPixels) : (g3.dxScreen = n9 * (g3.dxPixels + g3.width / 2) - t9 * (g3.dyPixels + g3.height / 2) - g3.width / 2, g3.dyScreen = t9 * (g3.dxPixels + g3.width / 2) + n9 * (g3.dyPixels + g3.height / 2) - g3.height / 2);
  }
  e9.colliders.length > 0 && x === e9.colliders.length && (e9.unique.show = false);
}
var l7 = class {
  constructor(o5, r12, s6, i7, l10, a7) {
    this._symbols = o5, this._styleRepository = i7, this._zoom = l10, this._currentLayerCursor = 0, this._currentSymbolCursor = 0, this._styleProps = /* @__PURE__ */ new Map(), this._allNeededMatrices = /* @__PURE__ */ new Map(), this._gridIndex = new o3(r12, s6, t4), this._si = Math.sin(Math.PI * a7 / 180), this._co = Math.cos(Math.PI * a7 / 180);
    for (const t9 of o5) for (const n9 of t9.symbols) this._allNeededMatrices.has(n9.tile) || this._allNeededMatrices.set(n9.tile, r3(n9.tile.transforms.tileUnitsToPixels));
  }
  work(e9) {
    const t9 = this._gridIndex;
    function n9(e10) {
      const n10 = e10.xScreen + e10.dxScreen, o6 = e10.yScreen + e10.dyScreen, r12 = n10 + e10.width, s6 = o6 + e10.height, [i7, l10, a7, c4] = t9.getCellSpan(n10, o6, r12, s6);
      for (let h7 = l10; h7 <= c4; h7++) for (let e11 = i7; e11 <= a7; e11++) {
        const i8 = t9.cells[h7][e11];
        for (const e12 of i8) {
          const t10 = e12.xScreen + e12.dxScreen, i9 = e12.yScreen + e12.dyScreen, l11 = t10 + e12.width, a8 = i9 + e12.height;
          if (!(r12 < t10 || n10 > l11 || s6 < i9 || o6 > a8)) return true;
        }
      }
      return false;
    }
    const o5 = performance.now();
    for (; this._currentLayerCursor < this._symbols.length; this._currentLayerCursor++, this._currentSymbolCursor = 0) {
      const t10 = this._symbols[this._currentLayerCursor], r12 = this._getProperties(t10.styleLayerUID);
      for (; this._currentSymbolCursor < t10.symbols.length; this._currentSymbolCursor++) {
        if (this._currentSymbolCursor % 100 == 99 && performance.now() - o5 > e9) return false;
        const s6 = t10.symbols[this._currentSymbolCursor];
        if (!s6.unique.show) continue;
        i4(s6, this._si, this._co, r12, this._allNeededMatrices.get(s6.tile), this._zoom);
        const l10 = s6.unique;
        if (!l10.show) continue;
        const { iconAllowOverlap: a7, iconIgnorePlacement: c4, textAllowOverlap: h7, textIgnorePlacement: u7 } = r12;
        for (const e10 of s6.colliders) {
          if (!e10.enabled) continue;
          const t11 = l10.parts[e10.partIndex];
          if (!t11.show) continue;
          !(e10.partIndex ? h7 : a7) && n9(e10) && (e10.hard ? l10.show = false : t11.show = false);
        }
        if (l10.show) for (const e10 of s6.colliders) {
          if (!e10.enabled) continue;
          if (e10.partIndex ? u7 : c4) continue;
          if (!l10.parts[e10.partIndex].show) continue;
          const t11 = e10.xScreen + e10.dxScreen, n10 = e10.yScreen + e10.dyScreen, o6 = t11 + e10.width, r13 = n10 + e10.height, [s7, i7, a8, h8] = this._gridIndex.getCellSpan(t11, n10, o6, r13);
          for (let l11 = i7; l11 <= h8; l11++) for (let t12 = s7; t12 <= a8; t12++) {
            this._gridIndex.cells[l11][t12].push(e10);
          }
        }
      }
    }
    return true;
  }
  _getProperties(e9) {
    const t9 = this._styleProps.get(e9);
    if (t9) return t9;
    const n9 = this._zoom, s6 = this._styleRepository.getStyleLayerByUID(e9), i7 = s6.getLayoutValue("symbol-placement", n9) !== n3.POINT;
    let l10 = s6.getLayoutValue("icon-rotation-alignment", n9);
    l10 === l3.AUTO && (l10 = i7 ? l3.MAP : l3.VIEWPORT);
    let a7 = s6.getLayoutValue("text-rotation-alignment", n9);
    a7 === l3.AUTO && (a7 = i7 ? l3.MAP : l3.VIEWPORT);
    const c4 = s6.getPaintValue("icon-translate", n9), h7 = s6.getPaintValue("icon-translate-anchor", n9), u7 = s6.getPaintValue("text-translate", n9), d2 = s6.getPaintValue("text-translate-anchor", n9), y3 = { iconAllowOverlap: s6.getLayoutValue("icon-allow-overlap", n9), iconIgnorePlacement: s6.getLayoutValue("icon-ignore-placement", n9), textAllowOverlap: s6.getLayoutValue("text-allow-overlap", n9), textIgnorePlacement: s6.getLayoutValue("text-ignore-placement", n9), iconRotationAlignment: l10, textRotationAlignment: a7, iconTranslateAnchor: h7, iconTranslate: c4, textTranslateAnchor: d2, textTranslate: u7 };
    return this._styleProps.set(e9, y3), y3;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/decluttering/SymbolDeclutterer.js
function t6(o5, e9) {
  if (o5.priority - e9.priority) return o5.priority - e9.priority;
  const t9 = o5.tile.key, i7 = e9.tile.key;
  return t9.world - i7.world ? t9.world - i7.world : t9.level - i7.level ? t9.level - i7.level : t9.row - i7.row ? t9.row - i7.row : t9.col - i7.col ? t9.col - i7.col : o5.xTile - e9.xTile ? o5.xTile - e9.xTile : o5.yTile - e9.yTile;
}
var i5 = class {
  get running() {
    return this._running;
  }
  constructor(o5, e9, t9, i7, s6, r12) {
    this._visibleTiles = o5, this._symbolRepository = e9, this._createCollisionJob = t9, this._assignTileSymbolsOpacity = i7, this._symbolLayerSorter = s6, this._isLayerVisible = r12, this._selectionJob = null, this._selectionJobCompleted = false, this._collisionJob = null, this._collisionJobCompleted = false, this._opacityJob = null, this._opacityJobCompleted = false, this._running = true;
  }
  setScreenSize(o5, e9) {
    this._screenWidth === o5 && this._screenHeight === e9 || this.restart(), this._screenWidth = o5, this._screenHeight = e9;
  }
  restart() {
    this._selectionJob = null, this._selectionJobCompleted = false, this._collisionJob = null, this._collisionJobCompleted = false, this._opacityJob = null, this._opacityJobCompleted = false, this._running = true;
  }
  continue(o5) {
    if (this._selectionJob || (this._selectionJob = this._createSelectionJob()), !this._selectionJobCompleted) {
      const e9 = performance.now();
      if (!this._selectionJob.work(o5)) return false;
      if (this._selectionJobCompleted = true, 0 === (o5 = Math.max(0, o5 - (performance.now() - e9)))) return false;
    }
    if (this._collisionJob || (this._collisionJob = this._createCollisionJob(this._selectionJob.sortedSymbols, this._screenWidth, this._screenHeight)), !this._collisionJobCompleted) {
      const e9 = performance.now();
      if (!this._collisionJob.work(o5)) return false;
      if (this._collisionJobCompleted = true, 0 === (o5 = Math.max(0, o5 - (performance.now() - e9)))) return false;
    }
    if (this._opacityJob || (this._opacityJob = this._createOpacityJob()), !this._opacityJobCompleted) {
      const e9 = performance.now();
      if (!this._opacityJob.work(o5)) return false;
      if (this._opacityJobCompleted = true, 0 === (o5 = Math.max(0, o5 - (performance.now() - e9)))) return false;
    }
    return this._running = false, true;
  }
  _createSelectionJob() {
    const o5 = this._symbolRepository.uniqueSymbols;
    for (let t9 = 0; t9 < o5.length; t9++) {
      const e10 = o5[t9];
      for (let o6 = 0; o6 < e10.uniqueSymbols.length; o6++) {
        const t10 = e10.uniqueSymbols[o6];
        for (const o7 of t10.tileSymbols) o7.selectedForRendering = false;
      }
    }
    const e9 = [];
    let i7 = 0, s6 = 0;
    const r12 = this._isLayerVisible;
    function n9(n10) {
      let l11;
      const c4 = performance.now();
      for (; s6 < o5.length; s6++, i7 = 0) {
        const t9 = o5[s6], h7 = t9.styleLayerUID;
        if (!r12(h7)) {
          e9[s6] || (e9[s6] = { styleLayerUID: h7, symbols: [] });
          continue;
        }
        e9[s6] = e9[s6] || { styleLayerUID: h7, symbols: [] };
        const a7 = e9[s6];
        for (; i7 < t9.uniqueSymbols.length; i7++) {
          if (l11 = t9.uniqueSymbols[i7], i7 % 100 == 99 && performance.now() - c4 > n10) return false;
          let o6 = null, e10 = false, s7 = false;
          for (const t10 of l11.tileSymbols) if (!s7 || !e10) {
            const i8 = t10.tile;
            (!o6 || i8.isCoverage || i8.neededForCoverage && !e10) && (o6 = t10, (i8.neededForCoverage || i8.isCoverage) && (s7 = true), i8.isCoverage && (e10 = true));
          }
          if (o6.selectedForRendering = true, s7) {
            a7.symbols.push(o6), l11.show = true;
            for (const o7 of l11.parts) o7.show = true;
          } else l11.show = false;
        }
      }
      for (const o6 of e9) o6.symbols.sort(t6);
      return true;
    }
    const l10 = this._symbolLayerSorter;
    return { work: n9, get sortedSymbols() {
      return e9.sort(l10);
    } };
  }
  _createOpacityJob() {
    const e9 = this._assignTileSymbolsOpacity, t9 = this._visibleTiles;
    let i7 = 0;
    function r12(o5, t10) {
      const i8 = o5.symbols;
      for (const [e10, r13] of i8) s5(r13, t10);
      e9(o5, t10);
      for (const e10 of o5.childrenTiles) r12(e10, t10);
    }
    return { work(e10) {
      const s6 = performance.now();
      for (; i7 < t9.length; i7++) {
        if (performance.now() - s6 > e10) return false;
        const n9 = t9[i7];
        if (r(n9.parentTile)) continue;
        r12(n9, performance.now());
      }
      return true;
    } };
  }
};
function s5(o5, t9) {
  for (const i7 of o5) {
    const o6 = i7.unique;
    for (const i8 of o6.parts) {
      const s6 = i8.targetOpacity > 0.5 ? 1 : -1;
      i8.startOpacity += s6 * ((t9 - i8.startTime) / e6), i8.startOpacity = Math.min(Math.max(i8.startOpacity, 0), 1), i8.startTime = t9, i8.targetOpacity = o6.show && i8.show ? 1 : 0;
    }
  }
}

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/decluttering/SymbolRepository.js
var l8 = 32;
var i6 = 8;
var n7 = 64;
var r11 = class {
  constructor(e9, s6, o5) {
    this.tileCoordRange = e9, this._visibleTiles = s6, this._createUnique = o5, this._tiles = /* @__PURE__ */ new Map(), this._uniqueSymbolsReferences = /* @__PURE__ */ new Map();
  }
  get uniqueSymbols() {
    return t(this._uniqueSymbolLayerArray) && (this._uniqueSymbolLayerArray = this._createUniqueSymbolLayerArray()), this._uniqueSymbolLayerArray;
  }
  add(e9, s6) {
    this._uniqueSymbolLayerArray = null;
    let t9 = this._tiles.get(e9.id);
    t9 || (t9 = { symbols: /* @__PURE__ */ new Map() }, this._tiles.set(e9.id, t9));
    const r12 = /* @__PURE__ */ new Map();
    if (s6) for (const o5 of s6) t9.symbols.has(o5) && (r12.set(o5, t9.symbols.get(o5)), t9.symbols.delete(o5));
    else for (const [o5, l10] of e9.layerData) t9.symbols.has(o5) && (r12.set(o5, t9.symbols.get(o5)), t9.symbols.delete(o5));
    this._removeSymbols(r12);
    const y3 = e9.symbols, a7 = /* @__PURE__ */ new Map();
    for (const [f5, c4] of y3) {
      let e10 = c4.length;
      if (e10 >= l8) {
        let s7 = this.tileCoordRange;
        do {
          s7 /= 2, e10 /= 4;
        } while (e10 > i6 && s7 > n7);
        const l10 = new o3(this.tileCoordRange, this.tileCoordRange, s7);
        a7.set(f5, { flat: c4, index: l10 }), t9.symbols.set(f5, { flat: c4, index: l10 });
        for (const e11 of c4) l10.getCell(e11.xTile, e11.yTile).push(e11);
      } else a7.set(f5, { flat: c4 }), t9.symbols.set(f5, { flat: c4 });
    }
    this._addSymbols(e9.key, y3);
  }
  deleteStyleLayers(e9) {
    this._uniqueSymbolLayerArray = null;
    for (const [s6, o5] of this._tiles) {
      const t9 = /* @__PURE__ */ new Map();
      for (const s7 of e9) o5.symbols.has(s7) && (t9.set(s7, o5.symbols.get(s7)), o5.symbols.delete(s7));
      this._removeSymbols(t9), 0 === o5.symbols.size && this._tiles.delete(s6);
    }
  }
  removeTile(e9) {
    this._uniqueSymbolLayerArray = null;
    const s6 = this._tiles.get(e9.id);
    if (!s6) return;
    const o5 = /* @__PURE__ */ new Map();
    for (const [t9, l10] of e9.symbols) s6.symbols.has(t9) && (o5.set(t9, s6.symbols.get(t9)), s6.symbols.delete(t9));
    this._removeSymbols(o5), 0 === s6.symbols.size && this._tiles.delete(e9.id);
  }
  _removeSymbols(e9) {
    for (const [s6, { flat: o5 }] of e9) for (const e10 of o5) {
      const o6 = e10.unique, t9 = o6.tileSymbols, l10 = t9.length - 1;
      for (let s7 = 0; s7 < l10; s7++) if (t9[s7] === e10) {
        t9[s7] = t9[l10];
        break;
      }
      if (t9.length = l10, 0 === l10) {
        const e11 = this._uniqueSymbolsReferences.get(s6);
        e11.delete(o6), 0 === e11.size && this._uniqueSymbolsReferences.delete(s6);
      }
      e10.unique = null;
    }
  }
  _addSymbols(s6, o5) {
    if (0 === o5.size) return;
    const t9 = this._visibleTiles;
    for (const e9 of t9) e9.parentTile || e9.key.world !== s6.world || e9.key.level === s6.level && !e9.key.equals(s6) || this._matchSymbols(e9, s6, o5);
    for (const [l10, i7] of o5) for (const s7 of i7) if (t(s7.unique)) {
      const e9 = this._createUnique();
      s7.unique = e9, e9.tileSymbols.push(s7);
      let o6 = this._uniqueSymbolsReferences.get(l10);
      o6 || (o6 = /* @__PURE__ */ new Set(), this._uniqueSymbolsReferences.set(l10, o6)), o6.add(e9);
    }
  }
  _matchSymbols(e9, o5, l10) {
    if (e9.key.level > o5.level) {
      const s6 = e9.key.level - o5.level;
      if (e9.key.row >> s6 !== o5.row || e9.key.col >> s6 !== o5.col) return;
    }
    if (o5.level > e9.key.level) {
      const s6 = o5.level - e9.key.level;
      if (o5.row >> s6 !== e9.key.row || o5.col >> s6 !== e9.key.col) return;
    }
    if (o5.equals(e9.key)) {
      for (const s6 of e9.childrenTiles) this._matchSymbols(s6, o5, l10);
      return;
    }
    const i7 = /* @__PURE__ */ new Map();
    for (const [n9, r12] of l10) {
      const l11 = [];
      for (const s6 of r12) {
        const i8 = s4(this.tileCoordRange, s6.xTile, o5.level, o5.col, e9.key.level, e9.key.col), n10 = s4(this.tileCoordRange, s6.yTile, o5.level, o5.row, e9.key.level, e9.key.row);
        i8 >= 0 && i8 < this.tileCoordRange && n10 >= 0 && n10 < this.tileCoordRange && l11.push({ symbol: s6, xTransformed: i8, yTransformed: n10 });
      }
      const y3 = [], a7 = e9.key.level < o5.level ? 1 : 1 << e9.key.level - o5.level, f5 = this._tiles.get(e9.id).symbols.get(n9);
      if (f5) {
        const e10 = f5.flat;
        for (const o6 of l11) {
          let t9, l12 = false;
          const i8 = o6.xTransformed, n10 = o6.yTransformed;
          t9 = r(f5.index) ? f5.index.getCell(i8, n10) : e10;
          const r13 = o6.symbol, c4 = r13.hash;
          for (const e11 of t9) if (c4 === e11.hash && Math.abs(i8 - e11.xTile) <= a7 && Math.abs(n10 - e11.yTile) <= a7) {
            const s6 = e11.unique;
            r13.unique = s6, s6.tileSymbols.push(r13), l12 = true;
            break;
          }
          l12 || y3.push(r13);
        }
      }
      y3.length > 0 && i7.set(n9, y3);
    }
    for (const s6 of e9.childrenTiles) this._matchSymbols(s6, o5, i7);
  }
  _createUniqueSymbolLayerArray() {
    const e9 = this._uniqueSymbolsReferences, s6 = new Array(e9.size);
    let o5, t9 = 0;
    for (const [l10, i7] of e9) {
      const e10 = new Array(i7.size);
      o5 = 0;
      for (const s7 of i7) e10[o5++] = s7;
      s6[t9] = { styleLayerUID: l10, uniqueSymbols: e10 }, t9++;
    }
    return s6;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/decluttering/SymbolFader.js
var n8 = 0.5;
var _ = 1e-6;
var m3 = class extends n {
  constructor(t9, e9) {
    super(), this.styleRepository = t9, this._tileToHandle = /* @__PURE__ */ new Map(), this._viewState = { scale: 0, rotation: 0, center: [0, 0], size: [0, 0] }, this._declutterViewState = { scale: 0, rotation: 0, center: [0, 0], size: [0, 0] }, this._completed = false, this._symbolRepository = new r11(4096, e9, () => new s3()), this._symbolDeclutterer = new i5(e9, this._symbolRepository, (t10, e10, i7) => new l7(t10, e10, i7, this.styleRepository, this._zoom, this._viewState.rotation), (t10, e10) => {
      t10.allSymbolsFadingOut = true, t10.lastOpacityUpdate = e10, i3(t10, e10, true), t10.decluttered = true, t10.requestRender();
    }, (t10, e10) => this.styleRepository.getStyleLayerByUID(t10.styleLayerUID).z - this.styleRepository.getStyleLayerByUID(e10.styleLayerUID).z, (t10) => {
      const e10 = this.styleRepository.getStyleLayerByUID(t10);
      if (this._zoom + _ < e10.minzoom || this._zoom - _ >= e10.maxzoom) return false;
      const i7 = e10.getLayoutProperty("visibility");
      return !i7 || i7.getValue() !== i.NONE;
    });
  }
  addTile(t9) {
    t9.decluttered = false, this._tileToHandle.set(t9, t9.on("symbols-changed", () => {
      this._symbolRepository.add(t9), this.restartDeclutter();
    })), this._symbolRepository.add(t9), this.restartDeclutter();
  }
  removeTile(t9) {
    const e9 = this._tileToHandle.get(t9);
    e9 && (this._symbolRepository.removeTile(t9), this.restartDeclutter(), e9.remove(), this._tileToHandle.delete(t9));
  }
  update(t9, e9) {
    return this._zoom = t9, this._viewState = { scale: e9.scale, rotation: e9.rotation, center: [e9.center[0], e9.center[1]], size: [e9.size[0], e9.size[1]] }, this._continueDeclutter(), this._completed;
  }
  restartDeclutter() {
    this._completed = false, this._symbolDeclutterer.restart(), this._notifyUnstable();
  }
  clear() {
    this._completed = false, this._symbolRepository = null, this._symbolDeclutterer.restart(), this._tileToHandle.forEach((t9) => t9.remove()), this._tileToHandle.clear();
  }
  get stale() {
    return this._zoom !== this._declutterZoom || this._viewState.size[0] !== this._declutterViewState.size[0] || this._viewState.size[1] !== this._declutterViewState.size[1] || this._viewState.scale !== this._declutterViewState.scale || this._viewState.rotation !== this._declutterViewState.rotation;
  }
  deleteStyleLayers(t9) {
    this._symbolRepository.deleteStyleLayers(t9);
  }
  _continueDeclutter() {
    this._completed && !this.stale || (this._symbolDeclutterer.running || (this._declutterZoom = this._zoom, this._declutterViewState.center[0] = this._viewState.center[0], this._declutterViewState.center[1] = this._viewState.center[1], this._declutterViewState.rotation = this._viewState.rotation, this._declutterViewState.scale = this._viewState.scale, this._declutterViewState.size[0] = this._viewState.size[0], this._declutterViewState.size[1] = this._viewState.size[1], this._symbolDeclutterer.restart()), this._symbolDeclutterer.setScreenSize(this._viewState.size[0], this._viewState.size[1]), this._completed = this._symbolDeclutterer.continue(c), this._completed && this._scheduleNotifyStable());
  }
  _scheduleNotifyStable() {
    r(this._stableNotificationHandle) && clearTimeout(this._stableNotificationHandle), this._stableNotificationHandle = setTimeout(() => {
      this._stableNotificationHandle = null, this.emit("fade-complete");
    }, (1 + n8) * e6);
  }
  _notifyUnstable() {
    r(this._stableNotificationHandle) && (clearTimeout(this._stableNotificationHandle), this._stableNotificationHandle = null), this.emit("fade-start");
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/RenderableTile.js
var t7 = class extends r6 {
  _createTransforms() {
    return { dvs: e5(), tileMat3: e5() };
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/VectorTileContainer.js
var f4 = 1e-6;
function _2(e9, t9) {
  if (e9) {
    const s6 = e9.getLayoutProperty("visibility");
    if (!s6 || s6.getValue() !== i.NONE && (void 0 === e9.minzoom || e9.minzoom < t9 + f4) && (void 0 === e9.maxzoom || e9.maxzoom >= t9 - f4)) return true;
  }
  return false;
}
var b2 = class extends i2 {
  constructor(e9) {
    super(e9), this._backgroundTiles = [], this._pointToCallbacks = /* @__PURE__ */ new Map();
  }
  destroy() {
    var _a, _b2;
    this.removeAllChildren(), (_a = this._spriteMosaic) == null ? void 0 : _a.dispose(), this._spriteMosaic = null, (_b2 = this._glyphMosaic) == null ? void 0 : _b2.dispose(), this._glyphMosaic = null, r(this._symbolFader) && (this._symbolFader.clear(), this._symbolFader = null), this._styleRepository = null, this._backgroundTiles = [], this._pointToCallbacks.clear();
  }
  setStyleResources(e9, r12, i7) {
    if (this._spriteMosaic = e9, this._glyphMosaic = r12, this._styleRepository = i7, t(this._symbolFader)) {
      const e10 = new m3(this._styleRepository, this.children);
      e10.on("fade-start", () => {
        this.emit("fade-start"), this.requestRender();
      }), e10.on("fade-complete", () => {
        this.emit("fade-complete"), this.requestRender();
      }), this._symbolFader = e10;
    }
    e2(this._symbolFader).styleRepository = i7;
  }
  setSpriteMosaic(e9) {
    var _a;
    (_a = this._spriteMosaic) == null ? void 0 : _a.dispose(), this._spriteMosaic = e9;
  }
  deleteStyleLayers(t9) {
    r(this._symbolFader) && this._symbolFader.deleteStyleLayers(t9);
  }
  async hitTest(e9) {
    const t9 = D();
    return this._pointToCallbacks.set(e9, t9), this.requestRender(), t9.promise;
  }
  enterTileInvalidation() {
    for (const e9 of this.children) e9.invalidating = true;
  }
  createRenderParams(e9) {
    return { ...super.createRenderParams(e9), renderPass: null, styleLayer: null, styleLayerUID: -1, glyphMosaic: this._glyphMosaic, spriteMosaic: this._spriteMosaic, hasClipping: !!this._clippingInfos };
  }
  doRender(e9) {
    !this.visible || e9.drawPhase !== T.MAP && e9.drawPhase !== T.DEBUG || void 0 === this._spriteMosaic || super.doRender(e9);
  }
  addChild(t9) {
    return super.addChild(t9), r(this._symbolFader) ? this._symbolFader.addTile(t9) : t9.decluttered = true, this.requestRender(), t9;
  }
  removeChild(t9) {
    return r(this._symbolFader) && this._symbolFader.removeTile(t9), this.requestRender(), super.removeChild(t9);
  }
  renderChildren(e9) {
    const { drawPhase: t9 } = e9;
    if (t9 !== T.DEBUG) {
      if (this._doRender(e9), this._pointToCallbacks.size > 0) {
        e9.drawPhase = T.HITTEST;
        const s6 = e9.painter.effects.hittestVTL;
        s6.bind(e9), this._doRender(e9), s6.draw(e9, this._pointToCallbacks), s6.unbind(e9), e9.drawPhase = t9;
      }
    } else super.renderChildren(e9);
  }
  removeAllChildren() {
    for (let t9 = 0; t9 < this.children.length; t9++) {
      const s6 = this.children[t9];
      r(this._symbolFader) && this._symbolFader.removeTile(s6), s6.dispose();
    }
    super.removeAllChildren();
  }
  getStencilTarget() {
    return this.children.filter((e9) => e9.neededForCoverage && e9.hasData());
  }
  restartDeclutter() {
    r(this._symbolFader) && this._symbolFader.restartDeclutter();
  }
  _doRender(e9) {
    const { context: t9 } = e9, s6 = this._styleRepository;
    if (!s6) return;
    const r12 = s6.layers;
    let i7 = true;
    e9.drawPhase === T.HITTEST && (i7 = false), s6.backgroundBucketIds.length > 0 && (e9.renderPass = "background", this._renderBackgroundLayers(e9, s6.backgroundBucketIds)), super.renderChildren(e9), e9.drawPhase === T.MAP && this._fade(e9.displayLevel, e9.state);
    const o5 = this.children.filter((e10) => e10.visible && e10.hasData());
    if (!o5 || 0 === o5.length) return t9.bindVAO(), t9.setStencilTestEnabled(true), void t9.setBlendingEnabled(true);
    for (const l10 of o5) l10.triangleCount = 0;
    t9.setStencilWriteMask(0), t9.setColorMask(true, true, true, true), t9.setStencilOp(O.KEEP, O.KEEP, O.REPLACE), t9.setStencilTestEnabled(true), t9.setBlendingEnabled(false), t9.setDepthTestEnabled(true), t9.setDepthWriteEnabled(true), t9.setDepthFunction(I.LEQUAL), t9.setClearDepth(1), t9.clear(t9.gl.DEPTH_BUFFER_BIT), e9.renderPass = "opaque";
    for (let l10 = r12.length - 1; l10 >= 0; l10--) this._renderStyleLayer(r12[l10], e9, o5);
    t9.setDepthWriteEnabled(false), t9.setBlendingEnabled(i7), t9.setBlendFunctionSeparate(R.ONE, R.ONE_MINUS_SRC_ALPHA, R.ONE, R.ONE_MINUS_SRC_ALPHA), e9.renderPass = "translucent";
    for (let l10 = 0; l10 < r12.length; l10++) this._renderStyleLayer(r12[l10], e9, o5);
    t9.bindVAO(), t9.setStencilTestEnabled(true), t9.setBlendingEnabled(true);
  }
  _fade(t9, s6) {
    r(this._symbolFader) && (this._symbolFader.update(t9, s6) || this.requestRender());
  }
  _renderStyleLayer(e9, t9, s6) {
    const { painter: r12, renderPass: i7 } = t9;
    if (void 0 === e9) return;
    const o5 = e9.getLayoutProperty("visibility");
    if (o5 && o5.getValue() === i.NONE) return;
    let a7;
    switch (e9.type) {
      case a4.BACKGROUND:
        return;
      case a4.FILL:
        if ("opaque" !== i7 && "translucent" !== t9.renderPass) return;
        a7 = "vtlFill";
        break;
      case a4.LINE:
        if ("translucent" !== i7) return;
        a7 = "vtlLine";
        break;
      case a4.CIRCLE:
        if ("translucent" !== i7) return;
        a7 = "vtlCircle";
        break;
      case a4.SYMBOL:
        if ("translucent" !== i7) return;
        a7 = "vtlSymbol";
    }
    if (s6 = e9.type === a4.SYMBOL ? s6.filter((e10) => e10.decluttered) : s6.filter((e10) => e10.neededForCoverage), "vtlSymbol" !== a7) {
      const r13 = t9.displayLevel;
      if (0 === s6.length || void 0 !== e9.minzoom && e9.minzoom >= r13 + f4 || void 0 !== e9.maxzoom && e9.maxzoom < r13 - f4) return;
    }
    const d2 = e9.uid;
    t9.styleLayerUID = d2, t9.styleLayer = e9;
    for (const l10 of s6) if (l10.layerData.has(d2)) {
      r12.renderObjects(t9, s6, a7);
      break;
    }
  }
  _renderBackgroundLayers(t9, s6) {
    const { context: r12, displayLevel: o5, painter: l10, state: h7 } = t9, m4 = this._styleRepository;
    let f5 = false;
    for (const e9 of s6) {
      if (m4.getLayerById(e9).type === a4.BACKGROUND && _2(m4.getLayerById(e9), o5)) {
        f5 = true;
        break;
      }
    }
    if (!f5) return;
    const b3 = this._tileInfoView.getTileCoverage(t9.state, 0, "smallest"), { spans: g3, lodInfo: T2 } = b3, { level: E7 } = T2, C2 = u(), L2 = [];
    if (this._renderPasses) {
      const s7 = this._renderPasses[0];
      r(this._clippingInfos) && (s7.brushes[0].prepareState(t9), s7.brushes[0].drawMany(t9, this._clippingInfos));
    }
    const v = this._backgroundTiles;
    let R2, S = 0;
    for (const { row: e9, colFrom: n9, colTo: a7 } of g3) for (let t10 = n9; t10 <= a7; t10++) {
      if (S < v.length) R2 = v[S], R2.key.set(E7, e9, T2.normalizeCol(t10), T2.getWorldForColumn(t10)), this._tileInfoView.getTileBounds(C2, R2.key, false), R2.x = C2[0], R2.y = C2[3], R2.resolution = this._tileInfoView.getTileResolution(E7);
      else {
        const s7 = new e4(E7, e9, T2.normalizeCol(t10), T2.getWorldForColumn(t10)), r13 = this._tileInfoView.getTileBounds(u(), s7), o6 = this._tileInfoView.getTileResolution(E7);
        R2 = new t7(s7, o6, r13[0], r13[3], 512, 512, 4096, 4096), v.push(R2);
      }
      R2.setTransform(h7), L2.push(R2), S++;
    }
    r12.setStencilWriteMask(0), r12.setColorMask(true, true, true, true), r12.setStencilOp(O.KEEP, O.KEEP, O.REPLACE), r12.setStencilFunction(I.EQUAL, 0, 255);
    let F2 = true;
    t9.drawPhase === T.HITTEST && (F2 = false), r12.setStencilTestEnabled(F2);
    for (const e9 of s6) {
      const s7 = m4.getLayerById(e9);
      s7.type === a4.BACKGROUND && _2(s7, o5) && (t9.styleLayerUID = s7.uid, t9.styleLayer = s7, l10.renderObjects(t9, L2, "vtlBackground"));
    }
    l2.pool.release(b3);
  }
};

// node_modules/@arcgis/core/views/2d/layers/support/DebugOverlay.js
var u6 = { geometry: [new t2("a_PositionAndFlags", 3, C.SHORT, 0, 6)] };
var d = /* @__PURE__ */ new Map();
d.set("a_PositionAndFlags", 0);
var g2 = { vsPath: "debug/overlay", fsPath: "debug/overlay", attributes: d };
var l9 = class extends r5 {
  constructor(e9) {
    super(), this._conf = e9;
  }
  static makeFlags(e9, t9) {
    return e9 | t9 << 2;
  }
  _createTransforms() {
    return { dvs: e5() };
  }
  doRender(e9) {
    this._updateTransforms(e9), this._ensureResources(e9);
    const { context: t9 } = e9;
    t9.useProgram(this._program), this._program.setUniformMatrix3fv("u_dvsMat3", this.transforms.dvs), this._program.setUniform4fv("u_colors", this._conf.getColors(e9)), this._program.setUniform1fv("u_opacities", this._conf.getOpacities(e9));
    const { vertexData: r12, indexData: s6 } = this._conf.getMesh(e9);
    this._vertexBuffer.setData(r12), this._indexBuffer.setData(s6), t9.bindVAO(this._vertexArray), t9.setBlendingEnabled(true), t9.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), t9.setDepthTestEnabled(false), t9.setStencilTestEnabled(false), t9.setColorMask(true, true, true, true), t9.drawElements(E3.TRIANGLES, s6.length, C.UNSIGNED_INT, 0);
  }
  onDetach() {
    this._vertexArray = h(this._vertexArray);
  }
  _updateTransforms(e9) {
    r2(this.transforms.dvs), M(this.transforms.dvs, this.transforms.dvs, [-1, 1]), f(this.transforms.dvs, this.transforms.dvs, [2 / e9.state.size[0], -2 / e9.state.size[1], 1]);
  }
  _ensureResources(e9) {
    const { context: t9 } = e9;
    this._program || (this._program = e9.painter.materialManager.getProgram(g2)), this._vertexBuffer || (this._vertexBuffer = E5.createVertex(t9, F.STREAM_DRAW)), this._indexBuffer || (this._indexBuffer = E5.createIndex(t9, F.STREAM_DRAW)), this._vertexArray || (this._vertexArray = new f2(t9, d, u6, { geometry: this._vertexBuffer }, this._indexBuffer));
  }
};

// node_modules/@arcgis/core/views/2d/tiling/TileInfoViewPOT.js
var t8 = class extends h4 {
  constructor() {
    super(...arguments), this._fullCacheLodInfos = null, this._levelByScale = {};
  }
  getTileParentId(e9) {
    const l10 = e4.pool.acquire(e9), t9 = 0 === l10.level ? null : e4.getId(l10.level - 1, l10.row >> 1, l10.col >> 1, l10.world);
    return e4.pool.release(l10), t9;
  }
  getTileCoverage(e9, l10, s6) {
    const t9 = super.getTileCoverage(e9, l10, s6);
    if (!t9) return t9;
    const o5 = 1 << t9.lodInfo.level;
    return t9.spans = t9.spans.filter((e10) => e10.row >= 0 && e10.row < o5), t9;
  }
  scaleToLevel(e9) {
    if (this._fullCacheLodInfos || this._initializeFullCacheLODs(this._lodInfos), this._levelByScale[e9]) return this._levelByScale[e9];
    {
      const l10 = this._fullCacheLodInfos;
      if (e9 > l10[0].scale) return l10[0].level;
      let s6, t9;
      for (let o5 = 0; o5 < l10.length - 1; o5++) if (t9 = l10[o5 + 1], e9 > t9.scale) return s6 = l10[o5], s6.level + (s6.scale - e9) / (s6.scale - t9.scale);
      return l10[l10.length - 1].level;
    }
  }
  _initializeFullCacheLODs(l10) {
    let s6;
    if (0 === l10[0].level) s6 = l10.map((e9) => ({ level: e9.level, resolution: e9.resolution, scale: e9.scale }));
    else {
      const l11 = this.tileInfo.size[0], t9 = this.tileInfo.spatialReference;
      s6 = j2.create({ size: l11, spatialReference: t9 }).lods.map((e9) => ({ level: e9.level, resolution: e9.resolution, scale: e9.scale }));
    }
    for (let e9 = 0; e9 < s6.length; e9++) this._levelByScale[s6[e9].scale] = s6[e9].level;
    this._fullCacheLodInfos = s6;
  }
};

// node_modules/@arcgis/core/views/2d/layers/VectorTileLayerView2D.js
var Q = class extends f3(u3) {
  constructor() {
    super(...arguments), this._styleChanges = [], this._fetchQueue = null, this._parseQueue = null, this._tileHandlerPromise = null, this._isTileHandlerReady = false, this._collisionOverlay = null, this.fading = false, this._getCollidersMesh = (e9) => {
      const { pixelRatio: t9 } = e9.state;
      let i7 = 0;
      const s6 = [], r12 = [];
      for (const a7 of this._vectorTileContainer.children) if (a7.symbols) for (const [e10, l10] of a7.symbols) for (const a8 of l10) for (const e11 of a8.colliders) {
        const l11 = (e11.xScreen + e11.dxScreen) * t9, n9 = (e11.yScreen + e11.dyScreen) * t9, o5 = e11.width * t9, h7 = e11.height * t9, u7 = a8.unique.parts[e11.partIndex].targetOpacity > 0.5;
        if (!u7 && "all" !== this.layer.showCollisionBoxes) continue;
        const c4 = 3, y3 = 1, p2 = 3, d2 = 0, _3 = u7 ? 2 : 0, f5 = u7 ? 3 : 0, g3 = l9.makeFlags(_3, f5);
        s6.push(l11, n9, g3, l11 + o5, n9, g3, l11, n9 + h7, g3, l11 + o5, n9 + h7, g3), r12.push(i7 + 0, i7 + 1, i7 + 2, i7 + 1, i7 + 3, i7 + 2), i7 += 4;
        const m4 = u7 ? c4 : y3, C2 = u7 ? p2 : d2, T2 = l9.makeFlags(m4, C2);
        s6.push(l11, n9, T2, l11 + o5, n9, T2, l11, n9 + 1, T2, l11 + o5, n9 + 1, T2), r12.push(i7 + 0, i7 + 1, i7 + 2, i7 + 1, i7 + 3, i7 + 2), i7 += 4, s6.push(l11, n9 + h7 - 1, T2, l11 + o5, n9 + h7 - 1, T2, l11, n9 + h7, T2, l11 + o5, n9 + h7, T2), r12.push(i7 + 0, i7 + 1, i7 + 2, i7 + 1, i7 + 3, i7 + 2), i7 += 4, s6.push(l11, n9, T2, l11 + 1, n9, T2, l11, n9 + h7, T2, l11 + 1, n9 + h7, T2), r12.push(i7 + 0, i7 + 1, i7 + 2, i7 + 1, i7 + 3, i7 + 2), i7 += 4, s6.push(l11 + o5 - 1, n9, T2, l11 + o5, n9, T2, l11 + o5 - 1, n9 + h7, T2, l11 + o5, n9 + h7, T2), r12.push(i7 + 0, i7 + 1, i7 + 2, i7 + 1, i7 + 3, i7 + 2), i7 += 4;
      }
      return { vertexData: new Int16Array(s6), indexData: new Uint32Array(r12) };
    }, this._getCollidersColors = () => [1, 0.5, 0, 1, 1, 0, 0, 1, 0, 1, 0.5, 1, 0, 0.5, 0, 1], this._getCollidersOpacities = () => [0.05, 0.01, 0.15, 0.2];
  }
  async hitTest(e9, i7) {
    if (!this._tileHandlerPromise) return null;
    await this._tileHandlerPromise;
    const s6 = await this._vectorTileContainer.hitTest(i7);
    if (!s6 || 0 === s6.length) return null;
    const r12 = s6[0] - 1, a7 = this._styleRepository, l10 = a7.getStyleLayerByUID(r12);
    if (!l10) return null;
    const n9 = a7.getStyleLayerIndex(l10.id);
    return [{ type: "graphic", mapPoint: e9, layer: this.layer, graphic: new g({ attributes: { layerId: n9, layerName: l10.id, layerUID: r12 }, layer: this.layer, sourceLayer: this.layer }) }];
  }
  update(e9) {
    if (this._tileHandlerPromise && this._isTileHandlerReady) return e9.pixelRatio !== this._tileHandler.devicePixelRatio ? (this._start(), void (this._tileHandler.devicePixelRatio = e9.pixelRatio)) : void (this._styleChanges.length > 0 ? this._tileHandlerPromise = this._applyStyleChanges() : (this._fetchQueue.pause(), this._parseQueue.pause(), this._fetchQueue.state = e9.state, this._parseQueue.state = e9.state, this._tileManager.update(e9) || this.requestUpdate(), this._parseQueue.resume(), this._fetchQueue.resume()));
  }
  attach() {
    const { style: e9 } = this.layer.currentStyleInfo;
    this._styleRepository = new l4(e9), this._tileInfoView = new t8(this.layer.tileInfo, this.layer.fullExtent), this._vectorTileContainer = new b2(this._tileInfoView), this._tileHandler = new n5(this.layer, this._styleRepository, window.devicePixelRatio || 1), this.container.addChild(this._vectorTileContainer), this._start(), this.addAttachHandles([this._vectorTileContainer.on("fade-start", () => {
      this.fading = true, this.notifyChange("updating"), this.requestUpdate();
    }), this._vectorTileContainer.on("fade-complete", () => {
      var _a;
      (_a = this._collisionOverlay) == null ? void 0 : _a.requestRender(), this.fading = false, this.notifyChange("updating"), this.requestUpdate();
    }), l(() => this.layer.showCollisionBoxes, (e10) => {
      "none" !== e10 ? this._collisionOverlay || (this._collisionOverlay = new l9({ getMesh: this._getCollidersMesh, getColors: this._getCollidersColors, getOpacities: this._getCollidersOpacities }), this.container.addChild(this._collisionOverlay)) : this._collisionOverlay && (this.container.removeChild(this._collisionOverlay), this._collisionOverlay = null), this.container.requestRender();
    }, h2), this.layer.on("paint-change", (e10) => {
      if (e10.isDataDriven) this._styleChanges.push({ type: I2.PAINTER_CHANGED, data: e10 }), this.notifyChange("updating"), this.requestUpdate();
      else {
        const t9 = this._styleRepository, i7 = t9.getLayerById(e10.layer);
        if (!i7) return;
        const s6 = i7.type === a4.SYMBOL;
        t9.setPaintProperties(e10.layer, e10.paint), s6 && this._vectorTileContainer.restartDeclutter(), this._vectorTileContainer.requestRender();
      }
    }), this.layer.on("layout-change", (e10) => {
      const t9 = this._styleRepository, i7 = t9.getLayerById(e10.layer);
      if (!i7) return;
      const s6 = m(i7.layout, e10.layout);
      if (!t(s6)) {
        if (a3(s6, "visibility") && 1 === L(s6)) return t9.setLayoutProperties(e10.layer, e10.layout), i7.type === a4.SYMBOL && this._vectorTileContainer.restartDeclutter(), void this._vectorTileContainer.requestRender();
        this._styleChanges.push({ type: I2.LAYOUT_CHANGED, data: e10 }), this.notifyChange("updating"), this.requestUpdate();
      }
    }), this.layer.on("style-layer-visibility-change", (e10) => {
      const t9 = this._styleRepository, i7 = t9.getLayerById(e10.layer);
      i7 && (t9.setStyleLayerVisibility(e10.layer, e10.visibility), i7.type === a4.SYMBOL && this._vectorTileContainer.restartDeclutter(), this._vectorTileContainer.requestRender());
    }), this.layer.on("style-layer-change", (e10) => {
      this._styleChanges.push({ type: I2.LAYER_CHANGED, data: e10 }), this.notifyChange("updating"), this.requestUpdate();
    }), this.layer.on("delete-style-layer", (e10) => {
      this._styleChanges.push({ type: I2.LAYER_REMOVED, data: e10 }), this.notifyChange("updating"), this.requestUpdate();
    }), this.layer.on("load-style", () => this._loadStyle()), this.layer.on("spriteSource-change", (e10) => {
      this._newSpriteSource = e10.spriteSource, this._styleChanges.push({ type: I2.SPRITES_CHANGED, data: null });
      const t9 = this._styleRepository.layers;
      for (const i7 of t9) switch (i7.type) {
        case a4.SYMBOL:
          i7.getLayoutProperty("icon-image") && this._styleChanges.push({ type: I2.LAYOUT_CHANGED, data: { layer: i7.id, layout: i7.layout } });
          break;
        case a4.LINE:
          i7.getPaintProperty("line-pattern") && this._styleChanges.push({ type: I2.PAINTER_CHANGED, data: { layer: i7.id, paint: i7.paint, isDataDriven: i7.isPainterDataDriven() } });
          break;
        case a4.FILL:
          i7.getLayoutProperty("fill-pattern") && this._styleChanges.push({ type: I2.PAINTER_CHANGED, data: { layer: i7.id, paint: i7.paint, isDataDriven: i7.isPainterDataDriven() } });
      }
      this.notifyChange("updating"), this.requestUpdate();
    })]);
  }
  detach() {
    this._stop(), this.container.removeAllChildren(), this._vectorTileContainer = a(this._vectorTileContainer), this._tileHandler = a(this._tileHandler);
  }
  moveStart() {
    this.requestUpdate();
  }
  viewChange() {
    this.requestUpdate();
  }
  moveEnd() {
    this._collisionOverlay && this._vectorTileContainer.restartDeclutter(), this.requestUpdate();
  }
  supportsSpatialReference(e9) {
    var _a;
    return E2((_a = this.layer.tileInfo) == null ? void 0 : _a.spatialReference, e9);
  }
  canResume() {
    let e9 = super.canResume();
    const { currentStyleInfo: t9 } = this.layer;
    if (e9 && (t9 == null ? void 0 : t9.layerDefinition)) {
      const i7 = this.view.scale, { minScale: s6, maxScale: r12 } = t9.layerDefinition;
      t9 && t9.layerDefinition && (s6 && s6 < i7 && (e9 = false), r12 && r12 > i7 && (e9 = false));
    }
    return e9;
  }
  isUpdating() {
    const e9 = this._vectorTileContainer.children;
    return !this._isTileHandlerReady || !this._fetchQueue || !this._parseQueue || this._fetchQueue.updating || this._parseQueue.updating || e9.length > 0 && e9.some((e10) => e10.invalidating) || this.fading;
  }
  acquireTile(e9) {
    var _a;
    const t9 = this._createVectorTile(e9);
    return (_a = this._tileHandlerPromise) == null ? void 0 : _a.then(() => {
      this._fetchQueue.push(t9.key).then((e10) => this._parseQueue.push({ key: t9.key, data: e10 })).then((e10) => {
        t9.once("attach", () => this.requestUpdate()), t9.setData(e10), this.requestUpdate(), this.notifyChange("updating");
      }).catch((e10) => {
        this.notifyChange("updating"), j(e10) || s.getLogger(this.declaredClass).error(e10);
      });
    }), t9;
  }
  releaseTile(e9) {
    const t9 = e9.key.id;
    this._fetchQueue.abort(t9), this._parseQueue.abort(t9), this.requestUpdate();
  }
  _start() {
    if (this._stop(), this._tileManager = new a6({ acquireTile: (e10) => this.acquireTile(e10), releaseTile: (e10) => this.releaseTile(e10), tileInfoView: this._tileInfoView }, this._vectorTileContainer), !this.layer.currentStyleInfo) return;
    const e9 = new AbortController(), t9 = this._tileHandler.start({ signal: e9.signal }).then(() => {
      this._fetchQueue = new y2({ tileInfoView: this._tileInfoView, process: (e10, t10) => this._getTileData(e10, t10), concurrency: 15 }), this._parseQueue = new y2({ tileInfoView: this._tileInfoView, process: (e10, t10) => this._parseTileData(e10, t10), concurrency: 8 }), this.requestUpdate(), this._isTileHandlerReady = true;
    });
    this._tileHandler.spriteMosaic.then((e10) => {
      this._vectorTileContainer.setStyleResources(e10, this._tileHandler.glyphMosaic, this._styleRepository), this.requestUpdate();
    }), this._tileHandlerAbortController = e9, this._tileHandlerPromise = t9;
  }
  _stop() {
    if (!this._tileHandlerAbortController || !this._vectorTileContainer) return;
    const e9 = this._tileHandlerAbortController;
    e9 && e9.abort(), this._tileHandlerPromise = null, this._isTileHandlerReady = false, this._fetchQueue = a(this._fetchQueue), this._parseQueue = a(this._parseQueue), this._tileManager = a(this._tileManager), this._vectorTileContainer.removeAllChildren();
  }
  async _getTileData(e9, t9) {
    const i7 = await this._tileHandler.fetchTileData(e9, t9);
    return this.notifyChange("updating"), i7;
  }
  async _parseTileData(e9, t9) {
    return this._tileHandler.parseTileData(e9, t9);
  }
  async _applyStyleChanges() {
    this._isTileHandlerReady = false, this._fetchQueue.pause(), this._parseQueue.pause(), this._fetchQueue.clear(), this._parseQueue.clear(), this._tileManager.clearCache();
    const e9 = this._styleChanges;
    try {
      await this._tileHandler.updateStyle(e9);
    } catch (n9) {
      s.getLogger(this.declaredClass).error("error applying vector-tiles style update", n9.message), this._fetchQueue.resume(), this._parseQueue.resume(), this._isTileHandlerReady = true;
    }
    const t9 = this._styleRepository, i7 = [];
    e9.forEach((e10) => {
      if (e10.type !== I2.LAYER_REMOVED) return;
      const s6 = e10.data, r13 = t9.getLayerById(s6.layer);
      r13 && i7.push(r13.uid);
    });
    const r12 = [];
    let a7;
    e9.forEach((e10) => {
      const i8 = e10.type, s6 = e10.data;
      switch (i8) {
        case I2.PAINTER_CHANGED:
          t9.setPaintProperties(s6.layer, s6.paint), a7 = s6.layer;
          break;
        case I2.LAYOUT_CHANGED:
          t9.setLayoutProperties(s6.layer, s6.layout), a7 = s6.layer;
          break;
        case I2.LAYER_REMOVED:
          return void t9.deleteStyleLayer(s6.layer);
        case I2.LAYER_CHANGED:
          t9.setStyleLayer(s6.layer, s6.index), a7 = s6.layer.id;
          break;
        case I2.SPRITES_CHANGED:
          this._vectorTileContainer.setSpriteMosaic(this._tileHandler.setSpriteSource(this._newSpriteSource)), this._newSpriteSource = null, a7 = null;
      }
      const l11 = t9.getLayerById(a7);
      l11 && r12.push(l11.uid);
    });
    const l10 = this._vectorTileContainer.children;
    if (i7.length > 0) {
      this._vectorTileContainer.deleteStyleLayers(i7);
      for (const e10 of l10) e10.deleteLayerData(i7);
    }
    if (this._fetchQueue.resume(), this._parseQueue.resume(), r12.length > 0) {
      const e10 = [];
      for (const t10 of l10) {
        const i8 = this._fetchQueue.push(t10.key).then((e11) => this._parseQueue.push({ key: t10.key, data: e11, styleLayerUIDs: r12 })).then((e11) => t10.setData(e11));
        e10.push(i8);
      }
      await Promise.all(e10);
    }
    this._styleChanges = [], this._isTileHandlerReady = true, this.notifyChange("updating"), this.requestUpdate();
  }
  async _loadStyle() {
    const { style: e9 } = this.layer.currentStyleInfo, t9 = p(e9);
    this._isTileHandlerReady = false, this._fetchQueue.pause(), this._parseQueue.pause(), this._fetchQueue.clear(), this._parseQueue.clear(), this.notifyChange("updating"), this._styleRepository = new l4(t9), this._vectorTileContainer.destroy(), this._tileManager.clear(), this._tileHandlerAbortController.abort(), this._tileHandlerAbortController = new AbortController();
    const { signal: s6 } = this._tileHandlerAbortController;
    try {
      this._tileHandlerPromise = this._tileHandler.setStyle(this._styleRepository, t9), await this._tileHandlerPromise;
    } catch (a7) {
      if (!j(a7)) throw a7;
    }
    if (s6.aborted) return this._fetchQueue.resume(), this._parseQueue.resume(), this._isTileHandlerReady = true, this.notifyChange("updating"), void this.requestUpdate();
    const r12 = await this._tileHandler.spriteMosaic;
    this._vectorTileContainer.setStyleResources(r12, this._tileHandler.glyphMosaic, this._styleRepository), this._fetchQueue.resume(), this._parseQueue.resume(), this._isTileHandlerReady = true, this.notifyChange("updating"), this.requestUpdate();
  }
  _createVectorTile(e9) {
    const t9 = this._tileInfoView.getTileBounds(u(), e9), i7 = this._tileInfoView.getTileResolution(e9.level);
    return new m2(e9, i7, t9[0], t9[3], 512, 512, this._styleRepository);
  }
};
function L(e9) {
  if (t(e9)) return 0;
  switch (e9.type) {
    case "partial":
      return Object.keys(e9.diff).length;
    case "complete":
      return Math.max(Object.keys(e9.oldValue).length, Object.keys(e9.newValue).length);
    case "collection":
      return Object.keys(e9.added).length + Object.keys(e9.changed).length + Object.keys(e9.removed).length;
  }
}
e([y()], Q.prototype, "_fetchQueue", void 0), e([y()], Q.prototype, "_parseQueue", void 0), e([y()], Q.prototype, "_isTileHandlerReady", void 0), e([y()], Q.prototype, "fading", void 0), Q = e([a2("esri.views.2d.layers.VectorTileLayerView2D")], Q);
var I3 = Q;
export {
  I3 as default
};
//# sourceMappingURL=VectorTileLayerView2D-CLLW3IBY.js.map
