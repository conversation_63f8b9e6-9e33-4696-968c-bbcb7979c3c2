import {
  D,
  kt
} from "./chunk-U4SVMKOQ.js";
import {
  has
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/support/revision.js
var c = "20230301";
var e = "2657e728c1857e6d94c324181c0788310bb0958a";

// node_modules/@arcgis/core/kernel.js
var a = "4.26";
var s = { async request(e2, a2) {
  var _a, _b;
  const { default: s2 } = await import("./@arcgis_core_request__js.js"), r2 = e2.options, n2 = r2.responseType;
  r2.signal = a2 == null ? void 0 : a2.signal, r2.responseType = "native" === n2 || "native-request-init" === n2 ? "native-request-init" : n2 && ["blob", "json", "text"].includes(n2) && ((_a = D(e2.url)) == null ? void 0 : _a.after) ? n2 : "array-buffer";
  const o2 = await s2(e2.url, r2), i = { data: o2.data, httpStatus: o2.httpStatus, ssl: o2.ssl };
  switch ((_b = o2.requestOptions) == null ? void 0 : _b.responseType) {
    case "native-request-init":
      return delete i.data.signal, i;
    case "blob":
      i.data = await i.data.arrayBuffer();
      break;
    case "json":
      i.data = new TextEncoder().encode(JSON.stringify(i.data)).buffer;
      break;
    case "text":
      i.data = new TextEncoder().encode(i.data).buffer;
  }
  return { result: i, transferList: [i.data] };
} };
var r;
function n(t) {
  r = t;
}
function o(t) {
  const a2 = r && r.findCredential(t);
  return a2 && a2.token ? kt(t, "token", a2.token) : t;
}
has("host-webworker");

export {
  c,
  e,
  a,
  s,
  r,
  n,
  o
};
//# sourceMappingURL=chunk-WXFAAYJL.js.map
