/**
 * 数字雨特效方案
 * 实现了从下往上流动的数字效果，增强空间感和深度
 */

export function initDigitalRainEffect(canvasId) {
  let animationFrameId = null;
  let canvas, ctx;
  let columns = [];
  const fontSize = 16;
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()|<>?水电气热管网运营智能监控系统安全可靠';
  // 主题颜色 - 蓝色调
  const themeColor = {
    r: 0,
    g: 120,
    b: 255
  };

  const init = () => {
    canvas = document.getElementById(canvasId);
    if (!canvas) return;
    
    ctx = canvas.getContext('2d');
    
    // 设置canvas尺寸为窗口大小
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      initColumns();
    };
    
    // 初始化列
    const initColumns = () => {
      const columnCount = Math.floor(canvas.width / fontSize) * 1.5; // 增加列数增强密度
      columns = [];
      
      for (let i = 0; i < columnCount; i++) {
        // 随机z深度 - 创造空间感
        const depth = Math.random() * 0.8 + 0.2; // 0.2到1.0之间的深度值，1.0最近
        
        columns[i] = {
          x: Math.random() * canvas.width, // 随机x位置，不再限制在网格上
          y: canvas.height + Math.random() * canvas.height, // 起始位置在屏幕下方
          length: Math.floor(Math.random() * 15 + 5), // 每列长度
          speed: (Math.random() * 1.5 + 0.5) * depth * 2, // 速度与深度相关，越近越快
          chars: [], // 存储字符
          startDelay: Math.random() * 5000, // 随机延迟开始时间
          depth: depth, // 深度值
          fontScale: depth * 0.8 + 0.4, // 字体大小随深度变化
          opacityFactor: depth * 0.7 + 0.3 // 透明度随深度变化
        };
        
        // 为每列初始化字符
        for (let j = 0; j < columns[i].length; j++) {
          columns[i].chars[j] = characters.charAt(Math.floor(Math.random() * characters.length));
        }
      }
      
      // 按深度排序，先绘制远处的列，后绘制近处的列
      columns.sort((a, b) => a.depth - b.depth);
    };
    
    // 绘制数字流
    const drawDigitalFlow = (timestamp) => {
      // 半透明的黑色背景，形成拖尾效果
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // 添加光晕效果增加空间感
      const gradient = ctx.createRadialGradient(
        canvas.width / 2, 
        canvas.height / 2, 
        0,
        canvas.width / 2, 
        canvas.height / 2, 
        canvas.width * 0.7
      );
      gradient.addColorStop(0, 'rgba(0, 60, 120, 0.1)');
      gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      columns.forEach((column, colIndex) => {
        // 检查是否应该开始绘制这一列（基于延迟）
        if (timestamp < column.startDelay) return;
        
        // 根据深度调整字体大小
        const scaledFontSize = Math.floor(fontSize * column.fontScale);
        ctx.font = `${scaledFontSize}px monospace`;
        ctx.textAlign = 'center';
        
        // 绘制列中的每个字符 - 从下往上
        for (let i = 0; i < column.length; i++) {
          // 从下往上计算位置，头部在下方
          const y = column.y + i * scaledFontSize * 0.9; // 字符间距略微压缩
          
          // 只绘制在可见区域内的字符
          if (y < canvas.height + scaledFontSize && y > 0) {
            // 计算颜色 - 底部字符颜色深，顶部字符颜色浅
            // 注意：由于方向反转，头部现在是最下面的字符
            const alphaBase = 1 - (column.length - i - 1) / column.length; // 基础透明度，反转索引计算
            const alpha = (i === column.length - 1 ? 1 : alphaBase * 0.8) * column.opacityFactor; // 头部字符更亮（现在是最下面的）
            
            // 颜色从下到上逐渐变浅
            const brightness = 0.5 + 0.5 * ((column.length - i - 1) / column.length); // 亮度因子，从下往上变亮
            const r = Math.floor(themeColor.r * brightness * (0.7 + column.depth * 0.3)); // 深度影响颜色
            const g = Math.floor(themeColor.g * brightness * (0.7 + column.depth * 0.3));
            const b = Math.floor(themeColor.b * brightness * (0.7 + column.depth * 0.3));
            
            ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${alpha})`;
            
            // 随机更换头部字符（现在头部是最下面的字符）
            if (i === column.length - 1 && Math.random() > 0.95) {
              column.chars[i] = characters.charAt(Math.floor(Math.random() * characters.length));
            }
            
            // 添加发光效果增强空间感
            if (i === column.length - 1) { // 只给头部字符添加发光效果
              ctx.shadowColor = `rgba(${r}, ${g}, ${b}, 0.7)`;
              ctx.shadowBlur = 10 * column.depth;
            } else {
              ctx.shadowBlur = 0;
            }
            
            ctx.fillText(column.chars[i], column.x, y);
          }
        }
        
        // 更新列的位置 - 向上移动
        column.y -= column.speed;
        
        // 当列完全移出屏幕顶部时，重置到底部
        if (column.y < -column.length * scaledFontSize) {
          // 随机更新深度，以便创造动态变化
          column.depth = Math.random() * 0.8 + 0.2;
          column.fontScale = column.depth * 0.8 + 0.4;
          column.opacityFactor = column.depth * 0.7 + 0.3;
          column.speed = (Math.random() * 1.5 + 0.5) * column.depth * 2;
          
          column.y = canvas.height + scaledFontSize;
          column.x = Math.random() * canvas.width; // 重新随机x位置
          column.length = Math.floor(Math.random() * 15 + 5);
          
          // 重新生成字符
          column.chars = [];
          for (let j = 0; j < column.length; j++) {
            column.chars[j] = characters.charAt(Math.floor(Math.random() * characters.length));
          }
        }
      });
      
      // 每隔一段时间重新排序列，保持深度正确
      if (timestamp % 1000 < 20) {
        columns.sort((a, b) => a.depth - b.depth);
      }
      
      animationFrameId = requestAnimationFrame(drawDigitalFlow);
    };
    
    // 初始化
    resizeCanvas();
    animationFrameId = requestAnimationFrame(drawDigitalFlow);
    
    // 窗口大小变化时重置canvas
    window.addEventListener('resize', resizeCanvas);
    
    // 返回清理函数
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      window.removeEventListener('resize', resizeCanvas);
    };
  };

  // 初始化并返回清理函数
  return init();
} 