import {
  T
} from "./chunk-VX64NK2J.js";
import {
  T as T2
} from "./chunk-T3GGN2P7.js";
import {
  L,
  O,
  Re,
  U,
  Y,
  ie,
  t as t2,
  v as v2
} from "./chunk-REVHHZEO.js";
import {
  c
} from "./chunk-6OFWBRK2.js";
import {
  e as e2,
  t
} from "./chunk-YFVPK4WM.js";
import {
  ut
} from "./chunk-B4KDIR4O.js";
import {
  v
} from "./chunk-ZACBBT3Y.js";
import {
  p,
  w
} from "./chunk-63M4K32A.js";
import {
  e,
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/arcade/Feature.js
var g = class _g {
  constructor() {
    this.arcadeDeclaredClass = "esri.arcade.Feature", this._optimizedGeomDefinition = null, this._geometry = null, this.attributes = null, this._layer = null, this._datesfixed = true, this.dateTimeReferenceFieldIndex = null, this.contextTimeReference = null, this.immutable = true, this._datefields = null, this.immutable = true;
  }
  static createFromGraphic(e3, t3) {
    const i = new _g();
    return i.contextTimeReference = t3 ?? null, i._geometry = r(e3.geometry) ? e3.geometry : null, void 0 === e3.attributes || null === e3.attributes ? i.attributes = {} : i.attributes = e3.attributes, e3._sourceLayer ? (i._layer = e3._sourceLayer, i._datesfixed = false) : e3._layer ? (i._layer = e3._layer, i._datesfixed = false) : e3.layer && "fields" in e3.layer ? (i._layer = e3.layer, i._datesfixed = false) : e3.sourceLayer && "fields" in e3.sourceLayer && (i._layer = e3.sourceLayer, i._datesfixed = false), i._layer && false === i._datesfixed && (void 0 !== i._layer.dateTimeReferenceFieldIndex ? i.dateTimeReferenceFieldIndex = i._layer.dateTimeReferenceFieldIndex : i.dateTimeReferenceFieldIndex = T2.createFromLayer(i._layer)), i;
  }
  static createFromArcadeFeature(e3) {
    const t3 = new _g();
    return t3._datesfixed = e3._datesfixed, t3.attributes = e3.attributes, t3._geometry = e3._geometry, t3._optimizedGeomDefinition = e3._optimizedGeomDefinition, e3._layer && (t3._layer = e3._layer), t3.dateTimeReferenceFieldIndex = e3.dateTimeReferenceFieldIndex, t3.contextTimeReference = e3.contextTimeReference, t3;
  }
  static createFromOptimisedFeature(e3, t3, i) {
    const r2 = new _g();
    return r2._geometry = e3.geometry ? { geometry: e3.geometry } : null, r2._optimizedGeomDefinition = i, r2.attributes = e3.attributes || {}, r2._layer = t3, r2._datesfixed = false, r2;
  }
  static createFromArcadeDictionary(t3) {
    const i = new _g();
    return i.attributes = t3.field("attributes"), null !== i.attributes && i.attributes instanceof T ? (i.attributes = i.attributes.attributes, null === i.attributes && (i.attributes = {})) : i.attributes = {}, i._geometry = t3.field("geometry"), null !== i._geometry && (i._geometry instanceof T ? i._geometry = _g.parseGeometryFromDictionary(i._geometry) : i._geometry instanceof p || (i._geometry = null)), i;
  }
  static createFromGraphicLikeObject(e3, t3, i = null, r2) {
    const s = new _g();
    return s.contextTimeReference = r2 ?? null, null === t3 && (t3 = {}), s.attributes = t3, s._geometry = r(e3) ? e3 : null, s._layer = i, s._layer && (s._datesfixed = false, void 0 !== s._layer.dateTimeReferenceFieldIndex ? s.dateTimeReferenceFieldIndex = s._layer.dateTimeReferenceFieldIndex : s.dateTimeReferenceFieldIndex = T2.createFromLayer(s._layer)), s;
  }
  repurposeFromGraphicLikeObject(e3, t3, i = null) {
    null === t3 && (t3 = {}), this.attributes = t3, this._geometry = e3 || null, this._layer = i, this._layer ? this._datesfixed = false : this._datesfixed = true;
  }
  get layerPreferredTimeZone() {
    var _a;
    return ((_a = this.dateTimeReferenceFieldIndex) == null ? void 0 : _a.layerPreferredTimeZone) ?? "";
  }
  fieldSourceTimeZone(e3) {
    var _a;
    return ((_a = this.dateTimeReferenceFieldIndex) == null ? void 0 : _a.fieldTimeZone(e3)) ?? "";
  }
  castToText(e3 = false) {
    let a = "";
    false === this._datesfixed && this._fixDates();
    for (const o in this.attributes) {
      "" !== a && (a += ",");
      const l = this.attributes[o];
      null == l ? a += JSON.stringify(o) + ":null" : L(l) || Y(l) || v2(l) ? a += JSON.stringify(o) + ":" + JSON.stringify(l) : l instanceof p ? a += JSON.stringify(o) + ":" + ie(l) : l instanceof t2 || l instanceof Array ? a += JSON.stringify(o) + ":" + ie(l, null, e3) : l instanceof c ? a += e3 ? JSON.stringify(o) + ":" + JSON.stringify(l.getTime()) : JSON.stringify(o) + ":" + l.stringify() : null !== l && "object" == typeof l && void 0 !== l.castToText && (a += JSON.stringify(o) + ":" + l.castToText(e3));
    }
    return '{"geometry":' + (null === this.geometry() ? "null" : ie(this.geometry())) + ',"attributes":{' + a + "}}";
  }
  _fixDates() {
    if (null !== this._datefields) return this._datefields.length > 0 && this._fixDateFields(this._datefields), void (this._datesfixed = true);
    const e3 = [], t3 = this._layer.fields;
    for (let i = 0; i < t3.length; i++) {
      const r2 = t3[i], s = r2.type;
      "date" !== s && "esriFieldTypeDate" !== s || e3.push(r2.name);
    }
    this._datefields = e3, e3.length > 0 && this._fixDateFields(e3), this._datesfixed = true;
  }
  isUnknownDateTimeField(e3) {
    var _a;
    return "unknown" === ((_a = this.dateTimeReferenceFieldIndex) == null ? void 0 : _a.fieldTimeZone(e3));
  }
  _fixDateFields(e3) {
    var _a;
    this.attributes = { ...this.attributes };
    const t3 = ((_a = this.contextTimeReference) == null ? void 0 : _a.timeZone) ?? "system";
    for (let i = 0; i < e3.length; i++) {
      let r2 = this.attributes[e3[i]];
      if (null === r2) ;
      else if (void 0 === r2) {
        for (const s in this.attributes) if (s.toLowerCase() === e3[i].toLowerCase()) {
          if (r2 = this.attributes[s], null !== r2) {
            const e4 = this.isUnknownDateTimeField(s);
            U(r2) ? this.attributes[s] = r2 : r2 instanceof Date ? this.attributes[s] = e4 ? c.unknownDateJSToArcadeDate(r2) : c.dateJSAndZoneToArcadeDate(r2, t3) : this.attributes[s] = e4 ? c.unknownEpochToArcadeDate(r2) : c.epochToArcadeDate(r2, t3);
          }
          break;
        }
      } else {
        const s = this.isUnknownDateTimeField(e3[i]);
        U(r2) ? this.attributes[e3[i]] = r2 : r2 instanceof Date ? this.attributes[e3[i]] = s ? c.unknownDateJSToArcadeDate(r2) : c.dateJSAndZoneToArcadeDate(r2, t3) : this.attributes[e3[i]] = s ? c.unknownEpochToArcadeDate(r2) : c.epochToArcadeDate(r2, t3);
      }
    }
  }
  geometry() {
    return null === this._geometry || this._geometry instanceof p || (this._optimizedGeomDefinition ? (this._geometry = e(v(ut(this._geometry, this._optimizedGeomDefinition.geometryType, this._optimizedGeomDefinition.hasZ, this._optimizedGeomDefinition.hasM))), this._geometry.spatialReference = this._optimizedGeomDefinition.spatialReference) : this._geometry = e(v(this._geometry))), this._geometry;
  }
  field(e3) {
    false === this._datesfixed && this._fixDates();
    const t3 = this.attributes[e3];
    if (void 0 !== t3) return t3;
    const i = e3.toLowerCase();
    for (const r2 in this.attributes) if (r2.toLowerCase() === i) return this.attributes[r2];
    if (this._hasFieldDefinition(i)) return null;
    throw new t(null, e2.FieldNotFound, null, { key: e3 });
  }
  _hasFieldDefinition(e3) {
    if (null === this._layer) return false;
    for (let t3 = 0; t3 < this._layer.fields.length; t3++) {
      if (this._layer.fields[t3].name.toLowerCase() === e3) return true;
    }
    return false;
  }
  setField(e3, t3) {
    if (this.immutable) throw new t(null, e2.Immutable, null);
    if (t3 instanceof Date && (t3 = this.isUnknownDateTimeField(e3) ? c.unknownDateJSToArcadeDate(t3) : c.dateJSToArcadeDate(t3)), false === O(t3)) throw new t(null, e2.TypeNotAllowedInFeature, null);
    const i = e3.toLowerCase();
    if (void 0 === this.attributes[e3]) {
      for (const e4 in this.attributes) if (e4.toLowerCase() === i) return void (this.attributes[e4] = t3);
      this.attributes[e3] = t3;
    } else this.attributes[e3] = t3;
  }
  hasField(e3) {
    const t3 = e3.toLowerCase();
    if (void 0 !== this.attributes[e3]) return true;
    for (const i in this.attributes) if (i.toLowerCase() === t3) return true;
    return !!this._hasFieldDefinition(t3);
  }
  keys() {
    let e3 = [];
    const t3 = {};
    for (const i in this.attributes) e3.push(i), t3[i.toLowerCase()] = 1;
    if (null !== this._layer) for (let i = 0; i < this._layer.fields.length; i++) {
      const r2 = this._layer.fields[i];
      1 !== t3[r2.name.toLowerCase()] && e3.push(r2.name);
    }
    return e3 = e3.sort(), e3;
  }
  static parseGeometryFromDictionary(e3) {
    const t3 = _g._convertDictionaryToJson(e3, true);
    return void 0 !== t3.hasm && (t3.hasM = t3.hasm, delete t3.hasm), void 0 !== t3.hasz && (t3.hasZ = t3.hasz, delete t3.hasz), void 0 !== t3.spatialreference && (t3.spatialReference = t3.spatialreference, delete t3.spatialreference), void 0 !== t3.rings && (t3.rings = this._fixPathArrays(t3.rings, true === t3.hasZ, true === t3.hasZ)), void 0 !== t3.paths && (t3.paths = this._fixPathArrays(t3.paths, true === t3.hasZ, true === t3.hasM)), void 0 !== t3.points && (t3.points = this._fixPointArrays(t3.points, true === t3.hasZ, true === t3.hasM)), v(t3);
  }
  static _fixPathArrays(e3, i, r2) {
    const s = [];
    if (e3 instanceof Array) for (let t3 = 0; t3 < e3.length; t3++) s.push(this._fixPointArrays(e3[t3], i, r2));
    else if (e3 instanceof t2) for (let t3 = 0; t3 < e3.length(); t3++) s.push(this._fixPointArrays(e3.get(t3), i, r2));
    return s;
  }
  static _fixPointArrays(e3, i, r2) {
    const s = [];
    if (e3 instanceof Array) for (let n = 0; n < e3.length; n++) {
      const a = e3[n];
      a instanceof w ? i && r2 ? s.push([a.x, a.y, a.z, a.m]) : i ? s.push([a.x, a.y, a.z]) : r2 ? s.push([a.x, a.y, a.m]) : s.push([a.x, a.y]) : a instanceof t2 ? s.push(a.toArray()) : s.push(a);
    }
    else if (e3 instanceof t2) for (let n = 0; n < e3.length(); n++) {
      const a = e3.get(n);
      a instanceof w ? i && r2 ? s.push([a.x, a.y, a.z, a.m]) : i ? s.push([a.x, a.y, a.z]) : r2 ? s.push([a.x, a.y, a.m]) : s.push([a.x, a.y]) : a instanceof t2 ? s.push(a.toArray()) : s.push(a);
    }
    return s;
  }
  static _convertDictionaryToJson(t3, i = false) {
    const r2 = {};
    for (const s in t3.attributes) {
      let n = t3.attributes[s];
      n instanceof T && (n = _g._convertDictionaryToJson(n)), i ? r2[s.toLowerCase()] = n : r2[s] = n;
    }
    return r2;
  }
  static parseAttributesFromDictionary(e3) {
    const t3 = {};
    for (const i in e3.attributes) {
      const r2 = e3.attributes[i];
      if (!O(r2)) throw new t(null, e2.InvalidParameter, null);
      t3[i] = r2;
    }
    return t3;
  }
  static fromJson(e3, t3) {
    let n = null;
    null !== e3.geometry && void 0 !== e3.geometry && (n = v(e3.geometry));
    const o = {};
    if (null !== e3.attributes && void 0 !== e3.attributes) for (const l in e3.attributes) {
      const t4 = e3.attributes[l];
      if (null === t4) o[l] = t4;
      else {
        if (!(v2(t4) || Y(t4) || L(t4) || U(t4))) throw new t(null, e2.InvalidParameter, null);
        o[l] = t4;
      }
    }
    return _g.createFromGraphicLikeObject(n, o, null, t3 ?? null);
  }
  fullSchema() {
    return this._layer;
  }
  gdbVersion() {
    if (null === this._layer) return "";
    const e3 = this._layer.gdbVersion;
    return void 0 === e3 ? "" : "" === e3 && this._layer.capabilities && this._layer.capabilities.isVersioned ? "SDE.DEFAULT" : e3;
  }
  castAsJson(e3) {
    var _a;
    const t3 = { attributes: {}, geometry: true === (e3 == null ? void 0 : e3.keepGeometryType) ? this.geometry() : ((_a = this.geometry()) == null ? void 0 : _a.toJSON()) ?? null };
    for (const i in this.attributes) {
      const r2 = this.attributes[i];
      void 0 !== r2 && (t3.attributes[i] = Re(r2, e3));
    }
    return t3;
  }
  async castAsJsonAsync(e3 = null, t3) {
    return this.castAsJson(t3);
  }
};

export {
  g
};
//# sourceMappingURL=chunk-HZJM3QX3.js.map
