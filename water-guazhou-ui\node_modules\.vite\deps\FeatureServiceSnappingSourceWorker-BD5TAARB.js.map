{"version": 3, "sources": ["../../@arcgis/core/core/AsyncSequence.js", "../../@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/PendingFeatureTile.js", "../../@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTiledFetcher.js", "../../@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTileCache.js", "../../@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTileStore.js", "../../@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceSnappingSourceWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as s}from\"../chunks/tslib.es6.js\";import t from\"./Accessor.js\";import{property as e}from\"./accessorSupport/decorators/property.js\";import{subclass as i}from\"./accessorSupport/decorators/subclass.js\";let r=class extends t{constructor(){super(...arguments),this.updating=!1,this._pending=[]}push(s,t){this._pending.push({promise:s,callback:t}),1===this._pending.length&&this._process()}_process(){if(!this._pending.length)return void(this.updating=!1);this.updating=!0;const s=this._pending[0];s.promise.then((t=>s.callback(t))).catch((()=>{})).then((()=>{this._pending.shift(),this._process()}))}};s([e()],r.prototype,\"updating\",void 0),r=s([i(\"esri.core.AsyncSequence\")],r);export{r as AsyncSequence};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{createTask as t,resultOrAbort as e}from\"../../../../../core/asyncUtils.js\";import{isNone as s}from\"../../../../../core/maybe.js\";import{create as a,fromExtent as r,intersects as E}from\"../../../../../geometry/support/aaBoundingRect.js\";class o{constructor(t,e){this.data=t,this.resolution=e,this.state={type:u.CREATED},this.alive=!0}process(t){switch(this.state.type){case u.CREATED:return this.state=this._gotoFetchCount(this.state,t),this.state.task.promise.then(t.resume,t.resume);case u.FETCH_COUNT:break;case u.FETCHED_COUNT:return this.state=this._gotoFetchFeatures(this.state,t),this.state.task.promise.then(t.resume,t.resume);case u.FETCH_FEATURES:break;case u.FETCHED_FEATURES:this.state=this._goToDone(this.state,t);case u.DONE:}return null}get debugInfo(){return{data:this.data,featureCount:this._featureCount,state:this._stateToString}}get _featureCount(){switch(this.state.type){case u.CREATED:case u.FETCH_COUNT:return 0;case u.FETCHED_COUNT:return this.state.featureCount;case u.FETCH_FEATURES:return this.state.previous.featureCount;case u.FETCHED_FEATURES:return this.state.features.length;case u.DONE:return this.state.previous.features.length}}get _stateToString(){switch(this.state.type){case u.CREATED:return\"created\";case u.FETCH_COUNT:return\"fetch-count\";case u.FETCHED_COUNT:return\"fetched-count\";case u.FETCH_FEATURES:return\"fetch-features\";case u.FETCHED_FEATURES:return\"fetched-features\";case u.DONE:return\"done\"}}_gotoFetchCount(s,a){return{type:u.FETCH_COUNT,previous:s,task:t((async t=>{const s=await e(a.fetchCount(this,t));this.state.type===u.FETCH_COUNT&&(this.state=this._gotoFetchedCount(this.state,s.ok?s.value:1/0))}))}}_gotoFetchedCount(t,e){return{type:u.FETCHED_COUNT,featureCount:e,previous:t}}_gotoFetchFeatures(s,a){return{type:u.FETCH_FEATURES,previous:s,task:t((async t=>{const r=await e(a.fetchFeatures(this,s.featureCount,t));this.state.type===u.FETCH_FEATURES&&(this.state=this._gotoFetchedFeatures(this.state,r.ok?r.value:[]))}))}}_gotoFetchedFeatures(t,e){return{type:u.FETCHED_FEATURES,previous:t,features:e}}_goToDone(t,e){return e.finish(this,t.features),{type:u.DONE,previous:t}}reset(){const t=this.state;switch(this.state={type:u.CREATED},t.type){case u.CREATED:case u.FETCHED_COUNT:case u.FETCHED_FEATURES:case u.DONE:break;case u.FETCH_COUNT:case u.FETCH_FEATURES:t.task.abort()}}intersects(t){return!(!s(t)&&this.data.extent)||(r(t,T),E(this.data.extent,T))}}var u;!function(t){t[t.CREATED=0]=\"CREATED\",t[t.FETCH_COUNT=1]=\"FETCH_COUNT\",t[t.FETCHED_COUNT=2]=\"FETCHED_COUNT\",t[t.FETCH_FEATURES=3]=\"FETCH_FEATURES\",t[t.FETCHED_FEATURES=4]=\"FETCHED_FEATURES\",t[t.DONE=5]=\"DONE\"}(u||(u={}));const T=a();export{o as PendingFeatureTile,u as StateType};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import{equals as t}from\"../../../../../core/arrayUtils.js\";import{AsyncSequence as i}from\"../../../../../core/AsyncSequence.js\";import{createTask as s}from\"../../../../../core/asyncUtils.js\";import{HandleOwner as r}from\"../../../../../core/HandleOwner.js\";import{makeHandle as o}from\"../../../../../core/handleUtils.js\";import n from\"../../../../../core/Logger.js\";import{isSome as a,isNone as l,unwrap as u}from\"../../../../../core/maybe.js\";import{throwIfAbortError as c}from\"../../../../../core/promiseUtils.js\";import{sync as d}from\"../../../../../core/reactiveUtils.js\";import{property as h}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import{subclass as p}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import f from\"../../../../../geometry/Extent.js\";import{toExtent as y}from\"../../../../../geometry/support/aaBoundingRect.js\";import{unquantizeOptimizedFeatureSet as g,convertFromFeatureSet as m}from\"../../../../../layers/graphics/featureConversionUtils.js\";import{isHostedAgolService as _}from\"../../../../../layers/support/arcgisLayerUrl.js\";import{OptimizedFeatureSetParserContext as b}from\"../../../../../rest/query/operations/pbfOptimizedFeatureSet.js\";import{executeQueryForExtent as F,executeQueryPBF as T,executeQuery as P}from\"../../../../../rest/query/operations/query.js\";import I from\"../../../../../rest/support/Query.js\";import{StateType as v,PendingFeatureTile as O}from\"./PendingFeatureTile.js\";let C=class extends r{get _minimumVerticesPerFeature(){switch(this.store?.featureStore.geometryType){case\"esriGeometryPoint\":case\"esriGeometryMultipoint\":return 1;case\"esriGeometryPolygon\":return 4;case\"esriGeometryPolyline\":return 2}}set filter(e){const t=this._get(\"filter\"),i=this._filterProperties(e);JSON.stringify(t)!==JSON.stringify(i)&&this._set(\"filter\",i)}set customParameters(e){const t=this._get(\"customParameters\");JSON.stringify(t)!==JSON.stringify(e)&&this._set(\"customParameters\",e)}get _configuration(){return{filter:this.filter,customParameters:this.customParameters,tileInfo:this.tileInfo,tileSize:this.tileSize}}set tileInfo(e){const t=this._get(\"tileInfo\");t!==e&&(a(e)&&a(t)&&JSON.stringify(e)===JSON.stringify(t)||(this._set(\"tileInfo\",e),this.store.tileInfo=e))}set tileSize(e){this._get(\"tileSize\")!==e&&this._set(\"tileSize\",e)}get updating(){return this.updatingExcludingEdits||this._pendingEdits.updating}get updatingExcludingEdits(){return this.updatingHandles.updating}get hasZ(){return this.store.featureStore.hasZ}constructor(e){super(e),this.tilesOfInterest=[],this.availability=0,this._pendingTiles=new Map,this._pendingEdits=new i,this._pendingEditsAbortController=new AbortController}initialize(){this._initializeFetchExtent(),this.updatingHandles.add((()=>this._configuration),(()=>this.refresh())),this.updatingHandles.add((()=>this.tilesOfInterest),((e,i)=>{t(e,i,(({id:e},{id:t})=>e===t))||this._process()}),d)}destroy(){this._pendingTiles.forEach((e=>this._deletePendingTile(e))),this._pendingTiles.clear(),this.store.destroy(),this.tilesOfInterest.length=0,this._pendingEditsAbortController.abort(),this._pendingEditsAbortController=null}refresh(){this.store.refresh(),this._pendingTiles.forEach((e=>this._deletePendingTile(e))),this._process()}applyEdits(e){this._pendingEdits.push(e,(async e=>{if(0===e.addedFeatures.length&&0===e.updatedFeatures.length&&0===e.deletedFeatures.length)return;for(const[,i]of this._pendingTiles)i.reset();const t={...e,deletedFeatures:e.deletedFeatures.map((({objectId:e,globalId:t})=>e&&-1!==e?e:this._lookupObjectIdByGlobalId(t)))};await this.updatingHandles.addPromise(this.store.processEdits(t,((e,t)=>this._queryFeaturesById(e,t)),this._pendingEditsAbortController.signal)),this._processPendingTiles()}))}_initializeFetchExtent(){if(!this.capabilities.query.supportsExtent||!_(this.url))return;const e=s((async e=>{try{const t=await F(this.url,new I({where:\"1=1\",outSpatialReference:this.spatialReference,cacheHint:!!this.capabilities.query.supportsCacheHint||void 0}),{query:this._configuration.customParameters,signal:e});this.store.extent=f.fromJSON(t.data?.extent)}catch(t){c(t),n.getLogger(this.declaredClass).warn(\"Failed to fetch data extent\",t)}}));this.updatingHandles.addPromise(e.promise.then((()=>this._process()))),this.handles.add(o((()=>e.abort())))}get debugInfo(){return{numberOfFeatures:this.store.featureStore.numFeatures,tilesOfInterest:this.tilesOfInterest,pendingTiles:Array.from(this._pendingTiles.values()).map((e=>e.debugInfo)),storedTiles:this.store.debugInfo}}_process(){this._markTilesNotAlive(),this._createPendingTiles(),this._deletePendingTiles(),this._processPendingTiles()}_markTilesNotAlive(){for(const[,e]of this._pendingTiles)e.alive=!1}_createPendingTiles(){const e=this._collectMissingTilesInfo();if(this._setAvailability(l(e)?1:e.coveredArea/e.fullArea),!l(e))for(const{data:t,resolution:i}of e.missingTiles){const e=this._pendingTiles.get(t.id);e?(e.resolution=i,e.alive=!0):this._createPendingTile(t,i)}}_collectMissingTilesInfo(){let e=null;for(let t=this.tilesOfInterest.length-1;t>=0;t--){const i=this.tilesOfInterest[t],s=this.store.process(i,((e,t)=>this._verifyTileComplexity(e,t)));l(e)?e=s:e.prepend(s)}return e}_deletePendingTiles(){for(const[,e]of this._pendingTiles)e.alive||this._deletePendingTile(e)}_processPendingTiles(){const e={fetchCount:(e,t)=>this._fetchCount(e,t),fetchFeatures:(e,t,i)=>this._fetchFeatures(e,t,i),finish:(e,t)=>this._finishPendingTile(e,t),resume:()=>this._processPendingTiles()};if(this._ensureFetchAllCounts(e))for(const[,t]of this._pendingTiles)this._verifyTileComplexity(this.store.getFeatureCount(t.data),t.resolution)&&this.updatingHandles.addPromise(t.process(e))}_verifyTileComplexity(e,t){return this._verifyVertexComplexity(e)&&this._verifyFeatureDensity(e,t)}_verifyVertexComplexity(e){return e*this._minimumVerticesPerFeature<x}_verifyFeatureDensity(e,t){if(l(this.tileInfo))return!1;const i=this.tileSize*t;return e*(j/(i*i))<w}_ensureFetchAllCounts(e){let t=!0;for(const[,i]of this._pendingTiles)i.state.type<v.FETCHED_COUNT&&this.updatingHandles.addPromise(i.process(e)),i.state.type<=v.FETCH_COUNT&&(t=!1);return t}_finishPendingTile(e,t){this.store.add(e.data,t),this._deletePendingTile(e),this._updateAvailability()}_updateAvailability(){const e=this._collectMissingTilesInfo();this._setAvailability(l(e)?1:e.coveredArea/e.fullArea)}_setAvailability(e){this._set(\"availability\",e)}_createPendingTile(e,t){const i=new O(e,t);return this._pendingTiles.set(e.id,i),i}_deletePendingTile(e){e.reset(),this._pendingTiles.delete(e.data.id)}async _fetchCount(e,t){return this.store.fetchCount(e.data,this.url,this._createCountQuery(e),{query:this.customParameters,timeout:S,signal:t})}async _fetchFeatures(e,t,i){let s=0;const r=[];let o=0,n=t;for(;;){const a=this._createFeaturesQuery(e),l=this._setPagingParameters(a,s,n),{features:c,exceededTransferLimit:d}=await this._queryFeatures(a,i);l&&(s+=u(a.num)),o+=c.length;for(const e of c)r.push(e);if(n=t-o,!l||!d||n<=0)return r}}_filterProperties(e){return l(e)?{where:\"1=1\",gdbVersion:void 0,timeExtent:void 0}:{where:e.where||\"1=1\",timeExtent:e.timeExtent,gdbVersion:e.gdbVersion}}_lookupObjectIdByGlobalId(e){const t=this.globalIdField,i=this.objectIdField;if(l(t))throw new Error(\"Expected globalIdField to be defined\");let s=null;if(this.store.featureStore.forEach((r=>{e===r.attributes[t]&&(s=r.objectId??r.attributes[i])})),l(s))throw new Error(`Expected to find a feature with globalId ${e}`);return s}_queryFeaturesById(e,t){const i=this._createFeaturesQuery();return i.objectIds=e,this._queryFeatures(i,t)}_queryFeatures(e,t){return this.capabilities.query.supportsFormatPBF?this._queryFeaturesPBF(e,t):this._queryFeaturesJSON(e,t)}async _queryFeaturesPBF(e,t){const{sourceSpatialReference:i}=this,{data:s}=await T(this.url,e,new b({sourceSpatialReference:i}),{query:this._configuration.customParameters,timeout:S,signal:t});return g(s)}async _queryFeaturesJSON(e,t){const{sourceSpatialReference:i}=this,{data:s}=await P(this.url,e,i,{query:this._configuration.customParameters,timeout:S,signal:t});return m(s,this.objectIdField)}_createCountQuery(e){const t=this._createBaseQuery(e);return this.capabilities.query.supportsCacheHint&&(t.cacheHint=!0),t}_createFeaturesQuery(e=null){const t=this._createBaseQuery(e);return t.outFields=this.globalIdField?[this.globalIdField,this.objectIdField]:[this.objectIdField],t.returnGeometry=!0,a(e)&&(this.capabilities.query.supportsResultType?t.resultType=\"tile\":this.capabilities.query.supportsCacheHint&&(t.cacheHint=!0)),t}_createBaseQuery(e){const t=new I({returnZ:this.hasZ,returnM:!1,geometry:a(this.tileInfo)&&a(e)?y(e.data.extent,this.tileInfo.spatialReference):void 0}),i=this._configuration.filter;return a(i)&&(t.where=i.where,t.gdbVersion=i.gdbVersion,t.timeExtent=i.timeExtent),t.outSpatialReference=this.spatialReference,t}_setPagingParameters(e,t,i){if(!this.capabilities.query.supportsPagination)return!1;const{supportsMaxRecordCountFactor:s,supportsCacheHint:r,tileMaxRecordCount:o,maxRecordCount:n,supportsResultType:a}=this.capabilities.query,l=s?I.MAX_MAX_RECORD_COUNT_FACTOR:1,u=l*((a||r)&&o?o:n||E);return e.start=t,s?(e.maxRecordCountFactor=Math.min(l,Math.ceil(i/u)),e.num=Math.min(i,e.maxRecordCountFactor*u)):e.num=Math.min(i,u),!0}};e([h({constructOnly:!0})],C.prototype,\"url\",void 0),e([h({constructOnly:!0})],C.prototype,\"objectIdField\",void 0),e([h({constructOnly:!0})],C.prototype,\"globalIdField\",void 0),e([h({constructOnly:!0})],C.prototype,\"capabilities\",void 0),e([h({constructOnly:!0})],C.prototype,\"sourceSpatialReference\",void 0),e([h({constructOnly:!0})],C.prototype,\"spatialReference\",void 0),e([h({constructOnly:!0})],C.prototype,\"store\",void 0),e([h({readOnly:!0})],C.prototype,\"_minimumVerticesPerFeature\",null),e([h()],C.prototype,\"filter\",null),e([h()],C.prototype,\"customParameters\",null),e([h({readOnly:!0})],C.prototype,\"_configuration\",null),e([h()],C.prototype,\"tileInfo\",null),e([h()],C.prototype,\"tileSize\",null),e([h()],C.prototype,\"tilesOfInterest\",void 0),e([h({readOnly:!0})],C.prototype,\"updating\",null),e([h({readOnly:!0})],C.prototype,\"updatingExcludingEdits\",null),e([h({readOnly:!0})],C.prototype,\"availability\",void 0),e([h()],C.prototype,\"hasZ\",null),C=e([p(\"esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiledFetcher\")],C);const E=2e3,S=6e5,x=1e6,j=25,w=1;export{C as FeatureServiceTiledFetcher};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(){this._store=new Map,this._byteSize=0}set(t,e){this.delete(t),this._store.set(t,e),this._byteSize+=e.byteSize}delete(t){const e=this._store.get(t);return!!this._store.delete(t)&&(null!=e&&(this._byteSize-=e.byteSize),!0)}get(t){return this._used(t),this._store.get(t)}has(t){return this._used(t),this._store.has(t)}clear(){this._store.clear()}applyByteSizeLimit(t,e){for(const[s,r]of this._store){if(this._byteSize<=t)break;this.delete(s),e(r)}}values(){return this._store.values()}[Symbol.iterator](){return this._store[Symbol.iterator]()}_used(t){const e=this._store.get(t);e&&(this._store.delete(t),this._store.set(t,e))}}export{t as FeatureServiceTileCache};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import t from\"../../../../../core/Accessor.js\";import{ByteSizeUnit as s,estimateAttributesObjectSize as i,estimateFixedArraySize as r}from\"../../../../../core/byteSizeEstimations.js\";import{isNone as o,isSome as n}from\"../../../../../core/maybe.js\";import{property as l}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as a}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{create as c,intersects as h,fromExtent as u,area as d}from\"../../../../../geometry/support/aaBoundingRect.js\";import{getBoundsOptimizedGeometry as f}from\"../../../../../layers/graphics/featureConversionUtils.js\";import{BoundsStore as _}from\"../../../../../layers/graphics/data/BoundsStore.js\";import p from\"../../../../../layers/support/TileInfo.js\";import{TileKey as m}from\"../../../../../layers/support/TileKey.js\";import{executeQueryForCount as g}from\"../../../../../rest/query/operations/query.js\";import{FeatureServiceTileCache as y}from\"./FeatureServiceTileCache.js\";let v=class extends t{constructor(e){super(e),this.tileInfo=null,this.extent=null,this.maximumByteSize=10*s.MEGABYTES,this._tileBounds=new _,this._tiles=new y,this._refCounts=new Map,this._tileFeatureCounts=new Map,this._tmpBoundingRect=c()}add(e,t){const s=[];for(const i of t)this._referenceFeature(i.objectId)===x.ADDED&&s.push(i);this._addTileStorage(e,new Set(t.map((e=>e.objectId))),C(t)),this.featureStore.addMany(s),this._tiles.applyByteSizeLimit(this.maximumByteSize,(e=>this._removeTileStorage(e)))}destroy(){this.clear(),this._tileFeatureCounts.clear()}clear(){this.featureStore.clear(),this._tileBounds.clear(),this._tiles.clear(),this._refCounts.clear()}refresh(){this.clear(),this._tileFeatureCounts.clear()}processEdits(e,t,s){return this._processEditsDelete(e.deletedFeatures.concat(e.updatedFeatures)),this._processEditsRefetch(e.addedFeatures.concat(e.updatedFeatures),t,s)}_addTileStorage(e,t,s){const i=e.id;this._tiles.set(i,new T(e,t,s)),this._tileBounds.set(i,e.extent),this._tileFeatureCounts.set(i,t.size)}_remove({id:e}){const t=this._tiles.get(e);t&&this._removeTileStorage(t)}_removeTileStorage(e){const t=[];for(const i of e.objectIds)this._unreferenceFeature(i)===x.REMOVED&&t.push(i);this.featureStore.removeManyById(t);const s=e.data.id;this._tiles.delete(s),this._tileBounds.delete(s)}_processEditsDelete(e){this.featureStore.removeManyById(e);for(const[,t]of this._tiles){for(const s of e)t.objectIds.delete(s);this._tileFeatureCounts.set(t.data.id,t.objectIds.size)}for(const t of e)this._refCounts.delete(t)}async _processEditsRefetch(e,t,s){const i=(await t(e,s)).features,{hasZ:r,hasM:n}=this.featureStore;for(const l of i){const e=f(this._tmpBoundingRect,l.geometry,r,n);o(e)||this._tileBounds.forEachInBounds(e,(e=>{const t=this._tiles.get(e);this.featureStore.add(l);const s=l.objectId;t.objectIds.has(s)||(t.objectIds.add(s),this._referenceFeature(s),this._tileFeatureCounts.set(t.data.id,t.objectIds.size))}))}}process(e,t=(()=>!0)){if(o(this.tileInfo)||!e.extent||n(this.extent)&&!h(u(this.extent,this._tmpBoundingRect),e.extent))return new I(e);if(this._tiles.has(e.id))return new I(e);const s=this._createTileTree(e,this.tileInfo);return this._simplify(s,t,null,0,1),this._collectMissingTiles(e,s,this.tileInfo)}get debugInfo(){return Array.from(this._tiles.values()).map((({data:e})=>({data:e,featureCount:this._tileFeatureCounts.get(e.id)||0})))}getFeatureCount(e){return this._tileFeatureCounts.get(e.id)??0}async fetchCount(e,t,s,i){const r=this._tileFeatureCounts.get(e.id);if(null!=r)return r;const o=await g(t,s,i);return this._tileFeatureCounts.set(e.id,o.data.count),o.data.count}_createTileTree(e,t){const s=new F(e.level,e.row,e.col);return t.updateTileInfo(s,p.ExtrapolateOptions.POWER_OF_TWO),this._tileBounds.forEachInBounds(e.extent,(i=>{const r=this._tiles.get(i)?.data;r&&this._tilesAreRelated(e,r)&&this._populateChildren(s,r,t,this._tileFeatureCounts.get(r.id)||0)})),s}_tilesAreRelated(e,t){if(!e||!t)return!1;if(e.level===t.level)return e.row===t.row&&e.col===t.col;const s=e.level<t.level,i=s?e:t,r=s?t:e,o=1<<r.level-i.level;return Math.floor(r.row/o)===i.row&&Math.floor(r.col/o)===i.col}_populateChildren(e,t,s,i){const r=t.level-e.level-1;if(r<0)return void(e.isLeaf=!0);const o=t.row>>r,l=t.col>>r,a=e.row<<1,c=l-(e.col<<1)+(o-a<<1),h=e.children[c];if(n(h))this._populateChildren(h,t,s,i);else{const r=new F(e.level+1,o,l);s.updateTileInfo(r,p.ExtrapolateOptions.POWER_OF_TWO),e.children[c]=r,this._populateChildren(r,t,s,i)}}_simplify(e,t,s,i,r){const o=r*r;if(e.isLeaf)return t(this.getFeatureCount(e),r)?0:(this._remove(e),n(s)&&(s.children[i]=null),o);const l=r/2,a=l*l;let c=0;for(let h=0;h<e.children.length;h++){const s=e.children[h];c+=n(s)?this._simplify(s,t,e,h,l):a}return 0===c?this._mergeChildren(e):1-c/o<w&&(this._purge(e),n(s)&&(s.children[i]=null),c=o),c}_mergeChildren(e){const t=new Set;let s=0;this._forEachLeaf(e,(e=>{const i=this._tiles.get(e.id);if(i){s+=i.byteSize;for(const e of i.objectIds)t.has(e)||(t.add(e),this._referenceFeature(e));this._remove(e)}})),this._addTileStorage(e,t,s),e.isLeaf=!0,e.children[0]=e.children[1]=e.children[2]=e.children[3]=null,this._tileFeatureCounts.set(e.id,t.size)}_forEachLeaf(e,t){for(const s of e.children)o(s)||(s.isLeaf?t(s):this._forEachLeaf(s,t))}_purge(e){if(!o(e))if(e.isLeaf)this._remove(e);else for(let t=0;t<e.children.length;t++){const s=e.children[t];this._purge(s),e.children[t]=null}}_collectMissingTiles(e,t,s){const i=new j(s,e,this.extent);return this._collectMissingTilesRecurse(t,i,1),i.info}_collectMissingTilesRecurse(e,t,s){if(e.isLeaf)return;if(!e.hasChildren)return void t.addMissing(e.level,e.row,e.col,s);const i=s/2;for(let r=0;r<e.children.length;r++){const s=e.children[r];o(s)?t.addMissing(e.level+1,(e.row<<1)+((2&r)>>1),(e.col<<1)+(1&r),i):this._collectMissingTilesRecurse(s,t,i)}}_referenceFeature(e){const t=(this._refCounts.get(e)||0)+1;return this._refCounts.set(e,t),1===t?x.ADDED:x.UNCHANGED}_unreferenceFeature(e){const t=(this._refCounts.get(e)||0)-1;return 0===t?(this._refCounts.delete(e),x.REMOVED):(t>0&&this._refCounts.set(e,t),x.UNCHANGED)}get test(){return{tiles:Array.from(this._tiles.values()).map((e=>`${e.data.id}:[${Array.from(e.objectIds)}]`)),featureReferences:Array.from(this._refCounts.keys()).map((e=>`${e}:${this._refCounts.get(e)}`))}}};function C(e){return e.reduce(((e,t)=>e+E(t)),0)}function E(e){return 32+S(e.geometry)+i(e.attributes)}function S(e){if(o(e))return 0;const t=r(e.lengths,4);return 32+r(e.coords,8)+t}e([l({constructOnly:!0})],v.prototype,\"featureStore\",void 0),e([l()],v.prototype,\"tileInfo\",void 0),e([l()],v.prototype,\"extent\",void 0),e([l()],v.prototype,\"maximumByteSize\",void 0),v=e([a(\"esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTileStore\")],v);class T{constructor(e,t,s){this.data=e,this.objectIds=t,this.byteSize=s}}class F{constructor(e,t,s){this.level=e,this.row=t,this.col=s,this.isLeaf=!1,this.extent=null,this.children=[null,null,null,null]}get hasChildren(){return!this.isLeaf&&(n(this.children[0])||n(this.children[1])||n(this.children[2])||n(this.children[3]))}}class I{constructor(e,t=[]){this.missingTiles=t,this.fullArea=0,this.coveredArea=0,this.fullArea=d(e.extent),this.coveredArea=this.fullArea}prepend(e){this.missingTiles=e.missingTiles.concat(this.missingTiles),this.coveredArea+=e.coveredArea,this.fullArea+=e.fullArea}}class j{constructor(e,t,s){this._tileInfo=e,this._extent=null,this.info=new I(t),n(s)&&(this._extent=u(s))}addMissing(e,t,s,i){const r=new m(null,e,t,s);this._tileInfo.updateTileInfo(r,p.ExtrapolateOptions.POWER_OF_TWO),o(r.extent)||n(this._extent)&&!h(this._extent,r.extent)||(this.info.missingTiles.push({data:r,resolution:i}),this.info.coveredArea-=d(r.extent))}}const w=.18751;var x;!function(e){e[e.ADDED=0]=\"ADDED\",e[e.REMOVED=1]=\"REMOVED\",e[e.UNCHANGED=2]=\"UNCHANGED\"}(x||(x={}));export{v as FeatureServiceTileStore,I as ProcessResult};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import t from\"../../../../../core/Evented.js\";import i from\"../../../../../core/Handles.js\";import{isSome as s}from\"../../../../../core/maybe.js\";import{createDeferred as r,throwIfAborted as n}from\"../../../../../core/promiseUtils.js\";import{watch as a,whenOnce as o,sync as l}from\"../../../../../core/reactiveUtils.js\";import{property as d}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as p}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{WatchUpdatingTracking as u}from\"../../../../../core/support/WatchUpdatingTracking.js\";import c from\"../../../../../geometry/SpatialReference.js\";import h from\"../../../../../layers/graphics/data/FeatureStore.js\";import{QueryEngine as f}from\"../../../../../layers/graphics/data/QueryEngine.js\";import g from\"../../../../../layers/support/TileInfo.js\";import m from\"../../../../../rest/support/Query.js\";import y from\"../../../../../symbols/support/ElevationInfo.js\";import{getSnappingCandidateElevationAligner as _}from\"../snappingCandidateElevationAlignment.js\";import{getSnappingCandidateElevationFilter as S}from\"../snappingCandidateElevationFilter.js\";import{getSymbologySnappingCandidatesFetcher as v}from\"../symbologySnappingCandidates.js\";import{FeatureServiceTiledFetcher as F}from\"./FeatureServiceTiledFetcher.js\";import{FeatureServiceTileStore as I}from\"./FeatureServiceTileStore.js\";let w=class extends t.EventedAccessor{constructor(){super(...arguments),this._isInitializing=!0,this.remoteClient=null,this._whenSetup=r(),this._elevationAligner=_(),this._elevationFilter=S(),this._symbologyCandidatesFetcher=v(),this._handles=new i,this._updatingHandles=new u,this._editsUpdatingHandles=new u,this._pendingApplyEdits=new Map,this._alignPointsInFeatures=async(e,t)=>{const i={points:e},s=await this.remoteClient.invoke(\"alignElevation\",i,{signal:t});return n(t),s},this._getSymbologyCandidates=async(e,t)=>{const i={candidates:e,spatialReference:this._spatialReference.toJSON()},s=await this.remoteClient.invoke(\"getSymbologyCandidates\",i,{signal:t});return n(t),s}}get updating(){return this.updatingExcludingEdits||this._editsUpdatingHandles.updating||this._featureFetcher.updating}get updatingExcludingEdits(){return this._featureFetcher.updatingExcludingEdits||this._isInitializing||this._updatingHandles.updating}destroy(){this._featureFetcher?.destroy(),this._queryEngine?.destroy(),this._featureStore?.clear(),this._handles?.destroy()}async setup(e){if(this.destroyed)return{result:{}};const{geometryType:t,objectIdField:i,timeInfo:r,fields:n}=e.serviceInfo,{hasZ:o}=e,d=c.fromJSON(e.spatialReference);this._spatialReference=d,this._featureStore=new h({...e.serviceInfo,hasZ:o,hasM:!1}),this._queryEngine=new f({spatialReference:e.spatialReference,featureStore:this._featureStore,geometryType:t,fields:n,hasZ:o,hasM:!1,objectIdField:i,timeInfo:r}),this._featureFetcher=new F({store:new I({featureStore:this._featureStore}),url:e.serviceInfo.url,objectIdField:e.serviceInfo.objectIdField,globalIdField:e.serviceInfo.globalIdField,capabilities:e.serviceInfo.capabilities,spatialReference:d,sourceSpatialReference:c.fromJSON(e.serviceInfo.spatialReference)});const p=\"3d\"===e.configuration.viewType;return this._elevationAligner=_(p,{elevationInfo:s(e.elevationInfo)?y.fromJSON(e.elevationInfo):null,alignPointsInFeatures:this._alignPointsInFeatures,spatialReference:d}),this._elevationFilter=S(p),this._handles.add([a((()=>this._featureFetcher.availability),(e=>this.emit(\"notify-availability\",{availability:e})),l),a((()=>this.updating),(()=>this._notifyUpdating()))]),this._whenSetup.resolve(),this._isInitializing=!1,this.configure(e.configuration)}async configure(e){return await this._updatingHandles.addPromise(this._whenSetup.promise),this._updateFeatureFetcherConfiguration(e),{result:{}}}async fetchCandidates(e,t){await this._whenSetup.promise,n(t);const i=E(e),r=s(t)?t.signal:null,a=await this._queryEngine.executeQueryForSnapping(i,r);n(r);const o=await this._elevationAligner.alignCandidates(a.candidates,r);n(r);const l=await this._symbologyCandidatesFetcher.fetch(o,r);n(r);const d=0===l.length?o:o.concat(l);return{result:{candidates:this._elevationFilter.filter(i,d)}}}async updateTiles(e,t){return await this._updatingHandles.addPromise(this._whenSetup.promise),n(t),this._featureFetcher.tileSize=e.tileSize,this._featureFetcher.tilesOfInterest=e.tiles,this._featureFetcher.tileInfo=s(e.tileInfo)?g.fromJSON(e.tileInfo):null,j}async refresh(e,t){return await this._updatingHandles.addPromise(this._whenSetup.promise),n(t),this._featureFetcher.refresh(),j}async whenNotUpdating(e,t){return await this._updatingHandles.addPromise(this._whenSetup.promise),n(t),await o((()=>!this.updatingExcludingEdits),t),n(t),j}async getDebugInfo(e,t){return n(t),{result:this._featureFetcher.debugInfo}}async beginApplyEdits(e,t){this._updatingHandles.addPromise(this._whenSetup.promise),n(t);const i=r();return this._pendingApplyEdits.set(e.id,i),this._featureFetcher.applyEdits(i.promise),this._editsUpdatingHandles.addPromise(i.promise),j}async endApplyEdits(e,t){const i=this._pendingApplyEdits.get(e.id);return i&&i.resolve(e.edits),n(t),j}async notifyElevationSourceChange(e,t){return this._elevationAligner.notifyElevationSourceChange(),j}async notifySymbologyChange(e,t){return this._symbologyCandidatesFetcher.notifySymbologyChange(),j}async setSymbologySnappingSupported(e){return this._symbologyCandidatesFetcher=v(e,this._getSymbologyCandidates),j}_updateFeatureFetcherConfiguration(e){this._featureFetcher.filter=s(e.filter)?m.fromJSON(e.filter):null,this._featureFetcher.customParameters=e.customParameters}_notifyUpdating(){this.emit(\"notify-updating\",{updating:this.updating})}};e([d({readOnly:!0})],w.prototype,\"updating\",null),e([d({readOnly:!0})],w.prototype,\"updatingExcludingEdits\",null),e([d()],w.prototype,\"_isInitializing\",void 0),w=e([p(\"esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceSnappingSourceWorker\")],w);const b=w;function E(e){return{point:e.point,mode:e.mode,distance:e.distance,types:e.types,query:s(e.filter)?e.filter:{where:\"1=1\"}}}const j={result:{}};export{b as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgN,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,WAAS,OAAG,KAAK,WAAS,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAEC,IAAE;AAAC,SAAK,SAAS,KAAK,EAAC,SAAQD,IAAE,UAASC,GAAC,CAAC,GAAE,MAAI,KAAK,SAAS,UAAQ,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,QAAG,CAAC,KAAK,SAAS,OAAO,QAAO,MAAK,KAAK,WAAS;AAAI,SAAK,WAAS;AAAG,UAAMD,KAAE,KAAK,SAAS,CAAC;AAAE,IAAAA,GAAE,QAAQ,KAAM,CAAAC,OAAGD,GAAE,SAASC,EAAC,CAAE,EAAE,MAAO,MAAI;AAAA,IAAC,CAAE,EAAE,KAAM,MAAI;AAAC,WAAK,SAAS,MAAM,GAAE,KAAK,SAAS;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,yBAAyB,CAAC,GAAEA,EAAC;;;ACAtb,IAAMG,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,OAAKD,IAAE,KAAK,aAAWC,IAAE,KAAK,QAAM,EAAC,MAAKC,GAAE,QAAO,GAAE,KAAK,QAAM;AAAA,EAAE;AAAA,EAAC,QAAQF,IAAE;AAAC,YAAO,KAAK,MAAM,MAAK;AAAA,MAAC,KAAKE,GAAE;AAAQ,eAAO,KAAK,QAAM,KAAK,gBAAgB,KAAK,OAAMF,EAAC,GAAE,KAAK,MAAM,KAAK,QAAQ,KAAKA,GAAE,QAAOA,GAAE,MAAM;AAAA,MAAE,KAAKE,GAAE;AAAY;AAAA,MAAM,KAAKA,GAAE;AAAc,eAAO,KAAK,QAAM,KAAK,mBAAmB,KAAK,OAAMF,EAAC,GAAE,KAAK,MAAM,KAAK,QAAQ,KAAKA,GAAE,QAAOA,GAAE,MAAM;AAAA,MAAE,KAAKE,GAAE;AAAe;AAAA,MAAM,KAAKA,GAAE;AAAiB,aAAK,QAAM,KAAK,UAAU,KAAK,OAAMF,EAAC;AAAA,MAAE,KAAKE,GAAE;AAAA,IAAK;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,IAAI,YAAW;AAAC,WAAM,EAAC,MAAK,KAAK,MAAK,cAAa,KAAK,eAAc,OAAM,KAAK,eAAc;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,YAAO,KAAK,MAAM,MAAK;AAAA,MAAC,KAAKA,GAAE;AAAA,MAAQ,KAAKA,GAAE;AAAY,eAAO;AAAA,MAAE,KAAKA,GAAE;AAAc,eAAO,KAAK,MAAM;AAAA,MAAa,KAAKA,GAAE;AAAe,eAAO,KAAK,MAAM,SAAS;AAAA,MAAa,KAAKA,GAAE;AAAiB,eAAO,KAAK,MAAM,SAAS;AAAA,MAAO,KAAKA,GAAE;AAAK,eAAO,KAAK,MAAM,SAAS,SAAS;AAAA,IAAM;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,YAAO,KAAK,MAAM,MAAK;AAAA,MAAC,KAAKA,GAAE;AAAQ,eAAM;AAAA,MAAU,KAAKA,GAAE;AAAY,eAAM;AAAA,MAAc,KAAKA,GAAE;AAAc,eAAM;AAAA,MAAgB,KAAKA,GAAE;AAAe,eAAM;AAAA,MAAiB,KAAKA,GAAE;AAAiB,eAAM;AAAA,MAAmB,KAAKA,GAAE;AAAK,eAAM;AAAA,IAAM;AAAA,EAAC;AAAA,EAAC,gBAAgBC,IAAEC,IAAE;AAAC,WAAM,EAAC,MAAKF,GAAE,aAAY,UAASC,IAAE,MAAKE,GAAG,OAAML,OAAG;AAAC,YAAMG,KAAE,MAAM,EAAEC,GAAE,WAAW,MAAKJ,EAAC,CAAC;AAAE,WAAK,MAAM,SAAOE,GAAE,gBAAc,KAAK,QAAM,KAAK,kBAAkB,KAAK,OAAMC,GAAE,KAAGA,GAAE,QAAM,IAAE,CAAC;AAAA,IAAE,CAAE,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAEC,IAAE;AAAC,WAAM,EAAC,MAAKC,GAAE,eAAc,cAAaD,IAAE,UAASD,GAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBG,IAAEC,IAAE;AAAC,WAAM,EAAC,MAAKF,GAAE,gBAAe,UAASC,IAAE,MAAKE,GAAG,OAAML,OAAG;AAAC,YAAMM,KAAE,MAAM,EAAEF,GAAE,cAAc,MAAKD,GAAE,cAAaH,EAAC,CAAC;AAAE,WAAK,MAAM,SAAOE,GAAE,mBAAiB,KAAK,QAAM,KAAK,qBAAqB,KAAK,OAAMI,GAAE,KAAGA,GAAE,QAAM,CAAC,CAAC;AAAA,IAAE,CAAE,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBN,IAAEC,IAAE;AAAC,WAAM,EAAC,MAAKC,GAAE,kBAAiB,UAASF,IAAE,UAASC,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUD,IAAEC,IAAE;AAAC,WAAOA,GAAE,OAAO,MAAKD,GAAE,QAAQ,GAAE,EAAC,MAAKE,GAAE,MAAK,UAASF,GAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,KAAK;AAAM,YAAO,KAAK,QAAM,EAAC,MAAKE,GAAE,QAAO,GAAEF,GAAE,MAAK;AAAA,MAAC,KAAKE,GAAE;AAAA,MAAQ,KAAKA,GAAE;AAAA,MAAc,KAAKA,GAAE;AAAA,MAAiB,KAAKA,GAAE;AAAK;AAAA,MAAM,KAAKA,GAAE;AAAA,MAAY,KAAKA,GAAE;AAAe,QAAAF,GAAE,KAAK,MAAM;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,WAAM,EAAE,CAAC,EAAEA,EAAC,KAAG,KAAK,KAAK,YAAU,EAAEA,IAAE,CAAC,GAAE,EAAE,KAAK,KAAK,QAAO,CAAC;AAAA,EAAE;AAAC;AAAC,IAAIE;AAAE,CAAC,SAASF,IAAE;AAAC,EAAAA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,cAAY,CAAC,IAAE,eAAcA,GAAEA,GAAE,gBAAc,CAAC,IAAE,iBAAgBA,GAAEA,GAAE,iBAAe,CAAC,IAAE,kBAAiBA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,OAAK,CAAC,IAAE;AAAM,EAAEE,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAM,IAAE,EAAE;;;ACAnkC,IAAI,IAAE,cAAcK,GAAC;AAAA,EAAC,IAAI,6BAA4B;AAJzlD;AAI0lD,aAAO,UAAK,UAAL,mBAAY,aAAa,cAAa;AAAA,MAAC,KAAI;AAAA,MAAoB,KAAI;AAAyB,eAAO;AAAA,MAAE,KAAI;AAAsB,eAAO;AAAA,MAAE,KAAI;AAAuB,eAAO;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAOC,IAAE;AAAC,UAAMC,KAAE,KAAK,KAAK,QAAQ,GAAEC,KAAE,KAAK,kBAAkBF,EAAC;AAAE,SAAK,UAAUC,EAAC,MAAI,KAAK,UAAUC,EAAC,KAAG,KAAK,KAAK,UAASA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAiBF,IAAE;AAAC,UAAMC,KAAE,KAAK,KAAK,kBAAkB;AAAE,SAAK,UAAUA,EAAC,MAAI,KAAK,UAAUD,EAAC,KAAG,KAAK,KAAK,oBAAmBA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAM,EAAC,QAAO,KAAK,QAAO,kBAAiB,KAAK,kBAAiB,UAAS,KAAK,UAAS,UAAS,KAAK,SAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,SAASA,IAAE;AAAC,UAAMC,KAAE,KAAK,KAAK,UAAU;AAAE,IAAAA,OAAID,OAAI,EAAEA,EAAC,KAAG,EAAEC,EAAC,KAAG,KAAK,UAAUD,EAAC,MAAI,KAAK,UAAUC,EAAC,MAAI,KAAK,KAAK,YAAWD,EAAC,GAAE,KAAK,MAAM,WAASA;AAAA,EAAG;AAAA,EAAC,IAAI,SAASA,IAAE;AAAC,SAAK,KAAK,UAAU,MAAIA,MAAG,KAAK,KAAK,YAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,0BAAwB,KAAK,cAAc;AAAA,EAAQ;AAAA,EAAC,IAAI,yBAAwB;AAAC,WAAO,KAAK,gBAAgB;AAAA,EAAQ;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,MAAM,aAAa;AAAA,EAAI;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,kBAAgB,CAAC,GAAE,KAAK,eAAa,GAAE,KAAK,gBAAc,oBAAI,OAAI,KAAK,gBAAc,IAAIG,MAAE,KAAK,+BAA6B,IAAI;AAAA,EAAe;AAAA,EAAC,aAAY;AAAC,SAAK,uBAAuB,GAAE,KAAK,gBAAgB,IAAK,MAAI,KAAK,gBAAiB,MAAI,KAAK,QAAQ,CAAE,GAAE,KAAK,gBAAgB,IAAK,MAAI,KAAK,iBAAkB,CAACH,IAAEE,OAAI;AAAC,QAAEF,IAAEE,IAAG,CAAC,EAAC,IAAGF,GAAC,GAAE,EAAC,IAAGC,GAAC,MAAID,OAAIC,EAAE,KAAG,KAAK,SAAS;AAAA,IAAC,GAAG,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc,QAAS,CAAAD,OAAG,KAAK,mBAAmBA,EAAC,CAAE,GAAE,KAAK,cAAc,MAAM,GAAE,KAAK,MAAM,QAAQ,GAAE,KAAK,gBAAgB,SAAO,GAAE,KAAK,6BAA6B,MAAM,GAAE,KAAK,+BAA6B;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM,QAAQ,GAAE,KAAK,cAAc,QAAS,CAAAA,OAAG,KAAK,mBAAmBA,EAAC,CAAE,GAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,SAAK,cAAc,KAAKA,IAAG,OAAMA,OAAG;AAAC,UAAG,MAAIA,GAAE,cAAc,UAAQ,MAAIA,GAAE,gBAAgB,UAAQ,MAAIA,GAAE,gBAAgB,OAAO;AAAO,iBAAS,CAAC,EAACE,EAAC,KAAI,KAAK,cAAc,CAAAA,GAAE,MAAM;AAAE,YAAMD,KAAE,EAAC,GAAGD,IAAE,iBAAgBA,GAAE,gBAAgB,IAAK,CAAC,EAAC,UAASA,IAAE,UAASC,GAAC,MAAID,MAAG,OAAKA,KAAEA,KAAE,KAAK,0BAA0BC,EAAC,CAAE,EAAC;AAAE,YAAM,KAAK,gBAAgB,WAAW,KAAK,MAAM,aAAaA,IAAG,CAACD,IAAEC,OAAI,KAAK,mBAAmBD,IAAEC,EAAC,GAAG,KAAK,6BAA6B,MAAM,CAAC,GAAE,KAAK,qBAAqB;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,QAAG,CAAC,KAAK,aAAa,MAAM,kBAAgB,CAACG,GAAE,KAAK,GAAG,EAAE;AAAO,UAAMJ,KAAEK,GAAG,OAAML,OAAG;AAJ53H;AAI63H,UAAG;AAAC,cAAMC,KAAE,MAAMK,GAAE,KAAK,KAAI,IAAI,EAAE,EAAC,OAAM,OAAM,qBAAoB,KAAK,kBAAiB,WAAU,CAAC,CAAC,KAAK,aAAa,MAAM,qBAAmB,OAAM,CAAC,GAAE,EAAC,OAAM,KAAK,eAAe,kBAAiB,QAAON,GAAC,CAAC;AAAE,aAAK,MAAM,SAAOO,GAAE,UAAS,KAAAN,GAAE,SAAF,mBAAQ,MAAM;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC,GAAE,EAAE,UAAU,KAAK,aAAa,EAAE,KAAK,+BAA8BA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAE,SAAK,gBAAgB,WAAWD,GAAE,QAAQ,KAAM,MAAI,KAAK,SAAS,CAAE,CAAC,GAAE,KAAK,QAAQ,IAAI,EAAG,MAAIA,GAAE,MAAM,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAM,EAAC,kBAAiB,KAAK,MAAM,aAAa,aAAY,iBAAgB,KAAK,iBAAgB,cAAa,MAAM,KAAK,KAAK,cAAc,OAAO,CAAC,EAAE,IAAK,CAAAA,OAAGA,GAAE,SAAU,GAAE,aAAY,KAAK,MAAM,UAAS;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,mBAAmB,GAAE,KAAK,oBAAoB,GAAE,KAAK,oBAAoB,GAAE,KAAK,qBAAqB;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,eAAS,CAAC,EAACA,EAAC,KAAI,KAAK,cAAc,CAAAA,GAAE,QAAM;AAAA,EAAE;AAAA,EAAC,sBAAqB;AAAC,UAAMA,KAAE,KAAK,yBAAyB;AAAE,QAAG,KAAK,iBAAiB,EAAEA,EAAC,IAAE,IAAEA,GAAE,cAAYA,GAAE,QAAQ,GAAE,CAAC,EAAEA,EAAC,EAAE,YAAS,EAAC,MAAKC,IAAE,YAAWC,GAAC,KAAIF,GAAE,cAAa;AAAC,YAAMA,KAAE,KAAK,cAAc,IAAIC,GAAE,EAAE;AAAE,MAAAD,MAAGA,GAAE,aAAWE,IAAEF,GAAE,QAAM,QAAI,KAAK,mBAAmBC,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,2BAA0B;AAAC,QAAIF,KAAE;AAAK,aAAQC,KAAE,KAAK,gBAAgB,SAAO,GAAEA,MAAG,GAAEA,MAAI;AAAC,YAAMC,KAAE,KAAK,gBAAgBD,EAAC,GAAEO,KAAE,KAAK,MAAM,QAAQN,IAAG,CAACF,IAAEC,OAAI,KAAK,sBAAsBD,IAAEC,EAAC,CAAE;AAAE,QAAED,EAAC,IAAEA,KAAEQ,KAAER,GAAE,QAAQQ,EAAC;AAAA,IAAC;AAAC,WAAOR;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,eAAS,CAAC,EAACA,EAAC,KAAI,KAAK,cAAc,CAAAA,GAAE,SAAO,KAAK,mBAAmBA,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,UAAMA,KAAE,EAAC,YAAW,CAACA,IAAEC,OAAI,KAAK,YAAYD,IAAEC,EAAC,GAAE,eAAc,CAACD,IAAEC,IAAEC,OAAI,KAAK,eAAeF,IAAEC,IAAEC,EAAC,GAAE,QAAO,CAACF,IAAEC,OAAI,KAAK,mBAAmBD,IAAEC,EAAC,GAAE,QAAO,MAAI,KAAK,qBAAqB,EAAC;AAAE,QAAG,KAAK,sBAAsBD,EAAC,EAAE,YAAS,CAAC,EAACC,EAAC,KAAI,KAAK,cAAc,MAAK,sBAAsB,KAAK,MAAM,gBAAgBA,GAAE,IAAI,GAAEA,GAAE,UAAU,KAAG,KAAK,gBAAgB,WAAWA,GAAE,QAAQD,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAEC,IAAE;AAAC,WAAO,KAAK,wBAAwBD,EAAC,KAAG,KAAK,sBAAsBA,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBD,IAAE;AAAC,WAAOA,KAAE,KAAK,6BAA2BM;AAAA,EAAC;AAAA,EAAC,sBAAsBN,IAAEC,IAAE;AAAC,QAAG,EAAE,KAAK,QAAQ,EAAE,QAAM;AAAG,UAAMC,KAAE,KAAK,WAASD;AAAE,WAAOD,MAAGK,MAAGH,KAAEA,OAAIK;AAAA,EAAC;AAAA,EAAC,sBAAsBP,IAAE;AAAC,QAAIC,KAAE;AAAG,eAAS,CAAC,EAACC,EAAC,KAAI,KAAK,cAAc,CAAAA,GAAE,MAAM,OAAKO,GAAE,iBAAe,KAAK,gBAAgB,WAAWP,GAAE,QAAQF,EAAC,CAAC,GAAEE,GAAE,MAAM,QAAMO,GAAE,gBAAcR,KAAE;AAAI,WAAOA;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAEC,IAAE;AAAC,SAAK,MAAM,IAAID,GAAE,MAAKC,EAAC,GAAE,KAAK,mBAAmBD,EAAC,GAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAMA,KAAE,KAAK,yBAAyB;AAAE,SAAK,iBAAiB,EAAEA,EAAC,IAAE,IAAEA,GAAE,cAAYA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,SAAK,KAAK,gBAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAIQ,GAAEV,IAAEC,EAAC;AAAE,WAAO,KAAK,cAAc,IAAID,GAAE,IAAGE,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,mBAAmBF,IAAE;AAAC,IAAAA,GAAE,MAAM,GAAE,KAAK,cAAc,OAAOA,GAAE,KAAK,EAAE;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYA,IAAEC,IAAE;AAAC,WAAO,KAAK,MAAM,WAAWD,GAAE,MAAK,KAAK,KAAI,KAAK,kBAAkBA,EAAC,GAAE,EAAC,OAAM,KAAK,kBAAiB,SAAQW,IAAE,QAAOV,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeD,IAAEC,IAAEC,IAAE;AAAC,QAAIM,KAAE;AAAE,UAAML,KAAE,CAAC;AAAE,QAAIO,KAAE,GAAEE,KAAEX;AAAE,eAAO;AAAC,YAAMY,KAAE,KAAK,qBAAqBb,EAAC,GAAEc,KAAE,KAAK,qBAAqBD,IAAEL,IAAEI,EAAC,GAAE,EAAC,UAASG,IAAE,uBAAsBhB,GAAC,IAAE,MAAM,KAAK,eAAec,IAAEX,EAAC;AAAE,MAAAY,OAAIN,MAAGR,GAAEa,GAAE,GAAG,IAAGH,MAAGK,GAAE;AAAO,iBAAUf,MAAKe,GAAE,CAAAZ,GAAE,KAAKH,EAAC;AAAE,UAAGY,KAAEX,KAAES,IAAE,CAACI,MAAG,CAACf,MAAGa,MAAG,EAAE,QAAOT;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAE;AAAC,WAAO,EAAEA,EAAC,IAAE,EAAC,OAAM,OAAM,YAAW,QAAO,YAAW,OAAM,IAAE,EAAC,OAAMA,GAAE,SAAO,OAAM,YAAWA,GAAE,YAAW,YAAWA,GAAE,WAAU;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAE;AAAC,UAAMC,KAAE,KAAK,eAAcC,KAAE,KAAK;AAAc,QAAG,EAAED,EAAC,EAAE,OAAM,IAAI,MAAM,sCAAsC;AAAE,QAAIO,KAAE;AAAK,QAAG,KAAK,MAAM,aAAa,QAAS,CAAAL,OAAG;AAAC,MAAAH,OAAIG,GAAE,WAAWF,EAAC,MAAIO,KAAEL,GAAE,YAAUA,GAAE,WAAWD,EAAC;AAAA,IAAE,CAAE,GAAE,EAAEM,EAAC,EAAE,OAAM,IAAI,MAAM,4CAA4CR,EAAC,EAAE;AAAE,WAAOQ;AAAA,EAAC;AAAA,EAAC,mBAAmBR,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,qBAAqB;AAAE,WAAOA,GAAE,YAAUF,IAAE,KAAK,eAAeE,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAEC,IAAE;AAAC,WAAO,KAAK,aAAa,MAAM,oBAAkB,KAAK,kBAAkBD,IAAEC,EAAC,IAAE,KAAK,mBAAmBD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAkBD,IAAEC,IAAE;AAAC,UAAK,EAAC,wBAAuBC,GAAC,IAAE,MAAK,EAAC,MAAKM,GAAC,IAAE,MAAMQ,GAAE,KAAK,KAAIhB,IAAE,IAAIa,GAAE,EAAC,wBAAuBX,GAAC,CAAC,GAAE,EAAC,OAAM,KAAK,eAAe,kBAAiB,SAAQS,IAAE,QAAOV,GAAC,CAAC;AAAE,WAAO,GAAEO,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBR,IAAEC,IAAE;AAAC,UAAK,EAAC,wBAAuBC,GAAC,IAAE,MAAK,EAAC,MAAKM,GAAC,IAAE,MAAMO,GAAE,KAAK,KAAIf,IAAEE,IAAE,EAAC,OAAM,KAAK,eAAe,kBAAiB,SAAQS,IAAE,QAAOV,GAAC,CAAC;AAAE,WAAO,GAAEO,IAAE,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,kBAAkBR,IAAE;AAAC,UAAMC,KAAE,KAAK,iBAAiBD,EAAC;AAAE,WAAO,KAAK,aAAa,MAAM,sBAAoBC,GAAE,YAAU,OAAIA;AAAA,EAAC;AAAA,EAAC,qBAAqBD,KAAE,MAAK;AAAC,UAAMC,KAAE,KAAK,iBAAiBD,EAAC;AAAE,WAAOC,GAAE,YAAU,KAAK,gBAAc,CAAC,KAAK,eAAc,KAAK,aAAa,IAAE,CAAC,KAAK,aAAa,GAAEA,GAAE,iBAAe,MAAG,EAAED,EAAC,MAAI,KAAK,aAAa,MAAM,qBAAmBC,GAAE,aAAW,SAAO,KAAK,aAAa,MAAM,sBAAoBA,GAAE,YAAU,QAAKA;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAE;AAAC,UAAMC,KAAE,IAAI,EAAE,EAAC,SAAQ,KAAK,MAAK,SAAQ,OAAG,UAAS,EAAE,KAAK,QAAQ,KAAG,EAAED,EAAC,IAAEgB,GAAEhB,GAAE,KAAK,QAAO,KAAK,SAAS,gBAAgB,IAAE,OAAM,CAAC,GAAEE,KAAE,KAAK,eAAe;AAAO,WAAO,EAAEA,EAAC,MAAID,GAAE,QAAMC,GAAE,OAAMD,GAAE,aAAWC,GAAE,YAAWD,GAAE,aAAWC,GAAE,aAAYD,GAAE,sBAAoB,KAAK,kBAAiBA;AAAA,EAAC;AAAA,EAAC,qBAAqBD,IAAEC,IAAEC,IAAE;AAAC,QAAG,CAAC,KAAK,aAAa,MAAM,mBAAmB,QAAM;AAAG,UAAK,EAAC,8BAA6BM,IAAE,mBAAkBL,IAAE,oBAAmBO,IAAE,gBAAeE,IAAE,oBAAmBC,GAAC,IAAE,KAAK,aAAa,OAAMC,KAAEN,KAAE,EAAE,8BAA4B,GAAEC,KAAEK,OAAID,MAAGV,OAAIO,KAAEA,KAAEE,MAAGK;AAAG,WAAOjB,GAAE,QAAMC,IAAEO,MAAGR,GAAE,uBAAqB,KAAK,IAAIc,IAAE,KAAK,KAAKZ,KAAEO,EAAC,CAAC,GAAET,GAAE,MAAI,KAAK,IAAIE,IAAEF,GAAE,uBAAqBS,EAAC,KAAGT,GAAE,MAAI,KAAK,IAAIE,IAAEO,EAAC,GAAE;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,8BAA6B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,0BAAyB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,gGAAgG,CAAC,GAAE,CAAC;AAAE,IAAMQ,KAAE;AAAR,IAAYN,KAAE;AAAd,IAAkBL,KAAE;AAApB,IAAwBD,KAAE;AAA1B,IAA6BE,KAAE;;;ACA/vU,IAAMW,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,SAAO,oBAAI,OAAI,KAAK,YAAU;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAEC,IAAE;AAAC,SAAK,OAAOD,EAAC,GAAE,KAAK,OAAO,IAAIA,IAAEC,EAAC,GAAE,KAAK,aAAWA,GAAE;AAAA,EAAQ;AAAA,EAAC,OAAOD,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAO,IAAID,EAAC;AAAE,WAAM,CAAC,CAAC,KAAK,OAAO,OAAOA,EAAC,MAAI,QAAMC,OAAI,KAAK,aAAWA,GAAE,WAAU;AAAA,EAAG;AAAA,EAAC,IAAID,IAAE;AAAC,WAAO,KAAK,MAAMA,EAAC,GAAE,KAAK,OAAO,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,MAAMA,EAAC,GAAE,KAAK,OAAO,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,OAAO,MAAM;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEC,IAAE;AAAC,eAAS,CAACC,IAAEC,EAAC,KAAI,KAAK,QAAO;AAAC,UAAG,KAAK,aAAWH,GAAE;AAAM,WAAK,OAAOE,EAAC,GAAED,GAAEE,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK,OAAO,OAAO;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,KAAK,OAAO,OAAO,QAAQ,EAAE;AAAA,EAAC;AAAA,EAAC,MAAMH,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAO,IAAID,EAAC;AAAE,IAAAC,OAAI,KAAK,OAAO,OAAOD,EAAC,GAAE,KAAK,OAAO,IAAIA,IAAEC,EAAC;AAAA,EAAE;AAAC;;;ACA+f,IAAIG,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,MAAK,KAAK,SAAO,MAAK,KAAK,kBAAgB,KAAGC,GAAE,WAAU,KAAK,cAAY,IAAI,KAAE,KAAK,SAAO,IAAIC,MAAE,KAAK,aAAW,oBAAI,OAAI,KAAK,qBAAmB,oBAAI,OAAI,KAAK,mBAAiB,EAAE;AAAA,EAAC;AAAA,EAAC,IAAIF,IAAEE,IAAE;AAAC,UAAMD,KAAE,CAAC;AAAE,eAAUE,MAAKD,GAAE,MAAK,kBAAkBC,GAAE,QAAQ,MAAIC,GAAE,SAAOH,GAAE,KAAKE,EAAC;AAAE,SAAK,gBAAgBH,IAAE,IAAI,IAAIE,GAAE,IAAK,CAAAF,OAAGA,GAAE,QAAS,CAAC,GAAEK,GAAEH,EAAC,CAAC,GAAE,KAAK,aAAa,QAAQD,EAAC,GAAE,KAAK,OAAO,mBAAmB,KAAK,iBAAiB,CAAAD,OAAG,KAAK,mBAAmBA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM,GAAE,KAAK,mBAAmB,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,aAAa,MAAM,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,OAAO,MAAM,GAAE,KAAK,WAAW,MAAM;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM,GAAE,KAAK,mBAAmB,MAAM;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEE,IAAED,IAAE;AAAC,WAAO,KAAK,oBAAoBD,GAAE,gBAAgB,OAAOA,GAAE,eAAe,CAAC,GAAE,KAAK,qBAAqBA,GAAE,cAAc,OAAOA,GAAE,eAAe,GAAEE,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBD,IAAEE,IAAED,IAAE;AAAC,UAAME,KAAEH,GAAE;AAAG,SAAK,OAAO,IAAIG,IAAE,IAAIG,GAAEN,IAAEE,IAAED,EAAC,CAAC,GAAE,KAAK,YAAY,IAAIE,IAAEH,GAAE,MAAM,GAAE,KAAK,mBAAmB,IAAIG,IAAED,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,QAAQ,EAAC,IAAGF,GAAC,GAAE;AAAC,UAAME,KAAE,KAAK,OAAO,IAAIF,EAAC;AAAE,IAAAE,MAAG,KAAK,mBAAmBA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBF,IAAE;AAAC,UAAME,KAAE,CAAC;AAAE,eAAUC,MAAKH,GAAE,UAAU,MAAK,oBAAoBG,EAAC,MAAIC,GAAE,WAASF,GAAE,KAAKC,EAAC;AAAE,SAAK,aAAa,eAAeD,EAAC;AAAE,UAAMD,KAAED,GAAE,KAAK;AAAG,SAAK,OAAO,OAAOC,EAAC,GAAE,KAAK,YAAY,OAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAE;AAAC,SAAK,aAAa,eAAeA,EAAC;AAAE,eAAS,CAAC,EAACE,EAAC,KAAI,KAAK,QAAO;AAAC,iBAAUD,MAAKD,GAAE,CAAAE,GAAE,UAAU,OAAOD,EAAC;AAAE,WAAK,mBAAmB,IAAIC,GAAE,KAAK,IAAGA,GAAE,UAAU,IAAI;AAAA,IAAC;AAAC,eAAUA,MAAKF,GAAE,MAAK,WAAW,OAAOE,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBF,IAAEE,IAAED,IAAE;AAAC,UAAME,MAAG,MAAMD,GAAEF,IAAEC,EAAC,GAAG,UAAS,EAAC,MAAKM,IAAE,MAAKC,GAAC,IAAE,KAAK;AAAa,eAAUC,MAAKN,IAAE;AAAC,YAAMH,KAAE,GAAE,KAAK,kBAAiBS,GAAE,UAASF,IAAEC,EAAC;AAAE,QAAER,EAAC,KAAG,KAAK,YAAY,gBAAgBA,IAAG,CAAAA,OAAG;AAAC,cAAME,KAAE,KAAK,OAAO,IAAIF,EAAC;AAAE,aAAK,aAAa,IAAIS,EAAC;AAAE,cAAMR,KAAEQ,GAAE;AAAS,QAAAP,GAAE,UAAU,IAAID,EAAC,MAAIC,GAAE,UAAU,IAAID,EAAC,GAAE,KAAK,kBAAkBA,EAAC,GAAE,KAAK,mBAAmB,IAAIC,GAAE,KAAK,IAAGA,GAAE,UAAU,IAAI;AAAA,MAAE,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAQF,IAAEE,KAAG,MAAI,MAAI;AAAC,QAAG,EAAE,KAAK,QAAQ,KAAG,CAACF,GAAE,UAAQ,EAAE,KAAK,MAAM,KAAG,CAAC,EAAE,EAAE,KAAK,QAAO,KAAK,gBAAgB,GAAEA,GAAE,MAAM,EAAE,QAAO,IAAI,EAAEA,EAAC;AAAE,QAAG,KAAK,OAAO,IAAIA,GAAE,EAAE,EAAE,QAAO,IAAI,EAAEA,EAAC;AAAE,UAAMC,KAAE,KAAK,gBAAgBD,IAAE,KAAK,QAAQ;AAAE,WAAO,KAAK,UAAUC,IAAEC,IAAE,MAAK,GAAE,CAAC,GAAE,KAAK,qBAAqBF,IAAEC,IAAE,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,IAAK,CAAC,EAAC,MAAKD,GAAC,OAAK,EAAC,MAAKA,IAAE,cAAa,KAAK,mBAAmB,IAAIA,GAAE,EAAE,KAAG,EAAC,EAAG;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,WAAO,KAAK,mBAAmB,IAAIA,GAAE,EAAE,KAAG;AAAA,EAAC;AAAA,EAAC,MAAM,WAAWA,IAAEE,IAAED,IAAEE,IAAE;AAAC,UAAMI,KAAE,KAAK,mBAAmB,IAAIP,GAAE,EAAE;AAAE,QAAG,QAAMO,GAAE,QAAOA;AAAE,UAAMG,KAAE,MAAM,EAAER,IAAED,IAAEE,EAAC;AAAE,WAAO,KAAK,mBAAmB,IAAIH,GAAE,IAAGU,GAAE,KAAK,KAAK,GAAEA,GAAE,KAAK;AAAA,EAAK;AAAA,EAAC,gBAAgBV,IAAEE,IAAE;AAAC,UAAMD,KAAE,IAAI,EAAED,GAAE,OAAMA,GAAE,KAAIA,GAAE,GAAG;AAAE,WAAOE,GAAE,eAAeD,IAAEU,GAAE,mBAAmB,YAAY,GAAE,KAAK,YAAY,gBAAgBX,GAAE,QAAQ,CAAAG,OAAG;AAJl3H;AAIm3H,YAAMI,MAAE,UAAK,OAAO,IAAIJ,EAAC,MAAjB,mBAAoB;AAAK,MAAAI,MAAG,KAAK,iBAAiBP,IAAEO,EAAC,KAAG,KAAK,kBAAkBN,IAAEM,IAAEL,IAAE,KAAK,mBAAmB,IAAIK,GAAE,EAAE,KAAG,CAAC;AAAA,IAAC,CAAE,GAAEN;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAEE,IAAE;AAAC,QAAG,CAACF,MAAG,CAACE,GAAE,QAAM;AAAG,QAAGF,GAAE,UAAQE,GAAE,MAAM,QAAOF,GAAE,QAAME,GAAE,OAAKF,GAAE,QAAME,GAAE;AAAI,UAAMD,KAAED,GAAE,QAAME,GAAE,OAAMC,KAAEF,KAAED,KAAEE,IAAEK,KAAEN,KAAEC,KAAEF,IAAEU,KAAE,KAAGH,GAAE,QAAMJ,GAAE;AAAM,WAAO,KAAK,MAAMI,GAAE,MAAIG,EAAC,MAAIP,GAAE,OAAK,KAAK,MAAMI,GAAE,MAAIG,EAAC,MAAIP,GAAE;AAAA,EAAG;AAAA,EAAC,kBAAkBH,IAAEE,IAAED,IAAEE,IAAE;AAAC,UAAMI,KAAEL,GAAE,QAAMF,GAAE,QAAM;AAAE,QAAGO,KAAE,EAAE,QAAO,MAAKP,GAAE,SAAO;AAAI,UAAMU,KAAER,GAAE,OAAKK,IAAEE,KAAEP,GAAE,OAAKK,IAAEK,KAAEZ,GAAE,OAAK,GAAEa,KAAEJ,MAAGT,GAAE,OAAK,MAAIU,KAAEE,MAAG,IAAG,IAAEZ,GAAE,SAASa,EAAC;AAAE,QAAG,EAAE,CAAC,EAAE,MAAK,kBAAkB,GAAEX,IAAED,IAAEE,EAAC;AAAA,SAAM;AAAC,YAAMI,KAAE,IAAI,EAAEP,GAAE,QAAM,GAAEU,IAAED,EAAC;AAAE,MAAAR,GAAE,eAAeM,IAAEI,GAAE,mBAAmB,YAAY,GAAEX,GAAE,SAASa,EAAC,IAAEN,IAAE,KAAK,kBAAkBA,IAAEL,IAAED,IAAEE,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAUH,IAAEE,IAAED,IAAEE,IAAEI,IAAE;AAAC,UAAMG,KAAEH,KAAEA;AAAE,QAAGP,GAAE,OAAO,QAAOE,GAAE,KAAK,gBAAgBF,EAAC,GAAEO,EAAC,IAAE,KAAG,KAAK,QAAQP,EAAC,GAAE,EAAEC,EAAC,MAAIA,GAAE,SAASE,EAAC,IAAE,OAAMO;AAAG,UAAMD,KAAEF,KAAE,GAAEK,KAAEH,KAAEA;AAAE,QAAII,KAAE;AAAE,aAAQ,IAAE,GAAE,IAAEb,GAAE,SAAS,QAAO,KAAI;AAAC,YAAMC,KAAED,GAAE,SAAS,CAAC;AAAE,MAAAa,MAAG,EAAEZ,EAAC,IAAE,KAAK,UAAUA,IAAEC,IAAEF,IAAE,GAAES,EAAC,IAAEG;AAAA,IAAC;AAAC,WAAO,MAAIC,KAAE,KAAK,eAAeb,EAAC,IAAE,IAAEa,KAAEH,KAAEI,OAAI,KAAK,OAAOd,EAAC,GAAE,EAAEC,EAAC,MAAIA,GAAE,SAASE,EAAC,IAAE,OAAMU,KAAEH,KAAGG;AAAA,EAAC;AAAA,EAAC,eAAeb,IAAE;AAAC,UAAME,KAAE,oBAAI;AAAI,QAAID,KAAE;AAAE,SAAK,aAAaD,IAAG,CAAAA,OAAG;AAAC,YAAMG,KAAE,KAAK,OAAO,IAAIH,GAAE,EAAE;AAAE,UAAGG,IAAE;AAAC,QAAAF,MAAGE,GAAE;AAAS,mBAAUH,MAAKG,GAAE,UAAU,CAAAD,GAAE,IAAIF,EAAC,MAAIE,GAAE,IAAIF,EAAC,GAAE,KAAK,kBAAkBA,EAAC;AAAG,aAAK,QAAQA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,KAAK,gBAAgBA,IAAEE,IAAED,EAAC,GAAED,GAAE,SAAO,MAAGA,GAAE,SAAS,CAAC,IAAEA,GAAE,SAAS,CAAC,IAAEA,GAAE,SAAS,CAAC,IAAEA,GAAE,SAAS,CAAC,IAAE,MAAK,KAAK,mBAAmB,IAAIA,GAAE,IAAGE,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,aAAaF,IAAEE,IAAE;AAAC,eAAUD,MAAKD,GAAE,SAAS,GAAEC,EAAC,MAAIA,GAAE,SAAOC,GAAED,EAAC,IAAE,KAAK,aAAaA,IAAEC,EAAC;AAAA,EAAE;AAAA,EAAC,OAAOF,IAAE;AAAC,QAAG,CAAC,EAAEA,EAAC,EAAE,KAAGA,GAAE,OAAO,MAAK,QAAQA,EAAC;AAAA,QAAO,UAAQE,KAAE,GAAEA,KAAEF,GAAE,SAAS,QAAOE,MAAI;AAAC,YAAMD,KAAED,GAAE,SAASE,EAAC;AAAE,WAAK,OAAOD,EAAC,GAAED,GAAE,SAASE,EAAC,IAAE;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,qBAAqBF,IAAEE,IAAED,IAAE;AAAC,UAAME,KAAE,IAAIQ,GAAEV,IAAED,IAAE,KAAK,MAAM;AAAE,WAAO,KAAK,4BAA4BE,IAAEC,IAAE,CAAC,GAAEA,GAAE;AAAA,EAAI;AAAA,EAAC,4BAA4BH,IAAEE,IAAED,IAAE;AAAC,QAAGD,GAAE,OAAO;AAAO,QAAG,CAACA,GAAE,YAAY,QAAO,KAAKE,GAAE,WAAWF,GAAE,OAAMA,GAAE,KAAIA,GAAE,KAAIC,EAAC;AAAE,UAAME,KAAEF,KAAE;AAAE,aAAQM,KAAE,GAAEA,KAAEP,GAAE,SAAS,QAAOO,MAAI;AAAC,YAAMN,KAAED,GAAE,SAASO,EAAC;AAAE,QAAEN,EAAC,IAAEC,GAAE,WAAWF,GAAE,QAAM,IAAGA,GAAE,OAAK,OAAK,IAAEO,OAAI,KAAIP,GAAE,OAAK,MAAI,IAAEO,KAAGJ,EAAC,IAAE,KAAK,4BAA4BF,IAAEC,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAE;AAAC,UAAME,MAAG,KAAK,WAAW,IAAIF,EAAC,KAAG,KAAG;AAAE,WAAO,KAAK,WAAW,IAAIA,IAAEE,EAAC,GAAE,MAAIA,KAAEE,GAAE,QAAMA,GAAE;AAAA,EAAS;AAAA,EAAC,oBAAoBJ,IAAE;AAAC,UAAME,MAAG,KAAK,WAAW,IAAIF,EAAC,KAAG,KAAG;AAAE,WAAO,MAAIE,MAAG,KAAK,WAAW,OAAOF,EAAC,GAAEI,GAAE,YAAUF,KAAE,KAAG,KAAK,WAAW,IAAIF,IAAEE,EAAC,GAAEE,GAAE;AAAA,EAAU;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM,EAAC,OAAM,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,EAAE,IAAK,CAAAJ,OAAG,GAAGA,GAAE,KAAK,EAAE,KAAK,MAAM,KAAKA,GAAE,SAAS,CAAC,GAAI,GAAE,mBAAkB,MAAM,KAAK,KAAK,WAAW,KAAK,CAAC,EAAE,IAAK,CAAAA,OAAG,GAAGA,EAAC,IAAI,KAAK,WAAW,IAAIA,EAAC,CAAC,EAAG,EAAC;AAAA,EAAC;AAAC;AAAE,SAASK,GAAEL,IAAE;AAAC,SAAOA,GAAE,OAAQ,CAACA,IAAEE,OAAIF,KAAEe,GAAEb,EAAC,GAAG,CAAC;AAAC;AAAC,SAASa,GAAEf,IAAE;AAAC,SAAO,KAAGgB,GAAEhB,GAAE,QAAQ,IAAEE,GAAEF,GAAE,UAAU;AAAC;AAAC,SAASgB,GAAEhB,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAO;AAAE,QAAME,KAAEW,GAAEb,GAAE,SAAQ,CAAC;AAAE,SAAO,KAAGa,GAAEb,GAAE,QAAO,CAAC,IAAEE;AAAC;AAAC,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEH,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,6FAA6F,CAAC,GAAEA,EAAC;AAAE,IAAMO,KAAN,MAAO;AAAA,EAAC,YAAYN,IAAEE,IAAED,IAAE;AAAC,SAAK,OAAKD,IAAE,KAAK,YAAUE,IAAE,KAAK,WAASD;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEE,IAAED,IAAE;AAAC,SAAK,QAAMD,IAAE,KAAK,MAAIE,IAAE,KAAK,MAAID,IAAE,KAAK,SAAO,OAAG,KAAK,SAAO,MAAK,KAAK,WAAS,CAAC,MAAK,MAAK,MAAK,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAM,CAAC,KAAK,WAAS,EAAE,KAAK,SAAS,CAAC,CAAC,KAAG,EAAE,KAAK,SAAS,CAAC,CAAC,KAAG,EAAE,KAAK,SAAS,CAAC,CAAC,KAAG,EAAE,KAAK,SAAS,CAAC,CAAC;AAAA,EAAE;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEE,KAAE,CAAC,GAAE;AAAC,SAAK,eAAaA,IAAE,KAAK,WAAS,GAAE,KAAK,cAAY,GAAE,KAAK,WAASe,GAAEjB,GAAE,MAAM,GAAE,KAAK,cAAY,KAAK;AAAA,EAAQ;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,eAAaA,GAAE,aAAa,OAAO,KAAK,YAAY,GAAE,KAAK,eAAaA,GAAE,aAAY,KAAK,YAAUA,GAAE;AAAA,EAAQ;AAAC;AAAC,IAAMW,KAAN,MAAO;AAAA,EAAC,YAAYX,IAAEE,IAAED,IAAE;AAAC,SAAK,YAAUD,IAAE,KAAK,UAAQ,MAAK,KAAK,OAAK,IAAI,EAAEE,EAAC,GAAE,EAAED,EAAC,MAAI,KAAK,UAAQ,EAAEA,EAAC;AAAA,EAAE;AAAA,EAAC,WAAWD,IAAEE,IAAED,IAAEE,IAAE;AAAC,UAAMI,KAAE,IAAIL,GAAE,MAAKF,IAAEE,IAAED,EAAC;AAAE,SAAK,UAAU,eAAeM,IAAEI,GAAE,mBAAmB,YAAY,GAAE,EAAEJ,GAAE,MAAM,KAAG,EAAE,KAAK,OAAO,KAAG,CAAC,EAAE,KAAK,SAAQA,GAAE,MAAM,MAAI,KAAK,KAAK,aAAa,KAAK,EAAC,MAAKA,IAAE,YAAWJ,GAAC,CAAC,GAAE,KAAK,KAAK,eAAac,GAAEV,GAAE,MAAM;AAAA,EAAE;AAAC;AAAC,IAAMO,KAAE;AAAO,IAAIV;AAAE,CAAC,SAASJ,IAAE;AAAC,EAAAA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,YAAU,CAAC,IAAE;AAAW,EAAEI,OAAIA,KAAE,CAAC,EAAE;;;ACA71M,IAAIc,KAAE,cAAcC,GAAE,gBAAe;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,kBAAgB,MAAG,KAAK,eAAa,MAAK,KAAK,aAAW,EAAE,GAAE,KAAK,oBAAkBC,GAAE,GAAE,KAAK,mBAAiBA,GAAE,GAAE,KAAK,8BAA4BD,GAAE,GAAE,KAAK,WAAS,IAAIE,MAAE,KAAK,mBAAiB,IAAIC,MAAE,KAAK,wBAAsB,IAAIA,MAAE,KAAK,qBAAmB,oBAAI,OAAI,KAAK,yBAAuB,OAAMC,IAAEF,OAAI;AAAC,YAAMG,KAAE,EAAC,QAAOD,GAAC,GAAEE,KAAE,MAAM,KAAK,aAAa,OAAO,kBAAiBD,IAAE,EAAC,QAAOH,GAAC,CAAC;AAAE,aAAO,EAAEA,EAAC,GAAEI;AAAA,IAAC,GAAE,KAAK,0BAAwB,OAAMF,IAAEF,OAAI;AAAC,YAAMG,KAAE,EAAC,YAAWD,IAAE,kBAAiB,KAAK,kBAAkB,OAAO,EAAC,GAAEE,KAAE,MAAM,KAAK,aAAa,OAAO,0BAAyBD,IAAE,EAAC,QAAOH,GAAC,CAAC;AAAE,aAAO,EAAEA,EAAC,GAAEI;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,0BAAwB,KAAK,sBAAsB,YAAU,KAAK,gBAAgB;AAAA,EAAQ;AAAA,EAAC,IAAI,yBAAwB;AAAC,WAAO,KAAK,gBAAgB,0BAAwB,KAAK,mBAAiB,KAAK,iBAAiB;AAAA,EAAQ;AAAA,EAAC,UAAS;AAJ17E;AAI27E,eAAK,oBAAL,mBAAsB,YAAU,UAAK,iBAAL,mBAAmB,YAAU,UAAK,kBAAL,mBAAoB,UAAQ,UAAK,aAAL,mBAAe;AAAA,EAAS;AAAA,EAAC,MAAM,MAAMF,IAAE;AAAC,QAAG,KAAK,UAAU,QAAM,EAAC,QAAO,CAAC,EAAC;AAAE,UAAK,EAAC,cAAaF,IAAE,eAAcG,IAAE,UAASJ,IAAE,QAAOD,GAAC,IAAEI,GAAE,aAAY,EAAC,MAAKG,GAAC,IAAEH,IAAEI,KAAEC,GAAE,SAASL,GAAE,gBAAgB;AAAE,SAAK,oBAAkBI,IAAE,KAAK,gBAAc,IAAI,EAAE,EAAC,GAAGJ,GAAE,aAAY,MAAKG,IAAE,MAAK,MAAE,CAAC,GAAE,KAAK,eAAa,IAAI,GAAE,EAAC,kBAAiBH,GAAE,kBAAiB,cAAa,KAAK,eAAc,cAAaF,IAAE,QAAOF,IAAE,MAAKO,IAAE,MAAK,OAAG,eAAcF,IAAE,UAASJ,GAAC,CAAC,GAAE,KAAK,kBAAgB,IAAI,EAAE,EAAC,OAAM,IAAIS,GAAE,EAAC,cAAa,KAAK,cAAa,CAAC,GAAE,KAAIN,GAAE,YAAY,KAAI,eAAcA,GAAE,YAAY,eAAc,eAAcA,GAAE,YAAY,eAAc,cAAaA,GAAE,YAAY,cAAa,kBAAiBI,IAAE,wBAAuBC,GAAE,SAASL,GAAE,YAAY,gBAAgB,EAAC,CAAC;AAAE,UAAM,IAAE,SAAOA,GAAE,cAAc;AAAS,WAAO,KAAK,oBAAkBH,GAAE,GAAE,EAAC,eAAc,EAAEG,GAAE,aAAa,IAAEO,GAAE,SAASP,GAAE,aAAa,IAAE,MAAK,uBAAsB,KAAK,wBAAuB,kBAAiBI,GAAC,CAAC,GAAE,KAAK,mBAAiBP,GAAE,CAAC,GAAE,KAAK,SAAS,IAAI,CAAC,EAAG,MAAI,KAAK,gBAAgB,cAAe,CAAAG,OAAG,KAAK,KAAK,uBAAsB,EAAC,cAAaA,GAAC,CAAC,GAAG,CAAC,GAAE,EAAG,MAAI,KAAK,UAAW,MAAI,KAAK,gBAAgB,CAAE,CAAC,CAAC,GAAE,KAAK,WAAW,QAAQ,GAAE,KAAK,kBAAgB,OAAG,KAAK,UAAUA,GAAE,aAAa;AAAA,EAAC;AAAA,EAAC,MAAM,UAAUA,IAAE;AAAC,WAAO,MAAM,KAAK,iBAAiB,WAAW,KAAK,WAAW,OAAO,GAAE,KAAK,mCAAmCA,EAAC,GAAE,EAAC,QAAO,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBA,IAAEF,IAAE;AAAC,UAAM,KAAK,WAAW,SAAQ,EAAEA,EAAC;AAAE,UAAMG,KAAEO,GAAER,EAAC,GAAEH,KAAE,EAAEC,EAAC,IAAEA,GAAE,SAAO,MAAKW,KAAE,MAAM,KAAK,aAAa,wBAAwBR,IAAEJ,EAAC;AAAE,MAAEA,EAAC;AAAE,UAAMM,KAAE,MAAM,KAAK,kBAAkB,gBAAgBM,GAAE,YAAWZ,EAAC;AAAE,MAAEA,EAAC;AAAE,UAAMa,KAAE,MAAM,KAAK,4BAA4B,MAAMP,IAAEN,EAAC;AAAE,MAAEA,EAAC;AAAE,UAAMO,KAAE,MAAIM,GAAE,SAAOP,KAAEA,GAAE,OAAOO,EAAC;AAAE,WAAM,EAAC,QAAO,EAAC,YAAW,KAAK,iBAAiB,OAAOT,IAAEG,EAAC,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYJ,IAAEF,IAAE;AAAC,WAAO,MAAM,KAAK,iBAAiB,WAAW,KAAK,WAAW,OAAO,GAAE,EAAEA,EAAC,GAAE,KAAK,gBAAgB,WAASE,GAAE,UAAS,KAAK,gBAAgB,kBAAgBA,GAAE,OAAM,KAAK,gBAAgB,WAAS,EAAEA,GAAE,QAAQ,IAAEW,GAAE,SAASX,GAAE,QAAQ,IAAE,MAAKW;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQX,IAAEF,IAAE;AAAC,WAAO,MAAM,KAAK,iBAAiB,WAAW,KAAK,WAAW,OAAO,GAAE,EAAEA,EAAC,GAAE,KAAK,gBAAgB,QAAQ,GAAEa;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBX,IAAEF,IAAE;AAAC,WAAO,MAAM,KAAK,iBAAiB,WAAW,KAAK,WAAW,OAAO,GAAE,EAAEA,EAAC,GAAE,MAAM,EAAG,MAAI,CAAC,KAAK,wBAAwBA,EAAC,GAAE,EAAEA,EAAC,GAAEa;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaX,IAAEF,IAAE;AAAC,WAAO,EAAEA,EAAC,GAAE,EAAC,QAAO,KAAK,gBAAgB,UAAS;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBE,IAAEF,IAAE;AAAC,SAAK,iBAAiB,WAAW,KAAK,WAAW,OAAO,GAAE,EAAEA,EAAC;AAAE,UAAMG,KAAE,EAAE;AAAE,WAAO,KAAK,mBAAmB,IAAID,GAAE,IAAGC,EAAC,GAAE,KAAK,gBAAgB,WAAWA,GAAE,OAAO,GAAE,KAAK,sBAAsB,WAAWA,GAAE,OAAO,GAAEU;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcX,IAAEF,IAAE;AAAC,UAAMG,KAAE,KAAK,mBAAmB,IAAID,GAAE,EAAE;AAAE,WAAOC,MAAGA,GAAE,QAAQD,GAAE,KAAK,GAAE,EAAEF,EAAC,GAAEa;AAAA,EAAC;AAAA,EAAC,MAAM,4BAA4BX,IAAEF,IAAE;AAAC,WAAO,KAAK,kBAAkB,4BAA4B,GAAEa;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsBX,IAAEF,IAAE;AAAC,WAAO,KAAK,4BAA4B,sBAAsB,GAAEa;AAAA,EAAC;AAAA,EAAC,MAAM,8BAA8BX,IAAE;AAAC,WAAO,KAAK,8BAA4BJ,GAAEI,IAAE,KAAK,uBAAuB,GAAEW;AAAA,EAAC;AAAA,EAAC,mCAAmCX,IAAE;AAAC,SAAK,gBAAgB,SAAO,EAAEA,GAAE,MAAM,IAAE,EAAE,SAASA,GAAE,MAAM,IAAE,MAAK,KAAK,gBAAgB,mBAAiBA,GAAE;AAAA,EAAgB;AAAA,EAAC,kBAAiB;AAAC,SAAK,KAAK,mBAAkB,EAAC,UAAS,KAAK,SAAQ,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEL,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,0BAAyB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,wGAAwG,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;AAAE,SAASa,GAAER,IAAE;AAAC,SAAM,EAAC,OAAMA,GAAE,OAAM,MAAKA,GAAE,MAAK,UAASA,GAAE,UAAS,OAAMA,GAAE,OAAM,OAAM,EAAEA,GAAE,MAAM,IAAEA,GAAE,SAAO,EAAC,OAAM,MAAK,EAAC;AAAC;AAAC,IAAMW,KAAE,EAAC,QAAO,CAAC,EAAC;", "names": ["r", "s", "t", "o", "t", "e", "u", "s", "a", "j", "r", "d", "e", "t", "i", "r", "g", "j", "x", "w", "s", "u", "o", "S", "n", "a", "l", "c", "f", "E", "t", "e", "s", "r", "v", "e", "s", "t", "i", "x", "C", "T", "r", "n", "l", "o", "j", "a", "c", "w", "E", "S", "y", "w", "n", "r", "t", "c", "e", "i", "s", "o", "d", "f", "v", "x", "E", "a", "l", "j"]}