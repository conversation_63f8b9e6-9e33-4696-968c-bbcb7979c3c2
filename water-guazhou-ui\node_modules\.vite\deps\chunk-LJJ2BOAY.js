import {
  j,
  p as p2
} from "./chunk-IEIKQ72S.js";
import {
  e as e2
} from "./chunk-WJWRKQWS.js";
import {
  p as p3
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import {
  c
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import {
  n
} from "./chunk-MIA6BJ32.js";
import {
  t
} from "./chunk-NGPCXWDX.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  $,
  K,
  ht
} from "./chunk-U4SVMKOQ.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r
} from "./chunk-LTKA6OKA.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/support/WMTSLayerInfo.js
var i;
var p4 = i = class extends l {
  constructor(r3) {
    super(r3);
  }
  clone() {
    return new i({ customLayerParameters: p(this.customLayerParameters), customParameters: p(this.customParameters), layerIdentifier: this.layerIdentifier, tileMatrixSet: this.tileMatrixSet, url: this.url });
  }
};
e([y({ json: { type: Object, write: true } })], p4.prototype, "customLayerParameters", void 0), e([y({ json: { type: Object, write: true } })], p4.prototype, "customParameters", void 0), e([y({ type: String, json: { write: true } })], p4.prototype, "layerIdentifier", void 0), e([y({ type: String, json: { write: true } })], p4.prototype, "tileMatrixSet", void 0), e([y({ type: String, json: { write: true } })], p4.prototype, "url", void 0), p4 = i = e([a("esri.layer.support.WMTSLayerInfo")], p4);

// node_modules/@arcgis/core/layers/WebTileLayer.js
var x;
var U2 = x = class extends n(p3(t(c(_(O(b)))))) {
  constructor(...e3) {
    super(...e3), this.copyright = "", this.fullExtent = new w2(-20037508342787e-6, -2003750834278e-5, 2003750834278e-5, 20037508342787e-6, f.WebMercator), this.legendEnabled = false, this.isReference = null, this.popupEnabled = false, this.spatialReference = f.WebMercator, this.subDomains = null, this.tileInfo = new j({ size: [256, 256], dpi: 96, format: "png8", compressionQuality: 0, origin: new w({ x: -20037508342787e-6, y: 20037508342787e-6, spatialReference: f.WebMercator }), spatialReference: f.WebMercator, lods: [new p2({ level: 0, scale: 591657527591555e-6, resolution: 156543.033928 }), new p2({ level: 1, scale: 295828763795777e-6, resolution: 78271.5169639999 }), new p2({ level: 2, scale: 147914381897889e-6, resolution: 39135.7584820001 }), new p2({ level: 3, scale: 73957190948944e-6, resolution: 19567.8792409999 }), new p2({ level: 4, scale: 36978595474472e-6, resolution: 9783.93962049996 }), new p2({ level: 5, scale: 18489297737236e-6, resolution: 4891.96981024998 }), new p2({ level: 6, scale: 9244648868618e-6, resolution: 2445.98490512499 }), new p2({ level: 7, scale: 4622324434309e-6, resolution: 1222.99245256249 }), new p2({ level: 8, scale: 2311162217155e-6, resolution: 611.49622628138 }), new p2({ level: 9, scale: 1155581108577e-6, resolution: 305.748113140558 }), new p2({ level: 10, scale: 577790.554289, resolution: 152.874056570411 }), new p2({ level: 11, scale: 288895.277144, resolution: 76.4370282850732 }), new p2({ level: 12, scale: 144447.638572, resolution: 38.2185141425366 }), new p2({ level: 13, scale: 72223.819286, resolution: 19.1092570712683 }), new p2({ level: 14, scale: 36111.909643, resolution: 9.55462853563415 }), new p2({ level: 15, scale: 18055.954822, resolution: 4.77731426794937 }), new p2({ level: 16, scale: 9027.977411, resolution: 2.38865713397468 }), new p2({ level: 17, scale: 4513.988705, resolution: 1.19432856685505 }), new p2({ level: 18, scale: 2256.994353, resolution: 0.597164283559817 }), new p2({ level: 19, scale: 1128.497176, resolution: 0.298582141647617 }), new p2({ level: 20, scale: 564.248588, resolution: 0.14929107082380833 }), new p2({ level: 21, scale: 282.124294, resolution: 0.07464553541190416 }), new p2({ level: 22, scale: 141.062147, resolution: 0.03732276770595208 }), new p2({ level: 23, scale: 70.5310735, resolution: 0.01866138385297604 })] }), this.type = "web-tile", this.urlTemplate = null, this.wmtsInfo = null;
  }
  normalizeCtorArgs(e3, t2) {
    return "string" == typeof e3 ? { urlTemplate: e3, ...t2 } : e3;
  }
  load(e3) {
    const t2 = this.loadFromPortal({ supportedTypes: ["WMTS"] }, e3).then(() => {
      var _a;
      let e4 = "";
      if (this.urlTemplate) if (this.spatialReference.equals(this.tileInfo.spatialReference)) {
        const t3 = new $(this.urlTemplate);
        !(!!this.subDomains && this.subDomains.length > 0) && ((_a = t3.authority) == null ? void 0 : _a.includes("{subDomain}")) && (e4 = "is missing 'subDomains' property");
      } else e4 = "spatialReference must match tileInfo.spatialReference";
      else e4 = "is missing the required 'urlTemplate' property value";
      if (e4) throw new s("web-tile-layer:load", `WebTileLayer (title: '${this.title}', id: '${this.id}') ${e4}`);
    });
    return this.addResolvingPromise(t2), Promise.resolve(this);
  }
  get levelValues() {
    const e3 = [];
    if (!this.tileInfo) return null;
    for (const t2 of this.tileInfo.lods) e3[t2.level] = t2.levelValue || t2.level;
    return e3;
  }
  readSpatialReference(e3, t2) {
    return e3 || t2.fullExtent && t2.fullExtent.spatialReference && f.fromJSON(t2.fullExtent.spatialReference);
  }
  get tileServers() {
    if (!this.urlTemplate) return null;
    const e3 = [], { urlTemplate: t2, subDomains: r3 } = this, l2 = new $(t2), o2 = l2.scheme ? l2.scheme + "://" : "//", i2 = o2 + l2.authority + "/", n2 = l2.authority;
    if (n2 == null ? void 0 : n2.includes("{subDomain}")) {
      if (r3 && r3.length > 0 && n2.split(".").length > 1) for (const s2 of r3) e3.push(o2 + n2.replace(/\{subDomain\}/gi, s2) + "/");
    } else e3.push(i2);
    return e3.map((e4) => ("/" !== e4.charAt(e4.length - 1) && (e4 += "/"), e4));
  }
  get urlPath() {
    if (!this.urlTemplate) return null;
    const e3 = this.urlTemplate, t2 = new $(e3), r3 = (t2.scheme ? t2.scheme + "://" : "//") + t2.authority + "/";
    return e3.substring(r3.length);
  }
  readUrlTemplate(e3, t2) {
    return e3 || t2.templateUrl;
  }
  writeUrlTemplate(e3, t2) {
    e3 && ht(e3) && (e3 = "https:" + e3), e3 && (e3 = e3.replace(/\{z\}/gi, "{level}").replace(/\{x\}/gi, "{col}").replace(/\{y\}/gi, "{row}"), e3 = K(e3)), t2.templateUrl = e3;
  }
  fetchTile(e3, r3, l2, o2 = {}) {
    const { signal: s2 } = o2, i2 = this.getTileUrl(e3, r3, l2), n2 = { responseType: "image", signal: s2, query: { ...this.refreshParameters } };
    return U(i2, n2).then((e4) => e4.data);
  }
  async fetchImageBitmapTile(e3, l2, o2, s2 = {}) {
    const { signal: i2 } = s2;
    if (this.fetchTile !== x.prototype.fetchTile) {
      const t2 = await this.fetchTile(e3, l2, o2, s2);
      try {
        return createImageBitmap(t2);
      } catch (u) {
        throw new s("request:server", `Unable to load tile ${e3}/${l2}/${o2}`, { error: u, level: e3, row: l2, col: o2 });
      }
    }
    const n2 = this.getTileUrl(e3, l2, o2), a2 = { responseType: "blob", signal: i2, query: { ...this.refreshParameters } }, { data: p5 } = await U(n2, a2);
    return e2(p5, n2);
  }
  getTileUrl(e3, t2, r3) {
    const { levelValues: l2, tileServers: s2, urlPath: i2 } = this;
    if (!l2 || !s2 || !i2) return "";
    const n2 = l2[e3];
    return s2[t2 % s2.length] + r(i2, { level: n2, z: n2, col: r3, x: r3, row: t2, y: t2 });
  }
};
e([y({ type: String, value: "", json: { write: true } })], U2.prototype, "copyright", void 0), e([y({ type: w2, json: { write: true }, nonNullable: true })], U2.prototype, "fullExtent", void 0), e([y({ readOnly: true, json: { read: false, write: false } })], U2.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide"] })], U2.prototype, "listMode", void 0), e([y({ json: { read: true, write: true } })], U2.prototype, "blendMode", void 0), e([y()], U2.prototype, "levelValues", null), e([y({ type: Boolean, json: { read: false, write: { enabled: true, overridePolicy: () => ({ enabled: false }) } } })], U2.prototype, "isReference", void 0), e([y({ type: ["WebTiledLayer"], value: "WebTiledLayer" })], U2.prototype, "operationalLayerType", void 0), e([y({ readOnly: true, json: { read: false, write: false } })], U2.prototype, "popupEnabled", void 0), e([y({ type: f })], U2.prototype, "spatialReference", void 0), e([o("spatialReference", ["spatialReference", "fullExtent.spatialReference"])], U2.prototype, "readSpatialReference", null), e([y({ type: [String], json: { write: true } })], U2.prototype, "subDomains", void 0), e([y({ type: j, json: { write: true } })], U2.prototype, "tileInfo", void 0), e([y({ readOnly: true })], U2.prototype, "tileServers", null), e([y({ json: { read: false } })], U2.prototype, "type", void 0), e([y()], U2.prototype, "urlPath", null), e([y({ type: String, json: { origins: { "portal-item": { read: { source: "url" } } } } })], U2.prototype, "urlTemplate", void 0), e([o("urlTemplate", ["urlTemplate", "templateUrl"])], U2.prototype, "readUrlTemplate", null), e([r2("urlTemplate", { templateUrl: { type: String } })], U2.prototype, "writeUrlTemplate", null), e([y({ type: p4, json: { write: true } })], U2.prototype, "wmtsInfo", void 0), U2 = x = e([a("esri.layers.WebTileLayer")], U2);
var L = U2;

export {
  p4 as p,
  L
};
//# sourceMappingURL=chunk-LJJ2BOAY.js.map
