import {
  m,
  p as p2
} from "./chunk-3M3FTH72.js";
import {
  p as p3
} from "./chunk-UYJR3ZHF.js";
import {
  v
} from "./chunk-6NKJB2TO.js";
import {
  a as a2,
  e as e2,
  i,
  t
} from "./chunk-HM62IZSE.js";
import {
  s as s4
} from "./chunk-7SWS36OI.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  o as o2
} from "./chunk-G5KX4JSG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s as s3
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T,
  u
} from "./chunk-HP475EI3.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/renderers/support/AuthoringInfoClassBreakInfo.js
var t2;
var p4 = t2 = class extends l {
  constructor(r2) {
    super(r2), this.minValue = 0, this.maxValue = 0;
  }
  clone() {
    return new t2({ minValue: this.minValue, maxValue: this.maxValue });
  }
};
e([y({ type: Number, json: { write: true } })], p4.prototype, "minValue", void 0), e([y({ type: Number, json: { write: true } })], p4.prototype, "maxValue", void 0), p4 = t2 = e([a("esri.renderer.support.AuthoringInfoClassBreakInfo")], p4);

// node_modules/@arcgis/core/renderers/support/AuthoringInfoFieldInfo.js
var n;
var p5 = n = class extends l {
  constructor(o3) {
    super(o3), this.field = "", this.normalizationField = "", this.label = "", this.classBreakInfos = [];
  }
  clone() {
    return new n({ field: this.field, normalizationField: this.normalizationField, label: this.label, classBreakInfos: p(this.classBreakInfos) });
  }
};
e([y({ type: String, json: { write: true } })], p5.prototype, "field", void 0), e([y({ type: String, json: { write: true } })], p5.prototype, "normalizationField", void 0), e([y({ type: String, json: { write: true } })], p5.prototype, "label", void 0), e([y({ type: [p4], json: { write: true } })], p5.prototype, "classBreakInfos", void 0), p5 = n = e([a("esri.renderers.support.AuthoringInfoFieldInfo")], p5);

// node_modules/@arcgis/core/renderers/support/AuthoringInfoVisualVariable.js
var n2;
var l2 = new s3({ percentTotal: "percent-of-total", ratio: "ratio", percent: "percent" });
var p6 = new s3({ sizeInfo: "size", colorInfo: "color", transparencyInfo: "opacity", rotationInfo: "rotation" });
var a3 = { key: (e3) => "number" == typeof e3 ? "number" : "string", typeMap: { number: Number, string: String }, base: null };
var u2 = ["high-to-low", "above-and-below", "centered-on", "extremes"];
var m2 = [.../* @__PURE__ */ new Set([...["high-to-low", "above-and-below", "centered-on", "extremes", "90-10", "above", "below"], ...["high-to-low", "above-and-below", "90-10", "above", "below"]])];
var y2 = ["seconds", "minutes", "hours", "days", "months", "years"];
var d = n2 = class extends l {
  constructor(e3) {
    super(e3), this.endTime = null, this.field = null, this.maxSliderValue = null, this.minSliderValue = null, this.startTime = null, this.type = null, this.units = null;
  }
  castEndTime(e3) {
    return "string" == typeof e3 || "number" == typeof e3 ? e3 : null;
  }
  castStartTime(e3) {
    return "string" == typeof e3 || "number" == typeof e3 ? e3 : null;
  }
  get style() {
    return "color" === this.type ? this._get("style") : null;
  }
  set style(e3) {
    this._set("style", e3);
  }
  get theme() {
    return "color" === this.type || "size" === this.type ? this._get("theme") || "high-to-low" : null;
  }
  set theme(e3) {
    this._set("theme", e3);
  }
  clone() {
    return new n2({ endTime: this.endTime, field: this.field, maxSliderValue: this.maxSliderValue, minSliderValue: this.minSliderValue, startTime: this.startTime, style: this.style, theme: this.theme, type: this.type, units: this.units });
  }
};
e([y({ types: a3, json: { write: true } })], d.prototype, "endTime", void 0), e([s4("endTime")], d.prototype, "castEndTime", null), e([y({ type: String, json: { write: true } })], d.prototype, "field", void 0), e([y({ type: Number, json: { write: true } })], d.prototype, "maxSliderValue", void 0), e([y({ type: Number, json: { write: true } })], d.prototype, "minSliderValue", void 0), e([y({ types: a3, json: { write: true } })], d.prototype, "startTime", void 0), e([s4("startTime")], d.prototype, "castStartTime", null), e([y({ type: l2.apiValues, value: null, json: { type: l2.jsonValues, read: l2.read, write: l2.write } })], d.prototype, "style", null), e([y({ type: m2, value: null, json: { type: m2, origins: { "web-scene": { type: u2, write: { writer: (e3, t5) => {
  u2.includes(e3) && (t5.theme = e3);
} } } }, write: true } })], d.prototype, "theme", null), e([y({ type: p6.apiValues, json: { type: p6.jsonValues, read: p6.read, write: p6.write } })], d.prototype, "type", void 0), e([y({ type: y2, json: { type: y2, write: true } })], d.prototype, "units", void 0), d = n2 = e([a("esri.renderers.support.AuthoringInfoVisualVariable")], d);
var h = d;

// node_modules/@arcgis/core/renderers/support/AuthoringInfo.js
var c;
var h2 = new s3({ esriClassifyDefinedInterval: "defined-interval", esriClassifyEqualInterval: "equal-interval", esriClassifyManual: "manual", esriClassifyNaturalBreaks: "natural-breaks", esriClassifyQuantile: "quantile", esriClassifyStandardDeviation: "standard-deviation" });
var y3 = new s3({ pieChart: "pie-chart", classedSize: "class-breaks-size", classedColor: "class-breaks-color", univariateColorSize: "univariate-color-size", relationship: "relationship", predominance: "predominance", dotDensity: "dot-density", flow: "flow" });
var m3 = new s3({ classedSize: "class-breaks-size", classedColor: "class-breaks-color", univariateColorSize: "univariate-color-size", relationship: "relationship", predominance: "predominance", dotDensity: "dot-density" });
var f = ["inches", "feet", "yards", "miles", "nautical-miles", "millimeters", "centimeters", "decimeters", "meters", "kilometers", "decimal-degrees"];
var v2 = ["high-to-low", "above-and-below", "above", "below", "90-10"];
var w = ["flow-line", "wave-front"];
var b = ["caret", "circle-caret", "arrow", "circle-arrow", "plus-minus", "circle-plus-minus", "square", "circle", "triangle", "happy-sad", "thumb", "custom"];
var g = c = class extends l {
  constructor(e3) {
    super(e3), this.colorRamp = null, this.fadeRatio = null, this.isAutoGenerated = false, this.lengthUnit = null, this.maxSliderValue = null, this.minSliderValue = null, this.visualVariables = null;
  }
  get classificationMethod() {
    const e3 = this._get("classificationMethod"), t5 = this.type;
    return t5 && "relationship" !== t5 ? "class-breaks-size" === t5 || "class-breaks-color" === t5 ? e3 || "manual" : null : e3;
  }
  set classificationMethod(e3) {
    this._set("classificationMethod", e3);
  }
  readColorRamp(e3) {
    return e3 ? p2(e3) : void 0;
  }
  get fields() {
    return this.type && "predominance" !== this.type ? null : this._get("fields");
  }
  set fields(e3) {
    this._set("fields", e3);
  }
  get field1() {
    return this.type && "relationship" !== this.type ? null : this._get("field1");
  }
  set field1(e3) {
    this._set("field1", e3);
  }
  get field2() {
    return this.type && "relationship" !== this.type ? null : this._get("field2");
  }
  set field2(e3) {
    this._set("field2", e3);
  }
  get flowTheme() {
    return "flow" === this.type ? this._get("flowTheme") : null;
  }
  set flowTheme(e3) {
    this._set("flowTheme", e3);
  }
  get focus() {
    return this.type && "relationship" !== this.type ? null : this._get("focus");
  }
  set focus(e3) {
    this._set("focus", e3);
  }
  get numClasses() {
    return this.type && "relationship" !== this.type ? null : this._get("numClasses");
  }
  set numClasses(e3) {
    this._set("numClasses", e3);
  }
  get statistics() {
    return "univariate-color-size" === this.type && "above-and-below" === this.univariateTheme ? this._get("statistics") : null;
  }
  set statistics(e3) {
    this._set("statistics", e3);
  }
  get standardDeviationInterval() {
    const e3 = this.type;
    return e3 && "relationship" !== e3 && "class-breaks-size" !== e3 && "class-breaks-color" !== e3 || this.classificationMethod && "standard-deviation" !== this.classificationMethod ? null : this._get("standardDeviationInterval");
  }
  set standardDeviationInterval(e3) {
    this._set("standardDeviationInterval", e3);
  }
  get type() {
    return this._get("type");
  }
  set type(e3) {
    let t5 = e3;
    "classed-size" === e3 ? t5 = "class-breaks-size" : "classed-color" === e3 && (t5 = "class-breaks-color"), this._set("type", t5);
  }
  get univariateSymbolStyle() {
    return "univariate-color-size" === this.type && "above-and-below" === this.univariateTheme ? this._get("univariateSymbolStyle") : null;
  }
  set univariateSymbolStyle(e3) {
    this._set("univariateSymbolStyle", e3);
  }
  get univariateTheme() {
    return "univariate-color-size" === this.type ? this._get("univariateTheme") : null;
  }
  set univariateTheme(e3) {
    this._set("univariateTheme", e3);
  }
  clone() {
    return new c({ classificationMethod: this.classificationMethod, colorRamp: p(this.colorRamp), fadeRatio: p(this.fadeRatio), fields: this.fields && this.fields.slice(0), field1: p(this.field1), field2: p(this.field2), isAutoGenerated: this.isAutoGenerated, focus: this.focus, numClasses: this.numClasses, maxSliderValue: this.maxSliderValue, minSliderValue: this.minSliderValue, lengthUnit: this.lengthUnit, statistics: this.statistics, standardDeviationInterval: this.standardDeviationInterval, type: this.type, visualVariables: this.visualVariables && this.visualVariables.map((e3) => e3.clone()), univariateSymbolStyle: this.univariateSymbolStyle, univariateTheme: this.univariateTheme, flowTheme: this.flowTheme });
  }
};
e([y({ type: h2.apiValues, value: null, json: { type: h2.jsonValues, read: h2.read, write: h2.write, origins: { "web-document": { default: "manual", type: h2.jsonValues, read: h2.read, write: h2.write } } } })], g.prototype, "classificationMethod", null), e([y({ types: m, json: { write: true } })], g.prototype, "colorRamp", void 0), e([o("colorRamp")], g.prototype, "readColorRamp", null), e([y({ json: { write: true, origins: { "web-scene": { write: false, read: false } } } })], g.prototype, "fadeRatio", void 0), e([y({ type: [String], value: null, json: { write: true } })], g.prototype, "fields", null), e([y({ type: p5, value: null, json: { write: true } })], g.prototype, "field1", null), e([y({ type: p5, value: null, json: { write: true } })], g.prototype, "field2", null), e([y({ type: w, value: null, json: { write: true, origins: { "web-scene": { write: false } } } })], g.prototype, "flowTheme", null), e([y({ type: ["HH", "HL", "LH", "LL"], value: null, json: { write: true } })], g.prototype, "focus", null), e([y({ type: Boolean, json: { write: true, default: false, origins: { "web-scene": { write: false } } } })], g.prototype, "isAutoGenerated", void 0), e([y({ type: Number, value: null, json: { type: T, write: true } })], g.prototype, "numClasses", null), e([y({ type: f, json: { type: f, read: false, write: false, origins: { "web-scene": { read: true, write: true } } } })], g.prototype, "lengthUnit", void 0), e([y({ type: Number, json: { write: true, origins: { "web-scene": { write: false, read: false } } } })], g.prototype, "maxSliderValue", void 0), e([y({ type: Number, json: { write: true, origins: { "web-scene": { write: false, read: false } } } })], g.prototype, "minSliderValue", void 0), e([y({ type: Object, value: null, json: { write: true, origins: { "web-scene": { write: false, read: false } } } })], g.prototype, "statistics", null), e([y({ type: [0.25, 0.33, 0.5, 1], value: null, json: { type: [0.25, 0.33, 0.5, 1], write: true } })], g.prototype, "standardDeviationInterval", null), e([y({ type: y3.apiValues, value: null, json: { type: y3.jsonValues, read: y3.read, write: y3.write, origins: { "web-scene": { type: m3.jsonValues, write: { writer: m3.write, overridePolicy: (e3) => ({ enabled: "flow" !== e3 }) } } } } })], g.prototype, "type", null), e([y({ type: [h], json: { write: true } })], g.prototype, "visualVariables", void 0), e([y({ type: b, value: null, json: { write: true, origins: { "web-scene": { write: false } } } })], g.prototype, "univariateSymbolStyle", null), e([y({ type: v2, value: null, json: { write: true, origins: { "web-scene": { write: false } } } })], g.prototype, "univariateTheme", null), g = c = e([a("esri.renderers.support.AuthoringInfo")], g);
var j = g;

// node_modules/@arcgis/core/renderers/visualVariables/support/VisualVariableLegendOptions.js
var t3;
var p7 = t3 = class extends p3 {
  constructor() {
    super(...arguments), this.showLegend = null;
  }
  clone() {
    return new t3({ title: this.title, showLegend: this.showLegend });
  }
};
e([y({ type: Boolean, json: { write: true } })], p7.prototype, "showLegend", void 0), p7 = t3 = e([a("esri.renderers.visualVariables.support.VisualVariableLegendOptions")], p7);
var i2 = p7;

// node_modules/@arcgis/core/renderers/visualVariables/VisualVariable.js
var a4 = new s3({ colorInfo: "color", transparencyInfo: "opacity", rotationInfo: "rotation", sizeInfo: "size" });
var u3 = class extends l {
  constructor(e3) {
    super(e3), this.index = null, this.type = null, this.field = null, this.valueExpression = null, this.valueExpressionTitle = null, this.legendOptions = null;
  }
  castField(e3) {
    return null == e3 ? e3 : "function" == typeof e3 ? (s.getLogger(this.declaredClass).error(".field: field must be a string value"), null) : u(e3);
  }
  get arcadeRequired() {
    return !!this.valueExpression;
  }
  clone() {
  }
  getAttributeHash() {
    return `${this.type}-${this.field}-${this.valueExpression}`;
  }
};
e([y()], u3.prototype, "index", void 0), e([y({ type: a4.apiValues, readOnly: true, json: { read: a4.read, write: a4.write } })], u3.prototype, "type", void 0), e([y({ type: String, json: { write: true } })], u3.prototype, "field", void 0), e([s4("field")], u3.prototype, "castField", null), e([y({ type: String, json: { write: true } })], u3.prototype, "valueExpression", void 0), e([y({ type: String, json: { write: true } })], u3.prototype, "valueExpressionTitle", void 0), e([y({ readOnly: true })], u3.prototype, "arcadeRequired", null), e([y({ type: i2, json: { write: true } })], u3.prototype, "legendOptions", void 0), u3 = e([a("esri.renderers.visualVariables.VisualVariable")], u3);
var c2 = u3;

// node_modules/@arcgis/core/renderers/visualVariables/support/SizeStop.js
var p8;
var i3 = p8 = class extends l {
  constructor(r2) {
    super(r2), this.label = null, this.size = null, this.value = null;
  }
  clone() {
    return new p8({ label: this.label, size: this.size, value: this.value });
  }
};
e([y({ type: String, json: { write: true } })], i3.prototype, "label", void 0), e([y({ type: Number, cast: o2, json: { write: true } })], i3.prototype, "size", void 0), e([y({ type: Number, json: { write: true } })], i3.prototype, "value", void 0), i3 = p8 = e([a("esri.renderers.visualVariables.support.SizeStop")], i3);
var l3 = i3;

// node_modules/@arcgis/core/renderers/visualVariables/support/SizeVariableLegendOptions.js
var t4;
var i4 = t4 = class extends i2 {
  constructor() {
    super(...arguments), this.customValues = null;
  }
  clone() {
    return new t4({ title: this.title, showLegend: this.showLegend, customValues: this.customValues && this.customValues.slice(0) });
  }
};
e([y({ type: [Number], json: { write: true } })], i4.prototype, "customValues", void 0), i4 = t4 = e([a("esri.renderers.visualVariables.support.SizeVariableLegendOptions")], i4);
var a5 = i4;

// node_modules/@arcgis/core/renderers/visualVariables/SizeVariable.js
var z;
var v3 = new s3({ width: "width", depth: "depth", height: "height", widthAndDepth: "width-and-depth", all: "all" });
var g2 = new s3({ unknown: "unknown", inch: "inches", foot: "feet", yard: "yards", mile: "miles", "nautical-mile": "nautical-miles", millimeter: "millimeters", centimeter: "centimeters", decimeter: "decimeters", meter: "meters", kilometer: "kilometers", "decimal-degree": "decimal-degrees" });
function w2(e3) {
  if (null != e3) return "string" == typeof e3 || "number" == typeof e3 ? o2(e3) : "size" === e3.type ? e2(e3) ? e3 : (delete (e3 = { ...e3 }).type, new V(e3)) : void 0;
}
function f2(e3, t5, i5) {
  if ("object" != typeof e3) return e3;
  const s5 = new V();
  return s5.read(e3, i5), s5;
}
var V = z = class extends c2 {
  constructor(e3) {
    super(e3), this.axis = null, this.legendOptions = null, this.normalizationField = null, this.scaleBy = null, this.target = null, this.type = "size", this.useSymbolValue = null, this.valueExpression = null, this.valueRepresentation = null, this.valueUnit = null;
  }
  get cache() {
    return { ipData: this._interpolateData(), hasExpression: !!this.valueExpression, compiledFunc: null, isScaleDriven: null != this.valueExpression && v.test(this.valueExpression) };
  }
  set expression(e3) {
    s.getLogger(this.declaredClass).warn("'expression' is deprecated since version 4.2. Use 'valueExpression' instead. The only supported expression is 'view.scale'."), "view.scale" === e3 ? (this.valueExpression = "$view.scale", this._set("expression", e3)) : this._set("expression", null);
  }
  set index(e3) {
    e2(this.maxSize) && (this.maxSize.index = `visualVariables[${e3}].maxSize`), e2(this.minSize) && (this.minSize.index = `visualVariables[${e3}].minSize`), this._set("index", e3);
  }
  get inputValueType() {
    return t(this);
  }
  set maxDataValue(e3) {
    e3 && this.stops && (s.getLogger(this.declaredClass).warn("cannot set maxDataValue when stops is not null."), e3 = null), this._set("maxDataValue", e3);
  }
  set maxSize(e3) {
    e3 && this.stops && (s.getLogger(this.declaredClass).warn("cannot set maxSize when stops is not null."), e3 = null), this._set("maxSize", e3);
  }
  castMaxSize(e3) {
    return w2(e3);
  }
  readMaxSize(e3, t5, i5) {
    return f2(e3, t5, i5);
  }
  set minDataValue(e3) {
    e3 && this.stops && (s.getLogger(this.declaredClass).warn("cannot set minDataValue when stops is not null."), e3 = null), this._set("minDataValue", e3);
  }
  set minSize(e3) {
    e3 && this.stops && (s.getLogger(this.declaredClass).warn("cannot set minSize when stops is not null."), e3 = null), this._set("minSize", e3);
  }
  castMinSize(e3) {
    return w2(e3);
  }
  readMinSize(e3, t5, i5) {
    return f2(e3, t5, i5);
  }
  get arcadeRequired() {
    return !!this.valueExpression || (null != this.minSize && "object" == typeof this.minSize && this.minSize.arcadeRequired || null != this.maxSize && "object" == typeof this.maxSize && this.maxSize.arcadeRequired);
  }
  set stops(e3) {
    null == this.minDataValue && null == this.maxDataValue && null == this.minSize && null == this.maxSize ? e3 && Array.isArray(e3) && (e3 = e3.filter((e4) => !!e4)).sort((e4, t5) => e4.value - t5.value) : e3 && (s.getLogger(this.declaredClass).warn("cannot set stops when one of minDataValue, maxDataValue, minSize or maxSize is not null."), e3 = null), this._set("stops", e3);
  }
  get transformationType() {
    return a2(this, this.inputValueType);
  }
  readValueExpression(e3, t5) {
    return e3 || t5.expression && "$view.scale";
  }
  writeValueExpressionWebScene(e3, i5, s5, r2) {
    if ("$view.scale" === e3) {
      if (r2 && r2.messages) {
        const e4 = this.index, i6 = "string" == typeof e4 ? e4 : `visualVariables[${e4}]`;
        r2.messages.push(new s2("property:unsupported", this.type + "VisualVariable.valueExpression = '$view.scale' is not supported in Web Scene. Please remove this property to save the Web Scene.", { instance: this, propertyName: i6 + ".valueExpression", context: r2 }));
      }
    } else i5[s5] = e3;
  }
  readValueUnit(e3) {
    return e3 ? g2.read(e3) : null;
  }
  clone() {
    return new z({ axis: this.axis, field: this.field, valueExpression: this.valueExpression, valueExpressionTitle: this.valueExpressionTitle, maxDataValue: this.maxDataValue, maxSize: e2(this.maxSize) ? this.maxSize.clone() : this.maxSize, minDataValue: this.minDataValue, minSize: e2(this.minSize) ? this.minSize.clone() : this.minSize, normalizationField: this.normalizationField, stops: this.stops && this.stops.map((e3) => e3.clone()), target: this.target, useSymbolValue: this.useSymbolValue, valueRepresentation: this.valueRepresentation, valueUnit: this.valueUnit, legendOptions: this.legendOptions && this.legendOptions.clone() });
  }
  flipSizes() {
    if (this.transformationType === i.ClampedLinear) {
      const { minSize: e3, maxSize: t5 } = this;
      return this.minSize = t5, this.maxSize = e3, this;
    }
    if (this.transformationType === i.Stops) {
      const e3 = this.stops;
      if (!e3) return this;
      const t5 = e3.map((e4) => e4.size).reverse(), i5 = e3.length;
      for (let s5 = 0; s5 < i5; s5++) e3[s5].size = t5[s5];
      return this;
    }
    return this;
  }
  getAttributeHash() {
    return `${super.getAttributeHash()}-${this.target}-${this.normalizationField}`;
  }
  _interpolateData() {
    return this.stops && this.stops.map((e3) => e3.value || 0);
  }
};
e([y({ readOnly: true })], V.prototype, "cache", null), e([y({ type: v3.apiValues, json: { type: v3.jsonValues, origins: { "web-map": { read: false } }, read: v3.read, write: v3.write } })], V.prototype, "axis", void 0), e([y({ type: String, value: null, json: { read: false } })], V.prototype, "expression", null), e([y()], V.prototype, "index", null), e([y({ type: String, readOnly: true })], V.prototype, "inputValueType", null), e([y({ type: a5, json: { write: true } })], V.prototype, "legendOptions", void 0), e([y({ type: Number, value: null, json: { write: true } })], V.prototype, "maxDataValue", null), e([y({ type: Number, value: null, json: { write: true } })], V.prototype, "maxSize", null), e([s4("maxSize")], V.prototype, "castMaxSize", null), e([o("maxSize")], V.prototype, "readMaxSize", null), e([y({ type: Number, value: null, json: { write: true } })], V.prototype, "minDataValue", null), e([y({ type: Number, value: null, json: { write: true } })], V.prototype, "minSize", null), e([s4("minSize")], V.prototype, "castMinSize", null), e([o("minSize")], V.prototype, "readMinSize", null), e([y({ type: String, json: { write: true } })], V.prototype, "normalizationField", void 0), e([y({ readOnly: true })], V.prototype, "arcadeRequired", null), e([y({ type: String })], V.prototype, "scaleBy", void 0), e([y({ type: [l3], value: null, json: { write: true } })], V.prototype, "stops", null), e([y({ type: ["outline"], json: { write: true } })], V.prototype, "target", void 0), e([y({ type: String, readOnly: true })], V.prototype, "transformationType", null), e([y({ type: ["size"], json: { type: ["sizeInfo"] } })], V.prototype, "type", void 0), e([y({ type: Boolean, json: { write: true, origins: { "web-map": { read: false } } } })], V.prototype, "useSymbolValue", void 0), e([y({ type: String, json: { write: true } })], V.prototype, "valueExpression", void 0), e([o("valueExpression", ["valueExpression", "expression"])], V.prototype, "readValueExpression", null), e([r("web-scene", "valueExpression")], V.prototype, "writeValueExpressionWebScene", null), e([y({ type: ["radius", "diameter", "area", "width", "distance"], json: { write: true } })], V.prototype, "valueRepresentation", void 0), e([y({ type: g2.apiValues, json: { write: g2.write, origins: { "web-map": { read: false }, "web-scene": { write: true }, "portal-item": { write: true } } } })], V.prototype, "valueUnit", void 0), e([o("valueUnit")], V.prototype, "readValueUnit", null), V = z = e([a("esri.renderers.visualVariables.SizeVariable")], V);
var b2 = V;

export {
  j,
  c2 as c,
  l3 as l,
  a5 as a,
  b2 as b
};
//# sourceMappingURL=chunk-QC5SLERR.js.map
