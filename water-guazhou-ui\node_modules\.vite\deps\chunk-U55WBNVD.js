import {
  r as r3
} from "./chunk-XDK46HA6.js";
import {
  e as e2
} from "./chunk-TKIS7RZA.js";
import {
  h as h2,
  n,
  p,
  u as u2
} from "./chunk-ZKWNW26M.js";
import {
  e2 as e4,
  o2 as o6,
  o3 as o7,
  v as v2
} from "./chunk-TOYJMVHA.js";
import {
  o as o5
} from "./chunk-Q6BEUTMN.js";
import {
  a2,
  v as v3
} from "./chunk-XVTFHFM3.js";
import {
  a
} from "./chunk-QB6AUIQ2.js";
import {
  r as r2
} from "./chunk-REGYRSW7.js";
import {
  n as n2
} from "./chunk-Y424ZXTG.js";
import {
  e as e5
} from "./chunk-UB5FTTH5.js";
import {
  d,
  i,
  o as o4
} from "./chunk-6GW7M2AQ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  c,
  d as d2,
  v
} from "./chunk-N3S5O3YO.js";
import {
  e as e3
} from "./chunk-32BGXH4N.js";
import {
  o as o3
} from "./chunk-BPRRRPC3.js";
import {
  o as o8
} from "./chunk-TUB4N6LD.js";
import {
  e
} from "./chunk-YV4RKNU4.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";
import {
  r
} from "./chunk-FOE4ICAJ.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/PathVertexPosition.glsl.js
var f = 8;
function n3(e6, c2) {
  const n4 = O.FEATUREVALUE;
  e6.attributes.add(n4, "vec4");
  const p3 = e6.vertex;
  p3.code.add(o`
  bool isCapVertex() {
    return ${n4}.w == 1.0;
  }
  `), p3.uniforms.add(new e("size", (e7) => e7.size)), c2.vvSize ? (p3.uniforms.add(new e3("vvSizeMinSize", (e7) => e7.vvSizeMinSize)), p3.uniforms.add(new e3("vvSizeMaxSize", (e7) => e7.vvSizeMaxSize)), p3.uniforms.add(new e3("vvSizeOffset", (e7) => e7.vvSizeOffset)), p3.uniforms.add(new e3("vvSizeFactor", (e7) => e7.vvSizeFactor)), p3.code.add(o`
    vec2 getSize() {
      return size * clamp(vvSizeOffset + ${n4}.x * vvSizeFactor, vvSizeMinSize, vvSizeMaxSize).xz;
    }
    `)) : p3.code.add(o`vec2 getSize(){
return size;
}`), c2.vvOpacity ? (p3.constants.add("vvOpacityNumber", "int", f), p3.uniforms.add([new o6("vvOpacityValues", (e7) => e7.vvOpacityValues, f), new o6("vvOpacityOpacities", (e7) => e7.vvOpacityOpacities, f)]), p3.code.add(o`
    vec4 applyOpacity(vec4 color) {
      float value = ${n4}.z;
      if (value <= vvOpacityValues[0]) {
        return vec4( color.xyz, vvOpacityOpacities[0]);
      }

      for (int i = 1; i < vvOpacityNumber; ++i) {
        if (vvOpacityValues[i] >= value) {
          float f = (value - vvOpacityValues[i-1]) / (vvOpacityValues[i] - vvOpacityValues[i-1]);
          return vec4( color.xyz, mix(vvOpacityOpacities[i-1], vvOpacityOpacities[i], f));
        }
      }

      return vec4( color.xyz, vvOpacityOpacities[vvOpacityNumber - 1]);
    }
    `)) : p3.code.add(o`vec4 applyOpacity(vec4 color){
return color;
}`), c2.vvColor ? (p3.constants.add("vvColorNumber", "int", o7), p3.uniforms.add([new o6("vvColorValues", (e7) => e7.vvColorValues, o7), new e4("vvColorColors", (e7) => e7.vvColorColors, o7)]), p3.code.add(o`
    vec4 getColor() {
      float value = ${n4}.y;
      if (value <= vvColorValues[0]) {
        return applyOpacity(vvColorColors[0]);
      }

      for (int i = 1; i < vvColorNumber; ++i) {
        if (vvColorValues[i] >= value) {
          float f = (value - vvColorValues[i-1]) / (vvColorValues[i] - vvColorValues[i-1]);
          return applyOpacity(mix(vvColorColors[i-1], vvColorColors[i], f));
        }
      }

      return applyOpacity(vvColorColors[vvColorNumber - 1]);
    }
    `)) : p3.code.add(o`vec4 getColor(){
return applyOpacity(vec4(1, 1, 1, 1));
}`), e6.include(o4), e6.attributes.add(O.PROFILERIGHT, "vec4"), e6.attributes.add(O.PROFILEUP, "vec4"), e6.attributes.add(O.PROFILEVERTEXANDNORMAL, "vec4"), p3.code.add(o`vec3 calculateVPos() {
vec2 size = getSize();
vec3 origin = position;
vec3 right = profileRight.xyz;
vec3 up = profileUp.xyz;
vec3 forward = cross(up, right);
vec2 profileVertex = profileVertexAndNormal.xy * size;
vec2 profileNormal = profileVertexAndNormal.zw;
float positionOffsetAlongProfilePlaneNormal = 0.0;
float normalOffsetAlongProfilePlaneNormal = 0.0;`), p3.code.add(o`if(!isCapVertex()) {
vec2 rotationRight = vec2(profileRight.w, profileUp.w);
float maxDistance = length(rotationRight);`), p3.code.add(o`rotationRight = maxDistance > 0.0 ? normalize(rotationRight) : vec2(0, 0);
float rx = dot(profileVertex, rotationRight);
if (abs(rx) > maxDistance) {
vec2 rotationUp = vec2(-rotationRight.y, rotationRight.x);
float ry = dot(profileVertex, rotationUp);
profileVertex = rotationRight * maxDistance * sign(rx) + rotationUp * ry;
}
}else{
positionOffsetAlongProfilePlaneNormal = profileRight.w * size[0];
normalOffsetAlongProfilePlaneNormal = profileUp.w;
}
vec3 offset = right * profileVertex.x + up * profileVertex.y + forward * positionOffsetAlongProfilePlaneNormal;
return origin + offset;
}`), p3.code.add(o`vec3 localNormal() {
vec3 right = profileRight.xyz;
vec3 up = profileUp.xyz;
vec3 forward = cross(up, right);
vec2 profileNormal = profileVertexAndNormal.zw;
vec3 normal = right * profileNormal.x + up * profileNormal.y;
if(isCapVertex()) {
normal += forward * profileUp.w;
}
return normal;
}`);
}
var p2 = class extends v2 {
  constructor() {
    super(...arguments), this.size = r(1, 1);
  }
};

// node_modules/@arcgis/core/chunks/Path.glsl.js
function F(F2) {
  const _2 = new o2(), { vertex: M, fragment: O2 } = _2;
  switch (v(M, F2), _2.varyings.add("vpos", "vec3"), _2.include(n3, F2), F2.output !== h.Color && F2.output !== h.Alpha || (_2.include(r2, F2), _2.include(v3, F2), _2.include(d, F2), _2.varyings.add("vnormal", "vec3"), _2.varyings.add("vcolor", "vec4"), F2.hasMultipassTerrain && _2.varyings.add("depth", "float"), M.code.add(o`
      void main() {
        vpos = calculateVPos();
        vnormal = normalize(localNormal());

        ${F2.hasMultipassTerrain ? "depth = (view * vec4(vpos, 1.0)).z;" : ""}
        gl_Position = transformPosition(proj, view, vpos);

        ${F2.output === h.Color ? "forwardLinearDepth();" : ""}

        vcolor = getColor();
      }
    `)), _2.include(n2, F2), F2.output) {
    case h.Alpha:
      _2.include(u, F2), O2.uniforms.add(new o8("opacity", (e6) => e6.opacity)), O2.code.add(o`
      void main() {
        discardBySlice(vpos);
        ${F2.hasMultipassTerrain ? "terrainDepthTest(gl_FragCoord, depth);" : ""}
        float combinedOpacity = vcolor.a * opacity;
        gl_FragColor = vec4(combinedOpacity);
      }
    `);
      break;
    case h.Color:
      _2.include(u, F2), _2.include(p, F2), _2.include(n, F2), _2.include(v3, F2), _2.include(e2, F2), c(O2, F2), h2(O2), u2(O2), O2.uniforms.add([M.uniforms.get("localOrigin"), new e3("ambient", (e6) => e6.ambient), new e3("diffuse", (e6) => e6.diffuse), new e3("specular", (e6) => e6.specular), new o8("opacity", (e6) => e6.opacity)]), O2.include(e5), a2(O2), O2.code.add(o`
        void main() {
          discardBySlice(vpos);
          ${F2.hasMultipassTerrain ? "terrainDepthTest(gl_FragCoord, depth);" : ""}

          shadingParams.viewDirection = normalize(vpos - cameraPosition);
          shadingParams.normalView = vnormal;
          vec3 normal = shadingNormal(shadingParams);
          float ssao = evaluateAmbientOcclusionInverse();

          float additionalAmbientScale = additionalDirectedAmbientLight(vpos + localOrigin);
          vec3 additionalLight = ssao * mainLightIntensity * additionalAmbientScale * ambientBoostFactor * lightingGlobalFactor;
          ${F2.receiveShadows ? "float shadow = readShadowMap(vpos, linearDepth);" : F2.spherical ? "float shadow = lightingGlobalFactor * (1.0 - additionalAmbientScale);" : "float shadow = 0.0;"}
          vec3 albedo = vcolor.rgb * max(ambient, diffuse); // combine the old material parameters into a single one
          float combinedOpacity = vcolor.a * opacity;
          albedo += 0.25 * specular; // don't completely ignore specular for now

          vec3 shadedColor = evaluateSceneLighting(normal, albedo, shadow, 1.0 - ssao, additionalLight);
          gl_FragColor = vec4(shadedColor, combinedOpacity);
          gl_FragColor = highlightSlice(gl_FragColor, vpos);
          ${F2.transparencyPassType === o3.Color ? "gl_FragColor = premultiplyAlpha(gl_FragColor);" : ""}
        }
      `);
      break;
    case h.Depth:
    case h.Shadow:
    case h.ShadowHighlight:
    case h.ShadowExcludeHighlight:
      _2.include(r2, F2), i(_2), _2.varyings.add("depth", "float"), M.code.add(o`void main() {
vpos = calculateVPos();
gl_Position = transformPositionWithDepth(proj, view, vpos, nearFar, depth);
}`), _2.include(u, F2), _2.include(o5, F2), O2.code.add(o`void main() {
discardBySlice(vpos);
outputDepth(depth);
}`);
      break;
    case h.Normal:
      _2.include(r2, F2), _2.include(r3, F2), d2(M), _2.varyings.add("vnormal", "vec3"), M.code.add(o`void main(void) {
vpos = calculateVPos();
vnormal = normalize((viewNormal * vec4(localNormal(), 1.0)).xyz);
gl_Position = transformPosition(proj, view, vpos);
}`), _2.include(u, F2), O2.code.add(o`void main() {
discardBySlice(vpos);
vec3 normal = normalize(vnormal);
if (gl_FrontFacing == false) normal = -normal;
gl_FragColor = vec4(vec3(0.5) + 0.5 * normal, 1.0);
}`);
      break;
    case h.Highlight:
      _2.include(r2, F2), _2.include(r3, F2), _2.varyings.add("vnormal", "vec3"), M.code.add(o`void main(void) {
vpos = calculateVPos();
gl_Position = transformPosition(proj, view, vpos);
}`), _2.include(u, F2), _2.include(a, F2), O2.code.add(o`void main() {
discardBySlice(vpos);
outputHighlight();
}`);
  }
  return _2;
}
var _ = Object.freeze(Object.defineProperty({ __proto__: null, build: F }, Symbol.toStringTag, { value: "Module" }));

export {
  p2 as p,
  F,
  _
};
//# sourceMappingURL=chunk-U55WBNVD.js.map
