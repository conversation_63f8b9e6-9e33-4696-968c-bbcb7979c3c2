{"version": 3, "sources": ["../../@arcgis/core/views/layers/GroupLayerView.js", "../../@arcgis/core/views/2d/layers/GroupLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as i}from\"../../chunks/tslib.es6.js\";import e from\"../../core/Collection.js\";import{referenceSetter as s,castForReferenceSetter as r}from\"../../core/collectionUtils.js\";import{isNone as t}from\"../../core/maybe.js\";import{watch as l,sync as a}from\"../../core/reactiveUtils.js\";import{property as y}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as o}from\"../../core/accessorSupport/decorators/subclass.js\";import h from\"./LayerView.js\";let p=class extends h{constructor(i){super(i),this.type=\"group\",this.layerViews=new e}_allLayerViewVisibility(i){this.layerViews.forEach((e=>{e.visible=i}))}initialize(){this.handles.add([this.layerViews.on(\"change\",(i=>this._layerViewsChangeHandler(i))),l((()=>this.layer.visibilityMode),(()=>this._applyVisibility((()=>this._allLayerViewVisibility(this.visible)),(()=>this._applyExclusiveVisibility(null)))),a),l((()=>this.visible),(i=>{this._applyVisibility((()=>this._allLayerViewVisibility(i)),(()=>{}))}),a)],\"grouplayerview\"),this._layerViewsChangeHandler({target:null,added:this.layerViews.toArray(),removed:[],moved:[]})}set layerViews(i){this._set(\"layerViews\",s(i,this._get(\"layerViews\")))}get updatingProgress(){return 0===this.layerViews.length?1:this.layerViews.reduce(((i,e)=>i+e.updatingProgress),0)/this.layerViews.length}isUpdating(){return this.layerViews.some((i=>i.updating))}_hasLayerViewVisibleOverrides(){return this.layerViews.some((i=>i._isOverridden(\"visible\")))}_findLayerViewForLayer(i){return i&&this.layerViews.find((e=>e.layer===i))}_firstVisibleOnLayerOrder(){const i=this.layer.layers.find((i=>!!this._findLayerViewForLayer(i)?.visible));return i&&this._findLayerViewForLayer(i)}_applyExclusiveVisibility(i){t(i)&&(i=this._firstVisibleOnLayerOrder(),t(i)&&this.layerViews.length>0&&(i=this._findLayerViewForLayer(this.layer.layers.getItemAt(0)))),this.layerViews.forEach((e=>{e.visible=e===i}))}_layerViewsChangeHandler(i){this.handles.remove(\"grouplayerview:visible\"),this.handles.add(this.layerViews.map((i=>l((()=>i.visible),(e=>this._applyVisibility((()=>{e!==this.visible&&(i.visible=this.visible)}),(()=>this._applyExclusiveVisibility(e?i:null)))),a))).toArray(),\"grouplayerview:visible\");const e=i.added[i.added.length-1];this._applyVisibility((()=>this._allLayerViewVisibility(this.visible)),(()=>this._applyExclusiveVisibility(e?.visible?e:null)))}_applyVisibility(i,e){this._hasLayerViewVisibleOverrides()&&(\"inherited\"===this.layer?.visibilityMode?i():\"exclusive\"===this.layer?.visibilityMode&&e())}};i([y({cast:r})],p.prototype,\"layerViews\",null),i([y({readOnly:!0})],p.prototype,\"updatingProgress\",null),i([y()],p.prototype,\"view\",void 0),p=i([o(\"esri.views.layers.GroupLayerView\")],p);const n=p;export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../core/Logger.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import\"../../../core/Error.js\";import\"../../../core/has.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import{GroupContainer as t}from\"../engine/webgl/GroupContainer.js\";import{LayerView2DMixin as s}from\"./LayerView2D.js\";import o from\"../../layers/GroupLayerView.js\";let a=class extends(s(o)){constructor(){super(...arguments),this.container=new t}attach(){this._updateStageChildren(),this.addAttachHandles(this.layerViews.on(\"after-changes\",(()=>this._updateStageChildren())))}detach(){this.container.removeAllChildren()}update(e){}moveStart(){}viewChange(){}moveEnd(){}_updateStageChildren(){this.container.removeAllChildren(),this.layerViews.forEach(((e,r)=>this.container.addChildAt(e.container,r)))}};a=e([r(\"esri.views.2d.layers.GroupLayerView2D\")],a);const i=a;export{i as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIuiB,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,SAAQ,KAAK,aAAW,IAAI;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAE;AAAC,SAAK,WAAW,QAAS,CAAAC,OAAG;AAAC,MAAAA,GAAE,UAAQD;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,QAAQ,IAAI,CAAC,KAAK,WAAW,GAAG,UAAU,CAAAA,OAAG,KAAK,yBAAyBA,EAAC,CAAE,GAAE,EAAG,MAAI,KAAK,MAAM,gBAAiB,MAAI,KAAK,iBAAkB,MAAI,KAAK,wBAAwB,KAAK,OAAO,GAAI,MAAI,KAAK,0BAA0B,IAAI,CAAE,GAAG,CAAC,GAAE,EAAG,MAAI,KAAK,SAAU,CAAAA,OAAG;AAAC,WAAK,iBAAkB,MAAI,KAAK,wBAAwBA,EAAC,GAAI,MAAI;AAAA,MAAC,CAAE;AAAA,IAAC,GAAG,CAAC,CAAC,GAAE,gBAAgB,GAAE,KAAK,yBAAyB,EAAC,QAAO,MAAK,OAAM,KAAK,WAAW,QAAQ,GAAE,SAAQ,CAAC,GAAE,OAAM,CAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAWA,IAAE;AAAC,SAAK,KAAK,cAAa,EAAEA,IAAE,KAAK,KAAK,YAAY,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,MAAI,KAAK,WAAW,SAAO,IAAE,KAAK,WAAW,OAAQ,CAACA,IAAEC,OAAID,KAAEC,GAAE,kBAAkB,CAAC,IAAE,KAAK,WAAW;AAAA,EAAM;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,WAAW,KAAM,CAAAD,OAAGA,GAAE,QAAS;AAAA,EAAC;AAAA,EAAC,gCAA+B;AAAC,WAAO,KAAK,WAAW,KAAM,CAAAA,OAAGA,GAAE,cAAc,SAAS,CAAE;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAE;AAAC,WAAOA,MAAG,KAAK,WAAW,KAAM,CAAAC,OAAGA,GAAE,UAAQD,EAAE;AAAA,EAAC;AAAA,EAAC,4BAA2B;AAAC,UAAMA,KAAE,KAAK,MAAM,OAAO,KAAM,CAAAA,OAAC;AAJ7oD;AAI+oD,cAAC,GAAC,UAAK,uBAAuBA,EAAC,MAA7B,mBAAgC;AAAA,KAAQ;AAAE,WAAOA,MAAG,KAAK,uBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAE;AAAC,MAAEA,EAAC,MAAIA,KAAE,KAAK,0BAA0B,GAAE,EAAEA,EAAC,KAAG,KAAK,WAAW,SAAO,MAAIA,KAAE,KAAK,uBAAuB,KAAK,MAAM,OAAO,UAAU,CAAC,CAAC,KAAI,KAAK,WAAW,QAAS,CAAAC,OAAG;AAAC,MAAAA,GAAE,UAAQA,OAAID;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAE;AAAC,SAAK,QAAQ,OAAO,wBAAwB,GAAE,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAK,CAAAA,OAAG,EAAG,MAAIA,GAAE,SAAU,CAAAC,OAAG,KAAK,iBAAkB,MAAI;AAAC,MAAAA,OAAI,KAAK,YAAUD,GAAE,UAAQ,KAAK;AAAA,IAAQ,GAAI,MAAI,KAAK,0BAA0BC,KAAED,KAAE,IAAI,CAAE,GAAG,CAAC,CAAE,EAAE,QAAQ,GAAE,wBAAwB;AAAE,UAAMC,KAAED,GAAE,MAAMA,GAAE,MAAM,SAAO,CAAC;AAAE,SAAK,iBAAkB,MAAI,KAAK,wBAAwB,KAAK,OAAO,GAAI,MAAI,KAAK,2BAA0BC,MAAA,gBAAAA,GAAG,WAAQA,KAAE,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAEC,IAAE;AAJ/5E;AAIg6E,SAAK,8BAA8B,MAAI,kBAAc,UAAK,UAAL,mBAAY,kBAAeD,GAAE,IAAE,kBAAc,UAAK,UAAL,mBAAY,mBAAgBC,GAAE;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;;;ACA9wE,IAAIC,KAAE,cAAc,EAAEC,EAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,YAAU,IAAI;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,qBAAqB,GAAE,KAAK,iBAAiB,KAAK,WAAW,GAAG,iBAAiB,MAAI,KAAK,qBAAqB,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,UAAU,kBAAkB;AAAA,EAAC;AAAA,EAAC,OAAOC,IAAE;AAAA,EAAC;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,aAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,SAAK,UAAU,kBAAkB,GAAE,KAAK,WAAW,QAAS,CAACA,IAAEC,OAAI,KAAK,UAAU,WAAWD,GAAE,WAAUC,EAAC,CAAE;AAAA,EAAC;AAAC;AAAEH,KAAE,EAAE,CAAC,EAAE,uCAAuC,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["i", "e", "t", "n", "a", "n", "e", "r"]}