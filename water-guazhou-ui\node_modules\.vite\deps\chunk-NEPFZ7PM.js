import {
  h
} from "./chunk-QKWIBVLD.js";
import {
  p as p2,
  p2 as p3
} from "./chunk-YDRLAXYR.js";
import {
  c
} from "./chunk-22FAZXOH.js";
import {
  a as a2,
  l,
  w as w2
} from "./chunk-QUHG7NMD.js";
import {
  n
} from "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  v as v2
} from "./chunk-ZACBBT3Y.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  p
} from "./chunk-63M4K32A.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-4RZONHOY.js";

// node_modules/@arcgis/core/views/layers/support/Geometry.js
var y2;
var c2 = { base: p, key: "type", typeMap: { extent: w, polygon: v } };
var n2 = y2 = class extends p2 {
  constructor(r) {
    super(r), this.type = "geometry", this.geometry = null;
  }
  clone() {
    var _a;
    return new y2({ geometry: ((_a = this.geometry) == null ? void 0 : _a.clone()) ?? null });
  }
  commitVersionProperties() {
    this.commitProperty("geometry");
  }
};
e([y({ types: c2, json: { read: v2, write: true } })], n2.prototype, "geometry", void 0), n2 = y2 = e([a("esri.views.layers.support.Geometry")], n2);
var a3 = n2;

// node_modules/@arcgis/core/views/layers/support/Path.js
var e2 = class extends p2 {
  constructor(r) {
    super(r), this.type = "path", this.path = [];
  }
  commitVersionProperties() {
    this.commitProperty("path");
  }
};
e([y({ type: [[[Number]]], json: { write: true } })], e2.prototype, "path", void 0), e2 = e([a("esri.views.layers.support.Path")], e2);
var p4 = e2;

// node_modules/@arcgis/core/views/2d/layers/LayerView2D.js
var y3 = j.ofType({ key: "type", base: null, typeMap: { rect: p3, path: p4, geometry: a3 } });
var f = (t) => {
  let l2 = class extends t {
    constructor() {
      super(...arguments), this.attached = false, this.clips = new y3(), this.lastUpdateId = -1, this.moving = false, this.updateRequested = false, this.visibleAtCurrentScale = false, this.highlightOptions = null;
    }
    initialize() {
      var _a, _b, _c;
      const e3 = ((_a = this.view) == null ? void 0 : _a.spatialReferenceLocked) ?? true, t2 = (_b = this.view) == null ? void 0 : _b.spatialReference;
      t2 && e3 && !this.spatialReferenceSupported ? this.addResolvingPromise(Promise.reject(new s("layerview:spatial-reference-incompatible", "The spatial reference of this layer does not meet the requirements of the view", { layer: this.layer }))) : (this.container || (this.container = new h()), this.container.fadeTransitionEnabled = true, this.container.visible = false, this.container.endTransitions(), this.addHandles([l(() => this.suspended, (e4) => {
        this.container && (this.container.visible = !e4), this.view && !e4 && this.updateRequested && this.view.requestUpdate();
      }, w2), l(() => {
        var _a2;
        return ((_a2 = this.layer) == null ? void 0 : _a2.opacity) ?? 1;
      }, (e4) => {
        this.container && (this.container.opacity = e4);
      }, w2), l(() => this.layer && "blendMode" in this.layer ? this.layer.blendMode : "normal", (e4) => {
        this.container && (this.container.blendMode = e4);
      }, w2), l(() => this.layer && "effect" in this.layer ? this.layer.effect : null, (e4) => {
        this.container && (this.container.effect = e4);
      }, w2), l(() => this.highlightOptions, (e4) => this.container.highlightOptions = e4, w2), a2(() => this.clips, "change", () => {
        this.container && (this.container.clips = this.clips);
      }, w2), l(() => {
        var _a2;
        return { scale: (_a2 = this.view) == null ? void 0 : _a2.scale, scaleRange: this.layer && "effectiveScaleRange" in this.layer ? this.layer.effectiveScaleRange : null };
      }, ({ scale: e4 }) => {
        const t3 = null != e4 && this.isVisibleAtScale(e4);
        t3 !== this.visibleAtCurrentScale && this._set("visibleAtCurrentScale", t3);
      }, w2)], "constructor"), ((_c = this.view) == null ? void 0 : _c.whenLayerView) ? this.view.whenLayerView(this.layer).then((e4) => {
        e4 === this && this.processAttach();
      }, () => {
      }) : this.when().then(() => {
        this.processAttach();
      }, () => {
      }));
    }
    destroy() {
      this.processDetach(), this.updateRequested = false;
    }
    get spatialReferenceSupported() {
      var _a;
      const e3 = (_a = this.view) == null ? void 0 : _a.spatialReference;
      return null == e3 || this.supportsSpatialReference(e3);
    }
    get updating() {
      var _a;
      return this.spatialReferenceSupported && (!this.attached || !this.suspended && (this.updateRequested || this.isUpdating()) || !!((_a = this.updatingHandles) == null ? void 0 : _a.updating));
    }
    processAttach() {
      this.isResolved() && !this.attached && !this.destroyed && this.spatialReferenceSupported && (this.attach(), this.attached = true, this.requestUpdate());
    }
    processDetach() {
      this.attached && (this.attached = false, this.removeHandles("attach"), this.detach(), this.updateRequested = false);
    }
    isVisibleAtScale(e3) {
      const t2 = this.layer && "effectiveScaleRange" in this.layer ? this.layer.effectiveScaleRange : null;
      if (!t2) return true;
      const { minScale: s2, maxScale: i } = t2;
      return (0 === s2 || e3 <= s2) && (0 === i || e3 >= i);
    }
    requestUpdate() {
      this.destroyed || this.updateRequested || (this.updateRequested = true, this.suspended || this.view.requestUpdate());
    }
    processUpdate(e3) {
      !this.isFulfilled() || this.isResolved() ? (this._set("updateParameters", e3), this.updateRequested && !this.suspended && (this.updateRequested = false, this.update(e3))) : this.updateRequested = false;
    }
    hitTest(e3, t2) {
      return Promise.resolve(null);
    }
    supportsSpatialReference(e3) {
      return true;
    }
    canResume() {
      return !!this.spatialReferenceSupported && (!!super.canResume() && this.visibleAtCurrentScale);
    }
    getSuspendInfo() {
      const e3 = super.getSuspendInfo(), t2 = !this.spatialReferenceSupported, s2 = this.visibleAtCurrentScale;
      return t2 && (e3.spatialReferenceNotSupported = t2), s2 && (e3.outsideScaleRange = s2), e3;
    }
    addAttachHandles(e3) {
      this.addHandles(e3, "attach");
    }
  };
  return e([y()], l2.prototype, "attached", void 0), e([y({ type: y3, set(e3) {
    const t2 = n(e3, this._get("clips"), y3);
    this._set("clips", t2);
  } })], l2.prototype, "clips", void 0), e([y()], l2.prototype, "container", void 0), e([y()], l2.prototype, "moving", void 0), e([y({ readOnly: true })], l2.prototype, "spatialReferenceSupported", null), e([y({ readOnly: true })], l2.prototype, "updateParameters", void 0), e([y()], l2.prototype, "updateRequested", void 0), e([y()], l2.prototype, "updating", null), e([y()], l2.prototype, "view", void 0), e([y({ readOnly: true })], l2.prototype, "visibleAtCurrentScale", void 0), e([y({ type: c })], l2.prototype, "highlightOptions", void 0), l2 = e([a("esri.views.2d.layers.LayerView2D")], l2), l2;
};

export {
  f
};
//# sourceMappingURL=chunk-NEPFZ7PM.js.map
