import {
  M as M5
} from "./chunk-NVZWP4JL.js";
import {
  _ as _8,
  p as p15
} from "./chunk-U55WBNVD.js";
import {
  T as T5,
  a as a23
} from "./chunk-QZXCL7E5.js";
import {
  $ as $2,
  $2 as $3,
  A as A5,
  B as B2,
  C as C2,
  C2 as C3,
  E as E12,
  G as G4,
  I as I5,
  I2 as I6,
  S as S5,
  T as T4,
  U as U3,
  U2 as U4,
  V as V5,
  X,
  a2 as a20,
  a3 as a21,
  a4 as a22,
  b as b11,
  c2 as c15,
  c3 as c16,
  ce,
  d as d10,
  d2 as d11,
  e as e16,
  e2 as e18,
  e3 as e19,
  et,
  f as f15,
  f2 as f16,
  f3 as f17,
  f4 as f18,
  g as g7,
  h as h14,
  h2 as h15,
  i as i10,
  i2 as i11,
  it,
  j as j8,
  k as k4,
  l as l19,
  l2 as l20,
  lt,
  m as m7,
  n as n14,
  ot as ot2,
  p as p11,
  p2 as p12,
  p3 as p14,
  q as q4,
  q2 as q5,
  s as s13,
  t as t18,
  t2 as t19,
  tt,
  u as u14,
  ut,
  v as v10,
  wt,
  x as x7,
  x2 as x8,
  z as z3
} from "./chunk-WHHWXCFL.js";
import "./chunk-OHIQTOTV.js";
import "./chunk-LGTBT7TM.js";
import "./chunk-ILZ6UF5N.js";
import {
  u as u16
} from "./chunk-22ERJX3T.js";
import {
  p as p13
} from "./chunk-7GO2KORS.js";
import {
  r as r19
} from "./chunk-J2CRYUH3.js";
import {
  a as a18,
  f as f14,
  p as p10,
  s as s12
} from "./chunk-A4XIY547.js";
import {
  a as a19,
  e as e17,
  o as o16
} from "./chunk-M3F5NDOQ.js";
import "./chunk-5HSEQ7GU.js";
import "./chunk-V57FPENY.js";
import "./chunk-XDK46HA6.js";
import "./chunk-U7VYWSSV.js";
import {
  a as a17
} from "./chunk-42I23CQ5.js";
import {
  i as i5
} from "./chunk-BI4P4NAQ.js";
import "./chunk-VYWZHTOQ.js";
import {
  U as U2,
  V as V4
} from "./chunk-PTYT4A5P.js";
import {
  E as E10,
  E2 as E11,
  c as c12,
  te
} from "./chunk-V27ZN6IB.js";
import {
  E as E9,
  O as O7,
  P as P5,
  a as a15,
  b as b10,
  e as e15,
  f as f12,
  g as g5,
  i as i8,
  l as l18,
  m as m6,
  o2 as o11,
  r as r14,
  r2 as r17,
  s as s9,
  s2 as s10,
  v as v8
} from "./chunk-NEOCII5B.js";
import "./chunk-7GPM2ZU5.js";
import {
  s as s11
} from "./chunk-FQZM46ZM.js";
import {
  G as G3
} from "./chunk-DXGQBULN.js";
import "./chunk-KETK5W7H.js";
import "./chunk-PI5OC2DP.js";
import {
  A as A4,
  E as E8,
  S as S4,
  _ as _7,
  a as a14,
  c as c14,
  l as l17
} from "./chunk-LGZKVOWE.js";
import "./chunk-SKYLCTPX.js";
import {
  b as b9,
  v as v7
} from "./chunk-FQMXSCOG.js";
import "./chunk-4ZR6RUFJ.js";
import {
  i as i7
} from "./chunk-TKIS7RZA.js";
import "./chunk-VVOL4DVY.js";
import "./chunk-ZSGF3UOS.js";
import "./chunk-ZKWNW26M.js";
import "./chunk-MNPAHSMX.js";
import {
  e as e14,
  o as o10,
  t as t16
} from "./chunk-QLHJUIIZ.js";
import "./chunk-TDGDXUFC.js";
import "./chunk-C2ZE76VJ.js";
import "./chunk-F26DNX7C.js";
import {
  r as r18
} from "./chunk-KVEZ26WH.js";
import {
  E as E5,
  E2 as E7,
  V as V3,
  c as c13,
  e as e13,
  h as h12,
  r2 as r16,
  u as u11,
  v as v9,
  y as y5
} from "./chunk-TOYJMVHA.js";
import "./chunk-Q6BEUTMN.js";
import "./chunk-BOT4BSSB.js";
import {
  d2 as d8,
  h as h11,
  t as t15
} from "./chunk-XVTFHFM3.js";
import "./chunk-QB6AUIQ2.js";
import "./chunk-REGYRSW7.js";
import "./chunk-PIQKEGGB.js";
import "./chunk-Y424ZXTG.js";
import "./chunk-UB5FTTH5.js";
import "./chunk-6GW7M2AQ.js";
import "./chunk-ND2RJTSZ.js";
import {
  h as h10
} from "./chunk-L4Y6W6Y5.js";
import "./chunk-IRHOIB3A.js";
import "./chunk-N3S5O3YO.js";
import "./chunk-JETZLJ6M.js";
import "./chunk-32BGXH4N.js";
import "./chunk-VK7XO5DN.js";
import {
  W,
  _ as _6,
  a as a13,
  h as h13,
  l as l16,
  o as o9
} from "./chunk-WKBMFG6J.js";
import {
  o as o8
} from "./chunk-BPRRRPC3.js";
import {
  E as E4,
  E2 as E6,
  R as R6
} from "./chunk-6G2NLXT7.js";
import "./chunk-3DGZE3NI.js";
import "./chunk-SARDHCB4.js";
import "./chunk-YKLDBJ7V.js";
import "./chunk-HTESBPT2.js";
import "./chunk-GXMOAZWH.js";
import "./chunk-WL2F66AK.js";
import "./chunk-6OHWWYET.js";
import "./chunk-TUB4N6LD.js";
import "./chunk-NLDHTNKF.js";
import "./chunk-YV4RKNU4.js";
import "./chunk-LHO3WKNH.js";
import "./chunk-RFTQI4ZD.js";
import {
  A as A2,
  a as a12,
  f as f10
} from "./chunk-CK22CKHH.js";
import {
  s as s7
} from "./chunk-IUNR7SKI.js";
import {
  n as n12,
  o as o7
} from "./chunk-3CEVKZPD.js";
import "./chunk-UHA44FM7.js";
import "./chunk-BLTZUGC7.js";
import {
  t as t13
} from "./chunk-F4KVXA42.js";
import {
  D as D4,
  n as n11,
  t as t12,
  u as u10
} from "./chunk-6ZZUUGXX.js";
import {
  a as a11,
  f as f9,
  l as l13,
  n as n10,
  t as t11
} from "./chunk-OMPEYGMV.js";
import {
  g as g4
} from "./chunk-YGFDIZJP.js";
import {
  c as c11
} from "./chunk-GJMH67CL.js";
import {
  i as i2
} from "./chunk-TZ4YPYDT.js";
import "./chunk-TMGUQ6KD.js";
import {
  o as o4
} from "./chunk-BWWOCIFU.js";
import {
  T as T2
} from "./chunk-QW426QEA.js";
import "./chunk-IEBU4QQL.js";
import {
  O as O4
} from "./chunk-CPQSD22U.js";
import "./chunk-MNYWPBDW.js";
import "./chunk-ZVJXF3ML.js";
import {
  n as n8,
  o as o5
} from "./chunk-SKIEIN3S.js";
import {
  O as O5,
  V,
  j as j6,
  k as k2,
  v as v5
} from "./chunk-B42W3AOR.js";
import {
  r as r10,
  t as t7
} from "./chunk-UQUDWTCY.js";
import "./chunk-IKOX2HGY.js";
import {
  h as h7
} from "./chunk-CCFNWAA2.js";
import {
  T,
  b as b3,
  c as c6,
  d as d3,
  h as h8,
  i as i4,
  l as l10,
  u as u7,
  x as x4
} from "./chunk-3KCCETWY.js";
import "./chunk-UYVDPJKH.js";
import {
  s as s8
} from "./chunk-EM4JSU7Z.js";
import {
  u as u12
} from "./chunk-HWB4LNSZ.js";
import "./chunk-FTRLEBHJ.js";
import {
  r as r12
} from "./chunk-PWCXATLS.js";
import {
  D as D3,
  F as F4,
  I as I3,
  R as R3
} from "./chunk-4M3AMTD4.js";
import {
  s as s14,
  u as u15
} from "./chunk-Y5QAWFQI.js";
import {
  f as f13,
  o as o15
} from "./chunk-JF4IAAZV.js";
import "./chunk-57ER3SHX.js";
import "./chunk-FRO3RSRO.js";
import {
  t as t9
} from "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import {
  r as r15
} from "./chunk-QCTKOQ44.js";
import {
  a as a9,
  g as g3,
  o as o6,
  u as u8
} from "./chunk-ST2RRB55.js";
import {
  d2 as d9
} from "./chunk-6KZTVN32.js";
import "./chunk-ANH6666P.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import {
  o as o14
} from "./chunk-RVGLVPCD.js";
import {
  t as t17
} from "./chunk-WFNEQMBV.js";
import {
  u as u13
} from "./chunk-TL5Y53I4.js";
import {
  A as A3,
  O as O6,
  P as P4,
  V as V2,
  Y as Y2,
  _ as _5,
  b as b8,
  l as l14,
  p as p8,
  p2 as p9,
  x as x6
} from "./chunk-6IU6DQRF.js";
import {
  R as R5,
  k2 as k3,
  p as p7,
  w as w6
} from "./chunk-YELYN22P.js";
import "./chunk-ZIKXCGU7.js";
import "./chunk-XSQFM27N.js";
import {
  e as e11,
  r as r9
} from "./chunk-QYOAH6AO.js";
import {
  e as e10
} from "./chunk-A7PY25IH.js";
import {
  l as l15
} from "./chunk-JJ3NE6DY.js";
import {
  D as D2,
  I as I2,
  P as P3,
  R as R2
} from "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import {
  e as e8,
  t as t6
} from "./chunk-6DAQTVXB.js";
import "./chunk-2RO3UJ2R.js";
import {
  a as a16
} from "./chunk-CDZ24ELJ.js";
import {
  I as I4,
  M as M4,
  R as R4,
  b as b4,
  v as v6,
  z as z2
} from "./chunk-VHLK35TF.js";
import "./chunk-KXA6I5TQ.js";
import {
  o as o13
} from "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import {
  _ as _4,
  j as j5,
  l as l9,
  o as o3,
  q as q3,
  r as r8,
  s as s6,
  v as v4
} from "./chunk-SROTSYJS.js";
import {
  e as e12,
  f as f11,
  n as n13,
  r as r13,
  t as t14
} from "./chunk-FOE4ICAJ.js";
import {
  t as t8
} from "./chunk-P2G4OGHI.js";
import "./chunk-TTEJD6GB.js";
import "./chunk-J4KDDSED.js";
import {
  ct
} from "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import {
  s as s4,
  t as t4
} from "./chunk-SEO6KEGF.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import "./chunk-ZJC3GHA7.js";
import "./chunk-HTXGAKOK.js";
import {
  b as b2,
  c as c4
} from "./chunk-P37TUI4J.js";
import {
  An,
  Bn,
  Hn,
  Zn,
  ee,
  gn,
  jn,
  pn,
  vn,
  xn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import {
  x as x5
} from "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  d as d4
} from "./chunk-Q4VCSCSY.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import {
  c as c5,
  e as e7,
  f as f6,
  h as h6,
  i,
  l as l8,
  m as m2,
  n as n7,
  p as p6,
  q as q2,
  r as r7,
  s as s5,
  x as x3
} from "./chunk-YEODPCXQ.js";
import {
  b as b5
} from "./chunk-FBVKALLT.js";
import "./chunk-BS3GJQ77.js";
import {
  T as T3
} from "./chunk-IOBN373Z.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-NEJXVYTI.js";
import "./chunk-64RWCMSJ.js";
import {
  d as d5,
  m as m4
} from "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import {
  a as a10,
  c as c10,
  u as u9
} from "./chunk-FIVMDF4P.js";
import {
  e as e9,
  i as i3
} from "./chunk-4GVJIP3E.js";
import {
  _ as _3,
  l as l7,
  n as n6,
  r as r6,
  t as t5
} from "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import {
  r as r11
} from "./chunk-3WUI7ZKG.js";
import {
  U,
  a as a8,
  f as f5,
  h as h5,
  j as j4,
  l as l6,
  w as w5
} from "./chunk-QUHG7NMD.js";
import "./chunk-ZL6CFFJK.js";
import "./chunk-TWFTBWXP.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import {
  S as S3
} from "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import {
  N
} from "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import {
  n as n9
} from "./chunk-CCAF47ZU.js";
import {
  m as m3
} from "./chunk-3WEGNHPY.js";
import {
  g as g6
} from "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import {
  S as S2,
  b as b6,
  b2 as b7,
  c as c7,
  c2 as c9,
  d as d7,
  f as f7,
  f2 as f8,
  h as h9,
  i as i6,
  j as j7,
  l2 as l12
} from "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import {
  c as c8,
  t as t10
} from "./chunk-762DBG4V.js";
import {
  d as d6
} from "./chunk-ADTC77YB.js";
import {
  B,
  E as E3,
  F as F3,
  G as G2,
  H as H2,
  I,
  M as M3,
  O as O3,
  P as P2,
  S,
  Y,
  Z,
  _ as _2,
  a as a7,
  c as c3,
  d as d2,
  f as f4,
  h as h4,
  k,
  p as p5,
  u as u5,
  y as y4
} from "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import {
  i as i9,
  o as o12
} from "./chunk-57XIOVP5.js";
import {
  D,
  M as M2,
  R,
  a as a6,
  b,
  h as h3,
  m,
  p as p4,
  u as u4,
  y as y3
} from "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j as j2
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import {
  n as n4
} from "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import {
  c,
  u as u2,
  v as v3
} from "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w4
} from "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import {
  w as w3
} from "./chunk-63M4K32A.js";
import {
  M
} from "./chunk-R5MYQRRS.js";
import {
  $,
  E as E2,
  L,
  O,
  f2
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import {
  m3 as m5
} from "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import {
  ot
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import {
  e as e6,
  u as u6
} from "./chunk-G5KX4JSG.js";
import {
  l as l11
} from "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import {
  F as F2,
  a as a5,
  h as h2,
  l as l5
} from "./chunk-EIGTETCG.js";
import {
  G,
  H,
  O as O2,
  P,
  _,
  c as c2,
  e as e5,
  g as g2,
  j as j3,
  o as o2,
  q,
  r as r4,
  s as s3,
  u as u3,
  x as x2,
  z
} from "./chunk-MQAXMQFG.js";
import {
  e as e4,
  f as f3,
  l as l3,
  n as n5,
  r as r2
} from "./chunk-36FLFRUE.js";
import {
  a as a4,
  l as l4,
  r as r5
} from "./chunk-RQXGVG3K.js";
import {
  a as a3,
  r as r3
} from "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  t2 as t3,
  v as v2
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y as y2
} from "./chunk-JN4FSB7Y.js";
import {
  n as n2,
  t as t2
} from "./chunk-HP475EI3.js";
import {
  l as l2,
  v
} from "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import {
  e as e3
} from "./chunk-TUM6KUQZ.js";
import {
  n as n3
} from "./chunk-2CM7MIII.js";
import {
  A,
  C,
  E,
  d,
  f,
  j,
  p as p3,
  w as w2
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has,
  p as p2
} from "./chunk-REW33H3I.js";
import {
  F,
  u
} from "./chunk-GZGAQUSK.js";
import {
  a,
  e as e2,
  g,
  h,
  l,
  n,
  o,
  p,
  r,
  t,
  w,
  x,
  y
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/3d/layers/LayerView3D.js
var n15 = (n26) => {
  let c34 = class extends n26 {
    constructor() {
      super(...arguments), this.slicePlaneEnabled = false, this.supportsHeightUnitConversion = false;
    }
    postscript(e33) {
      super.postscript(e33), f13(this.layer) && this.addResolvingPromise(this._validateHeightModelInfo());
    }
    async _validateHeightModelInfo() {
      const e33 = new AbortController(), o24 = e33.signal;
      this.handles.add(n3(() => e33.abort())), await j4(() => {
        var _a;
        return (_a = this.view.defaultsFromMap) == null ? void 0 : _a.heightModelInfoReady;
      }, o24), f(o24);
      const i19 = o15(this.layer, this.view.heightModelInfo, this.supportsHeightUnitConversion);
      if (i19) throw i19;
    }
    canResume() {
      const e33 = this.layer && "effectiveScaleRange" in this.layer ? this.layer.effectiveScaleRange : null;
      return super.canResume() && (!e33 || !e33.minScale || !e33.maxScale || e33.minScale >= e33.maxScale);
    }
    getSuspendInfo() {
      const e33 = super.getSuspendInfo(), s26 = this.layer && "effectiveScaleRange" in this.layer ? this.layer.effectiveScaleRange : null;
      return s26 && s26.minScale && s26.maxScale && s26.minScale < s26.maxScale && (e33.outsideScaleRange = true), e33;
    }
  };
  return e([y2()], c34.prototype, "view", void 0), e([y2()], c34.prototype, "slicePlaneEnabled", void 0), c34 = e([a2("esri.views.3d.layers.LayerView3D")], c34), c34;
};

// node_modules/@arcgis/core/views/3d/layers/graphics/elevationAlignPointsInFeatures.js
async function m8(t28, n26, i19, f27, m18) {
  const { elevationProvider: j12, renderCoordsHelper: u32, spatialReference: y14 } = t28, { elevationInfo: v21 } = n26, I13 = d11(v21, true), g23 = await a21(I13, y14, m18);
  f(m18);
  const x18 = [], h25 = /* @__PURE__ */ new Set(), w12 = /* @__PURE__ */ new Set();
  for (const { objectId: o24, points: a35 } of f27) {
    const p23 = i19(o24);
    if (t(p23)) {
      for (const e33 of a35) x18.push(e33[2]);
      h25.add(o24);
      continue;
    }
    p23.isDraped && w12.add(o24);
    const f28 = p23.graphic.geometry;
    c17.setFromElevationInfo(a16(f28, v21)), c17.updateFeatureExpressionInfoContext(g23, p23.graphic, n26), l21.spatialReference = t28.spatialReference;
    for (const { x: e33, y: o25, z: t29 } of a35) l21.x = e33, l21.y = o25, l21.z = t29 ?? 0, f16(l21, j12, c17, u32, d12), x18.push(d12.z);
  }
  return { elevations: x18, drapedObjectIds: w12, failedObjectIds: h25 };
}
var c17 = new h15();
var l21 = M4(0, 0, 0, f2.WGS84);
var d12 = new T4();

// node_modules/@arcgis/core/renderers/support/renderingInfoUtils.js
function i12(e33, o24) {
  if (!e33 || e33.symbol) return null;
  const r30 = o24 && o24.renderer;
  return e33 && r(r30) && r30.getObservationRenderer ? r30.getObservationRenderer(e33) : r30;
}
function n16(e33, o24) {
  if (r(e33.symbol)) return e33.symbol;
  const r30 = i12(e33, o24);
  return r(r30) && "dot-density" !== r30.type ? r30.getSymbol(e33, o24) : null;
}
function a24(e33, t28) {
  const a35 = i12(e33, t28), l34 = n16(e33, t28);
  if (t(l34)) return null;
  const s26 = { renderer: a35, symbol: l34 };
  if (t(a35) || !("visualVariables" in a35) || !a35.visualVariables) return s26;
  const u32 = N(a35, e33, t28) ?? [], c34 = ["proportional", "proportional", "proportional"];
  for (const { variable: o24, value: r30 } of u32) switch (o24.type) {
    case "color":
      s26.color = r30.toRgba();
      break;
    case "size":
      if ("outline" === o24.target) s26.outlineSize = r30;
      else {
        const e34 = o24.axis, t29 = o24.useSymbolValue ? "symbol-value" : r30;
        switch (e34) {
          case "width":
            c34[0] = t29;
            break;
          case "depth":
            c34[1] = t29;
            break;
          case "height":
            c34[2] = t29;
            break;
          case "width-and-depth":
            c34[0] = c34[1] = t29;
            break;
          default:
            c34[0] = c34[1] = c34[2] = t29;
        }
      }
      break;
    case "opacity":
      s26.opacity = r30;
      break;
    case "rotation":
      switch (o24.axis) {
        case "tilt":
          s26.tilt = r30;
          break;
        case "roll":
          s26.roll = r30;
          break;
        default:
          s26.heading = r30;
      }
  }
  return "proportional" === c34[0] && "proportional" === c34[1] && "proportional" === c34[2] || (s26.size = c34), s26;
}
async function l22(e33, o24) {
  if (r(e33.symbol)) return e33.symbol;
  const r30 = i12(e33, o24);
  return r(r30) ? r30.getSymbolAsync(e33, o24) : null;
}
async function s15(t28, o24) {
  const n26 = i12(t28, o24), a35 = await l22(t28, o24);
  if (!a35) return null;
  const s26 = { renderer: n26, symbol: a35 };
  if (!n26 || !("visualVariables" in n26) || !n26.visualVariables) return s26;
  const u32 = N(n26, t28, o24) ?? [], c34 = ["proportional", "proportional", "proportional"];
  for (const { variable: r30, value: i19 } of u32) if ("color" === r30.type) s26.color = l11.toUnitRGBA(i19);
  else if ("size" === r30.type) if ("outline" === r30.target) s26.outlineSize = i19;
  else {
    const e33 = r30.axis, t29 = r30.useSymbolValue ? "symbol-value" : i19;
    "width" === e33 ? c34[0] = t29 : "depth" === e33 ? c34[1] = t29 : "height" === e33 ? c34[2] = t29 : c34[0] = c34[1] = "width-and-depth" === e33 ? t29 : c34[2] = t29;
  }
  else "opacity" === r30.type ? s26.opacity = i19 : "rotation" === r30.type && "tilt" === r30.axis ? s26.tilt = i19 : "rotation" === r30.type && "roll" === r30.axis ? s26.roll = i19 : "rotation" === r30.type && (s26.heading = i19);
  return (isFinite(c34[0]) || isFinite(c34[1]) || isFinite(c34[2])) && (s26.size = c34), s26;
}
function u17(e33, t28 = 0) {
  const o24 = e33[t28];
  return "number" == typeof o24 && isFinite(o24) ? o24 : null;
}
function c18(e33) {
  for (let t28 = 0; t28 < 3; t28++) {
    const o24 = e33[t28];
    if ("number" == typeof o24) return isFinite(o24) ? o24 : 0;
  }
  return 0;
}

// node_modules/@arcgis/core/views/3d/layers/graphics/constants.js
var s16 = 1.2;
var c19 = l7;

// node_modules/@arcgis/core/symbols/support/defaults3D.js
var a25 = h9.fromSimpleMarkerSymbol(c10);
var c20 = b7.fromSimpleLineSymbol(u9);
var S6 = S2.fromSimpleFillSymbol(a10);
var u18 = new c9({ symbolLayers: [new f7({ material: { color: e9 }, edges: new c7({ size: "1px", color: i3 }) })] });
function b12(r30) {
  if (t(r30)) return null;
  switch (r30.type) {
    case "mesh":
      return u18;
    case "point":
    case "multipoint":
      return a25;
    case "polyline":
      return c20;
    case "polygon":
    case "extent":
      return S6;
  }
  return null;
}

// node_modules/@arcgis/core/views/3d/layers/graphics/ElevationQuery.js
var a27 = class {
  constructor(e33, t28) {
    this.spatialReference = e33, this._view = t28;
  }
  getElevation(e33, t28, r30) {
    return this._view.elevationProvider.getElevation(e33, t28, 0, this.spatialReference, r30);
  }
  async queryElevation(e33, t28, r30, s26, i19) {
    return this._view.elevationProvider.queryElevation(e33, t28, 0, this.spatialReference, i19, r30, s26);
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/enums.js
var E13;
var C4;
!function(E21) {
  E21[E21.GRAPHIC = 0] = "GRAPHIC", E21[E21.LABEL = 1] = "LABEL", E21[E21._COUNT = 2] = "_COUNT";
}(E13 || (E13 = {})), function(E21) {
  E21[E21.USER_SETTING = 0] = "USER_SETTING", E21[E21.SCALE_RANGE = 1] = "SCALE_RANGE", E21[E21.FILTER = 2] = "FILTER", E21[E21.DECONFLICTION = 3] = "DECONFLICTION", E21[E21._COUNT = 4] = "_COUNT";
}(C4 || (C4 = {}));

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DFeatureStore.js
var l23 = a7();
var m9 = class extends v2 {
  constructor(t28) {
    super(t28), this.events = new n4(), this.hasZ = null, this.hasM = null, this.objectIdField = null, this.featureAdapter = { getAttribute: (t29, e33) => "graphic" in t29 ? t29.graphic.attributes[e33] : i2.getAttribute(t29, e33), getAttributes: (t29) => "graphic" in t29 ? t29.graphic.attributes : i2.getAttributes(t29), getObjectId: (t29) => "graphic" in t29 ? R4(t29.graphic, this.objectIdField) ?? void 0 : i2.getObjectId(t29), getGeometry: (t29) => "graphic" in t29 ? t29.getAsOptimizedGeometry(this.hasZ, this.hasM) : i2.getGeometry(t29), getCentroid: (t29, e33) => {
      if ("graphic" in t29) {
        let r30 = null;
        r(t29.centroid) ? r30 = t29.centroid : "point" === t29.graphic.geometry.type && pn(t29.graphic.geometry, g8, this.viewSpatialReference) && (r30 = g8);
        const i19 = new Array(2 + (e33.hasZ ? 1 : 0) + (e33.hasM ? 1 : 0));
        return t(r30) ? (i19[0] = 0, i19[1] = 0, i19[2] = 0, i19[3] = 0) : (i19[0] = r30.x, i19[1] = r30.y, e33.hasZ && (i19[2] = r30.hasZ ? r30.z : 0), e33.hasM && (i19[e33.hasZ ? 3 : 2] = r30.hasM ? r30.m : 0)), new t4([], i19);
      }
      return i2.getCentroid(t29, e33);
    }, cloneWithGeometry: (t29, e33) => "graphic" in t29 ? new s4(e33, this.featureAdapter.getAttributes(t29), null, this.featureAdapter.getObjectId(t29)) : i2.cloneWithGeometry(t29, e33) };
  }
  forEachInBounds(t28, e33) {
    this.getSpatialIndex().forEachInBounds(t28, e33);
  }
  forEachBounds(t28, e33) {
    const r30 = this.getSpatialIndex();
    for (const s26 of t28) {
      const t29 = this.featureAdapter.getObjectId(s26);
      r(r30.getBounds(t29, l23)) && e33(l23);
    }
  }
};
e([y2({ constructOnly: true })], m9.prototype, "getSpatialIndex", void 0), e([y2({ constructOnly: true })], m9.prototype, "forEach", void 0), e([y2({ constructOnly: true })], m9.prototype, "hasZ", void 0), e([y2({ constructOnly: true })], m9.prototype, "hasM", void 0), e([y2({ constructOnly: true })], m9.prototype, "objectIdField", void 0), e([y2({ constructOnly: true })], m9.prototype, "viewSpatialReference", void 0), e([y2({ constructOnly: true })], m9.prototype, "featureSpatialReference", void 0), m9 = e([a2("esri.views.3d.layers.graphics.Graphics3DFeatureStore")], m9);
var g8 = { type: "point", x: 0, y: 0, hasZ: false, hasM: false, spatialReference: null };

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DGraphicCreationContext.js
var r20 = class {
  constructor(r30, s26, t28) {
    this.graphic = r30, this.renderingInfo = s26, this.layer = t28;
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DSymbolCreationContext.js
var e20 = class {
  constructor(e33, s26) {
    this.scheduler = e33, this.schedule = s26, this.sharedResources = null, this.streamDataRequester = null, this.elevationProvider = null, this.renderer = null, this.stage = null, this.clippingExtent = null, this.renderCoordsHelper = null, this.overlaySR = null, this.layer = null, this.drapeSourceRenderer = null, this.graphicsCoreOwner = null, this.localOriginFactory = null, this.featureExpressionInfoContext = null, this.screenSizePerspectiveEnabled = true, this.slicePlaneEnabled = false, this.physicalBasedRenderingEnabled = false, this.skipHighSymbolLods = false, this.isAsync = false;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/GeometryWithMapPositions.js
function t20(r30) {
  return r(r30.mapPositions);
}

// node_modules/@arcgis/core/views/3d/layers/graphics/ElevationAligners.js
function p16(t28, e33, o24, n26, a35) {
  const r30 = t28.stageObject, s26 = r30.geometries;
  let i19 = 0;
  for (const l34 of s26) {
    if (!t20(l34)) continue;
    const { update: t29, averageGeometrySampledElevation: s27 } = O8(l34, e33, o24, n26, a35);
    i19 += s27, t29 && r30.geometryVertexAttrsUpdated(l34);
  }
  return i19 / s26.length;
}
function d13(t28, o24, a35, s26, i19) {
  var _a;
  const c34 = t28.stageObject, f27 = o24.centerPointInElevationSR;
  let u32 = 0;
  if ((_a = c34.metadata) == null ? void 0 : _a.usesVerticalDistanceToGround) s26(f27, A6), U4(c34, A6.verticalDistanceToGround), u32 = A6.sampledElevation;
  else {
    s26(f27, A6);
    "absolute-height" !== o24.mode && (u32 = A6.sampledElevation);
  }
  const p23 = n7(g9, c34.transformation), d26 = o2(M6, p23[12], p23[13], p23[14]);
  t17.TESTS_DISABLE_OPTIMIZATIONS ? (h16[0] = f27.x, h16[1] = f27.y, h16[2] = A6.z, Zn(f27.spatialReference, h16, p23, i19.spatialReference) && (c34.transformation = p23)) : i19.setAltitudeOfTransformation(A6.z, p23);
  const b24 = T6 / i19.unitInMeters;
  return (Math.abs(p23[12] - d26[0]) >= b24 || Math.abs(p23[13] - d26[1]) >= b24 || Math.abs(p23[14] - d26[2]) >= b24) && (c34.transformation = p23), u32;
}
var g9 = e11();
function b13(e33, o24, a35, s26, i19) {
  const l34 = e33.graphics3DSymbolLayer.lodRenderer;
  if (t(l34)) return 0;
  const c34 = o24.centerPointInElevationSR;
  s26(c34, A6);
  const f27 = "absolute-height" !== o24.mode ? A6.sampledElevation : 0, u32 = l34.instanceData, p23 = e33.instanceIndex, d26 = E14;
  u32.getGlobalTransform(p23, d26);
  const g23 = o2(M6, d26[12], d26[13], d26[14]);
  t17.TESTS_DISABLE_OPTIMIZATIONS ? (h16[0] = c34.x, h16[1] = c34.y, h16[2] = A6.z, Zn(c34.spatialReference, h16, d26, i19.spatialReference) && u32.setGlobalTransform(p23, d26)) : i19.setAltitudeOfTransformation(A6.z, d26);
  const b24 = T6 / i19.unitInMeters;
  return (t17.TESTS_DISABLE_OPTIMIZATIONS || Math.abs(d26[12] - g23[0]) >= b24 || Math.abs(d26[13] - g23[1]) >= b24 || Math.abs(d26[14] - g23[2]) >= b24) && u32.setGlobalTransform(p23, d26), f27;
}
function I7(t28, e33, o24, n26, a35) {
  const r30 = t28.stageObject, s26 = r30.geometries;
  if (0 === s26.length) return 0;
  let i19 = 0, l34 = null, m18 = 0, c34 = false;
  for (const p23 of s26) {
    if (!t20(p23)) continue;
    const t29 = p23.vertexAttributes.get(O4.POSITION);
    if (t29 !== l34) {
      const { update: r31, averageGeometrySampledElevation: s27 } = O8(p23, e33, o24, n26, a35);
      m18 = s27, l34 = t29, c34 = r31;
    }
    c34 && r30.geometryVertexAttrsUpdated(p23), i19 += m18;
  }
  return i19 / s26.length;
}
var T6 = 0.01;
var h16 = n5();
var S7 = n5();
var v12 = n5();
var E14 = e11();
var M6 = n5();
var A6 = new T4();
function O8(t28, e33, o24, n26, a35) {
  let r30 = false;
  const i19 = t28.shaderTransformation, l34 = e33.requiresSampledElevationInfo;
  S7[0] = i19[12], S7[1] = i19[13], S7[2] = i19[14], t28.invalidateBoundingInfo();
  const f27 = t28.getMutableAttribute(O4.POSITION), p23 = f27.data, d26 = f27.size, g23 = p23.length / d26, b24 = new t19(t28.mapPositions, o24);
  let I13 = 0, E21 = 0;
  for (let c34 = 0; c34 < g23; c34++) {
    if (v12[0] = p23[I13], v12[1] = p23[I13 + 1], v12[2] = p23[I13 + 2], n26(b24, A6), l34 && (E21 += A6.sampledElevation), t17.TESTS_DISABLE_OPTIMIZATIONS) p23[I13] = b24.array[b24.offset], p23[I13 + 1] = b24.array[b24.offset + 1], p23[I13 + 2] = A6.z, xn(p23, o24, I13, p23, a35.spatialReference, I13, 1), p23[I13] -= S7[0], p23[I13 + 1] -= S7[1], p23[I13 + 2] -= S7[2], r30 = true;
    else {
      h16[0] = p23[I13] + S7[0], h16[1] = p23[I13 + 1] + S7[1], h16[2] = p23[I13 + 2] + S7[2], a35.setAltitude(h16, A6.z), p23[I13] = h16[0] - S7[0], p23[I13 + 1] = h16[1] - S7[1], p23[I13 + 2] = h16[2] - S7[2];
      const t29 = T6 / a35.unitInMeters;
      (Math.abs(v12[0] - p23[I13]) >= t29 || Math.abs(v12[1] - p23[I13 + 1]) >= t29 || Math.abs(v12[2] - p23[I13 + 2]) >= t29) && (r30 = true);
    }
    I13 += d26, b24.offset += 3;
  }
  return E21 /= g23, { update: r30, averageGeometrySampledElevation: E21 };
}

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DObject3DGraphicLayer.js
var f19 = class {
  constructor(e33, t28, s26) {
    this.baseMaterial = e33, this.edgeMaterials = t28, this.properties = s26;
  }
};
var S8 = class {
  get isElevationSource() {
    return !(!this.stageObject.metadata || !this.stageObject.metadata.isElevationSource);
  }
  constructor(e33, t28, s26, i19, a35, n26, r30, o24 = null) {
    this.graphics3DSymbolLayer = e33, this.stageObject = t28, this._uniqueGeometries = s26, this._uniqueMaterials = i19, this._sharedResource = a35, this.elevationAligner = n26, this.elevationContext = r30, this._edgeState = o24, this.type = "object3d", this._stageLayer = null, this._stage = null, this._visible = false, this._addedToStage = false, this.alignedSampledElevation = 0, this.needsElevationUpdates = false, this.useObjectOriginAsAttachmentOrigin = false;
  }
  initialize(e33, t28) {
    this._stageLayer = t28, this._stage = e33, e33.addMany(this._uniqueMaterials), e33.addMany(this._uniqueGeometries), e33.add(this.stageObject);
  }
  destroy() {
    const t28 = this._stage;
    this._stageLayer && (t28.removeMany(this._uniqueMaterials), t28.removeMany(this._uniqueGeometries)), t28.remove(this.stageObject), this._addedToStage && (this._stageLayer.remove(this.stageObject), this._addedToStage = false);
    const s26 = this._stage.renderer.ensureEdgeView();
    s26.hasObject(this.stageObject) && s26.removeObject(this.stageObject), this.stageObject.dispose(), r(this._sharedResource) && this._sharedResource.release(), this._visible = false, this._stageLayer = null, this._stage = null;
  }
  layerOpacityChanged(e33, s26) {
    if (t(this._edgeState)) return;
    const i19 = E15(this._edgeState.baseMaterial);
    let a35 = false;
    for (const t28 of this._edgeState.edgeMaterials) t28.objectTransparency !== i19 && (t28.objectTransparency = i19, a35 = true);
    a35 && this._resetEdgeObject(s26);
    this._stage.renderer.ensureEdgeView().updateAllComponentOpacities(this.stageObject, [e33]);
  }
  slicePlaneEnabledChanged(e33, s26) {
    if (t(this._edgeState)) return;
    this._stage.renderer.ensureEdgeView().updateAllComponentMaterials(this.stageObject, this._edgeState.edgeMaterials, { hasSlicePlane: e33 }, !s26), this._edgeState.properties.hasSlicePlane = e33;
  }
  setVisibility(t28) {
    if (null != this._stage && this._visible !== t28 && (this._visible = t28, this.stageObject.visible = t28, this._visible && !this._addedToStage && (this._stageLayer.add(this.stageObject), this._addedToStage = true), r(this._edgeState))) {
      const e33 = this._stage.renderer.ensureEdgeView();
      e33.hasObject(this.stageObject) ? e33.updateObjectVisibility(this.stageObject, t28) : t28 && this._addOrUpdateEdgeObject(e33, false);
    }
  }
  get visible() {
    return this._visible;
  }
  alignWithElevation(t28, i19, a35, n26) {
    if (null == this.elevationAligner) return;
    r(a35) && l20(this.elevationContext.featureExpressionInfoContext, a35);
    const r30 = (e33, s26) => f16(e33, t28, this.elevationContext, i19, s26);
    this.alignedSampledElevation = this.elevationAligner(this, this.elevationContext, e2(t28.spatialReference), r30, i19), this._resetEdgeObject(n26);
  }
  alignWithAbsoluteElevation(e33, t28, s26) {
    const i19 = (t29, s27) => {
      s27.sampledElevation = e33, s27.verticalDistanceToGround = 0, s27.z = e33;
    };
    this.alignedSampledElevation = this.elevationAligner(this, this.elevationContext, null, i19, t28), this._resetEdgeObject(s26);
  }
  getCenterObjectSpace(e33 = n5()) {
    return r4(e33, k3(this.stageObject.boundingVolumeObjectSpace.bounds));
  }
  getBoundingBoxObjectSpace(e33 = a7()) {
    const t28 = this.stageObject.boundingVolumeObjectSpace;
    return G2(e33, t28.min), O3(e33, t28.max), e33;
  }
  computeAttachmentOrigin(e33) {
    if (this.useObjectOriginAsAttachmentOrigin) {
      const t28 = this.stageObject.transformation;
      e33.render.origin[0] += t28[12], e33.render.origin[1] += t28[13], e33.render.origin[2] += t28[14], e33.render.num++;
    } else for (const t28 of this.stageObject.geometries) t28.computeAttachmentOrigin(x9) && (O2(x9, x9, this.stageObject.transformation), u3(e33.render.origin, e33.render.origin, x9), e33.render.num++);
  }
  async getProjectedBoundingBox(e33, t28, s26, a35, r30) {
    const o24 = this.getBoundingBoxObjectSpace(r30), c34 = A7, h25 = B(o24) ? 1 : c34.length;
    for (let i19 = 0; i19 < h25; i19++) {
      const e34 = c34[i19];
      M7[0] = o24[e34[0]], M7[1] = o24[e34[1]], M7[2] = o24[e34[2]], O2(M7, M7, this.stageObject.transformation), y6[3 * i19 + 0] = M7[0], y6[3 * i19 + 1] = M7[1], y6[3 * i19 + 2] = M7[2];
    }
    if (!e33(y6, 0, h25)) return null;
    S(o24);
    let l34 = null;
    this.calculateRelativeScreenBounds && (l34 = this.calculateRelativeScreenBounds());
    for (let i19 = 0; i19 < 3 * h25; i19 += 3) {
      for (let e34 = 0; e34 < 3; e34++) o24[e34] = Math.min(o24[e34], y6[i19 + e34]), o24[e34 + 3] = Math.max(o24[e34 + 3], y6[i19 + e34]);
      l34 && s26.push({ location: y6.slice(i19, i19 + 3), screenSpaceBoundingRect: l34 });
    }
    if (t28 && t28.service && "absolute-height" !== this.elevationContext.mode) {
      E3(o24, x9);
      const e34 = "relative-to-scene" === this.elevationContext.mode ? "scene" : "ground";
      let s27 = 0;
      if (t28.useViewElevation) s27 = l(t28.service.getElevation(x9[0], x9[1], e34), 0);
      else try {
        const n26 = X(o24, t28.service.spatialReference, t28);
        s27 = l(await t28.service.queryElevation(x9[0], x9[1], a35, n26, e34), 0);
      } catch (j12) {
      }
      _2(o24, 0, 0, -this.alignedSampledElevation + s27);
    }
    return o24;
  }
  addObjectState(e33, t28) {
    e33 === t12.Highlight && t28.addObject(this.stageObject, this.stageObject.highlight()), e33 === t12.MaskOccludee && t28.addObject(this.stageObject, this.stageObject.maskOccludee());
  }
  removeObjectState(e33) {
    e33.removeObject(this.stageObject);
  }
  _resetEdgeObject(e33) {
    if (t(this._edgeState)) return;
    const s26 = this._stage.renderer.ensureEdgeView();
    this._visible ? this._addOrUpdateEdgeObject(s26, e33) : s26.removeObject(this.stageObject);
  }
  _addOrUpdateEdgeObject(e33, s26) {
    const i19 = this._edgeState;
    if (t(i19)) return;
    const a35 = E15(i19.baseMaterial);
    for (const t28 of i19.edgeMaterials) t28.objectTransparency = a35;
    e33.addOrUpdateObject3D(this.stageObject, i19.edgeMaterials, i19.properties, !s26).then(() => {
      var _a;
      return (_a = this._stageLayer) == null ? void 0 : _a.sync();
    });
  }
};
function E15(e33) {
  return e33.isVisible() ? e33.parameters.transparent ? A2.TRANSPARENT : A2.OPAQUE : A2.INVISIBLE;
}
var y6 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
var M7 = n5();
var x9 = n5();
var A7 = [[0, 1, 2], [3, 1, 2], [0, 4, 2], [3, 4, 2], [0, 1, 5], [3, 1, 5], [0, 4, 5], [3, 4, 5]];

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DObjectMetadata.js
var e21 = class {
  constructor(e33, o24 = null) {
    this.labelText = o24, this.elevationOffset = l(e33, 0);
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/interfaces.js
var e22;
!function(e33) {
  e33[e33.Recreate_Symbol = 0] = "Recreate_Symbol", e33[e33.Recreate_Graphics = 1] = "Recreate_Graphics", e33[e33.Fast_Update = 2] = "Fast_Update";
}(e22 || (e22 = {}));

// node_modules/@arcgis/core/views/3d/layers/graphics/Loadable.js
var a28 = class {
  constructor(t28) {
    this.schedule = t28, this._abortController = null, this._loadStatus = e23.LOADING, this._loadError = null, this._loader = null, this.logger = null;
  }
  destroy() {
    this.abortLoad();
  }
  get loadStatus() {
    return this._loadStatus;
  }
  load(r30, l34) {
    return this._loadStatus === e23.LOADED ? (r30 && r30(), l(this._loader, Promise.resolve())) : this._loadStatus === e23.FAILED ? (l34 && l34(this._loadError), l(this._loader, Promise.resolve())) : (t(this._loader) && (this._abortController = new AbortController(), this._loader = this.doLoad(this._abortController.signal).then(() => {
      this._abortController = null, this._loadStatus = e23.LOADED;
    }, (t28) => {
      throw this._loadError = t28, this._abortController = null, this._loadStatus = e23.FAILED, !j(t28) && this.logger && t28.message && this.logger.warn(t28.message), t28;
    })), this._loader.then(r30, l34).catch(() => {
    }), this._loader);
  }
  abortLoad() {
    r(this._abortController) ? this._abortController = w(this._abortController) : this._loadStatus === e23.LOADING && (this._loadStatus = e23.FAILED), this._loader = null;
  }
};
var e23;
!function(t28) {
  t28[t28.LOADING = 0] = "LOADING", t28[t28.LOADED = 1] = "LOADED", t28[t28.FAILED = 2] = "FAILED";
}(e23 || (e23 = {}));

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DPathSymbolLayerConstants.js
var o17 = 3;
var t21 = 3;
var c21 = 10;

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/LodResources.js
var t22 = class {
  constructor(o24, r30 = null) {
    this.geometry = o24, this.textures = r30;
  }
};
function e24(r30) {
  const t28 = [];
  return r30.levels.forEach((o24) => o24.components.forEach((o25) => t28.push(o25.geometry.material))), u(t28);
}
function n17(t28) {
  const e33 = new Array();
  return t28.levels.forEach((o24) => {
    o24.components.forEach((o25) => {
      r(o25.textures) && e33.push(...o25.textures);
    });
  }), u(e33);
}
function s17(r30) {
  const t28 = r30.components.map((o24) => o24.geometry);
  return u(t28);
}
function c22(r30) {
  const t28 = [];
  return r30.levels.forEach((o24) => {
    o24.components.forEach((o25) => {
      t28.push(o25.geometry);
    });
  }), u(t28);
}

// node_modules/@arcgis/core/views/3d/layers/graphics/primitiveObjectSymbolUtils.js
function o18(e33) {
  switch (e33) {
    case "sphere":
    case "cube":
    case "diamond":
    case "cylinder":
    case "cone":
    case "inverted-cone":
    case "tetrahedron":
      return true;
  }
  return false;
}
function u20(o24, u32) {
  const S19 = (n26, s26, a35 = false) => ({ levels: n26.map((n27) => {
    const c34 = s26(n27.tesselation);
    return a35 && it(c34), { components: [new t22(c34)], faceCount: c34.indexCount / 3, minScreenSpaceRadius: n27.minScreenSpaceRadius };
  }) });
  switch (o24) {
    case "sphere":
      return S19([{ tesselation: 0, minScreenSpaceRadius: 0 }, { tesselation: 1, minScreenSpaceRadius: 8 }, { tesselation: 2, minScreenSpaceRadius: 16 }, { tesselation: 3, minScreenSpaceRadius: 50 }, { tesselation: 4, minScreenSpaceRadius: 250 }], (e33) => et(u32, 0.5, e33, true));
    case "cube":
      return S19([{ tesselation: 0, minScreenSpaceRadius: 0 }], () => C3(u32, 1));
    case "cone":
      return S19(d14, (e33) => lt(u32, 1, 0.5, e33, false), true);
    case "inverted-cone":
      return S19(d14, (e33) => lt(u32, 1, 0.5, e33, true), true);
    case "cylinder":
      return S19(d14, (e33) => ut(u32, 1, 0.5, e33, [0, 0, 1], [0, 0, 0.5]));
    case "tetrahedron":
      return S19([{ tesselation: 0, minScreenSpaceRadius: 0 }], () => tt(u32, 1), true);
    case "diamond":
      return S19([{ tesselation: 0, minScreenSpaceRadius: 0 }], () => q4(u32, 1), true);
    default:
      return;
  }
}
var d14 = [{ tesselation: 6, minScreenSpaceRadius: 0 }, { tesselation: 18, minScreenSpaceRadius: 7 }, { tesselation: 64, minScreenSpaceRadius: 65 }];

// node_modules/@arcgis/core/views/3d/layers/graphics/symbolComplexity.js
var d15 = { primitivesPerFeature: 0, primitivesPerCoordinate: 0, drawCallsPerFeature: 0, estimated: true, memory: { bytesPerFeature: 0, bytesPerCoordinate: 0, bytesPerFeatureLabel: 0, resourceBytes: 0, draped: { bytesPerFeature: 0, bytesPerFeatureLabel: 0, bytesPerCoordinate: 0 } } };
function m10(e33) {
  if ("web-style" === e33.type) return d15;
  return n18(e33.symbolLayers.toArray().map((r30) => c23(e33, r30)));
}
function n18(e33) {
  let t28 = 0, a35 = 0, s26 = 0, i19 = false, o24 = 0;
  const u32 = { bytesPerFeature: 0, bytesPerFeatureLabel: 0, bytesPerCoordinate: 0, resourceBytes: 0, draped: { bytesPerFeature: 0, bytesPerFeatureLabel: 0, bytesPerCoordinate: 0 } };
  for (const b24 of e33) t(b24) || (t28 += b24.primitivesPerFeature, a35 += b24.primitivesPerCoordinate, s26 += b24.drawCallsPerFeature, u32.bytesPerFeature += b24.memory.bytesPerFeature, u32.bytesPerFeatureLabel += b24.memory.bytesPerFeatureLabel, u32.bytesPerCoordinate += b24.memory.bytesPerCoordinate, u32.resourceBytes += b24.memory.resourceBytes, u32.draped.bytesPerFeature += b24.memory.bytesPerFeature, u32.draped.bytesPerFeatureLabel += b24.memory.bytesPerFeatureLabel, u32.draped.bytesPerCoordinate += b24.memory.bytesPerCoordinate, i19 = i19 || b24.estimated, ++o24);
  return { primitivesPerFeature: t28, primitivesPerCoordinate: a35, drawCallsPerFeature: s26, estimated: i19, memory: u32, numComplexities: o24 };
}
function l24(e33) {
  const r30 = n18(e33);
  return r30.numComplexities > 0 && (r30.primitivesPerFeature /= r30.numComplexities, r30.primitivesPerCoordinate /= r30.numComplexities, r30.drawCallsPerFeature /= r30.numComplexities, r30.memory.bytesPerFeature /= r30.numComplexities, r30.memory.bytesPerFeatureLabel /= r30.numComplexities, r30.memory.bytesPerCoordinate /= r30.numComplexities, r30.memory.resourceBytes /= r30.numComplexities, r30.memory.draped.bytesPerFeature /= r30.numComplexities, r30.memory.draped.bytesPerFeatureLabel /= r30.numComplexities, r30.memory.draped.bytesPerCoordinate /= r30.numComplexities), r30;
}
var F5 = {};
function c23(r30, u32) {
  const y14 = p17(r30, u32), P10 = a12(u32) ? 2 : 0;
  switch (u32.type) {
    case "extrude":
      return { primitivesPerFeature: -4, primitivesPerCoordinate: 4, drawCallsPerFeature: P10, estimated: false, memory: y14 };
    case "fill":
      return "mesh-3d" === r30.type ? { primitivesPerFeature: 0, primitivesPerCoordinate: 0, drawCallsPerFeature: P10, estimated: false, memory: y14 } : r(u32.outline) && u32.outline.size > 0 ? { primitivesPerFeature: -4, primitivesPerCoordinate: 3, drawCallsPerFeature: 0, estimated: false, memory: y14 } : { primitivesPerFeature: -2, primitivesPerCoordinate: 1, drawCallsPerFeature: 0, estimated: false, memory: y14 };
    case "water":
      return { primitivesPerFeature: -2, primitivesPerCoordinate: 1, drawCallsPerFeature: 0, estimated: false, memory: y14 };
    case "line":
      return { primitivesPerFeature: -2, primitivesPerCoordinate: 2, drawCallsPerFeature: 0, estimated: false, memory: y14 };
    case "object":
      if (u32.resource && u32.resource.href) return { primitivesPerFeature: 16, primitivesPerCoordinate: 0, drawCallsPerFeature: 0, estimated: true, memory: y14 };
      return { ...C5(u32.resource && u32.resource.primitive || d7), memory: y14 };
    case "path": {
      let r31 = 0, t28 = 0;
      switch (u32.profile) {
        case "circle":
          r31 = c21;
          break;
        case "quad":
          r31 = 4;
          break;
        default:
          return void n9(u32.profile);
      }
      switch (u32.join ?? "simple") {
        case "round":
          t28 = o17;
          break;
        case "miter":
        case "bevel":
          t28 = 1;
          break;
        default:
          return;
      }
      const a35 = 2 * r31, b24 = r31 * t28 * 2;
      let P11 = -2 * b24 - a35;
      switch (u32.cap) {
        case "none":
          break;
        case "butt":
        case "square":
          P11 += 2 * (r31 - 1);
          break;
        case "round":
          P11 += 2 * (r31 * (t21 - 1) * 2 + r31);
          break;
        default:
          return;
      }
      return { primitivesPerFeature: P11, primitivesPerCoordinate: b24 + a35, drawCallsPerFeature: 0, estimated: false, memory: y14 };
    }
    case "text":
    case "icon":
      return { primitivesPerFeature: 2, primitivesPerCoordinate: 0, drawCallsPerFeature: 0, estimated: false, memory: y14 };
    default:
      return;
  }
}
function p17(r30, a35) {
  const s26 = "point-3d" === r30.type;
  switch (a35.type) {
    case "extrude":
      return a35.edges && a35.edges.size > 0 ? L2.EXTRUDE_EDGES : L2.EXTRUDE;
    case "fill":
      return r(a35.outline) && a35.outline.size > 0 ? L2.FILL_OUTLINE : L2.FILL;
    case "water":
      return L2.FILL;
    case "line":
      return "round" === a35.join ? L2.LINE_ROUND : L2.LINE_MITER;
    case "path":
      switch (a35.join) {
        case "round":
          switch (a35.profile) {
            case "circle":
              return L2.PATH_ROUND_CIRCLE;
            case "quad":
              return L2.PATH_ROUND_QUAD;
            default:
              return void n9(a35.profile);
          }
        case "miter":
        case "bevel":
          switch (a35.profile) {
            case "circle":
              return L2.PATH_MITER_CIRCLE;
            case "quad":
              return L2.PATH_MITER_QUAD;
            default:
              return void n9(a35.profile);
          }
        default:
          return;
      }
    case "object":
      return s26 ? L2.OBJECT_POINT : L2.OBJECT_POLYGON;
    case "icon":
    case "text":
      return s26 ? L2.ICON_POINT : L2.ICON_POLYGON;
    default:
      return;
  }
}
function C5(e33) {
  let r30 = F5[e33];
  if (r30) return r30;
  const t28 = u20(e33, null);
  return r30 = { primitivesPerFeature: s17(t28.levels[0]).reduce((e34, r31) => e34 + r31.indices.get(O4.POSITION).length / 3, 0), primitivesPerCoordinate: 0, drawCallsPerFeature: 0, estimated: false }, F5[e33] = r30, r30;
}
var L2 = { ICON_POINT: { bytesPerFeature: 3293.7707557538765, bytesPerFeatureLabel: 1020.2764, bytesPerCoordinate: 0, resourceBytes: 0, draped: { bytesPerFeature: 2213.51585392203, bytesPerFeatureLabel: 1008.84664, bytesPerCoordinate: 0 } }, ICON_POLYGON: { bytesPerFeature: 4745.397589234022, bytesPerFeatureLabel: 1006.5440666666666, bytesPerCoordinate: 3.8971910718981526, resourceBytes: 0, draped: { bytesPerFeature: 3711.9299672493826, bytesPerFeatureLabel: 991.1003999999999, bytesPerCoordinate: 3.916858016273668 } }, OBJECT_POINT: { bytesPerFeature: 1751.6405617660876, bytesPerFeatureLabel: 1024.7827699999998, bytesPerCoordinate: 0, resourceBytes: 0, draped: { bytesPerFeature: 1751.6405617660876, bytesPerFeatureLabel: 1024.7827699999998, bytesPerCoordinate: 0 } }, OBJECT_POLYGON: { bytesPerFeature: 3265.877223973855, bytesPerFeatureLabel: 1002.3207366666666, bytesPerCoordinate: 4.003101524580855, resourceBytes: 0, draped: { bytesPerFeature: 3265.877223973855, bytesPerFeatureLabel: 1002.3207366666666, bytesPerCoordinate: 4.003101524580855 } }, LINE_MITER: { bytesPerFeature: 5757.019675077085, bytesPerFeatureLabel: 998.6150266666667, bytesPerCoordinate: 24.456810187064043, resourceBytes: 0, draped: { bytesPerFeature: 4101.480288021862, bytesPerFeatureLabel: 1007.7540599999999, bytesPerCoordinate: 18.25629912730874 } }, LINE_ROUND: { bytesPerFeature: 5770.95297166408, bytesPerFeatureLabel: 1017.77396, bytesPerCoordinate: 24.60919037084966, resourceBytes: 0, draped: { bytesPerFeature: 4089.5796612121826, bytesPerFeatureLabel: 987.8320266666666, bytesPerCoordinate: 18.43599029126731 } }, PATH_MITER_CIRCLE: { bytesPerFeature: 36120.81813311945, bytesPerFeatureLabel: 977.3815, bytesPerCoordinate: 2713.2216607858863, resourceBytes: 0, draped: { bytesPerFeature: 36120.81813311945, bytesPerFeatureLabel: 977.3815, bytesPerCoordinate: 2713.2216607858863 } }, PATH_ROUND_CIRCLE: { bytesPerFeature: 23268.96777866874, bytesPerFeatureLabel: 985.2637, bytesPerCoordinate: 5284.873051323177, resourceBytes: 0, draped: { bytesPerFeature: 23268.96777866874, bytesPerFeatureLabel: 985.2637, bytesPerCoordinate: 5284.873051323177 } }, PATH_MITER_QUAD: { bytesPerFeature: 24548.629753007182, bytesPerFeatureLabel: 964.6924, bytesPerCoordinate: 2114.107276663994, resourceBytes: 0, draped: { bytesPerFeature: 24548.629753007182, bytesPerFeatureLabel: 964.6924, bytesPerCoordinate: 2114.107276663994 } }, PATH_ROUND_QUAD: { bytesPerFeature: 34316.219663191616, bytesPerFeatureLabel: 975.7985, bytesPerCoordinate: 3331.3952550120293, resourceBytes: 0, draped: { bytesPerFeature: 34316.219663191616, bytesPerFeatureLabel: 975.7985, bytesPerCoordinate: 3331.3952550120293 } }, FILL: { bytesPerFeature: 6141.501452970723, bytesPerFeatureLabel: 1008.2056066666665, bytesPerCoordinate: 9.669541106191478, resourceBytes: 0, draped: { bytesPerFeature: 4615.812654782311, bytesPerFeatureLabel: 1004.2114200000001, bytesPerCoordinate: 7.378043214430644 } }, FILL_OUTLINE: { bytesPerFeature: 9038.575581319641, bytesPerFeatureLabel: 1017.9996066666668, bytesPerCoordinate: 14.96569682175086, resourceBytes: 0, draped: { bytesPerFeature: 6369.386021594582, bytesPerFeatureLabel: 1008.9863733333334, bytesPerCoordinate: 10.25287774000214 } }, EXTRUDE: { bytesPerFeature: 20071.2337049912, bytesPerFeatureLabel: 1019.49468, bytesPerCoordinate: 49.39369614206849, resourceBytes: 0, draped: { bytesPerFeature: 20071.2337049912, bytesPerFeatureLabel: 1019.49468, bytesPerCoordinate: 49.39369614206849 } }, EXTRUDE_EDGES: { bytesPerFeature: 22300.21739345088, bytesPerFeatureLabel: 1017.4348466666665, bytesPerCoordinate: 49.40242281963031, resourceBytes: 0, draped: { bytesPerFeature: 22300.21739345088, bytesPerFeatureLabel: 1017.4348466666665, bytesPerCoordinate: 49.40242281963031 } } };

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DSymbolLayer.js
var f20 = s.getLogger("esri.views.3d.layers.graphics.Graphics3DSymbolLayer");
var y7 = class extends a28 {
  constructor(e33, t28, i19, o24) {
    super(i19.schedule), this.symbol = e33, this.symbolLayer = t28, this._context = i19, this._elevationInfoOverride = null, this._ignoreDrivers = false, this._drivenProperties = { color: false, opacity: false, opacityAlwaysOpaque: true, size: false }, this.complexity = null, this.logger = f20, this._elevationOptions = { supportsOffsetAdjustment: false, supportsOnTheGround: true }, this._renderPriority = o24.renderPriority, this._renderPriorityStep = o24.renderPriorityStep, this._elevationContext = new h15(), this.complexity = this.computeComplexity(), this._ignoreDrivers = o24.ignoreDrivers, this._ignoreDrivers || (this._drivenProperties = g10(this._context.renderer)), this._updateElevationContext();
  }
  getCachedSize() {
    return null;
  }
  get extentPadding() {
    return 0;
  }
  _drivenPropertiesChanged(e33) {
    if (this._ignoreDrivers) return false;
    const t28 = this._drivenProperties, i19 = g10(e33);
    return i19.color !== t28.color || i19.opacity !== t28.opacity || i19.opacityAlwaysOpaque !== t28.opacityAlwaysOpaque || i19.size !== t28.size;
  }
  get needsDrivenTransparentPass() {
    return this._drivenProperties.opacity && !this._drivenProperties.opacityAlwaysOpaque;
  }
  _logGeometryCreationWarnings(e33, t28, i19, o24) {
    const r30 = e33.projectionSuccess, n26 = "polygons" in e33 ? e33.polygons : null, s26 = `${o24} geometry failed to be created`;
    let a35 = null;
    r30 ? !this._logGeometryValidationWarnings(t28, i19, o24) && n26 && 0 === n26.length && "rings" === i19 && t28.length > 0 && t28[0].length > 2 && (a35 = `${s26} (filled rings should use clockwise winding - try reversing the order of vertices)`) : a35 = `${s26} (failed to project geometry to view spatial reference)`, a35 && f20.warnOncePerTick(a35);
  }
  _logGeometryValidationWarnings(e33, t28, i19) {
    const o24 = `${i19} geometry failed to be created`;
    return !e33.length || 1 === e33.length && !e33[0].length ? (f20.warnOncePerTick(`${o24} (no ${t28} were defined)`), true) : (!Array.isArray(e33) || !Array.isArray(e33[0])) && (f20.warnOncePerTick(`${o24} (${t28} should be defined as a 2D array)`), true);
  }
  _validateGeometry(e33, t28 = null, o24 = null) {
    if (r(t28) && !t28.includes(e33.type)) return this.logger.warn("unsupported geometry type for " + o24 + ` symbol: ${e33.type}`), false;
    if ("point" === e33.type) {
      const t29 = e33;
      if (!isFinite(t29.x) || !isFinite(t29.y)) return f20.warn("point coordinate is not a valid number, graphic skipped"), false;
    }
    return true;
  }
  _defaultElevationInfoNoZ() {
    return m11;
  }
  _defaultElevationInfoZ() {
    return _9;
  }
  _updateElevationContext() {
    r(this._elevationInfoOverride) ? (this._elevationContext.setFromElevationInfo(this._elevationInfoOverride), this._elevationContext.updateFeatureExpressionInfoContext(null)) : this._context.layer.elevationInfo ? (this._elevationContext.setFromElevationInfo(this._context.layer.elevationInfo), this._elevationContext.updateFeatureExpressionInfoContext(this._context.featureExpressionInfoContext)) : this._elevationContext.reset();
  }
  getDefaultElevationInfo(e33) {
    return e33.hasZ ? this._defaultElevationInfoZ() : this._defaultElevationInfoNoZ();
  }
  getGeometryElevationMode(e33, t28 = this.getDefaultElevationInfo(e33)) {
    return this._elevationContext.mode || t28.mode;
  }
  setElevationInfoOverride(e33) {
    this._elevationInfoOverride = e33, this._updateElevationContext();
  }
  setGraphicElevationContext(e33, t28) {
    const i19 = e2(e33.geometry), n26 = this.getDefaultElevationInfo(i19);
    t28.unit = null != this._elevationContext.unit ? this._elevationContext.unit : n26.unit, t28.mode = this.getGeometryElevationMode(i19, n26), t28.offsetMeters = l(this._elevationContext.meterUnitOffset, l(n26.offset, 0));
    const s26 = !this._elevationOptions.supportsOnTheGround && "on-the-ground" === t28.mode;
    s26 && (t28.mode = "relative-to-ground", t28.offsetMeters = 0);
    const a35 = s26 ? f17 : this._elevationContext.featureExpressionInfoContext;
    return t28.updateFeatureExpressionInfoContext(a35, e33, this._context.layer), t28;
  }
  prepareSymbolLayerPatch(e33) {
  }
  updateGeometry(e33, t28) {
    return false;
  }
  onRemoveGraphic(e33) {
  }
  _getLayerOpacity() {
    if (this._context.graphicsCoreOwner && "fullOpacity" in this._context.graphicsCoreOwner) return this._context.graphicsCoreOwner.fullOpacity ?? 0;
    const e33 = this._context.layer.opacity;
    return e33 ?? 1;
  }
  _getCombinedOpacity(e33, t28 = x10) {
    let o24 = 1;
    return this.draped || (o24 *= this._getLayerOpacity()), this._drivenProperties.opacity || (r(e33) ? o24 *= e33.a : t28.hasIntrinsicColor || (o24 = 0)), o24;
  }
  _getCombinedOpacityAndColor(t28, o24 = x10) {
    const r30 = this._getCombinedOpacity(t28, o24);
    if (this._drivenProperties.color) return B2(null, r30);
    const s26 = r(t28) ? l11.toUnitRGB(t28) : l3;
    return B2(s26, r30);
  }
  _getVertexOpacityAndColor(e33, t28 = null) {
    const o24 = this._drivenProperties.color ? e33.color : null, r30 = this._drivenProperties.opacity ? e33.opacity : null, n26 = B2(o24, r30);
    return r(t28) && (n26[0] *= t28, n26[1] *= t28, n26[2] *= t28, n26[3] *= t28), n26;
  }
  isFastUpdatesEnabled() {
    return this._fastUpdates && this._fastUpdates.enabled;
  }
  computeComplexity() {
    return c23(this.symbol, this.symbolLayer);
  }
  globalPropertyChanged(e33, t28, i19) {
    switch (e33) {
      case "opacity":
        return this.layerOpacityChanged(t28, i19), true;
      case "elevationInfo": {
        const e34 = this._elevationContext.mode;
        this._updateElevationContext();
        return this.layerElevationInfoChanged(t28, i19, e34) !== j8.RECREATE;
      }
      case "slicePlaneEnabled":
        return this.slicePlaneEnabledChanged(t28, i19);
      case "physicalBasedRenderingEnabled":
        return this.physicalBasedRenderingChanged();
      case "pixelRatio":
        return this.pixelRatioChanged();
      case "skipHighSymbolLods":
        return this.skipHighSymbolLodsChanged();
      default:
        return false;
    }
  }
  updateGraphics3DGraphicElevationInfo(e33, t28, o24) {
    let r30 = j8.UPDATE;
    return e33.forEach((e34) => {
      const n26 = t28(e34);
      if (r(n26)) {
        const t29 = e34.graphic;
        this.setGraphicElevationContext(t29, n26.elevationContext), n26.needsElevationUpdates = o24(n26.elevationContext.mode);
      } else r30 = j8.RECREATE;
    }), r30;
  }
  applyRendererDiff(e33, t28) {
    return e22.Recreate_Symbol;
  }
  getFastUpdateAttrValues(e33) {
    if (!this._fastUpdates.enabled) return null;
    const t28 = this._fastUpdates.visualVariables, i19 = t28.size ? v13(t28.size.field, e33) : 0, o24 = t28.color ? v13(t28.color.field, e33) : 0, r30 = t28.opacity ? v13(t28.opacity.field, e33) : 0;
    return r6(i19, o24, r30, 0);
  }
  get draped() {
    return this._draped;
  }
  ensureDrapedStatus(e33) {
    return null == this._draped ? (this._draped = e33, true) : (e33 !== this.draped && f20.warnOnce("A symbol can only produce either draped or non-draped visualizations. Use two separate symbol instances for draped and non-draped graphics if necessary."), false);
  }
  test() {
    const e33 = () => {
      var _a, _b, _c, _d, _e5, _f, _g, _h, _i, _j, _k, _l;
      return { size: ((_c = (_b = (_a = this._fastUpdates) == null ? void 0 : _a.visualVariables) == null ? void 0 : _b.size) == null ? void 0 : _c.field) ?? null, color: ((_f = (_e5 = (_d = this._fastUpdates) == null ? void 0 : _d.visualVariables) == null ? void 0 : _e5.color) == null ? void 0 : _f.field) ?? null, opacity: ((_i = (_h = (_g = this._fastUpdates) == null ? void 0 : _g.visualVariables) == null ? void 0 : _h.opacity) == null ? void 0 : _i.field) ?? null, rotation: ((_l = (_k = (_j = this._fastUpdates) == null ? void 0 : _j.visualVariables) == null ? void 0 : _k.rotation) == null ? void 0 : _l.field) ?? null };
    };
    return { drivenProperties: this._drivenProperties, getVisVarFields: e33 };
  }
};
function v13(e33, t28) {
  const i19 = null != e33 ? t28.attributes[e33] : 0;
  return null != i19 && isFinite(i19) ? i19 : 0;
}
function g10(e33) {
  const t28 = { color: false, opacity: false, opacityAlwaysOpaque: true, size: false };
  return e33 && "visualVariables" in e33 && e33.visualVariables && e33.visualVariables.forEach((e34) => {
    switch (e34.type) {
      case "color":
        if (t28.color = true, e34.stops) for (let i19 = 0; i19 < e34.stops.length; i19++) {
          const o24 = e34.stops[i19].color;
          o24 && (t28.opacity = true, o24.a < 1 && (t28.opacityAlwaysOpaque = false));
        }
        break;
      case "opacity":
        t28.opacity = true, t28.opacityAlwaysOpaque = false;
        break;
      case "size":
        t28.size = true;
    }
  }), t28;
}
var m11 = { mode: "on-the-ground", offset: 0, unit: "meters" };
var _9 = { mode: "absolute-height", offset: 0, unit: "meters" };
var x10 = { hasIntrinsicColor: false };

// node_modules/@arcgis/core/views/3d/layers/graphics/pointUtils.js
function p18(r30, n26, i19, s26, l34, p23) {
  const m18 = r30.clippingExtent;
  if (gn(n26, d16, r30.elevationProvider.spatialReference), r(m18) && !p5(m18, d16)) return null;
  gn(n26, d16, r30.renderCoordsHelper.spatialReference);
  const u32 = r30.localOriginFactory.getOrigin(d16), f27 = new x7({ castShadow: false, metadata: { layerUid: r30.layer.uid, graphicUid: l34, usesVerticalDistanceToGround: true } });
  i19.shaderTransformer = p23, i19.localOrigin = u32, f27.addGeometry(i19);
  return { object: f27, sampledElevation: v10(f27, n26, r30.elevationProvider, r30.renderCoordsHelper, s26) };
}
function m12(r30, o24, n26) {
  const i19 = r30.elevationContext, a35 = n26.spatialReference;
  gn(o24, d16, a35), i19.centerPointInElevationSR = M4(d16[0], d16[1], o24.hasZ ? d16[2] : 0, r(a35) ? a35 : null);
}
function u21(e33) {
  switch (e33.type) {
    case "point":
      return e33;
    case "polygon":
    case "extent":
      return A5(e33);
    case "polyline": {
      const r30 = e33.paths[0];
      if (!r30 || 0 === r30.length) return null;
      const t28 = c(r30, u2(r30) / 2);
      return M4(t28[0], t28[1], t28[2], e33.spatialReference);
    }
    case "mesh":
      return e33.origin;
  }
  return null;
}
var d16 = n5();

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/LineCalloutTechnique.js
var h17 = class _h extends e14 {
  initializeConfiguration(i19, t28) {
    t28.spherical = i19.viewingMode === l15.Global;
  }
  initializeProgram(e33) {
    return new o10(e33.rctx, _h.shader.get().build(this.configuration), E5);
  }
  setPipelineState(e33) {
    const i19 = e33 ? I3.ALWAYS : I3.LESS;
    return this.configuration.depthHudEnabled ? W({ depthTest: { func: i19 }, depthWrite: a13 }) : W({ blending: l16(R3.ONE, R3.SRC_ALPHA, R3.ONE_MINUS_SRC_ALPHA, R3.ONE_MINUS_SRC_ALPHA), depthTest: { func: i19 }, colorWrite: _6 });
  }
  initializePipeline() {
    return this.setPipelineState(this.configuration.hasMultipassGeometry);
  }
};
h17.shader = new t16(u16, () => import("./LineCallout.glsl-NGUBMXTC.js"));

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/LineCalloutTechniqueConfiguration.js
var i13 = class extends s11 {
  constructor() {
    super(...arguments), this.screenCenterOffsetUnitsEnabled = p13.World, this.spherical = false, this.occlusionTestEnabled = true, this.hasVerticalOffset = false, this.hasScreenSizePerspective = false, this.depthHudEnabled = false, this.depthHudAlignStartEnabled = false, this.hasSlicePlane = false, this.hasMultipassGeometry = false;
  }
};
e([r18({ count: p13.COUNT })], i13.prototype, "screenCenterOffsetUnitsEnabled", void 0), e([r18()], i13.prototype, "spherical", void 0), e([r18()], i13.prototype, "occlusionTestEnabled", void 0), e([r18()], i13.prototype, "hasVerticalOffset", void 0), e([r18()], i13.prototype, "hasScreenSizePerspective", void 0), e([r18()], i13.prototype, "depthHudEnabled", void 0), e([r18()], i13.prototype, "depthHudAlignStartEnabled", void 0), e([r18()], i13.prototype, "hasSlicePlane", void 0), e([r18()], i13.prototype, "hasMultipassGeometry", void 0), e([r18({ constValue: true })], i13.prototype, "hasSliceInVertexProgram", void 0), e([r18({ constValue: false })], i13.prototype, "isDraped", void 0);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/LineCalloutMaterial.js
var g11 = class _g extends h12 {
  get uniqueMaterialIdentifier() {
    return this._uniqueMaterialIdentifier;
  }
  constructor(e33) {
    super(e33, new S9()), this._configuration = new i13(), this._uniqueMaterialIdentifier = _g.uniqueMaterialIdentifier(this.parameters);
  }
  getPassParameters() {
    return this.parameters;
  }
  getConfiguration(t28, r30) {
    const i19 = (r30 == null ? void 0 : r30.slot) !== E9.LINE_CALLOUTS;
    return this._configuration.occlusionTestEnabled = this.parameters.occlusionTest, this._configuration.hasVerticalOffset = r(this.parameters.verticalOffset), this._configuration.hasScreenSizePerspective = r(this.parameters.screenSizePerspective), this._configuration.depthHudEnabled = i19, this._configuration.depthHudAlignStartEnabled = !!this.parameters.depthHUDAlignStart, this._configuration.screenCenterOffsetUnitsEnabled = "screen" === this.parameters.centerOffsetUnits ? p13.Screen : p13.World, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.hasMultipassGeometry = r30.multipassGeometry.enabled, this._configuration;
  }
  intersect() {
  }
  requiresSlot(e33, t28) {
    if (t28 === h10.Color) switch (e33) {
      case E9.LINE_CALLOUTS:
      case E9.LINE_CALLOUTS_HUD_DEPTH:
        return true;
    }
    return false;
  }
  createGLMaterial(e33) {
    return new O9(e33);
  }
  createBufferWriter() {
    return new b14();
  }
  validateParameters(e33) {
    const t28 = _g.uniqueMaterialIdentifier(e33);
    t28 !== this._uniqueMaterialIdentifier && (this._uniqueMaterialIdentifier = t28);
  }
  static uniqueMaterialIdentifier(e33) {
    return JSON.stringify({ screenOffset: e33.screenOffset || [0, 0], centerOffsetUnits: e33.centerOffsetUnits || "world" });
  }
};
var O9 = class extends t15 {
  beginSlot(e33) {
    return this.ensureTechnique(h17, e33);
  }
};
var S9 = class extends u11 {
  constructor() {
    super(...arguments), this.screenOffset = f11, this.color = [0, 0, 0, 1], this.size = 1, this.occlusionTest = false, this.shaderPolygonOffset = 1e-5, this.depthHUDAlignStart = false, this.centerOffsetUnits = "world", this.hasSlicePlane = false;
  }
};
var L3 = T2().vec3f(O4.POSITION).vec3f(O4.NORMAL).vec2f(O4.UV0).vec4f(O4.AUXPOS1);
var I8 = [t9(0, 0), t9(1, 0), t9(0, 1), t9(1, 0), t9(1, 1), t9(0, 1)];
var b14 = class {
  constructor() {
    this.vertexBufferLayout = L3;
  }
  allocate(e33) {
    return this.vertexBufferLayout.createBuffer(e33);
  }
  elementCount(e33) {
    return 6 * e33.indices.get(O4.POSITION).length;
  }
  write(e33, t28, r30, i19, s26) {
    g5(r30.indices.get(O4.POSITION), r30.vertexAttributes.get(O4.POSITION).data, e33, i19.position, s26, 6), b10(r30.indices.get(O4.NORMAL), r30.vertexAttributes.get(O4.NORMAL).data, t28, i19.normal, s26, 6), a15(r30.indices.get(O4.AUXPOS1), r30.vertexAttributes.get(O4.AUXPOS1).data, i19.auxpos1, s26, 6);
    for (let n26 = 0; n26 < I8.length; ++n26) i19.uv0.setVec(s26 + n26, I8[n26]);
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DLineCalloutSymbolLayer.js
var w7 = class _w extends y7 {
  constructor(e33, t28) {
    super(e33, null, t28, S10), this._elevationOptions = { supportsOffsetAdjustment: true, supportsOnTheGround: false }, this.ensureDrapedStatus(false);
  }
  async doLoad() {
    this._material = new g11(this._materialParameters), this._context.stage.add(this._material);
  }
  destroy() {
    super.destroy(), this._context.stage.remove(this._material), this._material = null;
  }
  _perInstanceMaterialParameters(e33) {
    const t28 = this._materialParameters;
    return t28.screenOffset = e33.screenOffset || f11, t28.centerOffsetUnits = e33.centerOffsetUnits || "world", t28;
  }
  get _materialParameters() {
    const r30 = new S9(), a35 = this.symbol, n26 = a35.callout;
    if (r30.color = r(n26.color) ? l11.toUnitRGBA(n26.color) : [0, 0, 0, 0], r30.color[3] *= this._getLayerOpacity(), r30.size = u6(n26.size || 0), a35.verticalOffset) {
      const { screenLength: e33, minWorldLength: n27, maxWorldLength: s27 } = a35.verticalOffset;
      r30.verticalOffset = { screenLength: u6(e33), minWorldLength: n27 || 0, maxWorldLength: r(s27) ? s27 : 1 / 0 };
    }
    r30.borderColor = r(n26.border) && r(n26.border.color) ? l11.toUnitRGBA(n26.border.color) : null;
    const s26 = "object" === a35.symbolLayers.getItemAt(0).type, o24 = "label-3d" === a35.type;
    return r30.occlusionTest = !s26, r30.shaderPolygonOffset = s26 ? 0 : void 0, r30.depthHUDAlignStart = o24, r30.hasSlicePlane = this._context.slicePlaneEnabled, r30.screenSizePerspective = this._context.screenSizePerspectiveEnabled ? this._context.sharedResources.screenSizePerspectiveSettings : null, r30;
  }
  _defaultElevationInfoNoZ() {
    return L4;
  }
  createGraphics3DGraphic(e33) {
    const t28 = e33.renderingInfo, i19 = e33.graphic, a35 = this.setGraphicElevationContext(i19, new h15(), t28.elevationOffset || 0), n26 = t28.symbol, s26 = "on-the-ground" === this._elevationContext.mode && ("cim" === n26.type || !n26.symbolLayers.some((e34) => "object" === e34.type || "text" === e34.type));
    if ("label-3d" !== n26.type && s26) return null;
    if ("point-3d" === n26.type && n26.symbolLayers.every((e34) => "text" === e34.type && !l12(e34))) return null;
    const o24 = A5(i19.geometry);
    return t(o24) ? null : this._createAs3DShape(o24, a35, t28, i19.uid);
  }
  layerOpacityChanged() {
    r(this._material) && this._material.setParameters(this._materialParameters);
  }
  layerElevationInfoChanged(e33, r30, i19) {
    const a35 = this._elevationContext.mode, n26 = m7(_w.elevationModeChangeTypes, i19, a35);
    return n26 !== j8.UPDATE || e33.forEach((e34) => {
      const i20 = r30(e34);
      r(i20) && this.updateGraphicElevationContext(e34.graphic, i20);
    }), n26;
  }
  slicePlaneEnabledChanged() {
    return t(this._material) || this._material.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled }), true;
  }
  physicalBasedRenderingChanged() {
    return true;
  }
  pixelRatioChanged() {
    return true;
  }
  skipHighSymbolLodsChanged() {
    return true;
  }
  setGraphicElevationContext(e33, t28, r30 = 0) {
    const i19 = super.setGraphicElevationContext(e33, t28);
    return i19.addOffsetRenderUnits(r30), i19;
  }
  updateGraphicElevationContext(e33, r30) {
    this.setGraphicElevationContext(e33, r30.elevationContext, r(r30.metadata) ? r30.metadata.elevationOffset : 0), r30.needsElevationUpdates = p12(r30.elevationContext.mode);
  }
  computeComplexity() {
    return { primitivesPerFeature: 2, primitivesPerCoordinate: 0, drawCallsPerFeature: 0, estimated: false, memory: d15.memory };
  }
  _createVertexData(e33) {
    const { translation: t28, centerOffset: r30 } = e33, i19 = new s7(t28 ? [t28[0], t28[1], t28[2]] : [0, 0, 0], 3, true), a35 = new s7(r30 ? [r30[0], r30[1], r30[2], r30[3]] : [0, 0, 0, 1], 4, true);
    return [[O4.POSITION, i19], [O4.NORMAL, new s7([0, 0, 1], 3, true)], [O4.AUXPOS1, a35]];
  }
  _getOrCreateMaterial(e33) {
    const i19 = this._perInstanceMaterialParameters(e33), a35 = g11.uniqueMaterialIdentifier(i19);
    if (r(this._material) && a35 === this._material.uniqueMaterialIdentifier) return { material: this._material, isUnique: false };
    if (r(e33.materialCollection)) {
      let t28 = e33.materialCollection.get(a35);
      return t(t28) && (t28 = new g11(i19), e33.materialCollection.add(a35, t28)), { material: t28, isUnique: false };
    }
    return { material: new g11(i19), isUnique: true };
  }
  _createAs3DShape(e33, t28, i19, a35) {
    const n26 = this._context.layer.uid, s26 = this._context.stage.renderView.getObjectAndLayerIdColor({ graphicUid: a35, layerUid: n26 }), o24 = this._getOrCreateMaterial(i19), l34 = new v8(o24.material, this._createVertexData(i19), G5, null, e13.Point, s26), m18 = p18(this._context, e33, l34, t28, a35);
    if (t(m18)) return null;
    const h25 = new S8(this, m18.object, [l34], o24.isUnique ? [o24.material] : null, null, d13, t28);
    return h25.metadata = new e21(i19.elevationOffset), h25.alignedSampledElevation = m18.sampledElevation, h25.needsElevationUpdates = p12(t28.mode), m12(h25, e33, this._context.elevationProvider), h25;
  }
};
w7.elevationModeChangeTypes = { definedChanged: j8.UPDATE, staysOnTheGround: j8.UPDATE, onTheGroundChanged: j8.RECREATE };
var A8 = [0];
var G5 = [[O4.POSITION, A8], [O4.NORMAL, A8], [O4.AUXPOS1, A8]];
var L4 = { mode: "relative-to-ground", offset: 0 };
var S10 = { ignoreDrivers: true, renderPriority: 0, renderPriorityStep: 1 };
var D5 = class {
  constructor(e33, t28, r30 = n5(), i19 = n6(), a35 = n13(), l34 = "world", c34 = 0, m18 = null) {
    this.renderer = e33, this.symbol = t28, this.translation = r30, this.centerOffset = i19, this.screenOffset = a35, this.centerOffsetUnits = l34, this.elevationOffset = c34, this.materialCollection = m18;
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DCalloutSymbolLayerFactory.js
var t23 = s.getLogger("esri.views.3d.layers.graphics.Graphics3DCalloutSymbolLayerFactory");
function e25(o24, l34) {
  if (!i6(o24)) return t23.error("Graphics3DCalloutSymbolLayerFactory#make", `symbol of type '${o24.type}' does not support callouts`), null;
  if (!o24.callout) return null;
  const e33 = a29[o24.callout.type];
  return e33 ? new e33(o24, l34) : (t23.error("Graphics3DCalloutSymbolLayerFactory#make", `unknown or unsupported callout type ${o24.callout.type}`), null);
}
var a29 = { line: w7 };

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DGraphic.js
var S11 = new e3(Array, (e33) => P2(e33, H2), null, 10, 5);
var B3 = u4();
var V6 = class {
  get labelLayers() {
    return this._labelLayers;
  }
  get extent() {
    return this._extent;
  }
  constructor(e33, i19, r30, s26, a35) {
    this.graphic = e33, this.graphics3DSymbol = i19, this.layers = r30, this._labelLayers = new Array(), this._auxiliaryLayers = new Array(), this._visibilityFlags = z4(E13._COUNT, C4._COUNT), this._featureExpressionFeature = null, this._optimizedGeometry = { geometry: null, hasZ: false, hasM: false }, this._extent = null, this.isElevationSource = false, ++i19.referenced, this._featureExpressionFeature = a35 ? s13(a35, e33, s26) : null;
    for (const o24 of r30) r(o24) && (this.isElevationSource = this.isElevationSource || o24.isElevationSource);
  }
  initialize(e33, i19) {
    this._layer = i19, this._stage = e33, this._forEachSymbolLayerGraphic((t28) => {
      t28.initialize(e33, i19), t28.setVisibility(this.isVisible());
    });
  }
  destroy() {
    this._forEachSymbolLayerGraphic((e33) => e33.destroy()), this.layers = null, this._auxiliaryLayers = null, --this.graphics3DSymbol.referenced, this.graphics3DSymbol = null;
  }
  get destroyed() {
    return null == this.layers;
  }
  clearLabelGraphics() {
    this._forEachLabelGraphic((e33) => e33.destroy()), this._labelLayers.length = 0;
  }
  addLabelGraphic(e33, i19, t28) {
    this._labelLayers.push(e33), e33.initialize(i19, t28), e33.setVisibility(this.isVisible(E13.LABEL));
  }
  addAuxiliaryGraphic(e33) {
    this._auxiliaryLayers.push(e33), this._layer && (e33.initialize(this._stage, this._layer), e33.setVisibility(this.isVisible()));
  }
  get isDraped() {
    let e33 = false;
    return this._forEachSymbolLayerGraphic((i19) => {
      "draped" === i19.type && (e33 = true);
    }), e33;
  }
  isVisible(e33 = E13.GRAPHIC, i19) {
    for (let t28 = 0; t28 <= e33; t28++) {
      const e34 = this._visibilityFlags[t28];
      for (let t29 = 0; t29 < e34.length; ++t29) if (false === e34[t29] && t29 !== i19) return false;
    }
    return true;
  }
  hasVisibilityFlag(e33, i19) {
    return null != this._visibilityFlags[i19][e33];
  }
  setVisibilityFlag(e33, i19, t28) {
    const r30 = this.isVisible(t28);
    this._visibilityFlags[t28][e33] = i19;
    const s26 = this.isVisible(t28);
    if (r30 === s26) return false;
    if (t28 === E13.LABEL) this._forEachLabelGraphic((e34) => e34.setVisibility(s26));
    else {
      this._forEachSymbolLayerGraphic((e35) => e35.setVisibility(s26));
      const e34 = this.isVisible(E13.LABEL);
      this._forEachLabelGraphic((i20) => i20.setVisibility(e34));
    }
    return true;
  }
  clearVisibilityFlag(e33, i19 = E13.GRAPHIC) {
    return this.setVisibilityFlag(e33, void 0, i19);
  }
  computeExtent(e33) {
    if (!this._extent) {
      const i19 = this.graphic.geometry;
      if (t(i19)) return false;
      this._extent = u4(), v6(i19, this._extent);
      const t28 = i19.spatialReference;
      if (!E2(t28, e33) && !vn(this._extent, t28, this._extent, e33)) return this._extent = null, false;
    }
    return true;
  }
  getAsOptimizedGeometry(e33, i19) {
    return r(this._optimizedGeometry.geometry) && this._optimizedGeometry.hasZ === e33 && this._optimizedGeometry.hasM === i19 || (this._optimizedGeometry.geometry = this._convertGraphicToOptimizedGeometry(this.graphic, e33, i19), this._optimizedGeometry.hasZ = e33, this._optimizedGeometry.hasM = i19), this._optimizedGeometry.geometry;
  }
  _convertGraphicToOptimizedGeometry(e33, i19, t28) {
    let r30 = e33.geometry;
    return "mesh" !== r30.type && "extent" !== r30.type || (r30 = v3.fromExtent("mesh" === r30.type ? r30.extent : r30)), ct(r30, i19, t28);
  }
  get usedMemory() {
    let e33 = t8(this.graphic.attributes);
    return this._forEachSymbolLayerGraphic((i19) => {
      const t28 = i19.graphics3DSymbolLayer.complexity;
      if (t(t28)) return;
      const s26 = "draped" === i19.type ? t28.memory.draped : t28.memory;
      e33 += s26.bytesPerFeature, s26.bytesPerCoordinate && (e33 += z2(this.graphic.geometry) * s26.bytesPerCoordinate);
    }), e33;
  }
  computeAttachmentOrigin() {
    const e33 = { render: { origin: n5(), num: 0 }, draped: { origin: n13(), num: 0 } };
    for (const i19 of this.layers) t(i19) || i19.computeAttachmentOrigin(e33);
    return e33.render.num > 1 && g2(e33.render.origin, e33.render.origin, 1 / e33.render.num), e33.draped.num > 1 && l9(e33.draped.origin, e33.draped.origin, 1 / e33.draped.num), e33;
  }
  async getProjectedBoundingBox(i19, t28, s26, a35, o24) {
    return o24 || (o24 = { boundingBox: null, requiresDrapedElevation: false, screenSpaceObjects: [] }), o24.boundingBox ? S(o24.boundingBox) : o24.boundingBox = S(), o24.requiresDrapedElevation = false, await c4(this.layers, async (e33) => {
      if (t(e33)) return;
      const n26 = "draped" === e33.type ? t28 : i19, h25 = S11.acquire(), l34 = await e33.getProjectedBoundingBox(n26, s26, o24.screenSpaceObjects, a35, h25);
      isFinite(l34[2]) && isFinite(l34[5]) || (o24.requiresDrapedElevation = true), l34 && f4(o24.boundingBox, h25), S11.release(h25);
    }), I(o24.boundingBox) || M2(Z(o24.boundingBox, B3)) ? o24 : null;
  }
  needsElevationUpdates() {
    for (const e33 of this.layers) if (r(e33) && ("object3d" === e33.type || "lod-instance" === e33.type) && e33.needsElevationUpdates) return true;
    for (const e33 of this._labelLayers) if (e33 && e33.needsElevationUpdates) return true;
    return false;
  }
  alignWithElevation(e33, i19, t28) {
    this._forEachRenderedGraphic((r30) => {
      "object3d" !== r30.type && "lod-instance" !== r30.type || r30.alignWithElevation(e33, i19, this._featureExpressionFeature, t28);
    });
  }
  alignWithAbsoluteElevation(e33, i19, t28) {
    this._forEachRenderedGraphic((r30) => {
      "object3d" === r30.type && r30.alignWithAbsoluteElevation(e33, i19, t28);
    });
  }
  addObjectStateSet(e33, i19) {
    this._forEachSymbolLayerGraphic((t28) => t28.addObjectState(e33, i19));
  }
  removeObjectState(e33) {
    this._forEachSymbolLayerGraphic((i19) => i19.removeObjectState(e33));
  }
  _forEachGraphicList(e33, i19) {
    e33.forEach((e34) => e34 && i19(e34));
  }
  _forEachSymbolLayerGraphic(e33) {
    this._forEachGraphicList(this.layers, e33), this._forEachGraphicList(this._auxiliaryLayers, e33);
  }
  _forEachLabelGraphic(e33) {
    this._forEachGraphicList(this._labelLayers, e33);
  }
  _forEachRenderedGraphic(e33) {
    this._forEachSymbolLayerGraphic(e33), this._forEachLabelGraphic(e33);
  }
};
function z4(e33, i19) {
  const t28 = new Array(e33);
  for (let r30 = 0; r30 < t28.length; r30++) t28[r30] = new Array(i19);
  return t28;
}

// node_modules/@arcgis/core/views/3d/layers/graphics/polygonUtils.js
function s18(o24) {
  const a35 = [[O4.POSITION, o24.indices]], s26 = [[O4.POSITION, new s7(o24.attributeData.position, 3, true)]];
  return r(o24.attributeData.color) && (s26.push([O4.COLOR, new s7(o24.attributeData.color, 4, true)]), a35.push([O4.COLOR, new Array(o24.indices.length).fill(0)])), r(o24.attributeData.uvMapSpace) && (s26.push([O4.UVMAPSPACE, new s7(o24.attributeData.uvMapSpace, 4, true)]), a35.push([O4.UVMAPSPACE, o24.indices])), r(o24.attributeData.boundingRect) && (s26.push([O4.BOUNDINGRECT, new s7(o24.attributeData.boundingRect, 9, true)]), a35.push([O4.BOUNDINGRECT, o24.indices])), new v8(o24.material, s26, a35, o24.mapPositions, e13.Mesh, o24.attributeData.objectAndLayerIdColor);
}
function u22(t28) {
  const i19 = [[O4.POSITION, t28.indices], [O4.UV0, t28.indices]], o24 = [[O4.POSITION, new s7(t28.attributeData.position, 3, true)], [O4.UV0, new s7(t28.attributeData.uv0, 2, true)]];
  return new v8(t28.material, o24, i19, t28.mapPositions);
}
function c24(t28) {
  switch (t28.type) {
    case "extent":
      if (t28 instanceof w4) return v3.fromExtent(t28);
      break;
    case "polygon":
      return t28;
  }
  return null;
}
var p19 = class {
  constructor(t28, e33, i19) {
    this.renderData = t28, this.layerUid = e33, this.graphicUid = i19, this.outGeometries = new Array();
  }
};

// node_modules/@arcgis/core/views/3d/support/renderInfoUtils/polygon.js
function c25(o24, t28, e33, p23) {
  const c34 = l13(o24.rings, !!o24.hasZ, f9.CCW_IS_HOLE), l34 = n10(c34.position.length), a35 = c15(c34.position, o24.spatialReference, 0, l34, 0, c34.position, 0, c34.position.length / 3, t28, e33, p23), m18 = null != a35;
  return new d17(c34.position, l34, h18(c34.polygons, c34.position, l34), u23(c34.outlines, c34.position, l34), m18, a35);
}
function l25(o24, i19) {
  const r30 = l13(o24.rings, false, f9.CCW_IS_HOLE), p23 = xn(r30.position, o24.spatialReference, 0, r30.position, i19, 0, r30.position.length / 3);
  for (let t28 = 2; t28 < r30.position.length; t28 += 3) r30.position[t28] = ce;
  return { position: r30.position, polygons: h18(r30.polygons, r30.position), outlines: u23(r30.outlines, r30.position), projectionSuccess: p23 };
}
function u23(t28, n26, s26 = null) {
  return t28.filter(({ count: o24 }) => o24 > 1).map(({ index: t29, count: i19 }) => {
    const e33 = 3 * t29, r30 = 3 * i19;
    return r(s26) ? new m13(t29, i19, a11(n26, e33, r30), a11(s26, e33, r30)) : new a30(t29, i19, a11(n26, e33, r30));
  });
}
function h18(t28, n26, s26 = null) {
  const i19 = (r(s26), new Array());
  for (const { index: e33, count: r30, holeIndices: c34, pathLengths: l34 } of t28) {
    if (r30 <= 1) continue;
    const t29 = 3 * e33, u32 = 3 * r30, h25 = c34.map((o24) => o24 - e33), a35 = r(s26) ? new g12(e33, r30, a11(n26, 3 * e33, 3 * r30), a11(s26, t29, u32), h25, l34) : new f21(e33, r30, a11(n26, 3 * e33, 3 * r30), h25, l34);
    i19.push(a35);
  }
  return i19;
}
var a30 = class {
  constructor(o24, t28, n26) {
    this.index = o24, this.count = t28, this.position = n26;
  }
};
var m13 = class extends a30 {
  constructor(o24, t28, n26, s26) {
    super(o24, t28, n26), this.mapPositions = s26;
  }
};
var g12 = class extends m13 {
  constructor(o24, t28, n26, s26, i19, e33) {
    super(o24, t28, n26, s26), this.holeIndices = i19, this.pathLengths = e33;
  }
};
var f21 = class extends a30 {
  constructor(o24, t28, n26, s26, i19) {
    super(o24, t28, n26), this.holeIndices = s26, this.pathLengths = i19;
  }
};
var d17 = class {
  constructor(o24, t28, n26, s26, i19, e33) {
    this.position = o24, this.mapPositions = t28, this.polygons = n26, this.outlines = s26, this.projectionSuccess = i19, this.sampledElevation = e33;
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DExtrudeSymbolLayer.js
var $4 = ["polygon", "extent"];
var ee2 = class extends y7 {
  constructor(e33, t28, r30, s26) {
    super(e33, t28, r30, s26), this.ensureDrapedStatus(false);
  }
  async doLoad() {
    if (!this._drivenProperties.size) {
      const t29 = S5(this._getSymbolSize());
      if (t29) throw new s2("graphics3dextrudesymbollayer:invalid-size", t29);
    }
    const t28 = x(this.symbolLayer, "material", "color"), s26 = this._getCombinedOpacityAndColor(t28), n26 = e4(s26), i19 = s26[3], o24 = i19 < 1 || this.needsDrivenTransparentPass, a35 = { usePBR: this._context.physicalBasedRenderingEnabled, isSchematic: true, diffuse: n26, ambient: n26, opacity: i19, transparent: o24, cullFace: o24 ? n11.None : n11.Back, hasVertexColors: true, hasSlicePlane: this._context.slicePlaneEnabled, castShadows: this.symbolLayer.castShadows, offsetTransparentBackfaces: true };
    this._material = new E11(a35), this._bottomMaterial = new E11({ ...a35, cullFace: n11.Back }), this._context.stage.add(this._material), this._context.stage.add(this._bottomMaterial);
  }
  destroy() {
    super.destroy(), this._material && (this._context.stage.remove(this._material), this._context.stage.remove(this._bottomMaterial));
  }
  createGraphics3DGraphic(e33) {
    const t28 = e33.graphic;
    if (!this._validateGeometry(t28.geometry, $4, this.symbolLayer.type)) return null;
    const r30 = this._getVertexOpacityAndColor(e33.renderingInfo, 255), s26 = this.setGraphicElevationContext(t28, new h15());
    return this._createAs3DShape(t28, e33.renderingInfo, r30, s26, t28.uid);
  }
  layerOpacityChanged(e33, t28) {
    const n26 = x(this.symbolLayer, "material", "color"), i19 = this._getCombinedOpacity(n26), o24 = i19 < 1 || this.needsDrivenTransparentPass;
    this._material.setParameters({ opacity: i19, transparent: o24 }), this._bottomMaterial.setParameters({ opacity: i19, transparent: o24 });
    const a35 = this._getLayerOpacity();
    e33.forEach((e34) => {
      const r30 = t28(e34);
      r(r30) && r30.layerOpacityChanged(a35, this._context.isAsync);
    });
  }
  layerElevationInfoChanged(e33, t28) {
    return this.updateGraphics3DGraphicElevationInfo(e33, t28, g7);
  }
  slicePlaneEnabledChanged(e33, t28) {
    return this._material.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled }), this._bottomMaterial.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled }), e33.forEach((e34) => {
      const r30 = t28(e34);
      r(r30) && r30.slicePlaneEnabledChanged(this._context.slicePlaneEnabled, this._context.isAsync);
    }), true;
  }
  physicalBasedRenderingChanged() {
    return this._material.setParameters({ usePBR: this._context.physicalBasedRenderingEnabled, isSchematic: true }), this._bottomMaterial.setParameters({ usePBR: this._context.physicalBasedRenderingEnabled, isSchematic: true }), true;
  }
  pixelRatioChanged() {
    return true;
  }
  skipHighSymbolLodsChanged() {
    return true;
  }
  _getExtrusionSize(e33) {
    let t28;
    return t28 = e33.size && this._drivenProperties.size ? u17(e33.size, 2) ?? 0 : this._getSymbolSize(), t28 /= this._context.renderCoordsHelper.unitInMeters, t28;
  }
  applyRendererDiff(e33, t28) {
    return this._drivenPropertiesChanged(t28) ? e22.Recreate_Symbol : e22.Recreate_Graphics;
  }
  async queryForSnapping(e33, r30, s26, n26) {
    const o24 = this._getExtrusionSize(s26) * this._context.renderCoordsHelper.unitInMeters / L(r30), { objectId: a35, target: l34 } = e33, c34 = p2(l34);
    switch (c34.z = (c34.z ?? 0) + o24, e33.type) {
      case "edge": {
        const { start: r31, end: s27 } = e33, n27 = p2(r31), i19 = p2(s27);
        return n27.z = (n27.z ?? 0) + o24, i19.z = (i19.z ?? 0) + o24, [e8(a35, c34, 1 / 0, n27, i19)];
      }
      case "vertex":
        return [t6(a35, c34, 1 / 0), e8(a35, l34, 1 / 0, l34, c34)];
      default:
        return [];
    }
  }
  _getSymbolSize() {
    return this.symbolLayer.size ?? 1;
  }
  _createAs3DShape(e33, t28, r30, i19, h25) {
    const p23 = c24(e33.geometry);
    if (t(p23)) return null;
    if (0 === p23.rings.length || !p23.rings.some((e34) => e34.length > 0)) return this._logGeometryValidationWarnings(p23.rings, "rings", "ExtrudeSymbol3DLayer"), null;
    const d26 = c25(p23, this._context.elevationProvider, this._context.renderCoordsHelper, i19);
    this._logGeometryCreationWarnings(d26, p23.rings, "rings", "ExtrudeSymbol3DLayer");
    const g23 = A5(p23);
    if (t(g23)) return null;
    const f27 = new Array(), u32 = a7(), y14 = e11(), b24 = n5(), A13 = this._context.renderCoordsHelper.viewingMode === l15.Global;
    A13 || this._context.renderCoordsHelper.worldUpAtPosition(null, b24), Zn(p23.spatialReference, [g23.x, g23.y, 0], y14, this._context.renderCoordsHelper.spatialReference);
    const C11 = e11();
    h6(C11, y14);
    const M13 = e10();
    g3(M13, C11);
    const { polygons: B7, mapPositions: R12, position: T10 } = d26, D10 = T10.length / 3, U9 = new Float64Array(3 * D10 * 6), F11 = new Float64Array(3 * D10 * 6), V11 = new Float64Array(3 * D10 * 6), Y6 = new Float64Array(1 * D10 * 6);
    let W6 = 0;
    for (let s26 = 0; s26 < B7.length; ++s26) {
      const e34 = B7[s26], n26 = e34.count;
      if (this._context.clippingExtent && (S(u32), M3(u32, e34.mapPositions), !Y(u32, this._context.clippingExtent))) continue;
      const i20 = r12(e34.mapPositions, e34.holeIndices, 3);
      if (0 === i20.length) continue;
      const a35 = 3 * n26 * 2 + i20.length, l34 = new Array(a35), c34 = new Array(i20.length), m18 = 6 * n26, p24 = 3 * U9.BYTES_PER_ELEMENT, d27 = new T(U9.buffer, W6 * p24, p24, (W6 + m18) * p24), g24 = 3 * F11.BYTES_PER_ELEMENT, y15 = new T(F11.buffer, W6 * g24, g24, (W6 + m18) * g24), _21 = new Float64Array(V11.buffer, 3 * W6 * V11.BYTES_PER_ELEMENT, 3 * m18), E21 = new Float64Array(Y6.buffer, 1 * W6 * Y6.BYTES_PER_ELEMENT, 1 * m18), x18 = this._getExtrusionSize(t28);
      re(T10, R12, i20, e34, d27.typedBuffer, _21, y15.typedBuffer, E21, l34, c34, x18, b24, A13), t7(d27, d27, C11), r10(y15, y15, M13), W6 += 6 * n26;
      const I13 = this._context.stage.renderView.getObjectAndLayerIdColor({ graphicUid: h25, layerUid: this._context.layer.uid }), O13 = new xe(d27.typedBuffer, _21, y15.typedBuffer, E21);
      f27.push(te2(this._material, l34, l34.length - c34.length, O13, r30, I13)), f27.push(te2(this._bottomMaterial, c34, 0, O13, r30, I13));
    }
    if (0 === f27.length) return null;
    const Z4 = new x7({ geometries: f27, metadata: { layerUid: this._context.layer.uid, graphicUid: h25, isElevationSource: true } });
    Z4.transformation = y14;
    const q14 = f10(this.symbolLayer, { opacity: this._getLayerOpacity() }), J6 = r(q14) ? { baseMaterial: this._material, edgeMaterials: [q14], properties: { mergeGeometries: true, hasSlicePlane: this._context.slicePlaneEnabled } } : null, Q3 = new S8(this, Z4, f27, null, null, ge, i19, J6);
    return Q3.alignedSampledElevation = d26.sampledElevation, Q3.needsElevationUpdates = g7(i19.mode), Q3;
  }
};
function te2(e33, t28, r30, s26, n26, i19) {
  const o24 = new Array(t28.length).fill(0), a35 = [[O4.POSITION, new s7(s26.positions, 3, true)], [O4.NORMAL, new s7(s26.normals, 3, true)], [O4.COLOR, new s7(n26, 4, true)], [O4.SIZE, new s7(s26.heights, 1, true)]], l34 = [[O4.POSITION, t28], [O4.NORMAL, t28], [O4.COLOR, o24]];
  return new v8(e33, a35, l34, s26.elevation, e13.Mesh, i19, r30);
}
function re(e33, t28, r30, s26, n26, i19, o24, a35, l34, c34, h25, m18, p23) {
  const d26 = r30.length / 3;
  let g23 = 0, f27 = 2 * s26.count;
  se(e33, t28, s26.index, s26.count, r30, 0, d26, n26, i19, o24, a35, l34, c34, f27, h25, m18, p23);
  let u32 = 2 * s26.count;
  f27 = 0, oe(n26, i19, a35, o24, g23, s26.pathLengths[0], s26.count, u32, l34, f27, h25), u32 += 4 * s26.pathLengths[0], f27 += 2 * s26.pathLengths[0], g23 += s26.pathLengths[0];
  for (let y14 = 1; y14 < s26.pathLengths.length; ++y14) oe(n26, i19, a35, o24, g23, s26.pathLengths[y14], s26.count, u32, l34, f27, h25), u32 += 4 * s26.pathLengths[y14], f27 += 2 * s26.pathLengths[y14], g23 += s26.pathLengths[y14];
}
function se(e33, t28, r30, s26, n26, i19, o24, a35, l34, c34, h25, m18, g23, f27, u32, y14, b24) {
  r4(ue, y14);
  const _21 = u32 > 0 ? 1 : -1;
  let E21 = 3 * r30, x18 = 0, j12 = 3 * x18, w12 = s26, S19 = 3 * w12;
  for (let p23 = 0; p23 < s26; ++p23) b24 && (ue[0] = e33[E21 + 0], ue[1] = e33[E21 + 1], ue[2] = e33[E21 + 2], z(ue, ue)), a35[j12 + 0] = e33[E21 + 0], a35[j12 + 1] = e33[E21 + 1], a35[j12 + 2] = e33[E21 + 2], l34[j12 + 0] = t28[E21 + 0], l34[j12 + 1] = t28[E21 + 1], l34[j12 + 2] = t28[E21 + 2], c34[j12 + 0] = -_21 * ue[0], c34[j12 + 1] = -_21 * ue[1], c34[j12 + 2] = -_21 * ue[2], h25[x18] = 0, a35[S19 + 0] = e33[E21 + 0] + u32 * ue[0], a35[S19 + 1] = e33[E21 + 1] + u32 * ue[1], a35[S19 + 2] = e33[E21 + 2] + u32 * ue[2], l34[S19 + 0] = t28[E21 + 0], l34[S19 + 1] = t28[E21 + 1], l34[S19 + 2] = t28[E21 + 2], c34[S19 + 0] = _21 * ue[0], c34[S19 + 1] = _21 * ue[1], c34[S19 + 2] = _21 * ue[2], h25[w12] = u32, j12 += 3, S19 += 3, E21 += 3, x18 += 1, w12 += 1;
  E21 = 3 * i19, j12 = 0, S19 = 3 * f27;
  const P10 = u32 < 0 ? _e : be, v21 = u32 < 0 ? be : _e;
  for (let p23 = 0; p23 < o24; ++p23) g23[j12 + 0] = n26[E21 + P10[0]], g23[j12 + 1] = n26[E21 + P10[1]], g23[j12 + 2] = n26[E21 + P10[2]], m18[S19 + 0] = n26[E21 + v21[0]] + s26, m18[S19 + 1] = n26[E21 + v21[1]] + s26, m18[S19 + 2] = n26[E21 + v21[2]] + s26, j12 += 3, S19 += 3, E21 += 3;
}
function ne(e33, t28, r30, s26, n26, i19, o24) {
  s26[i19] = s26[o24], o24 *= 3, e33[(i19 *= 3) + 0] = e33[o24 + 0], e33[i19 + 1] = e33[o24 + 1], e33[i19 + 2] = e33[o24 + 2], t28[i19 + 0] = t28[o24 + 0], t28[i19 + 1] = t28[o24 + 1], t28[i19 + 2] = t28[o24 + 2], r30[i19 + 0] = n26[0], r30[i19 + 1] = n26[1], r30[i19 + 2] = n26[2];
}
var ie = n5();
function oe(e33, t28, r30, s26, n26, i19, o24, a35, l34, c34, h25) {
  let m18 = n26, p23 = n26 + 1, d26 = n26 + o24, g23 = n26 + o24 + 1, f27 = a35, u32 = a35 + 1, y14 = a35 + 2 * i19, b24 = a35 + 2 * i19 + 1;
  h25 < 0 && (m18 = n26 + o24 + 1, g23 = n26), c34 *= 3;
  for (let _21 = 0; _21 < i19; ++_21) _21 === i19 - 1 && (h25 > 0 ? (p23 = n26, g23 = n26 + o24) : (p23 = n26, m18 = n26 + o24)), pe(e33, m18, p23, d26, ie), ne(e33, t28, s26, r30, ie, f27, m18), ne(e33, t28, s26, r30, ie, u32, p23), ne(e33, t28, s26, r30, ie, y14, d26), ne(e33, t28, s26, r30, ie, b24, g23), l34[c34++] = f27, l34[c34++] = y14, l34[c34++] = b24, l34[c34++] = f27, l34[c34++] = b24, l34[c34++] = u32, m18++, p23++, d26++, g23++, f27 += 2, u32 += 2, y14 += 2, b24 += 2;
}
var ae = n5();
var le = n5();
var ce2 = n5();
var he = n5();
var me = n5();
function pe(e33, t28, r30, s26, n26) {
  t28 *= 3, r30 *= 3, s26 *= 3, o2(ae, e33[t28++], e33[t28++], e33[t28++]), o2(le, e33[r30++], e33[r30++], e33[r30++]), o2(ce2, e33[s26++], e33[s26++], e33[s26++]), e5(he, le, ae), e5(me, ce2, ae), _(n26, me, he), z(n26, n26);
}
var de = n5();
function ge(e33, t28, r30, n26, i19) {
  const o24 = e33.stageObject, a35 = o24.geometries, l34 = a35.length, c34 = "absolute-height" !== t28.mode;
  let p23 = 0;
  const d26 = o24.transformation, f27 = e7(e11(), d26);
  for (let h25 = 0; h25 < l34; h25 += 2) {
    const e34 = a35[h25];
    if (!t20(e34)) continue;
    const t29 = e34.getMutableAttribute(O4.POSITION).data, l35 = e34.vertexAttributes.get(O4.SIZE).data, m18 = new t19(e34.mapPositions), u32 = t29.length / 3;
    let b24 = 0, _21 = false, E21 = 0;
    for (let o25 = 0; o25 < u32; o25++) {
      de[0] = t29[b24], de[1] = t29[b24 + 1], de[2] = t29[b24 + 2], n26(m18, ye), c34 && (E21 += ye.sampledElevation), t17.TESTS_DISABLE_OPTIMIZATIONS ? (o2(fe, m18.array[m18.offset + 0], m18.array[m18.offset + 1], ye.z + l35[b24 / 3]), r(r30) && i19.toRenderCoords(fe, r30, fe), O2(fe, fe, f27)) : (o2(fe, t29[b24 + 0], t29[b24 + 1], t29[b24 + 2]), O2(fe, fe, d26), i19.setAltitude(fe, ye.z + l35[b24 / 3]), O2(fe, fe, f27)), t29[b24] = fe[0], t29[b24 + 1] = fe[1], t29[b24 + 2] = fe[2];
      const e35 = Ee / i19.unitInMeters;
      (Math.abs(de[0] - t29[b24]) >= e35 || Math.abs(de[1] - t29[b24 + 1]) >= e35 || Math.abs(de[2] - t29[b24 + 2]) >= e35) && (_21 = true), m18.offset += 3, b24 += 3;
    }
    _21 && (e34.invalidateBoundingInfo(), o24.geometryVertexAttrsUpdated(a35[h25]), a35[h25 + 1].invalidateBoundingInfo(), o24.geometryVertexAttrsUpdated(a35[h25 + 1])), p23 += E21 / u32;
  }
  return p23 / l34;
}
var fe = n5();
var ue = n5();
var ye = new T4();
var be = [0, 2, 1];
var _e = [0, 1, 2];
var Ee = 0.01;
var xe = class {
  constructor(e33, t28, r30, s26) {
    this.positions = e33, this.elevation = t28, this.normals = r30, this.heights = s26;
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DDrapedGraphicLayer.js
var l26 = class {
  constructor(e33, t28, r30, i19) {
    this.graphics3DSymbolLayer = e33, this.renderGeometries = t28, this.boundingBox = r30, this._drapeSourceRenderer = i19, this.type = "draped", this.stage = null, this._visible = false, this._addedToStage = false, this.isElevationSource = false;
  }
  initialize(e33) {
    this.stage = e33;
  }
  setVisibility(e33) {
    if (null != this.stage && this._visible !== e33) {
      if (this._visible = e33, e33 && !this._addedToStage) return this._addedToStage = true, void this._drapeSourceRenderer.addGeometries(this.renderGeometries, E12.ADD);
      if (e33 || this._addedToStage) {
        for (const e34 of this.renderGeometries) e34.visible = this._visible;
        this._drapeSourceRenderer.modifyGeometries(this.renderGeometries, I5.VISIBILITY);
      }
    }
  }
  destroy() {
    this.stage && this._addedToStage && this._drapeSourceRenderer.removeGeometries(this.renderGeometries, E12.REMOVE), this._addedToStage = false, this._visible = false, this.stage = null;
  }
  getCenterObjectSpace(e33 = n5()) {
    return o2(e33, 0, 0, 0);
  }
  getBoundingBoxObjectSpace(e33 = a7()) {
    return S(e33);
  }
  addObjectState(e33, t28) {
    e33 === t12.Highlight && (this.renderGeometries.forEach((e34) => {
      const r30 = e34.geometry.addHighlight();
      t28.addRenderGeometry(e34, r30, this);
    }), this._addedToStage && this._drapeSourceRenderer.modifyGeometries(this.renderGeometries, I5.HIGHLIGHT));
  }
  removeObjectState(e33) {
    this.renderGeometries.forEach((t28) => {
      e33.removeRenderGeometry(t28);
    });
  }
  removeRenderGeometryObjectState(e33, t28) {
    e33.geometry.removeHighlight(t28), this._addedToStage && this._drapeSourceRenderer.modifyGeometries(this.renderGeometries, I5.HIGHLIGHT);
  }
  computeAttachmentOrigin(e33) {
    for (const t28 of this.renderGeometries) t28.geometry.computeAttachmentOrigin(G6) && (e33.draped.origin[0] += G6[0], e33.draped.origin[1] += G6[1], e33.draped.num++);
  }
  async getProjectedBoundingBox(t28, r30, i19, n26, a35) {
    S(a35);
    for (let e33 = 0; e33 < this.renderGeometries.length; e33++) {
      const r31 = this.renderGeometries[e33];
      this._getRenderGeometryProjectedBoundingRect(r31, t28, p20, i19), h4(a35, p20);
    }
    if (r30) {
      let t29;
      E3(a35, G6);
      const i20 = X(a35, r30.service.spatialReference, r30);
      try {
        t29 = await r30.service.queryElevation(G6[0], G6[1], n26, i20, "ground");
      } catch (h25) {
      }
      r(t29) && (a35[2] = Math.min(a35[2], t29), a35[5] = Math.max(a35[5], t29));
    }
    return a35;
  }
  _getRenderGeometryProjectedBoundingRect(e33, t28, r30, i19) {
    if (this.boundingBox) P2(f22, this.boundingBox);
    else {
      const t29 = e33.boundingSphere, r31 = t29[3];
      f22[0] = t29[0] - r31, f22[1] = t29[1] - r31, f22[2] = t29[2] - r31, f22[3] = t29[0] + r31, f22[4] = t29[1] + r31, f22[5] = t29[2] + r31;
    }
    return t28(f22, 0, 2), this.calculateRelativeScreenBounds && i19.push({ location: E3(f22), screenSpaceBoundingRect: this.calculateRelativeScreenBounds() }), Z(f22, r30);
  }
};
var p20 = u4();
var f22 = a7();
var G6 = n5();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/TextRenderer.js
var n19 = class {
  constructor(t28, e33, i19, n26 = 2048) {
    this.text = t28, this._alignment = e33, this._parameters = i19, this._maxSize = n26, this._textWidths = [], this._lineWidths = [], this._renderPixelRatio = null, this._metricsCached = null, this.key = `TextRenderer-${this._parameters.key}-${this._alignment}--${t28}`, this._lines = t28.split(/\r?\n/);
  }
  get displayWidth() {
    return Math.ceil(this._displayWidth + 2 * this._backgroundHorizontalPadding);
  }
  get displayHeight() {
    const t28 = this._lineSpacing * (this._lines.length - 1), e33 = this._lineHeight;
    return Math.ceil(t28 + e33 + 2 * this._haloSize + this._backgroundTopPadding + this._backgroundBottomPadding);
  }
  get renderedWidth() {
    return Math.ceil(this._toRenderUnit(this.displayWidth));
  }
  get renderedHeight() {
    return Math.ceil(this._toRenderUnit(this.displayHeight));
  }
  get firstRenderedBaselinePosition() {
    return this._toRenderUnit(this._firstLineYOffset + this._baselinePosition);
  }
  get _firstLineYOffset() {
    return this._backgroundTopPadding + this._haloSize;
  }
  get _metrics() {
    if (t(this._metricsCached)) {
      const t28 = o19(h19, d18, d18).getContext("2d");
      this._setFontProperties(t28, this._fontSize);
      let e33 = 2 * this._haloSize;
      const i19 = this._parameters.definition.font;
      "italic" !== i19.style && "oblique" !== i19.style && "bold" !== i19.weight && "bolder" !== i19.weight || (e33 += 0.3 * t28.measureText("A").width), this._textWidths.length = 0, this._lineWidths.length = 0;
      let n26, s26, r30 = 0, a35 = 0, l34 = 0;
      this._lines.forEach((i20, o24) => {
        const h25 = t28.measureText(i20), d26 = h25.width, _21 = d26 + e33;
        this._textWidths.push(d26), this._lineWidths.push(_21), r30 = Math.max(r30, _21), a35 = Math.max(a35, h25.actualBoundingBoxAscent), l34 = Math.max(l34, h25.actualBoundingBoxDescent), 0 === o24 && (n26 = h25), o24 === this._lines.length - 1 && (s26 = h25);
      });
      const f27 = t28.font;
      let p23 = u24.get(f27);
      if (!p23) {
        const e34 = t28.measureText(_10);
        p23 = new g13(e34.actualBoundingBoxAscent, e34.actualBoundingBoxDescent), u24.set(f27, p23);
      }
      a35 = Math.max(a35, p23.actualBoundingBoxAscent), l34 = Math.max(l34, p23.actualBoundingBoxDescent);
      const m18 = a35 + l34, x18 = this._hasBackground ? n26.actualBoundingBoxAscent : a35, R12 = this._hasBackground ? s26.actualBoundingBoxDescent : l34;
      this._metricsCached = new c26(a35 - x18, l34 - R12, m18, r30, a35);
    }
    return this._metricsCached;
  }
  get _lineSpacing() {
    return (this._lineHeight + this._linePadding) * this._parameters.definition.lineSpacingFactor;
  }
  get _lineHeight() {
    return this._metrics.lineHeight;
  }
  get _linePadding() {
    return this._lineHeight * a31;
  }
  get _baselinePosition() {
    return this._metrics.baselinePosition;
  }
  get _renderedFontSize() {
    return this._toRenderUnit(this._fontSize);
  }
  get _fontSize() {
    return this._parameters.definition.size;
  }
  get _renderedHaloSize() {
    return this._toRenderUnit(this._haloSize);
  }
  get _haloSize() {
    return this._parameters.haloSize;
  }
  get _backgroundHorizontalPadding() {
    return this._hasBackground ? this._parameters.definition.background.padding[0] : 0;
  }
  get _backgroundVerticalPadding() {
    return this._hasBackground ? this._parameters.definition.background.padding[1] : 0;
  }
  get _backgroundTopPadding() {
    return Math.max(0, this._backgroundVerticalPadding - this._metrics.paddingTop);
  }
  get _backgroundBottomPadding() {
    return Math.max(0, this._backgroundVerticalPadding - this._metrics.paddingBottom);
  }
  get _hasBackground() {
    return !!this._parameters.backgroundStyle;
  }
  get renderPixelRatio() {
    if (t(this._renderPixelRatio)) {
      const t28 = this._parameters.definition.pixelRatio;
      this._maxSize > 0 ? this._renderPixelRatio = Math.min(t28, Math.min(this._maxSize / this.displayWidth, this._maxSize / this.displayHeight)) : this._renderPixelRatio = t28;
    }
    return this._renderPixelRatio;
  }
  _getLineXOffset(t28) {
    switch (this._alignment) {
      case r21.Left:
        return this._backgroundHorizontalPadding;
      case r21.Center:
        return (this.displayWidth - this._lineWidths[t28]) / 2;
      case r21.Right:
        return this.displayWidth - this._backgroundHorizontalPadding - this._lineWidths[t28];
    }
  }
  render(t28, e33 = 0, n26 = 0) {
    t28.save();
    const s26 = e33 /= this.renderPixelRatio, r30 = n26 /= this.renderPixelRatio, o24 = this._haloSize, h25 = this._firstLineYOffset;
    e33 += o24, n26 += h25 + this._baselinePosition;
    const a35 = this._haloSize > 0;
    a35 && this._renderHalo(t28, s26, r30, o24, h25), this._setFontProperties(t28, this._renderedFontSize);
    for (let i19 = 0; i19 < this._lines.length; ++i19) {
      const s27 = this._lines[i19], r31 = this._getLineXOffset(i19);
      a35 && (t28.globalCompositeOperation = "destination-out", t28.fillStyle = "rgb(0, 0, 0)", this._fillText(t28, s27, e33 + r31, n26), this._renderLineDecoration(t28, e33 + r31, n26, this._textWidths[i19])), t28.globalCompositeOperation = "source-over", t28.fillStyle = this._parameters.textStyle, this._fillText(t28, s27, e33 + this._getLineXOffset(i19), n26), this._renderLineDecoration(t28, e33 + r31, n26, this._textWidths[i19]), n26 += this._lineSpacing;
    }
    if (t17.TEXT_SHOW_BASELINE) {
      t28.strokeStyle = l27, t28.setLineDash([2, 2]), t28.lineWidth = 1;
      let e34 = r30 + h25;
      for (let i19 = 0; i19 < this._lines.length; ++i19) {
        const i20 = e34 + this._baselinePosition;
        this._drawLine(t28, [s26, i20], [s26 + this.displayWidth, i20]), e34 += this._lineSpacing;
      }
    }
    if (t17.TEXT_SHOW_BORDER && (t28.strokeStyle = l27, t28.setLineDash([]), t28.lineWidth = 1, this._drawBox(t28, [s26, r30], [this.displayWidth, this.displayHeight])), this._hasBackground) {
      const e34 = this._parameters.definition.background.borderRadius * this.renderPixelRatio;
      this._roundedRect(t28, s26, r30, e34), t28.globalCompositeOperation = "destination-over", t28.fillStyle = this._parameters.backgroundStyle, t28.fill();
    }
    t28.restore();
  }
  _renderLineDecoration(t28, e33, i19, n26, s26 = false) {
    if ("none" === this._parameters.definition.font.decoration || 0 === n26) return;
    const r30 = 1, o24 = Math.max(this._parameters.definition.size / 16, r30);
    switch (this._parameters.definition.font.decoration) {
      case "underline":
        i19 += 2 * o24;
        break;
      case "line-through":
        i19 -= 0.33 * this._baselinePosition;
    }
    const h25 = s26 ? this._haloSize : 0;
    t28.strokeStyle = s26 ? this._parameters.haloStyle : this._parameters.textStyle, t28.lineWidth = this._toRenderUnit(o24 + 2 * h25), t28.beginPath(), t28.moveTo(this._toRenderUnit(e33 - h25), this._toRenderUnit(i19)), t28.lineTo(this._toRenderUnit(e33 + n26 + h25), this._toRenderUnit(i19)), t28.stroke();
  }
  _roundedRect(e33, i19, n26, s26) {
    i19 = this._toRenderUnit(i19), n26 = this._toRenderUnit(n26);
    const r30 = this.renderedWidth, o24 = this.renderedHeight;
    0 !== s26 ? (s26 = a5(s26, 0, Math.floor(o24 / 2)), e33.beginPath(), e33.moveTo(i19, n26 + s26), e33.arcTo(i19, n26, i19 + s26, n26, s26), e33.lineTo(i19 + r30 - s26, n26), e33.arcTo(i19 + r30, n26, i19 + r30, n26 + s26, s26), e33.lineTo(i19 + r30, n26 + o24 - s26), e33.arcTo(i19 + r30, n26 + o24, i19 + r30 - s26, n26 + o24, s26), e33.lineTo(i19 + s26, n26 + o24), e33.arcTo(i19, n26 + o24, i19, n26 + o24 - s26, s26), e33.closePath()) : e33.rect(i19, n26, r30, o24);
  }
  _renderHalo(t28, e33, i19, n26, s26) {
    const r30 = this.renderedWidth, a35 = this.renderedHeight, l34 = o19(h19, Math.max(r30, d18), Math.max(a35, d18)), _21 = l34.getContext("2d");
    _21.clearRect(0, 0, r30, a35), this._setFontProperties(_21, this._renderedFontSize), _21.fillStyle = this._parameters.haloStyle, _21.strokeStyle = this._parameters.haloStyle;
    const c34 = this._renderedHaloSize < 3;
    _21.lineJoin = c34 ? "miter" : "round", c34 ? this._renderHaloEmulated(_21, n26, s26) : this._renderHaloNative(_21, n26, s26);
    let g23 = s26 + this._baselinePosition;
    for (let o24 = 0; o24 < this._lines.length; ++o24) {
      const t29 = this._getLineXOffset(o24);
      this._renderLineDecoration(_21, n26 + t29, g23, this._textWidths[o24], true), g23 += this._lineSpacing;
    }
    t28.globalAlpha = this._parameters.definition.halo.color[3], t28.drawImage(l34, 0, 0, r30, a35, this._toRenderUnit(e33), this._toRenderUnit(i19), r30, a35), t28.globalAlpha = 1;
  }
  _renderHaloEmulated(t28, e33, i19) {
    i19 += this._baselinePosition;
    for (let n26 = 0; n26 < this._lines.length; ++n26) {
      const r30 = this._lines[n26], o24 = this._getLineXOffset(n26);
      for (const [n27, h25] of s19) this._fillText(t28, r30, e33 + o24 + this._haloSize * n27, i19 + this._haloSize * h25);
      i19 += this._lineSpacing;
    }
  }
  _renderHaloNative(t28, e33, i19) {
    const n26 = 2 * this._haloSize;
    i19 += this._baselinePosition;
    for (let s26 = 0; s26 < this._lines.length; ++s26) {
      const r30 = this._lines[s26], o24 = this._getLineXOffset(s26), h25 = 5, a35 = 0.1;
      for (let s27 = 0; s27 < h25; s27++) {
        const d26 = 1 - (h25 - 1) * a35 + s27 * a35;
        t28.lineWidth = this._toRenderUnit(d26 * n26), this._strokeText(t28, r30, e33 + o24, i19);
      }
      i19 += this._lineSpacing;
    }
  }
  _setFontProperties(t28, e33) {
    t28.font = this._parameters.fontString(e33), t28.textAlign = "left", t28.textBaseline = "alphabetic";
  }
  get _displayWidth() {
    return this._metrics.displayWidth;
  }
  _toRenderUnit(t28) {
    return t28 * this.renderPixelRatio;
  }
  _toRoundedRenderUnit(t28) {
    return Math.round(t28 * this.renderPixelRatio);
  }
  _fillText(t28, e33, i19, n26) {
    t28.fillText(e33, this._toRenderUnit(i19), this._toRenderUnit(n26));
  }
  _strokeText(t28, e33, i19, n26) {
    t28.strokeText(e33, this._toRenderUnit(i19), this._toRenderUnit(n26));
  }
  _drawLine(t28, e33, i19) {
    t28.beginPath(), t28.moveTo(this._toRoundedRenderUnit(e33[0]) + 0.5, this._toRoundedRenderUnit(e33[1]) + 0.5), t28.lineTo(this._toRoundedRenderUnit(i19[0]) + 0.5, this._toRoundedRenderUnit(i19[1]) + 0.5), t28.stroke();
  }
  _drawBox(t28, e33, i19) {
    const n26 = this._toRenderUnit(e33[0]), s26 = this._toRenderUnit(e33[1]), r30 = this._toRenderUnit(i19[0]), o24 = this._toRenderUnit(i19[1]), h25 = Math.floor(n26) + 0.5, a35 = Math.ceil(n26 + r30) - 0.5, d26 = Math.floor(s26) + 0.5, l34 = Math.ceil(s26 + o24) - 0.5;
    t28.beginPath(), t28.moveTo(h25, d26), t28.lineTo(a35, d26), t28.lineTo(a35, l34), t28.lineTo(h25, l34), t28.lineTo(h25, d26), t28.stroke();
  }
};
var s19 = [];
{
  const t28 = 16;
  for (let e33 = 0; e33 < 360; e33 += 360 / t28) s19.push([Math.cos(Math.PI * e33 / 180), Math.sin(Math.PI * e33 / 180)]);
}
var r21;
function o19(t28, e33, i19) {
  return t28.canvas || (t28.canvas = document.createElement("canvas")), t28.canvas.width = e33, t28.canvas.height = i19, t28.canvas;
}
!function(t28) {
  t28[t28.Left = 0] = "Left", t28[t28.Center = 1] = "Center", t28[t28.Right = 2] = "Right";
}(r21 || (r21 = {}));
var h19 = { canvas: null };
var a31 = 0.2;
var d18 = 512;
var l27 = "rgb(255, 0, 255, 0.5)";
var _10 = (() => {
  let t28 = "";
  for (let e33 = 32; e33 < 127; e33++) t28 += String.fromCharCode(e33);
  return t28;
})();
var c26 = class {
  constructor(t28, e33, i19, n26, s26) {
    this.paddingTop = t28, this.paddingBottom = e33, this.lineHeight = i19, this.displayWidth = n26, this.baselinePosition = s26;
  }
};
var g13 = class {
  constructor(t28, e33) {
    this.actualBoundingBoxAscent = t28, this.actualBoundingBoxDescent = e33;
  }
};
var u24 = /* @__PURE__ */ new Map();

// node_modules/@arcgis/core/views/3d/layers/graphics/placementUtils.js
var r22 = Object.freeze({ left: 0, center: 0.5, right: 1 });
var o20 = Object.freeze({ "bottom-left": r13(0, 0), bottom: r13(0.5, 0), "bottom-right": r13(1, 0), left: r13(0, 0.5), center: r13(0.5, 0.5), right: r13(1, 0.5), "top-left": r13(0, 1), top: r13(0.5, 1), "top-right": r13(1, 1) });
function c27(t28) {
  switch (t28) {
    case "left":
      return r21.Left;
    case "right":
      return r21.Right;
    default:
      return r21.Center;
  }
}
function i14(t28, e33) {
  switch (e33) {
    case "bottom":
      return "left" === t28 ? "bottom-left" : "right" === t28 ? "bottom-right" : "bottom";
    case "center":
      return t28;
    case "top":
      return "left" === t28 ? "top-left" : "right" === t28 ? "top-right" : "top";
  }
}
function f23(t28) {
  return "middle" === t28 ? "center" : t28;
}

// node_modules/@arcgis/core/views/3d/layers/support/FastSymbolUpdates.js
var S12;
var y8;
function h20(e33) {
  return null != e33;
}
function x11(e33) {
  return "number" == typeof e33;
}
function b15(e33) {
  return "string" == typeof e33;
}
function C6(e33) {
  return null == e33 || b15(e33);
}
function g14(e33, o24) {
  e33 && e33.push(o24);
}
function V7(e33, o24, t28, i19 = e11()) {
  const n26 = e33 || 0, r30 = o24 || 0, s26 = t28 || 0;
  return 0 !== n26 && x3(i19, i19, -n26 / 180 * Math.PI), 0 !== r30 && l8(i19, i19, r30 / 180 * Math.PI), 0 !== s26 && m2(i19, i19, s26 / 180 * Math.PI), i19;
}
function D6(e33, o24, t28, i19, n26) {
  const r30 = e33.minSize, s26 = e33.maxSize;
  if (e33.expression) return g14(n26, "Could not convert size info: expression not supported"), false;
  if (e33.useSymbolValue) {
    const e34 = i19.symbolSize[t28];
    return o24.minSize[t28] = e34, o24.maxSize[t28] = e34, o24.offset[t28] = o24.minSize[t28], o24.factor[t28] = 0, o24.type[t28] = S12.DefinedSize, true;
  }
  if (h20(e33.field)) return h20(e33.stops) ? 2 === e33.stops.length && x11(e33.stops[0].size) && x11(e33.stops[1].size) ? (M8(e33.stops[0].size, e33.stops[1].size, e33.stops[0].value, e33.stops[1].value, o24, t28), o24.type[t28] = S12.DefinedSize, true) : (g14(n26, "Could not convert size info: stops only supported with 2 elements"), false) : x11(r30) && x11(s26) && h20(e33.minDataValue) && h20(e33.maxDataValue) ? (M8(r30, s26, e33.minDataValue, e33.maxDataValue, o24, t28), o24.type[t28] = S12.DefinedSize, true) : null != m3[e33.valueUnit] ? (o24.minSize[t28] = -1 / 0, o24.maxSize[t28] = 1 / 0, o24.offset[t28] = 0, o24.factor[t28] = 1 / m3[e33.valueUnit], o24.type[t28] = S12.DefinedSize, true) : "unknown" === e33.valueUnit ? (g14(n26, "Could not convert size info: proportional size not supported"), false) : (g14(n26, "Could not convert size info: scale-dependent size not supported"), false);
  if (!h20(e33.field)) {
    if (e33.stops && e33.stops[0] && x11(e33.stops[0].size)) return o24.minSize[t28] = e33.stops[0].size, o24.maxSize[t28] = e33.stops[0].size, o24.offset[t28] = o24.minSize[t28], o24.factor[t28] = 0, o24.type[t28] = S12.DefinedSize, true;
    if (x11(r30)) return o24.minSize[t28] = r30, o24.maxSize[t28] = r30, o24.offset[t28] = r30, o24.factor[t28] = 0, o24.type[t28] = S12.DefinedSize, true;
  }
  return g14(n26, "Could not convert size info: unsupported variant of sizeInfo"), false;
}
function M8(e33, o24, t28, i19, n26, r30) {
  const s26 = Math.abs(i19 - t28) > 0 ? (o24 - e33) / (i19 - t28) : 0;
  n26.minSize[r30] = s26 > 0 ? e33 : o24, n26.maxSize[r30] = s26 > 0 ? o24 : e33, n26.offset[r30] = e33 - t28 * s26, n26.factor[r30] = s26;
}
function U5(e33, o24, t28, i19) {
  if (e33.normalizationField || e33.valueRepresentation) return g14(i19, "Could not convert size info: unsupported property"), null;
  if (!C6(e33.field)) return g14(i19, "Could not convert size info: field is not a string"), null;
  if (o24.size) {
    if (e33.field) if (o24.size.field) {
      if (e33.field !== o24.size.field) return g14(i19, "Could not convert size info: multiple fields in use"), null;
    } else o24.size.field = e33.field;
  } else o24.size = { field: e33.field, minSize: [0, 0, 0], maxSize: [0, 0, 0], offset: [0, 0, 0], factor: [0, 0, 0], type: [S12.Undefined, S12.Undefined, S12.Undefined] };
  let n26;
  switch (e33.axis) {
    case "width":
      return n26 = D6(e33, o24.size, 0, t28, i19), n26 ? o24 : null;
    case "height":
      return n26 = D6(e33, o24.size, 2, t28, i19), n26 ? o24 : null;
    case "depth":
      return n26 = D6(e33, o24.size, 1, t28, i19), n26 ? o24 : null;
    case "width-and-depth":
      return n26 = D6(e33, o24.size, 0, t28, i19), n26 && D6(e33, o24.size, 1, t28, i19), n26 ? o24 : null;
    case null:
    case void 0:
    case "all":
      return n26 = D6(e33, o24.size, 0, t28, i19), n26 = n26 && D6(e33, o24.size, 1, t28, i19), n26 = n26 && D6(e33, o24.size, 2, t28, i19), n26 ? o24 : null;
    default:
      return g14(i19, `Could not convert size info: unknown axis "${e33.axis}""`), null;
  }
}
function T7(e33, o24, t28) {
  for (let n26 = 0; n26 < 3; ++n26) {
    let t29 = o24.unitInMeters;
    e33.type[n26] === S12.DefinedSize && (t29 *= o24.modelSize[n26], e33.type[n26] = S12.DefinedScale), e33.minSize[n26] = e33.minSize[n26] / t29, e33.maxSize[n26] = e33.maxSize[n26] / t29, e33.offset[n26] = e33.offset[n26] / t29, e33.factor[n26] = e33.factor[n26] / t29;
  }
  let i19;
  if (e33.type[0] !== S12.Undefined) i19 = 0;
  else if (e33.type[1] !== S12.Undefined) i19 = 1;
  else {
    if (e33.type[2] === S12.Undefined) return g14(t28, "No size axis contains a valid size or scale"), false;
    i19 = 2;
  }
  for (let n26 = 0; n26 < 3; ++n26) e33.type[n26] === S12.Undefined && (e33.minSize[n26] = e33.minSize[i19], e33.maxSize[n26] = e33.maxSize[i19], e33.offset[n26] = e33.offset[i19], e33.factor[n26] = e33.factor[i19], e33.type[n26] = e33.type[i19]);
  return true;
}
function E16(e33, o24, t28) {
  e33[4 * o24 + 0] = t28.r / 255, e33[4 * o24 + 1] = t28.g / 255, e33[4 * o24 + 2] = t28.b / 255, e33[4 * o24 + 3] = t28.a;
}
function O10(e33, o24, t28) {
  if (e33.normalizationField) return g14(t28, "Could not convert color info: unsupported property"), null;
  if (b15(e33.field)) {
    if (!e33.stops) return g14(t28, "Could not convert color info: missing stops or colors"), null;
    {
      if (e33.stops.length > 8) return g14(t28, "Could not convert color info: too many color stops"), null;
      o24.color = { field: e33.field, values: [0, 0, 0, 0, 0, 0, 0, 0], colors: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] };
      const i19 = e33.stops;
      for (let e34 = 0; e34 < 8; ++e34) {
        const t29 = i19[Math.min(e34, i19.length - 1)];
        o24.color.values[e34] = t29.value, E16(o24.color.colors, e34, t29.color);
      }
    }
  } else {
    if (!(e33.stops && e33.stops.length >= 0)) return g14(t28, "Could not convert color info: no field and no colors/stops"), null;
    {
      const t29 = e33.stops && e33.stops.length >= 0 && e33.stops[0].color;
      o24.color = { field: null, values: [0, 0, 0, 0, 0, 0, 0, 0], colors: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] };
      for (let e34 = 0; e34 < 8; e34++) o24.color.values[e34] = 1 / 0, E16(o24.color.colors, e34, t29);
    }
  }
  return o24;
}
function j9(e33, o24, t28) {
  if (e33.normalizationField) return g14(t28, "Could not convert opacity info: unsupported property"), null;
  if (b15(e33.field)) {
    if (!e33.stops) return g14(t28, "Could not convert opacity info: missing stops or opacities"), null;
    {
      if (e33.stops.length > 8) return g14(t28, "Could not convert opacity info: too many opacity stops"), null;
      o24.opacity = { field: e33.field, values: [0, 0, 0, 0, 0, 0, 0, 0], opacityValues: [0, 0, 0, 0, 0, 0, 0, 0] };
      const i19 = e33.stops;
      for (let e34 = 0; e34 < 8; ++e34) {
        const t29 = i19[Math.min(e34, i19.length - 1)];
        o24.opacity.values[e34] = t29.value, o24.opacity.opacityValues[e34] = t29.opacity;
      }
    }
  } else {
    if (!(e33.stops && e33.stops.length >= 0)) return g14(t28, "Could not convert opacity info: no field and no opacities/stops"), null;
    {
      const t29 = e33.stops && e33.stops.length >= 0 ? e33.stops[0].opacity : 0;
      o24.opacity = { field: null, values: [0, 0, 0, 0, 0, 0, 0, 0], opacityValues: [0, 0, 0, 0, 0, 0, 0, 0] };
      for (let e34 = 0; e34 < 8; e34++) o24.opacity.values[e34] = 1 / 0, o24.opacity.opacityValues[e34] = t29;
    }
  }
  return o24;
}
function k5(e33, o24, t28) {
  const i19 = 2 === t28 && "arithmetic" === e33.rotationType;
  o24.offset[t28] = i19 ? 90 : 0, o24.factor[t28] = i19 ? -1 : 1, o24.type[t28] = 1;
}
function w8(e33, o24, t28) {
  if (!b15(e33.field)) return g14(t28, "Could not convert rotation info: field is not a string"), null;
  if (o24.rotation) {
    if (e33.field) if (o24.rotation.field) {
      if (e33.field !== o24.rotation.field) return g14(t28, "Could not convert rotation info: multiple fields in use"), null;
    } else o24.rotation.field = e33.field;
  } else o24.rotation = { field: e33.field, offset: [0, 0, 0], factor: [1, 1, 1], type: [0, 0, 0] };
  switch (e33.axis) {
    case "tilt":
      return k5(e33, o24.rotation, 0), o24;
    case "roll":
      return k5(e33, o24.rotation, 1), o24;
    case null:
    case void 0:
    case "heading":
      return k5(e33, o24.rotation, 2), o24;
    default:
      return g14(t28, `Could not convert rotation info: unknown axis "${e33.axis}""`), null;
  }
}
function F6(e33, o24, t28) {
  if (!e33) return null;
  const i19 = !o24.supportedTypes || !!o24.supportedTypes.size, n26 = !o24.supportedTypes || !!o24.supportedTypes.color, r30 = !o24.supportedTypes || !!o24.supportedTypes.rotation, s26 = !!o24.supportedTypes && !!o24.supportedTypes.opacity, l34 = e33.reduce((e34, l35) => {
    if (!e34) return e34;
    if (l35.valueExpression) return g14(t28, "Could not convert visual variables: arcade expressions not supported"), null;
    switch (l35.type) {
      case "size":
        return i19 ? U5(l35, e34, o24, t28) : e34;
      case "color":
        return n26 ? O10(l35, e34, t28) : e34;
      case "opacity":
        return s26 ? j9(l35, e34, t28) : null;
      case "rotation":
        return r30 ? w8(l35, e34, t28) : e34;
      default:
        return null;
    }
  }, { size: null, color: null, opacity: null, rotation: null });
  return !(e33.length > 0 && l34) || l34.size || l34.color || l34.opacity || l34.rotation ? l34 && l34.size && !T7(l34.size, o24, t28) ? null : l34 : null;
}
function A9(e33) {
  return e33 && null != e33.size;
}
function I9(e33, o24) {
  if (!e33) return { enabled: false };
  if (t17.TESTS_DISABLE_FAST_UPDATES) return { enabled: false };
  const t28 = F6(e33.visualVariables, o24);
  return t28 ? { enabled: true, visualVariables: t28, materialParameters: _11(t28, o24), requiresShaderTransformation: A9(t28) } : { enabled: false };
}
function P6(e33, o24, t28) {
  if (!o24 || !e33.enabled) return false;
  const i19 = e33.visualVariables, n26 = F6(o24.visualVariables, t28);
  return !!n26 && (!!(R7(i19.size, n26.size, "size") && R7(i19.color, n26.color, "color") && R7(i19.rotation, n26.rotation, "rotation") && R7(i19.opacity, n26.opacity, "opacity")) && (e33.visualVariables = n26, e33.materialParameters = _11(n26, t28), e33.requiresShaderTransformation = A9(n26), true));
}
function R7(e33, o24, t28) {
  if (!!e33 != !!o24) return false;
  if (e33 && e33.field !== o24.field) return false;
  if (e33 && "rotation" === t28) {
    const t29 = e33, i19 = o24;
    for (let e34 = 0; e34 < 3; e34++) if (t29.type[e34] !== i19.type[e34] || t29.offset[e34] !== i19.offset[e34] || t29.factor[e34] !== i19.factor[e34]) return false;
  }
  return true;
}
function _11(e33, n26) {
  const r30 = { vvSizeEnabled: false, vvSizeMinSize: null, vvSizeMaxSize: null, vvSizeOffset: null, vvSizeFactor: null, vvSizeValue: null, vvColorEnabled: false, vvColorValues: null, vvColorColors: null, vvOpacityEnabled: false, vvOpacityValues: null, vvOpacityOpacities: null, vvSymbolAnchor: null, vvSymbolRotationMatrix: null }, s26 = A9(e33);
  return e33 && e33.size ? (r30.vvSizeEnabled = true, r30.vvSizeMinSize = e33.size.minSize, r30.vvSizeMaxSize = e33.size.maxSize, r30.vvSizeOffset = e33.size.offset, r30.vvSizeFactor = e33.size.factor) : e33 && s26 && (r30.vvSizeValue = n26.transformation.scale), e33 && s26 && (r30.vvSymbolAnchor = n26.transformation.anchor, r30.vvSymbolRotationMatrix = e10(), r7(N2), V7(n26.transformation.rotation[2], n26.transformation.rotation[0], n26.transformation.rotation[1], N2), a9(r30.vvSymbolRotationMatrix, N2)), e33 && e33.color && (r30.vvColorEnabled = true, r30.vvColorValues = e33.color.values, r30.vvColorColors = e33.color.colors), e33 && e33.opacity && (r30.vvOpacityEnabled = true, r30.vvOpacityValues = e33.opacity.values, r30.vvOpacityOpacities = e33.opacity.opacityValues), r30;
}
function q6(o24, t28, i19) {
  if (!o24.vvSizeEnabled) return i19;
  n7(B4, i19);
  const u32 = o24.vvSymbolRotationMatrix;
  s5(N2, u32[0], u32[1], u32[2], 0, u32[3], u32[4], u32[5], 0, u32[6], u32[7], u32[8], 0, 0, 0, 0, 1), c5(B4, B4, N2);
  for (let n26 = 0; n26 < 3; ++n26) {
    const i20 = o24.vvSizeOffset[n26] + t28[0] * o24.vvSizeFactor[n26];
    L5[n26] = a5(i20, o24.vvSizeMinSize[n26], o24.vvSizeMaxSize[n26]);
  }
  return f6(B4, B4, L5), i(B4, B4, o24.vvSymbolAnchor), B4;
}
function $5(o24, t28, i19) {
  if (!t28.vvSizeEnabled) return o2(o24, 1, 1, 1);
  for (let n26 = 0; n26 < 3; ++n26) {
    const r30 = t28.vvSizeOffset[n26] + i19[0] * t28.vvSizeFactor[n26];
    o24[n26] = a5(r30, t28.vvSizeMinSize[n26], t28.vvSizeMaxSize[n26]);
  }
  return o24;
}
!function(e33) {
  e33[e33.Undefined = 0] = "Undefined", e33[e33.DefinedSize = 1] = "DefinedSize", e33[e33.DefinedScale = 2] = "DefinedScale";
}(S12 || (S12 = {})), function(e33) {
  e33[e33.Undefined = 0] = "Undefined", e33[e33.DefinedAngle = 1] = "DefinedAngle";
}(y8 || (y8 = {}));
var B4 = e11();
var L5 = n5();
var N2 = e11();

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DIconSymbolLayer.js
var re2 = e11();
var se2 = r2(0, 0, 1);
var ae2 = 16;
var oe2 = 1.5;
var ne2 = a19;
var le2 = [ne2 / 2, ne2 / 2, 1 - ne2 / 2, 1 - ne2 / 2];
var ce3 = [e17 * ne2, e17 * ne2];
var he2 = class _he extends y7 {
  getCachedSize() {
    return { size: this._getIconSize() };
  }
  constructor(e33, t28, i19, r30) {
    super(e33, t28, i19, r30), this._cimLayers = null, this._cimSymbolMaterials = /* @__PURE__ */ new Map(), this._cimSymbolTextures = /* @__PURE__ */ new Map(), this._cimMaterialParametersInfo = null, this._cimRequiredFields = null, this._cimScaleFactorOrFunction = null, this._size = null, this._symbolTextureRatio = 1, this._outlineSize = 0, this._elevationOptions = { supportsOffsetAdjustment: true, supportsOnTheGround: true };
  }
  async doLoad(e33) {
    this._validateOrThrow();
    const t28 = this._prepareMaterialParameters(), i19 = this._getPrimitive();
    if (r(i19)) this._prepareResourcesPrimitive(t28, i19);
    else {
      const i20 = d9(this.symbolLayer), r30 = ot(i20);
      r30 && "application/json" === r30.mediaType ? await this._prepareResourcesCIM(t28, JSON.parse(r30.data), e33) : await this._prepareResourcesHref(t28, i20, e33);
    }
  }
  _validateOrThrow() {
    if (this._drivenProperties.size) return;
    const e33 = S5(this._getIconSize());
    if (e33) throw new s2("graphics3diconsymbollayer:invalid-size", e33);
  }
  _getIconSize() {
    const e33 = this.symbolLayer, t28 = Math.round(null != e33.size ? u6(e33.size) : ae2);
    return this._drivenProperties.size ? Math.max(t28, 64) : t28;
  }
  _generateTextureCIM(e33) {
    const t28 = this._getGraphicHash(e33);
    let i19 = "" === t28 ? null : this._cimSymbolTextures.get(t28);
    if (!i19) {
      const r30 = { scaleFactor: this._cimScaleFactorOrFunction }, s26 = this._context.sharedResources.cimSymbolRasterizer.rasterizeCIMSymbol3D(this._cimLayers, e33, "esriGeometryPoint", r30, void 0, void 0);
      this._cimMaterialParametersInfo.anchorPosition = this._getAnchorPos("relative", s26.anchorPosition);
      const a35 = { width: s26.imageData.width, height: s26.imageData.height, powerOfTwoResizeMode: D4.PAD };
      i19 = new G3(s26.imageData, a35), this._cimSymbolTextures.set(t28, i19), this._context.stage.add(i19);
    }
    return i19;
  }
  _computeSize(e33, t28) {
    const i19 = e33.width / e33.height;
    return i19 > 1 ? [t28, Math.round(t28 / i19)] : [Math.round(t28 * i19), t28];
  }
  _prepareMaterialParameters() {
    const e33 = { anchorPosition: this._getAnchorPos(this.symbolLayer.anchor, this.symbolLayer.anchorPosition) }, t28 = this.symbol;
    if (me2(t28)) {
      const { screenLength: i19, minWorldLength: s26, maxWorldLength: a35 } = t28.verticalOffset;
      e33.verticalOffset = { screenLength: u6(i19), minWorldLength: s26 || 0, maxWorldLength: r(a35) ? a35 : 1 / 0 };
    }
    return this._context.screenSizePerspectiveEnabled && (e33.screenSizePerspective = this._context.sharedResources.screenSizePerspectiveSettings), e33.occlusionTest = true, e33.hasSlicePlane = this._context.slicePlaneEnabled, e33;
  }
  _prepareResourcesPrimitive(e33, t28) {
    const i19 = this._getOutlineSize();
    if (ue2(t28) && 0 === i19) throw new Error("Nothing to render");
    if (this._outlineSize = i19, e33.color = this._getFillColor(), e33.outlineColor = this._getOutlineColor(), e33.outlineSize = this._outlineSize, r(this._context.sharedResources.textures)) {
      const i20 = this._context.sharedResources.textures.fromData(`${t28}-icon`, () => o16(t28));
      this._texture = i20.texture, this._releaseTexture = i20, e33.textureId = this._texture.id;
    }
    e33.textureIsSignedDistanceField = true, e33.distanceFieldBoundingBox = le2;
    const s26 = this._getIconSize();
    this._size = [s26, s26], this._symbolTextureRatio = 1 / ne2, this._createMaterialAndAddToStage(e33, this._context.stage);
  }
  async _prepareResourcesHref(e33, s26, a35) {
    this._outlineSize = this._getOutlineSize(), e33.color = this._getFillColor(), e33.outlineColor = this._getOutlineColor(), e33.outlineSize = this._outlineSize, e33.textureIsSignedDistanceField = false;
    const o24 = this._getIconSize(), n26 = o24 * this._context.graphicsCoreOwner.view.state.rasterPixelRatio;
    if (r(this._context.sharedResources.textures)) {
      const r30 = await b2(this._context.sharedResources.textures.fromUrl(s26, n26, { signal: a35 }));
      if (false === r30.ok) {
        w2(r30.error);
        throw new s2("graphics3diconsymbollayer:request-failed", `Failed to load (Request for icon resource failed: ${s26})`);
      }
      this._releaseTexture = r30.value;
      const l34 = r30.value.texture, h25 = l34.params;
      this._size = this._computeSize(h25, o24), e33.textureId = l34.id;
    }
    this._createMaterialAndAddToStage(e33, this._context.stage);
  }
  async _prepareResourcesCIM(e33, t28, i19) {
    const r30 = new d6({ data: t28 });
    if (!this._context.sharedResources.cimSymbolRasterizer) {
      const e34 = (await import("./CIMSymbolRasterizer-OZCXULTU.js")).CIMSymbolRasterizer;
      f(i19), this._context.sharedResources.cimSymbolRasterizer || (this._context.sharedResources.cimSymbolRasterizer = new e34(this._context.renderCoordsHelper.spatialReference, true));
    }
    const s26 = this._context.layer.fields ? this._context.layer.fields.map((e34) => e34.toJSON()) : null;
    let a35, o24;
    if (this._cimLayers = await this._context.sharedResources.cimSymbolRasterizer.analyzeCIMSymbol(r30, s26, this._context.renderer && "dictionary" === this._context.renderer.type ? this._context.renderer.fieldMap : null, "esriGeometryPoint", { signal: i19 }), this._context.renderer && "dictionary" === this._context.renderer.type && this._context.renderer.scaleExpression) {
      const e34 = this._context.renderer;
      if (isNaN(e34.scaleExpression)) {
        const t29 = e34.scaleExpression, i20 = await o12(t29, this._context.layer.spatialReference, s26);
        o24 = (e35, t30, r31) => {
          const s27 = i5(i20, e35, { $view: r31 }, "esriGeometryPoint", t30);
          return null !== s27 ? s27 : 1;
        };
      } else a35 = Number(e34.scaleExpression);
    }
    this._cimScaleFactorOrFunction = a35 || o24 || 1;
    const n26 = this._context.renderer ? await this._context.renderer.getRequiredFields(this._context.layer.fieldsIndex) : [];
    f(i19);
    const l34 = this._context.layer.fieldsIndex;
    this._cimRequiredFields = n26.map((e34) => l34.get(e34).name), this._cimMaterialParametersInfo = e33, this._cimMaterialParametersInfo.color = this._getFillColor(), this._cimMaterialParametersInfo.outlineColor = [0, 0, 0, 0], this._cimMaterialParametersInfo.outlineSize = 0, this._cimMaterialParametersInfo.textureIsSignedDistanceField = false;
  }
  _getPrimitive() {
    return this.symbolLayer.resource && this.symbolLayer.resource.href ? null : this.symbolLayer.resource && this.symbolLayer.resource.primitive || j7;
  }
  _getOutlineSize() {
    let e33 = 0;
    const t28 = this.symbolLayer;
    if (r(t28.outline) && null != t28.outline.size) return Math.max(u6(t28.outline.size), 0);
    return e33 = ue2(this._getPrimitive()) ? oe2 : 0, Math.max(e33, 0);
  }
  _getOutlineColor() {
    const t28 = this._getLayerOpacity(), i19 = this.symbolLayer, a35 = x(i19, "outline", "color");
    if (r(a35)) {
      const i20 = l11.toUnitRGB(a35), r30 = a35.a * t28;
      return [i20[0], i20[1], i20[2], r30];
    }
    return [0, 0, 0, 0];
  }
  _getFillColor() {
    if (ue2(this._getPrimitive())) return c19;
    const e33 = t(this._getPrimitive()), t28 = x(this.symbolLayer, "material", "color");
    return this._getCombinedOpacityAndColor(t28, { hasIntrinsicColor: e33 });
  }
  _getAnchorPos(e33, t28) {
    return "relative" === e33 ? r13((t28.x || 0) + 0.5, 0.5 - (t28.y || 0)) : e33 in o20 ? o20[e33] : o20.center;
  }
  _createMaterialAndAddToStage(e33, t28) {
    if (this._cimLayers ? this._fastUpdates = { enabled: false } : this._fastUpdates = I9(this._context.renderer, this._fastVisualVariableConvertOptions()), this._fastUpdates.enabled && Object.assign(e33, this._fastUpdates.materialParameters), this._cimLayers) {
      let i19 = r(e33.textureId) ? this._cimSymbolMaterials.get(e33.textureId) : null;
      return i19 || (i19 = new $3(e33), this._cimSymbolMaterials.set(l(e33.textureId, 0), i19), t28.add(i19)), i19;
    }
    return this._material = new $3(e33), t28.add(this._material), this._material;
  }
  _setDrapingDependentMaterialParameters() {
    this.draped && (this._forEachMaterial((e33) => {
      e33.setParameters({ verticalOffset: null, screenSizePerspective: null, occlusionTest: false, hasSlicePlane: false, shaderPolygonOffset: 0, isDraped: this.draped });
    }), this.layerOpacityChanged());
  }
  destroy() {
    super.destroy(), this._forEachMaterial((e33) => this._context.stage.remove(e33)), this._material = null, this._cimSymbolMaterials.clear(), this._cimSymbolTextures.forEach((e33) => this._context.stage.remove(e33)), this._cimSymbolTextures.clear(), this._releaseTexture = y(this._releaseTexture);
  }
  _getScaleFactor(e33, t28) {
    if (this._drivenProperties.size && e33.size) {
      for (let t29 = 0; t29 < 3; t29++) {
        const i19 = e33.size[t29];
        i19 && "symbol-value" !== i19 && "proportional" !== i19 && (e33.size[t29] = u6(i19));
      }
      if ("symbol-value" === e33.size[0]) return 1;
      if (isFinite(+e33.size[0])) return +e33.size[0] / t28;
      if (isFinite(+e33.size[2])) return +e33.size[2] / t28;
    }
    return 1;
  }
  createGraphics3DGraphic(e33) {
    const t28 = e33.graphic;
    if (!this._validateGeometry(t28.geometry)) return null;
    let i19, r30 = [0, 0];
    if (this._cimLayers) {
      if (!this._cimLayers.length) return null;
      const e34 = this._generateTextureCIM(t28), s27 = { textureId: e34.id, ...this._cimMaterialParametersInfo };
      i19 = this._createMaterialAndAddToStage(s27, this._context.stage), r30 = [e34.params.width, e34.params.height];
    } else r30 = this._size, i19 = e2(this._material);
    const s26 = u21(t28.geometry);
    if (t(s26)) return this.logger.warn(`unsupported geometry type for icon symbol: ${t28.geometry.type}`), null;
    const o24 = e33.renderingInfo, n26 = this._getVertexOpacityAndColor(o24);
    let c34 = 1;
    if (!this._fastUpdates.enabled || !this._fastUpdates.visualVariables.size) {
      const e34 = r30[0] > r30[1] ? r30[0] : r30[1];
      c34 = this._getScaleFactor(o24, e34);
    }
    c34 *= this._symbolTextureRatio;
    const h25 = r13(r30[0] * c34, r30[1] * c34), m18 = this.setGraphicElevationContext(t28, new h15());
    return this.ensureDrapedStatus("on-the-ground" === m18.mode) && this._setDrapingDependentMaterialParameters(), this.draped ? this._createAsOverlay(t28, s26, i19, n26, h25, e33.layer.uid) : this._createAs3DShape(t28, s26, i19, n26, h25, m18, t28.uid);
  }
  layerOpacityChanged() {
    const e33 = this._getFillColor(), t28 = this._getOutlineColor();
    this._forEachMaterial((i19) => {
      i19.setParameters({ color: e33 }), i19.setParameters({ outlineColor: t28 });
    });
  }
  layerElevationInfoChanged(e33, t28, i19) {
    const r30 = this._elevationContext.mode, s26 = m7(_he.elevationModeChangeTypes, i19, r30);
    if (s26 !== j8.UPDATE) return s26;
    const a35 = p12(r30) || "absolute-height" === r30;
    return this.updateGraphics3DGraphicElevationInfo(e33, t28, () => a35);
  }
  slicePlaneEnabledChanged() {
    return this.draped || this._forEachMaterial((e33) => {
      e33.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled });
    }), true;
  }
  physicalBasedRenderingChanged() {
    return true;
  }
  pixelRatioChanged() {
    return !!this._getPrimitive();
  }
  skipHighSymbolLodsChanged() {
    return true;
  }
  applyRendererDiff(e33, t28) {
    for (const i19 in e33.diff) {
      if ("visualVariables" !== i19) return e22.Recreate_Symbol;
      if (!P6(this._fastUpdates, t28, this._fastVisualVariableConvertOptions())) return e22.Recreate_Symbol;
      r(this._material) && this._material.setParameters(this._fastUpdates.materialParameters);
    }
    return e22.Fast_Update;
  }
  _defaultElevationInfoNoZ() {
    return de2;
  }
  _createAs3DShape(e33, t28, i19, r30, s26, o24, n26) {
    const l34 = this.getFastUpdateAttrValues(e33), c34 = l34 ? (e34) => q6(this._fastUpdates.materialParameters, l34, e34) : void 0, h25 = this._context.layer.uid, m18 = this._context.stage.renderView.getObjectAndLayerIdColor({ graphicUid: n26, layerUid: h25 }), u32 = ot2(i19, se2, null, r30, s26, _e2, null, l34, m18), d26 = p18(this._context, t28, u32, o24, n26, c34);
    if (t(d26)) return null;
    const _21 = new S8(this, d26.object, [u32], null, null, d13, o24);
    return _21.alignedSampledElevation = d26.sampledElevation, _21.needsElevationUpdates = p12(o24.mode) || "absolute-height" === o24.mode, _21.getScreenSize = this._createScreenSizeGetter(s26, c34), _21.calculateRelativeScreenBounds = (e34) => i19.calculateRelativeScreenBounds(_21.getScreenSize(), 1, e34), m12(_21, t28, this._context.elevationProvider), _21;
  }
  _createAsOverlay(e33, t28, i19, s26, a35, o24) {
    i19.renderPriority = this._renderPriority;
    const n26 = n6();
    gn(t28, n26, this._context.overlaySR), n26[2] = ce;
    const l34 = this._context.clippingExtent;
    if (r(l34) && !p5(l34, n26)) return null;
    const c34 = this.getFastUpdateAttrValues(e33), h25 = c34 ? (e34) => q6(this._fastUpdates.materialParameters, c34, e34) : void 0, m18 = this._context.stage.renderView.getObjectAndLayerIdColor({ graphicUid: e33.uid, layerUid: this._context.layer.uid }), u32 = ot2(i19, se2, n26, s26, a35, null, null, c34, m18), d26 = new h14(u32, { layerUid: o24, graphicUid: e33.uid, shaderTransformer: h25 });
    n26[3] = 0, a4(d26.boundingSphere, n26);
    const _21 = new l26(this, [d26], null, this._context.drapeSourceRenderer);
    return _21.getScreenSize = this._createScreenSizeGetter(a35, h25), _21.calculateRelativeScreenBounds = (e34) => i19.calculateRelativeScreenBounds(_21.getScreenSize(), 1, e34), _21;
  }
  _createScreenSizeGetter(e33, t28) {
    const i19 = this._outlineSize + 2;
    if (this._fastUpdates.enabled) {
      const r30 = e33[0] / this._symbolTextureRatio, s26 = e33[1] / this._symbolTextureRatio;
      return (e34 = n13()) => {
        const a35 = t28(re2);
        return e34[0] = a35[0] * r30 + i19, e34[1] = a35[5] * s26 + i19, e34;
      };
    }
    {
      const t29 = e33[0] / this._symbolTextureRatio + i19, r30 = e33[1] / this._symbolTextureRatio + i19;
      return (e34 = n13()) => (e34[0] = t29, e34[1] = r30, e34);
    }
  }
  _fastVisualVariableConvertOptions() {
    const e33 = this._size[0] > this._size[1] ? this._size[0] : this._size[1], t28 = r2(e33, e33, e33), i19 = e6(1), r30 = e33 * i19;
    return { modelSize: t28, symbolSize: r2(r30, r30, r30), unitInMeters: i19, transformation: { anchor: f3, scale: l3, rotation: f3 } };
  }
  _getGraphicHash(e33) {
    let t28 = "";
    for (const i19 of this._cimRequiredFields) t28 += i19 + e33.attributes[i19];
    return t28;
  }
  _forEachMaterial(e33) {
    r(this._material) && e33(this._material), this._cimSymbolMaterials.forEach(e33);
  }
  test() {
    return { ...super.test(), material: this._material };
  }
};
function me2(e33) {
  return e33 && "point-3d" === e33.type && e33.hasVisibleVerticalOffset();
}
function ue2(e33) {
  return !t(e33) && ("cross" === e33 || "x" === e33);
}
he2.PRIMITIVE_SIZE = ce3, he2.elevationModeChangeTypes = { definedChanged: j8.UPDATE, staysOnTheGround: j8.NONE, onTheGroundChanged: j8.RECREATE };
var de2 = { mode: "relative-to-ground", offset: 0 };
var _e2 = r6(0, 0, 0, 1);

// node_modules/@arcgis/core/views/3d/layers/graphics/lineUtils.js
function n20(n26) {
  switch (n26) {
    case "butt":
      return r19.BUTT;
    case "square":
      return r19.SQUARE;
    case "round":
      return r19.ROUND;
    default:
      return null;
  }
}
function r23(e33) {
  return "diamond" === e33 ? "kite" : e33;
}

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/LineMarkerTechnique.js
var R8 = /* @__PURE__ */ new Map([[O4.POSITION, 0], [O4.UV0, 2], [O4.AUXPOS1, 3], [O4.NORMAL, 4], [O4.COLOR, 5], [O4.COLORFEATUREATTRIBUTE, 5], [O4.SIZE, 6], [O4.SIZEFEATUREATTRIBUTE, 6], [O4.OPACITYFEATUREATTRIBUTE, 7]]);
var b16 = class _b extends e14 {
  initializeProgram(e33) {
    return new o10(e33.rctx, _b.shader.get().build(this.configuration), R8);
  }
  _makePipelineState(t28, i19) {
    const o24 = this.configuration, a35 = t28 === o8.NONE;
    return W({ blending: o24.output === h10.Color || o24.output === h10.Alpha ? a35 ? c14 : A4(t28) : null, depthTest: { func: l17(t28) }, depthWrite: a35 ? o24.writeDepth ? a13 : null : E8(t28), colorWrite: _6, stencilWrite: o24.hasOccludees ? e15 : null, stencilTest: o24.hasOccludees ? i19 ? o11 : f12 : null, polygonOffset: { factor: 0, units: -10 } });
  }
  initializePipeline() {
    return this.configuration.occluder && (this._occluderPipelineTransparent = W({ blending: c14, depthTest: s10, depthWrite: null, colorWrite: _6, stencilWrite: null, stencilTest: m6 }), this._occluderPipelineOpaque = W({ blending: c14, depthTest: s10, depthWrite: null, colorWrite: _6, stencilWrite: l18, stencilTest: P5 }), this._occluderPipelineMaskWrite = W({ blending: null, depthTest: i8, depthWrite: null, colorWrite: null, stencilWrite: e15, stencilTest: o11 })), this._occludeePipelineState = this._makePipelineState(this.configuration.transparencyPassType, true), this._makePipelineState(this.configuration.transparencyPassType, false);
  }
  getPipelineState(e33, t28) {
    return t28 ? this._occludeePipelineState : this.configuration.occluder ? e33 === E9.TRANSPARENT_OCCLUDER_MATERIAL ? this._occluderPipelineTransparent : e33 === E9.OCCLUDER_MATERIAL ? this._occluderPipelineOpaque : this._occluderPipelineMaskWrite : super.getPipelineState(e33, t28);
  }
};
b16.shader = new t16(M5, () => import("./LineMarker.glsl-3BHJVHXD.js"));

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/LineMarkerMaterial.js
var v14 = class extends h12 {
  constructor(e33) {
    super(e33, new S13()), this._vertexAttributeLocations = R8, this._configuration = new a18(), this._layout = this.createLayout();
  }
  dispose() {
  }
  getConfiguration(e33, t28) {
    return this._configuration.output = e33, this._configuration.space = t28.slot === E9.DRAPED_MATERIAL ? p10.Draped : this.parameters.worldSpace ? p10.World : p10.Screen, this._configuration.hideOnShortSegments = this.parameters.hideOnShortSegments, this._configuration.hasCap = this.parameters.cap !== r19.BUTT, this._configuration.anchor = this.parameters.anchor, this._configuration.hasTip = this.parameters.hasTip, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.hasOccludees = this.parameters.hasOccludees, this._configuration.writeDepth = this.parameters.writeDepth, this._configuration.vvColor = this.parameters.vvColorEnabled, this._configuration.vvOpacity = this.parameters.vvOpacityEnabled, this._configuration.vvSize = this.parameters.vvSizeEnabled, this._configuration.occluder = this.parameters.renderOccluded === c13.OccludeAndTransparentStencil, this._configuration.transparencyPassType = t28.transparencyPassType, this._configuration.hasMultipassTerrain = t28.multipassTerrain.enabled, this._configuration.cullAboveGround = t28.multipassTerrain.cullAboveGround, this._configuration;
  }
  intersect() {
  }
  createLayout() {
    const e33 = T2().vec3f(O4.POSITION).vec2f(O4.UV0).vec3f(O4.AUXPOS1);
    return this.parameters.worldSpace && e33.vec3f(O4.NORMAL), this.parameters.vvSizeEnabled ? e33.f32(O4.SIZEFEATUREATTRIBUTE) : e33.f32(O4.SIZE), this.parameters.vvColorEnabled ? e33.f32(O4.COLORFEATUREATTRIBUTE) : e33.vec4f(O4.COLOR), this.parameters.vvOpacityEnabled && e33.f32(O4.OPACITYFEATUREATTRIBUTE), e33;
  }
  createBufferWriter() {
    return new O11(this._layout, this.parameters);
  }
  requiresSlot(e33, t28) {
    if (t28 === h10.Color || t28 === h10.Alpha || t28 === h10.Highlight || t28 === h10.Depth) {
      if (e33 === E9.DRAPED_MATERIAL) return true;
      if (this.parameters.renderOccluded === c13.OccludeAndTransparentStencil) return e33 === E9.OPAQUE_MATERIAL || e33 === E9.OCCLUDER_MATERIAL || e33 === E9.TRANSPARENT_OCCLUDER_MATERIAL;
      if (t28 === h10.Color || t28 === h10.Alpha) {
        return e33 === (this.parameters.writeDepth ? E9.TRANSPARENT_MATERIAL : E9.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL);
      }
      return e33 === E9.OPAQUE_MATERIAL;
    }
    return false;
  }
  createGLMaterial(e33) {
    return new _12(e33);
  }
};
var _12 = class extends h11 {
  _updateParameters(e33) {
    return this.updateTexture(this._material.parameters.textureId), this._material.setParameters(this.textureBindParameters), this.ensureTechnique(b16, e33);
  }
  _updateOccludeeState(e33) {
    e33.hasOccludees !== this._material.parameters.hasOccludees && this._material.setParameters({ hasOccludees: e33.hasOccludees });
  }
  beginSlot(e33) {
    return this._output !== h10.Color && this._output !== h10.Alpha || this._updateOccludeeState(e33), this._updateParameters(e33);
  }
};
var S13 = class extends v9 {
  constructor() {
    super(...arguments), this.width = 0, this.color = [1, 1, 1, 1], this.placement = "end", this.cap = r19.BUTT, this.anchor = s12.Center, this.hasTip = false, this.worldSpace = false, this.hideOnShortSegments = false, this.writeDepth = true, this.hasSlicePlane = false, this.vvFastUpdate = false, this.hasOccludees = false;
  }
};
var O11 = class {
  constructor(e33, t28) {
    this.vertexBufferLayout = e33, this._parameters = t28;
  }
  allocate(e33) {
    return this.vertexBufferLayout.createBuffer(e33);
  }
  elementCount() {
    return "begin-end" === this._parameters.placement ? 12 : 6;
  }
  write(s26, i19, n26, o24, h25) {
    const c34 = n26.vertexAttributes.get(O4.POSITION).data, u32 = c34.length / 3;
    let p23 = [1, 0, 0];
    const m18 = n26.vertexAttributes.get(O4.NORMAL);
    this._parameters.worldSpace && r(m18) && (p23 = m18.data);
    let d26 = 1, A13 = 0;
    this._parameters.vvSizeEnabled ? A13 = n26.vertexAttributes.get(O4.SIZEFEATUREATTRIBUTE).data[0] : n26.vertexAttributes.has(O4.SIZE) && (d26 = n26.vertexAttributes.get(O4.SIZE).data[0]);
    let f27 = [1, 1, 1, 1], E21 = 0;
    this._parameters.vvColorEnabled ? E21 = n26.vertexAttributes.get(O4.COLORFEATUREATTRIBUTE).data[0] : n26.vertexAttributes.has(O4.COLOR) && (f27 = n26.vertexAttributes.get(O4.COLOR).data);
    let T10 = 0;
    this._parameters.vvOpacityEnabled && (T10 = n26.vertexAttributes.get(O4.OPACITYFEATUREATTRIBUTE).data[0]);
    const v21 = new Float32Array(o24.buffer);
    let _21 = h25 * (this.vertexBufferLayout.stride / 4);
    const S19 = (e33, t28, r30, a35) => {
      if (v21[_21++] = e33[0], v21[_21++] = e33[1], v21[_21++] = e33[2], v21[_21++] = r30[0], v21[_21++] = r30[1], v21[_21++] = t28[0], v21[_21++] = t28[1], v21[_21++] = t28[2], this._parameters.worldSpace && (v21[_21++] = p23[0], v21[_21++] = p23[1], v21[_21++] = p23[2]), this._parameters.vvSizeEnabled ? v21[_21++] = A13 : v21[_21++] = d26, this._parameters.vvColorEnabled) v21[_21++] = E21;
      else {
        const e34 = Math.min(4 * a35, f27.length - 4);
        v21[_21++] = f27[e34 + 0], v21[_21++] = f27[e34 + 1], v21[_21++] = f27[e34 + 2], v21[_21++] = f27[e34 + 3];
      }
      this._parameters.vvOpacityEnabled && (v21[_21++] = T10);
    };
    let O13;
    !function(e33) {
      e33[e33.ASCENDING = 1] = "ASCENDING", e33[e33.DESCENDING = -1] = "DESCENDING";
    }(O13 || (O13 = {}));
    const R12 = (e33, i20) => {
      const n27 = o2(b17, c34[3 * e33], c34[3 * e33 + 1], c34[3 * e33 + 2]), o25 = g15;
      let h26 = e33 + i20;
      do {
        o2(o25, c34[3 * h26], c34[3 * h26 + 1], c34[3 * h26 + 2]), h26 += i20;
      } while (G(n27, o25) && h26 >= 0 && h26 < u32);
      s26 && (O2(n27, n27, s26), O2(o25, o25, s26)), S19(n27, o25, [-1, -1], e33), S19(n27, o25, [1, -1], e33), S19(n27, o25, [1, 1], e33), S19(n27, o25, [-1, -1], e33), S19(n27, o25, [1, 1], e33), S19(n27, o25, [-1, 1], e33);
    }, I13 = this._parameters.placement;
    "begin" !== I13 && "begin-end" !== I13 || R12(0, O13.ASCENDING), "end" !== I13 && "begin-end" !== I13 || R12(u32 - 1, O13.DESCENDING);
  }
};
var b17 = n5();
var g15 = n5();

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DLineSymbolLayer.js
var N3 = ["polyline", "polygon", "extent"];
var q7 = class _q extends y7 {
  constructor(e33, t28, r30, a35) {
    super(e33, t28, r30, a35);
  }
  async doLoad() {
    if (this._vvConvertOptions = { modelSize: [1, 1, 1], symbolSize: [1, 1, 1], unitInMeters: 1, transformation: { anchor: [0, 0, 0], scale: [1, 1, 1], rotation: [0, 0, 0] }, supportedTypes: { size: true, color: true, opacity: true, rotation: false } }, this._context.renderer && this._context.renderer.visualVariables && this._context.renderer.visualVariables.length > 0 ? this._fastUpdates = I9(this._context.renderer, this._vvConvertOptions) : this._fastUpdates = { enabled: false }, !this._drivenProperties.size) {
      if ((null != this.symbolLayer.size ? this.symbolLayer.size : e6(1)) < 0) throw new s2("graphics3dlinesymbollayer:invalid-size", "Symbol sizes may not be negative values");
    }
    this._markerTexture = r(this.symbolLayer.marker) && r(this._context.sharedResources.textures) ? f14(this._context.sharedResources.textures, r23(this.symbolLayer.marker.style)) : null;
  }
  _getMaterialParameters(e33, t28 = false) {
    var _a;
    const r30 = this._getCombinedOpacityAndColor(t28 && this._markerColor || this._materialColor);
    this._patternHidesLine && !t28 && (r30[3] = 0);
    const a35 = { width: this._computeMaterialWidth((_a = this.symbolLayer) == null ? void 0 : _a.size), color: r30, hasPolygonOffset: true, join: this.symbolLayer.join || "miter", cap: n20(this.symbolLayer.cap || "butt"), hasSlicePlane: this._context.slicePlaneEnabled, isClosed: e33, stipplePattern: a22(this.symbolLayer.pattern), stippleScaleWithLineWidth: true };
    return this._fastUpdates && this._fastUpdates.visualVariables ? { ...a35, ...this._fastUpdates.materialParameters } : a35;
  }
  get _materialColor() {
    return o(this.symbolLayer.material, (e33) => e33.color);
  }
  get _markerColor() {
    return o(this.symbolLayer.marker, (e33) => e33.color);
  }
  get _lineMaterial() {
    return t(this._lineMaterialCached) && (this._lineMaterialCached = new z3(this._getMaterialParameters(false)), this._context.stage.add(this._lineMaterialCached)), this._lineMaterialCached;
  }
  get _ringMaterial() {
    return t(this._ringMaterialCached) && (this._ringMaterialCached = new z3(this._getMaterialParameters(true)), this._context.stage.add(this._ringMaterialCached)), this._ringMaterialCached;
  }
  get _wireframeLineMaterial() {
    return t(this._wireframeLineMaterialCached) && (this._wireframeLineMaterialCached = new z3({ ...this._getMaterialParameters(false), wireframe: true }), this._context.stage.add(this._wireframeLineMaterialCached)), this._wireframeLineMaterialCached;
  }
  get _wireframeRingMaterial() {
    return t(this._wireframeRingMaterialCached) && (this._wireframeRingMaterialCached = new z3({ ...this._getMaterialParameters(true), wireframe: true }), this._context.stage.add(this._wireframeRingMaterialCached)), this._wireframeRingMaterialCached;
  }
  get _markerMaterial() {
    return t(this._markerMaterialCached) && r(this.symbolLayer.marker) && r(this._markerTexture) && (this._markerMaterialCached = new v14({ ...this._getMaterialParameters(false, true), placement: this.symbolLayer.marker.placement, textureId: this._markerTexture.texture.id }), this._context.stage.add(this._markerMaterialCached)), this._markerMaterialCached;
  }
  destroy() {
    super.destroy(), this._forEachMaterial((e33) => this._context.stage.remove(e33)), this._lineMaterialCached = null, this._ringMaterialCached = null, this._wireframeLineMaterialCached = null, this._wireframeRingMaterialCached = null, this._markerMaterialCached = null, this._markerTexture = y(this._markerTexture);
  }
  _getDrivenSize(e33) {
    return this._drivenProperties.size && e33.size ? u6(c18(e33.size)) : 1;
  }
  _getSizeFeatureAttributeData(e33) {
    return this._fastUpdates.enabled && this._fastUpdates.visualVariables.size ? v13(this._fastUpdates.visualVariables.size.field, e33) : null;
  }
  _getDrivenColor(e33) {
    const t28 = r6(1, 1, 1, 1);
    return this._drivenProperties.color && e33.color && (t28[0] = e33.color[0], t28[1] = e33.color[1], t28[2] = e33.color[2], e33.color.length > 0 && (t28[3] = e33.color[3])), this._drivenProperties.opacity && e33.opacity && (t28[3] = e33.opacity), t28;
  }
  _getColorFeatureAttributeData(e33) {
    return this._fastUpdates.enabled && this._fastUpdates.visualVariables.color ? v13(this._fastUpdates.visualVariables.color.field, e33) : null;
  }
  _getOpacityFeatureAttributeData(e33) {
    return this._fastUpdates.enabled && this._fastUpdates.visualVariables.opacity ? v13(this._fastUpdates.visualVariables.opacity.field, e33) : null;
  }
  createGraphics3DGraphic(e33) {
    const t28 = e33.graphic;
    if (!this._validateGeometry(t28.geometry, N3, this.symbolLayer.type)) return null;
    const r30 = this.setGraphicElevationContext(t28, new h15());
    return this.ensureDrapedStatus("on-the-ground" === r30.mode), this.draped ? this._createAsOverlay(e33, this._context.layer.uid) : this._createAs3DShape(e33, r30, t28.uid);
  }
  applyRendererDiff(e33, t28) {
    for (const r30 in e33.diff) {
      if ("visualVariables" !== r30) return e22.Recreate_Symbol;
      if (!P6(this._fastUpdates, t28, this._vvConvertOptions)) return e22.Recreate_Symbol;
      this._forEachMaterial((e34) => e34.setParameters(this._fastUpdates.materialParameters));
    }
    return e22.Fast_Update;
  }
  prepareSymbolLayerPatch(e33) {
    var _a, _b;
    if ("partial" !== e33.diff.type) return;
    const t28 = e33.diff.diff, r30 = {};
    "complete" === ((_a = t28.size) == null ? void 0 : _a.type) && (r30.width = this._computeMaterialWidth(t28.size.newValue), delete t28.size), "complete" === ((_b = t28.cap) == null ? void 0 : _b.type) && (r30.cap = n20(l(t28.cap.newValue, "butt")), delete t28.cap);
    const a35 = this._prepareMarkerPatch(e33, t28);
    this._prepareMaterialPatch(e33, t28, a35), e33.symbolLayerStatePatches.push(() => this._forEachMaterial((e34) => e34.setParameters(r30)));
  }
  layerOpacityChanged() {
    this._forEachMaterial((e33, t28) => this._updateMaterialLayerOpacity(e33, t28));
  }
  _forEachMaterial(e33) {
    r(this._lineMaterialCached) && e33(this._lineMaterialCached), r(this._ringMaterialCached) && e33(this._ringMaterialCached), r(this._wireframeLineMaterialCached) && e33(this._wireframeLineMaterialCached), r(this._wireframeRingMaterialCached) && e33(this._wireframeRingMaterialCached), r(this._markerMaterialCached) && e33(this._markerMaterialCached, true);
  }
  _updateMaterialLayerOpacity(e33, t28 = false) {
    const r30 = e33.parameters.color, a35 = x(this.symbolLayer, "material", "color"), i19 = this._patternHidesLine && !t28 ? 0 : this._getCombinedOpacity(a35), s26 = r6(r30[0], r30[1], r30[2], i19);
    e33.setParameters({ color: s26 });
  }
  layerElevationInfoChanged(e33, t28, r30) {
    const a35 = this._elevationContext.mode, i19 = m7(_q.elevationModeChangeTypes, r30, a35);
    if (i19 !== j8.UPDATE) return i19;
    const s26 = p12(a35);
    return this.updateGraphics3DGraphicElevationInfo(e33, t28, () => s26);
  }
  slicePlaneEnabledChanged() {
    const e33 = { hasSlicePlane: this._context.slicePlaneEnabled };
    return this._forEachMaterial((t28) => t28.setParameters(e33)), true;
  }
  physicalBasedRenderingChanged() {
    return true;
  }
  pixelRatioChanged() {
    return true;
  }
  skipHighSymbolLodsChanged() {
    return true;
  }
  _getGeometryAsPolygonOrPolyline(e33) {
    switch (e33.type) {
      case "extent":
        if (e33 instanceof w4) return v3.fromExtent(e33);
        break;
      case "polygon":
      case "polyline":
        return e33;
    }
    return null;
  }
  _createAs3DShape(e33, r30, a35) {
    const i19 = e33.graphic, s26 = this._getGeometryAsPolygonOrPolyline(i19.geometry), o24 = "polygon" === s26.type ? s26.rings : s26.paths, n26 = new Array(), l34 = a7(), h25 = p14(s26, this._context.elevationProvider, this._context.renderCoordsHelper, r30), c34 = "polygon" === s26.type ? "rings" : "paths";
    this._logGeometryCreationWarnings(h25, o24, c34, "LineSymbol3DLayer");
    for (let m18 = 0; m18 < h25.lines.length; m18++) {
      const r31 = h25.lines[m18], i20 = r31.position, o25 = r31.mapPositions;
      if (r(this._context.clippingExtent) && (S(l34), M3(l34, o25), !Y(l34, this._context.clippingExtent))) continue;
      const c35 = this._createGeometry("polygon" === s26.type ? this._ringMaterial : this._lineMaterial, e33, i20, o25, s26.type, J.ELEVATED, a35);
      n26.push(c35), t17.LINE_WIREFRAMES && n26.push(c35.instantiate({ material: "polygon" === s26.type ? this._wireframeRingMaterial : this._wireframeLineMaterial })), r(this._markerMaterial) && n26.push(c35.instantiate({ material: this._markerMaterial }));
    }
    if (0 === n26.length) return null;
    const p23 = new x7({ geometries: n26, castShadow: false, metadata: { layerUid: this._context.layer.uid, graphicUid: a35 } }), d26 = new S8(this, p23, n26, null, null, I7, r30);
    return d26.alignedSampledElevation = h25.sampledElevation, d26.needsElevationUpdates = p12(r30.mode), d26;
  }
  _createGeometry(e33, t28, r30, a35, i19, s26, o24) {
    const n26 = "polygon" === i19, l34 = this._fastUpdates.enabled && this._fastUpdates.visualVariables.color, h25 = this._fastUpdates.enabled && this._fastUpdates.visualVariables.size, c34 = this._context.stage.renderView.getObjectAndLayerIdColor({ graphicUid: o24, layerUid: this._context.layer.uid });
    return b11(e33, { overlayInfo: s26 === J.DRAPED ? { spatialReference: this._context.overlaySR, renderCoordsHelper: this._context.renderCoordsHelper } : null, removeDuplicateStartEnd: n26, mapPositions: a35, attributeData: { position: r30, size: h25 ? null : this._getDrivenSize(t28.renderingInfo), color: l34 ? null : this._getDrivenColor(t28.renderingInfo), sizeFeature: this._getSizeFeatureAttributeData(t28.graphic), colorFeature: this._getColorFeatureAttributeData(t28.graphic), opacityFeature: this._getOpacityFeatureAttributeData(t28.graphic) } }, c34);
  }
  _createAsOverlay(e33, r30) {
    const a35 = e33.graphic, i19 = this._getGeometryAsPolygonOrPolyline(a35.geometry), s26 = "polygon" === i19.type ? i19.rings : i19.paths, o24 = "polygon" === i19.type ? this._ringMaterial : this._lineMaterial;
    o24.renderPriority = this._renderPriority;
    const l34 = t17.LINE_WIREFRAMES ? "polygon" === i19.type ? this._wireframeRingMaterial : this._wireframeLineMaterial : null, h25 = this._markerMaterial;
    r(l34) && (l34.renderPriority = this._renderPriority - 1e-3), r(h25) && (h25.renderPriority = this._renderPriority - 2e-3);
    const c34 = new Array(), p23 = a7(), d26 = S(), f27 = c16(i19, this._context.overlaySR), C11 = "polygon" === i19.type ? "rings" : "paths";
    this._logGeometryCreationWarnings(f27, s26, C11, "LineSymbol3DLayer");
    for (const m18 of f27.lines) {
      if (S(p23), M3(p23, m18.position), !Y(p23, this._context.clippingExtent)) continue;
      f4(d26, p23);
      const s27 = (t28) => {
        const s28 = this._createGeometry(t28, e33, m18.position, void 0, i19.type, J.DRAPED, a35.uid), o25 = new h14(s28, { layerUid: r30, graphicUid: a35.uid });
        return c34.push(o25), o25;
      };
      if (r(h25)) {
        const e34 = s27(h25), t28 = e2(this.symbolLayer.marker).placement;
        "begin" !== t28 && "begin-end" !== t28 || M3(p23, m18.position, 0, 1), "end" !== t28 && "begin-end" !== t28 || M3(p23, m18.position, m18.position.length - 3, 1), this._updateBoundingSphere(e34, p23);
      }
      const f28 = s27(o24);
      if (this._updateBoundingSphere(f28, p23), t17.LINE_WIREFRAMES) {
        const e34 = s27(l34);
        this._updateBoundingSphere(e34, p23);
      }
    }
    return new l26(this, c34, d26, this._context.drapeSourceRenderer);
  }
  _updateBoundingSphere(e33, t28) {
    r5(e33.boundingSphere, 0.5 * (t28[0] + t28[3]), 0.5 * (t28[1] + t28[4]), 0, 0.5 * Math.sqrt((t28[3] - t28[0]) * (t28[3] - t28[0]) + (t28[4] - t28[1]) * (t28[4] - t28[1])));
  }
  get _patternHidesLine() {
    const e33 = this.symbolLayer.pattern;
    return r(e33) && "style" === e33.type && "none" === e33.style;
  }
  _computeMaterialWidth(e33) {
    return e33 = l(e33, e6(1)), this._drivenProperties.size ? this._fastUpdates.enabled && this._fastUpdates.visualVariables.size ? u6(1) : 1 : u6(e33);
  }
  _prepareMaterialPatch(e33, t28, i19) {
    var _a;
    const s26 = t28.material;
    if (t(s26)) return void (i19.changed && i19.useMaterialColor && this._patchMaterialColor(this._getCombinedOpacityAndColor(this._materialColor), this._markerMaterialCached, e33));
    if ("collection" === s26.type) return;
    const o24 = "complete" === s26.type ? o(s26.newValue, (e34) => e34.color) : "complete" === ((_a = s26.diff.color) == null ? void 0 : _a.type) ? s26.diff.color.newValue : null, n26 = this._getCombinedOpacityAndColor(o24);
    i19.useMaterialColor && this._patchMaterialColor(t5(n26), this._markerMaterialCached, e33), this._patternHidesLine && (n26[3] = 0), this._patchMaterialColor(n26, this._lineMaterialCached, e33), delete t28.material;
  }
  _prepareMarkerPatch(e33, r30) {
    const i19 = r30.marker;
    if (t(i19) || "partial" !== i19.type || r(i19.diff.style) || r(i19.diff.placement) || r(i19.diff.color) && "complete" !== i19.diff.color.type) return { changed: false, useMaterialColor: t(this._markerColor) };
    const s26 = i19.diff.color;
    if (t(s26)) return delete r30.marker, { changed: false, useMaterialColor: t(this._markerColor) };
    const o24 = e2(s26.newValue);
    return t(o24) ? (delete r30.marker, { changed: true, useMaterialColor: true }) : (this._patchMaterialColor(this._getCombinedOpacityAndColor(o24), this._markerMaterialCached, e33), delete r30.marker, { changed: true, useMaterialColor: false });
  }
  _patchMaterialColor(e33, t28, r30) {
    t(t28) || r30.symbolLayerStatePatches.push(() => t28.setParameters({ color: e33 }));
  }
};
var J;
q7.elevationModeChangeTypes = { definedChanged: j8.RECREATE, staysOnTheGround: j8.NONE, onTheGroundChanged: j8.RECREATE }, function(e33) {
  e33[e33.DRAPED = 0] = "DRAPED", e33[e33.ELEVATED = 1] = "ELEVATED";
}(J || (J = {}));

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DMeshFillSymbolLayer.js
var de3 = ["mesh"];
var xe2 = class extends y7 {
  constructor(e33, t28, r30, o24) {
    super(e33, t28, r30, o24), this._materials = /* @__PURE__ */ new Map(), this._textures = /* @__PURE__ */ new Map(), this.ensureDrapedStatus(false);
  }
  async doLoad() {
    t17.DRAW_MESH_GEOMETRY_NORMALS && (this._debugVertexNormalMaterial = new q5({ color: [1, 0, 1, 1] }), this._debugFaceNormalMaterial = new q5({ color: [0, 1, 1, 1] }));
  }
  destroy() {
    super.destroy(), this._context.stage.removeMany(Array.from(this._materials.values(), (e33) => e33.material)), this._context.stage.removeMany(Array.from(this._textures.values())), this._materials.clear(), this._textures.clear();
  }
  createGraphics3DGraphic(e33) {
    const t28 = e33.graphic;
    if (!this._validateGeometry(t28.geometry, de3, "fill on mesh-3d")) return null;
    const r30 = this.setGraphicElevationContext(t28, new h15()), o24 = e33.renderingInfo;
    return this._createAs3DShape(t28, o24, r30, t28.uid);
  }
  layerOpacityChanged(e33, r30) {
    const o24 = this._getLayerOpacity();
    this._materials.forEach((e34) => {
      e34.material.setParameters({ layerOpacity: o24 });
      const t28 = e34.material.parameters;
      this._setMaterialTransparentParameter(t28, e34), e34.material.setParameters({ transparent: t28.transparent });
    }), e33.forEach((e34) => {
      const a35 = r30(e34);
      r(a35) && a35.layerOpacityChanged(o24, this._context.isAsync);
    });
  }
  layerElevationInfoChanged(e33, t28) {
    return this.updateGraphics3DGraphicElevationInfo(e33, t28, g7);
  }
  slicePlaneEnabledChanged(e33, r30) {
    return this._materials.forEach((e34) => {
      e34.material.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled });
    }), e33.forEach((e34) => {
      const o24 = r30(e34);
      r(o24) && o24.slicePlaneEnabledChanged(this._context.slicePlaneEnabled, this._context.isAsync);
    }), true;
  }
  physicalBasedRenderingChanged() {
    const e33 = this._usePBR();
    return this._materials.forEach((t28) => t28.material.setParameters({ usePBR: e33 })), true;
  }
  pixelRatioChanged() {
    return true;
  }
  skipHighSymbolLodsChanged() {
    return true;
  }
  _requiresSymbolVertexColors() {
    return this._drivenProperties.color || this._drivenProperties.opacity;
  }
  _colorOrTextureUid(t28) {
    return t(t28) ? "-" : t28 instanceof l11 ? t28.toHex() : t28.contentHash;
  }
  _materialPropertiesDefault(e33, t28) {
    const r30 = this._requiresSymbolVertexColors(), o24 = !!e33.vertexAttributes.color, a35 = !!e33.vertexAttributes.tangent;
    return { hasSymbolVertexColors: r30, hasVertexColors: o24, hasVertexTangents: a35, uid: `vc:${o24},vt:${a35},vct${t28},svc:${r30}` };
  }
  _materialProperties(e33, t28, r30) {
    const o24 = this._materialPropertiesDefault(e33, r30);
    if (!t28.material) return o24;
    const { color: a35, colorTexture: n26, normalTexture: s26, doubleSided: i19, alphaCutoff: l34, alphaMode: c34 } = t28.material, u32 = this._colorOrTextureUid(a35), m18 = this._colorOrTextureUid(n26), h25 = this._colorOrTextureUid(s26);
    if (o24.color = a35, o24.colorTexture = n26, o24.normalTexture = s26, o24.uid = `${o24.uid},cmuid:${u32},ctmuid:${m18},ntmuid:${h25},ds:${i19},ac:${l34},am:${c34}`, t28.material instanceof c11) {
      const { metallic: e34, roughness: r31, metallicRoughnessTexture: a36, emissiveColor: n27, emissiveTexture: s27, occlusionTexture: i20 } = t28.material, l35 = this._colorOrTextureUid(a36), c35 = this._colorOrTextureUid(n27), u33 = this._colorOrTextureUid(s27), m19 = this._colorOrTextureUid(i20);
      o24.metallic = e34, o24.roughness = r31, o24.metallicRoughnessTexture = a36, o24.emissiveColor = n27, o24.emissiveTexture = s27, o24.occlusionTexture = i20, o24.colorTextureTransform = t28.material.colorTextureTransform, o24.normalTextureTransform = t28.material.normalTextureTransform, o24.emissiveTextureTransform = t28.material.emissiveTextureTransform, o24.occlusionTextureTransform = t28.material.occlusionTextureTransform, o24.metallicRoughnessTextureTransform = t28.material.metallicRoughnessTextureTransform, o24.uid = `${o24.uid},mrm:${e34},mrr:${r31},mrt:${l35},emuid:${c35},etmuid:${u33},otmuid:${m19}`;
    }
    return o24;
  }
  _setInternalColorValueParameters(t28, r30) {
    r30.diffuse = l11.toUnitRGB(t28), r30.opacity = t28.a;
  }
  _getLoadableTextureResource(e33) {
    return e33.data ? e33.data : e33.url;
  }
  _getInternalTextureId(e33) {
    const r30 = this._getInternalTexture(e33, u10.Opaque);
    return r(r30) ? r30.id : null;
  }
  _getInternalTexture(e33, r30) {
    const o24 = this._getLoadableTextureResource(e33);
    if (!o24) return null;
    const a35 = `${e33.contentHash}/${r30}`;
    let n26 = this._textures.get(a35);
    return n26 || (n26 = new G3(t13(o24) ? o24.data : o24, { mipmap: true, wrap: this._castTextureWrap(e33.wrap), noUnpackFlip: true, preMultiplyAlpha: !t13(o24) && r30 !== u10.Opaque, encoding: t13(o24) && r(o24.encoding) ? o24.encoding : void 0 }), this._textures.set(a35, n26), this._context.stage.add(n26), this._context.stage.loadImmediate(n26)), n26;
  }
  _castTextureWrap(e33 = "repeat") {
    if ("string" == typeof e33) {
      const t28 = this._castTextureWrapIndividual(e33);
      return { s: t28, t: t28 };
    }
    return { s: this._castTextureWrapIndividual(e33.horizontal), t: this._castTextureWrapIndividual(e33.vertical) };
  }
  _castTextureWrapIndividual(e33) {
    switch (e33) {
      case "clamp":
        return D3.CLAMP_TO_EDGE;
      case "mirror":
        return D3.MIRRORED_REPEAT;
      default:
        return D3.REPEAT;
    }
  }
  _setInternalMaterialParameters(r30, o24) {
    if (r(r30.color) && this._setInternalColorValueParameters(r30.color, o24), r(r30.colorTexture)) {
      const e33 = this._getInternalTexture(r30.colorTexture, o24.textureAlphaMode);
      r(e33) ? (o24.textureId = e33.id, o24.textureAlphaPremultiplied = !!e33.params.preMultiplyAlpha) : o24.textureId = void 0;
    }
    r(r30.normalTexture) && (o24.normalTextureId = this._getInternalTextureId(r30.normalTexture)), r(r30.emissiveColor) && (o24.emissiveFactor = l11.toUnitRGB(r30.emissiveColor)), r(r30.emissiveTexture) && (o24.emissiveTextureId = this._getInternalTextureId(r30.emissiveTexture)), r(r30.occlusionTexture) && (o24.occlusionTextureId = this._getInternalTextureId(r30.occlusionTexture)), r(r30.metallicRoughnessTexture) && (o24.metallicRoughnessTextureId = this._getInternalTextureId(r30.metallicRoughnessTexture)), o24.colorTextureTransformMatrix = c12(r30.colorTextureTransform), o24.normalTextureTransformMatrix = c12(r30.normalTextureTransform), o24.occlusionTextureTransformMatrix = c12(r30.occlusionTextureTransform), o24.emissiveTextureTransformMatrix = c12(r30.emissiveTextureTransform), o24.metallicRoughnessTextureTransformMatrix = c12(r30.metallicRoughnessTextureTransform);
  }
  _setExternalMaterialParameters(r30) {
    const o24 = this._drivenProperties.color;
    let a35 = r(this.symbolLayer.material) ? this.symbolLayer.material.colorMixMode : null;
    if (o24) r30.externalColor = _3;
    else {
      const o25 = r(this.symbolLayer.material) ? this.symbolLayer.material.color : null;
      r(o25) ? r30.externalColor = l11.toUnitRGBA(o25) : (a35 = null, r30.externalColor = _3);
    }
    a35 && (r30.colorMixMode = a35), r30.castShadows = !!this.symbolLayer.castShadows;
  }
  _hasTransparentVertexColors(e33) {
    const t28 = e33.vertexAttributes.color;
    if (t(t28)) return false;
    for (let r30 = 3; r30 < t28.length; r30 += 4) if (255 !== t28[r30]) return true;
    return false;
  }
  _getOrCreateMaterial(e33, r30) {
    var _a, _b, _c;
    const o24 = (_a = r30.material) == null ? void 0 : _a.color, a35 = (_b = r30.material) == null ? void 0 : _b.colorTexture, n26 = (_c = r30.material) == null ? void 0 : _c.alphaMode, s26 = "blend" === n26, i19 = !("opaque" === n26) && (this._hasTransparentVertexColors(e33) || r(o24) && o24.a < 1 || r(a35) && a35.transparent || s26), l34 = this._materialProperties(e33, r30, i19), c34 = this._materials.get(l34.uid);
    if (c34) return c34.material;
    const u32 = { material: null, isComponentTransparent: i19, alphaMode: r30.material ? r30.material.alphaMode : "opaque" }, m18 = null == l34.metallicRoughnessTexture && null == l34.metallic && null == l34.roughness, h25 = { usePBR: this._usePBR(), isSchematic: m18, hasVertexColors: l34.hasVertexColors, hasSymbolColors: l34.hasSymbolVertexColors, hasVertexTangents: l34.hasVertexTangents, ambient: f3, diffuse: l3, opacity: 1, doubleSided: true, doubleSidedType: "winding-order", cullFace: n11.None, layerOpacity: this._getLayerOpacity(), hasSlicePlane: this._context.slicePlaneEnabled, initTextureTransparent: true };
    m18 || (h25.mrrFactors = [null != l34.metallic ? l34.metallic : 1, null != l34.roughness ? l34.roughness : 1, 0.5]), r30.material && (h25.doubleSided = r30.material.doubleSided, h25.cullFace = r30.material.doubleSided ? n11.None : n11.Back, h25.textureAlphaCutoff = r30.material.alphaCutoff), this._setExternalMaterialParameters(h25), this._setMaterialTransparentParameter(h25, u32), this._setInternalMaterialParameters(l34, h25);
    const f27 = new E11(h25);
    return u32.material = f27, this._materials.set(l34.uid, u32), this._context.stage.add(f27), f27;
  }
  _usePBR() {
    return this._context.physicalBasedRenderingEnabled;
  }
  _setMaterialTransparentParameter(e33, t28) {
    e33.transparent = this.needsDrivenTransparentPass || t28.isComponentTransparent || e33.layerOpacity < 1 || e33.opacity < 1 || e33.externalColor && e33.externalColor[3] < 1, "auto" === t28.alphaMode ? e33.textureAlphaMode = e33.transparent ? u10.MaskBlend : u10.Opaque : e33.textureAlphaMode = "opaque" === t28.alphaMode ? u10.Opaque : "mask" === t28.alphaMode ? u10.Mask : u10.Blend;
  }
  _addDebugNormals(e33, t28) {
    const r30 = t28.length, o24 = e33.spatialReference.isGeographic ? 20015077 / 180 : 1, a35 = 0.1 * Math.max(e33.extent.width * o24, e33.extent.height * o24, e33.extent.zmax - e33.extent.zmin), n26 = [], s26 = [], i19 = [], l34 = [];
    for (let h25 = 0; h25 < r30; h25++) {
      const e34 = t28[h25], r31 = e34.vertexAttributes.get(O4.POSITION), o25 = e34.vertexAttributes.get(O4.NORMAL), c35 = e34.indices.get(O4.POSITION), u33 = e34.indices.get(O4.NORMAL), m19 = r31.data, g23 = o25.data;
      for (let t29 = 0; t29 < c35.length; t29++) {
        const e35 = 3 * c35[t29], r32 = 3 * u33[t29];
        for (let t30 = 0; t30 < 3; t30++) n26.push(m19[e35 + t30]);
        for (let t30 = 0; t30 < 3; t30++) n26.push(m19[e35 + t30] + g23[r32 + t30] * a35);
        if (s26.push(s26.length), s26.push(s26.length), t29 % 3 == 0) {
          this._calculateFaceNormal(m19, c35, t29, Ce), this._getFaceVertices(m19, c35, t29, ye2, be2, ve), u3(ye2, ye2, be2), u3(ye2, ye2, ve), g2(ye2, ye2, 1 / 3);
          for (let e36 = 0; e36 < 3; e36++) i19.push(ye2[e36]);
          for (let e36 = 0; e36 < 3; e36++) i19.push(ye2[e36] + Ce[e36] * a35);
          l34.push(l34.length), l34.push(l34.length);
        }
      }
    }
    const c34 = t28[0].transformation, u32 = new v8(this._debugVertexNormalMaterial, [[O4.POSITION, new s7(n26, 3, true)]], [[O4.POSITION, s26]], null, e13.Line);
    t28.push(u32), u32.transformation = c34;
    const m18 = new v8(this._debugFaceNormalMaterial, [[O4.POSITION, new s7(i19, 3, true)]], [[O4.POSITION, l34]], null, e13.Line);
    m18.transformation = c34, t28.push(m18);
  }
  _createAs3DShape(e33, o24, a35, n26) {
    const s26 = e33.geometry;
    if ("mesh" !== s26.type) return null;
    const i19 = this._createGeometryInfo(s26, o24, n26);
    if (t(i19)) return null;
    const { geometries: l34, objectTransformation: c34 } = i19;
    t17.DRAW_MESH_GEOMETRY_NORMALS && this._addDebugNormals(s26, l34);
    const u32 = new x7({ geometries: l34, metadata: { layerUid: this._context.layer.uid, graphicUid: n26 } });
    u32.transformation = c34;
    const m18 = f10(this.symbolLayer, { opacity: this._getLayerOpacity() }), h25 = r(m18) ? new f19(l34[0].material, [m18], { mergeGeometries: true, hasSlicePlane: this._context.slicePlaneEnabled }) : null, f27 = new S8(this, u32, l34, null, null, d13, a35, h25);
    f27.needsElevationUpdates = g7(a35.mode), f27.useObjectOriginAsAttachmentOrigin = true, a35.centerPointInElevationSR = this._getCenterPointInElevationSR(u32);
    const { elevationProvider: p23, renderCoordsHelper: g23 } = this._context, d26 = (e34, t28) => f16(e34, p23, a35, g23, t28);
    return f27.alignedSampledElevation = d13(f27, a35, p23.spatialReference, d26, g23), f27;
  }
  _getCenterPointInElevationSR(e33) {
    const r30 = M4(0, 0, 0, r(this._context.elevationProvider.spatialReference) ? this._context.elevationProvider.spatialReference : null);
    return Hn([e33.transformation[12], e33.transformation[13], e33.transformation[14]], this._context.renderCoordsHelper.spatialReference, r30), r30;
  }
  _createComponentNormals(e33, t28, r30, o24) {
    switch (r30.shading || "flat") {
      default:
      case "source":
        return this._createComponentNormalsSource(e33, t28, r30, o24);
      case "flat":
        return this._createComponentNormalsFlat(e33, o24);
      case "smooth":
        return this._createComponentNormalsSmooth(e33, o24);
    }
  }
  _createComponentNormalsSource(e33, t28, o24, a35) {
    if (t(t28)) return this._createComponentNormalsFlat(e33, a35);
    let n26 = false;
    if (!o24.trustSourceNormals) for (let r30 = 0; r30 < a35.length; r30 += 3) {
      this._calculateFaceNormal(e33, a35, r30, Ce);
      for (let e34 = 0; e34 < 3; e34++) {
        const o25 = 3 * a35[r30 + e34];
        ye2[0] = t28[o25 + 0], ye2[1] = t28[o25 + 1], ye2[2] = t28[o25 + 2], P(Ce, ye2) < 0 && (t28[o25 + 0] = -t28[o25 + 0], t28[o25 + 1] = -t28[o25 + 1], t28[o25 + 2] = -t28[o25 + 2], n26 = true);
      }
    }
    return new _e3(t28, a35, n26);
  }
  _createComponentNormalsFlat(e33, t28) {
    const r30 = n14(t28.length), o24 = new Array(3 * t28.length);
    for (let a35 = 0; a35 < t28.length; a35 += 3) {
      const n26 = this._calculateFaceNormal(e33, t28, a35, Ce);
      for (let e34 = 0; e34 < 3; e34++) r30[a35 + e34] = n26[e34], o24[a35 + e34] = a35 / 3;
    }
    return new _e3(r30, o24, false);
  }
  _createComponentNormalsSmooth(e33, t28) {
    const r30 = {};
    for (let n26 = 0; n26 < t28.length; n26 += 3) {
      const o25 = this._calculateFaceNormal(e33, t28, n26, Ce);
      for (let e34 = 0; e34 < 3; e34++) {
        const a36 = t28[n26 + e34];
        let s26 = r30[a36];
        s26 || (s26 = { normal: n5(), count: 0 }, r30[a36] = s26), u3(s26.normal, s26.normal, o25), s26.count++;
      }
    }
    const o24 = n14(3 * t28.length), a35 = new Array(3 * t28.length);
    for (let n26 = 0; n26 < t28.length; n26++) {
      const e34 = r30[t28[n26]];
      1 !== e34.count && (z(e34.normal, e34.normal), e34.count = 1);
      for (let t29 = 0; t29 < 3; t29++) o24[3 * n26 + t29] = e34.normal[t29];
      a35[n26] = n26;
    }
    return new _e3(o24, a35, false);
  }
  _getFaceVertices(e33, t28, r30, o24, a35, n26) {
    const s26 = 3 * t28[r30 + 0], i19 = 3 * t28[r30 + 1], l34 = 3 * t28[r30 + 2];
    o24[0] = e33[s26 + 0], o24[1] = e33[s26 + 1], o24[2] = e33[s26 + 2], a35[0] = e33[i19 + 0], a35[1] = e33[i19 + 1], a35[2] = e33[i19 + 2], n26[0] = e33[l34 + 0], n26[1] = e33[l34 + 1], n26[2] = e33[l34 + 2];
  }
  _calculateFaceNormal(e33, t28, r30, o24) {
    return this._getFaceVertices(e33, t28, r30, ye2, be2, ve), e5(be2, be2, ye2), e5(ve, ve, ye2), _(ye2, be2, ve), z(o24, ye2), o24;
  }
  _getOrCreateComponents(e33) {
    return l(e33.components, Me);
  }
  _createPositionBuffer(e33, r30) {
    let o24 = e33.vertexAttributes.position;
    const a35 = r30.reprojection === je.ECEF ? r30.transformBeforeProject : null;
    if (r(a35) && (o24 = O5(o24, new Float64Array(o24.length), a35)), r30.reprojection === je.NONE) return r30.needsBufferCopy ? new Float64Array(o24) : o24;
    const n26 = r(a35) ? o24 : new Float64Array(o24.length);
    return xn(o24, e33.spatialReference, 0, n26, this._context.renderCoordsHelper.spatialReference, 0, o24.length / 3), n26;
  }
  _createNormalBuffer(e33, o24, a35) {
    let n26 = e33.vertexAttributes.normal;
    if (t(n26)) return null;
    const s26 = a35.reprojection === je.ECEF ? a35.transformBeforeProject : null;
    r(s26) && (n26 = v5(n26, new Float32Array(n26.length), s26));
    if ("local" === this._context.graphicsCoreOwner.view.viewingMode || a35.reprojection === je.NONE) return a35.needsBufferCopy && e33.vertexAttributes.normal === n26 ? new Float32Array(n26) : n26;
    const i19 = e33.vertexAttributes.position, l34 = r(s26) ? n26 : new Float32Array(n26.length);
    return j6(n26, i19, o24, e33.spatialReference, l34);
  }
  _createTangentBuffer(e33, o24, a35) {
    let n26 = e33.vertexAttributes.tangent;
    if (t(n26)) return null;
    const s26 = a35.reprojection === je.ECEF ? a35.transformBeforeProject : null;
    r(s26) && (n26 = V(n26, new Float32Array(n26.length), s26));
    if ("local" === this._context.graphicsCoreOwner.view.viewingMode || a35.reprojection === je.NONE) return a35.needsBufferCopy && e33.vertexAttributes.normal === n26 ? new Float32Array(n26) : n26;
    const i19 = e33.vertexAttributes.position, l34 = r(s26) ? n26 : new Float32Array(n26.length);
    return k2(n26, i19, o24, e33.spatialReference, l34);
  }
  _createColorBuffer(e33) {
    return e33.vertexAttributes.color;
  }
  _createSymbolColorBuffer(e33) {
    if (this._requiresSymbolVertexColors()) {
      const t28 = this._getVertexOpacityAndColor(e33), r30 = n12(x(this.symbolLayer, "material", "colorMixMode")), o24 = new Uint8Array(4);
      return o7(t28, r30, o24), o24;
    }
    return null;
  }
  _createBuffers(e33, r30) {
    const o24 = e33.vertexAttributes && e33.vertexAttributes.position;
    if (!o24) return this.logger.warn("Mesh geometry must contain position vertex attributes"), null;
    const a35 = e33.vertexAttributes.normal, n26 = e33.vertexAttributes.uv, s26 = e33.vertexAttributes.tangent;
    if (r(a35) && a35.length !== o24.length) return this.logger.warn("Mesh normal vertex buffer must contain the same number of elements as the position buffer"), null;
    if (r(s26) && s26.length / 4 != o24.length / 3) return this.logger.warn("Mesh tangent vertex buffer must contain the same number of elements as the position buffer"), null;
    if (r(n26) && n26.length / 2 != o24.length / 3) return this.logger.warn("Mesh uv vertex buffer must contain the same number of elements as the position buffer"), null;
    const i19 = this._computeReprojectionInfo(e33), l34 = this._createPositionBuffer(e33, i19), c34 = this._createColorBuffer(e33), u32 = this._createSymbolColorBuffer(r30), h25 = this._createNormalBuffer(e33, l34, i19), f27 = this._createTangentBuffer(e33, l34, i19);
    return { positionBuffer: l34, normalBuffer: h25, tangentBuffer: f27, uvBuffer: n26, colorBuffer: c34, symbolColorBuffer: u32, objectTransformation: i19.reprojection === je.NONE && r(i19.objectTransformation) ? i19.objectTransformation : this._transformOriginLocal(e33, l34, h25, f27), geometryTransformation: i19.reprojection === je.NONE && r(i19.geometryTransformation) ? i19.geometryTransformation : e11() };
  }
  _computeReprojectionInfo(e33) {
    const r30 = r(e33.transform), o24 = r30 && e33.transform.geographic || this._context.renderCoordsHelper.viewingMode === l15.Local ? je.NONE : je.ECEF;
    if (r30) {
      if (o24 === je.NONE) {
        const t29 = e11();
        Zn(e33.spatialReference, e33.transform.origin, t29, this._context.renderCoordsHelper.spatialReference);
        return { reprojection: o24, objectTransformation: t29, geometryTransformation: r9(e33.transform.localMatrix), needsBufferCopy: false };
      }
      const t28 = q2(e11(), e33.transform.origin);
      return c5(t28, t28, e33.transform.localMatrix), { reprojection: o24, transformBeforeProject: t28, needsBufferCopy: true };
    }
    return { reprojection: o24, needsBufferCopy: true };
  }
  _transformOriginLocal(e33, r30, o24, a35) {
    const i19 = this._context.renderCoordsHelper.spatialReference, l34 = e33.anchor;
    Te[0] = l34.x, Te[1] = l34.y, Te[2] = l34.z;
    const c34 = e11();
    Zn(e33.spatialReference, Te, c34, i19);
    const h25 = T.fromTypedArray(r30);
    if (h6(Oe, c34), t7(h25, h25, Oe), r(o24) || r(a35)) {
      if (a9(we, c34), o6(we, we), r(o24)) {
        const e34 = i4.fromTypedArray(o24);
        r10(e34, e34, we);
      }
      if (r(a35)) {
        const e34 = i4.fromTypedArray(a35, 4 * a35.BYTES_PER_ELEMENT);
        r10(e34, e34, we);
      }
    }
    return c34;
  }
  _validateFaces(e33, t28) {
    const r30 = e33.vertexAttributes.position.length / 3, o24 = t28.faces;
    if (o24) {
      let e34 = -1;
      for (let t29 = 0; t29 < o24.length; t29++) {
        const r31 = o24[t29];
        r31 > e34 && (e34 = r31);
      }
      if (r30 <= e34) return this.logger.warn(`Vertex index ${e34} is out of bounds of the mesh position buffer`), false;
    } else if (r30 % 3 != 0) return this.logger.warn("Mesh position buffer length must be a multiple of 9 if no component faces are defined (3 values per vertex * 3 vertices per triangle)"), false;
    return true;
  }
  _getOrCreateFaces(e33, t28) {
    return t28.faces ? t28.faces : o5(e33.vertexAttributes.position.length / 3);
  }
  _isOutsideClippingArea(e33) {
    if (!this._context.clippingExtent) return false;
    const r30 = e33.vertexAttributes && e33.vertexAttributes.position;
    if (!r30) return false;
    const o24 = this._context.elevationProvider.spatialReference;
    let a35;
    const n26 = r30.length / 3;
    return r(o24) && !e33.spatialReference.equals(o24) ? (a35 = new Float64Array(r30.length), xn(e33.vertexAttributes.position, e33.spatialReference, 0, a35, o24, 0, n26)) : a35 = r30, S(Ae), M3(Ae, a35, 0, n26), !Y(Ae, this._context.clippingExtent);
  }
  _createGeometryInfo(e33, o24, a35) {
    if (!An(e33.spatialReference, this._context.graphicsCoreOwner.view.spatialReference)) return this.logger.warn("Geometry spatial reference is not compatible with the view"), null;
    if (this._isOutsideClippingArea(e33)) return null;
    const n26 = this._createBuffers(e33, o24);
    if (t(n26)) return null;
    const { positionBuffer: s26, uvBuffer: i19, colorBuffer: l34, symbolColorBuffer: c34, normalBuffer: u32, tangentBuffer: m18, objectTransformation: h25, geometryTransformation: f27 } = n26, p23 = this._getOrCreateComponents(e33), g23 = new Array();
    let d26 = false;
    for (const r30 of p23) {
      if (!this._validateFaces(e33, r30)) return null;
      const o25 = this._getOrCreateFaces(e33, r30);
      if (0 === o25.length) continue;
      const n27 = this._createComponentNormals(s26, u32, r30, o25);
      n27.didFlipNormals && (d26 = true);
      const h26 = [[O4.POSITION, new s7(s26, 3, true)], [O4.NORMAL, new s7(n27.normals, 3, true)]], p24 = [[O4.POSITION, o25], [O4.NORMAL, n27.indices]];
      r(l34) && (h26.push([O4.COLOR, new s7(l34, 4, true)]), p24.push([O4.COLOR, o25])), r(c34) && (h26.push([O4.SYMBOLCOLOR, new s7(c34, 4, true)]), p24.push([O4.SYMBOLCOLOR, new Array(o25.length).fill(0)])), r(i19) && (h26.push([O4.UV0, new s7(i19, 2, true)]), p24.push([O4.UV0, o25])), r(m18) && (h26.push([O4.TANGENT, new s7(m18, 4, true)]), p24.push([O4.TANGENT, o25]));
      const x18 = this._context.stage.renderView.getObjectAndLayerIdColor({ graphicUid: a35, layerUid: this._context.layer.uid }), _21 = this._getOrCreateMaterial(e33, r30), T10 = new v8(_21, h26, p24, null, e13.Mesh, x18);
      T10.transformation = f27, g23.push(T10);
    }
    return d26 && this.logger.warn("Normals have been automatically flipped to be consistent with the counter clock wise face winding order. It is better to generate mesh geometries that have consistent normals."), { geometries: g23, objectTransformation: h25 };
  }
};
var _e3 = class {
  constructor(e33, t28, r30) {
    this.normals = e33, this.indices = t28, this.didFlipNormals = r30;
  }
};
var Te = n5();
var ye2 = n5();
var be2 = n5();
var ve = n5();
var Ce = n5();
var Oe = e11();
var we = e10();
var Ae = a7();
var Me = [new g4()];
var je;
!function(e33) {
  e33[e33.NONE = 0] = "NONE", e33[e33.ECEF = 1] = "ECEF";
}(je || (je = {}));

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DLodInstanceGraphicLayer.js
var u25 = class {
  constructor(e33, t28, i19, n26) {
    this.graphics3DSymbolLayer = e33, this.instanceIndex = t28, this.elevationAligner = i19, this.elevationContext = n26, this.type = "lod-instance", this._highlights = /* @__PURE__ */ new Set(), this.alignedSampledElevation = 0, this.isElevationSource = false, this.needsElevationUpdates = false;
  }
  initialize() {
  }
  setVisibility(e33) {
    const t28 = this._lodRenderer.instanceData;
    e33 !== t28.getVisible(this.instanceIndex) && t28.setVisible(this.instanceIndex, e33);
  }
  destroy() {
    null != this.instanceIndex && (this._lodRenderer.instanceData.removeInstance(this.instanceIndex), this.graphics3DSymbolLayer.notifyDestroyGraphicLayer(this));
  }
  alignWithElevation(t28, i19, n26) {
    if (this.elevationAligner) {
      l20(this.elevationContext.featureExpressionInfoContext, n26);
      const s26 = (e33, n27) => f16(e33, t28, this.elevationContext, i19, n27), a35 = this.elevationAligner(this, this.elevationContext, t28.spatialReference, s26, i19);
      r(a35) && (this.alignedSampledElevation = a35);
    }
  }
  getCenterObjectSpace(e33 = n5()) {
    return this._lodRenderer.instanceData.getCombinedLocalTransform(this.instanceIndex, _13), O2(e33, this._lodRenderer.baseBoundingSphere.center, _13);
  }
  getBoundingBoxObjectSpace(e33 = a7()) {
    this._lodRenderer.instanceData.getCombinedLocalTransform(this.instanceIndex, _13);
    const t28 = this._lodRenderer.baseBoundingBox;
    S(e33);
    for (let s26 = 0; s26 < 8; ++s26) o2(x12, 0 == (1 & s26) ? t28[0] : t28[3], 0 == (2 & s26) ? t28[1] : t28[4], 0 == (4 & s26) ? t28[2] : t28[5]), O2(x12, x12, _13), c3(e33, x12);
    return e33;
  }
  computeAttachmentOrigin(e33) {
    this._lodRenderer.instanceData.getGlobalTransform(this.instanceIndex, _13), e33.render.origin[0] += _13[12], e33.render.origin[1] += _13[13], e33.render.origin[2] += _13[14], e33.render.num++;
  }
  async getProjectedBoundingBox(t28, n26, s26, r30, o24) {
    const d26 = this.getBoundingBoxObjectSpace(o24), g23 = I10, f27 = B(d26) ? 1 : g23.length;
    this._lodRenderer.instanceData.getGlobalTransform(this.instanceIndex, _13);
    for (let e33 = 0; e33 < f27; e33++) {
      const t29 = g23[e33];
      x12[0] = d26[t29[0]], x12[1] = d26[t29[1]], x12[2] = d26[t29[2]], O2(x12, x12, _13), v15[3 * e33 + 0] = x12[0], v15[3 * e33 + 1] = x12[1], v15[3 * e33 + 2] = x12[2];
    }
    if (!t28(v15, 0, f27)) return null;
    S(d26);
    let p23 = null;
    this.calculateRelativeScreenBounds && (p23 = this.calculateRelativeScreenBounds());
    for (let e33 = 0; e33 < 3 * f27; e33 += 3) {
      for (let t29 = 0; t29 < 3; t29++) d26[t29] = Math.min(d26[t29], v15[e33 + t29]), d26[t29 + 3] = Math.max(d26[t29 + 3], v15[e33 + t29]);
      p23 && s26.push({ location: v15.slice(e33, e33 + 3), screenSpaceBoundingRect: p23 });
    }
    if (n26 && (E3(d26, b18), "absolute-height" !== this.elevationContext.mode)) {
      let t29;
      const i19 = X(d26, n26.service.spatialReference, n26);
      try {
        t29 = await n26.service.queryElevation(b18[0], b18[1], r30, i19, "ground");
      } catch (u32) {
      }
      r(t29) && _2(d26, 0, 0, -this.alignedSampledElevation + t29);
    }
    return d26;
  }
  addObjectState(e33, t28) {
    if (e33 === t12.Highlight) {
      const i19 = new r14(e33);
      this._addHighlightId(i19), t28.addExternal((e34) => {
        this._removeHighlightId(e34);
      }, i19);
    }
  }
  removeObjectState(e33) {
    this._highlights.forEach((t28) => e33.remove(t28));
  }
  _addHighlightId(e33) {
    this._highlights.add(e33), this._lodRenderer.instanceData.setHighlight(this.instanceIndex, true);
  }
  _removeHighlightId(e33) {
    this._highlights.delete(e33), this._lodRenderer.instanceData.setHighlight(this.instanceIndex, this._highlights.size > 0);
  }
  get _lodRenderer() {
    return this.graphics3DSymbolLayer.lodRenderer;
  }
};
var v15 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
var x12 = n5();
var b18 = n5();
var I10 = [[0, 1, 2], [3, 1, 2], [0, 4, 2], [3, 4, 2], [0, 1, 5], [3, 1, 5], [0, 4, 5], [3, 4, 5]];
var _13 = e11();

// node_modules/@arcgis/core/views/3d/layers/graphics/lodResourceUtils.js
function r24(r30) {
  const s26 = new Array();
  return r30.stageResources.geometries.forEach((e33) => {
    const t28 = r30.stageResources.textures;
    s26.push(new t22(e33, t28));
  }), { components: s26, minScreenSpaceRadius: l(r30.lodThreshold, 0), pivotOffset: r30.pivotOffset };
}
function s20(e33) {
  return { levels: e33.map((e34) => r24(e34)) };
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/InstanceData.js
var I11;
function M9(t28) {
  let e33 = T2().mat4f64(O4.LOCALTRANSFORM).mat4f64(O4.GLOBALTRANSFORM).vec4f64(O4.BOUNDINGSPHERE).vec3f64(O4.MODELORIGIN).mat3f(O4.MODEL).mat3f(O4.MODELNORMAL).vec2f(O4.MODELSCALEFACTORS);
  return t28.includes("color") && (e33 = e33.vec4f(O4.COLOR)), t28.includes("featureAttribute") && (e33 = e33.vec4f(O4.FEATUREATTRIBUTE)), e33 = e33.u8(O4.STATE).u8(O4.LODLEVEL), t28.includes(O4.OBJECTANDLAYERIDCOLOR) && (e33 = e33.vec4u8(O4.OBJECTANDLAYERIDCOLOR)), e33.alignTo(8), e33;
}
!function(t28) {
  t28[t28.ALLOCATED = 1] = "ALLOCATED", t28[t28.DEFAULT_ACTIVE = 2] = "DEFAULT_ACTIVE", t28[t28.VISIBLE = 4] = "VISIBLE", t28[t28.HIGHLIGHT = 8] = "HIGHLIGHT", t28[t28.HIGHLIGHT_ACTIVE = 16] = "HIGHLIGHT_ACTIVE", t28[t28.REMOVE = 32] = "REMOVE", t28[t28.TRANSFORM_CHANGED = 64] = "TRANSFORM_CHANGED", t28[t28.ACTIVE = 18] = "ACTIVE";
}(I11 || (I11 = {}));
var b19 = class {
  constructor(t28) {
    this.localTransform = t28.getField(O4.LOCALTRANSFORM, b3), this.globalTransform = t28.getField(O4.GLOBALTRANSFORM, b3), this.modelOrigin = t28.getField(O4.MODELORIGIN, T), this.model = t28.getField(O4.MODEL, l10), this.modelNormal = t28.getField(O4.MODELNORMAL, l10), this.modelScaleFactors = t28.getField(O4.MODELSCALEFACTORS, u7), this.boundingSphere = t28.getField(O4.BOUNDINGSPHERE, h8), this.color = t28.getField(O4.COLOR, c6), this.featureAttribute = t28.getField(O4.FEATUREATTRIBUTE, c6), this.state = t28.getField(O4.STATE, d3), this.lodLevel = t28.getField(O4.LODLEVEL, d3), this.objectAndLayerIdColor = t28.getField(O4.OBJECTANDLAYERIDCOLOR, x4);
  }
};
var C7 = class extends v2 {
  constructor(t28, e33) {
    super(t28), this.events = new n4(), this._capacity = 0, this._size = 0, this._next = 0, this._buffer = null, this._view = null, this._layout = M9(e33);
  }
  get capacity() {
    return this._capacity;
  }
  get size() {
    return this._size;
  }
  get buffer() {
    return this._buffer.buffer;
  }
  get view() {
    return this._view;
  }
  addInstance() {
    this._size + 1 > this._capacity && this._grow();
    const t28 = this._findSlot();
    return this._view.state.set(t28, I11.ALLOCATED), this._size++, this.events.emit("instances-changed"), t28;
  }
  removeInstance(t28) {
    const e33 = this._view.state;
    s8(t28 >= 0 && t28 < this._capacity && 0 != (e33.get(t28) & I11.ALLOCATED), "invalid instance handle"), this._getStateFlag(t28, I11.ACTIVE) ? this._setStateFlags(t28, I11.REMOVE) : this.freeInstance(t28), this.events.emit("instances-changed");
  }
  freeInstance(t28) {
    const e33 = this._view.state;
    s8(t28 >= 0 && t28 < this._capacity && 0 != (e33.get(t28) & I11.ALLOCATED), "invalid instance handle"), e33.set(t28, 0), this._size--;
  }
  setLocalTransform(t28, e33, s26 = true) {
    this._view.localTransform.setMat(t28, e33), s26 && this.updateModelTransform(t28);
  }
  getLocalTransform(t28, e33) {
    this._view.localTransform.getMat(t28, e33);
  }
  setGlobalTransform(t28, e33, s26 = true) {
    this._view.globalTransform.setMat(t28, e33), s26 && this.updateModelTransform(t28);
  }
  getGlobalTransform(t28, e33) {
    this._view.globalTransform.getMat(t28, e33);
  }
  updateModelTransform(t28) {
    const e33 = this._view, s26 = D7, i19 = V8;
    e33.localTransform.getMat(t28, H3), e33.globalTransform.getMat(t28, G7);
    const r30 = c5(G7, G7, H3);
    o2(s26, r30[12], r30[13], r30[14]), e33.modelOrigin.setVec(t28, s26), a9(i19, r30), e33.model.setMat(t28, i19);
    const a35 = p8(D7, r30);
    a35.sort(), e33.modelScaleFactors.set(t28, 0, a35[1]), e33.modelScaleFactors.set(t28, 1, a35[2]), u8(i19, i19), o6(i19, i19), e33.modelNormal.setMat(t28, i19), this._setStateFlags(t28, I11.TRANSFORM_CHANGED), this.events.emit("instance-transform-changed", { index: t28 });
  }
  getModelTransform(t28, e33) {
    const s26 = this._view;
    s26.model.getMat(t28, V8), s26.modelOrigin.getVec(t28, D7), e33[0] = V8[0], e33[1] = V8[1], e33[2] = V8[2], e33[3] = 0, e33[4] = V8[3], e33[5] = V8[4], e33[6] = V8[5], e33[7] = 0, e33[8] = V8[6], e33[9] = V8[7], e33[10] = V8[8], e33[11] = 0, e33[12] = D7[0], e33[13] = D7[1], e33[14] = D7[2], e33[15] = 1;
  }
  applyShaderTransformation(t28, e33) {
    r(this.shaderTransformation) && this.shaderTransformation.applyTransform(this, t28, e33);
  }
  getCombinedModelTransform(t28, e33) {
    return this.getModelTransform(t28, e33), r(this.shaderTransformation) && this.shaderTransformation.applyTransform(this, t28, e33), e33;
  }
  getCombinedLocalTransform(t28, e33) {
    return this._view.localTransform.getMat(t28, e33), r(this.shaderTransformation) && this.shaderTransformation.applyTransform(this, t28, e33), e33;
  }
  getCombinedMaxScaleFactor(t28) {
    let e33 = this._view.modelScaleFactors.get(t28, 1);
    if (r(this.shaderTransformation)) {
      const s26 = this.shaderTransformation.scaleFactor(D7, this, t28);
      e33 *= Math.max(s26[0], s26[1], s26[2]);
    }
    return e33;
  }
  getCombinedMedianScaleFactor(t28) {
    let e33 = this._view.modelScaleFactors.get(t28, 0);
    if (r(this.shaderTransformation)) {
      const s26 = this.shaderTransformation.scaleFactor(D7, this, t28);
      e33 *= R9(s26[0], s26[1], s26[2]);
    }
    return e33;
  }
  getModel(t28, e33) {
    this._view.model.getMat(t28, e33);
  }
  setFeatureAttribute(t28, e33) {
    this._view.featureAttribute.setVec(t28, e33);
  }
  getFeatureAttribute(t28, e33) {
    this._view.featureAttribute.getVec(t28, e33);
  }
  setColor(t28, e33) {
    this._view.color.setVec(t28, e33);
  }
  setObjectAndLayerIdColor(t28, e33) {
    this._view.objectAndLayerIdColor.setVec(t28, e33);
  }
  getColor(t28, e33) {
    this._view.color.getVec(t28, e33);
  }
  setVisible(t28, e33) {
    e33 !== this.getVisible(t28) && (this._setStateFlag(t28, I11.VISIBLE, e33), this.events.emit("instance-visibility-changed", { index: t28 }));
  }
  getVisible(t28) {
    return this._getStateFlag(t28, I11.VISIBLE);
  }
  setHighlight(t28, e33) {
    e33 !== this.getHighlight(t28) && (this._setStateFlag(t28, I11.HIGHLIGHT, e33), this.events.emit("instance-highlight-changed"));
  }
  getHighlight(t28) {
    return this._getStateFlag(t28, I11.HIGHLIGHT);
  }
  getState(t28) {
    return this._view.state.get(t28);
  }
  getLodLevel(t28) {
    return this._view.lodLevel.get(t28);
  }
  countFlags(t28) {
    let e33 = 0;
    for (let s26 = 0; s26 < this._capacity; ++s26) {
      this.getState(s26) & t28 && ++e33;
    }
    return e33;
  }
  _setStateFlags(t28, e33) {
    const s26 = this._view.state;
    e33 = s26.get(t28) | e33, s26.set(t28, e33);
  }
  _clearStateFlags(t28, e33) {
    const s26 = this._view.state;
    e33 = s26.get(t28) & ~e33, s26.set(t28, e33);
  }
  _setStateFlag(t28, e33, s26) {
    s26 ? this._setStateFlags(t28, e33) : this._clearStateFlags(t28, e33);
  }
  _getStateFlag(t28, e33) {
    return !!(this._view.state.get(t28) & e33);
  }
  _grow() {
    const t28 = Math.max(w9, Math.floor(this._capacity * y9)), e33 = this._layout.createBuffer(t28);
    if (this._buffer) {
      const t29 = new Uint8Array(this._buffer.buffer);
      new Uint8Array(e33.buffer).set(t29);
    }
    this._capacity = t28, this._buffer = e33, this._view = new b19(this._buffer);
  }
  _findSlot() {
    const t28 = this._view.state;
    let e33 = this._next;
    for (; t28.get(e33) & I11.ALLOCATED; ) e33 = e33 + 1 === this._capacity ? 0 : e33 + 1;
    return this._next = e33 + 1 === this._capacity ? 0 : e33 + 1, e33;
  }
};
function R9(t28, e33, s26) {
  return Math.max(Math.min(t28, e33), Math.min(Math.max(t28, e33), s26));
}
e([y2({ constructOnly: true })], C7.prototype, "shaderTransformation", void 0), e([y2()], C7.prototype, "_size", void 0), e([y2({ readOnly: true })], C7.prototype, "size", null), C7 = e([a2("esri.views.3d.webgl-engine.lib.lodRendering.InstanceData")], C7);
var w9 = 1024;
var y9 = 2;
var D7 = n5();
var V8 = e10();
var H3 = e11();
var G7 = e11();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/InstanceOctree.js
var n21 = class extends V4 {
  constructor(e33, r30) {
    super((t28) => w6(this._instanceData.view.boundingSphere.getVec(t28, this._tmpSphere)), { maximumDepth: 25 }), this._tmpSphere = R5(), this._tmpMat4 = e11(), this._instanceData = e33, this._boundingSphere = r30;
  }
  addInstance(t28) {
    const s26 = this._instanceData.view.boundingSphere, i19 = this._instanceData.getCombinedModelTransform(t28, this._tmpMat4);
    O2(this._tmpSphere, this._boundingSphere.center, i19), this._tmpSphere[3] = this._boundingSphere.radius * l14(i19), s26.setVec(t28, this._tmpSphere), this.add([t28]);
  }
  removeInstance(t28) {
    this.remove([t28]);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/LevelSelector.js
var e26 = class {
  constructor(e33, i19) {
    this._worldSpaceRadius = e33, this._minScreenSpaceRadii = i19;
  }
  selectLevel(e33, i19, t28) {
    const c34 = t28.computeScreenPixelSizeAt(e33), r30 = this._worldSpaceRadius * i19 / c34;
    let s26 = 0;
    for (let a35 = 1; a35 < this._minScreenSpaceRadii.length; ++a35) r30 >= this._minScreenSpaceRadii[a35] && (s26 = a35);
    return s26;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/Intersector.js
var s21 = class extends i11 {
  constructor(t28, r30, e33, s26, i19, o24) {
    super(t28, r30), this.layerUid = t28, this.graphicUid = r30, this.geometryId = e33, this.triangleNr = s26, this.baseBoundingSphere = i19, this.numLodLevels = o24;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/LodComponentData.js
var u26 = class {
  constructor(s26, o24) {
    const i19 = s26.renderContext.rctx, n26 = o24.geometry;
    this._materialRepository = s26.materialRepository, n26.material.setParameters({ instancedDoublePrecision: true });
    const u32 = n26.material.createBufferWriter(), f27 = u32.vertexBufferLayout, d26 = u32.elementCount(n26), p23 = u32.allocate(d26);
    u32.write(null, null, n26, p23, 0), this.geometry = n26, this.material = n26.material, this.glMaterials = new e18(n26.material, this._materialRepository), this.vertexBufferLayout = f27, this.vbo = E6.createVertex(i19, F4.STATIC_DRAW, p23.buffer), this.vao = new r16(i19, E5, { geometry: o4(f27) }, { geometry: this.vbo }), this.vertexCount = d26;
  }
  destroy() {
    this.glMaterials.destroy(), this.vbo.dispose(), this.vao.dispose();
  }
  get boundingInfo() {
    return this.geometry.boundingInfo;
  }
  get triangleCount() {
    return this.vertexCount / 3;
  }
  intersect(t28, r30, e33, a35, m18, l34, u32, f27) {
    const d26 = this.geometry.id;
    this.material.intersect(this.geometry, t28.transform.transform, t28, e33, a35, (e34, a36, p23, c34, y14) => {
      if (e34 >= 0) {
        if (null != r30 && !r30(t28.rayBegin, t28.rayEnd, e34)) return;
        const c35 = new s21(l34.layerUid, l34.graphicUid(m18), d26, p23, u32, f27);
        if ((null == t28.results.min.drapedLayerOrder || y14 >= t28.results.min.drapedLayerOrder) && (null == t28.results.min.dist || e34 < t28.results.min.dist) && t28.results.min.set(i10.LOD, c35, e34, a36, t28.transform.transform, y14), t28.options.store !== t18.MIN && (null == t28.results.max.drapedLayerOrder || y14 >= t28.results.max.drapedLayerOrder) && (null == t28.results.max.dist || e34 > t28.results.max.dist) && t28.results.max.set(i10.LOD, c35, e34, a36, t28.transform.transform, y14), t28.options.store === t18.ALL) {
          const r31 = U3(t28.results.min.ray);
          r31.set(i10.LOD, c35, e34, a36, t28.transform.transform, y14), t28.results.all.push(r31);
        }
      }
    });
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/LodLevel.js
var h21 = class _h {
  static async create(n26, r30, i19) {
    const s26 = await Promise.allSettled(r30.components.map((o24) => n26.controller.schedule(() => new u26(n26, o24), i19))), c34 = s26.map((o24) => "fulfilled" === o24.status ? o24.value : null).filter(r);
    if (p3(i19) || c34.length !== s26.length) {
      c34.forEach((o24) => o24.destroy()), f(i19);
      for (const o24 of s26) if ("rejected" === o24.status) throw o24.reason;
    }
    return new _h(r30.minScreenSpaceRadius, c34);
  }
  constructor(o24, n26) {
    this.minScreenSpaceRadius = o24, this.components = n26;
  }
  destroy() {
    this.components.forEach((o24) => o24.destroy());
  }
  intersect(o24, n26, t28, e33, r30, i19, s26) {
    this.components.forEach((c34) => c34.intersect(o24, n26, t28, e33, r30, i19, this.boundingSphere, s26));
  }
  get boundingBox() {
    if (t(this._boundingBox)) {
      const n26 = S();
      this.components.forEach((t28) => {
        r(t28.boundingInfo) && (c3(n26, t28.boundingInfo.bbMin), c3(n26, t28.boundingInfo.bbMax));
      }), this._boundingBox = n26;
    }
    return this._boundingBox;
  }
  get boundingSphere() {
    if (t(this._boundingSphere)) {
      const o24 = this.boundingBox, n26 = n5();
      E3(o24, n26), this._boundingSphere = { center: n26, radius: 0.5 * y4(o24) };
    }
    return this._boundingSphere;
  }
  get triangleCount() {
    return this.components.reduce((o24, n26) => o24 + n26.triangleCount, 0);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/BackedBufferObject.js
var r25 = class {
  constructor(r30, i19, s26) {
    this._elementSize = i19, this._buffer = E6.createVertex(r30, F4.STATIC_DRAW), this.resize(s26);
  }
  destroy() {
    this._buffer.dispose();
  }
  get elementSize() {
    return this._elementSize;
  }
  get capacity() {
    return this._capacity;
  }
  get array() {
    return this._array;
  }
  get buffer() {
    return this._buffer;
  }
  get memoryUsage() {
    return { cpu: this._capacity * this._elementSize, gpu: this._capacity * this._elementSize };
  }
  copyRange(e33, t28, r30, i19 = 0) {
    const s26 = new Uint8Array(this.array, e33 * this.elementSize, (t28 - e33) * this.elementSize);
    new Uint8Array(r30.array, i19 * this.elementSize).set(s26);
  }
  transferAll() {
    this._buffer.setData(this._array);
  }
  transferRange(e33, t28) {
    const r30 = e33 * this._elementSize, i19 = t28 * this._elementSize;
    this._buffer.setSubData(new Uint8Array(this._array), r30, r30, i19);
  }
  resize(e33) {
    const t28 = e33 * this._elementSize, r30 = new ArrayBuffer(t28);
    this._array && (e33 >= this._capacity ? new Uint8Array(r30).set(new Uint8Array(this._array)) : new Uint8Array(r30).set(new Uint8Array(this._array).subarray(0, e33 * this._elementSize))), this._array = r30, this._buffer.setSize(t28), this._capacity = e33;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/RenderInstanceData.js
var n22 = class {
  constructor(a35) {
    this.modelOriginHi = a35.getField(O4.MODELORIGINHI, i4), this.modelOriginLo = a35.getField(O4.MODELORIGINLO, i4), this.model = a35.getField(O4.MODEL, l10), this.modelNormal = a35.getField(O4.MODELNORMAL, l10), this.color = a35.getField(O4.INSTANCECOLOR, c6), this.featureAttribute = a35.getField(O4.INSTANCEFEATUREATTRIBUTE, c6), this.objectAndLayerIdColor = a35.getField(O4.OBJECTANDLAYERIDCOLOR_INSTANCED, x4);
  }
};
var d19 = class {
  constructor(t28, i19) {
    this._headIndex = 0, this._tailIndex = 0, this._firstIndex = null, this._captureFirstIndex = true, this._updating = false, this._prevHeadIndex = 0, this._resized = false, this._rctx = t28, this._instanceBufferLayout = i19, this._elementSize = i19.stride, this._capacity = 1;
  }
  destroy() {
    this._buffer && this._buffer.destroy();
  }
  get buffer() {
    return this._buffer.buffer;
  }
  get view() {
    return this._view;
  }
  get capacity() {
    return this._capacity;
  }
  get size() {
    const t28 = this._headIndex, i19 = this._tailIndex;
    return t28 >= i19 ? t28 - i19 : t28 + this._capacity - i19;
  }
  get isEmpty() {
    return this._headIndex === this._tailIndex;
  }
  get isFull() {
    return this._tailIndex === (this._headIndex + 1) % this._capacity;
  }
  get headIndex() {
    return this._headIndex;
  }
  get tailIndex() {
    return this._tailIndex;
  }
  get firstIndex() {
    return this._firstIndex;
  }
  get memoryUsage() {
    return this._buffer ? this._buffer.memoryUsage : { cpu: 0, gpu: 0 };
  }
  reset() {
    this._headIndex = 0, this._tailIndex = 0, this._firstIndex = null;
  }
  startUpdateCycle() {
    this._captureFirstIndex = true;
  }
  beginUpdate() {
    s8(!this._updating, "already updating"), this._updating = true, this._prevHeadIndex = this._headIndex;
  }
  endUpdate() {
    s8(this._updating, "not updating"), this.size < f24 * this.capacity && this._shrink(), this._resized ? (this._buffer.transferAll(), this._resized = false) : this._transferRange(this._prevHeadIndex, this._headIndex), this._updating = false;
  }
  allocateHead() {
    s8(this._updating, "not updating"), this.isFull && this._grow();
    const t28 = this.headIndex;
    return this._captureFirstIndex && (this._firstIndex = t28, this._captureFirstIndex = false), this._incrementHead(), s8(this._headIndex !== this._tailIndex, "invalid pointers"), t28;
  }
  freeTail() {
    s8(this._updating, "not updating"), s8(this.size > 0, "invalid size");
    const t28 = this._tailIndex === this._firstIndex;
    this._incrementTail(), t28 && (this._firstIndex = this._tailIndex);
  }
  _grow() {
    const t28 = Math.max(_14, Math.floor(this._capacity * c29));
    this._resize(t28);
  }
  _shrink() {
    const t28 = Math.max(_14, Math.floor(this._capacity * u27));
    this._resize(t28);
  }
  _resize(t28) {
    if (s8(this._updating, "not updating"), t28 === this._capacity) return;
    const i19 = new r25(this._rctx, this._elementSize, t28);
    if (this._buffer) {
      this._firstIndex && (this._firstIndex = (this._firstIndex + this._capacity - this._tailIndex) % this._capacity);
      const t29 = this.size, e33 = this._compactInstances(i19);
      s8(e33 === t29, "invalid compaction"), this._buffer.destroy(), this._tailIndex = 0, this._headIndex = e33, this._prevHeadIndex = 0;
    }
    this._resized = true, this._capacity = t28, this._buffer = i19, this._view = new n22(this._instanceBufferLayout.createView(this._buffer.array));
  }
  _compactInstances(t28) {
    const i19 = this._headIndex, e33 = this._tailIndex;
    return e33 < i19 ? (this._buffer.copyRange(e33, i19, t28), i19 - e33) : e33 > i19 ? (this._buffer.copyRange(e33, this._capacity, t28), i19 > 0 && this._buffer.copyRange(0, i19, t28, this._capacity - e33), i19 + (this._capacity - e33)) : 0;
  }
  _incrementHead(t28 = 1) {
    this._headIndex = (this._headIndex + t28) % this._capacity;
  }
  _incrementTail(t28 = 1) {
    this._tailIndex = (this._tailIndex + t28) % this._capacity;
  }
  _transferRange(t28, i19) {
    t28 < i19 ? this._buffer.transferRange(t28, i19) : t28 > i19 && (i19 > 0 && this._buffer.transferRange(0, i19), this._buffer.transferRange(t28, this._capacity));
  }
};
var _14 = 1024;
var c29 = 2;
var f24 = 0.3;
var u27 = 0.5;

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/lodRendering/LodRenderer.js
var H4 = (e33) => {
  const t28 = e33.baseBoundingSphere.radius, r30 = e33.levels.map((e34) => e34.minScreenSpaceRadius);
  return new e26(t28, r30);
};
var q8 = class extends v2 {
  constructor(e33, t28) {
    super(e33), this.type = i10.LOD, this.isGround = false, this._levels = [], this._defaultRenderInstanceData = [], this._highlightRenderInstanceData = [], this._instanceIndex = 0, this._cycleStartIndex = 0, this._slicePlane = false, this._camera = new $2(), this._updateCyclesWithStaticCamera = -1, this._needFullCycle = false, this.slots = [E9.OPAQUE_MATERIAL, E9.TRANSPARENT_MATERIAL], this.canRender = true, this._instanceData = new C7({ shaderTransformation: e33.shaderTransformation }, e33.optionalFields), this._frameTask = t28.registerTask(R2.LOD_RENDERER, this);
  }
  initialize() {
    this._instanceBufferLayout = k6(this.optionalFields), this._glInstanceBufferLayout = o4(this._instanceBufferLayout, 1), this._instanceData.events.on("instances-changed", () => this._requestUpdateCycle()), this._instanceData.events.on("instance-transform-changed", ({ index: e33 }) => {
      this._requestUpdateCycle(), this.metadata.notifyGraphicGeometryChanged(e33);
    }), this._instanceData.events.on("instance-visibility-changed", ({ index: e33 }) => {
      this._requestUpdateCycle(true), this.metadata.notifyGraphicVisibilityChanged(e33);
    }), this._instanceData.events.on("instance-highlight-changed", () => this._requestUpdateCycle(true));
  }
  destroy() {
    this._frameTask.remove();
  }
  get _enableLevelSelection() {
    return this.symbol.levels.length > 1;
  }
  get levels() {
    return this._levels;
  }
  get baseBoundingBox() {
    return this._levels[this._levels.length - 1].boundingBox;
  }
  get baseBoundingSphere() {
    return this._levels[this._levels.length - 1].boundingSphere;
  }
  get baseMaterial() {
    return this._levels[this._levels.length - 1].components[0].material;
  }
  get slicePlaneEnabled() {
    return this._slicePlane;
  }
  set slicePlaneEnabled(e33) {
    this._slicePlane = e33;
  }
  get layerUid() {
    return this.metadata.layerUid;
  }
  get instanceData() {
    return this._instanceData;
  }
  get memoryUsage() {
    const e33 = { cpu: 0, gpu: 0 };
    return this._defaultRenderInstanceData.forEach((t28) => {
      const r30 = t28.memoryUsage;
      e33.cpu += r30.cpu, e33.gpu += r30.gpu;
    }), this._highlightRenderInstanceData.forEach((t28) => {
      const r30 = t28.memoryUsage;
      e33.cpu += r30.cpu, e33.gpu += r30.gpu;
    }), e33;
  }
  get renderStats() {
    const e33 = this._instanceData.size, t28 = [];
    return this._levels.forEach((e34, r30) => {
      const a35 = this._defaultRenderInstanceData[r30], s26 = this._highlightRenderInstanceData[r30], n26 = a35.size + s26.size, i19 = e34.triangleCount;
      t28.push({ renderedInstances: n26, renderedTriangles: n26 * i19, trianglesPerInstance: i19 });
    }), { totalInstances: e33, renderedInstances: t28.reduce((e34, t29) => e34 + t29.renderedInstances, 0), renderedTriangles: t28.reduce((e34, t29) => e34 + t29.renderedTriangles, 0), levels: t28 };
  }
  async initializeRenderContext(e33, t28) {
    this._context = e33;
    const a35 = e33.renderContext.rctx, s26 = await Promise.allSettled(this.symbol.levels.map((r30) => (this._defaultRenderInstanceData.push(new d19(a35, this._instanceBufferLayout)), this._highlightRenderInstanceData.push(new d19(a35, this._instanceBufferLayout)), h21.create(e33, r30, t28)))), o24 = s26.map((e34) => "fulfilled" === e34.status ? e34.value : null).filter(r);
    if (p3(t28) || o24.length !== s26.length) {
      o24.forEach((e34) => e34.destroy()), f(t28);
      for (const e34 of s26) if ("rejected" === e34.status) throw e34.reason;
    }
    this._levels = o24, this._levelSelector = H4(this);
  }
  uninitializeRenderContext() {
    this._invalidateOctree(), this._levels.forEach((e33) => e33.destroy()), this._defaultRenderInstanceData.forEach((e33) => e33.destroy()), this._highlightRenderInstanceData.forEach((e33) => e33.destroy());
  }
  get needsTransparentPass() {
    return this._levels.some((e33) => e33.components.some((e34) => e34.material.requiresSlot(E9.TRANSPARENT_MATERIAL, h10.Color)));
  }
  get needsHighlight() {
    return this._highlightRenderInstanceData.some((e33) => e33.size > 0);
  }
  prepareRender(e33) {
    if (!t17.LOD_INSTANCE_RENDERER_DISABLE_UPDATES) {
      if (this._enableLevelSelection) {
        const t28 = e33.bindParameters.contentCamera.equals(this._camera);
        this._camera.copyFrom(e33.bindParameters.contentCamera), t28 || this._requestUpdateCycle();
      }
      this._needFullCycle && (this.runTask(P3), this._needFullCycle = false);
    }
  }
  render(e33) {
    if (!this.baseMaterial.isVisible() || !this.baseMaterial.isVisibleForOutput(e33.output)) return;
    e33.rctx.bindVAO();
    e33.output !== h10.Highlight && e33.output !== h10.ShadowHighlight && this._renderComponents(e33, this._defaultRenderInstanceData);
    e33.output !== h10.ShadowExcludeHighlight && this._renderComponents(e33, this._highlightRenderInstanceData);
  }
  intersect(e33, t28, r30, s26) {
    if (!this.baseMaterial.isVisible() || t(this._octree)) return;
    const n26 = n5();
    e5(n26, s26, r30);
    const i19 = (a35) => {
      this._instanceData.getCombinedModelTransform(a35, K), e33.transform.set(K), O2(Y3, r30, e33.transform.inverse), O2(J2, s26, e33.transform.inverse);
      const n27 = this._instanceData.getState(a35), i20 = this._instanceData.getLodLevel(a35), o24 = this._levels.length;
      s8(0 != (n27 & I11.ACTIVE), "invalid instance state"), s8(i20 >= 0 && i20 < o24, "invaid lod level"), this._levels[i20].intersect(e33, t28, Y3, J2, a35, this.metadata, o24);
    };
    this.baseMaterial.parameters.verticalOffset ? this._octree.forEach(i19) : this._octree.forEachAlongRay(r30, n26, i19);
  }
  queryDepthRange(e33) {
    return this._queryDepthRangeOctree(e33);
  }
  notifyShaderTransformationChanged() {
    this._invalidateOctree(), this._requestUpdateCycle();
  }
  get _octree() {
    var _a;
    if (t(this._octreeCached)) {
      const e33 = this._instanceData, t28 = (_a = e33.view) == null ? void 0 : _a.state;
      if (!t28) return null;
      this._octreeCached = new n21(e33, this.baseBoundingSphere);
      for (let r30 = 0; r30 < e33.capacity; ++r30) t28.get(r30) & I11.ACTIVE && this._octreeCached.addInstance(r30);
    }
    return this._octreeCached;
  }
  _invalidateOctree() {
    this._octreeCached = a(this._octreeCached);
  }
  _queryDepthRangeOctree(e33) {
    if (t(this._octree)) return { near: 1 / 0, far: -1 / 0 };
    const t28 = e33.viewForward, r30 = this._octree.findClosest(t28, V4.DepthOrder.FRONT_TO_BACK, e33.frustum), s26 = this._octree.findClosest(t28, V4.DepthOrder.BACK_TO_FRONT, e33.frustum);
    if (null == r30 || null == s26) return { near: 1 / 0, far: -1 / 0 };
    const n26 = e33.eye, i19 = this._instanceData.view;
    i19.boundingSphere.getVec(r30, W2), e5(W2, W2, n26);
    const o24 = P(W2, t28) - W2[3];
    i19.boundingSphere.getVec(s26, W2), e5(W2, W2, n26);
    const c34 = P(W2, t28) + W2[3];
    return { near: Math.max(e33.near, o24), far: Math.min(e33.far, c34) };
  }
  _requestUpdateCycle(e33 = false) {
    this._updateCyclesWithStaticCamera = -1, this._cycleStartIndex = this._instanceIndex, e33 && (this._needFullCycle = true, this._context.requestRender());
  }
  _startUpdateCycle() {
    this._updateCyclesWithStaticCamera++, this._defaultRenderInstanceData.forEach((e33) => e33.startUpdateCycle()), this._highlightRenderInstanceData.forEach((e33) => e33.startUpdateCycle());
  }
  get running() {
    return this._instanceData.size > 0 && this._updateCyclesWithStaticCamera < 1;
  }
  runTask(e33) {
    const { _enableLevelSelection: t28, _camera: a35, _levelSelector: s26 } = this;
    this._defaultRenderInstanceData.forEach((e34) => e34.beginUpdate()), this._highlightRenderInstanceData.forEach((e34) => e34.beginUpdate());
    const n26 = this._instanceData, i19 = n26.view;
    let o24 = n26.size;
    const c34 = n26.capacity;
    let l34 = this._instanceIndex;
    for (let h25 = 0; h25 < o24 && !e33.done; ++h25) {
      l34 === this._cycleStartIndex && this._startUpdateCycle();
      const h26 = i19.state.get(l34);
      let d26 = 0;
      if (!(h26 & I11.ALLOCATED)) {
        l34 = l34 + 1 === c34 ? 0 : l34 + 1, o24++;
        continue;
      }
      const u32 = i19.lodLevel.get(l34);
      if (h26 & I11.DEFAULT_ACTIVE && this._defaultRenderInstanceData[u32].freeTail(), h26 & I11.HIGHLIGHT_ACTIVE && this._highlightRenderInstanceData[u32].freeTail(), h26 & I11.REMOVE) n26.freeInstance(l34);
      else if (h26 & I11.VISIBLE) {
        let e34 = 0;
        t28 && (i19.modelOrigin.getVec(l34, z5), e34 = s26.selectLevel(z5, n26.getCombinedMedianScaleFactor(l34), a35)), d26 = h26 & ~(I11.ACTIVE | I11.TRANSFORM_CHANGED), e34 >= 0 && (h26 & I11.HIGHLIGHT ? (P7(this._highlightRenderInstanceData[e34], i19, l34), d26 |= I11.HIGHLIGHT_ACTIVE) : (P7(this._defaultRenderInstanceData[e34], i19, l34), d26 |= I11.DEFAULT_ACTIVE)), i19.state.set(l34, d26), i19.lodLevel.set(l34, e34);
      } else d26 = h26 & ~(I11.ACTIVE | I11.TRANSFORM_CHANGED), i19.state.set(l34, d26);
      if (r(this._octreeCached)) {
        const e34 = !!(h26 & I11.ACTIVE), t29 = !!(d26 & I11.ACTIVE);
        !e34 && t29 ? this._octreeCached.addInstance(l34) : e34 && !t29 ? this._octreeCached.removeInstance(l34) : e34 && t29 && h26 & I11.TRANSFORM_CHANGED && (this._octreeCached.removeInstance(l34), this._octreeCached.addInstance(l34));
      }
      l34 = l34 + 1 === c34 ? 0 : l34 + 1, e33.madeProgress();
    }
    this._instanceIndex = l34, this._defaultRenderInstanceData.forEach((e34) => e34.endUpdate()), this._highlightRenderInstanceData.forEach((e34) => e34.endUpdate()), this._context.requestRender();
  }
  _renderComponents(e33, t28) {
    this.levels.forEach((r30, a35) => {
      const s26 = r30.components.map((r31) => this._beginComponent(e33, t28[a35], r31));
      r30.components.forEach((r31, n26) => this._renderComponent(e33, s26[n26], t28[a35], r31, a35));
    });
  }
  _beginComponent(e33, t28, a35) {
    const { bindParameters: s26, rctx: n26, output: i19 } = e33;
    if (0 === t28.size || !a35.material.requiresSlot(s26.slot, e33.output)) return null;
    const o24 = a35.glMaterials.load(n26, s26.slot, i19);
    return r(o24) ? o24.beginSlot(s26) : null;
  }
  _renderComponent(e33, t28, r30, s26, n26) {
    if (t(t28)) return;
    const { bindParameters: i19, rctx: o24 } = e33, c34 = o24.bindTechnique(t28, s26.material.parameters, i19);
    o24.bindVAO(s26.vao), t28.ensureAttributeLocations(s26.vao), c34.bindDraw(Z2, i19, s26.material.parameters), t17.LOD_INSTANCE_RENDERER_COLORIZE_BY_LEVEL && e33.output === h10.Color && (c34.setUniform4fv("externalColor", Q[Math.min(n26, Q.length - 1)]), c34.setUniform1i("colorMixMode", E7.replace));
    const l34 = o24.capabilities.instancing, h25 = r30.capacity, d26 = r30.headIndex, u32 = r30.tailIndex, m18 = r30.firstIndex, p23 = this._glInstanceBufferLayout, _21 = (e34, a35) => {
      R6(o24, E5, r30.buffer, p23, e34), l34.drawArraysInstanced(t28.primitiveType, 0, s26.vertexCount, a35 - e34), E4(o24, E5, r30.buffer, p23);
    };
    s26.material.parameters.transparent && null != m18 ? d26 > u32 ? (s8(m18 >= u32 && m18 <= d26, "invalid firstIndex"), _21(m18, d26), _21(u32, m18)) : d26 < u32 && (m18 <= d26 ? (s8(m18 >= 0 && m18 <= d26, "invalid firstIndex"), _21(m18, d26), _21(u32, h25), _21(0, m18)) : (s8(m18 >= u32 && m18 <= h25, "invalid firstIndex"), _21(m18, h25), _21(0, d26), _21(u32, m18))) : d26 > u32 ? _21(u32, d26) : d26 < u32 && (_21(0, d26), _21(u32, h25)), o24.bindVAO(null);
  }
};
function P7(e33, t28, r30) {
  const a35 = e33.allocateHead();
  G8(t28, r30, e33.view, a35);
}
function G8(e33, t28, r30, a35) {
  s9(e33.modelOrigin, t28, r30.modelOriginHi, r30.modelOriginLo, a35), r30.model.copyFrom(a35, e33.model, t28), r30.modelNormal.copyFrom(a35, e33.modelNormal, t28), e33.color && r30.color && r30.color.copyFrom(a35, e33.color, t28), e33.objectAndLayerIdColor && r30.objectAndLayerIdColor && r30.objectAndLayerIdColor.copyFrom(a35, e33.objectAndLayerIdColor, t28), e33.featureAttribute && r30.featureAttribute && r30.featureAttribute.copyFrom(a35, e33.featureAttribute, t28);
}
function k6(e33) {
  let t28 = T2().vec3f(O4.MODELORIGINHI).vec3f(O4.MODELORIGINLO).mat3f(O4.MODEL).mat3f(O4.MODELNORMAL);
  return r(e33) && e33.includes("color") && (t28 = t28.vec4f(O4.INSTANCECOLOR)), r(e33) && e33.includes("featureAttribute") && (t28 = t28.vec4f(O4.INSTANCEFEATUREATTRIBUTE)), r(e33) && e33.includes("objectAndLayerIdColor") && (t28 = t28.vec4u8(O4.OBJECTANDLAYERIDCOLOR_INSTANCED)), t28;
}
e([y2({ constructOnly: true })], q8.prototype, "symbol", void 0), e([y2({ constructOnly: true })], q8.prototype, "optionalFields", void 0), e([y2({ constructOnly: true })], q8.prototype, "metadata", void 0), e([y2({ constructOnly: true })], q8.prototype, "shaderTransformation", void 0), e([y2()], q8.prototype, "_instanceData", void 0), e([y2()], q8.prototype, "_cycleStartIndex", void 0), e([y2({ readOnly: true })], q8.prototype, "_enableLevelSelection", null), e([y2()], q8.prototype, "_updateCyclesWithStaticCamera", void 0), e([y2({ readOnly: true })], q8.prototype, "running", null), q8 = e([a2("esri.views.3d.webgl-engine.lib.lodRendering.LodRenderer")], q8);
var z5 = n5();
var W2 = n6();
var K = e11();
var Y3 = n5();
var J2 = n5();
var Q = [r6(1, 0, 1, 1), r6(0, 0, 1, 1), r6(0, 1, 0, 1), r6(1, 1, 0, 1), r6(1, 0, 0, 1)];
var Z2 = new E10();

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DObjectSymbolLayer.js
var me3 = class {
  constructor(e33, t28, s26, r30, i19, a35, o24, n26, c34, l34, h25, d26) {
    this.lodResources = e33, this.lodRenderer = t28, this.stageResources = s26, this.originalMaterialParameters = r30, this.resourceSize = i19, this.isEsriSymbolResource = a35, this.isWosr = o24, this.resourceBoundingBox = n26, this.symbolSize = c34, this.extentPadding = l34, this.physicalBasedRenderingEnabled = h25, this.pivotOffset = d26;
  }
};
var ue3 = class extends y7 {
  getCachedSize() {
    const [e33, s26, r30] = r(this._resources) ? this._resources.symbolSize : [1, 1, 1];
    return { width: e33, depth: s26, height: r30 };
  }
  constructor(e33, t28, s26, r30) {
    super(e33, t28, s26, r30), this._resources = null, this._optionalFields = new Array(), this._instanceIndexToGraphicUid = /* @__PURE__ */ new Map(), this._hasLoadedPBRTextures = false, this._disposeResourceHandles = new Array(), this.ensureDrapedStatus(false), this._hasLoadedPBRTextures = s26.physicalBasedRenderingEnabled;
  }
  async doLoad(e33) {
    if (!this._drivenProperties.size) {
      if (S5(this.symbolLayer)) throw new Error();
    }
    const t28 = this.symbolLayer;
    if (this._isPrimitive) {
      const s26 = t28.resource ? t28.resource.primitive : d7;
      this._resources = await this._createResourcesForPrimitive(s26, e33);
    } else this._resources = await this._createResourcesForUrl(t28.resource.href, e33);
    this.layerOpacityChanged(), this.slicePlaneEnabledChanged(), this.physicalBasedRenderingChanged(), this.complexity = this.computeComplexity();
  }
  get extentPadding() {
    return r(this._resources) ? this._resources.extentPadding : 0;
  }
  get _isPrimitive() {
    return !(this.symbolLayer.resource && this.symbolLayer.resource.href);
  }
  get lodRenderer() {
    return r(this._resources) ? this._resources.lodRenderer : null;
  }
  _setMaterialTransparencyParams(e33, t28 = x(this.symbolLayer, "material", "color")) {
    const r30 = this._getCombinedOpacity(t28), i19 = r30 < 1 || this.needsDrivenTransparentPass;
    return e33.transparent = i19, e33.opacity = r30, e33.cullFace = i19 ? n11.None : n11.Back, e33;
  }
  async _createResourcesForPrimitive(s26, i19) {
    if (!o18(s26)) throw new Error(`Unknown object symbol primitive: ${s26}`);
    const a35 = this.symbolLayer, o24 = a7(c8(s26)), c34 = e4(F3(o24)), l34 = e4(t10(c34, a35)), h25 = s3(l34), d26 = false, p23 = false, u32 = { usePBR: this._context.physicalBasedRenderingEnabled, isSchematic: true, ambient: l3, diffuse: l3, hasSlicePlane: this._context.slicePlaneEnabled, hasSliceHighlight: false, castShadows: this.symbolLayer.castShadows, offsetTransparentBackfaces: !this.symbolLayer.isPrimitive }, y14 = !!u32.usePBR;
    this._setMaterialTransparencyParams(u32);
    const f27 = this.symbol;
    if ("point-3d" === f27.type && f27.verticalOffset) {
      const { screenLength: e33, minWorldLength: s27, maxWorldLength: r30 } = f27.verticalOffset;
      u32.verticalOffset = { screenLength: u6(e33), minWorldLength: s27 || 0, maxWorldLength: r(r30) ? r30 : 1 / 0 }, u32.castShadows = false;
    }
    if (this._context.screenSizePerspectiveEnabled && (u32.screenSizePerspective = this._context.sharedResources.screenSizePerspectiveSettings), this._drivenProperties.color) u32.externalColor = _3;
    else {
      const s27 = r(a35.material) ? a35.material.color : null, r30 = r(s27) ? l11.toUnitRGBA(s27) : _3;
      u32.externalColor = r30;
    }
    this._fastUpdates = I9(this._context.renderer, this._fastVisualVariableConvertOptions(o24, l34, c34, n)), u32.instanced = ["transformation"], this._fastUpdates.enabled ? (Object.assign(u32, this._fastUpdates.materialParameters), u32.instanced.push("featureAttribute"), this._optionalFields.push("featureAttribute")) : this._hasPerInstanceColor() && (u32.instanced.push("color"), this._optionalFields.push("color")), has("enable-feature:objectAndLayerId-rendering") && (u32.instanced.push("objectAndLayerIdColor"), this._optionalFields.push("objectAndLayerIdColor"));
    const _21 = new E11(u32), b24 = u20(s26, _21);
    if (!b24) throw new Error(`Unknown object symbol primitive: ${s26}`);
    const x18 = e24(b24).map((e33) => ({ opacity: 1, transparent: e33.parameters.transparent })), R12 = await this._createStageResources(b24, y14, i19), v21 = await this._createLodRenderer(b24, i19);
    return new me3(b24, v21, R12, x18, c34, d26, p23, o24, l34, h25, y14, n);
  }
  async _createResourcesForUrl(e33, i19) {
    const a35 = ["transformation"], o24 = { materialParamsMixin: { instanced: a35, hasSlicePlane: this._context.slicePlaneEnabled, castShadows: this.symbolLayer.castShadows }, streamDataRequester: this._context.streamDataRequester, cache: this._context.sharedResources.objectResourceCache };
    this._fastUpdates = I9(this._context.renderer, this._fastVisualVariableConvertOptions(n, n, n, n)), this._fastUpdates.enabled ? (Object.assign(o24.materialParamsMixin, this._fastUpdates.materialParameters), a35.push("featureAttribute"), this._optionalFields.push("featureAttribute")) : this._hasPerInstanceColor() && (a35.push("color"), this._optionalFields.push("color")), has("enable-feature:objectAndLayerId-rendering") && (a35.push("objectAndLayerIdColor"), this._optionalFields.push("objectAndLayerIdColor"));
    const c34 = this.symbol;
    if ("point-3d" === c34.type && c34.verticalOffset) {
      const { screenLength: e34, minWorldLength: s26, maxWorldLength: r30 } = c34.verticalOffset;
      o24.materialParamsMixin.verticalOffset = { screenLength: u6(e34), minWorldLength: s26 || 0, maxWorldLength: r(r30) ? r30 : 1 / 0 }, o24.materialParamsMixin.castShadows = false;
    }
    o24.signal = i19, o24.usePBR = this._context.physicalBasedRenderingEnabled, o24.skipHighLods = this._context.skipHighSymbolLods;
    const l34 = o24.usePBR, h25 = await te(e33, o24), d26 = h25.isEsriSymbolResource, p23 = h25.isWosr, u32 = s20(h25.lods);
    u32.levels.sort((e34, t28) => e34.minScreenSpaceRadius - t28.minScreenSpaceRadius);
    const y14 = this._context, f27 = this.symbolLayer.material, _21 = this._getExternalColorParameters(f27), b24 = x(this.symbolLayer, "material", "color"), P10 = this._getCombinedOpacity(b24, { hasIntrinsicColor: true }), x18 = this.needsDrivenTransparentPass, R12 = e24(u32), v21 = e24(u32).map((e34) => ({ opacity: e34.parameters.opacity || 1, transparent: e34.parameters.transparent }));
    R12.forEach((e34) => {
      const t28 = e34.parameters;
      e34.setParameters(_21);
      const s26 = t28.opacity * P10, r30 = s26 < 1 || x18 || t28.transparent;
      e34.setParameters({ opacity: s26, transparent: r30 }), y14.screenSizePerspectiveEnabled && e34.setParameters({ screenSizePerspective: y14.sharedResources.screenSizePerspectiveSettings });
    });
    const L8 = h25.referenceBoundingBox, S19 = e4(F3(L8)), C11 = e4(u32.levels[0].pivotOffset), j12 = e4(t10(S19, this.symbolLayer)), w12 = s3(j12);
    P6(this._fastUpdates, this._context.renderer, this._fastVisualVariableConvertOptions(L8, j12, S19, C11)) && R12.forEach((e34) => e34.setParameters(this._fastUpdates.materialParameters));
    const E21 = await this._createStageResources(u32, l34, i19), T10 = await this._createLodRenderer(u32, i19);
    return new me3(u32, T10, E21, v21, S19, d26, p23, L8, j12, w12, l34, C11);
  }
  _addDisposeResource(e33) {
    this._disposeResourceHandles.push(e33);
  }
  async _createStageResources(e33, t28, s26) {
    const r30 = this._context.stage, i19 = e24(e33);
    t28 !== this._context.physicalBasedRenderingEnabled && this.physicalBasedRenderingChanged(), r30.addMany(i19), this._addDisposeResource(() => r30.removeMany(i19));
    const a35 = n17(e33);
    r30.addMany(a35), this._addDisposeResource(() => r30.removeMany(a35)), await r30.load(a35, s26), f(s26);
    const n26 = c22(e33);
    return r30.addMany(n26), this._addDisposeResource(() => r30.removeMany(n26)), { materials: i19, textures: a35, geometries: n26 };
  }
  async _createLodRenderer(e33, t28) {
    const s26 = this._context.stage, r30 = { layerUid: this._context.layer.uid, graphicUid: (e34) => this._instanceIndexToGraphicUid.get(e34), notifyGraphicGeometryChanged: (e34) => this._context.notifyGraphicGeometryChanged(this._instanceIndexToGraphicUid.get(e34)), notifyGraphicVisibilityChanged: (e34) => this._context.notifyGraphicVisibilityChanged(this._instanceIndexToGraphicUid.get(e34)) }, i19 = this._fastUpdates.enabled ? { applyTransform: (e34, t29, s27) => {
      e34.getFeatureAttribute(t29, be3), n7(s27, q6(this._fastUpdates.materialParameters, be3, s27));
    }, scaleFactor: (e34, t29, s27) => (t29.getFeatureAttribute(s27, be3), $5(e34, this._fastUpdates.materialParameters, be3)) } : null, a35 = new q8({ symbol: e33, optionalFields: this._optionalFields, metadata: r30, shaderTransformation: i19 }, this._context.scheduler);
    return a35.slicePlaneEnabled = this._context.slicePlaneEnabled, this._addDisposeResource(() => {
      s26.removeRenderPlugin(a35), a35.destroy();
    }), await s26.addRenderPlugin(a35.slots, a35, t28), a35;
  }
  _getExternalColorParameters(s26) {
    const r30 = {};
    return this._drivenProperties.color ? r30.externalColor = _3 : r(s26) && r(s26.color) ? r30.externalColor = l11.toUnitRGBA(s26.color) : (r30.externalColor = _3, r30.colorMixMode = "ignore"), r30;
  }
  destroy() {
    super.destroy(), this._cleanupResources();
  }
  _cleanupResources() {
    this._disposeResourceHandles.forEach((e33) => e33()), this._disposeResourceHandles.length = 0, this._resources = null;
  }
  createGraphics3DGraphic(e33) {
    const t28 = e33.graphic;
    if (!this._validateGeometry(t28.geometry)) return null;
    const s26 = u21(t28.geometry);
    if (t(s26)) return this.logger.warn(`unsupported geometry type for icon symbol: ${t28.geometry.type}`), null;
    const r30 = this.setGraphicElevationContext(t28, new h15()), a35 = e33.renderingInfo;
    return this._createAs3DShape(t28, s26, a35, r30, t28.uid, e33.layer.uid);
  }
  notifyDestroyGraphicLayer(e33) {
    this._instanceIndexToGraphicUid.delete(e33.instanceIndex);
  }
  graphicLayerToGraphicId() {
    return 0;
  }
  layerOpacityChanged() {
    if (t(this._resources)) return;
    const e33 = this._drivenProperties.opacity, t28 = !this._isPrimitive, r30 = this._resources.stageResources.materials, a35 = this._resources.originalMaterialParameters;
    for (let i19 = 0; i19 < r30.length; i19++) {
      const o24 = r30[i19], n26 = x(this.symbolLayer, "material", "color"), c34 = a35[i19], l34 = this._getCombinedOpacity(n26, { hasIntrinsicColor: t28 }) * c34.opacity, h25 = l34 < 1 || e33 || c34.transparent;
      o24.setParameters({ opacity: l34, transparent: h25 }), this._isPrimitive && o24.setParameters({ cullFace: h25 ? n11.None : n11.Back });
    }
  }
  layerElevationInfoChanged(e33, t28) {
    return this.updateGraphics3DGraphicElevationInfo(e33, t28, g7);
  }
  slicePlaneEnabledChanged() {
    if (t(this._resources)) return true;
    this._resources.lodRenderer.slicePlaneEnabled = this._context.slicePlaneEnabled;
    for (const e33 of this._resources.stageResources.materials) e33.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled });
    return true;
  }
  physicalBasedRenderingChanged() {
    if (t(this._resources)) return true;
    const { stageResources: e33, isWosr: t28 } = this._resources;
    for (const s26 of e33.materials) this._isPrimitive ? s26.setParameters({ usePBR: this._context.physicalBasedRenderingEnabled, isSchematic: true }) : t28 || s26.setParameters({ usePBR: this._context.physicalBasedRenderingEnabled, isSchematic: false });
    return false !== this._hasLoadedPBRTextures || true !== this._context.physicalBasedRenderingEnabled || (this._hasLoadedPBRTextures = true, false);
  }
  pixelRatioChanged() {
    return true;
  }
  skipHighSymbolLodsChanged() {
    return false;
  }
  applyRendererDiff(e33, t28) {
    if (t(this._resources)) return e22.Recreate_Symbol;
    const { stageResources: { materials: s26 }, lodRenderer: r30, resourceBoundingBox: a35, symbolSize: o24, resourceSize: n26, pivotOffset: c34 } = this._resources;
    for (const i19 in e33.diff) {
      if ("visualVariables" !== i19) return e22.Recreate_Symbol;
      if (!P6(this._fastUpdates, t28, this._fastVisualVariableConvertOptions(a35, o24, n26, c34))) return e22.Recreate_Symbol;
      for (const e34 of s26) e34.setParameters(this._fastUpdates.materialParameters);
      r30.notifyShaderTransformationChanged();
    }
    return e22.Fast_Update;
  }
  computeComplexity() {
    if (t(this._resources)) return super.computeComplexity();
    const e33 = this._resources.lodResources, t28 = s17(e33.levels[0]).reduce((e34, t29) => e34 + t29.indices.get(O4.POSITION).length, 0) / 3, s26 = (e34) => Array.from(e34.vertexAttributes.values()).reduce((e35, t29) => {
      var _a;
      return e35 + (((_a = t29.data.buffer) == null ? void 0 : _a.byteLength) ?? 0);
    }, 0) + Array.from(e34.indices.values()).reduce((e35, t29) => e35 + (Array.isArray(t29) ? 12 * t29.length : t29.buffer.byteLength), 0), r30 = n17(e33).reduce((e34, t29) => e34 + (t29.params.encoding && "image/ktx2" === t29.params.encoding ? t29.estimatedTexMemRequired : t29.estimatedTexMemRequired / 4), 0) + c22(e33).reduce((e34, t29) => e34 + s26(t29), 0);
    return { primitivesPerFeature: t28, primitivesPerCoordinate: 0, drawCallsPerFeature: 0, estimated: false, memory: { ...p17(this.symbol, this.symbolLayer), resourceBytes: r30 } };
  }
  _hasLodRenderer() {
    return r(this._resources);
  }
  _createAs3DShape(e33, s26, r30, a35, o24, n26) {
    if (!this._hasLodRenderer() || t(this._resources)) return null;
    const c34 = this.getFastUpdateAttrValues(e33), l34 = !this._fastUpdates.enabled && this._hasPerInstanceColor() ? B2(r30.color, r30.opacity) : null, h25 = this._context.clippingExtent;
    if (gn(s26, ye3, this._context.elevationProvider.spatialReference), r(h25) && !p5(h25, ye3)) return null;
    const d26 = this._requiresTerrainElevation(a35), p23 = this._computeGlobalTransform(s26, a35, _e4, ge2), m18 = this._computeLocalTransform(this._resources, this.symbolLayer, r30, fe2), u32 = this._resources.lodRenderer.instanceData, y14 = u32.addInstance();
    this._instanceIndexToGraphicUid.set(y14, o24), u32.setLocalTransform(y14, m18, false), u32.setGlobalTransform(y14, p23), c34 && u32.setFeatureAttribute(y14, c34), l34 && u32.setColor(y14, l34), r(this._context.stage.renderView.objectAndLayerIdRenderHelper) && u32.setObjectAndLayerIdColor(y14, this._context.stage.renderView.objectAndLayerIdRenderHelper.getObjectAndLayerIdColor({ graphicUid: o24, layerUid: n26 }));
    const f27 = new u25(this, y14, b13, a35);
    return d26 && (f27.alignedSampledElevation = ge2.sampledElevation), f27.needsElevationUpdates = g7(a35.mode), m12(f27, s26, this._context.elevationProvider), f27;
  }
  _computeGlobalTransform(e33, t28, s26, r30) {
    return f16(e33, this._context.elevationProvider, t28, this._context.renderCoordsHelper, r30), ye3[0] = e33.x, ye3[1] = e33.y, ye3[2] = r30.z, Zn(e33.spatialReference, ye3, s26, this._context.renderCoordsHelper.spatialReference), s26;
  }
  _computeLocalTransform(e33, t28, s26, r30) {
    return r7(r30), this._applyObjectRotation(s26, false, r30), this._applyObjectRotation(t28, true, r30), this._applyObjectScale(e33, s26, r30), this._applyAnchor(e33, t28, r30), r30;
  }
  _applyObjectScale(e33, t28, s26) {
    if (this._fastUpdates.enabled && this._fastUpdates.requiresShaderTransformation) return;
    const r30 = this._drivenProperties.size && t28.size ? t28.size : e33.symbolSize, i19 = I6(r30, e33.symbolSize, e33.resourceSize, this._context.renderCoordsHelper.unitInMeters);
    1 === i19[0] && 1 === i19[1] && 1 === i19[2] || f6(s26, s26, i19);
  }
  prepareSymbolLayerPatch(e33) {
    if ("partial" !== e33.diff.type) return;
    const t28 = e33.diff.diff;
    this._preparePatchTransform(e33, t28), this._preparePatchColor(e33, t28);
  }
  updateGeometry(e33, t28) {
    if (t(this._resources)) return true;
    const s26 = t28 && u21(t28);
    if (t(s26)) return false;
    const r30 = this.getGeometryElevationMode(t28);
    return e33.elevationContext.mode === r30 && (this._computeGlobalTransform(s26, e33.elevationContext, _e4, ge2), this._requiresTerrainElevation(e33.elevationContext) && (e33.alignedSampledElevation = ge2.sampledElevation), this._resources.lodRenderer.instanceData.setGlobalTransform(e33.instanceIndex, _e4, true), m12(e33, s26, this._context.elevationProvider), true);
  }
  _preparePatchTransform(e33, t28) {
    if (!(t28.heading || t28.tilt || t28.roll || t28.width || t28.height || t28.depth || t28.anchor || t28.anchorPosition)) return;
    if (t(this._resources)) return;
    const s26 = (e34, t29, s27) => l(null != e34 && "complete" === e34.type ? e34.newValue : t29, s27), r30 = s26(t28.heading, this.symbolLayer.heading, 0), o24 = s26(t28.tilt, this.symbolLayer.tilt, 0), n26 = s26(t28.roll, this.symbolLayer.roll, 0), c34 = s26(t28.width, this.symbolLayer.width, void 0), l34 = s26(t28.height, this.symbolLayer.height, void 0), h25 = s26(t28.depth, this.symbolLayer.depth, void 0), d26 = s26(t28.anchor, this.symbolLayer.anchor, void 0), p23 = s26(t28.anchorPosition, this.symbolLayer.anchorPosition, void 0);
    delete t28.heading, delete t28.tilt, delete t28.roll, delete t28.width, delete t28.height, delete t28.depth, delete t28.anchor, delete t28.anchorPosition;
    const m18 = { heading: r30, tilt: o24, roll: n26, anchor: d26, anchorPosition: p23 }, u32 = this._resources;
    this.loadStatus === e23.LOADED && e33.symbolLayerStatePatches.push(() => {
      u32.symbolSize = e4(t10(u32.resourceSize, { width: c34, height: l34, depth: h25, isPrimitive: this.symbolLayer.isPrimitive }));
    }), e33.graphics3DGraphicPatches.push((e34, t29) => {
      const s27 = this._computeLocalTransform(u32, m18, t29, fe2), r31 = e34.instanceIndex;
      u32.lodRenderer.instanceData.setLocalTransform(r31, s27, true);
    });
  }
  _preparePatchColor(s26, r30) {
    if (!r30.material || "partial" !== r30.material.type) return;
    const a35 = r30.material.diff;
    if (!a35.color || "complete" !== a35.color.type || null == a35.color.newValue || null == a35.color.oldValue) return;
    const o24 = a35.color.newValue, n26 = r(o24) ? l11.toUnitRGBA(o24) : _3;
    delete a35.color;
    const c34 = this._resources;
    t(c34) || s26.graphics3DGraphicPatches.push((e33) => {
      let t28;
      this._hasPerInstanceColor() ? (c34.lodRenderer.instanceData.setColor(e33.instanceIndex, n26), t28 = this._setMaterialTransparencyParams({}, o24)) : t28 = this._setMaterialTransparencyParams({ externalColor: n26 }, o24);
      for (const s27 of c34.stageResources.materials) s27.setParameters(t28);
    });
  }
  _requiresTerrainElevation(e33) {
    return "absolute-height" !== e33.mode;
  }
  _applyObjectRotation(e33, t28, s26) {
    if (!(this._fastUpdates.enabled && this._fastUpdates.requiresShaderTransformation && t28)) return G4(e33.heading, e33.tilt, e33.roll, s26);
  }
  _computeAnchor(e33, s26, r30) {
    const i19 = n5();
    switch (r30.anchor) {
      case "center":
        r4(i19, E3(e33)), j3(i19, i19);
        break;
      case "top": {
        const t28 = E3(e33);
        o2(i19, -t28[0], -t28[1], -e33[5]);
        break;
      }
      case "bottom": {
        const t28 = E3(e33);
        o2(i19, -t28[0], -t28[1], -e33[2]);
        break;
      }
      case "relative": {
        const t28 = E3(e33), s27 = F3(e33), a35 = r30.anchorPosition, o24 = a35 ? r2(a35.x, a35.y, a35.z) : f3;
        c2(i19, s27, o24), u3(i19, i19, t28), j3(i19, i19);
        break;
      }
      default:
        r(s26) ? j3(i19, s26) : r4(i19, f3);
    }
    return i19;
  }
  _applyAnchor(e33, t28, s26) {
    if (this._fastUpdates.enabled && this._fastUpdates.requiresShaderTransformation) return;
    const r30 = this._computeAnchor(e33.resourceBoundingBox, e33.pivotOffset, t28);
    r30 && i(s26, s26, r30);
  }
  _hasPerInstanceColor() {
    return this._drivenProperties.color || this._drivenProperties.opacity;
  }
  _fastVisualVariableConvertOptions(e33, s26, r30, i19) {
    const a35 = r(e33) ? e4(F3(e33)) : l3, o24 = r(e33) ? this._computeAnchor(e33, i19, this.symbolLayer) : f3, n26 = this._context.renderCoordsHelper.unitInMeters, c34 = I6(r(s26) ? s26 : void 0, s26, r30, n26), l34 = r2(this.symbolLayer.tilt || 0, this.symbolLayer.roll || 0, this.symbolLayer.heading || 0);
    return { modelSize: a35, symbolSize: r(s26) ? s26 : l3, unitInMeters: n26, transformation: { anchor: o24, scale: c34, rotation: l34 } };
  }
};
var ye3 = n5();
var fe2 = e11();
var _e4 = e11();
var be3 = n6();
var ge2 = new T4();

// node_modules/@arcgis/core/chunks/mat2.js
function n23(t28, n26) {
  return t28[0] = n26[0], t28[1] = n26[1], t28[2] = n26[2], t28[3] = n26[3], t28;
}
function a32(t28) {
  return t28[0] = 1, t28[1] = 0, t28[2] = 0, t28[3] = 1, t28;
}
function r26(t28, n26, a35, r30, s26) {
  return t28[0] = n26, t28[1] = a35, t28[2] = r30, t28[3] = s26, t28;
}
function s22(t28, n26) {
  if (t28 === n26) {
    const a35 = n26[1];
    t28[1] = n26[2], t28[2] = a35;
  } else t28[0] = n26[0], t28[1] = n26[2], t28[2] = n26[1], t28[3] = n26[3];
  return t28;
}
function u28(t28, n26) {
  const a35 = n26[0], r30 = n26[1], s26 = n26[2], u32 = n26[3];
  let o24 = a35 * u32 - s26 * r30;
  return o24 ? (o24 = 1 / o24, t28[0] = u32 * o24, t28[1] = -r30 * o24, t28[2] = -s26 * o24, t28[3] = a35 * o24, t28) : null;
}
function o21(t28, n26) {
  const a35 = n26[0];
  return t28[0] = n26[3], t28[1] = -n26[1], t28[2] = -n26[2], t28[3] = a35, t28;
}
function e27(t28) {
  return t28[0] * t28[3] - t28[2] * t28[1];
}
function c30(t28, n26, a35) {
  const r30 = n26[0], s26 = n26[1], u32 = n26[2], o24 = n26[3], e33 = a35[0], c34 = a35[1], i19 = a35[2], f27 = a35[3];
  return t28[0] = r30 * e33 + u32 * c34, t28[1] = s26 * e33 + o24 * c34, t28[2] = r30 * i19 + u32 * f27, t28[3] = s26 * i19 + o24 * f27, t28;
}
function i15(t28, n26, a35) {
  const r30 = n26[0], s26 = n26[1], u32 = n26[2], o24 = n26[3], e33 = Math.sin(a35), c34 = Math.cos(a35);
  return t28[0] = r30 * c34 + u32 * e33, t28[1] = s26 * c34 + o24 * e33, t28[2] = r30 * -e33 + u32 * c34, t28[3] = s26 * -e33 + o24 * c34, t28;
}
function f25(t28, n26, a35) {
  const r30 = n26[0], s26 = n26[1], u32 = n26[2], o24 = n26[3], e33 = a35[0], c34 = a35[1];
  return t28[0] = r30 * e33, t28[1] = s26 * e33, t28[2] = u32 * c34, t28[3] = o24 * c34, t28;
}
function l28(t28, n26) {
  const a35 = Math.sin(n26), r30 = Math.cos(n26);
  return t28[0] = r30, t28[1] = a35, t28[2] = -a35, t28[3] = r30, t28;
}
function h22(t28, n26) {
  return t28[0] = n26[0], t28[1] = 0, t28[2] = 0, t28[3] = n26[1], t28;
}
function M10(t28) {
  return "mat2(" + t28[0] + ", " + t28[1] + ", " + t28[2] + ", " + t28[3] + ")";
}
function b20(t28) {
  return Math.sqrt(t28[0] ** 2 + t28[1] ** 2 + t28[2] ** 2 + t28[3] ** 2);
}
function m14(t28, n26, a35, r30) {
  return t28[2] = r30[2] / r30[0], a35[0] = r30[0], a35[1] = r30[1], a35[3] = r30[3] - t28[2] * a35[1], [t28, n26, a35];
}
function d20(t28, n26, a35) {
  return t28[0] = n26[0] + a35[0], t28[1] = n26[1] + a35[1], t28[2] = n26[2] + a35[2], t28[3] = n26[3] + a35[3], t28;
}
function p21(t28, n26, a35) {
  return t28[0] = n26[0] - a35[0], t28[1] = n26[1] - a35[1], t28[2] = n26[2] - a35[2], t28[3] = n26[3] - a35[3], t28;
}
function y10(t28, n26) {
  return t28[0] === n26[0] && t28[1] === n26[1] && t28[2] === n26[2] && t28[3] === n26[3];
}
function x13(n26, a35) {
  const r30 = n26[0], s26 = n26[1], u32 = n26[2], o24 = n26[3], e33 = a35[0], c34 = a35[1], i19 = a35[2], f27 = a35[3], l34 = a3();
  return Math.abs(r30 - e33) <= l34 * Math.max(1, Math.abs(r30), Math.abs(e33)) && Math.abs(s26 - c34) <= l34 * Math.max(1, Math.abs(s26), Math.abs(c34)) && Math.abs(u32 - i19) <= l34 * Math.max(1, Math.abs(u32), Math.abs(i19)) && Math.abs(o24 - f27) <= l34 * Math.max(1, Math.abs(o24), Math.abs(f27));
}
function g16(t28, n26, a35) {
  return t28[0] = n26[0] * a35, t28[1] = n26[1] * a35, t28[2] = n26[2] * a35, t28[3] = n26[3] * a35, t28;
}
function j10(t28, n26, a35, r30) {
  return t28[0] = n26[0] + a35[0] * r30, t28[1] = n26[1] + a35[1] * r30, t28[2] = n26[2] + a35[2] * r30, t28[3] = n26[3] + a35[3] * r30, t28;
}
var S14 = c30;
var q9 = p21;
var _15 = Object.freeze(Object.defineProperty({ __proto__: null, LDU: m14, add: d20, adjoint: o21, copy: n23, determinant: e27, equals: x13, exactEquals: y10, frob: b20, fromRotation: l28, fromScaling: h22, identity: a32, invert: u28, mul: S14, multiply: c30, multiplyScalar: g16, multiplyScalarAndAdd: j10, rotate: i15, scale: f25, set: r26, str: M10, sub: q9, subtract: p21, transpose: s22 }, Symbol.toStringTag, { value: "Module" }));

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/PathGeometry.js
var r27 = class extends v8 {
  constructor(e33, r30, n26, o24, i19, s26, c34, h25) {
    super(e33, r30, n26, null, e13.Mesh, h25), this.path = o24, this.geometrySR = i19, this.upVectorAlignment = s26, this.stencilWidth = c34;
  }
};
var n24;
function o22(t28) {
  return "upVectorAlignment" in t28;
}
!function(t28) {
  t28[t28.World = 0] = "World", t28[t28.Path = 1] = "Path";
}(n24 || (n24 = {}));

// node_modules/@arcgis/core/chunks/mat2f64.js
function e28() {
  return [1, 0, 0, 1];
}
function r28(e33) {
  return [e33[0], e33[1], e33[2], e33[3]];
}
function t24(e33, r30, t28, n26) {
  return [e33, r30, t28, n26];
}
function n25(e33, r30) {
  return new Float64Array(e33, r30, 4);
}
var o23 = Object.freeze(Object.defineProperty({ __proto__: null, clone: r28, create: e28, createView: n25, fromValues: t24 }, Symbol.toStringTag, { value: "Module" }));

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/pathGeometryUtils.js
function M11() {
  return { up: n5(), right: n5() };
}
function k7(t28, e33, i19) {
  O2(t28.up, e33.up, i19), O2(t28.right, e33.right, i19);
}
function B5(t28, e33, i19) {
  r8(t28, P(i19, e33.right), P(i19, e33.up));
}
var G9 = class {
  constructor() {
    this.pos = n5(), this.posES = n5(), this.vLeft = n5(), this.vRight = n5(), this.vMinSiblingLength = 0, this.frame = M11(), this.rotationFrameUp = n5(), this.rotationRight = n13(), this.rotationAngle = 0, this.miterStretch = e28(), this.maxStretchDistance = 0;
  }
  setFrameFromUpVector(t28) {
    r4(this.frame.up, t28), u3(at, this.vLeft, this.vRight), z(at, at), g2(nt, this.frame.up, P(at, this.frame.up)), e5(pt, at, nt), z(pt, pt), _(this.frame.right, pt, this.frame.up);
  }
};
var j11 = class {
  constructor() {
    this.vertices = [], this.vertexIndices = [], this.vertexNormals = [], this.poles = [], this.poleIndices = [];
  }
  addVertex(t28, e33) {
    return this.vertices.push(t14(t28)), this.vertexNormals.push(t14(e33)), this.vertices.length - 1;
  }
  addPole(t28, e33 = null) {
    return this.poles.push({ position: t14(t28), normal: e33 ? t14(e33) : null }), this.poles.length - 1;
  }
  addSegment(t28, e33 = null) {
    this.vertexIndices.push(t28.v0), this.vertexIndices.push(t28.v1), e33 && (this.poleIndices.push(e33.v0), this.poleIndices.push(e33.v1));
  }
  get numSegments() {
    return this.vertexIndices.length / 2;
  }
  translate(t28, e33) {
    for (const i19 of this.vertices) i19[0] += t28, i19[1] += e33;
    for (const i19 of this.poles) i19.position[0] += t28, i19.position[1] += e33;
  }
};
function F7(t28 = 20) {
  const e33 = 0.5, i19 = new j11(), s26 = { v0: 0, v1: 0 };
  i19.addPole(r13(0, 0));
  for (let o24 = 0; o24 < t28; ++o24) {
    const s27 = 2 * o24 * Math.PI / t28, r31 = Math.cos(s27), h25 = Math.sin(s27), n26 = r13(r31 * e33, h25 * e33), a35 = r13(r31, h25);
    i19.addVertex(n26, a35);
  }
  for (let o24 = 0; o24 < t28 - 1; ++o24) {
    const t29 = { v0: o24, v1: o24 + 1 };
    i19.addSegment(t29, s26);
  }
  const r30 = { v0: t28 - 1, v1: 0 };
  return i19.addSegment(r30, s26), i19;
}
function z6() {
  const t28 = 1, e33 = 1, i19 = new j11(), s26 = r13(0.5 * -t28, 0.5 * -e33), r30 = r13(0.5 * t28, 0.5 * -e33), o24 = r13(0.5 * t28, 0.5 * e33), h25 = r13(0.5 * -t28, 0.5 * e33), n26 = r13(0, -1), a35 = r13(1, 0), l34 = r13(0, 1), u32 = r13(-1, 0);
  return i19.addPole(r13(0, 0.5 * e33), l34), i19.addPole(r13(0, 0.5 * e33)), i19.addPole(r13(0, 0.5 * -e33)), i19.addPole(r13(0, 0.5 * -e33), n26), i19.addVertex(s26, n26), i19.addVertex(r30, n26), i19.addSegment({ v0: 0, v1: 1 }, { v0: 3, v1: 3 }), i19.addVertex(r30, a35), i19.addVertex(o24, a35), i19.addSegment({ v0: 2, v1: 3 }, { v0: 2, v1: 1 }), i19.addVertex(o24, l34), i19.addVertex(h25, l34), i19.addSegment({ v0: 4, v1: 5 }, { v0: 0, v1: 0 }), i19.addVertex(h25, u32), i19.addVertex(s26, u32), i19.addSegment({ v0: 6, v1: 7 }, { v0: 1, v1: 2 }), i19;
}
var J3 = class {
  constructor(t28) {
    this.vertices = t28, this.offset = n5(), this.xform = e11();
    const i19 = Math.floor((t28.length - 1) / 2);
    r4(this.offset, this.vertices[i19].pos);
    for (const e33 of this.vertices) e5(e33.pos, e33.pos, this.offset);
    i(this.xform, this.xform, this.offset), this.updatePathVertexInformation();
  }
  updatePathVertexInformation() {
    const t28 = this.vertices.length, e33 = this.vertices[0];
    e33.index = 0, e33.vLeft = n5(), e5(e33.vRight, this.vertices[1].pos, e33.pos);
    let i19 = s3(e33.vRight);
    e33.vMinSiblingLength = i19, z(e33.vRight, e33.vRight);
    let s26 = e33;
    for (let r30 = 1; r30 < t28; ++r30) {
      const e34 = this.vertices[r30];
      if (e34.index = r30, e34.vLeft = s26.vRight, r30 < t28 - 1) {
        e5(e34.vRight, this.vertices[r30 + 1].pos, e34.pos);
        const t29 = s3(e34.vRight);
        e34.vMinSiblingLength = Math.min(i19, t29), i19 = t29, z(e34.vRight, e34.vRight);
      } else r4(e34.vRight, e34.vLeft), e34.vMinSiblingLength = i19;
      s26 = e34;
    }
  }
};
function q10(t28, e33) {
  let i19 = null;
  const s26 = t28.vertices.length, r30 = 0.99619469809, o24 = n5(), h25 = n5(), n26 = n5(), a35 = n5(), l34 = n5(), u32 = n5(), p23 = p9();
  let f27 = t28.vertices[0];
  r4(h25, e33), o2(o24, 0, 1, 0), wt(f27.vRight, h25, o24, o24, n26, h25, r30), r4(f27.frame.up, h25), r4(f27.frame.right, n26), i19 = f27;
  for (let x18 = 1; x18 < s26; ++x18) {
    f27 = t28.vertices[x18], u3(l34, f27.vLeft, f27.vRight);
    let e34 = s3(l34);
    e34 > 0 ? (e34 = 1 / Math.sqrt(e34), l34[0] = l34[0] * e34, l34[1] = l34[1] * e34, l34[2] = l34[2] * e34) : (l34[0] = f27.vRight[0], l34[1] = f27.vRight[1], l34[2] = f27.vRight[2]), u3(u32, i19.pos, i19.frame.up), _5(f27.pos, l34, p23);
    x6(p23, p7(u32, f27.vLeft), a35) ? (e5(a35, a35, f27.pos), z(h25, a35), _(n26, l34, h25), z(n26, n26)) : wt(l34, i19.frame.up, i19.frame.right, o24, n26, h25, r30), r4(f27.frame.up, h25), r4(f27.frame.right, n26), i19 = f27;
  }
}
var X2 = class {
  numProfilesPerJoin() {
    return 1;
  }
  extrude(t28, e33, i19) {
    for (let s26 = 0; s26 < e33.vertices.length; ++s26) i19(t28.index, t28.frame, e33.vertices[s26], e33.vertexNormals[s26], false);
  }
};
var K2 = class {
  constructor(t28 = 0.8 * Math.PI, e33 = 1) {
    this.cutoffAngle = t28, this.numBendSubdivisions = e33;
  }
  numProfilesPerJoin() {
    return this.numBendSubdivisions + 1;
  }
  extrude(t28, e33, s26) {
    const r30 = ft;
    if (Math.abs(t28.rotationAngle) >= this.cutoffAngle) for (let n26 = 0; n26 < this.numBendSubdivisions + 1; ++n26) {
      p6(xt, 0.5 * -t28.rotationAngle + n26 * t28.rotationAngle / this.numBendSubdivisions, t28.rotationFrameUp), k7(r30, t28.frame, xt);
      for (let i19 = 0; i19 < e33.vertices.length; ++i19) {
        j5(e33.vertices[i19], t28.rotationRight) * t28.rotationAngle >= 0 ? s26(t28.index, r30, e33.vertices[i19], e33.vertexNormals[i19], false) : (_4(rt, e33.vertices[i19], t28.miterStretch), s26(t28.index, t28.frame, rt, e33.vertexNormals[i19], true));
      }
    }
    else for (let i19 = 0; i19 < this.numBendSubdivisions + 1; ++i19) for (let r31 = 0; r31 < e33.vertices.length; ++r31) {
      const i20 = j5(e33.vertices[r31], t28.rotationRight) * t28.rotationAngle >= 0;
      _4(rt, e33.vertices[r31], t28.miterStretch), s26(t28.index, t28.frame, rt, e33.vertexNormals[r31], !i20);
    }
  }
};
var Q2 = class {
  rebuildConnectingProfileGeometry(t28, e33, i19) {
    for (let s26 = 0; s26 < e33.vertices.length; ++s26) i19(t28.index, t28.frame, e33.vertices[s26], e33.vertexNormals[s26], 0, 0);
  }
};
var W3 = class extends Q2 {
  constructor() {
    super();
  }
  getNumVertices() {
    return 0;
  }
  getNumIndices() {
    return 0;
  }
  rebuildCapGeometry() {
  }
  buildTopology() {
  }
};
var Y4 = class extends Q2 {
  constructor(t28, e33 = 0, i19 = false) {
    super(), this.profile = t28, this.profilePlaneOffset = e33, this.flip = i19;
  }
  getNumVertices() {
    return this.profile.vertices.length;
  }
  getNumIndices() {
    return 3 * this.profile.numSegments;
  }
  rebuildConnectingProfileGeometry(t28, e33, i19) {
    for (let s26 = 0; s26 < e33.vertices.length; ++s26) i19(t28.index, t28.frame, e33.vertices[s26], e33.vertexNormals[s26], this.profilePlaneOffset, 0);
  }
  rebuildCapGeometry(t28, e33) {
    const i19 = ot3;
    r8(i19, 0, 0);
    const s26 = this.flip ? 1 : -1;
    for (let r30 = 0; r30 < this.profile.vertices.length; ++r30) e33(t28.index, t28.frame, this.profile.vertices[r30], i19, this.profilePlaneOffset, s26);
  }
  buildTopology(t28, e33) {
    const i19 = this.vertexBufferStart + this.profile.vertexIndices[0];
    for (let s26 = 1; s26 < this.profile.numSegments; ++s26) {
      const t29 = this.profile.vertexIndices[2 * s26 + 0], r30 = this.profile.vertexIndices[2 * s26 + 1], o24 = this.vertexBufferStart + t29, h25 = this.vertexBufferStart + r30;
      this.flip ? e33(h25, o24, i19) : e33(i19, o24, h25);
    }
  }
};
var Z3 = class extends Q2 {
  constructor(t28) {
    super(), this.flip = false, this.sign = 0, this.breakNormals = false, this.numSegments = 3, this.profile = t28.profile, this.flip = t28.flip, this.sign = this.flip ? 1 : -1, this.breakNormals = t28.breakNormals, this.numSegments = t28.subdivisions;
  }
  getNumVertices() {
    let t28 = 0;
    return t28 = this.profile.vertices.length * (this.numSegments - 1), this.breakNormals && (t28 += this.profile.vertices.length), t28 += this.profile.poles.length, t28;
  }
  getNumIndices() {
    let t28 = 0;
    t28 += 2 * this.profile.numSegments * (this.numSegments - 1);
    for (let e33 = 0; e33 < this.profile.numSegments; ++e33) {
      const i19 = this.profile.vertexIndices[2 * e33 + 0], s26 = this.profile.vertexIndices[2 * e33 + 1];
      this.profile.poleIndices[i19] === this.profile.poleIndices[s26] ? t28 += 1 : t28 += 2;
    }
    return 3 * t28;
  }
  rebuildCapGeometry(t28, e33) {
    const i19 = t28.frame, s26 = 0.5 * this.sign, o24 = rt, h25 = ot3;
    r8(h25, 0, 0);
    for (let r30 = 0; r30 < this.profile.poles.length; ++r30) {
      const o25 = this.profile.poles[r30];
      o25.normal ? e33(t28.index, i19, o25.position, o25.normal, s26, 0) : e33(t28.index, i19, o25.position, h25, s26, this.sign);
    }
    if (this.breakNormals) for (let r30 = 0; r30 < this.profile.vertices.length; ++r30) e33(t28.index, i19, this.profile.vertices[r30], this.profile.vertexNormals[r30], 0, 0);
    for (let r30 = 0; r30 < this.numSegments - 1; ++r30) {
      const p23 = (1 - (r30 + 1) / this.numSegments) * Math.PI * 0.5, f27 = Math.sin(p23), x18 = Math.cos(p23);
      for (let r31 = 0; r31 < this.profile.vertices.length; ++r31) {
        const p24 = this.profile.poles[this.profile.poleIndices[r31]];
        o3(o24, this.profile.vertices[r31], p24.position), l9(o24, o24, f27), p24.normal ? (s6(o24, o24, p24.position), e33(t28.index, i19, o24, p24.normal, s26 * x18, 0)) : (v4(h25, o24), l9(h25, h25, f27), s6(o24, o24, p24.position), e33(t28.index, i19, o24, h25, s26 * x18, this.sign * x18));
      }
    }
  }
  buildTopology(t28, e33) {
    const i19 = this.breakNormals ? this.vertexBufferStart + this.profile.poles.length : this.firstProfileVertexIndex, s26 = this.breakNormals ? this.vertexBufferStart + this.profile.poles.length + this.profile.vertices.length : this.vertexBufferStart + this.profile.poles.length;
    for (let r30 = 0; r30 < this.profile.numSegments; ++r30) {
      const t29 = this.profile.vertexIndices[2 * r30 + 0], o24 = this.profile.vertexIndices[2 * r30 + 1], h25 = this.vertexBufferStart + this.profile.poleIndices[t29], n26 = this.vertexBufferStart + this.profile.poleIndices[o24];
      let a35 = i19 + t29, l34 = i19 + o24;
      for (let i20 = 0; i20 < this.numSegments - 1; ++i20) {
        const r31 = s26 + i20 * this.profile.vertices.length + t29, h26 = s26 + i20 * this.profile.vertices.length + o24;
        this.flip ? (e33(r31, l34, a35), e33(l34, r31, h26)) : (e33(a35, l34, r31), e33(h26, r31, l34)), a35 = r31, l34 = h26;
      }
      this.flip ? (e33(h25, l34, a35), h25 !== n26 && e33(h25, n26, l34)) : (e33(a35, l34, h25), h25 !== n26 && e33(l34, n26, h25));
    }
  }
};
var $6 = class {
  constructor(t28, e33, i19, s26, r30, o24 = {}) {
    this.options = o24, this._extrusionVertexCount = 0, this.numExtrusionProfiles = 0, this.numVerticesTotal = 0, this.numNormalsTotal = 0, this.profile = e33, this.path = t28, this.extruder = i19, this.startCap = s26, this.endCap = r30;
    const h25 = this.path.vertices.length - 2;
    this.numExtrusionProfiles = i19.numProfilesPerJoin() * h25 + 2, this.numVerticesTotal = e33.vertices.length * this.numExtrusionProfiles, this.numNormalsTotal = this.numVerticesTotal, this.startCap.vertexBufferStart = this.numVerticesTotal;
    const n26 = this.startCap.getNumVertices();
    this.numVerticesTotal += n26, this.numNormalsTotal += n26, this.endCap.vertexBufferStart = this.numVerticesTotal;
    const a35 = this.endCap.getNumVertices();
    this.numVerticesTotal += a35, this.numNormalsTotal += a35, this.pathVertexData = n14(1 * this.numVerticesTotal), this.profileRightAxisData = n14(4 * this.numVerticesTotal), this.profileUpAxisData = n14(4 * this.numVerticesTotal), this.profileVertexAndNormalData = n14(4 * this.numVerticesTotal), this.originData = n14(3 * this.path.vertices.length), this._rebuildGeometry(), this.buildTopology();
  }
  emitVertex(t28, e33, i19, s26, r30) {
    if (this.profileRightAxisData[4 * this._extrusionVertexCount + 0] = e33.right[0], this.profileRightAxisData[4 * this._extrusionVertexCount + 1] = e33.right[1], this.profileRightAxisData[4 * this._extrusionVertexCount + 2] = e33.right[2], this.profileUpAxisData[4 * this._extrusionVertexCount + 0] = e33.up[0], this.profileUpAxisData[4 * this._extrusionVertexCount + 1] = e33.up[1], this.profileUpAxisData[4 * this._extrusionVertexCount + 2] = e33.up[2], this.profileVertexAndNormalData[4 * this._extrusionVertexCount + 0] = i19[0], this.profileVertexAndNormalData[4 * this._extrusionVertexCount + 1] = i19[1], this.profileVertexAndNormalData[4 * this._extrusionVertexCount + 2] = s26[0], this.profileVertexAndNormalData[4 * this._extrusionVertexCount + 3] = s26[1], this.pathVertexData[this._extrusionVertexCount] = t28, r30) {
      const e34 = this.path.vertices[t28];
      this.profileRightAxisData[4 * this._extrusionVertexCount + 3] = e34.rotationRight[0] * e34.maxStretchDistance, this.profileUpAxisData[4 * this._extrusionVertexCount + 3] = e34.rotationRight[1] * e34.maxStretchDistance;
    } else this.profileRightAxisData[4 * this._extrusionVertexCount + 3] = 0, this.profileUpAxisData[4 * this._extrusionVertexCount + 3] = 0;
    ++this._extrusionVertexCount;
  }
  emitCapVertex(t28, e33, i19, s26, r30, o24) {
    this.profileRightAxisData[4 * this._extrusionVertexCount + 0] = e33.right[0], this.profileRightAxisData[4 * this._extrusionVertexCount + 1] = e33.right[1], this.profileRightAxisData[4 * this._extrusionVertexCount + 2] = e33.right[2], this.profileUpAxisData[4 * this._extrusionVertexCount + 0] = e33.up[0], this.profileUpAxisData[4 * this._extrusionVertexCount + 1] = e33.up[1], this.profileUpAxisData[4 * this._extrusionVertexCount + 2] = e33.up[2], this.profileVertexAndNormalData[4 * this._extrusionVertexCount + 0] = i19[0], this.profileVertexAndNormalData[4 * this._extrusionVertexCount + 1] = i19[1], this.profileVertexAndNormalData[4 * this._extrusionVertexCount + 2] = s26[0], this.profileVertexAndNormalData[4 * this._extrusionVertexCount + 3] = s26[1], this.pathVertexData[this._extrusionVertexCount] = t28, this.profileRightAxisData[4 * this._extrusionVertexCount + 3] = r30, this.profileUpAxisData[4 * this._extrusionVertexCount + 3] = o24, ++this._extrusionVertexCount;
  }
  _rebuildGeometry() {
    const t28 = (t29, e34, i19, s26, r30) => this.emitVertex(t29, e34, i19, s26, r30), e33 = (t29, e34, i19, s26, r30, o24) => this.emitCapVertex(t29, e34, i19, s26, r30, o24);
    this._extrusionVertexCount = 0;
    for (const i19 of this.path.vertices) this.originData[3 * i19.index + 0] = i19.pos[0], this.originData[3 * i19.index + 1] = i19.pos[1], this.originData[3 * i19.index + 2] = i19.pos[2];
    this.startCap.rebuildConnectingProfileGeometry(this.path.vertices[0], this.profile, e33);
    for (let i19 = 1; i19 < this.path.vertices.length - 1; ++i19) this.extruder.extrude(this.path.vertices[i19], this.profile, t28);
    this.endCap.rebuildConnectingProfileGeometry(this.path.vertices[this.path.vertices.length - 1], this.profile, e33), this.startCap.rebuildCapGeometry(this.path.vertices[0], e33), this.endCap.rebuildCapGeometry(this.path.vertices[this.path.vertices.length - 1], e33);
  }
  buildTopology() {
    const t28 = this.profile.vertices.length, e33 = this.profile.numSegments, i19 = this.numExtrusionProfiles - 1;
    let s26 = 3 * (2 * (e33 * i19));
    this.startCap.indexBufferStart = s26, this.startCap.firstProfileVertexIndex = 0, s26 += this.startCap.getNumIndices(), this.endCap.indexBufferStart = s26, this.endCap.firstProfileVertexIndex = t28 * (this.numExtrusionProfiles - 1);
    const r30 = new Array(), o24 = new Array(), h25 = new Array(), n26 = (t29, e34, i20) => {
      r30.push(t29), r30.push(e34), r30.push(i20), o24.push(t29), o24.push(e34), o24.push(i20), h25.push(this.pathVertexData[t29]), h25.push(this.pathVertexData[e34]), h25.push(this.pathVertexData[i20]);
    };
    for (let a35 = 0; a35 < e33; ++a35) {
      const e34 = this.profile.vertexIndices[2 * a35], s27 = this.profile.vertexIndices[2 * a35 + 1];
      for (let r31 = 0; r31 < i19; ++r31) {
        const i20 = r31 * t28 + e34, o25 = (r31 + 1) * t28 + s27, h26 = r31 * t28 + s27;
        n26(i20, (r31 + 1) * t28 + e34, o25), n26(i20, o25, h26);
      }
    }
    this.startCap.buildTopology(this.path.vertices[0], n26), this.endCap.buildTopology(this.path.vertices[this.path.vertices.length - 1], n26), this.vertexIndices = n8(r30), this.normalIndices = n8(o24), this.pathVertexIndices = n8(h25);
  }
  onPathChanged() {
    this._rebuildGeometry();
  }
};
var tt2 = class {
  constructor(t28) {
    this.builder = t28;
  }
  get xform() {
    return this.builder.path.xform;
  }
  onPathChanged() {
    this.builder.onPathChanged();
  }
};
var et2 = class extends tt2 {
  constructor(t28) {
    super(t28), this.vertexAttributePosition = null, this.vertexAttributeNormal = null, this.vertexAttributeColor = null, this.vertexAttributePosition = n14(3 * this.builder.numVerticesTotal), this.vertexAttributeNormal = n14(3 * this.builder.numNormalsTotal), this.vertexAttributeColor = new Uint8Array(4), this.vertexAttributeColor[0] = 255, this.vertexAttributeColor[1] = 255, this.vertexAttributeColor[2] = 255, this.vertexAttributeColor[3] = 255;
  }
  bakeVertexColors(t28) {
    this.vertexAttributeColor[0] = 255 * t28[0], this.vertexAttributeColor[1] = 255 * t28[1], this.vertexAttributeColor[2] = 255 * t28[2], this.vertexAttributeColor[3] = 255 * (t28.length > 3 ? t28[3] : 1);
  }
  bake(t28) {
    this.size = t28;
    for (let e33 = 0; e33 < this.builder.numVerticesTotal; ++e33) {
      let i19 = this.builder.pathVertexData[e33];
      const s26 = 0 === i19 || i19 === this.builder.path.vertices.length - 1;
      i19 *= 3;
      const h25 = st;
      o2(h25, this.builder.originData[i19++], this.builder.originData[i19++], this.builder.originData[i19]);
      const n26 = 4 * e33, f27 = nt, x18 = rt, c34 = at, d26 = lt2, m18 = ut2;
      let v21 = 0, g23 = 0;
      if (o2(d26, this.builder.profileRightAxisData[n26], this.builder.profileRightAxisData[n26 + 1], this.builder.profileRightAxisData[n26 + 2]), o2(m18, this.builder.profileUpAxisData[n26], this.builder.profileUpAxisData[n26 + 1], this.builder.profileUpAxisData[n26 + 2]), r8(x18, this.builder.profileVertexAndNormalData[n26] * t28[0], this.builder.profileVertexAndNormalData[n26 + 1] * t28[1]), s26) _(c34, m18, d26), v21 = this.builder.profileRightAxisData[n26 + 3] * t28[0], g23 = this.builder.profileUpAxisData[n26 + 3];
      else {
        const t29 = ot3, e34 = ht;
        r8(t29, this.builder.profileRightAxisData[n26 + 3], this.builder.profileUpAxisData[n26 + 3]);
        const i20 = q3(t29);
        v4(t29, t29);
        const s27 = j5(x18, t29);
        if (Math.abs(s27) > i20) {
          r8(e34, -t29[1], t29[0]);
          const h26 = j5(x18, e34);
          l9(t29, t29, i20 * Math.sign(s27)), l9(e34, e34, h26), s6(x18, t29, e34);
        }
        o2(c34, 0, 0, 0);
      }
      o2(f27, d26[0] * x18[0] + m18[0] * x18[1], d26[1] * x18[0] + m18[1] * x18[1], d26[2] * x18[0] + m18[2] * x18[1]), this.vertexAttributePosition[3 * e33 + 0] = h25[0] + f27[0] + c34[0] * v21, this.vertexAttributePosition[3 * e33 + 1] = h25[1] + f27[1] + c34[1] * v21, this.vertexAttributePosition[3 * e33 + 2] = h25[2] + f27[2] + c34[2] * v21;
      const b24 = rt;
      r8(b24, this.builder.profileVertexAndNormalData[n26 + 2], this.builder.profileVertexAndNormalData[n26 + 3]), this.vertexAttributeNormal[3 * e33 + 0] = d26[0] * b24[0] + m18[0] * b24[1] + c34[0] * g23, this.vertexAttributeNormal[3 * e33 + 1] = d26[1] * b24[0] + m18[1] * b24[1] + c34[1] * g23, this.vertexAttributeNormal[3 * e33 + 2] = d26[2] * b24[0] + m18[2] * b24[1] + c34[2] * g23;
    }
  }
  createGeometryData() {
    const t28 = [[O4.POSITION, this.builder.vertexIndices], [O4.NORMAL, this.builder.normalIndices]], e33 = [[O4.POSITION, new s7(this.vertexAttributePosition, 3, true)], [O4.NORMAL, new s7(this.vertexAttributeNormal, 3, true)]];
    if (this.vertexAttributeColor) {
      const i19 = this.builder.vertexIndices.length;
      t28.push([O4.COLOR, new Array(i19).fill(0)]), e33.push([O4.COLOR, new s7(this.vertexAttributeColor, 4)]);
    }
    return { vertexAttributes: e33, indices: t28 };
  }
  onPathChanged() {
    super.onPathChanged(), this.bake(this.size);
  }
  intersect(t28, e33, i19) {
    const s26 = this.builder.vertexIndices, r30 = new s7(this.vertexAttributePosition, 3), o24 = s26.length / 3;
    y5(t28, e33, 0, o24, s26, r30, void 0, void 0, i19);
  }
};
var it2 = class extends tt2 {
  constructor(t28, e33, i19, s26) {
    super(t28), this.sizeAttributeValue = e33, this.colorAttributeValue = i19, this.opacityAttributeValue = s26, this.vvData = null, this.baked = new et2(t28), this.vvData = n14(4 * this.builder.path.vertices.length);
    for (let r30 = 0; r30 < this.builder.path.vertices.length; ++r30) {
      this.vvData[4 * r30 + 0] = e33, this.vvData[4 * r30 + 1] = i19, this.vvData[4 * r30 + 2] = s26;
      const t29 = 0 === r30 || r30 === this.builder.path.vertices.length - 1;
      this.vvData[4 * r30 + 3] = t29 ? 1 : 0;
    }
  }
  createGeometryData() {
    return { vertexAttributes: [[O4.POSITION, new s7(this.builder.originData, 3, true)], [O4.PROFILERIGHT, new s7(this.builder.profileRightAxisData, 4, true)], [O4.PROFILEUP, new s7(this.builder.profileUpAxisData, 4, true)], [O4.PROFILEVERTEXANDNORMAL, new s7(this.builder.profileVertexAndNormalData, 4, true)], [O4.FEATUREVALUE, new s7(this.vvData, 4, true)]], indices: [[O4.POSITION, this.builder.pathVertexIndices], [O4.PROFILERIGHT, this.builder.vertexIndices], [O4.PROFILEUP, this.builder.vertexIndices], [O4.PROFILEVERTEXANDNORMAL, this.builder.vertexIndices], [O4.FEATUREVALUE, this.builder.pathVertexIndices]] };
  }
};
var st = n5();
var rt = n13();
var ot3 = n13();
var ht = n13();
var nt = n5();
var at = n5();
var lt2 = n5();
var ut2 = n5();
var pt = n5();
var ft = M11();
var xt = e11();

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/PathTechnique.js
var N4 = /* @__PURE__ */ new Map([[O4.POSITION, 0], [O4.PROFILERIGHT, 1], [O4.PROFILEUP, 2], [O4.PROFILEVERTEXANDNORMAL, 3], [O4.FEATUREVALUE, 4]]);
var x14 = class extends p15 {
  constructor() {
    super(...arguments), this.ambient = r2(0.2, 0.2, 0.2), this.diffuse = r2(0.8, 0.8, 0.8), this.specular = r2(0, 0, 0), this.opacity = 1;
  }
};
var C8 = class _C extends e14 {
  initializeConfiguration(e33, o24) {
    o24.hasWebGL2Context = e33.rctx.type === r15.WEBGL2, o24.spherical = e33.viewingMode === l15.Global, o24.doublePrecisionRequiresObfuscation = e33.rctx.driverTest.doublePrecisionRequiresObfuscation.result;
  }
  initializeProgram(e33) {
    return new o10(e33.rctx, _C.shader.get().build(this.configuration), N4);
  }
  initializePipeline() {
    const e33 = this.configuration.transparencyPassType, o24 = this.configuration, t28 = e33 === o8.NONE, i19 = e33 === o8.FrontFace;
    return W({ blending: o24.output !== h10.Color && o24.output !== h10.Alpha || !o24.transparent ? null : t28 ? c14 : A4(e33), culling: o24.hasSlicePlane && !o24.transparent && o24.doubleSidedMode !== i7.None ? o9 : null, depthTest: { func: l17(e33) }, depthWrite: t28 || i19 ? a13 : null, colorWrite: _6, stencilWrite: o24.hasOccludees ? e15 : null, stencilTest: o24.hasOccludees ? f12 : null, polygonOffset: t28 || i19 ? null : _7 });
  }
};
C8.shader = new t16(_8, () => import("./Path.glsl-4A3JEDUS.js"));
var E17 = class extends s11 {
  constructor() {
    super(...arguments), this.output = h10.Color, this.doubleSidedMode = i7.None, this.transparencyPassType = o8.NONE, this.spherical = false, this.receiveShadows = false, this.receiveAmbientOcclusion = false, this.vvSize = false, this.vvColor = false, this.vvOpacity = false, this.hasSlicePlane = false, this.transparent = false, this.hasOccludees = false, this.hasMultipassTerrain = false, this.cullAboveGround = false, this.doublePrecisionRequiresObfuscation = false;
  }
};
e([r18({ count: h10.COUNT })], E17.prototype, "output", void 0), e([r18({ count: i7.COUNT })], E17.prototype, "doubleSidedMode", void 0), e([r18({ count: o8.COUNT })], E17.prototype, "transparencyPassType", void 0), e([r18()], E17.prototype, "spherical", void 0), e([r18()], E17.prototype, "receiveShadows", void 0), e([r18()], E17.prototype, "receiveAmbientOcclusion", void 0), e([r18()], E17.prototype, "vvSize", void 0), e([r18()], E17.prototype, "vvColor", void 0), e([r18()], E17.prototype, "vvOpacity", void 0), e([r18()], E17.prototype, "hasSlicePlane", void 0), e([r18()], E17.prototype, "transparent", void 0), e([r18()], E17.prototype, "hasOccludees", void 0), e([r18()], E17.prototype, "hasMultipassTerrain", void 0), e([r18()], E17.prototype, "cullAboveGround", void 0), e([r18()], E17.prototype, "doublePrecisionRequiresObfuscation", void 0), e([r18({ constValue: d8.Disabled })], E17.prototype, "pbrMode", void 0), e([r18({ constValue: true })], E17.prototype, "hasVvInstancing", void 0), e([r18({ constValue: false })], E17.prototype, "useCustomDTRExponentForWater", void 0), e([r18({ constValue: false })], E17.prototype, "useFillLights", void 0);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/PathMaterial.js
var S15 = class _S extends h12 {
  constructor(e33) {
    super(e33, new g17()), this.supportsEdges = true, this._vertexAttributeLocations = N4, this._configuration = new E17(), this._vertexBufferLayout = _S.getVertexBufferLayout();
  }
  getConfiguration(e33, t28) {
    return this._configuration.output = e33, this._configuration.vvSize = this.parameters.vvSizeEnabled, this._configuration.vvColor = this.parameters.vvColorEnabled, this._configuration.vvOpacity = this.parameters.vvOpacityEnabled, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.transparent = this.parameters.transparent, this._configuration.hasOccludees = this.parameters.hasOccludees, e33 !== h10.Color && e33 !== h10.Alpha || (this._configuration.doubleSidedMode = this.parameters.doubleSided && "normal" === this.parameters.doubleSidedType ? i7.View : this.parameters.doubleSided && "winding-order" === this.parameters.doubleSidedType ? i7.WindingOrder : i7.None, this._configuration.receiveShadows = this.parameters.receiveShadows, this._configuration.receiveAmbientOcclusion = !!t28.ssaoHelper.active && this.parameters.receiveSSAO), this._configuration.transparencyPassType = t28.transparencyPassType, this._configuration.hasMultipassTerrain = t28.multipassTerrain.enabled, this._configuration.cullAboveGround = t28.multipassTerrain.cullAboveGround, this._configuration;
  }
  isVisibleForOutput(e33) {
    return e33 !== h10.Shadow && e33 !== h10.ShadowExcludeHighlight && e33 !== h10.ShadowHighlight || this.parameters.castShadows;
  }
  isVisible() {
    return super.isVisible() && this.parameters.opacity > 0;
  }
  intersect(i19, a35, s26, o24, n26, c34) {
    const u32 = i19;
    if (!o22(u32)) return;
    const l34 = u32.path, d26 = [this.parameters.size[0], this.parameters.size[1]];
    if (this.parameters.vvSizeEnabled) {
      const t28 = this.parameters.vvSizeOffset, r30 = this.parameters.vvSizeFactor, i20 = this.parameters.vvSizeMinSize, a36 = this.parameters.vvSizeMaxSize, s27 = l34.sizeAttributeValue;
      d26[0] *= a5(t28[0] + s27 * r30[0], i20[0], a36[0]), d26[1] *= a5(t28[2] + s27 * r30[2], i20[2], a36[2]);
    }
    const p23 = Math.max(d26[0], d26[1]), m18 = i19.boundingInfo;
    if (t(m18)) return void this._intersectTriangles(l34, d26, o24, n26, c34);
    const f27 = u5(m18.bbMin[0] - p23, m18.bbMin[1] - p23, m18.bbMin[2] - p23, m18.bbMax[0] + p23, m18.bbMax[1] + p23, m18.bbMax[2] + p23), S19 = [n26[0] - o24[0], n26[1] - o24[1], n26[2] - o24[2]], v21 = Math.sqrt(S19[0] * S19[0] + S19[1] * S19[1] + S19[2] * S19[2]), g23 = [v21 / S19[0], v21 / S19[1], v21 / S19[2]];
    V3(f27, o24, g23, s26.tolerance) && this._intersectTriangles(l34, d26, o24, n26, c34);
  }
  _intersectTriangles(e33, t28, r30, i19, a35) {
    e33.baked.size && e33.baked.size[0] === t28[0] && e33.baked.size[1] === t28[1] || e33.baked.bake(t28), e33.baked.intersect(r30, i19, a35);
  }
  createBufferWriter() {
    return new r17(this._vertexBufferLayout);
  }
  requiresSlot(e33, t28) {
    switch (t28) {
      case h10.Shadow:
      case h10.ShadowHighlight:
      case h10.ShadowExcludeHighlight:
        if (!this.parameters.castShadows) return false;
      case h10.Color:
      case h10.Alpha:
      case h10.Depth:
      case h10.Normal:
      case h10.Highlight:
      case h10.ObjectAndLayerIdColor:
        return e33 === (this.parameters.transparent ? E9.TRANSPARENT_MATERIAL : E9.OPAQUE_MATERIAL) || e33 === E9.DRAPED_MATERIAL;
      default:
        return false;
    }
  }
  createGLMaterial(e33) {
    return new v16(e33);
  }
  static getVertexBufferLayout() {
    return T2().vec3f(O4.POSITION).vec4f(O4.PROFILERIGHT).vec4f(O4.PROFILEUP).vec4f(O4.PROFILEVERTEXANDNORMAL).vec4f(O4.FEATUREVALUE);
  }
};
var v16 = class extends t15 {
  _updateOccludeeState(e33) {
    e33.hasOccludees !== this._material.parameters.hasOccludees && this._material.setParameters({ hasOccludees: e33.hasOccludees });
  }
  _updateShadowState(e33) {
    (t(this.technique) || e33.shadowMap.enabled !== this.technique.configuration.receiveShadows) && this._material.setParameters({ receiveShadows: e33.shadowMap.enabled });
  }
  beginSlot(e33) {
    return this._output !== h10.Color && this._output !== h10.Alpha || (this._updateShadowState(e33), this._updateOccludeeState(e33)), this.ensureTechnique(C8, e33);
  }
};
var g17 = class extends x14 {
  constructor() {
    super(...arguments), this.doubleSided = false, this.doubleSidedType = "normal", this.receiveSSAO = true, this.receiveShadows = false, this.castShadows = true, this.hasSlicePlane = false, this.transparent = false, this.hasOccludees = false;
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DPathSymbolLayer.js
var ht2 = ["polyline"];
var ct2 = class extends y7 {
  constructor(t28, e33, i19, r30) {
    super(t28, e33, i19, r30), this._intrinsicSize = r13(1, 1), this._upVectorAlignment = n24.Path, this._stencilWidth = 0.1, this.ensureDrapedStatus(false);
  }
  async doLoad() {
    const e33 = r(this.symbolLayer.width) ? this.symbolLayer.width : this.symbolLayer.height, i19 = r(this.symbolLayer.height) ? this.symbolLayer.height : e33;
    this._vvConvertOptions = { modelSize: [1, 1, 1], symbolSize: [e33, 1, i19], unitInMeters: this._context.renderCoordsHelper.unitInMeters, transformation: { anchor: [0, 0, 0], scale: [1, 1, 1], rotation: [0, 0, 0] }, supportedTypes: { size: true, color: true, opacity: true, rotation: false } }, this._context.renderer && this._context.renderer.visualVariables && this._context.renderer.visualVariables.length > 0 ? this._fastUpdates = I9(this._context.renderer, this._vvConvertOptions) : this._fastUpdates = { enabled: false };
    const a35 = this.symbolLayer.anchor || "center";
    this._upVectorAlignment = "heading" === this.symbolLayer.profileRotation ? n24.World : n24.Path;
    const l34 = this.symbolLayer.profile || "circle";
    switch (l34) {
      default:
      case "circle":
        this._profile = F7(c21);
        break;
      case "quad":
        this._profile = z6();
    }
    let c34 = [0, 0];
    switch ("center" !== a35 && (c34 = { left: [0.5, 0], right: [-0.5, 0], top: [0, -0.5], bottom: [0, 0.5] }[a35], this._profile.translate(c34[0], c34[1])), this.symbolLayer.join) {
      case "round":
        this._extruder = new K2(0, o17);
        break;
      case "bevel":
        this._extruder = new K2(0, 1);
        break;
      case "miter":
        this._extruder = new K2(0.8 * Math.PI, 1);
        break;
      default:
        this._extruder = new X2();
    }
    const p23 = this.symbolLayer.cap || "butt";
    switch (p23) {
      case "none":
        this._startCap = new W3(), this._endCap = new W3();
        break;
      case "butt":
      default:
        this._startCap = new Y4(this._profile, 0), this._endCap = new Y4(this._profile, 0, true);
        break;
      case "square":
        this._startCap = new Y4(this._profile, -0.5), this._endCap = new Y4(this._profile, 0.5, true);
        break;
      case "round": {
        const t28 = "quad" === l34;
        this._startCap = new Z3({ profile: this._profile, flip: false, breakNormals: t28, subdivisions: t21 }), this._endCap = new Z3({ profile: this._profile, flip: true, breakNormals: t28, subdivisions: t21 });
        break;
      }
    }
    const m18 = x(this.symbolLayer, "material", "color"), f27 = this._getCombinedOpacityAndColor(m18), d26 = e4(f27), u32 = f27[3], g23 = u32 < 1 || this.needsDrivenTransparentPass, y14 = { diffuse: d26, ambient: d26, opacity: u32, transparent: g23, hasVertexColors: false, hasSlicePlane: this._context.slicePlaneEnabled, castShadows: this.symbolLayer.castShadows, cullFace: g23 || "none" === p23 ? n11.None : n11.Back, offsetTransparentBackfaces: true };
    if (!this._drivenProperties.size && (r8(this._intrinsicSize, e33, i19), !V5(this._intrinsicSize[0]) || !V5(this._intrinsicSize[1]))) throw new s2("graphics3dpathsymbollayer:invalid-size", "Symbol sizes may not be negative values");
    if (this._fastUpdates.enabled && this._fastUpdates.visualVariables.size || l9(this._intrinsicSize, this._intrinsicSize, 1 / this._context.renderCoordsHelper.unitInMeters), this._fastUpdates.enabled) {
      const t28 = { ...y14, ...this._fastUpdates.materialParameters, size: e12(this._intrinsicSize) };
      this._material = new S15(t28);
    } else y14.hasVertexColors = this._drivenProperties.color || this._drivenProperties.opacity, this._material = new E11(y14);
    this._material.setParameters({ usePBR: this._context.physicalBasedRenderingEnabled, isSchematic: true }), this._context.stage.add(this._material);
  }
  destroy() {
    super.destroy(), this._context.stage.remove(this._material), this._material = null;
  }
  createGraphics3DGraphic(t28) {
    const e33 = t28.graphic;
    if (!this._validateGeometry(e33.geometry, ht2, this.symbolLayer.type)) return null;
    const i19 = this.setGraphicElevationContext(e33, new h15()), r30 = t28.renderingInfo;
    return this._createAs3DShape(e33, r30, i19, e33.uid);
  }
  layerOpacityChanged() {
    const t28 = x(this.symbolLayer, "material", "color"), e33 = this._getCombinedOpacity(t28), i19 = e33 < 1 || this.needsDrivenTransparentPass;
    this._material.setParameters({ opacity: e33, transparent: i19 });
  }
  layerElevationInfoChanged(t28, e33) {
    return this.updateGraphics3DGraphicElevationInfo(t28, e33, g7);
  }
  slicePlaneEnabledChanged() {
    return this._material.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled }), true;
  }
  physicalBasedRenderingChanged() {
    return this._material.setParameters({ usePBR: this._context.physicalBasedRenderingEnabled, isSchematic: true }), true;
  }
  pixelRatioChanged() {
    return true;
  }
  skipHighSymbolLodsChanged() {
    return true;
  }
  applyRendererDiff(t28, e33) {
    for (const i19 in t28.diff) {
      if ("visualVariables" !== i19) return e22.Recreate_Symbol;
      if (!P6(this._fastUpdates, e33, this._vvConvertOptions)) return e22.Recreate_Symbol;
      this._material.setParameters(this._fastUpdates.materialParameters);
    }
    return e22.Fast_Update;
  }
  _getVertexData(t28) {
    let e33 = 0;
    const i19 = t28.paths, s26 = [], a35 = t28.spatialReference, o24 = this._context.elevationProvider.spatialReference, n26 = this._context.renderCoordsHelper.spatialReference;
    for (const r30 of i19) e33 += r30.length;
    const l34 = n10(3 * e33), h25 = n10(3 * e33);
    let c34 = 0;
    for (const r30 of i19) {
      s26.push({ index: c34, numVertices: r30.length });
      for (const e34 of r30) l34[c34++] = e34[0], l34[c34++] = e34[1], l34[c34++] = t28.hasZ ? e34[2] : 0;
    }
    let p23 = true;
    return r(o24) && !a35.equals(o24) && (p23 = xn(l34, a35, 0, l34, o24, 0, e33)), r(o24) && !o24.equals(n26) ? xn(l34, o24, 0, h25, n26, 0, e33) : this._copyVertices(l34, 0, h25, 0, e33), { pathVertexDataInfos: s26, vertexDataES: l34, vertexDataRS: h25, projectionSuccess: p23, terrainElevation: 0 };
  }
  _copyVertices(t28, e33, i19, r30, s26) {
    e33 *= 3, r30 *= 3;
    for (let a35 = 0; a35 < s26; ++a35) i19[r30++] = t28[e33++], i19[r30++] = t28[e33++], i19[r30++] = t28[e33++];
  }
  _createAs3DShape(t28, e33, i19, s26) {
    const a35 = t28.geometry, o24 = new Array(), n26 = a35.spatialReference, l34 = a7(), h25 = this._context.renderCoordsHelper;
    gt.spatialReference = n26;
    const m18 = this._getVertexData(a35);
    if (!m18.projectionSuccess) return this.logger.warn("PathSymbol3DLayer geometry failed to be created (failed to project geometry to view spatial reference)"), null;
    if (0 === m18.pathVertexDataInfos.length) return 0 !== a35.paths.length && a35.paths.some((t29) => t29.length > 0) || this.logger.warn("PathSymbol3DLayer geometry failed to be created (no paths were defined)"), null;
    for (const g23 of m18.pathVertexDataInfos) {
      const a36 = g23.index, f28 = g23.numVertices;
      if (f28 < 2) continue;
      if (r(this._context.clippingExtent) && (S(l34), M3(l34, m18.vertexDataES, 3 * a36, f28), !Y(l34, this._context.clippingExtent))) continue;
      const d27 = [];
      for (let t29 = a36; t29 < a36 + 3 * f28; ) {
        const e34 = t29++, r30 = t29++, s27 = t29++, a37 = new G9();
        o2(a37.posES, m18.vertexDataES[e34], m18.vertexDataES[r30], m18.vertexDataES[s27]);
        const o25 = d10(a37.posES, this._context.elevationProvider, i19, h25);
        o2(yt, m18.vertexDataRS[e34], m18.vertexDataRS[r30], m18.vertexDataRS[s27]), h25.setAltitude(yt, o25), r4(a37.pos, yt), d27.push(a37);
      }
      const u33 = new J3(d27);
      pt2(u33, this._upVectorAlignment, this._context.renderCoordsHelper);
      const y14 = new $6(u33, this._profile, this._extruder, this._startCap, this._endCap);
      let _21 = null;
      if (this._fastUpdates.enabled) {
        const e34 = this._fastUpdates.visualVariables, i20 = e34.size ? v13(e34.size.field, t28) : 0, r30 = e34.color ? v13(e34.color.field, t28) : 0, s27 = e34.opacity ? v13(e34.opacity.field, t28) : 0;
        _21 = new it2(y14, i20, r30, s27);
      } else {
        const t29 = [this._intrinsicSize[0], this._intrinsicSize[1]];
        if (this._drivenProperties.size) {
          const i21 = e33.size;
          t29[0] *= mt(i21[0], "symbol-value" === i21[2] ? this.symbolLayer.height || 0 : i21[2], this.symbolLayer.width || 0), t29[1] *= mt(i21[2], "symbol-value" === i21[0] ? this.symbolLayer.width || 0 : i21[0], this.symbolLayer.height || 0);
        }
        let i20;
        this._drivenProperties.color && (i20 = e33.color), this._drivenProperties.opacity && null != e33.opacity && (i20 = i20 ? [i20[0], i20[1], i20[2], e33.opacity] : [1, 1, 1, e33.opacity]);
        const r30 = new et2(y14);
        r30.bake(t29), i20 && r30.bakeVertexColors(i20), _21 = r30;
      }
      const { vertexAttributes: b24, indices: v21 } = _21.createGeometryData(), x18 = this._context.stage.renderView.getObjectAndLayerIdColor({ graphicUid: s26, layerUid: this._context.layer.uid }), w12 = new r27(this._material, b24, v21, _21, n26, this._upVectorAlignment, this._stencilWidth, x18);
      w12.transformation = _21.xform, o24.push(w12);
    }
    if (0 === o24.length) return null;
    const f27 = { layerUid: this._context.layer.uid, graphicUid: s26 }, d26 = new x7({ geometries: o24, metadata: f27 }), u32 = new S8(this, d26, o24, null, null, dt, i19);
    return u32.alignedSampledElevation = m18.terrainElevation, u32.needsElevationUpdates = g7(i19.mode), u32;
  }
};
function pt2(t28, r30, s26) {
  switch (r30) {
    default:
    case n24.World:
      for (const r31 of t28.vertices) {
        u3(yt, r31.pos, t28.offset), s26.worldUpAtPosition(yt, yt), r31.setFrameFromUpVector(yt), r31.rotationFrameUp = r31.frame.up, r8(r31.rotationRight, 1, 0), g2(yt, r31.frame.up, P(r31.frame.up, r31.vLeft)), e5(yt, r31.vLeft, yt), j3(yt, yt), z(yt, yt), g2(_t, r31.frame.up, P(r31.frame.up, r31.vRight)), e5(_t, r31.vRight, _t), z(_t, _t), _(bt, r31.rotationFrameUp, r31.vLeft);
        const n26 = Math.sign(P(bt, r31.vRight));
        if (r31.rotationAngle = n26 * (Math.PI - l5(P(yt, _t))), Math.abs(r31.rotationAngle) > 0) {
          const t29 = F2(Math.cos(0.5 * r31.rotationAngle));
          r26(r31.miterStretch, t29 - 1 + 1, 0, 0, 1);
        }
        const l34 = Math.PI - r31.rotationAngle;
        r31.maxStretchDistance = Math.abs(r31.vMinSiblingLength / Math.cos(0.5 * l34));
      }
      break;
    case n24.Path:
      u3(yt, t28.vertices[0].pos, t28.offset), s26.worldUpAtPosition(yt, yt), q10(t28, yt);
      for (const r31 of t28.vertices) {
        const t29 = Math.sign(P(r31.frame.right, r31.vRight));
        _(r31.rotationFrameUp, r31.vRight, r31.vLeft), g2(r31.rotationFrameUp, r31.rotationFrameUp, t29), z(r31.rotationFrameUp, r31.rotationFrameUp);
        const s27 = P(r31.rotationFrameUp, r31.frame.up), o24 = P(r31.rotationFrameUp, r31.frame.right);
        if (g2(yt, r31.frame.up, -o24), g2(_t, r31.frame.right, s27), u3(yt, yt, _t), z(yt, yt), B5(r31.rotationRight, r31.frame, yt), j3(yt, r31.vLeft), r31.rotationAngle = -t29 * (Math.PI - l5(P(yt, r31.vRight))), Math.abs(r31.rotationAngle) > 0) {
          const t30 = F2(Math.cos(0.5 * r31.rotationAngle));
          r26(r31.miterStretch, 1 + (t30 - 1) * r31.rotationRight[0] * r31.rotationRight[0], (t30 - 1) * r31.rotationRight[0] * r31.rotationRight[1], (t30 - 1) * r31.rotationRight[0] * r31.rotationRight[1], 1 + (t30 - 1) * r31.rotationRight[1] * r31.rotationRight[1]);
        }
        const n26 = Math.PI - r31.rotationAngle;
        r31.maxStretchDistance = Math.abs(r31.vMinSiblingLength * F2(Math.cos(0.5 * n26)));
      }
  }
}
function mt(t28, e33, i19) {
  switch (t28) {
    case "symbol-value":
      return i19;
    case "proportional":
      return e33;
    default:
      return t28;
  }
}
function ft2(t28, e33, i19, r30) {
  let s26 = 0;
  for (const a35 of t28.vertices) i19(a35.posES, vt), s26 += vt.sampledElevation, u3(yt, a35.pos, t28.offset), r30.setAltitude(yt, vt.z), e5(a35.pos, yt, t28.offset);
  return t28.updatePathVertexInformation(), s26 / t28.vertices.length;
}
function dt(t28, e33, i19, r30, s26) {
  const a35 = t28.stageObject, o24 = a35.geometries;
  let n26 = 0;
  ut3.spatialReference = s26.spatialReference;
  for (const l34 of o24) {
    if (!o22(l34)) continue;
    const t29 = l34.path, i20 = t29.builder.path, o25 = l34.geometrySR;
    gt.spatialReference = o25, n26 += ft2(i20, e33, r30, s26), l34.upVectorAlignment !== n24.World && pt2(i20, l34.upVectorAlignment, s26), t29.onPathChanged(), l34.invalidateBoundingInfo(), a35.geometryVertexAttrsUpdated(l34);
  }
  return n26 / o24.length;
}
var ut3 = M4(0, 0, 0, null);
var gt = M4(0, 0, 0, null);
var yt = n5();
var _t = n5();
var bt = n5();
var vt = new T4();

// node_modules/@arcgis/core/views/3d/layers/support/uvUtils.js
function T8(t28, o24, n26, r30, e33 = 1) {
  if (n26.isGeographic && r30 === l15.Global) {
    const t29 = n10(o24.length), s26 = o24.length, r31 = O(n26);
    for (let n27 = 0; n27 < s26; n27 += 3) ee(o24, n27, t29, n27, r31);
    o24 = t29;
  }
  r8(P8, Number.POSITIVE_INFINITY, Number.POSITIVE_INFINITY);
  for (let s26 = 0; s26 < o24.length; s26 += 3) P8[0] = Math.min(P8[0], o24[s26]), P8[1] = Math.min(P8[1], o24[s26 + 1]);
  const m18 = P8[0] % e33, a35 = P8[1] % e33, i19 = P8[0] - m18, c34 = P8[1] - a35;
  for (let s26 = 0; s26 < o24.length; s26 += 3) {
    const n27 = s26 / 3 * 4;
    t28[n27] = (o24[s26] - i19) / e33, t28[n27 + 1] = (o24[s26 + 1] - c34) / e33, t28[n27 + 2] = i19 / e33, t28[n27 + 3] = c34 / e33;
  }
}
function E18(t28, o24, n26, c34, f27 = 1) {
  o2(_16, 1, 0, 0), o2(k8, 0, 1, 0), o2(y11, 0, 0, 1), U6(V9, n26), O12(n26, Y5) && S16(Y5, _16, k8, y11, c34, V9), r8(P8, Number.POSITIVE_INFINITY, Number.POSITIVE_INFINITY), r8(v17, Number.NEGATIVE_INFINITY, Number.NEGATIVE_INFINITY);
  for (let s26 = 0; s26 < n26.length; s26 += 3) {
    o2(F8, n26[s26], n26[s26 + 1], n26[s26 + 2]);
    const t29 = P(_16, F8), o25 = P(k8, F8);
    P8[0] = Math.min(P8[0], t29), P8[1] = Math.min(P8[1], o25), v17[0] = Math.max(v17[0], t29), v17[1] = Math.max(v17[1], o25);
  }
  const I13 = P(y11, V9);
  B6(x15, P8[0], P8[1], I13, _16, k8, y11), B6(A10, v17[0], P8[1], I13, _16, k8, y11), B6(G10, P8[0], v17[1], I13, _16, k8, y11), e5(A10, A10, x15), g2(A10, A10, 0.5), e5(G10, G10, x15), g2(G10, G10, 0.5), u3(x15, x15, A10), u3(x15, x15, G10);
  const l34 = P8[0] % f27, h25 = P8[1] % f27, u32 = P8[0] - l34, N9 = P8[1] - h25;
  for (let s26 = 0; s26 < n26.length; s26 += 3) {
    o2(F8, n26[s26], n26[s26 + 1], n26[s26 + 2]);
    const m18 = s26 / 3, a35 = 4 * m18;
    t28[a35] = (P(_16, F8) - u32) / f27, t28[a35 + 1] = (P(k8, F8) - N9) / f27, t28[a35 + 2] = u32 / f27, t28[a35 + 3] = N9 / f27;
    const i19 = 9 * m18;
    for (let t29 = 0; t29 < 3; t29++) o24[i19 + t29] = x15[t29], o24[i19 + t29 + 3] = A10[t29], o24[i19 + t29 + 6] = G10[t29];
  }
}
var V9 = n5();
var F8 = n5();
var Y5 = p9();
var _16 = n5();
var k8 = n5();
var y11 = n5();
var P8 = n13();
var v17 = n13();
var x15 = n5();
var A10 = n5();
var G10 = n5();
function O12(t28, o24) {
  const s26 = t28.length / 3 - 1;
  return b8(t28, o24, 0, Math.floor(s26 / 3), Math.floor(s26 * (2 / 3)));
}
function S16(o24, s26, n26, m18, i19, h25) {
  r(i19) ? (i19.basisMatrixAtPosition(h25, d21), o2(w10, d21[0], d21[1], d21[2]), o2(z7, d21[4], d21[5], d21[6]), o2(D8, d21[8], d21[9], d21[10])) : (o2(w10, 1, 0, 0), o2(z7, 0, 1, 0), o2(D8, 0, 0, 1));
  const u32 = Y2(o24);
  P(u32, D8) < 0 && g2(u32, u32, -1), r4(m18, u32);
  const N9 = P(u32, z7), p23 = P(u32, w10);
  Math.abs(N9) < Math.abs(p23) ? (q(s26, w10, u32, -p23), z(s26, s26), _(n26, s26, u32), z(n26, n26), g2(n26, n26, -1)) : (q(n26, z7, u32, -N9), z(n26, n26), _(s26, n26, u32), z(s26, s26));
}
var d21 = e11();
var w10 = n5();
var z7 = n5();
var D8 = n5();
function U6(t28, o24) {
  o2(q11, 0, 0, 0);
  for (let s26 = 0; s26 < o24.length - 3; s26 += 3) q11[0] += o24[s26], q11[1] += o24[s26 + 1], q11[2] += o24[s26 + 2];
  g2(t28, q11, 1 / (o24.length / 3 - 1));
}
var q11 = n5();
function B6(t28, o24, s26, n26, e33, m18, a35) {
  o2(t28, o24 * e33[0] + s26 * m18[0] + n26 * a35[0], o24 * e33[1] + s26 * m18[1] + n26 * a35[1], o24 * e33[2] + s26 * m18[2] + n26 * a35[2]);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/PatternTechnique.js
var C9 = class _C extends e14 {
  initializeConfiguration(e33, t28) {
    t28.hasWebGL2Context = e33.rctx.type === r15.WEBGL2;
  }
  initializeProgram(e33) {
    return new o10(e33.rctx, _C.shader.get().build(this.configuration), w11);
  }
  _setPipelineState(e33, o24) {
    const r30 = this.configuration, i19 = e33 === o8.NONE, s26 = e33 === o8.FrontFace;
    return W({ blending: r30.output === h10.Color || r30.output === h10.Alpha ? i19 ? c14 : A4(e33) : null, culling: h13(r30.cullFace), depthTest: { func: l17(e33) }, depthWrite: i19 ? r30.writeDepth ? a13 : null : E8(e33), colorWrite: _6, stencilWrite: r30.hasOccludees ? e15 : null, stencilTest: r30.hasOccludees ? o24 ? o11 : f12 : null, polygonOffset: i19 || s26 ? r30.polygonOffset ? N5 : null : a14(r30.enableOffset) });
  }
  initializePipeline() {
    return this._occludeePipelineState = this._setPipelineState(this.configuration.transparencyPassType, true), this._setPipelineState(this.configuration.transparencyPassType, false);
  }
  getPipelineState(e33, t28) {
    return t28 ? this._occludeePipelineState : super.getPipelineState(e33, t28);
  }
};
C9.shader = new t16(T5, () => import("./Pattern.glsl-EX45NUL2.js"));
var N5 = { factor: 1, units: 1 };
var x16 = class extends s11 {
  constructor() {
    super(...arguments), this.output = h10.Color, this.cullFace = n11.None, this.transparencyPassType = o8.NONE, this.hasSlicePlane = false, this.hasVertexColors = false, this.polygonOffset = false, this.writeDepth = true, this.hasOccludees = false, this.enableOffset = true, this.hasMultipassTerrain = false, this.cullAboveGround = false;
  }
};
e([r18({ count: h10.COUNT })], x16.prototype, "output", void 0), e([r18({ count: n11.COUNT })], x16.prototype, "cullFace", void 0), e([r18({ count: a23.COUNT })], x16.prototype, "style", void 0), e([r18({ count: o8.COUNT })], x16.prototype, "transparencyPassType", void 0), e([r18()], x16.prototype, "hasSlicePlane", void 0), e([r18()], x16.prototype, "hasVertexColors", void 0), e([r18()], x16.prototype, "polygonOffset", void 0), e([r18()], x16.prototype, "writeDepth", void 0), e([r18()], x16.prototype, "hasOccludees", void 0), e([r18()], x16.prototype, "patternSpacing", void 0), e([r18()], x16.prototype, "lineWidth", void 0), e([r18()], x16.prototype, "enableOffset", void 0), e([r18()], x16.prototype, "draped", void 0), e([r18()], x16.prototype, "hasMultipassTerrain", void 0), e([r18()], x16.prototype, "cullAboveGround", void 0);
var w11 = /* @__PURE__ */ new Map([[O4.POSITION, 0], [O4.COLOR, 3], [O4.UVMAPSPACE, 4], [O4.BOUNDINGRECT, 5]]);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/PatternMaterial.js
var S17 = class extends e19 {
  constructor(t28) {
    super(t28, new R10()), this.supportsEdges = true, this._vertexAttributeLocations = w11, this._configuration = new x16();
  }
  getConfiguration(t28, e33) {
    return this._configuration.output = t28, this._configuration.cullFace = this.parameters.cullFace, this._configuration.hasVertexColors = this.parameters.hasVertexColors, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.polygonOffset = this.parameters.polygonOffset, this._configuration.writeDepth = this.parameters.writeDepth, this._configuration.style = this.parameters.style, this._configuration.patternSpacing = this.parameters.patternSpacing, this._configuration.lineWidth = this.parameters.lineWidth, this._configuration.draped = this.parameters.draped, this._configuration.transparencyPassType = e33.transparencyPassType, this._configuration.enableOffset = e33.camera.relativeElevation < S4, this._configuration.hasMultipassTerrain = e33.multipassTerrain.enabled, this._configuration.cullAboveGround = e33.multipassTerrain.cullAboveGround, this._configuration;
  }
  requiresSlot(t28, e33) {
    if (e33 === h10.Color || e33 === h10.Alpha || e33 === h10.Highlight || e33 === h10.Depth && this.parameters.writeLinearDepth) {
      if (t28 === E9.DRAPED_MATERIAL) return true;
      if (e33 === h10.Highlight) return t28 === E9.OPAQUE_MATERIAL;
      return t28 === (this.parameters.writeDepth ? E9.TRANSPARENT_MATERIAL : E9.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL);
    }
    return false;
  }
  createGLMaterial(t28) {
    return new y12(t28);
  }
  createBufferWriter() {
    const t28 = T2().vec3f(O4.POSITION).vec4u8(O4.COLOR).vec4f(O4.UVMAPSPACE);
    return this.parameters.draped || t28.mat3f(O4.BOUNDINGRECT), new E19(t28);
  }
};
var y12 = class extends t15 {
  _updateParameters(t28) {
    return this.ensureTechnique(C9, t28);
  }
  _updateOccludeeState(t28) {
    t28.hasOccludees !== this._material.parameters.hasOccludees && this._material.setParameters({ hasOccludees: t28.hasOccludees });
  }
  beginSlot(t28) {
    return this._output !== h10.Color && this._output !== h10.Alpha || this._updateOccludeeState(t28), this._updateParameters(t28);
  }
};
var E19 = class extends r17 {
  write(t28, a35, o24, n26, c34) {
    for (const h25 of this.vertexBufferLayout.fieldNames) {
      const a36 = o24.vertexAttributes.get(h25), u32 = o24.indices.get(h25);
      if (a36 && u32) switch (h25) {
        case O4.POSITION: {
          s8(3 === a36.size);
          const e33 = n26.getField(h25, i4);
          e33 && g5(u32, a36.data, t28, e33, c34);
          break;
        }
        case O4.COLOR: {
          s8(3 === a36.size || 4 === a36.size);
          const t29 = n26.getField(h25, x4);
          t29 && O7(u32, a36.data, a36.size, t29, c34);
          break;
        }
        case O4.UVMAPSPACE: {
          s8(4 === a36.size);
          const t29 = n26.getField(h25, c6);
          t29 && a15(u32, a36.data, t29, c34);
          break;
        }
        case O4.BOUNDINGRECT: {
          s8(9 === a36.size);
          const r30 = n26.getField(h25, l10);
          r30 && this.writeBoundingRect(u32, a36.data, t28, r30, c34);
          break;
        }
      }
    }
  }
  writeBoundingRect(t28, e33, r30, i19, s26) {
    const a35 = r30, o24 = i19.typedBuffer, n26 = i19.typedBufferStride, c34 = t28.length;
    s26 *= n26;
    for (let h25 = 0; h25 < c34; ++h25) {
      const r31 = 9 * t28[h25], i20 = e33[r31], c35 = e33[r31 + 1], u32 = e33[r31 + 2];
      o24[s26] = a35[0] * i20 + a35[4] * c35 + a35[8] * u32 + a35[12], o24[s26 + 1] = a35[1] * i20 + a35[5] * c35 + a35[9] * u32 + a35[13], o24[s26 + 2] = a35[2] * i20 + a35[6] * c35 + a35[10] * u32 + a35[14];
      for (let t29 = 3; t29 < 9; ++t29) o24[s26 + t29] = e33[r31 + t29];
      s26 += n26;
    }
  }
};
var R10 = class extends u11 {
  constructor() {
    super(...arguments), this.color = r6(1, 1, 1, 1), this.writeDepth = true, this.writeLinearDepth = false, this.hasVertexColors = false, this.polygonOffset = false, this.hasSlicePlane = false, this.cullFace = n11.None, this.hasOccludees = false, this.style = a23.Cross, this.patternSpacing = 10, this.lineWidth = 1, this.draped = true;
  }
};

// node_modules/@arcgis/core/views/3d/layers/support/patternUtils.js
function l29(t28, r30, e33) {
  return u29(c31(t28), r30, e33);
}
function c31(t28) {
  return t28 && t28.pattern || null;
}
function u29(e33, a35, n26) {
  return r(e33) ? "none" === e33.style || "solid" === e33.style ? ("none" === e33.style && (a35.color = r6(0, 0, 0, 0), a35.transparent = true), new f18(a35)) : (a35.style = m15(e33.style), a35.draped = n26.isDraped, new S17(a35)) : new f18(a35);
}
function m15(t28) {
  switch (t28) {
    case "horizontal":
      return a23.Horizontal;
    case "vertical":
      return a23.Vertical;
    case "cross":
      return a23.Cross;
    case "forward-diagonal":
      return a23.ForwardDiagonal;
    case "backward-diagonal":
      return a23.BackwardDiagonal;
    case "diagonal-cross":
      return a23.DiagonalCross;
    default:
      return;
  }
}
function f26(t28) {
  return t28.material instanceof S17 && !t28.material.parameters.draped;
}
function g18(t28, r30) {
  if (f26(t28)) {
    const e33 = t28.vertexAttributes.get(O4.POSITION).data, o24 = t28.getMutableAttribute(O4.UVMAPSPACE).data, i19 = t28.getMutableAttribute(O4.BOUNDINGRECT).data;
    E18(o24, i19, e33, r30);
  }
}
function d22(t28, r30, a35, n26, o24) {
  const i19 = p16(t28, r30, a35, n26, o24), s26 = t28.stageObject.geometries;
  for (const e33 of s26) g18(e33, o24);
  return i19;
}

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DPolygonFillSymbolLayer.js
var k9 = ["polyline", "polygon", "extent"];
var z8 = class _z extends y7 {
  constructor(e33, t28, i19, r30) {
    super(e33, t28, i19, r30), this._needsUV = false, this._hasOutline = false;
  }
  async doLoad() {
  }
  _ensureMaterials() {
    this._ensureFillMaterial(), this._ensureOutlineMaterial();
  }
  _ensureFillMaterial() {
    if (r(this._material)) return;
    const e33 = x(this.symbolLayer, "material", "color"), r30 = this._getCombinedOpacityAndColor(e33);
    this._material = l29(this.symbolLayer, { color: r30, transparent: r30[3] < 1 || this.needsDrivenTransparentPass, polygonOffset: false, hasVertexColors: true, writeLinearDepth: true, hasSlicePlane: this._context.slicePlaneEnabled }, { isDraped: this.draped }), this._needsUV = this._material instanceof S17, this._context.stage.add(this._material);
  }
  _ensureOutlineMaterial() {
    const e33 = this.symbolLayer.outline;
    if (r(this._outlineMaterial) || !this._isValidOutline(e33)) return;
    this._hasOutline = true;
    const i19 = (t28) => {
      const i20 = a22(e33.pattern);
      return new z3({ width: t28, color: this._getOutlineColor(), hasPolygonOffset: true, hasSlicePlane: this._context.slicePlaneEnabled, isClosed: true, stipplePattern: i20, stippleScaleWithLineWidth: true, cap: n20(e33.patternCap || "butt") });
    };
    this._outlineMaterial = i19(u6(e33.size)), this._context.stage.add(this._outlineMaterial);
  }
  _isValidOutline(e33) {
    return r(e33) && null != e33.size && e33.size > 0 && r(e33.color) && (t(e33.pattern) || "style" !== e33.pattern.type || "none" !== e33.pattern.style);
  }
  destroy() {
    super.destroy(), this._context.stage.remove(this._material), this._material = null, this._context.stage.remove(this._outlineMaterial), this._outlineMaterial = null;
  }
  createGraphics3DGraphic(e33) {
    const t28 = e33.graphic;
    if (!this._validateGeometry(t28.geometry, k9, this.symbolLayer.type)) return null;
    const i19 = this._getVertexOpacityAndColor(e33.renderingInfo, 255), r30 = this.setGraphicElevationContext(t28, new h15());
    return this.ensureDrapedStatus("on-the-ground" === r30.mode), this._ensureMaterials(), this.draped ? this._createAsOverlay(t28, i19) : this._createAs3DShape(t28, i19, r30);
  }
  layerOpacityChanged() {
    if (r(this._material)) {
      const e33 = this._material.parameters.color, t28 = x(this.symbolLayer, "material", "color"), r30 = this._getCombinedOpacity(t28);
      this._material.setParameters({ color: [e33[0], e33[1], e33[2], r30], transparent: r30 < 1 || this.needsDrivenTransparentPass });
    }
    if (r(this._outlineMaterial)) {
      const e33 = this._outlineMaterial.parameters.color;
      this._outlineMaterial.setParameters({ color: [e33[0], e33[1], e33[2], this._getOutlineOpacity()] });
    }
  }
  layerElevationInfoChanged(e33, t28, i19) {
    const r30 = this._elevationContext.mode, n26 = m7(_z.elevationModeChangeTypes, i19, r30);
    if (n26 !== j8.UPDATE) return n26;
    const o24 = p12(r30);
    return this.updateGraphics3DGraphicElevationInfo(e33, t28, () => o24);
  }
  slicePlaneEnabledChanged() {
    if (r(this._material) && this._material.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled }), r(this._outlineMaterial)) {
      const e33 = { hasSlicePlane: this._context.slicePlaneEnabled };
      this._outlineMaterial.setParameters(e33);
    }
    return true;
  }
  physicalBasedRenderingChanged() {
    return true;
  }
  pixelRatioChanged() {
    return true;
  }
  skipHighSymbolLodsChanged() {
    return true;
  }
  _createAs3DShape(e33, t28, i19) {
    var _a;
    const n26 = c24(e33.geometry);
    if (t(n26)) return null;
    const o24 = c25(n26, this._context.elevationProvider, this._context.renderCoordsHelper, i19), s26 = new q12(o24, t28, this._context.layer.uid, e33.uid), a35 = s26.renderData.position.length / 3;
    if (this._needsUV && (s26.uvMapSpace = n14(4 * a35, true), s26.boundingRect = n10(9 * a35, true), E18(s26.uvMapSpace, s26.boundingRect, s26.renderData.position, this._context.renderCoordsHelper)), s26.objectAndLayerIdColor = (_a = this._context.stage.renderView) == null ? void 0 : _a.getObjectAndLayerIdColor(s26), this._createAs3DShapeFill(s26), this._hasOutline && this._createAs3DShapeOutline(s26), this._logGeometryCreationWarnings(s26.renderData, n26.rings, "rings", "FillSymbol3DLayer"), 0 === s26.outGeometries.length) return null;
    const l34 = new x7({ geometries: s26.outGeometries, castShadow: false, metadata: { layerUid: this._context.layer.uid, graphicUid: e33.uid } }), c34 = new S8(this, l34, s26.outGeometries, null, null, d22, i19);
    return c34.alignedSampledElevation = s26.renderData.sampledElevation, c34.needsElevationUpdates = p12(i19.mode), c34;
  }
  _createAs3DShapeFill(e33) {
    const i19 = e33.renderData.polygons;
    for (const { position: r30, mapPositions: o24, holeIndices: a35, index: l34, count: u32 } of i19) {
      if (r(this._context.clippingExtent) && (S(N6), M3(N6, o24), !Y(N6, this._context.clippingExtent))) continue;
      const i20 = r12(o24, a35, 3);
      if (0 === i20.length) continue;
      const d26 = s18({ material: e2(this._material), indices: i20, mapPositions: o24, attributeData: { position: r30, color: e33.color, uvMapSpace: this._needsUV ? a20(e33.uvMapSpace, 4 * l34, 4 * u32) : null, boundingRect: this._needsUV ? a11(e33.boundingRect, 9 * l34, 9 * u32) : null, objectAndLayerIdColor: e33.objectAndLayerIdColor } });
      e33.outGeometries.push(d26);
    }
  }
  _createAs3DShapeOutline(e33) {
    if (!this._hasOutline) return;
    const i19 = e33.renderData.outlines;
    for (let r30 = 0; r30 < i19.length; ++r30) {
      const { mapPositions: o24, position: s26 } = i19[r30];
      if (r(this._context.clippingExtent) && (S(N6), M3(N6, o24), !Y(N6, this._context.clippingExtent))) continue;
      const a35 = b11(e2(this._outlineMaterial), { overlayInfo: null, removeDuplicateStartEnd: true, mapPositions: o24, attributeData: { position: s26 } }, e33.objectAndLayerIdColor), l34 = a35.vertexAttributes.get(O4.POSITION);
      l34.data === s26 && (l34.data = t11(s26)), e33.outGeometries.push(a35);
    }
  }
  _createAsOverlay(e33, i19) {
    var _a;
    const o24 = c24(e33.geometry);
    if (t(o24)) return null;
    e2(this._material).renderPriority = this._renderPriority + this._renderPriorityStep / 2, r(this._outlineMaterial) && (this._outlineMaterial.renderPriority = this._renderPriority);
    const s26 = l25(o24, this._context.overlaySR), a35 = new J4(s26, i19, this._context.layer.uid, e33.uid), l34 = a35.renderData.position.length / 3;
    return this._needsUV && (a35.uvMapSpace = n14(4 * l34, true), T8(a35.uvMapSpace, a35.renderData.position, this._context.overlaySR, this._context.graphicsCoreOwner.view.state.viewingMode)), a35.outBoundingBox = S(), a35.objectAndLayerIdColor = (_a = this._context.stage.renderView) == null ? void 0 : _a.getObjectAndLayerIdColor(a35), this._createAsOverlayFill(a35), this._hasOutline && this._createAsOverlayOutline(a35), this._logGeometryCreationWarnings(a35.renderData, o24.rings, "rings", "FillSymbol3DLayer"), 0 === a35.outGeometries.length ? null : new l26(this, a35.outGeometries, a35.outBoundingBox, this._context.drapeSourceRenderer);
  }
  _createAsOverlayFill(e33) {
    const t28 = e33.renderData.polygons;
    for (const { position: i19, holeIndices: r30, index: o24, count: l34 } of t28) {
      const t29 = S(N6);
      if (M3(t29, i19), !Y(t29, this._context.clippingExtent)) continue;
      const d26 = r12(i19, r30, 3);
      if (0 === d26.length) continue;
      f4(e33.outBoundingBox, t29);
      const m18 = s18({ material: e2(this._material), indices: d26, attributeData: { position: i19, color: e33.color, uvMapSpace: this._needsUV ? a20(e33.uvMapSpace, 4 * o24, 4 * l34) : null, objectAndLayerIdColor: e33.objectAndLayerIdColor } }), _21 = new h14(m18, e33);
      r5(_21.boundingSphere, 0.5 * (t29[0] + t29[3]), 0.5 * (t29[1] + t29[4]), 0, 0.5 * Math.sqrt((t29[3] - t29[0]) * (t29[3] - t29[0]) + (t29[4] - t29[1]) * (t29[4] - t29[1]))), e33.outGeometries.push(_21);
    }
  }
  _createAsOverlayOutline(e33) {
    if (!this._hasOutline) return;
    const t28 = e33.renderData.outlines;
    for (let i19 = 0; i19 < t28.length; ++i19) {
      const { position: r30 } = t28[i19];
      if (S(N6), M3(N6, r30), !Y(N6, this._context.clippingExtent)) continue;
      f4(e33.outBoundingBox, N6);
      const o24 = b11(e2(this._outlineMaterial), { overlayInfo: { spatialReference: this._context.overlaySR, renderCoordsHelper: this._context.renderCoordsHelper }, removeDuplicateStartEnd: true, attributeData: { position: r30 } }, e33.objectAndLayerIdColor), s26 = new h14(o24, e33), l34 = N6;
      r5(s26.boundingSphere, 0.5 * (l34[0] + l34[3]), 0.5 * (l34[1] + l34[4]), 0, 0.5 * Math.sqrt((l34[3] - l34[0]) * (l34[3] - l34[0]) + (l34[4] - l34[1]) * (l34[4] - l34[1]))), e33.outGeometries.push(s26);
    }
  }
  _getOutlineOpacity() {
    const e33 = x(this.symbolLayer, "outline", "color");
    return (this.draped ? 1 : this._getLayerOpacity()) * (r(e33) ? e33.a : 0);
  }
  _getOutlineColor() {
    const r30 = x(this.symbolLayer, "outline", "color"), n26 = this._getOutlineOpacity();
    return B2(r(r30) ? l11.toUnitRGB(r30) : null, n26);
  }
  test() {
    return { ...super.test(), createAsOverlay: (e33, t28) => this._createAsOverlay(e33, t28), createAs3DShape: (e33, t28, i19) => this._createAs3DShape(e33, t28, i19) };
  }
};
z8.elevationModeChangeTypes = { definedChanged: j8.RECREATE, staysOnTheGround: j8.NONE, onTheGroundChanged: j8.RECREATE };
var N6 = a7();
var q12 = class extends p19 {
  constructor(e33, t28, i19, r30) {
    super(e33, i19, r30), this.color = t28;
  }
};
var J4 = class extends p19 {
  constructor(e33, t28, i19, r30) {
    super(e33, i19, r30), this.color = t28;
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/CreateLabelParameters.js
var t25 = class {
  constructor(t28 = null, e33 = "center", s26 = "center", i19 = null, l34 = [0, 0, 0], n26 = 0, h25 = [0, 0, 0, 1], r30 = [0, 0], c34 = "world", a35 = 0, f27 = 0) {
    this.verticalOffset = t28, this.horizontalPlacement = e33, this.verticalPlacement = s26, this.text = i19, this.translation = l34, this.elevationOffset = n26, this.centerOffset = h25, this.screenOffset = r30, this.centerOffsetUnits = c34, this.displayWidth = a35, this.displayHeight = f27;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/TextRenderParameters.js
var a33 = class _a {
  constructor(o24) {
    this.definition = o24, this.key = JSON.stringify(o24), this.haloSize = Math.round(o24.halo.size), this.textStyle = this._colorToRGBA(o24.color), this.haloStyle = this._colorToRGB(o24.halo.color), this.backgroundStyle = 0 !== o24.background.color[3] ? this._colorToRGBA(o24.background.color) : null;
  }
  fontString(o24) {
    const r30 = this.definition.font;
    return `${r30.style} ${r30.weight} ${o24}px ${r30.family}, sans-serif`;
  }
  _colorToRGB(o24) {
    return `rgb(${o24.slice(0, 3).map((o25) => Math.floor(255 * o25)).toString()})`;
  }
  _colorToRGBA(o24) {
    return `rgba(${o24.slice(0, 3).map((o25) => Math.floor(255 * o25)).toString()},${o24[3]})`;
  }
  static async fromSymbol(c34, g23) {
    const f27 = x(c34, "material", "color"), d26 = g(f27, l7, l11.toUnitRGBA), h25 = g(c34.size, 12, u6), m18 = c34.lineHeight, u32 = r(c34.background) ? e2(l11.toUnitRGBA(c34.background.color)) : l7, b24 = { family: g(c34.font, "sans-serif", (o24) => o24.family), decoration: g(c34.font, "none", (o24) => o24.decoration), weight: g(c34.font, "normal", (o24) => o24.weight), style: g(c34.font, "normal", (o24) => o24.style) }, y14 = c34.halo, p23 = r(y14) && r(y14.color) && y14.size > 0 ? { size: u6(y14.size), color: l11.toUnitRGBA(y14.color) } : { size: 0, color: l7 }, S19 = new _a({ color: d26, size: h25, background: { color: u32, padding: r(c34.background) ? [0.65 * h25, 0.5 * h25] : [0, 0], borderRadius: r(c34.background) ? h25 * (6 / 16) : 0 }, lineSpacingFactor: m18, font: b24, halo: p23, pixelRatio: g23 }), R12 = S19.fontString(h25);
    try {
      await document.fonts.load(R12);
    } catch (k13) {
      s.getLogger("esri.views.3d.webgl-engine.lib.TextRenderParameters").warnOnce(`Failed to preload font '${R12}'. Some text symbology may be rendered using the default browser font.`);
    }
    return S19;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/TextTextureFactory.js
var i16 = class {
  constructor(e33, t28, n26) {
    this._renderer = new n19(e33, t28, n26);
  }
  get key() {
    return this._renderer.key;
  }
  get baselineAnchorY() {
    return 1 - this._renderer.firstRenderedBaselinePosition / this._renderer.renderedHeight;
  }
  get displayWidth() {
    return this._renderer.displayWidth;
  }
  get displayHeight() {
    return this._renderer.displayHeight;
  }
  create() {
    const r30 = o19(d23, this._renderer.renderedWidth, this._renderer.renderedHeight), i19 = r30.getContext("2d");
    return i19.save(), this._renderer.render(i19, 0, 0), i19.restore(), new G3(r30, { wrap: { s: D3.CLAMP_TO_EDGE, t: D3.CLAMP_TO_EDGE }, noUnpackFlip: false, mipmap: true, preMultiplyAlpha: true, powerOfTwoResizeMode: D4.PAD });
  }
};
var d23 = { canvas: null };

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DTextSymbolLayer.js
var U7 = [0, 0, 1];
var D9 = class extends y7 {
  constructor(e33, t28, r30, s26) {
    super(e33, t28, r30, s26), this._elevationOptions = { supportsOffsetAdjustment: true, supportsOnTheGround: false }, this.ensureDrapedStatus(false);
  }
  async doLoad() {
    if (!this._drivenProperties.size) {
      const t28 = S5(this.symbolLayer.size);
      if (t28) throw new s2("graphics3dtextsymbollayer:invalid-size", t28);
    }
    await this._createTextRenderParameters();
  }
  async _createTextRenderParameters() {
    const e33 = this._context.graphicsCoreOwner.view.state.rasterPixelRatio;
    this._textRenderParameters = await a33.fromSymbol(this.symbolLayer, e33);
  }
  destroy() {
    super.destroy();
  }
  createGraphics3DGraphic(e33) {
    const s26 = e33.graphic, n26 = u21(s26.geometry);
    if (t(n26)) return this.logger.warn(`unsupported geometry type for text symbol: ${s26.geometry.type}`), null;
    const i19 = this.symbolLayer.text;
    if (t(i19) || "" === i19) return null;
    const o24 = i6(this.symbol) && this.symbol.hasVisibleVerticalOffset() ? this.symbol.verticalOffset : null;
    if (r(o24) && !l12(this.symbolLayer)) return this.logger.errorOncePerTick(`Callouts and vertical offset on text symbols are currently only supported with 'center' horizontal alignment (not with '${this.symbolLayer.horizontalAlignment}' alignment)`), null;
    const a35 = new t25(o24, this.symbolLayer.horizontalAlignment, f23(this.symbolLayer.verticalAlignment));
    return this._createAs3DShape(s26, n26, i19, a35);
  }
  createLabel(e33, r30, s26, n26) {
    const i19 = e33.graphic, o24 = u21(i19.geometry);
    if (t(o24)) return this.logger.warn(`unsupported geometry type for label: ${i19.geometry.type}`), null;
    const a35 = r30.text;
    return !a35 || /^\s+$/.test(a35) ? null : this._createAs3DShape(i19, o24, a35, r30, s26, n26);
  }
  setGraphicElevationContext(e33, t28, r30 = 0) {
    const s26 = super.setGraphicElevationContext(e33, t28);
    return s26.addOffsetRenderUnits(r30), s26;
  }
  layerOpacityChanged() {
    return this.logger.warn("layer opacity change not yet implemented in Graphics3DTextSymbolLayer"), false;
  }
  layerElevationInfoChanged(e33, t28) {
    return A11(e33, t28, (e34, t29) => {
      this.updateGraphicElevationContext(t29, e34);
    }), j8.UPDATE;
  }
  slicePlaneEnabledChanged(e33, t28) {
    return A11(e33, t28, (e34) => {
      for (const t29 of e34.stageObject.geometries) t29.material.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled });
    }), true;
  }
  physicalBasedRenderingChanged() {
    return true;
  }
  pixelRatioChanged() {
    return false;
  }
  skipHighSymbolLodsChanged() {
    return true;
  }
  updateGraphicElevationContext(e33, t28) {
    const s26 = t28.elevationContext;
    this.setGraphicElevationContext(e33, s26, r(t28.metadata) ? t28.metadata.elevationOffset : 0), t28.needsElevationUpdates = p12(s26.mode) || "absolute-height" === s26.mode;
  }
  _defaultElevationInfoNoZ() {
    return W4;
  }
  _createAs3DShape(e33, m18, h25, d26, f27, b24) {
    const v21 = this.setGraphicElevationContext(e33, new h15(), d26.elevationOffset), P10 = "polyline" === x(e33.geometry, "type"), j12 = e33.uid;
    let w12 = null, S19 = null;
    if (t(b24)) {
      const e34 = c27(d26.horizontalPlacement);
      w12 = new i16(h25, e34, this._textRenderParameters);
      let s26 = null;
      if (r(this._context.sharedResources.textures)) {
        S19 = this._context.sharedResources.textures.fromData(w12.key, () => e2(w12).create(), () => {
          r(s26) && s26.release();
        });
        const e35 = this._context.stage.renderView.textureRepository.acquire(S19.texture.id);
        if (t(e35) || C(e35)) return S19.release(), null;
        s26 = e35;
      }
    }
    const _21 = T9(w12, d26), z10 = { occlusionTest: true, screenOffset: d26.screenOffset, anchorPosition: _21, polygonOffset: true, color: [1, 1, 1, 1], centerOffsetUnits: d26.centerOffsetUnits, drawInSecondSlot: true };
    if (r(b24) ? z10.textureId = b24.id : r(S19) && (z10.textureId = S19.texture.id), r(d26.verticalOffset)) {
      const { screenLength: e34, minWorldLength: t28, maxWorldLength: s26 } = d26.verticalOffset;
      z10.verticalOffset = { screenLength: u6(e34), minWorldLength: t28 || 0, maxWorldLength: r(s26) ? s26 : 1 / 0 };
    }
    if (this._context.screenSizePerspectiveEnabled) {
      const { screenSizePerspectiveSettings: e34, screenSizePerspectiveSettingsLabels: t28 } = this._context.sharedResources;
      z10.screenSizePerspective = t28.overridePadding(this._textRenderParameters.haloSize + this._textRenderParameters.definition.background.padding[0]), z10.screenSizePerspectiveAlignment = e34;
    }
    let D10;
    if (P10 && (z10.shaderPolygonOffset = 1e-4), z10.hasSlicePlane = this._context.slicePlaneEnabled, r(f27)) {
      const e34 = JSON.stringify(z10);
      D10 = f27.get(e34), t(D10) && (D10 = new $3(z10), f27.add(e34, D10));
    } else D10 = new $3(z10);
    const A13 = d26.translation, W6 = r(w12) ? r13(w12.displayWidth, w12.displayHeight) : f11, k13 = d26.centerOffset, I13 = ot2(D10, U7, A13, null, W6, k13, [0, 0], null), H7 = p18(this._context, m18, I13, v21, j12);
    if (t(H7)) return null;
    const $7 = new S8(this, H7.object, [I13], t(f27) ? [D10] : null, S19, d13, v21);
    $7.alignedSampledElevation = H7.sampledElevation, $7.needsElevationUpdates = p12(v21.mode) || "absolute-height" === v21.mode;
    const { displayWidth: V11, displayHeight: M13 } = r(w12) ? w12 : d26;
    $7.getScreenSize = (e34 = n13()) => (e34[0] = V11, e34[1] = M13, e34);
    const N9 = new e21(d26.elevationOffset, h25);
    return $7.metadata = N9, m12($7, m18, this._context.elevationProvider), $7;
  }
};
function A11(e33, t28, s26) {
  e33 && e33.forEach((e34) => {
    const n26 = t28(e34);
    r(n26) && s26(n26, e34.graphic);
  });
}
function T9(e33, t28) {
  if ("baseline" === t28.verticalPlacement) {
    const s27 = r22[t28.horizontalPlacement], n26 = r(e33) ? e33.baselineAnchorY : 0;
    return r13(s27, n26);
  }
  const s26 = i14(t28.horizontalPlacement, t28.verticalPlacement);
  return o20[s26];
}
var W4 = { mode: "relative-to-ground", offset: 0 };

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/internal/waterMaterialUtils.js
var e29 = { "calm-small": { waveStrength: 5e-3, perturbationStrength: 0.02, textureRepeat: 12, waveVelocity: 0.01 }, "rippled-small": { waveStrength: 0.02, perturbationStrength: 0.09, textureRepeat: 32, waveVelocity: 0.07 }, "slight-small": { waveStrength: 0.05, perturbationStrength: 0.07, textureRepeat: 28, waveVelocity: 0.1 }, "moderate-small": { waveStrength: 0.075, perturbationStrength: 0.07, textureRepeat: 24, waveVelocity: 0.1 }, "calm-medium": { waveStrength: 3125e-6, perturbationStrength: 0.01, textureRepeat: 8, waveVelocity: 0.02 }, "rippled-medium": { waveStrength: 0.035, perturbationStrength: 0.015, textureRepeat: 12, waveVelocity: 0.07 }, "slight-medium": { waveStrength: 0.06, perturbationStrength: 0.015, textureRepeat: 8, waveVelocity: 0.12 }, "moderate-medium": { waveStrength: 0.09, perturbationStrength: 0.03, textureRepeat: 4, waveVelocity: 0.12 }, "calm-large": { waveStrength: 0.01, perturbationStrength: 0, textureRepeat: 4, waveVelocity: 0.05 }, "rippled-large": { waveStrength: 0.025, perturbationStrength: 0.01, textureRepeat: 8, waveVelocity: 0.11 }, "slight-large": { waveStrength: 0.06, perturbationStrength: 0.02, textureRepeat: 3, waveVelocity: 0.13 }, "moderate-large": { waveStrength: 0.14, perturbationStrength: 0.03, textureRepeat: 2, waveVelocity: 0.15 } };

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DWaterSymbolLayer.js
var W5 = ["polyline", "polygon", "extent"];
var k10 = class _k extends y7 {
  constructor(e33, t28, r30, o24) {
    super(e33, t28, r30, o24);
  }
  async doLoad() {
  }
  destroy() {
    super.destroy(), this._context.stage.remove(this._material), this._material = null;
  }
  createGraphics3DGraphic(e33) {
    const t28 = e33.graphic;
    if (!this._validateGeometry(t28.geometry, W5, this.symbolLayer.type)) return null;
    const r30 = this.setGraphicElevationContext(t28, new h15());
    return this.ensureDrapedStatus("on-the-ground" === r30.mode), this.ensureMaterial(), this.draped ? this._createAsOverlay(t28) : this._createAs3DShape(t28, r30, t28.uid);
  }
  ensureMaterial() {
    if (r(this._material)) return;
    const r30 = new f15(), o24 = this.symbolLayer.color;
    r(o24) && (r30.color = l11.toUnitRGBA(o24));
    const i19 = this._getCombinedOpacity(o24, { hasIntrinsicColor: true });
    r30.color = [r30.color[0], r30.color[1], r30.color[2], i19], r30.transparent = i19 < 1 || this.needsDrivenTransparentPass, r30.waveDirection = r(this.symbolLayer.waveDirection) ? _k.headingVectorFromAngle(this.symbolLayer.waveDirection) : r13(0, 0);
    const n26 = this.symbolLayer.waveStrength + "-" + this.symbolLayer.waterbodySize, s26 = e29[n26];
    r30.waveStrength = s26.waveStrength, r30.waveTextureRepeat = s26.textureRepeat, r30.waveVelocity = s26.waveVelocity, r30.flowStrength = s26.perturbationStrength, r30.hasSlicePlane = this._context.slicePlaneEnabled, r30.isDraped = this.draped, this._material = new u14(r30), this._context.stage.add(this._material);
  }
  layerOpacityChanged() {
    if (t(this._material)) return;
    const e33 = this._material.parameters.color, t28 = this._getCombinedOpacity(this.symbolLayer.color, { hasIntrinsicColor: true }), o24 = t28 < 1 || this.needsDrivenTransparentPass;
    this._material.setParameters({ color: [e33[0], e33[1], e33[2], t28], transparent: o24 });
  }
  layerElevationInfoChanged(e33, t28, r30) {
    const o24 = this._elevationContext.mode, i19 = m7(_k.elevationModeChangeTypes, r30, o24);
    if (i19 !== j8.UPDATE) return i19;
    const n26 = p12(o24);
    return this.updateGraphics3DGraphicElevationInfo(e33, t28, () => n26);
  }
  slicePlaneEnabledChanged() {
    return r(this._material) && this._material.setParameters({ hasSlicePlane: this._context.slicePlaneEnabled }), true;
  }
  physicalBasedRenderingChanged() {
    return true;
  }
  pixelRatioChanged() {
    return true;
  }
  skipHighSymbolLodsChanged() {
    return true;
  }
  _createAs3DShape(e33, t28, o24) {
    const i19 = c24(e33.geometry);
    if (t(i19)) return null;
    const n26 = c25(i19, this._context.elevationProvider, this._context.renderCoordsHelper, t28), s26 = n26.position.length / 3, a35 = n10(2 * s26);
    this._createUVCoordsFromVertices(a35, n26.mapPositions, s26, this._context.elevationProvider.spatialReference);
    const l34 = new q13(n26, a35);
    if (this._create3DShapeGeometries(l34), this._logGeometryCreationWarnings(l34.renderData, i19.rings, "rings", "WaterSymbol3DLayer"), 0 === l34.outGeometries.length) return null;
    const c34 = new x7({ geometries: l34.outGeometries, castShadow: false, metadata: { layerUid: this._context.layer.uid, graphicUid: o24 } }), h25 = new S8(this, c34, l34.outGeometries, null, null, p16, t28);
    return h25.alignedSampledElevation = l34.renderData.sampledElevation, h25.needsElevationUpdates = p12(t28.mode), h25;
  }
  _createUVCoordsFromVertices(e33, t28, r30, o24) {
    const n26 = $(o24);
    D(F9);
    for (let i19 = 0; i19 < r30; i19++) r8(H5, t28[3 * i19], t28[3 * i19 + 1]), m(F9, H5);
    l4(F9, F9, n26);
    const a35 = F9[0] % _k.unitSizeOfTexture, l34 = F9[1] % _k.unitSizeOfTexture;
    z9[0] = F9[0] - a35, z9[1] = F9[1] - l34;
    for (let i19 = 0; i19 < r30; i19++) e33[2 * i19] = (t28[3 * i19] * n26 - z9[0]) / _k.unitSizeOfTexture, e33[2 * i19 + 1] = (t28[3 * i19 + 1] * n26 - z9[1]) / _k.unitSizeOfTexture;
  }
  _create3DShapeGeometries(e33) {
    const r30 = e33.renderData.polygons, i19 = e33.uvCoords;
    for (const { count: s26, index: a35, position: l34, mapPositions: c34, holeIndices: h25 } of r30) {
      if (r(this._context.clippingExtent) && (S(N7), M3(N7, c34), !Y(N7, this._context.clippingExtent))) continue;
      const r31 = r12(c34, h25, 3);
      if (0 === r31.length) continue;
      const m18 = a11(i19, 2 * a35, 2 * s26), p23 = u22({ material: e2(this._material), indices: r31, mapPositions: c34, attributeData: { position: l34, uv0: m18 } });
      e33.outGeometries.push(p23);
    }
  }
  _createAsOverlay(e33) {
    const t28 = c24(e33.geometry);
    if (t(t28)) return null;
    e2(this._material).renderPriority = this._renderPriority;
    const i19 = l25(t28, this._context.overlaySR), n26 = i19.position.length / 3, s26 = n10(2 * n26);
    this._createUVCoordsFromVertices(s26, i19.position, n26, this._context.overlaySR);
    const a35 = new J5(i19, s26, this._context.layer.uid, e33.uid);
    return a35.outBoundingBox = S(), this._createAsOverlayWater(a35), this._logGeometryCreationWarnings(a35.renderData, t28.rings, "rings", "WaterSymbol3DLayer"), 0 === a35.outGeometries.length ? null : new l26(this, a35.outGeometries, a35.outBoundingBox, this._context.drapeSourceRenderer);
  }
  _createAsOverlayWater(e33) {
    const t28 = e33.uvCoords, r30 = e33.renderData.polygons;
    for (const { position: i19, holeIndices: s26, index: a35, count: l34 } of r30) {
      if (S(N7), M3(N7, i19), !Y(N7, this._context.clippingExtent)) continue;
      f4(e33.outBoundingBox, N7);
      const r31 = r12(i19, s26, 3);
      if (0 === r31.length) continue;
      const c34 = a11(t28, 2 * a35, 2 * l34), m18 = u22({ material: e2(this._material), indices: r31, attributeData: { position: i19, uv0: c34 } }), p23 = new h14(m18, e33), f27 = N7;
      r5(p23.boundingSphere, 0.5 * (f27[0] + f27[3]), 0.5 * (f27[1] + f27[4]), 0, 0.5 * Math.sqrt((f27[3] - f27[0]) * (f27[3] - f27[0]) + (f27[4] - f27[1]) * (f27[4] - f27[1]))), e33.outGeometries.push(p23);
    }
  }
  static headingVectorFromAngle(e33) {
    const t28 = n13(), r30 = r3(e33);
    return t28[0] = Math.sin(r30), t28[1] = Math.cos(r30), t28;
  }
  test() {
    return { ...super.test(), create3DShape: (e33) => this._createAs3DShape(e33.graphic, e33.elevationContext, e33.graphicUid), ensureMaterial: () => this.ensureMaterial() };
  }
};
k10.unitSizeOfTexture = 100, k10.elevationModeChangeTypes = { definedChanged: j8.RECREATE, staysOnTheGround: j8.NONE, onTheGroundChanged: j8.RECREATE };
var z9 = n13();
var F9 = u4();
var H5 = n13();
var N7 = a7();
var q13 = class extends p19 {
  constructor(e33, t28) {
    super(e33, null, null), this.uvCoords = t28;
  }
};
var J5 = class extends p19 {
  constructor(e33, t28, r30, o24) {
    super(e33, r30, o24), this.uvCoords = t28;
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DSymbolLayerFactory.js
function c32(o24, e33, t28, i19) {
  const m18 = h23[o24.type] && h23[o24.type][e33.type] || l30[e33.type];
  return m18 ? new m18(o24, e33, t28, i19) : (s.getLogger("esri.views.3d.layers.graphics.Graphics3DSymbolLayerFactory").error("GraphicsLayerFactory#make", `unknown symbol type ${e33.type}`), null);
}
var l30 = { icon: he2, object: ue3, line: q7, path: ct2, fill: z8, extrude: ee2, text: D9, water: k10 };
var h23 = { "mesh-3d": { fill: xe2 } };

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DSymbol.js
var p22 = class extends a28 {
  set symbol(e33) {
    this._symbol = e33, e33.symbolLayers.forEach((s26, r30) => {
      const o24 = this.symbolLayers[r30];
      r(o24) && (o24.symbol = e33, o24.symbolLayer = s26);
    });
  }
  get symbol() {
    return this._symbol;
  }
  constructor(e33, t28, s26) {
    super(t28.schedule), this._symbol = e33, this._context = t28, this._backgroundLayers = s26, this._destroyed = false, this.symbolLayers = new Array(), this.referenced = 0, this._extentPadding = 0;
  }
  async doLoad(s26) {
    let r30 = this._symbol.symbolLayers;
    this._extentPadding = 0, this._backgroundLayers && (r30 = this._backgroundLayers.concat(r30));
    const i19 = r30.length;
    for (; this.symbolLayers.length < r30.length; ) this.symbolLayers.push(null);
    this.symbolLayers.length = r30.length;
    const n26 = [];
    for (let e33 = 0; e33 < i19; e33++) {
      const t28 = r30.getItemAt(e33);
      if (false === t28.enabled) continue;
      d24.renderPriority = 1 - (1 + e33) / i19, d24.renderPriorityStep = 1 / i19, d24.ignoreDrivers = t28._ignoreDrivers;
      const a35 = c32(this.symbol, t28, this._context, d24), l34 = d(s26, () => {
        this.symbolLayers[e33] = null, a35.destroy();
      });
      l34 && n26.push(l34), this.symbolLayers[e33] = a35;
    }
    if (await c4(this.symbolLayers, async (e33, s27) => {
      if (r(e33)) try {
        await e33.load(), this._extentPadding += Math.max(this._extentPadding, e33.extentPadding);
      } catch {
        this.symbolLayers[s27] = null;
      }
    }), n26.forEach((e33) => e33.remove()), f(s26), this.symbolLayers.length && !this.symbolLayers.some((e33) => !!e33)) throw new Error();
  }
  getSymbolLayerSize(e33) {
    const s26 = this.symbolLayers[e33];
    return r(s26) ? s26.getCachedSize() : null;
  }
  get extentPadding() {
    return this._extentPadding;
  }
  get symbologySnappingSupported() {
    return this.symbolLayers.some((e33) => r(e33) && e33.queryForSnapping);
  }
  createGraphics3DGraphic(e33, s26) {
    const r30 = e33.graphic, o24 = new Array(this.symbolLayers.length);
    for (let i19 = 0; i19 < this.symbolLayers.length; i19++) {
      const s27 = this.symbolLayers[i19];
      o24[i19] = r(s27) ? s27.createGraphics3DGraphic(e33) : null;
    }
    const a35 = this._context.arcade || this._context.featureExpressionInfoContext && this._context.featureExpressionInfoContext.arcade && this._context.featureExpressionInfoContext.arcade.modules || null;
    return new V6(r30, s26 || this, o24, e33.layer, a35);
  }
  get complexity() {
    return n18(this.symbolLayers.map((e33) => x(e33, "complexity")));
  }
  globalPropertyChanged(e33, s26) {
    const r30 = this.symbolLayers.length;
    for (let o24 = 0; o24 < r30; o24++) {
      const r31 = this.symbolLayers[o24], a35 = (e34) => {
        const t28 = e34.layers[o24];
        return t28 instanceof S8 ? t28 : null;
      };
      if (r(r31) && !r31.globalPropertyChanged(e33, s26, a35)) return false;
    }
    return true;
  }
  applyRendererDiff(e33, s26) {
    return this.loadStatus !== e23.LOADED ? e22.Recreate_Symbol : this.symbolLayers.reduce((r30, o24) => r30 !== e22.Recreate_Symbol && r(o24) ? Math.min(r30, o24.applyRendererDiff(e33, s26)) : r30, e22.Fast_Update);
  }
  prepareSymbolPatch(e33) {
    if (this.loadStatus === e23.FAILED) return;
    if ("partial" !== e33.diff.type) return;
    const s26 = e33.diff.diff;
    if (!s26.symbolLayers || "partial" !== s26.symbolLayers.type) return;
    const o24 = s26.symbolLayers.diff;
    this.symbolLayers.forEach((s27, a35) => {
      if (t(s27)) return;
      const i19 = o24[a35];
      if (i19) {
        const r30 = { diff: i19, graphics3DGraphicPatches: [], symbolLayerStatePatches: [] };
        s27.prepareSymbolLayerPatch(r30), e33.symbolStatePatches.push(...r30.symbolLayerStatePatches), r30.graphics3DGraphicPatches.length && e33.graphics3DGraphicPatches.push((e34, s28) => {
          const o25 = e34.layers[a35];
          r(o25) && r30.graphics3DGraphicPatches.forEach((e35) => e35(o25, s28));
        });
      }
    });
  }
  updateGeometry(e33, t28) {
    for (let s26 = 0; s26 < this.symbolLayers.length; s26++) {
      const o24 = this.symbolLayers[s26];
      if (t(o24)) continue;
      const a35 = e33.layers[s26];
      if (t(a35) || !o24.updateGeometry(a35, t28)) return false;
    }
    return true;
  }
  onRemoveGraphic(e33) {
    for (let s26 = 0; s26 < this.symbolLayers.length; s26++) {
      const o24 = this.symbolLayers[s26];
      if (t(o24)) continue;
      const a35 = e33.layers[s26];
      r(a35) && o24.onRemoveGraphic(a35);
    }
  }
  getFastUpdateStatus() {
    let e33 = 0, t28 = 0, s26 = 0;
    return this.symbolLayers.forEach((o24) => {
      t(o24) || (o24.loadStatus === e23.LOADING ? e33++ : o24.isFastUpdatesEnabled() ? s26++ : t28++);
    }), { loading: e33, slow: t28, fast: s26 };
  }
  async queryForSnapping(e33, s26, r30, o24) {
    const i19 = this.symbolLayers.filter(r).filter((e34) => r(e34.queryForSnapping)).map((t28) => t28.queryForSnapping(e33, s26, r30, o24)), n26 = await Promise.all(i19);
    return f(o24), n26.flat();
  }
  destroy() {
    if (this.destroyed) console.error("Graphics3DSymbol.destroy called when already destroyed!");
    else {
      super.destroy();
      for (const e33 of this.symbolLayers) r(e33) && e33.destroy();
      this.symbolLayers.length = 0, this._destroyed = true;
    }
  }
  get destroyed() {
    return this._destroyed;
  }
};
var d24 = { renderPriority: 0, renderPriorityStep: 1, ignoreDrivers: false };

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DPointSymbol.js
var i17 = class extends p22 {
  constructor(r30, a35, o24) {
    super(r30, a35, o24), this._calloutSymbolLayer = null, this.symbol.hasVisibleCallout() && (this._calloutSymbolLayer = e25(this.symbol, a35));
  }
  async doLoad(a35) {
    var _a;
    const o24 = this._calloutSymbolLayer ? b2(this._calloutSymbolLayer.load()) : null;
    try {
      await super.doLoad(a35), f(a35);
    } catch (l34) {
      throw (_a = this._calloutSymbolLayer) == null ? void 0 : _a.abortLoad(), l34;
    }
    o24 && await o24;
  }
  destroy() {
    super.destroy(), this._calloutSymbolLayer = a(this._calloutSymbolLayer);
  }
  createGraphics3DGraphic(r30, a35) {
    const t28 = super.createGraphics3DGraphic(r30, a35);
    if (r(this._calloutSymbolLayer) && r(t28)) {
      const a36 = this._createCalloutGraphic(r30);
      r(a36) && t28.addAuxiliaryGraphic(a36);
    }
    return t28;
  }
  globalPropertyChanged(r30, a35) {
    return !!super.globalPropertyChanged(r30, a35) && (!this._calloutSymbolLayer || this._calloutSymbolLayer.globalPropertyChanged(r30, a35, (r31) => this._getCalloutGraphicLayer(r31)));
  }
  updateGeometry(r30, a35) {
    const o24 = super.updateGeometry(r30, a35);
    if (o24 && this._calloutSymbolLayer) {
      const o25 = this._getCalloutGraphicLayer(r30);
      if (o25) return this._calloutSymbolLayer.updateGeometry(o25, a35);
    }
    return o24;
  }
  _createCalloutGraphic(r30) {
    const a35 = r30.renderingInfo;
    return r30.renderingInfo = new D5(a35.renderer, a35.symbol), this._calloutSymbolLayer.createGraphics3DGraphic(r30);
  }
  _getCalloutGraphicLayer(r30) {
    for (const a35 of r30._auxiliaryLayers) if (a35.graphics3DSymbolLayer === this._calloutSymbolLayer) return a35;
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DSymbolFactory.js
function t26(t28, i19, n26) {
  return "point-3d" === t28.type ? new i17(t28, i19, n26) : new p22(t28, i19, n26);
}

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DWebStyleSymbol.js
var e30 = class extends a28 {
  constructor(t28, s26, r30) {
    super(s26), this.symbol = t28, this._convert = r30, this.symbologySnappingSupported = false, this.graphics3DSymbol = null, this.referenced = 0;
  }
  getSymbolLayerSize(s26) {
    return r(this.graphics3DSymbol) ? this.graphics3DSymbol.getSymbolLayerSize(s26) : null;
  }
  get symbolLayers() {
    return r(this.graphics3DSymbol) ? this.graphics3DSymbol.symbolLayers : [];
  }
  get extentPadding() {
    return r(this.graphics3DSymbol) ? this.graphics3DSymbol.extentPadding : 0;
  }
  async doLoad(s26) {
    const r30 = await this.symbol.fetchSymbol({ signal: s26 });
    r30.id = this.symbol.id, this.graphics3DSymbol = this._convert(r30), r(this.graphics3DSymbol) && await this.graphics3DSymbol.load();
  }
  createGraphics3DGraphic(s26) {
    return r(this.graphics3DSymbol) ? this.graphics3DSymbol.createGraphics3DGraphic(s26, this) : null;
  }
  get complexity() {
    return r(this.graphics3DSymbol) ? this.graphics3DSymbol.complexity : d15;
  }
  globalPropertyChanged(s26, r30) {
    return !!r(this.graphics3DSymbol) && this.graphics3DSymbol.globalPropertyChanged(s26, r30);
  }
  applyRendererDiff(r30, i19) {
    return r(this.graphics3DSymbol) ? this.graphics3DSymbol.applyRendererDiff(r30, i19) : e22.Recreate_Symbol;
  }
  prepareSymbolPatch(s26) {
    r(this.graphics3DSymbol) && this.graphics3DSymbol.prepareSymbolPatch(s26);
  }
  updateGeometry(s26, r30) {
    return !!r(this.graphics3DSymbol) && this.graphics3DSymbol.updateGeometry(s26, r30);
  }
  onRemoveGraphic() {
  }
  getFastUpdateStatus() {
    return r(this.graphics3DSymbol) ? this.graphics3DSymbol.getFastUpdateStatus() : { loading: 1, fast: 0, slow: 0 };
  }
  destroy() {
    r(this.graphics3DSymbol) && this.graphics3DSymbol.destroy(), this.graphics3DSymbol = void 0, super.destroy();
  }
  get destroyed() {
    return void 0 === this.graphics3DSymbol;
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/GraphicStateTracking.js
var s23 = class {
  constructor(t28) {
    this._graphicsCore = t28, this._idToState = /* @__PURE__ */ new Map(), this._states = /* @__PURE__ */ new Set();
    const i19 = t28.owner.layer && t28.owner.layer.objectIdField;
    i19 ? (this._getGraphicId = (t29) => R4(t29, i19), this._getGraphics3DGraphicById = (t29) => this._graphicsCore.getGraphics3DGraphicByObjectId(t29)) : (this._getGraphicId = (t29) => t29.uid, this._getGraphics3DGraphicById = (t29) => this._graphicsCore.getGraphics3DGraphicById(t29));
  }
  destroy() {
    this._idToState.clear(), this._states.forEach((t28, i19) => this.remove(i19));
  }
  add(t28) {
    const e33 = { remove: () => this.remove(t28) };
    if (this._states.has(t28)) return e33;
    const s26 = this._getGraphicId(t28.graphic), a35 = this._getGraphics3DGraphicById(s26);
    this._states.has(t28) || this._states.add(t28);
    return this._ensureStateList(s26).push(t28), t28.displaying = !!r(a35) && a35.isVisible(), t28.isDraped = !!r(a35) && a35.isDraped, t28.tracking = true, r(a35) && t28.emit("changed"), e33;
  }
  remove(i19) {
    if (this._states.has(i19)) {
      if (this._idToState.size) {
        const e33 = this._getGraphicId(i19.graphic), s26 = this._idToState.get(e33);
        s26 && (F(s26, i19), 0 === s26.length && this._idToState.delete(e33));
      }
      this._states.delete(i19), i19.tracking = false, i19.displaying = false;
    }
  }
  addGraphic(t28) {
    this._forEachState(t28, (i19) => {
      i19.displaying = t28.isVisible(), i19.isDraped = t28.isDraped, i19.emit("changed");
    });
  }
  removeGraphic(t28) {
    this._forEachState(t28, (t29) => {
      t29.displaying = false, t29.isDraped = false;
    });
  }
  updateGraphicGeometry(t28) {
    this._forEachState(t28, (t29) => t29.emit("changed"));
  }
  updateGraphicVisibility(t28) {
    this._forEachState(t28, (i19) => i19.displaying = t28.isVisible());
  }
  allGraphicsDeleted() {
    this._states.forEach((t28) => {
      t28.displaying = false;
    });
  }
  _ensureStateList(t28) {
    const i19 = this._idToState.get(t28);
    if (i19) return i19;
    const e33 = new Array();
    return this._idToState.set(t28, e33), e33;
  }
  _forEachState(t28, i19) {
    if (0 === this._states.size || 0 === this._idToState.size) return;
    const e33 = this._getGraphicId(t28.graphic), s26 = this._idToState.get(e33);
    null != s26 && s26.forEach(i19);
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/SpatialIndex2D.js
var c33 = class extends v2 {
  constructor(t28) {
    super(t28), this._index = new h7(9, has("esri-csp-restrictions") ? (t29) => ({ minX: t29.extent[0], minY: t29.extent[1], maxX: t29.extent[2], maxY: t29.extent[3] }) : [".extent[0]", ".extent[1]", ".extent[2]", ".extent[3]"]), this._missing = /* @__PURE__ */ new Set(), this._boundsByFeature = /* @__PURE__ */ new Map(), this.spatialReference = null, this.hasZ = null, this.hasM = null, this.objectIdField = null, this.updating = false;
  }
  setup(t28) {
    this._addMany(t28);
  }
  destroy() {
    this._missing.clear(), this._index = a(this._index), this._boundsByFeature.clear(), this._boundsByFeature = null;
  }
  update() {
    this._missing.size > 0 && (this._addMany(Array.from(this._missing.values())), this.updating = false, this._missing.clear());
  }
  get updatingRemaining() {
    return this._missing.size;
  }
  queryGraphicUIDsInExtent(t28, e33, s26) {
    !t(e33) && e33.equals(this.spatialReference) && (u30.minX = t28[0], u30.minY = t28[1], u30.maxX = t28[2], u30.maxY = t28[3], this.update(), this._index.search(u30, (t29) => s26(t29.graphic.uid)));
  }
  add(t28) {
    this._missing.add(t28), this.updating = true;
  }
  remove(t28) {
    if (this._missing.delete(t28)) return void (this.updating = this._missing.size > 0);
    this._index.remove(t28);
    const e33 = R4(t28.graphic, this._get("objectIdField"));
    null != e33 && this._boundsByFeature.delete(e33);
  }
  _addMany(t28) {
    if (0 === t28.length) return;
    const e33 = this._get("objectIdField");
    for (const s26 of t28) {
      s26.computeExtent(this.spatialReference);
      const t29 = R4(s26.graphic, e33);
      null != t29 && this._boundsByFeature.set(t29, s26.extent);
    }
    this._index.load(t28);
  }
  clear() {
    this._index.clear(), this._missing.clear(), this._boundsByFeature.clear(), this.updating = false;
  }
  forEachInBounds(t28, e33) {
    u30.minX = t28[0], u30.minY = t28[1], u30.maxX = t28[2], u30.maxY = t28[3], this.update(), this._index.search(u30, (t29) => {
      e33(t29);
    });
  }
  getBounds(t28, e33) {
    this.update();
    const s26 = this._boundsByFeature.get(t28);
    return s26 ? d2(e33, s26) : null;
  }
};
e([y2({ constructOnly: true })], c33.prototype, "spatialReference", void 0), e([y2({ constructOnly: true })], c33.prototype, "hasZ", void 0), e([y2({ constructOnly: true })], c33.prototype, "hasM", void 0), e([y2({ constructOnly: true })], c33.prototype, "objectIdField", void 0), e([y2()], c33.prototype, "updating", void 0), e([y2({ readOnly: true })], c33.prototype, "updatingRemaining", null), c33 = e([a2("esri.views.3d.layers.graphics.SpatialIndex2D")], c33);
var u30 = { minX: 0, minY: 0, maxX: 0, maxY: 0 };

// node_modules/@arcgis/core/views/3d/layers/support/StageLayerElevationProvider.js
var v18 = 1;
var b21 = class extends n4.EventedMixin(v2) {
  get spatialReference() {
    var _a;
    return (_a = this.view) == null ? void 0 : _a.spatialReference;
  }
  constructor(e33) {
    super(e33), this._elevationOffset = 0, this._layerHandes = new t3();
  }
  initialize() {
    this._renderCoordsHelper = this.view.renderCoordsHelper, this._intersectLayers = [this.stageLayer], this._intersector = x8(this.view.state.viewingMode), this._intersector.options.store = t18.MIN;
    const e33 = this._computeLayerExtent(this.spatialReference, this.stageLayer);
    this._zmin = e33[2], this._zmax = e33[5];
    const t28 = this.stageLayer.events;
    this._layerHandes.add([t28.on("layerObjectAdded", (e34) => this._objectChanged(e34.object)), t28.on("layerObjectRemoved", (e34) => this._objectChanged(e34.object)), t28.on("objectGeometryAdded", (e34) => this._objectChanged(e34.object)), t28.on("objectGeometryRemoved", (e34) => this._objectChanged(e34.object)), t28.on("objectGeometryUpdated", (e34) => this._objectChanged(e34.object)), t28.on("objectTransformation", (e34) => this._objectChanged(e34))]);
  }
  dispose() {
    this._layerHandes.destroy();
  }
  elevationInfoChanged() {
    const e33 = null != this.layer ? this.layer.elevationInfo : null;
    if (null != e33 && "on-the-ground" !== e33.mode) {
      const t28 = L(this.layer.spatialReference), o24 = r11(e33.unit ?? "meters");
      this._elevationOffset = l(e33.offset, 0) * o24 / t28;
    } else this._elevationOffset = 0;
  }
  getElevation(e33, t28, o24, r30) {
    if (R11[0] = e33, R11[1] = t28, R11[2] = o24, !this._renderCoordsHelper.toRenderCoords(R11, r30, R11)) return s.getLogger(this.declaredClass).error("could not project point for elevation alignment"), null;
    const n26 = this._elevationOffset, i19 = this._zmin + n26, a35 = this._zmax + n26;
    this._renderCoordsHelper.setAltitude(H6, a35, R11), this._renderCoordsHelper.setAltitude(L6, i19, R11);
    const c34 = (e34) => {
      var _a;
      return !!((_a = e34.metadata) == null ? void 0 : _a.isElevationSource);
    };
    return this._intersector.reset(H6, L6, null), this._intersector.intersect(this._intersectLayers, null, v18, null, c34), this._intersector.results.min.getIntersectionPoint(R11) ? this._renderCoordsHelper.getAltitude(R11) : null;
  }
  _objectChanged(e33) {
    var _a;
    const t28 = this.spatialReference;
    if (!((_a = e33.metadata) == null ? void 0 : _a.isElevationSource) || t(t28)) return;
    S(x17);
    const o24 = e33.metadata.lastValidElevationBB;
    o24.isEmpty() || this._expandExtent(t28, o24.min, o24.max, x17);
    const { min: r30, max: s26 } = e33.boundingVolumeWorldSpace;
    this._expandExtent(t28, r30, s26, x17), Z(x17, C10), this._zmin = Math.min(this._zmin, x17[2]), this._zmax = Math.max(this._zmax, x17[5]), E20.extent = C10, E20.spatialReference = t28, this.emit("elevation-change", E20), r4(o24.min, r30), r4(o24.max, s26);
  }
  _computeLayerExtent(e33, t28) {
    return S(x17), r(e33) && t28.objects.forAll((t29) => this._expandExtent(e33, t29.boundingVolumeWorldSpace.min, t29.boundingVolumeWorldSpace.max, x17)), x17;
  }
  _expandExtent(e33, t28, o24, r30) {
    for (let s26 = 0; s26 < 8; ++s26) R11[0] = 1 & s26 ? t28[0] : o24[0], R11[1] = 2 & s26 ? t28[1] : o24[1], R11[2] = 4 & s26 ? t28[2] : o24[2], this._renderCoordsHelper.fromRenderCoords(R11, R11, e33), c3(r30, R11);
    return r30;
  }
};
e([y2({ constructOnly: true })], b21.prototype, "layer", void 0), e([y2({ constructOnly: true })], b21.prototype, "stageLayer", void 0), e([y2({ constructOnly: true })], b21.prototype, "view", void 0), e([y2()], b21.prototype, "spatialReference", null), b21 = e([a2("esri.views.3d.layers.support.StageLayerElevationProvider")], b21);
var x17 = S();
var C10 = D();
var E20 = { spatialReference: null, extent: C10, context: "scene" };
var R11 = n5();
var H6 = n5();
var L6 = n5();

// node_modules/@arcgis/core/views/3d/support/extentUtils.js
function e31(l34, x18, e33) {
  if (t(l34) || t(e33)) return false;
  let u32 = true;
  return i18[0] = null != l34.xmin ? l34.xmin : 0, i18[1] = null != l34.ymin ? l34.ymin : 0, i18[2] = null != l34.zmin ? l34.zmin : 0, u32 = u32 && xn(i18, l34.spatialReference, 0, i18, e33, 0, 1), x18[0] = i18[0], x18[1] = i18[1], i18[0] = null != l34.xmax ? l34.xmax : 0, i18[1] = null != l34.ymax ? l34.ymax : 0, i18[2] = null != l34.zmax ? l34.zmax : 0, u32 = u32 && xn(i18, l34.spatialReference, 0, i18, e33, 0, 1), x18[2] = i18[0], x18[3] = i18[1], null == l34.xmin && (x18[0] = -1 / 0), null == l34.ymin && (x18[1] = -1 / 0), null == l34.xmax && (x18[2] = 1 / 0), null == l34.ymax && (x18[3] = 1 / 0), u32;
}
var i18 = n5();

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DCore.js
var Le;
var je2 = n5();
var Ve = a7();
var Fe = "esri.views.3d.layers.graphics.Graphics3DCore";
var Te2 = s.getLogger(Fe);
var ke = Le = class extends v2 {
  get _viewSpatialReference() {
    return this.owner.view.spatialReference;
  }
  get spatialIndex() {
    var _a;
    return this._spatialIndex || (this._spatialIndex = new c33({ objectIdField: (_a = this.owner.layer) == null ? void 0 : _a.objectIdField, spatialReference: this._viewSpatialReference, hasZ: !!this.hasZ, hasM: !!this.hasM }), this._spatialIndex.setup(Array.from(this.graphics3DGraphics.values()))), this._spatialIndex.update(), this._spatialIndex;
  }
  get numberOfGraphics() {
    return this._numberOfGraphics;
  }
  get effectiveUpdatePolicy() {
    return r(this.currentRenderer) && "dictionary" === this.currentRenderer.type ? C2.ASYNC : l(this._forcedUpdatePolicy, this.preferredUpdatePolicy);
  }
  get featureStore() {
    return this._featureStore;
  }
  get initializePromise() {
    return this._initializePromise;
  }
  get scaleVisibility() {
    return this._scaleVisibility;
  }
  get elevationAlignment() {
    return this._elevationAlignment;
  }
  get objectStates() {
    return this._objectStates;
  }
  get filterVisibility() {
    return this._filterVisibility;
  }
  get updating() {
    var _a;
    return !!(this._graphicsWaitingForSymbol.size > 0 || this.running || ((_a = this._elevationAlignment) == null ? void 0 : _a.updating) || r(this._scaleVisibility) && this._scaleVisibility.updating || r(this._filterVisibility) && this._filterVisibility.updating || this._rendererChangeAbortController || this._elevationInfoChangeAbortController || this._updatingPendingLoadedGraphicsChange || this._frameTask.updating || this._loadingSymbols > 0);
  }
  get running() {
    var _a;
    return this._pendingUpdates.size > 0 || !!((_a = this._spatialIndex) == null ? void 0 : _a.updating);
  }
  get suspendedOrOutsideOfView() {
    var _a;
    return this.owner.suspended || !!((_a = this.owner.suspendInfo) == null ? void 0 : _a.outsideOfView);
  }
  get updatingRemaining() {
    var _a, _b;
    return this.updating ? this._pendingUpdates.size + 0.1 * (((_a = this._spatialIndex) == null ? void 0 : _a.updatingRemaining) || 0) + 0.1 * (((_b = this._elevationAlignment) == null ? void 0 : _b.updatingRemaining) || 0) : 0;
  }
  get displayFeatureLimit() {
    const e33 = this.owner && this.owner.view && this.owner.view.qualitySettings, i19 = e33 ? e33.graphics3D.minTotalNumberOfFeatures : 0, t28 = e33 ? e33.graphics3D.maxTotalNumberOfFeatures : 0, r30 = e33 ? e33.graphics3D.maxTotalNumberOfPrimitives : 0, s26 = this.averageSymbolComplexity, a35 = Math.max(1, r(s26) ? s26.primitivesPerFeature : 1), n26 = r(s26) && s26.drawCallsPerFeature > 0 ? t28 / s26.drawCallsPerFeature * 0.3 : t28, o24 = Math.ceil(r30 / a35), l34 = Math.max(i19, Math.min(t28, o24, n26)), d26 = this._get("displayFeatureLimit");
    return d26 && d26.minimumTotalNumberOfFeatures === i19 && d26.maximumTotalNumberOfFeatures === t28 && d26.maximumTotalNumberOfPrimitives === r30 && d26.averageSymbolComplexity === s26 && d26.maximumNumberOfFeatures === l34 ? d26 : { minimumTotalNumberOfFeatures: i19, maximumTotalNumberOfFeatures: t28, maximumTotalNumberOfPrimitives: r30, averageSymbolComplexity: s26, maximumNumberOfFeatures: l34 };
  }
  get averageSymbolComplexity() {
    const e33 = l24(this._symbolComplexities), i19 = this._get("averageSymbolComplexity");
    return 0 === e33.numComplexities || r(i19) && (e33.estimated && (i19.primitivesPerFeature >= e33.primitivesPerFeature || i19.primitivesPerCoordinate >= e33.primitivesPerCoordinate || i19.drawCallsPerFeature >= e33.drawCallsPerFeature) || i19.primitivesPerFeature === e33.primitivesPerFeature && i19.primitivesPerCoordinate === e33.primitivesPerCoordinate && i19.drawCallsPerFeature === e33.drawCallsPerFeature) ? i19 : e33;
  }
  get usedMemory() {
    const e33 = r(this.averageSymbolComplexity) && this.labelsEnabled ? this.averageSymbolComplexity.memory.bytesPerFeatureLabel * this._numberOfGraphics : 0, i19 = this._getSymbolComplexitiesUsed().reduce((e34, i20) => e34 + i20.memory.resourceBytes, 0);
    return this._usedMemory + e33 + i19;
  }
  get usedMemoryPerGraphic() {
    if (this._usedMemory && this._numberOfGraphics) {
      const e33 = this._numberOfGraphics / (this._numberOfGraphics + Math.max(this._pendingAdds, this._pendingRemoves));
      return this._usedMemory / this._numberOfGraphics * e33;
    }
    if (r(this.averageSymbolComplexity)) {
      const e33 = this.labelsEnabled ? this.averageSymbolComplexity.memory.bytesPerFeatureLabel : 0;
      return this.averageSymbolComplexity.memory.bytesPerFeature + e33;
    }
    return 0;
  }
  get unprocessedMemoryEstimate() {
    return (this._pendingAdds - this._pendingRemoves) * this.usedMemoryPerGraphic;
  }
  get _symbolComplexities() {
    return this.currentRenderer ? this._getSymbolComplexitiesUsedOrRenderer(this.currentRenderer) : this._getSymbolComplexitiesUsed();
  }
  get visible() {
    return this._visible;
  }
  _getConvertedSymbol(e33) {
    var _a;
    if ("web-style" === e33.type) return e33.clone();
    const i19 = this._symbolConversionCache.get(e33.id);
    if (r(i19)) return i19;
    const t28 = S3(e33, { geometryType: ((_a = this.layer) == null ? void 0 : _a.geometryType) ?? void 0, retainId: true, hasLabelingContext: this._hasLabelingContext(e33) }), r30 = t28.symbol || null;
    return t(r30) && t28.error && Te2.error(t28.error.message), this._symbolConversionCache.set(e33.id, r30), r30;
  }
  _getSymbolComplexitiesUsedOrRenderer(e33) {
    if (t(e33)) return [];
    const i19 = e33.getSymbols(), t28 = "backgroundFillSymbol" in e33 ? e33.backgroundFillSymbol : null;
    if (!(t28 || i19 && i19.length)) return [];
    const r30 = [], s26 = this._getSymbolComplexityUsedOrRenderer(t28);
    r(s26) && r30.push(s26);
    for (const a35 of i19) {
      const e34 = this._getSymbolComplexityUsedOrRenderer(a35);
      r(e34) && r30.push(e34);
    }
    return r30;
  }
  _getSymbolComplexityUsedOrRenderer(e33) {
    if (t(e33)) return null;
    const i19 = this._symbols.get(e33.id);
    if (r(i19)) return i19.complexity;
    const t28 = this._getConvertedSymbol(e33);
    return r(t28) ? m10(t28) : null;
  }
  _getSymbolComplexitiesUsed() {
    const e33 = [];
    return this._symbols.forEach((i19) => {
      r(i19) && e33.push(i19.complexity);
    }), e33;
  }
  get _objectIdField() {
    return this.layer.objectIdField;
  }
  constructor(e33) {
    super(e33), this._propertiesPool = new o13({ computedExtent: w4 }, this), this.computedExtent = null, this.currentRenderer = null, this.rendererHasGeometryOperations = false, this._graphicStateTracking = null, this.graphics3DGraphics = /* @__PURE__ */ new Map(), this.stageLayer = null, this.stage = null, this._graphicsDrapedUids = /* @__PURE__ */ new Set(), this._graphicsBySymbol = /* @__PURE__ */ new Map(), this._symbolConversionCache = /* @__PURE__ */ new Map(), this._symbols = /* @__PURE__ */ new Map(), this._graphicsWithoutSymbol = /* @__PURE__ */ new Map(), this._graphicsWaitingForSymbol = /* @__PURE__ */ new Map(), this._graphicsUpdateId = 0, this._handles = new t3(), this._frameTask = D2, this._suspendSymbolCleanup = false, this._arcadeOnDemand = null, this._rendererChangeAbortController = null, this._elevationInfoChangeAbortController = null, this._initializeAbortController = null, this._scaleVisibility = null, this._filterVisibility = null, this._spatialIndex = null, this.extentPadding = 0, this._updatingPendingLoadedGraphicsChange = null, this._featureStore = null, this._deconflictor = null, this._labeler = null, this._objectStates = null, this._viewElevationProvider = null, this._stageLayerElevationProvider = null, this._sharedSymbolResourcesOwnerHandle = null, this._whenGraphics3DGraphicRequests = {}, this._pendingUpdates = /* @__PURE__ */ new Map(), this._numberOfGraphics = 0, this._numberOfGraphicsProvidingElevation = 0, this._pendingAdds = 0, this._pendingRemoves = 0, this._applyPendingRemovesFirst = false, this._loadingSymbols = 0, this._pendingUpdatesPool = new l2({ allocator: (e34) => e34 || new We(), deallocator: (e34) => (e34.clear(), e34) }), this._symbolWarningLogged = false, this._geometryWarningLogged = false, this._objectIdInvisibleSet = /* @__PURE__ */ new Set(), this._whenSymbolRemoved = new l2(), this.preferredUpdatePolicy = C2.SYNC, this._forcedUpdatePolicy = null, this.elevationFeatureExpressionEnabled = true, this.owner = null, this.layer = null, this.graphicSymbolSupported = true, this.getRenderingInfoWithoutRenderer = false, this.setUidToIdOnAdd = true, this.hasZ = null, this.hasM = null, this._usedMemory = 0, this._visible = false, this._startCreateGraphics = false, this.symbolCreationContext = new e20(e33.owner.view.resourceController.scheduler, (e34, i19) => this._frameTask.schedule(e34, i19));
  }
  initialize() {
    this._featureStore = new m9({ objectIdField: this.owner.layer && this.owner.layer.objectIdField, hasZ: !!this.hasZ, hasM: !!this.hasM, viewSpatialReference: this._viewSpatialReference, featureSpatialReference: this.owner.featureSpatialReference, getSpatialIndex: () => this.spatialIndex, forEach: (e34) => this.graphics3DGraphics.forEach(e34) });
    const e33 = (e34, i20, t28) => this.spatialIndex.queryGraphicUIDsInExtent(e34, i20, t28), { componentFactories: i19 } = this;
    if (r(i19.elevationAlignment)) {
      const t28 = i19.elevationAlignment(this, e33);
      this._elevationAlignment = t28;
    }
    if (r(i19.scaleVisibility)) {
      const t28 = i19.scaleVisibility(this, e33);
      this._scaleVisibility = t28;
    }
    if (r(i19.filterVisibility)) {
      const e34 = i19.filterVisibility({ featureStore: this._featureStore, getFeatureCount: () => this.graphics3DGraphics.size, updateFeatureVisibilities: (e35) => this.modifyGraphics3DGraphicVisibilities((i20) => i20.setVisibilityFlag(C4.FILTER, e35(R4(i20.graphic, this._objectIdField)), E13.GRAPHIC)), setAllFeaturesVisibility: (e35) => this.modifyGraphics3DGraphicVisibilities((i20) => i20.setVisibilityFlag(C4.FILTER, e35, E13.GRAPHIC)), clearFeaturesVisibility: () => this.modifyGraphics3DGraphicVisibilities((e35) => e35.clearVisibilityFlag(C4.FILTER)) });
      this._filterVisibility = e34;
    }
    if (r(i19.deconflictor)) {
      const e34 = i19.deconflictor(this);
      this._deconflictor = e34;
    }
    if (r(i19.labeler) && r(this._scaleVisibility)) {
      const e34 = i19.labeler(this, this._scaleVisibility);
      this._labeler = e34;
    }
    if (r(i19.objectStates)) {
      const e34 = i19.objectStates(this);
      this._objectStates = e34;
    }
    this._initializeAbortController = new AbortController(), this._initializePromise = this._initializeAsync();
  }
  async _initializeAsync() {
    var _a, _b, _c, _d;
    const e33 = (_a = this._initializeAbortController) == null ? void 0 : _a.signal, i19 = this.owner.view;
    this._viewElevationProvider = new a27(this._viewSpatialReference, i19), this._initializeStage(i19, this.layer.uid);
    const t28 = i19.sharedSymbolResources;
    this.symbolCreationContext.sharedResources = t28, this._sharedSymbolResourcesOwnerHandle = t28.addGraphicsOwner(this.owner), r(this.currentRenderer) && (this.symbolCreationContext.renderer = this.currentRenderer), this.symbolCreationContext.stage = this.stage, this.symbolCreationContext.streamDataRequester = t28.streamDataRequester, this.symbolCreationContext.renderCoordsHelper = i19.renderCoordsHelper, this.symbolCreationContext.layer = this.layer, this.symbolCreationContext.graphicsCoreOwner = this.owner, this.symbolCreationContext.localOriginFactory = new p11(i19.renderSpatialReference), this.symbolCreationContext.elevationProvider = i19.elevationProvider, this.symbolCreationContext.notifyGraphicGeometryChanged = (e34) => this.notifyGraphicGeometryChanged(e34), this.symbolCreationContext.notifyGraphicVisibilityChanged = (e34) => this.notifyGraphicVisibilityChanged(e34);
    const r30 = d11(this.layer.elevationInfo, this.elevationFeatureExpressionEnabled);
    if (this.symbolCreationContext.featureExpressionInfoContext = await a21(r30, this._viewSpatialReference, e33, Te2), f(e33), this.symbolCreationContext.screenSizePerspectiveEnabled = i19.screenSizePerspectiveEnabled && !!this.layer.screenSizePerspectiveEnabled, this.symbolCreationContext.slicePlaneEnabled = !!this.owner.slicePlaneEnabled, this.symbolCreationContext.physicalBasedRenderingEnabled = !!((_b = this.owner.view.qualitySettings) == null ? void 0 : _b.physicallyBasedRenderingEnabled), this.symbolCreationContext.skipHighSymbolLods = !!((_d = (_c = this.owner.view.qualitySettings) == null ? void 0 : _c.graphics3D) == null ? void 0 : _d.skipHighSymbolLods), "drapeSourceType" in this.owner) {
      const { owner: e34 } = this;
      this.symbolCreationContext.drapeSourceRenderer = i19.basemapTerrain.overlayManager.registerGeometryDrapeSource(e34), this._handles.add(n3(() => i19.basemapTerrain.overlayManager.unregisterDrapeSource(e34)));
    }
    this._handles.add([l6(() => this.suspendedOrOutsideOfView, () => this._frameTask.reschedule(() => this._updateLayerVisibility())), l6(() => {
      var _a2;
      return [(_a2 = this.layer) == null ? void 0 : _a2.screenSizePerspectiveEnabled, this.owner.view.screenSizePerspectiveEnabled];
    }, () => {
      var _a2;
      const e34 = i19.screenSizePerspectiveEnabled && !!this.layer.screenSizePerspectiveEnabled;
      e34 !== this.symbolCreationContext.screenSizePerspectiveEnabled && (this.symbolCreationContext.screenSizePerspectiveEnabled = e34, (_a2 = this._labeler) == null ? void 0 : _a2.reset(), this.recreateAllGraphicsAndSymbols());
    }), l6(() => this.owner.slicePlaneEnabled, (e34) => this._slicePlaneEnabledChange(!!e34)), l6(() => {
      var _a2;
      return (_a2 = this.owner.view.state) == null ? void 0 : _a2.rasterPixelRatio;
    }, () => this._pixelRatioChange()), l6(() => {
      var _a2;
      return !!((_a2 = this.owner.view.qualitySettings) == null ? void 0 : _a2.physicallyBasedRenderingEnabled);
    }, (e34) => this._physicalBasedRenderingChange(e34)), l6(() => {
      var _a2, _b2;
      return !!((_b2 = (_a2 = this.owner.view.qualitySettings) == null ? void 0 : _a2.graphics3D) == null ? void 0 : _b2.skipHighSymbolLods);
    }, (e34) => this._skipHighSymbolLoDsChange(e34)), f5(() => {
      var _a2;
      return (_a2 = i19.basemapTerrain) == null ? void 0 : _a2.tilingScheme;
    }, (e34) => {
      if (!e34.spatialReference.equals(this.symbolCreationContext.overlaySR) && r(i19.basemapTerrain.spatialReference) && (this.symbolCreationContext.overlaySR = i19.basemapTerrain.spatialReference), this._handles.has("loaded-graphics")) this.recreateAllGraphics();
      else {
        const e35 = () => {
          var _a2;
          return (_a2 = this.owner) == null ? void 0 : _a2.loadedGraphics;
        };
        this._handles.add([a8(e35, "change", (e36) => {
          this._graphicsCollectionChanged(e36), this._signalUpdatingDuringAsyncLoadedGraphicsChange();
        }, { onListenerAdd: () => {
          this.recreateAllGraphics(), this._signalUpdatingDuringAsyncLoadedGraphicsChange();
        } })], "loaded-graphics");
      }
    }, { initial: true }), l6(() => this.effectiveUpdatePolicy, (e34) => {
      r(this.stageLayer) && (this.stageLayer.updatePolicy = e34), this.symbolCreationContext.isAsync = this.effectiveUpdatePolicy === C2.ASYNC, e34 === C2.SYNC && this.runTask(P3);
    }, w5)]), this._frameTask = i19.resourceController.scheduler.registerTask(R2.GRAPHICS_CORE, this), this.layer && "featureReduction" in this.layer && this._handles.add(l6(() => this.layer.featureReduction, () => this._deconflictor.featureReductionChange())), this.notifyChange("averageSymbolComplexity"), this.rendererChange(this.owner.renderer).catch(() => {
    }), this._initializeAbortController = null;
  }
  _abortInitialize() {
    this._initializeAbortController && (this._initializeAbortController.abort(), this._initializeAbortController = null);
  }
  destroy() {
    this._abortInitialize(), this._abortRendererChange(), this._abortElevationInfoChange(), this._frameTask.remove(), this._frameTask = D2, this.owner.view.deconflictor.removeGraphicsOwner(this), this.owner.view.labeler.removeGraphicsOwner(this), this._elevationAlignment = a(this._elevationAlignment), this._scaleVisibility = a(this._scaleVisibility), this._filterVisibility = a(this._filterVisibility), this._deconflictor = null, this._labeler = null, this._objectStates = a(this._objectStates), this.clear(), this._featureStore = a(this._featureStore), this._updatingPendingLoadedGraphicsChange = p(this._updatingPendingLoadedGraphicsChange), this._graphicStateTracking = a(this._graphicStateTracking), this.stage && (this.stage.remove(this.stageLayer), this.stageLayer = null, this.stage = null), this._handles = a(this._handles), this._set("owner", null);
    for (const e33 in this._whenGraphics3DGraphicRequests) this._whenGraphics3DGraphicRequests[e33].reject(new s2("graphic:layer-destroyed", "Layer has been destroyed"));
    this._whenGraphics3DGraphicRequests = null, this._sharedSymbolResourcesOwnerHandle = p(this._sharedSymbolResourcesOwnerHandle), this._propertiesPool = a(this._propertiesPool), this._pendingUpdatesPool = null, this._symbolConversionCache.clear(), this._objectIdInvisibleSet.clear(), this._spatialIndex = a(this._spatialIndex);
  }
  clear() {
    var _a, _b;
    (_a = this._objectStates) == null ? void 0 : _a.allGraphicsDeleted(), r(this._graphicStateTracking) && this._graphicStateTracking.allGraphicsDeleted(), this.graphics3DGraphics.forEach((e33) => e33.destroy()), (_b = this._spatialIndex) == null ? void 0 : _b.clear(), this.graphics3DGraphics.clear(), this._numberOfGraphics = 0, this._usedMemory = 0, this._updateLayerVisibility(), this._symbols.forEach(a), this._symbols.clear(), this._graphicsBySymbol.clear(), this._graphicsWithoutSymbol.clear(), this._graphicsWaitingForSymbol.clear(), this._pendingUpdates.clear(), this._pendingUpdatesPool.clear(), this._pendingAdds = 0, this._pendingRemoves = 0, this._applyPendingRemovesFirst = false, this.notifyChange("updating"), this.notifyChange("running"), this.notifyChange("updatingRemaining"), this._featureStore.events.emit("changed");
  }
  _initializeStage(e33, i19) {
    this.stage = e33._stage, this.stageLayer = new l19({ pickable: !this.suspendedOrOutsideOfView, updatePolicy: this.effectiveUpdatePolicy }, i19), this.stage.add(this.stageLayer);
    const t28 = this.stageLayer.events;
    t28.on("objectTransformation", (e34) => this.notifyGraphicGeometryChanged(e34.metadata.graphicUid)), t28.on("visibilityChanged", (e34) => this.notifyGraphicVisibilityChanged(e34.metadata.graphicUid)), t28.on("objectGeometryAdded", (e34) => this.notifyGraphicGeometryChanged(e34.object.metadata.graphicUid)), t28.on("objectGeometryRemoved", (e34) => this.notifyGraphicGeometryChanged(e34.object.metadata.graphicUid)), t28.on("objectGeometryUpdated", (e34) => this.notifyGraphicGeometryChanged(e34.object.metadata.graphicUid));
  }
  notifyGraphicGeometryChanged(e33) {
    if (t(this._graphicStateTracking) || t(e33)) return;
    const i19 = this.graphics3DGraphics.get(e33);
    i19 && this._graphicStateTracking.updateGraphicGeometry(i19);
  }
  notifyGraphicVisibilityChanged(e33) {
    if (t(this._graphicStateTracking) || t(e33)) return;
    const i19 = this.graphics3DGraphics.get(e33);
    i19 && this._graphicStateTracking.updateGraphicVisibility(i19);
  }
  _updateLayerVisibility() {
    const e33 = this.displayFeatureLimit.maximumNumberOfFeatures, i19 = this._numberOfGraphics > e33 * Me2, t28 = !this.suspendedOrOutsideOfView && !i19;
    t28 !== this._visible && (this._visible = t28, t28 ? (this.stageLayer.pickable = true, this.updateAllGraphicsVisibility()) : (this.stageLayer.pickable = false, this._hideAllGraphics()), this._updateStageLayerVisibility());
  }
  _updateStageLayerVisibility() {
    this.stageLayer.visible = this._visible && (null == this.layer.opacity || this.layer.opacity > 0);
  }
  getGraphics3DGraphicById(e33) {
    return null != e33 ? this.graphics3DGraphics.get(e33) : void 0;
  }
  getGraphics3DGraphicByObjectId(e33) {
    var _a;
    return ((_a = this.owner.layer) == null ? void 0 : _a.objectIdField) ? this._findGraphics3DGraphicByObjectId(e33) : null;
  }
  _getGraphicObjectID(e33, i19 = this.owner.layer && this.owner.layer.objectIdField) {
    return R4(e33, i19);
  }
  get graphics3DGraphicsByObjectID() {
    const e33 = this.owner.layer && this.owner.layer.objectIdField;
    if (!e33) return;
    const i19 = /* @__PURE__ */ new Map();
    return this.graphics3DGraphics.forEach((t28) => {
      if (!t28) return;
      const r30 = t28.graphic, s26 = this._getGraphicObjectID(r30, e33);
      r(s26) && i19.set(s26, t28);
    }), i19;
  }
  get labelsEnabled() {
    return !(!this._labeler || !this._labeler.layerLabelsEnabled());
  }
  async updateLabelingInfo(e33) {
    const i19 = this._deconflictor && this._deconflictor.labelingInfoChange(e33), t28 = this._labeler && this._labeler.labelingInfoChange(e33);
    await E([i19, t28]);
  }
  updateVisibilityInfo() {
    this._deconflictor && this._deconflictor.labelingInfoChange(), this._labeler && this._labeler.visibilityInfoChange();
  }
  get symbolUpdateType() {
    if (this._pendingUpdates.size > 0) return "unknown";
    let e33 = 0, i19 = 0;
    return n2(this._symbols, (t28, r30) => {
      if (r(t28)) {
        const s26 = t28.getFastUpdateStatus();
        if (s26.loading > 0) return true;
        this._graphicsBySymbol.has(r30) && (i19 += s26.fast, e33 += s26.slow);
      }
      return false;
    }) ? "unknown" : i19 >= 0 && 0 === e33 ? "fast" : e33 >= 0 && 0 === i19 ? "slow" : "mixed";
  }
  runTask(e33) {
    if (this._frameTask.processQueue(e33), this._applyPendingUpdates(e33), this.notifyChange("running"), this.running || this.notifyChange("updating"), this.notifyChange("updatingRemaining"), !e33.hasProgressed) return I2.YIELD;
  }
  setObjectIdVisibility(e33, i19) {
    i19 ? this._objectIdInvisibleSet.delete(e33) : this._objectIdInvisibleSet.add(e33);
    const t28 = this._findGraphics3DGraphicByObjectId(e33);
    r(t28) && this._updateUserVisibility(t28);
  }
  _findGraphics3DGraphicByObjectId(e33) {
    return t2(this.graphics3DGraphics, (i19) => this._getGraphicObjectID(i19.graphic) === e33);
  }
  _updateUserVisibility(e33) {
    if (t(e33)) return false;
    const i19 = e33.graphic, t28 = this._getGraphicObjectID(i19), r30 = i19.visible && !this.owner.suspended && (t(t28) || !this._objectIdInvisibleSet.has(t28));
    return e33.setVisibilityFlag(C4.USER_SETTING, r30, E13.GRAPHIC);
  }
  _whenGraphics3DGraphic(e33) {
    const i19 = this.graphics3DGraphics.get(e33.uid);
    if (i19) return Promise.resolve(i19);
    const t28 = this._whenGraphics3DGraphicRequests[e33.uid];
    if (t28) return t28.promise;
    const r30 = A();
    return this._whenGraphics3DGraphicRequests[e33.uid] = r30, r30.promise;
  }
  async _boundsForGraphics3DGraphic(e33, i19) {
    const t28 = this._viewSpatialReference, r30 = this.owner.view.renderSpatialReference, s26 = this.owner.view.basemapTerrain.spatialReference, a35 = (e34, i20, s27) => xn(e34, r30, i20, e34, t28, i20, s27), n26 = (e34, i20, r31) => xn(e34, s26, i20, e34, t28, i20, r31), o24 = this._viewElevationProvider ? { service: this._viewElevationProvider, useViewElevation: !!r(i19) && !!i19.useViewElevation, minDemResolution: r(i19) ? i19.minDemResolution : null, minDemResolutionForPoints: this.owner.view.resolution } : null, l34 = await e33.getProjectedBoundingBox(a35, n26, o24, x(i19, "signal"));
    if (!l34) return null;
    const p23 = l34.boundingBox;
    if (l34.requiresDrapedElevation) {
      const e34 = this.symbolCreationContext.elevationProvider;
      if (e34) {
        E3(p23, je2);
        const i20 = l(e34.getElevation(je2[0], je2[1], 0, t28, "ground"), 0);
        p23[2] = Math.min(p23[2], i20), p23[5] = Math.max(p23[5], i20);
      }
    }
    return { boundingBox: p23, screenSpaceObjects: l34.screenSpaceObjects };
  }
  async whenGraphicBounds(e33, i19) {
    await j4(() => {
      var _a;
      return (_a = this.owner) == null ? void 0 : _a.loadedGraphics;
    });
    const t28 = this.owner.layer && this.owner.layer.objectIdField, s26 = this.owner.loadedGraphics.find((i20) => i20 === e33 || null != t28 && null != i20.attributes && e33.attributes && i20.attributes[t28] === e33.attributes[t28]);
    if (!s26) throw new s2("internal:graphic-not-part-of-view", "Graphic is not part of this view");
    const a35 = await this._whenGraphics3DGraphic(s26);
    return this._boundsForGraphics3DGraphic(a35, i19);
  }
  computeAttachmentOrigin(e33, i19) {
    const t28 = this.graphics3DGraphics.get(e33.uid);
    if (!t28) return null;
    const r30 = t28.computeAttachmentOrigin();
    if (0 === r30.render.num && 0 === r30.draped.num) return null;
    o2(Ne, 0, 0, 0);
    let s26 = 0;
    if (r30.render.num > 0) {
      if (!jn(r30.render.origin, this.symbolCreationContext.renderCoordsHelper.spatialReference, Be, i19)) return null;
      u3(Ne, Ne, Be), s26++;
    }
    if (r30.draped.num > 0) {
      const [e34, t29] = r30.draped.origin, a35 = l(this._viewElevationProvider.getElevation(e34, t29, "ground"), 0);
      if (o2(Be, e34, t29, a35), !jn(Be, this._viewElevationProvider.spatialReference, Be, i19)) return null;
      u3(Ne, Ne, Be), s26++;
    }
    return s26 > 1 && g2(Ne, Ne, 1 / s26), new w3({ x: Ne[0], y: Ne[1], z: Ne[2], spatialReference: i19 });
  }
  getSymbolLayerSize(e33, i19) {
    const t28 = this._symbols.get(e33.id);
    if (t(t28)) throw new s2("internal:symbol-not-part-of-view", "Symbol is not part of this view");
    const s26 = e33.symbolLayers.indexOf(i19);
    if (-1 === s26) throw new s2("internal:missing-symbol-layer", "Symbol layer is not in symbol");
    const a35 = t28.getSymbolLayerSize(s26);
    if (t(a35)) throw new s2("internal:missing-size", "Symbol layer has no valid size");
    return a35;
  }
  _graphicsCollectionChanged(e33) {
    this._startCreateGraphics && (this.add(e33.added), this.remove(e33.removed));
  }
  graphicUpdateHandler(e33) {
    const i19 = e33.graphic.uid, t28 = this.graphics3DGraphics.get(i19);
    if (!t(t28) || !t(this._graphicsWithoutSymbol.get(i19))) switch (e33.property) {
      case "visible":
        this._graphicUpdateVisibleHandler(t28);
        break;
      case "geometry":
        this._graphicUpdateGeometryHandler(t28, e33);
        break;
      case "symbol":
        this._graphicUpdateSymbolHandler(t28, e33);
        break;
      case "attributes":
        break;
      case "transform":
        this._graphicUpdateTransformHandler(t28, e33);
    }
  }
  _graphicUpdateGeometryHandler(e33, i19) {
    const t28 = i19.graphic.geometry;
    if (t(t28)) return void this._recreateGraphic(i19.graphic);
    if (t(e33)) {
      const e34 = i19.graphic.symbol && i19.graphic.symbol.id;
      if (e34) {
        const i20 = this._symbols.get(e34);
        if (r(i20) && i20.loadStatus === e23.LOADING) return;
      }
      return void this._recreateGraphic(i19.graphic);
    }
    const r30 = e33.graphics3DSymbol;
    !t(i19.newValue) && r30.updateGeometry(e33, i19.newValue) || this._recreateGraphic(e33.graphic), this._expandComputedExtent(t28);
  }
  _graphicUpdateSymbolHandler(e33, i19) {
    const t28 = i19.graphic, r30 = r(e33) ? e33.graphics3DSymbol : r(i19.oldValue) ? this._symbols.get(i19.oldValue.id) : null;
    if (t(r30) || t(i19.newValue)) return void this._recreateGraphic(t28);
    const s26 = r30.symbol, a35 = this._getConvertedSymbol(i19.newValue);
    if (r(a35) && (a35.type !== s26.type || "web-style" === a35.type) || "web-style" === s26.type) return void this._recreateGraphic(t28);
    const n26 = this._graphicsBySymbol.get(s26.id);
    if (n26 && 1 !== n26.size) return void this._recreateGraphic(t28);
    const o24 = m4(s26, a35);
    if (t(o24)) return void this._updateSymbolMapping(s26.id, a35);
    const l34 = { diff: o24, graphics3DGraphicPatches: [], symbolStatePatches: [] };
    if (r30.prepareSymbolPatch(l34), !d5(l34.diff)) return void this._recreateGraphic(t28);
    const d26 = this._getRenderingInfo(t28);
    if (t(d26)) return void this._recreateGraphic(t28);
    const c34 = r30.extentPadding;
    for (const h25 of l34.symbolStatePatches) h25();
    if (c34 !== r30.extentPadding && this._recomputeExtentPadding(), r(e33)) for (const h25 of l34.graphics3DGraphicPatches) h25(e33, d26);
    this._updateSymbolMapping(s26.id, a35);
  }
  _graphicUpdateVisibleHandler(e33) {
    this._updateUserVisibility(e33) && (this._labeler && this.owner.view.labeler.setDirty(), this.owner.view.deconflictor.setDirty());
  }
  _graphicUpdateTransformHandler(e33, i19) {
  }
  recreateGraphics(e33) {
    this._suspendSymbolCleanup = true, this.remove(e33), this.add(e33), this._suspendSymbolCleanup = false, this.effectiveUpdatePolicy === C2.SYNC && this._cleanupSymbols();
  }
  _recreateGraphic(e33) {
    this.recreateGraphics([e33]);
  }
  _beginGraphicUpdate(e33) {
    const i19 = this._graphicsUpdateId;
    return this._graphicsUpdateId++, this._graphicsWaitingForSymbol.set(e33.uid, i19), 1 === this._graphicsWaitingForSymbol.size && this.notifyChange("updating"), i19;
  }
  _endGraphicUpdate(e33) {
    e33 && (this._graphicsWaitingForSymbol.delete(e33.uid), 0 === this._graphicsWaitingForSymbol.size && (this._cleanupSymbols(), this.notifyChange("updating")));
  }
  _recomputeExtentPadding() {
    let e33 = 0;
    this._symbols.forEach((i19) => {
      r(i19) && (e33 = Math.max(e33, i19.extentPadding));
    }), this._set("extentPadding", e33);
  }
  _expandComputedExtent(e33) {
    const i19 = Ve, t28 = e33.spatialReference;
    I4(e33, i19);
    const r30 = this._viewSpatialReference, s26 = Le.tmpVec;
    if (E2(t28, r30) || Bn(i19[0], i19[1], 0, t28, s26, r30) && (i19[0] = s26[0], i19[1] = s26[1], Bn(i19[3], i19[4], 0, t28, s26, r30), i19[3] = s26[0], i19[4] = s26[1]), !(isFinite(i19[0]) && isFinite(i19[3]) && isFinite(i19[1]) && isFinite(i19[4]))) return;
    const a35 = this.computedExtent;
    let n26 = null;
    const o24 = isFinite(i19[2]) && isFinite(i19[5]), l34 = o24 && (!a35 || null == a35.zmin || i19[2] < a35.zmin), h25 = o24 && (!a35 || null == a35.zmax || i19[5] > a35.zmax);
    if (a35) {
      (i19[0] < a35.xmin || i19[1] < a35.ymin || i19[3] > a35.xmax || i19[4] > a35.ymax || l34 || h25) && (n26 = this._propertiesPool.get("computedExtent"), n26.xmin = Math.min(i19[0], a35.xmin), n26.ymin = Math.min(i19[1], a35.ymin), n26.xmax = Math.max(i19[3], a35.xmax), n26.ymax = Math.max(i19[4], a35.ymax), n26.spatialReference = r30);
    } else n26 = this._propertiesPool.get("computedExtent"), n26.xmin = i19[0], n26.ymin = i19[1], n26.xmax = i19[3], n26.ymax = i19[4], n26.spatialReference = r30;
    n26 && (l34 && (n26.zmin = i19[2]), h25 && (n26.zmax = i19[5]), this._set("computedExtent", n26));
  }
  _abortElevationInfoChange() {
    this._elevationInfoChangeAbortController && (this._elevationInfoChangeAbortController.abort(), this._elevationInfoChangeAbortController = null);
  }
  async elevationInfoChange() {
    var _a, _b;
    this._abortElevationInfoChange();
    const e33 = new AbortController();
    this._elevationInfoChangeAbortController = e33;
    const i19 = d11(this.layer.elevationInfo, this.elevationFeatureExpressionEnabled);
    this.symbolCreationContext.featureExpressionInfoContext = await a21(i19, this._viewSpatialReference, e33.signal, Te2), f(e33.signal), this._elevationInfoChangeAbortController = null, (_a = this._labeler) == null ? void 0 : _a.elevationInfoChange(), this.forEachGraphics3DSymbol((e34, i20, t28) => {
      e34.globalPropertyChanged("elevationInfo", i20) ? i20.forEach((e35) => {
        const i21 = e35.graphic, t29 = e35.labelLayers;
        for (const r30 of t29) {
          r30.graphics3DSymbolLayer.updateGraphicElevationContext(i21, r30);
        }
      }) : this._recreateSymbol(t28);
    }), this.updateStageLayerElevationProvider(), (_b = this._elevationAlignment) == null ? void 0 : _b.elevationInfoChange();
  }
  updateStageLayerElevationProvider() {
    this._stageLayerElevationProvider ? (this.layer.elevationInfo && "relative-to-scene" === this.layer.elevationInfo.mode || 0 === this._numberOfGraphicsProvidingElevation) && (this.owner.view.elevationProvider.unregister(this._stageLayerElevationProvider), this._stageLayerElevationProvider = h(this._stageLayerElevationProvider)) : (!this.layer.elevationInfo || this.layer.elevationInfo && "relative-to-scene" !== this.layer.elevationInfo.mode) && this._numberOfGraphicsProvidingElevation > 0 && (this._stageLayerElevationProvider = new b21({ layer: this.layer, stageLayer: this.stageLayer, view: this.owner.view }), this.owner.view.elevationProvider.register("scene", this._stageLayerElevationProvider));
  }
  _clearSymbolsAndGraphics() {
    var _a, _b, _c, _d;
    this.clear(), r(this._filterVisibility) && this._filterVisibility.clear(), (_a = this._labeler) == null ? void 0 : _a.reset(), (_b = this._deconflictor) == null ? void 0 : _b.clear(), (_c = this._elevationAlignment) == null ? void 0 : _c.clear(), (_d = this.stageLayer) == null ? void 0 : _d.invalidateSpatialQueryAccelerator(), this._stageLayerElevationProvider && (this.owner.view.elevationProvider.unregister(this._stageLayerElevationProvider), this._stageLayerElevationProvider = h(this._stageLayerElevationProvider));
  }
  startCreateGraphics() {
    this._startCreateGraphics = true, this.recreateAllGraphics();
  }
  recreateAllGraphics() {
    this._recreateAllGraphics(false);
  }
  recreateAllGraphicsAndSymbols() {
    this._recreateAllGraphics(true);
  }
  _recreateAllGraphics(e33 = false) {
    if (!this._startCreateGraphics) return;
    const { loadedGraphics: i19, view: t28 } = this.owner, r30 = t28.basemapTerrain.tilingScheme && i19 && i19.length ? i19.toArray() : null;
    !e33 && r30 || this._clearSymbolsAndGraphics(), this.symbolCreationContext.screenSizePerspectiveEnabled = this.owner.view.screenSizePerspectiveEnabled && !!this.layer.screenSizePerspectiveEnabled, this.symbolCreationContext.slicePlaneEnabled = !!this.owner.slicePlaneEnabled, this._set("computedExtent", null), r30 && (e33 ? this.add(r30) : this.recreateGraphics(r30));
  }
  _recreateSymbol(e33) {
    const i19 = this._graphicsBySymbol.get(e33), t28 = [];
    i19 && (i19.forEach((e34, i20) => {
      var _a;
      const r31 = e34.usedMemory;
      this._conditionalRemove(e34, i20), (_a = this._spatialIndex) == null ? void 0 : _a.remove(e34), t28.push(e34.graphic), e34.destroy(), this._removeGraphics3DGraphic(i20, r31), this._updateLayerVisibility(), this._featureStore.events.emit("changed");
    }), this._graphicsBySymbol.set(e33, /* @__PURE__ */ new Map()));
    const r30 = this._symbols.get(e33);
    a(r30), this._symbols.delete(e33), this.add(t28);
  }
  _recreateGraphicsForSymbol(e33) {
    const i19 = this._graphicsBySymbol.get(e33);
    if (i19) {
      const e34 = [];
      i19.forEach((i20) => e34.push(i20.graphic)), this.recreateGraphics(e34);
    }
  }
  _conditionalRemove(e33, i19) {
    var _a, _b, _c;
    this._graphicsDrapedUids.delete(i19), (_a = this._objectStates) == null ? void 0 : _a.removeGraphic(e33), (_b = this._labeler) == null ? void 0 : _b.removeGraphic(e33), (_c = this._deconflictor) == null ? void 0 : _c.removeGraphic(e33), r(this._graphicStateTracking) && this._graphicStateTracking.removeGraphic(e33);
  }
  add(e33) {
    e33 && 0 !== e33.length && (this.owner.view.basemapTerrain && this.owner.view.basemapTerrain.tilingScheme ? (this._updatePolicyForGraphics(e33) === C2.ASYNC ? this._addDelayed(e33) : this._addImmediate(e33), this.notifyChange("updating")) : Te2.error("#add()", "Cannot add graphics before terrain surface has been initialized"));
  }
  _updatePolicyForGraphics(e33) {
    if (this.effectiveUpdatePolicy === C2.SYNC && ("mesh" === this.layer.geometryType || null == this.layer.geometryType)) {
      for (const i19 of e33) if (r(i19.geometry) && "mesh" === i19.geometry.type && !i19.geometry.loaded) return C2.ASYNC;
    }
    return this.effectiveUpdatePolicy;
  }
  _addImmediate(e33) {
    this._geometryWarningLogged = false, this._symbolWarningLogged = false;
    for (const i19 of e33) this._addGraphic(i19, this._getRenderingInfo(i19, Te2), C2.SYNC);
    this._cleanupSymbols(), this._labeler && (this.owner.view.labeler.setDirty(), this._cleanupSymbols()), this.owner.view.deconflictor.setDirty();
  }
  _addDelayed(e33) {
    var _a;
    for (const i19 of e33) {
      const e34 = i19.uid;
      let t28 = this._pendingUpdates.get(e34);
      t28 ? t28.add ? t28.state !== ze.NEW && ((_a = t28.abortController) == null ? void 0 : _a.abort()) : this._pendingAdds++ : (t28 = this._pendingUpdatesPool.pushNew(), this._pendingAdds++, this._pendingUpdates.set(e34, t28)), t28.add = i19;
    }
    this.notifyChange("running"), this.notifyChange("updatingRemaining");
  }
  remove(e33) {
    this.effectiveUpdatePolicy === C2.ASYNC ? this._removeDelayed(e33) : this._removeImmediate(e33), this.notifyChange("updating");
  }
  _removeImmediate(e33) {
    for (const i19 of e33) this._removeGraphic(i19);
    this._cleanupSymbols(), this._labeler && this.owner.view.labeler.setDirty(), this.owner.view.deconflictor.setDirty();
  }
  _removeDelayed(e33) {
    var _a;
    for (const i19 of e33) {
      const e34 = i19.uid, t28 = this._pendingUpdates.get(e34);
      if (t28) t28.add && (t28.remove ? t28.add = null : this._pendingUpdates.delete(e34), t28.state === ze.LOADING && ((_a = t28.abortController) == null ? void 0 : _a.abort()), this._pendingAdds--);
      else {
        const t29 = this._pendingUpdatesPool.pushNew();
        t29.remove = i19, this._pendingUpdates.set(e34, t29), this._pendingRemoves++, this._applyPendingRemovesFirst = true;
      }
    }
    0 === this._pendingUpdates.size && this._finishPendingUpdates(), this.notifyChange("running"), this.notifyChange("updatingRemaining");
  }
  _finishPendingUpdates() {
    this._pendingUpdatesPool.clear(), this._cleanupSymbols(), (this._pendingAdds || this._pendingRemoves) && Te2.warn("pendingAdds/Removes in inconsistent state!"), this._pendingAdds = 0, this._pendingRemoves = 0, this._applyPendingRemovesFirst = false;
  }
  _applyPendingUpdates(e33) {
    var _a;
    if (this._geometryWarningLogged = false, this._symbolWarningLogged = false, 0 === this._pendingUpdates.size && ((_a = this._spatialIndex) == null ? void 0 : _a.updating)) return this._spatialIndex.update(), void e33.madeProgress();
    if (this._applyPendingRemovesFirst) {
      this._applyPendingRemovesFirst = false;
      for (const [i19, t28] of this._pendingUpdates) {
        if (e33.done) {
          this._applyPendingRemovesFirst = true;
          break;
        }
        if (t28.remove && !t28.add && (this._pendingRemoves--, e33.madeProgress(), this._removeGraphic(t28.remove), t28.remove = null, this._pendingUpdates.delete(i19), 0 === this._pendingRemoves)) break;
      }
    }
    for (const [i19, t28] of this._pendingUpdates) {
      if (e33.done) break;
      t28.add && t28.state === ze.NEW && this._processPendingUpdateNew(t28);
      let r30 = this.effectiveUpdatePolicy;
      if (!t28.remove || t28.add && t28.state !== ze.READY || (this._pendingRemoves--, e33.madeProgress(), this._removeGraphic(t28.remove), t28.remove = null, r30 = C2.SYNC), t28.add) switch (t28.state) {
        case ze.READY:
          this._addGraphic(t28.add, t28.renderingInfo, r30), t28.add = null, this._pendingAdds--, e33.madeProgress();
          break;
        case ze.REJECTED:
          t28.add = null, this._pendingAdds--;
        case ze.LOADING:
      }
      null == t28.remove && null == t28.add && this._pendingUpdates.delete(i19);
    }
    0 === this._pendingUpdates.size && (this._finishPendingUpdates(), this.notifyChange("running"));
  }
  _processPendingUpdateNew(e33) {
    if (!e33.add) return void (e33.state = ze.READY);
    const i19 = e33.add.geometry;
    r(i19) && "mesh" === i19.type && !i19.loaded ? this._processPendingUpdateNewMesh(e33, i19) : this._processPendingUpdateNewRenderingInfo(e33);
  }
  async _processPendingUpdateNewMesh(e33, i19) {
    e33.state = ze.LOADING, e33.abortController = new AbortController();
    const t28 = e33.abortController.signal;
    try {
      await i19.load({ signal: t28 });
    } catch (r30) {
      return this._processPendingUpdateNewError(e33, r30);
    }
    e33.abortController = null, this._processPendingUpdateNewRenderingInfo(e33);
  }
  _processPendingUpdateNewError(e33, i19) {
    e33.abortController = null, j(i19) ? e33.state = ze.NEW : e33.state = ze.REJECTED;
  }
  async _processPendingUpdateNewRenderingInfo(e33) {
    if (t(this.layer.renderer) || "dictionary" !== this.layer.renderer.type) return e33.renderingInfo = this._getRenderingInfo(e33.add, Te2), void (e33.state = ze.READY);
    e33.state = ze.LOADING, e33.abortController = new AbortController();
    let i19 = null;
    try {
      i19 = await this._getRenderingInfoAsync(e33.add, { signal: e33.abortController.signal });
    } catch (t28) {
      return e33.abortController = null, void (j(t28) ? e33.state = ze.NEW : e33.state = ze.REJECTED);
    }
    t(i19) || t(i19.symbol) ? (Te2 && !this._symbolWarningLogged && (this._symbolWarningLogged = true, Te2.warn(`Graphic in layer ${this.layer.id} has no symbol and will not render`)), e33.renderingInfo = null) : e33.renderingInfo = i19, e33.state = ze.READY;
  }
  _addGraphic(e33, i19, t28) {
    if (this._graphicsWithoutSymbol.set(e33.uid, e33), t(i19) || t(i19.symbol) || !b4(e33)) return;
    r(this.stage.renderView.objectAndLayerIdRenderHelper) && this.setUidToIdOnAdd && this.stage.renderView.objectAndLayerIdRenderHelper.setUidToObjectAndLayerId(e33.objectId, e33.uid, this.layer.id, this.layer.uid, !!this.layer.popupEnabled);
    const r30 = i19.symbol, s26 = this.getOrCreateGraphics3DSymbol(r30, i19.renderer);
    if (t(s26)) return;
    this._expandComputedExtent(e33.geometry);
    const a35 = this._beginGraphicUpdate(e33), n26 = new r20(e33, i19, this.layer);
    let o24 = false;
    const l34 = (e34) => {
      e34 === s26.symbol.id && (o24 = true);
    };
    this._whenSymbolRemoved.push(l34);
    const d26 = () => {
      if (--this._loadingSymbols, this.destroyed) return;
      this._whenSymbolRemoved.removeUnordered(l34);
      if (this._graphicsWaitingForSymbol.get(e33.uid) !== a35 || o24 || s26.destroyed || this.graphicSymbolSupported && e33.symbol && e33.symbol.id !== s26.symbol.id) --s26.referenced, this._cleanupSymbols();
      else {
        const i20 = this._createGraphics3DGraphic(s26, n26);
        this._spatialIndex && r(i20) && this._spatialIndex.add(i20), --s26.referenced, this._endGraphicUpdate(e33);
      }
      this._featureStore.events.emit("changed"), this._labeler && this.owner.view.labeler.setDirty();
    }, c34 = (i20) => {
      --this._loadingSymbols, this.destroyed || (this._whenSymbolRemoved.removeUnordered(l34), o24 || (j(i20) ? this.add([e33]) : s26.destroyed || this._endGraphicUpdate(e33)));
    };
    ++this._loadingSymbols, t28 === C2.ASYNC ? s26.load(() => this._frameTask.schedule(d26), (e34) => this._frameTask.schedule(() => c34(e34))) : s26.load(d26, c34);
  }
  _removeGraphic(e33) {
    var _a, _b;
    const i19 = e33.uid, t28 = this.graphics3DGraphics.get(i19);
    if (t28) {
      t28.graphics3DSymbol.onRemoveGraphic(t28);
      const e34 = t28.usedMemory, r30 = t28.isElevationSource;
      this._conditionalRemove(t28, i19), (_a = this._spatialIndex) == null ? void 0 : _a.remove(t28);
      const s26 = t28.graphics3DSymbol.symbol.id;
      (_b = this._graphicsBySymbol.get(s26)) == null ? void 0 : _b.delete(i19), this._graphicsWithoutSymbol.delete(i19), this._removeGraphics3DGraphic(i19, e34, r30), t28.destroy(), this._featureStore.events.emit("changed");
    } else this._graphicsWithoutSymbol.delete(i19), this._graphicsWaitingForSymbol.delete(i19), 0 === this._graphicsWaitingForSymbol.size && (this._cleanupSymbols(), this.notifyChange("updating"));
  }
  _hasLabelingContext(e33) {
    if (e33 instanceof b6 || e33 instanceof m5) {
      const i19 = this.symbolCreationContext.layer;
      return !!i19.labelingInfo && i19.labelingInfo.some((i20) => i20.symbol === e33);
    }
    return false;
  }
  _hasValidSymbolCreationContext(e33) {
    return !(e33 instanceof b6 && !this._hasLabelingContext(e33)) || (Te2.error("LabelSymbol3D is only valid as part of a LabelClass. Using LabelSymbol3D as a renderer symbol is not supported."), false);
  }
  _getRenderingInfo(e33, i19) {
    const t28 = e33.geometry;
    if (t(t28)) return i19 && !this._geometryWarningLogged && (this._geometryWarningLogged = true, i19.warn(`Graphic in layer ${this.layer.id} has no geometry and will not render`)), null;
    if (!An(t28.spatialReference, this._viewSpatialReference)) return i19 && !this._geometryWarningLogged && (this._geometryWarningLogged = true, i19.warn(`Graphic in layer ${this.layer.id} has incompatible spatial reference and will not render`)), null;
    if (!this.graphicSymbolSupported && r(e33.symbol)) return i19 && !this._symbolWarningLogged && (this._symbolWarningLogged = true, i19.warn(`Graphic in layer ${this.layer.id} is not allowed to have a symbol, use a renderer instead`)), null;
    const r30 = this.rendererHasGeometryOperations ? u13(e33, this.layer) : e33;
    let s26;
    if (this.owner.getRenderingInfo && (this.getRenderingInfoWithoutRenderer || r(this.currentRenderer))) s26 = this.owner.getRenderingInfo(r30, this.currentRenderer, this._arcadeOnDemand);
    else {
      s26 = { symbol: r30.symbol || b12(r30.geometry) };
    }
    return t(s26) || t(s26.symbol) ? (i19 && !this._symbolWarningLogged && (this._symbolWarningLogged = true, i19.warn(`Graphic in layer ${this.layer.id} has no symbol and will not render`)), null) : s26;
  }
  _getRenderingInfoAsync(e33, i19) {
    const t28 = e33.geometry;
    if (t(t28)) return Te2 && !this._geometryWarningLogged && (this._geometryWarningLogged = true, Te2.warn(`Graphic in layer ${this.layer.id} has no geometry and will not render`)), null;
    if (!this.graphicSymbolSupported && r(e33.symbol)) return Te2 && !this._symbolWarningLogged && (this._symbolWarningLogged = true, Te2.warn(`Graphic in layer ${this.layer.id} is not allowed to have a symbol, use a renderer instead`)), null;
    const r30 = this.rendererHasGeometryOperations ? u13(e33, this.layer) : e33;
    return this.owner.getRenderingInfoAsync(r30, this.currentRenderer, this._arcadeOnDemand, i19);
  }
  _createGraphics3DSymbol(e33, i19) {
    if (!this._hasValidSymbolCreationContext(e33)) return null;
    const t28 = this._getConvertedSymbol(e33);
    if (!t28) return null;
    let r30;
    if (r(i19) && "backgroundFillSymbol" in i19 && i19.backgroundFillSymbol) {
      const e34 = S3(i19.backgroundFillSymbol, { ignoreDrivers: true });
      r(e34.symbol) && "web-style" !== e34.symbol.type && "cim" !== e34.symbol.type && (r30 = e34.symbol.symbolLayers);
    }
    const s26 = t26(t28, this.symbolCreationContext, r30);
    return s26.load(() => {
      const e34 = s26.extentPadding;
      e34 > this.extentPadding && this._set("extentPadding", e34), this.notifyChange("averageSymbolComplexity");
    }, () => {
    }), s26;
  }
  getOrCreateGraphics3DSymbol(e33, i19) {
    let t28 = this._symbols.get(e33.id);
    return void 0 === t28 && (t28 = e33 instanceof f8 ? new e30(e33, (e34) => this._frameTask.schedule(e34), (e34) => this._createGraphics3DSymbol(e34, i19)) : this._createGraphics3DSymbol(e33, i19), this._symbols.set(e33.id, t28)), r(t28) && ++t28.referenced, t28;
  }
  trackGraphicState(e33) {
    return t(this._graphicStateTracking) && (this._graphicStateTracking = new s23(this)), this._graphicStateTracking.add(e33);
  }
  _addGraphics3DGraphic(e33) {
    this._usedMemory += e33.usedMemory, this.graphics3DGraphics.set(e33.graphic.uid, e33), this._numberOfGraphics++, e33.isElevationSource && (this._numberOfGraphicsProvidingElevation++, this.updateStageLayerElevationProvider()), this._updateLayerVisibility();
  }
  _removeGraphics3DGraphic(e33, i19, t28 = false) {
    this._usedMemory -= i19, this.graphics3DGraphics.delete(e33), this._numberOfGraphics--, t28 && (this._numberOfGraphicsProvidingElevation--, this.updateStageLayerElevationProvider()), this._updateLayerVisibility();
  }
  _createGraphics3DGraphic(e33, i19) {
    var _a, _b, _c;
    const t28 = i19.graphic;
    if (this._graphicsWithoutSymbol.delete(t28.uid), !this._symbols.has(e33.symbol.id)) return this.add([t28]), null;
    if (this.graphics3DGraphics.has(t28.uid)) return null;
    const r30 = e33.createGraphics3DGraphic(i19);
    if (t(r30)) return null;
    this._addGraphics3DGraphic(r30);
    const s26 = e33.symbol.id;
    this._graphicsBySymbol.has(s26) || this._graphicsBySymbol.set(s26, /* @__PURE__ */ new Map()), this._graphicsBySymbol.get(s26).set(t28.uid, r30);
    if (r30.isDraped && this._graphicsDrapedUids.add(t28.uid), r30.centroid = null, r(t28.geometry) && "point" !== t28.geometry.type && (r30.centroid = A5(t28.geometry, this._viewSpatialReference)), this._updateUserVisibility(r30), r(this._scaleVisibility) && this._scaleVisibility.updateVisibility(r30), r(this._filterVisibility)) {
      const { defaultVisibility: e34 } = this._filterVisibility;
      r30.setVisibilityFlag(C4.FILTER, e34, E13.GRAPHIC), e34 || this._filterVisibility.reapply();
    }
    (_a = this._deconflictor) == null ? void 0 : _a.addGraphic(r30), (_b = this._labeler) == null ? void 0 : _b.addGraphic(r30), (_c = this._objectStates) == null ? void 0 : _c.addGraphic(r30), this._deconflictor && this.owner.view.deconflictor.setInitialIconVisibilityFlag(this, r30), r30.initialize(this.stage, this.stageLayer, this.owner), r(this._graphicStateTracking) && this._graphicStateTracking.addGraphic(r30);
    const a35 = this._whenGraphics3DGraphicRequests[t28.uid];
    return a35 && (delete this._whenGraphics3DGraphicRequests[t28.uid], a35.resolve(r30)), r30;
  }
  _abortRendererChange() {
    this._rendererChangeAbortController && (this._rendererChangeAbortController.abort(), this._rendererChangeAbortController = null);
  }
  async rendererChange(e33) {
    if (this._abortRendererChange(), e33 !== this.currentRenderer) if (this._validateRenderer(e33), t(e33) && this._currentRendererChange(null, false), s14(e33)) if (r(e33) && e33.arcadeRequired) {
      const i19 = new AbortController();
      this._rendererChangeAbortController = i19;
      const { arcadeUtils: t28 } = await this._ensureArcade();
      f(i19);
      const r30 = t28.hasGeometryOperations(e33);
      r30 && (await t28.enableGeometryOperations(), f(i19)), this.effectiveUpdatePolicy === C2.ASYNC ? await this._frameTask.schedule(() => this._currentRendererChange(e33, r30), i19.signal) : this._currentRendererChange(e33, r30), this._rendererChangeAbortController = null;
    } else if (this.effectiveUpdatePolicy === C2.ASYNC) {
      const i19 = new AbortController();
      this._rendererChangeAbortController = i19, await this._frameTask.schedule(() => this._currentRendererChange(e33, false), i19.signal), this._rendererChangeAbortController = null;
    } else this._currentRendererChange(e33, false);
    else this._currentRendererChange(e33, false);
  }
  async _ensureArcade() {
    return t(this._arcadeOnDemand) ? (this._arcadeOnDemand = await i9(), this._arcadeOnDemand) : this._arcadeOnDemand;
  }
  _currentRendererChange(e33, i19) {
    this.currentRenderer = e33, this.rendererHasGeometryOperations = i19, this.symbolCreationContext.arcade = e2(this._arcadeOnDemand);
    const t28 = this.symbolCreationContext.renderer;
    if (e33 === t28) return;
    if (this._symbolConversionCache.clear(), t(e33)) return this.symbolCreationContext.renderer = null, void this.recreateAllGraphicsAndSymbols();
    const r30 = m4(t28, e33);
    this._updateUnchangedSymbolMappings(r30, e33, t28), this.symbolCreationContext.renderer = e33, t(r30) || ("complete" === r30.type ? this.recreateAllGraphicsAndSymbols() : "partial" === r30.type && (this._applyRendererDiff(r30, e33, t28) ? this._volatileGraphicsUpdated() : this.recreateAllGraphicsAndSymbols()), this.notifyChange("averageSymbolComplexity"));
  }
  _diffHasSymbolChange(e33) {
    for (const i19 in e33.diff) switch (i19) {
      case "visualVariables":
      case "defaultSymbol":
      case "uniqueValueInfos":
        break;
      case "uniqueValueGroups":
      case "authoringInfo":
      case "fieldDelimiter":
        delete e33.diff[i19];
        break;
      default:
        return true;
    }
    return false;
  }
  _applySymbolSetDiff(e33, i19, t28) {
    e33 = e33 || [], i19 = i19 || [];
    const r30 = [];
    for (const s26 of i19) {
      const i20 = this._graphicsBySymbol.get(s26.id);
      i20 && i20.forEach((a35, n26) => {
        const o24 = a35.graphic, l34 = this.layer instanceof b5 ? this.layer : null, h25 = e2(this._arcadeOnDemand);
        if (s26 === t28.defaultSymbol && t28.getSymbol(u13(o24, l34), { arcade: h25 }) === t28.defaultSymbol) return;
        const d26 = a35.usedMemory;
        e33.length || t28.defaultSymbol ? r30.push(o24) : this._graphicsWithoutSymbol.set(n26, o24);
        const p23 = this.graphics3DGraphics.get(n26);
        this._conditionalRemove(p23, n26), a35.destroy(), i20.delete(n26), this._removeGraphics3DGraphic(n26, d26), this._updateLayerVisibility();
      }), this._whenSymbolRemoved.forAll((e34) => e34(s26.id));
    }
    (e33.length || r30.length) && (this._graphicsWithoutSymbol.forEach((e34) => r30.push(e34)), this._graphicsWithoutSymbol.clear(), this.add(r30)), this._cleanupSymbols(), this._labeler && this.owner.view.labeler.setDirty(), this.owner.view.deconflictor.setDirty();
  }
  _applyUniqueValueRendererDiff(e33, i19, t28) {
    const r30 = e33.diff.defaultSymbol, s26 = e33.diff.uniqueValueInfos;
    if (r30 || s26) {
      const a35 = s26 ? s26.added.map((e34) => e34.symbol).filter(r) : [], n26 = s26 ? s26.removed.map((e34) => e34.symbol).filter(r) : [];
      if (s26) for (let e34 = 0; e34 < s26.changed.length; e34++) a35.push(s26.changed[e34].newValue.symbol), n26.push(s26.changed[e34].oldValue.symbol);
      return r30 ? (t28.defaultSymbol && n26.push(t28.defaultSymbol), i19.defaultSymbol && a35.push(i19.defaultSymbol)) : t28.defaultSymbol && a35.length && n26.push(i19.defaultSymbol), this._applySymbolSetDiff(a35, n26, i19), delete e33.diff.defaultSymbol, delete e33.diff.uniqueValueInfos, true;
    }
    return false;
  }
  _calculateUnchangedSymbolMapping(e33, i19, t28) {
    var _a;
    if ("unique-value" !== (i19 == null ? void 0 : i19.type) || "unique-value" !== (t28 == null ? void 0 : t28.type) || r(e33) && "partial" !== e33.type) return [];
    const r30 = (e34) => r(e34) ? e34.id : null, s26 = e33 && e33.diff, a35 = s26 && s26.defaultSymbol, n26 = s26 && s26.uniqueValueInfos;
    let o24;
    if (n26) o24 = n26.unchanged.map((e34) => ({ oldId: r30(e34.oldValue.symbol), newId: r30(e34.newValue.symbol) }));
    else {
      o24 = [];
      for (const e34 of t28.uniqueValueInfos ?? []) {
        const t29 = r30(e34.symbol), s27 = (_a = i19.uniqueValueInfos) == null ? void 0 : _a.find((i20) => i20.value === e34.value);
        s27 && t29 !== r30(s27.symbol) && o24.push({ oldId: t29, newId: r30(s27.symbol) });
      }
    }
    return !a35 && t28.defaultSymbol && o24.push({ oldId: r30(t28.defaultSymbol), newId: r30(i19.defaultSymbol) }), o24;
  }
  _updateSymbolMapping(e33, i19) {
    const t28 = r(i19) && i19 ? "string" == typeof i19 ? i19 : i19.id : null;
    if (t(e33) || e33 === t28) return;
    const r30 = this._graphicsBySymbol.get(e33);
    this._graphicsBySymbol.delete(e33), void 0 !== r30 && this._graphicsBySymbol.set(t28, r30);
    const s26 = this._symbols.get(e33);
    if (void 0 !== s26 && (this._symbols.delete(e33), this._symbols.set(t28, s26), r(s26))) {
      const e34 = "string" == typeof i19 ? null : i19;
      r(e34) ? s26.symbol = e34 : s26.symbol.id = t28;
    }
  }
  _updateUnchangedSymbolMappings(e33, i19, t28) {
    const r30 = this._calculateUnchangedSymbolMapping(e33, i19, t28);
    for (const { oldId: s26, newId: a35 } of r30) this._updateSymbolMapping(s26, a35);
  }
  _applyRendererDiff(e33, t28, r30) {
    if (this._diffHasSymbolChange(e33)) return false;
    if (t28 instanceof T3 && r30 instanceof T3 && this._applyUniqueValueRendererDiff(e33, t28, r30) && 0 === Object.keys(e33.diff).length) return true;
    for (const [i19] of this._graphicsBySymbol) {
      const r31 = this._symbols.get(i19);
      if (r(r31)) switch (r31.applyRendererDiff(e33, t28)) {
        case e22.Recreate_Symbol:
          this._recreateSymbol(i19);
          break;
        case e22.Recreate_Graphics:
          this._recreateGraphicsForSymbol(i19);
        case e22.Fast_Update:
      }
    }
    return true;
  }
  opacityChange() {
    this.forEachGraphics3DSymbol((e33, i19) => e33.globalPropertyChanged("opacity", i19)), this._updateStageLayerVisibility();
  }
  _slicePlaneEnabledChange(e33) {
    e33 !== this.symbolCreationContext.slicePlaneEnabled && (this.symbolCreationContext.slicePlaneEnabled = e33, this.stageLayer.sliceable = e33, this.forEachGraphics3DSymbol((e34, i19) => e34.globalPropertyChanged("slicePlaneEnabled", i19)), this._deconflictor && this._deconflictor.slicePlaneEnabledChange(), this._labeler && this._labeler.slicePlaneEnabledChange());
  }
  _physicalBasedRenderingChange(e33) {
    this.symbolCreationContext.physicalBasedRenderingEnabled = e33, this.forEachGraphics3DSymbol((e34, i19, t28) => {
      e34.globalPropertyChanged("physicalBasedRenderingEnabled", i19) || this._recreateSymbol(t28);
    });
  }
  _skipHighSymbolLoDsChange(e33) {
    this.symbolCreationContext.skipHighSymbolLods = e33, this.forEachGraphics3DSymbol((e34, i19, t28) => {
      e34.globalPropertyChanged("skipHighSymbolLods", i19) || this._recreateSymbol(t28);
    });
  }
  _pixelRatioChange() {
    this.forEachGraphics3DSymbol((e33, i19, t28) => {
      e33.globalPropertyChanged("pixelRatio", i19) || this._recreateSymbol(t28);
    });
  }
  _signalUpdatingDuringAsyncLoadedGraphicsChange() {
    this._updatingPendingLoadedGraphicsChange && this._updatingPendingLoadedGraphicsChange.remove(), this._updatingPendingLoadedGraphicsChange = v(() => {
      this._updatingPendingLoadedGraphicsChange = null;
    });
  }
  setClippingExtent(e33, i19) {
    const t28 = this.symbolCreationContext.clippingExtent, r30 = u4();
    return e31(e33, r30, i19) ? this.symbolCreationContext.clippingExtent = d2(a7(), r30) : this.symbolCreationContext.clippingExtent = null, !k(this.symbolCreationContext.clippingExtent, t28);
  }
  modifyGraphics3DGraphicVisibilities(e33) {
    var _a;
    let i19 = false;
    this.graphics3DGraphics.forEach((t28) => {
      e33(t28) && (i19 = true);
    }), i19 && ((_a = this.owner.view.labeler) == null ? void 0 : _a.setDirty(), this.owner.view.deconflictor.setDirty());
  }
  forEachGraphics3DSymbol(e33) {
    for (const [i19, t28] of this._symbols) {
      if (t(t28)) return;
      e33(t28, this._graphicsBySymbol.get(i19) || He, i19);
    }
  }
  updateAllGraphicsVisibility() {
    r(this._filterVisibility) && this._filterVisibility.reapply(), this.modifyGraphics3DGraphicVisibilities((e33) => {
      const i19 = this._updateUserVisibility(e33), t28 = r(this._scaleVisibility) && this._scaleVisibility.updateVisibility(e33);
      return i19 || t28;
    });
  }
  _hideAllGraphics() {
    this.modifyGraphics3DGraphicVisibilities((e33) => e33.setVisibilityFlag(C4.USER_SETTING, false, E13.GRAPHIC));
  }
  _validateRenderer(e33) {
    var _a;
    const i19 = u15(e33, { geometryType: (_a = this.layer) == null ? void 0 : _a.geometryType });
    if (i19) {
      const e34 = `Renderer for layer '${this.layer.title ? `${this.layer.title}, ` : ""}, id:${this.layer.id}' is not supported in a SceneView`;
      Te2.warn(e34, i19.message);
    }
  }
  _volatileGraphicsUpdated() {
    var _a;
    (_a = this._labeler) == null ? void 0 : _a.reset(), this.stageLayer.shaderTransformationChanged(), this.notifyChange("updating");
  }
  _cleanupSymbols() {
    if (this._graphicsWaitingForSymbol.size > 0 || this._suspendSymbolCleanup) return;
    let e33 = false;
    this._symbols.forEach((i19, t28) => {
      if (t(i19) || i19.referenced > 0) return;
      const r30 = this._graphicsBySymbol.get(t28);
      r30 && 0 !== r30.size || (this._graphicsBySymbol.delete(t28), this._symbols.delete(t28), a(i19), e33 = true);
    }), e33 && (this._recomputeExtentPadding(), this.notifyChange("averageSymbolComplexity"));
  }
  get test() {
    return { snapshotInternals: () => ({ graphics: [...this.graphics3DGraphics.keys()].sort(), symbols: [...this._symbols.keys()].sort(), graphicsBySymbol: [...this._graphicsBySymbol.keys()].sort().map((e33) => ({ symbolId: e33, graphics: [...this._graphicsBySymbol.get(e33).keys()].sort() })), graphicsWithoutSymbol: [...this._graphicsWithoutSymbol.keys()].sort(), graphicsDrapedUids: [...this._graphicsDrapedUids].sort(), pendingUpdates: this._pendingUpdates }), symbols: this._symbols, filterVisibility: this._filterVisibility, numPending: this._pendingUpdates.size, forceUpdatePolicy: (e33) => {
      this._forcedUpdatePolicy = e33;
    } };
  }
  get performanceInfo() {
    return { visible: this.graphics3DGraphics.size, missing: this._graphicsWithoutSymbol.size, pending: this._pendingUpdates.size };
  }
};
var ze;
ke.tmpVec = n5(), e([y2({ readOnly: true })], ke.prototype, "computedExtent", void 0), e([y2()], ke.prototype, "currentRenderer", void 0), e([y2()], ke.prototype, "rendererHasGeometryOperations", void 0), e([y2()], ke.prototype, "_frameTask", void 0), e([y2({ readOnly: true })], ke.prototype, "_viewSpatialReference", null), e([y2()], ke.prototype, "_rendererChangeAbortController", void 0), e([y2()], ke.prototype, "_elevationInfoChangeAbortController", void 0), e([y2()], ke.prototype, "_initializeAbortController", void 0), e([y2()], ke.prototype, "_elevationAlignment", void 0), e([y2()], ke.prototype, "_scaleVisibility", void 0), e([y2()], ke.prototype, "_filterVisibility", void 0), e([y2()], ke.prototype, "_initializePromise", void 0), e([y2()], ke.prototype, "_spatialIndex", void 0), e([y2({ readOnly: true })], ke.prototype, "extentPadding", void 0), e([y2()], ke.prototype, "_updatingPendingLoadedGraphicsChange", void 0), e([y2()], ke.prototype, "_featureStore", void 0), e([y2()], ke.prototype, "_deconflictor", void 0), e([y2()], ke.prototype, "_labeler", void 0), e([y2()], ke.prototype, "_objectStates", void 0), e([y2()], ke.prototype, "_loadingSymbols", void 0), e([y2()], ke.prototype, "preferredUpdatePolicy", void 0), e([y2()], ke.prototype, "_forcedUpdatePolicy", void 0), e([y2({ readOnly: true })], ke.prototype, "effectiveUpdatePolicy", null), e([y2({ constructOnly: true })], ke.prototype, "elevationFeatureExpressionEnabled", void 0), e([y2({ constructOnly: true })], ke.prototype, "owner", void 0), e([y2({ constructOnly: true })], ke.prototype, "layer", void 0), e([y2({ constructOnly: true })], ke.prototype, "graphicSymbolSupported", void 0), e([y2({ constructOnly: true })], ke.prototype, "getRenderingInfoWithoutRenderer", void 0), e([y2({ constructOnly: true })], ke.prototype, "componentFactories", void 0), e([y2({ constructOnly: true })], ke.prototype, "setUidToIdOnAdd", void 0), e([y2()], ke.prototype, "featureStore", null), e([y2()], ke.prototype, "initializePromise", null), e([y2()], ke.prototype, "scaleVisibility", null), e([y2()], ke.prototype, "elevationAlignment", null), e([y2()], ke.prototype, "objectStates", null), e([y2()], ke.prototype, "filterVisibility", null), e([y2({ readOnly: true })], ke.prototype, "updating", null), e([y2({ readOnly: true })], ke.prototype, "running", null), e([y2({ readOnly: true })], ke.prototype, "suspendedOrOutsideOfView", null), e([y2({ readOnly: true, dependsOn: [] })], ke.prototype, "updatingRemaining", null), e([y2({ readOnly: true, dependsOn: ["owner.view.qualitySettings.graphics3D.maxTotalNumberOfPrimitives", "owner.view.qualitySettings.graphics3D.maxTotalNumberOfFeatures", "averageSymbolComplexity"] })], ke.prototype, "displayFeatureLimit", null), e([y2({ readOnly: true, dependsOn: [] })], ke.prototype, "averageSymbolComplexity", null), e([y2({ constructOnly: true })], ke.prototype, "hasZ", void 0), e([y2({ constructOnly: true })], ke.prototype, "hasM", void 0), e([y2()], ke.prototype, "_objectIdField", null), ke = Le = e([a2(Fe)], ke), function(e33) {
  e33[e33.NEW = 0] = "NEW", e33[e33.LOADING = 1] = "LOADING", e33[e33.READY = 2] = "READY", e33[e33.REJECTED = 3] = "REJECTED";
}(ze || (ze = {}));
var We = class {
  constructor() {
    this.add = null, this.renderingInfo = null, this.state = ze.NEW, this.abortController = null, this.remove = null;
  }
  clear() {
    this.add = null, this.renderingInfo = null, this.state = ze.NEW, this.abortController = null, this.remove = null;
  }
};
var Me2 = 10;
var Ne = n5();
var Be = n5();
var He = /* @__PURE__ */ new Map();

// node_modules/@arcgis/core/views/3d/layers/graphics/ExtentSet.js
var l31 = 0.05;
var _17 = class {
  constructor() {
    this._extents = new l2({ allocator: (t28) => t28 || u4() }), this._tmpExtent = u4(), this._dirty = false;
  }
  get empty() {
    return 0 === this._extents.length;
  }
  get size() {
    return this._extents.length;
  }
  clear() {
    this._extents.clear();
  }
  add(t28) {
    this._contains(t28) || (this._removeContained(t28), a6(this._extents.pushNew(), t28), this._dirty = true);
  }
  pop() {
    return this._dirty && this._mergeTight(), this._extents.pop();
  }
  merge(t28) {
    return this._mergeTight(t28), t28.hasProgressed;
  }
  _mergeTight(t28 = P3) {
    const e33 = this._extents, o24 = /* @__PURE__ */ new Set();
    let i19 = 0;
    for (; i19 !== e33.length; ) {
      e33.sort((t29, e34) => t29[0] - e34[0]), i19 = e33.length, o24.clear();
      for (let i20 = 0; i20 < e33.length; ++i20) {
        if (t28.done) return;
        const h25 = e33.getItemAt(i20);
        if (h25) {
          for (let t29 = i20 + 1; t29 < e33.length; ++t29) {
            const r30 = e33.getItemAt(t29);
            if (null == r30 || r30[0] >= h25[2]) break;
            o24.add(r30);
          }
          o24.forEach((i21) => {
            if (h25 === i21) return;
            if (i21[2] <= h25[0]) return void o24.delete(i21);
            const _21 = y3(h25), a35 = y3(i21), c34 = this._tmpExtent;
            h3(h25, i21, c34);
            const g23 = _21 + a35;
            (y3(c34) - g23) / g23 < l31 && (a6(h25, c34), o24.delete(i21), e33.remove(i21), t28.madeProgress());
          }), o24.add(h25);
        }
      }
    }
    this._dirty = false;
  }
  _contains(t28) {
    return this._extents.some((e33) => R(e33, t28));
  }
  _removeContained(t28) {
    this._extents.filterInPlace((e33) => !R(t28, e33));
  }
  get test() {
    const t28 = this;
    return { containsPoint: (e33) => t28._extents.some((t29) => b(t29, e33)) };
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DElevationAlignment.js
var l32 = class extends v2 {
  constructor(e33) {
    super(e33), this._dirtyExtents = new _17(), this._globalDirty = false, this._averageExtentUpdateSize = 0, this._dirtyGraphicsSet = /* @__PURE__ */ new Set(), this._handles = new t3(), this._updateElevation = false, this.graphicsCoreOwner = null, this.graphicsCore = null, this.events = new n4();
  }
  initialize() {
    const e33 = this.elevationProvider, t28 = this.graphicsCoreOwner.view.resourceController.scheduler;
    this._handles.add([e33.on("elevation-change", (e34) => this._elevationChanged(e34)), l6(() => this.graphicsCoreOwner.suspended, () => this._suspendedChange()), t28.registerTask(R2.ELEVATION_ALIGNMENT, this)]);
  }
  destroy() {
    this._dirtyGraphicsSet.clear(), this._handles = a(this._handles), this.graphicsCoreOwner = null, this.graphicsCore = null, this.queryGraphicUIDsInExtent = null, this.elevationProvider = null;
  }
  clear() {
    this._dirtyGraphicsSet.clear(), this.notifyChange("updating");
  }
  _suspendedChange() {
    true === this.graphicsCoreOwner.suspended ? this._updateElevation = false : false === this.graphicsCoreOwner.suspended && this._updateElevation && (this._globalDirty = true, this.notifyChange("updating"));
  }
  elevationInfoChange() {
    this._globalDirty = true, this.notifyChange("updating");
  }
  get updating() {
    return this.running;
  }
  get running() {
    return this._dirtyGraphicsSet.size > 0 || this._dirtyExtents && !this._dirtyExtents.empty || this._globalDirty;
  }
  get updatingRemaining() {
    return this._dirtyGraphicsSet.size + this._dirtyExtents.size * this._averageExtentUpdateSize;
  }
  runTask(e33) {
    for (this._globalDirty && (this._markAllGraphicsElevationDirty(), this._globalDirty = false, e33.madeProgress()), e33.run(() => this._dirtyExtents.merge(e33)); this.running && !e33.done; ) this._updateDirtyGraphics(e33), this._updateDirtyExtents(e33);
    this.notifyChange("updating");
  }
  _updateDirtyGraphics(e33) {
    const t28 = this.graphicsCoreOwner.view.renderCoordsHelper, i19 = this.graphicsCore.effectiveUpdatePolicy === C2.ASYNC;
    for (const r30 of this._dirtyGraphicsSet.keys()) {
      const s26 = this.graphicsCore.getGraphics3DGraphicById(r30);
      if (this._dirtyGraphicsSet.delete(r30), r(s26) && (s26.alignWithElevation(this.elevationProvider, t28, i19), this.graphicsCoreOwner.view.deconflictor.setDirty(), e33.madeProgress()), e33.done) return;
    }
  }
  _updateDirtyExtents(e33) {
    for (; !this._dirtyExtents.empty && !e33.done; ) {
      const t28 = this._dirtyExtents.pop(), i19 = this.elevationProvider.spatialReference;
      this.events.emit("invalidate-elevation", { extent: t28, spatialReference: i19 });
      const r30 = this._dirtyGraphicsSet.size;
      this.queryGraphicUIDsInExtent(t28, i19, (e34) => {
        const t29 = this.graphicsCore.getGraphics3DGraphicById(e34);
        r(t29) && t29.needsElevationUpdates() && this._dirtyGraphicsSet.add(e34);
      }), this._averageExtentUpdateSize = 0.1 * (this._dirtyGraphicsSet.size - r30) + 0.9 * this._averageExtentUpdateSize, e33.madeProgress();
    }
  }
  _markAllGraphicsElevationDirty() {
    this._dirtyExtents.clear(), this._dirtyGraphicsSet.clear(), this.graphicsCore.graphics3DGraphics.forEach((e33, t28) => this._dirtyGraphicsSet.add(t28));
  }
  _elevationChanged(e33) {
    if ("scene" === e33.context && (!this.graphicsCore.layer.elevationInfo || "relative-to-scene" !== this.graphicsCore.layer.elevationInfo.mode)) return;
    const { extent: t28, spatialReference: i19 } = e33;
    if (this.graphicsCoreOwner.suspended) {
      if (!this._updateElevation) {
        const e34 = this.graphicsCore.computedExtent;
        e34 && t28[2] > e34.xmin && t28[0] < e34.xmax && t28[3] > e34.ymin && t28[1] < e34.ymax && (this._updateElevation = true);
      }
      this.events.emit("invalidate-elevation", { extent: t28, spatialReference: i19 });
    } else t28[0] === -1 / 0 ? this._globalDirty = true : this._dirtyExtents.add(t28), this.notifyChange("updating");
  }
};
e([y2()], l32.prototype, "graphicsCoreOwner", void 0), e([y2()], l32.prototype, "graphicsCore", void 0), e([y2()], l32.prototype, "queryGraphicUIDsInExtent", void 0), e([y2()], l32.prototype, "elevationProvider", void 0), e([y2({ readOnly: true })], l32.prototype, "updating", null), e([y2({ readOnly: true })], l32.prototype, "updatingRemaining", null), l32 = e([a2("esri.views.3d.layers.graphics.Graphics3DElevationAlignment")], l32);
var g19 = l32;

// node_modules/@arcgis/core/views/3d/support/intersectionUtils.js
function h24(r30, n26, t28, i19) {
  return b22(r30, n26, t28, k11(i19, n26, t28, true));
}
var g20 = { dir: n5(), len: 0, clip: n13() };
function k11(r30, n26, i19, s26) {
  const f27 = g20;
  return r30 ? (i19 && s26 && (f27.len = x2(n26, i19)), r4(f27.dir, r30)) : s26 ? (f27.len = x2(n26, i19), e5(f27.dir, i19, n26), g2(f27.dir, f27.dir, 1 / f27.len)) : (e5(f27.dir, i19, n26), z(f27.dir, f27.dir)), f27;
}
function v19(r30, t28, i19) {
  const e33 = P(Y2(r30), i19.dir), c34 = -V2(r30, t28);
  if (c34 < 0 && e33 >= 0) return false;
  if (e33 > -1e-6 && e33 < 1e-6) return c34 > 0;
  if ((c34 < 0 || e33 < 0) && !(c34 < 0 && e33 < 0)) return true;
  const u32 = c34 / e33;
  return e33 > 0 ? u32 < i19.clip[1] && (i19.clip[1] = u32) : u32 > i19.clip[0] && (i19.clip[0] = u32), i19.clip[0] <= i19.clip[1];
}
function b22(r30, n26, t28, i19) {
  i19.clip[0] = 0, i19.clip[1] = t28 ? i19.len : Number.MAX_VALUE;
  for (let e33 = 0; e33 < r30.length; e33++) if (!v19(r30[e33], n26, i19)) return false;
  return true;
}

// node_modules/@arcgis/core/views/3d/support/FrustumExtentIntersection.js
var M12 = 0.5 * Math.PI;
var F10 = M12 / Math.PI * 180;
var N8 = class {
  constructor(t28) {
    this._renderCoordsHelper = t28.renderCoordsHelper, this._extent = new Array(4), this._planes = new Array(6), this._maxSpan = 0, this._center = { origin: n5(), direction: n5() };
    for (let e33 = 0; e33 < 4; e33++) this._extent[e33] = { origin: n5(), direction: n5(), cap: { next: null, direction: n5() } }, this._planes[e33] = p9();
    this._planes[U2.NEAR] = p9(), this._planes[U2.FAR] = p9(), this._planesWithoutFar = this._planes.slice(0, 5);
  }
  update(t28, e33, i19, r30 = true) {
    const a35 = this._extent;
    this._toRenderBoundingExtent(t28, e33, i19), u3(this._center.origin, a35[0].origin, a35[2].origin), g2(this._center.origin, this._center.origin, 0.5), this._renderCoordsHelper.worldUpAtPosition(this._center.origin, this._center.direction), r30 || g2(this._center.direction, this._center.direction, -1);
    for (let n26 = 0; n26 < 4; n26++) {
      const t29 = a35[n26];
      this._renderCoordsHelper.worldUpAtPosition(t29.origin, t29.direction);
      const e34 = a35[3 === n26 ? 0 : n26 + 1];
      t29.cap.next = e34.origin, H(t29.cap.direction, t29.origin, e34.origin), O6(t29.direction, t29.cap.direction, t29.origin, this._planes[n26]), r30 || g2(t29.direction, t29.direction, -1);
    }
    O6(a35[0].cap.direction, a35[1].cap.direction, a35[0].origin, this._planes[U2.NEAR]), r30 ? P4(this._planes[U2.NEAR], this._planes[U2.FAR]) : (A3(this._planes[U2.FAR], this._planes[U2.NEAR]), P4(this._planes[U2.NEAR], this._planes[U2.NEAR])), this._maxSpan = Math.max(Math.abs(t28[0] - t28[2]), Math.abs(t28[1] - t28[3])), this._maxSpanSpatialReference = e33, this._minGlobalAltitude = 0.9 * O(this._maxSpanSpatialReference).radius;
  }
  isVisibleInFrustum(t28, e33, i19 = false) {
    if (null == t28) return false;
    if (this._renderCoordsHelper.viewingMode === l15.Global) {
      const i20 = this._maxSpanSpatialReference.isGeographic ? F10 : M12 * e33;
      if (this._maxSpan > i20) return true;
      if (null != t28.altitude && t28.altitude >= this._minGlobalAltitude) return this._isVisibleInFrustumGlobal(t28);
    }
    if (0 === this._maxSpan) {
      const e34 = this._extent[0];
      return !(i19 || !t28.intersectsRay(p7(e34.origin, e34.direction)));
    }
    for (let n26 = 0; n26 < this._extent.length; n26++) {
      const e34 = this._extent[n26];
      if (!i19 && t28.intersectsRay(p7(e34.origin, e34.direction))) return true;
      if (t28.intersectsLineSegment(b9(e34.origin, e34.cap.next, V10), e34.cap.direction)) return true;
    }
    const r30 = i19 ? this._planes : this._planesWithoutFar;
    for (let n26 = 0; n26 < t28.lines.length; n26++) {
      const e34 = t28.lines[n26];
      if (h24(r30, e34.origin, e34.endpoint, e34.direction)) return true;
    }
    return false;
  }
  _toRenderBoundingExtentGlobal(t28, r30, n26) {
    const o24 = 5;
    p4(t28, U8), U8[2] = n26, Zn(r30, U8, v20, this._renderCoordsHelper.spatialReference), h6(I12, v20), S(L7);
    for (const { x0: i19, x1: s26, y0: c34, y1: l34 } of k12) for (let p23 = 0; p23 < o24; p23++) {
      const h25 = p23 / (o24 - 1);
      U8[0] = h2(t28[i19], t28[s26], h25), U8[1] = h2(t28[c34], t28[l34], h25), U8[2] = n26, jn(U8, r30, U8, this._renderCoordsHelper.spatialReference), O2(U8, U8, I12), c3(L7, U8);
    }
    o2(this._extent[0].origin, L7[0], L7[1], L7[2]), o2(this._extent[1].origin, L7[3], L7[1], L7[2]), o2(this._extent[2].origin, L7[3], L7[4], L7[2]), o2(this._extent[3].origin, L7[0], L7[4], L7[2]);
    for (let e33 = 0; e33 < 4; ++e33) O2(this._extent[e33].origin, this._extent[e33].origin, v20);
  }
  _toRenderBoundingExtentLocal(t28, e33, i19) {
    vn(t28, e33, P9, this._renderCoordsHelper.spatialReference), o2(this._extent[0].origin, P9[0], P9[1], i19), o2(this._extent[1].origin, P9[2], P9[1], i19), o2(this._extent[2].origin, P9[2], P9[3], i19), o2(this._extent[3].origin, P9[0], P9[3], i19);
  }
  _toRenderBoundingExtent(e33, i19, r30) {
    switch (this._renderCoordsHelper.viewingMode) {
      case l15.Global:
        this._toRenderBoundingExtentGlobal(e33, i19, r30);
        break;
      case l15.Local:
        this._toRenderBoundingExtentLocal(e33, i19, r30);
        break;
      default:
        n9(this._renderCoordsHelper.viewingMode);
    }
  }
  _isVisibleInFrustumGlobal(t28) {
    if (V2(t28.planes[U2.NEAR], this._center.origin) < 0 && P(this._center.direction, t28.direction) < 0) return true;
    for (let e33 = 0; e33 < 4; e33++) {
      const i19 = this._extent[e33];
      if (V2(t28.planes[U2.NEAR], i19.origin) < 0 && P(i19.direction, t28.direction) < 0) return true;
    }
    return false;
  }
};
var k12 = [{ x0: 0, y0: 1, x1: 2, y1: 1 }, { x0: 0, y0: 3, x1: 2, y1: 3 }, { x0: 0, y0: 1, x1: 0, y1: 3 }, { x0: 2, y0: 1, x1: 2, y1: 3 }];
var U8 = n5();
var v20 = e11();
var I12 = e11();
var L7 = a7();
var P9 = u4();
var V10 = v7();

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DFrustumVisibility.js
var d25 = 1.2;
var _18 = class extends v2 {
  constructor(e33) {
    super(e33), this.suspended = false, this._extent = null, this._extentIntersectionDirty = true, this._isVisibleBelowSurfaceInternal = false, this._handles = new t3(), this.graphicsCoreOwner = null, this.updating = true;
  }
  initialize() {
    const { graphicsCoreOwner: e33 } = this;
    this._extentIntersection = new N8({ renderCoordsHelper: e33.view.renderCoordsHelper });
    const t28 = e33.view, s26 = t28.basemapTerrain, i19 = t28.resourceController.scheduler;
    this._handles.add([t28.on("resize", () => this._viewChange()), l6(() => t28.state.camera, () => this._viewChange(), U), i19.registerTask(R2.FRUSTUM_VISIBILITY, this), l6(() => s26.visibleElevationBounds, () => this._elevationBoundsChange())]), "local" === t28.viewingMode ? this._isVisibleBelowSurface = true : this._handles.add([l6(() => {
      var _a, _b, _c;
      return [s26.baseOpacity, s26.wireframe, (_c = (_b = (_a = t28.map) == null ? void 0 : _a.ground) == null ? void 0 : _b.navigationConstraint) == null ? void 0 : _c.type];
    }, () => this._updateIsVisibleBelowSurface(), h5)]);
  }
  destroy() {
    this._set("graphicsCoreOwner", null), this._extent = null, this._extentIntersection = null, this._handles = a(this._handles);
  }
  _setDirty() {
    this.updating || this._set("updating", true);
  }
  setExtent(e33) {
    this._extent = e33, this._extentIntersectionDirty = true, this._setDirty();
  }
  _viewChange() {
    this._setDirty();
  }
  _elevationBoundsChange() {
    this._setDirty(), this._extentIntersectionDirty = true;
  }
  set _isVisibleBelowSurface(e33) {
    this._isVisibleBelowSurfaceInternal = e33, this._setDirty(), this._extentIntersectionDirty = true;
  }
  _updateIsVisibleBelowSurface() {
    var _a, _b;
    const e33 = this.graphicsCoreOwner.view, t28 = e33.basemapTerrain, s26 = "local" === e33.viewingMode, i19 = "none" === ((_b = (_a = e33.map.ground) == null ? void 0 : _a.navigationConstraint) == null ? void 0 : _b.type);
    this._isVisibleBelowSurface = s26 || !t28.opaque || i19;
  }
  _updateExtentIntersection() {
    if (!this._extentIntersectionDirty) return;
    this._extentIntersectionDirty = false;
    const e33 = this.graphicsCoreOwner.view;
    let t28;
    if (this._isVisibleBelowSurfaceInternal) t28 = -0.3 * O(e33.spatialReference).radius;
    else {
      const { min: s26, max: i19 } = e33.basemapTerrain.visibleElevationBounds;
      t28 = s26 - Math.max(1, (i19 - s26) * (d25 - 1));
    }
    this._extentIntersection.update(this._extent, e33.spatialReference, t28);
  }
  get running() {
    return this.updating;
  }
  runTask(e33) {
    if (this._set("updating", false), !this._extent) return this._set("suspended", false), I2.YIELD;
    this._updateExtentIntersection();
    const t28 = this.graphicsCoreOwner.view.frustum, s26 = O(this.graphicsCoreOwner.view.spatialReference).radius;
    this._set("suspended", !this._extentIntersection.isVisibleInFrustum(t28, s26)), e33.madeProgress();
  }
};
e([y2({ readOnly: true })], _18.prototype, "suspended", void 0), e([y2({ constructOnly: true })], _18.prototype, "graphicsCoreOwner", void 0), e([y2({ readOnly: true })], _18.prototype, "updating", void 0), _18 = e([a2("esri.views.3d.layers.graphics.Graphics3DFrustumVisibility")], _18);
var m16 = _18;

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/Object3DStateSet.js
var t27;
!function(e33) {
  e33[e33.Object = 0] = "Object", e33[e33.RenderGeometry = 1] = "RenderGeometry", e33[e33.External = 2] = "External", e33[e33.COUNT = 3] = "COUNT";
}(t27 || (t27 = {}));
var r29 = class {
  constructor() {
    this._items = [];
  }
  addObject(e33, r30) {
    this._items.push({ type: t27.Object, objectStateId: r30, object: e33 });
  }
  addRenderGeometry(e33, r30, o24) {
    this._items.push({ type: t27.RenderGeometry, objectStateId: r30, renderGeometry: e33, owner: o24 });
  }
  addExternal(e33, r30) {
    this._items.push({ type: t27.External, objectStateId: r30, remove: e33 });
  }
  remove(e33) {
    for (let t28 = this._items.length - 1; t28 >= 0; --t28) {
      const r30 = this._items[t28];
      r30.objectStateId === e33 && (this._removeObjectStateItem(r30), this._items.splice(t28, 1));
    }
  }
  removeObject(e33) {
    for (let r30 = this._items.length - 1; r30 >= 0; --r30) {
      const o24 = this._items[r30];
      o24.type === t27.Object && o24.object === e33 && (this._removeObjectStateItem(o24), this._items.splice(r30, 1));
    }
  }
  removeRenderGeometry(e33) {
    for (let r30 = this._items.length - 1; r30 >= 0; --r30) {
      const o24 = this._items[r30];
      o24.type === t27.RenderGeometry && o24.renderGeometry === e33 && (this._removeObjectStateItem(o24), this._items.splice(r30, 1));
    }
  }
  removeAll() {
    this._items.forEach((e33) => {
      this._removeObjectStateItem(e33);
    }), this._items = [];
  }
  _removeObjectStateItem(r30) {
    switch (r30.type) {
      case t27.Object:
        r30.objectStateId.channel === t12.Highlight ? r30.object.removeHighlight(r30.objectStateId) : r30.objectStateId.channel === t12.MaskOccludee && r30.object.removeOcclude(r30.objectStateId);
        break;
      case t27.RenderGeometry:
        r30.owner.removeRenderGeometryObjectState(r30.renderGeometry, r30.objectStateId);
        break;
      case t27.External:
        r30.remove(r30.objectStateId);
    }
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DObjectStateSet.js
var e32 = class {
  constructor(e33, i19) {
    this.stateType = e33, this.objectIdField = i19, this.objectStateSet = new r29(), this.ids = /* @__PURE__ */ new Set(), this.paused = false;
  }
  hasGraphic(t28) {
    if (this.objectIdField) {
      const e33 = t28.graphic.attributes[this.objectIdField];
      return this.ids.has(e33);
    }
    return this.ids.has(t28.graphic.uid);
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DObjectStates.js
var s24 = class {
  constructor(t28) {
    this._graphicsCore = t28, this._stateSets = new Array();
  }
  destroy() {
    this.reset(), this._stateSets = null;
  }
  reset() {
    this._stateSets && (this._stateSets.forEach((t28) => t28.objectStateSet.removeAll()), this._stateSets.length = 0);
  }
  acquireSet(s26, a35) {
    const i19 = new e32(s26, a35);
    this._stateSets.push(i19);
    const h25 = n3(() => this.releaseSet(i19));
    return { set: i19, handle: h25 };
  }
  releaseSet(t28) {
    t28.objectStateSet.removeAll();
    const e33 = this._stateSets ? this._stateSets.indexOf(t28) : -1;
    -1 !== e33 && this._stateSets.splice(e33, 1);
  }
  _addObjectStateSet(t28, e33) {
    t28.addObjectStateSet(e33.stateType, e33.objectStateSet);
  }
  _removeObjectStateSet(t28, e33) {
    t28.removeObjectState(e33.objectStateSet);
  }
  setUid(t28, e33) {
    t28.ids.add(e33);
    const s26 = this._graphicsCore.graphics3DGraphics.get(e33);
    s26 && this._addObjectStateSet(s26, t28);
  }
  setUids(t28, e33) {
    e33.forEach((e34) => this.setUid(t28, e34));
  }
  setObjectIds(t28, e33) {
    e33.forEach((e34) => t28.ids.add(e34)), this._initializeSet(t28);
  }
  addGraphic(t28) {
    this._stateSets.forEach((e33) => {
      !e33.paused && e33.hasGraphic(t28) && this._addObjectStateSet(t28, e33);
    });
  }
  removeGraphic(t28) {
    this._stateSets.forEach((e33) => {
      e33.hasGraphic(t28) && this._removeObjectStateSet(t28, e33);
    });
  }
  allGraphicsDeleted() {
    this._stateSets && this._stateSets.forEach((t28) => t28.objectStateSet.removeAll());
  }
  _initializeSet(t28) {
    const e33 = this._graphicsCore.graphics3DGraphics;
    t28.objectIdField ? e33.forEach((e34) => {
      e34 && t28.hasGraphic(e34) && this._addObjectStateSet(e34, t28);
    }) : t28.ids.forEach((s26) => {
      const a35 = e33.get(s26);
      a35 && this._addObjectStateSet(a35, t28);
    });
  }
  get test() {
    return { states: this._stateSets };
  }
};

// node_modules/@arcgis/core/views/3d/layers/graphics/Graphics3DScaleVisibility.js
var u31 = s.getLogger("esri.views.3d.layers.graphics.Graphics3DScaleVisibility");
var g21 = class extends d4 {
  constructor(e33) {
    super(e33), this._scaleRangeActive = false, this._layerScaleRangeVisibilityQuery = false, this._extent = null, this.graphicsCoreOwner = null, this.layer = null, this.queryGraphicUIDsInExtent = null, this.graphicsCore = null, this.basemapTerrain = null, this.layerScaleEnabled = true, this.suspended = false, this._dirty = true;
  }
  initialize() {
    this.updateScaleRangeActive();
    const e33 = this.graphicsCoreOwner.view.resourceController.scheduler;
    this.handles.add(e33.registerTask(R2.SCALE_VISIBILITY, this)), this.updatingHandles.add(() => this.layer.effectiveScaleRange, () => this.layerMinMaxScaleChangeHandler());
  }
  destroy() {
    this.updatingHandles.removeAll(), this.handles.removeAll(), this._dirty = false, this._extent = null, this.graphicsCoreOwner = null, this.layer = null, this.queryGraphicUIDsInExtent = null, this.graphicsCore = null, this.basemapTerrain = null;
  }
  get updating() {
    return this._dirty || this.updatingHandles.updating;
  }
  _setDirty() {
    this._dirty = true;
  }
  setExtent(e33) {
    const i19 = this.graphicsCoreOwner.view.spatialReference, t28 = this.graphicsCoreOwner.view.basemapTerrain.spatialReference;
    if (i19 === t28) this._extent = e33 ?? null;
    else {
      const r30 = u4();
      vn(e33, i19, r30, t28) ? this._extent = r30 : this._extent = null;
    }
    this._setDirty();
  }
  scaleRangeActive() {
    return this._scaleRangeActive;
  }
  updateScaleRangeActive() {
    const e33 = this.layer, i19 = e33.effectiveScaleRange;
    let t28 = this.layerScaleEnabled && null != i19 && b23(i19.minScale, i19.maxScale);
    e33.labelingInfo && !t28 && (t28 = e33.labelingInfo.some((e34) => e34 && b23(e34.minScale ?? 0, e34.maxScale ?? 0)));
    const r30 = this._scaleRangeActive !== t28;
    return this._scaleRangeActive = t28, t28 && !this.handles.has(m17) && this.basemapTerrain ? (this.handles.add(this.basemapTerrain.on("scale-change", (e34) => this._scaleUpdateHandler(e34)), m17), this.layerScaleEnabled && this.handles.add(this.basemapTerrain.on("tiles-visibility-changed", () => this._setDirty()), m17)) : !t28 && this.handles.has(m17) && this.handles.remove(m17), r30;
  }
  get running() {
    return !(!this.graphicsCoreOwner.view.basemapTerrain || !this.updating);
  }
  runTask(e33) {
    const i19 = this.graphicsCoreOwner.view.basemapTerrain;
    if (this._extent && i19 && i19.ready && this._scaleRangeActive && this.layerScaleEnabled) {
      if (this._layerScaleRangeVisibilityQuery) return I2.YIELD;
      {
        this._layerScaleRangeVisibilityQuery = true;
        const { minScale: e34, maxScale: t28 } = this.layer.effectiveScaleRange;
        i19.queryVisibleScaleRange(this._extent, e34, t28, (e35) => this._finishUpdate(e35));
      }
    } else this._finishUpdate(true);
    e33.madeProgress();
  }
  _finishUpdate(e33) {
    this._layerScaleRangeVisibilityQuery = false, this._set("suspended", !e33), this._dirty = false;
  }
  _visibleAtLayerScale(e33) {
    const i19 = this.layer.effectiveScaleRange;
    return !this.layerScaleEnabled || o14(e33, i19.minScale || 0, i19.maxScale || 0);
  }
  _visibleAtLabelScale(e33, i19) {
    return o14(e33, i19.minScale || 0, i19.maxScale || 0);
  }
  _graphicScale(e33) {
    let i19;
    if (r(e33.centroid) ? i19 = e33.centroid : r(e33.graphic.geometry) && "point" === e33.graphic.geometry.type && (i19 = e33.graphic.geometry), i19) {
      return this.graphicsCoreOwner.view.basemapTerrain ? this.graphicsCoreOwner.view.basemapTerrain.getScale(i19) : 1;
    }
    return null;
  }
  _graphicVisible(e33) {
    if (!this.layerScaleEnabled) return true;
    const i19 = this._graphicScale(e33);
    return this._visibleAtLayerScale(i19);
  }
  updateVisibility(e33) {
    if (this._scaleRangeActive) {
      const i19 = this._graphicVisible(e33);
      return e33.setVisibilityFlag(C4.SCALE_RANGE, i19, E13.GRAPHIC);
    }
    return false;
  }
  updateGraphicLabelScaleVisibility(e33) {
    if (!this._scaleRangeActive) return false;
    if (!e33.labelLayers || 0 === e33.labelLayers.length) return false;
    const i19 = this._graphicScale(e33), t28 = this._updateLabelScaleVisibility(e33, i19);
    return t28 && (this.graphicsCoreOwner.view.deconflictor.setDirty(), this.graphicsCoreOwner.view.labeler.setDirty()), t28;
  }
  _updateLabelScaleVisibility(e33, i19) {
    if (!e33.labelLayers || 0 === e33.labelLayers.length) return false;
    const t28 = e33.labelLayers[0]._labelClass;
    if (t28 && null != t28.minScale && null != t28.maxScale) {
      const r30 = this._visibleAtLabelScale(i19, t28);
      if (e33.setVisibilityFlag(C4.SCALE_RANGE, r30, E13.LABEL)) return true;
    }
    return false;
  }
  _scaleUpdateHandler(e33) {
    if (this._setDirty(), !this.graphicsCore.visible) return;
    const i19 = e33.extent, t28 = e33.scale, s26 = this._visibleAtLayerScale(t28);
    let l34 = false;
    const c34 = this.graphicsCoreOwner.view.spatialReference, p23 = e33.spatialReference;
    if (t(p23)) return void u31.error("scaleUpdate: Internal error, no SpatialReference given for tiles");
    const y14 = !p23.equals(c34);
    if (y14) {
      if (!vn(i19, p23, S18, c34)) return void u31.error("scaleUpdate: Internal error, cannot project AABR from " + p23 + " to wkid " + c34);
    }
    const d26 = y14 ? S18 : i19;
    this.queryGraphicUIDsInExtent(d26, c34, (e34) => {
      const n26 = this.graphicsCore.getGraphics3DGraphicById(e34);
      if (t(n26)) return;
      const c35 = n26.centroid;
      r(c35) && (i19[0] > c35.x || i19[1] > c35.y || i19[2] < c35.x || i19[3] < c35.y) || (n26.setVisibilityFlag(C4.SCALE_RANGE, s26, E13.GRAPHIC) && (l34 = true), this._updateLabelScaleVisibility(n26, t28) && (l34 = true));
    }), l34 && (this.graphicsCoreOwner.view.deconflictor.setDirty(), this.graphicsCoreOwner.view.labeler.setDirty());
  }
  layerMinMaxScaleChangeHandler() {
    this.updateScaleRangeActive() && !this._scaleRangeActive ? this.graphicsCore.modifyGraphics3DGraphicVisibilities((e33) => e33.clearVisibilityFlag(C4.SCALE_RANGE)) : this._scaleRangeActive && this.graphicsCore.updateAllGraphicsVisibility(), this._setDirty();
  }
};
function b23(e33, i19) {
  return e33 > 0 || i19 > 0;
}
e([y2()], g21.prototype, "graphicsCoreOwner", void 0), e([y2()], g21.prototype, "layer", void 0), e([y2()], g21.prototype, "queryGraphicUIDsInExtent", void 0), e([y2()], g21.prototype, "graphicsCore", void 0), e([y2()], g21.prototype, "basemapTerrain", void 0), e([y2({ constructOnly: true })], g21.prototype, "layerScaleEnabled", void 0), e([y2({ readOnly: true })], g21.prototype, "suspended", void 0), e([y2({ readOnly: true })], g21.prototype, "updating", null), e([y2()], g21.prototype, "_dirty", void 0), g21 = e([a2("esri.views.3d.layers.graphics.Graphics3DScaleVisibility")], g21);
var m17 = "terrain-events";
var S18 = u4();
var _19 = g21;

// node_modules/@arcgis/core/views/3d/layers/graphics/GraphicsProcessor.js
var A12 = class extends d4 {
  constructor(t28) {
    super(t28), this.type = "graphics-3d", this.graphicsCore = null, this.drapeSourceType = e16.Features, this.scaleVisibilityEnabled = false, this.frustumVisibilityEnabled = false, this._suspendResumeExtent = null;
  }
  initialize() {
    const { layer: t28 } = this, e33 = "effectiveScaleRange" in t28 ? t28 : null, i19 = this.scaleVisibilityEnabled && r(e33), r30 = new ke({ owner: this, layer: this.owner.layer, preferredUpdatePolicy: C2.SYNC, graphicSymbolSupported: true, componentFactories: { elevationAlignment: (t29, e34) => new g19({ graphicsCoreOwner: this, graphicsCore: t29, queryGraphicUIDsInExtent: e34, elevationProvider: this.view.elevationProvider }), scaleVisibility: i19 ? (t29, i20) => new _19({ graphicsCoreOwner: this, layer: e33, queryGraphicUIDsInExtent: i20, graphicsCore: t29, basemapTerrain: this.owner.view.basemapTerrain }) : null, objectStates: (t29) => new s24(t29) } });
    if (this._set("graphicsCore", r30), this.frustumVisibilityEnabled && this._set("frustumVisibility", new m16({ graphicsCoreOwner: this })), "fullOpacity" in this.owner) {
      const t29 = this.owner;
      this.updatingHandles.add(() => t29.fullOpacity, () => this.graphicsCore.opacityChange());
    }
    if ("elevationInfo" in t28) {
      const e34 = t28;
      this.updatingHandles.add(() => e34.elevationInfo, (t29, e35) => {
        m4(t29, e35) && this.updatingHandles.addPromise(this.graphicsCore.elevationInfoChange());
      });
    }
    this._set("initializePromise", this._initializeAsync()), this.updatingHandles.addPromise(this.initializePromise);
  }
  async _initializeAsync() {
    try {
      await this.graphicsCore.initializePromise;
    } catch (t28) {
      if (j(t28)) return;
      throw t28;
    }
    this.destroyed || (this.handles.add(l6(() => this.view.clippingArea, () => this._updateClippingExtent(), U)), this._updateClippingExtent(), this._setupSuspendResumeExtent(), this.graphicsCore.startCreateGraphics());
  }
  destroy() {
    this.handles.removeAll(), this.updatingHandles.removeAll(), this._set("frustumVisibility", a(this.frustumVisibility)), this._set("graphicsCore", a(this.graphicsCore));
  }
  get layer() {
    return this.owner.layer;
  }
  get view() {
    return this.owner.view;
  }
  get scaleVisibility() {
    var _a;
    return (_a = this.graphicsCore) == null ? void 0 : _a.scaleVisibility;
  }
  get elevationAlignment() {
    var _a;
    return (_a = this.graphicsCore) == null ? void 0 : _a.elevationAlignment;
  }
  get objectStates() {
    var _a;
    return (_a = this.graphicsCore) == null ? void 0 : _a.objectStates;
  }
  get scaleVisibilitySuspended() {
    return !(!r(this.scaleVisibility) || !this.scaleVisibility.suspended);
  }
  get frustumVisibilitySuspended() {
    return r(this.frustumVisibility) && this.frustumVisibility.suspended;
  }
  get suspended() {
    return this.owner.suspended ?? false;
  }
  get updating() {
    var _a;
    return !!(((_a = this.graphicsCore) == null ? void 0 : _a.updating) || r(this.scaleVisibility) && this.scaleVisibility.updating || r(this.frustumVisibility) && this.frustumVisibility.updating || this.updatingHandles.updating);
  }
  get graphics3DGraphics() {
    var _a;
    return (_a = this.graphicsCore) == null ? void 0 : _a.graphics3DGraphics;
  }
  get graphics3DGraphicsByObjectID() {
    var _a;
    return (_a = this.graphicsCore) == null ? void 0 : _a.graphics3DGraphicsByObjectID;
  }
  get loadedGraphics() {
    return this.owner.loadedGraphics;
  }
  get fullOpacity() {
    return this.owner.fullOpacity ?? 1;
  }
  get slicePlaneEnabled() {
    return this.owner.slicePlaneEnabled;
  }
  get updatePolicy() {
    return this.owner.updatePolicy;
  }
  notifyGraphicGeometryChanged(t28) {
    this.graphicsCore.notifyGraphicGeometryChanged(t28);
  }
  notifyGraphicVisibilityChanged(t28) {
    this.graphicsCore.notifyGraphicVisibilityChanged(t28);
  }
  getRenderingInfo(t28, e33, i19) {
    const r30 = a24(t28, { renderer: e33, arcade: i19 });
    if (r(r30) && r30.color) {
      const t29 = r30.color;
      t29[0] = t29[0] / 255, t29[1] = t29[1] / 255, t29[2] = t29[2] / 255;
    }
    return r30;
  }
  getRenderingInfoAsync(t28, e33, i19, r30) {
    return s15(t28, { renderer: e33, arcade: i19, ...r30 });
  }
  getHit(t28) {
    if (this.owner.loadedGraphics) {
      const e33 = this.owner.loadedGraphics.find((e34) => e34.uid === t28);
      if (e33) {
        const t29 = this.layer instanceof b5 ? this.layer : null, i19 = u13(e33, t29);
        return { type: "graphic", graphic: i19, layer: i19.layer };
      }
    }
    return null;
  }
  whenGraphicBounds(t28, e33) {
    return this.graphicsCore ? this.graphicsCore.whenGraphicBounds(t28, e33) : Promise.reject();
  }
  computeAttachmentOrigin(t28, e33) {
    return this.graphicsCore ? this.graphicsCore.computeAttachmentOrigin(t28, e33) : null;
  }
  getSymbolLayerSize(t28, e33) {
    return this.graphicsCore ? this.graphicsCore.getSymbolLayerSize(t28, e33) : null;
  }
  maskOccludee(t28) {
    const { set: e33, handle: i19 } = this.objectStates.acquireSet(t12.MaskOccludee, null);
    return this.objectStates.setUid(e33, t28.uid), i19;
  }
  highlight(t28) {
    if (t28 instanceof x5) return _20;
    if ("number" == typeof t28) return this.highlight([t28]);
    if (t28 instanceof g6) return this.highlight([t28]);
    if (t28 instanceof j2 && (t28 = t28.toArray()), Array.isArray(t28) && t28.length > 0) {
      if (t28[0] instanceof g6) {
        const e33 = t28.map((t29) => t29.uid), { set: i19, handle: r30 } = this.objectStates.acquireSet(t12.Highlight, null);
        return this.objectStates.setUids(i19, e33), r30;
      }
      if ("number" == typeof t28[0]) {
        const e33 = t28, { set: i19, handle: r30 } = this.objectStates.acquireSet(t12.Highlight, null);
        return this.objectStates.setObjectIds(i19, e33), r30;
      }
    }
    return _20;
  }
  _setupSuspendResumeExtent() {
    const { scaleVisibility: t28, frustumVisibility: e33 } = this;
    if (t(t28) && t(e33)) return;
    const i19 = ({ computedExtent: i20, extentPadding: r30 }) => {
      this._suspendResumeExtent = k4(i20, this._suspendResumeExtent, s16, r30), r(t28) && t28.setExtent(this._suspendResumeExtent), r(e33) && e33.setExtent(this._suspendResumeExtent);
    };
    this.handles.add(l6(() => {
      var _a, _b;
      return { computedExtent: (_a = this.graphicsCore) == null ? void 0 : _a.computedExtent, extentPadding: (_b = this.graphicsCore) == null ? void 0 : _b.extentPadding };
    }, (t29) => i19(t29), w5));
  }
  _updateClippingExtent() {
    const t28 = this.view.clippingArea;
    this.graphicsCore.setClippingExtent(t28, this.view.spatialReference) && this.graphicsCore.recreateAllGraphics();
  }
};
e([y2()], A12.prototype, "type", void 0), e([y2({ constructOnly: true })], A12.prototype, "owner", void 0), e([y2()], A12.prototype, "layer", null), e([y2()], A12.prototype, "view", null), e([y2({ constructOnly: true })], A12.prototype, "graphicsCore", void 0), e([y2()], A12.prototype, "scaleVisibility", null), e([y2({ constructOnly: true })], A12.prototype, "frustumVisibility", void 0), e([y2()], A12.prototype, "elevationAlignment", null), e([y2()], A12.prototype, "objectStates", null), e([y2()], A12.prototype, "scaleVisibilitySuspended", null), e([y2({ readOnly: true })], A12.prototype, "frustumVisibilitySuspended", null), e([y2()], A12.prototype, "suspended", null), e([y2({ readOnly: true })], A12.prototype, "updating", null), e([y2()], A12.prototype, "loadedGraphics", null), e([y2()], A12.prototype, "fullOpacity", null), e([y2()], A12.prototype, "slicePlaneEnabled", null), e([y2()], A12.prototype, "drapeSourceType", void 0), e([y2()], A12.prototype, "updatePolicy", null), e([y2({ constructOnly: true })], A12.prototype, "scaleVisibilityEnabled", void 0), e([y2({ constructOnly: true })], A12.prototype, "frustumVisibilityEnabled", void 0), e([y2()], A12.prototype, "initializePromise", void 0), A12 = e([a2("esri.views.3d.layers.graphics.GraphicsProcessor")], A12);
var _20 = n3();

// node_modules/@arcgis/core/views/3d/layers/graphics/queryForSymbologySnapping.js
async function a34(a35, o24, i19) {
  if (t(a35) || 0 === o24.candidates.length) return s25;
  const c34 = a35.graphics3DGraphicsByObjectID ?? a35.graphics3DGraphics, p23 = [], d26 = [], { renderer: u32 } = a35, g23 = r(u32) && "arcadeRequired" in u32 && u32.arcadeRequired ? i9() : null, l34 = async (n26, { graphic: r30, graphics3DSymbol: t28 }) => {
    const s26 = await g23, o25 = await a35.getRenderingInfoAsync(r30, u32, s26, { signal: i19 });
    return t(o25) ? [] : t28.queryForSnapping(n26, f27, o25, i19);
  }, { candidates: h25, spatialReference: f27 } = o24;
  for (let n26 = 0; n26 < h25.length; ++n26) {
    const r30 = h25[n26], { objectId: t28 } = r30, a36 = "number" == typeof t28 ? c34 == null ? void 0 : c34.get(t28) : void 0;
    if (t(a36)) continue;
    const { graphics3DSymbol: s26 } = a36;
    s26.symbologySnappingSupported && (p23.push(l34(r30, a36)), d26.push(n26));
  }
  if (0 === p23.length) return s25;
  const m18 = await Promise.all(p23);
  f(i19);
  const y14 = [], b24 = [];
  for (let e33 = 0; e33 < m18.length; ++e33) {
    const n26 = m18[e33], r30 = d26[e33];
    for (const e34 of n26) y14.push(e34), b24.push(r30);
  }
  return { candidates: y14, sourceCandidateIndices: b24 };
}
var s25 = { candidates: [], sourceCandidateIndices: [] };

// node_modules/@arcgis/core/views/3d/layers/support/projectExtentUtils.js
function l33(l34) {
  const s26 = l34.view.spatialReference, i19 = l34.layer.fullExtent, n26 = r(i19) && i19.spatialReference;
  if (t(i19) || !n26) return Promise.resolve(null);
  if (n26.equals(s26)) return Promise.resolve(i19.clone());
  const a35 = M(i19, s26);
  return r(a35) ? Promise.resolve(a35) : l34.view.state.isLocal ? a17(i19, s26, l34.layer.portalItem).then((e33) => !l34.destroyed && e33 ? e33 : null).catch(() => null) : Promise.resolve(null);
}

// node_modules/@arcgis/core/views/3d/layers/GraphicsLayerView3D.js
var y13 = class extends n15(u12) {
  constructor() {
    super(...arguments), this.type = "graphics-3d", this.symbologySnappingSupported = true, this._slicePlaneEnabled = false, this.fullExtentInLocalViewSpatialReference = null;
  }
  initialize() {
    this._set("processor", new A12({ owner: this, scaleVisibilityEnabled: true, frustumVisibilityEnabled: true })), this.addResolvingPromise(this.processor.initializePromise), this.handles.add(this.layer.on("graphic-update", (e33) => this.processor.graphicsCore.graphicUpdateHandler(e33))), this.addResolvingPromise(l33(this).then((e33) => this.fullExtentInLocalViewSpatialReference = e33)), this.layer.internal ? this.notifyChange("updating") : this.handles.add(f5(() => {
      var _a, _b;
      return (_b = (_a = this.view) == null ? void 0 : _a.basemapTerrain) == null ? void 0 : _b.ready;
    }, () => () => this.notifyChange("updating"), { once: true }));
  }
  destroy() {
    this.handles.removeAll(), this.updatingHandles.removeAll(), this._set("processor", a(this.processor));
  }
  get loadedGraphics() {
    return this.layer.graphics;
  }
  get legendEnabled() {
    var _a;
    return this.canResume() && !((_a = this.processor) == null ? void 0 : _a.frustumVisibilitySuspended);
  }
  get slicePlaneEnabled() {
    const e33 = this.layer.internal;
    return this._slicePlaneEnabled && !e33;
  }
  set slicePlaneEnabled(e33) {
    this._slicePlaneEnabled = e33;
  }
  getSuspendInfo() {
    var _a, _b;
    const e33 = super.getSuspendInfo();
    return e33.outsideScaleRange = ((_a = this.processor) == null ? void 0 : _a.scaleVisibilitySuspended) ?? false, e33.outsideOfView = ((_b = this.processor) == null ? void 0 : _b.frustumVisibilitySuspended) ?? false, e33;
  }
  async fetchPopupFeatures(e33, r30) {
    var _a;
    return ((_a = e2(r30)) == null ? void 0 : _a.clientGraphics) ?? [];
  }
  getHit(e33) {
    return this.processor.getHit(e33);
  }
  whenGraphicBounds(e33, r30) {
    return this.processor.whenGraphicBounds(e33, r30);
  }
  computeAttachmentOrigin(e33, r30) {
    var _a;
    return (_a = this.processor) == null ? void 0 : _a.computeAttachmentOrigin(e33, r30);
  }
  getSymbolLayerSize(e33, r30) {
    return this.processor.getSymbolLayerSize(e33, r30);
  }
  queryGraphics() {
    return Promise.resolve(this.loadedGraphics);
  }
  maskOccludee(e33) {
    return this.processor.maskOccludee(e33);
  }
  highlight(e33) {
    return this.processor.highlight(e33);
  }
  async elevationAlignPointsInFeatures(e33, s26) {
    const { processor: i19 } = this;
    if (t(i19) || t(i19.graphics3DGraphics)) throw new s2("graphicslayerview3d:missing-processor", "A Graphics3D processor is needed to resolve graphics elevation.");
    const { graphics3DGraphics: o24 } = i19, a35 = (e34) => "number" == typeof e34 ? o24.get(e34) : void 0;
    return m8(this.view, this.layer, a35, e33, s26);
  }
  async queryForSymbologySnapping(e33, r30) {
    return a34(this.processor, e33, r30);
  }
  get updatePolicy() {
    var _a;
    return ((_a = this.processor) == null ? void 0 : _a.graphicsCore.effectiveUpdatePolicy) || C2.SYNC;
  }
  canResume() {
    var _a;
    return super.canResume() && !((_a = this.processor) == null ? void 0 : _a.scaleVisibilitySuspended);
  }
  isUpdating() {
    var _a, _b, _c;
    return !(!((_a = this.processor) == null ? void 0 : _a.updating) && (this.layer.internal || ((_c = (_b = this.view) == null ? void 0 : _b.basemapTerrain) == null ? void 0 : _c.ready)));
  }
  get performanceInfo() {
    var _a, _b;
    return { displayedNumberOfFeatures: this.loadedGraphics.length, maximumNumberOfFeatures: -1, totalNumberOfFeatures: -1, nodes: 0, core: null, updating: this.updating, elevationUpdating: ((_a = this.processor) == null ? void 0 : _a.elevationAlignment.updating) ?? false, visibilityFrustum: !((_b = this.processor) == null ? void 0 : _b.frustumVisibilitySuspended) };
  }
  getUsedMemory() {
    var _a, _b;
    return ((_b = (_a = this.processor) == null ? void 0 : _a.graphicsCore) == null ? void 0 : _b.usedMemory) ?? 0;
  }
  getUnloadedMemory() {
    var _a, _b;
    return (_b = (_a = this.processor) == null ? void 0 : _a.graphicsCore) == null ? void 0 : _b.unprocessedMemoryEstimate;
  }
  ignoresMemoryFactor() {
    return true;
  }
};
e([y2()], y13.prototype, "loadedGraphics", null), e([y2({ readOnly: true })], y13.prototype, "legendEnabled", null), e([y2()], y13.prototype, "layer", void 0), e([y2({ readOnly: true })], y13.prototype, "processor", void 0), e([y2()], y13.prototype, "_slicePlaneEnabled", void 0), e([y2({ type: Boolean })], y13.prototype, "slicePlaneEnabled", null), y13 = e([a2("esri.views.3d.layers.GraphicsLayerView3D")], y13);
var g22 = y13;
export {
  g22 as default
};
//# sourceMappingURL=GraphicsLayerView3D-D3IE67BX.js.map
