{"version": 3, "sources": ["../../@arcgis/core/geometry/geometryEngine.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{G as n}from\"../chunks/geometryEngineBase.js\";import{hydratedAdapter as e}from\"./geometryAdapters/hydrated.js\";function t(n){return Array.isArray(n)?n[0].spatialReference:n&&n.spatialReference}function r(e){return n.extendedSpatialReferenceInfo(e)}function u(r,u){return n.clip(e,t(r),r,u)}function i(r,u){return n.cut(e,t(r),r,u)}function c(r,u){return n.contains(e,t(r),r,u)}function o(r,u){return n.crosses(e,t(r),r,u)}function f(r,u,i){return n.distance(e,t(r),r,u,i)}function s(r,u){return n.equals(e,t(r),r,u)}function a(r,u){return n.intersects(e,t(r),r,u)}function l(r,u){return n.touches(e,t(r),r,u)}function p(r,u){return n.within(e,t(r),r,u)}function d(r,u){return n.disjoint(e,t(r),r,u)}function m(r,u){return n.overlaps(e,t(r),r,u)}function g(r,u,i){return n.relate(e,t(r),r,u,i)}function h(r){return n.isSimple(e,t(r),r)}function w(r){return n.simplify(e,t(r),r)}function R(r,u=!1){return n.convexHull(e,t(r),r,u)}function x(r,u){return n.difference(e,t(r),r,u)}function y(r,u){return n.symmetricDifference(e,t(r),r,u)}function S(r,u){return n.intersect(e,t(r),r,u)}function A(r,u=null){return n.union(e,t(r),r,u)}function D(r,u,i,c,o,f){return n.offset(e,t(r),r,u,i,c,o,f)}function j(r,u,i,c=!1){return n.buffer(e,t(r),r,u,i,c)}function E(r,u,i,c,o,f){return n.geodesicBuffer(e,t(r),r,u,i,c,o,f)}function J(r,u,i=!0){return n.nearestCoordinate(e,t(r),r,u,i)}function L(r,u){return n.nearestVertex(e,t(r),r,u)}function N(r,u,i,c){return n.nearestVertices(e,t(r),r,u,i,c)}function O(n){return\"xmin\"in n?\"center\"in n?n.center:null:\"x\"in n?n:\"extent\"in n?n.extent?.center??null:null}function T(e,t,r){if(null==e)throw new F;const u=e.spatialReference;if(null==(r=r??O(e)))throw new F;const i=e.constructor.fromJSON(n.rotate(e,t,r));return i.spatialReference=u,i}function V(e,t){if(null==e)throw new F;const r=e.spatialReference;if(null==(t=t??O(e)))throw new F;const u=e.constructor.fromJSON(n.flipHorizontal(e,t));return u.spatialReference=r,u}function v(e,t){if(null==e)throw new F;const r=e.spatialReference;if(null==(t=t??O(e)))throw new F;const u=e.constructor.fromJSON(n.flipVertical(e,t));return u.spatialReference=r,u}function z(r,u,i,c){return n.generalize(e,t(r),r,u,i,c)}function B(r,u,i){return n.densify(e,t(r),r,u,i)}function H(r,u,i,c=0){return n.geodesicDensify(e,t(r),r,u,i,c)}function I(r,u){return n.planarArea(e,t(r),r,u)}function b(r,u){return n.planarLength(e,t(r),r,u)}function k(r,u,i){return n.geodesicArea(e,t(r),r,u,i)}function q(r,u,i){return n.geodesicLength(e,t(r),r,u,i)}function C(r,u){return n.intersectLinesToPoints(e,t(r),r,u)}function G(e,t){n.changeDefaultSpatialReferenceTolerance(e,t)}function P(e){n.clearDefaultSpatialReferenceTolerance(e)}class F extends Error{constructor(){super(\"Illegal Argument Exception\")}}export{j as buffer,G as changeDefaultSpatialReferenceTolerance,P as clearDefaultSpatialReferenceTolerance,u as clip,c as contains,R as convexHull,o as crosses,i as cut,B as densify,x as difference,d as disjoint,f as distance,s as equals,r as extendedSpatialReferenceInfo,V as flipHorizontal,v as flipVertical,z as generalize,k as geodesicArea,E as geodesicBuffer,H as geodesicDensify,q as geodesicLength,S as intersect,C as intersectLinesToPoints,a as intersects,h as isSimple,J as nearestCoordinate,L as nearestVertex,N as nearestVertices,D as offset,m as overlaps,I as planarArea,b as planarLength,g as relate,T as rotate,w as simplify,y as symmetricDifference,l as touches,A as union,p as within};\n"], "mappings": ";;;;;;;;AAIqH,SAAS,EAAE,GAAE;AAAC,SAAO,MAAM,QAAQ,CAAC,IAAE,EAAE,CAAC,EAAE,mBAAiB,KAAG,EAAE;AAAgB;AAAC,SAASA,GAAE,GAAE;AAAC,SAAO,EAAE,6BAA6B,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAO,EAAE,KAAK,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,IAAI,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,SAAS,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,QAAQ,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,SAAS,GAAE,EAAEF,EAAC,GAAEA,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAASC,GAAEH,IAAEC,IAAE;AAAC,SAAO,EAAE,OAAO,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,WAAW,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,QAAQ,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,OAAO,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,SAAS,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,SAAS,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,OAAO,GAAE,EAAEF,EAAC,GAAEA,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAE;AAAC,SAAO,EAAE,SAAS,GAAE,EAAEA,EAAC,GAAEA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAE,SAAS,GAAE,EAAEA,EAAC,GAAEA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,KAAE,OAAG;AAAC,SAAO,EAAE,WAAW,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,WAAW,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,oBAAoB,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,UAAU,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,KAAE,MAAK;AAAC,SAAO,EAAE,MAAM,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,OAAO,GAAE,EAAEN,EAAC,GAAEA,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEN,IAAEC,IAAEC,IAAEE,KAAE,OAAG;AAAC,SAAO,EAAE,OAAO,GAAE,EAAEJ,EAAC,GAAEA,IAAEC,IAAEC,IAAEE,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,eAAe,GAAE,EAAEN,EAAC,GAAEA,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEN,IAAEC,IAAEC,KAAE,MAAG;AAAC,SAAO,EAAE,kBAAkB,GAAE,EAAEF,EAAC,GAAEA,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAE;AAAC,SAAO,EAAE,cAAc,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAEE,IAAE;AAAC,SAAO,EAAE,gBAAgB,GAAE,EAAEJ,EAAC,GAAEA,IAAEC,IAAEC,IAAEE,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAJh+C;AAIi+C,SAAM,UAAS,IAAE,YAAW,IAAE,EAAE,SAAO,OAAK,OAAM,IAAE,IAAE,YAAW,MAAE,OAAE,WAAF,mBAAU,WAAQ,OAAK;AAAI;AAAC,SAAS,EAAE,GAAEG,IAAEP,IAAE;AAAC,MAAG,QAAM,EAAE,OAAM,IAAI;AAAE,QAAMC,KAAE,EAAE;AAAiB,MAAG,SAAOD,KAAEA,MAAG,EAAE,CAAC,GAAG,OAAM,IAAI;AAAE,QAAME,KAAE,EAAE,YAAY,SAAS,EAAE,OAAO,GAAEK,IAAEP,EAAC,CAAC;AAAE,SAAOE,GAAE,mBAAiBD,IAAEC;AAAC;AAAC,SAAS,EAAE,GAAEK,IAAE;AAAC,MAAG,QAAM,EAAE,OAAM,IAAI;AAAE,QAAMP,KAAE,EAAE;AAAiB,MAAG,SAAOO,KAAEA,MAAG,EAAE,CAAC,GAAG,OAAM,IAAI;AAAE,QAAMN,KAAE,EAAE,YAAY,SAAS,EAAE,eAAe,GAAEM,EAAC,CAAC;AAAE,SAAON,GAAE,mBAAiBD,IAAEC;AAAC;AAAC,SAAS,EAAE,GAAEM,IAAE;AAAC,MAAG,QAAM,EAAE,OAAM,IAAI;AAAE,QAAMP,KAAE,EAAE;AAAiB,MAAG,SAAOO,KAAEA,MAAG,EAAE,CAAC,GAAG,OAAM,IAAI;AAAE,QAAMN,KAAE,EAAE,YAAY,SAAS,EAAE,aAAa,GAAEM,EAAC,CAAC;AAAE,SAAON,GAAE,mBAAiBD,IAAEC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAEE,IAAE;AAAC,SAAO,EAAE,WAAW,GAAE,EAAEJ,EAAC,GAAEA,IAAEC,IAAEC,IAAEE,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,QAAQ,GAAE,EAAEF,EAAC,GAAEA,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAEE,KAAE,GAAE;AAAC,SAAO,EAAE,gBAAgB,GAAE,EAAEJ,EAAC,GAAEA,IAAEC,IAAEC,IAAEE,EAAC;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAE;AAAC,SAAO,EAAE,WAAW,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,EAAE,aAAa,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,aAAa,GAAE,EAAEF,EAAC,GAAEA,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,eAAe,GAAE,EAAEF,EAAC,GAAEA,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAE;AAAC,SAAO,EAAE,uBAAuB,GAAE,EAAED,EAAC,GAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEM,IAAE;AAAC,IAAE,uCAAuC,GAAEA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,IAAE,sCAAsC,CAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,MAAK;AAAA,EAAC,cAAa;AAAC,UAAM,4BAA4B;AAAA,EAAC;AAAC;", "names": ["r", "u", "i", "s", "c", "o", "f", "t"]}