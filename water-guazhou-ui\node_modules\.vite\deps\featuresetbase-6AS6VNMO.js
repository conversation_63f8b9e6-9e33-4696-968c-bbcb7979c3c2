import {
  g
} from "./chunk-HZJM3QX3.js";
import {
  l
} from "./chunk-XBLAL33X.js";
import {
  t as t4
} from "./chunk-YKSBY2H7.js";
import {
  t as t3
} from "./chunk-QIGFPFKT.js";
import {
  A as A2,
  A2 as A3,
  C as C3,
  D,
  E,
  E2,
  I as I2,
  S,
  a,
  a2,
  c as c2,
  e as e2,
  e2 as e3,
  f as f2,
  j,
  x,
  y as y2
} from "./chunk-7PERKTX5.js";
import "./chunk-YU2YCWN6.js";
import {
  T
} from "./chunk-VX64NK2J.js";
import {
  A,
  u2 as u
} from "./chunk-3XKBAFAG.js";
import "./chunk-3A5PKKOG.js";
import "./chunk-T3GGN2P7.js";
import "./chunk-CF4Y76HG.js";
import {
  B,
  C as C2,
  G,
  I2 as I,
  J,
  <PERSON>,
  U,
  V,
  Z,
  Ze,
  fe,
  le,
  o,
  re,
  t as t2,
  ue,
  v,
  z
} from "./chunk-REVHHZEO.js";
import {
  c
} from "./chunk-6OFWBRK2.js";
import "./chunk-WC4DQSYX.js";
import {
  f
} from "./chunk-2CLVPBYJ.js";
import {
  e,
  t
} from "./chunk-YFVPK4WM.js";
import "./chunk-5VSS44JR.js";
import "./chunk-PX6TFO4X.js";
import "./chunk-UMW4I2EJ.js";
import "./chunk-XZ2UVSB4.js";
import "./chunk-NSJUSNRV.js";
import "./chunk-YMY3DTA5.js";
import "./chunk-TNP2LXZZ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import {
  He
} from "./chunk-33Z6JDMT.js";
import "./chunk-L7LRY3AT.js";
import "./chunk-ZJC3GHA7.js";
import "./chunk-TVTTDN54.js";
import "./chunk-O4T45CJC.js";
import "./chunk-NNKS4NNY.js";
import "./chunk-LNCHRZJI.js";
import "./chunk-OA2XSLRZ.js";
import "./chunk-CIHGHHEZ.js";
import "./chunk-G3QAWKCD.js";
import "./chunk-FZKLUDSB.js";
import "./chunk-IKGI4J4I.js";
import "./chunk-MNWHGD3K.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-6GKVSPTV.js";
import "./chunk-4FIRBBKR.js";
import "./chunk-EKOSN3EW.js";
import "./chunk-RZCOX454.js";
import "./chunk-2WMCP27R.js";
import "./chunk-UVJUTW2U.js";
import "./chunk-RR74IWZB.js";
import "./chunk-KTB2COPC.js";
import "./chunk-FWXA4I6D.js";
import "./chunk-NQ3OACUM.js";
import "./chunk-HTXGAKOK.js";
import "./chunk-WL6G2MRC.js";
import "./chunk-OQK7L3JR.js";
import "./chunk-JZKMTUDN.js";
import "./chunk-UCWK623G.js";
import "./chunk-JV6TBH5W.js";
import "./chunk-77ZF73NA.js";
import "./chunk-55WN4LCX.js";
import "./chunk-VSFGOST3.js";
import "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-ZVU4V5QV.js";
import "./chunk-2AZSZWPE.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-KE7SPCM7.js";
import {
  y
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-MIA6BJ32.js";
import "./chunk-NGPCXWDX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FBVKALLT.js";
import "./chunk-6PEIQDFP.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-BS3GJQ77.js";
import "./chunk-IOBN373Z.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-NEJXVYTI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-VJW7RCN7.js";
import "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-ZL6CFFJK.js";
import "./chunk-TWFTBWXP.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  C
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/arcade/functions/featuresetbase.js
function Q(e4, t5, n, i) {
  if (1 === i.length) {
    if (J(i[0])) return l(e4, i[0], -1);
    if (V(i[0])) return l(e4, i[0].toArray(), -1);
  }
  return l(e4, i, -1);
}
async function J2(e4, t5, n) {
  const i = e4.getVariables();
  if (i.length > 0) {
    const a3 = [];
    for (let e5 = 0; e5 < i.length; e5++) {
      const r2 = { name: i[e5] };
      a3.push(await t5.evaluateIdentifier(n, r2));
    }
    const r = {};
    for (let e5 = 0; e5 < i.length; e5++) r[i[e5]] = a3[e5];
    return e4.parameters = r, e4;
  }
  return e4;
}
function K(e4, t5, n = null) {
  for (const i in e4) if (i.toLowerCase() === t5.toLowerCase()) return e4[i];
  return n;
}
function X(e4) {
  if (null === e4) return null;
  const t5 = { type: K(e4, "type", ""), name: K(e4, "name", "") };
  if ("range" === t5.type) t5.range = K(e4, "range", []);
  else {
    t5.codedValues = [];
    for (const n of K(e4, "codedValues", [])) t5.codedValues.push({ name: K(n, "name", ""), code: K(n, "code", null) });
  }
  return t5;
}
function Y(e4) {
  if (null === e4) return null;
  const t5 = {}, n = K(e4, "wkt", null);
  null !== n && (t5.wkt = n);
  const i = K(e4, "wkid", null);
  return null !== i && (t5.wkid = i), t5;
}
function ee(e4) {
  if (null === e4) return null;
  const t5 = { hasZ: K(e4, "hasz", false), hasM: K(e4, "hasm", false) }, n = K(e4, "spatialreference", null);
  n && (t5.spatialReference = Y(n));
  const i = K(e4, "x", null);
  if (null !== i) return t5.x = i, t5.y = K(e4, "y", null), t5;
  const a3 = K(e4, "rings", null);
  if (null !== a3) return t5.rings = a3, t5;
  const r = K(e4, "paths", null);
  if (null !== r) return t5.paths = r, t5;
  const s = K(e4, "points", null);
  if (null !== s) return t5.points = s, t5;
  for (const o2 of ["xmin", "xmax", "ymin", "ymax", "zmin", "zmax", "mmin", "mmax"]) {
    const n2 = K(e4, o2, null);
    null !== n2 && (t5[o2] = n2);
  }
  return t5;
}
function te(e4, t5) {
  for (const n of t5) if (n === e4) return true;
  return false;
}
function ne(e4) {
  return !!e4.layerDefinition && (!!e4.featureSet && (false !== te(e4.layerDefinition.geometryType, ["", null, "esriGeometryNull", "esriGeometryPoint", "esriGeometryPolyline", "esriGeometryPolygon", "esriGeometryMultipoint", "esriGeometryEnvelope"]) && (null !== e4.layerDefinition.objectIdField && "" !== e4.layerDefinition.objectIdField && (false !== J(e4.layerDefinition.fields) && false !== J(e4.featureSet.features)))));
}
function ie(_) {
  "async" === _.mode && (_.functions.timezone = function(t5, r) {
    return _.standardFunctionAsync(t5, r, async (s, o2, l2) => {
      if (B(l2, 1, 2, t5, r), G(l2[0])) {
        if (await l2[0].load(), 1 === l2.length || null === l2[1]) return l2[0].dateTimeReferenceFieldIndex.layerDateFieldsTimeZone;
        if (!(l2[1] instanceof T) || false === l2[1].hasField("type")) throw new t(t5, e.InvalidParameter, r);
        const e4 = l2[1].field("type");
        if (false === v(e4)) throw new t(t5, e.InvalidParameter, r);
        switch (re(e4).toLowerCase()) {
          case "preferredtimezone":
            return l2[0].dateTimeReferenceFieldIndex.layerPreferredTimeZone;
          case "editfieldsinfo":
            return l2[0].dateTimeReferenceFieldIndex.layerEditFieldsTimeZone;
          case "timeinfo":
            return l2[0].dateTimeReferenceFieldIndex.layerTimeInfoTimeZone;
          case "field":
            if (l2[1].hasField("fieldname") && v(l2[1].field("fieldname"))) return l2[0].dateTimeReferenceFieldIndex.fieldTimeZone(re(l2[1].field("fieldname")));
        }
        throw new t(t5, e.InvalidParameter, r);
      }
      const f3 = ue(l2[0], Ze(t5));
      if (null === f3) return null;
      const c3 = f3.timeZone;
      return "system" === c3 ? c.systemTimeZoneCanonicalName : c3;
    });
  }, _.functions.sqltimestamp = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (n, r, s) => {
      B(s, 1, 3, e4, t5);
      const o2 = s[0];
      if (U(o2)) {
        if (1 === s.length) return o2.toSQLString();
        if (2 === s.length) return o2.changeTimeZone(re(s[1])).toSQLString();
        throw new t(e4, e.InvalidParameter, t5);
      }
      if (G(o2)) {
        if (3 !== s.length) throw new t(e4, e.InvalidParameter, t5);
        await o2.load();
        const n2 = re(s[1]);
        if (false === U(s[2])) throw new t(e4, e.InvalidParameter, t5);
        const r2 = o2.fieldTimeZone(n2);
        return null === r2 ? s[2].toSQLString() : s[2].changeTimeZone(r2).toSQLString();
      }
      throw new t(e4, e.InvalidParameter, t5);
    });
  }, _.signatures.push({ name: "sqltimestamp", min: 2, max: 4 }), _.functions.featuresetbyid = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, (n, r, o2) => {
      if (B(o2, 2, 4, e4, t5), o2[0] instanceof e2) {
        const n2 = re(o2[1]);
        let r2 = Z(o2[2], null);
        const s = fe(Z(o2[3], true));
        if (null === r2 && (r2 = ["*"]), false === J(r2)) throw new t(e4, e.InvalidParameter, t5);
        return o2[0].featureSetById(n2, s, r2);
      }
      throw new t(e4, e.InvalidParameter, t5);
    });
  }, _.signatures.push({ name: "featuresetbyid", min: 2, max: 4 }), _.functions.getfeatureset = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, (n, r, s) => {
      if (B(s, 1, 2, e4, t5), z(s[0])) {
        let t6 = Z(s[1], "datasource");
        return null === t6 && (t6 = "datasource"), t6 = re(t6).toLowerCase(), E2(s[0].fullSchema(), t6, e4.lrucache, e4.interceptor, e4.spatialReference);
      }
      throw new t(e4, e.InvalidParameter, t5);
    });
  }, _.signatures.push({ name: "getfeatureset", min: 1, max: 2 }), _.functions.featuresetbyportalitem = function(e4, n) {
    return _.standardFunctionAsync(e4, n, (r, s, o2) => {
      if (B(o2, 2, 5, e4, n), null === o2[0]) throw new t(e4, e.PortalRequired, n);
      if (o2[0] instanceof t3) {
        const t5 = re(o2[1]), r2 = re(o2[2]);
        let s2 = Z(o2[3], null);
        const f4 = fe(Z(o2[4], true));
        if (null === s2 && (s2 = ["*"]), false === J(s2)) throw new t(e4, e.InvalidParameter, n);
        let c4 = null;
        return e4.services && e4.services.portal && (c4 = e4.services.portal), c4 = t4(o2[0], c4), j(t5, r2, e4.spatialReference, s2, f4, c4, e4.lrucache, e4.interceptor);
      }
      if (false === v(o2[0])) throw new t(e4, e.PortalRequired, n);
      const f3 = re(o2[0]), c3 = re(o2[1]);
      let u2 = Z(o2[2], null);
      const d = fe(Z(o2[3], true));
      if (null === u2 && (u2 = ["*"]), false === J(u2)) throw new t(e4, e.InvalidParameter, n);
      if (e4.services && e4.services.portal) return j(f3, c3, e4.spatialReference, u2, d, e4.services.portal, e4.lrucache, e4.interceptor);
      throw new t(e4, e.PortalRequired, n);
    });
  }, _.signatures.push({ name: "featuresetbyportalitem", min: 2, max: 5 }), _.functions.featuresetbyname = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, (n, r, o2) => {
      if (B(o2, 2, 4, e4, t5), o2[0] instanceof e2) {
        const n2 = re(o2[1]);
        let r2 = Z(o2[2], null);
        const s = fe(Z(o2[3], true));
        if (null === r2 && (r2 = ["*"]), false === J(r2)) throw new t(e4, e.InvalidParameter, t5);
        return o2[0].featureSetByName(n2, s, r2);
      }
      throw new t(e4, e.InvalidParameter, t5);
    });
  }, _.signatures.push({ name: "featuresetbyname", min: 2, max: 4 }), _.functions.featureset = function(e4, t5) {
    return _.standardFunction(e4, t5, (r, s, o2) => {
      var _a;
      B(o2, 1, 1, e4, t5);
      let l2 = o2[0];
      const f3 = { layerDefinition: { geometryType: "", objectIdField: "", globalIdField: "", typeIdField: "", fields: [] }, featureSet: { geometryType: "", features: [] } };
      if (v(l2)) l2 = JSON.parse(l2), void 0 !== l2.layerDefinition ? (f3.layerDefinition = l2.layerDefinition, f3.featureSet = l2.featureSet, l2.layerDefinition.spatialReference && (f3.layerDefinition.spatialReference = l2.layerDefinition.spatialReference)) : (f3.featureSet.features = l2.features, f3.featureSet.geometryType = l2.geometryType, f3.layerDefinition.geometryType = f3.featureSet.geometryType, f3.layerDefinition.objectIdField = l2.objectIdFieldName, f3.layerDefinition.typeIdField = l2.typeIdFieldName, f3.layerDefinition.globalIdField = l2.globalIdFieldName, f3.layerDefinition.fields = l2.fields, l2.spatialReference && (f3.layerDefinition.spatialReference = l2.spatialReference));
      else {
        if (!(o2[0] instanceof T)) throw new t(e4, e.InvalidParameter, t5);
        {
          l2 = JSON.parse(o2[0].castToText(true));
          const e5 = K(l2, "layerdefinition");
          if (null !== e5) {
            f3.layerDefinition.geometryType = K(e5, "geometrytype", ""), f3.featureSet.geometryType = f3.layerDefinition.geometryType, f3.layerDefinition.globalIdField = K(e5, "globalidfield", ""), f3.layerDefinition.objectIdField = K(e5, "objectidfield", ""), f3.layerDefinition.typeIdField = K(e5, "typeidfield", "");
            const t6 = K(e5, "spatialreference", null);
            t6 && (f3.layerDefinition.spatialReference = Y(t6));
            for (const i of K(e5, "fields", [])) {
              const e6 = { name: K(i, "name", ""), alias: K(i, "alias", ""), type: K(i, "type", ""), nullable: K(i, "nullable", true), editable: K(i, "editable", true), length: K(i, "length", null), domain: X(K(i, "domain")) };
              f3.layerDefinition.fields.push(e6);
            }
            const n = K(l2, "featureset", null);
            if (n) {
              const e6 = {};
              for (const t7 of f3.layerDefinition.fields) e6[t7.name.toLowerCase()] = t7.name;
              for (const t7 of K(n, "features", [])) {
                const n2 = {}, i = K(t7, "attributes", {});
                for (const t8 in i) n2[e6[t8.toLowerCase()]] = i[t8];
                f3.featureSet.features.push({ attributes: n2, geometry: ee(K(t7, "geometry", null)) });
              }
            }
          } else {
            f3.layerDefinition.geometryType = K(l2, "geometrytype", ""), f3.featureSet.geometryType = f3.layerDefinition.geometryType, f3.layerDefinition.objectIdField = K(l2, "objectidfieldname", ""), f3.layerDefinition.typeIdField = K(l2, "typeidfieldname", "");
            const e6 = K(l2, "spatialreference", null);
            e6 && (f3.layerDefinition.spatialReference = Y(e6));
            for (const n of K(l2, "fields", [])) {
              const e7 = { name: K(n, "name", ""), alias: K(n, "alias", ""), type: K(n, "type", ""), nullable: K(n, "nullable", true), editable: K(n, "editable", true), length: K(n, "length", null), domain: X(K(n, "domain")) };
              f3.layerDefinition.fields.push(e7);
            }
            const t6 = {};
            for (const n of f3.layerDefinition.fields) t6[n.name.toLowerCase()] = n.name;
            for (const n of K(l2, "features", [])) {
              const e7 = {}, i = K(n, "attributes", {});
              for (const n2 in i) e7[t6[n2.toLowerCase()]] = i[n2];
              f3.featureSet.features.push({ attributes: e7, geometry: ee(K(n, "geometry", null)) });
            }
          }
        }
      }
      if (false === ne(f3)) throw new t(e4, e.InvalidParameter, t5);
      return "" === (((_a = f3 == null ? void 0 : f3.layerDefinition) == null ? void 0 : _a.geometryType) || "") && (f3.layerDefinition.geometryType = "esriGeometryNull"), f2.create(f3, e4.spatialReference);
    });
  }, _.signatures.push({ name: "featureset", min: 1, max: 1 }), _.functions.filter = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (n, r, s) => {
      if (B(s, 2, 2, e4, t5), J(s[0]) || V(s[0])) {
        const n2 = [];
        let r2 = s[0];
        r2 instanceof t2 && (r2 = r2.toArray());
        let o2 = null;
        if (!C2(s[1])) throw new t(e4, e.InvalidParameter, t5);
        o2 = s[1].createFunction(e4);
        for (const e5 of r2) {
          const t6 = o2(e5);
          C(t6) ? true === await t6 && n2.push(e5) : true === t6 && n2.push(e5);
        }
        return n2;
      }
      if (G(s[0])) {
        const t6 = await s[0].load(), n2 = f.create(s[1], t6.getFieldsIndex()), i = n2.getVariables();
        if (i.length > 0) {
          const t7 = [];
          for (let n3 = 0; n3 < i.length; n3++) {
            const a4 = { name: i[n3] };
            t7.push(await _.evaluateIdentifier(e4, a4));
          }
          const a3 = {};
          for (let e5 = 0; e5 < i.length; e5++) a3[i[e5]] = t7[e5];
          return n2.parameters = a3, new c2({ parentfeatureset: s[0], whereclause: n2 });
        }
        return new c2({ parentfeatureset: s[0], whereclause: n2 });
      }
      throw new t(e4, e.InvalidParameter, t5);
    });
  }, _.signatures.push({ name: "filter", min: 2, max: 2 }), _.functions.orderby = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (n, r, s) => {
      if (B(s, 2, 2, e4, t5), G(s[0])) {
        const e5 = new e3(s[1]);
        return new a({ parentfeatureset: s[0], orderbyclause: e5 });
      }
      throw new t(e4, e.InvalidParameter, t5);
    });
  }, _.signatures.push({ name: "orderby", min: 2, max: 2 }), _.functions.top = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (n, r, s) => {
      if (B(s, 2, 2, e4, t5), G(s[0])) return new a2({ parentfeatureset: s[0], topnum: s[1] });
      if (J(s[0])) return le(s[1]) >= s[0].length ? s[0].slice(0) : s[0].slice(0, le(s[1]));
      if (V(s[0])) return le(s[1]) >= s[0].length() ? s[0].slice(0) : s[0].slice(0, le(s[1]));
      throw new t(e4, e.InvalidParameter, t5);
    });
  }, _.signatures.push({ name: "top", min: 2, max: 2 }), _.functions.first = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (n, i, a3) => {
      if (B(a3, 1, 1, e4, t5), G(a3[0])) {
        const t6 = await a3[0].first(n.abortSignal);
        if (null !== t6) {
          const n2 = g.createFromGraphicLikeObject(t6.geometry, t6.attributes, a3[0], e4.timeReference);
          return n2._underlyingGraphic = t6, n2;
        }
        return t6;
      }
      return J(a3[0]) ? 0 === a3[0].length ? null : a3[0][0] : V(a3[0]) ? 0 === a3[0].length() ? null : a3[0].get(0) : null;
    });
  }, _.signatures.push({ name: "first", min: 1, max: 1 }), _.functions.attachments = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (r, s, o2) => {
      B(o2, 1, 2, e4, t5);
      const l2 = { minsize: -1, maxsize: -1, types: null, returnMetadata: false };
      if (o2.length > 1) {
        if (o2[1] instanceof T) {
          if (o2[1].hasField("minsize") && (l2.minsize = le(o2[1].field("minsize"))), o2[1].hasField("metadata") && (l2.returnMetadata = fe(o2[1].field("metadata"))), o2[1].hasField("maxsize") && (l2.maxsize = le(o2[1].field("maxsize"))), o2[1].hasField("types")) {
            const e5 = Me(o2[1].field("types"), false);
            e5.length > 0 && (l2.types = e5);
          }
        } else if (null !== o2[1]) throw new t(e4, e.InvalidParameter, t5);
      }
      if (z(o2[0])) {
        let t6 = o2[0]._layer;
        return t6 instanceof He && (t6 = S(t6, e4.spatialReference, ["*"], true, e4.lrucache, e4.interceptor)), null === t6 ? [] : false === G(t6) ? [] : (await t6.load(), t6.queryAttachments(o2[0].field(t6.objectIdField), l2.minsize, l2.maxsize, l2.types, l2.returnMetadata));
      }
      if (null === o2[0]) return [];
      throw new t(e4, e.InvalidParameter, t5);
    });
  }, _.signatures.push({ name: "attachments", min: 1, max: 2 }), _.functions.featuresetbyrelationshipname = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (n, r, s) => {
      B(s, 2, 4, e4, t5);
      const o2 = s[0], l2 = re(s[1]);
      let d = Z(s[2], null);
      const m = fe(Z(s[3], true));
      if (null === d && (d = ["*"]), false === J(d)) throw new t(e4, e.InvalidParameter, t5);
      if (null === s[0]) return null;
      if (!z(s[0])) throw new t(e4, e.InvalidParameter, t5);
      let h = o2._layer;
      if (h instanceof He && (h = S(h, e4.spatialReference, ["*"], true, e4.lrucache, e4.interceptor)), null === h) return null;
      if (false === G(h)) return null;
      h = await h.load();
      const g2 = h.relationshipMetaData().filter((e5) => e5.name === l2);
      if (0 === g2.length) return null;
      if (void 0 !== g2[0].relationshipTableId && null !== g2[0].relationshipTableId && g2[0].relationshipTableId > -1) return C3(h, g2[0], o2.field(h.objectIdField), h.spatialReference, d, m, e4.lrucache, e4.interceptor);
      let I3 = h.serviceUrl();
      if (!I3) return null;
      I3 = "/" === I3.charAt(I3.length - 1) ? I3 + g2[0].relatedTableId.toString() : I3 + "/" + g2[0].relatedTableId.toString();
      const F = await I2(I3, h.spatialReference, d, m, e4.lrucache, e4.interceptor);
      await F.load();
      let A4 = F.relationshipMetaData();
      if (A4 = A4.filter((e5) => e5.id === g2[0].id), false === o2.hasField(g2[0].keyField) || null === o2.field(g2[0].keyField)) {
        const e5 = await h.getFeatureByObjectId(o2.field(h.objectIdField), [g2[0].keyField]);
        if (e5) {
          const t6 = f.create(A4[0].keyField + "= @id", F.getFieldsIndex());
          return t6.parameters = { id: e5.attributes[g2[0].keyField] }, F.filter(t6);
        }
        return new u({ parentfeatureset: F });
      }
      const N = f.create(A4[0].keyField + "= @id", F.getFieldsIndex());
      return N.parameters = { id: o2.field(g2[0].keyField) }, F.filter(N);
    });
  }, _.signatures.push({ name: "featuresetbyrelationshipname", min: 2, max: 4 }), _.functions.featuresetbyassociation = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (n, r, s) => {
      B(s, 2, 3, e4, t5);
      const o2 = s[0], l2 = re(Z(s[1], "")).toLowerCase(), c3 = v(s[2]) ? re(s[2]) : null;
      if (null === s[0]) return null;
      if (!z(s[0])) throw new t(e4, e.InvalidParameter, t5);
      let u2 = o2._layer;
      if (u2 instanceof He && (u2 = S(u2, e4.spatialReference, ["*"], true, e4.lrucache, e4.interceptor)), null === u2) return null;
      if (false === G(u2)) return null;
      await u2.load();
      const m = u2.serviceUrl(), g2 = await A3(m, e4.spatialReference);
      let I3 = null, F = null, D2 = false;
      if (null !== c3 && "" !== c3 && void 0 !== c3) {
        for (const e5 of g2.terminals) e5.terminalName === c3 && (F = e5.terminalId);
        null === F && (D2 = true);
      }
      const b = g2.associations.getFieldsIndex(), A4 = b.get("TOGLOBALID").name, N = b.get("FROMGLOBALID").name, S2 = b.get("TOTERMINALID").name, T2 = b.get("FROMTERMINALID").name, $ = b.get("FROMNETWORKSOURCEID").name, M = b.get("TONETWORKSOURCEID").name, O = b.get("ASSOCIATIONTYPE").name, k = b.get("ISCONTENTVISIBLE").name, z2 = b.get("OBJECTID").name;
      for (const e5 of u2.fields) if ("global-id" === e5.type) {
        I3 = o2.field(e5.name);
        break;
      }
      let H = null, G2 = new y2(new y({ name: "percentalong", alias: "percentalong", type: "double" }), f.create("0", g2.associations.getFieldsIndex())), W = new y2(new y({ name: "side", alias: "side", type: "string" }), f.create("''", g2.associations.getFieldsIndex()));
      const _2 = "globalid", U2 = "globalId", Q2 = {};
      for (const e5 in g2.lkp) Q2[e5] = g2.lkp[e5].sourceId;
      const J3 = new A2(new y({ name: "classname", alias: "classname", type: "string" }), null, Q2);
      let K2 = "";
      switch (l2) {
        case "midspan": {
          K2 = `((${A4}='${I3}') OR ( ${N}='${I3}')) AND (${O} IN (5))`, J3.codefield = f.create(`CASE WHEN (${A4}='${I3}') THEN ${$} ELSE ${M} END`, g2.associations.getFieldsIndex());
          const e5 = o(D.findField(g2.associations.fields, N));
          e5.name = _2, e5.alias = _2, H = new y2(e5, f.create(`CASE WHEN (${N}='${I3}') THEN ${A4} ELSE ${N} END`, g2.associations.getFieldsIndex())), G2 = g2.unVersion >= 4 ? new E(D.findField(g2.associations.fields, b.get("PERCENTALONG").name)) : new y2(new y({ name: "percentalong", alias: "percentalong", type: "double" }), f.create("0", g2.associations.getFieldsIndex()));
          break;
        }
        case "junctionedge": {
          K2 = `((${A4}='${I3}') OR ( ${N}='${I3}')) AND (${O} IN (4,6))`, J3.codefield = f.create(`CASE WHEN (${A4}='${I3}') THEN ${$} ELSE ${M} END`, g2.associations.getFieldsIndex());
          const e5 = o(D.findField(g2.associations.fields, N));
          e5.name = _2, e5.alias = _2, H = new y2(e5, f.create(`CASE WHEN (${N}='${I3}') THEN ${A4} ELSE ${N} END`, g2.associations.getFieldsIndex())), W = new y2(new y({ name: "side", alias: "side", type: "string" }), f.create(`CASE WHEN (${O}=4) THEN 'from' ELSE 'to' END`, g2.associations.getFieldsIndex()));
          break;
        }
        case "connected": {
          let e5 = `${A4}='@T'`, t6 = `${N}='@T'`;
          null !== F && (e5 += ` AND ${S2}=@A`, t6 += ` AND ${T2}=@A`), K2 = "((" + e5 + ") OR (" + t6 + "))", K2 = I(K2, "@T", I3 ?? ""), e5 = I(e5, "@T", I3 ?? ""), null !== F && (e5 = I(e5, "@A", F.toString()), K2 = I(K2, "@A", F.toString())), J3.codefield = f.create("CASE WHEN " + e5 + ` THEN ${$} ELSE ${M} END`, g2.associations.getFieldsIndex());
          const n2 = o(D.findField(g2.associations.fields, N));
          n2.name = _2, n2.alias = _2, H = new y2(n2, f.create("CASE WHEN " + e5 + ` THEN ${N} ELSE ${A4} END`, g2.associations.getFieldsIndex()));
          break;
        }
        case "container":
          K2 = `${A4}='${I3}' AND ${O} = 2`, null !== F && (K2 += ` AND ${S2} = ` + F.toString()), J3.codefield = $, K2 = "( " + K2 + " )", H = new x(D.findField(g2.associations.fields, N), _2, _2);
          break;
        case "content":
          K2 = `(${N}='${I3}' AND ${O} = 2)`, null !== F && (K2 += ` AND ${T2} = ` + F.toString()), J3.codefield = M, K2 = "( " + K2 + " )", H = new x(D.findField(g2.associations.fields, A4), _2, _2);
          break;
        case "structure":
          K2 = `(${A4}='${I3}' AND ${O} = 3)`, null !== F && (K2 += ` AND ${S2} = ` + F.toString()), J3.codefield = $, K2 = "( " + K2 + " )", H = new x(D.findField(g2.associations.fields, N), _2, U2);
          break;
        case "attached":
          K2 = `(${N}='${I3}' AND ${O} = 3)`, null !== F && (K2 += ` AND ${T2} = ` + F.toString()), J3.codefield = M, K2 = "( " + K2 + " )", H = new x(D.findField(g2.associations.fields, A4), _2, U2);
          break;
        default:
          throw new t(e4, e.InvalidParameter, t5);
      }
      D2 && (K2 = "1 <> 1");
      return new D({ parentfeatureset: g2.associations, adaptedFields: [new E(D.findField(g2.associations.fields, z2)), new E(D.findField(g2.associations.fields, k)), H, W, J3, G2], extraFilter: K2 ? f.create(K2, g2.associations.getFieldsIndex()) : null });
    });
  }, _.signatures.push({ name: "featuresetbyassociation", min: 2, max: 6 }), _.functions.groupby = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (r, s, o2) => {
      if (B(o2, 3, 3, e4, t5), !G(o2[0])) throw new t(e4, e.InvalidParameter, t5);
      const l2 = await o2[0].load(), f3 = [], c3 = [];
      let u2 = false, d = [];
      if (v(o2[1])) d.push(o2[1]);
      else if (o2[1] instanceof T) d.push(o2[1]);
      else if (J(o2[1])) d = o2[1];
      else {
        if (!V(o2[1])) throw new t(e4, e.InvalidParameter, t5);
        d = o2[1].toArray();
      }
      for (const m of d) if (v(m)) {
        const e5 = f.create(re(m), l2.getFieldsIndex()), t6 = true === A(e5) ? re(m) : "%%%%FIELDNAME";
        f3.push({ name: t6, expression: e5 }), "%%%%FIELDNAME" === t6 && (u2 = true);
      } else {
        if (!(m instanceof T)) throw new t(e4, e.InvalidParameter, t5);
        {
          const n = m.hasField("name") ? m.field("name") : "%%%%FIELDNAME", r2 = m.hasField("expression") ? m.field("expression") : "";
          if ("%%%%FIELDNAME" === n && (u2 = true), !n) throw new t(e4, e.InvalidParameter, t5);
          f3.push({ name: n, expression: f.create(r2 || n, l2.getFieldsIndex()) });
        }
      }
      if (d = [], v(o2[2])) d.push(o2[2]);
      else if (J(o2[2])) d = o2[2];
      else if (V(o2[2])) d = o2[2].toArray();
      else {
        if (!(o2[2] instanceof T)) throw new t(e4, e.InvalidParameter, t5);
        d.push(o2[2]);
      }
      for (const m of d) {
        if (!(m instanceof T)) throw new t(e4, e.InvalidParameter, t5);
        {
          const n = m.hasField("name") ? m.field("name") : "", r2 = m.hasField("statistic") ? m.field("statistic") : "", s2 = m.hasField("expression") ? m.field("expression") : "";
          if (!n || !r2 || !s2) throw new t(e4, e.InvalidParameter, t5);
          c3.push({ name: n, statistic: r2.toLowerCase(), expression: f.create(s2, l2.getFieldsIndex()) });
        }
      }
      if (u2) {
        const e5 = {};
        for (const n of l2.fields) e5[n.name.toLowerCase()] = 1;
        for (const n of f3) "%%%%FIELDNAME" !== n.name && (e5[n.name.toLowerCase()] = 1);
        for (const n of c3) "%%%%FIELDNAME" !== n.name && (e5[n.name.toLowerCase()] = 1);
        let t6 = 0;
        for (const n of f3) if ("%%%%FIELDNAME" === n.name) {
          for (; 1 === e5["field_" + t6.toString()]; ) t6++;
          e5["field_" + t6.toString()] = 1, n.name = "FIELD_" + t6.toString();
        }
      }
      for (const t6 of f3) await J2(t6.expression, _, e4);
      for (const t6 of c3) await J2(t6.expression, _, e4);
      return o2[0].groupby(f3, c3);
    });
  }, _.signatures.push({ name: "groupby", min: 3, max: 3 }), _.functions.distinct = function(e4, t5) {
    return _.standardFunctionAsync(e4, t5, async (r, s, o2) => {
      if (G(o2[0])) {
        B(o2, 2, 2, e4, t5);
        const r2 = await o2[0].load(), s2 = [];
        let l2 = [];
        if (v(o2[1])) l2.push(o2[1]);
        else if (o2[1] instanceof T) l2.push(o2[1]);
        else if (J(o2[1])) l2 = o2[1];
        else {
          if (!V(o2[1])) throw new t(e4, e.InvalidParameter, t5);
          l2 = o2[1].toArray();
        }
        let f3 = false;
        for (const o3 of l2) if (v(o3)) {
          const e5 = f.create(re(o3), r2.getFieldsIndex()), t6 = true === A(e5) ? re(o3) : "%%%%FIELDNAME";
          s2.push({ name: t6, expression: e5 }), "%%%%FIELDNAME" === t6 && (f3 = true);
        } else {
          if (!(o3 instanceof T)) throw new t(e4, e.InvalidParameter, t5);
          {
            const n = o3.hasField("name") ? o3.field("name") : "%%%%FIELDNAME", l3 = o3.hasField("expression") ? o3.field("expression") : "";
            if ("%%%%FIELDNAME" === n && (f3 = true), !n) throw new t(e4, e.InvalidParameter, t5);
            s2.push({ name: n, expression: f.create(l3 || n, r2.getFieldsIndex()) });
          }
        }
        if (f3) {
          const e5 = {};
          for (const n of r2.fields) e5[n.name.toLowerCase()] = 1;
          for (const n of s2) "%%%%FIELDNAME" !== n.name && (e5[n.name.toLowerCase()] = 1);
          let t6 = 0;
          for (const n of s2) if ("%%%%FIELDNAME" === n.name) {
            for (; 1 === e5["field_" + t6.toString()]; ) t6++;
            e5["field_" + t6.toString()] = 1, n.name = "FIELD_" + t6.toString();
          }
        }
        for (const t6 of s2) await J2(t6.expression, _, e4);
        return o2[0].groupby(s2, []);
      }
      return Q("distinct", r, s, o2);
    });
  });
}
export {
  ie as registerFunctions
};
//# sourceMappingURL=featuresetbase-6AS6VNMO.js.map
