{"version": 3, "sources": ["../../@arcgis/core/views/3d/interactive/editingTools/isSupportedGraphicUtils.js", "../../@arcgis/core/views/3d/interactive/editingTools/moveGraphic/isSupportedGraphic.js", "../../@arcgis/core/views/3d/interactive/editingTools/reshapeGraphic/isSupportedGraphic.js", "../../@arcgis/core/views/interactive/support/viewUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../core/has.js\";function E(E){switch(E){case P.SUPPORTED:break;case P.GRAPHICS_LAYER_MISSING:return\"not owned by a graphics layer\";case P.GEOMETRY_MISSING:return\"no geometry\";case P.GEOMETRY_TYPE_UNSUPPORTED:return\"the geometry type is not supported\";case P.ELEVATION_MODE_UNSUPPORTED:return\"the elevation mode is not supported\";case P.SYMBOL_TYPE_UNSUPPORTED:return\"the symbol type is not supported\"}return\"\"}var P;!function(E){E[E.SUPPORTED=0]=\"SUPPORTED\",E[E.GRAPHICS_LAYER_MISSING=1]=\"GRAPHICS_LAYER_MISSING\",E[E.GEOMETRY_MISSING=2]=\"GEOMETRY_MISSING\",E[E.GEOMETRY_TYPE_UNSUPPORTED=3]=\"GEOMETRY_TYPE_UNSUPPORTED\",E[E.ELEVATION_MODE_UNSUPPORTED=4]=\"ELEVATION_MODE_UNSUPPORTED\",E[E.SYMBOL_TYPE_UNSUPPORTED=5]=\"SYMBOL_TYPE_UNSUPPORTED\"}(P||(P={}));export{P as SupportedGraphicResult,E as isSupportedGraphicResultMessage};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../../core/has.js\";import{isNone as e}from\"../../../../../core/maybe.js\";import{getGraphicEffectiveElevationMode as r,hasGraphicFeatureExpressionInfo as o}from\"../../../../../support/elevationInfoUtils.js\";import{SupportedGraphicResult as t}from\"../isSupportedGraphicUtils.js\";function i(i){if(\"graphics\"!==i.layer?.type)return t.GRAPHICS_LAYER_MISSING;if(e(i.geometry))return t.GEOMETRY_MISSING;switch(i.geometry.type){case\"polygon\":case\"point\":case\"polyline\":case\"mesh\":break;default:return t.GEOMETRY_TYPE_UNSUPPORTED}return\"on-the-ground\"!==r(i)&&o(i)?t.ELEVATION_MODE_UNSUPPORTED:t.SUPPORTED}export{i as isSupportedGraphic};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e}from\"../../../../../core/maybe.js\";import r from\"../../../../../geometry/Circle.js\";import{getGraphicEffectiveElevationMode as t,hasGraphicFeatureExpressionInfo as o}from\"../../../../../support/elevationInfoUtils.js\";import{SupportedGraphicResult as n}from\"../isSupportedGraphicUtils.js\";function l(e){return u(e).result}function m(e){return u(e).geometry}function u(l){if(\"graphics\"!==l.layer?.type)return{result:n.GRAPHICS_LAYER_MISSING,geometry:null};if(e(l.geometry))return{result:n.GEOMETRY_MISSING,geometry:null};return\"on-the-ground\"!==t(l)&&o(l)?{result:n.ELEVATION_MODE_UNSUPPORTED,geometry:null}:\"point\"!==l.geometry.type&&\"polyline\"!==l.geometry.type&&(\"polygon\"!==l.geometry.type||l.geometry instanceof r)?{result:n.GEOMETRY_TYPE_UNSUPPORTED,geometry:null}:{result:n.SUPPORTED,geometry:l.geometry}}export{m as geometryOfSupportedGraphic,l as isSupportedGraphic};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{createScreenPoint as e,createRenderScreenPointArray3 as r,createScreenPointArray as o}from\"../../../core/screenUtils.js\";import{a as t,f as n}from\"../../../chunks/vec2.js\";import{c as s}from\"../../../chunks/vec3.js\";import{c}from\"../../../chunks/vec3f64.js\";import{makeDehydratedPoint as a}from\"../../../layers/graphics/dehydratedFeatures.js\";import{getConvertedElevationFromVector as i}from\"../../../support/elevationInfoUtils.js\";function p(e,r,o,s){o.projectToRenderScreen(e,h),o.projectToRenderScreen(r,C),t(s,C,h),n(s,s)}function d(e,r,o,t,n=c()){const s=r.toXYZ(e);return s[2]=i(t,s,r.spatialReference,o)||0,t.renderCoordsHelper.toRenderCoords(s,r.spatialReference,n),n}function f(e,r,o,t,n=c()){const a=s(j,e);return a[2]=i(t,a,r,o)||0,t.renderCoordsHelper.toRenderCoords(a,r,n),n}function l(e,r,o,t){return u(r.toXYZ(e),r.spatialReference,o,t)}function u(r,o,t,n){return\"2d\"===n.type?(R.x=r[0],R.y=r[1],R.spatialReference=o,n.toScreen(R)):(f(r,o,t,n,j),n.state.camera.projectToScreen(j,v),e(v[0],v[1]))}function m(e,r,o,t,n,s,c){if(\"absolute-height\"===t.mode)return d(e,o,t,n,s),void d(r,o,t,n,c);const a=o.toXYZ(e),p=o.toXYZ(r),f=i(n,a,o.spatialReference,t),l=i(n,p,o.spatialReference,t),u=(null==f?l:null==l?f:Math.min(f,l))||0;a[2]=u,p[2]=u,n.renderCoordsHelper.toRenderCoords(a,o.spatialReference,s),n.renderCoordsHelper.toRenderCoords(p,o.spatialReference,c)}const R=a(0,0,0,null),j=c(),h=r(),C=r(),v=o();export{d as anyMapPointToRender,l as anyMapPointToScreenPoint,m as anyMapPointsToRenderWithEqualRelativeZ,p as renderScreenSpaceTangent,f as vectorToRender,u as vectorToScreenPoint};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgC,SAAS,EAAEA,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK,EAAE;AAAU;AAAA,IAAM,KAAK,EAAE;AAAuB,aAAM;AAAA,IAAgC,KAAK,EAAE;AAAiB,aAAM;AAAA,IAAc,KAAK,EAAE;AAA0B,aAAM;AAAA,IAAqC,KAAK,EAAE;AAA2B,aAAM;AAAA,IAAsC,KAAK,EAAE;AAAwB,aAAM;AAAA,EAAkC;AAAC,SAAM;AAAE;AAAC,IAAI;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,yBAAuB,CAAC,IAAE,0BAAyBA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,4BAA0B,CAAC,IAAE,6BAA4BA,GAAEA,GAAE,6BAA2B,CAAC,IAAE,8BAA6BA,GAAEA,GAAE,0BAAwB,CAAC,IAAE;AAAyB,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACAtd,SAASC,GAAEA,IAAE;AAJlT;AAImT,MAAG,iBAAa,KAAAA,GAAE,UAAF,mBAAS,MAAK,QAAO,EAAE;AAAuB,MAAG,EAAEA,GAAE,QAAQ,EAAE,QAAO,EAAE;AAAiB,UAAOA,GAAE,SAAS,MAAK;AAAA,IAAC,KAAI;AAAA,IAAU,KAAI;AAAA,IAAQ,KAAI;AAAA,IAAW,KAAI;AAAO;AAAA,IAAM;AAAQ,aAAO,EAAE;AAAA,EAAyB;AAAC,SAAM,oBAAkB,EAAEA,EAAC,KAAGC,GAAED,EAAC,IAAE,EAAE,6BAA2B,EAAE;AAAS;;;ACAjT,SAASE,GAAE,GAAE;AAAC,SAAO,EAAE,CAAC,EAAE;AAAM;AAAC,SAASC,GAAE,GAAE;AAAC,SAAO,EAAE,CAAC,EAAE;AAAQ;AAAC,SAAS,EAAED,IAAE;AAJpY;AAIqY,MAAG,iBAAa,KAAAA,GAAE,UAAF,mBAAS,MAAK,QAAM,EAAC,QAAO,EAAE,wBAAuB,UAAS,KAAI;AAAE,MAAG,EAAEA,GAAE,QAAQ,EAAE,QAAM,EAAC,QAAO,EAAE,kBAAiB,UAAS,KAAI;AAAE,SAAM,oBAAkB,EAAEA,EAAC,KAAGE,GAAEF,EAAC,IAAE,EAAC,QAAO,EAAE,4BAA2B,UAAS,KAAI,IAAE,YAAUA,GAAE,SAAS,QAAM,eAAaA,GAAE,SAAS,SAAO,cAAYA,GAAE,SAAS,QAAMA,GAAE,oBAAoB,KAAG,EAAC,QAAO,EAAE,2BAA0B,UAAS,KAAI,IAAE,EAAC,QAAO,EAAE,WAAU,UAASA,GAAE,SAAQ;AAAC;;;ACAjJ,SAAS,EAAE,GAAEG,IAAEC,IAAEC,IAAEC,KAAE,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,GAAE,CAAC;AAAE,SAAO,EAAE,CAAC,IAAE,EAAED,IAAE,GAAEF,IAAEC,EAAC,KAAG,GAAEC,GAAE,mBAAmB,eAAe,GAAEF,IAAEG,EAAC,GAAEA;AAAC;AAAiE,SAASC,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAM,SAAOA,GAAE,QAAM,EAAE,IAAEH,GAAE,CAAC,GAAE,EAAE,IAAEA,GAAE,CAAC,GAAE,EAAE,mBAAiBC,IAAEE,GAAE,SAAS,CAAC,MAAI,EAAEH,IAAEC,IAAEC,IAAEC,IAAE,CAAC,GAAEA,GAAE,MAAM,OAAO,gBAAgB,GAAEC,EAAC,GAAE,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE;AAA0W,IAAM,IAAE,EAAE,GAAE,GAAE,GAAE,IAAI;AAApB,IAAsB,IAAE,EAAE;AAA1B,IAA4BC,KAAE,EAAE;AAAhC,IAAkC,IAAE,EAAE;AAAtC,IAAwCC,KAAE,EAAE;", "names": ["E", "i", "c", "l", "m", "c", "r", "o", "t", "n", "u", "r", "o", "t", "n", "v", "h", "v"]}