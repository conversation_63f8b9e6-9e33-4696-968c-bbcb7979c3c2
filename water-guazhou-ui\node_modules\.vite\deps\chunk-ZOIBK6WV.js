import {
  u
} from "./chunk-I7WHRVHF.js";
import {
  e
} from "./chunk-X7FOCGBC.js";
import {
  h
} from "./chunk-SRBBUKOI.js";
import {
  b
} from "./chunk-EIGTETCG.js";

// node_modules/@arcgis/core/views/draw/support/drawUtils.js
function i(t, n, e2, i2) {
  if (null == i2 || t.hasZ || (i2 = void 0), "point" === t.type) return t.x += n, t.y += e2, t.hasZ && null != i2 && (t.z += i2), t;
  if ("multipoint" === t.type) {
    const o = t.points;
    for (let t2 = 0; t2 < o.length; t2++) o[t2] = l(o[t2], n, e2, i2);
    return t;
  }
  if ("extent" === t.type) return t.xmin += n, t.xmax += n, t.ymin += e2, t.ymax += e2, null != i2 && (t.zmin ?? (t.zmin = 0), t.zmin += i2, t.zmax ?? (t.zmax = 0), t.zmax += i2), t;
  const r2 = e(t), s2 = "polyline" === t.type ? t.paths : t.rings;
  for (let o = 0; o < r2.length; o++) {
    const t2 = r2[o];
    for (let o2 = 0; o2 < t2.length; o2++) t2[o2] = l(t2[o2], n, e2, i2);
  }
  return "paths" in t ? t.paths = s2 : t.rings = s2, t;
}
function r(t, n, e2, r2, s2) {
  const m2 = t.clone(), a2 = r2.resolution;
  if ("point" === m2.type) {
    if (s2) i(m2, n * a2, -e2 * a2);
    else {
      const t2 = r2.state.transform, o = r2.state.inverseTransform, i2 = t2[0] * m2.x + t2[2] * m2.y + t2[4], s3 = t2[1] * m2.x + t2[3] * m2.y + t2[5];
      m2.x = o[0] * (i2 + n) + o[2] * (s3 + e2) + o[4], m2.y = o[1] * (i2 + n) + o[3] * (s3 + e2) + o[5];
    }
    return m2;
  }
  if ("multipoint" === m2.type) {
    if (s2) i(m2, n * a2, -e2 * a2);
    else {
      const t2 = m2.points, o = r2.state.transform, i2 = r2.state.inverseTransform;
      for (let r3 = 0; r3 < t2.length; r3++) {
        const s3 = t2[r3], m3 = o[0] * s3[0] + o[2] * s3[1] + o[4], a3 = o[1] * s3[0] + o[3] * s3[1] + o[5], l2 = i2[0] * (m3 + n) + i2[2] * (a3 + e2) + i2[4], p = i2[1] * (m3 + n) + i2[3] * (a3 + e2) + i2[5];
        t2[r3] = x(s3, l2, p, void 0);
      }
    }
    return m2;
  }
  if ("extent" === m2.type) {
    if (s2) i(m2, n * a2, -e2 * a2);
    else {
      const t2 = r2.state.transform, o = r2.state.inverseTransform, i2 = t2[0] * m2.xmin + t2[2] * m2.ymin + t2[4], s3 = t2[1] * m2.xmin + t2[3] * m2.ymin + t2[5], a3 = t2[0] * m2.xmax + t2[2] * m2.ymax + t2[4], l2 = t2[1] * m2.xmax + t2[3] * m2.ymax + t2[5];
      m2.xmin = o[0] * (i2 + n) + o[2] * (s3 + e2) + o[4], m2.ymin = o[1] * (i2 + n) + o[3] * (s3 + e2) + o[5], m2.xmax = o[0] * (a3 + n) + o[2] * (l2 + e2) + o[4], m2.ymax = o[1] * (a3 + n) + o[3] * (l2 + e2) + o[5];
    }
    return m2;
  }
  if (s2) i(m2, n * a2, -e2 * a2);
  else {
    const t2 = e(m2), i2 = "polyline" === m2.type ? m2.paths : m2.rings, s3 = r2.state.transform, a3 = r2.state.inverseTransform;
    for (let o = 0; o < t2.length; o++) {
      const i3 = t2[o];
      for (let t3 = 0; t3 < i3.length; t3++) {
        const o2 = i3[t3], r3 = s3[0] * o2[0] + s3[2] * o2[1] + s3[4], m3 = s3[1] * o2[0] + s3[3] * o2[1] + s3[5], l2 = a3[0] * (r3 + n) + a3[2] * (m3 + e2) + a3[4], p = a3[1] * (r3 + n) + a3[3] * (m3 + e2) + a3[5];
        i3[t3] = x(o2, l2, p, void 0);
      }
    }
    "paths" in m2 ? m2.paths = i2 : m2.rings = i2;
  }
  return m2;
}
function s(t, i2, r2, s2) {
  if ("point" === t.type) {
    const { x: n, y: e2 } = t, o = s2 ? s2[0] : n, m3 = s2 ? s2[1] : e2, a3 = t.clone(), l3 = (n - o) * i2 + o, x2 = (e2 - m3) * r2 + m3;
    return a3.x = l3, a3.y = x2, a3;
  }
  if ("multipoint" === t.type) {
    const m3 = e(t), a3 = u(), [l3, p2, y2, f2] = h(a3, [m3]), u3 = s2 ? s2[0] : (l3 + y2) / 2, c2 = s2 ? s2[1] : (f2 + p2) / 2, h3 = t.clone(), g2 = h3.points;
    for (let t2 = 0; t2 < g2.length; t2++) {
      const n = g2[t2], [e2, o] = n, s3 = (e2 - u3) * i2 + u3, m4 = (o - c2) * r2 + c2;
      g2[t2] = x(n, s3, m4, void 0);
    }
    return h3;
  }
  if ("extent" === t.type) {
    const { xmin: n, xmax: e2, ymin: o, ymax: m3 } = t, a3 = s2 ? s2[0] : (n + e2) / 2, l3 = s2 ? s2[1] : (m3 + o) / 2, x2 = t.clone();
    if (x2.xmin = (n - a3) * i2 + a3, x2.ymax = (m3 - l3) * r2 + l3, x2.xmax = (e2 - a3) * i2 + a3, x2.ymin = (o - l3) * r2 + l3, x2.xmin > x2.xmax) {
      const t2 = x2.xmin, n2 = x2.xmax;
      x2.xmin = n2, x2.xmax = t2;
    }
    if (x2.ymin > x2.ymax) {
      const t2 = x2.ymin, n2 = x2.ymax;
      x2.ymin = n2, x2.ymax = t2;
    }
    return x2;
  }
  const m2 = e(t), a2 = u(), [l2, p, y, f] = h(a2, m2), u2 = s2 ? s2[0] : (l2 + y) / 2, c = s2 ? s2[1] : (f + p) / 2, h2 = t.clone(), g = "polyline" === h2.type ? h2.paths : h2.rings;
  for (let n = 0; n < m2.length; n++) {
    const t2 = m2[n];
    for (let e2 = 0; e2 < t2.length; e2++) {
      const o = t2[e2], [s3, m3] = o, a3 = (s3 - u2) * i2 + u2, l3 = (m3 - c) * r2 + c;
      g[n][e2] = x(o, a3, l3, void 0);
    }
  }
  return "paths" in h2 ? h2.paths = g : h2.rings = g, h2;
}
function m(t, n, e2, o, i2, r2) {
  const s2 = Math.sqrt((e2 - t) * (e2 - t) + (o - n) * (o - n));
  return Math.sqrt((i2 - t) * (i2 - t) + (r2 - n) * (r2 - n)) / s2;
}
function a(n, e2, o, i2 = false) {
  const r2 = Math.atan2(e2.y - o.y, e2.x - o.x) - Math.atan2(n.y - o.y, n.x - o.x), s2 = Math.atan2(Math.sin(r2), Math.cos(r2));
  return i2 ? s2 : b(s2);
}
function l(t, n, e2, o) {
  return x(t, t[0] + n, t[1] + e2, null != t[2] && null != o ? t[2] + o : void 0);
}
function x(t, n, e2, o) {
  const i2 = [n, e2];
  return t.length > 2 && i2.push(null != o ? o : t[2]), t.length > 3 && i2.push(t[3]), i2;
}

export {
  i,
  r,
  s,
  m,
  a
};
//# sourceMappingURL=chunk-ZOIBK6WV.js.map
