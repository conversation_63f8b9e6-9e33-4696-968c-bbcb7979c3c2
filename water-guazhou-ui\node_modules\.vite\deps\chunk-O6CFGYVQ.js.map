{"version": 3, "sources": ["../../@arcgis/core/widgets/Zoom/IconButton.js", "../../@arcgis/core/widgets/Zoom/ZoomConditions2D.js", "../../@arcgis/core/widgets/Zoom/ZoomConditions3D.js", "../../@arcgis/core/widgets/Zoom/ZoomViewModel.js", "../../@arcgis/core/widgets/Zoom.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as i}from\"../../core/accessorSupport/decorators/subclass.js\";import e from\"../Widget.js\";import{accessibleHandler as o}from\"../support/decorators/accessibleHandler.js\";import{tsx as r}from\"../support/jsxFactory.js\";import\"../support/widgetUtils.js\";const c={button:\"esri-widget--button esri-widget\",disabled:\"esri-disabled\",interactive:\"esri-interactive\",iconText:\"esri-icon-font-fallback-text\",icon:\"esri-icon\"};let n=class extends e{constructor(){super(...arguments),this.enabled=!0,this.iconClass=\"\",this.title=\"\"}render(){const t=this.enabled?0:-1,s={[c.disabled]:!this.enabled,[c.interactive]:this.enabled},i={[this.iconClass]:!!this.iconClass};return r(\"div\",{bind:this,class:this.classes(c.button,s),onclick:this._triggerAction,onkeydown:this._triggerAction,role:\"button\",tabIndex:t,title:this.title},r(\"span\",{\"aria-hidden\":\"true\",role:\"presentation\",class:this.classes(c.icon,i)}),r(\"span\",{class:c.iconText},this.title))}_triggerAction(){this.action.call(this)}};t([s()],n.prototype,\"action\",void 0),t([s()],n.prototype,\"enabled\",void 0),t([s()],n.prototype,\"iconClass\",void 0),t([s()],n.prototype,\"title\",void 0),t([o()],n.prototype,\"_triggerAction\",null),n=t([i(\"esri.widgets.IconButton\")],n);const a=n;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../core/Accessor.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as r}from\"../../core/accessorSupport/decorators/subclass.js\";let s=class extends t{get canZoomIn(){if(!this.get(\"view.ready\"))return!1;const e=this.get(\"view.animation.target.scale\")||this.get(\"view.scale\"),t=this.get(\"view.constraints.effectiveMaxScale\");return 0===t||e>t}get canZoomOut(){if(!this.get(\"view.ready\"))return!1;const e=this.get(\"view.animation.target.scale\")||this.get(\"view.scale\"),t=this.get(\"view.constraints.effectiveMinScale\");return 0===t||e<t}};e([o({readOnly:!0})],s.prototype,\"canZoomIn\",null),e([o({readOnly:!0})],s.prototype,\"canZoomOut\",null),e([o()],s.prototype,\"view\",void 0),s=e([r(\"esri.widgets.Zoom.ZoomConditions2D\")],s);const i=s;export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import r from\"../../core/Accessor.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";let t=class extends r{get canZoomIn(){return!!this.view.ready}get canZoomOut(){return!!this.view.ready}};o([e({readOnly:!0})],t.prototype,\"canZoomIn\",null),o([e({readOnly:!0})],t.prototype,\"canZoomOut\",null),o([e()],t.prototype,\"view\",void 0),t=o([s(\"esri.widgets.Zoom.ZoomConditions3D\")],t);const c=t;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import t from\"../../core/Accessor.js\";import{isSome as s}from\"../../core/maybe.js\";import{ignoreAbortErrors as i}from\"../../core/promiseUtils.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as r}from\"../../core/accessorSupport/decorators/subclass.js\";import n from\"./ZoomConditions2D.js\";import m from\"./ZoomConditions3D.js\";let p=class extends t{constructor(o){super(o)}destroy(){this.view=null}get canZoomIn(){return s(this._zoomConditions)&&this._zoomConditions.canZoomIn}get canZoomOut(){return s(this._zoomConditions)&&this._zoomConditions?.canZoomOut}get state(){return this.view?.ready?\"ready\":\"disabled\"}set view(o){o?\"2d\"===o.type?this._zoomConditions=new n({view:o}):\"3d\"===o.type&&(this._zoomConditions=new m({view:o})):this._zoomConditions=null,this._set(\"view\",o)}zoomIn(){if(!this.canZoomIn)return;const o=this.view;\"2d\"===o.type?o.mapViewNavigation.zoomIn():i(o.goTo({zoomFactor:2}))}zoomOut(){if(!this.canZoomOut)return;const o=this.view;\"2d\"===o.type?o.mapViewNavigation.zoomOut():i(o.goTo({zoomFactor:.5}))}};o([e()],p.prototype,\"_zoomConditions\",void 0),o([e()],p.prototype,\"canZoomIn\",null),o([e()],p.prototype,\"canZoomOut\",null),o([e({readOnly:!0})],p.prototype,\"state\",null),o([e()],p.prototype,\"view\",null),p=o([r(\"esri.widgets.Zoom.ZoomViewModel\")],p);const a=p;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../chunks/tslib.es6.js\";import{destroyMaybe as t}from\"../core/maybe.js\";import{property as s}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as e}from\"../core/accessorSupport/decorators/subclass.js\";import i from\"./Widget.js\";import\"./support/widgetUtils.js\";import{messageBundle as r}from\"./support/decorators/messageBundle.js\";import{tsx as n}from\"./support/jsxFactory.js\";import m from\"./Zoom/IconButton.js\";import l from\"./Zoom/ZoomViewModel.js\";const u={base:\"esri-zoom esri-widget\",horizontalLayout:\"esri-zoom--horizontal\",zoomInIcon:\"esri-icon-plus\",zoomOutIcon:\"esri-icon-minus\",widgetIcon:\"esri-icon-zoom-in-magnifying-glass\"};let a=class extends i{constructor(o,t){super(o,t),this.iconClass=u.widgetIcon,this.messages=null,this.viewModel=new l}initialize(){this._zoomInButton=new m({action:this.zoomIn.bind(this),iconClass:u.zoomInIcon}),this._zoomOutButton=new m({action:this.zoomOut.bind(this),iconClass:u.zoomOutIcon})}destroy(){this._zoomInButton=t(this._zoomInButton),this._zoomOutButton=t(this._zoomOutButton)}get label(){return this.messages?.widgetLabel??\"\"}set label(o){this._overrideIfSome(\"label\",o)}set layout(o){\"horizontal\"!==o&&(o=\"vertical\"),this._set(\"layout\",o)}set view(o){this.viewModel.view=o}get view(){return this.viewModel.view}render(){const o=this.viewModel,t={[u.horizontalLayout]:\"horizontal\"===this.layout},{canZoomIn:s,canZoomOut:e}=o;this._zoomInButton.enabled=s,this._zoomOutButton.enabled=e;const{zoomIn:i,zoomOut:r}=this.messages;return this._zoomInButton.title=i,this._zoomOutButton.title=r,n(\"div\",{class:this.classes(u.base,t)},this._zoomInButton.render(),this._zoomOutButton.render())}zoomIn(){return this.viewModel.zoomIn()}zoomOut(){return this.viewModel.zoomOut()}};o([s()],a.prototype,\"iconClass\",void 0),o([s()],a.prototype,\"label\",null),o([s({value:\"vertical\"})],a.prototype,\"layout\",null),o([s(),r(\"esri/widgets/Zoom/t9n/Zoom\")],a.prototype,\"messages\",void 0),o([s()],a.prototype,\"view\",null),o([s({type:l})],a.prototype,\"viewModel\",void 0),a=o([e(\"esri.widgets.Zoom\")],a);const c=a;export{c as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAIsd,IAAM,IAAE,EAAC,QAAO,mCAAkC,UAAS,iBAAgB,aAAY,oBAAmB,UAAS,gCAA+B,MAAK,YAAW;AAAE,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,MAAG,KAAK,YAAU,IAAG,KAAK,QAAM;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,UAAMC,KAAE,KAAK,UAAQ,IAAE,IAAGC,KAAE,EAAC,CAAC,EAAE,QAAQ,GAAE,CAAC,KAAK,SAAQ,CAAC,EAAE,WAAW,GAAE,KAAK,QAAO,GAAEC,KAAE,EAAC,CAAC,KAAK,SAAS,GAAE,CAAC,CAAC,KAAK,UAAS;AAAE,WAAO,EAAE,OAAM,EAAC,MAAK,MAAK,OAAM,KAAK,QAAQ,EAAE,QAAOD,EAAC,GAAE,SAAQ,KAAK,gBAAe,WAAU,KAAK,gBAAe,MAAK,UAAS,UAASD,IAAE,OAAM,KAAK,MAAK,GAAE,EAAE,QAAO,EAAC,eAAc,QAAO,MAAK,gBAAe,OAAM,KAAK,QAAQ,EAAE,MAAKE,EAAC,EAAC,CAAC,GAAE,EAAE,QAAO,EAAC,OAAM,EAAE,SAAQ,GAAE,KAAK,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,SAAK,OAAO,KAAK,IAAI;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,IAAI,GAAEA,KAAE,EAAE,CAACI,GAAE,yBAAyB,CAAC,GAAEJ,EAAC;AAAE,IAAMI,KAAEJ;;;ACA1lC,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,QAAG,CAAC,KAAK,IAAI,YAAY,EAAE,QAAM;AAAG,UAAMK,KAAE,KAAK,IAAI,6BAA6B,KAAG,KAAK,IAAI,YAAY,GAAEC,KAAE,KAAK,IAAI,oCAAoC;AAAE,WAAO,MAAIA,MAAGD,KAAEC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,QAAG,CAAC,KAAK,IAAI,YAAY,EAAE,QAAM;AAAG,UAAMD,KAAE,KAAK,IAAI,6BAA6B,KAAG,KAAK,IAAI,YAAY,GAAEC,KAAE,KAAK,IAAI,oCAAoC;AAAE,WAAO,MAAIA,MAAGD,KAAEC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,EAAE,CAACC,GAAE,oCAAoC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACA1lB,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAM,CAAC,CAAC,KAAK,KAAK;AAAA,EAAK;AAAA,EAAC,IAAI,aAAY;AAAC,WAAM,CAAC,CAAC,KAAK,KAAK;AAAA,EAAK;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAACC,GAAE,oCAAoC,CAAC,GAAED,EAAC;AAAE,IAAME,KAAEF;;;ACAtH,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,OAAK;AAAA,EAAI;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,EAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB;AAAA,EAAS;AAAA,EAAC,IAAI,aAAY;AAJ5pB;AAI6pB,WAAO,EAAE,KAAK,eAAe,OAAG,UAAK,oBAAL,mBAAsB;AAAA,EAAU;AAAA,EAAC,IAAI,QAAO;AAJzuB;AAI0uB,aAAO,UAAK,SAAL,mBAAW,SAAM,UAAQ;AAAA,EAAU;AAAA,EAAC,IAAI,KAAK,GAAE;AAAC,QAAE,SAAO,EAAE,OAAK,KAAK,kBAAgB,IAAI,EAAE,EAAC,MAAK,EAAC,CAAC,IAAE,SAAO,EAAE,SAAO,KAAK,kBAAgB,IAAIG,GAAE,EAAC,MAAK,EAAC,CAAC,KAAG,KAAK,kBAAgB,MAAK,KAAK,KAAK,QAAO,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAG,CAAC,KAAK,UAAU;AAAO,UAAM,IAAE,KAAK;AAAK,aAAO,EAAE,OAAK,EAAE,kBAAkB,OAAO,IAAE,EAAE,EAAE,KAAK,EAAC,YAAW,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,CAAC,KAAK,WAAW;AAAO,UAAM,IAAE,KAAK;AAAK,aAAO,EAAE,OAAK,EAAE,kBAAkB,QAAQ,IAAE,EAAE,EAAE,KAAK,EAAC,YAAW,IAAE,CAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,IAAE,EAAE,CAACC,GAAE,iCAAiC,CAAC,GAAE,CAAC;AAAE,IAAMA,KAAE;;;ACA/3B,IAAM,IAAE,EAAC,MAAK,yBAAwB,kBAAiB,yBAAwB,YAAW,kBAAiB,aAAY,mBAAkB,YAAW,qCAAoC;AAAE,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAEC,IAAE;AAAC,UAAM,GAAEA,EAAC,GAAE,KAAK,YAAU,EAAE,YAAW,KAAK,WAAS,MAAK,KAAK,YAAU,IAAID;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,gBAAc,IAAIA,GAAE,EAAC,QAAO,KAAK,OAAO,KAAK,IAAI,GAAE,WAAU,EAAE,WAAU,CAAC,GAAE,KAAK,iBAAe,IAAIA,GAAE,EAAC,QAAO,KAAK,QAAQ,KAAK,IAAI,GAAE,WAAU,EAAE,YAAW,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,gBAAc,EAAE,KAAK,aAAa,GAAE,KAAK,iBAAe,EAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAJjoC;AAIkoC,aAAO,UAAK,aAAL,mBAAe,gBAAa;AAAA,EAAE;AAAA,EAAC,IAAI,MAAM,GAAE;AAAC,SAAK,gBAAgB,SAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAO,GAAE;AAAC,qBAAe,MAAI,IAAE,aAAY,KAAK,KAAK,UAAS,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,KAAK,GAAE;AAAC,SAAK,UAAU,OAAK;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,UAAU;AAAA,EAAI;AAAA,EAAC,SAAQ;AAAC,UAAM,IAAE,KAAK,WAAUC,KAAE,EAAC,CAAC,EAAE,gBAAgB,GAAE,iBAAe,KAAK,OAAM,GAAE,EAAC,WAAUC,IAAE,YAAWC,GAAC,IAAE;AAAE,SAAK,cAAc,UAAQD,IAAE,KAAK,eAAe,UAAQC;AAAE,UAAK,EAAC,QAAOC,IAAE,SAAQC,GAAC,IAAE,KAAK;AAAS,WAAO,KAAK,cAAc,QAAMD,IAAE,KAAK,eAAe,QAAMC,IAAE,EAAE,OAAM,EAAC,OAAM,KAAK,QAAQ,EAAE,MAAKJ,EAAC,EAAC,GAAE,KAAK,cAAc,OAAO,GAAE,KAAK,eAAe,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK,UAAU,OAAO;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,UAAU,QAAQ;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,WAAU,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,GAAEG,GAAE,4BAA4B,CAAC,GAAEH,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,GAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAEA,KAAE,EAAE,CAACA,GAAE,mBAAmB,CAAC,GAAEA,EAAC;AAAE,IAAMM,KAAEN;", "names": ["n", "t", "s", "i", "a", "e", "t", "a", "t", "a", "c", "c", "a", "a", "t", "s", "e", "i", "r", "c"]}