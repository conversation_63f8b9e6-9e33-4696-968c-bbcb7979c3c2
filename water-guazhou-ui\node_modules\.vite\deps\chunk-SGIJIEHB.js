import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";
import {
  r
} from "./chunk-2CM7MIII.js";

// node_modules/@arcgis/core/core/Evented.js
var i = class _i {
  constructor() {
    this._emitter = new _i.EventEmitter(this);
  }
  emit(t, e2) {
    return this._emitter.emit(t, e2);
  }
  on(t, e2) {
    return this._emitter.on(t, e2);
  }
  once(t, e2) {
    return this._emitter.once(t, e2);
  }
  hasEventListener(t) {
    return this._emitter.hasEventListener(t);
  }
};
!function(n2) {
  class o {
    constructor(t = null) {
      this._target = t, this._listenersMap = null;
    }
    clear() {
      this._listenersMap && this._listenersMap.clear(), this._listenersMap = null;
    }
    emit(t, e2) {
      const s = this._listenersMap && this._listenersMap.get(t);
      if (!s) return false;
      const r2 = this._target || this;
      return [...s].forEach((t2) => {
        t2.call(r2, e2);
      }), s.length > 0;
    }
    on(t, e2) {
      if (Array.isArray(t)) {
        const r3 = t.map((t2) => this.on(t2, e2));
        return r(r3);
      }
      if (t.includes(",")) throw new TypeError("Evented.on() with a comma delimited string of event types is not supported");
      this._listenersMap || (this._listenersMap = /* @__PURE__ */ new Map());
      const r2 = this._listenersMap.get(t) || [];
      return r2.push(e2), this._listenersMap.set(t, r2), { remove: () => {
        const s = this._listenersMap && this._listenersMap.get(t) || [], r3 = s.indexOf(e2);
        r3 >= 0 && s.splice(r3, 1);
      } };
    }
    once(t, e2) {
      const s = this.on(t, (t2) => {
        s.remove(), e2.call(null, t2);
      });
      return s;
    }
    hasEventListener(t) {
      const e2 = this._listenersMap && this._listenersMap.get(t);
      return null != e2 && e2.length > 0;
    }
  }
  n2.EventEmitter = o, n2.EventedMixin = (e2) => {
    let s = class extends e2 {
      constructor() {
        super(...arguments), this._emitter = new o();
      }
      destroy() {
        this._emitter.clear();
      }
      emit(t, e3) {
        return this._emitter.emit(t, e3);
      }
      on(t, e3) {
        return this._emitter.on(t, e3);
      }
      once(t, e3) {
        return this._emitter.once(t, e3);
      }
      hasEventListener(t) {
        return this._emitter.hasEventListener(t);
      }
    };
    return s = e([a("esri.core.Evented")], s), s;
  };
  let h = class extends v {
    constructor() {
      super(...arguments), this._emitter = new i.EventEmitter(this);
    }
    destroy() {
      this._emitter.clear();
    }
    emit(t, e2) {
      return this._emitter.emit(t, e2);
    }
    on(t, e2) {
      return this._emitter.on(t, e2);
    }
    once(t, e2) {
      return this._emitter.once(t, e2);
    }
    hasEventListener(t) {
      return this._emitter.hasEventListener(t);
    }
  };
  h = e([a("esri.core.Evented")], h), n2.EventedAccessor = h;
}(i || (i = {}));
var n = i;

export {
  n
};
//# sourceMappingURL=chunk-SGIJIEHB.js.map
