import {
  p as p2
} from "./chunk-WJPDYSRI.js";
import {
  m as m2
} from "./chunk-GE5PSQPZ.js";
import {
  l as l3
} from "./chunk-QUHG7NMD.js";
import {
  l as l4,
  m as m3,
  p as p3,
  y as y2,
  y2 as y3
} from "./chunk-TWFTBWXP.js";
import {
  p as p4
} from "./chunk-UYJR3ZHF.js";
import {
  P,
  f2 as f
} from "./chunk-ETY52UBV.js";
import {
  S,
  w
} from "./chunk-VNYCO3JG.js";
import {
  i as i2
} from "./chunk-57XIOVP5.js";
import {
  b as b2
} from "./chunk-QMNV7QQK.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  s as s4
} from "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  c,
  m
} from "./chunk-XVA5SA7P.js";
import {
  o as o2
} from "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l as l2
} from "./chunk-UOKTNY52.js";
import {
  s as s3
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  a,
  b,
  u
} from "./chunk-HP475EI3.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  n,
  p
} from "./chunk-REW33H3I.js";
import {
  e as e2,
  l,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/renderers/support/ClassBreakInfo.js
var i3;
var l5 = i3 = class extends l2 {
  constructor(s5) {
    super(s5), this.description = null, this.label = null, this.minValue = null, this.maxValue = 0, this.symbol = null;
  }
  clone() {
    return new i3({ description: this.description, label: this.label, minValue: this.minValue, maxValue: this.maxValue, symbol: this.symbol ? this.symbol.clone() : null });
  }
  getMeshHash() {
    const s5 = JSON.stringify(this.symbol);
    return `${this.minValue}.${this.maxValue}.${s5}`;
  }
};
e([y({ type: String, json: { write: true } })], l5.prototype, "description", void 0), e([y({ type: String, json: { write: true } })], l5.prototype, "label", void 0), e([y({ type: Number, json: { read: { source: "classMinValue" }, write: { target: "classMinValue" } } })], l5.prototype, "minValue", void 0), e([y({ type: Number, json: { read: { source: "classMaxValue" }, write: { target: "classMaxValue" } } })], l5.prototype, "maxValue", void 0), e([y(l4)], l5.prototype, "symbol", void 0), l5 = i3 = e([a2("esri.renderers.support.ClassBreakInfo")], l5);
var a3 = l5;

// node_modules/@arcgis/core/renderers/ClassBreaksRenderer.js
var V;
var E = "log";
var j = "percent-of-total";
var z = "field";
var w2 = new s3({ esriNormalizeByLog: E, esriNormalizeByPercentOfTotal: j, esriNormalizeByField: z });
var T = b(a3);
var C = V = class extends y2(p3) {
  constructor(e3) {
    super(e3), this._compiledValueExpression = { valueExpression: null, compiledFunction: null }, this.backgroundFillSymbol = null, this.classBreakInfos = null, this.defaultLabel = null, this.defaultSymbol = null, this.field = null, this.isMaxInclusive = true, this.legendOptions = null, this.normalizationField = null, this.normalizationTotal = null, this.type = "class-breaks", this.valueExpression = null, this.valueExpressionTitle = null, this._set("classBreakInfos", []);
  }
  readClassBreakInfos(e3, s5, t2) {
    if (!Array.isArray(e3)) return;
    let o3 = s5.minValue;
    return e3.map((e4) => {
      const s6 = new a3();
      return s6.read(e4, t2), null == s6.minValue && (s6.minValue = o3), null == s6.maxValue && (s6.maxValue = s6.minValue), o3 = s6.maxValue, s6;
    });
  }
  writeClassBreakInfos(e3, s5, t2, o3) {
    const i5 = e3.map((e4) => e4.write({}, o3));
    this._areClassBreaksConsecutive() && i5.forEach((e4) => delete e4.classMinValue), s5[t2] = i5;
  }
  castField(e3) {
    return null == e3 ? e3 : "function" == typeof e3 ? (s.getLogger(this.declaredClass).error(".field: field must be a string value"), null) : u(e3);
  }
  get minValue() {
    return this.classBreakInfos && this.classBreakInfos[0] && this.classBreakInfos[0].minValue || 0;
  }
  get normalizationType() {
    let e3 = this._get("normalizationType");
    const s5 = !!this.normalizationField, t2 = null != this.normalizationTotal;
    return s5 || t2 ? (e3 = s5 && z || t2 && j || null, s5 && t2 && s.getLogger(this.declaredClass).warn("warning: both normalizationField and normalizationTotal are set!")) : e3 !== z && e3 !== j || (e3 = null), e3;
  }
  set normalizationType(e3) {
    this._set("normalizationType", e3);
  }
  addClassBreakInfo(e3, t2, i5) {
    let r3 = null;
    r3 = "number" == typeof e3 ? new a3({ minValue: e3, maxValue: t2, symbol: P(i5) }) : T(p(e3)), this.classBreakInfos.push(r3), 1 === this.classBreakInfos.length && this.notifyChange("minValue");
  }
  removeClassBreakInfo(e3, s5) {
    const t2 = this.classBreakInfos.length;
    for (let o3 = 0; o3 < t2; o3++) {
      const t3 = [this.classBreakInfos[o3].minValue, this.classBreakInfos[o3].maxValue];
      if (t3[0] === e3 && t3[1] === s5) {
        this.classBreakInfos.splice(o3, 1);
        break;
      }
    }
  }
  getBreakIndex(e3, s5) {
    return this.valueExpression && (t(s5) || t(s5.arcade)) && s.getLogger(this.declaredClass).warn(""), this.valueExpression ? this._getBreakIndexForExpression(e3, s5) : this._getBreakIndexForField(e3);
  }
  async getClassBreakInfo(e3, s5) {
    let t2 = s5;
    this.valueExpression && (t(s5) || t(s5.arcade)) && (t2 = { ...t2, arcade: await i2() });
    const o3 = this.getBreakIndex(e3, t2);
    return -1 !== o3 ? this.classBreakInfos[o3] : null;
  }
  getSymbol(e3, s5) {
    if (this.valueExpression && (t(s5) || t(s5.arcade))) return void s.getLogger(this.declaredClass).error("#getSymbol()", "Please use getSymbolAsync if valueExpression is used");
    const t2 = this.getBreakIndex(e3, s5);
    return t2 > -1 ? this.classBreakInfos[t2].symbol : this.defaultSymbol;
  }
  async getSymbolAsync(e3, s5) {
    let t2 = s5;
    if (this.valueExpression && (t(s5) || t(s5.arcade))) {
      const e4 = await i2(), { arcadeUtils: s6 } = e4;
      s6.hasGeometryOperations(this.valueExpression) && await s6.enableGeometryOperations(), t2 = { ...t2, arcade: e4 };
    }
    const o3 = this.getBreakIndex(e3, t2);
    return o3 > -1 ? this.classBreakInfos[o3].symbol : this.defaultSymbol;
  }
  getSymbols() {
    const e3 = [];
    return this.classBreakInfos.forEach((s5) => {
      s5.symbol && e3.push(s5.symbol);
    }), this.defaultSymbol && e3.push(this.defaultSymbol), e3;
  }
  getAttributeHash() {
    return this.visualVariables && this.visualVariables.reduce((e3, s5) => e3 + s5.getAttributeHash(), "");
  }
  getMeshHash() {
    const e3 = JSON.stringify(this.backgroundFillSymbol), s5 = JSON.stringify(this.defaultSymbol), t2 = `${this.normalizationField}.${this.normalizationType}.${this.normalizationTotal}`;
    return `${e3}.${s5}.${this.classBreakInfos.reduce((e4, s6) => e4 + s6.getMeshHash(), "")}.${t2}.${this.field}.${this.valueExpression}`;
  }
  get arcadeRequired() {
    return this.arcadeRequiredForVisualVariables || !!this.valueExpression;
  }
  clone() {
    return new V({ field: this.field, backgroundFillSymbol: this.backgroundFillSymbol && this.backgroundFillSymbol.clone(), defaultLabel: this.defaultLabel, defaultSymbol: this.defaultSymbol && this.defaultSymbol.clone(), valueExpression: this.valueExpression, valueExpressionTitle: this.valueExpressionTitle, classBreakInfos: p(this.classBreakInfos), isMaxInclusive: this.isMaxInclusive, normalizationField: this.normalizationField, normalizationTotal: this.normalizationTotal, normalizationType: this.normalizationType, visualVariables: p(this.visualVariables), legendOptions: p(this.legendOptions), authoringInfo: this.authoringInfo && this.authoringInfo.clone() });
  }
  async collectRequiredFields(e3, s5) {
    const t2 = [this.collectVVRequiredFields(e3, s5), this.collectSymbolFields(e3, s5)];
    await Promise.all(t2);
  }
  async collectSymbolFields(e3, s5) {
    const t2 = [...this.getSymbols().map((t3) => t3.collectRequiredFields(e3, s5)), S(e3, s5, this.valueExpression)];
    w(e3, s5, this.field), w(e3, s5, this.normalizationField), await Promise.all(t2);
  }
  _getBreakIndexForExpression(e3, s5) {
    const { viewingMode: t2, scale: o3, spatialReference: i5, arcade: r3 } = l(s5, {}), { valueExpression: n4 } = this;
    let u4 = this._compiledValueExpression.valueExpression === n4 ? this._compiledValueExpression.compiledFunction : null;
    const c4 = e2(r3).arcadeUtils;
    if (!u4) {
      const e4 = c4.createSyntaxTree(n4);
      u4 = c4.createFunction(e4), this._compiledValueExpression.compiledFunction = u4;
    }
    this._compiledValueExpression.valueExpression = n4;
    const p6 = c4.executeFunction(u4, c4.createExecContext(e3, c4.getViewInfo({ viewingMode: t2, scale: o3, spatialReference: i5 })));
    return this._getBreakIndexfromInfos(p6);
  }
  _getBreakIndexForField(e3) {
    const s5 = this.field, t2 = e3.attributes, o3 = this.normalizationType;
    let i5 = parseFloat(t2[s5]);
    if (o3) {
      const e4 = this.normalizationTotal, s6 = parseFloat(this.normalizationField ? t2[this.normalizationField] : void 0);
      if (o3 === E) i5 = Math.log(i5) * Math.LOG10E;
      else if (o3 !== j || null == e4 || isNaN(e4)) {
        if (o3 === z && !isNaN(s6)) {
          if (isNaN(i5) || isNaN(s6)) return -1;
          i5 /= s6;
        }
      } else i5 = i5 / e4 * 100;
    }
    return this._getBreakIndexfromInfos(i5);
  }
  _getBreakIndexfromInfos(e3) {
    const s5 = this.isMaxInclusive;
    if (null != e3 && "number" == typeof e3 && !isNaN(e3)) for (let t2 = 0; t2 < this.classBreakInfos.length; t2++) {
      const o3 = [this.classBreakInfos[t2].minValue, this.classBreakInfos[t2].maxValue];
      if (o3[0] <= e3 && (s5 ? e3 <= o3[1] : e3 < o3[1])) return t2;
    }
    return -1;
  }
  _areClassBreaksConsecutive() {
    const e3 = this.classBreakInfos, s5 = e3.length;
    for (let t2 = 1; t2 < s5; t2++) if (e3[t2 - 1].maxValue !== e3[t2].minValue) return false;
    return true;
  }
};
e([y(y3)], C.prototype, "backgroundFillSymbol", void 0), e([y({ type: [a3] })], C.prototype, "classBreakInfos", void 0), e([o("classBreakInfos")], C.prototype, "readClassBreakInfos", null), e([r2("classBreakInfos")], C.prototype, "writeClassBreakInfos", null), e([y({ type: String, json: { write: true } })], C.prototype, "defaultLabel", void 0), e([y(l4)], C.prototype, "defaultSymbol", void 0), e([y({ type: String, json: { write: true } })], C.prototype, "field", void 0), e([s4("field")], C.prototype, "castField", null), e([y({ type: Boolean })], C.prototype, "isMaxInclusive", void 0), e([y({ type: p4, json: { write: true } })], C.prototype, "legendOptions", void 0), e([y({ type: Number, readOnly: true, value: null, json: { read: false, write: { overridePolicy() {
  return 0 !== this.classBreakInfos.length && this._areClassBreaksConsecutive() ? { enabled: true } : { enabled: false };
} } } })], C.prototype, "minValue", null), e([y({ type: String, json: { write: true } })], C.prototype, "normalizationField", void 0), e([y({ type: Number, cast: (e3) => a(e3), json: { write: true } })], C.prototype, "normalizationTotal", void 0), e([y({ type: w2.apiValues, value: null, json: { type: w2.jsonValues, read: w2.read, write: w2.write } })], C.prototype, "normalizationType", null), e([o2({ classBreaks: "class-breaks" })], C.prototype, "type", void 0), e([y({ type: String, json: { write: true } })], C.prototype, "valueExpression", void 0), e([y({ type: String, json: { write: true } })], C.prototype, "valueExpressionTitle", void 0), C = V = e([a2("esri.renderers.ClassBreaksRenderer")], C);
var _ = C;

// node_modules/@arcgis/core/renderers/support/UniqueValue.js
var u2 = class extends i(l2) {
  constructor(o3) {
    super(o3), this.value = null, this.value2 = null, this.value3 = null;
  }
};
e([y(m3)], u2.prototype, "value", void 0), e([y(m3)], u2.prototype, "value2", void 0), e([y(m3)], u2.prototype, "value3", void 0), u2 = e([a2("esri.renderers.support.UniqueValue")], u2);
var c2 = u2;

// node_modules/@arcgis/core/renderers/support/UniqueValueClass.js
var n2 = class extends i(l2) {
  constructor(r3) {
    super(r3), this.description = null, this.label = null, this.symbol = null, this.values = null;
  }
  castValues(r3) {
    if (null == r3) return null;
    const o3 = typeof (r3 = Array.isArray(r3) ? r3 : [r3])[0];
    return "string" === o3 || "number" === o3 ? r3.map((r4) => new c2({ value: r4 })) : "object" === o3 ? r3[0] instanceof c2 ? r3 : r3.map((r4) => new c2(r4)) : null;
  }
};
e([y({ type: String, json: { write: true } })], n2.prototype, "description", void 0), e([y({ type: String, json: { write: true } })], n2.prototype, "label", void 0), e([y(l4)], n2.prototype, "symbol", void 0), e([y({ type: [c2], json: { type: [[String]], read: { reader: (r3) => r3 ? r3.map((r4) => new c2({ value: r4[0], value2: r4[1], value3: r4[2] })) : null }, write: { writer: (r3, o3) => {
  const e3 = [];
  for (const s5 of r3) {
    const r4 = [s5.value, s5.value2, s5.value3].filter(r).map((r5) => r5.toString());
    e3.push(r4);
  }
  o3.values = e3;
} } } })], n2.prototype, "values", void 0), e([s4("values")], n2.prototype, "castValues", null), n2 = e([a2("esri.renderers.support.UniqueValueClass")], n2);
var u3 = n2;

// node_modules/@arcgis/core/renderers/support/UniqueValueGroup.js
var c3 = class extends i(l2) {
  constructor(r3) {
    super(r3), this.heading = null, this.classes = null;
  }
};
e([y({ type: String, json: { write: true } })], c3.prototype, "heading", void 0), e([y({ type: [u3], json: { write: true } })], c3.prototype, "classes", void 0), c3 = e([a2("esri.renderers.support.UniqueValueGroup")], c3);
var i4 = c3;

// node_modules/@arcgis/core/renderers/support/UniqueValueInfo.js
var l6;
var p5 = l6 = class extends l2 {
  constructor(o3) {
    super(o3), this.description = null, this.label = null, this.symbol = null, this.value = null;
  }
  clone() {
    return new l6({ value: this.value, description: this.description, label: this.label, symbol: this.symbol ? this.symbol.clone() : null });
  }
  getMeshHash() {
    const o3 = JSON.stringify(this.symbol && this.symbol.toJSON());
    return `${this.value}.${o3}`;
  }
};
e([y({ type: String, json: { write: true } })], p5.prototype, "description", void 0), e([y({ type: String, json: { write: true } })], p5.prototype, "label", void 0), e([y(l4)], p5.prototype, "symbol", void 0), e([y(m3)], p5.prototype, "value", void 0), p5 = l6 = e([a2("esri.renderers.support.UniqueValueInfo")], p5);
var n3 = p5;

// node_modules/@arcgis/core/renderers/UniqueValueRenderer.js
var R;
var P2 = "esri.renderers.UniqueValueRenderer";
var C2 = s.getLogger(P2);
var $ = "uvInfos-watcher";
var z2 = "uvGroups-watcher";
var k = ",";
var A = b(n3);
function H(e3) {
  const { field1: t2, field2: s5, field3: i5, fieldDelimiter: o3, uniqueValueInfos: l7, valueExpression: r3 } = e3, u4 = !(!t2 || !s5);
  return [{ classes: (l7 ?? []).map((e4) => {
    var _a;
    const { symbol: l8, label: n4, value: a4, description: p6 } = e4, [f2, d, c4] = u4 ? ((_a = a4 == null ? void 0 : a4.toString()) == null ? void 0 : _a.split(o3 || "")) || [] : [a4], h = [];
    return (t2 || r3) && h.push(f2), s5 && h.push(d), i5 && h.push(c4), { symbol: l8, label: n4, values: [h], description: p6 };
  }) }];
}
var L = R = class extends y2(p3) {
  constructor(e3) {
    super(e3), this._valueInfoMap = {}, this._isDefaultSymbolDerived = false, this._isInfosSource = null, this.type = "unique-value", this.backgroundFillSymbol = null, this.orderByClassesEnabled = false, this.valueExpressionTitle = null, this.legendOptions = null, this.defaultLabel = null, this.portal = null, this.styleOrigin = null, this.diff = { uniqueValueInfos(e4, t2) {
      if (!e4 && !t2) return;
      if (!e4 || !t2) return { type: "complete", oldValue: e4, newValue: t2 };
      let s5 = false;
      const i5 = { type: "collection", added: [], removed: [], changed: [], unchanged: [] };
      for (let o3 = 0; o3 < t2.length; o3++) {
        const l7 = e4.find((e5) => e5.value === t2[o3].value);
        l7 ? m2(l7, t2[o3]) ? (i5.changed.push({ type: "complete", oldValue: l7, newValue: t2[o3] }), s5 = true) : i5.unchanged.push({ oldValue: l7, newValue: t2[o3] }) : (i5.added.push(t2[o3]), s5 = true);
      }
      for (let o3 = 0; o3 < e4.length; o3++) {
        t2.find((t3) => t3.value === e4[o3].value) || (i5.removed.push(e4[o3]), s5 = true);
      }
      return s5 ? i5 : void 0;
    } }, this._set("uniqueValueInfos", []), this._set("uniqueValueGroups", []);
  }
  get _cache() {
    return { compiledFunc: null };
  }
  set field(e3) {
    this._set("field", e3), this._updateFieldDelimiter(), this._updateUniqueValues();
  }
  castField(e3) {
    return null == e3 || "function" == typeof e3 ? e3 : u(e3);
  }
  writeField(e3, t2, i5, o3) {
    "string" == typeof e3 ? t2[i5] = e3 : o3 && o3.messages ? o3.messages.push(new s2("property:unsupported", "UniqueValueRenderer.field set to a function cannot be written to JSON")) : C2.error(".field: cannot write field to JSON since it's not a string value");
  }
  set field2(e3) {
    this._set("field2", e3), this._updateFieldDelimiter(), this._updateUniqueValues();
  }
  set field3(e3) {
    this._set("field3", e3), this._updateUniqueValues();
  }
  set valueExpression(e3) {
    this._set("valueExpression", e3), this._updateUniqueValues();
  }
  set defaultSymbol(e3) {
    this._isDefaultSymbolDerived = false, this._set("defaultSymbol", e3);
  }
  set fieldDelimiter(e3) {
    this._set("fieldDelimiter", e3), this._updateUniqueValues();
  }
  readPortal(e3, t2, s5) {
    return s5.portal || b2.getDefault();
  }
  readStyleOrigin(e3, t2, s5) {
    if (t2.styleName) return Object.freeze({ styleName: t2.styleName });
    if (t2.styleUrl) {
      const e4 = c(t2.styleUrl, s5);
      return Object.freeze({ styleUrl: e4 });
    }
  }
  writeStyleOrigin(e3, t2, s5, i5) {
    e3.styleName ? t2.styleName = e3.styleName : e3.styleUrl && (t2.styleUrl = m(e3.styleUrl, i5));
  }
  set uniqueValueGroups(e3) {
    this.styleOrigin ? C2.error("#uniqueValueGroups=", "Cannot modify unique value groups of a UniqueValueRenderer created from a web style") : (this._set("uniqueValueGroups", e3), this._updateInfosFromGroups(), this._isInfosSource = false, this._watchUniqueValueGroups());
  }
  set uniqueValueInfos(e3) {
    this.styleOrigin ? C2.error("#uniqueValueInfos=", "Cannot modify unique value infos of a UniqueValueRenderer created from a web style") : (this._set("uniqueValueInfos", e3), this._updateValueInfoMap(), this._updateGroupsFromInfos(), this._isInfosSource = true, this._watchUniqueValueInfos());
  }
  addUniqueValueInfo(e3, s5) {
    var _a;
    if (this.styleOrigin) return void C2.error("#addUniqueValueInfo()", "Cannot modify unique value infos of a UniqueValueRenderer created from a web style");
    let i5;
    i5 = "object" == typeof e3 ? A(e3) : new n3({ value: e3, symbol: P(s5) }), (_a = this.uniqueValueInfos) == null ? void 0 : _a.push(i5), this._valueInfoMap[i5.value] = i5, this._updateGroupsFromInfos(), this._isInfosSource = true, this._watchUniqueValueInfos();
  }
  removeUniqueValueInfo(e3) {
    if (this.styleOrigin) return void C2.error("#removeUniqueValueInfo()", "Cannot modify unique value infos of a UniqueValueRenderer created from a web style");
    const t2 = this.uniqueValueInfos;
    if (t2) for (let s5 = 0; s5 < t2.length; s5++) {
      if (t2[s5].value === e3 + "") {
        delete this._valueInfoMap[e3], t2.splice(s5, 1);
        break;
      }
    }
    this._updateGroupsFromInfos(), this._isInfosSource = true, this._watchUniqueValueInfos();
  }
  async getUniqueValueInfo(e3, t2) {
    let s5 = t2;
    return this.valueExpression && (t(t2) || t(t2.arcade)) && (s5 = { ...s5, arcade: await i2() }), this._getUniqueValueInfo(e3, s5);
  }
  getSymbol(e3, t2) {
    if (this.valueExpression && (t(t2) || t(t2.arcade))) return void C2.error("#getSymbol()", "Please use getSymbolAsync if valueExpression is used");
    const s5 = this._getUniqueValueInfo(e3, t2);
    return s5 && s5.symbol || this.defaultSymbol;
  }
  async getSymbolAsync(e3, t2) {
    let s5 = t2;
    if (this.valueExpression && (t(s5) || t(s5.arcade))) {
      const e4 = await i2(), { arcadeUtils: t3 } = e4;
      t3.hasGeometryOperations(this.valueExpression) && await t3.enableGeometryOperations(), s5 = { ...s5, arcade: e4 };
    }
    const i5 = this._getUniqueValueInfo(e3, s5);
    return i5 && i5.symbol || this.defaultSymbol;
  }
  getSymbols() {
    const e3 = [];
    for (const t2 of this.uniqueValueInfos ?? []) t2.symbol && e3.push(t2.symbol);
    return this.defaultSymbol && e3.push(this.defaultSymbol), e3;
  }
  getAttributeHash() {
    return this.visualVariables && this.visualVariables.reduce((e3, t2) => e3 + t2.getAttributeHash(), "");
  }
  getMeshHash() {
    var _a;
    const e3 = JSON.stringify(this.backgroundFillSymbol), t2 = JSON.stringify(this.defaultSymbol), s5 = (_a = this.uniqueValueInfos) == null ? void 0 : _a.reduce((e4, t3) => e4 + t3.getMeshHash(), "");
    return `${e3}.${t2}.${s5}.${`${this.field}.${this.field2}.${this.field3}.${this.fieldDelimiter}`}.${this.valueExpression}`;
  }
  clone() {
    const e3 = new R({ field: this.field, field2: this.field2, field3: this.field3, defaultLabel: this.defaultLabel, defaultSymbol: p(this.defaultSymbol), orderByClassesEnabled: this.orderByClassesEnabled, valueExpression: this.valueExpression, valueExpressionTitle: this.valueExpressionTitle, fieldDelimiter: this.fieldDelimiter, visualVariables: p(this.visualVariables), legendOptions: p(this.legendOptions), authoringInfo: this.authoringInfo && this.authoringInfo.clone(), backgroundFillSymbol: p(this.backgroundFillSymbol) });
    this._isDefaultSymbolDerived && (e3._isDefaultSymbolDerived = true), e3._set("portal", this.portal);
    const t2 = p(this.uniqueValueInfos), s5 = p(this.uniqueValueGroups);
    return this.styleOrigin && (e3._set("styleOrigin", Object.freeze(p(this.styleOrigin))), Object.freeze(t2), Object.freeze(s5)), e3._set("uniqueValueInfos", t2), e3._updateValueInfoMap(), e3._set("uniqueValueGroups", s5), e3._isInfosSource = this._isInfosSource, e3._watchUniqueValueInfosAndGroups(), e3;
  }
  get arcadeRequired() {
    return this.arcadeRequiredForVisualVariables || !!this.valueExpression;
  }
  async collectRequiredFields(e3, t2) {
    const s5 = [this.collectVVRequiredFields(e3, t2), this.collectSymbolFields(e3, t2)];
    await Promise.all(s5);
  }
  async collectSymbolFields(e3, t2) {
    const s5 = [...this.getSymbols().map((s6) => s6.collectRequiredFields(e3, t2)), S(e3, t2, this.valueExpression)];
    w(e3, t2, this.field), w(e3, t2, this.field2), w(e3, t2, this.field3), await Promise.all(s5);
  }
  populateFromStyle() {
    return p2(this.styleOrigin, { portal: this.portal }).then((e3) => {
      var _a;
      const t2 = [];
      return this._valueInfoMap = {}, e3 && e3.data && Array.isArray(e3.data.items) && e3.data.items.forEach((s5) => {
        const i5 = new f({ styleUrl: e3.styleUrl, styleName: e3.styleName, portal: this.portal, name: s5.name });
        this.defaultSymbol || s5.name !== e3.data.defaultItem || (this.defaultSymbol = i5, this._isDefaultSymbolDerived = true);
        const o3 = new n3({ value: s5.name, symbol: i5 });
        t2.push(o3), this._valueInfoMap[s5.name] = o3;
      }), this._set("uniqueValueInfos", Object.freeze(t2)), this._updateGroupsFromInfos(true), this._isInfosSource = null, this._watchUniqueValueInfos(), !this.defaultSymbol && ((_a = this.uniqueValueInfos) == null ? void 0 : _a.length) && (this.defaultSymbol = this.uniqueValueInfos[0].symbol, this._isDefaultSymbolDerived = true), this;
    });
  }
  _updateFieldDelimiter() {
    this.field && this.field2 && !this.fieldDelimiter && this._set("fieldDelimiter", k);
  }
  _updateUniqueValues() {
    null != this._isInfosSource && (this._isInfosSource ? this._updateGroupsFromInfos() : this._updateInfosFromGroups());
  }
  _updateValueInfoMap() {
    this._valueInfoMap = {};
    const { uniqueValueInfos: e3 } = this;
    if (e3) for (const t2 of e3) this._valueInfoMap[t2.value + ""] = t2;
  }
  _watchUniqueValueInfosAndGroups() {
    this._watchUniqueValueInfos(), this._watchUniqueValueGroups();
  }
  _watchUniqueValueInfos() {
    this.removeHandles($);
    const { uniqueValueInfos: e3 } = this;
    if (e3) {
      const t2 = [];
      for (const s5 of e3) t2.push(l3(() => ({ symbol: s5.symbol, value: s5.value, label: s5.label, description: s5.description }), (e4, t3) => {
        e4 !== t3 && (this._updateGroupsFromInfos(), this._isInfosSource = true);
      }, { sync: true }));
      this.addHandles(t2, $);
    }
  }
  _watchUniqueValueGroups() {
    this.removeHandles(z2);
    const { uniqueValueGroups: e3 } = this;
    if (e3) {
      const t2 = [];
      for (const s5 of e3) {
        t2.push(l3(() => ({ classes: s5.classes }), (e4, t3) => {
          e4 !== t3 && (this._updateInfosFromGroups(), this._isInfosSource = false);
        }, { sync: true }));
        for (const e4 of s5.classes ?? []) t2.push(l3(() => ({ symbol: e4.symbol, values: e4.values, label: e4.label, description: e4.description }), (e5, t3) => {
          e5 !== t3 && (this._updateInfosFromGroups(), this._isInfosSource = false);
        }, { sync: true }));
      }
      this.addHandles(t2, z2);
    }
  }
  _updateInfosFromGroups() {
    if (!this.uniqueValueGroups) return this._set("uniqueValueInfos", null), this._updateValueInfoMap(), void this._watchUniqueValueInfos();
    const e3 = [], { field: t2, field2: s5, field3: i5, fieldDelimiter: o3, uniqueValueGroups: l7, valueExpression: r3 } = this;
    if (!t2 && !r3) return this._set("uniqueValueInfos", e3), this._updateValueInfoMap(), void this._watchUniqueValueInfos();
    const u4 = !(!t2 || !s5);
    for (const n4 of l7) for (const t3 of n4.classes ?? []) {
      const { symbol: l8, label: r4, values: n5, description: a4 } = t3;
      for (const t4 of n5 ?? []) {
        const { value: n6, value2: p6, value3: f2 } = t4, d = [n6];
        s5 && d.push(p6), i5 && d.push(f2);
        const c4 = u4 ? d.join(o3 || "") : d[0];
        e3.push(new n3({ symbol: l8, label: r4, value: c4, description: a4 }));
      }
    }
    this._set("uniqueValueInfos", e3), this._updateValueInfoMap(), this._watchUniqueValueInfos();
  }
  _updateGroupsFromInfos(e3 = false) {
    if (!this.uniqueValueInfos) return this._set("uniqueValueGroups", null), void this._watchUniqueValueGroups();
    const { field: t2, field2: s5, valueExpression: i5, fieldDelimiter: o3, uniqueValueInfos: l7 } = this;
    if (!t2 && !i5 || !l7.length) return this._set("uniqueValueGroups", []), void this._watchUniqueValueGroups();
    const r3 = !(!t2 || !s5), u4 = l7.map((e4) => {
      var _a;
      const { symbol: t3, label: s6, value: i6, description: l8 } = e4, [u5, n5, a4] = r3 ? ((_a = i6 == null ? void 0 : i6.toString()) == null ? void 0 : _a.split(o3 || "")) || [] : [i6];
      return new u3({ symbol: t3, label: s6, description: l8, values: [new c2({ value: u5, value2: n5, value3: a4 })] });
    }), n4 = [new i4({ classes: u4 })];
    e3 && Object.freeze(n4), this._set("uniqueValueGroups", n4), this._watchUniqueValueGroups();
  }
  _getUniqueValueInfo(e3, t2) {
    return this.valueExpression ? this._getUnqiueValueInfoForExpression(e3, t2) : this._getUnqiueValueInfoForFields(e3);
  }
  _getUnqiueValueInfoForExpression(e3, t2) {
    const { viewingMode: s5, scale: i5, spatialReference: o3, arcade: l7 } = l(t2, {});
    let n4 = this._cache.compiledFunc;
    const a4 = e2(l7).arcadeUtils;
    if (!n4) {
      const e4 = a4.createSyntaxTree(this.valueExpression);
      n4 = a4.createFunction(e4), this._cache.compiledFunc = n4;
    }
    const p6 = a4.executeFunction(n4, a4.createExecContext(e3, a4.getViewInfo({ viewingMode: s5, scale: i5, spatialReference: o3 })));
    return this._valueInfoMap[p6 + ""];
  }
  _getUnqiueValueInfoForFields(e3) {
    const t2 = this.field, s5 = e3.attributes;
    let i5;
    if ("function" != typeof t2 && this.field2) {
      const e4 = this.field2, o3 = this.field3, l7 = [];
      t2 && l7.push(s5[t2]), e4 && l7.push(s5[e4]), o3 && l7.push(s5[o3]), i5 = l7.join(this.fieldDelimiter || "");
    } else "function" == typeof t2 ? i5 = t2(e3) : t2 && (i5 = s5[t2]);
    return this._valueInfoMap[i5 + ""];
  }
  static fromPortalStyle(e3, t2) {
    const s5 = new R(t2 && t2.properties);
    s5._set("styleOrigin", Object.freeze({ styleName: e3 })), s5._set("portal", t2 && t2.portal || b2.getDefault());
    const i5 = s5.populateFromStyle();
    return i5.catch((t3) => {
      C2.error(`#fromPortalStyle('${e3}'[, ...])`, "Failed to create unique value renderer from style name", t3);
    }), i5;
  }
  static fromStyleUrl(e3, t2) {
    const s5 = new R(t2 && t2.properties);
    s5._set("styleOrigin", Object.freeze({ styleUrl: e3 }));
    const i5 = s5.populateFromStyle();
    return i5.catch((t3) => {
      C2.error(`#fromStyleUrl('${e3}'[, ...])`, "Failed to create unique value renderer from style URL", t3);
    }), i5;
  }
};
e([y({ readOnly: true })], L.prototype, "_cache", null), e([o2({ uniqueValue: "unique-value" })], L.prototype, "type", void 0), e([y(y3)], L.prototype, "backgroundFillSymbol", void 0), e([y({ value: null, json: { type: String, read: { source: "field1" }, write: { target: "field1" } } })], L.prototype, "field", null), e([s4("field")], L.prototype, "castField", null), e([r2("field")], L.prototype, "writeField", null), e([y({ type: String, value: null, json: { write: true } })], L.prototype, "field2", null), e([y({ type: String, value: null, json: { write: true } })], L.prototype, "field3", null), e([y({ type: Boolean, json: { name: "drawInClassOrder", default: false, write: true, origins: { "web-scene": { write: false } } } })], L.prototype, "orderByClassesEnabled", void 0), e([y({ type: String, value: null, json: { write: true } })], L.prototype, "valueExpression", null), e([y({ type: String, json: { write: true } })], L.prototype, "valueExpressionTitle", void 0), e([y({ type: p4, json: { write: true } })], L.prototype, "legendOptions", void 0), e([y({ type: String, json: { write: true } })], L.prototype, "defaultLabel", void 0), e([y(n({ ...l4 }, { json: { write: { overridePolicy() {
  return { enabled: !this._isDefaultSymbolDerived };
} }, origins: { "web-scene": { write: { overridePolicy() {
  return { enabled: !this._isDefaultSymbolDerived };
} } } } } }))], L.prototype, "defaultSymbol", null), e([y({ type: String, value: null, json: { write: true } })], L.prototype, "fieldDelimiter", null), e([y({ type: b2, readOnly: true })], L.prototype, "portal", void 0), e([o("portal", ["styleName"])], L.prototype, "readPortal", null), e([y({ readOnly: true, json: { write: { enabled: false, overridePolicy: () => ({ enabled: true }) } } })], L.prototype, "styleOrigin", void 0), e([o("styleOrigin", ["styleName", "styleUrl"])], L.prototype, "readStyleOrigin", null), e([r2("styleOrigin", { styleName: { type: String }, styleUrl: { type: String } })], L.prototype, "writeStyleOrigin", null), e([y({ type: [i4], json: { read: { source: ["uniqueValueGroups", "uniqueValueInfos"], reader: (e3, t2, s5) => (t2.uniqueValueGroups || H(t2)).map((e4) => i4.fromJSON(e4, s5)) }, write: { overridePolicy() {
  return this.styleOrigin ? { enabled: false } : { enabled: true };
} } } })], L.prototype, "uniqueValueGroups", null), e([y({ type: [n3], json: { read: false, write: { overridePolicy() {
  return this.styleOrigin ? { enabled: false } : { enabled: true };
} } } })], L.prototype, "uniqueValueInfos", null), L = R = e([a2(P2)], L);
var T2 = L;

export {
  a3 as a,
  _,
  n3 as n,
  T2 as T
};
//# sourceMappingURL=chunk-IOBN373Z.js.map
