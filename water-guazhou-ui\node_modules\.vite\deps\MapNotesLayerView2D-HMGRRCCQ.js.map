{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/MapNotesLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import i from\"../../../Graphic.js\";import s from\"../../../core/Collection.js\";import{isSome as t,isNone as r}from\"../../../core/maybe.js\";import{watch as a,on as h,initial as o}from\"../../../core/reactiveUtils.js\";import\"../../../core/Logger.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import\"../../../core/Error.js\";import\"../../../core/has.js\";import{subclass as n}from\"../../../core/accessorSupport/decorators/subclass.js\";import{Container as p}from\"../engine/Container.js\";import{GroupContainer as c}from\"../engine/webgl/GroupContainer.js\";import{LayerView2DMixin as l}from\"./LayerView2D.js\";import g from\"./graphics/GraphicContainer.js\";import d from\"./graphics/GraphicsView2D.js\";import u from\"../../layers/LayerView.js\";const f=\"sublayers\",m=\"layerView\",w=Object.freeze({remove(){},pause(){},resume(){}});let y=class extends(l(u)){constructor(){super(...arguments),this._highlightIds=new Map,this.container=new c}async fetchPopupFeatures(e){return Array.from(this.graphicsViews(),(i=>i.hitTest(e).filter((e=>!!e.popupTemplate)))).flat()}*graphicsViews(){t(this._graphicsViewsFeatureCollectionMap)?yield*this._graphicsViewsFeatureCollectionMap.keys():t(this._graphicsViews)?yield*this._graphicsViews:yield*[]}async hitTest(e,i){return Array.from(this.graphicsViews(),(i=>{const s=i.hitTest(e);if(t(this._graphicsViewsFeatureCollectionMap)){const e=this._graphicsViewsFeatureCollectionMap.get(i);for(const i of s)!i.popupTemplate&&e.popupTemplate&&(i.popupTemplate=e.popupTemplate),i.sourceLayer=i.layer=this.layer}return s})).flat().map((i=>({type:\"graphic\",graphic:i,layer:this.layer,mapPoint:e})))}highlight(e){let r;\"number\"==typeof e?r=[e]:e instanceof i?r=[e.uid]:Array.isArray(e)&&e.length>0?r=\"number\"==typeof e[0]?e:e.map((e=>e&&e.uid)):s.isCollection(e)&&(r=e.map((e=>e&&e.uid)).toArray());const a=r?.filter(t);return a?.length?(this._addHighlight(a),{remove:()=>{this._removeHighlight(a)}}):w}update(e){for(const i of this.graphicsViews())i.processUpdate(e)}attach(){const e=this.view,i=()=>this.requestUpdate(),s=this.layer.featureCollections;if(t(s)&&s.length){this._graphicsViewsFeatureCollectionMap=new Map;for(const t of s){const s=new g(this.view.featuresTilingScheme),r=new d({view:e,graphics:t.source,renderer:t.renderer,requestUpdateCallback:i,container:s});this._graphicsViewsFeatureCollectionMap.set(r,t),this.container.addChild(r.container),this.addHandles([a((()=>t.visible),(e=>r.container.visible=e),o),a((()=>r.updating),(()=>this.notifyChange(\"updating\")),o)],m)}this._updateHighlight()}else t(this.layer.sublayers)&&this.addHandles(h((()=>this.layer.sublayers),\"change\",(()=>this._createGraphicsViews()),{onListenerAdd:()=>this._createGraphicsViews(),onListenerRemove:()=>this._destroyGraphicsViews()}),f)}detach(){this._destroyGraphicsViews(),this.removeHandles(f)}moveStart(){}moveEnd(){}viewChange(){for(const e of this.graphicsViews())e.viewChange()}isUpdating(){for(const e of this.graphicsViews())if(e.updating)return!0;return!1}_destroyGraphicsViews(){this.container.removeAllChildren(),this.removeHandles(m);for(const e of this.graphicsViews())e.destroy();this._graphicsViews=null,this._graphicsViewsFeatureCollectionMap=null}_createGraphicsViews(){if(this._destroyGraphicsViews(),r(this.layer.sublayers))return;const e=[],i=this.view,s=()=>this.requestUpdate();for(const t of this.layer.sublayers){const r=new p,h=new g(this.view.featuresTilingScheme);h.fadeTransitionEnabled=!0;const n=new d({view:i,graphics:t.graphics,requestUpdateCallback:s,container:h});this.addHandles([t.on(\"graphic-update\",n.graphicUpdateHandler),a((()=>t.visible),(e=>n.container.visible=e),o),a((()=>n.updating),(()=>this.notifyChange(\"updating\")),o)],m),r.addChild(n.container),this.container.addChild(r),e.push(n)}this._graphicsViews=e,this._updateHighlight()}_addHighlight(e){for(const i of e)if(this._highlightIds.has(i)){const e=this._highlightIds.get(i);this._highlightIds.set(i,e+1)}else this._highlightIds.set(i,1);this._updateHighlight()}_removeHighlight(e){for(const i of e)if(this._highlightIds.has(i)){const e=this._highlightIds.get(i)-1;0===e?this._highlightIds.delete(i):this._highlightIds.set(i,e)}this._updateHighlight()}_updateHighlight(){const e=Array.from(this._highlightIds.keys());for(const i of this.graphicsViews())i.setHighlight(e)}};y=e([n(\"esri.views.2d.layers.MapNotesLayerView2D\")],y);const _=y;export{_ as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIwzB,IAAMA,KAAE;AAAR,IAAoB,IAAE;AAAtB,IAAkC,IAAE,OAAO,OAAO,EAAC,SAAQ;AAAC,GAAE,QAAO;AAAC,GAAE,SAAQ;AAAC,EAAC,CAAC;AAAE,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,oBAAI,OAAI,KAAK,YAAU,IAAIC;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBC,IAAE;AAAC,WAAO,MAAM,KAAK,KAAK,cAAc,GAAG,CAAAC,OAAGA,GAAE,QAAQD,EAAC,EAAE,OAAQ,CAAAA,OAAG,CAAC,CAACA,GAAE,aAAc,CAAE,EAAE,KAAK;AAAA,EAAC;AAAA,EAAC,CAAC,gBAAe;AAAC,MAAE,KAAK,kCAAkC,IAAE,OAAM,KAAK,mCAAmC,KAAK,IAAE,EAAE,KAAK,cAAc,IAAE,OAAM,KAAK,iBAAe,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQA,IAAEC,IAAE;AAAC,WAAO,MAAM,KAAK,KAAK,cAAc,GAAG,CAAAA,OAAG;AAAC,YAAM,IAAEA,GAAE,QAAQD,EAAC;AAAE,UAAG,EAAE,KAAK,kCAAkC,GAAE;AAAC,cAAMA,KAAE,KAAK,mCAAmC,IAAIC,EAAC;AAAE,mBAAUA,MAAK,EAAE,EAACA,GAAE,iBAAeD,GAAE,kBAAgBC,GAAE,gBAAcD,GAAE,gBAAeC,GAAE,cAAYA,GAAE,QAAM,KAAK;AAAA,MAAK;AAAC,aAAO;AAAA,IAAC,CAAE,EAAE,KAAK,EAAE,IAAK,CAAAA,QAAI,EAAC,MAAK,WAAU,SAAQA,IAAE,OAAM,KAAK,OAAM,UAASD,GAAC,EAAG;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,QAAID;AAAE,gBAAU,OAAOC,KAAED,KAAE,CAACC,EAAC,IAAEA,cAAa,IAAED,KAAE,CAACC,GAAE,GAAG,IAAE,MAAM,QAAQA,EAAC,KAAGA,GAAE,SAAO,IAAED,KAAE,YAAU,OAAOC,GAAE,CAAC,IAAEA,KAAEA,GAAE,IAAK,CAAAA,OAAGA,MAAGA,GAAE,GAAI,IAAE,EAAE,aAAaA,EAAC,MAAID,KAAEC,GAAE,IAAK,CAAAA,OAAGA,MAAGA,GAAE,GAAI,EAAE,QAAQ;AAAG,UAAME,KAAEH,MAAA,gBAAAA,GAAG,OAAO;AAAG,YAAOG,MAAA,gBAAAA,GAAG,WAAQ,KAAK,cAAcA,EAAC,GAAE,EAAC,QAAO,MAAI;AAAC,WAAK,iBAAiBA,EAAC;AAAA,IAAC,EAAC,KAAG;AAAA,EAAC;AAAA,EAAC,OAAOF,IAAE;AAAC,eAAUC,MAAK,KAAK,cAAc,EAAE,CAAAA,GAAE,cAAcD,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMA,KAAE,KAAK,MAAKC,KAAE,MAAI,KAAK,cAAc,GAAE,IAAE,KAAK,MAAM;AAAmB,QAAG,EAAE,CAAC,KAAG,EAAE,QAAO;AAAC,WAAK,qCAAmC,oBAAI;AAAI,iBAAUE,MAAK,GAAE;AAAC,cAAMC,KAAE,IAAI,EAAE,KAAK,KAAK,oBAAoB,GAAEL,KAAE,IAAI,GAAE,EAAC,MAAKC,IAAE,UAASG,GAAE,QAAO,UAASA,GAAE,UAAS,uBAAsBF,IAAE,WAAUG,GAAC,CAAC;AAAE,aAAK,mCAAmC,IAAIL,IAAEI,EAAC,GAAE,KAAK,UAAU,SAASJ,GAAE,SAAS,GAAE,KAAK,WAAW,CAAC,EAAG,MAAII,GAAE,SAAU,CAAAH,OAAGD,GAAE,UAAU,UAAQC,IAAG,CAAC,GAAE,EAAG,MAAID,GAAE,UAAW,MAAI,KAAK,aAAa,UAAU,GAAG,CAAC,CAAC,GAAE,CAAC;AAAA,MAAC;AAAC,WAAK,iBAAiB;AAAA,IAAC,MAAM,GAAE,KAAK,MAAM,SAAS,KAAG,KAAK,WAAWG,GAAG,MAAI,KAAK,MAAM,WAAW,UAAU,MAAI,KAAK,qBAAqB,GAAG,EAAC,eAAc,MAAI,KAAK,qBAAqB,GAAE,kBAAiB,MAAI,KAAK,sBAAsB,EAAC,CAAC,GAAEJ,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,sBAAsB,GAAE,KAAK,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,eAAUE,MAAK,KAAK,cAAc,EAAE,CAAAA,GAAE,WAAW;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,eAAUA,MAAK,KAAK,cAAc,EAAE,KAAGA,GAAE,SAAS,QAAM;AAAG,WAAM;AAAA,EAAE;AAAA,EAAC,wBAAuB;AAAC,SAAK,UAAU,kBAAkB,GAAE,KAAK,cAAc,CAAC;AAAE,eAAUA,MAAK,KAAK,cAAc,EAAE,CAAAA,GAAE,QAAQ;AAAE,SAAK,iBAAe,MAAK,KAAK,qCAAmC;AAAA,EAAI;AAAA,EAAC,uBAAsB;AAAC,QAAG,KAAK,sBAAsB,GAAE,EAAE,KAAK,MAAM,SAAS,EAAE;AAAO,UAAMA,KAAE,CAAC,GAAEC,KAAE,KAAK,MAAK,IAAE,MAAI,KAAK,cAAc;AAAE,eAAUE,MAAK,KAAK,MAAM,WAAU;AAAC,YAAMJ,KAAE,IAAIM,MAAEA,KAAE,IAAI,EAAE,KAAK,KAAK,oBAAoB;AAAE,MAAAA,GAAE,wBAAsB;AAAG,YAAM,IAAE,IAAI,GAAE,EAAC,MAAKJ,IAAE,UAASE,GAAE,UAAS,uBAAsB,GAAE,WAAUE,GAAC,CAAC;AAAE,WAAK,WAAW,CAACF,GAAE,GAAG,kBAAiB,EAAE,oBAAoB,GAAE,EAAG,MAAIA,GAAE,SAAU,CAAAH,OAAG,EAAE,UAAU,UAAQA,IAAG,CAAC,GAAE,EAAG,MAAI,EAAE,UAAW,MAAI,KAAK,aAAa,UAAU,GAAG,CAAC,CAAC,GAAE,CAAC,GAAED,GAAE,SAAS,EAAE,SAAS,GAAE,KAAK,UAAU,SAASA,EAAC,GAAEC,GAAE,KAAK,CAAC;AAAA,IAAC;AAAC,SAAK,iBAAeA,IAAE,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,eAAUC,MAAKD,GAAE,KAAG,KAAK,cAAc,IAAIC,EAAC,GAAE;AAAC,YAAMD,KAAE,KAAK,cAAc,IAAIC,EAAC;AAAE,WAAK,cAAc,IAAIA,IAAED,KAAE,CAAC;AAAA,IAAC,MAAM,MAAK,cAAc,IAAIC,IAAE,CAAC;AAAE,SAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAE;AAAC,eAAUC,MAAKD,GAAE,KAAG,KAAK,cAAc,IAAIC,EAAC,GAAE;AAAC,YAAMD,KAAE,KAAK,cAAc,IAAIC,EAAC,IAAE;AAAE,YAAID,KAAE,KAAK,cAAc,OAAOC,EAAC,IAAE,KAAK,cAAc,IAAIA,IAAED,EAAC;AAAA,IAAC;AAAC,SAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,UAAMA,KAAE,MAAM,KAAK,KAAK,cAAc,KAAK,CAAC;AAAE,eAAUC,MAAK,KAAK,cAAc,EAAE,CAAAA,GAAE,aAAaD,EAAC;AAAA,EAAC;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["f", "r", "e", "i", "a", "t", "s", "h"]}