{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/features/tileRenderers/support/HeatmapSource.js", "../../@arcgis/core/views/2d/layers/features/tileRenderers/HeatmapTileRenderer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{drawHeatmap as t}from\"../../../../../../renderers/support/heatmapUtils.js\";class i{constructor(){this.gradient=null,this.height=512,this.intensities=null,this.width=512}render(i){t(i,512,this.intensities,this.gradient,this.minDensity,this.maxDensity)}}export{i as HeatmapSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import\"../../../../../core/Logger.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import\"../../../../../core/Error.js\";import\"../../../../../core/has.js\";import{subclass as t}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{generateGradient as i}from\"../../../../../renderers/support/heatmapUtils.js\";import{BitmapTileContainer as r}from\"../../../engine/BitmapTileContainer.js\";import s from\"./BaseTileRenderer.js\";import{HeatmapSource as n}from\"./support/HeatmapSource.js\";let o=class extends s{constructor(e){super(e),this._intensityInfo={minDensity:0,maxDensity:0},this.type=\"heatmap\",this.featuresView={attributeView:{initialize:()=>{},requestUpdate:()=>{}},requestRender:()=>{}},this._container=new r(e.tileInfoView)}createTile(e){const t=this._container.createTile(e);return this.tileInfoView.getTileCoords(t.bitmap,e),t.bitmap.resolution=this.tileInfoView.getTileResolution(e),t}onConfigUpdate(){const e=this.layer.renderer;if(\"heatmap\"===e.type){const{minDensity:t,maxDensity:r,colorStops:s}=e;this._intensityInfo.minDensity=t,this._intensityInfo.maxDensity=r,this._gradient=i(s),this.tiles.forEach((e=>{const i=e.bitmap.source;i&&(i.minDensity=t,i.maxDensity=r,i.gradient=this._gradient,e.bitmap.invalidateTexture())}))}}hitTest(){return Promise.resolve([])}install(e){e.addChild(this._container)}uninstall(e){this._container.removeAllChildren(),e.removeChild(this._container)}disposeTile(e){this._container.removeChild(e),e.destroy()}supportsRenderer(e){return e&&\"heatmap\"===e.type}onTileData(e){const t=this.tiles.get(e.tileKey);if(!t)return;const i=e.intensityInfo,{minDensity:r,maxDensity:s}=this._intensityInfo,o=t.bitmap.source||new n;o.intensities=i&&i.matrix||null,o.minDensity=r,o.maxDensity=s,o.gradient=this._gradient,t.bitmap.source=o,this._container.addChild(t),this._container.requestRender(),this.requestUpdate()}onTileError(e){console.error(e)}lockGPUUploads(){}unlockGPUUploads(){}fetchResource(e,t){return console.error(e),Promise.reject()}};o=e([t(\"esri.views.2d.layers.features.tileRenderers.HeatmapTileRenderer\")],o);const a=o;export{a as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIkF,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,WAAS,MAAK,KAAK,SAAO,KAAI,KAAK,cAAY,MAAK,KAAK,QAAM;AAAA,EAAG;AAAA,EAAC,OAAOA,IAAE;AAAC,MAAEA,IAAE,KAAI,KAAK,aAAY,KAAK,UAAS,KAAK,YAAW,KAAK,UAAU;AAAA,EAAC;AAAC;;;ACA8V,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,iBAAe,EAAC,YAAW,GAAE,YAAW,EAAC,GAAE,KAAK,OAAK,WAAU,KAAK,eAAa,EAAC,eAAc,EAAC,YAAW,MAAI;AAAA,IAAC,GAAE,eAAc,MAAI;AAAA,IAAC,EAAC,GAAE,eAAc,MAAI;AAAA,IAAC,EAAC,GAAE,KAAK,aAAW,IAAI,EAAEA,GAAE,YAAY;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAM,IAAE,KAAK,WAAW,WAAWA,EAAC;AAAE,WAAO,KAAK,aAAa,cAAc,EAAE,QAAOA,EAAC,GAAE,EAAE,OAAO,aAAW,KAAK,aAAa,kBAAkBA,EAAC,GAAE;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,UAAMA,KAAE,KAAK,MAAM;AAAS,QAAG,cAAYA,GAAE,MAAK;AAAC,YAAK,EAAC,YAAW,GAAE,YAAW,GAAE,YAAW,EAAC,IAAEA;AAAE,WAAK,eAAe,aAAW,GAAE,KAAK,eAAe,aAAW,GAAE,KAAK,YAAU,EAAE,CAAC,GAAE,KAAK,MAAM,QAAS,CAAAA,OAAG;AAAC,cAAMC,KAAED,GAAE,OAAO;AAAO,QAAAC,OAAIA,GAAE,aAAW,GAAEA,GAAE,aAAW,GAAEA,GAAE,WAAS,KAAK,WAAUD,GAAE,OAAO,kBAAkB;AAAA,MAAE,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,QAAQ,QAAQ,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,IAAAA,GAAE,SAAS,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,SAAK,WAAW,kBAAkB,GAAEA,GAAE,YAAY,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,WAAW,YAAYA,EAAC,GAAEA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,WAAOA,MAAG,cAAYA,GAAE;AAAA,EAAI;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAM,IAAE,KAAK,MAAM,IAAIA,GAAE,OAAO;AAAE,QAAG,CAAC,EAAE;AAAO,UAAMC,KAAED,GAAE,eAAc,EAAC,YAAW,GAAE,YAAW,EAAC,IAAE,KAAK,gBAAeD,KAAE,EAAE,OAAO,UAAQ,IAAI;AAAE,IAAAA,GAAE,cAAYE,MAAGA,GAAE,UAAQ,MAAKF,GAAE,aAAW,GAAEA,GAAE,aAAW,GAAEA,GAAE,WAAS,KAAK,WAAU,EAAE,OAAO,SAAOA,IAAE,KAAK,WAAW,SAAS,CAAC,GAAE,KAAK,WAAW,cAAc,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,YAAQ,MAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE,GAAE;AAAC,WAAO,QAAQ,MAAMA,EAAC,GAAE,QAAQ,OAAO;AAAA,EAAC;AAAC;AAAED,KAAE,EAAE,CAAC,EAAE,iEAAiE,CAAC,GAAEA,EAAC;AAAE,IAAMG,KAAEH;", "names": ["i", "o", "e", "i", "a"]}