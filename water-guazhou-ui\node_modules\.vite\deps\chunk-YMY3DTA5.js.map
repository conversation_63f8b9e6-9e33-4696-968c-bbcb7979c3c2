{"version": 3, "sources": ["../../@arcgis/core/rest/query/operations/pbfOptimizedFeatureSet.js", "../../@arcgis/core/rest/query/operations/pbfFeatureServiceParser.js", "../../@arcgis/core/rest/query/operations/pbfQueryUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getMetersPerVerticalUnitForSR as e}from\"../../../core/unitUtils.js\";import{equals as t}from\"../../../geometry/support/spatialReferenceUtils.js\";import{hasGeometry as r,OptimizedFeature as o}from\"../../../layers/graphics/OptimizedFeature.js\";import i from\"../../../layers/graphics/OptimizedFeatureSet.js\";import s from\"../../../layers/graphics/OptimizedGeometry.js\";const n=[\"esriGeometryPoint\",\"esriGeometryMultipoint\",\"esriGeometryPolyline\",\"esriGeometryPolygon\"];class a{constructor(e){this._options=e,this.geometryTypes=n,this._coordinatePtr=0,this._vertexDimension=0}createFeatureResult(){return new i}prepareFeatures(e){this._vertexDimension=2,e.hasZ&&this._vertexDimension++,e.hasM&&this._vertexDimension++}finishFeatureResult(o){if(!o||!o.features||!o.hasZ||!this._options.sourceSpatialReference||!o.spatialReference||t(o.spatialReference,this._options.sourceSpatialReference)||o.spatialReference.vcsWkid)return;const i=e(this._options.sourceSpatialReference)/e(o.spatialReference);if(1!==i)for(const e of o.features){if(!r(e))continue;const t=e.geometry.coords;for(let e=2;e<t.length;e+=3)t[e]*=i}}addFeature(e,t){e.features.push(t)}createFeature(){return new o}createSpatialReference(){return{wkid:0}}createGeometry(){return new s}addField(e,t){e.fields.push(t)}allocateCoordinates(e){e.coords.length=e.lengths.reduce(((e,t)=>e+t),0)*this._vertexDimension,this._coordinatePtr=0}addCoordinate(e,t){e.coords[this._coordinatePtr++]=t}addCoordinatePoint(e,t){e.coords.push(t)}addLength(e,t){e.lengths.push(t)}addQueryGeometry(e,t){e.queryGeometry=t.queryGeometry,e.queryGeometryType=t.queryGeometryType}createPointGeometry(){return new s}}export{n as OPTIMIZED_GEOMETRY_TYPES,a as OptimizedFeatureSetParserContext};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../core/Error.js\";import{isSome as t}from\"../../../core/maybe.js\";import s from\"../../../core/pbf.js\";import r from\"../../../layers/graphics/OptimizedGeometry.js\";import{OPTIMIZED_GEOMETRY_TYPES as a}from\"./pbfOptimizedFeatureSet.js\";const n=[\"esriFieldTypeSmallInteger\",\"esriFieldTypeInteger\",\"esriFieldTypeSingle\",\"esriFieldTypeDouble\",\"esriFieldTypeString\",\"esriFieldTypeDate\",\"esriFieldTypeOID\",\"esriFieldTypeGeometry\",\"esriFieldTypeBlob\",\"esriFieldTypeRaster\",\"esriFieldTypeGUID\",\"esriFieldTypeGlobalID\",\"esriFieldTypeXML\"],o=[\"sqlTypeBigInt\",\"sqlTypeBinary\",\"sqlTypeBit\",\"sqlTypeChar\",\"sqlTypeDate\",\"sqlTypeDecimal\",\"sqlTypeDouble\",\"sqlTypeFloat\",\"sqlTypeGeometry\",\"sqlTypeGUID\",\"sqlTypeInteger\",\"sqlTypeLongNVarchar\",\"sqlTypeLongVarbinary\",\"sqlTypeLongVarchar\",\"sqlTypeNChar\",\"sqlTypeNVarchar\",\"sqlTypeOther\",\"sqlTypeReal\",\"sqlTypeSmallInt\",\"sqlTypeSqlXml\",\"sqlTypeTime\",\"sqlTypeTimestamp\",\"sqlTypeTimestamp2\",\"sqlTypeTinyInt\",\"sqlTypeVarbinary\",\"sqlTypeVarchar\"],i=[\"upperLeft\",\"lowerLeft\"];function c(e){return e>=n.length?null:n[e]}function l(e){return e>=o.length?null:o[e]}function g(e){return e>=i.length?null:i[e]}function p(e,t){return t>=e.geometryTypes.length?null:e.geometryTypes[t]}function u(e,t,s){const r=3,a=e.asUnsafe(),n=t.createPointGeometry(s);for(;a.next();)switch(a.tag()){case r:{const e=a.getUInt32(),s=a.pos()+e;let r=0;for(;a.pos()<s;)t.addCoordinatePoint(n,a.getSInt64(),r++);break}default:a.skip()}return n}function f(e,t,s){const r=2,a=3,n=e.asUnsafe(),o=t.createGeometry(s),i=2+(s.hasZ?1:0)+(s.hasM?1:0);for(;n.next();)switch(n.tag()){case r:{const e=n.getUInt32(),s=n.pos()+e;let r=0;for(;n.pos()<s;)t.addLength(o,n.getUInt32(),r++);break}case a:{const e=n.getUInt32(),s=n.pos()+e;let r=0;for(t.allocateCoordinates(o);n.pos()<s;)t.addCoordinate(o,n.getSInt64(),r),r++,r===i&&(r=0);break}default:n.skip()}return o}function y(e){const t=1,s=2,n=3,o=e.asUnsafe(),i=new r;let c=\"esriGeometryPoint\";for(;o.next();)switch(o.tag()){case s:{const e=o.getUInt32(),t=o.pos()+e;for(;o.pos()<t;)i.lengths.push(o.getUInt32());break}case n:{const e=o.getUInt32(),t=o.pos()+e;for(;o.pos()<t;)i.coords.push(o.getSInt64());break}case t:c=a[o.getEnum()];break;default:o.skip()}return{queryGeometry:i,queryGeometryType:c}}function b(e){const t=1,s=2,r=3,a=4,n=5,o=6,i=7,c=8,l=9,g=e.asUnsafe();for(;g.next();)switch(g.tag()){case t:return g.getString();case s:return g.getFloat();case r:return g.getDouble();case a:return g.getSInt32();case n:return g.getUInt32();case o:return g.getInt64();case i:return g.getUInt64();case c:return g.getSInt64();case l:return g.getBool();default:return g.skip(),null}return null}function k(e){const t=1,s=2,r=3,a=4,n=5,o=6,i=e.asUnsafe(),g={type:c(0)};for(;i.next();)switch(i.tag()){case t:g.name=i.getString();break;case s:g.type=c(i.getEnum());break;case r:g.alias=i.getString();break;case a:g.sqlType=l(i.getEnum());break;case n:i.skip();break;case o:g.defaultValue=i.getString();break;default:i.skip()}return g}function d(e){const t=1,s=2,r={},a=e.asUnsafe();for(;a.next();)switch(a.tag()){case t:r.name=a.getString();break;case s:r.isSystemMaintained=a.getBool();break;default:a.skip()}return r}function m(e,t,s,r){const a=1,n=2,o=4,i=t.createFeature(s);let c=0;for(;e.next();)switch(e.tag()){case a:{const t=r[c++].name;i.attributes[t]=e.processMessage(b);break}case n:i.geometry=e.processMessageWithArgs(f,t,s);break;case o:i.centroid=e.processMessageWithArgs(u,t,s);break;default:e.skip()}return i}function h(e){const t=1,s=2,r=3,a=4,n=[1,1,1,1],o=e.asUnsafe();for(;o.next();)switch(o.tag()){case t:n[0]=o.getDouble();break;case s:n[1]=o.getDouble();break;case a:n[2]=o.getDouble();break;case r:n[3]=o.getDouble();break;default:o.skip()}return n}function T(e){const t=1,s=2,r=3,a=4,n=[0,0,0,0],o=e.asUnsafe();for(;o.next();)switch(o.tag()){case t:n[0]=o.getDouble();break;case s:n[1]=o.getDouble();break;case a:n[2]=o.getDouble();break;case r:n[3]=o.getDouble();break;default:o.skip()}return n}function q(e){const t=1,s=2,r=3,a={originPosition:g(0)},n=e.asUnsafe();for(;n.next();)switch(n.tag()){case t:a.originPosition=g(n.getEnum());break;case s:a.scale=n.processMessage(h);break;case r:a.translate=n.processMessage(T);break;default:n.skip()}return a}function I(e){const t=1,s=2,r=3,a={},n=e.asUnsafe();for(;n.next();)switch(n.tag()){case t:a.shapeAreaFieldName=n.getString();break;case s:a.shapeLengthFieldName=n.getString();break;case r:a.units=n.getString();break;default:n.skip()}return a}function F(e,t){const s=1,r=2,a=3,n=4,o=5,i=t.createSpatialReference();for(;e.next();)switch(e.tag()){case s:i.wkid=e.getUInt32();break;case o:i.wkt=e.getString();break;case r:i.latestWkid=e.getUInt32();break;case a:i.vcsWkid=e.getUInt32();break;case n:i.latestVcsWkid=e.getUInt32();break;default:e.skip()}return i}function U(e,t){const s=1,r=2,a=3,n=4,o=5,i=7,c=8,l=9,g=10,u=11,f=12,y=13,b=15,h=t.createFeatureResult(),T=e.asUnsafe();h.geometryType=p(t,0);let U=!1;for(;T.next();)switch(T.tag()){case s:h.objectIdFieldName=T.getString();break;case a:h.globalIdFieldName=T.getString();break;case n:h.geohashFieldName=T.getString();break;case o:h.geometryProperties=T.processMessage(I);break;case i:h.geometryType=p(t,T.getEnum());break;case c:h.spatialReference=T.processMessageWithArgs(F,t);break;case g:h.hasZ=T.getBool();break;case u:h.hasM=T.getBool();break;case f:h.transform=T.processMessage(q);break;case l:{const e=T.getBool();h.exceededTransferLimit=e;break}case y:t.addField(h,T.processMessage(k));break;case b:U||(t.prepareFeatures(h),U=!0),t.addFeature(h,T.processMessageWithArgs(m,t,h,h.fields));break;case r:h.uniqueIdField=T.processMessage(d);break;default:T.skip()}return t.finishFeatureResult(h),h}function S(e,s){const r=1,a=4,n={};let o=null;for(;e.next();)switch(e.tag()){case a:o=e.processMessageWithArgs(y);break;case r:n.featureResult=e.processMessageWithArgs(U,s);break;default:e.skip()}return t(o)&&n.featureResult&&s.addQueryGeometry(n,o),n}function w(t,r){try{const e=2,a=new s(new Uint8Array(t),new DataView(t)),n={};for(;a.next();)if(a.tag()===e)n.queryResult=a.processMessageWithArgs(S,r);else a.skip();return n}catch(a){throw new e(\"query:parsing-pbf\",\"Error while parsing FeatureSet PBF payload\",{error:a})}}export{w as parseFeatureQuery,c as parseFieldType,q as parseTransform};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{parseFeatureQuery as e}from\"./pbfFeatureServiceParser.js\";function t(t,r){const u=e(t,r),o=u.queryResult.featureResult,s=u.queryResult.queryGeometry,y=u.queryResult.queryGeometryType;if(o&&o.features&&o.features.length&&o.objectIdFieldName){const e=o.objectIdFieldName;for(const t of o.features)t.attributes&&(t.objectId=t.attributes[e])}return o&&(o.queryGeometry=s,o.queryGeometryType=y),o}export{t as parsePBFFeatureQuery};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAIoX,IAAMA,KAAE,CAAC,qBAAoB,0BAAyB,wBAAuB,qBAAqB;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,WAASA,IAAE,KAAK,gBAAcD,IAAE,KAAK,iBAAe,GAAE,KAAK,mBAAiB;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,WAAO,IAAIC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,SAAK,mBAAiB,GAAEA,GAAE,QAAM,KAAK,oBAAmBA,GAAE,QAAM,KAAK;AAAA,EAAkB;AAAA,EAAC,oBAAoBC,IAAE;AAAC,QAAG,CAACA,MAAG,CAACA,GAAE,YAAU,CAACA,GAAE,QAAM,CAAC,KAAK,SAAS,0BAAwB,CAACA,GAAE,oBAAkB,EAAEA,GAAE,kBAAiB,KAAK,SAAS,sBAAsB,KAAGA,GAAE,iBAAiB,QAAQ;AAAO,UAAMC,KAAE,EAAE,KAAK,SAAS,sBAAsB,IAAE,EAAED,GAAE,gBAAgB;AAAE,QAAG,MAAIC,GAAE,YAAUF,MAAKC,GAAE,UAAS;AAAC,UAAG,CAAC,EAAED,EAAC,EAAE;AAAS,YAAMG,KAAEH,GAAE,SAAS;AAAO,eAAQA,KAAE,GAAEA,KAAEG,GAAE,QAAOH,MAAG,EAAE,CAAAG,GAAEH,EAAC,KAAGE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWF,IAAEG,IAAE;AAAC,IAAAH,GAAE,SAAS,KAAKG,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,IAAIC;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,WAAM,EAAC,MAAK,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,WAAO,IAAI;AAAA,EAAC;AAAA,EAAC,SAASJ,IAAEG,IAAE;AAAC,IAAAH,GAAE,OAAO,KAAKG,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBH,IAAE;AAAC,IAAAA,GAAE,OAAO,SAAOA,GAAE,QAAQ,OAAQ,CAACA,IAAEG,OAAIH,KAAEG,IAAG,CAAC,IAAE,KAAK,kBAAiB,KAAK,iBAAe;AAAA,EAAC;AAAA,EAAC,cAAcH,IAAEG,IAAE;AAAC,IAAAH,GAAE,OAAO,KAAK,gBAAgB,IAAEG;AAAA,EAAC;AAAA,EAAC,mBAAmBH,IAAEG,IAAE;AAAC,IAAAH,GAAE,OAAO,KAAKG,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUH,IAAEG,IAAE;AAAC,IAAAH,GAAE,QAAQ,KAAKG,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBH,IAAEG,IAAE;AAAC,IAAAH,GAAE,gBAAcG,GAAE,eAAcH,GAAE,oBAAkBG,GAAE;AAAA,EAAiB;AAAA,EAAC,sBAAqB;AAAC,WAAO,IAAI;AAAA,EAAC;AAAC;;;ACAp3C,IAAME,KAAE,CAAC,6BAA4B,wBAAuB,uBAAsB,uBAAsB,uBAAsB,qBAAoB,oBAAmB,yBAAwB,qBAAoB,uBAAsB,qBAAoB,yBAAwB,kBAAkB;AAArS,IAAuS,IAAE,CAAC,iBAAgB,iBAAgB,cAAa,eAAc,eAAc,kBAAiB,iBAAgB,gBAAe,mBAAkB,eAAc,kBAAiB,uBAAsB,wBAAuB,sBAAqB,gBAAe,mBAAkB,gBAAe,eAAc,mBAAkB,iBAAgB,eAAc,oBAAmB,qBAAoB,kBAAiB,oBAAmB,gBAAgB;AAA/tB,IAAiuB,IAAE,CAAC,aAAY,WAAW;AAAE,SAAS,EAAEC,IAAE;AAAC,SAAOA,MAAGD,GAAE,SAAO,OAAKA,GAAEC,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOA,MAAG,EAAE,SAAO,OAAK,EAAEA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOA,MAAG,EAAE,SAAO,OAAK,EAAEA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAOA,MAAGD,GAAE,cAAc,SAAO,OAAKA,GAAE,cAAcC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAEJ,GAAE,SAAS,GAAED,KAAEE,GAAE,oBAAoBC,EAAC;AAAE,SAAKE,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKD,IAAE;AAAC,YAAMH,KAAEI,GAAE,UAAU,GAAEF,KAAEE,GAAE,IAAI,IAAEJ;AAAE,UAAIG,KAAE;AAAE,aAAKC,GAAE,IAAI,IAAEF,KAAG,CAAAD,GAAE,mBAAmBF,IAAEK,GAAE,UAAU,GAAED,IAAG;AAAE;AAAA,IAAK;AAAA,IAAC;AAAQ,MAAAC,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOL;AAAC;AAAC,SAAS,EAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEL,KAAEC,GAAE,SAAS,GAAEK,KAAEJ,GAAE,eAAeC,EAAC,GAAEI,KAAE,KAAGJ,GAAE,OAAK,IAAE,MAAIA,GAAE,OAAK,IAAE;AAAG,SAAKH,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKI,IAAE;AAAC,YAAMH,KAAED,GAAE,UAAU,GAAEG,KAAEH,GAAE,IAAI,IAAEC;AAAE,UAAIG,KAAE;AAAE,aAAKJ,GAAE,IAAI,IAAEG,KAAG,CAAAD,GAAE,UAAUI,IAAEN,GAAE,UAAU,GAAEI,IAAG;AAAE;AAAA,IAAK;AAAA,IAAC,KAAKC,IAAE;AAAC,YAAMJ,KAAED,GAAE,UAAU,GAAEG,KAAEH,GAAE,IAAI,IAAEC;AAAE,UAAIG,KAAE;AAAE,WAAIF,GAAE,oBAAoBI,EAAC,GAAEN,GAAE,IAAI,IAAEG,KAAG,CAAAD,GAAE,cAAcI,IAAEN,GAAE,UAAU,GAAEI,EAAC,GAAEA,MAAIA,OAAIG,OAAIH,KAAE;AAAG;AAAA,IAAK;AAAA,IAAC;AAAQ,MAAAJ,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOM;AAAC;AAAC,SAAS,EAAEL,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEH,KAAE,GAAEM,KAAEL,GAAE,SAAS,GAAEM,KAAE,IAAI;AAAE,MAAIC,KAAE;AAAoB,SAAKF,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKH,IAAE;AAAC,YAAMF,KAAEK,GAAE,UAAU,GAAEJ,KAAEI,GAAE,IAAI,IAAEL;AAAE,aAAKK,GAAE,IAAI,IAAEJ,KAAG,CAAAK,GAAE,QAAQ,KAAKD,GAAE,UAAU,CAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAKN,IAAE;AAAC,YAAMC,KAAEK,GAAE,UAAU,GAAEJ,KAAEI,GAAE,IAAI,IAAEL;AAAE,aAAKK,GAAE,IAAI,IAAEJ,KAAG,CAAAK,GAAE,OAAO,KAAKD,GAAE,UAAU,CAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAKJ;AAAE,MAAAM,KAAER,GAAEM,GAAE,QAAQ,CAAC;AAAE;AAAA,IAAM;AAAQ,MAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAM,EAAC,eAAcC,IAAE,mBAAkBC,GAAC;AAAC;AAAC,SAAS,EAAEP,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEL,KAAE,GAAEM,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAET,GAAE,SAAS;AAAE,SAAKS,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKR;AAAE,aAAOQ,GAAE,UAAU;AAAA,IAAE,KAAKP;AAAE,aAAOO,GAAE,SAAS;AAAA,IAAE,KAAKN;AAAE,aAAOM,GAAE,UAAU;AAAA,IAAE,KAAKL;AAAE,aAAOK,GAAE,UAAU;AAAA,IAAE,KAAKV;AAAE,aAAOU,GAAE,UAAU;AAAA,IAAE,KAAKJ;AAAE,aAAOI,GAAE,SAAS;AAAA,IAAE,KAAKH;AAAE,aAAOG,GAAE,UAAU;AAAA,IAAE,KAAKF;AAAE,aAAOE,GAAE,UAAU;AAAA,IAAE,KAAKD;AAAE,aAAOC,GAAE,QAAQ;AAAA,IAAE;AAAQ,aAAOA,GAAE,KAAK,GAAE;AAAA,EAAI;AAAC,SAAO;AAAI;AAAC,SAAS,EAAET,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEL,KAAE,GAAEM,KAAE,GAAEC,KAAEN,GAAE,SAAS,GAAES,KAAE,EAAC,MAAK,EAAE,CAAC,EAAC;AAAE,SAAKH,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKL;AAAE,MAAAQ,GAAE,OAAKH,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKJ;AAAE,MAAAO,GAAE,OAAK,EAAEH,GAAE,QAAQ,CAAC;AAAE;AAAA,IAAM,KAAKH;AAAE,MAAAM,GAAE,QAAMH,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKF;AAAE,MAAAK,GAAE,UAAQ,EAAEH,GAAE,QAAQ,CAAC;AAAE;AAAA,IAAM,KAAKP;AAAE,MAAAO,GAAE,KAAK;AAAE;AAAA,IAAM,KAAKD;AAAE,MAAAI,GAAE,eAAaH,GAAE,UAAU;AAAE;AAAA,IAAM;AAAQ,MAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,SAAS,EAAET,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,CAAC,GAAEC,KAAEJ,GAAE,SAAS;AAAE,SAAKI,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKH;AAAE,MAAAE,GAAE,OAAKC,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKF;AAAE,MAAAC,GAAE,qBAAmBC,GAAE,QAAQ;AAAE;AAAA,IAAM;AAAQ,MAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAEL,KAAE,GAAEM,KAAE,GAAEC,KAAEL,GAAE,cAAcC,EAAC;AAAE,MAAIK,KAAE;AAAE,SAAKP,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKI,IAAE;AAAC,YAAMH,KAAEE,GAAEI,IAAG,EAAE;AAAK,MAAAD,GAAE,WAAWL,EAAC,IAAED,GAAE,eAAe,CAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAKD;AAAE,MAAAO,GAAE,WAASN,GAAE,uBAAuB,GAAEC,IAAEC,EAAC;AAAE;AAAA,IAAM,KAAKG;AAAE,MAAAC,GAAE,WAASN,GAAE,uBAAuB,GAAEC,IAAEC,EAAC;AAAE;AAAA,IAAM;AAAQ,MAAAF,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOM;AAAC;AAAC,SAAS,EAAEN,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEL,KAAE,CAAC,GAAE,GAAE,GAAE,CAAC,GAAEM,KAAEL,GAAE,SAAS;AAAE,SAAKK,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKJ;AAAE,MAAAF,GAAE,CAAC,IAAEM,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKH;AAAE,MAAAH,GAAE,CAAC,IAAEM,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKD;AAAE,MAAAL,GAAE,CAAC,IAAEM,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKF;AAAE,MAAAJ,GAAE,CAAC,IAAEM,GAAE,UAAU;AAAE;AAAA,IAAM;AAAQ,MAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEL,KAAE,CAAC,GAAE,GAAE,GAAE,CAAC,GAAEM,KAAEL,GAAE,SAAS;AAAE,SAAKK,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKJ;AAAE,MAAAF,GAAE,CAAC,IAAEM,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKH;AAAE,MAAAH,GAAE,CAAC,IAAEM,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKD;AAAE,MAAAL,GAAE,CAAC,IAAEM,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKF;AAAE,MAAAJ,GAAE,CAAC,IAAEM,GAAE,UAAU;AAAE;AAAA,IAAM;AAAQ,MAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,EAAC,gBAAe,EAAE,CAAC,EAAC,GAAEL,KAAEC,GAAE,SAAS;AAAE,SAAKD,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKE;AAAE,MAAAG,GAAE,iBAAe,EAAEL,GAAE,QAAQ,CAAC;AAAE;AAAA,IAAM,KAAKG;AAAE,MAAAE,GAAE,QAAML,GAAE,eAAe,CAAC;AAAE;AAAA,IAAM,KAAKI;AAAE,MAAAC,GAAE,YAAUL,GAAE,eAAe,CAAC;AAAE;AAAA,IAAM;AAAQ,MAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,SAAS,EAAEJ,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,CAAC,GAAEL,KAAEC,GAAE,SAAS;AAAE,SAAKD,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKE;AAAE,MAAAG,GAAE,qBAAmBL,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKG;AAAE,MAAAE,GAAE,uBAAqBL,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKI;AAAE,MAAAC,GAAE,QAAML,GAAE,UAAU;AAAE;AAAA,IAAM;AAAQ,MAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,SAAS,EAAEJ,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEL,KAAE,GAAEM,KAAE,GAAEC,KAAEL,GAAE,uBAAuB;AAAE,SAAKD,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKE;AAAE,MAAAI,GAAE,OAAKN,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKK;AAAE,MAAAC,GAAE,MAAIN,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKG;AAAE,MAAAG,GAAE,aAAWN,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKI;AAAE,MAAAE,GAAE,UAAQN,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKD;AAAE,MAAAO,GAAE,gBAAcN,GAAE,UAAU;AAAE;AAAA,IAAM;AAAQ,MAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOM;AAAC;AAAC,SAAS,EAAEN,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEL,KAAE,GAAEM,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAEb,GAAE,oBAAoB,GAAEc,KAAEf,GAAE,SAAS;AAAE,EAAAc,GAAE,eAAa,EAAEb,IAAE,CAAC;AAAE,MAAIe,KAAE;AAAG,SAAKD,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKb;AAAE,MAAAY,GAAE,oBAAkBC,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKX;AAAE,MAAAU,GAAE,oBAAkBC,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKhB;AAAE,MAAAe,GAAE,mBAAiBC,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKV;AAAE,MAAAS,GAAE,qBAAmBC,GAAE,eAAe,CAAC;AAAE;AAAA,IAAM,KAAKT;AAAE,MAAAQ,GAAE,eAAa,EAAEb,IAAEc,GAAE,QAAQ,CAAC;AAAE;AAAA,IAAM,KAAKR;AAAE,MAAAO,GAAE,mBAAiBC,GAAE,uBAAuB,GAAEd,EAAC;AAAE;AAAA,IAAM,KAAKQ;AAAE,MAAAK,GAAE,OAAKC,GAAE,QAAQ;AAAE;AAAA,IAAM,KAAKL;AAAE,MAAAI,GAAE,OAAKC,GAAE,QAAQ;AAAE;AAAA,IAAM,KAAKJ;AAAE,MAAAG,GAAE,YAAUC,GAAE,eAAe,CAAC;AAAE;AAAA,IAAM,KAAKP,IAAE;AAAC,YAAMR,KAAEe,GAAE,QAAQ;AAAE,MAAAD,GAAE,wBAAsBd;AAAE;AAAA,IAAK;AAAA,IAAC,KAAKY;AAAE,MAAAX,GAAE,SAASa,IAAEC,GAAE,eAAe,CAAC,CAAC;AAAE;AAAA,IAAM,KAAKF;AAAE,MAAAG,OAAIf,GAAE,gBAAgBa,EAAC,GAAEE,KAAE,OAAIf,GAAE,WAAWa,IAAEC,GAAE,uBAAuB,GAAEd,IAAEa,IAAEA,GAAE,MAAM,CAAC;AAAE;AAAA,IAAM,KAAKX;AAAE,MAAAW,GAAE,gBAAcC,GAAE,eAAe,CAAC;AAAE;AAAA,IAAM;AAAQ,MAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOd,GAAE,oBAAoBa,EAAC,GAAEA;AAAC;AAAC,SAAS,EAAEd,IAAEE,IAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,GAAEL,KAAE,CAAC;AAAE,MAAIM,KAAE;AAAK,SAAKL,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKI;AAAE,MAAAC,KAAEL,GAAE,uBAAuB,CAAC;AAAE;AAAA,IAAM,KAAKG;AAAE,MAAAJ,GAAE,gBAAcC,GAAE,uBAAuB,GAAEE,EAAC;AAAE;AAAA,IAAM;AAAQ,MAAAF,GAAE,KAAK;AAAA,EAAC;AAAC,SAAO,EAAEK,EAAC,KAAGN,GAAE,iBAAeG,GAAE,iBAAiBH,IAAEM,EAAC,GAAEN;AAAC;AAAC,SAAS,EAAEE,IAAEE,IAAE;AAAC,MAAG;AAAC,UAAMH,KAAE,GAAEI,KAAE,IAAI,EAAE,IAAI,WAAWH,EAAC,GAAE,IAAI,SAASA,EAAC,CAAC,GAAEF,KAAE,CAAC;AAAE,WAAKK,GAAE,KAAK,IAAG,KAAGA,GAAE,IAAI,MAAIJ,GAAE,CAAAD,GAAE,cAAYK,GAAE,uBAAuB,GAAED,EAAC;AAAA,QAAO,CAAAC,GAAE,KAAK;AAAE,WAAOL;AAAA,EAAC,SAAOK,IAAE;AAAC,UAAM,IAAI,EAAE,qBAAoB,8CAA6C,EAAC,OAAMA,GAAC,CAAC;AAAA,EAAC;AAAC;;;ACAtgM,SAASa,GAAEA,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEF,IAAEC,EAAC,GAAEE,KAAED,GAAE,YAAY,eAAcE,KAAEF,GAAE,YAAY,eAAcG,KAAEH,GAAE,YAAY;AAAkB,MAAGC,MAAGA,GAAE,YAAUA,GAAE,SAAS,UAAQA,GAAE,mBAAkB;AAAC,UAAMG,KAAEH,GAAE;AAAkB,eAAUH,MAAKG,GAAE,SAAS,CAAAH,GAAE,eAAaA,GAAE,WAASA,GAAE,WAAWM,EAAC;AAAA,EAAE;AAAC,SAAOH,OAAIA,GAAE,gBAAcC,IAAED,GAAE,oBAAkBE,KAAGF;AAAC;", "names": ["n", "e", "o", "i", "t", "s", "n", "e", "t", "s", "r", "a", "o", "i", "c", "l", "g", "u", "f", "y", "b", "h", "T", "U", "t", "r", "u", "o", "s", "y", "e"]}