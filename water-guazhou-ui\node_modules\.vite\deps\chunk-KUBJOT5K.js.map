{"version": 3, "sources": ["../../@arcgis/core/geometry/support/normalizeUtilsCommon.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../Polyline.js\";import n from\"../SpatialReference.js\";import{isPolygon as t}from\"./jsonUtils.js\";const r={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new e({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:n.WebMercator}),minus180Line:new e({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:n.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new e({paths:[[[180,-180],[180,180]]],spatialReference:n.WGS84}),minus180Line:new e({paths:[[[-180,-180],[-180,180]]],spatialReference:n.WGS84})}};function i(e,n){return Math.ceil((e-n)/(2*n))}function s(e,n){const t=o(e);for(const r of t)for(const e of r)e[0]+=n;return e}function o(e){return t(e)?e.rings:e.paths}export{r as cutParams,o as getGeometryParts,i as offsetMagnitude,s as updatePolyGeometry};\n"], "mappings": ";;;;;;;;;;;AAI+G,IAAM,IAAE,EAAC,QAAO,EAAC,MAAK,sBAAmB,MAAK,uBAAoB,aAAY,IAAI,EAAE,EAAC,OAAM,CAAC,CAAC,CAAC,sBAAmB,qBAAmB,GAAE,CAAC,sBAAmB,oBAAkB,CAAC,CAAC,GAAE,kBAAiB,EAAE,YAAW,CAAC,GAAE,cAAa,IAAI,EAAE,EAAC,OAAM,CAAC,CAAC,CAAC,uBAAoB,qBAAmB,GAAE,CAAC,uBAAoB,oBAAkB,CAAC,CAAC,GAAE,kBAAiB,EAAE,YAAW,CAAC,EAAC,GAAE,MAAK,EAAC,MAAK,KAAI,MAAK,MAAK,aAAY,IAAI,EAAE,EAAC,OAAM,CAAC,CAAC,CAAC,KAAI,IAAI,GAAE,CAAC,KAAI,GAAG,CAAC,CAAC,GAAE,kBAAiB,EAAE,MAAK,CAAC,GAAE,cAAa,IAAI,EAAE,EAAC,OAAM,CAAC,CAAC,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,GAAG,CAAC,CAAC,GAAE,kBAAiB,EAAE,MAAK,CAAC,EAAC,EAAC;AAAE,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,KAAK,MAAM,IAAE,MAAI,IAAE,EAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAE,CAAC;AAAE,aAAUA,MAAK,EAAE,YAAUC,MAAKD,GAAE,CAAAC,GAAE,CAAC,KAAG;AAAE,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,CAAC,IAAE,EAAE,QAAM,EAAE;AAAK;", "names": ["r", "e"]}