-- 测点站点测试数据
-- 为涵养水位功能创建对应的测点数据

-- 清空现有数据（可选）
-- TRUNCATE TABLE tb_station;

-- 插入测点数据
-- 注意：请将 '13814000-1dd2-11b2-8080-808080808080' 替换为实际的租户ID
INSERT INTO tb_station (
    id, name, type, address, location, additional_info, remark, 
    order_num, create_time, tenant_id
) VALUES
-- 测点1 - 瓜州县第一水厂
('station_001', '瓜州县第一水厂', '水厂', '瓜州县城关镇', '东经95.78°，北纬40.52°', 
 '{"capacity": "5000m³/day", "depth": "15m", "wellType": "深井"}', '主要供水点，水质优良', 
 1, NOW(), '13814000-1dd2-11b2-8080-808080808080'),

-- 测点2 - 瓜州县第二水厂  
('station_002', '瓜州县第二水厂', '水厂', '瓜州县柳园镇', '东经95.85°，北纬40.48°', 
 '{"capacity": "8000m³/day", "depth": "18m", "wellType": "深井"}', '备用水源，水量充足', 
 2, NOW(), '13814000-1dd2-11b2-8080-808080808080'),

-- 测点3 - 瓜州县第三水厂
('station_003', '瓜州县第三水厂', '水厂', '瓜州县三道沟镇', '东经95.92°，北纬40.45°', 
 '{"capacity": "3000m³/day", "depth": "12m", "wellType": "浅井"}', '季节性供水，需要监控', 
 3, NOW(), '13814000-1dd2-11b2-8080-808080808080'),

-- 测点4 - 瓜州县第四水厂
('station_004', '瓜州县第四水厂', '水厂', '瓜州县双塔镇', '东经95.88°，北纬40.55°', 
 '{"capacity": "6000m³/day", "depth": "16m", "wellType": "深井"}', '新建水厂，设备先进', 
 4, NOW(), '13814000-1dd2-11b2-8080-808080808080'),

-- 测点5 - 瓜州县第五水厂
('station_005', '瓜州县第五水厂', '水厂', '瓜州县河东镇', '东经95.75°，北纬40.58°', 
 '{"capacity": "4000m³/day", "depth": "14m", "wellType": "深井"}', '农村供水点，服务周边村镇', 
 5, NOW(), '13814000-1dd2-11b2-8080-808080808080');

-- 验证插入结果
SELECT id, name, type, location, remark FROM tb_station WHERE tenant_id = '13814000-1dd2-11b2-8080-808080808080';
