import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  b
} from "./chunk-HP475EI3.js";

// node_modules/@arcgis/core/rest/support/FindParameters.js
var p = class extends l {
  constructor(e2) {
    super(e2), this.contains = true, this.dynamicLayerInfos = null, this.gdbVersion = null, this.geometryPrecision = null, this.layerDefinitions = null, this.layerIds = null, this.maxAllowableOffset = null, this.outSpatialReference = null, this.returnGeometry = false, this.searchFields = null, this.searchText = null;
  }
};
e([y({ type: Boolean, json: { write: { enabled: true, isRequired: true } } })], p.prototype, "contains", void 0), e([y({ type: [Object], json: { read: { source: "dynamicLayers" }, write: { target: "dynamicLayers" } } })], p.prototype, "dynamicLayerInfos", void 0), e([y({ type: String, json: { write: true } })], p.prototype, "gdbVersion", void 0), e([y({ type: Number, json: { write: true } })], p.prototype, "geometryPrecision", void 0), e([y({ type: [Object], json: { write: true } })], p.prototype, "layerDefinitions", void 0), e([y({ type: [Number], json: { write: true } })], p.prototype, "layerIds", void 0), e([y({ type: Number, json: { write: true } })], p.prototype, "maxAllowableOffset", void 0), e([y({ type: f, json: { read: { source: "outSR" }, write: { target: "outSR" } } })], p.prototype, "outSpatialReference", void 0), e([y({ type: Boolean, json: { write: { enabled: true, isRequired: true } } })], p.prototype, "returnGeometry", void 0), e([y({ type: [String], json: { write: true } })], p.prototype, "searchFields", void 0), e([y({ type: String, json: { write: true } })], p.prototype, "searchText", void 0), p = e([a("esri.rest.support.FindParameters")], p), p.from = b(p);
var n = p;

export {
  n
};
//# sourceMappingURL=chunk-MDABT5CH.js.map
