{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/ArcGISService.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import r from\"../../core/Logger.js\";import{isSome as e}from\"../../core/maybe.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as o}from\"../../core/accessorSupport/decorators/subclass.js\";import{parse as i,sanitizeUrl as l}from\"../support/arcgisLayerUrl.js\";const p=p=>{let c=class extends p{get title(){if(this._get(\"title\")&&\"defaults\"!==this.originOf(\"title\"))return this._get(\"title\");if(this.url){const t=i(this.url);if(e(t)&&t.title)return t.title}return this._get(\"title\")||\"\"}set title(t){this._set(\"title\",t)}set url(t){this._set(\"url\",l(t,r.getLogger(this.declaredClass)))}};return t([s()],c.prototype,\"title\",null),t([s({type:String})],c.prototype,\"url\",null),c=t([o(\"esri.layers.mixins.ArcGISService\")],c),c};export{p as ArcGISService};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIib,IAAM,IAAE,CAAAA,OAAG;AAAC,MAAI,IAAE,cAAcA,GAAC;AAAA,IAAC,IAAI,QAAO;AAAC,UAAG,KAAK,KAAK,OAAO,KAAG,eAAa,KAAK,SAAS,OAAO,EAAE,QAAO,KAAK,KAAK,OAAO;AAAE,UAAG,KAAK,KAAI;AAAC,cAAM,IAAE,EAAE,KAAK,GAAG;AAAE,YAAG,EAAE,CAAC,KAAG,EAAE,MAAM,QAAO,EAAE;AAAA,MAAK;AAAC,aAAO,KAAK,KAAK,OAAO,KAAG;AAAA,IAAE;AAAA,IAAC,IAAI,MAAM,GAAE;AAAC,WAAK,KAAK,SAAQ,CAAC;AAAA,IAAC;AAAA,IAAC,IAAI,IAAI,GAAE;AAAC,WAAK,KAAK,OAAM,EAAE,GAAE,EAAE,UAAU,KAAK,aAAa,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAE,CAAC,GAAE;AAAC;", "names": ["p"]}