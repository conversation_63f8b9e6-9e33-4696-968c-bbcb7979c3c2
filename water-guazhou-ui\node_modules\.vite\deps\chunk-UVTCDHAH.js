import {
  Z,
  _,
  ft,
  lt,
  ot,
  ut,
  yt
} from "./chunk-T7HWQQFI.js";
import {
  t
} from "./chunk-DUEDINK5.js";
import {
  e as e3
} from "./chunk-MZ267CZB.js";
import {
  M,
  h,
  i,
  r as r2,
  s
} from "./chunk-ST2RRB55.js";
import {
  n
} from "./chunk-NWZTRS6O.js";
import {
  u
} from "./chunk-THUK4WUF.js";
import {
  e as e4
} from "./chunk-OYGWWPGZ.js";
import {
  e as e2,
  f,
  i as i2,
  r as r3
} from "./chunk-77E52HT5.js";
import {
  a as a2,
  z
} from "./chunk-SROTSYJS.js";
import {
  n as n2
} from "./chunk-FOE4ICAJ.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  r
} from "./chunk-EGHLQERQ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/core/libs/gl-matrix-2/types/vec2.js
function n3(n4) {
  return n4 instanceof Float32Array && n4.length >= 2;
}
function r4(n4) {
  return Array.isArray(n4) && n4.length >= 2;
}
function t2(t3) {
  return n3(t3) || r4(t3);
}

// node_modules/@arcgis/core/views/2d/ViewState.js
var b;
var G = [0, 0];
var O = b = class extends l {
  constructor(t3) {
    super(t3), this._viewpoint2D = { center: n2(), rotation: 0, scale: 0, spatialReference: void 0 }, this.center = [0, 0], this.extent = new w2(), this.id = 0, this.inverseTransform = e4(), this.resolution = 0, this.rotation = 0, this.scale = 0, this.transform = e4(), this.transformNoRotation = e4(), this.displayMat3 = e3(), this.displayViewMat3 = e3(), this.viewMat3 = e3(), this.viewMat2d = n(), this.worldScreenWidth = 0, this.size = [0, 0];
  }
  set pixelRatio(t3) {
    this._set("pixelRatio", t3), this._update();
  }
  set size(t3) {
    this._set("size", t3), this._update();
  }
  set viewpoint(t3) {
    if (t3) {
      const s2 = this._viewpoint2D, i3 = t3.targetGeometry;
      s2.center[0] = i3.x, s2.center[1] = i3.y, s2.rotation = t3.rotation, s2.scale = t3.scale, s2.spatialReference = i3.spatialReference;
    }
    this._update();
  }
  copy(t3) {
    const s2 = this.size, i3 = this.viewpoint;
    return i3 && s2 ? (this.viewpoint = Z(i3, t3.viewpoint), this._set("size", a2(s2, t3.size))) : (this.viewpoint = t3.viewpoint.clone(), this._set("size", [t3.size[0], t3.size[1]])), this._set("pixelRatio", t3.pixelRatio), this;
  }
  clone() {
    return new b({ size: this.size, viewpoint: this.viewpoint.clone(), pixelRatio: this.pixelRatio });
  }
  toMap(t3, s2, i3) {
    return t2(s2) ? z(t3, s2, this.inverseTransform) : (G[0] = s2, G[1] = i3, z(t3, G, this.inverseTransform));
  }
  toScreen(t3, s2, i3) {
    return t2(s2) ? z(t3, s2, this.transform) : (G[0] = s2, G[1] = i3, z(t3, G, this.transform));
  }
  toScreenNoRotation(t3, s2, i3) {
    return t2(s2) ? z(t3, s2, this.transformNoRotation) : (G[0] = s2, G[1] = i3, z(t3, G, this.transformNoRotation));
  }
  getScreenTransform(t3, s2) {
    const { center: i3 } = this._viewpoint2D, e5 = this._get("pixelRatio") || 1, o = this._get("size");
    return ut(t3, i3, o, s2, 0, e5), t3;
  }
  _update() {
    const { center: t3, spatialReference: i3, scale: e5, rotation: o } = this._viewpoint2D, c = this._get("pixelRatio") || 1, m = this._get("size"), d = new u({ targetGeometry: new w(t3[0], t3[1], i3), scale: e5, rotation: o });
    if (this._set("viewpoint", d), !m || !i3 || !e5) return;
    this.resolution = ot(d), this.rotation = o, this.scale = e5, this.spatialReference = i3, a2(this.center, t3);
    const y2 = 0 !== m[0] ? 2 / m[0] : 0, _2 = 0 !== m[1] ? -2 / m[1] : 0;
    s(this.displayMat3, y2, 0, 0, 0, _2, 0, -1, 1, 1);
    const x = r2(this.viewMat3), z2 = t(m[0] / 2, m[1] / 2), g = t(-m[0] / 2, -m[1] / 2), D = r(o);
    M(x, x, z2), h(x, x, D), M(x, x, g), i(this.displayViewMat3, this.displayMat3, x);
    const b2 = f(this.viewMat2d, z2);
    return e2(b2, b2, D), i2(b2, b2, g), _(this.extent, d, m), ft(this.transform, d, m, c), r3(this.inverseTransform, this.transform), lt(this.transformNoRotation, d, m, c), this.worldScreenWidth = yt(this.spatialReference, this.resolution), this._set("id", this.id + 1), this;
  }
};
e([y({ readOnly: true })], O.prototype, "id", void 0), e([y({ value: 1, json: { write: true } })], O.prototype, "pixelRatio", null), e([y({ json: { write: true } })], O.prototype, "size", null), e([y()], O.prototype, "spatialReference", void 0), e([y({ type: u, json: { write: true } })], O.prototype, "viewpoint", null), O = b = e([a("esri.views.2d.ViewState")], O);
var U = O;

export {
  U
};
//# sourceMappingURL=chunk-UVTCDHAH.js.map
