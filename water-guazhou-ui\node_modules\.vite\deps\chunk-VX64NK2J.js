import {
  C,
  <PERSON>,
  J,
  L,
  O,
  Re,
  U,
  Y,
  fe,
  ie,
  le,
  re,
  t as t2,
  ue,
  v
} from "./chunk-REVHHZEO.js";
import {
  c
} from "./chunk-6OFWBRK2.js";
import {
  e,
  t
} from "./chunk-YFVPK4WM.js";
import {
  p
} from "./chunk-63M4K32A.js";

// node_modules/@arcgis/core/arcade/Dictionary.js
function J2(t3, s, i = false) {
  if (null == t3) return null;
  if (Y(t3)) return le(t3);
  if (L(t3)) return fe(t3);
  if (v(t3)) return re(t3);
  if (U(t3)) return ue(t3, s);
  if (J(t3)) {
    const e3 = [];
    for (const n of t3) e3.push(J2(n, s, i));
    return e3;
  }
  const e2 = new T();
  e2.immutable = false;
  for (const n of Object.keys(t3)) {
    const r = t3[n];
    void 0 !== r && e2.setField(n, J2(r, s, i));
  }
  return e2.immutable = i, e2;
}
var T = class _T {
  constructor(t3) {
    this.declaredClass = "esri.arcade.Dictionary", this.attributes = null, this.plain = false, this.immutable = true, this.attributes = t3 instanceof _T ? t3.attributes : t3 ?? {};
  }
  field(t3) {
    const e2 = t3.toLowerCase(), n = this.attributes[t3];
    if (void 0 !== n) return n;
    for (const s in this.attributes) if (s.toLowerCase() === e2) return this.attributes[s];
    throw new t(null, e.FieldNotFound, null, { key: t3 });
  }
  setField(e2, r) {
    if (this.immutable) throw new t(null, e.Immutable, null);
    if (C(r)) throw new t(null, e.NoFunctionInDictionary, null);
    const o = e2.toLowerCase();
    r instanceof Date && (r = c.dateJSToArcadeDate(r));
    if (void 0 === this.attributes[e2]) {
      for (const t3 in this.attributes) if (t3.toLowerCase() === o) return void (this.attributes[t3] = r);
      this.attributes[e2] = r;
    } else this.attributes[e2] = r;
  }
  hasField(t3) {
    const s = t3.toLowerCase();
    if (void 0 !== this.attributes[t3]) return true;
    for (const i in this.attributes) if (i.toLowerCase() === s) return true;
    return false;
  }
  keys() {
    let t3 = [];
    for (const s in this.attributes) t3.push(s);
    return t3 = t3.sort(), t3;
  }
  castToText(s = false) {
    let i = "";
    for (const n in this.attributes) {
      "" !== i && (i += ",");
      const l = this.attributes[n];
      null == l ? i += JSON.stringify(n) + ":null" : L(l) || Y(l) || v(l) ? i += JSON.stringify(n) + ":" + JSON.stringify(l) : l instanceof p ? i += JSON.stringify(n) + ":" + ie(l) : l instanceof t2 || l instanceof Array ? i += JSON.stringify(n) + ":" + ie(l, null, s) : l instanceof c ? i += s ? JSON.stringify(n) + ":" + JSON.stringify(l.getTime()) : JSON.stringify(n) + ":" + l.stringify() : null !== l && "object" == typeof l && void 0 !== l.castToText && (i += JSON.stringify(n) + ":" + l.castToText(s));
    }
    return "{" + i + "}";
  }
  static convertObjectToArcadeDictionary(t3, s, i = true) {
    const e2 = new _T();
    e2.immutable = false;
    for (const n in t3) {
      const i2 = t3[n];
      void 0 !== i2 && e2.setField(n.toString(), J2(i2, s));
    }
    return e2.immutable = i, e2;
  }
  static convertJsonToArcade(t3, s, i = false) {
    return J2(t3, s, i);
  }
  castAsJson(t3 = null) {
    const s = {};
    for (let i in this.attributes) {
      const e2 = this.attributes[i];
      void 0 !== e2 && ((t3 == null ? void 0 : t3.keyTranslate) && (i = t3.keyTranslate(i)), s[i] = Re(e2, t3));
    }
    return s;
  }
  async castDictionaryValueAsJsonAsync(t3, s, i, e2 = null, n) {
    const r = await Fe(i, e2, n);
    return t3[s] = r, r;
  }
  async castAsJsonAsync(s = null, i = null) {
    const e2 = {}, n = [];
    for (let r in this.attributes) {
      const o = this.attributes[r];
      (i == null ? void 0 : i.keyTranslate) && (r = i.keyTranslate(r)), void 0 !== o && (O(o) || o instanceof p || o instanceof c ? e2[r] = Re(o, i) : n.push(this.castDictionaryValueAsJsonAsync(e2, r, o, s, i)));
    }
    return n.length > 0 && await Promise.all(n), e2;
  }
};

export {
  T
};
//# sourceMappingURL=chunk-VX64NK2J.js.map
