{"version": 3, "sources": ["../../@arcgis/core/layers/TileLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import r from\"../request.js\";import t from\"../core/Error.js\";import{HandleOwnerMixin as s}from\"../core/HandleOwner.js\";import{loadAll as i}from\"../core/loadAll.js\";import{isSome as o}from\"../core/maybe.js\";import{MultiOriginJSONMixin as a}from\"../core/MultiOriginJSONSupport.js\";import{throwIfAbortError as l}from\"../core/promiseUtils.js\";import{urlToObject as n,objectToQuery as p,makeAbsolute as c}from\"../core/urlUtils.js\";import{property as u}from\"../core/accessorSupport/decorators/property.js\";import{cast as m}from\"../core/accessorSupport/decorators/cast.js\";import\"../core/arrayUtils.js\";import{reader as h}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as d}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as y}from\"../core/accessorSupport/decorators/writer.js\";import f from\"../geometry/SpatialReference.js\";import v from\"./Layer.js\";import{APIKeyMixin as g}from\"./mixins/APIKeyMixin.js\";import{ArcGISCachedService as S}from\"./mixins/ArcGISCachedService.js\";import{ArcGISMapService as _}from\"./mixins/ArcGISMapService.js\";import{ArcGISService as j}from\"./mixins/ArcGISService.js\";import{BlendLayer as b}from\"./mixins/BlendLayer.js\";import{CustomParametersMixin as T}from\"./mixins/CustomParametersMixin.js\";import{OperationalLayer as w}from\"./mixins/OperationalLayer.js\";import{PortalLayer as O}from\"./mixins/PortalLayer.js\";import{RefreshableLayer as L}from\"./mixins/RefreshableLayer.js\";import{ScaleRangeLayer as R}from\"./mixins/ScaleRangeLayer.js\";import{SublayersOwner as U}from\"./mixins/SublayersOwner.js\";import{parse as P,isHostedAgolService as x,isArcGISUrl as A}from\"./support/arcgisLayerUrl.js\";import{url as I}from\"./support/commonProperties.js\";import{createBitmap as M}from\"./support/imageBitmapUtils.js\";import W from\"./support/Sublayer.js\";const B=[\"Canvas/World_Dark_Gray_Base\",\"Canvas/World_Dark_Gray_Reference\",\"Canvas/World_Light_Gray_Base\",\"Canvas/World_Light_Gray_Reference\",\"Elevation/World_Hillshade\",\"Elevation/World_Hillshade_Dark\",\"Ocean/World_Ocean_Base\",\"Ocean/World_Ocean_Reference\",\"Ocean_Basemap\",\"Reference/World_Boundaries_and_Places\",\"Reference/World_Boundaries_and_Places_Alternate\",\"Reference/World_Transportation\",\"World_Imagery\",\"World_Street_Map\",\"World_Topo_Map\"];let C=class extends(b(U(R(w(O(S(_(j(a(s(L(g(T(v)))))))))))))){constructor(...e){super(...e),this.listMode=\"show\",this.isReference=null,this.operationalLayerType=\"ArcGISTiledMapServiceLayer\",this.resampling=!0,this.sourceJSON=null,this.spatialReference=null,this.path=null,this.sublayers=null,this.type=\"tile\",this.url=null}normalizeCtorArgs(e,r){return\"string\"==typeof e?{url:e,...r}:e}load(e){const r=o(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:[\"Map Service\"]},e).catch(l).then((()=>this._fetchService(r)))),Promise.resolve(this)}get attributionDataUrl(){const e=this.parsedUrl?.path.toLowerCase();return e?this._getDefaultAttribution(this._getMapName(e)):null}readSpatialReference(e,r){return(e=e||r.tileInfo&&r.tileInfo.spatialReference)&&f.fromJSON(e)}writeSublayers(e,r,t,s){if(!this.loaded||!e)return;const i=e.slice().reverse().flatten((({sublayers:e})=>e&&e.toArray().reverse())).toArray(),o=[],a={writeSublayerStructure:!1,...s};i.forEach((e=>{const r=e.write({},a);o.push(r)}));o.some((e=>Object.keys(e).length>1))&&(r.layers=o)}get tileServers(){return this._getDefaultTileServers(this.parsedUrl?.path)}castTileServers(e){return Array.isArray(e)?e.map((e=>n(e).path)):null}fetchTile(e,t,s,i={}){const{signal:o}=i,a=this.getTileUrl(e,t,s),l={responseType:\"image\",signal:o,query:{...this.refreshParameters}};return r(a,l).then((e=>e.data))}async fetchImageBitmapTile(e,t,s,i={}){const{signal:o}=i,a=this.getTileUrl(e,t,s),l={responseType:\"blob\",signal:o,query:{...this.refreshParameters}},{data:n}=await r(a,l);return M(n,a)}getTileUrl(e,r,t){const s=!this.tilemapCache&&this.supportsBlankTile,i=p({...this.parsedUrl?.query,blankTile:!s&&null,...this.customParameters,token:this.apiKey}),o=this.tileServers;return`${o&&o.length?o[r%o.length]:this.parsedUrl?.path}/tile/${e}/${r}/${t}${i?\"?\"+i:\"\"}`}loadAll(){return i(this,(e=>{e(this.allSublayers)}))}_fetchService(e){return new Promise(((s,i)=>{if(this.sourceJSON){if(null!=this.sourceJSON.bandCount&&null!=this.sourceJSON.pixelSizeX)throw new t(\"tile-layer:unsupported-url\",\"use ImageryTileLayer to open a tiled image service\");return void s({data:this.sourceJSON})}if(!this.parsedUrl)throw new t(\"tile-layer:undefined-url\",\"layer's url is not defined\");const a=P(this.parsedUrl.path);if(o(a)&&\"ImageServer\"===a.serverType)throw new t(\"tile-layer:unsupported-url\",\"use ImageryTileLayer to open a tiled image service\");r(this.parsedUrl.path,{query:{f:\"json\",...this.parsedUrl.query,...this.customParameters,token:this.apiKey},responseType:\"json\",signal:e}).then(s,i)})).then((r=>{let t=this.url;if(r.ssl&&(t=this.url=t.replace(/^http:/i,\"https:\")),this.sourceJSON=r.data,this.read(r.data,{origin:\"service\",url:this.parsedUrl}),10.1===this.version&&!x(t))return this._fetchServerVersion(t,e).then((e=>{this.read({currentVersion:e})})).catch((()=>{}))}))}_fetchServerVersion(e,s){if(!A(e))return Promise.reject();const i=e.replace(/(.*\\/rest)\\/.*/i,\"$1\")+\"/info\";return r(i,{query:{f:\"json\",...this.customParameters,token:this.apiKey},responseType:\"json\",signal:s}).then((e=>{if(e.data&&e.data.currentVersion)return e.data.currentVersion;throw new t(\"tile-layer:version-not-available\")}))}_getMapName(e){const r=e.match(/^(?:https?:)?\\/\\/(server\\.arcgisonline\\.com|services\\.arcgisonline\\.com|ibasemaps-api\\.arcgis\\.com)\\/arcgis\\/rest\\/services\\/([^\\/]+(\\/[^\\/]+)*)\\/mapserver/i);return r?r[2]:void 0}_getDefaultAttribution(e){if(null==e)return null;let r;e=e.toLowerCase();for(let t=0,s=B.length;t<s;t++)if(r=B[t],r.toLowerCase().includes(e))return c(\"//static.arcgis.com/attribution/\"+r);return null}_getDefaultTileServers(e){if(null==e)return[];const r=-1!==e.search(/^(?:https?:)?\\/\\/server\\.arcgisonline\\.com/i),t=-1!==e.search(/^(?:https?:)?\\/\\/services\\.arcgisonline\\.com/i);return r||t?[e,e.replace(r?/server\\.arcgisonline/i:/services\\.arcgisonline/i,r?\"services.arcgisonline\":\"server.arcgisonline\")]:[]}get hasOverriddenFetchTile(){return!this.fetchTile.__isDefault__}};e([u({readOnly:!0})],C.prototype,\"attributionDataUrl\",null),e([u({type:[\"show\",\"hide\",\"hide-children\"]})],C.prototype,\"listMode\",void 0),e([u({json:{read:!0,write:!0}})],C.prototype,\"blendMode\",void 0),e([u({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],C.prototype,\"isReference\",void 0),e([u({readOnly:!0,type:[\"ArcGISTiledMapServiceLayer\"]})],C.prototype,\"operationalLayerType\",void 0),e([u({type:Boolean})],C.prototype,\"resampling\",void 0),e([u()],C.prototype,\"sourceJSON\",void 0),e([u({type:f})],C.prototype,\"spatialReference\",void 0),e([h(\"spatialReference\",[\"spatialReference\",\"tileInfo\"])],C.prototype,\"readSpatialReference\",null),e([u({type:String,json:{origins:{\"web-scene\":{read:!0,write:!0}},read:!1}})],C.prototype,\"path\",void 0),e([u({readOnly:!0})],C.prototype,\"sublayers\",void 0),e([y(\"sublayers\",{layers:{type:[W]}})],C.prototype,\"writeSublayers\",null),e([u({json:{read:!1,write:!1}})],C.prototype,\"popupEnabled\",void 0),e([u()],C.prototype,\"tileServers\",null),e([m(\"tileServers\")],C.prototype,\"castTileServers\",null),e([u({readOnly:!0,json:{read:!1}})],C.prototype,\"type\",void 0),e([u(I)],C.prototype,\"url\",void 0),C=e([d(\"esri.layers.TileLayer\")],C),C.prototype.fetchTile.__isDefault__=!0;const D=C;export{D as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIszD,IAAM,IAAE,CAAC,+BAA8B,oCAAmC,gCAA+B,qCAAoC,6BAA4B,kCAAiC,0BAAyB,+BAA8B,iBAAgB,yCAAwC,mDAAkD,kCAAiC,iBAAgB,oBAAmB,gBAAgB;AAAE,IAAI,IAAE,cAAc,EAAE,EAAE,EAAE,EAAE,EAAEA,GAAEC,GAAEC,GAAE,EAAEC,GAAE,EAAE,EAAEC,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,WAAS,QAAO,KAAK,cAAY,MAAK,KAAK,uBAAqB,8BAA6B,KAAK,aAAW,MAAG,KAAK,aAAW,MAAK,KAAK,mBAAiB,MAAK,KAAK,OAAK,MAAK,KAAK,YAAU,MAAK,KAAK,OAAK,QAAO,KAAK,MAAI;AAAA,EAAI;AAAA,EAAC,kBAAkBA,IAAEC,IAAE;AAAC,WAAM,YAAU,OAAOD,KAAE,EAAC,KAAIA,IAAE,GAAGC,GAAC,IAAED;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC,IAAEA,GAAE,SAAO;AAAK,WAAO,KAAK,oBAAoB,KAAK,eAAe,EAAC,gBAAe,CAAC,aAAa,EAAC,GAAEA,EAAC,EAAE,MAAM,CAAC,EAAE,KAAM,MAAI,KAAK,cAAcC,EAAC,CAAE,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAoB;AAJ90F;AAI+0F,UAAMD,MAAE,UAAK,cAAL,mBAAgB,KAAK;AAAc,WAAOA,KAAE,KAAK,uBAAuB,KAAK,YAAYA,EAAC,CAAC,IAAE;AAAA,EAAI;AAAA,EAAC,qBAAqBA,IAAEC,IAAE;AAAC,YAAOD,KAAEA,MAAGC,GAAE,YAAUA,GAAE,SAAS,qBAAmB,EAAE,SAASD,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,IAAEC,IAAEP,IAAE;AAAC,QAAG,CAAC,KAAK,UAAQ,CAACK,GAAE;AAAO,UAAMG,KAAEH,GAAE,MAAM,EAAE,QAAQ,EAAE,QAAS,CAAC,EAAC,WAAUA,GAAC,MAAIA,MAAGA,GAAE,QAAQ,EAAE,QAAQ,CAAE,EAAE,QAAQ,GAAED,KAAE,CAAC,GAAED,KAAE,EAAC,wBAAuB,OAAG,GAAGH,GAAC;AAAE,IAAAQ,GAAE,QAAS,CAAAH,OAAG;AAAC,YAAMC,KAAED,GAAE,MAAM,CAAC,GAAEF,EAAC;AAAE,MAAAC,GAAE,KAAKE,EAAC;AAAA,IAAC,CAAE;AAAE,IAAAF,GAAE,KAAM,CAAAC,OAAG,OAAO,KAAKA,EAAC,EAAE,SAAO,CAAE,MAAIC,GAAE,SAAOF;AAAA,EAAE;AAAA,EAAC,IAAI,cAAa;AAJn0G;AAIo0G,WAAO,KAAK,wBAAuB,UAAK,cAAL,mBAAgB,IAAI;AAAA,EAAC;AAAA,EAAC,gBAAgBC,IAAE;AAAC,WAAO,MAAM,QAAQA,EAAC,IAAEA,GAAE,IAAK,CAAAA,OAAG,EAAEA,EAAC,EAAE,IAAK,IAAE;AAAA,EAAI;AAAA,EAAC,UAAUA,IAAEE,IAAEP,IAAEQ,KAAE,CAAC,GAAE;AAAC,UAAK,EAAC,QAAOJ,GAAC,IAAEI,IAAEL,KAAE,KAAK,WAAWE,IAAEE,IAAEP,EAAC,GAAES,KAAE,EAAC,cAAa,SAAQ,QAAOL,IAAE,OAAM,EAAC,GAAG,KAAK,kBAAiB,EAAC;AAAE,WAAO,EAAED,IAAEM,EAAC,EAAE,KAAM,CAAAJ,OAAGA,GAAE,IAAK;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBA,IAAEE,IAAEP,IAAEQ,KAAE,CAAC,GAAE;AAAC,UAAK,EAAC,QAAOJ,GAAC,IAAEI,IAAEL,KAAE,KAAK,WAAWE,IAAEE,IAAEP,EAAC,GAAES,KAAE,EAAC,cAAa,QAAO,QAAOL,IAAE,OAAM,EAAC,GAAG,KAAK,kBAAiB,EAAC,GAAE,EAAC,MAAKM,GAAC,IAAE,MAAM,EAAEP,IAAEM,EAAC;AAAE,WAAOJ,GAAEK,IAAEP,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWE,IAAEC,IAAEC,IAAE;AAJlzH;AAImzH,UAAMP,KAAE,CAAC,KAAK,gBAAc,KAAK,mBAAkBQ,KAAE,EAAE,EAAC,IAAG,UAAK,cAAL,mBAAgB,OAAM,WAAU,CAACR,MAAG,MAAK,GAAG,KAAK,kBAAiB,OAAM,KAAK,OAAM,CAAC,GAAEI,KAAE,KAAK;AAAY,WAAM,GAAGA,MAAGA,GAAE,SAAOA,GAAEE,KAAEF,GAAE,MAAM,KAAE,UAAK,cAAL,mBAAgB,IAAI,SAASC,EAAC,IAAIC,EAAC,IAAIC,EAAC,GAAGC,KAAE,MAAIA,KAAE,EAAE;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,WAAO,EAAE,MAAM,CAAAH,OAAG;AAAC,MAAAA,GAAE,KAAK,YAAY;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,WAAO,IAAI,QAAS,CAACL,IAAEQ,OAAI;AAAC,UAAG,KAAK,YAAW;AAAC,YAAG,QAAM,KAAK,WAAW,aAAW,QAAM,KAAK,WAAW,WAAW,OAAM,IAAI,EAAE,8BAA6B,oDAAoD;AAAE,eAAO,KAAKR,GAAE,EAAC,MAAK,KAAK,WAAU,CAAC;AAAA,MAAC;AAAC,UAAG,CAAC,KAAK,UAAU,OAAM,IAAI,EAAE,4BAA2B,4BAA4B;AAAE,YAAMG,KAAE,EAAE,KAAK,UAAU,IAAI;AAAE,UAAG,EAAEA,EAAC,KAAG,kBAAgBA,GAAE,WAAW,OAAM,IAAI,EAAE,8BAA6B,oDAAoD;AAAE,QAAE,KAAK,UAAU,MAAK,EAAC,OAAM,EAAC,GAAE,QAAO,GAAG,KAAK,UAAU,OAAM,GAAG,KAAK,kBAAiB,OAAM,KAAK,OAAM,GAAE,cAAa,QAAO,QAAOE,GAAC,CAAC,EAAE,KAAKL,IAAEQ,EAAC;AAAA,IAAC,CAAE,EAAE,KAAM,CAAAF,OAAG;AAAC,UAAIC,KAAE,KAAK;AAAI,UAAGD,GAAE,QAAMC,KAAE,KAAK,MAAIA,GAAE,QAAQ,WAAU,QAAQ,IAAG,KAAK,aAAWD,GAAE,MAAK,KAAK,KAAKA,GAAE,MAAK,EAAC,QAAO,WAAU,KAAI,KAAK,UAAS,CAAC,GAAE,SAAO,KAAK,WAAS,CAAC,EAAEC,EAAC,EAAE,QAAO,KAAK,oBAAoBA,IAAEF,EAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,aAAK,KAAK,EAAC,gBAAeA,GAAC,CAAC;AAAA,MAAC,CAAE,EAAE,MAAO,MAAI;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEL,IAAE;AAAC,QAAG,CAAC,EAAEK,EAAC,EAAE,QAAO,QAAQ,OAAO;AAAE,UAAMG,KAAEH,GAAE,QAAQ,mBAAkB,IAAI,IAAE;AAAQ,WAAO,EAAEG,IAAE,EAAC,OAAM,EAAC,GAAE,QAAO,GAAG,KAAK,kBAAiB,OAAM,KAAK,OAAM,GAAE,cAAa,QAAO,QAAOR,GAAC,CAAC,EAAE,KAAM,CAAAK,OAAG;AAAC,UAAGA,GAAE,QAAMA,GAAE,KAAK,eAAe,QAAOA,GAAE,KAAK;AAAe,YAAM,IAAI,EAAE,kCAAkC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMC,KAAED,GAAE,MAAM,8JAA8J;AAAE,WAAOC,KAAEA,GAAE,CAAC,IAAE;AAAA,EAAM;AAAA,EAAC,uBAAuBD,IAAE;AAAC,QAAG,QAAMA,GAAE,QAAO;AAAK,QAAIC;AAAE,IAAAD,KAAEA,GAAE,YAAY;AAAE,aAAQE,KAAE,GAAEP,KAAE,EAAE,QAAOO,KAAEP,IAAEO,KAAI,KAAGD,KAAE,EAAEC,EAAC,GAAED,GAAE,YAAY,EAAE,SAASD,EAAC,EAAE,QAAO,EAAE,qCAAmCC,EAAC;AAAE,WAAO;AAAA,EAAI;AAAA,EAAC,uBAAuBD,IAAE;AAAC,QAAG,QAAMA,GAAE,QAAM,CAAC;AAAE,UAAMC,KAAE,OAAKD,GAAE,OAAO,6CAA6C,GAAEE,KAAE,OAAKF,GAAE,OAAO,+CAA+C;AAAE,WAAOC,MAAGC,KAAE,CAACF,IAAEA,GAAE,QAAQC,KAAE,0BAAwB,2BAA0BA,KAAE,0BAAwB,qBAAqB,CAAC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,yBAAwB;AAAC,WAAM,CAAC,KAAK,UAAU;AAAA,EAAa;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,QAAO,eAAe,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,OAAG,OAAM,EAAC,SAAQ,MAAG,gBAAe,OAAK,EAAC,SAAQ,MAAE,GAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,CAAC,4BAA4B,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,oBAAmB,CAAC,oBAAmB,UAAU,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,GAAE,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACA,GAAE,aAAY,EAAC,QAAO,EAAC,MAAK,CAAC,CAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAACN,GAAE,aAAa,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAEW,EAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,GAAE,CAAC,GAAE,EAAE,UAAU,UAAU,gBAAc;AAAG,IAAM,IAAE;", "names": ["s", "y", "p", "a", "o", "e", "r", "t", "i", "l", "n", "f"]}