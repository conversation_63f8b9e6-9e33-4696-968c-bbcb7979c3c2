import {
  M,
  ie,
  p
} from "./chunk-VNYCO3JG.js";

// node_modules/@arcgis/core/layers/support/FieldsIndex.js
function s(e) {
  return "date" === e.type || "esriFieldTypeDate" === e.type;
}
function l(e) {
  return "oid" === e.type || "esriFieldTypeOID" === e.type;
}
function d(e) {
  return "global-id" === e.type || "esriFieldTypeGlobalID" === e.type;
}
var r = class {
  constructor(i = []) {
    if (this.fields = [], this._fieldsMap = /* @__PURE__ */ new Map(), this._normalizedFieldsMap = /* @__PURE__ */ new Map(), this._dateFieldsSet = /* @__PURE__ */ new Set(), this._numericFieldsSet = /* @__PURE__ */ new Set(), this.dateFields = [], this.numericFields = [], this._requiredFields = null, !i) return;
    this.fields = i;
    const t = [];
    for (const r2 of i) {
      const i2 = r2 == null ? void 0 : r2.name, u = a(r2 == null ? void 0 : r2.name);
      if (i2 && u) {
        const a2 = n(i2);
        this._fieldsMap.set(i2, r2), this._fieldsMap.set(a2, r2), this._normalizedFieldsMap.set(u, r2), t.push(a2), s(r2) ? (this.dateFields.push(r2), this._dateFieldsSet.add(r2)) : ie(r2) && (this._numericFieldsSet.add(r2), this.numericFields.push(r2)), l(r2) || d(r2) || (r2.editable = null == r2.editable || !!r2.editable, r2.nullable = null == r2.nullable || !!r2.nullable);
      }
    }
    t.sort(), this.uid = t.join(",");
  }
  destroy() {
    this._fieldsMap.clear();
  }
  get requiredFields() {
    if (!this._requiredFields) {
      this._requiredFields = [];
      for (const e of this.fields) l(e) || d(e) || e.nullable || void 0 !== M(e) || this._requiredFields.push(e);
    }
    return this._requiredFields;
  }
  has(e) {
    return null != this.get(e);
  }
  get(e) {
    if (!e) return;
    let i = this._fieldsMap.get(e);
    return i || (i = this._fieldsMap.get(n(e)) ?? this._normalizedFieldsMap.get(a(e)), i && this._fieldsMap.set(e, i), i);
  }
  isDateField(e) {
    return this._dateFieldsSet.has(this.get(e));
  }
  isNumericField(e) {
    return this._numericFieldsSet.has(this.get(e));
  }
  normalizeFieldName(e) {
    const i = this.get(e);
    if (i) return i.name ?? void 0;
  }
};
function n(e) {
  return e.trim().toLowerCase();
}
function a(e) {
  var _a;
  return ((_a = p(e)) == null ? void 0 : _a.toLowerCase()) ?? "";
}

export {
  r
};
//# sourceMappingURL=chunk-UCWK623G.js.map
