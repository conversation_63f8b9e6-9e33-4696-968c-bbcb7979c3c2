import {
  l
} from "./chunk-EGZXDK64.js";
import "./chunk-2B52LX6T.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import {
  e as e4
} from "./chunk-BXAC62KA.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-RURSJOSG.js";
import {
  s as s4
} from "./chunk-RD2ET5MZ.js";
import {
  n as n2,
  z
} from "./chunk-4PRMO7C5.js";
import {
  l as l2
} from "./chunk-WZNPTIYX.js";
import "./chunk-VCFT2OOI.js";
import {
  e as e3
} from "./chunk-4CHRJPQP.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-O2BYTJI4.js";
import {
  j as j3,
  p as p3
} from "./chunk-CRV24QIM.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-P2G4OGHI.js";
import {
  i
} from "./chunk-JI2EKKOE.js";
import {
  p as p4
} from "./chunk-7FKSPW4T.js";
import {
  o as o2
} from "./chunk-YAIU7YWS.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-HTXGAKOK.js";
import {
  _
} from "./chunk-FDBF4TJR.js";
import "./chunk-YUDXR4IE.js";
import {
  c as c2
} from "./chunk-WTHPVARW.js";
import {
  O
} from "./chunk-B3Q27ZSC.js";
import "./chunk-C3LWQPIC.js";
import {
  j as j2
} from "./chunk-VYC4DNQO.js";
import "./chunk-LVWRJMBJ.js";
import "./chunk-JSANYNBO.js";
import "./chunk-TPRZH2SY.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-P6BL3OFI.js";
import "./chunk-FZ7BG3VX.js";
import {
  n
} from "./chunk-5IKCVZDA.js";
import {
  t as t2
} from "./chunk-AHLG6PXW.js";
import "./chunk-72RC7KC7.js";
import "./chunk-PGSBPPQ2.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-V6NQCXYQ.js";
import {
  b
} from "./chunk-S2ZSC2TN.js";
import "./chunk-XAKEPYSQ.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-HRASNGES.js";
import "./chunk-PBQFTVHM.js";
import "./chunk-ZJKAJ76S.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import "./chunk-SJRT3EVN.js";
import "./chunk-2CFIAWMM.js";
import "./chunk-MURG32WB.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import {
  y as y2
} from "./chunk-XVA5SA7P.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  U
} from "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import {
  A,
  Ct,
  Et,
  K,
  L,
  V,
  X,
  Y,
  a as a2,
  ht,
  jt,
  q
} from "./chunk-U4SVMKOQ.js";
import {
  w as w3
} from "./chunk-Q7LVCH5L.js";
import {
  w as w2
} from "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import {
  o
} from "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import {
  f2
} from "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import {
  r as r2
} from "./chunk-IJ6FZE6K.js";
import "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e as e2
} from "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2 as a,
  y
} from "./chunk-R4CPW7J5.js";
import "./chunk-2CM7MIII.js";
import {
  r
} from "./chunk-HP475EI3.js";
import {
  f,
  j,
  p as p2,
  v,
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s3
} from "./chunk-4RZONHOY.js";
import {
  s as s2
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  c2 as c,
  t2 as t
} from "./chunk-GZGAQUSK.js";
import {
  e
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/imageUtils.js
var A2 = null;
function o3(o5) {
  if (A2) return A2;
  const l5 = { lossy: "UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA", lossless: "UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==", alpha: "UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==", animation: "UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA" };
  return A2 = new Promise((A3) => {
    const n5 = new Image();
    n5.onload = () => {
      n5.onload = n5.onerror = null, A3(n5.width > 0 && n5.height > 0);
    }, n5.onerror = () => {
      n5.onload = n5.onerror = null, A3(false);
    }, n5.src = "data:image/webp;base64," + l5[o5];
  }), A2;
}

// node_modules/@arcgis/core/layers/support/SpriteSource.js
var h = 1.15;
var n3 = class {
  constructor(t3, e5) {
    this._spriteSource = t3, this._maxTextureSize = e5, this.devicePixelRatio = 1, this._spriteImageFormat = "png", this._isRetina = false, this._spritesData = {}, this.image = null, this.width = null, this.height = null, this.loadStatus = "not-loaded", "url" === t3.type && t3.spriteFormat && (this._spriteImageFormat = t3.spriteFormat), t3.pixelRatio && (this.devicePixelRatio = t3.pixelRatio), this.baseURL = t3.spriteUrl;
  }
  get spriteNames() {
    const t3 = [];
    for (const e5 in this._spritesData) t3.push(e5);
    return t3.sort(), t3;
  }
  getSpriteInfo(t3) {
    return this._spritesData ? this._spritesData[t3] : null;
  }
  async load(t3) {
    if (this.baseURL) {
      this.loadStatus = "loading";
      try {
        await this._loadSprites(t3), this.loadStatus = "loaded";
      } catch {
        this.loadStatus = "failed";
      }
    } else this.loadStatus = "failed";
  }
  async _loadSprites(t3) {
    this._isRetina = this.devicePixelRatio > h;
    const { width: s6, height: r5, data: a4, json: o5 } = await this._getSpriteData(this._spriteSource, t3), n5 = Object.keys(o5);
    if (!n5 || 0 === n5.length || !a4) return this._spritesData = this.image = null, void (this.width = this.height = 0);
    this._spritesData = o5, this.width = s6, this.height = r5;
    const d3 = Math.max(this._maxTextureSize, 4096);
    if (s6 > d3 || r5 > d3) {
      const t4 = `Sprite resource for style ${this.baseURL} is bigger than the maximum allowed of ${d3} pixels}`;
      throw s2.getLogger("esri.layers.support.SpriteSource").error(t4), new s3("SpriteSource", t4);
    }
    let p5;
    for (let e5 = 0; e5 < a4.length; e5 += 4) p5 = a4[e5 + 3] / 255, a4[e5] = a4[e5] * p5, a4[e5 + 1] = a4[e5 + 1] * p5, a4[e5 + 2] = a4[e5 + 2] * p5;
    this.image = a4;
  }
  async _getSpriteData(i3, n5) {
    if ("image" === i3.type) {
      let t3, a4;
      if (this.devicePixelRatio < h) {
        if (!i3.spriteSource1x) throw new s3("SpriteSource", "no image data provided for low resolution sprites!");
        t3 = i3.spriteSource1x.image, a4 = i3.spriteSource1x.json;
      } else {
        if (!i3.spriteSource2x) throw new s3("SpriteSource", "no image data provided for high resolution sprites!");
        t3 = i3.spriteSource2x.image, a4 = i3.spriteSource2x.json;
      }
      return "width" in t3 && "height" in t3 && "data" in t3 && (t(t3.data) || c(t3.data)) ? { width: t3.width, height: t3.height, data: new Uint8Array(t3.data), json: a4 } : { ...d(t3), json: a4 };
    }
    const p5 = L(this.baseURL), l5 = p5.query ? "?" + A(p5.query) : "", g2 = this._isRetina ? "@2x" : "", u = `${p5.path}${g2}.${this._spriteImageFormat}${l5}`, c3 = `${p5.path}${g2}.json${l5}`, [m2, S2] = await Promise.all([U(c3, n5), U(u, { responseType: "image", ...n5 })]);
    return { ...d(S2.data), json: m2.data };
  }
};
function d(t3) {
  const e5 = document.createElement("canvas"), i3 = e5.getContext("2d");
  e5.width = t3.width, e5.height = t3.height, i3.drawImage(t3, 0, 0, t3.width, t3.height);
  const s6 = i3.getImageData(0, 0, t3.width, t3.height);
  return { width: t3.width, height: t3.height, data: new Uint8Array(s6.data) };
}

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/TilemapIndex.js
var l3 = class {
  constructor(e5) {
    this.url = e5;
  }
  async fetchTileIndex() {
    return this._tileIndexPromise || (this._tileIndexPromise = U(this.url).then((e5) => e5.data.index)), this._tileIndexPromise;
  }
  async dataKey(e5, r5) {
    const l5 = await this.fetchTileIndex();
    return f(r5), this._getIndexedDataKey(l5, e5);
  }
  _getIndexedDataKey(e5, t3) {
    const l5 = [t3];
    if (t3.level < 0 || t3.row < 0 || t3.col < 0 || t3.row >> t3.level > 0 || t3.col >> t3.level > 0) return null;
    let i3 = t3;
    for (; 0 !== i3.level; ) i3 = new e3(i3.level - 1, i3.row >> 1, i3.col >> 1, i3.world), l5.push(i3);
    let o5, s6, n5 = e5, a4 = l5.pop();
    if (1 === n5) return a4;
    for (; l5.length; ) if (o5 = l5.pop(), s6 = (1 & o5.col) + ((1 & o5.row) << 1), n5) {
      if (0 === n5[s6]) {
        a4 = null;
        break;
      }
      if (1 === n5[s6]) {
        a4 = o5;
        break;
      }
      a4 = o5, n5 = n5[s6];
    }
    return a4;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/TilemapRequest.js
var r3 = class {
  constructor(e5, t3) {
    this._tilemap = e5, this._tileIndexUrl = t3;
  }
  async fetchTileIndex(t3) {
    return this._tileIndexPromise || (this._tileIndexPromise = U(this._tileIndexUrl, { query: { ...t3 == null ? void 0 : t3.query } }).then((e5) => e5.data.index)), this._tileIndexPromise;
  }
  dataKey(e5, r5) {
    const { level: l5, row: s6, col: o5 } = e5, n5 = new e3(e5);
    return this._tilemap.fetchAvailabilityUpsample(l5, s6, o5, n5, r5).then(() => (n5.world = e5.world, n5)).catch((e6) => {
      if (j(e6)) throw e6;
      return null;
    });
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/style/VectorTilesRequestManager.js
var i2 = class {
  constructor(t3) {
    this._tileUrl = t3, this._promise = null, this._abortController = null, this._abortOptions = [];
  }
  getData(t3) {
    null === this._promise && (this._abortController = new AbortController(), this._promise = this._makeRequest(this._tileUrl, this._abortController.signal));
    const e5 = this._abortOptions;
    return e5.push(t3), v(t3, () => {
      e5.every((t4) => p2(t4)) && this._abortController.abort();
    }), this._promise.then((t4) => p(t4));
  }
  async _makeRequest(r5, e5) {
    const { data: o5 } = await U(r5, { responseType: "array-buffer", signal: e5 });
    return o5;
  }
};
var n4 = /* @__PURE__ */ new Map();
function l4(t3, r5, e5, o5, s6) {
  return a3(t3.replace(/\{z\}/gi, r5.toString()).replace(/\{y\}/gi, e5.toString()).replace(/\{x\}/gi, o5.toString()), s6);
}
function a3(t3, r5) {
  return r(n4, t3, () => new i2(t3)).getData(r5).then((r6) => (n4.delete(t3), r6)).catch((r6) => {
    throw n4.delete(t3), r6;
  });
}

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/style/VectorTileSource.js
var h2 = class {
  constructor(u, h4, m2) {
    this.tilemap = null, this.tileInfo = null, this.capabilities = null, this.fullExtent = null, this.name = u, this.sourceUrl = h4;
    const f3 = L(this.sourceUrl), c3 = p(m2), x2 = c3.tiles;
    if (f3) for (let t3 = 0; t3 < x2.length; t3++) {
      const s6 = L(x2[t3]);
      s6 && (Y(s6.path) || (s6.path = V(f3.path, s6.path)), x2[t3] = Et(s6.path, { ...f3.query, ...s6.query }));
    }
    this.tileServers = x2;
    const d3 = m2.capabilities && m2.capabilities.split(",").map((t3) => t3.toLowerCase().trim()), y4 = true === (m2 == null ? void 0 : m2.exportTilesAllowed), T = true === (d3 == null ? void 0 : d3.includes("tilemap")), g2 = y4 && m2.hasOwnProperty("maxExportTilesCount") ? m2.maxExportTilesCount : 0;
    this.capabilities = { operations: { supportsExportTiles: y4, supportsTileMap: T }, exportTiles: y4 ? { maxExportTilesCount: +g2 } : null }, this.tileInfo = n2(c3.tileInfo, c3, null, { ignoreMinMaxLOD: true });
    const M = m2.tileMap ? Et(V(f3.path, m2.tileMap), f3.query ?? {}) : null;
    T ? (this.type = "vector-tile", this.tilemap = new r3(new z({ layer: { parsedUrl: f3, tileInfo: this.tileInfo, type: "vector-tile", tileServers: this.tileServers } }), M)) : M && (this.tilemap = new l3(M)), this.fullExtent = w3.fromJSON(m2.fullExtent);
  }
  destroy() {
  }
  async getRefKey(t3, e5) {
    var _a;
    return await ((_a = this.tilemap) == null ? void 0 : _a.dataKey(t3, e5)) ?? t3;
  }
  requestTile(t3, e5, i3, r5) {
    const l5 = this.tileServers[e5 % this.tileServers.length];
    return l4(l5, t3, e5, i3, r5);
  }
  isCompatibleWith(t3) {
    const e5 = this.tileInfo, i3 = t3.tileInfo;
    if (!e5.spatialReference.equals(i3.spatialReference)) return false;
    if (!e5.origin.equals(i3.origin)) return false;
    if (Math.round(e5.dpi) !== Math.round(i3.dpi)) return false;
    const r5 = e5.lods, l5 = i3.lods, s6 = Math.min(r5.length, l5.length);
    for (let o5 = 0; o5 < s6; o5++) {
      const t4 = r5[o5], e6 = l5[o5];
      if (t4.level !== e6.level || Math.round(t4.scale) !== Math.round(e6.scale)) return false;
    }
    return true;
  }
};

// node_modules/@arcgis/core/layers/support/vectorTileLayerLoader.js
var y3 = s.defaults && s.defaults.io.corsEnabledServers;
async function m(e5, r5) {
  const t3 = { source: null, sourceBase: null, sourceUrl: null, validatedSource: null, style: null, styleBase: null, styleUrl: null, sourceNameToSource: {}, primarySourceName: "", spriteFormat: "png" }, [s6, o5] = "string" == typeof e5 ? [e5, null] : [null, e5.jsonUrl];
  await h3(t3, "esri", e5, o5, r5);
  const l5 = { layerDefinition: t3.validatedSource, url: s6, serviceUrl: t3.sourceUrl, style: t3.style, styleUrl: t3.styleUrl, spriteUrl: t3.style.sprite && S(t3.styleBase, t3.style.sprite), spriteFormat: t3.spriteFormat, glyphsUrl: t3.style.glyphs && S(t3.styleBase, t3.style.glyphs), sourceNameToSource: t3.sourceNameToSource, primarySourceName: t3.primarySourceName };
  return d2(l5.spriteUrl), d2(l5.glyphsUrl), l5;
}
function d2(e5) {
  if (!e5) return;
  const r5 = X(e5);
  y3 && !y3.includes(r5) && y3.push(r5);
}
function S(...e5) {
  let r5;
  for (const t3 of e5) if (null != t3) if (ht(t3)) {
    if (r5) {
      const e6 = r5.split("://")[0];
      r5 = e6 + ":" + t3.trim();
    }
  } else r5 = Y(t3) ? t3 : V(r5, t3);
  return r5 ? jt(r5) : void 0;
}
async function h3(e5, s6, o5, l5, n5) {
  let i3, c3, a4;
  if (f(n5), "string" == typeof o5) {
    const e6 = K(o5);
    d2(e6), a4 = await U(e6, { ...n5, responseType: "json", query: { f: "json", ...n5 == null ? void 0 : n5.query } }), a4.ssl && (i3 && (i3 = i3.replace(/^http:/i, "https:")), c3 && (c3 = c3.replace(/^http:/i, "https:"))), i3 = e6, c3 = e6;
  } else null != o5 && (a4 = { data: o5 }, i3 = o5.jsonUrl || null, c3 = l5);
  const f3 = a4 == null ? void 0 : a4.data;
  if (U2(f3)) return e5.styleUrl = i3 || null, x(e5, f3, c3, n5);
  if (w4(f3)) return e5.sourceUrl ? v2(e5, f3, c3, false, s6, n5) : (e5.sourceUrl = i3 || null, v2(e5, f3, c3, true, s6, n5));
  throw new Error("You must specify the URL or the JSON for a service or for a style.");
}
function U2(e5) {
  return !!(e5 == null ? void 0 : e5.sources);
}
function w4(e5) {
  return !U2(e5);
}
async function x(e5, r5, t3, s6) {
  const o5 = t3 ? Ct(t3) : q();
  e5.styleBase = o5, e5.style = r5, e5.styleUrl && d2(e5.styleUrl), r5["sprite-format"] && "webp" === r5["sprite-format"].toLowerCase() && (e5.spriteFormat = "webp");
  const l5 = [];
  if (r5.sources && r5.sources.esri) {
    const t4 = r5.sources.esri;
    t4.url ? await h3(e5, "esri", S(o5, t4.url), void 0, s6) : l5.push(h3(e5, "esri", t4, o5, s6));
  }
  for (const n5 of Object.keys(r5.sources)) "esri" !== n5 && "vector" === r5.sources[n5].type && (r5.sources[n5].url ? l5.push(h3(e5, n5, S(o5, r5.sources[n5].url), void 0, s6)) : r5.sources[n5].tiles && l5.push(h3(e5, n5, r5.sources[n5], o5, s6)));
  await Promise.all(l5);
}
async function v2(e5, r5, t3, s6, o5, l5) {
  const n5 = t3 ? jt(t3) + "/" : q(), u = g(r5, n5), c3 = new h2(o5, Et(n5, (l5 == null ? void 0 : l5.query) ?? {}), u);
  if (!s6 && e5.primarySourceName in e5.sourceNameToSource) {
    const r6 = e5.sourceNameToSource[e5.primarySourceName];
    if (!r6.isCompatibleWith(c3)) return;
    null != c3.fullExtent && (null != r6.fullExtent ? r6.fullExtent.union(c3.fullExtent) : r6.fullExtent = c3.fullExtent.clone()), r6.tileInfo && c3.tileInfo && r6.tileInfo.lods.length < c3.tileInfo.lods.length && (r6.tileInfo = c3.tileInfo);
  }
  if (s6 ? (e5.sourceBase = n5, e5.source = r5, e5.validatedSource = u, e5.primarySourceName = o5, e5.sourceUrl && d2(e5.sourceUrl)) : d2(n5), e5.sourceNameToSource[o5] = c3, !e5.style) {
    if (null == r5.defaultStyles) throw new Error();
    return "string" == typeof r5.defaultStyles ? h3(e5, "", S(n5, r5.defaultStyles, "root.json"), void 0, l5) : h3(e5, "", r5.defaultStyles, S(n5, "root.json"), l5);
  }
}
function g(e5, r5) {
  if (e5.hasOwnProperty("tileInfo")) return e5;
  const t3 = { xmin: -20037507067161843e-9, ymin: -20037507067161843e-9, xmax: 20037507067161843e-9, ymax: 20037507067161843e-9, spatialReference: { wkid: 102100 } }, s6 = 512;
  let o5 = 78271.51696400007, l5 = 2958287637957775e-7;
  const n5 = [], i3 = e5.hasOwnProperty("minzoom") ? +e5.minzoom : 0, u = e5.hasOwnProperty("maxzoom") ? +e5.maxzoom : 22;
  for (let c3 = 0; c3 <= u; c3++) c3 >= i3 && n5.push({ level: c3, scale: l5, resolution: o5 }), o5 /= 2, l5 /= 2;
  for (const c3 of e5.tiles ?? []) d2(S(r5, c3));
  return { capabilities: "TilesOnly", initialExtent: t3, fullExtent: t3, minScale: 0, maxScale: 0, tiles: e5.tiles, tileInfo: { rows: s6, cols: s6, dpi: 96, format: "pbf", origin: { x: -20037508342787e-6, y: 20037508342787e-6 }, lods: n5, spatialReference: { wkid: 102100 } } };
}

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/tileInfoUtils.js
var s5 = 1e-6;
function r4(l5, e5) {
  if (l5 === e5) return true;
  if (null == l5 && null != e5) return false;
  if (null != l5 && null == e5) return false;
  if (null == l5 || null == e5) return false;
  if (!l5.spatialReference.equals(e5.spatialReference) || l5.dpi !== e5.dpi) return false;
  const n5 = l5.origin, r5 = e5.origin;
  if (Math.abs(n5.x - r5.x) >= s5 || Math.abs(n5.y - r5.y) >= s5) return false;
  let o5, t3;
  l5.lods[0].scale > e5.lods[0].scale ? (o5 = l5, t3 = e5) : (t3 = l5, o5 = e5);
  for (let i3 = o5.lods[0].scale; i3 >= t3.lods[t3.lods.length - 1].scale - s5; i3 /= 2) if (Math.abs(i3 - t3.lods[0].scale) < s5) return true;
  return false;
}
function o4(s6, r5) {
  if (s6 === r5) return s6;
  if (null == s6 && null != r5) return r5;
  if (null != s6 && null == r5) return s6;
  if (null == s6 || null == r5) return null;
  const o5 = s6.size[0], t3 = s6.format, i3 = s6.dpi, u = new w2({ x: s6.origin.x, y: s6.origin.y }), a4 = s6.spatialReference, f3 = s6.lods[0].scale > r5.lods[0].scale ? s6.lods[0] : r5.lods[0], d3 = s6.lods[s6.lods.length - 1].scale <= r5.lods[r5.lods.length - 1].scale ? s6.lods[s6.lods.length - 1] : r5.lods[r5.lods.length - 1], c3 = f3.scale, p5 = f3.resolution, g2 = d3.scale, h4 = [];
  let m2 = c3, y4 = p5, x2 = 0;
  for (; m2 > g2; ) h4.push(new p3({ level: x2, resolution: y4, scale: m2 })), x2++, m2 /= 2, y4 /= 2;
  return new j3({ size: [o5, o5], dpi: i3, format: t3 || "pbf", origin: u, lods: h4, spatialReference: a4 });
}

// node_modules/@arcgis/core/layers/VectorTileLayer.js
var B = class extends n(t2(s4(p4(c2(_(o2(i(O(b))))))))) {
  constructor(...e5) {
    super(...e5), this._spriteSourceMap = /* @__PURE__ */ new Map(), this.currentStyleInfo = null, this.style = null, this.isReference = null, this.operationalLayerType = "VectorTileLayer", this.type = "vector-tile", this.url = null, this.showCollisionBoxes = "none", this.path = null;
  }
  normalizeCtorArgs(e5, t3) {
    return "string" == typeof e5 ? { url: e5, ...t3 } : e5;
  }
  destroy() {
    if (this.sourceNameToSource) for (const e5 of Object.values(this.sourceNameToSource)) e5 == null ? void 0 : e5.destroy();
    this._spriteSourceMap.clear();
  }
  async prefetchResources(e5) {
    await this.loadSpriteSource(globalThis.devicePixelRatio || 1, e5);
  }
  load(e5) {
    const r5 = this.loadFromPortal({ supportedTypes: ["Vector Tile Service"], supportsData: false }, e5).catch(w).then(async () => {
      if (!this.portalItem || !this.portalItem.id) return;
      const r6 = `${this.portalItem.itemUrl}/resources/styles/root.json`;
      (await U(r6, { ...e5, query: { f: "json", ...this.customParameters, token: this.apiKey } })).data && this.read({ url: r6 }, e4(this.portalItem));
    }).catch(w).then(() => this._loadStyle(e5));
    return this.addResolvingPromise(r5), Promise.resolve(this);
  }
  get attributionDataUrl() {
    const e5 = this.currentStyleInfo, t3 = e5 && e5.serviceUrl && L(e5.serviceUrl);
    if (!t3) return null;
    const r5 = this._getDefaultAttribution(t3.path);
    return r5 ? Et(r5, { ...this.customParameters, token: this.apiKey }) : null;
  }
  get capabilities() {
    const e5 = this.primarySource;
    return e5 ? e5.capabilities : { operations: { supportsExportTiles: false, supportsTileMap: false }, exportTiles: null };
  }
  get fullExtent() {
    var _a;
    return ((_a = this.primarySource) == null ? void 0 : _a.fullExtent) || null;
  }
  get parsedUrl() {
    return this.serviceUrl ? L(this.serviceUrl) : null;
  }
  get serviceUrl() {
    return this.currentStyleInfo && this.currentStyleInfo.serviceUrl || null;
  }
  get spatialReference() {
    var _a;
    return ((_a = this.tileInfo) == null ? void 0 : _a.spatialReference) ?? null;
  }
  get styleUrl() {
    return this.currentStyleInfo && this.currentStyleInfo.styleUrl || null;
  }
  writeStyleUrl(e5, t3) {
    e5 && ht(e5) && (e5 = `https:${e5}`);
    const r5 = e(a2(e5));
    t3.styleUrl = y2(e5, r5);
  }
  get tileInfo() {
    var _a;
    const e5 = [];
    for (const r5 in this.sourceNameToSource) e5.push(this.sourceNameToSource[r5]);
    let t3 = ((_a = this.primarySource) == null ? void 0 : _a.tileInfo) || new j3();
    if (e5.length > 1) for (let r5 = 0; r5 < e5.length; r5++) r4(t3, e5[r5].tileInfo) && (t3 = o4(t3, e5[r5].tileInfo));
    return t3;
  }
  readVersion(e5, t3) {
    return t3.version ? parseFloat(t3.version) : parseFloat(t3.currentVersion);
  }
  async loadSpriteSource(e5 = 1, t3) {
    var _a, _b;
    if (!this._spriteSourceMap.has(e5)) {
      const r5 = l2("2d").maxTextureSize, o5 = ((_a = this.currentStyleInfo) == null ? void 0 : _a.spriteUrl) ? Et(this.currentStyleInfo.spriteUrl, { ...this.customParameters, token: this.apiKey }) : null, s6 = new n3({ type: "url", spriteUrl: o5, pixelRatio: e5, spriteFormat: (_b = this.currentStyleInfo) == null ? void 0 : _b.spriteFormat }, r5);
      await s6.load(t3), this._spriteSourceMap.set(e5, s6);
    }
    return this._spriteSourceMap.get(e5);
  }
  async setSpriteSource(e5, t3) {
    if (!e5) return null;
    const r5 = l2("2d").maxTextureSize, o5 = e5.spriteUrl, s6 = o5 ? Et(o5, { ...this.customParameters, token: this.apiKey }) : null;
    if (!s6 && "url" === e5.type) return null;
    const i3 = new n3(e5, r5);
    try {
      await i3.load(t3);
      const r6 = e5.pixelRatio || 1;
      return this._spriteSourceMap.clear(), this._spriteSourceMap.set(r6, i3), s6 && this.currentStyleInfo && (this.currentStyleInfo.spriteUrl = s6), this.emit("spriteSource-change", { spriteSource: i3 }), i3;
    } catch (l5) {
      w(l5);
    }
    return null;
  }
  async loadStyle(e5, t3) {
    var _a;
    const o5 = e5 || this.style || this.url;
    return this._loadingTask && "string" == typeof o5 && this.url === o5 || ((_a = this._loadingTask) == null ? void 0 : _a.abort(), this._loadingTask = j2((e6) => (this._spriteSourceMap.clear(), this._getSourceAndStyle(o5, { signal: e6 })), t3)), this._loadingTask.promise;
  }
  getStyleLayerId(e5) {
    return this.styleRepository.getStyleLayerId(e5);
  }
  getStyleLayerIndex(e5) {
    return this.styleRepository.getStyleLayerIndex(e5);
  }
  getPaintProperties(e5) {
    return p(this.styleRepository.getPaintProperties(e5));
  }
  setPaintProperties(e5, t3) {
    const r5 = this.styleRepository.isPainterDataDriven(e5);
    this.styleRepository.setPaintProperties(e5, t3);
    const o5 = this.styleRepository.isPainterDataDriven(e5);
    this.emit("paint-change", { layer: e5, paint: t3, isDataDriven: r5 || o5 });
  }
  getStyleLayer(e5) {
    return p(this.styleRepository.getStyleLayer(e5));
  }
  setStyleLayer(e5, t3) {
    this.styleRepository.setStyleLayer(e5, t3), this.emit("style-layer-change", { layer: e5, index: t3 });
  }
  deleteStyleLayer(e5) {
    this.styleRepository.deleteStyleLayer(e5), this.emit("delete-style-layer", { layer: e5 });
  }
  getLayoutProperties(e5) {
    return p(this.styleRepository.getLayoutProperties(e5));
  }
  setLayoutProperties(e5, t3) {
    this.styleRepository.setLayoutProperties(e5, t3), this.emit("layout-change", { layer: e5, layout: t3 });
  }
  setStyleLayerVisibility(e5, t3) {
    this.styleRepository.setStyleLayerVisibility(e5, t3), this.emit("style-layer-visibility-change", { layer: e5, visibility: t3 });
  }
  getStyleLayerVisibility(e5) {
    return this.styleRepository.getStyleLayerVisibility(e5);
  }
  write(e5, t3) {
    return (t3 == null ? void 0 : t3.origin) && !this.styleUrl ? (t3.messages && t3.messages.push(new s3("vectortilelayer:unsupported", `VectorTileLayer (${this.title}, ${this.id}) with style defined by JSON only are not supported`, { layer: this })), null) : super.write(e5, t3);
  }
  getTileUrl(e5, t3, r5) {
    return null;
  }
  async _getSourceAndStyle(e5, t3) {
    if (!e5) throw new Error("invalid style!");
    const r5 = await m(e5, { ...t3, query: { ...this.customParameters, token: this.apiKey } });
    if ("webp" === r5.spriteFormat) {
      await o3("lossy") || (r5.spriteFormat = "png");
    }
    this._set("currentStyleInfo", { ...r5 }), "string" == typeof e5 ? (this.url = e5, this.style = null) : (this.url = null, this.style = e5), this._set("sourceNameToSource", r5.sourceNameToSource), this._set("primarySource", r5.sourceNameToSource[r5.primarySourceName]), this._set("styleRepository", new l(r5.style)), this.read(r5.layerDefinition, { origin: "service" }), this.emit("load-style");
  }
  _getDefaultAttribution(e5) {
    const t3 = e5.match(/^https?:\/\/(?:basemaps|basemapsbeta|basemapsdev)(?:-api)?\.arcgis\.com(\/[^\/]+)?\/arcgis\/rest\/services\/([^\/]+(\/[^\/]+)*)\/vectortileserver/i), r5 = ["OpenStreetMap_v2", "OpenStreetMap_Daylight_v2", "OpenStreetMap_Export_v2", "OpenStreetMap_FTS_v2", "OpenStreetMap_GCS_v2", "World_Basemap", "World_Basemap_v2", "World_Basemap_Export_v2", "World_Basemap_GCS_v2", "World_Basemap_WGS84", "World_Contours_v2"];
    if (!t3) return;
    const o5 = t3[2] && t3[2].toLowerCase();
    if (!o5) return;
    const s6 = t3[1] || "";
    for (const i3 of r5) if (i3.toLowerCase().includes(o5)) return K(`//static.arcgis.com/attribution/Vector${s6}/${i3}`);
  }
  async _loadStyle(e5) {
    var _a;
    return ((_a = this._loadingTask) == null ? void 0 : _a.promise) ?? this.loadStyle(null, e5);
  }
};
e2([y({ readOnly: true })], B.prototype, "attributionDataUrl", null), e2([y({ type: ["show", "hide"] })], B.prototype, "listMode", void 0), e2([y({ json: { read: true, write: true } })], B.prototype, "blendMode", void 0), e2([y({ readOnly: true, json: { read: false } })], B.prototype, "capabilities", null), e2([y({ readOnly: true })], B.prototype, "currentStyleInfo", void 0), e2([y({ json: { read: false }, readOnly: true, type: w3 })], B.prototype, "fullExtent", null), e2([y()], B.prototype, "style", void 0), e2([y({ type: Boolean, json: { read: false, write: { enabled: true, overridePolicy: () => ({ enabled: false }) } } })], B.prototype, "isReference", void 0), e2([y({ type: ["VectorTileLayer"] })], B.prototype, "operationalLayerType", void 0), e2([y({ readOnly: true })], B.prototype, "parsedUrl", null), e2([y({ readOnly: true })], B.prototype, "serviceUrl", null), e2([y({ type: f2, readOnly: true })], B.prototype, "spatialReference", null), e2([y({ readOnly: true })], B.prototype, "styleRepository", void 0), e2([y({ readOnly: true })], B.prototype, "sourceNameToSource", void 0), e2([y({ readOnly: true })], B.prototype, "primarySource", void 0), e2([y({ type: String, readOnly: true, json: { write: { ignoreOrigin: true }, origins: { "web-document": { write: { ignoreOrigin: true, isRequired: true } } } } })], B.prototype, "styleUrl", null), e2([r2(["portal-item", "web-document"], "styleUrl")], B.prototype, "writeStyleUrl", null), e2([y({ json: { read: false, origins: { service: { read: false } } }, readOnly: true, type: j3 })], B.prototype, "tileInfo", null), e2([y({ json: { read: false }, readOnly: true, value: "vector-tile" })], B.prototype, "type", void 0), e2([y({ json: { origins: { "web-document": { read: { source: "styleUrl" } }, "portal-item": { read: { source: "url" } } }, write: false, read: false } })], B.prototype, "url", void 0), e2([y({ readOnly: true })], B.prototype, "version", void 0), e2([o("version", ["version", "currentVersion"])], B.prototype, "readVersion", null), e2([y({ type: String })], B.prototype, "showCollisionBoxes", void 0), e2([y({ type: String, json: { origins: { "web-scene": { read: true, write: true } }, read: false } })], B.prototype, "path", void 0), B = e2([a("esri.layers.VectorTileLayer")], B);
var E = B;
export {
  E as default
};
//# sourceMappingURL=VectorTileLayer-PNSDG26D.js.map
