{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/PathVertexPosition.glsl.js", "../../@arcgis/core/chunks/Path.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{f as e}from\"../../../../../../chunks/vec2f64.js\";import{PositionAttribute as o}from\"./PositionAttribute.glsl.js\";import{Float2PassUniform as i}from\"../../shaderModules/Float2PassUniform.js\";import{Float3PassUniform as r}from\"../../shaderModules/Float3PassUniform.js\";import{Float4sPassUniform as t}from\"../../shaderModules/Float4sPassUniform.js\";import{FloatsPassUniform as a}from\"../../shaderModules/FloatsPassUniform.js\";import{glsl as l}from\"../../shaderModules/interfaces.js\";import{VertexAttribute as v}from\"../../../lib/VertexAttribute.js\";import{vvColorNumber as s,VisualVariablePassParameters as c}from\"../../../materials/VisualVariablePassParameters.js\";const f=8;function n(e,c){const n=v.FEATUREVALUE;e.attributes.add(n,\"vec4\");const p=e.vertex;p.code.add(l`\n  bool isCapVertex() {\n    return ${n}.w == 1.0;\n  }\n  `),p.uniforms.add(new i(\"size\",(e=>e.size))),c.vvSize?(p.uniforms.add(new r(\"vvSizeMinSize\",(e=>e.vvSizeMinSize))),p.uniforms.add(new r(\"vvSizeMaxSize\",(e=>e.vvSizeMaxSize))),p.uniforms.add(new r(\"vvSizeOffset\",(e=>e.vvSizeOffset))),p.uniforms.add(new r(\"vvSizeFactor\",(e=>e.vvSizeFactor))),p.code.add(l`\n    vec2 getSize() {\n      return size * clamp(vvSizeOffset + ${n}.x * vvSizeFactor, vvSizeMinSize, vvSizeMaxSize).xz;\n    }\n    `)):p.code.add(l`vec2 getSize(){\nreturn size;\n}`),c.vvOpacity?(p.constants.add(\"vvOpacityNumber\",\"int\",f),p.uniforms.add([new a(\"vvOpacityValues\",(e=>e.vvOpacityValues),f),new a(\"vvOpacityOpacities\",(e=>e.vvOpacityOpacities),f)]),p.code.add(l`\n    vec4 applyOpacity(vec4 color) {\n      float value = ${n}.z;\n      if (value <= vvOpacityValues[0]) {\n        return vec4( color.xyz, vvOpacityOpacities[0]);\n      }\n\n      for (int i = 1; i < vvOpacityNumber; ++i) {\n        if (vvOpacityValues[i] >= value) {\n          float f = (value - vvOpacityValues[i-1]) / (vvOpacityValues[i] - vvOpacityValues[i-1]);\n          return vec4( color.xyz, mix(vvOpacityOpacities[i-1], vvOpacityOpacities[i], f));\n        }\n      }\n\n      return vec4( color.xyz, vvOpacityOpacities[vvOpacityNumber - 1]);\n    }\n    `)):p.code.add(l`vec4 applyOpacity(vec4 color){\nreturn color;\n}`),c.vvColor?(p.constants.add(\"vvColorNumber\",\"int\",s),p.uniforms.add([new a(\"vvColorValues\",(e=>e.vvColorValues),s),new t(\"vvColorColors\",(e=>e.vvColorColors),s)]),p.code.add(l`\n    vec4 getColor() {\n      float value = ${n}.y;\n      if (value <= vvColorValues[0]) {\n        return applyOpacity(vvColorColors[0]);\n      }\n\n      for (int i = 1; i < vvColorNumber; ++i) {\n        if (vvColorValues[i] >= value) {\n          float f = (value - vvColorValues[i-1]) / (vvColorValues[i] - vvColorValues[i-1]);\n          return applyOpacity(mix(vvColorColors[i-1], vvColorColors[i], f));\n        }\n      }\n\n      return applyOpacity(vvColorColors[vvColorNumber - 1]);\n    }\n    `)):p.code.add(l`vec4 getColor(){\nreturn applyOpacity(vec4(1, 1, 1, 1));\n}`),e.include(o),e.attributes.add(v.PROFILERIGHT,\"vec4\"),e.attributes.add(v.PROFILEUP,\"vec4\"),e.attributes.add(v.PROFILEVERTEXANDNORMAL,\"vec4\"),p.code.add(l`vec3 calculateVPos() {\nvec2 size = getSize();\nvec3 origin = position;\nvec3 right = profileRight.xyz;\nvec3 up = profileUp.xyz;\nvec3 forward = cross(up, right);\nvec2 profileVertex = profileVertexAndNormal.xy * size;\nvec2 profileNormal = profileVertexAndNormal.zw;\nfloat positionOffsetAlongProfilePlaneNormal = 0.0;\nfloat normalOffsetAlongProfilePlaneNormal = 0.0;`),p.code.add(l`if(!isCapVertex()) {\nvec2 rotationRight = vec2(profileRight.w, profileUp.w);\nfloat maxDistance = length(rotationRight);`),p.code.add(l`rotationRight = maxDistance > 0.0 ? normalize(rotationRight) : vec2(0, 0);\nfloat rx = dot(profileVertex, rotationRight);\nif (abs(rx) > maxDistance) {\nvec2 rotationUp = vec2(-rotationRight.y, rotationRight.x);\nfloat ry = dot(profileVertex, rotationUp);\nprofileVertex = rotationRight * maxDistance * sign(rx) + rotationUp * ry;\n}\n}else{\npositionOffsetAlongProfilePlaneNormal = profileRight.w * size[0];\nnormalOffsetAlongProfilePlaneNormal = profileUp.w;\n}\nvec3 offset = right * profileVertex.x + up * profileVertex.y + forward * positionOffsetAlongProfilePlaneNormal;\nreturn origin + offset;\n}`),p.code.add(l`vec3 localNormal() {\nvec3 right = profileRight.xyz;\nvec3 up = profileUp.xyz;\nvec3 forward = cross(up, right);\nvec2 profileNormal = profileVertexAndNormal.zw;\nvec3 normal = right * profileNormal.x + up * profileNormal.y;\nif(isCapVertex()) {\nnormal += forward * profileUp.w;\n}\nreturn normal;\n}`)}class p extends c{constructor(){super(...arguments),this.size=e(1,1)}}export{n as PathVertexPosition,p as PathVertexPositionPassParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ForwardLinearDepth as e,addNearFar as i}from\"../views/3d/webgl-engine/core/shaderLibrary/ForwardLinearDepth.glsl.js\";import{ShaderOutput as o}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as a}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{Transform as r}from\"../views/3d/webgl-engine/core/shaderLibrary/Transform.glsl.js\";import{PathVertexPosition as l}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/PathVertexPosition.glsl.js\";import{OutputDepth as s}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputDepth.glsl.js\";import{OutputHighlight as n}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputHighlight.glsl.js\";import{EvaluateAmbientOcclusion as d}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/EvaluateAmbientOcclusion.glsl.js\";import{EvaluateSceneLighting as t,addAmbientBoostFactor as c,addLightingGlobalFactor as g}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/EvaluateSceneLighting.glsl.js\";import{addMainLightIntensity as m}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MainLighting.glsl.js\";import{multipassTerrainTest as p}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{Normals as v}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/Normals.glsl.js\";import{NormalUtils as h}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/NormalUtils.glsl.js\";import{ReadShadowMapDraw as u}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/ReadShadowMap.glsl.js\";import{ColorConversion as w}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ColorConversion.glsl.js\";import{addProjViewLocalOrigin as b,addViewNormal as f,addCameraPosition as y}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float3PassUniform as j}from\"../views/3d/webgl-engine/core/shaderModules/Float3PassUniform.js\";import{FloatPassUniform as L}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as P}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as S}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{TransparencyPassType as C}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";function F(F){const _=new S,{vertex:M,fragment:O}=_;switch(b(M,F),_.varyings.add(\"vpos\",\"vec3\"),_.include(l,F),F.output!==o.Color&&F.output!==o.Alpha||(_.include(r,F),_.include(u,F),_.include(e,F),_.varyings.add(\"vnormal\",\"vec3\"),_.varyings.add(\"vcolor\",\"vec4\"),F.hasMultipassTerrain&&_.varyings.add(\"depth\",\"float\"),M.code.add(P`\n      void main() {\n        vpos = calculateVPos();\n        vnormal = normalize(localNormal());\n\n        ${F.hasMultipassTerrain?\"depth = (view * vec4(vpos, 1.0)).z;\":\"\"}\n        gl_Position = transformPosition(proj, view, vpos);\n\n        ${F.output===o.Color?\"forwardLinearDepth();\":\"\"}\n\n        vcolor = getColor();\n      }\n    `)),_.include(p,F),F.output){case o.Alpha:_.include(a,F),O.uniforms.add(new L(\"opacity\",(e=>e.opacity))),O.code.add(P`\n      void main() {\n        discardBySlice(vpos);\n        ${F.hasMultipassTerrain?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n        float combinedOpacity = vcolor.a * opacity;\n        gl_FragColor = vec4(combinedOpacity);\n      }\n    `);break;case o.Color:_.include(a,F),_.include(t,F),_.include(d,F),_.include(u,F),_.include(v,F),y(O,F),c(O),g(O),O.uniforms.add([M.uniforms.get(\"localOrigin\"),new j(\"ambient\",(e=>e.ambient)),new j(\"diffuse\",(e=>e.diffuse)),new j(\"specular\",(e=>e.specular)),new L(\"opacity\",(e=>e.opacity))]),O.include(w),m(O),O.code.add(P`\n        void main() {\n          discardBySlice(vpos);\n          ${F.hasMultipassTerrain?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n\n          shadingParams.viewDirection = normalize(vpos - cameraPosition);\n          shadingParams.normalView = vnormal;\n          vec3 normal = shadingNormal(shadingParams);\n          float ssao = evaluateAmbientOcclusionInverse();\n\n          float additionalAmbientScale = additionalDirectedAmbientLight(vpos + localOrigin);\n          vec3 additionalLight = ssao * mainLightIntensity * additionalAmbientScale * ambientBoostFactor * lightingGlobalFactor;\n          ${F.receiveShadows?\"float shadow = readShadowMap(vpos, linearDepth);\":F.spherical?\"float shadow = lightingGlobalFactor * (1.0 - additionalAmbientScale);\":\"float shadow = 0.0;\"}\n          vec3 albedo = vcolor.rgb * max(ambient, diffuse); // combine the old material parameters into a single one\n          float combinedOpacity = vcolor.a * opacity;\n          albedo += 0.25 * specular; // don't completely ignore specular for now\n\n          vec3 shadedColor = evaluateSceneLighting(normal, albedo, shadow, 1.0 - ssao, additionalLight);\n          gl_FragColor = vec4(shadedColor, combinedOpacity);\n          gl_FragColor = highlightSlice(gl_FragColor, vpos);\n          ${F.transparencyPassType===C.Color?\"gl_FragColor = premultiplyAlpha(gl_FragColor);\":\"\"}\n        }\n      `);break;case o.Depth:case o.Shadow:case o.ShadowHighlight:case o.ShadowExcludeHighlight:_.include(r,F),i(_),_.varyings.add(\"depth\",\"float\"),M.code.add(P`void main() {\nvpos = calculateVPos();\ngl_Position = transformPositionWithDepth(proj, view, vpos, nearFar, depth);\n}`),_.include(a,F),_.include(s,F),O.code.add(P`void main() {\ndiscardBySlice(vpos);\noutputDepth(depth);\n}`);break;case o.Normal:_.include(r,F),_.include(h,F),f(M),_.varyings.add(\"vnormal\",\"vec3\"),M.code.add(P`void main(void) {\nvpos = calculateVPos();\nvnormal = normalize((viewNormal * vec4(localNormal(), 1.0)).xyz);\ngl_Position = transformPosition(proj, view, vpos);\n}`),_.include(a,F),O.code.add(P`void main() {\ndiscardBySlice(vpos);\nvec3 normal = normalize(vnormal);\nif (gl_FrontFacing == false) normal = -normal;\ngl_FragColor = vec4(vec3(0.5) + 0.5 * normal, 1.0);\n}`);break;case o.Highlight:_.include(r,F),_.include(h,F),_.varyings.add(\"vnormal\",\"vec3\"),M.code.add(P`void main(void) {\nvpos = calculateVPos();\ngl_Position = transformPosition(proj, view, vpos);\n}`),_.include(a,F),_.include(n,F),O.code.add(P`void main() {\ndiscardBySlice(vpos);\noutputHighlight();\n}`)}return _}const _=Object.freeze(Object.defineProperty({__proto__:null,build:F},Symbol.toStringTag,{value:\"Module\"}));export{_ as P,F as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI8pB,IAAM,IAAE;AAAE,SAASA,GAAEC,IAAEC,IAAE;AAAC,QAAMF,KAAE,EAAE;AAAa,EAAAC,GAAE,WAAW,IAAID,IAAE,MAAM;AAAE,QAAMG,KAAEF,GAAE;AAAO,EAAAE,GAAE,KAAK,IAAI;AAAA;AAAA,aAEzvBH,EAAC;AAAA;AAAA,GAEX,GAAEG,GAAE,SAAS,IAAI,IAAI,EAAE,QAAQ,CAAAF,OAAGA,GAAE,IAAK,CAAC,GAAEC,GAAE,UAAQC,GAAE,SAAS,IAAI,IAAIF,GAAE,iBAAiB,CAAAA,OAAGA,GAAE,aAAc,CAAC,GAAEE,GAAE,SAAS,IAAI,IAAIF,GAAE,iBAAiB,CAAAA,OAAGA,GAAE,aAAc,CAAC,GAAEE,GAAE,SAAS,IAAI,IAAIF,GAAE,gBAAgB,CAAAA,OAAGA,GAAE,YAAa,CAAC,GAAEE,GAAE,SAAS,IAAI,IAAIF,GAAE,gBAAgB,CAAAA,OAAGA,GAAE,YAAa,CAAC,GAAEE,GAAE,KAAK,IAAI;AAAA;AAAA,2CAErQH,EAAC;AAAA;AAAA,KAEvC,KAAGG,GAAE,KAAK,IAAI;AAAA;AAAA,EAEjB,GAAED,GAAE,aAAWC,GAAE,UAAU,IAAI,mBAAkB,OAAM,CAAC,GAAEA,GAAE,SAAS,IAAI,CAAC,IAAIC,GAAE,mBAAmB,CAAAH,OAAGA,GAAE,iBAAiB,CAAC,GAAE,IAAIG,GAAE,sBAAsB,CAAAH,OAAGA,GAAE,oBAAoB,CAAC,CAAC,CAAC,GAAEE,GAAE,KAAK,IAAI;AAAA;AAAA,sBAE7KH,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAclB,KAAGG,GAAE,KAAK,IAAI;AAAA;AAAA,EAEjB,GAAED,GAAE,WAASC,GAAE,UAAU,IAAI,iBAAgB,OAAMC,EAAC,GAAED,GAAE,SAAS,IAAI,CAAC,IAAIC,GAAE,iBAAiB,CAAAH,OAAGA,GAAE,eAAeG,EAAC,GAAE,IAAIH,GAAE,iBAAiB,CAAAA,OAAGA,GAAE,eAAeG,EAAC,CAAC,CAAC,GAAED,GAAE,KAAK,IAAI;AAAA;AAAA,sBAE3JH,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAclB,KAAGG,GAAE,KAAK,IAAI;AAAA;AAAA,EAEjB,GAAEF,GAAE,QAAQG,EAAC,GAAEH,GAAE,WAAW,IAAI,EAAE,cAAa,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,WAAU,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,wBAAuB,MAAM,GAAEE,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iDAS1G,GAAEA,GAAE,KAAK,IAAI;AAAA;AAAA,2CAEnB,GAAEA,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAatD,GAAEA,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUb;AAAC;AAAC,IAAMA,KAAN,cAAgBE,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,EAAE,GAAE,CAAC;AAAA,EAAC;AAAC;;;AChFkqE,SAAS,EAAEC,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAE,EAAC,QAAO,GAAE,UAASC,GAAC,IAAEF;AAAE,UAAO,EAAE,GAAED,EAAC,GAAEC,GAAE,SAAS,IAAI,QAAO,MAAM,GAAEA,GAAE,QAAQG,IAAEJ,EAAC,GAAEA,GAAE,WAAS,EAAE,SAAOA,GAAE,WAAS,EAAE,UAAQC,GAAE,QAAQI,IAAEL,EAAC,GAAEC,GAAE,QAAQK,IAAEN,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,SAAS,IAAI,WAAU,MAAM,GAAEA,GAAE,SAAS,IAAI,UAAS,MAAM,GAAED,GAAE,uBAAqBC,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,UAKziFD,GAAE,sBAAoB,wCAAsC,EAAE;AAAA;AAAA;AAAA,UAG9DA,GAAE,WAAS,EAAE,QAAM,0BAAwB,EAAE;AAAA;AAAA;AAAA;AAAA,KAIlD,IAAGC,GAAE,QAAQG,IAAEJ,EAAC,GAAEA,GAAE,QAAO;AAAA,IAAC,KAAK,EAAE;AAAM,MAAAC,GAAE,QAAQ,GAAED,EAAC,GAAEG,GAAE,SAAS,IAAI,IAAID,GAAE,WAAW,CAAAK,OAAGA,GAAE,OAAQ,CAAC,GAAEJ,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA,UAG9GH,GAAE,sBAAoB,2CAAyC,EAAE;AAAA;AAAA;AAAA;AAAA,KAItE;AAAE;AAAA,IAAM,KAAK,EAAE;AAAM,MAAAC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQK,IAAEN,EAAC,GAAEC,GAAE,QAAQM,IAAEP,EAAC,GAAE,EAAEG,IAAEH,EAAC,GAAEQ,GAAEL,EAAC,GAAEM,GAAEN,EAAC,GAAEA,GAAE,SAAS,IAAI,CAAC,EAAE,SAAS,IAAI,aAAa,GAAE,IAAII,GAAE,WAAW,CAAAA,OAAGA,GAAE,OAAQ,GAAE,IAAIA,GAAE,WAAW,CAAAA,OAAGA,GAAE,OAAQ,GAAE,IAAIA,GAAE,YAAY,CAAAA,OAAGA,GAAE,QAAS,GAAE,IAAIL,GAAE,WAAW,CAAAK,OAAGA,GAAE,OAAQ,CAAC,CAAC,GAAEJ,GAAE,QAAQI,EAAC,GAAEG,GAAEP,EAAC,GAAEA,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA,YAGzTH,GAAE,sBAAoB,2CAAyC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YASjEA,GAAE,iBAAe,qDAAmDA,GAAE,YAAU,0EAAwE,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAQ7KA,GAAE,yBAAuBE,GAAE,QAAM,mDAAiD,EAAE;AAAA;AAAA,OAEzF;AAAE;AAAA,IAAM,KAAK,EAAE;AAAA,IAAM,KAAK,EAAE;AAAA,IAAO,KAAK,EAAE;AAAA,IAAgB,KAAK,EAAE;AAAuB,MAAAD,GAAE,QAAQI,IAAEL,EAAC,GAAE,EAAEC,EAAC,GAAEA,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAG5J,GAAEA,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQC,IAAEF,EAAC,GAAEG,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAG3C;AAAE;AAAA,IAAM,KAAK,EAAE;AAAO,MAAAF,GAAE,QAAQI,IAAEL,EAAC,GAAEC,GAAE,QAAQI,IAAEL,EAAC,GAAEW,GAAE,CAAC,GAAEV,GAAE,SAAS,IAAI,WAAU,MAAM,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,EAIrG,GAAEA,GAAE,QAAQ,GAAED,EAAC,GAAEG,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B;AAAE;AAAA,IAAM,KAAK,EAAE;AAAU,MAAAF,GAAE,QAAQI,IAAEL,EAAC,GAAEC,GAAE,QAAQI,IAAEL,EAAC,GAAEC,GAAE,SAAS,IAAI,WAAU,MAAM,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAGnG,GAAEA,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEG,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAG3C;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,IAAM,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAM,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["n", "e", "c", "p", "o", "v", "F", "_", "o", "O", "n", "r", "v", "e", "h", "u", "a", "d"]}