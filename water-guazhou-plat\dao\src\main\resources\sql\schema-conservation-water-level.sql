-- 涵养水位监测表
CREATE TABLE IF NOT EXISTS tb_conservation_water_level (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    station_id VARCHAR(36) NOT NULL,
    raw_water_level NUMERIC(10, 3),
    groundwater_level NUMERIC(10, 3),
    level_change NUMERIC(10, 3),
    rainfall_amount NUMERIC(10, 2),
    evaporation_amount NUMERIC(10, 2),
    surface_runoff NUMERIC(12, 2),
    extraction_amount NUMERIC(12, 2),
    soil_moisture NUMERIC(5, 2),
    permeability_coefficient NUMERIC(8, 4),
    record_time TIMESTAMP,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    remark TEXT,
    data_source INTEGER DEFAULT 1,
    creator VARCHAR(36)
);

-- 涵养水位分析结果表
CREATE TABLE IF NOT EXISTS tb_conservation_analysis (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    station_id VARCHAR(36) NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    initial_level NUMERIC(10, 3),
    final_level NUMERIC(10, 3),
    level_change NUMERIC(10, 3),
    avg_rainfall NUMERIC(10, 2),
    avg_evaporation NUMERIC(10, 2),
    total_extraction NUMERIC(12, 2),
    conservation_coefficient NUMERIC(8, 4),
    conservation_potential NUMERIC(5, 2),
    suggested_conservation_amount NUMERIC(12, 2),
    conservation_suggestion TEXT,
    risk_level INTEGER,
    risk_description TEXT,
    algorithm_version VARCHAR(50),
    analysis_details TEXT,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL,
    status INTEGER DEFAULT 1,
    creator VARCHAR(36)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_conservation_water_level_tenant_station ON tb_conservation_water_level(tenant_id, station_id);
CREATE INDEX IF NOT EXISTS idx_conservation_water_level_record_time ON tb_conservation_water_level(record_time);
CREATE INDEX IF NOT EXISTS idx_conservation_analysis_tenant_station ON tb_conservation_analysis(tenant_id, station_id);
CREATE INDEX IF NOT EXISTS idx_conservation_analysis_time_range ON tb_conservation_analysis(start_time, end_time);

-- 注释
COMMENT ON TABLE tb_conservation_water_level IS '涵养水位监测表';
COMMENT ON COLUMN tb_conservation_water_level.id IS '主键ID';
COMMENT ON COLUMN tb_conservation_water_level.tenant_id IS '租户ID';
COMMENT ON COLUMN tb_conservation_water_level.station_id IS '测点ID';
COMMENT ON COLUMN tb_conservation_water_level.raw_water_level IS '原水液位(米)';
COMMENT ON COLUMN tb_conservation_water_level.groundwater_level IS '地下水位(米)';
COMMENT ON COLUMN tb_conservation_water_level.level_change IS '液位变化量(米)';
COMMENT ON COLUMN tb_conservation_water_level.rainfall_amount IS '降雨量(毫米)';
COMMENT ON COLUMN tb_conservation_water_level.evaporation_amount IS '蒸发量(毫米)';
COMMENT ON COLUMN tb_conservation_water_level.surface_runoff IS '地表径流量(立方米)';
COMMENT ON COLUMN tb_conservation_water_level.extraction_amount IS '地下水开采量(立方米)';
COMMENT ON COLUMN tb_conservation_water_level.soil_moisture IS '土壤含水率(%)';
COMMENT ON COLUMN tb_conservation_water_level.permeability_coefficient IS '渗透系数';
COMMENT ON COLUMN tb_conservation_water_level.record_time IS '记录时间';
COMMENT ON COLUMN tb_conservation_water_level.create_time IS '创建时间';
COMMENT ON COLUMN tb_conservation_water_level.update_time IS '更新时间';
COMMENT ON COLUMN tb_conservation_water_level.remark IS '备注';
COMMENT ON COLUMN tb_conservation_water_level.data_source IS '数据来源 (1-手动录入, 2-设备采集)';
COMMENT ON COLUMN tb_conservation_water_level.creator IS '创建人';

COMMENT ON TABLE tb_conservation_analysis IS '涵养水位分析结果表';
COMMENT ON COLUMN tb_conservation_analysis.id IS '主键ID';
COMMENT ON COLUMN tb_conservation_analysis.tenant_id IS '租户ID';
COMMENT ON COLUMN tb_conservation_analysis.station_id IS '测点ID';
COMMENT ON COLUMN tb_conservation_analysis.start_time IS '分析周期开始时间';
COMMENT ON COLUMN tb_conservation_analysis.end_time IS '分析周期结束时间';
COMMENT ON COLUMN tb_conservation_analysis.initial_level IS '期初水位(米)';
COMMENT ON COLUMN tb_conservation_analysis.final_level IS '期末水位(米)';
COMMENT ON COLUMN tb_conservation_analysis.level_change IS '水位变化量(米)';
COMMENT ON COLUMN tb_conservation_analysis.avg_rainfall IS '平均降雨量(毫米)';
COMMENT ON COLUMN tb_conservation_analysis.avg_evaporation IS '平均蒸发量(毫米)';
COMMENT ON COLUMN tb_conservation_analysis.total_extraction IS '总开采量(立方米)';
COMMENT ON COLUMN tb_conservation_analysis.conservation_coefficient IS '涵养系数';
COMMENT ON COLUMN tb_conservation_analysis.conservation_potential IS '涵养潜力评分(0-100)';
COMMENT ON COLUMN tb_conservation_analysis.suggested_conservation_amount IS '建议涵养量(立方米)';
COMMENT ON COLUMN tb_conservation_analysis.conservation_suggestion IS '涵养建议';
COMMENT ON COLUMN tb_conservation_analysis.risk_level IS '风险等级(1-低风险,2-中风险,3-高风险)';
COMMENT ON COLUMN tb_conservation_analysis.risk_description IS '风险描述';
COMMENT ON COLUMN tb_conservation_analysis.algorithm_version IS '分析算法版本';
COMMENT ON COLUMN tb_conservation_analysis.analysis_details IS '分析结果详情(JSON)';
COMMENT ON COLUMN tb_conservation_analysis.create_time IS '创建时间';
COMMENT ON COLUMN tb_conservation_analysis.update_time IS '更新时间';
COMMENT ON COLUMN tb_conservation_analysis.status IS '分析状态(1-分析中,2-已完成,3-分析失败)';
COMMENT ON COLUMN tb_conservation_analysis.creator IS '创建人';
