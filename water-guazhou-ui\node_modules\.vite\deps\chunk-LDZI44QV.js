import {
  s as s2,
  t
} from "./chunk-SEO6KEGF.js";
import {
  ie,
  p
} from "./chunk-VNYCO3JG.js";
import {
  s
} from "./chunk-4RZONHOY.js";

// node_modules/@arcgis/core/layers/graphics/sources/geojson/geojson.js
var i = { LineString: "esriGeometryPolyline", MultiLineString: "esriGeometryPolyline", MultiPoint: "esriGeometryMultipoint", Point: "esriGeometryPoint", Polygon: "esriGeometryPolygon", MultiPolygon: "esriGeometryPolygon" };
function s3(e) {
  return i[e];
}
function* c(e) {
  switch (e.type) {
    case "Feature":
      yield e;
      break;
    case "FeatureCollection":
      for (const t2 of e.features) t2 && (yield t2);
  }
}
function* u(e) {
  if (e) switch (e.type) {
    case "Point":
      yield e.coordinates;
      break;
    case "LineString":
    case "MultiPoint":
      yield* e.coordinates;
      break;
    case "MultiLineString":
    case "Polygon":
      for (const t2 of e.coordinates) yield* t2;
      break;
    case "MultiPolygon":
      for (const t2 of e.coordinates) for (const e2 of t2) yield* e2;
  }
}
function* l(e, o = {}) {
  const { geometryType: r, objectIdField: i2 } = o;
  for (const c2 of e) {
    const { geometry: e2, properties: u2, id: l2 } = c2;
    if (e2 && s3(e2.type) !== r) continue;
    const f2 = u2 || {};
    let a2;
    i2 && (a2 = f2[i2], null == l2 || a2 || (f2[i2] = a2 = l2));
    const y2 = new s2(e2 ? g(new t(), e2, o) : null, f2, null, a2 ?? void 0);
    yield y2;
  }
}
function f(e) {
  for (const t2 of e) if (t2.length > 2) return true;
  return false;
}
function a(e) {
  return !p2(e);
}
function y(e) {
  return p2(e);
}
function p2(e) {
  let t2 = 0;
  for (let n = 0; n < e.length; n++) {
    const o = e[n], r = e[(n + 1) % e.length];
    t2 += o[0] * r[1] - r[0] * o[1];
  }
  return t2 <= 0;
}
function d(e) {
  const t2 = e[0], n = e[e.length - 1];
  return t2[0] === n[0] && t2[1] === n[1] && t2[2] === n[2] || e.push(t2), e;
}
function g(e, t2, n) {
  switch (t2.type) {
    case "LineString":
      return m(e, t2, n);
    case "MultiLineString":
      return h(e, t2, n);
    case "MultiPoint":
      return w(e, t2, n);
    case "MultiPolygon":
      return P(e, t2, n);
    case "Point":
      return b(e, t2, n);
    case "Polygon":
      return j(e, t2, n);
  }
}
function m(e, t2, n) {
  return G(e, t2.coordinates, n), e;
}
function h(e, t2, n) {
  for (const o of t2.coordinates) G(e, o, n);
  return e;
}
function w(e, t2, n) {
  return G(e, t2.coordinates, n), e;
}
function P(e, t2, n) {
  for (const o of t2.coordinates) {
    S(e, o[0], n);
    for (let t3 = 1; t3 < o.length; t3++) F(e, o[t3], n);
  }
  return e;
}
function b(e, t2, n) {
  return k(e, t2.coordinates, n), e;
}
function j(e, t2, n) {
  const o = t2.coordinates;
  S(e, o[0], n);
  for (let r = 1; r < o.length; r++) F(e, o[r], n);
  return e;
}
function S(e, t2, n) {
  const o = d(t2);
  a(o) ? M(e, o, n) : G(e, o, n);
}
function F(e, t2, n) {
  const o = d(t2);
  y(o) ? M(e, o, n) : G(e, o, n);
}
function G(e, t2, n) {
  for (const o of t2) k(e, o, n);
  e.lengths.push(t2.length);
}
function M(e, t2, n) {
  for (let o = t2.length - 1; o >= 0; o--) k(e, t2[o], n);
  e.lengths.push(t2.length);
}
function k(e, t2, n) {
  const [o, r, i2] = t2;
  e.coords.push(o, r), n.hasZ && e.coords.push(i2 || 0);
}
function O(e) {
  switch (typeof e) {
    case "string":
      return "esriFieldTypeString";
    case "number":
      return "esriFieldTypeDouble";
    default:
      return "unknown";
  }
}
function T(t2) {
  if (!t2) throw new s("geojson-layer:empty", "GeoJSON data is empty");
  if ("Feature" !== t2.type && "FeatureCollection" !== t2.type) throw new s("geojson-layer:unsupported-geojson-object", "missing or not supported GeoJSON object type", { data: t2 });
  const { crs: n } = t2;
  if (!n) return;
  const o = "string" == typeof n ? n : "name" === n.type ? n.properties.name : "EPSG" === n.type ? n.properties.code : null, r = new RegExp(".*(CRS84H?|4326)$", "i");
  if (!o || !r.test(o)) throw new s("geojson-layer:unsupported-crs", "unsupported GeoJSON 'crs' member", { crs: n });
}
function L(e, t2 = {}) {
  const n = [], i2 = /* @__PURE__ */ new Set(), l2 = /* @__PURE__ */ new Set();
  let a2, y2 = false, p3 = null, d2 = false, { geometryType: g2 = null } = t2, m2 = false;
  for (const r of c(e)) {
    const { geometry: e2, properties: t3, id: c2 } = r;
    if (!e2 || (g2 || (g2 = s3(e2.type)), s3(e2.type) === g2)) {
      if (!y2) {
        y2 = f(u(e2));
      }
      if (d2 || (d2 = null != c2, d2 && (a2 = typeof c2, t3 && (p3 = Object.keys(t3).filter((e3) => t3[e3] === c2)))), t3 && p3 && d2 && null != c2 && (p3.length > 1 ? p3 = p3.filter((e3) => t3[e3] === c2) : 1 === p3.length && (p3 = t3[p3[0]] === c2 ? p3 : [])), !m2 && t3) {
        let e3 = true;
        for (const r2 in t3) {
          if (i2.has(r2)) continue;
          const s4 = t3[r2];
          if (null == s4) {
            e3 = false, l2.add(r2);
            continue;
          }
          const c3 = O(s4);
          if ("unknown" === c3) {
            l2.add(r2);
            continue;
          }
          l2.delete(r2), i2.add(r2);
          const u2 = p(r2);
          u2 && n.push({ name: u2, alias: r2, type: c3 });
        }
        m2 = e3;
      }
    }
  }
  const h2 = p(1 === (p3 == null ? void 0 : p3.length) && p3[0] || null) ?? void 0;
  if (h2) {
    for (const o of n) if (o.name === h2 && ie(o)) {
      o.type = "esriFieldTypeOID";
      break;
    }
  }
  return { fields: n, geometryType: g2, hasZ: y2, objectIdFieldName: h2, objectIdFieldType: a2, unknownFields: Array.from(l2) };
}
function I(e, t2) {
  return Array.from(l(c(e), t2));
}

export {
  s3 as s,
  T,
  L,
  I
};
//# sourceMappingURL=chunk-LDZI44QV.js.map
