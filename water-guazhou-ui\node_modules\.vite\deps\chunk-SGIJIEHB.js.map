{"version": 3, "sources": ["../../@arcgis/core/core/Evented.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import e from\"./Accessor.js\";import{handlesGroup as s}from\"./handleUtils.js\";import{subclass as r}from\"./accessorSupport/decorators/subclass.js\";class i{constructor(){this._emitter=new i.EventEmitter(this)}emit(t,e){return this._emitter.emit(t,e)}on(t,e){return this._emitter.on(t,e)}once(t,e){return this._emitter.once(t,e)}hasEventListener(t){return this._emitter.hasEventListener(t)}}!function(n){class o{constructor(t=null){this._target=t,this._listenersMap=null}clear(){this._listenersMap&&this._listenersMap.clear(),this._listenersMap=null}emit(t,e){const s=this._listenersMap&&this._listenersMap.get(t);if(!s)return!1;const r=this._target||this;return[...s].forEach((t=>{t.call(r,e)})),s.length>0}on(t,e){if(Array.isArray(t)){const r=t.map((t=>this.on(t,e)));return s(r)}if(t.includes(\",\"))throw new TypeError(\"Evented.on() with a comma delimited string of event types is not supported\");this._listenersMap||(this._listenersMap=new Map);const r=this._listenersMap.get(t)||[];return r.push(e),this._listenersMap.set(t,r),{remove:()=>{const s=this._listenersMap&&this._listenersMap.get(t)||[],r=s.indexOf(e);r>=0&&s.splice(r,1)}}}once(t,e){const s=this.on(t,(t=>{s.remove(),e.call(null,t)}));return s}hasEventListener(t){const e=this._listenersMap&&this._listenersMap.get(t);return null!=e&&e.length>0}}n.EventEmitter=o,n.EventedMixin=e=>{let s=class extends e{constructor(){super(...arguments),this._emitter=new o}destroy(){this._emitter.clear()}emit(t,e){return this._emitter.emit(t,e)}on(t,e){return this._emitter.on(t,e)}once(t,e){return this._emitter.once(t,e)}hasEventListener(t){return this._emitter.hasEventListener(t)}};return s=t([r(\"esri.core.Evented\")],s),s};let h=class extends e{constructor(){super(...arguments),this._emitter=new i.EventEmitter(this)}destroy(){this._emitter.clear()}emit(t,e){return this._emitter.emit(t,e)}on(t,e){return this._emitter.on(t,e)}once(t,e){return this._emitter.once(t,e)}hasEventListener(t){return this._emitter.hasEventListener(t)}};h=t([r(\"esri.core.Evented\")],h),n.EventedAccessor=h}(i||(i={}));const n=i;export{n as default};\n"], "mappings": ";;;;;;;;;;;;AAI4L,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,cAAa;AAAC,SAAK,WAAS,IAAI,GAAE,aAAa,IAAI;AAAA,EAAC;AAAA,EAAC,KAAK,GAAEA,IAAE;AAAC,WAAO,KAAK,SAAS,KAAK,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAG,GAAEA,IAAE;AAAC,WAAO,KAAK,SAAS,GAAG,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,KAAK,GAAEA,IAAE;AAAC,WAAO,KAAK,SAAS,KAAK,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiB,GAAE;AAAC,WAAO,KAAK,SAAS,iBAAiB,CAAC;AAAA,EAAC;AAAC;AAAC,CAAC,SAASC,IAAE;AAAA,EAAC,MAAM,EAAC;AAAA,IAAC,YAAY,IAAE,MAAK;AAAC,WAAK,UAAQ,GAAE,KAAK,gBAAc;AAAA,IAAI;AAAA,IAAC,QAAO;AAAC,WAAK,iBAAe,KAAK,cAAc,MAAM,GAAE,KAAK,gBAAc;AAAA,IAAI;AAAA,IAAC,KAAK,GAAED,IAAE;AAAC,YAAM,IAAE,KAAK,iBAAe,KAAK,cAAc,IAAI,CAAC;AAAE,UAAG,CAAC,EAAE,QAAM;AAAG,YAAME,KAAE,KAAK,WAAS;AAAK,aAAM,CAAC,GAAG,CAAC,EAAE,QAAS,CAAAC,OAAG;AAAC,QAAAA,GAAE,KAAKD,IAAEF,EAAC;AAAA,MAAC,CAAE,GAAE,EAAE,SAAO;AAAA,IAAC;AAAA,IAAC,GAAG,GAAEA,IAAE;AAAC,UAAG,MAAM,QAAQ,CAAC,GAAE;AAAC,cAAME,KAAE,EAAE,IAAK,CAAAC,OAAG,KAAK,GAAGA,IAAEH,EAAC,CAAE;AAAE,eAAO,EAAEE,EAAC;AAAA,MAAC;AAAC,UAAG,EAAE,SAAS,GAAG,EAAE,OAAM,IAAI,UAAU,4EAA4E;AAAE,WAAK,kBAAgB,KAAK,gBAAc,oBAAI;AAAK,YAAMA,KAAE,KAAK,cAAc,IAAI,CAAC,KAAG,CAAC;AAAE,aAAOA,GAAE,KAAKF,EAAC,GAAE,KAAK,cAAc,IAAI,GAAEE,EAAC,GAAE,EAAC,QAAO,MAAI;AAAC,cAAM,IAAE,KAAK,iBAAe,KAAK,cAAc,IAAI,CAAC,KAAG,CAAC,GAAEA,KAAE,EAAE,QAAQF,EAAC;AAAE,QAAAE,MAAG,KAAG,EAAE,OAAOA,IAAE,CAAC;AAAA,MAAC,EAAC;AAAA,IAAC;AAAA,IAAC,KAAK,GAAEF,IAAE;AAAC,YAAM,IAAE,KAAK,GAAG,GAAG,CAAAG,OAAG;AAAC,UAAE,OAAO,GAAEH,GAAE,KAAK,MAAKG,EAAC;AAAA,MAAC,CAAE;AAAE,aAAO;AAAA,IAAC;AAAA,IAAC,iBAAiB,GAAE;AAAC,YAAMH,KAAE,KAAK,iBAAe,KAAK,cAAc,IAAI,CAAC;AAAE,aAAO,QAAMA,MAAGA,GAAE,SAAO;AAAA,IAAC;AAAA,EAAC;AAAC,EAAAC,GAAE,eAAa,GAAEA,GAAE,eAAa,CAAAD,OAAG;AAAC,QAAI,IAAE,cAAcA,GAAC;AAAA,MAAC,cAAa;AAAC,cAAM,GAAG,SAAS,GAAE,KAAK,WAAS,IAAI;AAAA,MAAC;AAAA,MAAC,UAAS;AAAC,aAAK,SAAS,MAAM;AAAA,MAAC;AAAA,MAAC,KAAK,GAAEA,IAAE;AAAC,eAAO,KAAK,SAAS,KAAK,GAAEA,EAAC;AAAA,MAAC;AAAA,MAAC,GAAG,GAAEA,IAAE;AAAC,eAAO,KAAK,SAAS,GAAG,GAAEA,EAAC;AAAA,MAAC;AAAA,MAAC,KAAK,GAAEA,IAAE;AAAC,eAAO,KAAK,SAAS,KAAK,GAAEA,EAAC;AAAA,MAAC;AAAA,MAAC,iBAAiB,GAAE;AAAC,eAAO,KAAK,SAAS,iBAAiB,CAAC;AAAA,MAAC;AAAA,IAAC;AAAE,WAAO,IAAE,EAAE,CAAC,EAAE,mBAAmB,CAAC,GAAE,CAAC,GAAE;AAAA,EAAC;AAAE,MAAI,IAAE,cAAc,EAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,WAAS,IAAI,EAAE,aAAa,IAAI;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,WAAK,SAAS,MAAM;AAAA,IAAC;AAAA,IAAC,KAAK,GAAEA,IAAE;AAAC,aAAO,KAAK,SAAS,KAAK,GAAEA,EAAC;AAAA,IAAC;AAAA,IAAC,GAAG,GAAEA,IAAE;AAAC,aAAO,KAAK,SAAS,GAAG,GAAEA,EAAC;AAAA,IAAC;AAAA,IAAC,KAAK,GAAEA,IAAE;AAAC,aAAO,KAAK,SAAS,KAAK,GAAEA,EAAC;AAAA,IAAC;AAAA,IAAC,iBAAiB,GAAE;AAAC,aAAO,KAAK,SAAS,iBAAiB,CAAC;AAAA,IAAC;AAAA,EAAC;AAAE,MAAE,EAAE,CAAC,EAAE,mBAAmB,CAAC,GAAE,CAAC,GAAEC,GAAE,kBAAgB;AAAC,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAM,IAAE;", "names": ["e", "n", "r", "t"]}