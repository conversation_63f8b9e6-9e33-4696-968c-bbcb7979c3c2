<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.thingsboard.server.dao.sql.waterSource.SamplingMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.sampling.Sampling">
        SELECT
            s.id,
            s.tenant_id as tenantId,
            s.creator,
            s.sampling_time as samplingTime,
            s.sampling_location as samplingLocation,
            s.sampling_person as samplingPerson,
            s.record_file as recordFile,
            s.sampling_method as samplingMethod,
            s.sample_number as sampleNumber,
            s.sample_type as sampleType,
            s.create_time as createTime,
            s.update_time as updateTime,
            s.remark,
            s.type
        FROM tb_water_sampling s
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                AND s.tenant_id = #{params.tenantId}
            </if>
            <if test="params.samplingLocation != null and params.samplingLocation != ''">
                AND s.sampling_location LIKE '%' || #{params.samplingLocation} || '%'
            </if>
            <if test="params.samplingPerson != null and params.samplingPerson != ''">
                AND s.sampling_person LIKE '%' || #{params.samplingPerson} || '%'
            </if>
            <if test="params.sampleNumber != null and params.sampleNumber != ''">
                AND s.sample_number LIKE '%' || #{params.sampleNumber} || '%'
            </if>
            <if test="params.sampleType != null and params.sampleType != ''">
                AND s.sample_type LIKE '%' || #{params.sampleType} || '%'
            </if>
            <if test="params.samplingTime != null">
                AND TO_CHAR(s.sampling_time, 'YYYY-MM') = TO_CHAR(to_timestamp(CAST(#{params.samplingTime} AS BIGINT)/1000), 'YYYY-MM')
            </if>
            <if test="params.type != null and params.type != ''">
                AND s.type = #{params.type}
            </if>
        </where>
        <choose>
            <when test="params.sortField != null and params.sortField != '' and params.sortOrder != null and params.sortOrder != ''">
                <choose>
                    <!-- 处理可能的驼峰命名转换为下划线命名 -->
                    <when test="params.sortField == 'createTime' or params.sortField == 'createtime'">
                        ORDER BY s.create_time ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'updateTime' or params.sortField == 'updatetime'">
                        ORDER BY s.update_time ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'samplingTime' or params.sortField == 'samplingtime'">
                        ORDER BY s.sampling_time ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'samplingLocation' or params.sortField == 'samplinglocation'">
                        ORDER BY s.sampling_location ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'samplingPerson' or params.sortField == 'samplingperson'">
                        ORDER BY s.sampling_person ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'sampleNumber' or params.sortField == 'samplenumber'">
                        ORDER BY s.sample_number ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'sampleType' or params.sortField == 'sampletype'">
                        ORDER BY s.sample_type ${params.sortOrder}
                    </when>
                    <otherwise>
                        <!-- 如果是其他字段，尝试直接使用 -->
                        ORDER BY s.${params.sortField} ${params.sortOrder}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY s.create_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
