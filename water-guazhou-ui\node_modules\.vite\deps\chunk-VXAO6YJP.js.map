{"version": 3, "sources": ["../../@arcgis/core/core/BidiText.js", "../../@arcgis/core/symbols/cim/TextRasterizer.js", "../../@arcgis/core/symbols/cim/CIMEffects.js", "../../@arcgis/core/symbols/cim/CIMImageColorSubstitutionHelper.js", "../../@arcgis/core/symbols/cim/CIMPlacements.js", "../../@arcgis/core/symbols/cim/CIMCursor.js", "../../@arcgis/core/symbols/cim/effects/EffectAddControlPoints.js", "../../@arcgis/core/symbols/cim/CurveHelper.js", "../../@arcgis/core/symbols/cim/effects/EffectArrow.js", "../../@arcgis/core/symbols/cim/effects/EffectBuffer.js", "../../@arcgis/core/symbols/cim/effects/EffectControlMeasureLine.js", "../../@arcgis/core/symbols/cim/effects/EffectCut.js", "../../@arcgis/core/symbols/cim/GeometryWalker.js", "../../@arcgis/core/symbols/cim/effects/EffectDashes.js", "../../@arcgis/core/symbols/cim/effects/EffectDonut.js", "../../@arcgis/core/symbols/cim/effects/EffectJog.js", "../../@arcgis/core/symbols/cim/effects/EffectMove.js", "../../@arcgis/core/symbols/cim/effects/EffectOffset.js", "../../@arcgis/core/symbols/cim/effects/EffectReverse.js", "../../@arcgis/core/symbols/cim/effects/EffectRotate.js", "../../@arcgis/core/symbols/cim/effects/EffectScale.js", "../../@arcgis/core/symbols/cim/effects/EffectWave.js", "../../@arcgis/core/symbols/cim/placements/PlacementAlongLineSameSize.js", "../../@arcgis/core/symbols/cim/placements/PlacementAtExtremities.js", "../../@arcgis/core/symbols/cim/placements/PlacementAtRatioPositions.js", "../../@arcgis/core/symbols/cim/placements/PlacementInsidePolygon.js", "../../@arcgis/core/symbols/cim/placements/PlacementOnLine.js", "../../@arcgis/core/symbols/cim/placements/PlacementOnVertices.js", "../../@arcgis/core/core/PriorityQueue.js", "../../@arcgis/core/geometry/support/labelPoint.js", "../../@arcgis/core/symbols/cim/placements/PlacementPolygonCenter.js", "../../@arcgis/core/symbols/cim/CIMOperators.js", "../../@arcgis/core/symbols/cim/imageUtils.js", "../../@arcgis/core/symbols/cim/Rect.js", "../../@arcgis/core/views/2d/engine/webgl/collisions/BoundingBox.js", "../../@arcgis/core/views/2d/engine/webgl/mesh/templates/shapingUtils.js", "../../@arcgis/core/symbols/cim/CIMSymbolDrawHelper.js", "../../@arcgis/core/symbols/cim/CIMSymbolHelper.js", "../../@arcgis/core/symbols/cim/quantizeTime.js", "../../@arcgis/core/symbols/cim/SDFHelper.js", "../../@arcgis/core/symbols/cim/effects/CIMEffectHelper.js", "../../@arcgis/core/views/2d/engine/webgl/grouping.js", "../../@arcgis/core/symbols/cim/cimAnalyzer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"./BidiEngine.js\";const n=new r;function i(r){if(null==r)return[\"\",!1];if(!n.hasBidiChar(r))return[r,!1];let i;return i=\"rtl\"===n.checkContextual(r)?\"IDNNN\":\"ICNNN\",[n.bidiTransform(r,i,\"VLYSN\"),!0]}export{i as bidiText};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{pt2px as t}from\"../../core/screenUtils.js\";function e(t){return`rgb(${t.slice(0,3).toString()})`}function i(t){return`rgba(${t.slice(0,3).toString()},${t[3]})`}class s{constructor(t){t&&(this._textRasterizationCanvas=t)}rasterizeText(t,s){this._textRasterizationCanvas||(this._textRasterizationCanvas=document.createElement(\"canvas\"));const r=this._textRasterizationCanvas,o=r.getContext(\"2d\");this._setFontProperties(o,s),this._parameters=s,this._textLines=t.split(/\\r?\\n/),this._lineHeight=this._computeLineHeight();const h=this._computeTextWidth(o,s),{decoration:a,weight:l}=s.font;this._lineThroughWidthOffset=a&&\"line-through\"===a?.1*this._lineHeight:0;const d=this._lineHeight*this._textLines.length;r.width=h+2*this._lineThroughWidthOffset,r.height=d,this._renderedLineHeight=Math.round(this._lineHeight*s.pixelRatio),this._renderedHaloSize=s.halo.size*s.pixelRatio,this._renderedWidth=h*s.pixelRatio,this._renderedHeight=d*s.pixelRatio,this._lineThroughWidthOffset*=s.pixelRatio;const c=s.color??[0,0,0,0],_=s.halo&&s.halo.color?s.halo.color:[0,0,0,0];this._fillStyle=i(c),this._haloStyle=e(_);const g=this._renderedLineHeight,f=this._renderedHaloSize;o.save(),o.clearRect(0,0,r.width,r.height),this._setFontProperties(o,s);const u=n(o.textAlign,this._renderedWidth)+f,p=f,x=f>0;let m=this._lineThroughWidthOffset,b=0;x&&this._renderHalo(o,u,p,m,b,s),b+=p,m+=u;for(const e of this._textLines)x?(o.globalCompositeOperation=\"destination-out\",o.fillStyle=\"rgb(0, 0, 0)\",o.fillText(e,m,b),o.globalCompositeOperation=\"source-over\",o.fillStyle=this._fillStyle,o.fillText(e,m,b)):(o.fillStyle=this._fillStyle,o.fillText(e,m,b)),a&&\"none\"!==a&&this._renderDecoration(o,m,b,a,l),b+=g;o.restore();const z=this._renderedWidth+2*this._lineThroughWidthOffset,w=this._renderedHeight,v=o.getImageData(0,0,z,w),H=new Uint8Array(v.data);if(s.premultiplyColors){let t;for(let e=0;e<H.length;e+=4)t=H[e+3]/255,H[e]=H[e]*t,H[e+1]=H[e+1]*t,H[e+2]=H[e+2]*t}let y,R;switch(s.horizontalAlignment){case\"left\":y=-.5;break;case\"right\":y=.5;break;default:y=0}switch(s.verticalAlignment){case\"bottom\":R=-.5;break;case\"top\":R=.5;break;default:R=0}return{size:[z,w],image:new Uint32Array(H.buffer),sdf:!1,simplePattern:!1,anchorX:y,anchorY:R,canvas:r}}_renderHalo(t,e,i,s,n,r){const o=this._renderedWidth,h=this._renderedHeight;this._haloRasterizationCanvas||(this._haloRasterizationCanvas=document.createElement(\"canvas\")),this._haloRasterizationCanvas.width=o,this._haloRasterizationCanvas.height=h;const a=this._haloRasterizationCanvas,l=a.getContext(\"2d\");l.clearRect(0,0,o,h),this._setFontProperties(l,r);const{decoration:d,weight:c}=r.font;l.fillStyle=this._haloStyle,l.strokeStyle=this._haloStyle,l.lineJoin=\"round\",this._renderHaloNative(l,e,i,d,c),t.globalAlpha=this._parameters.halo.color[3],t.drawImage(a,0,0,o,h,s,n,o,h),t.globalAlpha=1}_renderHaloNative(t,e,i,s,n){const r=this._renderedLineHeight,o=this._renderedHaloSize;for(const h of this._textLines){const a=2*o,l=5,d=.1;for(let r=0;r<l;r++){const o=(1-(l-1)*d+r*d)*a;t.lineWidth=o,t.strokeText(h,e,i),s&&\"none\"!==s&&this._renderDecoration(t,e,i,s,n,o)}i+=r}}_setFontProperties(e,i){const s=Math.max(i.size,.5),n=i.font,r=`${n.style} ${n.weight} ${t(s*i.pixelRatio).toFixed(1)}px ${n.family}, sans-serif`;let o;switch(e.font=r,e.textBaseline=\"top\",i.horizontalAlignment){case\"left\":default:o=\"left\";break;case\"right\":o=\"right\";break;case\"center\":o=\"center\"}e.textAlign=o}computeTextSize(t,e){this._textRasterizationCanvas||(this._textRasterizationCanvas=document.createElement(\"canvas\"));const i=this._textRasterizationCanvas,s=i.getContext(\"2d\");this._setFontProperties(s,e),this._parameters=e,this._textLines=t.split(/\\r?\\n/),this._lineHeight=this._computeLineHeight();const n=this._computeTextWidth(s,e),r=this._lineHeight*this._textLines.length;return i.width=n,i.height=r,[n*e.pixelRatio,r*e.pixelRatio]}_computeTextWidth(t,e){let i=0;for(const n of this._textLines)i=Math.max(i,t.measureText(n).width);const s=e.font;return(\"italic\"===s.style||\"oblique\"===s.style||\"string\"==typeof s.weight&&(\"bold\"===s.weight||\"bolder\"===s.weight)||\"number\"==typeof s.weight&&s.weight>600)&&(i+=.3*t.measureText(\"w\").width),i+=2*this._parameters.halo.size,Math.round(i)}_computeLineHeight(){let t=1.275*this._parameters.size;const e=this._parameters.font.decoration;return e&&\"underline\"===e&&(t*=1.3),Math.round(t+2*this._parameters.halo.size)}_renderDecoration(t,e,i,s,n,r){const o=.9*this._lineHeight,h=\"bold\"===n?.06:\"bolder\"===n?.09:.04;switch(t.textAlign){case\"center\":e-=this._renderedWidth/2;break;case\"right\":e-=this._renderedWidth}const a=t.textBaseline;if(\"underline\"===s)switch(a){case\"top\":i+=o;break;case\"middle\":i+=o/2}else if(\"line-through\"===s)switch(a){case\"top\":i+=o/1.5;break;case\"middle\":i+=o/3}const l=r?1.5*r:Math.ceil(o*h);t.save(),t.beginPath(),t.strokeStyle=t.fillStyle,t.lineWidth=l,t.moveTo(e-this._lineThroughWidthOffset,i),t.lineTo(e+this._renderedWidth+2*this._lineThroughWidthOffset,i),t.stroke(),t.restore()}}function n(t,e){return\"center\"===t?.5*e:\"right\"===t?e:0}export{s as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as t}from\"../../core/lang.js\";import{TileClipper as o,GeometryType as e}from\"../../geometry/support/TileClipper.js\";const n=512;let r;class s{constructor(t){this._geometry=t}next(){const t=this._geometry;return this._geometry=null,t}}function i(o){return t(o)}function l(t,s){let i,l;r||(r=new o(0,0,0,1)),r.reset(e.Polygon),r.setPixelMargin(s+1),r.setExtent(n);for(const o of t.rings)if(o&&!(o.length<3)){i=o[0][0],l=-o[0][1],r.moveTo(i,l);for(let t=1;t<o.length;t++)i=o[t][0],l=-o[t][1],r.lineTo(i,l);r.close()}const c=r.result(!1);if(c){const t=[];for(const o of c){const e=[];t.push(e);for(const t of o)e.push([t.x,-t.y])}return{rings:t}}return{rings:[]}}function c(t,s){let i,l;r||(r=new o(0,0,0,1)),r.reset(e.LineString),r.setPixelMargin(s+1),r.setExtent(n);for(const o of t.paths)if(o&&!(o.length<2)){i=o[0][0],l=-o[0][1],r.moveTo(i,l);for(let t=1;t<o.length;t++)i=o[t][0],l=-o[t][1],r.lineTo(i,l)}const c=r.result(!1);if(c){const t=[];for(const o of c){const e=[];t.push(e);for(const t of o)e.push([t.x,-t.y])}return{paths:t}}return{paths:[]}}export{s as SimpleGeometryCursor,l as clipPolygonToTileExtent,c as clipPolylineToTileExtent,i as clone};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{applyColorSubstituition(t,a){if(!a)return t;this._rasterizationCanvas||(this._rasterizationCanvas=document.createElement(\"canvas\"));const{width:e,height:n}=t,o=this._rasterizationCanvas,r=o.getContext(\"2d\");t!==o&&(o.width=e,o.height=n,r.drawImage(t,0,0,e,n));const i=r.getImageData(0,0,e,n).data;if(a)for(const h of a)if(h&&h.oldColor&&4===h.oldColor.length&&h.newColor&&4===h.newColor.length){const[t,a,e,n]=h.oldColor,[o,r,s,l]=h.newColor;if(t===o&&a===r&&e===s&&n===l)continue;for(let h=0;h<i.length;h+=4)t===i[h]&&a===i[h+1]&&e===i[h+2]&&n===i[h+3]&&(i[h]=o,i[h+1]=r,i[h+2]=s,i[h+3]=l)}const s=new ImageData(i,e,n);return r.putImageData(s,0,0),o}tintImageData(t,a){if(!a||a.length<4)return t;this._rasterizationCanvas||(this._rasterizationCanvas=document.createElement(\"canvas\"));const{width:e,height:n}=t,o=this._rasterizationCanvas,r=o.getContext(\"2d\");t!==o&&(o.width=e,o.height=n,r.drawImage(t,0,0,e,n));const i=r.getImageData(0,0,e,n),s=new Uint8Array(i.data),h=[a[0]/255,a[1]/255,a[2]/255,a[3]/255];for(let g=0;g<s.length;g+=4)s[g+0]*=h[0],s[g+1]*=h[1],s[g+2]*=h[2],s[g+3]*=h[3];const l=new ImageData(new Uint8ClampedArray(s.buffer),e,n);return r.putImageData(l,0,0),o}}export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(){this.setIdentity()}getAngle(){return(null==this.rz||0===this.rz&&1!==this.rzCos&&0!==this.rzSin)&&(this.rz=Math.atan2(this.rzSin,this.rzCos)),this.rz}setIdentity(){this.tx=0,this.ty=0,this.tz=0,this.s=1,this.rx=0,this.ry=0,this.rz=0,this.rzCos=1,this.rzSin=0}setTranslate(t,s){this.tx=t,this.ty=s}setTranslateZ(t){this.tz=t}setRotateCS(t,s){this.rz=void 0,this.rzCos=t,this.rzSin=s}setRotate(t){this.rz=t,this.rzCos=void 0,this.rzSin=void 0}setRotateY(t){this.ry=t}setScale(t){this.s=t}setMeasure(t){this.m=t}}class s{next(){return null}}export{s as EmptyPlacementCursor,t as Placement};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as t}from\"../../core/lang.js\";import{isPolygon as i,isPolyline as n,isMultipoint as s,isExtent as h,isPoint as e}from\"../../geometry/support/jsonUtils.js\";import{Placement as o}from\"./CIMPlacements.js\";function a(i){const n=t(i);return P(n),n}function l(t){t&&(e(t)?t.y=-t.y:i(t)?u(t.rings):n(t)?u(t.paths):s(t)&&r(t.points))}function r(t){if(t){const i=t.length;for(let n=0;n<i;n++)t[n][1]=-t[n][1]}}function u(t){if(t)for(const i of t)r(i)}function c(t){if(t){for(let i=t.length-1;i>0;--i)t[i][0]-=t[i-1][0],t[i][1]-=t[i-1][1]}}function f(t){if(t)for(const i of t)c(i)}function p(t){if(t){const i=t.length;for(let n=1;n<i;++n)t[n][0]+=t[n-1][0],t[n][1]+=t[n-1][1]}}function m(t){if(t)for(const i of t)p(i)}function P(t){t&&(i(t)?m(t.rings):n(t)?m(t.paths):s(t)&&p(t.points),l(t))}function x(t){t&&(l(t),i(t)?f(t.rings):n(t)?f(t.paths):s(t)&&c(t.points))}function g(t){if(t)for(const i of t)d(i)}function d(t){t&&t.reverse()}function C(t,i,n){return[t[0]+(i[0]-t[0])*n,t[1]+(i[1]-t[1])*n]}function y(t){return!(!t||0===t.length)&&(t[0][0]===t[t.length-1][0]&&t[0][1]===t[t.length-1][1])}function I(t){return t[4]}function j(t,i){t[4]=i}class G{constructor(t,s,e,a=0){this.isClosed=!1,this.multiPath=null,this.acceptPolygon=s,this.acceptPolyline=e,this.geomUnitsPerPoint=a,this.pathCount=-1,this.pathIndex=-1,this.iteratePath=!1,t&&(i(t)?s&&(this.multiPath=t.rings,this.isClosed=!0):n(t)?e&&(this.multiPath=t.paths,this.isClosed=!1):h(t)&&s&&(this.multiPath=b(t).rings,this.isClosed=!0),this.multiPath&&(this.pathCount=this.multiPath.length)),this.internalPlacement=new o}next(){if(!this.multiPath)return null;for(;this.iteratePath||this.pathIndex<this.pathCount-1;){this.iteratePath||this.pathIndex++;const t=this.processPath(this.multiPath[this.pathIndex]);if(t)return t}return this.pathCount=-1,this.pathIndex=-1,this.multiPath=null,null}}class U{constructor(t,i,n,s=0){this.isClosed=!1,this.multiPath=null,this.inputGeometries=t,this.acceptPolygon=i,this.acceptPolyline=n,this.geomUnitsPerPoint=s,this.pathCount=-1,this.pathIndex=-1,this.iteratePath=!1}next(){for(;;){if(!this.multiPath){let t=this.inputGeometries.next();for(;t;){if(i(t)?this.acceptPolygon&&(this.multiPath=t.rings,this.isClosed=!0):n(t)?this.acceptPolyline&&(this.multiPath=t.paths,this.isClosed=!1):h(t)&&this.acceptPolygon&&(this.multiPath=b(t).rings,this.isClosed=!0),this.multiPath){this.pathCount=this.multiPath.length,this.pathIndex=-1;break}t=this.inputGeometries.next()}if(!this.multiPath)return null}for(;this.iteratePath||this.pathIndex<this.pathCount-1;){this.iteratePath||this.pathIndex++;const t=this.processPath(this.multiPath[this.pathIndex]);if(t)return t}this.pathCount=-1,this.pathIndex=-1,this.multiPath=null}}}function b(t){return{rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]}}export{U as PathGeometryCursor,G as PathTransformationCursor,a as cloneAndDecodeGeometry,P as deltaDecodeGeometry,x as deltaEncodeGeometry,C as getCoord2D,I as getId,y as isClosedPath,g as reverseMultipath,d as reversePath,j as setId};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as s}from\"../../../core/lang.js\";import{isPolygon as t,isPolyline as e,isExtent as i}from\"../../../geometry/support/jsonUtils.js\";import{setId as n}from\"../CIMCursor.js\";class o{static local(){return null===o.instance&&(o.instance=new o),o.instance}execute(s,t,e,i,n){return new r(s,t,e)}}o.instance=null;class r{constructor(s,t,e){this._inputGeometries=s,this._angleTolerance=void 0!==t.angleTolerance?t.angleTolerance:120,this._maxCosAngle=Math.cos((1-Math.abs(this._angleTolerance)/180)*Math.PI)}next(){let n=this._inputGeometries.next();for(;n;){if(t(n)){this._isClosed=!0;const t=s(n);return this._processMultipath(t.rings),t}if(e(n)){this._isClosed=!1;const t=s(n);return this._processMultipath(t.paths),t}if(i(n)){if(this._maxCosAngle)return n;this._isClosed=!0;const s=[[n.xmin,n.ymin],[n.xmin,n.ymax],[n.xmax,n.ymax],[n.xmax,n.ymin],[n.xmin,n.ymin]];return this._processPath(s),{rings:[s]}}n=this._inputGeometries.next()}return null}_processMultipath(s){if(s)for(const t of s)this._processPath(t)}_processPath(s){if(s){let t,e,i,o,r,l,a=s.length,h=s[0];this._isClosed&&++a;for(let c=1;c<a;++c){let m;m=this._isClosed&&c===a-1?s[0]:s[c];const _=m[0]-h[0],u=m[1]-h[1],p=Math.sqrt(_*_+u*u);if(c>1&&p>0&&i>0){(t*_+e*u)/p/i<=this._maxCosAngle&&n(h,1)}1===c&&(o=_,r=u,l=p),p>0&&(h=m,t=_,e=u,i=p)}if(this._isClosed&&i>0&&l>0){(t*o+e*r)/l/i<=this._maxCosAngle&&n(s[0],1)}}}}export{o as EffectAddControlPoints};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{GeometricEffectOffsetMethod as t}from\"./enums.js\";const e=.03;class s{constructor(){this._path=[]}path(){return this._path}addPath(t,e){e||t.reverse(),Array.prototype.push.apply(this._path,t),e||t.reverse()}static mergePath(t,e){e&&Array.prototype.push.apply(t,e)}startPath(t){this._path.push(t)}lineTo(t){this._path.push(t)}close(){const t=this._path;t.length>1&&(t[0][0]===t[t.length-1][0]&&t[0][1]===t[t.length-1][1]||t.push([t[0][0],t[0][1]]))}}class n{constructor(t=0,e=!1){}normalize(t){const e=Math.sqrt(t[0]*t[0]+t[1]*t[1]);0!==e&&(t[0]/=e,t[1]/=e)}calculateLength(t,e){const s=e[0]-t[0],n=e[1]-t[1];return Math.sqrt(s*s+n*n)}calculateSegLength(t,e){return this.calculateLength(t[e],t[e+1])}calculatePathLength(t){let e=0;const s=t?t.length:0;for(let n=0;n<s-1;++n)e+=this.calculateSegLength(t,n);return e}calculatePathArea(t){let e=0;const s=t?t.length:0;for(let n=0;n<s-1;++n)e+=(t[n+1][0]-t[n][0])*(t[n+1][1]+t[n][1]);return e/2}getCoord2D(t,e,s){return[t[0]+(e[0]-t[0])*s,t[1]+(e[1]-t[1])*s]}getSegCoord2D(t,e,s){return this.getCoord2D(t[e],t[e+1],s)}getAngle(t,e,s){const n=e[0]-t[0],r=e[1]-t[1];return Math.atan2(r,n)}getSegAngle(t,e,s){return this.getAngle(t[e],t[e+1],s)}getAngleCS(t,e,s){const n=e[0]-t[0],r=e[1]-t[1],h=Math.sqrt(n*n+r*r);return h>0?[n/h,r/h]:[1,0]}getSegAngleCS(t,e,s){return this.getAngleCS(t[e],t[e+1],s)}cut(t,e,s,n){return[s<=0?t[e]:this.getSegCoord2D(t,e,s),n>=1?t[e+1]:this.getSegCoord2D(t,e,n)]}addSegment(t,e,s){s&&t.push(e[0]),t.push(e[1])}getSubCurve(t,e,s){const n=[];return this.appendSubCurve(n,t,e,s)?n:null}appendSubCurve(t,e,s,n){const r=e?e.length-1:0;let h=0,l=!0,o=0;for(;o<r;){const r=this.calculateSegLength(e,o);if(0!==r){if(l){if(h+r>s){const a=(s-h)/r;let u=1,c=!1;h+r>=n&&(u=(n-h)/r,c=!0);const i=this.cut(e,o,a,u);if(i&&this.addSegment(t,i,l),c)break;l=!1}}else{if(h+r>n){const s=this.cut(e,o,0,(n-h)/r);s&&this.addSegment(t,s,l);break}this.addSegment(t,[e[o],e[o+1]],l)}h+=r,++o}else++o}return!0}getCIMPointAlong(t,e){const s=t?t.length-1:0;let n=0,r=-1;for(;r<s;){++r;const s=this.calculateSegLength(t,r);if(0!==s){if(n+s>e){const h=(e-n)/s;return this.getCoord2D(t[r],t[r+1],h)}n+=s}}return null}isEmpty(t,e){if(!t||t.length<=1)return!0;const s=t?t.length-1:0;let n=-1;for(;n<s;){if(++n,t[n+1][0]!==t[n][0]||t[n+1][1]!==t[n][1])return!1;if(e&&t[n+1][2]!==t[n][2])return!1}return!0}offset(e,s,n,r,h){if(!e||e.length<2)return null;let l=0,o=e[l++],a=l;for(;l<e.length;){const t=e[l];t[0]===o[0]&&t[1]===o[1]||(l!==a&&(e[a]=e[l]),o=e[a++]),l++}const u=e[0][0]===e[a-1][0]&&e[0][1]===e[a-1][1];if(u&&--a,a<(u?3:2))return null;const c=[];o=u?e[a-1]:null;let i=e[0];for(let g=0;g<a;g++){const h=g===a-1?u?e[0]:null:e[g+1];if(o)if(h){const e=[h[0]-i[0],h[1]-i[1]];this.normalize(e);const l=[i[0]-o[0],i[1]-o[1]];this.normalize(l);const a=l[0]*e[1]-l[1]*e[0],u=l[0]*e[0]+l[1]*e[1];if(0===a&&1===u){i=h;continue}if(a>=0==s<=0){if(u<1){const t=[e[0]-l[0],e[1]-l[1]];this.normalize(t);const n=Math.sqrt((1+u)/2);if(n>1/r){const e=-Math.abs(s)/n;c.push([i[0]-t[0]*e,i[1]-t[1]*e])}}}else switch(n){case t.Mitered:{const t=Math.sqrt((1+u)/2);if(t>0&&1/t<r){const n=[e[0]-l[0],e[1]-l[1]];this.normalize(n);const r=Math.abs(s)/t;c.push([i[0]-n[0]*r,i[1]-n[1]*r]);break}}case t.Bevelled:c.push([i[0]+l[1]*s,i[1]-l[0]*s]),c.push([i[0]+e[1]*s,i[1]-e[0]*s]);break;case t.Rounded:if(u<1){c.push([i[0]+l[1]*s,i[1]-l[0]*s]);const t=Math.floor(2.5*(1-u));if(t>0){const n=1/t;let r=n;for(let h=1;h<t;h++,r+=n){const t=[l[1]*(1-r)+e[1]*r,-l[0]*(1-r)-e[0]*r];this.normalize(t),c.push([i[0]+t[0]*s,i[1]+t[1]*s])}}c.push([i[0]+e[1]*s,i[1]-e[0]*s])}break;case t.Square:default:if(a<0)c.push([i[0]+(l[1]+l[0])*s,i[1]+(l[1]-l[0])*s]),c.push([i[0]+(e[1]-e[0])*s,i[1]-(e[0]+e[1])*s]);else{const t=Math.sqrt((1+Math.abs(u))/2),n=[e[0]-l[0],e[1]-l[1]];this.normalize(n);const r=s/t;c.push([i[0]-n[0]*r,i[1]-n[1]*r])}}}else{const t=[i[0]-o[0],i[1]-o[1]];this.normalize(t),c.push([i[0]+t[1]*s,i[1]-t[0]*s])}else{const t=[h[0]-i[0],h[1]-i[1]];this.normalize(t),c.push([i[0]+t[1]*s,i[1]-t[0]*s])}o=i,i=h}return c.length<(u?3:2)?null:(u&&c.push([c[0][0],c[0][1]]),c)}}export{n as CurveHelper,e as PIXEL_TOLERANCE,s as PathHelper};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{PathGeometryCursor as t,setId as e}from\"../CIMCursor.js\";import{CurveHelper as r,PIXEL_TOLERANCE as n,PathHelper as o}from\"../CurveHelper.js\";import{GeometricEffectArrowType as s,GeometricEffectOffsetMethod as i}from\"../enums.js\";const l=1.7320508075688772,c=5,u=s.OpenEnded;class h{static local(){return null===h.instance&&(h.instance=new h),h.instance}execute(t,e,r,n,o){return new a(t,e,r)}}h.instance=null;class a extends t{constructor(t,e,o){super(t,!1,!0),this._curveHelper=new r,this._width=(void 0!==e.width?e.width:c)*o,this._arrowType=void 0!==e.geometricEffectArrowType?e.geometricEffectArrowType:void 0!==e.arrowType?e.arrowType:u,this._offsetFlattenError=n*o}processPath(t){switch(this._arrowType){case s.OpenEnded:default:return this._constructSimpleArrow(t,!0);case s.Block:return this._constructSimpleArrow(t,!1);case s.Crossed:return this._constructCrossedArrow(t)}}_constructSimpleArrow(t,e){const r=this._curveHelper.calculatePathLength(t);let n=this._width;r<2*n&&(n=r/2);const s=this._curveHelper.getSubCurve(t,0,r-n);if(!s)return null;const i=n/2;if(this._curveHelper.isEmpty(s,!1))return null;const l=this._constructOffset(s,-i);if(!l)return null;const c=this._constructOffset(s,i);if(!c)return null;const u=this._constructArrowBasePoint(l,-i/2);if(!u)return null;const h=this._constructArrowBasePoint(c,i/2);if(!h)return null;const a=t[t.length-1];e||(this._makeControlPoint(c,!0),this._makeControlPoint(l,!0));const _=new o;return _.addPath(c,!0),_.lineTo(h),this._makeControlPoint(_.path()),_.lineTo(a),this._makeControlPoint(_.path()),_.lineTo(u),this._makeControlPoint(_.path()),_.addPath(l,!1),e?{paths:[_.path()]}:(_.close(),{rings:[_.path()]})}_constructCrossedArrow(t){const e=this._curveHelper.calculatePathLength(t);let r=this._width;e<r*(1+l+1)&&(r=e/(1+l+1));const n=this._curveHelper.getSubCurve(t,0,e-r*(1+l));if(!n)return null;const s=r/2;if(this._curveHelper.isEmpty(n,!1))return null;const i=this._constructOffset(n,s);if(!i)return null;const c=this._constructOffset(n,-s);if(!c)return null;const u=this._curveHelper.getSubCurve(t,0,e-r);if(!u)return null;if(this._curveHelper.isEmpty(u,!1))return null;const h=this._constructOffset(u,s);if(!h)return null;const a=this._constructOffset(u,-s);if(!a)return null;const _=h[h.length-1],f=this._constructArrowBasePoint(h,s/2);if(!f)return null;const p=a[a.length-1],m=this._constructArrowBasePoint(a,-s/2);if(!m)return null;const d=t[t.length-1];this._makeControlPoint(i,!1),this._makeControlPoint(c,!1);const w=new o;return w.addPath(i,!0),this._makeControlPoint(w.path()),w.lineTo(p),w.lineTo(m),this._makeControlPoint(w.path()),w.lineTo(d),this._makeControlPoint(w.path()),w.lineTo(f),this._makeControlPoint(w.path()),w.lineTo(_),this._makeControlPoint(w.path()),w.addPath(c,!1),{paths:[w.path()]}}_constructOffset(t,e){return this._curveHelper.offset(t,e,i.Rounded,4,this._offsetFlattenError)}_constructArrowBasePoint(t,e){if(!t||t.length<2)return null;const r=t[t.length-2],n=t[t.length-1],o=[n[0]-r[0],n[1]-r[1]];return this._curveHelper.normalize(o),[n[0]+o[1]*e,n[1]-o[0]*e]}_makeControlPoint(t,r=!1){e(r?t[0]:t[t.length-1],1)}}export{h as EffectArrow};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../geometry.js\";import{isNone as e}from\"../../../core/maybe.js\";import{isExtent as i,isPolygon as t,isPolyline as s}from\"../../../geometry/support/jsonUtils.js\";import{clipPolygonToTileExtent as r,clipPolylineToTileExtent as n}from\"../CIMEffects.js\";import{CurveHelper as m,PIXEL_TOLERANCE as o}from\"../CurveHelper.js\";import{GeometricEffectOffsetMethod as a}from\"../enums.js\";import h from\"../../../geometry/SpatialReference.js\";class l{static local(){return null===l.instance&&(l.instance=new l),l.instance}execute(e,i,t,s,r){return new f(e,i,t,s,r)}}l.instance=null;class f{constructor(e,i,t,s,r){this._inputGeometries=e,this._tileKey=s,this._geometryEngine=r,this._curveHelper=new m,this._size=(void 0!==i.size?i.size:1)*t,this._offsetFlattenError=o*t}next(){let m;for(;m=this._inputGeometries.next();){if(0===this._size)return m;if(i(m))if(this._size>0){const e=[[m.xmin,m.ymin],[m.xmin,m.ymax],[m.xmax,m.ymax],[m.xmax,m.ymin],[m.xmin,m.ymin]],i=this._curveHelper.offset(e,this._size,a.Rounded,4,this._offsetFlattenError);if(i)return{rings:[i]}}else if(this._size<0&&Math.min(m.xmax-m.xmin,m.ymax-m.ymin)+2*this._size>0)return{xmin:m.xmin-this._size,xmax:m.xmax+this._size,ymin:m.ymin-this._size,ymax:m.ymax+this._size};const o=this._geometryEngine;if(e(o))return null;let l=m;if((!t(m)||!this._tileKey||(l=r(m,Math.abs(this._size)+1),l&&l.rings&&0!==l.rings.length))&&(!s(m)||!this._tileKey||(l=n(m,Math.abs(this._size)+1),l&&l.paths&&0!==l.paths.length)))return o.buffer(h.WebMercator,l,this._size,1)}return null}}export{l as EffectBuffer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isPoint as s,isMultipoint as t,isPolyline as i,isPolygon as h}from\"../../../geometry/support/jsonUtils.js\";import{setId as e}from\"../CIMCursor.js\";import{GeometricEffectControlMeasureLineRule as r}from\"../enums.js\";class _{static local(){return null===_.instance&&(_.instance=new _),_.instance}execute(s,t,i,h,e){return new c(s,t,i)}}_.instance=null;class c{constructor(s,t,i){this._defaultPointSize=20,this._inputGeometries=s,this._geomUnitsPerPoint=i,this._rule=t.rule??r.FullGeometry,this._defaultSize=this._defaultPointSize*i}next(){let e;for(;e=this._inputGeometries.next();){let r;if(s(e)?r=this._processGeom([[[e.x,e.y]]]):t(e)?r=this._processGeom([e.points]):i(e)?r=this._processGeom(e.paths):h(e)&&(r=this._processGeom(e.rings)),r&&r.length)return{paths:r}}return null}_clone(s){return[s[0],s[1]]}_mid(s,t){return[(s[0]+t[0])/2,(s[1]+t[1])/2]}_mix(s,t,i,h){return[s[0]*t+i[0]*h,s[1]*t+i[1]*h]}_add(s,t){return[s[0]+t[0],s[1]+t[1]]}_add2(s,t,i){return[s[0]+t,s[1]+i]}_sub(s,t){return[s[0]-t[0],s[1]-t[1]]}_dist(s,t){return Math.sqrt((s[0]-t[0])*(s[0]-t[0])+(s[1]-t[1])*(s[1]-t[1]))}_norm(s){return Math.sqrt(s[0]*s[0]+s[1]*s[1])}_normalize(s,t=1){const i=t/this._norm(s);s[0]*=i,s[1]*=i}_leftPerpendicular(s){const t=-s[1],i=s[0];s[0]=t,s[1]=i}_leftPerp(s){return[-s[1],s[0]]}_rightPerpendicular(s){const t=s[1],i=-s[0];s[0]=t,s[1]=i}_rightPerp(s){return[s[1],-s[0]]}_dotProduct(s,t){return s[0]*t[0]+s[1]*t[1]}_crossProduct(s,t){return s[0]*t[1]-s[1]*t[0]}_rotateDirect(s,t,i){const h=s[0]*t-s[1]*i,e=s[0]*i+s[1]*t;s[0]=h,s[1]=e}_makeCtrlPt(s){const t=[s[0],s[1]];return e(t,1),t}_addAngledTicks(s,t,i,h){const e=this._sub(i,t);this._normalize(e);const r=this._crossProduct(e,this._sub(h,t));let _;_=r>0?this._rightPerp(e):this._leftPerp(e);const c=Math.abs(r)/2,u=[];u.push([t[0]+(_[0]-e[0])*c,t[1]+(_[1]-e[1])*c]),u.push(t),u.push(i),u.push([i[0]+(_[0]+e[0])*c,i[1]+(_[1]+e[1])*c]),s.push(u)}_addBezier2(s,t,i,h,e){if(0==e--)return void s.push(h);const r=this._mid(t,i),_=this._mid(i,h),c=this._mid(r,_);this._addBezier2(s,t,r,c,e),this._addBezier2(s,c,_,h,e)}_addBezier3(s,t,i,h,e,r){if(0==r--)return void s.push(e);const _=this._mid(t,i),c=this._mid(i,h),u=this._mid(h,e),o=this._mid(_,c),n=this._mid(c,u),a=this._mid(o,n);this._addBezier3(s,t,_,o,a,r),this._addBezier3(s,a,n,u,e,r)}_add90DegArc(s,t,i,h,e){const r=e??this._crossProduct(this._sub(i,t),this._sub(h,t))>0,_=this._mid(t,i),c=this._sub(_,t);r?this._leftPerpendicular(c):this._rightPerpendicular(c),_[0]+=c[0],_[1]+=c[1],this._addBezier3(s,t,this._mix(t,.33333,_,.66667),this._mix(i,.33333,_,.66667),i,4)}_addArrow(s,t,i){const h=t[0],e=t[1],r=t[t.length-1],_=this._sub(h,e);this._normalize(_);const c=this._crossProduct(_,this._sub(r,e)),u=.5*c,o=this._leftPerp(_),n=[r[0]-o[0]*c,r[1]-o[1]*c],a=t.length-1,p=[];p.push(i?[-o[0],-o[1]]:o);let l=[-_[0],-_[1]];for(let d=1;d<a-1;d++){const s=this._sub(t[d+1],t[d]);this._normalize(s);const i=this._dotProduct(s,l),h=this._crossProduct(s,l),e=Math.sqrt((1+i)/2),r=this._sub(s,l);this._normalize(r),r[0]/=e,r[1]/=e,p.push(h<0?[-r[0],-r[1]]:r),l=s}p.push(this._rightPerp(l));for(let d=p.length-1;d>0;d--)s.push([t[d][0]+p[d][0]*u,t[d][1]+p[d][1]*u]);s.push([n[0]+p[0][0]*u,n[1]+p[0][1]*u]),s.push([n[0]+p[0][0]*c,n[1]+p[0][1]*c]),s.push(h),s.push([n[0]-p[0][0]*c,n[1]-p[0][1]*c]),s.push([n[0]-p[0][0]*u,n[1]-p[0][1]*u]);for(let d=1;d<p.length;d++)s.push([t[d][0]-p[d][0]*u,t[d][1]-p[d][1]*u])}_cp2(s,t,i){return s.length>=2?s[1]:this._add2(s[0],t*this._defaultSize,i*this._defaultSize)}_cp3(s,t,i,h){if(s.length>=3)return s[2];const e=this._mix(s[0],1-i,t,i),r=this._sub(t,s[0]);return this._normalize(r),this._rightPerpendicular(r),[e[0]+r[0]*h*this._defaultSize,e[1]+r[1]*h*this._defaultSize]}_arrowPath(s){if(s.length>2)return s;const t=s[0],i=this._cp2(s,-4,0),h=this._sub(t,i);this._normalize(h);const e=this._rightPerp(h);return[t,i,[t[0]+(e[0]-h[0])*this._defaultSize,t[1]+(e[1]-h[1])*this._defaultSize]]}_arrowLastSeg(s){const t=s[0],i=this._cp2(s,-4,0);let h;if(s.length>=3)h=s[s.length-1];else{const s=this._sub(t,i);this._normalize(s);const e=this._rightPerp(s);h=[t[0]+(e[0]-s[0])*this._defaultSize,t[1]+(e[1]-s[1])*this._defaultSize]}return[i,h]}_processGeom(s){if(!s)return null;const t=[];for(const i of s){if(!i||0===i.length)continue;const s=i.length;let h=i[0];switch(this._rule){case r.PerpendicularFromFirstSegment:{const s=this._cp2(i,0,-1),e=this._cp3(i,s,.5,4),r=[];r.push(e),r.push(this._mid(h,s)),t.push(r);break}case r.ReversedFirstSegment:{const s=this._cp2(i,0,-1);t.push([s,h]);break}case r.PerpendicularToSecondSegment:{const s=this._cp2(i,-4,1),e=this._cp3(i,s,.882353,-1.94),r=[];r.push(this._mid(s,e)),r.push(h),t.push(r);break}case r.SecondSegmentWithTicks:{const s=this._cp2(i,-4,1),e=this._cp3(i,s,.882353,-1.94),r=this._sub(e,s);let _;_=this._crossProduct(r,this._sub(h,s))>0?this._rightPerp(_):this._leftPerp(r);const c=[];c.push([s[0]+(_[0]-r[0])/3,s[1]+(_[1]-r[1])/3]),c.push(s),c.push(e),c.push([e[0]+(_[0]+r[0])/3,e[1]+(_[1]+r[1])/3]),t.push(c);break}case r.DoublePerpendicular:{const s=this._cp2(i,0,-1),e=this._cp3(i,s,.5,3),r=this._mid(h,s),_=this._sub(r,e);this._normalize(_);const c=this._crossProduct(_,this._sub(h,e));this._leftPerpendicular(_);const u=[];u.push(h),u.push([e[0]+_[0]*c,e[1]+_[1]*c]),t.push(u);const o=[];o.push([e[0]-_[0]*c,e[1]-_[1]*c]),o.push(s),t.push(o);break}case r.OppositeToFirstSegment:{const s=this._cp2(i,0,-1),e=this._cp3(i,s,.5,3),r=this._mid(h,s),_=this._sub(r,e);this._normalize(_);const c=this._crossProduct(_,this._sub(h,e));this._leftPerpendicular(_);const u=[];u.push([e[0]+_[0]*c,e[1]+_[1]*c]),u.push([e[0]-_[0]*c,e[1]-_[1]*c]),t.push(u);break}case r.TriplePerpendicular:{const s=this._cp2(i,0,-1),e=this._cp3(i,s,.5,4),r=this._mid(h,s),_=this._sub(r,e);this._normalize(_);const c=this._crossProduct(_,this._sub(h,e));this._leftPerpendicular(_);const u=[];u.push([e[0]+_[0]*c*.8,e[1]+_[1]*c*.8]),u.push([r[0]+.8*(h[0]-r[0]),r[1]+.8*(h[1]-r[1])]),t.push(u),t.push([e,r]);const o=[];o.push([e[0]-_[0]*c*.8,e[1]-_[1]*c*.8]),o.push([r[0]+.8*(s[0]-r[0]),r[1]+.8*(s[1]-r[1])]),t.push(o);break}case r.HalfCircleFirstSegment:{const s=this._cp2(i,0,-1),e=this._cp3(i,s,.5,4),r=this._mid(h,s);let _=this._sub(s,h);const c=Math.cos(Math.PI/18),u=Math.sin(Math.PI/18),o=Math.sqrt((1+c)/2),n=Math.sqrt((1-c)/2),a=[];let p;this._crossProduct(_,this._sub(e,h))>0?(a.push(h),_=this._sub(h,r),p=s):(a.push(s),_=this._sub(s,r),p=h),this._rotateDirect(_,o,n),_[0]/=o,_[1]/=o;for(let t=1;t<=18;t++)a.push(this._add(r,_)),this._rotateDirect(_,c,u);a.push(p),t.push(a);break}case r.HalfCircleSecondSegment:{const s=this._cp2(i,0,-1),e=this._cp3(i,s,1,-1);let r=this._sub(h,s);this._normalize(r);const _=this._crossProduct(r,this._sub(e,s))/2;this._leftPerpendicular(r);const c=[s[0]+r[0]*_,s[1]+r[1]*_];r=this._sub(s,c);const u=Math.cos(Math.PI/18);let o=Math.sin(Math.PI/18);_>0&&(o=-o);const n=[s];for(let t=1;t<=18;t++)this._rotateDirect(r,u,o),n.push(this._add(c,r));t.push(n);break}case r.HalfCircleExtended:{const e=this._cp2(i,0,-2),r=this._cp3(i,e,1,-1);let _;if(s>=4)_=i[3];else{const s=this._sub(h,e);_=this._add(r,s)}const c=this._dist(e,r)/2/.75,u=this._sub(e,h);this._normalize(u,c);const o=this._sub(r,_);this._normalize(o,c);const n=[_,r];t.push(n);const a=[this._clone(r)];this._addBezier3(a,r,this._add(r,o),this._add(e,u),e,4),a.push(h),t.push(a);break}case r.OpenCircle:{const s=this._cp2(i,-2,0),e=this._sub(s,h),r=Math.cos(Math.PI/18),_=-Math.sin(Math.PI/18),c=[s];for(let t=1;t<=33;t++)this._rotateDirect(e,r,_),c.push(this._add(h,e));t.push(c);break}case r.CoverageEdgesWithTicks:{const e=this._cp2(i,0,-1);let r,_;if(s>=3)r=i[2];else{const s=this._sub(e,h),t=this._leftPerp(s);r=[h[0]+t[0]-.25*s[0],h[1]+t[1]-.25*s[1]]}if(s>=4)_=i[3];else{const s=this._mid(h,e),t=this._sub(h,e);this._normalize(t),this._leftPerpendicular(t);const i=this._crossProduct(t,this._sub(r,s));this._rightPerpendicular(t),_=[r[0]+t[0]*i*2,r[1]+t[1]*i*2]}const c=this._sub(e,h);let u,o;u=this._crossProduct(c,this._sub(r,h))>0?this._rightPerp(c):this._leftPerp(c),o=[],o.push(r),o.push(h),o.push([h[0]+(u[0]-c[0])/3,h[1]+(u[1]-c[1])/3]),t.push(o),u=this._crossProduct(c,this._sub(_,e))>0?this._rightPerp(u):this._leftPerp(c),o=[],o.push([e[0]+(u[0]+c[0])/3,e[1]+(u[1]+c[1])/3]),o.push(e),o.push(_),t.push(o);break}case r.GapExtentWithDoubleTicks:{const e=this._cp2(i,0,2),r=this._cp3(i,e,0,1);let _;if(s>=4)_=i[3];else{const s=this._sub(e,h);_=this._add(r,s)}this._addAngledTicks(t,h,e,this._mid(r,_)),this._addAngledTicks(t,r,_,this._mid(h,e));break}case r.GapExtentMidline:{const e=this._cp2(i,2,0),r=this._cp3(i,e,0,1);let _;if(s>=4)_=i[3];else{const s=this._sub(e,h);_=this._add(r,s)}const c=[];c.push(this._mid(h,r)),c.push(this._mid(e,_)),t.push(c);break}case r.Chevron:{const e=this._cp2(i,-1,-1);let r;if(s>=3)r=i[2];else{const s=this._sub(e,h);this._leftPerpendicular(s),r=this._add(h,s)}t.push([e,this._makeCtrlPt(h),r]);break}case r.PerpendicularWithArc:{const s=this._cp2(i,0,-2),e=this._cp3(i,s,.5,-1);let r=this._sub(s,h);const _=this._norm(r);r[0]/=_,r[1]/=_;const c=this._crossProduct(r,this._sub(e,h));let u=this._dotProduct(r,this._sub(e,h));u<.05*_?u=.05*_:u>.95*_&&(u=.95*_);const o=[h[0]+r[0]*u,h[1]+r[1]*u];this._leftPerpendicular(r);let n=[];n.push([o[0]-r[0]*c,o[1]-r[1]*c]),n.push([o[0]+r[0]*c,o[1]+r[1]*c]),t.push(n);const a=[s[0]+r[0]*c,s[1]+r[1]*c];r=this._sub(s,a);const p=Math.cos(Math.PI/18);let l=Math.sin(Math.PI/18);c<0&&(l=-l),n=[h,s];for(let t=1;t<=9;t++)this._rotateDirect(r,p,l),n.push(this._add(a,r));t.push(n);break}case r.ClosedHalfCircle:{const s=this._cp2(i,2,0),e=this._mid(h,s),r=this._sub(s,e),_=Math.cos(Math.PI/18),c=Math.sin(Math.PI/18),u=[h,s];for(let t=1;t<=18;t++)this._rotateDirect(r,_,c),u.push(this._add(e,r));t.push(u);break}case r.TripleParallelExtended:{const s=this._cp2(i,0,-2),r=this._cp3(i,s,1,-2),_=this._mid(h,s),c=this._sub(r,s);this._normalize(c);const u=Math.abs(this._crossProduct(c,this._sub(_,s)))/2,o=this._dist(s,r),n=[s,h];n.push([h[0]+c[0]*o*.5,h[1]+c[1]*o*.5]),t.push(n);const a=[];a.push([_[0]-c[0]*u,_[1]-c[1]*u]),a.push([_[0]+c[0]*o*.375,_[1]+c[1]*o*.375]),e(a[a.length-1],1),a.push([_[0]+c[0]*o*.75,_[1]+c[1]*o*.75]),t.push(a);const p=[s,r];t.push(p);break}case r.ParallelWithTicks:{const s=this._cp2(i,3,0),e=this._cp3(i,s,.5,-1),r=this._sub(e,s);this._normalize(r);const _=this._crossProduct(r,this._sub(e,h));this._leftPerpendicular(r),this._addAngledTicks(t,h,s,e),this._addAngledTicks(t,this._mix(h,1,r,_),this._mix(s,1,r,_),this._mid(h,s));break}case r.Parallel:{const s=this._cp2(i,3,0),e=this._cp3(i,s,.5,-1),r=this._sub(s,h);this._normalize(r);const _=this._leftPerp(r),c=this._crossProduct(r,this._sub(e,h));let u=[h,s];t.push(u),u=[],u.push([h[0]+_[0]*c,h[1]+_[1]*c]),u.push([s[0]+_[0]*c,s[1]+_[1]*c]),t.push(u);break}case r.PerpendicularToFirstSegment:{const s=this._cp2(i,3,0),e=this._cp3(i,s,.5,-1),r=this._mid(h,s),_=this._sub(s,h);this._normalize(_);const c=this._crossProduct(_,this._sub(e,h));this._leftPerpendicular(_);const u=[];u.push([r[0]-_[0]*c*.25,r[1]-_[1]*c*.25]),u.push([r[0]+_[0]*c*1.25,r[1]+_[1]*c*1.25]),t.push(u);break}case r.ParallelOffset:{const s=this._cp2(i,3,0),e=this._cp3(i,s,.5,-1),r=this._sub(s,h);this._normalize(r);const _=this._crossProduct(r,this._sub(e,h));this._leftPerpendicular(r);const c=[];c.push([h[0]-r[0]*_,h[1]-r[1]*_]),c.push([s[0]-r[0]*_,s[1]-r[1]*_]),t.push(c);const u=[];u.push([h[0]+r[0]*_,h[1]+r[1]*_]),u.push([s[0]+r[0]*_,s[1]+r[1]*_]),t.push(u);break}case r.OffsetOpposite:{const s=this._cp2(i,3,0),e=this._cp3(i,s,.5,-1),r=this._sub(s,h);this._normalize(r);const _=this._crossProduct(r,this._sub(e,h));this._leftPerpendicular(r);const c=[];c.push([h[0]-r[0]*_,h[1]-r[1]*_]),c.push([s[0]-r[0]*_,s[1]-r[1]*_]),t.push(c);break}case r.OffsetSame:{const s=this._cp2(i,3,0),e=this._cp3(i,s,.5,-1),r=this._sub(s,h);this._normalize(r);const _=this._crossProduct(r,this._sub(e,h));this._leftPerpendicular(r);const c=[];c.push([h[0]+r[0]*_,h[1]+r[1]*_]),c.push([s[0]+r[0]*_,s[1]+r[1]*_]),t.push(c);break}case r.CircleWithArc:{let r=this._cp2(i,3,0);const _=this._cp3(i,r,.5,-1);let c,u;if(s>=4)c=i[3],u=this._crossProduct(this._sub(c,r),this._sub(_,r))>0;else{c=r,u=this._crossProduct(this._sub(c,h),this._sub(_,h))>0;const s=24*this._geomUnitsPerPoint,t=this._sub(c,h);this._normalize(t,s);const i=Math.sqrt(2)/2;this._rotateDirect(t,i,u?i:-i),r=this._add(h,t)}const o=this._sub(r,h),n=Math.cos(Math.PI/18),a=Math.sin(Math.PI/18),p=[r];for(let s=1;s<=36;s++)this._rotateDirect(o,n,a),p.push(this._add(h,o));this._add90DegArc(p,r,c,_,u),e(p[p.length-8],1),t.push(p);break}case r.DoubleJog:{let e,r,_=this._cp2(i,-3,1);if(e=s>=3?i[2]:this._add(h,this._sub(h,_)),s>=4)r=i[3];else{const s=h;h=_,r=e;const t=this._dist(h,s),i=this._dist(r,s);let c=30*this._geomUnitsPerPoint;.5*t<c&&(c=.5*t),.5*i<c&&(c=.5*i),_=this._mix(h,c/t,s,(t-c)/t),e=this._mix(r,c/i,s,(i-c)/i)}const c=this._mid(h,_),u=this._mid(r,e),o=this._dist(h,_),n=this._dist(e,r);let a=Math.min(o,n)/8;a=Math.min(a,24*this._geomUnitsPerPoint);const p=Math.cos(Math.PI/4);let l=this._sub(h,_);this._normalize(l,a),this._crossProduct(l,this._sub(r,_))>0?this._rotateDirect(l,p,-p):this._rotateDirect(l,p,p);let d=[];d.push(_),d.push(this._add(c,l)),d.push(this._sub(c,l)),d.push(h),t.push(d),l=this._sub(r,e),this._normalize(l,a),this._crossProduct(l,this._sub(h,e))<0?this._rotateDirect(l,p,p):this._rotateDirect(l,p,-p),d=[],d.push(e),d.push(this._add(u,l)),d.push(this._sub(u,l)),d.push(r),t.push(d);break}case r.PerpendicularOffset:{const s=this._cp2(i,-4,1),e=this._cp3(i,s,.882353,-1.94),r=this._sub(e,s);this._crossProduct(r,this._sub(h,s))>0?this._rightPerpendicular(r):this._leftPerpendicular(r);const _=[r[0]/8,r[1]/8],c=this._sub(this._mid(s,e),_);t.push([c,h]);break}case r.LineExcludingLastSegment:{const s=this._arrowPath(i),h=[];let e=s.length-2;for(;e--;)h.push(s[e]);t.push(h);break}case r.MultivertexArrow:{const s=this._arrowPath(i),h=[];this._addArrow(h,s,!1),t.push(h);break}case r.CrossedArrow:{const s=this._arrowPath(i),h=[];this._addArrow(h,s,!0),t.push(h);break}case r.ChevronArrow:{const[s,e]=this._arrowLastSeg(i),r=10*this._geomUnitsPerPoint,_=this._sub(h,s);this._normalize(_);const c=this._crossProduct(_,this._sub(e,s)),u=this._leftPerp(_),o=[e[0]-u[0]*c*2,e[1]-u[1]*c*2],n=[];n.push([e[0]+_[0]*r,e[1]+_[1]*r]),n.push(h),n.push([o[0]+_[0]*r,o[1]+_[1]*r]),t.push(n);break}case r.ChevronArrowOffset:{const[s,e]=this._arrowLastSeg(i),r=this._sub(h,s);this._normalize(r);const _=this._crossProduct(r,this._sub(e,s));this._leftPerpendicular(r);const c=[e[0]-r[0]*_,e[1]-r[1]*_],u=[];u.push([c[0]+r[0]*_*.5,c[1]+r[1]*_*.5]),u.push(this._mid(c,h)),u.push([c[0]-r[0]*_*.5,c[1]-r[1]*_*.5]),t.push(u);break}case r.PartialFirstSegment:{const[s,e]=this._arrowLastSeg(i),r=this._sub(h,s);this._normalize(r);const _=this._crossProduct(r,this._sub(e,s));this._leftPerpendicular(r);const c=[e[0]-r[0]*_,e[1]-r[1]*_];t.push([s,c]);break}case r.Arch:{const s=this._cp2(i,0,-1),e=this._cp3(i,s,.5,1),r=this._sub(h,s),_=this._mix(e,1,r,.55),c=this._mix(e,1,r,-.55),u=[h];this._addBezier2(u,h,_,e,4),this._addBezier2(u,e,c,s,4),t.push(u);break}case r.CurvedParallelTicks:{const s=this._cp2(i,-4,1),e=this._cp3(i,s,.882353,-1.94),r=this._sub(e,s);this._crossProduct(r,this._sub(h,s))>0?this._rightPerpendicular(r):this._leftPerpendicular(r);const _=[r[0]/8,r[1]/8],c=this._sub(this._mid(s,e),_),u=this._sub(this._mix(s,.75,e,.25),_),o=this._sub(this._mix(s,.25,e,.75),_),n=[s];this._addBezier2(n,s,u,c,3),this._addBezier2(n,c,o,e,3),t.push(n);for(let i=0;i<8;i++){const s=n[2*i+1],h=[this._clone(s)];h.push(this._add(s,[r[0]/4,r[1]/4])),t.push(h)}break}case r.Arc90Degrees:{const s=this._cp2(i,0,-1),e=this._cp3(i,s,.5,1),r=[s];this._add90DegArc(r,s,h,e),t.push(r);break}case r.FullGeometry:default:t.push(i)}}return t}}export{_ as EffectControlMeasureLine};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{PathGeometryCursor as e}from\"../CIMCursor.js\";import{CurveHelper as t}from\"../CurveHelper.js\";class u{static local(){return null===u.instance&&(u.instance=new u),u.instance}execute(e,t,u,i,r){return new s(e,t,u)}}u.instance=null;class s extends e{constructor(e,u,s){super(e,!0,!0),this._curveHelper=new t,this._beginCut=(void 0!==u.beginCut?u.beginCut:1)*s,this._endCut=(void 0!==u.endCut?u.endCut:1)*s,this._middleCut=(void 0!==u.middleCut?u.middleCut:0)*s,this._invert=void 0!==u.invert&&u.invert,this._beginCut<0&&(this._beginCut=0),this._endCut<0&&(this._endCut=0),this._middleCut<0&&(this._middleCut=0)}processPath(e){const t=this._beginCut,u=this._endCut,s=this._middleCut,i=this._curveHelper.calculatePathLength(e),r=[];if(this._invert)if(0===t&&0===u&&0===s);else if(t+u+s>=i)r.push(e);else{let n=this._curveHelper.getSubCurve(e,0,t);n&&r.push(n),n=this._curveHelper.getSubCurve(e,.5*(i-s),.5*(i+s)),n&&r.push(n),n=this._curveHelper.getSubCurve(e,i-u,u),n&&r.push(n)}else if(0===t&&0===u&&0===s)r.push(e);else if(t+u+s>=i);else if(0===s){const s=this._curveHelper.getSubCurve(e,t,i-u);s&&r.push(s)}else{let n=this._curveHelper.getSubCurve(e,t,.5*(i-s));n&&r.push(n),n=this._curveHelper.getSubCurve(e,.5*(i+s),i-u),n&&r.push(n)}return 0===r.length?null:{paths:r}}}export{u as EffectCut};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getId as t}from\"./CIMCursor.js\";import{CurveHelper as s,PIXEL_TOLERANCE as e}from\"./CurveHelper.js\";const i=1e-7;class n{constructor(){this._values=[],this.extPtGap=0,this.ctrlPtGap=0,this._length=0,this._currentValue=0}isEmpty(){return 0===this._values.length}size(){return this._values.length}init(t,s,e=!0){if(this._setEmpty(),!t||0===t.length)return!1;for(let n=0;n<t.length;n++){let s=Math.abs(t[n]);e&&s<i&&(s=i),this._values.push(s),this._length+=s}return s&&1&t.length&&(this._length*=2),0!==this._length&&(this.ctrlPtGap=this.extPtGap=0,this._currentValue=-1,!0)}scale(t){const s=this._values?this._values.length:0;for(let e=0;e<s;++e)this._values[e]*=t;this._length*=t,this.extPtGap*=t,this.ctrlPtGap*=t}addValue(t){this._length+=t,this._values.push(t)}firstValue(){return this._values[0]}lastValue(){return this._values[this._values.length-1]}nextValue(){return this._currentValue++,this._currentValue===this._values.length&&(this._currentValue=0),this._values[this._currentValue]}reset(){this._currentValue=-1}length(){return this._length}_setEmpty(){this.extPtGap=this.ctrlPtGap=this._length=0,this._currentValue=-1,this._values.length=0}}class h{constructor(){this.pt=null,this.ca=0,this.sa=0}}var r;!function(t){t[t.FAIL=0]=\"FAIL\",t[t.END=1]=\"END\",t[t.CONTINUE=2]=\"CONTINUE\"}(r||(r={}));class a{constructor(){this.reset()}reset(){this.segment=-1,this.segmentLength=0,this.abscissa=0,this.isPathEnd=!1,this.isPartEnd=!1}isValid(){return-1!==this.segment}copyTo(t){t.segment=this.segment,t.segmentLength=this.segmentLength,t.abscissa=this.abscissa,t.isPathEnd=this.isPathEnd,t.isPartEnd=this.isPartEnd}}class g extends s{constructor(t=0,s=!1){super(t,s),this._tolerance=e,this._currentPosition=new a}updateTolerance(t){this._tolerance=e*t}init(t,s,e=!0){return e?(this._patternLength=s.length(),this._partExtPtGap=s.extPtGap,this._partCtrlPtGap=s.ctrlPtGap):(this._patternLength=0,this._partExtPtGap=0,this._partCtrlPtGap=0),this._currentPosition.reset(),this._partSegCount=0,this._path=t,this._seg=-1,this._setPosAtNextPart()}curPositionIsValid(){return this._currentPosition.isValid()}nextPosition(t,s=r.FAIL){const e=new a;return!!this._nextPosition(t,e,null,s)&&(e.copyTo(this._currentPosition),!0)}curPointAndAngle(t){t.pt=this._getPoint(this._currentPosition);const[s,e]=this._getAngle(this._currentPosition);t.ca=s,t.sa=e}nextPointAndAngle(t,s,e=r.FAIL){const i=new a;if(!this._nextPosition(t,i,null,e))return!1;i.copyTo(this._currentPosition),s.pt=this._getPoint(i);const[n,h]=this._getAngle(i);return s.ca=n,s.sa=h,!0}nextCurve(t){if(0===t)return null;const s=[],e=new a;return this._nextPosition(t,e,s,r.END)?(e.copyTo(this._currentPosition),s):null}isPathEnd(){return this._currentPosition.isPathEnd}getPathEnd(){if(-1===this._currentPosition.segment)throw new Error(\"missing segment\");return this._path[this._currentPosition.segment+1]}_nextPosition(t,s,e,i){if(this._currentPosition.isPathEnd)return!1;let n=this._currentPosition.abscissa;for(this._currentPosition.segmentLength>0&&(n/=this._currentPosition.segmentLength),this._currentPosition.copyTo(s);s.abscissa+t*this._partLengthRatio>s.segmentLength+this._tolerance;){if(e){if(0===e.length)if(0===n){const t=this._path[s.segment];e.push([t[0],t[1]])}else e.push(this.getSegCoord2D(this._path,s.segment,n));const t=this._path[s.segment+1];e.push([t[0],t[1]])}if(n=0,t-=(s.segmentLength-s.abscissa)/this._partLengthRatio,this._partSegCount)s.segment=this._nextSegment(),s.segmentLength=this.calculateSegLength(this._path,s.segment),s.abscissa=0,this._partSegCount--;else{if(!this._setPosAtNextPart())return i!==r.FAIL&&(s.segmentLength=this.calculateSegLength(this._path,s.segment),s.isPartEnd=!0,i===r.END?(s.abscissa=s.segmentLength,s.isPathEnd=!0):s.abscissa=s.segmentLength+t,!0);this._currentPosition.copyTo(s)}}if(s.abscissa+=t*this._partLengthRatio,e){if(0===e.length)if(0===n){const t=this._path[s.segment];e.push([t[0],t[1]])}else e.push(this.getSegCoord2D(this._path,s.segment,n));const t=s.abscissa/s.segmentLength;if(1===t){const t=this._path[s.segment+1];e.push([t[0],t[1]])}else e.push(this.getSegCoord2D(this._path,s.segment,t))}return this._partSegCount||Math.abs(s.abscissa-s.segmentLength)<this._tolerance&&(s.isPathEnd=this._partIsLast,s.isPartEnd=!0),!0}_getPoint(t){if(-1===t.segment)throw new Error(\"missing segment\");const s=t.segmentLength<=0?0:t.abscissa/t.segmentLength;return this.getSegCoord2D(this._path,t.segment,s)}_getAngle(t){if(-1===t.segment)throw new Error(\"missing segment\");const s=t.segmentLength<=0?0:t.abscissa/t.segmentLength;return this.getSegAngleCS(this._path,t.segment,s)}_setPosAtNextPart(){for(;this._partSegCount;)this._hasNextSegment()&&this._nextSegment(),this._partSegCount--;if(!this._hasNextSegment())return!1;for(this._partLength=0,this._partIsLast=!0,this._partSegCount=0;this._hasNextSegment();)if(this._partLength+=this.calculateSegLength(this._path,this._nextSegment()),this._partSegCount++,1===t(this._path[this._getEndPointIndex()])){this._partIsLast=!this._hasNextSegment();break}let s=this._partSegCount;for(;s;)this._previousSegment(),--s;this._currentPosition.segment=this._nextSegment(),this._currentPosition.segmentLength=this.calculateSegLength(this._path,this._currentPosition.segment),this._currentPosition.abscissa=0,this._currentPosition.isPathEnd=this._currentPosition.isPartEnd=!1,--this._partSegCount;const e=this._getStartPointIndex();this._ctrlPtBegin=1===t(this._path[e]);let i=e+this._partSegCount+1;if(i>=this._path.length&&(i=0),this._ctrlPtEnd=1===t(this._path[i]),this._patternLength>0){const t=this._ctrlPtBegin?this._partCtrlPtGap:this._partExtPtGap,s=this._ctrlPtEnd?this._partCtrlPtGap:this._partExtPtGap;let e=Math.round((this._partLength-(t+s))/this._patternLength);e<=0&&(e=t+s>0?0:1),this._partLengthRatio=this._partLength/(t+s+e*this._patternLength),this._partLengthRatio<.01&&(this._partLengthRatio=1)}else this._partLengthRatio=1;return!0}_hasNextSegment(){return this._seg<this._path.length-2}_previousSegment(){return--this._seg}_nextSegment(){return++this._seg}_getStartPointIndex(){return this._seg}_getEndPointIndex(){return this._seg+1}}export{n as DashPattern,r as EndType,g as GeometryWalker,h as Pos};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{PathGeometryCursor as t}from\"../CIMCursor.js\";import{PathHelper as e}from\"../CurveHelper.js\";import{LineDashEnding as s}from\"../enums.js\";import{GeometryWalker as a,DashPattern as i}from\"../GeometryWalker.js\";class r{static local(){return null===r.instance&&(r.instance=new r),r.instance}execute(t,e,s,a,i){return new n(t,e,s)}}r.instance=null;class n extends t{constructor(t,e,s){super(t,!0,!0),this._firstCurve=null,this._walker=new a,this._walker.updateTolerance(s),this._endings=e.lineDashEnding,this._customDashPos=-(e.offsetAlongLine??0)*s,this._offsetAtEnd=(e.customEndingOffset??0)*s,this._pattern=new i,this._pattern.init(e.dashTemplate,!0),this._pattern.scale(s)}processPath(t){if(0===this._pattern.length())return this.iteratePath=!1,{paths:[t]};if(!this.iteratePath){let e=!0;switch(this._endings){case s.HalfPattern:case s.HalfGap:default:this._pattern.extPtGap=0;break;case s.FullPattern:this.isClosed||(this._pattern.extPtGap=.5*this._pattern.firstValue());break;case s.FullGap:this.isClosed||(this._pattern.extPtGap=.5*this._pattern.lastValue());break;case s.NoConstraint:this.isClosed||(e=!1);break;case s.Custom:this.isClosed||(this._pattern.extPtGap=.5*this._offsetAtEnd)}const a=this._walker.calculatePathLength(t);if(this._pattern.isEmpty()||a<.1*this._pattern.length())return{paths:[t]};if(!this._walker.init(t,this._pattern,e))return{paths:[t]}}let a;if(this.iteratePath)a=this._pattern.nextValue();else{let t;switch(this._endings){case s.HalfPattern:default:t=.5*this._pattern.firstValue();break;case s.HalfGap:t=.5*-this._pattern.lastValue();break;case s.FullGap:t=-this._pattern.lastValue();break;case s.FullPattern:t=0;break;case s.NoConstraint:case s.Custom:t=-this._customDashPos}let e=t/this._pattern.length();e-=Math.floor(e),t=e*this._pattern.length(),this._pattern.reset(),a=this._pattern.nextValue();let i=!1;for(;t>=a;)t-=a,a=this._pattern.nextValue(),i=!i;a-=t,i?(this._walker.nextPosition(a),a=this._pattern.nextValue()):this.isClosed&&(this._firstCurve=this._walker.nextCurve(a),a=this._pattern.nextValue(),this._walker.nextPosition(a),a=this._pattern.nextValue())}let i=this._walker.nextCurve(a);return i?this._walker.isPathEnd()?(this.iteratePath=!1,this._firstCurve&&(this._firstCurve.splice(0,1),e.mergePath(i,this._firstCurve),this._firstCurve=null)):(a=this._pattern.nextValue(),!this._walker.nextPosition(a)||this._walker.isPathEnd()?(this.iteratePath=!1,this._firstCurve&&(i=this._firstCurve,this._firstCurve=null)):this.iteratePath=!0):(this.iteratePath=!1,i=this._firstCurve,this._firstCurve=null),{paths:[i]}}}export{r as EffectDashes};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../geometry.js\";import{isSome as t}from\"../../../core/maybe.js\";import{isExtent as i,isPolygon as e}from\"../../../geometry/support/jsonUtils.js\";import{clipPolygonToTileExtent as s}from\"../CIMEffects.js\";import{GeometricEffectDonutMethod as n}from\"../enums.js\";import r from\"../../../geometry/SpatialReference.js\";class h{static local(){return null===h.instance&&(h.instance=new h),h.instance}execute(t,i,e,s,n){return new m(t,i,e,s,n)}}h.instance=null;class m{constructor(t,i,e,s,r){switch(this._inputGeometries=t,this._tileKey=s,this._geometryEngine=r,this._width=(void 0!==i.width?i.width:2)*e,i.method){case n.Mitered:case n.Bevelled:case n.Rounded:case n.TrueBuffer:case n.Square:}this._option=i.option}next(){let n;for(;n=this._inputGeometries.next();){if(i(n)&&this._width>0){if(Math.min(n.xmax-n.xmin,n.ymax-n.ymin)-2*this._width<0)return n;const t=[];return t.push([[n.xmin,n.ymin],[n.xmin,n.ymax],[n.xmax,n.ymax],[n.xmax,n.ymin],[n.xmin,n.ymin]]),t.push([[n.xmin+this._width,n.ymin+this._width],[n.xmax-this._width,n.ymin+this._width],[n.xmax-this._width,n.ymax-this._width],[n.xmin+this._width,n.ymax-this._width],[n.xmin+this._width,n.ymin+this._width]]),{rings:t}}if(e(n)){let i=null;const e=this._geometryEngine;let h=n;if(this._tileKey&&(h=s(n,Math.abs(this._width)+1),!h||!h.rings||0===h.rings.length))continue;if(t(e)&&(i=e.buffer(r.WebMercator,h,-this._width,1)),this._width>0){const t=[];for(const i of n.rings)i&&t.push(i);if(i)for(const e of i.rings)e&&t.push(e.reverse());if(t.length)return{rings:t}}}}return null}}export{h as EffectDonut};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{PathGeometryCursor as t}from\"../CIMCursor.js\";import{CurveHelper as i}from\"../CurveHelper.js\";class s{static local(){return null===s.instance&&(s.instance=new s),s.instance}execute(t,i,s,n,h){return new e(t,i,s)}}s.instance=null;class e extends t{constructor(t,s,e){super(t,!1,!0),this._curveHelper=new i,this._length=(void 0!==s.length?s.length:20)*e,this._angle=void 0!==s.angle?s.angle:225,this._position=void 0!==s.position?s.position:50,this._length<0&&(this._length=-this._length),this._position<20&&(this._position=20),this._position>80&&(this._position=80),this._mirror=!1}processPath(t){if(this._curveHelper.isEmpty(t,!1))return null;const i=t[0],s=t[t.length-1],e=[s[0]-i[0],s[1]-i[1]];this._curveHelper.normalize(e);const n=[i[0]+(s[0]-i[0])*this._position/100,i[1]+(s[1]-i[1])*this._position/100],h=Math.cos((90-this._angle)/180*Math.PI);let r=Math.sin((90-this._angle)/180*Math.PI);this._mirror&&(r=-r),this._mirror=!this._mirror;return{paths:[[i,[n[0]-this._length/2*h,n[1]-this._length/2*r],[n[0]+this._length/2*h,n[1]+this._length/2*r],s]]}}}export{s as EffectJog};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as t}from\"../../../core/lang.js\";import{isExtent as s,isPolygon as e,isPolyline as i,isMultipoint as o,isPoint as f}from\"../../../geometry/support/jsonUtils.js\";class n{static local(){return null===n.instance&&(n.instance=new n),n.instance}execute(t,s,e,i,o){return new r(t,s,e)}}n.instance=null;class r{constructor(t,s,e){this._inputGeometries=t,this._offsetX=void 0!==s.offsetX?s.offsetX*e:0,this._offsetY=void 0!==s.offsetY?-s.offsetY*e:0}next(){let n=this._inputGeometries.next();for(;n;){if(s(n))return{xmin:n.xmin+this._offsetX,xmax:n.xmax+this._offsetX,ymin:n.ymin+this._offsetY,ymax:n.ymax+this._offsetY};if(e(n)){const s=t(n);return this._moveMultipath(s.rings,this._offsetX,this._offsetY),s}if(i(n)){const s=t(n);return this._moveMultipath(s.paths,this._offsetX,this._offsetY),s}if(o(n)){const s=t(n);return this._movePath(s.points,this._offsetX,this._offsetY),s}if(f(n))return{x:n.x+this._offsetX,y:n.y+this._offsetY};n=this._inputGeometries.next()}return null}_moveMultipath(t,s,e){if(t)for(const i of t)this._movePath(i,s,e)}_movePath(t,s,e){if(t)for(const i of t)i[0]+=s,i[1]+=e}}export{n as EffectMove};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../geometry.js\";import{isNone as t}from\"../../../core/maybe.js\";import{isExtent as e,isPolygon as s,isPolyline as i}from\"../../../geometry/support/jsonUtils.js\";import{clipPolygonToTileExtent as n,clipPolylineToTileExtent as o}from\"../CIMEffects.js\";import{CurveHelper as r,PIXEL_TOLERANCE as f}from\"../CurveHelper.js\";import{GeometricEffectOffsetMethod as m}from\"../enums.js\";import h from\"../../../geometry/SpatialReference.js\";class a{static local(){return null===a.instance&&(a.instance=new a),a.instance}execute(t,e,s,i,n){return new l(t,e,s,i,n)}}a.instance=null;class l{constructor(t,e,s,i,n){this._inputGeometries=t,this._tileKey=i,this._geometryEngine=n,this._curveHelper=new r,this._offset=(e.offset??1)*s,this._method=e.method,this._option=e.option,this._offsetFlattenError=f*s}next(){let r;for(;r=this._inputGeometries.next();){if(0===this._offset)return r;if(e(r)){if(this._method===m.Rounded&&this._offset>0){const t=[[r.xmin,r.ymin],[r.xmin,r.ymax],[r.xmax,r.ymax],[r.xmax,r.ymin],[r.xmin,r.ymin]],e=this._curveHelper.offset(t,-this._offset,this._method,4,this._offsetFlattenError);return e?{rings:[e]}:null}if(Math.min(r.xmax-r.xmin,r.ymax-r.ymin)+2*this._offset>0)return{xmin:r.xmin-this._offset,xmax:r.xmax+this._offset,ymin:r.ymin-this._offset,ymax:r.ymax+this._offset}}const f=this._geometryEngine;if(t(f))return null;let a=r;if(s(r)){if(this._tileKey&&(a=n(r,Math.abs(this._offset)+1),!a||!a.rings||0===a.rings.length))continue}else if(i(r)&&this._tileKey&&(a=o(r,Math.abs(this._offset)+1),!a||!a.paths||0===a.paths.length))continue;return f.offset(h.WebMercator,a,-this._offset,1,this._method,4,this._offsetFlattenError)}return null}}export{a as EffectOffset};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as e}from\"../../../core/lang.js\";import{isPolyline as t}from\"../../../geometry/support/jsonUtils.js\";import{reverseMultipath as r}from\"../CIMCursor.js\";class s{static local(){return null===s.instance&&(s.instance=new s),s.instance}execute(e,t,r,s,i){return new n(e,t,r)}}s.instance=null;class n{constructor(e,t,r){this._inputGeometries=e,this._reverse=void 0===t.reverse||t.reverse}next(){let s=this._inputGeometries.next();for(;s;){if(!this._reverse)return s;if(t(s)){const t=e(s);return r(t.paths),t}s=this._inputGeometries.next()}return null}}export{s as EffectReverse};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as t}from\"../../../core/lang.js\";import{create as n}from\"../../../geometry/support/aaBoundingRect.js\";import{getBoundsXY as e}from\"../../../geometry/support/boundsUtils.js\";import{isExtent as r,isPolygon as i,isPolyline as o,isMultipoint as s,isPoint as a}from\"../../../geometry/support/jsonUtils.js\";class u{static local(){return null===u.instance&&(u.instance=new u),u.instance}execute(t,n,e,r,i){return new c(t,n,e)}}u.instance=null;class c{constructor(t,n,e){this._inputGeometries=t,this._rotateAngle=void 0!==n.angle?n.angle*Math.PI/180:0}next(){let u=this._inputGeometries.next();for(;u;){if(0===this._rotateAngle)return u;const c=n();e(c,u);const l=(c[2]+c[0])/2,m=(c[3]+c[1])/2;if(r(u)){const t={rings:[[[u.xmin,u.ymin],[u.xmin,u.ymax],[u.xmax,u.ymax],[u.xmax,u.ymin],[u.xmin,u.ymin]]]};return this._rotateMultipath(t.rings,l,m),t}if(i(u)){const n=t(u);return this._rotateMultipath(n.rings,l,m),n}if(o(u)){const n=t(u);return this._rotateMultipath(n.paths,l,m),n}if(s(u)){const n=t(u);return this._rotatePath(n.points,l,m),n}if(a(u))return u;u=this._inputGeometries.next()}return null}_rotateMultipath(t,n,e){if(t)for(const r of t)this._rotatePath(r,n,e)}_rotatePath(t,n,e){if(t){const r=Math.cos(this._rotateAngle),i=Math.sin(this._rotateAngle);for(const o of t){const t=o[0]-n,s=o[1]-e;o[0]=n+t*r-s*i,o[1]=e+t*i+s*r}}}}export{u as EffectRotate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as t}from\"../../../core/lang.js\";import{create as s}from\"../../../geometry/support/aaBoundingRect.js\";import{getBoundsXY as i}from\"../../../geometry/support/boundsUtils.js\";import{isExtent as r,isPolygon as n,isPolyline as o,isMultipoint as e,isPoint as a}from\"../../../geometry/support/jsonUtils.js\";class c{static local(){return null===c.instance&&(c.instance=new c),c.instance}execute(t,s,i,r,n){return new l(t,s,i)}}c.instance=null;class l{constructor(t,s,i){this._inputGeometries=t,this._xFactor=void 0!==s.xScaleFactor?s.xScaleFactor:1.15,this._yFactor=void 0!==s.yScaleFactor?s.yScaleFactor:1.15}next(){let c=this._inputGeometries.next();for(;c;){if(1===this._xFactor&&1===this._yFactor)return c;const l=s();i(l,c);const u=(l[2]+l[0])/2,m=(l[3]+l[1])/2;if(r(c)){const t={rings:[[[c.xmin,c.ymin],[c.xmin,c.ymax],[c.xmax,c.ymax],[c.xmax,c.ymin],[c.xmin,c.ymin]]]};return this._scaleMultipath(t.rings,u,m),t}if(n(c)){const s=t(c);return this._scaleMultipath(s.rings,u,m),s}if(o(c)){const s=t(c);return this._scaleMultipath(s.paths,u,m),s}if(e(c)){const s=t(c);return this._scalePath(s.points,u,m),s}if(a(c))return c;c=this._inputGeometries.next()}return null}_scaleMultipath(t,s,i){if(t)for(const r of t)this._scalePath(r,s,i)}_scalePath(t,s,i){if(t)for(const r of t){const t=(r[0]-s)*this._xFactor,n=(r[1]-i)*this._yFactor;r[0]=s+t,r[1]=i+n}}}export{c as EffectScale};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isPolyline as t,isPolygon as e}from\"../../../geometry/support/jsonUtils.js\";import{PathHelper as i}from\"../CurveHelper.js\";import{GeometricEffectWaveform as s}from\"../enums.js\";import{DashPattern as h,GeometryWalker as n,Pos as r}from\"../GeometryWalker.js\";class a{static local(){return null===a.instance&&(a.instance=new a),a.instance}execute(t,e,i,s,h){return new o(t,e,i)}}a.instance=null;class o{constructor(t,e,i){this._inputGeometries=t,this._height=(void 0!==e.amplitude?e.amplitude:2)*i,this._period=(void 0!==e.period?e.period:3)*i,this._style=e.waveform,this._height<=0&&(this._height=Math.abs(this._height)),this._period<=0&&(this._period=Math.abs(this._period)),this._pattern=new h,this._pattern.addValue(this._period),this._pattern.addValue(this._period),this._walker=new n,this._walker.updateTolerance(i)}next(){let i=this._inputGeometries.next();for(;i;){if(0===this._height||0===this._period)return i;if(t(i)){const t=this._processGeom(i.paths);if(t.length)return{paths:t}}if(e(i)){const t=this._processGeom(i.rings);if(t.length)return{rings:t}}i=this._inputGeometries.next()}return null}_processGeom(t){const e=[];for(const i of t)if(this._walker.init(i,this._pattern))switch(this._style){case s.Sinus:default:e.push(this._constructCurve(i,!1));break;case s.Square:e.push(this._constructSquare(i));break;case s.Triangle:e.push(this._constructTriangle(i));break;case s.Random:e.push(this._constructCurve(i,!0))}else e.push(i);return e}_constructCurve(t,e){const s=new i,h=this._walker.calculatePathLength(t);let n=Math.round(h/this._period);0===n&&(n=1);const a=n*16+1,o=h/n,l=this._period/16,_=1/a,c=2*Math.PI*h/o,p=2*Math.PI*Math.random(),u=2*Math.PI*Math.random(),d=2*Math.PI*Math.random(),g=.75-Math.random()/2,w=.75-Math.random()/2,f=new r;this._walker.curPointAndAngle(f),s.startPath(f.pt);let k=0;for(;;){if(!this._walker.nextPointAndAngle(l,f)){s.lineTo(t[t.length-1]);break}{const t=k;let i;if(k+=_,e){const e=this._height/2*(1+.3*Math.sin(g*c*t+p));i=e*Math.sin(c*t+u),i+=e*Math.sin(w*c*t+d),i/=2}else i=.5*this._height*Math.sin(.5*c*t);s.lineTo([f.pt[0]-i*f.sa,f.pt[1]+i*f.ca])}}return s.path()}_constructSquare(t){const e=new i,s=this._walker.calculatePathLength(t);Math.round(s/this._period);let h=!0;for(;;){let t=!1;if(this._walker.curPositionIsValid()){const i=new r;this._walker.curPointAndAngle(i);const s=new r;if(this._walker.nextPointAndAngle(this._period,s)){const n=new r;this._walker.nextPointAndAngle(this._period,n)&&(h?(e.startPath(i.pt),h=!1):e.lineTo(i.pt),e.lineTo([i.pt[0]-this._height/2*i.sa,i.pt[1]+this._height/2*i.ca]),e.lineTo([s.pt[0]-this._height/2*s.sa,s.pt[1]+this._height/2*s.ca]),e.lineTo([s.pt[0]+this._height/2*s.sa,s.pt[1]-this._height/2*s.ca]),e.lineTo([n.pt[0]+this._height/2*n.sa,n.pt[1]-this._height/2*n.ca]),t=!0)}}if(!t){e.lineTo(this._walker.getPathEnd());break}}return e.path()}_constructTriangle(t){const e=new i,s=this._walker.calculatePathLength(t);Math.round(s/this._period);let h=!0;for(;;){let t=!1;if(this._walker.curPositionIsValid()){const i=new r;this._walker.curPointAndAngle(i);const s=new r;if(this._walker.nextPointAndAngle(this._period/2,s)){const n=new r;this._walker.nextPointAndAngle(this._period,n)&&(this._walker.nextPosition(this._period/2)&&(h?(e.startPath(i.pt),h=!1):e.lineTo(i.pt),e.lineTo([s.pt[0]-this._height/2*s.sa,s.pt[1]+this._height/2*s.ca]),e.lineTo([n.pt[0]+this._height/2*n.sa,n.pt[1]-this._height/2*n.ca])),t=!0)}}if(!t){e.lineTo(this._walker.getPathEnd());break}}return e.path()}}export{a as EffectWave};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{PathTransformationCursor as t}from\"../CIMCursor.js\";import{PlacementEndings as e}from\"../enums.js\";import{GeometryWalker as s,DashPattern as i,Pos as n}from\"../GeometryWalker.js\";class a{static local(){return null===a.instance&&(a.instance=new a),a.instance}execute(t,e,s,i,n){return new r(t,e,s)}}a.instance=null;class r extends t{constructor(t,e,n){super(t,!0,!0),this._geometryWalker=new s,this._geometryWalker.updateTolerance(n),this._angleToLine=e.angleToLine??!0,this._offset=(e.offset?e.offset:0)*n,this._originalEndings=e.endings,this._offsetAtEnd=(e.customEndingOffset?e.customEndingOffset:0)*n,this._position=-(e.offsetAlongLine?e.offsetAlongLine:0)*n,this._pattern=new i,this._pattern.init(e.placementTemplate,!1),this._pattern.scale(n),this._endings=this._originalEndings}processPath(t){if(this._pattern.isEmpty())return null;let s;if(this.iteratePath)s=this._pattern.nextValue();else{this._originalEndings===e.WithFullGap&&this.isClosed?this._endings=e.WithMarkers:this._endings=this._originalEndings,this._pattern.extPtGap=0;let i,n=!0;switch(this._endings){case e.NoConstraint:i=-this._position,i=this._adjustPosition(i),n=!1;break;case e.WithHalfGap:default:i=-this._pattern.lastValue()/2;break;case e.WithFullGap:i=-this._pattern.lastValue(),this._pattern.extPtGap=this._pattern.lastValue();break;case e.WithMarkers:i=0;break;case e.Custom:i=-this._position,i=this._adjustPosition(i),this._pattern.extPtGap=.5*this._offsetAtEnd}if(!this._geometryWalker.init(t,this._pattern,n))return null;this._pattern.reset();let a=0;for(;i>a;)i-=a,a=this._pattern.nextValue();a-=i,s=a,this.iteratePath=!0}const i=new n;return this._geometryWalker.nextPointAndAngle(s,i)?this._endings===e.WithFullGap&&this._geometryWalker.isPathEnd()?(this.iteratePath=!1,null):this._endings===e.WithMarkers&&this._geometryWalker.isPathEnd()&&(this.iteratePath=!1,this.isClosed)?null:(this.internalPlacement.setTranslate(i.pt[0]-this._offset*i.sa,i.pt[1]+this._offset*i.ca),this._angleToLine&&this.internalPlacement.setRotateCS(i.ca,i.sa),this.internalPlacement):(this.iteratePath=!1,null)}_adjustPosition(t){let e=t/this._pattern.length();return e-=Math.floor(e),e*this._pattern.length()}}export{a as PlacementAlongLineSameSize};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{PathTransformationCursor as t,getCoord2D as e}from\"../CIMCursor.js\";import{CurveHelper as s}from\"../CurveHelper.js\";import{ExtremityPlacement as i}from\"../enums.js\";class n{static local(){return null===n.instance&&(n.instance=new n),n.instance}execute(t,e,s,i,n){return new r(t,e,s)}}n.instance=null;class r extends t{constructor(t,e,i){super(t,!1,!0),this._curveHelper=new s,this._angleToLine=void 0===e.angleToLine||e.angleToLine,this._offset=void 0!==e.offset?e.offset*i:0,this._type=e.extremityPlacement,this._position=void 0!==e.offsetAlongLine?e.offsetAlongLine*i:0,this._beginProcessed=!1}processPath(t){let e;switch(this._type){case i.Both:default:this._beginProcessed?(e=this._atExtremities(t,this._position,!1),this._beginProcessed=!1,this.iteratePath=!1):(e=this._atExtremities(t,this._position,!0),this._beginProcessed=!0,this.iteratePath=!0);break;case i.JustBegin:e=this._atExtremities(t,this._position,!0);break;case i.JustEnd:e=this._atExtremities(t,this._position,!1);case i.None:}return e}_atExtremities(t,s,i){const n=t.length;if(n<2)return null;const r=i?1:n-2,o=i?n:-1,a=i?1:-1;let l,h=0,c=i?t[0]:t[n-1];for(let _=r;_!==o;_+=a){l=c,c=t[_];const i=this._curveHelper.calculateLength(l,c);if(h+i>s){const t=(s-h)/i,[n,r]=this._curveHelper.getAngleCS(l,c,t),o=e(l,c,t);return this.internalPlacement.setTranslate(o[0]-this._offset*r,o[1]+this._offset*n),this._angleToLine&&this.internalPlacement.setRotateCS(-n,-r),this.internalPlacement}h+=i}return null}}export{n as PlacementAtExtremities};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{PathTransformationCursor as t}from\"../CIMCursor.js\";import{GeometryWalker as i,DashPattern as s,Pos as e,EndType as n}from\"../GeometryWalker.js\";class a{static local(){return null===a.instance&&(a.instance=new a),a.instance}execute(t,i,s,e,n){return new r(t,i,s)}}a.instance=null;class r extends t{constructor(t,e,n){super(t,!0,!0),this._walker=new i,this._walker.updateTolerance(n),this._angleToLine=void 0===e.angleToLine||e.angleToLine,this._offset=void 0!==e.offset?e.offset*n:0,this._beginGap=void 0!==e.beginPosition?e.beginPosition*n:0,this._endGap=void 0!==e.endPosition?e.endPosition*n:0,this._flipFirst=void 0===e.flipFirst||e.flipFirst,this._pattern=new s,this._pattern.init(e.positionArray,!1,!1),this._subPathLen=0,this._posCount=this._pattern.size(),this._isFirst=!0,this._prevPos=0}processPath(t){if(this._pattern.isEmpty())return null;let i;if(this.iteratePath){const t=this._pattern.nextValue()*this._subPathLen,s=this._beginGap+t;i=s-this._prevPos,this._prevPos=s}else{if(this._posCount=this._pattern.size(),this._isFirst=!0,this._prevPos=0,this._subPathLen=this._walker.calculatePathLength(t)-this._beginGap-this._endGap,this._subPathLen<0)return this.iteratePath=!1,null;if(!this._walker.init(t,this._pattern,!1))return null;this._pattern.reset();const s=this._pattern.nextValue()*this._subPathLen,e=this._beginGap+s;i=e-this._prevPos,this._prevPos=e,this.iteratePath=!0}const s=new e;if(!this._walker.nextPointAndAngle(i,s,n.END))return this.iteratePath=!1,null;this.internalPlacement.setTranslate(s.pt[0]-this._offset*s.sa,s.pt[1]+this._offset*s.ca);const a=this._isFirst&&this._flipFirst;let r,h;return this._angleToLine?(r=s.ca,h=s.sa):(r=1,h=0),a&&(r=-r,h=-h),this.internalPlacement.setRotateCS(r,h),this._isFirst=!1,this._posCount--,0===this._posCount&&(this.iteratePath=!1),this.internalPlacement}}export{a as PlacementAtRatioPositions};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t}from\"../../../core/maybe.js\";import s from\"../../../core/RandomLCG.js\";import{Placement as i}from\"../CIMPlacements.js\";import{PlacementGridType as e}from\"../enums.js\";const n=512,h=10,_=12,r=25,o=24;function a(t){return void 0!==t.rings}class l{static local(){return null===l.instance&&(l.instance=new l),l.instance}execute(t,s,i,e,n){return new f(t,s,i,e,n)}}l.instance=null;class f{constructor(t,h,_,r,o){if(this._xMin=0,this._xMax=0,this._yMin=0,this._yMax=0,this._currentX=0,this._currentY=0,this._accelerationMap=null,this._testInsidePolygon=!1,this._verticalSubdivision=!0,this._stepX=Math.abs(h.stepX??16)*_,this._stepY=Math.abs(h.stepY??16)*_,0!==this._stepX&&0!==this._stepY&&t&&a(t)&&t.rings){if(this._gridType=h.gridType??e.Fixed,this._gridType===e.Random){const t=h.seed??13,i=1;this._randomLCG=new s(t*i),this._randomness=(h.randomness??100)/100,this._gridAngle=0,this._shiftOddRows=!1,this._cosAngle=1,this._sinAngle=0,this._offsetX=0,this._offsetY=0,this._buildRandomValues()}else{if(this._randomness=0,this._gridAngle=h.gridAngle??0,this._shiftOddRows=h.shiftOddRows??!1,this._offsetX=(h.offsetX??0)*_,this._offsetY=(h.offsetY??0)*_,this._cosAngle=Math.cos(this._gridAngle/180*Math.PI),this._sinAngle=-Math.sin(this._gridAngle/180*Math.PI),this._stepX)if(this._offsetX<0)for(;this._offsetX<-.5*this._stepX;)this._offsetX+=this._stepX;else for(;this._offsetX>=.5*this._stepX;)this._offsetX-=this._stepX;if(this._stepY)if(this._offsetY<0)for(;this._offsetY<-.5*this._stepY;)this._offsetY+=this._stepY;else for(;this._offsetY>=.5*this._stepY;)this._offsetY-=this._stepY}if(this._graphicOriginX=0,this._graphicOriginY=0,null!=r){const[t,s,i]=r.split(\"/\"),e=parseFloat(s),h=parseFloat(i);this._graphicOriginX=-h*n,this._graphicOriginY=e*n,this._testInsidePolygon=!0}this._internalPlacement=new i,this._calculateMinMax(t),this._geometry=t}}next(){return this._geometry?this._nextInside():null}_buildRandomValues(){if(!f._randValues){f._randValues=[];for(let t=0;t<o;t++)for(let s=0;s<o;s++)f._randValues.push(this._randomLCG.getFloat()),f._randValues.push(this._randomLCG.getFloat())}}_calculateMinMax(t){let s,i,e,h,o,a,l,f,c,g,u,M,p,d;this._xMin=0,this._xMax=0,this._yMin=0,this._yMax=0,l=f=p=u=Number.MAX_VALUE,c=g=d=M=-Number.MAX_VALUE;const m=1!==this._cosAngle;let X=0;for(const n of t.rings){const t=n?n.length:0;for(let _=0;_<t;_++)a=n[_][0],o=n[_][1],s=a-this._graphicOriginX-this._offsetX,i=o-this._graphicOriginY-this._offsetY,m?(e=this._cosAngle*s-this._sinAngle*i,h=this._sinAngle*s+this._cosAngle*i):(e=s,h=i),l=Math.min(l,e),c=Math.max(c,e),f=Math.min(f,h),g=Math.max(g,h),u=Math.min(u,o),M=Math.max(M,o),p=Math.min(p,a),d=Math.max(d,a),X++}u=u!==Number.MAX_VALUE?u:-n-this._stepY,M=M!==-Number.MAX_VALUE?M:this._stepY,p=p!==Number.MAX_VALUE?p:-this._stepX,d=d!==-Number.MAX_VALUE?d:n+this._stepX;const A=M-u,Y=d-p;if(this._verticalSubdivision=A>=Y,this._polygonMin=this._verticalSubdivision?u:p,this._testInsidePolygon){let t=0-this._graphicOriginX-this._offsetX-this._stepX,s=n-this._graphicOriginX-this._offsetX+this._stepX,i=-n-this._graphicOriginY-this._offsetY-this._stepY,e=0-this._graphicOriginY-this._offsetY+this._stepY;if(m){const n=[[t,i],[t,e],[s,i],[s,e]];t=i=Number.MAX_VALUE,s=e=-Number.MAX_VALUE;for(const h of n){const n=this._cosAngle*h[0]-this._sinAngle*h[1],_=this._sinAngle*h[0]+this._cosAngle*h[1];t=Math.min(t,n),s=Math.max(s,n),i=Math.min(i,_),e=Math.max(e,_)}}l=l!==Number.MAX_VALUE?Math.max(l,t):t,f=f!==Number.MAX_VALUE?Math.max(f,i):i,c=c!==-Number.MAX_VALUE?Math.min(c,s):s,g=g!==-Number.MAX_VALUE?Math.min(g,e):e}this._xMin=Math.round(l/this._stepX),this._xMax=Math.round(c/this._stepX),this._yMin=Math.round(f/this._stepY),this._yMax=Math.round(g/this._stepY),this._currentX=this._xMax+1,this._currentY=this._yMin-1,this._testInsidePolygon&&X>_&&(A>r||Y>r)&&this._buildAccelerationMap(t,p,d,u,M)}_buildAccelerationMap(t,s,i,e,_){const{rings:r}=t,o=new Map,a=this._verticalSubdivision,l=a?_-e:i-s;let f=Math.ceil(l/h);if(f<=1)return;const c=Math.floor(l/f);let u,M,p,d,m,X,A,Y,x,y;f++,this._delta=c,a?(Y=-n-this._stepY,x=this._stepY,y=e):(Y=-this._stepX,x=n+this._stepX,y=s);for(let n=0;n<r.length;n++)if(u=r[n],!(u.length<2))for(let t=1;t<u.length;t++){if(M=u[t-1],p=u[t],a){if(M[1]===p[1]||M[1]<Y&&p[1]<Y||M[1]>x&&p[1]>x)continue;d=Math.min(M[1],p[1]),m=Math.max(M[1],p[1])}else{if(M[0]===p[0]||M[0]<Y&&p[0]<Y||M[0]>x&&p[0]>x)continue;d=Math.min(M[0],p[0]),m=Math.max(M[0],p[0])}for(;d<m;)X=Math.floor((d-y)/c),g(X,n,t,o),d+=c;A=Math.floor((m-y)/c),A>X&&g(A,n,t,o)}this._accelerationMap=o}_nextInside(){for(;;){if(this._currentX>this._xMax){if(this._currentY++,this._currentY>this._yMax)return null;this._currentX=this._xMin,this._shiftOddRows&&this._currentY%2&&this._currentX--}let t=this._currentX*this._stepX+this._offsetX;this._shiftOddRows&&this._currentY%2&&(t+=.5*this._stepX);const s=this._currentY*this._stepY+this._offsetY;let i,n;if(this._currentX++,this._gridType===e.Random){const e=(this._currentX%o+o)%o,h=(this._currentY%o+o)%o;i=this._graphicOriginX+t+this._stepX*this._randomness*(.5-f._randValues[h*o+e])*2/3,n=this._graphicOriginY+s+this._stepY*this._randomness*(.5-f._randValues[h*o+e+1])*2/3}else i=this._graphicOriginX+this._cosAngle*t+this._sinAngle*s,n=this._graphicOriginY-this._sinAngle*t+this._cosAngle*s;if(!this._testInsidePolygon||this._isInsidePolygon(i,n,this._geometry))return this._internalPlacement.setTranslate(i,n),this._internalPlacement}}_isInsidePolygon(s,i,e){const{rings:n}=e;if(t(this._accelerationMap))return c(s,i,e);const h=this._verticalSubdivision,_=h?i:s,r=Math.floor((_-this._polygonMin)/this._delta),o=this._accelerationMap.get(r);if(!o)return!1;let a,l,f,g,u,M=0;for(const t of o){u=t[0];const e=n[u];if(g=t[1],a=e[g-1],l=e[g],h){if(a[1]>i==l[1]>i)continue;f=(l[0]-a[0])*(i-a[1])-(l[1]-a[1])*(s-a[0])}else{if(a[0]>s==l[0]>s)continue;f=(l[1]-a[1])*(s-a[0])-(l[0]-a[0])*(i-a[1])}f>0?M++:M--}return 0!==M}}function c(t,s,i){const{rings:e}=i;let n,h,_,r=0;for(const o of e){n=o.length;for(let i=1;i<n;++i){if(h=o[i-1],_=o[i],h[1]>s==_[1]>s)continue;(_[0]-h[0])*(s-h[1])-(_[1]-h[1])*(t-h[0])>0?r++:r--}}return 0!==r}function g(t,s,i,e){let n=e.get(t);n||(n=[],e.set(t,n)),n.push([s,i])}export{l as PlacementInsidePolygon};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{PathTransformationCursor as e,getCoord2D as t,reversePath as n}from\"../CIMCursor.js\";import{CurveHelper as i}from\"../CurveHelper.js\";import{PlacementOnLineRelativeTo as s}from\"../enums.js\";const r=.001;class l{static local(){return null===l.instance&&(l.instance=new l),l.instance}execute(e,t,n,i,s){return new o(e,t,n)}}l.instance=null;class o extends e{constructor(e,t,n){super(e,!0,!0),this._curveHelper=new i,this._angleToLine=void 0===t.angleToLine||t.angleToLine,this._offset=void 0!==t.offset?t.offset*n:0,this._relativeTo=t.relativeTo,this._position=void 0!==t.startPointOffset?t.startPointOffset*n:0,this._epsilon=r*n}processPath(e){const i=this._position;if(this._relativeTo===s.SegmentMidpoint){for(this.iteratePath||(this._segmentCount=e.length,this._curSegment=1,this.iteratePath=!0);this._curSegment<this._segmentCount;){const n=this._curSegment;this._curSegment++;const i=e[n-1],s=e[n],r=this._curveHelper.calculateLength(i,s);if(r<this._epsilon)continue;const l=.5+this._position/r,[o,a]=this._curveHelper.getAngleCS(i,s,l),h=t(i,s,l);return this.internalPlacement.setTranslate(h[0]-this._offset*a,h[1]+this._offset*o),this._angleToLine&&this.internalPlacement.setRotateCS(o,a),this.internalPlacement}return this.iteratePath=!1,null}this._relativeTo===s.LineEnd&&n(e);const r=this.onLine(e,i);return this._relativeTo===s.LineEnd&&n(e),r}onLine(e,n){let i,r=!1;switch(this._relativeTo){case s.LineMiddle:default:i=this._curveHelper.calculatePathLength(e)/2+n;break;case s.LineBeginning:i=n;break;case s.LineEnd:i=n,r=!0}const l=e.length;let o,a=0,h=e[0];for(let s=1;s<l;++s){o=h,h=e[s];const n=this._curveHelper.calculateLength(o,h);if(a+n>i){const e=(i-a)/n,[s,l]=this._curveHelper.getAngleCS(o,h,e),c=t(o,h,e),u=r?-this._offset:this._offset;return this.internalPlacement.setTranslate(c[0]-u*l,c[1]+u*s),this._angleToLine&&(r?this.internalPlacement.setRotateCS(-s,-l):this.internalPlacement.setRotateCS(s,l)),this.internalPlacement}a+=n}return null}}export{l as PlacementOnLine};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{PathTransformationCursor as t,isClosedPath as s,getId as e}from\"../CIMCursor.js\";import{CurveHelper as i}from\"../CurveHelper.js\";class n{static local(){return null===n.instance&&(n.instance=new n),n.instance}execute(t,s,e,i,n){return new a(t,s,e)}}n.instance=null;const r=1e-15;class a extends t{constructor(t,s,e){super(t,!0,!0),this._curveHelper=new i,this._angleToLine=void 0===s.angleToLine||s.angleToLine,this._offset=void 0!==s.offset?s.offset*e:0,this._endPoints=void 0===s.placeOnEndPoints||s.placeOnEndPoints,this._controlPoints=void 0===s.placeOnControlPoints||s.placeOnControlPoints,this._regularVertices=void 0===s.placeOnRegularVertices||s.placeOnRegularVertices,this._tags=[],this._tagIterator=0}processPath(t){if(this.iteratePath||(this._preparePath(t),this.iteratePath=!0),this._tagIterator>=this._tags.length)return this._tags.length=0,this._tagIterator=0,this.iteratePath=!1,null;const s=this._tags[this._tagIterator];this._angleToLine&&this.internalPlacement.setRotate(s[2]);let e=s[0],i=s[1];if(0!==this._offset){const t=Math.cos(s[2]),n=Math.sin(s[2]);e-=this._offset*n,i+=this._offset*t}return this.internalPlacement.setTranslate(e,i),this._tagIterator++,this.internalPlacement}_preparePath(t){this._tags.length=0,this._tagIterator=0;const i=s(t),n=t.length-1;let r,a,h=0,l=0,_=0,c=0,g=0;for(;h<n;){h++,r=t[h-1],a=t[h];const s=e(r),u=e(a);(this._angleToLine||0!==this._offset)&&(c=this._curveHelper.getAngle(r,a,0)),1===h?i?(l=c,_=s):(this._endPoints||this._controlPoints&&1===s)&&this._tags.push([r[0],r[1],c]):1===s?this._controlPoints&&this._tags.push([r[0],r[1],o(g,c)]):this._regularVertices&&this._tags.push([r[0],r[1],o(g,c)]),(this._angleToLine||0!==this._offset)&&(g=this._curveHelper.getAngle(r,a,1)),h===n&&(i?1===u||1===_?this._controlPoints&&this._tags.push([a[0],a[1],o(g,l)]):this._regularVertices&&this._tags.push([a[0],a[1],o(g,l)]):(this._endPoints||this._controlPoints&&1===u)&&this._tags.push([a[0],a[1],g]))}this._tagIterator=0}}function o(t,s){const e=Math.PI;for(;Math.abs(s-t)>e+2*r;)s-t>e?s-=2*e:s+=2*e;return(t+s)/2}export{n as PlacementOnVertices};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t=e){this._data=[],this._compare=t}get size(){return this._data.length}enqueue(t){if(null==t)return;const{_data:e,_compare:n}=this;e.push(t);let l=e.length-1>>>0;const r=e[l];for(;l>0;){const t=l-1>>1,s=e[t];if(!(n(s,r)<=0))break;e[t]=r,e[l]=s,l=t}}dequeue(){const{_data:t,_compare:e}=this,n=t[0],l=t.pop();if(0===t.length)return n;t[0]=l;let r=0;const s=t.length,u=t[0];let a,o,c=null;for(;;){const n=2*r+1,l=2*r+2;if(c=null,n<s&&(a=t[n],e(a,u)>0&&(c=n)),l<s&&(o=t[l],(null===c&&e(o,u)<=0||null!==c&&e(o,a)<=0)&&(c=l)),null===c)break;t[r]=t[c],t[c]=u,r=c}return n}}const e=(t,e)=>t<e?-1:t>e?1:0;export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrap as t}from\"../../core/maybe.js\";import n from\"../../core/PriorityQueue.js\";import{create as e}from\"./aaBoundingRect.js\";import{getBoundsXY as i,getPointsBounds as r}from\"./boundsUtils.js\";import{ringCentroid as o,ringsCentroid as s}from\"./centroid.js\";import{getRingArea as a,projectPointOnLine as c,distanceFromPointToPolygon as l}from\"./coordsUtils.js\";import{segmentIntersects as f}from\"./intersectsBase.js\";const N=100*222045e-21;function u(t){const{rings:n}=t;if(!n||0===n.length)return null;const s=i(e(),t);if(!s)return null;const c=4*(Math.abs(s[0])+Math.abs(s[2])+Math.abs(s[1])+Math.abs(s[3])+1)*N;let l=0,f=0;for(let e=0;e<n.length;e++){const t=a(n[e]);t>f&&(f=t,l=e)}if(Math.abs(f)<=2*c*c){const t=r(e(),n[l]);return[(t[0]+t[2])/2,(t[1]+t[3])/2]}const u=o(n[l],!1,e());if(null===u)return null;if(1===n.length&&n[0].length<4)return u;const d=[[NaN,NaN],[NaN,NaN],[NaN,NaN],[NaN,NaN]],x=[NaN,NaN,NaN,NaN],M=[NaN,NaN,NaN,NaN];let b=!1,w=m(u,t,!0);0===w.distance&&(b=!0,d[0][0]=u[0],d[0][1]=u[1],w=m(u,t,!1)),x[0]=w.distance,M[0]=0;const y=[NaN,NaN];let C=!1,k=.25,P=-1;const T=r(e(),n[l]);let z=NaN;do{if(z=NaN,d[1]=g(t,p(T[0],T[2],k),c,s),isNaN(d[1][0])||isNaN(d[1][1])||(w=m(d[1],t,!1),z=w.distance),!isNaN(z)&&z>c&&h(d[1],t))C=!0,x[1]=z,M[1]=j(d[1],u);else if(!isNaN(z)&&z>P&&(P=z,y[0]=d[1][0],y[1]=d[1][1]),k-=.01,k<.1){if(!(P>=0))break;C=!0,x[1]=P,d[1][0]=y[0],d[1][1]=y[1],M[1]=j(d[1],u)}}while(!C);C=!1,k=.5,P=-1;let D=.01,S=1;do{if(z=NaN,d[2]=g(t,p(T[0],T[2],k),c,s),isNaN(d[2][0])||isNaN(d[2][1])||(w=m(d[2],t,!1),z=w.distance),!isNaN(z)&&z>c&&h(d[2],t))C=!0,x[2]=z,M[2]=j(d[2],u);else if(!isNaN(z)&&z>P)P=z,y[0]=d[2][0],y[1]=d[2][1];else if(z>P&&(P=z,y[0]=d[2][0],y[1]=d[2][1]),k=.5+D*S,D+=.01,S*=-1,k<.3||k>.7){if(!(P>=0))break;C=!0,x[2]=P,d[2][0]=y[0],d[2][1]=y[1],M[2]=j(d[2],u)}}while(!C);C=!1,k=.75,P=-1;do{if(z=NaN,d[3]=g(t,p(T[0],T[2],k),c,s),isNaN(d[3][0])||isNaN(d[3][1])||(w=m(d[3],t,!1),z=w.distance),!isNaN(z)&&z>c&&h(d[3],t))C=!0,x[3]=z,M[3]=j(d[3],u);else if(z>P&&(P=z,y[0]=d[3][0],y[1]=d[3][1]),k+=.01,k>.9){if(!(P>=0))break;C=!0,x[3]=P,d[3][0]=y[0],d[3][1]=y[1],M[3]=j(d[3],u)}}while(!C);const B=[0,1,2,3],Q=b?0:1;let R;for(let e=Q;e<4;e++)for(let t=Q;t<3;t++){const n=M[t],e=M[t+1];q(n,e)>0&&(R=B[t],B[t]=B[t+1],B[t+1]=R,M[t]=e,M[t+1]=n)}let U=Q,v=0,A=0;for(let e=Q;e<4;e++){switch(e){case 0:A=2*x[B[e]];break;case 1:A=1.66666666*x[B[e]];break;case 2:A=1.33333333*x[B[e]];break;case 3:A=x[B[e]]}A>v&&(v=A,U=B[e])}return d[U]}function h(t,n){const{rings:e}=n;let i=0;for(const r of e){const n=r.length;for(let e=1;e<n;++e){const n=r[e-1],o=r[e];if(n[1]>t[1]==o[1]>t[1])continue;(o[0]-n[0])*(t[1]-n[1])-(o[1]-n[1])*(t[0]-n[0])>0?i++:i--}}return 0!==i}function m(t,n,e){if(e&&h(t,n))return{coord:t,distance:0};let i=1/0,r=0,o=0;const s=[0,0],{rings:a}=n;for(const l of a)if(!(l.length<2))for(let n=0;n<l.length-1;n++){c(s,t,l,n);const e=j(t,s);e<i&&(i=e,r=s[0],o=s[1])}return{coord:[r,o],distance:Math.sqrt(i)}}function g(t,n,i,r){const o=[n,0];let s=1/0,a=1/0,c=!1,l=!1;const N=[[n,r[1]-1],[n,r[3]+1]],u=[0,0],h=[0,0],m=[0,0],g=[[0,0],[0,0]],x=e(),{rings:M}=t;for(const e of M)if(!(e.length<2))for(let t=1;t<e.length;t++){if(g[0][0]=e[t-1][0],g[0][1]=e[t-1][1],g[1][0]=e[t][0],g[1][1]=e[t][1],null===d(x,g))continue;if(h[0]=N[0][0],h[1]=N[0][1],m[0]=N[1][0],m[1]=N[1][1],0===y(x,h,m))continue;if(!f(N[0],N[1],g[0],g[1],u))continue;const n=u[1];s>a?n<s&&(s=n,c=!0):n<a&&(a=n,l=!0)}return c&&l?o[1]=(s+a)/2:o[0]=o[1]=NaN,o}function d(t,n){if(n.length<2)return null;t||(t=e());const[i,r]=n[0],[o,s]=n[1];return t[0]=Math.min(i,o),t[1]=Math.min(r,s),t[2]=Math.max(i,o),t[3]=Math.max(r,s),t}const x=1,M=4,b=3,w=12;function y(t,n,e){let i=C(n,t),r=C(e,t);const o=t[0],s=t[1],a=t[2],c=t[3];if(i&r)return 0;if(!(i|r))return 4;const l=(i?1:0)|(r?2:0);do{const l=e[0]-n[0],f=e[1]-n[1];if(l>f)i&b?(i&x?(n[1]+=f*(o-n[0])/l,n[0]=o):(n[1]+=f*(a-n[0])/l,n[0]=a),i=C(n,t)):r&b?(r&x?(e[1]+=f*(o-e[0])/l,e[0]=o):(e[1]+=f*(a-e[0])/l,e[0]=a),r=C(e,t)):i?(i&M?(n[0]+=l*(s-n[1])/f,n[1]=s):(n[0]+=l*(c-n[1])/f,n[1]=c),i=C(n,t)):(r&M?(e[0]+=l*(s-e[1])/f,e[1]=s):(e[0]+=l*(c-e[1])/f,e[1]=c),r=C(e,t));else if(i&w?(i&M?(n[0]+=l*(s-n[1])/f,n[1]=s):(n[0]+=l*(c-n[1])/f,n[1]=c),i=C(n,t)):r&w?(r&M?(e[0]+=l*(s-e[1])/f,e[1]=s):(e[0]+=l*(c-e[1])/f,e[1]=c),r=C(e,t)):i?(i&x?(n[1]+=f*(o-n[0])/l,n[0]=o):(n[1]+=f*(a-n[0])/l,n[0]=a),i=C(n,t)):(r&x?(e[1]+=f*(o-e[0])/l,e[0]=o):(e[1]+=f*(a-e[0])/l,e[0]=a),r=C(e,t)),i&r)return 0}while(i|r);return l}function C(t,n){return(t[0]<n[0]?1:0)|(t[0]>n[2]?1:0)<<1|(t[1]<n[1]?1:0)<<2|(t[1]>n[3]?1:0)<<3}function p(t,n,e){return t+(n-t)*e}function j(t,n){return(t[0]-n[0])*(t[0]-n[0])+(t[1]-n[1])*(t[1]-n[1])}function q(t,n){if(t<n)return-1;if(t>n)return 1;if(t===n)return 0;const e=isNaN(t),i=isNaN(n);return e<i?-1:e>i?1:0}class k{constructor(t,n,e,i){this.x=t,this.y=n,this.cellSize=e,this.distancefromCellCenter=l(t,n,i),this.maxDistanceToPolygon=this.distancefromCellCenter+this.cellSize*Math.SQRT2}}const P=1,T=100;function z(i){if(!i||!i.rings||0===i.rings.length)return null;const o=r(e(),i.rings[0]);if(!o)return null;const a=o[2]-o[0],c=o[3]-o[1];if(0===a||0===c)return[o[0]+a/2,o[1]+c/2];const l=Math.max(Math.min(a,c)/T,P),f=new n(((t,n)=>n.maxDistanceToPolygon-t.maxDistanceToPolygon)),N=Math.min(a,c);let u=N/2,h=0,m=0;for(h=o[0];h<o[2];h+=N)for(m=o[1];m<o[3];m+=N)f.enqueue(new k(h+u,m+u,u,i));const g=s(i.rings,!1);if(null===g)return null;let d,x=new k(g[0],g[1],0,i);for(;f.size>0;)d=t(f.dequeue()),d.distancefromCellCenter>x.distancefromCellCenter&&(x=d),d.maxDistanceToPolygon-x.distancefromCellCenter<=l||(u=d.cellSize/2,f.enqueue(new k(d.x-u,d.y-u,u,i)),f.enqueue(new k(d.x+u,d.y-u,u,i)),f.enqueue(new k(d.x-u,d.y+u,u,i)),f.enqueue(new k(d.x+u,d.y+u,u,i)));return[x.x,x.y]}export{u as getLabelPoint,z as getPolylabelPoint};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../core/has.js\";import{create as t}from\"../../../geometry/support/aaBoundingRect.js\";import{getBoundsXY as e}from\"../../../geometry/support/boundsUtils.js\";import{weightedAreaCentroid as s}from\"../../../geometry/support/centroid.js\";import{getPolylabelPoint as n,getLabelPoint as o}from\"../../../geometry/support/labelPoint.js\";import{Placement as r}from\"../CIMPlacements.js\";import{PlacementPolygonCenterMethod as i}from\"../enums.js\";function l(t){return void 0!==t.rings}class a{static local(){return null===a.instance&&(a.instance=new a),a.instance}execute(t,e,s,n,o){return new f(t,e,s)}}a.instance=null;class f{constructor(t,e,s){this._geometry=t,this._offsetX=void 0!==e.offsetX?e.offsetX*s:0,this._offsetY=void 0!==e.offsetY?e.offsetY*s:0,this._method=void 0!==e.method?e.method:i.OnPolygon,this._internalPlacement=new r}next(){const t=this._geometry;return this._geometry=null,t&&l(t)?this._polygonCenter(t):null}_polygonCenter(r){let l=!1;switch(this._method){case i.CenterOfMass:{const t=s(r);t&&(this._internalPlacement.setTranslate(t[0]+this._offsetX,t[1]+this._offsetY),l=!0)}break;case i.BoundingBoxCenter:{const s=t();e(s,r),s&&(this._internalPlacement.setTranslate((s[2]+s[0])/2+this._offsetX,(s[3]+s[1])/2+this._offsetY),l=!0)}break;case i.OnPolygon:default:{let t;t=has(\"polylabel-placement-enabled\")?n(r):o(r),null!==t&&(this._internalPlacement.setTranslate(t[0]+this._offsetX,t[1]+this._offsetY),l=!0)}}return l?this._internalPlacement:null}}export{a as PlacementPolygonCenter};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{EffectAddControlPoints as e}from\"./effects/EffectAddControlPoints.js\";import{EffectArrow as t}from\"./effects/EffectArrow.js\";import{EffectBuffer as r}from\"./effects/EffectBuffer.js\";import{EffectControlMeasureLine as c}from\"./effects/EffectControlMeasureLine.js\";import{EffectCut as f}from\"./effects/EffectCut.js\";import{EffectDashes as o}from\"./effects/EffectDashes.js\";import{EffectDonut as s}from\"./effects/EffectDonut.js\";import{EffectJog as a}from\"./effects/EffectJog.js\";import{EffectMove as n}from\"./effects/EffectMove.js\";import{EffectOffset as l}from\"./effects/EffectOffset.js\";import{EffectReverse as m}from\"./effects/EffectReverse.js\";import{EffectRotate as i}from\"./effects/EffectRotate.js\";import{EffectScale as u}from\"./effects/EffectScale.js\";import{EffectWave as M}from\"./effects/EffectWave.js\";import{PlacementAlongLineSameSize as p}from\"./placements/PlacementAlongLineSameSize.js\";import{PlacementAtExtremities as E}from\"./placements/PlacementAtExtremities.js\";import{PlacementAtRatioPositions as C}from\"./placements/PlacementAtRatioPositions.js\";import{PlacementInsidePolygon as I}from\"./placements/PlacementInsidePolygon.js\";import{PlacementOnLine as P}from\"./placements/PlacementOnLine.js\";import{PlacementOnVertices as j}from\"./placements/PlacementOnVertices.js\";import{PlacementPolygonCenter as G}from\"./placements/PlacementPolygonCenter.js\";function A(p){if(!p)return null;switch(p.type){case\"CIMGeometricEffectAddControlPoints\":return e.local();case\"CIMGeometricEffectArrow\":return t.local();case\"CIMGeometricEffectBuffer\":return r.local();case\"CIMGeometricEffectControlMeasureLine\":return c.local();case\"CIMGeometricEffectCut\":return f.local();case\"CIMGeometricEffectDashes\":return o.local();case\"CIMGeometricEffectDonut\":return s.local();case\"CIMGeometricEffectJog\":return a.local();case\"CIMGeometricEffectMove\":return n.local();case\"CIMGeometricEffectOffset\":return l.local();case\"CIMGeometricEffectReverse\":return m.local();case\"CIMGeometricEffectRotate\":return i.local();case\"CIMGeometricEffectScale\":return u.local();case\"CIMGeometricEffectWave\":return M.local()}return null}function g(e){if(!e)return null;switch(e.type){case\"CIMMarkerPlacementAlongLineSameSize\":return p.local();case\"CIMMarkerPlacementAtExtremities\":return E.local();case\"CIMMarkerPlacementAtRatioPositions\":return C.local();case\"CIMMarkerPlacementInsidePolygon\":return I.local();case\"CIMMarkerPlacementOnLine\":return P.local();case\"CIMMarkerPlacementOnVertices\":return j.local();case\"CIMMarkerPlacementPolygonCenter\":return G.local()}return null}export{A as getEffectOperator,g as getPlacementOperator};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction t(t){const e=t.getFrame(0);if(e instanceof HTMLImageElement||e instanceof HTMLCanvasElement)return e;const n=document.createElement(\"canvas\");n.width=t.width,n.height=t.height;const a=n.getContext(\"2d\");return e instanceof ImageData?a.putImageData(e,0,0):a.drawImage(e,0,0),n}export{t as getFirstFrame};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t=0,h=0,i=0,s=0){this.x=t,this.y=h,this.width=i,this.height=s}get isEmpty(){return this.width<=0||this.height<=0}union(t){this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.width=Math.max(this.width,t.width),this.height=Math.max(this.height,t.height)}}export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{f as t,c as h}from\"../../../../../chunks/vec2f32.js\";class i{constructor(i,e,s,r){this.center=t(i,e),this.centerT=h(),this.halfWidth=s/2,this.halfHeight=r/2,this.width=s,this.height=r}get x(){return this.center[0]}get y(){return this.center[1]}get blX(){return this.center[0]+this.halfWidth}get blY(){return this.center[1]+this.halfHeight}get trX(){return this.center[0]-this.halfWidth}get trY(){return this.center[1]-this.halfHeight}get xmin(){return this.x-this.halfWidth}get xmax(){return this.x+this.halfWidth}get ymin(){return this.y-this.halfHeight}get ymax(){return this.y+this.halfHeight}set x(t){this.center[0]=t}set y(t){this.center[1]=t}clone(){return new i(this.x,this.y,this.width,this.height)}serialize(t){return t.writeF32(this.center[0]),t.writeF32(this.center[1]),t.push(this.width),t.push(this.height),t}findCollisionDelta(t,h=4){const i=Math.abs(t.centerT[0]-this.centerT[0]),e=Math.abs(t.centerT[1]-this.centerT[1]),s=(t.halfWidth+this.halfWidth+h)/i,r=(t.halfHeight+this.halfHeight+h)/e,n=Math.min(s,r);return Math.log2(n)}extend(t){const h=Math.min(this.xmin,t.xmin),i=Math.min(this.ymin,t.ymin),e=Math.max(this.xmax,t.xmax)-h,s=Math.max(this.ymax,t.ymax)-i,r=h+e/2,n=i+s/2;this.width=e,this.height=s,this.halfWidth=e/2,this.halfHeight=s/2,this.x=r,this.y=n}static deserialize(t){const h=t.readF32(),e=t.readF32(),s=t.readInt32(),r=t.readInt32();return new i(h,e,s,r)}}export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{pt2px as t}from\"../../../../../../core/screenUtils.js\";import{b as s,m as e,t as i,r as n}from\"../../../../../../chunks/mat2d.js\";import{c as o,t as h}from\"../../../../../../chunks/mat2df32.js\";import{s as r,t as a}from\"../../../../../../chunks/vec2.js\";import{c}from\"../../../../../../chunks/vec2f32.js\";import{VAlign as d}from\"../../alignmentUtils.js\";import{i1616to32 as f}from\"../../number.js\";import m from\"../../Rect.js\";import u from\"../../collisions/BoundingBox.js\";const l=26,g=4,_=l+g,p=l-6,x=3,w=8,y=Math.PI/180,M=8,b=1.5;class B{constructor(t,s,e,i){this._rotationT=o(),this._xBounds=0,this._yBounds=0,this.minZoom=0,this.maxZoom=255,this._bounds=null;const n=e.rect,h=new Float32Array(8);t*=i,s*=i;const r=e.code?n.width*i:e.metrics.width,a=e.code?n.height*i:e.metrics.height;this.width=r,this.height=a,h[0]=t,h[1]=s,h[2]=t+r,h[3]=s,h[4]=t,h[5]=s+a,h[6]=t+r,h[7]=s+a,this._data=h,this._setTextureCoords(n),this._scale=i,this._mosaic=e,this.x=t,this.y=s,this.maxOffset=Math.max(t+r,s+a)}get mosaic(){return this._mosaic}set angle(t){this._angle=t,s(this._rotationT,-t),this._setOffsets(this._data)}get angle(){return this._angle}get xTopLeft(){return this._data[0]}get yTopLeft(){return this._data[1]}get xBottomRight(){return this._data[6]}get yBottomRight(){return this._data[7]}get texcoords(){return this._texcoords}get textureBinding(){return this._mosaic.textureBinding}get offsets(){return this._offsets||this._setOffsets(this._data),this._offsets}get char(){return String.fromCharCode(this._mosaic.code)}get code(){return this._mosaic.code}get bounds(){if(!this._bounds){const{height:t,width:s}=this._mosaic.metrics,i=s*this._scale,n=Math.abs(t)*this._scale,r=new Float32Array(8);r[0]=this.x,r[1]=this.y,r[2]=this.x+i,r[3]=this.y,r[4]=this.x,r[5]=this.y+n,r[6]=this.x+i,r[7]=this.y+n;const a=e(o(),this._rotationT,this._transform);h(r,r,a);let c=1/0,d=1/0,f=0,m=0;for(let e=0;e<4;e++){const t=r[2*e],s=r[2*e+1];c=Math.min(c,t),d=Math.min(d,s),f=Math.max(f,t),m=Math.max(m,s)}const l=f-c,g=m-d,_=c+l/2,p=d+g/2;this._bounds=new u(_,p,l,g)}return this._bounds}setTransform(t){this._transform=t,this._offsets=null}_setOffsets(t){this._offsets||(this._offsets={upperLeft:0,upperRight:0,lowerLeft:0,lowerRight:0});const s=this._offsets,i=new Float32Array(8),n=e(o(),this._rotationT,this._transform);h(i,t,n),s.upperLeft=f(i[0]*w,i[1]*w),s.upperRight=f(i[2]*w,i[3]*w),s.lowerLeft=f(i[4]*w,i[5]*w),s.lowerRight=f(i[6]*w,i[7]*w)}_setTextureCoords({x:t,y:s,width:e,height:i}){this._texcoords={upperLeft:f(t,s),upperRight:f(t+e,s),lowerLeft:f(t,s+i),lowerRight:f(t+e,s+i)}}}const L=(t,s)=>({code:0,page:0,sdf:!0,rect:new m(0,0,11,8),textureBinding:s,metrics:{advance:0,height:4,width:t,left:0,top:0}});function R(t,s){return t.forEach((t=>a(t,t,s))),{upperLeft:f(w*t[0][0],w*t[0][1]),upperRight:f(w*t[1][0],w*t[1][1]),lowerLeft:f(w*t[2][0],w*t[2][1]),lowerRight:f(w*t[3][0],w*t[3][1])}}class T{constructor(t,s,e){this._rotation=0,this._decorate(t,s,e),this.glyphs=t,this.bounds=this._createBounds(t),this.isMultiline=s.length>1,this._hasRotation=0!==e.angle,this._transform=this._createGlyphTransform(this.bounds,e),this._borderLineSize=e.borderLineSize,(e.borderLineSize||e.hasBackground)&&([this.bounds,this.background]=this.shapeBackground(this._transform));for(const i of t)i.setTransform(this._transform)}setRotation(t){if(0===t&&0===this._rotation)return;this._rotation=t;const i=this._transform,n=s(o(),t);e(i,n,i);for(const s of this.glyphs)s.setTransform(this._transform)}_decorate(t,s,e){if(!e.decoration||\"none\"===e.decoration||!t.length)return;const i=e.scale,n=\"underline\"===e.decoration?_:p,o=t[0].textureBinding;for(const h of s){const s=h.startX*i,e=h.startY*i,r=(h.width+h.glyphWidthEnd)*i;t.push(new B(s,e+n*i,L(r,o),1))}}shapeBackground(s){const e=t(this._borderLineSize||0),i=(b+e)/2,n=this._borderLineSize?i:0,{xmin:o,ymin:h,xmax:r,ymax:a,x:c,y:d,width:f,height:m}=this.bounds,l=[o-M,h-M],g=[r+M,h-M],_=[o-M,a+M],p=[r+M,a+M],x=R([[l[0]-i,l[1]-i],[g[0]+i,g[1]-i],[l[0]+n,l[1]+n],[g[0]-n,g[1]+n]],s),w=R([[_[0]+n,_[1]-n],[p[0]-n,p[1]-n],[_[0]-i,_[1]+i],[p[0]+i,p[1]+i]],s),y=R([[l[0]-i,l[1]-i],[l[0]+n,l[1]+n],[_[0]-i,_[1]+i],[_[0]+n,_[1]-n]],s),B=R([[g[0]-n,g[1]+n],[g[0]+i,g[1]-i],[p[0]-n,p[1]-n],[p[0]+i,p[1]+i]],s),L={main:R([l,g,_,p],s),top:x,bot:w,left:y,right:B};return[new u(c,d,f+2*i,m+2*i),L]}get boundsT(){const t=this.bounds,s=r(c(),t.x,t.y);if(a(s,s,this._transform),this._hasRotation){const e=Math.max(t.width,t.height);return new u(s[0],s[1],e,e)}return new u(s[0],s[1],t.width,t.height)}_createBounds(t){let s=1/0,e=1/0,i=0,n=0;for(const r of t)s=Math.min(s,r.xTopLeft),e=Math.min(e,r.yTopLeft),i=Math.max(i,r.xBottomRight),n=Math.max(n,r.yBottomRight);const o=i-s,h=n-e;return new u(s+o/2,e+h/2,o,h)}_createGlyphTransform(t,s){const e=y*s.angle,h=o(),a=c();return i(h,h,r(a,s.xOffset,-s.yOffset)),s.isCIM?n(h,h,e):(i(h,h,r(a,t.x,t.y)),n(h,h,e),i(h,h,r(a,-t.x,-t.y))),h}}class v{constructor(t,s,e,i,n,o){this.glyphWidthEnd=0,this.startX=0,this.startY=0,this.start=Math.max(0,Math.min(s,e)),this.end=Math.max(0,Math.max(s,e)),this.end<t.length&&(this.glyphWidthEnd=t[this.end].metrics.width),this.width=i,this.yMin=n,this.yMax=o}}const j=t=>10===t,k=t=>32===t;function A(t,s,e){const i=new Array,n=1/e.scale,o=e.maxLineWidth*n,h=s?t.length-1:0,r=s?-1:t.length,a=s?-1:1;let c=h,d=0,f=0,m=c,u=m,l=0,g=1/0,_=0;for(;c!==r;){const{code:s,metrics:e}=t[c],n=Math.abs(e.top);if(j(s)||k(s)||(g=Math.min(g,n),_=Math.max(_,n+e.height)),j(s))c!==h&&(i.push(new v(t,m,c-a,d,g,_)),g=1/0,_=0),d=0,m=c+a,u=c+a,f=0;else if(k(s))u=c+a,f=0,l=e.advance,d+=e.advance;else if(d>o){if(u!==m){const s=u-2*a;d-=l,i.push(new v(t,m,s,d-f,g,_)),g=1/0,_=0,m=u,d=f}else i.push(new v(t,m,c-a,d,g,_)),g=1/0,_=0,m=c,u=c,d=0;d+=e.advance,f+=e.advance}else d+=e.advance,f+=e.advance;c+=a}const p=new v(t,m,c-a,d,g,_);return p.start>=0&&p.end<t.length&&i.push(p),i}function O(t,s){let e=0;for(let o=0;o<t.length;o++){const{width:s}=t[o];e=Math.max(s,e)}const i=\"underline\"===s.decoration?g:0,n=t[0].yMin;return{x:0,y:n,height:t[t.length-1].yMax+s.lineHeight*(t.length-1)+i-n,width:e}}function S(t,s,e){const i=e.scale,n=new Array,o=A(t,s,e),h=O(o,e),{vAlign:r,hAlign:a}=e,c=r===d.Baseline?1:0,f=c?0:r-1,m=(1-c)*-h.y+f*(h.height/2)+(c?1:0)*-l;for(let d=0;d<o.length;d++){const{start:s,end:h,width:r}=o[d];let c=-1*(a+1)*(r/2)-x;const f=d*e.lineHeight+m-x;o[d].startX=c,o[d].startY=f;for(let e=s;e<=h;e++){const s=t[e];if(j(s.code))continue;const o=new B(c+s.metrics.left,f-s.metrics.top,s,i);c+=s.metrics.advance,n.push(o)}}return new T(n,o,e)}export{B as ShapedGlyph,T as ShapingInfo,S as shapeGlyphs};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{bidiText as t}from\"../../core/BidiText.js\";import{getFontFamily as e}from\"../../core/fontUtils.js\";import{clone as r}from\"../../core/lang.js\";import i from\"../../core/Logger.js\";import{isNone as s,isSome as o}from\"../../core/maybe.js\";import n from\"../../core/ObjectPool.js\";import{px2pt as a,pt2px as l}from\"../../core/screenUtils.js\";import{create as h,fromValues as c}from\"../../geometry/support/aaBoundingRect.js\";import{getBoundsXY as m}from\"../../geometry/support/boundsUtils.js\";import{polygonCentroid as f}from\"../../geometry/support/centroid.js\";import{isPoint as u,isPolygon as d,isPolyline as p,isExtent as _,isMultipoint as g}from\"../../geometry/support/jsonUtils.js\";import{SimpleGeometryCursor as y}from\"./CIMEffects.js\";import P from\"./CIMImageColorSubstitutionHelper.js\";import{getEffectOperator as w,getPlacementOperator as x}from\"./CIMOperators.js\";import{Placement as S}from\"./CIMPlacements.js\";import{LineCapStyle as M,LineJoinStyle as b}from\"./enums.js\";import{getFirstFrame as k}from\"./imageUtils.js\";import C from\"./Rect.js\";import I from\"./TextRasterizer.js\";import{isSVGImage as T,fromCIMFontDecoration as R,fromCIMFontStyle as v,fromCIMHorizontalAlignment as z,fromCIMVerticalAlignment as F,getFillColor as L,getStrokeColor as A,getFontStyle as j,getFontWeight as U}from\"./utils.js\";import{HAlign as H,VAlign as B}from\"../../views/2d/engine/webgl/alignmentUtils.js\";import{GLYPH_SIZE as D,MAGIC_LABEL_LINE_HEIGHT as G}from\"../../views/2d/engine/webgl/definitions.js\";import{shapeGlyphs as X}from\"../../views/2d/engine/webgl/mesh/templates/shapingUtils.js\";const E=Math.PI/180,J=10,N=i.getLogger(\"esri.symbols.cim.CIMSymbolDrawHelper\");class O{constructor(t){this._t=t}static createIdentity(){return new O([1,0,0,0,1,0])}clone(){const t=this._t;return new O(t.slice())}transform(t){const e=this._t;return[e[0]*t[0]+e[1]*t[1]+e[2],e[3]*t[0]+e[4]*t[1]+e[5]]}static createScale(t,e){return new O([t,0,0,0,e,0])}scale(t,e){const r=this._t;return r[0]*=t,r[1]*=t,r[2]*=t,r[3]*=e,r[4]*=e,r[5]*=e,this}scaleRatio(){return Math.sqrt(this._t[0]*this._t[0]+this._t[1]*this._t[1])}static createTranslate(t,e){return new O([0,0,t,0,0,e])}translate(t,e){const r=this._t;return r[2]+=t,r[5]+=e,this}static createRotate(t){const e=Math.cos(t),r=Math.sin(t);return new O([e,-r,0,r,e,0])}rotate(t){return O.multiply(this,O.createRotate(t),this)}angle(){const t=this._t[0],e=this._t[3],r=Math.sqrt(t*t+e*e);return[t/r,e/r]}static multiply(t,e,r){const i=t._t,s=e._t,o=i[0]*s[0]+i[3]*s[1],n=i[1]*s[0]+i[4]*s[1],a=i[2]*s[0]+i[5]*s[1]+s[2],l=i[0]*s[3]+i[3]*s[4],h=i[1]*s[3]+i[4]*s[4],c=i[2]*s[3]+i[5]*s[4]+s[5],m=r._t;return m[0]=o,m[1]=n,m[2]=a,m[3]=l,m[4]=h,m[5]=c,r}invert(){const t=this._t;let e=t[0]*t[4]-t[1]*t[3];if(0===e)return new O([0,0,0,0,0,0]);e=1/e;const r=(t[1]*t[5]-t[2]*t[4])*e,i=(t[2]*t[3]-t[0]*t[5])*e,s=t[4]*e,o=-t[1]*e,n=-t[3]*e,a=t[0]*e;return new O([s,o,r,n,a,i])}}class Y{constructor(t,e){this._resourceManager=t,this._transfos=[],this._sizeTransfos=[],this._geomUnitsPerPoint=1,this._placementPool=new n(S,void 0,void 0,100),this._earlyReturn=!1,this._mapRotation=0,this._transfos.push(e||O.createIdentity()),this._sizeTransfos.push(e?e.scaleRatio():1)}setTransform(t,e){this._transfos=[t||O.createIdentity()],this._sizeTransfos=[e||(t?t.scaleRatio():1)]}setGeomUnitsPerPoint(t){this._geomUnitsPerPoint=t}transformPt(t){return this._transfos[this._transfos.length-1].transform(t)}transformSize(t){return t*this._sizeTransfos[this._sizeTransfos.length-1]}reverseTransformPt(t){return this._transfos[this._transfos.length-1].invert().transform(t)}reverseTransformSize(t){return t/this._sizeTransfos[this._sizeTransfos.length-1]}getTransformAngle(){return this._transfos[this._transfos.length-1].angle()}geomUnitsPerPoint(){return this.isEmbedded()?1:this._geomUnitsPerPoint}isEmbedded(){return this._transfos.length>1}back(){return this._transfos[this._transfos.length-1]}push(t,e){const r=e?t.scaleRatio():1;O.multiply(t,this.back(),t),this._transfos.push(t),this._sizeTransfos.push(this._sizeTransfos[this._sizeTransfos.length-1]*r)}pop(){this._transfos.splice(-1,1),this._sizeTransfos.splice(-1,1)}drawSymbol(t,e,r){if(t)switch(t.type){case\"CIMPointSymbol\":case\"CIMLineSymbol\":case\"CIMPolygonSymbol\":this.drawMultiLayerSymbol(t,e);break;case\"CIMTextSymbol\":this.drawTextSymbol(t,e,r)}}drawMultiLayerSymbol(t,e){if(!t||!e)return;const r=t.symbolLayers;if(!r)return;const i=t.effects;if(i&&i.length>0){const t=this.executeEffects(i,e);if(t){let e=t.next();for(;e;)this.drawSymbolLayers(r,e),e=t.next()}}else this.drawSymbolLayers(r,e)}executeEffects(t,e){const r=this._resourceManager.geometryEngine;let i=new y(e);for(const s of t){const t=w(s);t&&(i=t.execute(i,s,this.geomUnitsPerPoint(),null,r))}return i}drawSymbolLayers(t,e){let r=t.length;for(;r--;){const i=t[r];if(!i||!1===i.enable)continue;const s=i.effects;if(s&&s.length>0){const t=this.executeEffects(s,e);if(t){let e=null;for(;(e=t.next())&&(this.drawSymbolLayer(i,e),!this._earlyReturn););}}else this.drawSymbolLayer(i,e);if(this._earlyReturn)return}}drawSymbolLayer(t,e){switch(t.type){case\"CIMSolidFill\":this.drawSolidFill(e,t.color);break;case\"CIMHatchFill\":this.drawHatchFill(e,t);break;case\"CIMPictureFill\":this.drawPictureFill(e,t);break;case\"CIMGradientFill\":this.drawGradientFill(e,t);break;case\"CIMSolidStroke\":this.drawSolidStroke(e,t.color,t.width,t.capStyle,t.joinStyle,t.miterLimit);break;case\"CIMPictureStroke\":this.drawPictureStroke(e,t);break;case\"CIMGradientStroke\":this.drawGradientStroke(e,t);break;case\"CIMCharacterMarker\":case\"CIMPictureMarker\":case\"CIMVectorMarker\":this.drawMarkerLayer(t,e)}}drawHatchFill(t,e){const r=this._buildHatchPolyline(e,t,this.geomUnitsPerPoint());r&&(this.pushClipPath(t),this.drawMultiLayerSymbol(e.lineSymbol,r),this.popClipPath())}drawPictureFill(t,e){}drawGradientFill(t,e){}drawPictureStroke(t,e){}drawGradientStroke(t,e){}drawMarkerLayer(t,e){const r=t.markerPlacement;if(r){const i=x(r);if(i){const s=\"CIMMarkerPlacementInsidePolygon\"===r.type||\"CIMMarkerPlacementPolygonCenter\"===r.type&&r.clipAtBoundary;s&&this.pushClipPath(e);const o=i.execute(e,r,this.geomUnitsPerPoint(),null,this._resourceManager.geometryEngine);if(o){let e=null;for(;(e=o.next())&&(this.drawMarker(t,e),!this._earlyReturn););}s&&this.popClipPath()}}else{const r=this._placementPool.acquire();if(u(e))r.tx=e.x,r.ty=e.y,this.drawMarker(t,r);else if(d(e)){const i=f(e);i&&([r.tx,r.ty]=i,this.drawMarker(t,r))}else for(const i of e.points)if(r.tx=i[0],r.ty=i[1],this.drawMarker(t,r),this._earlyReturn)break;this._placementPool.release(r)}}drawMarker(t,e){switch(t.type){case\"CIMCharacterMarker\":case\"CIMPictureMarker\":this.drawPictureMarker(t,e);break;case\"CIMVectorMarker\":this.drawVectorMarker(t,e)}}drawPictureMarker(t,e){if(!t)return;const r=this._resourceManager.getResource(t.url),i=t.size??10;if(s(r)||i<=0)return;const o=r.width,n=r.height;if(!o||!n)return;const a=o/n,l=t.scaleX??1,h=O.createIdentity(),c=t.anchorPoint;if(c){let e=c.x,r=c.y;\"Absolute\"!==t.anchorPointUnits&&(e*=i*a*l,r*=i),h.translate(-e,-r)}let m=t.rotation??0;t.rotateClockwise&&(m=-m),this._mapRotation&&(m+=this._mapRotation),m&&h.rotate(m*E);let f=t.offsetX??0,u=t.offsetY??0;if(f||u){if(this._mapRotation){const t=E*this._mapRotation,e=Math.cos(t),r=Math.sin(t),i=f*r+u*e;f=f*e-u*r,u=i}h.translate(f,u)}const d=this.geomUnitsPerPoint();1!==d&&h.scale(d,d);const p=e.getAngle();p&&h.rotate(p),h.translate(e.tx,e.ty),this.push(h,!1),this.drawImage(t,i),this.pop()}drawVectorMarker(t,e){if(!t)return;const r=t.markerGraphics;if(!r)return;const i=t.size??10,s=t.frame,o=s?s.ymax-s.ymin:0,n=i&&o?i/o:1,a=O.createIdentity();s&&a.translate(.5*-(s.xmax+s.xmin),.5*-(s.ymax+s.ymin));const l=t.anchorPoint;if(l){let e=l.x,r=l.y;\"Absolute\"!==t.anchorPointUnits?s&&(e*=s.xmax-s.xmin,r*=s.ymax-s.ymin):(e/=n,r/=n),a.translate(-e,-r)}1!==n&&a.scale(n,n);let h=t.rotation??0;t.rotateClockwise&&(h=-h),this._mapRotation&&(h+=this._mapRotation),h&&a.rotate(h*E);let c=t.offsetX??0,m=t.offsetY??0;if(c||m){if(this._mapRotation){const t=E*this._mapRotation,e=Math.cos(t),r=Math.sin(t),i=c*r+m*e;c=c*e-m*r,m=i}a.translate(c,m)}const f=this.geomUnitsPerPoint();1!==f&&a.scale(f,f);const u=e.getAngle();u&&a.rotate(u),a.translate(e.tx,e.ty),this.push(a,t.scaleSymbolsProportionally);for(const d of r)if(d&&d.symbol&&d.geometry||N.error(\"Invalid marker graphic\",d),this.drawSymbol(d.symbol,d.geometry,d.textString),this._earlyReturn)break;this.pop()}drawTextSymbol(t,e,r){if(!t)return;if(!u(e))return;if((t.height??10)<=0)return;const i=O.createIdentity();let s=t.angle??0;s=-s,s&&i.rotate(s*E);const o=t.offsetX??0,n=t.offsetY??0;(o||n)&&i.translate(o,n);const a=this.geomUnitsPerPoint();1!==a&&i.scale(a,a),i.translate(e.x,e.y),this.push(i,!1),this.drawText(t,r),this.pop()}_buildHatchPolyline(t,e,r){let i=(void 0!==t.separation?t.separation:4)*r,s=void 0!==t.rotation?t.rotation:0;if(0===i)return null;i<0&&(i=-i);let o=0;const n=.5*i;for(;o>n;)o-=i;for(;o<-n;)o+=i;const a=h();m(a,e),a[0]-=n,a[1]-=n,a[2]+=n,a[3]+=n;const l=[[a[0],a[1]],[a[0],a[3]],[a[2],a[3]],[a[2],a[1]]];for(;s>180;)s-=180;for(;s<0;)s+=180;const c=Math.cos(s*E),f=Math.sin(s*E),u=-i*f,d=i*c;let p,_,g,y;o=(void 0!==t.offsetX?t.offsetX*r:0)*f-(void 0!==t.offsetY?t.offsetY*r:0)*c,p=g=Number.MAX_VALUE,_=y=-Number.MAX_VALUE;for(const h of l){const t=h[0],e=h[1],r=c*t+f*e,i=-f*t+c*e;p=Math.min(p,r),g=Math.min(g,i),_=Math.max(_,r),y=Math.max(y,i)}g=Math.floor(g/i)*i;let P=c*p-f*g-u*o/i,w=f*p+c*g-d*o/i,x=c*_-f*g-u*o/i,S=f*_+c*g-d*o/i;const M=1+Math.round((y-g)/i),b=[];for(let h=0;h<M;h++)P+=u,w+=d,x+=u,S+=d,b.push([[P,w],[x,S]]);return{paths:b}}}class q extends Y{constructor(t,e){super(t,e),this.reset()}reset(){this._xmin=this._ymin=1/0,this._xmax=this._ymax=-1/0,this._clipCount=0}envelope(){return new C(this._xmin,this._ymin,this._xmax-this._xmin,this._ymax-this._ymin)}bounds(){return c(this._xmin,this._ymin,this._xmax,this._ymax)}drawSolidFill(t){if(t&&!(this._clipCount>0))if(d(t))this._processPath(t.rings,0);else if(p(t))this._processPath(t.paths,0);else if(_(t)){const e=Q(t);e&&this._processPath(e.rings,0)}else console.error(\"drawSolidFill Unexpected geometry type!\")}drawSolidStroke(t,e,r){if(!t||this._clipCount>0)return;const i=.5*this.transformSize(r??0);if(d(t))this._processPath(t.rings,i);else if(p(t))this._processPath(t.paths,i);else if(_(t)){const e=Q(t);e&&this._processPath(e.rings,i)}else console.error(\"drawSolidStroke unexpected geometry type!\")}drawMarkerLayer(t,e){d(e)&&t.markerPlacement&&(\"CIMMarkerPlacementInsidePolygon\"===t.markerPlacement.type||\"CIMMarkerPlacementPolygonCenter\"===t.markerPlacement.type&&t.markerPlacement.clipAtBoundary)?this._processPath(e.rings,0):super.drawMarkerLayer(t,e)}drawHatchFill(t,e){this.drawSolidFill(t)}drawPictureFill(t,e){this.drawSolidFill(t)}drawGradientFill(t,e){this.drawSolidFill(t)}drawPictureStroke(t,e){this.drawSolidStroke(t,null,e.width)}drawGradientStroke(t,e){this.drawSolidStroke(t,null,e.width)}pushClipPath(t){this.drawSolidFill(t),this._clipCount++}popClipPath(){this._clipCount--}drawImage(t,e){const{url:r}=t,i=t.scaleX??1;let s=i*e,n=e;const a=this._resourceManager.getResource(r);!e&&o(a)&&(s=i*a.width,n=a.height),this._merge(this.transformPt([-s/2,-n/2]),0),this._merge(this.transformPt([-s/2,n/2]),0),this._merge(this.transformPt([s/2,-n/2]),0),this._merge(this.transformPt([s/2,n/2]),0)}drawText(t,e){if(!e||0===e.length)return;this._textRasterizer||(this._textRasterizer=new I);const r=et(t),[i,s]=this._textRasterizer.computeTextSize(e,r);let o=0;switch(t.horizontalAlignment){case\"Left\":o=i/2;break;case\"Right\":o=-i/2}let n=0;switch(t.verticalAlignment){case\"Bottom\":n=s/2;break;case\"Top\":n=-s/2;break;case\"Baseline\":n=s/6}this._merge(this.transformPt([-i/2+o,-s/2+n]),0),this._merge(this.transformPt([-i/2+o,s/2+n]),0),this._merge(this.transformPt([i/2+o,-s/2+n]),0),this._merge(this.transformPt([i/2+o,s/2+n]),0)}_processPath(t,e){if(t)for(const r of t){const t=r?r.length:0;if(t>1){this._merge(this.transformPt(r[0]),e);for(let i=1;i<t;i++)this._merge(this.transformPt(r[i]),e)}}}_merge(t,e){t[0]-e<this._xmin&&(this._xmin=t[0]-e),t[0]+e>this._xmax&&(this._xmax=t[0]+e),t[1]-e<this._ymin&&(this._ymin=t[1]-e),t[1]+e>this._ymax&&(this._ymax=t[1]+e)}}class V extends Y{constructor(){super(...arguments),this._searchPoint=[0,0],this._searchDistPoint=0,this._textInfo=null}hitTest(t,e,r,i,s,o){const n=o*l(1);this.setTransform(),this.setGeomUnitsPerPoint(n),this._searchPoint=[(t[0]+t[2])/2,(t[1]+t[3])/2],this._searchDistPoint=(t[2]-t[0])/2/n,this._textInfo=i;const a=e&&(\"CIMPointSymbol\"===e.type&&\"Map\"!==e.angleAlignment||\"CIMTextSymbol\"===e.type);return this._mapRotation=a?s:0,this._earlyReturn=!1,this.drawSymbol(e,r),this._earlyReturn}drawSolidFill(t,e){this._hitTestFill(t)}drawHatchFill(t,e){this._hitTestFill(t)}drawPictureFill(t,e){this._hitTestFill(t)}drawGradientFill(t,e){this._hitTestFill(t)}drawSolidStroke(t,e,r,i,s,o){this._hitTestStroke(t,r)}drawPictureStroke(t,e){this._hitTestStroke(t,e.width)}drawGradientStroke(t,e){this._hitTestStroke(t,e.width)}drawMarkerLayer(t,e){t.markerPlacement&&(\"CIMMarkerPlacementInsidePolygon\"===t.markerPlacement.type||\"CIMMarkerPlacementPolygonCenter\"===t.markerPlacement.type&&t.markerPlacement.clipAtBoundary)?this._hitTestFill(e):super.drawMarkerLayer(t,e)}pushClipPath(t){}popClipPath(){}drawImage(t,e){const{url:r}=t,i=t.scaleX??1,o=this._resourceManager.getResource(r);if(s(o)||0===o.height||0===e)return;const n=e*this.geomUnitsPerPoint(),a=n*i*(o.width/o.height),l=this.reverseTransformPt(this._searchPoint),h=this._searchDistPoint;Math.abs(l[0])<a/2+h&&Math.abs(l[1])<n/2+h&&(this._earlyReturn=!0)}drawText(e,r){const i=this._textInfo;if(!i)return;const s=i.get(e);if(!s)return;const{text:o,mosaicItem:n}=s;if(!n?.glyphMosaicItems?.length)return;const a=e.height??J,{lineGapType:l,lineGap:h}=e,c=l?tt(l,h??0,a):0,m=t(o)[1],f=n.glyphMosaicItems,u=\"CIMBackgroundCallout\"===e.callout?.type,d=X(f,m,{scale:a/D,angle:0,xOffset:0,yOffset:0,hAlign:Z(e.horizontalAlignment),vAlign:$(e.verticalAlignment),maxLineWidth:512,lineHeight:G*Math.max(.25,Math.min(c||1,4)),decoration:e.font.decoration||\"none\",isCIM:!0,hasBackground:u}),p=this.reverseTransformPt(this._searchPoint),_=p[0],g=p[1];for(const t of d.glyphs)if(_>t.xTopLeft&&_<t.xBottomRight&&g>-t.yBottomRight&&g<-t.yTopLeft){this._earlyReturn=!0;break}}_hitTestFill(t){let e=null;if(_(t)){const r=t;e=[[[r.xmin,r.ymin],[r.xmin,r.ymax],[r.xmax,r.ymax],[r.xmax,r.ymin],[r.xmin,r.ymin]]]}else if(d(t))e=t.rings;else{if(!p(t))return;e=t.paths}const r=this.reverseTransformPt(this._searchPoint);if(this._pointInPolygon(r,e)&&(this._earlyReturn=!0),!this._earlyReturn){const t=this.reverseTransformSize(this._searchDistPoint)*this.geomUnitsPerPoint();this._nearLine(r,e,t)&&(this._earlyReturn=!0)}}_hitTestStroke(t,e){let r=null;if(_(t)){const e=t;r=[[[e.xmin,e.ymin],[e.xmin,e.ymax],[e.xmax,e.ymax],[e.xmax,e.ymin],[e.xmin,e.ymin]]]}else if(d(t))r=t.rings;else{if(!p(t))return;r=t.paths}const i=this.reverseTransformPt(this._searchPoint),s=(e??0)*this.geomUnitsPerPoint(),o=this.reverseTransformSize(this._searchDistPoint)*this.geomUnitsPerPoint();this._nearLine(i,r,s/2+o)&&(this._earlyReturn=!0)}_pointInPolygon(t,e){let r=0;for(const i of e){const e=i.length;for(let s=1;s<e;s++){const e=i[s-1],o=i[s];if(e[1]>t[1]==o[1]>t[1])continue;(o[0]-e[0])*(t[1]-e[1])-(o[1]-e[1])*(t[0]-e[0])>0?r++:r--}}return 0!==r}_nearLine(t,e,r){for(const i of e){const e=i.length;for(let s=1;s<e;s++){const e=i[s-1],o=i[s];let n=(o[0]-e[0])*(o[0]-e[0])+(o[1]-e[1])*(o[1]-e[1]);if(0===n)continue;n=Math.sqrt(n);const a=((o[0]-e[0])*(t[1]-e[1])-(o[1]-e[1])*(t[0]-e[0]))/n;if(Math.abs(a)<r){const i=((o[0]-e[0])*(t[0]-e[0])+(o[1]-e[1])*(t[1]-e[1]))/n;if(i>-r&&i<n+r)return!0}}}return!1}}class W extends Y{constructor(t,e,r,i){super(e,r),this._applyAdditionalRenderProps=i,this._colorSubstitutionHelper=new P,this._ctx=t}drawSolidFill(t,e){if(!t)return;if(d(t))this._buildPath(t.rings,!0);else if(p(t))this._buildPath(t.paths,!0);else if(_(t))this._buildPath(Q(t).rings,!0);else{if(!g(t))return;console.log(\"CanvasDrawHelper.drawSolidFill - No implementation!\")}const r=this._ctx;r.fillStyle=\"string\"==typeof e?e:\"rgba(\"+Math.round(e[0])+\",\"+Math.round(e[1])+\",\"+Math.round(e[2])+\",\"+(e[3]??255)/255+\")\",r.fill(\"evenodd\")}drawSolidStroke(t,e,r,i,s,o){if(!t||!e||0===r)return;if(d(t))this._buildPath(t.rings,!0);else if(p(t))this._buildPath(t.paths,!1);else{if(!_(t))return void console.log(\"CanvasDrawHelper.drawSolidStroke isn't implemented!\");this._buildPath(Q(t).rings,!0)}const n=this._ctx;n.strokeStyle=\"string\"==typeof e?e:\"rgba(\"+Math.round(e[0])+\",\"+Math.round(e[1])+\",\"+Math.round(e[2])+\",\"+(e[3]??255)/255+\")\",n.lineWidth=Math.max(this.transformSize(r),.5),this._setCapStyle(i),this._setJoinStyle(s),n.miterLimit=o,n.stroke()}pushClipPath(t){if(this._ctx.save(),d(t))this._buildPath(t.rings,!0);else if(p(t))this._buildPath(t.paths,!0);else{if(!_(t))return;this._buildPath(Q(t).rings,!0)}this._ctx.clip(\"evenodd\")}popClipPath(){this._ctx.restore()}drawImage(t,e){const{colorSubstitutions:r,url:i,tintColor:o}=t,n=t.scaleX??1,a=this._resourceManager.getResource(i);if(s(a))return;let l=e*(a.width/a.height),h=e;e||(l=a.width,h=a.height);const c=T(i)||\"src\"in a&&T(a.src);let m=\"getFrame\"in a?k(a):a;r&&(m=this._colorSubstitutionHelper.applyColorSubstituition(m,r)),this._applyAdditionalRenderProps&&!c&&o&&(m=this._colorSubstitutionHelper.tintImageData(m,o));const f=this.transformPt([0,0]),[u,d]=this.getTransformAngle(),p=this.transformSize(1),_=this._ctx;_.save(),_.setTransform({m11:n*p*u,m12:n*p*d,m21:-p*d,m22:p*u,m41:f[0],m42:f[1]}),_.drawImage(m,-l/2,-h/2,l,h),_.restore()}drawText(t,e){if(!e||0===e.length)return;this._textRasterizer||(this._textRasterizer=new I);const r=et(t);r.size*=this.transformSize(a(1));const i=this._textRasterizer.rasterizeText(e,r);if(!i)return;const{size:s,anchorX:o,anchorY:n,canvas:l}=i,h=s[0]*(o+.5),c=s[1]*(n-.5),m=this._ctx,f=this.transformPt([0,0]),[u,d]=this.getTransformAngle(),p=1;m.save(),m.setTransform({m11:p*u,m12:p*d,m21:-p*d,m22:p*u,m41:f[0]-p*h,m42:f[1]+p*c}),m.drawImage(l,0,0),m.restore()}drawPictureFill(t,e){if(!t)return;let{colorSubstitutions:r,height:i,offsetX:o,offsetY:n,rotation:a,scaleX:l,tintColor:h,url:c}=e;const m=this._resourceManager.getResource(c);if(s(m))return;if(d(t))this._buildPath(t.rings,!0);else if(p(t))this._buildPath(t.paths,!0);else if(_(t))this._buildPath(Q(t).rings,!0);else{if(!g(t))return;console.log(\"CanvasDrawHelper.drawPictureFill - No implementation!\")}const f=this._ctx,u=T(c)||\"src\"in m&&T(m.src);let y,P=\"getFrame\"in m?k(m):m;if(r&&(P=this._colorSubstitutionHelper.applyColorSubstituition(P,r)),this._applyAdditionalRenderProps){u||h&&(P=this._colorSubstitutionHelper.tintImageData(P,h)),y=f.createPattern(P,\"repeat\");const t=this.transformSize(1);a||(a=0),o?o*=t:o=0,n?n*=t:n=0,i&&(i*=t);const e=i?i/m.height:1,r=l&&i?l*i/m.width:1;if(0!==a||1!==e||1!==r||0!==o||0!==n){const t=new DOMMatrix;t.rotateSelf(0,0,-a).translateSelf(o,n).scaleSelf(r,e,1),y.setTransform(t)}}else y=f.createPattern(P,\"repeat\");f.save(),f.fillStyle=y,f.fill(\"evenodd\"),f.restore()}drawPictureStroke(t,e){if(!t)return;let{colorSubstitutions:i,capStyle:o,joinStyle:n,miterLimit:a,tintColor:h,url:c,width:m}=e;const f=this._resourceManager.getResource(c);if(s(f))return;let u;if(d(t))u=t.rings;else if(p(t))u=t.paths;else{if(!_(t))return g(t)?void console.log(\"CanvasDrawHelper.drawPictureStroke - No implementation!\"):void 0;u=Q(t).rings}m||(m=f.width);const y=T(c)||\"src\"in f&&T(f.src);let P=\"getFrame\"in f?k(f):f;i&&(P=this._colorSubstitutionHelper.applyColorSubstituition(P,i)),this._applyAdditionalRenderProps&&(y||h&&(P=this._colorSubstitutionHelper.tintImageData(P,h)));const w=Math.max(this.transformSize(l(m)),.5),x=w/P.width,S=this._ctx,M=S.createPattern(P,\"repeat-y\");let b,C;S.save(),this._setCapStyle(o),this._setJoinStyle(n),void 0!==a&&(S.miterLimit=a),S.lineWidth=w;for(let s of u)if(s=r(s),it(s),s&&!(s.length<=1)){b=this.transformPt(s[0]);for(let t=1;t<s.length;t++){C=this.transformPt(s[t]);const e=K(b,C),r=new DOMMatrix;r.translateSelf(0,b[1]-w/2).scaleSelf(x,x,1).rotateSelf(0,0,90-e),M.setTransform(r),S.strokeStyle=M,S.beginPath(),S.moveTo(b[0],b[1]),S.lineTo(C[0],C[1]),S.stroke(),b=C}}S.restore()}_buildPath(t,e){const r=this._ctx;if(r.beginPath(),t)for(const i of t){const t=i?i.length:0;if(t>1){let s=this.transformPt(i[0]);r.moveTo(s[0],s[1]);for(let e=1;e<t;e++)s=this.transformPt(i[e]),r.lineTo(s[0],s[1]);e&&r.closePath()}}}_setCapStyle(t){switch(t){case M.Butt:this._ctx.lineCap=\"butt\";break;case M.Round:this._ctx.lineCap=\"round\";break;case M.Square:this._ctx.lineCap=\"square\"}}_setJoinStyle(t){switch(t){case b.Bevel:this._ctx.lineJoin=\"bevel\";break;case b.Round:this._ctx.lineJoin=\"round\";break;case b.Miter:this._ctx.lineJoin=\"miter\"}}}function K(t,e){const r=e[0]-t[0],i=e[1]-t[1];return 180/Math.PI*Math.atan2(i,r)}const Q=t=>t?{spatialReference:t.spatialReference,rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]}:null,Z=t=>{switch(t){case\"Left\":return H.Left;case\"Right\":return H.Right;case\"Center\":return H.Center;case\"Justify\":return N.warnOnce(\"Horizontal alignment 'justify' is not implemented. Falling back to 'center'.\"),H.Center}},$=t=>{switch(t){case\"Top\":return B.Top;case\"Center\":return B.Center;case\"Bottom\":return B.Bottom;case\"Baseline\":return B.Baseline}},tt=(t,e,r)=>{switch(t){case\"ExtraLeading\":return 1+e/r;case\"Multiple\":return e;case\"Exact\":return e/r}};function et(t,r=1){const i=R(t),s=v(t.fontStyleName),o=e(t.fontFamilyName),{weight:n,style:a}=s,l=r*(t.height||5),h=z(t.horizontalAlignment),c=F(t.verticalAlignment),m=L(t),f=A(t.haloSymbol),u=f?r*(0|t.haloSize):0;return{color:m,size:l,horizontalAlignment:h,verticalAlignment:c,font:{family:o,style:j(a),weight:U(n),decoration:i},halo:{size:u||0,color:f,style:a},pixelRatio:1,premultiplyColors:!0}}const rt=1e-4;function it(t){let e,r,i,s,o,n=t[0],a=1;for(;a<t.length;)e=t[a][0]-n[0],r=t[a][1]-n[1],s=0!==e?r/e:Math.PI/2,void 0!==i&&s-i<=rt?(t.splice(a-1,1),n=o):(o=n,n=t[a],a++),i=s}export{Y as CIMSymbolDrawHelper,E as C_DEG_TO_RAD,W as CanvasDrawHelper,q as EnvDrawHelper,V as HittestDrawHelper,O as Transformation,Z as horizontalAlignment2HAlign,tt as lineGapType2LineHeight,$ as verticalAlignment2VAlign};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../Color.js\";import{bidiText as t}from\"../../core/BidiText.js\";import{clone as r}from\"../../core/lang.js\";import a from\"../../core/Logger.js\";import{isSome as o,isNone as i}from\"../../core/maybe.js\";import s from\"../../core/RandomLCG.js\";import{pt2px as n,px2pt as l}from\"../../core/screenUtils.js\";import{create as c,expandPointInPlace as m}from\"../../geometry/support/aaBoundingRect.js\";import{getBoundsXY as f}from\"../../geometry/support/boundsUtils.js\";import{createRendererExpression as y}from\"../../support/arcadeOnDemand.js\";import{Placement as u}from\"./CIMPlacements.js\";import{horizontalAlignment2HAlign as h,verticalAlignment2VAlign as p,EnvDrawHelper as M,Transformation as d,CanvasDrawHelper as S,lineGapType2LineHeight as b}from\"./CIMSymbolDrawHelper.js\";import{LineCapStyle as g,LineJoinStyle as C,BlockProgression as I,FontEffects as k,FontEncoding as x,FontType as w,GlyphHinting as P,TextReadingDirection as L,VerticalGlyphOrientation as v,BillBoardMode as D,TextureFilter as F,ExtremityPlacement as T}from\"./enums.js\";import{getValue as z,isCIMMarkerStrokePlacement as G,attributesToFields as E,analyzeTextParts as O,assignTextValuesFromFeature as A,isGeometryEngineRequired as N,importGeometryEngine as R}from\"./utils.js\";import j from\"../../views/2d/arcade/callExpressionWithFeature.js\";import{C_INFINITY as V}from\"../../views/2d/engine/vectorTiles/GeometryUtils.js\";import{GLYPH_SIZE as B,MAGIC_LABEL_LINE_HEIGHT as X,RANDOM_INSIDE_POLYGON_TEXTURE_SIZE as _}from\"../../views/2d/engine/webgl/definitions.js\";import{shapeGlyphs as H}from\"../../views/2d/engine/webgl/mesh/templates/shapingUtils.js\";const Y=Math.PI,$=Y/2,U=4,q=4,W=10,J=96/72,K=Math.PI/180,Q=a.getLogger(\"esri.symbols.cim.CIMSymbolHelper\");function Z(e){if(!e||!e.type)return null;let t;switch(e.type){case\"cim\":return e.data;case\"web-style\":return e;case\"simple-marker\":{const r=ie.fromSimpleMarker(e);if(!r)return null;t=r;break}case\"picture-marker\":t=ie.fromPictureMarker(e);break;case\"simple-line\":t=ie.fromSimpleLineSymbol(e);break;case\"simple-fill\":t=ie.fromSimpleFillSymbol(e);break;case\"picture-fill\":t=ie.fromPictureFillSymbol(e);break;case\"text\":t=ie.fromTextSymbol(e)}return{type:\"CIMSymbolReference\",symbol:t}}function ee(e,t,r){switch(t.type){case\"CIMSymbolReference\":return ee(e,t.symbol,r);case\"CIMPointSymbol\":null==r&&(r={x:0,y:0}),e.drawSymbol(t,r);break;case\"CIMLineSymbol\":null==r&&(r={paths:[[[0,0],[10,0]]]}),e.drawSymbol(t,r);break;case\"CIMPolygonSymbol\":null==r&&(r={rings:[[[0,0],[0,10],[10,10],[10,0],[0,0]]]}),e.drawSymbol(t,r);break;case\"CIMTextSymbol\":{const r={x:0,y:0};e.drawSymbol(t,r);break}case\"CIMVectorMarker\":{const r=new u;e.drawMarker(t,r);break}}return e.envelope()}function te(e){if(!e)return 0;switch(e.type){case\"CIMMarkerPlacementAlongLineSameSize\":case\"CIMMarkerPlacementAlongLineRandomSize\":case\"CIMMarkerPlacementAtExtremities\":case\"CIMMarkerPlacementAtMeasuredUnits\":case\"CIMMarkerPlacementAtRatioPositions\":case\"CIMMarkerPlacementOnLine\":case\"CIMMarkerPlacementOnVertices\":return Math.abs(e.offset);default:return 0}}function re(e){if(!e)return 0;switch(e.type){case\"CIMGeometricEffectArrow\":return Math.abs(.5*e.width);case\"CIMGeometricEffectBuffer\":return Math.abs(e.size);case\"CIMGeometricEffectExtension\":case\"CIMGeometricEffectRadial\":return Math.abs(e.length);case\"CIMGeometricEffectJog\":return Math.abs(.5*e.length);case\"CIMGeometricEffectMove\":return Math.max(Math.abs(z(e.offsetX)),Math.abs(z(e.offsetY)));case\"CIMGeometricEffectOffset\":case\"CIMGeometricEffectOffsetTangent\":return Math.abs(e.offset);case\"CIMGeometricEffectRegularPolygon\":return Math.abs(e.radius);case\"CIMGeometricEffectRotate\":case\"CIMGeometricEffectScale\":default:return 0;case\"CIMGeometricEffectTaperedPolygon\":return.5*Math.max(Math.abs(e.fromWidth),Math.abs(e.toWidth));case\"CIMGeometricEffectWave\":return Math.abs(e.amplitude)}}function ae(e){if(!e)return 0;let t=0;for(const r of e)t+=re(r);return t}class oe{getSymbolInflateSize(e,t,r,a,o){return e||(e=[0,0,0,0]),t?this._getInflateSize(e,t,r,a,o):e}static safeSize(e){const t=Math.max(Math.abs(e[0]),Math.abs(e[2])),r=Math.max(Math.abs(e[1]),Math.abs(e[3]));return Math.sqrt(t*t+r*r)}_vectorMarkerBounds(e,t,r,a){let o=!0;const i=c();if(t&&t.markerGraphics)for(const s of t.markerGraphics){const t=[0,0,0,0];s.geometry&&(f(i,s.geometry),t[0]=0,t[1]=0,t[2]=0,t[3]=0,this.getSymbolInflateSize(t,s.symbol,r,0,a),i[0]+=t[0],i[1]+=t[1],i[2]+=t[2],i[3]+=t[3],o?(e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],o=!1):(e[0]=Math.min(e[0],i[0]),e[1]=Math.min(e[1],i[1]),e[2]=Math.max(e[2],i[2]),e[3]=Math.max(e[3],i[3])))}return e}_getInflateSize(e,t,r,a,o){if(Me(t)){const i=this._getLayersInflateSize(e,t.symbolLayers,r,a,o),s=ae(t.effects);return s>0&&(i[0]-=s,i[1]-=s,i[2]+=s,i[3]+=s),i}return this._getTextInflatedSize(e,t,o)}_getLayersInflateSize(e,t,r,a,i){let s=!0;if(!t)return e;for(const n of t){if(!n)continue;let t=[0,0,0,0];switch(n.type){case\"CIMSolidFill\":case\"CIMPictureFill\":case\"CIMHatchFill\":case\"CIMGradientFill\":break;case\"CIMSolidStroke\":case\"CIMPictureStroke\":case\"CIMGradientStroke\":{const e=n;let r=e.width;null!=r&&(e.capStyle===g.Square||e.joinStyle===C.Miter?r/=1.4142135623730951:r/=2,t[0]=-r,t[1]=-r,t[2]=r,t[3]=r);break}case\"CIMCharacterMarker\":case\"CIMVectorMarker\":case\"CIMPictureMarker\":{const e=n;if(\"CIMVectorMarker\"===n.type){const e=n;if(t=this._vectorMarkerBounds(t,e,r,i),e.frame){const r=(e.frame.xmin+e.frame.xmax)/2,a=(e.frame.ymin+e.frame.ymax)/2;if(t[0]-=r,t[1]-=a,t[2]-=r,t[3]-=a,null!=e.size){const r=e.size/(e.frame.ymax-e.frame.ymin);t[0]*=r,t[1]*=r,t[2]*=r,t[3]*=r}}}else if(\"CIMPictureMarker\"===n.type){const a=n,i=r.getResource(a.url);let s=1;if(o(i)&&i.height&&(s=i.width/i.height),null!=e.size){const r=e.size/2,o=e.size*s*a.scaleX/2;t=[-o,-r,o,r]}}else if(null!=e.size){const r=e.size/2;t=[-r,-r,r,r]}if(e.anchorPoint){let r,a;\"Absolute\"===e.anchorPointUnits?(r=e.anchorPoint.x,a=e.anchorPoint.y):(r=e.anchorPoint.x*(t[2]-t[0]),a=e.anchorPoint.y*(t[3]-t[1])),t[0]-=r,t[1]-=a,t[2]-=r,t[3]-=a}let s=z(e.rotation);if(e.rotateClockwise&&(s=-s),a&&(s-=a),s){const e=K*s,r=Math.cos(e),a=Math.sin(e),o=c([V,V,-V,-V]);m(o,[t[0]*r-t[1]*a,t[0]*a+t[1]*r]),m(o,[t[0]*r-t[3]*a,t[0]*a+t[3]*r]),m(o,[t[2]*r-t[1]*a,t[2]*a+t[1]*r]),m(o,[t[2]*r-t[3]*a,t[2]*a+t[3]*r]),t=o}let l=z(e.offsetX),f=z(e.offsetY);if(a){const e=K*a,t=Math.cos(e),r=Math.sin(e),o=l*r+f*t;l=l*t-f*r,f=o}t[0]+=l,t[1]+=f,t[2]+=l,t[3]+=f;const y=te(e.markerPlacement);y>0&&(t[0]-=y,t[1]-=y,t[2]+=y,t[3]+=y);break}}const l=ae(n.effects);l>0&&(t[0]-=l,t[1]-=l,t[2]+=l,t[3]+=l),s?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],s=!1):(e[0]=Math.min(e[0],t[0]),e[1]=Math.min(e[1],t[1]),e[2]=Math.max(e[2],t[2]),e[3]=Math.max(e[3],t[3]))}return e}_getTextInflatedSize(e,r,a){const o=r.height??W;if(e[0]=-o/2,e[1]=-o/2,e[2]=o/2,e[3]=o/2,!a)return e;const i=a.get(r);if(!i)return e;const{text:s,mosaicItem:n}=i;if(!n?.glyphMosaicItems?.length)return e;const{lineGapType:l,lineGap:c}=r,m=l?b(l,c??0,o):0,f=t(s)[1],y=n.glyphMosaicItems,u=\"CIMBackgroundCallout\"===r.callout?.type,M=H(y,f,{scale:o/B,angle:z(r.angle),xOffset:z(r.offsetX),yOffset:z(r.offsetY),hAlign:h(r.horizontalAlignment),vAlign:p(r.verticalAlignment),maxLineWidth:512,lineHeight:X*Math.max(.25,Math.min(m||1,4)),decoration:r.font.decoration||\"none\",isCIM:!0,hasBackground:u}).boundsT;return e[0]=M.x-M.halfWidth,e[1]=-M.y-M.halfHeight,e[2]=M.x+M.halfWidth,e[3]=-M.y+M.halfHeight,e}}class ie{static getEnvelope(e,t,r){if(!e)return null;const a=new M(r);if(Array.isArray(e)){let r;for(const o of e)r?r.union(ee(a,o,t)):r=ee(a,o,t);return r}return ee(a,e,t)}static getTextureAnchor(e,t){const r=this.getEnvelope(e,null,t);if(!r)return[0,0,0];const a=(r.x+.5*r.width)*J,o=(r.y+.5*r.height)*J,i=r.width*J+2,s=r.height*J+2;return[-a/i,-o/s,s]}static rasterize(e,t,r,a,o=!0){const i=r||this.getEnvelope(t,null,a);if(!i)return[null,0,0,0,0];const s=(i.x+.5*i.width)*J,n=(i.y+.5*i.height)*J;e.width=i.width*J,e.height=i.height*J,r||(e.width+=2,e.height+=2);const l=e.getContext(\"2d\"),c=d.createScale(J,-J);c.translate(.5*e.width-s,.5*e.height+n);const m=new S(l,a,c);switch(t.type){case\"CIMPointSymbol\":{const e={type:\"point\",x:0,y:0};m.drawSymbol(t,e);break}case\"CIMVectorMarker\":{const e=new u;m.drawMarker(t,e);break}}const f=l.getImageData(0,0,e.width,e.height),y=new Uint8Array(f.data);if(o){let e;for(let t=0;t<y.length;t+=4)e=y[t+3]/255,y[t]=y[t]*e,y[t+1]=y[t+1]*e,y[t+2]=y[t+2]*e}return[y,e.width,e.height,-s/e.width,-n/e.height]}static fromTextSymbol(e){const{angle:r,color:a,font:o,haloColor:i,haloSize:s,horizontalAlignment:n,kerning:l,text:c,verticalAlignment:m,xoffset:f,yoffset:y,backgroundColor:u,borderLineColor:h,borderLineSize:p}=e;let M,d,S,b,g,C;o&&(M=o.family,d=o.style,S=o.weight,b=o.size,g=o.decoration);let F=!1;if(c){F=t(c)[1]}return(u||p)&&(C={type:\"CIMBackgroundCallout\",margin:null,backgroundSymbol:{type:\"CIMPolygonSymbol\",symbolLayers:[{type:\"CIMSolidFill\",color:fe(u)},{type:\"CIMSolidStroke\",color:fe(h),width:p}]},accentBarSymbol:null,gap:null,leaderLineSymbol:null,lineStyle:null}),{type:\"CIMPointSymbol\",symbolLayers:[{type:\"CIMVectorMarker\",enable:!0,anchorPointUnits:\"Relative\",dominantSizeAxis3D:\"Y\",size:10,billboardMode3D:\"FaceNearPlane\",frame:{xmin:-5,ymin:-5,xmax:5,ymax:5},markerGraphics:[{type:\"CIMMarkerGraphic\",geometry:{x:0,y:0},symbol:{type:\"CIMTextSymbol\",angle:r,blockProgression:I.BTT,depth3D:1,extrapolateBaselines:!0,fontEffects:k.Normal,fontEncoding:x.Unicode,fontFamilyName:M||\"Arial\",fontStyleName:ye(d,S),fontType:w.Unspecified,haloSize:s,height:b,hinting:P.Default,horizontalAlignment:ce(n??\"center\"),kerning:l,letterWidth:100,ligatures:!0,lineGapType:\"Multiple\",offsetX:z(f),offsetY:z(y),strikethrough:\"line-through\"===g,underline:\"underline\"===g,symbol:{type:\"CIMPolygonSymbol\",symbolLayers:[{type:\"CIMSolidFill\",enable:!0,color:fe(a)}]},haloSymbol:{type:\"CIMPolygonSymbol\",symbolLayers:[{type:\"CIMSolidFill\",enable:!0,color:fe(i)}]},shadowColor:[0,0,0,255],shadowOffsetX:1,shadowOffsetY:1,textCase:\"Normal\",textDirection:F?L.RTL:L.LTR,verticalAlignment:me(m??\"baseline\"),verticalGlyphOrientation:v.Right,wordSpacing:100,billboardMode3D:D.FaceNearPlane,callout:C},textString:c}],scaleSymbolsProportionally:!0,respectFrame:!0}],scaleX:1,angleAlignment:\"Display\"}}static fromPictureFillSymbol(e){const{height:t,outline:r,width:a,xoffset:o,xscale:i,yoffset:s,yscale:n}=e,l=[],c={type:\"CIMPolygonSymbol\",symbolLayers:l};if(r){const{cap:e,join:t,miterLimit:a,width:o}=r;l.push({type:\"CIMSolidStroke\",color:fe(r.color),capStyle:ne(e),joinStyle:le(t),miterLimit:a,width:o})}let m=e.url;\"esriPFS\"===e.type&&e.imageData&&(m=e.imageData);const f=\"angle\"in e?e.angle??0:0,y=(a??0)*(i||1),u=(t??0)*(n||1);return l.push({type:\"CIMPictureFill\",invertBackfaceTexture:!1,scaleX:1,textureFilter:F.Picture,tintColor:null,url:m,height:u,width:y,offsetX:z(o),offsetY:z(s),rotation:z(-f),colorSubstitutions:null}),c}static fromSimpleFillSymbol(e){const{color:t,style:a,outline:o}=e,i=[],s={type:\"CIMPolygonSymbol\",symbolLayers:i};let n=null;if(o){const{cap:e,join:t,style:r}=o;\"solid\"!==r&&\"none\"!==r&&\"esriSLSSolid\"!==r&&\"esriSLSNull\"!==r&&(n=[{type:\"CIMGeometricEffectDashes\",dashTemplate:pe(r,e),lineDashEnding:\"NoConstraint\",scaleDash:!0,offsetAlongLine:null}]),i.push({type:\"CIMSolidStroke\",color:fe(o.color),capStyle:ne(e),joinStyle:le(t),miterLimit:o.miterLimit,width:o.width,effects:n})}if(a&&\"solid\"!==a&&\"none\"!==a&&\"esriSFSSolid\"!==a&&\"esriSFSNull\"!==a){const e={type:\"CIMLineSymbol\",symbolLayers:[{type:\"CIMSolidStroke\",color:fe(t),capStyle:g.Butt,joinStyle:C.Miter,width:.75}]};let o=0;const s=l(Se(a)?8:10);switch(a){case\"vertical\":case\"esriSFSVertical\":o=90;break;case\"forward-diagonal\":case\"esriSFSForwardDiagonal\":case\"diagonal-cross\":case\"esriSFSDiagonalCross\":o=-45;break;case\"backward-diagonal\":case\"esriSFSBackwardDiagonal\":o=45;break;case\"cross\":case\"esriSFSCross\":o=0}i.push({type:\"CIMHatchFill\",lineSymbol:e,offsetX:0,offsetY:0,rotation:o,separation:s}),\"cross\"===a||\"esriSFSCross\"===a?i.push({type:\"CIMHatchFill\",lineSymbol:r(e),offsetX:0,offsetY:0,rotation:90,separation:s}):\"diagonal-cross\"!==a&&\"esriSFSDiagonalCross\"!==a||i.push({type:\"CIMHatchFill\",lineSymbol:r(e),offsetX:0,offsetY:0,rotation:45,separation:s})}else!a||\"solid\"!==a&&\"esriSFSSolid\"!==a||i.push({type:\"CIMSolidFill\",enable:!0,color:fe(t)});return s}static fromSimpleLineSymbol(e){const{cap:t,color:r,join:a,marker:o,miterLimit:i,style:s,width:n}=e;let l=null;\"solid\"!==s&&\"none\"!==s&&\"esriSLSSolid\"!==s&&\"esriSLSNull\"!==s&&(l=[{type:\"CIMGeometricEffectDashes\",dashTemplate:pe(s,t),lineDashEnding:\"NoConstraint\",scaleDash:!0,offsetAlongLine:null}]);const c=[];if(o){let e;switch(o.placement){case\"begin-end\":e=T.Both;break;case\"begin\":e=T.JustBegin;break;case\"end\":e=T.JustEnd;break;default:e=T.None}const t=ie.fromSimpleMarker(o,n,r).symbolLayers[0];t.markerPlacement={type:\"CIMMarkerPlacementAtExtremities\",angleToLine:!0,offset:0,extremityPlacement:e,offsetAlongLine:0},c.push(t)}return\"none\"!==s&&\"esriSLSNull\"!==s&&c.push({type:\"CIMSolidStroke\",color:fe(r),capStyle:ne(t),joinStyle:le(a),miterLimit:i,width:n,effects:l}),{type:\"CIMLineSymbol\",symbolLayers:c}}static fromPictureMarker(e){const{angle:t,height:r,width:a,xoffset:o,yoffset:i}=e;let s=e.url;return\"esriPMS\"===e.type&&e.imageData&&(s=e.imageData),{type:\"CIMPointSymbol\",symbolLayers:[{type:\"CIMPictureMarker\",invertBackfaceTexture:!1,scaleX:1,textureFilter:F.Picture,tintColor:null,url:s,size:r,width:a,offsetX:z(o),offsetY:z(i),rotation:z(-t)}]}}static fromSimpleMarker(e,t,r){const{style:a}=e,o=e.color??r;if(\"path\"===a){const t=[];if(\"outline\"in e&&e.outline){const r=e.outline;t.push({type:\"CIMSolidStroke\",enable:!0,width:n(Math.round(l(r.width))),color:fe(r.color)})}t.push({type:\"CIMSolidFill\",enable:!0,color:fe(o),path:e.path});const[r,a]=de(\"square\");return{type:\"CIMPointSymbol\",symbolLayers:[{type:\"CIMVectorMarker\",enable:!0,rotation:z(-e.angle),size:z(e.size||6),offsetX:z(e.xoffset),offsetY:z(e.yoffset),frame:r,markerGraphics:[{type:\"CIMMarkerGraphic\",geometry:a,symbol:{type:\"CIMPolygonSymbol\",symbolLayers:t}}]}]}}const[i,s]=de(a);let c;if(s&&i){const r=[];if(\"outline\"in e&&e.outline){const t=e.outline;r.push({type:\"CIMSolidStroke\",enable:!0,width:null!=t.width&&t.width>.667?n(Math.round(l(t.width))):t.width,color:fe(t.color)})}else!t||\"line-marker\"!==e.type||\"cross\"!==e.style&&\"x\"!==e.style||r.push({type:\"CIMSolidStroke\",enable:!0,width:t,color:fe(o)});r.push({type:\"CIMSolidFill\",enable:!0,color:fe(o)});const a={type:\"CIMPolygonSymbol\",symbolLayers:r};c={type:\"CIMPointSymbol\",symbolLayers:[{type:\"CIMVectorMarker\",enable:!0,rotation:z(-e.angle),size:z(e.size||6*t),offsetX:z(e.xoffset),offsetY:z(e.yoffset),frame:i,markerGraphics:[{type:\"CIMMarkerGraphic\",geometry:s,symbol:a}]}]}}return c}static fromCIMHatchFill(e,t){const a=t*(e.separation??U),o=a/2,i=r(e.lineSymbol);i.symbolLayers?.forEach((e=>{switch(e.type){case\"CIMSolidStroke\":null!=e.width&&(e.width*=t),e.effects?.forEach((e=>{\"CIMGeometricEffectDashes\"===e.type&&(e.dashTemplate=e.dashTemplate.map((e=>e*t)))}));break;case\"CIMVectorMarker\":{null!=e.size&&(e.size*=t);const r=e.markerPlacement;null!=r&&\"placementTemplate\"in r&&(r.placementTemplate=r.placementTemplate.map((e=>e*t)));break}}}));let s=this._getLineSymbolPeriod(i)||q;for(;s<q;)s*=2;const n=s/2;return{type:\"CIMVectorMarker\",frame:{xmin:-n,xmax:n,ymin:-o,ymax:o},markerGraphics:[{type:\"CIMMarkerGraphic\",geometry:{paths:[[[-n,0],[n,0]]]},symbol:i}],size:a}}static fetchResources(e,t,r){if(e&&t)switch(e.type){case\"CIMPointSymbol\":case\"CIMLineSymbol\":case\"CIMPolygonSymbol\":{const a=e.symbolLayers;if(!a)return;for(const e of a)switch(ge(e,t,r),e.type){case\"CIMPictureFill\":case\"CIMHatchFill\":case\"CIMGradientFill\":case\"CIMPictureStroke\":case\"CIMGradientStroke\":case\"CIMCharacterMarker\":case\"CIMPictureMarker\":\"url\"in e&&e.url&&r.push(t.fetchResource(e.url,null));break;case\"CIMVectorMarker\":{const a=e.markerGraphics;if(!a)continue;for(const e of a)if(e){const a=e.symbol;a&&ie.fetchResources(a,t,r)}}}}}}static _getLineSymbolPeriod(e){if(e){const t=this._getEffectsRepeat(e.effects);if(t)return t;if(e.symbolLayers)for(const r of e.symbolLayers)if(r){const e=this._getEffectsRepeat(r.effects);if(e)return e;switch(r.type){case\"CIMCharacterMarker\":case\"CIMPictureMarker\":case\"CIMVectorMarker\":case\"CIMObjectMarker3D\":case\"CIMglTFMarker3D\":{const e=this._getPlacementRepeat(r.markerPlacement);if(e)return e}}}}return 0}static _getEffectsRepeat(e){if(e)for(const t of e)if(t)switch(t.type){case\"CIMGeometricEffectDashes\":{const e=t.dashTemplate;if(e&&e.length){let t=0;for(const r of e)t+=r;return 1&e.length&&(t*=2),t}break}case\"CIMGeometricEffectWave\":return t.period;default:Q.error(`unsupported geometric effect type ${t.type}`)}return 0}static _getPlacementRepeat(e){if(e)switch(e.type){case\"CIMMarkerPlacementAlongLineSameSize\":case\"CIMMarkerPlacementAlongLineRandomSize\":case\"CIMMarkerPlacementAlongLineVariableSize\":{const t=e.placementTemplate;if(t&&t.length){let e=0;for(const r of t)e+=+r;return 1&t.length&&(e*=2),e}break}}return 0}static fromCIMInsidePolygon(e){const t=e.markerPlacement,r={...e};r.markerPlacement=null,r.anchorPoint=null;const a=Math.abs(t.stepX),o=Math.abs(t.stepY),i=(t.randomness??100)/100;let n,c,m,f;if(\"Random\"===t.gridType){const e=l(_),r=Math.max(Math.floor(e/a),1),y=Math.max(Math.floor(e/o),1);n=r*a/2,c=y*o/2,m=2*c;const u=new s(t.seed),h=i*a/1.5,p=i*o/1.5;f=[];for(let t=0;t<r;t++)for(let e=0;e<y;e++){const r=t*a-n+h*(.5-u.getFloat()),i=e*o-c+p*(.5-u.getFloat());f.push({x:r,y:i}),0===t&&f.push({x:r+2*n,y:i}),0===e&&f.push({x:r,y:i+2*c})}}else!0===t.shiftOddRows?(n=a/2,c=o,m=2*o,f=[{x:-n,y:0},{x:n,y:0},{x:0,y:c},{x:0,y:-c}]):(n=a/2,c=o/2,m=o,f=[{x:-a,y:0},{x:0,y:-o},{x:-a,y:-o},{x:0,y:0},{x:a,y:0},{x:0,y:o},{x:a,y:o},{x:-a,y:o},{x:a,y:-o}]);return{type:\"CIMVectorMarker\",frame:{xmin:-n,xmax:n,ymin:-c,ymax:c},markerGraphics:f.map((e=>({type:\"CIMMarkerGraphic\",geometry:e,symbol:{type:\"CIMPointSymbol\",symbolLayers:[r]}}))),size:m}}static getSize(e){if(e)switch(e.type){case\"CIMTextSymbol\":return e.height;case\"CIMPointSymbol\":{let t=0;if(e.symbolLayers)for(const r of e.symbolLayers)if(r)switch(r.type){case\"CIMCharacterMarker\":case\"CIMPictureMarker\":case\"CIMVectorMarker\":case\"CIMObjectMarker3D\":case\"CIMglTFMarker3D\":{const e=r.size;null!=e&&e>t&&(t=e);break}}return t}case\"CIMLineSymbol\":case\"CIMPolygonSymbol\":{let t=0;if(e.symbolLayers)for(const r of e.symbolLayers)if(r)switch(r.type){case\"CIMSolidStroke\":case\"CIMPictureStroke\":case\"CIMGradientStroke\":{const e=r.width;null!=e&&e>t&&(t=e);break}case\"CIMCharacterMarker\":case\"CIMPictureMarker\":case\"CIMVectorMarker\":case\"CIMObjectMarker3D\":case\"CIMglTFMarker3D\":if(r.markerPlacement&&G(r.markerPlacement)){const e=r.size;null!=e&&e>t&&(t=e)}}return t}}}static getMarkerScaleRatio(e){if(e&&\"CIMVectorMarker\"===e.type)if(!1!==e.scaleSymbolsProportionally&&e.frame&&null!=e.size){const t=e.frame.ymax-e.frame.ymin;return e.size/t}return 1}}class se{static findApplicableOverrides(e,t,r){if(e&&t){if(e.primitiveName){let a=!1;for(const t of r)if(t.primitiveName===e.primitiveName){a=!0;break}if(!a)for(const o of t)o.primitiveName===e.primitiveName&&r.push(o)}switch(e.type){case\"CIMPointSymbol\":case\"CIMLineSymbol\":case\"CIMPolygonSymbol\":if(e.effects)for(const a of e.effects)se.findApplicableOverrides(a,t,r);if(e.symbolLayers)for(const a of e.symbolLayers)se.findApplicableOverrides(a,t,r);break;case\"CIMTextSymbol\":break;case\"CIMSolidStroke\":case\"CIMPictureStroke\":case\"CIMGradientStroke\":case\"CIMSolidFill\":case\"CIMPictureFill\":case\"CIMHatchFill\":case\"CIMGradientFill\":case\"CIMVectorMarker\":case\"CIMCharacterMarker\":case\"CIMPictureMarker\":if(e.effects)for(const a of e.effects)se.findApplicableOverrides(a,t,r);if(e.markerPlacement&&se.findApplicableOverrides(e.markerPlacement,t,r),\"CIMVectorMarker\"===e.type){if(e.markerGraphics)for(const a of e.markerGraphics)se.findApplicableOverrides(a,t,r),se.findApplicableOverrides(a.symbol,t,r)}else\"CIMCharacterMarker\"===e.type?se.findApplicableOverrides(e.symbol,t,r):\"CIMHatchFill\"===e.type?se.findApplicableOverrides(e.lineSymbol,t,r):\"CIMPictureMarker\"===e.type&&se.findApplicableOverrides(e.animatedSymbolProperties,t,r)}}}static findEffectOverrides(e,t,r){if(!t||!e)return;const a=e.length;for(let o=0;o<a;o++){const a=e[o]?.primitiveName;if(a){let e=!1;for(const t of r)if(t.primitiveName===a){e=!0;break}if(!e)for(const o of t)o.primitiveName===a&&r.push(o)}}}static async resolveSymbolOverrides(e,t,a,o,i,s,n){if(!e||!e.symbol)return null;let{symbol:l,primitiveOverrides:c}=e;const m=!!c;if(!m&&!o)return l;l=r(l);let f=!0;if(t||(t={attributes:{}},f=!1),m){if(f||(c=c.filter((e=>!e.valueExpressionInfo?.expression.includes(\"$feature\")))),n||(c=c.filter((e=>!e.valueExpressionInfo?.expression.includes(\"$view\")))),c.length>0){const e=E(t.attributes);await se.evaluateOverrides(c,t,{spatialReference:a,fields:e,geometryType:i},s,n)}se.applyOverrides(l,c)}return o&&se.applyDictionaryTextOverrides(l,t,o),l}static async evaluateOverrides(e,t,r,a,o){if(!t)return;let i;for(const s of e){const e=s.valueExpressionInfo;if(e&&r&&r.geometryType){i||(i=[]),s.value=void 0;const n=y(e.expression,r.spatialReference,r.fields).then((e=>{s.value=j(e,t,{$view:o},r.geometryType,a)}));i.push(n)}}void 0!==i&&i.length>0&&await Promise.all(i)}static applyDictionaryTextOverrides(e,t,r,a=\"Normal\"){if(e&&e.type)switch(e.type){case\"CIMPointSymbol\":case\"CIMLineSymbol\":case\"CIMPolygonSymbol\":case\"CIMTextSymbol\":{const o=e.symbolLayers;if(!o)return;for(const i of o)i&&\"CIMVectorMarker\"===i.type&&se.applyDictionaryTextOverrides(i,t,r,\"CIMTextSymbol\"===e.type?e.textCase:a)}break;case\"CIMVectorMarker\":{const a=e.markerGraphics;if(!a)return;for(const e of a)e&&se.applyDictionaryTextOverrides(e,t,r)}break;case\"CIMMarkerGraphic\":{const o=e.textString;if(o&&o.includes(\"[\")){const i=O(o,r);e.textString=A(t,i,a)}}}}static applyOverrides(e,t,r,a){if(e.primitiveName)for(const i of t)if(i.primitiveName===e.primitiveName){const t=be(i.propertyName);if(a&&a.push({cim:e,nocapPropertyName:t,value:e[t]}),i.expression&&(i.value=se.toValue(i.propertyName,i.expression)),r){let t=!1;for(const a of r)a.primitiveName===e.primitiveName&&(t=!0);t||r.push(i)}o(i.value)&&(e[t]=i.value)}switch(e.type){case\"CIMPointSymbol\":case\"CIMLineSymbol\":case\"CIMPolygonSymbol\":if(e.effects)for(const o of e.effects)se.applyOverrides(o,t,r,a);if(e.symbolLayers)for(const o of e.symbolLayers)se.applyOverrides(o,t,r,a);break;case\"CIMTextSymbol\":break;case\"CIMSolidStroke\":case\"CIMSolidFill\":case\"CIMVectorMarker\":if(e.effects)for(const o of e.effects)se.applyOverrides(o,t,r,a);if(\"CIMVectorMarker\"===e.type&&e.markerGraphics)for(const o of e.markerGraphics)se.applyOverrides(o,t,r,a),se.applyOverrides(o.symbol,t,r,a)}}static restoreOverrides(e){for(const t of e)t.cim[t.nocapPropertyName]=t.value}static buildOverrideKey(e){let t=\"\";for(const r of e)void 0!==r.value&&(t+=`${r.primitiveName}${r.propertyName}${JSON.stringify(r.value)}`);return t}static toValue(t,r){if(\"DashTemplate\"===t)return r.split(\" \").map((e=>Number(e)));if(\"Color\"===t){const t=new e(r).toRgba();return t[3]*=255,t}return r}}const ne=e=>{if(!e)return g.Butt;switch(e){case\"butt\":return g.Butt;case\"square\":return g.Square;case\"round\":return g.Round}},le=e=>{if(!e)return C.Miter;switch(e){case\"miter\":return C.Miter;case\"round\":return C.Round;case\"bevel\":return C.Bevel}},ce=e=>{if(i(e))return\"Center\";switch(e){case\"left\":return\"Left\";case\"right\":return\"Right\";case\"center\":return\"Center\"}},me=e=>{if(i(e))return\"Center\";switch(e){case\"baseline\":return\"Baseline\";case\"top\":return\"Top\";case\"middle\":return\"Center\";case\"bottom\":return\"Bottom\"}},fe=e=>{if(!e)return[0,0,0,0];const{r:t,g:r,b:a,a:o}=e;return[t,r,a,255*o]},ye=(e,t)=>{const r=ue(t),a=he(e);return r&&a?`${r}-${a}`:`${r}${a}`},ue=e=>{if(!e)return\"\";switch(e.toLowerCase()){case\"bold\":case\"bolder\":return\"bold\"}return\"\"},he=e=>{if(!e)return\"\";switch(e.toLowerCase()){case\"italic\":case\"oblique\":return\"italic\"}return\"\"},pe=(e,t)=>{const r=\"butt\"===t;switch(e){case\"dash\":case\"esriSLSDash\":return r?[4,3]:[3,4];case\"dash-dot\":case\"esriSLSDashDot\":return r?[4,3,1,3]:[3,4,0,4];case\"dot\":case\"esriSLSDot\":return r?[1,3]:[0,4];case\"long-dash\":case\"esriSLSLongDash\":return r?[8,3]:[7,4];case\"long-dash-dot\":case\"esriSLSLongDashDot\":return r?[8,3,1,3]:[7,4,0,4];case\"long-dash-dot-dot\":case\"esriSLSDashDotDot\":return r?[8,3,1,3,1,3]:[7,4,0,4,0,4];case\"short-dash\":case\"esriSLSShortDash\":return r?[4,1]:[3,2];case\"short-dash-dot\":case\"esriSLSShortDashDot\":return r?[4,1,1,1]:[3,2,0,2];case\"short-dash-dot-dot\":case\"esriSLSShortDashDotDot\":return r?[4,1,1,1,1,1]:[3,2,0,2,0,2];case\"short-dot\":case\"esriSLSShortDot\":return r?[1,1]:[0,2];case\"solid\":case\"esriSLSSolid\":case\"none\":return Q.error(\"Unexpected: style does not require rasterization\"),[0,0];default:return Q.error(`Tried to rasterize SLS, but found an unexpected style: ${e}!`),[0,0]}};function Me(e){return void 0!==e.symbolLayers}const de=e=>{const t=100,r=50;let a,o;const i=e;if(\"circle\"===i||\"esriSMSCircle\"===i){const e=.25;let t=Math.acos(1-e/r),i=Math.ceil(Y/t/4);0===i&&(i=1),t=$/i,i*=4;const s=[];s.push([r,0]);for(let a=1;a<i;a++)s.push([r*Math.cos(a*t),-r*Math.sin(a*t)]);s.push([r,0]),a={rings:[s]},o={xmin:-r,ymin:-r,xmax:r,ymax:r}}else if(\"cross\"===i||\"esriSMSCross\"===i){const e=0;a={rings:[[[e,r],[e,e],[r,e],[r,-e],[e,-e],[e,-r],[-e,-r],[-e,-e],[-r,-e],[-r,e],[-e,e],[-e,r],[e,r]]]},o={xmin:-r,ymin:-r,xmax:r,ymax:r}}else if(\"diamond\"===i||\"esriSMSDiamond\"===i)a={rings:[[[-r,0],[0,r],[r,0],[0,-r],[-r,0]]]},o={xmin:-r,ymin:-r,xmax:r,ymax:r};else if(\"square\"===i||\"esriSMSSquare\"===i)a={rings:[[[-r,-r],[-r,r],[r,r],[r,-r],[-r,-r]]]},o={xmin:-r,ymin:-r,xmax:r,ymax:r};else if(\"x\"===i||\"esriSMSX\"===i){const e=0;a={rings:[[[0,e],[r-e,r],[r,r-e],[e,0],[r,e-r],[r-e,-r],[0,-e],[e-r,-r],[-r,e-r],[-e,0],[-r,r-e],[e-r,r],[0,e]]]},o={xmin:-r,ymin:-r,xmax:r,ymax:r}}else if(\"triangle\"===i||\"esriSMSTriangle\"===i){const e=t*.5773502691896257,r=-e,i=2/3*t,s=i-t;a={rings:[[[r,s],[0,i],[e,s],[r,s]]]},o={xmin:r,ymin:s,xmax:e,ymax:i}}else\"arrow\"===i&&(a={rings:[[[-50,50],[50,0],[-50,-50],[-33,-20],[-33,20],[-50,50]]]},o={xmin:-r,ymin:-r,xmax:r,ymax:r});return[o,a]},Se=e=>\"vertical\"===e||\"horizontal\"===e||\"cross\"===e||\"esriSFSCross\"===e||\"esriSFSVertical\"===e||\"esriSFSHorizontal\"===e,be=e=>e?e.charAt(0).toLowerCase()+e.substr(1):e;function ge(e,t,r){if(!e.effects||o(t.geometryEngine))return;if(t.geometryEnginePromise)return void r.push(t.geometryEnginePromise);N(e.effects)&&(t.geometryEnginePromise=R(),r.push(t.geometryEnginePromise),t.geometryEnginePromise.then((e=>t.geometryEngine=e)))}export{ie as CIMSymbolHelper,oe as CIMSymbolInflatedSizeHelper,se as OverrideHelper,pe as slsDashToTemplateArray,Z as symbolToCIM};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst t=.05;function n(n){return Math.max(Math.round(n/t),1)*t}const e=new Set([\"StartTimeOffset\",\"Duration\",\"RepeatDelay\"]);function a(t,a){return e.has(a)?n(t):t}export{a as quantizeIfNeeded};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{packFloatRGBA as t}from\"../../core/floatRGBA.js\";import{isExtent as o}from\"../../geometry/support/jsonUtils.js\";import n from\"./Rect.js\";function r(t){if(!t)return null;switch(t.type){case\"CIMPointSymbol\":{const o=t.symbolLayers;return o&&1===o.length?r(o[0]):null}case\"CIMVectorMarker\":{const o=t.markerGraphics;if(!o||1!==o.length)return null;const n=o[0];if(!n)return null;const r=n.geometry;if(!r)return null;const l=n.symbol;return!l||\"CIMPolygonSymbol\"!==l.type&&\"CIMLineSymbol\"!==l.type||l.symbolLayers?.some((t=>!!t.effects))?null:{geom:r,asFill:\"CIMPolygonSymbol\"===l.type}}case\"sdf\":return{geom:t.geom,asFill:t.asFill}}return null}function l(t){return t?t.rings?t.rings:t.paths?t.paths:void 0!==t.xmin&&void 0!==t.ymin&&void 0!==t.xmax&&void 0!==t.ymax?[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]:null:null}function e(t){let o=1/0,r=-1/0,l=1/0,e=-1/0;for(const n of t)for(const t of n)t[0]<o&&(o=t[0]),t[0]>r&&(r=t[0]),t[1]<l&&(l=t[1]),t[1]>e&&(e=t[1]);return new n(o,l,r-o,e-l)}function s(t){let o=1/0,n=-1/0,r=1/0,l=-1/0;for(const e of t)for(const t of e)t[0]<o&&(o=t[0]),t[0]>n&&(n=t[0]),t[1]<r&&(r=t[1]),t[1]>l&&(l=t[1]);return[o,r,n,l]}function i(t){return t?t.rings?s(t.rings):t.paths?s(t.paths):o(t)?[t.xmin,t.ymin,t.xmax,t.ymax]:null:null}function f(t,o,n,r,l){const[e,s,i,f]=t;if(i<e||f<s)return[0,0,0];const a=i-e,m=f-s,u=128,c=1,h=Math.floor(.5*(.5*u-c)),y=(u-2*(h+c))/Math.max(a,m),x=Math.round(a*y)+2*h,M=Math.round(m*y)+2*h;let g=1;if(o){g=M/y/(o.ymax-o.ymin)}let p=0,d=0,b=1;r&&(l?o&&n&&o.ymax-o.ymin>0&&(b=(o.xmax-o.xmin)/(o.ymax-o.ymin),p=r.x/(n*b),d=r.y/n):(p=r.x,d=r.y)),o&&(p=.5*(o.xmax+o.xmin)+p*(o.xmax-o.xmin),d=.5*(o.ymax+o.ymin)+d*(o.ymax-o.ymin)),p-=e,d-=s,p*=y,d*=y,p+=h,d+=h;let w=p/x-.5,F=d/M-.5;return l&&n&&(w*=n*b,F*=n),[g,w,F]}function a(t){const o=l(t.geom),n=e(o),r=128,s=1,i=Math.floor(.5*(.5*r-s)),f=(r-2*(i+s))/Math.max(n.width,n.height),a=Math.round(n.width*f)+2*i,h=Math.round(n.height*f)+2*i,y=[];for(const l of o)if(l&&l.length>1){const o=[];for(const r of l){let[l,e]=r;l-=n.x,e-=n.y,l*=f,e*=f,l+=i-.5,e+=i-.5,t.asFill?o.push([l,e]):o.push([Math.round(l),Math.round(e)])}if(t.asFill){const t=o.length-1;o[0][0]===o[t][0]&&o[0][1]===o[t][1]||o.push(o[0])}y.push(o)}const x=m(y,a,h,i);return t.asFill&&u(y,a,h,i,x),[c(x,i),a,h]}function m(t,o,n,r){const l=o*n,e=new Array(l),s=r*r+1;for(let i=0;i<l;++i)e[i]=s;for(const i of t){const t=i.length;for(let l=1;l<t;++l){const t=i[l-1],s=i[l];let f,a,m,u;t[0]<s[0]?(f=t[0],a=s[0]):(f=s[0],a=t[0]),t[1]<s[1]?(m=t[1],u=s[1]):(m=s[1],u=t[1]);let c=Math.floor(f)-r,h=Math.floor(a)+r,y=Math.floor(m)-r,x=Math.floor(u)+r;c<0&&(c=0),h>o&&(h=o),y<0&&(y=0),x>n&&(x=n);const M=s[0]-t[0],g=s[1]-t[1],p=M*M+g*g;for(let r=c;r<h;r++)for(let l=y;l<x;l++){let i,f,a=(r-t[0])*M+(l-t[1])*g;a<0?(i=t[0],f=t[1]):a>p?(i=s[0],f=s[1]):(a/=p,i=t[0]+a*M,f=t[1]+a*g);const m=(r-i)*(r-i)+(l-f)*(l-f),u=(n-l-1)*o+r;m<e[u]&&(e[u]=m)}}}for(let i=0;i<l;++i)e[i]=Math.sqrt(e[i]);return e}function u(t,o,n,r,l){for(const e of t){const t=e.length;for(let s=1;s<t;++s){const t=e[s-1],i=e[s];let f,a,m,u;t[0]<i[0]?(f=t[0],a=i[0]):(f=i[0],a=t[0]),t[1]<i[1]?(m=t[1],u=i[1]):(m=i[1],u=t[1]);let c=Math.floor(f),h=Math.floor(a)+1,y=Math.floor(m),x=Math.floor(u)+1;c<r&&(c=r),h>o-r&&(h=o-r),y<r&&(y=r),x>n-r&&(x=n-r);for(let e=y;e<x;++e){if(t[1]>e==i[1]>e)continue;const s=(n-e-1)*o;for(let o=c;o<h;++o)o<(i[0]-t[0])*(e-t[1])/(i[1]-t[1])+t[0]&&(l[s+o]=-l[s+o]);for(let t=r;t<c;++t)l[s+t]=-l[s+t]}}}}function c(o,n){const r=2*n,l=o.length,e=new Uint8Array(4*l);for(let s=0;s<l;++s){const n=.5-o[s]/r;t(n,e,4*s)}return e}export{a as buildSDF,i as getExtent,r as getSDFInfo,f as getSDFMetrics};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isPolyline as t,isPolygon as s}from\"../../../geometry/support/jsonUtils.js\";import{cloneAndDecodeGeometry as e,deltaEncodeGeometry as r}from\"../CIMCursor.js\";import{SimpleGeometryCursor as o}from\"../CIMEffects.js\";import{getEffectOperator as n}from\"../CIMOperators.js\";const c=96/72;class f{static executeEffects(t,s,r,f){const p=e(s),u=c;let i=new o(p);for(const e of t){const t=n(e);t&&(i=t.execute(i,e,u,r,f))}return i}static next(t){const s=t.next();return r(s),s}static applyEffects(e,r,c){if(!e)return r;let f=new o(r);for(const t of e){const s=n(t);s&&(f=s.execute(f,t,1,null,c))}let p,u=null;for(;p=f.next();)u?t(u)?t(p)&&u.paths.push(...p.paths):s(u)&&s(p)&&u.rings.push(...p.rings):u=p;return u}}export{f as CIMEffectHelper};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{numericHash as t}from\"../../../../core/string.js\";function e(e,o){let r;if(\"string\"==typeof e)r=t(e+`-seed(${o})`);else{let t=12;r=e^o;do{r=107*(r>>8^r)+t|0}while(0!=--t)}return(1+r/(1<<31))/2}function o(t){return Math.floor(e(t,r)*n)}const r=53290320,n=10;export{o as getMaterialGroup,e as getRandomValue};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../Color.js\";import{getFontFamily as t}from\"../../core/fontUtils.js\";import{clone as i}from\"../../core/lang.js\";import o from\"../../core/Logger.js\";import{isNone as r,isSome as n}from\"../../core/maybe.js\";import{pt2px as l,px2pt as a}from\"../../core/screenUtils.js\";import{numericHash as s}from\"../../core/string.js\";import{createRendererExpression as f,ArcadeExpression as c}from\"../../support/arcadeOnDemand.js\";import{CIMSymbolHelper as m,OverrideHelper as p}from\"./CIMSymbolHelper.js\";import{Alignment as u,CapType as y,JoinType as h}from\"./enums.js\";import{quantizeIfNeeded as g}from\"./quantizeTime.js\";import{getExtent as d,getSDFMetrics as S}from\"./SDFHelper.js\";import{fromCIMColor as v,getTintColor as N,getValue as b,fromCIMFontDecoration as O,fromCIMFontStyle as k,getFillColor as C,getStrokeColor as P,getStrokeWidth as M,adjustTextCase as L,createLabelOverrideFunction as I,evaluateValueOrFunction as w,fromCIMHorizontalAlignment as z,fromCIMVerticalAlignment as R}from\"./utils.js\";import{CIMEffectHelper as X}from\"./effects/CIMEffectHelper.js\";import A from\"../../views/2d/arcade/callExpressionWithFeature.js\";import{RANDOM_INSIDE_POLYGON_TEXTURE_SIZE as x}from\"../../views/2d/engine/webgl/definitions.js\";import{getMaterialGroup as J}from\"../../views/2d/engine/webgl/grouping.js\";const H=o.getLogger(\"esri.symbols.cim.cimAnalyzer\");function Y(e){switch(e){case\"Butt\":return y.BUTT;case\"Square\":return y.SQUARE;default:return y.ROUND}}function T(e){switch(e){case\"Bevel\":return h.BEVEL;case\"Miter\":return h.MITER;default:return h.ROUND}}function $(e,t,i,o){let r;e[t]?r=e[t]:(r={},e[t]=r),r[i]=o}function E(e){const t=e.markerPlacement;return t&&t.angleToLine?u.MAP:u.SCREEN}async function j(e,t,i,o,n){const l=o??[];if(!e)return l;let a,s;const c={};if(\"CIMSymbolReference\"!==e.type)return H.error(\"Expect cim type to be 'CIMSymbolReference'\"),l;if(a=e.symbol,s=e.primitiveOverrides,s){const e=[];for(const i of s){const o=i.valueExpressionInfo;if(o&&t){const n=o.expression,l=f(n,t.spatialReference,t.fields).then((e=>{r(e)||$(c,i.primitiveName,i.propertyName,e)}));e.push(l)}else null!=i.value&&$(c,i.primitiveName,i.propertyName,i.value)}e.length>0&&await Promise.all(e)}const p=[];switch(m.fetchResources(a,i,p),p.length>0&&await Promise.all(p),a?.type){case\"CIMPointSymbol\":case\"CIMLineSymbol\":case\"CIMPolygonSymbol\":F(a,s,c,t,l,i,!!n)}return l}function F(e,t,i,o,r,n,l){if(!e)return;const a=e.symbolLayers;if(!a)return;const s=e.effects;let f=u.SCREEN;const c=m.getSize(e)??0;\"CIMPointSymbol\"===e.type&&\"Map\"===e.angleAlignment&&(f=u.MAP);let y=a.length;for(;y--;){const m=a[y];if(!m||!1===m.enable)continue;let u;s&&s.length&&(u=[...s]);const h=m.effects;h&&h.length&&(s?u.push(...h):u=[...h]);const g=[];let d;p.findEffectOverrides(u,t,g),d=g.length>0?se(u,g,i,o):u;const S=[];switch(p.findApplicableOverrides(m,t,S),m.type){case\"CIMSolidFill\":U(m,d,i,S,o,r);break;case\"CIMPictureFill\":D(m,d,i,S,o,n,r);break;case\"CIMHatchFill\":W(m,d,i,S,o,r);break;case\"CIMGradientFill\":G(m,d,i,S,o,r);break;case\"CIMSolidStroke\":B(m,d,i,S,o,r,\"CIMPolygonSymbol\"===e.type,c);break;case\"CIMPictureStroke\":V(m,d,i,S,o,r,\"CIMPolygonSymbol\"===e.type,c);break;case\"CIMGradientStroke\":q(m,d,i,S,o,r,\"CIMPolygonSymbol\"===e.type,c);break;case\"CIMCharacterMarker\":if(K(m,d,i,S,o,r))break;break;case\"CIMPictureMarker\":if(K(m,d,i,S,o,r))break;\"CIMLineSymbol\"===e.type&&(f=E(m)),Q(m,d,i,S,o,n,r,f,c);break;case\"CIMVectorMarker\":if(K(m,d,i,S,o,r))break;\"CIMLineSymbol\"===e.type&&(f=E(m)),_(m,d,i,S,o,r,n,f,c,l);break;default:H.error(\"Cannot analyze CIM layer\",m.type)}}}function U(e,t,i,o,r,n){const l=e.primitiveName,a=v(e.color),[f,c]=ue(o,l,t,null,null),m=s(JSON.stringify(e)+c).toString();n.push({type:\"fill\",templateHash:m,materialHash:f?()=>m:m,cim:e,materialOverrides:null,colorLocked:!!e.colorLocked,color:le(l,i,\"Color\",r,a,ne),height:0,angle:0,offsetX:0,offsetY:0,scaleX:1,effects:t,applyRandomOffset:!1,sampleAlphaOnly:!0})}function D(e,t,i,o,r,l,a){const f=e.primitiveName,c=N(e),[m,p]=ue(o,f,t,null,null),u=s(JSON.stringify(e)+p).toString(),y=s(`${e.url}${JSON.stringify(e.colorSubstitutions)}`).toString();let h=b(e.scaleX);if(\"width\"in e&&\"number\"==typeof e.width){const t=e.width;let i=1;const o=l.getResource(e.url);n(o)&&(i=o.width/o.height),h/=i*(e.height/t)}a.push({type:\"fill\",templateHash:u,materialHash:m?()=>y:y,cim:e,materialOverrides:null,colorLocked:!!e.colorLocked,effects:t,color:le(f,i,\"TintColor\",r,c,ne),height:le(f,i,\"Height\",r,e.height),scaleX:le(f,i,\"ScaleX\",r,h),angle:le(f,i,\"Rotation\",r,b(e.rotation)),offsetX:le(f,i,\"OffsetX\",r,b(e.offsetX)),offsetY:le(f,i,\"OffsetY\",r,b(e.offsetY)),url:e.url,applyRandomOffset:!1,sampleAlphaOnly:!1})}function W(e,t,i,o,r,n){const l=[\"Rotation\",\"OffsetX\",\"OffsetY\"],a=o.filter((t=>t.primitiveName!==e.primitiveName||!l.includes(t.propertyName))),f=e.primitiveName;let[c,m]=ue(o,f,t,null,null);const p=s(JSON.stringify(e)+m).toString(),u=s(`${e.separation}${JSON.stringify(e.lineSymbol)}`).toString();let y={r:255,g:255,b:255,a:1},h=!1;const g=e.lineSymbol?.symbolLayers?.find((e=>\"CIMSolidStroke\"===e.type&&null!=i[e.primitiveName]?.Color));if(g){y=v(g.color),y=le(g.primitiveName,i,\"Color\",r,y,ne);const e=\"function\"==typeof y;c=c||e,h=null!=g.color||e}n.push({type:\"fill\",templateHash:p,materialHash:c?me(u,i,a,r):u,cim:e,materialOverrides:a,colorLocked:!!e.colorLocked,effects:t,color:y,height:le(f,i,\"Separation\",r,e.separation),scaleX:1,angle:le(f,i,\"Rotation\",r,b(e.rotation)),offsetX:le(f,i,\"OffsetX\",r,b(e.offsetX)),offsetY:le(f,i,\"OffsetY\",r,b(e.offsetY)),applyRandomOffset:!1,sampleAlphaOnly:!0,hasUnresolvedReplacementColor:!h})}function G(e,t,i,o,r,n){const l=e.primitiveName,[a,f]=ue(o,l,t,null,null),c=s(JSON.stringify(e)+f).toString();n.push({type:\"fill\",templateHash:c,materialHash:a?me(c,i,o,r):c,cim:e,materialOverrides:null,colorLocked:!!e.colorLocked,effects:t,color:{r:128,g:128,b:128,a:1},height:0,angle:0,offsetX:0,offsetY:0,scaleX:1,applyRandomOffset:!1,sampleAlphaOnly:!1})}function B(e,t,i,o,r,n,l,a){const f=e.primitiveName,c=v(e.color),m=null!=e.width?e.width:4,p=Y(e.capStyle),u=T(e.joinStyle),y=e.miterLimit,[h,g]=ue(o,f,t,null,null),d=s(JSON.stringify(e)+g).toString();let S,N;if(t&&t instanceof Array&&t.length>0){const e=t[t.length-1];if(\"CIMGeometricEffectDashes\"===e.type&&\"NoConstraint\"===e.lineDashEnding&&null===e.offsetAlongLine){const e=(t=[...t]).pop();S=e.dashTemplate,N=e.scaleDash}}n.push({type:\"line\",templateHash:d,materialHash:h?()=>d:d,cim:e,materialOverrides:null,isOutline:l,colorLocked:!!e.colorLocked,effects:t,color:le(f,i,\"Color\",r,c,ne),width:le(f,i,\"Width\",r,m),cap:le(f,i,\"CapStyle\",r,p),join:le(f,i,\"JoinStyle\",r,u),miterLimit:y&&le(f,i,\"MiterLimit\",r,y),referenceWidth:a,zOrder:re(e.name),dashTemplate:S,scaleDash:N,sampleAlphaOnly:!0})}function V(e,t,i,o,r,n,l,a){const f=s(`${e.url}${JSON.stringify(e.colorSubstitutions)}`).toString(),c=e.primitiveName,m=N(e),p=null!=e.width?e.width:4,u=Y(e.capStyle),y=T(e.joinStyle),h=e.miterLimit,[g,d]=ue(o,c,t,null,null),S=s(JSON.stringify(e)+d).toString();n.push({type:\"line\",templateHash:S,materialHash:g?()=>f:f,cim:e,materialOverrides:null,isOutline:l,colorLocked:!!e.colorLocked,effects:t,color:le(c,i,\"TintColor\",r,m,ne),width:le(c,i,\"Width\",r,p),cap:le(c,i,\"CapStyle\",r,u),join:le(c,i,\"JoinStyle\",r,y),miterLimit:h&&le(c,i,\"MiterLimit\",r,h),referenceWidth:a,zOrder:re(e.name),dashTemplate:null,scaleDash:!1,url:e.url,sampleAlphaOnly:!1})}function q(e,t,i,o,r,n,l,a){const f=e.primitiveName,c=null!=e.width?e.width:4,m=Y(e.capStyle),p=T(e.joinStyle),u=e.miterLimit,[y,h]=ue(o,f,t,null,null),g=s(JSON.stringify(e)+h).toString();n.push({type:\"line\",templateHash:g,materialHash:y?me(g,i,o,r):g,cim:e,materialOverrides:null,isOutline:l,colorLocked:!!e.colorLocked,effects:t,color:{r:128,g:128,b:128,a:1},width:le(f,i,\"Width\",r,c),cap:le(f,i,\"CapStyle\",r,m),join:le(f,i,\"JoinStyle\",r,p),miterLimit:u&&le(f,i,\"MiterLimit\",r,u),referenceWidth:a,zOrder:re(e.name),dashTemplate:null,scaleDash:!1,sampleAlphaOnly:!1})}function K(e,t,i,o,r,n){const{markerPlacement:l,type:f}=e;if(!l||\"CIMMarkerPlacementInsidePolygon\"!==l.type)return!1;if(\"CIMVectorMarker\"===f||\"CIMPictureMarker\"===f){const i=e.primitiveName;if(i){const[e,r]=ue(o,i,t,null,null);if(e)return!1}const r=l.primitiveName;if(r){const[e,i]=ue(o,r,t,null,null);if(e)return!1}if(\"CIMVectorMarker\"===f){const{markerGraphics:t}=e;if(t)for(const e of t){const{symbol:t}=e;if(\"CIMPolygonSymbol\"===t?.type&&t.symbolLayers){const{symbolLayers:e}=t;for(const t of e)if(\"CIMSolidStroke\"===t.type)return!1}}}else{const{animatedSymbolProperties:t}=e;if(t)return!1}}const c=l,m=Math.abs(c.stepX),p=Math.abs(c.stepY);if(0===m||0===p)return!0;const u=[\"Rotation\",\"OffsetX\",\"OffsetY\"],y=o.filter((t=>t.primitiveName!==e.primitiveName||!u.includes(t.propertyName))),h=\"url\"in e&&\"string\"==typeof e.url?e.url:void 0,[g,d]=ue(o,c.primitiveName,t,null,null),S=s(JSON.stringify(e)+d).toString();let v,O,k=null;if(\"Random\"===l.gridType){const e=a(x),t=Math.max(Math.floor(e/m),1),i=Math.max(Math.floor(e/p),1);v=p*i,k=e=>e?e*i:0;O=t*m/v}else l.shiftOddRows?(v=2*p,k=e=>e?2*e:0,O=m/p*.5):(v=p,k=null,O=m/p);const C=N(e);return n.push({type:\"fill\",templateHash:S,materialHash:g?me(S,i,y,r):S,cim:e,materialOverrides:y,colorLocked:!!e.colorLocked,effects:t,color:le(c.primitiveName,i,\"TintColor\",r,C,ne),height:le(c.primitiveName,i,\"StepY\",r,v,k),scaleX:O,angle:le(c.primitiveName,i,\"GridAngle\",r,c.gridAngle),offsetX:le(c.primitiveName,i,\"OffsetX\",r,b(c.offsetX)),offsetY:le(c.primitiveName,i,\"OffsetY\",r,b(c.offsetY)),url:h,applyRandomOffset:\"Random\"===l.gridType,sampleAlphaOnly:!h,hasUnresolvedReplacementColor:!0}),!0}function Q(e,t,i,o,r,l,a,f,c){const m=e.primitiveName,p=b(e.size);let u=b(e.scaleX,1);const y=b(e.rotation),h=b(e.offsetX),g=b(e.offsetY),d=N(e),S=s(`${e.url}${JSON.stringify(e.colorSubstitutions)}${JSON.stringify(e.animatedSymbolProperties)}`).toString(),v=fe(e.markerPlacement,o,i,r),O=ce(e.animatedSymbolProperties,o,i,r),[k,C]=ue(o,m,t,v,O),P=s(JSON.stringify(e)+C).toString(),M=e.anchorPoint??{x:0,y:0};if(\"width\"in e&&\"number\"==typeof e.width){const t=e.width;let i=1;const o=l.getResource(e.url);n(o)&&(i=o.width/o.height),u/=i*(p/t)}function L(e,t){return n(O)?w(O,e,t):null}const I=e.animatedSymbolProperties&&!0===e.animatedSymbolProperties.randomizeStartTime?(e,t,i,o)=>{const r=J(o??0),n=L(e,t);return S+`-MATERIALGROUP(${r})`+`-ASP(${JSON.stringify(n)})`}:k?(e,t)=>{const i=L(e,t);return S+`-ASP(${JSON.stringify(i)})`}:S;a.push({type:\"marker\",templateHash:P,materialHash:I,cim:e,materialOverrides:null,colorLocked:!!e.colorLocked,effects:t,scaleSymbolsProportionally:!1,alignment:f,size:le(m,i,\"Size\",r,p),scaleX:le(m,i,\"ScaleX\",r,u),rotation:le(m,i,\"Rotation\",r,y),offsetX:le(m,i,\"OffsetX\",r,h),offsetY:le(m,i,\"OffsetY\",r,g),color:le(m,i,\"TintColor\",r,d,ne),anchorPoint:{x:M.x,y:-M.y},isAbsoluteAnchorPoint:\"Relative\"!==e.anchorPointUnits,outlineColor:{r:0,g:0,b:0,a:0},outlineWidth:0,frameHeight:0,rotateClockwise:!!e.rotateClockwise,referenceSize:c,sizeRatio:1,markerPlacement:v,url:e.url,animatedSymbolProperties:O})}function _(e,t,i,o,r,n,l,a,s,f){const c=e.markerGraphics;if(!c)return;let m=0;if(e.scaleSymbolsProportionally){const t=e.frame;t&&(m=t.ymax-t.ymin)}const p=fe(e.markerPlacement,o,i,r);for(const u of c)if(u){const c=u.symbol;if(!c)continue;switch(c.type){case\"CIMPointSymbol\":case\"CIMLineSymbol\":case\"CIMPolygonSymbol\":ee(e,t,p,null,u,o,i,r,n,l,a,s,m,!!f);break;case\"CIMTextSymbol\":Z(e,t,p,u,i,o,r,n,a,s,m)}}}function Z(e,i,o,r,n,l,a,f,c,m,u){const y=[];p.findApplicableOverrides(r,l,y);const h=r.geometry;if(!(\"x\"in h)||!(\"y\"in h))return;const g=r.symbol,d=O(g),S=k(g.fontStyleName),N=t(g.fontFamilyName);g.font={family:N,decoration:d,...S};const X=e.frame,A=h.x-.5*(X.xmin+X.xmax),x=h.y-.5*(X.ymin+X.ymax),J=e.size/u,H=e.primitiveName,Y=b(g.height)*J,T=b(g.angle),$=b(e.offsetX)+(b(g.offsetX)+A)*J,E=b(e.offsetY)+(b(g.offsetY)+x)*J,j=v(C(g));let F=v(P(g)),U=M(g)??0;U||(F=v(C(g.haloSymbol)),g.haloSize&&(U=g.haloSize*J));let D=null,W=null,G=0;if(g.callout&&\"CIMBackgroundCallout\"===g.callout.type){const e=g.callout;if(e.backgroundSymbol){const t=e.backgroundSymbol.symbolLayers;if(t)for(const e of t)\"CIMSolidFill\"===e.type?D=v(e.color):\"CIMSolidStroke\"===e.type&&(W=v(e.color),G=b(e.width))}}const[B,V]=ue(l,H,i,o,null),q=JSON.stringify(e.effects)+Number(e.colorLocked).toString()+JSON.stringify(e.anchorPoint)+e.anchorPointUnits+JSON.stringify(e.markerPlacement)+e.size.toString(),K=s(JSON.stringify(r)+q+V).toString();let Q=le(r.primitiveName,n,\"TextString\",a,r.textString??\"\",L,g.textCase);if(null==Q)return;const{fontStyleName:_}=g,Z=N+(_?\"-\"+_.toLowerCase():\"-regular\"),ee=Z;\"string\"==typeof Q&&Q.includes(\"[\")&&g.fieldMap&&(Q=I(g.fieldMap,Q,g.textCase)),f.push({type:\"text\",templateHash:K,materialHash:B||\"function\"==typeof Q||Q.match(/\\[(.*?)\\]/)?(e,t,i)=>ee+\"-\"+w(Q,e,t,i):ee+\"-\"+s(Q),cim:g,materialOverrides:null,colorLocked:!!e.colorLocked,effects:i,alignment:c,anchorPoint:{x:e.anchorPoint?e.anchorPoint.x:0,y:e.anchorPoint?e.anchorPoint.y:0},isAbsoluteAnchorPoint:\"Relative\"!==e.anchorPointUnits,fontName:Z,decoration:d,weight:le(H,n,\"Weight\",a,S.weight),style:le(H,n,\"Size\",a,S.style),size:le(H,n,\"Size\",a,Y),angle:le(H,n,\"Rotation\",a,T),offsetX:le(H,n,\"OffsetX\",a,$),offsetY:le(H,n,\"OffsetY\",a,E),horizontalAlignment:z(g.horizontalAlignment),verticalAlignment:R(g.verticalAlignment),text:Q,color:j,outlineColor:F,outlineSize:U,backgroundColor:D,borderLineColor:W,borderLineWidth:G,referenceSize:m,sizeRatio:1,markerPlacement:o})}function ee(e,t,i,o,r,l,a,f,c,m,p,u,y,h){const g=r.symbol,N=g.symbolLayers;if(!N)return;if(h)return void ie(e,t,i,o,r,a,l,f,c,m,p,u,y);let O=N.length;if(ye(N))return void te(e,t,i,o,r,N,l,a,f,c,p,u,y);const k=X.applyEffects(g.effects,r.geometry,m.geometryEngine);if(k)for(;O--;){const h=N[O];if(h&&!1!==h.enable)switch(h.type){case\"CIMSolidFill\":case\"CIMSolidStroke\":{const g=X.applyEffects(h.effects,k,m.geometryEngine),N=d(g);if(!N)continue;const O=\"Relative\"!==e.anchorPointUnits,[L,I,w]=S(N,e.frame,e.size,e.anchorPoint,O),z=\"CIMSolidFill\"===h.type,R={type:\"sdf\",geom:g,asFill:z},A=e.primitiveName,x=b(e.size)??10,J=b(e.rotation),H=b(e.offsetX),Y=b(e.offsetY),T=h.path,$=h.primitiveName,E=v(z?C(h):P(h)),j=z?{r:0,g:0,b:0,a:0}:v(P(h)),F=M(h)??0;if(!z&&!F)break;let U=!1,D=\"\";for(const e of l)e.primitiveName!==$&&e.primitiveName!==A||(void 0!==e.value?D+=`-${e.primitiveName}-${e.propertyName}-${JSON.stringify(e.value)}`:e.valueExpressionInfo&&(U=!0));(n(t)&&\"function\"==typeof t||n(i)&&\"function\"==typeof i)&&(U=!0);const W=JSON.stringify({...e,markerGraphics:null}),G=s(JSON.stringify(R)+T).toString(),B={type:\"marker\",templateHash:s(JSON.stringify(r)+JSON.stringify(h)+W+D).toString(),materialHash:U?()=>G:G,cim:R,materialOverrides:null,colorLocked:!!e.colorLocked,effects:t,scaleSymbolsProportionally:!!e.scaleSymbolsProportionally,alignment:p,anchorPoint:{x:I,y:w},isAbsoluteAnchorPoint:O,size:le(e.primitiveName,a,\"Size\",f,x),rotation:le(e.primitiveName,a,\"Rotation\",f,J),offsetX:le(e.primitiveName,a,\"OffsetX\",f,H),offsetY:le(e.primitiveName,a,\"OffsetY\",f,Y),scaleX:1,frameHeight:y,rotateClockwise:!!e.rotateClockwise,referenceSize:u,sizeRatio:L,color:le($,a,\"Color\",f,E,ne),outlineColor:le($,a,\"Color\",f,j,ne),outlineWidth:le($,a,\"Width\",f,F),markerPlacement:i,animatedSymbolProperties:o,path:T};c.push(B);break}default:ie(e,t,i,o,r,a,l,f,c,m,p,u,y)}}}function te(e,t,i,o,r,l,a,f,c,m,p,u,y){const h=r.geometry,g=l[0],N=l[1],O=d(h);if(!O)return;const k=\"Relative\"!==e.anchorPointUnits,[L,I,w]=S(O,e.frame,e.size,e.anchorPoint,k),z={type:\"sdf\",geom:h,asFill:!0},R=e.primitiveName,X=b(e.size),A=b(e.rotation),x=b(e.offsetX),J=b(e.offsetY),H=N.path,Y=N.primitiveName,T=g.primitiveName,$=v(C(N)),E=v(P(g)),j=M(g)??0;let F=!1,U=\"\";for(const n of a)n.primitiveName!==Y&&n.primitiveName!==T&&n.primitiveName!==R||(void 0!==n.value?U+=`-${n.primitiveName}-${n.propertyName}-${JSON.stringify(n.value)}`:n.valueExpressionInfo&&(F=!0));n(i)&&\"function\"==typeof i&&(F=!0);const D=JSON.stringify({...e,markerGraphics:null}),W=s(JSON.stringify(z)+H).toString(),G={type:\"marker\",templateHash:s(JSON.stringify(r)+JSON.stringify(N)+JSON.stringify(g)+D+U).toString(),materialHash:F?()=>W:W,cim:z,materialOverrides:null,colorLocked:!!e.colorLocked,effects:t,scaleSymbolsProportionally:!!e.scaleSymbolsProportionally,alignment:p,anchorPoint:{x:I,y:w},isAbsoluteAnchorPoint:k,size:le(e.primitiveName,f,\"Size\",c,X),rotation:le(e.primitiveName,f,\"Rotation\",c,A),offsetX:le(e.primitiveName,f,\"OffsetX\",c,x),offsetY:le(e.primitiveName,f,\"OffsetY\",c,J),scaleX:1,frameHeight:y,rotateClockwise:!!e.rotateClockwise,referenceSize:u,sizeRatio:L,color:le(Y,f,\"Color\",c,$,ne),outlineColor:le(T,f,\"Color\",c,E,ne),outlineWidth:le(T,f,\"Width\",c,j),markerPlacement:i,path:H,animatedSymbolProperties:o};m.push(G)}function ie(e,t,i,o,r,a,f,c,p,u,y,h,g){const d=oe(e,r),S=[\"Rotation\",\"OffsetX\",\"OffsetY\"],v=f.filter((t=>t.primitiveName!==e.primitiveName||!S.includes(t.propertyName)));let N=\"\";for(const n of f)void 0!==n.value&&(N+=`-${n.primitiveName}-${n.propertyName}-${JSON.stringify(n.value)}`);const[O,k,C]=m.getTextureAnchor(d,u),P=e.primitiveName,M=b(e.rotation),L=b(e.offsetX),I=b(e.offsetY),w=s(JSON.stringify(d)+N).toString(),z={type:\"marker\",templateHash:w,materialHash:v.length>0||n(t)&&\"function\"==typeof t?me(w,a,v,c):w,cim:d,materialOverrides:v,colorLocked:!!e.colorLocked,effects:t,scaleSymbolsProportionally:!!e.scaleSymbolsProportionally,alignment:y,anchorPoint:{x:O,y:k},isAbsoluteAnchorPoint:!1,size:b(e.size),rotation:le(P,a,\"Rotation\",c,M),offsetX:le(P,a,\"OffsetX\",c,L),offsetY:le(P,a,\"OffsetY\",c,I),color:{r:255,g:255,b:255,a:1},outlineColor:{r:0,g:0,b:0,a:0},outlineWidth:0,scaleX:1,frameHeight:g,rotateClockwise:!!e.rotateClockwise,referenceSize:h,sizeRatio:C/l(e.size),markerPlacement:i,animatedSymbolProperties:o,avoidSDFRasterization:!0};p.push(z)}function oe(e,t){return{type:e.type,enable:!0,name:e.name,colorLocked:e.colorLocked,primitiveName:e.primitiveName,anchorPoint:e.anchorPoint,anchorPointUnits:e.anchorPointUnits,offsetX:0,offsetY:0,rotateClockwise:e.rotateClockwise,rotation:0,size:e.size,billboardMode3D:e.billboardMode3D,depth3D:e.depth3D,frame:e.frame,markerGraphics:[t],scaleSymbolsProportionally:e.scaleSymbolsProportionally,respectFrame:e.respectFrame,clippingPath:e.clippingPath}}function re(e){if(e&&0===e.indexOf(\"Level_\")){const t=parseInt(e.substr(6),10);if(!isNaN(t))return t}return 0}function ne(t){if(!t||0===t.length)return null;const i=new e(t).toRgba();return{r:i[0],g:i[1],b:i[2],a:i[3]}}function le(e,t,i,o,r,n,l){if(null==e)return r;const a=t[e];if(a){const e=a[i];if(\"string\"==typeof e||\"number\"==typeof e||e instanceof Array)return n?n.call(null,e,l):e;if(null!=e&&e instanceof c&&o?.geometryType)return(t,i,a)=>{let s=A(e,t,{$view:a},o.geometryType,i);return null!==s&&n&&(s=n.call(null,s,l)),null!==s?s:r}}return r}function ae(e){return e?e.charAt(0).toLowerCase()+e.substr(1):e}function se(e,t,o,r){for(const i of t){if(i.valueExpressionInfo&&r?.geometryType){const e=o[i.primitiveName]&&o[i.primitiveName][i.propertyName];e instanceof c&&(i.fn=(t,i,o)=>A(e,t,{$view:o},r.geometryType,i))}}return(o,r,n)=>{for(const e of t)e.fn&&(e.value=e.fn(o,r,n));const l=[];for(let a of e){const e=a?.primitiveName;if(e){let o=!1;for(const r of t)if(r.primitiveName===e){const e=ae(r.propertyName);null!=r.value&&r.value!==a[e]&&(o||(a=i(a),o=!0),a[e]=r.value)}}l.push(a)}return l}}function fe(e,t,o,r){const n=[];if(p.findApplicableOverrides(e,t,n),null==e||0===n.length)return e;for(const i of n){if(i.valueExpressionInfo&&r?.geometryType){const e=o[i.primitiveName]&&o[i.primitiveName][i.propertyName];e instanceof c&&(i.fn=(t,i,o)=>A(e,t,{$view:o},r.geometryType,i))}}return(t,o,r)=>{for(const e of n)e.fn&&(e.value=e.fn(t,o,r));const l=i(e),a=e.primitiveName;for(const e of n)if(e.primitiveName===a){const t=ae(e.propertyName);null!=e.value&&e.value!==l[t]&&(l[t]=e.value)}return l}}function ce(e,t,o,r){const n=[];if(p.findApplicableOverrides(e,t,n),null==e||0===n.length)return e;for(const i of n){if(i.valueExpressionInfo&&r?.geometryType){const e=o[i.primitiveName]&&o[i.primitiveName][i.propertyName];e instanceof c&&(i.fn=(t,i,o)=>A(e,t,{$view:o},r.geometryType,i))}}return(t,o,r)=>{for(const e of n)e.fn&&(e.value=e.fn(t,o,r));const l=i(e),a=e.primitiveName;for(const e of n)if(e.primitiveName===a){const t=ae(e.propertyName);if(null!=e.value){const i=g(e.value,e.propertyName);i!==l[t]&&(l[t]=i)}}return l}}function me(e,t,i,o){for(const r of i){if(r.valueExpressionInfo&&o?.geometryType){const e=t[r.primitiveName]&&t[r.primitiveName][r.propertyName];e instanceof c&&(r.fn=(t,i,r)=>A(e,t,{$view:r},o.geometryType,i))}}return(t,o,r)=>{for(const e of i)e.fn&&(e.value=e.fn(t,o,r));return s(e+p.buildOverrideKey(i)).toString()}}function pe(e,t){if(!t||0===t.length)return e;const o=i(e);return p.applyOverrides(o,t),o}function ue(e,t,i,o,r){let l=!1,a=\"\";for(const n of e)n.primitiveName===t&&(void 0!==n.value?a+=`-${n.primitiveName}-${n.propertyName}-${JSON.stringify(n.value)}`:n.valueExpressionInfo&&(l=!0));return n(i)&&\"function\"==typeof i&&(l=!0),n(o)&&\"function\"==typeof o&&(l=!0),n(r)&&\"function\"==typeof r&&(l=!0),[l,a]}const ye=e=>e&&2===e.length&&e[0].enable&&e[1].enable&&\"CIMSolidStroke\"===e[0].type&&\"CIMSolidFill\"===e[1].type&&!e[0].effects&&!e[1].effects;export{pe as analyzeCIMResource,j as analyzeCIMSymbol};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+B,IAAMA,KAAE,IAAI;AAAE,SAASC,GAAEC,KAAE;AAAC,MAAG,QAAMA,IAAE,QAAM,CAAC,IAAG,KAAE;AAAE,MAAG,CAACF,GAAE,YAAYE,GAAC,EAAE,QAAM,CAACA,KAAE,KAAE;AAAE,MAAID;AAAE,SAAOA,MAAE,UAAQD,GAAE,gBAAgBE,GAAC,IAAE,UAAQ,SAAQ,CAACF,GAAE,cAAcE,KAAED,KAAE,OAAO,GAAE,IAAE;AAAC;;;ACAjK,SAASE,GAAEC,KAAE;AAAC,SAAM,OAAOA,IAAE,MAAM,GAAE,CAAC,EAAE,SAAS,CAAC;AAAG;AAAC,SAASC,GAAED,KAAE;AAAC,SAAM,QAAQA,IAAE,MAAM,GAAE,CAAC,EAAE,SAAS,CAAC,IAAIA,IAAE,CAAC,CAAC;AAAG;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,YAAYF,KAAE;AAAC,IAAAA,QAAI,KAAK,2BAAyBA;AAAA,EAAE;AAAA,EAAC,cAAcA,KAAEE,KAAE;AAAC,SAAK,6BAA2B,KAAK,2BAAyB,SAAS,cAAc,QAAQ;AAAG,UAAMC,MAAE,KAAK,0BAAyBC,MAAED,IAAE,WAAW,IAAI;AAAE,SAAK,mBAAmBC,KAAEF,GAAC,GAAE,KAAK,cAAYA,KAAE,KAAK,aAAWF,IAAE,MAAM,OAAO,GAAE,KAAK,cAAY,KAAK,mBAAmB;AAAE,UAAMK,KAAE,KAAK,kBAAkBD,KAAEF,GAAC,GAAE,EAAC,YAAWI,KAAE,QAAOC,IAAC,IAAEL,IAAE;AAAK,SAAK,0BAAwBI,OAAG,mBAAiBA,MAAE,MAAG,KAAK,cAAY;AAAE,UAAME,KAAE,KAAK,cAAY,KAAK,WAAW;AAAO,IAAAL,IAAE,QAAME,KAAE,IAAE,KAAK,yBAAwBF,IAAE,SAAOK,IAAE,KAAK,sBAAoB,KAAK,MAAM,KAAK,cAAYN,IAAE,UAAU,GAAE,KAAK,oBAAkBA,IAAE,KAAK,OAAKA,IAAE,YAAW,KAAK,iBAAeG,KAAEH,IAAE,YAAW,KAAK,kBAAgBM,KAAEN,IAAE,YAAW,KAAK,2BAAyBA,IAAE;AAAW,UAAMO,MAAEP,IAAE,SAAO,CAAC,GAAE,GAAE,GAAE,CAAC,GAAEQ,KAAER,IAAE,QAAMA,IAAE,KAAK,QAAMA,IAAE,KAAK,QAAM,CAAC,GAAE,GAAE,GAAE,CAAC;AAAE,SAAK,aAAWD,GAAEQ,GAAC,GAAE,KAAK,aAAWV,GAAEW,EAAC;AAAE,UAAMC,MAAE,KAAK,qBAAoBC,MAAE,KAAK;AAAkB,IAAAR,IAAE,KAAK,GAAEA,IAAE,UAAU,GAAE,GAAED,IAAE,OAAMA,IAAE,MAAM,GAAE,KAAK,mBAAmBC,KAAEF,GAAC;AAAE,UAAMW,MAAEC,GAAEV,IAAE,WAAU,KAAK,cAAc,IAAEQ,KAAEG,KAAEH,KAAEI,KAAEJ,MAAE;AAAE,QAAIK,KAAE,KAAK,yBAAwBC,KAAE;AAAE,IAAAF,MAAG,KAAK,YAAYZ,KAAES,KAAEE,IAAEE,IAAEC,IAAEhB,GAAC,GAAEgB,MAAGH,IAAEE,MAAGJ;AAAE,eAAUd,OAAK,KAAK,WAAW,CAAAiB,MAAGZ,IAAE,2BAAyB,mBAAkBA,IAAE,YAAU,gBAAeA,IAAE,SAASL,KAAEkB,IAAEC,EAAC,GAAEd,IAAE,2BAAyB,eAAcA,IAAE,YAAU,KAAK,YAAWA,IAAE,SAASL,KAAEkB,IAAEC,EAAC,MAAId,IAAE,YAAU,KAAK,YAAWA,IAAE,SAASL,KAAEkB,IAAEC,EAAC,IAAGZ,OAAG,WAASA,OAAG,KAAK,kBAAkBF,KAAEa,IAAEC,IAAEZ,KAAEC,GAAC,GAAEW,MAAGP;AAAE,IAAAP,IAAE,QAAQ;AAAE,UAAMe,KAAE,KAAK,iBAAe,IAAE,KAAK,yBAAwBC,KAAE,KAAK,iBAAgBC,KAAEjB,IAAE,aAAa,GAAE,GAAEe,IAAEC,EAAC,GAAEE,KAAE,IAAI,WAAWD,GAAE,IAAI;AAAE,QAAGnB,IAAE,mBAAkB;AAAC,UAAIF;AAAE,eAAQD,MAAE,GAAEA,MAAEuB,GAAE,QAAOvB,OAAG,EAAE,CAAAC,MAAEsB,GAAEvB,MAAE,CAAC,IAAE,KAAIuB,GAAEvB,GAAC,IAAEuB,GAAEvB,GAAC,IAAEC,KAAEsB,GAAEvB,MAAE,CAAC,IAAEuB,GAAEvB,MAAE,CAAC,IAAEC,KAAEsB,GAAEvB,MAAE,CAAC,IAAEuB,GAAEvB,MAAE,CAAC,IAAEC;AAAA,IAAC;AAAC,QAAIuB,IAAEC;AAAE,YAAOtB,IAAE,qBAAoB;AAAA,MAAC,KAAI;AAAO,QAAAqB,KAAE;AAAI;AAAA,MAAM,KAAI;AAAQ,QAAAA,KAAE;AAAG;AAAA,MAAM;AAAQ,QAAAA,KAAE;AAAA,IAAC;AAAC,YAAOrB,IAAE,mBAAkB;AAAA,MAAC,KAAI;AAAS,QAAAsB,KAAE;AAAI;AAAA,MAAM,KAAI;AAAM,QAAAA,KAAE;AAAG;AAAA,MAAM;AAAQ,QAAAA,KAAE;AAAA,IAAC;AAAC,WAAM,EAAC,MAAK,CAACL,IAAEC,EAAC,GAAE,OAAM,IAAI,YAAYE,GAAE,MAAM,GAAE,KAAI,OAAG,eAAc,OAAG,SAAQC,IAAE,SAAQC,IAAE,QAAOrB,IAAC;AAAA,EAAC;AAAA,EAAC,YAAYH,KAAED,KAAEE,KAAEC,KAAEY,KAAEX,KAAE;AAAC,UAAMC,MAAE,KAAK,gBAAeC,KAAE,KAAK;AAAgB,SAAK,6BAA2B,KAAK,2BAAyB,SAAS,cAAc,QAAQ,IAAG,KAAK,yBAAyB,QAAMD,KAAE,KAAK,yBAAyB,SAAOC;AAAE,UAAMC,MAAE,KAAK,0BAAyBC,MAAED,IAAE,WAAW,IAAI;AAAE,IAAAC,IAAE,UAAU,GAAE,GAAEH,KAAEC,EAAC,GAAE,KAAK,mBAAmBE,KAAEJ,GAAC;AAAE,UAAK,EAAC,YAAWK,IAAE,QAAOC,IAAC,IAAEN,IAAE;AAAK,IAAAI,IAAE,YAAU,KAAK,YAAWA,IAAE,cAAY,KAAK,YAAWA,IAAE,WAAS,SAAQ,KAAK,kBAAkBA,KAAER,KAAEE,KAAEO,IAAEC,GAAC,GAAET,IAAE,cAAY,KAAK,YAAY,KAAK,MAAM,CAAC,GAAEA,IAAE,UAAUM,KAAE,GAAE,GAAEF,KAAEC,IAAEH,KAAEY,KAAEV,KAAEC,EAAC,GAAEL,IAAE,cAAY;AAAA,EAAC;AAAA,EAAC,kBAAkBA,KAAED,KAAEE,KAAEC,KAAEY,KAAE;AAAC,UAAMX,MAAE,KAAK,qBAAoBC,MAAE,KAAK;AAAkB,eAAUC,MAAK,KAAK,YAAW;AAAC,YAAMC,MAAE,IAAEF,KAAEG,MAAE,GAAEC,KAAE;AAAG,eAAQL,MAAE,GAAEA,MAAEI,KAAEJ,OAAI;AAAC,cAAMC,OAAG,KAAGG,MAAE,KAAGC,KAAEL,MAAEK,MAAGF;AAAE,QAAAN,IAAE,YAAUI,KAAEJ,IAAE,WAAWK,IAAEN,KAAEE,GAAC,GAAEC,OAAG,WAASA,OAAG,KAAK,kBAAkBF,KAAED,KAAEE,KAAEC,KAAEY,KAAEV,GAAC;AAAA,MAAC;AAAC,MAAAH,OAAGE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBJ,KAAEE,KAAE;AAAC,UAAMC,MAAE,KAAK,IAAID,IAAE,MAAK,GAAE,GAAEa,MAAEb,IAAE,MAAKE,MAAE,GAAGW,IAAE,KAAK,IAAIA,IAAE,MAAM,IAAID,GAAEX,MAAED,IAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,MAAMa,IAAE,MAAM;AAAe,QAAIV;AAAE,YAAOL,IAAE,OAAKI,KAAEJ,IAAE,eAAa,OAAME,IAAE,qBAAoB;AAAA,MAAC,KAAI;AAAA,MAAO;AAAQ,QAAAG,MAAE;AAAO;AAAA,MAAM,KAAI;AAAQ,QAAAA,MAAE;AAAQ;AAAA,MAAM,KAAI;AAAS,QAAAA,MAAE;AAAA,IAAQ;AAAC,IAAAL,IAAE,YAAUK;AAAA,EAAC;AAAA,EAAC,gBAAgBJ,KAAED,KAAE;AAAC,SAAK,6BAA2B,KAAK,2BAAyB,SAAS,cAAc,QAAQ;AAAG,UAAME,MAAE,KAAK,0BAAyBC,MAAED,IAAE,WAAW,IAAI;AAAE,SAAK,mBAAmBC,KAAEH,GAAC,GAAE,KAAK,cAAYA,KAAE,KAAK,aAAWC,IAAE,MAAM,OAAO,GAAE,KAAK,cAAY,KAAK,mBAAmB;AAAE,UAAMc,MAAE,KAAK,kBAAkBZ,KAAEH,GAAC,GAAEI,MAAE,KAAK,cAAY,KAAK,WAAW;AAAO,WAAOF,IAAE,QAAMa,KAAEb,IAAE,SAAOE,KAAE,CAACW,MAAEf,IAAE,YAAWI,MAAEJ,IAAE,UAAU;AAAA,EAAC;AAAA,EAAC,kBAAkBC,KAAED,KAAE;AAAC,QAAIE,MAAE;AAAE,eAAUa,OAAK,KAAK,WAAW,CAAAb,MAAE,KAAK,IAAIA,KAAED,IAAE,YAAYc,GAAC,EAAE,KAAK;AAAE,UAAMZ,MAAEH,IAAE;AAAK,YAAO,aAAWG,IAAE,SAAO,cAAYA,IAAE,SAAO,YAAU,OAAOA,IAAE,WAAS,WAASA,IAAE,UAAQ,aAAWA,IAAE,WAAS,YAAU,OAAOA,IAAE,UAAQA,IAAE,SAAO,SAAOD,OAAG,MAAGD,IAAE,YAAY,GAAG,EAAE,QAAOC,OAAG,IAAE,KAAK,YAAY,KAAK,MAAK,KAAK,MAAMA,GAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,QAAID,MAAE,QAAM,KAAK,YAAY;AAAK,UAAMD,MAAE,KAAK,YAAY,KAAK;AAAW,WAAOA,OAAG,gBAAcA,QAAIC,OAAG,MAAK,KAAK,MAAMA,MAAE,IAAE,KAAK,YAAY,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,kBAAkBA,KAAED,KAAEE,KAAEC,KAAEY,KAAEX,KAAE;AAAC,UAAMC,MAAE,MAAG,KAAK,aAAYC,KAAE,WAASS,MAAE,OAAI,aAAWA,MAAE,OAAI;AAAI,YAAOd,IAAE,WAAU;AAAA,MAAC,KAAI;AAAS,QAAAD,OAAG,KAAK,iBAAe;AAAE;AAAA,MAAM,KAAI;AAAQ,QAAAA,OAAG,KAAK;AAAA,IAAc;AAAC,UAAMO,MAAEN,IAAE;AAAa,QAAG,gBAAcE,IAAE,SAAOI,KAAE;AAAA,MAAC,KAAI;AAAM,QAAAL,OAAGG;AAAE;AAAA,MAAM,KAAI;AAAS,QAAAH,OAAGG,MAAE;AAAA,IAAC;AAAA,aAAS,mBAAiBF,IAAE,SAAOI,KAAE;AAAA,MAAC,KAAI;AAAM,QAAAL,OAAGG,MAAE;AAAI;AAAA,MAAM,KAAI;AAAS,QAAAH,OAAGG,MAAE;AAAA,IAAC;AAAC,UAAMG,MAAEJ,MAAE,MAAIA,MAAE,KAAK,KAAKC,MAAEC,EAAC;AAAE,IAAAL,IAAE,KAAK,GAAEA,IAAE,UAAU,GAAEA,IAAE,cAAYA,IAAE,WAAUA,IAAE,YAAUO,KAAEP,IAAE,OAAOD,MAAE,KAAK,yBAAwBE,GAAC,GAAED,IAAE,OAAOD,MAAE,KAAK,iBAAe,IAAE,KAAK,yBAAwBE,GAAC,GAAED,IAAE,OAAO,GAAEA,IAAE,QAAQ;AAAA,EAAC;AAAC;AAAC,SAASc,GAAEd,KAAED,KAAE;AAAC,SAAM,aAAWC,MAAE,MAAGD,MAAE,YAAUC,MAAED,MAAE;AAAC;;;ACA/yJ,IAAM0B,KAAE;AAAI,IAAIC;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAK,YAAUA;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,UAAMA,MAAE,KAAK;AAAU,WAAO,KAAK,YAAU,MAAKA;AAAA,EAAC;AAAC;AAA2B,SAASC,GAAEC,KAAEC,KAAE;AAAC,MAAIC,KAAEH;AAAE,EAAAI,OAAIA,KAAE,IAAIC,GAAE,GAAE,GAAE,GAAE,CAAC,IAAGD,GAAE,MAAMH,GAAE,OAAO,GAAEG,GAAE,eAAeF,MAAE,CAAC,GAAEE,GAAE,UAAUE,EAAC;AAAE,aAAUC,OAAKN,IAAE,MAAM,KAAGM,OAAG,EAAEA,IAAE,SAAO,IAAG;AAAC,IAAAJ,MAAEI,IAAE,CAAC,EAAE,CAAC,GAAEP,MAAE,CAACO,IAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,OAAOD,KAAEH,GAAC;AAAE,aAAQC,MAAE,GAAEA,MAAEM,IAAE,QAAON,MAAI,CAAAE,MAAEI,IAAEN,GAAC,EAAE,CAAC,GAAED,MAAE,CAACO,IAAEN,GAAC,EAAE,CAAC,GAAEG,GAAE,OAAOD,KAAEH,GAAC;AAAE,IAAAI,GAAE,MAAM;AAAA,EAAC;AAAC,QAAMI,MAAEJ,GAAE,OAAO,KAAE;AAAE,MAAGI,KAAE;AAAC,UAAMP,MAAE,CAAC;AAAE,eAAUM,OAAKC,KAAE;AAAC,YAAMH,MAAE,CAAC;AAAE,MAAAJ,IAAE,KAAKI,GAAC;AAAE,iBAAUJ,OAAKM,IAAE,CAAAF,IAAE,KAAK,CAACJ,IAAE,GAAE,CAACA,IAAE,CAAC,CAAC;AAAA,IAAC;AAAC,WAAM,EAAC,OAAMA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,OAAM,CAAC,EAAC;AAAC;AAAC,SAASO,GAAEP,KAAEC,KAAE;AAAC,MAAIC,KAAEH;AAAE,EAAAI,OAAIA,KAAE,IAAIC,GAAE,GAAE,GAAE,GAAE,CAAC,IAAGD,GAAE,MAAMH,GAAE,UAAU,GAAEG,GAAE,eAAeF,MAAE,CAAC,GAAEE,GAAE,UAAUE,EAAC;AAAE,aAAUC,OAAKN,IAAE,MAAM,KAAGM,OAAG,EAAEA,IAAE,SAAO,IAAG;AAAC,IAAAJ,MAAEI,IAAE,CAAC,EAAE,CAAC,GAAEP,MAAE,CAACO,IAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,OAAOD,KAAEH,GAAC;AAAE,aAAQC,MAAE,GAAEA,MAAEM,IAAE,QAAON,MAAI,CAAAE,MAAEI,IAAEN,GAAC,EAAE,CAAC,GAAED,MAAE,CAACO,IAAEN,GAAC,EAAE,CAAC,GAAEG,GAAE,OAAOD,KAAEH,GAAC;AAAA,EAAC;AAAC,QAAMQ,MAAEJ,GAAE,OAAO,KAAE;AAAE,MAAGI,KAAE;AAAC,UAAMP,MAAE,CAAC;AAAE,eAAUM,OAAKC,KAAE;AAAC,YAAMH,MAAE,CAAC;AAAE,MAAAJ,IAAE,KAAKI,GAAC;AAAE,iBAAUJ,OAAKM,IAAE,CAAAF,IAAE,KAAK,CAACJ,IAAE,GAAE,CAACA,IAAE,CAAC,CAAC;AAAA,IAAC;AAAC,WAAM,EAAC,OAAMA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,OAAM,CAAC,EAAC;AAAC;;;ACAviC,IAAMQ,KAAN,MAAO;AAAA,EAAC,wBAAwBA,KAAEC,KAAE;AAAC,QAAG,CAACA,IAAE,QAAOD;AAAE,SAAK,yBAAuB,KAAK,uBAAqB,SAAS,cAAc,QAAQ;AAAG,UAAK,EAAC,OAAME,KAAE,QAAOC,IAAC,IAAEH,KAAEI,MAAE,KAAK,sBAAqBC,MAAED,IAAE,WAAW,IAAI;AAAE,IAAAJ,QAAII,QAAIA,IAAE,QAAMF,KAAEE,IAAE,SAAOD,KAAEE,IAAE,UAAUL,KAAE,GAAE,GAAEE,KAAEC,GAAC;AAAG,UAAMG,MAAED,IAAE,aAAa,GAAE,GAAEH,KAAEC,GAAC,EAAE;AAAK,QAAGF;AAAE,iBAAUM,MAAKN,IAAE,KAAGM,MAAGA,GAAE,YAAU,MAAIA,GAAE,SAAS,UAAQA,GAAE,YAAU,MAAIA,GAAE,SAAS,QAAO;AAAC,cAAK,CAACP,KAAEC,KAAEC,KAAEC,GAAC,IAAEI,GAAE,UAAS,CAACH,KAAEC,KAAEG,KAAEC,GAAC,IAAEF,GAAE;AAAS,YAAGP,QAAII,OAAGH,QAAII,OAAGH,QAAIM,OAAGL,QAAIM,IAAE;AAAS,iBAAQF,KAAE,GAAEA,KAAED,IAAE,QAAOC,MAAG,EAAE,CAAAP,QAAIM,IAAEC,EAAC,KAAGN,QAAIK,IAAEC,KAAE,CAAC,KAAGL,QAAII,IAAEC,KAAE,CAAC,KAAGJ,QAAIG,IAAEC,KAAE,CAAC,MAAID,IAAEC,EAAC,IAAEH,KAAEE,IAAEC,KAAE,CAAC,IAAEF,KAAEC,IAAEC,KAAE,CAAC,IAAEC,KAAEF,IAAEC,KAAE,CAAC,IAAEE;AAAA,MAAE;AAAA;AAAC,UAAMD,MAAE,IAAI,UAAUF,KAAEJ,KAAEC,GAAC;AAAE,WAAOE,IAAE,aAAaG,KAAE,GAAE,CAAC,GAAEJ;AAAA,EAAC;AAAA,EAAC,cAAcJ,KAAEC,KAAE;AAAC,QAAG,CAACA,OAAGA,IAAE,SAAO,EAAE,QAAOD;AAAE,SAAK,yBAAuB,KAAK,uBAAqB,SAAS,cAAc,QAAQ;AAAG,UAAK,EAAC,OAAME,KAAE,QAAOC,IAAC,IAAEH,KAAEI,MAAE,KAAK,sBAAqBC,MAAED,IAAE,WAAW,IAAI;AAAE,IAAAJ,QAAII,QAAIA,IAAE,QAAMF,KAAEE,IAAE,SAAOD,KAAEE,IAAE,UAAUL,KAAE,GAAE,GAAEE,KAAEC,GAAC;AAAG,UAAMG,MAAED,IAAE,aAAa,GAAE,GAAEH,KAAEC,GAAC,GAAEK,MAAE,IAAI,WAAWF,IAAE,IAAI,GAAEC,KAAE,CAACN,IAAE,CAAC,IAAE,KAAIA,IAAE,CAAC,IAAE,KAAIA,IAAE,CAAC,IAAE,KAAIA,IAAE,CAAC,IAAE,GAAG;AAAE,aAAQS,MAAE,GAAEA,MAAEF,IAAE,QAAOE,OAAG,EAAE,CAAAF,IAAEE,MAAE,CAAC,KAAGH,GAAE,CAAC,GAAEC,IAAEE,MAAE,CAAC,KAAGH,GAAE,CAAC,GAAEC,IAAEE,MAAE,CAAC,KAAGH,GAAE,CAAC,GAAEC,IAAEE,MAAE,CAAC,KAAGH,GAAE,CAAC;AAAE,UAAME,MAAE,IAAI,UAAU,IAAI,kBAAkBD,IAAE,MAAM,GAAEN,KAAEC,GAAC;AAAE,WAAOE,IAAE,aAAaI,KAAE,GAAE,CAAC,GAAEL;AAAA,EAAC;AAAC;;;ACApqC,IAAMO,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAY;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,YAAO,QAAM,KAAK,MAAI,MAAI,KAAK,MAAI,MAAI,KAAK,SAAO,MAAI,KAAK,WAAS,KAAK,KAAG,KAAK,MAAM,KAAK,OAAM,KAAK,KAAK,IAAG,KAAK;AAAA,EAAE;AAAA,EAAC,cAAa;AAAC,SAAK,KAAG,GAAE,KAAK,KAAG,GAAE,KAAK,KAAG,GAAE,KAAK,IAAE,GAAE,KAAK,KAAG,GAAE,KAAK,KAAG,GAAE,KAAK,KAAG,GAAE,KAAK,QAAM,GAAE,KAAK,QAAM;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAEC,KAAE;AAAC,SAAK,KAAGD,KAAE,KAAK,KAAGC;AAAA,EAAC;AAAA,EAAC,cAAcD,KAAE;AAAC,SAAK,KAAGA;AAAA,EAAC;AAAA,EAAC,YAAYA,KAAEC,KAAE;AAAC,SAAK,KAAG,QAAO,KAAK,QAAMD,KAAE,KAAK,QAAMC;AAAA,EAAC;AAAA,EAAC,UAAUD,KAAE;AAAC,SAAK,KAAGA,KAAE,KAAK,QAAM,QAAO,KAAK,QAAM;AAAA,EAAM;AAAA,EAAC,WAAWA,KAAE;AAAC,SAAK,KAAGA;AAAA,EAAC;AAAA,EAAC,SAASA,KAAE;AAAC,SAAK,IAAEA;AAAA,EAAC;AAAA,EAAC,WAAWA,KAAE;AAAC,SAAK,IAAEA;AAAA,EAAC;AAAC;;;ACA5T,SAASE,GAAEC,KAAE;AAAC,QAAMC,MAAE,EAAED,GAAC;AAAE,SAAOE,GAAED,GAAC,GAAEA;AAAC;AAAC,SAASE,GAAEC,KAAE;AAAC,EAAAA,QAAIC,GAAED,GAAC,IAAEA,IAAE,IAAE,CAACA,IAAE,IAAE,EAAEA,GAAC,IAAEE,GAAEF,IAAE,KAAK,IAAEG,GAAEH,GAAC,IAAEE,GAAEF,IAAE,KAAK,IAAE,EAAEA,GAAC,KAAGI,GAAEJ,IAAE,MAAM;AAAE;AAAC,SAASI,GAAEJ,KAAE;AAAC,MAAGA,KAAE;AAAC,UAAMJ,MAAEI,IAAE;AAAO,aAAQH,MAAE,GAAEA,MAAED,KAAEC,MAAI,CAAAG,IAAEH,GAAC,EAAE,CAAC,IAAE,CAACG,IAAEH,GAAC,EAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAASK,GAAEF,KAAE;AAAC,MAAGA,IAAE,YAAUJ,OAAKI,IAAE,CAAAI,GAAER,GAAC;AAAC;AAAC,SAASS,GAAEL,KAAE;AAAC,MAAGA,KAAE;AAAC,aAAQJ,MAAEI,IAAE,SAAO,GAAEJ,MAAE,GAAE,EAAEA,IAAE,CAAAI,IAAEJ,GAAC,EAAE,CAAC,KAAGI,IAAEJ,MAAE,CAAC,EAAE,CAAC,GAAEI,IAAEJ,GAAC,EAAE,CAAC,KAAGI,IAAEJ,MAAE,CAAC,EAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAASO,GAAEH,KAAE;AAAC,MAAGA,IAAE,YAAUJ,OAAKI,IAAE,CAAAK,GAAET,GAAC;AAAC;AAAC,SAASU,GAAEN,KAAE;AAAC,MAAGA,KAAE;AAAC,UAAMJ,MAAEI,IAAE;AAAO,aAAQH,MAAE,GAAEA,MAAED,KAAE,EAAEC,IAAE,CAAAG,IAAEH,GAAC,EAAE,CAAC,KAAGG,IAAEH,MAAE,CAAC,EAAE,CAAC,GAAEG,IAAEH,GAAC,EAAE,CAAC,KAAGG,IAAEH,MAAE,CAAC,EAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAASU,GAAEP,KAAE;AAAC,MAAGA,IAAE,YAAUJ,OAAKI,IAAE,CAAAM,GAAEV,GAAC;AAAC;AAAC,SAASE,GAAEE,KAAE;AAAC,EAAAA,QAAI,EAAEA,GAAC,IAAEO,GAAEP,IAAE,KAAK,IAAEG,GAAEH,GAAC,IAAEO,GAAEP,IAAE,KAAK,IAAE,EAAEA,GAAC,KAAGM,GAAEN,IAAE,MAAM,GAAED,GAAEC,GAAC;AAAE;AAAC,SAASQ,GAAER,KAAE;AAAC,EAAAA,QAAID,GAAEC,GAAC,GAAE,EAAEA,GAAC,IAAEG,GAAEH,IAAE,KAAK,IAAEG,GAAEH,GAAC,IAAEG,GAAEH,IAAE,KAAK,IAAE,EAAEA,GAAC,KAAGK,GAAEL,IAAE,MAAM;AAAE;AAAC,SAASS,GAAET,KAAE;AAAC,MAAGA,IAAE,YAAUJ,OAAKI,IAAE,CAAAU,GAAEd,GAAC;AAAC;AAAC,SAASc,GAAEV,KAAE;AAAC,EAAAA,OAAGA,IAAE,QAAQ;AAAC;AAAC,SAASW,GAAEX,KAAEJ,KAAEC,KAAE;AAAC,SAAM,CAACG,IAAE,CAAC,KAAGJ,IAAE,CAAC,IAAEI,IAAE,CAAC,KAAGH,KAAEG,IAAE,CAAC,KAAGJ,IAAE,CAAC,IAAEI,IAAE,CAAC,KAAGH,GAAC;AAAC;AAAC,SAASe,GAAEZ,KAAE;AAAC,SAAM,EAAE,CAACA,OAAG,MAAIA,IAAE,YAAUA,IAAE,CAAC,EAAE,CAAC,MAAIA,IAAEA,IAAE,SAAO,CAAC,EAAE,CAAC,KAAGA,IAAE,CAAC,EAAE,CAAC,MAAIA,IAAEA,IAAE,SAAO,CAAC,EAAE,CAAC;AAAE;AAAC,SAASa,GAAEb,KAAE;AAAC,SAAOA,IAAE,CAAC;AAAC;AAAC,SAASc,GAAEd,KAAEJ,KAAE;AAAC,EAAAI,IAAE,CAAC,IAAEJ;AAAC;AAAC,IAAMmB,KAAN,MAAO;AAAA,EAAC,YAAYf,KAAEC,KAAEe,KAAErB,MAAE,GAAE;AAAC,SAAK,WAAS,OAAG,KAAK,YAAU,MAAK,KAAK,gBAAcM,KAAE,KAAK,iBAAee,KAAE,KAAK,oBAAkBrB,KAAE,KAAK,YAAU,IAAG,KAAK,YAAU,IAAG,KAAK,cAAY,OAAGK,QAAI,EAAEA,GAAC,IAAEC,QAAI,KAAK,YAAUD,IAAE,OAAM,KAAK,WAAS,QAAIG,GAAEH,GAAC,IAAEgB,QAAI,KAAK,YAAUhB,IAAE,OAAM,KAAK,WAAS,SAAIE,GAAEF,GAAC,KAAGC,QAAI,KAAK,YAAUgB,GAAEjB,GAAC,EAAE,OAAM,KAAK,WAAS,OAAI,KAAK,cAAY,KAAK,YAAU,KAAK,UAAU,UAAS,KAAK,oBAAkB,IAAIA;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAG,CAAC,KAAK,UAAU,QAAO;AAAK,WAAK,KAAK,eAAa,KAAK,YAAU,KAAK,YAAU,KAAG;AAAC,WAAK,eAAa,KAAK;AAAY,YAAMA,MAAE,KAAK,YAAY,KAAK,UAAU,KAAK,SAAS,CAAC;AAAE,UAAGA,IAAE,QAAOA;AAAA,IAAC;AAAC,WAAO,KAAK,YAAU,IAAG,KAAK,YAAU,IAAG,KAAK,YAAU,MAAK;AAAA,EAAI;AAAC;AAAC,IAAMkB,KAAN,MAAO;AAAA,EAAC,YAAYlB,KAAEJ,KAAEC,KAAEI,MAAE,GAAE;AAAC,SAAK,WAAS,OAAG,KAAK,YAAU,MAAK,KAAK,kBAAgBD,KAAE,KAAK,gBAAcJ,KAAE,KAAK,iBAAeC,KAAE,KAAK,oBAAkBI,KAAE,KAAK,YAAU,IAAG,KAAK,YAAU,IAAG,KAAK,cAAY;AAAA,EAAE;AAAA,EAAC,OAAM;AAAC,eAAO;AAAC,UAAG,CAAC,KAAK,WAAU;AAAC,YAAID,MAAE,KAAK,gBAAgB,KAAK;AAAE,eAAKA,OAAG;AAAC,cAAG,EAAEA,GAAC,IAAE,KAAK,kBAAgB,KAAK,YAAUA,IAAE,OAAM,KAAK,WAAS,QAAIG,GAAEH,GAAC,IAAE,KAAK,mBAAiB,KAAK,YAAUA,IAAE,OAAM,KAAK,WAAS,SAAIE,GAAEF,GAAC,KAAG,KAAK,kBAAgB,KAAK,YAAUiB,GAAEjB,GAAC,EAAE,OAAM,KAAK,WAAS,OAAI,KAAK,WAAU;AAAC,iBAAK,YAAU,KAAK,UAAU,QAAO,KAAK,YAAU;AAAG;AAAA,UAAK;AAAC,UAAAA,MAAE,KAAK,gBAAgB,KAAK;AAAA,QAAC;AAAC,YAAG,CAAC,KAAK,UAAU,QAAO;AAAA,MAAI;AAAC,aAAK,KAAK,eAAa,KAAK,YAAU,KAAK,YAAU,KAAG;AAAC,aAAK,eAAa,KAAK;AAAY,cAAMA,MAAE,KAAK,YAAY,KAAK,UAAU,KAAK,SAAS,CAAC;AAAE,YAAGA,IAAE,QAAOA;AAAA,MAAC;AAAC,WAAK,YAAU,IAAG,KAAK,YAAU,IAAG,KAAK,YAAU;AAAA,IAAI;AAAA,EAAC;AAAC;AAAC,SAASiB,GAAEjB,KAAE;AAAC,SAAM,EAAC,OAAM,CAAC,CAAC,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC,CAAC,EAAC;AAAC;;;ACAplF,IAAMmB,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,GAAE,WAAS;AAAK,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAE;AAAC,SAAK,mBAAiBF,KAAE,KAAK,kBAAgB,WAASC,IAAE,iBAAeA,IAAE,iBAAe,KAAI,KAAK,eAAa,KAAK,KAAK,IAAE,KAAK,IAAI,KAAK,eAAe,IAAE,OAAK,KAAK,EAAE;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAIG,MAAE,KAAK,iBAAiB,KAAK;AAAE,WAAKA,OAAG;AAAC,UAAG,EAAEA,GAAC,GAAE;AAAC,aAAK,YAAU;AAAG,cAAMH,MAAE,EAAEG,GAAC;AAAE,eAAO,KAAK,kBAAkBH,IAAE,KAAK,GAAEA;AAAA,MAAC;AAAC,UAAGK,GAAEF,GAAC,GAAE;AAAC,aAAK,YAAU;AAAG,cAAMH,MAAE,EAAEG,GAAC;AAAE,eAAO,KAAK,kBAAkBH,IAAE,KAAK,GAAEA;AAAA,MAAC;AAAC,UAAGM,GAAEH,GAAC,GAAE;AAAC,YAAG,KAAK,aAAa,QAAOA;AAAE,aAAK,YAAU;AAAG,cAAMJ,MAAE,CAAC,CAACI,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC;AAAE,eAAO,KAAK,aAAaJ,GAAC,GAAE,EAAC,OAAM,CAACA,GAAC,EAAC;AAAA,MAAC;AAAC,MAAAI,MAAE,KAAK,iBAAiB,KAAK;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,kBAAkBJ,KAAE;AAAC,QAAGA,IAAE,YAAUC,OAAKD,IAAE,MAAK,aAAaC,GAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,KAAE;AAAC,QAAGA,KAAE;AAAC,UAAIC,KAAEC,KAAEC,KAAEJ,KAAEM,KAAEG,KAAEC,MAAET,IAAE,QAAOU,KAAEV,IAAE,CAAC;AAAE,WAAK,aAAW,EAAES;AAAE,eAAQE,MAAE,GAAEA,MAAEF,KAAE,EAAEE,KAAE;AAAC,YAAIC;AAAE,QAAAA,KAAE,KAAK,aAAWD,QAAIF,MAAE,IAAET,IAAE,CAAC,IAAEA,IAAEW,GAAC;AAAE,cAAME,KAAED,GAAE,CAAC,IAAEF,GAAE,CAAC,GAAEH,MAAEK,GAAE,CAAC,IAAEF,GAAE,CAAC,GAAEI,KAAE,KAAK,KAAKD,KAAEA,KAAEN,MAAEA,GAAC;AAAE,YAAGI,MAAE,KAAGG,KAAE,KAAGX,MAAE,GAAE;AAAC,WAACF,MAAEY,KAAEX,MAAEK,OAAGO,KAAEX,OAAG,KAAK,gBAAcY,GAAEL,IAAE,CAAC;AAAA,QAAC;AAAC,cAAIC,QAAIZ,MAAEc,IAAER,MAAEE,KAAEC,MAAEM,KAAGA,KAAE,MAAIJ,KAAEE,IAAEX,MAAEY,IAAEX,MAAEK,KAAEJ,MAAEW;AAAA,MAAE;AAAC,UAAG,KAAK,aAAWX,MAAE,KAAGK,MAAE,GAAE;AAAC,SAACP,MAAEF,MAAEG,MAAEG,OAAGG,MAAEL,OAAG,KAAK,gBAAcY,GAAEf,IAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACAlzC,IAAMgB,KAAE;AAAI,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,QAAM,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,QAAQC,KAAEF,KAAE;AAAC,IAAAA,OAAGE,IAAE,QAAQ,GAAE,MAAM,UAAU,KAAK,MAAM,KAAK,OAAMA,GAAC,GAAEF,OAAGE,IAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAO,UAAUA,KAAEF,KAAE;AAAC,IAAAA,OAAG,MAAM,UAAU,KAAK,MAAME,KAAEF,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUE,KAAE;AAAC,SAAK,MAAM,KAAKA,GAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,KAAE;AAAC,SAAK,MAAM,KAAKA,GAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,MAAE,KAAK;AAAM,IAAAA,IAAE,SAAO,MAAIA,IAAE,CAAC,EAAE,CAAC,MAAIA,IAAEA,IAAE,SAAO,CAAC,EAAE,CAAC,KAAGA,IAAE,CAAC,EAAE,CAAC,MAAIA,IAAEA,IAAE,SAAO,CAAC,EAAE,CAAC,KAAGA,IAAE,KAAK,CAACA,IAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,EAAE;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYD,MAAE,GAAEF,MAAE,OAAG;AAAA,EAAC;AAAA,EAAC,UAAUE,KAAE;AAAC,UAAMF,MAAE,KAAK,KAAKE,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,CAAC;AAAE,UAAIF,QAAIE,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,KAAGF;AAAA,EAAE;AAAA,EAAC,gBAAgBE,KAAEF,KAAE;AAAC,UAAMC,MAAED,IAAE,CAAC,IAAEE,IAAE,CAAC,GAAEC,MAAEH,IAAE,CAAC,IAAEE,IAAE,CAAC;AAAE,WAAO,KAAK,KAAKD,MAAEA,MAAEE,MAAEA,GAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBD,KAAEF,KAAE;AAAC,WAAO,KAAK,gBAAgBE,IAAEF,GAAC,GAAEE,IAAEF,MAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBE,KAAE;AAAC,QAAIF,MAAE;AAAE,UAAMC,MAAEC,MAAEA,IAAE,SAAO;AAAE,aAAQC,MAAE,GAAEA,MAAEF,MAAE,GAAE,EAAEE,IAAE,CAAAH,OAAG,KAAK,mBAAmBE,KAAEC,GAAC;AAAE,WAAOH;AAAA,EAAC;AAAA,EAAC,kBAAkBE,KAAE;AAAC,QAAIF,MAAE;AAAE,UAAMC,MAAEC,MAAEA,IAAE,SAAO;AAAE,aAAQC,MAAE,GAAEA,MAAEF,MAAE,GAAE,EAAEE,IAAE,CAAAH,QAAIE,IAAEC,MAAE,CAAC,EAAE,CAAC,IAAED,IAAEC,GAAC,EAAE,CAAC,MAAID,IAAEC,MAAE,CAAC,EAAE,CAAC,IAAED,IAAEC,GAAC,EAAE,CAAC;AAAG,WAAOH,MAAE;AAAA,EAAC;AAAA,EAAC,WAAWE,KAAEF,KAAEC,KAAE;AAAC,WAAM,CAACC,IAAE,CAAC,KAAGF,IAAE,CAAC,IAAEE,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,KAAGF,IAAE,CAAC,IAAEE,IAAE,CAAC,KAAGD,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcC,KAAEF,KAAEC,KAAE;AAAC,WAAO,KAAK,WAAWC,IAAEF,GAAC,GAAEE,IAAEF,MAAE,CAAC,GAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,SAASC,KAAEF,KAAEC,KAAE;AAAC,UAAME,MAAEH,IAAE,CAAC,IAAEE,IAAE,CAAC,GAAEE,MAAEJ,IAAE,CAAC,IAAEE,IAAE,CAAC;AAAE,WAAO,KAAK,MAAME,KAAED,GAAC;AAAA,EAAC;AAAA,EAAC,YAAYD,KAAEF,KAAEC,KAAE;AAAC,WAAO,KAAK,SAASC,IAAEF,GAAC,GAAEE,IAAEF,MAAE,CAAC,GAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,WAAWC,KAAEF,KAAEC,KAAE;AAAC,UAAME,MAAEH,IAAE,CAAC,IAAEE,IAAE,CAAC,GAAEE,MAAEJ,IAAE,CAAC,IAAEE,IAAE,CAAC,GAAEG,KAAE,KAAK,KAAKF,MAAEA,MAAEC,MAAEA,GAAC;AAAE,WAAOC,KAAE,IAAE,CAACF,MAAEE,IAAED,MAAEC,EAAC,IAAE,CAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcH,KAAEF,KAAEC,KAAE;AAAC,WAAO,KAAK,WAAWC,IAAEF,GAAC,GAAEE,IAAEF,MAAE,CAAC,GAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,IAAIC,KAAEF,KAAEC,KAAEE,KAAE;AAAC,WAAM,CAACF,OAAG,IAAEC,IAAEF,GAAC,IAAE,KAAK,cAAcE,KAAEF,KAAEC,GAAC,GAAEE,OAAG,IAAED,IAAEF,MAAE,CAAC,IAAE,KAAK,cAAcE,KAAEF,KAAEG,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWD,KAAEF,KAAEC,KAAE;AAAC,IAAAA,OAAGC,IAAE,KAAKF,IAAE,CAAC,CAAC,GAAEE,IAAE,KAAKF,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYE,KAAEF,KAAEC,KAAE;AAAC,UAAME,MAAE,CAAC;AAAE,WAAO,KAAK,eAAeA,KAAED,KAAEF,KAAEC,GAAC,IAAEE,MAAE;AAAA,EAAI;AAAA,EAAC,eAAeD,KAAEF,KAAEC,KAAEE,KAAE;AAAC,UAAMC,MAAEJ,MAAEA,IAAE,SAAO,IAAE;AAAE,QAAIK,KAAE,GAAEC,MAAE,MAAGC,MAAE;AAAE,WAAKA,MAAEH,OAAG;AAAC,YAAMA,MAAE,KAAK,mBAAmBJ,KAAEO,GAAC;AAAE,UAAG,MAAIH,KAAE;AAAC,YAAGE,KAAE;AAAC,cAAGD,KAAED,MAAEH,KAAE;AAAC,kBAAMO,OAAGP,MAAEI,MAAGD;AAAE,gBAAIK,MAAE,GAAEC,MAAE;AAAG,YAAAL,KAAED,OAAGD,QAAIM,OAAGN,MAAEE,MAAGD,KAAEM,MAAE;AAAI,kBAAMC,MAAE,KAAK,IAAIX,KAAEO,KAAEC,KAAEC,GAAC;AAAE,gBAAGE,OAAG,KAAK,WAAWT,KAAES,KAAEL,GAAC,GAAEI,IAAE;AAAM,YAAAJ,MAAE;AAAA,UAAE;AAAA,QAAC,OAAK;AAAC,cAAGD,KAAED,MAAED,KAAE;AAAC,kBAAMF,MAAE,KAAK,IAAID,KAAEO,KAAE,IAAGJ,MAAEE,MAAGD,GAAC;AAAE,YAAAH,OAAG,KAAK,WAAWC,KAAED,KAAEK,GAAC;AAAE;AAAA,UAAK;AAAC,eAAK,WAAWJ,KAAE,CAACF,IAAEO,GAAC,GAAEP,IAAEO,MAAE,CAAC,CAAC,GAAED,GAAC;AAAA,QAAC;AAAC,QAAAD,MAAGD,KAAE,EAAEG;AAAA,MAAC,MAAK,GAAEA;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,iBAAiBL,KAAEF,KAAE;AAAC,UAAMC,MAAEC,MAAEA,IAAE,SAAO,IAAE;AAAE,QAAIC,MAAE,GAAEC,MAAE;AAAG,WAAKA,MAAEH,OAAG;AAAC,QAAEG;AAAE,YAAMH,MAAE,KAAK,mBAAmBC,KAAEE,GAAC;AAAE,UAAG,MAAIH,KAAE;AAAC,YAAGE,MAAEF,MAAED,KAAE;AAAC,gBAAMK,MAAGL,MAAEG,OAAGF;AAAE,iBAAO,KAAK,WAAWC,IAAEE,GAAC,GAAEF,IAAEE,MAAE,CAAC,GAAEC,EAAC;AAAA,QAAC;AAAC,QAAAF,OAAGF;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,QAAQC,KAAEF,KAAE;AAAC,QAAG,CAACE,OAAGA,IAAE,UAAQ,EAAE,QAAM;AAAG,UAAMD,MAAEC,MAAEA,IAAE,SAAO,IAAE;AAAE,QAAIC,MAAE;AAAG,WAAKA,MAAEF,OAAG;AAAC,UAAG,EAAEE,KAAED,IAAEC,MAAE,CAAC,EAAE,CAAC,MAAID,IAAEC,GAAC,EAAE,CAAC,KAAGD,IAAEC,MAAE,CAAC,EAAE,CAAC,MAAID,IAAEC,GAAC,EAAE,CAAC,EAAE,QAAM;AAAG,UAAGH,OAAGE,IAAEC,MAAE,CAAC,EAAE,CAAC,MAAID,IAAEC,GAAC,EAAE,CAAC,EAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,OAAOH,KAAEC,KAAEE,KAAEC,KAAEC,IAAE;AAAC,QAAG,CAACL,OAAGA,IAAE,SAAO,EAAE,QAAO;AAAK,QAAIM,MAAE,GAAEC,MAAEP,IAAEM,KAAG,GAAEE,MAAEF;AAAE,WAAKA,MAAEN,IAAE,UAAQ;AAAC,YAAME,MAAEF,IAAEM,GAAC;AAAE,MAAAJ,IAAE,CAAC,MAAIK,IAAE,CAAC,KAAGL,IAAE,CAAC,MAAIK,IAAE,CAAC,MAAID,QAAIE,QAAIR,IAAEQ,GAAC,IAAER,IAAEM,GAAC,IAAGC,MAAEP,IAAEQ,KAAG,IAAGF;AAAA,IAAG;AAAC,UAAMG,MAAET,IAAE,CAAC,EAAE,CAAC,MAAIA,IAAEQ,MAAE,CAAC,EAAE,CAAC,KAAGR,IAAE,CAAC,EAAE,CAAC,MAAIA,IAAEQ,MAAE,CAAC,EAAE,CAAC;AAAE,QAAGC,OAAG,EAAED,KAAEA,OAAGC,MAAE,IAAE,GAAG,QAAO;AAAK,UAAMC,MAAE,CAAC;AAAE,IAAAH,MAAEE,MAAET,IAAEQ,MAAE,CAAC,IAAE;AAAK,QAAIG,MAAEX,IAAE,CAAC;AAAE,aAAQY,MAAE,GAAEA,MAAEJ,KAAEI,OAAI;AAAC,YAAMP,KAAEO,QAAIJ,MAAE,IAAEC,MAAET,IAAE,CAAC,IAAE,OAAKA,IAAEY,MAAE,CAAC;AAAE,UAAGL,IAAE,KAAGF,IAAE;AAAC,cAAML,MAAE,CAACK,GAAE,CAAC,IAAEM,IAAE,CAAC,GAAEN,GAAE,CAAC,IAAEM,IAAE,CAAC,CAAC;AAAE,aAAK,UAAUX,GAAC;AAAE,cAAMM,MAAE,CAACK,IAAE,CAAC,IAAEJ,IAAE,CAAC,GAAEI,IAAE,CAAC,IAAEJ,IAAE,CAAC,CAAC;AAAE,aAAK,UAAUD,GAAC;AAAE,cAAME,MAAEF,IAAE,CAAC,IAAEN,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEN,IAAE,CAAC,GAAES,MAAEH,IAAE,CAAC,IAAEN,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEN,IAAE,CAAC;AAAE,YAAG,MAAIQ,OAAG,MAAIC,KAAE;AAAC,UAAAE,MAAEN;AAAE;AAAA,QAAQ;AAAC,YAAGG,OAAG,KAAGP,OAAG,GAAE;AAAC,cAAGQ,MAAE,GAAE;AAAC,kBAAMP,MAAE,CAACF,IAAE,CAAC,IAAEM,IAAE,CAAC,GAAEN,IAAE,CAAC,IAAEM,IAAE,CAAC,CAAC;AAAE,iBAAK,UAAUJ,GAAC;AAAE,kBAAMC,MAAE,KAAK,MAAM,IAAEM,OAAG,CAAC;AAAE,gBAAGN,MAAE,IAAEC,KAAE;AAAC,oBAAMJ,MAAE,CAAC,KAAK,IAAIC,GAAC,IAAEE;AAAE,cAAAO,IAAE,KAAK,CAACC,IAAE,CAAC,IAAET,IAAE,CAAC,IAAEF,KAAEW,IAAE,CAAC,IAAET,IAAE,CAAC,IAAEF,GAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,MAAM,SAAOG,KAAE;AAAA,UAAC,KAAK,EAAE,SAAQ;AAAC,kBAAMD,MAAE,KAAK,MAAM,IAAEO,OAAG,CAAC;AAAE,gBAAGP,MAAE,KAAG,IAAEA,MAAEE,KAAE;AAAC,oBAAMD,MAAE,CAACH,IAAE,CAAC,IAAEM,IAAE,CAAC,GAAEN,IAAE,CAAC,IAAEM,IAAE,CAAC,CAAC;AAAE,mBAAK,UAAUH,GAAC;AAAE,oBAAMC,MAAE,KAAK,IAAIH,GAAC,IAAEC;AAAE,cAAAQ,IAAE,KAAK,CAACC,IAAE,CAAC,IAAER,IAAE,CAAC,IAAEC,KAAEO,IAAE,CAAC,IAAER,IAAE,CAAC,IAAEC,GAAC,CAAC;AAAE;AAAA,YAAK;AAAA,UAAC;AAAA,UAAC,KAAK,EAAE;AAAS,YAAAM,IAAE,KAAK,CAACC,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAEL,KAAEU,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAEL,GAAC,CAAC,GAAES,IAAE,KAAK,CAACC,IAAE,CAAC,IAAEX,IAAE,CAAC,IAAEC,KAAEU,IAAE,CAAC,IAAEX,IAAE,CAAC,IAAEC,GAAC,CAAC;AAAE;AAAA,UAAM,KAAK,EAAE;AAAQ,gBAAGQ,MAAE,GAAE;AAAC,cAAAC,IAAE,KAAK,CAACC,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAEL,KAAEU,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAEL,GAAC,CAAC;AAAE,oBAAMC,MAAE,KAAK,MAAM,OAAK,IAAEO,IAAE;AAAE,kBAAGP,MAAE,GAAE;AAAC,sBAAMC,MAAE,IAAED;AAAE,oBAAIE,MAAED;AAAE,yBAAQE,MAAE,GAAEA,MAAEH,KAAEG,OAAID,OAAGD,KAAE;AAAC,wBAAMD,MAAE,CAACI,IAAE,CAAC,KAAG,IAAEF,OAAGJ,IAAE,CAAC,IAAEI,KAAE,CAACE,IAAE,CAAC,KAAG,IAAEF,OAAGJ,IAAE,CAAC,IAAEI,GAAC;AAAE,uBAAK,UAAUF,GAAC,GAAEQ,IAAE,KAAK,CAACC,IAAE,CAAC,IAAET,IAAE,CAAC,IAAED,KAAEU,IAAE,CAAC,IAAET,IAAE,CAAC,IAAED,GAAC,CAAC;AAAA,gBAAC;AAAA,cAAC;AAAC,cAAAS,IAAE,KAAK,CAACC,IAAE,CAAC,IAAEX,IAAE,CAAC,IAAEC,KAAEU,IAAE,CAAC,IAAEX,IAAE,CAAC,IAAEC,GAAC,CAAC;AAAA,YAAC;AAAC;AAAA,UAAM,KAAK,EAAE;AAAA,UAAO;AAAQ,gBAAGO,MAAE,EAAE,CAAAE,IAAE,KAAK,CAACC,IAAE,CAAC,KAAGL,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAGL,KAAEU,IAAE,CAAC,KAAGL,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAGL,GAAC,CAAC,GAAES,IAAE,KAAK,CAACC,IAAE,CAAC,KAAGX,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAGC,KAAEU,IAAE,CAAC,KAAGX,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAGC,GAAC,CAAC;AAAA,iBAAM;AAAC,oBAAMC,MAAE,KAAK,MAAM,IAAE,KAAK,IAAIO,GAAC,KAAG,CAAC,GAAEN,MAAE,CAACH,IAAE,CAAC,IAAEM,IAAE,CAAC,GAAEN,IAAE,CAAC,IAAEM,IAAE,CAAC,CAAC;AAAE,mBAAK,UAAUH,GAAC;AAAE,oBAAMC,MAAEH,MAAEC;AAAE,cAAAQ,IAAE,KAAK,CAACC,IAAE,CAAC,IAAER,IAAE,CAAC,IAAEC,KAAEO,IAAE,CAAC,IAAER,IAAE,CAAC,IAAEC,GAAC,CAAC;AAAA,YAAC;AAAA,QAAC;AAAA,MAAC,OAAK;AAAC,cAAMF,MAAE,CAACS,IAAE,CAAC,IAAEJ,IAAE,CAAC,GAAEI,IAAE,CAAC,IAAEJ,IAAE,CAAC,CAAC;AAAE,aAAK,UAAUL,GAAC,GAAEQ,IAAE,KAAK,CAACC,IAAE,CAAC,IAAET,IAAE,CAAC,IAAED,KAAEU,IAAE,CAAC,IAAET,IAAE,CAAC,IAAED,GAAC,CAAC;AAAA,MAAC;AAAA,WAAK;AAAC,cAAMC,MAAE,CAACG,GAAE,CAAC,IAAEM,IAAE,CAAC,GAAEN,GAAE,CAAC,IAAEM,IAAE,CAAC,CAAC;AAAE,aAAK,UAAUT,GAAC,GAAEQ,IAAE,KAAK,CAACC,IAAE,CAAC,IAAET,IAAE,CAAC,IAAED,KAAEU,IAAE,CAAC,IAAET,IAAE,CAAC,IAAED,GAAC,CAAC;AAAA,MAAC;AAAC,MAAAM,MAAEI,KAAEA,MAAEN;AAAA,IAAC;AAAC,WAAOK,IAAE,UAAQD,MAAE,IAAE,KAAG,QAAMA,OAAGC,IAAE,KAAK,CAACA,IAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAEA;AAAA,EAAE;AAAC;;;ACApxH,IAAMG,KAAE;AAAR,IAA2BC,KAAE;AAA7B,IAA+BC,KAAE,EAAE;AAAU,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,GAAE,WAAS;AAAK,IAAMM,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYN,KAAEC,KAAEG,KAAE;AAAC,UAAMJ,KAAE,OAAG,IAAE,GAAE,KAAK,eAAa,IAAIG,MAAE,KAAK,UAAQ,WAASF,IAAE,QAAMA,IAAE,QAAMJ,MAAGO,KAAE,KAAK,aAAW,WAASH,IAAE,2BAAyBA,IAAE,2BAAyB,WAASA,IAAE,YAAUA,IAAE,YAAUH,IAAE,KAAK,sBAAoBG,KAAEG;AAAA,EAAC;AAAA,EAAC,YAAYJ,KAAE;AAAC,YAAO,KAAK,YAAW;AAAA,MAAC,KAAK,EAAE;AAAA,MAAU;AAAQ,eAAO,KAAK,sBAAsBA,KAAE,IAAE;AAAA,MAAE,KAAK,EAAE;AAAM,eAAO,KAAK,sBAAsBA,KAAE,KAAE;AAAA,MAAE,KAAK,EAAE;AAAQ,eAAO,KAAK,uBAAuBA,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBA,KAAEC,KAAE;AAAC,UAAMC,MAAE,KAAK,aAAa,oBAAoBF,GAAC;AAAE,QAAIG,MAAE,KAAK;AAAO,IAAAD,MAAE,IAAEC,QAAIA,MAAED,MAAE;AAAG,UAAMK,MAAE,KAAK,aAAa,YAAYP,KAAE,GAAEE,MAAEC,GAAC;AAAE,QAAG,CAACI,IAAE,QAAO;AAAK,UAAMC,MAAEL,MAAE;AAAE,QAAG,KAAK,aAAa,QAAQI,KAAE,KAAE,EAAE,QAAO;AAAK,UAAMX,MAAE,KAAK,iBAAiBW,KAAE,CAACC,GAAC;AAAE,QAAG,CAACZ,IAAE,QAAO;AAAK,UAAMC,MAAE,KAAK,iBAAiBU,KAAEC,GAAC;AAAE,QAAG,CAACX,IAAE,QAAO;AAAK,UAAMC,MAAE,KAAK,yBAAyBF,KAAE,CAACY,MAAE,CAAC;AAAE,QAAG,CAACV,IAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,yBAAyBF,KAAEW,MAAE,CAAC;AAAE,QAAG,CAACT,GAAE,QAAO;AAAK,UAAMM,MAAEL,IAAEA,IAAE,SAAO,CAAC;AAAE,IAAAC,QAAI,KAAK,kBAAkBJ,KAAE,IAAE,GAAE,KAAK,kBAAkBD,KAAE,IAAE;AAAG,UAAMa,KAAE,IAAIF;AAAE,WAAOE,GAAE,QAAQZ,KAAE,IAAE,GAAEY,GAAE,OAAOV,EAAC,GAAE,KAAK,kBAAkBU,GAAE,KAAK,CAAC,GAAEA,GAAE,OAAOJ,GAAC,GAAE,KAAK,kBAAkBI,GAAE,KAAK,CAAC,GAAEA,GAAE,OAAOX,GAAC,GAAE,KAAK,kBAAkBW,GAAE,KAAK,CAAC,GAAEA,GAAE,QAAQb,KAAE,KAAE,GAAEK,MAAE,EAAC,OAAM,CAACQ,GAAE,KAAK,CAAC,EAAC,KAAGA,GAAE,MAAM,GAAE,EAAC,OAAM,CAACA,GAAE,KAAK,CAAC,EAAC;AAAA,EAAE;AAAA,EAAC,uBAAuBT,KAAE;AAAC,UAAMC,MAAE,KAAK,aAAa,oBAAoBD,GAAC;AAAE,QAAIE,MAAE,KAAK;AAAO,IAAAD,MAAEC,OAAG,IAAEN,KAAE,OAAKM,MAAED,OAAG,IAAEL,KAAE;AAAI,UAAMO,MAAE,KAAK,aAAa,YAAYH,KAAE,GAAEC,MAAEC,OAAG,IAAEN,GAAE;AAAE,QAAG,CAACO,IAAE,QAAO;AAAK,UAAMI,MAAEL,MAAE;AAAE,QAAG,KAAK,aAAa,QAAQC,KAAE,KAAE,EAAE,QAAO;AAAK,UAAMK,MAAE,KAAK,iBAAiBL,KAAEI,GAAC;AAAE,QAAG,CAACC,IAAE,QAAO;AAAK,UAAMX,MAAE,KAAK,iBAAiBM,KAAE,CAACI,GAAC;AAAE,QAAG,CAACV,IAAE,QAAO;AAAK,UAAMC,MAAE,KAAK,aAAa,YAAYE,KAAE,GAAEC,MAAEC,GAAC;AAAE,QAAG,CAACJ,IAAE,QAAO;AAAK,QAAG,KAAK,aAAa,QAAQA,KAAE,KAAE,EAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,iBAAiBD,KAAES,GAAC;AAAE,QAAG,CAACR,GAAE,QAAO;AAAK,UAAMM,MAAE,KAAK,iBAAiBP,KAAE,CAACS,GAAC;AAAE,QAAG,CAACF,IAAE,QAAO;AAAK,UAAMI,KAAEV,GAAEA,GAAE,SAAO,CAAC,GAAEW,MAAE,KAAK,yBAAyBX,IAAEQ,MAAE,CAAC;AAAE,QAAG,CAACG,IAAE,QAAO;AAAK,UAAMC,KAAEN,IAAEA,IAAE,SAAO,CAAC,GAAEO,KAAE,KAAK,yBAAyBP,KAAE,CAACE,MAAE,CAAC;AAAE,QAAG,CAACK,GAAE,QAAO;AAAK,UAAMC,KAAEb,IAAEA,IAAE,SAAO,CAAC;AAAE,SAAK,kBAAkBQ,KAAE,KAAE,GAAE,KAAK,kBAAkBX,KAAE,KAAE;AAAE,UAAMiB,KAAE,IAAIP;AAAE,WAAOO,GAAE,QAAQN,KAAE,IAAE,GAAE,KAAK,kBAAkBM,GAAE,KAAK,CAAC,GAAEA,GAAE,OAAOH,EAAC,GAAEG,GAAE,OAAOF,EAAC,GAAE,KAAK,kBAAkBE,GAAE,KAAK,CAAC,GAAEA,GAAE,OAAOD,EAAC,GAAE,KAAK,kBAAkBC,GAAE,KAAK,CAAC,GAAEA,GAAE,OAAOJ,GAAC,GAAE,KAAK,kBAAkBI,GAAE,KAAK,CAAC,GAAEA,GAAE,OAAOL,EAAC,GAAE,KAAK,kBAAkBK,GAAE,KAAK,CAAC,GAAEA,GAAE,QAAQjB,KAAE,KAAE,GAAE,EAAC,OAAM,CAACiB,GAAE,KAAK,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBd,KAAEC,KAAE;AAAC,WAAO,KAAK,aAAa,OAAOD,KAAEC,KAAE,EAAE,SAAQ,GAAE,KAAK,mBAAmB;AAAA,EAAC;AAAA,EAAC,yBAAyBD,KAAEC,KAAE;AAAC,QAAG,CAACD,OAAGA,IAAE,SAAO,EAAE,QAAO;AAAK,UAAME,MAAEF,IAAEA,IAAE,SAAO,CAAC,GAAEG,MAAEH,IAAEA,IAAE,SAAO,CAAC,GAAEI,MAAE,CAACD,IAAE,CAAC,IAAED,IAAE,CAAC,GAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,CAAC;AAAE,WAAO,KAAK,aAAa,UAAUE,GAAC,GAAE,CAACD,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAEH,KAAEE,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAEH,GAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBD,KAAEE,MAAE,OAAG;AAAC,IAAAa,GAAEb,MAAEF,IAAE,CAAC,IAAEA,IAAEA,IAAE,SAAO,CAAC,GAAE,CAAC;AAAA,EAAC;AAAC;;;ACA7nF,IAAMgB,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEL,KAAEC,KAAEC,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACL,GAAE,WAAS;AAAK,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,SAAK,mBAAiBJ,KAAE,KAAK,WAASG,KAAE,KAAK,kBAAgBC,KAAE,KAAK,eAAa,IAAIE,MAAE,KAAK,SAAO,WAASL,IAAE,OAAKA,IAAE,OAAK,KAAGC,KAAE,KAAK,sBAAoBF,KAAEE;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAIK;AAAE,WAAKA,KAAE,KAAK,iBAAiB,KAAK,KAAG;AAAC,UAAG,MAAI,KAAK,MAAM,QAAOA;AAAE,UAAGC,GAAED,EAAC;AAAE,YAAG,KAAK,QAAM,GAAE;AAAC,gBAAMP,MAAE,CAAC,CAACO,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,CAAC,GAAEN,MAAE,KAAK,aAAa,OAAOD,KAAE,KAAK,OAAM,EAAE,SAAQ,GAAE,KAAK,mBAAmB;AAAE,cAAGC,IAAE,QAAM,EAAC,OAAM,CAACA,GAAC,EAAC;AAAA,QAAC,WAAS,KAAK,QAAM,KAAG,KAAK,IAAIM,GAAE,OAAKA,GAAE,MAAKA,GAAE,OAAKA,GAAE,IAAI,IAAE,IAAE,KAAK,QAAM,EAAE,QAAM,EAAC,MAAKA,GAAE,OAAK,KAAK,OAAM,MAAKA,GAAE,OAAK,KAAK,OAAM,MAAKA,GAAE,OAAK,KAAK,OAAM,MAAKA,GAAE,OAAK,KAAK,MAAK;AAAA;AAAE,YAAME,MAAE,KAAK;AAAgB,UAAG,EAAEA,GAAC,EAAE,QAAO;AAAK,UAAIV,MAAEQ;AAAE,WAAI,CAAC,EAAEA,EAAC,KAAG,CAAC,KAAK,aAAWR,MAAEA,GAAEQ,IAAE,KAAK,IAAI,KAAK,KAAK,IAAE,CAAC,GAAER,OAAGA,IAAE,SAAO,MAAIA,IAAE,MAAM,aAAW,CAACM,GAAEE,EAAC,KAAG,CAAC,KAAK,aAAWR,MAAEW,GAAEH,IAAE,KAAK,IAAI,KAAK,KAAK,IAAE,CAAC,GAAER,OAAGA,IAAE,SAAO,MAAIA,IAAE,MAAM,SAAS,QAAOU,IAAE,OAAO,EAAE,aAAYV,KAAE,KAAK,OAAM,CAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAC;;;ACA/xC,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQY,KAAEC,KAAEC,KAAEC,IAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAAC,EAAE,WAAS;AAAK,IAAMG,KAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAE;AAAC,SAAK,oBAAkB,IAAG,KAAK,mBAAiBF,KAAE,KAAK,qBAAmBE,KAAE,KAAK,QAAMD,IAAE,QAAM,EAAE,cAAa,KAAK,eAAa,KAAK,oBAAkBC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAIE;AAAE,WAAKA,MAAE,KAAK,iBAAiB,KAAK,KAAG;AAAC,UAAIE;AAAE,UAAGN,GAAEI,GAAC,IAAEE,MAAE,KAAK,aAAa,CAAC,CAAC,CAACF,IAAE,GAAEA,IAAE,CAAC,CAAC,CAAC,CAAC,IAAE,EAAEA,GAAC,IAAEE,MAAE,KAAK,aAAa,CAACF,IAAE,MAAM,CAAC,IAAEG,GAAEH,GAAC,IAAEE,MAAE,KAAK,aAAaF,IAAE,KAAK,IAAE,EAAEA,GAAC,MAAIE,MAAE,KAAK,aAAaF,IAAE,KAAK,IAAGE,OAAGA,IAAE,OAAO,QAAM,EAAC,OAAMA,IAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,OAAON,KAAE;AAAC,WAAM,CAACA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,KAAEC,KAAE;AAAC,WAAM,EAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,KAAG,IAAGD,IAAE,CAAC,IAAEC,IAAE,CAAC,KAAG,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,KAAEC,KAAEC,KAAEC,IAAE;AAAC,WAAM,CAACH,IAAE,CAAC,IAAEC,MAAEC,IAAE,CAAC,IAAEC,IAAEH,IAAE,CAAC,IAAEC,MAAEC,IAAE,CAAC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKH,KAAEC,KAAE;AAAC,WAAM,CAACD,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAMD,KAAEC,KAAEC,KAAE;AAAC,WAAM,CAACF,IAAE,CAAC,IAAEC,KAAED,IAAE,CAAC,IAAEE,GAAC;AAAA,EAAC;AAAA,EAAC,KAAKF,KAAEC,KAAE;AAAC,WAAM,CAACD,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAMD,KAAEC,KAAE;AAAC,WAAO,KAAK,MAAMD,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,EAAE;AAAA,EAAC;AAAA,EAAC,MAAMD,KAAE;AAAC,WAAO,KAAK,KAAKA,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,KAAEC,MAAE,GAAE;AAAC,UAAMC,MAAED,MAAE,KAAK,MAAMD,GAAC;AAAE,IAAAA,IAAE,CAAC,KAAGE,KAAEF,IAAE,CAAC,KAAGE;AAAA,EAAC;AAAA,EAAC,mBAAmBF,KAAE;AAAC,UAAMC,MAAE,CAACD,IAAE,CAAC,GAAEE,MAAEF,IAAE,CAAC;AAAE,IAAAA,IAAE,CAAC,IAAEC,KAAED,IAAE,CAAC,IAAEE;AAAA,EAAC;AAAA,EAAC,UAAUF,KAAE;AAAC,WAAM,CAAC,CAACA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,KAAE;AAAC,UAAMC,MAAED,IAAE,CAAC,GAAEE,MAAE,CAACF,IAAE,CAAC;AAAE,IAAAA,IAAE,CAAC,IAAEC,KAAED,IAAE,CAAC,IAAEE;AAAA,EAAC;AAAA,EAAC,WAAWF,KAAE;AAAC,WAAM,CAACA,IAAE,CAAC,GAAE,CAACA,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,KAAEC,KAAE;AAAC,WAAOD,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,KAAEC,KAAE;AAAC,WAAOD,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,KAAEC,KAAEC,KAAE;AAAC,UAAMC,KAAEH,IAAE,CAAC,IAAEC,MAAED,IAAE,CAAC,IAAEE,KAAEE,MAAEJ,IAAE,CAAC,IAAEE,MAAEF,IAAE,CAAC,IAAEC;AAAE,IAAAD,IAAE,CAAC,IAAEG,IAAEH,IAAE,CAAC,IAAEI;AAAA,EAAC;AAAA,EAAC,YAAYJ,KAAE;AAAC,UAAMC,MAAE,CAACD,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC;AAAE,WAAOQ,GAAEP,KAAE,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,gBAAgBD,KAAEC,KAAEC,KAAEC,IAAE;AAAC,UAAMC,MAAE,KAAK,KAAKF,KAAED,GAAC;AAAE,SAAK,WAAWG,GAAC;AAAE,UAAME,MAAE,KAAK,cAAcF,KAAE,KAAK,KAAKD,IAAEF,GAAC,CAAC;AAAE,QAAIQ;AAAE,IAAAA,KAAEH,MAAE,IAAE,KAAK,WAAWF,GAAC,IAAE,KAAK,UAAUA,GAAC;AAAE,UAAMC,MAAE,KAAK,IAAIC,GAAC,IAAE,GAAEI,MAAE,CAAC;AAAE,IAAAA,IAAE,KAAK,CAACT,IAAE,CAAC,KAAGQ,GAAE,CAAC,IAAEL,IAAE,CAAC,KAAGC,KAAEJ,IAAE,CAAC,KAAGQ,GAAE,CAAC,IAAEL,IAAE,CAAC,KAAGC,GAAC,CAAC,GAAEK,IAAE,KAAKT,GAAC,GAAES,IAAE,KAAKR,GAAC,GAAEQ,IAAE,KAAK,CAACR,IAAE,CAAC,KAAGO,GAAE,CAAC,IAAEL,IAAE,CAAC,KAAGC,KAAEH,IAAE,CAAC,KAAGO,GAAE,CAAC,IAAEL,IAAE,CAAC,KAAGC,GAAC,CAAC,GAAEL,IAAE,KAAKU,GAAC;AAAA,EAAC;AAAA,EAAC,YAAYV,KAAEC,KAAEC,KAAEC,IAAEC,KAAE;AAAC,QAAG,KAAGA,MAAI,QAAO,KAAKJ,IAAE,KAAKG,EAAC;AAAE,UAAMG,MAAE,KAAK,KAAKL,KAAEC,GAAC,GAAEO,KAAE,KAAK,KAAKP,KAAEC,EAAC,GAAEE,MAAE,KAAK,KAAKC,KAAEG,EAAC;AAAE,SAAK,YAAYT,KAAEC,KAAEK,KAAED,KAAED,GAAC,GAAE,KAAK,YAAYJ,KAAEK,KAAEI,IAAEN,IAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,YAAYJ,KAAEC,KAAEC,KAAEC,IAAEC,KAAEE,KAAE;AAAC,QAAG,KAAGA,MAAI,QAAO,KAAKN,IAAE,KAAKI,GAAC;AAAE,UAAMK,KAAE,KAAK,KAAKR,KAAEC,GAAC,GAAEG,MAAE,KAAK,KAAKH,KAAEC,EAAC,GAAEO,MAAE,KAAK,KAAKP,IAAEC,GAAC,GAAEO,MAAE,KAAK,KAAKF,IAAEJ,GAAC,GAAEO,MAAE,KAAK,KAAKP,KAAEK,GAAC,GAAEG,MAAE,KAAK,KAAKF,KAAEC,GAAC;AAAE,SAAK,YAAYZ,KAAEC,KAAEQ,IAAEE,KAAEE,KAAEP,GAAC,GAAE,KAAK,YAAYN,KAAEa,KAAED,KAAEF,KAAEN,KAAEE,GAAC;AAAA,EAAC;AAAA,EAAC,aAAaN,KAAEC,KAAEC,KAAEC,IAAEC,KAAE;AAAC,UAAME,MAAEF,OAAG,KAAK,cAAc,KAAK,KAAKF,KAAED,GAAC,GAAE,KAAK,KAAKE,IAAEF,GAAC,CAAC,IAAE,GAAEQ,KAAE,KAAK,KAAKR,KAAEC,GAAC,GAAEG,MAAE,KAAK,KAAKI,IAAER,GAAC;AAAE,IAAAK,MAAE,KAAK,mBAAmBD,GAAC,IAAE,KAAK,oBAAoBA,GAAC,GAAEI,GAAE,CAAC,KAAGJ,IAAE,CAAC,GAAEI,GAAE,CAAC,KAAGJ,IAAE,CAAC,GAAE,KAAK,YAAYL,KAAEC,KAAE,KAAK,KAAKA,KAAE,SAAOQ,IAAE,OAAM,GAAE,KAAK,KAAKP,KAAE,SAAOO,IAAE,OAAM,GAAEP,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUF,KAAEC,KAAEC,KAAE;AAAC,UAAMC,KAAEF,IAAE,CAAC,GAAEG,MAAEH,IAAE,CAAC,GAAEK,MAAEL,IAAEA,IAAE,SAAO,CAAC,GAAEQ,KAAE,KAAK,KAAKN,IAAEC,GAAC;AAAE,SAAK,WAAWK,EAAC;AAAE,UAAMJ,MAAE,KAAK,cAAcI,IAAE,KAAK,KAAKH,KAAEF,GAAC,CAAC,GAAEM,MAAE,MAAGL,KAAEM,MAAE,KAAK,UAAUF,EAAC,GAAEG,MAAE,CAACN,IAAE,CAAC,IAAEK,IAAE,CAAC,IAAEN,KAAEC,IAAE,CAAC,IAAEK,IAAE,CAAC,IAAEN,GAAC,GAAEQ,MAAEZ,IAAE,SAAO,GAAEa,KAAE,CAAC;AAAE,IAAAA,GAAE,KAAKZ,MAAE,CAAC,CAACS,IAAE,CAAC,GAAE,CAACA,IAAE,CAAC,CAAC,IAAEA,GAAC;AAAE,QAAII,MAAE,CAAC,CAACN,GAAE,CAAC,GAAE,CAACA,GAAE,CAAC,CAAC;AAAE,aAAQO,KAAE,GAAEA,KAAEH,MAAE,GAAEG,MAAI;AAAC,YAAMhB,MAAE,KAAK,KAAKC,IAAEe,KAAE,CAAC,GAAEf,IAAEe,EAAC,CAAC;AAAE,WAAK,WAAWhB,GAAC;AAAE,YAAME,MAAE,KAAK,YAAYF,KAAEe,GAAC,GAAEZ,KAAE,KAAK,cAAcH,KAAEe,GAAC,GAAEX,MAAE,KAAK,MAAM,IAAEF,OAAG,CAAC,GAAEI,MAAE,KAAK,KAAKN,KAAEe,GAAC;AAAE,WAAK,WAAWT,GAAC,GAAEA,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,KAAGF,KAAEU,GAAE,KAAKX,KAAE,IAAE,CAAC,CAACG,IAAE,CAAC,GAAE,CAACA,IAAE,CAAC,CAAC,IAAEA,GAAC,GAAES,MAAEf;AAAA,IAAC;AAAC,IAAAc,GAAE,KAAK,KAAK,WAAWC,GAAC,CAAC;AAAE,aAAQC,KAAEF,GAAE,SAAO,GAAEE,KAAE,GAAEA,KAAI,CAAAhB,IAAE,KAAK,CAACC,IAAEe,EAAC,EAAE,CAAC,IAAEF,GAAEE,EAAC,EAAE,CAAC,IAAEN,KAAET,IAAEe,EAAC,EAAE,CAAC,IAAEF,GAAEE,EAAC,EAAE,CAAC,IAAEN,GAAC,CAAC;AAAE,IAAAV,IAAE,KAAK,CAACY,IAAE,CAAC,IAAEE,GAAE,CAAC,EAAE,CAAC,IAAEJ,KAAEE,IAAE,CAAC,IAAEE,GAAE,CAAC,EAAE,CAAC,IAAEJ,GAAC,CAAC,GAAEV,IAAE,KAAK,CAACY,IAAE,CAAC,IAAEE,GAAE,CAAC,EAAE,CAAC,IAAET,KAAEO,IAAE,CAAC,IAAEE,GAAE,CAAC,EAAE,CAAC,IAAET,GAAC,CAAC,GAAEL,IAAE,KAAKG,EAAC,GAAEH,IAAE,KAAK,CAACY,IAAE,CAAC,IAAEE,GAAE,CAAC,EAAE,CAAC,IAAET,KAAEO,IAAE,CAAC,IAAEE,GAAE,CAAC,EAAE,CAAC,IAAET,GAAC,CAAC,GAAEL,IAAE,KAAK,CAACY,IAAE,CAAC,IAAEE,GAAE,CAAC,EAAE,CAAC,IAAEJ,KAAEE,IAAE,CAAC,IAAEE,GAAE,CAAC,EAAE,CAAC,IAAEJ,GAAC,CAAC;AAAE,aAAQM,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAhB,IAAE,KAAK,CAACC,IAAEe,EAAC,EAAE,CAAC,IAAEF,GAAEE,EAAC,EAAE,CAAC,IAAEN,KAAET,IAAEe,EAAC,EAAE,CAAC,IAAEF,GAAEE,EAAC,EAAE,CAAC,IAAEN,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKV,KAAEC,KAAEC,KAAE;AAAC,WAAOF,IAAE,UAAQ,IAAEA,IAAE,CAAC,IAAE,KAAK,MAAMA,IAAE,CAAC,GAAEC,MAAE,KAAK,cAAaC,MAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,KAAKF,KAAEC,KAAEC,KAAEC,IAAE;AAAC,QAAGH,IAAE,UAAQ,EAAE,QAAOA,IAAE,CAAC;AAAE,UAAMI,MAAE,KAAK,KAAKJ,IAAE,CAAC,GAAE,IAAEE,KAAED,KAAEC,GAAC,GAAEI,MAAE,KAAK,KAAKL,KAAED,IAAE,CAAC,CAAC;AAAE,WAAO,KAAK,WAAWM,GAAC,GAAE,KAAK,oBAAoBA,GAAC,GAAE,CAACF,IAAE,CAAC,IAAEE,IAAE,CAAC,IAAEH,KAAE,KAAK,cAAaC,IAAE,CAAC,IAAEE,IAAE,CAAC,IAAEH,KAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,WAAWH,KAAE;AAAC,QAAGA,IAAE,SAAO,EAAE,QAAOA;AAAE,UAAMC,MAAED,IAAE,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAE,IAAG,CAAC,GAAEG,KAAE,KAAK,KAAKF,KAAEC,GAAC;AAAE,SAAK,WAAWC,EAAC;AAAE,UAAMC,MAAE,KAAK,WAAWD,EAAC;AAAE,WAAM,CAACF,KAAEC,KAAE,CAACD,IAAE,CAAC,KAAGG,IAAE,CAAC,IAAED,GAAE,CAAC,KAAG,KAAK,cAAaF,IAAE,CAAC,KAAGG,IAAE,CAAC,IAAED,GAAE,CAAC,KAAG,KAAK,YAAY,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcH,KAAE;AAAC,UAAMC,MAAED,IAAE,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAE,IAAG,CAAC;AAAE,QAAIG;AAAE,QAAGH,IAAE,UAAQ,EAAE,CAAAG,KAAEH,IAAEA,IAAE,SAAO,CAAC;AAAA,SAAM;AAAC,YAAMA,MAAE,KAAK,KAAKC,KAAEC,GAAC;AAAE,WAAK,WAAWF,GAAC;AAAE,YAAMI,MAAE,KAAK,WAAWJ,GAAC;AAAE,MAAAG,KAAE,CAACF,IAAE,CAAC,KAAGG,IAAE,CAAC,IAAEJ,IAAE,CAAC,KAAG,KAAK,cAAaC,IAAE,CAAC,KAAGG,IAAE,CAAC,IAAEJ,IAAE,CAAC,KAAG,KAAK,YAAY;AAAA,IAAC;AAAC,WAAM,CAACE,KAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaH,KAAE;AAAC,QAAG,CAACA,IAAE,QAAO;AAAK,UAAMC,MAAE,CAAC;AAAE,eAAUC,OAAKF,KAAE;AAAC,UAAG,CAACE,OAAG,MAAIA,IAAE,OAAO;AAAS,YAAMF,MAAEE,IAAE;AAAO,UAAIC,KAAED,IAAE,CAAC;AAAE,cAAO,KAAK,OAAM;AAAA,QAAC,KAAK,EAAE,+BAA8B;AAAC,gBAAMF,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,CAAC,GAAEM,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAKF,GAAC,GAAEE,IAAE,KAAK,KAAK,KAAKH,IAAEH,GAAC,CAAC,GAAEC,IAAE,KAAKK,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,sBAAqB;AAAC,gBAAMN,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE;AAAE,UAAAD,IAAE,KAAK,CAACD,KAAEG,EAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,8BAA6B;AAAC,gBAAMH,MAAE,KAAK,KAAKE,KAAE,IAAG,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,UAAQ,KAAK,GAAEM,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,KAAK,KAAKN,KAAEI,GAAC,CAAC,GAAEE,IAAE,KAAKH,EAAC,GAAEF,IAAE,KAAKK,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,wBAAuB;AAAC,gBAAMN,MAAE,KAAK,KAAKE,KAAE,IAAG,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,UAAQ,KAAK,GAAEM,MAAE,KAAK,KAAKF,KAAEJ,GAAC;AAAE,cAAIS;AAAE,UAAAA,KAAE,KAAK,cAAcH,KAAE,KAAK,KAAKH,IAAEH,GAAC,CAAC,IAAE,IAAE,KAAK,WAAWS,EAAC,IAAE,KAAK,UAAUH,GAAC;AAAE,gBAAMD,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACL,IAAE,CAAC,KAAGS,GAAE,CAAC,IAAEH,IAAE,CAAC,KAAG,GAAEN,IAAE,CAAC,KAAGS,GAAE,CAAC,IAAEH,IAAE,CAAC,KAAG,CAAC,CAAC,GAAED,IAAE,KAAKL,GAAC,GAAEK,IAAE,KAAKD,GAAC,GAAEC,IAAE,KAAK,CAACD,IAAE,CAAC,KAAGK,GAAE,CAAC,IAAEH,IAAE,CAAC,KAAG,GAAEF,IAAE,CAAC,KAAGK,GAAE,CAAC,IAAEH,IAAE,CAAC,KAAG,CAAC,CAAC,GAAEL,IAAE,KAAKI,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,qBAAoB;AAAC,gBAAML,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,CAAC,GAAEM,MAAE,KAAK,KAAKH,IAAEH,GAAC,GAAES,KAAE,KAAK,KAAKH,KAAEF,GAAC;AAAE,eAAK,WAAWK,EAAC;AAAE,gBAAMJ,MAAE,KAAK,cAAcI,IAAE,KAAK,KAAKN,IAAEC,GAAC,CAAC;AAAE,eAAK,mBAAmBK,EAAC;AAAE,gBAAMC,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAKP,EAAC,GAAEO,IAAE,KAAK,CAACN,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,KAAED,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,GAAC,CAAC,GAAEJ,IAAE,KAAKS,GAAC;AAAE,gBAAMC,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACP,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,KAAED,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,GAAC,CAAC,GAAEM,IAAE,KAAKX,GAAC,GAAEC,IAAE,KAAKU,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,wBAAuB;AAAC,gBAAMX,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,CAAC,GAAEM,MAAE,KAAK,KAAKH,IAAEH,GAAC,GAAES,KAAE,KAAK,KAAKH,KAAEF,GAAC;AAAE,eAAK,WAAWK,EAAC;AAAE,gBAAMJ,MAAE,KAAK,cAAcI,IAAE,KAAK,KAAKN,IAAEC,GAAC,CAAC;AAAE,eAAK,mBAAmBK,EAAC;AAAE,gBAAMC,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACN,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,KAAED,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,GAAC,CAAC,GAAEK,IAAE,KAAK,CAACN,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,KAAED,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,GAAC,CAAC,GAAEJ,IAAE,KAAKS,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,qBAAoB;AAAC,gBAAMV,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,CAAC,GAAEM,MAAE,KAAK,KAAKH,IAAEH,GAAC,GAAES,KAAE,KAAK,KAAKH,KAAEF,GAAC;AAAE,eAAK,WAAWK,EAAC;AAAE,gBAAMJ,MAAE,KAAK,cAAcI,IAAE,KAAK,KAAKN,IAAEC,GAAC,CAAC;AAAE,eAAK,mBAAmBK,EAAC;AAAE,gBAAMC,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACN,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,MAAE,KAAGD,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,MAAE,GAAE,CAAC,GAAEK,IAAE,KAAK,CAACJ,IAAE,CAAC,IAAE,OAAIH,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAE,OAAIH,GAAE,CAAC,IAAEG,IAAE,CAAC,EAAE,CAAC,GAAEL,IAAE,KAAKS,GAAC,GAAET,IAAE,KAAK,CAACG,KAAEE,GAAC,CAAC;AAAE,gBAAMK,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACP,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,MAAE,KAAGD,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEJ,MAAE,GAAE,CAAC,GAAEM,IAAE,KAAK,CAACL,IAAE,CAAC,IAAE,OAAIN,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAE,OAAIN,IAAE,CAAC,IAAEM,IAAE,CAAC,EAAE,CAAC,GAAEL,IAAE,KAAKU,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,wBAAuB;AAAC,gBAAMX,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,CAAC,GAAEM,MAAE,KAAK,KAAKH,IAAEH,GAAC;AAAE,cAAIS,KAAE,KAAK,KAAKT,KAAEG,EAAC;AAAE,gBAAME,MAAE,KAAK,IAAI,KAAK,KAAG,EAAE,GAAEK,MAAE,KAAK,IAAI,KAAK,KAAG,EAAE,GAAEC,MAAE,KAAK,MAAM,IAAEN,OAAG,CAAC,GAAEO,MAAE,KAAK,MAAM,IAAEP,OAAG,CAAC,GAAEQ,MAAE,CAAC;AAAE,cAAIC;AAAE,eAAK,cAAcL,IAAE,KAAK,KAAKL,KAAED,EAAC,CAAC,IAAE,KAAGU,IAAE,KAAKV,EAAC,GAAEM,KAAE,KAAK,KAAKN,IAAEG,GAAC,GAAEQ,KAAEd,QAAIa,IAAE,KAAKb,GAAC,GAAES,KAAE,KAAK,KAAKT,KAAEM,GAAC,GAAEQ,KAAEX,KAAG,KAAK,cAAcM,IAAEE,KAAEC,GAAC,GAAEH,GAAE,CAAC,KAAGE,KAAEF,GAAE,CAAC,KAAGE;AAAE,mBAAQV,MAAE,GAAEA,OAAG,IAAGA,MAAI,CAAAY,IAAE,KAAK,KAAK,KAAKP,KAAEG,EAAC,CAAC,GAAE,KAAK,cAAcA,IAAEJ,KAAEK,GAAC;AAAE,UAAAG,IAAE,KAAKC,EAAC,GAAEb,IAAE,KAAKY,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,yBAAwB;AAAC,gBAAMb,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,GAAE,EAAE;AAAE,cAAIM,MAAE,KAAK,KAAKH,IAAEH,GAAC;AAAE,eAAK,WAAWM,GAAC;AAAE,gBAAMG,KAAE,KAAK,cAAcH,KAAE,KAAK,KAAKF,KAAEJ,GAAC,CAAC,IAAE;AAAE,eAAK,mBAAmBM,GAAC;AAAE,gBAAMD,MAAE,CAACL,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,IAAET,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,EAAC;AAAE,UAAAH,MAAE,KAAK,KAAKN,KAAEK,GAAC;AAAE,gBAAMK,MAAE,KAAK,IAAI,KAAK,KAAG,EAAE;AAAE,cAAIC,MAAE,KAAK,IAAI,KAAK,KAAG,EAAE;AAAE,UAAAF,KAAE,MAAIE,MAAE,CAACA;AAAG,gBAAMC,MAAE,CAACZ,GAAC;AAAE,mBAAQC,MAAE,GAAEA,OAAG,IAAGA,MAAI,MAAK,cAAcK,KAAEI,KAAEC,GAAC,GAAEC,IAAE,KAAK,KAAK,KAAKP,KAAEC,GAAC,CAAC;AAAE,UAAAL,IAAE,KAAKW,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,oBAAmB;AAAC,gBAAMR,MAAE,KAAK,KAAKF,KAAE,GAAE,EAAE,GAAEI,MAAE,KAAK,KAAKJ,KAAEE,KAAE,GAAE,EAAE;AAAE,cAAIK;AAAE,cAAGT,OAAG,EAAE,CAAAS,KAAEP,IAAE,CAAC;AAAA,eAAM;AAAC,kBAAMF,MAAE,KAAK,KAAKG,IAAEC,GAAC;AAAE,YAAAK,KAAE,KAAK,KAAKH,KAAEN,GAAC;AAAA,UAAC;AAAC,gBAAMK,MAAE,KAAK,MAAMD,KAAEE,GAAC,IAAE,IAAE,MAAII,MAAE,KAAK,KAAKN,KAAED,EAAC;AAAE,eAAK,WAAWO,KAAEL,GAAC;AAAE,gBAAMM,MAAE,KAAK,KAAKL,KAAEG,EAAC;AAAE,eAAK,WAAWE,KAAEN,GAAC;AAAE,gBAAMO,MAAE,CAACH,IAAEH,GAAC;AAAE,UAAAL,IAAE,KAAKW,GAAC;AAAE,gBAAMC,MAAE,CAAC,KAAK,OAAOP,GAAC,CAAC;AAAE,eAAK,YAAYO,KAAEP,KAAE,KAAK,KAAKA,KAAEK,GAAC,GAAE,KAAK,KAAKP,KAAEM,GAAC,GAAEN,KAAE,CAAC,GAAES,IAAE,KAAKV,EAAC,GAAEF,IAAE,KAAKY,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,YAAW;AAAC,gBAAMb,MAAE,KAAK,KAAKE,KAAE,IAAG,CAAC,GAAEE,MAAE,KAAK,KAAKJ,KAAEG,EAAC,GAAEG,MAAE,KAAK,IAAI,KAAK,KAAG,EAAE,GAAEG,KAAE,CAAC,KAAK,IAAI,KAAK,KAAG,EAAE,GAAEJ,MAAE,CAACL,GAAC;AAAE,mBAAQC,MAAE,GAAEA,OAAG,IAAGA,MAAI,MAAK,cAAcG,KAAEE,KAAEG,EAAC,GAAEJ,IAAE,KAAK,KAAK,KAAKF,IAAEC,GAAC,CAAC;AAAE,UAAAH,IAAE,KAAKI,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,wBAAuB;AAAC,gBAAMD,MAAE,KAAK,KAAKF,KAAE,GAAE,EAAE;AAAE,cAAII,KAAEG;AAAE,cAAGT,OAAG,EAAE,CAAAM,MAAEJ,IAAE,CAAC;AAAA,eAAM;AAAC,kBAAMF,MAAE,KAAK,KAAKI,KAAED,EAAC,GAAEF,MAAE,KAAK,UAAUD,GAAC;AAAE,YAAAM,MAAE,CAACH,GAAE,CAAC,IAAEF,IAAE,CAAC,IAAE,OAAID,IAAE,CAAC,GAAEG,GAAE,CAAC,IAAEF,IAAE,CAAC,IAAE,OAAID,IAAE,CAAC,CAAC;AAAA,UAAC;AAAC,cAAGA,OAAG,EAAE,CAAAS,KAAEP,IAAE,CAAC;AAAA,eAAM;AAAC,kBAAMF,MAAE,KAAK,KAAKG,IAAEC,GAAC,GAAEH,MAAE,KAAK,KAAKE,IAAEC,GAAC;AAAE,iBAAK,WAAWH,GAAC,GAAE,KAAK,mBAAmBA,GAAC;AAAE,kBAAMC,MAAE,KAAK,cAAcD,KAAE,KAAK,KAAKK,KAAEN,GAAC,CAAC;AAAE,iBAAK,oBAAoBC,GAAC,GAAEQ,KAAE,CAACH,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAEC,MAAE,GAAEI,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAEC,MAAE,CAAC;AAAA,UAAC;AAAC,gBAAMG,MAAE,KAAK,KAAKD,KAAED,EAAC;AAAE,cAAIO,KAAEC;AAAE,UAAAD,MAAE,KAAK,cAAcL,KAAE,KAAK,KAAKC,KAAEH,EAAC,CAAC,IAAE,IAAE,KAAK,WAAWE,GAAC,IAAE,KAAK,UAAUA,GAAC,GAAEM,MAAE,CAAC,GAAEA,IAAE,KAAKL,GAAC,GAAEK,IAAE,KAAKR,EAAC,GAAEQ,IAAE,KAAK,CAACR,GAAE,CAAC,KAAGO,IAAE,CAAC,IAAEL,IAAE,CAAC,KAAG,GAAEF,GAAE,CAAC,KAAGO,IAAE,CAAC,IAAEL,IAAE,CAAC,KAAG,CAAC,CAAC,GAAEJ,IAAE,KAAKU,GAAC,GAAED,MAAE,KAAK,cAAcL,KAAE,KAAK,KAAKI,IAAEL,GAAC,CAAC,IAAE,IAAE,KAAK,WAAWM,GAAC,IAAE,KAAK,UAAUL,GAAC,GAAEM,MAAE,CAAC,GAAEA,IAAE,KAAK,CAACP,IAAE,CAAC,KAAGM,IAAE,CAAC,IAAEL,IAAE,CAAC,KAAG,GAAED,IAAE,CAAC,KAAGM,IAAE,CAAC,IAAEL,IAAE,CAAC,KAAG,CAAC,CAAC,GAAEM,IAAE,KAAKP,GAAC,GAAEO,IAAE,KAAKF,EAAC,GAAER,IAAE,KAAKU,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,0BAAyB;AAAC,gBAAMP,MAAE,KAAK,KAAKF,KAAE,GAAE,CAAC,GAAEI,MAAE,KAAK,KAAKJ,KAAEE,KAAE,GAAE,CAAC;AAAE,cAAIK;AAAE,cAAGT,OAAG,EAAE,CAAAS,KAAEP,IAAE,CAAC;AAAA,eAAM;AAAC,kBAAMF,MAAE,KAAK,KAAKI,KAAED,EAAC;AAAE,YAAAM,KAAE,KAAK,KAAKH,KAAEN,GAAC;AAAA,UAAC;AAAC,eAAK,gBAAgBC,KAAEE,IAAEC,KAAE,KAAK,KAAKE,KAAEG,EAAC,CAAC,GAAE,KAAK,gBAAgBR,KAAEK,KAAEG,IAAE,KAAK,KAAKN,IAAEC,GAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,kBAAiB;AAAC,gBAAMA,MAAE,KAAK,KAAKF,KAAE,GAAE,CAAC,GAAEI,MAAE,KAAK,KAAKJ,KAAEE,KAAE,GAAE,CAAC;AAAE,cAAIK;AAAE,cAAGT,OAAG,EAAE,CAAAS,KAAEP,IAAE,CAAC;AAAA,eAAM;AAAC,kBAAMF,MAAE,KAAK,KAAKI,KAAED,EAAC;AAAE,YAAAM,KAAE,KAAK,KAAKH,KAAEN,GAAC;AAAA,UAAC;AAAC,gBAAMK,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,KAAK,KAAKF,IAAEG,GAAC,CAAC,GAAED,IAAE,KAAK,KAAK,KAAKD,KAAEK,EAAC,CAAC,GAAER,IAAE,KAAKI,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,SAAQ;AAAC,gBAAMD,MAAE,KAAK,KAAKF,KAAE,IAAG,EAAE;AAAE,cAAII;AAAE,cAAGN,OAAG,EAAE,CAAAM,MAAEJ,IAAE,CAAC;AAAA,eAAM;AAAC,kBAAMF,MAAE,KAAK,KAAKI,KAAED,EAAC;AAAE,iBAAK,mBAAmBH,GAAC,GAAEM,MAAE,KAAK,KAAKH,IAAEH,GAAC;AAAA,UAAC;AAAC,UAAAC,IAAE,KAAK,CAACG,KAAE,KAAK,YAAYD,EAAC,GAAEG,GAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,sBAAqB;AAAC,gBAAMN,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,EAAE;AAAE,cAAIM,MAAE,KAAK,KAAKN,KAAEG,EAAC;AAAE,gBAAMM,KAAE,KAAK,MAAMH,GAAC;AAAE,UAAAA,IAAE,CAAC,KAAGG,IAAEH,IAAE,CAAC,KAAGG;AAAE,gBAAMJ,MAAE,KAAK,cAAcC,KAAE,KAAK,KAAKF,KAAED,EAAC,CAAC;AAAE,cAAIO,MAAE,KAAK,YAAYJ,KAAE,KAAK,KAAKF,KAAED,EAAC,CAAC;AAAE,UAAAO,MAAE,OAAID,KAAEC,MAAE,OAAID,KAAEC,MAAE,OAAID,OAAIC,MAAE,OAAID;AAAG,gBAAME,MAAE,CAACR,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEI,KAAEP,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEI,GAAC;AAAE,eAAK,mBAAmBJ,GAAC;AAAE,cAAIM,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACD,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAED,KAAEM,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAED,GAAC,CAAC,GAAEO,IAAE,KAAK,CAACD,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAED,KAAEM,IAAE,CAAC,IAAEL,IAAE,CAAC,IAAED,GAAC,CAAC,GAAEJ,IAAE,KAAKW,GAAC;AAAE,gBAAMC,MAAE,CAACb,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAED,KAAEL,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAED,GAAC;AAAE,UAAAC,MAAE,KAAK,KAAKN,KAAEa,GAAC;AAAE,gBAAMC,KAAE,KAAK,IAAI,KAAK,KAAG,EAAE;AAAE,cAAIC,MAAE,KAAK,IAAI,KAAK,KAAG,EAAE;AAAE,UAAAV,MAAE,MAAIU,MAAE,CAACA,MAAGH,MAAE,CAACT,IAAEH,GAAC;AAAE,mBAAQC,MAAE,GAAEA,OAAG,GAAEA,MAAI,MAAK,cAAcK,KAAEQ,IAAEC,GAAC,GAAEH,IAAE,KAAK,KAAK,KAAKC,KAAEP,GAAC,CAAC;AAAE,UAAAL,IAAE,KAAKW,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,kBAAiB;AAAC,gBAAMZ,MAAE,KAAK,KAAKE,KAAE,GAAE,CAAC,GAAEE,MAAE,KAAK,KAAKD,IAAEH,GAAC,GAAEM,MAAE,KAAK,KAAKN,KAAEI,GAAC,GAAEK,KAAE,KAAK,IAAI,KAAK,KAAG,EAAE,GAAEJ,MAAE,KAAK,IAAI,KAAK,KAAG,EAAE,GAAEK,MAAE,CAACP,IAAEH,GAAC;AAAE,mBAAQC,MAAE,GAAEA,OAAG,IAAGA,MAAI,MAAK,cAAcK,KAAEG,IAAEJ,GAAC,GAAEK,IAAE,KAAK,KAAK,KAAKN,KAAEE,GAAC,CAAC;AAAE,UAAAL,IAAE,KAAKS,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,wBAAuB;AAAC,gBAAMV,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEI,MAAE,KAAK,KAAKJ,KAAEF,KAAE,GAAE,EAAE,GAAES,KAAE,KAAK,KAAKN,IAAEH,GAAC,GAAEK,MAAE,KAAK,KAAKC,KAAEN,GAAC;AAAE,eAAK,WAAWK,GAAC;AAAE,gBAAMK,MAAE,KAAK,IAAI,KAAK,cAAcL,KAAE,KAAK,KAAKI,IAAET,GAAC,CAAC,CAAC,IAAE,GAAEW,MAAE,KAAK,MAAMX,KAAEM,GAAC,GAAEM,MAAE,CAACZ,KAAEG,EAAC;AAAE,UAAAS,IAAE,KAAK,CAACT,GAAE,CAAC,IAAEE,IAAE,CAAC,IAAEM,MAAE,KAAGR,GAAE,CAAC,IAAEE,IAAE,CAAC,IAAEM,MAAE,GAAE,CAAC,GAAEV,IAAE,KAAKW,GAAC;AAAE,gBAAMC,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACJ,GAAE,CAAC,IAAEJ,IAAE,CAAC,IAAEK,KAAED,GAAE,CAAC,IAAEJ,IAAE,CAAC,IAAEK,GAAC,CAAC,GAAEG,IAAE,KAAK,CAACJ,GAAE,CAAC,IAAEJ,IAAE,CAAC,IAAEM,MAAE,OAAKF,GAAE,CAAC,IAAEJ,IAAE,CAAC,IAAEM,MAAE,KAAI,CAAC,GAAEH,GAAEK,IAAEA,IAAE,SAAO,CAAC,GAAE,CAAC,GAAEA,IAAE,KAAK,CAACJ,GAAE,CAAC,IAAEJ,IAAE,CAAC,IAAEM,MAAE,MAAIF,GAAE,CAAC,IAAEJ,IAAE,CAAC,IAAEM,MAAE,IAAG,CAAC,GAAEV,IAAE,KAAKY,GAAC;AAAE,gBAAMC,KAAE,CAACd,KAAEM,GAAC;AAAE,UAAAL,IAAE,KAAKa,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,mBAAkB;AAAC,gBAAMd,MAAE,KAAK,KAAKE,KAAE,GAAE,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,EAAE,GAAEM,MAAE,KAAK,KAAKF,KAAEJ,GAAC;AAAE,eAAK,WAAWM,GAAC;AAAE,gBAAMG,KAAE,KAAK,cAAcH,KAAE,KAAK,KAAKF,KAAED,EAAC,CAAC;AAAE,eAAK,mBAAmBG,GAAC,GAAE,KAAK,gBAAgBL,KAAEE,IAAEH,KAAEI,GAAC,GAAE,KAAK,gBAAgBH,KAAE,KAAK,KAAKE,IAAE,GAAEG,KAAEG,EAAC,GAAE,KAAK,KAAKT,KAAE,GAAEM,KAAEG,EAAC,GAAE,KAAK,KAAKN,IAAEH,GAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,UAAS;AAAC,gBAAMA,MAAE,KAAK,KAAKE,KAAE,GAAE,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,EAAE,GAAEM,MAAE,KAAK,KAAKN,KAAEG,EAAC;AAAE,eAAK,WAAWG,GAAC;AAAE,gBAAMG,KAAE,KAAK,UAAUH,GAAC,GAAED,MAAE,KAAK,cAAcC,KAAE,KAAK,KAAKF,KAAED,EAAC,CAAC;AAAE,cAAIO,MAAE,CAACP,IAAEH,GAAC;AAAE,UAAAC,IAAE,KAAKS,GAAC,GAAEA,MAAE,CAAC,GAAEA,IAAE,KAAK,CAACP,GAAE,CAAC,IAAEM,GAAE,CAAC,IAAEJ,KAAEF,GAAE,CAAC,IAAEM,GAAE,CAAC,IAAEJ,GAAC,CAAC,GAAEK,IAAE,KAAK,CAACV,IAAE,CAAC,IAAES,GAAE,CAAC,IAAEJ,KAAEL,IAAE,CAAC,IAAES,GAAE,CAAC,IAAEJ,GAAC,CAAC,GAAEJ,IAAE,KAAKS,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,6BAA4B;AAAC,gBAAMV,MAAE,KAAK,KAAKE,KAAE,GAAE,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,EAAE,GAAEM,MAAE,KAAK,KAAKH,IAAEH,GAAC,GAAES,KAAE,KAAK,KAAKT,KAAEG,EAAC;AAAE,eAAK,WAAWM,EAAC;AAAE,gBAAMJ,MAAE,KAAK,cAAcI,IAAE,KAAK,KAAKL,KAAED,EAAC,CAAC;AAAE,eAAK,mBAAmBM,EAAC;AAAE,gBAAMC,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACJ,IAAE,CAAC,IAAEG,GAAE,CAAC,IAAEJ,MAAE,MAAIC,IAAE,CAAC,IAAEG,GAAE,CAAC,IAAEJ,MAAE,IAAG,CAAC,GAAEK,IAAE,KAAK,CAACJ,IAAE,CAAC,IAAEG,GAAE,CAAC,IAAEJ,MAAE,MAAKC,IAAE,CAAC,IAAEG,GAAE,CAAC,IAAEJ,MAAE,IAAI,CAAC,GAAEJ,IAAE,KAAKS,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,gBAAe;AAAC,gBAAMV,MAAE,KAAK,KAAKE,KAAE,GAAE,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,EAAE,GAAEM,MAAE,KAAK,KAAKN,KAAEG,EAAC;AAAE,eAAK,WAAWG,GAAC;AAAE,gBAAMG,KAAE,KAAK,cAAcH,KAAE,KAAK,KAAKF,KAAED,EAAC,CAAC;AAAE,eAAK,mBAAmBG,GAAC;AAAE,gBAAMD,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACF,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEG,IAAEN,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEG,EAAC,CAAC,GAAEJ,IAAE,KAAK,CAACL,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,IAAET,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,EAAC,CAAC,GAAER,IAAE,KAAKI,GAAC;AAAE,gBAAMK,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACP,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEG,IAAEN,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEG,EAAC,CAAC,GAAEC,IAAE,KAAK,CAACV,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,IAAET,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,EAAC,CAAC,GAAER,IAAE,KAAKS,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,gBAAe;AAAC,gBAAMV,MAAE,KAAK,KAAKE,KAAE,GAAE,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,EAAE,GAAEM,MAAE,KAAK,KAAKN,KAAEG,EAAC;AAAE,eAAK,WAAWG,GAAC;AAAE,gBAAMG,KAAE,KAAK,cAAcH,KAAE,KAAK,KAAKF,KAAED,EAAC,CAAC;AAAE,eAAK,mBAAmBG,GAAC;AAAE,gBAAMD,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACF,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEG,IAAEN,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEG,EAAC,CAAC,GAAEJ,IAAE,KAAK,CAACL,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,IAAET,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,EAAC,CAAC,GAAER,IAAE,KAAKI,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,YAAW;AAAC,gBAAML,MAAE,KAAK,KAAKE,KAAE,GAAE,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,EAAE,GAAEM,MAAE,KAAK,KAAKN,KAAEG,EAAC;AAAE,eAAK,WAAWG,GAAC;AAAE,gBAAMG,KAAE,KAAK,cAAcH,KAAE,KAAK,KAAKF,KAAED,EAAC,CAAC;AAAE,eAAK,mBAAmBG,GAAC;AAAE,gBAAMD,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACF,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEG,IAAEN,GAAE,CAAC,IAAEG,IAAE,CAAC,IAAEG,EAAC,CAAC,GAAEJ,IAAE,KAAK,CAACL,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,IAAET,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEG,EAAC,CAAC,GAAER,IAAE,KAAKI,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,eAAc;AAAC,cAAIC,MAAE,KAAK,KAAKJ,KAAE,GAAE,CAAC;AAAE,gBAAMO,KAAE,KAAK,KAAKP,KAAEI,KAAE,KAAG,EAAE;AAAE,cAAID,KAAEK;AAAE,cAAGV,OAAG,EAAE,CAAAK,MAAEH,IAAE,CAAC,GAAEQ,MAAE,KAAK,cAAc,KAAK,KAAKL,KAAEC,GAAC,GAAE,KAAK,KAAKG,IAAEH,GAAC,CAAC,IAAE;AAAA,eAAM;AAAC,YAAAD,MAAEC,KAAEI,MAAE,KAAK,cAAc,KAAK,KAAKL,KAAEF,EAAC,GAAE,KAAK,KAAKM,IAAEN,EAAC,CAAC,IAAE;AAAE,kBAAMH,MAAE,KAAG,KAAK,oBAAmBC,MAAE,KAAK,KAAKI,KAAEF,EAAC;AAAE,iBAAK,WAAWF,KAAED,GAAC;AAAE,kBAAME,MAAE,KAAK,KAAK,CAAC,IAAE;AAAE,iBAAK,cAAcD,KAAEC,KAAEQ,MAAER,MAAE,CAACA,GAAC,GAAEI,MAAE,KAAK,KAAKH,IAAEF,GAAC;AAAA,UAAC;AAAC,gBAAMU,MAAE,KAAK,KAAKL,KAAEH,EAAC,GAAES,MAAE,KAAK,IAAI,KAAK,KAAG,EAAE,GAAEC,MAAE,KAAK,IAAI,KAAK,KAAG,EAAE,GAAEC,KAAE,CAACR,GAAC;AAAE,mBAAQN,MAAE,GAAEA,OAAG,IAAGA,MAAI,MAAK,cAAcW,KAAEC,KAAEC,GAAC,GAAEC,GAAE,KAAK,KAAK,KAAKX,IAAEQ,GAAC,CAAC;AAAE,eAAK,aAAaG,IAAER,KAAED,KAAEI,IAAEC,GAAC,GAAEF,GAAEM,GAAEA,GAAE,SAAO,CAAC,GAAE,CAAC,GAAEb,IAAE,KAAKa,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,WAAU;AAAC,cAAIV,KAAEE,KAAEG,KAAE,KAAK,KAAKP,KAAE,IAAG,CAAC;AAAE,cAAGE,MAAEJ,OAAG,IAAEE,IAAE,CAAC,IAAE,KAAK,KAAKC,IAAE,KAAK,KAAKA,IAAEM,EAAC,CAAC,GAAET,OAAG,EAAE,CAAAM,MAAEJ,IAAE,CAAC;AAAA,eAAM;AAAC,kBAAMF,MAAEG;AAAE,YAAAA,KAAEM,IAAEH,MAAEF;AAAE,kBAAMH,MAAE,KAAK,MAAME,IAAEH,GAAC,GAAEE,MAAE,KAAK,MAAMI,KAAEN,GAAC;AAAE,gBAAIK,MAAE,KAAG,KAAK;AAAmB,kBAAGJ,MAAEI,QAAIA,MAAE,MAAGJ,MAAG,MAAGC,MAAEG,QAAIA,MAAE,MAAGH,MAAGO,KAAE,KAAK,KAAKN,IAAEE,MAAEJ,KAAED,MAAGC,MAAEI,OAAGJ,GAAC,GAAEG,MAAE,KAAK,KAAKE,KAAED,MAAEH,KAAEF,MAAGE,MAAEG,OAAGH,GAAC;AAAA,UAAC;AAAC,gBAAMG,MAAE,KAAK,KAAKF,IAAEM,EAAC,GAAEC,MAAE,KAAK,KAAKJ,KAAEF,GAAC,GAAEO,MAAE,KAAK,MAAMR,IAAEM,EAAC,GAAEG,MAAE,KAAK,MAAMR,KAAEE,GAAC;AAAE,cAAIO,MAAE,KAAK,IAAIF,KAAEC,GAAC,IAAE;AAAE,UAAAC,MAAE,KAAK,IAAIA,KAAE,KAAG,KAAK,kBAAkB;AAAE,gBAAMC,KAAE,KAAK,IAAI,KAAK,KAAG,CAAC;AAAE,cAAIC,MAAE,KAAK,KAAKZ,IAAEM,EAAC;AAAE,eAAK,WAAWM,KAAEF,GAAC,GAAE,KAAK,cAAcE,KAAE,KAAK,KAAKT,KAAEG,EAAC,CAAC,IAAE,IAAE,KAAK,cAAcM,KAAED,IAAE,CAACA,EAAC,IAAE,KAAK,cAAcC,KAAED,IAAEA,EAAC;AAAE,cAAIE,KAAE,CAAC;AAAE,UAAAA,GAAE,KAAKP,EAAC,GAAEO,GAAE,KAAK,KAAK,KAAKX,KAAEU,GAAC,CAAC,GAAEC,GAAE,KAAK,KAAK,KAAKX,KAAEU,GAAC,CAAC,GAAEC,GAAE,KAAKb,EAAC,GAAEF,IAAE,KAAKe,EAAC,GAAED,MAAE,KAAK,KAAKT,KAAEF,GAAC,GAAE,KAAK,WAAWW,KAAEF,GAAC,GAAE,KAAK,cAAcE,KAAE,KAAK,KAAKZ,IAAEC,GAAC,CAAC,IAAE,IAAE,KAAK,cAAcW,KAAED,IAAEA,EAAC,IAAE,KAAK,cAAcC,KAAED,IAAE,CAACA,EAAC,GAAEE,KAAE,CAAC,GAAEA,GAAE,KAAKZ,GAAC,GAAEY,GAAE,KAAK,KAAK,KAAKN,KAAEK,GAAC,CAAC,GAAEC,GAAE,KAAK,KAAK,KAAKN,KAAEK,GAAC,CAAC,GAAEC,GAAE,KAAKV,GAAC,GAAEL,IAAE,KAAKe,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,qBAAoB;AAAC,gBAAMhB,MAAE,KAAK,KAAKE,KAAE,IAAG,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,UAAQ,KAAK,GAAEM,MAAE,KAAK,KAAKF,KAAEJ,GAAC;AAAE,eAAK,cAAcM,KAAE,KAAK,KAAKH,IAAEH,GAAC,CAAC,IAAE,IAAE,KAAK,oBAAoBM,GAAC,IAAE,KAAK,mBAAmBA,GAAC;AAAE,gBAAMG,KAAE,CAACH,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,CAAC,GAAED,MAAE,KAAK,KAAK,KAAK,KAAKL,KAAEI,GAAC,GAAEK,EAAC;AAAE,UAAAR,IAAE,KAAK,CAACI,KAAEF,EAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,0BAAyB;AAAC,gBAAMH,MAAE,KAAK,WAAWE,GAAC,GAAEC,KAAE,CAAC;AAAE,cAAIC,MAAEJ,IAAE,SAAO;AAAE,iBAAKI,QAAK,CAAAD,GAAE,KAAKH,IAAEI,GAAC,CAAC;AAAE,UAAAH,IAAE,KAAKE,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,kBAAiB;AAAC,gBAAMH,MAAE,KAAK,WAAWE,GAAC,GAAEC,KAAE,CAAC;AAAE,eAAK,UAAUA,IAAEH,KAAE,KAAE,GAAEC,IAAE,KAAKE,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,cAAa;AAAC,gBAAMH,MAAE,KAAK,WAAWE,GAAC,GAAEC,KAAE,CAAC;AAAE,eAAK,UAAUA,IAAEH,KAAE,IAAE,GAAEC,IAAE,KAAKE,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,cAAa;AAAC,gBAAK,CAACH,KAAEI,GAAC,IAAE,KAAK,cAAcF,GAAC,GAAEI,MAAE,KAAG,KAAK,oBAAmBG,KAAE,KAAK,KAAKN,IAAEH,GAAC;AAAE,eAAK,WAAWS,EAAC;AAAE,gBAAMJ,MAAE,KAAK,cAAcI,IAAE,KAAK,KAAKL,KAAEJ,GAAC,CAAC,GAAEU,MAAE,KAAK,UAAUD,EAAC,GAAEE,MAAE,CAACP,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEL,MAAE,GAAED,IAAE,CAAC,IAAEM,IAAE,CAAC,IAAEL,MAAE,CAAC,GAAEO,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACR,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEH,KAAEF,IAAE,CAAC,IAAEK,GAAE,CAAC,IAAEH,GAAC,CAAC,GAAEM,IAAE,KAAKT,EAAC,GAAES,IAAE,KAAK,CAACD,IAAE,CAAC,IAAEF,GAAE,CAAC,IAAEH,KAAEK,IAAE,CAAC,IAAEF,GAAE,CAAC,IAAEH,GAAC,CAAC,GAAEL,IAAE,KAAKW,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,oBAAmB;AAAC,gBAAK,CAACZ,KAAEI,GAAC,IAAE,KAAK,cAAcF,GAAC,GAAEI,MAAE,KAAK,KAAKH,IAAEH,GAAC;AAAE,eAAK,WAAWM,GAAC;AAAE,gBAAMG,KAAE,KAAK,cAAcH,KAAE,KAAK,KAAKF,KAAEJ,GAAC,CAAC;AAAE,eAAK,mBAAmBM,GAAC;AAAE,gBAAMD,MAAE,CAACD,IAAE,CAAC,IAAEE,IAAE,CAAC,IAAEG,IAAEL,IAAE,CAAC,IAAEE,IAAE,CAAC,IAAEG,EAAC,GAAEC,MAAE,CAAC;AAAE,UAAAA,IAAE,KAAK,CAACL,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAEG,KAAE,KAAGJ,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAEG,KAAE,GAAE,CAAC,GAAEC,IAAE,KAAK,KAAK,KAAKL,KAAEF,EAAC,CAAC,GAAEO,IAAE,KAAK,CAACL,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAEG,KAAE,KAAGJ,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAEG,KAAE,GAAE,CAAC,GAAER,IAAE,KAAKS,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,qBAAoB;AAAC,gBAAK,CAACV,KAAEI,GAAC,IAAE,KAAK,cAAcF,GAAC,GAAEI,MAAE,KAAK,KAAKH,IAAEH,GAAC;AAAE,eAAK,WAAWM,GAAC;AAAE,gBAAMG,KAAE,KAAK,cAAcH,KAAE,KAAK,KAAKF,KAAEJ,GAAC,CAAC;AAAE,eAAK,mBAAmBM,GAAC;AAAE,gBAAMD,MAAE,CAACD,IAAE,CAAC,IAAEE,IAAE,CAAC,IAAEG,IAAEL,IAAE,CAAC,IAAEE,IAAE,CAAC,IAAEG,EAAC;AAAE,UAAAR,IAAE,KAAK,CAACD,KAAEK,GAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,MAAK;AAAC,gBAAML,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,CAAC,GAAEM,MAAE,KAAK,KAAKH,IAAEH,GAAC,GAAES,KAAE,KAAK,KAAKL,KAAE,GAAEE,KAAE,IAAG,GAAED,MAAE,KAAK,KAAKD,KAAE,GAAEE,KAAE,KAAI,GAAEI,MAAE,CAACP,EAAC;AAAE,eAAK,YAAYO,KAAEP,IAAEM,IAAEL,KAAE,CAAC,GAAE,KAAK,YAAYM,KAAEN,KAAEC,KAAEL,KAAE,CAAC,GAAEC,IAAE,KAAKS,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,qBAAoB;AAAC,gBAAMV,MAAE,KAAK,KAAKE,KAAE,IAAG,CAAC,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,UAAQ,KAAK,GAAEM,MAAE,KAAK,KAAKF,KAAEJ,GAAC;AAAE,eAAK,cAAcM,KAAE,KAAK,KAAKH,IAAEH,GAAC,CAAC,IAAE,IAAE,KAAK,oBAAoBM,GAAC,IAAE,KAAK,mBAAmBA,GAAC;AAAE,gBAAMG,KAAE,CAACH,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,CAAC,GAAED,MAAE,KAAK,KAAK,KAAK,KAAKL,KAAEI,GAAC,GAAEK,EAAC,GAAEC,MAAE,KAAK,KAAK,KAAK,KAAKV,KAAE,MAAII,KAAE,IAAG,GAAEK,EAAC,GAAEE,MAAE,KAAK,KAAK,KAAK,KAAKX,KAAE,MAAII,KAAE,IAAG,GAAEK,EAAC,GAAEG,MAAE,CAACZ,GAAC;AAAE,eAAK,YAAYY,KAAEZ,KAAEU,KAAEL,KAAE,CAAC,GAAE,KAAK,YAAYO,KAAEP,KAAEM,KAAEP,KAAE,CAAC,GAAEH,IAAE,KAAKW,GAAC;AAAE,mBAAQV,MAAE,GAAEA,MAAE,GAAEA,OAAI;AAAC,kBAAMF,MAAEY,IAAE,IAAEV,MAAE,CAAC,GAAEC,KAAE,CAAC,KAAK,OAAOH,GAAC,CAAC;AAAE,YAAAG,GAAE,KAAK,KAAK,KAAKH,KAAE,CAACM,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,CAAC,CAAC,CAAC,GAAEL,IAAE,KAAKE,EAAC;AAAA,UAAC;AAAC;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE,cAAa;AAAC,gBAAMH,MAAE,KAAK,KAAKE,KAAE,GAAE,EAAE,GAAEE,MAAE,KAAK,KAAKF,KAAEF,KAAE,KAAG,CAAC,GAAEM,MAAE,CAACN,GAAC;AAAE,eAAK,aAAaM,KAAEN,KAAEG,IAAEC,GAAC,GAAEH,IAAE,KAAKK,GAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAK,EAAE;AAAA,QAAa;AAAQ,UAAAL,IAAE,KAAKC,GAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAC;;;ACA11e,IAAMgB,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEF,KAAEG,KAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEJ,KAAEC,KAAEF,GAAC;AAAA,EAAC;AAAC;AAACA,GAAE,WAAS;AAAK,IAAMK,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYL,KAAED,KAAEK,KAAE;AAAC,UAAMJ,KAAE,MAAG,IAAE,GAAE,KAAK,eAAa,IAAIM,MAAE,KAAK,aAAW,WAASP,IAAE,WAASA,IAAE,WAAS,KAAGK,KAAE,KAAK,WAAS,WAASL,IAAE,SAAOA,IAAE,SAAO,KAAGK,KAAE,KAAK,cAAY,WAASL,IAAE,YAAUA,IAAE,YAAU,KAAGK,KAAE,KAAK,UAAQ,WAASL,IAAE,UAAQA,IAAE,QAAO,KAAK,YAAU,MAAI,KAAK,YAAU,IAAG,KAAK,UAAQ,MAAI,KAAK,UAAQ,IAAG,KAAK,aAAW,MAAI,KAAK,aAAW;AAAA,EAAE;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMC,MAAE,KAAK,WAAUF,MAAE,KAAK,SAAQK,MAAE,KAAK,YAAWF,MAAE,KAAK,aAAa,oBAAoBF,GAAC,GAAEG,MAAE,CAAC;AAAE,QAAG,KAAK,QAAQ,KAAG,MAAIF,OAAG,MAAIF,OAAG,MAAIK,IAAE;AAAA,aAASH,MAAEF,MAAEK,OAAGF,IAAE,CAAAC,IAAE,KAAKH,GAAC;AAAA,SAAM;AAAC,UAAIM,MAAE,KAAK,aAAa,YAAYN,KAAE,GAAEC,GAAC;AAAE,MAAAK,OAAGH,IAAE,KAAKG,GAAC,GAAEA,MAAE,KAAK,aAAa,YAAYN,KAAE,OAAIE,MAAEE,MAAG,OAAIF,MAAEE,IAAE,GAAEE,OAAGH,IAAE,KAAKG,GAAC,GAAEA,MAAE,KAAK,aAAa,YAAYN,KAAEE,MAAEH,KAAEA,GAAC,GAAEO,OAAGH,IAAE,KAAKG,GAAC;AAAA,IAAC;AAAA,aAAS,MAAIL,OAAG,MAAIF,OAAG,MAAIK,IAAE,CAAAD,IAAE,KAAKH,GAAC;AAAA,aAAUC,MAAEF,MAAEK,OAAGF,IAAE;AAAA,aAAS,MAAIE,KAAE;AAAC,YAAMA,MAAE,KAAK,aAAa,YAAYJ,KAAEC,KAAEC,MAAEH,GAAC;AAAE,MAAAK,OAAGD,IAAE,KAAKC,GAAC;AAAA,IAAC,OAAK;AAAC,UAAIE,MAAE,KAAK,aAAa,YAAYN,KAAEC,KAAE,OAAIC,MAAEE,IAAE;AAAE,MAAAE,OAAGH,IAAE,KAAKG,GAAC,GAAEA,MAAE,KAAK,aAAa,YAAYN,KAAE,OAAIE,MAAEE,MAAGF,MAAEH,GAAC,GAAEO,OAAGH,IAAE,KAAKG,GAAC;AAAA,IAAC;AAAC,WAAO,MAAIH,IAAE,SAAO,OAAK,EAAC,OAAMA,IAAC;AAAA,EAAC;AAAC;;;ACAlpC,IAAMI,KAAE;AAAK,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,UAAQ,CAAC,GAAE,KAAK,WAAS,GAAE,KAAK,YAAU,GAAE,KAAK,UAAQ,GAAE,KAAK,gBAAc;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,MAAI,KAAK,QAAQ;AAAA,EAAM;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAM;AAAA,EAAC,KAAKC,KAAEC,KAAEC,MAAE,MAAG;AAAC,QAAG,KAAK,UAAU,GAAE,CAACF,OAAG,MAAIA,IAAE,OAAO,QAAM;AAAG,aAAQD,MAAE,GAAEA,MAAEC,IAAE,QAAOD,OAAI;AAAC,UAAIE,MAAE,KAAK,IAAID,IAAED,GAAC,CAAC;AAAE,MAAAG,OAAGD,MAAEH,OAAIG,MAAEH,KAAG,KAAK,QAAQ,KAAKG,GAAC,GAAE,KAAK,WAASA;AAAA,IAAC;AAAC,WAAOA,OAAG,IAAED,IAAE,WAAS,KAAK,WAAS,IAAG,MAAI,KAAK,YAAU,KAAK,YAAU,KAAK,WAAS,GAAE,KAAK,gBAAc,IAAG;AAAA,EAAG;AAAA,EAAC,MAAMA,KAAE;AAAC,UAAMC,MAAE,KAAK,UAAQ,KAAK,QAAQ,SAAO;AAAE,aAAQC,MAAE,GAAEA,MAAED,KAAE,EAAEC,IAAE,MAAK,QAAQA,GAAC,KAAGF;AAAE,SAAK,WAASA,KAAE,KAAK,YAAUA,KAAE,KAAK,aAAWA;AAAA,EAAC;AAAA,EAAC,SAASA,KAAE;AAAC,SAAK,WAASA,KAAE,KAAK,QAAQ,KAAKA,GAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,QAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,QAAQ,KAAK,QAAQ,SAAO,CAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,iBAAgB,KAAK,kBAAgB,KAAK,QAAQ,WAAS,KAAK,gBAAc,IAAG,KAAK,QAAQ,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,gBAAc;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,YAAW;AAAC,SAAK,WAAS,KAAK,YAAU,KAAK,UAAQ,GAAE,KAAK,gBAAc,IAAG,KAAK,QAAQ,SAAO;AAAA,EAAC;AAAC;AAAC,IAAMG,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,KAAG,MAAK,KAAK,KAAG,GAAE,KAAK,KAAG;AAAA,EAAC;AAAC;AAAC,IAAIC;AAAE,CAAC,SAASJ,KAAE;AAAC,EAAAA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,MAAI,CAAC,IAAE,OAAMA,IAAEA,IAAE,WAAS,CAAC,IAAE;AAAU,EAAEI,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,UAAQ,IAAG,KAAK,gBAAc,GAAE,KAAK,WAAS,GAAE,KAAK,YAAU,OAAG,KAAK,YAAU;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,WAAM,OAAK,KAAK;AAAA,EAAO;AAAA,EAAC,OAAOL,KAAE;AAAC,IAAAA,IAAE,UAAQ,KAAK,SAAQA,IAAE,gBAAc,KAAK,eAAcA,IAAE,WAAS,KAAK,UAASA,IAAE,YAAU,KAAK,WAAUA,IAAE,YAAU,KAAK;AAAA,EAAS;AAAC;AAAC,IAAMM,KAAN,cAAgBP,GAAC;AAAA,EAAC,YAAYC,MAAE,GAAEC,MAAE,OAAG;AAAC,UAAMD,KAAEC,GAAC,GAAE,KAAK,aAAWC,IAAE,KAAK,mBAAiB,IAAIG;AAAA,EAAC;AAAA,EAAC,gBAAgBL,KAAE;AAAC,SAAK,aAAWE,KAAEF;AAAA,EAAC;AAAA,EAAC,KAAKA,KAAEC,KAAEC,MAAE,MAAG;AAAC,WAAOA,OAAG,KAAK,iBAAeD,IAAE,OAAO,GAAE,KAAK,gBAAcA,IAAE,UAAS,KAAK,iBAAeA,IAAE,cAAY,KAAK,iBAAe,GAAE,KAAK,gBAAc,GAAE,KAAK,iBAAe,IAAG,KAAK,iBAAiB,MAAM,GAAE,KAAK,gBAAc,GAAE,KAAK,QAAMD,KAAE,KAAK,OAAK,IAAG,KAAK,kBAAkB;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,KAAK,iBAAiB,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAEC,MAAEG,GAAE,MAAK;AAAC,UAAMF,MAAE,IAAIG;AAAE,WAAM,CAAC,CAAC,KAAK,cAAcL,KAAEE,KAAE,MAAKD,GAAC,MAAIC,IAAE,OAAO,KAAK,gBAAgB,GAAE;AAAA,EAAG;AAAA,EAAC,iBAAiBF,KAAE;AAAC,IAAAA,IAAE,KAAG,KAAK,UAAU,KAAK,gBAAgB;AAAE,UAAK,CAACC,KAAEC,GAAC,IAAE,KAAK,UAAU,KAAK,gBAAgB;AAAE,IAAAF,IAAE,KAAGC,KAAED,IAAE,KAAGE;AAAA,EAAC;AAAA,EAAC,kBAAkBF,KAAEC,KAAEC,MAAEE,GAAE,MAAK;AAAC,UAAMN,MAAE,IAAIO;AAAE,QAAG,CAAC,KAAK,cAAcL,KAAEF,KAAE,MAAKI,GAAC,EAAE,QAAM;AAAG,IAAAJ,IAAE,OAAO,KAAK,gBAAgB,GAAEG,IAAE,KAAG,KAAK,UAAUH,GAAC;AAAE,UAAK,CAACC,KAAEI,EAAC,IAAE,KAAK,UAAUL,GAAC;AAAE,WAAOG,IAAE,KAAGF,KAAEE,IAAE,KAAGE,IAAE;AAAA,EAAE;AAAA,EAAC,UAAUH,KAAE;AAAC,QAAG,MAAIA,IAAE,QAAO;AAAK,UAAMC,MAAE,CAAC,GAAEC,MAAE,IAAIG;AAAE,WAAO,KAAK,cAAcL,KAAEE,KAAED,KAAEG,GAAE,GAAG,KAAGF,IAAE,OAAO,KAAK,gBAAgB,GAAED,OAAG;AAAA,EAAI;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,iBAAiB;AAAA,EAAS;AAAA,EAAC,aAAY;AAAC,QAAG,OAAK,KAAK,iBAAiB,QAAQ,OAAM,IAAI,MAAM,iBAAiB;AAAE,WAAO,KAAK,MAAM,KAAK,iBAAiB,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,KAAEC,KAAEC,KAAEJ,KAAE;AAAC,QAAG,KAAK,iBAAiB,UAAU,QAAM;AAAG,QAAIC,MAAE,KAAK,iBAAiB;AAAS,SAAI,KAAK,iBAAiB,gBAAc,MAAIA,OAAG,KAAK,iBAAiB,gBAAe,KAAK,iBAAiB,OAAOE,GAAC,GAAEA,IAAE,WAASD,MAAE,KAAK,mBAAiBC,IAAE,gBAAc,KAAK,cAAY;AAAC,UAAGC,KAAE;AAAC,YAAG,MAAIA,IAAE,OAAO,KAAG,MAAIH,KAAE;AAAC,gBAAMC,MAAE,KAAK,MAAMC,IAAE,OAAO;AAAE,UAAAC,IAAE,KAAK,CAACF,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC;AAAA,QAAC,MAAM,CAAAE,IAAE,KAAK,KAAK,cAAc,KAAK,OAAMD,IAAE,SAAQF,GAAC,CAAC;AAAE,cAAMC,MAAE,KAAK,MAAMC,IAAE,UAAQ,CAAC;AAAE,QAAAC,IAAE,KAAK,CAACF,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC;AAAA,MAAC;AAAC,UAAGD,MAAE,GAAEC,QAAIC,IAAE,gBAAcA,IAAE,YAAU,KAAK,kBAAiB,KAAK,cAAc,CAAAA,IAAE,UAAQ,KAAK,aAAa,GAAEA,IAAE,gBAAc,KAAK,mBAAmB,KAAK,OAAMA,IAAE,OAAO,GAAEA,IAAE,WAAS,GAAE,KAAK;AAAA,WAAoB;AAAC,YAAG,CAAC,KAAK,kBAAkB,EAAE,QAAOH,QAAIM,GAAE,SAAOH,IAAE,gBAAc,KAAK,mBAAmB,KAAK,OAAMA,IAAE,OAAO,GAAEA,IAAE,YAAU,MAAGH,QAAIM,GAAE,OAAKH,IAAE,WAASA,IAAE,eAAcA,IAAE,YAAU,QAAIA,IAAE,WAASA,IAAE,gBAAcD,KAAE;AAAI,aAAK,iBAAiB,OAAOC,GAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAGA,IAAE,YAAUD,MAAE,KAAK,kBAAiBE,KAAE;AAAC,UAAG,MAAIA,IAAE,OAAO,KAAG,MAAIH,KAAE;AAAC,cAAMC,MAAE,KAAK,MAAMC,IAAE,OAAO;AAAE,QAAAC,IAAE,KAAK,CAACF,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC;AAAA,MAAC,MAAM,CAAAE,IAAE,KAAK,KAAK,cAAc,KAAK,OAAMD,IAAE,SAAQF,GAAC,CAAC;AAAE,YAAMC,MAAEC,IAAE,WAASA,IAAE;AAAc,UAAG,MAAID,KAAE;AAAC,cAAMA,MAAE,KAAK,MAAMC,IAAE,UAAQ,CAAC;AAAE,QAAAC,IAAE,KAAK,CAACF,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC;AAAA,MAAC,MAAM,CAAAE,IAAE,KAAK,KAAK,cAAc,KAAK,OAAMD,IAAE,SAAQD,GAAC,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK,iBAAe,KAAK,IAAIC,IAAE,WAASA,IAAE,aAAa,IAAE,KAAK,eAAaA,IAAE,YAAU,KAAK,aAAYA,IAAE,YAAU,OAAI;AAAA,EAAE;AAAA,EAAC,UAAUD,KAAE;AAAC,QAAG,OAAKA,IAAE,QAAQ,OAAM,IAAI,MAAM,iBAAiB;AAAE,UAAMC,MAAED,IAAE,iBAAe,IAAE,IAAEA,IAAE,WAASA,IAAE;AAAc,WAAO,KAAK,cAAc,KAAK,OAAMA,IAAE,SAAQC,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUD,KAAE;AAAC,QAAG,OAAKA,IAAE,QAAQ,OAAM,IAAI,MAAM,iBAAiB;AAAE,UAAMC,MAAED,IAAE,iBAAe,IAAE,IAAEA,IAAE,WAASA,IAAE;AAAc,WAAO,KAAK,cAAc,KAAK,OAAMA,IAAE,SAAQC,GAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAK,KAAK,gBAAe,MAAK,gBAAgB,KAAG,KAAK,aAAa,GAAE,KAAK;AAAgB,QAAG,CAAC,KAAK,gBAAgB,EAAE,QAAM;AAAG,SAAI,KAAK,cAAY,GAAE,KAAK,cAAY,MAAG,KAAK,gBAAc,GAAE,KAAK,gBAAgB,IAAG,KAAG,KAAK,eAAa,KAAK,mBAAmB,KAAK,OAAM,KAAK,aAAa,CAAC,GAAE,KAAK,iBAAgB,MAAIM,GAAE,KAAK,MAAM,KAAK,kBAAkB,CAAC,CAAC,GAAE;AAAC,WAAK,cAAY,CAAC,KAAK,gBAAgB;AAAE;AAAA,IAAK;AAAC,QAAIN,MAAE,KAAK;AAAc,WAAKA,MAAG,MAAK,iBAAiB,GAAE,EAAEA;AAAE,SAAK,iBAAiB,UAAQ,KAAK,aAAa,GAAE,KAAK,iBAAiB,gBAAc,KAAK,mBAAmB,KAAK,OAAM,KAAK,iBAAiB,OAAO,GAAE,KAAK,iBAAiB,WAAS,GAAE,KAAK,iBAAiB,YAAU,KAAK,iBAAiB,YAAU,OAAG,EAAE,KAAK;AAAc,UAAMC,MAAE,KAAK,oBAAoB;AAAE,SAAK,eAAa,MAAIK,GAAE,KAAK,MAAML,GAAC,CAAC;AAAE,QAAIJ,MAAEI,MAAE,KAAK,gBAAc;AAAE,QAAGJ,OAAG,KAAK,MAAM,WAASA,MAAE,IAAG,KAAK,aAAW,MAAIS,GAAE,KAAK,MAAMT,GAAC,CAAC,GAAE,KAAK,iBAAe,GAAE;AAAC,YAAME,MAAE,KAAK,eAAa,KAAK,iBAAe,KAAK,eAAcC,MAAE,KAAK,aAAW,KAAK,iBAAe,KAAK;AAAc,UAAIC,MAAE,KAAK,OAAO,KAAK,eAAaF,MAAEC,QAAI,KAAK,cAAc;AAAE,MAAAC,OAAG,MAAIA,MAAEF,MAAEC,MAAE,IAAE,IAAE,IAAG,KAAK,mBAAiB,KAAK,eAAaD,MAAEC,MAAEC,MAAE,KAAK,iBAAgB,KAAK,mBAAiB,SAAM,KAAK,mBAAiB;AAAA,IAAE,MAAM,MAAK,mBAAiB;AAAE,WAAM;AAAA,EAAE;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK,OAAK,KAAK,MAAM,SAAO;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAM,EAAE,KAAK;AAAA,EAAI;AAAA,EAAC,eAAc;AAAC,WAAM,EAAE,KAAK;AAAA,EAAI;AAAA,EAAC,sBAAqB;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK,OAAK;AAAA,EAAC;AAAC;;;ACAnxL,IAAMM,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,IAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,GAAE,WAAS;AAAK,IAAMM,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYN,KAAEC,KAAEC,KAAE;AAAC,UAAMF,KAAE,MAAG,IAAE,GAAE,KAAK,cAAY,MAAK,KAAK,UAAQ,IAAIO,MAAE,KAAK,QAAQ,gBAAgBL,GAAC,GAAE,KAAK,WAASD,IAAE,gBAAe,KAAK,iBAAe,EAAEA,IAAE,mBAAiB,KAAGC,KAAE,KAAK,gBAAcD,IAAE,sBAAoB,KAAGC,KAAE,KAAK,WAAS,IAAIG,MAAE,KAAK,SAAS,KAAKJ,IAAE,cAAa,IAAE,GAAE,KAAK,SAAS,MAAMC,GAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,KAAE;AAAC,QAAG,MAAI,KAAK,SAAS,OAAO,EAAE,QAAO,KAAK,cAAY,OAAG,EAAC,OAAM,CAACA,GAAC,EAAC;AAAE,QAAG,CAAC,KAAK,aAAY;AAAC,UAAIC,MAAE;AAAG,cAAO,KAAK,UAAS;AAAA,QAAC,KAAK,EAAE;AAAA,QAAY,KAAK,EAAE;AAAA,QAAQ;AAAQ,eAAK,SAAS,WAAS;AAAE;AAAA,QAAM,KAAK,EAAE;AAAY,eAAK,aAAW,KAAK,SAAS,WAAS,MAAG,KAAK,SAAS,WAAW;AAAG;AAAA,QAAM,KAAK,EAAE;AAAQ,eAAK,aAAW,KAAK,SAAS,WAAS,MAAG,KAAK,SAAS,UAAU;AAAG;AAAA,QAAM,KAAK,EAAE;AAAa,eAAK,aAAWA,MAAE;AAAI;AAAA,QAAM,KAAK,EAAE;AAAO,eAAK,aAAW,KAAK,SAAS,WAAS,MAAG,KAAK;AAAA,MAAa;AAAC,YAAME,MAAE,KAAK,QAAQ,oBAAoBH,GAAC;AAAE,UAAG,KAAK,SAAS,QAAQ,KAAGG,MAAE,MAAG,KAAK,SAAS,OAAO,EAAE,QAAM,EAAC,OAAM,CAACH,GAAC,EAAC;AAAE,UAAG,CAAC,KAAK,QAAQ,KAAKA,KAAE,KAAK,UAASC,GAAC,EAAE,QAAM,EAAC,OAAM,CAACD,GAAC,EAAC;AAAA,IAAC;AAAC,QAAIG;AAAE,QAAG,KAAK,YAAY,CAAAA,MAAE,KAAK,SAAS,UAAU;AAAA,SAAM;AAAC,UAAIH;AAAE,cAAO,KAAK,UAAS;AAAA,QAAC,KAAK,EAAE;AAAA,QAAY;AAAQ,UAAAA,MAAE,MAAG,KAAK,SAAS,WAAW;AAAE;AAAA,QAAM,KAAK,EAAE;AAAQ,UAAAA,MAAE,MAAG,CAAC,KAAK,SAAS,UAAU;AAAE;AAAA,QAAM,KAAK,EAAE;AAAQ,UAAAA,MAAE,CAAC,KAAK,SAAS,UAAU;AAAE;AAAA,QAAM,KAAK,EAAE;AAAY,UAAAA,MAAE;AAAE;AAAA,QAAM,KAAK,EAAE;AAAA,QAAa,KAAK,EAAE;AAAO,UAAAA,MAAE,CAAC,KAAK;AAAA,MAAc;AAAC,UAAIC,MAAED,MAAE,KAAK,SAAS,OAAO;AAAE,MAAAC,OAAG,KAAK,MAAMA,GAAC,GAAED,MAAEC,MAAE,KAAK,SAAS,OAAO,GAAE,KAAK,SAAS,MAAM,GAAEE,MAAE,KAAK,SAAS,UAAU;AAAE,UAAIC,MAAE;AAAG,aAAKJ,OAAGG,MAAG,CAAAH,OAAGG,KAAEA,MAAE,KAAK,SAAS,UAAU,GAAEC,MAAE,CAACA;AAAE,MAAAD,OAAGH,KAAEI,OAAG,KAAK,QAAQ,aAAaD,GAAC,GAAEA,MAAE,KAAK,SAAS,UAAU,KAAG,KAAK,aAAW,KAAK,cAAY,KAAK,QAAQ,UAAUA,GAAC,GAAEA,MAAE,KAAK,SAAS,UAAU,GAAE,KAAK,QAAQ,aAAaA,GAAC,GAAEA,MAAE,KAAK,SAAS,UAAU;AAAA,IAAE;AAAC,QAAIC,MAAE,KAAK,QAAQ,UAAUD,GAAC;AAAE,WAAOC,MAAE,KAAK,QAAQ,UAAU,KAAG,KAAK,cAAY,OAAG,KAAK,gBAAc,KAAK,YAAY,OAAO,GAAE,CAAC,GAAEF,GAAE,UAAUE,KAAE,KAAK,WAAW,GAAE,KAAK,cAAY,UAAQD,MAAE,KAAK,SAAS,UAAU,GAAE,CAAC,KAAK,QAAQ,aAAaA,GAAC,KAAG,KAAK,QAAQ,UAAU,KAAG,KAAK,cAAY,OAAG,KAAK,gBAAcC,MAAE,KAAK,aAAY,KAAK,cAAY,SAAO,KAAK,cAAY,SAAK,KAAK,cAAY,OAAGA,MAAE,KAAK,aAAY,KAAK,cAAY,OAAM,EAAC,OAAM,CAACA,GAAC,EAAC;AAAA,EAAC;AAAC;;;ACA9rE,IAAMI,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEL,KAAEC,KAAEC,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACL,GAAE,WAAS;AAAK,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAEC,KAAEG,KAAE;AAAC,YAAO,KAAK,mBAAiBN,KAAE,KAAK,WAASG,KAAE,KAAK,kBAAgBG,KAAE,KAAK,UAAQ,WAASL,IAAE,QAAMA,IAAE,QAAM,KAAGC,KAAED,IAAE,QAAO;AAAA,MAAC,KAAK,EAAE;AAAA,MAAQ,KAAK,EAAE;AAAA,MAAS,KAAK,EAAE;AAAA,MAAQ,KAAK,EAAE;AAAA,MAAW,KAAK,EAAE;AAAA,IAAO;AAAC,SAAK,UAAQA,IAAE;AAAA,EAAM;AAAA,EAAC,OAAM;AAAC,QAAIG;AAAE,WAAKA,MAAE,KAAK,iBAAiB,KAAK,KAAG;AAAC,UAAGG,GAAEH,GAAC,KAAG,KAAK,SAAO,GAAE;AAAC,YAAG,KAAK,IAAIA,IAAE,OAAKA,IAAE,MAAKA,IAAE,OAAKA,IAAE,IAAI,IAAE,IAAE,KAAK,SAAO,EAAE,QAAOA;AAAE,cAAMJ,MAAE,CAAC;AAAE,eAAOA,IAAE,KAAK,CAAC,CAACI,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC,CAAC,GAAEJ,IAAE,KAAK,CAAC,CAACI,IAAE,OAAK,KAAK,QAAOA,IAAE,OAAK,KAAK,MAAM,GAAE,CAACA,IAAE,OAAK,KAAK,QAAOA,IAAE,OAAK,KAAK,MAAM,GAAE,CAACA,IAAE,OAAK,KAAK,QAAOA,IAAE,OAAK,KAAK,MAAM,GAAE,CAACA,IAAE,OAAK,KAAK,QAAOA,IAAE,OAAK,KAAK,MAAM,GAAE,CAACA,IAAE,OAAK,KAAK,QAAOA,IAAE,OAAK,KAAK,MAAM,CAAC,CAAC,GAAE,EAAC,OAAMJ,IAAC;AAAA,MAAC;AAAC,UAAG,EAAEI,GAAC,GAAE;AAAC,YAAIH,MAAE;AAAK,cAAMC,MAAE,KAAK;AAAgB,YAAIH,KAAEK;AAAE,YAAG,KAAK,aAAWL,KAAES,GAAEJ,KAAE,KAAK,IAAI,KAAK,MAAM,IAAE,CAAC,GAAE,CAACL,MAAG,CAACA,GAAE,SAAO,MAAIA,GAAE,MAAM,QAAQ;AAAS,YAAG,EAAEG,GAAC,MAAID,MAAEC,IAAE,OAAO,EAAE,aAAYH,IAAE,CAAC,KAAK,QAAO,CAAC,IAAG,KAAK,SAAO,GAAE;AAAC,gBAAMC,MAAE,CAAC;AAAE,qBAAUC,OAAKG,IAAE,MAAM,CAAAH,OAAGD,IAAE,KAAKC,GAAC;AAAE,cAAGA,IAAE,YAAUC,OAAKD,IAAE,MAAM,CAAAC,OAAGF,IAAE,KAAKE,IAAE,QAAQ,CAAC;AAAE,cAAGF,IAAE,OAAO,QAAM,EAAC,OAAMA,IAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAC;;;ACAx6C,IAAMS,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEF,KAAEG,KAAEC,IAAE;AAAC,WAAO,IAAIC,IAAEJ,KAAEC,KAAEF,GAAC;AAAA,EAAC;AAAC;AAACA,IAAE,WAAS;AAAK,IAAMK,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYL,KAAED,KAAEK,KAAE;AAAC,UAAMJ,KAAE,OAAG,IAAE,GAAE,KAAK,eAAa,IAAIE,MAAE,KAAK,WAAS,WAASH,IAAE,SAAOA,IAAE,SAAO,MAAIK,KAAE,KAAK,SAAO,WAASL,IAAE,QAAMA,IAAE,QAAM,KAAI,KAAK,YAAU,WAASA,IAAE,WAASA,IAAE,WAAS,IAAG,KAAK,UAAQ,MAAI,KAAK,UAAQ,CAAC,KAAK,UAAS,KAAK,YAAU,OAAK,KAAK,YAAU,KAAI,KAAK,YAAU,OAAK,KAAK,YAAU,KAAI,KAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,YAAYC,KAAE;AAAC,QAAG,KAAK,aAAa,QAAQA,KAAE,KAAE,EAAE,QAAO;AAAK,UAAMC,MAAED,IAAE,CAAC,GAAED,MAAEC,IAAEA,IAAE,SAAO,CAAC,GAAEI,MAAE,CAACL,IAAE,CAAC,IAAEE,IAAE,CAAC,GAAEF,IAAE,CAAC,IAAEE,IAAE,CAAC,CAAC;AAAE,SAAK,aAAa,UAAUG,GAAC;AAAE,UAAMF,MAAE,CAACD,IAAE,CAAC,KAAGF,IAAE,CAAC,IAAEE,IAAE,CAAC,KAAG,KAAK,YAAU,KAAIA,IAAE,CAAC,KAAGF,IAAE,CAAC,IAAEE,IAAE,CAAC,KAAG,KAAK,YAAU,GAAG,GAAEE,KAAE,KAAK,KAAK,KAAG,KAAK,UAAQ,MAAI,KAAK,EAAE;AAAE,QAAIG,MAAE,KAAK,KAAK,KAAG,KAAK,UAAQ,MAAI,KAAK,EAAE;AAAE,SAAK,YAAUA,MAAE,CAACA,MAAG,KAAK,UAAQ,CAAC,KAAK;AAAQ,WAAM,EAAC,OAAM,CAAC,CAACL,KAAE,CAACC,IAAE,CAAC,IAAE,KAAK,UAAQ,IAAEC,IAAED,IAAE,CAAC,IAAE,KAAK,UAAQ,IAAEI,GAAC,GAAE,CAACJ,IAAE,CAAC,IAAE,KAAK,UAAQ,IAAEC,IAAED,IAAE,CAAC,IAAE,KAAK,UAAQ,IAAEI,GAAC,GAAEP,GAAC,CAAC,EAAC;AAAA,EAAC;AAAC;;;ACA13B,IAAMQ,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,IAAE,WAAS;AAAK,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAE;AAAC,SAAK,mBAAiBF,KAAE,KAAK,WAAS,WAASC,IAAE,UAAQA,IAAE,UAAQC,MAAE,GAAE,KAAK,WAAS,WAASD,IAAE,UAAQ,CAACA,IAAE,UAAQC,MAAE;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAIH,MAAE,KAAK,iBAAiB,KAAK;AAAE,WAAKA,OAAG;AAAC,UAAGO,GAAEP,GAAC,EAAE,QAAM,EAAC,MAAKA,IAAE,OAAK,KAAK,UAAS,MAAKA,IAAE,OAAK,KAAK,UAAS,MAAKA,IAAE,OAAK,KAAK,UAAS,MAAKA,IAAE,OAAK,KAAK,SAAQ;AAAE,UAAG,EAAEA,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAO,KAAK,eAAeE,IAAE,OAAM,KAAK,UAAS,KAAK,QAAQ,GAAEA;AAAA,MAAC;AAAC,UAAGM,GAAER,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAO,KAAK,eAAeE,IAAE,OAAM,KAAK,UAAS,KAAK,QAAQ,GAAEA;AAAA,MAAC;AAAC,UAAG,EAAEF,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAO,KAAK,UAAUE,IAAE,QAAO,KAAK,UAAS,KAAK,QAAQ,GAAEA;AAAA,MAAC;AAAC,UAAGA,GAAEF,GAAC,EAAE,QAAM,EAAC,GAAEA,IAAE,IAAE,KAAK,UAAS,GAAEA,IAAE,IAAE,KAAK,SAAQ;AAAE,MAAAA,MAAE,KAAK,iBAAiB,KAAK;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,eAAeC,KAAEC,KAAEC,KAAE;AAAC,QAAGF,IAAE,YAAUG,OAAKH,IAAE,MAAK,UAAUG,KAAEF,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUF,KAAEC,KAAEC,KAAE;AAAC,QAAGF,IAAE,YAAUG,OAAKH,IAAE,CAAAG,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,KAAGD;AAAA,EAAC;AAAC;;;ACAvpB,IAAMM,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEL,KAAEC,KAAEC,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACL,GAAE,WAAS;AAAK,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,SAAK,mBAAiBJ,KAAE,KAAK,WAASG,KAAE,KAAK,kBAAgBC,KAAE,KAAK,eAAa,IAAIA,MAAE,KAAK,WAASH,IAAE,UAAQ,KAAGC,KAAE,KAAK,UAAQD,IAAE,QAAO,KAAK,UAAQA,IAAE,QAAO,KAAK,sBAAoBA,KAAEC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAII;AAAE,WAAKA,MAAE,KAAK,iBAAiB,KAAK,KAAG;AAAC,UAAG,MAAI,KAAK,QAAQ,QAAOA;AAAE,UAAGC,GAAED,GAAC,GAAE;AAAC,YAAG,KAAK,YAAU,EAAE,WAAS,KAAK,UAAQ,GAAE;AAAC,gBAAMN,MAAE,CAAC,CAACM,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC,GAAEL,MAAE,KAAK,aAAa,OAAOD,KAAE,CAAC,KAAK,SAAQ,KAAK,SAAQ,GAAE,KAAK,mBAAmB;AAAE,iBAAOC,MAAE,EAAC,OAAM,CAACA,GAAC,EAAC,IAAE;AAAA,QAAI;AAAC,YAAG,KAAK,IAAIK,IAAE,OAAKA,IAAE,MAAKA,IAAE,OAAKA,IAAE,IAAI,IAAE,IAAE,KAAK,UAAQ,EAAE,QAAM,EAAC,MAAKA,IAAE,OAAK,KAAK,SAAQ,MAAKA,IAAE,OAAK,KAAK,SAAQ,MAAKA,IAAE,OAAK,KAAK,SAAQ,MAAKA,IAAE,OAAK,KAAK,QAAO;AAAA,MAAC;AAAC,YAAME,MAAE,KAAK;AAAgB,UAAG,EAAEA,GAAC,EAAE,QAAO;AAAK,UAAIT,MAAEO;AAAE,UAAG,EAAEA,GAAC,GAAE;AAAC,YAAG,KAAK,aAAWP,MAAEM,GAAEC,KAAE,KAAK,IAAI,KAAK,OAAO,IAAE,CAAC,GAAE,CAACP,OAAG,CAACA,IAAE,SAAO,MAAIA,IAAE,MAAM,QAAQ;AAAA,MAAQ,WAASS,GAAEF,GAAC,KAAG,KAAK,aAAWP,MAAEU,GAAEH,KAAE,KAAK,IAAI,KAAK,OAAO,IAAE,CAAC,GAAE,CAACP,OAAG,CAACA,IAAE,SAAO,MAAIA,IAAE,MAAM,QAAQ;AAAS,aAAOS,IAAE,OAAO,EAAE,aAAYT,KAAE,CAAC,KAAK,SAAQ,GAAE,KAAK,SAAQ,GAAE,KAAK,mBAAmB;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAC;;;ACA/9C,IAAMW,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEH,KAAEI,KAAE;AAAC,WAAO,IAAIC,IAAEJ,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,IAAE,WAAS;AAAK,IAAMK,MAAN,MAAO;AAAA,EAAC,YAAYJ,KAAEC,KAAEC,KAAE;AAAC,SAAK,mBAAiBF,KAAE,KAAK,WAAS,WAASC,IAAE,WAASA,IAAE;AAAA,EAAO;AAAA,EAAC,OAAM;AAAC,QAAIF,MAAE,KAAK,iBAAiB,KAAK;AAAE,WAAKA,OAAG;AAAC,UAAG,CAAC,KAAK,SAAS,QAAOA;AAAE,UAAGM,GAAEN,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAOO,GAAEL,IAAE,KAAK,GAAEA;AAAA,MAAC;AAAC,MAAAF,MAAE,KAAK,iBAAiB,KAAK;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAC;;;ACApP,IAAMQ,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,IAAE,WAAS;AAAK,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAE;AAAC,SAAK,mBAAiBF,KAAE,KAAK,eAAa,WAASC,IAAE,QAAMA,IAAE,QAAM,KAAK,KAAG,MAAI;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAIF,MAAE,KAAK,iBAAiB,KAAK;AAAE,WAAKA,OAAG;AAAC,UAAG,MAAI,KAAK,aAAa,QAAOA;AAAE,YAAMM,MAAEN,GAAE;AAAE,MAAAM,GAAEA,KAAEN,GAAC;AAAE,YAAMO,OAAGD,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,GAAEE,MAAGF,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG;AAAE,UAAGN,GAAEA,GAAC,GAAE;AAAC,cAAMC,MAAE,EAAC,OAAM,CAAC,CAAC,CAACD,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC,CAAC,EAAC;AAAE,eAAO,KAAK,iBAAiBC,IAAE,OAAMM,KAAEC,EAAC,GAAEP;AAAA,MAAC;AAAC,UAAG,EAAED,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAO,KAAK,iBAAiBE,IAAE,OAAMK,KAAEC,EAAC,GAAEN;AAAA,MAAC;AAAC,UAAGO,GAAET,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAO,KAAK,iBAAiBE,IAAE,OAAMK,KAAEC,EAAC,GAAEN;AAAA,MAAC;AAAC,UAAG,EAAEF,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAO,KAAK,YAAYE,IAAE,QAAOK,KAAEC,EAAC,GAAEN;AAAA,MAAC;AAAC,UAAGQ,GAAEV,GAAC,EAAE,QAAOA;AAAE,MAAAA,MAAE,KAAK,iBAAiB,KAAK;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,iBAAiBC,KAAEC,KAAEC,KAAE;AAAC,QAAGF,IAAE,YAAUG,OAAKH,IAAE,MAAK,YAAYG,KAAEF,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,KAAEC,KAAEC,KAAE;AAAC,QAAGF,KAAE;AAAC,YAAMG,MAAE,KAAK,IAAI,KAAK,YAAY,GAAEC,MAAE,KAAK,IAAI,KAAK,YAAY;AAAE,iBAAUM,OAAKV,KAAE;AAAC,cAAMA,MAAEU,IAAE,CAAC,IAAET,KAAEQ,MAAEC,IAAE,CAAC,IAAER;AAAE,QAAAQ,IAAE,CAAC,IAAET,MAAED,MAAEG,MAAEM,MAAEL,KAAEM,IAAE,CAAC,IAAER,MAAEF,MAAEI,MAAEK,MAAEN;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACAngC,IAAMQ,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,IAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,GAAE,WAAS;AAAK,IAAMM,MAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAE;AAAC,SAAK,mBAAiBF,KAAE,KAAK,WAAS,WAASC,IAAE,eAAaA,IAAE,eAAa,MAAK,KAAK,WAAS,WAASA,IAAE,eAAaA,IAAE,eAAa;AAAA,EAAI;AAAA,EAAC,OAAM;AAAC,QAAIF,MAAE,KAAK,iBAAiB,KAAK;AAAE,WAAKA,OAAG;AAAC,UAAG,MAAI,KAAK,YAAU,MAAI,KAAK,SAAS,QAAOA;AAAE,YAAMM,MAAEC,GAAE;AAAE,MAAAP,GAAEM,KAAEN,GAAC;AAAE,YAAMO,OAAGD,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,GAAEE,MAAGF,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG;AAAE,UAAGC,GAAEP,GAAC,GAAE;AAAC,cAAMC,MAAE,EAAC,OAAM,CAAC,CAAC,CAACD,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC,CAAC,EAAC;AAAE,eAAO,KAAK,gBAAgBC,IAAE,OAAMM,KAAEC,EAAC,GAAEP;AAAA,MAAC;AAAC,UAAG,EAAED,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAO,KAAK,gBAAgBE,IAAE,OAAMK,KAAEC,EAAC,GAAEN;AAAA,MAAC;AAAC,UAAGO,GAAET,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAO,KAAK,gBAAgBE,IAAE,OAAMK,KAAEC,EAAC,GAAEN;AAAA,MAAC;AAAC,UAAG,EAAEF,GAAC,GAAE;AAAC,cAAME,MAAE,EAAEF,GAAC;AAAE,eAAO,KAAK,WAAWE,IAAE,QAAOK,KAAEC,EAAC,GAAEN;AAAA,MAAC;AAAC,UAAGA,GAAEF,GAAC,EAAE,QAAOA;AAAE,MAAAA,MAAE,KAAK,iBAAiB,KAAK;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,gBAAgBC,KAAEC,KAAEC,KAAE;AAAC,QAAGF,IAAE,YAAUG,OAAKH,IAAE,MAAK,WAAWG,KAAEF,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,WAAWF,KAAEC,KAAEC,KAAE;AAAC,QAAGF,IAAE,YAAUG,OAAKH,KAAE;AAAC,YAAMA,OAAGG,IAAE,CAAC,IAAEF,OAAG,KAAK,UAASG,OAAGD,IAAE,CAAC,IAAED,OAAG,KAAK;AAAS,MAAAC,IAAE,CAAC,IAAEF,MAAED,KAAEG,IAAE,CAAC,IAAED,MAAEE;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACAxkC,IAAMK,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,IAAE;AAAC,WAAO,IAAIC,GAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,GAAE,WAAS;AAAK,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAE;AAAC,SAAK,mBAAiBF,KAAE,KAAK,WAAS,WAASC,IAAE,YAAUA,IAAE,YAAU,KAAGC,KAAE,KAAK,WAAS,WAASD,IAAE,SAAOA,IAAE,SAAO,KAAGC,KAAE,KAAK,SAAOD,IAAE,UAAS,KAAK,WAAS,MAAI,KAAK,UAAQ,KAAK,IAAI,KAAK,OAAO,IAAG,KAAK,WAAS,MAAI,KAAK,UAAQ,KAAK,IAAI,KAAK,OAAO,IAAG,KAAK,WAAS,IAAIK,MAAE,KAAK,SAAS,SAAS,KAAK,OAAO,GAAE,KAAK,SAAS,SAAS,KAAK,OAAO,GAAE,KAAK,UAAQ,IAAIC,MAAE,KAAK,QAAQ,gBAAgBL,GAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAIA,MAAE,KAAK,iBAAiB,KAAK;AAAE,WAAKA,OAAG;AAAC,UAAG,MAAI,KAAK,WAAS,MAAI,KAAK,QAAQ,QAAOA;AAAE,UAAGM,GAAEN,GAAC,GAAE;AAAC,cAAMF,MAAE,KAAK,aAAaE,IAAE,KAAK;AAAE,YAAGF,IAAE,OAAO,QAAM,EAAC,OAAMA,IAAC;AAAA,MAAC;AAAC,UAAG,EAAEE,GAAC,GAAE;AAAC,cAAMF,MAAE,KAAK,aAAaE,IAAE,KAAK;AAAE,YAAGF,IAAE,OAAO,QAAM,EAAC,OAAMA,IAAC;AAAA,MAAC;AAAC,MAAAE,MAAE,KAAK,iBAAiB,KAAK;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,aAAaF,KAAE;AAAC,UAAMC,MAAE,CAAC;AAAE,eAAUC,OAAKF,IAAE,KAAG,KAAK,QAAQ,KAAKE,KAAE,KAAK,QAAQ,EAAE,SAAO,KAAK,QAAO;AAAA,MAAC,KAAK,EAAE;AAAA,MAAM;AAAQ,QAAAD,IAAE,KAAK,KAAK,gBAAgBC,KAAE,KAAE,CAAC;AAAE;AAAA,MAAM,KAAK,EAAE;AAAO,QAAAD,IAAE,KAAK,KAAK,iBAAiBC,GAAC,CAAC;AAAE;AAAA,MAAM,KAAK,EAAE;AAAS,QAAAD,IAAE,KAAK,KAAK,mBAAmBC,GAAC,CAAC;AAAE;AAAA,MAAM,KAAK,EAAE;AAAO,QAAAD,IAAE,KAAK,KAAK,gBAAgBC,KAAE,IAAE,CAAC;AAAA,IAAC;AAAA,QAAM,CAAAD,IAAE,KAAKC,GAAC;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,gBAAgBD,KAAEC,KAAE;AAAC,UAAME,MAAE,IAAIA,MAAEC,KAAE,KAAK,QAAQ,oBAAoBJ,GAAC;AAAE,QAAIM,MAAE,KAAK,MAAMF,KAAE,KAAK,OAAO;AAAE,UAAIE,QAAIA,MAAE;AAAG,UAAMP,MAAEO,MAAE,KAAG,GAAED,MAAED,KAAEE,KAAEG,MAAE,KAAK,UAAQ,IAAGC,KAAE,IAAEX,KAAEY,MAAE,IAAE,KAAK,KAAGP,KAAEC,KAAEO,KAAE,IAAE,KAAK,KAAG,KAAK,OAAO,GAAEC,MAAE,IAAE,KAAK,KAAG,KAAK,OAAO,GAAEC,KAAE,IAAE,KAAK,KAAG,KAAK,OAAO,GAAEP,MAAE,OAAI,KAAK,OAAO,IAAE,GAAEQ,KAAE,OAAI,KAAK,OAAO,IAAE,GAAEP,MAAE,IAAIJ;AAAE,SAAK,QAAQ,iBAAiBI,GAAC,GAAEL,IAAE,UAAUK,IAAE,EAAE;AAAE,QAAIQ,KAAE;AAAE,eAAO;AAAC,UAAG,CAAC,KAAK,QAAQ,kBAAkBP,KAAED,GAAC,GAAE;AAAC,QAAAL,IAAE,OAAOH,IAAEA,IAAE,SAAO,CAAC,CAAC;AAAE;AAAA,MAAK;AAAC;AAAC,cAAMA,MAAEgB;AAAE,YAAId;AAAE,YAAGc,MAAGN,IAAET,KAAE;AAAC,gBAAMA,MAAE,KAAK,UAAQ,KAAG,IAAE,MAAG,KAAK,IAAIM,MAAEI,MAAEX,MAAEY,EAAC;AAAG,UAAAV,MAAED,MAAE,KAAK,IAAIU,MAAEX,MAAEa,GAAC,GAAEX,OAAGD,MAAE,KAAK,IAAIc,KAAEJ,MAAEX,MAAEc,EAAC,GAAEZ,OAAG;AAAA,QAAC,MAAM,CAAAA,MAAE,MAAG,KAAK,UAAQ,KAAK,IAAI,MAAGS,MAAEX,GAAC;AAAE,QAAAG,IAAE,OAAO,CAACK,IAAE,GAAG,CAAC,IAAEN,MAAEM,IAAE,IAAGA,IAAE,GAAG,CAAC,IAAEN,MAAEM,IAAE,EAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOL,IAAE,KAAK;AAAA,EAAC;AAAA,EAAC,iBAAiBH,KAAE;AAAC,UAAMC,MAAE,IAAIE,MAAEA,MAAE,KAAK,QAAQ,oBAAoBH,GAAC;AAAE,SAAK,MAAMG,MAAE,KAAK,OAAO;AAAE,QAAIC,KAAE;AAAG,eAAO;AAAC,UAAIJ,MAAE;AAAG,UAAG,KAAK,QAAQ,mBAAmB,GAAE;AAAC,cAAME,MAAE,IAAIE;AAAE,aAAK,QAAQ,iBAAiBF,GAAC;AAAE,cAAMC,MAAE,IAAIC;AAAE,YAAG,KAAK,QAAQ,kBAAkB,KAAK,SAAQD,GAAC,GAAE;AAAC,gBAAMG,MAAE,IAAIF;AAAE,eAAK,QAAQ,kBAAkB,KAAK,SAAQE,GAAC,MAAIF,MAAGH,IAAE,UAAUC,IAAE,EAAE,GAAEE,KAAE,SAAIH,IAAE,OAAOC,IAAE,EAAE,GAAED,IAAE,OAAO,CAACC,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,IAAGA,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,EAAE,CAAC,GAAED,IAAE,OAAO,CAACE,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,IAAGA,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,EAAE,CAAC,GAAEF,IAAE,OAAO,CAACE,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,IAAGA,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,EAAE,CAAC,GAAEF,IAAE,OAAO,CAACK,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,IAAGA,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,EAAE,CAAC,GAAEN,MAAE;AAAA,QAAG;AAAA,MAAC;AAAC,UAAG,CAACA,KAAE;AAAC,QAAAC,IAAE,OAAO,KAAK,QAAQ,WAAW,CAAC;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,WAAOA,IAAE,KAAK;AAAA,EAAC;AAAA,EAAC,mBAAmBD,KAAE;AAAC,UAAMC,MAAE,IAAIE,MAAEA,MAAE,KAAK,QAAQ,oBAAoBH,GAAC;AAAE,SAAK,MAAMG,MAAE,KAAK,OAAO;AAAE,QAAIC,KAAE;AAAG,eAAO;AAAC,UAAIJ,MAAE;AAAG,UAAG,KAAK,QAAQ,mBAAmB,GAAE;AAAC,cAAME,MAAE,IAAIE;AAAE,aAAK,QAAQ,iBAAiBF,GAAC;AAAE,cAAMC,MAAE,IAAIC;AAAE,YAAG,KAAK,QAAQ,kBAAkB,KAAK,UAAQ,GAAED,GAAC,GAAE;AAAC,gBAAMG,MAAE,IAAIF;AAAE,eAAK,QAAQ,kBAAkB,KAAK,SAAQE,GAAC,MAAI,KAAK,QAAQ,aAAa,KAAK,UAAQ,CAAC,MAAIF,MAAGH,IAAE,UAAUC,IAAE,EAAE,GAAEE,KAAE,SAAIH,IAAE,OAAOC,IAAE,EAAE,GAAED,IAAE,OAAO,CAACE,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,IAAGA,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,EAAE,CAAC,GAAEF,IAAE,OAAO,CAACK,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,IAAGA,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQ,IAAEA,IAAE,EAAE,CAAC,IAAGN,MAAE;AAAA,QAAG;AAAA,MAAC;AAAC,UAAG,CAACA,KAAE;AAAC,QAAAC,IAAE,OAAO,KAAK,QAAQ,WAAW,CAAC;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,WAAOA,IAAE,KAAK;AAAA,EAAC;AAAC;;;ACA1uG,IAAMgB,KAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,IAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,GAAE,WAAS;AAAK,IAAMM,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYN,KAAEC,KAAEG,KAAE;AAAC,UAAMJ,KAAE,MAAG,IAAE,GAAE,KAAK,kBAAgB,IAAIO,MAAE,KAAK,gBAAgB,gBAAgBH,GAAC,GAAE,KAAK,eAAaH,IAAE,eAAa,MAAG,KAAK,WAASA,IAAE,SAAOA,IAAE,SAAO,KAAGG,KAAE,KAAK,mBAAiBH,IAAE,SAAQ,KAAK,gBAAcA,IAAE,qBAAmBA,IAAE,qBAAmB,KAAGG,KAAE,KAAK,YAAU,EAAEH,IAAE,kBAAgBA,IAAE,kBAAgB,KAAGG,KAAE,KAAK,WAAS,IAAIA,MAAE,KAAK,SAAS,KAAKH,IAAE,mBAAkB,KAAE,GAAE,KAAK,SAAS,MAAMG,GAAC,GAAE,KAAK,WAAS,KAAK;AAAA,EAAgB;AAAA,EAAC,YAAYJ,KAAE;AAAC,QAAG,KAAK,SAAS,QAAQ,EAAE,QAAO;AAAK,QAAIE;AAAE,QAAG,KAAK,YAAY,CAAAA,MAAE,KAAK,SAAS,UAAU;AAAA,SAAM;AAAC,WAAK,qBAAmB,EAAE,eAAa,KAAK,WAAS,KAAK,WAAS,EAAE,cAAY,KAAK,WAAS,KAAK,kBAAiB,KAAK,SAAS,WAAS;AAAE,UAAIC,KAAEC,MAAE;AAAG,cAAO,KAAK,UAAS;AAAA,QAAC,KAAK,EAAE;AAAa,UAAAD,MAAE,CAAC,KAAK,WAAUA,MAAE,KAAK,gBAAgBA,GAAC,GAAEC,MAAE;AAAG;AAAA,QAAM,KAAK,EAAE;AAAA,QAAY;AAAQ,UAAAD,MAAE,CAAC,KAAK,SAAS,UAAU,IAAE;AAAE;AAAA,QAAM,KAAK,EAAE;AAAY,UAAAA,MAAE,CAAC,KAAK,SAAS,UAAU,GAAE,KAAK,SAAS,WAAS,KAAK,SAAS,UAAU;AAAE;AAAA,QAAM,KAAK,EAAE;AAAY,UAAAA,MAAE;AAAE;AAAA,QAAM,KAAK,EAAE;AAAO,UAAAA,MAAE,CAAC,KAAK,WAAUA,MAAE,KAAK,gBAAgBA,GAAC,GAAE,KAAK,SAAS,WAAS,MAAG,KAAK;AAAA,MAAY;AAAC,UAAG,CAAC,KAAK,gBAAgB,KAAKH,KAAE,KAAK,UAASI,GAAC,EAAE,QAAO;AAAK,WAAK,SAAS,MAAM;AAAE,UAAIL,MAAE;AAAE,aAAKI,MAAEJ,MAAG,CAAAI,OAAGJ,KAAEA,MAAE,KAAK,SAAS,UAAU;AAAE,MAAAA,OAAGI,KAAED,MAAEH,KAAE,KAAK,cAAY;AAAA,IAAE;AAAC,UAAMI,MAAE,IAAIK;AAAE,WAAO,KAAK,gBAAgB,kBAAkBN,KAAEC,GAAC,IAAE,KAAK,aAAW,EAAE,eAAa,KAAK,gBAAgB,UAAU,KAAG,KAAK,cAAY,OAAG,QAAM,KAAK,aAAW,EAAE,eAAa,KAAK,gBAAgB,UAAU,MAAI,KAAK,cAAY,OAAG,KAAK,YAAU,QAAM,KAAK,kBAAkB,aAAaA,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQA,IAAE,IAAGA,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQA,IAAE,EAAE,GAAE,KAAK,gBAAc,KAAK,kBAAkB,YAAYA,IAAE,IAAGA,IAAE,EAAE,GAAE,KAAK,sBAAoB,KAAK,cAAY,OAAG;AAAA,EAAK;AAAA,EAAC,gBAAgBH,KAAE;AAAC,QAAIC,MAAED,MAAE,KAAK,SAAS,OAAO;AAAE,WAAOC,OAAG,KAAK,MAAMA,GAAC,GAAEA,MAAE,KAAK,SAAS,OAAO;AAAA,EAAC;AAAC;;;ACA19D,IAAMQ,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEJ,KAAE;AAAC,WAAO,IAAIK,IAAEJ,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,IAAE,WAAS;AAAK,IAAMK,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYL,KAAEC,KAAEE,KAAE;AAAC,UAAMH,KAAE,OAAG,IAAE,GAAE,KAAK,eAAa,IAAID,MAAE,KAAK,eAAa,WAASE,IAAE,eAAaA,IAAE,aAAY,KAAK,UAAQ,WAASA,IAAE,SAAOA,IAAE,SAAOE,MAAE,GAAE,KAAK,QAAMF,IAAE,oBAAmB,KAAK,YAAU,WAASA,IAAE,kBAAgBA,IAAE,kBAAgBE,MAAE,GAAE,KAAK,kBAAgB;AAAA,EAAE;AAAA,EAAC,YAAYH,KAAE;AAAC,QAAIC;AAAE,YAAO,KAAK,OAAM;AAAA,MAAC,KAAKK,GAAE;AAAA,MAAK;AAAQ,aAAK,mBAAiBL,MAAE,KAAK,eAAeD,KAAE,KAAK,WAAU,KAAE,GAAE,KAAK,kBAAgB,OAAG,KAAK,cAAY,UAAKC,MAAE,KAAK,eAAeD,KAAE,KAAK,WAAU,IAAE,GAAE,KAAK,kBAAgB,MAAG,KAAK,cAAY;AAAI;AAAA,MAAM,KAAKM,GAAE;AAAU,QAAAL,MAAE,KAAK,eAAeD,KAAE,KAAK,WAAU,IAAE;AAAE;AAAA,MAAM,KAAKM,GAAE;AAAQ,QAAAL,MAAE,KAAK,eAAeD,KAAE,KAAK,WAAU,KAAE;AAAA,MAAE,KAAKM,GAAE;AAAA,IAAK;AAAC,WAAOL;AAAA,EAAC;AAAA,EAAC,eAAeD,KAAEE,KAAEC,KAAE;AAAC,UAAMJ,MAAEC,IAAE;AAAO,QAAGD,MAAE,EAAE,QAAO;AAAK,UAAMK,MAAED,MAAE,IAAEJ,MAAE,GAAEO,MAAEH,MAAEJ,MAAE,IAAGQ,MAAEJ,MAAE,IAAE;AAAG,QAAIK,KAAEC,KAAE,GAAEC,MAAEP,MAAEH,IAAE,CAAC,IAAEA,IAAED,MAAE,CAAC;AAAE,aAAQY,KAAEP,KAAEO,OAAIL,KAAEK,MAAGJ,KAAE;AAAC,MAAAC,MAAEE,KAAEA,MAAEV,IAAEW,EAAC;AAAE,YAAMR,MAAE,KAAK,aAAa,gBAAgBK,KAAEE,GAAC;AAAE,UAAGD,KAAEN,MAAED,KAAE;AAAC,cAAMF,OAAGE,MAAEO,MAAGN,KAAE,CAACJ,KAAEK,GAAC,IAAE,KAAK,aAAa,WAAWI,KAAEE,KAAEV,GAAC,GAAEM,MAAEM,GAAEJ,KAAEE,KAAEV,GAAC;AAAE,eAAO,KAAK,kBAAkB,aAAaM,IAAE,CAAC,IAAE,KAAK,UAAQF,KAAEE,IAAE,CAAC,IAAE,KAAK,UAAQP,GAAC,GAAE,KAAK,gBAAc,KAAK,kBAAkB,YAAY,CAACA,KAAE,CAACK,GAAC,GAAE,KAAK;AAAA,MAAiB;AAAC,MAAAK,MAAGN;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAC;;;ACA9yC,IAAMU,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,IAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,IAAE,WAAS;AAAK,IAAMM,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYN,KAAEG,KAAEC,KAAE;AAAC,UAAMJ,KAAE,MAAG,IAAE,GAAE,KAAK,UAAQ,IAAIO,MAAE,KAAK,QAAQ,gBAAgBH,GAAC,GAAE,KAAK,eAAa,WAASD,IAAE,eAAaA,IAAE,aAAY,KAAK,UAAQ,WAASA,IAAE,SAAOA,IAAE,SAAOC,MAAE,GAAE,KAAK,YAAU,WAASD,IAAE,gBAAcA,IAAE,gBAAcC,MAAE,GAAE,KAAK,UAAQ,WAASD,IAAE,cAAYA,IAAE,cAAYC,MAAE,GAAE,KAAK,aAAW,WAASD,IAAE,aAAWA,IAAE,WAAU,KAAK,WAAS,IAAIC,MAAE,KAAK,SAAS,KAAKD,IAAE,eAAc,OAAG,KAAE,GAAE,KAAK,cAAY,GAAE,KAAK,YAAU,KAAK,SAAS,KAAK,GAAE,KAAK,WAAS,MAAG,KAAK,WAAS;AAAA,EAAC;AAAA,EAAC,YAAYH,KAAE;AAAC,QAAG,KAAK,SAAS,QAAQ,EAAE,QAAO;AAAK,QAAIC;AAAE,QAAG,KAAK,aAAY;AAAC,YAAMD,MAAE,KAAK,SAAS,UAAU,IAAE,KAAK,aAAYE,MAAE,KAAK,YAAUF;AAAE,MAAAC,MAAEC,MAAE,KAAK,UAAS,KAAK,WAASA;AAAA,IAAC,OAAK;AAAC,UAAG,KAAK,YAAU,KAAK,SAAS,KAAK,GAAE,KAAK,WAAS,MAAG,KAAK,WAAS,GAAE,KAAK,cAAY,KAAK,QAAQ,oBAAoBF,GAAC,IAAE,KAAK,YAAU,KAAK,SAAQ,KAAK,cAAY,EAAE,QAAO,KAAK,cAAY,OAAG;AAAK,UAAG,CAAC,KAAK,QAAQ,KAAKA,KAAE,KAAK,UAAS,KAAE,EAAE,QAAO;AAAK,WAAK,SAAS,MAAM;AAAE,YAAME,MAAE,KAAK,SAAS,UAAU,IAAE,KAAK,aAAYC,MAAE,KAAK,YAAUD;AAAE,MAAAD,MAAEE,MAAE,KAAK,UAAS,KAAK,WAASA,KAAE,KAAK,cAAY;AAAA,IAAE;AAAC,UAAMD,MAAE,IAAIM;AAAE,QAAG,CAAC,KAAK,QAAQ,kBAAkBP,KAAEC,KAAEG,GAAE,GAAG,EAAE,QAAO,KAAK,cAAY,OAAG;AAAK,SAAK,kBAAkB,aAAaH,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQA,IAAE,IAAGA,IAAE,GAAG,CAAC,IAAE,KAAK,UAAQA,IAAE,EAAE;AAAE,UAAMH,MAAE,KAAK,YAAU,KAAK;AAAW,QAAIM,KAAEG;AAAE,WAAO,KAAK,gBAAcH,MAAEH,IAAE,IAAGM,KAAEN,IAAE,OAAKG,MAAE,GAAEG,KAAE,IAAGT,QAAIM,MAAE,CAACA,KAAEG,KAAE,CAACA,KAAG,KAAK,kBAAkB,YAAYH,KAAEG,EAAC,GAAE,KAAK,WAAS,OAAG,KAAK,aAAY,MAAI,KAAK,cAAY,KAAK,cAAY,QAAI,KAAK;AAAA,EAAiB;AAAC;;;ACA7mD,IAAMC,MAAE;AAAR,IAAYC,KAAE;AAAd,IAAiBC,KAAE;AAAnB,IAAsBC,MAAE;AAAxB,IAA2BC,MAAE;AAAG,SAASC,IAAEC,KAAE;AAAC,SAAO,WAASA,IAAE;AAAK;AAAC,IAAMC,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQD,KAAEE,KAAEC,KAAEC,KAAEV,KAAE;AAAC,WAAO,IAAIW,GAAEL,KAAEE,KAAEC,KAAEC,KAAEV,GAAC;AAAA,EAAC;AAAC;AAACO,IAAE,WAAS;AAAK,IAAMI,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYL,KAAEL,IAAEC,IAAEC,KAAEC,KAAE;AAAC,QAAG,KAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,YAAU,GAAE,KAAK,YAAU,GAAE,KAAK,mBAAiB,MAAK,KAAK,qBAAmB,OAAG,KAAK,uBAAqB,MAAG,KAAK,SAAO,KAAK,IAAIH,GAAE,SAAO,EAAE,IAAEC,IAAE,KAAK,SAAO,KAAK,IAAID,GAAE,SAAO,EAAE,IAAEC,IAAE,MAAI,KAAK,UAAQ,MAAI,KAAK,UAAQI,OAAGD,IAAEC,GAAC,KAAGA,IAAE,OAAM;AAAC,UAAG,KAAK,YAAUL,GAAE,YAAU,EAAE,OAAM,KAAK,cAAY,EAAE,QAAO;AAAC,cAAMK,MAAEL,GAAE,QAAM,IAAGQ,MAAE;AAAE,aAAK,aAAW,IAAIH,GAAEA,MAAEG,GAAC,GAAE,KAAK,eAAaR,GAAE,cAAY,OAAK,KAAI,KAAK,aAAW,GAAE,KAAK,gBAAc,OAAG,KAAK,YAAU,GAAE,KAAK,YAAU,GAAE,KAAK,WAAS,GAAE,KAAK,WAAS,GAAE,KAAK,mBAAmB;AAAA,MAAC,OAAK;AAAC,YAAG,KAAK,cAAY,GAAE,KAAK,aAAWA,GAAE,aAAW,GAAE,KAAK,gBAAcA,GAAE,gBAAc,OAAG,KAAK,YAAUA,GAAE,WAAS,KAAGC,IAAE,KAAK,YAAUD,GAAE,WAAS,KAAGC,IAAE,KAAK,YAAU,KAAK,IAAI,KAAK,aAAW,MAAI,KAAK,EAAE,GAAE,KAAK,YAAU,CAAC,KAAK,IAAI,KAAK,aAAW,MAAI,KAAK,EAAE,GAAE,KAAK,OAAO,KAAG,KAAK,WAAS,EAAE,QAAK,KAAK,WAAS,OAAI,KAAK,SAAQ,MAAK,YAAU,KAAK;AAAA,YAAY,QAAK,KAAK,YAAU,MAAG,KAAK,SAAQ,MAAK,YAAU,KAAK;AAAO,YAAG,KAAK,OAAO,KAAG,KAAK,WAAS,EAAE,QAAK,KAAK,WAAS,OAAI,KAAK,SAAQ,MAAK,YAAU,KAAK;AAAA,YAAY,QAAK,KAAK,YAAU,MAAG,KAAK,SAAQ,MAAK,YAAU,KAAK;AAAA,MAAM;AAAC,UAAG,KAAK,kBAAgB,GAAE,KAAK,kBAAgB,GAAE,QAAMC,KAAE;AAAC,cAAK,CAACG,KAAEE,KAAEC,GAAC,IAAEN,IAAE,MAAM,GAAG,GAAEO,MAAE,WAAWF,GAAC,GAAEP,KAAE,WAAWQ,GAAC;AAAE,aAAK,kBAAgB,CAACR,KAAED,KAAE,KAAK,kBAAgBU,MAAEV,KAAE,KAAK,qBAAmB;AAAA,MAAE;AAAC,WAAK,qBAAmB,IAAIM,MAAE,KAAK,iBAAiBA,GAAC,GAAE,KAAK,YAAUA;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,YAAU,KAAK,YAAY,IAAE;AAAA,EAAI;AAAA,EAAC,qBAAoB;AAAC,QAAG,CAAC,GAAE,aAAY;AAAC,SAAE,cAAY,CAAC;AAAE,eAAQA,MAAE,GAAEA,MAAEF,KAAEE,MAAI,UAAQE,MAAE,GAAEA,MAAEJ,KAAEI,MAAI,IAAE,YAAY,KAAK,KAAK,WAAW,SAAS,CAAC,GAAE,GAAE,YAAY,KAAK,KAAK,WAAW,SAAS,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBF,KAAE;AAAC,QAAIE,KAAEC,KAAEC,KAAET,IAAEG,KAAEC,KAAEE,KAAEI,KAAEC,KAAEC,KAAEC,KAAEC,IAAEC,IAAEC;AAAE,SAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,QAAM,GAAEV,MAAEI,MAAEK,KAAEF,MAAE,OAAO,WAAUF,MAAEC,MAAEI,KAAEF,KAAE,CAAC,OAAO;AAAU,UAAMG,KAAE,MAAI,KAAK;AAAU,QAAIC,KAAE;AAAE,eAAUnB,OAAKM,IAAE,OAAM;AAAC,YAAMA,MAAEN,MAAEA,IAAE,SAAO;AAAE,eAAQE,KAAE,GAAEA,KAAEI,KAAEJ,KAAI,CAAAG,MAAEL,IAAEE,EAAC,EAAE,CAAC,GAAEE,MAAEJ,IAAEE,EAAC,EAAE,CAAC,GAAEM,MAAEH,MAAE,KAAK,kBAAgB,KAAK,UAASI,MAAEL,MAAE,KAAK,kBAAgB,KAAK,UAASc,MAAGR,MAAE,KAAK,YAAUF,MAAE,KAAK,YAAUC,KAAER,KAAE,KAAK,YAAUO,MAAE,KAAK,YAAUC,QAAIC,MAAEF,KAAEP,KAAEQ,MAAGF,MAAE,KAAK,IAAIA,KAAEG,GAAC,GAAEE,MAAE,KAAK,IAAIA,KAAEF,GAAC,GAAEC,MAAE,KAAK,IAAIA,KAAEV,EAAC,GAAEY,MAAE,KAAK,IAAIA,KAAEZ,EAAC,GAAEa,MAAE,KAAK,IAAIA,KAAEV,GAAC,GAAEW,KAAE,KAAK,IAAIA,IAAEX,GAAC,GAAEY,KAAE,KAAK,IAAIA,IAAEX,GAAC,GAAEY,KAAE,KAAK,IAAIA,IAAEZ,GAAC,GAAEc;AAAA,IAAG;AAAC,IAAAL,MAAEA,QAAI,OAAO,YAAUA,MAAE,CAACd,MAAE,KAAK,QAAOe,KAAEA,OAAI,CAAC,OAAO,YAAUA,KAAE,KAAK,QAAOC,KAAEA,OAAI,OAAO,YAAUA,KAAE,CAAC,KAAK,QAAOC,KAAEA,OAAI,CAAC,OAAO,YAAUA,KAAEjB,MAAE,KAAK;AAAO,UAAMoB,KAAEL,KAAED,KAAEO,KAAEJ,KAAED;AAAE,QAAG,KAAK,uBAAqBI,MAAGC,IAAE,KAAK,cAAY,KAAK,uBAAqBP,MAAEE,IAAE,KAAK,oBAAmB;AAAC,UAAIV,MAAE,IAAE,KAAK,kBAAgB,KAAK,WAAS,KAAK,QAAOE,MAAER,MAAE,KAAK,kBAAgB,KAAK,WAAS,KAAK,QAAOS,MAAE,CAACT,MAAE,KAAK,kBAAgB,KAAK,WAAS,KAAK,QAAOU,MAAE,IAAE,KAAK,kBAAgB,KAAK,WAAS,KAAK;AAAO,UAAGQ,IAAE;AAAC,cAAMlB,MAAE,CAAC,CAACM,KAAEG,GAAC,GAAE,CAACH,KAAEI,GAAC,GAAE,CAACF,KAAEC,GAAC,GAAE,CAACD,KAAEE,GAAC,CAAC;AAAE,QAAAJ,MAAEG,MAAE,OAAO,WAAUD,MAAEE,MAAE,CAAC,OAAO;AAAU,mBAAUT,MAAKD,KAAE;AAAC,gBAAMA,MAAE,KAAK,YAAUC,GAAE,CAAC,IAAE,KAAK,YAAUA,GAAE,CAAC,GAAEC,KAAE,KAAK,YAAUD,GAAE,CAAC,IAAE,KAAK,YAAUA,GAAE,CAAC;AAAE,UAAAK,MAAE,KAAK,IAAIA,KAAEN,GAAC,GAAEQ,MAAE,KAAK,IAAIA,KAAER,GAAC,GAAES,MAAE,KAAK,IAAIA,KAAEP,EAAC,GAAEQ,MAAE,KAAK,IAAIA,KAAER,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,MAAAK,MAAEA,QAAI,OAAO,YAAU,KAAK,IAAIA,KAAED,GAAC,IAAEA,KAAEK,MAAEA,QAAI,OAAO,YAAU,KAAK,IAAIA,KAAEF,GAAC,IAAEA,KAAEG,MAAEA,QAAI,CAAC,OAAO,YAAU,KAAK,IAAIA,KAAEJ,GAAC,IAAEA,KAAEK,MAAEA,QAAI,CAAC,OAAO,YAAU,KAAK,IAAIA,KAAEH,GAAC,IAAEA;AAAA,IAAC;AAAC,SAAK,QAAM,KAAK,MAAMH,MAAE,KAAK,MAAM,GAAE,KAAK,QAAM,KAAK,MAAMK,MAAE,KAAK,MAAM,GAAE,KAAK,QAAM,KAAK,MAAMD,MAAE,KAAK,MAAM,GAAE,KAAK,QAAM,KAAK,MAAME,MAAE,KAAK,MAAM,GAAE,KAAK,YAAU,KAAK,QAAM,GAAE,KAAK,YAAU,KAAK,QAAM,GAAE,KAAK,sBAAoBM,KAAEjB,OAAIkB,KAAEjB,OAAGkB,KAAElB,QAAI,KAAK,sBAAsBG,KAAEU,IAAEC,IAAEH,KAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBT,KAAEE,KAAEC,KAAEC,KAAER,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAC,IAAEG,KAAEF,MAAE,oBAAI,OAAIC,MAAE,KAAK,sBAAqBE,MAAEF,MAAEH,KAAEQ,MAAED,MAAED;AAAE,QAAIG,MAAE,KAAK,KAAKJ,MAAEN,EAAC;AAAE,QAAGU,OAAG,EAAE;AAAO,UAAMC,MAAE,KAAK,MAAML,MAAEI,GAAC;AAAE,QAAIG,KAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,IAAAZ,OAAI,KAAK,SAAOC,KAAEP,OAAGgB,KAAE,CAACrB,MAAE,KAAK,QAAOsB,KAAE,KAAK,QAAOC,KAAEb,QAAIW,KAAE,CAAC,KAAK,QAAOC,KAAEtB,MAAE,KAAK,QAAOuB,KAAEf;AAAG,aAAQR,MAAE,GAAEA,MAAEG,IAAE,QAAOH,MAAI,KAAGc,MAAEX,IAAEH,GAAC,GAAE,EAAEc,IAAE,SAAO,GAAG,UAAQR,MAAE,GAAEA,MAAEQ,IAAE,QAAOR,OAAI;AAAC,UAAGS,KAAED,IAAER,MAAE,CAAC,GAAEU,KAAEF,IAAER,GAAC,GAAED,KAAE;AAAC,YAAGU,GAAE,CAAC,MAAIC,GAAE,CAAC,KAAGD,GAAE,CAAC,IAAEM,MAAGL,GAAE,CAAC,IAAEK,MAAGN,GAAE,CAAC,IAAEO,MAAGN,GAAE,CAAC,IAAEM,GAAE;AAAS,QAAAL,KAAE,KAAK,IAAIF,GAAE,CAAC,GAAEC,GAAE,CAAC,CAAC,GAAEE,KAAE,KAAK,IAAIH,GAAE,CAAC,GAAEC,GAAE,CAAC,CAAC;AAAA,MAAC,OAAK;AAAC,YAAGD,GAAE,CAAC,MAAIC,GAAE,CAAC,KAAGD,GAAE,CAAC,IAAEM,MAAGL,GAAE,CAAC,IAAEK,MAAGN,GAAE,CAAC,IAAEO,MAAGN,GAAE,CAAC,IAAEM,GAAE;AAAS,QAAAL,KAAE,KAAK,IAAIF,GAAE,CAAC,GAAEC,GAAE,CAAC,CAAC,GAAEE,KAAE,KAAK,IAAIH,GAAE,CAAC,GAAEC,GAAE,CAAC,CAAC;AAAA,MAAC;AAAC,aAAKC,KAAEC,KAAG,CAAAC,KAAE,KAAK,OAAOF,KAAEM,MAAGX,GAAC,GAAEC,GAAEM,IAAEnB,KAAEM,KAAEF,GAAC,GAAEa,MAAGL;AAAE,MAAAQ,KAAE,KAAK,OAAOF,KAAEK,MAAGX,GAAC,GAAEQ,KAAED,MAAGN,GAAEO,IAAEpB,KAAEM,KAAEF,GAAC;AAAA,IAAC;AAAC,SAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,eAAO;AAAC,UAAG,KAAK,YAAU,KAAK,OAAM;AAAC,YAAG,KAAK,aAAY,KAAK,YAAU,KAAK,MAAM,QAAO;AAAK,aAAK,YAAU,KAAK,OAAM,KAAK,iBAAe,KAAK,YAAU,KAAG,KAAK;AAAA,MAAW;AAAC,UAAIE,MAAE,KAAK,YAAU,KAAK,SAAO,KAAK;AAAS,WAAK,iBAAe,KAAK,YAAU,MAAIA,OAAG,MAAG,KAAK;AAAQ,YAAME,MAAE,KAAK,YAAU,KAAK,SAAO,KAAK;AAAS,UAAIC,KAAET;AAAE,UAAG,KAAK,aAAY,KAAK,cAAY,EAAE,QAAO;AAAC,cAAMU,OAAG,KAAK,YAAUN,MAAEA,OAAGA,KAAEH,MAAG,KAAK,YAAUG,MAAEA,OAAGA;AAAE,QAAAK,MAAE,KAAK,kBAAgBH,MAAE,KAAK,SAAO,KAAK,eAAa,MAAG,GAAE,YAAYL,KAAEG,MAAEM,GAAC,KAAG,IAAE,GAAEV,MAAE,KAAK,kBAAgBQ,MAAE,KAAK,SAAO,KAAK,eAAa,MAAG,GAAE,YAAYP,KAAEG,MAAEM,MAAE,CAAC,KAAG,IAAE;AAAA,MAAC,MAAM,CAAAD,MAAE,KAAK,kBAAgB,KAAK,YAAUH,MAAE,KAAK,YAAUE,KAAER,MAAE,KAAK,kBAAgB,KAAK,YAAUM,MAAE,KAAK,YAAUE;AAAE,UAAG,CAAC,KAAK,sBAAoB,KAAK,iBAAiBC,KAAET,KAAE,KAAK,SAAS,EAAE,QAAO,KAAK,mBAAmB,aAAaS,KAAET,GAAC,GAAE,KAAK;AAAA,IAAkB;AAAA,EAAC;AAAA,EAAC,iBAAiBQ,KAAEC,KAAEC,KAAE;AAAC,UAAK,EAAC,OAAMV,IAAC,IAAEU;AAAE,QAAG,EAAE,KAAK,gBAAgB,EAAE,QAAOE,IAAEJ,KAAEC,KAAEC,GAAC;AAAE,UAAMT,KAAE,KAAK,sBAAqBC,KAAED,KAAEQ,MAAED,KAAEL,MAAE,KAAK,OAAOD,KAAE,KAAK,eAAa,KAAK,MAAM,GAAEE,MAAE,KAAK,iBAAiB,IAAID,GAAC;AAAE,QAAG,CAACC,IAAE,QAAM;AAAG,QAAIC,KAAEE,KAAEI,KAAEE,KAAEC,KAAEC,KAAE;AAAE,eAAUT,OAAKF,KAAE;AAAC,MAAAU,MAAER,IAAE,CAAC;AAAE,YAAMI,MAAEV,IAAEc,GAAC;AAAE,UAAGD,MAAEP,IAAE,CAAC,GAAED,MAAEK,IAAEG,MAAE,CAAC,GAAEN,MAAEG,IAAEG,GAAC,GAAEZ,IAAE;AAAC,YAAGI,IAAE,CAAC,IAAEI,OAAGF,IAAE,CAAC,IAAEE,IAAE;AAAS,QAAAE,OAAGJ,IAAE,CAAC,IAAEF,IAAE,CAAC,MAAII,MAAEJ,IAAE,CAAC,MAAIE,IAAE,CAAC,IAAEF,IAAE,CAAC,MAAIG,MAAEH,IAAE,CAAC;AAAA,MAAE,OAAK;AAAC,YAAGA,IAAE,CAAC,IAAEG,OAAGD,IAAE,CAAC,IAAEC,IAAE;AAAS,QAAAG,OAAGJ,IAAE,CAAC,IAAEF,IAAE,CAAC,MAAIG,MAAEH,IAAE,CAAC,MAAIE,IAAE,CAAC,IAAEF,IAAE,CAAC,MAAII,MAAEJ,IAAE,CAAC;AAAA,MAAE;AAAC,MAAAM,MAAE,IAAEI,OAAIA;AAAA,IAAG;AAAC,WAAO,MAAIA;AAAA,EAAC;AAAC;AAAC,SAASH,IAAEN,KAAEE,KAAEC,KAAE;AAAC,QAAK,EAAC,OAAMC,IAAC,IAAED;AAAE,MAAIT,KAAEC,IAAEC,IAAEC,MAAE;AAAE,aAAUC,OAAKM,KAAE;AAAC,IAAAV,MAAEI,IAAE;AAAO,aAAQK,MAAE,GAAEA,MAAET,KAAE,EAAES,KAAE;AAAC,UAAGR,KAAEG,IAAEK,MAAE,CAAC,GAAEP,KAAEE,IAAEK,GAAC,GAAER,GAAE,CAAC,IAAEO,OAAGN,GAAE,CAAC,IAAEM,IAAE;AAAS,OAACN,GAAE,CAAC,IAAED,GAAE,CAAC,MAAIO,MAAEP,GAAE,CAAC,MAAIC,GAAE,CAAC,IAAED,GAAE,CAAC,MAAIK,MAAEL,GAAE,CAAC,KAAG,IAAEE,QAAIA;AAAA,IAAG;AAAA,EAAC;AAAC,SAAO,MAAIA;AAAC;AAAC,SAASU,GAAEP,KAAEE,KAAEC,KAAEC,KAAE;AAAC,MAAIV,MAAEU,IAAE,IAAIJ,GAAC;AAAE,EAAAN,QAAIA,MAAE,CAAC,GAAEU,IAAE,IAAIJ,KAAEN,GAAC,IAAGA,IAAE,KAAK,CAACQ,KAAEC,GAAC,CAAC;AAAC;;;ACA/3L,IAAMe,MAAE;AAAK,IAAMC,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,IAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,IAAE,WAAS;AAAK,IAAMM,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYN,KAAEC,KAAEC,KAAE;AAAC,UAAMF,KAAE,MAAG,IAAE,GAAE,KAAK,eAAa,IAAIE,MAAE,KAAK,eAAa,WAASD,IAAE,eAAaA,IAAE,aAAY,KAAK,UAAQ,WAASA,IAAE,SAAOA,IAAE,SAAOC,MAAE,GAAE,KAAK,cAAYD,IAAE,YAAW,KAAK,YAAU,WAASA,IAAE,mBAAiBA,IAAE,mBAAiBC,MAAE,GAAE,KAAK,WAASJ,MAAEI;AAAA,EAAC;AAAA,EAAC,YAAYF,KAAE;AAAC,UAAMG,MAAE,KAAK;AAAU,QAAG,KAAK,gBAAc,EAAE,iBAAgB;AAAC,WAAI,KAAK,gBAAc,KAAK,gBAAcH,IAAE,QAAO,KAAK,cAAY,GAAE,KAAK,cAAY,OAAI,KAAK,cAAY,KAAK,iBAAe;AAAC,cAAME,MAAE,KAAK;AAAY,aAAK;AAAc,cAAMC,MAAEH,IAAEE,MAAE,CAAC,GAAEE,MAAEJ,IAAEE,GAAC,GAAEJ,MAAE,KAAK,aAAa,gBAAgBK,KAAEC,GAAC;AAAE,YAAGN,MAAE,KAAK,SAAS;AAAS,cAAMC,MAAE,MAAG,KAAK,YAAUD,KAAE,CAACO,KAAEE,GAAC,IAAE,KAAK,aAAa,WAAWJ,KAAEC,KAAEL,GAAC,GAAES,KAAEC,GAAEN,KAAEC,KAAEL,GAAC;AAAE,eAAO,KAAK,kBAAkB,aAAaS,GAAE,CAAC,IAAE,KAAK,UAAQD,KAAEC,GAAE,CAAC,IAAE,KAAK,UAAQH,GAAC,GAAE,KAAK,gBAAc,KAAK,kBAAkB,YAAYA,KAAEE,GAAC,GAAE,KAAK;AAAA,MAAiB;AAAC,aAAO,KAAK,cAAY,OAAG;AAAA,IAAI;AAAC,SAAK,gBAAc,EAAE,WAASG,GAAEV,GAAC;AAAE,UAAMF,MAAE,KAAK,OAAOE,KAAEG,GAAC;AAAE,WAAO,KAAK,gBAAc,EAAE,WAASO,GAAEV,GAAC,GAAEF;AAAA,EAAC;AAAA,EAAC,OAAOE,KAAEE,KAAE;AAAC,QAAIC,KAAEL,MAAE;AAAG,YAAO,KAAK,aAAY;AAAA,MAAC,KAAK,EAAE;AAAA,MAAW;AAAQ,QAAAK,MAAE,KAAK,aAAa,oBAAoBH,GAAC,IAAE,IAAEE;AAAE;AAAA,MAAM,KAAK,EAAE;AAAc,QAAAC,MAAED;AAAE;AAAA,MAAM,KAAK,EAAE;AAAQ,QAAAC,MAAED,KAAEJ,MAAE;AAAA,IAAE;AAAC,UAAMC,MAAEC,IAAE;AAAO,QAAIK,KAAEE,MAAE,GAAEC,KAAER,IAAE,CAAC;AAAE,aAAQI,MAAE,GAAEA,MAAEL,KAAE,EAAEK,KAAE;AAAC,MAAAC,MAAEG,IAAEA,KAAER,IAAEI,GAAC;AAAE,YAAMF,MAAE,KAAK,aAAa,gBAAgBG,KAAEG,EAAC;AAAE,UAAGD,MAAEL,MAAEC,KAAE;AAAC,cAAMH,OAAGG,MAAEI,OAAGL,KAAE,CAACE,KAAEL,GAAC,IAAE,KAAK,aAAa,WAAWM,KAAEG,IAAER,GAAC,GAAEW,MAAEF,GAAEJ,KAAEG,IAAER,GAAC,GAAEY,MAAEd,MAAE,CAAC,KAAK,UAAQ,KAAK;AAAQ,eAAO,KAAK,kBAAkB,aAAaa,IAAE,CAAC,IAAEC,MAAEb,KAAEY,IAAE,CAAC,IAAEC,MAAER,GAAC,GAAE,KAAK,iBAAeN,MAAE,KAAK,kBAAkB,YAAY,CAACM,KAAE,CAACL,GAAC,IAAE,KAAK,kBAAkB,YAAYK,KAAEL,GAAC,IAAG,KAAK;AAAA,MAAiB;AAAC,MAAAQ,OAAGL;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAC;;;ACA5yD,IAAMW,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQC,KAAEC,KAAEC,KAAEC,KAAEJ,KAAE;AAAC,WAAO,IAAIK,IAAEJ,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACH,IAAE,WAAS;AAAK,IAAMM,MAAE;AAAM,IAAMD,MAAN,cAAgBE,GAAC;AAAA,EAAC,YAAYN,KAAEC,KAAEC,KAAE;AAAC,UAAMF,KAAE,MAAG,IAAE,GAAE,KAAK,eAAa,IAAID,MAAE,KAAK,eAAa,WAASE,IAAE,eAAaA,IAAE,aAAY,KAAK,UAAQ,WAASA,IAAE,SAAOA,IAAE,SAAOC,MAAE,GAAE,KAAK,aAAW,WAASD,IAAE,oBAAkBA,IAAE,kBAAiB,KAAK,iBAAe,WAASA,IAAE,wBAAsBA,IAAE,sBAAqB,KAAK,mBAAiB,WAASA,IAAE,0BAAwBA,IAAE,wBAAuB,KAAK,QAAM,CAAC,GAAE,KAAK,eAAa;AAAA,EAAC;AAAA,EAAC,YAAYD,KAAE;AAAC,QAAG,KAAK,gBAAc,KAAK,aAAaA,GAAC,GAAE,KAAK,cAAY,OAAI,KAAK,gBAAc,KAAK,MAAM,OAAO,QAAO,KAAK,MAAM,SAAO,GAAE,KAAK,eAAa,GAAE,KAAK,cAAY,OAAG;AAAK,UAAMC,MAAE,KAAK,MAAM,KAAK,YAAY;AAAE,SAAK,gBAAc,KAAK,kBAAkB,UAAUA,IAAE,CAAC,CAAC;AAAE,QAAIC,MAAED,IAAE,CAAC,GAAEE,MAAEF,IAAE,CAAC;AAAE,QAAG,MAAI,KAAK,SAAQ;AAAC,YAAMD,MAAE,KAAK,IAAIC,IAAE,CAAC,CAAC,GAAEF,MAAE,KAAK,IAAIE,IAAE,CAAC,CAAC;AAAE,MAAAC,OAAG,KAAK,UAAQH,KAAEI,OAAG,KAAK,UAAQH;AAAA,IAAC;AAAC,WAAO,KAAK,kBAAkB,aAAaE,KAAEC,GAAC,GAAE,KAAK,gBAAe,KAAK;AAAA,EAAiB;AAAA,EAAC,aAAaH,KAAE;AAAC,SAAK,MAAM,SAAO,GAAE,KAAK,eAAa;AAAE,UAAMG,MAAEI,GAAEP,GAAC,GAAED,MAAEC,IAAE,SAAO;AAAE,QAAIK,KAAED,KAAEI,KAAE,GAAEC,MAAE,GAAEC,KAAE,GAAEC,MAAE,GAAEC,MAAE;AAAE,WAAKJ,KAAET,OAAG;AAAC,MAAAS,MAAIH,MAAEL,IAAEQ,KAAE,CAAC,GAAEJ,MAAEJ,IAAEQ,EAAC;AAAE,YAAMP,MAAEY,GAAER,GAAC,GAAES,MAAED,GAAET,GAAC;AAAE,OAAC,KAAK,gBAAc,MAAI,KAAK,aAAWO,MAAE,KAAK,aAAa,SAASN,KAAED,KAAE,CAAC,IAAG,MAAII,KAAEL,OAAGM,MAAEE,KAAED,KAAET,QAAI,KAAK,cAAY,KAAK,kBAAgB,MAAIA,QAAI,KAAK,MAAM,KAAK,CAACI,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEM,GAAC,CAAC,IAAE,MAAIV,MAAE,KAAK,kBAAgB,KAAK,MAAM,KAAK,CAACI,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEU,IAAEH,KAAED,GAAC,CAAC,CAAC,IAAE,KAAK,oBAAkB,KAAK,MAAM,KAAK,CAACN,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEU,IAAEH,KAAED,GAAC,CAAC,CAAC,IAAG,KAAK,gBAAc,MAAI,KAAK,aAAWC,MAAE,KAAK,aAAa,SAASP,KAAED,KAAE,CAAC,IAAGI,OAAIT,QAAII,MAAE,MAAIW,OAAG,MAAIJ,KAAE,KAAK,kBAAgB,KAAK,MAAM,KAAK,CAACN,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEW,IAAEH,KAAEH,GAAC,CAAC,CAAC,IAAE,KAAK,oBAAkB,KAAK,MAAM,KAAK,CAACL,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEW,IAAEH,KAAEH,GAAC,CAAC,CAAC,KAAG,KAAK,cAAY,KAAK,kBAAgB,MAAIK,QAAI,KAAK,MAAM,KAAK,CAACV,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEQ,GAAC,CAAC;AAAA,IAAE;AAAC,SAAK,eAAa;AAAA,EAAC;AAAC;AAAC,SAASG,IAAEf,KAAEC,KAAE;AAAC,QAAMC,MAAE,KAAK;AAAG,SAAK,KAAK,IAAID,MAAED,GAAC,IAAEE,MAAE,IAAEG,MAAG,CAAAJ,MAAED,MAAEE,MAAED,OAAG,IAAEC,MAAED,OAAG,IAAEC;AAAE,UAAOF,MAAEC,OAAG;AAAC;;;ACAthE,IAAMe,MAAN,MAAO;AAAA,EAAC,YAAYA,MAAEC,KAAE;AAAC,SAAK,QAAM,CAAC,GAAE,KAAK,WAASD;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,MAAM;AAAA,EAAM;AAAA,EAAC,QAAQA,KAAE;AAAC,QAAG,QAAMA,IAAE;AAAO,UAAK,EAAC,OAAMC,KAAE,UAASC,IAAC,IAAE;AAAK,IAAAD,IAAE,KAAKD,GAAC;AAAE,QAAIG,MAAEF,IAAE,SAAO,MAAI;AAAE,UAAMG,MAAEH,IAAEE,GAAC;AAAE,WAAKA,MAAE,KAAG;AAAC,YAAMH,MAAEG,MAAE,KAAG,GAAEE,MAAEJ,IAAED,GAAC;AAAE,UAAG,EAAEE,IAAEG,KAAED,GAAC,KAAG,GAAG;AAAM,MAAAH,IAAED,GAAC,IAAEI,KAAEH,IAAEE,GAAC,IAAEE,KAAEF,MAAEH;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAK,EAAC,OAAMA,KAAE,UAASC,IAAC,IAAE,MAAKC,MAAEF,IAAE,CAAC,GAAEG,MAAEH,IAAE,IAAI;AAAE,QAAG,MAAIA,IAAE,OAAO,QAAOE;AAAE,IAAAF,IAAE,CAAC,IAAEG;AAAE,QAAIC,MAAE;AAAE,UAAMC,MAAEL,IAAE,QAAOM,MAAEN,IAAE,CAAC;AAAE,QAAIO,KAAEC,KAAEC,MAAE;AAAK,eAAO;AAAC,YAAMP,MAAE,IAAEE,MAAE,GAAED,MAAE,IAAEC,MAAE;AAAE,UAAGK,MAAE,MAAKP,MAAEG,QAAIE,MAAEP,IAAEE,GAAC,GAAED,IAAEM,KAAED,GAAC,IAAE,MAAIG,MAAEP,OAAIC,MAAEE,QAAIG,MAAER,IAAEG,GAAC,IAAG,SAAOM,OAAGR,IAAEO,KAAEF,GAAC,KAAG,KAAG,SAAOG,OAAGR,IAAEO,KAAED,GAAC,KAAG,OAAKE,MAAEN,OAAI,SAAOM,IAAE;AAAM,MAAAT,IAAEI,GAAC,IAAEJ,IAAES,GAAC,GAAET,IAAES,GAAC,IAAEH,KAAEF,MAAEK;AAAA,IAAC;AAAC,WAAOP;AAAA,EAAC;AAAC;AAAC,IAAMD,MAAE,CAACD,KAAEC,QAAID,MAAEC,MAAE,KAAGD,MAAEC,MAAE,IAAE;;;ACA9L,IAAM,IAAE,MAAI;AAAW,SAASS,IAAEC,KAAE;AAAC,QAAK,EAAC,OAAMC,IAAC,IAAED;AAAE,MAAG,CAACC,OAAG,MAAIA,IAAE,OAAO,QAAO;AAAK,QAAMC,MAAEC,GAAEJ,GAAE,GAAEC,GAAC;AAAE,MAAG,CAACE,IAAE,QAAO;AAAK,QAAMC,MAAE,KAAG,KAAK,IAAID,IAAE,CAAC,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,CAAC,IAAE,KAAG;AAAE,MAAIE,MAAE,GAAEC,MAAE;AAAE,WAAQC,MAAE,GAAEA,MAAEL,IAAE,QAAOK,OAAI;AAAC,UAAMN,MAAE,EAAEC,IAAEK,GAAC,CAAC;AAAE,IAAAN,MAAEK,QAAIA,MAAEL,KAAEI,MAAEE;AAAA,EAAE;AAAC,MAAG,KAAK,IAAID,GAAC,KAAG,IAAEF,MAAEA,KAAE;AAAC,UAAMH,MAAEO,GAAER,GAAE,GAAEE,IAAEG,GAAC,CAAC;AAAE,WAAM,EAAEJ,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,IAAGA,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,CAAC;AAAA,EAAC;AAAC,QAAMD,MAAE,EAAEE,IAAEG,GAAC,GAAE,OAAGL,GAAE,CAAC;AAAE,MAAG,SAAOA,IAAE,QAAO;AAAK,MAAG,MAAIE,IAAE,UAAQA,IAAE,CAAC,EAAE,SAAO,EAAE,QAAOF;AAAE,QAAMS,KAAE,CAAC,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC,GAAEC,KAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAEC,KAAE,CAAC,KAAI,KAAI,KAAI,GAAG;AAAE,MAAIC,KAAE,OAAGC,KAAEC,GAAEd,KAAEC,KAAE,IAAE;AAAE,QAAIY,GAAE,aAAWD,KAAE,MAAGH,GAAE,CAAC,EAAE,CAAC,IAAET,IAAE,CAAC,GAAES,GAAE,CAAC,EAAE,CAAC,IAAET,IAAE,CAAC,GAAEa,KAAEC,GAAEd,KAAEC,KAAE,KAAE,IAAGS,GAAE,CAAC,IAAEG,GAAE,UAASF,GAAE,CAAC,IAAE;AAAE,QAAMI,KAAE,CAAC,KAAI,GAAG;AAAE,MAAIC,KAAE,OAAGC,KAAE,MAAIC,KAAE;AAAG,QAAMC,KAAEX,GAAER,GAAE,GAAEE,IAAEG,GAAC,CAAC;AAAE,MAAIe,KAAE;AAAI,KAAE;AAAC,QAAGA,KAAE,KAAIX,GAAE,CAAC,IAAED,GAAEP,KAAEoB,GAAEF,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEF,EAAC,GAAEb,KAAED,GAAC,GAAE,MAAMM,GAAE,CAAC,EAAE,CAAC,CAAC,KAAG,MAAMA,GAAE,CAAC,EAAE,CAAC,CAAC,MAAII,KAAEC,GAAEL,GAAE,CAAC,GAAER,KAAE,KAAE,GAAEmB,KAAEP,GAAE,WAAU,CAAC,MAAMO,EAAC,KAAGA,KAAEhB,OAAGkB,GAAEb,GAAE,CAAC,GAAER,GAAC,EAAE,CAAAe,KAAE,MAAGN,GAAE,CAAC,IAAEU,IAAET,GAAE,CAAC,IAAEY,GAAEd,GAAE,CAAC,GAAET,GAAC;AAAA,aAAU,CAAC,MAAMoB,EAAC,KAAGA,KAAEF,OAAIA,KAAEE,IAAEL,GAAE,CAAC,IAAEN,GAAE,CAAC,EAAE,CAAC,GAAEM,GAAE,CAAC,IAAEN,GAAE,CAAC,EAAE,CAAC,IAAGQ,MAAG,MAAIA,KAAE,KAAG;AAAC,UAAG,EAAEC,MAAG,GAAG;AAAM,MAAAF,KAAE,MAAGN,GAAE,CAAC,IAAEQ,IAAET,GAAE,CAAC,EAAE,CAAC,IAAEM,GAAE,CAAC,GAAEN,GAAE,CAAC,EAAE,CAAC,IAAEM,GAAE,CAAC,GAAEJ,GAAE,CAAC,IAAEY,GAAEd,GAAE,CAAC,GAAET,GAAC;AAAA,IAAC;AAAA,EAAC,SAAO,CAACgB;AAAG,EAAAA,KAAE,OAAGC,KAAE,KAAGC,KAAE;AAAG,MAAIM,KAAE,MAAIC,KAAE;AAAE,KAAE;AAAC,QAAGL,KAAE,KAAIX,GAAE,CAAC,IAAED,GAAEP,KAAEoB,GAAEF,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEF,EAAC,GAAEb,KAAED,GAAC,GAAE,MAAMM,GAAE,CAAC,EAAE,CAAC,CAAC,KAAG,MAAMA,GAAE,CAAC,EAAE,CAAC,CAAC,MAAII,KAAEC,GAAEL,GAAE,CAAC,GAAER,KAAE,KAAE,GAAEmB,KAAEP,GAAE,WAAU,CAAC,MAAMO,EAAC,KAAGA,KAAEhB,OAAGkB,GAAEb,GAAE,CAAC,GAAER,GAAC,EAAE,CAAAe,KAAE,MAAGN,GAAE,CAAC,IAAEU,IAAET,GAAE,CAAC,IAAEY,GAAEd,GAAE,CAAC,GAAET,GAAC;AAAA,aAAU,CAAC,MAAMoB,EAAC,KAAGA,KAAEF,GAAE,CAAAA,KAAEE,IAAEL,GAAE,CAAC,IAAEN,GAAE,CAAC,EAAE,CAAC,GAAEM,GAAE,CAAC,IAAEN,GAAE,CAAC,EAAE,CAAC;AAAA,aAAUW,KAAEF,OAAIA,KAAEE,IAAEL,GAAE,CAAC,IAAEN,GAAE,CAAC,EAAE,CAAC,GAAEM,GAAE,CAAC,IAAEN,GAAE,CAAC,EAAE,CAAC,IAAGQ,KAAE,MAAGO,KAAEC,IAAED,MAAG,MAAIC,MAAG,IAAGR,KAAE,OAAIA,KAAE,KAAG;AAAC,UAAG,EAAEC,MAAG,GAAG;AAAM,MAAAF,KAAE,MAAGN,GAAE,CAAC,IAAEQ,IAAET,GAAE,CAAC,EAAE,CAAC,IAAEM,GAAE,CAAC,GAAEN,GAAE,CAAC,EAAE,CAAC,IAAEM,GAAE,CAAC,GAAEJ,GAAE,CAAC,IAAEY,GAAEd,GAAE,CAAC,GAAET,GAAC;AAAA,IAAC;AAAA,EAAC,SAAO,CAACgB;AAAG,EAAAA,KAAE,OAAGC,KAAE,MAAIC,KAAE;AAAG,KAAE;AAAC,QAAGE,KAAE,KAAIX,GAAE,CAAC,IAAED,GAAEP,KAAEoB,GAAEF,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEF,EAAC,GAAEb,KAAED,GAAC,GAAE,MAAMM,GAAE,CAAC,EAAE,CAAC,CAAC,KAAG,MAAMA,GAAE,CAAC,EAAE,CAAC,CAAC,MAAII,KAAEC,GAAEL,GAAE,CAAC,GAAER,KAAE,KAAE,GAAEmB,KAAEP,GAAE,WAAU,CAAC,MAAMO,EAAC,KAAGA,KAAEhB,OAAGkB,GAAEb,GAAE,CAAC,GAAER,GAAC,EAAE,CAAAe,KAAE,MAAGN,GAAE,CAAC,IAAEU,IAAET,GAAE,CAAC,IAAEY,GAAEd,GAAE,CAAC,GAAET,GAAC;AAAA,aAAUoB,KAAEF,OAAIA,KAAEE,IAAEL,GAAE,CAAC,IAAEN,GAAE,CAAC,EAAE,CAAC,GAAEM,GAAE,CAAC,IAAEN,GAAE,CAAC,EAAE,CAAC,IAAGQ,MAAG,MAAIA,KAAE,KAAG;AAAC,UAAG,EAAEC,MAAG,GAAG;AAAM,MAAAF,KAAE,MAAGN,GAAE,CAAC,IAAEQ,IAAET,GAAE,CAAC,EAAE,CAAC,IAAEM,GAAE,CAAC,GAAEN,GAAE,CAAC,EAAE,CAAC,IAAEM,GAAE,CAAC,GAAEJ,GAAE,CAAC,IAAEY,GAAEd,GAAE,CAAC,GAAET,GAAC;AAAA,IAAC;AAAA,EAAC,SAAO,CAACgB;AAAG,QAAMU,KAAE,CAAC,GAAE,GAAE,GAAE,CAAC,GAAEC,KAAEf,KAAE,IAAE;AAAE,MAAIgB;AAAE,WAAQrB,MAAEoB,IAAEpB,MAAE,GAAEA,MAAI,UAAQN,MAAE0B,IAAE1B,MAAE,GAAEA,OAAI;AAAC,UAAMC,MAAES,GAAEV,GAAC,GAAEM,MAAEI,GAAEV,MAAE,CAAC;AAAE,MAAEC,KAAEK,GAAC,IAAE,MAAIqB,KAAEF,GAAEzB,GAAC,GAAEyB,GAAEzB,GAAC,IAAEyB,GAAEzB,MAAE,CAAC,GAAEyB,GAAEzB,MAAE,CAAC,IAAE2B,IAAEjB,GAAEV,GAAC,IAAEM,KAAEI,GAAEV,MAAE,CAAC,IAAEC;AAAA,EAAE;AAAC,MAAI2B,KAAEF,IAAEG,KAAE,GAAEC,KAAE;AAAE,WAAQxB,MAAEoB,IAAEpB,MAAE,GAAEA,OAAI;AAAC,YAAOA,KAAE;AAAA,MAAC,KAAK;AAAE,QAAAwB,KAAE,IAAErB,GAAEgB,GAAEnB,GAAC,CAAC;AAAE;AAAA,MAAM,KAAK;AAAE,QAAAwB,KAAE,aAAWrB,GAAEgB,GAAEnB,GAAC,CAAC;AAAE;AAAA,MAAM,KAAK;AAAE,QAAAwB,KAAE,aAAWrB,GAAEgB,GAAEnB,GAAC,CAAC;AAAE;AAAA,MAAM,KAAK;AAAE,QAAAwB,KAAErB,GAAEgB,GAAEnB,GAAC,CAAC;AAAA,IAAC;AAAC,IAAAwB,KAAED,OAAIA,KAAEC,IAAEF,KAAEH,GAAEnB,GAAC;AAAA,EAAE;AAAC,SAAOE,GAAEoB,EAAC;AAAC;AAAC,SAASP,GAAErB,KAAEC,KAAE;AAAC,QAAK,EAAC,OAAMK,IAAC,IAAEL;AAAE,MAAI8B,MAAE;AAAE,aAAUC,OAAK1B,KAAE;AAAC,UAAML,MAAE+B,IAAE;AAAO,aAAQ1B,MAAE,GAAEA,MAAEL,KAAE,EAAEK,KAAE;AAAC,YAAML,MAAE+B,IAAE1B,MAAE,CAAC,GAAE2B,MAAED,IAAE1B,GAAC;AAAE,UAAGL,IAAE,CAAC,IAAED,IAAE,CAAC,KAAGiC,IAAE,CAAC,IAAEjC,IAAE,CAAC,EAAE;AAAS,OAACiC,IAAE,CAAC,IAAEhC,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAIgC,IAAE,CAAC,IAAEhC,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,KAAG,IAAE8B,QAAIA;AAAA,IAAG;AAAA,EAAC;AAAC,SAAO,MAAIA;AAAC;AAAC,SAASlB,GAAEb,KAAEC,KAAEK,KAAE;AAAC,MAAGA,OAAGe,GAAErB,KAAEC,GAAC,EAAE,QAAM,EAAC,OAAMD,KAAE,UAAS,EAAC;AAAE,MAAI+B,MAAE,IAAE,GAAEC,MAAE,GAAEC,MAAE;AAAE,QAAM/B,MAAE,CAAC,GAAE,CAAC,GAAE,EAAC,OAAMgC,IAAC,IAAEjC;AAAE,aAAUG,OAAK8B,IAAE,KAAG,EAAE9B,IAAE,SAAO,GAAG,UAAQH,MAAE,GAAEA,MAAEG,IAAE,SAAO,GAAEH,OAAI;AAAC,MAAEC,KAAEF,KAAEI,KAAEH,GAAC;AAAE,UAAMK,MAAEgB,GAAEtB,KAAEE,GAAC;AAAE,IAAAI,MAAEyB,QAAIA,MAAEzB,KAAE0B,MAAE9B,IAAE,CAAC,GAAE+B,MAAE/B,IAAE,CAAC;AAAA,EAAE;AAAC,SAAM,EAAC,OAAM,CAAC8B,KAAEC,GAAC,GAAE,UAAS,KAAK,KAAKF,GAAC,EAAC;AAAC;AAAC,SAASxB,GAAEP,KAAEC,KAAE8B,KAAEC,KAAE;AAAC,QAAMC,MAAE,CAAChC,KAAE,CAAC;AAAE,MAAIC,MAAE,IAAE,GAAEgC,MAAE,IAAE,GAAE/B,MAAE,OAAGC,MAAE;AAAG,QAAM+B,KAAE,CAAC,CAAClC,KAAE+B,IAAE,CAAC,IAAE,CAAC,GAAE,CAAC/B,KAAE+B,IAAE,CAAC,IAAE,CAAC,CAAC,GAAEjC,MAAE,CAAC,GAAE,CAAC,GAAEsB,KAAE,CAAC,GAAE,CAAC,GAAER,KAAE,CAAC,GAAE,CAAC,GAAEN,MAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAEV,GAAE,GAAE,EAAC,OAAMW,GAAC,IAAEV;AAAE,aAAUM,OAAKI,GAAE,KAAG,EAAEJ,IAAE,SAAO,GAAG,UAAQN,MAAE,GAAEA,MAAEM,IAAE,QAAON,OAAI;AAAC,QAAGO,IAAE,CAAC,EAAE,CAAC,IAAED,IAAEN,MAAE,CAAC,EAAE,CAAC,GAAEO,IAAE,CAAC,EAAE,CAAC,IAAED,IAAEN,MAAE,CAAC,EAAE,CAAC,GAAEO,IAAE,CAAC,EAAE,CAAC,IAAED,IAAEN,GAAC,EAAE,CAAC,GAAEO,IAAE,CAAC,EAAE,CAAC,IAAED,IAAEN,GAAC,EAAE,CAAC,GAAE,SAAOQ,GAAEC,IAAEF,GAAC,EAAE;AAAS,QAAGc,GAAE,CAAC,IAAEc,GAAE,CAAC,EAAE,CAAC,GAAEd,GAAE,CAAC,IAAEc,GAAE,CAAC,EAAE,CAAC,GAAEtB,GAAE,CAAC,IAAEsB,GAAE,CAAC,EAAE,CAAC,GAAEtB,GAAE,CAAC,IAAEsB,GAAE,CAAC,EAAE,CAAC,GAAE,MAAIrB,GAAEL,IAAEY,IAAER,EAAC,EAAE;AAAS,QAAG,CAAC,EAAEsB,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE5B,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAER,GAAC,EAAE;AAAS,UAAME,MAAEF,IAAE,CAAC;AAAE,IAAAG,MAAEgC,MAAEjC,MAAEC,QAAIA,MAAED,KAAEE,MAAE,QAAIF,MAAEiC,QAAIA,MAAEjC,KAAEG,MAAE;AAAA,EAAG;AAAC,SAAOD,OAAGC,MAAE6B,IAAE,CAAC,KAAG/B,MAAEgC,OAAG,IAAED,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAE,KAAIA;AAAC;AAAC,SAASzB,GAAER,KAAEC,KAAE;AAAC,MAAGA,IAAE,SAAO,EAAE,QAAO;AAAK,EAAAD,QAAIA,MAAED,GAAE;AAAG,QAAK,CAACgC,KAAEC,GAAC,IAAE/B,IAAE,CAAC,GAAE,CAACgC,KAAE/B,GAAC,IAAED,IAAE,CAAC;AAAE,SAAOD,IAAE,CAAC,IAAE,KAAK,IAAI+B,KAAEE,GAAC,GAAEjC,IAAE,CAAC,IAAE,KAAK,IAAIgC,KAAE9B,GAAC,GAAEF,IAAE,CAAC,IAAE,KAAK,IAAI+B,KAAEE,GAAC,GAAEjC,IAAE,CAAC,IAAE,KAAK,IAAIgC,KAAE9B,GAAC,GAAEF;AAAC;AAAC,IAAMS,KAAE;AAAR,IAAUC,KAAE;AAAZ,IAAcC,KAAE;AAAhB,IAAkBC,KAAE;AAAG,SAASE,GAAEd,KAAEC,KAAEK,KAAE;AAAC,MAAIyB,MAAEhB,GAAEd,KAAED,GAAC,GAAEgC,MAAEjB,GAAET,KAAEN,GAAC;AAAE,QAAMiC,MAAEjC,IAAE,CAAC,GAAEE,MAAEF,IAAE,CAAC,GAAEkC,MAAElC,IAAE,CAAC,GAAEG,MAAEH,IAAE,CAAC;AAAE,MAAG+B,MAAEC,IAAE,QAAO;AAAE,MAAG,EAAED,MAAEC,KAAG,QAAO;AAAE,QAAM5B,OAAG2B,MAAE,IAAE,MAAIC,MAAE,IAAE;AAAG,KAAE;AAAC,UAAM5B,MAAEE,IAAE,CAAC,IAAEL,IAAE,CAAC,GAAEI,MAAEC,IAAE,CAAC,IAAEL,IAAE,CAAC;AAAE,QAAGG,MAAEC,IAAE,CAAA0B,MAAEpB,MAAGoB,MAAEtB,MAAGR,IAAE,CAAC,KAAGI,OAAG4B,MAAEhC,IAAE,CAAC,KAAGG,KAAEH,IAAE,CAAC,IAAEgC,QAAIhC,IAAE,CAAC,KAAGI,OAAG6B,MAAEjC,IAAE,CAAC,KAAGG,KAAEH,IAAE,CAAC,IAAEiC,MAAGH,MAAEhB,GAAEd,KAAED,GAAC,KAAGgC,MAAErB,MAAGqB,MAAEvB,MAAGH,IAAE,CAAC,KAAGD,OAAG4B,MAAE3B,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,IAAE2B,QAAI3B,IAAE,CAAC,KAAGD,OAAG6B,MAAE5B,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,IAAE4B,MAAGF,MAAEjB,GAAET,KAAEN,GAAC,KAAG+B,OAAGA,MAAErB,MAAGT,IAAE,CAAC,KAAGG,OAAGF,MAAED,IAAE,CAAC,KAAGI,KAAEJ,IAAE,CAAC,IAAEC,QAAID,IAAE,CAAC,KAAGG,OAAGD,MAAEF,IAAE,CAAC,KAAGI,KAAEJ,IAAE,CAAC,IAAEE,MAAG4B,MAAEhB,GAAEd,KAAED,GAAC,MAAIgC,MAAEtB,MAAGJ,IAAE,CAAC,KAAGF,OAAGF,MAAEI,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,IAAEJ,QAAII,IAAE,CAAC,KAAGF,OAAGD,MAAEG,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,IAAEH,MAAG6B,MAAEjB,GAAET,KAAEN,GAAC;AAAA,aAAW+B,MAAEnB,MAAGmB,MAAErB,MAAGT,IAAE,CAAC,KAAGG,OAAGF,MAAED,IAAE,CAAC,KAAGI,KAAEJ,IAAE,CAAC,IAAEC,QAAID,IAAE,CAAC,KAAGG,OAAGD,MAAEF,IAAE,CAAC,KAAGI,KAAEJ,IAAE,CAAC,IAAEE,MAAG4B,MAAEhB,GAAEd,KAAED,GAAC,KAAGgC,MAAEpB,MAAGoB,MAAEtB,MAAGJ,IAAE,CAAC,KAAGF,OAAGF,MAAEI,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,IAAEJ,QAAII,IAAE,CAAC,KAAGF,OAAGD,MAAEG,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,IAAEH,MAAG6B,MAAEjB,GAAET,KAAEN,GAAC,KAAG+B,OAAGA,MAAEtB,MAAGR,IAAE,CAAC,KAAGI,OAAG4B,MAAEhC,IAAE,CAAC,KAAGG,KAAEH,IAAE,CAAC,IAAEgC,QAAIhC,IAAE,CAAC,KAAGI,OAAG6B,MAAEjC,IAAE,CAAC,KAAGG,KAAEH,IAAE,CAAC,IAAEiC,MAAGH,MAAEhB,GAAEd,KAAED,GAAC,MAAIgC,MAAEvB,MAAGH,IAAE,CAAC,KAAGD,OAAG4B,MAAE3B,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,IAAE2B,QAAI3B,IAAE,CAAC,KAAGD,OAAG6B,MAAE5B,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,IAAE4B,MAAGF,MAAEjB,GAAET,KAAEN,GAAC,IAAG+B,MAAEC,IAAE,QAAO;AAAA,EAAC,SAAOD,MAAEC;AAAG,SAAO5B;AAAC;AAAC,SAASW,GAAEf,KAAEC,KAAE;AAAC,UAAOD,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAE,IAAE,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAE,IAAE,MAAI,KAAGD,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAE,IAAE,MAAI,KAAGD,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAE,IAAE,MAAI;AAAC;AAAC,SAASmB,GAAEpB,KAAEC,KAAEK,KAAE;AAAC,SAAON,OAAGC,MAAED,OAAGM;AAAC;AAAC,SAASgB,GAAEtB,KAAEC,KAAE;AAAC,UAAOD,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC;AAAE;AAAC,SAAS,EAAED,KAAEC,KAAE;AAAC,MAAGD,MAAEC,IAAE,QAAM;AAAG,MAAGD,MAAEC,IAAE,QAAO;AAAE,MAAGD,QAAIC,IAAE,QAAO;AAAE,QAAMK,MAAE,MAAMN,GAAC,GAAE+B,MAAE,MAAM9B,GAAC;AAAE,SAAOK,MAAEyB,MAAE,KAAGzB,MAAEyB,MAAE,IAAE;AAAC;AAAC,IAAMf,KAAN,MAAO;AAAA,EAAC,YAAYhB,KAAEC,KAAEK,KAAEyB,KAAE;AAAC,SAAK,IAAE/B,KAAE,KAAK,IAAEC,KAAE,KAAK,WAASK,KAAE,KAAK,yBAAuBJ,GAAEF,KAAEC,KAAE8B,GAAC,GAAE,KAAK,uBAAqB,KAAK,yBAAuB,KAAK,WAAS,KAAK;AAAA,EAAK;AAAC;AAAC,IAAMd,KAAE;AAAR,IAAU,IAAE;AAAI,SAASE,GAAEY,KAAE;AAAC,MAAG,CAACA,OAAG,CAACA,IAAE,SAAO,MAAIA,IAAE,MAAM,OAAO,QAAO;AAAK,QAAME,MAAE1B,GAAER,GAAE,GAAEgC,IAAE,MAAM,CAAC,CAAC;AAAE,MAAG,CAACE,IAAE,QAAO;AAAK,QAAMC,MAAED,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAE9B,MAAE8B,IAAE,CAAC,IAAEA,IAAE,CAAC;AAAE,MAAG,MAAIC,OAAG,MAAI/B,IAAE,QAAM,CAAC8B,IAAE,CAAC,IAAEC,MAAE,GAAED,IAAE,CAAC,IAAE9B,MAAE,CAAC;AAAE,QAAMC,MAAE,KAAK,IAAI,KAAK,IAAI8B,KAAE/B,GAAC,IAAE,GAAEc,EAAC,GAAEZ,MAAE,IAAIL,IAAG,CAACA,KAAEC,QAAIA,IAAE,uBAAqBD,IAAE,oBAAqB,GAAEmC,KAAE,KAAK,IAAID,KAAE/B,GAAC;AAAE,MAAIJ,MAAEoC,KAAE,GAAEd,KAAE,GAAER,KAAE;AAAE,OAAIQ,KAAEY,IAAE,CAAC,GAAEZ,KAAEY,IAAE,CAAC,GAAEZ,MAAGc,GAAE,MAAItB,KAAEoB,IAAE,CAAC,GAAEpB,KAAEoB,IAAE,CAAC,GAAEpB,MAAGsB,GAAE,CAAA9B,IAAE,QAAQ,IAAIW,GAAEK,KAAEtB,KAAEc,KAAEd,KAAEA,KAAEgC,GAAC,CAAC;AAAE,QAAMxB,MAAE0B,GAAEF,IAAE,OAAM,KAAE;AAAE,MAAG,SAAOxB,IAAE,QAAO;AAAK,MAAIC,IAAEC,KAAE,IAAIO,GAAET,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,GAAEwB,GAAC;AAAE,SAAK1B,IAAE,OAAK,IAAG,CAAAG,KAAE,EAAEH,IAAE,QAAQ,CAAC,GAAEG,GAAE,yBAAuBC,GAAE,2BAAyBA,KAAED,KAAGA,GAAE,uBAAqBC,GAAE,0BAAwBL,QAAIL,MAAES,GAAE,WAAS,GAAEH,IAAE,QAAQ,IAAIW,GAAER,GAAE,IAAET,KAAES,GAAE,IAAET,KAAEA,KAAEgC,GAAC,CAAC,GAAE1B,IAAE,QAAQ,IAAIW,GAAER,GAAE,IAAET,KAAES,GAAE,IAAET,KAAEA,KAAEgC,GAAC,CAAC,GAAE1B,IAAE,QAAQ,IAAIW,GAAER,GAAE,IAAET,KAAES,GAAE,IAAET,KAAEA,KAAEgC,GAAC,CAAC,GAAE1B,IAAE,QAAQ,IAAIW,GAAER,GAAE,IAAET,KAAES,GAAE,IAAET,KAAEA,KAAEgC,GAAC,CAAC;AAAG,SAAM,CAACtB,GAAE,GAAEA,GAAE,CAAC;AAAC;;;ACArqK,SAAS2B,IAAEC,KAAE;AAAC,SAAO,WAASA,IAAE;AAAK;AAAC,IAAMC,MAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,SAAO,GAAE,aAAW,GAAE,WAAS,IAAI,OAAG,GAAE;AAAA,EAAQ;AAAA,EAAC,QAAQD,KAAEE,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAO,IAAIC,GAAEN,KAAEE,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACF,IAAE,WAAS;AAAK,IAAMK,KAAN,MAAO;AAAA,EAAC,YAAYN,KAAEE,KAAEC,KAAE;AAAC,SAAK,YAAUH,KAAE,KAAK,WAAS,WAASE,IAAE,UAAQA,IAAE,UAAQC,MAAE,GAAE,KAAK,WAAS,WAASD,IAAE,UAAQA,IAAE,UAAQC,MAAE,GAAE,KAAK,UAAQ,WAASD,IAAE,SAAOA,IAAE,SAAO,EAAE,WAAU,KAAK,qBAAmB,IAAIF;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,UAAMA,MAAE,KAAK;AAAU,WAAO,KAAK,YAAU,MAAKA,OAAGD,IAAEC,GAAC,IAAE,KAAK,eAAeA,GAAC,IAAE;AAAA,EAAI;AAAA,EAAC,eAAeO,KAAE;AAAC,QAAIR,MAAE;AAAG,YAAO,KAAK,SAAQ;AAAA,MAAC,KAAK,EAAE;AAAa;AAAC,gBAAMC,MAAE,EAAEO,GAAC;AAAE,UAAAP,QAAI,KAAK,mBAAmB,aAAaA,IAAE,CAAC,IAAE,KAAK,UAASA,IAAE,CAAC,IAAE,KAAK,QAAQ,GAAED,MAAE;AAAA,QAAG;AAAC;AAAA,MAAM,KAAK,EAAE;AAAkB;AAAC,gBAAMI,MAAEK,GAAE;AAAE,UAAAC,GAAEN,KAAEI,GAAC,GAAEJ,QAAI,KAAK,mBAAmB,cAAcA,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,IAAE,KAAK,WAAUA,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,IAAE,KAAK,QAAQ,GAAEJ,MAAE;AAAA,QAAG;AAAC;AAAA,MAAM,KAAK,EAAE;AAAA,MAAU,SAAQ;AAAC,YAAIC;AAAE,QAAAA,MAAE,IAAI,6BAA6B,IAAEU,GAAEH,GAAC,IAAEC,IAAED,GAAC,GAAE,SAAOP,QAAI,KAAK,mBAAmB,aAAaA,IAAE,CAAC,IAAE,KAAK,UAASA,IAAE,CAAC,IAAE,KAAK,QAAQ,GAAED,MAAE;AAAA,MAAG;AAAA,IAAC;AAAC,WAAOA,MAAE,KAAK,qBAAmB;AAAA,EAAI;AAAC;;;ACA/G,SAASY,GAAEC,IAAE;AAAC,MAAG,CAACA,GAAE,QAAO;AAAK,UAAOA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAqC,aAAOC,GAAE,MAAM;AAAA,IAAE,KAAI;AAA0B,aAAOC,GAAE,MAAM;AAAA,IAAE,KAAI;AAA2B,aAAOC,GAAE,MAAM;AAAA,IAAE,KAAI;AAAuC,aAAO,EAAE,MAAM;AAAA,IAAE,KAAI;AAAwB,aAAOC,GAAE,MAAM;AAAA,IAAE,KAAI;AAA2B,aAAOC,GAAE,MAAM;AAAA,IAAE,KAAI;AAA0B,aAAOH,GAAE,MAAM;AAAA,IAAE,KAAI;AAAwB,aAAOI,IAAE,MAAM;AAAA,IAAE,KAAI;AAAyB,aAAOC,IAAE,MAAM;AAAA,IAAE,KAAI;AAA2B,aAAOC,GAAE,MAAM;AAAA,IAAE,KAAI;AAA4B,aAAOF,IAAE,MAAM;AAAA,IAAE,KAAI;AAA2B,aAAOF,IAAE,MAAM;AAAA,IAAE,KAAI;AAA0B,aAAOK,GAAE,MAAM;AAAA,IAAE,KAAI;AAAyB,aAAOD,GAAE,MAAM;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAASE,GAAEC,KAAE;AAAC,MAAG,CAACA,IAAE,QAAO;AAAK,UAAOA,IAAE,MAAK;AAAA,IAAC,KAAI;AAAsC,aAAOH,GAAE,MAAM;AAAA,IAAE,KAAI;AAAkC,aAAOD,IAAE,MAAM;AAAA,IAAE,KAAI;AAAqC,aAAOC,IAAE,MAAM;AAAA,IAAE,KAAI;AAAkC,aAAOL,IAAE,MAAM;AAAA,IAAE,KAAI;AAA2B,aAAOA,IAAE,MAAM;AAAA,IAAE,KAAI;AAA+B,aAAOI,IAAE,MAAM;AAAA,IAAE,KAAI;AAAkC,aAAOC,IAAE,MAAM;AAAA,EAAC;AAAC,SAAO;AAAI;;;ACAz/E,SAASI,IAAEA,KAAE;AAAC,QAAMC,MAAED,IAAE,SAAS,CAAC;AAAE,MAAGC,eAAa,oBAAkBA,eAAa,kBAAkB,QAAOA;AAAE,QAAMC,MAAE,SAAS,cAAc,QAAQ;AAAE,EAAAA,IAAE,QAAMF,IAAE,OAAME,IAAE,SAAOF,IAAE;AAAO,QAAMG,MAAED,IAAE,WAAW,IAAI;AAAE,SAAOD,eAAa,YAAUE,IAAE,aAAaF,KAAE,GAAE,CAAC,IAAEE,IAAE,UAAUF,KAAE,GAAE,CAAC,GAAEC;AAAC;;;ACA5R,IAAME,MAAN,MAAO;AAAA,EAAC,YAAYA,MAAE,GAAEC,KAAE,GAAEC,MAAE,GAAEC,MAAE,GAAE;AAAC,SAAK,IAAEH,KAAE,KAAK,IAAEC,IAAE,KAAK,QAAMC,KAAE,KAAK,SAAOC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,SAAO,KAAG,KAAK,UAAQ;AAAA,EAAC;AAAA,EAAC,MAAMH,KAAE;AAAC,SAAK,IAAE,KAAK,IAAI,KAAK,GAAEA,IAAE,CAAC,GAAE,KAAK,IAAE,KAAK,IAAI,KAAK,GAAEA,IAAE,CAAC,GAAE,KAAK,QAAM,KAAK,IAAI,KAAK,OAAMA,IAAE,KAAK,GAAE,KAAK,SAAO,KAAK,IAAI,KAAK,QAAOA,IAAE,MAAM;AAAA,EAAC;AAAC;;;ACA7N,IAAMI,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYA,KAAEC,KAAEC,KAAEC,KAAE;AAAC,SAAK,SAAOC,GAAEJ,KAAEC,GAAC,GAAE,KAAK,UAAQI,GAAE,GAAE,KAAK,YAAUH,MAAE,GAAE,KAAK,aAAWC,MAAE,GAAE,KAAK,QAAMD,KAAE,KAAK,SAAOC;AAAA,EAAC;AAAA,EAAC,IAAI,IAAG;AAAC,WAAO,KAAK,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,IAAG;AAAC,WAAO,KAAK,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,OAAO,CAAC,IAAE,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,OAAO,CAAC,IAAE,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,OAAO,CAAC,IAAE,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,OAAO,CAAC,IAAE,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,IAAE,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,IAAE,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,IAAE,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,IAAE,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,EAAEC,KAAE;AAAC,SAAK,OAAO,CAAC,IAAEA;AAAA,EAAC;AAAA,EAAC,IAAI,EAAEA,KAAE;AAAC,SAAK,OAAO,CAAC,IAAEA;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,GAAE,KAAK,GAAE,KAAK,GAAE,KAAK,OAAM,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,WAAOA,IAAE,SAAS,KAAK,OAAO,CAAC,CAAC,GAAEA,IAAE,SAAS,KAAK,OAAO,CAAC,CAAC,GAAEA,IAAE,KAAK,KAAK,KAAK,GAAEA,IAAE,KAAK,KAAK,MAAM,GAAEA;AAAA,EAAC;AAAA,EAAC,mBAAmBA,KAAEE,KAAE,GAAE;AAAC,UAAMN,MAAE,KAAK,IAAII,IAAE,QAAQ,CAAC,IAAE,KAAK,QAAQ,CAAC,CAAC,GAAEH,MAAE,KAAK,IAAIG,IAAE,QAAQ,CAAC,IAAE,KAAK,QAAQ,CAAC,CAAC,GAAEF,OAAGE,IAAE,YAAU,KAAK,YAAUE,MAAGN,KAAEG,OAAGC,IAAE,aAAW,KAAK,aAAWE,MAAGL,KAAEI,MAAE,KAAK,IAAIH,KAAEC,GAAC;AAAE,WAAO,KAAK,KAAKE,GAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,KAAE;AAAC,UAAME,KAAE,KAAK,IAAI,KAAK,MAAKF,IAAE,IAAI,GAAEJ,MAAE,KAAK,IAAI,KAAK,MAAKI,IAAE,IAAI,GAAEH,MAAE,KAAK,IAAI,KAAK,MAAKG,IAAE,IAAI,IAAEE,IAAEJ,MAAE,KAAK,IAAI,KAAK,MAAKE,IAAE,IAAI,IAAEJ,KAAEG,MAAEG,KAAEL,MAAE,GAAEI,MAAEL,MAAEE,MAAE;AAAE,SAAK,QAAMD,KAAE,KAAK,SAAOC,KAAE,KAAK,YAAUD,MAAE,GAAE,KAAK,aAAWC,MAAE,GAAE,KAAK,IAAEC,KAAE,KAAK,IAAEE;AAAA,EAAC;AAAA,EAAC,OAAO,YAAYD,KAAE;AAAC,UAAME,KAAEF,IAAE,QAAQ,GAAEH,MAAEG,IAAE,QAAQ,GAAEF,MAAEE,IAAE,UAAU,GAAED,MAAEC,IAAE,UAAU;AAAE,WAAO,IAAI,GAAEE,IAAEL,KAAEC,KAAEC,GAAC;AAAA,EAAC;AAAC;;;ACAp5B,IAAMI,MAAE;AAAR,IAAWC,KAAE;AAAb,IAAeC,KAAEF,MAAEC;AAAnB,IAAqBE,KAAEH,MAAE;AAAzB,IAA2BI,KAAE;AAA7B,IAA+BC,KAAE;AAAjC,IAAmCC,KAAE,KAAK,KAAG;AAA7C,IAAiDC,KAAE;AAAnD,IAAqDC,KAAE;AAAI,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,SAAK,aAAWC,GAAE,GAAE,KAAK,WAAS,GAAE,KAAK,WAAS,GAAE,KAAK,UAAQ,GAAE,KAAK,UAAQ,KAAI,KAAK,UAAQ;AAAK,UAAMA,MAAEF,IAAE,MAAKG,KAAE,IAAI,aAAa,CAAC;AAAE,IAAAL,OAAGG,KAAEF,OAAGE;AAAE,UAAMG,MAAEJ,IAAE,OAAKE,IAAE,QAAMD,MAAED,IAAE,QAAQ,OAAMK,MAAEL,IAAE,OAAKE,IAAE,SAAOD,MAAED,IAAE,QAAQ;AAAO,SAAK,QAAMI,KAAE,KAAK,SAAOC,KAAEF,GAAE,CAAC,IAAEL,KAAEK,GAAE,CAAC,IAAEJ,KAAEI,GAAE,CAAC,IAAEL,MAAEM,KAAED,GAAE,CAAC,IAAEJ,KAAEI,GAAE,CAAC,IAAEL,KAAEK,GAAE,CAAC,IAAEJ,MAAEM,KAAEF,GAAE,CAAC,IAAEL,MAAEM,KAAED,GAAE,CAAC,IAAEJ,MAAEM,KAAE,KAAK,QAAMF,IAAE,KAAK,kBAAkBD,GAAC,GAAE,KAAK,SAAOD,KAAE,KAAK,UAAQD,KAAE,KAAK,IAAEF,KAAE,KAAK,IAAEC,KAAE,KAAK,YAAU,KAAK,IAAID,MAAEM,KAAEL,MAAEM,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,MAAMP,KAAE;AAAC,SAAK,SAAOA,KAAE,EAAE,KAAK,YAAW,CAACA,GAAC,GAAE,KAAK,YAAY,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAc;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,YAAU,KAAK,YAAY,KAAK,KAAK,GAAE,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,OAAO,aAAa,KAAK,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAI;AAAA,EAAC,IAAI,SAAQ;AAAC,QAAG,CAAC,KAAK,SAAQ;AAAC,YAAK,EAAC,QAAOA,KAAE,OAAMC,IAAC,IAAE,KAAK,QAAQ,SAAQE,MAAEF,MAAE,KAAK,QAAOG,MAAE,KAAK,IAAIJ,GAAC,IAAE,KAAK,QAAOM,MAAE,IAAI,aAAa,CAAC;AAAE,MAAAA,IAAE,CAAC,IAAE,KAAK,GAAEA,IAAE,CAAC,IAAE,KAAK,GAAEA,IAAE,CAAC,IAAE,KAAK,IAAEH,KAAEG,IAAE,CAAC,IAAE,KAAK,GAAEA,IAAE,CAAC,IAAE,KAAK,GAAEA,IAAE,CAAC,IAAE,KAAK,IAAEF,KAAEE,IAAE,CAAC,IAAE,KAAK,IAAEH,KAAEG,IAAE,CAAC,IAAE,KAAK,IAAEF;AAAE,YAAMG,MAAEC,GAAEJ,GAAE,GAAE,KAAK,YAAW,KAAK,UAAU;AAAE,MAAAG,GAAED,KAAEA,KAAEC,GAAC;AAAE,UAAIE,MAAE,IAAE,GAAEC,KAAE,IAAE,GAAEC,MAAE,GAAEC,KAAE;AAAE,eAAQV,MAAE,GAAEA,MAAE,GAAEA,OAAI;AAAC,cAAMF,MAAEM,IAAE,IAAEJ,GAAC,GAAED,MAAEK,IAAE,IAAEJ,MAAE,CAAC;AAAE,QAAAO,MAAE,KAAK,IAAIA,KAAET,GAAC,GAAEU,KAAE,KAAK,IAAIA,IAAET,GAAC,GAAEU,MAAE,KAAK,IAAIA,KAAEX,GAAC,GAAEY,KAAE,KAAK,IAAIA,IAAEX,GAAC;AAAA,MAAC;AAAC,YAAMX,MAAEqB,MAAEF,KAAElB,MAAEqB,KAAEF,IAAElB,KAAEiB,MAAEnB,MAAE,GAAEG,KAAEiB,KAAEnB,MAAE;AAAE,WAAK,UAAQ,IAAIY,GAAEX,IAAEC,IAAEH,KAAEC,GAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,aAAaS,KAAE;AAAC,SAAK,aAAWA,KAAE,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,YAAYA,KAAE;AAAC,SAAK,aAAW,KAAK,WAAS,EAAC,WAAU,GAAE,YAAW,GAAE,WAAU,GAAE,YAAW,EAAC;AAAG,UAAMC,MAAE,KAAK,UAASE,MAAE,IAAI,aAAa,CAAC,GAAEC,MAAEI,GAAEJ,GAAE,GAAE,KAAK,YAAW,KAAK,UAAU;AAAE,IAAAG,GAAEJ,KAAEH,KAAEI,GAAC,GAAEH,IAAE,YAAUN,GAAEQ,IAAE,CAAC,IAAER,IAAEQ,IAAE,CAAC,IAAER,EAAC,GAAEM,IAAE,aAAWN,GAAEQ,IAAE,CAAC,IAAER,IAAEQ,IAAE,CAAC,IAAER,EAAC,GAAEM,IAAE,YAAUN,GAAEQ,IAAE,CAAC,IAAER,IAAEQ,IAAE,CAAC,IAAER,EAAC,GAAEM,IAAE,aAAWN,GAAEQ,IAAE,CAAC,IAAER,IAAEQ,IAAE,CAAC,IAAER,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkB,EAAC,GAAEK,KAAE,GAAEC,KAAE,OAAMC,KAAE,QAAOC,IAAC,GAAE;AAAC,SAAK,aAAW,EAAC,WAAUR,GAAEK,KAAEC,GAAC,GAAE,YAAWN,GAAEK,MAAEE,KAAED,GAAC,GAAE,WAAUN,GAAEK,KAAEC,MAAEE,GAAC,GAAE,YAAWR,GAAEK,MAAEE,KAAED,MAAEE,GAAC,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMU,KAAE,CAACb,KAAEC,SAAK,EAAC,MAAK,GAAE,MAAK,GAAE,KAAI,MAAG,MAAK,IAAID,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,gBAAeC,KAAE,SAAQ,EAAC,SAAQ,GAAE,QAAO,GAAE,OAAMD,KAAE,MAAK,GAAE,KAAI,EAAC,EAAC;AAAG,SAASc,GAAEd,KAAEC,KAAE;AAAC,SAAOD,IAAE,QAAS,CAAAA,QAAG,EAAEA,KAAEA,KAAEC,GAAC,CAAE,GAAE,EAAC,WAAUN,GAAEA,KAAEK,IAAE,CAAC,EAAE,CAAC,GAAEL,KAAEK,IAAE,CAAC,EAAE,CAAC,CAAC,GAAE,YAAWL,GAAEA,KAAEK,IAAE,CAAC,EAAE,CAAC,GAAEL,KAAEK,IAAE,CAAC,EAAE,CAAC,CAAC,GAAE,WAAUL,GAAEA,KAAEK,IAAE,CAAC,EAAE,CAAC,GAAEL,KAAEK,IAAE,CAAC,EAAE,CAAC,CAAC,GAAE,YAAWL,GAAEA,KAAEK,IAAE,CAAC,EAAE,CAAC,GAAEL,KAAEK,IAAE,CAAC,EAAE,CAAC,CAAC,EAAC;AAAC;AAAC,IAAMe,KAAN,MAAO;AAAA,EAAC,YAAYf,KAAEC,KAAEC,KAAE;AAAC,SAAK,YAAU,GAAE,KAAK,UAAUF,KAAEC,KAAEC,GAAC,GAAE,KAAK,SAAOF,KAAE,KAAK,SAAO,KAAK,cAAcA,GAAC,GAAE,KAAK,cAAYC,IAAE,SAAO,GAAE,KAAK,eAAa,MAAIC,IAAE,OAAM,KAAK,aAAW,KAAK,sBAAsB,KAAK,QAAOA,GAAC,GAAE,KAAK,kBAAgBA,IAAE,iBAAgBA,IAAE,kBAAgBA,IAAE,mBAAiB,CAAC,KAAK,QAAO,KAAK,UAAU,IAAE,KAAK,gBAAgB,KAAK,UAAU;AAAG,eAAUC,OAAKH,IAAE,CAAAG,IAAE,aAAa,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,YAAYH,KAAE;AAAC,QAAG,MAAIA,OAAG,MAAI,KAAK,UAAU;AAAO,SAAK,YAAUA;AAAE,UAAMG,MAAE,KAAK,YAAWC,MAAE,EAAEA,GAAE,GAAEJ,GAAC;AAAE,IAAAQ,GAAEL,KAAEC,KAAED,GAAC;AAAE,eAAUF,OAAK,KAAK,OAAO,CAAAA,IAAE,aAAa,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,UAAUD,KAAEC,KAAEC,KAAE;AAAC,QAAG,CAACA,IAAE,cAAY,WAASA,IAAE,cAAY,CAACF,IAAE,OAAO;AAAO,UAAMG,MAAED,IAAE,OAAME,MAAE,gBAAcF,IAAE,aAAWV,KAAEC,IAAEe,MAAER,IAAE,CAAC,EAAE;AAAe,eAAUK,MAAKJ,KAAE;AAAC,YAAMA,MAAEI,GAAE,SAAOF,KAAED,MAAEG,GAAE,SAAOF,KAAEG,OAAGD,GAAE,QAAMA,GAAE,iBAAeF;AAAE,MAAAH,IAAE,KAAK,IAAID,GAAEE,KAAEC,MAAEE,MAAED,KAAEU,GAAEP,KAAEE,GAAC,GAAE,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBP,KAAE;AAAC,UAAMC,MAAEc,GAAE,KAAK,mBAAiB,CAAC,GAAEb,OAAGL,KAAEI,OAAG,GAAEE,MAAE,KAAK,kBAAgBD,MAAE,GAAE,EAAC,MAAKK,KAAE,MAAKH,IAAE,MAAKC,KAAE,MAAKC,KAAE,GAAEE,KAAE,GAAEC,IAAE,OAAMC,KAAE,QAAOC,GAAC,IAAE,KAAK,QAAOtB,MAAE,CAACkB,MAAEX,IAAEQ,KAAER,EAAC,GAAEN,MAAE,CAACe,MAAET,IAAEQ,KAAER,EAAC,GAAEL,KAAE,CAACgB,MAAEX,IAAEU,MAAEV,EAAC,GAAEJ,KAAE,CAACa,MAAET,IAAEU,MAAEV,EAAC,GAAEH,KAAEoB,GAAE,CAAC,CAACxB,IAAE,CAAC,IAAEa,KAAEb,IAAE,CAAC,IAAEa,GAAC,GAAE,CAACZ,IAAE,CAAC,IAAEY,KAAEZ,IAAE,CAAC,IAAEY,GAAC,GAAE,CAACb,IAAE,CAAC,IAAEc,KAAEd,IAAE,CAAC,IAAEc,GAAC,GAAE,CAACb,IAAE,CAAC,IAAEa,KAAEb,IAAE,CAAC,IAAEa,GAAC,CAAC,GAAEH,GAAC,GAAEN,KAAEmB,GAAE,CAAC,CAACtB,GAAE,CAAC,IAAEY,KAAEZ,GAAE,CAAC,IAAEY,GAAC,GAAE,CAACX,GAAE,CAAC,IAAEW,KAAEX,GAAE,CAAC,IAAEW,GAAC,GAAE,CAACZ,GAAE,CAAC,IAAEW,KAAEX,GAAE,CAAC,IAAEW,GAAC,GAAE,CAACV,GAAE,CAAC,IAAEU,KAAEV,GAAE,CAAC,IAAEU,GAAC,CAAC,GAAEF,GAAC,GAAEL,KAAEkB,GAAE,CAAC,CAACxB,IAAE,CAAC,IAAEa,KAAEb,IAAE,CAAC,IAAEa,GAAC,GAAE,CAACb,IAAE,CAAC,IAAEc,KAAEd,IAAE,CAAC,IAAEc,GAAC,GAAE,CAACZ,GAAE,CAAC,IAAEW,KAAEX,GAAE,CAAC,IAAEW,GAAC,GAAE,CAACX,GAAE,CAAC,IAAEY,KAAEZ,GAAE,CAAC,IAAEY,GAAC,CAAC,GAAEH,GAAC,GAAEF,KAAEe,GAAE,CAAC,CAACvB,IAAE,CAAC,IAAEa,KAAEb,IAAE,CAAC,IAAEa,GAAC,GAAE,CAACb,IAAE,CAAC,IAAEY,KAAEZ,IAAE,CAAC,IAAEY,GAAC,GAAE,CAACV,GAAE,CAAC,IAAEW,KAAEX,GAAE,CAAC,IAAEW,GAAC,GAAE,CAACX,GAAE,CAAC,IAAEU,KAAEV,GAAE,CAAC,IAAEU,GAAC,CAAC,GAAEF,GAAC,GAAEY,KAAE,EAAC,MAAKC,GAAE,CAACxB,KAAEC,KAAEC,IAAEC,EAAC,GAAEQ,GAAC,GAAE,KAAIP,IAAE,KAAIC,IAAE,MAAKC,IAAE,OAAMG,GAAC;AAAE,WAAM,CAAC,IAAII,GAAEM,KAAEC,IAAEC,MAAE,IAAER,KAAES,KAAE,IAAET,GAAC,GAAEU,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,UAAMb,MAAE,KAAK,QAAOC,MAAEK,GAAEF,GAAE,GAAEJ,IAAE,GAAEA,IAAE,CAAC;AAAE,QAAG,EAAEC,KAAEA,KAAE,KAAK,UAAU,GAAE,KAAK,cAAa;AAAC,YAAMC,MAAE,KAAK,IAAIF,IAAE,OAAMA,IAAE,MAAM;AAAE,aAAO,IAAIG,GAAEF,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEC,KAAEA,GAAC;AAAA,IAAC;AAAC,WAAO,IAAIC,GAAEF,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAED,IAAE,OAAMA,IAAE,MAAM;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAE;AAAC,QAAIC,MAAE,IAAE,GAAEC,MAAE,IAAE,GAAEC,MAAE,GAAEC,MAAE;AAAE,eAAUE,OAAKN,IAAE,CAAAC,MAAE,KAAK,IAAIA,KAAEK,IAAE,QAAQ,GAAEJ,MAAE,KAAK,IAAIA,KAAEI,IAAE,QAAQ,GAAEH,MAAE,KAAK,IAAIA,KAAEG,IAAE,YAAY,GAAEF,MAAE,KAAK,IAAIA,KAAEE,IAAE,YAAY;AAAE,UAAME,MAAEL,MAAEF,KAAEI,KAAED,MAAEF;AAAE,WAAO,IAAIC,GAAEF,MAAEO,MAAE,GAAEN,MAAEG,KAAE,GAAEG,KAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBL,KAAEC,KAAE;AAAC,UAAMC,MAAEN,KAAEK,IAAE,OAAMI,KAAED,GAAE,GAAEG,MAAEH,GAAE;AAAE,WAAOD,GAAEE,IAAEA,IAAEC,GAAEC,KAAEN,IAAE,SAAQ,CAACA,IAAE,OAAO,CAAC,GAAEA,IAAE,QAAMC,GAAEG,IAAEA,IAAEH,GAAC,KAAGC,GAAEE,IAAEA,IAAEC,GAAEC,KAAEP,IAAE,GAAEA,IAAE,CAAC,CAAC,GAAEE,GAAEG,IAAEA,IAAEH,GAAC,GAAEC,GAAEE,IAAEA,IAAEC,GAAEC,KAAE,CAACP,IAAE,GAAE,CAACA,IAAE,CAAC,CAAC,IAAGK;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYL,KAAEC,KAAEC,KAAEC,KAAEC,KAAEI,KAAE;AAAC,SAAK,gBAAc,GAAE,KAAK,SAAO,GAAE,KAAK,SAAO,GAAE,KAAK,QAAM,KAAK,IAAI,GAAE,KAAK,IAAIP,KAAEC,GAAC,CAAC,GAAE,KAAK,MAAI,KAAK,IAAI,GAAE,KAAK,IAAID,KAAEC,GAAC,CAAC,GAAE,KAAK,MAAIF,IAAE,WAAS,KAAK,gBAAcA,IAAE,KAAK,GAAG,EAAE,QAAQ,QAAO,KAAK,QAAMG,KAAE,KAAK,OAAKC,KAAE,KAAK,OAAKI;AAAA,EAAC;AAAC;AAAC,IAAMS,KAAE,CAAAjB,QAAG,OAAKA;AAAhB,IAAkBkB,KAAE,CAAAlB,QAAG,OAAKA;AAAE,SAASmB,GAAEnB,KAAEC,KAAEC,KAAE;AAAC,QAAMC,MAAE,IAAI,SAAMC,MAAE,IAAEF,IAAE,OAAMM,MAAEN,IAAE,eAAaE,KAAEC,KAAEJ,MAAED,IAAE,SAAO,IAAE,GAAEM,MAAEL,MAAE,KAAGD,IAAE,QAAOO,MAAEN,MAAE,KAAG;AAAE,MAAIQ,MAAEJ,IAAEK,KAAE,GAAEC,MAAE,GAAEC,KAAEH,KAAEO,MAAEJ,IAAEtB,MAAE,GAAEC,MAAE,IAAE,GAAEC,KAAE;AAAE,SAAKiB,QAAIH,OAAG;AAAC,UAAK,EAAC,MAAKL,KAAE,SAAQC,IAAC,IAAEF,IAAES,GAAC,GAAEL,MAAE,KAAK,IAAIF,IAAE,GAAG;AAAE,QAAGe,GAAEhB,GAAC,KAAGiB,GAAEjB,GAAC,MAAIV,MAAE,KAAK,IAAIA,KAAEa,GAAC,GAAEZ,KAAE,KAAK,IAAIA,IAAEY,MAAEF,IAAE,MAAM,IAAGe,GAAEhB,GAAC,EAAE,CAAAQ,QAAIJ,OAAIF,IAAE,KAAK,IAAI,EAAEH,KAAEY,IAAEH,MAAEF,KAAEG,IAAEnB,KAAEC,EAAC,CAAC,GAAED,MAAE,IAAE,GAAEC,KAAE,IAAGkB,KAAE,GAAEE,KAAEH,MAAEF,KAAES,MAAEP,MAAEF,KAAEI,MAAE;AAAA,aAAUO,GAAEjB,GAAC,EAAE,CAAAe,MAAEP,MAAEF,KAAEI,MAAE,GAAErB,MAAEY,IAAE,SAAQQ,MAAGR,IAAE;AAAA,aAAgBQ,KAAEF,KAAE;AAAC,UAAGQ,QAAIJ,IAAE;AAAC,cAAMX,MAAEe,MAAE,IAAET;AAAE,QAAAG,MAAGpB,KAAEa,IAAE,KAAK,IAAI,EAAEH,KAAEY,IAAEX,KAAES,KAAEC,KAAEpB,KAAEC,EAAC,CAAC,GAAED,MAAE,IAAE,GAAEC,KAAE,GAAEoB,KAAEI,KAAEN,KAAEC;AAAA,MAAC,MAAM,CAAAR,IAAE,KAAK,IAAI,EAAEH,KAAEY,IAAEH,MAAEF,KAAEG,IAAEnB,KAAEC,EAAC,CAAC,GAAED,MAAE,IAAE,GAAEC,KAAE,GAAEoB,KAAEH,KAAEO,MAAEP,KAAEC,KAAE;AAAE,MAAAA,MAAGR,IAAE,SAAQS,OAAGT,IAAE;AAAA,IAAO,MAAM,CAAAQ,MAAGR,IAAE,SAAQS,OAAGT,IAAE;AAAQ,IAAAO,OAAGF;AAAA,EAAC;AAAC,QAAMd,KAAE,IAAI,EAAEO,KAAEY,IAAEH,MAAEF,KAAEG,IAAEnB,KAAEC,EAAC;AAAE,SAAOC,GAAE,SAAO,KAAGA,GAAE,MAAIO,IAAE,UAAQG,IAAE,KAAKV,EAAC,GAAEU;AAAC;AAAC,SAASiB,GAAEpB,KAAEC,KAAE;AAAC,MAAIC,MAAE;AAAE,WAAQM,MAAE,GAAEA,MAAER,IAAE,QAAOQ,OAAI;AAAC,UAAK,EAAC,OAAMP,IAAC,IAAED,IAAEQ,GAAC;AAAE,IAAAN,MAAE,KAAK,IAAID,KAAEC,GAAC;AAAA,EAAC;AAAC,QAAMC,MAAE,gBAAcF,IAAE,aAAWV,KAAE,GAAEa,MAAEJ,IAAE,CAAC,EAAE;AAAK,SAAM,EAAC,GAAE,GAAE,GAAEI,KAAE,QAAOJ,IAAEA,IAAE,SAAO,CAAC,EAAE,OAAKC,IAAE,cAAYD,IAAE,SAAO,KAAGG,MAAEC,KAAE,OAAMF,IAAC;AAAC;AAAC,SAASmB,GAAErB,KAAEC,KAAEC,KAAE;AAAC,QAAMC,MAAED,IAAE,OAAME,MAAE,IAAI,SAAMI,MAAEW,GAAEnB,KAAEC,KAAEC,GAAC,GAAEG,KAAEe,GAAEZ,KAAEN,GAAC,GAAE,EAAC,QAAOI,KAAE,QAAOC,IAAC,IAAEL,KAAEO,MAAEH,QAAIN,GAAE,WAAS,IAAE,GAAEW,MAAEF,MAAE,IAAEH,MAAE,GAAEM,MAAG,IAAEH,OAAG,CAACJ,GAAE,IAAEM,OAAGN,GAAE,SAAO,MAAII,MAAE,IAAE,KAAG,CAACnB;AAAE,WAAQoB,KAAE,GAAEA,KAAEF,IAAE,QAAOE,MAAI;AAAC,UAAK,EAAC,OAAMT,KAAE,KAAII,IAAE,OAAMC,IAAC,IAAEE,IAAEE,EAAC;AAAE,QAAID,MAAE,MAAIF,MAAE,MAAID,MAAE,KAAGZ;AAAE,UAAMiB,MAAED,KAAER,IAAE,aAAWU,KAAElB;AAAE,IAAAc,IAAEE,EAAC,EAAE,SAAOD,KAAED,IAAEE,EAAC,EAAE,SAAOC;AAAE,aAAQT,MAAED,KAAEC,OAAGG,IAAEH,OAAI;AAAC,YAAMD,MAAED,IAAEE,GAAC;AAAE,UAAGe,GAAEhB,IAAE,IAAI,EAAE;AAAS,YAAMO,MAAE,IAAIT,GAAEU,MAAER,IAAE,QAAQ,MAAKU,MAAEV,IAAE,QAAQ,KAAIA,KAAEE,GAAC;AAAE,MAAAM,OAAGR,IAAE,QAAQ,SAAQG,IAAE,KAAKI,GAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,IAAIO,GAAEX,KAAEI,KAAEN,GAAC;AAAC;;;ACAv2J,IAAM,IAAE,KAAK,KAAG;AAAhB,IAAoB,IAAE;AAAtB,IAAyBoB,KAAE,EAAE,UAAU,sCAAsC;AAAE,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAK,KAAGA;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAgB;AAAC,WAAO,IAAI,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,MAAE,KAAK;AAAG,WAAO,IAAI,GAAEA,IAAE,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,UAAMC,MAAE,KAAK;AAAG,WAAM,CAACA,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAEA,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,YAAYD,KAAEC,KAAE;AAAC,WAAO,IAAI,GAAE,CAACD,KAAE,GAAE,GAAE,GAAEC,KAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAMD,KAAEC,KAAE;AAAC,UAAMC,MAAE,KAAK;AAAG,WAAOA,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,KAAGD,KAAE;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,KAAK,KAAK,GAAG,CAAC,IAAE,KAAK,GAAG,CAAC,IAAE,KAAK,GAAG,CAAC,IAAE,KAAK,GAAG,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,gBAAgBD,KAAEC,KAAE;AAAC,WAAO,IAAI,GAAE,CAAC,GAAE,GAAED,KAAE,GAAE,GAAEC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUD,KAAEC,KAAE;AAAC,UAAMC,MAAE,KAAK;AAAG,WAAOA,IAAE,CAAC,KAAGF,KAAEE,IAAE,CAAC,KAAGD,KAAE;AAAA,EAAI;AAAA,EAAC,OAAO,aAAaD,KAAE;AAAC,UAAMC,MAAE,KAAK,IAAID,GAAC,GAAEE,MAAE,KAAK,IAAIF,GAAC;AAAE,WAAO,IAAI,GAAE,CAACC,KAAE,CAACC,KAAE,GAAEA,KAAED,KAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,KAAE;AAAC,WAAO,GAAE,SAAS,MAAK,GAAE,aAAaA,GAAC,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,MAAE,KAAK,GAAG,CAAC,GAAEC,MAAE,KAAK,GAAG,CAAC,GAAEC,MAAE,KAAK,KAAKF,MAAEA,MAAEC,MAAEA,GAAC;AAAE,WAAM,CAACD,MAAEE,KAAED,MAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,OAAO,SAASF,KAAEC,KAAEC,KAAE;AAAC,UAAMC,MAAEH,IAAE,IAAGI,MAAEH,IAAE,IAAGI,MAAEF,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAEE,MAAEH,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAEG,MAAEJ,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAEI,MAAEL,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAEK,KAAEN,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAEM,MAAEP,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAEO,KAAET,IAAE;AAAG,WAAOS,GAAE,CAAC,IAAEN,KAAEM,GAAE,CAAC,IAAEL,KAAEK,GAAE,CAAC,IAAEJ,KAAEI,GAAE,CAAC,IAAEH,KAAEG,GAAE,CAAC,IAAEF,IAAEE,GAAE,CAAC,IAAED,KAAER;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMF,MAAE,KAAK;AAAG,QAAIC,MAAED,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC;AAAE,QAAG,MAAIC,IAAE,QAAO,IAAI,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAE,IAAAA,MAAE,IAAEA;AAAE,UAAMC,OAAGF,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAGC,KAAEE,OAAGH,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAGC,KAAEG,MAAEJ,IAAE,CAAC,IAAEC,KAAEI,MAAE,CAACL,IAAE,CAAC,IAAEC,KAAEK,MAAE,CAACN,IAAE,CAAC,IAAEC,KAAEM,MAAEP,IAAE,CAAC,IAAEC;AAAE,WAAO,IAAI,GAAE,CAACG,KAAEC,KAAEH,KAAEI,KAAEC,KAAEJ,GAAC,CAAC;AAAA,EAAC;AAAC;AAAC,IAAMS,KAAN,MAAO;AAAA,EAAC,YAAYZ,KAAEC,KAAE;AAAC,SAAK,mBAAiBD,KAAE,KAAK,YAAU,CAAC,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,qBAAmB,GAAE,KAAK,iBAAe,IAAIC,GAAED,IAAE,QAAO,QAAO,GAAG,GAAE,KAAK,eAAa,OAAG,KAAK,eAAa,GAAE,KAAK,UAAU,KAAKC,OAAGF,GAAE,eAAe,CAAC,GAAE,KAAK,cAAc,KAAKE,MAAEA,IAAE,WAAW,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,KAAEC,KAAE;AAAC,SAAK,YAAU,CAACD,OAAGD,GAAE,eAAe,CAAC,GAAE,KAAK,gBAAc,CAACE,QAAID,MAAEA,IAAE,WAAW,IAAE,EAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBA,KAAE;AAAC,SAAK,qBAAmBA;AAAA,EAAC;AAAA,EAAC,YAAYA,KAAE;AAAC,WAAO,KAAK,UAAU,KAAK,UAAU,SAAO,CAAC,EAAE,UAAUA,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAE;AAAC,WAAOA,MAAE,KAAK,cAAc,KAAK,cAAc,SAAO,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,KAAE;AAAC,WAAO,KAAK,UAAU,KAAK,UAAU,SAAO,CAAC,EAAE,OAAO,EAAE,UAAUA,GAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBA,KAAE;AAAC,WAAOA,MAAE,KAAK,cAAc,KAAK,cAAc,SAAO,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK,UAAU,KAAK,UAAU,SAAO,CAAC,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK,WAAW,IAAE,IAAE,KAAK;AAAA,EAAkB;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,UAAU,SAAO;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,UAAU,KAAK,UAAU,SAAO,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,KAAEC,KAAE;AAAC,UAAMC,MAAED,MAAED,IAAE,WAAW,IAAE;AAAE,IAAAD,GAAE,SAASC,KAAE,KAAK,KAAK,GAAEA,GAAC,GAAE,KAAK,UAAU,KAAKA,GAAC,GAAE,KAAK,cAAc,KAAK,KAAK,cAAc,KAAK,cAAc,SAAO,CAAC,IAAEE,GAAC;AAAA,EAAC;AAAA,EAAC,MAAK;AAAC,SAAK,UAAU,OAAO,IAAG,CAAC,GAAE,KAAK,cAAc,OAAO,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWF,KAAEC,KAAEC,KAAE;AAAC,QAAGF,IAAE,SAAOA,IAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAiB,KAAI;AAAA,MAAgB,KAAI;AAAmB,aAAK,qBAAqBA,KAAEC,GAAC;AAAE;AAAA,MAAM,KAAI;AAAgB,aAAK,eAAeD,KAAEC,KAAEC,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBF,KAAEC,KAAE;AAAC,QAAG,CAACD,OAAG,CAACC,IAAE;AAAO,UAAMC,MAAEF,IAAE;AAAa,QAAG,CAACE,IAAE;AAAO,UAAMC,MAAEH,IAAE;AAAQ,QAAGG,OAAGA,IAAE,SAAO,GAAE;AAAC,YAAMH,MAAE,KAAK,eAAeG,KAAEF,GAAC;AAAE,UAAGD,KAAE;AAAC,YAAIC,MAAED,IAAE,KAAK;AAAE,eAAKC,MAAG,MAAK,iBAAiBC,KAAED,GAAC,GAAEA,MAAED,IAAE,KAAK;AAAA,MAAC;AAAA,IAAC,MAAM,MAAK,iBAAiBE,KAAED,GAAC;AAAA,EAAC;AAAA,EAAC,eAAeD,KAAEC,KAAE;AAAC,UAAMC,MAAE,KAAK,iBAAiB;AAAe,QAAIC,MAAE,IAAIC,GAAEH,GAAC;AAAE,eAAUG,OAAKJ,KAAE;AAAC,YAAMA,MAAEa,GAAET,GAAC;AAAE,MAAAJ,QAAIG,MAAEH,IAAE,QAAQG,KAAEC,KAAE,KAAK,kBAAkB,GAAE,MAAKF,GAAC;AAAA,IAAE;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,iBAAiBH,KAAEC,KAAE;AAAC,QAAIC,MAAEF,IAAE;AAAO,WAAKE,SAAK;AAAC,YAAMC,MAAEH,IAAEE,GAAC;AAAE,UAAG,CAACC,OAAG,UAAKA,IAAE,OAAO;AAAS,YAAMC,MAAED,IAAE;AAAQ,UAAGC,OAAGA,IAAE,SAAO,GAAE;AAAC,cAAMJ,MAAE,KAAK,eAAeI,KAAEH,GAAC;AAAE,YAAGD,KAAE;AAAC,cAAIC,MAAE;AAAK,kBAAMA,MAAED,IAAE,KAAK,OAAK,KAAK,gBAAgBG,KAAEF,GAAC,GAAE,CAAC,KAAK,gBAAe;AAAA,QAAC;AAAA,MAAC,MAAM,MAAK,gBAAgBE,KAAEF,GAAC;AAAE,UAAG,KAAK,aAAa;AAAA,IAAM;AAAA,EAAC;AAAA,EAAC,gBAAgBD,KAAEC,KAAE;AAAC,YAAOD,IAAE,MAAK;AAAA,MAAC,KAAI;AAAe,aAAK,cAAcC,KAAED,IAAE,KAAK;AAAE;AAAA,MAAM,KAAI;AAAe,aAAK,cAAcC,KAAED,GAAC;AAAE;AAAA,MAAM,KAAI;AAAiB,aAAK,gBAAgBC,KAAED,GAAC;AAAE;AAAA,MAAM,KAAI;AAAkB,aAAK,iBAAiBC,KAAED,GAAC;AAAE;AAAA,MAAM,KAAI;AAAiB,aAAK,gBAAgBC,KAAED,IAAE,OAAMA,IAAE,OAAMA,IAAE,UAASA,IAAE,WAAUA,IAAE,UAAU;AAAE;AAAA,MAAM,KAAI;AAAmB,aAAK,kBAAkBC,KAAED,GAAC;AAAE;AAAA,MAAM,KAAI;AAAoB,aAAK,mBAAmBC,KAAED,GAAC;AAAE;AAAA,MAAM,KAAI;AAAA,MAAqB,KAAI;AAAA,MAAmB,KAAI;AAAkB,aAAK,gBAAgBA,KAAEC,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,KAAEC,KAAE;AAAC,UAAMC,MAAE,KAAK,oBAAoBD,KAAED,KAAE,KAAK,kBAAkB,CAAC;AAAE,IAAAE,QAAI,KAAK,aAAaF,GAAC,GAAE,KAAK,qBAAqBC,IAAE,YAAWC,GAAC,GAAE,KAAK,YAAY;AAAA,EAAE;AAAA,EAAC,gBAAgBF,KAAEC,KAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBD,KAAEC,KAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBD,KAAEC,KAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBD,KAAEC,KAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBD,KAAEC,KAAE;AAAC,UAAMC,MAAEF,IAAE;AAAgB,QAAGE,KAAE;AAAC,YAAMC,MAAEW,GAAEZ,GAAC;AAAE,UAAGC,KAAE;AAAC,cAAMC,MAAE,sCAAoCF,IAAE,QAAM,sCAAoCA,IAAE,QAAMA,IAAE;AAAe,QAAAE,OAAG,KAAK,aAAaH,GAAC;AAAE,cAAMI,MAAEF,IAAE,QAAQF,KAAEC,KAAE,KAAK,kBAAkB,GAAE,MAAK,KAAK,iBAAiB,cAAc;AAAE,YAAGG,KAAE;AAAC,cAAIJ,MAAE;AAAK,kBAAMA,MAAEI,IAAE,KAAK,OAAK,KAAK,WAAWL,KAAEC,GAAC,GAAE,CAAC,KAAK,gBAAe;AAAA,QAAC;AAAC,QAAAG,OAAG,KAAK,YAAY;AAAA,MAAC;AAAA,IAAC,OAAK;AAAC,YAAMF,MAAE,KAAK,eAAe,QAAQ;AAAE,UAAGE,GAAEH,GAAC,EAAE,CAAAC,IAAE,KAAGD,IAAE,GAAEC,IAAE,KAAGD,IAAE,GAAE,KAAK,WAAWD,KAAEE,GAAC;AAAA,eAAU,EAAED,GAAC,GAAE;AAAC,cAAME,MAAED,GAAED,GAAC;AAAE,QAAAE,QAAI,CAACD,IAAE,IAAGA,IAAE,EAAE,IAAEC,KAAE,KAAK,WAAWH,KAAEE,GAAC;AAAA,MAAE,MAAM,YAAUC,OAAKF,IAAE,OAAO,KAAGC,IAAE,KAAGC,IAAE,CAAC,GAAED,IAAE,KAAGC,IAAE,CAAC,GAAE,KAAK,WAAWH,KAAEE,GAAC,GAAE,KAAK,aAAa;AAAM,WAAK,eAAe,QAAQA,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWF,KAAEC,KAAE;AAAC,YAAOD,IAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAqB,KAAI;AAAmB,aAAK,kBAAkBA,KAAEC,GAAC;AAAE;AAAA,MAAM,KAAI;AAAkB,aAAK,iBAAiBD,KAAEC,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBD,KAAEC,KAAE;AAAC,QAAG,CAACD,IAAE;AAAO,UAAME,MAAE,KAAK,iBAAiB,YAAYF,IAAE,GAAG,GAAEG,MAAEH,IAAE,QAAM;AAAG,QAAG,EAAEE,GAAC,KAAGC,OAAG,EAAE;AAAO,UAAME,MAAEH,IAAE,OAAMI,MAAEJ,IAAE;AAAO,QAAG,CAACG,OAAG,CAACC,IAAE;AAAO,UAAMC,MAAEF,MAAEC,KAAEE,MAAER,IAAE,UAAQ,GAAES,KAAEV,GAAE,eAAe,GAAEW,MAAEV,IAAE;AAAY,QAAGU,KAAE;AAAC,UAAIT,MAAES,IAAE,GAAER,MAAEQ,IAAE;AAAE,qBAAaV,IAAE,qBAAmBC,OAAGE,MAAEI,MAAEC,KAAEN,OAAGC,MAAGM,GAAE,UAAU,CAACR,KAAE,CAACC,GAAC;AAAA,IAAC;AAAC,QAAIS,KAAEX,IAAE,YAAU;AAAE,IAAAA,IAAE,oBAAkBW,KAAE,CAACA,KAAG,KAAK,iBAAeA,MAAG,KAAK,eAAcA,MAAGF,GAAE,OAAOE,KAAE,CAAC;AAAE,QAAII,MAAEf,IAAE,WAAS,GAAEgB,MAAEhB,IAAE,WAAS;AAAE,QAAGe,OAAGC,KAAE;AAAC,UAAG,KAAK,cAAa;AAAC,cAAMhB,MAAE,IAAE,KAAK,cAAaC,MAAE,KAAK,IAAID,GAAC,GAAEE,MAAE,KAAK,IAAIF,GAAC,GAAEG,MAAEY,MAAEb,MAAEc,MAAEf;AAAE,QAAAc,MAAEA,MAAEd,MAAEe,MAAEd,KAAEc,MAAEb;AAAA,MAAC;AAAC,MAAAM,GAAE,UAAUM,KAAEC,GAAC;AAAA,IAAC;AAAC,UAAMC,KAAE,KAAK,kBAAkB;AAAE,UAAIA,MAAGR,GAAE,MAAMQ,IAAEA,EAAC;AAAE,UAAMC,KAAEjB,IAAE,SAAS;AAAE,IAAAiB,MAAGT,GAAE,OAAOS,EAAC,GAAET,GAAE,UAAUR,IAAE,IAAGA,IAAE,EAAE,GAAE,KAAK,KAAKQ,IAAE,KAAE,GAAE,KAAK,UAAUT,KAAEG,GAAC,GAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,iBAAiBH,KAAEC,KAAE;AAAC,QAAG,CAACD,IAAE;AAAO,UAAME,MAAEF,IAAE;AAAe,QAAG,CAACE,IAAE;AAAO,UAAMC,MAAEH,IAAE,QAAM,IAAGI,MAAEJ,IAAE,OAAMK,MAAED,MAAEA,IAAE,OAAKA,IAAE,OAAK,GAAEE,MAAEH,OAAGE,MAAEF,MAAEE,MAAE,GAAEE,MAAER,GAAE,eAAe;AAAE,IAAAK,OAAGG,IAAE,UAAU,MAAG,EAAEH,IAAE,OAAKA,IAAE,OAAM,MAAG,EAAEA,IAAE,OAAKA,IAAE,KAAK;AAAE,UAAMI,MAAER,IAAE;AAAY,QAAGQ,KAAE;AAAC,UAAIP,MAAEO,IAAE,GAAEN,MAAEM,IAAE;AAAE,qBAAaR,IAAE,mBAAiBI,QAAIH,OAAGG,IAAE,OAAKA,IAAE,MAAKF,OAAGE,IAAE,OAAKA,IAAE,SAAOH,OAAGK,KAAEJ,OAAGI,MAAGC,IAAE,UAAU,CAACN,KAAE,CAACC,GAAC;AAAA,IAAC;AAAC,UAAII,OAAGC,IAAE,MAAMD,KAAEA,GAAC;AAAE,QAAIG,KAAET,IAAE,YAAU;AAAE,IAAAA,IAAE,oBAAkBS,KAAE,CAACA,KAAG,KAAK,iBAAeA,MAAG,KAAK,eAAcA,MAAGF,IAAE,OAAOE,KAAE,CAAC;AAAE,QAAIC,MAAEV,IAAE,WAAS,GAAEW,KAAEX,IAAE,WAAS;AAAE,QAAGU,OAAGC,IAAE;AAAC,UAAG,KAAK,cAAa;AAAC,cAAMX,MAAE,IAAE,KAAK,cAAaC,MAAE,KAAK,IAAID,GAAC,GAAEE,MAAE,KAAK,IAAIF,GAAC,GAAEG,MAAEO,MAAER,MAAES,KAAEV;AAAE,QAAAS,MAAEA,MAAET,MAAEU,KAAET,KAAES,KAAER;AAAA,MAAC;AAAC,MAAAI,IAAE,UAAUG,KAAEC,EAAC;AAAA,IAAC;AAAC,UAAMI,MAAE,KAAK,kBAAkB;AAAE,UAAIA,OAAGR,IAAE,MAAMQ,KAAEA,GAAC;AAAE,UAAMC,MAAEf,IAAE,SAAS;AAAE,IAAAe,OAAGT,IAAE,OAAOS,GAAC,GAAET,IAAE,UAAUN,IAAE,IAAGA,IAAE,EAAE,GAAE,KAAK,KAAKM,KAAEP,IAAE,0BAA0B;AAAE,eAAUiB,MAAKf,IAAE,KAAGe,MAAGA,GAAE,UAAQA,GAAE,YAAUnB,GAAE,MAAM,0BAAyBmB,EAAC,GAAE,KAAK,WAAWA,GAAE,QAAOA,GAAE,UAASA,GAAE,UAAU,GAAE,KAAK,aAAa;AAAM,SAAK,IAAI;AAAA,EAAC;AAAA,EAAC,eAAejB,KAAEC,KAAEC,KAAE;AAAC,QAAG,CAACF,IAAE;AAAO,QAAG,CAACI,GAAEH,GAAC,EAAE;AAAO,SAAID,IAAE,UAAQ,OAAK,EAAE;AAAO,UAAMG,MAAEJ,GAAE,eAAe;AAAE,QAAIK,MAAEJ,IAAE,SAAO;AAAE,IAAAI,MAAE,CAACA,KAAEA,OAAGD,IAAE,OAAOC,MAAE,CAAC;AAAE,UAAMC,MAAEL,IAAE,WAAS,GAAEM,MAAEN,IAAE,WAAS;AAAE,KAACK,OAAGC,QAAIH,IAAE,UAAUE,KAAEC,GAAC;AAAE,UAAMC,MAAE,KAAK,kBAAkB;AAAE,UAAIA,OAAGJ,IAAE,MAAMI,KAAEA,GAAC,GAAEJ,IAAE,UAAUF,IAAE,GAAEA,IAAE,CAAC,GAAE,KAAK,KAAKE,KAAE,KAAE,GAAE,KAAK,SAASH,KAAEE,GAAC,GAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,oBAAoBF,KAAEC,KAAEC,KAAE;AAAC,QAAIC,OAAG,WAASH,IAAE,aAAWA,IAAE,aAAW,KAAGE,KAAEE,MAAE,WAASJ,IAAE,WAASA,IAAE,WAAS;AAAE,QAAG,MAAIG,IAAE,QAAO;AAAK,IAAAA,MAAE,MAAIA,MAAE,CAACA;AAAG,QAAIE,MAAE;AAAE,UAAMC,MAAE,MAAGH;AAAE,WAAKE,MAAEC,MAAG,CAAAD,OAAGF;AAAE,WAAKE,MAAE,CAACC,MAAG,CAAAD,OAAGF;AAAE,UAAMI,MAAES,GAAE;AAAE,IAAAN,GAAEH,KAAEN,GAAC,GAAEM,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,KAAGD,KAAEC,IAAE,CAAC,KAAGD;AAAE,UAAME,MAAE,CAAC,CAACD,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,GAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,GAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,GAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC;AAAE,WAAKH,MAAE,MAAK,CAAAA,OAAG;AAAI,WAAKA,MAAE,IAAG,CAAAA,OAAG;AAAI,UAAMM,MAAE,KAAK,IAAIN,MAAE,CAAC,GAAEW,MAAE,KAAK,IAAIX,MAAE,CAAC,GAAEY,MAAE,CAACb,MAAEY,KAAEE,KAAEd,MAAEO;AAAE,QAAIQ,IAAEC,IAAEL,KAAEM;AAAE,IAAAf,OAAG,WAASL,IAAE,UAAQA,IAAE,UAAQE,MAAE,KAAGa,OAAG,WAASf,IAAE,UAAQA,IAAE,UAAQE,MAAE,KAAGQ,KAAEQ,KAAEJ,MAAE,OAAO,WAAUK,KAAEC,KAAE,CAAC,OAAO;AAAU,eAAUX,MAAKD,KAAE;AAAC,YAAMR,MAAES,GAAE,CAAC,GAAER,MAAEQ,GAAE,CAAC,GAAEP,MAAEQ,MAAEV,MAAEe,MAAEd,KAAEE,MAAE,CAACY,MAAEf,MAAEU,MAAET;AAAE,MAAAiB,KAAE,KAAK,IAAIA,IAAEhB,GAAC,GAAEY,MAAE,KAAK,IAAIA,KAAEX,GAAC,GAAEgB,KAAE,KAAK,IAAIA,IAAEjB,GAAC,GAAEkB,KAAE,KAAK,IAAIA,IAAEjB,GAAC;AAAA,IAAC;AAAC,IAAAW,MAAE,KAAK,MAAMA,MAAEX,GAAC,IAAEA;AAAE,QAAIkB,KAAEX,MAAEQ,KAAEH,MAAED,MAAEE,MAAEX,MAAEF,KAAEmB,KAAEP,MAAEG,KAAER,MAAEI,MAAEG,KAAEZ,MAAEF,KAAEoB,KAAEb,MAAES,KAAEJ,MAAED,MAAEE,MAAEX,MAAEF,KAAEqB,KAAET,MAAEI,KAAET,MAAEI,MAAEG,KAAEZ,MAAEF;AAAE,UAAMsB,KAAE,IAAE,KAAK,OAAOL,KAAEN,OAAGX,GAAC,GAAEuB,KAAE,CAAC;AAAE,aAAQjB,KAAE,GAAEA,KAAEgB,IAAEhB,KAAI,CAAAY,MAAGL,KAAEM,MAAGL,IAAEM,MAAGP,KAAEQ,MAAGP,IAAES,GAAE,KAAK,CAAC,CAACL,IAAEC,EAAC,GAAE,CAACC,IAAEC,EAAC,CAAC,CAAC;AAAE,WAAM,EAAC,OAAME,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,cAAgBf,GAAC;AAAA,EAAC,YAAYZ,KAAEC,KAAE;AAAC,UAAMD,KAAEC,GAAC,GAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,QAAM,KAAK,QAAM,IAAE,GAAE,KAAK,QAAM,KAAK,QAAM,KAAG,GAAE,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,IAAID,IAAE,KAAK,OAAM,KAAK,OAAM,KAAK,QAAM,KAAK,OAAM,KAAK,QAAM,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAOK,GAAE,KAAK,OAAM,KAAK,OAAM,KAAK,OAAM,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,cAAcL,KAAE;AAAC,QAAGA,OAAG,EAAE,KAAK,aAAW,GAAG,KAAG,EAAEA,GAAC,EAAE,MAAK,aAAaA,IAAE,OAAM,CAAC;AAAA,aAAUe,GAAEf,GAAC,EAAE,MAAK,aAAaA,IAAE,OAAM,CAAC;AAAA,aAAUgB,GAAEhB,GAAC,GAAE;AAAC,YAAMC,MAAE,EAAED,GAAC;AAAE,MAAAC,OAAG,KAAK,aAAaA,IAAE,OAAM,CAAC;AAAA,IAAC,MAAM,SAAQ,MAAM,yCAAyC;AAAA,EAAC;AAAA,EAAC,gBAAgBD,KAAEC,KAAEC,KAAE;AAAC,QAAG,CAACF,OAAG,KAAK,aAAW,EAAE;AAAO,UAAMG,MAAE,MAAG,KAAK,cAAcD,OAAG,CAAC;AAAE,QAAG,EAAEF,GAAC,EAAE,MAAK,aAAaA,IAAE,OAAMG,GAAC;AAAA,aAAUY,GAAEf,GAAC,EAAE,MAAK,aAAaA,IAAE,OAAMG,GAAC;AAAA,aAAUa,GAAEhB,GAAC,GAAE;AAAC,YAAMC,MAAE,EAAED,GAAC;AAAE,MAAAC,OAAG,KAAK,aAAaA,IAAE,OAAME,GAAC;AAAA,IAAC,MAAM,SAAQ,MAAM,2CAA2C;AAAA,EAAC;AAAA,EAAC,gBAAgBH,KAAEC,KAAE;AAAC,MAAEA,GAAC,KAAGD,IAAE,oBAAkB,sCAAoCA,IAAE,gBAAgB,QAAM,sCAAoCA,IAAE,gBAAgB,QAAMA,IAAE,gBAAgB,kBAAgB,KAAK,aAAaC,IAAE,OAAM,CAAC,IAAE,MAAM,gBAAgBD,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,KAAEC,KAAE;AAAC,SAAK,cAAcD,GAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,KAAEC,KAAE;AAAC,SAAK,cAAcD,GAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,KAAEC,KAAE;AAAC,SAAK,cAAcD,GAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,KAAEC,KAAE;AAAC,SAAK,gBAAgBD,KAAE,MAAKC,IAAE,KAAK;AAAA,EAAC;AAAA,EAAC,mBAAmBD,KAAEC,KAAE;AAAC,SAAK,gBAAgBD,KAAE,MAAKC,IAAE,KAAK;AAAA,EAAC;AAAA,EAAC,aAAaD,KAAE;AAAC,SAAK,cAAcA,GAAC,GAAE,KAAK;AAAA,EAAY;AAAA,EAAC,cAAa;AAAC,SAAK;AAAA,EAAY;AAAA,EAAC,UAAUA,KAAEC,KAAE;AAAC,UAAK,EAAC,KAAIC,IAAC,IAAEF,KAAEG,MAAEH,IAAE,UAAQ;AAAE,QAAII,MAAED,MAAEF,KAAEK,MAAEL;AAAE,UAAMM,MAAE,KAAK,iBAAiB,YAAYL,GAAC;AAAE,KAACD,OAAG,EAAEM,GAAC,MAAIH,MAAED,MAAEI,IAAE,OAAMD,MAAEC,IAAE,SAAQ,KAAK,OAAO,KAAK,YAAY,CAAC,CAACH,MAAE,GAAE,CAACE,MAAE,CAAC,CAAC,GAAE,CAAC,GAAE,KAAK,OAAO,KAAK,YAAY,CAAC,CAACF,MAAE,GAAEE,MAAE,CAAC,CAAC,GAAE,CAAC,GAAE,KAAK,OAAO,KAAK,YAAY,CAACF,MAAE,GAAE,CAACE,MAAE,CAAC,CAAC,GAAE,CAAC,GAAE,KAAK,OAAO,KAAK,YAAY,CAACF,MAAE,GAAEE,MAAE,CAAC,CAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,SAASN,KAAEC,KAAE;AAAC,QAAG,CAACA,OAAG,MAAIA,IAAE,OAAO;AAAO,SAAK,oBAAkB,KAAK,kBAAgB,IAAIG;AAAG,UAAMF,MAAE,GAAGF,GAAC,GAAE,CAACG,KAAEC,GAAC,IAAE,KAAK,gBAAgB,gBAAgBH,KAAEC,GAAC;AAAE,QAAIG,MAAE;AAAE,YAAOL,IAAE,qBAAoB;AAAA,MAAC,KAAI;AAAO,QAAAK,MAAEF,MAAE;AAAE;AAAA,MAAM,KAAI;AAAQ,QAAAE,MAAE,CAACF,MAAE;AAAA,IAAC;AAAC,QAAIG,MAAE;AAAE,YAAON,IAAE,mBAAkB;AAAA,MAAC,KAAI;AAAS,QAAAM,MAAEF,MAAE;AAAE;AAAA,MAAM,KAAI;AAAM,QAAAE,MAAE,CAACF,MAAE;AAAE;AAAA,MAAM,KAAI;AAAW,QAAAE,MAAEF,MAAE;AAAA,IAAC;AAAC,SAAK,OAAO,KAAK,YAAY,CAAC,CAACD,MAAE,IAAEE,KAAE,CAACD,MAAE,IAAEE,GAAC,CAAC,GAAE,CAAC,GAAE,KAAK,OAAO,KAAK,YAAY,CAAC,CAACH,MAAE,IAAEE,KAAED,MAAE,IAAEE,GAAC,CAAC,GAAE,CAAC,GAAE,KAAK,OAAO,KAAK,YAAY,CAACH,MAAE,IAAEE,KAAE,CAACD,MAAE,IAAEE,GAAC,CAAC,GAAE,CAAC,GAAE,KAAK,OAAO,KAAK,YAAY,CAACH,MAAE,IAAEE,KAAED,MAAE,IAAEE,GAAC,CAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaN,KAAEC,KAAE;AAAC,QAAGD,IAAE,YAAUE,OAAKF,KAAE;AAAC,YAAMA,MAAEE,MAAEA,IAAE,SAAO;AAAE,UAAGF,MAAE,GAAE;AAAC,aAAK,OAAO,KAAK,YAAYE,IAAE,CAAC,CAAC,GAAED,GAAC;AAAE,iBAAQE,MAAE,GAAEA,MAAEH,KAAEG,MAAI,MAAK,OAAO,KAAK,YAAYD,IAAEC,GAAC,CAAC,GAAEF,GAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,KAAEC,KAAE;AAAC,IAAAD,IAAE,CAAC,IAAEC,MAAE,KAAK,UAAQ,KAAK,QAAMD,IAAE,CAAC,IAAEC,MAAGD,IAAE,CAAC,IAAEC,MAAE,KAAK,UAAQ,KAAK,QAAMD,IAAE,CAAC,IAAEC,MAAGD,IAAE,CAAC,IAAEC,MAAE,KAAK,UAAQ,KAAK,QAAMD,IAAE,CAAC,IAAEC,MAAGD,IAAE,CAAC,IAAEC,MAAE,KAAK,UAAQ,KAAK,QAAMD,IAAE,CAAC,IAAEC;AAAA,EAAE;AAAC;AAAC,IAAM,IAAN,cAAgBW,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,CAAC,GAAE,CAAC,GAAE,KAAK,mBAAiB,GAAE,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,QAAQZ,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,UAAMC,MAAED,MAAEW,GAAE,CAAC;AAAE,SAAK,aAAa,GAAE,KAAK,qBAAqBV,GAAC,GAAE,KAAK,eAAa,EAAEN,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,IAAGA,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,CAAC,GAAE,KAAK,oBAAkBA,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,IAAEM,KAAE,KAAK,YAAUH;AAAE,UAAMI,MAAEN,QAAI,qBAAmBA,IAAE,QAAM,UAAQA,IAAE,kBAAgB,oBAAkBA,IAAE;AAAM,WAAO,KAAK,eAAaM,MAAEH,MAAE,GAAE,KAAK,eAAa,OAAG,KAAK,WAAWH,KAAEC,GAAC,GAAE,KAAK;AAAA,EAAY;AAAA,EAAC,cAAcF,KAAEC,KAAE;AAAC,SAAK,aAAaD,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAEC,KAAE;AAAC,SAAK,aAAaD,GAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,KAAEC,KAAE;AAAC,SAAK,aAAaD,GAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,KAAEC,KAAE;AAAC,SAAK,aAAaD,GAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,SAAK,eAAeL,KAAEE,GAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBF,KAAEC,KAAE;AAAC,SAAK,eAAeD,KAAEC,IAAE,KAAK;AAAA,EAAC;AAAA,EAAC,mBAAmBD,KAAEC,KAAE;AAAC,SAAK,eAAeD,KAAEC,IAAE,KAAK;AAAA,EAAC;AAAA,EAAC,gBAAgBD,KAAEC,KAAE;AAAC,IAAAD,IAAE,oBAAkB,sCAAoCA,IAAE,gBAAgB,QAAM,sCAAoCA,IAAE,gBAAgB,QAAMA,IAAE,gBAAgB,kBAAgB,KAAK,aAAaC,GAAC,IAAE,MAAM,gBAAgBD,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,KAAE;AAAA,EAAC;AAAA,EAAC,cAAa;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAEC,KAAE;AAAC,UAAK,EAAC,KAAIC,IAAC,IAAEF,KAAEG,MAAEH,IAAE,UAAQ,GAAEK,MAAE,KAAK,iBAAiB,YAAYH,GAAC;AAAE,QAAG,EAAEG,GAAC,KAAG,MAAIA,IAAE,UAAQ,MAAIJ,IAAE;AAAO,UAAMK,MAAEL,MAAE,KAAK,kBAAkB,GAAEM,MAAED,MAAEH,OAAGE,IAAE,QAAMA,IAAE,SAAQG,MAAE,KAAK,mBAAmB,KAAK,YAAY,GAAEC,KAAE,KAAK;AAAiB,SAAK,IAAID,IAAE,CAAC,CAAC,IAAED,MAAE,IAAEE,MAAG,KAAK,IAAID,IAAE,CAAC,CAAC,IAAEF,MAAE,IAAEG,OAAI,KAAK,eAAa;AAAA,EAAG;AAAA,EAAC,SAASR,KAAEC,KAAE;AAJn0a;AAIo0a,UAAMC,MAAE,KAAK;AAAU,QAAG,CAACA,IAAE;AAAO,UAAMC,MAAED,IAAE,IAAIF,GAAC;AAAE,QAAG,CAACG,IAAE;AAAO,UAAK,EAAC,MAAKC,KAAE,YAAWC,IAAC,IAAEF;AAAE,QAAG,GAAC,KAAAE,OAAA,gBAAAA,IAAG,qBAAH,mBAAqB,QAAO;AAAO,UAAMC,MAAEN,IAAE,UAAQ,GAAE,EAAC,aAAYO,KAAE,SAAQC,GAAC,IAAER,KAAES,MAAEF,MAAE,GAAGA,KAAEC,MAAG,GAAEF,GAAC,IAAE,GAAEI,KAAER,GAAEE,GAAC,EAAE,CAAC,GAAEU,MAAET,IAAE,kBAAiBU,MAAE,6BAAyB,KAAAf,IAAE,YAAF,mBAAW,OAAKgB,KAAEO,GAAET,KAAEJ,IAAE,EAAC,OAAMJ,MAAE,GAAE,OAAM,GAAE,SAAQ,GAAE,SAAQ,GAAE,QAAOqB,GAAE3B,IAAE,mBAAmB,GAAE,QAAO,EAAEA,IAAE,iBAAiB,GAAE,cAAa,KAAI,YAAWc,KAAE,KAAK,IAAI,MAAI,KAAK,IAAIL,OAAG,GAAE,CAAC,CAAC,GAAE,YAAWT,IAAE,KAAK,cAAY,QAAO,OAAM,MAAG,eAAce,IAAC,CAAC,GAAEE,KAAE,KAAK,mBAAmB,KAAK,YAAY,GAAEC,KAAED,GAAE,CAAC,GAAEJ,MAAEI,GAAE,CAAC;AAAE,eAAUlB,OAAKiB,GAAE,OAAO,KAAGE,KAAEnB,IAAE,YAAUmB,KAAEnB,IAAE,gBAAcc,MAAE,CAACd,IAAE,gBAAcc,MAAE,CAACd,IAAE,UAAS;AAAC,WAAK,eAAa;AAAG;AAAA,IAAK;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAE;AAAC,QAAIC,MAAE;AAAK,QAAGe,GAAEhB,GAAC,GAAE;AAAC,YAAME,MAAEF;AAAE,MAAAC,MAAE,CAAC,CAAC,CAACC,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC,CAAC;AAAA,IAAC,WAAS,EAAEF,GAAC,EAAE,CAAAC,MAAED,IAAE;AAAA,SAAU;AAAC,UAAG,CAACe,GAAEf,GAAC,EAAE;AAAO,MAAAC,MAAED,IAAE;AAAA,IAAK;AAAC,UAAME,MAAE,KAAK,mBAAmB,KAAK,YAAY;AAAE,QAAG,KAAK,gBAAgBA,KAAED,GAAC,MAAI,KAAK,eAAa,OAAI,CAAC,KAAK,cAAa;AAAC,YAAMD,MAAE,KAAK,qBAAqB,KAAK,gBAAgB,IAAE,KAAK,kBAAkB;AAAE,WAAK,UAAUE,KAAED,KAAED,GAAC,MAAI,KAAK,eAAa;AAAA,IAAG;AAAA,EAAC;AAAA,EAAC,eAAeA,KAAEC,KAAE;AAAC,QAAIC,MAAE;AAAK,QAAGc,GAAEhB,GAAC,GAAE;AAAC,YAAMC,MAAED;AAAE,MAAAE,MAAE,CAAC,CAAC,CAACD,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC,CAAC;AAAA,IAAC,WAAS,EAAED,GAAC,EAAE,CAAAE,MAAEF,IAAE;AAAA,SAAU;AAAC,UAAG,CAACe,GAAEf,GAAC,EAAE;AAAO,MAAAE,MAAEF,IAAE;AAAA,IAAK;AAAC,UAAMG,MAAE,KAAK,mBAAmB,KAAK,YAAY,GAAEC,OAAGH,OAAG,KAAG,KAAK,kBAAkB,GAAEI,MAAE,KAAK,qBAAqB,KAAK,gBAAgB,IAAE,KAAK,kBAAkB;AAAE,SAAK,UAAUF,KAAED,KAAEE,MAAE,IAAEC,GAAC,MAAI,KAAK,eAAa;AAAA,EAAG;AAAA,EAAC,gBAAgBL,KAAEC,KAAE;AAAC,QAAIC,MAAE;AAAE,eAAUC,OAAKF,KAAE;AAAC,YAAMA,MAAEE,IAAE;AAAO,eAAQC,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,cAAMH,MAAEE,IAAEC,MAAE,CAAC,GAAEC,MAAEF,IAAEC,GAAC;AAAE,YAAGH,IAAE,CAAC,IAAED,IAAE,CAAC,KAAGK,IAAE,CAAC,IAAEL,IAAE,CAAC,EAAE;AAAS,SAACK,IAAE,CAAC,IAAEJ,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAII,IAAE,CAAC,IAAEJ,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,KAAG,IAAEC,QAAIA;AAAA,MAAG;AAAA,IAAC;AAAC,WAAO,MAAIA;AAAA,EAAC;AAAA,EAAC,UAAUF,KAAEC,KAAEC,KAAE;AAAC,eAAUC,OAAKF,KAAE;AAAC,YAAMA,MAAEE,IAAE;AAAO,eAAQC,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,cAAMH,MAAEE,IAAEC,MAAE,CAAC,GAAEC,MAAEF,IAAEC,GAAC;AAAE,YAAIE,OAAGD,IAAE,CAAC,IAAEJ,IAAE,CAAC,MAAII,IAAE,CAAC,IAAEJ,IAAE,CAAC,MAAII,IAAE,CAAC,IAAEJ,IAAE,CAAC,MAAII,IAAE,CAAC,IAAEJ,IAAE,CAAC;AAAG,YAAG,MAAIK,IAAE;AAAS,QAAAA,MAAE,KAAK,KAAKA,GAAC;AAAE,cAAMC,QAAIF,IAAE,CAAC,IAAEJ,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAII,IAAE,CAAC,IAAEJ,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAIK;AAAE,YAAG,KAAK,IAAIC,GAAC,IAAEL,KAAE;AAAC,gBAAMC,QAAIE,IAAE,CAAC,IAAEJ,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAII,IAAE,CAAC,IAAEJ,IAAE,CAAC,MAAID,IAAE,CAAC,IAAEC,IAAE,CAAC,MAAIK;AAAE,cAAGH,MAAE,CAACD,OAAGC,MAAEG,MAAEJ,IAAE,QAAM;AAAA,QAAE;AAAA,MAAC;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAC;AAAC,IAAM,IAAN,cAAgBU,GAAC;AAAA,EAAC,YAAYZ,KAAEC,KAAEC,KAAEC,KAAE;AAAC,UAAMF,KAAEC,GAAC,GAAE,KAAK,8BAA4BC,KAAE,KAAK,2BAAyB,IAAIH,MAAE,KAAK,OAAKA;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAEC,KAAE;AAAC,QAAG,CAACD,IAAE;AAAO,QAAG,EAAEA,GAAC,EAAE,MAAK,WAAWA,IAAE,OAAM,IAAE;AAAA,aAAUe,GAAEf,GAAC,EAAE,MAAK,WAAWA,IAAE,OAAM,IAAE;AAAA,aAAUgB,GAAEhB,GAAC,EAAE,MAAK,WAAW,EAAEA,GAAC,EAAE,OAAM,IAAE;AAAA,SAAM;AAAC,UAAG,CAAC,EAAEA,GAAC,EAAE;AAAO,cAAQ,IAAI,qDAAqD;AAAA,IAAC;AAAC,UAAME,MAAE,KAAK;AAAK,IAAAA,IAAE,YAAU,YAAU,OAAOD,MAAEA,MAAE,UAAQ,KAAK,MAAMA,IAAE,CAAC,CAAC,IAAE,MAAI,KAAK,MAAMA,IAAE,CAAC,CAAC,IAAE,MAAI,KAAK,MAAMA,IAAE,CAAC,CAAC,IAAE,OAAKA,IAAE,CAAC,KAAG,OAAK,MAAI,KAAIC,IAAE,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,gBAAgBF,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,QAAG,CAACL,OAAG,CAACC,OAAG,MAAIC,IAAE;AAAO,QAAG,EAAEF,GAAC,EAAE,MAAK,WAAWA,IAAE,OAAM,IAAE;AAAA,aAAUe,GAAEf,GAAC,EAAE,MAAK,WAAWA,IAAE,OAAM,KAAE;AAAA,SAAM;AAAC,UAAG,CAACgB,GAAEhB,GAAC,EAAE,QAAO,KAAK,QAAQ,IAAI,qDAAqD;AAAE,WAAK,WAAW,EAAEA,GAAC,EAAE,OAAM,IAAE;AAAA,IAAC;AAAC,UAAMM,MAAE,KAAK;AAAK,IAAAA,IAAE,cAAY,YAAU,OAAOL,MAAEA,MAAE,UAAQ,KAAK,MAAMA,IAAE,CAAC,CAAC,IAAE,MAAI,KAAK,MAAMA,IAAE,CAAC,CAAC,IAAE,MAAI,KAAK,MAAMA,IAAE,CAAC,CAAC,IAAE,OAAKA,IAAE,CAAC,KAAG,OAAK,MAAI,KAAIK,IAAE,YAAU,KAAK,IAAI,KAAK,cAAcJ,GAAC,GAAE,GAAE,GAAE,KAAK,aAAaC,GAAC,GAAE,KAAK,cAAcC,GAAC,GAAEE,IAAE,aAAWD,KAAEC,IAAE,OAAO;AAAA,EAAC;AAAA,EAAC,aAAaN,KAAE;AAAC,QAAG,KAAK,KAAK,KAAK,GAAE,EAAEA,GAAC,EAAE,MAAK,WAAWA,IAAE,OAAM,IAAE;AAAA,aAAUe,GAAEf,GAAC,EAAE,MAAK,WAAWA,IAAE,OAAM,IAAE;AAAA,SAAM;AAAC,UAAG,CAACgB,GAAEhB,GAAC,EAAE;AAAO,WAAK,WAAW,EAAEA,GAAC,EAAE,OAAM,IAAE;AAAA,IAAC;AAAC,SAAK,KAAK,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,SAAK,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAEC,KAAE;AAAC,UAAK,EAAC,oBAAmBC,KAAE,KAAIC,KAAE,WAAUE,IAAC,IAAEL,KAAEM,MAAEN,IAAE,UAAQ,GAAEO,MAAE,KAAK,iBAAiB,YAAYJ,GAAC;AAAE,QAAG,EAAEI,GAAC,EAAE;AAAO,QAAIC,MAAEP,OAAGM,IAAE,QAAMA,IAAE,SAAQE,KAAER;AAAE,IAAAA,QAAIO,MAAED,IAAE,OAAME,KAAEF,IAAE;AAAQ,UAAMG,MAAEG,GAAEV,GAAC,KAAG,SAAQI,OAAGM,GAAEN,IAAE,GAAG;AAAE,QAAII,KAAE,cAAaJ,MAAEP,IAAEO,GAAC,IAAEA;AAAE,IAAAL,QAAIS,KAAE,KAAK,yBAAyB,wBAAwBA,IAAET,GAAC,IAAG,KAAK,+BAA6B,CAACQ,OAAGL,QAAIM,KAAE,KAAK,yBAAyB,cAAcA,IAAEN,GAAC;AAAG,UAAMU,MAAE,KAAK,YAAY,CAAC,GAAE,CAAC,CAAC,GAAE,CAACC,KAAEC,EAAC,IAAE,KAAK,kBAAkB,GAAEC,KAAE,KAAK,cAAc,CAAC,GAAEC,KAAE,KAAK;AAAK,IAAAA,GAAE,KAAK,GAAEA,GAAE,aAAa,EAAC,KAAIb,MAAEY,KAAEF,KAAE,KAAIV,MAAEY,KAAED,IAAE,KAAI,CAACC,KAAED,IAAE,KAAIC,KAAEF,KAAE,KAAID,IAAE,CAAC,GAAE,KAAIA,IAAE,CAAC,EAAC,CAAC,GAAEI,GAAE,UAAUR,IAAE,CAACH,MAAE,GAAE,CAACC,KAAE,GAAED,KAAEC,EAAC,GAAEU,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,SAASnB,KAAEC,KAAE;AAAC,QAAG,CAACA,OAAG,MAAIA,IAAE,OAAO;AAAO,SAAK,oBAAkB,KAAK,kBAAgB,IAAIG;AAAG,UAAMF,MAAE,GAAGF,GAAC;AAAE,IAAAE,IAAE,QAAM,KAAK,cAAcD,GAAE,CAAC,CAAC;AAAE,UAAME,MAAE,KAAK,gBAAgB,cAAcF,KAAEC,GAAC;AAAE,QAAG,CAACC,IAAE;AAAO,UAAK,EAAC,MAAKC,KAAE,SAAQC,KAAE,SAAQC,KAAE,QAAOE,IAAC,IAAEL,KAAEM,KAAEL,IAAE,CAAC,KAAGC,MAAE,MAAIK,MAAEN,IAAE,CAAC,KAAGE,MAAE,MAAIK,KAAE,KAAK,MAAKI,MAAE,KAAK,YAAY,CAAC,GAAE,CAAC,CAAC,GAAE,CAACC,KAAEC,EAAC,IAAE,KAAK,kBAAkB,GAAEC,KAAE;AAAE,IAAAP,GAAE,KAAK,GAAEA,GAAE,aAAa,EAAC,KAAIO,KAAEF,KAAE,KAAIE,KAAED,IAAE,KAAI,CAACC,KAAED,IAAE,KAAIC,KAAEF,KAAE,KAAID,IAAE,CAAC,IAAEG,KAAET,IAAE,KAAIM,IAAE,CAAC,IAAEG,KAAER,IAAC,CAAC,GAAEC,GAAE,UAAUH,KAAE,GAAE,CAAC,GAAEG,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,gBAAgBX,KAAEC,KAAE;AAAC,QAAG,CAACD,IAAE;AAAO,QAAG,EAAC,oBAAmBE,KAAE,QAAOC,KAAE,SAAQE,KAAE,SAAQC,KAAE,UAASC,KAAE,QAAOC,KAAE,WAAUC,IAAE,KAAIC,IAAC,IAAET;AAAE,UAAMU,KAAE,KAAK,iBAAiB,YAAYD,GAAC;AAAE,QAAG,EAAEC,EAAC,EAAE;AAAO,QAAG,EAAEX,GAAC,EAAE,MAAK,WAAWA,IAAE,OAAM,IAAE;AAAA,aAAUe,GAAEf,GAAC,EAAE,MAAK,WAAWA,IAAE,OAAM,IAAE;AAAA,aAAUgB,GAAEhB,GAAC,EAAE,MAAK,WAAW,EAAEA,GAAC,EAAE,OAAM,IAAE;AAAA,SAAM;AAAC,UAAG,CAAC,EAAEA,GAAC,EAAE;AAAO,cAAQ,IAAI,uDAAuD;AAAA,IAAC;AAAC,UAAMe,MAAE,KAAK,MAAKC,MAAEH,GAAEH,GAAC,KAAG,SAAQC,MAAGE,GAAEF,GAAE,GAAG;AAAE,QAAIS,IAAEC,KAAE,cAAaV,KAAEX,IAAEW,EAAC,IAAEA;AAAE,QAAGT,QAAImB,KAAE,KAAK,yBAAyB,wBAAwBA,IAAEnB,GAAC,IAAG,KAAK,6BAA4B;AAAC,MAAAc,OAAGP,OAAIY,KAAE,KAAK,yBAAyB,cAAcA,IAAEZ,EAAC,IAAGW,KAAEL,IAAE,cAAcM,IAAE,QAAQ;AAAE,YAAMrB,MAAE,KAAK,cAAc,CAAC;AAAE,MAAAO,QAAIA,MAAE,IAAGF,MAAEA,OAAGL,MAAEK,MAAE,GAAEC,MAAEA,OAAGN,MAAEM,MAAE,GAAEH,QAAIA,OAAGH;AAAG,YAAMC,MAAEE,MAAEA,MAAEQ,GAAE,SAAO,GAAET,MAAEM,OAAGL,MAAEK,MAAEL,MAAEQ,GAAE,QAAM;AAAE,UAAG,MAAIJ,OAAG,MAAIN,OAAG,MAAIC,OAAG,MAAIG,OAAG,MAAIC,KAAE;AAAC,cAAMN,MAAE,IAAI;AAAU,QAAAA,IAAE,WAAW,GAAE,GAAE,CAACO,GAAC,EAAE,cAAcF,KAAEC,GAAC,EAAE,UAAUJ,KAAED,KAAE,CAAC,GAAEmB,GAAE,aAAapB,GAAC;AAAA,MAAC;AAAA,IAAC,MAAM,CAAAoB,KAAEL,IAAE,cAAcM,IAAE,QAAQ;AAAE,IAAAN,IAAE,KAAK,GAAEA,IAAE,YAAUK,IAAEL,IAAE,KAAK,SAAS,GAAEA,IAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,kBAAkBf,KAAEC,KAAE;AAAC,QAAG,CAACD,IAAE;AAAO,QAAG,EAAC,oBAAmBG,KAAE,UAASE,KAAE,WAAUC,KAAE,YAAWC,KAAE,WAAUE,IAAE,KAAIC,KAAE,OAAMC,GAAC,IAAEV;AAAE,UAAMc,MAAE,KAAK,iBAAiB,YAAYL,GAAC;AAAE,QAAG,EAAEK,GAAC,EAAE;AAAO,QAAIC;AAAE,QAAG,EAAEhB,GAAC,EAAE,CAAAgB,MAAEhB,IAAE;AAAA,aAAce,GAAEf,GAAC,EAAE,CAAAgB,MAAEhB,IAAE;AAAA,SAAU;AAAC,UAAG,CAACgB,GAAEhB,GAAC,EAAE,QAAO,EAAEA,GAAC,IAAE,KAAK,QAAQ,IAAI,yDAAyD,IAAE;AAAO,MAAAgB,MAAE,EAAEhB,GAAC,EAAE;AAAA,IAAK;AAAC,IAAAW,OAAIA,KAAEI,IAAE;AAAO,UAAMK,KAAEP,GAAEH,GAAC,KAAG,SAAQK,OAAGF,GAAEE,IAAE,GAAG;AAAE,QAAIM,KAAE,cAAaN,MAAEf,IAAEe,GAAC,IAAEA;AAAE,IAAAZ,QAAIkB,KAAE,KAAK,yBAAyB,wBAAwBA,IAAElB,GAAC,IAAG,KAAK,gCAA8BiB,MAAGX,OAAIY,KAAE,KAAK,yBAAyB,cAAcA,IAAEZ,EAAC;AAAI,UAAMa,KAAE,KAAK,IAAI,KAAK,cAAcN,GAAEL,EAAC,CAAC,GAAE,GAAE,GAAEY,KAAED,KAAED,GAAE,OAAMG,KAAE,KAAK,MAAKC,KAAED,GAAE,cAAcH,IAAE,UAAU;AAAE,QAAIK,IAAEG;AAAE,IAAAL,GAAE,KAAK,GAAE,KAAK,aAAanB,GAAC,GAAE,KAAK,cAAcC,GAAC,GAAE,WAASC,QAAIiB,GAAE,aAAWjB,MAAGiB,GAAE,YAAUF;AAAE,aAAQlB,OAAKY,IAAE,KAAGZ,MAAE,EAAEA,GAAC,GAAE,GAAGA,GAAC,GAAEA,OAAG,EAAEA,IAAE,UAAQ,IAAG;AAAC,MAAAsB,KAAE,KAAK,YAAYtB,IAAE,CAAC,CAAC;AAAE,eAAQJ,MAAE,GAAEA,MAAEI,IAAE,QAAOJ,OAAI;AAAC,QAAA6B,KAAE,KAAK,YAAYzB,IAAEJ,GAAC,CAAC;AAAE,cAAMC,MAAE6B,GAAEJ,IAAEG,EAAC,GAAE3B,MAAE,IAAI;AAAU,QAAAA,IAAE,cAAc,GAAEwB,GAAE,CAAC,IAAEJ,KAAE,CAAC,EAAE,UAAUC,IAAEA,IAAE,CAAC,EAAE,WAAW,GAAE,GAAE,KAAGtB,GAAC,GAAEwB,GAAE,aAAavB,GAAC,GAAEsB,GAAE,cAAYC,IAAED,GAAE,UAAU,GAAEA,GAAE,OAAOE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEF,GAAE,OAAOK,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAEL,GAAE,OAAO,GAAEE,KAAEG;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAL,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,WAAWxB,KAAEC,KAAE;AAAC,UAAMC,MAAE,KAAK;AAAK,QAAGA,IAAE,UAAU,GAAEF,IAAE,YAAUG,OAAKH,KAAE;AAAC,YAAMA,MAAEG,MAAEA,IAAE,SAAO;AAAE,UAAGH,MAAE,GAAE;AAAC,YAAII,MAAE,KAAK,YAAYD,IAAE,CAAC,CAAC;AAAE,QAAAD,IAAE,OAAOE,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC;AAAE,iBAAQH,MAAE,GAAEA,MAAED,KAAEC,MAAI,CAAAG,MAAE,KAAK,YAAYD,IAAEF,GAAC,CAAC,GAAEC,IAAE,OAAOE,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC;AAAE,QAAAH,OAAGC,IAAE,UAAU;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaF,KAAE;AAAC,YAAOA,KAAE;AAAA,MAAC,KAAK,EAAE;AAAK,aAAK,KAAK,UAAQ;AAAO;AAAA,MAAM,KAAK,EAAE;AAAM,aAAK,KAAK,UAAQ;AAAQ;AAAA,MAAM,KAAK,EAAE;AAAO,aAAK,KAAK,UAAQ;AAAA,IAAQ;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAE;AAAC,YAAOA,KAAE;AAAA,MAAC,KAAK,EAAE;AAAM,aAAK,KAAK,WAAS;AAAQ;AAAA,MAAM,KAAK,EAAE;AAAM,aAAK,KAAK,WAAS;AAAQ;AAAA,MAAM,KAAK,EAAE;AAAM,aAAK,KAAK,WAAS;AAAA,IAAO;AAAA,EAAC;AAAC;AAAC,SAAS8B,GAAE9B,KAAEC,KAAE;AAAC,QAAMC,MAAED,IAAE,CAAC,IAAED,IAAE,CAAC,GAAEG,MAAEF,IAAE,CAAC,IAAED,IAAE,CAAC;AAAE,SAAO,MAAI,KAAK,KAAG,KAAK,MAAMG,KAAED,GAAC;AAAC;AAAC,IAAM,IAAE,CAAAF,QAAGA,MAAE,EAAC,kBAAiBA,IAAE,kBAAiB,OAAM,CAAC,CAAC,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC,CAAC,EAAC,IAAE;AAA7I,IAAkJ4B,KAAE,CAAA5B,QAAG;AAAC,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAO,aAAOC,GAAE;AAAA,IAAK,KAAI;AAAQ,aAAOA,GAAE;AAAA,IAAM,KAAI;AAAS,aAAOA,GAAE;AAAA,IAAO,KAAI;AAAU,aAAOH,GAAE,SAAS,8EAA8E,GAAEG,GAAE;AAAA,EAAM;AAAC;AAA5W,IAA8W,IAAE,CAAAD,QAAG;AAAC,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAM,aAAOA,GAAE;AAAA,IAAI,KAAI;AAAS,aAAOA,GAAE;AAAA,IAAO,KAAI;AAAS,aAAOA,GAAE;AAAA,IAAO,KAAI;AAAW,aAAOA,GAAE;AAAA,EAAQ;AAAC;AAAhf,IAAkf,KAAG,CAACA,KAAEC,KAAEC,QAAI;AAAC,UAAOF,KAAE;AAAA,IAAC,KAAI;AAAe,aAAO,IAAEC,MAAEC;AAAA,IAAE,KAAI;AAAW,aAAOD;AAAA,IAAE,KAAI;AAAQ,aAAOA,MAAEC;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGF,KAAEE,MAAE,GAAE;AAAC,QAAMC,MAAE4B,GAAE/B,GAAC,GAAEI,MAAEU,GAAEd,IAAE,aAAa,GAAEK,MAAED,GAAEJ,IAAE,cAAc,GAAE,EAAC,QAAOM,KAAE,OAAMC,IAAC,IAAEH,KAAEI,MAAEN,OAAGF,IAAE,UAAQ,IAAGS,KAAE,EAAET,IAAE,mBAAmB,GAAEU,MAAE,EAAEV,IAAE,iBAAiB,GAAEW,KAAEU,GAAErB,GAAC,GAAEe,MAAEO,GAAEtB,IAAE,UAAU,GAAEgB,MAAED,MAAEb,OAAG,IAAEF,IAAE,YAAU;AAAE,SAAM,EAAC,OAAMW,IAAE,MAAKH,KAAE,qBAAoBC,IAAE,mBAAkBC,KAAE,MAAK,EAAC,QAAOL,KAAE,OAAMI,GAAEF,GAAC,GAAE,QAAO,EAAED,GAAC,GAAE,YAAWH,IAAC,GAAE,MAAK,EAAC,MAAKa,OAAG,GAAE,OAAMD,KAAE,OAAMR,IAAC,GAAE,YAAW,GAAE,mBAAkB,KAAE;AAAC;AAAC,IAAM,KAAG;AAAK,SAAS,GAAGP,KAAE;AAAC,MAAIC,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,MAAEN,IAAE,CAAC,GAAEO,MAAE;AAAE,SAAKA,MAAEP,IAAE,SAAQ,CAAAC,MAAED,IAAEO,GAAC,EAAE,CAAC,IAAED,IAAE,CAAC,GAAEJ,MAAEF,IAAEO,GAAC,EAAE,CAAC,IAAED,IAAE,CAAC,GAAEF,MAAE,MAAIH,MAAEC,MAAED,MAAE,KAAK,KAAG,GAAE,WAASE,OAAGC,MAAED,OAAG,MAAIH,IAAE,OAAOO,MAAE,GAAE,CAAC,GAAED,MAAED,QAAIA,MAAEC,KAAEA,MAAEN,IAAEO,GAAC,GAAEA,QAAKJ,MAAEC;AAAC;;;ACA18nB,IAAM4B,KAAE,KAAK;AAAb,IAAgBC,KAAED,KAAE;AAApB,IAAsBE,KAAE;AAAxB,IAA0BC,KAAE;AAA5B,IAA8BC,KAAE;AAAhC,IAAmCC,KAAE,KAAG;AAAxC,IAA2CC,KAAE,KAAK,KAAG;AAArD,IAAyDC,KAAE,EAAE,UAAU,kCAAkC;AAAE,SAASC,GAAEC,KAAE;AAAC,MAAG,CAACA,OAAG,CAACA,IAAE,KAAK,QAAO;AAAK,MAAIC;AAAE,UAAOD,IAAE,MAAK;AAAA,IAAC,KAAI;AAAM,aAAOA,IAAE;AAAA,IAAK,KAAI;AAAY,aAAOA;AAAA,IAAE,KAAI,iBAAgB;AAAC,YAAME,MAAEC,IAAG,iBAAiBH,GAAC;AAAE,UAAG,CAACE,IAAE,QAAO;AAAK,MAAAD,MAAEC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAI;AAAiB,MAAAD,MAAEE,IAAG,kBAAkBH,GAAC;AAAE;AAAA,IAAM,KAAI;AAAc,MAAAC,MAAEE,IAAG,qBAAqBH,GAAC;AAAE;AAAA,IAAM,KAAI;AAAc,MAAAC,MAAEE,IAAG,qBAAqBH,GAAC;AAAE;AAAA,IAAM,KAAI;AAAe,MAAAC,MAAEE,IAAG,sBAAsBH,GAAC;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAC,MAAEE,IAAG,eAAeH,GAAC;AAAA,EAAC;AAAC,SAAM,EAAC,MAAK,sBAAqB,QAAOC,IAAC;AAAC;AAAC,SAAS,GAAGD,KAAEC,KAAEC,KAAE;AAAC,UAAOD,IAAE,MAAK;AAAA,IAAC,KAAI;AAAqB,aAAO,GAAGD,KAAEC,IAAE,QAAOC,GAAC;AAAA,IAAE,KAAI;AAAiB,cAAMA,QAAIA,MAAE,EAAC,GAAE,GAAE,GAAE,EAAC,IAAGF,IAAE,WAAWC,KAAEC,GAAC;AAAE;AAAA,IAAM,KAAI;AAAgB,cAAMA,QAAIA,MAAE,EAAC,OAAM,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,CAAC,EAAC,IAAGF,IAAE,WAAWC,KAAEC,GAAC;AAAE;AAAA,IAAM,KAAI;AAAmB,cAAMA,QAAIA,MAAE,EAAC,OAAM,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC,EAAC,IAAGF,IAAE,WAAWC,KAAEC,GAAC;AAAE;AAAA,IAAM,KAAI,iBAAgB;AAAC,YAAMA,MAAE,EAAC,GAAE,GAAE,GAAE,EAAC;AAAE,MAAAF,IAAE,WAAWC,KAAEC,GAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAI,mBAAkB;AAAC,YAAMA,MAAE,IAAID;AAAE,MAAAD,IAAE,WAAWC,KAAEC,GAAC;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,SAAOF,IAAE,SAAS;AAAC;AAAC,SAAS,GAAGA,KAAE;AAAC,MAAG,CAACA,IAAE,QAAO;AAAE,UAAOA,IAAE,MAAK;AAAA,IAAC,KAAI;AAAA,IAAsC,KAAI;AAAA,IAAwC,KAAI;AAAA,IAAkC,KAAI;AAAA,IAAoC,KAAI;AAAA,IAAqC,KAAI;AAAA,IAA2B,KAAI;AAA+B,aAAO,KAAK,IAAIA,IAAE,MAAM;AAAA,IAAE;AAAQ,aAAO;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,KAAE;AAAC,MAAG,CAACA,IAAE,QAAO;AAAE,UAAOA,IAAE,MAAK;AAAA,IAAC,KAAI;AAA0B,aAAO,KAAK,IAAI,MAAGA,IAAE,KAAK;AAAA,IAAE,KAAI;AAA2B,aAAO,KAAK,IAAIA,IAAE,IAAI;AAAA,IAAE,KAAI;AAAA,IAA8B,KAAI;AAA2B,aAAO,KAAK,IAAIA,IAAE,MAAM;AAAA,IAAE,KAAI;AAAwB,aAAO,KAAK,IAAI,MAAGA,IAAE,MAAM;AAAA,IAAE,KAAI;AAAyB,aAAO,KAAK,IAAI,KAAK,IAAI,EAAEA,IAAE,OAAO,CAAC,GAAE,KAAK,IAAI,EAAEA,IAAE,OAAO,CAAC,CAAC;AAAA,IAAE,KAAI;AAAA,IAA2B,KAAI;AAAkC,aAAO,KAAK,IAAIA,IAAE,MAAM;AAAA,IAAE,KAAI;AAAmC,aAAO,KAAK,IAAIA,IAAE,MAAM;AAAA,IAAE,KAAI;AAAA,IAA2B,KAAI;AAAA,IAA0B;AAAQ,aAAO;AAAA,IAAE,KAAI;AAAmC,aAAM,MAAG,KAAK,IAAI,KAAK,IAAIA,IAAE,SAAS,GAAE,KAAK,IAAIA,IAAE,OAAO,CAAC;AAAA,IAAE,KAAI;AAAyB,aAAO,KAAK,IAAIA,IAAE,SAAS;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,KAAE;AAAC,MAAG,CAACA,IAAE,QAAO;AAAE,MAAIC,MAAE;AAAE,aAAUC,OAAKF,IAAE,CAAAC,OAAG,GAAGC,GAAC;AAAE,SAAOD;AAAC;AAAC,IAAMG,MAAN,MAAQ;AAAA,EAAC,qBAAqBJ,KAAEC,KAAEC,KAAEG,KAAEC,KAAE;AAAC,WAAON,QAAIA,MAAE,CAAC,GAAE,GAAE,GAAE,CAAC,IAAGC,MAAE,KAAK,gBAAgBD,KAAEC,KAAEC,KAAEG,KAAEC,GAAC,IAAEN;AAAA,EAAC;AAAA,EAAC,OAAO,SAASA,KAAE;AAAC,UAAMC,MAAE,KAAK,IAAI,KAAK,IAAID,IAAE,CAAC,CAAC,GAAE,KAAK,IAAIA,IAAE,CAAC,CAAC,CAAC,GAAEE,MAAE,KAAK,IAAI,KAAK,IAAIF,IAAE,CAAC,CAAC,GAAE,KAAK,IAAIA,IAAE,CAAC,CAAC,CAAC;AAAE,WAAO,KAAK,KAAKC,MAAEA,MAAEC,MAAEA,GAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBF,KAAEC,KAAEC,KAAEG,KAAE;AAAC,QAAIC,MAAE;AAAG,UAAMC,MAAEC,GAAE;AAAE,QAAGP,OAAGA,IAAE,eAAe,YAAUQ,OAAKR,IAAE,gBAAe;AAAC,YAAMA,MAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAE,MAAAQ,IAAE,aAAWC,GAAEH,KAAEE,IAAE,QAAQ,GAAER,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAE,KAAK,qBAAqBA,KAAEQ,IAAE,QAAOP,KAAE,GAAEG,GAAC,GAAEE,IAAE,CAAC,KAAGN,IAAE,CAAC,GAAEM,IAAE,CAAC,KAAGN,IAAE,CAAC,GAAEM,IAAE,CAAC,KAAGN,IAAE,CAAC,GAAEM,IAAE,CAAC,KAAGN,IAAE,CAAC,GAAEK,OAAGN,IAAE,CAAC,IAAEO,IAAE,CAAC,GAAEP,IAAE,CAAC,IAAEO,IAAE,CAAC,GAAEP,IAAE,CAAC,IAAEO,IAAE,CAAC,GAAEP,IAAE,CAAC,IAAEO,IAAE,CAAC,GAAED,MAAE,UAAKN,IAAE,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,GAAEO,IAAE,CAAC,CAAC,GAAEP,IAAE,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,GAAEO,IAAE,CAAC,CAAC,GAAEP,IAAE,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,GAAEO,IAAE,CAAC,CAAC,GAAEP,IAAE,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,GAAEO,IAAE,CAAC,CAAC;AAAA,IAAG;AAAC,WAAOP;AAAA,EAAC;AAAA,EAAC,gBAAgBA,KAAEC,KAAEC,KAAEG,KAAEC,KAAE;AAAC,QAAG,GAAGL,GAAC,GAAE;AAAC,YAAMM,MAAE,KAAK,sBAAsBP,KAAEC,IAAE,cAAaC,KAAEG,KAAEC,GAAC,GAAEG,MAAE,GAAGR,IAAE,OAAO;AAAE,aAAOQ,MAAE,MAAIF,IAAE,CAAC,KAAGE,KAAEF,IAAE,CAAC,KAAGE,KAAEF,IAAE,CAAC,KAAGE,KAAEF,IAAE,CAAC,KAAGE,MAAGF;AAAA,IAAC;AAAC,WAAO,KAAK,qBAAqBP,KAAEC,KAAEK,GAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBN,KAAEC,KAAEC,KAAEG,KAAEE,KAAE;AAAC,QAAIE,MAAE;AAAG,QAAG,CAACR,IAAE,QAAOD;AAAE,eAAUW,OAAKV,KAAE;AAAC,UAAG,CAACU,IAAE;AAAS,UAAIV,MAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAE,cAAOU,IAAE,MAAK;AAAA,QAAC,KAAI;AAAA,QAAe,KAAI;AAAA,QAAiB,KAAI;AAAA,QAAe,KAAI;AAAkB;AAAA,QAAM,KAAI;AAAA,QAAiB,KAAI;AAAA,QAAmB,KAAI,qBAAoB;AAAC,gBAAMX,MAAEW;AAAE,cAAIT,MAAEF,IAAE;AAAM,kBAAME,QAAIF,IAAE,aAAW,EAAE,UAAQA,IAAE,cAAY,EAAE,QAAME,OAAG,qBAAmBA,OAAG,GAAED,IAAE,CAAC,IAAE,CAACC,KAAED,IAAE,CAAC,IAAE,CAACC,KAAED,IAAE,CAAC,IAAEC,KAAED,IAAE,CAAC,IAAEC;AAAG;AAAA,QAAK;AAAA,QAAC,KAAI;AAAA,QAAqB,KAAI;AAAA,QAAkB,KAAI,oBAAmB;AAAC,gBAAMF,MAAEW;AAAE,cAAG,sBAAoBA,IAAE,MAAK;AAAC,kBAAMX,MAAEW;AAAE,gBAAGV,MAAE,KAAK,oBAAoBA,KAAED,KAAEE,KAAEK,GAAC,GAAEP,IAAE,OAAM;AAAC,oBAAME,OAAGF,IAAE,MAAM,OAAKA,IAAE,MAAM,QAAM,GAAEK,OAAGL,IAAE,MAAM,OAAKA,IAAE,MAAM,QAAM;AAAE,kBAAGC,IAAE,CAAC,KAAGC,KAAED,IAAE,CAAC,KAAGI,KAAEJ,IAAE,CAAC,KAAGC,KAAED,IAAE,CAAC,KAAGI,KAAE,QAAML,IAAE,MAAK;AAAC,sBAAME,MAAEF,IAAE,QAAMA,IAAE,MAAM,OAAKA,IAAE,MAAM;AAAM,gBAAAC,IAAE,CAAC,KAAGC,KAAED,IAAE,CAAC,KAAGC,KAAED,IAAE,CAAC,KAAGC,KAAED,IAAE,CAAC,KAAGC;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC,WAAS,uBAAqBS,IAAE,MAAK;AAAC,kBAAMN,MAAEM,KAAEJ,MAAEL,IAAE,YAAYG,IAAE,GAAG;AAAE,gBAAII,MAAE;AAAE,gBAAG,EAAEF,GAAC,KAAGA,IAAE,WAASE,MAAEF,IAAE,QAAMA,IAAE,SAAQ,QAAMP,IAAE,MAAK;AAAC,oBAAME,MAAEF,IAAE,OAAK,GAAEM,MAAEN,IAAE,OAAKS,MAAEJ,IAAE,SAAO;AAAE,cAAAJ,MAAE,CAAC,CAACK,KAAE,CAACJ,KAAEI,KAAEJ,GAAC;AAAA,YAAC;AAAA,UAAC,WAAS,QAAMF,IAAE,MAAK;AAAC,kBAAME,MAAEF,IAAE,OAAK;AAAE,YAAAC,MAAE,CAAC,CAACC,KAAE,CAACA,KAAEA,KAAEA,GAAC;AAAA,UAAC;AAAC,cAAGF,IAAE,aAAY;AAAC,gBAAIE,KAAEG;AAAE,2BAAaL,IAAE,oBAAkBE,MAAEF,IAAE,YAAY,GAAEK,MAAEL,IAAE,YAAY,MAAIE,MAAEF,IAAE,YAAY,KAAGC,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAGI,MAAEL,IAAE,YAAY,KAAGC,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAIA,IAAE,CAAC,KAAGC,KAAED,IAAE,CAAC,KAAGI,KAAEJ,IAAE,CAAC,KAAGC,KAAED,IAAE,CAAC,KAAGI;AAAA,UAAC;AAAC,cAAII,MAAE,EAAET,IAAE,QAAQ;AAAE,cAAGA,IAAE,oBAAkBS,MAAE,CAACA,MAAGJ,QAAII,OAAGJ,MAAGI,KAAE;AAAC,kBAAMT,MAAEH,KAAEY,KAAEP,MAAE,KAAK,IAAIF,GAAC,GAAEK,MAAE,KAAK,IAAIL,GAAC,GAAEM,MAAEE,GAAE,CAACG,IAAEA,IAAE,CAACA,IAAE,CAACA,EAAC,CAAC;AAAE,cAAEL,KAAE,CAACL,IAAE,CAAC,IAAEC,MAAED,IAAE,CAAC,IAAEI,KAAEJ,IAAE,CAAC,IAAEI,MAAEJ,IAAE,CAAC,IAAEC,GAAC,CAAC,GAAE,EAAEI,KAAE,CAACL,IAAE,CAAC,IAAEC,MAAED,IAAE,CAAC,IAAEI,KAAEJ,IAAE,CAAC,IAAEI,MAAEJ,IAAE,CAAC,IAAEC,GAAC,CAAC,GAAE,EAAEI,KAAE,CAACL,IAAE,CAAC,IAAEC,MAAED,IAAE,CAAC,IAAEI,KAAEJ,IAAE,CAAC,IAAEI,MAAEJ,IAAE,CAAC,IAAEC,GAAC,CAAC,GAAE,EAAEI,KAAE,CAACL,IAAE,CAAC,IAAEC,MAAED,IAAE,CAAC,IAAEI,KAAEJ,IAAE,CAAC,IAAEI,MAAEJ,IAAE,CAAC,IAAEC,GAAC,CAAC,GAAED,MAAEK;AAAA,UAAC;AAAC,cAAIM,MAAE,EAAEZ,IAAE,OAAO,GAAEa,MAAE,EAAEb,IAAE,OAAO;AAAE,cAAGK,KAAE;AAAC,kBAAML,MAAEH,KAAEQ,KAAEJ,MAAE,KAAK,IAAID,GAAC,GAAEE,MAAE,KAAK,IAAIF,GAAC,GAAEM,MAAEM,MAAEV,MAAEW,MAAEZ;AAAE,YAAAW,MAAEA,MAAEX,MAAEY,MAAEX,KAAEW,MAAEP;AAAA,UAAC;AAAC,UAAAL,IAAE,CAAC,KAAGW,KAAEX,IAAE,CAAC,KAAGY,KAAEZ,IAAE,CAAC,KAAGW,KAAEX,IAAE,CAAC,KAAGY;AAAE,gBAAMC,KAAE,GAAGd,IAAE,eAAe;AAAE,UAAAc,KAAE,MAAIb,IAAE,CAAC,KAAGa,IAAEb,IAAE,CAAC,KAAGa,IAAEb,IAAE,CAAC,KAAGa,IAAEb,IAAE,CAAC,KAAGa;AAAG;AAAA,QAAK;AAAA,MAAC;AAAC,YAAMF,MAAE,GAAGD,IAAE,OAAO;AAAE,MAAAC,MAAE,MAAIX,IAAE,CAAC,KAAGW,KAAEX,IAAE,CAAC,KAAGW,KAAEX,IAAE,CAAC,KAAGW,KAAEX,IAAE,CAAC,KAAGW,MAAGH,OAAGT,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAED,IAAE,CAAC,IAAEC,IAAE,CAAC,GAAEQ,MAAE,UAAKT,IAAE,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,GAAEC,IAAE,CAAC,CAAC,GAAED,IAAE,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,GAAEC,IAAE,CAAC,CAAC,GAAED,IAAE,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,GAAEC,IAAE,CAAC,CAAC,GAAED,IAAE,CAAC,IAAE,KAAK,IAAIA,IAAE,CAAC,GAAEC,IAAE,CAAC,CAAC;AAAA,IAAE;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,qBAAqBA,KAAEE,KAAEG,KAAE;AAJnlN;AAIolN,UAAMC,MAAEJ,IAAE,UAAQP;AAAE,QAAGK,IAAE,CAAC,IAAE,CAACM,MAAE,GAAEN,IAAE,CAAC,IAAE,CAACM,MAAE,GAAEN,IAAE,CAAC,IAAEM,MAAE,GAAEN,IAAE,CAAC,IAAEM,MAAE,GAAE,CAACD,IAAE,QAAOL;AAAE,UAAMO,MAAEF,IAAE,IAAIH,GAAC;AAAE,QAAG,CAACK,IAAE,QAAOP;AAAE,UAAK,EAAC,MAAKS,KAAE,YAAWE,IAAC,IAAEJ;AAAE,QAAG,GAAC,KAAAI,OAAA,gBAAAA,IAAG,qBAAH,mBAAqB,QAAO,QAAOX;AAAE,UAAK,EAAC,aAAYY,KAAE,SAAQF,IAAC,IAAER,KAAEa,KAAEH,MAAE,GAAEA,KAAEF,OAAG,GAAEJ,GAAC,IAAE,GAAEO,MAAEN,GAAEE,GAAC,EAAE,CAAC,GAAEK,KAAEH,IAAE,kBAAiBH,MAAE,6BAAyB,KAAAN,IAAE,YAAF,mBAAW,OAAKc,KAAEC,GAAEH,IAAED,KAAE,EAAC,OAAMP,MAAE,GAAE,OAAM,EAAEJ,IAAE,KAAK,GAAE,SAAQ,EAAEA,IAAE,OAAO,GAAE,SAAQ,EAAEA,IAAE,OAAO,GAAE,QAAOH,GAAEG,IAAE,mBAAmB,GAAE,QAAO,EAAEA,IAAE,iBAAiB,GAAE,cAAa,KAAI,YAAWW,KAAE,KAAK,IAAI,MAAI,KAAK,IAAIE,MAAG,GAAE,CAAC,CAAC,GAAE,YAAWb,IAAE,KAAK,cAAY,QAAO,OAAM,MAAG,eAAcM,IAAC,CAAC,EAAE;AAAQ,WAAOR,IAAE,CAAC,IAAEgB,GAAE,IAAEA,GAAE,WAAUhB,IAAE,CAAC,IAAE,CAACgB,GAAE,IAAEA,GAAE,YAAWhB,IAAE,CAAC,IAAEgB,GAAE,IAAEA,GAAE,WAAUhB,IAAE,CAAC,IAAE,CAACgB,GAAE,IAAEA,GAAE,YAAWhB;AAAA,EAAC;AAAC;AAAC,IAAMG,MAAN,MAAM,IAAE;AAAA,EAAC,OAAO,YAAYH,KAAEC,KAAEC,KAAE;AAAC,QAAG,CAACF,IAAE,QAAO;AAAK,UAAMK,MAAE,IAAIX,GAAEQ,GAAC;AAAE,QAAG,MAAM,QAAQF,GAAC,GAAE;AAAC,UAAIE;AAAE,iBAAUI,OAAKN,IAAE,CAAAE,MAAEA,IAAE,MAAM,GAAGG,KAAEC,KAAEL,GAAC,CAAC,IAAEC,MAAE,GAAGG,KAAEC,KAAEL,GAAC;AAAE,aAAOC;AAAA,IAAC;AAAC,WAAO,GAAGG,KAAEL,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiBD,KAAEC,KAAE;AAAC,UAAMC,MAAE,KAAK,YAAYF,KAAE,MAAKC,GAAC;AAAE,QAAG,CAACC,IAAE,QAAM,CAAC,GAAE,GAAE,CAAC;AAAE,UAAMG,OAAGH,IAAE,IAAE,MAAGA,IAAE,SAAON,IAAEU,OAAGJ,IAAE,IAAE,MAAGA,IAAE,UAAQN,IAAEW,MAAEL,IAAE,QAAMN,KAAE,GAAEa,MAAEP,IAAE,SAAON,KAAE;AAAE,WAAM,CAAC,CAACS,MAAEE,KAAE,CAACD,MAAEG,KAAEA,GAAC;AAAA,EAAC;AAAA,EAAC,OAAO,UAAUT,KAAEC,KAAEC,KAAEG,KAAEC,MAAE,MAAG;AAAC,UAAMC,MAAEL,OAAG,KAAK,YAAYD,KAAE,MAAKI,GAAC;AAAE,QAAG,CAACE,IAAE,QAAM,CAAC,MAAK,GAAE,GAAE,GAAE,CAAC;AAAE,UAAME,OAAGF,IAAE,IAAE,MAAGA,IAAE,SAAOX,IAAEe,OAAGJ,IAAE,IAAE,MAAGA,IAAE,UAAQX;AAAE,IAAAI,IAAE,QAAMO,IAAE,QAAMX,IAAEI,IAAE,SAAOO,IAAE,SAAOX,IAAEM,QAAIF,IAAE,SAAO,GAAEA,IAAE,UAAQ;AAAG,UAAMY,MAAEZ,IAAE,WAAW,IAAI,GAAEU,MAAEQ,GAAE,YAAYtB,IAAE,CAACA,EAAC;AAAE,IAAAc,IAAE,UAAU,MAAGV,IAAE,QAAMS,KAAE,MAAGT,IAAE,SAAOW,GAAC;AAAE,UAAMI,KAAE,IAAI,EAAEH,KAAEP,KAAEK,GAAC;AAAE,YAAOT,IAAE,MAAK;AAAA,MAAC,KAAI,kBAAiB;AAAC,cAAMD,MAAE,EAAC,MAAK,SAAQ,GAAE,GAAE,GAAE,EAAC;AAAE,QAAAe,GAAE,WAAWd,KAAED,GAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,mBAAkB;AAAC,cAAMA,MAAE,IAAIC;AAAE,QAAAc,GAAE,WAAWd,KAAED,GAAC;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,UAAMa,MAAED,IAAE,aAAa,GAAE,GAAEZ,IAAE,OAAMA,IAAE,MAAM,GAAEc,KAAE,IAAI,WAAWD,IAAE,IAAI;AAAE,QAAGP,KAAE;AAAC,UAAIN;AAAE,eAAQC,MAAE,GAAEA,MAAEa,GAAE,QAAOb,OAAG,EAAE,CAAAD,MAAEc,GAAEb,MAAE,CAAC,IAAE,KAAIa,GAAEb,GAAC,IAAEa,GAAEb,GAAC,IAAED,KAAEc,GAAEb,MAAE,CAAC,IAAEa,GAAEb,MAAE,CAAC,IAAED,KAAEc,GAAEb,MAAE,CAAC,IAAEa,GAAEb,MAAE,CAAC,IAAED;AAAA,IAAC;AAAC,WAAM,CAACc,IAAEd,IAAE,OAAMA,IAAE,QAAO,CAACS,MAAET,IAAE,OAAM,CAACW,MAAEX,IAAE,MAAM;AAAA,EAAC;AAAA,EAAC,OAAO,eAAeA,KAAE;AAAC,UAAK,EAAC,OAAME,KAAE,OAAMG,KAAE,MAAKC,KAAE,WAAUC,KAAE,UAASE,KAAE,qBAAoBE,KAAE,SAAQC,KAAE,MAAKF,KAAE,mBAAkBK,IAAE,SAAQF,KAAE,SAAQC,IAAE,iBAAgBN,KAAE,iBAAgBW,IAAE,gBAAeC,GAAC,IAAEpB;AAAE,QAAIgB,IAAEK,IAAEJ,IAAEK,IAAEC,KAAEC;AAAE,IAAAlB,QAAIU,KAAEV,IAAE,QAAOe,KAAEf,IAAE,OAAMW,KAAEX,IAAE,QAAOgB,KAAEhB,IAAE,MAAKiB,MAAEjB,IAAE;AAAY,QAAImB,KAAE;AAAG,QAAGf,KAAE;AAAC,MAAAe,KAAElB,GAAEG,GAAC,EAAE,CAAC;AAAA,IAAC;AAAC,YAAOF,OAAGY,QAAKI,KAAE,EAAC,MAAK,wBAAuB,QAAO,MAAK,kBAAiB,EAAC,MAAK,oBAAmB,cAAa,CAAC,EAAC,MAAK,gBAAe,OAAM,GAAGhB,GAAC,EAAC,GAAE,EAAC,MAAK,kBAAiB,OAAM,GAAGW,EAAC,GAAE,OAAMC,GAAC,CAAC,EAAC,GAAE,iBAAgB,MAAK,KAAI,MAAK,kBAAiB,MAAK,WAAU,KAAI,IAAG,EAAC,MAAK,kBAAiB,cAAa,CAAC,EAAC,MAAK,mBAAkB,QAAO,MAAG,kBAAiB,YAAW,oBAAmB,KAAI,MAAK,IAAG,iBAAgB,iBAAgB,OAAM,EAAC,MAAK,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK,EAAC,GAAE,gBAAe,CAAC,EAAC,MAAK,oBAAmB,UAAS,EAAC,GAAE,GAAE,GAAE,EAAC,GAAE,QAAO,EAAC,MAAK,iBAAgB,OAAMlB,KAAE,kBAAiBsB,GAAE,KAAI,SAAQ,GAAE,sBAAqB,MAAG,aAAYZ,GAAE,QAAO,cAAa,EAAE,SAAQ,gBAAeI,MAAG,SAAQ,eAAc,GAAGK,IAAEJ,EAAC,GAAE,UAAST,GAAE,aAAY,UAASC,KAAE,QAAOa,IAAE,SAAQR,GAAE,SAAQ,qBAAoB,GAAGH,OAAG,QAAQ,GAAE,SAAQC,KAAE,aAAY,KAAI,WAAU,MAAG,aAAY,YAAW,SAAQ,EAAEC,GAAC,GAAE,SAAQ,EAAEC,EAAC,GAAE,eAAc,mBAAiBS,KAAE,WAAU,gBAAcA,KAAE,QAAO,EAAC,MAAK,oBAAmB,cAAa,CAAC,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAM,GAAGlB,GAAC,EAAC,CAAC,EAAC,GAAE,YAAW,EAAC,MAAK,oBAAmB,cAAa,CAAC,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAM,GAAGE,GAAC,EAAC,CAAC,EAAC,GAAE,aAAY,CAAC,GAAE,GAAE,GAAE,GAAG,GAAE,eAAc,GAAE,eAAc,GAAE,UAAS,UAAS,eAAckB,KAAE,GAAE,MAAI,GAAE,KAAI,mBAAkB,GAAGV,MAAG,UAAU,GAAE,0BAAyB,GAAE,OAAM,aAAY,KAAI,iBAAgBA,GAAE,eAAc,SAAQS,GAAC,GAAE,YAAWd,IAAC,CAAC,GAAE,4BAA2B,MAAG,cAAa,KAAE,CAAC,GAAE,QAAO,GAAE,gBAAe,UAAS;AAAA,EAAC;AAAA,EAAC,OAAO,sBAAsBV,KAAE;AAAC,UAAK,EAAC,QAAOC,KAAE,SAAQC,KAAE,OAAMG,KAAE,SAAQC,KAAE,QAAOC,KAAE,SAAQE,KAAE,QAAOE,IAAC,IAAEX,KAAEY,MAAE,CAAC,GAAEF,MAAE,EAAC,MAAK,oBAAmB,cAAaE,IAAC;AAAE,QAAGV,KAAE;AAAC,YAAK,EAAC,KAAIF,KAAE,MAAKC,KAAE,YAAWI,KAAE,OAAMC,IAAC,IAAEJ;AAAE,MAAAU,IAAE,KAAK,EAAC,MAAK,kBAAiB,OAAM,GAAGV,IAAE,KAAK,GAAE,UAASwB,IAAG1B,GAAC,GAAE,WAAU,GAAGC,GAAC,GAAE,YAAWI,KAAE,OAAMC,IAAC,CAAC;AAAA,IAAC;AAAC,QAAIS,KAAEf,IAAE;AAAI,kBAAYA,IAAE,QAAMA,IAAE,cAAYe,KAAEf,IAAE;AAAW,UAAMa,MAAE,WAAUb,MAAEA,IAAE,SAAO,IAAE,GAAEc,MAAGT,OAAG,MAAIE,OAAG,IAAGC,OAAGP,OAAG,MAAIU,OAAG;AAAG,WAAOC,IAAE,KAAK,EAAC,MAAK,kBAAiB,uBAAsB,OAAG,QAAO,GAAE,eAAc,GAAE,SAAQ,WAAU,MAAK,KAAIG,IAAE,QAAOP,KAAE,OAAMM,IAAE,SAAQ,EAAER,GAAC,GAAE,SAAQ,EAAEG,GAAC,GAAE,UAAS,EAAE,CAACI,GAAC,GAAE,oBAAmB,KAAI,CAAC,GAAEH;AAAA,EAAC;AAAA,EAAC,OAAO,qBAAqBV,KAAE;AAAC,UAAK,EAAC,OAAMC,KAAE,OAAMI,KAAE,SAAQC,IAAC,IAAEN,KAAEO,MAAE,CAAC,GAAEE,MAAE,EAAC,MAAK,oBAAmB,cAAaF,IAAC;AAAE,QAAII,MAAE;AAAK,QAAGL,KAAE;AAAC,YAAK,EAAC,KAAIN,KAAE,MAAKC,KAAE,OAAMC,IAAC,IAAEI;AAAE,kBAAUJ,OAAG,WAASA,OAAG,mBAAiBA,OAAG,kBAAgBA,QAAIS,MAAE,CAAC,EAAC,MAAK,4BAA2B,cAAa,GAAGT,KAAEF,GAAC,GAAE,gBAAe,gBAAe,WAAU,MAAG,iBAAgB,KAAI,CAAC,IAAGO,IAAE,KAAK,EAAC,MAAK,kBAAiB,OAAM,GAAGD,IAAE,KAAK,GAAE,UAASoB,IAAG1B,GAAC,GAAE,WAAU,GAAGC,GAAC,GAAE,YAAWK,IAAE,YAAW,OAAMA,IAAE,OAAM,SAAQK,IAAC,CAAC;AAAA,IAAC;AAAC,QAAGN,OAAG,YAAUA,OAAG,WAASA,OAAG,mBAAiBA,OAAG,kBAAgBA,KAAE;AAAC,YAAML,MAAE,EAAC,MAAK,iBAAgB,cAAa,CAAC,EAAC,MAAK,kBAAiB,OAAM,GAAGC,GAAC,GAAE,UAAS,EAAE,MAAK,WAAU,EAAE,OAAM,OAAM,KAAG,CAAC,EAAC;AAAE,UAAIK,MAAE;AAAE,YAAMG,MAAET,GAAE,GAAGK,GAAC,IAAE,IAAE,EAAE;AAAE,cAAOA,KAAE;AAAA,QAAC,KAAI;AAAA,QAAW,KAAI;AAAkB,UAAAC,MAAE;AAAG;AAAA,QAAM,KAAI;AAAA,QAAmB,KAAI;AAAA,QAAyB,KAAI;AAAA,QAAiB,KAAI;AAAuB,UAAAA,MAAE;AAAI;AAAA,QAAM,KAAI;AAAA,QAAoB,KAAI;AAA0B,UAAAA,MAAE;AAAG;AAAA,QAAM,KAAI;AAAA,QAAQ,KAAI;AAAe,UAAAA,MAAE;AAAA,MAAC;AAAC,MAAAC,IAAE,KAAK,EAAC,MAAK,gBAAe,YAAWP,KAAE,SAAQ,GAAE,SAAQ,GAAE,UAASM,KAAE,YAAWG,IAAC,CAAC,GAAE,YAAUJ,OAAG,mBAAiBA,MAAEE,IAAE,KAAK,EAAC,MAAK,gBAAe,YAAW,EAAEP,GAAC,GAAE,SAAQ,GAAE,SAAQ,GAAE,UAAS,IAAG,YAAWS,IAAC,CAAC,IAAE,qBAAmBJ,OAAG,2BAAyBA,OAAGE,IAAE,KAAK,EAAC,MAAK,gBAAe,YAAW,EAAEP,GAAC,GAAE,SAAQ,GAAE,SAAQ,GAAE,UAAS,IAAG,YAAWS,IAAC,CAAC;AAAA,IAAC,MAAK,EAACJ,OAAG,YAAUA,OAAG,mBAAiBA,OAAGE,IAAE,KAAK,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAM,GAAGN,GAAC,EAAC,CAAC;AAAE,WAAOQ;AAAA,EAAC;AAAA,EAAC,OAAO,qBAAqBT,KAAE;AAAC,UAAK,EAAC,KAAIC,KAAE,OAAMC,KAAE,MAAKG,KAAE,QAAOC,KAAE,YAAWC,KAAE,OAAME,KAAE,OAAME,IAAC,IAAEX;AAAE,QAAIY,MAAE;AAAK,gBAAUH,OAAG,WAASA,OAAG,mBAAiBA,OAAG,kBAAgBA,QAAIG,MAAE,CAAC,EAAC,MAAK,4BAA2B,cAAa,GAAGH,KAAER,GAAC,GAAE,gBAAe,gBAAe,WAAU,MAAG,iBAAgB,KAAI,CAAC;AAAG,UAAMS,MAAE,CAAC;AAAE,QAAGJ,KAAE;AAAC,UAAIN;AAAE,cAAOM,IAAE,WAAU;AAAA,QAAC,KAAI;AAAY,UAAAN,MAAEM,GAAE;AAAK;AAAA,QAAM,KAAI;AAAQ,UAAAN,MAAEM,GAAE;AAAU;AAAA,QAAM,KAAI;AAAM,UAAAN,MAAEM,GAAE;AAAQ;AAAA,QAAM;AAAQ,UAAAN,MAAEM,GAAE;AAAA,MAAI;AAAC,YAAML,MAAE,IAAG,iBAAiBK,KAAEK,KAAET,GAAC,EAAE,aAAa,CAAC;AAAE,MAAAD,IAAE,kBAAgB,EAAC,MAAK,mCAAkC,aAAY,MAAG,QAAO,GAAE,oBAAmBD,KAAE,iBAAgB,EAAC,GAAEU,IAAE,KAAKT,GAAC;AAAA,IAAC;AAAC,WAAM,WAASQ,OAAG,kBAAgBA,OAAGC,IAAE,KAAK,EAAC,MAAK,kBAAiB,OAAM,GAAGR,GAAC,GAAE,UAASwB,IAAGzB,GAAC,GAAE,WAAU,GAAGI,GAAC,GAAE,YAAWE,KAAE,OAAMI,KAAE,SAAQC,IAAC,CAAC,GAAE,EAAC,MAAK,iBAAgB,cAAaF,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,kBAAkBV,KAAE;AAAC,UAAK,EAAC,OAAMC,KAAE,QAAOC,KAAE,OAAMG,KAAE,SAAQC,KAAE,SAAQC,IAAC,IAAEP;AAAE,QAAIS,MAAET,IAAE;AAAI,WAAM,cAAYA,IAAE,QAAMA,IAAE,cAAYS,MAAET,IAAE,YAAW,EAAC,MAAK,kBAAiB,cAAa,CAAC,EAAC,MAAK,oBAAmB,uBAAsB,OAAG,QAAO,GAAE,eAAc,GAAE,SAAQ,WAAU,MAAK,KAAIS,KAAE,MAAKP,KAAE,OAAMG,KAAE,SAAQ,EAAEC,GAAC,GAAE,SAAQ,EAAEC,GAAC,GAAE,UAAS,EAAE,CAACN,GAAC,EAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiBD,KAAEC,KAAEC,KAAE;AAAC,UAAK,EAAC,OAAMG,IAAC,IAAEL,KAAEM,MAAEN,IAAE,SAAOE;AAAE,QAAG,WAASG,KAAE;AAAC,YAAMJ,MAAE,CAAC;AAAE,UAAG,aAAYD,OAAGA,IAAE,SAAQ;AAAC,cAAME,MAAEF,IAAE;AAAQ,QAAAC,IAAE,KAAK,EAAC,MAAK,kBAAiB,QAAO,MAAG,OAAMO,GAAE,KAAK,MAAMR,GAAEE,IAAE,KAAK,CAAC,CAAC,GAAE,OAAM,GAAGA,IAAE,KAAK,EAAC,CAAC;AAAA,MAAC;AAAC,MAAAD,IAAE,KAAK,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAM,GAAGK,GAAC,GAAE,MAAKN,IAAE,KAAI,CAAC;AAAE,YAAK,CAACE,KAAEG,GAAC,IAAE,GAAG,QAAQ;AAAE,aAAM,EAAC,MAAK,kBAAiB,cAAa,CAAC,EAAC,MAAK,mBAAkB,QAAO,MAAG,UAAS,EAAE,CAACL,IAAE,KAAK,GAAE,MAAK,EAAEA,IAAE,QAAM,CAAC,GAAE,SAAQ,EAAEA,IAAE,OAAO,GAAE,SAAQ,EAAEA,IAAE,OAAO,GAAE,OAAME,KAAE,gBAAe,CAAC,EAAC,MAAK,oBAAmB,UAASG,KAAE,QAAO,EAAC,MAAK,oBAAmB,cAAaJ,IAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AAAA,IAAC;AAAC,UAAK,CAACM,KAAEE,GAAC,IAAE,GAAGJ,GAAC;AAAE,QAAIK;AAAE,QAAGD,OAAGF,KAAE;AAAC,YAAML,MAAE,CAAC;AAAE,UAAG,aAAYF,OAAGA,IAAE,SAAQ;AAAC,cAAMC,MAAED,IAAE;AAAQ,QAAAE,IAAE,KAAK,EAAC,MAAK,kBAAiB,QAAO,MAAG,OAAM,QAAMD,IAAE,SAAOA,IAAE,QAAM,QAAKO,GAAE,KAAK,MAAMR,GAAEC,IAAE,KAAK,CAAC,CAAC,IAAEA,IAAE,OAAM,OAAM,GAAGA,IAAE,KAAK,EAAC,CAAC;AAAA,MAAC,MAAK,EAACA,OAAG,kBAAgBD,IAAE,QAAM,YAAUA,IAAE,SAAO,QAAMA,IAAE,SAAOE,IAAE,KAAK,EAAC,MAAK,kBAAiB,QAAO,MAAG,OAAMD,KAAE,OAAM,GAAGK,GAAC,EAAC,CAAC;AAAE,MAAAJ,IAAE,KAAK,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAM,GAAGI,GAAC,EAAC,CAAC;AAAE,YAAMD,MAAE,EAAC,MAAK,oBAAmB,cAAaH,IAAC;AAAE,MAAAQ,MAAE,EAAC,MAAK,kBAAiB,cAAa,CAAC,EAAC,MAAK,mBAAkB,QAAO,MAAG,UAAS,EAAE,CAACV,IAAE,KAAK,GAAE,MAAK,EAAEA,IAAE,QAAM,IAAEC,GAAC,GAAE,SAAQ,EAAED,IAAE,OAAO,GAAE,SAAQ,EAAEA,IAAE,OAAO,GAAE,OAAMO,KAAE,gBAAe,CAAC,EAAC,MAAK,oBAAmB,UAASE,KAAE,QAAOJ,IAAC,CAAC,EAAC,CAAC,EAAC;AAAA,IAAC;AAAC,WAAOK;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiBV,KAAEC,KAAE;AAJ56c;AAI66c,UAAMI,MAAEJ,OAAGD,IAAE,cAAYP,KAAGa,MAAED,MAAE,GAAEE,MAAE,EAAEP,IAAE,UAAU;AAAE,UAAAO,IAAE,iBAAF,mBAAgB,QAAS,CAAAP,QAAG;AAJ7/c,UAAA2B;AAI8/c,cAAO3B,IAAE,MAAK;AAAA,QAAC,KAAI;AAAiB,kBAAMA,IAAE,UAAQA,IAAE,SAAOC,OAAG0B,MAAA3B,IAAE,YAAF,gBAAA2B,IAAW,QAAS,CAAA3B,QAAG;AAAC,2CAA6BA,IAAE,SAAOA,IAAE,eAAaA,IAAE,aAAa,IAAK,CAAAA,QAAGA,MAAEC,GAAE;AAAA,UAAE;AAAI;AAAA,QAAM,KAAI,mBAAkB;AAAC,kBAAMD,IAAE,SAAOA,IAAE,QAAMC;AAAG,gBAAMC,MAAEF,IAAE;AAAgB,kBAAME,OAAG,uBAAsBA,QAAIA,IAAE,oBAAkBA,IAAE,kBAAkB,IAAK,CAAAF,QAAGA,MAAEC,GAAE;AAAG;AAAA,QAAK;AAAA,MAAC;AAAA,IAAC;AAAI,QAAIQ,MAAE,KAAK,qBAAqBF,GAAC,KAAGb;AAAE,WAAKe,MAAEf,KAAG,CAAAe,OAAG;AAAE,UAAME,MAAEF,MAAE;AAAE,WAAM,EAAC,MAAK,mBAAkB,OAAM,EAAC,MAAK,CAACE,KAAE,MAAKA,KAAE,MAAK,CAACL,KAAE,MAAKA,IAAC,GAAE,gBAAe,CAAC,EAAC,MAAK,oBAAmB,UAAS,EAAC,OAAM,CAAC,CAAC,CAAC,CAACK,KAAE,CAAC,GAAE,CAACA,KAAE,CAAC,CAAC,CAAC,EAAC,GAAE,QAAOJ,IAAC,CAAC,GAAE,MAAKF,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,eAAeL,KAAEC,KAAEC,KAAE;AAAC,QAAGF,OAAGC,IAAE,SAAOD,IAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAiB,KAAI;AAAA,MAAgB,KAAI,oBAAmB;AAAC,cAAMK,MAAEL,IAAE;AAAa,YAAG,CAACK,IAAE;AAAO,mBAAUL,OAAKK,IAAE,SAAO,GAAGL,KAAEC,KAAEC,GAAC,GAAEF,IAAE,MAAK;AAAA,UAAC,KAAI;AAAA,UAAiB,KAAI;AAAA,UAAe,KAAI;AAAA,UAAkB,KAAI;AAAA,UAAmB,KAAI;AAAA,UAAoB,KAAI;AAAA,UAAqB,KAAI;AAAmB,qBAAQA,OAAGA,IAAE,OAAKE,IAAE,KAAKD,IAAE,cAAcD,IAAE,KAAI,IAAI,CAAC;AAAE;AAAA,UAAM,KAAI,mBAAkB;AAAC,kBAAMK,MAAEL,IAAE;AAAe,gBAAG,CAACK,IAAE;AAAS,uBAAUL,OAAKK,IAAE,KAAGL,KAAE;AAAC,oBAAMK,MAAEL,IAAE;AAAO,cAAAK,OAAG,IAAG,eAAeA,KAAEJ,KAAEC,GAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,qBAAqBF,KAAE;AAAC,QAAGA,KAAE;AAAC,YAAMC,MAAE,KAAK,kBAAkBD,IAAE,OAAO;AAAE,UAAGC,IAAE,QAAOA;AAAE,UAAGD,IAAE;AAAa,mBAAUE,OAAKF,IAAE,aAAa,KAAGE,KAAE;AAAC,gBAAMF,MAAE,KAAK,kBAAkBE,IAAE,OAAO;AAAE,cAAGF,IAAE,QAAOA;AAAE,kBAAOE,IAAE,MAAK;AAAA,YAAC,KAAI;AAAA,YAAqB,KAAI;AAAA,YAAmB,KAAI;AAAA,YAAkB,KAAI;AAAA,YAAoB,KAAI,mBAAkB;AAAC,oBAAMF,MAAE,KAAK,oBAAoBE,IAAE,eAAe;AAAE,kBAAGF,IAAE,QAAOA;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,OAAO,kBAAkBA,KAAE;AAAC,QAAGA;AAAE,iBAAUC,OAAKD,IAAE,KAAGC,IAAE,SAAOA,IAAE,MAAK;AAAA,QAAC,KAAI,4BAA2B;AAAC,gBAAMD,MAAEC,IAAE;AAAa,cAAGD,OAAGA,IAAE,QAAO;AAAC,gBAAIC,MAAE;AAAE,uBAAUC,OAAKF,IAAE,CAAAC,OAAGC;AAAE,mBAAO,IAAEF,IAAE,WAASC,OAAG,IAAGA;AAAA,UAAC;AAAC;AAAA,QAAK;AAAA,QAAC,KAAI;AAAyB,iBAAOA,IAAE;AAAA,QAAO;AAAQ,UAAAH,GAAE,MAAM,qCAAqCG,IAAE,IAAI,EAAE;AAAA,MAAC;AAAA;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,OAAO,oBAAoBD,KAAE;AAAC,QAAGA,IAAE,SAAOA,IAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAsC,KAAI;AAAA,MAAwC,KAAI,2CAA0C;AAAC,cAAMC,MAAED,IAAE;AAAkB,YAAGC,OAAGA,IAAE,QAAO;AAAC,cAAID,MAAE;AAAE,qBAAUE,OAAKD,IAAE,CAAAD,OAAG,CAACE;AAAE,iBAAO,IAAED,IAAE,WAASD,OAAG,IAAGA;AAAA,QAAC;AAAC;AAAA,MAAK;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,OAAO,qBAAqBA,KAAE;AAAC,UAAMC,MAAED,IAAE,iBAAgBE,MAAE,EAAC,GAAGF,IAAC;AAAE,IAAAE,IAAE,kBAAgB,MAAKA,IAAE,cAAY;AAAK,UAAMG,MAAE,KAAK,IAAIJ,IAAE,KAAK,GAAEK,MAAE,KAAK,IAAIL,IAAE,KAAK,GAAEM,OAAGN,IAAE,cAAY,OAAK;AAAI,QAAIU,KAAED,KAAEK,IAAEF;AAAE,QAAG,aAAWZ,IAAE,UAAS;AAAC,YAAMD,MAAEA,GAAE,EAAC,GAAEE,MAAE,KAAK,IAAI,KAAK,MAAMF,MAAEK,GAAC,GAAE,CAAC,GAAES,KAAE,KAAK,IAAI,KAAK,MAAMd,MAAEM,GAAC,GAAE,CAAC;AAAE,MAAAK,MAAET,MAAEG,MAAE,GAAEK,MAAEI,KAAER,MAAE,GAAES,KAAE,IAAEL;AAAE,YAAMF,MAAE,IAAIP,GAAEA,IAAE,IAAI,GAAEkB,KAAEZ,MAAEF,MAAE,KAAIe,KAAEb,MAAED,MAAE;AAAI,MAAAO,MAAE,CAAC;AAAE,eAAQZ,MAAE,GAAEA,MAAEC,KAAED,MAAI,UAAQD,MAAE,GAAEA,MAAEc,IAAEd,OAAI;AAAC,cAAME,MAAED,MAAEI,MAAEM,MAAEQ,MAAG,MAAGX,IAAE,SAAS,IAAGD,MAAEP,MAAEM,MAAEI,MAAEU,MAAG,MAAGZ,IAAE,SAAS;AAAG,QAAAK,IAAE,KAAK,EAAC,GAAEX,KAAE,GAAEK,IAAC,CAAC,GAAE,MAAIN,OAAGY,IAAE,KAAK,EAAC,GAAEX,MAAE,IAAES,KAAE,GAAEJ,IAAC,CAAC,GAAE,MAAIP,OAAGa,IAAE,KAAK,EAAC,GAAEX,KAAE,GAAEK,MAAE,IAAEG,IAAC,CAAC;AAAA,MAAC;AAAA,IAAC,MAAK,UAAKT,IAAE,gBAAcU,MAAEN,MAAE,GAAEK,MAAEJ,KAAES,KAAE,IAAET,KAAEO,MAAE,CAAC,EAAC,GAAE,CAACF,KAAE,GAAE,EAAC,GAAE,EAAC,GAAEA,KAAE,GAAE,EAAC,GAAE,EAAC,GAAE,GAAE,GAAED,IAAC,GAAE,EAAC,GAAE,GAAE,GAAE,CAACA,IAAC,CAAC,MAAIC,MAAEN,MAAE,GAAEK,MAAEJ,MAAE,GAAES,KAAET,KAAEO,MAAE,CAAC,EAAC,GAAE,CAACR,KAAE,GAAE,EAAC,GAAE,EAAC,GAAE,GAAE,GAAE,CAACC,IAAC,GAAE,EAAC,GAAE,CAACD,KAAE,GAAE,CAACC,IAAC,GAAE,EAAC,GAAE,GAAE,GAAE,EAAC,GAAE,EAAC,GAAED,KAAE,GAAE,EAAC,GAAE,EAAC,GAAE,GAAE,GAAEC,IAAC,GAAE,EAAC,GAAED,KAAE,GAAEC,IAAC,GAAE,EAAC,GAAE,CAACD,KAAE,GAAEC,IAAC,GAAE,EAAC,GAAED,KAAE,GAAE,CAACC,IAAC,CAAC;AAAG,WAAM,EAAC,MAAK,mBAAkB,OAAM,EAAC,MAAK,CAACK,KAAE,MAAKA,KAAE,MAAK,CAACD,KAAE,MAAKA,IAAC,GAAE,gBAAeG,IAAE,IAAK,CAAAb,SAAI,EAAC,MAAK,oBAAmB,UAASA,KAAE,QAAO,EAAC,MAAK,kBAAiB,cAAa,CAACE,GAAC,EAAC,EAAC,EAAG,GAAE,MAAKa,GAAC;AAAA,EAAC;AAAA,EAAC,OAAO,QAAQf,KAAE;AAAC,QAAGA,IAAE,SAAOA,IAAE,MAAK;AAAA,MAAC,KAAI;AAAgB,eAAOA,IAAE;AAAA,MAAO,KAAI,kBAAiB;AAAC,YAAIC,MAAE;AAAE,YAAGD,IAAE;AAAa,qBAAUE,OAAKF,IAAE,aAAa,KAAGE,IAAE,SAAOA,IAAE,MAAK;AAAA,YAAC,KAAI;AAAA,YAAqB,KAAI;AAAA,YAAmB,KAAI;AAAA,YAAkB,KAAI;AAAA,YAAoB,KAAI,mBAAkB;AAAC,oBAAMF,MAAEE,IAAE;AAAK,sBAAMF,OAAGA,MAAEC,QAAIA,MAAED;AAAG;AAAA,YAAK;AAAA,UAAC;AAAA;AAAC,eAAOC;AAAA,MAAC;AAAA,MAAC,KAAI;AAAA,MAAgB,KAAI,oBAAmB;AAAC,YAAIA,MAAE;AAAE,YAAGD,IAAE;AAAa,qBAAUE,OAAKF,IAAE,aAAa,KAAGE,IAAE,SAAOA,IAAE,MAAK;AAAA,YAAC,KAAI;AAAA,YAAiB,KAAI;AAAA,YAAmB,KAAI,qBAAoB;AAAC,oBAAMF,MAAEE,IAAE;AAAM,sBAAMF,OAAGA,MAAEC,QAAIA,MAAED;AAAG;AAAA,YAAK;AAAA,YAAC,KAAI;AAAA,YAAqB,KAAI;AAAA,YAAmB,KAAI;AAAA,YAAkB,KAAI;AAAA,YAAoB,KAAI;AAAkB,kBAAGE,IAAE,mBAAiBa,GAAEb,IAAE,eAAe,GAAE;AAAC,sBAAMF,MAAEE,IAAE;AAAK,wBAAMF,OAAGA,MAAEC,QAAIA,MAAED;AAAA,cAAE;AAAA,UAAC;AAAA;AAAC,eAAOC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,oBAAoBD,KAAE;AAAC,QAAGA,OAAG,sBAAoBA,IAAE;AAAK,UAAG,UAAKA,IAAE,8BAA4BA,IAAE,SAAO,QAAMA,IAAE,MAAK;AAAC,cAAMC,MAAED,IAAE,MAAM,OAAKA,IAAE,MAAM;AAAK,eAAOA,IAAE,OAAKC;AAAA,MAAC;AAAA;AAAC,WAAO;AAAA,EAAC;AAAC;AAAC,IAAM,KAAN,MAAM,IAAE;AAAA,EAAC,OAAO,wBAAwBD,KAAEC,KAAEC,KAAE;AAAC,QAAGF,OAAGC,KAAE;AAAC,UAAGD,IAAE,eAAc;AAAC,YAAIK,MAAE;AAAG,mBAAUJ,OAAKC,IAAE,KAAGD,IAAE,kBAAgBD,IAAE,eAAc;AAAC,UAAAK,MAAE;AAAG;AAAA,QAAK;AAAC,YAAG,CAACA,IAAE,YAAUC,OAAKL,IAAE,CAAAK,IAAE,kBAAgBN,IAAE,iBAAeE,IAAE,KAAKI,GAAC;AAAA,MAAC;AAAC,cAAON,IAAE,MAAK;AAAA,QAAC,KAAI;AAAA,QAAiB,KAAI;AAAA,QAAgB,KAAI;AAAmB,cAAGA,IAAE,QAAQ,YAAUK,OAAKL,IAAE,QAAQ,KAAG,wBAAwBK,KAAEJ,KAAEC,GAAC;AAAE,cAAGF,IAAE,aAAa,YAAUK,OAAKL,IAAE,aAAa,KAAG,wBAAwBK,KAAEJ,KAAEC,GAAC;AAAE;AAAA,QAAM,KAAI;AAAgB;AAAA,QAAM,KAAI;AAAA,QAAiB,KAAI;AAAA,QAAmB,KAAI;AAAA,QAAoB,KAAI;AAAA,QAAe,KAAI;AAAA,QAAiB,KAAI;AAAA,QAAe,KAAI;AAAA,QAAkB,KAAI;AAAA,QAAkB,KAAI;AAAA,QAAqB,KAAI;AAAmB,cAAGF,IAAE,QAAQ,YAAUK,OAAKL,IAAE,QAAQ,KAAG,wBAAwBK,KAAEJ,KAAEC,GAAC;AAAE,cAAGF,IAAE,mBAAiB,IAAG,wBAAwBA,IAAE,iBAAgBC,KAAEC,GAAC,GAAE,sBAAoBF,IAAE,MAAK;AAAC,gBAAGA,IAAE,eAAe,YAAUK,OAAKL,IAAE,eAAe,KAAG,wBAAwBK,KAAEJ,KAAEC,GAAC,GAAE,IAAG,wBAAwBG,IAAE,QAAOJ,KAAEC,GAAC;AAAA,UAAC,MAAK,0BAAuBF,IAAE,OAAK,IAAG,wBAAwBA,IAAE,QAAOC,KAAEC,GAAC,IAAE,mBAAiBF,IAAE,OAAK,IAAG,wBAAwBA,IAAE,YAAWC,KAAEC,GAAC,IAAE,uBAAqBF,IAAE,QAAM,IAAG,wBAAwBA,IAAE,0BAAyBC,KAAEC,GAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,oBAAoBF,KAAEC,KAAEC,KAAE;AAJrtnB;AAIstnB,QAAG,CAACD,OAAG,CAACD,IAAE;AAAO,UAAMK,MAAEL,IAAE;AAAO,aAAQM,MAAE,GAAEA,MAAED,KAAEC,OAAI;AAAC,YAAMD,OAAE,KAAAL,IAAEM,GAAC,MAAH,mBAAM;AAAc,UAAGD,KAAE;AAAC,YAAIL,MAAE;AAAG,mBAAUC,OAAKC,IAAE,KAAGD,IAAE,kBAAgBI,KAAE;AAAC,UAAAL,MAAE;AAAG;AAAA,QAAK;AAAC,YAAG,CAACA,IAAE,YAAUM,OAAKL,IAAE,CAAAK,IAAE,kBAAgBD,OAAGH,IAAE,KAAKI,GAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAa,uBAAuBN,KAAEC,KAAEI,KAAEC,KAAEC,KAAEE,KAAEE,KAAE;AAAC,QAAG,CAACX,OAAG,CAACA,IAAE,OAAO,QAAO;AAAK,QAAG,EAAC,QAAOY,KAAE,oBAAmBF,IAAC,IAAEV;AAAE,UAAMe,KAAE,CAAC,CAACL;AAAE,QAAG,CAACK,MAAG,CAACT,IAAE,QAAOM;AAAE,IAAAA,MAAE,EAAEA,GAAC;AAAE,QAAIC,MAAE;AAAG,QAAGZ,QAAIA,MAAE,EAAC,YAAW,CAAC,EAAC,GAAEY,MAAE,QAAIE,IAAE;AAAC,UAAGF,QAAIH,MAAEA,IAAE,OAAQ,CAAAV,QAAC;AAJ9noB;AAIgooB,kBAAC,KAAAA,IAAE,wBAAF,mBAAuB,WAAW,SAAS;AAAA,OAAY,IAAGW,QAAID,MAAEA,IAAE,OAAQ,CAAAV,QAAC;AAJ5soB;AAI8soB,kBAAC,KAAAA,IAAE,wBAAF,mBAAuB,WAAW,SAAS;AAAA,OAAS,IAAGU,IAAE,SAAO,GAAE;AAAC,cAAMV,MAAE4B,GAAE3B,IAAE,UAAU;AAAE,cAAM,IAAG,kBAAkBS,KAAET,KAAE,EAAC,kBAAiBI,KAAE,QAAOL,KAAE,cAAaO,IAAC,GAAEE,KAAEE,GAAC;AAAA,MAAC;AAAC,UAAG,eAAeC,KAAEF,GAAC;AAAA,IAAC;AAAC,WAAOJ,OAAG,IAAG,6BAA6BM,KAAEX,KAAEK,GAAC,GAAEM;AAAA,EAAC;AAAA,EAAC,aAAa,kBAAkBZ,KAAEC,KAAEC,KAAEG,KAAEC,KAAE;AAAC,QAAG,CAACL,IAAE;AAAO,QAAIM;AAAE,eAAUE,OAAKT,KAAE;AAAC,YAAMA,MAAES,IAAE;AAAoB,UAAGT,OAAGE,OAAGA,IAAE,cAAa;AAAC,QAAAK,QAAIA,MAAE,CAAC,IAAGE,IAAE,QAAM;AAAO,cAAME,MAAEL,GAAEN,IAAE,YAAWE,IAAE,kBAAiBA,IAAE,MAAM,EAAE,KAAM,CAAAF,QAAG;AAAC,UAAAS,IAAE,QAAMF,GAAEP,KAAEC,KAAE,EAAC,OAAMK,IAAC,GAAEJ,IAAE,cAAaG,GAAC;AAAA,QAAC,CAAE;AAAE,QAAAE,IAAE,KAAKI,GAAC;AAAA,MAAC;AAAA,IAAC;AAAC,eAASJ,OAAGA,IAAE,SAAO,KAAG,MAAM,QAAQ,IAAIA,GAAC;AAAA,EAAC;AAAA,EAAC,OAAO,6BAA6BP,KAAEC,KAAEC,KAAEG,MAAE,UAAS;AAAC,QAAGL,OAAGA,IAAE,KAAK,SAAOA,IAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAiB,KAAI;AAAA,MAAgB,KAAI;AAAA,MAAmB,KAAI;AAAgB;AAAC,gBAAMM,MAAEN,IAAE;AAAa,cAAG,CAACM,IAAE;AAAO,qBAAUC,OAAKD,IAAE,CAAAC,OAAG,sBAAoBA,IAAE,QAAM,IAAG,6BAA6BA,KAAEN,KAAEC,KAAE,oBAAkBF,IAAE,OAAKA,IAAE,WAASK,GAAC;AAAA,QAAC;AAAC;AAAA,MAAM,KAAI;AAAkB;AAAC,gBAAMA,MAAEL,IAAE;AAAe,cAAG,CAACK,IAAE;AAAO,qBAAUL,OAAKK,IAAE,CAAAL,OAAG,IAAG,6BAA6BA,KAAEC,KAAEC,GAAC;AAAA,QAAC;AAAC;AAAA,MAAM,KAAI,oBAAmB;AAAC,cAAMI,MAAEN,IAAE;AAAW,YAAGM,OAAGA,IAAE,SAAS,GAAG,GAAE;AAAC,gBAAMC,MAAEA,GAAED,KAAEJ,GAAC;AAAE,UAAAF,IAAE,aAAWY,GAAEX,KAAEM,KAAEF,GAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,eAAeL,KAAEC,KAAEC,KAAEG,KAAE;AAAC,QAAGL,IAAE;AAAc,iBAAUO,OAAKN,IAAE,KAAGM,IAAE,kBAAgBP,IAAE,eAAc;AAAC,cAAMC,MAAE,GAAGM,IAAE,YAAY;AAAE,YAAGF,OAAGA,IAAE,KAAK,EAAC,KAAIL,KAAE,mBAAkBC,KAAE,OAAMD,IAAEC,GAAC,EAAC,CAAC,GAAEM,IAAE,eAAaA,IAAE,QAAM,IAAG,QAAQA,IAAE,cAAaA,IAAE,UAAU,IAAGL,KAAE;AAAC,cAAID,MAAE;AAAG,qBAAUI,OAAKH,IAAE,CAAAG,IAAE,kBAAgBL,IAAE,kBAAgBC,MAAE;AAAI,UAAAA,OAAGC,IAAE,KAAKK,GAAC;AAAA,QAAC;AAAC,UAAEA,IAAE,KAAK,MAAIP,IAAEC,GAAC,IAAEM,IAAE;AAAA,MAAM;AAAA;AAAC,YAAOP,IAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAiB,KAAI;AAAA,MAAgB,KAAI;AAAmB,YAAGA,IAAE,QAAQ,YAAUM,OAAKN,IAAE,QAAQ,KAAG,eAAeM,KAAEL,KAAEC,KAAEG,GAAC;AAAE,YAAGL,IAAE,aAAa,YAAUM,OAAKN,IAAE,aAAa,KAAG,eAAeM,KAAEL,KAAEC,KAAEG,GAAC;AAAE;AAAA,MAAM,KAAI;AAAgB;AAAA,MAAM,KAAI;AAAA,MAAiB,KAAI;AAAA,MAAe,KAAI;AAAkB,YAAGL,IAAE,QAAQ,YAAUM,OAAKN,IAAE,QAAQ,KAAG,eAAeM,KAAEL,KAAEC,KAAEG,GAAC;AAAE,YAAG,sBAAoBL,IAAE,QAAMA,IAAE,eAAe,YAAUM,OAAKN,IAAE,eAAe,KAAG,eAAeM,KAAEL,KAAEC,KAAEG,GAAC,GAAE,IAAG,eAAeC,IAAE,QAAOL,KAAEC,KAAEG,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiBL,KAAE;AAAC,eAAUC,OAAKD,IAAE,CAAAC,IAAE,IAAIA,IAAE,iBAAiB,IAAEA,IAAE;AAAA,EAAK;AAAA,EAAC,OAAO,iBAAiBD,KAAE;AAAC,QAAIC,MAAE;AAAG,eAAUC,OAAKF,IAAE,YAASE,IAAE,UAAQD,OAAG,GAAGC,IAAE,aAAa,GAAGA,IAAE,YAAY,GAAG,KAAK,UAAUA,IAAE,KAAK,CAAC;AAAI,WAAOD;AAAA,EAAC;AAAA,EAAC,OAAO,QAAQA,KAAEC,KAAE;AAAC,QAAG,mBAAiBD,IAAE,QAAOC,IAAE,MAAM,GAAG,EAAE,IAAK,CAAAF,QAAG,OAAOA,GAAC,CAAE;AAAE,QAAG,YAAUC,KAAE;AAAC,YAAMA,MAAE,IAAIW,GAAEV,GAAC,EAAE,OAAO;AAAE,aAAOD,IAAE,CAAC,KAAG,KAAIA;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAC;AAAC,IAAMwB,MAAG,CAAA1B,QAAG;AAAC,MAAG,CAACA,IAAE,QAAO,EAAE;AAAK,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAO,aAAO,EAAE;AAAA,IAAK,KAAI;AAAS,aAAO,EAAE;AAAA,IAAO,KAAI;AAAQ,aAAO,EAAE;AAAA,EAAK;AAAC;AAA5H,IAA8H,KAAG,CAAAA,QAAG;AAAC,MAAG,CAACA,IAAE,QAAO,EAAE;AAAM,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAQ,aAAO,EAAE;AAAA,IAAM,KAAI;AAAQ,aAAO,EAAE;AAAA,IAAM,KAAI;AAAQ,aAAO,EAAE;AAAA,EAAK;AAAC;AAArP,IAAuP,KAAG,CAAAA,QAAG;AAAC,MAAG,EAAEA,GAAC,EAAE,QAAM;AAAS,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAO,aAAM;AAAA,IAAO,KAAI;AAAQ,aAAM;AAAA,IAAQ,KAAI;AAAS,aAAM;AAAA,EAAQ;AAAC;AAA7W,IAA+W,KAAG,CAAAA,QAAG;AAAC,MAAG,EAAEA,GAAC,EAAE,QAAM;AAAS,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAW,aAAM;AAAA,IAAW,KAAI;AAAM,aAAM;AAAA,IAAM,KAAI;AAAS,aAAM;AAAA,IAAS,KAAI;AAAS,aAAM;AAAA,EAAQ;AAAC;AAArgB,IAAugB,KAAG,CAAAA,QAAG;AAAC,MAAG,CAACA,IAAE,QAAM,CAAC,GAAE,GAAE,GAAE,CAAC;AAAE,QAAK,EAAC,GAAEC,KAAE,GAAEC,KAAE,GAAEG,KAAE,GAAEC,IAAC,IAAEN;AAAE,SAAM,CAACC,KAAEC,KAAEG,KAAE,MAAIC,GAAC;AAAC;AAAhlB,IAAklB,KAAG,CAACN,KAAEC,QAAI;AAAC,QAAMC,MAAE,GAAGD,GAAC,GAAEI,MAAE,GAAGL,GAAC;AAAE,SAAOE,OAAGG,MAAE,GAAGH,GAAC,IAAIG,GAAC,KAAG,GAAGH,GAAC,GAAGG,GAAC;AAAE;AAArpB,IAAupB,KAAG,CAAAL,QAAG;AAAC,MAAG,CAACA,IAAE,QAAM;AAAG,UAAOA,IAAE,YAAY,GAAE;AAAA,IAAC,KAAI;AAAA,IAAO,KAAI;AAAS,aAAM;AAAA,EAAM;AAAC,SAAM;AAAE;AAAlvB,IAAovB,KAAG,CAAAA,QAAG;AAAC,MAAG,CAACA,IAAE,QAAM;AAAG,UAAOA,IAAE,YAAY,GAAE;AAAA,IAAC,KAAI;AAAA,IAAS,KAAI;AAAU,aAAM;AAAA,EAAQ;AAAC,SAAM;AAAE;AAAp1B,IAAs1B,KAAG,CAACA,KAAEC,QAAI;AAAC,QAAMC,MAAE,WAASD;AAAE,UAAOD,KAAE;AAAA,IAAC,KAAI;AAAA,IAAO,KAAI;AAAc,aAAOE,MAAE,CAAC,GAAE,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAW,KAAI;AAAiB,aAAOA,MAAE,CAAC,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAM,KAAI;AAAa,aAAOA,MAAE,CAAC,GAAE,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAY,KAAI;AAAkB,aAAOA,MAAE,CAAC,GAAE,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAgB,KAAI;AAAqB,aAAOA,MAAE,CAAC,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAoB,KAAI;AAAoB,aAAOA,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAa,KAAI;AAAmB,aAAOA,MAAE,CAAC,GAAE,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAiB,KAAI;AAAsB,aAAOA,MAAE,CAAC,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAqB,KAAI;AAAyB,aAAOA,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAY,KAAI;AAAkB,aAAOA,MAAE,CAAC,GAAE,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAQ,KAAI;AAAA,IAAe,KAAI;AAAO,aAAOJ,GAAE,MAAM,kDAAkD,GAAE,CAAC,GAAE,CAAC;AAAA,IAAE;AAAQ,aAAOA,GAAE,MAAM,0DAA0DE,GAAC,GAAG,GAAE,CAAC,GAAE,CAAC;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGA,KAAE;AAAC,SAAO,WAASA,IAAE;AAAY;AAAC,IAAM,KAAG,CAAAA,QAAG;AAAC,QAAMC,MAAE,KAAIC,MAAE;AAAG,MAAIG,KAAEC;AAAE,QAAMC,MAAEP;AAAE,MAAG,aAAWO,OAAG,oBAAkBA,KAAE;AAAC,UAAMP,MAAE;AAAI,QAAIC,MAAE,KAAK,KAAK,IAAED,MAAEE,GAAC,GAAEK,MAAE,KAAK,KAAKhB,KAAEU,MAAE,CAAC;AAAE,UAAIM,QAAIA,MAAE,IAAGN,MAAET,KAAEe,KAAEA,OAAG;AAAE,UAAME,MAAE,CAAC;AAAE,IAAAA,IAAE,KAAK,CAACP,KAAE,CAAC,CAAC;AAAE,aAAQG,MAAE,GAAEA,MAAEE,KAAEF,MAAI,CAAAI,IAAE,KAAK,CAACP,MAAE,KAAK,IAAIG,MAAEJ,GAAC,GAAE,CAACC,MAAE,KAAK,IAAIG,MAAEJ,GAAC,CAAC,CAAC;AAAE,IAAAQ,IAAE,KAAK,CAACP,KAAE,CAAC,CAAC,GAAEG,MAAE,EAAC,OAAM,CAACI,GAAC,EAAC,GAAEH,MAAE,EAAC,MAAK,CAACJ,KAAE,MAAK,CAACA,KAAE,MAAKA,KAAE,MAAKA,IAAC;AAAA,EAAC,WAAS,YAAUK,OAAG,mBAAiBA,KAAE;AAAC,UAAMP,MAAE;AAAE,IAAAK,MAAE,EAAC,OAAM,CAAC,CAAC,CAACL,KAAEE,GAAC,GAAE,CAACF,KAAEA,GAAC,GAAE,CAACE,KAAEF,GAAC,GAAE,CAACE,KAAE,CAACF,GAAC,GAAE,CAACA,KAAE,CAACA,GAAC,GAAE,CAACA,KAAE,CAACE,GAAC,GAAE,CAAC,CAACF,KAAE,CAACE,GAAC,GAAE,CAAC,CAACF,KAAE,CAACA,GAAC,GAAE,CAAC,CAACE,KAAE,CAACF,GAAC,GAAE,CAAC,CAACE,KAAEF,GAAC,GAAE,CAAC,CAACA,KAAEA,GAAC,GAAE,CAAC,CAACA,KAAEE,GAAC,GAAE,CAACF,KAAEE,GAAC,CAAC,CAAC,EAAC,GAAEI,MAAE,EAAC,MAAK,CAACJ,KAAE,MAAK,CAACA,KAAE,MAAKA,KAAE,MAAKA,IAAC;AAAA,EAAC,WAAS,cAAYK,OAAG,qBAAmBA,IAAE,CAAAF,MAAE,EAAC,OAAM,CAAC,CAAC,CAAC,CAACH,KAAE,CAAC,GAAE,CAAC,GAAEA,GAAC,GAAE,CAACA,KAAE,CAAC,GAAE,CAAC,GAAE,CAACA,GAAC,GAAE,CAAC,CAACA,KAAE,CAAC,CAAC,CAAC,EAAC,GAAEI,MAAE,EAAC,MAAK,CAACJ,KAAE,MAAK,CAACA,KAAE,MAAKA,KAAE,MAAKA,IAAC;AAAA,WAAU,aAAWK,OAAG,oBAAkBA,IAAE,CAAAF,MAAE,EAAC,OAAM,CAAC,CAAC,CAAC,CAACH,KAAE,CAACA,GAAC,GAAE,CAAC,CAACA,KAAEA,GAAC,GAAE,CAACA,KAAEA,GAAC,GAAE,CAACA,KAAE,CAACA,GAAC,GAAE,CAAC,CAACA,KAAE,CAACA,GAAC,CAAC,CAAC,EAAC,GAAEI,MAAE,EAAC,MAAK,CAACJ,KAAE,MAAK,CAACA,KAAE,MAAKA,KAAE,MAAKA,IAAC;AAAA,WAAU,QAAMK,OAAG,eAAaA,KAAE;AAAC,UAAMP,MAAE;AAAE,IAAAK,MAAE,EAAC,OAAM,CAAC,CAAC,CAAC,GAAEL,GAAC,GAAE,CAACE,MAAEF,KAAEE,GAAC,GAAE,CAACA,KAAEA,MAAEF,GAAC,GAAE,CAACA,KAAE,CAAC,GAAE,CAACE,KAAEF,MAAEE,GAAC,GAAE,CAACA,MAAEF,KAAE,CAACE,GAAC,GAAE,CAAC,GAAE,CAACF,GAAC,GAAE,CAACA,MAAEE,KAAE,CAACA,GAAC,GAAE,CAAC,CAACA,KAAEF,MAAEE,GAAC,GAAE,CAAC,CAACF,KAAE,CAAC,GAAE,CAAC,CAACE,KAAEA,MAAEF,GAAC,GAAE,CAACA,MAAEE,KAAEA,GAAC,GAAE,CAAC,GAAEF,GAAC,CAAC,CAAC,EAAC,GAAEM,MAAE,EAAC,MAAK,CAACJ,KAAE,MAAK,CAACA,KAAE,MAAKA,KAAE,MAAKA,IAAC;AAAA,EAAC,WAAS,eAAaK,OAAG,sBAAoBA,KAAE;AAAC,UAAMP,MAAEC,MAAE,oBAAkBC,MAAE,CAACF,KAAEO,MAAE,IAAE,IAAEN,KAAEQ,MAAEF,MAAEN;AAAE,IAAAI,MAAE,EAAC,OAAM,CAAC,CAAC,CAACH,KAAEO,GAAC,GAAE,CAAC,GAAEF,GAAC,GAAE,CAACP,KAAES,GAAC,GAAE,CAACP,KAAEO,GAAC,CAAC,CAAC,EAAC,GAAEH,MAAE,EAAC,MAAKJ,KAAE,MAAKO,KAAE,MAAKT,KAAE,MAAKO,IAAC;AAAA,EAAC,MAAK,aAAUA,QAAIF,MAAE,EAAC,OAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,CAAC,CAAC,EAAC,GAAEC,MAAE,EAAC,MAAK,CAACJ,KAAE,MAAK,CAACA,KAAE,MAAKA,KAAE,MAAKA,IAAC;AAAG,SAAM,CAACI,KAAED,GAAC;AAAC;AAAztC,IAA2tC,KAAG,CAAAL,QAAG,eAAaA,OAAG,iBAAeA,OAAG,YAAUA,OAAG,mBAAiBA,OAAG,sBAAoBA,OAAG,wBAAsBA;AAAj1C,IAAm1C,KAAG,CAAAA,QAAGA,MAAEA,IAAE,OAAO,CAAC,EAAE,YAAY,IAAEA,IAAE,OAAO,CAAC,IAAEA;AAAE,SAAS,GAAGA,KAAEC,KAAEC,KAAE;AAAC,MAAG,CAACF,IAAE,WAAS,EAAEC,IAAE,cAAc,EAAE;AAAO,MAAGA,IAAE,sBAAsB,QAAO,KAAKC,IAAE,KAAKD,IAAE,qBAAqB;AAAE,IAAED,IAAE,OAAO,MAAIC,IAAE,wBAAsB,EAAE,GAAEC,IAAE,KAAKD,IAAE,qBAAqB,GAAEA,IAAE,sBAAsB,KAAM,CAAAD,QAAGC,IAAE,iBAAeD,GAAE;AAAE;;;ACA98zB,IAAM6B,MAAE;AAAI,SAASC,IAAEA,KAAE;AAAC,SAAO,KAAK,IAAI,KAAK,MAAMA,MAAED,GAAC,GAAE,CAAC,IAAEA;AAAC;AAAC,IAAME,MAAE,oBAAI,IAAI,CAAC,mBAAkB,YAAW,aAAa,CAAC;AAAE,SAASC,IAAEH,KAAEG,KAAE;AAAC,SAAOD,IAAE,IAAIC,GAAC,IAAEF,IAAED,GAAC,IAAEA;AAAC;;;ACAnB,SAASI,IAAEC,KAAE;AAJ7J;AAI8J,MAAG,CAACA,IAAE,QAAO;AAAK,UAAOA,IAAE,MAAK;AAAA,IAAC,KAAI,kBAAiB;AAAC,YAAMC,MAAED,IAAE;AAAa,aAAOC,OAAG,MAAIA,IAAE,SAAOF,IAAEE,IAAE,CAAC,CAAC,IAAE;AAAA,IAAI;AAAA,IAAC,KAAI,mBAAkB;AAAC,YAAMA,MAAED,IAAE;AAAe,UAAG,CAACC,OAAG,MAAIA,IAAE,OAAO,QAAO;AAAK,YAAMC,MAAED,IAAE,CAAC;AAAE,UAAG,CAACC,IAAE,QAAO;AAAK,YAAMH,MAAEG,IAAE;AAAS,UAAG,CAACH,IAAE,QAAO;AAAK,YAAMI,MAAED,IAAE;AAAO,aAAM,CAACC,OAAG,uBAAqBA,IAAE,QAAM,oBAAkBA,IAAE,UAAM,KAAAA,IAAE,iBAAF,mBAAgB,KAAM,CAAAH,QAAG,CAAC,CAACA,IAAE,YAAU,OAAK,EAAC,MAAKD,KAAE,QAAO,uBAAqBI,IAAE,KAAI;AAAA,IAAC;AAAA,IAAC,KAAI;AAAM,aAAM,EAAC,MAAKH,IAAE,MAAK,QAAOA,IAAE,OAAM;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAASG,IAAEH,KAAE;AAAC,SAAOA,MAAEA,IAAE,QAAMA,IAAE,QAAMA,IAAE,QAAMA,IAAE,QAAM,WAASA,IAAE,QAAM,WAASA,IAAE,QAAM,WAASA,IAAE,QAAM,WAASA,IAAE,OAAK,CAAC,CAAC,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,GAAE,CAACA,IAAE,MAAKA,IAAE,IAAI,CAAC,CAAC,IAAE,OAAK;AAAI;AAAC,SAASI,IAAEJ,KAAE;AAAC,MAAIC,MAAE,IAAE,GAAEF,MAAE,KAAG,GAAEI,MAAE,IAAE,GAAEC,MAAE,KAAG;AAAE,aAAUF,OAAKF,IAAE,YAAUA,OAAKE,IAAE,CAAAF,IAAE,CAAC,IAAEC,QAAIA,MAAED,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAED,QAAIA,MAAEC,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAEG,QAAIA,MAAEH,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAEI,QAAIA,MAAEJ,IAAE,CAAC;AAAG,SAAO,IAAIA,IAAEC,KAAEE,KAAEJ,MAAEE,KAAEG,MAAED,GAAC;AAAC;AAAC,SAASE,IAAEL,KAAE;AAAC,MAAIC,MAAE,IAAE,GAAEC,MAAE,KAAG,GAAEH,MAAE,IAAE,GAAEI,MAAE,KAAG;AAAE,aAAUC,OAAKJ,IAAE,YAAUA,OAAKI,IAAE,CAAAJ,IAAE,CAAC,IAAEC,QAAIA,MAAED,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAEE,QAAIA,MAAEF,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAED,QAAIA,MAAEC,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAEG,QAAIA,MAAEH,IAAE,CAAC;AAAG,SAAM,CAACC,KAAEF,KAAEG,KAAEC,GAAC;AAAC;AAAC,SAASG,GAAEN,KAAE;AAAC,SAAOA,MAAEA,IAAE,QAAMK,IAAEL,IAAE,KAAK,IAAEA,IAAE,QAAMK,IAAEL,IAAE,KAAK,IAAEO,GAAEP,GAAC,IAAE,CAACA,IAAE,MAAKA,IAAE,MAAKA,IAAE,MAAKA,IAAE,IAAI,IAAE,OAAK;AAAI;AAAC,SAASQ,GAAER,KAAEC,KAAEC,KAAEH,KAAEI,KAAE;AAAC,QAAK,CAACC,KAAEC,KAAEC,KAAEE,GAAC,IAAER;AAAE,MAAGM,MAAEF,OAAGI,MAAEH,IAAE,QAAM,CAAC,GAAE,GAAE,CAAC;AAAE,QAAMI,MAAEH,MAAEF,KAAEM,KAAEF,MAAEH,KAAEE,MAAE,KAAII,MAAE,GAAEC,KAAE,KAAK,MAAM,OAAI,MAAGL,MAAEI,IAAE,GAAEE,MAAGN,MAAE,KAAGK,KAAED,QAAI,KAAK,IAAIF,KAAEC,EAAC,GAAEI,KAAE,KAAK,MAAML,MAAEI,EAAC,IAAE,IAAED,IAAEG,KAAE,KAAK,MAAML,KAAEG,EAAC,IAAE,IAAED;AAAE,MAAII,MAAE;AAAE,MAAGf,KAAE;AAAC,IAAAe,MAAED,KAAEF,MAAGZ,IAAE,OAAKA,IAAE;AAAA,EAAK;AAAC,MAAIgB,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,EAAApB,QAAII,MAAEF,OAAGC,OAAGD,IAAE,OAAKA,IAAE,OAAK,MAAIkB,MAAGlB,IAAE,OAAKA,IAAE,SAAOA,IAAE,OAAKA,IAAE,OAAMgB,KAAElB,IAAE,KAAGG,MAAEiB,KAAGD,KAAEnB,IAAE,IAAEG,QAAIe,KAAElB,IAAE,GAAEmB,KAAEnB,IAAE,KAAIE,QAAIgB,KAAE,OAAIhB,IAAE,OAAKA,IAAE,QAAMgB,MAAGhB,IAAE,OAAKA,IAAE,OAAMiB,KAAE,OAAIjB,IAAE,OAAKA,IAAE,QAAMiB,MAAGjB,IAAE,OAAKA,IAAE,QAAOgB,MAAGb,KAAEc,MAAGb,KAAEY,MAAGJ,IAAEK,MAAGL,IAAEI,MAAGL,IAAEM,MAAGN;AAAE,MAAIQ,KAAEH,KAAEH,KAAE,KAAGO,KAAEH,KAAEH,KAAE;AAAG,SAAOZ,OAAGD,QAAIkB,MAAGlB,MAAEiB,IAAEE,MAAGnB,MAAG,CAACc,KAAEI,IAAEC,EAAC;AAAC;AAAC,SAASZ,IAAET,KAAE;AAAC,QAAMC,MAAEE,IAAEH,IAAE,IAAI,GAAEE,MAAEE,IAAEH,GAAC,GAAEF,MAAE,KAAIM,MAAE,GAAEC,MAAE,KAAK,MAAM,OAAI,MAAGP,MAAEM,IAAE,GAAEG,OAAGT,MAAE,KAAGO,MAAED,QAAI,KAAK,IAAIH,IAAE,OAAMA,IAAE,MAAM,GAAEO,MAAE,KAAK,MAAMP,IAAE,QAAMM,GAAC,IAAE,IAAEF,KAAEM,KAAE,KAAK,MAAMV,IAAE,SAAOM,GAAC,IAAE,IAAEF,KAAEO,KAAE,CAAC;AAAE,aAAUV,OAAKF,IAAE,KAAGE,OAAGA,IAAE,SAAO,GAAE;AAAC,UAAMF,MAAE,CAAC;AAAE,eAAUF,OAAKI,KAAE;AAAC,UAAG,CAACA,KAAEC,GAAC,IAAEL;AAAE,MAAAI,OAAGD,IAAE,GAAEE,OAAGF,IAAE,GAAEC,OAAGK,KAAEJ,OAAGI,KAAEL,OAAGG,MAAE,KAAGF,OAAGE,MAAE,KAAGN,IAAE,SAAOC,IAAE,KAAK,CAACE,KAAEC,GAAC,CAAC,IAAEH,IAAE,KAAK,CAAC,KAAK,MAAME,GAAC,GAAE,KAAK,MAAMC,GAAC,CAAC,CAAC;AAAA,IAAC;AAAC,QAAGJ,IAAE,QAAO;AAAC,YAAMA,MAAEC,IAAE,SAAO;AAAE,MAAAA,IAAE,CAAC,EAAE,CAAC,MAAIA,IAAED,GAAC,EAAE,CAAC,KAAGC,IAAE,CAAC,EAAE,CAAC,MAAIA,IAAED,GAAC,EAAE,CAAC,KAAGC,IAAE,KAAKA,IAAE,CAAC,CAAC;AAAA,IAAC;AAAC,IAAAY,GAAE,KAAKZ,GAAC;AAAA,EAAC;AAAC,QAAMa,KAAEJ,GAAEG,IAAEJ,KAAEG,IAAEN,GAAC;AAAE,SAAON,IAAE,UAAQO,IAAEM,IAAEJ,KAAEG,IAAEN,KAAEQ,EAAC,GAAE,CAACH,IAAEG,IAAER,GAAC,GAAEG,KAAEG,EAAC;AAAC;AAAC,SAASF,GAAEV,KAAEC,KAAEC,KAAEH,KAAE;AAAC,QAAMI,MAAEF,MAAEC,KAAEE,MAAE,IAAI,MAAMD,GAAC,GAAEE,MAAEN,MAAEA,MAAE;AAAE,WAAQO,MAAE,GAAEA,MAAEH,KAAE,EAAEG,IAAE,CAAAF,IAAEE,GAAC,IAAED;AAAE,aAAUC,OAAKN,KAAE;AAAC,UAAMA,MAAEM,IAAE;AAAO,aAAQH,MAAE,GAAEA,MAAEH,KAAE,EAAEG,KAAE;AAAC,YAAMH,MAAEM,IAAEH,MAAE,CAAC,GAAEE,MAAEC,IAAEH,GAAC;AAAE,UAAIK,KAAEC,KAAEC,IAAEH;AAAE,MAAAP,IAAE,CAAC,IAAEK,IAAE,CAAC,KAAGG,MAAER,IAAE,CAAC,GAAES,MAAEJ,IAAE,CAAC,MAAIG,MAAEH,IAAE,CAAC,GAAEI,MAAET,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAEK,IAAE,CAAC,KAAGK,KAAEV,IAAE,CAAC,GAAEO,MAAEF,IAAE,CAAC,MAAIK,KAAEL,IAAE,CAAC,GAAEE,MAAEP,IAAE,CAAC;AAAG,UAAIW,MAAE,KAAK,MAAMH,GAAC,IAAET,KAAEa,KAAE,KAAK,MAAMH,GAAC,IAAEV,KAAEc,KAAE,KAAK,MAAMH,EAAC,IAAEX,KAAEe,KAAE,KAAK,MAAMP,GAAC,IAAER;AAAE,MAAAY,MAAE,MAAIA,MAAE,IAAGC,KAAEX,QAAIW,KAAEX,MAAGY,KAAE,MAAIA,KAAE,IAAGC,KAAEZ,QAAIY,KAAEZ;AAAG,YAAMa,KAAEV,IAAE,CAAC,IAAEL,IAAE,CAAC,GAAEgB,MAAEX,IAAE,CAAC,IAAEL,IAAE,CAAC,GAAEiB,KAAEF,KAAEA,KAAEC,MAAEA;AAAE,eAAQjB,MAAEY,KAAEZ,MAAEa,IAAEb,MAAI,UAAQI,MAAEU,IAAEV,MAAEW,IAAEX,OAAI;AAAC,YAAIG,KAAEE,KAAEC,OAAGV,MAAEC,IAAE,CAAC,KAAGe,MAAGZ,MAAEH,IAAE,CAAC,KAAGgB;AAAE,QAAAP,MAAE,KAAGH,MAAEN,IAAE,CAAC,GAAEQ,MAAER,IAAE,CAAC,KAAGS,MAAEQ,MAAGX,MAAED,IAAE,CAAC,GAAEG,MAAEH,IAAE,CAAC,MAAII,OAAGQ,IAAEX,MAAEN,IAAE,CAAC,IAAES,MAAEM,IAAEP,MAAER,IAAE,CAAC,IAAES,MAAEO;AAAG,cAAMN,MAAGX,MAAEO,QAAIP,MAAEO,QAAIH,MAAEK,QAAIL,MAAEK,MAAGD,OAAGL,MAAEC,MAAE,KAAGF,MAAEF;AAAE,QAAAW,KAAEN,IAAEG,GAAC,MAAIH,IAAEG,GAAC,IAAEG;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC,WAAQJ,MAAE,GAAEA,MAAEH,KAAE,EAAEG,IAAE,CAAAF,IAAEE,GAAC,IAAE,KAAK,KAAKF,IAAEE,GAAC,CAAC;AAAE,SAAOF;AAAC;AAAC,SAASG,IAAEP,KAAEC,KAAEC,KAAEH,KAAEI,KAAE;AAAC,aAAUC,OAAKJ,KAAE;AAAC,UAAMA,MAAEI,IAAE;AAAO,aAAQC,MAAE,GAAEA,MAAEL,KAAE,EAAEK,KAAE;AAAC,YAAML,MAAEI,IAAEC,MAAE,CAAC,GAAEC,MAAEF,IAAEC,GAAC;AAAE,UAAIG,KAAEC,KAAEC,IAAEH;AAAE,MAAAP,IAAE,CAAC,IAAEM,IAAE,CAAC,KAAGE,MAAER,IAAE,CAAC,GAAES,MAAEH,IAAE,CAAC,MAAIE,MAAEF,IAAE,CAAC,GAAEG,MAAET,IAAE,CAAC,IAAGA,IAAE,CAAC,IAAEM,IAAE,CAAC,KAAGI,KAAEV,IAAE,CAAC,GAAEO,MAAED,IAAE,CAAC,MAAII,KAAEJ,IAAE,CAAC,GAAEC,MAAEP,IAAE,CAAC;AAAG,UAAIW,MAAE,KAAK,MAAMH,GAAC,GAAEI,KAAE,KAAK,MAAMH,GAAC,IAAE,GAAEI,KAAE,KAAK,MAAMH,EAAC,GAAEI,KAAE,KAAK,MAAMP,GAAC,IAAE;AAAE,MAAAI,MAAEZ,QAAIY,MAAEZ,MAAGa,KAAEX,MAAEF,QAAIa,KAAEX,MAAEF,MAAGc,KAAEd,QAAIc,KAAEd,MAAGe,KAAEZ,MAAEH,QAAIe,KAAEZ,MAAEH;AAAG,eAAQK,MAAES,IAAET,MAAEU,IAAE,EAAEV,KAAE;AAAC,YAAGJ,IAAE,CAAC,IAAEI,OAAGE,IAAE,CAAC,IAAEF,IAAE;AAAS,cAAMC,OAAGH,MAAEE,MAAE,KAAGH;AAAE,iBAAQA,MAAEU,KAAEV,MAAEW,IAAE,EAAEX,IAAE,CAAAA,OAAGK,IAAE,CAAC,IAAEN,IAAE,CAAC,MAAII,MAAEJ,IAAE,CAAC,MAAIM,IAAE,CAAC,IAAEN,IAAE,CAAC,KAAGA,IAAE,CAAC,MAAIG,IAAEE,MAAEJ,GAAC,IAAE,CAACE,IAAEE,MAAEJ,GAAC;AAAG,iBAAQD,MAAED,KAAEC,MAAEW,KAAE,EAAEX,IAAE,CAAAG,IAAEE,MAAEL,GAAC,IAAE,CAACG,IAAEE,MAAEL,GAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAASW,IAAEV,KAAEC,KAAE;AAAC,QAAMH,MAAE,IAAEG,KAAEC,MAAEF,IAAE,QAAOG,MAAE,IAAI,WAAW,IAAED,GAAC;AAAE,WAAQE,MAAE,GAAEA,MAAEF,KAAE,EAAEE,KAAE;AAAC,UAAMH,MAAE,MAAGD,IAAEI,GAAC,IAAEN;AAAE,IAAAE,GAAEC,KAAEE,KAAE,IAAEC,GAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;;;ACAhxG,IAAMkB,MAAE,KAAG;AAAG,IAAMC,KAAN,MAAO;AAAA,EAAC,OAAO,eAAeC,KAAEC,KAAEC,KAAEH,KAAE;AAAC,UAAMI,KAAEC,GAAEH,GAAC,GAAEI,MAAEP;AAAE,QAAIQ,MAAE,IAAIL,GAAEE,EAAC;AAAE,eAAUI,OAAKP,KAAE;AAAC,YAAMA,MAAEQ,GAAED,GAAC;AAAE,MAAAP,QAAIM,MAAEN,IAAE,QAAQM,KAAEC,KAAEF,KAAEH,KAAEH,GAAC;AAAA,IAAE;AAAC,WAAOO;AAAA,EAAC;AAAA,EAAC,OAAO,KAAKN,KAAE;AAAC,UAAMC,MAAED,IAAE,KAAK;AAAE,WAAOS,GAAER,GAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAO,aAAaM,KAAEL,KAAEJ,KAAE;AAAC,QAAG,CAACS,IAAE,QAAOL;AAAE,QAAIH,MAAE,IAAIE,GAAEC,GAAC;AAAE,eAAUF,OAAKO,KAAE;AAAC,YAAMN,MAAEO,GAAER,GAAC;AAAE,MAAAC,QAAIF,MAAEE,IAAE,QAAQF,KAAEC,KAAE,GAAE,MAAKF,GAAC;AAAA,IAAE;AAAC,QAAIK,IAAEE,MAAE;AAAK,WAAKF,KAAEJ,IAAE,KAAK,IAAG,CAAAM,MAAEN,GAAEM,GAAC,IAAEN,GAAEI,EAAC,KAAGE,IAAE,MAAM,KAAK,GAAGF,GAAE,KAAK,IAAE,EAAEE,GAAC,KAAG,EAAEF,EAAC,KAAGE,IAAE,MAAM,KAAK,GAAGF,GAAE,KAAK,IAAEE,MAAEF;AAAE,WAAOE;AAAA,EAAC;AAAC;;;ACA/oB,SAASK,IAAEA,KAAEC,KAAE;AAAC,MAAIC;AAAE,MAAG,YAAU,OAAOF,IAAE,CAAAE,MAAE,EAAEF,MAAE,SAASC,GAAC,GAAG;AAAA,OAAM;AAAC,QAAIE,MAAE;AAAG,IAAAD,MAAEF,MAAEC;AAAE,OAAE;AAAC,MAAAC,MAAE,OAAKA,OAAG,IAAEA,OAAGC,MAAE;AAAA,IAAC,SAAO,KAAG,EAAEA;AAAA,EAAE;AAAC,UAAO,IAAED,OAAG,KAAG,OAAK;AAAC;AAAC,SAASD,IAAEE,KAAE;AAAC,SAAO,KAAK,MAAMH,IAAEG,KAAED,GAAC,IAAEE,GAAC;AAAC;AAAC,IAAMF,MAAE;AAAR,IAAiBE,MAAE;;;ACA2hC,IAAM,IAAE,EAAE,UAAU,8BAA8B;AAAE,SAASC,GAAEC,KAAE;AAAC,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAO,aAAOA,GAAE;AAAA,IAAK,KAAI;AAAS,aAAOA,GAAE;AAAA,IAAO;AAAQ,aAAOA,GAAE;AAAA,EAAK;AAAC;AAAC,SAASC,GAAED,KAAE;AAAC,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAQ,aAAO,EAAE;AAAA,IAAM,KAAI;AAAQ,aAAO,EAAE;AAAA,IAAM;AAAQ,aAAO,EAAE;AAAA,EAAK;AAAC;AAAC,SAASE,GAAEF,KAAEG,KAAEC,KAAEC,KAAE;AAAC,MAAIC;AAAE,EAAAN,IAAEG,GAAC,IAAEG,MAAEN,IAAEG,GAAC,KAAGG,MAAE,CAAC,GAAEN,IAAEG,GAAC,IAAEG,MAAGA,IAAEF,GAAC,IAAEC;AAAC;AAAC,SAASE,GAAEP,KAAE;AAAC,QAAMG,MAAEH,IAAE;AAAgB,SAAOG,OAAGA,IAAE,cAAY,EAAE,MAAI,EAAE;AAAM;AAAC,eAAeK,GAAER,KAAEG,KAAEC,KAAEC,KAAEI,KAAE;AAAC,QAAMC,MAAEL,OAAG,CAAC;AAAE,MAAG,CAACL,IAAE,QAAOU;AAAE,MAAIC,KAAEC;AAAE,QAAMC,MAAE,CAAC;AAAE,MAAG,yBAAuBb,IAAE,KAAK,QAAO,EAAE,MAAM,4CAA4C,GAAEU;AAAE,MAAGC,MAAEX,IAAE,QAAOY,MAAEZ,IAAE,oBAAmBY,KAAE;AAAC,UAAMZ,MAAE,CAAC;AAAE,eAAUI,OAAKQ,KAAE;AAAC,YAAMP,MAAED,IAAE;AAAoB,UAAGC,OAAGF,KAAE;AAAC,cAAMM,MAAEJ,IAAE,YAAWK,MAAEL,GAAEI,KAAEN,IAAE,kBAAiBA,IAAE,MAAM,EAAE,KAAM,CAAAH,QAAG;AAAC,YAAEA,GAAC,KAAGE,GAAEW,KAAET,IAAE,eAAcA,IAAE,cAAaJ,GAAC;AAAA,QAAC,CAAE;AAAE,QAAAA,IAAE,KAAKU,GAAC;AAAA,MAAC,MAAM,SAAMN,IAAE,SAAOF,GAAEW,KAAET,IAAE,eAAcA,IAAE,cAAaA,IAAE,KAAK;AAAA,IAAC;AAAC,IAAAJ,IAAE,SAAO,KAAG,MAAM,QAAQ,IAAIA,GAAC;AAAA,EAAC;AAAC,QAAMc,KAAE,CAAC;AAAE,UAAOC,IAAE,eAAeJ,KAAEP,KAAEU,EAAC,GAAEA,GAAE,SAAO,KAAG,MAAM,QAAQ,IAAIA,EAAC,GAAEH,OAAA,gBAAAA,IAAG,MAAK;AAAA,IAAC,KAAI;AAAA,IAAiB,KAAI;AAAA,IAAgB,KAAI;AAAmB,MAAAK,GAAEL,KAAEC,KAAEC,KAAEV,KAAEO,KAAEN,KAAE,CAAC,CAACK,GAAC;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAASM,GAAEhB,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAEC,KAAE;AAAC,MAAG,CAACV,IAAE;AAAO,QAAMW,MAAEX,IAAE;AAAa,MAAG,CAACW,IAAE;AAAO,QAAMC,MAAEZ,IAAE;AAAQ,MAAIiB,MAAE,EAAE;AAAO,QAAMJ,MAAEE,IAAE,QAAQf,GAAC,KAAG;AAAE,uBAAmBA,IAAE,QAAM,UAAQA,IAAE,mBAAiBiB,MAAE,EAAE;AAAK,MAAIC,KAAEP,IAAE;AAAO,SAAKO,QAAK;AAAC,UAAMC,KAAER,IAAEO,EAAC;AAAE,QAAG,CAACC,MAAG,UAAKA,GAAE,OAAO;AAAS,QAAIC;AAAE,IAAAR,OAAGA,IAAE,WAASQ,MAAE,CAAC,GAAGR,GAAC;AAAG,UAAMS,KAAEF,GAAE;AAAQ,IAAAE,MAAGA,GAAE,WAAST,MAAEQ,IAAE,KAAK,GAAGC,EAAC,IAAED,MAAE,CAAC,GAAGC,EAAC;AAAG,UAAMC,MAAE,CAAC;AAAE,QAAIC;AAAE,OAAE,oBAAoBH,KAAEjB,KAAEmB,GAAC,GAAEC,KAAED,IAAE,SAAO,IAAEE,IAAGJ,KAAEE,KAAElB,KAAEC,GAAC,IAAEe;AAAE,UAAMK,KAAE,CAAC;AAAE,YAAO,GAAE,wBAAwBN,IAAEhB,KAAEsB,EAAC,GAAEN,GAAE,MAAK;AAAA,MAAC,KAAI;AAAe,QAAAO,GAAEP,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,GAAC;AAAE;AAAA,MAAM,KAAI;AAAiB,UAAEa,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEI,KAAEH,GAAC;AAAE;AAAA,MAAM,KAAI;AAAe,QAAAqB,GAAER,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,GAAC;AAAE;AAAA,MAAM,KAAI;AAAkB,QAAAsB,GAAET,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,GAAC;AAAE;AAAA,MAAM,KAAI;AAAiB,QAAAuB,GAAEV,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,KAAE,uBAAqBN,IAAE,MAAKa,GAAC;AAAE;AAAA,MAAM,KAAI;AAAmB,QAAAiB,GAAEX,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,KAAE,uBAAqBN,IAAE,MAAKa,GAAC;AAAE;AAAA,MAAM,KAAI;AAAoB,QAAAkB,GAAEZ,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,KAAE,uBAAqBN,IAAE,MAAKa,GAAC;AAAE;AAAA,MAAM,KAAI;AAAqB,YAAGmB,GAAEb,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,GAAC,EAAE;AAAM;AAAA,MAAM,KAAI;AAAmB,YAAG0B,GAAEb,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,GAAC,EAAE;AAAM,4BAAkBN,IAAE,SAAOiB,MAAEV,GAAEY,EAAC,IAAGc,GAAEd,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEI,KAAEH,KAAEW,KAAEJ,GAAC;AAAE;AAAA,MAAM,KAAI;AAAkB,YAAGmB,GAAEb,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,GAAC,EAAE;AAAM,4BAAkBN,IAAE,SAAOiB,MAAEV,GAAEY,EAAC,IAAGe,GAAEf,IAAEI,IAAEnB,KAAEqB,IAAEpB,KAAEC,KAAEG,KAAEQ,KAAEJ,KAAEH,GAAC;AAAE;AAAA,MAAM;AAAQ,UAAE,MAAM,4BAA2BS,GAAE,IAAI;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAASO,GAAE1B,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAE;AAAC,QAAMC,MAAEV,IAAE,eAAcW,MAAEA,GAAEX,IAAE,KAAK,GAAE,CAACiB,KAAEJ,GAAC,IAAEsB,IAAG9B,KAAEK,KAAEP,KAAE,MAAK,IAAI,GAAEgB,KAAE,EAAE,KAAK,UAAUnB,GAAC,IAAEa,GAAC,EAAE,SAAS;AAAE,EAAAJ,IAAE,KAAK,EAAC,MAAK,QAAO,cAAaU,IAAE,cAAaF,MAAE,MAAIE,KAAEA,IAAE,KAAInB,KAAE,mBAAkB,MAAK,aAAY,CAAC,CAACA,IAAE,aAAY,OAAMoC,IAAG1B,KAAEN,KAAE,SAAQE,KAAEK,KAAE0B,GAAE,GAAE,QAAO,GAAE,OAAM,GAAE,SAAQ,GAAE,SAAQ,GAAE,QAAO,GAAE,SAAQlC,KAAE,mBAAkB,OAAG,iBAAgB,KAAE,CAAC;AAAC;AAAC,SAAS,EAAEH,KAAEG,KAAEC,KAAEC,KAAEC,KAAEI,KAAEC,KAAE;AAAC,QAAMM,MAAEjB,IAAE,eAAca,MAAEC,GAAEd,GAAC,GAAE,CAACmB,IAAEL,EAAC,IAAEqB,IAAG9B,KAAEY,KAAEd,KAAE,MAAK,IAAI,GAAEiB,MAAE,EAAE,KAAK,UAAUpB,GAAC,IAAEc,EAAC,EAAE,SAAS,GAAEI,KAAE,EAAE,GAAGlB,IAAE,GAAG,GAAG,KAAK,UAAUA,IAAE,kBAAkB,CAAC,EAAE,EAAE,SAAS;AAAE,MAAIqB,KAAE,EAAErB,IAAE,MAAM;AAAE,MAAG,WAAUA,OAAG,YAAU,OAAOA,IAAE,OAAM;AAAC,UAAMG,MAAEH,IAAE;AAAM,QAAII,MAAE;AAAE,UAAMC,MAAEK,IAAE,YAAYV,IAAE,GAAG;AAAE,MAAEK,GAAC,MAAID,MAAEC,IAAE,QAAMA,IAAE,SAAQgB,MAAGjB,OAAGJ,IAAE,SAAOG;AAAA,EAAE;AAAC,EAAAQ,IAAE,KAAK,EAAC,MAAK,QAAO,cAAaS,KAAE,cAAaD,KAAE,MAAID,KAAEA,IAAE,KAAIlB,KAAE,mBAAkB,MAAK,aAAY,CAAC,CAACA,IAAE,aAAY,SAAQG,KAAE,OAAMiC,IAAGnB,KAAEb,KAAE,aAAYE,KAAEO,KAAEwB,GAAE,GAAE,QAAOD,IAAGnB,KAAEb,KAAE,UAASE,KAAEN,IAAE,MAAM,GAAE,QAAOoC,IAAGnB,KAAEb,KAAE,UAASE,KAAEe,EAAC,GAAE,OAAMe,IAAGnB,KAAEb,KAAE,YAAWE,KAAE,EAAEN,IAAE,QAAQ,CAAC,GAAE,SAAQoC,IAAGnB,KAAEb,KAAE,WAAUE,KAAE,EAAEN,IAAE,OAAO,CAAC,GAAE,SAAQoC,IAAGnB,KAAEb,KAAE,WAAUE,KAAE,EAAEN,IAAE,OAAO,CAAC,GAAE,KAAIA,IAAE,KAAI,mBAAkB,OAAG,iBAAgB,MAAE,CAAC;AAAC;AAAC,SAAS2B,GAAE3B,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAE;AAJnmJ;AAIomJ,QAAMC,MAAE,CAAC,YAAW,WAAU,SAAS,GAAEC,MAAEN,IAAE,OAAQ,CAAAF,QAAGA,IAAE,kBAAgBH,IAAE,iBAAe,CAACU,IAAE,SAASP,IAAE,YAAY,CAAE,GAAEc,MAAEjB,IAAE;AAAc,MAAG,CAACa,KAAEM,EAAC,IAAEgB,IAAG9B,KAAEY,KAAEd,KAAE,MAAK,IAAI;AAAE,QAAMW,KAAE,EAAE,KAAK,UAAUd,GAAC,IAAEmB,EAAC,EAAE,SAAS,GAAEC,MAAE,EAAE,GAAGpB,IAAE,UAAU,GAAG,KAAK,UAAUA,IAAE,UAAU,CAAC,EAAE,EAAE,SAAS;AAAE,MAAIkB,KAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,EAAC,GAAEG,KAAE;AAAG,QAAMC,OAAE,WAAAtB,IAAE,eAAF,mBAAc,iBAAd,mBAA4B,KAAM,CAAAA,QAAC;AAJr8J,QAAAsC;AAIu8J,gCAAmBtC,IAAE,QAAM,UAAMsC,MAAAlC,IAAEJ,IAAE,aAAa,MAAjB,gBAAAsC,IAAoB;AAAA;AAAQ,MAAGhB,KAAE;AAAC,IAAAJ,KAAEP,GAAEW,IAAE,KAAK,GAAEJ,KAAEkB,IAAGd,IAAE,eAAclB,KAAE,SAAQE,KAAEY,IAAEmB,GAAE;AAAE,UAAMrC,MAAE,cAAY,OAAOkB;AAAE,IAAAL,MAAEA,OAAGb,KAAEqB,KAAE,QAAMC,IAAE,SAAOtB;AAAA,EAAC;AAAC,EAAAS,IAAE,KAAK,EAAC,MAAK,QAAO,cAAaK,IAAE,cAAaD,MAAE0B,IAAGnB,KAAEhB,KAAEO,KAAEL,GAAC,IAAEc,KAAE,KAAIpB,KAAE,mBAAkBW,KAAE,aAAY,CAAC,CAACX,IAAE,aAAY,SAAQG,KAAE,OAAMe,IAAE,QAAOkB,IAAGnB,KAAEb,KAAE,cAAaE,KAAEN,IAAE,UAAU,GAAE,QAAO,GAAE,OAAMoC,IAAGnB,KAAEb,KAAE,YAAWE,KAAE,EAAEN,IAAE,QAAQ,CAAC,GAAE,SAAQoC,IAAGnB,KAAEb,KAAE,WAAUE,KAAE,EAAEN,IAAE,OAAO,CAAC,GAAE,SAAQoC,IAAGnB,KAAEb,KAAE,WAAUE,KAAE,EAAEN,IAAE,OAAO,CAAC,GAAE,mBAAkB,OAAG,iBAAgB,MAAG,+BAA8B,CAACqB,GAAC,CAAC;AAAC;AAAC,SAASO,GAAE5B,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAE;AAAC,QAAMC,MAAEV,IAAE,eAAc,CAACW,KAAEM,GAAC,IAAEkB,IAAG9B,KAAEK,KAAEP,KAAE,MAAK,IAAI,GAAEU,MAAE,EAAE,KAAK,UAAUb,GAAC,IAAEiB,GAAC,EAAE,SAAS;AAAE,EAAAR,IAAE,KAAK,EAAC,MAAK,QAAO,cAAaI,KAAE,cAAaF,MAAE4B,IAAG1B,KAAET,KAAEC,KAAEC,GAAC,IAAEO,KAAE,KAAIb,KAAE,mBAAkB,MAAK,aAAY,CAAC,CAACA,IAAE,aAAY,SAAQG,KAAE,OAAM,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,EAAC,GAAE,QAAO,GAAE,OAAM,GAAE,SAAQ,GAAE,SAAQ,GAAE,QAAO,GAAE,mBAAkB,OAAG,iBAAgB,MAAE,CAAC;AAAC;AAAC,SAAS0B,GAAE7B,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAEC,KAAEC,KAAE;AAAC,QAAMM,MAAEjB,IAAE,eAAca,MAAEF,GAAEX,IAAE,KAAK,GAAEmB,KAAE,QAAMnB,IAAE,QAAMA,IAAE,QAAM,GAAEc,KAAEf,GAAEC,IAAE,QAAQ,GAAEoB,MAAEnB,GAAED,IAAE,SAAS,GAAEkB,KAAElB,IAAE,YAAW,CAACqB,IAAEC,GAAC,IAAEa,IAAG9B,KAAEY,KAAEd,KAAE,MAAK,IAAI,GAAEoB,KAAE,EAAE,KAAK,UAAUvB,GAAC,IAAEsB,GAAC,EAAE,SAAS;AAAE,MAAIG,IAAEe;AAAE,MAAGrC,OAAGA,eAAa,SAAOA,IAAE,SAAO,GAAE;AAAC,UAAMH,MAAEG,IAAEA,IAAE,SAAO,CAAC;AAAE,QAAG,+BAA6BH,IAAE,QAAM,mBAAiBA,IAAE,kBAAgB,SAAOA,IAAE,iBAAgB;AAAC,YAAMA,OAAGG,MAAE,CAAC,GAAGA,GAAC,GAAG,IAAI;AAAE,MAAAsB,KAAEzB,IAAE,cAAawC,KAAExC,IAAE;AAAA,IAAS;AAAA,EAAC;AAAC,EAAAS,IAAE,KAAK,EAAC,MAAK,QAAO,cAAac,IAAE,cAAaF,KAAE,MAAIE,KAAEA,IAAE,KAAIvB,KAAE,mBAAkB,MAAK,WAAUU,KAAE,aAAY,CAAC,CAACV,IAAE,aAAY,SAAQG,KAAE,OAAMiC,IAAGnB,KAAEb,KAAE,SAAQE,KAAEO,KAAEwB,GAAE,GAAE,OAAMD,IAAGnB,KAAEb,KAAE,SAAQE,KAAEa,EAAC,GAAE,KAAIiB,IAAGnB,KAAEb,KAAE,YAAWE,KAAEQ,EAAC,GAAE,MAAKsB,IAAGnB,KAAEb,KAAE,aAAYE,KAAEc,GAAC,GAAE,YAAWF,MAAGkB,IAAGnB,KAAEb,KAAE,cAAaE,KAAEY,EAAC,GAAE,gBAAeP,KAAE,QAAO8B,IAAGzC,IAAE,IAAI,GAAE,cAAayB,IAAE,WAAUe,IAAE,iBAAgB,KAAE,CAAC;AAAC;AAAC,SAASV,GAAE9B,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAEC,KAAEC,KAAE;AAAC,QAAMM,MAAE,EAAE,GAAGjB,IAAE,GAAG,GAAG,KAAK,UAAUA,IAAE,kBAAkB,CAAC,EAAE,EAAE,SAAS,GAAEa,MAAEb,IAAE,eAAcmB,KAAEL,GAAEd,GAAC,GAAEc,KAAE,QAAMd,IAAE,QAAMA,IAAE,QAAM,GAAEoB,MAAErB,GAAEC,IAAE,QAAQ,GAAEkB,KAAEjB,GAAED,IAAE,SAAS,GAAEqB,KAAErB,IAAE,YAAW,CAACsB,KAAEC,EAAC,IAAEY,IAAG9B,KAAEQ,KAAEV,KAAE,MAAK,IAAI,GAAEsB,KAAE,EAAE,KAAK,UAAUzB,GAAC,IAAEuB,EAAC,EAAE,SAAS;AAAE,EAAAd,IAAE,KAAK,EAAC,MAAK,QAAO,cAAagB,IAAE,cAAaH,MAAE,MAAIL,MAAEA,KAAE,KAAIjB,KAAE,mBAAkB,MAAK,WAAUU,KAAE,aAAY,CAAC,CAACV,IAAE,aAAY,SAAQG,KAAE,OAAMiC,IAAGvB,KAAET,KAAE,aAAYE,KAAEa,IAAEkB,GAAE,GAAE,OAAMD,IAAGvB,KAAET,KAAE,SAAQE,KAAEQ,EAAC,GAAE,KAAIsB,IAAGvB,KAAET,KAAE,YAAWE,KAAEc,GAAC,GAAE,MAAKgB,IAAGvB,KAAET,KAAE,aAAYE,KAAEY,EAAC,GAAE,YAAWG,MAAGe,IAAGvB,KAAET,KAAE,cAAaE,KAAEe,EAAC,GAAE,gBAAeV,KAAE,QAAO8B,IAAGzC,IAAE,IAAI,GAAE,cAAa,MAAK,WAAU,OAAG,KAAIA,IAAE,KAAI,iBAAgB,MAAE,CAAC;AAAC;AAAC,SAAS+B,GAAE/B,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAEC,KAAEC,KAAE;AAAC,QAAMM,MAAEjB,IAAE,eAAca,MAAE,QAAMb,IAAE,QAAMA,IAAE,QAAM,GAAEmB,KAAEpB,GAAEC,IAAE,QAAQ,GAAEc,KAAEb,GAAED,IAAE,SAAS,GAAEoB,MAAEpB,IAAE,YAAW,CAACkB,IAAEG,EAAC,IAAEc,IAAG9B,KAAEY,KAAEd,KAAE,MAAK,IAAI,GAAEmB,MAAE,EAAE,KAAK,UAAUtB,GAAC,IAAEqB,EAAC,EAAE,SAAS;AAAE,EAAAZ,IAAE,KAAK,EAAC,MAAK,QAAO,cAAaa,KAAE,cAAaJ,KAAEqB,IAAGjB,KAAElB,KAAEC,KAAEC,GAAC,IAAEgB,KAAE,KAAItB,KAAE,mBAAkB,MAAK,WAAUU,KAAE,aAAY,CAAC,CAACV,IAAE,aAAY,SAAQG,KAAE,OAAM,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,EAAC,GAAE,OAAMiC,IAAGnB,KAAEb,KAAE,SAAQE,KAAEO,GAAC,GAAE,KAAIuB,IAAGnB,KAAEb,KAAE,YAAWE,KAAEa,EAAC,GAAE,MAAKiB,IAAGnB,KAAEb,KAAE,aAAYE,KAAEQ,EAAC,GAAE,YAAWM,OAAGgB,IAAGnB,KAAEb,KAAE,cAAaE,KAAEc,GAAC,GAAE,gBAAeT,KAAE,QAAO8B,IAAGzC,IAAE,IAAI,GAAE,cAAa,MAAK,WAAU,OAAG,iBAAgB,MAAE,CAAC;AAAC;AAAC,SAASgC,GAAEhC,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAE;AAAC,QAAK,EAAC,iBAAgBC,KAAE,MAAKO,IAAC,IAAEjB;AAAE,MAAG,CAACU,OAAG,sCAAoCA,IAAE,KAAK,QAAM;AAAG,MAAG,sBAAoBO,OAAG,uBAAqBA,KAAE;AAAC,UAAMb,MAAEJ,IAAE;AAAc,QAAGI,KAAE;AAAC,YAAK,CAACJ,KAAEM,GAAC,IAAE6B,IAAG9B,KAAED,KAAED,KAAE,MAAK,IAAI;AAAE,UAAGH,IAAE,QAAM;AAAA,IAAE;AAAC,UAAMM,MAAEI,IAAE;AAAc,QAAGJ,KAAE;AAAC,YAAK,CAACN,KAAEI,GAAC,IAAE+B,IAAG9B,KAAEC,KAAEH,KAAE,MAAK,IAAI;AAAE,UAAGH,IAAE,QAAM;AAAA,IAAE;AAAC,QAAG,sBAAoBiB,KAAE;AAAC,YAAK,EAAC,gBAAed,IAAC,IAAEH;AAAE,UAAGG,IAAE,YAAUH,OAAKG,KAAE;AAAC,cAAK,EAAC,QAAOA,IAAC,IAAEH;AAAE,YAAG,wBAAqBG,OAAA,gBAAAA,IAAG,SAAMA,IAAE,cAAa;AAAC,gBAAK,EAAC,cAAaH,IAAC,IAAEG;AAAE,qBAAUA,OAAKH,IAAE,KAAG,qBAAmBG,IAAE,KAAK,QAAM;AAAA,QAAE;AAAA,MAAC;AAAA,IAAC,OAAK;AAAC,YAAK,EAAC,0BAAyBA,IAAC,IAAEH;AAAE,UAAGG,IAAE,QAAM;AAAA,IAAE;AAAA,EAAC;AAAC,QAAMU,MAAEH,KAAES,KAAE,KAAK,IAAIN,IAAE,KAAK,GAAEC,KAAE,KAAK,IAAID,IAAE,KAAK;AAAE,MAAG,MAAIM,MAAG,MAAIL,GAAE,QAAM;AAAG,QAAMM,MAAE,CAAC,YAAW,WAAU,SAAS,GAAEF,KAAEb,IAAE,OAAQ,CAAAF,QAAGA,IAAE,kBAAgBH,IAAE,iBAAe,CAACoB,IAAE,SAASjB,IAAE,YAAY,CAAE,GAAEkB,KAAE,SAAQrB,OAAG,YAAU,OAAOA,IAAE,MAAIA,IAAE,MAAI,QAAO,CAACsB,KAAEC,EAAC,IAAEY,IAAG9B,KAAEQ,IAAE,eAAcV,KAAE,MAAK,IAAI,GAAEsB,KAAE,EAAE,KAAK,UAAUzB,GAAC,IAAEuB,EAAC,EAAE,SAAS;AAAE,MAAImB,IAAEC,IAAEC,KAAE;AAAK,MAAG,aAAWlC,IAAE,UAAS;AAAC,UAAMV,MAAEA,GAAE,EAAC,GAAEG,MAAE,KAAK,IAAI,KAAK,MAAMH,MAAEmB,EAAC,GAAE,CAAC,GAAEf,MAAE,KAAK,IAAI,KAAK,MAAMJ,MAAEc,EAAC,GAAE,CAAC;AAAE,IAAA4B,KAAE5B,KAAEV,KAAEwC,KAAE,CAAA5C,QAAGA,MAAEA,MAAEI,MAAE;AAAE,IAAAuC,KAAExC,MAAEgB,KAAEuB;AAAA,EAAC,MAAM,CAAAhC,IAAE,gBAAcgC,KAAE,IAAE5B,IAAE8B,KAAE,CAAA5C,QAAGA,MAAE,IAAEA,MAAE,GAAE2C,KAAExB,KAAEL,KAAE,QAAK4B,KAAE5B,IAAE8B,KAAE,MAAKD,KAAExB,KAAEL;AAAG,QAAM+B,KAAE/B,GAAEd,GAAC;AAAE,SAAOS,IAAE,KAAK,EAAC,MAAK,QAAO,cAAagB,IAAE,cAAaH,MAAEiB,IAAGd,IAAErB,KAAEc,IAAEZ,GAAC,IAAEmB,IAAE,KAAIzB,KAAE,mBAAkBkB,IAAE,aAAY,CAAC,CAAClB,IAAE,aAAY,SAAQG,KAAE,OAAMiC,IAAGvB,IAAE,eAAcT,KAAE,aAAYE,KAAEuC,IAAER,GAAE,GAAE,QAAOD,IAAGvB,IAAE,eAAcT,KAAE,SAAQE,KAAEoC,IAAEE,EAAC,GAAE,QAAOD,IAAE,OAAMP,IAAGvB,IAAE,eAAcT,KAAE,aAAYE,KAAEO,IAAE,SAAS,GAAE,SAAQuB,IAAGvB,IAAE,eAAcT,KAAE,WAAUE,KAAE,EAAEO,IAAE,OAAO,CAAC,GAAE,SAAQuB,IAAGvB,IAAE,eAAcT,KAAE,WAAUE,KAAE,EAAEO,IAAE,OAAO,CAAC,GAAE,KAAIQ,IAAE,mBAAkB,aAAWX,IAAE,UAAS,iBAAgB,CAACW,IAAE,+BAA8B,KAAE,CAAC,GAAE;AAAE;AAAC,SAASY,GAAEjC,KAAEG,KAAEC,KAAEC,KAAEC,KAAEI,KAAEC,KAAEM,KAAEJ,KAAE;AAAC,QAAMM,KAAEnB,IAAE,eAAcc,KAAE,EAAEd,IAAE,IAAI;AAAE,MAAIoB,MAAE,EAAEpB,IAAE,QAAO,CAAC;AAAE,QAAMkB,KAAE,EAAElB,IAAE,QAAQ,GAAEqB,KAAE,EAAErB,IAAE,OAAO,GAAEsB,MAAE,EAAEtB,IAAE,OAAO,GAAEuB,KAAET,GAAEd,GAAC,GAAEyB,KAAE,EAAE,GAAGzB,IAAE,GAAG,GAAG,KAAK,UAAUA,IAAE,kBAAkB,CAAC,GAAG,KAAK,UAAUA,IAAE,wBAAwB,CAAC,EAAE,EAAE,SAAS,GAAE0C,KAAEI,IAAG9C,IAAE,iBAAgBK,KAAED,KAAEE,GAAC,GAAEqC,KAAEI,IAAG/C,IAAE,0BAAyBK,KAAED,KAAEE,GAAC,GAAE,CAACsC,IAAEC,EAAC,IAAEV,IAAG9B,KAAEc,IAAEhB,KAAEuC,IAAEC,EAAC,GAAEK,KAAE,EAAE,KAAK,UAAUhD,GAAC,IAAE6C,EAAC,EAAE,SAAS,GAAEI,KAAEjD,IAAE,eAAa,EAAC,GAAE,GAAE,GAAE,EAAC;AAAE,MAAG,WAAUA,OAAG,YAAU,OAAOA,IAAE,OAAM;AAAC,UAAMG,MAAEH,IAAE;AAAM,QAAII,MAAE;AAAE,UAAMC,MAAEK,IAAE,YAAYV,IAAE,GAAG;AAAE,MAAEK,GAAC,MAAID,MAAEC,IAAE,QAAMA,IAAE,SAAQe,OAAGhB,OAAGU,KAAEX;AAAA,EAAE;AAAC,WAAS+C,GAAElD,KAAEG,KAAE;AAAC,WAAO,EAAEwC,EAAC,IAAExC,GAAEwC,IAAE3C,KAAEG,GAAC,IAAE;AAAA,EAAI;AAAC,QAAMgD,KAAEnD,IAAE,4BAA0B,SAAKA,IAAE,yBAAyB,qBAAmB,CAACA,KAAEG,KAAEC,KAAEC,QAAI;AAAC,UAAMC,MAAED,IAAEA,OAAG,CAAC,GAAEI,MAAEyC,GAAElD,KAAEG,GAAC;AAAE,WAAOsB,KAAE,kBAAkBnB,GAAC,SAAY,KAAK,UAAUG,GAAC,CAAC;AAAA,EAAG,IAAEmC,KAAE,CAAC5C,KAAEG,QAAI;AAAC,UAAMC,MAAE8C,GAAElD,KAAEG,GAAC;AAAE,WAAOsB,KAAE,QAAQ,KAAK,UAAUrB,GAAC,CAAC;AAAA,EAAG,IAAEqB;AAAE,EAAAd,IAAE,KAAK,EAAC,MAAK,UAAS,cAAaqC,IAAE,cAAaG,IAAE,KAAInD,KAAE,mBAAkB,MAAK,aAAY,CAAC,CAACA,IAAE,aAAY,SAAQG,KAAE,4BAA2B,OAAG,WAAUc,KAAE,MAAKmB,IAAGjB,IAAEf,KAAE,QAAOE,KAAEQ,EAAC,GAAE,QAAOsB,IAAGjB,IAAEf,KAAE,UAASE,KAAEc,GAAC,GAAE,UAASgB,IAAGjB,IAAEf,KAAE,YAAWE,KAAEY,EAAC,GAAE,SAAQkB,IAAGjB,IAAEf,KAAE,WAAUE,KAAEe,EAAC,GAAE,SAAQe,IAAGjB,IAAEf,KAAE,WAAUE,KAAEgB,GAAC,GAAE,OAAMc,IAAGjB,IAAEf,KAAE,aAAYE,KAAEiB,IAAEc,GAAE,GAAE,aAAY,EAAC,GAAEY,GAAE,GAAE,GAAE,CAACA,GAAE,EAAC,GAAE,uBAAsB,eAAajD,IAAE,kBAAiB,cAAa,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,cAAa,GAAE,aAAY,GAAE,iBAAgB,CAAC,CAACA,IAAE,iBAAgB,eAAca,KAAE,WAAU,GAAE,iBAAgB6B,IAAE,KAAI1C,IAAE,KAAI,0BAAyB2C,GAAC,CAAC;AAAC;AAAC,SAAST,GAAElC,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAEC,KAAEC,KAAEC,KAAEK,KAAE;AAAC,QAAMJ,MAAEb,IAAE;AAAe,MAAG,CAACa,IAAE;AAAO,MAAIM,KAAE;AAAE,MAAGnB,IAAE,4BAA2B;AAAC,UAAMG,MAAEH,IAAE;AAAM,IAAAG,QAAIgB,KAAEhB,IAAE,OAAKA,IAAE;AAAA,EAAK;AAAC,QAAMW,KAAEgC,IAAG9C,IAAE,iBAAgBK,KAAED,KAAEE,GAAC;AAAE,aAAUc,OAAKP,IAAE,KAAGO,KAAE;AAAC,UAAMP,MAAEO,IAAE;AAAO,QAAG,CAACP,IAAE;AAAS,YAAOA,IAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAiB,KAAI;AAAA,MAAgB,KAAI;AAAmB,QAAAuC,IAAGpD,KAAEG,KAAEW,IAAE,MAAKM,KAAEf,KAAED,KAAEE,KAAEG,KAAEC,KAAEC,KAAEC,KAAEO,IAAE,CAAC,CAACF,GAAC;AAAE;AAAA,MAAM,KAAI;AAAgB,QAAAoC,GAAErD,KAAEG,KAAEW,IAAEM,KAAEhB,KAAEC,KAAEC,KAAEG,KAAEE,KAAEC,KAAEO,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAASkC,GAAErD,KAAEI,KAAEC,KAAEC,KAAEG,KAAEC,KAAEC,KAAEM,KAAEJ,KAAEM,IAAEC,KAAE;AAAC,QAAMF,KAAE,CAAC;AAAE,KAAE,wBAAwBZ,KAAEI,KAAEQ,EAAC;AAAE,QAAMG,KAAEf,IAAE;AAAS,MAAG,EAAE,OAAMe,OAAI,EAAE,OAAMA,IAAG;AAAO,QAAMC,MAAEhB,IAAE,QAAOiB,KAAEqB,GAAEtB,GAAC,GAAEG,KAAEH,GAAEA,IAAE,aAAa,GAAEkB,KAAE5B,GAAEU,IAAE,cAAc;AAAE,EAAAA,IAAE,OAAK,EAAC,QAAOkB,IAAE,YAAWjB,IAAE,GAAGE,GAAC;AAAE,QAAM6B,KAAEtD,IAAE,OAAMuD,KAAElC,GAAE,IAAE,OAAIiC,GAAE,OAAKA,GAAE,OAAME,KAAEnC,GAAE,IAAE,OAAIiC,GAAE,OAAKA,GAAE,OAAMG,KAAEzD,IAAE,OAAKoB,KAAEsC,KAAE1D,IAAE,eAAcD,KAAE,EAAEuB,IAAE,MAAM,IAAEmC,IAAExD,KAAE,EAAEqB,IAAE,KAAK,GAAEpB,KAAE,EAAEF,IAAE,OAAO,KAAG,EAAEsB,IAAE,OAAO,IAAEiC,MAAGE,IAAElD,KAAE,EAAEP,IAAE,OAAO,KAAG,EAAEsB,IAAE,OAAO,IAAEkC,MAAGC,IAAEjD,KAAEG,GAAEqC,GAAE1B,GAAC,CAAC;AAAE,MAAIN,KAAEL,GAAEgD,GAAErC,GAAC,CAAC,GAAEI,KAAE,EAAEJ,GAAC,KAAG;AAAE,EAAAI,OAAIV,KAAEL,GAAEqC,GAAE1B,IAAE,UAAU,CAAC,GAAEA,IAAE,aAAWI,KAAEJ,IAAE,WAASmC;AAAI,MAAIG,KAAE,MAAKjC,KAAE,MAAKC,KAAE;AAAE,MAAGN,IAAE,WAAS,2BAAyBA,IAAE,QAAQ,MAAK;AAAC,UAAMtB,MAAEsB,IAAE;AAAQ,QAAGtB,IAAE,kBAAiB;AAAC,YAAMG,MAAEH,IAAE,iBAAiB;AAAa,UAAGG,IAAE,YAAUH,OAAKG,IAAE,oBAAiBH,IAAE,OAAK4D,KAAEjD,GAAEX,IAAE,KAAK,IAAE,qBAAmBA,IAAE,SAAO2B,KAAEhB,GAAEX,IAAE,KAAK,GAAE4B,KAAE,EAAE5B,IAAE,KAAK;AAAA,IAAE;AAAA,EAAC;AAAC,QAAK,CAAC6B,IAAEC,EAAC,IAAEK,IAAGzB,KAAEgD,IAAEtD,KAAEC,KAAE,IAAI,GAAE0B,KAAE,KAAK,UAAU/B,IAAE,OAAO,IAAE,OAAOA,IAAE,WAAW,EAAE,SAAS,IAAE,KAAK,UAAUA,IAAE,WAAW,IAAEA,IAAE,mBAAiB,KAAK,UAAUA,IAAE,eAAe,IAAEA,IAAE,KAAK,SAAS,GAAEgC,KAAE,EAAE,KAAK,UAAU1B,GAAC,IAAEyB,KAAED,EAAC,EAAE,SAAS;AAAE,MAAIG,KAAEG,IAAG9B,IAAE,eAAcG,KAAE,cAAaE,KAAEL,IAAE,cAAY,IAAGM,IAAEU,IAAE,QAAQ;AAAE,MAAG,QAAMW,GAAE;AAAO,QAAK,EAAC,eAAcC,GAAC,IAAEZ,KAAE+B,KAAEb,MAAGN,KAAE,MAAIA,GAAE,YAAY,IAAE,aAAYkB,MAAGC;AAAE,cAAU,OAAOpB,MAAGA,GAAE,SAAS,GAAG,KAAGX,IAAE,aAAWW,KAAEpB,GAAES,IAAE,UAASW,IAAEX,IAAE,QAAQ,IAAGL,IAAE,KAAK,EAAC,MAAK,QAAO,cAAae,IAAE,cAAaH,MAAG,cAAY,OAAOI,MAAGA,GAAE,MAAM,WAAW,IAAE,CAACjC,KAAEG,KAAEC,QAAIgD,MAAG,MAAIjD,GAAE8B,IAAEjC,KAAEG,KAAEC,GAAC,IAAEgD,MAAG,MAAI,EAAEnB,EAAC,GAAE,KAAIX,KAAE,mBAAkB,MAAK,aAAY,CAAC,CAACtB,IAAE,aAAY,SAAQI,KAAE,WAAUS,KAAE,aAAY,EAAC,GAAEb,IAAE,cAAYA,IAAE,YAAY,IAAE,GAAE,GAAEA,IAAE,cAAYA,IAAE,YAAY,IAAE,EAAC,GAAE,uBAAsB,eAAaA,IAAE,kBAAiB,UAASqD,IAAE,YAAW9B,IAAE,QAAOa,IAAGsB,IAAEjD,KAAE,UAASE,KAAEc,GAAE,MAAM,GAAE,OAAMW,IAAGsB,IAAEjD,KAAE,QAAOE,KAAEc,GAAE,KAAK,GAAE,MAAKW,IAAGsB,IAAEjD,KAAE,QAAOE,KAAEZ,EAAC,GAAE,OAAMqC,IAAGsB,IAAEjD,KAAE,YAAWE,KAAEV,EAAC,GAAE,SAAQmC,IAAGsB,IAAEjD,KAAE,WAAUE,KAAET,EAAC,GAAE,SAAQkC,IAAGsB,IAAEjD,KAAE,WAAUE,KAAEJ,EAAC,GAAE,qBAAoB,EAAEe,IAAE,mBAAmB,GAAE,mBAAkB,EAAEA,IAAE,iBAAiB,GAAE,MAAKW,IAAE,OAAMzB,IAAE,cAAaQ,IAAE,aAAYU,IAAE,iBAAgBkC,IAAE,iBAAgBjC,IAAE,iBAAgBC,IAAE,eAAcT,IAAE,WAAU,GAAE,iBAAgBd,IAAC,CAAC;AAAC;AAAC,SAAS+C,IAAGpD,KAAEG,KAAEC,KAAEC,KAAEC,KAAEI,KAAEC,KAAEM,KAAEJ,KAAEM,IAAEL,IAAEM,KAAEF,IAAEG,IAAE;AAAC,QAAMC,MAAEhB,IAAE,QAAOkC,KAAElB,IAAE;AAAa,MAAG,CAACkB,GAAE;AAAO,MAAGnB,GAAE,QAAO,KAAKN,IAAGf,KAAEG,KAAEC,KAAEC,KAAEC,KAAEK,KAAED,KAAEO,KAAEJ,KAAEM,IAAEL,IAAEM,KAAEF,EAAC;AAAE,MAAIyB,KAAEH,GAAE;AAAO,MAAGqB,IAAGrB,EAAC,EAAE,QAAO,KAAKsB,IAAG9D,KAAEG,KAAEC,KAAEC,KAAEC,KAAEkC,IAAE9B,KAAEC,KAAEM,KAAEJ,KAAEC,IAAEM,KAAEF,EAAC;AAAE,QAAM0B,KAAE3B,GAAE,aAAaK,IAAE,SAAQhB,IAAE,UAASa,GAAE,cAAc;AAAE,MAAGyB,GAAE,QAAKD,QAAK;AAAC,UAAMtB,KAAEmB,GAAEG,EAAC;AAAE,QAAGtB,MAAG,UAAKA,GAAE,OAAO,SAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAe,KAAI,kBAAiB;AAAC,cAAMC,MAAEL,GAAE,aAAaI,GAAE,SAAQuB,IAAEzB,GAAE,cAAc,GAAEqB,KAAEpC,GAAEkB,GAAC;AAAE,YAAG,CAACkB,GAAE;AAAS,cAAMG,KAAE,eAAa3C,IAAE,kBAAiB,CAACkD,IAAEC,IAAEQ,EAAC,IAAE1C,GAAEuB,IAAExC,IAAE,OAAMA,IAAE,MAAKA,IAAE,aAAY2C,EAAC,GAAEoB,KAAE,mBAAiB1C,GAAE,MAAK2C,KAAE,EAAC,MAAK,OAAM,MAAK1C,KAAE,QAAOyC,GAAC,GAAER,KAAEvD,IAAE,eAAcwD,KAAE,EAAExD,IAAE,IAAI,KAAG,IAAGyD,KAAE,EAAEzD,IAAE,QAAQ,GAAE0D,KAAE,EAAE1D,IAAE,OAAO,GAAED,KAAE,EAAEC,IAAE,OAAO,GAAEC,KAAEoB,GAAE,MAAKnB,KAAEmB,GAAE,eAAcd,KAAEI,GAAEoD,KAAEf,GAAE3B,EAAC,IAAEsC,GAAEtC,EAAC,CAAC,GAAEb,KAAEuD,KAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,IAAEpD,GAAEgD,GAAEtC,EAAC,CAAC,GAAEL,KAAE,EAAEK,EAAC,KAAG;AAAE,YAAG,CAAC0C,MAAG,CAAC/C,GAAE;AAAM,YAAIU,KAAE,OAAGkC,KAAE;AAAG,mBAAU5D,OAAKU,IAAE,CAAAV,IAAE,kBAAgBE,MAAGF,IAAE,kBAAgBuD,OAAI,WAASvD,IAAE,QAAM4D,MAAG,IAAI5D,IAAE,aAAa,IAAIA,IAAE,YAAY,IAAI,KAAK,UAAUA,IAAE,KAAK,CAAC,KAAGA,IAAE,wBAAsB0B,KAAE;AAAK,SAAC,EAAEvB,GAAC,KAAG,cAAY,OAAOA,OAAG,EAAEC,GAAC,KAAG,cAAY,OAAOA,SAAKsB,KAAE;AAAI,cAAMC,KAAE,KAAK,UAAU,EAAC,GAAG3B,KAAE,gBAAe,KAAI,CAAC,GAAE4B,KAAE,EAAE,KAAK,UAAUoC,EAAC,IAAE/D,EAAC,EAAE,SAAS,GAAE4B,KAAE,EAAC,MAAK,UAAS,cAAa,EAAE,KAAK,UAAUvB,GAAC,IAAE,KAAK,UAAUe,EAAC,IAAEM,KAAEiC,EAAC,EAAE,SAAS,GAAE,cAAalC,KAAE,MAAIE,KAAEA,IAAE,KAAIoC,IAAE,mBAAkB,MAAK,aAAY,CAAC,CAAChE,IAAE,aAAY,SAAQG,KAAE,4BAA2B,CAAC,CAACH,IAAE,4BAA2B,WAAUc,IAAE,aAAY,EAAC,GAAEqC,IAAE,GAAEQ,GAAC,GAAE,uBAAsBhB,IAAE,MAAKP,IAAGpC,IAAE,eAAcW,KAAE,QAAOM,KAAEuC,EAAC,GAAE,UAASpB,IAAGpC,IAAE,eAAcW,KAAE,YAAWM,KAAEwC,EAAC,GAAE,SAAQrB,IAAGpC,IAAE,eAAcW,KAAE,WAAUM,KAAEyC,EAAC,GAAE,SAAQtB,IAAGpC,IAAE,eAAcW,KAAE,WAAUM,KAAElB,EAAC,GAAE,QAAO,GAAE,aAAYmB,IAAE,iBAAgB,CAAC,CAAClB,IAAE,iBAAgB,eAAcoB,KAAE,WAAU8B,IAAE,OAAMd,IAAGlC,IAAES,KAAE,SAAQM,KAAEV,IAAE8B,GAAE,GAAE,cAAaD,IAAGlC,IAAES,KAAE,SAAQM,KAAET,IAAE6B,GAAE,GAAE,cAAaD,IAAGlC,IAAES,KAAE,SAAQM,KAAED,EAAC,GAAE,iBAAgBZ,KAAE,0BAAyBC,KAAE,MAAKJ,GAAC;AAAE,QAAAY,IAAE,KAAKgB,EAAC;AAAE;AAAA,MAAK;AAAA,MAAC;AAAQ,QAAAd,IAAGf,KAAEG,KAAEC,KAAEC,KAAEC,KAAEK,KAAED,KAAEO,KAAEJ,KAAEM,IAAEL,IAAEM,KAAEF,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS4C,IAAG9D,KAAEG,KAAEC,KAAEC,KAAEC,KAAEI,KAAEC,KAAEM,KAAEJ,KAAEM,IAAEL,IAAEM,KAAEF,IAAE;AAAC,QAAMG,KAAEf,IAAE,UAASgB,MAAEZ,IAAE,CAAC,GAAE8B,KAAE9B,IAAE,CAAC,GAAEiC,KAAEvC,GAAEiB,EAAC;AAAE,MAAG,CAACsB,GAAE;AAAO,QAAMC,KAAE,eAAa5C,IAAE,kBAAiB,CAACkD,IAAEC,IAAEQ,EAAC,IAAE1C,GAAE0B,IAAE3C,IAAE,OAAMA,IAAE,MAAKA,IAAE,aAAY4C,EAAC,GAAEmB,KAAE,EAAC,MAAK,OAAM,MAAK1C,IAAE,QAAO,KAAE,GAAE2C,KAAEhE,IAAE,eAAcsD,KAAE,EAAEtD,IAAE,IAAI,GAAEuD,KAAE,EAAEvD,IAAE,QAAQ,GAAEwD,KAAE,EAAExD,IAAE,OAAO,GAAEyD,KAAE,EAAEzD,IAAE,OAAO,GAAE0D,KAAElB,GAAE,MAAKzC,KAAEyC,GAAE,eAAcvC,KAAEqB,IAAE,eAAcpB,KAAES,GAAEqC,GAAER,EAAC,CAAC,GAAEjC,KAAEI,GAAEgD,GAAErC,GAAC,CAAC,GAAEd,KAAE,EAAEc,GAAC,KAAG;AAAE,MAAIN,KAAE,OAAGU,KAAE;AAAG,aAAUjB,OAAKE,IAAE,CAAAF,IAAE,kBAAgBV,MAAGU,IAAE,kBAAgBR,MAAGQ,IAAE,kBAAgBuD,OAAI,WAASvD,IAAE,QAAMiB,MAAG,IAAIjB,IAAE,aAAa,IAAIA,IAAE,YAAY,IAAI,KAAK,UAAUA,IAAE,KAAK,CAAC,KAAGA,IAAE,wBAAsBO,KAAE;AAAK,IAAEZ,GAAC,KAAG,cAAY,OAAOA,QAAIY,KAAE;AAAI,QAAM4C,KAAE,KAAK,UAAU,EAAC,GAAG5D,KAAE,gBAAe,KAAI,CAAC,GAAE2B,KAAE,EAAE,KAAK,UAAUoC,EAAC,IAAEL,EAAC,EAAE,SAAS,GAAE9B,KAAE,EAAC,MAAK,UAAS,cAAa,EAAE,KAAK,UAAUtB,GAAC,IAAE,KAAK,UAAUkC,EAAC,IAAE,KAAK,UAAUlB,GAAC,IAAEsC,KAAElC,EAAC,EAAE,SAAS,GAAE,cAAaV,KAAE,MAAIW,KAAEA,IAAE,KAAIoC,IAAE,mBAAkB,MAAK,aAAY,CAAC,CAAC/D,IAAE,aAAY,SAAQG,KAAE,4BAA2B,CAAC,CAACH,IAAE,4BAA2B,WAAUc,IAAE,aAAY,EAAC,GAAEqC,IAAE,GAAEQ,GAAC,GAAE,uBAAsBf,IAAE,MAAKR,IAAGpC,IAAE,eAAciB,KAAE,QAAOJ,KAAEyC,EAAC,GAAE,UAASlB,IAAGpC,IAAE,eAAciB,KAAE,YAAWJ,KAAE0C,EAAC,GAAE,SAAQnB,IAAGpC,IAAE,eAAciB,KAAE,WAAUJ,KAAE2C,EAAC,GAAE,SAAQpB,IAAGpC,IAAE,eAAciB,KAAE,WAAUJ,KAAE4C,EAAC,GAAE,QAAO,GAAE,aAAYvC,IAAE,iBAAgB,CAAC,CAAClB,IAAE,iBAAgB,eAAcoB,KAAE,WAAU8B,IAAE,OAAMd,IAAGrC,IAAEkB,KAAE,SAAQJ,KAAEX,IAAEmC,GAAE,GAAE,cAAaD,IAAGnC,IAAEgB,KAAE,SAAQJ,KAAEN,IAAE8B,GAAE,GAAE,cAAaD,IAAGnC,IAAEgB,KAAE,SAAQJ,KAAEL,EAAC,GAAE,iBAAgBJ,KAAE,MAAKsD,IAAE,0BAAyBrD,IAAC;AAAE,EAAAc,GAAE,KAAKS,EAAC;AAAC;AAAC,SAASb,IAAGf,KAAEG,KAAEC,KAAEC,KAAEC,KAAEK,KAAEM,KAAEJ,KAAEC,IAAEM,KAAEF,IAAEG,IAAEC,KAAE;AAAC,QAAMC,KAAE0C,IAAGjE,KAAEM,GAAC,GAAEmB,KAAE,CAAC,YAAW,WAAU,SAAS,GAAEiB,KAAEzB,IAAE,OAAQ,CAAAd,QAAGA,IAAE,kBAAgBH,IAAE,iBAAe,CAACyB,GAAE,SAAStB,IAAE,YAAY,CAAE;AAAE,MAAIqC,KAAE;AAAG,aAAU/B,OAAKQ,IAAE,YAASR,IAAE,UAAQ+B,MAAG,IAAI/B,IAAE,aAAa,IAAIA,IAAE,YAAY,IAAI,KAAK,UAAUA,IAAE,KAAK,CAAC;AAAI,QAAK,CAACkC,IAAEC,IAAEC,EAAC,IAAE9B,IAAE,iBAAiBQ,IAAEH,GAAC,GAAE4B,KAAEhD,IAAE,eAAciD,KAAE,EAAEjD,IAAE,QAAQ,GAAEkD,KAAE,EAAElD,IAAE,OAAO,GAAEmD,KAAE,EAAEnD,IAAE,OAAO,GAAE2D,KAAE,EAAE,KAAK,UAAUpC,EAAC,IAAEiB,EAAC,EAAE,SAAS,GAAEuB,KAAE,EAAC,MAAK,UAAS,cAAaJ,IAAE,cAAajB,GAAE,SAAO,KAAG,EAAEvC,GAAC,KAAG,cAAY,OAAOA,MAAEoC,IAAGoB,IAAEhD,KAAE+B,IAAE7B,GAAC,IAAE8C,IAAE,KAAIpC,IAAE,mBAAkBmB,IAAE,aAAY,CAAC,CAAC1C,IAAE,aAAY,SAAQG,KAAE,4BAA2B,CAAC,CAACH,IAAE,4BAA2B,WAAUkB,IAAE,aAAY,EAAC,GAAEyB,IAAE,GAAEC,GAAC,GAAE,uBAAsB,OAAG,MAAK,EAAE5C,IAAE,IAAI,GAAE,UAASoC,IAAGY,IAAErC,KAAE,YAAWE,KAAEoC,EAAC,GAAE,SAAQb,IAAGY,IAAErC,KAAE,WAAUE,KAAEqC,EAAC,GAAE,SAAQd,IAAGY,IAAErC,KAAE,WAAUE,KAAEsC,EAAC,GAAE,OAAM,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,EAAC,GAAE,cAAa,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,cAAa,GAAE,QAAO,GAAE,aAAY7B,KAAE,iBAAgB,CAAC,CAACtB,IAAE,iBAAgB,eAAcqB,IAAE,WAAUwB,KAAEzB,GAAEpB,IAAE,IAAI,GAAE,iBAAgBI,KAAE,0BAAyBC,KAAE,uBAAsB,KAAE;AAAE,EAAAS,GAAE,KAAKiD,EAAC;AAAC;AAAC,SAASE,IAAGjE,KAAEG,KAAE;AAAC,SAAM,EAAC,MAAKH,IAAE,MAAK,QAAO,MAAG,MAAKA,IAAE,MAAK,aAAYA,IAAE,aAAY,eAAcA,IAAE,eAAc,aAAYA,IAAE,aAAY,kBAAiBA,IAAE,kBAAiB,SAAQ,GAAE,SAAQ,GAAE,iBAAgBA,IAAE,iBAAgB,UAAS,GAAE,MAAKA,IAAE,MAAK,iBAAgBA,IAAE,iBAAgB,SAAQA,IAAE,SAAQ,OAAMA,IAAE,OAAM,gBAAe,CAACG,GAAC,GAAE,4BAA2BH,IAAE,4BAA2B,cAAaA,IAAE,cAAa,cAAaA,IAAE,aAAY;AAAC;AAAC,SAASyC,IAAGzC,KAAE;AAAC,MAAGA,OAAG,MAAIA,IAAE,QAAQ,QAAQ,GAAE;AAAC,UAAMG,MAAE,SAASH,IAAE,OAAO,CAAC,GAAE,EAAE;AAAE,QAAG,CAAC,MAAMG,GAAC,EAAE,QAAOA;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAASkC,IAAGlC,KAAE;AAAC,MAAG,CAACA,OAAG,MAAIA,IAAE,OAAO,QAAO;AAAK,QAAMC,MAAE,IAAIM,GAAEP,GAAC,EAAE,OAAO;AAAE,SAAM,EAAC,GAAEC,IAAE,CAAC,GAAE,GAAEA,IAAE,CAAC,GAAE,GAAEA,IAAE,CAAC,GAAE,GAAEA,IAAE,CAAC,EAAC;AAAC;AAAC,SAASgC,IAAGpC,KAAEG,KAAEC,KAAEC,KAAEC,KAAEG,KAAEC,KAAE;AAAC,MAAG,QAAMV,IAAE,QAAOM;AAAE,QAAMK,MAAER,IAAEH,GAAC;AAAE,MAAGW,KAAE;AAAC,UAAMX,MAAEW,IAAEP,GAAC;AAAE,QAAG,YAAU,OAAOJ,OAAG,YAAU,OAAOA,OAAGA,eAAa,MAAM,QAAOS,MAAEA,IAAE,KAAK,MAAKT,KAAEU,GAAC,IAAEV;AAAE,QAAG,QAAMA,OAAGA,eAAaoB,OAAGf,OAAA,gBAAAA,IAAG,cAAa,QAAM,CAACF,KAAEC,KAAEO,QAAI;AAAC,UAAIC,MAAER,GAAEJ,KAAEG,KAAE,EAAC,OAAMQ,IAAC,GAAEN,IAAE,cAAaD,GAAC;AAAE,aAAO,SAAOQ,OAAGH,QAAIG,MAAEH,IAAE,KAAK,MAAKG,KAAEF,GAAC,IAAG,SAAOE,MAAEA,MAAEN;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS4D,IAAGlE,KAAE;AAAC,SAAOA,MAAEA,IAAE,OAAO,CAAC,EAAE,YAAY,IAAEA,IAAE,OAAO,CAAC,IAAEA;AAAC;AAAC,SAASwB,IAAGxB,KAAEG,KAAEE,KAAEC,KAAE;AAAC,aAAUF,OAAKD,KAAE;AAAC,QAAGC,IAAE,wBAAqBE,OAAA,gBAAAA,IAAG,eAAa;AAAC,YAAMN,MAAEK,IAAED,IAAE,aAAa,KAAGC,IAAED,IAAE,aAAa,EAAEA,IAAE,YAAY;AAAE,MAAAJ,eAAaoB,OAAIhB,IAAE,KAAG,CAACD,KAAEC,KAAEC,QAAID,GAAEJ,KAAEG,KAAE,EAAC,OAAME,IAAC,GAAEC,IAAE,cAAaF,GAAC;AAAA,IAAE;AAAA,EAAC;AAAC,SAAM,CAACC,KAAEC,KAAEG,QAAI;AAAC,eAAUT,OAAKG,IAAE,CAAAH,IAAE,OAAKA,IAAE,QAAMA,IAAE,GAAGK,KAAEC,KAAEG,GAAC;AAAG,UAAMC,MAAE,CAAC;AAAE,aAAQC,OAAKX,KAAE;AAAC,YAAMA,MAAEW,OAAA,gBAAAA,IAAG;AAAc,UAAGX,KAAE;AAAC,YAAIK,MAAE;AAAG,mBAAUC,OAAKH,IAAE,KAAGG,IAAE,kBAAgBN,KAAE;AAAC,gBAAMA,MAAEkE,IAAG5D,IAAE,YAAY;AAAE,kBAAMA,IAAE,SAAOA,IAAE,UAAQK,IAAEX,GAAC,MAAIK,QAAIM,MAAE,EAAEA,GAAC,GAAEN,MAAE,OAAIM,IAAEX,GAAC,IAAEM,IAAE;AAAA,QAAM;AAAA,MAAC;AAAC,MAAAI,IAAE,KAAKC,GAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAC;AAAC,SAASoC,IAAG9C,KAAEG,KAAEE,KAAEC,KAAE;AAAC,QAAMG,MAAE,CAAC;AAAE,MAAG,GAAE,wBAAwBT,KAAEG,KAAEM,GAAC,GAAE,QAAMT,OAAG,MAAIS,IAAE,OAAO,QAAOT;AAAE,aAAUI,OAAKK,KAAE;AAAC,QAAGL,IAAE,wBAAqBE,OAAA,gBAAAA,IAAG,eAAa;AAAC,YAAMN,MAAEK,IAAED,IAAE,aAAa,KAAGC,IAAED,IAAE,aAAa,EAAEA,IAAE,YAAY;AAAE,MAAAJ,eAAaoB,OAAIhB,IAAE,KAAG,CAACD,KAAEC,KAAEC,QAAID,GAAEJ,KAAEG,KAAE,EAAC,OAAME,IAAC,GAAEC,IAAE,cAAaF,GAAC;AAAA,IAAE;AAAA,EAAC;AAAC,SAAM,CAACD,KAAEE,KAAEC,QAAI;AAAC,eAAUN,OAAKS,IAAE,CAAAT,IAAE,OAAKA,IAAE,QAAMA,IAAE,GAAGG,KAAEE,KAAEC,GAAC;AAAG,UAAMI,MAAE,EAAEV,GAAC,GAAEW,MAAEX,IAAE;AAAc,eAAUA,OAAKS,IAAE,KAAGT,IAAE,kBAAgBW,KAAE;AAAC,YAAMR,MAAE+D,IAAGlE,IAAE,YAAY;AAAE,cAAMA,IAAE,SAAOA,IAAE,UAAQU,IAAEP,GAAC,MAAIO,IAAEP,GAAC,IAAEH,IAAE;AAAA,IAAM;AAAC,WAAOU;AAAA,EAAC;AAAC;AAAC,SAASqC,IAAG/C,KAAEG,KAAEE,KAAEC,KAAE;AAAC,QAAMG,MAAE,CAAC;AAAE,MAAG,GAAE,wBAAwBT,KAAEG,KAAEM,GAAC,GAAE,QAAMT,OAAG,MAAIS,IAAE,OAAO,QAAOT;AAAE,aAAUI,OAAKK,KAAE;AAAC,QAAGL,IAAE,wBAAqBE,OAAA,gBAAAA,IAAG,eAAa;AAAC,YAAMN,MAAEK,IAAED,IAAE,aAAa,KAAGC,IAAED,IAAE,aAAa,EAAEA,IAAE,YAAY;AAAE,MAAAJ,eAAaoB,OAAIhB,IAAE,KAAG,CAACD,KAAEC,KAAEC,QAAID,GAAEJ,KAAEG,KAAE,EAAC,OAAME,IAAC,GAAEC,IAAE,cAAaF,GAAC;AAAA,IAAE;AAAA,EAAC;AAAC,SAAM,CAACD,KAAEE,KAAEC,QAAI;AAAC,eAAUN,OAAKS,IAAE,CAAAT,IAAE,OAAKA,IAAE,QAAMA,IAAE,GAAGG,KAAEE,KAAEC,GAAC;AAAG,UAAMI,MAAE,EAAEV,GAAC,GAAEW,MAAEX,IAAE;AAAc,eAAUA,OAAKS,IAAE,KAAGT,IAAE,kBAAgBW,KAAE;AAAC,YAAMR,MAAE+D,IAAGlE,IAAE,YAAY;AAAE,UAAG,QAAMA,IAAE,OAAM;AAAC,cAAMI,MAAEO,IAAEX,IAAE,OAAMA,IAAE,YAAY;AAAE,QAAAI,QAAIM,IAAEP,GAAC,MAAIO,IAAEP,GAAC,IAAEC;AAAA,MAAE;AAAA,IAAC;AAAC,WAAOM;AAAA,EAAC;AAAC;AAAC,SAAS6B,IAAGvC,KAAEG,KAAEC,KAAEC,KAAE;AAAC,aAAUC,OAAKF,KAAE;AAAC,QAAGE,IAAE,wBAAqBD,OAAA,gBAAAA,IAAG,eAAa;AAAC,YAAML,MAAEG,IAAEG,IAAE,aAAa,KAAGH,IAAEG,IAAE,aAAa,EAAEA,IAAE,YAAY;AAAE,MAAAN,eAAaoB,OAAId,IAAE,KAAG,CAACH,KAAEC,KAAEE,QAAIF,GAAEJ,KAAEG,KAAE,EAAC,OAAMG,IAAC,GAAED,IAAE,cAAaD,GAAC;AAAA,IAAE;AAAA,EAAC;AAAC,SAAM,CAACD,KAAEE,KAAEC,QAAI;AAAC,eAAUN,OAAKI,IAAE,CAAAJ,IAAE,OAAKA,IAAE,QAAMA,IAAE,GAAGG,KAAEE,KAAEC,GAAC;AAAG,WAAO,EAAEN,MAAE,GAAE,iBAAiBI,GAAC,CAAC,EAAE,SAAS;AAAA,EAAC;AAAC;AAAC,SAAS+D,IAAGnE,KAAEG,KAAE;AAAC,MAAG,CAACA,OAAG,MAAIA,IAAE,OAAO,QAAOH;AAAE,QAAMK,MAAE,EAAEL,GAAC;AAAE,SAAO,GAAE,eAAeK,KAAEF,GAAC,GAAEE;AAAC;AAAC,SAAS8B,IAAGnC,KAAEG,KAAEC,KAAEC,KAAEC,KAAE;AAAC,MAAII,MAAE,OAAGC,MAAE;AAAG,aAAUF,OAAKT,IAAE,CAAAS,IAAE,kBAAgBN,QAAI,WAASM,IAAE,QAAME,OAAG,IAAIF,IAAE,aAAa,IAAIA,IAAE,YAAY,IAAI,KAAK,UAAUA,IAAE,KAAK,CAAC,KAAGA,IAAE,wBAAsBC,MAAE;AAAK,SAAO,EAAEN,GAAC,KAAG,cAAY,OAAOA,QAAIM,MAAE,OAAI,EAAEL,GAAC,KAAG,cAAY,OAAOA,QAAIK,MAAE,OAAI,EAAEJ,GAAC,KAAG,cAAY,OAAOA,QAAII,MAAE,OAAI,CAACA,KAAEC,GAAC;AAAC;AAAC,IAAMkD,MAAG,CAAA7D,QAAGA,OAAG,MAAIA,IAAE,UAAQA,IAAE,CAAC,EAAE,UAAQA,IAAE,CAAC,EAAE,UAAQ,qBAAmBA,IAAE,CAAC,EAAE,QAAM,mBAAiBA,IAAE,CAAC,EAAE,QAAM,CAACA,IAAE,CAAC,EAAE,WAAS,CAACA,IAAE,CAAC,EAAE;", "names": ["n", "i", "r", "e", "t", "i", "s", "r", "o", "h", "a", "l", "d", "c", "_", "g", "f", "u", "n", "p", "x", "m", "b", "z", "w", "v", "H", "y", "R", "n", "r", "s", "t", "l", "t", "s", "i", "r", "e", "n", "o", "c", "t", "a", "e", "n", "o", "r", "i", "h", "s", "l", "g", "t", "s", "a", "i", "n", "P", "l", "t", "s", "u", "f", "r", "c", "p", "m", "x", "g", "d", "C", "y", "I", "j", "G", "e", "b", "U", "o", "s", "t", "e", "i", "n", "r", "f", "u", "l", "a", "h", "c", "m", "_", "p", "j", "e", "s", "t", "n", "r", "h", "l", "o", "a", "u", "c", "i", "g", "l", "c", "u", "h", "t", "e", "r", "n", "o", "a", "U", "s", "i", "_", "f", "p", "m", "d", "w", "j", "l", "e", "i", "t", "s", "r", "f", "n", "m", "u", "o", "c", "s", "t", "i", "h", "e", "c", "r", "f", "j", "_", "u", "o", "n", "a", "p", "l", "d", "u", "e", "t", "i", "r", "s", "U", "n", "i", "n", "t", "s", "e", "h", "r", "a", "g", "I", "r", "t", "e", "s", "a", "i", "n", "U", "g", "h", "t", "i", "e", "s", "n", "m", "r", "u", "l", "s", "t", "i", "n", "h", "e", "U", "r", "n", "t", "s", "e", "i", "o", "r", "u", "f", "a", "t", "e", "s", "i", "n", "l", "r", "u", "f", "c", "s", "e", "t", "r", "i", "n", "f", "g", "u", "t", "n", "e", "r", "i", "c", "l", "m", "f", "s", "o", "c", "t", "s", "i", "r", "n", "l", "u", "m", "f", "a", "t", "e", "i", "s", "h", "o", "n", "g", "f", "l", "_", "c", "p", "u", "d", "w", "k", "a", "t", "e", "s", "i", "n", "r", "G", "g", "h", "n", "t", "e", "s", "i", "r", "G", "o", "a", "l", "h", "c", "_", "C", "a", "t", "i", "s", "e", "n", "r", "G", "g", "h", "n", "h", "_", "r", "o", "a", "t", "l", "s", "i", "e", "f", "c", "g", "u", "M", "p", "d", "m", "X", "A", "Y", "x", "y", "r", "l", "e", "t", "n", "i", "s", "o", "G", "a", "h", "C", "d", "c", "u", "n", "t", "s", "e", "i", "a", "r", "G", "y", "h", "l", "_", "c", "g", "I", "u", "o", "t", "e", "n", "l", "r", "s", "u", "a", "o", "c", "u", "t", "n", "s", "c", "l", "f", "e", "g", "d", "x", "M", "b", "w", "m", "y", "C", "k", "P", "T", "z", "p", "h", "j", "D", "S", "B", "Q", "R", "U", "v", "A", "i", "r", "o", "a", "N", "l", "t", "a", "e", "s", "n", "o", "f", "r", "u", "c", "z", "A", "p", "o", "h", "l", "u", "r", "s", "n", "a", "c", "g", "e", "t", "e", "n", "a", "t", "h", "i", "s", "i", "e", "s", "r", "t", "n", "h", "l", "g", "_", "p", "x", "w", "y", "M", "b", "B", "t", "s", "e", "i", "n", "h", "r", "a", "o", "c", "d", "f", "m", "L", "R", "T", "u", "j", "k", "A", "O", "S", "N", "O", "t", "e", "r", "i", "s", "o", "n", "a", "l", "h", "c", "m", "Y", "A", "g", "f", "u", "d", "p", "_", "y", "P", "w", "x", "S", "M", "b", "q", "Z", "C", "K", "k", "Y", "$", "U", "q", "W", "J", "K", "Q", "Z", "e", "t", "r", "ie", "oe", "a", "o", "i", "u", "s", "c", "n", "l", "f", "y", "m", "M", "S", "O", "h", "p", "d", "b", "g", "C", "F", "ne", "_a", "G", "t", "n", "e", "a", "r", "t", "o", "n", "l", "e", "s", "i", "u", "f", "a", "m", "c", "h", "y", "x", "M", "g", "p", "d", "b", "w", "F", "c", "f", "t", "s", "r", "p", "a", "u", "i", "e", "A", "x", "e", "o", "r", "t", "n", "Y", "e", "T", "$", "t", "i", "o", "r", "E", "j", "n", "l", "a", "s", "c", "p", "ie", "F", "f", "y", "m", "u", "h", "g", "d", "se", "S", "U", "W", "G", "B", "V", "q", "K", "Q", "_", "ue", "le", "ne", "_a", "me", "N", "re", "v", "O", "k", "C", "fe", "ce", "P", "M", "L", "I", "ee", "Z", "X", "A", "x", "J", "H", "w", "D", "ye", "te", "z", "R", "oe", "ae", "pe"]}