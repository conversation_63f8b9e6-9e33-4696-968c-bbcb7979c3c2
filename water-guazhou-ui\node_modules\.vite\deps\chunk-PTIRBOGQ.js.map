{"version": 3, "sources": ["../../@arcgis/core/geometry/support/zscale.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as n}from\"../../core/maybe.js\";import{getMetersPerVerticalUnitForSR as e}from\"../../core/unitUtils.js\";import{equals as t}from\"./spatialReferenceUtils.js\";function o(o,f,u){if(n(f)||n(u)||u.vcsWkid||t(f,u))return null;const a=e(f)/e(u);if(1===a)return null;switch(o){case\"point\":case\"esriGeometryPoint\":return n=>r(n,a);case\"polyline\":case\"esriGeometryPolyline\":return n=>s(n,a);case\"polygon\":case\"esriGeometryPolygon\":return n=>i(n,a);case\"multipoint\":case\"esriGeometryMultipoint\":return n=>c(n,a);case\"extent\":case\"esriGeometryExtent\":return n=>l(n,a);default:return null}}function r(n,e){n&&null!=n.z&&(n.z*=e)}function i(n,e){if(n)for(const t of n.rings)for(const n of t)n.length>2&&(n[2]*=e)}function s(n,e){if(n)for(const t of n.paths)for(const n of t)n.length>2&&(n[2]*=e)}function c(n,e){if(n)for(const t of n.points)t.length>2&&(t[2]*=e)}function l(n,e){n&&null!=n.zmin&&null!=n.zmax&&(n.zmin*=e,n.zmax*=e)}export{o as getGeometryZScaler};\n"], "mappings": ";;;;;;;;;AAIyK,SAAS,EAAEA,IAAE,GAAE,GAAE;AAAC,MAAG,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,EAAE,WAAS,EAAE,GAAE,CAAC,EAAE,QAAO;AAAK,QAAM,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC;AAAE,MAAG,MAAI,EAAE,QAAO;AAAK,UAAOA,IAAE;AAAA,IAAC,KAAI;AAAA,IAAQ,KAAI;AAAoB,aAAO,OAAG,EAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAW,KAAI;AAAuB,aAAO,OAAG,EAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAU,KAAI;AAAsB,aAAO,OAAG,EAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAa,KAAI;AAAyB,aAAO,OAAG,EAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAA,IAAS,KAAI;AAAqB,aAAO,OAAG,EAAE,GAAE,CAAC;AAAA,IAAE;AAAQ,aAAO;AAAA,EAAI;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,OAAG,QAAM,EAAE,MAAI,EAAE,KAAG;AAAE;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,EAAE,YAAUC,MAAK,EAAE,MAAM,YAAUC,MAAKD,GAAE,CAAAC,GAAE,SAAO,MAAIA,GAAE,CAAC,KAAG;AAAE;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,EAAE,YAAUD,MAAK,EAAE,MAAM,YAAUC,MAAKD,GAAE,CAAAC,GAAE,SAAO,MAAIA,GAAE,CAAC,KAAG;AAAE;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,EAAE,YAAUD,MAAK,EAAE,OAAO,CAAAA,GAAE,SAAO,MAAIA,GAAE,CAAC,KAAG;AAAE;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,OAAG,QAAM,EAAE,QAAM,QAAM,EAAE,SAAO,EAAE,QAAM,GAAE,EAAE,QAAM;AAAE;", "names": ["o", "t", "n"]}