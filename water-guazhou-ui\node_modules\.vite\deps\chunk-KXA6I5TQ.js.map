{"version": 3, "sources": ["../../@arcgis/core/geometry/support/quantizationUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as n,isNone as t}from\"../../core/maybe.js\";import{isPoint as e,isPolyline as r,isPolygon as u,isMultipoint as i,isExtent as o}from\"./jsonUtils.js\";function l(n){return n&&\"upperLeft\"===n.originPosition}const a=(n,t,e)=>[t,e],m=(n,t,e)=>[t,e,n[2]],c=(n,t,e)=>[t,e,n[2],n[3]];function s(t){if(!t)return null;return{originPosition:\"upper-left\"===t.originPosition?\"upperLeft\":\"lower-left\"===t.originPosition?\"lowerLeft\":t.originPosition,scale:t.tolerance?[t.tolerance,t.tolerance]:[1,1],translate:n(t.extent)?[t.extent.xmin,t.extent.ymax]:[0,0]}}function f(t,e){if(t===e||null==t&&null==e)return!0;if(null==t||null==e)return!1;let r,u,i,o,a,m;return l(t)?(r=t.translate[0],u=t.translate[1],i=t.scale[0]):(r=n(t.extent)?t.extent.xmin:0,u=n(t.extent)?t.extent.ymax:0,i=t.tolerance),l(e)?(o=e.translate[0],a=e.translate[1],m=e.scale[0]):(o=n(e.extent)?e.extent.xmin:0,a=n(e.extent)?e.extent.ymax:0,m=e.tolerance),r===o&&u===a&&i===m}function x({scale:n,translate:t},e){return Math.round((e-t[0])/n[0])}function h({scale:n,translate:t},e){return Math.round((t[1]-e)/n[1])}function I(n,t,e){const r=[];let u,i,o,l;for(let a=0;a<e.length;a++){const m=e[a];a>0?(o=x(n,m[0]),l=h(n,m[1]),o===u&&l===i||(r.push(t(m,o-u,l-i)),u=o,i=l)):(u=x(n,m[0]),i=h(n,m[1]),r.push(t(m,u,i)))}return r.length>0?r:null}function g(n,t,e){return t[0]=x(n,e[0]),t[3]=h(n,e[1]),t[2]=x(n,e[2]),t[1]=h(n,e[3]),t}function p(n,t,e,r){return I(n,e?r?c:m:r?m:a,t)}function N(n,t,e,r){const u=[],i=e?r?c:m:r?m:a;for(let o=0;o<t.length;o++){const e=I(n,i,t[o]);e&&e.length>=3&&u.push(e)}return u.length?u:null}function y(n,t,e,r){const u=[],i=e?r?c:m:r?m:a;for(let o=0;o<t.length;o++){const e=I(n,i,t[o]);e&&e.length>=2&&u.push(e)}return u.length?u:null}function z({scale:n,translate:t},e){return e*n[0]+t[0]}function T({scale:n,translate:t},e){return t[1]-e*n[1]}function M(n,t,e){const r=new Array(e.length);if(!e.length)return r;const[u,i]=n.scale;let o=z(n,e[0][0]),l=T(n,e[0][1]);r[0]=t(e[0],o,l);for(let a=1;a<e.length;a++){const n=e[a];o+=n[0]*u,l-=n[1]*i,r[a]=t(n,o,l)}return r}function E(n,t,e){const r=new Array(e.length);for(let u=0;u<e.length;u++)r[u]=M(n,t,e[u]);return r}function P(n,t,e){return e?(t[0]=z(n,e[0]),t[1]=T(n,e[3]),t[2]=z(n,e[2]),t[3]=T(n,e[1]),t):[z(n,t[0]),T(n,t[3]),z(n,t[2]),T(n,t[1])]}function b(n,t,e,r){return M(n,e?r?c:m:r?m:a,t)}function F(n,t,e,r){return E(n,e?r?c:m:r?m:a,t)}function V(n,t,e,r){return E(n,e?r?c:m:r?m:a,t)}function Y(n,t,e){let[r,u]=e[0],i=Math.min(r,t[0]),o=Math.min(u,t[1]),l=Math.max(r,t[2]),a=Math.max(u,t[3]);for(let m=1;m<e.length;m++){const[n,t]=e[m];r+=n,u+=t,n<0&&(i=Math.min(i,r)),n>0&&(l=Math.max(l,r)),t<0?o=Math.min(o,u):t>0&&(a=Math.max(a,u))}return n[0]=i,n[1]=o,n[2]=l,n[3]=a,n}function _(n,t){if(!t.length)return null;n[0]=n[1]=Number.POSITIVE_INFINITY,n[2]=n[3]=Number.NEGATIVE_INFINITY;for(let e=0;e<t.length;e++)Y(n,n,t[e]);return n}function A(n){const t=[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY];return Y(t,t,n)}function w(n){return _([0,0,0,0],n)}function j(n){return _([0,0,0,0],n)}function G(n,t,e,r,u){return t.xmin=x(n,e.xmin),t.ymin=h(n,e.ymin),t.xmax=x(n,e.xmax),t.ymax=h(n,e.ymax),t!==e&&(r&&(t.zmin=e.zmin,t.zmax=e.zmax),u&&(t.mmin=e.mmin,t.mmax=e.mmax)),t}function L(n,t,e,r,u){return t.points=p(n,e.points,r,u)??[],t}function O(n,t,e,r,u){return t.x=x(n,e.x),t.y=h(n,e.y),t!==e&&(r&&(t.z=e.z),u&&(t.m=e.m)),t}function S(n,t,e,r,u){const i=N(n,e.rings,r,u);return i?(t.rings=i,t):null}function d(n,t,e,r,u){const i=y(n,e.paths,r,u);return i?(t.paths=i,t):null}function U(n,t){return n&&t?e(t)?O(n,{},t,!1,!1):r(t)?d(n,{},t,!1,!1):u(t)?S(n,{},t,!1,!1):i(t)?L(n,{},t,!1,!1):o(t)?G(n,{},t,!1,!1):null:null}function k(n,t,e,r,u){return t.xmin=z(n,e.xmin),t.ymin=T(n,e.ymin),t.xmax=z(n,e.xmax),t.ymax=T(n,e.ymax),t!==e&&(r&&(t.zmin=e.zmin,t.zmax=e.zmax),u&&(t.mmin=e.mmin,t.mmax=e.mmax)),t}function q(t,e,r,u,i){return n(r)&&(e.points=b(t,r.points,u,i)),e}function v(n,e,r,u,i){return t(r)||(e.x=z(n,r.x),e.y=T(n,r.y),e!==r&&(u&&(e.z=r.z),i&&(e.m=r.m))),e}function B(t,e,r,u,i){return n(r)&&(e.rings=V(t,r.rings,u,i)),e}function C(t,e,r,u,i){return n(r)&&(e.paths=F(t,r.paths,u,i)),e}export{f as equals,Y as getQuantizedBoundsCoordsArray,_ as getQuantizedBoundsCoordsArrayArray,w as getQuantizedBoundsPaths,A as getQuantizedBoundsPoints,j as getQuantizedBoundsRings,g as quantizeBounds,G as quantizeExtent,U as quantizeGeometry,L as quantizeMultipoint,y as quantizePaths,O as quantizePoint,p as quantizePoints,S as quantizePolygon,d as quantizePolyline,N as quantizeRings,x as quantizeX,h as quantizeY,s as toQuantizationTransform,P as unquantizeBounds,M as unquantizeCoordsArray,E as unquantizeCoordsArrayArray,k as unquantizeExtent,q as unquantizeMultipoint,F as unquantizePaths,v as unquantizePoint,b as unquantizePoints,B as unquantizePolygon,C as unquantizePolyline,V as unquantizeRings,z as unquantizeX,T as unquantizeY};\n"], "mappings": ";;;;;;;;;;;;;AAIwN,IAAM,IAAE,CAAC,GAAEA,IAAE,MAAI,CAACA,IAAE,CAAC;AAArB,IAAuB,IAAE,CAAC,GAAEA,IAAE,MAAI,CAACA,IAAE,GAAE,EAAE,CAAC,CAAC;AAA3C,IAA6C,IAAE,CAAC,GAAEA,IAAE,MAAI,CAACA,IAAE,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAE,SAASC,GAAED,IAAE;AAAC,MAAG,CAACA,GAAE,QAAO;AAAK,SAAM,EAAC,gBAAe,iBAAeA,GAAE,iBAAe,cAAY,iBAAeA,GAAE,iBAAe,cAAYA,GAAE,gBAAe,OAAMA,GAAE,YAAU,CAACA,GAAE,WAAUA,GAAE,SAAS,IAAE,CAAC,GAAE,CAAC,GAAE,WAAU,EAAEA,GAAE,MAAM,IAAE,CAACA,GAAE,OAAO,MAAKA,GAAE,OAAO,IAAI,IAAE,CAAC,GAAE,CAAC,EAAC;AAAC;AAAiY,SAAS,EAAE,EAAC,OAAM,GAAE,WAAUE,GAAC,GAAE,GAAE;AAAC,SAAO,KAAK,OAAO,IAAEA,GAAE,CAAC,KAAG,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,EAAC,OAAM,GAAE,WAAUA,GAAC,GAAE,GAAE;AAAC,SAAO,KAAK,OAAOA,GAAE,CAAC,IAAE,KAAG,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,MAAIC,IAAE,GAAE,GAAEC;AAAE,WAAQC,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,IAAAA,KAAE,KAAG,IAAE,EAAE,GAAEC,GAAE,CAAC,CAAC,GAAEF,KAAE,EAAE,GAAEE,GAAE,CAAC,CAAC,GAAE,MAAIH,MAAGC,OAAI,MAAIF,GAAE,KAAKD,GAAEK,IAAE,IAAEH,IAAEC,KAAE,CAAC,CAAC,GAAED,KAAE,GAAE,IAAEC,QAAKD,KAAE,EAAE,GAAEG,GAAE,CAAC,CAAC,GAAE,IAAE,EAAE,GAAEA,GAAE,CAAC,CAAC,GAAEJ,GAAE,KAAKD,GAAEK,IAAEH,IAAE,CAAC,CAAC;AAAA,EAAE;AAAC,SAAOD,GAAE,SAAO,IAAEA,KAAE;AAAI;AAAwF,SAAS,EAAE,GAAEK,IAAE,GAAEC,IAAE;AAAC,SAAO,EAAE,GAAE,IAAEA,KAAE,IAAE,IAAEA,KAAE,IAAE,GAAED,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAEC,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAE,IAAE,IAAED,KAAE,IAAE,IAAEA,KAAE,IAAE;AAAE,WAAQ,IAAE,GAAE,IAAED,GAAE,QAAO,KAAI;AAAC,UAAMG,KAAE,EAAE,GAAE,GAAEH,GAAE,CAAC,CAAC;AAAE,IAAAG,MAAGA,GAAE,UAAQ,KAAGD,GAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,SAAOD,GAAE,SAAOA,KAAE;AAAI;AAAC,SAASE,GAAE,GAAEJ,IAAE,GAAEC,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAE,IAAE,IAAED,KAAE,IAAE,IAAEA,KAAE,IAAE;AAAE,WAAQ,IAAE,GAAE,IAAED,GAAE,QAAO,KAAI;AAAC,UAAMG,KAAE,EAAE,GAAE,GAAEH,GAAE,CAAC,CAAC;AAAE,IAAAG,MAAGA,GAAE,UAAQ,KAAGD,GAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,SAAOD,GAAE,SAAOA,KAAE;AAAI;AAAC,SAAS,EAAE,EAAC,OAAM,GAAE,WAAUF,GAAC,GAAE,GAAE;AAAC,SAAO,IAAE,EAAE,CAAC,IAAEA,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,EAAC,OAAM,GAAE,WAAUA,GAAC,GAAE,GAAE;AAAC,SAAOA,GAAE,CAAC,IAAE,IAAE,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,QAAMC,KAAE,IAAI,MAAM,EAAE,MAAM;AAAE,MAAG,CAAC,EAAE,OAAO,QAAOA;AAAE,QAAK,CAACC,IAAE,CAAC,IAAE,EAAE;AAAM,MAAI,IAAE,EAAE,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEG,KAAE,EAAE,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC;AAAE,EAAAJ,GAAE,CAAC,IAAED,GAAE,EAAE,CAAC,GAAE,GAAEK,EAAC;AAAE,WAAQC,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,SAAGC,GAAE,CAAC,IAAEL,IAAEG,MAAGE,GAAE,CAAC,IAAE,GAAEN,GAAEK,EAAC,IAAEN,GAAEO,IAAE,GAAEF,EAAC;AAAA,EAAC;AAAC,SAAOJ;AAAC;AAAC,SAAS,EAAE,GAAED,IAAE,GAAE;AAAC,QAAMC,KAAE,IAAI,MAAM,EAAE,MAAM;AAAE,WAAQC,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,CAAAD,GAAEC,EAAC,IAAE,EAAE,GAAEF,IAAE,EAAEE,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAsI,SAAS,EAAE,GAAEO,IAAE,GAAEC,IAAE;AAAC,SAAO,EAAE,GAAE,IAAEA,KAAE,IAAE,IAAEA,KAAE,IAAE,GAAED,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAEC,IAAE;AAAC,SAAO,EAAE,GAAE,IAAEA,KAAE,IAAE,IAAEA,KAAE,IAAE,GAAED,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAEC,IAAE;AAAC,SAAO,EAAE,GAAE,IAAEA,KAAE,IAAE,IAAEA,KAAE,IAAE,GAAED,EAAC;AAAC;AAAopB,SAAS,EAAE,GAAEE,IAAE,GAAEC,IAAEC,IAAE;AAAC,SAAOF,GAAE,OAAK,EAAE,GAAE,EAAE,IAAI,GAAEA,GAAE,OAAK,EAAE,GAAE,EAAE,IAAI,GAAEA,GAAE,OAAK,EAAE,GAAE,EAAE,IAAI,GAAEA,GAAE,OAAK,EAAE,GAAE,EAAE,IAAI,GAAEA,OAAI,MAAIC,OAAID,GAAE,OAAK,EAAE,MAAKA,GAAE,OAAK,EAAE,OAAME,OAAIF,GAAE,OAAK,EAAE,MAAKA,GAAE,OAAK,EAAE,QAAOA;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAEC,IAAEC,IAAE;AAAC,SAAOF,GAAE,SAAO,EAAE,GAAE,EAAE,QAAOC,IAAEC,EAAC,KAAG,CAAC,GAAEF;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAEC,IAAEC,IAAE;AAAC,SAAOF,GAAE,IAAE,EAAE,GAAE,EAAE,CAAC,GAAEA,GAAE,IAAE,EAAE,GAAE,EAAE,CAAC,GAAEA,OAAI,MAAIC,OAAID,GAAE,IAAE,EAAE,IAAGE,OAAIF,GAAE,IAAE,EAAE,KAAIA;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAEC,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAE,GAAE,EAAE,OAAMD,IAAEC,EAAC;AAAE,SAAO,KAAGF,GAAE,QAAM,GAAEA,MAAG;AAAI;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAEC,IAAEC,IAAE;AAAC,QAAM,IAAEC,GAAE,GAAE,EAAE,OAAMF,IAAEC,EAAC;AAAE,SAAO,KAAGF,GAAE,QAAM,GAAEA,MAAG;AAAI;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,SAAO,KAAGA,KAAE,EAAEA,EAAC,IAAE,EAAE,GAAE,CAAC,GAAEA,IAAE,OAAG,KAAE,IAAE,EAAEA,EAAC,IAAE,EAAE,GAAE,CAAC,GAAEA,IAAE,OAAG,KAAE,IAAE,EAAEA,EAAC,IAAE,EAAE,GAAE,CAAC,GAAEA,IAAE,OAAG,KAAE,IAAE,EAAEA,EAAC,IAAE,EAAE,GAAE,CAAC,GAAEA,IAAE,OAAG,KAAE,IAAE,EAAEA,EAAC,IAAE,EAAE,GAAE,CAAC,GAAEA,IAAE,OAAG,KAAE,IAAE,OAAK;AAAI;AAAuL,SAAS,EAAEI,IAAE,GAAEC,IAAEC,IAAE,GAAE;AAAC,SAAO,EAAED,EAAC,MAAI,EAAE,SAAO,EAAED,IAAEC,GAAE,QAAOC,IAAE,CAAC,IAAG;AAAC;AAAC,SAAS,EAAE,GAAE,GAAED,IAAEC,IAAE,GAAE;AAAC,SAAO,EAAED,EAAC,MAAI,EAAE,IAAE,EAAE,GAAEA,GAAE,CAAC,GAAE,EAAE,IAAE,EAAE,GAAEA,GAAE,CAAC,GAAE,MAAIA,OAAIC,OAAI,EAAE,IAAED,GAAE,IAAG,MAAI,EAAE,IAAEA,GAAE,MAAK;AAAC;AAAC,SAAS,EAAED,IAAE,GAAEC,IAAEC,IAAE,GAAE;AAAC,SAAO,EAAED,EAAC,MAAI,EAAE,QAAM,EAAED,IAAEC,GAAE,OAAMC,IAAE,CAAC,IAAG;AAAC;AAAC,SAAS,EAAEF,IAAE,GAAEC,IAAEC,IAAE,GAAE;AAAC,SAAO,EAAED,EAAC,MAAI,EAAE,QAAM,EAAED,IAAEC,GAAE,OAAMC,IAAE,CAAC,IAAG;AAAC;", "names": ["t", "s", "t", "r", "u", "l", "a", "m", "t", "r", "u", "e", "y", "l", "a", "n", "t", "r", "t", "r", "u", "y", "t", "r", "u"]}