import {
  t
} from "./chunk-OEIEPNC6.js";
import {
  _n,
  ln
} from "./chunk-UYAKJRPP.js";
import {
  d,
  g,
  y
} from "./chunk-R5MYQRRS.js";
import {
  E,
  I,
  k
} from "./chunk-JXLVNWKF.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/graphics/data/projectionSupport.js
var l = [0, 0];
function h(s, t2) {
  if (!t2) return null;
  if ("x" in t2) {
    const e = { x: 0, y: 0 };
    return [e.x, e.y] = s(t2.x, t2.y, l), null != t2.z && (e.z = t2.z), null != t2.m && (e.m = t2.m), e;
  }
  if ("xmin" in t2) {
    const e = { xmin: 0, ymin: 0, xmax: 0, ymax: 0 };
    return [e.xmin, e.ymin] = s(t2.xmin, t2.ymin, l), [e.xmax, e.ymax] = s(t2.xmax, t2.ymax, l), t2.hasZ && (e.zmin = t2.zmin, e.zmax = t2.zmax, e.hasZ = true), t2.hasM && (e.mmin = t2.mmin, e.mmax = t2.mmax, e.hasM = true), e;
  }
  return "rings" in t2 ? { rings: c(t2.rings, s), hasM: t2.hasM, hasZ: t2.hasZ } : "paths" in t2 ? { paths: c(t2.paths, s), hasM: t2.hasM, hasZ: t2.hasZ } : "points" in t2 ? { points: p(t2.points, s), hasM: t2.hasM, hasZ: t2.hasZ } : null;
}
function c(s, t2) {
  const e = [];
  for (const n of s) e.push(p(n, t2));
  return e;
}
function p(s, t2) {
  const e = [];
  for (const n of s) {
    const s2 = t2(n[0], n[1], [0, 0]);
    e.push(s2), n.length > 2 && s2.push(n[2]), n.length > 3 && s2.push(n[3]);
  }
  return e;
}
async function f(e, n) {
  if (!e || !n) return;
  const r2 = Array.isArray(e) ? e.map((t2) => r(t2.geometry) ? t2.geometry.spatialReference : null).filter(r) : [e];
  await _n(r2.map((s) => ({ source: s, dest: n })));
}
var x = h.bind(null, y);
var y2 = h.bind(null, d);
function g2(s, t2, a, m) {
  if (!s) return s;
  if (a || (a = t2, t2 = s.spatialReference), !I(t2) || !I(a) || E(t2, a)) return s;
  if (g(t2, a)) {
    const t3 = k(a) ? x(s) : y2(s);
    return t3.spatialReference = a, t3;
  }
  return ln(t, [s], t2, a, null, m)[0];
}
var _ = class {
  constructor() {
    this._jobs = [], this._timer = null, this._process = this._process.bind(this);
  }
  async push(s, t2, e) {
    if (!s || !s.length || !t2 || !e || E(t2, e)) return s;
    const n = { geometries: s, inSpatialReference: t2, outSpatialReference: e, resolve: null };
    return this._jobs.push(n), new Promise((s2) => {
      n.resolve = s2, null === this._timer && (this._timer = setTimeout(this._process, 10));
    });
  }
  _process() {
    this._timer = null;
    const s = this._jobs.shift();
    if (!s) return;
    const { geometries: t2, inSpatialReference: r2, outSpatialReference: i, resolve: a } = s;
    g(r2, i) ? k(i) ? a(t2.map(x)) : a(t2.map(y2)) : a(ln(t, t2, r2, i, null, null)), this._jobs.length > 0 && (this._timer = setTimeout(this._process, 10));
  }
};
var j = new _();
function M(s, t2, e) {
  return j.push(s, t2, e);
}

export {
  f,
  g2 as g,
  M
};
//# sourceMappingURL=chunk-U4SDSCWW.js.map
