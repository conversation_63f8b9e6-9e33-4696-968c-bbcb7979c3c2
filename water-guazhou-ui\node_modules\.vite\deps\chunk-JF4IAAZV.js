import {
  v
} from "./chunk-ZJC3GHA7.js";
import {
  d
} from "./chunk-HTXGAKOK.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/heightModelInfoUtils.js
function o(t, i, r2) {
  const o2 = h(t), c2 = i, u2 = a(o2, c2, r2);
  if (o2) {
    const i2 = v.deriveUnitFromSR(o2, t.spatialReference).heightUnit;
    if (!r2 && i2 !== o2.heightUnit) {
      const t2 = new s("layerview:unmatched-height-unit", `The vertical units of the layer must match the horizontal units (${i2})`, { horizontalUnit: i2 });
      return new s("layerview:unsupported-height-model-info", "The vertical coordinate system of the layer is not supported", { heightModelInfo: o2, error: t2 });
    }
  }
  if (!l(t) || u2 === s2.Unsupported) return new s("layerview:unsupported-height-model-info", "The vertical coordinate system of the layer is not supported", { heightModelInfo: o2 });
  switch (u2) {
    case s2.Units: {
      const t2 = (o2 == null ? void 0 : o2.heightUnit) || "unknown", n = (c2 == null ? void 0 : c2.heightUnit) || "unknown", i2 = new s("layerview:incompatible-height-unit", `The vertical units of the layer (${t2}) must match the vertical units of the scene (${n})`, { layerUnit: t2, sceneUnit: n });
      return new s("layerview:incompatible-height-model-info", "The vertical coordinate system of the layer is incompatible with the scene", { layerHeightModelInfo: o2, sceneHeightModelInfo: c2, error: i2 });
    }
    case s2.HeightModel: {
      const t2 = (o2 == null ? void 0 : o2.heightModel) || "unknown", n = (c2 == null ? void 0 : c2.heightModel) || "unknown", i2 = new s("layerview:incompatible-height-model", `The height model of the layer (${t2}) must match the height model of the scene (${n})`, { layerHeightModel: t2, sceneHeightModel: n });
      return new s("layerview:incompatible-height-model-info", "The vertical coordinate system of the layer is incompatible with the scene", { layerHeightModelInfo: o2, sceneHeightModelInfo: c2, error: i2 });
    }
    case s2.CRS: {
      const t2 = (o2 == null ? void 0 : o2.vertCRS) || "unknown", n = (c2 == null ? void 0 : c2.vertCRS) || "unknown", i2 = new s("layerview:incompatible-vertical-datum", `The vertical datum of the layer (${t2}) must match the vertical datum of the scene (${n})`, { layerDatum: t2, sceneDatum: n });
      return new s("layerview:incompatible-height-model-info", "The vertical coordinate system of the layer is incompatible with the scene", { layerHeightModelInfo: o2, sceneHeightModelInfo: c2, error: i2 });
    }
  }
  return null;
}
function a(e, t, n) {
  if (!c(e) || !c(t)) return s2.Unsupported;
  if (null == e || null == t) return s2.Ok;
  if (!n && e.heightUnit !== t.heightUnit) return s2.Units;
  if (e.heightModel !== t.heightModel) return s2.HeightModel;
  switch (e.heightModel) {
    case "gravity-related-height":
      return s2.Ok;
    case "ellipsoidal":
      return e.vertCRS === t.vertCRS ? s2.Ok : s2.CRS;
    default:
      return s2.Unsupported;
  }
}
var s2;
function c(e) {
  return null == e || null != e.heightModel && null != e.heightUnit;
}
function l(e) {
  return "heightModelInfo" in e && null != e.heightModelInfo || null != e.spatialReference || !g(e);
}
function h(e) {
  var _a;
  const r2 = e.url ? d(e.url) : void 0, o2 = (_a = e.spatialReference) == null ? void 0 : _a.vcsWkid;
  return !(null == o2 && r(r2) && "ImageServer" === r2.serverType) && u(e) && e.heightModelInfo ? e.heightModelInfo : g(e) ? v.deriveUnitFromSR(p, e.spatialReference) : null;
}
function u(e) {
  return "heightModelInfo" in e;
}
function d2(e) {
  if ("unknown" === e.type || !("capabilities" in e)) return false;
  switch (e.type) {
    case "csv":
    case "feature":
    case "geojson":
    case "subtype-group":
    case "ogc-feature":
    case "oriented-imagery":
    case "wfs":
    case "knowledge-graph-sublayer":
      return true;
    default:
      return false;
  }
}
function g(e) {
  return d2(e) ? !!(e.capabilities && e.capabilities.data && e.capabilities.data.supportsZ) : m(e);
}
function f(e) {
  return null != e.layers || m(e) || d2(e) || u(e);
}
function m(e) {
  switch (e.type) {
    case "building-scene":
    case "elevation":
    case "integrated-mesh":
    case "point-cloud":
    case "scene":
    case "voxel":
      return true;
    case "base-dynamic":
    case "base-elevation":
    case "base-tile":
    case "bing-maps":
    case "csv":
    case "dimension":
    case "geojson":
    case "feature":
    case "subtype-group":
    case "geo-rss":
    case "graphics":
    case "group":
    case "imagery":
    case "imagery-tile":
    case "kml":
    case "knowledge-graph":
    case "link-chart":
    case "knowledge-graph-sublayer":
    case "line-of-sight":
    case "map-image":
    case "map-notes":
    case "media":
    case "ogc-feature":
    case "open-street-map":
    case "oriented-imagery":
    case "route":
    case "stream":
    case "tile":
    case "unknown":
    case "unsupported":
    case "vector-tile":
    case "wcs":
    case "web-tile":
    case "wfs":
    case "wms":
    case "wmts":
    case null:
      return false;
  }
  return false;
}
!function(e) {
  e[e.Ok = 0] = "Ok", e[e.Units = 1] = "Units", e[e.HeightModel = 2] = "HeightModel", e[e.CRS = 3] = "CRS", e[e.Unsupported = 4] = "Unsupported";
}(s2 || (s2 = {}));
var p = new v({ heightModel: "gravity-related-height" });

export {
  o,
  h,
  f
};
//# sourceMappingURL=chunk-JF4IAAZV.js.map
