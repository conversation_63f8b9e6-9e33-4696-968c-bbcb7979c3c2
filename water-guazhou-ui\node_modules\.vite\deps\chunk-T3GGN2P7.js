import {
  o
} from "./chunk-2WMCP27R.js";
import {
  r
} from "./chunk-UCWK623G.js";

// node_modules/@arcgis/core/arcade/arcadeTimeUtils.js
function c(e) {
  return (e == null ? void 0 : e.timeZoneIANA) ? e == null ? void 0 : e.timeZoneIANA : (e == null ? void 0 : e.timeZone) ? o(e, "") : "";
}
var T = class _T {
  constructor() {
    this.dateTimeReferenceMetaData = null, this._fieldTimeZoneIndex = {}, this._fieldIndex = null, this._ianaPreferred = null, this._ianaTimeInfo = null, this._ianaEditFields = null, this._ianaLayerDateFields = null;
  }
  static create(e, t) {
    const i2 = new _T();
    return i2.dateTimeReferenceMetaData = t, i2._fieldIndex = e instanceof r ? e : new r(e), i2;
  }
  static createFromLayer(e) {
    var _a, _b, _c, _d;
    if (!e) return null;
    if (!e.fieldsIndex) return !e.declaredClass && e.fields ? _T.create(e.fields, e) : null;
    const t = new _T();
    return t._fieldIndex = e.fieldsIndex, t.dateTimeReferenceMetaData = { timeInfo: ((_a = e == null ? void 0 : e.timeInfo) == null ? void 0 : _a.toJSON()) ?? null, editFieldsInfo: ((_b = e == null ? void 0 : e.editFieldsInfo) == null ? void 0 : _b.toJSON()) ?? null, dateFieldsTimeReference: ((_c = e == null ? void 0 : e.dateFieldsTimeReference) == null ? void 0 : _c.toJSON()) ?? null, preferredTimeReference: ((_d = e == null ? void 0 : e.preferredTimeReference) == null ? void 0 : _d.toJSON()) ?? null, datesInUnknownTimezone: true === (e == null ? void 0 : e.datesInUnknownTimezone) }, t;
  }
  fieldTimeZone(e) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v, _w;
    const t = (_a = this._fieldIndex) == null ? void 0 : _a.get(e);
    if (!t) return null;
    if ("date" !== t.type && "esriFieldTypeDate" !== t.type) return null;
    const i2 = this._fieldTimeZoneIndex[t.name];
    if (void 0 !== i2) return i2;
    const n = [{ field: (_c = (_b = this.dateTimeReferenceMetaData) == null ? void 0 : _b.editFieldsInfo) == null ? void 0 : _c.creationDateField, timeReference: (_e = (_d = this.dateTimeReferenceMetaData) == null ? void 0 : _d.editFieldsInfo) == null ? void 0 : _e.dateFieldsTimeReference, isunknown: true === ((_f = this.dateTimeReferenceMetaData) == null ? void 0 : _f.datesInUnknownTimezone) }, { field: (_h = (_g = this.dateTimeReferenceMetaData) == null ? void 0 : _g.editFieldsInfo) == null ? void 0 : _h.editDateField, timeReference: (_j = (_i = this.dateTimeReferenceMetaData) == null ? void 0 : _i.editFieldsInfo) == null ? void 0 : _j.dateFieldsTimeReference, isunknown: true === ((_k = this.dateTimeReferenceMetaData) == null ? void 0 : _k.datesInUnknownTimezone) }, { field: (_m = (_l = this.dateTimeReferenceMetaData) == null ? void 0 : _l.timeInfo) == null ? void 0 : _m.startTimeField, timeReference: (_o = (_n = this.dateTimeReferenceMetaData) == null ? void 0 : _n.timeInfo) == null ? void 0 : _o.timeReference, isunknown: true === ((_p = this.dateTimeReferenceMetaData) == null ? void 0 : _p.datesInUnknownTimezone) }, { field: (_r = (_q = this.dateTimeReferenceMetaData) == null ? void 0 : _q.timeInfo) == null ? void 0 : _r.endTimeField, timeReference: (_t = (_s = this.dateTimeReferenceMetaData) == null ? void 0 : _s.timeInfo) == null ? void 0 : _t.timeReference, isunknown: true === ((_u = this.dateTimeReferenceMetaData) == null ? void 0 : _u.datesInUnknownTimezone) }];
    for (const r2 of n) if (r2.field === t.name) {
      const e2 = this.convertToIANA(r2.timeReference, r2.isunknown);
      return this._fieldTimeZoneIndex[t.name] = e2, e2;
    }
    const a2 = this.convertToIANA((_v = this.dateTimeReferenceMetaData) == null ? void 0 : _v.dateFieldsTimeReference, (_w = this.dateTimeReferenceMetaData) == null ? void 0 : _w.datesInUnknownTimezone);
    return this._fieldTimeZoneIndex[t.name] = a2, a2;
  }
  convertToIANA(e, t) {
    return t ? "unknown" : c(e);
  }
  get layerPreferredTimeZone() {
    var _a, _b;
    if (null !== this._ianaPreferred) return this._ianaPreferred;
    this._ianaPreferred = "";
    const e = (_a = this.dateTimeReferenceMetaData) == null ? void 0 : _a.preferredTimeReference;
    return this._ianaPreferred = this.convertToIANA(e, true === ((_b = this.dateTimeReferenceMetaData) == null ? void 0 : _b.datesInUnknownTimezone)), this._ianaPreferred;
  }
  get layerTimeInfoTimeZone() {
    var _a, _b;
    if (null !== this._ianaTimeInfo) return this._ianaTimeInfo;
    this._ianaTimeInfo = "";
    const e = (_b = (_a = this.dateTimeReferenceMetaData) == null ? void 0 : _a.timeInfo) == null ? void 0 : _b.timeReference;
    return this._ianaTimeInfo = this.convertToIANA(e, false), this._ianaTimeInfo;
  }
  get layerEditFieldsTimeZone() {
    var _a, _b, _c;
    if (null !== this._ianaEditFields) return this._ianaEditFields;
    this._ianaEditFields = "";
    const e = (_b = (_a = this.dateTimeReferenceMetaData) == null ? void 0 : _a.editFieldsInfo) == null ? void 0 : _b.dateFieldsTimeReference;
    return this._ianaEditFields = this.convertToIANA(e, (_c = this.dateTimeReferenceMetaData) == null ? void 0 : _c.datesInUnknownTimezone), this._ianaEditFields;
  }
  get layerDateFieldsTimeZone() {
    var _a, _b;
    if (null !== this._ianaLayerDateFields) return this._ianaLayerDateFields;
    this._ianaLayerDateFields = "";
    const e = (_a = this.dateTimeReferenceMetaData) == null ? void 0 : _a.dateFieldsTimeReference;
    return this._ianaLayerDateFields = this.convertToIANA(e, true === ((_b = this.dateTimeReferenceMetaData) == null ? void 0 : _b.datesInUnknownTimezone)), this._ianaLayerDateFields;
  }
};

export {
  T
};
//# sourceMappingURL=chunk-T3GGN2P7.js.map
