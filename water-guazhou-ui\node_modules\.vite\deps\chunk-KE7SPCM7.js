import {
  y as y3
} from "./chunk-NZB6EMKN.js";
import {
  g
} from "./chunk-TLKX5XIJ.js";
import {
  n
} from "./chunk-7THWOTCY.js";
import {
  v,
  y as y2
} from "./chunk-ZACBBT3Y.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  e as e2,
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/rest/support/FeatureSet.js
var d;
var g2 = new s({ esriGeometryPoint: "point", esriGeometryMultipoint: "multipoint", esriGeometryPolyline: "polyline", esriGeometryPolygon: "polygon", esriGeometryEnvelope: "extent", mesh: "mesh", "": null });
var j = d = class extends l {
  constructor(e3) {
    super(e3), this.displayFieldName = null, this.exceededTransferLimit = false, this.features = [], this.fields = null, this.geometryType = null, this.hasM = false, this.hasZ = false, this.queryGeometry = null, this.spatialReference = null;
  }
  readFeatures(e3, t) {
    var _a;
    const o2 = f.fromJSON(t.spatialReference), s2 = [];
    for (let i = 0; i < e3.length; i++) {
      const t2 = e3[i], a2 = g.fromJSON(t2), p2 = t2.geometry && t2.geometry.spatialReference;
      r(a2.geometry) && !p2 && (a2.geometry.spatialReference = o2);
      const l2 = t2.aggregateGeometries, y4 = a2.aggregateGeometries;
      if (l2 && r(y4)) for (const e4 in y4) {
        const t3 = y4[e4], r3 = (_a = l2[e4]) == null ? void 0 : _a.spatialReference;
        r(t3) && !r3 && (t3.spatialReference = o2);
      }
      s2.push(a2);
    }
    return s2;
  }
  writeGeometryType(e3, t, r3, o2) {
    if (e3) return void g2.write(e3, t, r3, o2);
    const { features: s2 } = this;
    if (s2) {
      for (const i of s2) if (i && r(i.geometry)) return void g2.write(i.geometry.type, t, r3, o2);
    }
  }
  readQueryGeometry(e3, t) {
    if (!e3) return null;
    const r3 = !!e3.spatialReference, o2 = v(e3);
    return o2 && !r3 && t.spatialReference && (o2.spatialReference = f.fromJSON(t.spatialReference)), o2;
  }
  writeSpatialReference(e3, t) {
    if (e3) return void (t.spatialReference = e3.toJSON());
    const { features: r3 } = this;
    if (r3) {
      for (const o2 of r3) if (o2 && r(o2.geometry) && o2.geometry.spatialReference) return void (t.spatialReference = o2.geometry.spatialReference.toJSON());
    }
  }
  clone() {
    return new d(this.cloneProperties());
  }
  cloneProperties() {
    return p({ displayFieldName: this.displayFieldName, exceededTransferLimit: this.exceededTransferLimit, features: this.features, fields: this.fields, geometryType: this.geometryType, hasM: this.hasM, hasZ: this.hasZ, queryGeometry: this.queryGeometry, spatialReference: this.spatialReference, transform: this.transform });
  }
  toJSON(e3) {
    const t = this.write();
    if (t.features && Array.isArray(e3) && e3.length > 0) for (let r3 = 0; r3 < t.features.length; r3++) {
      const o2 = t.features[r3];
      if (o2.geometry) {
        const t2 = e3 && e3[r3];
        o2.geometry = t2 && t2.toJSON() || o2.geometry;
      }
    }
    return t;
  }
  quantize(e3) {
    const { scale: [t, r3], translate: [o2, s2] } = e3, i = (e4) => Math.round((e4 - o2) / t), n2 = (e4) => Math.round((s2 - e4) / r3), p2 = this.features, l2 = this._getQuantizationFunction(this.geometryType, i, n2);
    for (let y4 = 0, m = p2.length; y4 < m; y4++) (l2 == null ? void 0 : l2(e2(p2[y4].geometry))) || (p2.splice(y4, 1), y4--, m--);
    return this.transform = e3, this;
  }
  unquantize() {
    const { geometryType: e3, features: t, transform: r3 } = this;
    if (!r3) return this;
    const { translate: [o2, s2], scale: [i, a2] } = r3, p2 = (e4) => e4 * i + o2, l2 = (e4) => s2 - e4 * a2, y4 = this._getHydrationFunction(e3, p2, l2);
    for (const { geometry: m } of t) r(m) && y4 && y4(m);
    return this.transform = null, this;
  }
  _quantizePoints(e3, t, r3) {
    let o2, s2;
    const i = [];
    for (let n2 = 0, a2 = e3.length; n2 < a2; n2++) {
      const a3 = e3[n2];
      if (n2 > 0) {
        const e4 = t(a3[0]), n3 = r3(a3[1]);
        e4 === o2 && n3 === s2 || (i.push([e4 - o2, n3 - s2]), o2 = e4, s2 = n3);
      } else o2 = t(a3[0]), s2 = r3(a3[1]), i.push([o2, s2]);
    }
    return i.length > 0 ? i : null;
  }
  _getQuantizationFunction(e3, t, r3) {
    return "point" === e3 ? (e4) => (e4.x = t(e4.x), e4.y = r3(e4.y), e4) : "polyline" === e3 || "polygon" === e3 ? (e4) => {
      const o2 = y2(e4) ? e4.rings : e4.paths, s2 = [];
      for (let i = 0, n2 = o2.length; i < n2; i++) {
        const e5 = o2[i], n3 = this._quantizePoints(e5, t, r3);
        n3 && s2.push(n3);
      }
      return s2.length > 0 ? (y2(e4) ? e4.rings = s2 : e4.paths = s2, e4) : null;
    } : "multipoint" === e3 ? (e4) => {
      const o2 = this._quantizePoints(e4.points, t, r3);
      return o2 && o2.length > 0 ? (e4.points = o2, e4) : null;
    } : "extent" === e3 ? (e4) => e4 : null;
  }
  _getHydrationFunction(e3, t, r3) {
    return "point" === e3 ? (e4) => {
      e4.x = t(e4.x), e4.y = r3(e4.y);
    } : "polyline" === e3 || "polygon" === e3 ? (e4) => {
      const o2 = y2(e4) ? e4.rings : e4.paths;
      let s2, i;
      for (let n2 = 0, a2 = o2.length; n2 < a2; n2++) {
        const e5 = o2[n2];
        for (let o3 = 0, n3 = e5.length; o3 < n3; o3++) {
          const n4 = e5[o3];
          o3 > 0 ? (s2 += n4[0], i += n4[1]) : (s2 = n4[0], i = n4[1]), n4[0] = t(s2), n4[1] = r3(i);
        }
      }
    } : "extent" === e3 ? (e4) => {
      e4.xmin = t(e4.xmin), e4.ymin = r3(e4.ymin), e4.xmax = t(e4.xmax), e4.ymax = r3(e4.ymax);
    } : "multipoint" === e3 ? (e4) => {
      const o2 = e4.points;
      let s2, i;
      for (let n2 = 0, a2 = o2.length; n2 < a2; n2++) {
        const e5 = o2[n2];
        n2 > 0 ? (s2 += e5[0], i += e5[1]) : (s2 = e5[0], i = e5[1]), e5[0] = t(s2), e5[1] = r3(i);
      }
    } : null;
  }
};
e([y({ type: String, json: { write: true } })], j.prototype, "displayFieldName", void 0), e([y({ type: Boolean, json: { write: { overridePolicy: (e3) => ({ enabled: e3 }) } } })], j.prototype, "exceededTransferLimit", void 0), e([y({ type: [g], json: { write: true } })], j.prototype, "features", void 0), e([o("features")], j.prototype, "readFeatures", null), e([y({ type: [y3], json: { write: true } })], j.prototype, "fields", void 0), e([y({ type: ["point", "multipoint", "polyline", "polygon", "extent", "mesh"], json: { read: { reader: g2.read } } })], j.prototype, "geometryType", void 0), e([r2("geometryType")], j.prototype, "writeGeometryType", null), e([y({ type: Boolean, json: { write: { overridePolicy: (e3) => ({ enabled: e3 }) } } })], j.prototype, "hasM", void 0), e([y({ type: Boolean, json: { write: { overridePolicy: (e3) => ({ enabled: e3 }) } } })], j.prototype, "hasZ", void 0), e([y({ types: n, json: { write: true } })], j.prototype, "queryGeometry", void 0), e([o("queryGeometry")], j.prototype, "readQueryGeometry", null), e([y({ type: f, json: { write: true } })], j.prototype, "spatialReference", void 0), e([r2("spatialReference")], j.prototype, "writeSpatialReference", null), e([y({ json: { write: true } })], j.prototype, "transform", void 0), j = d = e([a("esri.rest.support.FeatureSet")], j), j.prototype.toJSON.isDefaultToJSON = true;
var x = j;

export {
  x
};
//# sourceMappingURL=chunk-KE7SPCM7.js.map
