import {
  t
} from "./chunk-Q7KDWWJV.js";
import {
  F
} from "./chunk-6KZTVN32.js";
import "./chunk-ANH6666P.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-P37TUI4J.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import {
  u
} from "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/symbols/support/previewWebStyleSymbol.js
function h(e, i, h2) {
  const s = e.thumbnail && e.thumbnail.url;
  return s ? U(s, { responseType: "image" }).then((t2) => {
    const e2 = r(t2.data, h2);
    return h2 && h2.node ? (h2.node.appendChild(e2), h2.node) : e2;
  }) : F(e).then((t2) => t2 ? i(t2, h2) : null);
}
function r(t2, n) {
  const h2 = !/\\.svg$/i.test(t2.src) && n && n.disableUpsampling, r2 = Math.max(t2.width, t2.height);
  let s = n && null != n.maxSize ? u(n.maxSize) : t.maxSize;
  h2 && (s = Math.min(r2, s));
  const o = "number" == typeof (n == null ? void 0 : n.size) ? n == null ? void 0 : n.size : null, m = Math.min(s, null != o ? u(o) : r2);
  if (m !== r2) {
    const e = 0 !== t2.width && 0 !== t2.height ? t2.width / t2.height : 1;
    e >= 1 ? (t2.width = m, t2.height = m / e) : (t2.width = m * e, t2.height = m);
  }
  return t2;
}
export {
  h as previewWebStyleSymbol
};
//# sourceMappingURL=previewWebStyleSymbol-C4WNNFNZ.js.map
