{"version": 3, "sources": ["../../@esri/calcite-components/dist/components/utils4.js"], "sourcesContent": ["/*!\n * All material copyright ESRI, All Rights Reserved, unless otherwise specified.\n * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.\n * v1.0.8-next.4\n */\n/**\n * This module provides utils to fix positioning across shadow DOM in browsers that follow the updated offsetParent spec https://github.com/w3c/csswg-drafts/issues/159\n */\nfunction offsetParent(element) {\n  // Do an initial walk to check for display:none ancestors.\n  for (let ancestor = element; ancestor; ancestor = flatTreeParent(ancestor)) {\n    if (!(ancestor instanceof Element)) {\n      continue;\n    }\n    if (getComputedStyle(ancestor).display === \"none\") {\n      return null;\n    }\n  }\n  for (let ancestor = flatTreeParent(element); ancestor; ancestor = flatTreeParent(ancestor)) {\n    if (!(ancestor instanceof Element)) {\n      continue;\n    }\n    const style = getComputedStyle(ancestor);\n    // Display:contents nodes aren't in the layout tree so they should be skipped.\n    if (style.display === \"contents\") {\n      continue;\n    }\n    if (style.position !== \"static\" || style.filter !== \"none\") {\n      return ancestor;\n    }\n    if (ancestor.tagName === \"BODY\") {\n      return ancestor;\n    }\n  }\n  return null;\n}\nfunction flatTreeParent(element) {\n  if (element.assignedSlot) {\n    return element.assignedSlot;\n  }\n  if (element.parentNode instanceof ShadowRoot) {\n    return element.parentNode.host;\n  }\n  return element.parentNode;\n}\n\nexport { offsetParent };\n"], "mappings": ";;;AAQA,SAAS,aAAa,SAAS;AAE7B,WAAS,WAAW,SAAS,UAAU,WAAW,eAAe,QAAQ,GAAG;AAC1E,QAAI,EAAE,oBAAoB,UAAU;AAClC;AAAA,IACF;AACA,QAAI,iBAAiB,QAAQ,EAAE,YAAY,QAAQ;AACjD,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,WAAW,eAAe,OAAO,GAAG,UAAU,WAAW,eAAe,QAAQ,GAAG;AAC1F,QAAI,EAAE,oBAAoB,UAAU;AAClC;AAAA,IACF;AACA,UAAM,QAAQ,iBAAiB,QAAQ;AAEvC,QAAI,MAAM,YAAY,YAAY;AAChC;AAAA,IACF;AACA,QAAI,MAAM,aAAa,YAAY,MAAM,WAAW,QAAQ;AAC1D,aAAO;AAAA,IACT;AACA,QAAI,SAAS,YAAY,QAAQ;AAC/B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,SAAS;AAC/B,MAAI,QAAQ,cAAc;AACxB,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,QAAQ,sBAAsB,YAAY;AAC5C,WAAO,QAAQ,WAAW;AAAA,EAC5B;AACA,SAAO,QAAQ;AACjB;", "names": []}