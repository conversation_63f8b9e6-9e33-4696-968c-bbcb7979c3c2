import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  o
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/support/OrderByInfo.js
var i;
var p = new s({ asc: "ascending", desc: "descending" });
var n = i = class extends l {
  constructor(r) {
    super(r), this.field = null, this.valueExpression = null, this.order = "ascending";
  }
  clone() {
    return new i({ field: this.field, valueExpression: this.valueExpression, order: this.order });
  }
};
e([y({ type: String, json: { write: true } })], n.prototype, "field", void 0), e([y({ type: String, json: { write: true } })], n.prototype, "valueExpression", void 0), e([y({ type: p.apiValues, json: { read: p.read, write: p.write } })], n.prototype, "order", void 0), n = i = e([a("esri.layers.support.OrderByInfo")], n);
var c = n;

// node_modules/@arcgis/core/layers/mixins/OrderedLayer.js
function i2(r, e2, o2) {
  if (!r) return null;
  const t = r.find((r2) => !!r2.field);
  if (!t) return null;
  const i3 = new c();
  return i3.read(t, o2), [i3];
}
function n2(r, o2, t, s2) {
  const i3 = r.find((r2) => !!r2.field);
  i3 && o(t, [i3.toJSON()], o2);
}
var c2 = (e2) => {
  let c3 = class extends e2 {
    constructor() {
      super(...arguments), this.orderBy = null;
    }
  };
  return e([y({ type: [c], json: { origins: { "web-scene": { write: false, read: false } }, read: { source: "layerDefinition.orderBy", reader: i2 }, write: { target: "layerDefinition.orderBy", writer: n2 } } })], c3.prototype, "orderBy", void 0), c3 = e([a("esri.layers.mixins.OrderedLayer")], c3), c3;
};

export {
  c2 as c
};
//# sourceMappingURL=chunk-LNCHRZJI.js.map
