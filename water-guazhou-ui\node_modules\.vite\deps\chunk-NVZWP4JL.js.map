{"version": 3, "sources": ["../../@arcgis/core/chunks/LineMarker.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{MARKER_TEXTURE_SIZE as e,MARKER_SYMBOL_SIZE as r,MARKER_TIP_THICKNESS_FACTOR as o}from\"../views/3d/support/engineContent/marker.js\";import{addLinearDepth as i,addCalculateLinearDepth as a}from\"../views/3d/webgl-engine/core/shaderLibrary/ForwardLinearDepth.glsl.js\";import{ShaderOutput as t}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as n}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{RibbonVertexPosition as s}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/RibbonVertexPosition.glsl.js\";import{OutputDepth as l}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputDepth.glsl.js\";import{MarkerSizing as c}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MarkerSizing.glsl.js\";import{multipassTerrainTest as d}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{symbolAlphaCutoff as p}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{ColorConversion as v}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ColorConversion.glsl.js\";import{RgbaFloatEncoding as m}from\"../views/3d/webgl-engine/core/shaderLibrary/util/RgbaFloatEncoding.glsl.js\";import{addProjViewLocalOrigin as g,addViewNormal as h}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float2PassUniform as f}from\"../views/3d/webgl-engine/core/shaderModules/Float2PassUniform.js\";import{Float4PassUniform as u}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{FloatPassUniform as w}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as y}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{Matrix4PassUniform as S}from\"../views/3d/webgl-engine/core/shaderModules/Matrix4PassUniform.js\";import{ShaderBuilder as b}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{Texture2DPassUniform as x}from\"../views/3d/webgl-engine/core/shaderModules/Texture2DPassUniform.js\";import{TransparencyPassType as P}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";import{VertexAttribute as z}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";import{LineMarkerSpace as L,LineMarkerAnchor as j}from\"../views/3d/webgl-engine/shaders/LineMarkerTechniqueConfiguration.js\";function C(C){const M=new b,D=C.hasMultipassTerrain&&(C.output===t.Color||C.output===t.Alpha),k=C.space===L.World;C.hasTip&&k&&M.extensions.add(\"GL_OES_standard_derivatives\"),M.include(s,C),M.include(c,C),C.output===t.Depth&&M.include(l,C);const{vertex:N,fragment:T}=M;return T.include(m),g(N,C),M.attributes.add(z.POSITION,\"vec3\"),M.attributes.add(z.UV0,\"vec2\"),M.attributes.add(z.AUXPOS1,\"vec3\"),M.varyings.add(\"vColor\",\"vec4\"),M.varyings.add(\"vpos\",\"vec3\"),M.varyings.add(\"vUV\",\"vec2\"),M.varyings.add(\"vSize\",\"float\"),i(M),D&&M.varyings.add(\"depth\",\"float\"),C.hasTip&&M.varyings.add(\"vLineWidth\",\"float\"),N.uniforms.add([new f(\"nearFar\",((e,r)=>r.camera.nearFar)),new u(\"viewport\",((e,r)=>r.camera.fullViewport))]),N.code.add(y`vec4 projectAndScale(vec4 pos) {\nvec4 posNdc = proj * pos;\nposNdc.xy *= viewport.zw / posNdc.w;\nreturn posNdc;\n}`),N.code.add(y`void clip(vec4 pos, inout vec4 prev) {\nfloat vnp = nearFar[0] * 0.99;\nif (prev.z > -nearFar[0]) {\nfloat interpolation = (-vnp - pos.z) / (prev.z - pos.z);\nprev = mix(pos, prev, interpolation);\n}\n}`),k?(M.attributes.add(z.NORMAL,\"vec3\"),h(N),N.constants.add(\"tiltThreshold\",\"float\",.7),N.code.add(y`vec3 perpendicular(vec3 v) {\nvec3 n = (viewNormal * vec4(normal.xyz, 1.0)).xyz;\nvec3 n2 = cross(v, n);\nvec3 forward = vec3(0.0, 0.0, 1.0);\nfloat tiltDot = dot(forward, n);\nreturn abs(tiltDot) < tiltThreshold ? n : n2;\n}`)):N.code.add(y`vec2 perpendicular(vec2 v) {\nreturn vec2(v.y, -v.x);\n}`),N.code.add(y`\n      #define vecN ${k?\"vec3\":\"vec2\"}\n\n      vecN normalizedSegment(vecN pos, vecN prev) {\n        vecN segment = pos - prev;\n        float segmentLen = length(segment);\n\n        // normalize or zero if too short\n        return (segmentLen > 0.001) ? segment / segmentLen : ${k?\"vec3(0.0, 0.0, 0.0)\":\"vec2(0.0, 0.0)\"};\n      }\n\n      vecN displace(vecN pos, vecN prev, float displacementLen) {\n        vecN segment = normalizedSegment(pos, prev);\n\n        vecN displacementDirU = perpendicular(segment);\n        vecN displacementDirV = segment;\n\n        ${C.anchor===j.Tip?\"pos -= 0.5 * displacementLen * displacementDirV;\":\"\"}\n\n        return pos + displacementLen * (uv0.x * displacementDirU + uv0.y * displacementDirV);\n      }\n    `),C.space===L.Screen&&(N.uniforms.add(new S(\"inverseProjectionMatrix\",((e,r)=>r.camera.inverseProjectionMatrix))),N.code.add(y`vec3 inverseProject(vec4 posScreen) {\nposScreen.xy = (posScreen.xy / viewport.zw) * posScreen.w;\nreturn (inverseProjectionMatrix * posScreen).xyz;\n}`),N.code.add(y`bool rayIntersectPlane(vec3 rayDir, vec3 planeOrigin, vec3 planeNormal, out vec3 intersection) {\nfloat cos = dot(rayDir, planeNormal);\nfloat t = dot(planeOrigin, planeNormal) / cos;\nintersection = t * rayDir;\nreturn abs(cos) > 0.001 && t > 0.0;\n}`),N.uniforms.add(new w(\"perScreenPixelRatio\",((e,r)=>r.camera.perScreenPixelRatio))),N.code.add(y`\n      vec4 toFront(vec4 displacedPosScreen, vec3 posLeft, vec3 posRight, vec3 prev, float lineWidth) {\n        // Project displaced position back to camera space\n        vec3 displacedPos = inverseProject(displacedPosScreen);\n\n        // Calculate the plane that we want the marker to lie in. Note that this will always be an approximation since ribbon lines are generally\n        // not planar and we do not know the actual position of the displaced prev vertices (they are offset in screen space, too).\n        vec3 planeNormal = normalize(cross(posLeft - posRight, posLeft - prev));\n        vec3 planeOrigin = posLeft;\n\n        ${C.hasCap?\"\\n                if(prev.z > posLeft.z) {\\n                  vec2 diff = posLeft.xy - posRight.xy;\\n                  planeOrigin.xy += perpendicular(diff) / 2.0;\\n                }\\n              \":\"\"};\n\n        // Move the plane towards the camera by a margin dependent on the line width (approximated in world space). This tolerance corrects for the\n        // non-planarity in most cases, but sharp joins can place the prev vertices at arbitrary positions so markers can still clip.\n        float offset = lineWidth * perScreenPixelRatio;\n        planeOrigin *= (1.0 - offset);\n\n        // Intersect camera ray with the plane and make sure it is within clip space\n        vec3 rayDir = normalize(displacedPos);\n        vec3 intersection;\n        if (rayIntersectPlane(rayDir, planeOrigin, planeNormal, intersection) && intersection.z < -nearFar[0] && intersection.z > -nearFar[1]) {\n          return vec4(intersection.xyz, 1.0);\n        }\n\n        // Fallback: use depth of pos or prev, whichever is closer to the camera\n        float minDepth = planeOrigin.z > prev.z ? length(planeOrigin) : length(prev);\n        displacedPos *= minDepth / length(displacedPos);\n        return vec4(displacedPos.xyz, 1.0);\n      }\n  `)),N.uniforms.add(new w(\"pixelRatio\",((e,r)=>r.camera.pixelRatio))),a(M),N.code.add(y`void main(void) {\nif (uv0.y == 0.0) {\ngl_Position = vec4(1e038, 1e038, 1e038, 1.0);\n}\nelse {\nfloat lineWidth = getLineWidth();\nfloat screenMarkerSize = getScreenMarkerSize();\nvec4 pos  = view * vec4(position.xyz, 1.0);\nvec4 prev = view * vec4(auxpos1.xyz, 1.0);\nclip(pos, prev);`),k?(C.hideOnShortSegments&&N.code.add(y`if (areWorldMarkersHidden(pos, prev)) {\ngl_Position = vec4(1e038, 1e038, 1e038, 1.0);\nreturn;\n}`),N.code.add(y`pos.xyz = displace(pos.xyz, prev.xyz, getWorldMarkerSize(pos));\nvec4 displacedPosScreen = projectAndScale(pos);`)):(N.code.add(y`vec4 posScreen = projectAndScale(pos);\nvec4 prevScreen = projectAndScale(prev);\nvec4 displacedPosScreen = posScreen;\ndisplacedPosScreen.xy = displace(posScreen.xy, prevScreen.xy, screenMarkerSize);`),C.space===L.Screen&&N.code.add(y`vec2 displacementDirU = perpendicular(normalizedSegment(posScreen.xy, prevScreen.xy));\nvec3 lineRight = inverseProject(posScreen + lineWidth * vec4(displacementDirU.xy, 0.0, 0.0));\nvec3 lineLeft = pos.xyz + (pos.xyz - lineRight);\npos = toFront(displacedPosScreen, lineLeft, lineRight, prev.xyz, lineWidth);\ndisplacedPosScreen = projectAndScale(pos);`)),N.code.add(y`\n        ${D?\"depth = pos.z;\":\"\"}\n        linearDepth = calculateLinearDepth(nearFar,pos.z);\n\n        // Convert back into NDC\n        displacedPosScreen.xy = (displacedPosScreen.xy / viewport.zw) * displacedPosScreen.w;\n\n        // Convert texture coordinate into [0,1]\n        vUV = (uv0 + 1.0) / 2.0;\n\n        ${k?\"\":\"vUV *= displacedPosScreen.w;\"}\n\n        ${C.hasTip?\"vLineWidth = lineWidth;\":\"\"}\n\n        vSize = screenMarkerSize;\n        vColor = getColor();\n\n        // Use camera space for slicing\n        vpos = pos.xyz;\n\n        gl_Position = displacedPosScreen;\n      }\n    }\n  `),D&&M.include(d,C),M.include(n,C),T.uniforms.add([new u(\"intrinsicColor\",(e=>e.color)),new x(\"tex\",(e=>e.texture))]),T.include(v),M.constants.add(\"texelSize\",\"float\",1/e),T.code.add(y`float markerAlpha(vec2 samplePos) {\nsamplePos += vec2(0.5, -0.5) * texelSize;\nfloat sdf = rgba2float(texture2D(tex, samplePos)) - 0.5;\nfloat distance = sdf * vSize;\ndistance -= 0.5;\nreturn clamp(0.5 - distance, 0.0, 1.0);\n}`),C.hasTip&&(M.constants.add(\"relativeMarkerSize\",\"float\",r/e),M.constants.add(\"relativeTipLineWidth\",\"float\",o),T.code.add(y`\n    float tipAlpha(vec2 samplePos) {\n      // Convert coordinates s.t. they are in pixels and relative to the tip of an arrow marker\n      samplePos -= vec2(0.5, 0.5 + 0.5 * relativeMarkerSize);\n      samplePos *= vSize;\n\n      float halfMarkerSize = 0.5 * relativeMarkerSize * vSize;\n      float halfTipLineWidth = 0.5 * max(1.0, relativeTipLineWidth * vLineWidth);\n\n      ${k?\"halfTipLineWidth *= fwidth(samplePos.y);\":\"\"}\n\n      float distance = max(abs(samplePos.x) - halfMarkerSize, abs(samplePos.y) - halfTipLineWidth);\n      return clamp(0.5 - distance, 0.0, 1.0);\n    }\n  `)),M.constants.add(\"symbolAlphaCutoff\",\"float\",p),T.code.add(y`\n  void main() {\n    discardBySlice(vpos);\n    ${D?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n\n    vec4 finalColor = intrinsicColor * vColor;\n\n    ${k?\"vec2 samplePos = vUV;\":\"vec2 samplePos = vUV * gl_FragCoord.w;\"}\n\n    ${C.hasTip?\"finalColor.a *= max(markerAlpha(samplePos), tipAlpha(samplePos));\":\"finalColor.a *= markerAlpha(samplePos);\"}\n\n    ${C.output===t.ObjectAndLayerIdColor?y`finalColor.a = 1.0;`:\"\"}\n\n    if (finalColor.a < symbolAlphaCutoff) {\n      discard;\n    }\n\n    ${C.output===t.Alpha?y`gl_FragColor = vec4(finalColor.a);`:\"\"}\n    ${C.output===t.Color?y`gl_FragColor = highlightSlice(finalColor, vpos);`:\"\"}\n    ${C.output===t.Color&&C.transparencyPassType===P.Color?\"gl_FragColor = premultiplyAlpha(gl_FragColor);\":\"\"}\n    ${C.output===t.Highlight?y`gl_FragColor = vec4(1.0);`:\"\"}\n    ${C.output===t.Depth?y`outputDepth(linearDepth);`:\"\"}\n  }\n  `),M}const M=Object.freeze(Object.defineProperty({__proto__:null,build:C},Symbol.toStringTag,{value:\"Module\"}));export{M as L,C as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIixE,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAE,IAAEF,GAAE,wBAAsBA,GAAE,WAAS,EAAE,SAAOA,GAAE,WAAS,EAAE,QAAO,IAAEA,GAAE,UAAQ,EAAE;AAAM,EAAAA,GAAE,UAAQ,KAAGC,GAAE,WAAW,IAAI,6BAA6B,GAAEA,GAAE,QAAQE,IAAEH,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEA,GAAE,WAAS,EAAE,SAAOC,GAAE,QAAQC,IAAEF,EAAC;AAAE,QAAK,EAAC,QAAO,GAAE,UAAS,EAAC,IAAEC;AAAE,SAAO,EAAE,QAAQ,CAAC,GAAE,EAAE,GAAED,EAAC,GAAEC,GAAE,WAAW,IAAI,EAAE,UAAS,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,KAAI,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,SAAQ,MAAM,GAAEA,GAAE,SAAS,IAAI,UAAS,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAEA,GAAE,SAAS,IAAI,OAAM,MAAM,GAAEA,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAEG,GAAEH,EAAC,GAAE,KAAGA,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAED,GAAE,UAAQC,GAAE,SAAS,IAAI,cAAa,OAAO,GAAE,EAAE,SAAS,IAAI,CAAC,IAAII,GAAE,WAAW,CAACA,IAAE,MAAI,EAAE,OAAO,OAAQ,GAAE,IAAI,EAAE,YAAY,CAACA,IAAE,MAAI,EAAE,OAAO,YAAa,CAAC,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,EAIx+F,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,GAAE,KAAGJ,GAAE,WAAW,IAAI,EAAE,QAAO,MAAM,GAAE,EAAE,CAAC,GAAE,EAAE,UAAU,IAAI,iBAAgB,SAAQ,GAAE,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnG,KAAG,EAAE,KAAK,IAAI;AAAA;AAAA,EAEd,GAAE,EAAE,KAAK,IAAI;AAAA,qBACM,IAAE,SAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAO2B,IAAE,wBAAsB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAS7FD,GAAE,WAAS,EAAE,MAAI,qDAAmD,EAAE;AAAA;AAAA;AAAA;AAAA,KAI3E,GAAEA,GAAE,UAAQ,EAAE,WAAS,EAAE,SAAS,IAAI,IAAIK,GAAE,2BAA2B,CAACA,IAAE,MAAI,EAAE,OAAO,uBAAwB,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAGhI,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,GAAE,EAAE,SAAS,IAAI,IAAIH,GAAE,uBAAuB,CAACG,IAAE,MAAI,EAAE,OAAO,mBAAoB,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAUxFL,GAAE,SAAO,2MAAyM,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAmB3N,IAAG,EAAE,SAAS,IAAI,IAAIE,GAAE,cAAc,CAACG,IAAE,MAAI,EAAE,OAAO,UAAW,CAAC,GAAE,EAAEJ,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAStE,GAAE,KAAGD,GAAE,uBAAqB,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAGtD,GAAE,EAAE,KAAK,IAAI;AAAA,gDACiC,MAAI,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,iFAGkB,GAAEA,GAAE,UAAQ,EAAE,UAAQ,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,2CAIvE,IAAG,EAAE,KAAK,IAAI;AAAA,UAC/C,IAAE,mBAAiB,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASrB,IAAE,KAAG,8BAA8B;AAAA;AAAA,UAEnCA,GAAE,SAAO,4BAA0B,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAW5C,GAAE,KAAGC,GAAE,QAAQK,IAAEN,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,kBAAkB,CAAAK,OAAGA,GAAE,KAAM,GAAE,IAAI,EAAE,OAAO,CAAAA,OAAGA,GAAE,OAAQ,CAAC,CAAC,GAAE,EAAE,QAAQA,EAAC,GAAEJ,GAAE,UAAU,IAAI,aAAY,SAAQ,IAAEG,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxL,GAAEJ,GAAE,WAASC,GAAE,UAAU,IAAI,sBAAqB,SAAQC,KAAEE,EAAC,GAAEH,GAAE,UAAU,IAAI,wBAAuB,SAAQI,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAStH,IAAE,6CAA2C,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,GAKpD,IAAGJ,GAAE,UAAU,IAAI,qBAAoB,SAAQ,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,MAG1D,IAAE,2CAAyC,EAAE;AAAA;AAAA;AAAA;AAAA,MAI7C,IAAE,0BAAwB,wCAAwC;AAAA;AAAA,MAElED,GAAE,SAAO,sEAAoE,yCAAyC;AAAA;AAAA,MAEtHA,GAAE,WAAS,EAAE,wBAAsB,yBAAuB,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM5DA,GAAE,WAAS,EAAE,QAAM,wCAAsC,EAAE;AAAA,MAC3DA,GAAE,WAAS,EAAE,QAAM,sDAAoD,EAAE;AAAA,MACzEA,GAAE,WAAS,EAAE,SAAOA,GAAE,yBAAuBE,GAAE,QAAM,mDAAiD,EAAE;AAAA,MACxGF,GAAE,WAAS,EAAE,YAAU,+BAA6B,EAAE;AAAA,MACtDA,GAAE,WAAS,EAAE,QAAM,+BAA6B,EAAE;AAAA;AAAA,GAErD,GAAEC;AAAC;AAAC,IAAM,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAM,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["C", "M", "o", "s", "t", "e", "n"]}