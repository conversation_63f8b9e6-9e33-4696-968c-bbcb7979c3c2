import {
  T
} from "./chunk-N7ADFPOO.js";
import {
  n
} from "./chunk-7THWOTCY.js";
import {
  v as v2
} from "./chunk-ZACBBT3Y.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  v
} from "./chunk-HP475EI3.js";

// node_modules/@arcgis/core/rest/support/IdentifyParameters.js
var m;
var a2 = m = class extends l {
  static from(t) {
    return v(m, t);
  }
  constructor(t) {
    super(t), this.dpi = 96, this.floors = null, this.gdbVersion = null, this.geometry = null, this.geometryPrecision = null, this.height = 400, this.layerIds = null, this.layerOption = "top", this.mapExtent = null, this.maxAllowableOffset = null, this.returnFieldName = true, this.returnGeometry = false, this.returnM = false, this.returnUnformattedValues = true, this.returnZ = false, this.spatialReference = null, this.sublayers = null, this.timeExtent = null, this.tolerance = null, this.width = 400;
  }
};
e([y({ type: Number, json: { write: true } })], a2.prototype, "dpi", void 0), e([y()], a2.prototype, "floors", void 0), e([y({ type: String, json: { write: true } })], a2.prototype, "gdbVersion", void 0), e([y({ types: n, json: { read: v2, write: true } })], a2.prototype, "geometry", void 0), e([y({ type: Number, json: { write: true } })], a2.prototype, "geometryPrecision", void 0), e([y({ type: Number, json: { write: true } })], a2.prototype, "height", void 0), e([y({ type: [Number], json: { write: true } })], a2.prototype, "layerIds", void 0), e([y({ type: ["top", "visible", "all", "popup"], json: { write: true } })], a2.prototype, "layerOption", void 0), e([y({ type: w, json: { write: true } })], a2.prototype, "mapExtent", void 0), e([y({ type: Number, json: { write: true } })], a2.prototype, "maxAllowableOffset", void 0), e([y({ type: Boolean, json: { write: true } })], a2.prototype, "returnFieldName", void 0), e([y({ type: Boolean, json: { write: true } })], a2.prototype, "returnGeometry", void 0), e([y({ type: Boolean, json: { write: true } })], a2.prototype, "returnM", void 0), e([y({ type: Boolean, json: { write: true } })], a2.prototype, "returnUnformattedValues", void 0), e([y({ type: Boolean, json: { write: true } })], a2.prototype, "returnZ", void 0), e([y({ type: f, json: { write: true } })], a2.prototype, "spatialReference", void 0), e([y()], a2.prototype, "sublayers", void 0), e([y({ type: T, json: { write: true } })], a2.prototype, "timeExtent", void 0), e([y({ type: Number, json: { write: true } })], a2.prototype, "tolerance", void 0), e([y({ type: Number, json: { write: true } })], a2.prototype, "width", void 0), a2 = m = e([a("esri.rest.support.IdentifyParameters")], a2);
var u = a2;

export {
  u
};
//# sourceMappingURL=chunk-SIWJOTKY.js.map
