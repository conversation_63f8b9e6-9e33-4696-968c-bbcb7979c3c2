import {
  c2 as c,
  c3 as c2,
  i,
  k,
  u
} from "./chunk-MQ2IOGEF.js";
import {
  <PERSON>,
  he
} from "./chunk-VNYCO3JG.js";

// node_modules/@arcgis/core/support/popupUtils.js
var l = ["oid", "global-id"];
var a = ["oid", "global-id", "guid"];
function p({ displayField: t, editFieldsInfo: n, fields: i2, objectIdField: o, title: r }, s) {
  if (!i2) return null;
  const l2 = g({ editFieldsInfo: n, fields: i2, objectIdField: o }, s);
  if (!l2.length) return null;
  const a2 = L({ titleBase: r, fields: i2, displayField: t }), p2 = I();
  return new k({ title: a2, content: p2, fieldInfos: l2 });
}
var u2 = [/^fnode_$/i, /^tnode_$/i, /^lpoly_$/i, /^rpoly_$/i, /^poly_$/i, /^subclass$/i, /^subclass_$/i, /^rings_ok$/i, /^rings_nok$/i, /shape/i, /perimeter/i, /objectid/i, /_i$/i];
var c3 = (e, { editFieldsInfo: t, objectIdField: n, visibleFieldNames: i2 }) => i2 ? i2.has(e.name) : !F(e.name, t) && ((!n || e.name !== n) && (!l.includes(e.type) && !u2.some((t2) => t2.test(e.name))));
function f(e, t) {
  const n = e;
  return t && (e = e.filter((e2) => !t.includes(e2.type))), e === n && (e = e.slice()), e.sort(m), e;
}
function m(e, t) {
  return "oid" === e.type ? -1 : "oid" === t.type ? 1 : _(e) ? -1 : _(t) ? 1 : (e.alias || e.name).toLocaleLowerCase().localeCompare((t.alias || t.name).toLocaleLowerCase());
}
function F(e, t) {
  if (!e || !t) return false;
  const { creationDateField: n, creatorField: i2, editDateField: o, editorField: r } = t;
  return [n && n.toLowerCase(), i2 && i2.toLowerCase(), o && o.toLowerCase(), r && r.toLowerCase()].includes(e.toLowerCase());
}
function b(e, t) {
  return e.editable && !a.includes(e.type) && !F(e.name, t);
}
function g({ editFieldsInfo: e, fields: t, objectIdField: n }, i2) {
  return f(t ?? [], (i2 == null ? void 0 : i2.ignoreFieldTypes) || h).map((t2) => new c({ fieldName: t2.name, isEditable: b(t2, e), label: t2.alias, format: j(t2), visible: c3(t2, { editFieldsInfo: e, objectIdField: n, visibleFieldNames: i2 == null ? void 0 : i2.visibleFieldNames }) }));
}
function j(e) {
  switch (e.type) {
    case "small-integer":
    case "integer":
    case "single":
      return new u({ digitSeparator: true, places: 0 });
    case "double":
      return new u({ digitSeparator: true, places: 2 });
    case "date":
      return new u({ dateFormat: "long-month-day-year" });
    default:
      return "string" === e.type && he(e.name) ? new u({ digitSeparator: true, places: 0 }) : null;
  }
}
function I() {
  return [new c2(), new i()];
}
function L(e) {
  const t = E(e), { titleBase: i2 } = e;
  return t ? `${i2}: {${t.trim()}}` : i2 ?? "";
}
function _(e) {
  if ("name" === (e.name && e.name.toLowerCase())) return true;
  return "name" === (e.alias && e.alias.toLowerCase());
}
var h = ["geometry", "blob", "raster", "guid", "xml"];

export {
  p
};
//# sourceMappingURL=chunk-JZKMTUDN.js.map
