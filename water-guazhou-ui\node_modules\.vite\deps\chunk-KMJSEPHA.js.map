{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/ArcGISCachedService.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import\"../../geometry.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as o}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";import{serviceTileInfoProperty as i}from\"../support/serviceTileInfoProperty.js\";import{TilemapCache as p}from\"../support/TilemapCache.js\";import a from\"../../geometry/SpatialReference.js\";const s=s=>{let l=class extends s{constructor(){super(...arguments),this.copyright=null,this.minScale=0,this.maxScale=0,this.spatialReference=null,this.tileInfo=null,this.tilemapCache=null}readMinScale(e,r){return null!=r.minLOD&&null!=r.maxLOD?e:0}readMaxScale(e,r){return null!=r.minLOD&&null!=r.maxLOD?e:0}get supportsBlankTile(){return this.version>=10.2}readTilemapCache(e,r){return r.capabilities&&r.capabilities.includes(\"Tilemap\")?new p({layer:this}):null}};return e([r({json:{read:{source:\"copyrightText\"}}})],l.prototype,\"copyright\",void 0),e([r()],l.prototype,\"minScale\",void 0),e([o(\"service\",\"minScale\")],l.prototype,\"readMinScale\",null),e([r()],l.prototype,\"maxScale\",void 0),e([o(\"service\",\"maxScale\")],l.prototype,\"readMaxScale\",null),e([r({type:a})],l.prototype,\"spatialReference\",void 0),e([r({readOnly:!0})],l.prototype,\"supportsBlankTile\",null),e([r(i)],l.prototype,\"tileInfo\",void 0),e([r()],l.prototype,\"tilemapCache\",void 0),e([o(\"service\",\"tilemapCache\",[\"capabilities\"])],l.prototype,\"readTilemapCache\",null),e([r()],l.prototype,\"version\",void 0),l=e([t(\"esri.layers.mixins.ArcGISCachedService\")],l),l};export{s as ArcGISCachedService};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIyjB,IAAM,IAAE,CAAAA,OAAG;AAAC,MAAI,IAAE,cAAcA,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,YAAU,MAAK,KAAK,WAAS,GAAE,KAAK,WAAS,GAAE,KAAK,mBAAiB,MAAK,KAAK,WAAS,MAAK,KAAK,eAAa;AAAA,IAAI;AAAA,IAAC,aAAaC,IAAEC,IAAE;AAAC,aAAO,QAAMA,GAAE,UAAQ,QAAMA,GAAE,SAAOD,KAAE;AAAA,IAAC;AAAA,IAAC,aAAaA,IAAEC,IAAE;AAAC,aAAO,QAAMA,GAAE,UAAQ,QAAMA,GAAE,SAAOD,KAAE;AAAA,IAAC;AAAA,IAAC,IAAI,oBAAmB;AAAC,aAAO,KAAK,WAAS;AAAA,IAAI;AAAA,IAAC,iBAAiBA,IAAEC,IAAE;AAAC,aAAOA,GAAE,gBAAcA,GAAE,aAAa,SAAS,SAAS,IAAE,IAAI,EAAE,EAAC,OAAM,KAAI,CAAC,IAAE;AAAA,IAAI;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,gBAAe,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,UAAU,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,UAAU,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,gBAAe,CAAC,cAAc,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,wCAAwC,CAAC,GAAE,CAAC,GAAE;AAAC;", "names": ["s", "e", "r"]}