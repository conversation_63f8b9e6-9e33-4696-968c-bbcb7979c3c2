{"version": 3, "sources": ["../../@arcgis/core/renderers/support/DictionaryLoader.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../Color.js\";import t from\"../../request.js\";import s from\"../../core/Error.js\";import o from\"../../core/Logger.js\";import i from\"../../core/LRUCache.js\";import{isSome as r,isNone as n}from\"../../core/maybe.js\";import{isAbortError as l}from\"../../core/promiseUtils.js\";import{numericHash as a}from\"../../core/string.js\";import{loadArcade as c,createDictionaryExpression as m}from\"../../support/arcadeOnDemand.js\";import f from\"../../symbols/CIMSymbol.js\";const h=\"esri.renderers.support.DictionaryLoader\",y={type:\"CIMSimpleLineCallout\",lineSymbol:{type:\"CIMLineSymbol\",symbolLayers:[{type:\"CIMSolidStroke\",width:.5,color:[0,0,0,255]}]}};class u{constructor(e,t,s){this.config=null,this.fieldMap=null,this.url=null,this._ongoingRequests=new Map,this._symbolCache=new i(100),this._dictionaryPromise=null,this.url=e,this.config=t,this.fieldMap=s}getSymbolFields(){return this._symbolFields}async getSymbolAsync(t,s){let o;this._dictionaryPromise||(this._dictionaryPromise=this.fetchResources(s));try{o=await this._dictionaryPromise}catch(g){if(l(g))return this._dictionaryPromise=null,null}const i={};if(this.fieldMap)for(const e of this._symbolFields){const s=this.fieldMap[e];if(s&&null!=t.attributes[s]){const o=\"\"+t.attributes[s];i[e]=o}else i[e]=\"\"}const n=o?.(i,s);if(!n||\"string\"!=typeof n)return null;const c=a(n).toString(),m=this._symbolCache.get(c);if(m)return m.catch((()=>{this._symbolCache.pop(c)})),m;const f=n.split(\";\"),h=[],y=[];for(const r of f)if(r)if(r.includes(\"po:\")){const t=r.substr(3).split(\"|\");if(3===t.length){const s=t[0],o=t[1];let i=t[2];if(\"DashTemplate\"===o)i=i.split(\" \").map((e=>Number(e)));else if(\"Color\"===o){const t=new e(i).toRgba();i=[t[0],t[1],t[2],255*t[3]]}else i=Number(i);y.push({primitiveName:s,propertyName:o,value:i})}}else if(r.includes(\"|\")){for(const e of r.split(\"|\"))if(this._itemNames.has(e)){h.push(e);break}}else this._itemNames.has(r)&&h.push(r);const u=!r(t.geometry)||!t.geometry.hasZ&&\"point\"===t.geometry.type,p=this._cimPartsToCIMSymbol(h,y,u,s);return this._symbolCache.put(c,p,1),p}async fetchResources(e){if(this._dictionaryPromise)return this._dictionaryPromise;if(!this.url)return void o.getLogger(h).error(\"no valid URL!\");const i=t(this.url+\"/resources/styles/dictionary-info.json\",{responseType:\"json\",query:{f:\"json\"},signal:r(e)?e.signal:null}),[{data:l}]=await Promise.all([i,c()]);if(!l)throw this._dictionaryPromise=null,new s(\"esri.renderers.DictionaryRenderer\",\"Bad dictionary data!\");const a=l.expression,f=l.authoringInfo;this._refSymbolUrlTemplate=this.url+\"/\"+l.cimRefTemplateUrl,this._itemNames=new Set(l.itemsNames),this._symbolFields=f.symbol;const y={};if(this.config){const e=this.config;for(const t in e)y[t]=e[t]}if(f.configuration)for(const t of f.configuration)y.hasOwnProperty(t.name)||(y[t.name]=t.value);const u=[];if(r(e)&&e.fields&&this.fieldMap)for(const t of this._symbolFields){const s=this.fieldMap[t],o=e.fields.filter((e=>e.name===s));o.length>0&&u.push({...o[0],name:t})}const p=m(a,r(e)?e.spatialReference:null,u,y).then((e=>{const t={scale:0};return(s,o)=>{if(n(e))return null;const i=e.repurposeFeature({geometry:null,attributes:s});return t.scale=r(o)?o.scale??void 0:void 0,e.evaluate({$feature:i,$view:t})}})).catch((e=>(o.getLogger(h).error(\"Creating dictinoary expression failed:\",e),null)));return this._dictionaryPromise=p,p}async _cimPartsToCIMSymbol(e,t,s,o){const i=new Array(e.length);for(let l=0;l<e.length;l++)i[l]=this._getSymbolPart(e[l],o);const r=await Promise.all(i),n=this.fieldMap;if(n)for(const l of r)p(l,n);return new f({data:this._combineSymbolParts(r,t,s)})}async _getSymbolPart(e,s){if(this._ongoingRequests.has(e))return this._ongoingRequests.get(e).then((e=>e.data));const o=this._refSymbolUrlTemplate.replace(/\\{itemName\\}/gi,e),i=t(o,{responseType:\"json\",query:{f:\"json\"},...s});this._ongoingRequests.set(e,i);try{return(await i).data}catch(r){throw this._ongoingRequests.delete(e),r}}_combineSymbolParts(e,t,s){if(!e||0===e.length)return null;const o={...e[0]};if(e.length>1){o.symbolLayers=[];for(const t of e){const e=t;o.symbolLayers.unshift(...e.symbolLayers)}}return s&&(o.callout=y),{type:\"CIMSymbolReference\",symbol:o,primitiveOverrides:t}}}function p(e,t){if(!e)return;const s=e.symbolLayers;if(!s)return;let o=s.length;for(;o--;){const e=s[o];if(e&&!1!==e.enable&&\"CIMVectorMarker\"===e.type)g(e,t)}}function g(e,t){const s=e.markerGraphics;if(s)for(const o of s){if(!o)continue;const e=o.symbol;if(e)switch(e.type){case\"CIMPointSymbol\":case\"CIMLineSymbol\":case\"CIMPolygonSymbol\":p(e,t);break;case\"CIMTextSymbol\":e.fieldMap=t}}}export{u as DictionaryLoader};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIwd,IAAM,IAAE;AAAR,IAAkD,IAAE,EAAC,MAAK,wBAAuB,YAAW,EAAC,MAAK,iBAAgB,cAAa,CAAC,EAAC,MAAK,kBAAiB,OAAM,KAAG,OAAM,CAAC,GAAE,GAAE,GAAE,GAAG,EAAC,CAAC,EAAC,EAAC;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,SAAK,SAAO,MAAK,KAAK,WAAS,MAAK,KAAK,MAAI,MAAK,KAAK,mBAAiB,oBAAI,OAAI,KAAK,eAAa,IAAI,EAAE,GAAG,GAAE,KAAK,qBAAmB,MAAK,KAAK,MAAIF,IAAE,KAAK,SAAOC,IAAE,KAAK,WAASC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,MAAM,eAAeD,IAAEC,IAAE;AAAC,QAAI;AAAE,SAAK,uBAAqB,KAAK,qBAAmB,KAAK,eAAeA,EAAC;AAAG,QAAG;AAAC,UAAE,MAAM,KAAK;AAAA,IAAkB,SAAOC,IAAE;AAAC,UAAG,EAAEA,EAAC,EAAE,QAAO,KAAK,qBAAmB,MAAK;AAAA,IAAI;AAAC,UAAMC,KAAE,CAAC;AAAE,QAAG,KAAK,SAAS,YAAUJ,MAAK,KAAK,eAAc;AAAC,YAAME,KAAE,KAAK,SAASF,EAAC;AAAE,UAAGE,MAAG,QAAMD,GAAE,WAAWC,EAAC,GAAE;AAAC,cAAMG,KAAE,KAAGJ,GAAE,WAAWC,EAAC;AAAE,QAAAE,GAAEJ,EAAC,IAAEK;AAAA,MAAC,MAAM,CAAAD,GAAEJ,EAAC,IAAE;AAAA,IAAE;AAAC,UAAM,IAAE,uBAAII,IAAEF;AAAG,QAAG,CAAC,KAAG,YAAU,OAAO,EAAE,QAAO;AAAK,UAAMI,KAAE,EAAE,CAAC,EAAE,SAAS,GAAE,IAAE,KAAK,aAAa,IAAIA,EAAC;AAAE,QAAG,EAAE,QAAO,EAAE,MAAO,MAAI;AAAC,WAAK,aAAa,IAAIA,EAAC;AAAA,IAAC,CAAE,GAAE;AAAE,UAAM,IAAE,EAAE,MAAM,GAAG,GAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,eAAUC,MAAK,EAAE,KAAGA,GAAE,KAAGA,GAAE,SAAS,KAAK,GAAE;AAAC,YAAMR,KAAEQ,GAAE,OAAO,CAAC,EAAE,MAAM,GAAG;AAAE,UAAG,MAAIR,GAAE,QAAO;AAAC,cAAMC,KAAED,GAAE,CAAC,GAAEI,KAAEJ,GAAE,CAAC;AAAE,YAAIG,KAAEH,GAAE,CAAC;AAAE,YAAG,mBAAiBI,GAAE,CAAAD,KAAEA,GAAE,MAAM,GAAG,EAAE,IAAK,CAAAJ,OAAG,OAAOA,EAAC,CAAE;AAAA,iBAAU,YAAUK,IAAE;AAAC,gBAAMJ,KAAE,IAAI,EAAEG,EAAC,EAAE,OAAO;AAAE,UAAAA,KAAE,CAACH,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,MAAIA,GAAE,CAAC,CAAC;AAAA,QAAC,MAAM,CAAAG,KAAE,OAAOA,EAAC;AAAE,QAAAI,GAAE,KAAK,EAAC,eAAcN,IAAE,cAAaG,IAAE,OAAMD,GAAC,CAAC;AAAA,MAAC;AAAA,IAAC,WAASK,GAAE,SAAS,GAAG,GAAE;AAAC,iBAAUT,MAAKS,GAAE,MAAM,GAAG,EAAE,KAAG,KAAK,WAAW,IAAIT,EAAC,GAAE;AAAC,QAAAO,GAAE,KAAKP,EAAC;AAAE;AAAA,MAAK;AAAA,IAAC,MAAM,MAAK,WAAW,IAAIS,EAAC,KAAGF,GAAE,KAAKE,EAAC;AAAE,UAAMC,KAAE,CAAC,EAAET,GAAE,QAAQ,KAAG,CAACA,GAAE,SAAS,QAAM,YAAUA,GAAE,SAAS,MAAKU,KAAE,KAAK,qBAAqBJ,IAAEC,IAAEE,IAAER,EAAC;AAAE,WAAO,KAAK,aAAa,IAAII,IAAEK,IAAE,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeX,IAAE;AAAC,QAAG,KAAK,mBAAmB,QAAO,KAAK;AAAmB,QAAG,CAAC,KAAK,IAAI,QAAO,KAAK,EAAE,UAAU,CAAC,EAAE,MAAM,eAAe;AAAE,UAAMI,KAAE,EAAE,KAAK,MAAI,0CAAyC,EAAC,cAAa,QAAO,OAAM,EAAC,GAAE,OAAM,GAAE,QAAO,EAAEJ,EAAC,IAAEA,GAAE,SAAO,KAAI,CAAC,GAAE,CAAC,EAAC,MAAKY,GAAC,CAAC,IAAE,MAAM,QAAQ,IAAI,CAACR,IAAE,EAAE,CAAC,CAAC;AAAE,QAAG,CAACQ,GAAE,OAAM,KAAK,qBAAmB,MAAK,IAAIV,GAAE,qCAAoC,sBAAsB;AAAE,UAAM,IAAEU,GAAE,YAAW,IAAEA,GAAE;AAAc,SAAK,wBAAsB,KAAK,MAAI,MAAIA,GAAE,mBAAkB,KAAK,aAAW,IAAI,IAAIA,GAAE,UAAU,GAAE,KAAK,gBAAc,EAAE;AAAO,UAAMJ,KAAE,CAAC;AAAE,QAAG,KAAK,QAAO;AAAC,YAAMR,KAAE,KAAK;AAAO,iBAAUC,MAAKD,GAAE,CAAAQ,GAAEP,EAAC,IAAED,GAAEC,EAAC;AAAA,IAAC;AAAC,QAAG,EAAE,cAAc,YAAUA,MAAK,EAAE,cAAc,CAAAO,GAAE,eAAeP,GAAE,IAAI,MAAIO,GAAEP,GAAE,IAAI,IAAEA,GAAE;AAAO,UAAMS,KAAE,CAAC;AAAE,QAAG,EAAEV,EAAC,KAAGA,GAAE,UAAQ,KAAK,SAAS,YAAUC,MAAK,KAAK,eAAc;AAAC,YAAMC,KAAE,KAAK,SAASD,EAAC,GAAE,IAAED,GAAE,OAAO,OAAQ,CAAAA,OAAGA,GAAE,SAAOE,EAAE;AAAE,QAAE,SAAO,KAAGQ,GAAE,KAAK,EAAC,GAAG,EAAE,CAAC,GAAE,MAAKT,GAAC,CAAC;AAAA,IAAC;AAAC,UAAMU,KAAE,EAAE,GAAE,EAAEX,EAAC,IAAEA,GAAE,mBAAiB,MAAKU,IAAEF,EAAC,EAAE,KAAM,CAAAR,OAAG;AAAC,YAAMC,KAAE,EAAC,OAAM,EAAC;AAAE,aAAM,CAACC,IAAE,MAAI;AAAC,YAAG,EAAEF,EAAC,EAAE,QAAO;AAAK,cAAMI,KAAEJ,GAAE,iBAAiB,EAAC,UAAS,MAAK,YAAWE,GAAC,CAAC;AAAE,eAAOD,GAAE,QAAM,EAAE,CAAC,IAAE,EAAE,SAAO,SAAO,QAAOD,GAAE,SAAS,EAAC,UAASI,IAAE,OAAMH,GAAC,CAAC;AAAA,MAAC;AAAA,IAAC,CAAE,EAAE,MAAO,CAAAD,QAAI,EAAE,UAAU,CAAC,EAAE,MAAM,0CAAyCA,EAAC,GAAE,KAAM;AAAE,WAAO,KAAK,qBAAmBW,IAAEA;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBX,IAAEC,IAAEC,IAAE,GAAE;AAAC,UAAME,KAAE,IAAI,MAAMJ,GAAE,MAAM;AAAE,aAAQY,KAAE,GAAEA,KAAEZ,GAAE,QAAOY,KAAI,CAAAR,GAAEQ,EAAC,IAAE,KAAK,eAAeZ,GAAEY,EAAC,GAAE,CAAC;AAAE,UAAMH,KAAE,MAAM,QAAQ,IAAIL,EAAC,GAAE,IAAE,KAAK;AAAS,QAAG,EAAE,YAAUQ,MAAKH,GAAE,CAAAE,GAAEC,IAAE,CAAC;AAAE,WAAO,IAAI,EAAE,EAAC,MAAK,KAAK,oBAAoBH,IAAER,IAAEC,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeF,IAAEE,IAAE;AAAC,QAAG,KAAK,iBAAiB,IAAIF,EAAC,EAAE,QAAO,KAAK,iBAAiB,IAAIA,EAAC,EAAE,KAAM,CAAAA,OAAGA,GAAE,IAAK;AAAE,UAAM,IAAE,KAAK,sBAAsB,QAAQ,kBAAiBA,EAAC,GAAEI,KAAE,EAAE,GAAE,EAAC,cAAa,QAAO,OAAM,EAAC,GAAE,OAAM,GAAE,GAAGF,GAAC,CAAC;AAAE,SAAK,iBAAiB,IAAIF,IAAEI,EAAC;AAAE,QAAG;AAAC,cAAO,MAAMA,IAAG;AAAA,IAAI,SAAOK,IAAE;AAAC,YAAM,KAAK,iBAAiB,OAAOT,EAAC,GAAES;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBT,IAAEC,IAAEC,IAAE;AAAC,QAAG,CAACF,MAAG,MAAIA,GAAE,OAAO,QAAO;AAAK,UAAM,IAAE,EAAC,GAAGA,GAAE,CAAC,EAAC;AAAE,QAAGA,GAAE,SAAO,GAAE;AAAC,QAAE,eAAa,CAAC;AAAE,iBAAUC,MAAKD,IAAE;AAAC,cAAMA,KAAEC;AAAE,UAAE,aAAa,QAAQ,GAAGD,GAAE,YAAY;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOE,OAAI,EAAE,UAAQ,IAAG,EAAC,MAAK,sBAAqB,QAAO,GAAE,oBAAmBD,GAAC;AAAA,EAAC;AAAC;AAAC,SAASU,GAAEX,IAAEC,IAAE;AAAC,MAAG,CAACD,GAAE;AAAO,QAAME,KAAEF,GAAE;AAAa,MAAG,CAACE,GAAE;AAAO,MAAI,IAAEA,GAAE;AAAO,SAAK,OAAK;AAAC,UAAMF,KAAEE,GAAE,CAAC;AAAE,QAAGF,MAAG,UAAKA,GAAE,UAAQ,sBAAoBA,GAAE,KAAK,GAAEA,IAAEC,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,QAAMC,KAAEF,GAAE;AAAe,MAAGE,GAAE,YAAU,KAAKA,IAAE;AAAC,QAAG,CAAC,EAAE;AAAS,UAAMF,KAAE,EAAE;AAAO,QAAGA,GAAE,SAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAiB,KAAI;AAAA,MAAgB,KAAI;AAAmB,QAAAW,GAAEX,IAAEC,EAAC;AAAE;AAAA,MAAM,KAAI;AAAgB,QAAAD,GAAE,WAASC;AAAA,IAAC;AAAA,EAAC;AAAC;", "names": ["e", "t", "s", "g", "i", "o", "c", "h", "y", "r", "u", "p", "l"]}