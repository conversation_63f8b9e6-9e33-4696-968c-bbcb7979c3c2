import {
  e
} from "./chunk-JK6A4R7D.js";
import {
  r,
  t
} from "./chunk-WIKVTG73.js";
import {
  t as t2
} from "./chunk-NNKS4NNY.js";
import {
  m
} from "./chunk-HTXGAKOK.js";
import {
  s as s2
} from "./chunk-55WN4LCX.js";
import {
  x
} from "./chunk-6NE6A2GD.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import {
  b as b2
} from "./chunk-QMNV7QQK.js";
import {
  s
} from "./chunk-4RZONHOY.js";

// node_modules/@arcgis/core/portal/support/layersLoader.js
async function p(e2, t3) {
  const r2 = e2.instance.portalItem;
  if (r2 && r2.id) return await r2.load(t3), c(e2), y(e2, t3);
}
function c(t3) {
  const r2 = t3.instance.portalItem;
  if (!(r2 == null ? void 0 : r2.type) || !t3.supportedTypes.includes(r2.type)) throw new s("portal:invalid-layer-item-type", "Invalid layer item type '${type}', expected '${expectedType}'", { type: r2 == null ? void 0 : r2.type, expectedType: t3.supportedTypes.join(", ") });
}
async function y(e2, t3) {
  const r2 = e2.instance, a = r2.portalItem;
  if (!a) return;
  const { url: n, title: o } = a, s3 = e(a);
  if ("group" === r2.type) return r2.read({ title: o }, s3), d(r2, e2);
  n && r2.read({ url: n }, s3);
  const u = await h(e2, t3);
  return u && r2.read(u, s3), r2.resourceReferences = { portalItem: a, paths: s3.readResourcePaths ?? [] }, "subtype-group" !== r2.type && r2.read({ title: o }, s3), t2(r2, s3);
}
async function d(t3, r2) {
  let a;
  const { portalItem: n } = t3;
  if (!n) return;
  const o = n.type, l = r2.layerModuleTypeMap, i = s2(n, "Oriented Imagery Layer") ?? false;
  switch (o) {
    case "Feature Service":
      a = i ? l.OrientedImageryLayer : l.FeatureLayer;
      break;
    case "Stream Service":
      a = l.StreamLayer;
      break;
    case "Scene Service":
      a = l.SceneLayer;
      break;
    case "Feature Collection":
      a = l.FeatureLayer;
      break;
    default:
      throw new s("portal:unsupported-item-type-as-group", `The item type '${o}' is not supported as a 'IGroupLayer'`);
  }
  let [u, p2] = await Promise.all([a(), h(r2)]), c2 = () => u;
  if ("Feature Service" === o) {
    p2 = n.url ? await w(p2, n.url) : {};
    if (j(p2).length) {
      const e2 = l.SubtypeGroupLayer, t4 = await e2();
      c2 = (e3) => "SubtypeGroupLayer" === e3.layerType ? t4 : u;
    }
    return b3(t3, c2, p2, await P(n.url));
  }
  return v(p2) > 0 ? b3(t3, c2, p2) : f(t3, c2);
}
async function f(e2, t3) {
  var _a, _b;
  const { portalItem: r2 } = e2;
  if (!(r2 == null ? void 0 : r2.url)) return;
  const a = await t(r2.url);
  a && b3(e2, t3, { layers: (_a = a.layers) == null ? void 0 : _a.map(m2), tables: (_b = a.tables) == null ? void 0 : _b.map(m2) });
}
function m2(e2) {
  return { id: e2.id, name: e2.name };
}
function b3(e2, t3, r2, a) {
  var _a;
  let n = r2.layers || [];
  const o = r2.tables || [];
  if ("Feature Collection" === ((_a = e2.portalItem) == null ? void 0 : _a.type) && (n.forEach((e3) => {
    var _a2;
    "Table" === ((_a2 = e3 == null ? void 0 : e3.layerDefinition) == null ? void 0 : _a2.type) && o.push(e3);
  }), n = n.filter((e3) => {
    var _a2;
    return "Table" !== ((_a2 = e3 == null ? void 0 : e3.layerDefinition) == null ? void 0 : _a2.type);
  })), "coverage" in r2) {
    const t4 = T(r2);
    t4 && e2.add(t4);
  }
  n.reverse().forEach((n2) => {
    const o2 = g(e2, t3(n2), r2, n2, a == null ? void 0 : a(n2));
    e2.add(o2);
  }), o.reverse().forEach((n2) => {
    const o2 = g(e2, t3(n2), r2, n2, a == null ? void 0 : a(n2));
    e2.tables.add(o2);
  });
}
function g(e2, t3, r2, a, o) {
  const l = e2.portalItem, s3 = new t3({ portalItem: l.clone(), layerId: a.id });
  if ("sourceJSON" in s3 && (s3.sourceJSON = o), "subtype-group" !== s3.type && (s3.sublayerTitleMode = "service-name"), "Feature Collection" === l.type) {
    const e3 = { origin: "portal-item", portal: l.portal || b2.getDefault() };
    s3.read(a, e3);
    const t4 = r2.showLegend;
    null != t4 && s3.read({ showLegend: t4 }, e3);
  }
  return s3;
}
async function h(e2, t3) {
  if (false === e2.supportsData) return;
  const r2 = e2.instance, a = r2.portalItem;
  if (!a) return;
  let n = null;
  try {
    n = await a.fetchData("json", t3);
  } catch (o) {
  }
  if (S(r2)) {
    let e3 = null, t4 = true;
    if (n && v(n) > 0) {
      if (null == r2.layerId) {
        const e4 = j(n);
        r2.layerId = "subtype-group" === r2.type ? e4 == null ? void 0 : e4[0] : I(n);
      }
      e3 = L(n, r2), e3 && (1 === v(n) && (t4 = false), null != n.showLegend && (e3.showLegend = n.showLegend));
    }
    return t4 && "service-name" !== r2.sublayerTitleMode && (r2.sublayerTitleMode = "item-title-and-service-name"), e3;
  }
  return n;
}
async function w(e2, t3) {
  if (null == (e2 == null ? void 0 : e2.layers) || null == (e2 == null ? void 0 : e2.tables)) {
    const r2 = await t(t3);
    (e2 = e2 || {}).layers = e2.layers || (r2 == null ? void 0 : r2.layers), e2.tables = e2.tables || (r2 == null ? void 0 : r2.tables);
  }
  return e2;
}
function I(e2) {
  const t3 = e2.layers;
  if (t3 && t3.length) return t3[0].id;
  const r2 = e2.tables;
  return r2 && r2.length ? r2[0].id : null;
}
function L(e2, t3) {
  var _a, _b;
  const { layerId: r2 } = t3, a = ((_a = e2.layers) == null ? void 0 : _a.find((e3) => e3.id === r2)) || ((_b = e2.tables) == null ? void 0 : _b.find((e3) => e3.id === r2));
  return a && F(a, t3) ? a : null;
}
function v(e2) {
  var _a, _b;
  return (((_a = e2 == null ? void 0 : e2.layers) == null ? void 0 : _a.length) ?? 0) + (((_b = e2 == null ? void 0 : e2.tables) == null ? void 0 : _b.length) ?? 0);
}
function S(e2) {
  return "stream" !== e2.type && "oriented-imagery" !== e2.type && "layerId" in e2;
}
function T(a) {
  const { coverage: n } = a;
  if (!n) return null;
  const l = new URL(n);
  if (n.toLowerCase().includes("item.html")) {
    const e2 = l.searchParams.get("id"), r2 = l.origin;
    return b.fromPortalItem({ portalItem: new x({ id: e2, url: r2 }) });
  }
  if (m(n)) return b.fromArcGISServerUrl({ url: n });
  throw new s("portal:oriented-imagery-layer-coverage", "the provided coverage url couldn't be loaded as a layer");
}
function j(e2) {
  var _a;
  const t3 = [];
  return (_a = e2 == null ? void 0 : e2.layers) == null ? void 0 : _a.forEach((e3) => {
    "SubtypeGroupLayer" === e3.layerType && t3.push(e3.id);
  }), t3;
}
function F(e2, t3) {
  return !("feature" === t3.type && "layerType" in e2 && "SubtypeGroupLayer" === e2.layerType || "subtype-group" === t3.type && !("layerType" in e2));
}
async function P(e2) {
  const { layersJSON: t3 } = await r(e2);
  if (!t3) return null;
  const r2 = [...t3.layers, ...t3.tables];
  return (e3) => r2.find((t4) => t4.id === e3.id);
}

export {
  p,
  w,
  I,
  v,
  j
};
//# sourceMappingURL=chunk-OWU4AWBA.js.map
