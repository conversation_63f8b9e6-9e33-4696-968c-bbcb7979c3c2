import {
  r
} from "./chunk-ZVJXF3ML.js";
import {
  r as r2
} from "./chunk-PWCXATLS.js";
import {
  h
} from "./chunk-X7FOCGBC.js";
import {
  y
} from "./chunk-GZGAQUSK.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/DoubleArray.js
function n(n2, t2 = false) {
  return n2 <= y ? t2 ? new Array(n2).fill(0) : new Array(n2) : new Float64Array(n2);
}
function t(n2) {
  return length <= y ? Array.from(n2) : new Float64Array(n2);
}
function a(r3, n2, t2) {
  return Array.isArray(r3) ? r3.slice(n2, n2 + t2) : r3.subarray(n2, n2 + t2);
}

// node_modules/@arcgis/core/geometry/support/triangulationUtils.js
function i(t2) {
  const r3 = l(t2.rings, t2.hasZ, f.CCW_IS_HOLE), i2 = new Array();
  let c2 = 0, h3 = 0;
  for (const e of r3.polygons) {
    const t3 = e.count, s2 = e.index, l2 = a(r3.position, 3 * s2, 3 * t3), f2 = e.holeIndices.map((n2) => n2 - s2), g2 = new Uint32Array(r2(l2, f2, 3));
    i2.push({ position: l2, faces: g2 }), c2 += l2.length, h3 += g2.length;
  }
  const g = s(i2, c2, h3), a2 = Array.isArray(g.position) ? r(g.position, 3, { originalIndices: g.faces }) : r(g.position.buffer, 6, { originalIndices: g.faces });
  return g.position = new Float64Array(a2.buffer), g.faces = a2.indices, g;
}
function s(n2, t2, e) {
  if (1 === n2.length) return n2[0];
  const o = n(t2), i2 = new Uint32Array(e);
  let s2 = 0, l2 = 0, c2 = 0;
  for (const r3 of n2) {
    for (let n3 = 0; n3 < r3.position.length; n3++) o[s2++] = r3.position[n3];
    for (let n3 = 0; n3 < r3.faces.length; n3++) i2[l2++] = r3.faces[n3] + c2;
    c2 = s2 / 3;
  }
  return { position: o, faces: i2 };
}
function l(n2, t2, e) {
  const o = n2.length, i2 = new Array(o), s2 = new Array(o), l2 = new Array(o);
  let g = 0, a2 = 0, u = 0, p = 0;
  for (let r3 = 0; r3 < o; ++r3) p += n2[r3].length;
  const d = n(3 * p);
  let y2 = 0;
  for (let r3 = o - 1; r3 >= 0; r3--) {
    const p2 = n2[r3], A = e === f.CCW_IS_HOLE && h2(p2);
    if (A && 1 !== o) i2[g++] = p2;
    else {
      let n3 = p2.length;
      for (let t3 = 0; t3 < g; ++t3) n3 += i2[t3].length;
      const e2 = { index: y2, pathLengths: new Array(g + 1), count: n3, holeIndices: new Array(g) };
      e2.pathLengths[0] = p2.length, p2.length > 0 && (l2[u++] = { index: y2, count: p2.length }), y2 = A ? c(p2, p2.length - 1, -1, d, y2, p2.length, t2) : c(p2, 0, 1, d, y2, p2.length, t2);
      for (let o2 = 0; o2 < g; ++o2) {
        const n4 = i2[o2];
        e2.holeIndices[o2] = y2, e2.pathLengths[o2 + 1] = n4.length, n4.length > 0 && (l2[u++] = { index: y2, count: n4.length }), y2 = c(n4, 0, 1, d, y2, n4.length, t2);
      }
      g = 0, e2.count > 0 && (s2[a2++] = e2);
    }
  }
  for (let r3 = 0; r3 < g; ++r3) {
    const n3 = i2[r3];
    n3.length > 0 && (l2[u++] = { index: y2, count: n3.length }), y2 = c(n3, 0, 1, d, y2, n3.length, t2);
  }
  return s2.length = a2, l2.length = u, { position: d, polygons: s2, outlines: l2 };
}
function c(n2, t2, e, o, r3, i2, s2) {
  r3 *= 3;
  for (let l2 = 0; l2 < i2; ++l2) {
    const i3 = n2[t2];
    o[r3++] = i3[0], o[r3++] = i3[1], o[r3++] = s2 ? i3[2] : 0, t2 += e;
  }
  return r3 / 3;
}
function h2(n2) {
  return !h(n2, false, false);
}
var f;
!function(n2) {
  n2[n2.NONE = 0] = "NONE", n2[n2.CCW_IS_HOLE = 1] = "CCW_IS_HOLE";
}(f || (f = {}));

export {
  n,
  t,
  a,
  i,
  l,
  f
};
//# sourceMappingURL=chunk-OMPEYGMV.js.map
