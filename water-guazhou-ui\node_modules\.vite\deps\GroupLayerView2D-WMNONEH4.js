import {
  r
} from "./chunk-CP7KJJPH.js";
import "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-53FPJYCC.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-IEBU4QQL.js";
import {
  f
} from "./chunk-NEPFZ7PM.js";
import {
  u
} from "./chunk-HWB4LNSZ.js";
import "./chunk-QKWIBVLD.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-22FAZXOH.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-RURSJOSG.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-NOZFLZZL.js";
import {
  U,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import {
  n,
  t as t2
} from "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/layers/GroupLayerView.js
var p = class extends u {
  constructor(i2) {
    super(i2), this.type = "group", this.layerViews = new j();
  }
  _allLayerViewVisibility(i2) {
    this.layerViews.forEach((e2) => {
      e2.visible = i2;
    });
  }
  initialize() {
    this.handles.add([this.layerViews.on("change", (i2) => this._layerViewsChangeHandler(i2)), l(() => this.layer.visibilityMode, () => this._applyVisibility(() => this._allLayerViewVisibility(this.visible), () => this._applyExclusiveVisibility(null)), U), l(() => this.visible, (i2) => {
      this._applyVisibility(() => this._allLayerViewVisibility(i2), () => {
      });
    }, U)], "grouplayerview"), this._layerViewsChangeHandler({ target: null, added: this.layerViews.toArray(), removed: [], moved: [] });
  }
  set layerViews(i2) {
    this._set("layerViews", n(i2, this._get("layerViews")));
  }
  get updatingProgress() {
    return 0 === this.layerViews.length ? 1 : this.layerViews.reduce((i2, e2) => i2 + e2.updatingProgress, 0) / this.layerViews.length;
  }
  isUpdating() {
    return this.layerViews.some((i2) => i2.updating);
  }
  _hasLayerViewVisibleOverrides() {
    return this.layerViews.some((i2) => i2._isOverridden("visible"));
  }
  _findLayerViewForLayer(i2) {
    return i2 && this.layerViews.find((e2) => e2.layer === i2);
  }
  _firstVisibleOnLayerOrder() {
    const i2 = this.layer.layers.find((i3) => {
      var _a;
      return !!((_a = this._findLayerViewForLayer(i3)) == null ? void 0 : _a.visible);
    });
    return i2 && this._findLayerViewForLayer(i2);
  }
  _applyExclusiveVisibility(i2) {
    t(i2) && (i2 = this._firstVisibleOnLayerOrder(), t(i2) && this.layerViews.length > 0 && (i2 = this._findLayerViewForLayer(this.layer.layers.getItemAt(0)))), this.layerViews.forEach((e2) => {
      e2.visible = e2 === i2;
    });
  }
  _layerViewsChangeHandler(i2) {
    this.handles.remove("grouplayerview:visible"), this.handles.add(this.layerViews.map((i3) => l(() => i3.visible, (e3) => this._applyVisibility(() => {
      e3 !== this.visible && (i3.visible = this.visible);
    }, () => this._applyExclusiveVisibility(e3 ? i3 : null)), U)).toArray(), "grouplayerview:visible");
    const e2 = i2.added[i2.added.length - 1];
    this._applyVisibility(() => this._allLayerViewVisibility(this.visible), () => this._applyExclusiveVisibility((e2 == null ? void 0 : e2.visible) ? e2 : null));
  }
  _applyVisibility(i2, e2) {
    var _a, _b;
    this._hasLayerViewVisibleOverrides() && ("inherited" === ((_a = this.layer) == null ? void 0 : _a.visibilityMode) ? i2() : "exclusive" === ((_b = this.layer) == null ? void 0 : _b.visibilityMode) && e2());
  }
};
e([y({ cast: t2 })], p.prototype, "layerViews", null), e([y({ readOnly: true })], p.prototype, "updatingProgress", null), e([y()], p.prototype, "view", void 0), p = e([a("esri.views.layers.GroupLayerView")], p);
var n2 = p;

// node_modules/@arcgis/core/views/2d/layers/GroupLayerView2D.js
var a2 = class extends f(n2) {
  constructor() {
    super(...arguments), this.container = new r();
  }
  attach() {
    this._updateStageChildren(), this.addAttachHandles(this.layerViews.on("after-changes", () => this._updateStageChildren()));
  }
  detach() {
    this.container.removeAllChildren();
  }
  update(e2) {
  }
  moveStart() {
  }
  viewChange() {
  }
  moveEnd() {
  }
  _updateStageChildren() {
    this.container.removeAllChildren(), this.layerViews.forEach((e2, r2) => this.container.addChildAt(e2.container, r2));
  }
};
a2 = e([a("esri.views.2d.layers.GroupLayerView2D")], a2);
var i = a2;
export {
  i as default
};
//# sourceMappingURL=GroupLayerView2D-WMNONEH4.js.map
