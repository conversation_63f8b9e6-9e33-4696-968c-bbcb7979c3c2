{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/webgl/techniques/Technique.js", "../../@arcgis/core/views/2d/engine/webgl/techniques/dotDensity/TechniqueDotDensity.js", "../../@arcgis/core/views/2d/engine/webgl/techniques/heatmap/TechniqueHeatmap.js", "../../@arcgis/core/views/2d/engine/webgl/techniques/pieChart/TechniquePieChart.js", "../../@arcgis/core/views/2d/engine/webgl/techniques/utils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../../../core/maybe.js\";class e{static getStorageSpec(t){return null}static createOrUpdateRendererSchema(e,r){return t(e)&&\"default\"===e.type?e:{type:\"default\"}}static getVariation(t){return{}}static getVariationHash(t){return 0}}e.type=\"default\",e.programSpec=null;export{e as Technique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../../../core/maybe.js\";import{writeColor as t}from\"../../color.js\";import{DOT_DENSITY_MAX_FIELDS as o}from\"../../definitions.js\";import{Technique as r}from\"../Technique.js\";import{DataType as a}from\"../../../../../webgl/enums.js\";class n extends r{static getStorageSpec({attributes:e}){return{visualVariables:!1,attributes:e??null}}static _createRendererSchema(){return{type:\"dot-density\",colors:new Float32Array(32),dotValue:-1,dotSize:-1,dotScale:-1,dotBlending:!1,backgroundColor:new Float32Array(4),activeDots:new Float32Array(8),seed:-1}}static createOrUpdateRendererSchema(r,a){const{attributes:n,dotValue:i,referenceScale:d,dotSize:l,dotBlendingEnabled:s,seed:c,backgroundColor:u}=a,m=e(r)&&\"dot-density\"===r.type?r:this._createRendererSchema();m.dotValue=i,m.dotSize=l,m.dotScale=d,m.dotBlending=s,m.seed=c;const{colors:g,activeDots:p,backgroundColor:y}=m;for(let e=0;e<o;e++){const o=e>=n.length?null:n[e].color;t(g,o,4*e)}for(let e=0;e<8;e++)p[e]=e<a.attributes.length?1:0;return t(y,u),m}static getVariation(e){return{ddDotBlending:e.dotBlending}}static getVariationHash(e){return e.dotBlending?1:0}}n.type=\"dot-density\",n.programSpec={shader:\"materials/fill\",vertexLayout:{geometry:[{location:0,name:\"a_pos\",count:2,type:a.SHORT},{location:1,name:\"a_id\",count:3,type:a.UNSIGNED_BYTE},{location:2,name:\"a_bitset\",count:1,type:a.UNSIGNED_BYTE},{location:3,name:\"a_inverseArea\",count:1,type:a.FLOAT}]}};export{n as DotDensityTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../../../core/maybe.js\";import{pt2px as t}from\"../../../../../../core/screenUtils.js\";import{generateGradient as r}from\"../../../../../../renderers/support/heatmapUtils.js\";import{Technique as a}from\"../Technique.js\";import{DataType as i}from\"../../../../../webgl/enums.js\";class n extends a{static getStorageSpec({field:e,valueExpression:t}){return{visualVariables:!1,attributes:e||t?[{field:e,valueExpression:t}]:null}}static _createRendererSchema(){return{type:\"heatmap\",radius:-1,referenceScale:-1,isFieldActive:0,minDensity:-1,densityRange:-1,kernel:null,gradient:null,gradientHash:\"invalid\"}}static createOrUpdateRendererSchema(a,i){const{radius:n,minDensity:s,maxDensity:o,referenceScale:c,field:l,valueExpression:m,colorStops:p}=i,d=o-s,u=l||m?1:0,y=p.map((({color:e,ratio:t})=>`${t}:${e.toString()}`)).join();let h,S=!0;return e(a)&&\"heatmap\"===a.type?(h=a,S=y!==a.gradientHash):h=this._createRendererSchema(),h.radius=t(n),h.minDensity=s,h.densityRange=d,h.referenceScale=c,h.isFieldActive=u,S&&(h.gradient=r(p),h.gradientHash=y),h}}n.type=\"heatmap\",n.programSpec={shader:\"materials/icon/heatmapAccumulate\",vertexLayout:{geometry:[{location:0,name:\"a_pos\",count:2,type:i.SHORT},{location:1,name:\"a_vertexOffset\",count:2,type:i.SHORT},{location:4,name:\"a_id\",count:4,type:i.UNSIGNED_BYTE}]}};export{n as HeatmapTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../../Color.js\";import{isSome as t}from\"../../../../../../core/maybe.js\";import{pt2px as o}from\"../../../../../../core/screenUtils.js\";import{writeColor as r}from\"../../color.js\";import{CHART_MAX_FIELDS as a}from\"../../definitions.js\";import{Technique as n}from\"../Technique.js\";import{DataType as i}from\"../../../../../webgl/enums.js\";class l extends n{static getStorageSpec({attributes:e}){return{visualVariables:!0,attributes:e??null}}static _createRendererSchema(){return{type:\"pie-chart\",colors:new Float32Array(4*a),defaultColor:new Float32Array(4),othersColor:new Float32Array(4),outlineColor:new Float32Array(4),holePercentage:0,sectorThreshold:0,outlineWidth:1,numberOfFields:10}}static createOrUpdateRendererSchema(n,i){const{attributes:l,defaultColor:s,holePercentage:c,othersCategory:m,outline:u}=i,d=t(n)&&\"pie-chart\"===n.type?n:this._createRendererSchema();for(let t=0;t<a;t++){const o=t>=l.length?new e([0,0,0,0]):l[t].color;r(d.colors,o,4*t)}return r(d.defaultColor,s),r(d.othersColor,m?.color),r(d.outlineColor,u?.color),d.outlineWidth=o(u?.width||0),d.holePercentage=c,d.sectorThreshold=m?.threshold||0,d.numberOfFields=l.length,d}static getVariation(e){return{numberOfFields:e.numberOfFields}}static getVariationHash(e){return e.numberOfFields}}l.type=\"pie-chart\",l.programSpec={shader:\"materials/pie\",vertexLayout:{geometry:[{location:0,name:\"a_pos\",count:2,type:i.SHORT},{location:1,name:\"a_vertexOffset\",count:2,type:i.SHORT},{location:2,name:\"a_texCoords\",count:2,type:i.UNSIGNED_SHORT},{location:3,name:\"a_bitSetAndDistRatio\",count:2,type:i.UNSIGNED_SHORT},{location:4,name:\"a_id\",count:4,type:i.UNSIGNED_BYTE},{location:5,name:\"a_color\",count:4,type:i.UNSIGNED_BYTE,normalized:!0},{location:6,name:\"a_outlineColor\",count:4,type:i.UNSIGNED_BYTE,normalized:!0},{location:7,name:\"a_sizeAndOutlineWidth\",count:4,type:i.UNSIGNED_BYTE},{location:8,name:\"a_zoomRange\",count:2,type:i.UNSIGNED_SHORT}]},hittestAttributes:[\"a_vertexOffset\",\"a_texCoords\"]};export{l as PieChartTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/Error.js\";import{WGLGeometryType as r,WGLSymbologyType as t}from\"../enums.js\";import{MaterialKeyBase as o}from\"../materialKey/MaterialKey.js\";import{Technique as i}from\"./Technique.js\";import{DotDensityTechnique as n}from\"./dotDensity/TechniqueDotDensity.js\";import{HeatmapTechnique as a}from\"./heatmap/TechniqueHeatmap.js\";import{PieChartTechnique as m}from\"./pieChart/TechniquePieChart.js\";function s(r,t){if(r.type!==t)throw new e(\"material-view-model:unexpected-renderer-schema\",`expected to find renderer schema of type \"${t}\" but found type \"${r.type}\"`)}function c(e){switch(e?.type){case\"dot-density\":return n;case\"heatmap\":return a;case\"pie-chart\":return m;default:return i}}function p(e){const{geometryType:s,symbologyType:c}=o.load(e);switch(s){case r.FILL:if(c===t.DOT_DENSITY)return n;break;case r.MARKER:switch(c){case t.HEATMAP:return a;case t.PIE_CHART:return m}}return i}export{s as assertRendererSchema,p as getTechniqueFromMaterialKey,c as getTechniqueFromRenderer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIsD,IAAMA,KAAN,MAAO;AAAA,EAAC,OAAO,eAAe,GAAE;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,OAAO,6BAA6BA,IAAEC,IAAE;AAAC,WAAO,EAAED,EAAC,KAAG,cAAYA,GAAE,OAAKA,KAAE,EAAC,MAAK,UAAS;AAAA,EAAC;AAAA,EAAC,OAAO,aAAa,GAAE;AAAC,WAAM,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiB,GAAE;AAAC,WAAO;AAAA,EAAC;AAAC;AAACA,GAAE,OAAK,WAAUA,GAAE,cAAY;;;ACA3B,IAAM,IAAN,cAAgBE,GAAC;AAAA,EAAC,OAAO,eAAe,EAAC,YAAWA,GAAC,GAAE;AAAC,WAAM,EAAC,iBAAgB,OAAG,YAAWA,MAAG,KAAI;AAAA,EAAC;AAAA,EAAC,OAAO,wBAAuB;AAAC,WAAM,EAAC,MAAK,eAAc,QAAO,IAAI,aAAa,EAAE,GAAE,UAAS,IAAG,SAAQ,IAAG,UAAS,IAAG,aAAY,OAAG,iBAAgB,IAAI,aAAa,CAAC,GAAE,YAAW,IAAI,aAAa,CAAC,GAAE,MAAK,GAAE;AAAA,EAAC;AAAA,EAAC,OAAO,6BAA6BC,IAAE,GAAE;AAAC,UAAK,EAAC,YAAWC,IAAE,UAAS,GAAE,gBAAe,GAAE,SAAQC,IAAE,oBAAmBC,IAAE,MAAKC,IAAE,iBAAgBC,GAAC,IAAE,GAAE,IAAE,EAAEL,EAAC,KAAG,kBAAgBA,GAAE,OAAKA,KAAE,KAAK,sBAAsB;AAAE,MAAE,WAAS,GAAE,EAAE,UAAQE,IAAE,EAAE,WAAS,GAAE,EAAE,cAAYC,IAAE,EAAE,OAAKC;AAAE,UAAK,EAAC,QAAO,GAAE,YAAWE,IAAE,iBAAgB,EAAC,IAAE;AAAE,aAAQP,KAAE,GAAEA,KAAEC,IAAED,MAAI;AAAC,YAAM,IAAEA,MAAGE,GAAE,SAAO,OAAKA,GAAEF,EAAC,EAAE;AAAM,QAAE,GAAE,GAAE,IAAEA,EAAC;AAAA,IAAC;AAAC,aAAQA,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAO,GAAEP,EAAC,IAAEA,KAAE,EAAE,WAAW,SAAO,IAAE;AAAE,WAAO,EAAE,GAAEM,EAAC,GAAE;AAAA,EAAC;AAAA,EAAC,OAAO,aAAaN,IAAE;AAAC,WAAM,EAAC,eAAcA,GAAE,YAAW;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiBA,IAAE;AAAC,WAAOA,GAAE,cAAY,IAAE;AAAA,EAAC;AAAC;AAAC,EAAE,OAAK,eAAc,EAAE,cAAY,EAAC,QAAO,kBAAiB,cAAa,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,QAAO,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,YAAW,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,iBAAgB,OAAM,GAAE,MAAK,EAAE,MAAK,CAAC,EAAC,EAAC;;;ACAjnC,IAAMQ,KAAN,cAAgBC,GAAC;AAAA,EAAC,OAAO,eAAe,EAAC,OAAMA,IAAE,iBAAgB,EAAC,GAAE;AAAC,WAAM,EAAC,iBAAgB,OAAG,YAAWA,MAAG,IAAE,CAAC,EAAC,OAAMA,IAAE,iBAAgB,EAAC,CAAC,IAAE,KAAI;AAAA,EAAC;AAAA,EAAC,OAAO,wBAAuB;AAAC,WAAM,EAAC,MAAK,WAAU,QAAO,IAAG,gBAAe,IAAG,eAAc,GAAE,YAAW,IAAG,cAAa,IAAG,QAAO,MAAK,UAAS,MAAK,cAAa,UAAS;AAAA,EAAC;AAAA,EAAC,OAAO,6BAA6B,GAAE,GAAE;AAAC,UAAK,EAAC,QAAOD,IAAE,YAAWE,IAAE,YAAW,GAAE,gBAAeC,IAAE,OAAMC,IAAE,iBAAgB,GAAE,YAAWC,GAAC,IAAE,GAAE,IAAE,IAAEH,IAAEI,KAAEF,MAAG,IAAE,IAAE,GAAE,IAAEC,GAAE,IAAK,CAAC,EAAC,OAAMJ,IAAE,OAAM,EAAC,MAAI,GAAG,CAAC,IAAIA,GAAE,SAAS,CAAC,EAAG,EAAE,KAAK;AAAE,QAAI,GAAEM,KAAE;AAAG,WAAO,EAAE,CAAC,KAAG,cAAY,EAAE,QAAM,IAAE,GAAEA,KAAE,MAAI,EAAE,gBAAc,IAAE,KAAK,sBAAsB,GAAE,EAAE,SAAO,EAAEP,EAAC,GAAE,EAAE,aAAWE,IAAE,EAAE,eAAa,GAAE,EAAE,iBAAeC,IAAE,EAAE,gBAAcG,IAAEC,OAAI,EAAE,WAASD,GAAED,EAAC,GAAE,EAAE,eAAa,IAAG;AAAA,EAAC;AAAC;AAACL,GAAE,OAAK,WAAUA,GAAE,cAAY,EAAC,QAAO,oCAAmC,cAAa,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,kBAAiB,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,QAAO,OAAM,GAAE,MAAK,EAAE,cAAa,CAAC,EAAC,EAAC;;;ACA18B,IAAMQ,KAAN,cAAgBC,GAAC;AAAA,EAAC,OAAO,eAAe,EAAC,YAAWA,GAAC,GAAE;AAAC,WAAM,EAAC,iBAAgB,MAAG,YAAWA,MAAG,KAAI;AAAA,EAAC;AAAA,EAAC,OAAO,wBAAuB;AAAC,WAAM,EAAC,MAAK,aAAY,QAAO,IAAI,aAAa,IAAEC,EAAC,GAAE,cAAa,IAAI,aAAa,CAAC,GAAE,aAAY,IAAI,aAAa,CAAC,GAAE,cAAa,IAAI,aAAa,CAAC,GAAE,gBAAe,GAAE,iBAAgB,GAAE,cAAa,GAAE,gBAAe,GAAE;AAAA,EAAC;AAAA,EAAC,OAAO,6BAA6BC,IAAE,GAAE;AAAC,UAAK,EAAC,YAAWH,IAAE,cAAaE,IAAE,gBAAeE,IAAE,gBAAe,GAAE,SAAQC,GAAC,IAAE,GAAE,IAAE,EAAEF,EAAC,KAAG,gBAAcA,GAAE,OAAKA,KAAE,KAAK,sBAAsB;AAAE,aAAQ,IAAE,GAAE,IAAED,IAAE,KAAI;AAAC,YAAM,IAAE,KAAGF,GAAE,SAAO,IAAI,EAAE,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,EAAE;AAAM,QAAE,EAAE,QAAO,GAAE,IAAE,CAAC;AAAA,IAAC;AAAC,WAAO,EAAE,EAAE,cAAaE,EAAC,GAAE,EAAE,EAAE,aAAY,uBAAG,KAAK,GAAE,EAAE,EAAE,cAAaG,MAAA,gBAAAA,GAAG,KAAK,GAAE,EAAE,eAAa,GAAEA,MAAA,gBAAAA,GAAG,UAAO,CAAC,GAAE,EAAE,iBAAeD,IAAE,EAAE,mBAAgB,uBAAG,cAAW,GAAE,EAAE,iBAAeJ,GAAE,QAAO;AAAA,EAAC;AAAA,EAAC,OAAO,aAAaC,IAAE;AAAC,WAAM,EAAC,gBAAeA,GAAE,eAAc;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiBA,IAAE;AAAC,WAAOA,GAAE;AAAA,EAAc;AAAC;AAACD,GAAE,OAAK,aAAYA,GAAE,cAAY,EAAC,QAAO,iBAAgB,cAAa,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,SAAQ,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,kBAAiB,OAAM,GAAE,MAAK,EAAE,MAAK,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,GAAE,EAAC,UAAS,GAAE,MAAK,wBAAuB,OAAM,GAAE,MAAK,EAAE,eAAc,GAAE,EAAC,UAAS,GAAE,MAAK,QAAO,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,WAAU,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,kBAAiB,OAAM,GAAE,MAAK,EAAE,eAAc,YAAW,KAAE,GAAE,EAAC,UAAS,GAAE,MAAK,yBAAwB,OAAM,GAAE,MAAK,EAAE,cAAa,GAAE,EAAC,UAAS,GAAE,MAAK,eAAc,OAAM,GAAE,MAAK,EAAE,eAAc,CAAC,EAAC,GAAE,mBAAkB,CAAC,kBAAiB,aAAa,EAAC;;;ACAjiD,SAASM,GAAEC,IAAE,GAAE;AAAC,MAAGA,GAAE,SAAO,EAAE,OAAM,IAAI,EAAE,kDAAiD,6CAA6C,CAAC,qBAAqBA,GAAE,IAAI,GAAG;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,UAAOA,MAAA,gBAAAA,GAAG,MAAK;AAAA,IAAC,KAAI;AAAc,aAAO;AAAA,IAAE,KAAI;AAAU,aAAOC;AAAA,IAAE,KAAI;AAAY,aAAOC;AAAA,IAAE;AAAQ,aAAOF;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAK,EAAC,cAAaF,IAAE,eAAcK,GAAC,IAAE,EAAE,KAAKH,EAAC;AAAE,UAAOF,IAAE;AAAA,IAAC,KAAK,EAAE;AAAK,UAAGK,OAAI,EAAE,YAAY,QAAO;AAAE;AAAA,IAAM,KAAK,EAAE;AAAO,cAAOA,IAAE;AAAA,QAAC,KAAK,EAAE;AAAQ,iBAAOF;AAAA,QAAE,KAAK,EAAE;AAAU,iBAAOC;AAAA,MAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;", "names": ["e", "r", "e", "r", "n", "l", "s", "c", "u", "p", "n", "e", "s", "c", "l", "p", "u", "S", "l", "e", "s", "n", "c", "u", "s", "r", "e", "n", "l", "c"]}