import {
  i,
  p,
  s2
} from "./chunk-A4XIY547.js";
import {
  g,
  u2
} from "./chunk-5HSEQ7GU.js";
import {
  s
} from "./chunk-FQZM46ZM.js";
import {
  r
} from "./chunk-KVEZ26WH.js";
import {
  o as o4
} from "./chunk-Q6BEUTMN.js";
import {
  d
} from "./chunk-BOT4BSSB.js";
import {
  t as t3
} from "./chunk-PIQKEGGB.js";
import {
  n as n2
} from "./chunk-Y424ZXTG.js";
import {
  e as e5
} from "./chunk-UB5FTTH5.js";
import {
  n,
  t as t2
} from "./chunk-6GW7M2AQ.js";
import {
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  v
} from "./chunk-N3S5O3YO.js";
import {
  e as e4
} from "./chunk-JETZLJ6M.js";
import {
  o as o3
} from "./chunk-BPRRRPC3.js";
import {
  e as e2
} from "./chunk-GXMOAZWH.js";
import {
  o as o5
} from "./chunk-TUB4N6LD.js";
import {
  e as e3
} from "./chunk-YV4RKNU4.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  l
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/RibbonLineTechniqueConfiguration.js
var r2;
!function(o6) {
  o6[o6.BUTT = 0] = "BUTT", o6[o6.SQUARE = 1] = "SQUARE", o6[o6.ROUND = 2] = "ROUND", o6[o6.COUNT = 3] = "COUNT";
}(r2 || (r2 = {}));
var s3 = class extends s {
  constructor() {
    super(...arguments), this.output = h.Color, this.capType = r2.BUTT, this.transparencyPassType = o3.NONE, this.occluder = false, this.hasSlicePlane = false, this.hasPolygonOffset = false, this.writeDepth = false, this.draped = false, this.stippleEnabled = false, this.stippleOffColorEnabled = false, this.stippleScaleWithLineWidth = false, this.stipplePreferContinuous = true, this.roundJoins = false, this.applyMarkerOffset = false, this.vvSize = false, this.vvColor = false, this.vvOpacity = false, this.falloffEnabled = false, this.innerColorEnabled = false, this.hasOccludees = false, this.hasMultipassTerrain = false, this.cullAboveGround = false, this.wireframe = false, this.objectAndLayerIdColorInstanced = false;
  }
};
e([r({ count: h.COUNT })], s3.prototype, "output", void 0), e([r({ count: r2.COUNT })], s3.prototype, "capType", void 0), e([r({ count: o3.COUNT })], s3.prototype, "transparencyPassType", void 0), e([r()], s3.prototype, "occluder", void 0), e([r()], s3.prototype, "hasSlicePlane", void 0), e([r()], s3.prototype, "hasPolygonOffset", void 0), e([r()], s3.prototype, "writeDepth", void 0), e([r()], s3.prototype, "draped", void 0), e([r()], s3.prototype, "stippleEnabled", void 0), e([r()], s3.prototype, "stippleOffColorEnabled", void 0), e([r()], s3.prototype, "stippleScaleWithLineWidth", void 0), e([r()], s3.prototype, "stipplePreferContinuous", void 0), e([r()], s3.prototype, "roundJoins", void 0), e([r()], s3.prototype, "applyMarkerOffset", void 0), e([r()], s3.prototype, "vvSize", void 0), e([r()], s3.prototype, "vvColor", void 0), e([r()], s3.prototype, "vvOpacity", void 0), e([r()], s3.prototype, "falloffEnabled", void 0), e([r()], s3.prototype, "innerColorEnabled", void 0), e([r()], s3.prototype, "hasOccludees", void 0), e([r()], s3.prototype, "hasMultipassTerrain", void 0), e([r()], s3.prototype, "cullAboveGround", void 0), e([r()], s3.prototype, "wireframe", void 0), e([r({ constValue: true })], s3.prototype, "stippleRequiresClamp", void 0), e([r({ constValue: true })], s3.prototype, "stippleRequiresStretchMeasure", void 0), e([r({ constValue: true })], s3.prototype, "hasVvInstancing", void 0), e([r({ constValue: true })], s3.prototype, "hasSliceTranslatedView", void 0), e([r()], s3.prototype, "objectAndLayerIdColorInstanced", void 0);

// node_modules/@arcgis/core/chunks/RibbonLine.glsl.js
var j = 1;
function R(R2) {
  const A2 = new o2(), { vertex: P, fragment: F } = A2, z = R2.hasMultipassTerrain && (R2.output === h.Color || R2.output === h.Alpha);
  A2.include(t3), A2.include(s2, R2), A2.include(u2, R2);
  const E = R2.applyMarkerOffset && !R2.draped;
  E && (P.uniforms.add(new o5("markerScale", (e6) => e6.markerScale)), A2.include(i, { space: p.World })), R2.output === h.Depth && A2.include(o4, R2), A2.include(d, R2), v(P, R2), P.uniforms.add([new e4("inverseProjectionMatrix", (e6, i2) => i2.camera.inverseProjectionMatrix), new e3("nearFar", (e6, i2) => i2.camera.nearFar), new o5("miterLimit", (e6) => "miter" !== e6.join ? 0 : e6.miterLimit), new e2("viewport", (e6, i2) => i2.camera.fullViewport)]), P.constants.add("LARGE_HALF_FLOAT", "float", 65500), A2.attributes.add(O.POSITION, "vec3"), A2.attributes.add(O.SUBDIVISIONFACTOR, "float"), A2.attributes.add(O.UV0, "vec2"), A2.attributes.add(O.AUXPOS1, "vec3"), A2.attributes.add(O.AUXPOS2, "vec3"), A2.varyings.add("vColor", "vec4"), A2.varyings.add("vpos", "vec3"), t2(A2), z && A2.varyings.add("depth", "float");
  const T = R2.capType === r2.ROUND, W = R2.stippleEnabled && R2.stippleScaleWithLineWidth || T;
  W && A2.varyings.add("vLineWidth", "float");
  const O2 = R2.stippleEnabled && R2.stippleScaleWithLineWidth;
  O2 && A2.varyings.add("vLineSizeInv", "float");
  const V = R2.innerColorEnabled || T;
  V && A2.varyings.add("vLineDistance", "float");
  const N = R2.stippleEnabled && T, I = R2.falloffEnabled || N;
  I && A2.varyings.add("vLineDistanceNorm", "float"), T && (A2.varyings.add("vSegmentSDF", "float"), A2.varyings.add("vReverseSegmentSDF", "float")), P.code.add(o`#define PERPENDICULAR(v) vec2(v.y, -v.x);
float interp(float ncp, vec4 a, vec4 b) {
return (-ncp - a.z) / (b.z - a.z);
}
vec2 rotate(vec2 v, float a) {
float s = sin(a);
float c = cos(a);
mat2 m = mat2(c, -s, s, c);
return m * v;
}`), P.code.add(o`vec4 projectAndScale(vec4 pos) {
vec4 posNdc = proj * pos;
posNdc.xy *= viewport.zw / posNdc.w;
return posNdc;
}`), n(A2), P.code.add(o`
    void clipAndTransform(inout vec4 pos, inout vec4 prev, inout vec4 next, in bool isStartVertex) {
      float vnp = nearFar[0] * 0.99;

      if(pos.z > -nearFar[0]) {
        //current pos behind ncp --> we need to clip
        if (!isStartVertex) {
          if(prev.z < -nearFar[0]) {
            //previous in front of ncp
            pos = mix(prev, pos, interp(vnp, prev, pos));
            next = pos;
          } else {
            pos = vec4(0.0, 0.0, 0.0, 1.0);
          }
        } else {
          if(next.z < -nearFar[0]) {
            //next in front of ncp
            pos = mix(pos, next, interp(vnp, pos, next));
            prev = pos;
          } else {
            pos = vec4(0.0, 0.0, 0.0, 1.0);
          }
        }
      } else {
        //current position visible
        if (prev.z > -nearFar[0]) {
          //previous behind ncp
          prev = mix(pos, prev, interp(vnp, pos, prev));
        }
        if (next.z > -nearFar[0]) {
          //next behind ncp
          next = mix(next, pos, interp(vnp, next, pos));
        }
      }

      ${z ? "depth = pos.z;" : ""}
      linearDepth = calculateLinearDepth(nearFar,pos.z);

      pos = projectAndScale(pos);
      next = projectAndScale(next);
      prev = projectAndScale(prev);
    }
  `), P.uniforms.add(new o5("pixelRatio", (e6, i2) => i2.camera.pixelRatio)), P.code.add(o`
  void main(void) {
    // unpack values from uv0.y
    bool isStartVertex = abs(abs(uv0.y)-3.0) == 1.0;

    float coverage = 1.0;

    // Check for special value of uv0.y which is used by the Renderer when graphics
    // are removed before the VBO is recompacted. If this is the case, then we just
    // project outside of clip space.
    if (uv0.y == 0.0) {
      // Project out of clip space
      gl_Position = vec4(1e038, 1e038, 1e038, 1.0);
    }
    else {
      bool isJoin = abs(uv0.y) < 3.0;

      float lineSize = getSize();
      float lineWidth = lineSize * pixelRatio;

      ${W ? o`vLineWidth = lineWidth;` : ""}
      ${O2 ? o`vLineSizeInv = 1.0 / lineSize;` : ""}

      // convert sub-pixel coverage to alpha
      if (lineWidth < 1.0) {
        coverage = lineWidth;
        lineWidth = 1.0;
      }else{
        // Ribbon lines cannot properly render non-integer sizes. Round width to integer size if
        // larger than one for better quality. Note that we do render < 1 pixels more or less correctly
        // so we only really care to round anything larger than 1.
        lineWidth = floor(lineWidth + 0.5);
      }

      vec4 pos  = view * vec4(position.xyz, 1.0);
      vec4 prev = view * vec4(auxpos1.xyz, 1.0);
      vec4 next = view * vec4(auxpos2.xyz, 1.0);
  `), E && P.code.add(o`vec4 other = isStartVertex ? next : prev;
bool markersHidden = areWorldMarkersHidden(pos, other);
if(!isJoin && !markersHidden) {
pos.xyz += normalize(other.xyz - pos.xyz) * getWorldMarkerSize(pos) * 0.5;
}`), P.code.add(o`clipAndTransform(pos, prev, next, isStartVertex);
vec2 left = (pos.xy - prev.xy);
vec2 right = (next.xy - pos.xy);
float leftLen = length(left);
float rightLen = length(right);`);
  (R2.stippleEnabled || T) && P.code.add(o`
      float isEndVertex = float(!isStartVertex);
      vec2 segmentOrigin = mix(pos.xy, prev.xy, isEndVertex);
      vec2 segment = mix(right, left, isEndVertex);
      ${T ? o`vec2 segmentEnd = mix(next.xy, pos.xy, isEndVertex);` : ""}
    `), P.code.add(o`left = (leftLen > 0.001) ? left/leftLen : vec2(0.0, 0.0);
right = (rightLen > 0.001) ? right/rightLen : vec2(0.0, 0.0);
vec2 capDisplacementDir = vec2(0, 0);
vec2 joinDisplacementDir = vec2(0, 0);
float displacementLen = lineWidth;
if (isJoin) {
bool isOutside = (left.x * right.y - left.y * right.x) * uv0.y > 0.0;
joinDisplacementDir = normalize(left + right);
joinDisplacementDir = PERPENDICULAR(joinDisplacementDir);
if (leftLen > 0.001 && rightLen > 0.001) {
float nDotSeg = dot(joinDisplacementDir, left);
displacementLen /= length(nDotSeg * left - joinDisplacementDir);
if (!isOutside) {
displacementLen = min(displacementLen, min(leftLen, rightLen)/abs(nDotSeg));
}
}
if (isOutside && (displacementLen > miterLimit * lineWidth)) {`), R2.roundJoins ? P.code.add(o`
        vec2 startDir = leftLen < 0.001 ? right : left;
        startDir = PERPENDICULAR(startDir);

        vec2 endDir = rightLen < 0.001 ? left : right;
        endDir = PERPENDICULAR(endDir);

        float factor = ${R2.stippleEnabled ? o`min(1.0, subdivisionFactor * ${o.float((j + 2) / (j + 1))})` : o`subdivisionFactor`};

        float rotationAngle = acos(clamp(dot(startDir, endDir), -1.0, 1.0));
        joinDisplacementDir = rotate(startDir, -sign(uv0.y) * factor * rotationAngle);
      `) : P.code.add(o`if (leftLen < 0.001) {
joinDisplacementDir = right;
}
else if (rightLen < 0.001) {
joinDisplacementDir = left;
}
else {
joinDisplacementDir = (isStartVertex || subdivisionFactor > 0.0) ? right : left;
}
joinDisplacementDir = PERPENDICULAR(joinDisplacementDir);`);
  const _ = R2.capType !== r2.BUTT;
  return P.code.add(o`
        displacementLen = lineWidth;
      }
    } else {
      // CAP handling ---------------------------------------------------
      joinDisplacementDir = isStartVertex ? right : left;
      joinDisplacementDir = PERPENDICULAR(joinDisplacementDir);

      ${_ ? o`capDisplacementDir = isStartVertex ? -right : left;` : ""}
    }
  `), P.code.add(o`
    // Displacement (in pixels) caused by join/or cap
    vec2 dpos = joinDisplacementDir * sign(uv0.y) * displacementLen + capDisplacementDir * displacementLen;

    ${I || V ? o`float lineDistNorm = sign(uv0.y) * pos.w;` : ""}

    ${V ? o`vLineDistance = lineWidth * lineDistNorm;` : ""}
    ${I ? o`vLineDistanceNorm = lineDistNorm;` : ""}

    pos.xy += dpos;
  `), T && P.code.add(o`vec2 segmentDir = normalize(segment);
vSegmentSDF = (isJoin && isStartVertex) ? LARGE_HALF_FLOAT : (dot(pos.xy - segmentOrigin, segmentDir) * pos.w) ;
vReverseSegmentSDF = (isJoin && !isStartVertex) ? LARGE_HALF_FLOAT : (dot(pos.xy - segmentEnd, -segmentDir) * pos.w);`), R2.stippleEnabled && (R2.draped ? P.uniforms.add(new o5("worldToScreenRatio", (e6, i2) => 1 / i2.screenToPCSRatio)) : P.code.add(o`vec3 segmentCenter = mix((auxpos2 + position) * 0.5, (position + auxpos1) * 0.5, isEndVertex);
float worldToScreenRatio = computeWorldToScreenRatio(segmentCenter);`), P.code.add(o`float segmentLengthScreenDouble = length(segment);
float segmentLengthScreen = segmentLengthScreenDouble * 0.5;
float discreteWorldToScreenRatio = discretizeWorldToScreenRatio(worldToScreenRatio);
float segmentLengthRender = length(mix(auxpos2 - position, position - auxpos1, isEndVertex));
vStipplePatternStretch = worldToScreenRatio / discreteWorldToScreenRatio;`), R2.draped ? P.code.add(o`float segmentLengthPseudoScreen = segmentLengthScreen / pixelRatio * discreteWorldToScreenRatio / worldToScreenRatio;
float startPseudoScreen = uv0.x * discreteWorldToScreenRatio - mix(0.0, segmentLengthPseudoScreen, isEndVertex);`) : P.code.add(o`float startPseudoScreen = mix(uv0.x, uv0.x - segmentLengthRender, isEndVertex) * discreteWorldToScreenRatio;
float segmentLengthPseudoScreen = segmentLengthRender * discreteWorldToScreenRatio;`), P.uniforms.add(new o5("stipplePatternPixelSize", (e6) => g(e6))), P.code.add(o`
      float patternLength = ${R2.stippleScaleWithLineWidth ? "lineSize * " : ""} stipplePatternPixelSize;

      // Compute the coordinates at both start and end of the line segment, because we need both to clamp to in the fragment shader
      vStippleDistanceLimits = computeStippleDistanceLimits(startPseudoScreen, segmentLengthPseudoScreen, segmentLengthScreen, patternLength);

      vStippleDistance = mix(vStippleDistanceLimits.x, vStippleDistanceLimits.y, isEndVertex);

      // Adjust the coordinate to the displaced position (the pattern is shortened/overextended on the in/outside of joins)
      if (segmentLengthScreenDouble >= 0.001) {
        // Project the actual vertex position onto the line segment. Note that the resulting factor is within [0..1] at the
        // original vertex positions, and slightly outside of that range at the displaced positions
        vec2 stippleDisplacement = pos.xy - segmentOrigin;
        float stippleDisplacementFactor = dot(segment, stippleDisplacement) / (segmentLengthScreenDouble * segmentLengthScreenDouble);

        // Apply this offset to the actual vertex coordinate (can be screen or pseudo-screen space)
        vStippleDistance += (stippleDisplacementFactor - isEndVertex) * (vStippleDistanceLimits.y - vStippleDistanceLimits.x);
      }

      // Cancel out perspective correct interpolation because we want this length the really represent the screen distance
      vStippleDistanceLimits *= pos.w;
      vStippleDistance *= pos.w;

      // Disable stipple distance limits on caps
      vStippleDistanceLimits = isJoin ?
                                 vStippleDistanceLimits :
                                 isStartVertex ?
                                  vec2(-1e038, vStippleDistanceLimits.y) :
                                  vec2(vStippleDistanceLimits.x, 1e038);
    `)), P.code.add(o`
      // Convert back into NDC
      pos.xy = (pos.xy / viewport.zw) * pos.w;

      vColor = getColor();
      vColor.a *= coverage;

      ${R2.wireframe && !R2.draped ? "pos.z -= 0.001 * pos.w;" : ""}

      // transform final position to camera space for slicing
      vpos = (inverseProjectionMatrix * pos).xyz;
      gl_Position = pos;
      forwardObjectAndLayerIdColor();
    }
  }
  `), z && A2.include(n2, R2), A2.include(u, R2), F.include(e5), F.code.add(o`
  void main() {
    discardBySlice(vpos);
    ${z ? "terrainDepthTest(gl_FragCoord, depth);" : ""}
  `), R2.wireframe ? F.code.add(o`vec4 finalColor = vec4(1.0, 0.0, 1.0, 1.0);`) : (T && F.code.add(o`
      float sdf = min(vSegmentSDF, vReverseSegmentSDF);
      vec2 fragmentPosition = vec2(
        min(sdf, 0.0),
        vLineDistance
      ) * gl_FragCoord.w;

      float fragmentRadius = length(fragmentPosition);
      float fragmentCapSDF = (fragmentRadius - vLineWidth) * 0.5; // Divide by 2 to transform from double pixel scale
      float capCoverage = clamp(0.5 - fragmentCapSDF, 0.0, 1.0);

      if (capCoverage < ${o.float(t)}) {
        discard;
      }
    `), N ? F.code.add(o`
      vec2 stipplePosition = vec2(
        min(getStippleSDF() * 2.0 - 1.0, 0.0),
        vLineDistanceNorm * gl_FragCoord.w
      );
      float stippleRadius = length(stipplePosition * vLineWidth);
      float stippleCapSDF = (stippleRadius - vLineWidth) * 0.5; // Divide by 2 to transform from double pixel scale
      float stippleCoverage = clamp(0.5 - stippleCapSDF, 0.0, 1.0);
      float stippleAlpha = step(${o.float(t)}, stippleCoverage);
      `) : F.code.add(o`float stippleAlpha = getStippleAlpha();`), F.uniforms.add(new e2("intrinsicColor", (e6) => e6.color)), R2.output !== h.ObjectAndLayerIdColor && F.code.add(o`discardByStippleAlpha(stippleAlpha, stippleAlphaColorDiscard);`), F.code.add(o`vec4 color = intrinsicColor * vColor;`), R2.innerColorEnabled && (F.uniforms.add(new e2("innerColor", (i2) => l(i2.innerColor, i2.color))), F.uniforms.add(new o5("innerWidth", (e6, i2) => e6.innerWidth * i2.camera.pixelRatio)), F.code.add(o`float distToInner = abs(vLineDistance * gl_FragCoord.w) - innerWidth;
float innerAA = clamp(0.5 - distToInner, 0.0, 1.0);
float innerAlpha = innerColor.a + color.a * (1.0 - innerColor.a);
color = mix(color, vec4(innerColor.rgb, innerAlpha), innerAA);`)), F.code.add(o`vec4 finalColor = blendStipple(color, stippleAlpha);`), R2.falloffEnabled && (F.uniforms.add(new o5("falloff", (e6) => e6.falloff)), F.code.add(o`finalColor.a *= pow(max(0.0, 1.0 - abs(vLineDistanceNorm * gl_FragCoord.w)), falloff);`))), F.code.add(o`
    ${R2.output === h.ObjectAndLayerIdColor ? o`finalColor.a = 1.0;` : ""}

    if (finalColor.a < ${o.float(t)}) {
      discard;
    }

    ${R2.output === h.Alpha ? o`gl_FragColor = vec4(finalColor.a);` : ""}
    ${R2.output === h.Color ? o`gl_FragColor = highlightSlice(finalColor, vpos);` : ""}
    ${R2.output === h.Color && R2.transparencyPassType === o3.Color ? "gl_FragColor = premultiplyAlpha(gl_FragColor);" : ""}
    ${R2.output === h.Highlight ? o`gl_FragColor = vec4(1.0);` : ""}
    ${R2.output === h.Depth ? o`outputDepth(linearDepth);` : ""}
    ${R2.output === h.ObjectAndLayerIdColor ? o`outputObjectAndLayerIdColor();` : ""}
  }
  `), A2;
}
var A = Object.freeze(Object.defineProperty({ __proto__: null, RIBBONLINE_NUM_ROUND_JOIN_SUBDIVISIONS: j, build: R }, Symbol.toStringTag, { value: "Module" }));

export {
  r2 as r,
  s3 as s,
  j,
  R,
  A
};
//# sourceMappingURL=chunk-J2CRYUH3.js.map
