{"version": 3, "sources": ["../../@arcgis/core/symbols/Symbol.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../chunks/tslib.es6.js\";import o from\"../Color.js\";import{JSONMap as e}from\"../core/jsonMap.js\";import{JSONSupport as s}from\"../core/JSONSupport.js\";import{property as t}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{reader as l}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as i}from\"../core/accessorSupport/decorators/subclass.js\";const p=new e({esriSMS:\"simple-marker\",esriPMS:\"picture-marker\",esriSLS:\"simple-line\",esriSFS:\"simple-fill\",esriPFS:\"picture-fill\",esriTS:\"text\",esriSHD:\"shield-label-symbol\",PointSymbol3D:\"point-3d\",LineSymbol3D:\"line-3d\",PolygonSymbol3D:\"polygon-3d\",WebStyleSymbol:\"web-style\",MeshSymbol3D:\"mesh-3d\",LabelSymbol3D:\"label-3d\",CIMSymbolReference:\"cim\"});let c=0,m=class extends s{constructor(r){super(r),this.id=\"sym\"+c++,this.type=null,this.color=new o([0,0,0,1])}readColor(r){return r&&null!=r[0]?[r[0],r[1],r[2],r[3]/255]:r}async collectRequiredFields(r,o){}hash(){return JSON.stringify(this.toJSON())}clone(){}};r([t({type:p.apiValues,readOnly:!0,json:{read:!1,write:{ignoreOrigin:!0,writer:p.write}}})],m.prototype,\"type\",void 0),r([t({type:o,json:{write:{allowNull:!0}}})],m.prototype,\"color\",void 0),r([l(\"color\")],m.prototype,\"readColor\",null),m=r([i(\"esri.symbols.Symbol\")],m);const a=m;export{a as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAI8c,IAAM,IAAE,IAAI,EAAE,EAAC,SAAQ,iBAAgB,SAAQ,kBAAiB,SAAQ,eAAc,SAAQ,eAAc,SAAQ,gBAAe,QAAO,QAAO,SAAQ,uBAAsB,eAAc,YAAW,cAAa,WAAU,iBAAgB,cAAa,gBAAe,aAAY,cAAa,WAAU,eAAc,YAAW,oBAAmB,MAAK,CAAC;AAAE,IAAI,IAAE;AAAN,IAAQ,IAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,KAAG,QAAM,KAAI,KAAK,OAAK,MAAK,KAAK,QAAM,IAAIA,GAAE,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAU,GAAE;AAAC,WAAO,KAAG,QAAM,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,IAAE,GAAG,IAAE;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsB,GAAEC,IAAE;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,WAAU,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,EAAC,cAAa,MAAG,QAAO,EAAE,MAAK,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKD,IAAE,MAAK,EAAC,OAAM,EAAC,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,qBAAqB,CAAC,GAAE,CAAC;AAAE,IAAME,KAAE;", "names": ["l", "o", "a"]}