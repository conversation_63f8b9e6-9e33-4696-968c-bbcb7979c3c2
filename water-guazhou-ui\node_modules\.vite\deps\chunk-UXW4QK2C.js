import {
  t as t3
} from "./chunk-WFNEQMBV.js";
import {
  m as m2,
  p as p2,
  y as y2
} from "./chunk-RBRCYKF7.js";
import {
  x
} from "./chunk-EVGSPNRI.js";
import {
  d,
  l as l5,
  t as t2
} from "./chunk-DUFXYLLP.js";
import {
  l as l3
} from "./chunk-NRSKI5BU.js";
import {
  l as l4
} from "./chunk-TDC6MNNF.js";
import {
  v as v2,
  w
} from "./chunk-D7S3BWBP.js";
import {
  o as o2
} from "./chunk-CCAF47ZU.js";
import {
  n as n3,
  r as r2
} from "./chunk-NVZMGX2J.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import {
  n as n2
} from "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  s as s3
} from "./chunk-7SWS36OI.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  l as l2
} from "./chunk-T23PB46T.js";
import {
  o
} from "./chunk-PEEUPDEG.js";
import {
  l,
  u2 as u
} from "./chunk-UOKTNY52.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T,
  b
} from "./chunk-HP475EI3.js";
import {
  E,
  f
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/ground/NavigationConstraint.js
var t4;
var p3 = t4 = class extends l {
  constructor(r3) {
    super(r3), this.type = "none";
  }
  clone() {
    return new t4({ type: this.type });
  }
};
e([o({ none: "none", stayAbove: "stay-above" })], p3.prototype, "type", void 0), p3 = t4 = e([a("esri.ground.NavigationConstraint")], p3);

// node_modules/@arcgis/core/Ground.js
var C;
var S = C = class extends u(m) {
  constructor(r3) {
    super(r3), this.opacity = 1, this.shading = !t3.TERRAIN_USE_LEGACY_SHADING, this.surfaceColor = null, this.navigationConstraint = null, this.layers = new j();
    const e2 = (r4) => {
      r4.parent && r4.parent !== this && "remove" in r4.parent && r4.parent.remove(r4), r4.parent = this, "elevation" !== r4.type && "base-elevation" !== r4.type && s.getLogger(this.declaredClass).error(`Layer '${r4.title}, id:${r4.id}' of type '${r4.type}' is not supported as a ground layer and will therefore be ignored. Only layers of type 'elevation' are supported.`);
    }, t6 = (r4) => {
      r4.parent = null;
    };
    this.layers.on("after-add", (r4) => e2(r4.item)), this.layers.on("after-remove", (r4) => t6(r4.item));
  }
  initialize() {
    this.when().catch((r3) => {
      s.getLogger(this.declaredClass).error("#load()", "Failed to load ground", r3);
    }), this.resourceInfo && this.read(this.resourceInfo.data, this.resourceInfo.context);
  }
  destroy() {
    const r3 = this.layers.removeAll();
    for (const e2 of r3) e2.destroy();
    this.layers.destroy();
  }
  normalizeCtorArgs(r3) {
    return r3 && "resourceInfo" in r3 && (this._set("resourceInfo", r3.resourceInfo), delete (r3 = { ...r3 }).resourceInfo), r3;
  }
  set layers(r3) {
    this._set("layers", n2(r3, this._get("layers")));
  }
  writeLayers(r3, e2, o3, t6) {
    const i2 = [];
    r3 ? (t6 = { ...t6, layerContainerType: "ground" }, r3.forEach((r4) => {
      if ("write" in r4) {
        const e3 = {};
        o2(r4)().write(e3, t6) && i2.push(e3);
      } else t6 && t6.messages && t6.messages.push(new s2("layer:unsupported", `Layers (${r4.title}, ${r4.id}) of type '${r4.declaredClass}' cannot be persisted in the ground`, { layer: r4 }));
    }), e2.layers = i2) : e2.layers = i2;
  }
  load(r3) {
    return this.addResolvingPromise(this._loadFromSource(r3)), Promise.resolve(this);
  }
  loadAll() {
    return l3(this, (r3) => {
      r3(this.layers);
    });
  }
  async queryElevation(r3, e2) {
    await this.load({ signal: e2 == null ? void 0 : e2.signal });
    const { ElevationQuery: o3 } = await import("./ElevationQuery-4T42SFZD.js");
    f(e2);
    const t6 = new o3(), s4 = this.layers.filter(A).toArray();
    return t6.queryAll(s4, r3, e2);
  }
  async createElevationSampler(r3, e2) {
    await this.load({ signal: e2 == null ? void 0 : e2.signal });
    const { ElevationQuery: o3 } = await import("./ElevationQuery-4T42SFZD.js");
    f(e2);
    const t6 = new o3(), s4 = this.layers.filter(A).toArray();
    return t6.createSamplerAll(s4, r3, e2);
  }
  clone() {
    const r3 = { opacity: this.opacity, surfaceColor: p(this.surfaceColor), navigationConstraint: p(this.navigationConstraint), layers: this.layers.slice() };
    return this.loaded && (r3.loadStatus = "loaded"), new C({ resourceInfo: this.resourceInfo }).set(r3);
  }
  read(r3, e2) {
    this.resourceInfo || this._set("resourceInfo", { data: r3, context: e2 }), super.read(r3, e2);
  }
  _loadFromSource(r3) {
    const e2 = this.resourceInfo;
    return e2 ? this._loadLayersFromJSON(e2.data, e2.context, r3) : Promise.resolve();
  }
  _loadLayersFromJSON(r3, e2, o3) {
    const t6 = e2 && e2.origin || "web-scene", s4 = e2 && e2.portal || null, a2 = e2 && e2.url || null;
    return import("./layersCreator-2FCCT3FN.js").then(({ populateOperationalLayers: e3 }) => {
      f(o3);
      const i2 = [];
      if (r3.layers && Array.isArray(r3.layers)) {
        const o4 = { context: { origin: t6, url: a2, portal: s4, layerContainerType: "ground" }, defaultLayerType: "ArcGISTiledElevationServiceLayer" };
        i2.push(e3(this.layers, r3.layers, o4));
      }
      return E(i2);
    }).then(() => {
    });
  }
};
function I(r3) {
  return r3 && "createElevationSampler" in r3;
}
function A(r3) {
  return "elevation" === r3.type || I(r3);
}
e([y({ json: { read: false } })], S.prototype, "layers", null), e([r("layers")], S.prototype, "writeLayers", null), e([y({ readOnly: true })], S.prototype, "resourceInfo", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 1 }, json: { type: T, read: { reader: r2, source: "transparency" }, write: { writer: (r3, e2) => {
  e2.transparency = n3(r3);
}, target: "transparency" } } })], S.prototype, "opacity", void 0), e([y({ type: Boolean, nonNullable: true, json: { read: false } })], S.prototype, "shading", void 0), e([y({ type: l2, json: { type: [T], write: (r3, e2) => {
  e2.surfaceColor = r3.toJSON().slice(0, 3);
} } })], S.prototype, "surfaceColor", void 0), e([y({ type: p3, json: { write: true } })], S.prototype, "navigationConstraint", void 0), S = C = e([a("esri.Ground")], S);
var L = S;

// node_modules/@arcgis/core/layers/support/editableLayers.js
function n4(n5) {
  var _a, _b;
  return !(!(n5 == null ? void 0 : n5.loaded) || !((_b = (_a = v2(n5)) == null ? void 0 : _a.operations) == null ? void 0 : _b.supportsEditing) || "editingEnabled" in n5 && !w(n5));
}

// node_modules/@arcgis/core/support/groundUtils.js
var i = { "world-elevation": { id: "worldElevation", url: "//elevation3d.arcgis.com/arcgis/rest/services/WorldElevation3D/Terrain3D/ImageServer", layerType: "ArcGISTiledElevationServiceLayer" }, "world-topobathymetry": { id: "worldTopoBathymetry", url: "//elevation3d.arcgis.com/arcgis/rest/services/WorldElevation3D/TopoBathy3D/ImageServer", layerType: "ArcGISTiledElevationServiceLayer" } };
function t5(t6) {
  let a2 = null;
  if ("string" == typeof t6) if (t6 in i) {
    const r3 = i[t6];
    a2 = new L({ resourceInfo: { data: { layers: [r3] } } });
  } else s.getLogger("esri.support.groundUtils").warn(`Unable to find ground definition for: ${t6}. Try "world-elevation"`);
  else a2 = b(L, t6);
  return a2;
}

// node_modules/@arcgis/core/Map.js
var j2 = class extends l5(d(n.EventedMixin(v))) {
  constructor(s4) {
    super(s4), this.allLayers = new l4({ getCollections: () => {
      var _a, _b, _c;
      return [(_a = this.basemap) == null ? void 0 : _a.baseLayers, (_b = this.ground) == null ? void 0 : _b.layers, this.layers, (_c = this.basemap) == null ? void 0 : _c.referenceLayers];
    }, getChildrenFunction: (s5) => "layers" in s5 ? s5.layers : null }), this.allTables = t2(this), this.basemap = null, this.editableLayers = new l4({ getCollections: () => [this.allLayers], itemFilterFunction: n4 }), this.ground = new L(), this._basemapCache = p2();
  }
  destroy() {
    var _a, _b;
    this.allLayers.destroy(), this.allTables.destroy(), this.editableLayers.destroy(), (_a = this.ground) == null ? void 0 : _a.destroy(), (_b = this.basemap) == null ? void 0 : _b.destroy(), y2(this._basemapCache), this._basemapCache = null;
  }
  castBasemap(s4) {
    return m2(s4, this._basemapCache);
  }
  castGround(s4) {
    const e2 = t5(s4);
    return t(e2) ? this._get("ground") : e2;
  }
  findLayerById(s4) {
    return this.allLayers.find((e2) => e2.id === s4);
  }
  findTableById(s4) {
    return this.allTables.find((e2) => e2.id === s4);
  }
};
e([y({ readOnly: true, dependsOn: [] })], j2.prototype, "allLayers", void 0), e([y({ readOnly: true })], j2.prototype, "allTables", void 0), e([y({ type: x })], j2.prototype, "basemap", void 0), e([s3("basemap")], j2.prototype, "castBasemap", null), e([y({ readOnly: true })], j2.prototype, "editableLayers", void 0), e([y({ type: L, nonNullable: true })], j2.prototype, "ground", void 0), e([s3("ground")], j2.prototype, "castGround", null), j2 = e([a("esri.Map")], j2);
var L2 = j2;

export {
  L2 as L
};
//# sourceMappingURL=chunk-UXW4QK2C.js.map
