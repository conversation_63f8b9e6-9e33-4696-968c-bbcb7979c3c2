{"version": 3, "sources": ["../../@arcgis/core/views/layers/MapImageLayerView.js", "../../@arcgis/core/views/2d/layers/MapImageLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";import{combinedViewLayerTimeExtentProperty as o}from\"../../layers/support/commonProperties.js\";import{ExportImageParameters as s}from\"../../layers/support/ExportImageParameters.js\";const p=p=>{let a=class extends p{initialize(){this.exportImageParameters=new s({layer:this.layer})}destroy(){this.exportImageParameters.destroy(),this.exportImageParameters=null}get floors(){return this.view?.floors??null}get exportImageVersion(){return this.exportImageParameters?.commitProperty(\"version\"),this.commitProperty(\"timeExtent\"),this.commitProperty(\"floors\"),(this._get(\"exportImageVersion\")||0)+1}canResume(){return!!super.canResume()&&!this.timeExtent?.isEmpty}};return e([r()],a.prototype,\"exportImageParameters\",void 0),e([r({readOnly:!0})],a.prototype,\"floors\",null),e([r({readOnly:!0})],a.prototype,\"exportImageVersion\",null),e([r()],a.prototype,\"layer\",void 0),e([r()],a.prototype,\"suspended\",void 0),e([r(o)],a.prototype,\"timeExtent\",void 0),a=e([t(\"esri.views.layers.MapImageLayerView\")],a),a};export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import e from\"../../../core/Logger.js\";import{isAbortError as i}from\"../../../core/promiseUtils.js\";import{watch as r}from\"../../../core/reactiveUtils.js\";import{property as s}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as a}from\"../../../core/accessorSupport/decorators/subclass.js\";import{GraphicsCollection as h}from\"../../../support/GraphicsCollection.js\";import{BitmapContainer as o}from\"../engine/BitmapContainer.js\";import{LayerView2DMixin as p}from\"./LayerView2D.js\";import g from\"./graphics/GraphicsView2D.js\";import n from\"./graphics/HighlightGraphicContainer.js\";import l from\"./support/ExportStrategy.js\";import m from\"../../layers/LayerView.js\";import c from\"../../layers/MapImageLayerView.js\";import d from\"../../layers/RefreshableLayerView.js\";import{MapServiceLayerViewHelper as u}from\"../../layers/support/MapServiceLayerViewHelper.js\";import{createQueryGeometry as y}from\"../../support/drapedUtils.js\";let f=class extends(c(d(p(m)))){constructor(){super(...arguments),this._highlightGraphics=new h,this._updateHash=\"\"}fetchPopupFeatures(t,e){return this._popupHighlightHelper.fetchPopupFeatures(t,e)}update(t){const r=`${this.exportImageVersion}/${t.state.id}/${t.pixelRatio}/${t.stationary}`;this._updateHash!==r&&(this._updateHash=r,this.strategy.update(t).catch((t=>{i(t)||e.getLogger(this.declaredClass).error(t)})),t.stationary&&this._popupHighlightHelper.updateHighlightedFeatures(t.state.resolution)),this._highlightView.processUpdate(t)}attach(){const{imageMaxWidth:t,imageMaxHeight:e,version:i}=this.layer,s=i>=10.3,a=i>=10;this._bitmapContainer=new o,this.container.addChild(this._bitmapContainer),this._highlightView=new g({view:this.view,graphics:this._highlightGraphics,requestUpdateCallback:()=>this.requestUpdate(),container:new n(this.view.featuresTilingScheme),defaultPointSymbolEnabled:!1}),this.container.addChild(this._highlightView.container),this._popupHighlightHelper=new u({createFetchPopupFeaturesQueryGeometry:(t,e)=>y(t,e,this.view),highlightGraphics:this._highlightGraphics,highlightGraphicUpdated:(t,e)=>{this._highlightView.graphicUpdateHandler({graphic:t,property:e})},layerView:this,updatingHandles:this.updatingHandles}),this.strategy=new l({container:this._bitmapContainer,fetchSource:this.fetchImageBitmap.bind(this),requestUpdate:this.requestUpdate.bind(this),imageMaxWidth:t,imageMaxHeight:e,imageRotationSupported:s,imageNormalizationSupported:a,hidpi:!0}),this.addAttachHandles(r((()=>this.exportImageVersion),(()=>this.requestUpdate()))),this.requestUpdate()}detach(){this.strategy.destroy(),this.container.removeAllChildren(),this._bitmapContainer.removeAllChildren(),this._highlightView.destroy(),this._popupHighlightHelper.destroy()}moveStart(){}viewChange(){}moveEnd(){this.requestUpdate()}supportsSpatialReference(t){return this.layer.serviceSupportsSpatialReference(t)}async doRefresh(){this._updateHash=\"\",this.requestUpdate()}isUpdating(){return this.strategy.updating||this.updateRequested}fetchImage(t,e,i,r){return this.layer.fetchImage(t,e,i,{timeExtent:this.timeExtent,floors:this.floors,...r})}fetchImageBitmap(t,e,i,r){return this.layer.fetchImageBitmap(t,e,i,{timeExtent:this.timeExtent,floors:this.floors,...r})}highlight(t){return this._popupHighlightHelper.highlight(t)}};t([s()],f.prototype,\"strategy\",void 0),t([s()],f.prototype,\"updating\",void 0),f=t([a(\"esri.views.2d.layers.MapImageLayerView2D\")],f);const w=f;export{w as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+c,IAAM,IAAE,CAAAA,OAAG;AAAC,MAAIC,KAAE,cAAcD,GAAC;AAAA,IAAC,aAAY;AAAC,WAAK,wBAAsB,IAAI,EAAE,EAAC,OAAM,KAAK,MAAK,CAAC;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,WAAK,sBAAsB,QAAQ,GAAE,KAAK,wBAAsB;AAAA,IAAI;AAAA,IAAC,IAAI,SAAQ;AAJ9oB;AAI+oB,eAAO,UAAK,SAAL,mBAAW,WAAQ;AAAA,IAAI;AAAA,IAAC,IAAI,qBAAoB;AAJtsB;AAIusB,cAAO,UAAK,0BAAL,mBAA4B,eAAe,YAAW,KAAK,eAAe,YAAY,GAAE,KAAK,eAAe,QAAQ,IAAG,KAAK,KAAK,oBAAoB,KAAG,KAAG;AAAA,IAAC;AAAA,IAAC,YAAW;AAJt3B;AAIu3B,aAAM,CAAC,CAAC,MAAM,UAAU,KAAG,GAAC,UAAK,eAAL,mBAAiB;AAAA,IAAO;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,CAAC,GAAEC,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,sBAAqB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,GAAEA,EAAC,GAAEA;AAAC;;;ACA9L,IAAIC,KAAE,cAAc,EAAEC,GAAE,EAAE,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,qBAAmB,IAAI,KAAE,KAAK,cAAY;AAAA,EAAE;AAAA,EAAC,mBAAmB,GAAEC,IAAE;AAAC,WAAO,KAAK,sBAAsB,mBAAmB,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,GAAE;AAAC,UAAM,IAAE,GAAG,KAAK,kBAAkB,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,IAAI,EAAE,UAAU;AAAG,SAAK,gBAAc,MAAI,KAAK,cAAY,GAAE,KAAK,SAAS,OAAO,CAAC,EAAE,MAAO,CAAAC,OAAG;AAAC,QAAEA,EAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,EAAC;AAAA,IAAC,CAAE,GAAE,EAAE,cAAY,KAAK,sBAAsB,0BAA0B,EAAE,MAAM,UAAU,IAAG,KAAK,eAAe,cAAc,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAK,EAAC,eAAc,GAAE,gBAAeD,IAAE,SAAQD,GAAC,IAAE,KAAK,OAAMG,KAAEH,MAAG,MAAKI,KAAEJ,MAAG;AAAG,SAAK,mBAAiB,IAAII,MAAE,KAAK,UAAU,SAAS,KAAK,gBAAgB,GAAE,KAAK,iBAAe,IAAI,GAAE,EAAC,MAAK,KAAK,MAAK,UAAS,KAAK,oBAAmB,uBAAsB,MAAI,KAAK,cAAc,GAAE,WAAU,IAAI,EAAE,KAAK,KAAK,oBAAoB,GAAE,2BAA0B,MAAE,CAAC,GAAE,KAAK,UAAU,SAAS,KAAK,eAAe,SAAS,GAAE,KAAK,wBAAsB,IAAI,EAAE,EAAC,uCAAsC,CAACF,IAAED,OAAIG,GAAEF,IAAED,IAAE,KAAK,IAAI,GAAE,mBAAkB,KAAK,oBAAmB,yBAAwB,CAACC,IAAED,OAAI;AAAC,WAAK,eAAe,qBAAqB,EAAC,SAAQC,IAAE,UAASD,GAAC,CAAC;AAAA,IAAC,GAAE,WAAU,MAAK,iBAAgB,KAAK,gBAAe,CAAC,GAAE,KAAK,WAAS,IAAI,EAAE,EAAC,WAAU,KAAK,kBAAiB,aAAY,KAAK,iBAAiB,KAAK,IAAI,GAAE,eAAc,KAAK,cAAc,KAAK,IAAI,GAAE,eAAc,GAAE,gBAAeA,IAAE,wBAAuBE,IAAE,6BAA4BC,IAAE,OAAM,KAAE,CAAC,GAAE,KAAK,iBAAiB,EAAG,MAAI,KAAK,oBAAqB,MAAI,KAAK,cAAc,CAAE,CAAC,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,SAAS,QAAQ,GAAE,KAAK,UAAU,kBAAkB,GAAE,KAAK,iBAAiB,kBAAkB,GAAE,KAAK,eAAe,QAAQ,GAAE,KAAK,sBAAsB,QAAQ;AAAA,EAAC;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,aAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,yBAAyB,GAAE;AAAC,WAAO,KAAK,MAAM,gCAAgC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAW;AAAC,SAAK,cAAY,IAAG,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,SAAS,YAAU,KAAK;AAAA,EAAe;AAAA,EAAC,WAAW,GAAEH,IAAED,IAAE,GAAE;AAAC,WAAO,KAAK,MAAM,WAAW,GAAEC,IAAED,IAAE,EAAC,YAAW,KAAK,YAAW,QAAO,KAAK,QAAO,GAAG,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiB,GAAEC,IAAED,IAAE,GAAE;AAAC,WAAO,KAAK,MAAM,iBAAiB,GAAEC,IAAED,IAAE,EAAC,YAAW,KAAK,YAAW,QAAO,KAAK,QAAO,GAAG,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAU,GAAE;AAAC,WAAO,KAAK,sBAAsB,UAAU,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["p", "a", "f", "i", "e", "t", "s", "a"]}