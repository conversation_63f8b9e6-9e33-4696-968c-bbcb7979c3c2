import {
  a as a4,
  c,
  e as e3,
  e2 as e4,
  i2 as i,
  n as n3,
  o,
  p as p2,
  t as t3,
  t2 as t4
} from "./chunk-IZLLLMFE.js";
import {
  autoMode,
  darkMode
} from "./chunk-K4QGLA2K.js";
import {
  setAssetPath
} from "./chunk-5XZZKPPL.js";
import {
  n as n2
} from "./chunk-PCLDCFRI.js";
import {
  u
} from "./chunk-DTQ34PEY.js";
import {
  a as a3
} from "./chunk-FZ7BG3VX.js";
import {
  f as f2,
  h,
  l
} from "./chunk-QUHG7NMD.js";
import {
  s as s3
} from "./chunk-5GX2JMCX.js";
import {
  m
} from "./chunk-EDS4WCRT.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  s as s2
} from "./chunk-7SWS36OI.js";
import {
  F
} from "./chunk-U4SVMKOQ.js";
import {
  e,
  t2 as t
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  t2
} from "./chunk-C5VMWMBD.js";
import {
  f
} from "./chunk-2CM7MIII.js";
import {
  E,
  b,
  e as e2,
  r2 as r,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  a
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/domUtils.js
function e5(e11) {
  return "string" == typeof e11 ? document.getElementById(e11) : e11 ?? null;
}
function t5(e11) {
  for (; e11.hasChildNodes(); ) e11.removeChild(e11.firstChild);
}
function o2(e11, t6) {
  const n6 = t6.parentNode;
  n6 && n6.insertBefore(e11, t6);
}
function r2(e11, t6) {
  for (; ; ) {
    const n6 = e11.firstChild;
    if (!n6) break;
    t6.appendChild(n6);
  }
}
function l2(e11) {
  e11.parentNode && e11.parentNode.removeChild(e11);
}
var i2 = (() => {
  if ("function" == typeof Element.prototype.closest) return (e12, t6) => e12.closest(t6);
  const e11 = Element.prototype.matches || Element.prototype.msMatchesSelector;
  return (t6, n6) => {
    let o4 = t6;
    do {
      if (e11.call(o4, n6)) return o4;
      o4 = o4.parentElement;
    } while (null !== o4 && 1 === o4.nodeType);
    return null;
  };
})();

// node_modules/@arcgis/core/libs/maquette-advanced-projector/advanced-projector-options.js
var e6 = { handleInterceptedEvent: (e11, p3, t6, n6) => (e11.scheduleRender(), p3.properties[`on${n6.type}`].apply(p3.properties.bind || t6, [n6])) };

// node_modules/@arcgis/core/libs/maquette-advanced-projector/utils.js
var e7 = { namespace: void 0, performanceLogger: () => {
}, eventHandlerInterceptor: void 0, styleApplyer: (e11, r5, o4) => {
  e11.style[r5] = o4;
} };
var r3 = (r5) => ({ ...e7, ...r5 });

// node_modules/@arcgis/core/libs/maquette-advanced-projector/projector.js
var o3 = (e11, t6) => {
  const r5 = [];
  for (; e11 && e11 !== t6; ) r5.push(e11), e11 = e11.parentNode;
  return r5;
};
var n4 = (e11, t6) => e11.find(t6);
var d = (e11, t6, r5 = false) => {
  let o4 = e11;
  return t6.forEach((e12, d2) => {
    const s5 = (o4 == null ? void 0 : o4.children) ? n4(o4.children, (t7) => t7.domNode === e12) : void 0;
    r5 && !s5 && d2 !== t6.length - 1 || (o4 = s5);
  }), o4;
};
var s4 = (n6) => {
  let s5;
  const i3 = { ...e6, ...n6 }, c2 = r3(i3), a5 = c2.performanceLogger;
  let m2, p3 = true, l3 = false;
  const f3 = [], u2 = [], h2 = (e11, t6, r5) => {
    var _a;
    let n7;
    c2.eventHandlerInterceptor = (e12, t7, r6, c3) => function(e13) {
      let t8;
      a5("domEvent", e13);
      const r7 = o3(e13.currentTarget, n7.domNode), c4 = r7.some((e14) => {
        var _a2;
        return customElements.get((_a2 = e14 == null ? void 0 : e14.tagName) == null ? void 0 : _a2.toLowerCase());
      });
      if (e13.eventPhase === Event.CAPTURING_PHASE || !c4) r7.reverse(), t8 = d(n7.getLastRender(), r7);
      else {
        const r8 = e13.composedPath(), o4 = r8.slice(r8.indexOf(e13.currentTarget), r8.indexOf(n7.domNode)).filter((e14) => e14.getRootNode() === e14.ownerDocument).reverse();
        t8 = d(n7.getLastRender(), o4, true);
      }
      let m4;
      return t8 && (m4 = i3.handleInterceptedEvent(s5, t8, this, e13)), a5("domEventProcessed", e13), m4;
    }, (_a = i3.postProcessProjectionOptions) == null ? void 0 : _a.call(i3, c2);
    const m3 = r5();
    n7 = e11(t6, m3, c2), f3.push(n7), u2.push(r5), i3.afterFirstVNodeRendered && i3.afterFirstVNodeRendered(n7, m3);
  };
  let v = () => {
    if (m2 = void 0, p3) {
      p3 = false, a5("renderStart", void 0);
      for (let e11 = 0; e11 < f3.length; e11++) {
        const t6 = u2[e11]();
        a5("rendered", void 0), f3[e11].update(t6), a5("patched", void 0);
      }
      a5("renderDone", void 0), p3 = true;
    }
  };
  return i3.modifyDoRenderImplementation && (v = i3.modifyDoRenderImplementation(v, f3, u2)), s5 = { renderNow: v, scheduleRender: () => {
    m2 || l3 || (m2 = requestAnimationFrame(v));
  }, stop: () => {
    m2 && (cancelAnimationFrame(m2), m2 = void 0), l3 = true;
  }, resume: () => {
    l3 = false, p3 = true, s5.scheduleRender();
  }, append: (t6, r5) => {
    h2(n3.append, t6, r5);
  }, insertBefore: (t6, r5) => {
    h2(n3.insertBefore, t6, r5);
  }, merge: (t6, r5) => {
    h2(n3.merge, t6, r5);
  }, replace: (t6, r5) => {
    h2(n3.replace, t6, r5);
  }, detach: (e11) => {
    for (let t6 = 0; t6 < u2.length; t6++) if (u2[t6] === e11) return u2.splice(t6, 1), f3.splice(t6, 1)[0];
    throw new Error("renderFunction was not found");
  } }, s5;
};

// node_modules/@esri/calcite-components/dist/components/index.js
function initModeChangeEvent() {
  const { classList } = document.body;
  const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
  const getMode = () => classList.contains(darkMode) || classList.contains(autoMode) && prefersDark ? "dark" : "light";
  const emitModeChange = (mode) => document.body.dispatchEvent(new CustomEvent("calciteModeChange", { bubbles: true, detail: { mode } }));
  const modeChangeHandler = (newMode) => {
    currentMode !== newMode && emitModeChange(newMode);
    currentMode = newMode;
  };
  let currentMode = getMode();
  emitModeChange(currentMode);
  window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change", (event) => modeChangeHandler(event.matches ? "dark" : "light"));
  new MutationObserver(() => modeChangeHandler(getMode())).observe(document.body, {
    attributes: true,
    attributeFilter: ["class"]
  });
}
function appGlobalScript() {
  const isBrowser = typeof window !== "undefined" && typeof location !== "undefined" && typeof document !== "undefined" && window.location === location && window.document === document;
  if (isBrowser) {
    if (document.readyState === "interactive") {
      initModeChangeEvent();
    } else {
      document.addEventListener("DOMContentLoaded", () => initModeChangeEvent(), { once: true });
    }
  }
}
var globalScripts = appGlobalScript;
globalScripts();

// node_modules/@arcgis/core/widgets/support/componentsUtils.js
var e8;
function r4() {
  setAssetPath(F(a3(e8)));
}
e8 = "components/assets";

// node_modules/@arcgis/core/widgets/support/tests.js
var e9 = /* @__PURE__ */ new Set();
function n5(n6) {
  e9.add(n6), n6.finally(() => e9.delete(n6));
}

// node_modules/@arcgis/core/widgets/Widget.js
var $;
var x2 = "esri.widgets.Widget";
var z = 0;
var A = { widgetIcon: "esri-icon-checkbox-unchecked" };
function B(e11, t6) {
  for (const r5 in t6) null != e11[r5] && ("object" == typeof e11[r5] && "object" == typeof t6[r5] ? B(e11[r5], t6 == null ? void 0 : t6[r5]) : e11[r5] = t6[r5]);
  return e11;
}
var D = s4({ postProcessProjectionOptions(e11) {
  const t6 = e11.eventHandlerInterceptor, r5 = /capture$/i;
  e11.eventHandlerInterceptor = (e12, s5, o4, i3) => {
    const n6 = t6 == null ? void 0 : t6(e12, s5, o4, i3), a5 = r5.test(e12);
    if (!((e12 = e12.replace(r5, "")).toLowerCase() in o4) || a5) {
      const t7 = e12[2].toLowerCase() + e12.slice(3), r6 = (e13) => n6 == null ? void 0 : n6.call(o4, e13);
      o4.addEventListener(t7, r6, a5);
      const s6 = () => o4.removeEventListener(t7, r6, a5), c2 = i3.afterRemoved;
      i3.afterRemoved = (e13) => {
        c2 == null ? void 0 : c2(e13), s6();
      };
    }
    return n6;
  };
}, handleInterceptedEvent(e11, t6, r5, s5) {
  const { eventPhase: o4, type: i3 } = s5, n6 = o4 === Event.CAPTURING_PHASE;
  let a5 = `on${i3}${n6 ? "capture" : ""}`;
  const c2 = t6.properties;
  (c2 && a5 in c2 || (a5 = `on${i3[0].toUpperCase()}${i3.slice(1)}${n6 ? "Capture" : ""}`, c2 && a5 in c2)) && (t3(), e11.scheduleRender(), c2[a5].call(c2.bind || r5, s5));
} });
var M = false;
var O = class extends m(n.EventedAccessor) {
  constructor(e11, t6) {
    super(e11, t6), this._attached = false, this._internalHandles = new t(), this._projector = D, this._readyForTrueRender = false, this.iconClass = A.widgetIcon, this.key = this, this._loadLocale = x(async () => {
      if (this._messageBundleProps && this._messageBundleProps.length) {
        const e12 = await E(this._messageBundleProps.map(async ({ bundlePath: e13, propertyName: t7 }) => {
          let r6 = await u(e13);
          this.uiStrings && Object.keys(this.uiStrings) && (r6 = B(p(r6), this.uiStrings)), this[t7] = r6;
        }));
        for (const t7 of e12) t7.error && s.getLogger(this.declaredClass).error("widget-intl:locale-error", this.declaredClass, t7.error);
      }
      await this.loadLocale();
    }), r4();
    const r5 = "esri-widget-uid-" + n2(), s5 = this.render.bind(this);
    this._trackingTarget = new t2(() => this.scheduleRender());
    const o4 = () => {
      var _a;
      if (!this._readyForTrueRender || this.destroyed) return null;
      if (!this.visible) return { vnodeSelector: "div", properties: { key: r5, class: "", styles: { display: "none" } }, domNode: null, children: void 0, text: void 0 };
      const e12 = s5();
      let { properties: t7 } = e12;
      t7 || (e12.properties = t7 = {});
      let { key: o5, styles: i3 } = t7;
      o5 || (t7.key = r5), i3 || (t7.styles = i3 = {}), i3.display || (i3.display = "");
      let n6 = 0;
      return (_a = e12.children) == null ? void 0 : _a.forEach((e13) => {
        if (a4(e13.vnodeSelector)) return;
        let { properties: t8 } = e13;
        t8 || (e13.properties = t8 = {}), t8.key || (t8.key = `${this.id}--${n6++}`);
      }), i(this, e12);
    };
    this.render = () => {
      if (M) return o4();
      let e12 = e3(this) ?? null;
      if (e12) return e12;
      this._trackingTarget.clear(), M = true;
      try {
        e12 = f(this._trackingTarget, o4);
      } catch (t7) {
        throw console.error(t7), t7;
      } finally {
        M = false;
      }
      return e12 && c(this, e12), e12;
    }, this.addResolvingPromise(this._resourcesFetch = this.beforeFirstRender().then(() => {
      this._readyForTrueRender = true, this._postInitialize();
    })), n5(this._resourcesFetch);
  }
  normalizeCtorArgs(e11, t6) {
    const r5 = { ...e11 };
    return t6 && (r5.container = t6), r5;
  }
  postInitialize() {
  }
  beforeFirstRender() {
    return Promise.all([this.loadDependencies(), this._loadLocale()]).then(() => {
    }).catch(b);
  }
  async loadDependencies() {
  }
  async loadLocale() {
  }
  destroy() {
    this.destroyed || (a(this._trackingTarget), a(this.viewModel), this._detach(this.container), this._set("container", null), this._internalHandles.destroy(), this._emitter.clear(), this.render = () => null, this._projector = null, o(this));
  }
  set container(e11) {
    this._get("container") || this._set("container", e11);
  }
  castContainer(e11) {
    return e5(e11);
  }
  get domNode() {
    return this.container;
  }
  set domNode(e11) {
    this.container = e11;
  }
  get id() {
    return this._get("id") || this.get("container.id") || Date.now().toString(16) + "-widget-" + z++;
  }
  set id(e11) {
    e11 && this._set("id", e11);
  }
  get label() {
    return this.declaredClass.split(".").pop();
  }
  set label(e11) {
    this._overrideIfSome("label", e11);
  }
  get renderable() {
    return this._resourcesFetch;
  }
  get visible() {
    return this._get("visible");
  }
  set visible(e11) {
    this._set("visible", e11);
  }
  get [($ = t4, e4)]() {
    return { projector: this._projector };
  }
  render() {
    throw new Error("not implemented");
  }
  scheduleRender() {
    this.destroyed || (o(this), this._projector.scheduleRender());
  }
  classes(...e11) {
    return p2.apply(this, e11);
  }
  renderNow() {
    o(this), this._projector.renderNow();
  }
  _postInitialize() {
    var _a;
    if (this.destroyed) return;
    this.scheduleRender(), ((_a = this._delegatedEventNames) == null ? void 0 : _a.length) && this._internalHandles.add(l(() => this.viewModel, (e12, t6) => {
      t6 && this._internalHandles.remove("delegated-events"), e12 && e2(e12) && this._internalHandles.add(this._delegatedEventNames.map((t7) => r(e12, t7, (e13) => {
        this.emit(t7, e13);
      })), "delegated-events");
    }, h)), this.postInitialize();
    const e11 = async () => {
      await this._loadLocale().catch(b), this.scheduleRender();
    };
    this._internalHandles.add([s3(e11), l(() => this.uiStrings, e11), f2(() => this.container, (e12) => {
      this.destroyed || this._attach(e12);
    }, { initial: true, once: true })]);
  }
  _attach(e11) {
    e11 && (this._projector.merge(e11, this.render), this._attached = true);
  }
  _detach(e11) {
    var _a;
    this._attached && (this._projector.detach(this.render), this._attached = false), (_a = e11 == null ? void 0 : e11.parentNode) == null ? void 0 : _a.removeChild(e11);
  }
};
O[$] = true, e([y()], O.prototype, "_readyForTrueRender", void 0), e([y({ value: null })], O.prototype, "container", null), e([s2("container")], O.prototype, "castContainer", null), e([y()], O.prototype, "iconClass", void 0), e([y()], O.prototype, "id", null), e([y()], O.prototype, "label", null), e([y()], O.prototype, "renderable", null), e([y()], O.prototype, "uiStrings", void 0), e([y()], O.prototype, "viewModel", void 0), e([y({ value: true })], O.prototype, "visible", null), e([y()], O.prototype, "key", void 0), e([y()], O.prototype, "children", void 0), e([y()], O.prototype, "afterCreate", void 0), e([y()], O.prototype, "afterUpdate", void 0), e([y()], O.prototype, "afterRemoved", void 0), O = e([a2(x2)], O);
var W = O;

// node_modules/@arcgis/core/widgets/support/decorators/messageBundle.js
function e10(e11) {
  return (s5, r5) => {
    s5.hasOwnProperty("_messageBundleProps") || (s5._messageBundleProps = s5._messageBundleProps ? s5._messageBundleProps.slice() : []);
    s5._messageBundleProps.push({ bundlePath: e11, propertyName: r5 });
  };
}

export {
  e5 as e,
  t5 as t,
  o2 as o,
  r2 as r,
  l2 as l,
  W,
  e10 as e2
};
/*! Bundled license information:

@esri/calcite-components/dist/components/index.js:
  (*!
   * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
   * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
   * v1.0.8-next.4
   *)
*/
//# sourceMappingURL=chunk-NQQSL2QK.js.map
