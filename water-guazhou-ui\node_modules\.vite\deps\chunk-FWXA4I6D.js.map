{"version": 3, "sources": ["../../@arcgis/core/layers/support/FeatureType.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import{ClonableMixin as r}from\"../../core/Clonable.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as e}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as p}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as i}from\"../../core/accessorSupport/decorators/writer.js\";import{fromJSON as a}from\"./domains.js\";import m from\"./FeatureTemplate.js\";let c=class extends(r(t)){constructor(o){super(o),this.id=null,this.name=null,this.domains=null,this.templates=null}readDomains(o){const r={};for(const t of Object.keys(o))r[t]=a(o[t]);return r}writeDomains(o,r){const t={};for(const s of Object.keys(o))o[s]&&(t[s]=o[s]?.toJSON());r.domains=t}};o([s({json:{write:!0}})],c.prototype,\"id\",void 0),o([s({json:{write:!0}})],c.prototype,\"name\",void 0),o([s({json:{write:!0}})],c.prototype,\"domains\",void 0),o([e(\"domains\")],c.prototype,\"readDomains\",null),o([i(\"domains\")],c.prototype,\"writeDomains\",null),o([s({type:[m],json:{write:!0}})],c.prototype,\"templates\",void 0),c=o([p(\"esri.layers.support.FeatureType\")],c);const n=c;export{n as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAIumB,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,KAAG,MAAK,KAAK,OAAK,MAAK,KAAK,UAAQ,MAAK,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAU,KAAK,OAAO,KAAKD,EAAC,EAAE,CAAAC,GAAE,CAAC,IAAEC,GAAEF,GAAE,CAAC,CAAC;AAAE,WAAOC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAEC,IAAE;AAJ1zB;AAI2zB,UAAM,IAAE,CAAC;AAAE,eAAU,KAAK,OAAO,KAAKD,EAAC,EAAE,CAAAA,GAAE,CAAC,MAAI,EAAE,CAAC,KAAE,KAAAA,GAAE,CAAC,MAAH,mBAAM;AAAU,IAAAC,GAAE,UAAQ;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,SAAS,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,SAAS,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,iCAAiC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["o", "r", "i"]}