import {
  r as r7
} from "./chunk-JXO7W6XW.js";
import {
  p,
  s as s4
} from "./chunk-QQS4HCWF.js";
import {
  e as e3,
  e2 as e4
} from "./chunk-5LZTDVVY.js";
import {
  e as e2
} from "./chunk-J6VS6FXY.js";
import {
  c as c2
} from "./chunk-AZEN5UFW.js";
import {
  ee
} from "./chunk-AOYBG2OC.js";
import {
  M as M2,
  w
} from "./chunk-O2JKCGK6.js";
import {
  l as l3,
  n as n3,
  r as r5
} from "./chunk-53FPJYCC.js";
import {
  C as C3,
  N as N2,
  P as P2,
  U as U2,
  Z as Z2,
  w as w2
} from "./chunk-34BE5ZRD.js";
import {
  E2 as E5,
  f as f2,
  s as s3,
  x
} from "./chunk-6G2NLXT7.js";
import {
  t as t4
} from "./chunk-IEBU4QQL.js";
import {
  h as h2,
  r as r6
} from "./chunk-QKWIBVLD.js";
import {
  E as E3,
  S,
  T
} from "./chunk-WAPZ634R.js";
import {
  E as E4
} from "./chunk-FTRLEBHJ.js";
import {
  r as r3
} from "./chunk-PWCXATLS.js";
import {
  $,
  A,
  B as B2,
  C as C2,
  D as D2,
  E as E2,
  F as F2,
  G as G2,
  K,
  L as L2,
  Z,
  _ as _2,
  at,
  o
} from "./chunk-RRNRSHX3.js";
import {
  B,
  C,
  D,
  E,
  F,
  G,
  I,
  L,
  M,
  N,
  O,
  P,
  R,
  U,
  V,
  Y,
  _
} from "./chunk-4M3AMTD4.js";
import {
  n,
  t as t5
} from "./chunk-DUEDINK5.js";
import {
  e
} from "./chunk-MZ267CZB.js";
import {
  r as r4
} from "./chunk-QCTKOQ44.js";
import {
  r as r2
} from "./chunk-SROTSYJS.js";
import {
  n as n2
} from "./chunk-FOE4ICAJ.js";
import {
  W,
  X
} from "./chunk-B4KDIR4O.js";
import {
  t as t3
} from "./chunk-SEO6KEGF.js";
import {
  l as l2
} from "./chunk-QUHG7NMD.js";
import {
  f
} from "./chunk-EIGTETCG.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  t as t2
} from "./chunk-GZGAQUSK.js";
import {
  c,
  h,
  l,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/engine/webgl/VertexStream.js
var n4 = class {
  constructor(s7, n8) {
    this._rctx = s7, this._vertexBuffer = E5.createVertex(s7, F.STATIC_DRAW, new Uint16Array(n8)), this._vao = new f2(s7, /* @__PURE__ */ new Map([["a_position", 0]]), { geometry: [new t4("a_position", 2, C.SHORT, 0, 4)] }, { geometry: this._vertexBuffer }), this._count = n8.length / 2;
  }
  bind() {
    this._rctx.bindVAO(this._vao);
  }
  unbind() {
    this._rctx.bindVAO(null);
  }
  dispose() {
    this._vao.dispose(false), this._vertexBuffer.dispose();
  }
  draw() {
    this._rctx.bindVAO(this._vao), this._rctx.drawArrays(E.TRIANGLE_STRIP, 0, this._count);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrush.js
var t6 = class {
  constructor() {
    this.name = this.constructor.name || "UnnamedBrush", this.brushEffect = null;
  }
  prepareState(t10, r10) {
  }
  draw(t10, r10, s7) {
  }
  drawMany(t10, r10, s7) {
    for (const a2 of r10) a2.visible && this.draw(t10, a2, s7);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/BrushBitmap.js
var o2 = { nearest: { defines: [], samplingMode: L.NEAREST, mips: false }, bilinear: { defines: [], samplingMode: L.LINEAR, mips: false }, bicubic: { defines: ["bicubic"], samplingMode: L.LINEAR, mips: false }, trilinear: { defines: [], samplingMode: L.LINEAR_MIPMAP_LINEAR, mips: true } };
var d = (e7, t10, i2) => {
  if ("dynamic" === i2.samplingMode) {
    const { state: i3 } = e7, s7 = t10.resolution / t10.pixelRatio / i3.resolution, n8 = Math.round(e7.pixelRatio) !== e7.pixelRatio, r10 = s7 > 1.05 || s7 < 0.95;
    return i3.rotation || r10 || n8 || t10.isSourceScaled || t10.rotation ? o2.bilinear : o2.nearest;
  }
  return o2[i2.samplingMode];
};
var m = class extends t6 {
  constructor() {
    super(...arguments), this._desc = { vsPath: "raster/bitmap", fsPath: "raster/bitmap", attributes: /* @__PURE__ */ new Map([["a_pos", 0]]) };
  }
  dispose() {
    this._quad && this._quad.dispose();
  }
  prepareState({ context: e7 }) {
    e7.setBlendingEnabled(true), e7.setColorMask(true, true, true, true), e7.setStencilWriteMask(0), e7.setStencilTestEnabled(true);
  }
  draw(s7, n8) {
    const { context: o4, renderingOptions: m9, painter: l6, requestRender: c9, allowDelayedRender: p4 } = s7;
    if (!n8.source || !n8.isReady) return;
    const u3 = d(s7, n8, m9), f3 = l6.materialManager.getProgram(this._desc, u3.defines);
    if (p4 && r(c9) && !f3.compiled) return void c9();
    s7.timeline.begin(this.name), "additive" === n8.blendFunction ? o4.setBlendFunctionSeparate(R.ONE, R.ONE, R.ONE, R.ONE) : o4.setBlendFunctionSeparate(R.ONE, R.ONE_MINUS_SRC_ALPHA, R.ONE, R.ONE_MINUS_SRC_ALPHA), o4.setStencilFunction(I.EQUAL, n8.stencilRef, 255), this._quad || (this._quad = new n4(o4, [0, 0, 1, 0, 0, 1, 1, 1]));
    const { coordScale: _3, computedOpacity: E6, transforms: M4 } = n8;
    n8.setSamplingProfile(u3), n8.bind(s7.context, A), o4.useProgram(f3), f3.setUniformMatrix3fv("u_dvsMat3", M4.dvs), f3.setUniform1i("u_texture", A), f3.setUniform2fv("u_coordScale", _3), f3.setUniform1f("u_opacity", E6), this._quad.draw(), s7.timeline.end(this.name);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/shaders/sources/shaderRepository.js
var e5 = { background: { "background.frag": "uniform lowp vec4 u_color;\nvoid main() {\ngl_FragColor = u_color;\n}", "background.vert": "attribute vec2 a_pos;\nuniform highp mat3 u_dvsMat3;\nuniform mediump vec2 u_coord_range;\nuniform mediump float u_depth;\nvoid main() {\nvec3 v_pos = u_dvsMat3 * vec3(u_coord_range * a_pos, 1.0);\ngl_Position = vec4(v_pos.xy, 0.0, 1.0);\n}" }, bitBlit: { "bitBlit.frag": "uniform lowp sampler2D u_tex;\nuniform lowp float u_opacity;\nvarying mediump vec2 v_uv;\nvoid main() {\nlowp vec4 color = texture2D(u_tex, v_uv);\ngl_FragColor = color *  u_opacity;\n}", "bitBlit.vert": "attribute vec2 a_pos;\nattribute vec2 a_tex;\nvarying mediump vec2 v_uv;\nvoid main(void) {\ngl_Position = vec4(a_pos, 0.0, 1.0);\nv_uv = a_tex;\n}" }, blend: { "blend.frag": "precision mediump float;\nuniform sampler2D u_layerTexture;\nuniform lowp float u_opacity;\nuniform lowp float u_inFadeOpacity;\n#ifndef NORMAL\nuniform sampler2D u_backbufferTexture;\n#endif\nvarying mediump vec2 v_uv;\nfloat rgb2v(in vec3 c) {\nreturn max(c.x, max(c.y, c.z));\n}\nvec3 rgb2hsv(in vec3 c) {\nvec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);\nvec4 p = c.g < c.b ? vec4(c.bg, K.wz) : vec4(c.gb, K.xy);\nvec4 q = c.r < p.x ? vec4(p.xyw, c.r) : vec4(c.r, p.yzx);\nfloat d = q.x - min(q.w, q.y);\nfloat e = 1.0e-10;\nreturn vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), min(d / (q.x + e), 1.0), q.x);\n}\nvec3 hsv2rgb(in vec3 c) {\nvec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);\nvec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);\nreturn c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);\n}\nvec3 tint(in vec3 Cb, in vec3 Cs) {\nfloat vIn = rgb2v(Cb);\nvec3 hsvTint = rgb2hsv(Cs);\nvec3 hsvOut = vec3(hsvTint.x, hsvTint.y, vIn * hsvTint.z);\nreturn hsv2rgb(hsvOut);\n}\nfloat overlay(in float Cb, in float Cs) {\nreturn (1.0 - step(0.5, Cs)) * (1.0 - 2.0 * (1.0 - Cs ) * (1.0 - Cb)) + step(0.5, Cs) * (2.0 * Cs * Cb);\n}\nfloat colorDodge(in float Cb, in float Cs) {\nreturn (Cb == 0.0) ? 0.0 : (Cs == 1.0) ? 1.0 : min(1.0, Cb / (1.0 - Cs));\n}\nfloat colorBurn(in float Cb, in float Cs) {\nreturn (Cb == 1.0) ? 1.0 : (Cs == 0.0) ? 0.0 : 1.0 - min(1.0, (1.0 - Cb) / Cs);\n}\nfloat hardLight(in float Cb, in float Cs) {\nreturn (1.0 - step(0.5, Cs)) * (2.0 * Cs * Cb) + step(0.5, Cs) * (1.0 - 2.0 * (1.0 - Cs) * (1.0 - Cb));\n}\nfloat reflectBlend(in float Cb, in float Cs) {\nreturn (Cs == 1.0) ? Cs : min(Cb * Cb / (1.0 - Cs), 1.0);\n}\nfloat softLight(in float Cb, in float Cs) {\nif (Cs <= 0.5) {\nreturn Cb - (1.0 - 2.0 * Cs) * Cb * (1.0 - Cb);\n}\nif (Cb <= 0.25) {\nreturn Cb + (2.0 * Cs - 1.0) * Cb * ((16.0 * Cb - 12.0) * Cb + 3.0);\n}\nreturn Cb + (2.0 * Cs - 1.0) * (sqrt(Cb) - Cb);\n}\nfloat vividLight(in float Cb, in float Cs) {\nreturn (1.0 - step(0.5, Cs)) * colorBurn(Cb, 2.0 * Cs) + step(0.5, Cs) * colorDodge(Cb, (2.0 * (Cs - 0.5)));\n}\nfloat minv3(in vec3 c) {\nreturn min(min(c.r, c.g), c.b);\n}\nfloat maxv3(in vec3 c) {\nreturn max(max(c.r, c.g), c.b);\n}\nfloat lumv3(in vec3 c) {\nreturn dot(c, vec3(0.3, 0.59, 0.11));\n}\nfloat satv3(vec3 c) {\nreturn maxv3(c) - minv3(c);\n}\nvec3 clipColor(vec3 color) {\nfloat lum = lumv3(color);\nfloat mincol = minv3(color);\nfloat maxcol = maxv3(color);\nif (mincol < 0.0) {\ncolor = lum + ((color - lum) * lum) / (lum - mincol);\n}\nif (maxcol > 1.0) {\ncolor = lum + ((color - lum) * (1.0 - lum)) / (maxcol - lum);\n}\nreturn color;\n}\nvec3 setLum(vec3 cbase, vec3 clum) {\nfloat lbase = lumv3(cbase);\nfloat llum = lumv3(clum);\nfloat ldiff = llum - lbase;\nvec3 color = cbase + vec3(ldiff);\nreturn clipColor(color);\n}\nvec3 setLumSat(vec3 cbase, vec3 csat, vec3 clum)\n{\nfloat minbase = minv3(cbase);\nfloat sbase = satv3(cbase);\nfloat ssat = satv3(csat);\nvec3 color;\nif (sbase > 0.0) {\ncolor = (cbase - minbase) * ssat / sbase;\n} else {\ncolor = vec3(0.0);\n}\nreturn setLum(color, clum);\n}\nvoid main() {\nvec4 src = texture2D(u_layerTexture, v_uv);\n#ifdef NORMAL\ngl_FragColor = src *  u_opacity;\n#else\nvec4 dst = texture2D(u_backbufferTexture, v_uv);\nvec3 Cs = src.a == 0.0 ? src.rgb : vec3(src.rgb / src.a);\nvec3 Cb = dst.a == 0.0 ? dst.rgb : vec3(dst.rgb / dst.a);\nfloat as = u_opacity * src.a;\nfloat ab = dst.a;\n#ifdef DESTINATION_OVER\ngl_FragColor = vec4(as * Cs * (1.0 - ab) + ab * Cb, as + ab - as * ab);\n#endif\n#ifdef SOURCE_IN\nvec4 color = vec4(as * Cs * ab, as * ab);\nvec4 fadeColor = (1.0 - u_opacity) * u_inFadeOpacity * vec4(ab * Cb, ab);\ngl_FragColor = color + fadeColor;\n#endif\n#ifdef DESTINATION_IN\nvec4 color = vec4(ab * Cb * as, ab * as);\nvec4 fadeColor = (1.0 - u_opacity) * u_inFadeOpacity * vec4(ab * Cb, ab);\ngl_FragColor = color + fadeColor;\n#endif\n#ifdef SOURCE_OUT\ngl_FragColor = vec4(as * Cs * (1.0 - ab), as * (1.0 - ab));\n#endif\n#ifdef DESTINATION_OUT\ngl_FragColor = vec4(ab * Cb * (1.0 - as), ab * (1.0 - as));\n#endif\n#ifdef SOURCE_ATOP\ngl_FragColor = vec4(as * Cs * ab + ab * Cb * (1.0 - as), ab);\n#endif\n#ifdef DESTINATION_ATOP\ngl_FragColor = vec4(as * Cs * (1.0 - ab) + ab * Cb * as, as);\n#endif\n#ifdef XOR\ngl_FragColor = vec4(as * Cs * (1.0 - ab) + ab * Cb * (1.0 - as),\nas * (1.0 - ab) + ab * (1.0 - as));\n#endif\n#ifdef MULTIPLY\ngl_FragColor = vec4(as * Cs * ab * Cb + (1.0 - ab) * as * Cs + (1.0 - as) * ab * Cb,\nas + ab * (1.0 - as));\n#endif\n#ifdef SCREEN\ngl_FragColor = vec4((Cs + Cb - Cs * Cb) * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef OVERLAY\nvec3 f = vec3(overlay(Cb.r, Cs.r), overlay(Cb.g, Cs.g), overlay(Cb.b, Cs.b));\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef DARKEN\ngl_FragColor = vec4(min(Cs, Cb) * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef LIGHTER\ngl_FragColor = vec4(as * Cs + ab * Cb, as + ab);\n#endif\n#ifdef LIGHTEN\ngl_FragColor = vec4(max(Cs, Cb) * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef COLOR_DODGE\nvec3 f = clamp(vec3(colorDodge(Cb.r, Cs.r), colorDodge(Cb.g, Cs.g), colorDodge(Cb.b, Cs.b)), vec3(0.0), vec3(1.0));\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef COLOR_BURN\nvec3 f = vec3(colorBurn(Cb.r, Cs.r), colorBurn(Cb.g, Cs.g), colorBurn(Cb.b, Cs.b));\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef HARD_LIGHT\nvec3 f = vec3(hardLight(Cb.r, Cs.r), hardLight(Cb.g, Cs.g), hardLight(Cb.b, Cs.b));\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef SOFT_LIGHT\nvec3 f = vec3(softLight(Cb.r, Cs.r), softLight(Cb.g, Cs.g), softLight(Cb.b, Cs.b));\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef DIFFERENCE\ngl_FragColor = vec4(abs(Cb - Cs) * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef EXCLUSION\nvec3 f = Cs + Cb - 2.0 * Cs * Cb;\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef INVERT\ngl_FragColor = vec4((1.0 - Cb) * as * ab + Cb * ab * (1.0 - as), ab);\n#endif\n#ifdef VIVID_LIGHT\nvec3 f = vec3(clamp(vividLight(Cb.r, Cs.r), 0.0, 1.0),\nclamp(vividLight(Cb.g, Cs.g), 0.0, 1.0),\nclamp(vividLight(Cb.b, Cs.b), 0.0, 1.0));\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef HUE\nvec3 f = setLumSat(Cs,Cb,Cb);\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef SATURATION\nvec3 f = setLumSat(Cb,Cs,Cb);\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef COLOR\nvec3 f = setLum(Cs,Cb);\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef LUMINOSITY\nvec3 f = setLum(Cb,Cs);\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef PLUS\ngl_FragColor = clamp(vec4(src.r + Cb.r, src.g + Cb.g, src.b + Cb.b, as + ab), 0.0, 1.0);\n#endif\n#ifdef MINUS\ngl_FragColor = vec4(clamp(vec3(Cb.r - src.r, Cb.g - src.g, Cb.b - src.b), 0.0, 1.0), ab * as);\n#endif\n#ifdef AVERAGE\nvec3 f = (Cb + Cs) / 2.0;\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#ifdef REFLECT\nvec3 f = clamp(vec3(reflectBlend(Cb.r, Cs.r),\nreflectBlend(Cb.g, Cs.g),\nreflectBlend(Cb.b, Cs.b)), vec3(0.0), vec3(1.0));\ngl_FragColor = vec4(f * as * ab + Cs * as * (1.0 - ab) + Cb * ab *(1.0 - as),\nas + ab * (1.0 - as));\n#endif\n#endif\n}", "blend.vert": "attribute vec2 a_position;\nvarying mediump vec2 v_uv;\nvoid main(void) {\ngl_Position = vec4(a_position , 0.0, 1.0);\nv_uv = (a_position + 1.0) / 2.0;\n}" }, debug: { overlay: { "overlay.frag": "precision mediump float;\nvarying vec4 v_color;\nvoid main(void) {\ngl_FragColor = v_color;\n}", "overlay.vert": "attribute vec3 a_PositionAndFlags;\nuniform mat3 u_dvsMat3;\nuniform vec4 u_colors[4];\nuniform float u_opacities[4];\nvarying vec4 v_color;\nvoid main(void) {\nvec2 position = a_PositionAndFlags.xy;\nfloat flags = a_PositionAndFlags.z;\nint colorIndex = int(mod(flags, 4.0));\nvec4 color;\nfor (int i = 0; i < 4; i++) {\ncolor = u_colors[i];\nif (i == colorIndex) {\nbreak;\n}\n}\nint opacityIndex = int(mod(floor(flags / 4.0), 4.0));\nfloat opacity;\nfor (int i = 0; i < 4; i++) {\nopacity = u_opacities[i];\nif (i == opacityIndex) {\nbreak;\n}\n}\nv_color = color * opacity;\ngl_Position = vec4((u_dvsMat3 * vec3(position, 1.0)).xy, 0.0, 1.0);\n}" } }, dot: { dot: { "dot.frag": "precision mediump float;\nvarying vec4 v_color;\nvarying float v_dotRatio;\nvarying float v_invEdgeRatio;\nuniform highp float u_tileZoomFactor;\nvoid main()\n{\nfloat dist = length(gl_PointCoord - vec2(.5, .5)) * 2.;\nfloat alpha = smoothstep(0., 1., v_invEdgeRatio * (dist - v_dotRatio) + 1.);\ngl_FragColor = v_color * alpha;\n}", "dot.vert": "precision highp float;\nattribute vec2 a_pos;\nuniform sampler2D u_texture;\nuniform highp mat3 u_dvsMat3;\nuniform highp float u_tileZoomFactor;\nuniform highp float u_dotSize;\nuniform highp float u_pixelRatio;\nvarying vec2 v_pos;\nvarying vec4 v_color;\nvarying float v_dotRatio;\nvarying float v_invEdgeRatio;\nconst float EPSILON = 0.000001;\nvoid main()\n{\nmat3 tileToTileTexture = mat3(  1., 0., 0.,\n0., -1., 0.,\n0., 1., 1.  );\nvec3 texCoords = tileToTileTexture * vec3(a_pos.xy / 512., 1.);\nv_color = texture2D(u_texture, texCoords.xy);\nfloat smoothEdgeWidth = max(u_dotSize / 2., 1.) ;\nfloat z = 0.;\nz += 2.0 * step(v_color.a, EPSILON);\ngl_PointSize = (smoothEdgeWidth + u_dotSize);\ngl_Position = vec4((u_dvsMat3 * vec3(a_pos + .5, 1.)).xy, z, 1.);\nv_dotRatio = u_dotSize / gl_PointSize;\nv_invEdgeRatio = -1. / ( smoothEdgeWidth / gl_PointSize );\ngl_PointSize  *= (u_pixelRatio * u_tileZoomFactor);\n}" } }, filtering: { "bicubic.glsl": "vec4 computeWeights(float v) {\nfloat b = 1.0 / 6.0;\nfloat v2 = v * v;\nfloat v3 = v2 * v;\nfloat w0 = b * (-v3 + 3.0 * v2 - 3.0 * v + 1.0);\nfloat w1 = b * (3.0 * v3  - 6.0 * v2 + 4.0);\nfloat w2 = b * (-3.0 * v3 + 3.0 * v2 + 3.0 * v + 1.0);\nfloat w3 = b * v3;\nreturn vec4(w0, w1, w2, w3);\n}\nvec4 bicubicOffsetsAndWeights(float v) {\nvec4 w = computeWeights(v);\nfloat g0 = w.x + w.y;\nfloat g1 = w.z + w.w;\nfloat h0 = 1.0 - (w.y / g0) + v;\nfloat h1 = 1.0 + (w.w / g1) - v;\nreturn vec4(h0, h1, g0, g1);\n}\nvec4 sampleBicubicBSpline(sampler2D sampler, vec2 coords, vec2 texSize) {\nvec2 eX = vec2(1.0 / texSize.x, 0.0);\nvec2 eY = vec2(0.0, 1.0 / texSize.y);\nvec2 texel = coords * texSize - 0.5;\nvec3 hgX = bicubicOffsetsAndWeights(fract(texel).x).xyz;\nvec3 hgY = bicubicOffsetsAndWeights(fract(texel).y).xyz;\nvec2 coords10 = coords + hgX.x * eX;\nvec2 coords00 = coords - hgX.y * eX;\nvec2 coords11 = coords10 + hgY.x * eY;\nvec2 coords01 = coords00 + hgY.x * eY;\ncoords10 = coords10 - hgY.y * eY;\ncoords00 = coords00 - hgY.y * eY;\nvec4 color00 = texture2D(sampler, coords00);\nvec4 color10 = texture2D(sampler, coords10);\nvec4 color01 = texture2D(sampler, coords01);\nvec4 color11 = texture2D(sampler, coords11);\ncolor00 = mix(color00, color01, hgY.z);\ncolor10 = mix(color10, color11, hgY.z);\ncolor00 = mix(color00, color10, hgX.z);\nreturn color00;\n}", "bilinear.glsl": "vec4 sampleBilinear(sampler2D sampler, vec2 coords, vec2 texSize) {\nvec2 texelStart = floor(coords * texSize);\nvec2 coord0 = texelStart / texSize;\nvec2 coord1 = (texelStart +  vec2(1.0, 0.0)) / texSize;\nvec2 coord2 = (texelStart +  vec2(0.0, 1.0)) / texSize;\nvec2 coord3 = (texelStart +  vec2(1.0, 1.0)) / texSize;\nvec4 color0 = texture2D(sampler, coord0);\nvec4 color1 = texture2D(sampler, coord1);\nvec4 color2 = texture2D(sampler, coord2);\nvec4 color3 = texture2D(sampler, coord3);\nvec2 blend = fract(coords * texSize);\nvec4 color01 = mix(color0, color1, blend.x);\nvec4 color23 = mix(color2, color3, blend.x);\nvec4 color = mix(color01, color23, blend.y);\n#ifdef NNEDGE\nfloat alpha = floor(color0.a * color1.a * color2.a * color3.a + 0.5);\ncolor = color * alpha + (1.0 - alpha) * texture2D(sampler, coords);\n#endif\nreturn color;\n}", "epx.glsl": "vec4 sampleEPX(sampler2D sampler, float size, vec2 coords, vec2 texSize) {\nvec2 invSize = 1.0 / texSize;\nvec2 texel = coords * texSize;\nvec2 texel_i = floor(texel);\nvec2 texel_frac = fract(texel);\nvec4 colorP = texture2D(sampler, texel_i * invSize);\nvec4 colorP1 = vec4(colorP);\nvec4 colorP2 = vec4(colorP);\nvec4 colorP3 = vec4(colorP);\nvec4 colorP4 = vec4(colorP);\nvec4 colorA = texture2D(sampler, (texel_i - vec2(0.0, 1.0)) * invSize);\nvec4 colorB = texture2D(sampler, (texel_i + vec2(1.0, 0.0)) * invSize);\nvec4 colorC = texture2D(sampler, (texel_i - vec2(1.0, 0.0)) * invSize);\nvec4 colorD = texture2D(sampler, (texel_i + vec2(0.0, 1.0)) * invSize);\nif (colorC == colorA && colorC != colorD && colorA != colorB) {\ncolorP1 = colorA;\n}\nif (colorA == colorB && colorA != colorC && colorB != colorD) {\ncolorP2 = colorB;\n}\nif (colorD == colorC && colorD != colorB && colorC != colorA) {\ncolorP3 = colorC;\n}\nif (colorB == colorD && colorB != colorA && colorD != colorC) {\ncolorP4 = colorD;\n}\nvec4 colorP12 = mix(colorP1, colorP2, texel_frac.x);\nvec4 colorP34 = mix(colorP1, colorP2, texel_frac.x);\nreturn mix(colorP12, colorP34, texel_frac.y);\n}" }, fx: { integrate: { "integrate.frag": "precision mediump float;\nuniform lowp sampler2D u_sourceTexture;\nuniform lowp sampler2D u_maskTexture;\nuniform mediump float u_zoomLevel;\nuniform highp float u_timeDelta;\nuniform highp float u_animationTime;\nvarying highp vec2 v_texcoord;\n#include <materials/utils.glsl>\nvoid main()\n{\n#ifdef DELTA\nvec4 texel = texture2D(u_sourceTexture, v_texcoord);\nvec4 data0 = texture2D(u_maskTexture, v_texcoord);\nfloat flags = data0.r * 255.0;\nfloat groupMinZoom = data0.g * 255.0;\nfloat isVisible = getFilterBit(flags, 0);\nfloat wouldClip = step(groupMinZoom, u_zoomLevel);\nfloat direction = wouldClip * 1.0 + (1.0 - wouldClip) * -1.0;\nfloat dt = u_timeDelta / max(u_animationTime, 0.0001);\nvec4 nextState = vec4(texel + direction * dt);\ngl_FragColor =  vec4(nextState);\n#elif defined(UPDATE)\nvec4 texel = texture2D(u_sourceTexture, v_texcoord);\ngl_FragColor = texel;\n#endif\n}", "integrate.vert": "precision mediump float;\nattribute vec2 a_pos;\nvarying highp vec2 v_texcoord;\nvoid main()\n{\nv_texcoord = a_pos;\ngl_Position = vec4(a_pos * 2.0 - 1.0, 0.0, 1.0);\n}" } }, heatmap: { heatmapResolve: { "heatmapResolve.frag": "precision highp float;\n#ifdef HEATMAP_PRECISION_HALF_FLOAT\n#define COMPRESSION_FACTOR 4.0\n#else\n#define COMPRESSION_FACTOR 1.0\n#endif\nuniform sampler2D u_texture;\nuniform sampler2D u_gradient;\nuniform vec2 u_densityMinAndInvRange;\nuniform float u_densityNormalization;\nvarying vec2 v_uv;\nvoid main() {\nvec4 data = texture2D(u_texture, v_uv);\nfloat density = data.r * COMPRESSION_FACTOR;\ndensity *= u_densityNormalization;\ndensity = (density - u_densityMinAndInvRange.x) * u_densityMinAndInvRange.y;\nvec4 color = texture2D(u_gradient, vec2(density, 0.5));\ngl_FragColor = vec4(color.rgb * color.a, color.a);\n}", "heatmapResolve.vert": "precision highp float;\nattribute vec2 a_pos;\nvarying vec2 v_uv;\nvoid main() {\nv_uv = a_pos;\ngl_Position = vec4(a_pos * 2.0 - 1.0, 1., 1.);\n}" } }, highlight: { "blur.frag": "varying mediump vec2 v_texcoord;\nuniform mediump vec4 u_direction;\nuniform mediump mat4 u_channelSelector;\nuniform mediump float u_sigma;\nuniform sampler2D u_texture;\nmediump float gauss1(mediump vec2 dir) {\nreturn exp(-dot(dir, dir) / (2.0 * u_sigma * u_sigma));\n}\nmediump vec4 selectChannel(mediump vec4 sample) {\nreturn u_channelSelector * sample;\n}\nvoid accumGauss1(mediump float i, inout mediump float tot, inout mediump float weight) {\nmediump float w = gauss1(i * u_direction.xy);\ntot += selectChannel(texture2D(u_texture, v_texcoord + i * u_direction.zw))[3] * w;\nweight += w;\n}\nvoid main(void) {\nmediump float tot = 0.0;\nmediump float weight = 0.0;\naccumGauss1(-5.0, tot, weight);\naccumGauss1(-4.0, tot, weight);\naccumGauss1(-3.0, tot, weight);\naccumGauss1(-2.0, tot, weight);\naccumGauss1(-1.0, tot, weight);\naccumGauss1(0.0, tot, weight);\naccumGauss1(1.0, tot, weight);\naccumGauss1(2.0, tot, weight);\naccumGauss1(3.0, tot, weight);\naccumGauss1(4.0, tot, weight);\naccumGauss1(5.0, tot, weight);\ngl_FragColor = vec4(0.0, 0.0, 0.0, tot / weight);\n}", "highlight.frag": "varying mediump vec2 v_texcoord;\nuniform sampler2D u_texture;\nuniform mediump float u_sigma;\nuniform sampler2D u_shade;\nuniform mediump vec2 u_minMaxDistance;\nmediump float estimateDistance() {\nmediump float y = texture2D(u_texture, v_texcoord)[3];\nconst mediump float y0 = 0.5;\nmediump float m0 = 1.0 / (sqrt(2.0 * 3.1415) * u_sigma);\nmediump float d = (y - y0) / m0;\nreturn d;\n}\nmediump vec4 shade(mediump float d) {\nmediump float mappedDistance = (d - u_minMaxDistance.x) / (u_minMaxDistance.y - u_minMaxDistance.x);\nmappedDistance = clamp(mappedDistance, 0.0, 1.0);\nreturn texture2D(u_shade, vec2(mappedDistance, 0.5));\n}\nvoid main(void) {\nmediump float d = estimateDistance();\ngl_FragColor = shade(d);\n}", "textured.vert": "attribute mediump vec2 a_position;\nattribute mediump vec2 a_texcoord;\nvarying mediump vec2 v_texcoord;\nvoid main(void) {\ngl_Position = vec4(a_position, 0.0, 1.0);\nv_texcoord = a_texcoord;\n}" }, magnifier: { "magnifier.frag": "uniform lowp vec4 u_background;\nuniform mediump sampler2D u_readbackTexture;\nuniform mediump sampler2D u_maskTexture;\nuniform mediump sampler2D u_overlayTexture;\nuniform bool u_maskEnabled;\nuniform bool u_overlayEnabled;\nvarying mediump vec2 v_texCoord;\nconst lowp float barrelFactor = 1.1;\nlowp vec2 barrel(lowp vec2 uv) {\nlowp vec2 uvn = uv * 2.0 - 1.0;\nif (uvn.x == 0.0 && uvn.y == 0.0) {\nreturn vec2(0.5, 0.5);\n}\nlowp float theta = atan(uvn.y, uvn.x);\nlowp float r = pow(length(uvn), barrelFactor);\nreturn r * vec2(cos(theta), sin(theta)) * 0.5 + 0.5;\n}\nvoid main(void)\n{\nlowp vec4 color = texture2D(u_readbackTexture, barrel(v_texCoord));\ncolor = (color + (1.0 - color.a) * u_background);\nlowp float mask = u_maskEnabled ? texture2D(u_maskTexture, v_texCoord).a : 1.0;\ncolor *= mask;\nlowp vec4 overlayColor = u_overlayEnabled ? texture2D(u_overlayTexture, v_texCoord) : vec4(0);\ngl_FragColor = overlayColor + (1.0 - overlayColor.a) * color;\n}", "magnifier.vert": "precision mediump float;\nattribute mediump vec2 a_pos;\nuniform mediump vec4 u_drawPos;\nvarying mediump vec2 v_texCoord;\nvoid main(void)\n{\nv_texCoord = a_pos;\ngl_Position = vec4(u_drawPos.xy + vec2(a_pos - 0.5) * u_drawPos.zw, 0.0, 1.0);\n}" }, materials: { "attributeData.glsl": "uniform highp sampler2D u_attributeData0;\nuniform highp sampler2D u_attributeData1;\nuniform highp sampler2D u_attributeData2;\nuniform highp sampler2D u_attributeData3;\nuniform highp sampler2D u_attributeData4;\nuniform highp sampler2D u_attributeData5;\nuniform highp int u_attributeTextureSize;\nhighp vec2 getAttributeDataCoords(in highp vec3 id) {\nhighp vec3  texel = unpackDisplayIdTexel(id);\nhighp float size = float(u_attributeTextureSize);\nhighp float u32 = float(int(texel.r) + int(texel.g) * 256 + int(texel.b) * 256 * 256);\nhighp float col = mod(u32, size);\nhighp float row = (u32 - col) / size;\nhighp float u = col / size;\nhighp float v = row / size;\nreturn vec2(u, v);\n}\nhighp vec2 getAttributeDataTextureCoords(in highp vec3 id) {\nreturn (getAttributeDataCoords(id) * 2.0) - 1.0 + (.5 / vec2(u_attributeTextureSize));\n}\nhighp vec4 getAttributeData0(in highp vec3 id) {\nvec2 coords = getAttributeDataCoords(id);\nreturn texture2D(u_attributeData0, coords);\n}\nhighp vec4 getAttributeData1(in highp vec3 id) {\nhighp vec2 coords = getAttributeDataCoords(id);\nreturn texture2D(u_attributeData1, coords);\n}\nhighp vec4 getAttributeData2(in highp vec3 id) {\nhighp vec2 coords = getAttributeDataCoords(id);\nreturn texture2D(u_attributeData2, coords);\n}\nhighp vec4 getAttributeData3(in highp vec3 id) {\nhighp vec2 coords = getAttributeDataCoords(id);\nreturn texture2D(u_attributeData3, coords);\n}\nhighp vec4 getAttributeData4(in highp vec3 id) {\nhighp vec2 coords = getAttributeDataCoords(id);\nreturn texture2D(u_attributeData4, coords);\n}\nhighp vec4 getAttributeData5(in highp vec3 id) {\nhighp vec2 coords = getAttributeDataCoords(id);\nreturn texture2D(u_attributeData5, coords);\n}\nfloat u88VVToFloat(in vec2 v) {\nbool isMagic = v.x == 255.0 && v.y == 255.0;\nif (isMagic) {\nreturn NAN_MAGIC_NUMBER;\n}\nreturn (v.x + v.y * float(0x100)) - 32768.0;\n}", "barycentric.glsl": "float inTriangle(vec3 bary) {\nvec3 absBary = abs(bary);\nreturn step((absBary.x + absBary.y + absBary.z), 1.05);\n}\nvec3 xyToBarycentric(in vec2 pos, in vec2 v0,  in vec2 v1, in vec2 v2) {\nmat3 xyToBarycentricMat3 = mat3(\nv1.x * v2.y - v2.x * v1.y, v2.x * v0.y - v0.x * v2.y, v0.x * v1.y - v1.x * v0.y,\nv1.y - v2.y, v2.y - v0.y, v0.y - v1.y,\nv2.x - v1.x, v0.x - v2.x, v1.x - v0.x\n);\nfloat A2 = v0.x * (v1.y - v2.y) + v1.x * (v2.y - v0.y) + v2.x * (v0.y - v1.y);\nreturn (1. / A2) * xyToBarycentricMat3 * vec3(1., pos);\n}", "constants.glsl": "const float C_DEG_TO_RAD = 3.14159265359 / 180.0;\nconst float C_256_TO_RAD = 3.14159265359 / 128.0;\nconst float C_RAD_TO_DEG = 180.0 / 3.141592654;\nconst float POSITION_PRECISION = 1.0 / 8.0;\nconst float FILL_POSITION_PRECISION = 1.0 / 1.0;\nconst float SOFT_EDGE_RATIO = 1.0;\nconst float THIN_LINE_WIDTH_FACTOR = 1.1;\nconst float THIN_LINE_HALF_WIDTH = 1.0;\nconst float EXTRUDE_SCALE_PLACEMENT_PADDING = 1.0 / 4.0;\nconst float OFFSET_PRECISION = 1.0 / 8.0;\nconst float OUTLINE_SCALE = 1.0 / 5.0;\nconst float SDF_FONT_SIZE = 24.0;\nconst float MAX_SDF_DISTANCE = 8.0;\nconst float PLACEMENT_PADDING = 8.0;\nconst float EPSILON = 0.00001;\nconst float EPSILON_HITTEST = 0.05;\nconst int MAX_FILTER_COUNT = 2;\nconst int ATTR_VV_SIZE = 0;\nconst int ATTR_VV_COLOR = 1;\nconst int ATTR_VV_OPACITY = 2;\nconst int ATTR_VV_ROTATION = 3;\nconst highp float NAN_MAGIC_NUMBER = 1e-30;\nconst int BITSET_GENERIC_LOCK_COLOR = 1;\nconst int BITSET_GENERIC_CONSIDER_ALPHA_ONLY = 4;\nconst int BITSET_MARKER_ALIGNMENT_MAP = 0;\nconst int BITSET_MARKER_OUTLINE_ALLOW_COLOR_OVERRIDE = 2;\nconst int BITSET_MARKER_SCALE_SYMBOLS_PROPORTIONALLY = 3;\nconst int BITSET_TYPE_FILL_OUTLINE = 0;\nconst int BITSET_FILL_RANDOM_PATTERN_OFFSET = 2;\nconst int BITSET_FILL_HAS_UNRESOLVED_REPLACEMENT_COLOR = 3;\nconst int BITSET_LINE_SCALE_DASH = 2;", fill: { "common.glsl": "#include <materials/symbologyTypeUtils.glsl>\n#ifdef PATTERN\nuniform mediump vec2 u_mosaicSize;\nvarying mediump float v_sampleAlphaOnly;\n#endif\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\nuniform lowp vec4 u_isActive[ 2 ];\nuniform highp float u_dotValue;\nuniform highp float u_tileDotsOverArea;\nuniform highp float u_dotTextureDotCount;\nuniform mediump float u_tileZoomFactor;\n#endif\nvarying highp vec3 v_id;\nvarying lowp vec4 v_color;\nvarying lowp float v_opacity;\nvarying mediump vec4 v_aux1;\n#ifdef PATTERN\nvarying mediump vec2 v_tileTextureCoord;\n#endif\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\nvarying lowp float v_isOutline;\n#endif\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\nvarying highp vec2 v_dotTextureCoords;\nvarying highp vec4 v_dotThresholds[ 2 ];\n#endif", "fill.frag": "precision highp float;\n#include <materials/constants.glsl>\n#include <materials/utils.glsl>\n#include <materials/fill/common.glsl>\n#ifdef PATTERN\nuniform lowp sampler2D u_texture;\n#endif\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\nuniform mediump mat4 u_dotColors[ 2 ];\nuniform sampler2D u_dotTextures[ 2 ];\nuniform vec4 u_dotBackgroundColor;\n#endif\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\n#include <materials/shared/line/common.glsl>\n#include <materials/shared/line/line.frag>\nlowp vec4 drawLine() {\nfloat v_lineWidth = v_aux1.x;\nvec2  v_normal    = v_aux1.yz;\nLineData inputs = LineData(\nv_color,\nv_normal,\nv_lineWidth,\nv_opacity,\nv_id\n);\nreturn shadeLine(inputs);\n}\n#endif\nlowp vec4 drawFill() {\nlowp vec4 out_color = vec4(0.);\n#ifdef HITTEST\nout_color = v_color;\n#elif defined(PATTERN)\nmediump vec4 v_tlbr = v_aux1;\nmediump vec2 normalizedTextureCoord = mod(v_tileTextureCoord, 1.0);\nmediump vec2 samplePos = mix(v_tlbr.xy, v_tlbr.zw, normalizedTextureCoord);\nlowp vec4 color = texture2D(u_texture, samplePos);\nif (v_sampleAlphaOnly > 0.5) {\ncolor.rgb = vec3(color.a);\n}\nout_color = v_opacity * v_color * color;\n#elif SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY && !defined(HIGHLIGHT)\nvec4 textureThresholds0 = texture2D(u_dotTextures[0], v_dotTextureCoords);\nvec4 textureThresholds1 = texture2D(u_dotTextures[1], v_dotTextureCoords);\nvec4 difference0 = v_dotThresholds[0] - textureThresholds0;\nvec4 difference1 = v_dotThresholds[1] - textureThresholds1;\n#ifdef DD_DOT_BLENDING\nvec4 isPositive0 = step(0.0, difference0);\nvec4 isPositive1 = step(0.0, difference1);\nfloat weightSum = dot(isPositive0, difference0) + dot(isPositive1, difference1);\nfloat lessThanEqZero = step(weightSum, 0.0);\nfloat greaterThanZero = 1.0 - lessThanEqZero ;\nfloat divisor = (weightSum + lessThanEqZero);\nvec4 weights0 = difference0 * isPositive0 / divisor;\nvec4 weights1 = difference1 * isPositive1 / divisor;\nvec4 dotColor = u_dotColors[0] * weights0 + u_dotColors[1] * weights1;\nvec4 preEffectColor = greaterThanZero * dotColor + lessThanEqZero * u_dotBackgroundColor;\n#else\nfloat diffMax = max(max4(difference0), max4(difference1));\nfloat lessThanZero = step(diffMax, 0.0);\nfloat greaterOrEqZero = 1.0 - lessThanZero;\nvec4 isMax0 = step(diffMax, difference0);\nvec4 isMax1 = step(diffMax, difference1);\nvec4 dotColor = u_dotColors[0] * isMax0 + u_dotColors[1] * isMax1;\nvec4 preEffectColor = greaterOrEqZero * dotColor + lessThanZero * u_dotBackgroundColor;\n#endif\nout_color = preEffectColor;\n#else\nout_color = v_opacity * v_color;\n#endif\n#ifdef HIGHLIGHT\nout_color.a = 1.0;\n#endif\nreturn out_color;\n}\nvoid main() {\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\nif (v_isOutline > 0.5) {\ngl_FragColor = drawLine();\n} else {\ngl_FragColor = drawFill();\n}\n#else\ngl_FragColor = drawFill();\n#endif\n}", "fill.vert": "#include <materials/symbologyTypeUtils.glsl>\n#define PACKED_LINE\nprecision highp float;\nattribute float a_bitset;\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\nattribute float a_inverseArea;\nvec4 a_color = vec4(0.0, 0.0, 0.0, 1.0);\nvec2 a_zoomRange = vec2(0.0, 10000.0);\n#else\nattribute vec4 a_color;\nattribute vec4 a_aux2;\nattribute vec4 a_aux3;\n#ifndef SYMBOLOGY_TYPE_IS_SIMPLE_LIKE\nattribute vec4 a_aux1;\nattribute vec2 a_zoomRange;\n#else\nvec2 a_zoomRange = vec2(0.0, 10000.0);\n#endif\n#endif\nuniform vec2 u_tileOffset;\n#include <util/encoding.glsl>\n#include <materials/vcommon.glsl>\n#include <materials/fill/common.glsl>\n#include <materials/fill/hittest.glsl>\nconst float INV_SCALE_COMPRESSION_FACTOR = 1.0 / 128.0;\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\nvec4 dotThreshold(vec4 featureAttrOverFeatureArea, float dotValue, float tileDotsOverArea) {\nreturn featureAttrOverFeatureArea * (1.0 / dotValue)  * (1.0 / tileDotsOverArea);\n}\n#endif\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\n#include <materials/shared/line/common.glsl>\n#include <materials/shared/line/line.vert>\nvoid drawLine(out lowp vec4 out_color, out highp vec3 out_pos) {\nLineData outputs = buildLine(\nout_pos,\na_id,\na_pos,\na_color,\n(a_aux3.xy - 128.) / 16.,\n(a_aux3.zw - 128.) / 16.,\n0.,\na_aux2.z / 16.,\na_bitset,\nvec4(0.),\nvec2(0.),\na_aux2.w / 16.\n);\nv_id      = outputs.id;\nv_opacity = outputs.opacity;\nv_aux1    = vec4(outputs.lineHalfWidth, outputs.normal, 0.);\nout_color = outputs.color;\n}\n#endif\nvoid drawFill(out lowp vec4 out_color, out highp vec3 out_pos) {\nfloat a_bitSet = a_bitset;\nout_color = getColor(a_color, a_bitSet, BITSET_GENERIC_LOCK_COLOR);\nv_opacity = getOpacity();\nv_id      = norm(a_id);\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\nmat3 tileToTileNormalized = mat3(  2. / 512.,  0.,  0.,\n0., -2. / 512.,  0.,\n-1.,  1.,  1.  );\nout_pos   = tileToTileNormalized * vec3((a_pos * FILL_POSITION_PRECISION), 1.);\n#else\nout_pos   = u_dvsMat3 * vec3(a_pos * FILL_POSITION_PRECISION, 1.);\n#endif\n#ifdef PATTERN\nvec4  a_tlbr   = a_aux1;\nfloat a_width  = a_aux2.x;\nfloat a_height = a_aux2.y;\nvec2  a_offset = a_aux2.zw;\nvec2  a_scale  = a_aux3.xy;\nfloat a_angle  = a_aux3.z;\nvec2 scale = INV_SCALE_COMPRESSION_FACTOR * a_scale;\nfloat width = u_zoomFactor * a_width * scale.x;\nfloat height = u_zoomFactor * a_height * scale.y;\nfloat angle = C_256_TO_RAD * a_angle;\nfloat sinA = sin(angle);\nfloat cosA = cos(angle);\nfloat dx = 0.0;\nfloat dy = 0.0;\nif (getBit(a_bitset, BITSET_FILL_RANDOM_PATTERN_OFFSET) > 0.5) {\nfloat id = rgba2float(vec4(a_id, 0.0));\ndx = rand(vec2(id, 0.0));\ndy = rand(vec2(0.0, id));\n}\nmat3 patternMatrix = mat3(cosA / width, sinA / height, 0,\n-sinA / width, cosA / height, 0,\ndx,            dy,           1);\nvec2 tileOffset = vec2(u_tileOffset.x * cosA - u_tileOffset.y * sinA, u_tileOffset.x * sinA + u_tileOffset.y * cosA);\ntileOffset = mod(tileOffset, vec2(a_aux2.x, a_aux2.y));\nvec2 symbolOffset = (a_offset - tileOffset) / vec2(width, height);\nv_tileTextureCoord = (patternMatrix * vec3(a_pos * FILL_POSITION_PRECISION, 1.0)).xy - symbolOffset;\nv_aux1 = a_tlbr / u_mosaicSize.xyxy;\nv_sampleAlphaOnly = getBit(a_bitset, BITSET_GENERIC_CONSIDER_ALPHA_ONLY);\nif (getBit(a_bitSet, BITSET_FILL_HAS_UNRESOLVED_REPLACEMENT_COLOR) > 0.5) {\n#ifdef VV_COLOR\nv_sampleAlphaOnly *= 1.0 - getBit(a_bitSet, BITSET_GENERIC_LOCK_COLOR);\n#else\nv_sampleAlphaOnly = 0.0;\n#endif\n}\n#elif SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_DOT_DENSITY\nvec4 ddAttributeData0 = getAttributeData2(a_id) * u_isActive[0] * a_inverseArea;\nvec4 ddAttributeData1 = getAttributeData3(a_id) * u_isActive[1] * a_inverseArea;\nfloat size = u_tileZoomFactor * 512.0 * 1.0 / u_pixelRatio;\nv_dotThresholds[0] = dotThreshold(ddAttributeData0, u_dotValue, u_tileDotsOverArea);\nv_dotThresholds[1] = dotThreshold(ddAttributeData1, u_dotValue, u_tileDotsOverArea);\nv_dotTextureCoords = (a_pos * FILL_POSITION_PRECISION + 0.5) / size;\n#endif\n}\n#ifdef HITTEST\nvoid draw(out lowp vec4 out_color, out highp vec3 out_pos) {\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\nif (getBit(a_bitset, BITSET_TYPE_FILL_OUTLINE) > 0.5) {\nout_pos = vec3(0., 0., 2.);\nreturn;\n}\n#endif\nhittestFill(out_color, out_pos);\ngl_PointSize = 1.0;\n}\n#elif defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE)\nvoid draw(out lowp vec4 out_color, out highp vec3 out_pos) {\nv_isOutline = getBit(a_bitset, BITSET_TYPE_FILL_OUTLINE);\nif (v_isOutline > 0.5) {\ndrawLine(out_color, out_pos);\n} else {\ndrawFill(out_color, out_pos);\n}\n}\n#else\n#define draw drawFill\n#endif\nvoid main()\n{\nINIT;\nhighp vec3 pos  = vec3(0.);\nhighp vec4 color  = vec4(0.);\ndraw(color, pos);\nv_color = color;\ngl_Position = vec4(clip(v_color, pos, getFilterFlags(), a_zoomRange), 1.0);\n}", "hittest.glsl": "#ifdef HITTEST\n#include <materials/hittest/common.glsl>\nattribute vec2 a_pos1;\nattribute vec2 a_pos2;\nvoid hittestFill(\nout lowp vec4 out_color,\nout highp vec3 out_pos\n) {\nvec3 pos        = u_viewMat3 * u_tileMat3 * vec3(a_pos  * FILL_POSITION_PRECISION, 1.);\nvec3 pos1       = u_viewMat3 * u_tileMat3 * vec3(a_pos1 * FILL_POSITION_PRECISION, 1.);\nvec3 pos2       = u_viewMat3 * u_tileMat3 * vec3(a_pos2 * FILL_POSITION_PRECISION, 1.);\nfloat hittestDist = u_hittestDist;\nfloat dist = distPointTriangle(u_hittestPos, pos.xy, pos1.xy, pos2.xy);\nout_pos = vec3(getAttributeDataTextureCoords(a_id), 0.0);\nif (dist < 0. || dist >= hittestDist) {\nout_pos.z += 2.0;\n}\nout_color = vec4(1. / 255., 0, 0, dist == 0. ? (1. / 255.) : 0.);\n}\n#endif" }, hittest: { "common.glsl": "#ifdef HITTEST\nuniform float u_hittestDist;\nuniform highp vec2 u_hittestPos;\nfloat projectScalar(vec2 a, vec2 b) {\nreturn dot(a, normalize(b));\n}\nfloat distPointSegment(vec2 p0, vec2 p1, vec2 p2) {\nvec2 L = p2 - p1;\nvec2 A = p0 - p1;\nfloat projAL = projectScalar(A, L);\nfloat t = clamp(projAL / length(L), 0., 1.);\nreturn distance(p0, p1 + t * (p2 - p1));\n}\nvoid hittestMarker(out lowp vec4 out_color, out highp vec3 out_pos, in highp vec3 pos, float size) {\nfloat dist = distance(pos, vec3(u_hittestPos, 1.));\nout_pos = vec3(getAttributeDataTextureCoords(a_id), 0.0);\nif ((dist - size) > u_hittestDist) {\nout_pos.z += 2.0;\n}\nout_color = vec4(1. / 255., 0, 0, (dist - size) < 0. ? (1. / 255.) : 0.);\n}\nfloat intersectPointTriangleBary(vec2 p, vec2 a, vec2 b, vec2 c) {\nreturn inTriangle(xyToBarycentric(p, a, b, c));\n}\nfloat distPointTriangle(vec2 p, vec2 a, vec2 b, vec2 c) {\nvec2 ba = b - a;\nvec2 ca = c - a;\nfloat crossProduct = ba.x * ca.y - ca.x * ba.y;\nbool isParallel = crossProduct < EPSILON_HITTEST && crossProduct > -EPSILON_HITTEST;\nif (isParallel) {\nreturn -1.;\n}\nif (intersectPointTriangleBary(p.xy, a, b, c) == 1.) {\nreturn 0.;\n}\nfloat distAB = distPointSegment(p, a, b);\nfloat distBC = distPointSegment(p, b, c);\nfloat distCA = distPointSegment(p, c, a);\nreturn min(min(distAB, distBC), distCA);\n}\n#endif" }, icon: { "common.glsl": "#include <util/encoding.glsl>\nuniform lowp vec2 u_mosaicSize;\nvarying lowp vec4 v_color;\nvarying highp vec3 v_id;\nvarying highp vec4 v_sizeTex;\nvarying mediump vec3 v_pos;\nvarying lowp float v_opacity;\nuniform lowp sampler2D u_texture;\n#ifdef SDF\nvarying lowp vec4 v_outlineColor;\nvarying mediump float v_outlineWidth;\nvarying mediump float v_distRatio;\nvarying mediump float v_overridingOutlineColor;\nvarying mediump float v_isThin;\n#endif\n#ifdef SDF\nvec4 getColor(vec2 v_size, vec2 v_tex) {\nlowp vec4 fillPixelColor = v_color;\nfloat d = 0.5 - rgba2float(texture2D(u_texture, v_tex));\nfloat size = max(v_size.x, v_size.y);\nfloat dist = d * size * SOFT_EDGE_RATIO * v_distRatio;\nfillPixelColor *= clamp(0.5 - dist, 0.0, 1.0);\nfloat outlineWidth = v_outlineWidth;\n#ifdef HIGHLIGHT\noutlineWidth = max(outlineWidth, 4.0 * v_isThin);\n#endif\nif (outlineWidth > 0.25) {\nlowp vec4 outlinePixelColor = v_overridingOutlineColor * v_color + (1.0 - v_overridingOutlineColor) * v_outlineColor;\nfloat clampedOutlineSize = min(outlineWidth, size);\noutlinePixelColor *= clamp(0.5 - abs(dist) + clampedOutlineSize * 0.5, 0.0, 1.0);\nreturn v_opacity * ((1.0 - outlinePixelColor.a) * fillPixelColor + outlinePixelColor);\n}\nreturn v_opacity * fillPixelColor;\n}\n#else\nvec4 getColor(vec2 _v_size, vec2 v_tex) {\nlowp vec4 texColor = texture2D(u_texture, v_tex);\nreturn v_opacity * texColor * v_color;\n}\n#endif", heatmapAccumulate: { "common.glsl": "varying lowp vec4 v_hittestResult;\nvarying mediump vec2 v_offsetFromCenter;\nvarying highp float v_fieldValue;", "heatmapAccumulate.frag": "precision mediump float;\n#include <materials/icon/heatmapAccumulate/common.glsl>\n#ifdef HEATMAP_PRECISION_HALF_FLOAT\n#define COMPRESSION_FACTOR 0.25\n#else\n#define COMPRESSION_FACTOR 1.0\n#endif\nuniform lowp sampler2D u_texture;\nvoid main() {\n#ifdef HITTEST\ngl_FragColor = v_hittestResult;\n#else\nfloat radius = length(v_offsetFromCenter);\nfloat shapeWeight = step(radius, 1.0);\nfloat oneMinusRadiusSquared = 1.0 - radius * radius;\nfloat kernelWeight = oneMinusRadiusSquared * oneMinusRadiusSquared;\ngl_FragColor = vec4(shapeWeight * kernelWeight * v_fieldValue * COMPRESSION_FACTOR);\n#endif\n}", "heatmapAccumulate.vert": "precision highp float;\nattribute vec2 a_vertexOffset;\nvec4 a_color = vec4(0.0);\nvec2 a_zoomRange = vec2(0.0, 10000.0);\nuniform float u_radius;\nuniform float u_isFieldActive;\n#include <materials/vcommon.glsl>\n#include <materials/hittest/common.glsl>\n#include <materials/icon/heatmapAccumulate/common.glsl>\nvoid main() {\nfloat filterFlags = getFilterFlags();\n#ifdef HITTEST\nhighp vec4 out_hittestResult = vec4(0.);\nhighp vec3 out_pos = vec3(0.);\nvec3 pos = u_viewMat3 * u_tileMat3 * vec3(a_pos * POSITION_PRECISION, 1.0);\nhittestMarker(out_hittestResult, out_pos, pos, u_radius);\nv_hittestResult = out_hittestResult;\ngl_PointSize = 1.;\ngl_Position = vec4(clip(a_color, out_pos, filterFlags, a_zoomRange), 1.0);\n#else\nv_offsetFromCenter = sign(a_vertexOffset);\nv_fieldValue = getAttributeData2(a_id).x * u_isFieldActive + 1.0 - u_isFieldActive;\nvec3 centerPos = u_dvsMat3 * vec3(a_pos * POSITION_PRECISION, 1.0);\nvec3 vertexPos = centerPos + u_displayViewMat3 * vec3(v_offsetFromCenter, 0.0) * u_radius;\ngl_Position = vec4(clip(a_color, vertexPos, filterFlags, a_zoomRange), 1.0);\n#endif\n}" }, "hittest.glsl": "#ifdef HITTEST\n#include <materials/hittest/common.glsl>\nattribute vec2 a_vertexOffset1;\nattribute vec2 a_vertexOffset2;\nattribute vec2 a_texCoords1;\nattribute vec2 a_texCoords2;\nvec2 getTextureCoords(in vec3 bary, in vec2 texCoords0, in vec2 texCoords1, in vec2 texCoords2) {\nreturn texCoords0 * bary.x + texCoords1 * bary.y + texCoords2 * bary.z;\n}\nvoid hittestIcon(\ninout lowp vec4 out_color,\nout highp vec3 out_pos,\nin vec3 pos,\nin vec3 offset,\nin vec2 size,\nin float scaleFactor,\nin float isMapAligned\n) {\nout_pos = vec3(getAttributeDataTextureCoords(a_id), 0.0);\nvec3 posBase = u_viewMat3 * u_tileMat3  * pos;\nvec3 offset1 = scaleFactor * vec3(a_vertexOffset1 / 16.0, 0.);\nvec3 offset2 = scaleFactor * vec3(a_vertexOffset2 / 16.0, 0.);\nvec2 pos0    = (posBase + getMatrixNoDisplay(isMapAligned) * offset).xy;\nvec2 pos1    = (posBase + getMatrixNoDisplay(isMapAligned) * offset1).xy;\nvec2 pos2    = (posBase + getMatrixNoDisplay(isMapAligned) * offset2).xy;\nvec3 bary0 = xyToBarycentric(u_hittestPos + vec2(-u_hittestDist, -u_hittestDist), pos0, pos1, pos2);\nvec3 bary1 = xyToBarycentric(u_hittestPos + vec2(0., -u_hittestDist), pos0, pos1, pos2);\nvec3 bary2 = xyToBarycentric(u_hittestPos + vec2(u_hittestDist, -u_hittestDist), pos0, pos1, pos2);\nvec3 bary3 = xyToBarycentric(u_hittestPos + vec2(-u_hittestDist, 0.), pos0, pos1, pos2);\nvec3 bary4 = xyToBarycentric(u_hittestPos, pos0, pos1, pos2);\nvec3 bary5 = xyToBarycentric(u_hittestPos + vec2(u_hittestDist, 0.), pos0, pos1, pos2);\nvec3 bary6 = xyToBarycentric(u_hittestPos + vec2(-u_hittestDist, u_hittestDist), pos0, pos1, pos2);\nvec3 bary7 = xyToBarycentric(u_hittestPos + vec2(0., u_hittestDist), pos0, pos1, pos2);\nvec3 bary8 = xyToBarycentric(u_hittestPos + vec2(u_hittestDist, u_hittestDist), pos0, pos1, pos2);\nvec2 tex0 = a_texCoords  / u_mosaicSize;\nvec2 tex1 = a_texCoords1 / u_mosaicSize;\nvec2 tex2 = a_texCoords2 / u_mosaicSize;\nfloat alphaSum = 0.;\nalphaSum += inTriangle(bary0) * getColor(size, getTextureCoords(bary0, tex0, tex1, tex2)).a;\nalphaSum += inTriangle(bary1) * getColor(size, getTextureCoords(bary1, tex0, tex1, tex2)).a;\nalphaSum += inTriangle(bary2) * getColor(size, getTextureCoords(bary2, tex0, tex1, tex2)).a;\nalphaSum += inTriangle(bary3) * getColor(size, getTextureCoords(bary3, tex0, tex1, tex2)).a;\nalphaSum += inTriangle(bary4) * getColor(size, getTextureCoords(bary4, tex0, tex1, tex2)).a;\nalphaSum += inTriangle(bary5) * getColor(size, getTextureCoords(bary5, tex0, tex1, tex2)).a;\nalphaSum += inTriangle(bary6) * getColor(size, getTextureCoords(bary6, tex0, tex1, tex2)).a;\nalphaSum += inTriangle(bary7) * getColor(size, getTextureCoords(bary7, tex0, tex1, tex2)).a;\nout_pos.z += step(alphaSum, .05) * 2.0;\nout_color = vec4(1. / 255., 0., 0., alphaSum / 255.);\n}\n#endif", "icon.frag": "precision mediump float;\n#include <materials/constants.glsl>\n#include <materials/utils.glsl>\n#include <materials/icon/common.glsl>\nvoid main()\n{\n#ifdef HITTEST\nvec4 color = v_color;\n#else\nvec4 color = getColor(v_sizeTex.xy, v_sizeTex.zw);\n#endif\n#ifdef HIGHLIGHT\ncolor.a = step(1.0 / 255.0, color.a);\n#endif\ngl_FragColor = color;\n}", "icon.vert": "precision highp float;\nattribute vec4 a_color;\nattribute vec4 a_outlineColor;\nattribute vec4 a_sizeAndOutlineWidth;\nattribute vec2 a_vertexOffset;\nattribute vec2 a_texCoords;\nattribute vec2 a_bitSetAndDistRatio;\nattribute vec2 a_zoomRange;\n#include <materials/vcommon.glsl>\n#include <materials/icon/common.glsl>\n#include <materials/icon/hittest.glsl>\nfloat getMarkerScaleFactor(inout vec2 size, in float referenceSize) {\n#ifdef VV_SIZE\nfloat f = getSize(size.y) / size.y;\nfloat sizeFactor = size.y / referenceSize;\nreturn getSize(referenceSize) / referenceSize;\n#else\nreturn 1.;\n#endif\n}\nvoid main()\n{\nINIT;\nfloat a_bitSet = a_bitSetAndDistRatio.x;\nvec3  pos           = vec3(a_pos * POSITION_PRECISION, 1.0);\nvec2  size          = a_sizeAndOutlineWidth.xy * a_sizeAndOutlineWidth.xy / 128.0;\nvec3  offset        = vec3(a_vertexOffset / 16.0, 0.);\nfloat outlineSize   = a_sizeAndOutlineWidth.z * a_sizeAndOutlineWidth.z / 128.0;\nfloat isMapAligned  = getBit(a_bitSet, BITSET_MARKER_ALIGNMENT_MAP);\nfloat referenceSize = a_sizeAndOutlineWidth.w * a_sizeAndOutlineWidth.w / 128.0;\nfloat scaleSymbolProportionally = getBit(a_bitSet, BITSET_MARKER_SCALE_SYMBOLS_PROPORTIONALLY);\nfloat scaleFactor               = getMarkerScaleFactor(size, referenceSize);\nsize.xy     *= scaleFactor;\noffset.xy   *= scaleFactor;\noutlineSize *= scaleSymbolProportionally * (scaleFactor - 1.0) + 1.0;\nvec2 v_tex   = a_texCoords / u_mosaicSize;\nfloat filterFlags = getFilterFlags();\nv_color    = getColor(a_color, a_bitSet, BITSET_GENERIC_LOCK_COLOR);\nv_opacity  = getOpacity();\nv_id       = norm(a_id);\nv_pos      = u_dvsMat3 * pos + getMatrix(isMapAligned) * getRotation()  * offset;\nv_sizeTex  = vec4(size.xy, v_tex.xy);\n#ifdef SDF\nv_isThin   = getBit(a_bitSet, BITSET_MARKER_OUTLINE_ALLOW_COLOR_OVERRIDE);\n#ifdef VV_COLOR\nv_overridingOutlineColor = v_isThin;\n#else\nv_overridingOutlineColor = 0.0;\n#endif\nv_outlineWidth = min(outlineSize, max(max(size.x, size.y) - 0.99, 0.0));\nv_outlineColor = a_outlineColor;\nv_distRatio = a_bitSetAndDistRatio.y / 126.0;\n#endif\n#ifdef HITTEST\nhighp vec4 out_color = vec4(0.);\nhighp vec3 out_pos   = vec3(0.);\nhittestIcon(out_color, out_pos, pos, offset, size, scaleFactor, isMapAligned);\nv_color = out_color;\ngl_PointSize = 1.;\ngl_Position = vec4(clip(v_color, out_pos, filterFlags, a_zoomRange), 1.0);\n#else\ngl_Position = vec4(clip(v_color, v_pos, filterFlags, a_zoomRange), 1.0);\n#endif\n}" }, label: { "common.glsl": "uniform mediump float u_zoomLevel;\nuniform mediump float u_mapRotation;\nuniform mediump float u_mapAligned;\nuniform mediump vec2 u_mosaicSize;\nvarying mediump float v_antialiasingWidth;\nvarying mediump float v_edgeDistanceOffset;\nvarying mediump vec2 v_tex;\nvarying mediump vec4 v_color;\nvarying lowp vec4 v_animation;", "label.frag": "#include <materials/text/text.frag>", "label.vert": "precision highp float;\n#include <materials/vcommon.glsl>\n#include <materials/text/common.glsl>\nattribute vec4 a_color;\nattribute vec4 a_haloColor;\nattribute vec4 a_texAndSize;\nattribute vec4 a_refSymbolAndPlacementOffset;\nattribute vec4 a_glyphData;\nattribute vec2 a_vertexOffset;\nattribute vec2 a_texCoords;\nuniform float u_isHaloPass;\nuniform float u_isBackgroundPass;\nuniform float u_mapRotation;\nuniform float u_mapAligned;\nfloat getZ(in float minZoom, in float maxZoom, in float angle) {\nfloat glyphAngle = angle * 360.0 / 254.0;\nfloat mapAngle = u_mapRotation * 360.0 / 254.0;\nfloat diffAngle = min(360.0 - abs(mapAngle - glyphAngle), abs(mapAngle - glyphAngle));\nfloat z = 0.0;\nz += u_mapAligned * (2.0 * (1.0 - step(minZoom, u_currentZoom)));\nz += u_mapAligned * 2.0 * step(90.0, diffAngle);\nz += 2.0 * (1.0 - step(u_currentZoom, maxZoom));\nreturn z;\n}\nvoid main()\n{\nINIT;\nfloat groupMinZoom    = getMinZoom();\nfloat glyphMinZoom    = a_glyphData.x;\nfloat glyphMaxZoom    = a_glyphData.y;\nfloat glyphAngle      = a_glyphData.z;\nfloat a_isBackground  = a_glyphData.w;\nfloat a_minZoom          = max(groupMinZoom, glyphMinZoom);\nfloat a_placementPadding = a_refSymbolAndPlacementOffset.x * EXTRUDE_SCALE_PLACEMENT_PADDING;\nvec2  a_placementDir     = unpack_u8_nf32(a_refSymbolAndPlacementOffset.zw);\nfloat a_refSymbolSize    = a_refSymbolAndPlacementOffset.y;\nfloat fontSize           = a_texAndSize.z;\nfloat haloSize           = a_texAndSize.w * OUTLINE_SCALE;\nvec2  vertexOffset = a_vertexOffset * OFFSET_PRECISION;\nvec3  pos          = vec3(a_pos * POSITION_PRECISION, 1.0);\nfloat z            = getZ(a_minZoom, glyphMaxZoom, glyphAngle);\nfloat fontScale    = fontSize / SDF_FONT_SIZE;\nfloat halfSize     = getSize(a_refSymbolSize) / 2.0;\nfloat animation    = pow(getAnimationState(), vec4(2.0)).r;\nfloat isText = 1.0 - a_isBackground;\nfloat isBackground = u_isBackgroundPass * a_isBackground;\nvec4  nonHaloColor = (isBackground + isText) * a_color;\nv_color     = animation * ((1.0 - u_isHaloPass) * nonHaloColor + (u_isHaloPass * a_haloColor));\nv_opacity   = 1.0;\nv_tex       = a_texCoords / u_mosaicSize;\nv_edgeDistanceOffset = u_isHaloPass * haloSize / fontScale / MAX_SDF_DISTANCE;\nv_antialiasingWidth  = 0.105 * SDF_FONT_SIZE / fontSize / u_pixelRatio;\nvec2 placementOffset = a_placementDir * (halfSize + a_placementPadding);\nvec3 glyphOffset     = u_displayMat3 * vec3(vertexOffset + placementOffset, 0.0);\nvec3 v_pos           = vec3((u_dvsMat3 * pos + glyphOffset).xy, z);\nfloat isHidden = u_isBackgroundPass * isText + (1.0 - u_isBackgroundPass) * a_isBackground;\nv_pos.z += 2.0 * isHidden;\ngl_Position = vec4(v_pos, 1.0);\n#ifdef DEBUG\nv_color = vec4(a_color.rgb, z == 0.0 ? 1.0 : 0.645);\n#endif\n}" }, line: { "common.glsl": "varying lowp vec4 v_color;\nvarying highp vec3 v_id;\nvarying mediump vec2 v_normal;\nvarying mediump float v_lineHalfWidth;\nvarying lowp float v_opacity;\n#ifdef PATTERN\nvarying mediump vec4 v_tlbr;\nvarying mediump vec2 v_patternSize;\n#endif\n#if defined(PATTERN) || defined(SDF)\nvarying highp float v_accumulatedDistance;\n#endif\n#ifdef SDF\nvarying mediump float v_lineWidthRatio;\n#endif", "hittest.glsl": "#include <materials/hittest/common.glsl>\n#ifdef HITTEST\nattribute vec2 a_pos1;\nattribute vec2 a_pos2;\nvoid hittestLine(out lowp vec4 out_color, out highp vec3 out_pos, float halfWidth) {\nvec3 pos        = u_viewMat3 * u_tileMat3 * vec3(a_pos  * POSITION_PRECISION, 1.);\nvec3 pos1       = u_viewMat3 * u_tileMat3 * vec3(a_pos1 * POSITION_PRECISION, 1.);\nvec3 pos2       = u_viewMat3 * u_tileMat3 * vec3(a_pos2 * POSITION_PRECISION, 1.);\nvec3 outTextureCoords = vec3(getAttributeDataTextureCoords(a_id), 0.0);\nfloat dist = min(distPointSegment(u_hittestPos, pos.xy, pos1.xy),\ndistPointSegment(u_hittestPos, pos.xy, pos2.xy)) - halfWidth;\nout_pos = vec3(getAttributeDataTextureCoords(a_id), 0.0);\nif (dist >= u_hittestDist) {\nout_pos.z += 2.0;\n}\nout_color = vec4(1. / 255., 0, 0, dist <= 0. ? (1. / 255.) : 0.);\n}\n#endif", "line.frag": "precision lowp float;\n#include <util/encoding.glsl>\n#include <materials/constants.glsl>\n#include <materials/symbologyTypeUtils.glsl>\n#include <materials/line/common.glsl>\n#include <materials/shared/line/common.glsl>\n#include <materials/shared/line/line.frag>\n#ifdef HITTEST\nvoid main() {\ngl_FragColor = v_color;\n}\n#else\nvoid main() {\nLineData inputs = LineData(\nv_color,\nv_normal,\nv_lineHalfWidth,\nv_opacity,\n#ifndef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\n#ifdef PATTERN\nv_tlbr,\nv_patternSize,\n#endif\n#ifdef SDF\nv_lineWidthRatio,\n#endif\n#if defined(PATTERN) || defined(SDF)\nv_accumulatedDistance,\n#endif\n#endif\nv_id\n);\ngl_FragColor = shadeLine(inputs);\n}\n#endif", "line.vert": "precision highp float;\nattribute vec4 a_color;\nattribute vec4 a_offsetAndNormal;\nattribute vec2 a_accumulatedDistanceAndHalfWidth;\nattribute vec4 a_tlbr;\nattribute vec4 a_segmentDirection;\nattribute vec2 a_aux;\nattribute vec2 a_zoomRange;\n#include <materials/vcommon.glsl>\n#include <materials/symbologyTypeUtils.glsl>\n#include <materials/line/common.glsl>\n#include <materials/line/hittest.glsl>\n#include <materials/shared/line/common.glsl>\n#include <materials/shared/line/line.vert>\n#ifdef HITTEST\nvoid draw() {\nfloat aa        = 0.5 * u_antialiasing;\nfloat a_halfWidth = a_accumulatedDistanceAndHalfWidth.y / 16.;\nfloat a_cimHalfWidth = a_aux.x / 16. ;\nvec2  a_offset = a_offsetAndNormal.xy / 16.;\nfloat baseWidth = getBaseLineHalfWidth(a_halfWidth, a_cimHalfWidth);\nfloat halfWidth = getLineHalfWidth(baseWidth, aa);\nhighp vec3 pos  = vec3(0.);\nv_color = vec4(0.);\nhittestLine(v_color, pos, halfWidth);\ngl_PointSize = 1.;\ngl_Position = vec4(clip(v_color, pos, getFilterFlags(), a_zoomRange), 1.0);\n}\n#else\nvoid draw()\n{\nhighp vec3 pos = vec3(0.);\nLineData outputs = buildLine(\npos,\na_id,\na_pos,\na_color,\na_offsetAndNormal.xy / 16.,\na_offsetAndNormal.zw / 16.,\na_accumulatedDistanceAndHalfWidth.x,\na_accumulatedDistanceAndHalfWidth.y / 16.,\na_segmentDirection.w,\na_tlbr,\na_segmentDirection.xy / 16.,\na_aux.x / 16.\n);\nv_id              = outputs.id;\nv_color           = outputs.color;\nv_normal          = outputs.normal;\nv_lineHalfWidth   = outputs.lineHalfWidth;\nv_opacity         = outputs.opacity;\n#ifndef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\n#ifdef PATTERN\nv_tlbr          = outputs.tlbr;\nv_patternSize   = outputs.patternSize;\n#endif\n#ifdef SDF\nv_lineWidthRatio = outputs.lineWidthRatio;\n#endif\n#if defined(PATTERN) || defined(SDF)\nv_accumulatedDistance = outputs.accumulatedDistance;\n#endif\n#endif\ngl_Position = vec4(clip(outputs.color, pos, getFilterFlags(), a_zoomRange), 1.0);\n}\n#endif\nvoid main() {\nINIT;\ndraw();\n}" }, pie: { "pie.frag": "precision mediump float;\n#include <util/atan2.glsl>\n#include <materials/constants.glsl>\n#include <materials/utils.glsl>\n#include <materials/icon/common.glsl>\nvarying float v_size;\nvarying vec2 v_offset;\nvarying vec2 v_filteredSectorToColorId[NUMBER_OF_FIELDS];\nvarying float v_numOfEntries;\nvarying float v_maxSectorAngle;\nuniform lowp vec4 u_colors[NUMBER_OF_FIELDS];\nuniform lowp vec4 u_defaultColor;\nuniform lowp vec4 u_othersColor;\nuniform lowp vec4 u_outlineColor;\nuniform float u_donutRatio;\nuniform float u_sectorThreshold;\nstruct FilteredChartInfo {\nfloat endSectorAngle;\nint colorId;\n};\nlowp vec4 getSectorColor(in int index, in vec2 filteredSectorToColorId[NUMBER_OF_FIELDS]) {\n#if __VERSION__ == 300\nmediump int colorIndex = int(filteredSectorToColorId[index].y);\nreturn u_colors[colorIndex];\n#else\nmediump int colorIndex;\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\nif (i == index) {\ncolorIndex = int(filteredSectorToColorId[i].y);\n}\n}\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\nif (i == colorIndex) {\nreturn u_colors[i];\n}\n}\nreturn u_colors[NUMBER_OF_FIELDS - 1];\n#endif\n}\nconst int OTHER_SECTOR_ID = 255;\n#ifdef HITTEST\nvec4 getColor() {\nreturn v_color;\n}\n#else\nvec4 getColor() {\nfloat angle = 90.0 - C_RAD_TO_DEG * atan2(v_offset.y, v_offset.x);\nif (angle < 0.0) {\nangle += 360.0;\n} else if (angle > 360.0) {\nangle = mod(angle, 360.0);\n}\nint numOfEntries = int(v_numOfEntries);\nfloat maxSectorAngle = v_maxSectorAngle;\nlowp vec4 fillColor = (maxSectorAngle > 0.0 || u_sectorThreshold > 0.0) ? u_othersColor : u_defaultColor;\nlowp vec4 prevColor = vec4(0.0);\nlowp vec4 nextColor = vec4(0.0);\nfloat startSectorAngle = 0.0;\nfloat endSectorAngle = 0.0;\nif (angle < maxSectorAngle) {\nfor (int index = 0; index < NUMBER_OF_FIELDS; ++index) {\nstartSectorAngle = endSectorAngle;\nendSectorAngle = v_filteredSectorToColorId[index].x;\nif (endSectorAngle > angle) {\nfillColor = getSectorColor(index, v_filteredSectorToColorId);\nprevColor = u_sectorThreshold != 0.0 && index == 0 && abs(360.0 - maxSectorAngle) < EPSILON ? u_othersColor :\ngetSectorColor(index > 0 ? index - 1 : numOfEntries - 1, v_filteredSectorToColorId);\nnextColor = u_sectorThreshold != 0.0 && abs(endSectorAngle - maxSectorAngle) < EPSILON ? u_othersColor :\ngetSectorColor(index < numOfEntries - 1 ? index + 1 : 0, v_filteredSectorToColorId);\nbreak;\n}\nif (index == numOfEntries - 1) {\nbreak;\n}\n}\n} else {\nprevColor = getSectorColor(numOfEntries - 1, v_filteredSectorToColorId);\nnextColor = getSectorColor(0, v_filteredSectorToColorId);\nstartSectorAngle = maxSectorAngle;\nendSectorAngle = 360.0;\n}\nlowp vec4 outlineColor = u_outlineColor;\nfloat offset = length(v_offset);\nfloat distanceSize = offset * v_size;\nif (startSectorAngle != 0.0 || endSectorAngle != 360.0) {\nfloat distanceToStartSector = (angle - startSectorAngle);\nfloat distanceToEndSector = (endSectorAngle - angle);\nfloat sectorThreshold = 0.6;\nfloat beginSectorAlpha = smoothstep(0.0, sectorThreshold, distanceToStartSector * offset);\nfloat endSectorAlpha = smoothstep(0.0, sectorThreshold, distanceToEndSector * offset);\nif (endSectorAlpha > 0.0) {\nfillColor = mix(nextColor, fillColor, endSectorAlpha);\n} else if (beginSectorAlpha > 0.0) {\nfillColor = mix(prevColor, fillColor, beginSectorAlpha);\n}\n}\nfloat donutSize = u_donutRatio * (v_size - v_outlineWidth);\nfloat endOfDonut = donutSize - v_outlineWidth;\nfloat aaThreshold = 0.75;\nfloat innerCircleAlpha = endOfDonut - aaThreshold > 0.0 ? smoothstep(endOfDonut - aaThreshold, endOfDonut + aaThreshold, distanceSize) : 1.0;\nfloat outerCircleAlpha = 1.0 - smoothstep(v_size - aaThreshold, v_size + aaThreshold , distanceSize);\nfloat circleAlpha = innerCircleAlpha * outerCircleAlpha;\nfloat startOfOutline = v_size - v_outlineWidth;\nif (startOfOutline > 0.0 && v_outlineWidth > 0.25) {\nfloat outlineFactor = smoothstep(startOfOutline - aaThreshold, startOfOutline + aaThreshold, distanceSize);\nfloat innerLineFactor = donutSize - aaThreshold > 0.0 ? 1.0 - smoothstep(donutSize - aaThreshold, donutSize + aaThreshold , distanceSize) : 0.0;\nfillColor = mix(fillColor, outlineColor, innerLineFactor + outlineFactor);\n}\nreturn v_opacity * circleAlpha * fillColor;\n}\n#endif\nvoid main()\n{\nvec4 color = getColor();\n#ifdef HIGHLIGHT\ncolor.a = step(1.0 / 255.0, color.a);\n#endif\ngl_FragColor = color;\n}", "pie.vert": "precision highp float;\nattribute vec4 a_color;\nattribute vec4 a_outlineColor;\nattribute vec4 a_sizeAndOutlineWidth;\nattribute vec2 a_vertexOffset;\nattribute vec2 a_texCoords;\nattribute vec2 a_bitSetAndDistRatio;\nattribute vec2 a_zoomRange;\nuniform float u_outlineWidth;\nuniform mediump float u_sectorThreshold;\nvarying float v_size;\nvarying vec2 v_offset;\nvarying vec2 v_filteredSectorToColorId[NUMBER_OF_FIELDS];\nvarying float v_numOfEntries;\nvarying float v_maxSectorAngle;\nstruct FilteredChartInfo {\nfloat endSectorAngle;\nint colorId;\n};\nint filter(in float sectorAngle,\nin int currentIndex,\ninout FilteredChartInfo filteredInfo,\ninout vec2 filteredSectorToColorId[NUMBER_OF_FIELDS]) {\nif (sectorAngle > u_sectorThreshold * 360.0) {\nfilteredInfo.endSectorAngle += sectorAngle;\n#if __VERSION__ == 300\nfilteredSectorToColorId[filteredInfo.colorId] = vec2(filteredInfo.endSectorAngle, currentIndex);\n#else\nfor (int i = 0; i < NUMBER_OF_FIELDS; i++) {\nif (i == filteredInfo.colorId) {\nfilteredSectorToColorId[i] = vec2(filteredInfo.endSectorAngle, currentIndex);\n}\n}\n#endif\n++filteredInfo.colorId;\n}\nreturn 0;\n}\nint filterValues(inout vec2 filteredSectorToColorId[NUMBER_OF_FIELDS],\ninout FilteredChartInfo filteredInfo,\nin float sectorAngles[NUMBER_OF_FIELDS]) {\nfor (int index = 0; index < NUMBER_OF_FIELDS; ++index) {\nfloat sectorValue = sectorAngles[index];\nfilter(sectorValue, index, filteredInfo, filteredSectorToColorId);\n}\nreturn filteredInfo.colorId;\n}\n#include <materials/vcommon.glsl>\n#include <materials/icon/common.glsl>\n#include <materials/hittest/common.glsl>\nvec2 getMarkerSize(inout vec2 offset, inout vec2 baseSize, inout float outlineSize, in float referenceSize, in float bitSet) {\nvec2 outSize = baseSize;\n#ifdef VV_SIZE\nfloat r = 0.5 * getSize(referenceSize) / referenceSize;\noutSize.xy *= r;\noffset.xy *= r;\nfloat scaleSymbolProportionally = getBit(bitSet, BITSET_MARKER_SCALE_SYMBOLS_PROPORTIONALLY);\noutlineSize *= scaleSymbolProportionally * (r - 1.0) + 1.0;\n#endif\nreturn outSize;\n}\nvec3 getOffset(in vec2 in_offset, float a_bitSet) {\nfloat isMapAligned = getBit(a_bitSet, BITSET_MARKER_ALIGNMENT_MAP);\nvec3  offset       = vec3(in_offset, 0.0);\nreturn getMatrix(isMapAligned) * offset;\n}\nfloat filterNaNValues(in float value) {\nreturn value != NAN_MAGIC_NUMBER && value > 0.0 ? value : 0.0;\n}\nvoid main()\n{\nINIT;\nvec2  a_size   = a_sizeAndOutlineWidth.xy * a_sizeAndOutlineWidth.xy / 128.0;\nvec2  a_offset = a_vertexOffset / 16.0;\nfloat outlineSize = u_outlineWidth;\nfloat a_bitSet = a_bitSetAndDistRatio.x;\nvec2 size = getMarkerSize(a_offset, a_size, outlineSize, a_sizeAndOutlineWidth.w * a_sizeAndOutlineWidth.w / 128.0, a_bitSet);\nfloat filterFlags = getFilterFlags();\nvec3  pos         = vec3(a_pos * POSITION_PRECISION, 1.0);\nv_opacity      = getOpacity();\nv_id           = norm(a_id);\nv_pos          = u_dvsMat3 * pos + getOffset(a_offset, a_bitSet);\nv_offset       = sign(a_texCoords - 0.5);\nv_size         = max(size.x, size.y);\nv_outlineWidth = outlineSize;\nfloat attributeData[10];\nvec4 attributeData0 = getAttributeData3(a_id);\nattributeData[0] = filterNaNValues(attributeData0.x);\nattributeData[1] = filterNaNValues(attributeData0.y);\nattributeData[2] = filterNaNValues(attributeData0.z);\nattributeData[3] = filterNaNValues(attributeData0.w);\n#if (NUMBER_OF_FIELDS > 4)\nvec4 attributeData1 = getAttributeData4(a_id);\nattributeData[4] = filterNaNValues(attributeData1.x);\nattributeData[5] = filterNaNValues(attributeData1.y);\nattributeData[6] = filterNaNValues(attributeData1.z);\nattributeData[7] = filterNaNValues(attributeData1.w);\n#endif\n#if (NUMBER_OF_FIELDS > 8)\nvec4 attributeData2 = getAttributeData5(a_id);\nattributeData[8] = filterNaNValues(attributeData2.x);\nattributeData[9] = filterNaNValues(attributeData2.y);\n#endif\nfloat sum = 0.0;\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\nsum += attributeData[i];\n}\nfloat sectorAngles[NUMBER_OF_FIELDS];\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\nsectorAngles[i] = 360.0 * attributeData[i] / sum;\n}\nvec2 filteredSectorToColorId[NUMBER_OF_FIELDS];\nFilteredChartInfo filteredInfo = FilteredChartInfo(0.0, 0);\nint numOfEntries = filterValues(filteredSectorToColorId, filteredInfo, sectorAngles);\nv_numOfEntries = float(numOfEntries);\nv_maxSectorAngle = filteredInfo.endSectorAngle;\n#if __VERSION__ == 300\nv_filteredSectorToColorId = filteredSectorToColorId;\n#else\nfor (int i = 0; i < NUMBER_OF_FIELDS; ++i) {\nif (i == numOfEntries) {\nbreak;\n}\nv_filteredSectorToColorId[i] = filteredSectorToColorId[i];\n}\n#endif\n#ifdef HITTEST\nhighp vec3 out_pos = vec3(0.0);\nv_color            = vec4(0.0);\nhittestMarker(v_color, out_pos, u_viewMat3 * u_tileMat3 *  pos, v_size);\ngl_PointSize = 1.0;\ngl_Position = vec4(clip(v_color, out_pos, filterFlags, a_zoomRange), 1.0);\n#else\ngl_Position = vec4(clip(v_color, v_pos, filterFlags, a_zoomRange), 1.0);\n#endif\n}" }, shared: { line: { "common.glsl": "#if !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE) && defined(PATTERN)\nuniform mediump vec2 u_mosaicSize;\nvarying mediump float v_sampleAlphaOnly;\n#endif\nstruct LineData {\nlowp vec4 color;\nmediump vec2 normal;\nmediump float lineHalfWidth;\nlowp float opacity;\n#ifndef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\n#ifdef PATTERN\nmediump vec4 tlbr;\nmediump vec2 patternSize;\n#endif\n#ifdef SDF\nmediump float lineWidthRatio;\n#endif\n#if defined(PATTERN) || defined(SDF)\nhighp float accumulatedDistance;\n#endif\n#endif\nhighp vec3 id;\n};", "line.frag": "uniform lowp float u_blur;\n#if !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE) && !defined(HIGHLIGHT)\n#if defined(PATTERN) || defined(SDF)\nuniform sampler2D u_texture;\nuniform highp float u_pixelRatio;\n#endif\n#endif\n#if defined(SDF) && !defined(HIGHLIGHT) && !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE)\nlowp vec4 getLineColor(LineData line) {\nmediump float adjustedPatternWidth = line.patternSize.x * 2.0 * line.lineWidthRatio;\nmediump float relativeTexX = fract(line.accumulatedDistance / adjustedPatternWidth);\nmediump float relativeTexY = 0.5 + 0.25 * line.normal.y;\nmediump vec2 texCoord = mix(line.tlbr.xy, line.tlbr.zw, vec2(relativeTexX, relativeTexY));\nmediump float d = rgba2float(texture2D(u_texture, texCoord)) - 0.5;\nfloat dist = d * line.lineHalfWidth;\nreturn line.opacity * clamp(0.5 - dist, 0.0, 1.0) * line.color;\n}\n#elif defined(PATTERN) && !defined(HIGHLIGHT) && !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE)\nlowp vec4 getLineColor(LineData line) {\nmediump float lineHalfWidth = line.lineHalfWidth;\nmediump float adjustedPatternHeight = line.patternSize.y * 2.0 * lineHalfWidth / line.patternSize.x;\nmediump float relativeTexY = fract(line.accumulatedDistance / adjustedPatternHeight);\nmediump float relativeTexX = 0.5 + 0.5 * line.normal.y;\nmediump vec2 texCoord = mix(line.tlbr.xy, line.tlbr.zw, vec2(relativeTexX, relativeTexY));\nlowp vec4 color = texture2D(u_texture, texCoord);\n#ifdef VV_COLOR\nif (v_sampleAlphaOnly > 0.5) {\ncolor.rgb = vec3(color.a);\n}\n#endif\nreturn line.opacity * line.color * color;\n}\n#else\nlowp vec4 getLineColor(LineData line) {\nreturn line.opacity * line.color;\n}\n#endif\nvec4 shadeLine(LineData line)\n{\nmediump float thinLineFactor = max(THIN_LINE_WIDTH_FACTOR * step(line.lineHalfWidth, THIN_LINE_HALF_WIDTH), 1.0);\nmediump float fragDist = length(line.normal) * line.lineHalfWidth;\nlowp float alpha = clamp(thinLineFactor * (line.lineHalfWidth - fragDist) / (u_blur + thinLineFactor - 1.0), 0.0, 1.0);\nlowp vec4 out_color = getLineColor(line) * alpha;\n#ifdef HIGHLIGHT\nout_color.a = step(1.0 / 255.0, out_color.a);\n#endif\n#ifdef ID\nif (out_color.a < 1.0 / 255.0) {\ndiscard;\n}\nout_color = vec4(line.id, 0.0);\n#endif\nreturn out_color;\n}", "line.vert": "float getBaseLineHalfWidth(in float lineHalfWidth, in float referenceHalfWidth) {\n#ifdef VV_SIZE\nfloat refLineWidth = 2.0 * referenceHalfWidth;\nreturn 0.5 * (lineHalfWidth / max(referenceHalfWidth, EPSILON)) * getSize(refLineWidth);\n#else\nreturn lineHalfWidth;\n#endif\n}\nfloat getLineHalfWidth(in float baseWidth, in float aa) {\nfloat halfWidth = max(baseWidth + aa, 0.45) + 0.1 * aa;\n#ifdef HIGHLIGHT\nhalfWidth = max(halfWidth, 2.0);\n#endif\nreturn halfWidth;\n}\nvec2 getDist(in vec2 offset, in float halfWidth) {\nfloat thinLineFactor = max(THIN_LINE_WIDTH_FACTOR * step(halfWidth, THIN_LINE_HALF_WIDTH), 1.0);\nreturn thinLineFactor * halfWidth * offset;\n}\nLineData buildLine(\nout vec3 out_pos,\nin vec3 in_id,\nin vec2 in_pos,\nin vec4 in_color,\nin vec2 in_offset,\nin vec2 in_normal,\nin float in_accumulatedDist,\nin float in_lineHalfWidth,\nin float in_bitSet,\nin vec4 in_tlbr,\nin vec2 in_segmentDirection,\nin float in_referenceHalfWidth\n)\n{\nfloat aa        = 0.5 * u_antialiasing;\nfloat baseWidth = getBaseLineHalfWidth(in_lineHalfWidth, in_referenceHalfWidth);\nfloat halfWidth = getLineHalfWidth(baseWidth, aa);\nfloat z         = 2.0 * step(baseWidth, 0.0);\nvec2  dist      = getDist(in_offset, halfWidth);\nvec3  offset    = u_displayViewMat3 * vec3(dist, 0.0);\nvec3  pos       = u_dvsMat3 * vec3(in_pos * POSITION_PRECISION, 1.0) + offset;\n#ifdef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\nvec4  color     = in_color;\nfloat opacity   = 1.0;\n#else\nvec4  color     = getColor(in_color, in_bitSet, BITSET_GENERIC_LOCK_COLOR);\nfloat opacity   = getOpacity();\n#ifdef SDF\nconst float SDF_PATTERN_HALF_WIDTH = 15.5;\nfloat scaleDash = getBit(in_bitSet, BITSET_LINE_SCALE_DASH);\nfloat lineWidthRatio = (scaleDash * max(halfWidth - 0.55 * u_antialiasing, 0.25) + (1.0 - scaleDash)) / SDF_PATTERN_HALF_WIDTH;\n#endif\n#endif\n#if !defined(SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE) && defined(PATTERN)\nv_sampleAlphaOnly = getBit(in_bitSet, BITSET_GENERIC_CONSIDER_ALPHA_ONLY);\n#endif\nout_pos = vec3(pos.xy, z);\nreturn LineData(\ncolor,\nin_normal,\nhalfWidth,\nopacity,\n#ifndef SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\n#ifdef PATTERN\nin_tlbr / u_mosaicSize.xyxy,\nvec2(in_tlbr.z - in_tlbr.x, in_tlbr.w - in_tlbr.y),\n#endif\n#ifdef SDF\nlineWidthRatio,\n#endif\n#if defined(PATTERN) || defined(SDF)\nin_accumulatedDist * u_zoomFactor + dot(in_segmentDirection, dist),\n#endif\n#endif\nnorm(in_id)\n);\n}" } }, "symbologyTypeUtils.glsl": "#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_OUTLINE_FILL || SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_OUTLINE_FILL_SIMPLE\n#define SYMBOLOGY_TYPE_IS_OUTLINE_FILL_LIKE\n#endif\n#if SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_SIMPLE || SYMBOLOGY_TYPE == SYMBOLOGY_TYPE_OUTLINE_FILL_SIMPLE\n#define SYMBOLOGY_TYPE_IS_SIMPLE_LIKE\n#endif", text: { "common.glsl": "uniform highp vec2 u_mosaicSize;\nvarying highp vec3 v_id;\nvarying mediump vec3 v_pos;\nvarying lowp float v_opacity;\nvarying lowp vec4 v_color;\nvarying highp vec2 v_tex;\nvarying mediump float v_antialiasingWidth;\nvarying mediump float v_edgeDistanceOffset;\nvarying lowp float v_transparency;", "hittest.glsl": "#include <materials/hittest/common.glsl>", "text.frag": "precision mediump float;\n#include <materials/text/common.glsl>\nuniform lowp sampler2D u_texture;\n#ifdef HITTEST\nvec4 getColor() {\nreturn v_color;\n}\n#else\nvec4 getColor()\n{\nfloat SDF_CUTOFF = (2.0 / 8.0);\nfloat SDF_BASE_EDGE_DIST = 1.0 - SDF_CUTOFF;\nlowp float dist = texture2D(u_texture, v_tex).a;\nmediump float edge = SDF_BASE_EDGE_DIST - v_edgeDistanceOffset;\n#ifdef HIGHLIGHT\nedge /= 2.0;\n#endif\nlowp float aa = v_antialiasingWidth;\nlowp float alpha = smoothstep(edge - aa, edge + aa, dist);\nreturn alpha * v_color * v_opacity;\n}\n#endif\nvoid main()\n{\ngl_FragColor = getColor();\n}", "text.vert": "precision highp float;\n#include <materials/utils.glsl>\n#include <materials/vcommon.glsl>\n#include <materials/text/common.glsl>\n#include <materials/text/hittest.glsl>\nattribute vec4 a_color;\nattribute vec4 a_haloColor;\nattribute vec4 a_texFontSize;\nattribute vec4 a_aux;\nattribute vec2 a_zoomRange;\nattribute vec2 a_vertexOffset;\nattribute vec2 a_texCoords;\nuniform float u_isHaloPass;\nuniform float u_isBackgroundPass;\nfloat getTextSize(inout vec2 offset, inout float baseSize, in float referenceSize) {\n#ifdef VV_SIZE\nfloat r = getSize(referenceSize) / referenceSize;\nbaseSize *= r;\noffset.xy *= r;\nreturn baseSize;\n#endif\nreturn baseSize;\n}\nvoid main()\n{\nINIT;\nfloat a_isBackground  = a_aux.y;\nfloat a_referenceSize = a_aux.z * a_aux.z / 256.0;\nfloat a_bitSet        = a_aux.w;\nfloat a_fontSize      = a_texFontSize.z;\nvec2  a_offset        = a_vertexOffset * OFFSET_PRECISION;\nvec3  in_pos        = vec3(a_pos * POSITION_PRECISION, 1.0);\nfloat fontSize      = getTextSize(a_offset, a_fontSize, a_referenceSize);\nfloat fontScale     = fontSize / SDF_FONT_SIZE;\nvec3  offset        = getRotation() * vec3(a_offset, 0.0);\nmat3  extrudeMatrix = getBit(a_bitSet, 0) == 1.0 ? u_displayViewMat3 : u_displayMat3;\nfloat isText = 1.0 - a_isBackground;\nfloat isBackground = u_isBackgroundPass * a_isBackground;\nvec4  nonHaloColor  = (isBackground * a_color) + (isText * getColor(a_color, a_bitSet, 1));\nv_color   = u_isHaloPass * a_haloColor + (1.0 - u_isHaloPass) * nonHaloColor;\nv_opacity = getOpacity();\nv_id      = norm(a_id);\nv_tex     = a_texCoords / u_mosaicSize;\nv_pos     = u_dvsMat3 * in_pos + extrudeMatrix * offset;\nfloat isHidden = u_isBackgroundPass * isText + (1.0 - u_isBackgroundPass) * a_isBackground;\nv_pos.z += 2.0 * isHidden;\nv_edgeDistanceOffset = u_isHaloPass * OUTLINE_SCALE * a_texFontSize.w / fontScale / MAX_SDF_DISTANCE;\nv_antialiasingWidth  = 0.105 * SDF_FONT_SIZE / fontSize / u_pixelRatio;\n#ifdef HITTEST\nhighp vec3 out_pos  = vec3(0.);\nv_color = vec4(0.);\nhittestMarker(v_color, out_pos, u_viewMat3 * u_tileMat3 *  vec3(a_pos * POSITION_PRECISION, 1.0)\n+ u_tileMat3 * offset, fontSize / 2.);\ngl_PointSize = 1.;\ngl_Position = vec4(clip(v_color, out_pos, getFilterFlags(), a_zoomRange), 1.0);\n#else\ngl_Position =  vec4(clip(v_color, v_pos, getFilterFlags(), a_zoomRange), 1.0);\n#endif\n}" }, "utils.glsl": "float rshift(in float u32, in int amount) {\nreturn floor(u32 / pow(2.0, float(amount)));\n}\nfloat getBit(in float bitset, in int bitIndex) {\nfloat offset = pow(2.0, float(bitIndex));\nreturn mod(floor(bitset / offset), 2.0);\n}\nfloat getFilterBit(in float bitset, in int bitIndex) {\nreturn getBit(bitset, bitIndex + 1);\n}\nfloat getHighlightBit(in float bitset) {\nreturn getBit(bitset, 0);\n}\nhighp vec3 unpackDisplayIdTexel(in highp vec3 bitset) {\nfloat isAggregate = getBit(bitset.b, 7);\nreturn (1.0 - isAggregate) * bitset + isAggregate * (vec3(bitset.rgb) - vec3(0.0, 0.0, float(0x80)));\n}\nvec4 unpack(in float u32) {\nfloat r = mod(rshift(u32, 0), 255.0);\nfloat g = mod(rshift(u32, 8), 255.0);\nfloat b = mod(rshift(u32, 16), 255.0);\nfloat a = mod(rshift(u32, 24), 255.0);\nreturn vec4(r, g, b, a);\n}\nvec3 norm(in vec3 v) {\nreturn v /= 255.0;\n}\nvec4 norm(in vec4 v) {\nreturn v /= 255.0;\n}\nfloat max4(vec4 target) {\nreturn max(max(max(target.x, target.y), target.z), target.w);\n}\nvec2 unpack_u8_nf32(vec2 bytes) {\nreturn (bytes - 127.0) / 127.0;\n}\nhighp float rand(in vec2 co) {\nhighp float a = 12.9898;\nhighp float b = 78.233;\nhighp float c = 43758.5453;\nhighp float dt = dot(co, vec2(a,b));\nhighp float sn = mod(dt, 3.14);\nreturn fract(sin(sn) * c);\n}", "vcommon.glsl": "#include <materials/constants.glsl>\n#include <materials/utils.glsl>\n#include <materials/attributeData.glsl>\n#include <materials/vv.glsl>\n#include <materials/barycentric.glsl>\nattribute vec2 a_pos;\nattribute highp vec3 a_id;\nuniform highp mat3 u_dvsMat3;\nuniform highp mat3 u_displayMat3;\nuniform highp mat3 u_displayViewMat3;\nuniform highp mat3 u_tileMat3;\nuniform highp mat3 u_viewMat3;\nuniform highp float u_pixelRatio;\nuniform mediump float u_zoomFactor;\nuniform mediump float u_antialiasing;\nuniform mediump float u_currentZoom;\nvec4 VV_ADATA = vec4(0.0);\nvoid loadVisualVariableData(inout vec4 target) {\n#ifdef SUPPORTS_TEXTURE_FLOAT\ntarget.rgba = getAttributeData2(a_id);\n#else\nvec4 data0 = getAttributeData2(a_id);\nvec4 data1 = getAttributeData3(a_id);\ntarget.r = u88VVToFloat(data0.rg * 255.0);\ntarget.g = u88VVToFloat(data0.ba * 255.0);\ntarget.b = u88VVToFloat(data1.rg * 255.0);\ntarget.a = u88VVToFloat(data1.ba * 255.0);\n#endif\n}\n#ifdef VV\n#define INIT loadVisualVariableData(VV_ADATA)\n#else\n#define INIT\n#endif\nvec4 getColor(in vec4 a_color, in float a_bitSet, int index) {\n#ifdef VV_COLOR\nfloat isColorLocked   = getBit(a_bitSet, index);\nreturn getVVColor(VV_ADATA[ATTR_VV_COLOR], a_color, isColorLocked);\n#else\nreturn a_color;\n#endif\n}\nfloat getOpacity() {\n#ifdef VV_OPACITY\nreturn getVVOpacity(VV_ADATA[ATTR_VV_OPACITY]);\n#else\nreturn 1.0;\n#endif\n}\nfloat getSize(in float in_size) {\n#ifdef VV_SIZE\nreturn getVVSize(in_size, VV_ADATA[ATTR_VV_SIZE]);\n#else\nreturn in_size;\n#endif\n}\nmat3 getRotation() {\n#ifdef VV_ROTATION\nreturn getVVRotationMat3(mod(VV_ADATA[ATTR_VV_ROTATION], 360.0));\n#else\nreturn mat3(1.0);\n#endif\n}\nfloat getFilterFlags() {\n#ifdef IGNORES_SAMPLER_PRECISION\nreturn ceil(getAttributeData0(a_id).x * 255.0);\n#else\nreturn getAttributeData0(a_id).x * 255.0;\n#endif\n}\nvec4 getAnimationState() {\nreturn getAttributeData1(a_id);\n}\nfloat getMinZoom() {\nvec4 data0 = getAttributeData0(a_id) * 255.0;\nreturn data0.g;\n}\nmat3 getMatrixNoDisplay(float isMapAligned) {\nreturn isMapAligned * u_viewMat3 * u_tileMat3 + (1.0 - isMapAligned) * u_tileMat3;\n}\nmat3 getMatrix(float isMapAligned) {\nreturn isMapAligned * u_displayViewMat3 + (1.0 - isMapAligned) * u_displayMat3;\n}\nvec3 clip(inout vec4 color, inout vec3 pos, in float filterFlags, in vec2 minMaxZoom) {\npos.z += 2.0 * (1.0 - getFilterBit(filterFlags, 0));\n#ifdef INSIDE\npos.z += 2.0 * (1.0 - getFilterBit(filterFlags, 1));\n#elif defined(OUTSIDE)\npos.z += 2.0 * getFilterBit(filterFlags, 1);\n#elif defined(HIGHLIGHT)\n#if !defined(HIGHLIGHT_ALL)\npos.z += 2.0 * (1.0 - getHighlightBit(filterFlags));\n#endif\n#endif\npos.z += 2.0 * (step(minMaxZoom.y, u_currentZoom) + (1.0 - step(minMaxZoom.x, u_currentZoom)));\nreturn pos;\n}", "vv.glsl": "#if defined(VV_SIZE_MIN_MAX_VALUE) || defined(VV_SIZE_SCALE_STOPS) || defined(VV_SIZE_FIELD_STOPS) || defined(VV_SIZE_UNIT_VALUE)\n#define VV_SIZE\n#endif\n#if defined(VV_COLOR) || defined(VV_SIZE) || defined(VV_OPACITY) || defined(VV_ROTATION)\n#define VV\n#endif\n#ifdef VV_COLOR\nuniform highp float u_vvColorValues[8];\nuniform vec4 u_vvColors[8];\n#endif\n#ifdef VV_SIZE_MIN_MAX_VALUE\nuniform highp vec4 u_vvSizeMinMaxValue;\n#endif\n#ifdef VV_SIZE_SCALE_STOPS\nuniform highp float u_vvSizeScaleStopsValue;\n#endif\n#ifdef VV_SIZE_FIELD_STOPS\nuniform highp float u_vvSizeFieldStopsValues[6];\nuniform float u_vvSizeFieldStopsSizes[6];\n#endif\n#ifdef VV_SIZE_UNIT_VALUE\nuniform highp float u_vvSizeUnitValueWorldToPixelsRatio;\n#endif\n#ifdef VV_OPACITY\nuniform highp float u_vvOpacityValues[8];\nuniform float u_vvOpacities[8];\n#endif\n#ifdef VV_ROTATION\nuniform lowp float u_vvRotationType;\n#endif\nbool isNan(float val) {\nreturn (val == NAN_MAGIC_NUMBER);\n}\n#ifdef VV_SIZE_MIN_MAX_VALUE\nfloat getVVMinMaxSize(float sizeValue, float fallback) {\nif (isNan(sizeValue)) {\nreturn fallback;\n}\nfloat interpolationRatio = (sizeValue  - u_vvSizeMinMaxValue.x) / (u_vvSizeMinMaxValue.y - u_vvSizeMinMaxValue.x);\ninterpolationRatio = clamp(interpolationRatio, 0.0, 1.0);\nreturn u_vvSizeMinMaxValue.z + interpolationRatio * (u_vvSizeMinMaxValue.w - u_vvSizeMinMaxValue.z);\n}\n#endif\n#ifdef VV_SIZE_FIELD_STOPS\nconst int VV_SIZE_N = 6;\nfloat getVVStopsSize(float sizeValue, float fallback) {\nif (isNan(sizeValue)) {\nreturn fallback;\n}\nif (sizeValue <= u_vvSizeFieldStopsValues[0]) {\nreturn u_vvSizeFieldStopsSizes[0];\n}\nfor (int i = 1; i < VV_SIZE_N; ++i) {\nif (u_vvSizeFieldStopsValues[i] >= sizeValue) {\nfloat f = (sizeValue - u_vvSizeFieldStopsValues[i-1]) / (u_vvSizeFieldStopsValues[i] - u_vvSizeFieldStopsValues[i-1]);\nreturn mix(u_vvSizeFieldStopsSizes[i-1], u_vvSizeFieldStopsSizes[i], f);\n}\n}\nreturn u_vvSizeFieldStopsSizes[VV_SIZE_N - 1];\n}\n#endif\n#ifdef VV_SIZE_UNIT_VALUE\nfloat getVVUnitValue(float sizeValue, float fallback) {\nif (isNan(sizeValue)) {\nreturn fallback;\n}\nreturn u_vvSizeUnitValueWorldToPixelsRatio * sizeValue;\n}\n#endif\n#ifdef VV_OPACITY\nconst int VV_OPACITY_N = 8;\nfloat getVVOpacity(float opacityValue) {\nif (isNan(opacityValue)) {\nreturn 1.0;\n}\nif (opacityValue <= u_vvOpacityValues[0]) {\nreturn u_vvOpacities[0];\n}\nfor (int i = 1; i < VV_OPACITY_N; ++i) {\nif (u_vvOpacityValues[i] >= opacityValue) {\nfloat f = (opacityValue - u_vvOpacityValues[i-1]) / (u_vvOpacityValues[i] - u_vvOpacityValues[i-1]);\nreturn mix(u_vvOpacities[i-1], u_vvOpacities[i], f);\n}\n}\nreturn u_vvOpacities[VV_OPACITY_N - 1];\n}\n#endif\n#ifdef VV_ROTATION\nmat4 getVVRotation(float rotationValue) {\nif (isNan(rotationValue)) {\nreturn mat4(1, 0, 0, 0,\n0, 1, 0, 0,\n0, 0, 1, 0,\n0, 0, 0, 1);\n}\nfloat rotation = rotationValue;\nif (u_vvRotationType == 1.0) {\nrotation = 90.0 - rotation;\n}\nfloat angle = C_DEG_TO_RAD * rotation;\nfloat sinA = sin(angle);\nfloat cosA = cos(angle);\nreturn mat4(cosA, sinA, 0, 0,\n-sinA,  cosA, 0, 0,\n0,     0, 1, 0,\n0,     0, 0, 1);\n}\nmat3 getVVRotationMat3(float rotationValue) {\nif (isNan(rotationValue)) {\nreturn mat3(1, 0, 0,\n0, 1, 0,\n0, 0, 1);\n}\nfloat rotation = rotationValue;\nif (u_vvRotationType == 1.0) {\nrotation = 90.0 - rotation;\n}\nfloat angle = C_DEG_TO_RAD * -rotation;\nfloat sinA = sin(angle);\nfloat cosA = cos(angle);\nreturn mat3(cosA, -sinA, 0,\nsinA, cosA, 0,\n0,    0,    1);\n}\n#endif\n#ifdef VV_COLOR\nconst int VV_COLOR_N = 8;\nvec4 getVVColor(float colorValue, vec4 fallback, float isColorLocked) {\nif (isNan(colorValue) || isColorLocked == 1.0) {\nreturn fallback;\n}\nif (colorValue <= u_vvColorValues[0]) {\nreturn u_vvColors[0];\n}\nfor (int i = 1; i < VV_COLOR_N; ++i) {\nif (u_vvColorValues[i] >= colorValue) {\nfloat f = (colorValue - u_vvColorValues[i-1]) / (u_vvColorValues[i] - u_vvColorValues[i-1]);\nreturn mix(u_vvColors[i-1], u_vvColors[i], f);\n}\n}\nreturn u_vvColors[VV_COLOR_N - 1];\n}\n#endif\nfloat getVVSize(in float size, in float vvSize)  {\n#ifdef VV_SIZE_MIN_MAX_VALUE\nreturn getVVMinMaxSize(vvSize, size);\n#elif defined(VV_SIZE_SCALE_STOPS)\nreturn u_vvSizeScaleStopsValue;\n#elif defined(VV_SIZE_FIELD_STOPS)\nfloat outSize = getVVStopsSize(vvSize, size);\nreturn isNan(outSize) ? size : outSize;\n#elif defined(VV_SIZE_UNIT_VALUE)\nreturn getVVUnitValue(vvSize, size);\n#else\nreturn size;\n#endif\n}" }, overlay: { overlay: { "overlay.frag": "precision lowp float;\nuniform lowp sampler2D u_texture;\nuniform lowp float u_opacity;\nvarying mediump vec2 v_uv;\nvoid main() {\nvec4 color = texture2D(u_texture, v_uv);\ngl_FragColor = color *  u_opacity;\n}", "overlay.vert": "precision mediump float;\nattribute vec2 a_pos;\nattribute vec2 a_uv;\nuniform highp mat3 u_dvsMat3;\nuniform mediump vec2 u_perspective;\nvarying mediump vec2 v_uv;\nvoid main(void) {\nv_uv = a_uv;\nfloat w = 1.0 + dot(a_uv, u_perspective);\nvec3 pos = u_dvsMat3 * vec3(a_pos, 1.0);\ngl_Position = vec4(w * pos.xy, 0.0, w);\n}" } }, "post-processing": { blit: { "blit.frag": "precision mediump float;\nuniform sampler2D u_texture;\nvarying vec2 v_uv;\nvoid main() {\ngl_FragColor = texture2D(u_texture, v_uv);\n}" }, bloom: { composite: { "composite.frag": "precision mediump float;\nvarying vec2 v_uv;\nuniform sampler2D u_blurTexture1;\nuniform sampler2D u_blurTexture2;\nuniform sampler2D u_blurTexture3;\nuniform sampler2D u_blurTexture4;\nuniform sampler2D u_blurTexture5;\nuniform float u_bloomStrength;\nuniform float u_bloomRadius;\nuniform float u_bloomFactors[NUMMIPS];\nuniform vec3 u_bloomTintColors[NUMMIPS];\nfloat lerpBloomFactor(const in float factor) {\nfloat mirrorFactor = 1.2 - factor;\nreturn mix(factor, mirrorFactor, u_bloomRadius);\n}\nvoid main() {\nvec4 color = u_bloomStrength * (\nlerpBloomFactor(u_bloomFactors[0]) * vec4(u_bloomTintColors[0], 1.0) * texture2D(u_blurTexture1, v_uv) +\nlerpBloomFactor(u_bloomFactors[1]) * vec4(u_bloomTintColors[1], 1.0) * texture2D(u_blurTexture2, v_uv) +\nlerpBloomFactor(u_bloomFactors[2]) * vec4(u_bloomTintColors[2], 1.0) * texture2D(u_blurTexture3, v_uv) +\nlerpBloomFactor(u_bloomFactors[3]) * vec4(u_bloomTintColors[3], 1.0) * texture2D(u_blurTexture4, v_uv) +\nlerpBloomFactor(u_bloomFactors[4]) * vec4(u_bloomTintColors[4], 1.0) * texture2D(u_blurTexture5, v_uv)\n);\ngl_FragColor = clamp(color, 0.0, 1.0);\n}" }, gaussianBlur: { "gaussianBlur.frag": "precision mediump float;\nuniform sampler2D u_colorTexture;\nuniform vec2 u_texSize;\nuniform vec2 u_direction;\nvarying vec2 v_uv;\n#define KERNEL_RADIUS RADIUS\n#define SIGMA RADIUS\nfloat gaussianPdf(in float x, in float sigma) {\nreturn 0.39894 * exp(-0.5 * x * x / ( sigma * sigma)) / sigma;\n}\nvoid main() {\nvec2 invSize = 1.0 / u_texSize;\nfloat fSigma = float(SIGMA);\nfloat weightSum = gaussianPdf(0.0, fSigma);\nvec4 pixelColorSum = texture2D(u_colorTexture, v_uv) * weightSum;\nfor (int i = 1; i < KERNEL_RADIUS; i ++) {\nfloat x = float(i);\nfloat w = gaussianPdf(x, fSigma);\nvec2 uvOffset = u_direction * invSize * x;\nvec4 sample1 = texture2D(u_colorTexture, v_uv + uvOffset);\nvec4 sample2 = texture2D(u_colorTexture, v_uv - uvOffset);\npixelColorSum += (sample1 + sample2) * w;\nweightSum += 2.0 * w;\n}\ngl_FragColor = pixelColorSum /weightSum;\n}" }, luminosityHighPass: { "luminosityHighPass.frag": "precision mediump float;\nuniform sampler2D u_texture;\nuniform vec3 u_defaultColor;\nuniform float u_defaultOpacity;\nuniform float u_luminosityThreshold;\nuniform float u_smoothWidth;\nvarying vec2 v_uv;\nvoid main() {\nvec4 texel = texture2D(u_texture, v_uv);\nvec3 luma = vec3(0.299, 0.587, 0.114);\nfloat v = dot(texel.xyz, luma);\nvec4 outputColor = vec4(u_defaultColor.rgb, u_defaultOpacity);\nfloat alpha = smoothstep(u_luminosityThreshold, u_luminosityThreshold + u_smoothWidth, v);\ngl_FragColor = mix(outputColor, texel, alpha);\n}" } }, blur: { gaussianBlur: { "gaussianBlur.frag": "precision mediump float;\nuniform sampler2D u_colorTexture;\nuniform vec2 u_texSize;\nuniform vec2 u_direction;\nuniform float u_sigma;\nvarying vec2 v_uv;\n#define KERNEL_RADIUS RADIUS\nfloat gaussianPdf(in float x, in float sigma) {\nreturn 0.39894 * exp(-0.5 * x * x / ( sigma * sigma)) / sigma;\n}\nvoid main() {\nvec2 invSize = 1.0 / u_texSize;\nfloat fSigma = u_sigma;\nfloat weightSum = gaussianPdf(0.0, fSigma);\nvec4 pixelColorSum = texture2D(u_colorTexture, v_uv) * weightSum;\nfor (int i = 1; i < KERNEL_RADIUS; i ++) {\nfloat x = float(i);\nfloat w = gaussianPdf(x, fSigma);\nvec2 uvOffset = u_direction * invSize * x;\nvec4 sample1 = texture2D(u_colorTexture, v_uv + uvOffset);\nvec4 sample2 = texture2D(u_colorTexture, v_uv - uvOffset);\npixelColorSum += (sample1 + sample2) * w;\nweightSum += 2.0 * w;\n}\ngl_FragColor = pixelColorSum /weightSum;\n}" }, "radial-blur": { "radial-blur.frag": "precision mediump float;\nuniform sampler2D u_colorTexture;\nvarying vec2 v_uv;\nconst float sampleDist = 1.0;\nconst float sampleStrength = 2.2;\nvoid main(void) {\nfloat samples[10];\nsamples[0] = -0.08;\nsamples[1] = -0.05;\nsamples[2] = -0.03;\nsamples[3] = -0.02;\nsamples[4] = -0.01;\nsamples[5] =  0.01;\nsamples[6] =  0.02;\nsamples[7] =  0.03;\nsamples[8] =  0.05;\nsamples[9] =  0.08;\nvec2 dir = 0.5 - v_uv;\nfloat dist = sqrt(dir.x * dir.x + dir.y * dir.y);\ndir = dir / dist;\nvec4 color = texture2D(u_colorTexture,v_uv);\nvec4 sum = color;\nfor (int i = 0; i < 10; i++) {\nsum += texture2D(u_colorTexture, v_uv + dir * samples[i] * sampleDist);\n}\nsum *= 1.0 / 11.0;\nfloat t = dist * sampleStrength;\nt = clamp(t, 0.0, 1.0);\ngl_FragColor = mix(color, sum, t);\n}" } }, dra: { "dra.frag": "precision mediump float;\nuniform sampler2D u_minColor;\nuniform sampler2D u_maxColor;\nuniform sampler2D u_texture;\nvarying vec2 v_uv;\nvoid main() {\nvec4 minColor = texture2D(u_minColor, vec2(0.5));\nvec4 maxColor = texture2D(u_maxColor, vec2(0.5));\nvec4 color = texture2D(u_texture, v_uv);\nvec3 minColorUnpremultiply = minColor.rgb / minColor.a;\nvec3 maxColorUnpremultiply = maxColor.rgb / maxColor.a;\nvec3 colorUnpremultiply = color.rgb / color.a;\nvec3 range = maxColorUnpremultiply - minColorUnpremultiply;\ngl_FragColor = vec4(color.a * (colorUnpremultiply - minColorUnpremultiply) / range, color.a);\n}", "min-max": { "min-max.frag": "#extension GL_EXT_draw_buffers : require\nprecision mediump float;\n#define CELL_SIZE 2\nuniform sampler2D u_minTexture;\nuniform sampler2D u_maxTexture;\nuniform vec2 u_srcResolution;\nuniform vec2 u_dstResolution;\nvarying vec2 v_uv;\nvoid main() {\nvec2 srcPixel = floor(gl_FragCoord.xy) * float(CELL_SIZE);\nvec2 onePixel = vec2(1.0) / u_srcResolution;\nvec2 uv = (srcPixel + 0.5) / u_srcResolution;\nvec4 minColor = vec4(1.0);\nvec4 maxColor = vec4(0.0);\nfor (int y = 0; y < CELL_SIZE; ++y) {\nfor (int x = 0; x < CELL_SIZE; ++x) {\nvec2 offset = uv + vec2(x, y) * onePixel;\nminColor = min(minColor, texture2D(u_minTexture, offset));\nmaxColor = max(maxColor, texture2D(u_maxTexture, offset));\n}\n}\ngl_FragData[0] = minColor;\ngl_FragData[1] = maxColor;\n}" } }, "drop-shadow": { composite: { "composite.frag": "precision mediump float;\nuniform sampler2D u_layerFBOTexture;\nuniform sampler2D u_blurTexture;\nuniform vec4 u_shadowColor;\nuniform vec2 u_shadowOffset;\nuniform highp mat3 u_displayViewMat3;\nvarying vec2 v_uv;\nvoid main() {\nvec3 offset = u_displayViewMat3 * vec3(u_shadowOffset, 0.0);\nvec4 layerColor = texture2D(u_layerFBOTexture, v_uv);\nvec4 blurColor = texture2D(u_blurTexture, v_uv - offset.xy / 2.0);\ngl_FragColor = ((1.0 - layerColor.a) * blurColor.a * u_shadowColor + layerColor);\n}" } }, "edge-detect": { "frei-chen": { "frei-chen.frag": "precision mediump float;\nuniform sampler2D u_colorTexture;\nuniform vec2 u_texSize;\nvarying vec2 v_uv;\nvec2 texel = vec2(1.0 / u_texSize.x, 1.0 / u_texSize.y);\nmat3 G[9];\nconst mat3 g0 = mat3( 0.3535533845424652, 0, -0.3535533845424652, 0.5, 0, -0.5, 0.3535533845424652, 0, -0.3535533845424652 );\nconst mat3 g1 = mat3( 0.3535533845424652, 0.5, 0.3535533845424652, 0, 0, 0, -0.3535533845424652, -0.5, -0.3535533845424652 );\nconst mat3 g2 = mat3( 0, 0.3535533845424652, -0.5, -0.3535533845424652, 0, 0.3535533845424652, 0.5, -0.3535533845424652, 0 );\nconst mat3 g3 = mat3( 0.5, -0.3535533845424652, 0, -0.3535533845424652, 0, 0.3535533845424652, 0, 0.3535533845424652, -0.5 );\nconst mat3 g4 = mat3( 0, -0.5, 0, 0.5, 0, 0.5, 0, -0.5, 0 );\nconst mat3 g5 = mat3( -0.5, 0, 0.5, 0, 0, 0, 0.5, 0, -0.5 );\nconst mat3 g6 = mat3( 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.6666666865348816, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204 );\nconst mat3 g7 = mat3( -0.3333333432674408, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, 0.6666666865348816, 0.1666666716337204, -0.3333333432674408, 0.1666666716337204, -0.3333333432674408 );\nconst mat3 g8 = mat3( 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408, 0.3333333432674408 );\nvoid main() {\nG[0] = g0,\nG[1] = g1,\nG[2] = g2,\nG[3] = g3,\nG[4] = g4,\nG[5] = g5,\nG[6] = g6,\nG[7] = g7,\nG[8] = g8;\nmat3 I;\nfloat cnv[9];\nvec3 sample;\nfor (float i = 0.0; i < 3.0; i++) {\nfor (float j = 0.0; j < 3.0; j++) {\nsample = texture2D(u_colorTexture, v_uv + texel * vec2(i - 1.0,j - 1.0)).rgb;\nI[int(i)][int(j)] = length(sample);\n}\n}\nfor (int i = 0; i < 9; i++) {\nfloat dp3 = dot(G[i][0], I[0]) + dot(G[i][1], I[1]) + dot(G[i][2], I[2]);\ncnv[i] = dp3 * dp3;\n}\nfloat M = (cnv[0] + cnv[1]) + (cnv[2] + cnv[3]);\nfloat S = (cnv[4] + cnv[5]) + (cnv[6] + cnv[7]) + (cnv[8] + M);\ngl_FragColor = vec4(vec3(sqrt(M / S)), texture2D(u_colorTexture, v_uv).a);\n}" }, sobel: { "sobel.frag": "precision mediump float;\nuniform sampler2D u_colorTexture;\nvarying vec2 v_uv;\nuniform vec2 u_texSize;\nvec2 texel = vec2(1.0 / u_texSize.x, 1.0 / u_texSize.y);\nmat3 G[2];\nconst mat3 g0 = mat3( 1.0, 2.0, 1.0, 0.0, 0.0, 0.0, -1.0, -2.0, -1.0 );\nconst mat3 g1 = mat3( 1.0, 0.0, -1.0, 2.0, 0.0, -2.0, 1.0, 0.0, -1.0 );\nvoid main() {\nmat3 I;\nfloat cnv[2];\nvec3 sample;\nG[0] = g0;\nG[1] = g1;\nfor (float i = 0.0; i < 3.0; i++) {\nfor (float j = 0.0; j < 3.0; j++) {\nsample = texture2D( u_colorTexture, v_uv + texel * vec2(i-1.0,j-1.0) ).rgb;\nI[int(i)][int(j)] = length(sample);\n}\n}\nfor (int i = 0; i < 2; i++) {\nfloat dp3 = dot(G[i][0], I[0]) + dot(G[i][1], I[1]) + dot(G[i][2], I[2]);\ncnv[i] = dp3 * dp3;\n}\ngl_FragColor = vec4(vec3(0.5 * sqrt(cnv[0] * cnv[0] + cnv[1] * cnv[1])), texture2D(u_colorTexture, v_uv).a);\n}" } }, "edge-enhance": { "edge-enhance.frag": "precision mediump float;\nuniform sampler2D u_colorTexture;\nvarying vec2 v_uv;\nuniform vec2 u_texSize;\nvec2 texel = vec2(1.0 / u_texSize.x, 1.0 / u_texSize.y);\nmat3 G[2];\nconst mat3 g0 = mat3( 1.0, 0.0, -1.0, 1.0, 0.0, -1.0, 1.0, 0.0, -1.0 );\nconst mat3 g1 = mat3( 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, -1.0, -1.0, -1.0 );\nvoid main() {\nmat3 I;\nfloat cnv[2];\nvec3 sample;\nG[0] = g0;\nG[1] = g1;\nfor (float i = 0.0; i < 3.0; i++) {\nfor (float j = 0.0; j < 3.0; j++) {\nsample = texture2D( u_colorTexture, v_uv + texel * vec2(i-1.0,j-1.0) ).rgb;\nI[int(i)][int(j)] = length(sample);\n}\n}\nfor (int i = 0; i < 2; i++) {\nfloat dp3 = dot(G[i][0], I[0]) + dot(G[i][1], I[1]) + dot(G[i][2], I[2]);\ncnv[i] = dp3 * dp3;\n}\nvec4 color = texture2D(u_colorTexture, v_uv);\ngl_FragColor = vec4(0.5 * sqrt(cnv[0] * cnv[0] + cnv[1] * cnv[1]) * color);\n}" }, filterEffect: { "filterEffect.frag": "precision mediump float;\nuniform sampler2D u_colorTexture;\nuniform mat4 u_coefficients;\nvarying vec2 v_uv;\nvoid main() {\nvec4 color = texture2D(u_colorTexture, v_uv);\nvec4 rgbw = u_coefficients * vec4(color.a > 0.0 ? color.rgb / color.a : vec3(0.0), 1.0);\nfloat a = color.a;\ngl_FragColor = vec4(a * rgbw.rgb, a);\n}" }, pp: { "pp.vert": "precision mediump float;\nattribute vec2 a_position;\nvarying vec2 v_uv;\nvoid main() {\ngl_Position = vec4(a_position, 0.0, 1.0);\nv_uv = (a_position + 1.0) / 2.0;\n}" } }, raster: { bitmap: { "bitmap.frag": "precision mediump float;\nvarying highp vec2 v_texcoord;\nuniform sampler2D u_texture;\nuniform highp vec2 u_coordScale;\nuniform lowp float u_opacity;\n#include <filtering/bicubic.glsl>\nvoid main() {\n#ifdef BICUBIC\nvec4 color = sampleBicubicBSpline(u_texture, v_texcoord, u_coordScale);\n#else\nvec4 color = texture2D(u_texture, v_texcoord);\n#endif\ngl_FragColor = vec4(color.rgb * u_opacity, color.a * u_opacity);\n}", "bitmap.vert": "precision mediump float;\nattribute vec2 a_pos;\nuniform highp mat3 u_dvsMat3;\nuniform highp vec2 u_coordScale;\nvarying highp vec2 v_texcoord;\nvoid main()\n{\nv_texcoord = a_pos;\ngl_Position = vec4(u_dvsMat3 * vec3(a_pos * u_coordScale, 1.0), 1.0);\n}" }, common: { "common.glsl": "uniform sampler2D u_image;\nuniform int u_bandCount;\nuniform bool u_flipY;\nuniform float u_opacity;\nuniform int u_resampling;\nuniform vec2 u_srcImageSize;\n#ifdef APPLY_PROJECTION\n#include <raster/common/projection.glsl>\n#endif\n#ifdef BICUBIC\n#include <filtering/bicubic.glsl>\n#endif\n#ifdef BILINEAR\n#include <filtering/bilinear.glsl>\n#endif\nvec2 getPixelLocation(vec2 coords) {\nvec2 targetLocation = u_flipY ? vec2(coords.s, 1.0 - coords.t) : coords;\n#ifdef APPLY_PROJECTION\ntargetLocation = projectPixelLocation(targetLocation);\n#endif\nreturn targetLocation;\n}\nbool isOutside(vec2 coords){\nif (coords.t>1.00001 ||coords.t<-0.00001 || coords.s>1.00001 ||coords.s<-0.00001) {\nreturn true;\n} else {\nreturn false;\n}\n}\nvec4 getPixel(vec2 pixelLocation) {\n#ifdef BICUBIC\nvec4 color = sampleBicubicBSpline(u_image, pixelLocation, u_srcImageSize);\n#elif defined(BILINEAR)\nvec4 color = sampleBilinear(u_image, pixelLocation, u_srcImageSize);\n#else\nvec4 color = texture2D(u_image, pixelLocation);\n#endif\nreturn color;\n}", "common.vert": "precision mediump float;\nattribute vec2 a_pos;\nuniform highp mat3 u_dvsMat3;\nuniform highp vec2 u_coordScale;\nuniform highp float u_scale;\nuniform highp vec2 u_offset;\nvarying highp vec2 v_texcoord;\nvoid main()\n{\nv_texcoord = a_pos * u_scale + u_offset;\ngl_Position = vec4(u_dvsMat3 * vec3(a_pos * u_coordScale, 1.0), 1.0);\n}", "contrastBrightness.glsl": "uniform float u_contrastOffset;\nuniform float u_brightnessOffset;\nvec4 adjustContrastBrightness(vec4 currentPixel, bool isFloat) {\nvec4 pixelValue = isFloat ? currentPixel * 255.0 : currentPixel;\nfloat maxI = 255.0;\nfloat mid = 128.0;\nfloat c = u_contrastOffset;\nfloat b = u_brightnessOffset;\nvec4 v;\nif (c > 0.0 && c < 100.0) {\nv = (200.0 * pixelValue - 100.0 * maxI + 2.0 * maxI * b) / (2.0 * (100.0 - c)) + mid;\n} else if (c <= 0.0 && c > -100.0) {\nv = (200.0 * pixelValue - 100.0 * maxI + 2.0 * maxI * b) * (100.0 + c) / 20000.0 + mid;\n} else if (c == 100.0) {\nv = (200.0 * pixelValue - 100.0 * maxI + (maxI + 1.0) * (100.0 - c) + 2.0 * maxI * b);\nv = (sign(v) + 1.0) / 2.0;\n} else if (c == -100.0) {\nv = vec4(mid, mid, mid, currentPixel.a);\n}\nreturn vec4(v.r / 255.0, v.g / 255.0, v.b / 255.0, currentPixel.a);\n}", "inverse.glsl": "float invertValue(float value) {\nfloat s = sign(value);\nreturn (s * s) / (value + abs(s) - 1.0);\n}", "mirror.glsl": "vec2 mirror(vec2 pos) {\nvec2 pos1 = abs(pos);\nreturn step(pos1, vec2(1.0, 1.0)) * pos1 + step(1.0, pos1) * (2.0 - pos1);\n}", "projection.glsl": "uniform sampler2D u_transformGrid;\nuniform vec2 u_transformSpacing;\nuniform vec2 u_transformGridSize;\nuniform vec2 u_targetImageSize;\nvec2 projectPixelLocation(vec2 coords) {\n#ifdef LOOKUP_PROJECTION\nvec4 pv = texture2D(u_transformGrid, coords);\nreturn vec2(pv.r, pv.g);\n#endif\nvec2 index_image = floor(coords * u_targetImageSize);\nvec2 oneTransformPixel = vec2(0.25 / u_transformGridSize.s, 1.0 / u_transformGridSize.t);\nvec2 index_transform = floor(index_image / u_transformSpacing) / u_transformGridSize;\nvec2 pos = fract((index_image + vec2(0.5, 0.5)) / u_transformSpacing);\nvec2 srcLocation;\nvec2 transform_location = index_transform + oneTransformPixel * 0.5;\nif (pos.s <= pos.t) {\nvec4 ll_abc = texture2D(u_transformGrid, vec2(transform_location.s, transform_location.t));\nvec4 ll_def = texture2D(u_transformGrid, vec2(transform_location.s + oneTransformPixel.s, transform_location.t));\nsrcLocation.s = dot(ll_abc.rgb, vec3(pos, 1.0));\nsrcLocation.t = dot(ll_def.rgb, vec3(pos, 1.0));\n} else {\nvec4 ur_abc = texture2D(u_transformGrid, vec2(transform_location.s + 2.0 * oneTransformPixel.s, transform_location.t));\nvec4 ur_def = texture2D(u_transformGrid, vec2(transform_location.s + 3.0 * oneTransformPixel.s, transform_location.t));\nsrcLocation.s = dot(ur_abc.rgb, vec3(pos, 1.0));\nsrcLocation.t = dot(ur_def.rgb, vec3(pos, 1.0));\n}\nreturn srcLocation;\n}" }, flow: { "getFadeOpacity.glsl": "uniform float u_decayRate;\nuniform float u_fadeToZero;\nfloat getFadeOpacity(float x) {\nfloat cutOff = mix(0.0, exp(-u_decayRate), u_fadeToZero);\nreturn (exp(-u_decayRate * x) - cutOff) / (1.0 - cutOff);\n}", "getFragmentColor.glsl": "vec4 getFragmentColor(vec4 color, float dist, float size, float featheringSize) {\nfloat featheringStart = clamp(0.5 - featheringSize / size, 0.0, 0.5);\nif (dist > featheringStart) {\ncolor *= 1.0 - (dist - featheringStart) / (0.5 - featheringStart);\n}\nreturn color;\n}", imagery: { "imagery.frag": "precision highp float;\nvarying vec2 v_texcoord;\nuniform sampler2D u_texture;\nuniform float u_Min;\nuniform float u_Max;\nuniform float u_featheringSize;\n#include <raster/flow/vv.glsl>\nfloat getIntensity(float v) {\nreturn u_Min + v * (u_Max - u_Min);\n}\nvoid main(void) {\nvec4 sampled = texture2D(u_texture, v_texcoord);\nfloat intensity = getIntensity(sampled.r);\ngl_FragColor = getColor(intensity);\ngl_FragColor.a *= getOpacity(sampled.r);\ngl_FragColor.a *= sampled.a;\ngl_FragColor.rgb *= gl_FragColor.a;\n}", "imagery.vert": "attribute vec2 a_position;\nattribute vec2 a_texcoord;\nuniform mat3 u_dvsMat3;\nvarying vec2 v_texcoord;\nvoid main(void) {\nvec2 xy = (u_dvsMat3 * vec3(a_position, 1.0)).xy;\ngl_Position = vec4(xy, 0.0, 1.0);\nv_texcoord = a_texcoord;\n}" }, particles: { "particles.frag": "precision highp float;\nvarying vec4 v_color;\nvarying vec2 v_texcoord;\nvarying float v_size;\nuniform float u_featheringSize;\n#include <raster/flow/getFragmentColor.glsl>\nvoid main(void) {\ngl_FragColor = getFragmentColor(v_color, length(v_texcoord - 0.5), v_size, u_featheringSize);\n}", "particles.vert": "attribute vec4 a_xyts0;\nattribute vec4 a_xyts1;\nattribute vec4 a_typeIdDurationSeed;\nattribute vec4 a_extrudeInfo;\nuniform mat3 u_dvsMat3;\nuniform mat3 u_displayViewMat3;\nuniform float u_time;\nuniform float u_trailLength;\nuniform float u_flowSpeed;\nvarying vec4 v_color;\nvarying vec2 v_texcoord;\nvarying float v_size;\nuniform float u_featheringSize;\nuniform float u_introFade;\n#include <raster/flow/vv.glsl>\n#include <raster/flow/getFadeOpacity.glsl>\nvoid main(void) {\nvec2 position0 = a_xyts0.xy;\nfloat t0 = a_xyts0.z;\nfloat speed0 = a_xyts0.w;\nvec2 position1 = a_xyts1.xy;\nfloat t1 = a_xyts1.z;\nfloat speed1 = a_xyts1.w;\nfloat type = a_typeIdDurationSeed.x;\nfloat id = a_typeIdDurationSeed.y;\nfloat duration = a_typeIdDurationSeed.z;\nfloat seed = a_typeIdDurationSeed.w;\nvec2 e0 = a_extrudeInfo.xy;\nvec2 e1 = a_extrudeInfo.zw;\nfloat animationPeriod = duration + u_trailLength;\nfloat scaledTime = u_time * u_flowSpeed;\nfloat randomizedTime = scaledTime + seed * animationPeriod;\nfloat t = mod(randomizedTime, animationPeriod);\nfloat fUnclamped = (t - t0) / (t1 - t0);\nfloat f = clamp(fUnclamped, 0.0, 1.0);\nfloat clampedTime = mix(t0, t1, f);\nfloat speed = mix(speed0, speed1, f);\nvec2 extrude;\nvec2 position;\nfloat fadeOpacity;\nfloat introOpacity;\nif (type == 2.0) {\nif (fUnclamped < 0.0 || (fUnclamped > 1.0 && t1 != duration)) {\ngl_Position = vec4(0.0, 0.0, -2.0, 1.0);\nreturn;\n}\nvec2 ortho = mix(e0, e1, f);\nvec2 parallel;\nparallel = normalize(position1 - position0) * 0.5;\nif (id == 1.0) {\nextrude = ortho;\nv_texcoord = vec2(0.5, 0.0);\n} else if (id == 2.0) {\nextrude = -ortho;\nv_texcoord = vec2(0.5, 1.0);\n} else if (id == 3.0) {\nextrude = ortho + parallel;\nv_texcoord = vec2(1.0, 0.0);\n} else if (id == 4.0) {\nextrude = -ortho + parallel;\nv_texcoord = vec2(1.0, 1.0);\n}\nfadeOpacity = getFadeOpacity((t - clampedTime) / u_trailLength);\nintroOpacity = 1.0 - exp(-clampedTime);\nv_size = getSize(speed);\nv_color = getColor(speed);\nv_color.a *= getOpacity(speed);\nposition = mix(position0, position1, f);\n} else {\nif (fUnclamped < 0.0) {\ngl_Position = vec4(0.0, 0.0, -2.0, 1.0);\nreturn;\n}\nif (id == 1.0) {\nextrude = e0;\nv_texcoord = vec2(0.5, 0.0);\nfadeOpacity = getFadeOpacity((t - t0) / u_trailLength);\nintroOpacity = 1.0 - exp(-t0);\nv_size = getSize(speed0);\nv_color = getColor(speed0);\nv_color.a *= getOpacity(speed0);\nposition = position0;\n} else if (id == 2.0) {\nextrude = -e0;\nv_texcoord = vec2(0.5, 1.0);\nfadeOpacity = getFadeOpacity((t - t0) / u_trailLength);\nintroOpacity = 1.0 - exp(-t0);\nv_size = getSize(speed0);\nv_color = getColor(speed0);\nv_color.a *= getOpacity(speed0);\nposition = position0;\n} else if (id == 3.0) {\nextrude = mix(e0, e1, f);\nv_texcoord = vec2(0.5, 0.0);\nfadeOpacity = getFadeOpacity((t - clampedTime) / u_trailLength);\nintroOpacity = 1.0 - exp(-clampedTime);\nv_size = getSize(speed);\nv_color = getColor(speed);\nv_color.a *= getOpacity(speed);\nposition = mix(position0, position1, f);\n} else if (id == 4.0) {\nextrude = -mix(e0, e1, f);\nv_texcoord = vec2(0.5, 1.0);\nfadeOpacity = getFadeOpacity((t - clampedTime) / u_trailLength);\nintroOpacity = 1.0 - exp(-clampedTime);\nv_size = getSize(speed);\nv_color = getColor(speed);\nv_color.a *= getOpacity(speed);\nposition = mix(position0, position1, f);\n}\n}\nvec2 xy = (u_dvsMat3 * vec3(position, 1.0) + u_displayViewMat3 * vec3(extrude * v_size, 0.0)).xy;\ngl_Position = vec4(xy, 0.0, 1.0);\nv_color.a *= fadeOpacity;\nv_color.a *= mix(1.0, introOpacity, u_introFade);\nv_color.rgb *= v_color.a;\n}" }, streamlines: { "streamlines.frag": "precision highp float;\nvarying float v_side;\nvarying float v_time;\nvarying float v_totalTime;\nvarying float v_timeSeed;\nvarying vec4 v_color;\nvarying float v_size;\nuniform float u_time;\nuniform float u_trailLength;\nuniform float u_flowSpeed;\nuniform float u_featheringSize;\nuniform float u_introFade;\n#include <raster/flow/getFragmentColor.glsl>\n#include <raster/flow/getFadeOpacity.glsl>\nvoid main(void) {\nfloat t = mod(v_timeSeed * (v_totalTime + u_trailLength) + u_time * u_flowSpeed, v_totalTime + u_trailLength) - v_time;\nvec4 color = v_color * step(0.0, t) * getFadeOpacity(t / u_trailLength);\ncolor *= mix(1.0, 1.0 - exp(-v_time), u_introFade);\ngl_FragColor = getFragmentColor(color, length((v_side + 1.0) / 2.0 - 0.5), v_size, u_featheringSize);\n}", "streamlines.vert": "attribute vec3 a_positionAndSide;\nattribute vec3 a_timeInfo;\nattribute vec2 a_extrude;\nattribute float a_speed;\nuniform mat3 u_dvsMat3;\nuniform mat3 u_displayViewMat3;\nvarying float v_time;\nvarying float v_totalTime;\nvarying float v_timeSeed;\nvarying vec4 v_color;\nvarying float v_side;\nvarying float v_size;\nuniform float u_featheringSize;\n#include <raster/flow/vv.glsl>\nvoid main(void) {\nvec4 lineColor = getColor(a_speed);\nfloat lineOpacity = getOpacity(a_speed);\nfloat lineSize = getSize(a_speed);\nvec2 position = a_positionAndSide.xy;\nv_side = a_positionAndSide.z;\nvec2 xy = (u_dvsMat3 * vec3(position, 1.0) + u_displayViewMat3 * vec3(a_extrude * lineSize, 0.0)).xy;\ngl_Position = vec4(xy, 0.0, 1.0);\nv_time = a_timeInfo.x;\nv_totalTime = a_timeInfo.y;\nv_timeSeed = a_timeInfo.z;\nv_color = lineColor;\nv_color.a *= lineOpacity;\nv_color.rgb *= v_color.a;\nv_size = lineSize;\n}" }, "vv.glsl": "#define MAX_STOPS 8\n#ifdef VV_COLOR\nuniform float u_color_stops[MAX_STOPS];\nuniform vec4 u_color_values[MAX_STOPS];\nuniform int u_color_count;\n#else\nuniform vec4 u_color;\n#endif\n#ifdef VV_OPACITY\nuniform float u_opacity_stops[MAX_STOPS];\nuniform float u_opacity_values[MAX_STOPS];\nuniform int u_opacity_count;\n#else\nuniform float u_opacity;\n#endif\n#ifdef VV_SIZE\nuniform float u_size_stops[MAX_STOPS];\nuniform float u_size_values[MAX_STOPS];\nuniform int u_size_count;\n#else\nuniform float u_size;\n#endif\nuniform float u_featheringOffset;\nvec4 getColor(float x) {\n#ifdef VV_COLOR\nvec4 color = u_color_values[0];\n{\nfor (int i = 1; i < MAX_STOPS; i++) {\nif (i >= u_color_count) {\nbreak;\n}\nfloat x1 = u_color_stops[i - 1];\nif (x < x1) {\nbreak;\n}\nfloat x2 = u_color_stops[i];\nvec4 y2 = u_color_values[i];\nif (x < x2) {\nvec4 y1 = u_color_values[i - 1];\ncolor = y1 + (y2 - y1) * (x - x1) / (x2 - x1);\n} else {\ncolor = y2;\n}\n}\n}\n#else\nvec4 color = u_color;\n#endif\nreturn color;\n}\nfloat getOpacity(float x) {\n#ifdef VV_OPACITY\nfloat opacity = u_opacity_values[0];\n{\nfor (int i = 1; i < MAX_STOPS; i++) {\nif (i >= u_opacity_count) {\nbreak;\n}\nfloat x1 = u_opacity_stops[i - 1];\nif (x < x1) {\nbreak;\n}\nfloat x2 = u_opacity_stops[i];\nfloat y2 = u_opacity_values[i];\nif (x < x2) {\nfloat y1 = u_opacity_values[i - 1];\nopacity = y1 + (y2 - y1) * (x - x1) / (x2 - x1);\n} else {\nopacity = y2;\n}\n}\n}\n#else\nfloat opacity = u_opacity;\n#endif\nreturn opacity;\n}\nfloat getSize(float x) {\n#ifdef VV_SIZE\nfloat size = u_size_values[0];\n{\nfor (int i = 1; i < MAX_STOPS; i++) {\nif (i >= u_size_count) {\nbreak;\n}\nfloat x1 = u_size_stops[i - 1];\nif (x < x1) {\nbreak;\n}\nfloat x2 = u_size_stops[i];\nfloat y2 = u_size_values[i];\nif (x < x2) {\nfloat y1 = u_size_values[i - 1];\nsize = y1 + (y2 - y1) * (x - x1) / (x2 - x1);\n} else {\nsize = y2;\n}\n}\n}\n#else\nfloat size = u_size;\n#endif\nreturn size + 2.0 * u_featheringSize * u_featheringOffset;\n}" }, hillshade: { "hillshade.frag": "precision mediump float;\nvarying highp vec2 v_texcoord;\n#include <raster/common/common.glsl>\nuniform int u_hillshadeType;\nuniform float u_sinZcosAs[6];\nuniform float u_sinZsinAs[6];\nuniform float u_cosZs[6];\nuniform float u_weights[6];\nuniform vec2 u_factor;\nuniform float u_minValue;\nuniform float u_maxValue;\n#include <raster/lut/colorize.glsl>\nfloat getNeighborHoodAlpha(float a, float b, float c, float d, float e, float f, float g, float h, float i){\nif (a == 0.0 || a == 0.0 || a==0.0 || a == 0.0 || a == 0.0 || a==0.0 || a == 0.0 || a == 0.0 || a==0.0) {\nreturn 0.0;\n}\nelse {\nreturn e;\n}\n}\nvec3 rgb2hsv(vec3 c) {\nvec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);\nvec4 p = c.g < c.b ? vec4(c.bg, K.wz) : vec4(c.gb, K.xy);\nvec4 q = c.r < p.x ? vec4(p.xyw, c.r) : vec4(c.r, p.yzx);\nfloat d = q.x - min(q.w, q.y);\nfloat e = 1.0e-10;\nreturn vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), min(d / (q.x + e), 1.0), q.x);\n}\nvec3 hsv2rgb(vec3 c) {\nvec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);\nvec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);\nreturn c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);\n}\nvec4 overlay(float val, float minValue, float maxValue, float hillshade) {\nval = clamp((val - minValue) / (maxValue - minValue), 0.0, 1.0);\nvec4 rgb = colorize(vec4(val, val, val, 1.0), 255.0);\nvec3 hsv = rgb2hsv(rgb.xyz);\nhsv.z = hillshade;\nreturn vec4(hsv2rgb(hsv), 1.0) * rgb.a;\n}\nvoid main() {\nvec2 pixelLocation = getPixelLocation(v_texcoord);\nif (isOutside(pixelLocation)) {\ngl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\nreturn;\n}\nvec4 currentPixel = getPixel(pixelLocation);\nif (currentPixel.a == 0.0) {\ngl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\nreturn;\n}\nvec2 axy = vec2(-1.0, -1.0);\nvec2 bxy = vec2(0.0, -1.0);\nvec2 cxy = vec2(1.0, -1.0);\nvec2 dxy = vec2(-1.0, 0.0);\nvec2 fxy = vec2(1.0, 0.0);\nvec2 gxy = vec2(-1.0, 1.0);\nvec2 hxy = vec2(0.0, 1.0);\nvec2 ixy = vec2(1.0, 1.0);\nvec2 onePixel = 1.0 / u_srcImageSize;\nif (pixelLocation.s < onePixel.s) {\naxy[0] = 1.0;\ndxy[0] = 1.0;\ngxy[0] = 1.0;\n}\nif (pixelLocation.t < onePixel.t) {\naxy[1] = 1.0;\nbxy[1] = 1.0;\ncxy[1] = 1.0;\n}\nif (pixelLocation.s > 1.0 - onePixel.s) {\ncxy[0] = -1.0;\nfxy[0] = -1.0;\nixy[0] = -1.0;\n}\nif (pixelLocation.t > 1.0 - onePixel.t) {\ngxy[1] = -1.0;\nhxy[1] = -1.0;\nixy[1] = -1.0;\n}\nvec4 va = texture2D(u_image, pixelLocation + onePixel * axy);\nvec4 vb = texture2D(u_image, pixelLocation + onePixel * bxy);\nvec4 vc = texture2D(u_image, pixelLocation + onePixel * cxy);\nvec4 vd = texture2D(u_image, pixelLocation + onePixel * dxy);\nvec4 ve = texture2D(u_image, pixelLocation);\nvec4 vf = texture2D(u_image, pixelLocation + onePixel * fxy);\nvec4 vg = texture2D(u_image, pixelLocation + onePixel * gxy);\nvec4 vh = texture2D(u_image, pixelLocation + onePixel * hxy);\nvec4 vi = texture2D(u_image, pixelLocation + onePixel * ixy);\nfloat dzx = (vc + 2.0 * vf + vi - va - 2.0 * vd - vg).r * u_factor.s;\nfloat dzy = (vg + 2.0 * vh + vi - va - 2.0 * vb - vc).r * u_factor.t;\nfloat dzd = sqrt(1.0 + dzx * dzx + dzy * dzy);\nfloat hillshade = 0.0;\nif (u_hillshadeType == 0){\nfloat cosDelta = u_sinZsinAs[0] * dzy - u_sinZcosAs[0] * dzx;\nfloat z = (u_cosZs[0] + cosDelta) / dzd;\nif (z < 0.0)  z = 0.0;\nhillshade = z;\n} else {\nfor (int k = 0; k < 6; k++) {\nfloat cosDelta = u_sinZsinAs[k] * dzy - u_sinZcosAs[k] * dzx;\nfloat z = (u_cosZs[k] + cosDelta) / dzd;\nif (z < 0.0) z = 0.0;\nhillshade = hillshade + z * u_weights[k];\nif (k == 5) break;\n}\n}\nfloat alpha = getNeighborHoodAlpha(va.a, vb.a, vc.a, vd.a, ve.a, vf.a, vg.a, vh.a, vi.a);\n#ifdef APPLY_COLORMAP\ngl_FragColor = overlay(ve.r, u_minValue, u_maxValue, hillshade) * alpha * u_opacity;\n#else\ngl_FragColor = vec4(hillshade, hillshade, hillshade, 1.0) * alpha * u_opacity;\n#endif\n}" }, lut: { "colorize.glsl": "uniform sampler2D u_colormap;\nuniform float u_colormapOffset;\nuniform float u_colormapMaxIndex;\nvec4 colorize(vec4 currentPixel, float scaleFactor) {\nfloat clrIndex = clamp(currentPixel.r * scaleFactor - u_colormapOffset, 0.0, u_colormapMaxIndex);\nvec2 clrPosition = vec2((clrIndex + 0.5) / (u_colormapMaxIndex + 1.0), 0.0);\nvec4 color = texture2D(u_colormap, clrPosition);\nvec4 result = vec4(color.rgb, color.a * currentPixel.a);\nreturn result;\n}", "lut.frag": "precision mediump float;\nvarying highp vec2 v_texcoord;\n#include <raster/common/common.glsl>\n#include <raster/lut/colorize.glsl>\nvoid main() {\nvec2 pixelLocation = getPixelLocation(v_texcoord);\nif (isOutside(pixelLocation)) {\ngl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\nreturn;\n}\nvec4 currentPixel = getPixel(pixelLocation);\nvec4 result = colorize(currentPixel, 1.0);\ngl_FragColor = vec4(result.xyz, 1.0) * result.a * u_opacity;\n}" }, magdir: { "magdir.frag": "precision mediump float;\nvarying vec4 v_color;\nuniform lowp float u_opacity;\nvoid main() {\ngl_FragColor = v_color * u_opacity;\n}", "magdir.vert": "precision mediump float;\nattribute vec2 a_pos;\nattribute vec2 a_offset;\nattribute vec2 a_vv;\nuniform highp mat3 u_dvsMat3;\nuniform highp vec2 u_coordScale;\nuniform vec2 u_symbolSize;\nuniform vec2 u_symbolPercentRange;\nuniform vec2 u_dataRange;\nuniform float u_rotation;\nuniform vec4 u_colors[12];\nvarying vec4 v_color;\nvoid main()\n{\nfloat angle = a_offset.y + u_rotation;\n#ifndef ROTATION_GEOGRAPHIC\nangle = 3.14159265359 * 2.0 - angle - 3.14159265359 / 2.0;\n#endif\nvec2 offset = vec2(cos(angle), sin(angle)) * a_offset.x;\n#ifdef DATA_RANGE\nfloat valuePercentage = clamp((a_vv.y - u_dataRange.x) / (u_dataRange.y - u_dataRange.x), 0.0, 1.0);\nfloat sizeRatio = u_symbolPercentRange.x + valuePercentage * (u_symbolPercentRange.y - u_symbolPercentRange.x);\nfloat sizePercentage = clamp(sizeRatio, u_symbolPercentRange.x, u_symbolPercentRange.y);\n#else\nfloat sizePercentage = (u_symbolPercentRange.x + u_symbolPercentRange.y) / 2.0;\n#endif\nvec2 pos = a_pos + offset * sizePercentage * u_symbolSize;\nv_color = u_colors[int(a_vv.x)];\ngl_Position = vec4(u_dvsMat3 * vec3(pos * u_coordScale, 1.0), 1.0);\n}" }, reproject: { "reproject.frag": "precision mediump float;\nvarying vec2 v_texcoord;\n#include <raster/common/common.glsl>\nvoid main() {\nvec2 pixelLocation = getPixelLocation(v_texcoord);\nif (isOutside(pixelLocation)) {\ngl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\nreturn;\n}\nvec4 currentPixel = getPixel(pixelLocation);\ngl_FragColor = vec4(currentPixel.rgb, 1.0) * currentPixel.a * u_opacity;\n}", "reproject.vert": "precision mediump float;\nattribute vec2 a_position;\nvarying highp vec2 v_texcoord;\nvoid main()\n{\nv_texcoord = a_position;\ngl_Position = vec4(2.0 * (a_position - 0.5), 0.0, 1.0);\n}" }, rfx: { aspect: { "aspect.frag": "precision mediump float;\nuniform sampler2D u_image;\nvarying vec2 v_texcoord;\nuniform vec2 u_cellSize;\nuniform vec2 u_srcImageSize;\n#include <raster/common/mirror.glsl>\nconst float pi = 3.14159265359;\nvoid main() {\nvec2 axy = vec2(-1.0, -1.0);\nvec2 bxy = vec2(0.0, -1.0);\nvec2 cxy = vec2(1.0, -1.0);\nvec2 dxy = vec2(-1.0, 0.0);\nvec2 fxy = vec2(1.0, 0.0);\nvec2 gxy = vec2(-1.0, 1.0);\nvec2 hxy = vec2(0.0, 1.0);\nvec2 ixy = vec2(1.0, 1.0);\nvec2 onePixel = 1.0 / u_srcImageSize;\nvec4 va = texture2D(u_image, mirror(v_texcoord + onePixel * axy));\nvec4 vb = texture2D(u_image, mirror(v_texcoord + onePixel * bxy));\nvec4 vc = texture2D(u_image, mirror(v_texcoord + onePixel * cxy));\nvec4 vd = texture2D(u_image, mirror(v_texcoord + onePixel * dxy));\nvec4 ve = texture2D(u_image, mirror(v_texcoord + onePixel * vec2(0, 0)));\nvec4 vf = texture2D(u_image, mirror(v_texcoord + onePixel * fxy));\nvec4 vg = texture2D(u_image, mirror(v_texcoord + onePixel * gxy));\nvec4 vh = texture2D(u_image, mirror(v_texcoord + onePixel * hxy));\nvec4 vi = texture2D(u_image, mirror(v_texcoord + onePixel * ixy));\nfloat dzx = (vc + 2.0 * vf + vi - va - 2.0 * vd - vg).r / (8.0 * u_cellSize[0]);\nfloat dzy = -(vg + 2.0 * vh + vi - va - 2.0 * vb - vc).r / (8.0 * u_cellSize[1]);\nfloat alpha = va.a * vb.a * vc.a * vd.a * ve.a * vf.a * vg.a * vh.a * vi.a * sign(abs(dzx) + abs(dzy));\nfloat aspect_rad = (dzx == 0.0) ? (step(0.0, dzy) * 0.5 * pi + step(dzy, 0.0) * 1.5 * pi) : mod((2.5 * pi + atan(dzy, -dzx)), 2.0 * pi);\nfloat aspect = aspect_rad * 180.0 / pi;\ngl_FragColor = vec4(aspect, aspect, aspect, 1.0) * alpha;\n}" }, bandarithmetic: { "bandarithmetic.frag": "precision mediump float;\nuniform sampler2D u_image;\nvarying vec2 v_texcoord;\nuniform mediump mat3 u_bandIndexMat3;\nuniform float u_adjustments[3];\n#include <raster/common/inverse.glsl>\nvoid main() {\nvec4 pv = texture2D(u_image, v_texcoord);\nvec3 pv2 = u_bandIndexMat3 * pv.rgb;\nfloat nir = pv2.r;\nfloat red = pv2.g;\nfloat index;\n#ifdef NDXI\nindex = (nir - red) * invertValue(nir + red);\n#elif defined(SR)\nindex = nir * invertValue(red);\n#elif defined(CI)\nindex = nir * invertValue(red) - 1.0;\n#elif defined(SAVI)\nindex = (nir - red) * invertValue(nir + red + u_adjustments[0]) * (1.0 + u_adjustments[0]);\n#elif defined(TSAVI)\nfloat s = u_adjustments[0];\nfloat a = u_adjustments[1];\nfloat x = u_adjustments[2];\nfloat y = -a * s + x * (1.0 + s * s);\nindex = (s * (nir - s * red - a)) * invertValue(a * nir + red + y);\n#elif defined(MAVI)\nindex = 0.5 * (2.0 * (nir + 1.0) - sqrt(pow((2.0 * nir + 1.0), 2.0) - 8.0 * (nir - red)));\n#elif defined(GEMI)\nfloat eta = (2.0 * (nir * nir - red * red) + 1.5 * nir + 0.5 * red) * invertValue(nir + red + 0.5);\nindex = eta * (1.0 - 0.25 * eta) - (red - 0.125) * invertValue(1.0 - red);\n#elif defined(PVI)\nfloat a = u_adjustments[0];\nfloat b = u_adjustments[1];\nfloat y = sqrt(1.0 + a * a);\nindex = (nir - a * red - b) * invertValue(y);\n#elif defined(VARI)\nindex = (pv2.g - pv2.r) * invertValue(pv2.g + pv2.r - pv2.b);\n#elif defined(MTVI2)\nfloat green = pv2.b;\nfloat v = pow(sqrt((2.0 * nir + 1.0), 2.0) - 6.0 * nir - 5.0 * sqrt(red) - 0.5);\nindex = 1.5 * (1.2 * (nir - green) - 2.5 * (red - green)) * v;\n#elif defined(RTVICORE)\nfloat green = pv2.b;\nindex = 100.0 * (nir - red) - 10.0 * (nir - green);\n#elif defined(EVI)\nfloat blue = pv2.b;\nfloat denom = nir + 6.0 * red - 7.5 * blue + 1.0;\nindex =  (2.5 * (nir - red)) * invertValue(denom);\n#elif defined(WNDWI)\nfloat g = pv2.r;\nfloat n = pv2.g;\nfloat s = pv2.s;\nfloat a = u_adjustments[0];\nfloat denom = g + a * n + (1.0 - a) * s;\nindex = (g - a * n - (1 - a) * s) * invertValue(denom);\n#elif defined(BAI)\nindex = invertValue(pow((0.1 - red), 2.0) + pow((0.06 - nir), 2.0));\n#else\ngl_FragColor = pv;\nreturn;\n#endif\ngl_FragColor = vec4(index, index, index, pv.a);\n}" }, compositeband: { "compositeband.frag": "precision mediump float;\nuniform sampler2D u_image;\nuniform sampler2D u_image1;\nuniform sampler2D u_image2;\nvarying vec2 v_texcoord;\nvoid main() {\nvec4 p0 = texture2D(u_image, v_texcoord);\nvec4 p1 = texture2D(u_image1, v_texcoord);\nvec4 p2 = texture2D(u_image2, v_texcoord);\ngl_FragColor = vec4(p0.r, p1.r, p2.r, p0.a * p1.a * p2.a);\n}" }, convolution: { "convolution.frag": "precision mediump float;\nuniform sampler2D u_image;\nvarying vec2 v_texcoord;\nuniform vec2 u_srcImageSize;\n#define KERNEL_SIZE_ROWS ROWS\n#define KERNEL_SIZE_COLS COLS\nuniform vec2 u_clampRange;\nuniform float u_kernel[25];\n#include <raster/common/mirror.glsl>\nvoid main() {\nvec3 rgb = vec3(0.0, 0.0, 0.0);\nvec2 resolution = 1.0 / u_srcImageSize;\nfloat rowOffset = -float(floor(float(KERNEL_SIZE_ROWS) / 2.0));\nfloat colOffset = -float(floor(float(KERNEL_SIZE_COLS) / 2.0));\nfloat alpha = 1.0;\nfor (int row = 0; row < KERNEL_SIZE_ROWS; row++) {\nfloat pos_row = rowOffset + float(row);\nfor (int col = 0; col < KERNEL_SIZE_COLS; col++) {\nvec2 pos = v_texcoord + vec2(colOffset + float(col), pos_row) * resolution;\nvec4 pv = texture2D(u_image, mirror(pos));\nrgb += pv.rgb * u_kernel[row * KERNEL_SIZE_COLS + col];\nalpha *= pv.a;\n}\n}\nrgb = clamp(rgb, u_clampRange.s, u_clampRange.t);\ngl_FragColor = vec4(rgb * alpha, alpha);\n}" }, extractband: { "extractband.frag": "precision mediump float;\nuniform sampler2D u_image;\nvarying vec2 v_texcoord;\nuniform mediump mat3 u_bandIndexMat3;\nvoid main() {\nvec4 pv = texture2D(u_image, v_texcoord);\nvec3 pv2 = u_bandIndexMat3 * pv.rgb;\ngl_FragColor = vec4(pv2, pv.a);\n}" }, local: { "local.frag": "precision mediump float;\nuniform sampler2D u_image;\nuniform sampler2D u_image1;\n#ifdef ONE_CONSTANT\nuniform float u_image1Const;\n#ifdef TWO_CONSTANT\nuniform float u_image2Const;\n#endif\nuniform mat3 u_imageSwap;\n#endif\nvarying vec2 v_texcoord;\nuniform vec2 u_domainRange;\n#include <raster/common/inverse.glsl>\nvoid main() {\nvec4 pv0 = texture2D(u_image, v_texcoord);\nfloat a = pv0.r;\n#ifdef TWO_IMAGES\n#ifdef ONE_CONSTANT\nfloat b = u_image1Const;\nvec3 abc = u_imageSwap * vec3(a, b, 0);\na = abc.s;\nb = abc.t;\n#else\nvec4 pv1 = texture2D(u_image1, v_texcoord);\nfloat b = pv1.r;\n#endif\n#elif defined(CONDITIONAL)\n#ifdef TWO_CONSTANT\nfloat b = u_image1Const;\nfloat c = u_image2Const;\nvec3 abc = u_imageSwap * vec3(a, b, c);\na = abc.s;\nb = abc.t;\nc = abc.p;\n#elif defined(ONE_CONSTANT)\nvec4 pv1 = texture2D(u_image1, v_texcoord);\nfloat b = pv1.r;\nfloat c = u_image1Const;\nvec3 abc = u_imageSwap * vec3(a, b, c);\na = abc.s;\nb = abc.t;\nc = abc.p;\n#else\nvec4 pv1 = texture2D(u_image1, v_texcoord);\nvec4 pv2 = texture2D(u_image2, v_texcoord);\nfloat b = pv1.r;\nfloat c = pv2.r;\n#endif\n#endif\nfloat result = a;\nfloat alpha = pv0.a;\n#ifdef PLUS\nresult = a + b;\n#elif defined(MINUS)\nresult = a - b;\n#elif defined(TIMES)\nresult = a * b;\n#elif defined(DIVIDE)\nresult = a * invertValue(b);\nalpha *= float(abs(sign(b)));\n#elif defined(FLOATDIVIDE)\nresult = a * invertValue(b);\nalpha *= float(abs(sign(b)));\n#elif defined(FLOORDIVIDE)\nresult = floor(a * invertValue(b));\nalpha *= float(abs(sign(b)));\n#elif defined(SQUARE)\nresult = a * a;\n#elif defined(SQRT)\nresult = sqrt(a);\n#elif defined(POWER)\nresult = pow(a, b);\n#elif defined(LN)\nresult = a <= 0.0 ? 0.0: log(a);\nalpha *= float(a > 0.0);\n#elif defined(LOG_1_0)\nresult = a <= 0.0 ? 0.0: log2(a) * invertValue(log2(10.0));\nalpha *= float(a > 0.0);\n#elif defined(LOG_2)\nresult = a <= 0.0 ? 0.0: log2(a);\nalpha *= float(a > 0.0);\n#elif defined(EXP)\nresult = exp(a);\n#elif defined(EXP_1_0)\nresult = pow(10.0, a);\n#elif defined(EXP_2)\nresult = pow(2.0, a);\n#elif defined(ROUNDDOWN)\nresult = floor(a);\n#elif defined(ROUNDUP)\nresult = ceil(a);\n#elif defined(INT)\nresult = float(sign(a)) * floor(abs(a));\n#elif defined(MOD)\nresult = mod(a, b);\n#elif defined(NEGATE)\nresult = -a;\n#elif defined(ABS)\nresult = abs(a);\n#elif defined(ACOS)\nresult = abs(a) > 1.0 ? 0.0: acos(a);\nalpha *= step(abs(a), 1.00001);\n#elif defined(ACOSH)\nresult = acosh(a);\n#elif defined(POLYFILLACOSH)\nresult = log(a + sqrt(a * a - 1.0));\n#elif defined(ASIN)\nresult = abs(a) > 1.0 ? 0.0: asin(a);\nalpha *= step(abs(a), 1.00001);\n#elif defined(ASINH)\nresult = asinh(a);\n#elif defined(POLYFILLASINH)\nresult = log(a + sqrt(a * a + 1.0));\n#elif defined(ATAN)\nresult = atan(a);\n#elif defined(ATANH)\nresult = abs(a) > 1.0 ? 0.0: atanh(a);\nalpha *= step(abs(a), 1.0);\n#elif defined(POLYFILLATANH)\nresult = a == 1.0 ? 0.0 : 0.5 * log((1.0 + a)/(1.0 -a));\n#elif defined(ATAN_2)\nresult = atan(a, b);\n#elif defined(COS)\nresult = cos(a);\n#elif defined(COSH)\nresult = cosh(a);\n#elif defined(POLYFILLCOSH)\nfloat halfexp = exp(a) / 2.0;\nresult = halfexp + 1.0 / halfexp;\n#elif defined(SIN)\nresult = sin(a);\n#elif defined(SINH)\nresult = sinh(a);\n#elif defined(POLYFILLSINH)\nfloat halfexp = exp(a) / 2.0;\nresult = halfexp - 1.0 / halfexp;\n#elif defined(TAN)\nresult = tan(a);\n#elif defined(TANH)\nresult = tanh(a);\n#elif defined(POLYFILLTANH)\nfloat expx = exp(a);\nresult = (expx - 1.0 / expx) / (expx + 1.0 / expx);\n#elif defined(BITWISEAND)\nresult = a & b;\n#elif defined(BITWISEOR)\nresult = a | b;\n#elif defined(BITWISELEFTSHIFT)\nresult = a << b;\n#elif defined(BITWISERIGHTSHIFT)\nresult = a >> b;\n#elif defined(BITWISENOT)\nresult = ~a;\n#elif defined(BITWISEXOR)\nresult = a ^ b;\n#elif defined(BOOLEANAND)\nresult = float((a != 0.0) && (b != 0.0));\n#elif defined(BOOLEANNOT)\nresult = float(a == 0.0);\n#elif defined(BOOLEANOR)\nresult = float((a != 0.0) || (b != 0.0));\n#elif defined(BOOLEANXOR)\nresult = float((a != 0.0) ^^ (b != 0.0));\n#elif defined(GREATERTHAN)\nresult = float(a > b);\n#elif defined(GREATERTHANEQUAL)\nresult = float(a >= b);\n#elif defined(LESSTHAN)\nresult = float(a < b);\n#elif defined(LESSTHANEQUAL)\nresult = float(a <= b);\n#elif defined(EQUALTO)\nresult = float(a == b);\n#elif defined(NOTEQUAL)\nresult = float(a != b);\n#elif defined(ISNULL)\nresult = float(alpha == 0.0);\nalpha = 1.0;\n#elif defined(SETNULL)\nfloat maskValue = float(a == 0.0);\nresult = maskValue * b;\nalpha *= maskValue;\n#elif defined(CONDITIONAL)\nfloat weight = float(abs(sign(a)));\nresult = weight * b + (1.0 - weight) * c;\n#endif\nbool isInvalid = result < u_domainRange.s || result > u_domainRange.t;\nresult = isInvalid ? 0.0 : result;\nalpha *= float(!isInvalid);\n#ifdef ROUND_OUTPUT\nresult = floor(result + 0.5);\n#endif\ngl_FragColor = vec4(result, result, result, alpha);\n}" }, mask: { "mask.frag": "precision mediump float;\nuniform sampler2D u_image;\nvarying vec2 v_texcoord;\n#define LEN_INCLUDED_RANGES 6\n#define LEN_NODATA_VALUES 6\nuniform highp float u_includedRanges[6];\nuniform highp float u_noDataValues[6];\nfloat maskFactor(float bandValue, float fromValue, float to) {\nfloat factor = 1.0;\nfor (int i = 0; i < LEN_NODATA_VALUES; i++) {\nfactor *= float(u_noDataValues[i] != bandValue);\n}\nfactor *= step(fromValue, bandValue) * step(bandValue, to);\nreturn factor;\n}\nvoid main() {\nvec4 pv = texture2D(u_image, v_texcoord);\nfloat redFactor = maskFactor(pv.r, u_includedRanges[0], u_includedRanges[1]);\n#ifdef MULTI_BAND\nfloat greenFactor = maskFactor(pv.g, u_includedRanges[2], u_includedRanges[3]);\nfloat blueFactor = maskFactor(pv.b, u_includedRanges[4], u_includedRanges[5]);\nfloat maskFactor = redFactor * greenFactor * blueFactor;\ngl_FragColor = pv * maskFactor;\n#else\ngl_FragColor = pv * redFactor;\n#endif\n}" }, ndvi: { "ndvi.frag": "precision mediump float;\nuniform sampler2D u_image;\nvarying vec2 v_texcoord;\nuniform mediump mat3 u_bandIndexMat3;\n#include <raster/common/inverse.glsl>\nvoid main() {\nvec4 pv = texture2D(u_image, v_texcoord);\nvec3 pv2 = u_bandIndexMat3 * pv.rgb;\nfloat nir = pv2.r;\nfloat red = pv2.g;\nfloat index = (nir - red) * invertValue(nir + red);\n#ifdef SCALED\nindex = floor((index + 1.0) * 100.0 + 0.5);\n#endif\ngl_FragColor = vec4(index, index, index, pv.a);\n}" }, remap: { "remap.frag": "precision mediump float;\nuniform sampler2D u_image;\nvarying vec2 v_texcoord;\n#define LEN_REMAP_RANGES 18\n#define LEN_NODATA_RANGES 12\nuniform highp float u_rangeMaps[18];\nuniform highp float u_noDataRanges[12];\nuniform highp float u_unmatchMask;\nuniform vec2 u_clampRange;\nvoid main() {\nvec4 pv = texture2D(u_image, v_texcoord);\nfloat factor = 1.0;\nfloat bandValue = pv.r;\nfor (int i = 0; i < LEN_NODATA_RANGES; i+=2) {\nfloat inside = 1.0 - step(u_noDataRanges[i], bandValue) * step(bandValue, u_noDataRanges[i+1]);\nfactor *= inside;\n}\nfloat mapValue = 0.0;\nfloat includeMask = 0.0;\nfor (int i = 0; i < LEN_REMAP_RANGES; i+=3) {\nfloat stepMask = step(u_rangeMaps[i], bandValue) * step(bandValue, u_rangeMaps[i+1]);\nincludeMask = (1.0 - stepMask) * includeMask + stepMask;\nmapValue = (1.0 - stepMask) * mapValue + stepMask * u_rangeMaps[i+2];\n}\nbandValue = factor * (mapValue + (1.0 - includeMask) * u_unmatchMask * pv.r);\nfloat bandMask = factor * max(u_unmatchMask, includeMask);\nbandValue = clamp(bandValue, u_clampRange.s, u_clampRange.t);\ngl_FragColor = vec4(bandValue, bandValue, bandValue, bandMask * pv.a);\n}" }, slope: { "slope.frag": "precision mediump float;\nuniform sampler2D u_image;\nvarying vec2 v_texcoord;\nuniform vec2 u_cellSize;\nuniform float u_zFactor;\nuniform vec2 u_srcImageSize;\nuniform float u_pixelSizePower;\nuniform float u_pixelSizeFactor;\n#include <raster/common/mirror.glsl>\nvoid main() {\nvec2 axy = vec2(-1.0, -1.0);\nvec2 bxy = vec2(0.0, -1.0);\nvec2 cxy = vec2(1.0, -1.0);\nvec2 dxy = vec2(-1.0, 0.0);\nvec2 fxy = vec2(1.0, 0.0);\nvec2 gxy = vec2(-1.0, 1.0);\nvec2 hxy = vec2(0.0, 1.0);\nvec2 ixy = vec2(1.0, 1.0);\nvec2 onePixel = 1.0 / u_srcImageSize;\nvec4 va = texture2D(u_image, mirror(v_texcoord + onePixel * axy));\nvec4 vb = texture2D(u_image, mirror(v_texcoord + onePixel * bxy));\nvec4 vc = texture2D(u_image, mirror(v_texcoord + onePixel * cxy));\nvec4 vd = texture2D(u_image, mirror(v_texcoord + onePixel * dxy));\nvec4 ve = texture2D(u_image, mirror(v_texcoord + onePixel * vec2(0, 0)));\nvec4 vf = texture2D(u_image, mirror(v_texcoord + onePixel * fxy));\nvec4 vg = texture2D(u_image, mirror(v_texcoord + onePixel * gxy));\nvec4 vh = texture2D(u_image, mirror(v_texcoord + onePixel * hxy));\nvec4 vi = texture2D(u_image, mirror(v_texcoord + onePixel * ixy));\nfloat xf = (u_zFactor + pow(u_cellSize[0], u_pixelSizePower) * u_pixelSizeFactor) / (8.0 * u_cellSize[0]);\nfloat yf = (u_zFactor + pow(u_cellSize[1], u_pixelSizePower) * u_pixelSizeFactor) / (8.0 * u_cellSize[1]);\nfloat dzx = (vc + 2.0 * vf + vi - va - 2.0 * vd - vg).r * xf;\nfloat dzy = -(vg + 2.0 * vh + vi - va - 2.0 * vb - vc).r * yf;\nfloat alpha = va.a * vb.a * vc.a * vd.a * ve.a * vf.a * vg.a * vh.a * vi.a;\nfloat rise2run = sqrt(dzx * dzx + dzy * dzy);\n#ifdef PERCENT_RISE\nfloat percentRise = rise2run * 100.0;\ngl_FragColor = vec4(percentRise, percentRise, percentRise, alpha);\n#else\nfloat degree = atan(rise2run) * 57.2957795;\ngl_FragColor = vec4(degree, degree, degree, alpha);\n#endif\n}" }, stretch: { "stretch.frag": "precision mediump float;\nuniform sampler2D u_image;\nvarying highp vec2 v_texcoord;\nuniform float u_minCutOff[3];\nuniform float u_maxCutOff[3];\nuniform float u_minOutput;\nuniform float u_maxOutput;\nuniform float u_factor[3];\nuniform float u_gamma[3];\nuniform float u_gammaCorrection[3];\nfloat stretchOneValue(float val, float minCutOff, float maxCutOff, float minOutput, float maxOutput, float factor, float gamma, float gammaCorrection) {\nval = clamp(val, minCutOff, maxCutOff);\nfloat stretchedVal;\n#ifdef USE_GAMMA\nfloat tempf = 1.0;\nfloat outRange = maxOutput - minOutput;\nfloat relativeVal = (val - minCutOff) / (maxCutOff - minCutOff);\ntempf -= step(1.0, gamma) * sign(gamma - 1.0) * pow(1.0 / outRange, relativeVal * gammaCorrection);\nstretchedVal = tempf * outRange * pow(relativeVal, 1.0 / gamma) + minOutput;\nstretchedVal = clamp(stretchedVal, minOutput, maxOutput);\n#else\nstretchedVal = minOutput + (val - minCutOff) * factor;\n#endif\n#ifdef ROUND_OUTPUT\nstretchedVal = floor(stretchedVal + 0.5);\n#endif\nreturn stretchedVal;\n}\nvoid main() {\nvec4 currentPixel = texture2D(u_image, v_texcoord);\nfloat redVal = stretchOneValue(currentPixel.r, u_minCutOff[0], u_maxCutOff[0], u_minOutput, u_maxOutput, u_factor[0], u_gamma[0], u_gammaCorrection[0]);\n#ifdef MULTI_BAND\nfloat greenVal = stretchOneValue(currentPixel.g, u_minCutOff[1], u_maxCutOff[1], u_minOutput, u_maxOutput, u_factor[1], u_gamma[1], u_gammaCorrection[1]);\nfloat blueVal = stretchOneValue(currentPixel.b, u_minCutOff[2], u_maxCutOff[2], u_minOutput, u_maxOutput, u_factor[2], u_gamma[2], u_gammaCorrection[2]);\ngl_FragColor = vec4(redVal, greenVal, blueVal, currentPixel.a);\n#else\ngl_FragColor = vec4(redVal, redVal, redVal, currentPixel.a);\n#endif\n}" }, vs: { "vs.vert": "precision mediump float;\nattribute vec2 a_pos;\nuniform highp mat3 u_dvsMat3;\nuniform highp vec2 u_coordScale;\nvarying highp vec2 v_texcoord;\nvoid main()\n{\nv_texcoord = a_pos;\ngl_Position = vec4(u_dvsMat3 * vec3(a_pos * u_coordScale, 1.0), 1.0);\n}" } }, scalar: { "scalar.frag": "precision mediump float;\nuniform lowp float u_opacity;\nvarying vec2 v_pos;\nconst vec4 outlineColor = vec4(0.2, 0.2, 0.2, 1.0);\nconst float outlineSize = 0.02;\nconst float innerRadius = 0.25;\nconst float outerRadius = 0.42;\nconst float innerSquareLength = 0.15;\nvoid main() {\nmediump float dist = length(v_pos);\nmediump float fillalpha1 = smoothstep(outerRadius, outerRadius + outlineSize, dist);\nfillalpha1 *= (1.0-smoothstep(outerRadius + outlineSize, outerRadius + 0.1 + outlineSize, dist));\n#ifdef INNER_CIRCLE\nmediump float fillalpha2 = smoothstep(innerRadius, innerRadius + outlineSize, dist);\nfillalpha2 *= (1.0-smoothstep(innerRadius + outlineSize, innerRadius + 0.1 + outlineSize, dist));\n#else\nmediump float fillalpha2 = (abs(v_pos.x) < innerSquareLength ? 1.0 : 0.0) * (abs(v_pos.y) < innerSquareLength ? 1.0 : 0.0);\n#endif\ngl_FragColor = (fillalpha2 + fillalpha1) * outlineColor * u_opacity;\n}", "scalar.vert": "precision mediump float;\nattribute vec2 a_pos;\nattribute vec2 a_offset;\nattribute vec2 a_vv;\nuniform highp mat3 u_dvsMat3;\nuniform highp vec2 u_coordScale;\nuniform vec2 u_symbolSize;\nuniform vec2 u_symbolPercentRange;\nuniform vec2 u_dataRange;\nvarying vec2 v_pos;\nvoid main()\n{\n#ifdef DATA_RANGE\nfloat valuePercentage = clamp((a_vv.y - u_dataRange.x) / (u_dataRange.y - u_dataRange.x), 0.0, 1.0);\nfloat sizeRatio = u_symbolPercentRange.x + valuePercentage * (u_symbolPercentRange.y - u_symbolPercentRange.x);\nfloat sizePercentage = clamp(sizeRatio, u_symbolPercentRange.x, u_symbolPercentRange.y);\n#else\nfloat sizePercentage = (u_symbolPercentRange.x + u_symbolPercentRange.y) / 2.0;\n#endif\nvec2 size = u_symbolSize * sizePercentage;\nvec2 pos = a_pos + a_offset * size;\nv_pos = a_offset;\ngl_Position = vec4(u_dvsMat3 * vec3(pos * u_coordScale, 1.0), 1.0);\n}" }, stretch: { "stretch.frag": "precision mediump float;\nvarying highp vec2 v_texcoord;\n#include <raster/common/common.glsl>\nuniform float u_minCutOff[3];\nuniform float u_maxCutOff[3];\nuniform float u_minOutput;\nuniform float u_maxOutput;\nuniform float u_factor[3];\nuniform bool u_useGamma;\nuniform float u_gamma[3];\nuniform float u_gammaCorrection[3];\n#include <raster/lut/colorize.glsl>\nfloat stretchOneValue(float val, float minCutOff, float maxCutOff, float minOutput, float maxOutput, float factor, bool useGamma, float gamma, float gammaCorrection) {\nif (val >= maxCutOff) {\nreturn maxOutput;\n} else if (val <= minCutOff) {\nreturn minOutput;\n}\nfloat stretchedVal;\nif (useGamma) {\nfloat tempf = 1.0;\nfloat outRange = maxOutput - minOutput;\nfloat relativeVal = (val - minCutOff) / (maxCutOff - minCutOff);\nif (gamma > 1.0) {\ntempf -= pow(1.0 / outRange, relativeVal * gammaCorrection);\n}\nstretchedVal = (tempf * outRange * pow(relativeVal, 1.0 / gamma) + minOutput) / 255.0;\n} else {\nstretchedVal = minOutput + (val - minCutOff) * factor;\n}\nreturn stretchedVal;\n}\nvoid main() {\nvec2 pixelLocation = getPixelLocation(v_texcoord);\nif (isOutside(pixelLocation)) {\ngl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\nreturn;\n}\nvec4 currentPixel = getPixel(pixelLocation);\n#ifdef NOOP\ngl_FragColor = vec4(currentPixel.rgb, 1.0) * currentPixel.a * u_opacity;\nreturn;\n#endif\nif (u_bandCount == 1) {\nfloat grayVal = stretchOneValue(currentPixel.r, u_minCutOff[0], u_maxCutOff[0], u_minOutput, u_maxOutput, u_factor[0], u_useGamma, u_gamma[0], u_gammaCorrection[0]);\n#ifdef APPLY_COLORMAP\nvec4 result = colorize(vec4(grayVal, grayVal, grayVal, 1.0), u_useGamma ? 255.0 : 1.0);\ngl_FragColor = vec4(result.xyz, 1.0) * result.a * currentPixel.a * u_opacity;\n#else\ngl_FragColor = vec4(grayVal, grayVal, grayVal, 1.0) * currentPixel.a * u_opacity;\n#endif\n} else {\nfloat redVal = stretchOneValue(currentPixel.r, u_minCutOff[0], u_maxCutOff[0], u_minOutput, u_maxOutput, u_factor[0], u_useGamma, u_gamma[0], u_gammaCorrection[0]);\nfloat greenVal = stretchOneValue(currentPixel.g, u_minCutOff[1], u_maxCutOff[1], u_minOutput, u_maxOutput, u_factor[1], u_useGamma, u_gamma[1], u_gammaCorrection[1]);\nfloat blueVal = stretchOneValue(currentPixel.b, u_minCutOff[2], u_maxCutOff[2], u_minOutput, u_maxOutput, u_factor[2], u_useGamma, u_gamma[2], u_gammaCorrection[2]);\ngl_FragColor = vec4(redVal, greenVal, blueVal, 1.0) * currentPixel.a * u_opacity;\n}\n}" } }, stencil: { "stencil.frag": "void main() {\ngl_FragColor = vec4(1.0, 1.0, 1.0, 1.0);\n}", "stencil.vert": "attribute vec2 a_pos;\nuniform mat3 u_worldExtent;\nvoid main() {\ngl_Position = vec4(u_worldExtent * vec3(a_pos, 1.0), 1.0);\n}" }, tileInfo: { "tileInfo.frag": "uniform mediump sampler2D u_texture;\nvarying mediump vec2 v_tex;\nvoid main(void) {\nlowp vec4 color = texture2D(u_texture, v_tex);\ngl_FragColor = 0.75 * color;\n}", "tileInfo.vert": "attribute vec2 a_pos;\nuniform highp mat3 u_dvsMat3;\nuniform mediump float u_depth;\nuniform mediump vec2 u_coord_ratio;\nuniform mediump vec2 u_delta;\nuniform mediump vec2 u_dimensions;\nvarying mediump vec2 v_tex;\nvoid main() {\nmediump vec2 offset = u_coord_ratio * vec2(u_delta + a_pos * u_dimensions);\nvec3 v_pos = u_dvsMat3 * vec3(offset, 1.0);\ngl_Position = vec4(v_pos.xy, 0.0, 1.0);\nv_tex = a_pos;\n}" }, util: { "atan2.glsl": "float atan2(in float y, in float x) {\nfloat t0, t1, t2, t3, t4;\nt3 = abs(x);\nt1 = abs(y);\nt0 = max(t3, t1);\nt1 = min(t3, t1);\nt3 = 1.0 / t0;\nt3 = t1 * t3;\nt4 = t3 * t3;\nt0 =         - 0.013480470;\nt0 = t0 * t4 + 0.057477314;\nt0 = t0 * t4 - 0.121239071;\nt0 = t0 * t4 + 0.195635925;\nt0 = t0 * t4 - 0.332994597;\nt0 = t0 * t4 + 0.999995630;\nt3 = t0 * t3;\nt3 = (abs(y) > abs(x)) ? 1.570796327 - t3 : t3;\nt3 = x < 0.0 ?  3.141592654 - t3 : t3;\nt3 = y < 0.0 ? -t3 : t3;\nreturn t3;\n}", "encoding.glsl": "const vec4 rgba2float_factors = vec4(\n255.0 / (256.0),\n255.0 / (256.0 * 256.0),\n255.0 / (256.0 * 256.0 * 256.0),\n255.0 / (256.0 * 256.0 * 256.0 * 256.0)\n);\nfloat rgba2float(vec4 rgba) {\nreturn dot(rgba, rgba2float_factors);\n}" } };

// node_modules/@arcgis/core/views/2d/engine/webgl/shaders/sources/resolver.js
function o3(e7) {
  let o4 = e5;
  return e7.split("/").forEach((r10) => {
    o4 && (o4 = o4[r10]);
  }), o4;
}
var t7 = new e3(o3);
function n5(r10) {
  return t7.resolveIncludes(r10);
}

// node_modules/@arcgis/core/views/2d/engine/webgl/shaders/BackgroundPrograms.js
var e6 = { shaders: { vertexShader: n5("background/background.vert"), fragmentShader: n5("background/background.frag") }, attributes: /* @__PURE__ */ new Map([["a_pos", 0]]) };

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrush.js
var p2 = class extends t6 {
  constructor() {
    super(...arguments), this._computeDesc = /* @__PURE__ */ new Map();
  }
  prepareState({ context: t10 }, e7) {
    e7 && e7.includes("hittest") ? t10.setBlendFunctionSeparate(R.ONE, R.ONE, R.ONE, R.ONE) : t10.setBlendFunctionSeparate(R.ONE, R.ONE_MINUS_SRC_ALPHA, R.ONE, R.ONE_MINUS_SRC_ALPHA), t10.setBlendingEnabled(true), t10.setColorMask(true, true, true, true), t10.setStencilWriteMask(0), t10.setStencilTestEnabled(true);
  }
  draw(e7, i2, s7) {
    const o4 = this.getGeometryType();
    i2.commit(e7);
    const a2 = i2.getGeometry(o4);
    t(a2) || (e7.timeline.begin(this.name), e7.attributeView.bindTextures(e7.context), e7.context.setStencilFunction(I.EQUAL, i2.stencilRef, 255), a2.forEachCommand((t10) => {
      const o5 = U2.load(t10.materialKey).symbologyType;
      this.supportsSymbology(o5) && this.drawGeometry(e7, i2, t10, s7);
    }));
  }
  _setSharedUniforms(t10, m9, v2) {
    const { displayLevel: f3, pixelRatio: l6, state: p4, passOptions: c9 } = m9;
    r(c9) && "hittest" === c9.type && (t10.setUniform2fv("u_hittestPos", c9.position), t10.setUniform1f("u_hittestDist", c9.distance)), t10.setUniform1f("u_pixelRatio", l6), t10.setUniformMatrix3fv("u_tileMat3", v2.transforms.tileMat3), t10.setUniformMatrix3fv("u_viewMat3", p4.viewMat3), t10.setUniformMatrix3fv("u_dvsMat3", v2.transforms.dvs), t10.setUniformMatrix3fv("u_displayViewMat3", p4.displayViewMat3), t10.setUniform1f("u_currentZoom", Math.round(f3 * at)), t10.setUniform1i("u_attributeTextureSize", m9.attributeView.size), t10.setUniform1i("u_attributeData0", B2), t10.setUniform1i("u_attributeData1", C2), t10.setUniform1i("u_attributeData2", D2), t10.setUniform1i("u_attributeData3", E2), t10.setUniform1i("u_attributeData4", F2), t10.setUniform1i("u_attributeData5", G2);
  }
  _setSizeVVUniforms(t10, e7, i2, s7) {
    if (t10.vvSizeMinMaxValue && e7.setUniform4fv("u_vvSizeMinMaxValue", i2.vvSizeMinMaxValue), t10.vvSizeScaleStops && e7.setUniform1f("u_vvSizeScaleStopsValue", i2.vvSizeScaleStopsValue), t10.vvSizeFieldStops) {
      const t11 = i2.getSizeVVFieldStops(s7.key.level);
      null != t11 && (e7.setUniform1fv("u_vvSizeFieldStopsValues", t11.values), e7.setUniform1fv("u_vvSizeFieldStopsSizes", t11.sizes));
    }
    t10.vvSizeUnitValue && e7.setUniform1f("u_vvSizeUnitValueWorldToPixelsRatio", i2.vvSizeUnitValueToPixelsRatio);
  }
  _setColorAndOpacityVVUniforms(t10, e7, i2) {
    t10.vvColor && (e7.setUniform1fv("u_vvColorValues", i2.vvColorValues), e7.setUniform4fv("u_vvColors", i2.vvColors)), t10.vvOpacity && (e7.setUniform1fv("u_vvOpacityValues", i2.vvOpacityValues), e7.setUniform1fv("u_vvOpacities", i2.vvOpacities));
  }
  _setRotationVVUniforms(t10, e7, i2) {
    t10.vvRotation && e7.setUniform1f("u_vvRotationType", "geographic" === i2.vvMaterialParameters.vvRotationType ? 0 : 1);
  }
  _getTriangleDesc(t10, e7, i2 = ["a_pos"]) {
    const s7 = e7.bufferLayouts.geometry, o4 = i2.map((t11) => s7.findIndex((e8) => e8.name === t11)), a2 = `${t10}-${o4.join("-")}`;
    let r10 = this._computeDesc.get(a2);
    if (!r10) {
      const t11 = e7.strides, i3 = e7.strides.geometry, n8 = new Map(e7.attributes), u3 = s7.map((t12) => ({ ...t12 })), m9 = Math.max(...e7.attributes.values()), v2 = { geometry: u3 };
      let f3 = 0;
      for (const e8 of o4) {
        const t12 = s7[e8];
        v2.geometry.push({ count: t12.count, name: t12.name + "1", divisor: t12.divisor, normalized: t12.normalized, offset: i3 + t12.offset, stride: i3, type: t12.type }), v2.geometry.push({ count: t12.count, name: t12.name + "2", divisor: t12.divisor, normalized: t12.normalized, offset: 2 * i3 + t12.offset, stride: i3, type: t12.type }), n8.set(t12.name + "1", m9 + ++f3), n8.set(t12.name + "2", m9 + ++f3);
      }
      r10 = { bufferLayouts: v2, attributes: n8, strides: t11 }, this._computeDesc.set(a2, r10);
    }
    return r10;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushMarker.js
var d2 = { shader: "materials/icon", vertexLayout: { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.SHORT }, { location: 1, name: "a_vertexOffset", count: 2, type: C.SHORT }, { location: 2, name: "a_texCoords", count: 2, type: C.UNSIGNED_SHORT }, { location: 3, name: "a_bitSetAndDistRatio", count: 2, type: C.UNSIGNED_SHORT }, { location: 4, name: "a_id", count: 4, type: C.UNSIGNED_BYTE }, { location: 5, name: "a_color", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 6, name: "a_outlineColor", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 7, name: "a_sizeAndOutlineWidth", count: 4, type: C.UNSIGNED_BYTE }, { location: 8, name: "a_zoomRange", count: 2, type: C.UNSIGNED_SHORT }] }, hittestAttributes: ["a_vertexOffset", "a_texCoords"] };
var u = class extends p2 {
  dispose() {
  }
  getGeometryType() {
    return E3.MARKER;
  }
  supportsSymbology(t10) {
    return t10 !== S.HEATMAP && t10 !== S.PIE_CHART;
  }
  drawGeometry(o4, a2, i2, m9) {
    const { context: u3, painter: c9, rendererInfo: p4, state: y2, passOptions: _3, requestRender: f3, allowDelayedRender: E6 } = o4, S3 = N2.load(i2.materialKey), N3 = p(S3.data), T2 = r(_3) && "hittest" === _3.type, { shader: g2, vertexLayout: x5, hittestAttributes: R2 } = l(N3.programSpec, d2);
    let h5 = E.TRIANGLES, U3 = ee(S3.data, x5);
    T2 && (U3 = this._getTriangleDesc(i2.materialKey, U3, R2), h5 = E.POINTS);
    const { attributes: A3, bufferLayouts: O3 } = U3, G3 = c9.materialManager.getMaterialProgram(o4, S3, g2, A3, m9);
    if (E6 && r(f3) && !G3.compiled) return void f3();
    u3.useProgram(G3), S3.textureBinding && c9.textureManager.bindTextures(u3, G3, S3, true), this._setSharedUniforms(G3, o4, a2);
    const I2 = S3.vvRotation ? y2.displayViewMat3 : y2.displayMat3;
    G3.setUniformMatrix3fv("u_displayMat3", I2), this._setSizeVVUniforms(S3, G3, p4, a2), this._setColorAndOpacityVVUniforms(S3, G3, p4), this._setRotationVVUniforms(S3, G3, p4);
    const M4 = i2.target.getVAO(u3, O3, A3, T2);
    let b2 = i2.indexCount, D3 = i2.indexFrom * Uint32Array.BYTES_PER_ELEMENT;
    T2 && (b2 /= 3, D3 /= 3), u3.bindVAO(M4), this._drawMarkers(o4, a2, G3, h5, b2, D3, T2), u3.bindVAO(null);
  }
  _drawMarkers(t10, e7, o4, a2, r10, i2, n8) {
    t10.context.drawElements(a2, r10, C.UNSIGNED_INT, i2);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/Effect.js
var t8 = class {
  constructor() {
    this.name = this.constructor.name;
  }
  createOptions(t10, r10) {
    return null;
  }
};

// node_modules/@arcgis/core/views/webgl/heatmapTextureUtils.js
function n6(n8, i2) {
  const { textureFloat: f3, colorBufferFloat: s7 } = n8.capabilities, u3 = f3 == null ? void 0 : f3.textureFloat, m9 = f3 == null ? void 0 : f3.textureFloatLinear, _3 = f3 == null ? void 0 : f3.textureHalfFloat, p4 = f3 == null ? void 0 : f3.textureHalfFloatLinear, d5 = f3 == null ? void 0 : f3.HALF_FLOAT, x5 = s7 == null ? void 0 : s7.textureFloat, b2 = s7 == null ? void 0 : s7.textureHalfFloat, c9 = s7 == null ? void 0 : s7.floatBlend, h5 = c(n8.driverTest).floatBufferBlend.result;
  if (!u3 && !_3) throw new s2("heatmap:missing-texture-float", "HeatmapRenderer requires WebGL2 or the WebGL1 extension OES_texture_float or OES_texture_half_float.");
  if (!x5 && !b2) throw new s2("heatmap:missing-color-buffer-float", "HeatmapRenderer requires the WebGL extension EXT_color_buffer_float or EXT_color_buffer_half_float or WEBGL_color_buffer_float.");
  if (!(c9 && h5 || b2)) throw new s2("heatmap:missing-float-blend", "HeatmapRenderer requires the WebGL extension EXT_float_blend or EXT_color_buffer_half_float." + (h5 ? "" : " This device claims support for EXT_float_blend, but does not actually support it."));
  const E6 = u3 && x5 && c9 && h5, F3 = _3 && b2, R2 = m9, L3 = p4, T2 = !!(s7 == null ? void 0 : s7.R32F), w4 = !!(s7 == null ? void 0 : s7.R16F);
  if (E6 && (R2 || !L3)) return R2 || i2.warnOnce("Missing WebGL extension OES_texture_float_linear. Heatmap quality may be reduced."), { dataType: G.FLOAT, samplingMode: R2 ? L.LINEAR : L.NEAREST, pixelFormat: T2 ? P.RED : P.RGBA, internalFormat: T2 ? U.R32F : P.RGBA };
  if (F3) return L3 || i2.warnOnce("Missing WebGL extension OES_texture_half_float_linear. Heatmap quality may be reduced."), { dataType: d5, samplingMode: L3 ? L.LINEAR : L.NEAREST, pixelFormat: w4 ? P.RED : P.RGBA, internalFormat: w4 ? U.R16F : P.RGBA };
  throw new s2("heatmap:missing-hardware-support", "HeatmapRenderer requires WebGL extensions that allow it to render and blend to float or half float textures.");
}

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushHeatmap.js
var S2 = s.getLogger("esri.views.2d.engine.webgl.brushes.WGLBrushHeatmap");
function x2(e7) {
  return "heatmap" === e7.type;
}
var B3 = class extends u {
  constructor() {
    super(...arguments), this.brushEffect = new y();
  }
  supportsSymbology(e7) {
    return e7 === S.HEATMAP;
  }
  dispose() {
    super.dispose(), this.brushEffect.dispose(), this.brushEffect = null;
  }
  prepareState() {
  }
  drawGeometry(e7, t10, r10, i2) {
    const { defines: a2 } = this.brushEffect.loadQualityProfile(e7.context);
    super.drawGeometry(e7, t10, r10, i2 ? [...i2, ...a2] : a2);
  }
  _drawMarkers(e7, t10, r10, i2, a2, s7, u3) {
    const { context: n8, rendererInfo: h5, state: f3 } = e7, { rendererSchema: m9 } = h5;
    s4(m9, "heatmap");
    const { referenceScale: d5, radius: p4, isFieldActive: _3 } = m9, b2 = p4 * (0 !== d5 ? d5 / f3.scale : 1);
    r10.setUniform1f("u_radius", b2), u3 || (r10.setUniform1f("u_isFieldActive", _3), n8.setStencilFunction(I.GEQUAL, t10.stencilRef, 255)), n8.drawElements(i2, a2, C.UNSIGNED_INT, s7);
  }
};
var v = { vsPath: "heatmap/heatmapResolve", fsPath: "heatmap/heatmapResolve", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) };
var y = class extends t8 {
  constructor() {
    super(...arguments), this.name = this.constructor.name;
  }
  createOptions({ passOptions: e7 }) {
    return e7;
  }
  dispose() {
    this._prevFBO = null, this._accumulateOutputTexture = h(this._accumulateOutputTexture), r(this._accumulateFramebuffer) && this._accumulateFramebuffer.detachDepthStencilBuffer(), this._accumulateOutputStencilBuffer = h(this._accumulateOutputStencilBuffer), this._accumulateFramebuffer = h(this._accumulateFramebuffer), this._resolveGradientTexture = h(this._resolveGradientTexture), this._tileQuad = h(this._tileQuad);
  }
  bind(e7) {
    const { context: t10, rendererInfo: i2, passOptions: a2 } = e7, { rendererSchema: s7 } = i2;
    !(r(a2) && "hittest" === a2.type) && x2(s7) && (this._prevFBO = t10.getBoundFramebufferObject(), this._prevViewport = t10.getViewport(), s4(s7, "heatmap"), this._loadResources(e7), this._updateResources(t10, s7), t10.bindFramebuffer(this._accumulateFramebuffer), t10.setViewport(0, 0, this._accumulateFramebuffer.width, this._accumulateFramebuffer.height), t10.setStencilTestEnabled(true), t10.setBlendingEnabled(true), t10.setBlendFunction(R.ONE, R.ONE), t10.setClearColor(0, 0, 0, 0), t10.clear(_.COLOR_BUFFER_BIT));
  }
  unbind() {
    this._prevFBO = null, this._prevViewport = null;
  }
  draw(e7) {
    const { context: t10, painter: i2, rendererInfo: a2, passOptions: s7 } = e7, { rendererSchema: u3 } = a2;
    if (r(s7) && "hittest" === s7.type || !x2(u3)) return;
    const { defines: n8 } = this.loadQualityProfile(t10), o4 = i2.materialManager.getProgram(v, n8);
    t10.useProgram(o4), t10.bindFramebuffer(this._prevFBO), t10.setViewport(0, 0, this._prevViewport.width, this._prevViewport.height), t10.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), t10.setStencilTestEnabled(false);
    const { radius: h5, minDensity: c9, densityRange: l6 } = u3;
    t10.bindTexture(this._accumulateOutputTexture, 8), t10.bindTexture(this._resolveGradientTexture, 9), o4.setUniform1i("u_texture", 8), o4.setUniform1i("u_gradient", 9), o4.setUniform2f("u_densityMinAndInvRange", c9, 1 / l6), o4.setUniform1f("u_densityNormalization", 3 / (h5 * h5 * Math.PI)), this._tileQuad.draw();
  }
  _loadResources({ context: e7, painter: t10 }) {
    const { dataType: r10, samplingMode: i2, pixelFormat: a2, internalFormat: u3, shadingRate: n8, requiresSharedStencilBuffer: o4 } = this.loadQualityProfile(e7), { width: h5, height: c9 } = this._prevViewport, l6 = h5 * n8, f3 = c9 * n8;
    this._accumulateOutputTexture ?? (this._accumulateOutputTexture = new E4(e7, { target: M.TEXTURE_2D, pixelFormat: a2, internalFormat: u3, dataType: r10, samplingMode: i2, wrapMode: D.CLAMP_TO_EDGE, width: l6, height: f3 })), o4 || (this._accumulateOutputStencilBuffer ?? (this._accumulateOutputStencilBuffer = new s3(e7, { width: l6, height: f3, internalFormat: B.DEPTH_STENCIL }))), this._accumulateFramebuffer ?? (this._accumulateFramebuffer = new x(e7, {}, this._accumulateOutputTexture, o4 ? t10.getSharedStencilBuffer() : this._accumulateOutputStencilBuffer)), this._resolveGradientTexture ?? (this._resolveGradientTexture = new E4(e7, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, samplingMode: L.LINEAR, wrapMode: D.CLAMP_TO_EDGE })), this._tileQuad ?? (this._tileQuad = new n4(e7, [0, 0, 1, 0, 0, 1, 1, 1]));
  }
  _updateResources(e7, t10) {
    const { gradientHash: i2, gradient: a2 } = t10;
    this._prevGradientHash !== i2 && (this._resolveGradientTexture.resize(a2.length / 4, 1), this._resolveGradientTexture.setData(a2), this._prevGradientHash = i2);
    const { shadingRate: s7, requiresSharedStencilBuffer: u3 } = this.loadQualityProfile(e7), { width: n8, height: o4 } = this._prevViewport, h5 = n8 * s7, c9 = o4 * s7, { width: l6, height: f3 } = this._accumulateFramebuffer;
    if (l6 !== h5 || f3 !== c9) {
      const e8 = this._accumulateFramebuffer.depthStencilAttachment;
      if (u3 && r(e8)) {
        const { width: t11, height: r10 } = e8.descriptor;
        t11 === h5 && r10 === c9 || (S2.errorOnce("Attempted to resize shared stencil buffer! Detaching instead."), this._accumulateFramebuffer.detachDepthStencilBuffer());
      }
      this._accumulateFramebuffer.resize(h5, c9);
    }
    u3 || e7.blitFramebuffer(this._prevFBO, this._accumulateFramebuffer, 0, 0, this._prevFBO.width, this._prevFBO.height, 0, 0, this._accumulateFramebuffer.width, this._accumulateFramebuffer.height, _.STENCIL_BUFFER_BIT, L.NEAREST);
  }
  loadQualityProfile(e7) {
    if (t(this._qualityProfile)) {
      const t10 = n6(e7, S2), r10 = e7.type === r4.WEBGL1;
      this._qualityProfile = { ...t10, requiresSharedStencilBuffer: r10, shadingRate: r10 ? 1 : 0.25, defines: t10.dataType !== G.FLOAT ? ["heatmapPrecisionHalfFloat"] : [] };
    }
    return this._qualityProfile;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/DefaultVertexAttributeLayouts.js
var r8 = { geometry: [new t4("a_pos", 2, C.BYTE, 0, 2)] };
var t9 = { geometry: [new t4("a_pos", 2, C.BYTE, 0, 4), new t4("a_tex", 2, C.BYTE, 2, 4)] };
var m2 = { geometry: [new t4("a_pos", 2, C.UNSIGNED_SHORT, 0, 4)] };

// node_modules/@arcgis/core/views/2d/engine/webgl/shaders/TileInfoPrograms.js
var r9 = { shaders: { vertexShader: n5("tileInfo/tileInfo.vert"), fragmentShader: n5("tileInfo/tileInfo.frag") }, attributes: /* @__PURE__ */ new Map([["a_pos", 0]]) };

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/BrushClip.js
var c3 = () => ee("clip", { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.SHORT }] });
var d3 = class extends t6 {
  constructor() {
    super(...arguments), this._color = r7(0, 1, 0, 1);
  }
  dispose() {
    this._program && this._program.dispose();
  }
  prepareState({ context: r10 }) {
    r10.setStencilTestEnabled(true), r10.setBlendingEnabled(false), r10.setFaceCullingEnabled(false), r10.setColorMask(false, false, false, false), r10.setStencilOp(O.KEEP, O.KEEP, O.REPLACE), r10.setStencilWriteMask(255), r10.setStencilFunction(I.ALWAYS, 0, 255);
  }
  draw(t10, s7) {
    const { context: o4, state: i2, requestRender: m9, allowDelayedRender: d5 } = t10, f3 = c3(), g2 = s7.getVAO(o4, i2, f3.attributes, f3.bufferLayouts);
    t(g2.indexBuffer) || (this._program || (this._program = e4(o4, e6)), d5 && r(m9) && !this._program.compiled ? m9() : (o4.useProgram(this._program), this._program.setUniform2fv("u_coord_range", [1, 1]), this._program.setUniform4fv("u_color", this._color), this._program.setUniformMatrix3fv("u_dvsMat3", i2.displayMat3), o4.bindVAO(g2), o4.drawElements(E.TRIANGLES, g2.indexBuffer.size, C.UNSIGNED_INT, 0), o4.bindVAO()));
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/BrushOverlay.js
var c4 = () => ee("overlay", { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.FLOAT }], tex: [{ location: 1, name: "a_uv", count: 2, type: C.UNSIGNED_SHORT }] });
var m3 = class extends t6 {
  constructor() {
    super(...arguments), this._desc = { vsPath: "overlay/overlay", fsPath: "overlay/overlay", attributes: /* @__PURE__ */ new Map([["a_pos", 0], ["a_uv", 1]]) };
  }
  dispose() {
  }
  prepareState({ context: e7 }) {
    e7.setBlendingEnabled(true), e7.setColorMask(true, true, true, true), e7.setBlendFunctionSeparate(R.ONE, R.ONE_MINUS_SRC_ALPHA, R.ONE, R.ONE_MINUS_SRC_ALPHA), e7.setStencilWriteMask(0), e7.setStencilTestEnabled(true), e7.setStencilFunction(I.GREATER, 255, 255);
  }
  draw(r10, s7) {
    const { context: o4, painter: n8, requestRender: i2, allowDelayedRender: m9 } = r10;
    if (!s7.isReady) return;
    const { computedOpacity: u3, dvsMat3: d5, isWrapAround: l6, perspectiveTransform: p4, texture: f3 } = s7;
    r10.timeline.begin(this.name);
    const _3 = n8.materialManager.getProgram(this._desc);
    if (m9 && r(i2) && !_3.compiled) return void i2();
    const v2 = c4(), y2 = s7.getVAO(o4, v2.bufferLayouts, v2.attributes);
    if (!y2) return;
    o4.bindVAO(y2), o4.useProgram(_3), o4.bindTexture(f3, A), _3.setUniformMatrix3fv("u_dvsMat3", d5), _3.setUniform1i("u_texture", A), _3.setUniform1f("u_opacity", u3), _3.setUniform2fv("u_perspective", p4);
    const A3 = l6 ? 10 : 4;
    o4.drawArrays(E.TRIANGLE_STRIP, 0, A3), o4.bindVAO(), r10.timeline.end(this.name);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushFill.js
function u2(e7) {
  const t10 = { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.SHORT }, { location: 1, name: "a_id", count: 3, type: C.UNSIGNED_BYTE }, { location: 2, name: "a_bitset", count: 1, type: C.UNSIGNED_BYTE }, { location: 3, name: "a_color", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 4, name: "a_aux1", count: 4, type: C.UNSIGNED_SHORT }, { location: 5, name: "a_aux2", count: 4, type: C.SHORT }, { location: 6, name: "a_aux3", count: 4, type: C.UNSIGNED_BYTE }, { location: 7, name: "a_zoomRange", count: 2, type: C.UNSIGNED_SHORT }] };
  switch (e7.symbologyType) {
    case S.SIMPLE:
    case S.OUTLINE_FILL_SIMPLE:
      t10.geometry.splice(7, 1), t10.geometry.splice(4, 1);
  }
  return { shader: "materials/fill", vertexLayout: t10 };
}
var c5 = class extends p2 {
  dispose() {
  }
  getGeometryType() {
    return E3.FILL;
  }
  supportsSymbology(e7) {
    return e7 !== S.DOT_DENSITY;
  }
  drawGeometry(o4, r10, i2, l6) {
    const { context: c9, painter: y2, rendererInfo: p4, requiredLevel: _3, passOptions: d5, requestRender: f3, allowDelayedRender: E6 } = o4, S3 = w2.load(i2.materialKey), g2 = p(S3.data), N3 = r(d5) && "hittest" === d5.type, T2 = y2.materialManager, { shader: I2, vertexLayout: U3, hittestAttributes: x5 } = l(g2.programSpec, u2(S3));
    let L3 = E.TRIANGLES, h5 = ee(S3.data, U3);
    N3 && (h5 = this._getTriangleDesc(i2.materialKey, h5, x5), L3 = E.POINTS);
    const { attributes: G3, bufferLayouts: O3 } = h5, b2 = T2.getMaterialProgram(o4, S3, I2, G3, l6);
    if (E6 && r(f3) && !b2.compiled) return void f3();
    if (c9.useProgram(b2), this._setSharedUniforms(b2, o4, r10), b2.setUniform2f("u_tileOffset", 512 * r10.key.col, 512 * r10.key.row), S3.textureBinding) {
      y2.textureManager.bindTextures(c9, b2, S3);
      const e7 = 1 / 2 ** (_3 - r10.key.level);
      b2.setUniform1f("u_zoomFactor", e7);
    }
    const D3 = 1 / o4.pixelRatio;
    b2.setUniform1f("u_blur", D3), b2.setUniform1f("u_antialiasing", D3), this._setSizeVVUniforms(S3, b2, p4, r10), this._setColorAndOpacityVVUniforms(S3, b2, p4);
    const R2 = i2.target.getVAO(c9, O3, G3, N3);
    let w4 = i2.indexCount, j = i2.indexFrom * Uint32Array.BYTES_PER_ELEMENT;
    N3 && (w4 /= 3, j /= 3), c9.bindVAO(R2), this._drawFills(o4, r10, b2, L3, w4, j);
  }
  _drawFills(e7, t10, o4, r10, a2, i2) {
    e7.context.drawElements(r10, a2, C.UNSIGNED_INT, i2);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushDotDensity.js
var O2 = class extends c5 {
  constructor() {
    super(...arguments), this._dotTextureSize = 0, this._dotTextures = null, this._dotSamplers = new Int32Array([K, L2]), this._dotVAO = null, this._dotDesc = { vsPath: "dot/dot", fsPath: "dot/dot", attributes: /* @__PURE__ */ new Map([["a_pos", 0]]) };
  }
  dispose() {
    super.dispose(), this._disposeTextures(), this._dotFBO = h(this._dotFBO), this._dotVAO = h(this._dotVAO);
  }
  getGeometryType() {
    return E3.FILL;
  }
  supportsSymbology(e7) {
    return e7 === S.DOT_DENSITY;
  }
  _drawFills(e7, o4, r10, s7, i2, n8) {
    const { passOptions: d5 } = e7;
    if (r(d5) && "hittest" === d5.type) super._drawFills(e7, o4, r10, s7, i2, n8);
    else {
      const t10 = this._drawDotLocations(e7, o4, r10, i2, n8);
      this._drawDotDensity(e7, o4, t10);
    }
  }
  _drawDotDensity(e7, o4, r10) {
    const { context: s7, painter: i2, rendererInfo: n8, requestRender: d5, allowDelayedRender: a2 } = e7, l6 = i2.materialManager.getProgram(this._dotDesc);
    if (a2 && r(d5) && !l6.compiled) return void d5();
    const { rendererSchema: u3 } = n8;
    s4(u3, "dot-density");
    const c9 = this._createDotDensityMesh(s7, this._dotDesc.attributes, { geometry: [{ name: "a_pos", count: 2, type: C.SHORT, divisor: 0, normalized: false, offset: 0, stride: 4 }] });
    s7.setStencilTestEnabled(true), s7.useProgram(l6), l6.setUniform1f("u_tileZoomFactor", 1), l6.setUniform1i("u_texture", this._dotSamplers[0]), l6.setUniform1f("u_dotSize", Math.max(u3.dotSize, 1)), l6.setUniform1f("u_pixelRatio", window.devicePixelRatio), this._setSharedUniforms(l6, e7, o4), s7.bindTexture(r10, this._dotSamplers[0]), s7.bindVAO(c9), s7.drawArrays(E.POINTS, 0, 262144);
  }
  _drawDotLocations(e7, t10, o4, r10, s7) {
    const { context: i2, rendererInfo: d5, requiredLevel: a2 } = e7, l6 = i2.getViewport(), { rendererSchema: u3 } = d5;
    s4(u3, "dot-density");
    const { dotScale: c9, colors: f3, activeDots: T2, backgroundColor: p4, dotValue: x5 } = u3;
    i2.setViewport(0, 0, 512, 512);
    const w4 = i2.getBoundFramebufferObject(), D3 = this._createFBO(i2);
    i2.bindFramebuffer(D3), i2.setClearColor(0, 0, 0, 0), i2.clear(i2.gl.COLOR_BUFFER_BIT | i2.gl.STENCIL_BUFFER_BIT), i2.setStencilTestEnabled(false);
    const y2 = 1 / 2 ** (a2 - t10.key.level), g2 = o, S3 = g2 * window.devicePixelRatio * g2 * window.devicePixelRatio, b2 = 1 / y2 * (1 / y2), E6 = c9 ? e7.state.scale / c9 : 1;
    return o4.setUniform1f("u_tileZoomFactor", y2), o4.setUniform1f("u_tileDotsOverArea", S3 / (o * window.devicePixelRatio * o * window.devicePixelRatio)), o4.setUniformMatrix4fv("u_dotColors", f3), o4.setUniform4fv("u_isActive", T2), o4.setUniform4fv("u_dotBackgroundColor", p4), o4.setUniform1f("u_dotValue", Math.max(1, x5 * E6 * b2)), this._bindDotDensityTextures(i2, o4, d5, g2), i2.drawElements(E.TRIANGLES, r10, C.UNSIGNED_INT, s7), i2.setViewport(l6.x, l6.y, l6.width, l6.height), i2.bindFramebuffer(w4), D3.colorTexture;
  }
  _createFBO(e7) {
    if (t(this._dotFBO)) {
      const t10 = 512, o4 = 512, r10 = { target: M.TEXTURE_2D, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, samplingMode: L.NEAREST, wrapMode: D.CLAMP_TO_EDGE, width: t10, height: o4 }, s7 = { colorTarget: Y.TEXTURE, depthStencilTarget: V.DEPTH_STENCIL_RENDER_BUFFER }, i2 = new s3(e7, { width: t10, height: o4, internalFormat: B.DEPTH_STENCIL });
      this._dotFBO = new x(e7, s7, r10, i2);
    }
    return this._dotFBO;
  }
  _disposeTextures() {
    if (this._dotTextures) {
      for (let e7 = 0; e7 < this._dotTextures.length; e7++) this._dotTextures[e7].dispose();
      this._dotTextures = null;
    }
  }
  _bindDotDensityTextures(e7, t10, o4, r10) {
    const { rendererSchema: s7 } = o4;
    s4(s7, "dot-density");
    const i2 = this._createDotDensityTextures(e7, r10, s7.seed);
    t10.setUniform1iv("u_dotTextures", this._dotSamplers);
    for (let n8 = 0; n8 < i2.length; n8++) e7.bindTexture(i2[n8], this._dotSamplers[n8]);
  }
  _createDotDensityMesh(e7, t10, r10) {
    if (t(this._dotVAO)) {
      const o4 = 2, s7 = new Int16Array(262144 * o4);
      for (let e8 = 0; e8 < 512; e8++) for (let t11 = 0; t11 < 512; t11++) s7[o4 * (t11 + 512 * e8)] = t11, s7[o4 * (t11 + 512 * e8) + 1] = e8;
      const i2 = E5.createVertex(e7, F.STATIC_DRAW, s7);
      this._dotVAO = new f2(e7, t10, r10, { geometry: i2 }, null);
    }
    return this._dotVAO;
  }
  _createDotDensityTextures(e7, t10, o4) {
    if (this._dotTextureSize === t10 && this._seed === o4 || (this._disposeTextures(), this._dotTextureSize = t10, this._seed = o4), null === this._dotTextures) {
      const s7 = new t2(o4);
      this._dotTextures = [this._allocDotDensityTexture(e7, t10, s7), this._allocDotDensityTexture(e7, t10, s7)];
    }
    return this._dotTextures;
  }
  _allocDotDensityTexture(e7, t10, o4) {
    const r10 = new Float32Array(t10 * t10 * 4);
    for (let s7 = 0; s7 < r10.length; s7++) r10[s7] = o4.getFloat();
    return new E4(e7, { wrapMode: D.REPEAT, pixelFormat: P.RGBA, dataType: G.FLOAT, samplingMode: L.NEAREST, width: t10, height: t10 }, r10);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushInfo.js
var A2 = 300;
var b = 32;
var x3 = class extends t6 {
  constructor() {
    super(...arguments), this._color = r7(1, 0, 0, 1);
  }
  dispose() {
    var _a, _b, _c, _d;
    (_a = this._outlineProgram) == null ? void 0 : _a.dispose(), this._outlineProgram = null, (_b = this._tileInfoProgram) == null ? void 0 : _b.dispose(), this._tileInfoProgram = null, (_c = this._outlineVertexArrayObject) == null ? void 0 : _c.dispose(), this._outlineVertexArrayObject = null, (_d = this._tileInfoVertexArrayObject) == null ? void 0 : _d.dispose(), this._tileInfoVertexArrayObject = null, this._canvas = null;
  }
  prepareState({ context: t10 }) {
    t10.setBlendingEnabled(true), t10.setBlendFunctionSeparate(R.ONE, R.ONE_MINUS_SRC_ALPHA, R.ONE, R.ONE_MINUS_SRC_ALPHA), t10.setColorMask(true, true, true, true), t10.setStencilWriteMask(0), t10.setStencilTestEnabled(false);
  }
  draw(e7, r10) {
    const { context: i2, requestRender: o4, allowDelayedRender: s7 } = e7;
    if (!r10.isReady) return;
    if (this._loadWGLResources(i2), s7 && r(o4) && (!this._outlineProgram.compiled || !this._tileInfoProgram.compiled)) return void o4();
    i2.bindVAO(this._outlineVertexArrayObject), i2.useProgram(this._outlineProgram), this._outlineProgram.setUniformMatrix3fv("u_dvsMat3", r10.transforms.dvs), this._outlineProgram.setUniform2f("u_coord_range", r10.rangeX, r10.rangeY), this._outlineProgram.setUniform1f("u_depth", 0), this._outlineProgram.setUniform4fv("u_color", this._color), i2.drawArrays(E.LINE_STRIP, 0, 4);
    const n8 = this._getTexture(i2, r10);
    n8 ? (i2.bindVAO(this._tileInfoVertexArrayObject), i2.useProgram(this._tileInfoProgram), i2.bindTexture(n8, 0), this._tileInfoProgram.setUniformMatrix3fv("u_dvsMat3", r10.transforms.dvs), this._tileInfoProgram.setUniform1f("u_depth", 0), this._tileInfoProgram.setUniform2f("u_coord_ratio", r10.rangeX / r10.width, r10.rangeY / r10.height), this._tileInfoProgram.setUniform2f("u_delta", 8, 8), this._tileInfoProgram.setUniform2f("u_dimensions", n8.descriptor.width, n8.descriptor.height), i2.drawArrays(E.TRIANGLE_STRIP, 0, 4), i2.bindVAO()) : i2.bindVAO();
  }
  _loadWGLResources(t10) {
    if (this._outlineProgram && this._tileInfoProgram) return;
    const e7 = e4(t10, e6), i2 = e4(t10, r9), a2 = new Int8Array([0, 0, 1, 0, 1, 1, 0, 1]), l6 = E5.createVertex(t10, F.STATIC_DRAW, a2), m9 = new f2(t10, e6.attributes, r8, { geometry: l6 }), _3 = new Int8Array([0, 0, 1, 0, 0, 1, 1, 1]), f3 = E5.createVertex(t10, F.STATIC_DRAW, _3), c9 = new f2(t10, r9.attributes, r8, { geometry: f3 });
    this._outlineProgram = e7, this._tileInfoProgram = i2, this._outlineVertexArrayObject = m9, this._tileInfoVertexArrayObject = c9;
  }
  _getTexture(t10, e7) {
    if (e7.texture && e7.triangleCountReportedInDebug === e7.triangleCount) return e7.texture;
    e7.triangleCountReportedInDebug = e7.triangleCount, this._canvas || (this._canvas = document.createElement("canvas"), this._canvas.setAttribute("id", "canvas2d"), this._canvas.setAttribute("width", `${A2}`), this._canvas.setAttribute("height", `${b}`), this._canvas.setAttribute("style", "display:none"));
    const r10 = e7.triangleCount;
    let i2 = e7.key.id;
    e7.triangleCount > 0 && (i2 += `, ${r10}`);
    const o4 = this._canvas, s7 = o4.getContext("2d");
    return s7.font = "24px sans-serif", s7.textAlign = "left", s7.textBaseline = "top", s7.clearRect(0, 0, A2, b), r10 > 1e5 ? (s7.fillStyle = "red", s7.fillRect(0, 0, A2, b), s7.fillStyle = "black") : (s7.clearRect(0, 0, A2, b), s7.fillStyle = "blue"), s7.fillText(i2, 0, 0), e7.texture = new E4(t10, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, samplingMode: L.NEAREST, wrapMode: D.CLAMP_TO_EDGE }, o4), e7.texture;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushPieChart.js
var s5 = class extends u {
  supportsSymbology(r10) {
    return r10 === S.PIE_CHART;
  }
  _drawMarkers(o4, r10, s7, n8, l6, f3, u3) {
    const { context: i2 } = o4, { rendererInfo: m9 } = o4, { rendererSchema: a2 } = m9;
    s4(a2, "pie-chart"), s7.setUniform4fv("u_colors", a2.colors), s7.setUniform4fv("u_defaultColor", a2.defaultColor), s7.setUniform4fv("u_othersColor", a2.othersColor), s7.setUniform4fv("u_outlineColor", a2.outlineColor), s7.setUniform1f("u_donutRatio", a2.holePercentage), s7.setUniform1f("u_sectorThreshold", a2.sectorThreshold), s7.setUniform1f("u_outlineWidth", a2.outlineWidth), i2.drawElements(n8, l6, C.UNSIGNED_INT, f3);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushStencil.js
var h3 = class extends t6 {
  constructor() {
    super(...arguments), this._color = r7(1, 0, 0, 1), this._initialized = false;
  }
  dispose() {
    this._solidProgram && (this._solidProgram.dispose(), this._solidProgram = null), this._solidVertexArrayObject && (this._solidVertexArrayObject.dispose(), this._solidVertexArrayObject = null);
  }
  prepareState({ context: r10 }) {
    r10.setDepthWriteEnabled(false), r10.setDepthTestEnabled(false), r10.setStencilTestEnabled(true), r10.setBlendingEnabled(false), r10.setColorMask(false, false, false, false), r10.setStencilOp(O.KEEP, O.KEEP, O.REPLACE), r10.setStencilWriteMask(255);
  }
  draw(e7, t10) {
    const { context: s7, requestRender: i2, allowDelayedRender: o4 } = e7;
    this._initialized || this._initialize(s7), o4 && r(i2) && !this._solidProgram.compiled ? i2() : (s7.setStencilFunctionSeparate(N.FRONT_AND_BACK, I.GREATER, t10.stencilRef, 255), s7.bindVAO(this._solidVertexArrayObject), s7.useProgram(this._solidProgram), this._solidProgram.setUniformMatrix3fv("u_dvsMat3", t10.transforms.dvs), this._solidProgram.setUniform2fv("u_coord_range", [t10.rangeX, t10.rangeY]), this._solidProgram.setUniform1f("u_depth", 0), this._solidProgram.setUniform4fv("u_color", this._color), s7.drawArrays(E.TRIANGLE_STRIP, 0, 4), s7.bindVAO());
  }
  _initialize(r10) {
    if (this._initialized) return true;
    const e7 = e4(r10, e6);
    if (!e7) return false;
    const s7 = new Int8Array([0, 0, 1, 0, 0, 1, 1, 1]), a2 = E5.createVertex(r10, F.STATIC_DRAW, s7), l6 = new f2(r10, e6.attributes, r8, { geometry: a2 });
    return this._solidProgram = e7, this._solidVertexArrayObject = l6, this._initialized = true, true;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLBackground.js
var d4 = class extends t6 {
  constructor() {
    super(...arguments), this._color = r7(1, 0, 0, 1), this._patternMatrix = e(), this._programOptions = { id: false, pattern: false };
  }
  dispose() {
    this._vao && (this._vao.dispose(), this._vao = null);
  }
  drawMany(e7, o4) {
    const { context: c9, painter: m9, styleLayerUID: p4, requestRender: _3, allowDelayedRender: d5 } = e7;
    this._loadWGLResources(e7);
    const h5 = e7.displayLevel, g2 = e7.styleLayer, v2 = g2.backgroundMaterial, y2 = m9.vectorTilesMaterialManager, b2 = g2.getPaintValue("background-color", h5), x5 = g2.getPaintValue("background-opacity", h5), M4 = g2.getPaintValue("background-pattern", h5), j = void 0 !== M4, U3 = b2[3] * x5, w4 = 1 | window.devicePixelRatio, L3 = e7.spriteMosaic;
    let A3, P3;
    const I2 = w4 > _2 ? 2 : 1, R2 = e7.drawPhase === T.HITTEST, k = this._programOptions;
    k.id = R2, k.pattern = j;
    const T2 = y2.getMaterialProgram(c9, v2, k);
    if (d5 && r(_3) && !T2.compiled) _3();
    else {
      if (c9.bindVAO(this._vao), c9.useProgram(T2), j) {
        const t10 = L3.getMosaicItemPosition(M4, true);
        if (r(t10)) {
          const { tl: e8, br: o5, page: s7 } = t10;
          A3 = o5[0] - e8[0], P3 = o5[1] - e8[1];
          const a2 = L3.getPageSize(s7);
          r(a2) && (L3.bind(c9, L.LINEAR, s7, Z), T2.setUniform4f("u_tlbr", e8[0], e8[1], o5[0], o5[1]), T2.setUniform2fv("u_mosaicSize", a2), T2.setUniform1i("u_texture", Z));
        }
        T2.setUniform1f("u_opacity", x5);
      } else this._color[0] = U3 * b2[0], this._color[1] = U3 * b2[1], this._color[2] = U3 * b2[2], this._color[3] = U3, T2.setUniform4fv("u_color", this._color);
      if (T2.setUniform1f("u_depth", g2.z || 0), R2) {
        const t10 = M2(p4 + 1);
        T2.setUniform4fv("u_id", t10);
      }
      for (const r10 of o4) {
        if (T2.setUniform1f("u_coord_range", r10.rangeX), T2.setUniformMatrix3fv("u_dvsMat3", r10.transforms.dvs), j) {
          const e8 = Math.max(2 ** (Math.round(h5) - r10.key.level), 1), o5 = I2 * r10.width * e8, i2 = o5 / f(A3), s7 = o5 / f(P3);
          this._patternMatrix[0] = i2, this._patternMatrix[4] = s7, T2.setUniformMatrix3fv("u_pattern_matrix", this._patternMatrix);
        }
        c9.setStencilFunction(I.EQUAL, 0, 255), c9.drawArrays(E.TRIANGLE_STRIP, 0, 4);
      }
    }
  }
  _loadWGLResources(t10) {
    if (this._vao) return;
    const { context: r10, styleLayer: e7 } = t10, o4 = e7.backgroundMaterial, i2 = new Int8Array([0, 0, 1, 0, 0, 1, 1, 1]), s7 = E5.createVertex(r10, F.STATIC_DRAW, i2), a2 = new f2(r10, o4.getAttributeLocations(), o4.getLayoutInfo(), { geometry: s7 });
    this._vao = a2;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLCircle.js
var c6 = class extends t6 {
  constructor() {
    super(...arguments), this._programOptions = { id: false };
  }
  dispose() {
  }
  drawMany(n8, c9) {
    const { context: m9, displayLevel: d5, requiredLevel: f3, state: u3, drawPhase: p4, painter: y2, spriteMosaic: g2, styleLayerUID: v2, requestRender: E6, allowDelayedRender: M4 } = n8;
    if (!c9.some((e7) => {
      var _a;
      return ((_a = e7.layerData.get(v2)) == null ? void 0 : _a.circleIndexCount) ?? false;
    })) return;
    const T2 = n8.styleLayer, x5 = T2.circleMaterial, I2 = y2.vectorTilesMaterialManager, U3 = 1.2, _3 = T2.getPaintValue("circle-translate", d5), R2 = T2.getPaintValue("circle-translate-anchor", d5), h5 = p4 === T.HITTEST, D3 = this._programOptions;
    D3.id = h5;
    const L3 = I2.getMaterialProgram(m9, x5, D3);
    if (M4 && r(E6) && !L3.compiled) return void E6();
    m9.useProgram(L3), L3.setUniformMatrix3fv("u_displayMat3", R2 === r5.VIEWPORT ? u3.displayMat3 : u3.displayViewMat3), L3.setUniform2fv("u_circleTranslation", _3), L3.setUniform1f("u_depth", T2.z), L3.setUniform1f("u_antialiasingWidth", U3);
    let S3 = -1;
    if (h5) {
      const e7 = M2(v2 + 1);
      L3.setUniform4fv("u_id", e7);
    }
    for (const e7 of c9) {
      if (!e7.layerData.has(v2)) continue;
      e7.key.level !== S3 && (S3 = e7.key.level, x5.setDataUniforms(L3, d5, T2, S3, g2));
      const r10 = e7.layerData.get(v2);
      if (!r10.circleIndexCount) continue;
      r10.prepareForRendering(m9);
      const i2 = r10.circleVertexArrayObject;
      t(i2) || (m9.bindVAO(i2), L3.setUniformMatrix3fv("u_dvsMat3", e7.transforms.dvs), f3 !== e7.key.level ? m9.setStencilFunction(I.EQUAL, e7.stencilRef, 255) : m9.setStencilFunction(I.GREATER, 255, 255), m9.drawElements(E.TRIANGLES, r10.circleIndexCount, C.UNSIGNED_INT, Uint32Array.BYTES_PER_ELEMENT * r10.circleIndexStart), e7.triangleCount += r10.circleIndexCount / 3);
    }
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLFill.js
var c7 = 1 / 65536;
var m4 = class extends t6 {
  constructor() {
    super(...arguments), this._fillProgramOptions = { id: false, pattern: false }, this._outlineProgramOptions = { id: false };
  }
  dispose() {
  }
  drawMany(t10, e7) {
    const { displayLevel: i2, drawPhase: r10, renderPass: a2, spriteMosaic: l6, styleLayerUID: s7 } = t10;
    let f3 = false;
    for (const n8 of e7) if (n8.layerData.has(s7)) {
      const t11 = n8.layerData.get(s7);
      if (t11.fillIndexCount > 0 || t11.outlineIndexCount > 0) {
        f3 = true;
        break;
      }
    }
    if (!f3) return;
    const u3 = t10.styleLayer, d5 = u3.getPaintProperty("fill-pattern"), c9 = void 0 !== d5, m9 = c9 && d5.isDataDriven;
    let p4;
    if (c9 && !m9) {
      const t11 = d5.getValue(i2);
      p4 = l6.getMosaicItemPosition(t11, true);
    }
    const y2 = !c9 && u3.getPaintValue("fill-antialias", i2);
    let g2 = true, _3 = 1;
    if (!c9) {
      const t11 = u3.getPaintProperty("fill-color"), e8 = u3.getPaintProperty("fill-opacity");
      if (!(t11 == null ? void 0 : t11.isDataDriven) && !(e8 == null ? void 0 : e8.isDataDriven)) {
        const t12 = u3.getPaintValue("fill-color", i2);
        _3 = u3.getPaintValue("fill-opacity", i2) * t12[3], _3 >= 1 && (g2 = false);
      }
    }
    if (g2 && "opaque" === a2) return;
    let E6;
    r10 === T.HITTEST && (E6 = M2(s7 + 1));
    const v2 = u3.getPaintValue("fill-translate", i2), M4 = u3.getPaintValue("fill-translate-anchor", i2);
    (g2 || "translucent" !== a2) && this._drawFill(t10, s7, u3, e7, v2, M4, c9, p4, m9, E6);
    const P3 = !u3.hasDataDrivenOutlineColor && u3.outlineUsesFillColor && _3 < 1;
    y2 && "opaque" !== a2 && !P3 && this._drawOutline(t10, s7, u3, e7, v2, M4, E6);
  }
  _drawFill(o4, l6, m9, p4, y2, g2, _3, E6, v2, M4) {
    if (_3 && !v2 && t(E6)) return;
    const { context: P3, displayLevel: I2, state: T2, drawPhase: U3, painter: x5, pixelRatio: D3, spriteMosaic: h5, requestRender: S3, allowDelayedRender: R2 } = o4, w4 = m9.fillMaterial, N3 = x5.vectorTilesMaterialManager, L3 = D3 > _2 ? 2 : 1, A3 = U3 === T.HITTEST, V2 = this._fillProgramOptions;
    V2.id = A3, V2.pattern = _3;
    const O3 = N3.getMaterialProgram(P3, w4, V2);
    if (R2 && r(S3) && !O3.compiled) return void S3();
    if (P3.useProgram(O3), r(E6)) {
      const { page: t10 } = E6, i2 = h5.getPageSize(t10);
      r(i2) && (h5.bind(P3, L.LINEAR, t10, Z), O3.setUniform2fv("u_mosaicSize", i2), O3.setUniform1i("u_texture", Z));
    }
    O3.setUniformMatrix3fv("u_displayMat3", g2 === r5.VIEWPORT ? T2.displayMat3 : T2.displayViewMat3), O3.setUniform2fv("u_fillTranslation", y2), O3.setUniform1f("u_depth", m9.z + c7), A3 && O3.setUniform4fv("u_id", M4);
    let C4 = -1;
    for (const i2 of p4) {
      if (!i2.layerData.has(l6)) continue;
      i2.key.level !== C4 && (C4 = i2.key.level, w4.setDataUniforms(O3, I2, m9, C4, h5));
      const a2 = i2.layerData.get(l6);
      if (!a2.fillIndexCount) continue;
      a2.prepareForRendering(P3);
      const n8 = a2.fillVertexArrayObject;
      if (!t(n8)) {
        if (P3.bindVAO(n8), O3.setUniformMatrix3fv("u_dvsMat3", i2.transforms.dvs), P3.setStencilFunction(I.EQUAL, i2.stencilRef, 255), _3) {
          const t10 = Math.max(2 ** (Math.round(I2) - i2.key.level), 1), e7 = i2.rangeX / (L3 * i2.width * t10);
          O3.setUniform1f("u_patternFactor", e7);
        }
        if (v2) {
          const t10 = a2.patternMap;
          if (!t10) continue;
          for (const [i3, a3] of t10) {
            const t11 = h5.getPageSize(i3);
            r(t11) && (h5.bind(P3, L.LINEAR, i3, Z), O3.setUniform2fv("u_mosaicSize", t11), O3.setUniform1i("u_texture", Z), P3.drawElements(E.TRIANGLES, a3[1], C.UNSIGNED_INT, Uint32Array.BYTES_PER_ELEMENT * a3[0]));
          }
        } else P3.drawElements(E.TRIANGLES, a2.fillIndexCount, C.UNSIGNED_INT, Uint32Array.BYTES_PER_ELEMENT * a2.fillIndexStart);
        i2.triangleCount += a2.fillIndexCount / 3;
      }
    }
  }
  _drawOutline(r10, a2, o4, l6, s7, m9, p4) {
    const { context: y2, displayLevel: g2, state: _3, drawPhase: E6, painter: v2, pixelRatio: M4, spriteMosaic: P3, requestRender: I2, allowDelayedRender: T2 } = r10, U3 = o4.outlineMaterial, x5 = v2.vectorTilesMaterialManager, D3 = 0.75 / M4, h5 = E6 === T.HITTEST, S3 = this._outlineProgramOptions;
    S3.id = h5;
    const R2 = x5.getMaterialProgram(y2, U3, S3);
    if (T2 && r(I2) && !R2.compiled) return void I2();
    y2.useProgram(R2), R2.setUniformMatrix3fv("u_displayMat3", m9 === r5.VIEWPORT ? _3.displayMat3 : _3.displayViewMat3), R2.setUniform2fv("u_fillTranslation", s7), R2.setUniform1f("u_depth", o4.z + c7), R2.setUniform1f("u_outline_width", D3), h5 && R2.setUniform4fv("u_id", p4);
    let w4 = -1;
    for (const e7 of l6) {
      if (!e7.layerData.has(a2)) continue;
      e7.key.level !== w4 && (w4 = e7.key.level, U3.setDataUniforms(R2, g2, o4, w4, P3));
      const i2 = e7.layerData.get(a2);
      if (i2.prepareForRendering(y2), !i2.outlineIndexCount) continue;
      const r11 = i2.outlineVertexArrayObject;
      t(r11) || (y2.bindVAO(r11), R2.setUniformMatrix3fv("u_dvsMat3", e7.transforms.dvs), y2.setStencilFunction(I.EQUAL, e7.stencilRef, 255), y2.drawElements(E.TRIANGLES, i2.outlineIndexCount, C.UNSIGNED_INT, Uint32Array.BYTES_PER_ELEMENT * i2.outlineIndexStart), e7.triangleCount += i2.outlineIndexCount / 3);
    }
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLLine.js
var c8 = class extends t6 {
  constructor() {
    super(...arguments), this._programOptions = { id: false, pattern: false, sdf: false };
  }
  dispose() {
  }
  drawMany(o4, c9) {
    const { context: u3, displayLevel: d5, state: p4, drawPhase: y2, painter: g2, pixelRatio: E6, spriteMosaic: M4, styleLayerUID: _3, requestRender: v2, allowDelayedRender: U3 } = o4;
    if (!c9.some((e7) => {
      var _a;
      return ((_a = e7.layerData.get(_3)) == null ? void 0 : _a.lineIndexCount) ?? false;
    })) return;
    const I2 = o4.styleLayer, P3 = I2.lineMaterial, x5 = g2.vectorTilesMaterialManager, T2 = I2.getPaintValue("line-translate", d5), D3 = I2.getPaintValue("line-translate-anchor", d5), S3 = I2.getPaintProperty("line-pattern"), L3 = void 0 !== S3, N3 = L3 && S3.isDataDriven;
    let R2, V2;
    if (L3 && !N3) {
      const e7 = S3.getValue(d5);
      R2 = M4.getMosaicItemPosition(e7);
    }
    let h5 = false;
    if (!L3) {
      const e7 = I2.getPaintProperty("line-dasharray");
      if (V2 = void 0 !== e7, h5 = V2 && e7.isDataDriven, V2 && !h5) {
        const t10 = e7.getValue(d5), i2 = I2.getDashKey(t10, I2.getLayoutValue("line-cap", d5));
        R2 = M4.getMosaicItemPosition(i2);
      }
    }
    const w4 = 1 / E6, A3 = y2 === T.HITTEST, j = this._programOptions;
    j.id = A3, j.pattern = L3, j.sdf = V2;
    const b2 = x5.getMaterialProgram(u3, P3, j);
    if (U3 && r(v2) && !b2.compiled) return void v2();
    if (u3.useProgram(b2), b2.setUniformMatrix3fv("u_displayViewMat3", p4.displayViewMat3), b2.setUniformMatrix3fv("u_displayMat3", D3 === r5.VIEWPORT ? p4.displayMat3 : p4.displayViewMat3), b2.setUniform2fv("u_lineTranslation", T2), b2.setUniform1f("u_depth", I2.z), b2.setUniform1f("u_antialiasing", w4), A3) {
      const e7 = M2(_3 + 1);
      b2.setUniform4fv("u_id", e7);
    }
    if (R2 && r(R2)) {
      const { page: t10 } = R2, i2 = M4.getPageSize(t10);
      r(i2) && (M4.bind(u3, L.LINEAR, t10, Z), b2.setUniform2fv("u_mosaicSize", i2), b2.setUniform1i("u_texture", Z));
    }
    let z = -1;
    for (const i2 of c9) {
      if (!i2.layerData.has(_3)) continue;
      i2.key.level !== z && (z = i2.key.level, P3.setDataUniforms(b2, d5, I2, z, M4));
      const r10 = 2 ** (d5 - z) / E6;
      b2.setUniform1f("u_zoomFactor", r10);
      const a2 = i2.layerData.get(_3);
      if (!a2.lineIndexCount) continue;
      a2.prepareForRendering(u3);
      const o5 = a2.lineVertexArrayObject;
      if (!t(o5)) {
        if (u3.bindVAO(o5), b2.setUniformMatrix3fv("u_dvsMat3", i2.transforms.dvs), u3.setStencilFunction(I.EQUAL, i2.stencilRef, 255), N3 || h5) {
          const t10 = a2.patternMap;
          if (!t10) continue;
          for (const [i3, r11] of t10) {
            const t11 = M4.getPageSize(i3);
            r(t11) && (M4.bind(u3, L.LINEAR, i3, Z), b2.setUniform2fv("u_mosaicSize", t11), b2.setUniform1i("u_texture", Z), u3.drawElements(E.TRIANGLES, r11[1], C.UNSIGNED_INT, Uint32Array.BYTES_PER_ELEMENT * r11[0]));
          }
        } else u3.drawElements(E.TRIANGLES, a2.lineIndexCount, C.UNSIGNED_INT, Uint32Array.BYTES_PER_ELEMENT * a2.lineIndexStart);
        i2.triangleCount += a2.lineIndexCount / 3;
      }
    }
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLBrushVTLSymbol.js
var h4 = 1 / 65536;
var M3 = class extends t6 {
  constructor() {
    super(...arguments), this._iconProgramOptions = { id: false, sdf: false }, this._sdfProgramOptions = { id: false }, this._spritesTextureSize = n();
  }
  dispose() {
  }
  drawMany(e7, t10) {
    const { drawPhase: i2, styleLayerUID: a2 } = e7, r10 = e7.styleLayer;
    let n8;
    i2 === T.HITTEST && (n8 = M2(a2 + 1)), this._drawIcons(e7, r10, t10, n8), this._drawText(e7, r10, t10, n8);
  }
  _drawIcons(i2, a2, f3, m9) {
    const { context: p4, displayLevel: d5, drawPhase: y2, painter: g2, spriteMosaic: _3, state: h5, styleLayerUID: M4, requestRender: P3, allowDelayedRender: T2 } = i2, U3 = a2.iconMaterial, E6 = g2.vectorTilesMaterialManager;
    let x5, v2 = false;
    for (const e7 of f3) if (e7.layerData.has(M4) && (x5 = e7.layerData.get(M4), x5.iconPerPageElementsMap.size > 0)) {
      v2 = true;
      break;
    }
    if (!v2) return;
    const D3 = a2.getPaintValue("icon-translate", d5), I2 = a2.getPaintValue("icon-translate-anchor", d5);
    let R2 = a2.getLayoutValue("icon-rotation-alignment", d5);
    R2 === l3.AUTO && (R2 = a2.getLayoutValue("symbol-placement", d5) === n3.POINT ? l3.VIEWPORT : l3.MAP);
    const S3 = R2 === l3.MAP, V2 = a2.getLayoutValue("icon-keep-upright", d5) && S3, w4 = x5.isIconSDF, A3 = y2 === T.HITTEST, L3 = this._iconProgramOptions;
    L3.id = A3, L3.sdf = w4;
    const O3 = E6.getMaterialProgram(p4, U3, L3);
    if (T2 && r(P3) && !O3.compiled) return void P3();
    p4.useProgram(O3), O3.setUniformMatrix3fv("u_displayViewMat3", R2 === l3.MAP ? h5.displayViewMat3 : h5.displayMat3), O3.setUniformMatrix3fv("u_displayMat3", I2 === r5.VIEWPORT ? h5.displayMat3 : h5.displayViewMat3), O3.setUniform2fv("u_iconTranslation", D3), O3.setUniform1f("u_depth", a2.z), O3.setUniform1f("u_mapRotation", c2(h5.rotation)), O3.setUniform1f("u_keepUpright", V2 ? 1 : 0), O3.setUniform1f("u_level", 10 * d5), O3.setUniform1i("u_texture", Z), O3.setUniform1f("u_fadeDuration", e2 / 1e3), A3 && O3.setUniform4fv("u_id", m9);
    let N3 = -1;
    for (const e7 of f3) {
      if (!e7.layerData.has(M4)) continue;
      if (e7.key.level !== N3 && (N3 = e7.key.level, U3.setDataUniforms(O3, d5, a2, N3, _3)), x5 = e7.layerData.get(M4), 0 === x5.iconPerPageElementsMap.size) continue;
      x5.prepareForRendering(p4), x5.updateOpacityInfo();
      const r10 = x5.iconVertexArrayObject;
      if (!t(r10)) {
        p4.bindVAO(r10), O3.setUniformMatrix3fv("u_dvsMat3", e7.transforms.dvs), O3.setUniform1f("u_time", (performance.now() - x5.lastOpacityUpdate) / 1e3);
        for (const [t10, a3] of x5.iconPerPageElementsMap) this._renderIconRange(i2, O3, a3, t10, e7);
      }
    }
  }
  _renderIconRange(e7, t10, i2, a2, r10) {
    const { context: n8, spriteMosaic: s7 } = e7;
    this._spritesTextureSize[0] = s7.getWidth(a2) / 4, this._spritesTextureSize[1] = s7.getHeight(a2) / 4, t10.setUniform2fv("u_mosaicSize", this._spritesTextureSize), s7.bind(n8, L.LINEAR, a2, Z), n8.setStencilTestEnabled(true), n8.setStencilFunction(I.GREATER, 255, 255), n8.setStencilWriteMask(0), n8.drawElements(E.TRIANGLES, i2[1], C.UNSIGNED_INT, Uint32Array.BYTES_PER_ELEMENT * i2[0]), r10.triangleCount += i2[1] / 3;
  }
  _drawText(i2, l6, m9, p4) {
    const { context: d5, displayLevel: g2, drawPhase: _3, glyphMosaic: M4, painter: P3, pixelRatio: T2, spriteMosaic: U3, state: E6, styleLayerUID: x5, requestRender: v2, allowDelayedRender: D3 } = i2, I2 = l6.textMaterial, R2 = P3.vectorTilesMaterialManager;
    let S3, V2 = false;
    for (const e7 of m9) if (e7.layerData.has(x5) && (S3 = e7.layerData.get(x5), S3.glyphPerPageElementsMap.size > 0)) {
      V2 = true;
      break;
    }
    if (!V2) return;
    const w4 = l6.getPaintProperty("text-opacity");
    if (w4 && !w4.isDataDriven && 0 === w4.getValue(g2)) return;
    const A3 = l6.getPaintProperty("text-color"), L3 = !A3 || A3.isDataDriven || A3.getValue(g2)[3] > 0, O3 = l6.getPaintProperty("text-halo-width"), N3 = l6.getPaintProperty("text-halo-color"), b2 = (!O3 || O3.isDataDriven || O3.getValue(g2) > 0) && (!N3 || N3.isDataDriven || N3.getValue(g2)[3] > 0);
    if (!L3 && !b2) return;
    const z = 24 / 8;
    let k = l6.getLayoutValue("text-rotation-alignment", g2);
    k === l3.AUTO && (k = l6.getLayoutValue("symbol-placement", g2) === n3.POINT ? l3.VIEWPORT : l3.MAP);
    const j = k === l3.MAP, G3 = l6.getLayoutValue("text-keep-upright", g2) && j, W2 = _3 === T.HITTEST, F3 = 0.8 * z / T2;
    this._glyphTextureSize || (this._glyphTextureSize = t5(M4.width / 4, M4.height / 4));
    const B4 = l6.getPaintValue("text-translate", g2), H = l6.getPaintValue("text-translate-anchor", g2), C4 = this._sdfProgramOptions;
    C4.id = W2;
    const Y2 = R2.getMaterialProgram(d5, I2, C4);
    if (D3 && r(v2) && !Y2.compiled) return void v2();
    d5.useProgram(Y2), Y2.setUniformMatrix3fv("u_displayViewMat3", k === l3.MAP ? E6.displayViewMat3 : E6.displayMat3), Y2.setUniformMatrix3fv("u_displayMat3", H === r5.VIEWPORT ? E6.displayMat3 : E6.displayViewMat3), Y2.setUniform2fv("u_textTranslation", B4), Y2.setUniform1f("u_depth", l6.z + h4), Y2.setUniform2fv("u_mosaicSize", this._glyphTextureSize), Y2.setUniform1f("u_mapRotation", c2(E6.rotation)), Y2.setUniform1f("u_keepUpright", G3 ? 1 : 0), Y2.setUniform1f("u_level", 10 * g2), Y2.setUniform1i("u_texture", $), Y2.setUniform1f("u_antialiasingWidth", F3), Y2.setUniform1f("u_fadeDuration", e2 / 1e3), W2 && Y2.setUniform4fv("u_id", p4);
    let q = -1;
    for (const e7 of m9) {
      if (!e7.layerData.has(x5)) continue;
      if (e7.key.level !== q && (q = e7.key.level, I2.setDataUniforms(Y2, g2, l6, q, U3)), S3 = e7.layerData.get(x5), 0 === S3.glyphPerPageElementsMap.size) continue;
      S3.prepareForRendering(d5), S3.updateOpacityInfo();
      const i3 = S3.textVertexArrayObject;
      if (t(i3)) continue;
      d5.bindVAO(i3), Y2.setUniformMatrix3fv("u_dvsMat3", e7.transforms.dvs), d5.setStencilTestEnabled(true), d5.setStencilFunction(I.GREATER, 255, 255), d5.setStencilWriteMask(0);
      const a2 = (performance.now() - S3.lastOpacityUpdate) / 1e3;
      Y2.setUniform1f("u_time", a2), S3.glyphPerPageElementsMap.forEach((t10, i4) => {
        this._renderGlyphRange(d5, t10, i4, M4, Y2, b2, L3, e7);
      });
    }
  }
  _renderGlyphRange(e7, t10, i2, a2, r10, n8, s7, o4) {
    a2.bind(e7, L.LINEAR, i2, $), n8 && (r10.setUniform1f("u_halo", 1), e7.drawElements(E.TRIANGLES, t10[1], C.UNSIGNED_INT, Uint32Array.BYTES_PER_ELEMENT * t10[0]), o4.triangleCount += t10[1] / 3), s7 && (r10.setUniform1f("u_halo", 0), e7.drawElements(E.TRIANGLES, t10[1], C.UNSIGNED_INT, Uint32Array.BYTES_PER_ELEMENT * t10[0]), o4.triangleCount += t10[1] / 3);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushLabel.js
var m5 = (e7) => ee(e7.data, { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.SHORT }, { location: 1, name: "a_id", count: 4, type: C.UNSIGNED_BYTE }, { location: 2, name: "a_color", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 3, name: "a_haloColor", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 4, name: "a_texAndSize", count: 4, type: C.UNSIGNED_BYTE }, { location: 5, name: "a_refSymbolAndPlacementOffset", count: 4, type: C.UNSIGNED_BYTE }, { location: 6, name: "a_glyphData", count: 4, type: C.UNSIGNED_BYTE }, { location: 7, name: "a_vertexOffset", count: 2, type: C.SHORT }, { location: 8, name: "a_texCoords", count: 2, type: C.UNSIGNED_SHORT }] });
var l4 = class extends p2 {
  dispose() {
  }
  getGeometryType() {
    return E3.LABEL;
  }
  supportsSymbology(e7) {
    return true;
  }
  drawGeometry(t10, o4, a2, l6) {
    const { context: u3, painter: f3, state: d5, rendererInfo: c9, requestRender: _3, allowDelayedRender: p4 } = t10, y2 = Z2.load(a2.materialKey), E6 = y2.mapAligned ? 1 : 0;
    if (!E6 && Math.abs(o4.key.level - Math.round(100 * t10.displayLevel) / 100) >= 1) return;
    const { bufferLayouts: N3, attributes: U3 } = m5(y2), S3 = f3.materialManager.getMaterialProgram(t10, y2, "materials/label", U3, l6);
    if (p4 && r(_3) && !S3.compiled) return void _3();
    t10.context.setStencilFunction(I.EQUAL, 0, 255), u3.useProgram(S3), this._setSharedUniforms(S3, t10, o4), f3.textureManager.bindTextures(u3, S3, y2);
    const T2 = 1 === E6 ? d5.displayViewMat3 : d5.displayMat3;
    this._setSizeVVUniforms(y2, S3, c9, o4), S3.setUniform1f("u_mapRotation", Math.floor(d5.rotation / 360 * 254)), S3.setUniform1f("u_mapAligned", E6), S3.setUniformMatrix3fv("u_displayMat3", T2), S3.setUniform1f("u_opacity", 1), S3.setUniform2fv("u_screenSize", t10.state.size);
    const g2 = a2.target.getVAO(u3, N3, U3), G3 = a2.indexFrom * Uint32Array.BYTES_PER_ELEMENT;
    u3.bindVAO(g2), S3.setUniform1f("u_isHaloPass", 0), S3.setUniform1f("u_isBackgroundPass", 1), u3.drawElements(E.TRIANGLES, a2.indexCount, C.UNSIGNED_INT, G3), S3.setUniform1f("u_isHaloPass", 1), S3.setUniform1f("u_isBackgroundPass", 0), u3.drawElements(E.TRIANGLES, a2.indexCount, C.UNSIGNED_INT, G3), S3.setUniform1f("u_isHaloPass", 0), S3.setUniform1f("u_isBackgroundPass", 0), u3.drawElements(E.TRIANGLES, a2.indexCount, C.UNSIGNED_INT, G3), u3.setStencilTestEnabled(true), u3.setBlendingEnabled(true);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushLine.js
var s6 = (e7) => ee(e7.data, { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.SHORT }, { location: 1, name: "a_id", count: 4, type: C.UNSIGNED_BYTE }, { location: 2, name: "a_color", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 3, name: "a_offsetAndNormal", count: 4, type: C.BYTE }, { location: 4, name: "a_accumulatedDistanceAndHalfWidth", count: 2, type: C.UNSIGNED_SHORT }, { location: 5, name: "a_tlbr", count: 4, type: C.UNSIGNED_SHORT }, { location: 6, name: "a_segmentDirection", count: 4, type: C.BYTE }, { location: 7, name: "a_aux", count: 2, type: C.UNSIGNED_SHORT }, { location: 8, name: "a_zoomRange", count: 2, type: C.UNSIGNED_SHORT }] });
var m6 = class extends p2 {
  dispose() {
  }
  getGeometryType() {
    return E3.LINE;
  }
  supportsSymbology(e7) {
    return true;
  }
  drawGeometry(t10, o4, a2, m9) {
    const { context: l6, painter: c9, rendererInfo: u3, displayLevel: p4, passOptions: d5, requestRender: y2, allowDelayedRender: _3 } = t10, f3 = C3.load(a2.materialKey), E6 = r(d5) && "hittest" === d5.type;
    let N3 = s6(f3), g2 = E.TRIANGLES;
    E6 && (N3 = this._getTriangleDesc(a2.materialKey, N3), g2 = E.POINTS);
    const { attributes: S3, bufferLayouts: T2 } = N3, U3 = c9.materialManager.getMaterialProgram(t10, f3, "materials/line", S3, m9);
    if (_3 && r(y2) && !U3.compiled) return void y2();
    const G3 = 1 / t10.pixelRatio, I2 = 0;
    l6.useProgram(U3), this._setSharedUniforms(U3, t10, o4), f3.textureBinding && c9.textureManager.bindTextures(l6, U3, f3);
    const D3 = 2 ** (p4 - o4.key.level);
    U3.setUniform1f("u_zoomFactor", D3), U3.setUniform1f("u_blur", I2 + G3), U3.setUniform1f("u_antialiasing", G3), this._setSizeVVUniforms(f3, U3, u3, o4), this._setColorAndOpacityVVUniforms(f3, U3, u3), l6.setFaceCullingEnabled(false);
    const R2 = a2.target.getVAO(l6, T2, S3, E6);
    let b2 = a2.indexCount, x5 = a2.indexFrom * Uint32Array.BYTES_PER_ELEMENT;
    E6 && (b2 /= 3, x5 /= 3), l6.bindVAO(R2), l6.drawElements(g2, b2, C.UNSIGNED_INT, x5);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/brushes/WGLGeometryBrushText.js
var i = (t10) => ee(t10.data, { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.SHORT }, { location: 1, name: "a_id", count: 4, type: C.UNSIGNED_BYTE }, { location: 2, name: "a_color", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 3, name: "a_haloColor", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 4, name: "a_texFontSize", count: 4, type: C.UNSIGNED_BYTE }, { location: 5, name: "a_aux", count: 4, type: C.BYTE }, { location: 6, name: "a_zoomRange", count: 2, type: C.UNSIGNED_SHORT }, { location: 7, name: "a_vertexOffset", count: 2, type: C.SHORT }, { location: 8, name: "a_texCoords", count: 2, type: C.UNSIGNED_SHORT }] });
var m7 = class extends p2 {
  dispose() {
  }
  getGeometryType() {
    return E3.TEXT;
  }
  supportsSymbology(t10) {
    return true;
  }
  drawGeometry(e7, o4, a2, m9) {
    const { context: l6, painter: u3, rendererInfo: d5, state: f3, passOptions: _3, requestRender: p4, allowDelayedRender: c9 } = e7, y2 = P2.load(a2.materialKey), E6 = r(_3) && "hittest" === _3.type, { bufferLayouts: N3, attributes: U3 } = i(y2), S3 = u3.materialManager.getMaterialProgram(e7, y2, "materials/text", U3, m9);
    if (c9 && r(p4) && !S3.compiled) return void p4();
    l6.useProgram(S3);
    let T2 = E.TRIANGLES;
    E6 && (T2 = E.POINTS), this._setSharedUniforms(S3, e7, o4), u3.textureManager.bindTextures(l6, S3, y2), S3.setUniformMatrix3fv("u_displayMat3", f3.displayMat3), S3.setUniformMatrix3fv("u_displayViewMat3", f3.displayViewMat3), this._setSizeVVUniforms(y2, S3, d5, o4), this._setColorAndOpacityVVUniforms(y2, S3, d5), this._setRotationVVUniforms(y2, S3, d5);
    const x5 = a2.target.getVAO(l6, N3, U3), I2 = a2.indexFrom * Uint32Array.BYTES_PER_ELEMENT;
    S3.setUniform1f("u_isHaloPass", 0), S3.setUniform1f("u_isBackgroundPass", 1), l6.bindVAO(x5), l6.drawElements(T2, a2.indexCount, C.UNSIGNED_INT, I2), S3.setUniform1f("u_isHaloPass", 1), S3.setUniform1f("u_isBackgroundPass", 0), l6.drawElements(E.TRIANGLES, a2.indexCount, C.UNSIGNED_INT, I2), S3.setUniform1f("u_isHaloPass", 0), S3.setUniform1f("u_isBackgroundPass", 0), l6.drawElements(T2, a2.indexCount, C.UNSIGNED_INT, I2);
  }
};

// node_modules/@arcgis/core/views/2d/engine/brushes.js
var w3 = { marker: u, line: m6, fill: c5, text: m7, label: l4, clip: d3, stencil: h3, bitmap: m, overlay: m3, tileInfo: x3, vtlBackground: d4, vtlFill: m4, vtlLine: c8, vtlCircle: c6, vtlSymbol: M3, dotDensity: O2, heatmap: B3, pieChart: s5 };

// node_modules/@arcgis/core/views/2d/engine/webgl/Mesh2D.js
var x4 = (t10) => {
  switch (t10.BYTES_PER_ELEMENT) {
    case 1:
      return C.UNSIGNED_BYTE;
    case 2:
      return C.UNSIGNED_SHORT;
    case 4:
      return C.UNSIGNED_INT;
    default:
      throw new s2("Cannot get DataType of array");
  }
};
var g = (e7, t10, r10, s7) => {
  let o4 = 0;
  for (let n8 = 1; n8 < r10; n8++) {
    const r11 = e7[2 * (t10 + n8 - 1)], s8 = e7[2 * (t10 + n8 - 1) + 1];
    o4 += (e7[2 * (t10 + n8)] - r11) * (e7[2 * (t10 + n8) + 1] + s8);
  }
  return s7 ? o4 > 0 : o4 < 0;
};
var p3 = ({ coords: e7, lengths: t10 }, r10) => {
  const o4 = [];
  for (let n8 = 0, i2 = 0; n8 < t10.length; i2 += t10[n8], n8 += 1) {
    const c9 = i2, a2 = [];
    for (; n8 < t10.length - 1 && g(e7, i2 + t10[n8], t10[n8 + 1], r10); n8 += 1, i2 += t10[n8]) a2.push(i2 + t10[n8] - c9);
    const f3 = e7.slice(2 * c9, 2 * (i2 + t10[n8])), h5 = r3(f3, a2, 2);
    for (const e8 of h5) o4.push(e8 + c9);
  }
  return o4;
};
var l5 = class _l {
  constructor(e7, t10, r10, s7 = false) {
    this._cache = {}, this.vertices = e7, this.indices = t10, this.primitiveType = r10, this.isMapSpace = s7;
  }
  static fromRect({ x: e7, y: t10, width: r10, height: s7 }) {
    const o4 = e7, n8 = t10, i2 = o4 + r10, c9 = n8 + s7;
    return _l.fromScreenExtent({ xmin: o4, ymin: n8, xmax: i2, ymax: c9 });
  }
  static fromPath(e7) {
    const t10 = X(new t3(), e7.path, false, false), r10 = t10.coords, s7 = new Uint32Array(p3(t10, true)), o4 = new Uint32Array(r10.length / 2);
    for (let n8 = 0; n8 < o4.length; n8++) o4[n8] = w(Math.floor(r10[2 * n8]), Math.floor(r10[2 * n8 + 1]));
    return new _l({ geometry: o4 }, s7, E.TRIANGLES);
  }
  static fromGeometry(r10, s7) {
    var _a;
    const o4 = (_a = s7.geometry) == null ? void 0 : _a.type;
    switch (o4) {
      case "polygon":
        return _l.fromPolygon(r10, s7.geometry);
      case "extent":
        return _l.fromMapExtent(r10, s7.geometry);
      default:
        return s.getLogger("esri.views.2d.engine.webgl.Mesh2D").error(new s2("mapview-bad-type", `Unable to create a mesh from type ${o4}`, s7)), _l.fromRect({ x: 0, y: 0, width: 1, height: 1 });
    }
  }
  static fromPolygon(e7, t10) {
    const r10 = W(new t3(), t10, false, false), s7 = r10.coords, i2 = new Uint32Array(p3(r10, false)), h5 = new Uint32Array(s7.length / 2), u3 = n2(), y2 = n2();
    for (let n8 = 0; n8 < h5.length; n8++) r2(u3, s7[2 * n8], s7[2 * n8 + 1]), e7.toScreen(y2, u3), h5[n8] = w(Math.floor(y2[0]), Math.floor(y2[1]));
    return new _l({ geometry: h5 }, i2, E.TRIANGLES, true);
  }
  static fromScreenExtent({ xmin: e7, xmax: t10, ymin: r10, ymax: s7 }) {
    const o4 = { geometry: new Uint32Array([w(e7, r10), w(t10, r10), w(e7, s7), w(e7, s7), w(t10, r10), w(t10, s7)]) }, n8 = new Uint32Array([0, 1, 2, 3, 4, 5]);
    return new _l(o4, n8, E.TRIANGLES);
  }
  static fromMapExtent(e7, t10) {
    const [r10, s7] = e7.toScreen([0, 0], [t10.xmin, t10.ymin]), [o4, n8] = e7.toScreen([0, 0], [t10.xmax, t10.ymax]), i2 = { geometry: new Uint32Array([w(r10, s7), w(o4, s7), w(r10, n8), w(r10, n8), w(o4, s7), w(o4, n8)]) }, c9 = new Uint32Array([0, 1, 2, 3, 4, 5]);
    return new _l(i2, c9, E.TRIANGLES);
  }
  destroy() {
    r(this._cache.indexBuffer) && this._cache.indexBuffer.dispose();
    for (const e7 in this._cache.vertexBuffers) r(this._cache.vertexBuffers[e7]) && this._cache.vertexBuffers[e7].dispose();
  }
  get elementType() {
    return x4(this.indices);
  }
  getIndexBuffer(e7, t10 = F.STATIC_DRAW) {
    return this._cache.indexBuffer || (this._cache.indexBuffer = E5.createIndex(e7, t10, this.indices)), this._cache.indexBuffer;
  }
  getVertexBuffers(e7, t10 = F.STATIC_DRAW) {
    return this._cache.vertexBuffers || (this._cache.vertexBuffers = Object.keys(this.vertices).reduce((r10, s7) => ({ ...r10, [s7]: E5.createVertex(e7, t10, this.vertices[s7]) }), {})), this._cache.vertexBuffers;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/ClippingInfo.js
var n7 = (t10) => parseFloat(t10) / 100;
var m8 = class _m extends r6 {
  constructor(t10, e7) {
    super(), this._clip = e7, this._cache = {}, this.stage = t10, this._handle = l2(() => e7.version, () => this._invalidate()), this.ready();
  }
  static fromClipArea(t10, e7) {
    return new _m(t10, e7);
  }
  _destroyGL() {
    r(this._cache.mesh) && (this._cache.mesh.destroy(), this._cache.mesh = null), r(this._cache.vao) && (this._cache.vao.dispose(), this._cache.vao = null);
  }
  destroy() {
    this._destroyGL(), this._handle.remove();
  }
  getVAO(t10, e7, r10, i2) {
    const [o4, h5] = e7.size;
    if ("geometry" !== this._clip.type && this._lastWidth === o4 && this._lastHeight === h5 || (this._lastWidth = o4, this._lastHeight = h5, this._destroyGL()), t(this._cache.vao)) {
      const s7 = this._createMesh(e7, this._clip), o5 = s7.getIndexBuffer(t10), h6 = s7.getVertexBuffers(t10);
      this._cache.mesh = s7, this._cache.vao = new f2(t10, r10, i2, h6, o5);
    }
    return this._cache.vao;
  }
  _createTransforms() {
    return { dvs: e() };
  }
  _invalidate() {
    this._destroyGL(), this.requestRender();
  }
  _createScreenRect(t10, e7) {
    const [r10, s7] = t10.size, i2 = "string" == typeof e7.left ? n7(e7.left) * r10 : e7.left, o4 = "string" == typeof e7.right ? n7(e7.right) * r10 : e7.right, h5 = "string" == typeof e7.top ? n7(e7.top) * s7 : e7.top, a2 = "string" == typeof e7.bottom ? n7(e7.bottom) * s7 : e7.bottom, c9 = i2, m9 = h5;
    return { x: c9, y: m9, width: Math.max(r10 - o4 - c9, 0), height: Math.max(s7 - a2 - m9, 0) };
  }
  _createMesh(r10, s7) {
    switch (s7.type) {
      case "rect":
        return l5.fromRect(this._createScreenRect(r10, s7));
      case "path":
        return l5.fromPath(s7);
      case "geometry":
        return l5.fromGeometry(r10, s7);
      default:
        return s.getLogger("esri.views.2d.engine.webgl.ClippingInfo").error(new s2("mapview-bad-type", "Unable to create ClippingInfo mesh from clip of type: ${clip.type}")), l5.fromRect({ x: 0, y: 0, width: 1, height: 1 });
    }
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/WGLContainer.js
var a = class extends h2 {
  constructor() {
    super(...arguments), this.name = this.constructor.name;
  }
  set clips(e7) {
    this._clips = e7, this.children.forEach((r10) => r10.clips = e7), this._updateClippingInfo();
  }
  beforeRender(e7) {
    super.beforeRender(e7), this.updateTransforms(e7.state);
  }
  _createTransforms() {
    return { dvs: e() };
  }
  doRender(e7) {
    const r10 = this.createRenderParams(e7), { painter: s7, globalOpacity: t10, profiler: i2, drawPhase: n8 } = r10, a2 = n8 === T.LABEL || n8 === T.HIGHLIGHT ? 1 : t10 * this.computedOpacity;
    i2.recordContainerStart(this.name), s7.beforeRenderLayer(r10, this._clippingInfos ? 255 : 0, a2), this.renderChildren(r10), s7.compositeLayer(r10, a2), i2.recordContainerEnd();
  }
  renderChildren(r10) {
    t(this._renderPasses) && (this._renderPasses = this.prepareRenderPasses(r10.painter));
    for (const e7 of this._renderPasses) try {
      e7.render(r10);
    } catch (s7) {
    }
  }
  createRenderParams(e7) {
    return e7.requireFBO = this.requiresDedicatedFBO, e7;
  }
  prepareRenderPasses(e7) {
    return [e7.registerRenderPass({ name: "clip", brushes: [w3.clip], target: () => this._clippingInfos, drawPhase: T.MAP | T.LABEL | T.LABEL_ALPHA | T.DEBUG | T.HIGHLIGHT })];
  }
  updateTransforms(e7) {
    for (const r10 of this.children) r10.setTransform(e7);
  }
  onAttach() {
    super.onAttach(), this._updateClippingInfo();
  }
  onDetach() {
    super.onDetach(), this._updateClippingInfo();
  }
  _updateClippingInfo() {
    r(this._clippingInfos) && (this._clippingInfos.forEach((e8) => e8.destroy()), this._clippingInfos = null);
    const e7 = this.stage;
    if (!e7) return;
    const s7 = this._clips;
    r(s7) && s7.length && (this._clippingInfos = s7.items.map((r10) => m8.fromClipArea(e7, r10))), this.requestRender();
  }
};

export {
  n4 as n,
  t6 as t,
  n5 as n2,
  t8 as t2,
  t9 as t3,
  m2 as m,
  x3 as x,
  h3 as h,
  w3 as w,
  a
};
//# sourceMappingURL=chunk-SMSZBVG5.js.map
