import {
  r
} from "./chunk-6HCWK637.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  b
} from "./chunk-HP475EI3.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/rest/support/AttachmentQuery.js
var a2;
var p2 = a2 = class extends l {
  constructor(t) {
    super(t), this.attachmentTypes = null, this.attachmentsWhere = null, this.cacheHint = void 0, this.keywords = null, this.globalIds = null, this.name = null, this.num = null, this.objectIds = null, this.returnMetadata = false, this.size = null, this.start = null, this.where = null;
  }
  writeStart(t, e2) {
    e2.resultOffset = this.start, e2.resultRecordCount = this.num || 10;
  }
  clone() {
    return new a2(p({ attachmentTypes: this.attachmentTypes, attachmentsWhere: this.attachmentsWhere, cacheHint: this.cacheHint, keywords: this.keywords, where: this.where, globalIds: this.globalIds, name: this.name, num: this.num, objectIds: this.objectIds, returnMetadata: this.returnMetadata, size: this.size, start: this.start }));
  }
};
e([y({ type: [String], json: { write: true } })], p2.prototype, "attachmentTypes", void 0), e([y({ type: String, json: { read: { source: "attachmentsDefinitionExpression" }, write: { target: "attachmentsDefinitionExpression" } } })], p2.prototype, "attachmentsWhere", void 0), e([y({ type: Boolean, json: { write: true } })], p2.prototype, "cacheHint", void 0), e([y({ type: [String], json: { write: true } })], p2.prototype, "keywords", void 0), e([y({ type: [Number], json: { write: true } })], p2.prototype, "globalIds", void 0), e([y({ json: { write: true } })], p2.prototype, "name", void 0), e([y({ type: Number, json: { read: { source: "resultRecordCount" } } })], p2.prototype, "num", void 0), e([y({ type: [Number], json: { write: true } })], p2.prototype, "objectIds", void 0), e([y({ type: Boolean, json: { default: false, write: true } })], p2.prototype, "returnMetadata", void 0), e([y({ type: [Number], json: { write: true } })], p2.prototype, "size", void 0), e([y({ type: Number, json: { read: { source: "resultOffset" } } })], p2.prototype, "start", void 0), e([r("start"), r("num")], p2.prototype, "writeStart", null), e([y({ type: String, json: { read: { source: "definitionExpression" }, write: { target: "definitionExpression" } } })], p2.prototype, "where", void 0), p2 = a2 = e([a("esri.rest.support.AttachmentQuery")], p2), p2.from = b(p2);
var c = p2;

export {
  c
};
//# sourceMappingURL=chunk-ZVU4V5QV.js.map
