{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/webgl/WGLRendererInfo.js", "../../@arcgis/core/views/2d/engine/FeatureContainer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrapOr as e,unwrap as t,isSome as i}from\"../../../../core/maybe.js\";import{pt2px as s}from\"../../../../core/screenUtils.js\";import{getMetersPerUnitForSR as a}from\"../../../../core/unitUtils.js\";import{meterIn as r}from\"../../../../renderers/support/lengthUtils.js\";import{isDefined as l,isNumber as o}from\"./Utils.js\";import{Technique as n}from\"./techniques/Technique.js\";import{getTechniqueFromRenderer as v}from\"./techniques/utils.js\";import{getWebGLCapabilities as u}from\"../../../webgl/capabilities.js\";function c(e,t){const i=t.length;if(e<t[0].value||1===i)return t[0].size;for(let s=1;s<i;s++)if(e<t[s].value){const i=(e-t[s-1].value)/(t[s].value-t[s-1].value);return t[s-1].size+i*(t[s].size-t[s-1].size)}return t[i-1].size}class h{constructor(){this.symbolLevels=[],this.vvColorValues=new Float32Array(8),this.vvColors=new Float32Array(32),this.vvOpacityValues=new Float32Array(8),this.vvOpacities=new Float32Array(8),this.vvSizeMinMaxValue=new Float32Array(4),this.outsideLabelsVisible=!1,this._vvMaterialParameters={vvSizeEnabled:!1,vvColorEnabled:!1,vvRotationEnabled:!1,vvRotationType:\"geographic\",vvOpacityEnabled:!1},this._technique=n}getSizeVVFieldStops(i){const s=this._vvSizeFieldStops;if(s)switch(s.type){case\"static\":return s;case\"level-dependent\":return e(s.levels[i],(()=>{let e=1/0,a=0;for(const t in s.levels){const s=parseFloat(t),r=Math.abs(i-s);r<e&&(e=r,a=s)}if(e===1/0)return{sizes:new Float32Array([0,0,0,0,0,0]),values:new Float32Array([0,0,0,0,0,0])};const r=2**((i-a)/2),l=t(s.levels[a]),o=new Float32Array(l.values);return o[2]*=r,o[3]*=r,{sizes:t(l.sizes),values:o}}));default:return}}get vvMaterialParameters(){return this._vvMaterialParameters}update(e){i(this._vvInfo)&&this._updateVisualVariables(this._vvInfo.vvRanges,e)}setInfo(e,t,i){this._updateEffects(i),this._vvInfo=t,this._technique=v(e),this.rendererSchema=this._technique.createOrUpdateRendererSchema(this.rendererSchema,e)}getVariation(){return{...this._technique.getVariation(this.rendererSchema),outsideLabelsVisible:this.outsideLabelsVisible,supportsTextureFloat:u(\"2d\").supportsTextureFloat}}getVariationHash(){return this._technique.getVariationHash(this.rendererSchema)<<1|(this.outsideLabelsVisible?1:0)}_updateEffects(e){i(e)?this.outsideLabelsVisible=e.excludedLabelsVisible:this.outsideLabelsVisible=!1}_updateVisualVariables(e,t){const i=this._vvMaterialParameters;if(i.vvOpacityEnabled=!1,i.vvSizeEnabled=!1,i.vvColorEnabled=!1,i.vvRotationEnabled=!1,!e)return;const n=e.size;if(n){if(i.vvSizeEnabled=!0,n.minMaxValue){const e=n.minMaxValue;let i,a;if(l(e.minSize)&&l(e.maxSize))if(o(e.minSize)&&o(e.maxSize))i=s(e.minSize),a=s(e.maxSize);else{const r=t.scale;i=s(c(r,e.minSize.stops)),a=s(c(r,e.maxSize.stops))}this.vvSizeMinMaxValue.set([e.minDataValue,e.maxDataValue,i,a])}if(n.scaleStops&&(this.vvSizeScaleStopsValue=s(c(t.scale,n.scaleStops.stops))),n.unitValue){const e=a(t.spatialReference)/r[n.unitValue.unit];this.vvSizeUnitValueToPixelsRatio=e/t.resolution}n.fieldStops&&(this._vvSizeFieldStops=n.fieldStops)}const v=e.color;v&&(i.vvColorEnabled=!0,this.vvColorValues.set(v.values),this.vvColors.set(v.colors));const u=e.opacity;u&&(i.vvOpacityEnabled=!0,this.vvOpacityValues.set(u.values),this.vvOpacities.set(u.opacities));const h=e.rotation;h&&(i.vvRotationEnabled=!0,i.vvRotationType=h.type)}}export{h as WGLRendererInfo};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{createResolver as e}from\"../../../core/promiseUtils.js\";import t from\"../../../core/Queue.js\";import{AttributeStoreView as r}from\"./webgl/AttributeStoreView.js\";import s from\"./webgl/TileContainer.js\";import{WGLRendererInfo as i}from\"./webgl/WGLRendererInfo.js\";class o extends s{constructor(e){super(e),this._rendererInfo=new i,this._materialItemsRequestQueue=new t,this.attributeView=new r((()=>this.onAttributeStoreUpdate()))}destroy(){this.children.forEach((e=>e.destroy())),this.removeAllChildren(),this.attributeView.destroy(),this._materialItemsRequestQueue.clear()}setRendererInfo(e,t,r){this._rendererInfo.setInfo(e,t,r),this.requestRender()}async getMaterialItems(t,r){if(!t||0===t.length)return[];const s=e();return this._materialItemsRequestQueue.push({items:t,abortOptions:r,resolver:s}),this.requestRender(),s.promise}doRender(e){if(e.context.capabilities.enable(\"textureFloat\"),e.context.capabilities.enable(\"vao\"),this._materialItemsRequestQueue.length>0){let t=this._materialItemsRequestQueue.pop();for(;t;)this._processMaterialItemRequest(e,t),t=this._materialItemsRequestQueue.pop()}super.doRender(e)}renderChildren(e){for(const t of this.children)t.commit(e);this._rendererInfo.update(e.state),super.renderChildren(e)}updateTransforms(e){if(this.children.some((e=>e.hasData)))for(const t of this.children)t.setTransform(e)}createRenderParams(e){const t=super.createRenderParams(e);return t.rendererInfo=this._rendererInfo,t.attributeView=this.attributeView,t}onAttributeStoreUpdate(){}_processMaterialItemRequest(e,{items:t,abortOptions:r,resolver:s}){const{painter:i,pixelRatio:o}=e,a=t.map((e=>i.textureManager.rasterizeItem(e.symbol,o,e.glyphIds,r)));Promise.all(a).then((e=>{if(!this.stage)return void s.reject();const r=e.map(((e,r)=>({id:t[r].id,mosaicItem:e})));s.resolve(r)}),s.reject)}}export{o as FeatureContainer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIogB,SAASA,GAAEC,IAAE,GAAE;AAAC,QAAMC,KAAE,EAAE;AAAO,MAAGD,KAAE,EAAE,CAAC,EAAE,SAAO,MAAIC,GAAE,QAAO,EAAE,CAAC,EAAE;AAAK,WAAQ,IAAE,GAAE,IAAEA,IAAE,IAAI,KAAGD,KAAE,EAAE,CAAC,EAAE,OAAM;AAAC,UAAMC,MAAGD,KAAE,EAAE,IAAE,CAAC,EAAE,UAAQ,EAAE,CAAC,EAAE,QAAM,EAAE,IAAE,CAAC,EAAE;AAAO,WAAO,EAAE,IAAE,CAAC,EAAE,OAAKC,MAAG,EAAE,CAAC,EAAE,OAAK,EAAE,IAAE,CAAC,EAAE;AAAA,EAAK;AAAC,SAAO,EAAEA,KAAE,CAAC,EAAE;AAAI;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,eAAa,CAAC,GAAE,KAAK,gBAAc,IAAI,aAAa,CAAC,GAAE,KAAK,WAAS,IAAI,aAAa,EAAE,GAAE,KAAK,kBAAgB,IAAI,aAAa,CAAC,GAAE,KAAK,cAAY,IAAI,aAAa,CAAC,GAAE,KAAK,oBAAkB,IAAI,aAAa,CAAC,GAAE,KAAK,uBAAqB,OAAG,KAAK,wBAAsB,EAAC,eAAc,OAAG,gBAAe,OAAG,mBAAkB,OAAG,gBAAe,cAAa,kBAAiB,MAAE,GAAE,KAAK,aAAWD;AAAA,EAAC;AAAA,EAAC,oBAAoBC,IAAE;AAAC,UAAM,IAAE,KAAK;AAAkB,QAAG,EAAE,SAAO,EAAE,MAAK;AAAA,MAAC,KAAI;AAAS,eAAO;AAAA,MAAE,KAAI;AAAkB,eAAO,EAAE,EAAE,OAAOA,EAAC,GAAG,MAAI;AAAC,cAAID,KAAE,IAAE,GAAE,IAAE;AAAE,qBAAU,KAAK,EAAE,QAAO;AAAC,kBAAME,KAAE,WAAW,CAAC,GAAEC,KAAE,KAAK,IAAIF,KAAEC,EAAC;AAAE,YAAAC,KAAEH,OAAIA,KAAEG,IAAE,IAAED;AAAA,UAAE;AAAC,cAAGF,OAAI,IAAE,EAAE,QAAM,EAAC,OAAM,IAAI,aAAa,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,QAAO,IAAI,aAAa,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,EAAC;AAAE,gBAAMG,KAAE,OAAKF,KAAE,KAAG,IAAGG,KAAE,EAAE,EAAE,OAAO,CAAC,CAAC,GAAEC,KAAE,IAAI,aAAaD,GAAE,MAAM;AAAE,iBAAOC,GAAE,CAAC,KAAGF,IAAEE,GAAE,CAAC,KAAGF,IAAE,EAAC,OAAM,EAAEC,GAAE,KAAK,GAAE,QAAOC,GAAC;AAAA,QAAC,CAAE;AAAA,MAAE;AAAQ;AAAA,IAAM;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK;AAAA,EAAqB;AAAA,EAAC,OAAOL,IAAE;AAAC,MAAE,KAAK,OAAO,KAAG,KAAK,uBAAuB,KAAK,QAAQ,UAASA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE,GAAEC,IAAE;AAAC,SAAK,eAAeA,EAAC,GAAE,KAAK,UAAQ,GAAE,KAAK,aAAW,EAAED,EAAC,GAAE,KAAK,iBAAe,KAAK,WAAW,6BAA6B,KAAK,gBAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,WAAM,EAAC,GAAG,KAAK,WAAW,aAAa,KAAK,cAAc,GAAE,sBAAqB,KAAK,sBAAqB,sBAAqBI,GAAE,IAAI,EAAE,qBAAoB;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAO,KAAK,WAAW,iBAAiB,KAAK,cAAc,KAAG,KAAG,KAAK,uBAAqB,IAAE;AAAA,EAAE;AAAA,EAAC,eAAeJ,IAAE;AAAC,MAAEA,EAAC,IAAE,KAAK,uBAAqBA,GAAE,wBAAsB,KAAK,uBAAqB;AAAA,EAAE;AAAA,EAAC,uBAAuBA,IAAE,GAAE;AAAC,UAAMC,KAAE,KAAK;AAAsB,QAAGA,GAAE,mBAAiB,OAAGA,GAAE,gBAAc,OAAGA,GAAE,iBAAe,OAAGA,GAAE,oBAAkB,OAAG,CAACD,GAAE;AAAO,UAAM,IAAEA,GAAE;AAAK,QAAG,GAAE;AAAC,UAAGC,GAAE,gBAAc,MAAG,EAAE,aAAY;AAAC,cAAMD,KAAE,EAAE;AAAY,YAAIC,IAAE;AAAE,YAAG,EAAED,GAAE,OAAO,KAAG,EAAEA,GAAE,OAAO,EAAE,KAAGM,GAAEN,GAAE,OAAO,KAAGM,GAAEN,GAAE,OAAO,EAAE,CAAAC,KAAE,EAAED,GAAE,OAAO,GAAE,IAAE,EAAEA,GAAE,OAAO;AAAA,aAAM;AAAC,gBAAMG,KAAE,EAAE;AAAM,UAAAF,KAAE,EAAEF,GAAEI,IAAEH,GAAE,QAAQ,KAAK,CAAC,GAAE,IAAE,EAAED,GAAEI,IAAEH,GAAE,QAAQ,KAAK,CAAC;AAAA,QAAC;AAAC,aAAK,kBAAkB,IAAI,CAACA,GAAE,cAAaA,GAAE,cAAaC,IAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAE,eAAa,KAAK,wBAAsB,EAAEF,GAAE,EAAE,OAAM,EAAE,WAAW,KAAK,CAAC,IAAG,EAAE,WAAU;AAAC,cAAMC,KAAE,EAAE,EAAE,gBAAgB,IAAE,EAAE,EAAE,UAAU,IAAI;AAAE,aAAK,+BAA6BA,KAAE,EAAE;AAAA,MAAU;AAAC,QAAE,eAAa,KAAK,oBAAkB,EAAE;AAAA,IAAW;AAAC,UAAM,IAAEA,GAAE;AAAM,UAAIC,GAAE,iBAAe,MAAG,KAAK,cAAc,IAAI,EAAE,MAAM,GAAE,KAAK,SAAS,IAAI,EAAE,MAAM;AAAG,UAAMM,KAAEP,GAAE;AAAQ,IAAAO,OAAIN,GAAE,mBAAiB,MAAG,KAAK,gBAAgB,IAAIM,GAAE,MAAM,GAAE,KAAK,YAAY,IAAIA,GAAE,SAAS;AAAG,UAAMC,KAAER,GAAE;AAAS,IAAAQ,OAAIP,GAAE,oBAAkB,MAAGA,GAAE,iBAAeO,GAAE;AAAA,EAAK;AAAC;;;ACAl/F,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,gBAAc,IAAI,KAAE,KAAK,6BAA2B,IAAIA,MAAE,KAAK,gBAAc,IAAI,EAAG,MAAI,KAAK,uBAAuB,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAS,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,kBAAkB,GAAE,KAAK,cAAc,QAAQ,GAAE,KAAK,2BAA2B,MAAM;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE,GAAEC,IAAE;AAAC,SAAK,cAAc,QAAQD,IAAE,GAAEC,EAAC,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiB,GAAEA,IAAE;AAAC,QAAG,CAAC,KAAG,MAAI,EAAE,OAAO,QAAM,CAAC;AAAE,UAAM,IAAE,EAAE;AAAE,WAAO,KAAK,2BAA2B,KAAK,EAAC,OAAM,GAAE,cAAaA,IAAE,UAAS,EAAC,CAAC,GAAE,KAAK,cAAc,GAAE,EAAE;AAAA,EAAO;AAAA,EAAC,SAASD,IAAE;AAAC,QAAGA,GAAE,QAAQ,aAAa,OAAO,cAAc,GAAEA,GAAE,QAAQ,aAAa,OAAO,KAAK,GAAE,KAAK,2BAA2B,SAAO,GAAE;AAAC,UAAI,IAAE,KAAK,2BAA2B,IAAI;AAAE,aAAK,IAAG,MAAK,4BAA4BA,IAAE,CAAC,GAAE,IAAE,KAAK,2BAA2B,IAAI;AAAA,IAAC;AAAC,UAAM,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,eAAU,KAAK,KAAK,SAAS,GAAE,OAAOA,EAAC;AAAE,SAAK,cAAc,OAAOA,GAAE,KAAK,GAAE,MAAM,eAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,QAAG,KAAK,SAAS,KAAM,CAAAA,OAAGA,GAAE,OAAQ,EAAE,YAAU,KAAK,KAAK,SAAS,GAAE,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,UAAM,IAAE,MAAM,mBAAmBA,EAAC;AAAE,WAAO,EAAE,eAAa,KAAK,eAAc,EAAE,gBAAc,KAAK,eAAc;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAA,EAAC;AAAA,EAAC,4BAA4BA,IAAE,EAAC,OAAM,GAAE,cAAaC,IAAE,UAAS,EAAC,GAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,YAAWC,GAAC,IAAEH,IAAE,IAAE,EAAE,IAAK,CAAAA,OAAGE,GAAE,eAAe,cAAcF,GAAE,QAAOG,IAAEH,GAAE,UAASC,EAAC,CAAE;AAAE,YAAQ,IAAI,CAAC,EAAE,KAAM,CAAAD,OAAG;AAAC,UAAG,CAAC,KAAK,MAAM,QAAO,KAAK,EAAE,OAAO;AAAE,YAAMC,KAAED,GAAE,IAAK,CAACA,IAAEC,QAAK,EAAC,IAAG,EAAEA,EAAC,EAAE,IAAG,YAAWD,GAAC,EAAG;AAAE,QAAE,QAAQC,EAAC;AAAA,IAAC,GAAG,EAAE,MAAM;AAAA,EAAC;AAAC;", "names": ["c", "e", "i", "s", "r", "l", "o", "$", "u", "h", "e", "r", "i", "o"]}