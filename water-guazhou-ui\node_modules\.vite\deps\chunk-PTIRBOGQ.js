import {
  E,
  L
} from "./chunk-JXLVNWKF.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/zscale.js
function o(o2, f, u) {
  if (t(f) || t(u) || u.vcsWkid || E(f, u)) return null;
  const a = L(f) / L(u);
  if (1 === a) return null;
  switch (o2) {
    case "point":
    case "esriGeometryPoint":
      return (n) => r(n, a);
    case "polyline":
    case "esriGeometryPolyline":
      return (n) => s(n, a);
    case "polygon":
    case "esriGeometryPolygon":
      return (n) => i(n, a);
    case "multipoint":
    case "esriGeometryMultipoint":
      return (n) => c(n, a);
    case "extent":
    case "esriGeometryExtent":
      return (n) => l(n, a);
    default:
      return null;
  }
}
function r(n, e) {
  n && null != n.z && (n.z *= e);
}
function i(n, e) {
  if (n) for (const t2 of n.rings) for (const n2 of t2) n2.length > 2 && (n2[2] *= e);
}
function s(n, e) {
  if (n) for (const t2 of n.paths) for (const n2 of t2) n2.length > 2 && (n2[2] *= e);
}
function c(n, e) {
  if (n) for (const t2 of n.points) t2.length > 2 && (t2[2] *= e);
}
function l(n, e) {
  n && null != n.zmin && null != n.zmax && (n.zmin *= e, n.zmax *= e);
}

export {
  o
};
//# sourceMappingURL=chunk-PTIRBOGQ.js.map
