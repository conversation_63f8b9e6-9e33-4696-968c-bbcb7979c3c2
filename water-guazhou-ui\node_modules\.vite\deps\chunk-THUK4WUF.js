import {
  s as s2
} from "./chunk-5ZZCQR67.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  n
} from "./chunk-7THWOTCY.js";
import {
  v
} from "./chunk-ZACBBT3Y.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  s
} from "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  a as a3
} from "./chunk-EIGTETCG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  a
} from "./chunk-HP475EI3.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/Camera.js
var d = class extends i(l) {
  constructor(...o2) {
    super(...o2), this.position = new w([0, 0, 0]), this.heading = 0, this.tilt = 0, this.fov = 55;
  }
  normalizeCtorArgs(o2, r3, t2, e2) {
    if (o2 && "object" == typeof o2 && ("x" in o2 || Array.isArray(o2))) {
      const s3 = { position: o2 };
      return null != r3 && (s3.heading = r3), null != t2 && (s3.tilt = t2), null != e2 && (s3.fov = e2), s3;
    }
    return o2;
  }
  writePosition(o2, r3, t2, e2) {
    const s3 = o2.clone();
    s3.x = a(o2.x || 0), s3.y = a(o2.y || 0), s3.z = o2.hasZ ? a(o2.z || 0) : o2.z, r3[t2] = s3.write({}, e2);
  }
  readPosition(o2, r3) {
    const t2 = new w();
    return t2.read(o2, r3), t2.x = a(t2.x || 0), t2.y = a(t2.y || 0), t2.z = t2.hasZ ? a(t2.z || 0) : t2.z, t2;
  }
  equals(o2) {
    return !t(o2) && (this.tilt === o2.tilt && this.heading === o2.heading && this.fov === o2.fov && this.position.equals(o2.position));
  }
};
e([y({ type: w, json: { write: { isRequired: true } } })], d.prototype, "position", void 0), e([r2("position")], d.prototype, "writePosition", null), e([o("position")], d.prototype, "readPosition", null), e([y({ type: Number, nonNullable: true, json: { write: { isRequired: true } } }), s((o2) => s2.normalize(a(o2)))], d.prototype, "heading", void 0), e([y({ type: Number, nonNullable: true, json: { write: { isRequired: true } } }), s((o2) => a3(a(o2), -180, 180))], d.prototype, "tilt", void 0), e([y({ type: Number, nonNullable: true, json: { read: false, write: false } })], d.prototype, "fov", void 0), d = e([a2("esri.Camera")], d);
var y2 = d;

// node_modules/@arcgis/core/Viewpoint.js
var m;
var n2 = m = class extends l {
  constructor(r3) {
    super(r3), this.rotation = 0, this.scale = 0, this.targetGeometry = null, this.camera = null;
  }
  castRotation(r3) {
    return (r3 %= 360) < 0 && (r3 += 360), r3;
  }
  clone() {
    return new m({ rotation: this.rotation, scale: this.scale, targetGeometry: r(this.targetGeometry) ? this.targetGeometry.clone() : null, camera: r(this.camera) ? this.camera.clone() : null });
  }
};
function l2() {
  return { enabled: !this.camera };
}
e([y({ type: Number, json: { write: true, origins: { "web-map": { default: 0, write: true }, "web-scene": { write: { overridePolicy: l2 } } } } })], n2.prototype, "rotation", void 0), e([s("rotation")], n2.prototype, "castRotation", null), e([y({ type: Number, json: { write: true, origins: { "web-map": { default: 0, write: true }, "web-scene": { write: { overridePolicy: l2 } } } } })], n2.prototype, "scale", void 0), e([y({ types: n, json: { read: v, write: true, origins: { "web-scene": { read: v, write: { overridePolicy: l2 } } } } })], n2.prototype, "targetGeometry", void 0), e([y({ type: y2, json: { write: true } })], n2.prototype, "camera", void 0), n2 = m = e([a2("esri.Viewpoint")], n2);
var u = n2;

export {
  u
};
//# sourceMappingURL=chunk-THUK4WUF.js.map
