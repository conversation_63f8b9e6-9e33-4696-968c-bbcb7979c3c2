{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/features/processors/HeatmapProcessor.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import\"../../../../../core/has.js\";import{isSome as t,applySome as s}from\"../../../../../core/maybe.js\";import\"../../../../../core/Logger.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import\"../../../../../core/Error.js\";import{subclass as r}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{diff as i}from\"../../../../../core/accessorSupport/diffUtils.js\";import{calculateHeatmapIntensityInfoReaders as o}from\"../../../../../renderers/support/heatmapUtils.js\";import{TILE_SIZE as a}from\"../../../engine/webgl/definitions.js\";import n from\"./BaseProcessor.js\";import{getPow2NeighborKey as d}from\"../support/tileUtils.js\";class p{constructor(e,t){this.offset=e,this.extent=t}}function c(e){const t=e.key,s=new Map,r=256,i=a,o=e.tileInfoView.tileInfo.isWrappable;return s.set(d(t,-1,-1,o).id,new p([-i,-i],[i-r,i-r,i,i])),s.set(d(t,0,-1,o).id,new p([0,-i],[0,i-r,i,i])),s.set(d(t,1,-1,o).id,new p([i,-i],[0,i-r,r,i])),s.set(d(t,-1,0,o).id,new p([-i,0],[i-r,0,i,i])),s.set(d(t,1,0,o).id,new p([i,0],[0,0,r,i])),s.set(d(t,-1,1,o).id,new p([-i,i],[i-r,0,i,r])),s.set(d(t,0,1,o).id,new p([0,i],[0,0,i,r])),s.set(d(t,1,1,o).id,new p([i,i],[0,0,r,r])),s}let l=class extends n{constructor(){super(...arguments),this.type=\"heatmap\",this._tileKeyToFeatureSets=new Map}initialize(){this.handles.add([this.tileStore.on(\"update\",this.onTileUpdate.bind(this))])}async update(e,t){const s=t.schema.processors[0];if(\"heatmap\"!==s.type)return;i(this._schema,s)&&(e.mesh=!0,this._schema=s)}onTileUpdate(e){for(const t of e.removed)this._tileKeyToFeatureSets.delete(t.key.id)}onTileClear(e){const t={clear:!0};return this._tileKeyToFeatureSets.delete(e.key.id),this.remoteClient.invoke(\"tileRenderer.onTileData\",{tileKey:e.id,data:t})}async onTileMessage(e,r,i){this._tileKeyToFeatureSets.has(e.key.id)||this._tileKeyToFeatureSets.set(e.key.id,new Map);const a=this._tileKeyToFeatureSets.get(e.key.id);if(a&&t(r.addOrUpdate)&&r.addOrUpdate.hasFeatures&&a.set(r.addOrUpdate.instance,r),r.end){const t=[],r=c(e);this._tileKeyToFeatureSets.forEach(((i,o)=>{if(o===e.key.id)i.forEach((e=>s(e.addOrUpdate,(e=>t.push(e)))));else if(r.has(o)){const e=r.get(o),[a,n]=e.offset;i.forEach((e=>s(e.addOrUpdate,(e=>{const s=e.transform(a,n,1,1);t.push(s)}))))}}));const a=o(t,this._schema.mesh,512,512),n={tileKey:e.key.id,intensityInfo:a},d=[a.matrix];return this.remoteClient.invoke(\"tileRenderer.onTileData\",n,{...i,transferList:d})}}onTileError(e,t,s){return this.remoteClient.invoke(\"tileRenderer.onTileError\",{tileKey:e.id,error:t},s)}};l=e([r(\"esri.views.2d.layers.features.processors.HeatmapProcessor\")],l);const h=l;export{h as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIovB,IAAMA,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE,GAAE;AAAC,SAAK,SAAOA,IAAE,KAAK,SAAO;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAM,IAAEA,GAAE,KAAIC,KAAE,oBAAI,OAAIC,KAAE,KAAI,IAAEC,IAAEA,KAAEH,GAAE,aAAa,SAAS;AAAY,SAAOC,GAAE,IAAIE,GAAE,GAAE,IAAG,IAAGA,EAAC,EAAE,IAAG,IAAIJ,GAAE,CAAC,CAAC,GAAE,CAAC,CAAC,GAAE,CAAC,IAAEG,IAAE,IAAEA,IAAE,GAAE,CAAC,CAAC,CAAC,GAAED,GAAE,IAAIE,GAAE,GAAE,GAAE,IAAGA,EAAC,EAAE,IAAG,IAAIJ,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,IAAEG,IAAE,GAAE,CAAC,CAAC,CAAC,GAAED,GAAE,IAAIE,GAAE,GAAE,GAAE,IAAGA,EAAC,EAAE,IAAG,IAAIJ,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,IAAEG,IAAEA,IAAE,CAAC,CAAC,CAAC,GAAED,GAAE,IAAIE,GAAE,GAAE,IAAG,GAAEA,EAAC,EAAE,IAAG,IAAIJ,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,IAAEG,IAAE,GAAE,GAAE,CAAC,CAAC,CAAC,GAAED,GAAE,IAAIE,GAAE,GAAE,GAAE,GAAEA,EAAC,EAAE,IAAG,IAAIJ,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAEG,IAAE,CAAC,CAAC,CAAC,GAAED,GAAE,IAAIE,GAAE,GAAE,IAAG,GAAEA,EAAC,EAAE,IAAG,IAAIJ,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,IAAEG,IAAE,GAAE,GAAEA,EAAC,CAAC,CAAC,GAAED,GAAE,IAAIE,GAAE,GAAE,GAAE,GAAEA,EAAC,EAAE,IAAG,IAAIJ,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAEG,EAAC,CAAC,CAAC,GAAED,GAAE,IAAIE,GAAE,GAAE,GAAE,GAAEA,EAAC,EAAE,IAAG,IAAIJ,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAEG,IAAEA,EAAC,CAAC,CAAC,GAAED;AAAC;AAAC,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,WAAU,KAAK,wBAAsB,oBAAI;AAAA,EAAG;AAAA,EAAC,aAAY;AAAC,SAAK,QAAQ,IAAI,CAAC,KAAK,UAAU,GAAG,UAAS,KAAK,aAAa,KAAK,IAAI,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOD,IAAE,GAAE;AAAC,UAAMC,KAAE,EAAE,OAAO,WAAW,CAAC;AAAE,QAAG,cAAYA,GAAE,KAAK;AAAO,MAAE,KAAK,SAAQA,EAAC,MAAID,GAAE,OAAK,MAAG,KAAK,UAAQC;AAAA,EAAE;AAAA,EAAC,aAAaD,IAAE;AAAC,eAAU,KAAKA,GAAE,QAAQ,MAAK,sBAAsB,OAAO,EAAE,IAAI,EAAE;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAM,IAAE,EAAC,OAAM,KAAE;AAAE,WAAO,KAAK,sBAAsB,OAAOA,GAAE,IAAI,EAAE,GAAE,KAAK,aAAa,OAAO,2BAA0B,EAAC,SAAQA,GAAE,IAAG,MAAK,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcA,IAAEE,IAAE,GAAE;AAAC,SAAK,sBAAsB,IAAIF,GAAE,IAAI,EAAE,KAAG,KAAK,sBAAsB,IAAIA,GAAE,IAAI,IAAG,oBAAI,KAAG;AAAE,UAAMI,KAAE,KAAK,sBAAsB,IAAIJ,GAAE,IAAI,EAAE;AAAE,QAAGI,MAAG,EAAEF,GAAE,WAAW,KAAGA,GAAE,YAAY,eAAaE,GAAE,IAAIF,GAAE,YAAY,UAASA,EAAC,GAAEA,GAAE,KAAI;AAAC,YAAM,IAAE,CAAC,GAAEA,KAAE,EAAEF,EAAC;AAAE,WAAK,sBAAsB,QAAS,CAACK,IAAEF,OAAI;AAAC,YAAGA,OAAIH,GAAE,IAAI,GAAG,CAAAK,GAAE,QAAS,CAAAL,OAAG,EAAEA,GAAE,aAAa,CAAAA,OAAG,EAAE,KAAKA,EAAC,CAAE,CAAE;AAAA,iBAAUE,GAAE,IAAIC,EAAC,GAAE;AAAC,gBAAMH,KAAEE,GAAE,IAAIC,EAAC,GAAE,CAACC,IAAEE,EAAC,IAAEN,GAAE;AAAO,UAAAK,GAAE,QAAS,CAAAL,OAAG,EAAEA,GAAE,aAAa,CAAAA,OAAG;AAAC,kBAAMC,KAAED,GAAE,UAAUI,IAAEE,IAAE,GAAE,CAAC;AAAE,cAAE,KAAKL,EAAC;AAAA,UAAC,CAAE,CAAE;AAAA,QAAC;AAAA,MAAC,CAAE;AAAE,YAAMG,KAAE,EAAE,GAAE,KAAK,QAAQ,MAAK,KAAI,GAAG,GAAE,IAAE,EAAC,SAAQJ,GAAE,IAAI,IAAG,eAAcI,GAAC,GAAE,IAAE,CAACA,GAAE,MAAM;AAAE,aAAO,KAAK,aAAa,OAAO,2BAA0B,GAAE,EAAC,GAAG,GAAE,cAAa,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAYJ,IAAE,GAAEC,IAAE;AAAC,WAAO,KAAK,aAAa,OAAO,4BAA2B,EAAC,SAAQD,GAAE,IAAG,OAAM,EAAC,GAAEC,EAAC;AAAA,EAAC;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,2DAA2D,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["p", "e", "s", "r", "o", "a", "i", "n"]}