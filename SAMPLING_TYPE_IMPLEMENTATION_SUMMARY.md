# 采样记录Type参数实现总结

## 概述
参照assay（化验记录）下的type类型实现，为sampling（采样记录）页面新增type参数，用于区分不同类型的采样记录。

## Type值分配规则
根据现有assay实现的type值，采样记录使用相同的type值：

- **type = '0'**: 水源 (waterSource)
- **type = '1'**: 供水厂 (waterSupplyPlant) 
- **type = '2'**: 污水厂 (sewagePlant)
- **type = '3'**: 管网监测 (pipeNetworkMonitoring)

## 后端修改

### 1. 实体类修改
**文件**: `water-guazhou-plat/dao/src/main/java/org/thingsboard/server/dao/model/sql/sampling/Sampling.java`
- 添加type字段：
```java
@ApiModelProperty(value = "类型")
private String type;
```

### 2. 数据库表结构修改
**文件**: `water-guazhou-plat/dao/src/main/resources/sql/schema-sampling.sql`
- 为tb_water_sampling表添加type字段：
```sql
type VARCHAR(50)
```

### 3. Mapper XML修改
**文件**: `water-guazhou-plat/application/src/main/resources/mapper/waterSource/SamplingMapper.xml`
- 在SELECT语句中添加type字段查询
- 在WHERE条件中添加type参数过滤：
```xml
<if test="params.type != null and params.type != ''">
    AND s.type = #{params.type}
</if>
```

### 4. 数据库迁移脚本
**文件**: `water-guazhou-plat/dao/src/main/resources/sql/migration/add_type_to_sampling.sql`
- 创建迁移脚本为现有表添加type字段

## 前端修改

### 1. API接口类型定义
**文件**: `water-guazhou-ui/src/api/waterSource/sampling.ts`
- 在saveSamplingRecord和updateSamplingRecord的数据类型中添加type字段

### 2. 各模块页面修改

#### 污水厂采样 (type = '2')
**文件**: `water-guazhou-ui/src/views/sewagePlant/sampling/index.vue`
- queryParams中添加type: '2'
- form中添加type: '2'
- getList方法中传递type: '2'
- submitForm方法中设置type: '2'
- resetForm方法中重置type: '2'

#### 水源采样 (type = '0')
**文件**: `water-guazhou-ui/src/views/waterSource/sampling/index.vue`
- 同污水厂，但使用type: '0'

#### 供水厂采样 (type = '1')
**文件**: `water-guazhou-ui/src/views/waterSupplyPlant/sampling/index.vue`
- 同污水厂，但使用type: '1'

#### 管网监测采样 (type = '3')
**文件**: `water-guazhou-ui/src/views/pipeNetworkMonitoring/waterQualityManagement/sampling/index.vue`
- 同污水厂，但使用type: '3'

## 实现细节

### 查询参数处理
每个页面的getList方法都会在查询参数中添加对应的type值：
```javascript
const params: any = {
  pageNum: queryParams.pageNum,
  pageSize: queryParams.pageSize,
  samplingLocation: queryParams.samplingLocation,
  samplingPerson: queryParams.samplingPerson,
  type: 'X' // X为对应模块的type值
}
```

### 表单提交处理
每个页面的submitForm方法都会在提交数据中设置type值：
```javascript
const submitData = {
  ...form,
  samplingTime: form.samplingTime ? String(form.samplingTime) : String(Date.now()),
  recordFile: typeof form.recordFile === 'string' ? form.recordFile : '',
  type: 'X' // X为对应模块的type值
}
```

### 表单重置处理
每个页面的resetForm方法都会重置type值：
```javascript
Object.assign(form, {
  id: '',
  samplingLocation: '',
  samplingPerson: '',
  samplingMethod: '',
  sampleNumber: '',
  sampleType: '',
  samplingTime: undefined,
  recordFile: '',
  remark: '',
  type: 'X' // X为对应模块的type值
})
```

## 数据隔离效果
通过type参数的实现，不同模块的采样记录将被完全隔离：
- 污水厂页面只显示type=2的采样记录
- 供水厂页面只显示type=1的采样记录  
- 水源页面只显示type=0的采样记录
- 管网监测页面只显示type=3的采样记录

## 注意事项
1. 所有type值都使用字符串类型，保持与assay实现的一致性
2. 后端Mapper XML中的type查询条件确保了数据的正确过滤
3. 前端每个操作（查询、新增、编辑、重置）都正确设置了type值
4. 数据库迁移脚本确保现有数据的兼容性
