import {
  y as y2
} from "./chunk-FIVMDF4P.js";
import {
  _,
  f,
  g,
  u as u2,
  w as w2,
  x
} from "./chunk-Y4E3DGVA.js";
import {
  a2
} from "./chunk-PNIF6I3E.js";
import {
  u
} from "./chunk-D7S3BWBP.js";
import {
  C,
  w
} from "./chunk-ETY52UBV.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  o as o2
} from "./chunk-G5KX4JSG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/support/LabelExpressionInfo.js
var l2;
var n = l2 = class extends l {
  constructor() {
    super(...arguments), this.expression = null, this.title = null, this.value = null;
  }
  readExpression(r2, e2) {
    return e2.value ? w2(e2.value) : r2;
  }
  writeExpression(r2, e2, s2) {
    null != this.value && (r2 = w2(this.value)), null != r2 && (e2[s2] = r2);
  }
  clone() {
    return new l2({ expression: this.expression, title: this.title, value: this.value });
  }
};
e([y({ type: String, json: { write: { writerEnsuresNonNull: true } } })], n.prototype, "expression", void 0), e([o("expression", ["expression", "value"])], n.prototype, "readExpression", null), e([r("expression")], n.prototype, "writeExpression", null), e([y({ type: String, json: { write: true, origins: { "web-scene": { write: false } } } })], n.prototype, "title", void 0), e([y({ json: { read: false, write: false } })], n.prototype, "value", void 0), n = l2 = e([a("esri.layers.support.LabelExpressionInfo")], n);
var a3 = n;

// node_modules/@arcgis/core/layers/support/LabelClass.js
var h;
var P = new s({ esriServerPointLabelPlacementAboveCenter: "above-center", esriServerPointLabelPlacementAboveLeft: "above-left", esriServerPointLabelPlacementAboveRight: "above-right", esriServerPointLabelPlacementBelowCenter: "below-center", esriServerPointLabelPlacementBelowLeft: "below-left", esriServerPointLabelPlacementBelowRight: "below-right", esriServerPointLabelPlacementCenterCenter: "center-center", esriServerPointLabelPlacementCenterLeft: "center-left", esriServerPointLabelPlacementCenterRight: "center-right", esriServerLinePlacementAboveAfter: "above-after", esriServerLinePlacementAboveAlong: "above-along", esriServerLinePlacementAboveBefore: "above-before", esriServerLinePlacementAboveStart: "above-start", esriServerLinePlacementAboveEnd: "above-end", esriServerLinePlacementBelowAfter: "below-after", esriServerLinePlacementBelowAlong: "below-along", esriServerLinePlacementBelowBefore: "below-before", esriServerLinePlacementBelowStart: "below-start", esriServerLinePlacementBelowEnd: "below-end", esriServerLinePlacementCenterAfter: "center-after", esriServerLinePlacementCenterAlong: "center-along", esriServerLinePlacementCenterBefore: "center-before", esriServerLinePlacementCenterStart: "center-start", esriServerLinePlacementCenterEnd: "center-end", esriServerPolygonPlacementAlwaysHorizontal: "always-horizontal" }, { ignoreUnknown: true });
function x2(e2, r2, t) {
  return { enabled: !u(t == null ? void 0 : t.layer) };
}
function L(e2) {
  var _a;
  return !e2 || "service" !== e2.origin && !("map-image" === ((_a = e2.layer) == null ? void 0 : _a.type));
}
function g2(e2) {
  return "map-image" === (e2 == null ? void 0 : e2.type);
}
function E(e2) {
  var _a, _b;
  return !!g2(e2) && !!((_b = (_a = e2.capabilities) == null ? void 0 : _a.exportMap) == null ? void 0 : _b.supportsArcadeExpressionForLabeling);
}
function j(e2) {
  return L(e2) || E(e2 == null ? void 0 : e2.layer);
}
var A = h = class extends l {
  static evaluateWhere(e2, r2) {
    const t = (e3, r3, t2) => {
      switch (r3) {
        case "=":
          return e3 == t2;
        case "<>":
          return e3 != t2;
        case ">":
          return e3 > t2;
        case ">=":
          return e3 >= t2;
        case "<":
          return e3 < t2;
        case "<=":
          return e3 <= t2;
      }
      return false;
    };
    try {
      if (null == e2) return true;
      const o3 = e2.split(" ");
      if (3 === o3.length) return t(r2[o3[0]], o3[1], o3[2]);
      if (7 === o3.length) {
        const e3 = t(r2[o3[0]], o3[1], o3[2]), i = o3[3], n2 = t(r2[o3[4]], o3[5], o3[6]);
        switch (i) {
          case "AND":
            return e3 && n2;
          case "OR":
            return e3 || n2;
        }
      }
      return false;
    } catch (o3) {
      console.log("Error.: can't parse = " + e2);
    }
  }
  constructor(e2) {
    super(e2), this.type = "label", this.name = null, this.allowOverrun = false, this.deconflictionStrategy = "static", this.labelExpression = null, this.labelExpressionInfo = null, this.labelPlacement = null, this.labelPosition = "curved", this.maxScale = 0, this.minScale = 0, this.repeatLabel = true, this.repeatLabelDistance = null, this.symbol = y2, this.useCodedValues = void 0, this.where = null;
  }
  readLabelExpression(e2, r2) {
    const t = r2.labelExpressionInfo;
    if (!t || !t.value && !t.expression) return e2;
  }
  writeLabelExpression(e2, r2, t) {
    if (this.labelExpressionInfo) {
      if (null != this.labelExpressionInfo.value) e2 = u2(this.labelExpressionInfo.value);
      else if (null != this.labelExpressionInfo.expression) {
        const r3 = _(this.labelExpressionInfo.expression);
        r3 && (e2 = "[" + r3 + "]");
      }
    }
    null != e2 && (r2[t] = e2);
  }
  writeLabelExpressionInfo(e2, r2, t, o3) {
    if (null == e2 && null != this.labelExpression && L(o3)) e2 = new a3({ expression: this.getLabelExpressionArcade() });
    else if (!e2) return;
    const i = e2.toJSON(o3);
    i.expression && (r2[t] = i);
  }
  writeMaxScale(e2, r2) {
    (e2 || this.minScale) && (r2.maxScale = e2);
  }
  writeMinScale(e2, r2) {
    (e2 || this.maxScale) && (r2.minScale = e2);
  }
  getLabelExpression() {
    return x(this);
  }
  getLabelExpressionArcade() {
    return f(this);
  }
  getLabelExpressionSingleField() {
    return g(this);
  }
  hash() {
    return JSON.stringify(this);
  }
  clone() {
    return new h({ allowOverrun: this.allowOverrun, deconflictionStrategy: this.deconflictionStrategy, labelExpression: this.labelExpression, labelExpressionInfo: p(this.labelExpressionInfo), labelPosition: this.labelPosition, labelPlacement: this.labelPlacement, maxScale: this.maxScale, minScale: this.minScale, name: this.name, repeatLabel: this.repeatLabel, repeatLabelDistance: this.repeatLabelDistance, symbol: p(this.symbol), where: this.where, useCodedValues: this.useCodedValues });
  }
};
e([y({ type: String, json: { write: true } })], A.prototype, "name", void 0), e([y({ type: Boolean, json: { write: true, default: false, origins: { "web-scene": { write: false }, "portal-item": { default: false, write: { overridePolicy: x2 } } } } })], A.prototype, "allowOverrun", void 0), e([y({ type: String, json: { write: true, default: "static", origins: { "web-scene": { write: false }, "portal-item": { default: "static", write: { overridePolicy: x2 } } } } })], A.prototype, "deconflictionStrategy", void 0), e([y({ type: String, json: { write: { overridePolicy(e2, r2, t) {
  return this.labelExpressionInfo && "service" === (t == null ? void 0 : t.origin) && E(t.layer) ? { enabled: false } : { allowNull: true };
} } } })], A.prototype, "labelExpression", void 0), e([o("labelExpression")], A.prototype, "readLabelExpression", null), e([r("labelExpression")], A.prototype, "writeLabelExpression", null), e([y({ type: a3, json: { write: { overridePolicy: (e2, r2, t) => j(t) ? { allowNull: true } : { enabled: false } } } })], A.prototype, "labelExpressionInfo", void 0), e([r("labelExpressionInfo")], A.prototype, "writeLabelExpressionInfo", null), e([y({ type: P.apiValues, json: { type: P.jsonValues, read: P.read, write: P.write } })], A.prototype, "labelPlacement", void 0), e([y({ type: ["curved", "parallel"], json: { write: true, origins: { "web-map": { write: false }, "web-scene": { write: false }, "portal-item": { write: false } } } })], A.prototype, "labelPosition", void 0), e([y({ type: Number })], A.prototype, "maxScale", void 0), e([r("maxScale")], A.prototype, "writeMaxScale", null), e([y({ type: Number })], A.prototype, "minScale", void 0), e([r("minScale")], A.prototype, "writeMinScale", null), e([y({ type: Boolean, json: { write: true, origins: { "web-scene": { write: false }, "portal-item": { write: { overridePolicy: x2 } } } } })], A.prototype, "repeatLabel", void 0), e([y({ type: Number, cast: o2, json: { write: true, origins: { "web-scene": { write: false }, "portal-item": { write: { overridePolicy: x2 } } } } })], A.prototype, "repeatLabelDistance", void 0), e([y({ types: C, json: { origins: { "web-scene": { types: w, write: a2, default: null } }, write: a2, default: null } })], A.prototype, "symbol", void 0), e([y({ type: Boolean, json: { write: true } })], A.prototype, "useCodedValues", void 0), e([y({ type: String, json: { write: true } })], A.prototype, "where", void 0), A = h = e([a("esri.layers.support.LabelClass")], A);
var C2 = A;

export {
  C2 as C
};
//# sourceMappingURL=chunk-Q7K3J54I.js.map
