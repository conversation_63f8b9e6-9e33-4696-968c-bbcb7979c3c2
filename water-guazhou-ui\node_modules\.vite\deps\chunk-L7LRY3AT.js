import {
  v
} from "./chunk-ZJC3GHA7.js";
import {
  d as d2
} from "./chunk-4FIRBBKR.js";
import {
  a as a2
} from "./chunk-2WMCP27R.js";
import {
  n as n2,
  p as p2
} from "./chunk-UVJUTW2U.js";
import {
  C,
  b
} from "./chunk-HTXGAKOK.js";
import {
  r as r4
} from "./chunk-JEANRG5Q.js";
import {
  c as c2
} from "./chunk-ZVU4V5QV.js";
import {
  x
} from "./chunk-W3CLOCDX.js";
import {
  i as i2,
  n
} from "./chunk-NZB6EMKN.js";
import {
  D,
  I,
  d,
  f as f2
} from "./chunk-VJW7RCN7.js";
import {
  b as b2
} from "./chunk-D7S3BWBP.js";
import {
  c
} from "./chunk-MQ2IOGEF.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  r as r3
} from "./chunk-WXFAAYJL.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  f2 as f,
  le,
  me
} from "./chunk-JXLVNWKF.js";
import {
  s as s4
} from "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s as s3
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  S
} from "./chunk-HP475EI3.js";
import {
  t2 as t
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/form/ExpressionInfo.js
var s5;
var p3 = s5 = class extends l {
  constructor(e4) {
    super(e4), this.expression = null, this.name = null, this.returnType = "boolean", this.title = null;
  }
  clone() {
    return new s5({ name: this.name, title: this.title, expression: this.expression, returnType: this.returnType });
  }
};
e([y({ type: String, json: { write: true } })], p3.prototype, "expression", void 0), e([y({ type: String, json: { write: true } })], p3.prototype, "name", void 0), e([y({ type: ["boolean", "date", "number", "string"], json: { write: true } })], p3.prototype, "returnType", void 0), e([y({ type: String, json: { write: true } })], p3.prototype, "title", void 0), p3 = s5 = e([a("esri.form.ExpressionInfo")], p3);
var i3 = p3;

// node_modules/@arcgis/core/form/elements/Element.js
var e2 = class extends l {
  constructor(t2) {
    super(t2), this.description = null, this.label = null, this.type = null, this.visibilityExpression = null;
  }
};
e([y({ type: String, json: { write: true } })], e2.prototype, "description", void 0), e([y({ type: String, json: { write: true } })], e2.prototype, "label", void 0), e([y()], e2.prototype, "type", void 0), e([y({ type: String, json: { write: true } })], e2.prototype, "visibilityExpression", void 0), e2 = e([a("esri.form.elements.Element")], e2);
var i4 = e2;

// node_modules/@arcgis/core/form/elements/inputs/AttachmentInput.js
var s6;
var p4 = s6 = class extends l {
  constructor(t2) {
    super(t2), this.type = null;
  }
  clone() {
    return new s6({ type: this.type });
  }
};
e([y({ type: ["attachment", "audio", "document", "image", "signature", "video"], json: { write: true } })], p4.prototype, "type", void 0), p4 = s6 = e([a("esri.form.elements.inputs.AttachmentInput")], p4);
var c3 = p4;

// node_modules/@arcgis/core/form/elements/AttachmentElement.js
var i5;
var p5 = i5 = class extends i4 {
  constructor(t2) {
    super(t2), this.attachmentKeyword = null, this.editable = true, this.input = null, this.type = "attachment";
  }
  clone() {
    return new i5({ attachmentKeyword: this.attachmentKeyword, description: this.description, editable: this.editable, input: this.input, label: this.label, visibilityExpression: this.visibilityExpression });
  }
};
e([y({ type: String, json: { write: true } })], p5.prototype, "attachmentKeyword", void 0), e([y({ type: Boolean, json: { write: true } })], p5.prototype, "editable", void 0), e([y({ type: c3, json: { read: { source: "inputType" }, write: { target: "inputType" } } })], p5.prototype, "input", void 0), e([y({ type: ["attachment"], json: { read: false, write: true } })], p5.prototype, "type", void 0), p5 = i5 = e([a("esri.form.elements.AttachmentElement")], p5);
var n3 = p5;

// node_modules/@arcgis/core/form/elements/inputs/Input.js
var e3 = class extends l {
  constructor(r5) {
    super(r5), this.type = null;
  }
};
e([y()], e3.prototype, "type", void 0), e3 = e([a("esri.form.elements.inputs.Input")], e3);
var p6 = e3;

// node_modules/@arcgis/core/form/elements/inputs/TextInput.js
var s7 = class extends p6 {
  constructor(r5) {
    super(r5), this.maxLength = null, this.minLength = 0;
  }
};
e([y({ type: Number, json: { write: true } })], s7.prototype, "maxLength", void 0), e([y({ type: Number, json: { write: true } })], s7.prototype, "minLength", void 0), s7 = e([a("esri.form.elements.inputs.TextInput")], s7);
var p7 = s7;

// node_modules/@arcgis/core/form/elements/inputs/BarcodeScannerInput.js
var o2;
var c4 = o2 = class extends p7 {
  constructor(r5) {
    super(r5), this.type = "barcode-scanner";
  }
  clone() {
    return new o2({ maxLength: this.maxLength, minLength: this.minLength });
  }
};
e([y({ type: ["barcode-scanner"], json: { read: false, write: true } })], c4.prototype, "type", void 0), c4 = o2 = e([a("esri.form.elements.inputs.BarcodeScannerInput")], c4);
var n4 = c4;

// node_modules/@arcgis/core/form/elements/inputs/ComboBoxInput.js
var s8;
var p8 = s8 = class extends p6 {
  constructor(o4) {
    super(o4), this.noValueOptionLabel = null, this.showNoValueOption = true, this.type = "combo-box";
  }
  clone() {
    return new s8({ showNoValueOption: this.showNoValueOption, noValueOptionLabel: this.noValueOptionLabel });
  }
};
e([y({ type: String, json: { write: true } })], p8.prototype, "noValueOptionLabel", void 0), e([y({ type: Boolean, json: { write: true } })], p8.prototype, "showNoValueOption", void 0), e([y({ type: ["combo-box"], json: { read: false, write: true } })], p8.prototype, "type", void 0), p8 = s8 = e([a("esri.form.elements.inputs.ComboBoxInput")], p8);
var i6 = p8;

// node_modules/@arcgis/core/form/elements/inputs/DateTimePickerInput.js
var s9;
function n5(e4) {
  return null != e4 ? new Date(e4) : null;
}
function a3(e4) {
  return e4 ? e4.getTime() : null;
}
var m = s9 = class extends p6 {
  constructor(e4) {
    super(e4), this.includeTime = false, this.max = null, this.min = null, this.type = "datetime-picker";
  }
  readMax(e4, r5) {
    return n5(r5.max);
  }
  writeMax(e4, r5) {
    r5.max = a3(e4);
  }
  readMin(e4, r5) {
    return n5(r5.min);
  }
  writeMin(e4, r5) {
    r5.min = a3(e4);
  }
  clone() {
    return new s9({ includeTime: this.includeTime, max: this.max, min: this.min });
  }
};
e([y({ type: Boolean, json: { write: true } })], m.prototype, "includeTime", void 0), e([y({ type: Date, json: { type: Number, write: true } })], m.prototype, "max", void 0), e([o("max")], m.prototype, "readMax", null), e([r2("max")], m.prototype, "writeMax", null), e([y({ type: Date, json: { type: Number, write: true } })], m.prototype, "min", void 0), e([o("min")], m.prototype, "readMin", null), e([r2("min")], m.prototype, "writeMin", null), e([y({ type: ["datetime-picker"], json: { read: false, write: true } })], m.prototype, "type", void 0), m = s9 = e([a("esri.form.elements.inputs.DateTimePickerInput")], m);
var c5 = m;

// node_modules/@arcgis/core/form/elements/inputs/RadioButtonsInput.js
var s10;
var p9 = s10 = class extends p6 {
  constructor(o4) {
    super(o4), this.noValueOptionLabel = null, this.showNoValueOption = true, this.type = "radio-buttons";
  }
  clone() {
    return new s10({ noValueOptionLabel: this.noValueOptionLabel, showNoValueOption: this.showNoValueOption });
  }
};
e([y({ type: String, json: { write: true } })], p9.prototype, "noValueOptionLabel", void 0), e([y({ type: Boolean, json: { write: true } })], p9.prototype, "showNoValueOption", void 0), e([y({ type: ["radio-buttons"], json: { read: false, write: true } })], p9.prototype, "type", void 0), p9 = s10 = e([a("esri.form.elements.inputs.RadioButtonsInput")], p9);
var i7 = p9;

// node_modules/@arcgis/core/form/elements/inputs/SwitchInput.js
var s11;
var p10 = s11 = class extends p6 {
  constructor(o4) {
    super(o4), this.offValue = null, this.onValue = null, this.type = "switch";
  }
  clone() {
    return new s11({ offValue: this.offValue, onValue: this.onValue });
  }
};
e([y({ type: [String, Number], json: { write: true } })], p10.prototype, "offValue", void 0), e([y({ type: [String, Number], json: { write: true } })], p10.prototype, "onValue", void 0), e([y({ type: ["switch"], json: { read: false, write: true } })], p10.prototype, "type", void 0), p10 = s11 = e([a("esri.form.elements.inputs.SwitchInput")], p10);
var i8 = p10;

// node_modules/@arcgis/core/form/elements/inputs/TextAreaInput.js
var o3;
var p11 = o3 = class extends p7 {
  constructor(t2) {
    super(t2), this.type = "text-area";
  }
  clone() {
    return new o3({ maxLength: this.maxLength, minLength: this.minLength });
  }
};
e([y({ type: ["text-area"], json: { read: false, write: true } })], p11.prototype, "type", void 0), p11 = o3 = e([a("esri.form.elements.inputs.TextAreaInput")], p11);
var a4 = p11;

// node_modules/@arcgis/core/form/elements/inputs/TextBoxInput.js
var s12;
var p12 = s12 = class extends p7 {
  constructor(t2) {
    super(t2), this.type = "text-box";
  }
  clone() {
    return new s12({ maxLength: this.maxLength, minLength: this.minLength });
  }
};
e([y({ type: ["text-box"], json: { read: false, write: true } })], p12.prototype, "type", void 0), p12 = s12 = e([a("esri.form.elements.inputs.TextBoxInput")], p12);
var c6 = p12;

// node_modules/@arcgis/core/form/elements/support/inputs.js
var m2 = { base: p6, key: "type", typeMap: { "barcode-scanner": n4, "combo-box": i6, "datetime-picker": c5, "radio-buttons": i7, switch: i8, "text-area": a4, "text-box": c6 } };

// node_modules/@arcgis/core/form/elements/FieldElement.js
var a5;
var d3 = "esri.form.elements.FieldElement";
var u = s.getLogger(d3);
var m3 = a5 = class extends i4 {
  constructor(e4) {
    super(e4), this.domain = null, this.editableExpression = null, this.fieldName = null, this.hint = null, this.input = null, this.requiredExpression = null, this.type = "field", this.valueExpression = null;
  }
  get editable() {
    return t(u, "editable", { replacement: "editableExpression", version: "4.26", warnOnce: true }), this._get("editable") ?? true;
  }
  set editable(e4) {
    t(u, "editable", { replacement: "editableExpression", version: "4.26", warnOnce: true }), this._set("editable", e4);
  }
  clone() {
    return new a5({ description: this.description, domain: this.domain, editable: this.editable, editableExpression: this.editableExpression, fieldName: this.fieldName, hint: this.hint, input: this.input, label: this.label, requiredExpression: this.requiredExpression, valueExpression: this.valueExpression, visibilityExpression: this.visibilityExpression });
  }
};
e([y({ types: n, json: { read: { reader: i2 }, write: true } })], m3.prototype, "domain", void 0), e([y({ type: Boolean, json: { write: true } })], m3.prototype, "editable", null), e([y({ type: String, json: { write: true } })], m3.prototype, "editableExpression", void 0), e([y({ type: String, json: { write: true } })], m3.prototype, "fieldName", void 0), e([y({ type: String, json: { write: true } })], m3.prototype, "hint", void 0), e([y({ types: m2, json: { read: { source: "inputType" }, write: { target: "inputType" } } })], m3.prototype, "input", void 0), e([y({ type: String, json: { write: true } })], m3.prototype, "requiredExpression", void 0), e([y({ type: String, json: { read: false, write: true } })], m3.prototype, "type", void 0), e([y({ type: String, json: { write: true } })], m3.prototype, "valueExpression", void 0), m3 = a5 = e([a(d3)], m3);
var c7 = m3;

// node_modules/@arcgis/core/form/elements/RelationshipElement.js
var p13;
var l2 = p13 = class extends i4 {
  constructor(e4) {
    super(e4), this.displayCount = null, this.displayType = "list", this.editable = true, this.orderByFields = null, this.relationshipId = null, this.type = "relationship";
  }
  clone() {
    return new p13({ description: this.description, displayCount: this.displayCount, displayType: this.displayType, editable: this.editable, label: this.label, orderByFields: p(this.orderByFields), relationshipId: this.relationshipId, visibilityExpression: this.visibilityExpression });
  }
};
e([y({ type: Number, json: { write: true } })], l2.prototype, "displayCount", void 0), e([y({ type: ["list"], json: { write: true } })], l2.prototype, "displayType", void 0), e([y({ type: Boolean, json: { write: true } })], l2.prototype, "editable", void 0), e([y({ type: [c], json: { write: true } })], l2.prototype, "orderByFields", void 0), e([y({ type: Number, json: { write: true } })], l2.prototype, "relationshipId", void 0), e([y({ type: ["relationship"], json: { read: false, write: true } })], l2.prototype, "type", void 0), l2 = p13 = e([a("esri.form.elements.RelationshipElement")], l2);
var d4 = l2;

// node_modules/@arcgis/core/form/support/elements.js
function n6(t2) {
  return { typesWithGroup: { base: i4, key: "type", typeMap: { attachment: n3, field: c7, group: t2, relationship: d4 } }, typesWithoutGroup: { base: i4, key: "type", typeMap: { attachment: n3, field: c7, relationship: d4 } } };
}
function i9(t2, e4, p18 = true) {
  if (!t2) return null;
  const r5 = p18 ? e4.typesWithGroup.typeMap : e4.typesWithoutGroup.typeMap;
  return t2.filter((t3) => r5[t3.type]).map((t3) => r5[t3.type].fromJSON(t3));
}
function u2(t2, e4, p18 = true) {
  if (!t2) return null;
  const r5 = p18 ? e4.typesWithGroup.typeMap : e4.typesWithoutGroup.typeMap;
  return t2.filter((t3) => r5[t3.type]).map((t3) => t3.toJSON());
}
function s13(e4, p18, r5 = true) {
  return e4 ? e4.map((e5) => S(r5 ? p18.typesWithGroup : p18.typesWithoutGroup, e5)) : null;
}

// node_modules/@arcgis/core/form/elements/GroupElement.js
var d5;
var u3 = d5 = class extends i4 {
  constructor(e4) {
    super(e4), this.elements = null, this.initialState = "expanded", this.type = "group";
  }
  castElements(e4) {
    return s13(e4, f3, false);
  }
  readElements(e4, t2) {
    return i9(t2.formElements, f3, false);
  }
  writeElements(e4, t2) {
    t2.formElements = u2(e4, f3, false);
  }
  clone() {
    return new d5({ description: this.description, elements: p(this.elements), initialState: this.initialState, label: this.label, visibilityExpression: this.visibilityExpression });
  }
};
e([y({ json: { write: true } })], u3.prototype, "elements", void 0), e([s4("elements")], u3.prototype, "castElements", null), e([o("elements", ["formElements"])], u3.prototype, "readElements", null), e([r2("elements")], u3.prototype, "writeElements", null), e([y({ type: ["collapsed", "expanded"], json: { write: true } })], u3.prototype, "initialState", void 0), e([y({ type: String, json: { read: false, write: true } })], u3.prototype, "type", void 0), u3 = d5 = e([a("esri.form.elements.GroupElement")], u3);
var f3 = n6(u3);
var y2 = u3;

// node_modules/@arcgis/core/form/FormTemplate.js
var f4;
var j2 = n6(y2);
var h = f4 = class extends l {
  constructor(e4) {
    super(e4), this.description = null, this.elements = null, this.expressionInfos = null, this.preserveFieldValuesWhenHidden = false, this.title = null;
  }
  castElements(e4) {
    return s13(e4, j2);
  }
  readElements(e4, t2) {
    return i9(t2.formElements, j2);
  }
  writeElements(e4, t2) {
    t2.formElements = u2(e4, j2);
  }
  clone() {
    return new f4({ description: this.description, expressionInfos: p(this.expressionInfos), elements: p(this.elements), title: this.title, preserveFieldValuesWhenHidden: this.preserveFieldValuesWhenHidden });
  }
};
e([y({ type: String, json: { write: true } })], h.prototype, "description", void 0), e([y({ json: { write: true } })], h.prototype, "elements", void 0), e([s4("elements")], h.prototype, "castElements", null), e([o("elements", ["formElements"])], h.prototype, "readElements", null), e([r2("elements")], h.prototype, "writeElements", null), e([y({ type: [i3], json: { write: true } })], h.prototype, "expressionInfos", void 0), e([y({ type: Boolean, json: { default: false, write: true } })], h.prototype, "preserveFieldValuesWhenHidden", void 0), e([y({ type: String, json: { write: true } })], h.prototype, "title", void 0), h = f4 = e([a("esri.form.FormTemplate")], h);
var y3 = h;

// node_modules/@arcgis/core/layers/support/featureLayerUtils.js
var c8 = new s3({ esriGeometryPoint: "point", esriGeometryMultipoint: "multipoint", esriGeometryPolyline: "polyline", esriGeometryPolygon: "polygon", esriGeometryMultiPatch: "multipatch" });
async function p14(t2, r5, n8, o4) {
  const a8 = await E(t2);
  if (await l3(t2, r5, o4), !a8.addAttachment) throw new s2(o4, "Layer source does not support addAttachment capability");
  return a8.addAttachment(r5, n8);
}
function l3(t2, r5, n8) {
  const { attributes: o4 } = r5, { objectIdField: a8 } = t2;
  return t2.get("capabilities.data.supportsAttachment") ? r5 ? o4 ? a8 && o4[a8] ? Promise.resolve() : Promise.reject(new s2(n8, `feature is missing the identifying attribute ${a8}`)) : Promise.reject(new s2(n8, "'attributes' are required on a feature to query attachments")) : Promise.reject(new s2(n8, "A feature is required to add/delete/update attachments")) : Promise.reject(new s2(n8, "this layer doesn't support attachments"));
}
async function y4(t2, r5, n8, o4, a8) {
  const i10 = await E(t2);
  if (await l3(t2, r5, a8), !i10.updateAttachment) throw new s2(a8, "Layer source does not support updateAttachment capability");
  return i10.updateAttachment(r5, n8, o4);
}
async function d6(t2, e4, r5) {
  const n8 = await import("./editingSupport-IX2FTKWT.js"), o4 = await t2.load();
  return n8.applyEdits(o4, o4.source, e4, r5);
}
async function f5(t2, r5, n8, o4) {
  const a8 = await E(t2);
  if (await l3(t2, r5, o4), !a8.deleteAttachments) throw new s2(o4, "Layer source does not support deleteAttachments capability");
  return a8.deleteAttachments(r5, n8);
}
async function m4(t2, r5, n8) {
  const o4 = (await t2.load({ signal: r5 == null ? void 0 : r5.signal })).source;
  if (!o4.fetchRecomputedExtents) throw new s2(n8, "Layer source does not support fetchUpdates capability");
  return o4.fetchRecomputedExtents(r5);
}
async function h2(t2, r5, n8, o4) {
  var _a, _b;
  r5 = c2.from(r5), await t2.load();
  const a8 = t2.source, s14 = t2.capabilities;
  if (!((_a = s14 == null ? void 0 : s14.data) == null ? void 0 : _a.supportsAttachment)) throw new s2(o4, "this layer doesn't support attachments");
  const { attachmentTypes: u4, objectIds: c10, globalIds: p18, num: l6, size: y5, start: d7, where: f6 } = r5;
  if (!((_b = s14 == null ? void 0 : s14.operations) == null ? void 0 : _b.supportsQueryAttachments)) {
    if ((u4 == null ? void 0 : u4.length) > 0 || (p18 == null ? void 0 : p18.length) > 0 || (y5 == null ? void 0 : y5.length) > 0 || l6 || d7 || f6) throw new s2(o4, "when 'capabilities.operations.supportsQueryAttachments' is false, only objectIds is supported", r5);
  }
  if (!((c10 == null ? void 0 : c10.length) || (p18 == null ? void 0 : p18.length) || f6)) throw new s2(o4, "'objectIds', 'globalIds', or 'where' are required to perform attachment query", r5);
  if (!a8.queryAttachments) throw new s2(o4, "Layer source does not support queryAttachments capability", r5);
  return a8.queryAttachments(r5);
}
async function w2(t2, r5, n8, o4) {
  const a8 = await E(t2);
  if (!a8.queryObjectIds) throw new s2(o4, "Layer source does not support queryObjectIds capability");
  return a8.queryObjectIds(x.from(r5) ?? t2.createQuery(), n8);
}
async function b3(t2, r5, n8, o4) {
  const a8 = await E(t2);
  if (!a8.queryFeatureCount) throw new s2(o4, "Layer source does not support queryFeatureCount capability");
  return a8.queryFeatureCount(x.from(r5) ?? t2.createQuery(), n8);
}
async function g(t2, r5, n8, o4) {
  const a8 = await E(t2);
  if (!a8.queryExtent) throw new s2(o4, "Layer source does not support queryExtent capability");
  return a8.queryExtent(x.from(r5) ?? t2.createQuery(), n8);
}
async function q(t2, r5, n8, o4) {
  const a8 = await E(t2);
  if (!a8.queryRelatedFeatures) throw new s2(o4, "Layer source does not support queryRelatedFeatures capability");
  return a8.queryRelatedFeatures(d2.from(r5), n8);
}
async function j3(t2, r5, n8, o4) {
  const a8 = await E(t2);
  if (!a8.queryRelatedFeaturesCount) throw new s2(o4, "Layer source does not support queryRelatedFeaturesCount capability");
  return a8.queryRelatedFeaturesCount(d2.from(r5), n8);
}
async function F(t2) {
  const e4 = t2.source;
  if (e4 == null ? void 0 : e4.refresh) try {
    const { dataChanged: r5, updates: o4 } = await e4.refresh();
    if (r(o4) && (t2.sourceJSON = { ...t2.sourceJSON, ...o4 }, t2.read(o4, { origin: "service", url: t2.parsedUrl })), r5) return true;
  } catch {
  }
  if (t2.definitionExpression) try {
    return (await r4(t2.definitionExpression, t2.fieldsIndex)).hasDateFunctions;
  } catch {
  }
  return false;
}
function I2(t2) {
  const e4 = new x(), r5 = t2.get("capabilities.data"), n8 = t2.get("capabilities.query");
  e4.historicMoment = t2.historicMoment, e4.gdbVersion = t2.gdbVersion, e4.returnGeometry = true, n8 && (e4.compactGeometryEnabled = n8.supportsCompactGeometry, e4.defaultSpatialReferenceEnabled = n8.supportsDefaultSpatialReference), r5 && (r5.supportsZ && null != t2.returnZ && (e4.returnZ = t2.returnZ), r5.supportsM && null != t2.returnM && (e4.returnM = t2.returnM)), e4.outFields = ["*"];
  const { timeOffset: o4, timeExtent: a8 } = t2;
  return e4.timeExtent = null != o4 && null != a8 ? a8.offset(-o4.value, o4.unit) : a8 || null, e4.multipatchOption = "multipatch" === t2.geometryType ? "xyFootprint" : null, e4;
}
function P(t2) {
  const { globalIdField: e4, fields: r5 } = t2;
  if (e4) return e4;
  if (r5) {
    for (const n8 of r5) if ("esriFieldTypeGlobalID" === n8.type) return n8.name;
  }
}
function A(t2) {
  const { objectIdField: e4, fields: r5 } = t2;
  if (e4) return e4;
  if (r5) {
    for (const n8 of r5) if ("esriFieldTypeOID" === n8.type) return n8.name;
  }
}
function O(t2) {
  return t2.currentVersion ? t2.currentVersion : t2.hasOwnProperty("capabilities") || t2.hasOwnProperty("drawingInfo") || t2.hasOwnProperty("hasAttachments") || t2.hasOwnProperty("htmlPopupType") || t2.hasOwnProperty("relationships") || t2.hasOwnProperty("timeInfo") || t2.hasOwnProperty("typeIdField") || t2.hasOwnProperty("types") ? 10 : 9.3;
}
async function E(t2) {
  return (await t2.load()).source;
}
async function x2(e4, r5) {
  if (!r3) return;
  if (r3.findCredential(e4)) return;
  let o4;
  try {
    const n8 = await b2(e4, r5);
    n8 && (o4 = await r3.checkSignInStatus(`${n8}/sharing`));
  } catch (i10) {
  }
  if (o4) try {
    const o5 = r(r5) ? r5.signal : null;
    await r3.getCredential(e4, { signal: o5 });
  } catch (i10) {
  }
}
async function R(t2, e4) {
  var _a;
  const r5 = (_a = t2.parsedUrl) == null ? void 0 : _a.path;
  if (!r5) return;
  const n8 = t2.editFieldsInfo;
  (t2.userHasUpdateItemPrivileges || t2.userHasFullEditingPrivileges && t2.capabilities.operations.supportsEditing || (n8 == null ? void 0 : n8.creatorField) || (n8 == null ? void 0 : n8.editorField)) && await x2(r5, e4);
}
function C2(t2) {
  var _a;
  return !((_a = t2.sourceJSON) == null ? void 0 : _a.isMultiServicesView) && (t2.userHasUpdateItemPrivileges || t2.editingEnabled);
}

// node_modules/@arcgis/core/layers/support/EditFieldsInfo.js
var p15 = class extends i(l) {
  constructor(e4) {
    super(e4), this.creatorField = null, this.creationDateField = null, this.editorField = null, this.editDateField = null, this.realm = null, this.dateFieldsTimeReference = null;
  }
};
e([y()], p15.prototype, "creatorField", void 0), e([y()], p15.prototype, "creationDateField", void 0), e([y()], p15.prototype, "editorField", void 0), e([y()], p15.prototype, "editDateField", void 0), e([y()], p15.prototype, "realm", void 0), e([y({ type: a2 })], p15.prototype, "dateFieldsTimeReference", void 0), p15 = e([a("esri.layers.support.EditFieldsInfo")], p15);
var l4 = p15;

// node_modules/@arcgis/core/layers/support/FeatureIndex.js
var p16 = class extends i(l) {
  constructor(o4) {
    super(o4);
  }
};
e([y({ constructOnly: true, json: { write: true } })], p16.prototype, "name", void 0), e([y({ constructOnly: true, json: { write: true } })], p16.prototype, "fields", void 0), e([y({ constructOnly: true, json: { write: true } })], p16.prototype, "isAscending", void 0), e([y({ constructOnly: true, json: { write: true } })], p16.prototype, "indexType", void 0), e([y({ constructOnly: true, json: { write: true } })], p16.prototype, "isUnique", void 0), e([y({ constructOnly: true, json: { write: true } })], p16.prototype, "description", void 0), p16 = e([a("esri.layers.support.FeatureIndex")], p16);

// node_modules/@arcgis/core/layers/support/GeometryFieldsInfo.js
var a6 = class extends i(l) {
  constructor(e4) {
    super(e4), this.shapeAreaField = null, this.shapeLengthField = null, this.units = null;
  }
};
e([y({ type: String, json: { read: { source: "shapeAreaFieldName" } } })], a6.prototype, "shapeAreaField", void 0), e([y({ type: String, json: { read: { source: "shapeLengthFieldName" } } })], a6.prototype, "shapeLengthField", void 0), e([y({ type: String, json: { read: (e4) => le.read(e4) || me.read(e4) } })], a6.prototype, "units", void 0), a6 = e([a("esri.layers.support.GeometryFieldsInfo")], a6);
var c9 = a6;

// node_modules/@arcgis/core/layers/support/Relationship.js
var n7 = new s3({ esriRelCardinalityOneToOne: "one-to-one", esriRelCardinalityOneToMany: "one-to-many", esriRelCardinalityManyToMany: "many-to-many" });
var a7 = new s3({ esriRelRoleOrigin: "origin", esriRelRoleDestination: "destination" });
var l5 = class extends i(l) {
  constructor(e4) {
    super(e4), this.cardinality = null, this.composite = null, this.id = null, this.keyField = null, this.keyFieldInRelationshipTable = null, this.name = null, this.relatedTableId = null, this.relationshipTableId = null, this.role = null;
  }
};
e([y({ json: { read: n7.read, write: n7.write } })], l5.prototype, "cardinality", void 0), e([y({ json: { read: true, write: true } })], l5.prototype, "composite", void 0), e([y({ json: { read: true, write: true } })], l5.prototype, "id", void 0), e([y({ json: { read: true, write: true } })], l5.prototype, "keyField", void 0), e([y({ json: { read: true, write: true } })], l5.prototype, "keyFieldInRelationshipTable", void 0), e([y({ json: { read: true, write: true } })], l5.prototype, "name", void 0), e([y({ json: { read: true, write: true } })], l5.prototype, "relatedTableId", void 0), e([y({ json: { read: true, write: true } })], l5.prototype, "relationshipTableId", void 0), e([y({ json: { read: a7.read, write: a7.write } })], l5.prototype, "role", void 0), l5 = e([a("esri.layers.support.Relationship")], l5);
var p17 = l5;

// node_modules/@arcgis/core/layers/mixins/FeatureLayerBase.js
var T = (T2) => {
  let O2 = class extends T2 {
    constructor() {
      super(...arguments), this.capabilities = null, this.copyright = null, this.dateFieldsTimeReference = null, this.datesInUnknownTimezone = false, this.displayField = null, this.definitionExpression = null, this.editFieldsInfo = null, this.editingInfo = null, this.elevationInfo = null, this.floorInfo = null, this.fullExtent = null, this.gdbVersion = null, this.geometryFieldsInfo = null, this.geometryType = null, this.hasM = void 0, this.hasZ = void 0, this.heightModelInfo = null, this.historicMoment = null, this.isTable = false, this.layerId = void 0, this.minScale = 0, this.maxScale = 0, this.globalIdField = null, this.objectIdField = null, this.preferredTimeReference = null, this.relationships = null, this.sourceJSON = null, this.returnM = void 0, this.returnZ = void 0, this.serviceDefinitionExpression = null, this.serviceItemId = null, this.spatialReference = f.WGS84, this.subtypeField = null, this.trackIdField = null, this.indexes = new (j.ofType(p16))(), this.version = void 0;
    }
    readCapabilitiesFromService(e4, t2) {
      return n2(t2, this.url);
    }
    get effectiveCapabilities() {
      var _a;
      const e4 = this.capabilities;
      if (!e4) return null;
      const t2 = p(e4), { operations: o4, editing: i10 } = t2;
      return ((_a = this.sourceJSON) == null ? void 0 : _a.isMultiServicesView) ? (this.userHasUpdateItemPrivileges && (o4.supportsQuery = true), t2) : this.userHasUpdateItemPrivileges ? (o4.supportsAdd = o4.supportsDelete = o4.supportsEditing = o4.supportsQuery = o4.supportsUpdate = i10.supportsDeleteByOthers = i10.supportsGeometryUpdate = i10.supportsUpdateByOthers = true, t2) : (this.userHasFullEditingPrivileges && o4.supportsEditing && (o4.supportsAdd = o4.supportsDelete = o4.supportsUpdate = i10.supportsGeometryUpdate = true), t2);
    }
    readEditingInfo(e4, t2) {
      const { editingInfo: r5 } = t2;
      return r5 ? { lastEditDate: null != r5.lastEditDate ? new Date(r5.lastEditDate) : null } : null;
    }
    readIsTableFromService(e4, t2) {
      return "Table" === t2.type;
    }
    readMinScale(e4, t2) {
      return t2.effectiveMinScale || e4 || 0;
    }
    readMaxScale(e4, t2) {
      return t2.effectiveMaxScale || e4 || 0;
    }
    readGlobalIdFieldFromService(e4, t2) {
      return P(t2);
    }
    readObjectIdFieldFromService(e4, t2) {
      return A(t2);
    }
    readServiceDefinitionExpression(e4, t2) {
      return t2.definitionQuery || t2.definitionExpression;
    }
    set url(e4) {
      const t2 = b({ layer: this, url: e4, nonStandardUrlAllowed: true, logger: s.getLogger(this.declaredClass) });
      this._set("url", t2.url), null != t2.layerId && this._set("layerId", t2.layerId);
    }
    writeUrl(e4, t2, r5, o4) {
      C(this, e4, null, t2, o4);
    }
    readVersion(e4, t2) {
      return O(t2);
    }
  };
  return e([y({ readOnly: true, json: { read: false, origins: { service: { read: { source: ["advancedQueryCapabilities", "allowGeometryUpdates", "allowUpdateWithoutMValues", "archivingInfo", "capabilities", "datesInUnknownTimezone", "hasAttachments", "hasM", "hasZ", "maxRecordCount", "maxRecordCountFactor", "ownershipBasedAccessControlForFeatures", "standardMaxRecordCount", "supportedQueryFormats", "supportsAdvancedQueries", "supportsApplyEditsWithGlobalIds", "supportsAttachmentsByUploadId", "supportsAttachmentsResizing", "supportsCalculate", "supportsCoordinatesQuantization", "supportsExceedsLimitStatistics", "supportsFieldDescriptionProperty", "supportsQuantizationEditMode", "supportsRollbackOnFailureParameter", "supportsStatistics", "supportsTruncate", "supportsValidateSql", "tileMaxRecordCount", "useStandardizedQueries"] } } } } })], O2.prototype, "capabilities", void 0), e([o("service", "capabilities")], O2.prototype, "readCapabilitiesFromService", null), e([y({ readOnly: true })], O2.prototype, "effectiveCapabilities", null), e([y({ type: String, json: { origins: { service: { read: { source: "copyrightText" } } } } })], O2.prototype, "copyright", void 0), e([y({ type: a2 })], O2.prototype, "dateFieldsTimeReference", void 0), e([y({ type: Boolean })], O2.prototype, "datesInUnknownTimezone", void 0), e([y({ type: String, json: { origins: { service: { read: { source: "displayField" } } } } })], O2.prototype, "displayField", void 0), e([y({ type: String, json: { origins: { service: { read: false, write: false } }, name: "layerDefinition.definitionExpression", write: { enabled: true, allowNull: true } } })], O2.prototype, "definitionExpression", void 0), e([y({ readOnly: true, type: l4 })], O2.prototype, "editFieldsInfo", void 0), e([y({ readOnly: true })], O2.prototype, "editingInfo", void 0), e([o("editingInfo")], O2.prototype, "readEditingInfo", null), e([y((() => {
    const e4 = p(d), t2 = e4.json.origins;
    return t2["web-map"] = { read: false, write: false }, t2["portal-item"] = { read: false, write: false }, e4;
  })())], O2.prototype, "elevationInfo", void 0), e([y({ type: p2, json: { read: { source: "layerDefinition.floorInfo" }, write: { target: "layerDefinition.floorInfo" } } })], O2.prototype, "floorInfo", void 0), e([y({ type: w, json: { origins: { service: { read: { source: "extent" } } } } })], O2.prototype, "fullExtent", void 0), e([y()], O2.prototype, "gdbVersion", void 0), e([y({ readOnly: true, type: c9, json: { read: { source: "geometryProperties" } } })], O2.prototype, "geometryFieldsInfo", void 0), e([y({ type: ["point", "polygon", "polyline", "multipoint", "multipatch", "mesh"], json: { origins: { service: { read: c8.read } } } })], O2.prototype, "geometryType", void 0), e([y({ type: Boolean, json: { origins: { service: { read: true } } } })], O2.prototype, "hasM", void 0), e([y({ type: Boolean, json: { origins: { service: { read: true } } } })], O2.prototype, "hasZ", void 0), e([y({ readOnly: true, type: v })], O2.prototype, "heightModelInfo", void 0), e([y({ type: Date })], O2.prototype, "historicMoment", void 0), e([y({ readOnly: true })], O2.prototype, "isTable", void 0), e([o("service", "isTable", ["type"])], O2.prototype, "readIsTableFromService", null), e([y({ type: Number, json: { origins: { service: { read: { source: "id" } }, "portal-item": { read: false, write: { target: "id" } } }, read: false } })], O2.prototype, "layerId", void 0), e([y(I)], O2.prototype, "minScale", void 0), e([o("service", "minScale", ["minScale", "effectiveMinScale"])], O2.prototype, "readMinScale", null), e([y(D)], O2.prototype, "maxScale", void 0), e([o("service", "maxScale", ["maxScale", "effectiveMaxScale"])], O2.prototype, "readMaxScale", null), e([y({ type: String })], O2.prototype, "globalIdField", void 0), e([o("service", "globalIdField", ["globalIdField", "fields"])], O2.prototype, "readGlobalIdFieldFromService", null), e([y({ type: String })], O2.prototype, "objectIdField", void 0), e([o("service", "objectIdField", ["objectIdField", "fields"])], O2.prototype, "readObjectIdFieldFromService", null), e([y({ type: a2 })], O2.prototype, "preferredTimeReference", void 0), e([y({ type: [p17], readOnly: true })], O2.prototype, "relationships", void 0), e([y()], O2.prototype, "sourceJSON", void 0), e([y({ type: Boolean })], O2.prototype, "returnM", void 0), e([y({ type: Boolean })], O2.prototype, "returnZ", void 0), e([y({ readOnly: true })], O2.prototype, "serviceDefinitionExpression", void 0), e([o("service", "serviceDefinitionExpression", ["definitionQuery", "definitionExpression"])], O2.prototype, "readServiceDefinitionExpression", null), e([y({ type: String, readOnly: true, json: { read: false, origins: { service: { read: true } } } })], O2.prototype, "serviceItemId", void 0), e([y({ type: f, json: { origins: { service: { read: { source: "extent.spatialReference" } } } } })], O2.prototype, "spatialReference", void 0), e([y({ type: String, readOnly: true, json: { origins: { service: { read: true } } } })], O2.prototype, "subtypeField", void 0), e([y({ type: String, json: { read: { source: "timeInfo.trackIdField" } } })], O2.prototype, "trackIdField", void 0), e([y({ readOnly: true, json: { write: false } })], O2.prototype, "serverGens", void 0), e([y({ type: j.ofType(p16), readOnly: true })], O2.prototype, "indexes", void 0), e([y(f2)], O2.prototype, "url", null), e([r2("url")], O2.prototype, "writeUrl", null), e([y({ json: { origins: { service: { read: true } }, read: false } })], O2.prototype, "version", void 0), e([o("service", "version", ["currentVersion", "capabilities", "drawingInfo", "hasAttachments", "htmlPopupType", "relationships", "timeInfo", "typeIdField", "types"])], O2.prototype, "readVersion", null), O2 = e([a("esri.layers.mixins.FeatureLayerBase")], O2), O2;
};

export {
  y3 as y,
  c8 as c,
  p14 as p,
  y4 as y2,
  d6 as d,
  f5 as f,
  m4 as m,
  h2 as h,
  w2 as w,
  b3 as b,
  g,
  q,
  j3 as j,
  F,
  I2 as I,
  P,
  A,
  R,
  C2 as C,
  T
};
//# sourceMappingURL=chunk-L7LRY3AT.js.map
