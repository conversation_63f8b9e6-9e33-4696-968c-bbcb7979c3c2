{"version": 3, "sources": ["../../@arcgis/core/arcade/Dictionary.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ArcadeDate as t}from\"./ArcadeDate.js\";import{ArcadeExecutionError as s,ExecutionErrorCodes as i}from\"./executionError.js\";import e from\"./ImmutableArray.js\";import{i as n,a as r,b as o,c as a,t as u,d as l,e as c,f,g as h,h as b,j as y,k as m,l as d,m as g}from\"../chunks/languageUtils.js\";import w from\"../geometry/Geometry.js\";function J(t,s,i=!1){if(null==t)return null;if(o(t))return h(t);if(r(t))return b(t);if(a(t))return y(t);if(m(t))return d(t,s);if(g(t)){const e=[];for(const n of t)e.push(J(n,s,i));return e}const e=new T;e.immutable=!1;for(const n of Object.keys(t)){const r=t[n];void 0!==r&&e.setField(n,J(r,s,i))}return e.immutable=i,e}class T{constructor(t){this.declaredClass=\"esri.arcade.Dictionary\",this.attributes=null,this.plain=!1,this.immutable=!0,this.attributes=t instanceof T?t.attributes:t??{}}field(t){const e=t.toLowerCase(),n=this.attributes[t];if(void 0!==n)return n;for(const s in this.attributes)if(s.toLowerCase()===e)return this.attributes[s];throw new s(null,i.FieldNotFound,null,{key:t})}setField(e,r){if(this.immutable)throw new s(null,i.Immutable,null);if(n(r))throw new s(null,i.NoFunctionInDictionary,null);const o=e.toLowerCase();r instanceof Date&&(r=t.dateJSToArcadeDate(r));if(void 0===this.attributes[e]){for(const t in this.attributes)if(t.toLowerCase()===o)return void(this.attributes[t]=r);this.attributes[e]=r}else this.attributes[e]=r}hasField(t){const s=t.toLowerCase();if(void 0!==this.attributes[t])return!0;for(const i in this.attributes)if(i.toLowerCase()===s)return!0;return!1}keys(){let t=[];for(const s in this.attributes)t.push(s);return t=t.sort(),t}castToText(s=!1){let i=\"\";for(const n in this.attributes){\"\"!==i&&(i+=\",\");const l=this.attributes[n];null==l?i+=JSON.stringify(n)+\":null\":r(l)||o(l)||a(l)?i+=JSON.stringify(n)+\":\"+JSON.stringify(l):l instanceof w?i+=JSON.stringify(n)+\":\"+u(l):l instanceof e||l instanceof Array?i+=JSON.stringify(n)+\":\"+u(l,null,s):l instanceof t?i+=s?JSON.stringify(n)+\":\"+JSON.stringify(l.getTime()):JSON.stringify(n)+\":\"+l.stringify():null!==l&&\"object\"==typeof l&&void 0!==l.castToText&&(i+=JSON.stringify(n)+\":\"+l.castToText(s))}return\"{\"+i+\"}\"}static convertObjectToArcadeDictionary(t,s,i=!0){const e=new T;e.immutable=!1;for(const n in t){const i=t[n];void 0!==i&&e.setField(n.toString(),J(i,s))}return e.immutable=i,e}static convertJsonToArcade(t,s,i=!1){return J(t,s,i)}castAsJson(t=null){const s={};for(let i in this.attributes){const e=this.attributes[i];void 0!==e&&(t?.keyTranslate&&(i=t.keyTranslate(i)),s[i]=l(e,t))}return s}async castDictionaryValueAsJsonAsync(t,s,i,e=null,n){const r=await c(i,e,n);return t[s]=r,r}async castAsJsonAsync(s=null,i=null){const e={},n=[];for(let r in this.attributes){const o=this.attributes[r];i?.keyTranslate&&(r=i.keyTranslate(r)),void 0!==o&&(f(o)||o instanceof w||o instanceof t?e[r]=l(o,i):n.push(this.castDictionaryValueAsJsonAsync(e,r,o,s,i)))}return n.length>0&&await Promise.all(n),e}}export{T as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgV,SAASA,GAAEC,IAAE,GAAE,IAAE,OAAG;AAAC,MAAG,QAAMA,GAAE,QAAO;AAAK,MAAG,EAAEA,EAAC,EAAE,QAAO,GAAEA,EAAC;AAAE,MAAG,EAAEA,EAAC,EAAE,QAAO,GAAEA,EAAC;AAAE,MAAG,EAAEA,EAAC,EAAE,QAAO,GAAEA,EAAC;AAAE,MAAG,EAAEA,EAAC,EAAE,QAAO,GAAEA,IAAE,CAAC;AAAE,MAAG,EAAEA,EAAC,GAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAU,KAAKD,GAAE,CAAAC,GAAE,KAAKF,GAAE,GAAE,GAAE,CAAC,CAAC;AAAE,WAAOE;AAAA,EAAC;AAAC,QAAMA,KAAE,IAAI;AAAE,EAAAA,GAAE,YAAU;AAAG,aAAU,KAAK,OAAO,KAAKD,EAAC,GAAE;AAAC,UAAM,IAAEA,GAAE,CAAC;AAAE,eAAS,KAAGC,GAAE,SAAS,GAAEF,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAC,SAAOE,GAAE,YAAU,GAAEA;AAAC;AAAC,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,SAAK,gBAAc,0BAAyB,KAAK,aAAW,MAAK,KAAK,QAAM,OAAG,KAAK,YAAU,MAAG,KAAK,aAAWA,cAAa,KAAEA,GAAE,aAAWA,MAAG,CAAC;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAE;AAAC,UAAMC,KAAED,GAAE,YAAY,GAAE,IAAE,KAAK,WAAWA,EAAC;AAAE,QAAG,WAAS,EAAE,QAAO;AAAE,eAAU,KAAK,KAAK,WAAW,KAAG,EAAE,YAAY,MAAIC,GAAE,QAAO,KAAK,WAAW,CAAC;AAAE,UAAM,IAAI,EAAE,MAAK,EAAE,eAAc,MAAK,EAAC,KAAID,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,SAASC,IAAE,GAAE;AAAC,QAAG,KAAK,UAAU,OAAM,IAAI,EAAE,MAAK,EAAE,WAAU,IAAI;AAAE,QAAG,EAAE,CAAC,EAAE,OAAM,IAAI,EAAE,MAAK,EAAE,wBAAuB,IAAI;AAAE,UAAM,IAAEA,GAAE,YAAY;AAAE,iBAAa,SAAO,IAAE,EAAE,mBAAmB,CAAC;AAAG,QAAG,WAAS,KAAK,WAAWA,EAAC,GAAE;AAAC,iBAAUD,MAAK,KAAK,WAAW,KAAGA,GAAE,YAAY,MAAI,EAAE,QAAO,MAAK,KAAK,WAAWA,EAAC,IAAE;AAAG,WAAK,WAAWC,EAAC,IAAE;AAAA,IAAC,MAAM,MAAK,WAAWA,EAAC,IAAE;AAAA,EAAC;AAAA,EAAC,SAASD,IAAE;AAAC,UAAM,IAAEA,GAAE,YAAY;AAAE,QAAG,WAAS,KAAK,WAAWA,EAAC,EAAE,QAAM;AAAG,eAAU,KAAK,KAAK,WAAW,KAAG,EAAE,YAAY,MAAI,EAAE,QAAM;AAAG,WAAM;AAAA,EAAE;AAAA,EAAC,OAAM;AAAC,QAAIA,KAAE,CAAC;AAAE,eAAU,KAAK,KAAK,WAAW,CAAAA,GAAE,KAAK,CAAC;AAAE,WAAOA,KAAEA,GAAE,KAAK,GAAEA;AAAA,EAAC;AAAA,EAAC,WAAW,IAAE,OAAG;AAAC,QAAI,IAAE;AAAG,eAAU,KAAK,KAAK,YAAW;AAAC,aAAK,MAAI,KAAG;AAAK,YAAM,IAAE,KAAK,WAAW,CAAC;AAAE,cAAM,IAAE,KAAG,KAAK,UAAU,CAAC,IAAE,UAAQ,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,KAAG,KAAK,UAAU,CAAC,IAAE,MAAI,KAAK,UAAU,CAAC,IAAE,aAAa,IAAE,KAAG,KAAK,UAAU,CAAC,IAAE,MAAI,GAAE,CAAC,IAAE,aAAaA,MAAG,aAAa,QAAM,KAAG,KAAK,UAAU,CAAC,IAAE,MAAI,GAAE,GAAE,MAAK,CAAC,IAAE,aAAa,IAAE,KAAG,IAAE,KAAK,UAAU,CAAC,IAAE,MAAI,KAAK,UAAU,EAAE,QAAQ,CAAC,IAAE,KAAK,UAAU,CAAC,IAAE,MAAI,EAAE,UAAU,IAAE,SAAO,KAAG,YAAU,OAAO,KAAG,WAAS,EAAE,eAAa,KAAG,KAAK,UAAU,CAAC,IAAE,MAAI,EAAE,WAAW,CAAC;AAAA,IAAE;AAAC,WAAM,MAAI,IAAE;AAAA,EAAG;AAAA,EAAC,OAAO,gCAAgCA,IAAE,GAAE,IAAE,MAAG;AAAC,UAAMC,KAAE,IAAI;AAAE,IAAAA,GAAE,YAAU;AAAG,eAAU,KAAKD,IAAE;AAAC,YAAME,KAAEF,GAAE,CAAC;AAAE,iBAASE,MAAGD,GAAE,SAAS,EAAE,SAAS,GAAEF,GAAEG,IAAE,CAAC,CAAC;AAAA,IAAC;AAAC,WAAOD,GAAE,YAAU,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAO,oBAAoBD,IAAE,GAAE,IAAE,OAAG;AAAC,WAAOD,GAAEC,IAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,KAAE,MAAK;AAAC,UAAM,IAAE,CAAC;AAAE,aAAQ,KAAK,KAAK,YAAW;AAAC,YAAMC,KAAE,KAAK,WAAW,CAAC;AAAE,iBAASA,QAAID,MAAA,gBAAAA,GAAG,kBAAe,IAAEA,GAAE,aAAa,CAAC,IAAG,EAAE,CAAC,IAAE,GAAEC,IAAED,EAAC;AAAA,IAAE;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,MAAM,+BAA+BA,IAAE,GAAE,GAAEC,KAAE,MAAK,GAAE;AAAC,UAAM,IAAE,MAAM,GAAE,GAAEA,IAAE,CAAC;AAAE,WAAOD,GAAE,CAAC,IAAE,GAAE;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgB,IAAE,MAAK,IAAE,MAAK;AAAC,UAAMC,KAAE,CAAC,GAAE,IAAE,CAAC;AAAE,aAAQ,KAAK,KAAK,YAAW;AAAC,YAAM,IAAE,KAAK,WAAW,CAAC;AAAE,8BAAG,kBAAe,IAAE,EAAE,aAAa,CAAC,IAAG,WAAS,MAAI,EAAE,CAAC,KAAG,aAAa,KAAG,aAAa,IAAEA,GAAE,CAAC,IAAE,GAAE,GAAE,CAAC,IAAE,EAAE,KAAK,KAAK,+BAA+BA,IAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,IAAE;AAAC,WAAO,EAAE,SAAO,KAAG,MAAM,QAAQ,IAAI,CAAC,GAAEA;AAAA,EAAC;AAAC;", "names": ["J", "t", "e", "i"]}