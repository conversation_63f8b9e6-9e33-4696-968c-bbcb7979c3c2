import {
  r
} from "./chunk-I2B245QS.js";
import {
  s
} from "./chunk-MN2LGVDI.js";

// node_modules/@arcgis/core/geometry/geometryEngine.js
function t(n) {
  return Array.isArray(n) ? n[0].spatialReference : n && n.spatialReference;
}
function r2(e) {
  return s.extendedSpatialReferenceInfo(e);
}
function u(r3, u2) {
  return s.clip(r, t(r3), r3, u2);
}
function i(r3, u2) {
  return s.cut(r, t(r3), r3, u2);
}
function c(r3, u2) {
  return s.contains(r, t(r3), r3, u2);
}
function o(r3, u2) {
  return s.crosses(r, t(r3), r3, u2);
}
function f(r3, u2, i2) {
  return s.distance(r, t(r3), r3, u2, i2);
}
function s2(r3, u2) {
  return s.equals(r, t(r3), r3, u2);
}
function a(r3, u2) {
  return s.intersects(r, t(r3), r3, u2);
}
function l(r3, u2) {
  return s.touches(r, t(r3), r3, u2);
}
function p(r3, u2) {
  return s.within(r, t(r3), r3, u2);
}
function d(r3, u2) {
  return s.disjoint(r, t(r3), r3, u2);
}
function m(r3, u2) {
  return s.overlaps(r, t(r3), r3, u2);
}
function g(r3, u2, i2) {
  return s.relate(r, t(r3), r3, u2, i2);
}
function h(r3) {
  return s.isSimple(r, t(r3), r3);
}
function w(r3) {
  return s.simplify(r, t(r3), r3);
}
function R(r3, u2 = false) {
  return s.convexHull(r, t(r3), r3, u2);
}
function x(r3, u2) {
  return s.difference(r, t(r3), r3, u2);
}
function y(r3, u2) {
  return s.symmetricDifference(r, t(r3), r3, u2);
}
function S(r3, u2) {
  return s.intersect(r, t(r3), r3, u2);
}
function A(r3, u2 = null) {
  return s.union(r, t(r3), r3, u2);
}
function D(r3, u2, i2, c2, o2, f2) {
  return s.offset(r, t(r3), r3, u2, i2, c2, o2, f2);
}
function j(r3, u2, i2, c2 = false) {
  return s.buffer(r, t(r3), r3, u2, i2, c2);
}
function E(r3, u2, i2, c2, o2, f2) {
  return s.geodesicBuffer(r, t(r3), r3, u2, i2, c2, o2, f2);
}
function J(r3, u2, i2 = true) {
  return s.nearestCoordinate(r, t(r3), r3, u2, i2);
}
function L(r3, u2) {
  return s.nearestVertex(r, t(r3), r3, u2);
}
function N(r3, u2, i2, c2) {
  return s.nearestVertices(r, t(r3), r3, u2, i2, c2);
}
function O(n) {
  var _a;
  return "xmin" in n ? "center" in n ? n.center : null : "x" in n ? n : "extent" in n ? ((_a = n.extent) == null ? void 0 : _a.center) ?? null : null;
}
function T(e, t2, r3) {
  if (null == e) throw new F();
  const u2 = e.spatialReference;
  if (null == (r3 = r3 ?? O(e))) throw new F();
  const i2 = e.constructor.fromJSON(s.rotate(e, t2, r3));
  return i2.spatialReference = u2, i2;
}
function V(e, t2) {
  if (null == e) throw new F();
  const r3 = e.spatialReference;
  if (null == (t2 = t2 ?? O(e))) throw new F();
  const u2 = e.constructor.fromJSON(s.flipHorizontal(e, t2));
  return u2.spatialReference = r3, u2;
}
function v(e, t2) {
  if (null == e) throw new F();
  const r3 = e.spatialReference;
  if (null == (t2 = t2 ?? O(e))) throw new F();
  const u2 = e.constructor.fromJSON(s.flipVertical(e, t2));
  return u2.spatialReference = r3, u2;
}
function z(r3, u2, i2, c2) {
  return s.generalize(r, t(r3), r3, u2, i2, c2);
}
function B(r3, u2, i2) {
  return s.densify(r, t(r3), r3, u2, i2);
}
function H(r3, u2, i2, c2 = 0) {
  return s.geodesicDensify(r, t(r3), r3, u2, i2, c2);
}
function I(r3, u2) {
  return s.planarArea(r, t(r3), r3, u2);
}
function b(r3, u2) {
  return s.planarLength(r, t(r3), r3, u2);
}
function k(r3, u2, i2) {
  return s.geodesicArea(r, t(r3), r3, u2, i2);
}
function q(r3, u2, i2) {
  return s.geodesicLength(r, t(r3), r3, u2, i2);
}
function C(r3, u2) {
  return s.intersectLinesToPoints(r, t(r3), r3, u2);
}
function G(e, t2) {
  s.changeDefaultSpatialReferenceTolerance(e, t2);
}
function P(e) {
  s.clearDefaultSpatialReferenceTolerance(e);
}
var F = class extends Error {
  constructor() {
    super("Illegal Argument Exception");
  }
};

export {
  r2 as r,
  u,
  i,
  c,
  o,
  f,
  s2 as s,
  a,
  l,
  p,
  d,
  m,
  g,
  h,
  w,
  R,
  x,
  y,
  S,
  A,
  D,
  j,
  E,
  J,
  L,
  N,
  T,
  V,
  v,
  z,
  B,
  H,
  I,
  b,
  k,
  q,
  C,
  G,
  P
};
//# sourceMappingURL=chunk-XXXEFHEN.js.map
