import {
  e
} from "./chunk-2RO3UJ2R.js";
import "./chunk-MUYX6GXF.js";
import {
  c as c2,
  t
} from "./chunk-762DBG4V.js";
import {
  F,
  a
} from "./chunk-AVKOL7OR.js";
import "./chunk-I7WHRVHF.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  c,
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/symbols/support/symbolLayerUtils.js
var a2 = m();
function m() {
  return new e(50);
}
function y() {
  a2 = m();
}
function f(e2, o) {
  if ("icon" === e2.type) return p(e2, o);
  if ("object" === e2.type) return d(e2, o);
  throw new s("symbol3d:unsupported-symbol-layer", "computeLayerSize only works with symbol layers of type Icon and Object");
}
async function l(e2, o) {
  if ("icon" === e2.type) return h(e2, o);
  if ("object" === e2.type) return w(e2, o);
  throw new s("symbol3d:unsupported-symbol-layer", "computeLayerSize only works with symbol layers of type Icon and Object");
}
async function p(e2, o) {
  var _a, _b;
  if ((_a = e2.resource) == null ? void 0 : _a.href) return b(e2.resource.href).then((e3) => [e3.width, e3.height]);
  if ((_b = e2.resource) == null ? void 0 : _b.primitive) return r(o) ? [o, o] : [256, 256];
  throw new s("symbol3d:invalid-symbol-layer", "symbol layers of type Icon must have either an href or a primitive resource");
}
function h(e2, r2) {
  return p(e2, r2).then((r3) => {
    if (null == e2.size) return r3;
    const o = r3[0] / r3[1];
    return o > 1 ? [e2.size, e2.size / o] : [e2.size * o, e2.size];
  });
}
function b(r2) {
  return U(r2, { responseType: "image" }).then((e2) => e2.data);
}
function d(e2, r2) {
  return j(e2, r2).then((e3) => F(e3));
}
async function w(e2, r2) {
  const o = await d(e2, r2);
  return t(o, e2);
}
async function j(e2, o) {
  var _a;
  if (!e2.isPrimitive) {
    const r2 = c((_a = e2.resource) == null ? void 0 : _a.href), o2 = a2.get(r2);
    if (void 0 !== o2) return o2;
    const t2 = await import("./objectResourceUtils-UAXK5O2Y.js"), i2 = await t2.fetch(r2, { disableTextures: true });
    return a2.put(r2, i2.referenceBoundingBox), i2.referenceBoundingBox;
  }
  let i = null;
  if (e2.resource && e2.resource.primitive && (i = a(c2(e2.resource.primitive)), r(o))) for (let r2 = 0; r2 < i.length; r2++) i[r2] *= o;
  return i ? Promise.resolve(i) : Promise.reject(new s("symbol:invalid-resource", "The symbol does not have a valid resource"));
}
export {
  y as clearBoundingBoxCache,
  p as computeIconLayerResourceSize,
  f as computeLayerResourceSize,
  l as computeLayerSize,
  d as computeObjectLayerResourceSize
};
//# sourceMappingURL=symbolLayerUtils-5S47SW6L.js.map
