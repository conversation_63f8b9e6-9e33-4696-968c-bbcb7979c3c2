{"version": 3, "sources": ["../../@arcgis/core/arcade/functions/featuresetgeom.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ArcadeExecutionError as e,ExecutionErrorCodes as n}from\"../executionError.js\";import{shapeExtent as t}from\"../kernel.js\";import{G as r,y as i,j as a,T as s}from\"../../chunks/languageUtils.js\";import u from\"../featureset/actions/SpatialFilter.js\";import o from\"../featureset/sources/Empty.js\";import l from\"../../geometry/Geometry.js\";import{relate as c,crosses as f,touches as p,within as m,overlaps as d,contains as w,intersects as y}from\"../../geometry/geometryEngineAsync.js\";function h(e){return e instanceof l}function S(i,a,c,S){return S(i,a,(async(S,R,v)=>{if(v.length<2)throw new e(i,n.WrongNumberOfParameters,a);if(null===(v=r(v))[0]&&null===v[1])return!1;if(s(v[0])){if(v[1]instanceof l)return new u({parentfeatureset:v[0],relation:c,relationGeom:v[1]});if(null===v[1])return new o({parentfeatureset:v[0]});throw new e(i,n.InvalidParameter,a)}if(h(v[0])){if(h(v[1])){switch(c){case\"esriSpatialRelEnvelopeIntersects\":return y(t(v[0]),t(v[1]));case\"esriSpatialRelIntersects\":return y(v[0],v[1]);case\"esriSpatialRelContains\":return w(v[0],v[1]);case\"esriSpatialRelOverlaps\":return d(v[0],v[1]);case\"esriSpatialRelWithin\":return m(v[0],v[1]);case\"esriSpatialRelTouches\":return p(v[0],v[1]);case\"esriSpatialRelCrosses\":return f(v[0],v[1])}throw new e(i,n.InvalidParameter,a)}if(s(v[1]))return new u({parentfeatureset:v[1],relation:c,relationGeom:v[0]});if(null===v[1])return!1;throw new e(i,n.InvalidParameter,a)}if(null!==v[0])throw new e(i,n.InvalidParameter,a);return s(v[1])?new o({parentfeatureset:v[1]}):!(v[1]instanceof l||null===v[1])&&void 0}))}function R(t){\"async\"===t.mode&&(t.functions.intersects=function(e,n){return S(e,n,\"esriSpatialRelIntersects\",t.standardFunctionAsync)},t.functions.envelopeintersects=function(e,n){return S(e,n,\"esriSpatialRelEnvelopeIntersects\",t.standardFunctionAsync)},t.signatures.push({name:\"envelopeintersects\",min:2,max:2}),t.functions.contains=function(e,n){return S(e,n,\"esriSpatialRelContains\",t.standardFunctionAsync)},t.functions.overlaps=function(e,n){return S(e,n,\"esriSpatialRelOverlaps\",t.standardFunctionAsync)},t.functions.within=function(e,n){return S(e,n,\"esriSpatialRelWithin\",t.standardFunctionAsync)},t.functions.touches=function(e,n){return S(e,n,\"esriSpatialRelTouches\",t.standardFunctionAsync)},t.functions.crosses=function(e,n){return S(e,n,\"esriSpatialRelCrosses\",t.standardFunctionAsync)},t.functions.relate=function(u,f){return t.standardFunctionAsync(u,f,((t,p,m)=>{if(m=r(m),i(m,3,3,u,f),h(m[0])&&h(m[1]))return c(m[0],m[1],a(m[2]));if(m[0]instanceof l&&null===m[1])return!1;if(m[1]instanceof l&&null===m[0])return!1;if(s(m[0])&&null===m[1])return new o({parentfeatureset:m[0]});if(s(m[1])&&null===m[0])return new o({parentfeatureset:m[1]});if(s(m[0])&&m[1]instanceof l)return m[0].relate(m[1],a(m[2]));if(s(m[1])&&m[0]instanceof l)return m[1].relate(m[0],a(m[2]));if(null===m[0]&&null===m[1])return!1;throw new e(u,n.InvalidParameter,f)}))})}export{R as registerFunctions};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIse,SAASA,GAAEC,IAAE;AAAC,SAAOA,cAAa;AAAC;AAAC,SAASC,GAAE,GAAE,GAAE,GAAEA,IAAE;AAAC,SAAOA,GAAE,GAAE,GAAG,OAAMA,IAAEC,IAAE,MAAI;AAAC,QAAG,EAAE,SAAO,EAAE,OAAM,IAAI,EAAE,GAAE,EAAE,yBAAwB,CAAC;AAAE,QAAG,UAAQ,IAAE,GAAE,CAAC,GAAG,CAAC,KAAG,SAAO,EAAE,CAAC,EAAE,QAAM;AAAG,QAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,UAAG,EAAE,CAAC,aAAY,EAAE,QAAO,IAAI,EAAE,EAAC,kBAAiB,EAAE,CAAC,GAAE,UAAS,GAAE,cAAa,EAAE,CAAC,EAAC,CAAC;AAAE,UAAG,SAAO,EAAE,CAAC,EAAE,QAAO,IAAI,EAAE,EAAC,kBAAiB,EAAE,CAAC,EAAC,CAAC;AAAE,YAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,IAAC;AAAC,QAAGH,GAAE,EAAE,CAAC,CAAC,GAAE;AAAC,UAAGA,GAAE,EAAE,CAAC,CAAC,GAAE;AAAC,gBAAO,GAAE;AAAA,UAAC,KAAI;AAAmC,mBAAO,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAAA,UAAE,KAAI;AAA2B,mBAAO,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,UAAE,KAAI;AAAyB,mBAAOI,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,UAAE,KAAI;AAAyB,mBAAO,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,UAAE,KAAI;AAAuB,mBAAO,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,UAAE,KAAI;AAAwB,mBAAO,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,UAAE,KAAI;AAAwB,mBAAO,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,QAAC;AAAC,cAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,MAAC;AAAC,UAAG,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,IAAI,EAAE,EAAC,kBAAiB,EAAE,CAAC,GAAE,UAAS,GAAE,cAAa,EAAE,CAAC,EAAC,CAAC;AAAE,UAAG,SAAO,EAAE,CAAC,EAAE,QAAM;AAAG,YAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,IAAC;AAAC,QAAG,SAAO,EAAE,CAAC,EAAE,OAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAE,WAAO,EAAE,EAAE,CAAC,CAAC,IAAE,IAAI,EAAE,EAAC,kBAAiB,EAAE,CAAC,EAAC,CAAC,IAAE,EAAE,EAAE,CAAC,aAAY,KAAG,SAAO,EAAE,CAAC,MAAI;AAAA,EAAM,CAAE;AAAC;AAAC,SAASD,GAAEE,IAAE;AAAC,cAAUA,GAAE,SAAOA,GAAE,UAAU,aAAW,SAASJ,IAAE,GAAE;AAAC,WAAOC,GAAED,IAAE,GAAE,4BAA2BI,GAAE,qBAAqB;AAAA,EAAC,GAAEA,GAAE,UAAU,qBAAmB,SAASJ,IAAE,GAAE;AAAC,WAAOC,GAAED,IAAE,GAAE,oCAAmCI,GAAE,qBAAqB;AAAA,EAAC,GAAEA,GAAE,WAAW,KAAK,EAAC,MAAK,sBAAqB,KAAI,GAAE,KAAI,EAAC,CAAC,GAAEA,GAAE,UAAU,WAAS,SAASJ,IAAE,GAAE;AAAC,WAAOC,GAAED,IAAE,GAAE,0BAAyBI,GAAE,qBAAqB;AAAA,EAAC,GAAEA,GAAE,UAAU,WAAS,SAASJ,IAAE,GAAE;AAAC,WAAOC,GAAED,IAAE,GAAE,0BAAyBI,GAAE,qBAAqB;AAAA,EAAC,GAAEA,GAAE,UAAU,SAAO,SAASJ,IAAE,GAAE;AAAC,WAAOC,GAAED,IAAE,GAAE,wBAAuBI,GAAE,qBAAqB;AAAA,EAAC,GAAEA,GAAE,UAAU,UAAQ,SAASJ,IAAE,GAAE;AAAC,WAAOC,GAAED,IAAE,GAAE,yBAAwBI,GAAE,qBAAqB;AAAA,EAAC,GAAEA,GAAE,UAAU,UAAQ,SAASJ,IAAE,GAAE;AAAC,WAAOC,GAAED,IAAE,GAAE,yBAAwBI,GAAE,qBAAqB;AAAA,EAAC,GAAEA,GAAE,UAAU,SAAO,SAASC,IAAEC,IAAE;AAAC,WAAOF,GAAE,sBAAsBC,IAAEC,IAAG,CAACF,IAAED,IAAEI,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEF,IAAEC,EAAC,GAAEP,GAAEQ,GAAE,CAAC,CAAC,KAAGR,GAAEQ,GAAE,CAAC,CAAC,EAAE,QAAO,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAE,UAAGA,GAAE,CAAC,aAAY,KAAG,SAAOA,GAAE,CAAC,EAAE,QAAM;AAAG,UAAGA,GAAE,CAAC,aAAY,KAAG,SAAOA,GAAE,CAAC,EAAE,QAAM;AAAG,UAAG,EAAEA,GAAE,CAAC,CAAC,KAAG,SAAOA,GAAE,CAAC,EAAE,QAAO,IAAI,EAAE,EAAC,kBAAiBA,GAAE,CAAC,EAAC,CAAC;AAAE,UAAG,EAAEA,GAAE,CAAC,CAAC,KAAG,SAAOA,GAAE,CAAC,EAAE,QAAO,IAAI,EAAE,EAAC,kBAAiBA,GAAE,CAAC,EAAC,CAAC;AAAE,UAAG,EAAEA,GAAE,CAAC,CAAC,KAAGA,GAAE,CAAC,aAAY,EAAE,QAAOA,GAAE,CAAC,EAAE,OAAOA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAE,UAAG,EAAEA,GAAE,CAAC,CAAC,KAAGA,GAAE,CAAC,aAAY,EAAE,QAAOA,GAAE,CAAC,EAAE,OAAOA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAE,UAAG,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,EAAE,QAAM;AAAG,YAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAE;", "names": ["h", "e", "S", "R", "p", "t", "u", "f", "m"]}