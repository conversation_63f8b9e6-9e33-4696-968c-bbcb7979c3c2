import {
  r
} from "./chunk-REGYRSW7.js";
import {
  n as n2
} from "./chunk-Y424ZXTG.js";
import {
  e as e3
} from "./chunk-UB5FTTH5.js";
import {
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  c,
  d,
  v
} from "./chunk-N3S5O3YO.js";
import {
  e as e2
} from "./chunk-32BGXH4N.js";
import {
  o as o3
} from "./chunk-BPRRRPC3.js";
import {
  e
} from "./chunk-GXMOAZWH.js";
import {
  o as o4
} from "./chunk-TUB4N6LD.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";
import {
  n
} from "./chunk-NOZFLZZL.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/ScreenSizeScaling.glsl.js
function o5(o6, n3) {
  if (!n3.screenSizeEnabled) return;
  const c2 = o6.vertex;
  c(c2, n3), c2.uniforms.add(new o4("perScreenPixelRatio", (e4, i) => i.camera.perScreenPixelRatio)), c2.uniforms.add(new o4("screenSizeScale", (e4) => e4.screenSizeScale)), c2.code.add(o`float computeRenderPixelSizeAt( vec3 pWorld ){
vec3 viewForward = - vec3(view[0][2], view[1][2], view[2][2]);
float viewDirectionDistance = abs(dot(viewForward, pWorld - cameraPosition));
return viewDirectionDistance * perScreenPixelRatio;
}
vec3 screenSizeScaling(vec3 position, vec3 anchor){
return position * screenSizeScale * computeRenderPixelSizeAt(anchor) + anchor;
}`);
}

// node_modules/@arcgis/core/chunks/ShadedColorMaterial.glsl.js
function f(e4) {
  const f2 = new o2(), b2 = e4.hasMultipassTerrain && (e4.output === h.Color || e4.output === h.Alpha);
  f2.include(r, e4), f2.include(o5, e4), f2.include(u, e4);
  const { vertex: h3, fragment: C } = f2;
  return C.include(e3), v(h3, e4), C.uniforms.add(new e("uColor", (e5) => e5.color)), f2.attributes.add(O.POSITION, "vec3"), f2.varyings.add("vWorldPosition", "vec3"), b2 && f2.varyings.add("depth", "float"), e4.screenSizeEnabled && f2.attributes.add(O.OFFSET, "vec3"), e4.shadingEnabled && (d(h3), f2.attributes.add(O.NORMAL, "vec3"), f2.varyings.add("vViewNormal", "vec3")), h3.code.add(o`
    void main(void) {
      vWorldPosition = ${e4.screenSizeEnabled ? "screenSizeScaling(offset, position)" : "position"};
  `), e4.shadingEnabled && h3.code.add(o`vec3 worldNormal = normal;
vViewNormal = (viewNormal * vec4(worldNormal, 1)).xyz;`), h3.code.add(o`
    ${b2 ? "depth = (view * vec4(vWorldPosition, 1.0)).z;" : ""}
    gl_Position = transformPosition(proj, view, vWorldPosition);
  }
  `), b2 && f2.include(n2, e4), C.code.add(o`
    void main() {
      discardBySlice(vWorldPosition);
      ${b2 ? "terrainDepthTest(gl_FragCoord, depth);" : ""}
    `), e4.shadingEnabled ? (C.uniforms.add(new e2("shadingDirection", (e5) => e5.shadingDirection)), C.uniforms.add(new e("shadedColor", (e5) => w(e5.shadingTint, e5.color))), C.code.add(o`vec3 viewNormalNorm = normalize(vViewNormal);
float shadingFactor = 1.0 - clamp(-dot(viewNormalNorm, shadingDirection), 0.0, 1.0);
vec4 finalColor = mix(uColor, shadedColor, shadingFactor);`)) : C.code.add(o`vec4 finalColor = uColor;`), C.code.add(o`
      ${e4.output === h.ObjectAndLayerIdColor ? o`finalColor.a = 1.0;` : ""}
      if (finalColor.a < ${o.float(t)}) {
        discard;
      }
      ${e4.output === h.Alpha ? o`gl_FragColor = vec4(finalColor.a);` : ""}

      ${e4.output === h.Color ? o`gl_FragColor = highlightSlice(finalColor, vWorldPosition); ${e4.transparencyPassType === o3.Color ? "gl_FragColor = premultiplyAlpha(gl_FragColor);" : ""}` : ""}
    }
    `), f2;
}
function w(e4, o6) {
  const r2 = 1 - e4[3], i = e4[3] + o6[3] * r2;
  return 0 === i ? (b[3] = i, b) : (b[0] = (e4[0] * e4[3] + o6[0] * o6[3] * r2) / i, b[1] = (e4[1] * e4[3] + o6[1] * o6[3] * r2) / i, b[2] = (e4[2] * e4[3] + o6[2] * o6[3] * r2) / i, b[3] = o6[3], b);
}
var b = n();
var h2 = Object.freeze(Object.defineProperty({ __proto__: null, build: f }, Symbol.toStringTag, { value: "Module" }));

export {
  f,
  h2 as h
};
//# sourceMappingURL=chunk-NXCE4GJJ.js.map
