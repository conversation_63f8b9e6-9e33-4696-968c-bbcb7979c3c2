import {
  r
} from "./chunk-UCWK623G.js";
import {
  y
} from "./chunk-NZB6EMKN.js";
import {
  T
} from "./chunk-VNYCO3JG.js";
import {
  s
} from "./chunk-RV4I37UI.js";

// node_modules/@arcgis/core/layers/support/fieldProperties.js
function s2() {
  return { fields: { type: [y], value: null }, fieldsIndex: { readOnly: true, get() {
    return new r(this.fields || []);
  } }, outFields: { type: [String], json: { read: false }, set: function(e) {
    this._userOutFields = e, this.notifyChange("outFields");
  }, get: function() {
    var _a;
    const i = this._userOutFields;
    if (!i || !i.length) return null;
    if (i.includes("*")) return ["*"];
    if (!this.fields) return i;
    for (const t of i) {
      const r2 = (_a = this.fieldsIndex) == null ? void 0 : _a.has(t);
      r2 || s.getLogger("esri.layers.support.fieldProperties").error("field-attributes-layer:invalid-field", `Invalid field ${t} found in outFields`, { layer: this, outFields: i });
    }
    return T(this.fieldsIndex, i);
  } } };
}

export {
  s2 as s
};
//# sourceMappingURL=chunk-FZKLUDSB.js.map
