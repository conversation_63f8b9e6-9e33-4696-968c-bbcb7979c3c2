{"version": 3, "sources": ["../../@arcgis/core/core/Clonable.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../chunks/tslib.es6.js\";import s from\"./Accessor.js\";import\"./has.js\";import{tryClone as t}from\"./lang.js\";import\"./Logger.js\";import{unwrapOrThrow as r}from\"./maybe.js\";import{OriginId as e}from\"./accessorSupport/PropertyOrigin.js\";import{getProperties as n}from\"./accessorSupport/utils.js\";import{subclass as c}from\"./accessorSupport/decorators/subclass.js\";const i=s=>{let i=class extends s{clone(){const o=r(n(this),\"unable to clone instance of non-accessor class\"),s=o.metadatas,c=o.store,i={},l=new Map;for(const r in s){const o=s[r],n=c?.originOf(r),a=o.clonable;if(o.readOnly||!1===a||n!==e.USER&&n!==e.DEFAULTS&&n!==e.WEB_MAP&&n!==e.WEB_SCENE)continue;const p=this[r];let f=null;f=\"function\"==typeof a?a(p):\"reference\"===a?p:t(p),null!=p&&null==f||(n===e.DEFAULTS?l.set(r,f):i[r]=f)}const a=new(0,Object.getPrototypeOf(this).constructor)(i);if(l.size){const o=n(a)?.store;if(o)for(const[s,t]of l)o.set(s,t,e.DEFAULTS)}return a}};return i=o([c(\"esri.core.Clonable\")],i),i};let l=class extends(i(s)){};l=o([c(\"esri.core.Clonable\")],l);export{l as Clonable,i as ClonableMixin};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAI2X,IAAM,IAAE,OAAG;AAAC,MAAIA,KAAE,cAAc,EAAC;AAAA,IAAC,QAAO;AAJpa;AAIqa,YAAM,IAAE,EAAEC,GAAE,IAAI,GAAE,gDAAgD,GAAEC,KAAE,EAAE,WAAUC,KAAE,EAAE,OAAMH,KAAE,CAAC,GAAEI,KAAE,oBAAI;AAAI,iBAAUC,MAAKH,IAAE;AAAC,cAAMI,KAAEJ,GAAEG,EAAC,GAAE,IAAEF,MAAA,gBAAAA,GAAG,SAASE,KAAGE,KAAED,GAAE;AAAS,YAAGA,GAAE,YAAU,UAAKC,MAAG,MAAI,EAAE,QAAM,MAAI,EAAE,YAAU,MAAI,EAAE,WAAS,MAAI,EAAE,UAAU;AAAS,cAAM,IAAE,KAAKF,EAAC;AAAE,YAAI,IAAE;AAAK,YAAE,cAAY,OAAOE,KAAEA,GAAE,CAAC,IAAE,gBAAcA,KAAE,IAAE,EAAE,CAAC,GAAE,QAAM,KAAG,QAAM,MAAI,MAAI,EAAE,WAASH,GAAE,IAAIC,IAAE,CAAC,IAAEL,GAAEK,EAAC,IAAE;AAAA,MAAE;AAAC,YAAME,KAAE,KAAI,GAAE,OAAO,eAAe,IAAI,EAAE,aAAaP,EAAC;AAAE,UAAGI,GAAE,MAAK;AAAC,cAAME,MAAE,KAAAL,GAAEM,EAAC,MAAH,mBAAM;AAAM,YAAGD,GAAE,YAAS,CAACJ,IAAE,CAAC,KAAIE,GAAE,CAAAE,GAAE,IAAIJ,IAAE,GAAE,EAAE,QAAQ;AAAA,MAAC;AAAC,aAAOK;AAAA,IAAC;AAAA,EAAC;AAAE,SAAOP,KAAE,EAAE,CAAC,EAAE,oBAAoB,CAAC,GAAEA,EAAC,GAAEA;AAAC;AAAE,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,oBAAoB,CAAC,GAAE,CAAC;", "names": ["i", "e", "s", "c", "l", "r", "o", "a"]}