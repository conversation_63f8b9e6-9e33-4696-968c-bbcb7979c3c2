{"version": 3, "sources": ["../../@arcgis/core/geometry/HeightModelInfo.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import{removeUnordered as t}from\"../core/arrayUtils.js\";import{strict as r}from\"../core/jsonMap.js\";import{JSONSupport as o}from\"../core/JSONSupport.js\";import{unitFromRESTJSON as i,getVerticalUnitStringForSR as s}from\"../core/unitUtils.js\";import n from\"../core/Warning.js\";import{property as a}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{reader as l}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as c}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as h}from\"../core/accessorSupport/decorators/writer.js\";var d;const p=r()({orthometric:\"gravity-related-height\",gravity_related_height:\"gravity-related-height\",ellipsoidal:\"ellipsoidal\"}),u=p.jsonValues.slice();t(u,\"orthometric\");const g=r()({meter:\"meters\",foot:\"feet\",\"us-foot\":\"us-feet\",\"clarke-foot\":\"clarke-feet\",\"clarke-yard\":\"clarke-yards\",\"clarke-link\":\"clarke-links\",\"sears-yard\":\"sears-yards\",\"sears-foot\":\"sears-feet\",\"sears-chain\":\"sears-chains\",\"benoit-1895-b-chain\":\"benoit-1895-b-chains\",\"indian-yard\":\"indian-yards\",\"indian-1937-yard\":\"indian-1937-yards\",\"gold-coast-foot\":\"gold-coast-feet\",\"sears-1922-truncated-chain\":\"sears-1922-truncated-chains\",\"50-kilometers\":\"50-kilometers\",\"150-kilometers\":\"150-kilometers\"});let m=d=class extends o{constructor(e){super(e),this.heightModel=\"gravity-related-height\",this.heightUnit=\"meters\",this.vertCRS=null}writeHeightModel(e,t,r){return p.write(e,t,r)}readHeightModel(e,t,r){const o=p.read(e);return o||(r&&r.messages&&r.messages.push(f(e,{context:r})),null)}readHeightUnit(e,t,r){const o=g.read(e);return o||(r&&r.messages&&r.messages.push(y(e,{context:r})),null)}readHeightUnitService(e,t,r){const o=i(e)||g.read(e);return o||(r&&r.messages&&r.messages.push(y(e,{context:r})),null)}readVertCRS(e,t){return t.vertCRS||t.ellipsoid||t.geoid}clone(){return new d({heightModel:this.heightModel,heightUnit:this.heightUnit,vertCRS:this.vertCRS})}equals(e){return!!e&&(this===e||this.heightModel===e.heightModel&&this.heightUnit===e.heightUnit&&this.vertCRS===e.vertCRS)}static deriveUnitFromSR(e,t){const r=s(t);return new d({heightModel:e.heightModel,heightUnit:r,vertCRS:e.vertCRS})}write(e,t){return t={origin:\"web-scene\",...t},super.write(e,t)}static fromJSON(e){if(!e)return null;const t=new d;return t.read(e,{origin:\"web-scene\"}),t}};function y(e,t){return new n(\"height-unit:unsupported\",`Height unit of value '${e}' is not supported`,t)}function f(e,t){return new n(\"height-model:unsupported\",`Height model of value '${e}' is not supported`,t)}e([a({type:p.apiValues,constructOnly:!0,json:{origins:{\"web-scene\":{type:u,default:\"ellipsoidal\"}}}})],m.prototype,\"heightModel\",void 0),e([h(\"web-scene\",\"heightModel\")],m.prototype,\"writeHeightModel\",null),e([l([\"web-scene\",\"service\"],\"heightModel\")],m.prototype,\"readHeightModel\",null),e([a({type:g.apiValues,constructOnly:!0,json:{origins:{\"web-scene\":{type:g.jsonValues,write:g.write}}}})],m.prototype,\"heightUnit\",void 0),e([l(\"web-scene\",\"heightUnit\")],m.prototype,\"readHeightUnit\",null),e([l(\"service\",\"heightUnit\")],m.prototype,\"readHeightUnitService\",null),e([a({type:String,constructOnly:!0,json:{origins:{\"web-scene\":{write:!0}}}})],m.prototype,\"vertCRS\",void 0),e([l(\"service\",\"vertCRS\",[\"vertCRS\",\"ellipsoid\",\"geoid\"])],m.prototype,\"readVertCRS\",null),m=d=e([c(\"esri.geometry.HeightModelInfo\")],m);const v=m;export{v as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4oB,IAAI;AAAE,IAAM,IAAEA,GAAE,EAAE,EAAC,aAAY,0BAAyB,wBAAuB,0BAAyB,aAAY,cAAa,CAAC;AAA5H,IAA8H,IAAE,EAAE,WAAW,MAAM;AAAE,EAAE,GAAE,aAAa;AAAE,IAAM,IAAEA,GAAE,EAAE,EAAC,OAAM,UAAS,MAAK,QAAO,WAAU,WAAU,eAAc,eAAc,eAAc,gBAAe,eAAc,gBAAe,cAAa,eAAc,cAAa,cAAa,eAAc,gBAAe,uBAAsB,wBAAuB,eAAc,gBAAe,oBAAmB,qBAAoB,mBAAkB,mBAAkB,8BAA6B,+BAA8B,iBAAgB,iBAAgB,kBAAiB,iBAAgB,CAAC;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,cAAY,0BAAyB,KAAK,aAAW,UAAS,KAAK,UAAQ;AAAA,EAAI;AAAA,EAAC,iBAAiBA,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAE,MAAMF,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAEC,IAAEC,IAAE;AAAC,UAAMH,KAAE,EAAE,KAAKC,EAAC;AAAE,WAAOD,OAAIG,MAAGA,GAAE,YAAUA,GAAE,SAAS,KAAK,EAAEF,IAAE,EAAC,SAAQE,GAAC,CAAC,CAAC,GAAE;AAAA,EAAK;AAAA,EAAC,eAAeF,IAAEC,IAAEC,IAAE;AAAC,UAAMH,KAAE,EAAE,KAAKC,EAAC;AAAE,WAAOD,OAAIG,MAAGA,GAAE,YAAUA,GAAE,SAAS,KAAKC,GAAEH,IAAE,EAAC,SAAQE,GAAC,CAAC,CAAC,GAAE;AAAA,EAAK;AAAA,EAAC,sBAAsBF,IAAEC,IAAEC,IAAE;AAAC,UAAMH,KAAE,EAAEC,EAAC,KAAG,EAAE,KAAKA,EAAC;AAAE,WAAOD,OAAIG,MAAGA,GAAE,YAAUA,GAAE,SAAS,KAAKC,GAAEH,IAAE,EAAC,SAAQE,GAAC,CAAC,CAAC,GAAE;AAAA,EAAK;AAAA,EAAC,YAAYF,IAAEC,IAAE;AAAC,WAAOA,GAAE,WAASA,GAAE,aAAWA,GAAE;AAAA,EAAK;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,aAAY,KAAK,aAAY,YAAW,KAAK,YAAW,SAAQ,KAAK,QAAO,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,WAAM,CAAC,CAACA,OAAI,SAAOA,MAAG,KAAK,gBAAcA,GAAE,eAAa,KAAK,eAAaA,GAAE,cAAY,KAAK,YAAUA,GAAE;AAAA,EAAQ;AAAA,EAAC,OAAO,iBAAiBA,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,WAAO,IAAI,EAAE,EAAC,aAAYD,GAAE,aAAY,YAAWE,IAAE,SAAQF,GAAE,QAAO,CAAC;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAEC,IAAE;AAAC,WAAOA,KAAE,EAAC,QAAO,aAAY,GAAGA,GAAC,GAAE,MAAM,MAAMD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,SAASD,IAAE;AAAC,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMC,KAAE,IAAI;AAAE,WAAOA,GAAE,KAAKD,IAAE,EAAC,QAAO,YAAW,CAAC,GAAEC;AAAA,EAAC;AAAC;AAAE,SAASE,GAAEH,IAAEC,IAAE;AAAC,SAAO,IAAI,EAAE,2BAA0B,yBAAyBD,EAAC,sBAAqBC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAO,IAAI,EAAE,4BAA2B,0BAA0BD,EAAC,sBAAqBC,EAAC;AAAC;AAAC,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,WAAU,eAAc,MAAG,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,GAAE,SAAQ,cAAa,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,aAAY,aAAa,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,aAAY,SAAS,GAAE,aAAa,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,WAAU,eAAc,MAAG,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,EAAE,YAAW,OAAM,EAAE,MAAK,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,aAAY,YAAY,CAAC,GAAE,EAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,WAAU,YAAY,CAAC,GAAE,EAAE,WAAU,yBAAwB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,eAAc,MAAG,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,OAAM,KAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,WAAU,CAAC,WAAU,aAAY,OAAO,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,+BAA+B,CAAC,GAAE,CAAC;AAAE,IAAMG,KAAE;", "names": ["o", "e", "t", "r", "y", "v"]}