{"version": 3, "sources": ["../../@arcgis/core/symbols/FillSymbol.js", "../../@arcgis/core/symbols/SimpleFillSymbol.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import{property as s}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as t}from\"../core/accessorSupport/decorators/subclass.js\";import o from\"./SimpleLineSymbol.js\";import r from\"./Symbol.js\";let l=class extends r{constructor(e){super(e),this.outline=null,this.type=null}hash(){return`${this.type}.${this.outline&&this.outline.hash()}`}};e([s({types:{key:\"type\",base:null,defaultKeyValue:\"simple-line\",typeMap:{\"simple-line\":o}},json:{default:null,write:!0}})],l.prototype,\"outline\",void 0),e([s({type:[\"simple-fill\",\"picture-fill\"],readOnly:!0})],l.prototype,\"type\",void 0),l=e([t(\"esri.symbols.FillSymbol\")],l);const p=l;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../chunks/tslib.es6.js\";import r from\"../Color.js\";import{JSONMap as s}from\"../core/jsonMap.js\";import{clone as e}from\"../core/lang.js\";import{property as t}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{enumeration as i}from\"../core/accessorSupport/decorators/enumeration.js\";import{subclass as l}from\"../core/accessorSupport/decorators/subclass.js\";import a from\"./FillSymbol.js\";import n from\"./SimpleLineSymbol.js\";var p;const c=new s({esriSFSSolid:\"solid\",esriSFSNull:\"none\",esriSFSHorizontal:\"horizontal\",esriSFSVertical:\"vertical\",esriSFSForwardDiagonal:\"forward-diagonal\",esriSFSBackwardDiagonal:\"backward-diagonal\",esriSFSCross:\"cross\",esriSFSDiagonalCross:\"diagonal-cross\"});let m=p=class extends a{constructor(...o){super(...o),this.color=new r([0,0,0,.25]),this.outline=new n,this.type=\"simple-fill\",this.style=\"solid\"}normalizeCtorArgs(o,r,s){if(o&&\"string\"!=typeof o)return o;const e={};return o&&(e.style=o),r&&(e.outline=r),s&&(e.color=s),e}clone(){return new p({color:e(this.color),outline:this.outline&&this.outline.clone(),style:this.style})}hash(){return`${super.hash()}${this.style}.${this.color&&this.color.hash()}`}};o([t()],m.prototype,\"color\",void 0),o([t()],m.prototype,\"outline\",void 0),o([i({esriSFS:\"simple-fill\"},{readOnly:!0})],m.prototype,\"type\",void 0),o([t({type:c.apiValues,json:{read:c.read,write:c.write}})],m.prototype,\"style\",void 0),m=p=o([l(\"esri.symbols.SimpleFillSymbol\")],m);const S=m;export{S as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2U,IAAIA,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,MAAK,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,OAAM;AAAC,WAAM,GAAG,KAAK,IAAI,IAAI,KAAK,WAAS,KAAK,QAAQ,KAAK,CAAC;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,OAAM,EAAC,KAAI,QAAO,MAAK,MAAK,iBAAgB,eAAc,SAAQ,EAAC,eAAc,EAAC,EAAC,GAAE,MAAK,EAAC,SAAQ,MAAK,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,eAAc,cAAc,GAAE,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,yBAAyB,CAAC,GAAEA,EAAC;AAAE,IAAMG,KAAEH;;;ACAvQ,IAAII;AAAE,IAAM,IAAE,IAAI,EAAE,EAAC,cAAa,SAAQ,aAAY,QAAO,mBAAkB,cAAa,iBAAgB,YAAW,wBAAuB,oBAAmB,yBAAwB,qBAAoB,cAAa,SAAQ,sBAAqB,iBAAgB,CAAC;AAAE,IAAIC,KAAED,KAAE,cAAcA,GAAC;AAAA,EAAC,eAAeE,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,QAAM,IAAI,EAAE,CAAC,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,KAAK,UAAQ,IAAI,KAAE,KAAK,OAAK,eAAc,KAAK,QAAM;AAAA,EAAO;AAAA,EAAC,kBAAkBA,IAAE,GAAEC,IAAE;AAAC,QAAGD,MAAG,YAAU,OAAOA,GAAE,QAAOA;AAAE,UAAME,KAAE,CAAC;AAAE,WAAOF,OAAIE,GAAE,QAAMF,KAAG,MAAIE,GAAE,UAAQ,IAAGD,OAAIC,GAAE,QAAMD,KAAGC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIJ,GAAE,EAAC,OAAM,EAAE,KAAK,KAAK,GAAE,SAAQ,KAAK,WAAS,KAAK,QAAQ,MAAM,GAAE,OAAM,KAAK,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAM,GAAG,MAAM,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,SAAO,KAAK,MAAM,KAAK,CAAC;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEC,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,SAAQ,cAAa,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,WAAU,MAAK,EAAC,MAAK,EAAE,MAAK,OAAM,EAAE,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,+BAA+B,CAAC,GAAEC,EAAC;AAAE,IAAM,IAAEA;", "names": ["l", "a", "e", "p", "p", "m", "o", "s", "e"]}