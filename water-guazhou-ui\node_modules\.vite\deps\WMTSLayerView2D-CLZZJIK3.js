import {
  n,
  t
} from "./chunk-Z5QBIDP2.js";
import "./chunk-6BXQDUKY.js";
import "./chunk-5L4KKQ6Z.js";
import "./chunk-R7EXIHGC.js";
import "./chunk-V5RJCYWT.js";
import "./chunk-FSU3GUUE.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-M46W6WAV.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-53FPJYCC.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-IEBU4QQL.js";
import {
  i
} from "./chunk-BNOUIPKE.js";
import {
  f
} from "./chunk-7TISIISL.js";
import {
  u
} from "./chunk-74RIZBAU.js";
import "./chunk-6W6ECZU2.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-REFSHSQW.js";
import "./chunk-RURSJOSG.js";
import "./chunk-LFDODH6Z.js";
import {
  r
} from "./chunk-ZVGXJHEK.js";
import {
  y as y2
} from "./chunk-U6IEQ6CF.js";
import {
  h
} from "./chunk-ZL54NZ7B.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-5JCRZXRL.js";
import {
  e as e2
} from "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-HDM6HCKB.js";
import "./chunk-2ILOD42U.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MCIIPWB6.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-V6NQCXYQ.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-UV4E33V4.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-RCNP3U5T.js";
import "./chunk-2CFIAWMM.js";
import "./chunk-3MWB7OGY.js";
import "./chunk-MURG32WB.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import {
  E
} from "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-IJ6FZE6K.js";
import "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2 as a,
  y
} from "./chunk-R4CPW7J5.js";
import "./chunk-2CM7MIII.js";
import "./chunk-HP475EI3.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/WMTSLayerView2D.js
var y3 = [102113, 102100, 3857, 3785, 900913];
var _ = [0, 0];
var w = class extends i(t(f(u))) {
  constructor() {
    super(...arguments), this._tileStrategy = null, this._fetchQueue = null, this._tileRequests = /* @__PURE__ */ new Map(), this.layer = null;
  }
  get tileMatrixSet() {
    const e3 = this._getTileMatrixSetBySpatialReference(this.layer.activeLayer);
    return e3 ? (e3.id !== this.layer.activeLayer.tileMatrixSetId && (this.layer.activeLayer.tileMatrixSetId = e3.id), e3) : null;
  }
  update(e3) {
    this._fetchQueue.pause(), this._fetchQueue.state = e3.state, this._tileStrategy.update(e3), this._fetchQueue.resume();
  }
  attach() {
    var _a;
    const e3 = (_a = this.tileMatrixSet) == null ? void 0 : _a.tileInfo;
    e3 && (this._tileInfoView = new h(e3), this._fetchQueue = new y2({ tileInfoView: this._tileInfoView, concurrency: 16, process: (e4, t2) => this.fetchTile(e4, t2) }), this._tileStrategy = new r({ cachePolicy: "keep", resampling: true, acquireTile: (e4) => this.acquireTile(e4), releaseTile: (e4) => this.releaseTile(e4), tileInfoView: this._tileInfoView }), this.addAttachHandles(l(() => {
      var _a2, _b;
      return [(_b = (_a2 = this.layer) == null ? void 0 : _a2.activeLayer) == null ? void 0 : _b.styleId, this.tileMatrixSet];
    }, () => this._refresh())), super.attach());
  }
  detach() {
    var _a, _b;
    super.detach(), (_a = this._tileStrategy) == null ? void 0 : _a.destroy(), (_b = this._fetchQueue) == null ? void 0 : _b.destroy(), this._fetchQueue = this._tileStrategy = this._tileInfoView = null;
  }
  moveStart() {
    this.requestUpdate();
  }
  viewChange() {
    this.requestUpdate();
  }
  moveEnd() {
    this.requestUpdate();
  }
  releaseTile(e3) {
    this._fetchQueue.abort(e3.key.id), this._bitmapView.removeChild(e3), e3.once("detach", () => e3.destroy()), this.requestUpdate();
  }
  acquireTile(e3) {
    const t2 = this._bitmapView.createTile(e3), i2 = t2.bitmap;
    return [i2.x, i2.y] = this._tileInfoView.getTileCoords(_, t2.key), i2.resolution = this._tileInfoView.getTileResolution(t2.key), [i2.width, i2.height] = this._tileInfoView.tileInfo.size, this._enqueueTileFetch(t2), this._bitmapView.addChild(t2), this.requestUpdate(), t2;
  }
  async doRefresh() {
    !this.attached || this.updateRequested || this.suspended || this._refresh();
  }
  isUpdating() {
    var _a;
    return ((_a = this._fetchQueue) == null ? void 0 : _a.updating) ?? false;
  }
  async fetchTile(e3, t2 = {}) {
    const s2 = "tilemapCache" in this.layer ? this.layer.tilemapCache : null, { signal: r2, resamplingLevel: a2 = 0 } = t2;
    if (!s2) return this._fetchImage(e3, r2);
    const l2 = new e2(0, 0, 0, 0);
    let o;
    try {
      await s2.fetchAvailabilityUpsample(e3.level, e3.row, e3.col, l2, { signal: r2 }), o = await this._fetchImage(l2, r2);
    } catch (h2) {
      if (j(h2)) throw h2;
      if (a2 < 3) {
        const i2 = this._tileInfoView.getTileParentId(e3.id);
        if (i2) {
          const s3 = new e2(i2), r3 = await this.fetchTile(s3, { ...t2, resamplingLevel: a2 + 1 });
          return n(this._tileInfoView, r3, s3, e3);
        }
      }
      throw h2;
    }
    return n(this._tileInfoView, o, l2, e3);
  }
  canResume() {
    const e3 = super.canResume();
    return e3 ? null !== this.tileMatrixSet : e3;
  }
  supportsSpatialReference(e3) {
    var _a;
    return ((_a = this.layer.activeLayer.tileMatrixSets) == null ? void 0 : _a.some((t2) => {
      var _a2;
      return E((_a2 = t2.tileInfo) == null ? void 0 : _a2.spatialReference, e3);
    })) ?? false;
  }
  async _enqueueTileFetch(e3) {
    if (!this._fetchQueue.has(e3.key.id)) {
      try {
        const t2 = await this._fetchQueue.push(e3.key);
        e3.bitmap.source = t2, e3.bitmap.width = this._tileInfoView.tileInfo.size[0], e3.bitmap.height = this._tileInfoView.tileInfo.size[1], e3.once("attach", () => this.requestUpdate());
      } catch (s2) {
        j(s2) || s.getLogger(this.declaredClass).error(s2);
      }
      this.requestUpdate();
    }
  }
  async _fetchImage(e3, t2) {
    return this.layer.fetchImageBitmapTile(e3.level, e3.row, e3.col, { signal: t2 });
  }
  _refresh() {
    this._fetchQueue.reset(), this._tileStrategy.tiles.forEach((e3) => {
      if (!e3.bitmap.source) return;
      const t2 = { id: e3.key.id, fulfilled: false, promise: this._fetchQueue.push(e3.key).then((t3) => {
        e3.bitmap.source = t3;
      }).catch((t3) => {
        j(t3) || (e3.bitmap.source = null);
      }).finally(() => {
        e3.requestRender(), t2.fulfilled = true;
      }) };
      this._tileRequests.set(e3, t2);
    });
  }
  _getTileMatrixSetBySpatialReference(e3) {
    const t2 = this.view.spatialReference;
    if (!e3.tileMatrixSets) return null;
    let i2 = e3.tileMatrixSets.find((e4) => {
      var _a;
      return E((_a = e4.tileInfo) == null ? void 0 : _a.spatialReference, t2);
    });
    return !i2 && t2.isWebMercator && (i2 = e3.tileMatrixSets.find((e4) => {
      var _a;
      return y3.includes(((_a = e4.tileInfo) == null ? void 0 : _a.spatialReference.wkid) ?? -1);
    })), i2;
  }
};
e([y()], w.prototype, "_fetchQueue", void 0), e([y({ readOnly: true })], w.prototype, "tileMatrixSet", null), w = e([a("esri.views.2d.layers.WMTSLayerView2D")], w);
var g = w;
export {
  g as default
};
//# sourceMappingURL=WMTSLayerView2D-CLZZJIK3.js.map
