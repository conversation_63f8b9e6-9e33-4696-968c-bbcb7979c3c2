import {
  A,
  E,
  E2,
  I2 as I,
  P,
  R2 as R,
  f2 as f,
  s
} from "./chunk-JXLVNWKF.js";

// node_modules/@arcgis/core/geometry/spatialReferenceEllipsoidUtils.js
var f2 = new f(I);
var l = new f(E2);
var m = new f(R);
var a = new f(A);
function c(e) {
  return e && (P(e) || E(e, l)) ? l : e && (s(e) || E(e, m)) ? m : f2;
}

export {
  f2 as f,
  a,
  c
};
//# sourceMappingURL=chunk-IKOX2HGY.js.map
