{"version": 3, "sources": ["../../@arcgis/core/portal/support/resourceUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../request.js\";import t from\"../../core/Error.js\";import{get as r,isSome as a,isNone as o}from\"../../core/maybe.js\";import{join as s,getPathExtension as n}from\"../../core/urlUtils.js\";async function c(e,t={},a){await e.load(a);const o=s(e.itemUrl,\"resources\"),{start:n=1,num:c=10,sortOrder:u=\"asc\",sortField:i=\"created\"}=t,l={query:{start:n,num:c,sortOrder:u,sortField:i,token:e.api<PERSON>ey},signal:r(a,\"signal\")},p=await e.portal.request(o,l);return{total:p.total,nextStart:p.nextStart,resources:p.resources.map((({created:t,size:r,resource:a})=>({created:new Date(t),size:r,resource:e.resourceFromPath(a)})))}}async function u(e,o,n,c){if(!e.hasPath())throw new t(`portal-item-resource-${o}:invalid-path`,\"Resource does not have a valid path\");const u=e.portalItem;await u.load(c);const i=s(u.userItemUrl,\"add\"===o?\"addResources\":\"updateResources\"),[l,d]=p(e.path),m=await h(n),f=new FormData;return l&&\".\"!==l&&f.append(\"resourcesPrefix\",l),a(c)&&c.compress&&f.append(\"compress\",\"true\"),f.append(\"fileName\",d),f.append(\"file\",m,d),f.append(\"f\",\"json\"),a(c)&&c.access&&f.append(\"access\",c.access),await u.portal.request(i,{method:\"post\",body:f,signal:r(c,\"signal\")}),e}async function i(e,a,o){if(!a.hasPath())throw new t(\"portal-item-resources-remove:invalid-path\",\"Resource does not have a valid path\");await e.load(o);const n=s(e.userItemUrl,\"removeResources\");await e.portal.request(n,{method:\"post\",query:{resource:a.path},signal:r(o,\"signal\")}),a.portalItem=null}async function l(e,t){await e.load(t);const a=s(e.userItemUrl,\"removeResources\");return e.portal.request(a,{method:\"post\",query:{deleteAll:!0},signal:r(t,\"signal\")})}function p(e){const t=e.lastIndexOf(\"/\");return-1===t?[\".\",e]:[e.slice(0,t),e.slice(t+1)]}function d(e){const[t,r]=m(e),[a,o]=p(t);return[a,o,r]}function m(e){const t=n(e);return o(t)?[e,\"\"]:[e.slice(0,e.length-t.length-1),`.${t}`]}async function h(t){if(t instanceof Blob)return t;return(await e(t.url,{responseType:\"blob\"})).data}function f(e,t){if(!e.hasPath())return null;const[r,,a]=d(e.path);return e.portalItem.resourceFromPath(s(r,t+a))}function w(e,t){if(!e.hasPath())return null;const[r,,a]=d(e.path);return e.portalItem.resourceFromPath(s(r,t+a))}export{u as addOrUpdateResource,h as contentToBlob,c as fetchResources,f as getSiblingOfSameType,w as getSiblingOfSameTypeI,l as removeAllResources,i as removeResource,d as splitPrefixFileNameAndExtension};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAIyM,eAAe,EAAE,GAAEA,KAAE,CAAC,GAAE,GAAE;AAAC,QAAM,EAAE,KAAK,CAAC;AAAE,QAAM,IAAE,EAAE,EAAE,SAAQ,WAAW,GAAE,EAAC,OAAM,IAAE,GAAE,KAAIC,KAAE,IAAG,WAAUC,KAAE,OAAM,WAAUC,KAAE,UAAS,IAAEH,IAAEI,KAAE,EAAC,OAAM,EAAC,OAAM,GAAE,KAAIH,IAAE,WAAUC,IAAE,WAAUC,IAAE,OAAM,EAAE,OAAM,GAAE,QAAO,EAAE,GAAE,QAAQ,EAAC,GAAEE,KAAE,MAAM,EAAE,OAAO,QAAQ,GAAED,EAAC;AAAE,SAAM,EAAC,OAAMC,GAAE,OAAM,WAAUA,GAAE,WAAU,WAAUA,GAAE,UAAU,IAAK,CAAC,EAAC,SAAQL,IAAE,MAAKM,IAAE,UAASC,GAAC,OAAK,EAAC,SAAQ,IAAI,KAAKP,EAAC,GAAE,MAAKM,IAAE,UAAS,EAAE,iBAAiBC,EAAC,EAAC,EAAG,EAAC;AAAC;AAAC,eAAe,EAAE,GAAE,GAAE,GAAEN,IAAE;AAAC,MAAG,CAAC,EAAE,QAAQ,EAAE,OAAM,IAAI,EAAE,wBAAwB,CAAC,iBAAgB,qCAAqC;AAAE,QAAMC,KAAE,EAAE;AAAW,QAAMA,GAAE,KAAKD,EAAC;AAAE,QAAME,KAAE,EAAED,GAAE,aAAY,UAAQ,IAAE,iBAAe,iBAAiB,GAAE,CAACE,IAAEI,EAAC,IAAE,EAAE,EAAE,IAAI,GAAEC,KAAE,MAAM,EAAE,CAAC,GAAEC,KAAE,IAAI;AAAS,SAAON,MAAG,QAAMA,MAAGM,GAAE,OAAO,mBAAkBN,EAAC,GAAE,EAAEH,EAAC,KAAGA,GAAE,YAAUS,GAAE,OAAO,YAAW,MAAM,GAAEA,GAAE,OAAO,YAAWF,EAAC,GAAEE,GAAE,OAAO,QAAOD,IAAED,EAAC,GAAEE,GAAE,OAAO,KAAI,MAAM,GAAE,EAAET,EAAC,KAAGA,GAAE,UAAQS,GAAE,OAAO,UAAST,GAAE,MAAM,GAAE,MAAMC,GAAE,OAAO,QAAQC,IAAE,EAAC,QAAO,QAAO,MAAKO,IAAE,QAAO,EAAET,IAAE,QAAQ,EAAC,CAAC,GAAE;AAAC;AAAC,eAAe,EAAE,GAAE,GAAE,GAAE;AAAC,MAAG,CAAC,EAAE,QAAQ,EAAE,OAAM,IAAI,EAAE,6CAA4C,qCAAqC;AAAE,QAAM,EAAE,KAAK,CAAC;AAAE,QAAM,IAAE,EAAE,EAAE,aAAY,iBAAiB;AAAE,QAAM,EAAE,OAAO,QAAQ,GAAE,EAAC,QAAO,QAAO,OAAM,EAAC,UAAS,EAAE,KAAI,GAAE,QAAO,EAAE,GAAE,QAAQ,EAAC,CAAC,GAAE,EAAE,aAAW;AAAI;AAAC,eAAe,EAAE,GAAED,IAAE;AAAC,QAAM,EAAE,KAAKA,EAAC;AAAE,QAAM,IAAE,EAAE,EAAE,aAAY,iBAAiB;AAAE,SAAO,EAAE,OAAO,QAAQ,GAAE,EAAC,QAAO,QAAO,OAAM,EAAC,WAAU,KAAE,GAAE,QAAO,EAAEA,IAAE,QAAQ,EAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAMA,KAAE,EAAE,YAAY,GAAG;AAAE,SAAM,OAAKA,KAAE,CAAC,KAAI,CAAC,IAAE,CAAC,EAAE,MAAM,GAAEA,EAAC,GAAE,EAAE,MAAMA,KAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAK,CAACA,IAAEM,EAAC,IAAE,EAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAE,EAAEN,EAAC;AAAE,SAAM,CAAC,GAAE,GAAEM,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAMN,KAAE,GAAE,CAAC;AAAE,SAAO,EAAEA,EAAC,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,EAAE,MAAM,GAAE,EAAE,SAAOA,GAAE,SAAO,CAAC,GAAE,IAAIA,EAAC,EAAE;AAAC;AAAC,eAAe,EAAEA,IAAE;AAAC,MAAGA,cAAa,KAAK,QAAOA;AAAE,UAAO,MAAM,EAAEA,GAAE,KAAI,EAAC,cAAa,OAAM,CAAC,GAAG;AAAI;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAG,CAAC,EAAE,QAAQ,EAAE,QAAO;AAAK,QAAK,CAACM,IAAE,EAAC,CAAC,IAAE,EAAE,EAAE,IAAI;AAAE,SAAO,EAAE,WAAW,iBAAiB,EAAEA,IAAEN,KAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAG,CAAC,EAAE,QAAQ,EAAE,QAAO;AAAK,QAAK,CAACM,IAAE,EAAC,CAAC,IAAE,EAAE,EAAE,IAAI;AAAE,SAAO,EAAE,WAAW,iBAAiB,EAAEA,IAAEN,KAAE,CAAC,CAAC;AAAC;", "names": ["t", "c", "u", "i", "l", "p", "r", "a", "d", "m", "f"]}