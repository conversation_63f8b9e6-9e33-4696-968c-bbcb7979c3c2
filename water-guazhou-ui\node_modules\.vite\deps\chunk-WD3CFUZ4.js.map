{"version": 3, "sources": ["../../@arcgis/core/widgets/Compass/CompassViewModel.js", "../../@arcgis/core/widgets/Compass/css.js", "../../@arcgis/core/widgets/Compass.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import o from\"../../core/Accessor.js\";import e from\"../../core/Handles.js\";import{removeMaybe as r}from\"../../core/maybe.js\";import{watch as i,initial as s}from\"../../core/reactiveUtils.js\";import{property as a}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as n}from\"../../core/accessorSupport/decorators/subclass.js\";import{GoToMixin as p}from\"../support/GoTo.js\";const h=\"esri.widgets.CompassViewModel\";let d=class extends(p(o)){constructor(t){super(t),this._handles=new e,this.orientation={x:0,y:0,z:0},this.view=null,this._updateForCamera=this._updateForCamera.bind(this),this._updateForRotation=this._updateForRotation.bind(this),this._updateRotationWatcher=this._updateRotationWatcher.bind(this)}initialize(){this._handles.add(i((()=>this.view),this._updateRotationWatcher,s))}destroy(){r(this._handles),this.view=null}get canShowNorth(){const t=this.get(\"view.spatialReference\");return!(!t||!t.isWebMercator&&!t.isGeographic)}get state(){return this.get(\"view.ready\")?this.canShowNorth?\"compass\":\"rotation\":\"disabled\"}reset(){if(!this.get(\"view.ready\"))return;const t={};\"2d\"===this.view?.type?t.rotation=0:t.heading=0,this.callGoTo({target:t})}_updateForRotation(t){null!=t&&(this.orientation={z:t})}_updateForCamera(t){if(!t)return;const o=-t.heading;this.orientation={x:0,y:0,z:o}}_updateRotationWatcher(t){this._handles.removeAll(),t&&this._handles.add(\"2d\"===t.type?i((()=>t?.rotation),this._updateForRotation,s):i((()=>t?.camera),this._updateForCamera,s))}};t([a({readOnly:!0})],d.prototype,\"canShowNorth\",null),t([a()],d.prototype,\"orientation\",void 0),t([a({readOnly:!0})],d.prototype,\"state\",null),t([a()],d.prototype,\"view\",void 0),d=t([n(h)],d);const c=d;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst i={base:\"esri-compass esri-widget--button esri-widget\",text:\"esri-icon-font-fallback-text\",icon:\"esri-compass__icon\",rotationIcon:\"esri-icon-dial\",northIcon:\"esri-icon-compass\",widgetIcon:\"esri-icon-locate-circled\",interactive:\"esri-interactive\",disabled:\"esri-disabled\"};export{i as CSS};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import{property as s}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as t}from\"../core/accessorSupport/decorators/subclass.js\";import o from\"./Widget.js\";import r from\"./Compass/CompassViewModel.js\";import{CSS as i}from\"./Compass/css.js\";import{accessibleHandler as a}from\"./support/decorators/accessibleHandler.js\";import{messageBundle as p}from\"./support/decorators/messageBundle.js\";import{tsx as l}from\"./support/jsxFactory.js\";import\"./support/widgetUtils.js\";let n=class extends o{constructor(e,s){super(e,s),this.iconClass=i.widgetIcon,this.messages=null,this.viewModel=new r}get goToOverride(){return this.viewModel.goToOverride}set goToOverride(e){this.viewModel.goToOverride=e}get label(){return this.messages?.widgetLabel??\"\"}set label(e){this._overrideIfSome(\"label\",e)}get view(){return this.viewModel.view}set view(e){this.viewModel.view=e}reset(){return this.viewModel.reset()}render(){const{orientation:e,state:s}=this.viewModel,t=\"disabled\"===s,o=\"compass\"===(\"rotation\"===s?\"rotation\":\"compass\"),r=t?-1:0,a={[i.disabled]:t,[i.interactive]:!t},p={[i.northIcon]:o,[i.rotationIcon]:!o},{messages:n}=this;return l(\"div\",{bind:this,class:this.classes(i.base,a),onclick:this._reset,onkeydown:this._reset,role:\"button\",tabIndex:r,\"aria-label\":n.reset,title:n.reset},l(\"span\",{\"aria-hidden\":\"true\",class:this.classes(i.icon,p),styles:this._toRotationTransform(e)}),l(\"span\",{class:i.text},n.reset))}_reset(){this.viewModel.reset()}_toRotationTransform(e){return{transform:`rotateZ(${e.z}deg)`}}};e([s()],n.prototype,\"goToOverride\",null),e([s()],n.prototype,\"iconClass\",void 0),e([s()],n.prototype,\"label\",null),e([s(),p(\"esri/widgets/Compass/t9n/Compass\")],n.prototype,\"messages\",void 0),e([s()],n.prototype,\"view\",null),e([s({type:r})],n.prototype,\"viewModel\",void 0),e([a()],n.prototype,\"_reset\",null),n=e([t(\"esri.widgets.Compass\")],n);const d=n;export{d as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIugB,IAAMA,KAAE;AAAgC,IAAI,IAAE,cAAcC,GAAE,CAAC,EAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,IAAI,KAAE,KAAK,cAAY,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,KAAK,OAAK,MAAK,KAAK,mBAAiB,KAAK,iBAAiB,KAAK,IAAI,GAAE,KAAK,qBAAmB,KAAK,mBAAmB,KAAK,IAAI,GAAE,KAAK,yBAAuB,KAAK,uBAAuB,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,SAAS,IAAI,EAAG,MAAI,KAAK,MAAM,KAAK,wBAAuB,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,MAAE,KAAK,QAAQ,GAAE,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,IAAI,eAAc;AAAC,UAAMA,KAAE,KAAK,IAAI,uBAAuB;AAAE,WAAM,EAAE,CAACA,MAAG,CAACA,GAAE,iBAAe,CAACA,GAAE;AAAA,EAAa;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,IAAI,YAAY,IAAE,KAAK,eAAa,YAAU,aAAW;AAAA,EAAU;AAAA,EAAC,QAAO;AAJlqC;AAImqC,QAAG,CAAC,KAAK,IAAI,YAAY,EAAE;AAAO,UAAMA,KAAE,CAAC;AAAE,eAAO,UAAK,SAAL,mBAAW,QAAKA,GAAE,WAAS,IAAEA,GAAE,UAAQ,GAAE,KAAK,SAAS,EAAC,QAAOA,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,YAAMA,OAAI,KAAK,cAAY,EAAC,GAAEA,GAAC;AAAA,EAAE;AAAA,EAAC,iBAAiBA,IAAE;AAAC,QAAG,CAACA,GAAE;AAAO,UAAM,IAAE,CAACA,GAAE;AAAQ,SAAK,cAAY,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAE;AAAC,SAAK,SAAS,UAAU,GAAEA,MAAG,KAAK,SAAS,IAAI,SAAOA,GAAE,OAAK,EAAG,MAAIA,MAAA,gBAAAA,GAAG,UAAU,KAAK,oBAAmB,CAAC,IAAE,EAAG,MAAIA,MAAA,gBAAAA,GAAG,QAAQ,KAAK,kBAAiB,CAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,EAAE,CAAC,EAAED,EAAC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACAjyD,IAAM,IAAE,EAAC,MAAK,gDAA+C,MAAK,gCAA+B,MAAK,sBAAqB,cAAa,kBAAiB,WAAU,qBAAoB,YAAW,4BAA2B,aAAY,oBAAmB,UAAS,gBAAe;;;ACAyU,IAAIE,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE,GAAE;AAAC,UAAMA,IAAE,CAAC,GAAE,KAAK,YAAU,EAAE,YAAW,KAAK,WAAS,MAAK,KAAK,YAAU,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,UAAU;AAAA,EAAY;AAAA,EAAC,IAAI,aAAaA,IAAE;AAAC,SAAK,UAAU,eAAaA;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAJt0B;AAIu0B,aAAO,UAAK,aAAL,mBAAe,gBAAa;AAAA,EAAE;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,gBAAgB,SAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,UAAU;AAAA,EAAI;AAAA,EAAC,IAAI,KAAKA,IAAE;AAAC,SAAK,UAAU,OAAKA;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,KAAK,UAAU,MAAM;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAK,EAAC,aAAYA,IAAE,OAAM,EAAC,IAAE,KAAK,WAAUC,KAAE,eAAa,GAAE,IAAE,eAAa,eAAa,IAAE,aAAW,YAAW,IAAEA,KAAE,KAAG,GAAEC,KAAE,EAAC,CAAC,EAAE,QAAQ,GAAED,IAAE,CAAC,EAAE,WAAW,GAAE,CAACA,GAAC,GAAEE,KAAE,EAAC,CAAC,EAAE,SAAS,GAAE,GAAE,CAAC,EAAE,YAAY,GAAE,CAAC,EAAC,GAAE,EAAC,UAASJ,GAAC,IAAE;AAAK,WAAO,EAAE,OAAM,EAAC,MAAK,MAAK,OAAM,KAAK,QAAQ,EAAE,MAAKG,EAAC,GAAE,SAAQ,KAAK,QAAO,WAAU,KAAK,QAAO,MAAK,UAAS,UAAS,GAAE,cAAaH,GAAE,OAAM,OAAMA,GAAE,MAAK,GAAE,EAAE,QAAO,EAAC,eAAc,QAAO,OAAM,KAAK,QAAQ,EAAE,MAAKI,EAAC,GAAE,QAAO,KAAK,qBAAqBH,EAAC,EAAC,CAAC,GAAE,EAAE,QAAO,EAAC,OAAM,EAAE,KAAI,GAAED,GAAE,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,UAAU,MAAM;AAAA,EAAC;AAAA,EAAC,qBAAqBC,IAAE;AAAC,WAAM,EAAC,WAAU,WAAWA,GAAE,CAAC,OAAM;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,GAAEC,GAAE,kCAAkC,CAAC,GAAED,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACE,GAAE,CAAC,GAAEF,GAAE,WAAU,UAAS,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,GAAEA,EAAC;AAAE,IAAMK,KAAEL;", "names": ["h", "t", "n", "e", "t", "a", "p", "d"]}