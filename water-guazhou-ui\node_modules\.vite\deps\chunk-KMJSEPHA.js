import {
  r,
  z
} from "./chunk-DPVQQ4NR.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/layers/mixins/ArcGISCachedService.js
var s = (s2) => {
  let l = class extends s2 {
    constructor() {
      super(...arguments), this.copyright = null, this.minScale = 0, this.maxScale = 0, this.spatialReference = null, this.tileInfo = null, this.tilemapCache = null;
    }
    readMinScale(e2, r2) {
      return null != r2.minLOD && null != r2.maxLOD ? e2 : 0;
    }
    readMaxScale(e2, r2) {
      return null != r2.minLOD && null != r2.maxLOD ? e2 : 0;
    }
    get supportsBlankTile() {
      return this.version >= 10.2;
    }
    readTilemapCache(e2, r2) {
      return r2.capabilities && r2.capabilities.includes("Tilemap") ? new z({ layer: this }) : null;
    }
  };
  return e([y({ json: { read: { source: "copyrightText" } } })], l.prototype, "copyright", void 0), e([y()], l.prototype, "minScale", void 0), e([o("service", "minScale")], l.prototype, "readMinScale", null), e([y()], l.prototype, "maxScale", void 0), e([o("service", "maxScale")], l.prototype, "readMaxScale", null), e([y({ type: f })], l.prototype, "spatialReference", void 0), e([y({ readOnly: true })], l.prototype, "supportsBlankTile", null), e([y(r)], l.prototype, "tileInfo", void 0), e([y()], l.prototype, "tilemapCache", void 0), e([o("service", "tilemapCache", ["capabilities"])], l.prototype, "readTilemapCache", null), e([y()], l.prototype, "version", void 0), l = e([a("esri.layers.mixins.ArcGISCachedService")], l), l;
};

export {
  s
};
//# sourceMappingURL=chunk-KMJSEPHA.js.map
