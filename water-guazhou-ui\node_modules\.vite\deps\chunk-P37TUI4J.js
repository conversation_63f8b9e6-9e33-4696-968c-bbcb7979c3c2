import {
  e,
  v as v2
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  E,
  v,
  w as w2
} from "./chunk-EKX3LLYN.js";
import {
  p,
  r,
  t,
  w
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/asyncUtils.js
function c(r2, t2, o) {
  return E(r2.map((r3, e2) => t2.apply(o, [r3, e2])));
}
async function h(r2, t2, o) {
  return (await E(r2.map((r3, e2) => t2.apply(o, [r3, e2])))).map((r3) => r3.value);
}
function m(r2) {
  return { ok: true, value: r2 };
}
function f(r2) {
  return { ok: false, error: r2 };
}
function _(r2) {
  return r(r2) && true === r2.ok ? r2.value : null;
}
function y2(r2) {
  return r(r2) && false === r2.ok ? r2.error : null;
}
async function b(r2) {
  if (t(r2)) return { ok: false, error: new Error("no promise provided") };
  try {
    return m(await r2);
  } catch (t2) {
    return f(t2);
  }
}
async function d(r2) {
  try {
    return m(await r2);
  } catch (t2) {
    return w2(t2), f(t2);
  }
}
function j(r2, t2) {
  return new k(r2, t2);
}
var k = class extends v2 {
  get value() {
    return _(this._result);
  }
  get error() {
    return y2(this._result);
  }
  get finished() {
    return r(this._result);
  }
  constructor(r2, t2) {
    super({}), this._result = null, this._abortHandle = null, this.abort = () => {
      this._abortController = w(this._abortController);
    }, this.remove = this.abort, this._abortController = new AbortController();
    const { signal: o } = this._abortController;
    this.promise = r2(o), this.promise.then((r3) => {
      this._result = m(r3), this._cleanup();
    }, (r3) => {
      this._result = f(r3), this._cleanup();
    }), this._abortHandle = v(t2, this.abort);
  }
  normalizeCtorArgs() {
    return {};
  }
  destroy() {
    this.abort();
  }
  _cleanup() {
    this._abortHandle = p(this._abortHandle), this._abortController = null;
  }
};
e([y()], k.prototype, "value", null), e([y()], k.prototype, "error", null), e([y()], k.prototype, "finished", null), e([y()], k.prototype, "promise", void 0), e([y()], k.prototype, "_result", void 0), k = e([a("esri.core.asyncUtils.ReactiveTask")], k);

export {
  c,
  h,
  b,
  d,
  j
};
//# sourceMappingURL=chunk-P37TUI4J.js.map
