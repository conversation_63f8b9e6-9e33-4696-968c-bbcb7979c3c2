import {
  n
} from "./chunk-WCRONQ5Z.js";
import {
  T
} from "./chunk-WAPZ634R.js";

// node_modules/@arcgis/core/views/2d/layers/graphics/GraphicContainer.js
var i = class extends n {
  renderChildren(r) {
    this.attributeView.update(), this.children.some((e) => e.hasData) && (this.attributeView.bindTextures(r.context, false), super.renderChildren(r), r.drawPhase === T.MAP && this._renderChildren(r), this.hasHighlight() && r.drawPhase === T.HIGHLIGHT && this._renderHighlight(r), this._boundsRenderer && this._boundsRenderer.doRender(r));
  }
  _renderHighlight(e) {
    const { painter: r } = e, i2 = r.effects.highlight;
    i2.bind(e), this._renderChildren(e, i2.defines), i2.draw(e), i2.unbind();
  }
};

export {
  i
};
//# sourceMappingURL=chunk-WKXLAUK5.js.map
