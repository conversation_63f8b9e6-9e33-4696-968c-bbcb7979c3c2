{"version": 3, "sources": ["../../@arcgis/core/layers/effects/EffectView.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import e from\"../../core/Accessor.js\";import has from\"../../core/has.js\";import{clone as s}from\"../../core/lang.js\";import r from\"../../core/Logger.js\";import{property as i}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import{subclass as n}from\"../../core/accessorSupport/decorators/subclass.js\";import{parse as c}from\"./parser.js\";import{canInterpolateEffects as o,normalizeEffects as f}from\"./utils.js\";const l=-1;let a=class extends e{constructor(t){super(t),this._from=null,this._to=null,this._final=null,this._current=[],this._time=0,this.duration=has(\"mapview-transitions-duration\"),this.effects=[]}set effect(t){if(this._get(\"effect\")!==(t=t||\"\")){this._set(\"effect\",t);try{this._transitionTo(h(t))}catch(e){this._transitionTo([]),r.getLogger(this.declaredClass).warn(\"Invalid Effect\",{effect:t,error:e})}}}get hasEffects(){return this.transitioning||!!this.effects.length}set scale(t){this._updateForScale(t)}get transitioning(){return null!==this._to}canTransitionTo(t){try{return this.scale>0&&u(this._current,h(t),this.scale)}catch{return!1}}transitionStep(t,e){this._applyTimeTransition(t),this._updateForScale(e)}endTransitions(){this._applyTimeTransition(this.duration)}_transitionTo(t){this.scale>0&&u(this._current,t,this.scale)?(this._final=t,this._to=s(t),_(this._current,this._to,this.scale),this._from=s(this._current),this._time=0):(this._from=this._to=this._final=null,this._current=t),this._set(\"effects\",this._current[0]?s(this._current[0].effects):[])}_applyTimeTransition(t){if(!(this._to&&this._from&&this._current&&this._final))return;this._time+=t;const e=Math.min(1,this._time/this.duration);for(let s=0;s<this._current.length;s++){const t=this._current[s],r=this._from[s],i=this._to[s];t.scale=p(r.scale,i.scale,e);for(let s=0;s<t.effects.length;s++){const n=t.effects[s],c=r.effects[s],o=i.effects[s];n.interpolate(c,o,e)}}1===e&&(this._current=this._final,this._set(\"effects\",this._current[0]?s(this._current[0].effects):[]),this._from=this._to=this._final=null)}_updateForScale(t){if(this._set(\"scale\",t),0===this._current.length)return;const e=this._current,s=this._current.length-1;let r,i,n=1;if(1===e.length||t>=e[0].scale)i=r=e[0].effects;else if(t<=e[s].scale)i=r=e[s].effects;else for(let c=0;c<s;c++){const s=e[c],o=e[c+1];if(s.scale>=t&&o.scale<=t){n=(t-s.scale)/(o.scale-s.scale),r=s.effects,i=o.effects;break}}for(let c=0;c<this.effects.length;c++){this.effects[c].interpolate(r[c],i[c],n)}}};function h(t){const e=c(t)||[];return m(e)?[{scale:l,effects:e}]:e}function u(t,e,s){if(!t[0]?.effects||!e[0]?.effects)return!0;return!((t[0]?.scale===l||e[0]?.scale===l)&&(t.length>1||e.length>1)&&s<=0)&&o(t[0].effects,e[0].effects)}function _(t,e,s){const r=t.length>e.length?t:e,i=t.length>e.length?e:t,n=i[i.length-1],c=n?.scale??s,o=n?.effects??[];for(let f=i.length;f<r.length;f++)i.push({scale:c,effects:[...o]});for(let a=0;a<r.length;a++)i[a].scale=i[a].scale===l?s:i[a].scale,r[a].scale=r[a].scale===l?s:r[a].scale,f(i[a].effects,r[a].effects)}function p(t,e,s){return t+(e-t)*s}function m(t){const e=t[0];return!!e&&\"type\"in e}t([i()],a.prototype,\"_to\",void 0),t([i()],a.prototype,\"duration\",void 0),t([i({value:\"\"})],a.prototype,\"effect\",null),t([i({readOnly:!0})],a.prototype,\"effects\",void 0),t([i({readOnly:!0})],a.prototype,\"hasEffects\",null),t([i({value:0})],a.prototype,\"scale\",null),t([i({readOnly:!0})],a.prototype,\"transitioning\",null),a=t([n(\"esri.layers.effects.EffectView\")],a);export{a as EffectView,u as canInterpolateEffectStops,h as convertEffectToStops,_ as normalizeEffectStops};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAI8f,IAAM,IAAE;AAAG,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,QAAM,MAAK,KAAK,MAAI,MAAK,KAAK,SAAO,MAAK,KAAK,WAAS,CAAC,GAAE,KAAK,QAAM,GAAE,KAAK,WAAS,IAAI,8BAA8B,GAAE,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAO,GAAE;AAAC,QAAG,KAAK,KAAK,QAAQ,OAAK,IAAE,KAAG,KAAI;AAAC,WAAK,KAAK,UAAS,CAAC;AAAE,UAAG;AAAC,aAAK,cAAc,EAAE,CAAC,CAAC;AAAA,MAAC,SAAOC,IAAE;AAAC,aAAK,cAAc,CAAC,CAAC,GAAE,EAAE,UAAU,KAAK,aAAa,EAAE,KAAK,kBAAiB,EAAC,QAAO,GAAE,OAAMA,GAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,iBAAe,CAAC,CAAC,KAAK,QAAQ;AAAA,EAAM;AAAA,EAAC,IAAI,MAAM,GAAE;AAAC,SAAK,gBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,SAAO,KAAK;AAAA,EAAG;AAAA,EAAC,gBAAgB,GAAE;AAAC,QAAG;AAAC,aAAO,KAAK,QAAM,KAAG,EAAE,KAAK,UAAS,EAAE,CAAC,GAAE,KAAK,KAAK;AAAA,IAAC,QAAM;AAAC,aAAM;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,eAAe,GAAEA,IAAE;AAAC,SAAK,qBAAqB,CAAC,GAAE,KAAK,gBAAgBA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,SAAK,qBAAqB,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,cAAc,GAAE;AAAC,SAAK,QAAM,KAAG,EAAE,KAAK,UAAS,GAAE,KAAK,KAAK,KAAG,KAAK,SAAO,GAAE,KAAK,MAAI,EAAE,CAAC,GAAE,EAAE,KAAK,UAAS,KAAK,KAAI,KAAK,KAAK,GAAE,KAAK,QAAM,EAAE,KAAK,QAAQ,GAAE,KAAK,QAAM,MAAI,KAAK,QAAM,KAAK,MAAI,KAAK,SAAO,MAAK,KAAK,WAAS,IAAG,KAAK,KAAK,WAAU,KAAK,SAAS,CAAC,IAAE,EAAE,KAAK,SAAS,CAAC,EAAE,OAAO,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAqB,GAAE;AAAC,QAAG,EAAE,KAAK,OAAK,KAAK,SAAO,KAAK,YAAU,KAAK,QAAQ;AAAO,SAAK,SAAO;AAAE,UAAMA,KAAE,KAAK,IAAI,GAAE,KAAK,QAAM,KAAK,QAAQ;AAAE,aAAQC,KAAE,GAAEA,KAAE,KAAK,SAAS,QAAOA,MAAI;AAAC,YAAMC,KAAE,KAAK,SAASD,EAAC,GAAE,IAAE,KAAK,MAAMA,EAAC,GAAE,IAAE,KAAK,IAAIA,EAAC;AAAE,MAAAC,GAAE,QAAMC,GAAE,EAAE,OAAM,EAAE,OAAMH,EAAC;AAAE,eAAQC,KAAE,GAAEA,KAAEC,GAAE,QAAQ,QAAOD,MAAI;AAAC,cAAMG,KAAEF,GAAE,QAAQD,EAAC,GAAE,IAAE,EAAE,QAAQA,EAAC,GAAE,IAAE,EAAE,QAAQA,EAAC;AAAE,QAAAG,GAAE,YAAY,GAAE,GAAEJ,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,UAAIA,OAAI,KAAK,WAAS,KAAK,QAAO,KAAK,KAAK,WAAU,KAAK,SAAS,CAAC,IAAE,EAAE,KAAK,SAAS,CAAC,EAAE,OAAO,IAAE,CAAC,CAAC,GAAE,KAAK,QAAM,KAAK,MAAI,KAAK,SAAO;AAAA,EAAK;AAAA,EAAC,gBAAgB,GAAE;AAAC,QAAG,KAAK,KAAK,SAAQ,CAAC,GAAE,MAAI,KAAK,SAAS,OAAO;AAAO,UAAMA,KAAE,KAAK,UAASC,KAAE,KAAK,SAAS,SAAO;AAAE,QAAI,GAAE,GAAEG,KAAE;AAAE,QAAG,MAAIJ,GAAE,UAAQ,KAAGA,GAAE,CAAC,EAAE,MAAM,KAAE,IAAEA,GAAE,CAAC,EAAE;AAAA,aAAgB,KAAGA,GAAEC,EAAC,EAAE,MAAM,KAAE,IAAED,GAAEC,EAAC,EAAE;AAAA,QAAa,UAAQ,IAAE,GAAE,IAAEA,IAAE,KAAI;AAAC,YAAMA,KAAED,GAAE,CAAC,GAAE,IAAEA,GAAE,IAAE,CAAC;AAAE,UAAGC,GAAE,SAAO,KAAG,EAAE,SAAO,GAAE;AAAC,QAAAG,MAAG,IAAEH,GAAE,UAAQ,EAAE,QAAMA,GAAE,QAAO,IAAEA,GAAE,SAAQ,IAAE,EAAE;AAAQ;AAAA,MAAK;AAAA,IAAC;AAAC,aAAQ,IAAE,GAAE,IAAE,KAAK,QAAQ,QAAO,KAAI;AAAC,WAAK,QAAQ,CAAC,EAAE,YAAY,EAAE,CAAC,GAAE,EAAE,CAAC,GAAEG,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAE,SAAS,EAAE,GAAE;AAAC,QAAMJ,KAAE,EAAE,CAAC,KAAG,CAAC;AAAE,SAAO,EAAEA,EAAC,IAAE,CAAC,EAAC,OAAM,GAAE,SAAQA,GAAC,CAAC,IAAEA;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAEC,IAAE;AAJnkF;AAIokF,MAAG,GAAC,OAAE,CAAC,MAAH,mBAAM,YAAS,GAAC,KAAAD,GAAE,CAAC,MAAH,mBAAM,SAAQ,QAAM;AAAG,SAAM,KAAG,OAAE,CAAC,MAAH,mBAAM,WAAQ,OAAG,KAAAA,GAAE,CAAC,MAAH,mBAAM,WAAQ,OAAK,EAAE,SAAO,KAAGA,GAAE,SAAO,MAAIC,MAAG,MAAI,EAAE,EAAE,CAAC,EAAE,SAAQD,GAAE,CAAC,EAAE,OAAO;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAE,SAAOD,GAAE,SAAO,IAAEA,IAAE,IAAE,EAAE,SAAOA,GAAE,SAAOA,KAAE,GAAEI,KAAE,EAAE,EAAE,SAAO,CAAC,GAAE,KAAEA,MAAA,gBAAAA,GAAG,UAAOH,IAAE,KAAEG,MAAA,gBAAAA,GAAG,YAAS,CAAC;AAAE,WAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,QAAO,IAAI,GAAE,KAAK,EAAC,OAAM,GAAE,SAAQ,CAAC,GAAG,CAAC,EAAC,CAAC;AAAE,WAAQL,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,GAAEA,EAAC,EAAE,QAAM,EAAEA,EAAC,EAAE,UAAQ,IAAEE,KAAE,EAAEF,EAAC,EAAE,OAAM,EAAEA,EAAC,EAAE,QAAM,EAAEA,EAAC,EAAE,UAAQ,IAAEE,KAAE,EAAEF,EAAC,EAAE,OAAMC,GAAE,EAAED,EAAC,EAAE,SAAQ,EAAEA,EAAC,EAAE,OAAO;AAAC;AAAC,SAASI,GAAE,GAAEH,IAAEC,IAAE;AAAC,SAAO,KAAGD,KAAE,KAAGC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAMD,KAAE,EAAE,CAAC;AAAE,SAAM,CAAC,CAACA,MAAG,UAASA;AAAC;AAAC,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,GAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,gCAAgC,CAAC,GAAEA,EAAC;", "names": ["a", "e", "s", "t", "p", "n"]}