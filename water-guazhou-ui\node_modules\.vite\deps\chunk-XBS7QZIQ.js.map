{"version": 3, "sources": ["../../@arcgis/core/rest/utils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"../config.js\";import{id as t}from\"../kernel.js\";import{clone as o}from\"../core/lang.js\";import{urlToObject as e}from\"../core/urlUtils.js\";import{supportsApiKey as n}from\"../support/apiKeyUtils.js\";function i(r,t){return t?{...t,query:{...r??{},...t.query}}:{query:r}}function f(r){return\"string\"==typeof r?e(r):o(r)}function s(r,t,o){const e={};for(const n in r){if(\"declaredClass\"===n)continue;const i=r[n];if(null!=i&&\"function\"!=typeof i)if(Array.isArray(i)){e[n]=[];for(let r=0;r<i.length;r++)e[n][r]=s(i[r])}else if(\"object\"==typeof i)if(i.toJSON){const r=i.toJSON(o&&o[n]);e[n]=t?r:JSON.stringify(r)}else e[n]=t?i:JSON.stringify(i);else e[n]=i}return e}function u(o,e){return n(o)&&(e||r.apiKey)?e||r.apiKey:t?.findCredential(o)?.token}export{i as asValidOptions,s as encode,u as getToken,f as parseUrl};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAImN,SAAS,EAAEA,IAAE,GAAE;AAAC,SAAO,IAAE,EAAC,GAAG,GAAE,OAAM,EAAC,GAAGA,MAAG,CAAC,GAAE,GAAG,EAAE,MAAK,EAAC,IAAE,EAAC,OAAMA,GAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAM,YAAU,OAAOA,KAAE,EAAEA,EAAC,IAAE,EAAEA,EAAC;AAAC;AAAC,SAASC,GAAED,IAAE,GAAE,GAAE;AAAC,QAAM,IAAE,CAAC;AAAE,aAAU,KAAKA,IAAE;AAAC,QAAG,oBAAkB,EAAE;AAAS,UAAME,KAAEF,GAAE,CAAC;AAAE,QAAG,QAAME,MAAG,cAAY,OAAOA,GAAE,KAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,QAAE,CAAC,IAAE,CAAC;AAAE,eAAQF,KAAE,GAAEA,KAAEE,GAAE,QAAOF,KAAI,GAAE,CAAC,EAAEA,EAAC,IAAEC,GAAEC,GAAEF,EAAC,CAAC;AAAA,IAAC,WAAS,YAAU,OAAOE,GAAE,KAAGA,GAAE,QAAO;AAAC,YAAMF,KAAEE,GAAE,OAAO,KAAG,EAAE,CAAC,CAAC;AAAE,QAAE,CAAC,IAAE,IAAEF,KAAE,KAAK,UAAUA,EAAC;AAAA,IAAC,MAAM,GAAE,CAAC,IAAE,IAAEE,KAAE,KAAK,UAAUA,EAAC;AAAA,QAAO,GAAE,CAAC,IAAEA;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAJhrB;AAIirB,SAAO,EAAE,CAAC,MAAI,KAAG,EAAE,UAAQ,KAAG,EAAE,UAAO,WAAAF,OAAA,mBAAG,eAAe,OAAlB,mBAAsB;AAAK;", "names": ["r", "s", "i"]}