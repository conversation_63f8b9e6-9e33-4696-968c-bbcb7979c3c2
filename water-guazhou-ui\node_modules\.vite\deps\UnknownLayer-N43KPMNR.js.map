{"version": 3, "sources": ["../../@arcgis/core/layers/UnknownLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../chunks/tslib.es6.js\";import o from\"../core/Error.js\";import{MultiOriginJSONMixin as e}from\"../core/MultiOriginJSONSupport.js\";import{schedule as s}from\"../core/scheduling.js\";import{property as t}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as n}from\"../core/accessorSupport/decorators/subclass.js\";import i from\"./Layer.js\";import{PortalLayer as p}from\"./mixins/PortalLayer.js\";let a=class extends(p(e(i))){constructor(r){super(r),this.resourceInfo=null,this.type=\"unknown\"}initialize(){this.addResolvingPromise(new Promise(((r,e)=>{s((()=>{const r=this.resourceInfo&&(this.resourceInfo.layerType||this.resourceInfo.type);let s=\"Unknown layer type\";r&&(s+=\" \"+r),e(new o(\"layer:unknown-layer-type\",s,{layerType:r}))}))})))}read(r,o){super.read({resourceInfo:r},o)}write(){return null}};r([t({readOnly:!0})],a.prototype,\"resourceInfo\",void 0),r([t({type:[\"show\",\"hide\"]})],a.prototype,\"listMode\",void 0),r([t({json:{read:!1},readOnly:!0,value:\"unknown\"})],a.prototype,\"type\",void 0),a=r([n(\"esri.layers.UnknownLayer\")],a);const c=a;export{c as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIqf,IAAIA,KAAE,cAAc,EAAE,EAAE,CAAC,CAAC,EAAE;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,eAAa,MAAK,KAAK,OAAK;AAAA,EAAS;AAAA,EAAC,aAAY;AAAC,SAAK,oBAAoB,IAAI,QAAS,CAAC,GAAEC,OAAI;AAAC,QAAG,MAAI;AAAC,cAAMC,KAAE,KAAK,iBAAe,KAAK,aAAa,aAAW,KAAK,aAAa;AAAM,YAAIC,KAAE;AAAqB,QAAAD,OAAIC,MAAG,MAAID,KAAGD,GAAE,IAAI,EAAE,4BAA2BE,IAAE,EAAC,WAAUD,GAAC,CAAC,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,KAAK,GAAE,GAAE;AAAC,UAAM,KAAK,EAAC,cAAa,EAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEF,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,MAAG,OAAM,UAAS,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,0BAA0B,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["a", "e", "r", "s"]}