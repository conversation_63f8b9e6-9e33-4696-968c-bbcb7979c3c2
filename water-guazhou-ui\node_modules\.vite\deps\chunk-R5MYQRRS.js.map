{"version": 3, "sources": ["../../@arcgis/core/geometry/support/webMercatorUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as n}from\"../../core/lang.js\";import{isNone as t}from\"../../core/maybe.js\";import e from\"../SpatialReference.js\";import{earth as i}from\"./Ellipsoid.js\";import{equals as r,isWebMercator as s,isWGS84 as a}from\"./spatialReferenceUtils.js\";const o=57.29577951308232,u=.017453292519943;function l(n){return n*o}function p(n){return n*u}function f(n){return n/i.radius}function c(n){return Math.PI/2-2*Math.atan(Math.exp(-n/i.radius))}function h(n){return null!=n.wkid||null!=n.wkt}const m=[0,0];function x(n,t,e,i,r){const s=n,a=r;if(a.spatialReference=e,\"x\"in s&&\"x\"in a)[a.x,a.y]=t(s.x,s.y,m,i);else if(\"xmin\"in s&&\"xmin\"in a)[a.xmin,a.ymin]=t(s.xmin,s.ymin,m,i),[a.xmax,a.ymax]=t(s.xmax,s.ymax,m,i);else if(\"paths\"in s&&\"paths\"in a||\"rings\"in s&&\"rings\"in a){const n=\"paths\"in s?s.paths:s.rings,e=[];let r;for(let s=0;s<n.length;s++){const a=n[s];r=[],e.push(r);for(let n=0;n<a.length;n++)r.push(t(a[n][0],a[n][1],[0,0],i)),a[n].length>2&&r[n].push(a[n][2]),a[n].length>3&&r[n].push(a[n][3])}\"paths\"in a?a.paths=e:a.rings=e}else if(\"points\"in s&&\"points\"in a){const n=s.points,e=[];for(let r=0;r<n.length;r++)e[r]=t(n[r][0],n[r][1],[0,0],i),n[r].length>2&&e[r].push(n[r][2]),n[r].length>3&&e[r].push(n[r][3]);a.points=e}return r}function g(n,t){const e=n&&(h(n)?n:n.spatialReference),i=t&&(h(t)?t:t.spatialReference);return!(n&&\"type\"in n&&\"mesh\"===n.type||t&&\"type\"in t&&\"mesh\"===t.type||!e||!i)&&(!!r(i,e)||(s(i)&&a(e)||s(e)&&a(i)))}function M(i,o){if(t(i))return null;const u=i.spatialReference,l=o&&(h(o)?o:o.spatialReference);return g(u,l)?r(u,l)?n(i):s(l)?x(i,y,e.WebMercator,!1,n(i)):a(l)?x(i,d,e.WGS84,!1,n(i)):null:null}function y(n,t,e=[0,0]){t>89.99999?t=89.99999:t<-89.99999&&(t=-89.99999);const r=p(t);return e[0]=p(n)*i.radius,e[1]=i.halfSemiMajorAxis*Math.log((1+Math.sin(r))/(1-Math.sin(r))),e}function d(n,t,e=[0,0],r=!1){const s=l(n/i.radius);return e[0]=r?s:s-360*Math.floor((s+180)/360),e[1]=l(Math.PI/2-2*Math.atan(Math.exp(-t/i.radius))),e}function R(t,i=!1,r=n(t)){return x(t,y,e.WebMercator,i,r)}function j(t,i=!1,r=n(t)){return x(t,d,e.WGS84,i,r)}export{g as canProject,R as geographicToWebMercator,y as lngLatToXY,M as project,j as webMercatorToGeographic,f as x2lon,d as xyToLngLat,c as y2lat};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAIyP,IAAM,IAAE;AAAR,IAA0B,IAAE;AAAiB,SAAS,EAAE,GAAE;AAAC,SAAO,IAAE;AAAC;AAAC,SAASA,GAAE,GAAE;AAAC,SAAO,IAAE;AAAC;AAAC,SAASC,GAAE,GAAE;AAAC,SAAO,IAAE,EAAE;AAAM;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,KAAK,KAAG,IAAE,IAAE,KAAK,KAAK,KAAK,IAAI,CAAC,IAAE,EAAE,MAAM,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,QAAM,EAAE,QAAM,QAAM,EAAE;AAAG;AAAC,IAAM,IAAE,CAAC,GAAE,CAAC;AAAE,SAAS,EAAE,GAAEC,IAAE,GAAE,GAAE,GAAE;AAAC,QAAMC,KAAE,GAAE,IAAE;AAAE,MAAG,EAAE,mBAAiB,GAAE,OAAMA,MAAG,OAAM,EAAE,EAAC,EAAE,GAAE,EAAE,CAAC,IAAED,GAAEC,GAAE,GAAEA,GAAE,GAAE,GAAE,CAAC;AAAA,WAAU,UAASA,MAAG,UAAS,EAAE,EAAC,EAAE,MAAK,EAAE,IAAI,IAAED,GAAEC,GAAE,MAAKA,GAAE,MAAK,GAAE,CAAC,GAAE,CAAC,EAAE,MAAK,EAAE,IAAI,IAAED,GAAEC,GAAE,MAAKA,GAAE,MAAK,GAAE,CAAC;AAAA,WAAU,WAAUA,MAAG,WAAU,KAAG,WAAUA,MAAG,WAAU,GAAE;AAAC,UAAMC,KAAE,WAAUD,KAAEA,GAAE,QAAMA,GAAE,OAAME,KAAE,CAAC;AAAE,QAAIC;AAAE,aAAQH,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAI;AAAC,YAAMI,KAAEH,GAAED,EAAC;AAAE,MAAAG,KAAE,CAAC,GAAED,GAAE,KAAKC,EAAC;AAAE,eAAQF,KAAE,GAAEA,KAAEG,GAAE,QAAOH,KAAI,CAAAE,GAAE,KAAKJ,GAAEK,GAAEH,EAAC,EAAE,CAAC,GAAEG,GAAEH,EAAC,EAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAEG,GAAEH,EAAC,EAAE,SAAO,KAAGE,GAAEF,EAAC,EAAE,KAAKG,GAAEH,EAAC,EAAE,CAAC,CAAC,GAAEG,GAAEH,EAAC,EAAE,SAAO,KAAGE,GAAEF,EAAC,EAAE,KAAKG,GAAEH,EAAC,EAAE,CAAC,CAAC;AAAA,IAAC;AAAC,eAAU,IAAE,EAAE,QAAMC,KAAE,EAAE,QAAMA;AAAA,EAAC,WAAS,YAAWF,MAAG,YAAW,GAAE;AAAC,UAAMC,KAAED,GAAE,QAAOE,KAAE,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAD,GAAEC,EAAC,IAAEJ,GAAEE,GAAEE,EAAC,EAAE,CAAC,GAAEF,GAAEE,EAAC,EAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAEF,GAAEE,EAAC,EAAE,SAAO,KAAGD,GAAEC,EAAC,EAAE,KAAKF,GAAEE,EAAC,EAAE,CAAC,CAAC,GAAEF,GAAEE,EAAC,EAAE,SAAO,KAAGD,GAAEC,EAAC,EAAE,KAAKF,GAAEE,EAAC,EAAE,CAAC,CAAC;AAAE,MAAE,SAAOD;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAEH,IAAE;AAAC,QAAM,IAAE,MAAI,EAAE,CAAC,IAAE,IAAE,EAAE,mBAAkB,IAAEA,OAAI,EAAEA,EAAC,IAAEA,KAAEA,GAAE;AAAkB,SAAM,EAAE,KAAG,UAAS,KAAG,WAAS,EAAE,QAAMA,MAAG,UAASA,MAAG,WAASA,GAAE,QAAM,CAAC,KAAG,CAAC,OAAK,CAAC,CAAC,EAAE,GAAE,CAAC,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,EAAE,CAAC;AAAG;AAAC,SAAS,EAAE,GAAEM,IAAE;AAAC,MAAG,EAAE,CAAC,EAAE,QAAO;AAAK,QAAMC,KAAE,EAAE,kBAAiBC,KAAEF,OAAI,EAAEA,EAAC,IAAEA,KAAEA,GAAE;AAAkB,SAAO,EAAEC,IAAEC,EAAC,IAAE,EAAED,IAAEC,EAAC,IAAE,EAAE,CAAC,IAAE,EAAEA,EAAC,IAAE,EAAE,GAAE,GAAE,EAAE,aAAY,OAAG,EAAE,CAAC,CAAC,IAAE,EAAEA,EAAC,IAAE,EAAE,GAAE,GAAE,EAAE,OAAM,OAAG,EAAE,CAAC,CAAC,IAAE,OAAK;AAAI;AAAC,SAAS,EAAE,GAAER,IAAE,IAAE,CAAC,GAAE,CAAC,GAAE;AAAC,EAAAA,KAAE,WAASA,KAAE,WAASA,KAAE,cAAYA,KAAE;AAAW,QAAM,IAAEF,GAAEE,EAAC;AAAE,SAAO,EAAE,CAAC,IAAEF,GAAE,CAAC,IAAE,EAAE,QAAO,EAAE,CAAC,IAAE,EAAE,oBAAkB,KAAK,KAAK,IAAE,KAAK,IAAI,CAAC,MAAI,IAAE,KAAK,IAAI,CAAC,EAAE,GAAE;AAAC;AAAC,SAAS,EAAE,GAAEE,IAAE,IAAE,CAAC,GAAE,CAAC,GAAE,IAAE,OAAG;AAAC,QAAMC,KAAE,EAAE,IAAE,EAAE,MAAM;AAAE,SAAO,EAAE,CAAC,IAAE,IAAEA,KAAEA,KAAE,MAAI,KAAK,OAAOA,KAAE,OAAK,GAAG,GAAE,EAAE,CAAC,IAAE,EAAE,KAAK,KAAG,IAAE,IAAE,KAAK,KAAK,KAAK,IAAI,CAACD,KAAE,EAAE,MAAM,CAAC,CAAC,GAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,IAAE,OAAG,IAAE,EAAEA,EAAC,GAAE;AAAC,SAAO,EAAEA,IAAE,GAAE,EAAE,aAAY,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE,IAAE,OAAG,IAAE,EAAEA,EAAC,GAAE;AAAC,SAAO,EAAEA,IAAE,GAAE,EAAE,OAAM,GAAE,CAAC;AAAC;", "names": ["p", "f", "t", "s", "n", "e", "r", "a", "o", "u", "l"]}