{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/ImageryBitmapSource.js", "../../@arcgis/core/views/2d/engine/Bitmap.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e,isNone as t}from\"../../../core/maybe.js\";class l{constructor(e,t,l){this.pixelBlock=e,this.extent=t,this.originalPixelBlock=l}get width(){return e(this.pixelBlock)?this.pixelBlock.width:0}get height(){return e(this.pixelBlock)?this.pixelBlock.height:0}render(e){const l=this.pixelBlock;if(t(l))return;const i=this.filter({extent:this.extent,pixelBlock:this.originalPixelBlock??l});if(t(i.pixelBlock))return;i.pixelBlock.maskIsAlpha&&(i.pixelBlock.premultiplyAlpha=!0);const o=i.pixelBlock.getAsRGBA(),s=e.createImageData(i.pixelBlock.width,i.pixelBlock.height);s.data.set(o),e.putImageData(s,0,0)}getRenderedRasterPixels(){const e=this.filter({extent:this.extent,pixelBlock:this.pixelBlock});return t(e.pixelBlock)?null:(e.pixelBlock.maskIsAlpha&&(e.pixelBlock.premultiplyAlpha=!0),{width:e.pixelBlock.width,height:e.pixelBlock.height,renderedRasterPixels:new Uint8Array(e.pixelBlock.getAsRGBA().buffer)})}}export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,applySome as e}from\"../../../core/maybe.js\";import{createResolver as s,onAbortOrThrow as i,throwIfNotAbortError as r}from\"../../../core/promiseUtils.js\";import{g as h,h as o,r as u,k as a,m as n}from\"../../../chunks/mat3.js\";import{c as d}from\"../../../chunks/mat3f32.js\";import{f as l}from\"../../../chunks/vec2f32.js\";import{DisplayObject as c}from\"./DisplayObject.js\";import _ from\"./ImageryBitmapSource.js\";import{ContextType as m}from\"../../webgl/context-util.js\";import{TextureType as p,PixelFormat as g,SizedPixelFormat as x,PixelType as f,TextureWrapMode as b}from\"../../webgl/enums.js\";import{Texture as w}from\"../../webgl/Texture.js\";function T(t){return t&&\"render\"in t}function S(t){const e=document.createElement(\"canvas\");return e.width=t.width,e.height=t.height,t.render(e.getContext(\"2d\")),e}function v(t){return T(t)?t instanceof _?e(t.getRenderedRasterPixels(),(t=>t.renderedRasterPixels)):S(t):t}class R extends c{constructor(t=null,e){super(),this.blendFunction=\"standard\",this._sourceWidth=0,this._sourceHeight=0,this._textureInvalidated=!1,this._texture=null,this.stencilRef=0,this.coordScale=[1,1],this._height=void 0,this.pixelRatio=1,this.resolution=0,this.rotation=0,this._source=null,this._width=void 0,this.x=0,this.y=0,this.immutable=e.immutable??!1,this.requestRenderOnSourceChangedEnabled=e.requestRenderOnSourceChangedEnabled??!0,this.source=t,this.requestRender=this.requestRender.bind(this)}destroy(){this._texture&&(this._texture.dispose(),this._texture=null),t(this._uploadStatus)&&(this._uploadStatus.controller.abort(),this._uploadStatus=null)}get isSourceScaled(){return this.width!==this._sourceWidth||this.height!==this._sourceHeight}get height(){return void 0!==this._height?this._height:this._sourceHeight}set height(t){this._height=t}get source(){return this._source}set source(t){null==t&&null==this._source||(this._source=t,this._source instanceof HTMLImageElement?(this._sourceHeight=this._source.naturalHeight,this._sourceWidth=this._source.naturalWidth):this._source&&(this._sourceHeight=this._source.height,this._sourceWidth=this._source.width),this.invalidateTexture())}get width(){return void 0!==this._width?this._width:this._sourceWidth}set width(t){this._width=t}beforeRender(t){super.beforeRender(t),this.updateTexture(t)}async setSourceAsync(e,r){t(this._uploadStatus)&&this._uploadStatus.controller.abort();const h=new AbortController,o=s();return i(r,(()=>h.abort())),i(h,(t=>o.reject(t))),this._uploadStatus={controller:h,resolver:o},this.source=e,o.promise}invalidateTexture(){this._textureInvalidated||(this._textureInvalidated=!0,this.requestRenderOnSourceChangedEnabled&&this.requestRender())}updateTransitionProperties(t,e){t>=64&&(this.fadeTransitionEnabled=!1,this.inFadeTransition=!1),super.updateTransitionProperties(t,e)}setTransform(t){const e=h(this.transforms.dvs),[s,i]=t.toScreenNoRotation([0,0],[this.x,this.y]),r=this.resolution/this.pixelRatio/t.resolution,d=r*this.width,c=r*this.height,_=Math.PI*this.rotation/180;o(e,e,l(s,i)),o(e,e,l(d/2,c/2)),u(e,e,-_),o(e,e,l(-d/2,-c/2)),a(e,e,l(d,c)),n(this.transforms.dvs,t.displayViewMat3,e)}setSamplingProfile(t){this._texture&&(t.mips&&!this._texture.descriptor.hasMipmap&&this._texture.generateMipmap(),this._texture.setSamplingMode(t.samplingMode))}bind(t,e){this._texture&&t.bindTexture(this._texture,e)}async updateTexture({context:e,painter:s}){if(!this._textureInvalidated)return;if(this._textureInvalidated=!1,this._texture||(this._texture=this._createTexture(e)),!this.source)return void this._texture.setData(null);this._texture.resize(this._sourceWidth,this._sourceHeight);const i=v(this.source);try{if(t(this._uploadStatus)){const{controller:t,resolver:e}=this._uploadStatus,r={signal:t.signal},{width:h,height:o}=this,u=this._texture,a=s.textureUploadManager;await a.enqueueTextureUpdate({data:i,texture:u,width:h,height:o},r),e.resolve(),this._uploadStatus=null}else this._texture.setData(i);this.ready()}catch(h){r(h)}}onDetach(){this.destroy()}_createTransforms(){return{dvs:d()}}_createTexture(t){const e=this.immutable&&t.type===m.WEBGL2;return new w(t,{target:p.TEXTURE_2D,pixelFormat:g.RGBA,internalFormat:e?x.RGBA8:g.RGBA,dataType:f.UNSIGNED_BYTE,wrapMode:b.CLAMP_TO_EDGE,isImmutable:e,width:this._sourceWidth,height:this._sourceHeight})}}export{R as Bitmap,T as isImageSource,S as rasterize};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4D,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,SAAK,aAAWF,IAAE,KAAK,SAAOC,IAAE,KAAK,qBAAmBC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,EAAE,KAAK,UAAU,IAAE,KAAK,WAAW,QAAM;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,EAAE,KAAK,UAAU,IAAE,KAAK,WAAW,SAAO;AAAA,EAAC;AAAA,EAAC,OAAOF,IAAE;AAAC,UAAME,KAAE,KAAK;AAAW,QAAG,EAAEA,EAAC,EAAE;AAAO,UAAMC,KAAE,KAAK,OAAO,EAAC,QAAO,KAAK,QAAO,YAAW,KAAK,sBAAoBD,GAAC,CAAC;AAAE,QAAG,EAAEC,GAAE,UAAU,EAAE;AAAO,IAAAA,GAAE,WAAW,gBAAcA,GAAE,WAAW,mBAAiB;AAAI,UAAMC,KAAED,GAAE,WAAW,UAAU,GAAE,IAAEH,GAAE,gBAAgBG,GAAE,WAAW,OAAMA,GAAE,WAAW,MAAM;AAAE,MAAE,KAAK,IAAIC,EAAC,GAAEJ,GAAE,aAAa,GAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,UAAMA,KAAE,KAAK,OAAO,EAAC,QAAO,KAAK,QAAO,YAAW,KAAK,WAAU,CAAC;AAAE,WAAO,EAAEA,GAAE,UAAU,IAAE,QAAMA,GAAE,WAAW,gBAAcA,GAAE,WAAW,mBAAiB,OAAI,EAAC,OAAMA,GAAE,WAAW,OAAM,QAAOA,GAAE,WAAW,QAAO,sBAAqB,IAAI,WAAWA,GAAE,WAAW,UAAU,EAAE,MAAM,EAAC;AAAA,EAAE;AAAC;;;ACAvQ,SAAS,EAAEK,IAAE;AAAC,SAAOA,MAAG,YAAWA;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAE,SAAS,cAAc,QAAQ;AAAE,SAAOA,GAAE,QAAMD,GAAE,OAAMC,GAAE,SAAOD,GAAE,QAAOA,GAAE,OAAOC,GAAE,WAAW,IAAI,CAAC,GAAEA;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,EAAEA,EAAC,IAAEA,cAAa,IAAE,EAAEA,GAAE,wBAAwB,GAAG,CAAAA,OAAGA,GAAE,oBAAqB,IAAE,EAAEA,EAAC,IAAEA;AAAC;AAAC,IAAM,IAAN,cAAgBE,GAAC;AAAA,EAAC,YAAYF,KAAE,MAAKC,IAAE;AAAC,UAAM,GAAE,KAAK,gBAAc,YAAW,KAAK,eAAa,GAAE,KAAK,gBAAc,GAAE,KAAK,sBAAoB,OAAG,KAAK,WAAS,MAAK,KAAK,aAAW,GAAE,KAAK,aAAW,CAAC,GAAE,CAAC,GAAE,KAAK,UAAQ,QAAO,KAAK,aAAW,GAAE,KAAK,aAAW,GAAE,KAAK,WAAS,GAAE,KAAK,UAAQ,MAAK,KAAK,SAAO,QAAO,KAAK,IAAE,GAAE,KAAK,IAAE,GAAE,KAAK,YAAUA,GAAE,aAAW,OAAG,KAAK,sCAAoCA,GAAE,uCAAqC,MAAG,KAAK,SAAOD,IAAE,KAAK,gBAAc,KAAK,cAAc,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,aAAW,KAAK,SAAS,QAAQ,GAAE,KAAK,WAAS,OAAM,EAAE,KAAK,aAAa,MAAI,KAAK,cAAc,WAAW,MAAM,GAAE,KAAK,gBAAc;AAAA,EAAK;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,UAAQ,KAAK,gBAAc,KAAK,WAAS,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,WAAS,KAAK,UAAQ,KAAK,UAAQ,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,OAAOA,IAAE;AAAC,SAAK,UAAQA;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,OAAOA,IAAE;AAAC,YAAMA,MAAG,QAAM,KAAK,YAAU,KAAK,UAAQA,IAAE,KAAK,mBAAmB,oBAAkB,KAAK,gBAAc,KAAK,QAAQ,eAAc,KAAK,eAAa,KAAK,QAAQ,gBAAc,KAAK,YAAU,KAAK,gBAAc,KAAK,QAAQ,QAAO,KAAK,eAAa,KAAK,QAAQ,QAAO,KAAK,kBAAkB;AAAA,EAAE;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,WAAS,KAAK,SAAO,KAAK,SAAO,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,SAAOA;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAM,aAAaA,EAAC,GAAE,KAAK,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeC,IAAEC,IAAE;AAAC,MAAE,KAAK,aAAa,KAAG,KAAK,cAAc,WAAW,MAAM;AAAE,UAAMC,KAAE,IAAI,mBAAgBC,KAAE,EAAE;AAAE,WAAO,EAAEF,IAAG,MAAIC,GAAE,MAAM,CAAE,GAAE,EAAEA,IAAG,CAAAH,OAAGI,GAAE,OAAOJ,EAAC,CAAE,GAAE,KAAK,gBAAc,EAAC,YAAWG,IAAE,UAASC,GAAC,GAAE,KAAK,SAAOH,IAAEG,GAAE;AAAA,EAAO;AAAA,EAAC,oBAAmB;AAAC,SAAK,wBAAsB,KAAK,sBAAoB,MAAG,KAAK,uCAAqC,KAAK,cAAc;AAAA,EAAE;AAAA,EAAC,2BAA2BJ,IAAEC,IAAE;AAAC,IAAAD,MAAG,OAAK,KAAK,wBAAsB,OAAG,KAAK,mBAAiB,QAAI,MAAM,2BAA2BA,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAE;AAAC,UAAMC,KAAEC,GAAE,KAAK,WAAW,GAAG,GAAE,CAAC,GAAEG,EAAC,IAAEL,GAAE,mBAAmB,CAAC,GAAE,CAAC,GAAE,CAAC,KAAK,GAAE,KAAK,CAAC,CAAC,GAAEE,KAAE,KAAK,aAAW,KAAK,aAAWF,GAAE,YAAWM,KAAEJ,KAAE,KAAK,OAAM,IAAEA,KAAE,KAAK,QAAO,IAAE,KAAK,KAAG,KAAK,WAAS;AAAI,IAAAK,GAAEN,IAAEA,IAAED,GAAE,GAAEK,EAAC,CAAC,GAAEE,GAAEN,IAAEA,IAAED,GAAEM,KAAE,GAAE,IAAE,CAAC,CAAC,GAAE,EAAEL,IAAEA,IAAE,CAAC,CAAC,GAAEM,GAAEN,IAAEA,IAAED,GAAE,CAACM,KAAE,GAAE,CAAC,IAAE,CAAC,CAAC,GAAEE,GAAEP,IAAEA,IAAED,GAAEM,IAAE,CAAC,CAAC,GAAE,EAAE,KAAK,WAAW,KAAIN,GAAE,iBAAgBC,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAE;AAAC,SAAK,aAAWA,GAAE,QAAM,CAAC,KAAK,SAAS,WAAW,aAAW,KAAK,SAAS,eAAe,GAAE,KAAK,SAAS,gBAAgBA,GAAE,YAAY;AAAA,EAAE;AAAA,EAAC,KAAKA,IAAEC,IAAE;AAAC,SAAK,YAAUD,GAAE,YAAY,KAAK,UAASC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,cAAc,EAAC,SAAQA,IAAE,SAAQ,EAAC,GAAE;AAAC,QAAG,CAAC,KAAK,oBAAoB;AAAO,QAAG,KAAK,sBAAoB,OAAG,KAAK,aAAW,KAAK,WAAS,KAAK,eAAeA,EAAC,IAAG,CAAC,KAAK,OAAO,QAAO,KAAK,KAAK,SAAS,QAAQ,IAAI;AAAE,SAAK,SAAS,OAAO,KAAK,cAAa,KAAK,aAAa;AAAE,UAAMI,KAAE,EAAE,KAAK,MAAM;AAAE,QAAG;AAAC,UAAG,EAAE,KAAK,aAAa,GAAE;AAAC,cAAK,EAAC,YAAWL,IAAE,UAASC,GAAC,IAAE,KAAK,eAAcC,KAAE,EAAC,QAAOF,GAAE,OAAM,GAAE,EAAC,OAAMG,IAAE,QAAOC,GAAC,IAAE,MAAK,IAAE,KAAK,UAAS,IAAE,EAAE;AAAqB,cAAM,EAAE,qBAAqB,EAAC,MAAKC,IAAE,SAAQ,GAAE,OAAMF,IAAE,QAAOC,GAAC,GAAEF,EAAC,GAAED,GAAE,QAAQ,GAAE,KAAK,gBAAc;AAAA,MAAI,MAAM,MAAK,SAAS,QAAQI,EAAC;AAAE,WAAK,MAAM;AAAA,IAAC,SAAOF,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAI,EAAE,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeH,IAAE;AAAC,UAAMC,KAAE,KAAK,aAAWD,GAAE,SAAOE,GAAE;AAAO,WAAO,IAAI,EAAEF,IAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAeC,KAAE,EAAE,QAAM,EAAE,MAAK,UAAS,EAAE,eAAc,UAASQ,GAAE,eAAc,aAAYR,IAAE,OAAM,KAAK,cAAa,QAAO,KAAK,cAAa,CAAC;AAAA,EAAC;AAAC;", "names": ["e", "t", "l", "i", "o", "t", "e", "r", "h", "o", "i", "d", "M", "b", "D"]}