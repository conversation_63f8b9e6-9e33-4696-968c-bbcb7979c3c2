{"version": 3, "sources": ["../../@arcgis/core/arcade/functions/geomsync.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{version as n}from\"../../kernel.js\";import{cloneGeometry as e,convertSquareUnitsToCode as t,convertLinearUnitsToCode as r}from\"../kernel.js\";import{G as i,y as a,k as l,j as o,m as f,x as s,q as u,J as c,A as m,H as h,K as w,g as p,h as d}from\"../../chunks/languageUtils.js\";import{centroidPolyline as g,centroidMultiPoint as P,getMetersPerVerticalUnitForSR as v,segmentLength3d as I}from\"./centroid.js\";import y from\"../../geometry/Extent.js\";import R from\"../../geometry/Geometry.js\";import x from\"../../geometry/Multipoint.js\";import N from\"../../geometry/Point.js\";import j from\"../../geometry/Polygon.js\";import L from\"../../geometry/Polyline.js\";import{fromJSON as k}from\"../../geometry/support/jsonUtils.js\";import{ArcadeExecutionError as M,ExecutionErrorCodes as Z}from\"../executionError.js\";import{getMetersPerUnitForSR as b}from\"../../core/unitUtils.js\";let A=null;function q(e){return 0===n.indexOf(\"4.\")?j.fromExtent(e):new j({spatialReference:e.spatialReference,rings:[[[e.xmin,e.ymin],[e.xmin,e.ymax],[e.xmax,e.ymax],[e.xmax,e.ymin],[e.xmin,e.ymin]]]})}function z(n){A=n}function E(n,e){if(\"polygon\"!==n.type&&\"polyline\"!==n.type&&\"extent\"!==n.type)return 0;let t=1;if(n.spatialReference.vcsWkid||n.spatialReference.latestVcsWkid){t=v(n.spatialReference)/b(n.spatialReference)}let r=0;if(\"polyline\"===n.type)for(const a of n.paths)for(let n=1;n<a.length;n++)r+=I(a[n],a[n-1],t);else if(\"polygon\"===n.type)for(const a of n.rings){for(let n=1;n<a.length;n++)r+=I(a[n],a[n-1],t);(a[0][0]!==a[a.length-1][0]||a[0][1]!==a[a.length-1][1]||void 0!==a[0][2]&&a[0][2]!==a[a.length-1][2])&&(r+=I(a[0],a[a.length-1],t))}else\"extent\"===n.type&&(r+=2*I([n.xmin,n.ymin,0],[n.xmax,n.ymin,0],t),r+=2*I([n.xmin,n.ymin,0],[n.xmin,n.ymax,0],t),r*=2,r+=4*Math.abs(m(n.zmax,0)*t-m(n.zmin,0)*t));const i=new L({hasZ:!1,hasM:!1,spatialReference:n.spatialReference,paths:[[0,0],[0,r]]});return A.planarLength(i,e)}function O(n,v){function I(n,e,t){if(a(t,2,2,n,e),t[0]instanceof R&&t[1]instanceof R);else if(t[0]instanceof R&&null===t[1]);else if(t[1]instanceof R&&null===t[0]);else if(null!==t[0]||null!==t[1])throw new M(n,Z.InvalidParameter,e)}n.disjoint=function(n,e){return v(n,e,((t,r,a)=>(a=i(a),I(n,e,a),null===a[0]||null===a[1]||A.disjoint(a[0],a[1]))))},n.intersects=function(n,e){return v(n,e,((t,r,a)=>(a=i(a),I(n,e,a),null!==a[0]&&null!==a[1]&&A.intersects(a[0],a[1]))))},n.touches=function(n,e){return v(n,e,((t,r,a)=>(a=i(a),I(n,e,a),null!==a[0]&&null!==a[1]&&A.touches(a[0],a[1]))))},n.crosses=function(n,e){return v(n,e,((t,r,a)=>(a=i(a),I(n,e,a),null!==a[0]&&null!==a[1]&&A.crosses(a[0],a[1]))))},n.within=function(n,e){return v(n,e,((t,r,a)=>(a=i(a),I(n,e,a),null!==a[0]&&null!==a[1]&&A.within(a[0],a[1]))))},n.contains=function(n,e){return v(n,e,((t,r,a)=>(a=i(a),I(n,e,a),null!==a[0]&&null!==a[1]&&A.contains(a[0],a[1]))))},n.overlaps=function(n,e){return v(n,e,((t,r,a)=>(a=i(a),I(n,e,a),null!==a[0]&&null!==a[1]&&A.overlaps(a[0],a[1]))))},n.equals=function(n,e){return v(n,e,((t,r,i)=>(a(i,2,2,n,e),i[0]===i[1]||(i[0]instanceof R&&i[1]instanceof R?A.equals(i[0],i[1]):!(!l(i[0])||!l(i[1]))&&i[0].equals(i[1])))))},n.relate=function(n,e){return v(n,e,((t,r,l)=>{if(l=i(l),a(l,3,3,n,e),l[0]instanceof R&&l[1]instanceof R)return A.relate(l[0],l[1],o(l[2]));if(l[0]instanceof R&&null===l[1])return!1;if(l[1]instanceof R&&null===l[0])return!1;if(null===l[0]&&null===l[1])return!1;throw new M(n,Z.InvalidParameter,e)}))},n.intersection=function(n,e){return v(n,e,((t,r,a)=>(a=i(a),I(n,e,a),null===a[0]||null===a[1]?null:A.intersect(a[0],a[1]))))},n.union=function(n,t){return v(n,t,((r,a,l)=>{const o=[];if(0===(l=i(l)).length)throw new M(n,Z.WrongNumberOfParameters,t);if(1===l.length)if(f(l[0])){const e=i(l[0]);for(let r=0;r<e.length;r++)if(null!==e[r]){if(!(e[r]instanceof R))throw new M(n,Z.InvalidParameter,t);o.push(e[r])}}else{if(!s(l[0])){if(l[0]instanceof R)return u(e(l[0]),n.spatialReference);if(null===l[0])return null;throw new M(n,Z.InvalidParameter,t)}{const e=i(l[0].toArray());for(let r=0;r<e.length;r++)if(null!==e[r]){if(!(e[r]instanceof R))throw new M(n,Z.InvalidParameter,t);o.push(e[r])}}}else for(let e=0;e<l.length;e++)if(null!==l[e]){if(!(l[e]instanceof R))throw new M(n,Z.InvalidParameter,t);o.push(l[e])}return 0===o.length?null:A.union(o)}))},n.difference=function(n,t){return v(n,t,((r,a,l)=>(l=i(l),I(n,t,l),null!==l[0]&&null===l[1]?e(l[0]):null===l[0]?null:A.difference(l[0],l[1]))))},n.symmetricdifference=function(n,t){return v(n,t,((r,a,l)=>(l=i(l),I(n,t,l),null===l[0]&&null===l[1]?null:null===l[0]?e(l[1]):null===l[1]?e(l[0]):A.symmetricDifference(l[0],l[1]))))},n.clip=function(n,e){return v(n,e,((t,r,l)=>{if(l=i(l),a(l,2,2,n,e),!(l[1]instanceof y)&&null!==l[1])throw new M(n,Z.InvalidParameter,e);if(null===l[0])return null;if(!(l[0]instanceof R))throw new M(n,Z.InvalidParameter,e);return null===l[1]?null:A.clip(l[0],l[1])}))},n.cut=function(n,t){return v(n,t,((r,l,o)=>{if(o=i(o),a(o,2,2,n,t),!(o[1]instanceof L)&&null!==o[1])throw new M(n,Z.InvalidParameter,t);if(null===o[0])return[];if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,t);return null===o[1]?[e(o[0])]:A.cut(o[0],o[1])}))},n.area=function(n,e){return v(n,e,((r,l,o)=>{if(a(o,1,2,n,e),null===(o=i(o))[0])return 0;if(f(o[0])||s(o[0])){const e=c(o[0],n.spatialReference);return null===e?0:A.planarArea(e,t(m(o[1],-1)))}if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,e);return A.planarArea(o[0],t(m(o[1],-1)))}))},n.areageodetic=function(n,e){return v(n,e,((r,l,o)=>{if(a(o,1,2,n,e),null===(o=i(o))[0])return 0;if(f(o[0])||s(o[0])){const e=c(o[0],n.spatialReference);return null===e?0:A.geodesicArea(e,t(m(o[1],-1)))}if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,e);return A.geodesicArea(o[0],t(m(o[1],-1)))}))},n.length=function(n,e){return v(n,e,((t,l,o)=>{if(a(o,1,2,n,e),null===(o=i(o))[0])return 0;if(f(o[0])||s(o[0])){const e=h(o[0],n.spatialReference);return null===e?0:A.planarLength(e,r(m(o[1],-1)))}if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,e);return A.planarLength(o[0],r(m(o[1],-1)))}))},n.length3d=function(n,e){return v(n,e,((t,l,o)=>{if(a(o,1,2,n,e),null===(o=i(o))[0])return 0;if(f(o[0])||s(o[0])){const e=h(o[0],n.spatialReference);return null===e?0:!0===e.hasZ?E(e,r(m(o[1],-1))):A.planarLength(e,r(m(o[1],-1)))}if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,e);return!0===o[0].hasZ?E(o[0],r(m(o[1],-1))):A.planarLength(o[0],r(m(o[1],-1)))}))},n.lengthgeodetic=function(n,e){return v(n,e,((t,l,o)=>{if(a(o,1,2,n,e),null===(o=i(o))[0])return 0;if(f(o[0])||s(o[0])){const e=h(o[0],n.spatialReference);return null===e?0:A.geodesicLength(e,r(m(o[1],-1)))}if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,e);return A.geodesicLength(o[0],r(m(o[1],-1)))}))},n.distance=function(n,e){return v(n,e,((t,l,o)=>{o=i(o),a(o,2,3,n,e);let u=o[0];(f(o[0])||s(o[0]))&&(u=w(o[0],n.spatialReference));let c=o[1];if((f(o[1])||s(o[1]))&&(c=w(o[1],n.spatialReference)),!(u instanceof R))throw new M(n,Z.InvalidParameter,e);if(!(c instanceof R))throw new M(n,Z.InvalidParameter,e);return A.distance(u,c,r(m(o[2],-1)))}))},n.distancegeodetic=function(n,e){return v(n,e,((t,l,o)=>{o=i(o),a(o,2,3,n,e);const f=o[0],s=o[1];if(!(f instanceof N))throw new M(n,Z.InvalidParameter,e);if(!(s instanceof N))throw new M(n,Z.InvalidParameter,e);const u=new L({paths:[],spatialReference:f.spatialReference});return u.addPath([f,s]),A.geodesicLength(u,r(m(o[2],-1)))}))},n.densify=function(n,e){return v(n,e,((t,l,o)=>{if(o=i(o),a(o,2,3,n,e),null===o[0])return null;if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,e);const f=p(o[1]);if(isNaN(f))throw new M(n,Z.InvalidParameter,e);if(f<=0)throw new M(n,Z.InvalidParameter,e);return o[0]instanceof j||o[0]instanceof L?A.densify(o[0],f,r(m(o[2],-1))):o[0]instanceof y?A.densify(q(o[0]),f,r(m(o[2],-1))):o[0]}))},n.densifygeodetic=function(n,e){return v(n,e,((t,l,o)=>{if(o=i(o),a(o,2,3,n,e),null===o[0])return null;if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,e);const f=p(o[1]);if(isNaN(f))throw new M(n,Z.InvalidParameter,e);if(f<=0)throw new M(n,Z.InvalidParameter,e);return o[0]instanceof j||o[0]instanceof L?A.geodesicDensify(o[0],f,r(m(o[2],-1))):o[0]instanceof y?A.geodesicDensify(q(o[0]),f,r(m(o[2],-1))):o[0]}))},n.generalize=function(n,e){return v(n,e,((t,l,o)=>{if(o=i(o),a(o,2,4,n,e),null===o[0])return null;if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,e);const f=p(o[1]);if(isNaN(f))throw new M(n,Z.InvalidParameter,e);return A.generalize(o[0],f,d(m(o[2],!0)),r(m(o[3],-1)))}))},n.buffer=function(n,t){return v(n,t,((l,o,f)=>{if(f=i(f),a(f,2,3,n,t),null===f[0])return null;if(!(f[0]instanceof R))throw new M(n,Z.InvalidParameter,t);const s=p(f[1]);if(isNaN(s))throw new M(n,Z.InvalidParameter,t);return 0===s?e(f[0]):A.buffer(f[0],s,r(m(f[2],-1)))}))},n.buffergeodetic=function(n,t){return v(n,t,((l,o,f)=>{if(f=i(f),a(f,2,3,n,t),null===f[0])return null;if(!(f[0]instanceof R))throw new M(n,Z.InvalidParameter,t);const s=p(f[1]);if(isNaN(s))throw new M(n,Z.InvalidParameter,t);return 0===s?e(f[0]):A.geodesicBuffer(f[0],s,r(m(f[2],-1)))}))},n.offset=function(n,e){return v(n,e,((t,l,f)=>{if(f=i(f),a(f,2,6,n,e),null===f[0])return null;if(!(f[0]instanceof j||f[0]instanceof L))throw new M(n,Z.InvalidParameter,e);const s=p(f[1]);if(isNaN(s))throw new M(n,Z.InvalidParameter,e);const u=p(m(f[4],10));if(isNaN(u))throw new M(n,Z.InvalidParameter,e);const c=p(m(f[5],0));if(isNaN(c))throw new M(n,Z.InvalidParameter,e);return A.offset(f[0],s,r(m(f[2],-1)),o(m(f[3],\"round\")).toLowerCase(),u,c)}))},n.rotate=function(n,e){return v(n,e,((t,r,l)=>{l=i(l),a(l,2,3,n,e);let o=l[0];if(null===o)return null;if(!(o instanceof R))throw new M(n,Z.InvalidParameter,e);o instanceof y&&(o=j.fromExtent(o));const f=p(l[1]);if(isNaN(f))throw new M(n,Z.InvalidParameter,e);const s=m(l[2],null);if(null===s)return A.rotate(o,f);if(s instanceof N)return A.rotate(o,f,s);throw new M(n,Z.InvalidParameter,e)}))},n.centroid=function(n,t){return v(n,t,((r,l,o)=>{if(o=i(o),a(o,1,1,n,t),null===o[0])return null;let c=o[0];if((f(o[0])||s(o[0]))&&(c=w(o[0],n.spatialReference)),null===c)return null;if(!(c instanceof R))throw new M(n,Z.InvalidParameter,t);return c instanceof N?u(e(o[0]),n.spatialReference):c instanceof j?c.centroid:c instanceof L?g(c):c instanceof x?P(c):c instanceof y?c.center:null}))},n.multiparttosinglepart=function(n,t){return v(n,t,((r,l,o)=>{o=i(o),a(o,1,1,n,t);const f=[];if(null===o[0])return null;if(!(o[0]instanceof R))throw new M(n,Z.InvalidParameter,t);if(o[0]instanceof N)return[u(e(o[0]),n.spatialReference)];if(o[0]instanceof y)return[u(e(o[0]),n.spatialReference)];const s=A.simplify(o[0]);if(s instanceof j){const n=[],e=[];for(let t=0;t<s.rings.length;t++)if(s.isClockwise(s.rings[t])){const e=k({rings:[s.rings[t]],hasZ:!0===s.hasZ,hasM:!0===s.hasM,spatialReference:s.spatialReference.toJSON()});n.push(e)}else e.push({ring:s.rings[t],pt:s.getPoint(t,0)});for(let t=0;t<e.length;t++)for(let r=0;r<n.length;r++)if(n[r].contains(e[t].pt)){n[r].addRing(e[t].ring);break}return n}if(s instanceof L){const n=[];for(let e=0;e<s.paths.length;e++){const t=k({paths:[s.paths[e]],hasZ:!0===s.hasZ,hasM:!0===s.hasM,spatialReference:s.spatialReference.toJSON()});n.push(t)}return n}if(o[0]instanceof x){const t=u(e(o[0]),n.spatialReference);for(let n=0;n<t.points.length;n++)f.push(t.getPoint(n));return f}return null}))},n.issimple=function(n,e){return v(n,e,((t,r,l)=>{if(l=i(l),a(l,1,1,n,e),null===l[0])return!0;if(!(l[0]instanceof R))throw new M(n,Z.InvalidParameter,e);return A.isSimple(l[0])}))},n.simplify=function(n,e){return v(n,e,((t,r,l)=>{if(l=i(l),a(l,1,1,n,e),null===l[0])return null;if(!(l[0]instanceof R))throw new M(n,Z.InvalidParameter,e);return A.simplify(l[0])}))},n.convexhull=function(n,e){return v(n,e,((t,r,l)=>{if(l=i(l),a(l,1,1,n,e),null===l[0])return null;if(!(l[0]instanceof R))throw new M(n,Z.InvalidParameter,e);return A.convexHull(l[0])}))}}export{O as registerFunctions,z as setGeometryEngine};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIs2B,IAAI,IAAE;AAAK,SAAS,EAAEA,IAAE;AAAC,SAAO,MAAI,EAAE,QAAQ,IAAI,IAAE,EAAE,WAAWA,EAAC,IAAE,IAAI,EAAE,EAAC,kBAAiBA,GAAE,kBAAiB,OAAM,CAAC,CAAC,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,CAAC,CAAC,EAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAE;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAG,cAAY,EAAE,QAAM,eAAa,EAAE,QAAM,aAAW,EAAE,KAAK,QAAO;AAAE,MAAIC,KAAE;AAAE,MAAG,EAAE,iBAAiB,WAAS,EAAE,iBAAiB,eAAc;AAAC,IAAAA,KAAE,EAAE,EAAE,gBAAgB,IAAE,EAAE,EAAE,gBAAgB;AAAA,EAAC;AAAC,MAAIC,KAAE;AAAE,MAAG,eAAa,EAAE,KAAK,YAAUC,MAAK,EAAE,MAAM,UAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAF,MAAGF,GAAEG,GAAEC,EAAC,GAAED,GAAEC,KAAE,CAAC,GAAEH,EAAC;AAAA,WAAU,cAAY,EAAE,KAAK,YAAUE,MAAK,EAAE,OAAM;AAAC,aAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAF,MAAGF,GAAEG,GAAEC,EAAC,GAAED,GAAEC,KAAE,CAAC,GAAEH,EAAC;AAAE,KAACE,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAEA,GAAE,SAAO,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAEA,GAAE,SAAO,CAAC,EAAE,CAAC,KAAG,WAASA,GAAE,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAEA,GAAE,SAAO,CAAC,EAAE,CAAC,OAAKD,MAAGF,GAAEG,GAAE,CAAC,GAAEA,GAAEA,GAAE,SAAO,CAAC,GAAEF,EAAC;AAAA,EAAE;AAAA,MAAK,cAAW,EAAE,SAAOC,MAAG,IAAEF,GAAE,CAAC,EAAE,MAAK,EAAE,MAAK,CAAC,GAAE,CAAC,EAAE,MAAK,EAAE,MAAK,CAAC,GAAEC,EAAC,GAAEC,MAAG,IAAEF,GAAE,CAAC,EAAE,MAAK,EAAE,MAAK,CAAC,GAAE,CAAC,EAAE,MAAK,EAAE,MAAK,CAAC,GAAEC,EAAC,GAAEC,MAAG,GAAEA,MAAG,IAAE,KAAK,IAAI,EAAE,EAAE,MAAK,CAAC,IAAED,KAAE,EAAE,EAAE,MAAK,CAAC,IAAEA,EAAC;AAAG,QAAM,IAAE,IAAI,EAAE,EAAC,MAAK,OAAG,MAAK,OAAG,kBAAiB,EAAE,kBAAiB,OAAM,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAEC,EAAC,CAAC,EAAC,CAAC;AAAE,SAAO,EAAE,aAAa,GAAEF,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEK,IAAE;AAAC,WAAS,EAAED,IAAEJ,IAAEC,IAAE;AAAC,QAAG,EAAEA,IAAE,GAAE,GAAEG,IAAEJ,EAAC,GAAEC,GAAE,CAAC,aAAY,KAAGA,GAAE,CAAC,aAAY,EAAE;AAAA,aAASA,GAAE,CAAC,aAAY,KAAG,SAAOA,GAAE,CAAC,EAAE;AAAA,aAASA,GAAE,CAAC,aAAY,KAAG,SAAOA,GAAE,CAAC,EAAE;AAAA,aAAS,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,EAAE,OAAM,IAAI,EAAEG,IAAE,EAAE,kBAAiBJ,EAAC;AAAA,EAAC;AAAC,IAAE,WAAS,SAASI,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEC,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEC,IAAEJ,IAAEG,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAE,SAASA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,aAAW,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEC,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEC,IAAEJ,IAAEG,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAE,WAAWA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAQ,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEC,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEC,IAAEJ,IAAEG,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAE,QAAQA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAQ,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEC,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEC,IAAEJ,IAAEG,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAE,QAAQA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,SAAO,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEC,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEC,IAAEJ,IAAEG,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAE,OAAOA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,WAAS,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEC,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEC,IAAEJ,IAAEG,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAE,SAASA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,WAAS,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEC,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEC,IAAEJ,IAAEG,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAE,SAASA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,SAAO,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAE,OAAK,EAAE,GAAE,GAAE,GAAEE,IAAEJ,EAAC,GAAE,EAAE,CAAC,MAAI,EAAE,CAAC,MAAI,EAAE,CAAC,aAAY,KAAG,EAAE,CAAC,aAAY,IAAE,EAAE,OAAO,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAG,CAAC,EAAE,EAAE,CAAC,CAAC,MAAI,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,GAAI;AAAA,EAAC,GAAE,EAAE,SAAO,SAASI,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEI,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAEM,GAAE,CAAC,aAAY,KAAGA,GAAE,CAAC,aAAY,EAAE,QAAO,EAAE,OAAOA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAE,UAAGA,GAAE,CAAC,aAAY,KAAG,SAAOA,GAAE,CAAC,EAAE,QAAM;AAAG,UAAGA,GAAE,CAAC,aAAY,KAAG,SAAOA,GAAE,CAAC,EAAE,QAAM;AAAG,UAAG,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,EAAE,QAAM;AAAG,YAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBJ,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,eAAa,SAASI,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEC,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEC,IAAEJ,IAAEG,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,IAAE,OAAK,EAAE,UAAUA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,QAAM,SAASC,IAAEH,IAAE;AAAC,WAAOI,GAAED,IAAEH,IAAG,CAACC,IAAEC,IAAEG,OAAI;AAAC,YAAM,IAAE,CAAC;AAAE,UAAG,OAAKA,KAAE,GAAEA,EAAC,GAAG,OAAO,OAAM,IAAI,EAAEF,IAAE,EAAE,yBAAwBH,EAAC;AAAE,UAAG,MAAIK,GAAE,OAAO,KAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMN,KAAE,GAAEM,GAAE,CAAC,CAAC;AAAE,iBAAQJ,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,KAAG,SAAOF,GAAEE,EAAC,GAAE;AAAC,cAAG,EAAEF,GAAEE,EAAC,aAAY,GAAG,OAAM,IAAI,EAAEE,IAAE,EAAE,kBAAiBH,EAAC;AAAE,YAAE,KAAKD,GAAEE,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC,OAAK;AAAC,YAAG,CAAC,EAAEI,GAAE,CAAC,CAAC,GAAE;AAAC,cAAGA,GAAE,CAAC,aAAY,EAAE,QAAO,GAAE,EAAEA,GAAE,CAAC,CAAC,GAAEF,GAAE,gBAAgB;AAAE,cAAG,SAAOE,GAAE,CAAC,EAAE,QAAO;AAAK,gBAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBH,EAAC;AAAA,QAAC;AAAC;AAAC,gBAAMD,KAAE,GAAEM,GAAE,CAAC,EAAE,QAAQ,CAAC;AAAE,mBAAQJ,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,KAAG,SAAOF,GAAEE,EAAC,GAAE;AAAC,gBAAG,EAAEF,GAAEE,EAAC,aAAY,GAAG,OAAM,IAAI,EAAEE,IAAE,EAAE,kBAAiBH,EAAC;AAAE,cAAE,KAAKD,GAAEE,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,UAAM,UAAQF,KAAE,GAAEA,KAAEM,GAAE,QAAON,KAAI,KAAG,SAAOM,GAAEN,EAAC,GAAE;AAAC,YAAG,EAAEM,GAAEN,EAAC,aAAY,GAAG,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBH,EAAC;AAAE,UAAE,KAAKK,GAAEN,EAAC,CAAC;AAAA,MAAC;AAAC,aAAO,MAAI,EAAE,SAAO,OAAK,EAAE,MAAM,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,aAAW,SAASI,IAAEH,IAAE;AAAC,WAAOI,GAAED,IAAEH,IAAG,CAACC,IAAEC,IAAEG,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEF,IAAEH,IAAEK,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,IAAE,EAAEA,GAAE,CAAC,CAAC,IAAE,SAAOA,GAAE,CAAC,IAAE,OAAK,EAAE,WAAWA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,sBAAoB,SAASF,IAAEH,IAAE;AAAC,WAAOI,GAAED,IAAEH,IAAG,CAACC,IAAEC,IAAEG,QAAKA,KAAE,GAAEA,EAAC,GAAE,EAAEF,IAAEH,IAAEK,EAAC,GAAE,SAAOA,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,IAAE,OAAK,SAAOA,GAAE,CAAC,IAAE,EAAEA,GAAE,CAAC,CAAC,IAAE,SAAOA,GAAE,CAAC,IAAE,EAAEA,GAAE,CAAC,CAAC,IAAE,EAAE,oBAAoBA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,OAAK,SAASF,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEI,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,EAAEM,GAAE,CAAC,aAAYC,OAAI,SAAOD,GAAE,CAAC,EAAE,OAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,UAAG,SAAOM,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,SAAOM,GAAE,CAAC,IAAE,OAAK,EAAE,KAAKA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,MAAI,SAASF,IAAEH,IAAE;AAAC,WAAOI,GAAED,IAAEH,IAAG,CAACC,IAAEI,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEH,EAAC,GAAE,EAAE,EAAE,CAAC,aAAY,MAAI,SAAO,EAAE,CAAC,EAAE,OAAM,IAAI,EAAEG,IAAE,EAAE,kBAAiBH,EAAC;AAAE,UAAG,SAAO,EAAE,CAAC,EAAE,QAAM,CAAC;AAAE,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEG,IAAE,EAAE,kBAAiBH,EAAC;AAAE,aAAO,SAAO,EAAE,CAAC,IAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAE,EAAE,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,OAAK,SAASG,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACE,IAAEI,IAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,UAAQ,IAAE,GAAE,CAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAE,EAAE,CAAC,CAAC,KAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAMA,KAAE,GAAE,EAAE,CAAC,GAAEI,GAAE,gBAAgB;AAAE,eAAO,SAAOJ,KAAE,IAAE,EAAE,WAAWA,IAAE,EAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,WAAW,EAAE,CAAC,GAAE,EAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,eAAa,SAASI,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACE,IAAEI,IAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,UAAQ,IAAE,GAAE,CAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAE,EAAE,CAAC,CAAC,KAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAMA,KAAE,GAAE,EAAE,CAAC,GAAEI,GAAE,gBAAgB;AAAE,eAAO,SAAOJ,KAAE,IAAE,EAAE,aAAaA,IAAE,EAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,aAAa,EAAE,CAAC,GAAE,EAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,SAAO,SAASI,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEK,IAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,UAAQ,IAAE,GAAE,CAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAE,EAAE,CAAC,CAAC,KAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAMA,KAAE,GAAE,EAAE,CAAC,GAAEI,GAAE,gBAAgB;AAAE,eAAO,SAAOJ,KAAE,IAAE,EAAE,aAAaA,IAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEC,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,aAAa,EAAE,CAAC,GAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,WAAS,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEK,IAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,UAAQ,IAAE,GAAE,CAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAE,EAAE,CAAC,CAAC,KAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAMA,KAAE,GAAE,EAAE,CAAC,GAAEI,GAAE,gBAAgB;AAAE,eAAO,SAAOJ,KAAE,IAAE,SAAKA,GAAE,OAAK,EAAEA,IAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,aAAaH,IAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEC,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAM,SAAK,EAAE,CAAC,EAAE,OAAK,EAAE,EAAE,CAAC,GAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,aAAa,EAAE,CAAC,GAAEA,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,iBAAe,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEK,IAAE,MAAI;AAAC,UAAG,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,UAAQ,IAAE,GAAE,CAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAE,EAAE,CAAC,CAAC,KAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAMA,KAAE,GAAE,EAAE,CAAC,GAAEI,GAAE,gBAAgB;AAAE,eAAO,SAAOJ,KAAE,IAAE,EAAE,eAAeA,IAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEC,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,eAAe,EAAE,CAAC,GAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,WAAS,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEK,IAAE,MAAI;AAAC,UAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC;AAAE,UAAIQ,KAAE,EAAE,CAAC;AAAE,OAAC,EAAE,EAAE,CAAC,CAAC,KAAG,EAAE,EAAE,CAAC,CAAC,OAAKA,KAAE,GAAE,EAAE,CAAC,GAAEJ,GAAE,gBAAgB;AAAG,UAAIK,KAAE,EAAE,CAAC;AAAE,WAAI,EAAE,EAAE,CAAC,CAAC,KAAG,EAAE,EAAE,CAAC,CAAC,OAAKA,KAAE,GAAE,EAAE,CAAC,GAAEL,GAAE,gBAAgB,IAAG,EAAEI,cAAa,GAAG,OAAM,IAAI,EAAEJ,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,UAAG,EAAES,cAAa,GAAG,OAAM,IAAI,EAAEL,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,SAASQ,IAAEC,IAAEN,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,mBAAiB,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEK,IAAE,MAAI;AAAC,UAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC;AAAE,YAAM,IAAE,EAAE,CAAC,GAAEU,KAAE,EAAE,CAAC;AAAE,UAAG,EAAE,aAAa,GAAG,OAAM,IAAI,EAAEN,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,UAAG,EAAEU,cAAa,GAAG,OAAM,IAAI,EAAEN,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,YAAMQ,KAAE,IAAI,EAAE,EAAC,OAAM,CAAC,GAAE,kBAAiB,EAAE,iBAAgB,CAAC;AAAE,aAAOA,GAAE,QAAQ,CAAC,GAAEE,EAAC,CAAC,GAAE,EAAE,eAAeF,IAAEL,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAQ,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEK,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,YAAM,IAAE,GAAE,EAAE,CAAC,CAAC;AAAE,UAAG,MAAM,CAAC,EAAE,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,UAAG,KAAG,EAAE,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,CAAC,aAAY,KAAG,EAAE,CAAC,aAAY,IAAE,EAAE,QAAQ,EAAE,CAAC,GAAE,GAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC,aAAYI,KAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC,GAAE,GAAEJ,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,kBAAgB,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEK,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,YAAM,IAAE,GAAE,EAAE,CAAC,CAAC;AAAE,UAAG,MAAM,CAAC,EAAE,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,UAAG,KAAG,EAAE,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,CAAC,aAAY,KAAG,EAAE,CAAC,aAAY,IAAE,EAAE,gBAAgB,EAAE,CAAC,GAAE,GAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC,aAAYI,KAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC,GAAE,GAAEJ,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,aAAW,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEK,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,YAAM,IAAE,GAAE,EAAE,CAAC,CAAC;AAAE,UAAG,MAAM,CAAC,EAAE,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,WAAW,EAAE,CAAC,GAAE,GAAE,GAAE,EAAE,EAAE,CAAC,GAAE,IAAE,CAAC,GAAEG,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,SAAO,SAASC,IAAEH,IAAE;AAAC,WAAOI,GAAED,IAAEH,IAAG,CAACK,IAAE,GAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEH,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEG,IAAE,EAAE,kBAAiBH,EAAC;AAAE,YAAMS,KAAE,GAAE,EAAE,CAAC,CAAC;AAAE,UAAG,MAAMA,EAAC,EAAE,OAAM,IAAI,EAAEN,IAAE,EAAE,kBAAiBH,EAAC;AAAE,aAAO,MAAIS,KAAE,EAAE,EAAE,CAAC,CAAC,IAAE,EAAE,OAAO,EAAE,CAAC,GAAEA,IAAEP,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,iBAAe,SAASC,IAAEH,IAAE;AAAC,WAAOI,GAAED,IAAEH,IAAG,CAACK,IAAE,GAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEH,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEG,IAAE,EAAE,kBAAiBH,EAAC;AAAE,YAAMS,KAAE,GAAE,EAAE,CAAC,CAAC;AAAE,UAAG,MAAMA,EAAC,EAAE,OAAM,IAAI,EAAEN,IAAE,EAAE,kBAAiBH,EAAC;AAAE,aAAO,MAAIS,KAAE,EAAE,EAAE,CAAC,CAAC,IAAE,EAAE,eAAe,EAAE,CAAC,GAAEA,IAAEP,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,SAAO,SAASC,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEK,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,KAAG,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEI,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,YAAMU,KAAE,GAAE,EAAE,CAAC,CAAC;AAAE,UAAG,MAAMA,EAAC,EAAE,OAAM,IAAI,EAAEN,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,YAAMQ,KAAE,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC;AAAE,UAAG,MAAMA,EAAC,EAAE,OAAM,IAAI,EAAEJ,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,YAAMS,KAAE,GAAE,EAAE,EAAE,CAAC,GAAE,CAAC,CAAC;AAAE,UAAG,MAAMA,EAAC,EAAE,OAAM,IAAI,EAAEL,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,OAAO,EAAE,CAAC,GAAEU,IAAEP,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,EAAE,CAAC,GAAE,OAAO,CAAC,EAAE,YAAY,GAAEK,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,SAAO,SAASL,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEI,OAAI;AAAC,MAAAA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEF,IAAEJ,EAAC;AAAE,UAAI,IAAEM,GAAE,CAAC;AAAE,UAAG,SAAO,EAAE,QAAO;AAAK,UAAG,EAAE,aAAa,GAAG,OAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,mBAAaO,OAAI,IAAE,EAAE,WAAW,CAAC;AAAG,YAAM,IAAE,GAAED,GAAE,CAAC,CAAC;AAAE,UAAG,MAAM,CAAC,EAAE,OAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,YAAMU,KAAE,EAAEJ,GAAE,CAAC,GAAE,IAAI;AAAE,UAAG,SAAOI,GAAE,QAAO,EAAE,OAAO,GAAE,CAAC;AAAE,UAAGA,cAAa,EAAE,QAAO,EAAE,OAAO,GAAE,GAAEA,EAAC;AAAE,YAAM,IAAI,EAAEN,IAAE,EAAE,kBAAiBJ,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,WAAS,SAASI,IAAEH,IAAE;AAAC,WAAOI,GAAED,IAAEH,IAAG,CAACC,IAAEI,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEH,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAIQ,KAAE,EAAE,CAAC;AAAE,WAAI,EAAE,EAAE,CAAC,CAAC,KAAG,EAAE,EAAE,CAAC,CAAC,OAAKA,KAAE,GAAE,EAAE,CAAC,GAAEL,GAAE,gBAAgB,IAAG,SAAOK,GAAE,QAAO;AAAK,UAAG,EAAEA,cAAa,GAAG,OAAM,IAAI,EAAEL,IAAE,EAAE,kBAAiBH,EAAC;AAAE,aAAOQ,cAAa,IAAE,GAAE,EAAE,EAAE,CAAC,CAAC,GAAEL,GAAE,gBAAgB,IAAEK,cAAa,IAAEA,GAAE,WAASA,cAAa,IAAE,EAAEA,EAAC,IAAEA,cAAa,IAAED,GAAEC,EAAC,IAAEA,cAAaF,KAAEE,GAAE,SAAO;AAAA,IAAI,CAAE;AAAA,EAAC,GAAE,EAAE,wBAAsB,SAASL,IAAEH,IAAE;AAAC,WAAOI,GAAED,IAAEH,IAAG,CAACC,IAAEI,IAAE,MAAI;AAAC,UAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEH,EAAC;AAAE,YAAM,IAAE,CAAC;AAAE,UAAG,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEG,IAAE,EAAE,kBAAiBH,EAAC;AAAE,UAAG,EAAE,CAAC,aAAY,EAAE,QAAM,CAAC,GAAE,EAAE,EAAE,CAAC,CAAC,GAAEG,GAAE,gBAAgB,CAAC;AAAE,UAAG,EAAE,CAAC,aAAYG,GAAE,QAAM,CAAC,GAAE,EAAE,EAAE,CAAC,CAAC,GAAEH,GAAE,gBAAgB,CAAC;AAAE,YAAMM,KAAE,EAAE,SAAS,EAAE,CAAC,CAAC;AAAE,UAAGA,cAAa,GAAE;AAAC,cAAMN,KAAE,CAAC,GAAEJ,KAAE,CAAC;AAAE,iBAAQC,KAAE,GAAEA,KAAES,GAAE,MAAM,QAAOT,KAAI,KAAGS,GAAE,YAAYA,GAAE,MAAMT,EAAC,CAAC,GAAE;AAAC,gBAAMD,KAAEK,GAAE,EAAC,OAAM,CAACK,GAAE,MAAMT,EAAC,CAAC,GAAE,MAAK,SAAKS,GAAE,MAAK,MAAK,SAAKA,GAAE,MAAK,kBAAiBA,GAAE,iBAAiB,OAAO,EAAC,CAAC;AAAE,UAAAN,GAAE,KAAKJ,EAAC;AAAA,QAAC,MAAM,CAAAA,GAAE,KAAK,EAAC,MAAKU,GAAE,MAAMT,EAAC,GAAE,IAAGS,GAAE,SAAST,IAAE,CAAC,EAAC,CAAC;AAAE,iBAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,UAAQC,KAAE,GAAEA,KAAEE,GAAE,QAAOF,KAAI,KAAGE,GAAEF,EAAC,EAAE,SAASF,GAAEC,EAAC,EAAE,EAAE,GAAE;AAAC,UAAAG,GAAEF,EAAC,EAAE,QAAQF,GAAEC,EAAC,EAAE,IAAI;AAAE;AAAA,QAAK;AAAC,eAAOG;AAAA,MAAC;AAAC,UAAGM,cAAa,GAAE;AAAC,cAAMN,KAAE,CAAC;AAAE,iBAAQJ,KAAE,GAAEA,KAAEU,GAAE,MAAM,QAAOV,MAAI;AAAC,gBAAMC,KAAEI,GAAE,EAAC,OAAM,CAACK,GAAE,MAAMV,EAAC,CAAC,GAAE,MAAK,SAAKU,GAAE,MAAK,MAAK,SAAKA,GAAE,MAAK,kBAAiBA,GAAE,iBAAiB,OAAO,EAAC,CAAC;AAAE,UAAAN,GAAE,KAAKH,EAAC;AAAA,QAAC;AAAC,eAAOG;AAAA,MAAC;AAAC,UAAG,EAAE,CAAC,aAAY,GAAE;AAAC,cAAMH,KAAE,GAAE,EAAE,EAAE,CAAC,CAAC,GAAEG,GAAE,gBAAgB;AAAE,iBAAQA,KAAE,GAAEA,KAAEH,GAAE,OAAO,QAAOG,KAAI,GAAE,KAAKH,GAAE,SAASG,EAAC,CAAC;AAAE,eAAO;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI,CAAE;AAAA,EAAC,GAAE,EAAE,WAAS,SAASA,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEI,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,SAAOM,GAAE,CAAC,EAAE,QAAM;AAAG,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,SAASM,GAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,WAAS,SAASF,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEI,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,SAAOM,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,SAASM,GAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,aAAW,SAASF,IAAEJ,IAAE;AAAC,WAAOK,GAAED,IAAEJ,IAAG,CAACC,IAAEC,IAAEI,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEF,IAAEJ,EAAC,GAAE,SAAOM,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAEF,IAAE,EAAE,kBAAiBJ,EAAC;AAAE,aAAO,EAAE,WAAWM,GAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;", "names": ["e", "t", "r", "a", "n", "v", "l", "w", "u", "c", "s"]}