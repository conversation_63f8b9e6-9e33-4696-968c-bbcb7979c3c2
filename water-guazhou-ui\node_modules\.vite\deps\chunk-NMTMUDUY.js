import {
  a,
  w
} from "./chunk-SMSZBVG5.js";
import {
  T
} from "./chunk-WAPZ634R.js";

// node_modules/@arcgis/core/views/2d/engine/BitmapContainer.js
var a2 = class extends a {
  constructor() {
    super(...arguments), this._hasCrossfade = false;
  }
  get requiresDedicatedFBO() {
    return this._hasCrossfade;
  }
  beforeRender(e) {
    super.beforeRender(e), this._manageFade();
  }
  prepareRenderPasses(r) {
    const a3 = r.registerRenderPass({ name: "bitmap", brushes: [w.bitmap], target: () => this.children, drawPhase: T.MAP });
    return [...super.prepareRenderPasses(r), a3];
  }
  _manageFade() {
    this.children.reduce((e, s) => e + (s.inFadeTransition ? 1 : 0), 0) >= 2 ? (this.children.forEach((e) => e.blendFunction = "additive"), this._hasCrossfade = true) : (this.children.forEach((e) => e.blendFunction = "standard"), this._hasCrossfade = false);
  }
};

export {
  a2 as a
};
//# sourceMappingURL=chunk-NMTMUDUY.js.map
