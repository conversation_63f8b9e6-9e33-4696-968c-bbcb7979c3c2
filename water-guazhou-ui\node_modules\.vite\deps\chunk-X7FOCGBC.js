import {
  v
} from "./chunk-SRBBUKOI.js";
import {
  h
} from "./chunk-M6X55NI4.js";
import {
  c,
  p as p3,
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  p as p2,
  w
} from "./chunk-63M4K32A.js";
import {
  M
} from "./chunk-R5MYQRRS.js";
import {
  R,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  i
} from "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/coordsUtils.js
function e2(t2) {
  if (!t2) return null;
  if (Array.isArray(t2)) return t2;
  const n = t2.hasZ, e4 = t2.hasM;
  if ("point" === t2.type) return e4 && n ? [t2.x, t2.y, t2.z, t2.m] : n ? [t2.x, t2.y, t2.z] : e4 ? [t2.x, t2.y, t2.m] : [t2.x, t2.y];
  if ("polygon" === t2.type) return t2.rings.slice(0);
  if ("polyline" === t2.type) return t2.paths.slice(0);
  if ("multipoint" === t2.type) return t2.points.slice(0);
  if ("extent" === t2.type) {
    const n2 = t2.clone().normalize();
    if (!n2) return null;
    let e5 = false, r5 = false;
    return n2.forEach((t3) => {
      t3.hasZ && (e5 = true), t3.hasM && (r5 = true);
    }), n2.map((t3) => {
      const n3 = [[t3.xmin, t3.ymin], [t3.xmin, t3.ymax], [t3.xmax, t3.ymax], [t3.xmax, t3.ymin], [t3.xmin, t3.ymin]];
      if (e5 && t3.hasZ) {
        const e6 = 0.5 * (t3.zmax - t3.zmin);
        for (let t4 = 0; t4 < n3.length; t4++) n3[t4].push(e6);
      }
      if (r5 && t3.hasM) {
        const e6 = 0.5 * (t3.mmax - t3.mmin);
        for (let t4 = 0; t4 < n3.length; t4++) n3[t4].push(e6);
      }
      return n3;
    });
  }
  return null;
}
function r3(t2, n) {
  const e4 = n[0] - t2[0], r5 = n[1] - t2[1];
  if (t2.length > 2 && n.length > 2) {
    const i4 = t2[2] - n[2];
    return Math.sqrt(e4 * e4 + r5 * r5 + i4 * i4);
  }
  return Math.sqrt(e4 * e4 + r5 * r5);
}
function i2(t2, n, e4) {
  const r5 = t2[0] + e4 * (n[0] - t2[0]), i4 = t2[1] + e4 * (n[1] - t2[1]);
  return t2.length > 2 && n.length > 2 ? [r5, i4, t2[2] + e4 * (n[2] - t2[2])] : [r5, i4];
}
function o(t2, n, e4, r5) {
  const [i4, o3] = n, [s3, l2] = e4[r5], [f3, u3] = e4[r5 + 1], c4 = f3 - s3, h4 = u3 - l2, a3 = c4 * c4 + h4 * h4, g3 = (i4 - s3) * c4 + (o3 - l2) * h4, p5 = Math.min(1, Math.max(0, g3 / a3));
  return t2[0] = s3 + c4 * p5, t2[1] = l2 + h4 * p5, t2;
}
function s(t2, n, e4) {
  const r5 = e4.rings;
  let i4, o3, s3 = false, f3 = 1 / 0;
  for (let u3 = 0; u3 < r5.length; u3++) {
    const e5 = r5[u3];
    for (let r6 = 0, u4 = e5.length - 1; r6 < e5.length; u4 = r6++) i4 = e5[r6], o3 = e5[u4], i4[1] > n != o3[1] > n && t2 < (o3[0] - i4[0]) * (n - i4[1]) / (o3[1] - i4[1]) + i4[0] && (s3 = !s3), f3 = Math.min(f3, l(t2, n, i4, o3));
  }
  return 0 === f3 ? 0 : (s3 ? 1 : -1) * Math.sqrt(f3);
}
function l(t2, n, e4, r5) {
  let i4 = e4[0], o3 = e4[1], s3 = r5[0] - i4, l2 = r5[1] - o3;
  if (0 !== s3 || 0 !== l2) {
    const e5 = ((t2 - i4) * s3 + (n - o3) * l2) / (s3 * s3 + l2 * l2);
    e5 > 1 ? (i4 = r5[0], o3 = r5[1]) : e5 > 0 && (i4 += s3 * e5, o3 += l2 * e5);
  }
  return s3 = t2 - i4, l2 = n - o3, s3 * s3 + l2 * l2;
}
function f2(t2, n) {
  return i2(t2, n, 0.5);
}
function u(t2) {
  const n = t2.length;
  let e4 = 0;
  for (let i4 = 0; i4 < n - 1; ++i4) e4 += r3(t2[i4], t2[i4 + 1]);
  return e4;
}
function c2(t2, n) {
  if (n <= 0) return t2[0];
  const e4 = t2.length;
  let o3 = 0;
  for (let s3 = 0; s3 < e4 - 1; ++s3) {
    const e5 = r3(t2[s3], t2[s3 + 1]);
    if (n - o3 < e5) {
      const r5 = (n - o3) / e5;
      return i2(t2[s3], t2[s3 + 1], r5);
    }
    o3 += e5;
  }
  return t2[e4 - 1];
}
function h2(t2, n, e4) {
  const r5 = t2.length;
  let i4 = 0, o3 = 0, s3 = 0;
  for (let l2 = 0; l2 < r5; l2++) {
    const f3 = t2[l2], u3 = t2[(l2 + 1) % r5];
    let c4 = 2;
    i4 += f3[0] * u3[1] - u3[0] * f3[1], f3.length > 2 && u3.length > 2 && e4 && (o3 += f3[0] * u3[2] - u3[0] * f3[2], c4 = 3), f3.length > c4 && u3.length > c4 && n && (s3 += f3[0] * u3[c4] - u3[0] * f3[c4]);
  }
  return i4 <= 0 && o3 <= 0 && s3 <= 0;
}
function a2(n) {
  const e4 = n.length;
  return e4 > 2 && i(n[0], n[e4 - 1]);
}
function g(t2) {
  if ("rings" in t2 && (p4(t2), t2.rings.length > 0 && !h2(t2.rings[0], t2.hasM ?? false, t2.hasZ ?? false))) for (const n of t2.rings) n.reverse();
}
function p4(t2) {
  if ("rings" in t2) for (const n of t2.rings) a2(n) || n.push(n[0].slice());
}
function y2(t2) {
  if ("polygon" !== t2.type && "polyline" !== t2.type) return t2;
  return m("polygon" === t2.type ? t2.rings : t2.paths, t2.spatialReference), t2;
}
function m(t2, e4) {
  const r5 = R(e4);
  if (!r5) return;
  const i4 = r5.valid[0], o3 = r5.valid[1], s3 = o3 - i4;
  for (const n of t2) {
    let t3 = 1 / 0, e5 = -1 / 0;
    for (const s4 of n) {
      const n2 = x(s4[0], i4, o3);
      t3 = Math.min(t3, n2), e5 = Math.max(e5, n2), s4[0] = n2;
    }
    const r6 = e5 - t3;
    s3 - r6 < r6 && n.forEach((t4) => {
      t4[0] < 0 && (t4[0] += s3);
    });
  }
}
function x(t2, n, e4) {
  const r5 = e4 - n;
  return t2 < n ? e4 - (n - t2) % r5 : t2 > e4 ? n + (t2 - n) % r5 : t2;
}
function M2(t2) {
  if (!t2 || t2.length < 3) return 0;
  let n = 0;
  const e4 = t2.length - 1;
  for (let r5 = 0; r5 < e4; r5++) n += (t2[r5][0] - t2[r5 + 1][0]) * (t2[r5][1] + t2[r5 + 1][1]);
  return n += (t2[e4][0] - t2[0][0]) * (t2[e4][1] + t2[0][1]), -0.5 * n;
}
function z(t2, n) {
  if (t2 === n) return true;
  if (t2.type !== n.type) return false;
  if ("point" === t2.type || "extent" === t2.type) return true;
  if ("multipoint" === t2.type) return t2.points.length === n.points.length;
  const [e4, r5] = "polyline" === t2.type ? [t2.paths, n.paths] : [t2.rings, n.rings];
  return e4.length === r5.length && e4.every((t3, n2) => t3.length === r5[n2].length);
}

// node_modules/@arcgis/core/geometry/support/centroid.js
function e3(n) {
  return n ? n.hasZ ? [n.xmax - n.xmin / 2, n.ymax - n.ymin / 2, n.zmax - n.zmin / 2] : [n.xmax - n.xmin / 2, n.ymax - n.ymin / 2] : null;
}
function r4(n) {
  return n ? o2(n.rings, n.hasZ ?? false) : null;
}
function o2(n, t2) {
  if (!n || !n.length) return null;
  const l2 = [], e4 = [], r5 = t2 ? [1 / 0, -1 / 0, 1 / 0, -1 / 0, 1 / 0, -1 / 0] : [1 / 0, -1 / 0, 1 / 0, -1 / 0];
  for (let o3 = 0, i4 = n.length; o3 < i4; o3++) {
    const l3 = u2(n[o3], t2, r5);
    l3 && e4.push(l3);
  }
  if (e4.sort((n2, l3) => {
    let e5 = n2[2] - l3[2];
    return 0 === e5 && t2 && (e5 = n2[4] - l3[4]), e5;
  }), e4.length && (l2[0] = e4[0][0], l2[1] = e4[0][1], t2 && (l2[2] = e4[0][3]), (l2[0] < r5[0] || l2[0] > r5[1] || l2[1] < r5[2] || l2[1] > r5[3] || t2 && (l2[2] < r5[4] || l2[2] > r5[5])) && (l2.length = 0)), !l2.length) {
    const e5 = n[0] && n[0].length ? i3(n[0], t2) : null;
    if (!e5) return null;
    l2[0] = e5[0], l2[1] = e5[1], t2 && e5.length > 2 && (l2[2] = e5[2]);
  }
  return l2;
}
function u2(n, t2, l2) {
  let e4 = 0, r5 = 0, o3 = 0, u3 = 0, i4 = 0;
  const s3 = n.length ? n[0][0] : 0, g3 = n.length ? n[0][1] : 0, h4 = n.length && t2 ? n[0][2] : 0;
  for (let f3 = 0; f3 < n.length; f3++) {
    const c5 = n[f3], m2 = n[(f3 + 1) % n.length], [x3, a3, y3] = c5, p5 = x3 - s3, z2 = a3 - g3, [Z, d, j2] = m2, U = Z - s3, b = d - g3, k = p5 * b - U * z2;
    if (u3 += k, e4 += (p5 + U) * k, r5 += (z2 + b) * k, t2 && c5.length > 2 && m2.length > 2) {
      const n2 = y3 - h4, t3 = j2 - h4, l3 = p5 * t3 - U * n2;
      o3 += (n2 + t3) * l3, i4 += l3;
    }
    x3 < l2[0] && (l2[0] = x3), x3 > l2[1] && (l2[1] = x3), a3 < l2[2] && (l2[2] = a3), a3 > l2[3] && (l2[3] = a3), t2 && (y3 < l2[4] && (l2[4] = y3), y3 > l2[5] && (l2[5] = y3));
  }
  if (u3 > 0 && (u3 *= -1), i4 > 0 && (i4 *= -1), !u3) return null;
  u3 *= 0.5, i4 *= 0.5;
  const c4 = [e4 / (6 * u3) + s3, r5 / (6 * u3) + g3, u3];
  return t2 && (l2[4] === l2[5] || 0 === i4 ? (c4[3] = (l2[4] + l2[5]) / 2, c4[4] = 0) : (c4[3] = o3 / (6 * i4) + h4, c4[4] = i4)), c4;
}
function i3(l2, e4) {
  const r5 = e4 ? [0, 0, 0] : [0, 0], o3 = e4 ? [0, 0, 0] : [0, 0];
  let u3 = 0, i4 = 0, s3 = 0, g3 = 0;
  for (let h4 = 0, c4 = l2.length; h4 < c4 - 1; h4++) {
    const c5 = l2[h4], f3 = l2[h4 + 1];
    if (c5 && f3) {
      r5[0] = c5[0], r5[1] = c5[1], o3[0] = f3[0], o3[1] = f3[1], e4 && c5.length > 2 && f3.length > 2 && (r5[2] = c5[2], o3[2] = f3[2]);
      const l3 = r3(r5, o3);
      if (l3) {
        u3 += l3;
        const n = f2(c5, f3);
        i4 += l3 * n[0], s3 += l3 * n[1], e4 && n.length > 2 && (g3 += l3 * n[2]);
      }
    }
  }
  return u3 > 0 ? e4 ? [i4 / u3, s3 / u3, g3 / u3] : [i4 / u3, s3 / u3] : l2.length ? l2[0] : null;
}
var s2 = 1e-6;
function g2(n) {
  if (!n || !n.rings) return null;
  const { rings: t2 } = n;
  let e4 = 0;
  for (let o3 = 0; o3 < t2.length; o3++) e4 += M2(t2[o3]);
  if (e4 < s2) return o2(t2, false);
  const r5 = [0, 0], u3 = t2[0][0];
  for (let l2 = 0; l2 < t2.length; l2++) c3(r5, u3, t2[l2]);
  return r5[0] *= 1 / e4, r5[1] *= 1 / e4, r5[0] += u3[0], r5[1] += u3[1], r5;
}
var h3 = 1 / 3;
function c3(n, t2, e4) {
  if (!n || !e4 || e4.length < 3) return null;
  const r5 = e4[0], o3 = [0, 0], u3 = [e4[1][0] - r5[0], e4[1][1] - r5[1]];
  let i4;
  for (let l2 = 2; l2 < e4.length; l2++) o3[0] = e4[l2][0] - r5[0], o3[1] = e4[l2][1] - r5[1], i4 = 0.5 * h3 * (o3[0] * u3[1] - o3[1] * u3[0]), n[0] += i4 * (u3[0] + o3[0]), n[1] += i4 * (u3[1] + o3[1]), u3[0] = o3[0], u3[1] = o3[1];
  const s3 = M2(e4), g3 = [r5[0], r5[1]];
  return g3[0] -= t2[0], g3[1] -= t2[1], g3[0] *= s3, g3[1] *= s3, n[0] += g3[0], n[1] += g3[1], n;
}

// node_modules/@arcgis/core/geometry/Polygon.js
var x2;
function j(t2) {
  return !Array.isArray(t2[0]);
}
var w3 = x2 = class extends p2 {
  static fromExtent(t2) {
    const r5 = t2.clone().normalize(), e4 = t2.spatialReference;
    let s3 = false, i4 = false;
    for (const o3 of r5) o3.hasZ && (s3 = true), o3.hasM && (i4 = true);
    const n = { rings: r5.map((t3) => {
      const r6 = [[t3.xmin, t3.ymin], [t3.xmin, t3.ymax], [t3.xmax, t3.ymax], [t3.xmax, t3.ymin], [t3.xmin, t3.ymin]];
      if (s3 && t3.hasZ) {
        const e5 = t3.zmin + 0.5 * (t3.zmax - t3.zmin);
        for (let t4 = 0; t4 < r6.length; t4++) r6[t4].push(e5);
      }
      if (i4 && t3.hasM) {
        const e5 = t3.mmin + 0.5 * (t3.mmax - t3.mmin);
        for (let t4 = 0; t4 < r6.length; t4++) r6[t4].push(e5);
      }
      return r6;
    }), spatialReference: e4 };
    return s3 && (n.hasZ = true), i4 && (n.hasM = true), new x2(n);
  }
  constructor(...t2) {
    super(...t2), this.rings = [], this.type = "polygon";
  }
  normalizeCtorArgs(t2, r5) {
    let e4, s3, i4 = null, n = null;
    return t2 && !Array.isArray(t2) ? (i4 = t2.rings ? t2.rings : null, r5 || (t2.spatialReference ? r5 = t2.spatialReference : t2.rings || (r5 = t2)), e4 = t2.hasZ, s3 = t2.hasM) : i4 = t2, i4 = i4 || [], r5 = r5 || f.WGS84, i4.length && i4[0] && null != i4[0][0] && "number" == typeof i4[0][0] && (i4 = [i4]), n = i4[0] && i4[0][0], n && (void 0 === e4 && void 0 === s3 ? (e4 = n.length > 2, s3 = n.length > 3) : void 0 === e4 ? e4 = s3 ? n.length > 3 : n.length > 2 : void 0 === s3 && (s3 = e4 ? n.length > 3 : n.length > 2)), { rings: i4, spatialReference: r5, hasZ: e4, hasM: s3 };
  }
  get cache() {
    return this.commitProperty("rings"), this.commitProperty("hasZ"), this.commitProperty("hasM"), this.commitProperty("spatialReference"), {};
  }
  get centroid() {
    const t2 = r4(this);
    if (!t2 || isNaN(t2[0]) || isNaN(t2[1]) || this.hasZ && isNaN(t2[2])) return null;
    const r5 = new w();
    return r5.x = t2[0], r5.y = t2[1], r5.spatialReference = this.spatialReference, this.hasZ && (r5.z = t2[2]), r5;
  }
  get extent() {
    const { spatialReference: t2 } = this, r5 = v(this);
    if (!r5) return null;
    const e4 = new w2(r5);
    return e4.spatialReference = t2, e4;
  }
  get isSelfIntersecting() {
    return p3(this.rings);
  }
  writeRings(t2, r5) {
    r5.rings = p(this.rings);
  }
  addRing(t2) {
    if (!t2) return;
    const r5 = this.rings, e4 = r5.length;
    if (j(t2)) {
      const s3 = [];
      for (let r6 = 0, e5 = t2.length; r6 < e5; r6++) s3[r6] = t2[r6].toArray();
      r5[e4] = s3;
    } else r5[e4] = t2.concat();
    return this.notifyChange("rings"), this;
  }
  clone() {
    const t2 = new x2();
    return t2.spatialReference = this.spatialReference, t2.rings = p(this.rings), t2.hasZ = this.hasZ, t2.hasM = this.hasM, t2;
  }
  equals(t2) {
    if (this === t2) return true;
    if (t(t2)) return false;
    const e4 = this.spatialReference, n = t2.spatialReference;
    if (r(e4) !== r(n)) return false;
    if (r(e4) && r(n) && !e4.equals(n)) return false;
    if (this.rings.length !== t2.rings.length) return false;
    const o3 = ([t3, r5, e5, s3], [i4, n2, o4, a3]) => t3 === i4 && r5 === n2 && (null == e5 && null == o4 || e5 === o4) && (null == s3 && null == a3 || s3 === a3);
    for (let s3 = 0; s3 < this.rings.length; s3++) {
      const e5 = this.rings[s3], i4 = t2.rings[s3];
      if (!i(e5, i4, o3)) return false;
    }
    return true;
  }
  contains(t2) {
    if (!t2) return false;
    const r5 = M(t2, this.spatialReference);
    return c(this, r(r5) ? r5 : t2);
  }
  isClockwise(t2) {
    let r5;
    return r5 = j(t2) ? t2.map((t3) => this.hasZ ? this.hasM ? [t3.x, t3.y, t3.z, t3.m] : [t3.x, t3.y, t3.z] : [t3.x, t3.y]) : t2, h2(r5, this.hasM, this.hasZ);
  }
  getPoint(t2, r5) {
    if (!this._validateInputs(t2, r5)) return null;
    const e4 = this.rings[t2][r5], s3 = this.hasZ, i4 = this.hasM;
    return s3 && !i4 ? new w(e4[0], e4[1], e4[2], void 0, this.spatialReference) : i4 && !s3 ? new w(e4[0], e4[1], void 0, e4[2], this.spatialReference) : s3 && i4 ? new w(e4[0], e4[1], e4[2], e4[3], this.spatialReference) : new w(e4[0], e4[1], this.spatialReference);
  }
  insertPoint(t2, r5, e4) {
    return this._validateInputs(t2, r5, true) ? (h(this, e4), Array.isArray(e4) || (e4 = e4.toArray()), this.rings[t2].splice(r5, 0, e4), this.notifyChange("rings"), this) : this;
  }
  removePoint(t2, r5) {
    if (!this._validateInputs(t2, r5)) return null;
    const e4 = new w(this.rings[t2].splice(r5, 1)[0], this.spatialReference);
    return this.notifyChange("rings"), e4;
  }
  removeRing(t2) {
    if (!this._validateInputs(t2, null)) return null;
    const r5 = this.rings.splice(t2, 1)[0], e4 = this.spatialReference, s3 = r5.map((t3) => new w(t3, e4));
    return this.notifyChange("rings"), s3;
  }
  setPoint(t2, r5, e4) {
    return this._validateInputs(t2, r5) ? (h(this, e4), Array.isArray(e4) || (e4 = e4.toArray()), this.rings[t2][r5] = e4, this.notifyChange("rings"), this) : this;
  }
  _validateInputs(t2, r5, e4 = false) {
    if (null == t2 || t2 < 0 || t2 >= this.rings.length) return false;
    if (null != r5) {
      const s3 = this.rings[t2];
      if (e4 && (r5 < 0 || r5 > s3.length)) return false;
      if (!e4 && (r5 < 0 || r5 >= s3.length)) return false;
    }
    return true;
  }
  toJSON(t2) {
    return this.write({}, t2);
  }
};
e([y({ readOnly: true })], w3.prototype, "cache", null), e([y({ readOnly: true })], w3.prototype, "centroid", null), e([y({ readOnly: true })], w3.prototype, "extent", null), e([y({ readOnly: true })], w3.prototype, "isSelfIntersecting", null), e([y({ type: [[[Number]]], json: { write: { isRequired: true } } })], w3.prototype, "rings", void 0), e([r2("rings")], w3.prototype, "writeRings", null), w3 = x2 = e([a("esri.geometry.Polygon")], w3), w3.prototype.toJSON.isDefaultToJSON = true;
var v2 = w3;

export {
  e2 as e,
  o,
  s,
  f2 as f,
  u,
  c2 as c,
  h2 as h,
  g,
  p4 as p,
  y2 as y,
  m,
  M2 as M,
  z,
  e3 as e2,
  r4 as r,
  o2,
  u2,
  g2,
  v2 as v
};
//# sourceMappingURL=chunk-X7FOCGBC.js.map
