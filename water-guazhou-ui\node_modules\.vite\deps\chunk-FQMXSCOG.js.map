{"version": 3, "sources": ["../../@arcgis/core/geometry/support/lineSegment.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clamp as t}from\"../../core/mathUtils.js\";import{ObjectStack as r}from\"../../core/ObjectStack.js\";import{c as n,b as o,e,g as i,a as c,p as s,d as u}from\"../../chunks/vec3.js\";import{a,c as g}from\"../../chunks/vec3f64.js\";import{sv3d as f}from\"./vectorStacks.js\";function v(t){return t?{origin:a(t.origin),vector:a(t.vector)}:{origin:g(),vector:g()}}function p(t,r){const n=x.get();return n.origin=t,n.vector=r,n}function h(t,r=v()){return m(t.origin,t.vector,r)}function m(t,r,o=v()){return n(o.origin,t),n(o.vector,r),o}function b(t,r,e=v()){return n(e.origin,t),o(e.vector,r,t),e}function M(r,n){const c=o(f.get(),n,r.origin),s=e(r.vector,c),u=e(r.vector,r.vector),a=t(s/u,0,1),g=o(f.get(),i(f.get(),r.vector,a),c);return e(g,g)}function d(t,r){return Math.sqrt(M(t,r))}function j(t,r,n){return A(t,r,0,1,n)}function l(t,r,n){return c(n,t.origin,i(n,t.vector,r))}function A(r,n,u,a,g){const{vector:v,origin:p}=r,h=o(f.get(),n,p),m=e(v,h)/s(v);return i(g,v,t(m,u,a)),c(g,g,r.origin)}function B(t,r){if(q(t,p(r.origin,r.direction),!1,w)){const{tA:r,pB:n,distance2:o}=w;if(r>=0&&r<=1)return o;if(r<0)return u(t.origin,n);if(r>1)return u(c(f.get(),t.origin,t.vector),n)}return null}function k(t,r,o){return!!q(t,r,!0,w)&&(n(o,w.pA),!0)}function S(t,r){return q(t,r,!0,w)?w.distance2:null}function q(r,n,o,e){const i=1e-6,s=r.origin,a=c(f.get(),s,r.vector),g=n.origin,v=c(f.get(),g,n.vector),p=f.get(),h=f.get();if(p[0]=s[0]-g[0],p[1]=s[1]-g[1],p[2]=s[2]-g[2],h[0]=v[0]-g[0],h[1]=v[1]-g[1],h[2]=v[2]-g[2],Math.abs(h[0])<i&&Math.abs(h[1])<i&&Math.abs(h[2])<i)return!1;const m=f.get();if(m[0]=a[0]-s[0],m[1]=a[1]-s[1],m[2]=a[2]-s[2],Math.abs(m[0])<i&&Math.abs(m[1])<i&&Math.abs(m[2])<i)return!1;const b=p[0]*h[0]+p[1]*h[1]+p[2]*h[2],M=h[0]*m[0]+h[1]*m[1]+h[2]*m[2],d=p[0]*m[0]+p[1]*m[1]+p[2]*m[2],j=h[0]*h[0]+h[1]*h[1]+h[2]*h[2],l=(m[0]*m[0]+m[1]*m[1]+m[2]*m[2])*j-M*M;if(Math.abs(l)<i)return!1;let A=(b*M-d*j)/l,B=(b+M*A)/j;o&&(A=t(A,0,1),B=t(B,0,1));const k=f.get(),S=f.get();return k[0]=s[0]+A*m[0],k[1]=s[1]+A*m[1],k[2]=s[2]+A*m[2],S[0]=g[0]+B*h[0],S[1]=g[1]+B*h[1],S[2]=g[2]+B*h[2],e.tA=A,e.tB=B,e.pA=k,e.pB=S,e.distance2=u(k,S),!0}const w={tA:0,tB:0,pA:g(),pB:g(),distance2:0},x=new r((()=>v()));export{S as closestLineSegmentDistance2,k as closestLineSegmentPoint,B as closestRayDistance2,h as copy,v as create,d as distance,M as distance2,b as fromPoints,m as fromValues,l as pointAt,j as projectPoint,A as projectPointClamp,p as wrap};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAI6Q,SAASA,GAAEC,IAAE;AAAC,SAAOA,KAAE,EAAC,QAAO,EAAEA,GAAE,MAAM,GAAE,QAAO,EAAEA,GAAE,MAAM,EAAC,IAAE,EAAC,QAAO,EAAE,GAAE,QAAO,EAAE,EAAC;AAAC;AAAC,SAASC,GAAED,IAAEE,IAAE;AAAC,QAAMC,KAAE,EAAE,IAAI;AAAE,SAAOA,GAAE,SAAOH,IAAEG,GAAE,SAAOD,IAAEC;AAAC;AAAC,SAAS,EAAEH,IAAEE,KAAEH,GAAE,GAAE;AAAC,SAAO,EAAEC,GAAE,QAAOA,GAAE,QAAOE,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEE,IAAE,IAAEH,GAAE,GAAE;AAAC,SAAO,EAAE,EAAE,QAAOC,EAAC,GAAE,EAAE,EAAE,QAAOE,EAAC,GAAE;AAAC;AAAC,SAAS,EAAEF,IAAEE,IAAEE,KAAEL,GAAE,GAAE;AAAC,SAAO,EAAEK,GAAE,QAAOJ,EAAC,GAAE,EAAEI,GAAE,QAAOF,IAAEF,EAAC,GAAEI;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAE;AAAC,QAAME,KAAE,EAAE,EAAE,IAAI,GAAEF,IAAED,GAAE,MAAM,GAAEI,KAAE,EAAEJ,GAAE,QAAOG,EAAC,GAAEE,KAAE,EAAEL,GAAE,QAAOA,GAAE,MAAM,GAAEM,KAAE,EAAEF,KAAEC,IAAE,GAAE,CAAC,GAAEE,KAAE,EAAE,EAAE,IAAI,GAAE,EAAE,EAAE,IAAI,GAAEP,GAAE,QAAOM,EAAC,GAAEH,EAAC;AAAE,SAAO,EAAEI,IAAEA,EAAC;AAAC;AAA0C,SAAS,EAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAEF,IAAEC,IAAE,GAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAEA,IAAEF,GAAE,QAAO,EAAEE,IAAEF,GAAE,QAAOC,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAK,EAAC,QAAOC,IAAE,QAAOC,GAAC,IAAEN,IAAEO,KAAE,EAAE,EAAE,IAAI,GAAEN,IAAEK,EAAC,GAAEE,KAAE,EAAEH,IAAEE,EAAC,IAAE,EAAEF,EAAC;AAAE,SAAO,EAAED,IAAEC,IAAE,EAAEG,IAAEN,IAAEC,EAAC,CAAC,GAAE,EAAEC,IAAEA,IAAEJ,GAAE,MAAM;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,MAAG,EAAED,IAAEO,GAAEN,GAAE,QAAOA,GAAE,SAAS,GAAE,OAAG,CAAC,GAAE;AAAC,UAAK,EAAC,IAAGA,IAAE,IAAGC,IAAE,WAAU,EAAC,IAAE;AAAE,QAAGD,MAAG,KAAGA,MAAG,EAAE,QAAO;AAAE,QAAGA,KAAE,EAAE,QAAO,EAAED,GAAE,QAAOE,EAAC;AAAE,QAAGD,KAAE,EAAE,QAAO,EAAE,EAAE,EAAE,IAAI,GAAED,GAAE,QAAOA,GAAE,MAAM,GAAEE,EAAC;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAAS,EAAEF,IAAEC,IAAE,GAAE;AAAC,SAAM,CAAC,CAAC,EAAED,IAAEC,IAAE,MAAG,CAAC,MAAI,EAAE,GAAE,EAAE,EAAE,GAAE;AAAG;AAAqD,SAAS,EAAES,IAAEC,IAAE,GAAEC,IAAE;AAAC,QAAM,IAAE,MAAKC,KAAEH,GAAE,QAAOI,KAAE,EAAE,EAAE,IAAI,GAAED,IAAEH,GAAE,MAAM,GAAEK,KAAEJ,GAAE,QAAOK,KAAE,EAAE,EAAE,IAAI,GAAED,IAAEJ,GAAE,MAAM,GAAEM,KAAE,EAAE,IAAI,GAAEC,KAAE,EAAE,IAAI;AAAE,MAAGD,GAAE,CAAC,IAAEJ,GAAE,CAAC,IAAEE,GAAE,CAAC,GAAEE,GAAE,CAAC,IAAEJ,GAAE,CAAC,IAAEE,GAAE,CAAC,GAAEE,GAAE,CAAC,IAAEJ,GAAE,CAAC,IAAEE,GAAE,CAAC,GAAEG,GAAE,CAAC,IAAEF,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEG,GAAE,CAAC,IAAEF,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEG,GAAE,CAAC,IAAEF,GAAE,CAAC,IAAED,GAAE,CAAC,GAAE,KAAK,IAAIG,GAAE,CAAC,CAAC,IAAE,KAAG,KAAK,IAAIA,GAAE,CAAC,CAAC,IAAE,KAAG,KAAK,IAAIA,GAAE,CAAC,CAAC,IAAE,EAAE,QAAM;AAAG,QAAMC,KAAE,EAAE,IAAI;AAAE,MAAGA,GAAE,CAAC,IAAEL,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEM,GAAE,CAAC,IAAEL,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEM,GAAE,CAAC,IAAEL,GAAE,CAAC,IAAED,GAAE,CAAC,GAAE,KAAK,IAAIM,GAAE,CAAC,CAAC,IAAE,KAAG,KAAK,IAAIA,GAAE,CAAC,CAAC,IAAE,KAAG,KAAK,IAAIA,GAAE,CAAC,CAAC,IAAE,EAAE,QAAM;AAAG,QAAMC,KAAEH,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAEG,KAAEH,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,GAAE,IAAEF,GAAE,CAAC,IAAEE,GAAE,CAAC,IAAEF,GAAE,CAAC,IAAEE,GAAE,CAAC,IAAEF,GAAE,CAAC,IAAEE,GAAE,CAAC,GAAEG,KAAEJ,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAEK,MAAGJ,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGG,KAAED,KAAEA;AAAE,MAAG,KAAK,IAAIE,EAAC,IAAE,EAAE,QAAM;AAAG,MAAIC,MAAGJ,KAAEC,KAAE,IAAEC,MAAGC,IAAEE,MAAGL,KAAEC,KAAEG,MAAGF;AAAE,QAAIE,KAAE,EAAEA,IAAE,GAAE,CAAC,GAAEC,KAAE,EAAEA,IAAE,GAAE,CAAC;AAAG,QAAMC,KAAE,EAAE,IAAI,GAAE,IAAE,EAAE,IAAI;AAAE,SAAOA,GAAE,CAAC,IAAEb,GAAE,CAAC,IAAEW,KAAEL,GAAE,CAAC,GAAEO,GAAE,CAAC,IAAEb,GAAE,CAAC,IAAEW,KAAEL,GAAE,CAAC,GAAEO,GAAE,CAAC,IAAEb,GAAE,CAAC,IAAEW,KAAEL,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEJ,GAAE,CAAC,IAAEU,KAAEP,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEH,GAAE,CAAC,IAAEU,KAAEP,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEH,GAAE,CAAC,IAAEU,KAAEP,GAAE,CAAC,GAAEN,GAAE,KAAGY,IAAEZ,GAAE,KAAGa,IAAEb,GAAE,KAAGc,IAAEd,GAAE,KAAG,GAAEA,GAAE,YAAU,EAAEc,IAAE,CAAC,GAAE;AAAE;AAAC,IAAM,IAAE,EAAC,IAAG,GAAE,IAAG,GAAE,IAAG,EAAE,GAAE,IAAG,EAAE,GAAE,WAAU,EAAC;AAA5C,IAA8C,IAAE,IAAI,EAAG,MAAIV,GAAE,CAAE;", "names": ["v", "t", "p", "r", "n", "e", "c", "s", "u", "a", "g", "t", "r", "n", "u", "a", "g", "v", "p", "h", "m", "r", "n", "e", "s", "a", "g", "v", "p", "h", "m", "b", "M", "j", "l", "A", "B", "k"]}