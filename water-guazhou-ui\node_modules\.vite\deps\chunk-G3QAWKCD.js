import {
  a as a2,
  i
} from "./chunk-ORU3OGKZ.js";
import {
  b,
  j,
  l
} from "./chunk-QC5SLERR.js";
import {
  e as e2
} from "./chunk-HM62IZSE.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has,
  p
} from "./chunk-REW33H3I.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/MD5.js
var n = { Base64: 0, Hex: 1, String: 2, Raw: 3 };
var t2 = 8;
var r2 = (1 << t2) - 1;
function e3(n3, t3) {
  const r3 = (65535 & n3) + (65535 & t3);
  return (n3 >> 16) + (t3 >> 16) + (r3 >> 16) << 16 | 65535 & r3;
}
function o(n3) {
  const e4 = [];
  for (let o2 = 0, u2 = n3.length * t2; o2 < u2; o2 += t2) e4[o2 >> 5] |= (n3.charCodeAt(o2 / t2) & r2) << o2 % 32;
  return e4;
}
function u(n3) {
  const e4 = [];
  for (let o2 = 0, u2 = 32 * n3.length; o2 < u2; o2 += t2) e4.push(String.fromCharCode(n3[o2 >> 5] >>> o2 % 32 & r2));
  return e4.join("");
}
function c(n3) {
  const t3 = "0123456789abcdef", r3 = [];
  for (let e4 = 0, o2 = 4 * n3.length; e4 < o2; e4++) r3.push(t3.charAt(n3[e4 >> 2] >> e4 % 4 * 8 + 4 & 15) + t3.charAt(n3[e4 >> 2] >> e4 % 4 * 8 & 15));
  return r3.join("");
}
function f(n3) {
  const t3 = "=", r3 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", e4 = [];
  for (let o2 = 0, u2 = 4 * n3.length; o2 < u2; o2 += 3) {
    const u3 = (n3[o2 >> 2] >> o2 % 4 * 8 & 255) << 16 | (n3[o2 + 1 >> 2] >> (o2 + 1) % 4 * 8 & 255) << 8 | n3[o2 + 2 >> 2] >> (o2 + 2) % 4 * 8 & 255;
    for (let c3 = 0; c3 < 4; c3++) 8 * o2 + 6 * c3 > 32 * n3.length ? e4.push(t3) : e4.push(r3.charAt(u3 >> 6 * (3 - c3) & 63));
  }
  return e4.join("");
}
function s3(n3, t3) {
  return n3 << t3 | n3 >>> 32 - t3;
}
function i2(n3, t3, r3, o2, u2, c3) {
  return e3(s3(e3(e3(t3, n3), e3(o2, c3)), u2), r3);
}
function h(n3, t3, r3, e4, o2, u2, c3) {
  return i2(t3 & r3 | ~t3 & e4, n3, t3, o2, u2, c3);
}
function a3(n3, t3, r3, e4, o2, u2, c3) {
  return i2(t3 & e4 | r3 & ~e4, n3, t3, o2, u2, c3);
}
function l2(n3, t3, r3, e4, o2, u2, c3) {
  return i2(t3 ^ r3 ^ e4, n3, t3, o2, u2, c3);
}
function g(n3, t3, r3, e4, o2, u2, c3) {
  return i2(r3 ^ (t3 | ~e4), n3, t3, o2, u2, c3);
}
function p2(n3, t3) {
  n3[t3 >> 5] |= 128 << t3 % 32, n3[14 + (t3 + 64 >>> 9 << 4)] = t3;
  let r3 = 1732584193, o2 = -271733879, u2 = -1732584194, c3 = 271733878;
  for (let f3 = 0; f3 < n3.length; f3 += 16) {
    const t4 = r3, s4 = o2, i3 = u2, p4 = c3;
    r3 = h(r3, o2, u2, c3, n3[f3 + 0], 7, -680876936), c3 = h(c3, r3, o2, u2, n3[f3 + 1], 12, -389564586), u2 = h(u2, c3, r3, o2, n3[f3 + 2], 17, 606105819), o2 = h(o2, u2, c3, r3, n3[f3 + 3], 22, -1044525330), r3 = h(r3, o2, u2, c3, n3[f3 + 4], 7, -176418897), c3 = h(c3, r3, o2, u2, n3[f3 + 5], 12, 1200080426), u2 = h(u2, c3, r3, o2, n3[f3 + 6], 17, -1473231341), o2 = h(o2, u2, c3, r3, n3[f3 + 7], 22, -45705983), r3 = h(r3, o2, u2, c3, n3[f3 + 8], 7, 1770035416), c3 = h(c3, r3, o2, u2, n3[f3 + 9], 12, -1958414417), u2 = h(u2, c3, r3, o2, n3[f3 + 10], 17, -42063), o2 = h(o2, u2, c3, r3, n3[f3 + 11], 22, -1990404162), r3 = h(r3, o2, u2, c3, n3[f3 + 12], 7, 1804603682), c3 = h(c3, r3, o2, u2, n3[f3 + 13], 12, -40341101), u2 = h(u2, c3, r3, o2, n3[f3 + 14], 17, -1502002290), o2 = h(o2, u2, c3, r3, n3[f3 + 15], 22, 1236535329), r3 = a3(r3, o2, u2, c3, n3[f3 + 1], 5, -165796510), c3 = a3(c3, r3, o2, u2, n3[f3 + 6], 9, -1069501632), u2 = a3(u2, c3, r3, o2, n3[f3 + 11], 14, 643717713), o2 = a3(o2, u2, c3, r3, n3[f3 + 0], 20, -373897302), r3 = a3(r3, o2, u2, c3, n3[f3 + 5], 5, -701558691), c3 = a3(c3, r3, o2, u2, n3[f3 + 10], 9, 38016083), u2 = a3(u2, c3, r3, o2, n3[f3 + 15], 14, -660478335), o2 = a3(o2, u2, c3, r3, n3[f3 + 4], 20, -405537848), r3 = a3(r3, o2, u2, c3, n3[f3 + 9], 5, 568446438), c3 = a3(c3, r3, o2, u2, n3[f3 + 14], 9, -1019803690), u2 = a3(u2, c3, r3, o2, n3[f3 + 3], 14, -187363961), o2 = a3(o2, u2, c3, r3, n3[f3 + 8], 20, 1163531501), r3 = a3(r3, o2, u2, c3, n3[f3 + 13], 5, -1444681467), c3 = a3(c3, r3, o2, u2, n3[f3 + 2], 9, -51403784), u2 = a3(u2, c3, r3, o2, n3[f3 + 7], 14, 1735328473), o2 = a3(o2, u2, c3, r3, n3[f3 + 12], 20, -1926607734), r3 = l2(r3, o2, u2, c3, n3[f3 + 5], 4, -378558), c3 = l2(c3, r3, o2, u2, n3[f3 + 8], 11, -2022574463), u2 = l2(u2, c3, r3, o2, n3[f3 + 11], 16, 1839030562), o2 = l2(o2, u2, c3, r3, n3[f3 + 14], 23, -35309556), r3 = l2(r3, o2, u2, c3, n3[f3 + 1], 4, -1530992060), c3 = l2(c3, r3, o2, u2, n3[f3 + 4], 11, 1272893353), u2 = l2(u2, c3, r3, o2, n3[f3 + 7], 16, -155497632), o2 = l2(o2, u2, c3, r3, n3[f3 + 10], 23, -1094730640), r3 = l2(r3, o2, u2, c3, n3[f3 + 13], 4, 681279174), c3 = l2(c3, r3, o2, u2, n3[f3 + 0], 11, -358537222), u2 = l2(u2, c3, r3, o2, n3[f3 + 3], 16, -722521979), o2 = l2(o2, u2, c3, r3, n3[f3 + 6], 23, 76029189), r3 = l2(r3, o2, u2, c3, n3[f3 + 9], 4, -640364487), c3 = l2(c3, r3, o2, u2, n3[f3 + 12], 11, -421815835), u2 = l2(u2, c3, r3, o2, n3[f3 + 15], 16, 530742520), o2 = l2(o2, u2, c3, r3, n3[f3 + 2], 23, -995338651), r3 = g(r3, o2, u2, c3, n3[f3 + 0], 6, -198630844), c3 = g(c3, r3, o2, u2, n3[f3 + 7], 10, 1126891415), u2 = g(u2, c3, r3, o2, n3[f3 + 14], 15, -1416354905), o2 = g(o2, u2, c3, r3, n3[f3 + 5], 21, -57434055), r3 = g(r3, o2, u2, c3, n3[f3 + 12], 6, 1700485571), c3 = g(c3, r3, o2, u2, n3[f3 + 3], 10, -1894986606), u2 = g(u2, c3, r3, o2, n3[f3 + 10], 15, -1051523), o2 = g(o2, u2, c3, r3, n3[f3 + 1], 21, -2054922799), r3 = g(r3, o2, u2, c3, n3[f3 + 8], 6, 1873313359), c3 = g(c3, r3, o2, u2, n3[f3 + 15], 10, -30611744), u2 = g(u2, c3, r3, o2, n3[f3 + 6], 15, -1560198380), o2 = g(o2, u2, c3, r3, n3[f3 + 13], 21, 1309151649), r3 = g(r3, o2, u2, c3, n3[f3 + 4], 6, -145523070), c3 = g(c3, r3, o2, u2, n3[f3 + 11], 10, -1120210379), u2 = g(u2, c3, r3, o2, n3[f3 + 2], 15, 718787259), o2 = g(o2, u2, c3, r3, n3[f3 + 9], 21, -343485551), r3 = e3(r3, t4), o2 = e3(o2, s4), u2 = e3(u2, i3), c3 = e3(c3, p4);
  }
  return [r3, o2, u2, c3];
}
function x(r3, e4 = n.Hex) {
  const s4 = e4 || n.Base64, i3 = p2(o(r3), r3.length * t2);
  switch (s4) {
    case n.Raw:
      return i3;
    case n.Hex:
      return c(i3);
    case n.String:
      return u(i3);
    case n.Base64:
      return f(i3);
  }
}

// node_modules/@arcgis/core/views/2d/engine/LevelDependentSizeVariable.js
var l3;
var n2 = l3 = class extends b {
  writeLevels(e4, s4, i3) {
    for (const t3 in e4) {
      const e5 = this.levels[t3];
      return void (s4.stops = e5);
    }
  }
  clone() {
    return new l3({ axis: this.axis, field: this.field, valueExpression: this.valueExpression, valueExpressionTitle: this.valueExpressionTitle, maxDataValue: this.maxDataValue, maxSize: e2(this.maxSize) ? this.maxSize.clone() : this.maxSize, minDataValue: this.minDataValue, minSize: e2(this.minSize) ? this.minSize.clone() : this.minSize, normalizationField: this.normalizationField, stops: this.stops && this.stops.map((e4) => e4.clone()), target: this.target, useSymbolValue: this.useSymbolValue, valueRepresentation: this.valueRepresentation, valueUnit: this.valueUnit, legendOptions: this.legendOptions && this.legendOptions.clone(), levels: p(this.levels) });
  }
};
e([y()], n2.prototype, "levels", void 0), e([r("levels")], n2.prototype, "writeLevels", null), n2 = l3 = e([a("esri.views.2d.engine.LevelDependentSizeVariable")], n2);

// node_modules/@arcgis/core/views/2d/layers/support/clusterUtils.js
var p3 = s.getLogger("esri.views.2d.layers.support.clusterUtils");
has.add("esri-cluster-arcade-enabled", true);
var c2 = has("esri-cluster-arcade-enabled");
var d = (e4, r3, i3, n3, a4) => {
  const l4 = r3.clone();
  if (!g2(l4)) return l4;
  if (l4.authoringInfo || (l4.authoringInfo = new j()), l4.authoringInfo.isAutoGenerated = true, "visualVariables" in l4) {
    const r4 = (l4.visualVariables || []).filter((e5) => "$view.scale" !== e5.valueExpression), t3 = f2(r4);
    r4.forEach((r5) => {
      "rotation" === r5.type ? r5.field ? r5.field = w(e4, r5.field, "avg_angle", "number") : r5.valueExpression && (r5.field = E(e4, r5.valueExpression, "avg_angle", "number"), r5.valueExpression = null) : r5.normalizationField ? (r5.field = w(e4, r5.field, "avg_norm", "number", r5.normalizationField), r5.normalizationField = null) : r5.field ? r5.field = w(e4, r5.field, "avg", "number") : r5.valueExpression && (r5.field = E(e4, r5.valueExpression, "avg", "number"), r5.valueExpression = null);
    }), t(t3) && !v(r4) && a4 && (r4.push(b2(i3, n3)), l4.dynamicClusterSize = true), l4.visualVariables = r4;
  }
  switch (l4.type) {
    case "simple":
      break;
    case "pie-chart":
      for (const r4 of l4.attributes) r4.field ? r4.field = w(e4, r4.field, "sum", "number") : r4.valueExpression && (r4.field = E(e4, r4.valueExpression, "sum", "number"), r4.valueExpression = null);
      break;
    case "unique-value":
      l4.field ? l4.field = w(e4, l4.field, "mode", "string") : l4.valueExpression && (l4.field = E(e4, l4.valueExpression, "mode", "string"), l4.valueExpression = null);
      break;
    case "class-breaks":
      l4.normalizationField ? (l4.field = w(e4, l4.field, "avg_norm", "number", l4.normalizationField), l4.normalizationField = null) : l4.field ? l4.field = w(e4, l4.field, "avg", "number") : l4.valueExpression && (l4.field = E(e4, l4.valueExpression, "avg", "number"), l4.valueExpression = null);
  }
  return l4;
};
var f2 = (e4) => {
  for (const r3 of e4) if ("size" === r3.type) return r3;
  return null;
};
function m(e4, r3, i3) {
  const n3 = e4.clone();
  let a4 = false;
  if ("visualVariables" in n3) {
    const e5 = (n3.visualVariables || []).filter((e6) => "$view.scale" !== e6.valueExpression), t3 = f2(e5);
    t(t3) && (n3.visualVariables || (n3.visualVariables = []), n3.visualVariables.push(b2(r3, i3)), n3.dynamicClusterSize = true, a4 = true);
  }
  return { renderer: n3, didInject: a4 };
}
var v = (e4) => {
  for (const r3 of e4) if ("cluster_count" === r3.field) return true;
  return false;
};
var b2 = (e4, r3) => {
  const i3 = [new l({ value: 0, size: 0 }), new l({ value: 1 })];
  if (t(r3)) return new b({ field: "cluster_count", stops: [...i3, new l({ value: 2, size: 0 })] });
  const n3 = Object.keys(r3).reduce((s4, n4) => ({ ...s4, [n4]: [...i3, new l({ value: Math.max(2, r3[n4].minValue), size: e4.clusterMinSize }), new l({ value: Math.max(3, r3[n4].maxValue), size: e4.clusterMaxSize })] }), {});
  return new n2({ field: "cluster_count", levels: n3 });
};
var g2 = (r3) => {
  const s4 = (s5) => p3.error(new s2("Unsupported-renderer", s5, { renderer: r3 }));
  switch (r3.type) {
    case "unique-value":
      if (r3.field2 || r3.field3) return s4("FeatureReductionCluster does not support multi-field UniqueValueRenderers"), false;
      break;
    case "class-breaks":
      if (r3.normalizationField) {
        const e4 = r3.normalizationType;
        if ("field" !== e4) return s4(`FeatureReductionCluster does not support a normalizationType of ${e4}`), false;
      }
      break;
    case "simple":
    case "pie-chart":
      break;
    default:
      return s4(`FeatureReductionCluster does not support renderers of type ${r3.type}`), false;
  }
  if (!c2) {
    if ("valueExpression" in r3 && r3.valueExpression) return s4("FeatureReductionCluster does not currently support renderer.valueExpression. Support will be added in a future release"), false;
    if (("visualVariables" in r3 && r3.visualVariables || []).some((e4) => !(!("valueExpression" in e4) || !e4.valueExpression))) return s4("FeatureReductionCluster does not currently support visualVariables with a valueExpression. Support will be added in a future release"), false;
  }
  return true;
};
function x2(e4, r3, s4) {
  switch (e4) {
    case "sum":
      return `cluster_sum_${r3}`;
    case "avg":
    case "avg_angle":
      return `cluster_avg_${r3}`;
    case "mode":
      return `cluster_type_${r3}`;
    case "avg_norm": {
      const e5 = s4, n3 = "field", a4 = r3.toLowerCase() + ",norm:" + n3 + "," + e5.toLowerCase();
      return "cluster_avg_" + x(a4);
    }
  }
}
function E(e4, r3, s4, t3) {
  const l4 = x(r3), o2 = "mode" === s4 ? `cluster_type_${l4}` : "sum" === s4 ? `cluster_sum_${l4}` : `cluster_avg_${l4}`;
  return e4.some((e5) => e5.name === o2) || e4.push(new a2({ name: o2, isAutoGenerated: true, onStatisticExpression: new i({ expression: r3, returnType: t3 }), statisticType: s4 })), o2;
}
function w(e4, r3, s4, i3, t3) {
  if ("cluster_count" === r3 || e4.some((e5) => e5.name === r3)) return r3;
  const l4 = x2(s4, r3, t3);
  return e4.some((e5) => e5.name === l4) || ("avg_norm" === s4 ? e4.push(new a2({ name: l4, isAutoGenerated: true, onStatisticExpression: new i({ expression: `$feature.${r3} / $feature.${t3}`, returnType: i3 }), statisticType: "avg" })) : e4.push(new a2({ name: l4, isAutoGenerated: true, onStatisticField: r3, statisticType: s4 }))), l4;
}

export {
  n,
  x,
  d,
  f2 as f,
  m,
  b2 as b,
  g2 as g
};
//# sourceMappingURL=chunk-G3QAWKCD.js.map
