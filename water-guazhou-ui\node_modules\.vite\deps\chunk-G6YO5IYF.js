import {
  s as s2
} from "./chunk-CV76WXPW.js";
import {
  b
} from "./chunk-EVADT7ME.js";
import {
  i
} from "./chunk-3JR5KBYG.js";
import {
  a as a2,
  m
} from "./chunk-GE5PSQPZ.js";
import {
  a,
  q
} from "./chunk-AVKOL7OR.js";
import {
  o
} from "./chunk-57XIOVP5.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  e,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/arcade/callExpressionWithCursor.js
function a3(a4, t2, o3) {
  if (t(a4)) return null;
  const u2 = t2.readArcadeFeature();
  try {
    return a4.evaluate({ ...o3, $feature: u2 });
  } catch (n2) {
    return s.getLogger("esri.views.2d.support.arcadeOnDemand").warn("Feature arcade evaluation failed:", n2), null;
  }
}

// node_modules/@arcgis/core/views/2d/layers/features/Store2D.js
var o2 = import("./labelFormatUtils-T7H6SIQV.js");
var c = class {
  constructor(e2, s3) {
    this._canCacheExpressionValue = false, this._sourceInfo = e2, this._storage = s3, this._bitsets = { computed: s3.getBitset(s3.createBitset()) };
  }
  get storage() {
    return this._storage;
  }
  invalidate() {
    this._bitsets.computed.clear();
  }
  async updateSchema(r3, a4) {
    const o3 = m(this._schema, a4);
    if (this._schema = a4, !a4 || t(o3) || !a2(o3, "attributes")) return;
    has("esri-2d-update-debug") && console.debug("Applying Update - Store:", o3), this._bitsets.computed.clear(), r3.targets[a4.name] = true;
    const c3 = a4.attributes, n2 = [], p2 = [];
    for (const e2 in c3) {
      const s3 = c3[e2];
      switch (s3.type) {
        case "field":
          break;
        case "expression":
          n2.push(this._createArcadeComputedField(s3));
          break;
        case "label-expression":
          n2.push(this._createLabelArcadeComputedField(s3));
          break;
        case "statistic":
          p2.push(s3);
      }
    }
    this._computedFields = await Promise.all(n2), this._canCacheExpressionValue = !this._computedFields.some((e2) => "expression" === e2.type && r(e2.expression) && e2.expression.referencesScale()), this._statisticFields = p2;
  }
  setComputedAttributes(e2, s3, t2, i2) {
    const r3 = this._bitsets.computed;
    if (!this._canCacheExpressionValue || !r3.has(t2)) {
      r3.set(t2);
      for (const r4 of this._computedFields) {
        const a4 = this._evaluateField(s3, r4, i2);
        switch (r4.resultType) {
          case "numeric":
            e2.setComputedNumericAtIndex(t2, r4.fieldIndex, a4);
            break;
          case "string":
            e2.setComputedStringAtIndex(t2, r4.fieldIndex, a4);
        }
      }
    }
  }
  async _createArcadeComputedField(e2) {
    const s3 = this._sourceInfo.spatialReference, t2 = this._sourceInfo.fieldsIndex;
    return { ...e2, expression: await o(e2.valueExpression, s3, t2) };
  }
  async _createLabelArcadeComputedField(e2) {
    const s3 = this._sourceInfo.spatialReference, t2 = this._sourceInfo.fieldsIndex, { createLabelFunction: i2 } = await o2, r3 = await i2(e2.label, t2, s3);
    return { ...e2, builder: r3 };
  }
  _evaluateField(e2, s3, t2) {
    switch (s3.type) {
      case "label-expression": {
        const t3 = e2.readArcadeFeature();
        return s3.builder.evaluate(t3) || "";
      }
      case "expression": {
        const { expression: i2 } = s3;
        return a3(i2, e2, { $view: { scale: t2 } });
      }
    }
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/support/FeatureSetReaderPBFIndirect.js
var r2 = class _r extends b {
  static from(e2, t2) {
    return new _r(e2.copy(), t2);
  }
  constructor(r3, t2) {
    super(b.createInstance(), r3.fullSchema()), this._currentIndex = -1, this._reader = r3, this._indices = t2;
  }
  get hasNext() {
    return this._currentIndex + 1 < this._indices.length;
  }
  getSize() {
    return this._indices.length;
  }
  getCursor() {
    return this.copy();
  }
  copy() {
    const e2 = new _r(this._reader.copy(), this._indices);
    return e2._currentIndex = this._currentIndex, e2;
  }
  next() {
    for (; this._nextIndex() && !this._reader._getExists(); ) ;
    return this._currentIndex < this._indices.length;
  }
  _nextIndex() {
    return ++this._currentIndex < this._indices.length && (this._reader.setIndex(this._indices[this._currentIndex]), true);
  }
  setArcadeSpatialReference(e2) {
    this._reader.setArcadeSpatialReference(e2);
  }
  attachStorage(e2) {
    this._reader.attachStorage(e2);
  }
  get geometryType() {
    return this._reader.geometryType;
  }
  get hasFeatures() {
    return this._reader.hasFeatures;
  }
  get exceededTransferLimit() {
    return this._reader.exceededTransferLimit;
  }
  get hasZ() {
    return this._reader.hasZ;
  }
  get hasM() {
    return this._reader.hasM;
  }
  getStorage() {
    return this._reader.getStorage();
  }
  getComputedNumeric(e2) {
    return this._reader.getComputedNumericAtIndex(0);
  }
  setComputedNumeric(e2, r3) {
    return this._reader.setComputedNumericAtIndex(r3, 0);
  }
  getComputedString(e2) {
    return this._reader.getComputedStringAtIndex(0);
  }
  setComputedString(e2, r3) {
    return this._reader.setComputedStringAtIndex(0, r3);
  }
  getComputedNumericAtIndex(e2) {
    return this._reader.getComputedNumericAtIndex(e2);
  }
  setComputedNumericAtIndex(e2, r3) {
    this._reader.setComputedNumericAtIndex(e2, r3);
  }
  getComputedStringAtIndex(e2) {
    return this._reader.getComputedStringAtIndex(e2);
  }
  setComputedStringAtIndex(e2, r3) {
    return this._reader.setComputedStringAtIndex(e2, r3);
  }
  transform(e2, r3, t2, d) {
    const a4 = this.copy();
    return a4._reader = this._reader.transform(e2, r3, t2, d), a4;
  }
  readAttribute(e2, r3 = false) {
    return this._reader.readAttribute(e2, r3);
  }
  readAttributes() {
    return this._reader.readAttributes();
  }
  joinAttributes(e2) {
    return this._reader.joinAttributes(e2);
  }
  readArcadeFeature() {
    return this._reader.readArcadeFeature();
  }
  geometry() {
    return this._reader.geometry();
  }
  field(e2) {
    return this.readAttribute(e2, true);
  }
  hasField(e2) {
    return this._reader.hasField(e2);
  }
  setField(e2, r3) {
    return this._reader.setField(e2, r3);
  }
  keys() {
    return this._reader.keys();
  }
  castToText(e2 = false) {
    return this._reader.castToText(e2);
  }
  getQuantizationTransform() {
    return this._reader.getQuantizationTransform();
  }
  getFieldNames() {
    return this._reader.getFieldNames();
  }
  getAttributeHash() {
    return this._reader.getAttributeHash();
  }
  getObjectId() {
    return this._reader.getObjectId();
  }
  getDisplayId() {
    return this._reader.getDisplayId();
  }
  setDisplayId(e2) {
    return this._reader.setDisplayId(e2);
  }
  getGroupId() {
    return this._reader.getGroupId();
  }
  setGroupId(e2) {
    return this._reader.setGroupId(e2);
  }
  getXHydrated() {
    return this._reader.getXHydrated();
  }
  getYHydrated() {
    return this._reader.getYHydrated();
  }
  getX() {
    return this._reader.getX();
  }
  getY() {
    return this._reader.getY();
  }
  setIndex(e2) {
    return this._reader.setIndex(e2);
  }
  getIndex() {
    return this._reader.getIndex();
  }
  readLegacyFeature() {
    return this._reader.readLegacyFeature();
  }
  readOptimizedFeature() {
    return this._reader.readOptimizedFeature();
  }
  readLegacyPointGeometry() {
    return this._reader.readLegacyPointGeometry();
  }
  readLegacyGeometry() {
    return this._reader.readLegacyGeometry();
  }
  readLegacyCentroid() {
    return this._reader.readLegacyCentroid();
  }
  readGeometryArea() {
    return this._reader.readGeometryArea();
  }
  readUnquantizedGeometry() {
    return this._reader.readUnquantizedGeometry();
  }
  readHydratedGeometry() {
    return this._reader.readHydratedGeometry();
  }
  readGeometry() {
    return this._reader.readGeometry();
  }
  readCentroid() {
    return this._reader.readCentroid();
  }
  _readAttribute(e2, r3) {
    throw new Error("Error: Should not be called. Underlying _reader should be used instead");
  }
  _readAttributes() {
    throw new Error("Error: Should not be called. Underlying _reader should be used instead");
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/FeatureStore2D.js
var c2 = a();
function I(t2, e2) {
  return t2 << 16 | e2;
}
function u(t2) {
  return (4294901760 & t2) >>> 16;
}
function l(t2) {
  return 65535 & t2;
}
var p = { getObjectId: (t2) => t2.getObjectId(), getAttributes: (t2) => t2.readAttributes(), getAttribute: (t2, e2) => t2.readAttribute(e2), cloneWithGeometry: (t2, e2) => t2, getGeometry: (t2) => t2.readHydratedGeometry(), getCentroid: (t2, e2) => t2.readCentroid() };
var g = class extends c {
  constructor(s3, a4, r3) {
    super(s3, a4), this.featureAdapter = p, this.events = new n(), this._featureSetsByInstance = /* @__PURE__ */ new Map(), this._objectIdToDisplayId = /* @__PURE__ */ new Map(), this._spatialIndexInvalid = true, this._indexSearchCache = new s2(50), this._index = i(9, (t2) => ({ minX: this._storage.getXMin(t2), minY: this._storage.getYMin(t2), maxX: this._storage.getXMax(t2), maxY: this._storage.getYMax(t2) })), this.mode = r3;
  }
  get storeStatistics() {
    let t2 = 0, e2 = 0, s3 = 0;
    return this.forEach((a4) => {
      const r3 = a4.readGeometry();
      r3 && (e2 += r3.isPoint ? 1 : r3.lengths.reduce((t3, e3) => t3 + e3, 0), s3 += r3.isPoint ? 1 : r3.lengths.length, t2 += 1);
    }), { featureCount: t2, vertexCount: e2, ringCount: s3 };
  }
  hasInstance(t2) {
    return this._featureSetsByInstance.has(t2);
  }
  onTileData(t2, e2) {
    if (t(e2.addOrUpdate)) return e2;
    if (e2.addOrUpdate.attachStorage(this._storage), "snapshot" === this.mode) {
      const s3 = e2.addOrUpdate.getCursor();
      for (; s3.next(); ) {
        const e3 = s3.getDisplayId();
        this.setComputedAttributes(this._storage, s3, e3, t2.scale);
      }
      return e2;
    }
    this._featureSetsByInstance.set(e2.addOrUpdate.instance, e2.addOrUpdate);
    const a4 = e2.addOrUpdate.getCursor();
    for (; a4.next(); ) this._insertFeature(a4, t2.scale);
    return this._spatialIndexInvalid = true, this.events.emit("changed"), e2;
  }
  search(t2) {
    this._rebuildIndex();
    const e2 = t2.id, s3 = this._indexSearchCache.find((t3) => t3.tileId === e2);
    if (r(s3)) return s3.readers;
    const r3 = /* @__PURE__ */ new Map(), n2 = this._searchIndex(t2.bounds), i2 = [];
    for (const a4 of n2) {
      const t3 = this._storage.getInstanceId(a4), e3 = u(t3), s4 = l(t3);
      r3.has(e3) || r3.set(e3, []);
      r3.get(e3).push(s4);
    }
    return r3.forEach((t3, e3) => {
      const s4 = this._featureSetsByInstance.get(e3);
      i2.push(r2.from(s4, t3));
    }), this._indexSearchCache.enqueue({ tileId: e2, readers: i2 }), i2;
  }
  insert(t2) {
    const e2 = t2.getCursor(), s3 = this._storage;
    for (; e2.next(); ) {
      const t3 = I(e2.instance, e2.getIndex()), a4 = e2.getObjectId(), r3 = this._objectIdToDisplayId.get(a4) ?? this._storage.createDisplayId();
      e2.setDisplayId(r3), s3.setInstanceId(r3, t3), this._objectIdToDisplayId.set(a4, r3);
    }
    this._featureSetsByInstance.set(t2.instance, t2), this._spatialIndexInvalid = true;
  }
  remove(t2) {
    const e2 = this._objectIdToDisplayId.get(t2);
    if (!e2) return;
    const s3 = this._storage.getInstanceId(e2), a4 = l(s3), r3 = u(s3), n2 = this._featureSetsByInstance.get(r3);
    this._objectIdToDisplayId.delete(t2), this._storage.releaseDisplayId(e2), n2.removeAtIndex(a4), n2.isEmpty && this._featureSetsByInstance.delete(r3), this._spatialIndexInvalid = true;
  }
  forEach(t2) {
    this._objectIdToDisplayId.forEach((e2) => {
      const s3 = this._storage.getInstanceId(e2), a4 = this._lookupFeature(s3);
      t2(a4);
    });
  }
  forEachUnsafe(t2) {
    this._objectIdToDisplayId.forEach((e2) => {
      const s3 = this._storage.getInstanceId(e2), a4 = u(s3), r3 = l(s3), n2 = this._getFeatureSet(a4);
      n2.setIndex(r3), t2(n2);
    });
  }
  forEachInBounds(t2, e2) {
    const s3 = this._searchIndex(t2);
    for (const a4 of s3) {
      const t3 = this.lookupFeatureByDisplayId(a4, this._storage);
      e2(e(t3));
    }
  }
  forEachBounds(t2, e2) {
    this._rebuildIndex();
    for (const s3 of t2) {
      if (!s3.readGeometry()) continue;
      const t3 = s3.getDisplayId();
      q(c2, this._storage.getXMin(t3), this._storage.getYMin(t3), this._storage.getXMax(t3), this._storage.getYMax(t3)), e2(c2);
    }
  }
  sweepFeatures(t2, e2, s3) {
    this._spatialIndexInvalid = true, this._objectIdToDisplayId.forEach((a4, r3) => {
      t2.has(a4) || (e2.releaseDisplayId(a4), s3 && s3.unsetAttributeData(a4), this._objectIdToDisplayId.delete(r3));
    }), this.events.emit("changed");
  }
  sweepFeatureSets(t2) {
    this._spatialIndexInvalid = true, this._featureSetsByInstance.forEach((e2, s3) => {
      t2.has(s3) || this._featureSetsByInstance.delete(s3);
    });
  }
  lookupObjectId(t2, e2) {
    const a4 = this.lookupFeatureByDisplayId(t2, e2);
    return t(a4) ? null : a4.getObjectId();
  }
  lookupDisplayId(t2) {
    return this._objectIdToDisplayId.get(t2);
  }
  lookupFeatureByDisplayId(t2, e2) {
    const s3 = e2.getInstanceId(t2);
    return this._lookupFeature(s3);
  }
  lookupByDisplayIdUnsafe(t2) {
    const e2 = this._storage.getInstanceId(t2), s3 = u(e2), a4 = l(e2), r3 = this._getFeatureSet(s3);
    return r3 ? (r3.setIndex(a4), r3) : null;
  }
  _insertFeature(t2, e2) {
    const s3 = this._storage, a4 = t2.getObjectId(), r3 = I(t2.instance, t2.getIndex());
    s3.getInstanceId(t2.getDisplayId());
    let n2 = this._objectIdToDisplayId.get(a4);
    n2 || (n2 = s3.createDisplayId(), this._objectIdToDisplayId.set(a4, n2), this._spatialIndexInvalid = true), t2.setDisplayId(n2), s3.setInstanceId(n2, r3), this.setComputedAttributes(s3, t2, n2, e2);
  }
  _searchIndex(t2) {
    this._rebuildIndex();
    const e2 = { minX: t2[0], minY: t2[1], maxX: t2[2], maxY: t2[3] };
    return this._index.search(e2);
  }
  _rebuildIndex() {
    if (!this._spatialIndexInvalid) return;
    const t2 = [];
    "snapshot" === this.mode ? this._featureSetsByInstance.forEach((e2) => {
      const s3 = e2.getCursor();
      for (; s3.next(); ) {
        const e3 = s3.getDisplayId();
        this._storage.setBounds(e3, s3) && t2.push(e3);
      }
    }) : this._objectIdToDisplayId.forEach((e2) => {
      const s3 = this._storage.getInstanceId(e2);
      this._storage.setBounds(e2, this._lookupFeature(s3)) && t2.push(e2);
    }), this._index.clear(), this._index.load(t2), this._indexSearchCache.clear(), this._spatialIndexInvalid = false;
  }
  _lookupFeature(t2) {
    const e2 = u(t2), s3 = this._getFeatureSet(e2);
    if (!s3) return;
    const a4 = s3.getCursor(), r3 = l(t2);
    return a4.setIndex(r3), a4;
  }
  _getFeatureSet(t2) {
    return this._featureSetsByInstance.get(t2);
  }
};

export {
  c,
  r2 as r,
  p,
  g
};
//# sourceMappingURL=chunk-G6YO5IYF.js.map
