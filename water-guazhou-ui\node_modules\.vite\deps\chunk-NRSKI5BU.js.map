{"version": 3, "sources": ["../../@arcgis/core/core/loadAll.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{map as o,result as r}from\"./asyncUtils.js\";import t from\"./Collection.js\";import a from\"./Loadable.js\";import{isNone as n}from\"./maybe.js\";async function l(o,r){return await o.load(),i(o,r)}async function i(l,i){const c=[],f=(...o)=>{for(const r of o)n(r)||(Array.isArray(r)?f(...r):t.isCollection(r)?r.forEach((o=>f(o))):a.isLoadable(r)&&c.push(r))};i(f);let e=null;if(await o(c,(async o=>{const t=await r(s(o)?o.loadAll():o.load());!1!==t.ok||e||(e=t)})),e)throw e.error;return l}function s(o){return\"loadAll\"in o&&\"function\"==typeof o.loadAll}export{l as loadAll,i as loadAllChildren};\n"], "mappings": ";;;;;;;;;;;;;;;AAIkJ,eAAe,EAAE,GAAE,GAAE;AAAC,SAAO,MAAM,EAAE,KAAK,GAAE,EAAE,GAAE,CAAC;AAAC;AAAC,eAAe,EAAEA,IAAEC,IAAE;AAAC,QAAM,IAAE,CAAC,GAAE,IAAE,IAAI,MAAI;AAAC,eAAU,KAAK,EAAE,GAAE,CAAC,MAAI,MAAM,QAAQ,CAAC,IAAE,EAAE,GAAG,CAAC,IAAE,EAAE,aAAa,CAAC,IAAE,EAAE,QAAS,CAAAC,OAAG,EAAEA,EAAC,CAAE,IAAE,EAAE,WAAW,CAAC,KAAG,EAAE,KAAK,CAAC;AAAA,EAAE;AAAE,EAAAD,GAAE,CAAC;AAAE,MAAI,IAAE;AAAK,MAAG,MAAM,EAAE,GAAG,OAAM,MAAG;AAAC,UAAME,KAAE,MAAM,EAAE,EAAE,CAAC,IAAE,EAAE,QAAQ,IAAE,EAAE,KAAK,CAAC;AAAE,cAAKA,GAAE,MAAI,MAAI,IAAEA;AAAA,EAAE,CAAE,GAAE,EAAE,OAAM,EAAE;AAAM,SAAOH;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,aAAY,KAAG,cAAY,OAAO,EAAE;AAAO;", "names": ["l", "i", "o", "t"]}