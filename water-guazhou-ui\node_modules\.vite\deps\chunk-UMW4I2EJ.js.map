{"version": 3, "sources": ["../../@arcgis/core/rest/query/executeQueryJSON.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{parseUrl as r}from\"../utils.js\";import{executeQuery as t}from\"./operations/query.js\";import o from\"../support/FeatureSet.js\";import e from\"../support/Query.js\";async function s(r,t,e){const s=await a(r,t,e);return o.fromJSON(s)}async function a(o,s,a){const n=r(o),i={...a},p=e.from(s),{data:u}=await t(n,p,p.sourceSpatialReference,i);return u}export{s as executeQueryJSON,a as executeRawQueryJSON};\n"], "mappings": ";;;;;;;;;;;;;;AAIuK,eAAe,EAAE,GAAE,GAAE,GAAE;AAAC,QAAMA,KAAE,MAAM,EAAE,GAAE,GAAE,CAAC;AAAE,SAAOC,GAAE,SAASD,EAAC;AAAC;AAAC,eAAe,EAAE,GAAEA,IAAEE,IAAE;AAAC,QAAM,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,GAAGA,GAAC,GAAE,IAAE,EAAE,KAAKF,EAAC,GAAE,EAAC,MAAK,EAAC,IAAE,MAAM,EAAE,GAAE,GAAE,EAAE,wBAAuB,CAAC;AAAE,SAAO;AAAC;", "names": ["s", "x", "a"]}