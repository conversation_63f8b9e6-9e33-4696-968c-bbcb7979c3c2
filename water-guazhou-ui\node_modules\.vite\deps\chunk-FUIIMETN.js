import {
  f as f2,
  g
} from "./chunk-U4SDSCWW.js";
import {
  C,
  K,
  Mt,
  gt,
  j,
  mt,
  q
} from "./chunk-B4KDIR4O.js";
import {
  t as t2
} from "./chunk-SEO6KEGF.js";
import {
  v as v2
} from "./chunk-Z2LHI3D7.js";
import {
  An
} from "./chunk-UYAKJRPP.js";
import {
  f,
  u,
  v,
  y
} from "./chunk-ZACBBT3Y.js";
import {
  l
} from "./chunk-SRBBUKOI.js";
import {
  E,
  X,
  c,
  k,
  o
} from "./chunk-JXLVNWKF.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/graphics/data/utils.js
var O = new s({ esriSRUnit_Meter: "meters", esriSRUnit_Kilometer: "kilometers", esriSRUnit_Foot: "feet", esriSRUnit_StatuteMile: "miles", esriSRUnit_NauticalMile: "nautical-miles", esriSRUnit_USNauticalMile: "us-nautical-miles" });
var F = Object.freeze({});
var N = new t2();
var _ = new t2();
var G = new t2();
var P = { esriGeometryPoint: j, esriGeometryPolyline: C, esriGeometryPolygon: K, esriGeometryMultipoint: q };
function b(e, i, r2, n = e.hasZ, o2 = e.hasM) {
  if (t(i)) return null;
  const s2 = e.hasZ && n, a = e.hasM && o2;
  if (r2) {
    const t3 = mt(G, i, e.hasZ, e.hasM, "esriGeometryPoint", r2, n, o2);
    return j(t3, s2, a);
  }
  return j(i, s2, a);
}
function v3(e, r2, n, o2, s2, a, l2 = r2, m = n) {
  var _a, _b, _c;
  const f3 = r2 && l2, u2 = n && m, c2 = r(o2) ? "coords" in o2 ? o2 : o2.geometry : null;
  if (t(c2)) return null;
  if (s2) {
    let t3 = gt(_, c2, r2, n, e, s2, l2, m);
    return a && (t3 = mt(G, t3, f3, u2, e, a)), ((_a = P[e]) == null ? void 0 : _a.call(P, t3, f3, u2)) ?? null;
  }
  if (a) {
    const t3 = mt(G, c2, r2, n, e, a, l2, m);
    return ((_b = P[e]) == null ? void 0 : _b.call(P, t3, f3, u2)) ?? null;
  }
  return Mt(N, c2, r2, n, l2, m), ((_c = P[e]) == null ? void 0 : _c.call(P, N, f3, u2)) ?? null;
}
async function z(e, t3, i) {
  const { outFields: r2, orderByFields: n, groupByFieldsForStatistics: o2, outStatistics: s2 } = e;
  if (r2) for (let a = 0; a < r2.length; a++) r2[a] = r2[a].trim();
  if (n) for (let a = 0; a < n.length; a++) n[a] = n[a].trim();
  if (o2) for (let a = 0; a < o2.length; a++) o2[a] = o2[a].trim();
  if (s2) for (let a = 0; a < s2.length; a++) s2[a].onStatisticField && (s2[a].onStatisticField = s2[a].onStatisticField.trim());
  return e.geometry && !e.outSR && (e.outSR = e.geometry.spatialReference), J(e, t3, i);
}
async function J(e, i, r2) {
  var _a;
  if (!e) return null;
  let { where: n } = e;
  if (e.where = n = n && n.trim(), (!n || /^1 *= *1$/.test(n) || i && i === n) && (e.where = null), !e.geometry) return e;
  let a = await Z(e);
  if (e.distance = 0, e.units = null, "esriSpatialRelEnvelopeIntersects" === e.spatialRel) {
    const { spatialReference: t3 } = e.geometry;
    a = l(a), a.spatialReference = t3;
  }
  if (a) {
    await f2(a.spatialReference, r2), a = B(a, r2);
    const i2 = (await v2(v(a)))[0];
    if (t(i2)) throw F;
    const n2 = "quantizationParameters" in e && ((_a = e.quantizationParameters) == null ? void 0 : _a.tolerance) || "maxAllowableOffset" in e && e.maxAllowableOffset || 0, o2 = n2 && A(a, r2) ? { densificationStep: 8 * n2 } : void 0, l2 = i2.toJSON(), m = await g(l2, l2.spatialReference, r2, o2);
    if (!m) throw F;
    m.spatialReference = r2, e.geometry = m;
  }
  return e;
}
function A(e, t3) {
  if (!e) return false;
  const i = e.spatialReference;
  return (u(e) || y(e) || f(e)) && !E(i, t3) && !An(i, t3);
}
function B(e, t3) {
  const i = e.spatialReference;
  return A(e, t3) && u(e) ? { spatialReference: i, rings: [[[e.xmin, e.ymin], [e.xmin, e.ymax], [e.xmax, e.ymax], [e.xmax, e.ymin], [e.xmin, e.ymin]]] } : e;
}
async function Z(e) {
  const { distance: t3, units: i } = e, n = e.geometry;
  if (null == t3 || "vertexAttributes" in n) return n;
  const o2 = n.spatialReference, s2 = i ? O.fromJSON(i) : X(o2), a = o2 && (o(o2) || k(o2)) ? n : await f2(o2, c).then(() => g(n, c));
  return (await q2())(a.spatialReference, a, t3, s2);
}
async function q2() {
  return (await import("./geometryEngineJSON-2XCCV7A5.js")).geodesicBuffer;
}
function E2(e) {
  return e && k2 in e ? JSON.parse(JSON.stringify(e, C2)) : e;
}
var k2 = "_geVersion";
var C2 = (e, t3) => e !== k2 ? t3 : void 0;

export {
  F,
  b,
  v3 as v,
  z,
  J,
  E2 as E
};
//# sourceMappingURL=chunk-FUIIMETN.js.map
