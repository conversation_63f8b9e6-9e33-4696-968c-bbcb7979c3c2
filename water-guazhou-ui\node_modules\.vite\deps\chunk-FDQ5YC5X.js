import {
  I,
  L,
  T
} from "./chunk-LDZI44QV.js";
import {
  o
} from "./chunk-D7BTYVTV.js";
import {
  ct,
  ft,
  ut
} from "./chunk-B4KDIR4O.js";
import {
  e
} from "./chunk-RE7K5Z3I.js";
import {
  r as r2
} from "./chunk-UCWK623G.js";
import {
  i
} from "./chunk-DHWMTT76.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  M
} from "./chunk-R5MYQRRS.js";
import {
  c,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  l,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/ogc/ogcFeatureUtils.js
var h = s.getLogger("esri.layers.graphics.sources.ogcfeature");
var j = "http://www.opengis.net/def/crs/";
var F = `${j}OGC/1.3/CRS84`;
async function I2(n, r3, a = {}, o2 = 5) {
  const { links: s3 } = n, l2 = D(s3, "items", "application/geo+json") || D(s3, "http://www.opengis.net/def/rel/ogc/1.0/items", "application/geo+json");
  if (t(l2)) throw new s2("ogc-feature-layer:missing-items-page", "Missing items url");
  const { data: c2 } = await U(l2.href, { signal: a.signal, query: { limit: o2, ...a.customParameters, token: a.apiKey }, headers: { accept: "application/geo+json" } });
  await T(c2);
  const d = L(c2, { geometryType: r3.geometryType }), u = r3.fields || d.fields || [], f2 = null != r3.hasZ ? r3.hasZ : d.hasZ, b = d.geometryType, j2 = r3.objectIdField || d.objectIdFieldName || "OBJECTID";
  let F2 = r3.timeInfo;
  const I3 = u.find(({ name: e2 }) => e2 === j2);
  if (I3) I3.editable = false, I3.nullable = false;
  else {
    if (!d.objectIdFieldType) throw new s2("ogc-feature-layer:missing-feature-id", "Collection geojson require a feature id as a unique identifier");
    u.unshift({ name: j2, alias: j2, type: "number" === d.objectIdFieldType ? "esriFieldTypeOID" : "esriFieldTypeString", editable: false, nullable: false });
  }
  if (j2 !== d.objectIdFieldName) {
    const e2 = u.find(({ name: e3 }) => e3 === d.objectIdFieldName);
    e2 && (e2.type = "esriFieldTypeInteger");
  }
  u === d.fields && d.unknownFields.length > 0 && h.warn({ name: "ogc-feature-layer:unknown-field-types", message: "Some fields types couldn't be inferred from the features and were dropped", details: { unknownFields: d.unknownFields } });
  for (const e2 of u) {
    if (null == e2.name && (e2.name = e2.alias), null == e2.alias && (e2.alias = e2.name), "esriFieldTypeOID" !== e2.type && "esriFieldTypeGlobalID" !== e2.type && (e2.editable = null == e2.editable || !!e2.editable, e2.nullable = null == e2.nullable || !!e2.nullable), !e2.name) throw new s2("ogc-feature-layer:invalid-field-name", "field name is missing", { field: e2 });
    if (!i.jsonValues.includes(e2.type)) throw new s2("ogc-feature-layer:invalid-field-type", `invalid type for field "${e2.name}"`, { field: e2 });
  }
  if (F2) {
    const e2 = new r2(u);
    if (F2.startTimeField) {
      const t2 = e2.get(F2.startTimeField);
      t2 ? (F2.startTimeField = t2.name, t2.type = "esriFieldTypeDate") : F2.startTimeField = null;
    }
    if (F2.endTimeField) {
      const t2 = e2.get(F2.endTimeField);
      t2 ? (F2.endTimeField = t2.name, t2.type = "esriFieldTypeDate") : F2.endTimeField = null;
    }
    if (F2.trackIdField) {
      const t2 = e2.get(F2.trackIdField);
      t2 ? F2.trackIdField = t2.name : (F2.trackIdField = null, h.warn({ name: "ogc-feature-layer:invalid-timeInfo-trackIdField", message: "trackIdField is missing", details: { timeInfo: F2 } }));
    }
    F2.startTimeField || F2.endTimeField || (h.warn({ name: "ogc-feature-layer:invalid-timeInfo", message: "startTimeField and endTimeField are missing", details: { timeInfo: F2 } }), F2 = null);
  }
  return { drawingInfo: b ? o(b) : null, extent: W(n), geometryType: b, fields: u, hasZ: !!f2, objectIdField: j2, timeInfo: F2 };
}
async function T2(n, r3 = {}) {
  const { links: a } = n, o2 = D(a, "data", "application/json") || D(a, "http://www.opengis.net/def/rel/ogc/1.0/data", "application/json");
  if (t(o2)) throw new s2("ogc-feature-layer:missing-collections-page", "Missing collections url");
  const { apiKey: s3, customParameters: l2, signal: c2 } = r3, { data: d } = await U(o2.href, { signal: c2, headers: { accept: "application/json" }, query: { ...l2, token: s3 } });
  return d;
}
async function k(n, r3 = {}) {
  const { links: a } = n, o2 = D(a, "conformance", "application/json") || D(a, "http://www.opengis.net/def/rel/ogc/1.0/conformance", "application/json");
  if (t(o2)) throw new s2("ogc-feature-layer:missing-conformance-page", "Missing conformance url");
  const { apiKey: s3, customParameters: l2, signal: c2 } = r3, { data: d } = await U(o2.href, { signal: c2, headers: { accept: "application/json" }, query: { ...l2, token: s3 } });
  return d;
}
async function x(t2, n = {}) {
  const { apiKey: i2, customParameters: r3, signal: a } = n, { data: o2 } = await U(t2, { signal: a, headers: { accept: "application/json" }, query: { ...r3, token: i2 } });
  return o2;
}
async function S(t2, n = {}) {
  const r3 = "application/vnd.oai.openapi+json;version=3.0", a = D(t2.links, "service-desc", r3);
  if (t(a)) return h.warn("ogc-feature-layer:missing-openapi-page", "The OGC API-Features server does not have an OpenAPI page."), null;
  const { apiKey: o2, customParameters: s3, signal: l2 } = n, { data: c2 } = await U(a.href, { signal: l2, headers: { accept: r3 }, query: { ...s3, token: o2 } });
  return c2;
}
function v(e2) {
  var _a;
  const t2 = (_a = /^http:\/\/www\.opengis.net\/def\/crs\/(?<authority>.*)\/(?<version>.*)\/(?<code>.*)$/i.exec(e2)) == null ? void 0 : _a.groups;
  if (!t2) return null;
  const { authority: n, code: i2 } = t2;
  switch (n.toLowerCase()) {
    case "ogc":
      switch (i2.toLowerCase()) {
        case "crs27":
          return f.GCS_NAD_1927.wkid;
        case "crs83":
          return 4269;
        case "crs84":
        case "crs84h":
          return f.WGS84.wkid;
        default:
          return null;
      }
    case "esri":
    case "epsg": {
      const e3 = Number.parseInt(i2, 10);
      return Number.isNaN(e3) ? null : e3;
    }
    default:
      return null;
  }
}
async function N(e2, t2, n) {
  const i2 = await q(e2, t2, n);
  return ft(i2);
}
async function q(n, l2, m) {
  const { collection: p, layerDefinition: g, maxRecordCount: y, queryParameters: { apiKey: w, customParameters: h2 }, spatialReference: j2, supportedCrs: F2 } = n, { links: I3 } = p, T3 = D(I3, "items", "application/geo+json") || D(I3, "http://www.opengis.net/def/rel/ogc/1.0/items", "application/geo+json");
  if (t(T3)) throw new s2("ogc-feature-layer:missing-items-page", "Missing items url");
  const { geometry: k2, num: x2, start: S2, timeExtent: v2, where: N2 } = l2;
  if (l2.objectIds) throw new s2("ogc-feature-layer:query-by-objectids-not-supported", "Queries with objectids are not supported");
  const q2 = f.fromJSON(j2), O2 = l(l2.outSpatialReference, q2), C2 = O2.isWGS84 ? null : R(O2, F2), W2 = G(k2, F2), P = M2(v2), Z = $(N2), K = x2 ?? (null != S2 && void 0 !== S2 ? 10 : y), { data: L2 } = await U(T3.href, { ...m, query: { ...h2, ...W2, crs: C2, datetime: P, query: Z, limit: K, startindex: S2, token: w }, headers: { accept: "application/geo+json" } });
  let J = false;
  if (L2.links) {
    const e2 = L2.links.find((e3) => "next" === e3.rel);
    J = !!e2;
  }
  !J && Number.isInteger(L2.numberMatched) && Number.isInteger(L2.numberReturned) && (J = L2.numberReturned < L2.numberMatched);
  const { fields: z, geometryType: A, hasZ: E, objectIdField: U2 } = g, _ = I(L2, { geometryType: A, hasZ: E, objectIdField: U2 });
  if (!C2 && O2.isWebMercator) {
    for (const e2 of _) if (r(e2.geometry) && null != A) {
      const t2 = ut(e2.geometry, A, E, false);
      t2.spatialReference = f.WGS84, e2.geometry = ct(M(t2, O2));
    }
  }
  for (const e2 of _) e2.objectId = e2.attributes[U2];
  const B = C2 || !C2 && O2.isWebMercator ? O2.toJSON() : c, Q = new e();
  return Q.exceededTransferLimit = J, Q.features = _, Q.fields = z, Q.geometryType = A, Q.hasZ = E, Q.objectIdFieldName = U2, Q.spatialReference = B, Q;
}
function O(e2) {
  return r(e2) && "extent" === e2.type;
}
function R(e2, t2) {
  const { isWebMercator: n, wkid: i2 } = e2;
  if (!i2) return null;
  const r3 = n ? t2[3857] ?? t2[102100] ?? t2[102113] ?? t2[900913] : t2[e2.wkid];
  return r3 ? `${j}${r3}` : null;
}
function C(e2) {
  if (t(e2)) return "";
  const { xmin: t2, ymin: n, xmax: r3, ymax: a } = e2;
  return `${t2},${n},${r3},${a}`;
}
function M2(e2) {
  if (t(e2)) return null;
  const { start: t2, end: n } = e2;
  return `${r(t2) ? t2.toISOString() : ".."}/${r(n) ? n.toISOString() : ".."}`;
}
function $(e2) {
  return t(e2) || !e2 || "1=1" === e2 ? null : e2;
}
function G(e2, t2) {
  if (!O(e2)) return null;
  const { spatialReference: n } = e2;
  if (!n || n.isWGS84) return { bbox: C(e2) };
  const i2 = R(n, t2);
  return r(i2) ? { bbox: C(e2), "bbox-crs": i2 } : n.isWebMercator ? { bbox: C(M(e2, f.WGS84)) } : null;
}
function W(e2) {
  var _a;
  const t2 = (_a = e2.extent) == null ? void 0 : _a.spatial;
  if (!t2) return null;
  const n = t2.bbox[0], i2 = 4 === n.length, r3 = n[0], a = n[1], o2 = i2 ? void 0 : n[2];
  return { xmin: r3, ymin: a, xmax: i2 ? n[2] : n[3], ymax: i2 ? n[3] : n[4], zmin: o2, zmax: i2 ? void 0 : n[5], spatialReference: f.WGS84.toJSON() };
}
function D(e2, t2, n) {
  return e2.find((e3) => e3.rel === t2 && e3.type === n) || e2.find((e3) => e3.rel === t2 && !e3.type);
}

export {
  j,
  F,
  I2 as I,
  T2 as T,
  k,
  x,
  S,
  v,
  N,
  q
};
//# sourceMappingURL=chunk-FDQ5YC5X.js.map
