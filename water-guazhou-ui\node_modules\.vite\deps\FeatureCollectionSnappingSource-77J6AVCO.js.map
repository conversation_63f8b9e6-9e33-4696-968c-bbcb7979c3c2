{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/featureSources/FeatureCollectionSnappingSource.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import t from\"../../../../core/Accessor.js\";import{isSome as i,toNullable as r,unwrap as o}from\"../../../../core/maybe.js\";import{throwIfAborted as n,whenOrAbort as a}from\"../../../../core/promiseUtils.js\";import{watch as s,initial as p,on as l}from\"../../../../core/reactiveUtils.js\";import{property as y}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as c}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{elevationContextAffectsAlignment as g}from\"../../../../support/elevationInfoUtils.js\";import{makeFilter as u,makeSnappingQuery as h}from\"../snappingUtils.js\";import{convertSnappingCandidate as m,makeGetGroundElevation as d}from\"./queryEngineUtils.js\";import{getSnappingCandidateElevationAligner as v}from\"./snappingCandidateElevationAlignment.js\";import{getSnappingCandidateElevationFilter as S}from\"./snappingCandidateElevationFilter.js\";import{getSymbologySnappingCandidatesFetcher as f}from\"./symbologySnappingCandidates.js\";let w=class extends t{get availability(){return 1}get updating(){return this.layerSource.updating}get _snappingElevationAligner(){const{view:e}=this,{layer:t}=this.layerSource,r=i(e)&&\"3d\"===e.type;if(!r||\"subtype-group\"===t.type)return v();const o=async(i,r)=>(await a(e.whenLayerView(t),r)).elevationAlignPointsInFeatures(i,r);return v(r,{elevationInfo:t.elevationInfo,alignPointsInFeatures:o,spatialReference:e.spatialReference})}get _snappingElevationFilter(){const{view:e}=this,t=i(e)&&\"3d\"===e.type&&\"subtype-group\"!==this.layerSource.layer.type;return S(t)}get _symbologySnappingFetcher(){const{view:e}=this,{layer:t}=this.layerSource;return i(e)&&\"3d\"===e.type&&\"subtype-group\"!==t.type?f(this._symbologySnappingSupported,(async(i,r)=>{const o=await e.whenLayerView(t);return n(r),o.queryForSymbologySnapping({candidates:i,spatialReference:e.spatialReference},r)})):f()}get _symbologySnappingSupported(){return i(this._layerView3D)&&this._layerView3D.symbologySnappingSupported}initialize(){const{view:e}=this,{layer:t}=this.layerSource;i(e)&&\"3d\"===e.type&&\"subtype-group\"!==t.type&&(e.whenLayerView(t).then((e=>this._layerView3D=e)),this.addHandles([e.elevationProvider.on(\"elevation-change\",(({context:e})=>{const{elevationInfo:i}=t;g(e,i)&&this._snappingElevationAligner.notifyElevationSourceChange()})),s((()=>t.elevationInfo),(()=>this._snappingElevationAligner.notifyElevationSourceChange()),p),s((()=>i(this._layerView3D)?this._layerView3D.processor?.renderer:null),(()=>this._symbologySnappingFetcher.notifySymbologyChange()),p),l((()=>r(this._layerView3D)?.layer),[\"edits\",\"apply-edits\",\"graphic-update\"],(()=>this._symbologySnappingFetcher.notifySymbologyChange()))]))}constructor(e){super(e),this.view=null,this._layerView3D=null}refresh(){}async fetchCandidates(e,t){const{layer:i}=this.layerSource,r=i.source;if(!r?.querySnapping)return[];const a=u(i),s=h(e,o(this.view)?.type??\"2d\",a),p=await r.querySnapping(s,{signal:t});n(t);const l=await this._snappingElevationAligner.alignCandidates(p.candidates,t);n(t);const y=await this._symbologySnappingFetcher.fetch(l,t);n(t);const c=0===y.length?l:[...l,...y],g=this._snappingElevationFilter.filter(s,c),d=this._getGroundElevation;return g.map((e=>m(e,d)))}get _getGroundElevation(){return d(this.view)}};e([y({constructOnly:!0})],w.prototype,\"layerSource\",void 0),e([y({constructOnly:!0})],w.prototype,\"view\",void 0),e([y()],w.prototype,\"_snappingElevationAligner\",null),e([y()],w.prototype,\"_snappingElevationFilter\",null),e([y()],w.prototype,\"_symbologySnappingFetcher\",null),e([y()],w.prototype,\"_layerView3D\",void 0),e([y()],w.prototype,\"_symbologySnappingSupported\",null),e([y()],w.prototype,\"_getGroundElevation\",null),w=e([c(\"esri.views.interactive.snapping.featureSources.FeatureCollectionSnappingSource\")],w);export{w as FeatureCollectionSnappingSource};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4mC,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAY;AAAA,EAAQ;AAAA,EAAC,IAAI,4BAA2B;AAAC,UAAK,EAAC,MAAKA,GAAC,IAAE,MAAK,EAAC,OAAM,EAAC,IAAE,KAAK,aAAYC,KAAE,EAAED,EAAC,KAAG,SAAOA,GAAE;AAAK,QAAG,CAACC,MAAG,oBAAkB,EAAE,KAAK,QAAOA,GAAE;AAAE,UAAM,IAAE,OAAMC,IAAED,QAAK,MAAME,GAAEH,GAAE,cAAc,CAAC,GAAEC,EAAC,GAAG,+BAA+BC,IAAED,EAAC;AAAE,WAAOA,GAAEA,IAAE,EAAC,eAAc,EAAE,eAAc,uBAAsB,GAAE,kBAAiBD,GAAE,iBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,2BAA0B;AAAC,UAAK,EAAC,MAAKA,GAAC,IAAE,MAAK,IAAE,EAAEA,EAAC,KAAG,SAAOA,GAAE,QAAM,oBAAkB,KAAK,YAAY,MAAM;AAAK,WAAOC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,4BAA2B;AAAC,UAAK,EAAC,MAAKD,GAAC,IAAE,MAAK,EAAC,OAAM,EAAC,IAAE,KAAK;AAAY,WAAO,EAAEA,EAAC,KAAG,SAAOA,GAAE,QAAM,oBAAkB,EAAE,OAAK,EAAE,KAAK,6BAA6B,OAAME,IAAED,OAAI;AAAC,YAAM,IAAE,MAAMD,GAAE,cAAc,CAAC;AAAE,aAAOI,GAAEH,EAAC,GAAE,EAAE,0BAA0B,EAAC,YAAWC,IAAE,kBAAiBF,GAAE,iBAAgB,GAAEC,EAAC;AAAA,IAAC,CAAE,IAAE,EAAE;AAAA,EAAC;AAAA,EAAC,IAAI,8BAA6B;AAAC,WAAO,EAAE,KAAK,YAAY,KAAG,KAAK,aAAa;AAAA,EAA0B;AAAA,EAAC,aAAY;AAAC,UAAK,EAAC,MAAKD,GAAC,IAAE,MAAK,EAAC,OAAM,EAAC,IAAE,KAAK;AAAY,MAAEA,EAAC,KAAG,SAAOA,GAAE,QAAM,oBAAkB,EAAE,SAAOA,GAAE,cAAc,CAAC,EAAE,KAAM,CAAAA,OAAG,KAAK,eAAaA,EAAE,GAAE,KAAK,WAAW,CAACA,GAAE,kBAAkB,GAAG,oBAAoB,CAAC,EAAC,SAAQA,GAAC,MAAI;AAAC,YAAK,EAAC,eAAcE,GAAC,IAAE;AAAE,MAAAC,GAAEH,IAAEE,EAAC,KAAG,KAAK,0BAA0B,4BAA4B;AAAA,IAAC,CAAE,GAAE,EAAG,MAAI,EAAE,eAAgB,MAAI,KAAK,0BAA0B,4BAA4B,GAAG,CAAC,GAAE,EAAG,MAAE;AAJn/E;AAIq/E,eAAE,KAAK,YAAY,KAAE,UAAK,aAAa,cAAlB,mBAA6B,WAAS;AAAA,OAAO,MAAI,KAAK,0BAA0B,sBAAsB,GAAG,CAAC,GAAEG,GAAG,MAAE;AAJ3nF;AAI6nF,qBAAE,KAAK,YAAY,MAAnB,mBAAsB;AAAA,OAAO,CAAC,SAAQ,eAAc,gBAAgB,GAAG,MAAI,KAAK,0BAA0B,sBAAsB,CAAE,CAAC,CAAC;AAAA,EAAE;AAAA,EAAC,YAAYL,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,MAAK,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBA,IAAE,GAAE;AAJv2F;AAIw2F,UAAK,EAAC,OAAME,GAAC,IAAE,KAAK,aAAYD,KAAEC,GAAE;AAAO,QAAG,EAACD,MAAA,gBAAAA,GAAG,eAAc,QAAM,CAAC;AAAE,UAAMI,KAAED,GAAEF,EAAC,GAAE,IAAEI,GAAEN,MAAE,KAAAA,GAAE,KAAK,IAAI,MAAX,mBAAc,SAAM,MAAKK,EAAC,GAAEE,KAAE,MAAMN,GAAE,cAAc,GAAE,EAAC,QAAO,EAAC,CAAC;AAAE,IAAAG,GAAE,CAAC;AAAE,UAAME,KAAE,MAAM,KAAK,0BAA0B,gBAAgBC,GAAE,YAAW,CAAC;AAAE,IAAAH,GAAE,CAAC;AAAE,UAAMD,KAAE,MAAM,KAAK,0BAA0B,MAAMG,IAAE,CAAC;AAAE,IAAAF,GAAE,CAAC;AAAE,UAAM,IAAE,MAAID,GAAE,SAAOG,KAAE,CAAC,GAAGA,IAAE,GAAGH,EAAC,GAAE,IAAE,KAAK,yBAAyB,OAAO,GAAE,CAAC,GAAE,IAAE,KAAK;AAAoB,WAAO,EAAE,IAAK,CAAAH,OAAG,EAAEA,IAAE,CAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAO,EAAE,KAAK,IAAI;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,6BAA4B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,4BAA2B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,6BAA4B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,+BAA8B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,uBAAsB,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,gFAAgF,CAAC,GAAE,CAAC;", "names": ["e", "r", "i", "y", "f", "a", "l", "p"]}