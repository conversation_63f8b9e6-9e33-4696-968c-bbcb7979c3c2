/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.obtain;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.Utils.AttributeConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.Utils.TimeDiff;
import org.thingsboard.server.common.data.asset.Asset;
import org.thingsboard.server.common.data.constantsAttribute.*;
import org.thingsboard.server.common.data.energy.Energy;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.AssetId;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.inputKv.InputKv;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.telemetryAttribute.RequestTs;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;
import org.thingsboard.server.common.data.virtual.Virtual;
import org.thingsboard.server.dao.asset.AssetDao;
import org.thingsboard.server.dao.attributes.AttributesDao;
import org.thingsboard.server.dao.energy.EnergyDao;
import org.thingsboard.server.dao.influx.InfluxService;
import org.thingsboard.server.dao.inputKv.InputKvDao;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.timeseries.TimeseriesDao;
import org.thingsboard.server.dao.virtual.VirtualDao;
import org.thingsboard.server.service.utils.JsonNodeUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.DataConstants.RESPONSE_ENERGY_PRICE;
import static org.thingsboard.server.common.data.virtual.VirtualUtils.handleVirtualFormula;
import static org.thingsboard.server.common.data.virtual.VirtualUtils.isMathSymbol;
import static org.thingsboard.server.service.rest.RestUtil.doResetPost;

@Service
@Slf4j
public class ObtainServiceImpl implements BaseObtainDataService {
    @Value("${tsdb.HITSDB_IP}")
    private String TSDB_IP;

    @Value("${tsdb.HITSDB_PORT}")
    private String TSDB_PORT;

    @Autowired
    private VirtualDao virtualDao;
    @Autowired
    private InputKvDao inputKvDao;
    @Autowired
    private AssetDao assetDao;
    @Autowired
    private AttributesDao attributesDao;
    @Autowired
    private EnergyDao energyDao;
    @Autowired
    private TimeseriesDao timeseriesDao;

    @Autowired
    private InfluxService influxService;


    /**
     * 获取asset一段时间内的数据
     *
     * @param assetIds
     * @param start
     * @param end
     * @param type
     * @param name
     * @param readmeterTime
     * @return
     */
    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getAssetData(List<String> assetIds, long start, long end, String type, String name, String readmeterTime, TenantId tenantId) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = new LinkedHashMap<>();
            List<String> formula = getAssetSFormula(tenantId, assetIds, name);
            if (formula.isEmpty()) {
                return result;
            }
            start = DateUtils.getMeterReadingTime(new Date(start), readmeterTime, DateUtils.DAY).getTime();
            end = DateUtils.getMeterReadingTime(new Date(end), readmeterTime, DateUtils.DAY).getTime();
            List<ResponseTs> responseTs = getDeviceDataFromOpenTSDB(start, end, formula, tenantId, null);
            if (responseTs == null || responseTs.isEmpty()) {
                return result;
            }
            result = convertResponseTs(tenantId, responseTs, assetIds, type, name, readmeterTime, null, start, end);
            return result;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    /**
     * 获取asset一段时间内的数据和费用
     *
     * @param assetIds
     * @param start
     * @param end
     * @param type
     * @param name
     * @param readmeterTime
     * @return
     */
    @Override
    public LinkedHashMap<String, HashMap<String, BigDecimal>> getAssetDataAndCost(String assetIds, long start, long end, String type, String name, String readmeterTime, TenantId tenantId) throws ThingsboardException {
        try {
            LinkedHashMap<String, HashMap<String, BigDecimal>> data = new LinkedHashMap<>();
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = getAssetData(Collections.singletonList(assetIds), start, end, DateUtils.FIFTEEN_MINUTE, name, readmeterTime, tenantId);
            if (result.isEmpty()) {
                return data;
            }
            data = convertDataCost(result.get(assetIds), assetIds, type, name, readmeterTime, tenantId);
            return data;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    /**
     * 根据分时获取asset数据
     *
     * @param assetId
     * @param start
     * @param end
     * @param type
     * @param name
     * @param
     * @return
     */
    @Override
    public LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> getTimeSharing(String assetId, long start, long end, String type, String name, String readmeterTime, String timeSharingName, TenantId tenantId) throws ThingsboardException {
        LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> mapLinkedHashMap = new LinkedHashMap<>();
        try {
            List<String> assetIds = new ArrayList<>();
            assetIds.add(assetId);
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = getAssetData(assetIds, start, end, DateUtils.FIFTEEN_MINUTE, name, readmeterTime, tenantId);
            if (result == null || result.isEmpty()) {
                return mapLinkedHashMap;
            }
            mapLinkedHashMap = convertDataByTimeSharing(tenantId, result.get(assetId), assetId, type, timeSharingName, name, readmeterTime);
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
        return mapLinkedHashMap;
    }

    /**
     * 获取设备数据
     *
     * @param attributes
     * @param start
     * @param end
     * @param type
     * @param readmeterTime
     * @param tenantId
     * @return
     * @throws ThingsboardException
     */
    @Override
    @Cacheable(value = "getDeviceData", key = "{#attributes,#start,#end,#type,#readmeterTime,#tenantId}")
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getDeviceData(List<String> attributes, long start, long end, String type, String readmeterTime, TenantId tenantId) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> linkedHashMapLinkedHashMap = new LinkedHashMap<>();
            String timeLimit = null;
            if (type.equals(DateUtils.DAY) || type.equals(DateUtils.MONTH) || type.equals(DateUtils.YEAR) || type.equals(DateUtils.HOUR)) {
                timeLimit = null;
            } else {
                timeLimit = type;
            }
            long startTime = System.currentTimeMillis();
            List<FluxTable> fluxTables = influxService.findDeviceDataFromInflux(attributes, start, end);
            if (fluxTables == null || fluxTables.isEmpty()) {
                return linkedHashMapLinkedHashMap;
            }
            log.info("查询耗时: [{}]", System.currentTimeMillis() - startTime);

            linkedHashMapLinkedHashMap.putAll(convertInfluxByFormula(tenantId, fluxTables, null, readmeterTime, type, start, end));
            return linkedHashMapLinkedHashMap;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    @Override
    public LinkedHashMap<String, LinkedHashMap<String, String>> getDeviceLastAllData(List<String> attributes, TenantId tenantId) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, String>> linkedHashMapLinkedHashMap = new LinkedHashMap<>();
            List<ResponseTs> responseTs = getDeviceDataFromOpenTSDB(System.currentTimeMillis() - 1000 * 60 * 15, System.currentTimeMillis(), attributes, tenantId, DataConstants.RESPONSE_ENERGY_OPEN_TS_DB_NONE);
            if (responseTs == null || responseTs.isEmpty()) {
                return linkedHashMapLinkedHashMap;
            }
            responseTs.forEach(responseTs1 -> {
                linkedHashMapLinkedHashMap.put(responseTs1.getTags().get(ModelConstants.PROR), responseTs1.getDps());
//                responseTs1.getMetric().split("\\.")[1] +
            });
            return linkedHashMapLinkedHashMap;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    /**
     * 根据分时类型对数据进行分组
     *
     * @param map
     * @param type
     * @return
     */
    private LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertTimeSharingByType(LinkedHashMap<String, BigDecimal> map, String type, String meterReadTime) {
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> linkedHashMap = new LinkedHashMap<>();
        //2018-11-08 获取分时数据，第一条数据为0，避免出现时间线前移去掉第一条数据
        map.remove(map.keySet().toArray()[0]);
        map.entrySet().forEach(entry -> {
            String dayLay = null;
            switch (type) {
                case DateUtils.DAY: {
                    dayLay = TimeDiff.fifiteen2day(entry.getKey(), DateUtils.DAY, meterReadTime);
                    //dayLay = DateUtils.date2Str(DateUtils.str2Date(entry.getKey(), DateUtils.DATE_FORMATE_MINUTE), DateUtils.DATE_FORMATE_DAY);
                    break;
                }
                case DateUtils.MONTH: {
                    dayLay = TimeDiff.fifiteen2day(entry.getKey(), DateUtils.MONTH, meterReadTime);
                    //dayLay = DateUtils.date2Str(DateUtils.str2Date(entry.getKey(), DateUtils.DATE_FORMATE_MINUTE), DateUtils.DATE_FORMATE_MONTH);
                    break;
                }
                case DateUtils.YEAR: {
                    dayLay = TimeDiff.fifiteen2day(entry.getKey(), DateUtils.YEAR, meterReadTime);
                    //dayLay = DateUtils.date2Str(DateUtils.str2Date(entry.getKey(), DateUtils.DATE_FORMATE_MINUTE), DateUtils.DATE_FORMATE_YEAR);
                    break;
                }
            }
            if (!linkedHashMap.keySet().contains(dayLay)) {
                linkedHashMap.put(dayLay, new LinkedHashMap<>());
            }
            linkedHashMap.get(dayLay).put(entry.getKey(), entry.getValue());
        });
        return linkedHashMap;
    }

    private LinkedHashMap<String, HashMap<String, BigDecimal>> convertDataCost(LinkedHashMap<String, BigDecimal> map, String assetId, String type, String attributeName, String meterReadTime, TenantId tenantId) {
        //获取租户的分时设置
        Asset asset = assetDao.findById(tenantId, UUIDConverter.fromString(assetId));
        try {
            List<Cost> costList = getCostByTenant(tenantId).stream().filter(cost -> cost.getType().equals(attributeName)).collect(Collectors.toList());
            return convertDataByPrice(attributeName, convertTimeSharingByType(map, type, meterReadTime), costList, tenantId);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 获取分时数据已经费用
     *
     * @param map
     * @param assetId
     * @param type
     * @param timeSharingName
     * @param attributeName
     * @return
     */
    private LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> convertDataByTimeSharing(TenantId tenantId, LinkedHashMap<String, BigDecimal> map, String assetId, String type, String timeSharingName, String attributeName, String meterTReadTime) {
        LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> mapLinkedHashMap = new LinkedHashMap<>();

        Asset asset = assetDao.findById(tenantId, UUIDConverter.fromString(assetId));
        try {
            //获取租户的分时设置
            Optional<AttributeKvEntry> attributeKvEntry = attributesDao.find(tenantId, tenantId, DataConstants.SERVER_SCOPE, ModelConstants.TIME_SHARING).get();
            if (attributeKvEntry.isPresent()) {
                Optional<TimeSharing> timeSharing = handleTimeSharing(attributeKvEntry.get().getValueAsString(), timeSharingName);
                //如果获取到的分时设置为空，则返回空数组
                if (!timeSharing.isPresent()) {
                    return mapLinkedHashMap;
                }

                //获取用户的能源费用设置
                mapLinkedHashMap = convertDataByTimeSharing(convertTimeSharingByType(map, type, meterTReadTime), timeSharing.get(), (ArrayList<Cost>) (getCostByTenant(asset.getTenantId())), meterTReadTime, attributeName);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return mapLinkedHashMap;
    }


    /**
     * 根据tenantId获取能源价格
     *
     * @param tenantId
     * @return
     */
    private List<Cost> getCostByTenant(TenantId tenantId) {
        List<Cost> costList = new ArrayList<>();
        try {
            List<Energy> energyList = energyDao.findByTenantId(tenantId).get();
            energyList.forEach(energy -> {
                if (energy.getAdditionalInfo() != null) {
                    try {
                        JsonNode jsonNode = new ObjectMapper().readTree(energy.getAdditionalInfo().asText());
                        if (jsonNode.get(RESPONSE_ENERGY_PRICE) != null) {
                            Iterator<JsonNode> iterator = jsonNode.get(RESPONSE_ENERGY_PRICE).getElements();
                            iterator.forEachRemaining(json -> {
                                costList.add(new Cost(json.get(DataConstants.REQUEST_PARAM_TYPE).asText(), json.get(DataConstants.REQUEST_PARAM_START_TIME).asText(), json.get(DataConstants.REQUEST_PARAM_PRICE).asText(), json.get(DataConstants.REQUEST_PARAM_INTERVAL).asText()));
                            });
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            });
            Collections.sort(costList);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return costList;
    }

    /**
     * 根据分时计算数据
     *
     * @param linkedHashMap
     * @param timeSharing
     * @return
     */
    private LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> convertDataByTimeSharing(LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> linkedHashMap, TimeSharing timeSharing, ArrayList<Cost> costs, String readMeterTime, String attr) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> hashMap = new LinkedHashMap<>();
            switch (timeSharing.getType()) {
                case ModelConstants.TIME_SHARING_TIME: {
                    linkedHashMap.entrySet().forEach(entry -> {
                        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> timeSharingMap = new LinkedHashMap<>();
                        // hashMap.put(entry.getKey(), new HashMap<>());
                        entry.getValue().entrySet().forEach(va -> {
                                    if (va.getValue() != null && va.getValue().compareTo(new BigDecimal("0")) < 0) {
                                        log.info("出现负数，时间为：" + va.getKey() + "值为：" + va.getValue());
                                    }
                                    timeSharing.getData().forEach(data -> {
                                        //新增一个分时-<时段-数据>的map
                                        if (timeSharingMap.get(data.getKey()) == null) {
                                            timeSharingMap.put(data.getKey(), new LinkedHashMap<>());
                                        }
                                        data.getValue().forEach(period -> {
                                            try {
                                                //判断是否处于当前时间段
                                                if (TimeDiff.isOnTimeSharing(va.getKey(), Collections.singletonList(period))) {
                                                    //如果该时间段已经有数据，则进行数据求和
                                                    if (timeSharingMap.get(data.getKey()).get(period) != null && va.getValue() != null) {
                                                        timeSharingMap.get(data.getKey()).put(period, va.getValue().add(timeSharingMap.get(data.getKey()).get(period)));
                                                    } else if (va.getValue() != null) {
                                                        //没有数据则直接进行新增操作
                                                        timeSharingMap.get(data.getKey()).put(period, va.getValue());
                                                    } else if (timeSharingMap.get(data.getKey()).get(period) == null) {
                                                        timeSharingMap.get(data.getKey()).put(period, new BigDecimal(0));
                                                    }
                                                    if (timeSharingMap.get(data.getKey()).get(DataConstants.ATTRIBUTE_COST) == null) {
                                                        timeSharingMap.get(data.getKey()).put(DataConstants.ATTRIBUTE_COST, calculationCost(va.getKey(), va.getValue(), costs, attr));
                                                    } else {
                                                        timeSharingMap.get(data.getKey()).put(DataConstants.ATTRIBUTE_COST, calculationCost(va.getKey(), va.getValue(), costs, attr).add(timeSharingMap.get(data.getKey()).get(DataConstants.ATTRIBUTE_COST)));
                                                    }
                                                } else if (timeSharingMap.get(data.getKey()).get(period) == null) {
                                                    timeSharingMap.get(data.getKey()).put(period, null);
                                                    if (timeSharingMap.get(data.getKey()).get(DataConstants.ATTRIBUTE_COST) == null) {
                                                        timeSharingMap.get(data.getKey()).put(DataConstants.ATTRIBUTE_COST, null);
                                                    }
                                                }

                                            } catch (Exception e) {
                                                e.printStackTrace();
                                                log.error("计算分时失败 e=[{}]" + e.getMessage());
                                            }
                                        });

                                    });

                                }
                        );
                        hashMap.put(entry.getKey(), timeSharingMap);
                    });
                    break;
                }
                case ModelConstants.TIME_SHARING_WEEK: {
                    linkedHashMap.entrySet().forEach(entry -> {
                        // hashMap.put(entry.getKey(), new HashMap<>());
                        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> timeSharingMap = new LinkedHashMap<>();
                        entry.getValue().entrySet().forEach(va -> {
                                    timeSharing.getData().forEach(data -> {
                                        //新增一个分时-<时段-数据>的map
                                        if (timeSharingMap.get(data.getKey()) == null) {
                                            timeSharingMap.put(data.getKey(), new LinkedHashMap<>());
                                        }
                                        data.getValue().forEach(period -> {
                                            try {
                                                //判断是否处于当前时间段
                                                if (DateUtils.getWeekday(va.getKey(), readMeterTime).equals(period)) {
                                                    //如果该时间段已经有数据，则进行数据求和
                                                    if (timeSharingMap.get(data.getKey()).get(period) != null) {
                                                        timeSharingMap.get(data.getKey()).put(period, va.getValue().add(timeSharingMap.get(data.getKey()).get(period)));
                                                    } else if (va.getValue() != null) {
                                                        //没有数据则直接进行新增操作
                                                        timeSharingMap.get(data.getKey()).put(period, va.getValue());
                                                    } else {
                                                        timeSharingMap.get(data.getKey()).put(period, new BigDecimal(0));
                                                    }
                                                    if (timeSharingMap.get(data.getKey()).get(DataConstants.ATTRIBUTE_COST) == null) {
                                                        timeSharingMap.get(data.getKey()).put(DataConstants.ATTRIBUTE_COST, calculationCost(va.getKey(), va.getValue(), costs, attr));
                                                    } else {
                                                    }
                                                    timeSharingMap.get(data.getKey()).put(DataConstants.ATTRIBUTE_COST, calculationCost(va.getKey(), va.getValue(), costs, attr).add(timeSharingMap.get(data.getKey()).get(DataConstants.ATTRIBUTE_COST)));
                                                } else if (timeSharingMap.get(data.getKey()).get(period) == null) {
                                                    timeSharingMap.get(data.getKey()).put(period, new BigDecimal("0"));
                                                    if (timeSharingMap.get(data.getKey()).get(DataConstants.ATTRIBUTE_COST) == null) {
                                                        timeSharingMap.get(data.getKey()).put(DataConstants.ATTRIBUTE_COST, new BigDecimal("0"));
                                                    }
                                                }

                                            } catch (Exception e) {
                                                log.error("计算分时失败 e=[{}]" + e.getMessage());
                                            }
                                        });

                                    });

                                }
                        );
                        hashMap.put(entry.getKey(), timeSharingMap);
                    });
                    break;
                }
            }
            return hashMap;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    /**
     * 根据价格计算成本
     *
     * @param linkedHashMap
     * @return
     */
    private LinkedHashMap<String, HashMap<String, BigDecimal>> convertDataByPrice(String name, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> linkedHashMap, List<Cost> costs, TenantId tenantId) throws ThingsboardException {
        try {
            Map<String, Coal> energyData = getEnergyByTenantId(tenantId);
            LinkedHashMap<String, HashMap<String, BigDecimal>> hashMap = new LinkedHashMap<>();
            linkedHashMap.entrySet().forEach(entry -> {
                hashMap.put(entry.getKey(), new HashMap<>());
                entry.getValue().entrySet().forEach(va -> {
                            try {
                                if (hashMap.get(entry.getKey()).get(DataConstants.VALUE) != null && va.getValue() != null) {
                                    hashMap.get(entry.getKey()).put(DataConstants.VALUE, va.getValue().add(hashMap.get(entry.getKey()).get(DataConstants.VALUE)));
                                    hashMap.get(entry.getKey()).put(DataConstants.REQUEST_PARAM_CARBON_RATIO, va.getValue().multiply(energyData.get(name).getCo2()).add(hashMap.get(entry.getKey()).get(DataConstants.REQUEST_PARAM_CARBON_RATIO)));
                                    hashMap.get(entry.getKey()).put(DataConstants.REQUEST_PARAM_COAL_RATIO, va.getValue().multiply(energyData.get(name).getCoalRatio()).add(hashMap.get(entry.getKey()).get(DataConstants.REQUEST_PARAM_COAL_RATIO)));
                                    hashMap.get(entry.getKey()).put(DataConstants.ATTRIBUTE_COST, calculationCost(va.getKey(), va.getValue(), costs, name).add(hashMap.get(entry.getKey()).get(DataConstants.ATTRIBUTE_COST)));
                                } else if (va.getValue() != null) {
                                    hashMap.get(entry.getKey()).put(DataConstants.VALUE, va.getValue());
                                    hashMap.get(entry.getKey()).put(DataConstants.REQUEST_PARAM_CARBON_RATIO, va.getValue().multiply(energyData.get(name).getCo2()));
                                    hashMap.get(entry.getKey()).put(DataConstants.REQUEST_PARAM_COAL_RATIO, va.getValue().multiply(energyData.get(name).getCoalRatio()));
                                    hashMap.get(entry.getKey()).put(DataConstants.ATTRIBUTE_COST, calculationCost(va.getKey(), va.getValue(), costs, name));
                                }
                            } catch (ThingsboardException e) {
                                log.error("获取成本数据失败，e=[{}]" + e.getMessage());
                            }
                        }
                );
            });
            return hashMap;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    /**
     * 计算费用
     * 更新费用计算规则时间在前，
     *
     * @param time
     * @param value
     * @param costs
     * @return
     */
    private BigDecimal calculationCost(String time, BigDecimal value, List<Cost> costs, String type) throws ThingsboardException {
        BigDecimal result = new BigDecimal("0.0");
        if (costs == null || costs.isEmpty() || value == null) {
            return result;
        }
        try {
            for (Cost cost : costs) {
                if (cost.getType().equals(type) && DateUtils.str2Date(time, DateUtils.DATE_FORMATE_MINUTE).getTime() > DateUtils.str2Date(cost.getStartTime(), DateUtils.DATE_FORMATE_DAY).getTime()) {
                    if (TimeDiff.isOnTimeSharing(time, Collections.singletonList(cost.getInterval()))) {
                        result = value.multiply(new BigDecimal(cost.getPrice()));
                    }
                }
            }
            return result;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_COST_ERROR + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


    /**
     * 获取用户的分时设置
     *
     * @param timeSharing
     * @param name
     * @return
     */
    public Optional<TimeSharing> handleTimeSharing(String timeSharing, String name) throws ThingsboardException {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Optional<TimeSharing> result = Optional.ofNullable(null);
            JsonNode jsonNode = objectMapper.readTree(timeSharing);
            List<TimeSharing> timeSharingList = new ArrayList<>();
            Iterator<JsonNode> iterator = jsonNode.getElements();
            iterator.forEachRemaining(json -> {
                if (json == null) {
                    return;
                }
                TimeSharing time = new TimeSharing();
                time.setName(json.get(DataConstants.REQUEST_PARAM_NAME).asText());
                time.setType(json.get(DataConstants.REQUEST_PARAM_TYPE).asText());
                //读取data数组
                List<LocalTimeSharing> data = new ArrayList<>();
                Iterator<JsonNode> dataNode = json.get(DataConstants.REQUEST_PARAM_DATA).getElements();
                dataNode.forEachRemaining(node -> {
                    if (node == null) {
                        return;
                    }
                    LocalTimeSharing localTimeSharing = new LocalTimeSharing();
                    localTimeSharing.setKey(node.get(DataConstants.REQUEST_PARAM_KEY).asText());
                    Iterator<JsonNode> valueNode = node.get(DataConstants.REQUEST_PARAM_VALUE).getElements();
                    List<String> value = new ArrayList<>();
                    valueNode.forEachRemaining(v -> {
                        if (v == null) {
                            return;
                        }
                        value.add(v.asText());
                    });
                    localTimeSharing.setValue(value);
                    data.add(localTimeSharing);
                });
                time.setData(data);
                timeSharingList.add(time);
            });
            for (TimeSharing T : timeSharingList) {
                if (T.getName().equals(name)) {
                    result = Optional.of(T);
                }
            }
            return result;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


    /**
     * 根据公式和从tsdb中获取的数据进行处理
     *
     * @param
     * @return
     */
    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertResponseTsByVirtuals(TenantId tenantId, List<ResponseTs> responseTs, List<String> virtuals, String type, String readmeterTime, List<Virtual> list, long start, long end) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap = new LinkedHashMap<>();
            dataMap.putAll(convertByFormula(tenantId, responseTs, null, readmeterTime, type, start, end));
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> assetData = new LinkedHashMap<>();
            virtuals.stream().forEach(key -> {
                try {
                    assetData.put(key, getVirtualData(dataMap, key, list, type, start, end));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            return assetData;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }

    }

    /**
     * 根据公式和从tsdb中获取的数据进行处理
     *
     * @param
     * @return
     */
    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertResponseTsByFormula(TenantId tenantId, List<ResponseTs> responseTs, List<String> formula, String type, long start, long end, boolean useLastData) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap = new LinkedHashMap<>();
            dataMap.putAll(convertByFormula(tenantId, responseTs, type, null, DateUtils.FIFTEEN_MINUTE, start, end));
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> assetData = new LinkedHashMap<>();
            formula.forEach(key -> {
                assetData.put(key, getFormulaData(dataMap, key, useLastData));
            });
            return assetData;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


    /**
     * 能耗统计使用
     *
     * @param responseTs
     * @param formula
     * @return
     */
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertResponseTsByFormulaInEnergy(TenantId tenantId, List<ResponseTs> responseTs, List<String> formula, long start, long end) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap = new LinkedHashMap<>();
            dataMap.putAll(convertByFormula(tenantId, responseTs, null, null, DateUtils.YEAR, start, end));
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> assetData = new LinkedHashMap<>();
            formula.stream().parallel().forEach(key -> {
                assetData.put(key, getFormulaData(dataMap, key, false));
            });
            return assetData;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


    /**
     * 获取能耗，此处为获取多个asset的能耗数据
     *
     * @return
     */
    @Override
    public Object getEnergyData(List<String> assetIds, long start, long end, String type, TenantId tenantId) throws ThingsboardException {
        try {
            Map<String, BigDecimal> energyDataMap = new HashMap<>();
            List<String> formulas = new ArrayList<>();
            assetIds.stream().forEach(asset -> {
                try {
                    Optional<AttributeKvEntry> attributeKvEntry = attributesDao.find(tenantId, new AssetId(UUIDConverter.fromString(asset)), DataConstants.SERVER_SCOPE, DataConstants.REQUEST_PARAM_FORMULA_LIST).get();
                    if (attributeKvEntry.isPresent()) {
                        JsonNode jsonNode = new ObjectMapper().readTree(attributeKvEntry.get().getValueAsString());
                        Map<String, String> map = new HashMap<>();
                        Iterator<JsonNode> formula = jsonNode.getElements();
                        formula.forEachRemaining(node -> {
                            map.put(node.get(DataConstants.REQUEST_PARAM_KEY).asText(), node.get(DataConstants.REQUEST_PARAM_VALUE).asText());
                        });
                        if (type.equals(DataConstants.REQUEST_PARAM_ENERGY_ALL)) {
                            map.entrySet().forEach(entry -> {
                                formulas.add(entry.getValue());
                            });
                        } else {
                            formulas.add(map.get(type));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (formulas == null) {
                return energyDataMap;
            }
            List<ResponseTs> responseTsList = getDeviceDataFromOpenTSDB(start, end, formulas, tenantId, null);
            if (responseTsList == null || responseTsList.size() == 0) {
                return energyDataMap;
            }
            //获取系统设置中的能源设置
            try {
                //获取能耗数据
                Map<String, Coal> energyData = getEnergyByTenantId(tenantId);
                //对每个asset进行数据处理
                assetIds.stream().forEach(asset -> {
                    try {
                        energyDataMap.put(asset, new BigDecimal("0"));
                        Optional<AttributeKvEntry> attributeKvEntry = attributesDao.find(tenantId, new AssetId(UUIDConverter.fromString(asset)), DataConstants.SERVER_SCOPE, DataConstants.REQUEST_PARAM_FORMULA_LIST).get();
                        if (attributeKvEntry.isPresent()) {
                            JsonNode jsonNode = new ObjectMapper().readTree(attributeKvEntry.get().getValueAsString());
                            Map<String, String> map = new HashMap<>();
                            Iterator<JsonNode> formula = jsonNode.getElements();
                            formula.forEachRemaining(node -> {
                                map.put(node.get(DataConstants.REQUEST_PARAM_KEY).asText(), node.get(DataConstants.REQUEST_PARAM_VALUE).asText());
                            });
                            //新建asset-data数组
                            if (type.equals(DataConstants.REQUEST_PARAM_ENERGY_ALL)) {
                                //创建一个formula-data的数组
                                Map<String, BigDecimal> data = new HashMap<>();
                                map.entrySet().forEach(entry -> {
                                    //此处因传入的formula只有一个，所以直接以energy为key即可
                                    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> linkedHashMapLinkedHashMap = null;
                                    try {
                                        linkedHashMapLinkedHashMap = convertResponseTsByFormulaInEnergy(tenantId, responseTsList, Collections.singletonList(entry.getValue()), start, end);
                                    } catch (ThingsboardException e) {
                                        log.error("处理能耗数据失败 e=[{}]" + e.getMessage());
                                    }
                                    linkedHashMapLinkedHashMap.entrySet().forEach(m -> {
                                        data.put(entry.getKey(), new BigDecimal("0"));
                                        m.getValue().entrySet().forEach(str -> {
                                            data.put(entry.getKey(), data.get(entry.getKey()).add(str.getValue()));
                                        });
                                    });
                                });
                                //根据折煤系数进行计算
                                data.entrySet().forEach(d -> {
                                    //获取能源的折煤系数
                                    energyData.entrySet().forEach(entry -> {
                                        if (entry.getKey().equals(d.getKey())) {
                                            energyDataMap.put(asset, energyDataMap.get(asset).add(entry.getValue().getCoalRatio().multiply(d.getValue())));
                                        }
                                    });
                                });
                            }
                            //如果是单项能源计算，则无需根据折煤系数进行计算
                            else {
                                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> linkedHashMapLinkedHashMap = convertResponseTsByFormula(tenantId, responseTsList, Collections.singletonList(map.get(type)), DateUtils.YEAR, start, end, false);
                                linkedHashMapLinkedHashMap.entrySet().forEach(m -> {
                                    m.getValue().entrySet().forEach(str -> {
                                        energyDataMap.put(asset, energyDataMap.get(asset).add(str.getValue()));
                                    });

                                });
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }

            return energyDataMap;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    /**
     * 能耗看板定制模块，返回当日，当月，当年能耗，以及当月成本
     *
     * @return
     */
    @Override
    public Optional<Object> getBaseEnergyData(String assetId, String type, TenantId tenantId) {
        Map<String, BigDecimal> result = new HashMap<>();
        try {
            //获取本天数据
            Map<String, BigDecimal> dayData = (Map<String, BigDecimal>) getEnergyData(Collections.singletonList(assetId), TimeDiff.getTodayFirstTime(System.currentTimeMillis()), new Date().getTime(), type, tenantId);
            //获取本年数据
            Map<String, BigDecimal> yearData = (Map<String, BigDecimal>) getEnergyData(Collections.singletonList(assetId), TimeDiff.getYearFirstTime(), new Date().getTime(), type, tenantId);
            //获取本月数据
            Map<String, BigDecimal> monthData = (Map<String, BigDecimal>) getEnergyData(Collections.singletonList(assetId), TimeDiff.getMonthFirstTime(), new Date().getTime(), type, tenantId);
            //计算但当月成本

            List<BigDecimal> costMonth = new ArrayList<>();
            Map<String, BigDecimal> energy = new HashMap<>();
            if (type.equals(DataConstants.REQUEST_PARAM_ENERGY_ALL)) {
                Optional<AttributeKvEntry> attributeKvEntry = attributesDao.find(tenantId, new AssetId(UUIDConverter.fromString(assetId)), DataConstants.SERVER_SCOPE, DataConstants.REQUEST_PARAM_FORMULA_LIST).get();
                if (attributeKvEntry.isPresent()) {
                    JsonNode jsonNode = new ObjectMapper().readTree(attributeKvEntry.get().getValueAsString());
                    Map<String, String> map = new HashMap<>();
                    Iterator<JsonNode> formula = jsonNode.getElements();
                    formula.forEachRemaining(node -> {
                        map.put(node.get(DataConstants.REQUEST_PARAM_KEY).asText(), node.get(DataConstants.REQUEST_PARAM_VALUE).asText());
                    });
                    //轮询能源查询数据
                    map.entrySet().forEach(entry -> {
                        try {
                            energy.putAll((Map<? extends String, ? extends BigDecimal>) getEnergyData(Collections.singletonList(assetId), TimeDiff.getMonthFirstTime(), new Date().getTime(), entry.getKey(), tenantId));
                        } catch (ThingsboardException e) {
                            log.error("获取能耗数据失败 e=[{}]" + e.getMessage());
                        }
                    });
                }
            } else {
                energy.putAll((Map<? extends String, ? extends BigDecimal>) getEnergyData(Collections.singletonList(assetId), TimeDiff.getMonthFirstTime(), new Date().getTime(), type, tenantId));
            }
            Optional<AttributeKvEntry> costAttr = attributesDao.find(tenantId, tenantId, DataConstants.SERVER_SCOPE, ModelConstants.ENERGY_PRICE).get();
            if (costAttr.isPresent()) {
                JsonNode jsonNode = new ObjectMapper().readTree(costAttr.get().getValueAsString());
                List<Cost> costList = new ArrayList<>();
                Iterator<JsonNode> iterator = jsonNode.getElements();
                iterator.forEachRemaining(json -> {
                    costList.add(new Cost(json.get(DataConstants.REQUEST_PARAM_TYPE).asText(), json.get(DataConstants.REQUEST_PARAM_START_TIME).asText(), json.get(DataConstants.REQUEST_PARAM_PRICE).asText(), json.get(DataConstants.REQUEST_PARAM_INTERVAL).asText()));
                });
                energy.entrySet().forEach(entry -> {
                    costList.forEach(cost -> {
                        if (cost.getName().equals(entry.getKey())) {
                            costMonth.add(entry.getValue().multiply(new BigDecimal(cost.getPrice())));
                        }
                    });
                });
            }
            //构造返回数组
            result.put(DataConstants.RESPONSE_ENERGY_DAY_DATA, dayData.get(assetId));
            result.put(DataConstants.RESPONSE_ENERGY_MONTH_DATA, monthData.get(assetId));
            result.put(DataConstants.RESPONSE_ENERGY_YEAR_DATA, yearData.get(assetId));
            BigDecimal cost = new BigDecimal("0");
            for (BigDecimal d : costMonth) {
                cost = cost.add(d);
            }
            result.put(DataConstants.RESPONSE_ENERGY_MONTH_COST_DATA, cost);
        } catch (Exception e) {
            e.printStackTrace();
            return Optional.ofNullable(result);
        }
        return Optional.ofNullable(result);
    }

    /**
     * 获取分时数据的总能耗和成本
     *
     * @param assetId
     * @param start
     * @param end
     * @param type
     * @param readmeterTime
     * @param timeSharingName
     * @param tenantId
     * @return
     * @throws ThingsboardException
     */
    @Override
    public LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> getAllEnergyByTimeSharing(String assetId, long start, long end, String type, String readmeterTime, String timeSharingName, TenantId tenantId) throws ThingsboardException {
        LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> result = new LinkedHashMap<>();
        try {
            List<ObjectMap> formulaList = getFormulaList(tenantId, assetId);
            if (formulaList.isEmpty()) {
                return result;
            }
            //循环获取每一个公式的能耗和成本
            Map<String, LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>>> linkedHashMaps = new LinkedHashMap<>();
            List<LinkedHashMap<String, LinkedHashMap>> collects = formulaList.parallelStream().map(f -> {
                LinkedHashMap<String, LinkedHashMap> map = new LinkedHashMap();
                try {
                    map.put(f.getKey(), getTimeSharing(assetId, start, end, type, f.getKey(), readmeterTime, timeSharingName, tenantId));
                } catch (ThingsboardException e) {
                    e.printStackTrace();
                }
                return map;
            }).collect(Collectors.toList());
            collects.forEach(obj -> {
                obj.entrySet().forEach(entry -> {
                    linkedHashMaps.put(entry.getKey(), entry.getValue());
                });
            });
//            formulaList.stream().parallel().forEach(f -> {
//                try {
//                    linkedHashMaps.put(f.getKey(), getTimeSharing(assetId, start, end, type, f.getKey(), readmeterTime, timeSharingName, tenantId));
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            });
            //获取能耗数据
            Map<String, Coal> energyData = getEnergyByTenantId(tenantId);
            linkedHashMaps.entrySet().forEach(entry -> {
                //energyType -  value map
                entry.getValue().entrySet().forEach(time -> {
                    //时间-数据map
                    if (result.get(time.getKey()) == null) {
                        result.put(time.getKey(), new LinkedHashMap<>());
                    }
                    time.getValue().entrySet().forEach(value -> {
                        if (result.get(time.getKey()).get(value.getKey()) == null) {
                            result.get(time.getKey()).put(value.getKey(), new LinkedHashMap<>());
                        }
                        value.getValue().entrySet().forEach(fo -> {
                            BigDecimal energyValue = fo.getValue();
                            //不是成本值，则计算能耗
                            if (!fo.getKey().equals(DataConstants.ATTRIBUTE_COST)) {
                                boolean change = false;
                                for (Map.Entry<String, Coal> o : energyData.entrySet()) {
                                    if (o.getKey().equals(entry.getKey()) && o.getValue() != null && energyValue != null) {
                                        energyValue = energyValue.multiply(o.getValue().getCoalRatio());
                                        change = true;
                                    }
                                }
                                if (!change) {
                                    energyValue = new BigDecimal("0");
                                }
                            }

                            if (result.get(time.getKey()).get(value.getKey()).get(fo.getKey()) == null) {
                                result.get(time.getKey()).get(value.getKey()).put(fo.getKey(), energyValue);
                            } else {
                                result.get(time.getKey()).get(value.getKey()).put(fo.getKey(), energyValue.add(result.get(time.getKey()).get(value.getKey()).get(fo.getKey())));
                            }
                        });
                    });
                });
            });
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    /**
     * 根据assetId获取公式列表
     *
     * @param assetId
     * @return
     */
    public List<ObjectMap> getFormulaList(TenantId tenantId, String assetId) {
        List<ObjectMap> formulaList = new ArrayList<>();
        try {
            Optional<AttributeKvEntry> attributeKvEntry = attributesDao.find(tenantId, new AssetId(UUIDConverter.fromString(assetId)), DataConstants.SERVER_SCOPE, DataConstants.REQUEST_PARAM_FORMULA_LIST).get();
            if (attributeKvEntry.isPresent()) {
                JsonNode jsonNode = new ObjectMapper().readTree(attributeKvEntry.get().getValueAsString());

                Iterator<JsonNode> formula = jsonNode.getElements();
                formula.forEachRemaining(node -> {
                    formulaList.add(new ObjectMap(node.get(DataConstants.REQUEST_PARAM_KEY).asText(), node.get(DataConstants.REQUEST_PARAM_VALUE).asText()));
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            return formulaList;
        }
        return formulaList;
    }

    //获取asset的总能耗和总成本
    @Override
    public Object getAssetAllEnergyAndCost(List<String> assetId, long start, long end, String type, String name, String readmeterTime, TenantId tenantId) throws ThingsboardException {
        LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> result = new LinkedHashMap<>();
        try {
            //LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> data = new LinkedHashMap<>();
            //获取能耗数据
            Map<String, Coal> energyData = getEnergyByTenantId(tenantId);
            //获取成本数据
            List<Cost> costs = getCostByTenant(tenantId);
            List<LinkedHashMap> collect = assetId.parallelStream().map(id -> {
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> assetData = new LinkedHashMap<>();
                LinkedHashMap map = new LinkedHashMap<>();
                List<ObjectMap> formulaList = getFormulaList(tenantId, id);
                if (formulaList.isEmpty()) {
                    return map;
                }
                //循环获取每一个公式的能耗和成本
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> linkedHashMaps = new LinkedHashMap<>();
                formulaList.stream().forEach(f -> {
                    try {
                        linkedHashMaps.put(f.getKey(), getAssetData(Collections.singletonList(id), start, end, DateUtils.FIFTEEN_MINUTE, f.getKey(), readmeterTime, tenantId).get(id));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                ArrayList<String> dates = DateUtils.findDates(DateUtils.getMeterReadingTime(new Date(start), readmeterTime, DateUtils.DAY).getTime(), DateUtils.getMeterReadingTime(new Date(end), readmeterTime, DateUtils.DAY).getTime(), type);
                dates.forEach(s -> assetData.put(s, new LinkedHashMap<>()));
                //已经获取了每个公式的数据，计算能耗和成本并求和
                linkedHashMaps.entrySet().forEach(entry -> {
                    if (entry.getValue() != null) {
                        entry.getValue().entrySet().forEach(time -> {
                            try {
                                String timeLay = TimeDiff.fifiteen2day(time.getKey(), type, readmeterTime);
                                if (time.getValue() != null && dates.contains(timeLay)) {
                                    if (assetData.get(timeLay) == null) {
                                        assetData.put(timeLay, new LinkedHashMap<>());
                                    }
                                    //分别计算成本和能耗
                                    if (assetData.get(timeLay).get(DataConstants.VALUE) == null) {
                                        assetData.get(timeLay).put(DataConstants.VALUE, time.getValue().multiply(energyData.get(entry.getKey()).getCoalRatio()));
                                        assetData.get(timeLay).put(DataConstants.REQUEST_PARAM_COAL_RATIO, assetData.get(timeLay).get(DataConstants.VALUE));
                                        assetData.get(timeLay).put(DataConstants.REQUEST_PARAM_CARBON_RATIO, time.getValue().multiply(energyData.get(entry.getKey()).getCo2()));
                                    } else {
                                        assetData.get(timeLay).put(DataConstants.VALUE, assetData.get(timeLay).get(DataConstants.VALUE).add(time.getValue().multiply(energyData.get(entry.getKey()).getCoalRatio())));
                                        assetData.get(timeLay).put(DataConstants.REQUEST_PARAM_COAL_RATIO, assetData.get(timeLay).get(DataConstants.VALUE));
                                        assetData.get(timeLay).put(DataConstants.REQUEST_PARAM_CARBON_RATIO, assetData.get(timeLay).get(DataConstants.REQUEST_PARAM_CARBON_RATIO).add(time.getValue().multiply(energyData.get(entry.getKey()).getCo2())));
                                    }
                                    //计算成本

                                    if (assetData.get(timeLay).get(DataConstants.ATTRIBUTE_COST) == null) {
                                        assetData.get(timeLay).put(DataConstants.ATTRIBUTE_COST, calculationCost(time.getKey(), time.getValue(), costs, entry.getKey()));
                                    } else {
                                        assetData.get(timeLay).put(DataConstants.ATTRIBUTE_COST, assetData.get(timeLay).get(DataConstants.ATTRIBUTE_COST).add(calculationCost(time.getKey(), time.getValue(), costs, entry.getKey())));
                                    }
                                }
                            } catch (ThingsboardException e) {
                                e.printStackTrace();
                            }

                        });
                    }
                });
                map.put(id, assetData);
//                result.put(id, assetData);
                return map;
            }).collect(Collectors.toList());
            collect.forEach(obj -> {
                if (obj.size() > 0) {
                    result.putAll(obj);
                }
            });
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    //dasda
    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertResponseTsByFormula(TenantId tenantId, List<String> formula, long start) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap = new LinkedHashMap<>();
            formula.forEach(f -> {
                LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
                handleVirtualFormula(f).stream().forEach(m -> {
                    String[] f1 = m.split("//.");
                    try {
                        TsKvEntry tsKvEntry = timeseriesDao.findLatest(new DeviceId(UUIDConverter.fromString(f1[0])), f1[1]).get();
                        if (tsKvEntry != null) {
                            map.put(m, new BigDecimal(tsKvEntry.getValueAsString()));
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    } catch (ExecutionException e) {
                        e.printStackTrace();
                    }
                });
                dataMap.put(String.valueOf(start), map);
            });
//            dataMap.putAll(convertByFormula(tenantId, responseTs, type, null, DateUtils.FIFTEEN_MINUTE, start, end));
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> assetData = new LinkedHashMap<>();
            formula.forEach(key -> {
                assetData.put(key, getFormulaData(dataMap, key, true));
            });
            return assetData;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


    //获取asset的总能耗
    @Override
    public Object getAssetAllEnergy(List<String> assetId, long start, long end, String type, String name, String readmeterTime, TenantId tenantId) throws ThingsboardException {
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = new LinkedHashMap<>();
        try {
            //获取能耗数据
            Map<String, Coal> energyData = getEnergyByTenantId(tenantId);
            List<LinkedHashMap> collect = assetId.parallelStream().map(id -> {
                LinkedHashMap<String, BigDecimal> assetData = new LinkedHashMap<>();
                List<ObjectMap> formulaList = getFormulaList(tenantId, id);
                if (formulaList.isEmpty()) {
                    return new LinkedHashMap();
                }
                //循环获取每一个公式的能耗和成本
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> linkedHashMaps = new LinkedHashMap<>();
                formulaList.stream().forEach(f -> {
                    try {
                        linkedHashMaps.put(f.getKey(), getAssetData(Collections.singletonList(id), start, end, type, f.getKey(), readmeterTime, tenantId).get(id));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                ArrayList<String> dates = DateUtils.findDates(DateUtils.getMeterReadingTime(new Date(start), readmeterTime, DateUtils.DAY).getTime(), DateUtils.getMeterReadingTime(new Date(end), readmeterTime, DateUtils.DAY).getTime(), type);
                dates.forEach(s -> assetData.put(s, null));
                //已经获取了每个公式的数据，计算能耗和成本并求和
                linkedHashMaps.entrySet().forEach(entry -> {
                    if (entry.getValue() != null) {
                        entry.getValue().entrySet().forEach(time -> {
                            if (time.getValue() != null && energyData.get(entry.getKey()) != null) {
                                //分别计算成本和能耗
                                if (assetData.get(time.getKey()) == null) {
                                    assetData.put(time.getKey(), time.getValue().multiply(energyData.get(entry.getKey()).getCoalRatio()));
                                } else {
                                    assetData.put(time.getKey(), assetData.get(time.getKey()).add(time.getValue().multiply(energyData.get(entry.getKey()).getCoalRatio())));
                                }
                            }
                        });
                    }
                });
                LinkedHashMap map = new LinkedHashMap<>();
                map.put(id, assetData);
//                result.put(id, assetData);
                return map;
            }).collect(Collectors.toList());
            // 遍历collect获取数据返回
            collect.forEach(obj -> {
                if (obj.size() > 0) {
                    result.putAll(obj);
                }
            });
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    /**
     * 根据tenantId获取能耗数据
     *
     * @param tenantId
     * @return
     */
    private Map<String, Coal> getEnergyByTenantId(TenantId tenantId) {
        Map<String, Coal> energyData = new HashMap<>();
        try {
            List<Energy> energyList = energyDao.findByTenantId(tenantId).get();
            if (energyList.size() > 0) {
                energyList.forEach(energy -> {
                    try {
                        if (energy.getAdditionalInfo() != null) {
                            JsonNode jsonNode = new ObjectMapper().readTree(energy.getAdditionalInfo().asText());
                            energyData.put(energy.getEnergyType(), new Coal(new BigDecimal(jsonNode.get(DataConstants.REQUEST_PARAM_CARBON_RATIO).asText()), new BigDecimal(jsonNode.get(DataConstants.REQUEST_PARAM_COAL_RATIO).asText())));
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return energyData;
    }

    /**
     * 根据公式计算出值
     *
     * @param dataMap
     * @param formula
     * @return
     */
    public LinkedHashMap<String, BigDecimal> getFormulaData
    (LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap, String formula, boolean useLastData) {
        LinkedHashMap<String, BigDecimal> result = new LinkedHashMap<>();
        List<String> noSernumber = new ArrayList<>();
        List<String> formulas = handleVirtualFormula(formula);
        formulas.forEach(fo -> {
            noSernumber.add(fo);
        });
        dataMap.entrySet().forEach(time -> {
            result.put(time.getKey(), handleDataByFormula(time.getValue(), noSernumber, useLastData));
        });
        return result;
    }


    public LinkedHashMap<String, BigDecimal> getVirtualData
            (LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap, String
                    virtualId, List<Virtual> list, String type, long start, long end) {
        LinkedHashMap<String, BigDecimal> result = new LinkedHashMap<>();
        try {
            Virtual virtual = virtualDao.findByVirtualId(virtualId).get();
            switch (virtual.getVirtualType()) {
                case INPUT_VALUE: {
                    List<InputKv> inputKvList = inputKvDao.getInputKvByEntityIdAndTime(virtual.getId().toString(), start, end);
                    result.putAll(ResponseUtil.getInputKvData(inputKvList, type));
                    break;
                }
                case CALCULATION_VALUE: {
                    List<String> noSernumber = new ArrayList<>();
                    if (virtual.getFormula() == null) {
                        return result;
                    }
                    List<String> formulas = handleVirtualFormula(virtual.getFormula());
                    formulas.forEach(fo -> {
                        //是虚拟表编号
                        if (virtualCodeList().contains(fo)) {
                            for (Virtual v : list) {
                                if (v.getSerialNumber().equals(fo)) {
                                    switch (v.getVirtualType()) {
                                        case INPUT_VALUE: {
                                            List<InputKv> inputKvList = inputKvDao.getInputKvByEntityIdAndTime(UUIDConverter.fromTimeUUID(v.getId().getId()), TimeDiff.getTodayFirstTime(start), TimeDiff.getTodayFirstTime(end));
                                            LinkedHashMap<String, BigDecimal> inputKvData = ResponseUtil.getInputKvData(inputKvList, type);
                                            dataMap.entrySet().forEach(entry -> {
                                                inputKvData.entrySet().forEach(inputKvEntry -> {
                                                    if (inputKvEntry.getKey().equals(entry.getKey())) {
                                                        entry.getValue().put(v.getSerialNumber(), inputKvEntry.getValue());
                                                    }
                                                });
                                            });
                                            break;
                                        }
                                        case CALCULATION_VALUE: {
                                            LinkedHashMap<String, BigDecimal> map = getVirtualData(dataMap, UUIDConverter.fromTimeUUID(v.getId().getId()), list, type, start, end);
                                            dataMap.entrySet().forEach(ca -> {
                                                map.entrySet().forEach(m -> {
                                                    if (ca.getKey().equals(m.getKey())) {
                                                        ca.getValue().put(fo, m.getValue());
                                                    }
                                                });
                                            });
                                            result.putAll(getVirtualData(dataMap, UUIDConverter.fromTimeUUID(v.getId().getId()), list, type, start, end));
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        noSernumber.add(fo);
                    });
                    dataMap.keySet().forEach(time -> {
                        result.put(time, handleDataByFormula(dataMap.get(time), noSernumber, false));
                    });
                    break;
                }
            }


        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
        return result;
    }


//    /**
//     * 获取输入类型的值（从数据库查询）
//     *
//     * @param inputKvs
//     * @param type
//     * @return
//     */
//    public LinkedHashMap<String, BigDecimal> getInputKvData(List<InputKv> inputKvs, String type) {
//        LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
//        inputKvs.forEach(inputKv -> {
//            switch (type) {
//                case DAY: {
//                    map.put(DateUtils.date2Str(new Date(inputKv.getTs()), DateUtils.DATE_FORMATE_DAY), new BigDecimal(inputKv.getValue()));
//                    break;
//                }
//                case MONTH: {
//                    String month = DateUtils.date2Str(new Date(inputKv.getTs()), DateUtils.DATE_FORMATE_MONTH);
//                    if (map.get(month) != null)
//                        map.put(month, map.get(month).add(new BigDecimal(inputKv.getValue())));
//                    else
//                        map.put(month, new BigDecimal(inputKv.getValue()));
//                    break;
//                }
//                case YEAR: {
//                    String year = DateUtils.date2Str(new Date(inputKv.getTs()), DateUtils.DATE_FORMATE_YEAR);
//                    if (map.get(year) != null)
//                        map.put(year, map.get(year).add(new BigDecimal(inputKv.getValue())));
//                    else
//                        map.put(year, new BigDecimal(inputKv.getValue()));
//                    break;
//                }
//            }
//        });
//        return map;
//
//    }


    /**
     * 根据公式和从tsdb中获取的数据进行处理
     *
     * @param
     * @return
     */
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertResponseTs
    (TenantId tenantId, List<ResponseTs> responseTs, List<String> assetIds, String type, String name, String
            readmeterTime, String propType, long start, long end) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap = new LinkedHashMap<>();
            dataMap.putAll(convertByFormula(tenantId, responseTs, propType, readmeterTime, type, start, end));
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> assetData = new LinkedHashMap<>();
            List<LinkedHashMap<String, LinkedHashMap>> collect = assetIds.parallelStream().map(key -> {
                List<String> fo = handleVirtualFormula(getFormulaByAssetAndName(tenantId, key, name));
                if (fo.isEmpty() || fo.equals(""))
                    return new LinkedHashMap<String, LinkedHashMap>();
                LinkedHashMap<String, BigDecimal> result = new LinkedHashMap<>();
                dataMap.keySet().forEach(time -> {
                    result.put(time, handleDataByFormula(dataMap.get(time), fo, false));
                });
                LinkedHashMap<String, LinkedHashMap> linkedHashMap = new LinkedHashMap();
                linkedHashMap.put(key, result);
                return linkedHashMap;
            }).collect(Collectors.toList());
            collect.forEach(obj -> {
                obj.entrySet().forEach(entry -> {
                    assetData.put(entry.getKey(), entry.getValue());
                });
            });
            return assetData;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }

    }


    /**
     * 根据公式解析数据
     *
     *
     * @param readmeterTime
     * @param type
     * @return
     */
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertInfluxByFormula
    (TenantId tenantId, List<FluxTable> fluxTables, String propType, String readmeterTime, String type, long start, long end) throws
            ThingsboardException {
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = new LinkedHashMap<>();
        List<LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> collect = fluxTables.stream().map(fluxTable -> {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> responseData = new LinkedHashMap<>();
            String deviceId = fluxTable.getRecords().get(0).getMeasurement();
            String attr = String.valueOf(fluxTable.getRecords().get(0).getValueByKey(DataConstants.ATTRIBUTE_PROP));
            //查找换表时间
            try {
                PropAttribute propAttribute = getProp(tenantId, deviceId, attr);
                if (propAttribute == null) {
                    return responseData;
                }
                LinkedHashMap<Long, Long> changemeterMap = new LinkedHashMap<>();
                Optional<AttributeKvEntry> changemeterAttr = attributesDao.find(tenantId, new DeviceId(UUIDConverter.fromString(deviceId)), DataConstants.SERVER_SCOPE, DataConstants.ATTRIBUTE_CHANGE_METER).get();
                changemeterAttr.ifPresent(Attr ->
                        changemeterMap.putAll(TimeDiff.changemeter(Attr.getValueAsString())));
                //筛选出换表数据
                ArrayList<ArrayList<LoocalMap>> linkedHashMaps = ResponseUtil.handleResponseTs(fluxTable, changemeterMap);
                LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
                if (propType != null) {
                    map.putAll(groupDataByType(linkedHashMaps, propType, readmeterTime, type, start, end));
                } else if (propAttribute != null) {
                    map.putAll(groupDataByType(linkedHashMaps, propAttribute.getStatType(), readmeterTime, type, start, end));
                }
                //System.out.println("处理分组数据花费时间" + (new Date().getTime() - start1));
                map.entrySet().forEach(entry -> {
                    LinkedHashMap<String, BigDecimal> data = new LinkedHashMap<>();
                    data.put(deviceId + "." + attr, entry.getValue());
                    if (responseData.keySet().contains(entry.getKey()) && responseData.get(entry.getKey()) != null) {
                        responseData.get(entry.getKey()).putAll(data);
                    } else {
                        responseData.put(entry.getKey(), data);
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
            processData(responseData, type);
            return responseData;
        }).collect(Collectors.toList());
        collect.forEach(obj -> {
            obj.entrySet().forEach(entry -> {
                if (result.get(entry.getKey()) == null) {
                    result.put(entry.getKey(), entry.getValue());
                } else {
                    entry.getValue().entrySet().forEach(e -> {
                        result.get(entry.getKey()).put(e.getKey(), e.getValue());
                    });
                }

            });
        });
        return result;
    }

    /**
     * 根据公式解析数据
     *
     *
     * @param readmeterTime
     * @param type
     * @return
     */
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertInfluxByFormulaByOriginal
    (TenantId tenantId, List<FluxTable> fluxTables, String propType, String readmeterTime, String type, long start, long end) throws
            ThingsboardException {
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = new LinkedHashMap<>();
        List<LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> collect = fluxTables.stream().map(fluxTable -> {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> responseData = new LinkedHashMap<>();
            String deviceId = fluxTable.getRecords().get(0).getMeasurement();
            String attr = String.valueOf(fluxTable.getRecords().get(0).getValueByKey(DataConstants.ATTRIBUTE_PROP));
            //查找换表时间
            try {
                PropAttribute propAttribute = getProp(tenantId, deviceId, attr);
                if (propAttribute == null) {
                    return responseData;
                }
                LinkedHashMap<Long, Long> changemeterMap = new LinkedHashMap<>();
                Optional<AttributeKvEntry> changemeterAttr = attributesDao.find(tenantId, new DeviceId(UUIDConverter.fromString(deviceId)), DataConstants.SERVER_SCOPE, DataConstants.ATTRIBUTE_CHANGE_METER).get();
                changemeterAttr.ifPresent(Attr ->
                        changemeterMap.putAll(TimeDiff.changemeter(Attr.getValueAsString())));
                //筛选出换表数据
                ArrayList<ArrayList<LoocalMap>> linkedHashMaps = ResponseUtil.handleResponseTs(fluxTable, changemeterMap);
                LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
                if (propType != null) {
                    map.putAll(groupDataByTypeByOriginal(linkedHashMaps, propType, readmeterTime, type, start, end));
                } else if (propAttribute != null) {
                    map.putAll(groupDataByTypeByOriginal(linkedHashMaps, propAttribute.getStatType(), readmeterTime, type, start, end));
                }
                //System.out.println("处理分组数据花费时间" + (new Date().getTime() - start1));
                map.entrySet().forEach(entry -> {
                    LinkedHashMap<String, BigDecimal> data = new LinkedHashMap<>();
                    data.put(deviceId + "." + attr, entry.getValue());
                    if (responseData.keySet().contains(entry.getKey()) && responseData.get(entry.getKey()) != null) {
                        responseData.get(entry.getKey()).putAll(data);
                    } else {
                        responseData.put(entry.getKey(), data);
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
            processData(responseData, type);
            return responseData;
        }).collect(Collectors.toList());
        collect.forEach(obj -> {
            obj.entrySet().forEach(entry -> {
                if (result.get(entry.getKey()) == null) {
                    result.put(entry.getKey(), entry.getValue());
                } else {
                    entry.getValue().entrySet().forEach(e -> {
                        result.get(entry.getKey()).put(e.getKey(), e.getValue());
                    });
                }

            });
        });
        return result;
    }

    private void processData(LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> responseData, String type) {
        SimpleDateFormat dateFormat = null;
        switch (type) {
            case "1m":
            case "5m":
            case "10m":
            case "15m":
            case "30m":
                dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_MINUTE);
                break;
            case "1h":
                dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_HOUR);
                break;
            case "day":
                dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_DAY);
                break;
            case "month":
                dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_MONTH);
                break;
            case "year":
                dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_YEAR);
                break;
            default:
                return;
        }


        try {
            Date now = new Date();
            List<String> keys = new ArrayList<>();
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : responseData.entrySet()) {
                String key = entry.getKey();
                Date date = dateFormat.parse(key);
                if (now.compareTo(date) < 0) {
                    keys.add(key);
                }
            }
            keys.forEach(responseData::remove);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 根据公式解析数据
     *
     * @param responseTs
     * @param readmeterTime
     * @param type
     * @return
     */
    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> convertByFormula
    (TenantId tenantId, List<ResponseTs> responseTs, String propType, String readmeterTime, String type, long start, long end) throws
            ThingsboardException {
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = new LinkedHashMap<>();
        if (responseTs == null || responseTs.size() < 1) {
            return result;
        }
        List<LinkedHashMap<String, LinkedHashMap<String, BigDecimal>>> collect = responseTs.stream().map(response -> {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> responseData = new LinkedHashMap<>();
            String[] array = response.getMetric().split("\\.");
            String deviceId = array[1];
            String attr = response.getTags().get(DataConstants.ATTRIBUTE_PROP);
            //查找换表时间
            try {
                PropAttribute propAttribute = getProp(tenantId, deviceId, attr);
                if (propAttribute == null) {
                    return responseData;
                }
                LinkedHashMap<Long, Long> changemeterMap = new LinkedHashMap<>();
                Optional<AttributeKvEntry> changemeterAttr = attributesDao.find(tenantId, new DeviceId(UUIDConverter.fromString(deviceId)), DataConstants.SERVER_SCOPE, DataConstants.ATTRIBUTE_CHANGE_METER).get();
                changemeterAttr.ifPresent(Attr ->
                        changemeterMap.putAll(TimeDiff.changemeter(Attr.getValueAsString())));
                //筛选出换表数据
                ArrayList<ArrayList<LoocalMap>> linkedHashMaps = ResponseUtil.handleResponseTs(response, changemeterMap);
                LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
                if (propType != null) {
                    map.putAll(groupDataByType(linkedHashMaps, propType, readmeterTime, type, start, end));
                } else if (propAttribute != null) {
                    map.putAll(groupDataByType(linkedHashMaps, propAttribute.getStatType(), readmeterTime, type, start, end));
                }
                //System.out.println("处理分组数据花费时间" + (new Date().getTime() - start1));
                map.entrySet().forEach(entry -> {
                    LinkedHashMap<String, BigDecimal> data = new LinkedHashMap<>();
                    data.put(deviceId + "." + attr, entry.getValue());
                    if (responseData.keySet().contains(entry.getKey()) && responseData.get(entry.getKey()) != null) {
                        responseData.get(entry.getKey()).putAll(data);
                    } else {
                        responseData.put(entry.getKey(), data);
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
            return responseData;
        }).collect(Collectors.toList());
        collect.forEach(obj -> {
            obj.entrySet().forEach(entry -> {
                if (result.get(entry.getKey()) == null) {
                    result.put(entry.getKey(), entry.getValue());
                } else {
                    entry.getValue().entrySet().forEach(e -> {
                        result.get(entry.getKey()).put(e.getKey(), e.getValue());
                    });
                }

            });
        });
        return result;
    }

    /**
     * 根据公式计算值
     *
     * @param data
     * @param formulaList
     * @return
     */
    public BigDecimal handleDataByFormula(LinkedHashMap<String, BigDecimal> data, List<String> formulaList, boolean useLastData) {
        if (!checkDataExist(data, formulaList))
            return null;
        String formula = "";
        BigDecimal result = null;
        try {
            for (String fou : formulaList) {
                if (isMathSymbol(fou)) {
                    formula = formula + fou;
                } else {
                    if (data.get(fou) == null) {
                        //是否适用最后一次数据
                        if (useLastData) {
                            String[] f1 = fou.split("\\.");
                            TsKvEntry tsKvEntry = timeseriesDao.findLatest(new DeviceId(UUIDConverter.fromString(f1[0])), f1[1]).get();
                            if (tsKvEntry != null && tsKvEntry.getValueAsString() != null) {
                                formula = formula + tsKvEntry.getValueAsString();
                            } else {
                                formula = formula + 0.0;
                            }
                        } else {
                            formula = formula + 0.0;
                        }
                    } else {
                        formula = formula + data.get(fou).toPlainString();
                    }
                }
            }
            result = new BigDecimal(String.valueOf(AviatorEvaluator.execute(formula)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 检查是否公式的所有参数都没有值
     *
     * @param data
     * @param formulaList
     * @return
     */
    public boolean checkDataExist(LinkedHashMap<String, BigDecimal> data, List<String> formulaList) {
        boolean exist = false;
        for (String fou : formulaList) {
            if (!isMathSymbol(fou) && data.get(fou) != null && !data.get(fou).equals("")) {
                exist = true;
            }
        }
        return exist;
    }


    /**
     * 把数据按每天进行分组并对数据进行处理
     *
     * @param data
     * @return
     */
    @Override
    public LinkedHashMap<String, BigDecimal> groupDataByType(ArrayList<ArrayList<LoocalMap>> data, String
            statType, String readmeterTime, String type, long Start, long end) {
        ArrayList<LinkedHashMap<String, ArrayList<BigDecimal>>> linkedHashMaps = new ArrayList<>();
        // 2018-10-30 修改：在分组数据之前使用类型对时间做分组处理
        // 2018-11-05 修改：去掉之前的填充逻辑，对不同时间类型的数据进行不同的处理，将每个时间段内的最后一条数据加入到该时间段内
        ArrayList<String> dates = DateUtils.findDates(Start, end, type);
        Iterator iter = data.iterator();
        while (iter.hasNext()) {
            ArrayList<LoocalMap> loocalMapArrayList = (ArrayList<LoocalMap>) iter.next();
            Iterator attr = loocalMapArrayList.iterator();
            LinkedHashMap<String, ArrayList<BigDecimal>> map = new LinkedHashMap<>();
            dates.forEach(d -> {
                map.put(d, new ArrayList<>());
            });
            while (attr.hasNext()) {
                LoocalMap entry = (LoocalMap) attr.next();
                String timeLay = null;
                String lastTimeLay = null;
                switch (type) {
                    case DateUtils.SECOND:
                    case DateUtils.MINUTE:
                    case DateUtils.FIVE_MINUTE:
                    case DateUtils.TEN_MINUTE:
                    case DateUtils.FIFTEEN_MINUTE:
                    case DateUtils.THIRTY_MINUTE:
                        timeLay = TimeDiff.getTrulyMinute(entry.getKey(), type);
                        lastTimeLay = TimeDiff.getAfterFiveMinute(timeLay, type);
                        break;
                    case "1h":
                    case DateUtils.HOUR: {
                        timeLay = DateUtils.date2Str(entry.getKey(), DateUtils.DATE_FORMATE_HOUR);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.HOUR, readmeterTime);
                        break;
                    }
                    case "1d":
                    case DateUtils.DAY: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, DateUtils.DAY);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.DAY, readmeterTime);
                        break;
                    }
                    case "1nc":
                    case DateUtils.MONTH: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, DateUtils.MONTH);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.MONTH, readmeterTime);
                        break;
                    }
                    case "1yc":
                    case DateUtils.YEAR: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, DateUtils.YEAR);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.YEAR, readmeterTime);
                        break;
                    }
                }
                if (timeLay != null && map.get(timeLay) != null) {
                    map.get(timeLay).add(entry.getData());
                }
                if (statType.equalsIgnoreCase(AttributeConstants.TELEMETRY_DATA_TYPE_ACCUMULATE) && lastTimeLay != null && map.get(lastTimeLay) != null) {
                    map.get(lastTimeLay).add(entry.getData());
                }
            }
            linkedHashMaps.add(map);
        }

        //对数据进行差值求和
        LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
        linkedHashMaps.forEach(list -> {
            list.entrySet().forEach(entry -> {
                switch (statType) {
                    case AttributeConstants.TELEMETRY_DATA_TYPE_RANDOM: {
                        if (entry.getValue().size() > 0) {
                            map.put(entry.getKey(), entry.getValue().get(entry.getValue().size() - 1));
                        } else {
                            map.put(entry.getKey(), null);
                        }
                        break;
                    }
                    case AttributeConstants.TELEMETRY_DATA_TYPE_ACCUMULATE: {
                        if (entry.getValue().size() > 0) {
                            if (map.keySet().contains(entry.getKey()) && map.get(entry.getKey()) != null) {
                                map.put(entry.getKey(), map.get(entry.getKey()).add(entry.getValue().get(entry.getValue().size() - 1).subtract(entry.getValue().get(0))));
                            } else {
                            }
                            map.put(entry.getKey(), (entry.getValue().get(entry.getValue().size() - 1).subtract(entry.getValue().get(0))));
                        } else if (map.get(entry.getKey()) == null) {
                            map.put(entry.getKey(), null);
                        }
                        break;
                    }
                }

            });
        });
        return map;
    }

    /**
     * 把数据按每天进行分组并对数据进行处理
     *
     * @param data
     * @return
     */
    public LinkedHashMap<String, BigDecimal> groupDataByTypeByOriginal(ArrayList<ArrayList<LoocalMap>> data, String
            statType, String readmeterTime, String type, long Start, long end) {
        ArrayList<LinkedHashMap<String, ArrayList<BigDecimal>>> linkedHashMaps = new ArrayList<>();
        // 2018-10-30 修改：在分组数据之前使用类型对时间做分组处理
        // 2018-11-05 修改：去掉之前的填充逻辑，对不同时间类型的数据进行不同的处理，将每个时间段内的最后一条数据加入到该时间段内
        ArrayList<String> dates = DateUtils.findDates(Start, end, type);
        Iterator iter = data.iterator();
        while (iter.hasNext()) {
            ArrayList<LoocalMap> loocalMapArrayList = (ArrayList<LoocalMap>) iter.next();
            Iterator attr = loocalMapArrayList.iterator();
            LinkedHashMap<String, ArrayList<BigDecimal>> map = new LinkedHashMap<>();
            dates.forEach(d -> {
                map.put(d, new ArrayList<>());
            });
            while (attr.hasNext()) {
                LoocalMap entry = (LoocalMap) attr.next();
                String timeLay = null;
                String lastTimeLay = null;
                switch (type) {
                    case DateUtils.SECOND:
                    case DateUtils.MINUTE:
                    case DateUtils.FIVE_MINUTE:
                    case DateUtils.TEN_MINUTE:
                    case DateUtils.FIFTEEN_MINUTE:
                    case DateUtils.THIRTY_MINUTE:
                        timeLay = TimeDiff.getTrulyMinute(entry.getKey(), type);
                        lastTimeLay = TimeDiff.getAfterFiveMinute(timeLay, type);
                        break;
                    case "1h":
                    case DateUtils.HOUR: {
                        timeLay = DateUtils.date2Str(entry.getKey(), DateUtils.DATE_FORMATE_HOUR);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.HOUR, readmeterTime);
                        break;
                    }
                    case "1d":
                    case DateUtils.DAY: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, DateUtils.DAY);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.DAY, readmeterTime);
                        break;
                    }
                    case "1nc":
                    case DateUtils.MONTH: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, DateUtils.MONTH);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.MONTH, readmeterTime);
                        break;
                    }
                    case "1yc":
                    case DateUtils.YEAR: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, DateUtils.YEAR);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.YEAR, readmeterTime);
                        break;
                    }
                }
                if (timeLay != null && map.get(timeLay) != null) {
                    map.get(timeLay).add(entry.getData());
                }
                if (statType.equalsIgnoreCase(AttributeConstants.TELEMETRY_DATA_TYPE_ACCUMULATE) && lastTimeLay != null && map.get(lastTimeLay) != null) {
                    map.get(lastTimeLay).add(entry.getData());
                }
            }
            linkedHashMaps.add(map);
        }

        LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
        linkedHashMaps.forEach(list -> {
            list.entrySet().forEach(entry -> {
                if (entry.getValue().size() > 0) {
                    map.put(entry.getKey(), entry.getValue().get(entry.getValue().size() - 1));
                } else {
                    map.put(entry.getKey(), null);
                }
            });
        });
        return map;
    }

    /**
     * 获取Asset列表下的所有非符号
     *
     * @param assetIds
     * @return
     */
    private List<String> getAssetSFormula(TenantId tenantId, List<String> assetIds, String name) {
        List<String> formula = new ArrayList<>();
        assetIds.forEach(assetId -> {
            List<String> fo = getAssetFormula(getFormulaByAssetAndName(tenantId, assetId, name));
            fo.forEach(s -> {
                if (!formula.contains(s)) {
                    formula.add(s);
                }
            });
        });
        return formula;
    }


    /**
     * 根据assetId和能源的名称来获取公式
     *
     * @param name
     * @return
     */
    public String getFormulaByAssetAndName(TenantId tenantId, String assetId, String name) {
        List<String> strings = new ArrayList<>();
        try {
            Optional<AttributeKvEntry> attributeKvEntry = attributesDao.find(tenantId, new AssetId(UUIDConverter.fromString(assetId)), DataConstants.SERVER_SCOPE, DataConstants.REQUEST_PARAM_FORMULA_LIST).get();
            if (attributeKvEntry.isPresent()) {
                Iterator<JsonNode> iterator = new ObjectMapper().readTree(attributeKvEntry.get().getValueAsString()).getElements();
                iterator.forEachRemaining(node -> {
                    if (node.get(DataConstants.REQUEST_PARAM_KEY).asText().equals(name)) {
                        strings.add(node.get(DataConstants.REQUEST_PARAM_VALUE).asText());
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return strings.isEmpty() ? null : strings.get(0);
    }


    /**
     * 获取所有的虚拟表编号
     *
     * @return
     */
    private List<String> virtualCodeList() {
        try {
            List<Virtual> virtuals = virtualDao.findAll().get();
            List<String> result = new ArrayList<>();
            virtuals.forEach(virtual -> result.add(virtual.getSerialNumber()));
            return result;
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取asset公式中非数学符号的属性
     *
     * @param formula
     * @return
     */
    private List<String> getAssetFormula(String formula) {
        if (formula == null) {
            return new ArrayList<>();
        }
        List<String> formulaList = handleVirtualFormula(formula);
        List<String> result = new ArrayList<>();
        formulaList.forEach(fo -> {
            if (!isMathSymbol(fo) && !result.contains(fo)) {
                result.add(fo);
            }
        });
        return result;
    }

    /**
     * 获取virtual中非数学符号以及虚拟表编号的属性
     *
     * @param formula
     * @return
     */
    private List<String> getVirtualFormula(String formula) {
        List<String> numbers = virtualCodeList();
        List<String> formulaList = handleVirtualFormula(formula);
        List<String> result = new ArrayList<>();
        formulaList.forEach(fo -> {
            if (!isMathSymbol(fo) && !numbers.contains(fo) && !result.contains(fo)) {
                result.add(fo);
            }
        });
        return result;
    }

    /**
     * 根据时间获取设备数据
     *
     * @param start
     * @param end
     * @param formula
     * @return
     */
    @Override
    public List<ResponseTs> getDeviceDataFromOpenTSDB(long start, long end, List<String> formula, TenantId
            tenantId, String timeLimit) throws ThingsboardException {
        ArrayList<HashMap<String, Object>> queries = new ArrayList<>();
        try {
            formula.forEach(fo -> {
                String[] array = fo.split("\\.");
                LinkedHashMap<String, Object> query = new LinkedHashMap<>();
                query.put(AttributeConstants.TSDB_AGGREGATOR, AttributeConstants.TSDB_AGGREGATOR_TYPE.none);
                query.put(AttributeConstants.TSDB_METRIC, UUIDConverter.fromTimeUUID(tenantId.getId()) + "." + array[0]);
                LinkedHashMap<String, String> tags = new LinkedHashMap<>();
                tags.put(AttributeConstants.TSDB_PROPERTY, array[1]);
                query.put("tags", tags);
                if (timeLimit == null || timeLimit.equalsIgnoreCase("1d") || timeLimit.equalsIgnoreCase("1nc") || timeLimit.equalsIgnoreCase("1yc")) {
                    query.put(AttributeConstants.TSDB_DOWNSAMPLE, "15m-last");
                } else if (!timeLimit.equalsIgnoreCase(DataConstants.RESPONSE_ENERGY_OPEN_TS_DB_NONE)) {
                    query.put(AttributeConstants.TSDB_DOWNSAMPLE, timeLimit + "-last");
                }
                queries.add(query);
            });
            if (queries.isEmpty()) {
                return null;
            }
            String putUrl = TSDB_IP + ":" + TSDB_PORT + AttributeConstants.TSDB_API_QUERY;
            List<ResponseTs> responseTs = doResetPost(putUrl, new RequestTs(start, end, queries));
            return responseTs;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_GET_DATA_FALIED + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


    /**
     * 获取当前时间段内的第一个数据
     *
     * @param start
     * @param end
     * @param formula
     * @return
     */
    @Override
    public List<ResponseTs> getFirstDataFromOpenTSDB(long start, long end, String formula, TenantId
            tenantId, String timeLimit) throws ThingsboardException {
        ArrayList<HashMap<String, Object>> queries = new ArrayList<>();
        try {
            String[] array = formula.split("\\.");
            LinkedHashMap<String, Object> query = new LinkedHashMap<>();
            query.put(AttributeConstants.TSDB_AGGREGATOR, AttributeConstants.TSDB_AGGREGATOR_TYPE.none);
            query.put(AttributeConstants.TSDB_METRIC, UUIDConverter.fromTimeUUID(tenantId.getId()) + "." + array[0]);
            LinkedHashMap<String, String> tags = new LinkedHashMap<>();
            tags.put(AttributeConstants.TSDB_PROPERTY, array[1]);
            query.put("tags", tags);
//            if (timeLimit == null) {
//                query.put(AttributeConstants.TSDB_DOWNSAMPLE, "15m-first");
//            } else {
//                query.put(AttributeConstants.TSDB_DOWNSAMPLE, timeLimit + "-first");
//            }
            query.put(AttributeConstants.TSDB_DOWNSAMPLE, "1h-first");
            queries.add(query);
            if (queries.isEmpty()) {
                return null;
            }
            String putUrl = TSDB_IP + ":" + TSDB_PORT + AttributeConstants.TSDB_API_QUERY;
            List<ResponseTs> responseTs = doResetPost(putUrl, new RequestTs(start, end, queries));
            return responseTs;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_GET_DATA_FALIED + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getDeviceOriginalData(List<String> sourceIds, long start, long end, String readmeterTime, TenantId tenantId) {
        List<FluxTable> fluxTables = influxService.findDeviceDataFromInflux(sourceIds, start, end);
        if (fluxTables == null || fluxTables.isEmpty()) {
            return null;
        }

        LinkedHashMap<Long, List<JSONObject>> dataMap = new LinkedHashMap<>();
        fluxTables.forEach(fluxTable -> {
            List<FluxRecord> records = fluxTable.getRecords();
            if (records.size() > 0) {
                for (FluxRecord record : records) {
                    long dataTime = record.getTime().toEpochMilli();
                    List<JSONObject> list = new ArrayList<>();
                    if (dataMap.containsKey(dataTime)) {
                        list = dataMap.get(dataTime);
                    }

                    JSONObject object = new JSONObject();
                    object.put("sourceId", record.getMeasurement() + "." + record.getValueByKey(DataConstants.ATTRIBUTE_PROP));
                    object.put("value", record.getValue());
                    list.add(object);

                    dataMap.put(dataTime, list);
                }
            }
        });

        // keys排序
        ArrayList<Long> dateKeys = new ArrayList<>(dataMap.keySet());
        dateKeys.sort(Comparator.reverseOrder());

        // 构建数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> result = new LinkedHashMap<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (Long dateKey : dateKeys) {
            String dateString = simpleDateFormat.format(new Date(dateKey));
            List<JSONObject> list = dataMap.get(dateKey);
            LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
            if (list != null && list.size() > 0) {
                for (JSONObject object : list) {
                    map.put(object.getString("sourceId"), object.getBigDecimal("value"));
                }
            }

            result.put(dateString, map);
        }

        return result;
    }

    @Override
    public LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getDeviceDataByOriginal(List<String> attributes, long start, long end, String type, String readmeterTime, TenantId tenantId) throws ThingsboardException {
        try {
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> linkedHashMapLinkedHashMap = new LinkedHashMap<>();
            String timeLimit = null;
            if (type.equals(DateUtils.DAY) || type.equals(DateUtils.MONTH) || type.equals(DateUtils.YEAR) || type.equals(DateUtils.HOUR)) {
                timeLimit = null;
            } else {
                timeLimit = type;
            }
            long startTime = System.currentTimeMillis();
            List<FluxTable> fluxTables = influxService.findDeviceDataFromInflux(attributes, start, end);
            if (fluxTables == null || fluxTables.isEmpty()) {
                return linkedHashMapLinkedHashMap;
            }
            log.info("查询耗时: [{}]", System.currentTimeMillis() - startTime);

            linkedHashMapLinkedHashMap.putAll(convertInfluxByFormulaByOriginal(tenantId, fluxTables, null, readmeterTime, type, start, end));
            return linkedHashMapLinkedHashMap;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_UN_KNOW + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    @Override
    public List<ResponseTs> getLastDataFromOpenTSDB(List<String> formula, TenantId tenantId) throws ThingsboardException {
        ArrayList<HashMap<String, Object>> queries = new ArrayList<>();
        try {
            formula.forEach(fo -> {
                String[] array = fo.split("\\.");
                LinkedHashMap<String, Object> query = new LinkedHashMap<>();
                query.put(AttributeConstants.TSDB_METRIC, UUIDConverter.fromTimeUUID(tenantId.getId()) + "." + array[0]);
                LinkedHashMap<String, String> tags = new LinkedHashMap<>();
                tags.put(AttributeConstants.TSDB_PROPERTY, array[1]);
                query.put("tags", tags);
                queries.add(query);
            });
            if (queries.isEmpty()) {
                return null;
            }
            String putUrl = TSDB_IP + ":" + TSDB_PORT + AttributeConstants.TSDB_API_QUERY_LAST;
            List<ResponseTs> responseTs = doResetPost(putUrl, new RequestTs(queries, "true"));
            return responseTs;
        } catch (ThingsboardException e) {
            throw e;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_GET_DATA_FALIED + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }

    @Override
    public Object convertFormula(long start, long end, List<String> formula, TenantId tenantId) throws ThingsboardException {
        Map<String, BigDecimal> map = new HashMap<>();
        formula.forEach(f -> {
            try {
                String[] mata = f.split("\\.");
                TsKvEntry tsKvEntry = timeseriesDao.findLatest(new DeviceId(UUIDConverter.fromString(mata[0])), mata[1]).get();
                if (tsKvEntry != null) {
                    map.put(f, new BigDecimal(tsKvEntry.getValueAsString()));
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (ExecutionException e) {
                e.printStackTrace();
            }
        });
        return null;
    }


    /**
     * 获取设备对应属性名的统计类型
     *
     * @param deviceId 设备ID
     * @param propName 属性名称
     */
    @Override
    public PropAttribute getProp(TenantId tenantId, String deviceId, String propName) throws ThingsboardException {
        try {
            Optional<AttributeKvEntry> prop = attributesDao.find(tenantId, new DeviceId(UUIDConverter.fromString(deviceId)), DataConstants.SHARED_SCOPE, DataConstants.ATTRIBUTE_PROP).get();
            PropAttribute propAttribute = new PropAttribute();
            prop.ifPresent(attr -> {
                Iterator<com.fasterxml.jackson.databind.JsonNode> elements = JsonNodeUtils.readJson(attr.getValueAsString());
                elements.forEachRemaining(element -> {
                    if (element.get("propertyCategory").asText().equals(propName)) {
                        propAttribute.setName(propName);
                        propAttribute.setStatType(element.get("statType").asText());
                    }
                });
            });
            return propAttribute.getStatType() == null ? null : propAttribute;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_GET_DEVICE_INFO + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


}
