{"version": 3, "sources": ["../../@arcgis/core/views/layers/support/MapServiceLayerViewHelper.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import t from\"../../../Graphic.js\";import r from\"../../../core/Accessor.js\";import s from\"../../../core/Collection.js\";import i from\"../../../core/Error.js\";import has from\"../../../core/has.js\";import{getOrCreateMapValue as o}from\"../../../core/MapUtils.js\";import{isSome as a,isNone as n}from\"../../../core/maybe.js\";import{debounce as l,eachAlways as p}from\"../../../core/promiseUtils.js\";import{on as c}from\"../../../core/reactiveUtils.js\";import{getMetersPerUnitForSR as u}from\"../../../core/unitUtils.js\";import{property as h}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as y}from\"../../../core/accessorSupport/decorators/subclass.js\";import m from\"../../../geometry/Extent.js\";import{getResolutionForScale as f}from\"../../../geometry/support/scaleUtils.js\";import{getLayerFloorFilterClause as d}from\"../../../layers/support/floorFilterUtils.js\";import{calculateTolerance as g}from\"../../../renderers/support/clickToleranceUtils.js\";import{identify as w}from\"../../../rest/identify.js\";import b from\"../../../rest/support/IdentifyParameters.js\";import{loadArcade as v}from\"../../../support/arcadeOnDemand.js\";import j from\"../../../symbols/SimpleMarkerSymbol.js\";import{getRequiredFields as x,getFetchPopupTemplate as F}from\"./popupUtils.js\";let G=null;function P(e,t){return\"tile\"===t.type||\"map-image\"===t.type}let S=class extends r{constructor(e){super(e),this._featuresResolutions=new WeakMap,this.highlightGraphics=null,this.highlightGraphicUpdated=null,this.updateHighlightedFeatures=l((async e=>{this.destroyed||this.updatingHandles.addPromise(this._updateHighlightedFeaturesGeometries(e).catch((()=>{})))}))}initialize(){const e=e=>{this.updatingHandles.addPromise(this._updateHighlightedFeaturesSymbols(e).catch((()=>{}))),this.updateHighlightedFeatures(this._highlightGeometriesResolution)};this.addHandles([c((()=>this.highlightGraphics),\"change\",(t=>e(t.added)),{onListenerAdd:t=>e(t)})])}async fetchPopupFeatures(e,t){const{layerView:{layer:r,view:{scale:s}}}=this;if(!e)throw new i(\"fetchPopupFeatures:invalid-area\",\"Nothing to fetch without area\",{layer:r});const o=_(r.sublayers,s,t);if(!o.length)return[];const a=await R(r,o);if(!((r.capabilities?.operations?.supportsIdentify??!0)&&r.version>=10.5)&&!a)throw new i(\"fetchPopupFeatures:not-supported\",\"query operation is disabled for this service\",{layer:r});return a?this._fetchPopupFeaturesUsingQueries(e,o,t):this._fetchPopupFeaturesUsingIdentify(e,o,t)}clearHighlights(){this.highlightGraphics?.removeAll()}highlight(e){const r=this.highlightGraphics;if(!r)return{remove(){}};let i=null;if(e instanceof t?i=[e]:s.isCollection(e)&&e.length>0?i=e.toArray():Array.isArray(e)&&e.length>0&&(i=e),i=i?.filter(a),!i||!i.length)return{remove:()=>{}};for(const t of i){const e=t.sourceLayer;null!=e&&\"geometryType\"in e&&\"point\"===e.geometryType&&(t.visible=!1)}return r.addMany(i),{remove:()=>{r.removeMany(i??[])}}}async _updateHighlightedFeaturesSymbols(e){const{layerView:{view:t},highlightGraphics:r,highlightGraphicUpdated:s}=this;if(r&&s)for(const i of e){const e=i.sourceLayer&&\"renderer\"in i.sourceLayer&&i.sourceLayer.renderer;i.sourceLayer&&\"geometryType\"in i.sourceLayer&&\"point\"===i.sourceLayer.geometryType&&e&&\"getSymbolAsync\"in e&&e.getSymbolAsync(i).then((async o=>{o||(o=new j);let a=null;const n=\"visualVariables\"in e?e.visualVariables?.find((e=>\"size\"===e.type)):void 0;n&&(G||(G=(await import(\"../../../renderers/visualVariables/support/visualVariableUtils.js\")).getSize),a=G(n,i,{view:t.type,scale:t.scale,shape:\"simple-marker\"===o.type?o.style:null})),a||(a=\"width\"in o&&\"height\"in o&&null!=o.width&&null!=o.height?Math.max(o.width,o.height):\"size\"in o?o.size:16),r.includes(i)&&(i.symbol=new j({style:\"square\",size:a,xoffset:\"xoffset\"in o?o.xoffset:0,yoffset:\"yoffset\"in o?o.yoffset:0}),s(i,\"symbol\"),i.visible=!0)}))}}async _updateHighlightedFeaturesGeometries(e){const{layerView:{layer:t,view:r},highlightGraphics:s,highlightGraphicUpdated:i}=this;if(this._highlightGeometriesResolution=e,!i||!s?.length||!t.capabilities.operations.supportsQuery)return;const a=this._getTargetResolution(e),n=new Map;for(const c of s)if(!this._featuresResolutions.has(c)||this._featuresResolutions.get(c)>a){const e=c.sourceLayer;o(n,e,(()=>new Map)).set(c.getObjectId(),c)}const l=Array.from(n,(([e,t])=>{const s=e.createQuery();return s.objectIds=[...t.keys()],s.outFields=[e.objectIdField],s.returnGeometry=!0,s.maxAllowableOffset=a,s.outSpatialReference=r.spatialReference,e.queryFeatures(s)})),p=await Promise.all(l);if(!this.destroyed)for(const{features:o}of p)for(const e of o){const t=e.sourceLayer,r=n.get(t).get(e.getObjectId());r&&s.includes(r)&&(r.geometry=e.geometry,i(r,\"geometry\"),this._featuresResolutions.set(r,a))}}_getTargetResolution(e){const t=e*u(this.layerView.view.spatialReference),r=t/16;return r<=10?0:e/t*r}async _fetchPopupFeaturesUsingIdentify(e,t,r){const s=await this._createIdentifyParameters(e,t,r);if(n(s))return[];const{results:i}=await w(this.layerView.layer.parsedUrl,s);return i.map((e=>e.feature))}async _createIdentifyParameters(e,t,r){const{floors:s,layer:i,timeExtent:o,view:{spatialReference:n,scale:l}}=this.layerView,p=a(r)?r.event:null;if(!t.length)return null;await Promise.all(t.map((({sublayer:e})=>e.load().catch((()=>{})))));const c=Math.min(has(\"mapservice-popup-identify-max-tolerance\"),i.allSublayers.reduce(((e,t)=>t.renderer?g({renderer:t.renderer,event:p}):e),2)),u=this.createFetchPopupFeaturesQueryGeometry(e,c),h=f(l,n),y=Math.round(u.width/h),d=new m({xmin:u.center.x-h*y,ymin:u.center.y-h*y,xmax:u.center.x+h*y,ymax:u.center.y+h*y,spatialReference:u.spatialReference});return new b({floors:s,gdbVersion:\"gdbVersion\"in i?i.gdbVersion:void 0,geometry:e,height:y,layerOption:\"popup\",mapExtent:d,returnGeometry:!0,spatialReference:n,sublayers:i.sublayers,timeExtent:o,tolerance:c,width:y})}async _fetchPopupFeaturesUsingQueries(e,t,r){const{layerView:{floors:s,timeExtent:i}}=this,o=a(r)?r.event:null,n=t.map((async({sublayer:t,popupTemplate:r})=>{if(await t.load().catch((()=>{})),t.capabilities&&!t.capabilities.operations.supportsQuery)return[];const n=t.createQuery(),l=g({renderer:t.renderer,event:o}),p=this.createFetchPopupFeaturesQueryGeometry(e,l);if(n.geometry=p,n.outFields=await x(t,r),n.timeExtent=i,s){const e=s.clone(),r=d(e,t);a(r)&&(n.where=n.where?`(${n.where}) AND (${r})`:r)}const c=this._getTargetResolution(p.width/l),u=await U(r),h=\"point\"===t.geometryType||u&&u.arcadeUtils.hasGeometryOperations(r);h||(n.maxAllowableOffset=c);let{features:y}=await t.queryFeatures(n);const m=h?0:c;y=await A(t,y);for(const e of y)this._featuresResolutions.set(e,m);return y}));return(await p(n)).reverse().reduce(((e,t)=>t.value?[...e,...t.value]:e),[]).filter((e=>null!=e))}};function _(e,t,r){const s=[],i=e=>{const o=0===e.minScale||t<=e.minScale,n=0===e.maxScale||t>=e.maxScale;if(e.visible&&o&&n)if(e.sublayers)e.sublayers.forEach(i);else if(e.popupEnabled){const t=F(e,{...r,defaultPopupTemplateEnabled:!1});a(t)&&s.unshift({sublayer:e,popupTemplate:t})}};return(e?.toArray()??[]).reverse().map(i),s}function U(e){return e.expressionInfos?.length||Array.isArray(e.content)&&e.content.some((e=>\"expression\"===e.type))?v():Promise.resolve()}async function R(e,t){if(e.capabilities?.operations?.supportsQuery)return!0;try{return await Promise.any(t.map((({sublayer:e})=>e.load().then((()=>e.capabilities.operations.supportsQuery)))))}catch{return!1}}async function A(e,t){const r=e.renderer;return r&&\"defaultSymbol\"in r&&!r.defaultSymbol&&(t=r.valueExpression?await Promise.all(t.map((e=>r.getSymbolAsync(e).then((t=>t?e:null))))).then((e=>e.filter((e=>null!=e)))):t.filter((e=>null!=r.getSymbol(e)))),t}e([h({constructOnly:!0})],S.prototype,\"createFetchPopupFeaturesQueryGeometry\",void 0),e([h({constructOnly:!0})],S.prototype,\"layerView\",void 0),e([h({constructOnly:!0})],S.prototype,\"highlightGraphics\",void 0),e([h({constructOnly:!0})],S.prototype,\"highlightGraphicUpdated\",void 0),e([h({constructOnly:!0})],S.prototype,\"updatingHandles\",void 0),S=e([y(\"esri.views.layers.support.MapService\")],S);export{S as MapServiceLayerViewHelper,_ as collectPopupProviders,P as isMapServiceLayerView};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIu4C,IAAI,IAAE;AAAK,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAM,WAASA,GAAE,QAAM,gBAAcA,GAAE;AAAI;AAAC,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,uBAAqB,oBAAI,WAAQ,KAAK,oBAAkB,MAAK,KAAK,0BAAwB,MAAK,KAAK,4BAA0B,EAAG,OAAMA,OAAG;AAAC,WAAK,aAAW,KAAK,gBAAgB,WAAW,KAAK,qCAAqCA,EAAC,EAAE,MAAO,MAAI;AAAA,MAAC,CAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,UAAMA,KAAE,CAAAA,OAAG;AAAC,WAAK,gBAAgB,WAAW,KAAK,kCAAkCA,EAAC,EAAE,MAAO,MAAI;AAAA,MAAC,CAAE,CAAC,GAAE,KAAK,0BAA0B,KAAK,8BAA8B;AAAA,IAAC;AAAE,SAAK,WAAW,CAACE,GAAG,MAAI,KAAK,mBAAmB,UAAU,CAAAD,OAAGD,GAAEC,GAAE,KAAK,GAAG,EAAC,eAAc,CAAAA,OAAGD,GAAEC,EAAC,EAAC,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBD,IAAEC,IAAE;AAJvjE;AAIwjE,UAAK,EAAC,WAAU,EAAC,OAAME,IAAE,MAAK,EAAC,OAAMC,GAAC,EAAC,EAAC,IAAE;AAAK,QAAG,CAACJ,GAAE,OAAM,IAAI,EAAE,mCAAkC,iCAAgC,EAAC,OAAMG,GAAC,CAAC;AAAE,UAAM,IAAE,EAAEA,GAAE,WAAUC,IAAEH,EAAC;AAAE,QAAG,CAAC,EAAE,OAAO,QAAM,CAAC;AAAE,UAAMC,KAAE,MAAM,EAAEC,IAAE,CAAC;AAAE,QAAG,KAAG,WAAAA,GAAE,iBAAF,mBAAgB,eAAhB,mBAA4B,qBAAkB,SAAKA,GAAE,WAAS,SAAO,CAACD,GAAE,OAAM,IAAI,EAAE,oCAAmC,gDAA+C,EAAC,OAAMC,GAAC,CAAC;AAAE,WAAOD,KAAE,KAAK,gCAAgCF,IAAE,GAAEC,EAAC,IAAE,KAAK,iCAAiCD,IAAE,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAJtjF;AAIujF,eAAK,sBAAL,mBAAwB;AAAA,EAAW;AAAA,EAAC,UAAUD,IAAE;AAAC,UAAMG,KAAE,KAAK;AAAkB,QAAG,CAACA,GAAE,QAAM,EAAC,SAAQ;AAAA,IAAC,EAAC;AAAE,QAAIE,KAAE;AAAK,QAAGL,cAAa,IAAEK,KAAE,CAACL,EAAC,IAAE,EAAE,aAAaA,EAAC,KAAGA,GAAE,SAAO,IAAEK,KAAEL,GAAE,QAAQ,IAAE,MAAM,QAAQA,EAAC,KAAGA,GAAE,SAAO,MAAIK,KAAEL,KAAGK,KAAEA,MAAA,gBAAAA,GAAG,OAAO,IAAG,CAACA,MAAG,CAACA,GAAE,OAAO,QAAM,EAAC,QAAO,MAAI;AAAA,IAAC,EAAC;AAAE,eAAUJ,MAAKI,IAAE;AAAC,YAAML,KAAEC,GAAE;AAAY,cAAMD,MAAG,kBAAiBA,MAAG,YAAUA,GAAE,iBAAeC,GAAE,UAAQ;AAAA,IAAG;AAAC,WAAOE,GAAE,QAAQE,EAAC,GAAE,EAAC,QAAO,MAAI;AAAC,MAAAF,GAAE,WAAWE,MAAG,CAAC,CAAC;AAAA,IAAC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,kCAAkCL,IAAE;AAAC,UAAK,EAAC,WAAU,EAAC,MAAKC,GAAC,GAAE,mBAAkBE,IAAE,yBAAwBC,GAAC,IAAE;AAAK,QAAGD,MAAGC,GAAE,YAAUC,MAAKL,IAAE;AAAC,YAAMA,KAAEK,GAAE,eAAa,cAAaA,GAAE,eAAaA,GAAE,YAAY;AAAS,MAAAA,GAAE,eAAa,kBAAiBA,GAAE,eAAa,YAAUA,GAAE,YAAY,gBAAcL,MAAG,oBAAmBA,MAAGA,GAAE,eAAeK,EAAC,EAAE,KAAM,OAAM,MAAG;AAJx1G;AAIy1G,cAAI,IAAE,IAAIC;AAAG,YAAIJ,KAAE;AAAK,cAAMK,KAAE,qBAAoBP,MAAE,KAAAA,GAAE,oBAAF,mBAAmB,KAAM,CAAAA,OAAG,WAASA,GAAE,QAAO;AAAO,QAAAO,OAAI,MAAI,KAAG,MAAM,OAAO,mCAAmE,GAAG,UAASL,KAAE,EAAEK,IAAEF,IAAE,EAAC,MAAKJ,GAAE,MAAK,OAAMA,GAAE,OAAM,OAAM,oBAAkB,EAAE,OAAK,EAAE,QAAM,KAAI,CAAC,IAAGC,OAAIA,KAAE,WAAU,KAAG,YAAW,KAAG,QAAM,EAAE,SAAO,QAAM,EAAE,SAAO,KAAK,IAAI,EAAE,OAAM,EAAE,MAAM,IAAE,UAAS,IAAE,EAAE,OAAK,KAAIC,GAAE,SAASE,EAAC,MAAIA,GAAE,SAAO,IAAIC,GAAE,EAAC,OAAM,UAAS,MAAKJ,IAAE,SAAQ,aAAY,IAAE,EAAE,UAAQ,GAAE,SAAQ,aAAY,IAAE,EAAE,UAAQ,EAAC,CAAC,GAAEE,GAAEC,IAAE,QAAQ,GAAEA,GAAE,UAAQ;AAAA,MAAG,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qCAAqCL,IAAE;AAAC,UAAK,EAAC,WAAU,EAAC,OAAMC,IAAE,MAAKE,GAAC,GAAE,mBAAkBC,IAAE,yBAAwBC,GAAC,IAAE;AAAK,QAAG,KAAK,iCAA+BL,IAAE,CAACK,MAAG,EAACD,MAAA,gBAAAA,GAAG,WAAQ,CAACH,GAAE,aAAa,WAAW,cAAc;AAAO,UAAMC,KAAE,KAAK,qBAAqBF,EAAC,GAAEO,KAAE,oBAAI;AAAI,eAAU,KAAKH,GAAE,KAAG,CAAC,KAAK,qBAAqB,IAAI,CAAC,KAAG,KAAK,qBAAqB,IAAI,CAAC,IAAEF,IAAE;AAAC,YAAMF,KAAE,EAAE;AAAY,MAAAG,GAAEI,IAAEP,IAAG,MAAI,oBAAI,KAAI,EAAE,IAAI,EAAE,YAAY,GAAE,CAAC;AAAA,IAAC;AAAC,UAAM,IAAE,MAAM,KAAKO,IAAG,CAAC,CAACP,IAAEC,EAAC,MAAI;AAAC,YAAMG,KAAEJ,GAAE,YAAY;AAAE,aAAOI,GAAE,YAAU,CAAC,GAAGH,GAAE,KAAK,CAAC,GAAEG,GAAE,YAAU,CAACJ,GAAE,aAAa,GAAEI,GAAE,iBAAe,MAAGA,GAAE,qBAAmBF,IAAEE,GAAE,sBAAoBD,GAAE,kBAAiBH,GAAE,cAAcI,EAAC;AAAA,IAAC,CAAE,GAAE,IAAE,MAAM,QAAQ,IAAI,CAAC;AAAE,QAAG,CAAC,KAAK,UAAU,YAAS,EAAC,UAAS,EAAC,KAAI,EAAE,YAAUJ,MAAK,GAAE;AAAC,YAAMC,KAAED,GAAE,aAAYG,KAAEI,GAAE,IAAIN,EAAC,EAAE,IAAID,GAAE,YAAY,CAAC;AAAE,MAAAG,MAAGC,GAAE,SAASD,EAAC,MAAIA,GAAE,WAASH,GAAE,UAASK,GAAEF,IAAE,UAAU,GAAE,KAAK,qBAAqB,IAAIA,IAAED,EAAC;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBF,IAAE;AAAC,UAAMC,KAAED,KAAE,EAAE,KAAK,UAAU,KAAK,gBAAgB,GAAEG,KAAEF,KAAE;AAAG,WAAOE,MAAG,KAAG,IAAEH,KAAEC,KAAEE;AAAA,EAAC;AAAA,EAAC,MAAM,iCAAiCH,IAAEC,IAAEE,IAAE;AAAC,UAAMC,KAAE,MAAM,KAAK,0BAA0BJ,IAAEC,IAAEE,EAAC;AAAE,QAAG,EAAEC,EAAC,EAAE,QAAM,CAAC;AAAE,UAAK,EAAC,SAAQC,GAAC,IAAE,MAAM,EAAE,KAAK,UAAU,MAAM,WAAUD,EAAC;AAAE,WAAOC,GAAE,IAAK,CAAAL,OAAGA,GAAE,OAAQ;AAAA,EAAC;AAAA,EAAC,MAAM,0BAA0BA,IAAEC,IAAEE,IAAE;AAAC,UAAK,EAAC,QAAOC,IAAE,OAAMC,IAAE,YAAW,GAAE,MAAK,EAAC,kBAAiBE,IAAE,OAAM,EAAC,EAAC,IAAE,KAAK,WAAU,IAAE,EAAEJ,EAAC,IAAEA,GAAE,QAAM;AAAK,QAAG,CAACF,GAAE,OAAO,QAAO;AAAK,UAAM,QAAQ,IAAIA,GAAE,IAAK,CAAC,EAAC,UAASD,GAAC,MAAIA,GAAE,KAAK,EAAE,MAAO,MAAI;AAAA,IAAC,CAAE,CAAE,CAAC;AAAE,UAAM,IAAE,KAAK,IAAI,IAAI,yCAAyC,GAAEK,GAAE,aAAa,OAAQ,CAACL,IAAEC,OAAIA,GAAE,WAASG,GAAE,EAAC,UAASH,GAAE,UAAS,OAAM,EAAC,CAAC,IAAED,IAAG,CAAC,CAAC,GAAEQ,KAAE,KAAK,sCAAsCR,IAAE,CAAC,GAAE,IAAEG,GAAE,GAAEI,EAAC,GAAED,KAAE,KAAK,MAAME,GAAE,QAAM,CAAC,GAAEC,KAAE,IAAI,EAAE,EAAC,MAAKD,GAAE,OAAO,IAAE,IAAEF,IAAE,MAAKE,GAAE,OAAO,IAAE,IAAEF,IAAE,MAAKE,GAAE,OAAO,IAAE,IAAEF,IAAE,MAAKE,GAAE,OAAO,IAAE,IAAEF,IAAE,kBAAiBE,GAAE,iBAAgB,CAAC;AAAE,WAAO,IAAI,EAAE,EAAC,QAAOJ,IAAE,YAAW,gBAAeC,KAAEA,GAAE,aAAW,QAAO,UAASL,IAAE,QAAOM,IAAE,aAAY,SAAQ,WAAUG,IAAE,gBAAe,MAAG,kBAAiBF,IAAE,WAAUF,GAAE,WAAU,YAAW,GAAE,WAAU,GAAE,OAAMC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gCAAgCN,IAAEC,IAAEE,IAAE;AAAC,UAAK,EAAC,WAAU,EAAC,QAAOC,IAAE,YAAWC,GAAC,EAAC,IAAE,MAAK,IAAE,EAAEF,EAAC,IAAEA,GAAE,QAAM,MAAKI,KAAEN,GAAE,IAAK,OAAM,EAAC,UAASA,IAAE,eAAcE,GAAC,MAAI;AAAC,UAAG,MAAMF,GAAE,KAAK,EAAE,MAAO,MAAI;AAAA,MAAC,CAAE,GAAEA,GAAE,gBAAc,CAACA,GAAE,aAAa,WAAW,cAAc,QAAM,CAAC;AAAE,YAAMM,KAAEN,GAAE,YAAY,GAAE,IAAEG,GAAE,EAAC,UAASH,GAAE,UAAS,OAAM,EAAC,CAAC,GAAE,IAAE,KAAK,sCAAsCD,IAAE,CAAC;AAAE,UAAGO,GAAE,WAAS,GAAEA,GAAE,YAAU,MAAM,EAAEN,IAAEE,EAAC,GAAEI,GAAE,aAAWF,IAAED,IAAE;AAAC,cAAMJ,KAAEI,GAAE,MAAM,GAAED,KAAE,EAAEH,IAAEC,EAAC;AAAE,UAAEE,EAAC,MAAII,GAAE,QAAMA,GAAE,QAAM,IAAIA,GAAE,KAAK,UAAUJ,EAAC,MAAIA;AAAA,MAAE;AAAC,YAAM,IAAE,KAAK,qBAAqB,EAAE,QAAM,CAAC,GAAEK,KAAE,MAAM,EAAEL,EAAC,GAAE,IAAE,YAAUF,GAAE,gBAAcO,MAAGA,GAAE,YAAY,sBAAsBL,EAAC;AAAE,YAAII,GAAE,qBAAmB;AAAG,UAAG,EAAC,UAASD,GAAC,IAAE,MAAML,GAAE,cAAcM,EAAC;AAAE,YAAM,IAAE,IAAE,IAAE;AAAE,MAAAD,KAAE,MAAM,EAAEL,IAAEK,EAAC;AAAE,iBAAUN,MAAKM,GAAE,MAAK,qBAAqB,IAAIN,IAAE,CAAC;AAAE,aAAOM;AAAA,IAAC,CAAE;AAAE,YAAO,MAAM,EAAEC,EAAC,GAAG,QAAQ,EAAE,OAAQ,CAACP,IAAEC,OAAIA,GAAE,QAAM,CAAC,GAAGD,IAAE,GAAGC,GAAE,KAAK,IAAED,IAAG,CAAC,CAAC,EAAE,OAAQ,CAAAA,OAAG,QAAMA,EAAE;AAAA,EAAC;AAAC;AAAE,SAAS,EAAEA,IAAEC,IAAEE,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAEC,KAAE,CAAAL,OAAG;AAAC,UAAM,IAAE,MAAIA,GAAE,YAAUC,MAAGD,GAAE,UAASO,KAAE,MAAIP,GAAE,YAAUC,MAAGD,GAAE;AAAS,QAAGA,GAAE,WAAS,KAAGO;AAAE,UAAGP,GAAE,UAAU,CAAAA,GAAE,UAAU,QAAQK,EAAC;AAAA,eAAUL,GAAE,cAAa;AAAC,cAAMC,KAAEG,GAAEJ,IAAE,EAAC,GAAGG,IAAE,6BAA4B,MAAE,CAAC;AAAE,UAAEF,EAAC,KAAGG,GAAE,QAAQ,EAAC,UAASJ,IAAE,eAAcC,GAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAE,WAAOD,MAAA,gBAAAA,GAAG,cAAW,CAAC,GAAG,QAAQ,EAAE,IAAIK,EAAC,GAAED;AAAC;AAAC,SAAS,EAAEJ,IAAE;AAJ7jO;AAI8jO,WAAO,KAAAA,GAAE,oBAAF,mBAAmB,WAAQ,MAAM,QAAQA,GAAE,OAAO,KAAGA,GAAE,QAAQ,KAAM,CAAAA,OAAG,iBAAeA,GAAE,IAAK,IAAE,EAAE,IAAE,QAAQ,QAAQ;AAAC;AAAC,eAAe,EAAEA,IAAEC,IAAE;AAJhtO;AAIitO,OAAG,WAAAD,GAAE,iBAAF,mBAAgB,eAAhB,mBAA4B,cAAc,QAAM;AAAG,MAAG;AAAC,WAAO,MAAM,QAAQ,IAAIC,GAAE,IAAK,CAAC,EAAC,UAASD,GAAC,MAAIA,GAAE,KAAK,EAAE,KAAM,MAAIA,GAAE,aAAa,WAAW,aAAc,CAAE,CAAC;AAAA,EAAC,QAAM;AAAC,WAAM;AAAA,EAAE;AAAC;AAAC,eAAe,EAAEA,IAAEC,IAAE;AAAC,QAAME,KAAEH,GAAE;AAAS,SAAOG,MAAG,mBAAkBA,MAAG,CAACA,GAAE,kBAAgBF,KAAEE,GAAE,kBAAgB,MAAM,QAAQ,IAAIF,GAAE,IAAK,CAAAD,OAAGG,GAAE,eAAeH,EAAC,EAAE,KAAM,CAAAC,OAAGA,KAAED,KAAE,IAAK,CAAE,CAAC,EAAE,KAAM,CAAAA,OAAGA,GAAE,OAAQ,CAAAA,OAAG,QAAMA,EAAE,CAAE,IAAEC,GAAE,OAAQ,CAAAD,OAAG,QAAMG,GAAE,UAAUH,EAAC,CAAE,IAAGC;AAAC;AAAC,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,yCAAwC,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,GAAE,CAAC;", "names": ["e", "t", "a", "r", "s", "i", "y", "n", "u", "d"]}