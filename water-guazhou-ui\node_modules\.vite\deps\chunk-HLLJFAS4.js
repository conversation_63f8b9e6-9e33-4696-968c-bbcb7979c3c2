import {
  e as e3,
  t as t2
} from "./chunk-6DAQTVXB.js";
import {
  E as E2,
  b,
  v as v3
} from "./chunk-FUIIMETN.js";
import {
  f as f2
} from "./chunk-2CLVPBYJ.js";
import {
  D,
  M,
  S,
  T,
  c,
  d,
  f,
  m,
  p,
  v as v2,
  x,
  y,
  z
} from "./chunk-M4ZUXRA3.js";
import {
  g
} from "./chunk-U4SDSCWW.js";
import {
  e as e2
} from "./chunk-2RO3UJ2R.js";
import {
  s as s2
} from "./chunk-KXA6I5TQ.js";
import {
  te
} from "./chunk-VNYCO3JG.js";
import {
  i
} from "./chunk-57XIOVP5.js";
import {
  e2 as e,
  r as r2
} from "./chunk-X7FOCGBC.js";
import {
  l,
  v
} from "./chunk-SRBBUKOI.js";
import {
  E,
  I
} from "./chunk-JXLVNWKF.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/sql/WhereClauseCache.js
var c2 = class {
  constructor(e4, c4) {
    this._cache = new e2(e4), this._invalidCache = new e2(c4);
  }
  get(t4, c4) {
    const i2 = `${c4.uid}:${t4}`, r5 = this._cache.get(i2);
    if (r5) return r5;
    if (void 0 !== this._invalidCache.get(i2)) return null;
    try {
      const r6 = f2.create(t4, c4);
      return this._cache.put(i2, r6), r6;
    } catch {
      return this._invalidCache.put(i2, null), null;
    }
  }
};

// node_modules/@arcgis/core/layers/graphics/data/attributeSupport.js
var n = new c2(50, 500);
var s3 = "feature-store:unsupported-query";
var t3 = " as ";
var r3 = /* @__PURE__ */ new Set(["esriFieldTypeOID", "esriFieldTypeSmallInteger", "esriFieldTypeInteger", "esriFieldTypeSingle", "esriFieldTypeDouble", "esriFieldTypeLong", "esriFieldTypeDate"]);
function o(i2, t4) {
  if (!t4) return true;
  const r5 = n.get(t4, i2);
  if (!r5) throw new s(s3, "invalid SQL expression", { where: t4 });
  if (!r5.isStandardized) throw new s(s3, "where clause is not standard", { where: t4 });
  return c3(i2, r5.fieldNames, "where clause contains missing fields"), true;
}
function a(i2, t4, r5) {
  if (!t4) return true;
  const o2 = n.get(t4, i2);
  if (!o2) throw new s(s3, "invalid SQL expression", { having: t4 });
  if (!o2.isAggregate) throw new s(s3, "having does not contain a valid aggregate function", { having: t4 });
  const a2 = o2.fieldNames;
  c3(i2, a2, "having contains missing fields");
  if (!o2.getExpressions().every((e4) => {
    var _a;
    const { aggregateType: n2, field: s4 } = e4, t5 = (_a = i2.get(s4)) == null ? void 0 : _a.name;
    return r5.some((e5) => {
      var _a2;
      const { onStatisticField: s5, statisticType: r6 } = e5, o3 = (_a2 = i2.get(s5)) == null ? void 0 : _a2.name;
      return o3 === t5 && r6.toLowerCase().trim() === n2;
    });
  })) throw new s(s3, "expressions in having should also exist in outStatistics", { having: t4 });
  return true;
}
function l2(e4, i2) {
  return e4 ? n.get(e4, i2) : null;
}
function c3(i2, n2, t4, r5 = true) {
  const o2 = [];
  for (const u2 of n2) if ("*" !== u2 && !i2.has(u2)) if (r5) {
    const n3 = d2(u2);
    try {
      const t5 = l2(n3, i2);
      if (!t5) throw new s(s3, "invalid SQL expression", { where: n3 });
      if (!t5.isStandardized) throw new s(s3, "expression is not standard", { clause: t5 });
      c3(i2, t5.fieldNames, "expression contains missing fields");
    } catch (a2) {
      const e4 = a2 && a2.details;
      if (e4 && (e4.clause || e4.where)) throw a2;
      e4 && e4.missingFields ? o2.push(...e4.missingFields) : o2.push(u2);
    }
  } else o2.push(u2);
  if (o2.length) throw new s(s3, t4, { missingFields: o2 });
}
function d2(e4) {
  return e4.split(t3)[0];
}
function u(e4) {
  return e4.split(t3)[1];
}
function f3(e4, i2) {
  const n2 = i2.get(e4);
  return !!n2 && !r3.has(n2.type);
}

// node_modules/@arcgis/core/layers/graphics/data/AttributesBuilder.js
var r4 = class {
  constructor(t4, a2, l3) {
    this._fieldDataCache = /* @__PURE__ */ new Map(), this._returnDistinctMap = /* @__PURE__ */ new Map(), this.returnDistinctValues = t4.returnDistinctValues ?? false, this.fieldsIndex = l3, this.featureAdapter = a2;
    const r5 = t4.outFields;
    if (r5 && !r5.includes("*")) {
      this.outFields = r5;
      let t5 = 0;
      for (const a3 of r5) {
        const r6 = d2(a3), n2 = this.fieldsIndex.get(r6), u2 = n2 ? null : l2(r6, l3), d3 = n2 ? n2.name : u(a3) || "FIELD_EXP_" + t5++;
        this._fieldDataCache.set(a3, { alias: d3, clause: u2 });
      }
    }
  }
  countDistinctValues(t4) {
    return this.returnDistinctValues ? (t4.forEach((t5) => this.getAttributes(t5)), this._returnDistinctMap.size) : t4.length;
  }
  getAttributes(t4) {
    const e4 = this._processAttributesForOutFields(t4);
    return this._processAttributesForDistinctValues(e4);
  }
  getFieldValue(t4, e4, s4) {
    var _a;
    const a2 = s4 ? s4.name : e4;
    let l3 = null;
    return this._fieldDataCache.has(a2) ? l3 = (_a = this._fieldDataCache.get(a2)) == null ? void 0 : _a.clause : s4 || (l3 = l2(e4, this.fieldsIndex), this._fieldDataCache.set(a2, { alias: a2, clause: l3 })), s4 ? this.featureAdapter.getAttribute(t4, a2) : l3 == null ? void 0 : l3.calculateValue(t4, this.featureAdapter);
  }
  getDataValue(t4, e4) {
    const i2 = e4.normalizationType, s4 = e4.normalizationTotal;
    let r5 = e4.field && this.getFieldValue(t4, e4.field, this.fieldsIndex.get(e4.field));
    if (e4.field2 && (r5 = `${c(r5)}${e4.fieldDelimiter}${c(this.getFieldValue(t4, e4.field2, this.fieldsIndex.get(e4.field2)))}`, e4.field3 && (r5 = `${r5}${e4.fieldDelimiter}${c(this.getFieldValue(t4, e4.field3, this.fieldsIndex.get(e4.field3)))}`)), i2 && Number.isFinite(r5)) {
      const a2 = "field" === i2 && e4.normalizationField ? this.getFieldValue(t4, e4.normalizationField, this.fieldsIndex.get(e4.normalizationField)) : null;
      r5 = D(r5, i2, a2, s4);
    }
    return r5;
  }
  getExpressionValue(t4, e4, i2, s4) {
    const a2 = { attributes: this.featureAdapter.getAttributes(t4), layer: { fields: this.fieldsIndex.fields } }, l3 = s4.createExecContext(a2, i2);
    return s4.executeFunction(e4, l3);
  }
  getExpressionValues(t4, e4, i2, s4) {
    const a2 = { fields: this.fieldsIndex.fields };
    return t4.map((t5) => {
      const l3 = { attributes: this.featureAdapter.getAttributes(t5), layer: a2 }, r5 = s4.createExecContext(l3, i2);
      return s4.executeFunction(e4, r5);
    });
  }
  validateItem(t4, e4) {
    var _a, _b;
    return this._fieldDataCache.has(e4) || this._fieldDataCache.set(e4, { alias: e4, clause: l2(e4, this.fieldsIndex) }), ((_b = (_a = this._fieldDataCache.get(e4)) == null ? void 0 : _a.clause) == null ? void 0 : _b.testFeature(t4, this.featureAdapter)) ?? false;
  }
  validateItems(t4, e4) {
    var _a, _b;
    return this._fieldDataCache.has(e4) || this._fieldDataCache.set(e4, { alias: e4, clause: l2(e4, this.fieldsIndex) }), ((_b = (_a = this._fieldDataCache.get(e4)) == null ? void 0 : _a.clause) == null ? void 0 : _b.testSet(t4, this.featureAdapter)) ?? false;
  }
  _processAttributesForOutFields(t4) {
    const e4 = this.outFields;
    if (!e4 || !e4.length) return this.featureAdapter.getAttributes(t4);
    const i2 = {};
    for (const s4 of e4) {
      const { alias: e5, clause: a2 } = this._fieldDataCache.get(s4);
      i2[e5] = a2 ? a2.calculateValue(t4, this.featureAdapter) : this.featureAdapter.getAttribute(t4, e5);
    }
    return i2;
  }
  _processAttributesForDistinctValues(e4) {
    if (t(e4) || !this.returnDistinctValues) return e4;
    const i2 = this.outFields, s4 = [];
    if (i2) for (const t4 of i2) {
      const { alias: i3 } = this._fieldDataCache.get(t4);
      s4.push(e4[i3]);
    }
    else for (const t4 in e4) s4.push(e4[t4]);
    const a2 = `${(i2 || ["*"]).join(",")}=${s4.join(",")}`;
    let l3 = this._returnDistinctMap.get(a2) || 0;
    return this._returnDistinctMap.set(a2, ++l3), l3 > 1 ? null : e4;
  }
};

// node_modules/@arcgis/core/layers/graphics/data/QueryEngineResult.js
var A = class {
  constructor(e4, t4, i2) {
    this.items = e4, this.query = t4, this.geometryType = i2.geometryType, this.hasM = i2.hasM, this.hasZ = i2.hasZ, this.fieldsIndex = i2.fieldsIndex, this.objectIdField = i2.objectIdField, this.spatialReference = i2.spatialReference, this.featureAdapter = i2.featureAdapter;
  }
  get size() {
    return this.items.length;
  }
  createQueryResponseForCount() {
    const e4 = new r4(this.query, this.featureAdapter, this.fieldsIndex);
    if (!this.query.outStatistics) return e4.countDistinctValues(this.items);
    const { groupByFieldsForStatistics: t4, having: i2, outStatistics: s4 } = this.query, a2 = t4 == null ? void 0 : t4.length;
    if (!!!a2) return 1;
    const r5 = /* @__PURE__ */ new Map(), n2 = /* @__PURE__ */ new Map(), o2 = /* @__PURE__ */ new Set();
    for (const l3 of s4) {
      const { statisticType: s5 } = l3, a3 = "exceedslimit" !== s5 ? l3.onStatisticField : void 0;
      if (!n2.has(a3)) {
        const i3 = [];
        for (const s6 of t4) {
          const t5 = this._getAttributeValues(e4, s6, r5);
          i3.push(t5);
        }
        n2.set(a3, this._calculateUniqueValues(i3, e4.returnDistinctValues));
      }
      const u2 = n2.get(a3);
      for (const t5 in u2) {
        const { data: s6, items: a4 } = u2[t5], r6 = s6.join(",");
        i2 && !e4.validateItems(a4, i2) || o2.add(r6);
      }
    }
    return o2.size;
  }
  async createQueryResponse() {
    let e4;
    if (this.query.outStatistics) {
      e4 = this.query.outStatistics.some((e5) => "exceedslimit" === e5.statisticType) ? this._createExceedsLimitQueryResponse(this.query) : await this._createStatisticsQueryResponse(this.query);
    } else e4 = this._createFeatureQueryResponse(this.query);
    if (this.query.returnQueryGeometry) {
      const t4 = this.query.geometry;
      I(this.query.outSR) && !E(t4.spatialReference, this.query.outSR) ? e4.queryGeometry = E2({ spatialReference: this.query.outSR, ...g(t4, t4.spatialReference, this.query.outSR) }) : e4.queryGeometry = E2({ spatialReference: this.query.outSR, ...t4 });
    }
    return e4;
  }
  createSnappingResponse(t4, i2) {
    const s4 = this.featureAdapter, a2 = N(this.hasZ, this.hasM), { point: r5, mode: n2 } = t4, o2 = "number" == typeof t4.distance ? t4.distance : t4.distance.x, l3 = "number" == typeof t4.distance ? t4.distance : t4.distance.y, u2 = { candidates: [] }, c4 = "esriGeometryPolygon" === this.geometryType, h = this._getPointCreator(n2, this.spatialReference, i2), f4 = new q(null, 0), g2 = new q(null, 0), p2 = { x: 0, y: 0, z: 0 };
    for (const y2 of this.items) {
      const i3 = s4.getGeometry(y2);
      if (t(i3)) continue;
      const { coords: n3, lengths: x2 } = i3;
      if (f4.coords = n3, g2.coords = n3, t4.types & D2.EDGE) {
        let e4 = 0;
        for (let t5 = 0; t5 < x2.length; t5++) {
          const i4 = x2[t5];
          for (let t6 = 0; t6 < i4; t6++, e4 += a2) {
            const n4 = f4;
            if (n4.coordsIndex = e4, t6 !== i4 - 1) {
              const t7 = g2;
              t7.coordsIndex = e4 + a2;
              const i5 = p2;
              E3(p2, r5, n4, t7);
              const c5 = (r5.x - i5.x) / o2, m2 = (r5.y - i5.y) / l3, f5 = c5 * c5 + m2 * m2;
              f5 <= 1 && u2.candidates.push(e3(s4.getObjectId(y2), h(i5), Math.sqrt(f5), h(n4), h(t7)));
            }
          }
        }
      }
      if (t4.types & D2.VERTEX) {
        const e4 = c4 ? n3.length - a2 : n3.length;
        for (let t5 = 0; t5 < e4; t5 += a2) {
          const e5 = f4;
          e5.coordsIndex = t5;
          const i4 = (r5.x - e5.x) / o2, a3 = (r5.y - e5.y) / l3, n4 = i4 * i4 + a3 * a3;
          n4 <= 1 && u2.candidates.push(t2(s4.getObjectId(y2), h(e5), Math.sqrt(n4)));
        }
      }
    }
    return u2.candidates.sort((e4, t5) => e4.distance - t5.distance), u2;
  }
  _getPointCreator(e4, i2, s4) {
    const a2 = r(s4) && !E(i2, s4) ? (e5) => g(e5, i2, s4) : (e5) => e5, { hasZ: r5 } = this, n2 = 0;
    return "3d" === e4 ? r5 ? ({ x: e5, y: t4, z: i3 }) => a2({ x: e5, y: t4, z: i3 }) : ({ x: e5, y: t4 }) => a2({ x: e5, y: t4, z: n2 }) : ({ x: e5, y: t4 }) => a2({ x: e5, y: t4 });
  }
  async createSummaryStatisticsResponse(e4) {
    const { field: t4, valueExpression: i2, normalizationField: s4, normalizationType: a2, normalizationTotal: r5, minValue: n2, maxValue: o2, scale: l3 } = e4, u2 = this.fieldsIndex.isDateField(t4), c4 = await this._getDataValues({ field: t4, valueExpression: i2, normalizationField: s4, normalizationType: a2, normalizationTotal: r5, scale: l3 }), d3 = m({ normalizationType: a2, normalizationField: s4, minValue: n2, maxValue: o2 }), m2 = this.fieldsIndex.get(t4), h = { value: 0.5, fieldType: m2 == null ? void 0 : m2.type }, f4 = te(m2) ? f({ values: c4, supportsNullCount: d3, percentileParams: h }) : d({ values: c4, minValue: n2, maxValue: o2, useSampleStdDev: !a2, supportsNullCount: d3, percentileParams: h });
    return T(f4, u2);
  }
  async createUniqueValuesResponse(e4) {
    const { field: t4, valueExpression: i2, domains: s4, returnAllCodedValues: a2, scale: r5 } = e4, n2 = await this._getDataValues({ field: t4, field2: e4.field2, field3: e4.field3, fieldDelimiter: e4.fieldDelimiter, valueExpression: i2, scale: r5 }), o2 = y(n2);
    return x(o2, s4, a2, e4.fieldDelimiter);
  }
  async createClassBreaksResponse(e4) {
    const { field: t4, valueExpression: i2, normalizationField: s4, normalizationType: a2, normalizationTotal: r5, classificationMethod: n2, standardDeviationInterval: o2, minValue: l3, maxValue: u2, numClasses: c4, scale: d3 } = e4, m2 = await this._getDataValues({ field: t4, valueExpression: i2, normalizationField: s4, normalizationType: a2, normalizationTotal: r5, scale: d3 }), h = z(m2, { field: t4, normalizationField: s4, normalizationType: a2, normalizationTotal: r5, classificationMethod: n2, standardDeviationInterval: o2, minValue: l3, maxValue: u2, numClasses: c4 });
    return S(h, n2);
  }
  async createHistogramResponse(e4) {
    const { field: t4, valueExpression: i2, normalizationField: s4, normalizationType: a2, normalizationTotal: r5, classificationMethod: n2, standardDeviationInterval: o2, minValue: l3, maxValue: u2, numBins: c4, scale: d3 } = e4, m2 = await this._getDataValues({ field: t4, valueExpression: i2, normalizationField: s4, normalizationType: a2, normalizationTotal: r5, scale: d3 });
    return M(m2, { field: t4, normalizationField: s4, normalizationType: a2, normalizationTotal: r5, classificationMethod: n2, standardDeviationInterval: o2, minValue: l3, maxValue: u2, numBins: c4 });
  }
  _sortFeatures(e4, t4, i2) {
    if (e4.length > 1 && t4 && t4.length) for (const s4 of t4.reverse()) {
      const t5 = s4.split(" "), a2 = t5[0], r5 = this.fieldsIndex.get(a2), n2 = !!t5[1] && "desc" === t5[1].toLowerCase(), o2 = v2(r5 == null ? void 0 : r5.type, n2);
      e4.sort((e5, t6) => {
        const s5 = i2(e5, a2, r5), n3 = i2(t6, a2, r5);
        return o2(s5, n3);
      });
    }
  }
  _createFeatureQueryResponse(e4) {
    const t4 = this.items, { geometryType: i2, hasM: s4, hasZ: a2, objectIdField: r5, spatialReference: o2 } = this, { outFields: l3, outSR: u2, quantizationParameters: c4, resultRecordCount: d3, resultOffset: m2, returnZ: f4, returnM: g2 } = e4, p2 = null != d3 && t4.length > (m2 || 0) + d3, y2 = l3 && (l3.includes("*") ? [...this.fieldsIndex.fields] : l3.map((e5) => this.fieldsIndex.get(e5)));
    return { exceededTransferLimit: p2, features: this._createFeatures(e4, t4), fields: y2, geometryType: i2, hasM: s4 && g2, hasZ: a2 && f4, objectIdFieldName: r5, spatialReference: E2(u2 || o2), transform: c4 && s2(c4) || null };
  }
  _createFeatures(e4, t4) {
    const i2 = new r4(e4, this.featureAdapter, this.fieldsIndex), { hasM: s4, hasZ: a2 } = this, { orderByFields: r5, quantizationParameters: o2, returnGeometry: l3, returnCentroid: c4, maxAllowableOffset: d3, resultOffset: m2, resultRecordCount: h, returnZ: p2 = false, returnM: y2 = false } = e4, x2 = a2 && p2, I2 = s4 && y2;
    let T2 = [], V = 0;
    const F = [...t4];
    if (this._sortFeatures(F, r5, (e5, t5, s5) => i2.getFieldValue(e5, t5, s5)), l3 || c4) {
      const e5 = s2(o2) ?? void 0;
      if (l3 && !c4) for (const t5 of F) T2[V++] = { attributes: i2.getAttributes(t5), geometry: v3(this.geometryType, this.hasZ, this.hasM, this.featureAdapter.getGeometry(t5), d3, e5, x2, I2) };
      else if (!l3 && c4) for (const t5 of F) T2[V++] = { attributes: i2.getAttributes(t5), centroid: b(this, this.featureAdapter.getCentroid(t5, this), e5) };
      else for (const t5 of F) T2[V++] = { attributes: i2.getAttributes(t5), centroid: b(this, this.featureAdapter.getCentroid(t5, this), e5), geometry: v3(this.geometryType, this.hasZ, this.hasM, this.featureAdapter.getGeometry(t5), d3, e5, x2, I2) };
    } else for (const n2 of F) {
      const e5 = i2.getAttributes(n2);
      e5 && (T2[V++] = { attributes: e5 });
    }
    const S2 = m2 || 0;
    if (null != h) {
      const e5 = S2 + h;
      T2 = T2.slice(S2, Math.min(T2.length, e5));
    }
    return T2;
  }
  _createExceedsLimitQueryResponse(e4) {
    let i2 = false, s4 = Number.POSITIVE_INFINITY, a2 = Number.POSITIVE_INFINITY, r5 = Number.POSITIVE_INFINITY;
    for (const t4 of e4.outStatistics ?? []) if ("exceedslimit" === t4.statisticType) {
      s4 = null != t4.maxPointCount ? t4.maxPointCount : Number.POSITIVE_INFINITY, a2 = null != t4.maxRecordCount ? t4.maxRecordCount : Number.POSITIVE_INFINITY, r5 = null != t4.maxVertexCount ? t4.maxVertexCount : Number.POSITIVE_INFINITY;
      break;
    }
    if ("esriGeometryPoint" === this.geometryType) i2 = this.items.length > s4;
    else if (this.items.length > a2) i2 = true;
    else {
      const e5 = N(this.hasZ, this.hasM), s5 = this.featureAdapter;
      i2 = this.items.reduce((e6, i3) => {
        const a3 = s5.getGeometry(i3);
        return e6 + (r(a3) && a3.coords.length || 0);
      }, 0) / e5 > r5;
    }
    return { fields: [{ name: "exceedslimit", type: "esriFieldTypeInteger", alias: "exceedslimit", sqlType: "sqlTypeInteger", domain: null, defaultValue: null }], features: [{ attributes: { exceedslimit: Number(i2) } }] };
  }
  async _createStatisticsQueryResponse(e4) {
    const t4 = { attributes: {} }, i2 = [], s4 = /* @__PURE__ */ new Map(), a2 = /* @__PURE__ */ new Map(), r5 = /* @__PURE__ */ new Map(), n2 = /* @__PURE__ */ new Map(), o2 = new r4(e4, this.featureAdapter, this.fieldsIndex), l3 = e4.outStatistics, { groupByFieldsForStatistics: c4, having: d3, orderByFields: m2 } = e4, h = c4 && c4.length, f4 = !!h, g2 = f4 ? c4[0] : null, p2 = f4 && !this.fieldsIndex.get(g2);
    for (const u2 of l3 ?? []) {
      const { outStatisticFieldName: e5, statisticType: l4 } = u2, m3 = u2, y3 = "exceedslimit" !== l4 ? u2.onStatisticField : void 0, x2 = "percentile_disc" === l4 || "percentile_cont" === l4, I2 = "EnvelopeAggregate" === l4 || "CentroidAggregate" === l4 || "ConvexHullAggregate" === l4, T2 = f4 && 1 === h && (y3 === g2 || p2) && "count" === l4;
      if (f4) {
        if (!r5.has(y3)) {
          const e6 = [];
          for (const t6 of c4) {
            const i3 = this._getAttributeValues(o2, t6, s4);
            e6.push(i3);
          }
          r5.set(y3, this._calculateUniqueValues(e6, !I2 && o2.returnDistinctValues));
        }
        const t5 = r5.get(y3);
        for (const i3 in t5) {
          const { count: a3, data: r6, items: l5, itemPositions: u3 } = t5[i3], h2 = r6.join(",");
          if (!d3 || o2.validateItems(l5, d3)) {
            const t6 = n2.get(h2) || { attributes: {} };
            if (I2) {
              t6.aggregateGeometries || (t6.aggregateGeometries = {});
              const { aggregateGeometries: e6, outStatisticFieldName: i5 } = await this._getAggregateGeometry(m3, l5);
              t6.aggregateGeometries[i5] = e6;
            } else {
              let i5 = null;
              if (T2) i5 = a3;
              else {
                const e6 = this._getAttributeValues(o2, y3, s4), t7 = u3.map((t8) => e6[t8]);
                i5 = x2 && "statisticParameters" in m3 ? this._getPercentileValue(m3, t7) : this._getStatisticValue(m3, t7, null, o2.returnDistinctValues);
              }
              t6.attributes[e5] = i5;
            }
            let i4 = 0;
            c4.forEach((e6, s5) => t6.attributes[this.fieldsIndex.get(e6) ? e6 : "EXPR_" + ++i4] = r6[s5]), n2.set(h2, t6);
          }
        }
      } else if (I2) {
        t4.aggregateGeometries || (t4.aggregateGeometries = {});
        const { aggregateGeometries: e6, outStatisticFieldName: i3 } = await this._getAggregateGeometry(m3, this.items);
        t4.aggregateGeometries[i3] = e6;
      } else {
        const i3 = this._getAttributeValues(o2, y3, s4);
        t4.attributes[e5] = x2 && "statisticParameters" in m3 ? this._getPercentileValue(m3, i3) : this._getStatisticValue(m3, i3, a2, o2.returnDistinctValues);
      }
      i2.push({ name: e5, alias: e5, type: "esriFieldTypeDouble" });
    }
    const y2 = f4 ? Array.from(n2.values()) : [t4];
    return this._sortFeatures(y2, m2, (e5, t5) => e5.attributes[t5]), { fields: i2, features: y2 };
  }
  async _getAggregateGeometry(e4, t4) {
    const n2 = await import("./geometryEngineJSON-2XCCV7A5.js"), { statisticType: o2, outStatisticFieldName: l3 } = e4, { featureAdapter: u2, spatialReference: c4, geometryType: d3, hasZ: m2, hasM: h } = this, g2 = t4.map((e5) => v3(d3, m2, h, u2.getGeometry(e5))), p2 = n2.convexHull(c4, g2, true)[0], y2 = { aggregateGeometries: null, outStatisticFieldName: null };
    if ("EnvelopeAggregate" === o2) {
      const e5 = p2 ? v(p2) : l(n2.union(c4, g2));
      y2.aggregateGeometries = { ...e5, spatialReference: c4 }, y2.outStatisticFieldName = l3 || "extent";
    } else if ("CentroidAggregate" === o2) {
      const e5 = p2 ? r2(p2) : e(l(n2.union(c4, g2)));
      y2.aggregateGeometries = { x: e5[0], y: e5[1], spatialReference: c4 }, y2.outStatisticFieldName = l3 || "centroid";
    } else "ConvexHullAggregate" === o2 && (y2.aggregateGeometries = p2, y2.outStatisticFieldName = l3 || "convexHull");
    return y2;
  }
  _getStatisticValue(e4, t4, i2, s4) {
    const { onStatisticField: a2, statisticType: r5 } = e4;
    let n2 = null;
    n2 = (i2 == null ? void 0 : i2.has(a2)) ? i2.get(a2) : te(this.fieldsIndex.get(a2)) ? f({ values: t4, returnDistinct: s4 }) : d({ values: s4 ? [...new Set(t4)] : t4, minValue: null, maxValue: null, useSampleStdDev: true }), i2 && i2.set(a2, n2);
    return n2["var" === r5 ? "variance" : r5];
  }
  _getPercentileValue(e4, t4) {
    const { onStatisticField: i2, statisticParameters: s4, statisticType: a2 } = e4, { value: r5, orderBy: n2 } = s4, o2 = this.fieldsIndex.get(i2);
    return p(t4, { value: r5, orderBy: n2, fieldType: o2 == null ? void 0 : o2.type, isDiscrete: "percentile_disc" === a2 });
  }
  _getAttributeValues(e4, t4, i2) {
    if (i2.has(t4)) return i2.get(t4);
    const s4 = this.fieldsIndex.get(t4), a2 = this.items.map((i3) => e4.getFieldValue(i3, t4, s4));
    return i2.set(t4, a2), a2;
  }
  _getAttributeDataValues(e4, t4) {
    return this.items.map((i2) => e4.getDataValue(i2, { field: t4.field, field2: t4.field2, field3: t4.field3, fieldDelimiter: t4.fieldDelimiter, normalizationField: t4.normalizationField, normalizationType: t4.normalizationType, normalizationTotal: t4.normalizationTotal }));
  }
  async _getAttributeExpressionValues(e4, t4, i2) {
    const { arcadeUtils: s4 } = await i(), a2 = s4.createFunction(t4), r5 = i2 && s4.getViewInfo(i2);
    return e4.getExpressionValues(this.items, a2, r5, s4);
  }
  _calculateUniqueValues(e4, t4) {
    const i2 = {}, s4 = this.items, a2 = s4.length;
    for (let r5 = 0; r5 < a2; r5++) {
      const a3 = s4[r5], n2 = [];
      for (const t5 of e4) n2.push(t5[r5]);
      const o2 = n2.join(",");
      null == i2[o2] ? i2[o2] = { count: 1, data: n2, items: [a3], itemPositions: [r5] } : (t4 || i2[o2].count++, i2[o2].items.push(a3), i2[o2].itemPositions.push(r5));
    }
    return i2;
  }
  async _getDataValues(e4) {
    const t4 = new r4(this.query, this.featureAdapter, this.fieldsIndex), { valueExpression: i2, field: s4, normalizationField: a2, normalizationType: r5, normalizationTotal: n2, scale: o2 } = e4, l3 = i2 ? { viewingMode: "map", scale: o2, spatialReference: this.query.outSR || this.spatialReference } : null;
    return i2 ? this._getAttributeExpressionValues(t4, i2, l3) : this._getAttributeDataValues(t4, { field: s4, field2: e4.field2, field3: e4.field3, fieldDelimiter: e4.fieldDelimiter, normalizationField: a2, normalizationType: r5, normalizationTotal: n2 });
  }
};
function E3(e4, t4, i2, s4) {
  const a2 = s4.x - i2.x, r5 = s4.y - i2.y, n2 = a2 * a2 + r5 * r5, o2 = (t4.x - i2.x) * a2 + (t4.y - i2.y) * r5, l3 = Math.min(1, Math.max(0, o2 / n2));
  e4.x = i2.x + a2 * l3, e4.y = i2.y + r5 * l3;
}
function N(e4, t4) {
  return e4 ? t4 ? 4 : 3 : t4 ? 3 : 2;
}
var D2;
!function(e4) {
  e4[e4.NONE = 0] = "NONE", e4[e4.EDGE = 1] = "EDGE", e4[e4.VERTEX = 2] = "VERTEX";
}(D2 || (D2 = {}));
var q = class {
  constructor(e4, t4) {
    this.coords = e4, this.coordsIndex = t4;
  }
  get x() {
    return this.coords[this.coordsIndex];
  }
  get y() {
    return this.coords[this.coordsIndex + 1];
  }
  get z() {
    return this.coords[this.coordsIndex + 2];
  }
};

export {
  o,
  a,
  l2 as l,
  c3 as c,
  f3 as f,
  A,
  D2 as D
};
//# sourceMappingURL=chunk-HLLJFAS4.js.map
