{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/FoamRendering.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/WaterDistortion.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/Gamma.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/ScreenSpaceReflections.glsl.js", "../../@arcgis/core/views/3d/environment/CloudsData.js", "../../@arcgis/core/views/3d/environment/CloudsCompositionParameters.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderModules/TextureCubePassUniform.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/CloudsParallaxShading.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/Water.glsl.js", "../../@arcgis/core/chunks/WaterSurface.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{glsl as t}from\"../../shaderModules/interfaces.js\";function o(o){o.fragment.code.add(t`float normals2FoamIntensity(vec3 n, float waveStrength){\nfloat normalizationFactor =  max(0.015, waveStrength);\nreturn max((n.x + n.y)*0.3303545/normalizationFactor + 0.3303545, 0.0);\n}`)}function n(o){o.fragment.code.add(t`vec3 foamIntensity2FoamColor(float foamIntensityExternal, float foamPixelIntensity, vec3 skyZenitColor, float dayMod){\nreturn foamIntensityExternal * (0.075 * skyZenitColor * pow(foamPixelIntensity, 4.) +  50.* pow(foamPixelIntensity, 23.0)) * dayMod;\n}`)}export{n as FoamColor,o as FoamIntensity};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{s as e}from\"../../../../../../chunks/vec2.js\";import{a as t}from\"../../../../../../chunks/vec2f64.js\";import{s as r}from\"../../../../../../chunks/vec4.js\";import{c as a}from\"../../../../../../chunks/vec4f64.js\";import{FoamIntensity as o}from\"./FoamRendering.glsl.js\";import{Float2PassUniform as m}from\"../../shaderModules/Float2PassUniform.js\";import{Float4PassUniform as s}from\"../../shaderModules/Float4PassUniform.js\";import{glsl as v,NoParameters as u}from\"../../shaderModules/interfaces.js\";import{Texture2DPassUniform as l}from\"../../shaderModules/Texture2DPassUniform.js\";function i(t){t.fragment.uniforms.add(new l(\"texWaveNormal\",(e=>e.waveNormal))),t.fragment.uniforms.add(new l(\"texWavePerturbation\",(e=>e.wavePertubation))),t.fragment.uniforms.add([new s(\"waveParams\",(e=>r(f,e.waveStrength,e.waveTextureRepeat,e.flowStrength,e.flowOffset))),new m(\"waveDirection\",(t=>e(c,t.waveDirection[0]*t.waveVelocity,t.waveDirection[1]*t.waveVelocity)))]),t.include(o),t.fragment.code.add(v`const vec2  FLOW_JUMP = vec2(6.0/25.0, 5.0/24.0);\nvec2 textureDenormalized2D(sampler2D _tex, vec2 _uv) {\nreturn 2.0 * texture2D(_tex, _uv).rg - 1.0;\n}\nfloat sampleNoiseTexture(vec2 _uv) {\nreturn texture2D(texWavePerturbation, _uv).b;\n}\nvec3 textureDenormalized3D(sampler2D _tex, vec2 _uv) {\nreturn 2.0 * texture2D(_tex, _uv).rgb - 1.0;\n}\nfloat computeProgress(vec2 uv, float time) {\nreturn fract(time);\n}\nfloat computeWeight(vec2 uv, float time) {\nfloat progress = computeProgress(uv, time);\nreturn 1.0 - abs(1.0 - 2.0 * progress);\n}\nvec3 computeUVPerturbedWeigth(sampler2D texFlow, vec2 uv, float time, float phaseOffset) {\nfloat flowStrength = waveParams[2];\nfloat flowOffset = waveParams[3];\nvec2 flowVector = textureDenormalized2D(texFlow, uv) * flowStrength;\nfloat progress = computeProgress(uv, time + phaseOffset);\nfloat weight = computeWeight(uv, time + phaseOffset);\nvec2 result = uv;\nresult -= flowVector * (progress + flowOffset);\nresult += phaseOffset;\nresult += (time - progress) * FLOW_JUMP;\nreturn vec3(result, weight);\n}\nconst float TIME_NOISE_TEXTURE_REPEAT = 0.3737;\nconst float TIME_NOISE_STRENGTH = 7.77;\nvec3 getWaveLayer(sampler2D _texNormal, sampler2D _dudv, vec2 _uv, vec2 _waveDir, float time) {\nfloat waveStrength = waveParams[0];\nvec2 waveMovement = time * -_waveDir;\nfloat timeNoise = sampleNoiseTexture(_uv * TIME_NOISE_TEXTURE_REPEAT) * TIME_NOISE_STRENGTH;\nvec3 uv_A = computeUVPerturbedWeigth(_dudv, _uv + waveMovement, time + timeNoise, 0.0);\nvec3 uv_B = computeUVPerturbedWeigth(_dudv, _uv + waveMovement, time + timeNoise, 0.5);\nvec3 normal_A = textureDenormalized3D(_texNormal, uv_A.xy) * uv_A.z;\nvec3 normal_B = textureDenormalized3D(_texNormal, uv_B.xy) * uv_B.z;\nvec3 mixNormal = normalize(normal_A + normal_B);\nmixNormal.xy *= waveStrength;\nmixNormal.z = sqrt(1.0 - dot(mixNormal.xy, mixNormal.xy));\nreturn mixNormal;\n}\nvec4 getSurfaceNormalAndFoam(vec2 _uv, float _time) {\nfloat waveTextureRepeat = waveParams[1];\nvec3 normal = getWaveLayer(texWaveNormal, texWavePerturbation, _uv * waveTextureRepeat, waveDirection, _time);\nfloat foam  = normals2FoamIntensity(normal, waveParams[0]);\nreturn vec4(normal, foam);\n}`)}class n extends u{}const f=a(),c=t();export{i as WaterDistortion,n as WaterDistortionPassParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{glsl as o}from\"../../shaderModules/interfaces.js\";function e(e){e.fragment.code.add(o`const float GAMMA = 2.2;\nconst float INV_GAMMA = 0.4545454545;\nvec4 delinearizeGamma(vec4 color) {\nreturn vec4(pow(color.rgb, vec3(INV_GAMMA)), color.w);\n}\nvec3 linearizeGamma(vec3 color) {\nreturn pow(color, vec3(GAMMA));\n}`)}export{e as Gamma};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as e}from\"../../../../../../chunks/mat4f64.js\";import{ReadLinearDepth as o}from\"../output/ReadLinearDepth.glsl.js\";import{Float2PassUniform as t}from\"../../shaderModules/Float2PassUniform.js\";import{FloatPassUniform as r}from\"../../shaderModules/FloatPassUniform.js\";import{glsl as a}from\"../../shaderModules/interfaces.js\";import{Matrix4PassUniform as i}from\"../../shaderModules/Matrix4PassUniform.js\";import{Texture2DPassUniform as d}from\"../../shaderModules/Texture2DPassUniform.js\";function n(e,n){const c=e.fragment;c.include(o),c.uniforms.add(new t(\"nearFar\",((e,o)=>o.camera.nearFar))),c.uniforms.add(new d(\"depthMap\",((e,o)=>o.linearDepthTexture))),c.uniforms.add(new i(\"proj\",((e,o)=>o.ssr.camera.projectionMatrix))),c.uniforms.add(new r(\"invResolutionHeight\",((e,o)=>1/o.ssr.camera.height))),c.uniforms.add(new i(\"reprojectionMatrix\",((e,o)=>o.ssr.reprojectionMatrix))),c.code.add(a`\n  vec2 reprojectionCoordinate(vec3 projectionCoordinate)\n  {\n    vec4 zw = proj * vec4(0.0, 0.0, -projectionCoordinate.z, 1.0);\n    vec4 reprojectedCoord = reprojectionMatrix * vec4(zw.w * (projectionCoordinate.xy * 2.0 - 1.0), zw.z, zw.w);\n    reprojectedCoord.xy /= reprojectedCoord.w;\n    return reprojectedCoord.xy * 0.5 + 0.5;\n  }\n\n  const int maxSteps = ${n.highStepCount?\"150\":\"75\"};\n\n  vec4 applyProjectionMat(mat4 projectionMat, vec3 x)\n  {\n    vec4 projectedCoord =  projectionMat * vec4(x, 1.0);\n    projectedCoord.xy /= projectedCoord.w;\n    projectedCoord.xy = projectedCoord.xy*0.5 + 0.5;\n    return projectedCoord;\n  }\n\n  vec3 screenSpaceIntersection(vec3 dir, vec3 startPosition, vec3 viewDir, vec3 normal)\n  {\n    vec3 viewPos = startPosition;\n    vec3 viewPosEnd = startPosition;\n\n    // Project the start position to the screen\n    vec4 projectedCoordStart = applyProjectionMat(proj, viewPos);\n    vec3  Q0 = viewPos / projectedCoordStart.w; // homogeneous camera space\n    float k0 = 1.0/ projectedCoordStart.w;\n\n    // advance the position in the direction of the reflection\n    viewPos += dir;\n\n    vec4 projectedCoordVanishingPoint = applyProjectionMat(proj, dir);\n\n    // Project the advanced position to the screen\n    vec4 projectedCoordEnd = applyProjectionMat(proj, viewPos);\n    vec3  Q1 = viewPos / projectedCoordEnd.w; // homogeneous camera space\n    float k1 = 1.0/ projectedCoordEnd.w;\n\n    // calculate the reflection direction in the screen space\n    vec2 projectedCoordDir = (projectedCoordEnd.xy - projectedCoordStart.xy);\n    vec2 projectedCoordDistVanishingPoint = (projectedCoordVanishingPoint.xy - projectedCoordStart.xy);\n\n    float yMod = min(abs(projectedCoordDistVanishingPoint.y), 1.0);\n\n    float projectedCoordDirLength = length(projectedCoordDir);\n    float maxSt = float(maxSteps);\n\n    // normalize the projection direction depending on maximum steps\n    // this determines how blocky the reflection looks\n    vec2 dP = yMod * (projectedCoordDir)/(maxSt * projectedCoordDirLength);\n\n    // Normalize the homogeneous camera space coordinates\n    vec3  dQ = yMod * (Q1 - Q0)/(maxSt * projectedCoordDirLength);\n    float dk = yMod * (k1 - k0)/(maxSt * projectedCoordDirLength);\n\n    // initialize the variables for ray marching\n    vec2 P = projectedCoordStart.xy;\n    vec3 Q = Q0;\n    float k = k0;\n    float rayStartZ = -startPosition.z; // estimated ray start depth value\n    float rayEndZ = -startPosition.z;   // estimated ray end depth value\n    float prevEstimateZ = -startPosition.z;\n    float rayDiffZ = 0.0;\n    float dDepth;\n    float depth;\n    float rayDiffZOld = 0.0;\n\n    // early outs\n    if (dot(normal, dir) < 0.0 || dot(-viewDir, normal) < 0.0)\n      return vec3(P, 0.0);\n\n    for(int i = 0; i < maxSteps-1; i++)\n    {\n      depth = -linearDepthFromTexture(depthMap, P, nearFar); // get linear depth from the depth buffer\n\n      // estimate depth of the marching ray\n      rayStartZ = prevEstimateZ;\n      dDepth = -rayStartZ - depth;\n      rayEndZ = (dQ.z * 0.5 + Q.z)/ ((dk * 0.5 + k));\n      rayDiffZ = rayEndZ- rayStartZ;\n      prevEstimateZ = rayEndZ;\n\n      if(-rayEndZ > nearFar[1] || -rayEndZ < nearFar[0] || P.y < 0.0  || P.y > 1.0 )\n      {\n        return vec3(P, 0.);\n      }\n\n      // If we detect a hit - return the intersection point, two conditions:\n      //  - dDepth > 0.0 - sampled point depth is in front of estimated depth\n      //  - if difference between dDepth and rayDiffZOld is not too large\n      //  - if difference between dDepth and 0.025/abs(k) is not too large\n      //  - if the sampled depth is not behind far plane or in front of near plane\n\n      if((dDepth) < 0.025/abs(k) + abs(rayDiffZ) && dDepth > 0.0 && depth > nearFar[0] && depth < nearFar[1] && abs(P.y - projectedCoordStart.y) > invResolutionHeight)\n      {\n        return vec3(P, depth);\n      }\n\n      // continue with ray marching\n      P += dP;\n      Q.z += dQ.z;\n      k += dk;\n      rayDiffZOld = rayDiffZ;\n    }\n    return vec3(P, 0.0);\n  }\n  `)}class c{constructor(){this.enabled=!1,this.fadeFactor=1,this.reprojectionMatrix=e()}}export{c as SSRUniforms,n as ScreenSpaceReflections};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as N}from\"../../../core/maybe.js\";var E,I;function n(E){return N(E)&&N(E.cubeMap)}function R(E){return N(E)&&!E.running}!function(N){N[N.RENDERING=0]=\"RENDERING\",N[N.FINISHED_RENDERING=1]=\"FINISHED_RENDERING\",N[N.FADING_TEXTURE_CHANNELS=2]=\"FADING_TEXTURE_CHANNELS\",N[N.SWITCH_CHANNELS=3]=\"SWITCH_CHANNELS\",N[N.FINISHED=4]=\"FINISHED\"}(E||(E={})),function(N){N[N.RG=0]=\"RG\",N[N.BA=1]=\"BA\"}(I||(I={}));export{E as CloudsRenderingStages,I as CloudsTextureChannels,R as ensureClouds,n as isReadyCloudsData};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as t}from\"../../../chunks/mat4f64.js\";import{c as s}from\"../../../chunks/vec3f64.js\";import{CloudsTextureChannels as a,CloudsRenderingStages as e}from\"./CloudsData.js\";class i{constructor(){this.readChannels=a.RG,this.renderingStage=e.FINISHED,this.startTime=0,this.startTimeHeightFade=0,this.cameraPositionLastFrame=s(),this.isCameraPositionFinal=!0,this.parallax=new o,this.parallaxNew=new o,this.crossFade={enabled:!1,factor:1,distanceThresholdFactor:.3},this.fadeInOut={stage:I.FINISHED,factor:1,distanceThresholdFactor:.6},this.fadeIn={stage:r.FINISHED,factor:1,distanceThresholdFactor:2},this.fadeInOutHeight={stage:n.FINISHED,factor:-1}}get isFading(){return this.fadeInOut.stage===I.FADE_OUT||this.fadeInOut.stage===I.FADE_IN||this.fadeIn.stage===r.FADE_IN||this.fadeInOutHeight.stage!==n.FINISHED||this.renderingStage===e.FADING_TEXTURE_CHANNELS}}var r,I,n;!function(t){t[t.FINISHED=0]=\"FINISHED\",t[t.CHANGE_ANCHOR=1]=\"CHANGE_ANCHOR\",t[t.FADE_IN=2]=\"FADE_IN\"}(r||(r={})),function(t){t[t.FINISHED=0]=\"FINISHED\",t[t.FADE_OUT=1]=\"FADE_OUT\",t[t.SWITCH=2]=\"SWITCH\",t[t.FADE_IN=3]=\"FADE_IN\"}(I||(I={})),function(t){t[t.FINISHED=0]=\"FINISHED\",t[t.HEIGHT_FADE=1]=\"HEIGHT_FADE\"}(n||(n={}));class o{constructor(){this.anchorPointClouds=s(),this.cloudsHeight=1e5,this.radiusCurvatureCorrectionFactor=0,this.transform=t()}}export{i as CloudsFadeParameters,n as FadeHeightStages,I as FadeInOutStages,r as FadeInStages};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Uniform as e}from\"./Uniform.js\";import{BindType as r}from\"../shaderTechnique/BindType.js\";class s extends e{constructor(e,s){super(e,\"samplerCube\",r.Pass,((r,o,t)=>r.bindTexture(e,s(o,t))))}}export{s as TextureCubePassUniform};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clamp as o}from\"../../../../../../core/mathUtils.js\";import{isSome as a}from\"../../../../../../core/maybe.js\";import{earth as t}from\"../../../../../../geometry/support/Ellipsoid.js\";import{FadeInOutStages as e}from\"../../../../environment/CloudsCompositionParameters.js\";import{CloudsTextureChannels as r,CloudsRenderingStages as d}from\"../../../../environment/CloudsData.js\";import{addMainLightDirection as i,addMainLightIntensity as c}from\"../shading/MainLighting.glsl.js\";import{BooleanPassUniform as n}from\"../../shaderModules/BooleanPassUniform.js\";import{Float3PassUniform as l}from\"../../shaderModules/Float3PassUniform.js\";import{FloatPassUniform as s}from\"../../shaderModules/FloatPassUniform.js\";import{glsl as u}from\"../../shaderModules/interfaces.js\";import{Matrix4PassUniform as C}from\"../../shaderModules/Matrix4PassUniform.js\";import{TextureCubePassUniform as m}from\"../../shaderModules/TextureCubePassUniform.js\";function v(v){const R=v.fragment;R.uniforms.add([new C(\"rotationMatrixClouds\",((o,a)=>a.cloudsFade.parallax.transform)),new C(\"rotationMatrixCloudsCrossFade\",((o,a)=>a.cloudsFade.parallaxNew.transform)),new l(\"anchorPosition\",((o,a)=>a.cloudsFade.parallax.anchorPointClouds)),new l(\"anchorPositionCrossFade\",((o,a)=>a.cloudsFade.parallaxNew.anchorPointClouds)),new s(\"cloudsHeight\",((o,a)=>a.cloudsFade.parallax.cloudsHeight)),new s(\"radiusCurvatureCorrectionFactor\",((o,a)=>a.cloudsFade.parallax.radiusCurvatureCorrectionFactor)),new s(\"totalFadeInOut\",((o,a)=>a.cloudsFade.fadeInOut.stage===e.FINISHED?a.cloudsFade.fadeInOutHeight.factor+1-a.cloudsFade.fadeIn.factor:a.cloudsFade.fadeInOutHeight.factor+1-a.cloudsFade.fadeInOut.factor)),new s(\"crossFadeAnchorFactor\",((a,t)=>o(t.cloudsFade.crossFade.factor,0,1))),new m(\"cubeMap\",((o,t)=>a(t.cloudsFade.data)&&a(t.cloudsFade.data.cubeMap)?t.cloudsFade.data.cubeMap.colorTexture:null)),new n(\"crossFade\",((o,a)=>a.cloudsFade.crossFade.enabled)),new n(\"readChannelsRG\",((o,a)=>a.cloudsFade.readChannels===r.RG)),new n(\"fadeTextureChannels\",((o,a)=>a.cloudsFade.renderingStage===d.FADING_TEXTURE_CHANNELS))]),R.constants.add(\"planetRadius\",\"float\",t.radius),R.code.add(u`vec3 intersectWithCloudLayer(vec3 dir, vec3 cameraPosition, vec3 spherePos)\n{\nfloat radiusClouds = planetRadius + cloudsHeight;\nfloat B = 2.0 * dot(cameraPosition, dir);\nfloat C = dot(cameraPosition, cameraPosition) - radiusClouds * radiusClouds;\nfloat det = B * B - 4.0 * C;\nfloat pointIntDist = max(0.0, 0.5 *(-B + sqrt(det)));\nvec3 intersectionPont = cameraPosition + dir * pointIntDist;\nintersectionPont =  intersectionPont - spherePos;\nreturn intersectionPont;\n}`),R.code.add(u`vec3 correctForPlanetCurvature(vec3 dir)\n{\ndir.z = dir.z*(1.-radiusCurvatureCorrectionFactor) + radiusCurvatureCorrectionFactor;\nreturn dir;\n}`),R.code.add(u`vec3 rotateDirectionToAnchorPoint(mat4 rotMat, vec3 inVec)\n{\nreturn (rotMat * vec4(inVec, 0.0)).xyz;\n}`),i(R),c(R),R.code.add(u`const float SUNSET_TRANSITION_FACTOR = 0.3;\nconst vec3 RIM_COLOR = vec3(0.28, 0.175, 0.035);\nconst float RIM_SCATTERING_FACTOR = 140.0;\nconst float BACKLIGHT_FACTOR = 0.2;\nconst float BACKLIGHT_SCATTERING_FACTOR = 10.0;\nconst float BACKLIGHT_TRANSITION_FACTOR = 0.3;\nvec3 calculateCloudColor(vec3 cameraPosition, vec3 worldSpaceRay, vec4 clouds)\n{\nfloat upDotLight = dot(normalize(cameraPosition), normalize(mainLightDirection));\nfloat dirDotLight = max(dot(normalize(-worldSpaceRay), normalize(mainLightDirection)), 0.0);\nfloat sunsetTransition = clamp(pow(max(upDotLight, 0.0), SUNSET_TRANSITION_FACTOR), 0.0, 1.0);\nvec3 ambientLight = calculateAmbientIrradiance(normalize(cameraPosition),  0.0);\nvec3 mainLight = evaluateMainLighting(normalize(cameraPosition),  0.0);\nvec3 combinedLight = clamp((mainLightIntensity + ambientLight )/PI, vec3(0.0), vec3(1.0));\nvec3 baseCloudColor = pow(combinedLight * pow(clouds.xyz, vec3(GAMMA)), vec3(INV_GAMMA));\nfloat scatteringMod = max(clouds.a < 0.5 ? clouds.a / 0.5 : - clouds.a / 0.5 + 2.0, 0.0);\nfloat rimLightIntensity = 0.5 + 0.5 *pow(max(upDotLight, 0.0), 0.35);\nvec3 directSunScattering = RIM_COLOR * rimLightIntensity * (pow(dirDotLight, RIM_SCATTERING_FACTOR)) * scatteringMod;\nfloat additionalLight = BACKLIGHT_FACTOR * pow(dirDotLight, BACKLIGHT_SCATTERING_FACTOR) * (1. - pow(sunsetTransition, BACKLIGHT_TRANSITION_FACTOR)) ;\nreturn vec3(baseCloudColor * (1. + additionalLight) + directSunScattering);\n}`),R.code.add(u`vec4 getCloudData(vec3 rayDir, bool readOtherChannel)\n{\nvec4 cloudData = textureCube(cubeMap, rayDir);\nfloat mu = dot(rayDir, vec3(0, 0, 1));\nbool readChannels = readChannelsRG ^^ readOtherChannel;\nif (readChannels) {\ncloudData = vec4(vec3(cloudData.r), cloudData.g);\n} else {\ncloudData = vec4(vec3(cloudData.b), cloudData.a);\n}\nif (length(cloudData) == 0.0) {\nreturn vec4(cloudData.rgb, 1.0);\n}\nreturn cloudData;\n}`),R.code.add(u`vec4 renderCloudsNoFade(vec3 worldRay, vec3 cameraPosition)\n{\nvec3 intersectionPoint = intersectWithCloudLayer(normalize(worldRay), cameraPosition, anchorPosition);\nvec3 worldRayRotated = rotateDirectionToAnchorPoint(rotationMatrixClouds, normalize(intersectionPoint));\nvec3 worldRayRotatedCorrected = correctForPlanetCurvature(worldRayRotated);\nvec4 cloudData = getCloudData(worldRayRotatedCorrected, false);\nfloat totalTransmittance = clamp(cloudData.a * (1.0 - totalFadeInOut) + totalFadeInOut, 0.0 , 1.0);\nif (length(cloudData.rgb) == 0.0) {\ntotalTransmittance = 1.0;\n}\nreturn vec4(calculateCloudColor(cameraPosition, normalize(-worldRay), cloudData), totalTransmittance);\n}`),R.code.add(u`vec4 renderCloudsCrossFade(vec3 worldRay, vec3 cameraPosition)\n{\nvec3 intersectionPoint = intersectWithCloudLayer(normalize(worldRay), cameraPosition, anchorPosition);\nvec3 worldRayRotated = rotateDirectionToAnchorPoint(rotationMatrixClouds, normalize(intersectionPoint));\nvec3 worldRayRotatedCorrected = correctForPlanetCurvature(worldRayRotated);\nvec4 cloudData = getCloudData(worldRayRotatedCorrected, false);\nvec4 cloudColor = vec4(calculateCloudColor(cameraPosition, normalize(-worldRay), cloudData), cloudData.a);\nintersectionPoint = intersectWithCloudLayer(normalize(worldRay), cameraPosition, anchorPositionCrossFade);\nworldRayRotated = rotateDirectionToAnchorPoint(rotationMatrixCloudsCrossFade, normalize(intersectionPoint));\nworldRayRotatedCorrected = correctForPlanetCurvature(worldRayRotated);\ncloudData = getCloudData(worldRayRotatedCorrected, fadeTextureChannels);\nvec4 cloudColorCrossFade = vec4(calculateCloudColor(cameraPosition, normalize(-worldRay), cloudData), cloudData.a);\ncloudColor = mix(cloudColor, cloudColorCrossFade, crossFadeAnchorFactor);\nfloat totalTransmittance = clamp(cloudColor.a * (1.0 - totalFadeInOut) + totalFadeInOut, 0.0 , 1.0);\nif (length(cloudColor.rgb) == 0.0) {\ntotalTransmittance = 1.0;\n}\nreturn vec4(cloudColor.rgb, totalTransmittance);\n}`),R.code.add(u`vec4 renderClouds(vec3 worldRay, vec3 cameraPosition)\n{\nreturn crossFade ? renderCloudsCrossFade(worldRay, cameraPosition) : renderCloudsNoFade(worldRay, cameraPosition);\n}`)}export{v as CloudsParallaxShading};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{FoamColor as e}from\"./FoamRendering.glsl.js\";import{Gamma as o}from\"./Gamma.glsl.js\";import{PhysicallyBasedRenderingWater as r}from\"./PhysicallyBasedRendering.glsl.js\";import{ScreenSpaceReflections as t}from\"./ScreenSpaceReflections.glsl.js\";import{CloudsParallaxShading as i}from\"../util/CloudsParallaxShading.glsl.js\";import{FloatPassUniform as a}from\"../../shaderModules/FloatPassUniform.js\";import{glsl as l}from\"../../shaderModules/interfaces.js\";import{Matrix4PassUniform as n}from\"../../shaderModules/Matrix4PassUniform.js\";import{Texture2DPassUniform as d}from\"../../shaderModules/Texture2DPassUniform.js\";function c(c,s){c.include(r,s),c.include(o),c.include(e),s.hasCloudsReflections&&c.include(i,s),s.hasScreenSpaceReflections&&c.include(t,s);const f=c.fragment;f.constants.add(\"fresnelSky\",\"vec3\",[.02,1,15]).add(\"fresnelMaterial\",\"vec2\",[.02,.1]).add(\"roughness\",\"float\",.015).add(\"foamIntensityExternal\",\"float\",1.7).add(\"ssrIntensity\",\"float\",.65).add(\"ssrHeightFadeStart\",\"float\",3e5).add(\"ssrHeightFadeEnd\",\"float\",5e5).add(\"waterDiffusion\",\"float\",.92).add(\"waterSeaColorMod\",\"float\",.8).add(\"correctionViewingPowerFactor\",\"float\",.4).add(\"skyZenitColor\",\"vec3\",[.52,.68,.9]).add(\"skyColor\",\"vec3\",[.67,.79,.9]).add(\"cloudFresnelModifier\",\"vec2\",[1.2,.01]),f.code.add(l`PBRShadingWater shadingInfo;\nvec3 getSkyGradientColor(in float cosTheta, in vec3 horizon, in vec3 zenit) {\nfloat exponent = pow((1.0 - cosTheta), fresnelSky[2]);\nreturn mix(zenit, horizon, exponent);\n}`),f.uniforms.add([new a(\"lightingSpecularStrength\",((e,o)=>o.lighting.mainLight.specularStrength)),new a(\"lightingEnvironmentStrength\",((e,o)=>o.lighting.mainLight.environmentStrength))]),f.code.add(l`vec3 getSeaColor(in vec3 n, in vec3 v, in vec3 l, vec3 color, in vec3 lightIntensity, in vec3 localUp, in float shadow, float foamIntensity, vec3 viewPosition, vec3 position) {\nfloat reflectionHit = 0.0;\nfloat reflectionHitDiffused = 0.0;\nvec3 seaWaterColor = linearizeGamma(color);\nvec3 h = normalize(l + v);\nshadingInfo.NdotL = clamp(dot(n, l), 0.0, 1.0);\nshadingInfo.NdotV = clamp(dot(n, v), 0.001, 1.0);\nshadingInfo.VdotN = clamp(dot(v, n), 0.001, 1.0);\nshadingInfo.NdotH = clamp(dot(n, h), 0.0, 1.0);\nshadingInfo.VdotH = clamp(dot(v, h), 0.0, 1.0);\nshadingInfo.LdotH = clamp(dot(l, h), 0.0, 1.0);\nfloat upDotV = max(dot(localUp,v), 0.0);\nvec3 skyHorizon = linearizeGamma(skyColor);\nvec3 skyZenit = linearizeGamma(skyZenitColor);\nvec3 skyColor = getSkyGradientColor(upDotV, skyHorizon, skyZenit );\nfloat upDotL = max(dot(localUp,l),0.0);\nfloat daytimeMod = 0.1 + upDotL * 0.9;\nskyColor *= daytimeMod;\nfloat shadowModifier = clamp(shadow, 0.8, 1.0);\nvec3 fresnelModifier = fresnelReflection(shadingInfo.VdotN, vec3(fresnelSky[0]), fresnelSky[1]);\nvec3 reflSky = lightingEnvironmentStrength * fresnelModifier * skyColor * shadowModifier;\nvec3 reflSea = seaWaterColor * mix(skyColor, upDotL * lightIntensity * LIGHT_NORMALIZATION, 2.0 / 3.0) * shadowModifier;\nvec3 specular = vec3(0.0);\nif(upDotV > 0.0 && upDotL > 0.0) {\nvec3 specularSun = brdfSpecularWater(shadingInfo, roughness, vec3(fresnelMaterial[0]), fresnelMaterial[1]);\nvec3 incidentLight = lightIntensity * LIGHT_NORMALIZATION * shadow;\nspecular = lightingSpecularStrength * shadingInfo.NdotL * incidentLight * specularSun;\n}\nvec3 foam = vec3(0.0);\nif(upDotV > 0.0) {\nfoam = foamIntensity2FoamColor(foamIntensityExternal, foamIntensity, skyZenitColor, daytimeMod);\n}\nfloat correctionViewingFactor = pow(max(dot(v, localUp), 0.0), correctionViewingPowerFactor);\nvec3 normalCorrectedClouds = mix(localUp, n, correctionViewingFactor);\nvec3 reflectedWorld = normalize(reflect(-v, normalCorrectedClouds));`),s.hasCloudsReflections&&f.code.add(l`vec4 cloudsColor = renderClouds(reflectedWorld, position);\ncloudsColor.a = 1.0 - cloudsColor.a;\ncloudsColor = pow(cloudsColor, vec4(GAMMA));\ncloudsColor *= clamp(fresnelModifier.y*cloudFresnelModifier[0] - cloudFresnelModifier[1], 0.0, 1.0) * clamp((1.0 - totalFadeInOut), 0.0, 1.0);`),s.hasScreenSpaceReflections?(f.uniforms.add([new n(\"view\",((e,o)=>o.ssr.camera.viewMatrix)),new d(\"lastFrameColorTexture\",((e,o)=>o.ssr.lastFrameColorTexture)),new a(\"fadeFactor\",((e,o)=>o.ssr.fadeFactor))]),f.code.add(l`vec3 viewDir = normalize(viewPosition);\nvec4 viewNormalVectorCoordinate = view *vec4(n, 0.0);\nvec3 viewNormal = normalize(viewNormalVectorCoordinate.xyz);\nvec4 viewUp = view * vec4(localUp, 0.0);\nvec3 viewNormalCorrectedSSR = mix(viewUp.xyz, viewNormal, correctionViewingFactor);\nvec3 reflected = normalize(reflect(viewDir, viewNormalCorrectedSSR));\nvec3 hitCoordinate = screenSpaceIntersection(reflected, viewPosition, viewDir, viewUp.xyz);\nvec3 reflectedColor = vec3(0.0);\nif (hitCoordinate.z > 0.0)\n{\nvec2 reprojectedCoordinate = reprojectionCoordinate(hitCoordinate);\nvec2 dCoords = smoothstep(0.3, 0.6, abs(vec2(0.5, 0.5) - hitCoordinate.xy));\nfloat heightMod = smoothstep(ssrHeightFadeEnd, ssrHeightFadeStart, -viewPosition.z);\nreflectionHit = clamp(1.0 - (1.3 * dCoords.y), 0.0, 1.0) * heightMod * fadeFactor;\nreflectionHitDiffused = waterDiffusion * reflectionHit;\nreflectedColor = linearizeGamma(texture2D(lastFrameColorTexture, reprojectedCoordinate).xyz) *\nreflectionHitDiffused * fresnelModifier.y * ssrIntensity;\n}\nfloat seaColorMod =  mix(waterSeaColorMod, waterSeaColorMod * 0.5, reflectionHitDiffused);\nvec3 waterRenderedColor = tonemapACES((1.0 - reflectionHitDiffused) * reflSky + reflectedColor +\nreflSea * seaColorMod + specular + foam);`)):f.code.add(l`vec3 waterRenderedColor = tonemapACES(reflSky + reflSea * waterSeaColorMod + specular + foam);`),s.hasCloudsReflections?s.hasScreenSpaceReflections?f.code.add(l`return waterRenderedColor * (1.0 - (1.0 - reflectionHit) * cloudsColor.a) + (1.0 - reflectionHit) * cloudsColor.xyz;\n}`):f.code.add(l`return waterRenderedColor * (1.0 - cloudsColor.a) + cloudsColor.xyz;\n}`):f.code.add(l`return waterRenderedColor;\n}`)}export{c as Water};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ForwardLinearDepth as e}from\"../views/3d/webgl-engine/core/shaderLibrary/ForwardLinearDepth.glsl.js\";import{ShaderOutput as r}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as i}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{Transform as o}from\"../views/3d/webgl-engine/core/shaderLibrary/Transform.glsl.js\";import{OutputHighlight as a}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputHighlight.glsl.js\";import{EvaluateAmbientLighting as s}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/EvaluateAmbientLighting.glsl.js\";import{MainLighting as n,addMainLightDirection as t,addMainLightIntensity as l}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MainLighting.glsl.js\";import{multipassTerrainTest as d}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{NormalUtils as g}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/NormalUtils.glsl.js\";import{PBRMode as v}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/PhysicallyBasedRenderingParameters.glsl.js\";import{ReadShadowMapDraw as m}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/ReadShadowMap.glsl.js\";import{Water as c}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/Water.glsl.js\";import{WaterDistortion as p}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/WaterDistortion.glsl.js\";import{symbolAlphaCutoff as u}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{ColorConversion as w}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ColorConversion.glsl.js\";import{addProjViewLocalOrigin as h,addCameraPosition as f}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float4PassUniform as b}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{FloatPassUniform as y}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as j}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as C}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{TransparencyPassType as F}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";import{VertexAttribute as L}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";function P(P){const _=new C,{vertex:M,fragment:S}=_;h(M,P),_.include(o,P),_.attributes.add(L.POSITION,\"vec3\"),_.attributes.add(L.UV0,\"vec2\");const x=new b(\"waterColor\",(e=>e.color));if(P.output===r.Color&&P.isDraped)return _.varyings.add(\"vpos\",\"vec3\"),M.uniforms.add(x),M.code.add(j`\n        void main(void) {\n          if (waterColor.a < ${j.float(u)}) {\n            // Discard this vertex\n            gl_Position = vec4(1e38, 1e38, 1e38, 1.0);\n            return;\n          }\n\n          vpos = position;\n          gl_Position = transformPosition(proj, view, vpos);\n        }\n    `),S.uniforms.add(x),S.code.add(j`void main() {\ngl_FragColor = waterColor;\n}`),_;switch(P.output!==r.Color&&P.output!==r.Alpha||(_.include(g,P),_.include(e,P),_.varyings.add(\"vuv\",\"vec2\"),_.varyings.add(\"vpos\",\"vec3\"),_.varyings.add(\"vnormal\",\"vec3\"),_.varyings.add(\"vtbnMatrix\",\"mat3\"),P.hasMultipassTerrain&&_.varyings.add(\"depth\",\"float\"),M.uniforms.add(x),M.code.add(j`\n      void main(void) {\n        if (waterColor.a < ${j.float(u)}) {\n          // Discard this vertex\n          gl_Position = vec4(1e38, 1e38, 1e38, 1.0);\n          return;\n        }\n\n        vuv = uv0;\n        vpos = position;\n\n        vnormal = getLocalUp(vpos, localOrigin);\n        vtbnMatrix = getTBNMatrix(vnormal);\n\n        ${P.hasMultipassTerrain?\"depth = (view * vec4(vpos, 1.0)).z;\":\"\"}\n\n        gl_Position = transformPosition(proj, view, vpos);\n        ${P.output===r.Color?\"forwardLinearDepth();\":\"\"}\n      }\n    `)),_.include(d,P),P.output){case r.Alpha:_.include(i,P),S.uniforms.add(x),S.code.add(j`\n        void main() {\n          discardBySlice(vpos);\n          ${P.hasMultipassTerrain?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n\n          gl_FragColor = vec4(waterColor.a);\n        }\n      `);break;case r.Color:_.include(n,P),_.include(s,{pbrMode:v.Disabled,lightingSphericalHarmonicsOrder:2}),_.include(p),_.include(i,P),_.include(m,P),_.include(c,P),S.uniforms.add([x,new y(\"timeElapsed\",(e=>e.timeElapsed)),M.uniforms.get(\"view\"),M.uniforms.get(\"localOrigin\")]),f(S,P),S.include(w),t(S),l(S),S.code.add(j`\n      void main() {\n        discardBySlice(vpos);\n        ${P.hasMultipassTerrain?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n        vec3 localUp = vnormal;\n        // the created normal is in tangent space\n        vec4 tangentNormalFoam = getSurfaceNormalAndFoam(vuv, timeElapsed);\n\n        // we rotate the normal according to the tangent-bitangent-normal-Matrix\n        vec3 n = normalize(vtbnMatrix * tangentNormalFoam.xyz);\n        vec3 v = -normalize(vpos - cameraPosition);\n        float shadow = ${P.receiveShadows?j`1.0 - readShadowMap(vpos, linearDepth)`:\"1.0\"};\n        vec4 vPosView = view * vec4(vpos, 1.0);\n        vec4 final = vec4(getSeaColor(n, v, mainLightDirection, waterColor.rgb, mainLightIntensity, localUp, shadow, tangentNormalFoam.w, vPosView.xyz, vpos + localOrigin), waterColor.w);\n\n        // gamma correction\n        gl_FragColor = delinearizeGamma(final);\n        gl_FragColor = highlightSlice(gl_FragColor, vpos);\n        ${P.transparencyPassType===F.Color?\"gl_FragColor = premultiplyAlpha(gl_FragColor);\":\"\"}\n      }\n    `);break;case r.Normal:_.include(g,P),_.include(p,P),_.include(i,P),_.varyings.add(\"vpos\",\"vec3\"),_.varyings.add(\"vuv\",\"vec2\"),M.uniforms.add(x),M.code.add(j`\n        void main(void) {\n          if (waterColor.a < ${j.float(u)}) {\n            // Discard this vertex\n            gl_Position = vec4(1e38, 1e38, 1e38, 1.0);\n            return;\n          }\n\n          vuv = uv0;\n          vpos = position;\n\n          gl_Position = transformPosition(proj, view, vpos);\n        }\n    `),S.uniforms.add(new y(\"timeElapsed\",(e=>e.timeElapsed))),S.code.add(j`void main() {\ndiscardBySlice(vpos);\nvec4 tangentNormalFoam = getSurfaceNormalAndFoam(vuv, timeElapsed);\ntangentNormalFoam.xyz = normalize(tangentNormalFoam.xyz);\ngl_FragColor = vec4((tangentNormalFoam.xyz + vec3(1.0)) * 0.5, tangentNormalFoam.w);\n}`);break;case r.Highlight:_.include(a,P),_.varyings.add(\"vpos\",\"vec3\"),M.uniforms.add(x),M.code.add(j`\n      void main(void) {\n        if (waterColor.a < ${j.float(u)}) {\n          // Discard this vertex\n          gl_Position = vec4(1e38, 1e38, 1e38, 1.0);\n          return;\n        }\n\n        vpos = position;\n        gl_Position = transformPosition(proj, view, vpos);\n      }\n    `),_.include(i,P),S.code.add(j`void main() {\ndiscardBySlice(vpos);\noutputHighlight();\n}`)}return _}const _=Object.freeze(Object.defineProperty({__proto__:null,build:P},Symbol.toStringTag,{value:\"Module\"}));export{_ as W,P as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIyD,SAASA,GAAEA,IAAE;AAAC,EAAAA,GAAE,SAAS,KAAK,IAAI;AAAA;AAAA;AAAA,EAGzF;AAAC;AAAC,SAASC,GAAED,IAAE;AAAC,EAAAA,GAAE,SAAS,KAAK,IAAI;AAAA;AAAA,EAEpC;AAAC;;;ACLukB,SAASE,GAAEC,IAAE;AAAC,EAAAA,GAAE,SAAS,SAAS,IAAI,IAAI,EAAE,iBAAiB,CAAAC,OAAGA,GAAE,UAAW,CAAC,GAAED,GAAE,SAAS,SAAS,IAAI,IAAI,EAAE,uBAAuB,CAAAC,OAAGA,GAAE,eAAgB,CAAC,GAAED,GAAE,SAAS,SAAS,IAAI,CAAC,IAAIC,GAAE,cAAc,CAAAA,OAAGC,GAAEC,IAAEF,GAAE,cAAaA,GAAE,mBAAkBA,GAAE,cAAaA,GAAE,UAAU,CAAE,GAAE,IAAIA,GAAE,iBAAiB,CAAAD,OAAGE,GAAEE,IAAEJ,GAAE,cAAc,CAAC,IAAEA,GAAE,cAAaA,GAAE,cAAc,CAAC,IAAEA,GAAE,YAAY,CAAE,CAAC,CAAC,GAAEA,GAAE,QAAQK,EAAC,GAAEL,GAAE,SAAS,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiDn+B;AAAC;AAAoB,IAAMM,KAAEC,GAAE;AAAV,IAAYC,KAAED,GAAE;;;ACjDkB,SAASE,GAAEA,IAAE;AAAC,EAAAA,GAAE,SAAS,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzF;AAAC;;;ACP4e,SAASC,GAAEC,IAAED,IAAE;AAAC,QAAME,KAAED,GAAE;AAAS,EAAAC,GAAE,QAAQC,EAAC,GAAED,GAAE,SAAS,IAAI,IAAID,GAAE,WAAW,CAACA,IAAEG,OAAIA,GAAE,OAAO,OAAQ,CAAC,GAAEF,GAAE,SAAS,IAAI,IAAI,EAAE,YAAY,CAACD,IAAEG,OAAIA,GAAE,kBAAmB,CAAC,GAAEF,GAAE,SAAS,IAAI,IAAID,GAAE,QAAQ,CAACA,IAAEG,OAAIA,GAAE,IAAI,OAAO,gBAAiB,CAAC,GAAEF,GAAE,SAAS,IAAI,IAAIE,GAAE,uBAAuB,CAACH,IAAEG,OAAI,IAAEA,GAAE,IAAI,OAAO,MAAO,CAAC,GAAEF,GAAE,SAAS,IAAI,IAAID,GAAE,sBAAsB,CAACA,IAAEG,OAAIA,GAAE,IAAI,kBAAmB,CAAC,GAAEF,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAS32BF,GAAE,gBAAc,QAAM,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAkGhD;AAAC;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,UAAQ,OAAG,KAAK,aAAW,GAAE,KAAK,qBAAmB,EAAE;AAAA,EAAC;AAAC;;;AC3GzC,IAAI;AAAJ,IAAM;AAAgF,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,YAAU,CAAC,IAAE,aAAY,EAAE,EAAE,qBAAmB,CAAC,IAAE,sBAAqB,EAAE,EAAE,0BAAwB,CAAC,IAAE,2BAA0B,EAAE,EAAE,kBAAgB,CAAC,IAAE,mBAAkB,EAAE,EAAE,WAAS,CAAC,IAAE;AAAU,EAAE,MAAI,IAAE,CAAC,EAAE,GAAE,SAAS,GAAE;AAAC,IAAE,EAAE,KAAG,CAAC,IAAE,MAAK,EAAE,EAAE,KAAG,CAAC,IAAE;AAAI,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACA3O,IAAMG,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,eAAa,EAAE,IAAG,KAAK,iBAAe,EAAE,UAAS,KAAK,YAAU,GAAE,KAAK,sBAAoB,GAAE,KAAK,0BAAwB,EAAE,GAAE,KAAK,wBAAsB,MAAG,KAAK,WAAS,IAAIC,MAAE,KAAK,cAAY,IAAIA,MAAE,KAAK,YAAU,EAAC,SAAQ,OAAG,QAAO,GAAE,yBAAwB,IAAE,GAAE,KAAK,YAAU,EAAC,OAAMC,GAAE,UAAS,QAAO,GAAE,yBAAwB,IAAE,GAAE,KAAK,SAAO,EAAC,OAAMC,GAAE,UAAS,QAAO,GAAE,yBAAwB,EAAC,GAAE,KAAK,kBAAgB,EAAC,OAAMC,GAAE,UAAS,QAAO,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,UAAU,UAAQF,GAAE,YAAU,KAAK,UAAU,UAAQA,GAAE,WAAS,KAAK,OAAO,UAAQC,GAAE,WAAS,KAAK,gBAAgB,UAAQC,GAAE,YAAU,KAAK,mBAAiB,EAAE;AAAA,EAAuB;AAAC;AAAC,IAAID;AAAJ,IAAMD;AAAN,IAAQE;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,gBAAc,CAAC,IAAE,iBAAgBA,GAAEA,GAAE,UAAQ,CAAC,IAAE;AAAS,EAAEF,OAAIA,KAAE,CAAC,EAAE,GAAE,SAASE,IAAE;AAAC,EAAAA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,UAAQ,CAAC,IAAE;AAAS,EAAEH,OAAIA,KAAE,CAAC,EAAE,GAAE,SAASG,IAAE;AAAC,EAAAA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,cAAY,CAAC,IAAE;AAAa,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAMH,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,oBAAkB,EAAE,GAAE,KAAK,eAAa,KAAI,KAAK,kCAAgC,GAAE,KAAK,YAAU,EAAE;AAAA,EAAC;AAAC;;;ACA/sC,IAAMK,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAED,IAAE;AAAC,UAAMC,IAAE,eAAcC,GAAE,MAAM,CAACC,IAAEC,IAAEC,OAAIF,GAAE,YAAYF,IAAED,GAAEI,IAAEC,EAAC,CAAC,CAAE;AAAA,EAAC;AAAC;;;ACAmuB,SAASC,GAAEA,IAAE;AAAC,QAAM,IAAEA,GAAE;AAAS,IAAE,SAAS,IAAI,CAAC,IAAIC,GAAE,wBAAwB,CAACC,IAAEC,OAAIA,GAAE,WAAW,SAAS,SAAU,GAAE,IAAIF,GAAE,iCAAiC,CAACC,IAAEC,OAAIA,GAAE,WAAW,YAAY,SAAU,GAAE,IAAIF,GAAE,kBAAkB,CAACC,IAAEC,OAAIA,GAAE,WAAW,SAAS,iBAAkB,GAAE,IAAIF,GAAE,2BAA2B,CAACC,IAAEC,OAAIA,GAAE,WAAW,YAAY,iBAAkB,GAAE,IAAID,GAAE,gBAAgB,CAACA,IAAEC,OAAIA,GAAE,WAAW,SAAS,YAAa,GAAE,IAAID,GAAE,mCAAmC,CAACA,IAAEC,OAAIA,GAAE,WAAW,SAAS,+BAAgC,GAAE,IAAID,GAAE,kBAAkB,CAACA,IAAEC,OAAIA,GAAE,WAAW,UAAU,UAAQC,GAAE,WAASD,GAAE,WAAW,gBAAgB,SAAO,IAAEA,GAAE,WAAW,OAAO,SAAOA,GAAE,WAAW,gBAAgB,SAAO,IAAEA,GAAE,WAAW,UAAU,MAAO,GAAE,IAAID,GAAE,yBAAyB,CAACC,IAAEE,OAAI,EAAEA,GAAE,WAAW,UAAU,QAAO,GAAE,CAAC,CAAE,GAAE,IAAIC,GAAE,WAAW,CAACJ,IAAEG,OAAI,EAAEA,GAAE,WAAW,IAAI,KAAG,EAAEA,GAAE,WAAW,KAAK,OAAO,IAAEA,GAAE,WAAW,KAAK,QAAQ,eAAa,IAAK,GAAE,IAAIC,GAAE,aAAa,CAACJ,IAAEC,OAAIA,GAAE,WAAW,UAAU,OAAQ,GAAE,IAAIG,GAAE,kBAAkB,CAACJ,IAAEC,OAAIA,GAAE,WAAW,iBAAe,EAAE,EAAG,GAAE,IAAIG,GAAE,uBAAuB,CAACJ,IAAEC,OAAIA,GAAE,WAAW,mBAAiB,EAAE,uBAAwB,CAAC,CAAC,GAAE,EAAE,UAAU,IAAI,gBAAe,SAAQ,EAAE,MAAM,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUxmE,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,EAIb,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAGb,GAAED,GAAE,CAAC,GAAEC,GAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBvB,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcb,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWb,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBb,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAGb;AAAC;;;ACnF0mB,SAASI,GAAEA,IAAEC,IAAE;AAAC,EAAAD,GAAE,QAAQE,IAAED,EAAC,GAAED,GAAE,QAAQG,EAAC,GAAEH,GAAE,QAAQI,EAAC,GAAEH,GAAE,wBAAsBD,GAAE,QAAQK,IAAEJ,EAAC,GAAEA,GAAE,6BAA2BD,GAAE,QAAQI,IAAEH,EAAC;AAAE,QAAMK,KAAEN,GAAE;AAAS,EAAAM,GAAE,UAAU,IAAI,cAAa,QAAO,CAAC,MAAI,GAAE,EAAE,CAAC,EAAE,IAAI,mBAAkB,QAAO,CAAC,MAAI,GAAE,CAAC,EAAE,IAAI,aAAY,SAAQ,KAAI,EAAE,IAAI,yBAAwB,SAAQ,GAAG,EAAE,IAAI,gBAAe,SAAQ,IAAG,EAAE,IAAI,sBAAqB,SAAQ,GAAG,EAAE,IAAI,oBAAmB,SAAQ,GAAG,EAAE,IAAI,kBAAiB,SAAQ,IAAG,EAAE,IAAI,oBAAmB,SAAQ,GAAE,EAAE,IAAI,gCAA+B,SAAQ,GAAE,EAAE,IAAI,iBAAgB,QAAO,CAAC,MAAI,MAAI,GAAE,CAAC,EAAE,IAAI,YAAW,QAAO,CAAC,MAAI,MAAI,GAAE,CAAC,EAAE,IAAI,wBAAuB,QAAO,CAAC,KAAI,IAAG,CAAC,GAAEA,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,EAI3wC,GAAEA,GAAE,SAAS,IAAI,CAAC,IAAIC,GAAE,4BAA4B,CAACJ,IAAEI,OAAIA,GAAE,SAAS,UAAU,gBAAiB,GAAE,IAAIA,GAAE,+BAA+B,CAACJ,IAAEI,OAAIA,GAAE,SAAS,UAAU,mBAAoB,CAAC,CAAC,GAAED,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qEAkCpI,GAAEL,GAAE,wBAAsBK,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA,+IAGqC,GAAEL,GAAE,6BAA2BK,GAAE,SAAS,IAAI,CAAC,IAAIH,GAAE,QAAQ,CAACA,IAAEI,OAAIA,GAAE,IAAI,OAAO,UAAW,GAAE,IAAI,EAAE,yBAAyB,CAACJ,IAAEI,OAAIA,GAAE,IAAI,qBAAsB,GAAE,IAAIA,GAAE,cAAc,CAACJ,IAAEI,OAAIA,GAAE,IAAI,UAAW,CAAC,CAAC,GAAED,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0CAoBlU,KAAGA,GAAE,KAAK,IAAI,iGAAiG,GAAEL,GAAE,uBAAqBA,GAAE,4BAA0BK,GAAE,KAAK,IAAI;AAAA,EACvN,IAAEA,GAAE,KAAK,IAAI;AAAA,EACb,IAAEA,GAAE,KAAK,IAAI;AAAA,EACb;AAAC;;;AChE2vE,SAAS,EAAEE,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAE,EAAC,QAAO,GAAE,UAAS,EAAC,IAAED;AAAE,IAAE,GAAED,EAAC,GAAEC,GAAE,QAAQE,IAAEH,EAAC,GAAEC,GAAE,WAAW,IAAI,EAAE,UAAS,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,KAAI,MAAM;AAAE,QAAM,IAAE,IAAIG,GAAE,cAAc,CAAAA,OAAGA,GAAE,KAAM;AAAE,MAAGJ,GAAE,WAAS,EAAE,SAAOA,GAAE,SAAS,QAAOC,GAAE,SAAS,IAAI,QAAO,MAAM,GAAE,EAAE,SAAS,IAAI,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,+BAEz/E,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KASpC,GAAE,EAAE,SAAS,IAAI,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,EAElC,GAAEA;AAAE,UAAOD,GAAE,WAAS,EAAE,SAAOA,GAAE,WAAS,EAAE,UAAQC,GAAE,QAAQE,IAAEH,EAAC,GAAEC,GAAE,QAAQI,IAAEL,EAAC,GAAEC,GAAE,SAAS,IAAI,OAAM,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAEA,GAAE,SAAS,IAAI,WAAU,MAAM,GAAEA,GAAE,SAAS,IAAI,cAAa,MAAM,GAAED,GAAE,uBAAqBC,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAE,EAAE,SAAS,IAAI,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,6BAE3Q,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAY7BD,GAAE,sBAAoB,wCAAsC,EAAE;AAAA;AAAA;AAAA,UAG9DA,GAAE,WAAS,EAAE,QAAM,0BAAwB,EAAE;AAAA;AAAA,KAElD,IAAGC,GAAE,QAAQK,IAAEN,EAAC,GAAEA,GAAE,QAAO;AAAA,IAAC,KAAK,EAAE;AAAM,MAAAC,GAAE,QAAQ,GAAED,EAAC,GAAE,EAAE,SAAS,IAAI,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,YAG9EA,GAAE,sBAAoB,2CAAyC,EAAE;AAAA;AAAA;AAAA;AAAA,OAItE;AAAE;AAAA,IAAM,KAAK,EAAE;AAAM,MAAAC,GAAE,QAAQE,IAAEH,EAAC,GAAEC,GAAE,QAAQ,GAAE,EAAC,SAAQ,EAAE,UAAS,iCAAgC,EAAC,CAAC,GAAEA,GAAE,QAAQM,EAAC,GAAEN,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQO,IAAER,EAAC,GAAEC,GAAE,QAAQQ,IAAET,EAAC,GAAE,EAAE,SAAS,IAAI,CAAC,GAAE,IAAIE,GAAE,eAAe,CAAAE,OAAGA,GAAE,WAAY,GAAE,EAAE,SAAS,IAAI,MAAM,GAAE,EAAE,SAAS,IAAI,aAAa,CAAC,CAAC,GAAE,EAAE,GAAEJ,EAAC,GAAE,EAAE,QAAQI,EAAC,GAAEF,GAAE,CAAC,GAAEQ,GAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,UAGzTV,GAAE,sBAAoB,2CAAyC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAQlDA,GAAE,iBAAe,4CAA0C,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAO/EA,GAAE,yBAAuBE,GAAE,QAAM,mDAAiD,EAAE;AAAA;AAAA,KAEzF;AAAE;AAAA,IAAM,KAAK,EAAE;AAAO,MAAAD,GAAE,QAAQE,IAAEH,EAAC,GAAEC,GAAE,QAAQM,IAAEP,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,SAAS,IAAI,QAAO,MAAM,GAAEA,GAAE,SAAS,IAAI,OAAM,MAAM,GAAE,EAAE,SAAS,IAAI,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,+BAEjI,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAWpC,GAAE,EAAE,SAAS,IAAI,IAAIC,GAAE,eAAe,CAAAE,OAAGA,GAAE,WAAY,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxE;AAAE;AAAA,IAAM,KAAK,EAAE;AAAU,MAAAH,GAAE,QAAQS,IAAEV,EAAC,GAAEC,GAAE,SAAS,IAAI,QAAO,MAAM,GAAE,EAAE,SAAS,IAAI,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,6BAExE,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KASlC,GAAEA,GAAE,QAAQ,GAAED,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAG/B;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,IAAM,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAM,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["o", "n", "i", "t", "e", "r", "f", "c", "o", "f", "n", "c", "e", "n", "e", "c", "a", "o", "i", "o", "I", "r", "n", "t", "s", "e", "a", "r", "o", "t", "v", "e", "o", "a", "I", "t", "s", "c", "s", "r", "e", "n", "v", "f", "o", "P", "_", "o", "r", "e", "d", "n", "i", "v", "c", "a"]}