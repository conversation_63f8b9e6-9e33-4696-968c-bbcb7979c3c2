{"version": 3, "sources": ["../../@arcgis/core/portal/PortalFolder.js", "../../@arcgis/core/portal/PortalUser.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import r from\"../core/Error.js\";import{JSONSupport as o}from\"../core/JSONSupport.js\";import{property as e}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as s}from\"../core/accessorSupport/decorators/subclass.js\";let p=class extends o{constructor(t){super(t),this.created=null,this.id=null,this.portal=null,this.title=null,this.username=null}get url(){const t=this.get(\"portal.restUrl\");return t?`${t}/content/users/${this.username}/${this.id}`:null}toJSON(){throw new r(\"internal:not-yet-implemented\",\"PortalFolder.toJSON is not yet implemented\")}};t([e({type:Date})],p.prototype,\"created\",void 0),t([e()],p.prototype,\"id\",void 0),t([e()],p.prototype,\"portal\",void 0),t([e()],p.prototype,\"title\",void 0),t([e({readOnly:!0})],p.prototype,\"url\",null),t([e()],p.prototype,\"username\",void 0),p=t([s(\"esri.portal.PortalFolder\")],p);const l=p;export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import r from\"../core/Error.js\";import{JSONSupport as e}from\"../core/JSONSupport.js\";import{property as o}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as l}from\"../core/accessorSupport/decorators/subclass.js\";import s from\"./PortalFolder.js\";import n from\"./PortalGroup.js\";var i;let u=i=class extends e{constructor(...t){super(...t),this.access=null,this.created=null,this.culture=null,this.description=null,this.email=null,this.fullName=null,this.modified=null,this.orgId=null,this.portal=null,this.preferredView=null,this.privileges=null,this.region=null,this.role=null,this.roleId=null,this.sourceJSON=null,this.units=null,this.username=null,this.userType=null}get thumbnailUrl(){const t=this.url,r=this.thumbnail;return t&&r?this.portal.normalizeUrl(`${t}/info/${r}?f=json`):null}get userContentUrl(){const t=this.get(\"portal.restUrl\");return t?`${t}/content/users/${this.username}`:null}get url(){const t=this.get(\"portal.restUrl\");return t?`${t}/community/users/${this.username}`:null}addItem(t){const r=t&&t.item,e=t&&t.data,o=t&&t.folder,l={method:\"post\"};r&&(l.query=r.createPostQuery(),null!=e&&(\"string\"==typeof e?l.query.text=e:\"object\"==typeof e&&(l.query.text=JSON.stringify(e))));let s=this.userContentUrl;return o&&(s+=\"/\"+(\"string\"==typeof o?o:o.id)),this.portal.request(s+\"/addItem\",l).then((t=>(r.id=t.id,r.portal=this.portal,r.loaded?r.reload():r.load())))}deleteItem(t){let r=this.userContentUrl;return t.ownerFolder&&(r+=\"/\"+t.ownerFolder),this.portal.request(r+`/items/${t.id}/delete`,{method:\"post\"}).then((()=>{t.id=null,t.portal=null}))}deleteItems(t){const r=this.userContentUrl+\"/deleteItems\",e=t.map((t=>t.id));if(e.length){const o={method:\"post\",query:{items:e.join(\",\")}};return this.portal.request(r,o).then((()=>{t.forEach((t=>{t.id=null,t.portal=null}))}))}return Promise.resolve(void 0)}fetchFolders(){const t={query:{num:1}};return this.portal.request(this.userContentUrl??\"\",t).then((t=>{let r;return r=t&&t.folders?t.folders.map((t=>{const r=s.fromJSON(t);return r.portal=this.portal,r})):[],r}))}fetchGroups(){return this.portal.request(this.url??\"\").then((t=>{let r;return r=t&&t.groups?t.groups.map((t=>{const r=n.fromJSON(t);return r.portal=this.portal,r})):[],r}))}fetchItems(t){const r=t??{};let e,o=this.userContentUrl??\"\";return r.folder&&(o+=\"/\"+r.folder.id),import(\"./PortalItem.js\").then((({default:t})=>{e=t;const l={folders:!1,num:r.num||10,start:r.start||1,sortField:r.sortField||\"created\",sortOrder:r.sortOrder||\"asc\"};return this.portal.request(o,{query:l})})).then((t=>{let r;return t&&t.items?(r=t.items.map((t=>{const r=e.fromJSON(t);return r.portal=this.portal,r})),Promise.all(r.map((t=>t.load()))).catch((t=>t)).then((()=>({items:r,nextStart:t.nextStart,total:t.total})))):{items:[],nextStart:-1,total:0}}))}fetchTags(){return this.portal.request(this.url+\"/tags\").then((t=>t.tags))}getThumbnailUrl(t){let r=this.thumbnailUrl;return r&&t&&(r+=`&w=${t}`),r}queryFavorites(t){return this.favGroupId?(this._favGroup||(this._favGroup=new n({id:this.favGroupId,portal:this.portal})),this._favGroup.queryItems(t)):Promise.reject(new r(\"internal:unknown\",\"Unknown internal error\",{internalError:\"Unknown favGroupId\"}))}toJSON(){throw new r(\"internal:not-yet-implemented\",\"PortalGroup.toJSON is not yet implemented\")}static fromJSON(t){if(!t)return null;if(t.declaredClass)throw new Error(\"JSON object is already hydrated\");const r=new i;return r.sourceJSON=t,r.read(t),r}};t([o()],u.prototype,\"access\",void 0),t([o({type:Date})],u.prototype,\"created\",void 0),t([o()],u.prototype,\"culture\",void 0),t([o()],u.prototype,\"description\",void 0),t([o()],u.prototype,\"email\",void 0),t([o()],u.prototype,\"favGroupId\",void 0),t([o()],u.prototype,\"fullName\",void 0),t([o({type:Date})],u.prototype,\"modified\",void 0),t([o()],u.prototype,\"orgId\",void 0),t([o()],u.prototype,\"portal\",void 0),t([o()],u.prototype,\"preferredView\",void 0),t([o()],u.prototype,\"privileges\",void 0),t([o()],u.prototype,\"region\",void 0),t([o()],u.prototype,\"role\",void 0),t([o()],u.prototype,\"roleId\",void 0),t([o()],u.prototype,\"sourceJSON\",void 0),t([o()],u.prototype,\"thumbnail\",void 0),t([o({readOnly:!0})],u.prototype,\"thumbnailUrl\",null),t([o()],u.prototype,\"units\",void 0),t([o({readOnly:!0})],u.prototype,\"userContentUrl\",null),t([o({readOnly:!0})],u.prototype,\"url\",null),t([o()],u.prototype,\"username\",void 0),t([o()],u.prototype,\"userType\",void 0),u=i=t([l(\"esri.portal.PortalUser\")],u);const p=u;export{p as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAIgW,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,UAAQ,MAAK,KAAK,KAAG,MAAK,KAAK,SAAO,MAAK,KAAK,QAAM,MAAK,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,IAAI,MAAK;AAAC,UAAM,IAAE,KAAK,IAAI,gBAAgB;AAAE,WAAO,IAAE,GAAG,CAAC,kBAAkB,KAAK,QAAQ,IAAI,KAAK,EAAE,KAAG;AAAA,EAAI;AAAA,EAAC,SAAQ;AAAC,UAAM,IAAI,EAAE,gCAA+B,4CAA4C;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,KAAI,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,0BAA0B,CAAC,GAAEA,EAAC;AAAE,IAAMC,KAAED;;;ACA9iB,IAAI;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,eAAe,GAAE;AAAC,UAAM,GAAG,CAAC,GAAE,KAAK,SAAO,MAAK,KAAK,UAAQ,MAAK,KAAK,UAAQ,MAAK,KAAK,cAAY,MAAK,KAAK,QAAM,MAAK,KAAK,WAAS,MAAK,KAAK,WAAS,MAAK,KAAK,QAAM,MAAK,KAAK,SAAO,MAAK,KAAK,gBAAc,MAAK,KAAK,aAAW,MAAK,KAAK,SAAO,MAAK,KAAK,OAAK,MAAK,KAAK,SAAO,MAAK,KAAK,aAAW,MAAK,KAAK,QAAM,MAAK,KAAK,WAAS,MAAK,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,IAAI,eAAc;AAAC,UAAM,IAAE,KAAK,KAAI,IAAE,KAAK;AAAU,WAAO,KAAG,IAAE,KAAK,OAAO,aAAa,GAAG,CAAC,SAAS,CAAC,SAAS,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,iBAAgB;AAAC,UAAM,IAAE,KAAK,IAAI,gBAAgB;AAAE,WAAO,IAAE,GAAG,CAAC,kBAAkB,KAAK,QAAQ,KAAG;AAAA,EAAI;AAAA,EAAC,IAAI,MAAK;AAAC,UAAM,IAAE,KAAK,IAAI,gBAAgB;AAAE,WAAO,IAAE,GAAG,CAAC,oBAAoB,KAAK,QAAQ,KAAG;AAAA,EAAI;AAAA,EAAC,QAAQ,GAAE;AAAC,UAAM,IAAE,KAAG,EAAE,MAAKE,KAAE,KAAG,EAAE,MAAK,IAAE,KAAG,EAAE,QAAOC,KAAE,EAAC,QAAO,OAAM;AAAE,UAAIA,GAAE,QAAM,EAAE,gBAAgB,GAAE,QAAMD,OAAI,YAAU,OAAOA,KAAEC,GAAE,MAAM,OAAKD,KAAE,YAAU,OAAOA,OAAIC,GAAE,MAAM,OAAK,KAAK,UAAUD,EAAC;AAAK,QAAIE,KAAE,KAAK;AAAe,WAAO,MAAIA,MAAG,OAAK,YAAU,OAAO,IAAE,IAAE,EAAE,MAAK,KAAK,OAAO,QAAQA,KAAE,YAAWD,EAAC,EAAE,KAAM,CAAAE,QAAI,EAAE,KAAGA,GAAE,IAAG,EAAE,SAAO,KAAK,QAAO,EAAE,SAAO,EAAE,OAAO,IAAE,EAAE,KAAK,EAAG;AAAA,EAAC;AAAA,EAAC,WAAW,GAAE;AAAC,QAAI,IAAE,KAAK;AAAe,WAAO,EAAE,gBAAc,KAAG,MAAI,EAAE,cAAa,KAAK,OAAO,QAAQ,IAAE,UAAU,EAAE,EAAE,WAAU,EAAC,QAAO,OAAM,CAAC,EAAE,KAAM,MAAI;AAAC,QAAE,KAAG,MAAK,EAAE,SAAO;AAAA,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,IAAE,KAAK,iBAAe,gBAAeH,KAAE,EAAE,IAAK,CAAAG,OAAGA,GAAE,EAAG;AAAE,QAAGH,GAAE,QAAO;AAAC,YAAM,IAAE,EAAC,QAAO,QAAO,OAAM,EAAC,OAAMA,GAAE,KAAK,GAAG,EAAC,EAAC;AAAE,aAAO,KAAK,OAAO,QAAQ,GAAE,CAAC,EAAE,KAAM,MAAI;AAAC,UAAE,QAAS,CAAAG,OAAG;AAAC,UAAAA,GAAE,KAAG,MAAKA,GAAE,SAAO;AAAA,QAAI,CAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,WAAO,QAAQ,QAAQ,MAAM;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,UAAM,IAAE,EAAC,OAAM,EAAC,KAAI,EAAC,EAAC;AAAE,WAAO,KAAK,OAAO,QAAQ,KAAK,kBAAgB,IAAG,CAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,UAAI;AAAE,aAAO,IAAEA,MAAGA,GAAE,UAAQA,GAAE,QAAQ,IAAK,CAAAA,OAAG;AAAC,cAAMC,KAAEH,GAAE,SAASE,EAAC;AAAE,eAAOC,GAAE,SAAO,KAAK,QAAOA;AAAA,MAAC,CAAE,IAAE,CAAC,GAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK,OAAO,QAAQ,KAAK,OAAK,EAAE,EAAE,KAAM,OAAG;AAAC,UAAI;AAAE,aAAO,IAAE,KAAG,EAAE,SAAO,EAAE,OAAO,IAAK,CAAAD,OAAG;AAAC,cAAMC,KAAE,EAAE,SAASD,EAAC;AAAE,eAAOC,GAAE,SAAO,KAAK,QAAOA;AAAA,MAAC,CAAE,IAAE,CAAC,GAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,WAAW,GAAE;AAAC,UAAM,IAAE,KAAG,CAAC;AAAE,QAAIJ,IAAE,IAAE,KAAK,kBAAgB;AAAG,WAAO,EAAE,WAAS,KAAG,MAAI,EAAE,OAAO,KAAI,OAAO,0BAAiB,EAAE,KAAM,CAAC,EAAC,SAAQG,GAAC,MAAI;AAAC,MAAAH,KAAEG;AAAE,YAAMF,KAAE,EAAC,SAAQ,OAAG,KAAI,EAAE,OAAK,IAAG,OAAM,EAAE,SAAO,GAAE,WAAU,EAAE,aAAW,WAAU,WAAU,EAAE,aAAW,MAAK;AAAE,aAAO,KAAK,OAAO,QAAQ,GAAE,EAAC,OAAMA,GAAC,CAAC;AAAA,IAAC,CAAE,EAAE,KAAM,CAAAE,OAAG;AAAC,UAAIC;AAAE,aAAOD,MAAGA,GAAE,SAAOC,KAAED,GAAE,MAAM,IAAK,CAAAA,OAAG;AAAC,cAAMC,KAAEJ,GAAE,SAASG,EAAC;AAAE,eAAOC,GAAE,SAAO,KAAK,QAAOA;AAAA,MAAC,CAAE,GAAE,QAAQ,IAAIA,GAAE,IAAK,CAAAD,OAAGA,GAAE,KAAK,CAAE,CAAC,EAAE,MAAO,CAAAA,OAAGA,EAAE,EAAE,KAAM,OAAK,EAAC,OAAMC,IAAE,WAAUD,GAAE,WAAU,OAAMA,GAAE,MAAK,EAAG,KAAG,EAAC,OAAM,CAAC,GAAE,WAAU,IAAG,OAAM,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,OAAO,QAAQ,KAAK,MAAI,OAAO,EAAE,KAAM,OAAG,EAAE,IAAK;AAAA,EAAC;AAAA,EAAC,gBAAgB,GAAE;AAAC,QAAI,IAAE,KAAK;AAAa,WAAO,KAAG,MAAI,KAAG,MAAM,CAAC,KAAI;AAAA,EAAC;AAAA,EAAC,eAAe,GAAE;AAAC,WAAO,KAAK,cAAY,KAAK,cAAY,KAAK,YAAU,IAAI,EAAE,EAAC,IAAG,KAAK,YAAW,QAAO,KAAK,OAAM,CAAC,IAAG,KAAK,UAAU,WAAW,CAAC,KAAG,QAAQ,OAAO,IAAI,EAAE,oBAAmB,0BAAyB,EAAC,eAAc,qBAAoB,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAM,IAAI,EAAE,gCAA+B,2CAA2C;AAAA,EAAC;AAAA,EAAC,OAAO,SAAS,GAAE;AAAC,QAAG,CAAC,EAAE,QAAO;AAAK,QAAG,EAAE,cAAc,OAAM,IAAI,MAAM,iCAAiC;AAAE,UAAM,IAAE,IAAI;AAAE,WAAO,EAAE,aAAW,GAAE,EAAE,KAAK,CAAC,GAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,KAAI,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,KAAI,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,wBAAwB,CAAC,GAAE,CAAC;AAAE,IAAME,KAAE;", "names": ["p", "l", "e", "l", "s", "t", "r", "p"]}