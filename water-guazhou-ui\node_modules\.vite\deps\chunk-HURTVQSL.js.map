{"version": 3, "sources": ["../../@arcgis/core/views/input/keys.js", "../../@arcgis/core/views/input/EventMatch.js", "../../@arcgis/core/views/input/InputHandler.js", "../../@arcgis/core/views/input/handlers/LatestPointer.js", "../../@arcgis/core/views/input/handlers/MultiTouch.js", "../../@arcgis/core/views/input/InputManager.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{eventKey as r}from\"../../core/events.js\";import has from\"../../core/has.js\";const e=has(\"mac\")?\"Meta\":\"Ctrl\",t={8:\"Backspace\",9:\"Tab\",13:\"Enter\",27:\"Escape\",33:\"PageUp\",34:\"PageDown\",35:\"End\",36:\"Home\",37:\"ArrowLeft\",38:\"ArrowUp\",39:\"ArrowRight\",40:\"ArrowDown\",45:\"Insert\",46:\"Delete\"};for(let n=48;n<58;n++)t[n]=String.fromCharCode(n);for(let n=1;n<25;n++)t[111+n]=`F${n}`;for(let n=65;n<91;n++)t[n]=[String.fromCharCode(n+32),String.fromCharCode(n)];function o(e){if(void 0!==e.key)return r(e);const o=t[e.keyCode];return Array.isArray(o)?e.shiftKey?o[1]:o[0]:o}function a(r){switch(r){case\"Ctrl\":case\"Alt\":case\"Shift\":case\"Meta\":case\"Primary\":return!0}return!1}export{o as eventKey,a as isSystemModifier,e as primaryKey};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass e{constructor(e,t=[]){this.eventType=e,this.keyModifiers=t}matches(e){if(e.type!==this.eventType)return!1;if(0===this.keyModifiers.length)return!0;const t=e.modifiers;for(const i of this.keyModifiers)if(!t.has(i))return!1;return!0}}export{e as EventMatch};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Logger.js\";import{EventMatch as t}from\"./EventMatch.js\";const n=e.getLogger(\"esri.views.input.InputHandler\");class i{constructor(e){this._manager=null,this._incoming={},this._outgoing={},this._incomingEventMatches=null,this._incomingEventTypes=null,this._outgoingEventTypes=null,this._hasSideEffects=e}get incomingEventMatches(){if(!this._incomingEventMatches){this._incomingEventMatches=[];for(const e in this._incoming){const t=this._incoming[e];for(const e of t)this._incomingEventMatches.push(e.match)}}return this._incomingEventMatches}get incomingEventTypes(){return this._incomingEventTypes||(this._incomingEventTypes=this.incomingEventMatches.map((e=>e.eventType))),this._incomingEventTypes}get outgoingEventTypes(){return this._outgoingEventTypes||(this._outgoingEventTypes=Object.keys(this._outgoing)),this._outgoingEventTypes}get hasSideEffects(){return this._hasSideEffects}get hasPendingInputs(){return!1}onInstall(e){this._manager?n.error(\"This InputHandler has already been registered with an InputManager\"):(e.setEventCallback((e=>this._handleEvent(e))),e.setUninstallCallback((()=>this._onUninstall())),this._manager=e)}onUninstall(){}registerIncoming(e,n,i){let a;\"function\"==typeof n?(i=n,a=[]):a=n||[];const o=\"string\"==typeof e?new t(e,a):e,h=()=>{this._incomingEventTypes=null,this._incomingEventMatches=null},r=e=>{const t=this._incoming[e.match.eventType];if(t){const n=t.indexOf(e);t.splice(n,1),h(),this._manager&&this._manager.updateDependencies()}},g=new s(o,i,{onPause:r,onRemove:r,onResume:e=>{const t=this._incoming[e.match.eventType];t&&!t.includes(e)&&(t.push(e),h(),this._manager&&this._manager.updateDependencies())}});let c=this._incoming[o.eventType];return c||(c=[],this._incoming[o.eventType]=c),c.push(g),h(),this._manager&&this._manager.updateDependencies(),g}registerOutgoing(e){if(this._outgoing[e])throw new Error(\"There is already a callback registered for this outgoing InputEvent: \"+e);const t=new a(e,{onEmit:(e,t,n,i)=>{this._manager?.emit(e.eventType,t,n,i)},onRemove:e=>{delete this._outgoing[e.eventType],this._manager?.updateDependencies()}});return this._outgoing[e]=t,this._outgoingEventTypes=null,this._manager&&this._manager.updateDependencies(),t}startCapturingPointer(e){this._manager?.setPointerCapture(e,!0)}stopCapturingPointer(e){this._manager?.setPointerCapture(e,!1)}refreshHasPendingInputs(){this._manager?.refreshHasPendingInputs()}_onUninstall(){this._manager?(this.onUninstall(),this._manager=null):n.error(\"This InputHandler is not registered with an InputManager\")}_handleEvent(e){const t=this._incoming[e.type];if(t)for(const n of t)if(n.match.matches(e)&&(n.callback?.(e),e.shouldStopPropagation()))break}}class s{constructor(e,t,n){this.match=e,this._callback=t,this._handler=n}pause(){this._handler.onPause(this)}resume(){this._handler.onResume(this)}remove(){this._handler.onRemove(this)}get callback(){return this._callback}}class a{constructor(e,t){this.eventType=e,this._removed=!1,this._handler=t}emit(e,t,n){this._removed||this._handler.onEmit(this,e,t,n)}remove(){this._removed=!0,this._handler.onRemove(this)}}export{i as InputHandler};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{InputHandler as t}from\"../InputHandler.js\";class s extends t{constructor(t){super(!0),this._onChange=t,this._value=\"mouse\",this._x=null,this._y=null,this.registerIncoming(\"pointer-move\",(t=>{this._update(t.data)}))}_update(t){const s=\"touch\"===t.native.pointerType?\"touch\":\"mouse\",{x:e,y:i}=t;s===this._value&&this._x===e&&this._y===i||(this._value=s,this._x=e,this._y=i,this._onChange(s,e,i))}}export{s as LatestPointer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ObservableValue as t}from\"../../../core/accessorSupport/tracking/ObservableValue.js\";import{InputHandler as e}from\"../InputHandler.js\";class i extends e{get multiTouchActive(){return this._multiTouchActive.get()}constructor(){super(!0),this._activeTouchPointerIds=new Set,this._multiTouchActive=new t(!1),this._onPointerAdd=({data:t})=>{\"touch\"===t.pointerType&&(this._activeTouchPointerIds.add(t.native.pointerId),this._update())},this._onPointerRemove=({data:t})=>{\"touch\"===t.pointerType&&(this._activeTouchPointerIds.delete(t.native.pointerId),this._update())},this.registerIncoming(\"pointer-down\",this._onPointerAdd),this.registerIncoming(\"pointer-up\",this._onPointerRemove),this.registerIncoming(\"pointer-capture-lost\",this._onPointerRemove),this.registerIncoming(\"pointer-cancel\",this._onPointerRemove)}_update(){this._multiTouchActive.set(this._activeTouchPointerIds.size>1)}}export{i as MultiTouch};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../core/Accessor.js\";import r from\"../../core/Logger.js\";import{isNone as i,unwrapOrThrow as n}from\"../../core/maybe.js\";import s from\"../../core/Queue.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as a}from\"../../core/accessorSupport/decorators/subclass.js\";import{PropertiesPool as l}from\"../3d/support/PropertiesPool.js\";import{primaryKey as h,isSystemModifier as p}from\"./keys.js\";import{LatestPointer as d}from\"./handlers/LatestPointer.js\";import{MultiTouch as c}from\"./handlers/MultiTouch.js\";let u=class extends t{constructor(e){super(e),this._pointerCaptures=new Map,this._nameToGroup={},this._handlers=[],this._handlersPriority=[],this._currentPropagation=null,this._updateDependenciesAfterPropagation=!1,this._sourceEvents=new Set,this._keyModifiers=new Set,this._activeKeyModifiers=new Set,this._stoppedPropagationEventIds=new Set,this.primaryKey=h,this._latestPointerType=\"mouse\",this._propertiesPool=new l({latestPointerLocation:f},this),this.latestPointerLocation=null,this.test={timestamp:void 0,hasCurrentPropagation:()=>!!this._currentPropagation}}initialize(){this.eventSource.onEventReceived=this._onEventReceived.bind(this),this._installRecognizers()}destroy(){const e=Object.keys(this._nameToGroup);for(const t of e)this.uninstallHandlers(t);this.eventSource.destroy(),this._currentPropagation=null,this._propertiesPool.destroy()}get hasPendingInputs(){return this._handlers.some((e=>e.handler.hasPendingInputs))}get latestPointerType(){return this._latestPointerType}get multiTouchActive(){return this._multiTouchHandler.multiTouchActive}installHandlers(e,t,i=P.INTERNAL){if(this._nameToGroup[e])return void r.getLogger(this.declaredClass).error(\"There is already an InputHandler group registered under the name `\"+e+\"`\");if(0===t.length)return void r.getLogger(this.declaredClass).error(\"Can't register a group of zero handlers\");const n={name:e,handlers:t.map((e=>({handler:e,active:!0,removed:!1,priorityIndex:0,groupPriority:i,eventCallback:null,uninstallCallback:null})))};this._nameToGroup[e]=n;for(let r=n.handlers.length-1;r>=0;r--){const e=n.handlers[r];this._handlers.push(e),e.handler.onInstall({updateDependencies:()=>{this.updateDependencies()},emit:(t,r,i,n,s)=>{this._emitInputEvent(e.priorityIndex+1,t,r,i,s,n)},setPointerCapture:(t,r)=>{this._setPointerCapture(n,e,t,r)},setEventCallback:t=>{e.eventCallback=t},setUninstallCallback:t=>{e.uninstallCallback=t},refreshHasPendingInputs:()=>{this.notifyChange(\"hasPendingInputs\")}})}this.updateDependencies()}uninstallHandlers(e){const t=this._nameToGroup[e];t?(t.handlers.forEach((e=>{e.removed=!0,e.uninstallCallback?.()})),delete this._nameToGroup[e],this._currentPropagation?this._currentPropagation.needsHandlerGarbageCollect=!0:this._garbageCollectRemovedHandlers()):r.getLogger(this.declaredClass).error(\"There is no InputHandler group registered under the name `\"+e+\"`\")}hasHandlers(e){return void 0!==this._nameToGroup[e]}updateDependencies(){if(this._currentPropagation)return void(this._updateDependenciesAfterPropagation=!0);this._updateDependenciesAfterPropagation=!1;const e=new Set,t=new Set;this._handlersPriority=[];for(let r=this._handlers.length-1;r>=0;r--){const e=this._handlers[r];e.priorityIndex=r,this._handlersPriority.push(e)}this._handlersPriority=this._sortHandlersPriority(this._handlersPriority);for(let r=this._handlersPriority.length-1;r>=0;r--){const i=this._handlersPriority[r];i.priorityIndex=r;let n=i.handler.hasSideEffects;if(!n)for(const t of i.handler.outgoingEventTypes)if(e.has(t)){n=!0;break}if(n)for(const r of i.handler.incomingEventMatches){e.add(r.eventType);for(const e of r.keyModifiers)p(e)||t.add(e)}i.active=n}this._sourceEvents=e,this._keyModifiers=t,this._pointerCaptures.size>0&&this._sourceEvents.add(\"pointer-capture-lost\"),this._keyModifiers.size>0&&(this._sourceEvents.add(\"key-down\"),this._sourceEvents.add(\"key-up\")),this.eventSource&&(this.eventSource.activeEvents=this._sourceEvents)}_setLatestPointer(e,t,r){this._latestPointerType=e;const n=this._get(\"latestPointerLocation\");if(i(n)||n.x!==t||n.y!==r){const e=this._propertiesPool.get(\"latestPointerLocation\");e.x=t,e.y=r,this._set(\"latestPointerLocation\",e)}}_onEventReceived(e,t){if(\"pointer-capture-lost\"===e){const e=t;this._pointerCaptures.delete(e.native.pointerId)}this._updateKeyModifiers(e,t);const r=null!=this.test.timestamp?this.test.timestamp:t.native?t.native.timestamp:void 0,i=t.native?t.native.cancelable:void 0;this._emitInputEventFromSource(e,t,r,i)}_updateKeyModifiers(e,t){if(!t)return;let r=!1;const i=()=>{if(!r){const e=new Set;this._activeKeyModifiers.forEach((t=>{e.add(t)})),this._activeKeyModifiers=e,r=!0}},n=(e,t)=>{t&&!this._activeKeyModifiers.has(e)?(i(),this._activeKeyModifiers.add(e)):!t&&this._activeKeyModifiers.has(e)&&(i(),this._activeKeyModifiers.delete(e))};if(\"key-down\"===e||\"key-up\"===e){const r=t.key;this._keyModifiers.has(r)&&n(r,\"key-down\"===e)}const s=t.native;n(\"Alt\",!(!s||!s.altKey)),n(\"Ctrl\",!(!s||!s.ctrlKey)),n(\"Shift\",!(!s||!s.shiftKey)),n(\"Meta\",!(!s||!s.metaKey)),n(\"Primary\",this._activeKeyModifiers.has(this.primaryKey))}_installRecognizers(){this._latestPointerHandler=new d(((e,t,r)=>this._setLatestPointer(e,t,r))),this._multiTouchHandler=new c,this.installHandlers(\"input-manager-logic\",[this._latestPointerHandler,this._multiTouchHandler],P.ALWAYS),this.recognizers.length>0&&this.installHandlers(\"default\",this.recognizers,P.INTERNAL)}_setPointerCapture(e,t,r,i){const n=e.name+\"-\"+t.priorityIndex,s=this._pointerCaptures.get(r.pointerId)||new Set;this._pointerCaptures.set(r.pointerId,s),i?(s.add(n),1===s.size&&this.eventSource&&this.eventSource.setPointerCapture(r,!0)):s.has(n)&&(s.delete(n),0===s.size&&(this._pointerCaptures.delete(r.pointerId),this.eventSource&&this.eventSource.setPointerCapture(r,!1)))}_garbageCollectRemovedHandlers(){this._handlers=this._handlers.filter((e=>!e.removed)),this.updateDependencies()}_emitInputEventFromSource(e,t,r,i){this._emitInputEvent(0,e,t,r,i)}_emitInputEvent(e,t,r,i,n,s){const o=void 0!==i?i:this._currentPropagation?this._currentPropagation.timestamp:performance.now(),a=void 0!==n&&n,l={event:new _(t,r,o,s||this._activeKeyModifiers,a),priorityIndex:e};this._currentPropagation?this._currentPropagation.events.push(l):this._doNewPropagation(l)}_doNewPropagation(e){this._currentPropagation={events:new s,currentHandler:null,needsHandlerGarbageCollect:!1,timestamp:e.event.timestamp},this._currentPropagation.events.push(e),this._continuePropagation()}_continuePropagation(){const e=n(this._currentPropagation);for(;e.events.length>0;){const{event:t,priorityIndex:r}=e.events.pop(),i=t.data&&t.data.eventId;if(!(null!=i&&this._stoppedPropagationEventIds.has(i)))for(e.currentHandler=this._handlersPriority[r];e.currentHandler;){if(e.currentHandler.removed)e.needsHandlerGarbageCollect=!0;else{if(e.currentHandler.active&&!t.shouldStopPropagation()&&e.currentHandler.eventCallback?.(t),t.shouldStopPropagation()){null!=i&&this._stoppedPropagationEventIds.add(i);break}if(t.shouldPausePropagation((()=>this._continuePropagation())))return void this._pausePropagation({event:t,priorityIndex:e.currentHandler.priorityIndex+1})}e.currentHandler=this._handlersPriority[e.currentHandler.priorityIndex+1]}}e.needsHandlerGarbageCollect&&this._garbageCollectRemovedHandlers(),this.hasPendingInputs||this._stoppedPropagationEventIds.clear(),this._currentPropagation=null,this._updateDependenciesAfterPropagation&&this.updateDependencies()}_pausePropagation(e){const t=new s;t.push(e);const r=this._currentPropagation;if(r){for(;r.events.length;)t.push(r.events.pop());r.events=t,r.currentHandler=null}}_compareHandlerPriority(e,t){if(e.handler.hasSideEffects!==t.handler.hasSideEffects)return e.handler.hasSideEffects?1:-1;if(e.groupPriority!==t.groupPriority)return e.groupPriority>t.groupPriority?-1:1;for(const r of e.handler.incomingEventMatches)for(const e of t.handler.incomingEventMatches){if(r.eventType!==e.eventType)continue;const t=r.keyModifiers.filter((t=>e.keyModifiers.includes(t)));if(t.length===r.keyModifiers.length!==(t.length===e.keyModifiers.length))return r.keyModifiers.length>e.keyModifiers.length?-1:1}return e.priorityIndex>t.priorityIndex?-1:1}_sortHandlersPriority(e){const t=[];for(const r of e){let e=0;for(;e<t.length&&this._compareHandlerPriority(r,t[e])>=0;)e++;t.splice(e,0,r)}return t}get debug(){const e=e=>{const t=this._setPointerCapture;this._setPointerCapture=()=>{},e(),this._setPointerCapture=t};return{injectEvent:(t,r)=>{e((()=>{this._onEventReceived(t,r)}))},disablePointerCapture:e}}};e([o({readOnly:!0})],u.prototype,\"hasPendingInputs\",null),e([o({constructOnly:!0})],u.prototype,\"eventSource\",void 0),e([o({constructOnly:!0})],u.prototype,\"recognizers\",void 0),e([o()],u.prototype,\"_latestPointerType\",void 0),e([o()],u.prototype,\"latestPointerType\",null),e([o()],u.prototype,\"multiTouchActive\",null),e([o({readOnly:!0})],u.prototype,\"latestPointerLocation\",void 0),u=e([a(\"esri.views.input.InputManager\")],u);class _{constructor(e,t,r,i,n){this.type=e,this.data=t,this.timestamp=r,this.modifiers=i,this.cancelable=n,this._propagationState=g.NONE,this._resumeCallback=null}stopPropagation(){this._propagationState|=g.STOPPED}shouldStopPropagation(){return 0!=(this._propagationState&g.STOPPED)}async(e){this._propagationState|=g.PAUSED;const t=(e,t)=>{this._propagationState&=~g.PAUSED;const r=this._resumeCallback;if(this._resumeCallback=null,r&&r(),t)throw e;return e};return(\"function\"==typeof e?e():e).then((e=>t(e,!1)),(e=>t(e,!0)))}shouldPausePropagation(e){return!!(this._propagationState&g.PAUSED)&&(this._resumeCallback=e,!0)}preventDefault(){this.data.native.preventDefault()}}var g;!function(e){e[e.NONE=0]=\"NONE\",e[e.STOPPED=1]=\"STOPPED\",e[e.PAUSED=2]=\"PAUSED\"}(g||(g={}));const P={ALWAYS:1,DEFAULT:0,TOOL:-1,WIDGET:-2,INTERNAL:-3};class v{}const f=v;export{u as InputManager,P as ViewEventPriorities};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAImF,IAAMA,KAAE,IAAI,KAAK,IAAE,SAAO;AAA1B,IAAiCC,KAAE,EAAC,GAAE,aAAY,GAAE,OAAM,IAAG,SAAQ,IAAG,UAAS,IAAG,UAAS,IAAG,YAAW,IAAG,OAAM,IAAG,QAAO,IAAG,aAAY,IAAG,WAAU,IAAG,cAAa,IAAG,aAAY,IAAG,UAAS,IAAG,SAAQ;AAAE,SAAQC,KAAE,IAAGA,KAAE,IAAGA,KAAI,CAAAD,GAAEC,EAAC,IAAE,OAAO,aAAaA,EAAC;AAAE,SAAQA,KAAE,GAAEA,KAAE,IAAGA,KAAI,CAAAD,GAAE,MAAIC,EAAC,IAAE,IAAIA,EAAC;AAAG,SAAQA,KAAE,IAAGA,KAAE,IAAGA,KAAI,CAAAD,GAAEC,EAAC,IAAE,CAAC,OAAO,aAAaA,KAAE,EAAE,GAAE,OAAO,aAAaA,EAAC,CAAC;AAAE,SAASC,GAAEH,IAAE;AAAC,MAAG,WAASA,GAAE,IAAI,QAAO,EAAEA,EAAC;AAAE,QAAMG,KAAEF,GAAED,GAAE,OAAO;AAAE,SAAO,MAAM,QAAQG,EAAC,IAAEH,GAAE,WAASG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA;AAAC;AAAC,SAASC,GAAE,GAAE;AAAC,UAAO,GAAE;AAAA,IAAC,KAAI;AAAA,IAAO,KAAI;AAAA,IAAM,KAAI;AAAA,IAAQ,KAAI;AAAA,IAAO,KAAI;AAAU,aAAM;AAAA,EAAE;AAAC,SAAM;AAAE;;;ACA9pB,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,KAAE,CAAC,GAAE;AAAC,SAAK,YAAUD,IAAE,KAAK,eAAaC;AAAA,EAAC;AAAA,EAAC,QAAQD,IAAE;AAAC,QAAGA,GAAE,SAAO,KAAK,UAAU,QAAM;AAAG,QAAG,MAAI,KAAK,aAAa,OAAO,QAAM;AAAG,UAAMC,KAAED,GAAE;AAAU,eAAUE,MAAK,KAAK,aAAa,KAAG,CAACD,GAAE,IAAIC,EAAC,EAAE,QAAM;AAAG,WAAM;AAAA,EAAE;AAAC;;;ACA5J,IAAM,IAAE,EAAE,UAAU,+BAA+B;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,WAAS,MAAK,KAAK,YAAU,CAAC,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,wBAAsB,MAAK,KAAK,sBAAoB,MAAK,KAAK,sBAAoB,MAAK,KAAK,kBAAgBA;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,QAAG,CAAC,KAAK,uBAAsB;AAAC,WAAK,wBAAsB,CAAC;AAAE,iBAAUA,MAAK,KAAK,WAAU;AAAC,cAAMC,KAAE,KAAK,UAAUD,EAAC;AAAE,mBAAUA,MAAKC,GAAE,MAAK,sBAAsB,KAAKD,GAAE,KAAK;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAqB;AAAA,EAAC,IAAI,qBAAoB;AAAC,WAAO,KAAK,wBAAsB,KAAK,sBAAoB,KAAK,qBAAqB,IAAK,CAAAA,OAAGA,GAAE,SAAU,IAAG,KAAK;AAAA,EAAmB;AAAA,EAAC,IAAI,qBAAoB;AAAC,WAAO,KAAK,wBAAsB,KAAK,sBAAoB,OAAO,KAAK,KAAK,SAAS,IAAG,KAAK;AAAA,EAAmB;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,UAAUA,IAAE;AAAC,SAAK,WAAS,EAAE,MAAM,oEAAoE,KAAGA,GAAE,iBAAkB,CAAAA,OAAG,KAAK,aAAaA,EAAC,CAAE,GAAEA,GAAE,qBAAsB,MAAI,KAAK,aAAa,CAAE,GAAE,KAAK,WAASA;AAAA,EAAE;AAAA,EAAC,cAAa;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEE,IAAEH,IAAE;AAAC,QAAII;AAAE,kBAAY,OAAOD,MAAGH,KAAEG,IAAEC,KAAE,CAAC,KAAGA,KAAED,MAAG,CAAC;AAAE,UAAME,KAAE,YAAU,OAAOJ,KAAE,IAAIA,GAAEA,IAAEG,EAAC,IAAEH,IAAE,IAAE,MAAI;AAAC,WAAK,sBAAoB,MAAK,KAAK,wBAAsB;AAAA,IAAI,GAAE,IAAE,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,UAAUD,GAAE,MAAM,SAAS;AAAE,UAAGC,IAAE;AAAC,cAAMC,KAAED,GAAE,QAAQD,EAAC;AAAE,QAAAC,GAAE,OAAOC,IAAE,CAAC,GAAE,EAAE,GAAE,KAAK,YAAU,KAAK,SAAS,mBAAmB;AAAA,MAAC;AAAA,IAAC,GAAEG,KAAE,IAAIC,GAAEF,IAAEL,IAAE,EAAC,SAAQ,GAAE,UAAS,GAAE,UAAS,CAAAC,OAAG;AAAC,YAAMC,KAAE,KAAK,UAAUD,GAAE,MAAM,SAAS;AAAE,MAAAC,MAAG,CAACA,GAAE,SAASD,EAAC,MAAIC,GAAE,KAAKD,EAAC,GAAE,EAAE,GAAE,KAAK,YAAU,KAAK,SAAS,mBAAmB;AAAA,IAAE,EAAC,CAAC;AAAE,QAAIO,KAAE,KAAK,UAAUH,GAAE,SAAS;AAAE,WAAOG,OAAIA,KAAE,CAAC,GAAE,KAAK,UAAUH,GAAE,SAAS,IAAEG,KAAGA,GAAE,KAAKF,EAAC,GAAE,EAAE,GAAE,KAAK,YAAU,KAAK,SAAS,mBAAmB,GAAEA;AAAA,EAAC;AAAA,EAAC,iBAAiBL,IAAE;AAAC,QAAG,KAAK,UAAUA,EAAC,EAAE,OAAM,IAAI,MAAM,0EAAwEA,EAAC;AAAE,UAAMC,KAAE,IAAIE,GAAEH,IAAE,EAAC,QAAO,CAACA,IAAEC,IAAEC,IAAEH,OAAI;AAJz8D;AAI08D,iBAAK,aAAL,mBAAe,KAAKC,GAAE,WAAUC,IAAEC,IAAEH;AAAA,IAAE,GAAE,UAAS,CAAAC,OAAG;AAJ9/D;AAI+/D,aAAO,KAAK,UAAUA,GAAE,SAAS,IAAE,UAAK,aAAL,mBAAe;AAAA,IAAoB,EAAC,CAAC;AAAE,WAAO,KAAK,UAAUA,EAAC,IAAEC,IAAE,KAAK,sBAAoB,MAAK,KAAK,YAAU,KAAK,SAAS,mBAAmB,GAAEA;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAE;AAJ9sE;AAI+sE,eAAK,aAAL,mBAAe,kBAAkBA,IAAE;AAAA,EAAG;AAAA,EAAC,qBAAqBA,IAAE;AAJ7wE;AAI8wE,eAAK,aAAL,mBAAe,kBAAkBA,IAAE;AAAA,EAAG;AAAA,EAAC,0BAAyB;AAJ90E;AAI+0E,eAAK,aAAL,mBAAe;AAAA,EAAyB;AAAA,EAAC,eAAc;AAAC,SAAK,YAAU,KAAK,YAAY,GAAE,KAAK,WAAS,QAAM,EAAE,MAAM,0DAA0D;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAJhhF;AAIihF,UAAMC,KAAE,KAAK,UAAUD,GAAE,IAAI;AAAE,QAAGC;AAAE,iBAAUC,MAAKD,GAAE,KAAGC,GAAE,MAAM,QAAQF,EAAC,OAAI,KAAAE,GAAE,aAAF,wBAAAA,IAAaF,KAAGA,GAAE,sBAAsB,GAAG;AAAA;AAAA,EAAK;AAAC;AAAC,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYN,IAAEC,IAAEC,IAAE;AAAC,SAAK,QAAMF,IAAE,KAAK,YAAUC,IAAE,KAAK,WAASC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,SAAS,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,SAAS,SAAS,IAAI;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,SAAS,SAAS,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK;AAAA,EAAS;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYH,IAAEC,IAAE;AAAC,SAAK,YAAUD,IAAE,KAAK,WAAS,OAAG,KAAK,WAASC;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAEC,IAAEC,IAAE;AAAC,SAAK,YAAU,KAAK,SAAS,OAAO,MAAKF,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,WAAS,MAAG,KAAK,SAAS,SAAS,IAAI;AAAA,EAAC;AAAC;;;ACA3/F,IAAMM,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,IAAE,GAAE,KAAK,YAAUA,IAAE,KAAK,SAAO,SAAQ,KAAK,KAAG,MAAK,KAAK,KAAG,MAAK,KAAK,iBAAiB,gBAAgB,CAAAA,OAAG;AAAC,WAAK,QAAQA,GAAE,IAAI;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,UAAMF,KAAE,YAAUE,GAAE,OAAO,cAAY,UAAQ,SAAQ,EAAC,GAAEC,IAAE,GAAEF,GAAC,IAAEC;AAAE,IAAAF,OAAI,KAAK,UAAQ,KAAK,OAAKG,MAAG,KAAK,OAAKF,OAAI,KAAK,SAAOD,IAAE,KAAK,KAAGG,IAAE,KAAK,KAAGF,IAAE,KAAK,UAAUD,IAAEG,IAAEF,EAAC;AAAA,EAAE;AAAC;;;ACAnQ,IAAMG,KAAN,cAAgBA,GAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,kBAAkB,IAAI;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,IAAE,GAAE,KAAK,yBAAuB,oBAAI,OAAI,KAAK,oBAAkB,IAAIC,GAAE,KAAE,GAAE,KAAK,gBAAc,CAAC,EAAC,MAAKA,GAAC,MAAI;AAAC,kBAAUA,GAAE,gBAAc,KAAK,uBAAuB,IAAIA,GAAE,OAAO,SAAS,GAAE,KAAK,QAAQ;AAAA,IAAE,GAAE,KAAK,mBAAiB,CAAC,EAAC,MAAKA,GAAC,MAAI;AAAC,kBAAUA,GAAE,gBAAc,KAAK,uBAAuB,OAAOA,GAAE,OAAO,SAAS,GAAE,KAAK,QAAQ;AAAA,IAAE,GAAE,KAAK,iBAAiB,gBAAe,KAAK,aAAa,GAAE,KAAK,iBAAiB,cAAa,KAAK,gBAAgB,GAAE,KAAK,iBAAiB,wBAAuB,KAAK,gBAAgB,GAAE,KAAK,iBAAiB,kBAAiB,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,kBAAkB,IAAI,KAAK,uBAAuB,OAAK,CAAC;AAAA,EAAC;AAAC;;;ACAnM,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,mBAAiB,oBAAI,OAAI,KAAK,eAAa,CAAC,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,oBAAkB,CAAC,GAAE,KAAK,sBAAoB,MAAK,KAAK,sCAAoC,OAAG,KAAK,gBAAc,oBAAI,OAAI,KAAK,gBAAc,oBAAI,OAAI,KAAK,sBAAoB,oBAAI,OAAI,KAAK,8BAA4B,oBAAI,OAAI,KAAK,aAAWA,IAAE,KAAK,qBAAmB,SAAQ,KAAK,kBAAgB,IAAI,EAAE,EAAC,uBAAsB,EAAC,GAAE,IAAI,GAAE,KAAK,wBAAsB,MAAK,KAAK,OAAK,EAAC,WAAU,QAAO,uBAAsB,MAAI,CAAC,CAAC,KAAK,oBAAmB;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,YAAY,kBAAgB,KAAK,iBAAiB,KAAK,IAAI,GAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAMA,KAAE,OAAO,KAAK,KAAK,YAAY;AAAE,eAAUC,MAAKD,GAAE,MAAK,kBAAkBC,EAAC;AAAE,SAAK,YAAY,QAAQ,GAAE,KAAK,sBAAoB,MAAK,KAAK,gBAAgB,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,UAAU,KAAM,CAAAD,OAAGA,GAAE,QAAQ,gBAAiB;AAAA,EAAC;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAO,KAAK;AAAA,EAAkB;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,mBAAmB;AAAA,EAAgB;AAAA,EAAC,gBAAgBA,IAAEC,IAAEC,KAAE,EAAE,UAAS;AAAC,QAAG,KAAK,aAAaF,EAAC,EAAE,QAAO,KAAK,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,uEAAqEA,KAAE,GAAG;AAAE,QAAG,MAAIC,GAAE,OAAO,QAAO,KAAK,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,yCAAyC;AAAE,UAAME,KAAE,EAAC,MAAKH,IAAE,UAASC,GAAE,IAAK,CAAAD,QAAI,EAAC,SAAQA,IAAE,QAAO,MAAG,SAAQ,OAAG,eAAc,GAAE,eAAcE,IAAE,eAAc,MAAK,mBAAkB,KAAI,EAAG,EAAC;AAAE,SAAK,aAAaF,EAAC,IAAEG;AAAE,aAAQ,IAAEA,GAAE,SAAS,SAAO,GAAE,KAAG,GAAE,KAAI;AAAC,YAAMH,KAAEG,GAAE,SAAS,CAAC;AAAE,WAAK,UAAU,KAAKH,EAAC,GAAEA,GAAE,QAAQ,UAAU,EAAC,oBAAmB,MAAI;AAAC,aAAK,mBAAmB;AAAA,MAAC,GAAE,MAAK,CAACC,IAAEG,IAAEF,IAAEC,IAAEE,OAAI;AAAC,aAAK,gBAAgBL,GAAE,gBAAc,GAAEC,IAAEG,IAAEF,IAAEG,IAAEF,EAAC;AAAA,MAAC,GAAE,mBAAkB,CAACF,IAAEG,OAAI;AAAC,aAAK,mBAAmBD,IAAEH,IAAEC,IAAEG,EAAC;AAAA,MAAC,GAAE,kBAAiB,CAAAH,OAAG;AAAC,QAAAD,GAAE,gBAAcC;AAAA,MAAC,GAAE,sBAAqB,CAAAA,OAAG;AAAC,QAAAD,GAAE,oBAAkBC;AAAA,MAAC,GAAE,yBAAwB,MAAI;AAAC,aAAK,aAAa,kBAAkB;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC;AAAC,SAAK,mBAAmB;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAE;AAAC,UAAMC,KAAE,KAAK,aAAaD,EAAC;AAAE,IAAAC,MAAGA,GAAE,SAAS,QAAS,CAAAD,OAAG;AAJ9sF;AAI+sF,MAAAA,GAAE,UAAQ,OAAG,KAAAA,GAAE,sBAAF,wBAAAA;AAAA,IAAuB,CAAE,GAAE,OAAO,KAAK,aAAaA,EAAC,GAAE,KAAK,sBAAoB,KAAK,oBAAoB,6BAA2B,OAAG,KAAK,+BAA+B,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,+DAA6DA,KAAE,GAAG;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAO,WAAS,KAAK,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,QAAG,KAAK,oBAAoB,QAAO,MAAK,KAAK,sCAAoC;AAAI,SAAK,sCAAoC;AAAG,UAAMA,KAAE,oBAAI,OAAIC,KAAE,oBAAI;AAAI,SAAK,oBAAkB,CAAC;AAAE,aAAQ,IAAE,KAAK,UAAU,SAAO,GAAE,KAAG,GAAE,KAAI;AAAC,YAAMD,KAAE,KAAK,UAAU,CAAC;AAAE,MAAAA,GAAE,gBAAc,GAAE,KAAK,kBAAkB,KAAKA,EAAC;AAAA,IAAC;AAAC,SAAK,oBAAkB,KAAK,sBAAsB,KAAK,iBAAiB;AAAE,aAAQ,IAAE,KAAK,kBAAkB,SAAO,GAAE,KAAG,GAAE,KAAI;AAAC,YAAME,KAAE,KAAK,kBAAkB,CAAC;AAAE,MAAAA,GAAE,gBAAc;AAAE,UAAIC,KAAED,GAAE,QAAQ;AAAe,UAAG,CAACC;AAAE,mBAAUF,MAAKC,GAAE,QAAQ,mBAAmB,KAAGF,GAAE,IAAIC,EAAC,GAAE;AAAC,UAAAE,KAAE;AAAG;AAAA,QAAK;AAAA;AAAC,UAAGA,GAAE,YAAUC,MAAKF,GAAE,QAAQ,sBAAqB;AAAC,QAAAF,GAAE,IAAII,GAAE,SAAS;AAAE,mBAAUJ,MAAKI,GAAE,aAAa,CAAAE,GAAEN,EAAC,KAAGC,GAAE,IAAID,EAAC;AAAA,MAAC;AAAC,MAAAE,GAAE,SAAOC;AAAA,IAAC;AAAC,SAAK,gBAAcH,IAAE,KAAK,gBAAcC,IAAE,KAAK,iBAAiB,OAAK,KAAG,KAAK,cAAc,IAAI,sBAAsB,GAAE,KAAK,cAAc,OAAK,MAAI,KAAK,cAAc,IAAI,UAAU,GAAE,KAAK,cAAc,IAAI,QAAQ,IAAG,KAAK,gBAAc,KAAK,YAAY,eAAa,KAAK;AAAA,EAAc;AAAA,EAAC,kBAAkBD,IAAEC,IAAE,GAAE;AAAC,SAAK,qBAAmBD;AAAE,UAAMG,KAAE,KAAK,KAAK,uBAAuB;AAAE,QAAG,EAAEA,EAAC,KAAGA,GAAE,MAAIF,MAAGE,GAAE,MAAI,GAAE;AAAC,YAAMH,KAAE,KAAK,gBAAgB,IAAI,uBAAuB;AAAE,MAAAA,GAAE,IAAEC,IAAED,GAAE,IAAE,GAAE,KAAK,KAAK,yBAAwBA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEC,IAAE;AAAC,QAAG,2BAAyBD,IAAE;AAAC,YAAMA,KAAEC;AAAE,WAAK,iBAAiB,OAAOD,GAAE,OAAO,SAAS;AAAA,IAAC;AAAC,SAAK,oBAAoBA,IAAEC,EAAC;AAAE,UAAM,IAAE,QAAM,KAAK,KAAK,YAAU,KAAK,KAAK,YAAUA,GAAE,SAAOA,GAAE,OAAO,YAAU,QAAOC,KAAED,GAAE,SAAOA,GAAE,OAAO,aAAW;AAAO,SAAK,0BAA0BD,IAAEC,IAAE,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAEC,IAAE;AAAC,QAAG,CAACA,GAAE;AAAO,QAAI,IAAE;AAAG,UAAMC,KAAE,MAAI;AAAC,UAAG,CAAC,GAAE;AAAC,cAAMF,KAAE,oBAAI;AAAI,aAAK,oBAAoB,QAAS,CAAAC,OAAG;AAAC,UAAAD,GAAE,IAAIC,EAAC;AAAA,QAAC,CAAE,GAAE,KAAK,sBAAoBD,IAAE,IAAE;AAAA,MAAE;AAAA,IAAC,GAAEG,KAAE,CAACH,IAAEC,OAAI;AAAC,MAAAA,MAAG,CAAC,KAAK,oBAAoB,IAAID,EAAC,KAAGE,GAAE,GAAE,KAAK,oBAAoB,IAAIF,EAAC,KAAG,CAACC,MAAG,KAAK,oBAAoB,IAAID,EAAC,MAAIE,GAAE,GAAE,KAAK,oBAAoB,OAAOF,EAAC;AAAA,IAAE;AAAE,QAAG,eAAaA,MAAG,aAAWA,IAAE;AAAC,YAAMI,KAAEH,GAAE;AAAI,WAAK,cAAc,IAAIG,EAAC,KAAGD,GAAEC,IAAE,eAAaJ,EAAC;AAAA,IAAC;AAAC,UAAMK,KAAEJ,GAAE;AAAO,IAAAE,GAAE,OAAM,EAAE,CAACE,MAAG,CAACA,GAAE,OAAO,GAAEF,GAAE,QAAO,EAAE,CAACE,MAAG,CAACA,GAAE,QAAQ,GAAEF,GAAE,SAAQ,EAAE,CAACE,MAAG,CAACA,GAAE,SAAS,GAAEF,GAAE,QAAO,EAAE,CAACE,MAAG,CAACA,GAAE,QAAQ,GAAEF,GAAE,WAAU,KAAK,oBAAoB,IAAI,KAAK,UAAU,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,SAAK,wBAAsB,IAAIE,GAAG,CAACL,IAAEC,IAAE,MAAI,KAAK,kBAAkBD,IAAEC,IAAE,CAAC,CAAE,GAAE,KAAK,qBAAmB,IAAIC,MAAE,KAAK,gBAAgB,uBAAsB,CAAC,KAAK,uBAAsB,KAAK,kBAAkB,GAAE,EAAE,MAAM,GAAE,KAAK,YAAY,SAAO,KAAG,KAAK,gBAAgB,WAAU,KAAK,aAAY,EAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,mBAAmBF,IAAEC,IAAE,GAAEC,IAAE;AAAC,UAAMC,KAAEH,GAAE,OAAK,MAAIC,GAAE,eAAcI,KAAE,KAAK,iBAAiB,IAAI,EAAE,SAAS,KAAG,oBAAI;AAAI,SAAK,iBAAiB,IAAI,EAAE,WAAUA,EAAC,GAAEH,MAAGG,GAAE,IAAIF,EAAC,GAAE,MAAIE,GAAE,QAAM,KAAK,eAAa,KAAK,YAAY,kBAAkB,GAAE,IAAE,KAAGA,GAAE,IAAIF,EAAC,MAAIE,GAAE,OAAOF,EAAC,GAAE,MAAIE,GAAE,SAAO,KAAK,iBAAiB,OAAO,EAAE,SAAS,GAAE,KAAK,eAAa,KAAK,YAAY,kBAAkB,GAAE,KAAE;AAAA,EAAG;AAAA,EAAC,iCAAgC;AAAC,SAAK,YAAU,KAAK,UAAU,OAAQ,CAAAL,OAAG,CAACA,GAAE,OAAQ,GAAE,KAAK,mBAAmB;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAEC,IAAE,GAAEC,IAAE;AAAC,SAAK,gBAAgB,GAAEF,IAAEC,IAAE,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAEC,IAAE,GAAEC,IAAEC,IAAEE,IAAE;AAAC,UAAME,KAAE,WAASL,KAAEA,KAAE,KAAK,sBAAoB,KAAK,oBAAoB,YAAU,YAAY,IAAI,GAAEI,KAAE,WAASH,MAAGA,IAAE,IAAE,EAAC,OAAM,IAAI,EAAEF,IAAE,GAAEM,IAAEF,MAAG,KAAK,qBAAoBC,EAAC,GAAE,eAAcN,GAAC;AAAE,SAAK,sBAAoB,KAAK,oBAAoB,OAAO,KAAK,CAAC,IAAE,KAAK,kBAAkB,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,SAAK,sBAAoB,EAAC,QAAO,IAAIA,MAAE,gBAAe,MAAK,4BAA2B,OAAG,WAAUA,GAAE,MAAM,UAAS,GAAE,KAAK,oBAAoB,OAAO,KAAKA,EAAC,GAAE,KAAK,qBAAqB;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAJhiN;AAIiiN,UAAMA,KAAE,EAAE,KAAK,mBAAmB;AAAE,WAAKA,GAAE,OAAO,SAAO,KAAG;AAAC,YAAK,EAAC,OAAMC,IAAE,eAAc,EAAC,IAAED,GAAE,OAAO,IAAI,GAAEE,KAAED,GAAE,QAAMA,GAAE,KAAK;AAAQ,UAAG,EAAE,QAAMC,MAAG,KAAK,4BAA4B,IAAIA,EAAC,GAAG,MAAIF,GAAE,iBAAe,KAAK,kBAAkB,CAAC,GAAEA,GAAE,kBAAgB;AAAC,YAAGA,GAAE,eAAe,QAAQ,CAAAA,GAAE,6BAA2B;AAAA,aAAO;AAAC,cAAGA,GAAE,eAAe,UAAQ,CAACC,GAAE,sBAAsB,OAAG,WAAAD,GAAE,gBAAe,kBAAjB,4BAAiCC,MAAGA,GAAE,sBAAsB,GAAE;AAAC,oBAAMC,MAAG,KAAK,4BAA4B,IAAIA,EAAC;AAAE;AAAA,UAAK;AAAC,cAAGD,GAAE,uBAAwB,MAAI,KAAK,qBAAqB,CAAE,EAAE,QAAO,KAAK,KAAK,kBAAkB,EAAC,OAAMA,IAAE,eAAcD,GAAE,eAAe,gBAAc,EAAC,CAAC;AAAA,QAAC;AAAC,QAAAA,GAAE,iBAAe,KAAK,kBAAkBA,GAAE,eAAe,gBAAc,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAA,GAAE,8BAA4B,KAAK,+BAA+B,GAAE,KAAK,oBAAkB,KAAK,4BAA4B,MAAM,GAAE,KAAK,sBAAoB,MAAK,KAAK,uCAAqC,KAAK,mBAAmB;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,UAAMC,KAAE,IAAID;AAAE,IAAAC,GAAE,KAAKD,EAAC;AAAE,UAAM,IAAE,KAAK;AAAoB,QAAG,GAAE;AAAC,aAAK,EAAE,OAAO,SAAQ,CAAAC,GAAE,KAAK,EAAE,OAAO,IAAI,CAAC;AAAE,QAAE,SAAOA,IAAE,EAAE,iBAAe;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,wBAAwBD,IAAEC,IAAE;AAAC,QAAGD,GAAE,QAAQ,mBAAiBC,GAAE,QAAQ,eAAe,QAAOD,GAAE,QAAQ,iBAAe,IAAE;AAAG,QAAGA,GAAE,kBAAgBC,GAAE,cAAc,QAAOD,GAAE,gBAAcC,GAAE,gBAAc,KAAG;AAAE,eAAU,KAAKD,GAAE,QAAQ,qBAAqB,YAAUA,MAAKC,GAAE,QAAQ,sBAAqB;AAAC,UAAG,EAAE,cAAYD,GAAE,UAAU;AAAS,YAAMC,KAAE,EAAE,aAAa,OAAQ,CAAAA,OAAGD,GAAE,aAAa,SAASC,EAAC,CAAE;AAAE,UAAGA,GAAE,WAAS,EAAE,aAAa,YAAUA,GAAE,WAASD,GAAE,aAAa,QAAQ,QAAO,EAAE,aAAa,SAAOA,GAAE,aAAa,SAAO,KAAG;AAAA,IAAC;AAAC,WAAOA,GAAE,gBAAcC,GAAE,gBAAc,KAAG;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAU,KAAKD,IAAE;AAAC,UAAIA,KAAE;AAAE,aAAKA,KAAEC,GAAE,UAAQ,KAAK,wBAAwB,GAAEA,GAAED,EAAC,CAAC,KAAG,IAAG,CAAAA;AAAI,MAAAC,GAAE,OAAOD,IAAE,GAAE,CAAC;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,UAAMD,KAAE,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK;AAAmB,WAAK,qBAAmB,MAAI;AAAA,MAAC,GAAED,GAAE,GAAE,KAAK,qBAAmBC;AAAA,IAAC;AAAE,WAAM,EAAC,aAAY,CAACA,IAAE,MAAI;AAAC,MAAAD,GAAG,MAAI;AAAC,aAAK,iBAAiBC,IAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,GAAE,uBAAsBD,GAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,yBAAwB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,+BAA+B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAE,GAAEC,IAAEC,IAAE;AAAC,SAAK,OAAKH,IAAE,KAAK,OAAKC,IAAE,KAAK,YAAU,GAAE,KAAK,YAAUC,IAAE,KAAK,aAAWC,IAAE,KAAK,oBAAkB,EAAE,MAAK,KAAK,kBAAgB;AAAA,EAAI;AAAA,EAAC,kBAAiB;AAAC,SAAK,qBAAmB,EAAE;AAAA,EAAO;AAAA,EAAC,wBAAuB;AAAC,WAAO,MAAI,KAAK,oBAAkB,EAAE;AAAA,EAAQ;AAAA,EAAC,MAAMH,IAAE;AAAC,SAAK,qBAAmB,EAAE;AAAO,UAAMC,KAAE,CAACD,IAAEC,OAAI;AAAC,WAAK,qBAAmB,CAAC,EAAE;AAAO,YAAM,IAAE,KAAK;AAAgB,UAAG,KAAK,kBAAgB,MAAK,KAAG,EAAE,GAAEA,GAAE,OAAMD;AAAE,aAAOA;AAAA,IAAC;AAAE,YAAO,cAAY,OAAOA,KAAEA,GAAE,IAAEA,IAAG,KAAM,CAAAA,OAAGC,GAAED,IAAE,KAAE,GAAI,CAAAA,OAAGC,GAAED,IAAE,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAE;AAAC,WAAM,CAAC,EAAE,KAAK,oBAAkB,EAAE,YAAU,KAAK,kBAAgBA,IAAE;AAAA,EAAG;AAAA,EAAC,iBAAgB;AAAC,SAAK,KAAK,OAAO,eAAe;AAAA,EAAC;AAAC;AAAC,IAAI;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,SAAO,CAAC,IAAE;AAAQ,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAM,IAAE,EAAC,QAAO,GAAE,SAAQ,GAAE,MAAK,IAAG,QAAO,IAAG,UAAS,GAAE;AAAE,IAAMQ,KAAN,MAAO;AAAC;AAAC,IAAM,IAAEA;", "names": ["e", "t", "n", "o", "a", "e", "t", "i", "i", "e", "t", "n", "a", "o", "g", "s", "c", "s", "i", "t", "e", "i", "t", "e", "t", "i", "n", "r", "s", "a", "o", "v"]}