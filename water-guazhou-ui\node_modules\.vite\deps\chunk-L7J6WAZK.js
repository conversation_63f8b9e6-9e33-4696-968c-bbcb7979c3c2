import {
  G
} from "./chunk-EJ4BPAYT.js";
import {
  i
} from "./chunk-JTTSDQPH.js";
import {
  c,
  e as e3
} from "./chunk-QQS4HCWF.js";
import {
  $ as $2,
  z
} from "./chunk-AOYBG2OC.js";
import {
  l as l2
} from "./chunk-WZNPTIYX.js";
import {
  e as e2
} from "./chunk-SY6DBVDS.js";
import {
  m
} from "./chunk-3WEGNHPY.js";
import {
  $
} from "./chunk-JXLVNWKF.js";
import {
  u
} from "./chunk-G5KX4JSG.js";
import {
  D
} from "./chunk-EKX3LLYN.js";
import {
  e,
  l,
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/engine/webgl/WGLRendererInfo.js
function c2(e4, t) {
  const i2 = t.length;
  if (e4 < t[0].value || 1 === i2) return t[0].size;
  for (let s = 1; s < i2; s++) if (e4 < t[s].value) {
    const i3 = (e4 - t[s - 1].value) / (t[s].value - t[s - 1].value);
    return t[s - 1].size + i3 * (t[s].size - t[s - 1].size);
  }
  return t[i2 - 1].size;
}
var h = class {
  constructor() {
    this.symbolLevels = [], this.vvColorValues = new Float32Array(8), this.vvColors = new Float32Array(32), this.vvOpacityValues = new Float32Array(8), this.vvOpacities = new Float32Array(8), this.vvSizeMinMaxValue = new Float32Array(4), this.outsideLabelsVisible = false, this._vvMaterialParameters = { vvSizeEnabled: false, vvColorEnabled: false, vvRotationEnabled: false, vvRotationType: "geographic", vvOpacityEnabled: false }, this._technique = e3;
  }
  getSizeVVFieldStops(i2) {
    const s = this._vvSizeFieldStops;
    if (s) switch (s.type) {
      case "static":
        return s;
      case "level-dependent":
        return l(s.levels[i2], () => {
          let e4 = 1 / 0, a = 0;
          for (const t in s.levels) {
            const s2 = parseFloat(t), r3 = Math.abs(i2 - s2);
            r3 < e4 && (e4 = r3, a = s2);
          }
          if (e4 === 1 / 0) return { sizes: new Float32Array([0, 0, 0, 0, 0, 0]), values: new Float32Array([0, 0, 0, 0, 0, 0]) };
          const r2 = 2 ** ((i2 - a) / 2), l3 = e(s.levels[a]), o2 = new Float32Array(l3.values);
          return o2[2] *= r2, o2[3] *= r2, { sizes: e(l3.sizes), values: o2 };
        });
      default:
        return;
    }
  }
  get vvMaterialParameters() {
    return this._vvMaterialParameters;
  }
  update(e4) {
    r(this._vvInfo) && this._updateVisualVariables(this._vvInfo.vvRanges, e4);
  }
  setInfo(e4, t, i2) {
    this._updateEffects(i2), this._vvInfo = t, this._technique = c(e4), this.rendererSchema = this._technique.createOrUpdateRendererSchema(this.rendererSchema, e4);
  }
  getVariation() {
    return { ...this._technique.getVariation(this.rendererSchema), outsideLabelsVisible: this.outsideLabelsVisible, supportsTextureFloat: l2("2d").supportsTextureFloat };
  }
  getVariationHash() {
    return this._technique.getVariationHash(this.rendererSchema) << 1 | (this.outsideLabelsVisible ? 1 : 0);
  }
  _updateEffects(e4) {
    r(e4) ? this.outsideLabelsVisible = e4.excludedLabelsVisible : this.outsideLabelsVisible = false;
  }
  _updateVisualVariables(e4, t) {
    const i2 = this._vvMaterialParameters;
    if (i2.vvOpacityEnabled = false, i2.vvSizeEnabled = false, i2.vvColorEnabled = false, i2.vvRotationEnabled = false, !e4) return;
    const n = e4.size;
    if (n) {
      if (i2.vvSizeEnabled = true, n.minMaxValue) {
        const e5 = n.minMaxValue;
        let i3, a;
        if (z(e5.minSize) && z(e5.maxSize)) if ($2(e5.minSize) && $2(e5.maxSize)) i3 = u(e5.minSize), a = u(e5.maxSize);
        else {
          const r2 = t.scale;
          i3 = u(c2(r2, e5.minSize.stops)), a = u(c2(r2, e5.maxSize.stops));
        }
        this.vvSizeMinMaxValue.set([e5.minDataValue, e5.maxDataValue, i3, a]);
      }
      if (n.scaleStops && (this.vvSizeScaleStopsValue = u(c2(t.scale, n.scaleStops.stops))), n.unitValue) {
        const e5 = $(t.spatialReference) / m[n.unitValue.unit];
        this.vvSizeUnitValueToPixelsRatio = e5 / t.resolution;
      }
      n.fieldStops && (this._vvSizeFieldStops = n.fieldStops);
    }
    const v = e4.color;
    v && (i2.vvColorEnabled = true, this.vvColorValues.set(v.values), this.vvColors.set(v.colors));
    const u2 = e4.opacity;
    u2 && (i2.vvOpacityEnabled = true, this.vvOpacityValues.set(u2.values), this.vvOpacities.set(u2.opacities));
    const h2 = e4.rotation;
    h2 && (i2.vvRotationEnabled = true, i2.vvRotationType = h2.type);
  }
};

// node_modules/@arcgis/core/views/2d/engine/FeatureContainer.js
var o = class extends i {
  constructor(e4) {
    super(e4), this._rendererInfo = new h(), this._materialItemsRequestQueue = new e2(), this.attributeView = new G(() => this.onAttributeStoreUpdate());
  }
  destroy() {
    this.children.forEach((e4) => e4.destroy()), this.removeAllChildren(), this.attributeView.destroy(), this._materialItemsRequestQueue.clear();
  }
  setRendererInfo(e4, t, r2) {
    this._rendererInfo.setInfo(e4, t, r2), this.requestRender();
  }
  async getMaterialItems(t, r2) {
    if (!t || 0 === t.length) return [];
    const s = D();
    return this._materialItemsRequestQueue.push({ items: t, abortOptions: r2, resolver: s }), this.requestRender(), s.promise;
  }
  doRender(e4) {
    if (e4.context.capabilities.enable("textureFloat"), e4.context.capabilities.enable("vao"), this._materialItemsRequestQueue.length > 0) {
      let t = this._materialItemsRequestQueue.pop();
      for (; t; ) this._processMaterialItemRequest(e4, t), t = this._materialItemsRequestQueue.pop();
    }
    super.doRender(e4);
  }
  renderChildren(e4) {
    for (const t of this.children) t.commit(e4);
    this._rendererInfo.update(e4.state), super.renderChildren(e4);
  }
  updateTransforms(e4) {
    if (this.children.some((e5) => e5.hasData)) for (const t of this.children) t.setTransform(e4);
  }
  createRenderParams(e4) {
    const t = super.createRenderParams(e4);
    return t.rendererInfo = this._rendererInfo, t.attributeView = this.attributeView, t;
  }
  onAttributeStoreUpdate() {
  }
  _processMaterialItemRequest(e4, { items: t, abortOptions: r2, resolver: s }) {
    const { painter: i2, pixelRatio: o2 } = e4, a = t.map((e5) => i2.textureManager.rasterizeItem(e5.symbol, o2, e5.glyphIds, r2));
    Promise.all(a).then((e5) => {
      if (!this.stage) return void s.reject();
      const r3 = e5.map((e6, r4) => ({ id: t[r4].id, mosaicItem: e6 }));
      s.resolve(r3);
    }, s.reject);
  }
};

export {
  o
};
//# sourceMappingURL=chunk-L7J6WAZK.js.map
