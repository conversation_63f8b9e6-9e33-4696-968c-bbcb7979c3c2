import {
  p
} from "./chunk-NQ3OACUM.js";
import {
  i as i2
} from "./chunk-NZB6EMKN.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/layers/support/FeatureType.js
var c = class extends i(l) {
  constructor(o2) {
    super(o2), this.id = null, this.name = null, this.domains = null, this.templates = null;
  }
  readDomains(o2) {
    const r2 = {};
    for (const t of Object.keys(o2)) r2[t] = i2(o2[t]);
    return r2;
  }
  writeDomains(o2, r2) {
    var _a;
    const t = {};
    for (const s of Object.keys(o2)) o2[s] && (t[s] = (_a = o2[s]) == null ? void 0 : _a.toJSON());
    r2.domains = t;
  }
};
e([y({ json: { write: true } })], c.prototype, "id", void 0), e([y({ json: { write: true } })], c.prototype, "name", void 0), e([y({ json: { write: true } })], c.prototype, "domains", void 0), e([o("domains")], c.prototype, "readDomains", null), e([r("domains")], c.prototype, "writeDomains", null), e([y({ type: [p], json: { write: true } })], c.prototype, "templates", void 0), c = e([a("esri.layers.support.FeatureType")], c);
var n = c;

export {
  n
};
//# sourceMappingURL=chunk-FWXA4I6D.js.map
