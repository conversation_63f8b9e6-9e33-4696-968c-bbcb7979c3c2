{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/features/support/whereUtils.js", "../../@arcgis/core/views/2d/layers/features/support/FeatureFilter.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/Error.js\";import r from\"../../../../../core/Logger.js\";const t=r.getLogger(\"esri.views.2d.layers.features.support.whereUtils\"),a={getAttribute:(e,r)=>e.field(r)};async function s(r,s){const n=await import(\"../../../../../core/sql/WhereClause.js\");try{const o=n.WhereClause.create(r,s);if(!o.isStandardized){const r=new e(\"mapview - bad input\",\"Unable to apply filter's definition expression, as expression is not standardized.\",o);t.error(r)}return e=>{const r=e.readArcadeFeature();return o.testFeature(r,a)}}catch(o){return t.warn(\"mapview-bad-where-clause\",\"Encountered an error when evaluating where clause\",r),e=>!0}}export{s as createWhereClause};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../../core/Error.js\";import e from\"../../../../../core/Logger.js\";import{isNone as i}from\"../../../../../core/maybe.js\";import{create as s}from\"../../../../../geometry/support/aaBoundingRect.js\";import{getBoundsXY as r}from\"../../../../../geometry/support/boundsUtils.js\";import{getSpatialQueryOperator as o}from\"../../../../../layers/graphics/data/spatialQuerySupport.js\";import{getTimeOperator as h}from\"../../../../../layers/graphics/data/timeSupport.js\";import{normalizeQueryLike as a}from\"../../../../../layers/graphics/data/utils.js\";import l from\"../../../../../rest/support/Query.js\";import{featureAdapter as n}from\"../FeatureStore2D.js\";import{createWhereClause as d}from\"./whereUtils.js\";const m=1,_=2;class p{constructor(t){this._geometryBounds=s(),this._idToVisibility=new Map,this._serviceInfo=t}get hash(){return this._hash}check(t){return this._applyFilter(t)}clear(){const t=this._resetAllHiddenIds();return this.update(),{show:t,hide:[]}}invalidate(){this._idToVisibility.forEach(((t,e)=>{this._idToVisibility.set(e,0)}))}setKnownIds(t){for(const e of t)this._idToVisibility.set(e,m)}setTrue(t){const e=[],i=[],s=new Set(t);return this._idToVisibility.forEach(((t,r)=>{const o=!!(this._idToVisibility.get(r)&m),h=s.has(r);!o&&h?e.push(r):o&&!h&&i.push(r),this._idToVisibility.set(r,h?m|_:0)})),{show:e,hide:i}}createQuery(){const{geometry:t,spatialRel:e,where:i,timeExtent:s,objectIds:r}=this;return l.fromJSON({geometry:t,spatialRel:e,where:i,timeExtent:s,objectIds:r})}async update(t,e){this._hash=JSON.stringify(t);const i=await a(t,null,e);await Promise.all([this._setGeometryFilter(i),this._setIdFilter(i),this._setAttributeFilter(i),this._setTimeFilter(i)])}async _setAttributeFilter(t){if(!t||!t.where)return this._clause=null,void(this.where=null);this._clause=await d(t.where,this._serviceInfo.fieldsIndex),this.where=t.where}_setIdFilter(t){this._idsToShow=t&&t.objectIds&&new Set(t.objectIds),this._idsToHide=t&&t.hiddenIds&&new Set(t.hiddenIds),this.objectIds=t&&t.objectIds}async _setGeometryFilter(t){if(!t||!t.geometry)return this._spatialQueryOperator=null,this.geometry=null,void(this.spatialRel=null);const e=t.geometry,i=t.spatialRel||\"esriSpatialRelIntersects\",s=await o(i,e,this._serviceInfo.geometryType,this._serviceInfo.hasZ,this._serviceInfo.hasM);r(this._geometryBounds,e),this._spatialQueryOperator=s,this.geometry=e,this.spatialRel=i}_setTimeFilter(i){if(this.timeExtent=this._timeOperator=null,i&&i.timeExtent)if(this._serviceInfo.timeInfo)this.timeExtent=i.timeExtent,this._timeOperator=h(this._serviceInfo.timeInfo,i.timeExtent,n);else{const s=new t(\"feature-layer-view:time-filter-not-available\",\"Unable to apply time filter, as layer doesn't have time metadata.\",i.timeExtent);e.getLogger(\"esri.views.2d.layers.features.controllers.FeatureFilter\").error(s)}}_applyFilter(t){return this._filterByGeometry(t)&&this._filterById(t)&&this._filterByTime(t)&&this._filterByExpression(t)}_filterByExpression(t){return!this.where||this._clause(t)}_filterById(t){return(!this._idsToHide||!this._idsToHide.size||!this._idsToHide.has(t.getObjectId()))&&(!this._idsToShow||!this._idsToShow.size||this._idsToShow.has(t.getObjectId()))}_filterByGeometry(t){if(!this.geometry)return!0;const e=t.readHydratedGeometry();return!!e&&this._spatialQueryOperator(e)}_filterByTime(t){return!!i(this._timeOperator)||this._timeOperator(t)}_resetAllHiddenIds(){const t=[];return this._idToVisibility.forEach(((e,i)=>{e&m||(this._idToVisibility.set(i,m),t.push(i))})),t}}export{p as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIyF,IAAMA,KAAE,EAAE,UAAU,kDAAkD;AAAtE,IAAwE,IAAE,EAAC,cAAa,CAAC,GAAE,MAAI,EAAE,MAAM,CAAC,EAAC;AAAE,eAAeC,GAAE,GAAEA,IAAE;AAAC,QAAMC,KAAE,MAAM,OAAO,2BAAwC;AAAE,MAAG;AAAC,UAAM,IAAEA,GAAE,YAAY,OAAO,GAAED,EAAC;AAAE,QAAG,CAAC,EAAE,gBAAe;AAAC,YAAME,KAAE,IAAIF,GAAE,uBAAsB,sFAAqF,CAAC;AAAE,MAAAD,GAAE,MAAMG,EAAC;AAAA,IAAC;AAAC,WAAO,OAAG;AAAC,YAAMA,KAAE,EAAE,kBAAkB;AAAE,aAAO,EAAE,YAAYA,IAAE,CAAC;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,WAAOH,GAAE,KAAK,4BAA2B,qDAAoD,CAAC,GAAE,OAAG;AAAA,EAAE;AAAC;;;ACAkE,IAAM,IAAE;AAAR,IAAU,IAAE;AAAE,IAAMI,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,kBAAgB,EAAE,GAAE,KAAK,kBAAgB,oBAAI,OAAI,KAAK,eAAaA;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,MAAMA,IAAE;AAAC,WAAO,KAAK,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,KAAK,mBAAmB;AAAE,WAAO,KAAK,OAAO,GAAE,EAAC,MAAKA,IAAE,MAAK,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,gBAAgB,QAAS,CAACA,IAAE,MAAI;AAAC,WAAK,gBAAgB,IAAI,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,eAAU,KAAKA,GAAE,MAAK,gBAAgB,IAAI,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,UAAM,IAAE,CAAC,GAAE,IAAE,CAAC,GAAEC,KAAE,IAAI,IAAID,EAAC;AAAE,WAAO,KAAK,gBAAgB,QAAS,CAACA,IAAE,MAAI;AAAC,YAAM,IAAE,CAAC,EAAE,KAAK,gBAAgB,IAAI,CAAC,IAAE,IAAG,IAAEC,GAAE,IAAI,CAAC;AAAE,OAAC,KAAG,IAAE,EAAE,KAAK,CAAC,IAAE,KAAG,CAAC,KAAG,EAAE,KAAK,CAAC,GAAE,KAAK,gBAAgB,IAAI,GAAE,IAAE,IAAE,IAAE,CAAC;AAAA,IAAC,CAAE,GAAE,EAAC,MAAK,GAAE,MAAK,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAK,EAAC,UAASD,IAAE,YAAW,GAAE,OAAM,GAAE,YAAWC,IAAE,WAAU,EAAC,IAAE;AAAK,WAAO,EAAE,SAAS,EAAC,UAASD,IAAE,YAAW,GAAE,OAAM,GAAE,YAAWC,IAAE,WAAU,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOD,IAAE,GAAE;AAAC,SAAK,QAAM,KAAK,UAAUA,EAAC;AAAE,UAAM,IAAE,MAAM,EAAEA,IAAE,MAAK,CAAC;AAAE,UAAM,QAAQ,IAAI,CAAC,KAAK,mBAAmB,CAAC,GAAE,KAAK,aAAa,CAAC,GAAE,KAAK,oBAAoB,CAAC,GAAE,KAAK,eAAe,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,oBAAoBA,IAAE;AAAC,QAAG,CAACA,MAAG,CAACA,GAAE,MAAM,QAAO,KAAK,UAAQ,MAAK,MAAK,KAAK,QAAM;AAAM,SAAK,UAAQ,MAAMC,GAAED,GAAE,OAAM,KAAK,aAAa,WAAW,GAAE,KAAK,QAAMA,GAAE;AAAA,EAAK;AAAA,EAAC,aAAaA,IAAE;AAAC,SAAK,aAAWA,MAAGA,GAAE,aAAW,IAAI,IAAIA,GAAE,SAAS,GAAE,KAAK,aAAWA,MAAGA,GAAE,aAAW,IAAI,IAAIA,GAAE,SAAS,GAAE,KAAK,YAAUA,MAAGA,GAAE;AAAA,EAAS;AAAA,EAAC,MAAM,mBAAmBA,IAAE;AAAC,QAAG,CAACA,MAAG,CAACA,GAAE,SAAS,QAAO,KAAK,wBAAsB,MAAK,KAAK,WAAS,MAAK,MAAK,KAAK,aAAW;AAAM,UAAM,IAAEA,GAAE,UAAS,IAAEA,GAAE,cAAY,4BAA2BC,KAAE,MAAM,EAAE,GAAE,GAAE,KAAK,aAAa,cAAa,KAAK,aAAa,MAAK,KAAK,aAAa,IAAI;AAAE,MAAE,KAAK,iBAAgB,CAAC,GAAE,KAAK,wBAAsBA,IAAE,KAAK,WAAS,GAAE,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,eAAe,GAAE;AAAC,QAAG,KAAK,aAAW,KAAK,gBAAc,MAAK,KAAG,EAAE,WAAW,KAAG,KAAK,aAAa,SAAS,MAAK,aAAW,EAAE,YAAW,KAAK,gBAAc,EAAE,KAAK,aAAa,UAAS,EAAE,YAAW,CAAC;AAAA,SAAM;AAAC,YAAMA,KAAE,IAAIA,GAAE,gDAA+C,qEAAoE,EAAE,UAAU;AAAE,QAAE,UAAU,yDAAyD,EAAE,MAAMA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAE;AAAC,WAAO,KAAK,kBAAkBA,EAAC,KAAG,KAAK,YAAYA,EAAC,KAAG,KAAK,cAAcA,EAAC,KAAG,KAAK,oBAAoBA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,WAAM,CAAC,KAAK,SAAO,KAAK,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,YAAO,CAAC,KAAK,cAAY,CAAC,KAAK,WAAW,QAAM,CAAC,KAAK,WAAW,IAAIA,GAAE,YAAY,CAAC,OAAK,CAAC,KAAK,cAAY,CAAC,KAAK,WAAW,QAAM,KAAK,WAAW,IAAIA,GAAE,YAAY,CAAC;AAAA,EAAE;AAAA,EAAC,kBAAkBA,IAAE;AAAC,QAAG,CAAC,KAAK,SAAS,QAAM;AAAG,UAAM,IAAEA,GAAE,qBAAqB;AAAE,WAAM,CAAC,CAAC,KAAG,KAAK,sBAAsB,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,WAAM,CAAC,CAAC,EAAE,KAAK,aAAa,KAAG,KAAK,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMA,KAAE,CAAC;AAAE,WAAO,KAAK,gBAAgB,QAAS,CAAC,GAAE,MAAI;AAAC,UAAE,MAAI,KAAK,gBAAgB,IAAI,GAAE,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAA,IAAE,CAAE,GAAEA;AAAA,EAAC;AAAC;", "names": ["t", "s", "n", "r", "p", "t", "s"]}