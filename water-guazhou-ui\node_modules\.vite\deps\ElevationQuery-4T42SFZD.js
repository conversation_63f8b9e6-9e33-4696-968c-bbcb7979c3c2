import {
  t as t2
} from "./chunk-3IDKVHSA.js";
import {
  b
} from "./chunk-P37TUI4J.js";
import {
  _n,
  rn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YEODPCXQ.js";
import {
  R,
  c,
  f,
  u as u2,
  w as w3
} from "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  u
} from "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import {
  w as w2
} from "./chunk-63M4K32A.js";
import {
  M
} from "./chunk-R5MYQRRS.js";
import {
  $
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  E,
  w,
  y
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  i,
  l,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/ElevationSampler.js
var m2 = s.getLogger("esri.layers.support.ElevationSampler");
var c2 = class {
  queryElevation(e) {
    return h(e.clone(), this);
  }
  on() {
    return g;
  }
  projectIfRequired(e, t4) {
    return f2(e, t4);
  }
};
var u3 = class extends c2 {
  get spatialReference() {
    return this.extent.spatialReference;
  }
  constructor(e, t4, n) {
    super(), this.tile = e, this.noDataValue = n;
    const o = e.tile.extent;
    this.extent = f(o, t4.spatialReference), this.extent.zmin = e.zmin, this.extent.zmax = e.zmax, this._aaExtent = o;
    const a = $(t4.spatialReference), r3 = t4.lodAt(e.tile.level).resolution * a;
    this.demResolution = { min: r3, max: r3 };
  }
  contains(e) {
    const n = this.projectIfRequired(e, this.spatialReference);
    return !t(n) && this.containsAt(n.x, n.y);
  }
  containsAt(e, t4) {
    return w3(this._aaExtent, e, t4);
  }
  elevationAt(e, t4) {
    if (!this.containsAt(e, t4)) {
      const n = this.extent, s4 = `${n.xmin}, ${n.ymin}, ${n.xmax}, ${n.ymax}`;
      return m2.warn("#elevationAt()", `Point used to sample elevation (${e}, ${t4}) is outside of the sampler extent (${s4})`), this.noDataValue;
    }
    return l(this.tile.sample(e, t4), this.noDataValue);
  }
};
var p = class extends c2 {
  get spatialReference() {
    return this.extent.spatialReference;
  }
  constructor(e, t4, n) {
    let s4;
    super(), "number" == typeof t4 ? (this.noDataValue = t4, s4 = null) : (s4 = t4, this.noDataValue = n), this.samplers = s4 ? e.map((e2) => new u3(e2, s4, this.noDataValue)) : e;
    const o = this.samplers[0];
    if (o) {
      this.extent = o.extent.clone();
      const { min: e2, max: t5 } = o.demResolution;
      this.demResolution = { min: e2, max: t5 };
      for (let n2 = 1; n2 < this.samplers.length; n2++) {
        const e3 = this.samplers[n2];
        this.extent.union(e3.extent), this.demResolution.min = Math.min(this.demResolution.min, e3.demResolution.min), this.demResolution.max = Math.max(this.demResolution.max, e3.demResolution.max);
      }
    } else this.extent = f(u2(), s4.spatialReference), this.demResolution = { min: 0, max: 0 };
  }
  elevationAt(e, t4) {
    for (const n of this.samplers) if (n.containsAt(e, t4)) return n.elevationAt(e, t4);
    return m2.warn("#elevationAt()", `Point used to sample elevation (${e}, ${t4}) is outside of the sampler`), this.noDataValue;
  }
};
function h(e, t4) {
  const n = f2(e, t4.spatialReference);
  if (!n) return null;
  switch (e.type) {
    case "point":
      x(e, n, t4);
      break;
    case "polyline":
      R2(e, n, t4);
      break;
    case "multipoint":
      d(e, n, t4);
  }
  return e;
}
function f2(e, n) {
  if (t(e)) return null;
  const s4 = e.spatialReference;
  if (s4.equals(n)) return e;
  const i2 = M(e, n);
  return i2 || m2.error(`Cannot project geometry spatial reference (wkid:${s4.wkid}) to elevation sampler spatial reference (wkid:${n.wkid})`), i2;
}
function x(e, t4, n) {
  e.z = n.elevationAt(t4.x, t4.y);
}
function R2(e, t4, n) {
  y2.spatialReference = t4.spatialReference;
  const s4 = e.hasM && !e.hasZ;
  for (let i2 = 0; i2 < e.paths.length; i2++) {
    const o = e.paths[i2], a = t4.paths[i2];
    for (let e2 = 0; e2 < o.length; e2++) {
      const t5 = o[e2], i3 = a[e2];
      y2.x = i3[0], y2.y = i3[1], s4 && (t5[3] = t5[2]), t5[2] = n.elevationAt(y2.x, y2.y);
    }
  }
  e.hasZ = true;
}
function d(e, t4, n) {
  y2.spatialReference = t4.spatialReference;
  const s4 = e.hasM && !e.hasZ;
  for (let i2 = 0; i2 < e.points.length; i2++) {
    const o = e.points[i2], a = t4.points[i2];
    y2.x = a[0], y2.y = a[1], s4 && (o[3] = o[2]), o[2] = n.elevationAt(y2.x, y2.y);
  }
  e.hasZ = true;
}
var y2 = new w2();
var g = { remove() {
} };

// node_modules/@arcgis/core/layers/support/ElevationSamplerData.js
var t3 = class {
  constructor(t4, h2) {
    this.data = t4, this.safeWidth = 0.99999999 * (t4.width - 1), this.dx = (t4.width - 1) / (h2[2] - h2[0]), this.dy = (t4.width - 1) / (h2[3] - h2[1]), this.x0 = h2[0], this.y1 = h2[3];
  }
};

// node_modules/@arcgis/core/layers/support/ElevationTile.js
var r2 = class {
  constructor(t4, r3 = null) {
    if (this.tile = t4, r(r3) && r(t4)) {
      const a = t4.extent;
      this._samplerData = new t3(r3, a);
    }
  }
  get zmin() {
    return r(this._samplerData) ? this._samplerData.data.minValue : 0;
  }
  get zmax() {
    return r(this._samplerData) ? this._samplerData.data.maxValue : 0;
  }
  sample(a, e) {
    if (t(this._samplerData)) return;
    const { safeWidth: r3, data: i2, dx: l2, dy: m3, y1: n, x0: o } = this._samplerData, { width: h2, values: p2, noDataValue: u4 } = i2, f3 = s3(m3 * (n - e), 0, r3), D = s3(l2 * (a - o), 0, r3), c3 = Math.floor(f3), d2 = Math.floor(D), _2 = c3 * h2 + d2, x3 = _2 + h2, y3 = p2[_2], V = p2[x3], g3 = p2[_2 + 1], j = p2[x3 + 1];
    if (y3 !== u4 && V !== u4 && g3 !== u4 && j !== u4) {
      const a2 = D - d2, t4 = y3 + (g3 - y3) * a2;
      return t4 + (V + (j - V) * a2 - t4) * (f3 - c3);
    }
  }
};
function s3(a, t4, e) {
  return a < t4 ? t4 : a > e ? e : a;
}

// node_modules/@arcgis/core/layers/support/ElevationQuery.js
var g2 = class {
  async queryAll(e, i2, s4) {
    if (!(e = s4 && s4.ignoreInvisibleLayers ? e.filter((e2) => e2.visible) : e.slice()).length) throw new s2("elevation-query:invalid-layer", "Elevation queries require at least one elevation layer to fetch tiles from");
    const n = x2.fromGeometry(i2);
    let o = false;
    s4 && s4.returnSampleInfo || (o = true);
    const l2 = { ...A, ...s4, returnSampleInfo: true }, a = await this.query(e[e.length - 1], n, l2), r3 = await this._queryAllContinue(e, a, l2);
    return r3.geometry = r3.geometry.export(), o && delete r3.sampleInfo, r3;
  }
  async query(e, i2, s4) {
    if (!e) throw new s2("elevation-query:invalid-layer", "Elevation queries require an elevation layer to fetch tiles from");
    if (!i2 || !(i2 instanceof x2) && "point" !== i2.type && "multipoint" !== i2.type && "polyline" !== i2.type) throw new s2("elevation-query:invalid-geometry", "Only point, polyline and multipoint geometries can be used to query elevation");
    const n = { ...A, ...s4 }, o = new R3(e, i2.spatialReference, n), l2 = n.signal;
    return await e.load({ signal: l2 }), await this._createGeometryDescriptor(o, i2, l2), await this._selectTiles(o, l2), await this._populateElevationTiles(o, l2), this._sampleGeometryWithElevation(o), this._createQueryResult(o, l2);
  }
  async createSampler(e, i2, s4) {
    if (!e) throw new s2("elevation-query:invalid-layer", "Elevation queries require an elevation layer to fetch tiles from");
    if (!i2 || "extent" !== i2.type) throw new s2("elevation-query:invalid-extent", "Invalid or undefined extent");
    const n = { ...A, ...s4 };
    return this._createSampler(e, i2, n);
  }
  async createSamplerAll(e, i2, s4) {
    if (!(e = s4 && s4.ignoreInvisibleLayers ? e.filter((e2) => e2.visible) : e.slice()).length) throw new s2("elevation-query:invalid-layer", "Elevation queries require at least one elevation layer to fetch tiles from");
    if (!i2 || "extent" !== i2.type) throw new s2("elevation-query:invalid-extent", "Invalid or undefined extent");
    const n = { ...A, ...s4, returnSampleInfo: true }, o = await this._createSampler(e[e.length - 1], i2, n);
    return this._createSamplerAllContinue(e, i2, o, n);
  }
  async _createSampler(e, t4, i2, s4) {
    const n = i2.signal;
    await e.load({ signal: n });
    const o = t4.spatialReference, l2 = e.tileInfo.spatialReference;
    o.equals(l2) || (await _n([{ source: o, dest: l2 }], { signal: n }), t4 = rn(t4, l2));
    const a = new q(e, t4, i2, s4);
    return await this._selectTiles(a, n), await this._populateElevationTiles(a, n), new p(a.elevationTiles, a.layer.tileInfo, a.options.noDataValue);
  }
  async _createSamplerAllContinue(e, t4, i2, s4) {
    if (e.pop(), !e.length) return i2;
    const n = i2.samplers.map((e2) => c(e2.extent)), o = await this._createSampler(e[e.length - 1], t4, s4, n);
    if (0 === o.samplers.length) return i2;
    const l2 = i2.samplers.concat(o.samplers), a = new p(l2, s4.noDataValue);
    return this._createSamplerAllContinue(e, t4, a, s4);
  }
  async _queryAllContinue(e, t4, s4) {
    const n = e.pop(), o = t4.geometry.coordinates, l2 = t4.sampleInfo;
    i(l2);
    const a = [], r3 = [];
    for (let i2 = 0; i2 < o.length; i2++) {
      const t5 = l2[i2];
      t5.demResolution >= 0 ? t5.source || (t5.source = n) : e.length && (a.push(o[i2]), r3.push(i2));
    }
    if (!e.length || 0 === a.length) return t4;
    const c3 = t4.geometry.clone(a), u4 = await this.query(e[e.length - 1], c3, s4), h2 = u4.sampleInfo;
    if (!h2) throw new Error("no sampleInfo");
    return r3.forEach((e2, t5) => {
      o[e2].z = u4.geometry.coordinates[t5].z, l2[e2].demResolution = h2[t5].demResolution;
    }), this._queryAllContinue(e, t4, s4);
  }
  async _createQueryResult(e, t4) {
    const s4 = await e.geometry.project(e.outSpatialReference, t4);
    i(s4);
    const n = { geometry: s4.export(), noDataValue: e.options.noDataValue };
    return e.options.returnSampleInfo && (n.sampleInfo = this._extractSampleInfo(e)), e.geometry.coordinates.forEach((e2) => {
      e2.tile = null, e2.elevationTile = null;
    }), n;
  }
  async _createGeometryDescriptor(e, i2, s4) {
    let n;
    const o = e.layer.tileInfo.spatialReference;
    if (i2 instanceof x2 ? n = await i2.project(o, s4) : (await _n([{ source: i2.spatialReference, dest: o }], { signal: s4 }), n = rn(i2, o)), !n) throw new s2("elevation-query:spatial-reference-mismatch", `Cannot query elevation in '${i2.spatialReference.wkid}' on an elevation service in '${o.wkid}'`);
    e.geometry = x2.fromGeometry(n);
  }
  async _selectTiles(e, i2) {
    const s4 = e.options.demResolution;
    if ("geometry" === e.type && this._preselectOutsideLayerExtent(e), "number" == typeof s4) this._selectTilesClosestResolution(e);
    else if ("finest-contiguous" === s4) await this._selectTilesFinestContiguous(e, i2);
    else {
      if ("auto" !== s4) throw new s2("elevation-query:invalid-dem-resolution", `Invalid dem resolution value '${s4}', expected a number, "finest-contiguous" or "auto"`);
      await this._selectTilesAuto(e, i2);
    }
  }
  _preselectOutsideLayerExtent(e) {
    if (t(e.layer.fullExtent)) return;
    const t4 = new r2(null);
    t4.sample = () => e.options.noDataValue, e.outsideExtentTile = t4;
    const i2 = e.layer.fullExtent;
    e.geometry.coordinates.forEach((e2) => {
      const s4 = e2.x, n = e2.y;
      (s4 < i2.xmin || s4 > i2.xmax || n < i2.ymin || n > i2.ymax) && (e2.elevationTile = t4);
    });
  }
  _selectTilesClosestResolution(e) {
    const t4 = e.layer.tileInfo, i2 = this._findNearestDemResolutionLODIndex(t4, e.options.demResolution);
    e.selectTilesAtLOD(i2);
  }
  _findNearestDemResolutionLODIndex(e, t4) {
    const i2 = t4 / $(e.spatialReference);
    let s4 = e.lods[0], n = 0;
    for (let o = 1; o < e.lods.length; o++) {
      const t5 = e.lods[o];
      Math.abs(t5.resolution - i2) < Math.abs(s4.resolution - i2) && (s4 = t5, n = o);
    }
    return n;
  }
  async _selectTilesFinestContiguous(e, t4) {
    const i2 = I(e.layer.tileInfo, e.options.minDemResolution);
    await this._selectTilesFinestContiguousAt(e, i2, t4);
  }
  async _selectTilesFinestContiguousAt(e, i2, s4) {
    const n = e.layer;
    if (e.selectTilesAtLOD(i2), i2 < 0) return;
    const a = n.tilemapCache, r3 = e.getTilesToFetch();
    try {
      if (a) await y(Promise.all(r3.map((e2) => a.fetchAvailability(e2.level, e2.row, e2.col, { signal: s4 }))), s4);
      else if (await this._populateElevationTiles(e, s4), !e.allElevationTilesFetched()) throw e.clearElevationTiles(), new s2("elevation-query:has-unavailable-tiles");
    } catch (c3) {
      w(c3), await this._selectTilesFinestContiguousAt(e, i2 - 1, s4);
    }
  }
  async _populateElevationTiles(e, t4) {
    const i2 = e.getTilesToFetch(), s4 = {}, l2 = e.options.cache, r3 = e.options.noDataValue, c3 = i2.map(async (i3) => {
      if (null == i3.id) return;
      const o = `${e.layer.uid}:${i3.id}:${r3}`, a = r(l2) ? l2.get(o) : null, c4 = r(a) ? a : await e.layer.fetchTile(i3.level, i3.row, i3.col, { noDataValue: r3, signal: t4 });
      r(l2) && l2.put(o, c4), s4[i3.id] = new r2(i3, c4);
    });
    await y(E(c3), t4), e.populateElevationTiles(s4);
  }
  async _selectTilesAuto(t4, i2) {
    this._selectTilesAutoFinest(t4), this._reduceTilesForMaximumRequests(t4);
    const s4 = t4.layer.tilemapCache;
    if (!s4) return this._selectTilesAutoPrefetchUpsample(t4, i2);
    const n = t4.getTilesToFetch(), a = {}, r3 = n.map(async (t5) => {
      const n2 = new t2(null, 0, 0, 0, u2()), o = await b(s4.fetchAvailabilityUpsample(t5.level, t5.row, t5.col, n2, { signal: i2 }));
      false !== o.ok ? null != t5.id && (a[t5.id] = n2) : w(o.error);
    });
    await y(Promise.all(r3), i2), t4.remapTiles(a);
  }
  _reduceTilesForMaximumRequests(e) {
    const t4 = e.layer.tileInfo;
    let i2 = 0;
    const s4 = {}, n = (e2) => {
      null != e2.id && (e2.id in s4 ? s4[e2.id]++ : (s4[e2.id] = 1, i2++));
    }, o = (e2) => {
      if (null == e2.id) return;
      const t5 = s4[e2.id];
      1 === t5 ? (delete s4[e2.id], i2--) : s4[e2.id] = t5 - 1;
    };
    e.forEachTileToFetch(n, o);
    let l2 = true;
    for (; l2 && (l2 = false, e.forEachTileToFetch((s5) => {
      i2 <= e.options.maximumAutoTileRequests || (o(s5), t4.upsampleTile(s5) && (l2 = true), n(s5));
    }, o), l2); ) ;
  }
  _selectTilesAutoFinest(e) {
    const t4 = I(e.layer.tileInfo, e.options.minDemResolution);
    e.selectTilesAtLOD(t4, e.options.maximumAutoTileRequests);
  }
  async _selectTilesAutoPrefetchUpsample(e, t4) {
    const i2 = e.layer.tileInfo;
    await this._populateElevationTiles(e, t4);
    let s4 = false;
    e.forEachTileToFetch((e2, t5) => {
      i2.upsampleTile(e2) ? s4 = true : t5();
    }), s4 && await this._selectTilesAutoPrefetchUpsample(e, t4);
  }
  _sampleGeometryWithElevation(e) {
    e.geometry.coordinates.forEach((t4) => {
      const i2 = t4.elevationTile;
      let s4 = e.options.noDataValue;
      if (i2) {
        const e2 = i2.sample(t4.x, t4.y);
        r(e2) ? s4 = e2 : t4.elevationTile = null;
      }
      t4.z = s4;
    });
  }
  _extractSampleInfo(e) {
    const t4 = e.layer.tileInfo, i2 = $(t4.spatialReference);
    return e.geometry.coordinates.map((s4) => {
      let n = -1;
      if (s4.elevationTile && s4.elevationTile !== e.outsideExtentTile) {
        n = t4.lodAt(s4.elevationTile.tile.level).resolution * i2;
      }
      return { demResolution: n };
    });
  }
};
var x2 = class _x {
  export() {
    return this._exporter(this.coordinates, this.spatialReference);
  }
  clone(e) {
    const t4 = new _x();
    return t4.geometry = this.geometry, t4.spatialReference = this.spatialReference, t4.coordinates = e || this.coordinates.map((e2) => e2.clone()), t4._exporter = this._exporter, t4;
  }
  async project(e, t4) {
    if (this.spatialReference.equals(e)) return this.clone();
    await _n([{ source: this.spatialReference, dest: e }], { signal: t4 });
    const i2 = new u({ spatialReference: this.spatialReference, points: this.coordinates.map((e2) => [e2.x, e2.y]) }), s4 = rn(i2, e);
    if (!s4) return null;
    const n = this.coordinates.map((e2, t5) => {
      const i3 = e2.clone(), n2 = s4.points[t5];
      return i3.x = n2[0], i3.y = n2[1], i3;
    }), o = this.clone(n);
    return o.spatialReference = e, o;
  }
  static fromGeometry(e) {
    const t4 = new _x();
    if (t4.geometry = e, t4.spatialReference = e.spatialReference, e instanceof _x) t4.coordinates = e.coordinates.map((e2) => e2.clone()), t4._exporter = (t5, i2) => {
      const s4 = e.clone(t5);
      return s4.spatialReference = i2, s4;
    };
    else switch (e.type) {
      case "point": {
        const i2 = e, { hasZ: s4, hasM: n } = i2;
        t4.coordinates = s4 && n ? [new _(i2.x, i2.y, i2.z, i2.m)] : s4 ? [new _(i2.x, i2.y, i2.z)] : n ? [new _(i2.x, i2.y, null, i2.m)] : [new _(i2.x, i2.y)], t4._exporter = (t5, i3) => e.hasM ? new w2(t5[0].x, t5[0].y, t5[0].z, t5[0].m, i3) : new w2(t5[0].x, t5[0].y, t5[0].z, i3);
        break;
      }
      case "multipoint": {
        const i2 = e, { hasZ: s4, hasM: n } = i2;
        t4.coordinates = s4 && n ? i2.points.map((e2) => new _(e2[0], e2[1], e2[2], e2[3])) : s4 ? i2.points.map((e2) => new _(e2[0], e2[1], e2[2])) : n ? i2.points.map((e2) => new _(e2[0], e2[1], null, e2[2])) : i2.points.map((e2) => new _(e2[0], e2[1])), t4._exporter = (t5, i3) => e.hasM ? new u({ points: t5.map((e2) => [e2.x, e2.y, e2.z, e2.m]), hasZ: true, hasM: true, spatiaReference: i3 }) : new u(t5.map((e2) => [e2.x, e2.y, e2.z]), i3);
        break;
      }
      case "polyline": {
        const i2 = e, s4 = [], n = [], { hasZ: o, hasM: l2 } = e;
        let a = 0;
        for (const e2 of i2.paths) if (n.push([a, a + e2.length]), a += e2.length, o && l2) for (const t5 of e2) s4.push(new _(t5[0], t5[1], t5[2], t5[3]));
        else if (o) for (const t5 of e2) s4.push(new _(t5[0], t5[1], t5[2]));
        else if (l2) for (const t5 of e2) s4.push(new _(t5[0], t5[1], null, t5[2]));
        else for (const t5 of e2) s4.push(new _(t5[0], t5[1]));
        t4.coordinates = s4, t4._exporter = (t5, i3) => {
          const s5 = e.hasM ? t5.map((e2) => [e2.x, e2.y, e2.z, e2.m]) : t5.map((e2) => [e2.x, e2.y, e2.z]), o2 = n.map((e2) => s5.slice(e2[0], e2[1]));
          return new m({ paths: o2, hasM: e.hasM, hasZ: true, spatialReference: i3 });
        };
        break;
      }
    }
    return t4;
  }
};
var _ = class __ {
  constructor(e, t4, i2 = null, s4 = null, n = null, o = null) {
    this.x = e, this.y = t4, this.z = i2, this.m = s4, this.tile = n, this.elevationTile = o;
  }
  clone() {
    return new __(this.x, this.y, this.z, this.m);
  }
};
var E2 = class {
  constructor(e, t4) {
    this.layer = e, this.options = t4;
  }
};
var R3 = class extends E2 {
  constructor(e, t4, i2) {
    super(e, i2), this.outSpatialReference = t4, this.type = "geometry";
  }
  selectTilesAtLOD(e) {
    if (e < 0) this.geometry.coordinates.forEach((e2) => {
      e2.tile = null;
    });
    else {
      const t4 = this.layer.tileInfo, i2 = t4.lods[e].level;
      this.geometry.coordinates.forEach((e2) => {
        e2.tile = t4.tileAt(i2, e2.x, e2.y);
      });
    }
  }
  allElevationTilesFetched() {
    return !this.geometry.coordinates.some((e) => !e.elevationTile);
  }
  clearElevationTiles() {
    for (const e of this.geometry.coordinates) e.elevationTile !== this.outsideExtentTile && (e.elevationTile = null);
  }
  populateElevationTiles(e) {
    var _a;
    for (const t4 of this.geometry.coordinates) !t4.elevationTile && ((_a = t4.tile) == null ? void 0 : _a.id) && (t4.elevationTile = e[t4.tile.id]);
  }
  remapTiles(e) {
    var _a;
    for (const t4 of this.geometry.coordinates) {
      const i2 = (_a = t4.tile) == null ? void 0 : _a.id;
      t4.tile = i2 ? e[i2] : null;
    }
  }
  getTilesToFetch() {
    var _a;
    const e = {}, t4 = [];
    for (const i2 of this.geometry.coordinates) {
      const s4 = i2.tile;
      if (!s4) continue;
      const n = (_a = i2.tile) == null ? void 0 : _a.id;
      i2.elevationTile || !n || e[n] || (e[n] = s4, t4.push(s4));
    }
    return t4;
  }
  forEachTileToFetch(e) {
    for (const t4 of this.geometry.coordinates) t4.tile && !t4.elevationTile && e(t4.tile, () => {
      t4.tile = null;
    });
  }
};
var q = class extends E2 {
  constructor(e, t4, i2, s4) {
    super(e, i2), this.type = "extent", this.elevationTiles = [], this._candidateTiles = [], this._fetchedCandidates = /* @__PURE__ */ new Set(), this.extent = t4.intersection(e.fullExtent), this.maskExtents = s4;
  }
  selectTilesAtLOD(e, t4) {
    const i2 = this._maximumLodForRequests(t4), s4 = Math.min(i2, e);
    s4 < 0 ? this._candidateTiles.length = 0 : this._selectCandidateTilesCoveringExtentAt(s4);
  }
  _maximumLodForRequests(e) {
    const t4 = this.layer.tileInfo;
    if (!e) return t4.lods.length - 1;
    const i2 = this.extent;
    if (t(i2)) return -1;
    for (let s4 = t4.lods.length - 1; s4 >= 0; s4--) {
      const n = t4.lods[s4], o = n.resolution * t4.size[0], l2 = n.resolution * t4.size[1];
      if (Math.ceil(i2.width / o) * Math.ceil(i2.height / l2) <= e) return s4;
    }
    return -1;
  }
  allElevationTilesFetched() {
    return this._candidateTiles.length === this.elevationTiles.length;
  }
  clearElevationTiles() {
    this.elevationTiles.length = 0, this._fetchedCandidates.clear();
  }
  populateElevationTiles(e) {
    for (const t4 of this._candidateTiles) {
      const i2 = t4.id && e[t4.id];
      i2 && (this._fetchedCandidates.add(t4), this.elevationTiles.push(i2));
    }
  }
  remapTiles(e) {
    this._candidateTiles = this._uniqueNonOverlappingTiles(this._candidateTiles.map((t4) => e[t4.id]));
  }
  getTilesToFetch() {
    return this._candidateTiles;
  }
  forEachTileToFetch(e, t4) {
    const i2 = this._candidateTiles;
    this._candidateTiles = [], i2.forEach((i3) => {
      if (this._fetchedCandidates.has(i3)) return void (t4 && t4(i3));
      let s4 = false;
      e(i3, () => s4 = true), s4 ? t4 && t4(i3) : this._candidateTiles.push(i3);
    }), this._candidateTiles = this._uniqueNonOverlappingTiles(this._candidateTiles, t4);
  }
  _uniqueNonOverlappingTiles(e, t4) {
    const i2 = {}, s4 = [];
    for (const o of e) {
      const e2 = o.id;
      e2 && !i2[e2] ? (i2[e2] = o, s4.push(o)) : t4 && t4(o);
    }
    const n = s4.sort((e2, t5) => e2.level - t5.level);
    return n.filter((e2, i3) => {
      for (let s5 = 0; s5 < i3; s5++) {
        const i4 = n[s5].extent;
        if (i4 && e2.extent && R(i4, e2.extent)) return t4 && t4(e2), false;
      }
      return true;
    });
  }
  _selectCandidateTilesCoveringExtentAt(e) {
    this._candidateTiles.length = 0;
    const t4 = this.extent;
    if (t(t4)) return;
    const i2 = this.layer.tileInfo, n = i2.lods[e], o = i2.tileAt(n.level, t4.xmin, t4.ymin), l2 = o.extent;
    if (t(l2)) return;
    const a = n.resolution * i2.size[0], r3 = n.resolution * i2.size[1], c3 = Math.ceil((t4.xmax - l2[0]) / a), u4 = Math.ceil((t4.ymax - l2[1]) / r3);
    for (let s4 = 0; s4 < u4; s4++) for (let e2 = 0; e2 < c3; e2++) {
      const t5 = new t2(null, o.level, o.row - s4, o.col + e2);
      i2.updateTileInfo(t5), this._tileIsMasked(t5) || this._candidateTiles.push(t5);
    }
  }
  _tileIsMasked(e) {
    return !!this.maskExtents && this.maskExtents.some((t4) => e.extent && R(t4, e.extent));
  }
};
function I(e, t4 = 0) {
  let i2 = e.lods.length - 1;
  if (t4 > 0) {
    const s4 = t4 / $(e.spatialReference), n = e.lods.findIndex((e2) => e2.resolution < s4);
    0 === n ? i2 = 0 : n > 0 && (i2 = n - 1);
  }
  return i2;
}
var A = { maximumAutoTileRequests: 20, noDataValue: 0, returnSampleInfo: false, demResolution: "auto", minDemResolution: 0 };
export {
  g2 as ElevationQuery,
  x2 as GeometryDescriptor,
  I as getFinestLodIndex
};
//# sourceMappingURL=ElevationQuery-4T42SFZD.js.map
