import {
  r
} from "./chunk-AW4AS2UW.js";
import {
  r as r2
} from "./chunk-WXFAAYJL.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/rest/utils.js
function i(r3, t) {
  return t ? { ...t, query: { ...r3 ?? {}, ...t.query } } : { query: r3 };
}
function f(r3) {
  return "string" == typeof r3 ? L(r3) : p(r3);
}
function s2(r3, t, o) {
  const e = {};
  for (const n in r3) {
    if ("declaredClass" === n) continue;
    const i2 = r3[n];
    if (null != i2 && "function" != typeof i2) if (Array.isArray(i2)) {
      e[n] = [];
      for (let r4 = 0; r4 < i2.length; r4++) e[n][r4] = s2(i2[r4]);
    } else if ("object" == typeof i2) if (i2.toJSON) {
      const r4 = i2.toJSON(o && o[n]);
      e[n] = t ? r4 : JSON.stringify(r4);
    } else e[n] = t ? i2 : JSON.stringify(i2);
    else e[n] = i2;
  }
  return e;
}
function u(o, e) {
  var _a, _b;
  return r(o) && (e || s.apiKey) ? e || s.apiKey : (_b = (_a = r2) == null ? void 0 : _a.findCredential(o)) == null ? void 0 : _b.token;
}

export {
  i,
  f,
  s2 as s,
  u
};
//# sourceMappingURL=chunk-XBS7QZIQ.js.map
