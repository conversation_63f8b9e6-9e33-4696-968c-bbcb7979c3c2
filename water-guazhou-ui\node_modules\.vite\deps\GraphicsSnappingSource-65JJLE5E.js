import {
  i,
  p as p2
} from "./chunk-I25FGXOD.js";
import "./chunk-3LXNA5FS.js";
import {
  n,
  r as r2,
  r2 as r3
} from "./chunk-TNXJBJO3.js";
import {
  g
} from "./chunk-5HHROPN6.js";
import "./chunk-TZ4YPYDT.js";
import {
  ee
} from "./chunk-D6BU5EUE.js";
import "./chunk-QYYMKGDW.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-ZK5O2DLX.js";
import "./chunk-CCFNWAA2.js";
import {
  p
} from "./chunk-OHAM27JH.js";
import "./chunk-JLELSJK5.js";
import "./chunk-FRO3RSRO.js";
import {
  B
} from "./chunk-6KZTVN32.js";
import "./chunk-ANH6666P.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-FAMLZKHJ.js";
import "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import "./chunk-NMGVJ2ZX.js";
import "./chunk-INH5JU5P.js";
import "./chunk-HLLJFAS4.js";
import "./chunk-6DAQTVXB.js";
import "./chunk-FUIIMETN.js";
import "./chunk-2CLVPBYJ.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-M4ZUXRA3.js";
import "./chunk-WJKHSSMC.js";
import "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-2RO3UJ2R.js";
import {
  d,
  l2
} from "./chunk-UQWZJZ2S.js";
import "./chunk-5S4W3ME5.js";
import {
  y as y3
} from "./chunk-CDZ24ELJ.js";
import "./chunk-VHLK35TF.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import {
  ct
} from "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import {
  s
} from "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-UCWK623G.js";
import {
  j
} from "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import {
  An,
  _n,
  rn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  a as a3
} from "./chunk-Q4VCSCSY.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-3WUI7ZKG.js";
import {
  a as a2,
  h,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import {
  o
} from "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import {
  v as v3
} from "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  v as v2
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  L,
  f,
  y as y2
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import {
  v
} from "./chunk-GZGAQUSK.js";
import {
  e as e2,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/GraphicsSnappingSource.js
var x = "graphics-collections";
var z = class extends a3(v2) {
  get updating() {
    return this.updatingHandles.updating;
  }
  get _hasZ() {
    const e3 = this.view;
    return r(e3) && "3d" === e3.type && "map-notes" !== this.layerSource.layer.type;
  }
  get _snappingElevationAligner() {
    const { view: e3 } = this, { layer: t2 } = this.layerSource, o2 = r(e3) && "3d" === e3.type;
    if (!o2 || "map-notes" === t2.type) return r2();
    const s2 = async (o3, s3) => (await y2(e3.whenLayerView(t2), s3)).elevationAlignPointsInFeatures(o3, s3);
    return r2(o2, { elevationInfo: t2.elevationInfo, alignPointsInFeatures: s2, spatialReference: e3.spatialReference });
  }
  get _snappingElevationFilter() {
    const { view: e3 } = this, t2 = r(e3) && "3d" === e3.type && "map-notes" !== this.layerSource.layer.type;
    return r3(t2);
  }
  get _symbologySnappingFetcher() {
    const { view: e3 } = this, { layer: t2 } = this.layerSource, o2 = r(e3) && "3d" === e3.type, s2 = this._extrudedPolygonSymbolsCount > 0;
    return o2 && "map-notes" !== t2.type && s2 ? n(s2, async (o3, s3) => {
      const r4 = await e3.whenLayerView(t2);
      return f(s3), r4.queryForSymbologySnapping({ candidates: o3, spatialReference: e3.spatialReference }, s3);
    }) : n();
  }
  constructor(e3) {
    super(e3), this.availability = 1, this._sources = { multipoint: null, point: null, polygon: null, polyline: null }, this._loadedWkids = /* @__PURE__ */ new Set(), this._loadedWkts = /* @__PURE__ */ new Set(), this._pendingAdds = [], this._extrudedPolygonSymbolsCount = 0;
  }
  destroy() {
    for (const e3 of this._pendingAdds) e3.task.abort();
    this._pendingAdds.length = 0, this._mapSources((e3) => this._destroySource(e3));
  }
  initialize() {
    this.updatingHandles.add(() => this.getGraphicsLayers(), (e4) => {
      this.updatingHandles.removeHandles(x);
      for (const t3 of e4) this._addMany(t3.graphics.toArray()), this.handles.add([t3.on("graphic-update", (e5) => this._onGraphicUpdate(e5)), this.updatingHandles.addOnCollectionChange(() => t3.graphics, (e5) => this._onGraphicsChanged(e5))], x);
    }, h);
    const { view: e3 } = this, { layer: t2 } = this.layerSource;
    r(e3) && "3d" === e3.type && "map-notes" !== t2.type && this.addHandles([e3.elevationProvider.on("elevation-change", ({ context: e4 }) => {
      y3(e4, t2.elevationInfo) && this._snappingElevationAligner.notifyElevationSourceChange();
    }), l(() => t2.elevationInfo, () => this._snappingElevationAligner.notifyElevationSourceChange(), h), a2(() => t2, ["edits", "apply-edits", "graphic-update"], () => this._symbologySnappingFetcher.notifySymbologyChange())]);
  }
  async fetchCandidates(e3, t2) {
    const { point: o2 } = e3, s2 = await L(this._mapSources((o3) => this._fetchCandidatesForSource(o3, e3, t2)));
    f(t2);
    const r4 = this._getGroundElevation, i2 = s2.flat().map((e4) => i(e4, r4));
    return d(o2, i2), i2;
  }
  get _getGroundElevation() {
    return p2(this.view);
  }
  async _fetchCandidatesForSource(e3, t2, o2) {
    var _a;
    const s2 = l2(t2, ((_a = e2(this.view)) == null ? void 0 : _a.type) ?? "2d"), r4 = await e3.queryEngine.executeQueryForSnapping(s2, o2);
    f(o2);
    const i2 = await this._snappingElevationAligner.alignCandidates(r4.candidates, o2);
    f(o2);
    const a4 = await this._symbologySnappingFetcher.fetch(i2, o2);
    f(o2);
    const p3 = 0 === a4.length ? i2 : [...i2, ...a4];
    return this._snappingElevationFilter.filter(s2, p3);
  }
  refresh() {
  }
  _onGraphicUpdate(e3) {
    if (this.getGraphicsLayers().some((t2) => t2.graphics.includes(e3.graphic))) switch (e3.property) {
      case "geometry":
      case "visible":
        this._remove(e3.graphic), this._addMany([e3.graphic]);
    }
  }
  _onGraphicsChanged(e3) {
    for (const t2 of e3.removed) this._remove(t2);
    this._addMany(e3.added);
  }
  _addMany(e3) {
    const t2 = [], o2 = /* @__PURE__ */ new Map();
    for (const s2 of e3) t(s2.geometry) || (this._needsInitializeProjection(s2.geometry.spatialReference) ? (t2.push(s2.geometry.spatialReference), o2.set(s2.uid, s2)) : this._add(s2));
    this._createPendingAdd(t2, o2);
  }
  _createPendingAdd(e3, t2) {
    if (!e3.length) return;
    const r4 = j(async (o2) => {
      await _n(e3.map((e4) => ({ source: e4, dest: this.spatialReference })), { signal: o2 }), this._markLoadedSpatialReferences(e3);
      for (const [, e4] of t2) this._add(e4);
    });
    this.updatingHandles.addPromise(r4.promise);
    const i2 = { task: r4, graphics: t2 }, n2 = () => v(this._pendingAdds, i2);
    r4.promise.then(n2, n2), this._pendingAdds.push(i2);
  }
  _markLoadedSpatialReferences(e3) {
    for (const t2 of e3) null != t2.wkid && this._loadedWkids.add(t2.wkid), null != t2.wkt && this._loadedWkts.add(t2.wkt);
  }
  _add(e3) {
    if (t(e3.geometry) || !e3.visible) return;
    let t2 = e3.geometry;
    if ("mesh" === t2.type) return;
    "extent" === t2.type && (t2 = v3.fromExtent(t2));
    const o2 = this._ensureSource(t2.type);
    if (t(o2)) return;
    const s2 = this._createOptimizedFeature(e3.uid, t2);
    t(s2) || (o2.featureStore.add(s2), B(e3.symbol) && this._extrudedPolygonSymbolsCount++);
  }
  _needsInitializeProjection(e3) {
    return (null == e3.wkid || !this._loadedWkids.has(e3.wkid)) && ((null == e3.wkt || !this._loadedWkts.has(e3.wkt)) && !An(e3, this.spatialReference));
  }
  _createOptimizedFeature(e3, t2) {
    const o2 = rn(p(t2), this.spatialReference);
    if (!o2) return null;
    const s2 = this._ensureGeometryHasZ(o2), r4 = ct(s2, this._hasZ, false);
    return new s(r4, { [H]: e3 }, null, e3);
  }
  _ensureGeometryHasZ(e3) {
    if (!this._hasZ) return e3;
    const t2 = (e4) => {
      for (; e4.length < 3; ) e4.push(0);
    }, o2 = e3.clone();
    switch (o2.hasZ = true, o2.type) {
      case "point":
        o2.z = o2.z ?? 0;
        break;
      case "multipoint":
        o2.points.forEach(t2);
        break;
      case "polyline":
        o2.paths.forEach((e4) => e4.forEach(t2));
        break;
      case "polygon":
        o2.rings.forEach((e4) => e4.forEach(t2));
    }
    return o2;
  }
  _ensureSource(e3) {
    const t2 = this._sources[e3];
    if (r(t2)) return t2;
    const o2 = this._createSource(e3);
    return this._sources[e3] = o2, o2;
  }
  _createSource(e3) {
    const t2 = o.toJSON(e3), o2 = this._hasZ, s2 = new g({ geometryType: t2, hasZ: o2, hasM: false });
    return { featureStore: s2, queryEngine: new ee({ featureStore: s2, fields: [{ name: H, type: "esriFieldTypeOID", alias: H }], geometryType: t2, hasM: false, hasZ: o2, objectIdField: H, spatialReference: this.spatialReference, scheduler: r(this.view) && "3d" === this.view.type ? this.view.resourceController.scheduler : null }), type: e3 };
  }
  _remove(e3) {
    this._mapSources((t2) => this._removeFromSource(t2, e3));
    for (const t2 of this._pendingAdds) t2.graphics.delete(e3.uid), 0 === t2.graphics.size && t2.task.abort();
  }
  _removeFromSource(e3, t2) {
    const o2 = t2.uid;
    e3.featureStore.has(o2) && (e3.featureStore.removeById(t2.uid), B(t2.symbol) && this._extrudedPolygonSymbolsCount--);
  }
  _destroySource(e3) {
    e3.queryEngine.destroy(), this._sources[e3.type] = null;
  }
  _mapSources(e3) {
    const { point: t2, polygon: o2, polyline: s2, multipoint: r4 } = this._sources, n2 = [];
    return r(t2) && n2.push(e3(t2)), r(o2) && n2.push(e3(o2)), r(s2) && n2.push(e3(s2)), r(r4) && n2.push(e3(r4)), n2;
  }
};
e([y()], z.prototype, "getGraphicsLayers", void 0), e([y({ constructOnly: true })], z.prototype, "layerSource", void 0), e([y({ constructOnly: true })], z.prototype, "spatialReference", void 0), e([y({ constructOnly: true })], z.prototype, "view", void 0), e([y({ readOnly: true })], z.prototype, "updating", null), e([y({ readOnly: true })], z.prototype, "availability", void 0), e([y()], z.prototype, "_hasZ", null), e([y()], z.prototype, "_snappingElevationAligner", null), e([y()], z.prototype, "_snappingElevationFilter", null), e([y()], z.prototype, "_symbologySnappingFetcher", null), e([y()], z.prototype, "_extrudedPolygonSymbolsCount", void 0), e([y()], z.prototype, "_getGroundElevation", null), z = e([a("esri.views.interactive.snapping.featureSources.GraphicsSnappingSource")], z);
var H = "OBJECTID";
export {
  z as GraphicsSnappingSource
};
//# sourceMappingURL=GraphicsSnappingSource-65JJLE5E.js.map
