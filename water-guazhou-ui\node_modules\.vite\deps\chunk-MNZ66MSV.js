import {
  a as a3
} from "./chunk-3MMXGUA5.js";
import {
  c
} from "./chunk-AYK667QL.js";
import {
  v as v3
} from "./chunk-Z2LHI3D7.js";
import {
  f as f2,
  s as s2
} from "./chunk-XBS7QZIQ.js";
import {
  x
} from "./chunk-KE7SPCM7.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  o as o2
} from "./chunk-PEEUPDEG.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  o,
  s
} from "./chunk-KUPAGB4V.js";
import {
  e,
  v as v2
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  b
} from "./chunk-HP475EI3.js";
import {
  a as a2,
  v
} from "./chunk-EKX3LLYN.js";

// node_modules/@arcgis/core/rest/geoprocessor/GPOptions.js
var i = class extends v2 {
  constructor() {
    super(...arguments), this.outSpatialReference = null, this.processExtent = null, this.processSpatialReference = null, this.returnFeatureCollection = false, this.returnM = false, this.returnZ = false;
  }
};
e([y({ type: f })], i.prototype, "outSpatialReference", void 0), e([y({ type: w })], i.prototype, "processExtent", void 0), e([y({ type: f })], i.prototype, "processSpatialReference", void 0), e([y({ nonNullable: true })], i.prototype, "returnFeatureCollection", void 0), e([y({ nonNullable: true })], i.prototype, "returnM", void 0), e([y({ nonNullable: true })], i.prototype, "returnZ", void 0), i = e([a("esri.rest.geoprocessor.GPOptions")], i), i.from = b(i);
var n = i;

// node_modules/@arcgis/core/layers/support/MapImage.js
var p = class extends l {
  constructor() {
    super(...arguments), this.extent = null, this.height = null, this.href = null, this.opacity = 1, this.rotation = 0, this.scale = null, this.visible = true, this.width = null;
  }
};
e([y({ type: w })], p.prototype, "extent", void 0), e([y()], p.prototype, "height", void 0), e([y()], p.prototype, "href", void 0), e([y()], p.prototype, "opacity", void 0), e([y()], p.prototype, "rotation", void 0), e([y()], p.prototype, "scale", void 0), e([y()], p.prototype, "visible", void 0), e([y()], p.prototype, "width", void 0), p = e([a("esri.layer.support.MapImage")], p);
var i2 = p;

// node_modules/@arcgis/core/rest/support/DataFile.js
var e2 = class extends l {
  constructor(r) {
    super(r), this.itemId = null, this.url = null;
  }
};
e([y({ type: String, json: { read: { source: "itemID" }, write: { target: "itemID" } } })], e2.prototype, "itemId", void 0), e([y({ type: String, json: { write: true } })], e2.prototype, "url", void 0), e2 = e([a("esri.rest.support.DataFile")], e2);
var p2 = e2;

// node_modules/@arcgis/core/rest/support/ParameterValue.js
var o3 = new s({ GPBoolean: "boolean", GPDataFile: "data-file", GPDate: "date", GPDouble: "double", GPFeatureRecordSetLayer: "feature-record-set-layer", GPField: "field", GPLinearUnit: "linear-unit", GPLong: "long", GPRasterData: "raster-data", GPRasterDataLayer: "raster-data-layer", GPRecordSet: "record-set", GPString: "string", "GPMultiValue:GPBoolean": "multi-value", "GPMultiValue:GPDataFile": "multi-value", "GPMultiValue:GPDate": "multi-value", "GPMultiValue:GPDouble": "multi-value", "GPMultiValue:GPFeatureRecordSetLayer": "multi-value", "GPMultiValue:GPField": "multi-value", "GPMultiValue:GPLinearUnit": "multi-value", "GPMultiValue:GPLong": "multi-value", "GPMultiValue:GPRasterData": "multi-value", "GPMultiValue:GPRasterDataLayer": "multi-value", "GPMultiValue:GPRecordSet": "multi-value", "GPMultiValue:GPString": "multi-value" });
var i3 = class extends l {
  constructor(e4) {
    super(e4), this.dataType = null, this.value = null;
  }
};
e([o2(o3, { ignoreUnknown: false })], i3.prototype, "dataType", void 0), e([y()], i3.prototype, "value", void 0), i3 = e([a("esri.rest.support.ParameterValue")], i3);
var s3 = i3;

// node_modules/@arcgis/core/rest/support/RasterData.js
var e3 = class extends l {
  constructor(r) {
    super(r), this.format = null, this.itemId = null, this.url = null;
  }
};
e([y()], e3.prototype, "format", void 0), e([y({ json: { read: { source: "itemID" }, write: { target: "itemID" } } })], e3.prototype, "itemId", void 0), e([y()], e3.prototype, "url", void 0), e3 = e([a("esri.rest.support.RasterData")], e3);
var p3 = e3;

// node_modules/@arcgis/core/rest/geoprocessor/utils.js
async function m(t, r, u, l2, s5) {
  const n2 = {}, i4 = {}, c2 = [];
  return p4(l2, c2, n2), v3(c2).then((a4) => {
    const { outSpatialReference: c3, processExtent: m3, processSpatialReference: p5, returnFeatureCollection: f4, returnM: G, returnZ: v4 } = u, { path: S } = f2(t);
    for (const e4 in n2) {
      const t2 = n2[e4];
      i4[e4] = a4.slice(t2[0], t2[1]);
    }
    const y3 = c3 ? c3.wkid || c3 : null, D = p5 ? p5.wkid || p5 : null, J = "execute" === r ? { returnFeatureCollection: f4 || void 0, returnM: G || void 0, returnZ: v4 || void 0 } : null, M = P({ ...m3 ? { context: { extent: m3, outSR: y3, processSR: D } } : { "env:outSR": y3, "env:processSR": D }, ...l2, ...J, f: "json" }, null, i4), N = { ...s5, query: M };
    return U(`${S}/${r}`, N);
  });
}
function p4(e4, a4, t) {
  for (const r in e4) {
    const o5 = e4[r];
    if (o5 && "object" == typeof o5 && o5 instanceof x) {
      const { features: e5 } = o5;
      t[r] = [a4.length, a4.length + e5.length], e5.forEach((e6) => {
        a4.push(e6.geometry);
      });
    }
  }
}
function f3(e4) {
  const a4 = e4.dataType, o5 = s3.fromJSON(e4);
  switch (a4) {
    case "GPBoolean":
    case "GPDouble":
    case "GPLong":
    case "GPString":
    case "GPMultiValue:GPBoolean":
    case "GPMultiValue:GPDouble":
    case "GPMultiValue:GPLong":
    case "GPMultiValue:GPString":
      return o5;
    case "GPDate":
      o5.value = new Date(o5.value);
      break;
    case "GPDataFile":
      o5.value = p2.fromJSON(o5.value);
      break;
    case "GPLinearUnit":
      o5.value = c.fromJSON(o5.value);
      break;
    case "GPFeatureRecordSetLayer":
    case "GPRecordSet": {
      const a5 = e4.value.url;
      o5.value = a5 ? p2.fromJSON(o5.value) : x.fromJSON(o5.value);
      break;
    }
    case "GPRasterData":
    case "GPRasterDataLayer": {
      const a5 = e4.value.mapImage;
      o5.value = a5 ? i2.fromJSON(a5) : p3.fromJSON(o5.value);
      break;
    }
    case "GPField":
      o5.value = y2.fromJSON(o5.value);
      break;
    case "GPMultiValue:GPDate": {
      const e5 = o5.value;
      o5.value = e5.map((e6) => new Date(e6));
      break;
    }
    case "GPMultiValue:GPDataFile":
      o5.value = o5.value.map((e5) => p2.fromJSON(e5));
      break;
    case "GPMultiValue:GPLinearUnit":
      o5.value = o5.value.map((e5) => c.fromJSON(e5));
      break;
    case "GPMultiValue:GPFeatureRecordSetLayer":
    case "GPMultiValue:GPRecordSet":
      o5.value = o5.value.map((e5) => x.fromJSON(e5));
      break;
    case "GPMultiValue:GPRasterData":
    case "GPMultiValue:GPRasterDataLayer":
      o5.value = o5.value.map((e5) => e5 ? i2.fromJSON(e5) : p3.fromJSON(o5.value));
      break;
    case "GPMultiValue:GPField":
      o5.value = o5.value.map((e5) => y2.fromJSON(e5));
  }
  return o5;
}
function P(e4, a4, t) {
  for (const r in e4) {
    const a5 = e4[r];
    Array.isArray(a5) ? e4[r] = JSON.stringify(a5.map((e5) => P({ item: e5 }, true).item)) : a5 instanceof Date && (e4[r] = a5.getTime());
  }
  return s2(e4, a4, t);
}

// node_modules/@arcgis/core/rest/geoprocessor/execute.js
async function o4(o5, m3, a4, p5) {
  return a4 = n.from(a4 || {}), m(o5, "execute", a4, m3 ?? {}, p5).then((s5) => {
    const t = s5.data.results || [], o6 = s5.data.messages || [];
    return { results: t.map(f3), messages: o6.map((s6) => a3.fromJSON(s6)) };
  });
}

// node_modules/@arcgis/core/rest/support/JobInfo.js
var m2;
var h = o()({ esriJobCancelled: "job-cancelled", esriJobCancelling: "job-cancelling", esriJobDeleted: "job-deleted", esriJobDeleting: "job-deleting", esriJobTimedOut: "job-timed-out", esriJobExecuting: "job-executing", esriJobFailed: "job-failed", esriJobNew: "job-new", esriJobSubmitted: "job-submitted", esriJobSucceeded: "job-succeeded", esriJobWaiting: "job-waiting" }, { ignoreUnknown: false });
var d = m2 = class extends l {
  constructor(e4) {
    super(e4), this.jobId = null, this.jobStatus = null, this.messages = null, this.progress = null, this.requestOptions = null, this.sourceUrl = null, this._timer = void 0;
  }
  async cancelJob(e4) {
    const { jobId: t, sourceUrl: o5 } = this, { path: r } = f2(o5), i4 = { ...this.requestOptions, ...e4, query: { f: "json" } };
    this._clearTimer();
    const a4 = `${r}/jobs/${t}/cancel`, { data: c2 } = await U(a4, i4), { messages: n2, jobStatus: l2, progress: p5 } = m2.fromJSON(c2);
    return this.set({ messages: n2, jobStatus: l2, progress: p5 }), this;
  }
  destroy() {
    clearInterval(this._timer);
  }
  async checkJobStatus(e4) {
    const { path: t } = f2(this.sourceUrl), o5 = { ...this.requestOptions, ...e4, query: { f: "json" } }, r = `${t}/jobs/${this.jobId}`, { data: i4 } = await U(r, o5), { messages: a4, jobStatus: c2, progress: n2 } = m2.fromJSON(i4);
    return this.set({ messages: a4, jobStatus: c2, progress: n2 }), this;
  }
  async fetchResultData(e4, t, o5) {
    t = n.from(t || {});
    const { returnFeatureCollection: r, returnM: i4, returnZ: a4, outSpatialReference: c2 } = t, { path: n2 } = f2(this.sourceUrl), j = P({ returnFeatureCollection: r, returnM: i4, returnZ: a4, outSR: c2, returnType: "data", f: "json" }, null), m3 = { ...this.requestOptions, ...o5, query: j }, h2 = `${n2}/jobs/${this.jobId}/results/${e4}`, { data: d2 } = await U(h2, m3);
    return f3(d2);
  }
  async fetchResultImage(e4, t, o5) {
    const { path: r } = f2(this.sourceUrl), i4 = { ...t.toJSON(), f: "json" }, a4 = P(i4), c2 = { ...this.requestOptions, ...o5, query: a4 }, n2 = `${r}/jobs/${this.jobId}/results/${e4}`, { data: l2 } = await U(n2, c2);
    return f3(l2);
  }
  async fetchResultMapImageLayer() {
    const { path: e4 } = f2(this.sourceUrl), s5 = e4.indexOf("/GPServer/"), t = `${e4.substring(0, s5)}/MapServer/jobs/${this.jobId}`;
    return new (0, (await import("./@arcgis_core_layers_MapImageLayer.js")).default)({ url: t });
  }
  async waitForJobCompletion(e4 = {}) {
    const { interval: s5 = 1e3, signal: t, statusCallback: o5 } = e4;
    return new Promise((e5, a4) => {
      v(t, () => {
        this._clearTimer(), a4(a2());
      }), this._clearTimer();
      const c2 = setInterval(() => {
        this._timer || a4(a2()), this.checkJobStatus(this.requestOptions).then((s6) => {
          const { jobStatus: t2 } = s6;
          switch (this.jobStatus = t2, t2) {
            case "job-succeeded":
              this._clearTimer(), e5(this);
              break;
            case "job-submitted":
            case "job-executing":
            case "job-waiting":
            case "job-new":
              o5 && o5(this);
              break;
            case "job-cancelled":
            case "job-cancelling":
            case "job-deleted":
            case "job-deleting":
            case "job-timed-out":
            case "job-failed":
              this._clearTimer(), a4(this);
          }
        });
      }, s5);
      this._timer = c2;
    });
  }
  _clearTimer() {
    clearInterval(this._timer), this._timer = void 0;
  }
};
e([y()], d.prototype, "jobId", void 0), e([o2(h, { ignoreUnknown: false })], d.prototype, "jobStatus", void 0), e([y({ type: [a3] })], d.prototype, "messages", void 0), e([y()], d.prototype, "progress", void 0), e([y()], d.prototype, "requestOptions", void 0), e([y({ json: { write: true } })], d.prototype, "sourceUrl", void 0), d = m2 = e([a("esri.rest.support.JobInfo")], d);
var g = d;

// node_modules/@arcgis/core/rest/geoprocessor/submitJob.js
async function s4(s5, m3, n2, f4) {
  return n2 = n.from(n2 || {}), m(s5, "submitJob", n2, m3 ?? {}, f4).then((o5) => {
    const r = g.fromJSON(o5.data);
    return r.sourceUrl = s5, r;
  });
}

export {
  o4 as o,
  s4 as s
};
//# sourceMappingURL=chunk-MNZ66MSV.js.map
