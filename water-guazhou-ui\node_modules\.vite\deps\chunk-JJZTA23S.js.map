{"version": 3, "sources": ["../../@arcgis/core/symbols/cim/ExpandedCIM.js", "../../@arcgis/core/views/2d/layers/support/webStyleUtils.js", "../../@arcgis/core/views/2d/layers/support/cimSymbolUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{WGLGeometryType as e,WGLSymbologyType as t}from\"../../views/2d/engine/webgl/enums.js\";import{createMaterialKey as a}from\"../../views/2d/engine/webgl/materialKey/MaterialKey.js\";const s={marker:e.MARKER,fill:e.FILL,line:e.LINE,text:e.TEXT};class l{constructor(e,l,n,r){const c={minScale:l?.minScale,maxScale:l?.maxScale},m=i(c);this.layers=e,this.data=l,this.hash=this._createHash()+m,this.rendererKey=n;const o={isOutline:!1,placement:null,symbologyType:t.DEFAULT,vvFlags:n};for(const t of e){const e=s[t.type];o.isOutline=\"line\"===t.type&&t.isOutline,t.materialKey=a(e,o),t.maxVVSize=r,t.scaleInfo=c,t.templateHash+=m}}get type(){return\"expanded-cim\"}_createHash(){let e=\"\";for(const t of this.layers)e+=t.templateHash;return e}}function i(e){return e.minScale||e.maxScale?e.minScale+\"-\"+e.maxScale:\"\"}export{l as ExpandedCIM};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isDevEnvironment as t,adjustStaticAGOUrl as r}from\"../../../../core/devEnvironmentUtils.js\";import e from\"../../../../core/Error.js\";import{isSome as o}from\"../../../../core/maybe.js\";import{throwIfAborted as n}from\"../../../../core/promiseUtils.js\";import{urlToObject as s}from\"../../../../core/urlUtils.js\";import m from\"../../../../portal/Portal.js\";import{f as l}from\"../../../../chunks/persistableUrlUtils.js\";import{fetchStyle as a,Style2DUrlTemplate as i,requestJSON as c,makeCIMSymbolRef as y,symbolUrlFromStyleItem as f}from\"../../../../symbols/support/styleUtils.js\";async function u(t,r,o){if(!t.name)throw new e(\"style-symbol-reference-name-missing\",\"Missing name in style symbol reference\");if(t.styleName&&\"Esri2DPointSymbolsStyle\"===t.styleName)return p(t,o);try{return b(await a(t,r,o),t.name,r,o)}catch(s){return n(s),null}}async function p(t,r){const e=i.replace(/\\{SymbolName\\}/gi,t.name);try{const t=await c(e,r);return y(t.data)}catch(o){return n(o),null}}async function b(a,i,u,p){const b=a.data,d={portal:u&&o(u.portal)?u.portal:m.getDefault(),url:s(a.baseUrl),origin:\"portal-item\"},j=b.items.find((t=>t.name===i));if(!j){throw new e(\"symbolstyleutils:symbol-name-not-found\",`The symbol name '${i}' could not be found`,{symbolName:i})}let h=l(f(j,\"cimRef\"),d);t()&&(h=r(h));try{const t=await c(h,p);return y(t.data)}catch(w){return n(w),null}}export{u as fetchCIMSymbolReference};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{analyzeCIMSymbol as e}from\"../../../../symbols/cim/cimAnalyzer.js\";import{ExpandedCIM as t}from\"../../../../symbols/cim/ExpandedCIM.js\";import{fetchCIMSymbolReference as a}from\"./webStyleUtils.js\";const r=async(a,r,i)=>new t(await e(a.data,r,i),a.data,a.renderer<PERSON>ey,a.maxVVSize);async function i(e,t,i,n){if(!e)return null;if(\"cim\"===e.type)return r(e,t,i);if(\"web-style\"===e.type){const l={type:\"cim\",data:await a(e,null,n)??void 0,rendererKey:e.render<PERSON><PERSON><PERSON>,maxVVSize:e.maxVVSize};return r(l,t,i)}return e}function n(e){if(!e)return null;const{avoidSDFRasterization:t,type:a,cim:r,url:i,materialHash:n}=e,l={cim:r,type:a,mosaicHash:n,url:i,size:null,dashTemplate:null,path:null,text:null,fontName:null,animatedSymbolProperties:null,avoidSDFRasterization:t};switch(a){case\"marker\":l.size=e.size,l.path=e.path,l.animatedSymbolProperties=e.animatedSymbolProperties;break;case\"line\":l.dashTemplate=e.dashTemplate;break;case\"text\":l.text=e.text,l.fontName=e.fontName}return l}export{n as cimLayerToRasterizationInfo,i as expandSymbol};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIwL,IAAMA,KAAE,EAAC,QAAO,EAAE,QAAO,MAAK,EAAE,MAAK,MAAK,EAAE,MAAK,MAAK,EAAE,KAAI;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAY,GAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAC,UAASH,MAAA,gBAAAA,GAAG,UAAS,UAASA,MAAA,gBAAAA,GAAG,SAAQ,GAAE,IAAE,EAAEG,EAAC;AAAE,SAAK,SAAO,GAAE,KAAK,OAAKH,IAAE,KAAK,OAAK,KAAK,YAAY,IAAE,GAAE,KAAK,cAAYC;AAAE,UAAM,IAAE,EAAC,WAAU,OAAG,WAAU,MAAK,eAAc,EAAE,SAAQ,SAAQA,GAAC;AAAE,eAAU,KAAK,GAAE;AAAC,YAAMG,KAAEL,GAAE,EAAE,IAAI;AAAE,QAAE,YAAU,WAAS,EAAE,QAAM,EAAE,WAAU,EAAE,cAAYM,GAAED,IAAE,CAAC,GAAE,EAAE,YAAUF,IAAE,EAAE,YAAUC,IAAE,EAAE,gBAAc;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM;AAAA,EAAc;AAAA,EAAC,cAAa;AAAC,QAAI,IAAE;AAAG,eAAU,KAAK,KAAK,OAAO,MAAG,EAAE;AAAa,WAAO;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,YAAU,EAAE,WAAS,EAAE,WAAS,MAAI,EAAE,WAAS;AAAE;;;ACAjO,eAAe,EAAE,GAAEG,IAAE,GAAE;AAAC,MAAG,CAAC,EAAE,KAAK,OAAM,IAAI,EAAE,uCAAsC,wCAAwC;AAAE,MAAG,EAAE,aAAW,8BAA4B,EAAE,UAAU,QAAOC,GAAE,GAAE,CAAC;AAAE,MAAG;AAAC,WAAOC,GAAE,MAAM,EAAE,GAAEF,IAAE,CAAC,GAAE,EAAE,MAAKA,IAAE,CAAC;AAAA,EAAC,SAAOG,IAAE;AAAC,WAAO,EAAEA,EAAC,GAAE;AAAA,EAAI;AAAC;AAAC,eAAeF,GAAE,GAAED,IAAE;AAAC,QAAM,IAAE,EAAE,QAAQ,oBAAmB,EAAE,IAAI;AAAE,MAAG;AAAC,UAAMI,KAAE,MAAMC,GAAE,GAAEL,EAAC;AAAE,WAAO,EAAEI,GAAE,IAAI;AAAA,EAAC,SAAO,GAAE;AAAC,WAAO,EAAE,CAAC,GAAE;AAAA,EAAI;AAAC;AAAC,eAAeF,GAAEI,IAAEC,IAAEC,IAAEP,IAAE;AAAC,QAAMC,KAAEI,GAAE,MAAKG,KAAE,EAAC,QAAOD,MAAG,EAAEA,GAAE,MAAM,IAAEA,GAAE,SAAON,GAAE,WAAW,GAAE,KAAI,EAAEI,GAAE,OAAO,GAAE,QAAO,cAAa,GAAED,KAAEH,GAAE,MAAM,KAAM,OAAG,EAAE,SAAOK,EAAE;AAAE,MAAG,CAACF,IAAE;AAAC,UAAM,IAAI,EAAE,0CAAyC,oBAAoBE,EAAC,wBAAuB,EAAC,YAAWA,GAAC,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,EAAE,EAAEF,IAAE,QAAQ,GAAEI,EAAC;AAAE,EAAAC,GAAE,MAAI,IAAE,EAAE,CAAC;AAAG,MAAG;AAAC,UAAM,IAAE,MAAML,GAAE,GAAEJ,EAAC;AAAE,WAAO,EAAE,EAAE,IAAI;AAAA,EAAC,SAAO,GAAE;AAAC,WAAO,EAAE,CAAC,GAAE;AAAA,EAAI;AAAC;;;ACAhpC,IAAMU,KAAE,OAAMC,IAAED,IAAEE,OAAI,IAAI,EAAE,MAAM,EAAED,GAAE,MAAKD,IAAEE,EAAC,GAAED,GAAE,MAAKA,GAAE,aAAYA,GAAE,SAAS;AAAE,eAAeC,GAAE,GAAE,GAAEA,IAAEC,IAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,MAAG,UAAQ,EAAE,KAAK,QAAOH,GAAE,GAAE,GAAEE,EAAC;AAAE,MAAG,gBAAc,EAAE,MAAK;AAAC,UAAME,KAAE,EAAC,MAAK,OAAM,MAAK,MAAM,EAAE,GAAE,MAAKD,EAAC,KAAG,QAAO,aAAY,EAAE,aAAY,WAAU,EAAE,UAAS;AAAE,WAAOH,GAAEI,IAAE,GAAEF,EAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,QAAK,EAAC,uBAAsB,GAAE,MAAKD,IAAE,KAAID,IAAE,KAAIE,IAAE,cAAaC,GAAC,IAAE,GAAEC,KAAE,EAAC,KAAIJ,IAAE,MAAKC,IAAE,YAAWE,IAAE,KAAID,IAAE,MAAK,MAAK,cAAa,MAAK,MAAK,MAAK,MAAK,MAAK,UAAS,MAAK,0BAAyB,MAAK,uBAAsB,EAAC;AAAE,UAAOD,IAAE;AAAA,IAAC,KAAI;AAAS,MAAAG,GAAE,OAAK,EAAE,MAAKA,GAAE,OAAK,EAAE,MAAKA,GAAE,2BAAyB,EAAE;AAAyB;AAAA,IAAM,KAAI;AAAO,MAAAA,GAAE,eAAa,EAAE;AAAa;AAAA,IAAM,KAAI;AAAO,MAAAA,GAAE,OAAK,EAAE,MAAKA,GAAE,WAAS,EAAE;AAAA,EAAQ;AAAC,SAAOA;AAAC;", "names": ["s", "l", "n", "r", "c", "e", "f", "r", "p", "b", "s", "t", "j", "a", "i", "u", "d", "c", "r", "a", "i", "n", "l"]}