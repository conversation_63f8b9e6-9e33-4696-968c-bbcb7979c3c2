import {
  p,
  w
} from "./chunk-63M4K32A.js";
import {
  R as R2,
  g,
  j
} from "./chunk-R5MYQRRS.js";
import {
  E,
  R,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  r as r2
} from "./chunk-LTKA6OKA.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/contains.js
var t2 = [0, 0];
function r3(t3, r4) {
  return !!r(r4) && f2(t3, r4.x, r4.y, r4.z);
}
function i(n, t3) {
  if (!t3.points || t3.points.length) return false;
  for (const r4 of t3.points) if (!u(n, r4)) return false;
  return true;
}
function o(n, t3) {
  const { xmin: r4, ymin: i3, zmin: o3, xmax: u3, ymax: e3, zmax: c3 } = t3;
  return n.hasZ && t3.hasZ ? f2(n, r4, i3, o3) && f2(n, r4, e3, o3) && f2(n, u3, e3, o3) && f2(n, u3, i3, o3) && f2(n, r4, i3, c3) && f2(n, r4, e3, c3) && f2(n, u3, e3, c3) && f2(n, u3, i3, c3) : f2(n, r4, i3) && f2(n, r4, e3) && f2(n, u3, e3) && f2(n, u3, i3);
}
function u(n, t3) {
  return f2(n, t3[0], t3[1]);
}
function e2(n, t3) {
  return f2(n, t3[0], t3[1], t3[2]);
}
function f2(n, t3, r4, i3) {
  return t3 >= n.xmin && t3 <= n.xmax && r4 >= n.ymin && r4 <= n.ymax && (null == i3 || !n.hasZ || i3 >= n.zmin && i3 <= n.zmax);
}
function c(n, r4) {
  return t2[1] = r4.y, t2[0] = r4.x, m(n, t2);
}
function m(n, t3) {
  return s(n.rings, t3);
}
function s(n, t3) {
  if (!n) return false;
  if (x(n)) return a2(false, n, t3);
  let r4 = false;
  for (let i3 = 0, o3 = n.length; i3 < o3; i3++) r4 = a2(r4, n[i3], t3);
  return r4;
}
function x(n) {
  return !Array.isArray(n[0][0]);
}
function a2(n, t3, r4) {
  const [i3, o3] = r4;
  let u3 = n, e3 = 0;
  for (let f5 = 0, c3 = t3.length; f5 < c3; f5++) {
    e3++, e3 === c3 && (e3 = 0);
    const [n2, r5] = t3[f5], [m3, s3] = t3[e3];
    (r5 < o3 && s3 >= o3 || s3 < o3 && r5 >= o3) && n2 + (o3 - r5) / (s3 - r5) * (m3 - n2) < i3 && (u3 = !u3);
  }
  return u3;
}

// node_modules/@arcgis/core/geometry/support/intersectsBase.js
function i2(t3, e3) {
  return r3(t3, e3);
}
function o2(n, t3) {
  const e3 = n.hasZ && t3.hasZ;
  let r4, i3, o3;
  if (n.xmin <= t3.xmin) {
    if (r4 = t3.xmin, n.xmax < r4) return false;
  } else if (r4 = n.xmin, t3.xmax < r4) return false;
  if (n.ymin <= t3.ymin) {
    if (i3 = t3.ymin, n.ymax < i3) return false;
  } else if (i3 = n.ymin, t3.ymax < i3) return false;
  if (e3 && t3.hasZ) {
    if (n.zmin <= t3.zmin) {
      if (o3 = t3.zmin, n.zmax < o3) return false;
    } else if (o3 = n.zmin, t3.zmax < o3) return false;
  }
  return true;
}
function f3(n, t3) {
  const { points: i3, hasZ: o3 } = t3, f5 = o3 ? e2 : u;
  for (const e3 of i3) if (f5(n, e3)) return true;
  return false;
}
var s2 = [0, 0];
var u2 = [0, 0];
var c2 = [0, 0];
var m2 = [0, 0];
var l = [s2, u2, c2, m2];
var a3 = [[c2, s2], [s2, u2], [u2, m2], [m2, c2]];
function x2(n, t3) {
  return y2(n, t3.rings);
}
function y2(n, r4) {
  s2[0] = n.xmin, s2[1] = n.ymax, u2[0] = n.xmax, u2[1] = n.ymax, c2[0] = n.xmin, c2[1] = n.ymin, m2[0] = n.xmax, m2[1] = n.ymin;
  for (const e3 of l) if (s(r4, e3)) return true;
  for (const t3 of r4) {
    if (!t3.length) continue;
    let r5 = t3[0];
    if (u(n, r5)) return true;
    for (let i3 = 1; i3 < t3.length; i3++) {
      const o3 = t3[i3];
      if (u(n, o3) || z(r5, o3, a3)) return true;
      r5 = o3;
    }
  }
  return false;
}
function h(n, t3) {
  s2[0] = n.xmin, s2[1] = n.ymax, u2[0] = n.xmax, u2[1] = n.ymax, c2[0] = n.xmin, c2[1] = n.ymin, m2[0] = n.xmax, m2[1] = n.ymin;
  const r4 = t3.paths;
  for (const i3 of r4) {
    if (!r4.length) continue;
    let t4 = i3[0];
    if (u(n, t4)) return true;
    for (let r5 = 1; r5 < i3.length; r5++) {
      const o3 = i3[r5];
      if (u(n, o3) || z(t4, o3, a3)) return true;
      t4 = o3;
    }
  }
  return false;
}
var g2 = [0, 0];
function p2(n) {
  for (let t3 = 0; t3 < n.length; t3++) {
    const e3 = n[t3];
    for (let i3 = 0; i3 < e3.length - 1; i3++) {
      const r5 = e3[i3], o3 = e3[i3 + 1];
      for (let e4 = t3 + 1; e4 < n.length; e4++) for (let t4 = 0; t4 < n[e4].length - 1; t4++) {
        const i4 = n[e4][t4], f5 = n[e4][t4 + 1];
        if (G(r5, o3, i4, f5, g2) && !(g2[0] === r5[0] && g2[1] === r5[1] || g2[0] === i4[0] && g2[1] === i4[1] || g2[0] === o3[0] && g2[1] === o3[1] || g2[0] === f5[0] && g2[1] === f5[1])) return true;
      }
    }
    const r4 = e3.length;
    if (!(r4 <= 4)) for (let n2 = 0; n2 < r4 - 3; n2++) {
      let t4 = r4 - 1;
      0 === n2 && (t4 = r4 - 2);
      const i3 = e3[n2], o3 = e3[n2 + 1];
      for (let r5 = n2 + 2; r5 < t4; r5++) {
        const n3 = e3[r5], t5 = e3[r5 + 1];
        if (G(i3, o3, n3, t5, g2) && !(g2[0] === i3[0] && g2[1] === i3[1] || g2[0] === n3[0] && g2[1] === n3[1] || g2[0] === o3[0] && g2[1] === o3[1] || g2[0] === t5[0] && g2[1] === t5[1])) return true;
      }
    }
  }
  return false;
}
function z(n, t3, e3) {
  for (let r4 = 0; r4 < e3.length; r4++) if (G(n, t3, e3[r4][0], e3[r4][1])) return true;
  return false;
}
function G(n, t3, e3, r4, i3) {
  const [o3, f5] = n, [s3, u3] = t3, [c3, m3] = e3, [l2, a4] = r4, x3 = l2 - c3, y3 = o3 - c3, h2 = s3 - o3, g3 = a4 - m3, p3 = f5 - m3, z3 = u3 - f5, G2 = g3 * h2 - x3 * z3;
  if (0 === G2) return false;
  const Z2 = (x3 * p3 - g3 * y3) / G2, P = (h2 * p3 - z3 * y3) / G2;
  return Z2 >= 0 && Z2 <= 1 && P >= 0 && P <= 1 && (i3 && (i3[0] = o3 + Z2 * (s3 - o3), i3[1] = f5 + Z2 * (u3 - f5)), true);
}
function Z(n) {
  switch (n) {
    case "esriGeometryEnvelope":
    case "extent":
      return o2;
    case "esriGeometryMultipoint":
    case "multipoint":
      return f3;
    case "esriGeometryPoint":
    case "point":
      return i2;
    case "esriGeometryPolygon":
    case "polygon":
      return x2;
    case "esriGeometryPolyline":
    case "polyline":
      return h;
  }
}

// node_modules/@arcgis/core/geometry/Extent.js
var f4;
function d(t3) {
  return t3 && ("esri.geometry.SpatialReference" === t3.declaredClass || null != t3.wkid);
}
function z2(t3, i3, e3) {
  return null == i3 ? e3 : null == e3 ? i3 : t3(i3, e3);
}
var M = f4 = class extends p {
  constructor(...t3) {
    super(...t3), this.type = "extent", this.xmin = 0, this.ymin = 0, this.mmin = void 0, this.zmin = void 0, this.xmax = 0, this.ymax = 0, this.mmax = void 0, this.zmax = void 0;
  }
  normalizeCtorArgs(t3, i3, e3, s3, n) {
    return d(t3) ? { spatialReference: t3, xmin: 0, ymin: 0, xmax: 0, ymax: 0 } : "object" == typeof t3 ? (t3.spatialReference = null == t3.spatialReference ? f.WGS84 : t3.spatialReference, t3) : { xmin: t3, ymin: i3, xmax: e3, ymax: s3, spatialReference: n ?? f.WGS84 };
  }
  static fromBounds(t3, i3) {
    return new f4({ xmin: t3[0], ymin: t3[1], xmax: t3[2], ymax: t3[3], spatialReference: i3 });
  }
  static fromPoint(t3) {
    return new f4({ xmin: t3.x, ymin: t3.y, zmin: t3.z, xmax: t3.x, ymax: t3.y, zmax: t3.z, spatialReference: t3.spatialReference });
  }
  get cache() {
    return this.commitProperty("xmin"), this.commitProperty("ymin"), this.commitProperty("zmin"), this.commitProperty("mmin"), this.commitProperty("xmax"), this.commitProperty("ymax"), this.commitProperty("zmax"), this.commitProperty("mmax"), this.commitProperty("spatialReference"), {};
  }
  get center() {
    const t3 = new w({ x: 0.5 * (this.xmin + this.xmax), y: 0.5 * (this.ymin + this.ymax), spatialReference: this.spatialReference });
    return this.hasZ && (t3.z = 0.5 * (this.zmin + this.zmax)), this.hasM && (t3.m = 0.5 * (this.mmin + this.mmax)), t3;
  }
  get extent() {
    return this.clone();
  }
  get hasM() {
    return null != this.mmin && null != this.mmax;
  }
  get hasZ() {
    return null != this.zmin && null != this.zmax;
  }
  get height() {
    return Math.abs(this.ymax - this.ymin);
  }
  get width() {
    return Math.abs(this.xmax - this.xmin);
  }
  centerAt(t3) {
    const i3 = this.center;
    return null != t3.z && this.hasZ ? this.offset(t3.x - i3.x, t3.y - i3.y, t3.z - i3.z) : this.offset(t3.x - i3.x, t3.y - i3.y);
  }
  clone() {
    const t3 = new f4();
    return t3.xmin = this.xmin, t3.ymin = this.ymin, t3.xmax = this.xmax, t3.ymax = this.ymax, t3.spatialReference = this.spatialReference, null != this.zmin && (t3.zmin = this.zmin, t3.zmax = this.zmax), null != this.mmin && (t3.mmin = this.mmin, t3.mmax = this.mmax), t3;
  }
  contains(t3) {
    if (!t3) return false;
    const i3 = this.spatialReference, e3 = t3.spatialReference;
    return i3 && e3 && !i3.equals(e3) && g(i3, e3) && (t3 = i3.isWebMercator ? R2(t3) : j(t3, true)), "point" === t3.type ? r3(this, t3) : "extent" === t3.type && o(this, t3);
  }
  equals(t3) {
    if (this === t3) return true;
    if (t(t3)) return false;
    const e3 = this.spatialReference, s3 = t3.spatialReference;
    return e3 && s3 && !e3.equals(s3) && g(e3, s3) && (t3 = e3.isWebMercator ? R2(t3) : j(t3, true)), this.xmin === t3.xmin && this.ymin === t3.ymin && this.zmin === t3.zmin && this.mmin === t3.mmin && this.xmax === t3.xmax && this.ymax === t3.ymax && this.zmax === t3.zmax && this.mmax === t3.mmax;
  }
  expand(t3) {
    const i3 = 0.5 * (1 - t3), e3 = this.width * i3, s3 = this.height * i3;
    if (this.xmin += e3, this.ymin += s3, this.xmax -= e3, this.ymax -= s3, this.hasZ) {
      const t4 = (this.zmax - this.zmin) * i3;
      this.zmin += t4, this.zmax -= t4;
    }
    if (this.hasM) {
      const t4 = (this.mmax - this.mmin) * i3;
      this.mmin += t4, this.mmax -= t4;
    }
    return this;
  }
  intersects(t3) {
    if (t(t3)) return false;
    "mesh" === t3.type && (t3 = t3.extent);
    const e3 = this.spatialReference, s3 = t3.spatialReference;
    e3 && s3 && !E(e3, s3) && g(e3, s3) && (t3 = e3.isWebMercator ? R2(t3) : j(t3, true));
    return Z(t3.type)(this, t3);
  }
  normalize() {
    const t3 = this._normalize(false, true);
    return Array.isArray(t3) ? t3 : [t3];
  }
  offset(t3, i3, e3) {
    return this.xmin += t3, this.ymin += i3, this.xmax += t3, this.ymax += i3, null != e3 && (this.zmin += e3, this.zmax += e3), this;
  }
  shiftCentralMeridian() {
    return this._normalize(true);
  }
  union(t3) {
    return this === t3 || (this.xmin = Math.min(this.xmin, t3.xmin), this.ymin = Math.min(this.ymin, t3.ymin), this.xmax = Math.max(this.xmax, t3.xmax), this.ymax = Math.max(this.ymax, t3.ymax), (this.hasZ || t3.hasZ) && (this.zmin = z2(Math.min, this.zmin, t3.zmin), this.zmax = z2(Math.max, this.zmax, t3.zmax)), (this.hasM || t3.hasM) && (this.mmin = z2(Math.min, this.mmin, t3.mmin), this.mmax = z2(Math.max, this.mmax, t3.mmax))), this;
  }
  intersection(t3) {
    return this === t3 ? this : t(t3) || !this.intersects(t3) ? null : (this.xmin = Math.max(this.xmin, t3.xmin), this.ymin = Math.max(this.ymin, t3.ymin), this.xmax = Math.min(this.xmax, t3.xmax), this.ymax = Math.min(this.ymax, t3.ymax), (this.hasZ || t3.hasZ) && (this.zmin = z2(Math.max, this.zmin, t3.zmin), this.zmax = z2(Math.min, this.zmax, t3.zmax)), (this.hasM || t3.hasM) && (this.mmin = z2(Math.max, this.mmin, t3.mmin), this.mmax = z2(Math.min, this.mmax, t3.mmax)), this);
  }
  toJSON(t3) {
    return this.write({}, t3);
  }
  _shiftCM(t3 = R(this.spatialReference)) {
    if (!t3 || !this.spatialReference) return this;
    const i3 = this.spatialReference, s3 = this._getCM(t3);
    if (s3) {
      const n = i3.isWebMercator ? j(s3) : s3;
      this.xmin -= s3.x, this.xmax -= s3.x, i3.isWebMercator || (n.x = this._normalizeX(n.x, t3).x), this.spatialReference = new f(r2((i3.isWGS84 ? t3.altTemplate : null) ?? t3.wkTemplate, { Central_Meridian: n.x }));
    }
    return this;
  }
  _getCM(t3) {
    let i3 = null;
    const [e3, s3] = t3.valid, n = this.xmin, m3 = this.xmax;
    return n >= e3 && n <= s3 && (m3 >= e3 && m3 <= s3) || (i3 = this.center), i3;
  }
  _normalize(t3, i3, e3) {
    const s3 = this.spatialReference;
    if (!s3) return this;
    const n = e3 ?? R(s3);
    if (null == n) return this;
    const m3 = this._getParts(n).map((t4) => t4.extent);
    if (m3.length < 2) return m3[0] || this;
    if (m3.length > 2) return t3 ? this._shiftCM(n) : this.set({ xmin: n.valid[0], xmax: n.valid[1] });
    if (t3) return this._shiftCM(n);
    if (i3) return m3;
    let r4 = true, a4 = true;
    return m3.forEach((t4) => {
      t4.hasZ || (r4 = false), t4.hasM || (a4 = false);
    }), { rings: m3.map((t4) => {
      const i4 = [[t4.xmin, t4.ymin], [t4.xmin, t4.ymax], [t4.xmax, t4.ymax], [t4.xmax, t4.ymin], [t4.xmin, t4.ymin]];
      if (r4) {
        const e4 = (t4.zmax - t4.zmin) / 2;
        for (let t5 = 0; t5 < i4.length; t5++) i4[t5].push(e4);
      }
      if (a4) {
        const e4 = (t4.mmax - t4.mmin) / 2;
        for (let t5 = 0; t5 < i4.length; t5++) i4[t5].push(e4);
      }
      return i4;
    }), hasZ: r4, hasM: a4, spatialReference: s3 };
  }
  _getParts(t3) {
    let i3 = this.cache._parts;
    if (!i3) {
      i3 = [];
      const { ymin: e4, ymax: s4, spatialReference: n } = this, m3 = this.width, r4 = this.xmin, a4 = this.xmax;
      let h2;
      t3 = t3 || R(n);
      const [o3, x3] = t3.valid;
      h2 = this._normalizeX(this.xmin, t3);
      const l2 = h2.x, c3 = h2.frameId;
      h2 = this._normalizeX(this.xmax, t3);
      const y3 = h2.x, u3 = h2.frameId, d2 = l2 === y3 && m3 > 0;
      if (m3 > 2 * x3) {
        const t4 = new f4(r4 < a4 ? l2 : y3, e4, x3, s4, n), m4 = new f4(o3, e4, r4 < a4 ? y3 : l2, s4, n), h3 = new f4(0, e4, x3, s4, n), p3 = new f4(o3, e4, 0, s4, n), d3 = [], z3 = [];
        t4.contains(h3) && d3.push(c3), t4.contains(p3) && z3.push(c3), m4.contains(h3) && d3.push(u3), m4.contains(p3) && z3.push(u3);
        for (let i4 = c3 + 1; i4 < u3; i4++) d3.push(i4), z3.push(i4);
        i3.push({ extent: t4, frameIds: [c3] }, { extent: m4, frameIds: [u3] }, { extent: h3, frameIds: d3 }, { extent: p3, frameIds: z3 });
      } else l2 > y3 || d2 ? i3.push({ extent: new f4(l2, e4, x3, s4, n), frameIds: [c3] }, { extent: new f4(o3, e4, y3, s4, n), frameIds: [u3] }) : i3.push({ extent: new f4(l2, e4, y3, s4, n), frameIds: [c3] });
      this.cache._parts = i3;
    }
    const e3 = this.hasZ, s3 = this.hasM;
    if (e3 || s3) {
      const t4 = {};
      e3 && (t4.zmin = this.zmin, t4.zmax = this.zmax), s3 && (t4.mmin = this.mmin, t4.mmax = this.mmax);
      for (let e4 = 0; e4 < i3.length; e4++) i3[e4].extent.set(t4);
    }
    return i3;
  }
  _normalizeX(t3, i3) {
    const [e3, s3] = i3.valid, n = 2 * s3;
    let m3, r4 = 0;
    return t3 > s3 ? (m3 = Math.ceil(Math.abs(t3 - s3) / n), t3 -= m3 * n, r4 = m3) : t3 < e3 && (m3 = Math.ceil(Math.abs(t3 - e3) / n), t3 += m3 * n, r4 = -m3), { x: t3, frameId: r4 };
  }
};
e([y({ readOnly: true })], M.prototype, "cache", null), e([y({ readOnly: true })], M.prototype, "center", null), e([y({ readOnly: true })], M.prototype, "extent", null), e([y({ readOnly: true, json: { write: { enabled: false, overridePolicy: null } } })], M.prototype, "hasM", null), e([y({ readOnly: true, json: { write: { enabled: false, overridePolicy: null } } })], M.prototype, "hasZ", null), e([y({ readOnly: true })], M.prototype, "height", null), e([y({ readOnly: true })], M.prototype, "width", null), e([y({ type: Number, json: { type: [Number, String], write: { enabled: true, allowNull: true } } })], M.prototype, "xmin", void 0), e([y({ type: Number, json: { write: true } })], M.prototype, "ymin", void 0), e([y({ type: Number, json: { origins: { "web-scene": { write: false } }, write: { overridePolicy() {
  return { enabled: this.hasM };
} } } })], M.prototype, "mmin", void 0), e([y({ type: Number, json: { origins: { "web-scene": { write: false } }, write: { overridePolicy() {
  return { enabled: this.hasZ };
} } } })], M.prototype, "zmin", void 0), e([y({ type: Number, json: { write: true } })], M.prototype, "xmax", void 0), e([y({ type: Number, json: { write: true } })], M.prototype, "ymax", void 0), e([y({ type: Number, json: { origins: { "web-scene": { write: false } }, write: { overridePolicy() {
  return { enabled: this.hasM };
} } } })], M.prototype, "mmax", void 0), e([y({ type: Number, json: { origins: { "web-scene": { write: false } }, write: { overridePolicy() {
  return { enabled: this.hasZ };
} } } })], M.prototype, "zmax", void 0), M = f4 = e([a("esri.geometry.Extent")], M), M.prototype.toJSON.isDefaultToJSON = true;
var w2 = M;

export {
  r3 as r,
  i,
  c,
  s,
  o2 as o,
  x2 as x,
  p2 as p,
  G,
  Z,
  w2 as w
};
//# sourceMappingURL=chunk-XTO3XXZ3.js.map
