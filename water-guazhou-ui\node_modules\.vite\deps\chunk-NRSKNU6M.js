import {
  a as a2
} from "./chunk-Q4VCSCSY.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/layers/support/ExportWMSImageParameters.js
var o = { visible: "visibleSublayers" };
var l = class extends a2(v) {
  constructor(e2) {
    super(e2), this.scale = 0;
  }
  set layer(e2) {
    this._get("layer") !== e2 && (this._set("layer", e2), this.handles.remove("layer"), e2 && this.handles.add([e2.sublayers.on("change", () => this.notifyChange("visibleSublayers")), e2.on("wms-sublayer-update", (e3) => this.notifyChange(o[e3.propertyName]))], "layer"));
  }
  get layers() {
    return this.visibleSublayers.filter(({ name: e2 }) => e2).map(({ name: e2 }) => e2).join();
  }
  get version() {
    this.commitProperty("layers");
    const e2 = this.layer;
    return e2 && e2.commitProperty("imageTransparency"), (this._get("version") || 0) + 1;
  }
  get visibleSublayers() {
    const { layer: e2, scale: r } = this, s = e2 == null ? void 0 : e2.sublayers, t = [], a3 = (e3) => {
      const { minScale: s2, maxScale: o2, sublayers: l2, visible: i } = e3;
      i && (0 === r || (0 === s2 || r <= s2) && (0 === o2 || r >= o2)) && (l2 ? l2.forEach(a3) : t.push(e3));
    };
    return s == null ? void 0 : s.forEach(a3), t;
  }
  toJSON() {
    const { layer: e2, layers: r } = this, { imageFormat: s, imageTransparency: t, version: a3 } = e2;
    return { format: s, request: "GetMap", service: "WMS", styles: "", transparent: t ? "TRUE" : "FALSE", version: a3, layers: r };
  }
};
e([y()], l.prototype, "layer", null), e([y({ readOnly: true })], l.prototype, "layers", null), e([y({ type: Number })], l.prototype, "scale", void 0), e([y({ readOnly: true })], l.prototype, "version", null), e([y({ readOnly: true })], l.prototype, "visibleSublayers", null), l = e([a("esri.layers.support.ExportWMSImageParameters")], l);

export {
  l
};
//# sourceMappingURL=chunk-NRSKNU6M.js.map
