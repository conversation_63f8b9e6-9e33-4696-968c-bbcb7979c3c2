import {
  R
} from "./chunk-KYDW2SHL.js";
import {
  i
} from "./chunk-JTTSDQPH.js";
import {
  r
} from "./chunk-6DXPU43Z.js";
import {
  w
} from "./chunk-SMSZBVG5.js";
import {
  T
} from "./chunk-WAPZ634R.js";
import {
  e
} from "./chunk-MZ267CZB.js";
import {
  u
} from "./chunk-I7WHRVHF.js";

// node_modules/@arcgis/core/views/2d/engine/BitmapTile.js
var r2 = class extends r {
  constructor(e2, s, r3, i2, a, n2, m = null) {
    super(e2, s, r3, i2, a, n2), this.bitmap = new R(m, { immutable: false, requestRenderOnSourceChangedEnabled: false }), this.bitmap.coordScale = [a, n2], this.bitmap.once("isReady", () => this.ready());
  }
  destroy() {
    super.destroy(), this.bitmap.destroy();
  }
  beforeRender(e2) {
    super.beforeRender(e2), this.bitmap.beforeRender(e2);
  }
  afterRender(e2) {
    super.afterRender(e2), this.bitmap.afterRender(e2);
  }
  set stencilRef(e2) {
    this.bitmap.stencilRef = e2;
  }
  get stencilRef() {
    return this.bitmap.stencilRef;
  }
  _createTransforms() {
    return { dvs: e(), tileMat3: e() };
  }
  setTransform(e2) {
    super.setTransform(e2), this.bitmap.transforms.dvs = this.transforms.dvs;
  }
  onAttach() {
    this.bitmap.stage = this.stage;
  }
  onDetach() {
    this.bitmap && (this.bitmap.stage = null);
  }
};

// node_modules/@arcgis/core/views/2d/engine/BitmapTileContainer.js
var n = class extends i {
  get requiresDedicatedFBO() {
    return this.children.some((e2) => "additive" === e2.bitmap.blendFunction);
  }
  createTile(r3) {
    const t = this._tileInfoView.getTileBounds(u(), r3), s = this._tileInfoView.getTileResolution(r3.level), [n2, o] = this._tileInfoView.tileInfo.size;
    return new r2(r3, s, t[0], t[3], n2, o);
  }
  prepareRenderPasses(e2) {
    const i2 = e2.registerRenderPass({ name: "bitmap (tile)", brushes: [w.bitmap], target: () => this.children.map((e3) => e3.bitmap), drawPhase: T.MAP });
    return [...super.prepareRenderPasses(e2), i2];
  }
  doRender(e2) {
    this.visible && e2.drawPhase === T.MAP && super.doRender(e2);
  }
};

export {
  n
};
//# sourceMappingURL=chunk-TAERZTFZ.js.map
