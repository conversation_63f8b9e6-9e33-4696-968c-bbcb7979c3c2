import {
  i as i4
} from "./chunk-BI4P4NAQ.js";
import {
  o as o6
} from "./chunk-VYWZHTOQ.js";
import {
  s as s4
} from "./chunk-KEY2Y5WF.js";
import {
  C
} from "./chunk-SZNZM2TR.js";
import {
  t as t6
} from "./chunk-5SYMUP5B.js";
import {
  w as w2
} from "./chunk-O2JKCGK6.js";
import {
  e as e7,
  n as n4,
  t as t4
} from "./chunk-N73MYEJE.js";
import {
  e as e6,
  t as t3
} from "./chunk-KXNV6PXI.js";
import {
  dt,
  f as f3,
  j
} from "./chunk-RRNRSHX3.js";
import {
  n as n3,
  t as t5
} from "./chunk-DUEDINK5.js";
import {
  A as A2,
  F,
  G as G2,
  I,
  L,
  P as P2,
  S,
  a as a3,
  b,
  c as c3,
  d,
  g as g3,
  h as h2,
  i as i3,
  k as k2,
  l as l4,
  m as m3,
  p as p2,
  s as s5,
  t as t7,
  w as w3,
  x
} from "./chunk-J4YX6DLU.js";
import {
  a as a2,
  n as n2
} from "./chunk-NWZTRS6O.js";
import {
  A,
  B,
  C as C2,
  K,
  O,
  P,
  R,
  U,
  X,
  Y,
  Z,
  a,
  e as e4,
  i,
  ie,
  k,
  l as l2,
  m as m2,
  n,
  ne,
  o as o4,
  oe,
  u as u5,
  w,
  y as y2
} from "./chunk-RURSJOSG.js";
import {
  e as e5,
  h,
  i as i2,
  o as o5
} from "./chunk-77E52HT5.js";
import {
  r as r3,
  z
} from "./chunk-SROTSYJS.js";
import {
  o as o7,
  u as u6
} from "./chunk-57XIOVP5.js";
import {
  m,
  o as o3,
  u as u3
} from "./chunk-I7WHRVHF.js";
import {
  f as f2,
  l,
  s as s3,
  u as u2,
  y
} from "./chunk-ZACBBT3Y.js";
import {
  M,
  g2 as g,
  o,
  o2,
  r as r2,
  s as s2,
  u2 as u
} from "./chunk-X7FOCGBC.js";
import {
  c as c2,
  g as g2
} from "./chunk-SRBBUKOI.js";
import {
  G
} from "./chunk-XTO3XXZ3.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  e as e3,
  u as u4
} from "./chunk-G5KX4JSG.js";
import {
  l as l3
} from "./chunk-T23PB46T.js";
import {
  e as e2
} from "./chunk-TUM6KUQZ.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  c
} from "./chunk-LTKA6OKA.js";
import {
  has,
  p
} from "./chunk-REW33H3I.js";
import {
  t as t2
} from "./chunk-GZGAQUSK.js";
import {
  e,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/BidiText.js
var n5 = new C();
function i5(r18) {
  if (null == r18) return ["", false];
  if (!n5.hasBidiChar(r18)) return [r18, false];
  let i10;
  return i10 = "rtl" === n5.checkContextual(r18) ? "IDNNN" : "ICNNN", [n5.bidiTransform(r18, i10, "VLYSN"), true];
}

// node_modules/@arcgis/core/symbols/cim/TextRasterizer.js
function e8(t14) {
  return `rgb(${t14.slice(0, 3).toString()})`;
}
function i6(t14) {
  return `rgba(${t14.slice(0, 3).toString()},${t14[3]})`;
}
var s6 = class {
  constructor(t14) {
    t14 && (this._textRasterizationCanvas = t14);
  }
  rasterizeText(t14, s13) {
    this._textRasterizationCanvas || (this._textRasterizationCanvas = document.createElement("canvas"));
    const r18 = this._textRasterizationCanvas, o14 = r18.getContext("2d");
    this._setFontProperties(o14, s13), this._parameters = s13, this._textLines = t14.split(/\r?\n/), this._lineHeight = this._computeLineHeight();
    const h8 = this._computeTextWidth(o14, s13), { decoration: a16, weight: l16 } = s13.font;
    this._lineThroughWidthOffset = a16 && "line-through" === a16 ? 0.1 * this._lineHeight : 0;
    const d4 = this._lineHeight * this._textLines.length;
    r18.width = h8 + 2 * this._lineThroughWidthOffset, r18.height = d4, this._renderedLineHeight = Math.round(this._lineHeight * s13.pixelRatio), this._renderedHaloSize = s13.halo.size * s13.pixelRatio, this._renderedWidth = h8 * s13.pixelRatio, this._renderedHeight = d4 * s13.pixelRatio, this._lineThroughWidthOffset *= s13.pixelRatio;
    const c13 = s13.color ?? [0, 0, 0, 0], _5 = s13.halo && s13.halo.color ? s13.halo.color : [0, 0, 0, 0];
    this._fillStyle = i6(c13), this._haloStyle = e8(_5);
    const g10 = this._renderedLineHeight, f10 = this._renderedHaloSize;
    o14.save(), o14.clearRect(0, 0, r18.width, r18.height), this._setFontProperties(o14, s13);
    const u13 = n6(o14.textAlign, this._renderedWidth) + f10, p6 = f10, x5 = f10 > 0;
    let m8 = this._lineThroughWidthOffset, b5 = 0;
    x5 && this._renderHalo(o14, u13, p6, m8, b5, s13), b5 += p6, m8 += u13;
    for (const e15 of this._textLines) x5 ? (o14.globalCompositeOperation = "destination-out", o14.fillStyle = "rgb(0, 0, 0)", o14.fillText(e15, m8, b5), o14.globalCompositeOperation = "source-over", o14.fillStyle = this._fillStyle, o14.fillText(e15, m8, b5)) : (o14.fillStyle = this._fillStyle, o14.fillText(e15, m8, b5)), a16 && "none" !== a16 && this._renderDecoration(o14, m8, b5, a16, l16), b5 += g10;
    o14.restore();
    const z3 = this._renderedWidth + 2 * this._lineThroughWidthOffset, w6 = this._renderedHeight, v2 = o14.getImageData(0, 0, z3, w6), H2 = new Uint8Array(v2.data);
    if (s13.premultiplyColors) {
      let t15;
      for (let e15 = 0; e15 < H2.length; e15 += 4) t15 = H2[e15 + 3] / 255, H2[e15] = H2[e15] * t15, H2[e15 + 1] = H2[e15 + 1] * t15, H2[e15 + 2] = H2[e15 + 2] * t15;
    }
    let y6, R3;
    switch (s13.horizontalAlignment) {
      case "left":
        y6 = -0.5;
        break;
      case "right":
        y6 = 0.5;
        break;
      default:
        y6 = 0;
    }
    switch (s13.verticalAlignment) {
      case "bottom":
        R3 = -0.5;
        break;
      case "top":
        R3 = 0.5;
        break;
      default:
        R3 = 0;
    }
    return { size: [z3, w6], image: new Uint32Array(H2.buffer), sdf: false, simplePattern: false, anchorX: y6, anchorY: R3, canvas: r18 };
  }
  _renderHalo(t14, e15, i10, s13, n18, r18) {
    const o14 = this._renderedWidth, h8 = this._renderedHeight;
    this._haloRasterizationCanvas || (this._haloRasterizationCanvas = document.createElement("canvas")), this._haloRasterizationCanvas.width = o14, this._haloRasterizationCanvas.height = h8;
    const a16 = this._haloRasterizationCanvas, l16 = a16.getContext("2d");
    l16.clearRect(0, 0, o14, h8), this._setFontProperties(l16, r18);
    const { decoration: d4, weight: c13 } = r18.font;
    l16.fillStyle = this._haloStyle, l16.strokeStyle = this._haloStyle, l16.lineJoin = "round", this._renderHaloNative(l16, e15, i10, d4, c13), t14.globalAlpha = this._parameters.halo.color[3], t14.drawImage(a16, 0, 0, o14, h8, s13, n18, o14, h8), t14.globalAlpha = 1;
  }
  _renderHaloNative(t14, e15, i10, s13, n18) {
    const r18 = this._renderedLineHeight, o14 = this._renderedHaloSize;
    for (const h8 of this._textLines) {
      const a16 = 2 * o14, l16 = 5, d4 = 0.1;
      for (let r19 = 0; r19 < l16; r19++) {
        const o15 = (1 - (l16 - 1) * d4 + r19 * d4) * a16;
        t14.lineWidth = o15, t14.strokeText(h8, e15, i10), s13 && "none" !== s13 && this._renderDecoration(t14, e15, i10, s13, n18, o15);
      }
      i10 += r18;
    }
  }
  _setFontProperties(e15, i10) {
    const s13 = Math.max(i10.size, 0.5), n18 = i10.font, r18 = `${n18.style} ${n18.weight} ${u4(s13 * i10.pixelRatio).toFixed(1)}px ${n18.family}, sans-serif`;
    let o14;
    switch (e15.font = r18, e15.textBaseline = "top", i10.horizontalAlignment) {
      case "left":
      default:
        o14 = "left";
        break;
      case "right":
        o14 = "right";
        break;
      case "center":
        o14 = "center";
    }
    e15.textAlign = o14;
  }
  computeTextSize(t14, e15) {
    this._textRasterizationCanvas || (this._textRasterizationCanvas = document.createElement("canvas"));
    const i10 = this._textRasterizationCanvas, s13 = i10.getContext("2d");
    this._setFontProperties(s13, e15), this._parameters = e15, this._textLines = t14.split(/\r?\n/), this._lineHeight = this._computeLineHeight();
    const n18 = this._computeTextWidth(s13, e15), r18 = this._lineHeight * this._textLines.length;
    return i10.width = n18, i10.height = r18, [n18 * e15.pixelRatio, r18 * e15.pixelRatio];
  }
  _computeTextWidth(t14, e15) {
    let i10 = 0;
    for (const n18 of this._textLines) i10 = Math.max(i10, t14.measureText(n18).width);
    const s13 = e15.font;
    return ("italic" === s13.style || "oblique" === s13.style || "string" == typeof s13.weight && ("bold" === s13.weight || "bolder" === s13.weight) || "number" == typeof s13.weight && s13.weight > 600) && (i10 += 0.3 * t14.measureText("w").width), i10 += 2 * this._parameters.halo.size, Math.round(i10);
  }
  _computeLineHeight() {
    let t14 = 1.275 * this._parameters.size;
    const e15 = this._parameters.font.decoration;
    return e15 && "underline" === e15 && (t14 *= 1.3), Math.round(t14 + 2 * this._parameters.halo.size);
  }
  _renderDecoration(t14, e15, i10, s13, n18, r18) {
    const o14 = 0.9 * this._lineHeight, h8 = "bold" === n18 ? 0.06 : "bolder" === n18 ? 0.09 : 0.04;
    switch (t14.textAlign) {
      case "center":
        e15 -= this._renderedWidth / 2;
        break;
      case "right":
        e15 -= this._renderedWidth;
    }
    const a16 = t14.textBaseline;
    if ("underline" === s13) switch (a16) {
      case "top":
        i10 += o14;
        break;
      case "middle":
        i10 += o14 / 2;
    }
    else if ("line-through" === s13) switch (a16) {
      case "top":
        i10 += o14 / 1.5;
        break;
      case "middle":
        i10 += o14 / 3;
    }
    const l16 = r18 ? 1.5 * r18 : Math.ceil(o14 * h8);
    t14.save(), t14.beginPath(), t14.strokeStyle = t14.fillStyle, t14.lineWidth = l16, t14.moveTo(e15 - this._lineThroughWidthOffset, i10), t14.lineTo(e15 + this._renderedWidth + 2 * this._lineThroughWidthOffset, i10), t14.stroke(), t14.restore();
  }
};
function n6(t14, e15) {
  return "center" === t14 ? 0.5 * e15 : "right" === t14 ? e15 : 0;
}

// node_modules/@arcgis/core/symbols/cim/CIMEffects.js
var n7 = 512;
var r4;
var s7 = class {
  constructor(t14) {
    this._geometry = t14;
  }
  next() {
    const t14 = this._geometry;
    return this._geometry = null, t14;
  }
};
function l5(t14, s13) {
  let i10, l16;
  r4 || (r4 = new e7(0, 0, 0, 1)), r4.reset(t4.Polygon), r4.setPixelMargin(s13 + 1), r4.setExtent(n7);
  for (const o14 of t14.rings) if (o14 && !(o14.length < 3)) {
    i10 = o14[0][0], l16 = -o14[0][1], r4.moveTo(i10, l16);
    for (let t15 = 1; t15 < o14.length; t15++) i10 = o14[t15][0], l16 = -o14[t15][1], r4.lineTo(i10, l16);
    r4.close();
  }
  const c13 = r4.result(false);
  if (c13) {
    const t15 = [];
    for (const o14 of c13) {
      const e15 = [];
      t15.push(e15);
      for (const t16 of o14) e15.push([t16.x, -t16.y]);
    }
    return { rings: t15 };
  }
  return { rings: [] };
}
function c4(t14, s13) {
  let i10, l16;
  r4 || (r4 = new e7(0, 0, 0, 1)), r4.reset(t4.LineString), r4.setPixelMargin(s13 + 1), r4.setExtent(n7);
  for (const o14 of t14.paths) if (o14 && !(o14.length < 2)) {
    i10 = o14[0][0], l16 = -o14[0][1], r4.moveTo(i10, l16);
    for (let t15 = 1; t15 < o14.length; t15++) i10 = o14[t15][0], l16 = -o14[t15][1], r4.lineTo(i10, l16);
  }
  const c13 = r4.result(false);
  if (c13) {
    const t15 = [];
    for (const o14 of c13) {
      const e15 = [];
      t15.push(e15);
      for (const t16 of o14) e15.push([t16.x, -t16.y]);
    }
    return { paths: t15 };
  }
  return { paths: [] };
}

// node_modules/@arcgis/core/symbols/cim/CIMImageColorSubstitutionHelper.js
var t8 = class {
  applyColorSubstituition(t14, a16) {
    if (!a16) return t14;
    this._rasterizationCanvas || (this._rasterizationCanvas = document.createElement("canvas"));
    const { width: e15, height: n18 } = t14, o14 = this._rasterizationCanvas, r18 = o14.getContext("2d");
    t14 !== o14 && (o14.width = e15, o14.height = n18, r18.drawImage(t14, 0, 0, e15, n18));
    const i10 = r18.getImageData(0, 0, e15, n18).data;
    if (a16) {
      for (const h8 of a16) if (h8 && h8.oldColor && 4 === h8.oldColor.length && h8.newColor && 4 === h8.newColor.length) {
        const [t15, a17, e16, n19] = h8.oldColor, [o15, r19, s14, l16] = h8.newColor;
        if (t15 === o15 && a17 === r19 && e16 === s14 && n19 === l16) continue;
        for (let h9 = 0; h9 < i10.length; h9 += 4) t15 === i10[h9] && a17 === i10[h9 + 1] && e16 === i10[h9 + 2] && n19 === i10[h9 + 3] && (i10[h9] = o15, i10[h9 + 1] = r19, i10[h9 + 2] = s14, i10[h9 + 3] = l16);
      }
    }
    const s13 = new ImageData(i10, e15, n18);
    return r18.putImageData(s13, 0, 0), o14;
  }
  tintImageData(t14, a16) {
    if (!a16 || a16.length < 4) return t14;
    this._rasterizationCanvas || (this._rasterizationCanvas = document.createElement("canvas"));
    const { width: e15, height: n18 } = t14, o14 = this._rasterizationCanvas, r18 = o14.getContext("2d");
    t14 !== o14 && (o14.width = e15, o14.height = n18, r18.drawImage(t14, 0, 0, e15, n18));
    const i10 = r18.getImageData(0, 0, e15, n18), s13 = new Uint8Array(i10.data), h8 = [a16[0] / 255, a16[1] / 255, a16[2] / 255, a16[3] / 255];
    for (let g10 = 0; g10 < s13.length; g10 += 4) s13[g10 + 0] *= h8[0], s13[g10 + 1] *= h8[1], s13[g10 + 2] *= h8[2], s13[g10 + 3] *= h8[3];
    const l16 = new ImageData(new Uint8ClampedArray(s13.buffer), e15, n18);
    return r18.putImageData(l16, 0, 0), o14;
  }
};

// node_modules/@arcgis/core/symbols/cim/CIMPlacements.js
var t9 = class {
  constructor() {
    this.setIdentity();
  }
  getAngle() {
    return (null == this.rz || 0 === this.rz && 1 !== this.rzCos && 0 !== this.rzSin) && (this.rz = Math.atan2(this.rzSin, this.rzCos)), this.rz;
  }
  setIdentity() {
    this.tx = 0, this.ty = 0, this.tz = 0, this.s = 1, this.rx = 0, this.ry = 0, this.rz = 0, this.rzCos = 1, this.rzSin = 0;
  }
  setTranslate(t14, s13) {
    this.tx = t14, this.ty = s13;
  }
  setTranslateZ(t14) {
    this.tz = t14;
  }
  setRotateCS(t14, s13) {
    this.rz = void 0, this.rzCos = t14, this.rzSin = s13;
  }
  setRotate(t14) {
    this.rz = t14, this.rzCos = void 0, this.rzSin = void 0;
  }
  setRotateY(t14) {
    this.ry = t14;
  }
  setScale(t14) {
    this.s = t14;
  }
  setMeasure(t14) {
    this.m = t14;
  }
};

// node_modules/@arcgis/core/symbols/cim/CIMCursor.js
function a4(i10) {
  const n18 = p(i10);
  return P3(n18), n18;
}
function l6(t14) {
  t14 && (s3(t14) ? t14.y = -t14.y : y(t14) ? u7(t14.rings) : f2(t14) ? u7(t14.paths) : l(t14) && r5(t14.points));
}
function r5(t14) {
  if (t14) {
    const i10 = t14.length;
    for (let n18 = 0; n18 < i10; n18++) t14[n18][1] = -t14[n18][1];
  }
}
function u7(t14) {
  if (t14) for (const i10 of t14) r5(i10);
}
function c5(t14) {
  if (t14) {
    for (let i10 = t14.length - 1; i10 > 0; --i10) t14[i10][0] -= t14[i10 - 1][0], t14[i10][1] -= t14[i10 - 1][1];
  }
}
function f4(t14) {
  if (t14) for (const i10 of t14) c5(i10);
}
function p3(t14) {
  if (t14) {
    const i10 = t14.length;
    for (let n18 = 1; n18 < i10; ++n18) t14[n18][0] += t14[n18 - 1][0], t14[n18][1] += t14[n18 - 1][1];
  }
}
function m4(t14) {
  if (t14) for (const i10 of t14) p3(i10);
}
function P3(t14) {
  t14 && (y(t14) ? m4(t14.rings) : f2(t14) ? m4(t14.paths) : l(t14) && p3(t14.points), l6(t14));
}
function x2(t14) {
  t14 && (l6(t14), y(t14) ? f4(t14.rings) : f2(t14) ? f4(t14.paths) : l(t14) && c5(t14.points));
}
function g4(t14) {
  if (t14) for (const i10 of t14) d2(i10);
}
function d2(t14) {
  t14 && t14.reverse();
}
function C3(t14, i10, n18) {
  return [t14[0] + (i10[0] - t14[0]) * n18, t14[1] + (i10[1] - t14[1]) * n18];
}
function y3(t14) {
  return !(!t14 || 0 === t14.length) && (t14[0][0] === t14[t14.length - 1][0] && t14[0][1] === t14[t14.length - 1][1]);
}
function I2(t14) {
  return t14[4];
}
function j2(t14, i10) {
  t14[4] = i10;
}
var G3 = class {
  constructor(t14, s13, e15, a16 = 0) {
    this.isClosed = false, this.multiPath = null, this.acceptPolygon = s13, this.acceptPolyline = e15, this.geomUnitsPerPoint = a16, this.pathCount = -1, this.pathIndex = -1, this.iteratePath = false, t14 && (y(t14) ? s13 && (this.multiPath = t14.rings, this.isClosed = true) : f2(t14) ? e15 && (this.multiPath = t14.paths, this.isClosed = false) : u2(t14) && s13 && (this.multiPath = b2(t14).rings, this.isClosed = true), this.multiPath && (this.pathCount = this.multiPath.length)), this.internalPlacement = new t9();
  }
  next() {
    if (!this.multiPath) return null;
    for (; this.iteratePath || this.pathIndex < this.pathCount - 1; ) {
      this.iteratePath || this.pathIndex++;
      const t14 = this.processPath(this.multiPath[this.pathIndex]);
      if (t14) return t14;
    }
    return this.pathCount = -1, this.pathIndex = -1, this.multiPath = null, null;
  }
};
var U2 = class {
  constructor(t14, i10, n18, s13 = 0) {
    this.isClosed = false, this.multiPath = null, this.inputGeometries = t14, this.acceptPolygon = i10, this.acceptPolyline = n18, this.geomUnitsPerPoint = s13, this.pathCount = -1, this.pathIndex = -1, this.iteratePath = false;
  }
  next() {
    for (; ; ) {
      if (!this.multiPath) {
        let t14 = this.inputGeometries.next();
        for (; t14; ) {
          if (y(t14) ? this.acceptPolygon && (this.multiPath = t14.rings, this.isClosed = true) : f2(t14) ? this.acceptPolyline && (this.multiPath = t14.paths, this.isClosed = false) : u2(t14) && this.acceptPolygon && (this.multiPath = b2(t14).rings, this.isClosed = true), this.multiPath) {
            this.pathCount = this.multiPath.length, this.pathIndex = -1;
            break;
          }
          t14 = this.inputGeometries.next();
        }
        if (!this.multiPath) return null;
      }
      for (; this.iteratePath || this.pathIndex < this.pathCount - 1; ) {
        this.iteratePath || this.pathIndex++;
        const t14 = this.processPath(this.multiPath[this.pathIndex]);
        if (t14) return t14;
      }
      this.pathCount = -1, this.pathIndex = -1, this.multiPath = null;
    }
  }
};
function b2(t14) {
  return { rings: [[[t14.xmin, t14.ymin], [t14.xmin, t14.ymax], [t14.xmax, t14.ymax], [t14.xmax, t14.ymin], [t14.xmin, t14.ymin]]] };
}

// node_modules/@arcgis/core/symbols/cim/effects/EffectAddControlPoints.js
var o8 = class _o {
  static local() {
    return null === _o.instance && (_o.instance = new _o()), _o.instance;
  }
  execute(s13, t14, e15, i10, n18) {
    return new r6(s13, t14, e15);
  }
};
o8.instance = null;
var r6 = class {
  constructor(s13, t14, e15) {
    this._inputGeometries = s13, this._angleTolerance = void 0 !== t14.angleTolerance ? t14.angleTolerance : 120, this._maxCosAngle = Math.cos((1 - Math.abs(this._angleTolerance) / 180) * Math.PI);
  }
  next() {
    let n18 = this._inputGeometries.next();
    for (; n18; ) {
      if (y(n18)) {
        this._isClosed = true;
        const t14 = p(n18);
        return this._processMultipath(t14.rings), t14;
      }
      if (f2(n18)) {
        this._isClosed = false;
        const t14 = p(n18);
        return this._processMultipath(t14.paths), t14;
      }
      if (u2(n18)) {
        if (this._maxCosAngle) return n18;
        this._isClosed = true;
        const s13 = [[n18.xmin, n18.ymin], [n18.xmin, n18.ymax], [n18.xmax, n18.ymax], [n18.xmax, n18.ymin], [n18.xmin, n18.ymin]];
        return this._processPath(s13), { rings: [s13] };
      }
      n18 = this._inputGeometries.next();
    }
    return null;
  }
  _processMultipath(s13) {
    if (s13) for (const t14 of s13) this._processPath(t14);
  }
  _processPath(s13) {
    if (s13) {
      let t14, e15, i10, o14, r18, l16, a16 = s13.length, h8 = s13[0];
      this._isClosed && ++a16;
      for (let c13 = 1; c13 < a16; ++c13) {
        let m8;
        m8 = this._isClosed && c13 === a16 - 1 ? s13[0] : s13[c13];
        const _5 = m8[0] - h8[0], u13 = m8[1] - h8[1], p6 = Math.sqrt(_5 * _5 + u13 * u13);
        if (c13 > 1 && p6 > 0 && i10 > 0) {
          (t14 * _5 + e15 * u13) / p6 / i10 <= this._maxCosAngle && j2(h8, 1);
        }
        1 === c13 && (o14 = _5, r18 = u13, l16 = p6), p6 > 0 && (h8 = m8, t14 = _5, e15 = u13, i10 = p6);
      }
      if (this._isClosed && i10 > 0 && l16 > 0) {
        (t14 * o14 + e15 * r18) / l16 / i10 <= this._maxCosAngle && j2(s13[0], 1);
      }
    }
  }
};

// node_modules/@arcgis/core/symbols/cim/CurveHelper.js
var e9 = 0.03;
var s8 = class {
  constructor() {
    this._path = [];
  }
  path() {
    return this._path;
  }
  addPath(t14, e15) {
    e15 || t14.reverse(), Array.prototype.push.apply(this._path, t14), e15 || t14.reverse();
  }
  static mergePath(t14, e15) {
    e15 && Array.prototype.push.apply(t14, e15);
  }
  startPath(t14) {
    this._path.push(t14);
  }
  lineTo(t14) {
    this._path.push(t14);
  }
  close() {
    const t14 = this._path;
    t14.length > 1 && (t14[0][0] === t14[t14.length - 1][0] && t14[0][1] === t14[t14.length - 1][1] || t14.push([t14[0][0], t14[0][1]]));
  }
};
var n8 = class {
  constructor(t14 = 0, e15 = false) {
  }
  normalize(t14) {
    const e15 = Math.sqrt(t14[0] * t14[0] + t14[1] * t14[1]);
    0 !== e15 && (t14[0] /= e15, t14[1] /= e15);
  }
  calculateLength(t14, e15) {
    const s13 = e15[0] - t14[0], n18 = e15[1] - t14[1];
    return Math.sqrt(s13 * s13 + n18 * n18);
  }
  calculateSegLength(t14, e15) {
    return this.calculateLength(t14[e15], t14[e15 + 1]);
  }
  calculatePathLength(t14) {
    let e15 = 0;
    const s13 = t14 ? t14.length : 0;
    for (let n18 = 0; n18 < s13 - 1; ++n18) e15 += this.calculateSegLength(t14, n18);
    return e15;
  }
  calculatePathArea(t14) {
    let e15 = 0;
    const s13 = t14 ? t14.length : 0;
    for (let n18 = 0; n18 < s13 - 1; ++n18) e15 += (t14[n18 + 1][0] - t14[n18][0]) * (t14[n18 + 1][1] + t14[n18][1]);
    return e15 / 2;
  }
  getCoord2D(t14, e15, s13) {
    return [t14[0] + (e15[0] - t14[0]) * s13, t14[1] + (e15[1] - t14[1]) * s13];
  }
  getSegCoord2D(t14, e15, s13) {
    return this.getCoord2D(t14[e15], t14[e15 + 1], s13);
  }
  getAngle(t14, e15, s13) {
    const n18 = e15[0] - t14[0], r18 = e15[1] - t14[1];
    return Math.atan2(r18, n18);
  }
  getSegAngle(t14, e15, s13) {
    return this.getAngle(t14[e15], t14[e15 + 1], s13);
  }
  getAngleCS(t14, e15, s13) {
    const n18 = e15[0] - t14[0], r18 = e15[1] - t14[1], h8 = Math.sqrt(n18 * n18 + r18 * r18);
    return h8 > 0 ? [n18 / h8, r18 / h8] : [1, 0];
  }
  getSegAngleCS(t14, e15, s13) {
    return this.getAngleCS(t14[e15], t14[e15 + 1], s13);
  }
  cut(t14, e15, s13, n18) {
    return [s13 <= 0 ? t14[e15] : this.getSegCoord2D(t14, e15, s13), n18 >= 1 ? t14[e15 + 1] : this.getSegCoord2D(t14, e15, n18)];
  }
  addSegment(t14, e15, s13) {
    s13 && t14.push(e15[0]), t14.push(e15[1]);
  }
  getSubCurve(t14, e15, s13) {
    const n18 = [];
    return this.appendSubCurve(n18, t14, e15, s13) ? n18 : null;
  }
  appendSubCurve(t14, e15, s13, n18) {
    const r18 = e15 ? e15.length - 1 : 0;
    let h8 = 0, l16 = true, o14 = 0;
    for (; o14 < r18; ) {
      const r19 = this.calculateSegLength(e15, o14);
      if (0 !== r19) {
        if (l16) {
          if (h8 + r19 > s13) {
            const a16 = (s13 - h8) / r19;
            let u13 = 1, c13 = false;
            h8 + r19 >= n18 && (u13 = (n18 - h8) / r19, c13 = true);
            const i10 = this.cut(e15, o14, a16, u13);
            if (i10 && this.addSegment(t14, i10, l16), c13) break;
            l16 = false;
          }
        } else {
          if (h8 + r19 > n18) {
            const s14 = this.cut(e15, o14, 0, (n18 - h8) / r19);
            s14 && this.addSegment(t14, s14, l16);
            break;
          }
          this.addSegment(t14, [e15[o14], e15[o14 + 1]], l16);
        }
        h8 += r19, ++o14;
      } else ++o14;
    }
    return true;
  }
  getCIMPointAlong(t14, e15) {
    const s13 = t14 ? t14.length - 1 : 0;
    let n18 = 0, r18 = -1;
    for (; r18 < s13; ) {
      ++r18;
      const s14 = this.calculateSegLength(t14, r18);
      if (0 !== s14) {
        if (n18 + s14 > e15) {
          const h8 = (e15 - n18) / s14;
          return this.getCoord2D(t14[r18], t14[r18 + 1], h8);
        }
        n18 += s14;
      }
    }
    return null;
  }
  isEmpty(t14, e15) {
    if (!t14 || t14.length <= 1) return true;
    const s13 = t14 ? t14.length - 1 : 0;
    let n18 = -1;
    for (; n18 < s13; ) {
      if (++n18, t14[n18 + 1][0] !== t14[n18][0] || t14[n18 + 1][1] !== t14[n18][1]) return false;
      if (e15 && t14[n18 + 1][2] !== t14[n18][2]) return false;
    }
    return true;
  }
  offset(e15, s13, n18, r18, h8) {
    if (!e15 || e15.length < 2) return null;
    let l16 = 0, o14 = e15[l16++], a16 = l16;
    for (; l16 < e15.length; ) {
      const t14 = e15[l16];
      t14[0] === o14[0] && t14[1] === o14[1] || (l16 !== a16 && (e15[a16] = e15[l16]), o14 = e15[a16++]), l16++;
    }
    const u13 = e15[0][0] === e15[a16 - 1][0] && e15[0][1] === e15[a16 - 1][1];
    if (u13 && --a16, a16 < (u13 ? 3 : 2)) return null;
    const c13 = [];
    o14 = u13 ? e15[a16 - 1] : null;
    let i10 = e15[0];
    for (let g10 = 0; g10 < a16; g10++) {
      const h9 = g10 === a16 - 1 ? u13 ? e15[0] : null : e15[g10 + 1];
      if (o14) if (h9) {
        const e16 = [h9[0] - i10[0], h9[1] - i10[1]];
        this.normalize(e16);
        const l17 = [i10[0] - o14[0], i10[1] - o14[1]];
        this.normalize(l17);
        const a17 = l17[0] * e16[1] - l17[1] * e16[0], u14 = l17[0] * e16[0] + l17[1] * e16[1];
        if (0 === a17 && 1 === u14) {
          i10 = h9;
          continue;
        }
        if (a17 >= 0 == s13 <= 0) {
          if (u14 < 1) {
            const t14 = [e16[0] - l17[0], e16[1] - l17[1]];
            this.normalize(t14);
            const n19 = Math.sqrt((1 + u14) / 2);
            if (n19 > 1 / r18) {
              const e17 = -Math.abs(s13) / n19;
              c13.push([i10[0] - t14[0] * e17, i10[1] - t14[1] * e17]);
            }
          }
        } else switch (n18) {
          case O.Mitered: {
            const t14 = Math.sqrt((1 + u14) / 2);
            if (t14 > 0 && 1 / t14 < r18) {
              const n19 = [e16[0] - l17[0], e16[1] - l17[1]];
              this.normalize(n19);
              const r19 = Math.abs(s13) / t14;
              c13.push([i10[0] - n19[0] * r19, i10[1] - n19[1] * r19]);
              break;
            }
          }
          case O.Bevelled:
            c13.push([i10[0] + l17[1] * s13, i10[1] - l17[0] * s13]), c13.push([i10[0] + e16[1] * s13, i10[1] - e16[0] * s13]);
            break;
          case O.Rounded:
            if (u14 < 1) {
              c13.push([i10[0] + l17[1] * s13, i10[1] - l17[0] * s13]);
              const t14 = Math.floor(2.5 * (1 - u14));
              if (t14 > 0) {
                const n19 = 1 / t14;
                let r19 = n19;
                for (let h10 = 1; h10 < t14; h10++, r19 += n19) {
                  const t15 = [l17[1] * (1 - r19) + e16[1] * r19, -l17[0] * (1 - r19) - e16[0] * r19];
                  this.normalize(t15), c13.push([i10[0] + t15[0] * s13, i10[1] + t15[1] * s13]);
                }
              }
              c13.push([i10[0] + e16[1] * s13, i10[1] - e16[0] * s13]);
            }
            break;
          case O.Square:
          default:
            if (a17 < 0) c13.push([i10[0] + (l17[1] + l17[0]) * s13, i10[1] + (l17[1] - l17[0]) * s13]), c13.push([i10[0] + (e16[1] - e16[0]) * s13, i10[1] - (e16[0] + e16[1]) * s13]);
            else {
              const t14 = Math.sqrt((1 + Math.abs(u14)) / 2), n19 = [e16[0] - l17[0], e16[1] - l17[1]];
              this.normalize(n19);
              const r19 = s13 / t14;
              c13.push([i10[0] - n19[0] * r19, i10[1] - n19[1] * r19]);
            }
        }
      } else {
        const t14 = [i10[0] - o14[0], i10[1] - o14[1]];
        this.normalize(t14), c13.push([i10[0] + t14[1] * s13, i10[1] - t14[0] * s13]);
      }
      else {
        const t14 = [h9[0] - i10[0], h9[1] - i10[1]];
        this.normalize(t14), c13.push([i10[0] + t14[1] * s13, i10[1] - t14[0] * s13]);
      }
      o14 = i10, i10 = h9;
    }
    return c13.length < (u13 ? 3 : 2) ? null : (u13 && c13.push([c13[0][0], c13[0][1]]), c13);
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectArrow.js
var l7 = 1.7320508075688772;
var c6 = 5;
var u8 = P.OpenEnded;
var h3 = class _h {
  static local() {
    return null === _h.instance && (_h.instance = new _h()), _h.instance;
  }
  execute(t14, e15, r18, n18, o14) {
    return new a5(t14, e15, r18);
  }
};
h3.instance = null;
var a5 = class extends U2 {
  constructor(t14, e15, o14) {
    super(t14, false, true), this._curveHelper = new n8(), this._width = (void 0 !== e15.width ? e15.width : c6) * o14, this._arrowType = void 0 !== e15.geometricEffectArrowType ? e15.geometricEffectArrowType : void 0 !== e15.arrowType ? e15.arrowType : u8, this._offsetFlattenError = e9 * o14;
  }
  processPath(t14) {
    switch (this._arrowType) {
      case P.OpenEnded:
      default:
        return this._constructSimpleArrow(t14, true);
      case P.Block:
        return this._constructSimpleArrow(t14, false);
      case P.Crossed:
        return this._constructCrossedArrow(t14);
    }
  }
  _constructSimpleArrow(t14, e15) {
    const r18 = this._curveHelper.calculatePathLength(t14);
    let n18 = this._width;
    r18 < 2 * n18 && (n18 = r18 / 2);
    const s13 = this._curveHelper.getSubCurve(t14, 0, r18 - n18);
    if (!s13) return null;
    const i10 = n18 / 2;
    if (this._curveHelper.isEmpty(s13, false)) return null;
    const l16 = this._constructOffset(s13, -i10);
    if (!l16) return null;
    const c13 = this._constructOffset(s13, i10);
    if (!c13) return null;
    const u13 = this._constructArrowBasePoint(l16, -i10 / 2);
    if (!u13) return null;
    const h8 = this._constructArrowBasePoint(c13, i10 / 2);
    if (!h8) return null;
    const a16 = t14[t14.length - 1];
    e15 || (this._makeControlPoint(c13, true), this._makeControlPoint(l16, true));
    const _5 = new s8();
    return _5.addPath(c13, true), _5.lineTo(h8), this._makeControlPoint(_5.path()), _5.lineTo(a16), this._makeControlPoint(_5.path()), _5.lineTo(u13), this._makeControlPoint(_5.path()), _5.addPath(l16, false), e15 ? { paths: [_5.path()] } : (_5.close(), { rings: [_5.path()] });
  }
  _constructCrossedArrow(t14) {
    const e15 = this._curveHelper.calculatePathLength(t14);
    let r18 = this._width;
    e15 < r18 * (1 + l7 + 1) && (r18 = e15 / (1 + l7 + 1));
    const n18 = this._curveHelper.getSubCurve(t14, 0, e15 - r18 * (1 + l7));
    if (!n18) return null;
    const s13 = r18 / 2;
    if (this._curveHelper.isEmpty(n18, false)) return null;
    const i10 = this._constructOffset(n18, s13);
    if (!i10) return null;
    const c13 = this._constructOffset(n18, -s13);
    if (!c13) return null;
    const u13 = this._curveHelper.getSubCurve(t14, 0, e15 - r18);
    if (!u13) return null;
    if (this._curveHelper.isEmpty(u13, false)) return null;
    const h8 = this._constructOffset(u13, s13);
    if (!h8) return null;
    const a16 = this._constructOffset(u13, -s13);
    if (!a16) return null;
    const _5 = h8[h8.length - 1], f10 = this._constructArrowBasePoint(h8, s13 / 2);
    if (!f10) return null;
    const p6 = a16[a16.length - 1], m8 = this._constructArrowBasePoint(a16, -s13 / 2);
    if (!m8) return null;
    const d4 = t14[t14.length - 1];
    this._makeControlPoint(i10, false), this._makeControlPoint(c13, false);
    const w6 = new s8();
    return w6.addPath(i10, true), this._makeControlPoint(w6.path()), w6.lineTo(p6), w6.lineTo(m8), this._makeControlPoint(w6.path()), w6.lineTo(d4), this._makeControlPoint(w6.path()), w6.lineTo(f10), this._makeControlPoint(w6.path()), w6.lineTo(_5), this._makeControlPoint(w6.path()), w6.addPath(c13, false), { paths: [w6.path()] };
  }
  _constructOffset(t14, e15) {
    return this._curveHelper.offset(t14, e15, O.Rounded, 4, this._offsetFlattenError);
  }
  _constructArrowBasePoint(t14, e15) {
    if (!t14 || t14.length < 2) return null;
    const r18 = t14[t14.length - 2], n18 = t14[t14.length - 1], o14 = [n18[0] - r18[0], n18[1] - r18[1]];
    return this._curveHelper.normalize(o14), [n18[0] + o14[1] * e15, n18[1] - o14[0] * e15];
  }
  _makeControlPoint(t14, r18 = false) {
    j2(r18 ? t14[0] : t14[t14.length - 1], 1);
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectBuffer.js
var l8 = class _l {
  static local() {
    return null === _l.instance && (_l.instance = new _l()), _l.instance;
  }
  execute(e15, i10, t14, s13, r18) {
    return new f5(e15, i10, t14, s13, r18);
  }
};
l8.instance = null;
var f5 = class {
  constructor(e15, i10, t14, s13, r18) {
    this._inputGeometries = e15, this._tileKey = s13, this._geometryEngine = r18, this._curveHelper = new n8(), this._size = (void 0 !== i10.size ? i10.size : 1) * t14, this._offsetFlattenError = e9 * t14;
  }
  next() {
    let m8;
    for (; m8 = this._inputGeometries.next(); ) {
      if (0 === this._size) return m8;
      if (u2(m8)) {
        if (this._size > 0) {
          const e15 = [[m8.xmin, m8.ymin], [m8.xmin, m8.ymax], [m8.xmax, m8.ymax], [m8.xmax, m8.ymin], [m8.xmin, m8.ymin]], i10 = this._curveHelper.offset(e15, this._size, O.Rounded, 4, this._offsetFlattenError);
          if (i10) return { rings: [i10] };
        } else if (this._size < 0 && Math.min(m8.xmax - m8.xmin, m8.ymax - m8.ymin) + 2 * this._size > 0) return { xmin: m8.xmin - this._size, xmax: m8.xmax + this._size, ymin: m8.ymin - this._size, ymax: m8.ymax + this._size };
      }
      const o14 = this._geometryEngine;
      if (t(o14)) return null;
      let l16 = m8;
      if ((!y(m8) || !this._tileKey || (l16 = l5(m8, Math.abs(this._size) + 1), l16 && l16.rings && 0 !== l16.rings.length)) && (!f2(m8) || !this._tileKey || (l16 = c4(m8, Math.abs(this._size) + 1), l16 && l16.paths && 0 !== l16.paths.length))) return o14.buffer(f.WebMercator, l16, this._size, 1);
    }
    return null;
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectControlMeasureLine.js
var _ = class __ {
  static local() {
    return null === __.instance && (__.instance = new __()), __.instance;
  }
  execute(s13, t14, i10, h8, e15) {
    return new c7(s13, t14, i10);
  }
};
_.instance = null;
var c7 = class {
  constructor(s13, t14, i10) {
    this._defaultPointSize = 20, this._inputGeometries = s13, this._geomUnitsPerPoint = i10, this._rule = t14.rule ?? R.FullGeometry, this._defaultSize = this._defaultPointSize * i10;
  }
  next() {
    let e15;
    for (; e15 = this._inputGeometries.next(); ) {
      let r18;
      if (s3(e15) ? r18 = this._processGeom([[[e15.x, e15.y]]]) : l(e15) ? r18 = this._processGeom([e15.points]) : f2(e15) ? r18 = this._processGeom(e15.paths) : y(e15) && (r18 = this._processGeom(e15.rings)), r18 && r18.length) return { paths: r18 };
    }
    return null;
  }
  _clone(s13) {
    return [s13[0], s13[1]];
  }
  _mid(s13, t14) {
    return [(s13[0] + t14[0]) / 2, (s13[1] + t14[1]) / 2];
  }
  _mix(s13, t14, i10, h8) {
    return [s13[0] * t14 + i10[0] * h8, s13[1] * t14 + i10[1] * h8];
  }
  _add(s13, t14) {
    return [s13[0] + t14[0], s13[1] + t14[1]];
  }
  _add2(s13, t14, i10) {
    return [s13[0] + t14, s13[1] + i10];
  }
  _sub(s13, t14) {
    return [s13[0] - t14[0], s13[1] - t14[1]];
  }
  _dist(s13, t14) {
    return Math.sqrt((s13[0] - t14[0]) * (s13[0] - t14[0]) + (s13[1] - t14[1]) * (s13[1] - t14[1]));
  }
  _norm(s13) {
    return Math.sqrt(s13[0] * s13[0] + s13[1] * s13[1]);
  }
  _normalize(s13, t14 = 1) {
    const i10 = t14 / this._norm(s13);
    s13[0] *= i10, s13[1] *= i10;
  }
  _leftPerpendicular(s13) {
    const t14 = -s13[1], i10 = s13[0];
    s13[0] = t14, s13[1] = i10;
  }
  _leftPerp(s13) {
    return [-s13[1], s13[0]];
  }
  _rightPerpendicular(s13) {
    const t14 = s13[1], i10 = -s13[0];
    s13[0] = t14, s13[1] = i10;
  }
  _rightPerp(s13) {
    return [s13[1], -s13[0]];
  }
  _dotProduct(s13, t14) {
    return s13[0] * t14[0] + s13[1] * t14[1];
  }
  _crossProduct(s13, t14) {
    return s13[0] * t14[1] - s13[1] * t14[0];
  }
  _rotateDirect(s13, t14, i10) {
    const h8 = s13[0] * t14 - s13[1] * i10, e15 = s13[0] * i10 + s13[1] * t14;
    s13[0] = h8, s13[1] = e15;
  }
  _makeCtrlPt(s13) {
    const t14 = [s13[0], s13[1]];
    return j2(t14, 1), t14;
  }
  _addAngledTicks(s13, t14, i10, h8) {
    const e15 = this._sub(i10, t14);
    this._normalize(e15);
    const r18 = this._crossProduct(e15, this._sub(h8, t14));
    let _5;
    _5 = r18 > 0 ? this._rightPerp(e15) : this._leftPerp(e15);
    const c13 = Math.abs(r18) / 2, u13 = [];
    u13.push([t14[0] + (_5[0] - e15[0]) * c13, t14[1] + (_5[1] - e15[1]) * c13]), u13.push(t14), u13.push(i10), u13.push([i10[0] + (_5[0] + e15[0]) * c13, i10[1] + (_5[1] + e15[1]) * c13]), s13.push(u13);
  }
  _addBezier2(s13, t14, i10, h8, e15) {
    if (0 == e15--) return void s13.push(h8);
    const r18 = this._mid(t14, i10), _5 = this._mid(i10, h8), c13 = this._mid(r18, _5);
    this._addBezier2(s13, t14, r18, c13, e15), this._addBezier2(s13, c13, _5, h8, e15);
  }
  _addBezier3(s13, t14, i10, h8, e15, r18) {
    if (0 == r18--) return void s13.push(e15);
    const _5 = this._mid(t14, i10), c13 = this._mid(i10, h8), u13 = this._mid(h8, e15), o14 = this._mid(_5, c13), n18 = this._mid(c13, u13), a16 = this._mid(o14, n18);
    this._addBezier3(s13, t14, _5, o14, a16, r18), this._addBezier3(s13, a16, n18, u13, e15, r18);
  }
  _add90DegArc(s13, t14, i10, h8, e15) {
    const r18 = e15 ?? this._crossProduct(this._sub(i10, t14), this._sub(h8, t14)) > 0, _5 = this._mid(t14, i10), c13 = this._sub(_5, t14);
    r18 ? this._leftPerpendicular(c13) : this._rightPerpendicular(c13), _5[0] += c13[0], _5[1] += c13[1], this._addBezier3(s13, t14, this._mix(t14, 0.33333, _5, 0.66667), this._mix(i10, 0.33333, _5, 0.66667), i10, 4);
  }
  _addArrow(s13, t14, i10) {
    const h8 = t14[0], e15 = t14[1], r18 = t14[t14.length - 1], _5 = this._sub(h8, e15);
    this._normalize(_5);
    const c13 = this._crossProduct(_5, this._sub(r18, e15)), u13 = 0.5 * c13, o14 = this._leftPerp(_5), n18 = [r18[0] - o14[0] * c13, r18[1] - o14[1] * c13], a16 = t14.length - 1, p6 = [];
    p6.push(i10 ? [-o14[0], -o14[1]] : o14);
    let l16 = [-_5[0], -_5[1]];
    for (let d4 = 1; d4 < a16 - 1; d4++) {
      const s14 = this._sub(t14[d4 + 1], t14[d4]);
      this._normalize(s14);
      const i11 = this._dotProduct(s14, l16), h9 = this._crossProduct(s14, l16), e16 = Math.sqrt((1 + i11) / 2), r19 = this._sub(s14, l16);
      this._normalize(r19), r19[0] /= e16, r19[1] /= e16, p6.push(h9 < 0 ? [-r19[0], -r19[1]] : r19), l16 = s14;
    }
    p6.push(this._rightPerp(l16));
    for (let d4 = p6.length - 1; d4 > 0; d4--) s13.push([t14[d4][0] + p6[d4][0] * u13, t14[d4][1] + p6[d4][1] * u13]);
    s13.push([n18[0] + p6[0][0] * u13, n18[1] + p6[0][1] * u13]), s13.push([n18[0] + p6[0][0] * c13, n18[1] + p6[0][1] * c13]), s13.push(h8), s13.push([n18[0] - p6[0][0] * c13, n18[1] - p6[0][1] * c13]), s13.push([n18[0] - p6[0][0] * u13, n18[1] - p6[0][1] * u13]);
    for (let d4 = 1; d4 < p6.length; d4++) s13.push([t14[d4][0] - p6[d4][0] * u13, t14[d4][1] - p6[d4][1] * u13]);
  }
  _cp2(s13, t14, i10) {
    return s13.length >= 2 ? s13[1] : this._add2(s13[0], t14 * this._defaultSize, i10 * this._defaultSize);
  }
  _cp3(s13, t14, i10, h8) {
    if (s13.length >= 3) return s13[2];
    const e15 = this._mix(s13[0], 1 - i10, t14, i10), r18 = this._sub(t14, s13[0]);
    return this._normalize(r18), this._rightPerpendicular(r18), [e15[0] + r18[0] * h8 * this._defaultSize, e15[1] + r18[1] * h8 * this._defaultSize];
  }
  _arrowPath(s13) {
    if (s13.length > 2) return s13;
    const t14 = s13[0], i10 = this._cp2(s13, -4, 0), h8 = this._sub(t14, i10);
    this._normalize(h8);
    const e15 = this._rightPerp(h8);
    return [t14, i10, [t14[0] + (e15[0] - h8[0]) * this._defaultSize, t14[1] + (e15[1] - h8[1]) * this._defaultSize]];
  }
  _arrowLastSeg(s13) {
    const t14 = s13[0], i10 = this._cp2(s13, -4, 0);
    let h8;
    if (s13.length >= 3) h8 = s13[s13.length - 1];
    else {
      const s14 = this._sub(t14, i10);
      this._normalize(s14);
      const e15 = this._rightPerp(s14);
      h8 = [t14[0] + (e15[0] - s14[0]) * this._defaultSize, t14[1] + (e15[1] - s14[1]) * this._defaultSize];
    }
    return [i10, h8];
  }
  _processGeom(s13) {
    if (!s13) return null;
    const t14 = [];
    for (const i10 of s13) {
      if (!i10 || 0 === i10.length) continue;
      const s14 = i10.length;
      let h8 = i10[0];
      switch (this._rule) {
        case R.PerpendicularFromFirstSegment: {
          const s15 = this._cp2(i10, 0, -1), e15 = this._cp3(i10, s15, 0.5, 4), r18 = [];
          r18.push(e15), r18.push(this._mid(h8, s15)), t14.push(r18);
          break;
        }
        case R.ReversedFirstSegment: {
          const s15 = this._cp2(i10, 0, -1);
          t14.push([s15, h8]);
          break;
        }
        case R.PerpendicularToSecondSegment: {
          const s15 = this._cp2(i10, -4, 1), e15 = this._cp3(i10, s15, 0.882353, -1.94), r18 = [];
          r18.push(this._mid(s15, e15)), r18.push(h8), t14.push(r18);
          break;
        }
        case R.SecondSegmentWithTicks: {
          const s15 = this._cp2(i10, -4, 1), e15 = this._cp3(i10, s15, 0.882353, -1.94), r18 = this._sub(e15, s15);
          let _5;
          _5 = this._crossProduct(r18, this._sub(h8, s15)) > 0 ? this._rightPerp(_5) : this._leftPerp(r18);
          const c13 = [];
          c13.push([s15[0] + (_5[0] - r18[0]) / 3, s15[1] + (_5[1] - r18[1]) / 3]), c13.push(s15), c13.push(e15), c13.push([e15[0] + (_5[0] + r18[0]) / 3, e15[1] + (_5[1] + r18[1]) / 3]), t14.push(c13);
          break;
        }
        case R.DoublePerpendicular: {
          const s15 = this._cp2(i10, 0, -1), e15 = this._cp3(i10, s15, 0.5, 3), r18 = this._mid(h8, s15), _5 = this._sub(r18, e15);
          this._normalize(_5);
          const c13 = this._crossProduct(_5, this._sub(h8, e15));
          this._leftPerpendicular(_5);
          const u13 = [];
          u13.push(h8), u13.push([e15[0] + _5[0] * c13, e15[1] + _5[1] * c13]), t14.push(u13);
          const o14 = [];
          o14.push([e15[0] - _5[0] * c13, e15[1] - _5[1] * c13]), o14.push(s15), t14.push(o14);
          break;
        }
        case R.OppositeToFirstSegment: {
          const s15 = this._cp2(i10, 0, -1), e15 = this._cp3(i10, s15, 0.5, 3), r18 = this._mid(h8, s15), _5 = this._sub(r18, e15);
          this._normalize(_5);
          const c13 = this._crossProduct(_5, this._sub(h8, e15));
          this._leftPerpendicular(_5);
          const u13 = [];
          u13.push([e15[0] + _5[0] * c13, e15[1] + _5[1] * c13]), u13.push([e15[0] - _5[0] * c13, e15[1] - _5[1] * c13]), t14.push(u13);
          break;
        }
        case R.TriplePerpendicular: {
          const s15 = this._cp2(i10, 0, -1), e15 = this._cp3(i10, s15, 0.5, 4), r18 = this._mid(h8, s15), _5 = this._sub(r18, e15);
          this._normalize(_5);
          const c13 = this._crossProduct(_5, this._sub(h8, e15));
          this._leftPerpendicular(_5);
          const u13 = [];
          u13.push([e15[0] + _5[0] * c13 * 0.8, e15[1] + _5[1] * c13 * 0.8]), u13.push([r18[0] + 0.8 * (h8[0] - r18[0]), r18[1] + 0.8 * (h8[1] - r18[1])]), t14.push(u13), t14.push([e15, r18]);
          const o14 = [];
          o14.push([e15[0] - _5[0] * c13 * 0.8, e15[1] - _5[1] * c13 * 0.8]), o14.push([r18[0] + 0.8 * (s15[0] - r18[0]), r18[1] + 0.8 * (s15[1] - r18[1])]), t14.push(o14);
          break;
        }
        case R.HalfCircleFirstSegment: {
          const s15 = this._cp2(i10, 0, -1), e15 = this._cp3(i10, s15, 0.5, 4), r18 = this._mid(h8, s15);
          let _5 = this._sub(s15, h8);
          const c13 = Math.cos(Math.PI / 18), u13 = Math.sin(Math.PI / 18), o14 = Math.sqrt((1 + c13) / 2), n18 = Math.sqrt((1 - c13) / 2), a16 = [];
          let p6;
          this._crossProduct(_5, this._sub(e15, h8)) > 0 ? (a16.push(h8), _5 = this._sub(h8, r18), p6 = s15) : (a16.push(s15), _5 = this._sub(s15, r18), p6 = h8), this._rotateDirect(_5, o14, n18), _5[0] /= o14, _5[1] /= o14;
          for (let t15 = 1; t15 <= 18; t15++) a16.push(this._add(r18, _5)), this._rotateDirect(_5, c13, u13);
          a16.push(p6), t14.push(a16);
          break;
        }
        case R.HalfCircleSecondSegment: {
          const s15 = this._cp2(i10, 0, -1), e15 = this._cp3(i10, s15, 1, -1);
          let r18 = this._sub(h8, s15);
          this._normalize(r18);
          const _5 = this._crossProduct(r18, this._sub(e15, s15)) / 2;
          this._leftPerpendicular(r18);
          const c13 = [s15[0] + r18[0] * _5, s15[1] + r18[1] * _5];
          r18 = this._sub(s15, c13);
          const u13 = Math.cos(Math.PI / 18);
          let o14 = Math.sin(Math.PI / 18);
          _5 > 0 && (o14 = -o14);
          const n18 = [s15];
          for (let t15 = 1; t15 <= 18; t15++) this._rotateDirect(r18, u13, o14), n18.push(this._add(c13, r18));
          t14.push(n18);
          break;
        }
        case R.HalfCircleExtended: {
          const e15 = this._cp2(i10, 0, -2), r18 = this._cp3(i10, e15, 1, -1);
          let _5;
          if (s14 >= 4) _5 = i10[3];
          else {
            const s15 = this._sub(h8, e15);
            _5 = this._add(r18, s15);
          }
          const c13 = this._dist(e15, r18) / 2 / 0.75, u13 = this._sub(e15, h8);
          this._normalize(u13, c13);
          const o14 = this._sub(r18, _5);
          this._normalize(o14, c13);
          const n18 = [_5, r18];
          t14.push(n18);
          const a16 = [this._clone(r18)];
          this._addBezier3(a16, r18, this._add(r18, o14), this._add(e15, u13), e15, 4), a16.push(h8), t14.push(a16);
          break;
        }
        case R.OpenCircle: {
          const s15 = this._cp2(i10, -2, 0), e15 = this._sub(s15, h8), r18 = Math.cos(Math.PI / 18), _5 = -Math.sin(Math.PI / 18), c13 = [s15];
          for (let t15 = 1; t15 <= 33; t15++) this._rotateDirect(e15, r18, _5), c13.push(this._add(h8, e15));
          t14.push(c13);
          break;
        }
        case R.CoverageEdgesWithTicks: {
          const e15 = this._cp2(i10, 0, -1);
          let r18, _5;
          if (s14 >= 3) r18 = i10[2];
          else {
            const s15 = this._sub(e15, h8), t15 = this._leftPerp(s15);
            r18 = [h8[0] + t15[0] - 0.25 * s15[0], h8[1] + t15[1] - 0.25 * s15[1]];
          }
          if (s14 >= 4) _5 = i10[3];
          else {
            const s15 = this._mid(h8, e15), t15 = this._sub(h8, e15);
            this._normalize(t15), this._leftPerpendicular(t15);
            const i11 = this._crossProduct(t15, this._sub(r18, s15));
            this._rightPerpendicular(t15), _5 = [r18[0] + t15[0] * i11 * 2, r18[1] + t15[1] * i11 * 2];
          }
          const c13 = this._sub(e15, h8);
          let u13, o14;
          u13 = this._crossProduct(c13, this._sub(r18, h8)) > 0 ? this._rightPerp(c13) : this._leftPerp(c13), o14 = [], o14.push(r18), o14.push(h8), o14.push([h8[0] + (u13[0] - c13[0]) / 3, h8[1] + (u13[1] - c13[1]) / 3]), t14.push(o14), u13 = this._crossProduct(c13, this._sub(_5, e15)) > 0 ? this._rightPerp(u13) : this._leftPerp(c13), o14 = [], o14.push([e15[0] + (u13[0] + c13[0]) / 3, e15[1] + (u13[1] + c13[1]) / 3]), o14.push(e15), o14.push(_5), t14.push(o14);
          break;
        }
        case R.GapExtentWithDoubleTicks: {
          const e15 = this._cp2(i10, 0, 2), r18 = this._cp3(i10, e15, 0, 1);
          let _5;
          if (s14 >= 4) _5 = i10[3];
          else {
            const s15 = this._sub(e15, h8);
            _5 = this._add(r18, s15);
          }
          this._addAngledTicks(t14, h8, e15, this._mid(r18, _5)), this._addAngledTicks(t14, r18, _5, this._mid(h8, e15));
          break;
        }
        case R.GapExtentMidline: {
          const e15 = this._cp2(i10, 2, 0), r18 = this._cp3(i10, e15, 0, 1);
          let _5;
          if (s14 >= 4) _5 = i10[3];
          else {
            const s15 = this._sub(e15, h8);
            _5 = this._add(r18, s15);
          }
          const c13 = [];
          c13.push(this._mid(h8, r18)), c13.push(this._mid(e15, _5)), t14.push(c13);
          break;
        }
        case R.Chevron: {
          const e15 = this._cp2(i10, -1, -1);
          let r18;
          if (s14 >= 3) r18 = i10[2];
          else {
            const s15 = this._sub(e15, h8);
            this._leftPerpendicular(s15), r18 = this._add(h8, s15);
          }
          t14.push([e15, this._makeCtrlPt(h8), r18]);
          break;
        }
        case R.PerpendicularWithArc: {
          const s15 = this._cp2(i10, 0, -2), e15 = this._cp3(i10, s15, 0.5, -1);
          let r18 = this._sub(s15, h8);
          const _5 = this._norm(r18);
          r18[0] /= _5, r18[1] /= _5;
          const c13 = this._crossProduct(r18, this._sub(e15, h8));
          let u13 = this._dotProduct(r18, this._sub(e15, h8));
          u13 < 0.05 * _5 ? u13 = 0.05 * _5 : u13 > 0.95 * _5 && (u13 = 0.95 * _5);
          const o14 = [h8[0] + r18[0] * u13, h8[1] + r18[1] * u13];
          this._leftPerpendicular(r18);
          let n18 = [];
          n18.push([o14[0] - r18[0] * c13, o14[1] - r18[1] * c13]), n18.push([o14[0] + r18[0] * c13, o14[1] + r18[1] * c13]), t14.push(n18);
          const a16 = [s15[0] + r18[0] * c13, s15[1] + r18[1] * c13];
          r18 = this._sub(s15, a16);
          const p6 = Math.cos(Math.PI / 18);
          let l16 = Math.sin(Math.PI / 18);
          c13 < 0 && (l16 = -l16), n18 = [h8, s15];
          for (let t15 = 1; t15 <= 9; t15++) this._rotateDirect(r18, p6, l16), n18.push(this._add(a16, r18));
          t14.push(n18);
          break;
        }
        case R.ClosedHalfCircle: {
          const s15 = this._cp2(i10, 2, 0), e15 = this._mid(h8, s15), r18 = this._sub(s15, e15), _5 = Math.cos(Math.PI / 18), c13 = Math.sin(Math.PI / 18), u13 = [h8, s15];
          for (let t15 = 1; t15 <= 18; t15++) this._rotateDirect(r18, _5, c13), u13.push(this._add(e15, r18));
          t14.push(u13);
          break;
        }
        case R.TripleParallelExtended: {
          const s15 = this._cp2(i10, 0, -2), r18 = this._cp3(i10, s15, 1, -2), _5 = this._mid(h8, s15), c13 = this._sub(r18, s15);
          this._normalize(c13);
          const u13 = Math.abs(this._crossProduct(c13, this._sub(_5, s15))) / 2, o14 = this._dist(s15, r18), n18 = [s15, h8];
          n18.push([h8[0] + c13[0] * o14 * 0.5, h8[1] + c13[1] * o14 * 0.5]), t14.push(n18);
          const a16 = [];
          a16.push([_5[0] - c13[0] * u13, _5[1] - c13[1] * u13]), a16.push([_5[0] + c13[0] * o14 * 0.375, _5[1] + c13[1] * o14 * 0.375]), j2(a16[a16.length - 1], 1), a16.push([_5[0] + c13[0] * o14 * 0.75, _5[1] + c13[1] * o14 * 0.75]), t14.push(a16);
          const p6 = [s15, r18];
          t14.push(p6);
          break;
        }
        case R.ParallelWithTicks: {
          const s15 = this._cp2(i10, 3, 0), e15 = this._cp3(i10, s15, 0.5, -1), r18 = this._sub(e15, s15);
          this._normalize(r18);
          const _5 = this._crossProduct(r18, this._sub(e15, h8));
          this._leftPerpendicular(r18), this._addAngledTicks(t14, h8, s15, e15), this._addAngledTicks(t14, this._mix(h8, 1, r18, _5), this._mix(s15, 1, r18, _5), this._mid(h8, s15));
          break;
        }
        case R.Parallel: {
          const s15 = this._cp2(i10, 3, 0), e15 = this._cp3(i10, s15, 0.5, -1), r18 = this._sub(s15, h8);
          this._normalize(r18);
          const _5 = this._leftPerp(r18), c13 = this._crossProduct(r18, this._sub(e15, h8));
          let u13 = [h8, s15];
          t14.push(u13), u13 = [], u13.push([h8[0] + _5[0] * c13, h8[1] + _5[1] * c13]), u13.push([s15[0] + _5[0] * c13, s15[1] + _5[1] * c13]), t14.push(u13);
          break;
        }
        case R.PerpendicularToFirstSegment: {
          const s15 = this._cp2(i10, 3, 0), e15 = this._cp3(i10, s15, 0.5, -1), r18 = this._mid(h8, s15), _5 = this._sub(s15, h8);
          this._normalize(_5);
          const c13 = this._crossProduct(_5, this._sub(e15, h8));
          this._leftPerpendicular(_5);
          const u13 = [];
          u13.push([r18[0] - _5[0] * c13 * 0.25, r18[1] - _5[1] * c13 * 0.25]), u13.push([r18[0] + _5[0] * c13 * 1.25, r18[1] + _5[1] * c13 * 1.25]), t14.push(u13);
          break;
        }
        case R.ParallelOffset: {
          const s15 = this._cp2(i10, 3, 0), e15 = this._cp3(i10, s15, 0.5, -1), r18 = this._sub(s15, h8);
          this._normalize(r18);
          const _5 = this._crossProduct(r18, this._sub(e15, h8));
          this._leftPerpendicular(r18);
          const c13 = [];
          c13.push([h8[0] - r18[0] * _5, h8[1] - r18[1] * _5]), c13.push([s15[0] - r18[0] * _5, s15[1] - r18[1] * _5]), t14.push(c13);
          const u13 = [];
          u13.push([h8[0] + r18[0] * _5, h8[1] + r18[1] * _5]), u13.push([s15[0] + r18[0] * _5, s15[1] + r18[1] * _5]), t14.push(u13);
          break;
        }
        case R.OffsetOpposite: {
          const s15 = this._cp2(i10, 3, 0), e15 = this._cp3(i10, s15, 0.5, -1), r18 = this._sub(s15, h8);
          this._normalize(r18);
          const _5 = this._crossProduct(r18, this._sub(e15, h8));
          this._leftPerpendicular(r18);
          const c13 = [];
          c13.push([h8[0] - r18[0] * _5, h8[1] - r18[1] * _5]), c13.push([s15[0] - r18[0] * _5, s15[1] - r18[1] * _5]), t14.push(c13);
          break;
        }
        case R.OffsetSame: {
          const s15 = this._cp2(i10, 3, 0), e15 = this._cp3(i10, s15, 0.5, -1), r18 = this._sub(s15, h8);
          this._normalize(r18);
          const _5 = this._crossProduct(r18, this._sub(e15, h8));
          this._leftPerpendicular(r18);
          const c13 = [];
          c13.push([h8[0] + r18[0] * _5, h8[1] + r18[1] * _5]), c13.push([s15[0] + r18[0] * _5, s15[1] + r18[1] * _5]), t14.push(c13);
          break;
        }
        case R.CircleWithArc: {
          let r18 = this._cp2(i10, 3, 0);
          const _5 = this._cp3(i10, r18, 0.5, -1);
          let c13, u13;
          if (s14 >= 4) c13 = i10[3], u13 = this._crossProduct(this._sub(c13, r18), this._sub(_5, r18)) > 0;
          else {
            c13 = r18, u13 = this._crossProduct(this._sub(c13, h8), this._sub(_5, h8)) > 0;
            const s15 = 24 * this._geomUnitsPerPoint, t15 = this._sub(c13, h8);
            this._normalize(t15, s15);
            const i11 = Math.sqrt(2) / 2;
            this._rotateDirect(t15, i11, u13 ? i11 : -i11), r18 = this._add(h8, t15);
          }
          const o14 = this._sub(r18, h8), n18 = Math.cos(Math.PI / 18), a16 = Math.sin(Math.PI / 18), p6 = [r18];
          for (let s15 = 1; s15 <= 36; s15++) this._rotateDirect(o14, n18, a16), p6.push(this._add(h8, o14));
          this._add90DegArc(p6, r18, c13, _5, u13), j2(p6[p6.length - 8], 1), t14.push(p6);
          break;
        }
        case R.DoubleJog: {
          let e15, r18, _5 = this._cp2(i10, -3, 1);
          if (e15 = s14 >= 3 ? i10[2] : this._add(h8, this._sub(h8, _5)), s14 >= 4) r18 = i10[3];
          else {
            const s15 = h8;
            h8 = _5, r18 = e15;
            const t15 = this._dist(h8, s15), i11 = this._dist(r18, s15);
            let c14 = 30 * this._geomUnitsPerPoint;
            0.5 * t15 < c14 && (c14 = 0.5 * t15), 0.5 * i11 < c14 && (c14 = 0.5 * i11), _5 = this._mix(h8, c14 / t15, s15, (t15 - c14) / t15), e15 = this._mix(r18, c14 / i11, s15, (i11 - c14) / i11);
          }
          const c13 = this._mid(h8, _5), u13 = this._mid(r18, e15), o14 = this._dist(h8, _5), n18 = this._dist(e15, r18);
          let a16 = Math.min(o14, n18) / 8;
          a16 = Math.min(a16, 24 * this._geomUnitsPerPoint);
          const p6 = Math.cos(Math.PI / 4);
          let l16 = this._sub(h8, _5);
          this._normalize(l16, a16), this._crossProduct(l16, this._sub(r18, _5)) > 0 ? this._rotateDirect(l16, p6, -p6) : this._rotateDirect(l16, p6, p6);
          let d4 = [];
          d4.push(_5), d4.push(this._add(c13, l16)), d4.push(this._sub(c13, l16)), d4.push(h8), t14.push(d4), l16 = this._sub(r18, e15), this._normalize(l16, a16), this._crossProduct(l16, this._sub(h8, e15)) < 0 ? this._rotateDirect(l16, p6, p6) : this._rotateDirect(l16, p6, -p6), d4 = [], d4.push(e15), d4.push(this._add(u13, l16)), d4.push(this._sub(u13, l16)), d4.push(r18), t14.push(d4);
          break;
        }
        case R.PerpendicularOffset: {
          const s15 = this._cp2(i10, -4, 1), e15 = this._cp3(i10, s15, 0.882353, -1.94), r18 = this._sub(e15, s15);
          this._crossProduct(r18, this._sub(h8, s15)) > 0 ? this._rightPerpendicular(r18) : this._leftPerpendicular(r18);
          const _5 = [r18[0] / 8, r18[1] / 8], c13 = this._sub(this._mid(s15, e15), _5);
          t14.push([c13, h8]);
          break;
        }
        case R.LineExcludingLastSegment: {
          const s15 = this._arrowPath(i10), h9 = [];
          let e15 = s15.length - 2;
          for (; e15--; ) h9.push(s15[e15]);
          t14.push(h9);
          break;
        }
        case R.MultivertexArrow: {
          const s15 = this._arrowPath(i10), h9 = [];
          this._addArrow(h9, s15, false), t14.push(h9);
          break;
        }
        case R.CrossedArrow: {
          const s15 = this._arrowPath(i10), h9 = [];
          this._addArrow(h9, s15, true), t14.push(h9);
          break;
        }
        case R.ChevronArrow: {
          const [s15, e15] = this._arrowLastSeg(i10), r18 = 10 * this._geomUnitsPerPoint, _5 = this._sub(h8, s15);
          this._normalize(_5);
          const c13 = this._crossProduct(_5, this._sub(e15, s15)), u13 = this._leftPerp(_5), o14 = [e15[0] - u13[0] * c13 * 2, e15[1] - u13[1] * c13 * 2], n18 = [];
          n18.push([e15[0] + _5[0] * r18, e15[1] + _5[1] * r18]), n18.push(h8), n18.push([o14[0] + _5[0] * r18, o14[1] + _5[1] * r18]), t14.push(n18);
          break;
        }
        case R.ChevronArrowOffset: {
          const [s15, e15] = this._arrowLastSeg(i10), r18 = this._sub(h8, s15);
          this._normalize(r18);
          const _5 = this._crossProduct(r18, this._sub(e15, s15));
          this._leftPerpendicular(r18);
          const c13 = [e15[0] - r18[0] * _5, e15[1] - r18[1] * _5], u13 = [];
          u13.push([c13[0] + r18[0] * _5 * 0.5, c13[1] + r18[1] * _5 * 0.5]), u13.push(this._mid(c13, h8)), u13.push([c13[0] - r18[0] * _5 * 0.5, c13[1] - r18[1] * _5 * 0.5]), t14.push(u13);
          break;
        }
        case R.PartialFirstSegment: {
          const [s15, e15] = this._arrowLastSeg(i10), r18 = this._sub(h8, s15);
          this._normalize(r18);
          const _5 = this._crossProduct(r18, this._sub(e15, s15));
          this._leftPerpendicular(r18);
          const c13 = [e15[0] - r18[0] * _5, e15[1] - r18[1] * _5];
          t14.push([s15, c13]);
          break;
        }
        case R.Arch: {
          const s15 = this._cp2(i10, 0, -1), e15 = this._cp3(i10, s15, 0.5, 1), r18 = this._sub(h8, s15), _5 = this._mix(e15, 1, r18, 0.55), c13 = this._mix(e15, 1, r18, -0.55), u13 = [h8];
          this._addBezier2(u13, h8, _5, e15, 4), this._addBezier2(u13, e15, c13, s15, 4), t14.push(u13);
          break;
        }
        case R.CurvedParallelTicks: {
          const s15 = this._cp2(i10, -4, 1), e15 = this._cp3(i10, s15, 0.882353, -1.94), r18 = this._sub(e15, s15);
          this._crossProduct(r18, this._sub(h8, s15)) > 0 ? this._rightPerpendicular(r18) : this._leftPerpendicular(r18);
          const _5 = [r18[0] / 8, r18[1] / 8], c13 = this._sub(this._mid(s15, e15), _5), u13 = this._sub(this._mix(s15, 0.75, e15, 0.25), _5), o14 = this._sub(this._mix(s15, 0.25, e15, 0.75), _5), n18 = [s15];
          this._addBezier2(n18, s15, u13, c13, 3), this._addBezier2(n18, c13, o14, e15, 3), t14.push(n18);
          for (let i11 = 0; i11 < 8; i11++) {
            const s16 = n18[2 * i11 + 1], h9 = [this._clone(s16)];
            h9.push(this._add(s16, [r18[0] / 4, r18[1] / 4])), t14.push(h9);
          }
          break;
        }
        case R.Arc90Degrees: {
          const s15 = this._cp2(i10, 0, -1), e15 = this._cp3(i10, s15, 0.5, 1), r18 = [s15];
          this._add90DegArc(r18, s15, h8, e15), t14.push(r18);
          break;
        }
        case R.FullGeometry:
        default:
          t14.push(i10);
      }
    }
    return t14;
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectCut.js
var u9 = class _u {
  static local() {
    return null === _u.instance && (_u.instance = new _u()), _u.instance;
  }
  execute(e15, t14, u13, i10, r18) {
    return new s9(e15, t14, u13);
  }
};
u9.instance = null;
var s9 = class extends U2 {
  constructor(e15, u13, s13) {
    super(e15, true, true), this._curveHelper = new n8(), this._beginCut = (void 0 !== u13.beginCut ? u13.beginCut : 1) * s13, this._endCut = (void 0 !== u13.endCut ? u13.endCut : 1) * s13, this._middleCut = (void 0 !== u13.middleCut ? u13.middleCut : 0) * s13, this._invert = void 0 !== u13.invert && u13.invert, this._beginCut < 0 && (this._beginCut = 0), this._endCut < 0 && (this._endCut = 0), this._middleCut < 0 && (this._middleCut = 0);
  }
  processPath(e15) {
    const t14 = this._beginCut, u13 = this._endCut, s13 = this._middleCut, i10 = this._curveHelper.calculatePathLength(e15), r18 = [];
    if (this._invert) if (0 === t14 && 0 === u13 && 0 === s13) ;
    else if (t14 + u13 + s13 >= i10) r18.push(e15);
    else {
      let n18 = this._curveHelper.getSubCurve(e15, 0, t14);
      n18 && r18.push(n18), n18 = this._curveHelper.getSubCurve(e15, 0.5 * (i10 - s13), 0.5 * (i10 + s13)), n18 && r18.push(n18), n18 = this._curveHelper.getSubCurve(e15, i10 - u13, u13), n18 && r18.push(n18);
    }
    else if (0 === t14 && 0 === u13 && 0 === s13) r18.push(e15);
    else if (t14 + u13 + s13 >= i10) ;
    else if (0 === s13) {
      const s14 = this._curveHelper.getSubCurve(e15, t14, i10 - u13);
      s14 && r18.push(s14);
    } else {
      let n18 = this._curveHelper.getSubCurve(e15, t14, 0.5 * (i10 - s13));
      n18 && r18.push(n18), n18 = this._curveHelper.getSubCurve(e15, 0.5 * (i10 + s13), i10 - u13), n18 && r18.push(n18);
    }
    return 0 === r18.length ? null : { paths: r18 };
  }
};

// node_modules/@arcgis/core/symbols/cim/GeometryWalker.js
var i7 = 1e-7;
var n9 = class {
  constructor() {
    this._values = [], this.extPtGap = 0, this.ctrlPtGap = 0, this._length = 0, this._currentValue = 0;
  }
  isEmpty() {
    return 0 === this._values.length;
  }
  size() {
    return this._values.length;
  }
  init(t14, s13, e15 = true) {
    if (this._setEmpty(), !t14 || 0 === t14.length) return false;
    for (let n18 = 0; n18 < t14.length; n18++) {
      let s14 = Math.abs(t14[n18]);
      e15 && s14 < i7 && (s14 = i7), this._values.push(s14), this._length += s14;
    }
    return s13 && 1 & t14.length && (this._length *= 2), 0 !== this._length && (this.ctrlPtGap = this.extPtGap = 0, this._currentValue = -1, true);
  }
  scale(t14) {
    const s13 = this._values ? this._values.length : 0;
    for (let e15 = 0; e15 < s13; ++e15) this._values[e15] *= t14;
    this._length *= t14, this.extPtGap *= t14, this.ctrlPtGap *= t14;
  }
  addValue(t14) {
    this._length += t14, this._values.push(t14);
  }
  firstValue() {
    return this._values[0];
  }
  lastValue() {
    return this._values[this._values.length - 1];
  }
  nextValue() {
    return this._currentValue++, this._currentValue === this._values.length && (this._currentValue = 0), this._values[this._currentValue];
  }
  reset() {
    this._currentValue = -1;
  }
  length() {
    return this._length;
  }
  _setEmpty() {
    this.extPtGap = this.ctrlPtGap = this._length = 0, this._currentValue = -1, this._values.length = 0;
  }
};
var h4 = class {
  constructor() {
    this.pt = null, this.ca = 0, this.sa = 0;
  }
};
var r7;
!function(t14) {
  t14[t14.FAIL = 0] = "FAIL", t14[t14.END = 1] = "END", t14[t14.CONTINUE = 2] = "CONTINUE";
}(r7 || (r7 = {}));
var a6 = class {
  constructor() {
    this.reset();
  }
  reset() {
    this.segment = -1, this.segmentLength = 0, this.abscissa = 0, this.isPathEnd = false, this.isPartEnd = false;
  }
  isValid() {
    return -1 !== this.segment;
  }
  copyTo(t14) {
    t14.segment = this.segment, t14.segmentLength = this.segmentLength, t14.abscissa = this.abscissa, t14.isPathEnd = this.isPathEnd, t14.isPartEnd = this.isPartEnd;
  }
};
var g5 = class extends n8 {
  constructor(t14 = 0, s13 = false) {
    super(t14, s13), this._tolerance = e9, this._currentPosition = new a6();
  }
  updateTolerance(t14) {
    this._tolerance = e9 * t14;
  }
  init(t14, s13, e15 = true) {
    return e15 ? (this._patternLength = s13.length(), this._partExtPtGap = s13.extPtGap, this._partCtrlPtGap = s13.ctrlPtGap) : (this._patternLength = 0, this._partExtPtGap = 0, this._partCtrlPtGap = 0), this._currentPosition.reset(), this._partSegCount = 0, this._path = t14, this._seg = -1, this._setPosAtNextPart();
  }
  curPositionIsValid() {
    return this._currentPosition.isValid();
  }
  nextPosition(t14, s13 = r7.FAIL) {
    const e15 = new a6();
    return !!this._nextPosition(t14, e15, null, s13) && (e15.copyTo(this._currentPosition), true);
  }
  curPointAndAngle(t14) {
    t14.pt = this._getPoint(this._currentPosition);
    const [s13, e15] = this._getAngle(this._currentPosition);
    t14.ca = s13, t14.sa = e15;
  }
  nextPointAndAngle(t14, s13, e15 = r7.FAIL) {
    const i10 = new a6();
    if (!this._nextPosition(t14, i10, null, e15)) return false;
    i10.copyTo(this._currentPosition), s13.pt = this._getPoint(i10);
    const [n18, h8] = this._getAngle(i10);
    return s13.ca = n18, s13.sa = h8, true;
  }
  nextCurve(t14) {
    if (0 === t14) return null;
    const s13 = [], e15 = new a6();
    return this._nextPosition(t14, e15, s13, r7.END) ? (e15.copyTo(this._currentPosition), s13) : null;
  }
  isPathEnd() {
    return this._currentPosition.isPathEnd;
  }
  getPathEnd() {
    if (-1 === this._currentPosition.segment) throw new Error("missing segment");
    return this._path[this._currentPosition.segment + 1];
  }
  _nextPosition(t14, s13, e15, i10) {
    if (this._currentPosition.isPathEnd) return false;
    let n18 = this._currentPosition.abscissa;
    for (this._currentPosition.segmentLength > 0 && (n18 /= this._currentPosition.segmentLength), this._currentPosition.copyTo(s13); s13.abscissa + t14 * this._partLengthRatio > s13.segmentLength + this._tolerance; ) {
      if (e15) {
        if (0 === e15.length) if (0 === n18) {
          const t16 = this._path[s13.segment];
          e15.push([t16[0], t16[1]]);
        } else e15.push(this.getSegCoord2D(this._path, s13.segment, n18));
        const t15 = this._path[s13.segment + 1];
        e15.push([t15[0], t15[1]]);
      }
      if (n18 = 0, t14 -= (s13.segmentLength - s13.abscissa) / this._partLengthRatio, this._partSegCount) s13.segment = this._nextSegment(), s13.segmentLength = this.calculateSegLength(this._path, s13.segment), s13.abscissa = 0, this._partSegCount--;
      else {
        if (!this._setPosAtNextPart()) return i10 !== r7.FAIL && (s13.segmentLength = this.calculateSegLength(this._path, s13.segment), s13.isPartEnd = true, i10 === r7.END ? (s13.abscissa = s13.segmentLength, s13.isPathEnd = true) : s13.abscissa = s13.segmentLength + t14, true);
        this._currentPosition.copyTo(s13);
      }
    }
    if (s13.abscissa += t14 * this._partLengthRatio, e15) {
      if (0 === e15.length) if (0 === n18) {
        const t16 = this._path[s13.segment];
        e15.push([t16[0], t16[1]]);
      } else e15.push(this.getSegCoord2D(this._path, s13.segment, n18));
      const t15 = s13.abscissa / s13.segmentLength;
      if (1 === t15) {
        const t16 = this._path[s13.segment + 1];
        e15.push([t16[0], t16[1]]);
      } else e15.push(this.getSegCoord2D(this._path, s13.segment, t15));
    }
    return this._partSegCount || Math.abs(s13.abscissa - s13.segmentLength) < this._tolerance && (s13.isPathEnd = this._partIsLast, s13.isPartEnd = true), true;
  }
  _getPoint(t14) {
    if (-1 === t14.segment) throw new Error("missing segment");
    const s13 = t14.segmentLength <= 0 ? 0 : t14.abscissa / t14.segmentLength;
    return this.getSegCoord2D(this._path, t14.segment, s13);
  }
  _getAngle(t14) {
    if (-1 === t14.segment) throw new Error("missing segment");
    const s13 = t14.segmentLength <= 0 ? 0 : t14.abscissa / t14.segmentLength;
    return this.getSegAngleCS(this._path, t14.segment, s13);
  }
  _setPosAtNextPart() {
    for (; this._partSegCount; ) this._hasNextSegment() && this._nextSegment(), this._partSegCount--;
    if (!this._hasNextSegment()) return false;
    for (this._partLength = 0, this._partIsLast = true, this._partSegCount = 0; this._hasNextSegment(); ) if (this._partLength += this.calculateSegLength(this._path, this._nextSegment()), this._partSegCount++, 1 === I2(this._path[this._getEndPointIndex()])) {
      this._partIsLast = !this._hasNextSegment();
      break;
    }
    let s13 = this._partSegCount;
    for (; s13; ) this._previousSegment(), --s13;
    this._currentPosition.segment = this._nextSegment(), this._currentPosition.segmentLength = this.calculateSegLength(this._path, this._currentPosition.segment), this._currentPosition.abscissa = 0, this._currentPosition.isPathEnd = this._currentPosition.isPartEnd = false, --this._partSegCount;
    const e15 = this._getStartPointIndex();
    this._ctrlPtBegin = 1 === I2(this._path[e15]);
    let i10 = e15 + this._partSegCount + 1;
    if (i10 >= this._path.length && (i10 = 0), this._ctrlPtEnd = 1 === I2(this._path[i10]), this._patternLength > 0) {
      const t14 = this._ctrlPtBegin ? this._partCtrlPtGap : this._partExtPtGap, s14 = this._ctrlPtEnd ? this._partCtrlPtGap : this._partExtPtGap;
      let e16 = Math.round((this._partLength - (t14 + s14)) / this._patternLength);
      e16 <= 0 && (e16 = t14 + s14 > 0 ? 0 : 1), this._partLengthRatio = this._partLength / (t14 + s14 + e16 * this._patternLength), this._partLengthRatio < 0.01 && (this._partLengthRatio = 1);
    } else this._partLengthRatio = 1;
    return true;
  }
  _hasNextSegment() {
    return this._seg < this._path.length - 2;
  }
  _previousSegment() {
    return --this._seg;
  }
  _nextSegment() {
    return ++this._seg;
  }
  _getStartPointIndex() {
    return this._seg;
  }
  _getEndPointIndex() {
    return this._seg + 1;
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectDashes.js
var r8 = class _r {
  static local() {
    return null === _r.instance && (_r.instance = new _r()), _r.instance;
  }
  execute(t14, e15, s13, a16, i10) {
    return new n10(t14, e15, s13);
  }
};
r8.instance = null;
var n10 = class extends U2 {
  constructor(t14, e15, s13) {
    super(t14, true, true), this._firstCurve = null, this._walker = new g5(), this._walker.updateTolerance(s13), this._endings = e15.lineDashEnding, this._customDashPos = -(e15.offsetAlongLine ?? 0) * s13, this._offsetAtEnd = (e15.customEndingOffset ?? 0) * s13, this._pattern = new n9(), this._pattern.init(e15.dashTemplate, true), this._pattern.scale(s13);
  }
  processPath(t14) {
    if (0 === this._pattern.length()) return this.iteratePath = false, { paths: [t14] };
    if (!this.iteratePath) {
      let e15 = true;
      switch (this._endings) {
        case k.HalfPattern:
        case k.HalfGap:
        default:
          this._pattern.extPtGap = 0;
          break;
        case k.FullPattern:
          this.isClosed || (this._pattern.extPtGap = 0.5 * this._pattern.firstValue());
          break;
        case k.FullGap:
          this.isClosed || (this._pattern.extPtGap = 0.5 * this._pattern.lastValue());
          break;
        case k.NoConstraint:
          this.isClosed || (e15 = false);
          break;
        case k.Custom:
          this.isClosed || (this._pattern.extPtGap = 0.5 * this._offsetAtEnd);
      }
      const a17 = this._walker.calculatePathLength(t14);
      if (this._pattern.isEmpty() || a17 < 0.1 * this._pattern.length()) return { paths: [t14] };
      if (!this._walker.init(t14, this._pattern, e15)) return { paths: [t14] };
    }
    let a16;
    if (this.iteratePath) a16 = this._pattern.nextValue();
    else {
      let t15;
      switch (this._endings) {
        case k.HalfPattern:
        default:
          t15 = 0.5 * this._pattern.firstValue();
          break;
        case k.HalfGap:
          t15 = 0.5 * -this._pattern.lastValue();
          break;
        case k.FullGap:
          t15 = -this._pattern.lastValue();
          break;
        case k.FullPattern:
          t15 = 0;
          break;
        case k.NoConstraint:
        case k.Custom:
          t15 = -this._customDashPos;
      }
      let e15 = t15 / this._pattern.length();
      e15 -= Math.floor(e15), t15 = e15 * this._pattern.length(), this._pattern.reset(), a16 = this._pattern.nextValue();
      let i11 = false;
      for (; t15 >= a16; ) t15 -= a16, a16 = this._pattern.nextValue(), i11 = !i11;
      a16 -= t15, i11 ? (this._walker.nextPosition(a16), a16 = this._pattern.nextValue()) : this.isClosed && (this._firstCurve = this._walker.nextCurve(a16), a16 = this._pattern.nextValue(), this._walker.nextPosition(a16), a16 = this._pattern.nextValue());
    }
    let i10 = this._walker.nextCurve(a16);
    return i10 ? this._walker.isPathEnd() ? (this.iteratePath = false, this._firstCurve && (this._firstCurve.splice(0, 1), s8.mergePath(i10, this._firstCurve), this._firstCurve = null)) : (a16 = this._pattern.nextValue(), !this._walker.nextPosition(a16) || this._walker.isPathEnd() ? (this.iteratePath = false, this._firstCurve && (i10 = this._firstCurve, this._firstCurve = null)) : this.iteratePath = true) : (this.iteratePath = false, i10 = this._firstCurve, this._firstCurve = null), { paths: [i10] };
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectDonut.js
var h5 = class _h {
  static local() {
    return null === _h.instance && (_h.instance = new _h()), _h.instance;
  }
  execute(t14, i10, e15, s13, n18) {
    return new m5(t14, i10, e15, s13, n18);
  }
};
h5.instance = null;
var m5 = class {
  constructor(t14, i10, e15, s13, r18) {
    switch (this._inputGeometries = t14, this._tileKey = s13, this._geometryEngine = r18, this._width = (void 0 !== i10.width ? i10.width : 2) * e15, i10.method) {
      case B.Mitered:
      case B.Bevelled:
      case B.Rounded:
      case B.TrueBuffer:
      case B.Square:
    }
    this._option = i10.option;
  }
  next() {
    let n18;
    for (; n18 = this._inputGeometries.next(); ) {
      if (u2(n18) && this._width > 0) {
        if (Math.min(n18.xmax - n18.xmin, n18.ymax - n18.ymin) - 2 * this._width < 0) return n18;
        const t14 = [];
        return t14.push([[n18.xmin, n18.ymin], [n18.xmin, n18.ymax], [n18.xmax, n18.ymax], [n18.xmax, n18.ymin], [n18.xmin, n18.ymin]]), t14.push([[n18.xmin + this._width, n18.ymin + this._width], [n18.xmax - this._width, n18.ymin + this._width], [n18.xmax - this._width, n18.ymax - this._width], [n18.xmin + this._width, n18.ymax - this._width], [n18.xmin + this._width, n18.ymin + this._width]]), { rings: t14 };
      }
      if (y(n18)) {
        let i10 = null;
        const e15 = this._geometryEngine;
        let h8 = n18;
        if (this._tileKey && (h8 = l5(n18, Math.abs(this._width) + 1), !h8 || !h8.rings || 0 === h8.rings.length)) continue;
        if (r(e15) && (i10 = e15.buffer(f.WebMercator, h8, -this._width, 1)), this._width > 0) {
          const t14 = [];
          for (const i11 of n18.rings) i11 && t14.push(i11);
          if (i10) for (const e16 of i10.rings) e16 && t14.push(e16.reverse());
          if (t14.length) return { rings: t14 };
        }
      }
    }
    return null;
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectJog.js
var s10 = class _s {
  static local() {
    return null === _s.instance && (_s.instance = new _s()), _s.instance;
  }
  execute(t14, i10, s13, n18, h8) {
    return new e10(t14, i10, s13);
  }
};
s10.instance = null;
var e10 = class extends U2 {
  constructor(t14, s13, e15) {
    super(t14, false, true), this._curveHelper = new n8(), this._length = (void 0 !== s13.length ? s13.length : 20) * e15, this._angle = void 0 !== s13.angle ? s13.angle : 225, this._position = void 0 !== s13.position ? s13.position : 50, this._length < 0 && (this._length = -this._length), this._position < 20 && (this._position = 20), this._position > 80 && (this._position = 80), this._mirror = false;
  }
  processPath(t14) {
    if (this._curveHelper.isEmpty(t14, false)) return null;
    const i10 = t14[0], s13 = t14[t14.length - 1], e15 = [s13[0] - i10[0], s13[1] - i10[1]];
    this._curveHelper.normalize(e15);
    const n18 = [i10[0] + (s13[0] - i10[0]) * this._position / 100, i10[1] + (s13[1] - i10[1]) * this._position / 100], h8 = Math.cos((90 - this._angle) / 180 * Math.PI);
    let r18 = Math.sin((90 - this._angle) / 180 * Math.PI);
    this._mirror && (r18 = -r18), this._mirror = !this._mirror;
    return { paths: [[i10, [n18[0] - this._length / 2 * h8, n18[1] - this._length / 2 * r18], [n18[0] + this._length / 2 * h8, n18[1] + this._length / 2 * r18], s13]] };
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectMove.js
var n11 = class _n {
  static local() {
    return null === _n.instance && (_n.instance = new _n()), _n.instance;
  }
  execute(t14, s13, e15, i10, o14) {
    return new r9(t14, s13, e15);
  }
};
n11.instance = null;
var r9 = class {
  constructor(t14, s13, e15) {
    this._inputGeometries = t14, this._offsetX = void 0 !== s13.offsetX ? s13.offsetX * e15 : 0, this._offsetY = void 0 !== s13.offsetY ? -s13.offsetY * e15 : 0;
  }
  next() {
    let n18 = this._inputGeometries.next();
    for (; n18; ) {
      if (u2(n18)) return { xmin: n18.xmin + this._offsetX, xmax: n18.xmax + this._offsetX, ymin: n18.ymin + this._offsetY, ymax: n18.ymax + this._offsetY };
      if (y(n18)) {
        const s13 = p(n18);
        return this._moveMultipath(s13.rings, this._offsetX, this._offsetY), s13;
      }
      if (f2(n18)) {
        const s13 = p(n18);
        return this._moveMultipath(s13.paths, this._offsetX, this._offsetY), s13;
      }
      if (l(n18)) {
        const s13 = p(n18);
        return this._movePath(s13.points, this._offsetX, this._offsetY), s13;
      }
      if (s3(n18)) return { x: n18.x + this._offsetX, y: n18.y + this._offsetY };
      n18 = this._inputGeometries.next();
    }
    return null;
  }
  _moveMultipath(t14, s13, e15) {
    if (t14) for (const i10 of t14) this._movePath(i10, s13, e15);
  }
  _movePath(t14, s13, e15) {
    if (t14) for (const i10 of t14) i10[0] += s13, i10[1] += e15;
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectOffset.js
var a7 = class _a {
  static local() {
    return null === _a.instance && (_a.instance = new _a()), _a.instance;
  }
  execute(t14, e15, s13, i10, n18) {
    return new l9(t14, e15, s13, i10, n18);
  }
};
a7.instance = null;
var l9 = class {
  constructor(t14, e15, s13, i10, n18) {
    this._inputGeometries = t14, this._tileKey = i10, this._geometryEngine = n18, this._curveHelper = new n8(), this._offset = (e15.offset ?? 1) * s13, this._method = e15.method, this._option = e15.option, this._offsetFlattenError = e9 * s13;
  }
  next() {
    let r18;
    for (; r18 = this._inputGeometries.next(); ) {
      if (0 === this._offset) return r18;
      if (u2(r18)) {
        if (this._method === O.Rounded && this._offset > 0) {
          const t14 = [[r18.xmin, r18.ymin], [r18.xmin, r18.ymax], [r18.xmax, r18.ymax], [r18.xmax, r18.ymin], [r18.xmin, r18.ymin]], e15 = this._curveHelper.offset(t14, -this._offset, this._method, 4, this._offsetFlattenError);
          return e15 ? { rings: [e15] } : null;
        }
        if (Math.min(r18.xmax - r18.xmin, r18.ymax - r18.ymin) + 2 * this._offset > 0) return { xmin: r18.xmin - this._offset, xmax: r18.xmax + this._offset, ymin: r18.ymin - this._offset, ymax: r18.ymax + this._offset };
      }
      const f10 = this._geometryEngine;
      if (t(f10)) return null;
      let a16 = r18;
      if (y(r18)) {
        if (this._tileKey && (a16 = l5(r18, Math.abs(this._offset) + 1), !a16 || !a16.rings || 0 === a16.rings.length)) continue;
      } else if (f2(r18) && this._tileKey && (a16 = c4(r18, Math.abs(this._offset) + 1), !a16 || !a16.paths || 0 === a16.paths.length)) continue;
      return f10.offset(f.WebMercator, a16, -this._offset, 1, this._method, 4, this._offsetFlattenError);
    }
    return null;
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectReverse.js
var s11 = class _s {
  static local() {
    return null === _s.instance && (_s.instance = new _s()), _s.instance;
  }
  execute(e15, t14, r18, s13, i10) {
    return new n12(e15, t14, r18);
  }
};
s11.instance = null;
var n12 = class {
  constructor(e15, t14, r18) {
    this._inputGeometries = e15, this._reverse = void 0 === t14.reverse || t14.reverse;
  }
  next() {
    let s13 = this._inputGeometries.next();
    for (; s13; ) {
      if (!this._reverse) return s13;
      if (f2(s13)) {
        const t14 = p(s13);
        return g4(t14.paths), t14;
      }
      s13 = this._inputGeometries.next();
    }
    return null;
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectRotate.js
var u10 = class _u {
  static local() {
    return null === _u.instance && (_u.instance = new _u()), _u.instance;
  }
  execute(t14, n18, e15, r18, i10) {
    return new c8(t14, n18, e15);
  }
};
u10.instance = null;
var c8 = class {
  constructor(t14, n18, e15) {
    this._inputGeometries = t14, this._rotateAngle = void 0 !== n18.angle ? n18.angle * Math.PI / 180 : 0;
  }
  next() {
    let u13 = this._inputGeometries.next();
    for (; u13; ) {
      if (0 === this._rotateAngle) return u13;
      const c13 = u3();
      c2(c13, u13);
      const l16 = (c13[2] + c13[0]) / 2, m8 = (c13[3] + c13[1]) / 2;
      if (u2(u13)) {
        const t14 = { rings: [[[u13.xmin, u13.ymin], [u13.xmin, u13.ymax], [u13.xmax, u13.ymax], [u13.xmax, u13.ymin], [u13.xmin, u13.ymin]]] };
        return this._rotateMultipath(t14.rings, l16, m8), t14;
      }
      if (y(u13)) {
        const n18 = p(u13);
        return this._rotateMultipath(n18.rings, l16, m8), n18;
      }
      if (f2(u13)) {
        const n18 = p(u13);
        return this._rotateMultipath(n18.paths, l16, m8), n18;
      }
      if (l(u13)) {
        const n18 = p(u13);
        return this._rotatePath(n18.points, l16, m8), n18;
      }
      if (s3(u13)) return u13;
      u13 = this._inputGeometries.next();
    }
    return null;
  }
  _rotateMultipath(t14, n18, e15) {
    if (t14) for (const r18 of t14) this._rotatePath(r18, n18, e15);
  }
  _rotatePath(t14, n18, e15) {
    if (t14) {
      const r18 = Math.cos(this._rotateAngle), i10 = Math.sin(this._rotateAngle);
      for (const o14 of t14) {
        const t15 = o14[0] - n18, s13 = o14[1] - e15;
        o14[0] = n18 + t15 * r18 - s13 * i10, o14[1] = e15 + t15 * i10 + s13 * r18;
      }
    }
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectScale.js
var c9 = class _c {
  static local() {
    return null === _c.instance && (_c.instance = new _c()), _c.instance;
  }
  execute(t14, s13, i10, r18, n18) {
    return new l10(t14, s13, i10);
  }
};
c9.instance = null;
var l10 = class {
  constructor(t14, s13, i10) {
    this._inputGeometries = t14, this._xFactor = void 0 !== s13.xScaleFactor ? s13.xScaleFactor : 1.15, this._yFactor = void 0 !== s13.yScaleFactor ? s13.yScaleFactor : 1.15;
  }
  next() {
    let c13 = this._inputGeometries.next();
    for (; c13; ) {
      if (1 === this._xFactor && 1 === this._yFactor) return c13;
      const l16 = u3();
      c2(l16, c13);
      const u13 = (l16[2] + l16[0]) / 2, m8 = (l16[3] + l16[1]) / 2;
      if (u2(c13)) {
        const t14 = { rings: [[[c13.xmin, c13.ymin], [c13.xmin, c13.ymax], [c13.xmax, c13.ymax], [c13.xmax, c13.ymin], [c13.xmin, c13.ymin]]] };
        return this._scaleMultipath(t14.rings, u13, m8), t14;
      }
      if (y(c13)) {
        const s13 = p(c13);
        return this._scaleMultipath(s13.rings, u13, m8), s13;
      }
      if (f2(c13)) {
        const s13 = p(c13);
        return this._scaleMultipath(s13.paths, u13, m8), s13;
      }
      if (l(c13)) {
        const s13 = p(c13);
        return this._scalePath(s13.points, u13, m8), s13;
      }
      if (s3(c13)) return c13;
      c13 = this._inputGeometries.next();
    }
    return null;
  }
  _scaleMultipath(t14, s13, i10) {
    if (t14) for (const r18 of t14) this._scalePath(r18, s13, i10);
  }
  _scalePath(t14, s13, i10) {
    if (t14) for (const r18 of t14) {
      const t15 = (r18[0] - s13) * this._xFactor, n18 = (r18[1] - i10) * this._yFactor;
      r18[0] = s13 + t15, r18[1] = i10 + n18;
    }
  }
};

// node_modules/@arcgis/core/symbols/cim/effects/EffectWave.js
var a8 = class _a {
  static local() {
    return null === _a.instance && (_a.instance = new _a()), _a.instance;
  }
  execute(t14, e15, i10, s13, h8) {
    return new o9(t14, e15, i10);
  }
};
a8.instance = null;
var o9 = class {
  constructor(t14, e15, i10) {
    this._inputGeometries = t14, this._height = (void 0 !== e15.amplitude ? e15.amplitude : 2) * i10, this._period = (void 0 !== e15.period ? e15.period : 3) * i10, this._style = e15.waveform, this._height <= 0 && (this._height = Math.abs(this._height)), this._period <= 0 && (this._period = Math.abs(this._period)), this._pattern = new n9(), this._pattern.addValue(this._period), this._pattern.addValue(this._period), this._walker = new g5(), this._walker.updateTolerance(i10);
  }
  next() {
    let i10 = this._inputGeometries.next();
    for (; i10; ) {
      if (0 === this._height || 0 === this._period) return i10;
      if (f2(i10)) {
        const t14 = this._processGeom(i10.paths);
        if (t14.length) return { paths: t14 };
      }
      if (y(i10)) {
        const t14 = this._processGeom(i10.rings);
        if (t14.length) return { rings: t14 };
      }
      i10 = this._inputGeometries.next();
    }
    return null;
  }
  _processGeom(t14) {
    const e15 = [];
    for (const i10 of t14) if (this._walker.init(i10, this._pattern)) switch (this._style) {
      case A.Sinus:
      default:
        e15.push(this._constructCurve(i10, false));
        break;
      case A.Square:
        e15.push(this._constructSquare(i10));
        break;
      case A.Triangle:
        e15.push(this._constructTriangle(i10));
        break;
      case A.Random:
        e15.push(this._constructCurve(i10, true));
    }
    else e15.push(i10);
    return e15;
  }
  _constructCurve(t14, e15) {
    const s13 = new s8(), h8 = this._walker.calculatePathLength(t14);
    let n18 = Math.round(h8 / this._period);
    0 === n18 && (n18 = 1);
    const a16 = n18 * 16 + 1, o14 = h8 / n18, l16 = this._period / 16, _5 = 1 / a16, c13 = 2 * Math.PI * h8 / o14, p6 = 2 * Math.PI * Math.random(), u13 = 2 * Math.PI * Math.random(), d4 = 2 * Math.PI * Math.random(), g10 = 0.75 - Math.random() / 2, w6 = 0.75 - Math.random() / 2, f10 = new h4();
    this._walker.curPointAndAngle(f10), s13.startPath(f10.pt);
    let k5 = 0;
    for (; ; ) {
      if (!this._walker.nextPointAndAngle(l16, f10)) {
        s13.lineTo(t14[t14.length - 1]);
        break;
      }
      {
        const t15 = k5;
        let i10;
        if (k5 += _5, e15) {
          const e16 = this._height / 2 * (1 + 0.3 * Math.sin(g10 * c13 * t15 + p6));
          i10 = e16 * Math.sin(c13 * t15 + u13), i10 += e16 * Math.sin(w6 * c13 * t15 + d4), i10 /= 2;
        } else i10 = 0.5 * this._height * Math.sin(0.5 * c13 * t15);
        s13.lineTo([f10.pt[0] - i10 * f10.sa, f10.pt[1] + i10 * f10.ca]);
      }
    }
    return s13.path();
  }
  _constructSquare(t14) {
    const e15 = new s8(), s13 = this._walker.calculatePathLength(t14);
    Math.round(s13 / this._period);
    let h8 = true;
    for (; ; ) {
      let t15 = false;
      if (this._walker.curPositionIsValid()) {
        const i10 = new h4();
        this._walker.curPointAndAngle(i10);
        const s14 = new h4();
        if (this._walker.nextPointAndAngle(this._period, s14)) {
          const n18 = new h4();
          this._walker.nextPointAndAngle(this._period, n18) && (h8 ? (e15.startPath(i10.pt), h8 = false) : e15.lineTo(i10.pt), e15.lineTo([i10.pt[0] - this._height / 2 * i10.sa, i10.pt[1] + this._height / 2 * i10.ca]), e15.lineTo([s14.pt[0] - this._height / 2 * s14.sa, s14.pt[1] + this._height / 2 * s14.ca]), e15.lineTo([s14.pt[0] + this._height / 2 * s14.sa, s14.pt[1] - this._height / 2 * s14.ca]), e15.lineTo([n18.pt[0] + this._height / 2 * n18.sa, n18.pt[1] - this._height / 2 * n18.ca]), t15 = true);
        }
      }
      if (!t15) {
        e15.lineTo(this._walker.getPathEnd());
        break;
      }
    }
    return e15.path();
  }
  _constructTriangle(t14) {
    const e15 = new s8(), s13 = this._walker.calculatePathLength(t14);
    Math.round(s13 / this._period);
    let h8 = true;
    for (; ; ) {
      let t15 = false;
      if (this._walker.curPositionIsValid()) {
        const i10 = new h4();
        this._walker.curPointAndAngle(i10);
        const s14 = new h4();
        if (this._walker.nextPointAndAngle(this._period / 2, s14)) {
          const n18 = new h4();
          this._walker.nextPointAndAngle(this._period, n18) && (this._walker.nextPosition(this._period / 2) && (h8 ? (e15.startPath(i10.pt), h8 = false) : e15.lineTo(i10.pt), e15.lineTo([s14.pt[0] - this._height / 2 * s14.sa, s14.pt[1] + this._height / 2 * s14.ca]), e15.lineTo([n18.pt[0] + this._height / 2 * n18.sa, n18.pt[1] - this._height / 2 * n18.ca])), t15 = true);
        }
      }
      if (!t15) {
        e15.lineTo(this._walker.getPathEnd());
        break;
      }
    }
    return e15.path();
  }
};

// node_modules/@arcgis/core/symbols/cim/placements/PlacementAlongLineSameSize.js
var a9 = class _a {
  static local() {
    return null === _a.instance && (_a.instance = new _a()), _a.instance;
  }
  execute(t14, e15, s13, i10, n18) {
    return new r10(t14, e15, s13);
  }
};
a9.instance = null;
var r10 = class extends G3 {
  constructor(t14, e15, n18) {
    super(t14, true, true), this._geometryWalker = new g5(), this._geometryWalker.updateTolerance(n18), this._angleToLine = e15.angleToLine ?? true, this._offset = (e15.offset ? e15.offset : 0) * n18, this._originalEndings = e15.endings, this._offsetAtEnd = (e15.customEndingOffset ? e15.customEndingOffset : 0) * n18, this._position = -(e15.offsetAlongLine ? e15.offsetAlongLine : 0) * n18, this._pattern = new n9(), this._pattern.init(e15.placementTemplate, false), this._pattern.scale(n18), this._endings = this._originalEndings;
  }
  processPath(t14) {
    if (this._pattern.isEmpty()) return null;
    let s13;
    if (this.iteratePath) s13 = this._pattern.nextValue();
    else {
      this._originalEndings === X.WithFullGap && this.isClosed ? this._endings = X.WithMarkers : this._endings = this._originalEndings, this._pattern.extPtGap = 0;
      let i11, n18 = true;
      switch (this._endings) {
        case X.NoConstraint:
          i11 = -this._position, i11 = this._adjustPosition(i11), n18 = false;
          break;
        case X.WithHalfGap:
        default:
          i11 = -this._pattern.lastValue() / 2;
          break;
        case X.WithFullGap:
          i11 = -this._pattern.lastValue(), this._pattern.extPtGap = this._pattern.lastValue();
          break;
        case X.WithMarkers:
          i11 = 0;
          break;
        case X.Custom:
          i11 = -this._position, i11 = this._adjustPosition(i11), this._pattern.extPtGap = 0.5 * this._offsetAtEnd;
      }
      if (!this._geometryWalker.init(t14, this._pattern, n18)) return null;
      this._pattern.reset();
      let a16 = 0;
      for (; i11 > a16; ) i11 -= a16, a16 = this._pattern.nextValue();
      a16 -= i11, s13 = a16, this.iteratePath = true;
    }
    const i10 = new h4();
    return this._geometryWalker.nextPointAndAngle(s13, i10) ? this._endings === X.WithFullGap && this._geometryWalker.isPathEnd() ? (this.iteratePath = false, null) : this._endings === X.WithMarkers && this._geometryWalker.isPathEnd() && (this.iteratePath = false, this.isClosed) ? null : (this.internalPlacement.setTranslate(i10.pt[0] - this._offset * i10.sa, i10.pt[1] + this._offset * i10.ca), this._angleToLine && this.internalPlacement.setRotateCS(i10.ca, i10.sa), this.internalPlacement) : (this.iteratePath = false, null);
  }
  _adjustPosition(t14) {
    let e15 = t14 / this._pattern.length();
    return e15 -= Math.floor(e15), e15 * this._pattern.length();
  }
};

// node_modules/@arcgis/core/symbols/cim/placements/PlacementAtExtremities.js
var n13 = class _n {
  static local() {
    return null === _n.instance && (_n.instance = new _n()), _n.instance;
  }
  execute(t14, e15, s13, i10, n18) {
    return new r11(t14, e15, s13);
  }
};
n13.instance = null;
var r11 = class extends G3 {
  constructor(t14, e15, i10) {
    super(t14, false, true), this._curveHelper = new n8(), this._angleToLine = void 0 === e15.angleToLine || e15.angleToLine, this._offset = void 0 !== e15.offset ? e15.offset * i10 : 0, this._type = e15.extremityPlacement, this._position = void 0 !== e15.offsetAlongLine ? e15.offsetAlongLine * i10 : 0, this._beginProcessed = false;
  }
  processPath(t14) {
    let e15;
    switch (this._type) {
      case o4.Both:
      default:
        this._beginProcessed ? (e15 = this._atExtremities(t14, this._position, false), this._beginProcessed = false, this.iteratePath = false) : (e15 = this._atExtremities(t14, this._position, true), this._beginProcessed = true, this.iteratePath = true);
        break;
      case o4.JustBegin:
        e15 = this._atExtremities(t14, this._position, true);
        break;
      case o4.JustEnd:
        e15 = this._atExtremities(t14, this._position, false);
      case o4.None:
    }
    return e15;
  }
  _atExtremities(t14, s13, i10) {
    const n18 = t14.length;
    if (n18 < 2) return null;
    const r18 = i10 ? 1 : n18 - 2, o14 = i10 ? n18 : -1, a16 = i10 ? 1 : -1;
    let l16, h8 = 0, c13 = i10 ? t14[0] : t14[n18 - 1];
    for (let _5 = r18; _5 !== o14; _5 += a16) {
      l16 = c13, c13 = t14[_5];
      const i11 = this._curveHelper.calculateLength(l16, c13);
      if (h8 + i11 > s13) {
        const t15 = (s13 - h8) / i11, [n19, r19] = this._curveHelper.getAngleCS(l16, c13, t15), o15 = C3(l16, c13, t15);
        return this.internalPlacement.setTranslate(o15[0] - this._offset * r19, o15[1] + this._offset * n19), this._angleToLine && this.internalPlacement.setRotateCS(-n19, -r19), this.internalPlacement;
      }
      h8 += i11;
    }
    return null;
  }
};

// node_modules/@arcgis/core/symbols/cim/placements/PlacementAtRatioPositions.js
var a10 = class _a {
  static local() {
    return null === _a.instance && (_a.instance = new _a()), _a.instance;
  }
  execute(t14, i10, s13, e15, n18) {
    return new r12(t14, i10, s13);
  }
};
a10.instance = null;
var r12 = class extends G3 {
  constructor(t14, e15, n18) {
    super(t14, true, true), this._walker = new g5(), this._walker.updateTolerance(n18), this._angleToLine = void 0 === e15.angleToLine || e15.angleToLine, this._offset = void 0 !== e15.offset ? e15.offset * n18 : 0, this._beginGap = void 0 !== e15.beginPosition ? e15.beginPosition * n18 : 0, this._endGap = void 0 !== e15.endPosition ? e15.endPosition * n18 : 0, this._flipFirst = void 0 === e15.flipFirst || e15.flipFirst, this._pattern = new n9(), this._pattern.init(e15.positionArray, false, false), this._subPathLen = 0, this._posCount = this._pattern.size(), this._isFirst = true, this._prevPos = 0;
  }
  processPath(t14) {
    if (this._pattern.isEmpty()) return null;
    let i10;
    if (this.iteratePath) {
      const t15 = this._pattern.nextValue() * this._subPathLen, s14 = this._beginGap + t15;
      i10 = s14 - this._prevPos, this._prevPos = s14;
    } else {
      if (this._posCount = this._pattern.size(), this._isFirst = true, this._prevPos = 0, this._subPathLen = this._walker.calculatePathLength(t14) - this._beginGap - this._endGap, this._subPathLen < 0) return this.iteratePath = false, null;
      if (!this._walker.init(t14, this._pattern, false)) return null;
      this._pattern.reset();
      const s14 = this._pattern.nextValue() * this._subPathLen, e15 = this._beginGap + s14;
      i10 = e15 - this._prevPos, this._prevPos = e15, this.iteratePath = true;
    }
    const s13 = new h4();
    if (!this._walker.nextPointAndAngle(i10, s13, r7.END)) return this.iteratePath = false, null;
    this.internalPlacement.setTranslate(s13.pt[0] - this._offset * s13.sa, s13.pt[1] + this._offset * s13.ca);
    const a16 = this._isFirst && this._flipFirst;
    let r18, h8;
    return this._angleToLine ? (r18 = s13.ca, h8 = s13.sa) : (r18 = 1, h8 = 0), a16 && (r18 = -r18, h8 = -h8), this.internalPlacement.setRotateCS(r18, h8), this._isFirst = false, this._posCount--, 0 === this._posCount && (this.iteratePath = false), this.internalPlacement;
  }
};

// node_modules/@arcgis/core/symbols/cim/placements/PlacementInsidePolygon.js
var n14 = 512;
var h6 = 10;
var _2 = 12;
var r13 = 25;
var o10 = 24;
function a11(t14) {
  return void 0 !== t14.rings;
}
var l11 = class _l {
  static local() {
    return null === _l.instance && (_l.instance = new _l()), _l.instance;
  }
  execute(t14, s13, i10, e15, n18) {
    return new f6(t14, s13, i10, e15, n18);
  }
};
l11.instance = null;
var f6 = class _f {
  constructor(t14, h8, _5, r18, o14) {
    if (this._xMin = 0, this._xMax = 0, this._yMin = 0, this._yMax = 0, this._currentX = 0, this._currentY = 0, this._accelerationMap = null, this._testInsidePolygon = false, this._verticalSubdivision = true, this._stepX = Math.abs(h8.stepX ?? 16) * _5, this._stepY = Math.abs(h8.stepY ?? 16) * _5, 0 !== this._stepX && 0 !== this._stepY && t14 && a11(t14) && t14.rings) {
      if (this._gridType = h8.gridType ?? Y.Fixed, this._gridType === Y.Random) {
        const t15 = h8.seed ?? 13, i10 = 1;
        this._randomLCG = new t2(t15 * i10), this._randomness = (h8.randomness ?? 100) / 100, this._gridAngle = 0, this._shiftOddRows = false, this._cosAngle = 1, this._sinAngle = 0, this._offsetX = 0, this._offsetY = 0, this._buildRandomValues();
      } else {
        if (this._randomness = 0, this._gridAngle = h8.gridAngle ?? 0, this._shiftOddRows = h8.shiftOddRows ?? false, this._offsetX = (h8.offsetX ?? 0) * _5, this._offsetY = (h8.offsetY ?? 0) * _5, this._cosAngle = Math.cos(this._gridAngle / 180 * Math.PI), this._sinAngle = -Math.sin(this._gridAngle / 180 * Math.PI), this._stepX) if (this._offsetX < 0) for (; this._offsetX < -0.5 * this._stepX; ) this._offsetX += this._stepX;
        else for (; this._offsetX >= 0.5 * this._stepX; ) this._offsetX -= this._stepX;
        if (this._stepY) if (this._offsetY < 0) for (; this._offsetY < -0.5 * this._stepY; ) this._offsetY += this._stepY;
        else for (; this._offsetY >= 0.5 * this._stepY; ) this._offsetY -= this._stepY;
      }
      if (this._graphicOriginX = 0, this._graphicOriginY = 0, null != r18) {
        const [t15, s13, i10] = r18.split("/"), e15 = parseFloat(s13), h9 = parseFloat(i10);
        this._graphicOriginX = -h9 * n14, this._graphicOriginY = e15 * n14, this._testInsidePolygon = true;
      }
      this._internalPlacement = new t9(), this._calculateMinMax(t14), this._geometry = t14;
    }
  }
  next() {
    return this._geometry ? this._nextInside() : null;
  }
  _buildRandomValues() {
    if (!_f._randValues) {
      _f._randValues = [];
      for (let t14 = 0; t14 < o10; t14++) for (let s13 = 0; s13 < o10; s13++) _f._randValues.push(this._randomLCG.getFloat()), _f._randValues.push(this._randomLCG.getFloat());
    }
  }
  _calculateMinMax(t14) {
    let s13, i10, e15, h8, o14, a16, l16, f10, c13, g10, u13, M4, p6, d4;
    this._xMin = 0, this._xMax = 0, this._yMin = 0, this._yMax = 0, l16 = f10 = p6 = u13 = Number.MAX_VALUE, c13 = g10 = d4 = M4 = -Number.MAX_VALUE;
    const m8 = 1 !== this._cosAngle;
    let X2 = 0;
    for (const n18 of t14.rings) {
      const t15 = n18 ? n18.length : 0;
      for (let _5 = 0; _5 < t15; _5++) a16 = n18[_5][0], o14 = n18[_5][1], s13 = a16 - this._graphicOriginX - this._offsetX, i10 = o14 - this._graphicOriginY - this._offsetY, m8 ? (e15 = this._cosAngle * s13 - this._sinAngle * i10, h8 = this._sinAngle * s13 + this._cosAngle * i10) : (e15 = s13, h8 = i10), l16 = Math.min(l16, e15), c13 = Math.max(c13, e15), f10 = Math.min(f10, h8), g10 = Math.max(g10, h8), u13 = Math.min(u13, o14), M4 = Math.max(M4, o14), p6 = Math.min(p6, a16), d4 = Math.max(d4, a16), X2++;
    }
    u13 = u13 !== Number.MAX_VALUE ? u13 : -n14 - this._stepY, M4 = M4 !== -Number.MAX_VALUE ? M4 : this._stepY, p6 = p6 !== Number.MAX_VALUE ? p6 : -this._stepX, d4 = d4 !== -Number.MAX_VALUE ? d4 : n14 + this._stepX;
    const A5 = M4 - u13, Y5 = d4 - p6;
    if (this._verticalSubdivision = A5 >= Y5, this._polygonMin = this._verticalSubdivision ? u13 : p6, this._testInsidePolygon) {
      let t15 = 0 - this._graphicOriginX - this._offsetX - this._stepX, s14 = n14 - this._graphicOriginX - this._offsetX + this._stepX, i11 = -n14 - this._graphicOriginY - this._offsetY - this._stepY, e16 = 0 - this._graphicOriginY - this._offsetY + this._stepY;
      if (m8) {
        const n18 = [[t15, i11], [t15, e16], [s14, i11], [s14, e16]];
        t15 = i11 = Number.MAX_VALUE, s14 = e16 = -Number.MAX_VALUE;
        for (const h9 of n18) {
          const n19 = this._cosAngle * h9[0] - this._sinAngle * h9[1], _5 = this._sinAngle * h9[0] + this._cosAngle * h9[1];
          t15 = Math.min(t15, n19), s14 = Math.max(s14, n19), i11 = Math.min(i11, _5), e16 = Math.max(e16, _5);
        }
      }
      l16 = l16 !== Number.MAX_VALUE ? Math.max(l16, t15) : t15, f10 = f10 !== Number.MAX_VALUE ? Math.max(f10, i11) : i11, c13 = c13 !== -Number.MAX_VALUE ? Math.min(c13, s14) : s14, g10 = g10 !== -Number.MAX_VALUE ? Math.min(g10, e16) : e16;
    }
    this._xMin = Math.round(l16 / this._stepX), this._xMax = Math.round(c13 / this._stepX), this._yMin = Math.round(f10 / this._stepY), this._yMax = Math.round(g10 / this._stepY), this._currentX = this._xMax + 1, this._currentY = this._yMin - 1, this._testInsidePolygon && X2 > _2 && (A5 > r13 || Y5 > r13) && this._buildAccelerationMap(t14, p6, d4, u13, M4);
  }
  _buildAccelerationMap(t14, s13, i10, e15, _5) {
    const { rings: r18 } = t14, o14 = /* @__PURE__ */ new Map(), a16 = this._verticalSubdivision, l16 = a16 ? _5 - e15 : i10 - s13;
    let f10 = Math.ceil(l16 / h6);
    if (f10 <= 1) return;
    const c13 = Math.floor(l16 / f10);
    let u13, M4, p6, d4, m8, X2, A5, Y5, x5, y6;
    f10++, this._delta = c13, a16 ? (Y5 = -n14 - this._stepY, x5 = this._stepY, y6 = e15) : (Y5 = -this._stepX, x5 = n14 + this._stepX, y6 = s13);
    for (let n18 = 0; n18 < r18.length; n18++) if (u13 = r18[n18], !(u13.length < 2)) for (let t15 = 1; t15 < u13.length; t15++) {
      if (M4 = u13[t15 - 1], p6 = u13[t15], a16) {
        if (M4[1] === p6[1] || M4[1] < Y5 && p6[1] < Y5 || M4[1] > x5 && p6[1] > x5) continue;
        d4 = Math.min(M4[1], p6[1]), m8 = Math.max(M4[1], p6[1]);
      } else {
        if (M4[0] === p6[0] || M4[0] < Y5 && p6[0] < Y5 || M4[0] > x5 && p6[0] > x5) continue;
        d4 = Math.min(M4[0], p6[0]), m8 = Math.max(M4[0], p6[0]);
      }
      for (; d4 < m8; ) X2 = Math.floor((d4 - y6) / c13), g6(X2, n18, t15, o14), d4 += c13;
      A5 = Math.floor((m8 - y6) / c13), A5 > X2 && g6(A5, n18, t15, o14);
    }
    this._accelerationMap = o14;
  }
  _nextInside() {
    for (; ; ) {
      if (this._currentX > this._xMax) {
        if (this._currentY++, this._currentY > this._yMax) return null;
        this._currentX = this._xMin, this._shiftOddRows && this._currentY % 2 && this._currentX--;
      }
      let t14 = this._currentX * this._stepX + this._offsetX;
      this._shiftOddRows && this._currentY % 2 && (t14 += 0.5 * this._stepX);
      const s13 = this._currentY * this._stepY + this._offsetY;
      let i10, n18;
      if (this._currentX++, this._gridType === Y.Random) {
        const e15 = (this._currentX % o10 + o10) % o10, h8 = (this._currentY % o10 + o10) % o10;
        i10 = this._graphicOriginX + t14 + this._stepX * this._randomness * (0.5 - _f._randValues[h8 * o10 + e15]) * 2 / 3, n18 = this._graphicOriginY + s13 + this._stepY * this._randomness * (0.5 - _f._randValues[h8 * o10 + e15 + 1]) * 2 / 3;
      } else i10 = this._graphicOriginX + this._cosAngle * t14 + this._sinAngle * s13, n18 = this._graphicOriginY - this._sinAngle * t14 + this._cosAngle * s13;
      if (!this._testInsidePolygon || this._isInsidePolygon(i10, n18, this._geometry)) return this._internalPlacement.setTranslate(i10, n18), this._internalPlacement;
    }
  }
  _isInsidePolygon(s13, i10, e15) {
    const { rings: n18 } = e15;
    if (t(this._accelerationMap)) return c10(s13, i10, e15);
    const h8 = this._verticalSubdivision, _5 = h8 ? i10 : s13, r18 = Math.floor((_5 - this._polygonMin) / this._delta), o14 = this._accelerationMap.get(r18);
    if (!o14) return false;
    let a16, l16, f10, g10, u13, M4 = 0;
    for (const t14 of o14) {
      u13 = t14[0];
      const e16 = n18[u13];
      if (g10 = t14[1], a16 = e16[g10 - 1], l16 = e16[g10], h8) {
        if (a16[1] > i10 == l16[1] > i10) continue;
        f10 = (l16[0] - a16[0]) * (i10 - a16[1]) - (l16[1] - a16[1]) * (s13 - a16[0]);
      } else {
        if (a16[0] > s13 == l16[0] > s13) continue;
        f10 = (l16[1] - a16[1]) * (s13 - a16[0]) - (l16[0] - a16[0]) * (i10 - a16[1]);
      }
      f10 > 0 ? M4++ : M4--;
    }
    return 0 !== M4;
  }
};
function c10(t14, s13, i10) {
  const { rings: e15 } = i10;
  let n18, h8, _5, r18 = 0;
  for (const o14 of e15) {
    n18 = o14.length;
    for (let i11 = 1; i11 < n18; ++i11) {
      if (h8 = o14[i11 - 1], _5 = o14[i11], h8[1] > s13 == _5[1] > s13) continue;
      (_5[0] - h8[0]) * (s13 - h8[1]) - (_5[1] - h8[1]) * (t14 - h8[0]) > 0 ? r18++ : r18--;
    }
  }
  return 0 !== r18;
}
function g6(t14, s13, i10, e15) {
  let n18 = e15.get(t14);
  n18 || (n18 = [], e15.set(t14, n18)), n18.push([s13, i10]);
}

// node_modules/@arcgis/core/symbols/cim/placements/PlacementOnLine.js
var r14 = 1e-3;
var l12 = class _l {
  static local() {
    return null === _l.instance && (_l.instance = new _l()), _l.instance;
  }
  execute(e15, t14, n18, i10, s13) {
    return new o11(e15, t14, n18);
  }
};
l12.instance = null;
var o11 = class extends G3 {
  constructor(e15, t14, n18) {
    super(e15, true, true), this._curveHelper = new n8(), this._angleToLine = void 0 === t14.angleToLine || t14.angleToLine, this._offset = void 0 !== t14.offset ? t14.offset * n18 : 0, this._relativeTo = t14.relativeTo, this._position = void 0 !== t14.startPointOffset ? t14.startPointOffset * n18 : 0, this._epsilon = r14 * n18;
  }
  processPath(e15) {
    const i10 = this._position;
    if (this._relativeTo === Z.SegmentMidpoint) {
      for (this.iteratePath || (this._segmentCount = e15.length, this._curSegment = 1, this.iteratePath = true); this._curSegment < this._segmentCount; ) {
        const n18 = this._curSegment;
        this._curSegment++;
        const i11 = e15[n18 - 1], s13 = e15[n18], r19 = this._curveHelper.calculateLength(i11, s13);
        if (r19 < this._epsilon) continue;
        const l16 = 0.5 + this._position / r19, [o14, a16] = this._curveHelper.getAngleCS(i11, s13, l16), h8 = C3(i11, s13, l16);
        return this.internalPlacement.setTranslate(h8[0] - this._offset * a16, h8[1] + this._offset * o14), this._angleToLine && this.internalPlacement.setRotateCS(o14, a16), this.internalPlacement;
      }
      return this.iteratePath = false, null;
    }
    this._relativeTo === Z.LineEnd && d2(e15);
    const r18 = this.onLine(e15, i10);
    return this._relativeTo === Z.LineEnd && d2(e15), r18;
  }
  onLine(e15, n18) {
    let i10, r18 = false;
    switch (this._relativeTo) {
      case Z.LineMiddle:
      default:
        i10 = this._curveHelper.calculatePathLength(e15) / 2 + n18;
        break;
      case Z.LineBeginning:
        i10 = n18;
        break;
      case Z.LineEnd:
        i10 = n18, r18 = true;
    }
    const l16 = e15.length;
    let o14, a16 = 0, h8 = e15[0];
    for (let s13 = 1; s13 < l16; ++s13) {
      o14 = h8, h8 = e15[s13];
      const n19 = this._curveHelper.calculateLength(o14, h8);
      if (a16 + n19 > i10) {
        const e16 = (i10 - a16) / n19, [s14, l17] = this._curveHelper.getAngleCS(o14, h8, e16), c13 = C3(o14, h8, e16), u13 = r18 ? -this._offset : this._offset;
        return this.internalPlacement.setTranslate(c13[0] - u13 * l17, c13[1] + u13 * s14), this._angleToLine && (r18 ? this.internalPlacement.setRotateCS(-s14, -l17) : this.internalPlacement.setRotateCS(s14, l17)), this.internalPlacement;
      }
      a16 += n19;
    }
    return null;
  }
};

// node_modules/@arcgis/core/symbols/cim/placements/PlacementOnVertices.js
var n15 = class _n {
  static local() {
    return null === _n.instance && (_n.instance = new _n()), _n.instance;
  }
  execute(t14, s13, e15, i10, n18) {
    return new a12(t14, s13, e15);
  }
};
n15.instance = null;
var r15 = 1e-15;
var a12 = class extends G3 {
  constructor(t14, s13, e15) {
    super(t14, true, true), this._curveHelper = new n8(), this._angleToLine = void 0 === s13.angleToLine || s13.angleToLine, this._offset = void 0 !== s13.offset ? s13.offset * e15 : 0, this._endPoints = void 0 === s13.placeOnEndPoints || s13.placeOnEndPoints, this._controlPoints = void 0 === s13.placeOnControlPoints || s13.placeOnControlPoints, this._regularVertices = void 0 === s13.placeOnRegularVertices || s13.placeOnRegularVertices, this._tags = [], this._tagIterator = 0;
  }
  processPath(t14) {
    if (this.iteratePath || (this._preparePath(t14), this.iteratePath = true), this._tagIterator >= this._tags.length) return this._tags.length = 0, this._tagIterator = 0, this.iteratePath = false, null;
    const s13 = this._tags[this._tagIterator];
    this._angleToLine && this.internalPlacement.setRotate(s13[2]);
    let e15 = s13[0], i10 = s13[1];
    if (0 !== this._offset) {
      const t15 = Math.cos(s13[2]), n18 = Math.sin(s13[2]);
      e15 -= this._offset * n18, i10 += this._offset * t15;
    }
    return this.internalPlacement.setTranslate(e15, i10), this._tagIterator++, this.internalPlacement;
  }
  _preparePath(t14) {
    this._tags.length = 0, this._tagIterator = 0;
    const i10 = y3(t14), n18 = t14.length - 1;
    let r18, a16, h8 = 0, l16 = 0, _5 = 0, c13 = 0, g10 = 0;
    for (; h8 < n18; ) {
      h8++, r18 = t14[h8 - 1], a16 = t14[h8];
      const s13 = I2(r18), u13 = I2(a16);
      (this._angleToLine || 0 !== this._offset) && (c13 = this._curveHelper.getAngle(r18, a16, 0)), 1 === h8 ? i10 ? (l16 = c13, _5 = s13) : (this._endPoints || this._controlPoints && 1 === s13) && this._tags.push([r18[0], r18[1], c13]) : 1 === s13 ? this._controlPoints && this._tags.push([r18[0], r18[1], o12(g10, c13)]) : this._regularVertices && this._tags.push([r18[0], r18[1], o12(g10, c13)]), (this._angleToLine || 0 !== this._offset) && (g10 = this._curveHelper.getAngle(r18, a16, 1)), h8 === n18 && (i10 ? 1 === u13 || 1 === _5 ? this._controlPoints && this._tags.push([a16[0], a16[1], o12(g10, l16)]) : this._regularVertices && this._tags.push([a16[0], a16[1], o12(g10, l16)]) : (this._endPoints || this._controlPoints && 1 === u13) && this._tags.push([a16[0], a16[1], g10]));
    }
    this._tagIterator = 0;
  }
};
function o12(t14, s13) {
  const e15 = Math.PI;
  for (; Math.abs(s13 - t14) > e15 + 2 * r15; ) s13 - t14 > e15 ? s13 -= 2 * e15 : s13 += 2 * e15;
  return (t14 + s13) / 2;
}

// node_modules/@arcgis/core/core/PriorityQueue.js
var t10 = class {
  constructor(t14 = e11) {
    this._data = [], this._compare = t14;
  }
  get size() {
    return this._data.length;
  }
  enqueue(t14) {
    if (null == t14) return;
    const { _data: e15, _compare: n18 } = this;
    e15.push(t14);
    let l16 = e15.length - 1 >>> 0;
    const r18 = e15[l16];
    for (; l16 > 0; ) {
      const t15 = l16 - 1 >> 1, s13 = e15[t15];
      if (!(n18(s13, r18) <= 0)) break;
      e15[t15] = r18, e15[l16] = s13, l16 = t15;
    }
  }
  dequeue() {
    const { _data: t14, _compare: e15 } = this, n18 = t14[0], l16 = t14.pop();
    if (0 === t14.length) return n18;
    t14[0] = l16;
    let r18 = 0;
    const s13 = t14.length, u13 = t14[0];
    let a16, o14, c13 = null;
    for (; ; ) {
      const n19 = 2 * r18 + 1, l17 = 2 * r18 + 2;
      if (c13 = null, n19 < s13 && (a16 = t14[n19], e15(a16, u13) > 0 && (c13 = n19)), l17 < s13 && (o14 = t14[l17], (null === c13 && e15(o14, u13) <= 0 || null !== c13 && e15(o14, a16) <= 0) && (c13 = l17)), null === c13) break;
      t14[r18] = t14[c13], t14[c13] = u13, r18 = c13;
    }
    return n18;
  }
};
var e11 = (t14, e15) => t14 < e15 ? -1 : t14 > e15 ? 1 : 0;

// node_modules/@arcgis/core/geometry/support/labelPoint.js
var N = 100 * 222045e-21;
function u11(t14) {
  const { rings: n18 } = t14;
  if (!n18 || 0 === n18.length) return null;
  const s13 = c2(u3(), t14);
  if (!s13) return null;
  const c13 = 4 * (Math.abs(s13[0]) + Math.abs(s13[2]) + Math.abs(s13[1]) + Math.abs(s13[3]) + 1) * N;
  let l16 = 0, f10 = 0;
  for (let e15 = 0; e15 < n18.length; e15++) {
    const t15 = M(n18[e15]);
    t15 > f10 && (f10 = t15, l16 = e15);
  }
  if (Math.abs(f10) <= 2 * c13 * c13) {
    const t15 = g2(u3(), n18[l16]);
    return [(t15[0] + t15[2]) / 2, (t15[1] + t15[3]) / 2];
  }
  const u13 = u(n18[l16], false, u3());
  if (null === u13) return null;
  if (1 === n18.length && n18[0].length < 4) return u13;
  const d4 = [[NaN, NaN], [NaN, NaN], [NaN, NaN], [NaN, NaN]], x5 = [NaN, NaN, NaN, NaN], M4 = [NaN, NaN, NaN, NaN];
  let b5 = false, w6 = m6(u13, t14, true);
  0 === w6.distance && (b5 = true, d4[0][0] = u13[0], d4[0][1] = u13[1], w6 = m6(u13, t14, false)), x5[0] = w6.distance, M4[0] = 0;
  const y6 = [NaN, NaN];
  let C5 = false, k5 = 0.25, P5 = -1;
  const T4 = g2(u3(), n18[l16]);
  let z3 = NaN;
  do {
    if (z3 = NaN, d4[1] = g7(t14, p4(T4[0], T4[2], k5), c13, s13), isNaN(d4[1][0]) || isNaN(d4[1][1]) || (w6 = m6(d4[1], t14, false), z3 = w6.distance), !isNaN(z3) && z3 > c13 && h7(d4[1], t14)) C5 = true, x5[1] = z3, M4[1] = j3(d4[1], u13);
    else if (!isNaN(z3) && z3 > P5 && (P5 = z3, y6[0] = d4[1][0], y6[1] = d4[1][1]), k5 -= 0.01, k5 < 0.1) {
      if (!(P5 >= 0)) break;
      C5 = true, x5[1] = P5, d4[1][0] = y6[0], d4[1][1] = y6[1], M4[1] = j3(d4[1], u13);
    }
  } while (!C5);
  C5 = false, k5 = 0.5, P5 = -1;
  let D2 = 0.01, S3 = 1;
  do {
    if (z3 = NaN, d4[2] = g7(t14, p4(T4[0], T4[2], k5), c13, s13), isNaN(d4[2][0]) || isNaN(d4[2][1]) || (w6 = m6(d4[2], t14, false), z3 = w6.distance), !isNaN(z3) && z3 > c13 && h7(d4[2], t14)) C5 = true, x5[2] = z3, M4[2] = j3(d4[2], u13);
    else if (!isNaN(z3) && z3 > P5) P5 = z3, y6[0] = d4[2][0], y6[1] = d4[2][1];
    else if (z3 > P5 && (P5 = z3, y6[0] = d4[2][0], y6[1] = d4[2][1]), k5 = 0.5 + D2 * S3, D2 += 0.01, S3 *= -1, k5 < 0.3 || k5 > 0.7) {
      if (!(P5 >= 0)) break;
      C5 = true, x5[2] = P5, d4[2][0] = y6[0], d4[2][1] = y6[1], M4[2] = j3(d4[2], u13);
    }
  } while (!C5);
  C5 = false, k5 = 0.75, P5 = -1;
  do {
    if (z3 = NaN, d4[3] = g7(t14, p4(T4[0], T4[2], k5), c13, s13), isNaN(d4[3][0]) || isNaN(d4[3][1]) || (w6 = m6(d4[3], t14, false), z3 = w6.distance), !isNaN(z3) && z3 > c13 && h7(d4[3], t14)) C5 = true, x5[3] = z3, M4[3] = j3(d4[3], u13);
    else if (z3 > P5 && (P5 = z3, y6[0] = d4[3][0], y6[1] = d4[3][1]), k5 += 0.01, k5 > 0.9) {
      if (!(P5 >= 0)) break;
      C5 = true, x5[3] = P5, d4[3][0] = y6[0], d4[3][1] = y6[1], M4[3] = j3(d4[3], u13);
    }
  } while (!C5);
  const B4 = [0, 1, 2, 3], Q4 = b5 ? 0 : 1;
  let R3;
  for (let e15 = Q4; e15 < 4; e15++) for (let t15 = Q4; t15 < 3; t15++) {
    const n19 = M4[t15], e16 = M4[t15 + 1];
    q(n19, e16) > 0 && (R3 = B4[t15], B4[t15] = B4[t15 + 1], B4[t15 + 1] = R3, M4[t15] = e16, M4[t15 + 1] = n19);
  }
  let U5 = Q4, v2 = 0, A5 = 0;
  for (let e15 = Q4; e15 < 4; e15++) {
    switch (e15) {
      case 0:
        A5 = 2 * x5[B4[e15]];
        break;
      case 1:
        A5 = 1.66666666 * x5[B4[e15]];
        break;
      case 2:
        A5 = 1.33333333 * x5[B4[e15]];
        break;
      case 3:
        A5 = x5[B4[e15]];
    }
    A5 > v2 && (v2 = A5, U5 = B4[e15]);
  }
  return d4[U5];
}
function h7(t14, n18) {
  const { rings: e15 } = n18;
  let i10 = 0;
  for (const r18 of e15) {
    const n19 = r18.length;
    for (let e16 = 1; e16 < n19; ++e16) {
      const n20 = r18[e16 - 1], o14 = r18[e16];
      if (n20[1] > t14[1] == o14[1] > t14[1]) continue;
      (o14[0] - n20[0]) * (t14[1] - n20[1]) - (o14[1] - n20[1]) * (t14[0] - n20[0]) > 0 ? i10++ : i10--;
    }
  }
  return 0 !== i10;
}
function m6(t14, n18, e15) {
  if (e15 && h7(t14, n18)) return { coord: t14, distance: 0 };
  let i10 = 1 / 0, r18 = 0, o14 = 0;
  const s13 = [0, 0], { rings: a16 } = n18;
  for (const l16 of a16) if (!(l16.length < 2)) for (let n19 = 0; n19 < l16.length - 1; n19++) {
    o(s13, t14, l16, n19);
    const e16 = j3(t14, s13);
    e16 < i10 && (i10 = e16, r18 = s13[0], o14 = s13[1]);
  }
  return { coord: [r18, o14], distance: Math.sqrt(i10) };
}
function g7(t14, n18, i10, r18) {
  const o14 = [n18, 0];
  let s13 = 1 / 0, a16 = 1 / 0, c13 = false, l16 = false;
  const N3 = [[n18, r18[1] - 1], [n18, r18[3] + 1]], u13 = [0, 0], h8 = [0, 0], m8 = [0, 0], g10 = [[0, 0], [0, 0]], x5 = u3(), { rings: M4 } = t14;
  for (const e15 of M4) if (!(e15.length < 2)) for (let t15 = 1; t15 < e15.length; t15++) {
    if (g10[0][0] = e15[t15 - 1][0], g10[0][1] = e15[t15 - 1][1], g10[1][0] = e15[t15][0], g10[1][1] = e15[t15][1], null === d3(x5, g10)) continue;
    if (h8[0] = N3[0][0], h8[1] = N3[0][1], m8[0] = N3[1][0], m8[1] = N3[1][1], 0 === y4(x5, h8, m8)) continue;
    if (!G(N3[0], N3[1], g10[0], g10[1], u13)) continue;
    const n19 = u13[1];
    s13 > a16 ? n19 < s13 && (s13 = n19, c13 = true) : n19 < a16 && (a16 = n19, l16 = true);
  }
  return c13 && l16 ? o14[1] = (s13 + a16) / 2 : o14[0] = o14[1] = NaN, o14;
}
function d3(t14, n18) {
  if (n18.length < 2) return null;
  t14 || (t14 = u3());
  const [i10, r18] = n18[0], [o14, s13] = n18[1];
  return t14[0] = Math.min(i10, o14), t14[1] = Math.min(r18, s13), t14[2] = Math.max(i10, o14), t14[3] = Math.max(r18, s13), t14;
}
var x3 = 1;
var M2 = 4;
var b3 = 3;
var w4 = 12;
function y4(t14, n18, e15) {
  let i10 = C4(n18, t14), r18 = C4(e15, t14);
  const o14 = t14[0], s13 = t14[1], a16 = t14[2], c13 = t14[3];
  if (i10 & r18) return 0;
  if (!(i10 | r18)) return 4;
  const l16 = (i10 ? 1 : 0) | (r18 ? 2 : 0);
  do {
    const l17 = e15[0] - n18[0], f10 = e15[1] - n18[1];
    if (l17 > f10) i10 & b3 ? (i10 & x3 ? (n18[1] += f10 * (o14 - n18[0]) / l17, n18[0] = o14) : (n18[1] += f10 * (a16 - n18[0]) / l17, n18[0] = a16), i10 = C4(n18, t14)) : r18 & b3 ? (r18 & x3 ? (e15[1] += f10 * (o14 - e15[0]) / l17, e15[0] = o14) : (e15[1] += f10 * (a16 - e15[0]) / l17, e15[0] = a16), r18 = C4(e15, t14)) : i10 ? (i10 & M2 ? (n18[0] += l17 * (s13 - n18[1]) / f10, n18[1] = s13) : (n18[0] += l17 * (c13 - n18[1]) / f10, n18[1] = c13), i10 = C4(n18, t14)) : (r18 & M2 ? (e15[0] += l17 * (s13 - e15[1]) / f10, e15[1] = s13) : (e15[0] += l17 * (c13 - e15[1]) / f10, e15[1] = c13), r18 = C4(e15, t14));
    else if (i10 & w4 ? (i10 & M2 ? (n18[0] += l17 * (s13 - n18[1]) / f10, n18[1] = s13) : (n18[0] += l17 * (c13 - n18[1]) / f10, n18[1] = c13), i10 = C4(n18, t14)) : r18 & w4 ? (r18 & M2 ? (e15[0] += l17 * (s13 - e15[1]) / f10, e15[1] = s13) : (e15[0] += l17 * (c13 - e15[1]) / f10, e15[1] = c13), r18 = C4(e15, t14)) : i10 ? (i10 & x3 ? (n18[1] += f10 * (o14 - n18[0]) / l17, n18[0] = o14) : (n18[1] += f10 * (a16 - n18[0]) / l17, n18[0] = a16), i10 = C4(n18, t14)) : (r18 & x3 ? (e15[1] += f10 * (o14 - e15[0]) / l17, e15[0] = o14) : (e15[1] += f10 * (a16 - e15[0]) / l17, e15[0] = a16), r18 = C4(e15, t14)), i10 & r18) return 0;
  } while (i10 | r18);
  return l16;
}
function C4(t14, n18) {
  return (t14[0] < n18[0] ? 1 : 0) | (t14[0] > n18[2] ? 1 : 0) << 1 | (t14[1] < n18[1] ? 1 : 0) << 2 | (t14[1] > n18[3] ? 1 : 0) << 3;
}
function p4(t14, n18, e15) {
  return t14 + (n18 - t14) * e15;
}
function j3(t14, n18) {
  return (t14[0] - n18[0]) * (t14[0] - n18[0]) + (t14[1] - n18[1]) * (t14[1] - n18[1]);
}
function q(t14, n18) {
  if (t14 < n18) return -1;
  if (t14 > n18) return 1;
  if (t14 === n18) return 0;
  const e15 = isNaN(t14), i10 = isNaN(n18);
  return e15 < i10 ? -1 : e15 > i10 ? 1 : 0;
}
var k3 = class {
  constructor(t14, n18, e15, i10) {
    this.x = t14, this.y = n18, this.cellSize = e15, this.distancefromCellCenter = s2(t14, n18, i10), this.maxDistanceToPolygon = this.distancefromCellCenter + this.cellSize * Math.SQRT2;
  }
};
var P4 = 1;
var T = 100;
function z2(i10) {
  if (!i10 || !i10.rings || 0 === i10.rings.length) return null;
  const o14 = g2(u3(), i10.rings[0]);
  if (!o14) return null;
  const a16 = o14[2] - o14[0], c13 = o14[3] - o14[1];
  if (0 === a16 || 0 === c13) return [o14[0] + a16 / 2, o14[1] + c13 / 2];
  const l16 = Math.max(Math.min(a16, c13) / T, P4), f10 = new t10((t14, n18) => n18.maxDistanceToPolygon - t14.maxDistanceToPolygon), N3 = Math.min(a16, c13);
  let u13 = N3 / 2, h8 = 0, m8 = 0;
  for (h8 = o14[0]; h8 < o14[2]; h8 += N3) for (m8 = o14[1]; m8 < o14[3]; m8 += N3) f10.enqueue(new k3(h8 + u13, m8 + u13, u13, i10));
  const g10 = o2(i10.rings, false);
  if (null === g10) return null;
  let d4, x5 = new k3(g10[0], g10[1], 0, i10);
  for (; f10.size > 0; ) d4 = e(f10.dequeue()), d4.distancefromCellCenter > x5.distancefromCellCenter && (x5 = d4), d4.maxDistanceToPolygon - x5.distancefromCellCenter <= l16 || (u13 = d4.cellSize / 2, f10.enqueue(new k3(d4.x - u13, d4.y - u13, u13, i10)), f10.enqueue(new k3(d4.x + u13, d4.y - u13, u13, i10)), f10.enqueue(new k3(d4.x - u13, d4.y + u13, u13, i10)), f10.enqueue(new k3(d4.x + u13, d4.y + u13, u13, i10)));
  return [x5.x, x5.y];
}

// node_modules/@arcgis/core/symbols/cim/placements/PlacementPolygonCenter.js
function l13(t14) {
  return void 0 !== t14.rings;
}
var a13 = class _a {
  static local() {
    return null === _a.instance && (_a.instance = new _a()), _a.instance;
  }
  execute(t14, e15, s13, n18, o14) {
    return new f7(t14, e15, s13);
  }
};
a13.instance = null;
var f7 = class {
  constructor(t14, e15, s13) {
    this._geometry = t14, this._offsetX = void 0 !== e15.offsetX ? e15.offsetX * s13 : 0, this._offsetY = void 0 !== e15.offsetY ? e15.offsetY * s13 : 0, this._method = void 0 !== e15.method ? e15.method : K.OnPolygon, this._internalPlacement = new t9();
  }
  next() {
    const t14 = this._geometry;
    return this._geometry = null, t14 && l13(t14) ? this._polygonCenter(t14) : null;
  }
  _polygonCenter(r18) {
    let l16 = false;
    switch (this._method) {
      case K.CenterOfMass:
        {
          const t14 = g(r18);
          t14 && (this._internalPlacement.setTranslate(t14[0] + this._offsetX, t14[1] + this._offsetY), l16 = true);
        }
        break;
      case K.BoundingBoxCenter:
        {
          const s13 = u3();
          c2(s13, r18), s13 && (this._internalPlacement.setTranslate((s13[2] + s13[0]) / 2 + this._offsetX, (s13[3] + s13[1]) / 2 + this._offsetY), l16 = true);
        }
        break;
      case K.OnPolygon:
      default: {
        let t14;
        t14 = has("polylabel-placement-enabled") ? z2(r18) : u11(r18), null !== t14 && (this._internalPlacement.setTranslate(t14[0] + this._offsetX, t14[1] + this._offsetY), l16 = true);
      }
    }
    return l16 ? this._internalPlacement : null;
  }
};

// node_modules/@arcgis/core/symbols/cim/CIMOperators.js
function A3(p6) {
  if (!p6) return null;
  switch (p6.type) {
    case "CIMGeometricEffectAddControlPoints":
      return o8.local();
    case "CIMGeometricEffectArrow":
      return h3.local();
    case "CIMGeometricEffectBuffer":
      return l8.local();
    case "CIMGeometricEffectControlMeasureLine":
      return _.local();
    case "CIMGeometricEffectCut":
      return u9.local();
    case "CIMGeometricEffectDashes":
      return r8.local();
    case "CIMGeometricEffectDonut":
      return h5.local();
    case "CIMGeometricEffectJog":
      return s10.local();
    case "CIMGeometricEffectMove":
      return n11.local();
    case "CIMGeometricEffectOffset":
      return a7.local();
    case "CIMGeometricEffectReverse":
      return s11.local();
    case "CIMGeometricEffectRotate":
      return u10.local();
    case "CIMGeometricEffectScale":
      return c9.local();
    case "CIMGeometricEffectWave":
      return a8.local();
  }
  return null;
}
function g8(e15) {
  if (!e15) return null;
  switch (e15.type) {
    case "CIMMarkerPlacementAlongLineSameSize":
      return a9.local();
    case "CIMMarkerPlacementAtExtremities":
      return n13.local();
    case "CIMMarkerPlacementAtRatioPositions":
      return a10.local();
    case "CIMMarkerPlacementInsidePolygon":
      return l11.local();
    case "CIMMarkerPlacementOnLine":
      return l12.local();
    case "CIMMarkerPlacementOnVertices":
      return n15.local();
    case "CIMMarkerPlacementPolygonCenter":
      return a13.local();
  }
  return null;
}

// node_modules/@arcgis/core/symbols/cim/imageUtils.js
function t11(t14) {
  const e15 = t14.getFrame(0);
  if (e15 instanceof HTMLImageElement || e15 instanceof HTMLCanvasElement) return e15;
  const n18 = document.createElement("canvas");
  n18.width = t14.width, n18.height = t14.height;
  const a16 = n18.getContext("2d");
  return e15 instanceof ImageData ? a16.putImageData(e15, 0, 0) : a16.drawImage(e15, 0, 0), n18;
}

// node_modules/@arcgis/core/symbols/cim/Rect.js
var t12 = class {
  constructor(t14 = 0, h8 = 0, i10 = 0, s13 = 0) {
    this.x = t14, this.y = h8, this.width = i10, this.height = s13;
  }
  get isEmpty() {
    return this.width <= 0 || this.height <= 0;
  }
  union(t14) {
    this.x = Math.min(this.x, t14.x), this.y = Math.min(this.y, t14.y), this.width = Math.max(this.width, t14.width), this.height = Math.max(this.height, t14.height);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/collisions/BoundingBox.js
var i8 = class _i {
  constructor(i10, e15, s13, r18) {
    this.center = t5(i10, e15), this.centerT = n3(), this.halfWidth = s13 / 2, this.halfHeight = r18 / 2, this.width = s13, this.height = r18;
  }
  get x() {
    return this.center[0];
  }
  get y() {
    return this.center[1];
  }
  get blX() {
    return this.center[0] + this.halfWidth;
  }
  get blY() {
    return this.center[1] + this.halfHeight;
  }
  get trX() {
    return this.center[0] - this.halfWidth;
  }
  get trY() {
    return this.center[1] - this.halfHeight;
  }
  get xmin() {
    return this.x - this.halfWidth;
  }
  get xmax() {
    return this.x + this.halfWidth;
  }
  get ymin() {
    return this.y - this.halfHeight;
  }
  get ymax() {
    return this.y + this.halfHeight;
  }
  set x(t14) {
    this.center[0] = t14;
  }
  set y(t14) {
    this.center[1] = t14;
  }
  clone() {
    return new _i(this.x, this.y, this.width, this.height);
  }
  serialize(t14) {
    return t14.writeF32(this.center[0]), t14.writeF32(this.center[1]), t14.push(this.width), t14.push(this.height), t14;
  }
  findCollisionDelta(t14, h8 = 4) {
    const i10 = Math.abs(t14.centerT[0] - this.centerT[0]), e15 = Math.abs(t14.centerT[1] - this.centerT[1]), s13 = (t14.halfWidth + this.halfWidth + h8) / i10, r18 = (t14.halfHeight + this.halfHeight + h8) / e15, n18 = Math.min(s13, r18);
    return Math.log2(n18);
  }
  extend(t14) {
    const h8 = Math.min(this.xmin, t14.xmin), i10 = Math.min(this.ymin, t14.ymin), e15 = Math.max(this.xmax, t14.xmax) - h8, s13 = Math.max(this.ymax, t14.ymax) - i10, r18 = h8 + e15 / 2, n18 = i10 + s13 / 2;
    this.width = e15, this.height = s13, this.halfWidth = e15 / 2, this.halfHeight = s13 / 2, this.x = r18, this.y = n18;
  }
  static deserialize(t14) {
    const h8 = t14.readF32(), e15 = t14.readF32(), s13 = t14.readInt32(), r18 = t14.readInt32();
    return new _i(h8, e15, s13, r18);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/mesh/templates/shapingUtils.js
var l14 = 26;
var g9 = 4;
var _3 = l14 + g9;
var p5 = l14 - 6;
var x4 = 3;
var w5 = 8;
var y5 = Math.PI / 180;
var M3 = 8;
var b4 = 1.5;
var B2 = class {
  constructor(t14, s13, e15, i10) {
    this._rotationT = n2(), this._xBounds = 0, this._yBounds = 0, this.minZoom = 0, this.maxZoom = 255, this._bounds = null;
    const n18 = e15.rect, h8 = new Float32Array(8);
    t14 *= i10, s13 *= i10;
    const r18 = e15.code ? n18.width * i10 : e15.metrics.width, a16 = e15.code ? n18.height * i10 : e15.metrics.height;
    this.width = r18, this.height = a16, h8[0] = t14, h8[1] = s13, h8[2] = t14 + r18, h8[3] = s13, h8[4] = t14, h8[5] = s13 + a16, h8[6] = t14 + r18, h8[7] = s13 + a16, this._data = h8, this._setTextureCoords(n18), this._scale = i10, this._mosaic = e15, this.x = t14, this.y = s13, this.maxOffset = Math.max(t14 + r18, s13 + a16);
  }
  get mosaic() {
    return this._mosaic;
  }
  set angle(t14) {
    this._angle = t14, h(this._rotationT, -t14), this._setOffsets(this._data);
  }
  get angle() {
    return this._angle;
  }
  get xTopLeft() {
    return this._data[0];
  }
  get yTopLeft() {
    return this._data[1];
  }
  get xBottomRight() {
    return this._data[6];
  }
  get yBottomRight() {
    return this._data[7];
  }
  get texcoords() {
    return this._texcoords;
  }
  get textureBinding() {
    return this._mosaic.textureBinding;
  }
  get offsets() {
    return this._offsets || this._setOffsets(this._data), this._offsets;
  }
  get char() {
    return String.fromCharCode(this._mosaic.code);
  }
  get code() {
    return this._mosaic.code;
  }
  get bounds() {
    if (!this._bounds) {
      const { height: t14, width: s13 } = this._mosaic.metrics, i10 = s13 * this._scale, n18 = Math.abs(t14) * this._scale, r18 = new Float32Array(8);
      r18[0] = this.x, r18[1] = this.y, r18[2] = this.x + i10, r18[3] = this.y, r18[4] = this.x, r18[5] = this.y + n18, r18[6] = this.x + i10, r18[7] = this.y + n18;
      const a16 = o5(n2(), this._rotationT, this._transform);
      a2(r18, r18, a16);
      let c13 = 1 / 0, d4 = 1 / 0, f10 = 0, m8 = 0;
      for (let e15 = 0; e15 < 4; e15++) {
        const t15 = r18[2 * e15], s14 = r18[2 * e15 + 1];
        c13 = Math.min(c13, t15), d4 = Math.min(d4, s14), f10 = Math.max(f10, t15), m8 = Math.max(m8, s14);
      }
      const l16 = f10 - c13, g10 = m8 - d4, _5 = c13 + l16 / 2, p6 = d4 + g10 / 2;
      this._bounds = new i8(_5, p6, l16, g10);
    }
    return this._bounds;
  }
  setTransform(t14) {
    this._transform = t14, this._offsets = null;
  }
  _setOffsets(t14) {
    this._offsets || (this._offsets = { upperLeft: 0, upperRight: 0, lowerLeft: 0, lowerRight: 0 });
    const s13 = this._offsets, i10 = new Float32Array(8), n18 = o5(n2(), this._rotationT, this._transform);
    a2(i10, t14, n18), s13.upperLeft = w2(i10[0] * w5, i10[1] * w5), s13.upperRight = w2(i10[2] * w5, i10[3] * w5), s13.lowerLeft = w2(i10[4] * w5, i10[5] * w5), s13.lowerRight = w2(i10[6] * w5, i10[7] * w5);
  }
  _setTextureCoords({ x: t14, y: s13, width: e15, height: i10 }) {
    this._texcoords = { upperLeft: w2(t14, s13), upperRight: w2(t14 + e15, s13), lowerLeft: w2(t14, s13 + i10), lowerRight: w2(t14 + e15, s13 + i10) };
  }
};
var L2 = (t14, s13) => ({ code: 0, page: 0, sdf: true, rect: new t6(0, 0, 11, 8), textureBinding: s13, metrics: { advance: 0, height: 4, width: t14, left: 0, top: 0 } });
function R2(t14, s13) {
  return t14.forEach((t15) => z(t15, t15, s13)), { upperLeft: w2(w5 * t14[0][0], w5 * t14[0][1]), upperRight: w2(w5 * t14[1][0], w5 * t14[1][1]), lowerLeft: w2(w5 * t14[2][0], w5 * t14[2][1]), lowerRight: w2(w5 * t14[3][0], w5 * t14[3][1]) };
}
var T2 = class {
  constructor(t14, s13, e15) {
    this._rotation = 0, this._decorate(t14, s13, e15), this.glyphs = t14, this.bounds = this._createBounds(t14), this.isMultiline = s13.length > 1, this._hasRotation = 0 !== e15.angle, this._transform = this._createGlyphTransform(this.bounds, e15), this._borderLineSize = e15.borderLineSize, (e15.borderLineSize || e15.hasBackground) && ([this.bounds, this.background] = this.shapeBackground(this._transform));
    for (const i10 of t14) i10.setTransform(this._transform);
  }
  setRotation(t14) {
    if (0 === t14 && 0 === this._rotation) return;
    this._rotation = t14;
    const i10 = this._transform, n18 = h(n2(), t14);
    o5(i10, n18, i10);
    for (const s13 of this.glyphs) s13.setTransform(this._transform);
  }
  _decorate(t14, s13, e15) {
    if (!e15.decoration || "none" === e15.decoration || !t14.length) return;
    const i10 = e15.scale, n18 = "underline" === e15.decoration ? _3 : p5, o14 = t14[0].textureBinding;
    for (const h8 of s13) {
      const s14 = h8.startX * i10, e16 = h8.startY * i10, r18 = (h8.width + h8.glyphWidthEnd) * i10;
      t14.push(new B2(s14, e16 + n18 * i10, L2(r18, o14), 1));
    }
  }
  shapeBackground(s13) {
    const e15 = u4(this._borderLineSize || 0), i10 = (b4 + e15) / 2, n18 = this._borderLineSize ? i10 : 0, { xmin: o14, ymin: h8, xmax: r18, ymax: a16, x: c13, y: d4, width: f10, height: m8 } = this.bounds, l16 = [o14 - M3, h8 - M3], g10 = [r18 + M3, h8 - M3], _5 = [o14 - M3, a16 + M3], p6 = [r18 + M3, a16 + M3], x5 = R2([[l16[0] - i10, l16[1] - i10], [g10[0] + i10, g10[1] - i10], [l16[0] + n18, l16[1] + n18], [g10[0] - n18, g10[1] + n18]], s13), w6 = R2([[_5[0] + n18, _5[1] - n18], [p6[0] - n18, p6[1] - n18], [_5[0] - i10, _5[1] + i10], [p6[0] + i10, p6[1] + i10]], s13), y6 = R2([[l16[0] - i10, l16[1] - i10], [l16[0] + n18, l16[1] + n18], [_5[0] - i10, _5[1] + i10], [_5[0] + n18, _5[1] - n18]], s13), B4 = R2([[g10[0] - n18, g10[1] + n18], [g10[0] + i10, g10[1] - i10], [p6[0] - n18, p6[1] - n18], [p6[0] + i10, p6[1] + i10]], s13), L3 = { main: R2([l16, g10, _5, p6], s13), top: x5, bot: w6, left: y6, right: B4 };
    return [new i8(c13, d4, f10 + 2 * i10, m8 + 2 * i10), L3];
  }
  get boundsT() {
    const t14 = this.bounds, s13 = r3(n3(), t14.x, t14.y);
    if (z(s13, s13, this._transform), this._hasRotation) {
      const e15 = Math.max(t14.width, t14.height);
      return new i8(s13[0], s13[1], e15, e15);
    }
    return new i8(s13[0], s13[1], t14.width, t14.height);
  }
  _createBounds(t14) {
    let s13 = 1 / 0, e15 = 1 / 0, i10 = 0, n18 = 0;
    for (const r18 of t14) s13 = Math.min(s13, r18.xTopLeft), e15 = Math.min(e15, r18.yTopLeft), i10 = Math.max(i10, r18.xBottomRight), n18 = Math.max(n18, r18.yBottomRight);
    const o14 = i10 - s13, h8 = n18 - e15;
    return new i8(s13 + o14 / 2, e15 + h8 / 2, o14, h8);
  }
  _createGlyphTransform(t14, s13) {
    const e15 = y5 * s13.angle, h8 = n2(), a16 = n3();
    return i2(h8, h8, r3(a16, s13.xOffset, -s13.yOffset)), s13.isCIM ? e5(h8, h8, e15) : (i2(h8, h8, r3(a16, t14.x, t14.y)), e5(h8, h8, e15), i2(h8, h8, r3(a16, -t14.x, -t14.y))), h8;
  }
};
var v = class {
  constructor(t14, s13, e15, i10, n18, o14) {
    this.glyphWidthEnd = 0, this.startX = 0, this.startY = 0, this.start = Math.max(0, Math.min(s13, e15)), this.end = Math.max(0, Math.max(s13, e15)), this.end < t14.length && (this.glyphWidthEnd = t14[this.end].metrics.width), this.width = i10, this.yMin = n18, this.yMax = o14;
  }
};
var j4 = (t14) => 10 === t14;
var k4 = (t14) => 32 === t14;
function A4(t14, s13, e15) {
  const i10 = new Array(), n18 = 1 / e15.scale, o14 = e15.maxLineWidth * n18, h8 = s13 ? t14.length - 1 : 0, r18 = s13 ? -1 : t14.length, a16 = s13 ? -1 : 1;
  let c13 = h8, d4 = 0, f10 = 0, m8 = c13, u13 = m8, l16 = 0, g10 = 1 / 0, _5 = 0;
  for (; c13 !== r18; ) {
    const { code: s14, metrics: e16 } = t14[c13], n19 = Math.abs(e16.top);
    if (j4(s14) || k4(s14) || (g10 = Math.min(g10, n19), _5 = Math.max(_5, n19 + e16.height)), j4(s14)) c13 !== h8 && (i10.push(new v(t14, m8, c13 - a16, d4, g10, _5)), g10 = 1 / 0, _5 = 0), d4 = 0, m8 = c13 + a16, u13 = c13 + a16, f10 = 0;
    else if (k4(s14)) u13 = c13 + a16, f10 = 0, l16 = e16.advance, d4 += e16.advance;
    else if (d4 > o14) {
      if (u13 !== m8) {
        const s15 = u13 - 2 * a16;
        d4 -= l16, i10.push(new v(t14, m8, s15, d4 - f10, g10, _5)), g10 = 1 / 0, _5 = 0, m8 = u13, d4 = f10;
      } else i10.push(new v(t14, m8, c13 - a16, d4, g10, _5)), g10 = 1 / 0, _5 = 0, m8 = c13, u13 = c13, d4 = 0;
      d4 += e16.advance, f10 += e16.advance;
    } else d4 += e16.advance, f10 += e16.advance;
    c13 += a16;
  }
  const p6 = new v(t14, m8, c13 - a16, d4, g10, _5);
  return p6.start >= 0 && p6.end < t14.length && i10.push(p6), i10;
}
function O2(t14, s13) {
  let e15 = 0;
  for (let o14 = 0; o14 < t14.length; o14++) {
    const { width: s14 } = t14[o14];
    e15 = Math.max(s14, e15);
  }
  const i10 = "underline" === s13.decoration ? g9 : 0, n18 = t14[0].yMin;
  return { x: 0, y: n18, height: t14[t14.length - 1].yMax + s13.lineHeight * (t14.length - 1) + i10 - n18, width: e15 };
}
function S2(t14, s13, e15) {
  const i10 = e15.scale, n18 = new Array(), o14 = A4(t14, s13, e15), h8 = O2(o14, e15), { vAlign: r18, hAlign: a16 } = e15, c13 = r18 === t3.Baseline ? 1 : 0, f10 = c13 ? 0 : r18 - 1, m8 = (1 - c13) * -h8.y + f10 * (h8.height / 2) + (c13 ? 1 : 0) * -l14;
  for (let d4 = 0; d4 < o14.length; d4++) {
    const { start: s14, end: h9, width: r19 } = o14[d4];
    let c14 = -1 * (a16 + 1) * (r19 / 2) - x4;
    const f11 = d4 * e15.lineHeight + m8 - x4;
    o14[d4].startX = c14, o14[d4].startY = f11;
    for (let e16 = s14; e16 <= h9; e16++) {
      const s15 = t14[e16];
      if (j4(s15.code)) continue;
      const o15 = new B2(c14 + s15.metrics.left, f11 - s15.metrics.top, s15, i10);
      c14 += s15.metrics.advance, n18.push(o15);
    }
  }
  return new T2(n18, o14, e15);
}

// node_modules/@arcgis/core/symbols/cim/CIMSymbolDrawHelper.js
var E = Math.PI / 180;
var J = 10;
var N2 = s.getLogger("esri.symbols.cim.CIMSymbolDrawHelper");
var O3 = class _O {
  constructor(t14) {
    this._t = t14;
  }
  static createIdentity() {
    return new _O([1, 0, 0, 0, 1, 0]);
  }
  clone() {
    const t14 = this._t;
    return new _O(t14.slice());
  }
  transform(t14) {
    const e15 = this._t;
    return [e15[0] * t14[0] + e15[1] * t14[1] + e15[2], e15[3] * t14[0] + e15[4] * t14[1] + e15[5]];
  }
  static createScale(t14, e15) {
    return new _O([t14, 0, 0, 0, e15, 0]);
  }
  scale(t14, e15) {
    const r18 = this._t;
    return r18[0] *= t14, r18[1] *= t14, r18[2] *= t14, r18[3] *= e15, r18[4] *= e15, r18[5] *= e15, this;
  }
  scaleRatio() {
    return Math.sqrt(this._t[0] * this._t[0] + this._t[1] * this._t[1]);
  }
  static createTranslate(t14, e15) {
    return new _O([0, 0, t14, 0, 0, e15]);
  }
  translate(t14, e15) {
    const r18 = this._t;
    return r18[2] += t14, r18[5] += e15, this;
  }
  static createRotate(t14) {
    const e15 = Math.cos(t14), r18 = Math.sin(t14);
    return new _O([e15, -r18, 0, r18, e15, 0]);
  }
  rotate(t14) {
    return _O.multiply(this, _O.createRotate(t14), this);
  }
  angle() {
    const t14 = this._t[0], e15 = this._t[3], r18 = Math.sqrt(t14 * t14 + e15 * e15);
    return [t14 / r18, e15 / r18];
  }
  static multiply(t14, e15, r18) {
    const i10 = t14._t, s13 = e15._t, o14 = i10[0] * s13[0] + i10[3] * s13[1], n18 = i10[1] * s13[0] + i10[4] * s13[1], a16 = i10[2] * s13[0] + i10[5] * s13[1] + s13[2], l16 = i10[0] * s13[3] + i10[3] * s13[4], h8 = i10[1] * s13[3] + i10[4] * s13[4], c13 = i10[2] * s13[3] + i10[5] * s13[4] + s13[5], m8 = r18._t;
    return m8[0] = o14, m8[1] = n18, m8[2] = a16, m8[3] = l16, m8[4] = h8, m8[5] = c13, r18;
  }
  invert() {
    const t14 = this._t;
    let e15 = t14[0] * t14[4] - t14[1] * t14[3];
    if (0 === e15) return new _O([0, 0, 0, 0, 0, 0]);
    e15 = 1 / e15;
    const r18 = (t14[1] * t14[5] - t14[2] * t14[4]) * e15, i10 = (t14[2] * t14[3] - t14[0] * t14[5]) * e15, s13 = t14[4] * e15, o14 = -t14[1] * e15, n18 = -t14[3] * e15, a16 = t14[0] * e15;
    return new _O([s13, o14, r18, n18, a16, i10]);
  }
};
var Y2 = class {
  constructor(t14, e15) {
    this._resourceManager = t14, this._transfos = [], this._sizeTransfos = [], this._geomUnitsPerPoint = 1, this._placementPool = new e2(t9, void 0, void 0, 100), this._earlyReturn = false, this._mapRotation = 0, this._transfos.push(e15 || O3.createIdentity()), this._sizeTransfos.push(e15 ? e15.scaleRatio() : 1);
  }
  setTransform(t14, e15) {
    this._transfos = [t14 || O3.createIdentity()], this._sizeTransfos = [e15 || (t14 ? t14.scaleRatio() : 1)];
  }
  setGeomUnitsPerPoint(t14) {
    this._geomUnitsPerPoint = t14;
  }
  transformPt(t14) {
    return this._transfos[this._transfos.length - 1].transform(t14);
  }
  transformSize(t14) {
    return t14 * this._sizeTransfos[this._sizeTransfos.length - 1];
  }
  reverseTransformPt(t14) {
    return this._transfos[this._transfos.length - 1].invert().transform(t14);
  }
  reverseTransformSize(t14) {
    return t14 / this._sizeTransfos[this._sizeTransfos.length - 1];
  }
  getTransformAngle() {
    return this._transfos[this._transfos.length - 1].angle();
  }
  geomUnitsPerPoint() {
    return this.isEmbedded() ? 1 : this._geomUnitsPerPoint;
  }
  isEmbedded() {
    return this._transfos.length > 1;
  }
  back() {
    return this._transfos[this._transfos.length - 1];
  }
  push(t14, e15) {
    const r18 = e15 ? t14.scaleRatio() : 1;
    O3.multiply(t14, this.back(), t14), this._transfos.push(t14), this._sizeTransfos.push(this._sizeTransfos[this._sizeTransfos.length - 1] * r18);
  }
  pop() {
    this._transfos.splice(-1, 1), this._sizeTransfos.splice(-1, 1);
  }
  drawSymbol(t14, e15, r18) {
    if (t14) switch (t14.type) {
      case "CIMPointSymbol":
      case "CIMLineSymbol":
      case "CIMPolygonSymbol":
        this.drawMultiLayerSymbol(t14, e15);
        break;
      case "CIMTextSymbol":
        this.drawTextSymbol(t14, e15, r18);
    }
  }
  drawMultiLayerSymbol(t14, e15) {
    if (!t14 || !e15) return;
    const r18 = t14.symbolLayers;
    if (!r18) return;
    const i10 = t14.effects;
    if (i10 && i10.length > 0) {
      const t15 = this.executeEffects(i10, e15);
      if (t15) {
        let e16 = t15.next();
        for (; e16; ) this.drawSymbolLayers(r18, e16), e16 = t15.next();
      }
    } else this.drawSymbolLayers(r18, e15);
  }
  executeEffects(t14, e15) {
    const r18 = this._resourceManager.geometryEngine;
    let i10 = new s7(e15);
    for (const s13 of t14) {
      const t15 = A3(s13);
      t15 && (i10 = t15.execute(i10, s13, this.geomUnitsPerPoint(), null, r18));
    }
    return i10;
  }
  drawSymbolLayers(t14, e15) {
    let r18 = t14.length;
    for (; r18--; ) {
      const i10 = t14[r18];
      if (!i10 || false === i10.enable) continue;
      const s13 = i10.effects;
      if (s13 && s13.length > 0) {
        const t15 = this.executeEffects(s13, e15);
        if (t15) {
          let e16 = null;
          for (; (e16 = t15.next()) && (this.drawSymbolLayer(i10, e16), !this._earlyReturn); ) ;
        }
      } else this.drawSymbolLayer(i10, e15);
      if (this._earlyReturn) return;
    }
  }
  drawSymbolLayer(t14, e15) {
    switch (t14.type) {
      case "CIMSolidFill":
        this.drawSolidFill(e15, t14.color);
        break;
      case "CIMHatchFill":
        this.drawHatchFill(e15, t14);
        break;
      case "CIMPictureFill":
        this.drawPictureFill(e15, t14);
        break;
      case "CIMGradientFill":
        this.drawGradientFill(e15, t14);
        break;
      case "CIMSolidStroke":
        this.drawSolidStroke(e15, t14.color, t14.width, t14.capStyle, t14.joinStyle, t14.miterLimit);
        break;
      case "CIMPictureStroke":
        this.drawPictureStroke(e15, t14);
        break;
      case "CIMGradientStroke":
        this.drawGradientStroke(e15, t14);
        break;
      case "CIMCharacterMarker":
      case "CIMPictureMarker":
      case "CIMVectorMarker":
        this.drawMarkerLayer(t14, e15);
    }
  }
  drawHatchFill(t14, e15) {
    const r18 = this._buildHatchPolyline(e15, t14, this.geomUnitsPerPoint());
    r18 && (this.pushClipPath(t14), this.drawMultiLayerSymbol(e15.lineSymbol, r18), this.popClipPath());
  }
  drawPictureFill(t14, e15) {
  }
  drawGradientFill(t14, e15) {
  }
  drawPictureStroke(t14, e15) {
  }
  drawGradientStroke(t14, e15) {
  }
  drawMarkerLayer(t14, e15) {
    const r18 = t14.markerPlacement;
    if (r18) {
      const i10 = g8(r18);
      if (i10) {
        const s13 = "CIMMarkerPlacementInsidePolygon" === r18.type || "CIMMarkerPlacementPolygonCenter" === r18.type && r18.clipAtBoundary;
        s13 && this.pushClipPath(e15);
        const o14 = i10.execute(e15, r18, this.geomUnitsPerPoint(), null, this._resourceManager.geometryEngine);
        if (o14) {
          let e16 = null;
          for (; (e16 = o14.next()) && (this.drawMarker(t14, e16), !this._earlyReturn); ) ;
        }
        s13 && this.popClipPath();
      }
    } else {
      const r19 = this._placementPool.acquire();
      if (s3(e15)) r19.tx = e15.x, r19.ty = e15.y, this.drawMarker(t14, r19);
      else if (y(e15)) {
        const i10 = r2(e15);
        i10 && ([r19.tx, r19.ty] = i10, this.drawMarker(t14, r19));
      } else for (const i10 of e15.points) if (r19.tx = i10[0], r19.ty = i10[1], this.drawMarker(t14, r19), this._earlyReturn) break;
      this._placementPool.release(r19);
    }
  }
  drawMarker(t14, e15) {
    switch (t14.type) {
      case "CIMCharacterMarker":
      case "CIMPictureMarker":
        this.drawPictureMarker(t14, e15);
        break;
      case "CIMVectorMarker":
        this.drawVectorMarker(t14, e15);
    }
  }
  drawPictureMarker(t14, e15) {
    if (!t14) return;
    const r18 = this._resourceManager.getResource(t14.url), i10 = t14.size ?? 10;
    if (t(r18) || i10 <= 0) return;
    const o14 = r18.width, n18 = r18.height;
    if (!o14 || !n18) return;
    const a16 = o14 / n18, l16 = t14.scaleX ?? 1, h8 = O3.createIdentity(), c13 = t14.anchorPoint;
    if (c13) {
      let e16 = c13.x, r19 = c13.y;
      "Absolute" !== t14.anchorPointUnits && (e16 *= i10 * a16 * l16, r19 *= i10), h8.translate(-e16, -r19);
    }
    let m8 = t14.rotation ?? 0;
    t14.rotateClockwise && (m8 = -m8), this._mapRotation && (m8 += this._mapRotation), m8 && h8.rotate(m8 * E);
    let f10 = t14.offsetX ?? 0, u13 = t14.offsetY ?? 0;
    if (f10 || u13) {
      if (this._mapRotation) {
        const t15 = E * this._mapRotation, e16 = Math.cos(t15), r19 = Math.sin(t15), i11 = f10 * r19 + u13 * e16;
        f10 = f10 * e16 - u13 * r19, u13 = i11;
      }
      h8.translate(f10, u13);
    }
    const d4 = this.geomUnitsPerPoint();
    1 !== d4 && h8.scale(d4, d4);
    const p6 = e15.getAngle();
    p6 && h8.rotate(p6), h8.translate(e15.tx, e15.ty), this.push(h8, false), this.drawImage(t14, i10), this.pop();
  }
  drawVectorMarker(t14, e15) {
    if (!t14) return;
    const r18 = t14.markerGraphics;
    if (!r18) return;
    const i10 = t14.size ?? 10, s13 = t14.frame, o14 = s13 ? s13.ymax - s13.ymin : 0, n18 = i10 && o14 ? i10 / o14 : 1, a16 = O3.createIdentity();
    s13 && a16.translate(0.5 * -(s13.xmax + s13.xmin), 0.5 * -(s13.ymax + s13.ymin));
    const l16 = t14.anchorPoint;
    if (l16) {
      let e16 = l16.x, r19 = l16.y;
      "Absolute" !== t14.anchorPointUnits ? s13 && (e16 *= s13.xmax - s13.xmin, r19 *= s13.ymax - s13.ymin) : (e16 /= n18, r19 /= n18), a16.translate(-e16, -r19);
    }
    1 !== n18 && a16.scale(n18, n18);
    let h8 = t14.rotation ?? 0;
    t14.rotateClockwise && (h8 = -h8), this._mapRotation && (h8 += this._mapRotation), h8 && a16.rotate(h8 * E);
    let c13 = t14.offsetX ?? 0, m8 = t14.offsetY ?? 0;
    if (c13 || m8) {
      if (this._mapRotation) {
        const t15 = E * this._mapRotation, e16 = Math.cos(t15), r19 = Math.sin(t15), i11 = c13 * r19 + m8 * e16;
        c13 = c13 * e16 - m8 * r19, m8 = i11;
      }
      a16.translate(c13, m8);
    }
    const f10 = this.geomUnitsPerPoint();
    1 !== f10 && a16.scale(f10, f10);
    const u13 = e15.getAngle();
    u13 && a16.rotate(u13), a16.translate(e15.tx, e15.ty), this.push(a16, t14.scaleSymbolsProportionally);
    for (const d4 of r18) if (d4 && d4.symbol && d4.geometry || N2.error("Invalid marker graphic", d4), this.drawSymbol(d4.symbol, d4.geometry, d4.textString), this._earlyReturn) break;
    this.pop();
  }
  drawTextSymbol(t14, e15, r18) {
    if (!t14) return;
    if (!s3(e15)) return;
    if ((t14.height ?? 10) <= 0) return;
    const i10 = O3.createIdentity();
    let s13 = t14.angle ?? 0;
    s13 = -s13, s13 && i10.rotate(s13 * E);
    const o14 = t14.offsetX ?? 0, n18 = t14.offsetY ?? 0;
    (o14 || n18) && i10.translate(o14, n18);
    const a16 = this.geomUnitsPerPoint();
    1 !== a16 && i10.scale(a16, a16), i10.translate(e15.x, e15.y), this.push(i10, false), this.drawText(t14, r18), this.pop();
  }
  _buildHatchPolyline(t14, e15, r18) {
    let i10 = (void 0 !== t14.separation ? t14.separation : 4) * r18, s13 = void 0 !== t14.rotation ? t14.rotation : 0;
    if (0 === i10) return null;
    i10 < 0 && (i10 = -i10);
    let o14 = 0;
    const n18 = 0.5 * i10;
    for (; o14 > n18; ) o14 -= i10;
    for (; o14 < -n18; ) o14 += i10;
    const a16 = u3();
    c2(a16, e15), a16[0] -= n18, a16[1] -= n18, a16[2] += n18, a16[3] += n18;
    const l16 = [[a16[0], a16[1]], [a16[0], a16[3]], [a16[2], a16[3]], [a16[2], a16[1]]];
    for (; s13 > 180; ) s13 -= 180;
    for (; s13 < 0; ) s13 += 180;
    const c13 = Math.cos(s13 * E), f10 = Math.sin(s13 * E), u13 = -i10 * f10, d4 = i10 * c13;
    let p6, _5, g10, y6;
    o14 = (void 0 !== t14.offsetX ? t14.offsetX * r18 : 0) * f10 - (void 0 !== t14.offsetY ? t14.offsetY * r18 : 0) * c13, p6 = g10 = Number.MAX_VALUE, _5 = y6 = -Number.MAX_VALUE;
    for (const h8 of l16) {
      const t15 = h8[0], e16 = h8[1], r19 = c13 * t15 + f10 * e16, i11 = -f10 * t15 + c13 * e16;
      p6 = Math.min(p6, r19), g10 = Math.min(g10, i11), _5 = Math.max(_5, r19), y6 = Math.max(y6, i11);
    }
    g10 = Math.floor(g10 / i10) * i10;
    let P5 = c13 * p6 - f10 * g10 - u13 * o14 / i10, w6 = f10 * p6 + c13 * g10 - d4 * o14 / i10, x5 = c13 * _5 - f10 * g10 - u13 * o14 / i10, S3 = f10 * _5 + c13 * g10 - d4 * o14 / i10;
    const M4 = 1 + Math.round((y6 - g10) / i10), b5 = [];
    for (let h8 = 0; h8 < M4; h8++) P5 += u13, w6 += d4, x5 += u13, S3 += d4, b5.push([[P5, w6], [x5, S3]]);
    return { paths: b5 };
  }
};
var q2 = class extends Y2 {
  constructor(t14, e15) {
    super(t14, e15), this.reset();
  }
  reset() {
    this._xmin = this._ymin = 1 / 0, this._xmax = this._ymax = -1 / 0, this._clipCount = 0;
  }
  envelope() {
    return new t12(this._xmin, this._ymin, this._xmax - this._xmin, this._ymax - this._ymin);
  }
  bounds() {
    return o3(this._xmin, this._ymin, this._xmax, this._ymax);
  }
  drawSolidFill(t14) {
    if (t14 && !(this._clipCount > 0)) if (y(t14)) this._processPath(t14.rings, 0);
    else if (f2(t14)) this._processPath(t14.paths, 0);
    else if (u2(t14)) {
      const e15 = Q(t14);
      e15 && this._processPath(e15.rings, 0);
    } else console.error("drawSolidFill Unexpected geometry type!");
  }
  drawSolidStroke(t14, e15, r18) {
    if (!t14 || this._clipCount > 0) return;
    const i10 = 0.5 * this.transformSize(r18 ?? 0);
    if (y(t14)) this._processPath(t14.rings, i10);
    else if (f2(t14)) this._processPath(t14.paths, i10);
    else if (u2(t14)) {
      const e16 = Q(t14);
      e16 && this._processPath(e16.rings, i10);
    } else console.error("drawSolidStroke unexpected geometry type!");
  }
  drawMarkerLayer(t14, e15) {
    y(e15) && t14.markerPlacement && ("CIMMarkerPlacementInsidePolygon" === t14.markerPlacement.type || "CIMMarkerPlacementPolygonCenter" === t14.markerPlacement.type && t14.markerPlacement.clipAtBoundary) ? this._processPath(e15.rings, 0) : super.drawMarkerLayer(t14, e15);
  }
  drawHatchFill(t14, e15) {
    this.drawSolidFill(t14);
  }
  drawPictureFill(t14, e15) {
    this.drawSolidFill(t14);
  }
  drawGradientFill(t14, e15) {
    this.drawSolidFill(t14);
  }
  drawPictureStroke(t14, e15) {
    this.drawSolidStroke(t14, null, e15.width);
  }
  drawGradientStroke(t14, e15) {
    this.drawSolidStroke(t14, null, e15.width);
  }
  pushClipPath(t14) {
    this.drawSolidFill(t14), this._clipCount++;
  }
  popClipPath() {
    this._clipCount--;
  }
  drawImage(t14, e15) {
    const { url: r18 } = t14, i10 = t14.scaleX ?? 1;
    let s13 = i10 * e15, n18 = e15;
    const a16 = this._resourceManager.getResource(r18);
    !e15 && r(a16) && (s13 = i10 * a16.width, n18 = a16.height), this._merge(this.transformPt([-s13 / 2, -n18 / 2]), 0), this._merge(this.transformPt([-s13 / 2, n18 / 2]), 0), this._merge(this.transformPt([s13 / 2, -n18 / 2]), 0), this._merge(this.transformPt([s13 / 2, n18 / 2]), 0);
  }
  drawText(t14, e15) {
    if (!e15 || 0 === e15.length) return;
    this._textRasterizer || (this._textRasterizer = new s6());
    const r18 = et(t14), [i10, s13] = this._textRasterizer.computeTextSize(e15, r18);
    let o14 = 0;
    switch (t14.horizontalAlignment) {
      case "Left":
        o14 = i10 / 2;
        break;
      case "Right":
        o14 = -i10 / 2;
    }
    let n18 = 0;
    switch (t14.verticalAlignment) {
      case "Bottom":
        n18 = s13 / 2;
        break;
      case "Top":
        n18 = -s13 / 2;
        break;
      case "Baseline":
        n18 = s13 / 6;
    }
    this._merge(this.transformPt([-i10 / 2 + o14, -s13 / 2 + n18]), 0), this._merge(this.transformPt([-i10 / 2 + o14, s13 / 2 + n18]), 0), this._merge(this.transformPt([i10 / 2 + o14, -s13 / 2 + n18]), 0), this._merge(this.transformPt([i10 / 2 + o14, s13 / 2 + n18]), 0);
  }
  _processPath(t14, e15) {
    if (t14) for (const r18 of t14) {
      const t15 = r18 ? r18.length : 0;
      if (t15 > 1) {
        this._merge(this.transformPt(r18[0]), e15);
        for (let i10 = 1; i10 < t15; i10++) this._merge(this.transformPt(r18[i10]), e15);
      }
    }
  }
  _merge(t14, e15) {
    t14[0] - e15 < this._xmin && (this._xmin = t14[0] - e15), t14[0] + e15 > this._xmax && (this._xmax = t14[0] + e15), t14[1] - e15 < this._ymin && (this._ymin = t14[1] - e15), t14[1] + e15 > this._ymax && (this._ymax = t14[1] + e15);
  }
};
var V = class extends Y2 {
  constructor() {
    super(...arguments), this._searchPoint = [0, 0], this._searchDistPoint = 0, this._textInfo = null;
  }
  hitTest(t14, e15, r18, i10, s13, o14) {
    const n18 = o14 * u4(1);
    this.setTransform(), this.setGeomUnitsPerPoint(n18), this._searchPoint = [(t14[0] + t14[2]) / 2, (t14[1] + t14[3]) / 2], this._searchDistPoint = (t14[2] - t14[0]) / 2 / n18, this._textInfo = i10;
    const a16 = e15 && ("CIMPointSymbol" === e15.type && "Map" !== e15.angleAlignment || "CIMTextSymbol" === e15.type);
    return this._mapRotation = a16 ? s13 : 0, this._earlyReturn = false, this.drawSymbol(e15, r18), this._earlyReturn;
  }
  drawSolidFill(t14, e15) {
    this._hitTestFill(t14);
  }
  drawHatchFill(t14, e15) {
    this._hitTestFill(t14);
  }
  drawPictureFill(t14, e15) {
    this._hitTestFill(t14);
  }
  drawGradientFill(t14, e15) {
    this._hitTestFill(t14);
  }
  drawSolidStroke(t14, e15, r18, i10, s13, o14) {
    this._hitTestStroke(t14, r18);
  }
  drawPictureStroke(t14, e15) {
    this._hitTestStroke(t14, e15.width);
  }
  drawGradientStroke(t14, e15) {
    this._hitTestStroke(t14, e15.width);
  }
  drawMarkerLayer(t14, e15) {
    t14.markerPlacement && ("CIMMarkerPlacementInsidePolygon" === t14.markerPlacement.type || "CIMMarkerPlacementPolygonCenter" === t14.markerPlacement.type && t14.markerPlacement.clipAtBoundary) ? this._hitTestFill(e15) : super.drawMarkerLayer(t14, e15);
  }
  pushClipPath(t14) {
  }
  popClipPath() {
  }
  drawImage(t14, e15) {
    const { url: r18 } = t14, i10 = t14.scaleX ?? 1, o14 = this._resourceManager.getResource(r18);
    if (t(o14) || 0 === o14.height || 0 === e15) return;
    const n18 = e15 * this.geomUnitsPerPoint(), a16 = n18 * i10 * (o14.width / o14.height), l16 = this.reverseTransformPt(this._searchPoint), h8 = this._searchDistPoint;
    Math.abs(l16[0]) < a16 / 2 + h8 && Math.abs(l16[1]) < n18 / 2 + h8 && (this._earlyReturn = true);
  }
  drawText(e15, r18) {
    var _a, _b;
    const i10 = this._textInfo;
    if (!i10) return;
    const s13 = i10.get(e15);
    if (!s13) return;
    const { text: o14, mosaicItem: n18 } = s13;
    if (!((_a = n18 == null ? void 0 : n18.glyphMosaicItems) == null ? void 0 : _a.length)) return;
    const a16 = e15.height ?? J, { lineGapType: l16, lineGap: h8 } = e15, c13 = l16 ? tt(l16, h8 ?? 0, a16) : 0, m8 = i5(o14)[1], f10 = n18.glyphMosaicItems, u13 = "CIMBackgroundCallout" === ((_b = e15.callout) == null ? void 0 : _b.type), d4 = S2(f10, m8, { scale: a16 / j, angle: 0, xOffset: 0, yOffset: 0, hAlign: Z2(e15.horizontalAlignment), vAlign: $(e15.verticalAlignment), maxLineWidth: 512, lineHeight: f3 * Math.max(0.25, Math.min(c13 || 1, 4)), decoration: e15.font.decoration || "none", isCIM: true, hasBackground: u13 }), p6 = this.reverseTransformPt(this._searchPoint), _5 = p6[0], g10 = p6[1];
    for (const t14 of d4.glyphs) if (_5 > t14.xTopLeft && _5 < t14.xBottomRight && g10 > -t14.yBottomRight && g10 < -t14.yTopLeft) {
      this._earlyReturn = true;
      break;
    }
  }
  _hitTestFill(t14) {
    let e15 = null;
    if (u2(t14)) {
      const r19 = t14;
      e15 = [[[r19.xmin, r19.ymin], [r19.xmin, r19.ymax], [r19.xmax, r19.ymax], [r19.xmax, r19.ymin], [r19.xmin, r19.ymin]]];
    } else if (y(t14)) e15 = t14.rings;
    else {
      if (!f2(t14)) return;
      e15 = t14.paths;
    }
    const r18 = this.reverseTransformPt(this._searchPoint);
    if (this._pointInPolygon(r18, e15) && (this._earlyReturn = true), !this._earlyReturn) {
      const t15 = this.reverseTransformSize(this._searchDistPoint) * this.geomUnitsPerPoint();
      this._nearLine(r18, e15, t15) && (this._earlyReturn = true);
    }
  }
  _hitTestStroke(t14, e15) {
    let r18 = null;
    if (u2(t14)) {
      const e16 = t14;
      r18 = [[[e16.xmin, e16.ymin], [e16.xmin, e16.ymax], [e16.xmax, e16.ymax], [e16.xmax, e16.ymin], [e16.xmin, e16.ymin]]];
    } else if (y(t14)) r18 = t14.rings;
    else {
      if (!f2(t14)) return;
      r18 = t14.paths;
    }
    const i10 = this.reverseTransformPt(this._searchPoint), s13 = (e15 ?? 0) * this.geomUnitsPerPoint(), o14 = this.reverseTransformSize(this._searchDistPoint) * this.geomUnitsPerPoint();
    this._nearLine(i10, r18, s13 / 2 + o14) && (this._earlyReturn = true);
  }
  _pointInPolygon(t14, e15) {
    let r18 = 0;
    for (const i10 of e15) {
      const e16 = i10.length;
      for (let s13 = 1; s13 < e16; s13++) {
        const e17 = i10[s13 - 1], o14 = i10[s13];
        if (e17[1] > t14[1] == o14[1] > t14[1]) continue;
        (o14[0] - e17[0]) * (t14[1] - e17[1]) - (o14[1] - e17[1]) * (t14[0] - e17[0]) > 0 ? r18++ : r18--;
      }
    }
    return 0 !== r18;
  }
  _nearLine(t14, e15, r18) {
    for (const i10 of e15) {
      const e16 = i10.length;
      for (let s13 = 1; s13 < e16; s13++) {
        const e17 = i10[s13 - 1], o14 = i10[s13];
        let n18 = (o14[0] - e17[0]) * (o14[0] - e17[0]) + (o14[1] - e17[1]) * (o14[1] - e17[1]);
        if (0 === n18) continue;
        n18 = Math.sqrt(n18);
        const a16 = ((o14[0] - e17[0]) * (t14[1] - e17[1]) - (o14[1] - e17[1]) * (t14[0] - e17[0])) / n18;
        if (Math.abs(a16) < r18) {
          const i11 = ((o14[0] - e17[0]) * (t14[0] - e17[0]) + (o14[1] - e17[1]) * (t14[1] - e17[1])) / n18;
          if (i11 > -r18 && i11 < n18 + r18) return true;
        }
      }
    }
    return false;
  }
};
var W = class extends Y2 {
  constructor(t14, e15, r18, i10) {
    super(e15, r18), this._applyAdditionalRenderProps = i10, this._colorSubstitutionHelper = new t8(), this._ctx = t14;
  }
  drawSolidFill(t14, e15) {
    if (!t14) return;
    if (y(t14)) this._buildPath(t14.rings, true);
    else if (f2(t14)) this._buildPath(t14.paths, true);
    else if (u2(t14)) this._buildPath(Q(t14).rings, true);
    else {
      if (!l(t14)) return;
      console.log("CanvasDrawHelper.drawSolidFill - No implementation!");
    }
    const r18 = this._ctx;
    r18.fillStyle = "string" == typeof e15 ? e15 : "rgba(" + Math.round(e15[0]) + "," + Math.round(e15[1]) + "," + Math.round(e15[2]) + "," + (e15[3] ?? 255) / 255 + ")", r18.fill("evenodd");
  }
  drawSolidStroke(t14, e15, r18, i10, s13, o14) {
    if (!t14 || !e15 || 0 === r18) return;
    if (y(t14)) this._buildPath(t14.rings, true);
    else if (f2(t14)) this._buildPath(t14.paths, false);
    else {
      if (!u2(t14)) return void console.log("CanvasDrawHelper.drawSolidStroke isn't implemented!");
      this._buildPath(Q(t14).rings, true);
    }
    const n18 = this._ctx;
    n18.strokeStyle = "string" == typeof e15 ? e15 : "rgba(" + Math.round(e15[0]) + "," + Math.round(e15[1]) + "," + Math.round(e15[2]) + "," + (e15[3] ?? 255) / 255 + ")", n18.lineWidth = Math.max(this.transformSize(r18), 0.5), this._setCapStyle(i10), this._setJoinStyle(s13), n18.miterLimit = o14, n18.stroke();
  }
  pushClipPath(t14) {
    if (this._ctx.save(), y(t14)) this._buildPath(t14.rings, true);
    else if (f2(t14)) this._buildPath(t14.paths, true);
    else {
      if (!u2(t14)) return;
      this._buildPath(Q(t14).rings, true);
    }
    this._ctx.clip("evenodd");
  }
  popClipPath() {
    this._ctx.restore();
  }
  drawImage(t14, e15) {
    const { colorSubstitutions: r18, url: i10, tintColor: o14 } = t14, n18 = t14.scaleX ?? 1, a16 = this._resourceManager.getResource(i10);
    if (t(a16)) return;
    let l16 = e15 * (a16.width / a16.height), h8 = e15;
    e15 || (l16 = a16.width, h8 = a16.height);
    const c13 = A2(i10) || "src" in a16 && A2(a16.src);
    let m8 = "getFrame" in a16 ? t11(a16) : a16;
    r18 && (m8 = this._colorSubstitutionHelper.applyColorSubstituition(m8, r18)), this._applyAdditionalRenderProps && !c13 && o14 && (m8 = this._colorSubstitutionHelper.tintImageData(m8, o14));
    const f10 = this.transformPt([0, 0]), [u13, d4] = this.getTransformAngle(), p6 = this.transformSize(1), _5 = this._ctx;
    _5.save(), _5.setTransform({ m11: n18 * p6 * u13, m12: n18 * p6 * d4, m21: -p6 * d4, m22: p6 * u13, m41: f10[0], m42: f10[1] }), _5.drawImage(m8, -l16 / 2, -h8 / 2, l16, h8), _5.restore();
  }
  drawText(t14, e15) {
    if (!e15 || 0 === e15.length) return;
    this._textRasterizer || (this._textRasterizer = new s6());
    const r18 = et(t14);
    r18.size *= this.transformSize(e3(1));
    const i10 = this._textRasterizer.rasterizeText(e15, r18);
    if (!i10) return;
    const { size: s13, anchorX: o14, anchorY: n18, canvas: l16 } = i10, h8 = s13[0] * (o14 + 0.5), c13 = s13[1] * (n18 - 0.5), m8 = this._ctx, f10 = this.transformPt([0, 0]), [u13, d4] = this.getTransformAngle(), p6 = 1;
    m8.save(), m8.setTransform({ m11: p6 * u13, m12: p6 * d4, m21: -p6 * d4, m22: p6 * u13, m41: f10[0] - p6 * h8, m42: f10[1] + p6 * c13 }), m8.drawImage(l16, 0, 0), m8.restore();
  }
  drawPictureFill(t14, e15) {
    if (!t14) return;
    let { colorSubstitutions: r18, height: i10, offsetX: o14, offsetY: n18, rotation: a16, scaleX: l16, tintColor: h8, url: c13 } = e15;
    const m8 = this._resourceManager.getResource(c13);
    if (t(m8)) return;
    if (y(t14)) this._buildPath(t14.rings, true);
    else if (f2(t14)) this._buildPath(t14.paths, true);
    else if (u2(t14)) this._buildPath(Q(t14).rings, true);
    else {
      if (!l(t14)) return;
      console.log("CanvasDrawHelper.drawPictureFill - No implementation!");
    }
    const f10 = this._ctx, u13 = A2(c13) || "src" in m8 && A2(m8.src);
    let y6, P5 = "getFrame" in m8 ? t11(m8) : m8;
    if (r18 && (P5 = this._colorSubstitutionHelper.applyColorSubstituition(P5, r18)), this._applyAdditionalRenderProps) {
      u13 || h8 && (P5 = this._colorSubstitutionHelper.tintImageData(P5, h8)), y6 = f10.createPattern(P5, "repeat");
      const t15 = this.transformSize(1);
      a16 || (a16 = 0), o14 ? o14 *= t15 : o14 = 0, n18 ? n18 *= t15 : n18 = 0, i10 && (i10 *= t15);
      const e16 = i10 ? i10 / m8.height : 1, r19 = l16 && i10 ? l16 * i10 / m8.width : 1;
      if (0 !== a16 || 1 !== e16 || 1 !== r19 || 0 !== o14 || 0 !== n18) {
        const t16 = new DOMMatrix();
        t16.rotateSelf(0, 0, -a16).translateSelf(o14, n18).scaleSelf(r19, e16, 1), y6.setTransform(t16);
      }
    } else y6 = f10.createPattern(P5, "repeat");
    f10.save(), f10.fillStyle = y6, f10.fill("evenodd"), f10.restore();
  }
  drawPictureStroke(t14, e15) {
    if (!t14) return;
    let { colorSubstitutions: i10, capStyle: o14, joinStyle: n18, miterLimit: a16, tintColor: h8, url: c13, width: m8 } = e15;
    const f10 = this._resourceManager.getResource(c13);
    if (t(f10)) return;
    let u13;
    if (y(t14)) u13 = t14.rings;
    else if (f2(t14)) u13 = t14.paths;
    else {
      if (!u2(t14)) return l(t14) ? void console.log("CanvasDrawHelper.drawPictureStroke - No implementation!") : void 0;
      u13 = Q(t14).rings;
    }
    m8 || (m8 = f10.width);
    const y6 = A2(c13) || "src" in f10 && A2(f10.src);
    let P5 = "getFrame" in f10 ? t11(f10) : f10;
    i10 && (P5 = this._colorSubstitutionHelper.applyColorSubstituition(P5, i10)), this._applyAdditionalRenderProps && (y6 || h8 && (P5 = this._colorSubstitutionHelper.tintImageData(P5, h8)));
    const w6 = Math.max(this.transformSize(u4(m8)), 0.5), x5 = w6 / P5.width, S3 = this._ctx, M4 = S3.createPattern(P5, "repeat-y");
    let b5, C5;
    S3.save(), this._setCapStyle(o14), this._setJoinStyle(n18), void 0 !== a16 && (S3.miterLimit = a16), S3.lineWidth = w6;
    for (let s13 of u13) if (s13 = p(s13), it(s13), s13 && !(s13.length <= 1)) {
      b5 = this.transformPt(s13[0]);
      for (let t15 = 1; t15 < s13.length; t15++) {
        C5 = this.transformPt(s13[t15]);
        const e16 = K2(b5, C5), r18 = new DOMMatrix();
        r18.translateSelf(0, b5[1] - w6 / 2).scaleSelf(x5, x5, 1).rotateSelf(0, 0, 90 - e16), M4.setTransform(r18), S3.strokeStyle = M4, S3.beginPath(), S3.moveTo(b5[0], b5[1]), S3.lineTo(C5[0], C5[1]), S3.stroke(), b5 = C5;
      }
    }
    S3.restore();
  }
  _buildPath(t14, e15) {
    const r18 = this._ctx;
    if (r18.beginPath(), t14) for (const i10 of t14) {
      const t15 = i10 ? i10.length : 0;
      if (t15 > 1) {
        let s13 = this.transformPt(i10[0]);
        r18.moveTo(s13[0], s13[1]);
        for (let e16 = 1; e16 < t15; e16++) s13 = this.transformPt(i10[e16]), r18.lineTo(s13[0], s13[1]);
        e15 && r18.closePath();
      }
    }
  }
  _setCapStyle(t14) {
    switch (t14) {
      case U.Butt:
        this._ctx.lineCap = "butt";
        break;
      case U.Round:
        this._ctx.lineCap = "round";
        break;
      case U.Square:
        this._ctx.lineCap = "square";
    }
  }
  _setJoinStyle(t14) {
    switch (t14) {
      case w.Bevel:
        this._ctx.lineJoin = "bevel";
        break;
      case w.Round:
        this._ctx.lineJoin = "round";
        break;
      case w.Miter:
        this._ctx.lineJoin = "miter";
    }
  }
};
function K2(t14, e15) {
  const r18 = e15[0] - t14[0], i10 = e15[1] - t14[1];
  return 180 / Math.PI * Math.atan2(i10, r18);
}
var Q = (t14) => t14 ? { spatialReference: t14.spatialReference, rings: [[[t14.xmin, t14.ymin], [t14.xmin, t14.ymax], [t14.xmax, t14.ymax], [t14.xmax, t14.ymin], [t14.xmin, t14.ymin]]] } : null;
var Z2 = (t14) => {
  switch (t14) {
    case "Left":
      return e6.Left;
    case "Right":
      return e6.Right;
    case "Center":
      return e6.Center;
    case "Justify":
      return N2.warnOnce("Horizontal alignment 'justify' is not implemented. Falling back to 'center'."), e6.Center;
  }
};
var $ = (t14) => {
  switch (t14) {
    case "Top":
      return t3.Top;
    case "Center":
      return t3.Center;
    case "Bottom":
      return t3.Bottom;
    case "Baseline":
      return t3.Baseline;
  }
};
var tt = (t14, e15, r18) => {
  switch (t14) {
    case "ExtraLeading":
      return 1 + e15 / r18;
    case "Multiple":
      return e15;
    case "Exact":
      return e15 / r18;
  }
};
function et(t14, r18 = 1) {
  const i10 = k2(t14), s13 = g3(t14.fontStyleName), o14 = s4(t14.fontFamilyName), { weight: n18, style: a16 } = s13, l16 = r18 * (t14.height || 5), h8 = x(t14.horizontalAlignment), c13 = F(t14.verticalAlignment), m8 = P2(t14), f10 = w3(t14.haloSymbol), u13 = f10 ? r18 * (0 | t14.haloSize) : 0;
  return { color: m8, size: l16, horizontalAlignment: h8, verticalAlignment: c13, font: { family: o14, style: h2(a16), weight: S(n18), decoration: i10 }, halo: { size: u13 || 0, color: f10, style: a16 }, pixelRatio: 1, premultiplyColors: true };
}
var rt = 1e-4;
function it(t14) {
  let e15, r18, i10, s13, o14, n18 = t14[0], a16 = 1;
  for (; a16 < t14.length; ) e15 = t14[a16][0] - n18[0], r18 = t14[a16][1] - n18[1], s13 = 0 !== e15 ? r18 / e15 : Math.PI / 2, void 0 !== i10 && s13 - i10 <= rt ? (t14.splice(a16 - 1, 1), n18 = o14) : (o14 = n18, n18 = t14[a16], a16++), i10 = s13;
}

// node_modules/@arcgis/core/symbols/cim/CIMSymbolHelper.js
var Y3 = Math.PI;
var $2 = Y3 / 2;
var U3 = 4;
var q3 = 4;
var W2 = 10;
var J2 = 96 / 72;
var K3 = Math.PI / 180;
var Q2 = s.getLogger("esri.symbols.cim.CIMSymbolHelper");
function Z3(e15) {
  if (!e15 || !e15.type) return null;
  let t14;
  switch (e15.type) {
    case "cim":
      return e15.data;
    case "web-style":
      return e15;
    case "simple-marker": {
      const r18 = ie2.fromSimpleMarker(e15);
      if (!r18) return null;
      t14 = r18;
      break;
    }
    case "picture-marker":
      t14 = ie2.fromPictureMarker(e15);
      break;
    case "simple-line":
      t14 = ie2.fromSimpleLineSymbol(e15);
      break;
    case "simple-fill":
      t14 = ie2.fromSimpleFillSymbol(e15);
      break;
    case "picture-fill":
      t14 = ie2.fromPictureFillSymbol(e15);
      break;
    case "text":
      t14 = ie2.fromTextSymbol(e15);
  }
  return { type: "CIMSymbolReference", symbol: t14 };
}
function ee(e15, t14, r18) {
  switch (t14.type) {
    case "CIMSymbolReference":
      return ee(e15, t14.symbol, r18);
    case "CIMPointSymbol":
      null == r18 && (r18 = { x: 0, y: 0 }), e15.drawSymbol(t14, r18);
      break;
    case "CIMLineSymbol":
      null == r18 && (r18 = { paths: [[[0, 0], [10, 0]]] }), e15.drawSymbol(t14, r18);
      break;
    case "CIMPolygonSymbol":
      null == r18 && (r18 = { rings: [[[0, 0], [0, 10], [10, 10], [10, 0], [0, 0]]] }), e15.drawSymbol(t14, r18);
      break;
    case "CIMTextSymbol": {
      const r19 = { x: 0, y: 0 };
      e15.drawSymbol(t14, r19);
      break;
    }
    case "CIMVectorMarker": {
      const r19 = new t9();
      e15.drawMarker(t14, r19);
      break;
    }
  }
  return e15.envelope();
}
function te(e15) {
  if (!e15) return 0;
  switch (e15.type) {
    case "CIMMarkerPlacementAlongLineSameSize":
    case "CIMMarkerPlacementAlongLineRandomSize":
    case "CIMMarkerPlacementAtExtremities":
    case "CIMMarkerPlacementAtMeasuredUnits":
    case "CIMMarkerPlacementAtRatioPositions":
    case "CIMMarkerPlacementOnLine":
    case "CIMMarkerPlacementOnVertices":
      return Math.abs(e15.offset);
    default:
      return 0;
  }
}
function re(e15) {
  if (!e15) return 0;
  switch (e15.type) {
    case "CIMGeometricEffectArrow":
      return Math.abs(0.5 * e15.width);
    case "CIMGeometricEffectBuffer":
      return Math.abs(e15.size);
    case "CIMGeometricEffectExtension":
    case "CIMGeometricEffectRadial":
      return Math.abs(e15.length);
    case "CIMGeometricEffectJog":
      return Math.abs(0.5 * e15.length);
    case "CIMGeometricEffectMove":
      return Math.max(Math.abs(b(e15.offsetX)), Math.abs(b(e15.offsetY)));
    case "CIMGeometricEffectOffset":
    case "CIMGeometricEffectOffsetTangent":
      return Math.abs(e15.offset);
    case "CIMGeometricEffectRegularPolygon":
      return Math.abs(e15.radius);
    case "CIMGeometricEffectRotate":
    case "CIMGeometricEffectScale":
    default:
      return 0;
    case "CIMGeometricEffectTaperedPolygon":
      return 0.5 * Math.max(Math.abs(e15.fromWidth), Math.abs(e15.toWidth));
    case "CIMGeometricEffectWave":
      return Math.abs(e15.amplitude);
  }
}
function ae(e15) {
  if (!e15) return 0;
  let t14 = 0;
  for (const r18 of e15) t14 += re(r18);
  return t14;
}
var oe2 = class {
  getSymbolInflateSize(e15, t14, r18, a16, o14) {
    return e15 || (e15 = [0, 0, 0, 0]), t14 ? this._getInflateSize(e15, t14, r18, a16, o14) : e15;
  }
  static safeSize(e15) {
    const t14 = Math.max(Math.abs(e15[0]), Math.abs(e15[2])), r18 = Math.max(Math.abs(e15[1]), Math.abs(e15[3]));
    return Math.sqrt(t14 * t14 + r18 * r18);
  }
  _vectorMarkerBounds(e15, t14, r18, a16) {
    let o14 = true;
    const i10 = u3();
    if (t14 && t14.markerGraphics) for (const s13 of t14.markerGraphics) {
      const t15 = [0, 0, 0, 0];
      s13.geometry && (c2(i10, s13.geometry), t15[0] = 0, t15[1] = 0, t15[2] = 0, t15[3] = 0, this.getSymbolInflateSize(t15, s13.symbol, r18, 0, a16), i10[0] += t15[0], i10[1] += t15[1], i10[2] += t15[2], i10[3] += t15[3], o14 ? (e15[0] = i10[0], e15[1] = i10[1], e15[2] = i10[2], e15[3] = i10[3], o14 = false) : (e15[0] = Math.min(e15[0], i10[0]), e15[1] = Math.min(e15[1], i10[1]), e15[2] = Math.max(e15[2], i10[2]), e15[3] = Math.max(e15[3], i10[3])));
    }
    return e15;
  }
  _getInflateSize(e15, t14, r18, a16, o14) {
    if (Me(t14)) {
      const i10 = this._getLayersInflateSize(e15, t14.symbolLayers, r18, a16, o14), s13 = ae(t14.effects);
      return s13 > 0 && (i10[0] -= s13, i10[1] -= s13, i10[2] += s13, i10[3] += s13), i10;
    }
    return this._getTextInflatedSize(e15, t14, o14);
  }
  _getLayersInflateSize(e15, t14, r18, a16, i10) {
    let s13 = true;
    if (!t14) return e15;
    for (const n18 of t14) {
      if (!n18) continue;
      let t15 = [0, 0, 0, 0];
      switch (n18.type) {
        case "CIMSolidFill":
        case "CIMPictureFill":
        case "CIMHatchFill":
        case "CIMGradientFill":
          break;
        case "CIMSolidStroke":
        case "CIMPictureStroke":
        case "CIMGradientStroke": {
          const e16 = n18;
          let r19 = e16.width;
          null != r19 && (e16.capStyle === U.Square || e16.joinStyle === w.Miter ? r19 /= 1.4142135623730951 : r19 /= 2, t15[0] = -r19, t15[1] = -r19, t15[2] = r19, t15[3] = r19);
          break;
        }
        case "CIMCharacterMarker":
        case "CIMVectorMarker":
        case "CIMPictureMarker": {
          const e16 = n18;
          if ("CIMVectorMarker" === n18.type) {
            const e17 = n18;
            if (t15 = this._vectorMarkerBounds(t15, e17, r18, i10), e17.frame) {
              const r19 = (e17.frame.xmin + e17.frame.xmax) / 2, a17 = (e17.frame.ymin + e17.frame.ymax) / 2;
              if (t15[0] -= r19, t15[1] -= a17, t15[2] -= r19, t15[3] -= a17, null != e17.size) {
                const r20 = e17.size / (e17.frame.ymax - e17.frame.ymin);
                t15[0] *= r20, t15[1] *= r20, t15[2] *= r20, t15[3] *= r20;
              }
            }
          } else if ("CIMPictureMarker" === n18.type) {
            const a17 = n18, i11 = r18.getResource(a17.url);
            let s15 = 1;
            if (r(i11) && i11.height && (s15 = i11.width / i11.height), null != e16.size) {
              const r19 = e16.size / 2, o14 = e16.size * s15 * a17.scaleX / 2;
              t15 = [-o14, -r19, o14, r19];
            }
          } else if (null != e16.size) {
            const r19 = e16.size / 2;
            t15 = [-r19, -r19, r19, r19];
          }
          if (e16.anchorPoint) {
            let r19, a17;
            "Absolute" === e16.anchorPointUnits ? (r19 = e16.anchorPoint.x, a17 = e16.anchorPoint.y) : (r19 = e16.anchorPoint.x * (t15[2] - t15[0]), a17 = e16.anchorPoint.y * (t15[3] - t15[1])), t15[0] -= r19, t15[1] -= a17, t15[2] -= r19, t15[3] -= a17;
          }
          let s14 = b(e16.rotation);
          if (e16.rotateClockwise && (s14 = -s14), a16 && (s14 -= a16), s14) {
            const e17 = K3 * s14, r19 = Math.cos(e17), a17 = Math.sin(e17), o14 = u3([n4, n4, -n4, -n4]);
            m(o14, [t15[0] * r19 - t15[1] * a17, t15[0] * a17 + t15[1] * r19]), m(o14, [t15[0] * r19 - t15[3] * a17, t15[0] * a17 + t15[3] * r19]), m(o14, [t15[2] * r19 - t15[1] * a17, t15[2] * a17 + t15[1] * r19]), m(o14, [t15[2] * r19 - t15[3] * a17, t15[2] * a17 + t15[3] * r19]), t15 = o14;
          }
          let l17 = b(e16.offsetX), f10 = b(e16.offsetY);
          if (a16) {
            const e17 = K3 * a16, t16 = Math.cos(e17), r19 = Math.sin(e17), o14 = l17 * r19 + f10 * t16;
            l17 = l17 * t16 - f10 * r19, f10 = o14;
          }
          t15[0] += l17, t15[1] += f10, t15[2] += l17, t15[3] += f10;
          const y6 = te(e16.markerPlacement);
          y6 > 0 && (t15[0] -= y6, t15[1] -= y6, t15[2] += y6, t15[3] += y6);
          break;
        }
      }
      const l16 = ae(n18.effects);
      l16 > 0 && (t15[0] -= l16, t15[1] -= l16, t15[2] += l16, t15[3] += l16), s13 ? (e15[0] = t15[0], e15[1] = t15[1], e15[2] = t15[2], e15[3] = t15[3], s13 = false) : (e15[0] = Math.min(e15[0], t15[0]), e15[1] = Math.min(e15[1], t15[1]), e15[2] = Math.max(e15[2], t15[2]), e15[3] = Math.max(e15[3], t15[3]));
    }
    return e15;
  }
  _getTextInflatedSize(e15, r18, a16) {
    var _a, _b;
    const o14 = r18.height ?? W2;
    if (e15[0] = -o14 / 2, e15[1] = -o14 / 2, e15[2] = o14 / 2, e15[3] = o14 / 2, !a16) return e15;
    const i10 = a16.get(r18);
    if (!i10) return e15;
    const { text: s13, mosaicItem: n18 } = i10;
    if (!((_a = n18 == null ? void 0 : n18.glyphMosaicItems) == null ? void 0 : _a.length)) return e15;
    const { lineGapType: l16, lineGap: c13 } = r18, m8 = l16 ? tt(l16, c13 ?? 0, o14) : 0, f10 = i5(s13)[1], y6 = n18.glyphMosaicItems, u13 = "CIMBackgroundCallout" === ((_b = r18.callout) == null ? void 0 : _b.type), M4 = S2(y6, f10, { scale: o14 / j, angle: b(r18.angle), xOffset: b(r18.offsetX), yOffset: b(r18.offsetY), hAlign: Z2(r18.horizontalAlignment), vAlign: $(r18.verticalAlignment), maxLineWidth: 512, lineHeight: f3 * Math.max(0.25, Math.min(m8 || 1, 4)), decoration: r18.font.decoration || "none", isCIM: true, hasBackground: u13 }).boundsT;
    return e15[0] = M4.x - M4.halfWidth, e15[1] = -M4.y - M4.halfHeight, e15[2] = M4.x + M4.halfWidth, e15[3] = -M4.y + M4.halfHeight, e15;
  }
};
var ie2 = class _ie {
  static getEnvelope(e15, t14, r18) {
    if (!e15) return null;
    const a16 = new q2(r18);
    if (Array.isArray(e15)) {
      let r19;
      for (const o14 of e15) r19 ? r19.union(ee(a16, o14, t14)) : r19 = ee(a16, o14, t14);
      return r19;
    }
    return ee(a16, e15, t14);
  }
  static getTextureAnchor(e15, t14) {
    const r18 = this.getEnvelope(e15, null, t14);
    if (!r18) return [0, 0, 0];
    const a16 = (r18.x + 0.5 * r18.width) * J2, o14 = (r18.y + 0.5 * r18.height) * J2, i10 = r18.width * J2 + 2, s13 = r18.height * J2 + 2;
    return [-a16 / i10, -o14 / s13, s13];
  }
  static rasterize(e15, t14, r18, a16, o14 = true) {
    const i10 = r18 || this.getEnvelope(t14, null, a16);
    if (!i10) return [null, 0, 0, 0, 0];
    const s13 = (i10.x + 0.5 * i10.width) * J2, n18 = (i10.y + 0.5 * i10.height) * J2;
    e15.width = i10.width * J2, e15.height = i10.height * J2, r18 || (e15.width += 2, e15.height += 2);
    const l16 = e15.getContext("2d"), c13 = O3.createScale(J2, -J2);
    c13.translate(0.5 * e15.width - s13, 0.5 * e15.height + n18);
    const m8 = new W(l16, a16, c13);
    switch (t14.type) {
      case "CIMPointSymbol": {
        const e16 = { type: "point", x: 0, y: 0 };
        m8.drawSymbol(t14, e16);
        break;
      }
      case "CIMVectorMarker": {
        const e16 = new t9();
        m8.drawMarker(t14, e16);
        break;
      }
    }
    const f10 = l16.getImageData(0, 0, e15.width, e15.height), y6 = new Uint8Array(f10.data);
    if (o14) {
      let e16;
      for (let t15 = 0; t15 < y6.length; t15 += 4) e16 = y6[t15 + 3] / 255, y6[t15] = y6[t15] * e16, y6[t15 + 1] = y6[t15 + 1] * e16, y6[t15 + 2] = y6[t15 + 2] * e16;
    }
    return [y6, e15.width, e15.height, -s13 / e15.width, -n18 / e15.height];
  }
  static fromTextSymbol(e15) {
    const { angle: r18, color: a16, font: o14, haloColor: i10, haloSize: s13, horizontalAlignment: n18, kerning: l16, text: c13, verticalAlignment: m8, xoffset: f10, yoffset: y6, backgroundColor: u13, borderLineColor: h8, borderLineSize: p6 } = e15;
    let M4, d4, S3, b5, g10, C5;
    o14 && (M4 = o14.family, d4 = o14.style, S3 = o14.weight, b5 = o14.size, g10 = o14.decoration);
    let F3 = false;
    if (c13) {
      F3 = i5(c13)[1];
    }
    return (u13 || p6) && (C5 = { type: "CIMBackgroundCallout", margin: null, backgroundSymbol: { type: "CIMPolygonSymbol", symbolLayers: [{ type: "CIMSolidFill", color: fe(u13) }, { type: "CIMSolidStroke", color: fe(h8), width: p6 }] }, accentBarSymbol: null, gap: null, leaderLineSymbol: null, lineStyle: null }), { type: "CIMPointSymbol", symbolLayers: [{ type: "CIMVectorMarker", enable: true, anchorPointUnits: "Relative", dominantSizeAxis3D: "Y", size: 10, billboardMode3D: "FaceNearPlane", frame: { xmin: -5, ymin: -5, xmax: 5, ymax: 5 }, markerGraphics: [{ type: "CIMMarkerGraphic", geometry: { x: 0, y: 0 }, symbol: { type: "CIMTextSymbol", angle: r18, blockProgression: C2.BTT, depth3D: 1, extrapolateBaselines: true, fontEffects: l2.Normal, fontEncoding: a.Unicode, fontFamilyName: M4 || "Arial", fontStyleName: ye(d4, S3), fontType: u5.Unspecified, haloSize: s13, height: b5, hinting: y2.Default, horizontalAlignment: ce(n18 ?? "center"), kerning: l16, letterWidth: 100, ligatures: true, lineGapType: "Multiple", offsetX: b(f10), offsetY: b(y6), strikethrough: "line-through" === g10, underline: "underline" === g10, symbol: { type: "CIMPolygonSymbol", symbolLayers: [{ type: "CIMSolidFill", enable: true, color: fe(a16) }] }, haloSymbol: { type: "CIMPolygonSymbol", symbolLayers: [{ type: "CIMSolidFill", enable: true, color: fe(i10) }] }, shadowColor: [0, 0, 0, 255], shadowOffsetX: 1, shadowOffsetY: 1, textCase: "Normal", textDirection: F3 ? ne.RTL : ne.LTR, verticalAlignment: me(m8 ?? "baseline"), verticalGlyphOrientation: oe.Right, wordSpacing: 100, billboardMode3D: m2.FaceNearPlane, callout: C5 }, textString: c13 }], scaleSymbolsProportionally: true, respectFrame: true }], scaleX: 1, angleAlignment: "Display" };
  }
  static fromPictureFillSymbol(e15) {
    const { height: t14, outline: r18, width: a16, xoffset: o14, xscale: i10, yoffset: s13, yscale: n18 } = e15, l16 = [], c13 = { type: "CIMPolygonSymbol", symbolLayers: l16 };
    if (r18) {
      const { cap: e16, join: t15, miterLimit: a17, width: o15 } = r18;
      l16.push({ type: "CIMSolidStroke", color: fe(r18.color), capStyle: ne2(e16), joinStyle: le(t15), miterLimit: a17, width: o15 });
    }
    let m8 = e15.url;
    "esriPFS" === e15.type && e15.imageData && (m8 = e15.imageData);
    const f10 = "angle" in e15 ? e15.angle ?? 0 : 0, y6 = (a16 ?? 0) * (i10 || 1), u13 = (t14 ?? 0) * (n18 || 1);
    return l16.push({ type: "CIMPictureFill", invertBackfaceTexture: false, scaleX: 1, textureFilter: ie.Picture, tintColor: null, url: m8, height: u13, width: y6, offsetX: b(o14), offsetY: b(s13), rotation: b(-f10), colorSubstitutions: null }), c13;
  }
  static fromSimpleFillSymbol(e15) {
    const { color: t14, style: a16, outline: o14 } = e15, i10 = [], s13 = { type: "CIMPolygonSymbol", symbolLayers: i10 };
    let n18 = null;
    if (o14) {
      const { cap: e16, join: t15, style: r18 } = o14;
      "solid" !== r18 && "none" !== r18 && "esriSLSSolid" !== r18 && "esriSLSNull" !== r18 && (n18 = [{ type: "CIMGeometricEffectDashes", dashTemplate: pe(r18, e16), lineDashEnding: "NoConstraint", scaleDash: true, offsetAlongLine: null }]), i10.push({ type: "CIMSolidStroke", color: fe(o14.color), capStyle: ne2(e16), joinStyle: le(t15), miterLimit: o14.miterLimit, width: o14.width, effects: n18 });
    }
    if (a16 && "solid" !== a16 && "none" !== a16 && "esriSFSSolid" !== a16 && "esriSFSNull" !== a16) {
      const e16 = { type: "CIMLineSymbol", symbolLayers: [{ type: "CIMSolidStroke", color: fe(t14), capStyle: U.Butt, joinStyle: w.Miter, width: 0.75 }] };
      let o15 = 0;
      const s14 = e3(Se(a16) ? 8 : 10);
      switch (a16) {
        case "vertical":
        case "esriSFSVertical":
          o15 = 90;
          break;
        case "forward-diagonal":
        case "esriSFSForwardDiagonal":
        case "diagonal-cross":
        case "esriSFSDiagonalCross":
          o15 = -45;
          break;
        case "backward-diagonal":
        case "esriSFSBackwardDiagonal":
          o15 = 45;
          break;
        case "cross":
        case "esriSFSCross":
          o15 = 0;
      }
      i10.push({ type: "CIMHatchFill", lineSymbol: e16, offsetX: 0, offsetY: 0, rotation: o15, separation: s14 }), "cross" === a16 || "esriSFSCross" === a16 ? i10.push({ type: "CIMHatchFill", lineSymbol: p(e16), offsetX: 0, offsetY: 0, rotation: 90, separation: s14 }) : "diagonal-cross" !== a16 && "esriSFSDiagonalCross" !== a16 || i10.push({ type: "CIMHatchFill", lineSymbol: p(e16), offsetX: 0, offsetY: 0, rotation: 45, separation: s14 });
    } else !a16 || "solid" !== a16 && "esriSFSSolid" !== a16 || i10.push({ type: "CIMSolidFill", enable: true, color: fe(t14) });
    return s13;
  }
  static fromSimpleLineSymbol(e15) {
    const { cap: t14, color: r18, join: a16, marker: o14, miterLimit: i10, style: s13, width: n18 } = e15;
    let l16 = null;
    "solid" !== s13 && "none" !== s13 && "esriSLSSolid" !== s13 && "esriSLSNull" !== s13 && (l16 = [{ type: "CIMGeometricEffectDashes", dashTemplate: pe(s13, t14), lineDashEnding: "NoConstraint", scaleDash: true, offsetAlongLine: null }]);
    const c13 = [];
    if (o14) {
      let e16;
      switch (o14.placement) {
        case "begin-end":
          e16 = o4.Both;
          break;
        case "begin":
          e16 = o4.JustBegin;
          break;
        case "end":
          e16 = o4.JustEnd;
          break;
        default:
          e16 = o4.None;
      }
      const t15 = _ie.fromSimpleMarker(o14, n18, r18).symbolLayers[0];
      t15.markerPlacement = { type: "CIMMarkerPlacementAtExtremities", angleToLine: true, offset: 0, extremityPlacement: e16, offsetAlongLine: 0 }, c13.push(t15);
    }
    return "none" !== s13 && "esriSLSNull" !== s13 && c13.push({ type: "CIMSolidStroke", color: fe(r18), capStyle: ne2(t14), joinStyle: le(a16), miterLimit: i10, width: n18, effects: l16 }), { type: "CIMLineSymbol", symbolLayers: c13 };
  }
  static fromPictureMarker(e15) {
    const { angle: t14, height: r18, width: a16, xoffset: o14, yoffset: i10 } = e15;
    let s13 = e15.url;
    return "esriPMS" === e15.type && e15.imageData && (s13 = e15.imageData), { type: "CIMPointSymbol", symbolLayers: [{ type: "CIMPictureMarker", invertBackfaceTexture: false, scaleX: 1, textureFilter: ie.Picture, tintColor: null, url: s13, size: r18, width: a16, offsetX: b(o14), offsetY: b(i10), rotation: b(-t14) }] };
  }
  static fromSimpleMarker(e15, t14, r18) {
    const { style: a16 } = e15, o14 = e15.color ?? r18;
    if ("path" === a16) {
      const t15 = [];
      if ("outline" in e15 && e15.outline) {
        const r20 = e15.outline;
        t15.push({ type: "CIMSolidStroke", enable: true, width: u4(Math.round(e3(r20.width))), color: fe(r20.color) });
      }
      t15.push({ type: "CIMSolidFill", enable: true, color: fe(o14), path: e15.path });
      const [r19, a17] = de("square");
      return { type: "CIMPointSymbol", symbolLayers: [{ type: "CIMVectorMarker", enable: true, rotation: b(-e15.angle), size: b(e15.size || 6), offsetX: b(e15.xoffset), offsetY: b(e15.yoffset), frame: r19, markerGraphics: [{ type: "CIMMarkerGraphic", geometry: a17, symbol: { type: "CIMPolygonSymbol", symbolLayers: t15 } }] }] };
    }
    const [i10, s13] = de(a16);
    let c13;
    if (s13 && i10) {
      const r19 = [];
      if ("outline" in e15 && e15.outline) {
        const t15 = e15.outline;
        r19.push({ type: "CIMSolidStroke", enable: true, width: null != t15.width && t15.width > 0.667 ? u4(Math.round(e3(t15.width))) : t15.width, color: fe(t15.color) });
      } else !t14 || "line-marker" !== e15.type || "cross" !== e15.style && "x" !== e15.style || r19.push({ type: "CIMSolidStroke", enable: true, width: t14, color: fe(o14) });
      r19.push({ type: "CIMSolidFill", enable: true, color: fe(o14) });
      const a17 = { type: "CIMPolygonSymbol", symbolLayers: r19 };
      c13 = { type: "CIMPointSymbol", symbolLayers: [{ type: "CIMVectorMarker", enable: true, rotation: b(-e15.angle), size: b(e15.size || 6 * t14), offsetX: b(e15.xoffset), offsetY: b(e15.yoffset), frame: i10, markerGraphics: [{ type: "CIMMarkerGraphic", geometry: s13, symbol: a17 }] }] };
    }
    return c13;
  }
  static fromCIMHatchFill(e15, t14) {
    var _a;
    const a16 = t14 * (e15.separation ?? U3), o14 = a16 / 2, i10 = p(e15.lineSymbol);
    (_a = i10.symbolLayers) == null ? void 0 : _a.forEach((e16) => {
      var _a2;
      switch (e16.type) {
        case "CIMSolidStroke":
          null != e16.width && (e16.width *= t14), (_a2 = e16.effects) == null ? void 0 : _a2.forEach((e17) => {
            "CIMGeometricEffectDashes" === e17.type && (e17.dashTemplate = e17.dashTemplate.map((e18) => e18 * t14));
          });
          break;
        case "CIMVectorMarker": {
          null != e16.size && (e16.size *= t14);
          const r18 = e16.markerPlacement;
          null != r18 && "placementTemplate" in r18 && (r18.placementTemplate = r18.placementTemplate.map((e17) => e17 * t14));
          break;
        }
      }
    });
    let s13 = this._getLineSymbolPeriod(i10) || q3;
    for (; s13 < q3; ) s13 *= 2;
    const n18 = s13 / 2;
    return { type: "CIMVectorMarker", frame: { xmin: -n18, xmax: n18, ymin: -o14, ymax: o14 }, markerGraphics: [{ type: "CIMMarkerGraphic", geometry: { paths: [[[-n18, 0], [n18, 0]]] }, symbol: i10 }], size: a16 };
  }
  static fetchResources(e15, t14, r18) {
    if (e15 && t14) switch (e15.type) {
      case "CIMPointSymbol":
      case "CIMLineSymbol":
      case "CIMPolygonSymbol": {
        const a16 = e15.symbolLayers;
        if (!a16) return;
        for (const e16 of a16) switch (ge(e16, t14, r18), e16.type) {
          case "CIMPictureFill":
          case "CIMHatchFill":
          case "CIMGradientFill":
          case "CIMPictureStroke":
          case "CIMGradientStroke":
          case "CIMCharacterMarker":
          case "CIMPictureMarker":
            "url" in e16 && e16.url && r18.push(t14.fetchResource(e16.url, null));
            break;
          case "CIMVectorMarker": {
            const a17 = e16.markerGraphics;
            if (!a17) continue;
            for (const e17 of a17) if (e17) {
              const a18 = e17.symbol;
              a18 && _ie.fetchResources(a18, t14, r18);
            }
          }
        }
      }
    }
  }
  static _getLineSymbolPeriod(e15) {
    if (e15) {
      const t14 = this._getEffectsRepeat(e15.effects);
      if (t14) return t14;
      if (e15.symbolLayers) {
        for (const r18 of e15.symbolLayers) if (r18) {
          const e16 = this._getEffectsRepeat(r18.effects);
          if (e16) return e16;
          switch (r18.type) {
            case "CIMCharacterMarker":
            case "CIMPictureMarker":
            case "CIMVectorMarker":
            case "CIMObjectMarker3D":
            case "CIMglTFMarker3D": {
              const e17 = this._getPlacementRepeat(r18.markerPlacement);
              if (e17) return e17;
            }
          }
        }
      }
    }
    return 0;
  }
  static _getEffectsRepeat(e15) {
    if (e15) {
      for (const t14 of e15) if (t14) switch (t14.type) {
        case "CIMGeometricEffectDashes": {
          const e16 = t14.dashTemplate;
          if (e16 && e16.length) {
            let t15 = 0;
            for (const r18 of e16) t15 += r18;
            return 1 & e16.length && (t15 *= 2), t15;
          }
          break;
        }
        case "CIMGeometricEffectWave":
          return t14.period;
        default:
          Q2.error(`unsupported geometric effect type ${t14.type}`);
      }
    }
    return 0;
  }
  static _getPlacementRepeat(e15) {
    if (e15) switch (e15.type) {
      case "CIMMarkerPlacementAlongLineSameSize":
      case "CIMMarkerPlacementAlongLineRandomSize":
      case "CIMMarkerPlacementAlongLineVariableSize": {
        const t14 = e15.placementTemplate;
        if (t14 && t14.length) {
          let e16 = 0;
          for (const r18 of t14) e16 += +r18;
          return 1 & t14.length && (e16 *= 2), e16;
        }
        break;
      }
    }
    return 0;
  }
  static fromCIMInsidePolygon(e15) {
    const t14 = e15.markerPlacement, r18 = { ...e15 };
    r18.markerPlacement = null, r18.anchorPoint = null;
    const a16 = Math.abs(t14.stepX), o14 = Math.abs(t14.stepY), i10 = (t14.randomness ?? 100) / 100;
    let n18, c13, m8, f10;
    if ("Random" === t14.gridType) {
      const e16 = e3(dt), r19 = Math.max(Math.floor(e16 / a16), 1), y6 = Math.max(Math.floor(e16 / o14), 1);
      n18 = r19 * a16 / 2, c13 = y6 * o14 / 2, m8 = 2 * c13;
      const u13 = new t2(t14.seed), h8 = i10 * a16 / 1.5, p6 = i10 * o14 / 1.5;
      f10 = [];
      for (let t15 = 0; t15 < r19; t15++) for (let e17 = 0; e17 < y6; e17++) {
        const r20 = t15 * a16 - n18 + h8 * (0.5 - u13.getFloat()), i11 = e17 * o14 - c13 + p6 * (0.5 - u13.getFloat());
        f10.push({ x: r20, y: i11 }), 0 === t15 && f10.push({ x: r20 + 2 * n18, y: i11 }), 0 === e17 && f10.push({ x: r20, y: i11 + 2 * c13 });
      }
    } else true === t14.shiftOddRows ? (n18 = a16 / 2, c13 = o14, m8 = 2 * o14, f10 = [{ x: -n18, y: 0 }, { x: n18, y: 0 }, { x: 0, y: c13 }, { x: 0, y: -c13 }]) : (n18 = a16 / 2, c13 = o14 / 2, m8 = o14, f10 = [{ x: -a16, y: 0 }, { x: 0, y: -o14 }, { x: -a16, y: -o14 }, { x: 0, y: 0 }, { x: a16, y: 0 }, { x: 0, y: o14 }, { x: a16, y: o14 }, { x: -a16, y: o14 }, { x: a16, y: -o14 }]);
    return { type: "CIMVectorMarker", frame: { xmin: -n18, xmax: n18, ymin: -c13, ymax: c13 }, markerGraphics: f10.map((e16) => ({ type: "CIMMarkerGraphic", geometry: e16, symbol: { type: "CIMPointSymbol", symbolLayers: [r18] } })), size: m8 };
  }
  static getSize(e15) {
    if (e15) switch (e15.type) {
      case "CIMTextSymbol":
        return e15.height;
      case "CIMPointSymbol": {
        let t14 = 0;
        if (e15.symbolLayers) {
          for (const r18 of e15.symbolLayers) if (r18) switch (r18.type) {
            case "CIMCharacterMarker":
            case "CIMPictureMarker":
            case "CIMVectorMarker":
            case "CIMObjectMarker3D":
            case "CIMglTFMarker3D": {
              const e16 = r18.size;
              null != e16 && e16 > t14 && (t14 = e16);
              break;
            }
          }
        }
        return t14;
      }
      case "CIMLineSymbol":
      case "CIMPolygonSymbol": {
        let t14 = 0;
        if (e15.symbolLayers) {
          for (const r18 of e15.symbolLayers) if (r18) switch (r18.type) {
            case "CIMSolidStroke":
            case "CIMPictureStroke":
            case "CIMGradientStroke": {
              const e16 = r18.width;
              null != e16 && e16 > t14 && (t14 = e16);
              break;
            }
            case "CIMCharacterMarker":
            case "CIMPictureMarker":
            case "CIMVectorMarker":
            case "CIMObjectMarker3D":
            case "CIMglTFMarker3D":
              if (r18.markerPlacement && m3(r18.markerPlacement)) {
                const e16 = r18.size;
                null != e16 && e16 > t14 && (t14 = e16);
              }
          }
        }
        return t14;
      }
    }
  }
  static getMarkerScaleRatio(e15) {
    if (e15 && "CIMVectorMarker" === e15.type) {
      if (false !== e15.scaleSymbolsProportionally && e15.frame && null != e15.size) {
        const t14 = e15.frame.ymax - e15.frame.ymin;
        return e15.size / t14;
      }
    }
    return 1;
  }
};
var se = class _se {
  static findApplicableOverrides(e15, t14, r18) {
    if (e15 && t14) {
      if (e15.primitiveName) {
        let a16 = false;
        for (const t15 of r18) if (t15.primitiveName === e15.primitiveName) {
          a16 = true;
          break;
        }
        if (!a16) for (const o14 of t14) o14.primitiveName === e15.primitiveName && r18.push(o14);
      }
      switch (e15.type) {
        case "CIMPointSymbol":
        case "CIMLineSymbol":
        case "CIMPolygonSymbol":
          if (e15.effects) for (const a16 of e15.effects) _se.findApplicableOverrides(a16, t14, r18);
          if (e15.symbolLayers) for (const a16 of e15.symbolLayers) _se.findApplicableOverrides(a16, t14, r18);
          break;
        case "CIMTextSymbol":
          break;
        case "CIMSolidStroke":
        case "CIMPictureStroke":
        case "CIMGradientStroke":
        case "CIMSolidFill":
        case "CIMPictureFill":
        case "CIMHatchFill":
        case "CIMGradientFill":
        case "CIMVectorMarker":
        case "CIMCharacterMarker":
        case "CIMPictureMarker":
          if (e15.effects) for (const a16 of e15.effects) _se.findApplicableOverrides(a16, t14, r18);
          if (e15.markerPlacement && _se.findApplicableOverrides(e15.markerPlacement, t14, r18), "CIMVectorMarker" === e15.type) {
            if (e15.markerGraphics) for (const a16 of e15.markerGraphics) _se.findApplicableOverrides(a16, t14, r18), _se.findApplicableOverrides(a16.symbol, t14, r18);
          } else "CIMCharacterMarker" === e15.type ? _se.findApplicableOverrides(e15.symbol, t14, r18) : "CIMHatchFill" === e15.type ? _se.findApplicableOverrides(e15.lineSymbol, t14, r18) : "CIMPictureMarker" === e15.type && _se.findApplicableOverrides(e15.animatedSymbolProperties, t14, r18);
      }
    }
  }
  static findEffectOverrides(e15, t14, r18) {
    var _a;
    if (!t14 || !e15) return;
    const a16 = e15.length;
    for (let o14 = 0; o14 < a16; o14++) {
      const a17 = (_a = e15[o14]) == null ? void 0 : _a.primitiveName;
      if (a17) {
        let e16 = false;
        for (const t15 of r18) if (t15.primitiveName === a17) {
          e16 = true;
          break;
        }
        if (!e16) for (const o15 of t14) o15.primitiveName === a17 && r18.push(o15);
      }
    }
  }
  static async resolveSymbolOverrides(e15, t14, a16, o14, i10, s13, n18) {
    if (!e15 || !e15.symbol) return null;
    let { symbol: l16, primitiveOverrides: c13 } = e15;
    const m8 = !!c13;
    if (!m8 && !o14) return l16;
    l16 = p(l16);
    let f10 = true;
    if (t14 || (t14 = { attributes: {} }, f10 = false), m8) {
      if (f10 || (c13 = c13.filter((e16) => {
        var _a;
        return !((_a = e16.valueExpressionInfo) == null ? void 0 : _a.expression.includes("$feature"));
      })), n18 || (c13 = c13.filter((e16) => {
        var _a;
        return !((_a = e16.valueExpressionInfo) == null ? void 0 : _a.expression.includes("$view"));
      })), c13.length > 0) {
        const e16 = G2(t14.attributes);
        await _se.evaluateOverrides(c13, t14, { spatialReference: a16, fields: e16, geometryType: i10 }, s13, n18);
      }
      _se.applyOverrides(l16, c13);
    }
    return o14 && _se.applyDictionaryTextOverrides(l16, t14, o14), l16;
  }
  static async evaluateOverrides(e15, t14, r18, a16, o14) {
    if (!t14) return;
    let i10;
    for (const s13 of e15) {
      const e16 = s13.valueExpressionInfo;
      if (e16 && r18 && r18.geometryType) {
        i10 || (i10 = []), s13.value = void 0;
        const n18 = o7(e16.expression, r18.spatialReference, r18.fields).then((e17) => {
          s13.value = i4(e17, t14, { $view: o14 }, r18.geometryType, a16);
        });
        i10.push(n18);
      }
    }
    void 0 !== i10 && i10.length > 0 && await Promise.all(i10);
  }
  static applyDictionaryTextOverrides(e15, t14, r18, a16 = "Normal") {
    if (e15 && e15.type) switch (e15.type) {
      case "CIMPointSymbol":
      case "CIMLineSymbol":
      case "CIMPolygonSymbol":
      case "CIMTextSymbol":
        {
          const o14 = e15.symbolLayers;
          if (!o14) return;
          for (const i10 of o14) i10 && "CIMVectorMarker" === i10.type && _se.applyDictionaryTextOverrides(i10, t14, r18, "CIMTextSymbol" === e15.type ? e15.textCase : a16);
        }
        break;
      case "CIMVectorMarker":
        {
          const a17 = e15.markerGraphics;
          if (!a17) return;
          for (const e16 of a17) e16 && _se.applyDictionaryTextOverrides(e16, t14, r18);
        }
        break;
      case "CIMMarkerGraphic": {
        const o14 = e15.textString;
        if (o14 && o14.includes("[")) {
          const i10 = i3(o14, r18);
          e15.textString = l4(t14, i10, a16);
        }
      }
    }
  }
  static applyOverrides(e15, t14, r18, a16) {
    if (e15.primitiveName) {
      for (const i10 of t14) if (i10.primitiveName === e15.primitiveName) {
        const t15 = be(i10.propertyName);
        if (a16 && a16.push({ cim: e15, nocapPropertyName: t15, value: e15[t15] }), i10.expression && (i10.value = _se.toValue(i10.propertyName, i10.expression)), r18) {
          let t16 = false;
          for (const a17 of r18) a17.primitiveName === e15.primitiveName && (t16 = true);
          t16 || r18.push(i10);
        }
        r(i10.value) && (e15[t15] = i10.value);
      }
    }
    switch (e15.type) {
      case "CIMPointSymbol":
      case "CIMLineSymbol":
      case "CIMPolygonSymbol":
        if (e15.effects) for (const o14 of e15.effects) _se.applyOverrides(o14, t14, r18, a16);
        if (e15.symbolLayers) for (const o14 of e15.symbolLayers) _se.applyOverrides(o14, t14, r18, a16);
        break;
      case "CIMTextSymbol":
        break;
      case "CIMSolidStroke":
      case "CIMSolidFill":
      case "CIMVectorMarker":
        if (e15.effects) for (const o14 of e15.effects) _se.applyOverrides(o14, t14, r18, a16);
        if ("CIMVectorMarker" === e15.type && e15.markerGraphics) for (const o14 of e15.markerGraphics) _se.applyOverrides(o14, t14, r18, a16), _se.applyOverrides(o14.symbol, t14, r18, a16);
    }
  }
  static restoreOverrides(e15) {
    for (const t14 of e15) t14.cim[t14.nocapPropertyName] = t14.value;
  }
  static buildOverrideKey(e15) {
    let t14 = "";
    for (const r18 of e15) void 0 !== r18.value && (t14 += `${r18.primitiveName}${r18.propertyName}${JSON.stringify(r18.value)}`);
    return t14;
  }
  static toValue(t14, r18) {
    if ("DashTemplate" === t14) return r18.split(" ").map((e15) => Number(e15));
    if ("Color" === t14) {
      const t15 = new l3(r18).toRgba();
      return t15[3] *= 255, t15;
    }
    return r18;
  }
};
var ne2 = (e15) => {
  if (!e15) return U.Butt;
  switch (e15) {
    case "butt":
      return U.Butt;
    case "square":
      return U.Square;
    case "round":
      return U.Round;
  }
};
var le = (e15) => {
  if (!e15) return w.Miter;
  switch (e15) {
    case "miter":
      return w.Miter;
    case "round":
      return w.Round;
    case "bevel":
      return w.Bevel;
  }
};
var ce = (e15) => {
  if (t(e15)) return "Center";
  switch (e15) {
    case "left":
      return "Left";
    case "right":
      return "Right";
    case "center":
      return "Center";
  }
};
var me = (e15) => {
  if (t(e15)) return "Center";
  switch (e15) {
    case "baseline":
      return "Baseline";
    case "top":
      return "Top";
    case "middle":
      return "Center";
    case "bottom":
      return "Bottom";
  }
};
var fe = (e15) => {
  if (!e15) return [0, 0, 0, 0];
  const { r: t14, g: r18, b: a16, a: o14 } = e15;
  return [t14, r18, a16, 255 * o14];
};
var ye = (e15, t14) => {
  const r18 = ue(t14), a16 = he(e15);
  return r18 && a16 ? `${r18}-${a16}` : `${r18}${a16}`;
};
var ue = (e15) => {
  if (!e15) return "";
  switch (e15.toLowerCase()) {
    case "bold":
    case "bolder":
      return "bold";
  }
  return "";
};
var he = (e15) => {
  if (!e15) return "";
  switch (e15.toLowerCase()) {
    case "italic":
    case "oblique":
      return "italic";
  }
  return "";
};
var pe = (e15, t14) => {
  const r18 = "butt" === t14;
  switch (e15) {
    case "dash":
    case "esriSLSDash":
      return r18 ? [4, 3] : [3, 4];
    case "dash-dot":
    case "esriSLSDashDot":
      return r18 ? [4, 3, 1, 3] : [3, 4, 0, 4];
    case "dot":
    case "esriSLSDot":
      return r18 ? [1, 3] : [0, 4];
    case "long-dash":
    case "esriSLSLongDash":
      return r18 ? [8, 3] : [7, 4];
    case "long-dash-dot":
    case "esriSLSLongDashDot":
      return r18 ? [8, 3, 1, 3] : [7, 4, 0, 4];
    case "long-dash-dot-dot":
    case "esriSLSDashDotDot":
      return r18 ? [8, 3, 1, 3, 1, 3] : [7, 4, 0, 4, 0, 4];
    case "short-dash":
    case "esriSLSShortDash":
      return r18 ? [4, 1] : [3, 2];
    case "short-dash-dot":
    case "esriSLSShortDashDot":
      return r18 ? [4, 1, 1, 1] : [3, 2, 0, 2];
    case "short-dash-dot-dot":
    case "esriSLSShortDashDotDot":
      return r18 ? [4, 1, 1, 1, 1, 1] : [3, 2, 0, 2, 0, 2];
    case "short-dot":
    case "esriSLSShortDot":
      return r18 ? [1, 1] : [0, 2];
    case "solid":
    case "esriSLSSolid":
    case "none":
      return Q2.error("Unexpected: style does not require rasterization"), [0, 0];
    default:
      return Q2.error(`Tried to rasterize SLS, but found an unexpected style: ${e15}!`), [0, 0];
  }
};
function Me(e15) {
  return void 0 !== e15.symbolLayers;
}
var de = (e15) => {
  const t14 = 100, r18 = 50;
  let a16, o14;
  const i10 = e15;
  if ("circle" === i10 || "esriSMSCircle" === i10) {
    const e16 = 0.25;
    let t15 = Math.acos(1 - e16 / r18), i11 = Math.ceil(Y3 / t15 / 4);
    0 === i11 && (i11 = 1), t15 = $2 / i11, i11 *= 4;
    const s13 = [];
    s13.push([r18, 0]);
    for (let a17 = 1; a17 < i11; a17++) s13.push([r18 * Math.cos(a17 * t15), -r18 * Math.sin(a17 * t15)]);
    s13.push([r18, 0]), a16 = { rings: [s13] }, o14 = { xmin: -r18, ymin: -r18, xmax: r18, ymax: r18 };
  } else if ("cross" === i10 || "esriSMSCross" === i10) {
    const e16 = 0;
    a16 = { rings: [[[e16, r18], [e16, e16], [r18, e16], [r18, -e16], [e16, -e16], [e16, -r18], [-e16, -r18], [-e16, -e16], [-r18, -e16], [-r18, e16], [-e16, e16], [-e16, r18], [e16, r18]]] }, o14 = { xmin: -r18, ymin: -r18, xmax: r18, ymax: r18 };
  } else if ("diamond" === i10 || "esriSMSDiamond" === i10) a16 = { rings: [[[-r18, 0], [0, r18], [r18, 0], [0, -r18], [-r18, 0]]] }, o14 = { xmin: -r18, ymin: -r18, xmax: r18, ymax: r18 };
  else if ("square" === i10 || "esriSMSSquare" === i10) a16 = { rings: [[[-r18, -r18], [-r18, r18], [r18, r18], [r18, -r18], [-r18, -r18]]] }, o14 = { xmin: -r18, ymin: -r18, xmax: r18, ymax: r18 };
  else if ("x" === i10 || "esriSMSX" === i10) {
    const e16 = 0;
    a16 = { rings: [[[0, e16], [r18 - e16, r18], [r18, r18 - e16], [e16, 0], [r18, e16 - r18], [r18 - e16, -r18], [0, -e16], [e16 - r18, -r18], [-r18, e16 - r18], [-e16, 0], [-r18, r18 - e16], [e16 - r18, r18], [0, e16]]] }, o14 = { xmin: -r18, ymin: -r18, xmax: r18, ymax: r18 };
  } else if ("triangle" === i10 || "esriSMSTriangle" === i10) {
    const e16 = t14 * 0.5773502691896257, r19 = -e16, i11 = 2 / 3 * t14, s13 = i11 - t14;
    a16 = { rings: [[[r19, s13], [0, i11], [e16, s13], [r19, s13]]] }, o14 = { xmin: r19, ymin: s13, xmax: e16, ymax: i11 };
  } else "arrow" === i10 && (a16 = { rings: [[[-50, 50], [50, 0], [-50, -50], [-33, -20], [-33, 20], [-50, 50]]] }, o14 = { xmin: -r18, ymin: -r18, xmax: r18, ymax: r18 });
  return [o14, a16];
};
var Se = (e15) => "vertical" === e15 || "horizontal" === e15 || "cross" === e15 || "esriSFSCross" === e15 || "esriSFSVertical" === e15 || "esriSFSHorizontal" === e15;
var be = (e15) => e15 ? e15.charAt(0).toLowerCase() + e15.substr(1) : e15;
function ge(e15, t14, r18) {
  if (!e15.effects || r(t14.geometryEngine)) return;
  if (t14.geometryEnginePromise) return void r18.push(t14.geometryEnginePromise);
  d(e15.effects) && (t14.geometryEnginePromise = I(), r18.push(t14.geometryEnginePromise), t14.geometryEnginePromise.then((e16) => t14.geometryEngine = e16));
}

// node_modules/@arcgis/core/symbols/cim/quantizeTime.js
var t13 = 0.05;
function n16(n18) {
  return Math.max(Math.round(n18 / t13), 1) * t13;
}
var e12 = /* @__PURE__ */ new Set(["StartTimeOffset", "Duration", "RepeatDelay"]);
function a14(t14, a16) {
  return e12.has(a16) ? n16(t14) : t14;
}

// node_modules/@arcgis/core/symbols/cim/SDFHelper.js
function r16(t14) {
  var _a;
  if (!t14) return null;
  switch (t14.type) {
    case "CIMPointSymbol": {
      const o14 = t14.symbolLayers;
      return o14 && 1 === o14.length ? r16(o14[0]) : null;
    }
    case "CIMVectorMarker": {
      const o14 = t14.markerGraphics;
      if (!o14 || 1 !== o14.length) return null;
      const n18 = o14[0];
      if (!n18) return null;
      const r18 = n18.geometry;
      if (!r18) return null;
      const l16 = n18.symbol;
      return !l16 || "CIMPolygonSymbol" !== l16.type && "CIMLineSymbol" !== l16.type || ((_a = l16.symbolLayers) == null ? void 0 : _a.some((t15) => !!t15.effects)) ? null : { geom: r18, asFill: "CIMPolygonSymbol" === l16.type };
    }
    case "sdf":
      return { geom: t14.geom, asFill: t14.asFill };
  }
  return null;
}
function l15(t14) {
  return t14 ? t14.rings ? t14.rings : t14.paths ? t14.paths : void 0 !== t14.xmin && void 0 !== t14.ymin && void 0 !== t14.xmax && void 0 !== t14.ymax ? [[[t14.xmin, t14.ymin], [t14.xmin, t14.ymax], [t14.xmax, t14.ymax], [t14.xmax, t14.ymin], [t14.xmin, t14.ymin]]] : null : null;
}
function e13(t14) {
  let o14 = 1 / 0, r18 = -1 / 0, l16 = 1 / 0, e15 = -1 / 0;
  for (const n18 of t14) for (const t15 of n18) t15[0] < o14 && (o14 = t15[0]), t15[0] > r18 && (r18 = t15[0]), t15[1] < l16 && (l16 = t15[1]), t15[1] > e15 && (e15 = t15[1]);
  return new t12(o14, l16, r18 - o14, e15 - l16);
}
function s12(t14) {
  let o14 = 1 / 0, n18 = -1 / 0, r18 = 1 / 0, l16 = -1 / 0;
  for (const e15 of t14) for (const t15 of e15) t15[0] < o14 && (o14 = t15[0]), t15[0] > n18 && (n18 = t15[0]), t15[1] < r18 && (r18 = t15[1]), t15[1] > l16 && (l16 = t15[1]);
  return [o14, r18, n18, l16];
}
function i9(t14) {
  return t14 ? t14.rings ? s12(t14.rings) : t14.paths ? s12(t14.paths) : u2(t14) ? [t14.xmin, t14.ymin, t14.xmax, t14.ymax] : null : null;
}
function f8(t14, o14, n18, r18, l16) {
  const [e15, s13, i10, f10] = t14;
  if (i10 < e15 || f10 < s13) return [0, 0, 0];
  const a16 = i10 - e15, m8 = f10 - s13, u13 = 128, c13 = 1, h8 = Math.floor(0.5 * (0.5 * u13 - c13)), y6 = (u13 - 2 * (h8 + c13)) / Math.max(a16, m8), x5 = Math.round(a16 * y6) + 2 * h8, M4 = Math.round(m8 * y6) + 2 * h8;
  let g10 = 1;
  if (o14) {
    g10 = M4 / y6 / (o14.ymax - o14.ymin);
  }
  let p6 = 0, d4 = 0, b5 = 1;
  r18 && (l16 ? o14 && n18 && o14.ymax - o14.ymin > 0 && (b5 = (o14.xmax - o14.xmin) / (o14.ymax - o14.ymin), p6 = r18.x / (n18 * b5), d4 = r18.y / n18) : (p6 = r18.x, d4 = r18.y)), o14 && (p6 = 0.5 * (o14.xmax + o14.xmin) + p6 * (o14.xmax - o14.xmin), d4 = 0.5 * (o14.ymax + o14.ymin) + d4 * (o14.ymax - o14.ymin)), p6 -= e15, d4 -= s13, p6 *= y6, d4 *= y6, p6 += h8, d4 += h8;
  let w6 = p6 / x5 - 0.5, F3 = d4 / M4 - 0.5;
  return l16 && n18 && (w6 *= n18 * b5, F3 *= n18), [g10, w6, F3];
}
function a15(t14) {
  const o14 = l15(t14.geom), n18 = e13(o14), r18 = 128, s13 = 1, i10 = Math.floor(0.5 * (0.5 * r18 - s13)), f10 = (r18 - 2 * (i10 + s13)) / Math.max(n18.width, n18.height), a16 = Math.round(n18.width * f10) + 2 * i10, h8 = Math.round(n18.height * f10) + 2 * i10, y6 = [];
  for (const l16 of o14) if (l16 && l16.length > 1) {
    const o15 = [];
    for (const r19 of l16) {
      let [l17, e15] = r19;
      l17 -= n18.x, e15 -= n18.y, l17 *= f10, e15 *= f10, l17 += i10 - 0.5, e15 += i10 - 0.5, t14.asFill ? o15.push([l17, e15]) : o15.push([Math.round(l17), Math.round(e15)]);
    }
    if (t14.asFill) {
      const t15 = o15.length - 1;
      o15[0][0] === o15[t15][0] && o15[0][1] === o15[t15][1] || o15.push(o15[0]);
    }
    y6.push(o15);
  }
  const x5 = m7(y6, a16, h8, i10);
  return t14.asFill && u12(y6, a16, h8, i10, x5), [c11(x5, i10), a16, h8];
}
function m7(t14, o14, n18, r18) {
  const l16 = o14 * n18, e15 = new Array(l16), s13 = r18 * r18 + 1;
  for (let i10 = 0; i10 < l16; ++i10) e15[i10] = s13;
  for (const i10 of t14) {
    const t15 = i10.length;
    for (let l17 = 1; l17 < t15; ++l17) {
      const t16 = i10[l17 - 1], s14 = i10[l17];
      let f10, a16, m8, u13;
      t16[0] < s14[0] ? (f10 = t16[0], a16 = s14[0]) : (f10 = s14[0], a16 = t16[0]), t16[1] < s14[1] ? (m8 = t16[1], u13 = s14[1]) : (m8 = s14[1], u13 = t16[1]);
      let c13 = Math.floor(f10) - r18, h8 = Math.floor(a16) + r18, y6 = Math.floor(m8) - r18, x5 = Math.floor(u13) + r18;
      c13 < 0 && (c13 = 0), h8 > o14 && (h8 = o14), y6 < 0 && (y6 = 0), x5 > n18 && (x5 = n18);
      const M4 = s14[0] - t16[0], g10 = s14[1] - t16[1], p6 = M4 * M4 + g10 * g10;
      for (let r19 = c13; r19 < h8; r19++) for (let l18 = y6; l18 < x5; l18++) {
        let i11, f11, a17 = (r19 - t16[0]) * M4 + (l18 - t16[1]) * g10;
        a17 < 0 ? (i11 = t16[0], f11 = t16[1]) : a17 > p6 ? (i11 = s14[0], f11 = s14[1]) : (a17 /= p6, i11 = t16[0] + a17 * M4, f11 = t16[1] + a17 * g10);
        const m9 = (r19 - i11) * (r19 - i11) + (l18 - f11) * (l18 - f11), u14 = (n18 - l18 - 1) * o14 + r19;
        m9 < e15[u14] && (e15[u14] = m9);
      }
    }
  }
  for (let i10 = 0; i10 < l16; ++i10) e15[i10] = Math.sqrt(e15[i10]);
  return e15;
}
function u12(t14, o14, n18, r18, l16) {
  for (const e15 of t14) {
    const t15 = e15.length;
    for (let s13 = 1; s13 < t15; ++s13) {
      const t16 = e15[s13 - 1], i10 = e15[s13];
      let f10, a16, m8, u13;
      t16[0] < i10[0] ? (f10 = t16[0], a16 = i10[0]) : (f10 = i10[0], a16 = t16[0]), t16[1] < i10[1] ? (m8 = t16[1], u13 = i10[1]) : (m8 = i10[1], u13 = t16[1]);
      let c13 = Math.floor(f10), h8 = Math.floor(a16) + 1, y6 = Math.floor(m8), x5 = Math.floor(u13) + 1;
      c13 < r18 && (c13 = r18), h8 > o14 - r18 && (h8 = o14 - r18), y6 < r18 && (y6 = r18), x5 > n18 - r18 && (x5 = n18 - r18);
      for (let e16 = y6; e16 < x5; ++e16) {
        if (t16[1] > e16 == i10[1] > e16) continue;
        const s14 = (n18 - e16 - 1) * o14;
        for (let o15 = c13; o15 < h8; ++o15) o15 < (i10[0] - t16[0]) * (e16 - t16[1]) / (i10[1] - t16[1]) + t16[0] && (l16[s14 + o15] = -l16[s14 + o15]);
        for (let t17 = r18; t17 < c13; ++t17) l16[s14 + t17] = -l16[s14 + t17];
      }
    }
  }
}
function c11(o14, n18) {
  const r18 = 2 * n18, l16 = o14.length, e15 = new Uint8Array(4 * l16);
  for (let s13 = 0; s13 < l16; ++s13) {
    const n19 = 0.5 - o14[s13] / r18;
    o6(n19, e15, 4 * s13);
  }
  return e15;
}

// node_modules/@arcgis/core/symbols/cim/effects/CIMEffectHelper.js
var c12 = 96 / 72;
var f9 = class {
  static executeEffects(t14, s13, r18, f10) {
    const p6 = a4(s13), u13 = c12;
    let i10 = new s7(p6);
    for (const e15 of t14) {
      const t15 = A3(e15);
      t15 && (i10 = t15.execute(i10, e15, u13, r18, f10));
    }
    return i10;
  }
  static next(t14) {
    const s13 = t14.next();
    return x2(s13), s13;
  }
  static applyEffects(e15, r18, c13) {
    if (!e15) return r18;
    let f10 = new s7(r18);
    for (const t14 of e15) {
      const s13 = A3(t14);
      s13 && (f10 = s13.execute(f10, t14, 1, null, c13));
    }
    let p6, u13 = null;
    for (; p6 = f10.next(); ) u13 ? f2(u13) ? f2(p6) && u13.paths.push(...p6.paths) : y(u13) && y(p6) && u13.rings.push(...p6.rings) : u13 = p6;
    return u13;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/grouping.js
function e14(e15, o14) {
  let r18;
  if ("string" == typeof e15) r18 = c(e15 + `-seed(${o14})`);
  else {
    let t14 = 12;
    r18 = e15 ^ o14;
    do {
      r18 = 107 * (r18 >> 8 ^ r18) + t14 | 0;
    } while (0 != --t14);
  }
  return (1 + r18 / (1 << 31)) / 2;
}
function o13(t14) {
  return Math.floor(e14(t14, r17) * n17);
}
var r17 = 53290320;
var n17 = 10;

// node_modules/@arcgis/core/symbols/cim/cimAnalyzer.js
var H = s.getLogger("esri.symbols.cim.cimAnalyzer");
function Y4(e15) {
  switch (e15) {
    case "Butt":
      return e4.BUTT;
    case "Square":
      return e4.SQUARE;
    default:
      return e4.ROUND;
  }
}
function T3(e15) {
  switch (e15) {
    case "Bevel":
      return n.BEVEL;
    case "Miter":
      return n.MITER;
    default:
      return n.ROUND;
  }
}
function $3(e15, t14, i10, o14) {
  let r18;
  e15[t14] ? r18 = e15[t14] : (r18 = {}, e15[t14] = r18), r18[i10] = o14;
}
function E2(e15) {
  const t14 = e15.markerPlacement;
  return t14 && t14.angleToLine ? i.MAP : i.SCREEN;
}
async function j5(e15, t14, i10, o14, n18) {
  const l16 = o14 ?? [];
  if (!e15) return l16;
  let a16, s13;
  const c13 = {};
  if ("CIMSymbolReference" !== e15.type) return H.error("Expect cim type to be 'CIMSymbolReference'"), l16;
  if (a16 = e15.symbol, s13 = e15.primitiveOverrides, s13) {
    const e16 = [];
    for (const i11 of s13) {
      const o15 = i11.valueExpressionInfo;
      if (o15 && t14) {
        const n19 = o15.expression, l17 = o7(n19, t14.spatialReference, t14.fields).then((e17) => {
          t(e17) || $3(c13, i11.primitiveName, i11.propertyName, e17);
        });
        e16.push(l17);
      } else null != i11.value && $3(c13, i11.primitiveName, i11.propertyName, i11.value);
    }
    e16.length > 0 && await Promise.all(e16);
  }
  const p6 = [];
  switch (ie2.fetchResources(a16, i10, p6), p6.length > 0 && await Promise.all(p6), a16 == null ? void 0 : a16.type) {
    case "CIMPointSymbol":
    case "CIMLineSymbol":
    case "CIMPolygonSymbol":
      F2(a16, s13, c13, t14, l16, i10, !!n18);
  }
  return l16;
}
function F2(e15, t14, i10, o14, r18, n18, l16) {
  if (!e15) return;
  const a16 = e15.symbolLayers;
  if (!a16) return;
  const s13 = e15.effects;
  let f10 = i.SCREEN;
  const c13 = ie2.getSize(e15) ?? 0;
  "CIMPointSymbol" === e15.type && "Map" === e15.angleAlignment && (f10 = i.MAP);
  let y6 = a16.length;
  for (; y6--; ) {
    const m8 = a16[y6];
    if (!m8 || false === m8.enable) continue;
    let u13;
    s13 && s13.length && (u13 = [...s13]);
    const h8 = m8.effects;
    h8 && h8.length && (s13 ? u13.push(...h8) : u13 = [...h8]);
    const g10 = [];
    let d4;
    se.findEffectOverrides(u13, t14, g10), d4 = g10.length > 0 ? se2(u13, g10, i10, o14) : u13;
    const S3 = [];
    switch (se.findApplicableOverrides(m8, t14, S3), m8.type) {
      case "CIMSolidFill":
        U4(m8, d4, i10, S3, o14, r18);
        break;
      case "CIMPictureFill":
        D(m8, d4, i10, S3, o14, n18, r18);
        break;
      case "CIMHatchFill":
        W3(m8, d4, i10, S3, o14, r18);
        break;
      case "CIMGradientFill":
        G4(m8, d4, i10, S3, o14, r18);
        break;
      case "CIMSolidStroke":
        B3(m8, d4, i10, S3, o14, r18, "CIMPolygonSymbol" === e15.type, c13);
        break;
      case "CIMPictureStroke":
        V2(m8, d4, i10, S3, o14, r18, "CIMPolygonSymbol" === e15.type, c13);
        break;
      case "CIMGradientStroke":
        q4(m8, d4, i10, S3, o14, r18, "CIMPolygonSymbol" === e15.type, c13);
        break;
      case "CIMCharacterMarker":
        if (K4(m8, d4, i10, S3, o14, r18)) break;
        break;
      case "CIMPictureMarker":
        if (K4(m8, d4, i10, S3, o14, r18)) break;
        "CIMLineSymbol" === e15.type && (f10 = E2(m8)), Q3(m8, d4, i10, S3, o14, n18, r18, f10, c13);
        break;
      case "CIMVectorMarker":
        if (K4(m8, d4, i10, S3, o14, r18)) break;
        "CIMLineSymbol" === e15.type && (f10 = E2(m8)), _4(m8, d4, i10, S3, o14, r18, n18, f10, c13, l16);
        break;
      default:
        H.error("Cannot analyze CIM layer", m8.type);
    }
  }
}
function U4(e15, t14, i10, o14, r18, n18) {
  const l16 = e15.primitiveName, a16 = a3(e15.color), [f10, c13] = ue2(o14, l16, t14, null, null), m8 = c(JSON.stringify(e15) + c13).toString();
  n18.push({ type: "fill", templateHash: m8, materialHash: f10 ? () => m8 : m8, cim: e15, materialOverrides: null, colorLocked: !!e15.colorLocked, color: le2(l16, i10, "Color", r18, a16, ne3), height: 0, angle: 0, offsetX: 0, offsetY: 0, scaleX: 1, effects: t14, applyRandomOffset: false, sampleAlphaOnly: true });
}
function D(e15, t14, i10, o14, r18, l16, a16) {
  const f10 = e15.primitiveName, c13 = p2(e15), [m8, p6] = ue2(o14, f10, t14, null, null), u13 = c(JSON.stringify(e15) + p6).toString(), y6 = c(`${e15.url}${JSON.stringify(e15.colorSubstitutions)}`).toString();
  let h8 = b(e15.scaleX);
  if ("width" in e15 && "number" == typeof e15.width) {
    const t15 = e15.width;
    let i11 = 1;
    const o15 = l16.getResource(e15.url);
    r(o15) && (i11 = o15.width / o15.height), h8 /= i11 * (e15.height / t15);
  }
  a16.push({ type: "fill", templateHash: u13, materialHash: m8 ? () => y6 : y6, cim: e15, materialOverrides: null, colorLocked: !!e15.colorLocked, effects: t14, color: le2(f10, i10, "TintColor", r18, c13, ne3), height: le2(f10, i10, "Height", r18, e15.height), scaleX: le2(f10, i10, "ScaleX", r18, h8), angle: le2(f10, i10, "Rotation", r18, b(e15.rotation)), offsetX: le2(f10, i10, "OffsetX", r18, b(e15.offsetX)), offsetY: le2(f10, i10, "OffsetY", r18, b(e15.offsetY)), url: e15.url, applyRandomOffset: false, sampleAlphaOnly: false });
}
function W3(e15, t14, i10, o14, r18, n18) {
  var _a, _b;
  const l16 = ["Rotation", "OffsetX", "OffsetY"], a16 = o14.filter((t15) => t15.primitiveName !== e15.primitiveName || !l16.includes(t15.propertyName)), f10 = e15.primitiveName;
  let [c13, m8] = ue2(o14, f10, t14, null, null);
  const p6 = c(JSON.stringify(e15) + m8).toString(), u13 = c(`${e15.separation}${JSON.stringify(e15.lineSymbol)}`).toString();
  let y6 = { r: 255, g: 255, b: 255, a: 1 }, h8 = false;
  const g10 = (_b = (_a = e15.lineSymbol) == null ? void 0 : _a.symbolLayers) == null ? void 0 : _b.find((e16) => {
    var _a2;
    return "CIMSolidStroke" === e16.type && null != ((_a2 = i10[e16.primitiveName]) == null ? void 0 : _a2.Color);
  });
  if (g10) {
    y6 = a3(g10.color), y6 = le2(g10.primitiveName, i10, "Color", r18, y6, ne3);
    const e16 = "function" == typeof y6;
    c13 = c13 || e16, h8 = null != g10.color || e16;
  }
  n18.push({ type: "fill", templateHash: p6, materialHash: c13 ? me2(u13, i10, a16, r18) : u13, cim: e15, materialOverrides: a16, colorLocked: !!e15.colorLocked, effects: t14, color: y6, height: le2(f10, i10, "Separation", r18, e15.separation), scaleX: 1, angle: le2(f10, i10, "Rotation", r18, b(e15.rotation)), offsetX: le2(f10, i10, "OffsetX", r18, b(e15.offsetX)), offsetY: le2(f10, i10, "OffsetY", r18, b(e15.offsetY)), applyRandomOffset: false, sampleAlphaOnly: true, hasUnresolvedReplacementColor: !h8 });
}
function G4(e15, t14, i10, o14, r18, n18) {
  const l16 = e15.primitiveName, [a16, f10] = ue2(o14, l16, t14, null, null), c13 = c(JSON.stringify(e15) + f10).toString();
  n18.push({ type: "fill", templateHash: c13, materialHash: a16 ? me2(c13, i10, o14, r18) : c13, cim: e15, materialOverrides: null, colorLocked: !!e15.colorLocked, effects: t14, color: { r: 128, g: 128, b: 128, a: 1 }, height: 0, angle: 0, offsetX: 0, offsetY: 0, scaleX: 1, applyRandomOffset: false, sampleAlphaOnly: false });
}
function B3(e15, t14, i10, o14, r18, n18, l16, a16) {
  const f10 = e15.primitiveName, c13 = a3(e15.color), m8 = null != e15.width ? e15.width : 4, p6 = Y4(e15.capStyle), u13 = T3(e15.joinStyle), y6 = e15.miterLimit, [h8, g10] = ue2(o14, f10, t14, null, null), d4 = c(JSON.stringify(e15) + g10).toString();
  let S3, N3;
  if (t14 && t14 instanceof Array && t14.length > 0) {
    const e16 = t14[t14.length - 1];
    if ("CIMGeometricEffectDashes" === e16.type && "NoConstraint" === e16.lineDashEnding && null === e16.offsetAlongLine) {
      const e17 = (t14 = [...t14]).pop();
      S3 = e17.dashTemplate, N3 = e17.scaleDash;
    }
  }
  n18.push({ type: "line", templateHash: d4, materialHash: h8 ? () => d4 : d4, cim: e15, materialOverrides: null, isOutline: l16, colorLocked: !!e15.colorLocked, effects: t14, color: le2(f10, i10, "Color", r18, c13, ne3), width: le2(f10, i10, "Width", r18, m8), cap: le2(f10, i10, "CapStyle", r18, p6), join: le2(f10, i10, "JoinStyle", r18, u13), miterLimit: y6 && le2(f10, i10, "MiterLimit", r18, y6), referenceWidth: a16, zOrder: re2(e15.name), dashTemplate: S3, scaleDash: N3, sampleAlphaOnly: true });
}
function V2(e15, t14, i10, o14, r18, n18, l16, a16) {
  const f10 = c(`${e15.url}${JSON.stringify(e15.colorSubstitutions)}`).toString(), c13 = e15.primitiveName, m8 = p2(e15), p6 = null != e15.width ? e15.width : 4, u13 = Y4(e15.capStyle), y6 = T3(e15.joinStyle), h8 = e15.miterLimit, [g10, d4] = ue2(o14, c13, t14, null, null), S3 = c(JSON.stringify(e15) + d4).toString();
  n18.push({ type: "line", templateHash: S3, materialHash: g10 ? () => f10 : f10, cim: e15, materialOverrides: null, isOutline: l16, colorLocked: !!e15.colorLocked, effects: t14, color: le2(c13, i10, "TintColor", r18, m8, ne3), width: le2(c13, i10, "Width", r18, p6), cap: le2(c13, i10, "CapStyle", r18, u13), join: le2(c13, i10, "JoinStyle", r18, y6), miterLimit: h8 && le2(c13, i10, "MiterLimit", r18, h8), referenceWidth: a16, zOrder: re2(e15.name), dashTemplate: null, scaleDash: false, url: e15.url, sampleAlphaOnly: false });
}
function q4(e15, t14, i10, o14, r18, n18, l16, a16) {
  const f10 = e15.primitiveName, c13 = null != e15.width ? e15.width : 4, m8 = Y4(e15.capStyle), p6 = T3(e15.joinStyle), u13 = e15.miterLimit, [y6, h8] = ue2(o14, f10, t14, null, null), g10 = c(JSON.stringify(e15) + h8).toString();
  n18.push({ type: "line", templateHash: g10, materialHash: y6 ? me2(g10, i10, o14, r18) : g10, cim: e15, materialOverrides: null, isOutline: l16, colorLocked: !!e15.colorLocked, effects: t14, color: { r: 128, g: 128, b: 128, a: 1 }, width: le2(f10, i10, "Width", r18, c13), cap: le2(f10, i10, "CapStyle", r18, m8), join: le2(f10, i10, "JoinStyle", r18, p6), miterLimit: u13 && le2(f10, i10, "MiterLimit", r18, u13), referenceWidth: a16, zOrder: re2(e15.name), dashTemplate: null, scaleDash: false, sampleAlphaOnly: false });
}
function K4(e15, t14, i10, o14, r18, n18) {
  const { markerPlacement: l16, type: f10 } = e15;
  if (!l16 || "CIMMarkerPlacementInsidePolygon" !== l16.type) return false;
  if ("CIMVectorMarker" === f10 || "CIMPictureMarker" === f10) {
    const i11 = e15.primitiveName;
    if (i11) {
      const [e16, r20] = ue2(o14, i11, t14, null, null);
      if (e16) return false;
    }
    const r19 = l16.primitiveName;
    if (r19) {
      const [e16, i12] = ue2(o14, r19, t14, null, null);
      if (e16) return false;
    }
    if ("CIMVectorMarker" === f10) {
      const { markerGraphics: t15 } = e15;
      if (t15) for (const e16 of t15) {
        const { symbol: t16 } = e16;
        if ("CIMPolygonSymbol" === (t16 == null ? void 0 : t16.type) && t16.symbolLayers) {
          const { symbolLayers: e17 } = t16;
          for (const t17 of e17) if ("CIMSolidStroke" === t17.type) return false;
        }
      }
    } else {
      const { animatedSymbolProperties: t15 } = e15;
      if (t15) return false;
    }
  }
  const c13 = l16, m8 = Math.abs(c13.stepX), p6 = Math.abs(c13.stepY);
  if (0 === m8 || 0 === p6) return true;
  const u13 = ["Rotation", "OffsetX", "OffsetY"], y6 = o14.filter((t15) => t15.primitiveName !== e15.primitiveName || !u13.includes(t15.propertyName)), h8 = "url" in e15 && "string" == typeof e15.url ? e15.url : void 0, [g10, d4] = ue2(o14, c13.primitiveName, t14, null, null), S3 = c(JSON.stringify(e15) + d4).toString();
  let v2, O4, k5 = null;
  if ("Random" === l16.gridType) {
    const e16 = e3(dt), t15 = Math.max(Math.floor(e16 / m8), 1), i11 = Math.max(Math.floor(e16 / p6), 1);
    v2 = p6 * i11, k5 = (e17) => e17 ? e17 * i11 : 0;
    O4 = t15 * m8 / v2;
  } else l16.shiftOddRows ? (v2 = 2 * p6, k5 = (e16) => e16 ? 2 * e16 : 0, O4 = m8 / p6 * 0.5) : (v2 = p6, k5 = null, O4 = m8 / p6);
  const C5 = p2(e15);
  return n18.push({ type: "fill", templateHash: S3, materialHash: g10 ? me2(S3, i10, y6, r18) : S3, cim: e15, materialOverrides: y6, colorLocked: !!e15.colorLocked, effects: t14, color: le2(c13.primitiveName, i10, "TintColor", r18, C5, ne3), height: le2(c13.primitiveName, i10, "StepY", r18, v2, k5), scaleX: O4, angle: le2(c13.primitiveName, i10, "GridAngle", r18, c13.gridAngle), offsetX: le2(c13.primitiveName, i10, "OffsetX", r18, b(c13.offsetX)), offsetY: le2(c13.primitiveName, i10, "OffsetY", r18, b(c13.offsetY)), url: h8, applyRandomOffset: "Random" === l16.gridType, sampleAlphaOnly: !h8, hasUnresolvedReplacementColor: true }), true;
}
function Q3(e15, t14, i10, o14, r18, l16, a16, f10, c13) {
  const m8 = e15.primitiveName, p6 = b(e15.size);
  let u13 = b(e15.scaleX, 1);
  const y6 = b(e15.rotation), h8 = b(e15.offsetX), g10 = b(e15.offsetY), d4 = p2(e15), S3 = c(`${e15.url}${JSON.stringify(e15.colorSubstitutions)}${JSON.stringify(e15.animatedSymbolProperties)}`).toString(), v2 = fe2(e15.markerPlacement, o14, i10, r18), O4 = ce2(e15.animatedSymbolProperties, o14, i10, r18), [k5, C5] = ue2(o14, m8, t14, v2, O4), P5 = c(JSON.stringify(e15) + C5).toString(), M4 = e15.anchorPoint ?? { x: 0, y: 0 };
  if ("width" in e15 && "number" == typeof e15.width) {
    const t15 = e15.width;
    let i11 = 1;
    const o15 = l16.getResource(e15.url);
    r(o15) && (i11 = o15.width / o15.height), u13 /= i11 * (p6 / t15);
  }
  function L3(e16, t15) {
    return r(O4) ? t7(O4, e16, t15) : null;
  }
  const I3 = e15.animatedSymbolProperties && true === e15.animatedSymbolProperties.randomizeStartTime ? (e16, t15, i11, o15) => {
    const r19 = o13(o15 ?? 0), n18 = L3(e16, t15);
    return S3 + `-MATERIALGROUP(${r19})-ASP(${JSON.stringify(n18)})`;
  } : k5 ? (e16, t15) => {
    const i11 = L3(e16, t15);
    return S3 + `-ASP(${JSON.stringify(i11)})`;
  } : S3;
  a16.push({ type: "marker", templateHash: P5, materialHash: I3, cim: e15, materialOverrides: null, colorLocked: !!e15.colorLocked, effects: t14, scaleSymbolsProportionally: false, alignment: f10, size: le2(m8, i10, "Size", r18, p6), scaleX: le2(m8, i10, "ScaleX", r18, u13), rotation: le2(m8, i10, "Rotation", r18, y6), offsetX: le2(m8, i10, "OffsetX", r18, h8), offsetY: le2(m8, i10, "OffsetY", r18, g10), color: le2(m8, i10, "TintColor", r18, d4, ne3), anchorPoint: { x: M4.x, y: -M4.y }, isAbsoluteAnchorPoint: "Relative" !== e15.anchorPointUnits, outlineColor: { r: 0, g: 0, b: 0, a: 0 }, outlineWidth: 0, frameHeight: 0, rotateClockwise: !!e15.rotateClockwise, referenceSize: c13, sizeRatio: 1, markerPlacement: v2, url: e15.url, animatedSymbolProperties: O4 });
}
function _4(e15, t14, i10, o14, r18, n18, l16, a16, s13, f10) {
  const c13 = e15.markerGraphics;
  if (!c13) return;
  let m8 = 0;
  if (e15.scaleSymbolsProportionally) {
    const t15 = e15.frame;
    t15 && (m8 = t15.ymax - t15.ymin);
  }
  const p6 = fe2(e15.markerPlacement, o14, i10, r18);
  for (const u13 of c13) if (u13) {
    const c14 = u13.symbol;
    if (!c14) continue;
    switch (c14.type) {
      case "CIMPointSymbol":
      case "CIMLineSymbol":
      case "CIMPolygonSymbol":
        ee2(e15, t14, p6, null, u13, o14, i10, r18, n18, l16, a16, s13, m8, !!f10);
        break;
      case "CIMTextSymbol":
        Z4(e15, t14, p6, u13, i10, o14, r18, n18, a16, s13, m8);
    }
  }
}
function Z4(e15, i10, o14, r18, n18, l16, a16, f10, c13, m8, u13) {
  const y6 = [];
  se.findApplicableOverrides(r18, l16, y6);
  const h8 = r18.geometry;
  if (!("x" in h8) || !("y" in h8)) return;
  const g10 = r18.symbol, d4 = k2(g10), S3 = g3(g10.fontStyleName), N3 = s4(g10.fontFamilyName);
  g10.font = { family: N3, decoration: d4, ...S3 };
  const X2 = e15.frame, A5 = h8.x - 0.5 * (X2.xmin + X2.xmax), x5 = h8.y - 0.5 * (X2.ymin + X2.ymax), J3 = e15.size / u13, H2 = e15.primitiveName, Y5 = b(g10.height) * J3, T4 = b(g10.angle), $4 = b(e15.offsetX) + (b(g10.offsetX) + A5) * J3, E3 = b(e15.offsetY) + (b(g10.offsetY) + x5) * J3, j6 = a3(P2(g10));
  let F3 = a3(w3(g10)), U5 = L(g10) ?? 0;
  U5 || (F3 = a3(P2(g10.haloSymbol)), g10.haloSize && (U5 = g10.haloSize * J3));
  let D2 = null, W4 = null, G5 = 0;
  if (g10.callout && "CIMBackgroundCallout" === g10.callout.type) {
    const e16 = g10.callout;
    if (e16.backgroundSymbol) {
      const t14 = e16.backgroundSymbol.symbolLayers;
      if (t14) for (const e17 of t14) "CIMSolidFill" === e17.type ? D2 = a3(e17.color) : "CIMSolidStroke" === e17.type && (W4 = a3(e17.color), G5 = b(e17.width));
    }
  }
  const [B4, V3] = ue2(l16, H2, i10, o14, null), q5 = JSON.stringify(e15.effects) + Number(e15.colorLocked).toString() + JSON.stringify(e15.anchorPoint) + e15.anchorPointUnits + JSON.stringify(e15.markerPlacement) + e15.size.toString(), K5 = c(JSON.stringify(r18) + q5 + V3).toString();
  let Q4 = le2(r18.primitiveName, n18, "TextString", a16, r18.textString ?? "", s5, g10.textCase);
  if (null == Q4) return;
  const { fontStyleName: _5 } = g10, Z5 = N3 + (_5 ? "-" + _5.toLowerCase() : "-regular"), ee3 = Z5;
  "string" == typeof Q4 && Q4.includes("[") && g10.fieldMap && (Q4 = c3(g10.fieldMap, Q4, g10.textCase)), f10.push({ type: "text", templateHash: K5, materialHash: B4 || "function" == typeof Q4 || Q4.match(/\[(.*?)\]/) ? (e16, t14, i11) => ee3 + "-" + t7(Q4, e16, t14, i11) : ee3 + "-" + c(Q4), cim: g10, materialOverrides: null, colorLocked: !!e15.colorLocked, effects: i10, alignment: c13, anchorPoint: { x: e15.anchorPoint ? e15.anchorPoint.x : 0, y: e15.anchorPoint ? e15.anchorPoint.y : 0 }, isAbsoluteAnchorPoint: "Relative" !== e15.anchorPointUnits, fontName: Z5, decoration: d4, weight: le2(H2, n18, "Weight", a16, S3.weight), style: le2(H2, n18, "Size", a16, S3.style), size: le2(H2, n18, "Size", a16, Y5), angle: le2(H2, n18, "Rotation", a16, T4), offsetX: le2(H2, n18, "OffsetX", a16, $4), offsetY: le2(H2, n18, "OffsetY", a16, E3), horizontalAlignment: x(g10.horizontalAlignment), verticalAlignment: F(g10.verticalAlignment), text: Q4, color: j6, outlineColor: F3, outlineSize: U5, backgroundColor: D2, borderLineColor: W4, borderLineWidth: G5, referenceSize: m8, sizeRatio: 1, markerPlacement: o14 });
}
function ee2(e15, t14, i10, o14, r18, l16, a16, f10, c13, m8, p6, u13, y6, h8) {
  const g10 = r18.symbol, N3 = g10.symbolLayers;
  if (!N3) return;
  if (h8) return void ie3(e15, t14, i10, o14, r18, a16, l16, f10, c13, m8, p6, u13, y6);
  let O4 = N3.length;
  if (ye2(N3)) return void te2(e15, t14, i10, o14, r18, N3, l16, a16, f10, c13, p6, u13, y6);
  const k5 = f9.applyEffects(g10.effects, r18.geometry, m8.geometryEngine);
  if (k5) for (; O4--; ) {
    const h9 = N3[O4];
    if (h9 && false !== h9.enable) switch (h9.type) {
      case "CIMSolidFill":
      case "CIMSolidStroke": {
        const g11 = f9.applyEffects(h9.effects, k5, m8.geometryEngine), N4 = i9(g11);
        if (!N4) continue;
        const O5 = "Relative" !== e15.anchorPointUnits, [L3, I3, w6] = f8(N4, e15.frame, e15.size, e15.anchorPoint, O5), z3 = "CIMSolidFill" === h9.type, R3 = { type: "sdf", geom: g11, asFill: z3 }, A5 = e15.primitiveName, x5 = b(e15.size) ?? 10, J3 = b(e15.rotation), H2 = b(e15.offsetX), Y5 = b(e15.offsetY), T4 = h9.path, $4 = h9.primitiveName, E3 = a3(z3 ? P2(h9) : w3(h9)), j6 = z3 ? { r: 0, g: 0, b: 0, a: 0 } : a3(w3(h9)), F3 = L(h9) ?? 0;
        if (!z3 && !F3) break;
        let U5 = false, D2 = "";
        for (const e16 of l16) e16.primitiveName !== $4 && e16.primitiveName !== A5 || (void 0 !== e16.value ? D2 += `-${e16.primitiveName}-${e16.propertyName}-${JSON.stringify(e16.value)}` : e16.valueExpressionInfo && (U5 = true));
        (r(t14) && "function" == typeof t14 || r(i10) && "function" == typeof i10) && (U5 = true);
        const W4 = JSON.stringify({ ...e15, markerGraphics: null }), G5 = c(JSON.stringify(R3) + T4).toString(), B4 = { type: "marker", templateHash: c(JSON.stringify(r18) + JSON.stringify(h9) + W4 + D2).toString(), materialHash: U5 ? () => G5 : G5, cim: R3, materialOverrides: null, colorLocked: !!e15.colorLocked, effects: t14, scaleSymbolsProportionally: !!e15.scaleSymbolsProportionally, alignment: p6, anchorPoint: { x: I3, y: w6 }, isAbsoluteAnchorPoint: O5, size: le2(e15.primitiveName, a16, "Size", f10, x5), rotation: le2(e15.primitiveName, a16, "Rotation", f10, J3), offsetX: le2(e15.primitiveName, a16, "OffsetX", f10, H2), offsetY: le2(e15.primitiveName, a16, "OffsetY", f10, Y5), scaleX: 1, frameHeight: y6, rotateClockwise: !!e15.rotateClockwise, referenceSize: u13, sizeRatio: L3, color: le2($4, a16, "Color", f10, E3, ne3), outlineColor: le2($4, a16, "Color", f10, j6, ne3), outlineWidth: le2($4, a16, "Width", f10, F3), markerPlacement: i10, animatedSymbolProperties: o14, path: T4 };
        c13.push(B4);
        break;
      }
      default:
        ie3(e15, t14, i10, o14, r18, a16, l16, f10, c13, m8, p6, u13, y6);
    }
  }
}
function te2(e15, t14, i10, o14, r18, l16, a16, f10, c13, m8, p6, u13, y6) {
  const h8 = r18.geometry, g10 = l16[0], N3 = l16[1], O4 = i9(h8);
  if (!O4) return;
  const k5 = "Relative" !== e15.anchorPointUnits, [L3, I3, w6] = f8(O4, e15.frame, e15.size, e15.anchorPoint, k5), z3 = { type: "sdf", geom: h8, asFill: true }, R3 = e15.primitiveName, X2 = b(e15.size), A5 = b(e15.rotation), x5 = b(e15.offsetX), J3 = b(e15.offsetY), H2 = N3.path, Y5 = N3.primitiveName, T4 = g10.primitiveName, $4 = a3(P2(N3)), E3 = a3(w3(g10)), j6 = L(g10) ?? 0;
  let F3 = false, U5 = "";
  for (const n18 of a16) n18.primitiveName !== Y5 && n18.primitiveName !== T4 && n18.primitiveName !== R3 || (void 0 !== n18.value ? U5 += `-${n18.primitiveName}-${n18.propertyName}-${JSON.stringify(n18.value)}` : n18.valueExpressionInfo && (F3 = true));
  r(i10) && "function" == typeof i10 && (F3 = true);
  const D2 = JSON.stringify({ ...e15, markerGraphics: null }), W4 = c(JSON.stringify(z3) + H2).toString(), G5 = { type: "marker", templateHash: c(JSON.stringify(r18) + JSON.stringify(N3) + JSON.stringify(g10) + D2 + U5).toString(), materialHash: F3 ? () => W4 : W4, cim: z3, materialOverrides: null, colorLocked: !!e15.colorLocked, effects: t14, scaleSymbolsProportionally: !!e15.scaleSymbolsProportionally, alignment: p6, anchorPoint: { x: I3, y: w6 }, isAbsoluteAnchorPoint: k5, size: le2(e15.primitiveName, f10, "Size", c13, X2), rotation: le2(e15.primitiveName, f10, "Rotation", c13, A5), offsetX: le2(e15.primitiveName, f10, "OffsetX", c13, x5), offsetY: le2(e15.primitiveName, f10, "OffsetY", c13, J3), scaleX: 1, frameHeight: y6, rotateClockwise: !!e15.rotateClockwise, referenceSize: u13, sizeRatio: L3, color: le2(Y5, f10, "Color", c13, $4, ne3), outlineColor: le2(T4, f10, "Color", c13, E3, ne3), outlineWidth: le2(T4, f10, "Width", c13, j6), markerPlacement: i10, path: H2, animatedSymbolProperties: o14 };
  m8.push(G5);
}
function ie3(e15, t14, i10, o14, r18, a16, f10, c13, p6, u13, y6, h8, g10) {
  const d4 = oe3(e15, r18), S3 = ["Rotation", "OffsetX", "OffsetY"], v2 = f10.filter((t15) => t15.primitiveName !== e15.primitiveName || !S3.includes(t15.propertyName));
  let N3 = "";
  for (const n18 of f10) void 0 !== n18.value && (N3 += `-${n18.primitiveName}-${n18.propertyName}-${JSON.stringify(n18.value)}`);
  const [O4, k5, C5] = ie2.getTextureAnchor(d4, u13), P5 = e15.primitiveName, M4 = b(e15.rotation), L3 = b(e15.offsetX), I3 = b(e15.offsetY), w6 = c(JSON.stringify(d4) + N3).toString(), z3 = { type: "marker", templateHash: w6, materialHash: v2.length > 0 || r(t14) && "function" == typeof t14 ? me2(w6, a16, v2, c13) : w6, cim: d4, materialOverrides: v2, colorLocked: !!e15.colorLocked, effects: t14, scaleSymbolsProportionally: !!e15.scaleSymbolsProportionally, alignment: y6, anchorPoint: { x: O4, y: k5 }, isAbsoluteAnchorPoint: false, size: b(e15.size), rotation: le2(P5, a16, "Rotation", c13, M4), offsetX: le2(P5, a16, "OffsetX", c13, L3), offsetY: le2(P5, a16, "OffsetY", c13, I3), color: { r: 255, g: 255, b: 255, a: 1 }, outlineColor: { r: 0, g: 0, b: 0, a: 0 }, outlineWidth: 0, scaleX: 1, frameHeight: g10, rotateClockwise: !!e15.rotateClockwise, referenceSize: h8, sizeRatio: C5 / u4(e15.size), markerPlacement: i10, animatedSymbolProperties: o14, avoidSDFRasterization: true };
  p6.push(z3);
}
function oe3(e15, t14) {
  return { type: e15.type, enable: true, name: e15.name, colorLocked: e15.colorLocked, primitiveName: e15.primitiveName, anchorPoint: e15.anchorPoint, anchorPointUnits: e15.anchorPointUnits, offsetX: 0, offsetY: 0, rotateClockwise: e15.rotateClockwise, rotation: 0, size: e15.size, billboardMode3D: e15.billboardMode3D, depth3D: e15.depth3D, frame: e15.frame, markerGraphics: [t14], scaleSymbolsProportionally: e15.scaleSymbolsProportionally, respectFrame: e15.respectFrame, clippingPath: e15.clippingPath };
}
function re2(e15) {
  if (e15 && 0 === e15.indexOf("Level_")) {
    const t14 = parseInt(e15.substr(6), 10);
    if (!isNaN(t14)) return t14;
  }
  return 0;
}
function ne3(t14) {
  if (!t14 || 0 === t14.length) return null;
  const i10 = new l3(t14).toRgba();
  return { r: i10[0], g: i10[1], b: i10[2], a: i10[3] };
}
function le2(e15, t14, i10, o14, r18, n18, l16) {
  if (null == e15) return r18;
  const a16 = t14[e15];
  if (a16) {
    const e16 = a16[i10];
    if ("string" == typeof e16 || "number" == typeof e16 || e16 instanceof Array) return n18 ? n18.call(null, e16, l16) : e16;
    if (null != e16 && e16 instanceof u6 && (o14 == null ? void 0 : o14.geometryType)) return (t15, i11, a17) => {
      let s13 = i4(e16, t15, { $view: a17 }, o14.geometryType, i11);
      return null !== s13 && n18 && (s13 = n18.call(null, s13, l16)), null !== s13 ? s13 : r18;
    };
  }
  return r18;
}
function ae2(e15) {
  return e15 ? e15.charAt(0).toLowerCase() + e15.substr(1) : e15;
}
function se2(e15, t14, o14, r18) {
  for (const i10 of t14) {
    if (i10.valueExpressionInfo && (r18 == null ? void 0 : r18.geometryType)) {
      const e16 = o14[i10.primitiveName] && o14[i10.primitiveName][i10.propertyName];
      e16 instanceof u6 && (i10.fn = (t15, i11, o15) => i4(e16, t15, { $view: o15 }, r18.geometryType, i11));
    }
  }
  return (o15, r19, n18) => {
    for (const e16 of t14) e16.fn && (e16.value = e16.fn(o15, r19, n18));
    const l16 = [];
    for (let a16 of e15) {
      const e16 = a16 == null ? void 0 : a16.primitiveName;
      if (e16) {
        let o16 = false;
        for (const r20 of t14) if (r20.primitiveName === e16) {
          const e17 = ae2(r20.propertyName);
          null != r20.value && r20.value !== a16[e17] && (o16 || (a16 = p(a16), o16 = true), a16[e17] = r20.value);
        }
      }
      l16.push(a16);
    }
    return l16;
  };
}
function fe2(e15, t14, o14, r18) {
  const n18 = [];
  if (se.findApplicableOverrides(e15, t14, n18), null == e15 || 0 === n18.length) return e15;
  for (const i10 of n18) {
    if (i10.valueExpressionInfo && (r18 == null ? void 0 : r18.geometryType)) {
      const e16 = o14[i10.primitiveName] && o14[i10.primitiveName][i10.propertyName];
      e16 instanceof u6 && (i10.fn = (t15, i11, o15) => i4(e16, t15, { $view: o15 }, r18.geometryType, i11));
    }
  }
  return (t15, o15, r19) => {
    for (const e16 of n18) e16.fn && (e16.value = e16.fn(t15, o15, r19));
    const l16 = p(e15), a16 = e15.primitiveName;
    for (const e16 of n18) if (e16.primitiveName === a16) {
      const t16 = ae2(e16.propertyName);
      null != e16.value && e16.value !== l16[t16] && (l16[t16] = e16.value);
    }
    return l16;
  };
}
function ce2(e15, t14, o14, r18) {
  const n18 = [];
  if (se.findApplicableOverrides(e15, t14, n18), null == e15 || 0 === n18.length) return e15;
  for (const i10 of n18) {
    if (i10.valueExpressionInfo && (r18 == null ? void 0 : r18.geometryType)) {
      const e16 = o14[i10.primitiveName] && o14[i10.primitiveName][i10.propertyName];
      e16 instanceof u6 && (i10.fn = (t15, i11, o15) => i4(e16, t15, { $view: o15 }, r18.geometryType, i11));
    }
  }
  return (t15, o15, r19) => {
    for (const e16 of n18) e16.fn && (e16.value = e16.fn(t15, o15, r19));
    const l16 = p(e15), a16 = e15.primitiveName;
    for (const e16 of n18) if (e16.primitiveName === a16) {
      const t16 = ae2(e16.propertyName);
      if (null != e16.value) {
        const i10 = a14(e16.value, e16.propertyName);
        i10 !== l16[t16] && (l16[t16] = i10);
      }
    }
    return l16;
  };
}
function me2(e15, t14, i10, o14) {
  for (const r18 of i10) {
    if (r18.valueExpressionInfo && (o14 == null ? void 0 : o14.geometryType)) {
      const e16 = t14[r18.primitiveName] && t14[r18.primitiveName][r18.propertyName];
      e16 instanceof u6 && (r18.fn = (t15, i11, r19) => i4(e16, t15, { $view: r19 }, o14.geometryType, i11));
    }
  }
  return (t15, o15, r18) => {
    for (const e16 of i10) e16.fn && (e16.value = e16.fn(t15, o15, r18));
    return c(e15 + se.buildOverrideKey(i10)).toString();
  };
}
function pe2(e15, t14) {
  if (!t14 || 0 === t14.length) return e15;
  const o14 = p(e15);
  return se.applyOverrides(o14, t14), o14;
}
function ue2(e15, t14, i10, o14, r18) {
  let l16 = false, a16 = "";
  for (const n18 of e15) n18.primitiveName === t14 && (void 0 !== n18.value ? a16 += `-${n18.primitiveName}-${n18.propertyName}-${JSON.stringify(n18.value)}` : n18.valueExpressionInfo && (l16 = true));
  return r(i10) && "function" == typeof i10 && (l16 = true), r(o14) && "function" == typeof o14 && (l16 = true), r(r18) && "function" == typeof r18 && (l16 = true), [l16, a16];
}
var ye2 = (e15) => e15 && 2 === e15.length && e15[0].enable && e15[1].enable && "CIMSolidStroke" === e15[0].type && "CIMSolidFill" === e15[1].type && !e15[0].effects && !e15[1].effects;

export {
  i5 as i,
  a4 as a,
  g8 as g,
  S2 as S,
  f9 as f,
  t12 as t,
  s6 as s,
  O3 as O,
  V,
  W,
  Z3 as Z,
  oe2 as oe,
  ie2 as ie,
  se,
  pe,
  r16 as r,
  a15 as a2,
  e14 as e,
  o13 as o,
  j5 as j,
  pe2
};
//# sourceMappingURL=chunk-VXAO6YJP.js.map
