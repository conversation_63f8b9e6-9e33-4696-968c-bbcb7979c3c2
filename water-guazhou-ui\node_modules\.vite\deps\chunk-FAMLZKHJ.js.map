{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/hints/PointSnappingHint.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{k as t}from\"../../../../chunks/vec3.js\";import{SnappingHint as s}from\"./SnappingHint.js\";class n extends s{constructor(t,s,n){super(s,n),this.point=t}equals(s){return s instanceof n&&t(this.point,s.point)}}export{n as PointSnappingHint};\n"], "mappings": ";;;;;;;;AAIgG,IAAM,IAAN,MAAM,WAAU,EAAC;AAAA,EAAC,YAAY,GAAEA,IAAEC,IAAE;AAAC,UAAMD,IAAEC,EAAC,GAAE,KAAK,QAAM;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,WAAOA,cAAa,MAAG,EAAE,KAAK,OAAMA,GAAE,KAAK;AAAA,EAAC;AAAC;", "names": ["s", "n"]}