import {
  g
} from "./chunk-TLKX5XIJ.js";
import {
  k
} from "./chunk-MQ2IOGEF.js";
import {
  j3 as j
} from "./chunk-ETY52UBV.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o as o3
} from "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  o as o2
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  e as e2,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/rest/support/networkEnums.js
var i2 = o2()({ esriCentimeters: "centimeters", esriDecimalDegrees: "decimal-degrees", esriDecimeters: "decimeters", esriFeet: "feet", esriInches: "inches", esriKilometers: "kilometers", esriMeters: "meters", esriMiles: "miles", esriMillimeters: "millimeters", esriNauticalMiles: "nautical-miles", esriPoints: "points", esriUnknownUnits: "unknown", esriYards: "yards" });
var r3 = o2()({ esriNAUCentimeters: "centimeters", esriNAUDecimalDegrees: "decimal-degrees", esriNAUDecimeters: "decimeters", esriNAUFeet: "feet", esriNAUInches: "inches", esriNAUKilometers: "kilometers", esriNAUMeters: "meters", esriNAUMiles: "miles", esriNAUMillimeters: "millimeters", esriNAUNauticalMiles: "nautical-miles", esriNAUPoints: "points", esriNAUYards: "yards" });
var t2 = o2()({ esriNAUDays: "days", esriNAUHours: "hours", esriNAUMinutes: "minutes", esriNAUSeconds: "seconds" });
var s = o2()({ esriNAUCentimeters: "centimeters", esriNAUDecimalDegrees: "decimal-degrees", esriNAUDecimeters: "decimeters", esriNAUFeet: "feet", esriNAUInches: "inches", esriNAUKilometers: "kilometers", esriNAUMeters: "meters", esriNAUMiles: "miles", esriNAUMillimeters: "millimeters", esriNAUNauticalMiles: "nautical-miles", esriNAUPoints: "points", esriNAUYards: "yards", esriNAUDays: "days", esriNAUHours: "hours", esriNAUMinutes: "minutes", esriNAUSeconds: "seconds", esriNAUKilometersPerHour: "kilometers-per-hour", esriNAUMilesPerHour: "miles-per-hour", esriNAUUnknown: "unknown" });
var a2 = o2()({ esriDOTComplete: "complete", esriDOTCompleteNoEvents: "complete-no-events", esriDOTFeatureSets: "featuresets", esriDOTInstructionsOnly: "instructions-only", esriDOTStandard: "standard", esriDOTSummaryOnly: "summary-only" });
var o4 = o2()({ esriNAOutputLineNone: "none", esriNAOutputLineStraight: "straight", esriNAOutputLineTrueShape: "true-shape", esriNAOutputLineTrueShapeWithMeasure: "true-shape-with-measure" });
var n = o2()({ esriNAOutputPolygonNone: "none", esriNAOutputPolygonSimplified: "simplified", esriNAOutputPolygonDetailed: "detailed" });
var l2 = o2()({ esriNFSBAllowBacktrack: "allow-backtrack", esriNFSBAtDeadEndsOnly: "at-dead-ends-only", esriNFSBNoBacktrack: "no-backtrack", esriNFSBAtDeadEndsAndIntersections: "at-dead-ends-and-intersections" });
var d = o2()({ esriNATravelDirectionFromFacility: "from-facility", esriNATravelDirectionToFacility: "to-facility" });
var c = o2()({ esriNATimeOfDayNotUsed: "not-used", esriNATimeOfDayUseAsStartTime: "start", esriNATimeOfDayUseAsEndTime: "end" });
var m2 = o2()({ AUTOMOBILE: "automobile", TRUCK: "truck", WALK: "walk", OTHER: "other" });
var u = o2()({ 0: "either-side-of-vehicle", 1: "right-side-of-vehicle", 2: "left-side-of-vehicle", 3: "no-u-turn" }, { useNumericKeys: true });
var h = o2()({ 0: "stop", 1: "waypoint", 2: "break" }, { useNumericKeys: true });
var p = o2()({ 0: "ok", 1: "not-located", 2: "network-element-not-located", 3: "element-not-traversable", 4: "invalid-field-values", 5: "not-reached", 6: "time-window-violation", 7: "not-located-on-closest" }, { useNumericKeys: true });
var v2 = o2()({ 1: "right", 2: "left" }, { useNumericKeys: true });
var A = o2()({ 0: "restriction", 1: "added-cost" }, { useNumericKeys: true });
var T = o2()({ 0: "permit", 1: "restrict" }, { useNumericKeys: true });
var N = o2()({ 1: "header", 50: "arrive", 51: "depart", 52: "straight", 100: "on-ferry", 101: "off-ferry", 102: "central-fork", 103: "roundabout", 104: "u-turn", 150: "door", 151: "stairs", 152: "elevator", 153: "escalator", 154: "pedestrian-ramp", 200: "left-fork", 201: "left-ramp", 202: "clockwise-roundabout", 203: "left-handed-u-turn", 204: "bear-left", 205: "left-turn", 206: "sharp-left", 207: "left-turn-and-immediate-left-turn", 208: "left-turn-and-immediate-right-turn", 300: "right-fork", 301: "right-ramp", 302: "counter-clockwise-roundabout", 303: "right-handed-u-turn", 304: "bear-right", 305: "right-turn", 306: "sharp-right", 307: "right-turn-and-immediate-left-turn", 308: "right-turn-and-immediate-right-turn", 400: "up-elevator", 401: "up-escalator", 402: "up-stairs", 500: "down-elevator", 501: "down-escalator", 502: "down-stairs", 1e3: "general-event", 1001: "landmark", 1002: "time-zone-change", 1003: "traffic-event", 1004: "scaled-cost-barrier-event", 1005: "boundary-crossing", 1006: "restriction-violation" }, { useNumericKeys: true });
var g2 = o2()({ 0: "unknown", 1: "segment", 2: "maneuver-segment", 3: "restriction-violation", 4: "scaled-cost-barrier", 5: "heavy-traffic", 6: "slow-traffic", 7: "moderate-traffic" }, { useNumericKeys: true });
var k2 = o2()({ "NA Campus": "campus", "NA Desktop": "desktop", "NA Navigation": "navigation" });
var f = o2()({ Kilometers: "kilometers", Miles: "miles", Meters: "meters" }, { ignoreUnknown: false });
var y2 = o2()({ Minutes: "minutes", TimeAt1KPH: "time-at-1-kph", TravelTime: "travel-time", TruckMinutes: "truck-minutes", TruckTravelTime: "truck-travel-time", WalkTime: "walk-time" }, { ignoreUnknown: false });
var U = o2()({ Kilometers: "kilometers", Miles: "miles", Meters: "meters", Minutes: "minutes", TimeAt1KPH: "time-at-1-kph", TravelTime: "travel-time", TruckMinutes: "truck-minutes", TruckTravelTime: "truck-travel-time", WalkTime: "walk-time" }, { ignoreUnknown: false });
var D = o2()({ "Any Hazmat Prohibited": "any-hazmat-prohibited", "Avoid Carpool Roads": "avoid-carpool-roads", "Avoid Express Lanes": "avoid-express-lanes", "Avoid Ferries": "avoid-ferries", "Avoid Gates": "avoid-gates", "Avoid Limited Access Roads": "avoid-limited-access-roads", "Avoid Private Roads": "avoid-private-roads", "Avoid Roads Unsuitable for Pedestrians": "avoid-roads-unsuitable-for-pedestrians", "Avoid Stairways": "avoid-stairways", "Avoid Toll Roads": "avoid-toll-roads", "Avoid Toll Roads for Trucks": "avoid-toll-roads-for-trucks", "Avoid Truck Restricted Roads": "avoid-truck-restricted-roads", "Avoid Unpaved Roads": "avoid-unpaved-roads", "Axle Count Restriction": "axle-count-restriction", "Driving a Bus": "driving-a-bus", "Driving a Taxi": "driving-a-taxi", "Driving a Truck": "driving-a-truck", "Driving an Automobile": "driving-an-automobile", "Driving an Emergency Vehicle": "driving-an-emergency-vehicle", "Height Restriction": "height-restriction", "Kingpin to Rear Axle Length Restriction": "kingpin-to-rear-axle-length-restriction", "Length Restriction": "length-restriction", "Preferred for Pedestrians": "preferred-for-pedestrians", "Riding a Motorcycle": "riding-a-motorcycle", "Roads Under Construction Prohibited": "roads-under-construction-prohibited", "Semi or Tractor with One or More Trailers Prohibited": "semi-or-tractor-with-one-or-more-trailers-prohibited", "Single Axle Vehicles Prohibited": "single-axle-vehicles-prohibited", "Tandem Axle Vehicles Prohibited": "tandem-axle-vehicles-prohibited", "Through Traffic Prohibited": "through-traffic-prohibited", "Truck with Trailers Restriction": "truck-with-trailers-restriction", "Use Preferred Hazmat Routes": "use-preferred-hazmat-routes", "Use Preferred Truck Routes": "use-preferred-truck-routes", Walking: "walking", "Weight Restriction": "weight-restriction" }, { ignoreUnknown: false });
var S = o2()({ esriSpatialRelIntersects: "intersects", esriSpatialRelContains: "contains", esriSpatialRelCrosses: "crosses", esriSpatialRelEnvelopeIntersects: "envelope-intersects", esriSpatialRelIndexIntersects: "index-intersects", esriSpatialRelOverlaps: "overlaps", esriSpatialRelTouches: "touches", esriSpatialRelWithin: "within", esriSpatialRelRelation: "relation" });
var w2 = o2()({ esriGeometryPoint: "point", esriGeometryPolyline: "polyline", esriGeometryPolygon: "polygon", esriGeometryEnvelope: "envelope", esriGeometryMultipoint: "multipoint" });
var R = o2()({ esriNAUTCost: "cost", esriNAUTDescriptor: "descriptor", esriNAUTRestriction: "restriction", esriNAUTHierarchy: "hierarchy" });
var b = o2()({ esriDSTAltName: "alt-name", esriDSTArrive: "arrive", esriDSTBranch: "branch", esriDSTCrossStreet: "cross-street", esriDSTCumulativeLength: "cumulative-length", esriDSTDepart: "depart", esriDSTEstimatedArrivalTime: "estimated-arrival-time", esriDSTExit: "exit", esriDSTGeneral: "general", esriDSTLength: "length", esriDSTServiceTime: "service-time", esriDSTStreetName: "street-name", esriDSTSummary: "summary", esriDSTTime: "time", esriDSTTimeWindow: "time-window", esriDSTToward: "toward", esriDSTViolationTime: "violation-time", esriDSTWaitTime: "wait-time" });

// node_modules/@arcgis/core/rest/support/DirectionLine.js
var m3;
var u2 = m3 = class extends i(l) {
  constructor(e4) {
    super(e4), this.directionLineType = null, this.directionPointId = null, this.distance = null, this.duration = null, this.fromLevel = null, this.geometry = null, this.objectId = null, this.popupTemplate = null, this.symbol = null, this.toLevel = null, this.type = "direction-line";
  }
  static fromGraphic(e4) {
    return new m3({ directionLineType: g2.fromJSON(e4.attributes.DirectionLineType), directionPointId: e4.attributes.DirectionPointID, distance: e4.attributes.Meters, duration: e4.attributes.Minutes, fromLevel: e4.attributes.FromLevel ?? null, geometry: e4.geometry, objectId: e4.attributes.ObjectID ?? e4.attributes.__OBJECTID, popupTemplate: e4.popupTemplate, symbol: e4.symbol, toLevel: e4.attributes.ToLevel ?? null });
  }
  toGraphic() {
    const e4 = { ObjectID: e2(this.objectId), DirectionLineType: r(this.directionLineType) ? g2.toJSON(this.directionLineType) : null, DirectionPointID: e2(this.directionPointId), Meters: e2(this.distance), Minutes: e2(this.duration) };
    return r(this.fromLevel) && (e4.FromLevel = this.fromLevel), r(this.toLevel) && (e4.ToLevel = this.toLevel), new g({ geometry: this.geometry, attributes: e4, symbol: this.symbol, popupTemplate: this.popupTemplate });
  }
};
u2.fields = [{ name: "ObjectID", alias: "ObjectID", type: "esriFieldTypeOID", editable: false, nullable: false, domain: null }, { name: "DirectionLineType", alias: "Line Type", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriDirectionsLineType", codedValues: [{ name: "Unknown", code: 0 }, { name: "Segment", code: 1 }, { name: "Maneuver Segment", code: 2 }, { name: "Restriction violation", code: 3 }, { name: "Scale cost barrier crossing", code: 4 }, { name: "Heavy Traffic", code: 5 }, { name: "Slow Traffic", code: 6 }, { name: "Moderate Traffic", code: 7 }] } }, { name: "DirectionPointID", alias: "Direction Point ID", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: false }, { name: "FromLevel", alias: "Start from 3D Level", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: false }, { name: "Meters", alias: "Length in Meters", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true }, { name: "Minutes", alias: "Duration in Minutes", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true }, { name: "ToLevel", alias: "End at 3D Level", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: false }], u2.popupInfo = { title: "Direction Lines", fieldInfos: [{ fieldName: "DirectionLineType", label: "Line Type", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "Meters", label: "Length in Meters", isEditable: false, tooltip: "", visible: true, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "Minutes", label: "Duration in Minutes", isEditable: false, tooltip: "", visible: true, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "DirectionPointID", label: "Direction Point ID", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "FromLevel", label: "Start from 3D Level", isEditable: false, tooltip: "", visible: false, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "ToLevel", label: "End at 3D Level", isEditable: false, tooltip: "", visible: false, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }], description: null, showAttachments: false, mediaInfos: [] }, e([y({ type: g2.apiValues, json: { read: { source: "attributes.DirectionLineType", reader: g2.read } } })], u2.prototype, "directionLineType", void 0), e([y({ json: { read: { source: "attributes.DirectionPointID" } } })], u2.prototype, "directionPointId", void 0), e([y({ json: { read: { source: "attributes.Meters" } } })], u2.prototype, "distance", void 0), e([y({ json: { read: { source: "attributes.Minutes" } } })], u2.prototype, "duration", void 0), e([y({ json: { read: { source: "attributes.FromLevel" } } })], u2.prototype, "fromLevel", void 0), e([y({ type: m })], u2.prototype, "geometry", void 0), e([y({ json: { read: { source: "attributes.ObjectID" } } })], u2.prototype, "objectId", void 0), e([y({ type: k })], u2.prototype, "popupTemplate", void 0), e([y({ types: j })], u2.prototype, "symbol", void 0), e([y({ json: { read: { source: "attributes.ToLevel" } } })], u2.prototype, "toLevel", void 0), e([y({ readOnly: true, json: { read: false } })], u2.prototype, "type", void 0), u2 = m3 = e([a("esri.rest.support.DirectionLine")], u2);
var b2 = u2;

// node_modules/@arcgis/core/rest/support/DirectionPoint.js
var u3;
var b3 = u3 = class extends i(l) {
  constructor(e4) {
    super(e4), this.alternateName = null, this.arrivalTime = null, this.arrivalTimeOffset = null, this.azimuth = null, this.branchName = null, this.directionPointType = null, this.displayText = null, this.exitName = null, this.geometry = null, this.intersectingName = null, this.level = null, this.name = null, this.objectId = null, this.popupTemplate = null, this.sequence = null, this.shortVoiceInstruction = null, this.stopId = null, this.symbol = null, this.towardName = null, this.type = "direction-point", this.voiceInstruction = null;
  }
  readArrivalTime(e4, t3) {
    return r(t3.attributes.ArrivalTime) ? new Date(t3.attributes.ArrivalTime) : null;
  }
  static fromGraphic(e4) {
    return new u3({ alternateName: e4.attributes.AlternateName ?? null, arrivalTime: r(e4.attributes.ArrivalTime) ? new Date(e4.attributes.ArrivalTime) : null, arrivalTimeOffset: e4.attributes.ArrivalUTCOffset ?? null, azimuth: e4.attributes.Azimuth ?? null, branchName: e4.attributes.BranchName ?? null, directionPointType: N.fromJSON(e4.attributes.DirectionPointType), displayText: e4.attributes.DisplayText ?? null, exitName: e4.attributes.ExitName ?? null, geometry: e4.geometry, intersectingName: e4.attributes.IntersectingName ?? null, level: e4.attributes.Level ?? null, name: e4.attributes.Name ?? null, objectId: e4.attributes.ObjectID ?? e4.attributes.__OBJECTID, popupTemplate: e4.popupTemplate, sequence: e4.attributes.Sequence, shortVoiceInstruction: e4.attributes.ShortVoiceInstruction ?? null, stopId: e4.attributes.StopID ?? null, symbol: e4.symbol, towardName: e4.attributes.TowardName ?? null, voiceInstruction: e4.attributes.VoiceInstruction ?? null });
  }
  toGraphic() {
    const e4 = { ObjectID: e2(this.objectId), DirectionPointType: r(this.directionPointType) ? N.toJSON(this.directionPointType) : null, Sequence: e2(this.sequence), StopID: this.stopId };
    return r(this.alternateName) && (e4.AlternateName = this.alternateName), r(this.arrivalTime) && (e4.ArrivalTime = this.arrivalTime.getTime()), r(this.arrivalTimeOffset) && (e4.ArrivalUTCOffset = this.arrivalTimeOffset), r(this.azimuth) && (e4.Azimuth = this.azimuth), r(this.branchName) && (e4.BranchName = this.branchName), r(this.displayText) && (e4.DisplayText = this.displayText), r(this.exitName) && (e4.ExitName = this.exitName), r(this.intersectingName) && (e4.IntersectingName = this.intersectingName), r(this.level) && (e4.Level = this.level), r(this.name) && (e4.Name = this.name), r(this.shortVoiceInstruction) && (e4.ShortVoiceInstruction = this.shortVoiceInstruction), r(this.towardName) && (e4.TowardName = this.towardName), r(this.voiceInstruction) && (e4.VoiceInstruction = this.voiceInstruction), new g({ geometry: this.geometry, attributes: e4, symbol: this.symbol, popupTemplate: this.popupTemplate });
  }
};
b3.fields = [{ name: "ObjectID", alias: "ObjectID", type: "esriFieldTypeOID", editable: false, nullable: false, domain: null }, { name: "AlternateName", alias: "Alternative Feature Name", type: "esriFieldTypeString", length: 2048, editable: true, nullable: true, visible: true, domain: null }, { name: "ArrivalTime", alias: "Maneuver Starts at", type: "esriFieldTypeDate", length: 36, editable: true, nullable: true, visible: true }, { name: "ArrivalUTCOffset", alias: "Offset from UTC in Minutes", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "Azimuth", alias: "Azimuth", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true }, { name: "BranchName", alias: "Signpost Branch Name", type: "esriFieldTypeString", length: 2048, editable: true, nullable: true, visible: true, domain: null }, { name: "DirectionPointType", alias: "Directions Item Type", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriDirectionPointType", codedValues: [{ name: "Unknown", code: 0 }, { name: "", code: 1 }, { name: "Arrive at stop", code: 50 }, { name: "Depart at stop", code: 51 }, { name: "Go straight", code: 52 }, { name: "Take ferry", code: 100 }, { name: "Take off ferry", code: 101 }, { name: "Keep center at fork", code: 102 }, { name: "Take roundabout", code: 103 }, { name: "Make U-Turn", code: 104 }, { name: "Pass the door", code: 150 }, { name: "Take stairs", code: 151 }, { name: "", code: 152 }, { name: "Take escalator", code: 153 }, { name: "Take pedestrian ramp", code: 154 }, { name: "Keep left at fork", code: 200 }, { name: "Ramp left", code: 201 }, { name: "Take left-handed roundabout", code: 202 }, { name: "Make left-handed U-Turn", code: 203 }, { name: "Bear left", code: 204 }, { name: "Turn left", code: 205 }, { name: "Make sharp left", code: 206 }, { name: "Turn left, followed by turn left", code: 207 }, { name: "Turn left, followed by turn right", code: 208 }, { name: "Keep right at fork", code: 300 }, { name: "Ramp right", code: 301 }, { name: "Take right-handed roundabout", code: 302 }, { name: "Make right-handed U-Turn", code: 303 }, { name: "Bear right", code: 304 }, { name: "Turn right", code: 305 }, { name: "Make sharp right", code: 306 }, { name: "Turn right, followed by turn left", code: 307 }, { name: "Turn right, followed by turn right", code: 308 }, { name: "Indicates up direction of elevator", code: 400 }, { name: "Indicates up direction of escalator", code: 401 }, { name: "Take up-stairs", code: 402 }, { name: "Indicates down direction of elevator", code: 500 }, { name: "Indicates down direction of escalator", code: 501 }, { name: "Take down-stairs", code: 502 }, { name: "General event", code: 1e3 }, { name: "Landmark", code: 1001 }, { name: "Time zone change", code: 1002 }, { name: "Heavy traffic segment", code: 1003 }, { name: "Scale cost barrier crossing", code: 1004 }, { name: "Administrative Border crossing", code: 1005 }, { name: "Restriction violation", code: 1006 }] } }, { name: "DisplayText", alias: "Text to Display", type: "esriFieldTypeString", length: 2048, editable: true, nullable: true, visible: true, domain: null }, { name: "ExitName", alias: "Highway Exit Name", type: "esriFieldTypeString", length: 2048, editable: true, nullable: true, visible: true, domain: null }, { name: "IntersectingName", alias: "Intersecting Feature Name", type: "esriFieldTypeString", length: 2048, editable: true, nullable: true, visible: true, domain: null }, { name: "Level", alias: "3D Logical Level", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "Name", alias: "Primary Feature Name", type: "esriFieldTypeString", length: 2048, editable: true, nullable: true, visible: true, domain: null }, { name: "Sequence", alias: "Sequence", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "ShortVoiceInstruction", alias: "Voice Instruction", type: "esriFieldTypeString", length: 2048, editable: true, nullable: true, visible: true, domain: null }, { name: "StopID", alias: "Stop ID", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "TowardName", alias: "Signpost Toward Name", type: "esriFieldTypeString", length: 2048, editable: true, nullable: true, visible: true, domain: null }, { name: "VoiceInstruction", alias: "Voice Full Instruction", type: "esriFieldTypeString", length: 2048, editable: true, nullable: true, visible: true, domain: null }], b3.popupInfo = { title: "{DisplayText}", fieldInfos: [{ fieldName: "DirectionPointType", label: "Directions Item Type", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "DisplayText", label: "Text to Display", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "Sequence", label: "Sequence", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "StopID", label: "Stop ID", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "ArrivalTime", label: "Maneuver Starts at", isEditable: true, tooltip: "", visible: true, format: { dateFormat: "shortDateShortTime24" }, stringFieldOption: "textbox" }, { fieldName: "ArrivalUTCOffset", label: "Offset from UTC in Minutes", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "Azimuth", label: "Azimuth", isEditable: false, tooltip: "", visible: false, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "Name", label: "Primary Feature Name", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "AlternateName", label: "Alternative Feature Name", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "ExitName", label: "Highway Exit Name", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "IntersectingName", label: "Intersecting Feature Name", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "BranchName", label: "Signpost Branch Name", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "TowardName", label: "Signpost Toward Name", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "ShortVoiceInstruction", label: "Voice Instruction", isEditable: false, tooltip: "", visible: false, stringFieldOption: "textbox" }, { fieldName: "VoiceInstruction", label: "Voice Full Instruction", isEditable: false, tooltip: "", visible: false, stringFieldOption: "textbox" }], description: null, showAttachments: false, mediaInfos: [] }, e([y()], b3.prototype, "alternateName", void 0), e([y()], b3.prototype, "arrivalTime", void 0), e([o("arrivalTime", ["attributes.ArrivalTime"])], b3.prototype, "readArrivalTime", null), e([y({ json: { read: { source: "attributes.ArrivalUTCOffset" } } })], b3.prototype, "arrivalTimeOffset", void 0), e([y({ json: { read: { source: "attributes.Azimuth" } } })], b3.prototype, "azimuth", void 0), e([y({ json: { read: { source: "attributes.BranchName" } } })], b3.prototype, "branchName", void 0), e([y({ type: N.apiValues, json: { read: { source: "attributes.DirectionPointType", reader: N.read } } })], b3.prototype, "directionPointType", void 0), e([y({ json: { read: { source: "attributes.DisplayText" } } })], b3.prototype, "displayText", void 0), e([y({ json: { read: { source: "attributes.ExitName" } } })], b3.prototype, "exitName", void 0), e([y({ type: w })], b3.prototype, "geometry", void 0), e([y()], b3.prototype, "intersectingName", void 0), e([y()], b3.prototype, "level", void 0), e([y({ json: { read: { source: "attributes.Name" } } })], b3.prototype, "name", void 0), e([y({ json: { read: { source: "attributes.ObjectID" } } })], b3.prototype, "objectId", void 0), e([y({ type: k })], b3.prototype, "popupTemplate", void 0), e([y({ json: { read: { source: "attributes.Sequence" } } })], b3.prototype, "sequence", void 0), e([y()], b3.prototype, "shortVoiceInstruction", void 0), e([y({ json: { read: { source: "attributes.StopID" } } })], b3.prototype, "stopId", void 0), e([y({ types: j })], b3.prototype, "symbol", void 0), e([y({ json: { read: { source: "attributes.TowardName" } } })], b3.prototype, "towardName", void 0), e([y({ readOnly: true, json: { read: false } })], b3.prototype, "type", void 0), e([y()], b3.prototype, "voiceInstruction", void 0), b3 = u3 = e([a("esri.rest.support.DirectionPoint")], b3);
var h2 = b3;

// node_modules/@arcgis/core/rest/route/utils.js
function e3(e4, i4) {
  if (t(e4)) return null;
  const s3 = {}, o6 = new RegExp(`^${i4}`, "i");
  for (const r4 of Object.keys(e4)) if (o6.test(r4)) {
    const o7 = r4.substring(i4.length);
    s3[U.fromJSON(o7)] = e4[r4];
  }
  return s3;
}
function i3(e4, i4, s3) {
  if (!t(e4)) {
    i4.attributes || (i4.attributes = {});
    for (const r4 in e4) {
      const o6 = U.toJSON(r4);
      i4.attributes[`${s3}${o6}`] = e4[r4];
    }
  }
}
function s2(r4) {
  const e4 = {};
  for (const i4 of Object.keys(r4)) {
    const s3 = i4;
    e4[U.fromJSON(s3)] = r4[i4];
  }
  return e4;
}
function o5(r4) {
  const e4 = {};
  for (const i4 of Object.keys(r4)) {
    const s3 = i4;
    e4[U.toJSON(s3)] = r4[i4];
  }
  return e4;
}
function a3(t3, e4) {
  return t(t3) || t(e4) ? null : Math.round((t3 - e4) / 6e4);
}
function n2(r4) {
  var _a, _b, _c;
  const t3 = r4.toJSON(), e4 = t3;
  return e4.accumulateAttributeNames && (e4.accumulateAttributeNames = (_a = t3.accumulateAttributeNames) == null ? void 0 : _a.join()), e4.attributeParameterValues && (e4.attributeParameterValues = JSON.stringify(t3.attributeParameterValues)), e4.barriers && (e4.barriers = JSON.stringify(t3.barriers)), e4.outSR && (e4.outSR = (_b = t3.outSR) == null ? void 0 : _b.wkid), e4.overrides && (e4.overrides = JSON.stringify(t3.overrides)), e4.polygonBarriers && (e4.polygonBarriers = JSON.stringify(t3.polygonBarriers)), e4.polylineBarriers && (e4.polylineBarriers = JSON.stringify(t3.polylineBarriers)), e4.restrictionAttributeNames && (e4.restrictionAttributeNames = (_c = t3.restrictionAttributeNames) == null ? void 0 : _c.join()), e4.stops && (e4.stops = JSON.stringify(t3.stops)), e4.travelMode && (e4.travelMode = JSON.stringify(t3.travelMode)), e4;
}

// node_modules/@arcgis/core/rest/support/PointBarrier.js
var C;
var N2 = C = class extends i(l) {
  constructor(e4) {
    super(e4), this.addedCost = null, this.barrierType = null, this.costs = null, this.curbApproach = null, this.fullEdge = null, this.geometry = null, this.name = null, this.objectId = null, this.popupTemplate = null, this.sideOfEdge = null, this.sourceId = null, this.sourceOid = null, this.status = null, this.symbol = null, this.type = "point-barrier";
  }
  readCosts(e4, t3) {
    return e3(t3.attributes, "Attr_");
  }
  writeCosts(e4, t3) {
    i3(e4, t3, "Attr_");
  }
  static fromGraphic(e4) {
    return new C({ addedCost: e4.attributes.AddedCost ?? null, barrierType: r(e4.attributes.BarrierType) ? A.fromJSON(e4.attributes.BarrierType) : null, costs: r(e4.attributes.Costs) ? s2(JSON.parse(e4.attributes.Costs)) : null, curbApproach: r(e4.attributes.CurbApproach) ? u.fromJSON(e4.attributes.CurbApproach) : null, fullEdge: r(e4.attributes.FullEdge) ? T.fromJSON(e4.attributes.FullEdge) : null, geometry: e4.geometry, name: e4.attributes.Name ?? null, objectId: e4.attributes.ObjectID ?? e4.attributes.__OBJECTID, popupTemplate: e4.popupTemplate, status: r(e4.attributes.Status) ? p.fromJSON(e4.attributes.Status) : null, symbol: e4.symbol });
  }
  toGraphic() {
    const e4 = { ObjectID: e2(this.objectId), AddedCost: this.addedCost, BarrierType: r(this.barrierType) ? A.toJSON(this.barrierType) : null, Costs: r(this.costs) ? JSON.stringify(o5(this.costs)) : null, CurbApproach: r(this.curbApproach) ? u.toJSON(this.curbApproach) : null, FullEdge: r(this.fullEdge) ? T.toJSON(this.fullEdge) : null, Name: this.name, Status: r(this.status) ? p.toJSON(this.status) : null };
    return new g({ geometry: this.geometry, attributes: e4, symbol: this.symbol, popupTemplate: this.popupTemplate });
  }
};
N2.fields = [{ name: "ObjectID", alias: "ObjectID", type: "esriFieldTypeOID", editable: false, nullable: false, domain: null }, { name: "AddedCost", alias: "Added Cost", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true, domain: null }, { name: "BarrierType", alias: "Barrier Type", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriNABarrierType", codedValues: [{ name: "Restriction", code: 0 }, { name: "Scaled Cost", code: 1 }, { name: "Added Cost", code: 2 }] } }, { name: "Costs", alias: "Costs", type: "esriFieldTypeString", length: 1048576, editable: true, nullable: true, visible: false, domain: null }, { name: "CurbApproach", alias: "Curb Approach", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: false, domain: { type: "codedValue", name: "esriNACurbApproachType", codedValues: [{ name: "Either side", code: 0 }, { name: "From the right", code: 1 }, { name: "From the left", code: 2 }, { name: "Depart in the same direction", code: 3 }] } }, { name: "FullEdge", alias: "Full Edge", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriNAIntYesNo", codedValues: [{ name: "No", code: 0 }, { name: "Yes", code: 1 }] } }, { name: "Name", alias: "Name", type: "esriFieldTypeString", length: 255, editable: true, nullable: true, visible: true }, { name: "Status", alias: "Status", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriNAObjectStatus", codedValues: [{ name: "OK", code: 0 }, { name: "Not Located on Network", code: 1 }, { name: "Network Unbuilt", code: 2 }, { name: "Prohibited Street", code: 3 }, { name: "Invalid Field Values", code: 4 }, { name: "Cannot Reach", code: 5 }, { name: "Time Window Violation", code: 6 }] } }], N2.popupInfo = { title: "Point Barriers", fieldInfos: [{ fieldName: "Name", label: "Name", isEditable: true, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "BarrierType", label: "Barrier Type", isEditable: true, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "AddedCost", label: "Added Cost", isEditable: true, tooltip: "", visible: true, format: { places: 3, digitSeparator: true }, stringFieldOption: "textbox" }], description: null, showAttachments: false, mediaInfos: [] }, e([y()], N2.prototype, "addedCost", void 0), e([y({ type: A.apiValues, json: { name: "attributes.BarrierType", read: { reader: A.read }, write: { writer: A.write } } })], N2.prototype, "barrierType", void 0), e([y()], N2.prototype, "costs", void 0), e([o("costs", ["attributes"])], N2.prototype, "readCosts", null), e([r2("costs")], N2.prototype, "writeCosts", null), e([y({ type: u.apiValues, json: { read: { source: "attributes.CurbApproach", reader: u.read } } })], N2.prototype, "curbApproach", void 0), e([y({ type: T.apiValues, json: { name: "attributes.FullEdge", read: { reader: T.read }, write: { writer: T.write } } })], N2.prototype, "fullEdge", void 0), e([y({ type: w, json: { write: true } })], N2.prototype, "geometry", void 0), e([y({ json: { name: "attributes.Name" } })], N2.prototype, "name", void 0), e([y({ json: { name: "attributes.ObjectID" } })], N2.prototype, "objectId", void 0), e([y({ type: k })], N2.prototype, "popupTemplate", void 0), e([y({ type: v2.apiValues, json: { read: { source: "attributes.SideOfEdge", reader: v2.read } } })], N2.prototype, "sideOfEdge", void 0), e([y({ json: { read: { source: "attributes.SourceID" } } })], N2.prototype, "sourceId", void 0), e([y({ json: { read: { source: "attributes.SourceOID" } } })], N2.prototype, "sourceOid", void 0), e([y({ type: p.apiValues, json: { read: { source: "attributes.Status", reader: p.read } } })], N2.prototype, "status", void 0), e([y({ types: j })], N2.prototype, "symbol", void 0), e([y({ readOnly: true, json: { read: false } })], N2.prototype, "type", void 0), N2 = C = e([a("esri.rest.support.PointBarrier")], N2);
var O = N2;

// node_modules/@arcgis/core/rest/support/PolygonBarrier.js
var T2;
var h3 = T2 = class extends i(l) {
  constructor(e4) {
    super(e4), this.barrierType = null, this.costs = null, this.geometry = null, this.name = null, this.objectId = null, this.popupTemplate = null, this.scaleFactor = null, this.symbol = null, this.type = "polygon-barrier";
  }
  readCosts(e4, t3) {
    return e3(t3.attributes, "Attr_");
  }
  writeCosts(e4, t3) {
    i3(e4, t3, "Attr_");
  }
  static fromGraphic(e4) {
    return new T2({ barrierType: r(e4.attributes.BarrierType) ? A.fromJSON(e4.attributes.BarrierType) : null, costs: r(e4.attributes.Costs) ? s2(JSON.parse(e4.attributes.Costs)) : null, geometry: e4.geometry, name: e4.attributes.Name ?? null, objectId: e4.attributes.ObjectID ?? e4.attributes.__OBJECTID, popupTemplate: e4.popupTemplate, scaleFactor: e4.attributes.ScaleFactor ?? null, symbol: e4.symbol });
  }
  toGraphic() {
    const e4 = { ObjectID: e2(this.objectId), BarrierType: r(this.barrierType) ? A.toJSON(this.barrierType) : null, Costs: r(this.costs) ? JSON.stringify(o5(this.costs)) : null, Name: this.name ?? null, ScaleFactor: this.scaleFactor ?? null };
    return new g({ geometry: this.geometry, attributes: e4, symbol: this.symbol, popupTemplate: this.popupTemplate });
  }
};
h3.fields = [{ name: "ObjectID", alias: "ObjectID", type: "esriFieldTypeOID", editable: false, nullable: false, domain: null }, { name: "BarrierType", alias: "Barrier Type", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriNABarrierType", codedValues: [{ name: "Restriction", code: 0 }, { name: "Scaled Cost", code: 1 }, { name: "Added Cost", code: 2 }] } }, { name: "Costs", alias: "Costs", type: "esriFieldTypeString", length: 1048576, editable: true, nullable: true, visible: false, domain: null }, { name: "Name", alias: "Name", type: "esriFieldTypeString", length: 255, editable: true, nullable: true, visible: true }, { name: "ScaleFactor", alias: "Scale Factor", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true }], h3.popupInfo = { title: "Polygon Barriers", fieldInfos: [{ fieldName: "Name", label: "Name", isEditable: true, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "BarrierType", label: "Barrier Type", isEditable: true, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "ScaleFactor", isEditable: true, tooltip: "", visible: true, format: { places: 3, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "Costs", label: "Costs", isEditable: true, tooltip: "", visible: false, stringFieldOption: "textbox" }], description: null, showAttachments: false, mediaInfos: [] }, e([y({ type: A.apiValues, json: { name: "attributes.BarrierType", read: { reader: A.read }, write: { writer: A.write } } })], h3.prototype, "barrierType", void 0), e([y()], h3.prototype, "costs", void 0), e([o("costs", ["attributes"])], h3.prototype, "readCosts", null), e([r2("costs")], h3.prototype, "writeCosts", null), e([y({ type: v, json: { write: true } })], h3.prototype, "geometry", void 0), e([y({ json: { name: "attributes.Name" } })], h3.prototype, "name", void 0), e([y({ json: { name: "attributes.ObjectID" } })], h3.prototype, "objectId", void 0), e([y({ type: k })], h3.prototype, "popupTemplate", void 0), e([y()], h3.prototype, "scaleFactor", void 0), e([y({ types: j })], h3.prototype, "symbol", void 0), e([y({ readOnly: true, json: { read: false } })], h3.prototype, "type", void 0), h3 = T2 = e([a("esri.rest.support.PolygonBarrier")], h3);
var g3 = h3;

// node_modules/@arcgis/core/rest/support/PolylineBarrier.js
var j2;
var T3 = j2 = class extends i(l) {
  constructor(e4) {
    super(e4), this.barrierType = null, this.costs = null, this.geometry = null, this.name = null, this.objectId = null, this.popupTemplate = null, this.scaleFactor = null, this.symbol = null, this.type = "polyline-barrier";
  }
  readCosts(e4, t3) {
    return e3(t3.attributes, "Attr_");
  }
  static fromGraphic(e4) {
    return new j2({ barrierType: r(e4.attributes.BarrierType) ? A.fromJSON(e4.attributes.BarrierType) : null, costs: r(e4.attributes.Costs) ? s2(JSON.parse(e4.attributes.Costs)) : null, geometry: e4.geometry, name: e4.attributes.Name ?? null, objectId: e4.attributes.ObjectID ?? e4.attributes.__OBJECTID, popupTemplate: e4.popupTemplate, scaleFactor: e4.attributes.ScaleFactor ?? null, symbol: e4.symbol });
  }
  toGraphic() {
    const e4 = { ObjectID: e2(this.objectId), BarrierType: r(this.barrierType) ? A.toJSON(this.barrierType) : null, Costs: r(this.costs) ? JSON.stringify(o5(this.costs)) : null, Name: this.name, ScaleFactor: this.scaleFactor };
    return new g({ geometry: this.geometry, attributes: e4, symbol: this.symbol, popupTemplate: this.popupTemplate });
  }
};
T3.fields = [{ name: "ObjectID", alias: "ObjectID", type: "esriFieldTypeOID", editable: false, nullable: false, domain: null }, { name: "BarrierType", alias: "Barrier Type", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriNABarrierType", codedValues: [{ name: "Restriction", code: 0 }, { name: "Scaled Cost", code: 1 }, { name: "Added Cost", code: 2 }] } }, { name: "Costs", alias: "Costs", type: "esriFieldTypeString", length: 1048576, editable: true, nullable: true, visible: false, domain: null }, { name: "Name", alias: "Name", type: "esriFieldTypeString", length: 255, editable: true, nullable: true, visible: true }, { name: "ScaleFactor", alias: "Scale Factor", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true }], T3.popupInfo = { title: "Line Barriers", fieldInfos: [{ fieldName: "Name", label: "Name", isEditable: true, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "BarrierType", label: "Barrier Type", isEditable: true, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "ScaleFactor", isEditable: true, tooltip: "", visible: true, format: { places: 3, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "Costs", label: "Costs", isEditable: true, tooltip: "", visible: false, stringFieldOption: "textbox" }], description: null, showAttachments: false, mediaInfos: [] }, e([y({ type: A.apiValues, json: { read: { source: "attributes.BarrierType", reader: A.read } } })], T3.prototype, "barrierType", void 0), e([y()], T3.prototype, "costs", void 0), e([o("costs", ["attributes"])], T3.prototype, "readCosts", null), e([y({ type: m, json: { write: true } })], T3.prototype, "geometry", void 0), e([y({ json: { name: "attributes.Name" } })], T3.prototype, "name", void 0), e([y({ json: { name: "attributes.ObjectID" } })], T3.prototype, "objectId", void 0), e([y({ type: k })], T3.prototype, "popupTemplate", void 0), e([y()], T3.prototype, "scaleFactor", void 0), e([y({ types: j })], T3.prototype, "symbol", void 0), e([y({ readOnly: true, json: { read: false } })], T3.prototype, "type", void 0), T3 = j2 = e([a("esri.rest.support.PolylineBarrier")], T3);
var f2 = T3;

// node_modules/@arcgis/core/rest/support/TravelMode.js
var j3 = class extends i(l) {
  constructor(t3) {
    super(t3), this.attributeParameterValues = null, this.description = null, this.distanceAttributeName = null, this.id = null, this.impedanceAttributeName = null, this.name = null, this.restrictionAttributeNames = null, this.simplificationTolerance = null, this.simplificationToleranceUnits = null, this.timeAttributeName = null, this.type = null, this.useHierarchy = null, this.uturnAtJunctions = null;
  }
  readId(t3, r4) {
    return r4.id ?? r4.itemId ?? null;
  }
  readRestrictionAttributes(t3, r4) {
    const { restrictionAttributeNames: e4 } = r4;
    return t(e4) ? null : e4.map((t4) => D.fromJSON(t4));
  }
  writeRestrictionAttributes(t3, r4, e4) {
    t(t3) || (r4[e4] = t3.map((t4) => D.toJSON(t4)));
  }
};
e([y({ type: [Object], json: { write: true } })], j3.prototype, "attributeParameterValues", void 0), e([y({ type: String, json: { write: true } })], j3.prototype, "description", void 0), e([o3(f, { ignoreUnknown: false })], j3.prototype, "distanceAttributeName", void 0), e([y({ type: String, json: { write: true } })], j3.prototype, "id", void 0), e([o("id", ["id", "itemId"])], j3.prototype, "readId", null), e([o3(U, { ignoreUnknown: false })], j3.prototype, "impedanceAttributeName", void 0), e([y({ type: String, json: { write: true } })], j3.prototype, "name", void 0), e([y({ type: [String], json: { write: true } })], j3.prototype, "restrictionAttributeNames", void 0), e([o("restrictionAttributeNames")], j3.prototype, "readRestrictionAttributes", null), e([r2("restrictionAttributeNames")], j3.prototype, "writeRestrictionAttributes", null), e([y({ type: Number, json: { write: { allowNull: true } } })], j3.prototype, "simplificationTolerance", void 0), e([o3(i2)], j3.prototype, "simplificationToleranceUnits", void 0), e([o3(y2, { ignoreUnknown: false })], j3.prototype, "timeAttributeName", void 0), e([o3(m2)], j3.prototype, "type", void 0), e([y({ type: Boolean, json: { write: true } })], j3.prototype, "useHierarchy", void 0), e([o3(l2)], j3.prototype, "uturnAtJunctions", void 0), j3 = e([a("esri.rest.support.TravelMode")], j3);
var A2 = j3;

// node_modules/@arcgis/core/rest/support/RouteSettings.js
var c2 = class extends l {
  constructor(t3) {
    super(t3), this.accumulateAttributes = null, this.directionsLanguage = null, this.findBestSequence = null, this.preserveFirstStop = null, this.preserveLastStop = null, this.startTimeIsUTC = null, this.timeWindowsAreUTC = null, this.travelMode = null;
  }
  readAccumulateAttributes(t3) {
    return t(t3) ? null : t3.map((t4) => U.fromJSON(t4));
  }
  writeAccumulateAttributes(t3, e4, o6) {
    !t(t3) && t3.length && (e4[o6] = t3.map((t4) => U.toJSON(t4)));
  }
};
e([y({ type: [String], json: { name: "accumulateAttributeNames", write: true } })], c2.prototype, "accumulateAttributes", void 0), e([o("accumulateAttributes")], c2.prototype, "readAccumulateAttributes", null), e([r2("accumulateAttributes")], c2.prototype, "writeAccumulateAttributes", null), e([y({ type: String, json: { write: true } })], c2.prototype, "directionsLanguage", void 0), e([y({ type: Boolean, json: { write: true } })], c2.prototype, "findBestSequence", void 0), e([y({ type: Boolean, json: { write: true } })], c2.prototype, "preserveFirstStop", void 0), e([y({ type: Boolean, json: { write: true } })], c2.prototype, "preserveLastStop", void 0), e([y({ type: Boolean, json: { write: true } })], c2.prototype, "startTimeIsUTC", void 0), e([y({ type: Boolean, json: { write: true } })], c2.prototype, "timeWindowsAreUTC", void 0), e([y({ type: A2, json: { write: true } })], c2.prototype, "travelMode", void 0), c2 = e([a("esri.layers.support.RouteSettings")], c2);
var l3 = c2;

// node_modules/@arcgis/core/rest/support/RouteInfo.js
var S2;
var g4 = S2 = class extends i(l) {
  constructor(t3) {
    super(t3), this.analysisSettings = null, this.endTime = null, this.endTimeOffset = null, this.firstStopId = null, this.geometry = null, this.lastStopId = null, this.messages = null, this.name = null, this.objectId = null, this.popupTemplate = null, this.startTime = null, this.startTimeOffset = null, this.stopCount = null, this.symbol = null, this.totalCosts = null, this.totalDistance = null, this.totalDuration = null, this.totalLateDuration = null, this.totalViolations = null, this.totalWait = null, this.totalWaitDuration = null, this.type = "route-info", this.version = "1.0.0";
  }
  readEndTime(t3, e4) {
    return r(e4.attributes.EndTimeUTC) ? new Date(e4.attributes.EndTimeUTC) : null;
  }
  readEndTimeOffset(t3, e4) {
    return a3(e4.attributes.EndTime, e4.attributes.EndTimeUTC);
  }
  readStartTime(t3, e4) {
    return r(e4.attributes.StartTimeUTC) ? new Date(e4.attributes.StartTimeUTC) : null;
  }
  readStartTimeOffset(t3, e4) {
    return a3(e4.attributes.StartTime, e4.attributes.StartTimeUTC);
  }
  readTotalCosts(t3, e4) {
    return e3(e4.attributes, "Total_");
  }
  readTotalViolations(t3, e4) {
    return e3(e4.attributes, "TotalViolation_");
  }
  readTotalWait(t3, e4) {
    return e3(e4.attributes, "TotalWait_");
  }
  static fromGraphic(t3) {
    return new S2({ analysisSettings: r(t3.attributes.AnalysisSettings) ? l3.fromJSON(JSON.parse(t3.attributes.AnalysisSettings)) : null, endTime: r(t3.attributes.EndTime) ? new Date(t3.attributes.EndTime) : null, endTimeOffset: t3.attributes.EndUTCOffset ?? null, geometry: t3.geometry, messages: r(t3.attributes.Messages) ? JSON.parse(t3.attributes.Messages) : null, name: t3.attributes.RouteName, objectId: t3.attributes.ObjectID ?? t3.attributes.__OBJECTID, popupTemplate: t3.popupTemplate, startTime: r(t3.attributes.StartTime) ? new Date(t3.attributes.StartTime) : null, startTimeOffset: t3.attributes.StartUTCOffset ?? null, symbol: t3.symbol, totalCosts: r(t3.attributes.TotalCosts) ? s2(JSON.parse(t3.attributes.TotalCosts)) : null, totalDistance: t3.attributes.TotalMeters ?? null, totalDuration: t3.attributes.TotalMinutes ?? null, totalLateDuration: t3.attributes.TotalLateMinutes ?? null, totalWaitDuration: t3.attributes.TotalWaitMinutes ?? null, version: t3.attributes.Version });
  }
  toGraphic() {
    const t3 = { ObjectID: e2(this.objectId), AnalysisSettings: r(this.analysisSettings) ? JSON.stringify(this.analysisSettings.toJSON()) : null, EndTime: r(this.endTime) ? this.endTime.getTime() : null, EndUTCOffset: this.endTimeOffset, Messages: r(this.messages) ? JSON.stringify(this.messages) : null, RouteName: e2(this.name), StartTime: r(this.startTime) ? this.startTime.getTime() : null, StartUTCOffset: this.startTimeOffset, TotalCosts: r(this.totalCosts) ? JSON.stringify(o5(this.totalCosts)) : null, TotalLateMinutes: this.totalLateDuration, TotalMeters: this.totalDistance, TotalMinutes: this.totalDuration, TotalWaitMinutes: this.totalWaitDuration, Version: e2(this.version) };
    return new g({ geometry: this.geometry, attributes: t3, symbol: this.symbol, popupTemplate: e2(this.popupTemplate) });
  }
};
g4.fields = [{ name: "ObjectID", alias: "ObjectID", type: "esriFieldTypeOID", editable: false, nullable: false, domain: null }, { name: "AnalysisSettings", alias: "Analysis Settings", type: "esriFieldTypeString", length: 1048576, editable: true, nullable: true, visible: false, domain: null }, { name: "EndTime", alias: "End Time", type: "esriFieldTypeDate", length: 36, editable: true, nullable: true, visible: true }, { name: "EndUTCOffset", alias: "End Time: Offset from UTC in Minutes", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "Messages", alias: "Analysis Messages", type: "esriFieldTypeString", length: 1048576, editable: true, nullable: true, visible: false, domain: null }, { name: "RouteName", alias: "Route Name", type: "esriFieldTypeString", length: 1024, editable: true, nullable: true, visible: true, domain: null }, { name: "StartTime", alias: "Start Time", type: "esriFieldTypeDate", length: 36, editable: true, nullable: true, visible: true }, { name: "StartUTCOffset", alias: "Start Time: Offset from UTC in Minutes", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "TotalCosts", alias: "Total Costs", type: "esriFieldTypeString", length: 1048576, editable: true, nullable: true, visible: false, domain: null }, { name: "TotalLateMinutes", alias: "Total Late Minutes", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: false }, { name: "TotalMeters", alias: "Total Meters", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true }, { name: "TotalMinutes", alias: "Total Minutes", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true }, { name: "TotalWaitMinutes", alias: "Total Wait Minutes", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: false }, { name: "Version", alias: "Version", type: "esriFieldTypeString", length: 16, editable: true, nullable: true, visible: true, domain: null }], g4.popupInfo = { title: "Route Details", fieldInfos: [{ fieldName: "RouteName", label: "Route Name", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "TotalMinutes", label: "Total Minutes", isEditable: false, tooltip: "", visible: true, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "TotalMeters", label: "Total Meters", isEditable: false, tooltip: "", visible: true, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "TotalLateMinutes", label: "Total Late Minutes", isEditable: false, tooltip: "", visible: false, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "TotalWaitMinutes", label: "Total Wait Minutes", isEditable: false, tooltip: "", visible: false, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "TotalCosts", label: "Total Costs", isEditable: false, tooltip: "", visible: false, stringFieldOption: "textbox" }, { fieldName: "StartTime", label: "Start Time", isEditable: false, tooltip: "", visible: true, format: { dateFormat: "shortDateShortTime24" }, stringFieldOption: "textbox" }, { fieldName: "StartUTCOffset", label: "Start Time: Offset from UTC in Minutes", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "EndTime", label: "End Time", isEditable: false, tooltip: "", visible: true, format: { dateFormat: "shortDateShortTime24" }, stringFieldOption: "textbox" }, { fieldName: "EndUTCOffset", label: "End Time: Offset from UTC in Minutes", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "Messages", label: "Analysis Messages", isEditable: false, tooltip: "", visible: false, stringFieldOption: "textbox" }, { fieldName: "AnalysisSettings", isEditable: false, tooltip: "", visible: false, stringFieldOption: "textbox" }, { fieldName: "Version", label: "Version", isEditable: false, tooltip: "", visible: true, stringFieldOption: "textbox" }], description: null, showAttachments: false, mediaInfos: [] }, e([y()], g4.prototype, "analysisSettings", void 0), e([y()], g4.prototype, "endTime", void 0), e([o("endTime", ["attributes.EndTimeUTC"])], g4.prototype, "readEndTime", null), e([y()], g4.prototype, "endTimeOffset", void 0), e([o("endTimeOffset", ["attributes.EndTime", "attributes.EndTimeUTC"])], g4.prototype, "readEndTimeOffset", null), e([y({ json: { read: { source: "attributes.FirstStopID" } } })], g4.prototype, "firstStopId", void 0), e([y({ type: m })], g4.prototype, "geometry", void 0), e([y({ json: { read: { source: "attributes.LastStopID" } } })], g4.prototype, "lastStopId", void 0), e([y()], g4.prototype, "messages", void 0), e([y({ json: { read: { source: "attributes.Name" } } })], g4.prototype, "name", void 0), e([y({ json: { read: { source: "attributes.ObjectID" } } })], g4.prototype, "objectId", void 0), e([y({ type: k })], g4.prototype, "popupTemplate", void 0), e([y()], g4.prototype, "startTime", void 0), e([o("startTime", ["attributes.StartTimeUTC"])], g4.prototype, "readStartTime", null), e([y()], g4.prototype, "startTimeOffset", void 0), e([o("startTimeOffset", ["attributes.StartTime", "attributes.StartTimeUTC"])], g4.prototype, "readStartTimeOffset", null), e([y({ json: { read: { source: "attributes.StopCount" } } })], g4.prototype, "stopCount", void 0), e([y({ types: j })], g4.prototype, "symbol", void 0), e([y()], g4.prototype, "totalCosts", void 0), e([o("totalCosts", ["attributes"])], g4.prototype, "readTotalCosts", null), e([y()], g4.prototype, "totalDistance", void 0), e([y()], g4.prototype, "totalDuration", void 0), e([y()], g4.prototype, "totalLateDuration", void 0), e([y()], g4.prototype, "totalViolations", void 0), e([o("totalViolations", ["attributes"])], g4.prototype, "readTotalViolations", null), e([y()], g4.prototype, "totalWait", void 0), e([o("totalWait", ["attributes"])], g4.prototype, "readTotalWait", null), e([y()], g4.prototype, "totalWaitDuration", void 0), e([y({ readOnly: true, json: { read: false } })], g4.prototype, "type", void 0), e([y()], g4.prototype, "version", void 0), g4 = S2 = e([a("esri.rest.support.RouteInfo")], g4);
var h4 = g4;

// node_modules/@arcgis/core/rest/support/Stop.js
var g5;
var w3 = g5 = class extends i(l) {
  constructor(e4) {
    super(e4), this.arriveCurbApproach = null, this.arriveTime = null, this.arriveTimeOffset = null, this.bearing = null, this.bearingTol = null, this.cumulativeCosts = null, this.cumulativeDistance = null, this.cumulativeDuration = null, this.curbApproach = null, this.departCurbApproach = null, this.departTime = null, this.departTimeOffset = null, this.distanceToNetworkInMeters = null, this.geometry = null, this.lateDuration = null, this.locationType = null, this.name = null, this.navLatency = null, this.objectId = null, this.popupTemplate = null, this.posAlong = null, this.routeName = null, this.serviceCosts = null, this.serviceDistance = null, this.serviceDuration = null, this.sequence = null, this.sideOfEdge = null, this.snapX = null, this.snapY = null, this.snapZ = null, this.sourceId = null, this.sourceOid = null, this.status = null, this.symbol = null, this.timeWindowEnd = null, this.timeWindowEndOffset = null, this.timeWindowStart = null, this.timeWindowStartOffset = null, this.type = "stop", this.violations = null, this.waitDuration = null, this.wait = null;
  }
  readArriveTimeOffset(e4, t3) {
    return a3(t3.attributes.ArriveTime, t3.attributes.ArriveTimeUTC);
  }
  readCumulativeCosts(e4, t3) {
    return e3(t3.attributes, "Cumul_");
  }
  readDepartTimeOffset(e4, t3) {
    return a3(t3.attributes.DepartTime, t3.attributes.DepartTimeUTC);
  }
  readServiceCosts(e4, t3) {
    return e3(t3.attributes, "Attr_");
  }
  writeServiceCosts(e4, t3) {
    i3(e4, t3, "Attr_");
  }
  writeTimeWindowEnd(e4, t3) {
    t(e4) || (t3.attributes || (t3.attributes = {}), t3.attributes.TimeWindowEnd = e4.getTime());
  }
  writeTimeWindowStart(e4, t3) {
    t(e4) || (t3.attributes || (t3.attributes = {}), t3.attributes.TimeWindowStart = e4.getTime());
  }
  readViolations(e4, t3) {
    return e3(t3.attributes, "Violation_");
  }
  readWait(e4, t3) {
    return e3(t3.attributes, "Wait_");
  }
  static fromGraphic(e4) {
    return new g5({ arriveCurbApproach: r(e4.attributes.ArrivalCurbApproach) ? u.fromJSON(e4.attributes.ArrivalCurbApproach) : null, arriveTime: r(e4.attributes.ArrivalTime) ? new Date(e4.attributes.ArrivalTime) : null, arriveTimeOffset: e4.attributes.ArrivalUTCOffset, cumulativeCosts: r(e4.attributes.CumulativeCosts) ? s2(JSON.parse(e4.attributes.CumulativeCosts)) : null, cumulativeDistance: e4.attributes.CumulativeMeters ?? null, cumulativeDuration: e4.attributes.CumulativeMinutes ?? null, curbApproach: r(e4.attributes.CurbApproach) ? u.fromJSON(e4.attributes.CurbApproach) : null, departCurbApproach: r(e4.attributes.DepartureCurbApproach) ? u.fromJSON(e4.attributes.DepartureCurbApproach) : null, departTime: r(e4.attributes.DepartureTime) ? new Date(e4.attributes.DepartureTime) : null, departTimeOffset: e4.attributes.DepartureUTCOffset ?? null, geometry: e4.geometry, lateDuration: e4.attributes.LateMinutes ?? null, locationType: r(e4.attributes.LocationType) ? h.fromJSON(e4.attributes.LocationType) : null, name: e4.attributes.Name, objectId: e4.attributes.ObjectID ?? e4.attributes.__OBJECTID, popupTemplate: e4.popupTemplate, routeName: e4.attributes.RouteName, sequence: e4.attributes.Sequence ?? null, serviceCosts: r(e4.attributes.ServiceCosts) ? s2(JSON.parse(e4.attributes.ServiceCosts)) : null, serviceDistance: e4.attributes.ServiceMeters ?? null, serviceDuration: e4.attributes.ServiceMinutes ?? null, status: r(e4.attributes.Status) ? p.fromJSON(e4.attributes.Status) : null, symbol: e4.symbol, timeWindowEnd: r(e4.attributes.TimeWindowEnd) ? new Date(e4.attributes.TimeWindowEnd) : null, timeWindowEndOffset: e4.attributes.TimeWindowEndUTCOffset ?? null, timeWindowStart: r(e4.attributes.TimeWindowStart) ? new Date(e4.attributes.TimeWindowStart) : null, timeWindowStartOffset: e4.attributes.TimeWindowStartUTCOffset ?? null, waitDuration: e4.attributes.WaitMinutes ?? null });
  }
  toGraphic() {
    const e4 = { ObjectID: e2(this.objectId), ArrivalCurbApproach: r(this.arriveCurbApproach) ? u.toJSON(this.arriveCurbApproach) : null, ArrivalTime: r(this.arriveTime) ? this.arriveTime.getTime() : null, ArrivalUTCOffset: this.arriveTimeOffset, CumulativeCosts: r(this.cumulativeCosts) ? JSON.stringify(o5(this.cumulativeCosts)) : null, CumulativeMeters: this.cumulativeDistance, CumulativeMinutes: this.cumulativeDuration, CurbApproach: r(this.curbApproach) ? u.toJSON(this.curbApproach) : null, DepartureCurbApproach: r(this.departCurbApproach) ? u.toJSON(this.departCurbApproach) : null, DepartureTime: r(this.departTime) ? this.departTime.getTime() : null, DepartureUTCOffset: this.departTimeOffset, LateMinutes: this.lateDuration, LocationType: r(this.locationType) ? h.toJSON(this.locationType) : null, Name: e2(this.name), RouteName: e2(this.routeName), Sequence: this.sequence, ServiceCosts: r(this.serviceCosts) ? JSON.stringify(o5(this.serviceCosts)) : null, ServiceMeters: this.serviceDistance, ServiceMinutes: this.serviceDuration, Status: r(this.status) ? p.toJSON(this.status) : null, TimeWindowEnd: r(this.timeWindowEnd) ? this.timeWindowEnd.getTime() : null, TimeWindowEndUTCOffset: this.timeWindowEndOffset ?? this.arriveTimeOffset, TimeWindowStart: r(this.timeWindowStart) ? this.timeWindowStart.getTime() : null, TimeWindowStartUTCOffset: this.timeWindowStartOffset ?? this.arriveTimeOffset, WaitMinutes: this.waitDuration };
    return new g({ geometry: this.geometry, attributes: e4, symbol: this.symbol, popupTemplate: this.popupTemplate });
  }
};
w3.fields = [{ name: "ObjectID", alias: "ObjectID", type: "esriFieldTypeOID", editable: false, nullable: false, domain: null }, { name: "ArrivalCurbApproach", alias: "Arrival Curb Approach", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriNACurbApproachType", codedValues: [{ name: "Either side", code: 0 }, { name: "From the right", code: 1 }, { name: "From the left", code: 2 }, { name: "Depart in the same direction", code: 3 }] } }, { name: "ArrivalTime", alias: "Arrival Time", type: "esriFieldTypeDate", length: 36, editable: true, nullable: true, visible: true }, { name: "ArrivalUTCOffset", alias: "Arrival Time: Offset from UTC in Minutes", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "CumulativeCosts", alias: "Cumulative Costs", type: "esriFieldTypeString", length: 1048576, editable: true, nullable: true, visible: false, domain: null }, { name: "CumulativeMeters", alias: "Cumulative Meters", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true }, { name: "CumulativeMinutes", alias: "Cumulative Minutes", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: true }, { name: "CurbApproach", alias: "Curb Approach", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: false, domain: { type: "codedValue", name: "esriNACurbApproachType", codedValues: [{ name: "Either side", code: 0 }, { name: "From the right", code: 1 }, { name: "From the left", code: 2 }, { name: "Depart in the same direction", code: 3 }] } }, { name: "DepartureCurbApproach", alias: "Departure Curb Approach", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriNACurbApproachType", codedValues: [{ name: "Either side", code: 0 }, { name: "From the right", code: 1 }, { name: "From the left", code: 2 }, { name: "Depart in the same direction", code: 3 }] } }, { name: "DepartureTime", alias: "Departure Time", type: "esriFieldTypeDate", length: 36, editable: true, nullable: true, visible: true }, { name: "DepartureUTCOffset", alias: "Departure Time: Offset from UTC in Minutes", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "LateMinutes", alias: "Minutes Late", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: false }, { name: "LocationType", alias: "Location Type", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriNALocationType", codedValues: [{ name: "Stop", code: 0 }, { name: "Waypoint", code: 1 }] } }, { name: "Name", alias: "Name", type: "esriFieldTypeString", length: 255, editable: true, nullable: true, visible: true }, { name: "RouteName", alias: "Route Name", type: "esriFieldTypeString", length: 255, editable: true, nullable: true, visible: true }, { name: "Sequence", alias: "Sequence", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "ServiceCosts", alias: "Service Costs", type: "esriFieldTypeString", length: 1048576, editable: true, nullable: true, visible: false, domain: null }, { name: "ServiceMeters", alias: "Service Meters", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: false }, { name: "ServiceMinutes", alias: "Service Minutes", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: false }, { name: "Status", alias: "Status", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true, domain: { type: "codedValue", name: "esriNAObjectStatus", codedValues: [{ name: "OK", code: 0 }, { name: "Not Located on Network", code: 1 }, { name: "Network Unbuilt", code: 2 }, { name: "Prohibited Street", code: 3 }, { name: "Invalid Field Values", code: 4 }, { name: "Cannot Reach", code: 5 }, { name: "Time Window Violation", code: 6 }] } }, { name: "TimeWindowEnd", alias: "Time Window End", type: "esriFieldTypeDate", length: 36, editable: true, nullable: true, visible: false }, { name: "TimeWindowEndUTCOffset", alias: "Time Window End: Offset from UTC in Minutes", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "TimeWindowStart", alias: "Time Window Start", type: "esriFieldTypeDate", length: 36, editable: true, nullable: true, visible: false }, { name: "TimeWindowStartUTCOffset", alias: "Time Window Start: Offset from UTC in Minutes", type: "esriFieldTypeInteger", editable: true, nullable: true, visible: true }, { name: "WaitMinutes", alias: "Minutes Early", type: "esriFieldTypeDouble", editable: true, nullable: true, visible: false }], w3.popupInfo = { title: "{Name}", fieldInfos: [{ fieldName: "Name", label: "Name", isEditable: true, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "RouteName", label: "Route Name", isEditable: true, tooltip: "", visible: true, stringFieldOption: "textbox" }, { fieldName: "Sequence", label: "Sequence", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "ArrivalTime", label: "Arrival Time", isEditable: true, tooltip: "", visible: true, format: { dateFormat: "shortDateShortTime24" }, stringFieldOption: "textbox" }, { fieldName: "ArrivalUTCOffset", label: "Arrival Time: Offset from UTC in Minutes", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "DepartureTime", label: "Departure Time", isEditable: true, tooltip: "", visible: true, format: { dateFormat: "shortDateShortTime24" }, stringFieldOption: "textbox" }, { fieldName: "DepartureUTCOffset", label: "Departure Time: Offset from UTC in Minutes", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "CurbApproach", label: "Curb Approach", isEditable: true, tooltip: "", visible: false, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "ArrivalCurbApproach", label: "Arrival Curb Approach", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "DepartureCurbApproach", label: "Departure Curb Approach", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "Status", label: "Status", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "LocationType", label: "Location Type", isEditable: false, tooltip: "", visible: true, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "TimeWindowStart", label: "Time Window Start", isEditable: true, tooltip: "", visible: false, format: { dateFormat: "shortDateShortTime24" }, stringFieldOption: "textbox" }, { fieldName: "TimeWindowStartUTCOffset", label: "Time Window Start: Offset from UTC in Minutes", isEditable: false, tooltip: "", visible: false, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "TimeWindowEnd", label: "Time Window End", isEditable: true, tooltip: "", visible: false, format: { dateFormat: "shortDateShortTime24" }, stringFieldOption: "textbox" }, { fieldName: "TimeWindowEndUTCOffset", label: "Time Window End: Offset from UTC in Minutes", isEditable: false, tooltip: "", visible: false, format: { places: 0, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "ServiceMinutes", label: "Service Minutes", isEditable: true, tooltip: "", visible: false, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "ServiceMeters", label: "Service Meters", isEditable: true, tooltip: "", visible: false, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "ServiceCosts", label: "Service Costs", isEditable: true, tooltip: "", visible: false, stringFieldOption: "textbox" }, { fieldName: "CumulativeMinutes", label: "Cumulative Minutes", isEditable: false, tooltip: "", visible: true, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "CumulativeMeters", label: "Cumulative Meters", isEditable: false, tooltip: "", visible: true, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "CumulativeCosts", label: "Cumulative Costs", isEditable: true, tooltip: "", visible: false, stringFieldOption: "textbox" }, { fieldName: "LateMinutes", label: "Minutes Late", isEditable: false, tooltip: "", visible: false, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }, { fieldName: "WaitMinutes", label: "Minutes Early", isEditable: false, tooltip: "", visible: false, format: { places: 2, digitSeparator: true }, stringFieldOption: "textbox" }], description: null, showAttachments: false, mediaInfos: [] }, e([y({ type: u.apiValues, json: { read: { source: "attributes.ArrivalCurbApproach", reader: u.read } } })], w3.prototype, "arriveCurbApproach", void 0), e([y({ type: Date, json: { read: { source: "attributes.ArriveTimeUTC" } } })], w3.prototype, "arriveTime", void 0), e([y()], w3.prototype, "arriveTimeOffset", void 0), e([o("arriveTimeOffset", ["attributes.ArriveTime", "attributes.ArriveTimeUTC"])], w3.prototype, "readArriveTimeOffset", null), e([y({ json: { name: "attributes.Bearing", read: false, write: true } })], w3.prototype, "bearing", void 0), e([y({ json: { name: "attributes.BearingTol", read: false, write: true } })], w3.prototype, "bearingTol", void 0), e([y()], w3.prototype, "cumulativeCosts", void 0), e([o("cumulativeCosts", ["attributes"])], w3.prototype, "readCumulativeCosts", null), e([y()], w3.prototype, "cumulativeDistance", void 0), e([y()], w3.prototype, "cumulativeDuration", void 0), e([y({ type: u.apiValues, json: { name: "attributes.CurbApproach", read: { reader: u.read }, write: { writer: u.write } } })], w3.prototype, "curbApproach", void 0), e([y({ type: u.apiValues, json: { read: { source: "attributes.DepartCurbApproach", reader: u.read } } })], w3.prototype, "departCurbApproach", void 0), e([y({ type: Date, json: { read: { source: "attributes.DepartTimeUTC" } } })], w3.prototype, "departTime", void 0), e([y()], w3.prototype, "departTimeOffset", void 0), e([o("departTimeOffset", ["attributes.DepartTime", "attributes.DepartTimeUTC"])], w3.prototype, "readDepartTimeOffset", null), e([y({ json: { read: { source: "attributes.DistanceToNetworkInMeters" } } })], w3.prototype, "distanceToNetworkInMeters", void 0), e([y({ type: w, json: { write: true } })], w3.prototype, "geometry", void 0), e([y()], w3.prototype, "lateDuration", void 0), e([y({ type: h.apiValues, json: { name: "attributes.LocationType", read: { reader: h.read }, write: { writer: h.write } } })], w3.prototype, "locationType", void 0), e([y({ json: { name: "attributes.Name" } })], w3.prototype, "name", void 0), e([y({ json: { name: "attributes.NavLatency", read: false, write: true } })], w3.prototype, "navLatency", void 0), e([y({ json: { name: "attributes.ObjectID" } })], w3.prototype, "objectId", void 0), e([y({ type: k })], w3.prototype, "popupTemplate", void 0), e([y({ json: { read: { source: "attributes.PosAlong" } } })], w3.prototype, "posAlong", void 0), e([y({ json: { name: "attributes.RouteName" } })], w3.prototype, "routeName", void 0), e([y()], w3.prototype, "serviceCosts", void 0), e([o("serviceCosts", ["attributes"])], w3.prototype, "readServiceCosts", null), e([r2("serviceCosts")], w3.prototype, "writeServiceCosts", null), e([y()], w3.prototype, "serviceDistance", void 0), e([y()], w3.prototype, "serviceDuration", void 0), e([y({ json: { name: "attributes.Sequence" } })], w3.prototype, "sequence", void 0), e([y({ type: v2.apiValues, json: { read: { source: "attributes.SideOfEdge", reader: v2.read } } })], w3.prototype, "sideOfEdge", void 0), e([y({ json: { read: { source: "attributes.SnapX" } } })], w3.prototype, "snapX", void 0), e([y({ json: { read: { source: "attributes.SnapY" } } })], w3.prototype, "snapY", void 0), e([y({ json: { read: { source: "attributes.SnapZ" } } })], w3.prototype, "snapZ", void 0), e([y({ json: { read: { source: "attributes.SourceID" } } })], w3.prototype, "sourceId", void 0), e([y({ json: { read: { source: "attributes.SourceOID" } } })], w3.prototype, "sourceOid", void 0), e([y({ type: p.apiValues, json: { read: { source: "attributes.Status", reader: p.read } } })], w3.prototype, "status", void 0), e([y({ types: j })], w3.prototype, "symbol", void 0), e([y({ type: Date, json: { name: "attributes.TimeWindowEnd" } })], w3.prototype, "timeWindowEnd", void 0), e([r2("timeWindowEnd")], w3.prototype, "writeTimeWindowEnd", null), e([y()], w3.prototype, "timeWindowEndOffset", void 0), e([y({ type: Date, json: { name: "attributes.TimeWindowStart" } })], w3.prototype, "timeWindowStart", void 0), e([r2("timeWindowStart")], w3.prototype, "writeTimeWindowStart", null), e([y()], w3.prototype, "timeWindowStartOffset", void 0), e([y({ readOnly: true, json: { read: false } })], w3.prototype, "type", void 0), e([y()], w3.prototype, "violations", void 0), e([o("violations", ["attributes"])], w3.prototype, "readViolations", null), e([y()], w3.prototype, "waitDuration", void 0), e([y()], w3.prototype, "wait", void 0), e([o("wait", ["attributes"])], w3.prototype, "readWait", null), w3 = g5 = e([a("esri.rest.support.Stop")], w3);
var D2 = w3;

export {
  i2 as i,
  r3 as r,
  s,
  a2 as a,
  o4 as o,
  l2 as l,
  k2 as k,
  y2 as y,
  U,
  D,
  S,
  w2 as w,
  R,
  b,
  A2 as A,
  a3 as a2,
  n2 as n,
  b2,
  h2 as h,
  O,
  g3 as g,
  f2 as f,
  l3 as l2,
  h4 as h2,
  D2
};
//# sourceMappingURL=chunk-OD5TAVCV.js.map
