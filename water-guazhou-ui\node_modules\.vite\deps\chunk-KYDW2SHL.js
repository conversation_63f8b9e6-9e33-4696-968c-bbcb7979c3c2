import {
  r as r4
} from "./chunk-QKWIBVLD.js";
import {
  E
} from "./chunk-FTRLEBHJ.js";
import {
  D as D2,
  G,
  M,
  P,
  U
} from "./chunk-4M3AMTD4.js";
import {
  t as t2
} from "./chunk-DUEDINK5.js";
import {
  e
} from "./chunk-MZ267CZB.js";
import {
  r as r3
} from "./chunk-QCTKOQ44.js";
import {
  M as M2,
  b as b2,
  h,
  i,
  r as r2
} from "./chunk-ST2RRB55.js";
import {
  D,
  b,
  d
} from "./chunk-EKX3LLYN.js";
import {
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/engine/ImageryBitmapSource.js
var l = class {
  constructor(e2, t3, l2) {
    this.pixelBlock = e2, this.extent = t3, this.originalPixelBlock = l2;
  }
  get width() {
    return r(this.pixelBlock) ? this.pixelBlock.width : 0;
  }
  get height() {
    return r(this.pixelBlock) ? this.pixelBlock.height : 0;
  }
  render(e2) {
    const l2 = this.pixelBlock;
    if (t(l2)) return;
    const i2 = this.filter({ extent: this.extent, pixelBlock: this.originalPixelBlock ?? l2 });
    if (t(i2.pixelBlock)) return;
    i2.pixelBlock.maskIsAlpha && (i2.pixelBlock.premultiplyAlpha = true);
    const o2 = i2.pixelBlock.getAsRGBA(), s = e2.createImageData(i2.pixelBlock.width, i2.pixelBlock.height);
    s.data.set(o2), e2.putImageData(s, 0, 0);
  }
  getRenderedRasterPixels() {
    const e2 = this.filter({ extent: this.extent, pixelBlock: this.pixelBlock });
    return t(e2.pixelBlock) ? null : (e2.pixelBlock.maskIsAlpha && (e2.pixelBlock.premultiplyAlpha = true), { width: e2.pixelBlock.width, height: e2.pixelBlock.height, renderedRasterPixels: new Uint8Array(e2.pixelBlock.getAsRGBA().buffer) });
  }
};

// node_modules/@arcgis/core/views/2d/engine/Bitmap.js
function T(t3) {
  return t3 && "render" in t3;
}
function S(t3) {
  const e2 = document.createElement("canvas");
  return e2.width = t3.width, e2.height = t3.height, t3.render(e2.getContext("2d")), e2;
}
function v(t3) {
  return T(t3) ? t3 instanceof l ? o(t3.getRenderedRasterPixels(), (t4) => t4.renderedRasterPixels) : S(t3) : t3;
}
var R = class extends r4 {
  constructor(t3 = null, e2) {
    super(), this.blendFunction = "standard", this._sourceWidth = 0, this._sourceHeight = 0, this._textureInvalidated = false, this._texture = null, this.stencilRef = 0, this.coordScale = [1, 1], this._height = void 0, this.pixelRatio = 1, this.resolution = 0, this.rotation = 0, this._source = null, this._width = void 0, this.x = 0, this.y = 0, this.immutable = e2.immutable ?? false, this.requestRenderOnSourceChangedEnabled = e2.requestRenderOnSourceChangedEnabled ?? true, this.source = t3, this.requestRender = this.requestRender.bind(this);
  }
  destroy() {
    this._texture && (this._texture.dispose(), this._texture = null), r(this._uploadStatus) && (this._uploadStatus.controller.abort(), this._uploadStatus = null);
  }
  get isSourceScaled() {
    return this.width !== this._sourceWidth || this.height !== this._sourceHeight;
  }
  get height() {
    return void 0 !== this._height ? this._height : this._sourceHeight;
  }
  set height(t3) {
    this._height = t3;
  }
  get source() {
    return this._source;
  }
  set source(t3) {
    null == t3 && null == this._source || (this._source = t3, this._source instanceof HTMLImageElement ? (this._sourceHeight = this._source.naturalHeight, this._sourceWidth = this._source.naturalWidth) : this._source && (this._sourceHeight = this._source.height, this._sourceWidth = this._source.width), this.invalidateTexture());
  }
  get width() {
    return void 0 !== this._width ? this._width : this._sourceWidth;
  }
  set width(t3) {
    this._width = t3;
  }
  beforeRender(t3) {
    super.beforeRender(t3), this.updateTexture(t3);
  }
  async setSourceAsync(e2, r5) {
    r(this._uploadStatus) && this._uploadStatus.controller.abort();
    const h2 = new AbortController(), o2 = D();
    return d(r5, () => h2.abort()), d(h2, (t3) => o2.reject(t3)), this._uploadStatus = { controller: h2, resolver: o2 }, this.source = e2, o2.promise;
  }
  invalidateTexture() {
    this._textureInvalidated || (this._textureInvalidated = true, this.requestRenderOnSourceChangedEnabled && this.requestRender());
  }
  updateTransitionProperties(t3, e2) {
    t3 >= 64 && (this.fadeTransitionEnabled = false, this.inFadeTransition = false), super.updateTransitionProperties(t3, e2);
  }
  setTransform(t3) {
    const e2 = r2(this.transforms.dvs), [s, i2] = t3.toScreenNoRotation([0, 0], [this.x, this.y]), r5 = this.resolution / this.pixelRatio / t3.resolution, d2 = r5 * this.width, c = r5 * this.height, _ = Math.PI * this.rotation / 180;
    M2(e2, e2, t2(s, i2)), M2(e2, e2, t2(d2 / 2, c / 2)), h(e2, e2, -_), M2(e2, e2, t2(-d2 / 2, -c / 2)), b2(e2, e2, t2(d2, c)), i(this.transforms.dvs, t3.displayViewMat3, e2);
  }
  setSamplingProfile(t3) {
    this._texture && (t3.mips && !this._texture.descriptor.hasMipmap && this._texture.generateMipmap(), this._texture.setSamplingMode(t3.samplingMode));
  }
  bind(t3, e2) {
    this._texture && t3.bindTexture(this._texture, e2);
  }
  async updateTexture({ context: e2, painter: s }) {
    if (!this._textureInvalidated) return;
    if (this._textureInvalidated = false, this._texture || (this._texture = this._createTexture(e2)), !this.source) return void this._texture.setData(null);
    this._texture.resize(this._sourceWidth, this._sourceHeight);
    const i2 = v(this.source);
    try {
      if (r(this._uploadStatus)) {
        const { controller: t3, resolver: e3 } = this._uploadStatus, r5 = { signal: t3.signal }, { width: h2, height: o2 } = this, u = this._texture, a = s.textureUploadManager;
        await a.enqueueTextureUpdate({ data: i2, texture: u, width: h2, height: o2 }, r5), e3.resolve(), this._uploadStatus = null;
      } else this._texture.setData(i2);
      this.ready();
    } catch (h2) {
      b(h2);
    }
  }
  onDetach() {
    this.destroy();
  }
  _createTransforms() {
    return { dvs: e() };
  }
  _createTexture(t3) {
    const e2 = this.immutable && t3.type === r3.WEBGL2;
    return new E(t3, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: e2 ? U.RGBA8 : P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, isImmutable: e2, width: this._sourceWidth, height: this._sourceHeight });
  }
};

export {
  l,
  T,
  S,
  R
};
//# sourceMappingURL=chunk-KYDW2SHL.js.map
