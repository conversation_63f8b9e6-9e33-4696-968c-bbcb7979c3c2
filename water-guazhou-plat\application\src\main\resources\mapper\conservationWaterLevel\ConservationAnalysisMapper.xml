<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.conservationWaterLevel.ConservationAnalysisMapper">

    <sql id="Base_Column_List">
        a.id,
        a.tenant_id as tenantId,
        a.station_id as stationId,
        a.start_time as startTime,
        a.end_time as endTime,
        a.initial_level as initialLevel,
        a.final_level as finalLevel,
        a.level_change as levelChange,
        a.avg_rainfall as avgRainfall,
        a.avg_evaporation as avgEvaporation,
        a.total_extraction as totalExtraction,
        a.conservation_coefficient as conservationCoefficient,
        a.conservation_potential as conservationPotential,
        a.suggested_conservation_amount as suggestedConservationAmount,
        a.conservation_suggestion as conservationSuggestion,
        a.risk_level as riskLevel,
        a.risk_description as riskDescription,
        a.algorithm_version as algorithmVersion,
        a.analysis_details as analysisDetails,
        a.create_time as createTime,
        a.update_time as updateTime,
        a.status,
        a.creator,
        s.name as stationName,
        u.first_name as creatorName
    </sql>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationAnalysis">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_conservation_analysis a
        LEFT JOIN tb_station s ON a.station_id = s.id
        LEFT JOIN tb_user u ON a.creator = u.id
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                AND a.tenant_id = #{params.tenantId}
            </if>
            <if test="params.stationId != null and params.stationId != ''">
                AND a.station_id = #{params.stationId}
            </if>
            <if test="params.stationName != null and params.stationName != ''">
                AND s.name LIKE CONCAT('%', #{params.stationName}, '%')
            </if>
            <if test="params.status != null">
                AND a.status = #{params.status}
            </if>
            <if test="params.riskLevel != null">
                AND a.risk_level = #{params.riskLevel}
            </if>
            <if test="params.startTime != null">
                AND a.start_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND a.end_time &lt;= #{params.endTime}
            </if>
            <if test="params.creator != null and params.creator != ''">
                AND a.creator = #{params.creator}
            </if>
        </where>
        ORDER BY a.create_time DESC
        <if test="params.pageNum != null and params.pageSize != null">
            LIMIT #{params.pageSize} OFFSET #{params.offset}
        </if>
    </select>

    <select id="getCount" resultType="int">
        SELECT COUNT(1)
        FROM tb_conservation_analysis a
        LEFT JOIN tb_station s ON a.station_id = s.id
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                AND a.tenant_id = #{params.tenantId}
            </if>
            <if test="params.stationId != null and params.stationId != ''">
                AND a.station_id = #{params.stationId}
            </if>
            <if test="params.stationName != null and params.stationName != ''">
                AND s.name LIKE CONCAT('%', #{params.stationName}, '%')
            </if>
            <if test="params.status != null">
                AND a.status = #{params.status}
            </if>
            <if test="params.riskLevel != null">
                AND a.risk_level = #{params.riskLevel}
            </if>
            <if test="params.startTime != null">
                AND a.start_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND a.end_time &lt;= #{params.endTime}
            </if>
            <if test="params.creator != null and params.creator != ''">
                AND a.creator = #{params.creator}
            </if>
        </where>
        ORDER BY a.create_time DESC
        <if test="params.pageNum != null and params.pageSize != null">
            LIMIT #{params.pageSize} OFFSET #{params.offset}
        </if>
    </select>

    <select id="getCount" resultType="int">
        SELECT COUNT(1)
        FROM tb_conservation_analysis a
        LEFT JOIN tb_station s ON a.station_id = s.id
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                AND a.tenant_id = #{params.tenantId}
            </if>
            <if test="params.stationId != null and params.stationId != ''">
                AND a.station_id = #{params.stationId}
            </if>
            <if test="params.stationName != null and params.stationName != ''">
                AND s.name LIKE CONCAT('%', #{params.stationName}, '%')
            </if>
            <if test="params.status != null">
                AND a.status = #{params.status}
            </if>
            <if test="params.riskLevel != null">
                AND a.risk_level = #{params.riskLevel}
            </if>
            <if test="params.algorithmVersion != null and params.algorithmVersion != ''">
                AND a.algorithm_version = #{params.algorithmVersion}
            </if>
            <if test="params.startTime != null">
                AND a.start_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND a.end_time &lt;= #{params.endTime}
            </if>
            <if test="params.creator != null and params.creator != ''">
                AND a.creator = #{params.creator}
            </if>
        </where>
    </select>

    <select id="getLatestAnalysis" resultType="org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationAnalysis">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_conservation_analysis a
        LEFT JOIN tb_station s ON a.station_id = s.id
        LEFT JOIN tb_user u ON a.creator = u.id
        WHERE a.station_id = #{stationId}
        AND a.status = 2
        ORDER BY a.create_time DESC
        LIMIT 1
    </select>

    <select id="getAnalysisByTimeRange" resultType="org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationAnalysis">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_conservation_analysis a
        LEFT JOIN tb_station s ON a.station_id = s.id
        LEFT JOIN tb_user u ON a.creator = u.id
        WHERE a.station_id = #{stationId}
        AND a.start_time >= #{startTime}
        AND a.end_time &lt;= #{endTime}
        AND a.status = 2
        ORDER BY a.start_time ASC
    </select>

    <select id="getRiskLevelStatistics" resultType="map">
        SELECT
            a.risk_level as riskLevel,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
        FROM tb_conservation_analysis a
        WHERE a.tenant_id = #{tenantId}
        AND a.status = 2
        GROUP BY a.risk_level
        ORDER BY a.risk_level
    </select>

    <select id="getConservationPotentialTrend" resultType="map">
        SELECT
            DATE(a.create_time) as analysisDate,
            AVG(a.conservation_potential) as avgPotential,
            AVG(a.conservation_coefficient) as avgCoefficient,
            COUNT(*) as recordCount
        FROM tb_conservation_analysis a
        WHERE a.station_id = #{stationId}
        AND a.start_time >= #{startTime}
        AND a.end_time &lt;= #{endTime}
        AND a.status = 2
        GROUP BY DATE(a.create_time)
        ORDER BY analysisDate ASC
    </select>

</mapper>
