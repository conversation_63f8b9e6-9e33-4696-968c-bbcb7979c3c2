import {
  i
} from "./chunk-3JR5KBYG.js";
import {
  l
} from "./chunk-5JCRZXRL.js";
import {
  e
} from "./chunk-4CHRJPQP.js";
import {
  a
} from "./chunk-W3CLOCDX.js";
import {
  u
} from "./chunk-I7WHRVHF.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  t
} from "./chunk-TUM6KUQZ.js";
import {
  has
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/views/2d/layers/features/support/Tile.js
var n2 = class _n {
  constructor(t2, e2) {
    this.key = new e(0, 0, 0, 0), this.bounds = u(), this.objectIds = /* @__PURE__ */ new Set(), this.key.set(e2);
    const s = t2.getLODInfoAt(this.key);
    this.tileInfoView = t2, this.tileInfoView.getTileBounds(this.bounds, this.key, true), this.resolution = s.resolution, this.scale = s.scale, this.level = s.level;
  }
  get id() {
    return this.key.id;
  }
  get extent() {
    return w.fromBounds(this.bounds, this.tileInfoView.tileInfo.spatialReference);
  }
  get transform() {
    return { originPosition: "upperLeft", scale: [this.resolution, this.resolution], translate: [this.bounds[0], this.bounds[3]] };
  }
  createChildTiles() {
    const e2 = this.key.getChildKeys(), i2 = t.acquire();
    for (let t2 = 0; t2 < e2.length; t2++) i2[t2] = new _n(this.tileInfoView, e2[t2]);
    return i2;
  }
  getQuantizationParameters() {
    return a.fromJSON({ mode: "view", originPosition: "upperLeft", tolerance: this.resolution, extent: { xmin: this.bounds[0], ymin: this.bounds[1], xmax: this.bounds[2], ymax: this.bounds[3], spatialReference: this.tileInfoView.tileInfo.spatialReference } });
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/support/TileStore.js
var h = { added: [], removed: [] };
var n3 = /* @__PURE__ */ new Set();
var r = new e(0, 0, 0, 0);
var d = class extends n {
  constructor(e2) {
    super(), this._tiles = /* @__PURE__ */ new Map(), this._index = i(9, has("esri-csp-restrictions") ? (e3) => ({ minX: e3.bounds[0], minY: e3.bounds[1], maxX: e3.bounds[2], maxY: e3.bounds[3] }) : [".bounds[0]", ".bounds[1]", ".bounds[2]", ".bounds[3]"]), this.tiles = [], this.tileScheme = e2;
  }
  destroy() {
    this.clear();
  }
  clear() {
    this.tiles.length = 0, this._tiles.clear(), this._index.clear();
  }
  has(e2) {
    return this._tiles.has(e2);
  }
  get(e2) {
    return this._tiles.get(e2);
  }
  boundsIntersections(e2) {
    return this._index.search({ minX: e2[0], minY: e2[1], maxX: e2[2], maxY: e2[3] });
  }
  updateTiles(e2) {
    const t2 = { added: [], removed: [] };
    for (const i2 of e2.added) if (!this.has(i2)) {
      const e3 = new n2(this.tileScheme, i2);
      this._tiles.set(i2, e3), this._index.insert(e3), t2.added.push(e3);
    }
    for (const s of e2.removed) if (this.has(s)) {
      const e3 = this.get(s);
      this._tiles.delete(s), this._index.remove(e3), t2.removed.push(e3);
    }
    this.tiles.length = 0, this._tiles.forEach((e3) => this.tiles.push(e3)), (t2.added.length || t2.removed.length) && this.emit("update", t2);
  }
  setViewState(e2) {
    const t2 = this.tileScheme.getTileCoverage(e2, 0);
    if (!t2) return;
    const { spans: o, lodInfo: d2 } = t2, { level: l2 } = d2;
    if (o.length > 0) for (const { row: i2, colFrom: a2, colTo: m } of o) for (let e3 = a2; e3 <= m; e3++) {
      const t3 = r.set(l2, i2, d2.normalizeCol(e3), d2.getWorldForColumn(e3)).id;
      if (n3.add(t3), !this.has(t3)) {
        const e4 = new n2(this.tileScheme, t3);
        this._tiles.set(t3, e4), this._index.insert(e4), this.tiles.push(e4), h.added.push(e4);
      }
    }
    for (let s = this.tiles.length - 1; s >= 0; s--) {
      const e3 = this.tiles[s];
      n3.has(e3.id) || (this._tiles.delete(e3.id), this.tiles.splice(s, 1), this._index.remove(e3), h.removed.push(e3));
    }
    (h.added.length || h.removed.length) && this.emit("update", h), l.pool.release(t2), n3.clear(), h.added.length = 0, h.removed.length = 0;
  }
};

export {
  d
};
//# sourceMappingURL=chunk-MWAJDZK5.js.map
