import {
  t as t2
} from "./chunk-PIQKEGGB.js";
import {
  o as o3
} from "./chunk-N3S5O3YO.js";
import {
  e as e3
} from "./chunk-32BGXH4N.js";
import {
  u as u2
} from "./chunk-SARDHCB4.js";
import {
  e
} from "./chunk-GXMOAZWH.js";
import {
  a as a2
} from "./chunk-6OHWWYET.js";
import {
  o as o4
} from "./chunk-TUB4N6LD.js";
import {
  e2,
  r as r4,
  u
} from "./chunk-YV4RKNU4.js";
import {
  a,
  i
} from "./chunk-LHO3WKNH.js";
import {
  n as n4,
  o as o2
} from "./chunk-RFTQI4ZD.js";
import {
  r as r3
} from "./chunk-UHA44FM7.js";
import {
  N
} from "./chunk-6ZZUUGXX.js";
import {
  O
} from "./chunk-CPQSD22U.js";
import {
  n as n2
} from "./chunk-NOZFLZZL.js";
import {
  n as n3
} from "./chunk-CCAF47ZU.js";
import {
  o
} from "./chunk-MQAXMQFG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import {
  r as r2
} from "./chunk-RQXGVG3K.js";
import {
  C
} from "./chunk-EKX3LLYN.js";
import {
  r,
  t,
  y
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/TextureCoordinateAttribute.glsl.js
var d;
function o5(o9, v2) {
  switch (v2.textureCoordinateType) {
    case d.Default:
      return o9.attributes.add(O.UV0, "vec2"), o9.varyings.add("vuv0", "vec2"), void o9.vertex.code.add(o2`void forwardTextureCoordinates() {
vuv0 = uv0;
}`);
    case d.Compressed:
      return o9.attributes.add(O.UV0, "vec2"), o9.varyings.add("vuv0", "vec2"), void o9.vertex.code.add(o2`vec2 getUV0() {
return uv0 / 16384.0;
}
void forwardTextureCoordinates() {
vuv0 = getUV0();
}`);
    case d.Atlas:
      return o9.attributes.add(O.UV0, "vec2"), o9.varyings.add("vuv0", "vec2"), o9.attributes.add(O.UVREGION, "vec4"), o9.varyings.add("vuvRegion", "vec4"), void o9.vertex.code.add(o2`void forwardTextureCoordinates() {
vuv0 = uv0;
vuvRegion = uvRegion;
}`);
    default:
      n3(v2.textureCoordinateType);
    case d.None:
      return void o9.vertex.code.add(o2`void forwardTextureCoordinates() {}`);
    case d.COUNT:
      return;
  }
}
!function(e7) {
  e7[e7.None = 0] = "None", e7[e7.Default = 1] = "Default", e7[e7.Atlas = 2] = "Atlas", e7[e7.Compressed = 3] = "Compressed", e7[e7.COUNT = 4] = "COUNT";
}(d || (d = {}));

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/TextureAtlasLookup.glsl.js
function t3(t6) {
  t6.extensions.add("GL_EXT_shader_texture_lod"), t6.extensions.add("GL_OES_standard_derivatives"), t6.fragment.code.add(o2`#ifndef GL_EXT_shader_texture_lod
float calcMipMapLevel(const vec2 ddx, const vec2 ddy) {
float deltaMaxSqr = max(dot(ddx, ddx), dot(ddy, ddy));
return max(0.0, 0.5 * log2(deltaMaxSqr));
}
#endif
vec4 textureAtlasLookup(sampler2D texture, vec2 textureSize, vec2 textureCoordinates, vec4 atlasRegion) {
vec2 atlasScale = atlasRegion.zw - atlasRegion.xy;
vec2 uvAtlas = fract(textureCoordinates) * atlasScale + atlasRegion.xy;
float maxdUV = 0.125;
vec2 dUVdx = clamp(dFdx(textureCoordinates), -maxdUV, maxdUV) * atlasScale;
vec2 dUVdy = clamp(dFdy(textureCoordinates), -maxdUV, maxdUV) * atlasScale;
#ifdef GL_EXT_shader_texture_lod
return texture2DGradEXT(texture, uvAtlas, dUVdx, dUVdy);
#else
vec2 dUVdxAuto = dFdx(uvAtlas);
vec2 dUVdyAuto = dFdy(uvAtlas);
float mipMapLevel = calcMipMapLevel(dUVdx * textureSize, dUVdy * textureSize);
float autoMipMapLevel = calcMipMapLevel(dUVdxAuto * textureSize, dUVdyAuto * textureSize);
return texture2D(texture, uvAtlas, mipMapLevel - autoMipMapLevel);
#endif
}`);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/VertexTextureCoordinates.glsl.js
function a3(a6, s2) {
  switch (a6.include(o5, s2), a6.fragment.code.add(o2`
  struct TextureLookupParameter {
    vec2 uv;
    ${s2.supportsTextureAtlas ? "vec2 size;" : ""}
  } vtc;
  `), s2.textureCoordinateType) {
    case d.Default:
    case d.Compressed:
      return void a6.fragment.code.add(o2`vec4 textureLookup(sampler2D texture, TextureLookupParameter params) {
return texture2D(texture, params.uv);
}`);
    case d.Atlas:
      return a6.include(t3), void a6.fragment.code.add(o2`vec4 textureLookup(sampler2D texture, TextureLookupParameter params) {
return textureAtlasLookup(texture, params.size, params.uv, vuvRegion);
}`);
    default:
      n3(s2.textureCoordinateType);
    case d.None:
    case d.COUNT:
      return;
  }
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/GLMaterial.js
var t4 = class {
  constructor(e7) {
    this._material = e7.material, this._techniqueRepository = e7.techniqueRep, this._output = e7.output;
  }
  dispose() {
    this._techniqueRepository.release(this._technique);
  }
  get technique() {
    return this._technique;
  }
  get _stippleTextureRepository() {
    return this._techniqueRepository.constructionContext.stippleTextureRepository;
  }
  ensureTechnique(e7, t6) {
    return this._technique = this._techniqueRepository.releaseAndAcquire(e7, this._material.getConfiguration(this._output, t6), this._technique), this._technique;
  }
  ensureResources(t6) {
    return N.LOADED;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/GLTextureMaterial.js
var h = class extends t4 {
  constructor(t6) {
    super(t6), this._numLoading = 0, this._disposed = false, this._textureRepository = t6.textureRep, this._textureId = t6.textureId, this._acquire(t6.textureId, (t7) => this._texture = t7), this._acquire(t6.normalTextureId, (t7) => this._textureNormal = t7), this._acquire(t6.emissiveTextureId, (t7) => this._textureEmissive = t7), this._acquire(t6.occlusionTextureId, (t7) => this._textureOcclusion = t7), this._acquire(t6.metallicRoughnessTextureId, (t7) => this._textureMetallicRoughness = t7);
  }
  dispose() {
    this._texture = y(this._texture), this._textureNormal = y(this._textureNormal), this._textureEmissive = y(this._textureEmissive), this._textureOcclusion = y(this._textureOcclusion), this._textureMetallicRoughness = y(this._textureMetallicRoughness), this._disposed = true;
  }
  ensureResources(t6) {
    return 0 === this._numLoading ? N.LOADED : N.LOADING;
  }
  get textureBindParameters() {
    return new o6(r(this._texture) ? this._texture.glTexture : null, r(this._textureNormal) ? this._textureNormal.glTexture : null, r(this._textureEmissive) ? this._textureEmissive.glTexture : null, r(this._textureOcclusion) ? this._textureOcclusion.glTexture : null, r(this._textureMetallicRoughness) ? this._textureMetallicRoughness.glTexture : null);
  }
  updateTexture(e7) {
    (t(this._texture) || e7 !== this._texture.id) && (this._texture = y(this._texture), this._textureId = e7, this._acquire(this._textureId, (t6) => this._texture = t6));
  }
  _acquire(e7, r8) {
    if (t(e7)) return void r8(null);
    const u3 = this._textureRepository.acquire(e7);
    if (C(u3)) return ++this._numLoading, void u3.then((e8) => {
      if (this._disposed) return y(e8), void r8(null);
      r8(e8);
    }).finally(() => --this._numLoading);
    r8(u3);
  }
};
var o6 = class extends n4 {
  constructor(t6 = null, e7 = null, s2 = null, i2 = null, r8 = null) {
    super(), this.texture = t6, this.textureNormal = e7, this.textureEmissive = s2, this.textureOcclusion = i2, this.textureMetallicRoughness = r8;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/PhysicallyBasedRenderingParameters.glsl.js
var m = r3(0, 0.6, 0.2);
var d2;
!function(e7) {
  e7[e7.Disabled = 0] = "Disabled", e7[e7.Normal = 1] = "Normal", e7[e7.Schematic = 2] = "Schematic", e7[e7.Water = 3] = "Water", e7[e7.WaterOnIntegratedMesh = 4] = "WaterOnIntegratedMesh", e7[e7.Terrain = 5] = "Terrain", e7[e7.TerrainWithWater = 6] = "TerrainWithWater", e7[e7.COUNT = 7] = "COUNT";
}(d2 || (d2 = {}));
function x(e7, l) {
  const m3 = e7.fragment, p = l.hasMetallicRoughnessTexture || l.hasEmissionTexture || l.hasOcclusionTexture;
  if (l.pbrMode === d2.Normal && p && e7.include(a3, l), l.pbrMode !== d2.Schematic) if (l.pbrMode !== d2.Disabled) {
    if (l.pbrMode === d2.Normal) {
      m3.code.add(o2`vec3 mrr;
vec3 emission;
float occlusion;`);
      const e8 = l.supportsTextureAtlas ? l.hasWebGL2Context ? e2.None : e2.Size : e2.None, s2 = l.pbrTextureBindType;
      l.hasMetallicRoughnessTexture && (m3.uniforms.add(s2 === a.Pass ? u("texMetallicRoughness", (e9) => e9.textureMetallicRoughness, e8) : u2("texMetallicRoughness", (e9) => e9.textureMetallicRoughness, e8)), m3.code.add(o2`void applyMetallnessAndRoughness(TextureLookupParameter params) {
vec3 metallicRoughness = textureLookup(texMetallicRoughness, params).rgb;
mrr[0] *= metallicRoughness.b;
mrr[1] *= metallicRoughness.g;
}`)), l.hasEmissionTexture && (m3.uniforms.add(s2 === a.Pass ? u("texEmission", (e9) => e9.textureEmissive, e8) : u2("texEmission", (e9) => e9.textureEmissive, e8)), m3.code.add(o2`void applyEmission(TextureLookupParameter params) {
emission *= textureLookup(texEmission, params).rgb;
}`)), l.hasOcclusionTexture ? (m3.uniforms.add(s2 === a.Pass ? u("texOcclusion", (e9) => e9.textureOcclusion, e8) : u2("texOcclusion", (e9) => e9.textureOcclusion, e8)), m3.code.add(o2`void applyOcclusion(TextureLookupParameter params) {
occlusion *= textureLookup(texOcclusion, params).r;
}
float getBakedOcclusion() {
return occlusion;
}`)) : m3.code.add(o2`float getBakedOcclusion() { return 1.0; }`), m3.uniforms.add(s2 === a.Pass ? [new e3("emissionFactor", (e9) => e9.emissiveFactor), new e3("mrrFactors", (e9) => e9.mrrFactors)] : [new o3("emissionFactor", (e9) => e9.emissiveFactor), new o3("mrrFactors", (e9) => e9.mrrFactors)]), m3.code.add(o2`
    void applyPBRFactors() {
      mrr = mrrFactors;
      emission = emissionFactor;
      occlusion = 1.0;
      ${p ? o2`vtc.uv = vuv0;` : ""}
      ${l.hasMetallicRoughnessTextureTransform ? o2`vtc.uv = metallicRoughnessUV;` : ""}
      ${l.hasMetallicRoughnessTexture ? l.supportsTextureAtlas ? o2`
                vtc.size = ${r4(l, "texMetallicRoughness")};
                applyMetallnessAndRoughness(vtc);` : o2`applyMetallnessAndRoughness(vtc);` : ""}
      ${l.hasEmissiveTextureTransform ? o2`vtc.uv = emissiveUV;` : ""}
      ${l.hasEmissionTexture ? l.supportsTextureAtlas ? o2`
                vtc.size = ${r4(l, "texEmission")};
                applyEmission(vtc);` : o2`applyEmission(vtc);` : ""}
      ${l.hasOcclusionTextureTransform ? o2`vtc.uv = occlusionUV;` : ""}
      ${l.hasOcclusionTexture ? l.supportsTextureAtlas ? o2`
                vtc.size = ${r4(l, "texOcclusion")};
                applyOcclusion(vtc);` : o2`applyOcclusion(vtc);` : ""}
    }
  `);
    }
  } else m3.code.add(o2`float getBakedOcclusion() { return 1.0; }`);
  else m3.code.add(o2`vec3 mrr = vec3(0.0, 0.6, 0.2);
vec3 emission = vec3(0.0);
float occlusion = 1.0;
void applyPBRFactors() {}
float getBakedOcclusion() { return 1.0; }`);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/EvaluateAmbientLighting.glsl.js
function m2(n6, g) {
  const m3 = n6.fragment, o9 = void 0 !== g.lightingSphericalHarmonicsOrder ? g.lightingSphericalHarmonicsOrder : 2;
  0 === o9 ? (m3.uniforms.add(new e3("lightingAmbientSH0", (n7, t6) => o(a4, t6.lighting.sh.r[0], t6.lighting.sh.g[0], t6.lighting.sh.b[0]))), m3.code.add(o2`vec3 calculateAmbientIrradiance(vec3 normal, float ambientOcclusion) {
vec3 ambientLight = 0.282095 * lightingAmbientSH0;
return ambientLight * (1.0 - ambientOcclusion);
}`)) : 1 === o9 ? (m3.uniforms.add([new e("lightingAmbientSH_R", (i2, n7) => r2(r5, n7.lighting.sh.r[0], n7.lighting.sh.r[1], n7.lighting.sh.r[2], n7.lighting.sh.r[3])), new e("lightingAmbientSH_G", (i2, n7) => r2(r5, n7.lighting.sh.g[0], n7.lighting.sh.g[1], n7.lighting.sh.g[2], n7.lighting.sh.g[3])), new e("lightingAmbientSH_B", (i2, n7) => r2(r5, n7.lighting.sh.b[0], n7.lighting.sh.b[1], n7.lighting.sh.b[2], n7.lighting.sh.b[3]))]), m3.code.add(o2`vec3 calculateAmbientIrradiance(vec3 normal, float ambientOcclusion) {
vec4 sh0 = vec4(
0.282095,
0.488603 * normal.x,
0.488603 * normal.z,
0.488603 * normal.y
);
vec3 ambientLight = vec3(
dot(lightingAmbientSH_R, sh0),
dot(lightingAmbientSH_G, sh0),
dot(lightingAmbientSH_B, sh0)
);
return ambientLight * (1.0 - ambientOcclusion);
}`)) : 2 === o9 && (m3.uniforms.add([new e3("lightingAmbientSH0", (n7, t6) => o(a4, t6.lighting.sh.r[0], t6.lighting.sh.g[0], t6.lighting.sh.b[0])), new e("lightingAmbientSH_R1", (i2, n7) => r2(r5, n7.lighting.sh.r[1], n7.lighting.sh.r[2], n7.lighting.sh.r[3], n7.lighting.sh.r[4])), new e("lightingAmbientSH_G1", (i2, n7) => r2(r5, n7.lighting.sh.g[1], n7.lighting.sh.g[2], n7.lighting.sh.g[3], n7.lighting.sh.g[4])), new e("lightingAmbientSH_B1", (i2, n7) => r2(r5, n7.lighting.sh.b[1], n7.lighting.sh.b[2], n7.lighting.sh.b[3], n7.lighting.sh.b[4])), new e("lightingAmbientSH_R2", (i2, n7) => r2(r5, n7.lighting.sh.r[5], n7.lighting.sh.r[6], n7.lighting.sh.r[7], n7.lighting.sh.r[8])), new e("lightingAmbientSH_G2", (i2, n7) => r2(r5, n7.lighting.sh.g[5], n7.lighting.sh.g[6], n7.lighting.sh.g[7], n7.lighting.sh.g[8])), new e("lightingAmbientSH_B2", (i2, n7) => r2(r5, n7.lighting.sh.b[5], n7.lighting.sh.b[6], n7.lighting.sh.b[7], n7.lighting.sh.b[8]))]), m3.code.add(o2`vec3 calculateAmbientIrradiance(vec3 normal, float ambientOcclusion) {
vec3 ambientLight = 0.282095 * lightingAmbientSH0;
vec4 sh1 = vec4(
0.488603 * normal.x,
0.488603 * normal.z,
0.488603 * normal.y,
1.092548 * normal.x * normal.y
);
vec4 sh2 = vec4(
1.092548 * normal.y * normal.z,
0.315392 * (3.0 * normal.z * normal.z - 1.0),
1.092548 * normal.x * normal.z,
0.546274 * (normal.x * normal.x - normal.y * normal.y)
);
ambientLight += vec3(
dot(lightingAmbientSH_R1, sh1),
dot(lightingAmbientSH_G1, sh1),
dot(lightingAmbientSH_B1, sh1)
);
ambientLight += vec3(
dot(lightingAmbientSH_R2, sh2),
dot(lightingAmbientSH_G2, sh2),
dot(lightingAmbientSH_B2, sh2)
);
return ambientLight * (1.0 - ambientOcclusion);
}`), g.pbrMode !== d2.Normal && g.pbrMode !== d2.Schematic || m3.code.add(o2`const vec3 skyTransmittance = vec3(0.9, 0.9, 1.0);
vec3 calculateAmbientRadiance(float ambientOcclusion)
{
vec3 ambientLight = 1.2 * (0.282095 * lightingAmbientSH0) - 0.2;
return ambientLight *= (1.0 - ambientOcclusion) * skyTransmittance;
}`));
}
var a4 = n();
var r5 = n2();

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/MainLighting.glsl.js
function o7(n6) {
  n6.uniforms.add(new e3("mainLightDirection", (i2, n7) => n7.lighting.mainLight.direction));
}
function a5(n6) {
  n6.uniforms.add(new e3("mainLightIntensity", (i2, n7) => n7.lighting.mainLight.intensity));
}
function e4(i2, t6) {
  t6.useLegacyTerrainShading ? i2.uniforms.add(new o4("lightingFixedFactor", (i3, n6) => n6.lighting.noonFactor * (1 - n6.lighting.globalFactor))) : i2.constants.add("lightingFixedFactor", "float", 0);
}
function r6(i2, n6) {
  const r8 = i2.fragment;
  o7(r8), a5(r8), e4(r8, n6), r8.code.add(o2`vec3 evaluateMainLighting(vec3 normal_global, float shadowing) {
float dotVal = clamp(dot(normal_global, mainLightDirection), 0.0, 1.0);
dotVal = mix(dotVal, 1.0, lightingFixedFactor);
return mainLightIntensity * ((1.0 - shadowing) * dotVal);
}`);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/AnalyticalSkyModel.glsl.js
function t5(t6) {
  const a6 = t6.fragment.code;
  a6.add(o2`vec3 evaluateDiffuseIlluminationHemisphere(vec3 ambientGround, vec3 ambientSky, float NdotNG)
{
return ((1.0 - NdotNG) * ambientGround + (1.0 + NdotNG) * ambientSky) * 0.5;
}`), a6.add(o2`float integratedRadiance(float cosTheta2, float roughness)
{
return (cosTheta2 - 1.0) / (cosTheta2 * (1.0 - roughness * roughness) - 1.0);
}`), a6.add(o2`vec3 evaluateSpecularIlluminationHemisphere(vec3 ambientGround, vec3 ambientSky, float RdotNG, float roughness)
{
float cosTheta2 = 1.0 - RdotNG * RdotNG;
float intRadTheta = integratedRadiance(cosTheta2, roughness);
float ground = RdotNG < 0.0 ? 1.0 - intRadTheta : 1.0 + intRadTheta;
float sky = 2.0 - ground;
return (ground * ambientGround + sky * ambientSky) * 0.5;
}`);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/PhysicallyBasedRendering.glsl.js
function n5(n6, r8) {
  const l = n6.fragment.code;
  n6.include(t2), r8.pbrMode !== d2.Normal && r8.pbrMode !== d2.Schematic && r8.pbrMode !== d2.Terrain && r8.pbrMode !== d2.TerrainWithWater || (l.add(o2`float normalDistribution(float NdotH, float roughness)
{
float a = NdotH * roughness;
float b = roughness / (1.0 - NdotH * NdotH + a * a);
return b * b * INV_PI;
}`), l.add(o2`const vec4 c0 = vec4(-1.0, -0.0275, -0.572,  0.022);
const vec4 c1 = vec4( 1.0,  0.0425,  1.040, -0.040);
const vec2 c2 = vec2(-1.04, 1.04);
vec2 prefilteredDFGAnalytical(float roughness, float NdotV) {
vec4 r = roughness * c0 + c1;
float a004 = min(r.x * r.x, exp2(-9.28 * NdotV)) * r.x + r.y;
return c2 * a004 + r.zw;
}`)), r8.pbrMode !== d2.Normal && r8.pbrMode !== d2.Schematic || (n6.include(t5), l.add(o2`struct PBRShadingInfo
{
float NdotL;
float NdotV;
float NdotH;
float VdotH;
float LdotH;
float NdotNG;
float RdotNG;
float NdotAmbDir;
float NdotH_Horizon;
vec3 skyRadianceToSurface;
vec3 groundRadianceToSurface;
vec3 skyIrradianceToSurface;
vec3 groundIrradianceToSurface;
float averageAmbientRadiance;
float ssao;
vec3 albedoLinear;
vec3 f0;
vec3 f90;
vec3 diffuseColor;
float metalness;
float roughness;
};`), l.add(o2`vec3 evaluateEnvironmentIllumination(PBRShadingInfo inputs) {
vec3 indirectDiffuse = evaluateDiffuseIlluminationHemisphere(inputs.groundIrradianceToSurface, inputs.skyIrradianceToSurface, inputs.NdotNG);
vec3 indirectSpecular = evaluateSpecularIlluminationHemisphere(inputs.groundRadianceToSurface, inputs.skyRadianceToSurface, inputs.RdotNG, inputs.roughness);
vec3 diffuseComponent = inputs.diffuseColor * indirectDiffuse * INV_PI;
vec2 dfg = prefilteredDFGAnalytical(inputs.roughness, inputs.NdotV);
vec3 specularColor = inputs.f0 * dfg.x + inputs.f90 * dfg.y;
vec3 specularComponent = specularColor * indirectSpecular;
return (diffuseComponent + specularComponent);
}`), l.add(o2`float gamutMapChanel(float x, vec2 p){
return (x < p.x) ? mix(0.0, p.y, x/p.x) : mix(p.y, 1.0, (x - p.x) / (1.0 - p.x) );
}`), l.add(o2`vec3 blackLevelSoftCompression(vec3 inColor, PBRShadingInfo inputs){
vec3 outColor;
vec2 p = vec2(0.02 * (inputs.averageAmbientRadiance), 0.0075 * (inputs.averageAmbientRadiance));
outColor.x = gamutMapChanel(inColor.x, p) ;
outColor.y = gamutMapChanel(inColor.y, p) ;
outColor.z = gamutMapChanel(inColor.z, p) ;
return outColor;
}`));
}
function r7(e7, o9) {
  const n6 = e7.fragment.code;
  e7.include(t2), n6.add(o2`
  struct PBRShadingWater
  {
      float NdotL;   // cos angle between normal and light direction
      float NdotV;   // cos angle between normal and view direction
      float NdotH;   // cos angle between normal and half vector
      float VdotH;   // cos angle between view direction and half vector
      float LdotH;   // cos angle between light direction and half vector
      float VdotN;   // cos angle between view direction and normal vector
  };

  float dtrExponent = ${o9.useCustomDTRExponentForWater ? "2.2" : "2.0"};
  `), n6.add(o2`vec3 fresnelReflection(float angle, vec3 f0, float f90) {
return f0 + (f90 - f0) * pow(1.0 - angle, 5.0);
}`), n6.add(o2`float normalDistributionWater(float NdotH, float roughness)
{
float r2 = roughness * roughness;
float NdotH2 = NdotH * NdotH;
float denom = pow((NdotH2 * (r2 - 1.0) + 1.0), dtrExponent) * PI;
return r2 / denom;
}`), n6.add(o2`float geometricOcclusionKelemen(float LoH)
{
return 0.25 / (LoH * LoH);
}`), n6.add(o2`vec3 brdfSpecularWater(in PBRShadingWater props, float roughness, vec3 F0, float F0Max)
{
vec3  F = fresnelReflection(props.VdotH, F0, F0Max);
float dSun = normalDistributionWater(props.NdotH, roughness);
float V = geometricOcclusionKelemen(props.LdotH);
float diffusionSunHaze = mix(roughness + 0.045, roughness + 0.385, 1.0 - props.VdotH);
float strengthSunHaze  = 1.2;
float dSunHaze = normalDistributionWater(props.NdotH, diffusionSunHaze)*strengthSunHaze;
return ((dSun + dSunHaze) * V) * F;
}
vec3 tonemapACES(const vec3 x) {
return (x * (2.51 * x + 0.03)) / (x * (2.43 * x + 0.59) + 0.14);
}`);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderModules/IntegerPassUniform.js
var e5 = class extends i {
  constructor(r8, e7) {
    super(r8, "int", a.Pass, (s2, o9, i2) => s2.setUniform1i(r8, e7(o9, i2)));
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderModules/Matrix4sDrawUniform.js
var o8 = class extends i {
  constructor(r8, o9, s2) {
    super(r8, "mat4", a.Draw, (e7, s3, t6) => e7.setUniformMatrix4fv(r8, o9(s3, t6)), s2);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderModules/Matrix4sPassUniform.js
var e6 = class extends i {
  constructor(r8, e7, o9) {
    super(r8, "mat4", a.Pass, (s2, o10, t6) => s2.setUniformMatrix4fv(r8, e7(o10, t6)), o9);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/ReadShadowMap.glsl.js
function h2(e7, a6) {
  a6.receiveShadows && (e7.fragment.uniforms.add(new e6("shadowMapMatrix", (e8, a7) => a7.shadowMap.getShadowMapMatrices(e8.origin), 4)), f(e7, a6));
}
function v(e7, a6) {
  a6.receiveShadows && (e7.fragment.uniforms.add(new o8("shadowMapMatrix", (e8, a7) => a7.shadowMap.getShadowMapMatrices(e8.origin), 4)), f(e7, a6));
}
function f(e7, i2) {
  const d3 = e7.fragment;
  d3.include(a2), d3.uniforms.add([...u("shadowMapTex", (e8, a6) => a6.shadowMap.depthTexture, i2.hasWebGL2Context ? e2.None : e2.Size), new e5("numCascades", (e8, a6) => a6.shadowMap.numCascades), new e("cascadeDistances", (e8, a6) => a6.shadowMap.cascadeDistances)]), d3.code.add(o2`
    int chooseCascade(float depth, out mat4 mat) {
      vec4 distance = cascadeDistances;

      // choose correct cascade
      int i = depth < distance[1] ? 0 : depth < distance[2] ? 1 : depth < distance[3] ? 2 : 3;

      mat = i == 0 ? shadowMapMatrix[0] : i == 1 ? shadowMapMatrix[1] : i == 2 ? shadowMapMatrix[2] : shadowMapMatrix[3];

      return i;
    }

    vec3 lightSpacePosition(vec3 _vpos, mat4 mat) {
      vec4 lv = mat * vec4(_vpos, 1.0);
      lv.xy /= lv.w;
      return 0.5 * lv.xyz + vec3(0.5);
    }

    vec2 cascadeCoordinates(int i, vec3 lvpos) {
      return vec2(float(i - 2 * (i / 2)) * 0.5, float(i / 2) * 0.5) + 0.5 * lvpos.xy;
    }

    float readShadowMapDepth(vec2 uv, sampler2D _depthTex) {
      return rgba2float(texture2D(_depthTex, uv));
    }

    float posIsInShadow(vec2 uv, vec3 lvpos, sampler2D _depthTex) {
      return readShadowMapDepth(uv, _depthTex) < lvpos.z ? 1.0 : 0.0;
    }

    float filterShadow(vec2 uv, vec3 lvpos, float textureSize, sampler2D _depthTex) {
      float halfPixelSize = 0.5 / textureSize;

      // filter, offset by half pixels
      vec2 st = fract((vec2(halfPixelSize) + uv) * textureSize);

      float s00 = posIsInShadow(uv + vec2(-halfPixelSize, -halfPixelSize), lvpos, _depthTex);
      float s10 = posIsInShadow(uv + vec2(halfPixelSize, -halfPixelSize), lvpos, _depthTex);
      float s11 = posIsInShadow(uv + vec2(halfPixelSize, halfPixelSize), lvpos, _depthTex);
      float s01 = posIsInShadow(uv + vec2(-halfPixelSize, halfPixelSize), lvpos, _depthTex);

      return mix(mix(s00, s10, st.x), mix(s01, s11, st.x), st.y);
    }

    float readShadowMap(const in vec3 _vpos, float _linearDepth) {
      mat4 mat;
      int i = chooseCascade(_linearDepth, mat);

      if (i >= numCascades) { return 0.0; }

      vec3 lvpos = lightSpacePosition(_vpos, mat);

      // vertex completely outside? -> no shadow
      if (lvpos.z >= 1.0) { return 0.0; }
      if (lvpos.x < 0.0 || lvpos.x > 1.0 || lvpos.y < 0.0 || lvpos.y > 1.0) { return 0.0; }

      // calc coord in cascade texture
      vec2 uv = cascadeCoordinates(i, lvpos);

      vec2 textureSize = ${r4(i2, "shadowMapTex")};

      return filterShadow(uv, lvpos, textureSize.x, shadowMapTex);
    }
  `);
}

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderModules/BooleanPassUniform.js
var s = class extends i {
  constructor(o9, s2) {
    super(o9, "bool", a.Pass, (r8, e7, t6) => r8.setUniform1b(o9, s2(e7, t6)));
  }
};

export {
  d,
  o5 as o,
  a3 as a,
  t4 as t,
  h,
  o6 as o2,
  d2,
  x,
  e5 as e,
  m2 as m,
  o7 as o3,
  a5 as a2,
  r6 as r,
  n5 as n,
  r7 as r2,
  s,
  h2,
  v
};
//# sourceMappingURL=chunk-XVTFHFM3.js.map
