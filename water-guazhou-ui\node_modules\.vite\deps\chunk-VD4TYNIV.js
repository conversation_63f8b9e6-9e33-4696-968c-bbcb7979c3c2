import {
  t as t2
} from "./chunk-XX6IKIRW.js";
import {
  i
} from "./chunk-OWSDEANX.js";
import {
  n
} from "./chunk-PCLDCFRI.js";
import {
  R,
  U,
  d as d2,
  m,
  p,
  x
} from "./chunk-XVA5SA7P.js";
import {
  Jt,
  V,
  Y,
  Z
} from "./chunk-U4SVMKOQ.js";
import {
  t3 as t
} from "./chunk-NDCSRZLO.js";
import {
  c,
  d
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/core/accessorSupport/decorators/persistable.js
function g(t3) {
  const r = (t3 == null ? void 0 : t3.origins) ?? [void 0];
  return (e, o) => {
    const s = h(t3, e, o);
    for (const t4 of r) {
      const r2 = d(e, t4, o);
      for (const t5 in s) r2[t5] = s[t5];
    }
  };
}
function h(t3, r, e) {
  if ("resource" === (t3 == null ? void 0 : t3.type)) return v(t3, r, e);
  switch ((t3 == null ? void 0 : t3.type) ?? "other") {
    case "other":
      return { read: true, write: true };
    case "url": {
      const { read: t4, write: r2 } = x;
      return { read: t4, write: r2 };
    }
  }
}
function v(o, s, n2) {
  const p2 = c(s, n2);
  return { type: String, read: (t3, r, e) => {
    const o2 = p(t3, r, e);
    return p2.type === String ? o2 : "function" == typeof p2.type ? new p2.type({ url: o2 }) : void 0;
  }, write: { writer(s2, i2, u, c2) {
    if (!c2 || !c2.resources) return "string" == typeof s2 ? void (i2[u] = m(s2, c2)) : void (i2[u] = s2.write({}, c2));
    const y = x2(s2), d3 = m(y, { ...c2, verifyItemRelativeUrls: c2 && c2.verifyItemRelativeUrls ? { writtenUrls: c2.verifyItemRelativeUrls.writtenUrls, rootPath: void 0 } : void 0 }, R.NO), g2 = p2.type !== String && (!i(this) || c2 && c2.origin && this.originIdOf(n2) > t(c2.origin)), h2 = { object: this, propertyName: n2, value: s2, targetUrl: d3, dest: i2, targetPropertyName: u, context: c2, params: o };
    c2 && c2.portalItem && d3 && !Y(d3) ? g2 ? w(h2) : j(h2) : c2 && c2.portalItem && (null == d3 || null != U(d3) || Z(d3) || g2) ? U2(h2) : i2[u] = d3;
  } } };
}
function U2(t3) {
  const { targetUrl: r, params: s, value: i2, context: a, dest: p2, targetPropertyName: c2 } = t3;
  if (!a.portalItem) return;
  const l = d2(r), m2 = (l == null ? void 0 : l.filename) ?? n(), f = (s == null ? void 0 : s.prefix) ?? (l == null ? void 0 : l.prefix), d3 = N(i2, r, a), g2 = V(f, m2), h2 = `${g2}.${t2(d3)}`, v2 = a.portalItem.resourceFromPath(h2);
  Z(r) && a.resources && a.resources.pendingOperations.push(P(r).then((t4) => {
    v2.path = `${g2}.${t2(t4)}`, p2[c2] = v2.itemRelativeUrl;
  }).catch(() => {
  }));
  const U3 = (s == null ? void 0 : s.compress) ?? false;
  a.resources && I({ ...t3, resource: v2, content: d3, compress: U3, updates: a.resources.toAdd }), p2[c2] = v2.itemRelativeUrl;
}
function w(t3) {
  const { context: r, targetUrl: e, params: o, value: n2, dest: i2, targetPropertyName: a } = t3;
  if (!r.portalItem) return;
  const p2 = r.portalItem.resourceFromPath(e), c2 = N(n2, e, r), l = t2(c2), m2 = Jt(p2.path), f = (o == null ? void 0 : o.compress) ?? false;
  l === m2 ? (r.resources && I({ ...t3, resource: p2, content: c2, compress: f, updates: r.resources.toUpdate }), i2[a] = e) : U2(t3);
}
function j({ context: t3, targetUrl: r, dest: e, targetPropertyName: o }) {
  t3.portalItem && t3.resources && (t3.resources.toKeep.push({ resource: t3.portalItem.resourceFromPath(r), compress: false }), e[o] = r);
}
function I({ object: t3, propertyName: r, updates: e, resource: o, content: s, compress: n2 }) {
  e.push({ resource: o, content: s, compress: n2, finish: (e2) => {
    O(t3, r, e2);
  } });
}
function N(t3, r, e) {
  return "string" == typeof t3 ? { url: r } : new Blob([JSON.stringify(t3.toJSON(e))], { type: "application/json" });
}
async function P(t3) {
  const r = (await import("./@arcgis_core_request__js.js")).default, { data: e } = await r(t3, { responseType: "blob" });
  return e;
}
function x2(t3) {
  return null == t3 ? null : "string" == typeof t3 ? t3 : t3.url;
}
function O(t3, r, e) {
  "string" == typeof t3[r] ? t3[r] = e.url : t3[r].url = e.url;
}

export {
  g
};
//# sourceMappingURL=chunk-VD4TYNIV.js.map
