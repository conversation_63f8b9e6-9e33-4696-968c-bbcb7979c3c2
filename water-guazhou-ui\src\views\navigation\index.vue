<template>
  <div class="navigation-container">
    <canvas id="bg-canvas" class="background-canvas"></canvas>
    
    <div class="btn-1" :class="{ active: isActive.btn1 }" @mouseenter="handleMouseEnter('btn1')" @mouseleave="handleMouseLeave('btn1')" @click="navigateTo('/production-map')">生产一张图</div>
    <div class="btn-2" :class="{ active: isActive.btn2 }" @mouseenter="handleMouseEnter('btn2')" @mouseleave="handleMouseLeave('btn2')" @click="navigateTo('/revenue-map')">营收一张图</div>
    <div class="small-cylinder small-cylinder-1-1" :class="{ active: isActive.smallCylinder11 }" @mouseenter="handleMouseEnter('smallCylinder11')" @mouseleave="handleMouseLeave('smallCylinder11')" @click.stop="navigateTo('/smart-production-1')">
      <div class="small-cylinder-text vertical-text">水源地监管系统</div>
    </div>
    <div class="small-cylinder small-cylinder-1-2" :class="{ active: isActive.smallCylinder12 }" @mouseenter="handleMouseEnter('smallCylinder12')" @mouseleave="handleMouseLeave('smallCylinder12')" @click.stop="navigateTo('/smart-production-2')">
      <div class="small-cylinder-text vertical-text">智慧水厂管理系统</div>
    </div>
    
    <!-- 第二个大圆柱对应的小圆柱 -->
    <div class="small-cylinder small-cylinder-2-1" :class="{ active: isActive.smallCylinder21 }" @mouseenter="handleMouseEnter('smallCylinder21')" @mouseleave="handleMouseLeave('smallCylinder21')" @click.stop="navigateTo('/revenue-1')">
      <div class="small-cylinder-text vertical-text">管网地理信息综合管理系统</div>
    </div>
    <div class="small-cylinder small-cylinder-2-2" :class="{ active: isActive.smallCylinder22 }" @mouseenter="handleMouseEnter('smallCylinder22')" @mouseleave="handleMouseLeave('smallCylinder22')" @click.stop="navigateTo('/revenue-2')">
      <div class="small-cylinder-text vertical-text">管网监控系统</div>
    </div>
    
    <!-- 第三个大圆柱对应的小圆柱 -->
    <div class="small-cylinder small-cylinder-3-1" :class="{ active: isActive.smallCylinder31 }" @mouseenter="handleMouseEnter('smallCylinder31')" @mouseleave="handleMouseLeave('smallCylinder31')" @click.stop="navigateTo('/operation-1')">
      <div class="small-cylinder-text vertical-text">供水管网漏损控制系统</div>
    </div>
    <div class="small-cylinder small-cylinder-3-2" :class="{ active: isActive.smallCylinder32 }" @mouseenter="handleMouseEnter('smallCylinder32')" @mouseleave="handleMouseLeave('smallCylinder32')" @click.stop="navigateTo('/operation-2')">
      <div class="small-cylinder-text vertical-text">营业收费系统</div>
    </div>
    
    <!-- 第四个大圆柱对应的小圆柱 -->
    <div class="small-cylinder small-cylinder-4-1" :class="{ active: isActive.smallCylinder41 }" @mouseenter="handleMouseEnter('smallCylinder41')" @mouseleave="handleMouseLeave('smallCylinder41')" @click.stop="navigateTo('/pipe-1')">
      <div class="small-cylinder-text vertical-text">外业管理系统</div>
    </div>
    <div class="small-cylinder small-cylinder-4-2" :class="{ active: isActive.smallCylinder42 }" @mouseenter="handleMouseEnter('smallCylinder42')" @mouseleave="handleMouseLeave('smallCylinder42')" @click.stop="navigateTo('/pipe-2')">
      <div class="small-cylinder-text vertical-text">公共管理系统</div>
    </div>
    
    <!-- 大圆柱作为背景 -->
    <div class="cylinder-1" :class="{ active: isActive.cylinder1 }" @mouseenter="handleMouseEnter('cylinder1')" @mouseleave="handleMouseLeave('cylinder1')" @click="navigateTo('/production-map')"></div>
    <div class="cylinder-2" :class="{ active: isActive.cylinder2 }" @mouseenter="handleMouseEnter('cylinder2')" @mouseleave="handleMouseLeave('cylinder2')" @click="navigateTo('/revenue-map')"></div>
    <div class="cylinder-3" :class="{ active: isActive.cylinder3 }" @mouseenter="handleMouseEnter('cylinder3')" @mouseleave="handleMouseLeave('cylinder3')" @click="navigateTo('/smart-operation')"></div>
    <div class="cylinder-4" :class="{ active: isActive.cylinder4 }" @mouseenter="handleMouseEnter('cylinder4')" @mouseleave="handleMouseLeave('cylinder4')" @click="navigateTo('/smart-pipe')"></div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { initBackgroundEffect, EffectType } from './effects';

const router = useRouter();

// 特效清理函数
let cleanupEffect = null;

// 按钮状态
const isActive = reactive({
  cylinder1: false,
  cylinder2: false,
  cylinder3: false,
  cylinder4: false,
  smallCylinder11: false,
  smallCylinder12: false,
  smallCylinder21: false,
  smallCylinder22: false,
  smallCylinder31: false,
  smallCylinder32: false,
  smallCylinder41: false,
  smallCylinder42: false,
  btn1: false,
  btn2: false
});

// 鼠标进入按钮
const handleMouseEnter = (id) => {
  isActive[id] = true;
};

// 鼠标离开按钮
const handleMouseLeave = (id) => {
  isActive[id] = false;
};

// 导航到指定路径
const navigateTo = (path) => {
  console.log('导航到:', path);
  router.push(path);
};

onMounted(() => {
  console.log('组件已挂载');
  
  // 初始化特效 - 只使用水面粒子特效
  cleanupEffect = initBackgroundEffect(EffectType.WATER_PARTICLES, 'bg-canvas');
});

onUnmounted(() => {
  // 清理特效
  if (cleanupEffect) {
    cleanupEffect();
  }
});
</script>

<style lang="scss" scoped>
.navigation-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background: url('./img/bg.svg') no-repeat;
  background-size: cover;
  
  .background-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none; // 确保不影响点击事件
    opacity: 0.8;
  }
  
  .btn-1, .btn-2 {
    position: absolute;
    font-family: 'MStiffHeiPRC-Bold', sans-serif;
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    z-index: 3;
    width: 265px;
    height: 76px;
    background: url('./img/btn.svg') no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      background: url('./img/btn-active.svg') no-repeat;
      background-size: cover;
      transform: scale(1.05);
    }
  }
  
  .btn-1 {
    top: 285px;
    left: 600px;
  }
  
  .btn-2 {
    top: 285px;
    left: 1056px;
  }
  
  /* 大圆柱样式 */
  .cylinder-1{
    position: absolute;
    height: 410px;
    width: 208px;
    background: url('./img/cylinder.svg') no-repeat;
    background-size: cover;
    top: 454px;
    left: 478px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .cylinder-2{
    position: absolute;
    height: 410px;
    width: 208px;
    background: url('./img/cylinder.svg') no-repeat;
    background-size: cover;
    top: 468px;
    left: 726px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .cylinder-3{
    position: absolute;
    height: 410px;
    width: 208px;
    background: url('./img/cylinder.svg') no-repeat;
    background-size: cover;
    top: 468px;
    left: 971px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .cylinder-4{
    position: absolute;
    height: 410px;
    width: 208px;
    background: url('./img/cylinder.svg') no-repeat;
    background-size: cover;
    top: 454px;
    left: 1234px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  /* 小圆柱的通用样式 */
  .small-cylinder {
    position: absolute;
    height: 338px;
    width: 94px;
    background: url('./img/smallCylinder.svg') no-repeat;
    background-size: contain;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
  }
  
  /* 为每个小圆柱单独设置位置，形成弧线 */
  .small-cylinder-1-1 {
    top: 488px;
    left: 478px;
  }
  
  .small-cylinder-1-2 {
    top: 528px;
    left: 593px;
  }
  
  .small-cylinder-2-1 {
    top: 548px;
    left: 726px;
  }
  
  .small-cylinder-2-2 {
    top: 558px;
    left: 841px;
  }
  
  .small-cylinder-3-1 {
    top: 558px;
    left: 971px;
  }
  
  .small-cylinder-3-2 {
    top: 548px;
    left: 1086px;
  }
  
  .small-cylinder-4-1 {
    top: 528px;
    left: 1234px;
  }
  
  .small-cylinder-4-2 {
    top: 488px;
    left: 1349px;
  }
  
  /* 小圆柱文字样式 */
  .small-cylinder-text {
    font-family: 'MStiffHeiPRC-Bold', sans-serif;
    font-size: 22px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    text-align: center;
  }
  
  /* 纵向文字样式 */
  .vertical-text {
    writing-mode: vertical-lr;
    text-orientation: upright;
    letter-spacing: 4px;
  }
  
  /* 大圆柱激活状态 */
  .active{
    background: url('./img/cylinder_active.svg') no-repeat;
    background-size: cover;
    transform: scale(1.05);
  }
  
  /* 小圆柱激活状态 */
  .small-cylinder.active {
    background: url('./img/smallCylinder_active.svg') no-repeat;
    background-size: contain;
    transform: scale(1.05);
  }
}
</style> 