import {
  i,
  p
} from "./chunk-I25FGXOD.js";
import "./chunk-3LXNA5FS.js";
import {
  n,
  r as r2,
  r2 as r3
} from "./chunk-TNXJBJO3.js";
import "./chunk-FAMLZKHJ.js";
import "./chunk-NMGVJ2ZX.js";
import "./chunk-INH5JU5P.js";
import {
  f2 as f3,
  l2
} from "./chunk-UQWZJZ2S.js";
import "./chunk-5S4W3ME5.js";
import {
  y as y3
} from "./chunk-CDZ24ELJ.js";
import "./chunk-VHLK35TF.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-3WUI7ZKG.js";
import {
  a as a2,
  h,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  f as f2,
  y as y2
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e as e2,
  f,
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/FeatureCollectionSnappingSource.js
var w = class extends v {
  get availability() {
    return 1;
  }
  get updating() {
    return this.layerSource.updating;
  }
  get _snappingElevationAligner() {
    const { view: e3 } = this, { layer: t } = this.layerSource, r4 = r(e3) && "3d" === e3.type;
    if (!r4 || "subtype-group" === t.type) return r2();
    const o = async (i2, r5) => (await y2(e3.whenLayerView(t), r5)).elevationAlignPointsInFeatures(i2, r5);
    return r2(r4, { elevationInfo: t.elevationInfo, alignPointsInFeatures: o, spatialReference: e3.spatialReference });
  }
  get _snappingElevationFilter() {
    const { view: e3 } = this, t = r(e3) && "3d" === e3.type && "subtype-group" !== this.layerSource.layer.type;
    return r3(t);
  }
  get _symbologySnappingFetcher() {
    const { view: e3 } = this, { layer: t } = this.layerSource;
    return r(e3) && "3d" === e3.type && "subtype-group" !== t.type ? n(this._symbologySnappingSupported, async (i2, r4) => {
      const o = await e3.whenLayerView(t);
      return f2(r4), o.queryForSymbologySnapping({ candidates: i2, spatialReference: e3.spatialReference }, r4);
    }) : n();
  }
  get _symbologySnappingSupported() {
    return r(this._layerView3D) && this._layerView3D.symbologySnappingSupported;
  }
  initialize() {
    const { view: e3 } = this, { layer: t } = this.layerSource;
    r(e3) && "3d" === e3.type && "subtype-group" !== t.type && (e3.whenLayerView(t).then((e4) => this._layerView3D = e4), this.addHandles([e3.elevationProvider.on("elevation-change", ({ context: e4 }) => {
      const { elevationInfo: i2 } = t;
      y3(e4, i2) && this._snappingElevationAligner.notifyElevationSourceChange();
    }), l(() => t.elevationInfo, () => this._snappingElevationAligner.notifyElevationSourceChange(), h), l(() => {
      var _a;
      return r(this._layerView3D) ? (_a = this._layerView3D.processor) == null ? void 0 : _a.renderer : null;
    }, () => this._symbologySnappingFetcher.notifySymbologyChange(), h), a2(() => {
      var _a;
      return (_a = f(this._layerView3D)) == null ? void 0 : _a.layer;
    }, ["edits", "apply-edits", "graphic-update"], () => this._symbologySnappingFetcher.notifySymbologyChange())]));
  }
  constructor(e3) {
    super(e3), this.view = null, this._layerView3D = null;
  }
  refresh() {
  }
  async fetchCandidates(e3, t) {
    var _a;
    const { layer: i2 } = this.layerSource, r4 = i2.source;
    if (!(r4 == null ? void 0 : r4.querySnapping)) return [];
    const a3 = f3(i2), s = l2(e3, ((_a = e2(this.view)) == null ? void 0 : _a.type) ?? "2d", a3), p2 = await r4.querySnapping(s, { signal: t });
    f2(t);
    const l3 = await this._snappingElevationAligner.alignCandidates(p2.candidates, t);
    f2(t);
    const y4 = await this._symbologySnappingFetcher.fetch(l3, t);
    f2(t);
    const c = 0 === y4.length ? l3 : [...l3, ...y4], g = this._snappingElevationFilter.filter(s, c), d = this._getGroundElevation;
    return g.map((e4) => i(e4, d));
  }
  get _getGroundElevation() {
    return p(this.view);
  }
};
e([y({ constructOnly: true })], w.prototype, "layerSource", void 0), e([y({ constructOnly: true })], w.prototype, "view", void 0), e([y()], w.prototype, "_snappingElevationAligner", null), e([y()], w.prototype, "_snappingElevationFilter", null), e([y()], w.prototype, "_symbologySnappingFetcher", null), e([y()], w.prototype, "_layerView3D", void 0), e([y()], w.prototype, "_symbologySnappingSupported", null), e([y()], w.prototype, "_getGroundElevation", null), w = e([a("esri.views.interactive.snapping.featureSources.FeatureCollectionSnappingSource")], w);
export {
  w as FeatureCollectionSnappingSource
};
//# sourceMappingURL=FeatureCollectionSnappingSource-77J6AVCO.js.map
