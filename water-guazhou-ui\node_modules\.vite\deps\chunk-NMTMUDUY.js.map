{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/BitmapContainer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{brushes as e}from\"./brushes.js\";import{WGLDrawPhase as s}from\"./webgl/enums.js\";import r from\"./webgl/WGLContainer.js\";class a extends r{constructor(){super(...arguments),this._hasCrossfade=!1}get requiresDedicatedFBO(){return this._hasCrossfade}beforeRender(e){super.beforeRender(e),this._manageFade()}prepareRenderPasses(r){const a=r.registerRenderPass({name:\"bitmap\",brushes:[e.bitmap],target:()=>this.children,drawPhase:s.MAP});return[...super.prepareRenderPasses(r),a]}_manageFade(){this.children.reduce(((e,s)=>e+(s.inFadeTransition?1:0)),0)>=2?(this.children.forEach((e=>e.blendFunction=\"additive\")),this._hasCrossfade=!0):(this.children.forEach((e=>e.blendFunction=\"standard\")),this._hasCrossfade=!1)}}export{a as BitmapContainer};\n"], "mappings": ";;;;;;;;;AAI8H,IAAMA,KAAN,cAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc;AAAA,EAAE;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,aAAa,GAAE;AAAC,UAAM,aAAa,CAAC,GAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,oBAAoB,GAAE;AAAC,UAAMA,KAAE,EAAE,mBAAmB,EAAC,MAAK,UAAS,SAAQ,CAAC,EAAE,MAAM,GAAE,QAAO,MAAI,KAAK,UAAS,WAAU,EAAE,IAAG,CAAC;AAAE,WAAM,CAAC,GAAG,MAAM,oBAAoB,CAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,SAAK,SAAS,OAAQ,CAAC,GAAE,MAAI,KAAG,EAAE,mBAAiB,IAAE,IAAI,CAAC,KAAG,KAAG,KAAK,SAAS,QAAS,OAAG,EAAE,gBAAc,UAAW,GAAE,KAAK,gBAAc,SAAK,KAAK,SAAS,QAAS,OAAG,EAAE,gBAAc,UAAW,GAAE,KAAK,gBAAc;AAAA,EAAG;AAAC;", "names": ["a"]}