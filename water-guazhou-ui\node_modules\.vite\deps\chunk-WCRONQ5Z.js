import {
  o
} from "./chunk-L7J6WAZK.js";
import {
  ee
} from "./chunk-AOYBG2OC.js";
import {
  E2,
  f as f2
} from "./chunk-6G2NLXT7.js";
import {
  r as r2
} from "./chunk-UHA44FM7.js";
import {
  r as r3
} from "./chunk-QKWIBVLD.js";
import {
  C,
  E,
  F,
  I,
  R
} from "./chunk-4M3AMTD4.js";
import {
  t
} from "./chunk-DUEDINK5.js";
import {
  e
} from "./chunk-MZ267CZB.js";
import {
  M,
  f,
  h as h2,
  i,
  r
} from "./chunk-ST2RRB55.js";
import {
  U
} from "./chunk-Z2LHI3D7.js";
import {
  a,
  h
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/layers/graphics/GraphicBoundsRenderer.js
var v = Math.PI / 180;
var x = 4;
var b = class extends r3 {
  constructor(t2) {
    super(), this._program = null, this._vao = null, this._vertexBuffer = null, this._indexBuffer = null, this._dvsMat3 = e(), this._localOrigin = { x: 0, y: 0 }, this._getBounds = t2;
  }
  destroy() {
    this._vao && (this._vao.dispose(true), this._vao = null, this._vertexBuffer = null, this._indexBuffer = null), this._program = h(this._program);
  }
  doRender(t2) {
    const { context: e2 } = t2, r4 = this._getBounds();
    if (r4.length < 1) return;
    this._createShaderProgram(e2), this._updateMatricesAndLocalOrigin(t2), this._updateBufferData(e2, r4), e2.setBlendingEnabled(true), e2.setDepthTestEnabled(false), e2.setStencilWriteMask(0), e2.setStencilTestEnabled(false), e2.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), e2.setColorMask(true, true, true, true);
    const s = this._program;
    e2.bindVAO(this._vao), e2.useProgram(s), s.setUniformMatrix3fv("u_dvsMat3", this._dvsMat3), e2.gl.lineWidth(1), e2.drawElements(E.LINES, 8 * r4.length, C.UNSIGNED_INT, 0), e2.bindVAO();
  }
  _createTransforms() {
    return { dvs: e() };
  }
  _createShaderProgram(t2) {
    if (this._program) return;
    const e2 = "precision highp float;\n        uniform mat3 u_dvsMat3;\n\n        attribute vec2 a_position;\n\n        void main() {\n          mediump vec3 pos = u_dvsMat3 * vec3(a_position, 1.0);\n          gl_Position = vec4(pos.xy, 0.0, 1.0);\n        }", r4 = "precision mediump float;\n      void main() {\n        gl_FragColor = vec4(0.75, 0.0, 0.0, 0.75);\n      }";
    this._program = t2.programCache.acquire(e2, r4, y().attributes);
  }
  _updateMatricesAndLocalOrigin(t2) {
    const { state: a2 } = t2, { displayMat3: u, size: _, resolution: c, pixelRatio: h3, rotation: m, viewpoint: d } = a2, p = v * m, { x: g, y: x2 } = d.targetGeometry, b2 = U(g, a2.spatialReference);
    this._localOrigin.x = b2, this._localOrigin.y = x2;
    const y2 = h3 * _[0], B = h3 * _[1], M2 = c * y2, j = c * B, A = r(this._dvsMat3);
    i(A, A, u), M(A, A, t(y2 / 2, B / 2)), f(A, A, r2(_[0] / M2, -B / j, 1)), h2(A, A, -p);
  }
  _updateBufferData(t2, e2) {
    const { x: r4, y: s } = this._localOrigin, i2 = 2 * x * e2.length, o2 = new Float32Array(i2), a2 = new Uint32Array(8 * e2.length);
    let n2 = 0, f3 = 0;
    for (const l of e2) l && (o2[2 * n2 + 0] = l[0] - r4, o2[2 * n2 + 1] = l[1] - s, o2[2 * n2 + 2] = l[0] - r4, o2[2 * n2 + 3] = l[3] - s, o2[2 * n2 + 4] = l[2] - r4, o2[2 * n2 + 5] = l[3] - s, o2[2 * n2 + 6] = l[2] - r4, o2[2 * n2 + 7] = l[1] - s, a2[f3 + 0] = n2 + 0, a2[f3 + 1] = n2 + 3, a2[f3 + 2] = n2 + 3, a2[f3 + 3] = n2 + 2, a2[f3 + 4] = n2 + 2, a2[f3 + 5] = n2 + 1, a2[f3 + 6] = n2 + 1, a2[f3 + 7] = n2 + 0, n2 += 4, f3 += 8);
    if (this._vertexBuffer ? this._vertexBuffer.setData(o2.buffer) : this._vertexBuffer = E2.createVertex(t2, F.DYNAMIC_DRAW, o2.buffer), this._indexBuffer ? this._indexBuffer.setData(a2) : this._indexBuffer = E2.createIndex(t2, F.DYNAMIC_DRAW, a2), !this._vao) {
      const e3 = y();
      this._vao = new f2(t2, e3.attributes, e3.bufferLayouts, { geometry: this._vertexBuffer }, this._indexBuffer);
    }
  }
};
var y = () => ee("bounds", { geometry: [{ location: 0, name: "a_position", count: 2, type: C.FLOAT }] });

// node_modules/@arcgis/core/views/2d/layers/graphics/BaseGraphicContainer.js
var n = class extends o {
  constructor(e2) {
    super(e2), this.hasHighlight = () => true;
  }
  destroy() {
    super.destroy(), this._boundsRenderer = a(this._boundsRenderer);
  }
  enableRenderingBounds(e2) {
    this._boundsRenderer = new b(e2), this.requestRender();
  }
  get hasLabels() {
    return false;
  }
  onTileData(e2, t2) {
    e2.patch(t2), this.contains(e2) || this.addChild(e2), this.requestRender();
  }
  onTileError(e2) {
    e2.clear(), this.contains(e2) || this.addChild(e2);
  }
  _renderChildren(e2, t2) {
    for (const r4 of this.children) r4.isReady && r4.hasData && (r4.commit(e2), e2.context.setStencilFunction(I.EQUAL, r4.stencilRef, 255), r4.getDisplayList().replay(e2, r4, t2));
  }
};

export {
  n
};
//# sourceMappingURL=chunk-WCRONQ5Z.js.map
