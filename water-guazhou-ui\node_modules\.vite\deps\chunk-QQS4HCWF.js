import {
  e
} from "./chunk-AOYBG2OC.js";
import {
  U
} from "./chunk-34BE5ZRD.js";
import {
  E,
  S
} from "./chunk-WAPZ634R.js";
import {
  r as r2,
  s as s2
} from "./chunk-RRNRSHX3.js";
import {
  C
} from "./chunk-4M3AMTD4.js";
import {
  u as u2
} from "./chunk-2ILOD42U.js";
import {
  u
} from "./chunk-G5KX4JSG.js";
import {
  l
} from "./chunk-T23PB46T.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/engine/webgl/techniques/Technique.js
var e2 = class {
  static getStorageSpec(t) {
    return null;
  }
  static createOrUpdateRendererSchema(e3, r3) {
    return r(e3) && "default" === e3.type ? e3 : { type: "default" };
  }
  static getVariation(t) {
    return {};
  }
  static getVariationHash(t) {
    return 0;
  }
};
e2.type = "default", e2.programSpec = null;

// node_modules/@arcgis/core/views/2d/engine/webgl/techniques/dotDensity/TechniqueDotDensity.js
var n = class extends e2 {
  static getStorageSpec({ attributes: e3 }) {
    return { visualVariables: false, attributes: e3 ?? null };
  }
  static _createRendererSchema() {
    return { type: "dot-density", colors: new Float32Array(32), dotValue: -1, dotSize: -1, dotScale: -1, dotBlending: false, backgroundColor: new Float32Array(4), activeDots: new Float32Array(8), seed: -1 };
  }
  static createOrUpdateRendererSchema(r3, a) {
    const { attributes: n3, dotValue: i, referenceScale: d, dotSize: l3, dotBlendingEnabled: s4, seed: c2, backgroundColor: u3 } = a, m = r(r3) && "dot-density" === r3.type ? r3 : this._createRendererSchema();
    m.dotValue = i, m.dotSize = l3, m.dotScale = d, m.dotBlending = s4, m.seed = c2;
    const { colors: g, activeDots: p2, backgroundColor: y } = m;
    for (let e3 = 0; e3 < r2; e3++) {
      const o = e3 >= n3.length ? null : n3[e3].color;
      e(g, o, 4 * e3);
    }
    for (let e3 = 0; e3 < 8; e3++) p2[e3] = e3 < a.attributes.length ? 1 : 0;
    return e(y, u3), m;
  }
  static getVariation(e3) {
    return { ddDotBlending: e3.dotBlending };
  }
  static getVariationHash(e3) {
    return e3.dotBlending ? 1 : 0;
  }
};
n.type = "dot-density", n.programSpec = { shader: "materials/fill", vertexLayout: { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.SHORT }, { location: 1, name: "a_id", count: 3, type: C.UNSIGNED_BYTE }, { location: 2, name: "a_bitset", count: 1, type: C.UNSIGNED_BYTE }, { location: 3, name: "a_inverseArea", count: 1, type: C.FLOAT }] } };

// node_modules/@arcgis/core/views/2d/engine/webgl/techniques/heatmap/TechniqueHeatmap.js
var n2 = class extends e2 {
  static getStorageSpec({ field: e3, valueExpression: t }) {
    return { visualVariables: false, attributes: e3 || t ? [{ field: e3, valueExpression: t }] : null };
  }
  static _createRendererSchema() {
    return { type: "heatmap", radius: -1, referenceScale: -1, isFieldActive: 0, minDensity: -1, densityRange: -1, kernel: null, gradient: null, gradientHash: "invalid" };
  }
  static createOrUpdateRendererSchema(a, i) {
    const { radius: n3, minDensity: s4, maxDensity: o, referenceScale: c2, field: l3, valueExpression: m, colorStops: p2 } = i, d = o - s4, u3 = l3 || m ? 1 : 0, y = p2.map(({ color: e3, ratio: t }) => `${t}:${e3.toString()}`).join();
    let h, S2 = true;
    return r(a) && "heatmap" === a.type ? (h = a, S2 = y !== a.gradientHash) : h = this._createRendererSchema(), h.radius = u(n3), h.minDensity = s4, h.densityRange = d, h.referenceScale = c2, h.isFieldActive = u3, S2 && (h.gradient = u2(p2), h.gradientHash = y), h;
  }
};
n2.type = "heatmap", n2.programSpec = { shader: "materials/icon/heatmapAccumulate", vertexLayout: { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.SHORT }, { location: 1, name: "a_vertexOffset", count: 2, type: C.SHORT }, { location: 4, name: "a_id", count: 4, type: C.UNSIGNED_BYTE }] } };

// node_modules/@arcgis/core/views/2d/engine/webgl/techniques/pieChart/TechniquePieChart.js
var l2 = class extends e2 {
  static getStorageSpec({ attributes: e3 }) {
    return { visualVariables: true, attributes: e3 ?? null };
  }
  static _createRendererSchema() {
    return { type: "pie-chart", colors: new Float32Array(4 * s2), defaultColor: new Float32Array(4), othersColor: new Float32Array(4), outlineColor: new Float32Array(4), holePercentage: 0, sectorThreshold: 0, outlineWidth: 1, numberOfFields: 10 };
  }
  static createOrUpdateRendererSchema(n3, i) {
    const { attributes: l3, defaultColor: s4, holePercentage: c2, othersCategory: m, outline: u3 } = i, d = r(n3) && "pie-chart" === n3.type ? n3 : this._createRendererSchema();
    for (let t = 0; t < s2; t++) {
      const o = t >= l3.length ? new l([0, 0, 0, 0]) : l3[t].color;
      e(d.colors, o, 4 * t);
    }
    return e(d.defaultColor, s4), e(d.othersColor, m == null ? void 0 : m.color), e(d.outlineColor, u3 == null ? void 0 : u3.color), d.outlineWidth = u((u3 == null ? void 0 : u3.width) || 0), d.holePercentage = c2, d.sectorThreshold = (m == null ? void 0 : m.threshold) || 0, d.numberOfFields = l3.length, d;
  }
  static getVariation(e3) {
    return { numberOfFields: e3.numberOfFields };
  }
  static getVariationHash(e3) {
    return e3.numberOfFields;
  }
};
l2.type = "pie-chart", l2.programSpec = { shader: "materials/pie", vertexLayout: { geometry: [{ location: 0, name: "a_pos", count: 2, type: C.SHORT }, { location: 1, name: "a_vertexOffset", count: 2, type: C.SHORT }, { location: 2, name: "a_texCoords", count: 2, type: C.UNSIGNED_SHORT }, { location: 3, name: "a_bitSetAndDistRatio", count: 2, type: C.UNSIGNED_SHORT }, { location: 4, name: "a_id", count: 4, type: C.UNSIGNED_BYTE }, { location: 5, name: "a_color", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 6, name: "a_outlineColor", count: 4, type: C.UNSIGNED_BYTE, normalized: true }, { location: 7, name: "a_sizeAndOutlineWidth", count: 4, type: C.UNSIGNED_BYTE }, { location: 8, name: "a_zoomRange", count: 2, type: C.UNSIGNED_SHORT }] }, hittestAttributes: ["a_vertexOffset", "a_texCoords"] };

// node_modules/@arcgis/core/views/2d/engine/webgl/techniques/utils.js
function s3(r3, t) {
  if (r3.type !== t) throw new s("material-view-model:unexpected-renderer-schema", `expected to find renderer schema of type "${t}" but found type "${r3.type}"`);
}
function c(e3) {
  switch (e3 == null ? void 0 : e3.type) {
    case "dot-density":
      return n;
    case "heatmap":
      return n2;
    case "pie-chart":
      return l2;
    default:
      return e2;
  }
}
function p(e3) {
  const { geometryType: s4, symbologyType: c2 } = U.load(e3);
  switch (s4) {
    case E.FILL:
      if (c2 === S.DOT_DENSITY) return n;
      break;
    case E.MARKER:
      switch (c2) {
        case S.HEATMAP:
          return n2;
        case S.PIE_CHART:
          return l2;
      }
  }
  return e2;
}

export {
  e2 as e,
  s3 as s,
  c,
  p
};
//# sourceMappingURL=chunk-QQS4HCWF.js.map
