import {
  l as l2
} from "./chunk-T23PB46T.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/symbols/Symbol.js
var p = new s({ esriSMS: "simple-marker", esriPMS: "picture-marker", esriSLS: "simple-line", esriSFS: "simple-fill", esriPFS: "picture-fill", esriTS: "text", esriSHD: "shield-label-symbol", PointSymbol3D: "point-3d", LineSymbol3D: "line-3d", PolygonSymbol3D: "polygon-3d", WebStyleSymbol: "web-style", MeshSymbol3D: "mesh-3d", LabelSymbol3D: "label-3d", CIMSymbolReference: "cim" });
var c = 0;
var m = class extends l {
  constructor(r) {
    super(r), this.id = "sym" + c++, this.type = null, this.color = new l2([0, 0, 0, 1]);
  }
  readColor(r) {
    return r && null != r[0] ? [r[0], r[1], r[2], r[3] / 255] : r;
  }
  async collectRequiredFields(r, o2) {
  }
  hash() {
    return JSON.stringify(this.toJSON());
  }
  clone() {
  }
};
e([y({ type: p.apiValues, readOnly: true, json: { read: false, write: { ignoreOrigin: true, writer: p.write } } })], m.prototype, "type", void 0), e([y({ type: l2, json: { write: { allowNull: true } } })], m.prototype, "color", void 0), e([o("color")], m.prototype, "readColor", null), m = e([a("esri.symbols.Symbol")], m);
var a2 = m;

export {
  a2 as a
};
//# sourceMappingURL=chunk-QMG7GZIF.js.map
