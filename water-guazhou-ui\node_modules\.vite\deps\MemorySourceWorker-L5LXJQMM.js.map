{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/sources/support/MemorySourceWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/Error.js\";import{isNone as t,isSome as i}from\"../../../../core/maybe.js\";import{getJsonType as s,isPoint as r}from\"../../../../geometry/support/jsonUtils.js\";import{WGS84 as n}from\"../../../../geometry/support/spatialReferenceUtils.js\";import{convertFromFeatures as a,convertToFeature as o,convertFromFeature as l}from\"../../featureConversionUtils.js\";import{initialObjectId as d,findLastObjectIdFromFeatures as u}from\"../../objectIdUtils.js\";import p from\"../../data/FeatureStore.js\";import{checkProjectionSupport as f,project as c}from\"../../data/projectionSupport.js\";import{QueryEngine as y}from\"../../data/QueryEngine.js\";import{createDefaultAttributesFunction as m,createDrawingInfo as h,createDefaultTemplate as g}from\"./clientSideDefaults.js\";import{loadGeometryEngineForSimplify as I,createFeatureEditErrorResult as b,mixAttributes as F,createFeatureEditSuccessResult as j,simplify as _}from\"./sourceUtils.js\";import E from\"../../../support/FieldsIndex.js\";import{kebabDict as x}from\"../../../support/fieldType.js\";import{getFieldDefaultValue as T}from\"../../../support/fieldUtils.js\";const R=n,q={xmin:-180,ymin:-90,xmax:180,ymax:90,spatialReference:n},w={hasAttachments:!1,capabilities:\"query, editing, create, delete, update\",useStandardizedQueries:!0,supportsCoordinatesQuantization:!0,supportsReturningQueryGeometry:!0,advancedQueryCapabilities:{supportsQueryAttachments:!1,supportsStatistics:!0,supportsPercentileStatistics:!0,supportsReturningGeometryCentroid:!0,supportsQueryWithDistance:!0,supportsDistinct:!0,supportsReturningQueryExtent:!0,supportsReturningGeometryProperties:!1,supportsHavingClause:!0,supportsOrderBy:!0,supportsPagination:!0,supportsQueryWithResultType:!1,supportsSqlExpression:!0,supportsDisjointSpatialRel:!0}};function D(e){return r(e)?null!=e.z:!!e.hasZ}function O(e){return r(e)?null!=e.m:!!e.hasM}class S{constructor(){this._queryEngine=null,this._nextObjectId=null}destroy(){this._queryEngine&&this._queryEngine&&this._queryEngine.destroy(),this._queryEngine=this._fieldsIndex=this._createDefaultAttributes=null}async load(t){const i=[],{features:s}=t,r=this._inferLayerProperties(s,t.fields),n=t.fields||[],a=null!=t.hasM?t.hasM:!!r.hasM,o=null!=t.hasZ?t.hasZ:!!r.hasZ,l=!t.spatialReference&&!r.spatialReference,c=l?R:t.spatialReference||r.spatialReference,I=l?q:null,b=t.geometryType||r.geometryType,F=!b;let j=t.objectIdField||r.objectIdField,_=t.timeInfo;if(!F&&(l&&i.push({name:\"feature-layer:spatial-reference-not-found\",message:\"Spatial reference not provided or found in features. Defaults to WGS84\"}),!b))throw new e(\"feature-layer:missing-property\",\"geometryType not set and couldn't be inferred from the provided features\");if(!j)throw new e(\"feature-layer:missing-property\",\"objectIdField not set and couldn't be found in the provided fields\");if(r.objectIdField&&j!==r.objectIdField&&(i.push({name:\"feature-layer:duplicated-oid-field\",message:`Provided objectIdField \"${j}\" doesn't match the field name \"${r.objectIdField}\", found in the provided fields`}),j=r.objectIdField),j&&!r.objectIdField){let e=null;n.some((t=>t.name===j&&(e=t,!0)))?(e.type=\"esriFieldTypeOID\",e.editable=!1,e.nullable=!1):n.unshift({alias:j,name:j,type:\"esriFieldTypeOID\",editable:!1,nullable:!1})}for(const d of n){if(null==d.name&&(d.name=d.alias),null==d.alias&&(d.alias=d.name),!d.name)throw new e(\"feature-layer:invalid-field-name\",\"field name is missing\",{field:d});if(d.name===j&&(d.type=\"esriFieldTypeOID\"),!x.jsonValues.includes(d.type))throw new e(\"feature-layer:invalid-field-type\",`invalid type for field \"${d.name}\"`,{field:d})}const D={};for(const e of n)if(\"esriFieldTypeOID\"!==e.type&&\"esriFieldTypeGlobalID\"!==e.type){const t=T(e);void 0!==t&&(D[e.name]=t)}if(this._fieldsIndex=new E(n),this._createDefaultAttributes=m(D,j),_){if(_.startTimeField){const e=this._fieldsIndex.get(_.startTimeField);e?(_.startTimeField=e.name,e.type=\"esriFieldTypeDate\"):_.startTimeField=null}if(_.endTimeField){const e=this._fieldsIndex.get(_.endTimeField);e?(_.endTimeField=e.name,e.type=\"esriFieldTypeDate\"):_.endTimeField=null}if(_.trackIdField){const e=this._fieldsIndex.get(_.trackIdField);e?_.trackIdField=e.name:(_.trackIdField=null,i.push({name:\"feature-layer:invalid-timeInfo-trackIdField\",message:\"trackIdField is missing\",details:{timeInfo:_}}))}_.startTimeField||_.endTimeField||(i.push({name:\"feature-layer:invalid-timeInfo\",message:\"startTimeField and endTimeField are missing or invalid\",details:{timeInfo:_}}),_=null)}const O={warnings:i,featureErrors:[],layerDefinition:{...w,drawingInfo:h(b),templates:g(D),extent:I,geometryType:b,objectIdField:j,fields:n,hasZ:o,hasM:a,timeInfo:_},assignedObjectIds:{}};if(this._queryEngine=new y({fields:n,geometryType:b,hasM:a,hasZ:o,objectIdField:j,spatialReference:c,featureStore:new p({geometryType:b,hasM:a,hasZ:o}),timeInfo:_,cacheSpatialQueries:!0}),!s||!s.length)return this._nextObjectId=d,O;const S=u(j,s);return this._nextObjectId=S+1,await f(s,c),this._loadInitialFeatures(O,s)}async applyEdits(e){const{spatialReference:t,geometryType:i}=this._queryEngine;return await Promise.all([I(t,i),f(e.adds,t),f(e.updates,t)]),this._applyEdits(e)}queryFeatures(e,t={}){return this._queryEngine.executeQuery(e,t.signal)}queryFeatureCount(e,t={}){return this._queryEngine.executeQueryForCount(e,t.signal)}queryObjectIds(e,t={}){return this._queryEngine.executeQueryForIds(e,t.signal)}queryExtent(e,t={}){return this._queryEngine.executeQueryForExtent(e,t.signal)}querySnapping(e,t={}){return this._queryEngine.executeQueryForSnapping(e,t.signal)}_inferLayerProperties(e,i){let r,n,a=null,o=null,l=null;for(const d of e){const e=d.geometry;if(!t(e)&&(a||(a=s(e)),o||(o=e.spatialReference),null==r&&(r=D(e)),null==n&&(n=O(e)),a&&o&&null!=r&&null!=n))break}if(i&&i.length){let e=null;i.some((t=>{const i=\"esriFieldTypeOID\"===t.type,s=!t.type&&t.name&&\"objectid\"===t.name.toLowerCase();return e=t,i||s}))&&(l=e.name)}return{geometryType:a,spatialReference:o,objectIdField:l,hasM:n,hasZ:r}}async _loadInitialFeatures(e,t){const{geometryType:r,hasM:n,hasZ:o,objectIdField:l,spatialReference:d,featureStore:u}=this._queryEngine,p=[];for(const a of t){if(null!=a.uid&&(e.assignedObjectIds[a.uid]=-1),a.geometry&&r!==s(a.geometry)){e.featureErrors.push(b(\"Incorrect geometry type.\"));continue}const t=this._createDefaultAttributes(),n=F(this._fieldsIndex,t,a.attributes,!0,e.warnings);n?e.featureErrors.push(n):(this._assignObjectId(t,a.attributes,!0),a.attributes=t,null!=a.uid&&(e.assignedObjectIds[a.uid]=a.attributes[l]),i(a.geometry)&&(a.geometry=c(a.geometry,a.geometry.spatialReference,d)),p.push(a))}u.addMany(a([],p,r,o,n,l));const{fullExtent:f,timeExtent:y}=await this._queryEngine.fetchRecomputedExtents();if(e.layerDefinition.extent=f,y){const{start:t,end:i}=y;e.layerDefinition.timeInfo.timeExtent=[t,i]}return e}async _applyEdits(e){const{adds:t,updates:i,deletes:s}=e,r={addResults:[],deleteResults:[],updateResults:[],uidToObjectId:{}};if(t&&t.length&&this._applyAddEdits(r,t),i&&i.length&&this._applyUpdateEdits(r,i),s&&s.length){for(const e of s)r.deleteResults.push(j(e));this._queryEngine.featureStore.removeManyById(s)}const{fullExtent:n,timeExtent:a}=await this._queryEngine.fetchRecomputedExtents();return{extent:n,timeExtent:a,featureEditResults:r}}_applyAddEdits(e,t){const{addResults:r}=e,{geometryType:n,hasM:o,hasZ:l,objectIdField:d,spatialReference:u,featureStore:p}=this._queryEngine,f=[];for(const a of t){if(a.geometry&&n!==s(a.geometry)){r.push(b(\"Incorrect geometry type.\"));continue}const t=this._createDefaultAttributes(),o=F(this._fieldsIndex,t,a.attributes);if(o)r.push(o);else{if(this._assignObjectId(t,a.attributes),a.attributes=t,null!=a.uid){const t=a.attributes[d];e.uidToObjectId[a.uid]=t}if(i(a.geometry)){const e=a.geometry.spatialReference??u;a.geometry=c(_(a.geometry,e),e,u)}f.push(a),r.push(j(a.attributes[d]))}}p.addMany(a([],f,n,l,o,d))}_applyUpdateEdits({updateResults:e},t){const{geometryType:r,hasM:n,hasZ:a,objectIdField:d,spatialReference:u,featureStore:p}=this._queryEngine;for(const f of t){const{attributes:t,geometry:y}=f,m=t&&t[d];if(null==m){e.push(b(`Identifier field ${d} missing`));continue}if(!p.has(m)){e.push(b(`Feature with object id ${m} missing`));continue}const h=o(p.getFeature(m),r,a,n);if(i(y)){if(r!==s(y)){e.push(b(\"Incorrect geometry type.\"));continue}const t=y.spatialReference??u;h.geometry=c(_(y,t),t,u)}if(t){const i=F(this._fieldsIndex,h.attributes,t);if(i){e.push(i);continue}}p.add(l(h,r,a,n,d)),e.push(j(m))}}_assignObjectId(e,t,i=!1){const s=this._queryEngine.objectIdField;i&&t&&isFinite(t[s])?e[s]=t[s]:e[s]=this._nextObjectId++}}export{S as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIqmC,IAAM,IAAE;AAAR,IAAU,IAAE,EAAC,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,MAAK,IAAG,kBAAiB,EAAC;AAAnE,IAAqEA,KAAE,EAAC,gBAAe,OAAG,cAAa,0CAAyC,wBAAuB,MAAG,iCAAgC,MAAG,gCAA+B,MAAG,2BAA0B,EAAC,0BAAyB,OAAG,oBAAmB,MAAG,8BAA6B,MAAG,mCAAkC,MAAG,2BAA0B,MAAG,kBAAiB,MAAG,8BAA6B,MAAG,qCAAoC,OAAG,sBAAqB,MAAG,iBAAgB,MAAG,oBAAmB,MAAG,6BAA4B,OAAG,uBAAsB,MAAG,4BAA2B,KAAE,EAAC;AAAE,SAAS,EAAE,GAAE;AAAC,SAAOC,GAAE,CAAC,IAAE,QAAM,EAAE,IAAE,CAAC,CAAC,EAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,SAAOA,GAAE,CAAC,IAAE,QAAM,EAAE,IAAE,CAAC,CAAC,EAAE;AAAI;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,eAAa,MAAK,KAAK,gBAAc;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,SAAK,gBAAc,KAAK,gBAAc,KAAK,aAAa,QAAQ,GAAE,KAAK,eAAa,KAAK,eAAa,KAAK,2BAAyB;AAAA,EAAI;AAAA,EAAC,MAAM,KAAKC,IAAE;AAAC,UAAMC,KAAE,CAAC,GAAE,EAAC,UAASF,GAAC,IAAEC,IAAEE,KAAE,KAAK,sBAAsBH,IAAEC,GAAE,MAAM,GAAEG,KAAEH,GAAE,UAAQ,CAAC,GAAEI,KAAE,QAAMJ,GAAE,OAAKA,GAAE,OAAK,CAAC,CAACE,GAAE,MAAKG,KAAE,QAAML,GAAE,OAAKA,GAAE,OAAK,CAAC,CAACE,GAAE,MAAK,IAAE,CAACF,GAAE,oBAAkB,CAACE,GAAE,kBAAiBI,KAAE,IAAE,IAAEN,GAAE,oBAAkBE,GAAE,kBAAiB,IAAE,IAAE,IAAE,MAAK,IAAEF,GAAE,gBAAcE,GAAE,cAAa,IAAE,CAAC;AAAE,QAAI,IAAEF,GAAE,iBAAeE,GAAE,eAAc,IAAEF,GAAE;AAAS,QAAG,CAAC,MAAI,KAAGC,GAAE,KAAK,EAAC,MAAK,6CAA4C,SAAQ,yEAAwE,CAAC,GAAE,CAAC,GAAG,OAAM,IAAI,EAAE,kCAAiC,0EAA0E;AAAE,QAAG,CAAC,EAAE,OAAM,IAAI,EAAE,kCAAiC,oEAAoE;AAAE,QAAGC,GAAE,iBAAe,MAAIA,GAAE,kBAAgBD,GAAE,KAAK,EAAC,MAAK,sCAAqC,SAAQ,2BAA2B,CAAC,mCAAmCC,GAAE,aAAa,kCAAiC,CAAC,GAAE,IAAEA,GAAE,gBAAe,KAAG,CAACA,GAAE,eAAc;AAAC,UAAI,IAAE;AAAK,MAAAC,GAAE,KAAM,CAAAH,OAAGA,GAAE,SAAO,MAAI,IAAEA,IAAE,KAAI,KAAG,EAAE,OAAK,oBAAmB,EAAE,WAAS,OAAG,EAAE,WAAS,SAAIG,GAAE,QAAQ,EAAC,OAAM,GAAE,MAAK,GAAE,MAAK,oBAAmB,UAAS,OAAG,UAAS,MAAE,CAAC;AAAA,IAAC;AAAC,eAAU,KAAKA,IAAE;AAAC,UAAG,QAAM,EAAE,SAAO,EAAE,OAAK,EAAE,QAAO,QAAM,EAAE,UAAQ,EAAE,QAAM,EAAE,OAAM,CAAC,EAAE,KAAK,OAAM,IAAI,EAAE,oCAAmC,yBAAwB,EAAC,OAAM,EAAC,CAAC;AAAE,UAAG,EAAE,SAAO,MAAI,EAAE,OAAK,qBAAoB,CAAC,EAAE,WAAW,SAAS,EAAE,IAAI,EAAE,OAAM,IAAI,EAAE,oCAAmC,2BAA2B,EAAE,IAAI,KAAI,EAAC,OAAM,EAAC,CAAC;AAAA,IAAC;AAAC,UAAMI,KAAE,CAAC;AAAE,eAAU,KAAKJ,GAAE,KAAG,uBAAqB,EAAE,QAAM,4BAA0B,EAAE,MAAK;AAAC,YAAMH,KAAE,EAAE,CAAC;AAAE,iBAASA,OAAIO,GAAE,EAAE,IAAI,IAAEP;AAAA,IAAE;AAAC,QAAG,KAAK,eAAa,IAAIE,GAAEC,EAAC,GAAE,KAAK,2BAAyBF,GAAEM,IAAE,CAAC,GAAE,GAAE;AAAC,UAAG,EAAE,gBAAe;AAAC,cAAM,IAAE,KAAK,aAAa,IAAI,EAAE,cAAc;AAAE,aAAG,EAAE,iBAAe,EAAE,MAAK,EAAE,OAAK,uBAAqB,EAAE,iBAAe;AAAA,MAAI;AAAC,UAAG,EAAE,cAAa;AAAC,cAAM,IAAE,KAAK,aAAa,IAAI,EAAE,YAAY;AAAE,aAAG,EAAE,eAAa,EAAE,MAAK,EAAE,OAAK,uBAAqB,EAAE,eAAa;AAAA,MAAI;AAAC,UAAG,EAAE,cAAa;AAAC,cAAM,IAAE,KAAK,aAAa,IAAI,EAAE,YAAY;AAAE,YAAE,EAAE,eAAa,EAAE,QAAM,EAAE,eAAa,MAAKN,GAAE,KAAK,EAAC,MAAK,+CAA8C,SAAQ,2BAA0B,SAAQ,EAAC,UAAS,EAAC,EAAC,CAAC;AAAA,MAAE;AAAC,QAAE,kBAAgB,EAAE,iBAAeA,GAAE,KAAK,EAAC,MAAK,kCAAiC,SAAQ,0DAAyD,SAAQ,EAAC,UAAS,EAAC,EAAC,CAAC,GAAE,IAAE;AAAA,IAAK;AAAC,UAAMO,KAAE,EAAC,UAASP,IAAE,eAAc,CAAC,GAAE,iBAAgB,EAAC,GAAGH,IAAE,aAAY,EAAE,CAAC,GAAE,WAAU,EAAES,EAAC,GAAE,QAAO,GAAE,cAAa,GAAE,eAAc,GAAE,QAAOJ,IAAE,MAAKE,IAAE,MAAKD,IAAE,UAAS,EAAC,GAAE,mBAAkB,CAAC,EAAC;AAAE,QAAG,KAAK,eAAa,IAAI,GAAE,EAAC,QAAOD,IAAE,cAAa,GAAE,MAAKC,IAAE,MAAKC,IAAE,eAAc,GAAE,kBAAiBC,IAAE,cAAa,IAAIG,GAAE,EAAC,cAAa,GAAE,MAAKL,IAAE,MAAKC,GAAC,CAAC,GAAE,UAAS,GAAE,qBAAoB,KAAE,CAAC,GAAE,CAACN,MAAG,CAACA,GAAE,OAAO,QAAO,KAAK,gBAAcC,IAAEQ;AAAE,UAAME,KAAE,EAAE,GAAEX,EAAC;AAAE,WAAO,KAAK,gBAAcW,KAAE,GAAE,MAAM,EAAEX,IAAEO,EAAC,GAAE,KAAK,qBAAqBE,IAAET,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,WAAW,GAAE;AAAC,UAAK,EAAC,kBAAiBC,IAAE,cAAaC,GAAC,IAAE,KAAK;AAAa,WAAO,MAAM,QAAQ,IAAI,CAAC,EAAED,IAAEC,EAAC,GAAE,EAAE,EAAE,MAAKD,EAAC,GAAE,EAAE,EAAE,SAAQA,EAAC,CAAC,CAAC,GAAE,KAAK,YAAY,CAAC;AAAA,EAAC;AAAA,EAAC,cAAc,GAAEA,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,aAAa,aAAa,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,kBAAkB,GAAEA,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,aAAa,qBAAqB,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,eAAe,GAAEA,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,aAAa,mBAAmB,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,YAAY,GAAEA,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,aAAa,sBAAsB,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,cAAc,GAAEA,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,aAAa,wBAAwB,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,sBAAsB,GAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE,MAAKC,KAAE,MAAK,IAAE;AAAK,eAAU,KAAK,GAAE;AAAC,YAAMM,KAAE,EAAE;AAAS,UAAG,CAAC,EAAEA,EAAC,MAAIP,OAAIA,KAAEE,GAAEK,EAAC,IAAGN,OAAIA,KAAEM,GAAE,mBAAkB,QAAMT,OAAIA,KAAE,EAAES,EAAC,IAAG,QAAMR,OAAIA,KAAE,EAAEQ,EAAC,IAAGP,MAAGC,MAAG,QAAMH,MAAG,QAAMC,IAAG;AAAA,IAAK;AAAC,QAAGF,MAAGA,GAAE,QAAO;AAAC,UAAIU,KAAE;AAAK,MAAAV,GAAE,KAAM,CAAAD,OAAG;AAAC,cAAMC,KAAE,uBAAqBD,GAAE,MAAKD,KAAE,CAACC,GAAE,QAAMA,GAAE,QAAM,eAAaA,GAAE,KAAK,YAAY;AAAE,eAAOW,KAAEX,IAAEC,MAAGF;AAAA,MAAC,CAAE,MAAI,IAAEY,GAAE;AAAA,IAAK;AAAC,WAAM,EAAC,cAAaP,IAAE,kBAAiBC,IAAE,eAAc,GAAE,MAAKF,IAAE,MAAKD,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqB,GAAEF,IAAE;AAAC,UAAK,EAAC,cAAaE,IAAE,MAAKC,IAAE,MAAKE,IAAE,eAAc,GAAE,kBAAiB,GAAE,cAAa,EAAC,IAAE,KAAK,cAAa,IAAE,CAAC;AAAE,eAAUD,MAAKJ,IAAE;AAAC,UAAG,QAAMI,GAAE,QAAM,EAAE,kBAAkBA,GAAE,GAAG,IAAE,KAAIA,GAAE,YAAUF,OAAII,GAAEF,GAAE,QAAQ,GAAE;AAAC,UAAE,cAAc,KAAKA,GAAE,0BAA0B,CAAC;AAAE;AAAA,MAAQ;AAAC,YAAMJ,KAAE,KAAK,yBAAyB,GAAEG,KAAE,EAAE,KAAK,cAAaH,IAAEI,GAAE,YAAW,MAAG,EAAE,QAAQ;AAAE,MAAAD,KAAE,EAAE,cAAc,KAAKA,EAAC,KAAG,KAAK,gBAAgBH,IAAEI,GAAE,YAAW,IAAE,GAAEA,GAAE,aAAWJ,IAAE,QAAMI,GAAE,QAAM,EAAE,kBAAkBA,GAAE,GAAG,IAAEA,GAAE,WAAW,CAAC,IAAG,EAAEA,GAAE,QAAQ,MAAIA,GAAE,WAAS,EAAEA,GAAE,UAASA,GAAE,SAAS,kBAAiB,CAAC,IAAG,EAAE,KAAKA,EAAC;AAAA,IAAE;AAAC,MAAE,QAAQ,GAAE,CAAC,GAAE,GAAEF,IAAEG,IAAEF,IAAE,CAAC,CAAC;AAAE,UAAK,EAAC,YAAWS,IAAE,YAAW,EAAC,IAAE,MAAM,KAAK,aAAa,uBAAuB;AAAE,QAAG,EAAE,gBAAgB,SAAOA,IAAE,GAAE;AAAC,YAAK,EAAC,OAAMZ,IAAE,KAAIC,GAAC,IAAE;AAAE,QAAE,gBAAgB,SAAS,aAAW,CAACD,IAAEC,EAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,MAAM,YAAY,GAAE;AAAC,UAAK,EAAC,MAAKD,IAAE,SAAQC,IAAE,SAAQF,GAAC,IAAE,GAAEG,KAAE,EAAC,YAAW,CAAC,GAAE,eAAc,CAAC,GAAE,eAAc,CAAC,GAAE,eAAc,CAAC,EAAC;AAAE,QAAGF,MAAGA,GAAE,UAAQ,KAAK,eAAeE,IAAEF,EAAC,GAAEC,MAAGA,GAAE,UAAQ,KAAK,kBAAkBC,IAAED,EAAC,GAAEF,MAAGA,GAAE,QAAO;AAAC,iBAAUY,MAAKZ,GAAE,CAAAG,GAAE,cAAc,KAAKU,GAAED,EAAC,CAAC;AAAE,WAAK,aAAa,aAAa,eAAeZ,EAAC;AAAA,IAAC;AAAC,UAAK,EAAC,YAAWI,IAAE,YAAWC,GAAC,IAAE,MAAM,KAAK,aAAa,uBAAuB;AAAE,WAAM,EAAC,QAAOD,IAAE,YAAWC,IAAE,oBAAmBF,GAAC;AAAA,EAAC;AAAA,EAAC,eAAe,GAAEF,IAAE;AAAC,UAAK,EAAC,YAAWE,GAAC,IAAE,GAAE,EAAC,cAAaC,IAAE,MAAKE,IAAE,MAAK,GAAE,eAAc,GAAE,kBAAiB,GAAE,cAAa,EAAC,IAAE,KAAK,cAAaO,KAAE,CAAC;AAAE,eAAUR,MAAKJ,IAAE;AAAC,UAAGI,GAAE,YAAUD,OAAIG,GAAEF,GAAE,QAAQ,GAAE;AAAC,QAAAF,GAAE,KAAKE,GAAE,0BAA0B,CAAC;AAAE;AAAA,MAAQ;AAAC,YAAMJ,KAAE,KAAK,yBAAyB,GAAEK,KAAE,EAAE,KAAK,cAAaL,IAAEI,GAAE,UAAU;AAAE,UAAGC,GAAE,CAAAH,GAAE,KAAKG,EAAC;AAAA,WAAM;AAAC,YAAG,KAAK,gBAAgBL,IAAEI,GAAE,UAAU,GAAEA,GAAE,aAAWJ,IAAE,QAAMI,GAAE,KAAI;AAAC,gBAAMJ,KAAEI,GAAE,WAAW,CAAC;AAAE,YAAE,cAAcA,GAAE,GAAG,IAAEJ;AAAA,QAAC;AAAC,YAAG,EAAEI,GAAE,QAAQ,GAAE;AAAC,gBAAMO,KAAEP,GAAE,SAAS,oBAAkB;AAAE,UAAAA,GAAE,WAAS,EAAEK,GAAEL,GAAE,UAASO,EAAC,GAAEA,IAAE,CAAC;AAAA,QAAC;AAAC,QAAAC,GAAE,KAAKR,EAAC,GAAEF,GAAE,KAAKU,GAAER,GAAE,WAAW,CAAC,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,MAAE,QAAQ,GAAE,CAAC,GAAEQ,IAAET,IAAE,GAAEE,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkB,EAAC,eAAc,EAAC,GAAEL,IAAE;AAAC,UAAK,EAAC,cAAaE,IAAE,MAAKC,IAAE,MAAKC,IAAE,eAAc,GAAE,kBAAiB,GAAE,cAAa,EAAC,IAAE,KAAK;AAAa,eAAUQ,MAAKZ,IAAE;AAAC,YAAK,EAAC,YAAWA,IAAE,UAAS,EAAC,IAAEY,IAAEC,KAAEb,MAAGA,GAAE,CAAC;AAAE,UAAG,QAAMa,IAAE;AAAC,UAAE,KAAKT,GAAE,oBAAoB,CAAC,UAAU,CAAC;AAAE;AAAA,MAAQ;AAAC,UAAG,CAAC,EAAE,IAAIS,EAAC,GAAE;AAAC,UAAE,KAAKT,GAAE,0BAA0BS,EAAC,UAAU,CAAC;AAAE;AAAA,MAAQ;AAAC,YAAM,IAAE,GAAE,EAAE,WAAWA,EAAC,GAAEX,IAAEE,IAAED,EAAC;AAAE,UAAG,EAAE,CAAC,GAAE;AAAC,YAAGD,OAAII,GAAE,CAAC,GAAE;AAAC,YAAE,KAAKF,GAAE,0BAA0B,CAAC;AAAE;AAAA,QAAQ;AAAC,cAAMJ,KAAE,EAAE,oBAAkB;AAAE,UAAE,WAAS,EAAES,GAAE,GAAET,EAAC,GAAEA,IAAE,CAAC;AAAA,MAAC;AAAC,UAAGA,IAAE;AAAC,cAAMC,KAAE,EAAE,KAAK,cAAa,EAAE,YAAWD,EAAC;AAAE,YAAGC,IAAE;AAAC,YAAE,KAAKA,EAAC;AAAE;AAAA,QAAQ;AAAA,MAAC;AAAC,QAAE,IAAI,GAAE,GAAEC,IAAEE,IAAED,IAAE,CAAC,CAAC,GAAE,EAAE,KAAKS,GAAEC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgB,GAAEb,IAAEC,KAAE,OAAG;AAAC,UAAMF,KAAE,KAAK,aAAa;AAAc,IAAAE,MAAGD,MAAG,SAASA,GAAED,EAAC,CAAC,IAAE,EAAEA,EAAC,IAAEC,GAAED,EAAC,IAAE,EAAEA,EAAC,IAAE,KAAK;AAAA,EAAe;AAAC;", "names": ["w", "s", "t", "i", "r", "n", "a", "o", "c", "D", "O", "g", "S", "e", "f", "m"]}