{"version": 3, "sources": ["../../@arcgis/core/views/draw/support/Reshape.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import t from\"../../../Graphic.js\";import{createTask as i}from\"../../../core/asyncUtils.js\";import s from\"../../../core/Collection.js\";import o from\"../../../core/Error.js\";import r from\"../../../core/Evented.js\";import n from\"../../../core/Handles.js\";import a from\"../../../core/Logger.js\";import{destroyMaybe as h,isNone as p,isSome as c,abortMaybe as l,get as d}from\"../../../core/maybe.js\";import{zeroMeters as m}from\"../../../core/quantityUtils.js\";import{when as v,pausable as y,watch as _,syncAndInitial as g}from\"../../../core/reactiveUtils.js\";import{createScreenPoint as u}from\"../../../core/screenUtils.js\";import{property as f}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as x}from\"../../../core/accessorSupport/decorators/subclass.js\";import{closeRingsAndFixWinding as G,geometryToCoordinates as b,getMidpoint as V}from\"../../../geometry/support/coordsUtils.js\";import w from\"../../../layers/GraphicsLayer.js\";import M from\"../../../symbols/SimpleMarkerSymbol.js\";import{ViewingMode as S}from\"../../ViewingMode.js\";import{SnappingVisualizer2D as E}from\"../../2d/interactive/SnappingVisualizer2D.js\";import{cloneMove as k}from\"./drawUtils.js\";import I from\"./GraphicMover.js\";import O from\"./HighlightHelper.js\";import{addUniqueLayer as C}from\"./layerUtils.js\";import{settings as j}from\"./settings.js\";import{ViewEventPriorities as T}from\"../../input/InputManager.js\";import{SKETCH_KEYS as R}from\"../../interactive/keybindings.js\";import{EditGeometryOperations as H}from\"../../interactive/editGeometry/EditGeometryOperations.js\";import U from\"../../interactive/sketch/SketchTooltipOptions.js\";import{SnappingContext as A}from\"../../interactive/snapping/SnappingContext.js\";import{Tooltip as L}from\"../../interactive/tooltip/Tooltip.js\";import{TranslateGraphicTooltipInfo as F,TranslateVertexTooltipInfo as D}from\"../../interactive/tooltip/TranslateTooltipInfos.js\";import{autoArea2D as z}from\"../../support/automaticAreaMeasurementUtils.js\";import{autoDistanceBetweenPoints2D as P,autoLength2D as q}from\"../../support/automaticLengthMeasurementUtils.js\";import K from\"../../../geometry/Polyline.js\";import N from\"../../../geometry/Point.js\";class B{constructor(e,t,i){this.graphic=e,this.mover=t,this.selected=i,this.type=\"reshape-start\"}}class J{constructor(e,t,i){this.graphic=e,this.mover=t,this.selected=i,this.type=\"reshape\"}}class Q{constructor(e,t,i){this.graphic=e,this.mover=t,this.selected=i,this.type=\"reshape-stop\"}}class W{constructor(e,t,i){this.mover=e,this.dx=t,this.dy=i,this.type=\"move-start\"}}class X{constructor(e,t,i){this.mover=e,this.dx=t,this.dy=i,this.type=\"move\"}}class Y{constructor(e,t,i){this.mover=e,this.dx=t,this.dy=i,this.type=\"move-stop\"}}class Z{constructor(e){this.added=e,this.type=\"vertex-select\"}}class ${constructor(e){this.removed=e,this.type=\"vertex-deselect\"}}class ee{constructor(e,t,i,s){this.added=e,this.graphic=t,this.oldGraphic=i,this.vertices=s,this.type=\"vertex-add\"}}class te{constructor(e,t,i,s){this.removed=e,this.graphic=t,this.oldGraphic=i,this.vertices=s,this.type=\"vertex-remove\"}}const ie=j.reshapeGraphics,se={vertices:{default:new M({style:\"circle\",size:ie.vertex.size,color:ie.vertex.color,outline:{color:ie.vertex.outlineColor,width:1}}),hover:new M({style:\"circle\",size:ie.vertex.hoverSize,color:ie.vertex.hoverColor,outline:{color:ie.vertex.hoverOutlineColor,width:1}}),selected:new M({style:\"circle\",size:ie.selected.size,color:ie.selected.color,outline:{color:ie.selected.outlineColor,width:1}})},midpoints:{default:new M({style:\"circle\",size:ie.midpoint.size,color:ie.midpoint.color,outline:{color:ie.midpoint.outlineColor,width:1}}),hover:new M({style:\"circle\",size:ie.midpoint.size,color:ie.midpoint.color,outline:{color:ie.midpoint.outlineColor,width:1}})}};let oe=class extends r.EventedAccessor{constructor(e){super(e),this._activeOperationInfo=null,this._editGeometryOperations=null,this._handles=new n,this._graphicAttributes={esriSketchTool:\"box\"},this._mover=null,this._snappingContext=null,this._snappingTask=null,this._stagedVertex=null,this._tooltip=null,this._viewHandles=new n,this.callbacks={onReshapeStart(){},onReshape(){},onReshapeStop(){},onMoveStart(){},onMove(){},onMoveStop(){},onGraphicClick(){}},this.enableMidpoints=!0,this.enableMovement=!0,this.enableVertices=!0,this.graphic=null,this.layer=null,this.midpointGraphics=new s,this.midpointSymbol=new M({style:\"circle\",size:6,color:[200,200,200],outline:{color:[100,100,100],width:1}}),this.selectedVertices=[],this.snappingManager=null,this.tooltipOptions=new U,this.type=\"reshape\",this.vertexGraphics=new s,this.view=null}initialize(){const e=this.view;this._highlightHelper=new O({view:e}),this._setup(),this._handles.add([v((()=>e?.ready),(()=>{const{layer:e,view:t}=this;C(t,e),this._viewHandles.add(t.on(\"key-down\",(e=>this._keyDownHandler(e)),T.TOOL))}),{once:!0,initial:!0}),y((()=>this.graphic),(()=>this.refresh())),y((()=>this.layer),((e,t)=>{t&&(this._clearSelection(),this._resetGraphics(t)),this.refresh()})),y((()=>this.enableMidpoints),(()=>this.refresh())),_((()=>this.tooltipOptions.enabled),(e=>{this._tooltip=e?new L({view:this.view}):h(this._tooltip)}),g)])}destroy(){this._reset(),this._mover?.destroy(),this._mover=null,this._tooltip=h(this._tooltip),this._handles=h(this._handles),this._viewHandles=h(this._viewHandles)}set highlightsEnabled(e){this._highlightHelper?.removeAll(),this._set(\"highlightsEnabled\",e),this._setUpHighlights()}get state(){const e=!!this.get(\"view.ready\"),t=!(!this.get(\"graphic\")||!this.get(\"layer\"));return e&&t?\"active\":e?\"ready\":\"disabled\"}set symbols(e){const{midpoints:t=se.midpoints,vertices:i=se.vertices}=e||{};this._set(\"symbols\",{midpoints:t,vertices:i})}isUIGraphic(e){const t=[];return this.graphic&&t.push(this.graphic),t.concat(this.vertexGraphics.items,this.midpointGraphics.items),t.length>0&&t.includes(e)}refresh(){this._reset(),this._setup()}reset(){this.graphic=null}clearSelection(){this._clearSelection()}removeSelectedVertices(){this.selectedVertices.length&&this._removeVertices(this.selectedVertices)}_setup(){const{graphic:e,layer:t}=this;if(!t||!e||p(e.geometry))return;const i=e.geometry;\"mesh\"!==i.type&&\"extent\"!==i.type?(\"polygon\"===i.type&&G(i),this._setUpHighlights(),this._setupGraphics(),this._setupMover()):this._logGeometryTypeError()}_setUpHighlights(){this.highlightsEnabled&&this.graphic&&this._highlightHelper.add(this.graphic)}_setUpGeometryHelper(){const e=this.graphic.geometry;if(p(e)||\"mesh\"===e.type||\"extent\"===e.type)return void this._logGeometryTypeError();const t=\"multipoint\"===e.type?new K({paths:e.points,spatialReference:e.spatialReference}):e;this._editGeometryOperations=H.fromGeometry(t,S.Local)}_saveSnappingContextForHandle(e,t){this._snappingGraphicsLayer=new w({listMode:\"hide\",internal:!0,title:\"Reshape snapping layer\"}),this.view.map.layers.add(this._snappingGraphicsLayer),this._snappingContext=new A({editGeometryOperations:this._editGeometryOperations,elevationInfo:{mode:\"on-the-ground\",offset:0},pointer:t.viewEvent?.pointerType||\"mouse\",excludeFeature:this.graphic,visualizer:new E(this._snappingGraphicsLayer),vertexHandle:this._getVertexFromEditGeometry(e)})}_reset(){this._clearSelection(),this._highlightHelper.removeAll(),this._updateTooltip(),this._resetGraphics(),this._resetSnappingStateVars(),this._activeOperationInfo=null,this._mover&&this._mover.destroy(),this._mover=null,this.view.cursor=\"default\"}_resetSnappingStateVars(){c(this.snappingManager)&&this.snappingManager.doneSnapping(),c(this._snappingGraphicsLayer)&&(this.view?.map&&this.view.map.layers.remove(this._snappingGraphicsLayer),this._snappingGraphicsLayer.destroy()),this._editGeometryOperations=h(this._editGeometryOperations),this._snappingTask=l(this._snappingTask),this._snappingTask=null,this._snappingContext=null,this._stagedVertex=null}_resetGraphics(e){this._removeMidpointGraphics(e),this._removeVertexGraphics(e),this._set(\"selectedVertices\",[])}_removeMidpointGraphics(e){const t=e||this.layer;t&&t.removeMany(this.midpointGraphics.items),this.midpointGraphics.items.forEach((e=>e.destroy())),this.midpointGraphics.removeAll()}_removeVertexGraphics(e){const t=e||this.layer;t&&t.removeMany(this.vertexGraphics.items),this.vertexGraphics.items.forEach((e=>e.destroy())),this.vertexGraphics.removeAll()}_getCoordinatesForUI(e){const t=b(e.clone());if(\"polygon\"===e.type)for(const i of t){const e=i[i.length-1];i[0][0]===e[0]&&i[0][1]===e[1]&&i.length>2&&i.pop()}return t}_setupGraphics(){const e=this.graphic.geometry;if(c(e)&&(\"polyline\"===e.type||\"polygon\"===e.type)){const t=this._getCoordinatesForUI(e);this.enableMidpoints&&this._setUpMidpointGraphics(t),this.enableVertices&&this._setUpVertexGraphics(t)}}_setUpMidpointGraphics(e){this._removeMidpointGraphics();const t=this._createMidpointGraphics(e);this.midpointGraphics.addMany(t),this.layer.addMany(t)}_setUpVertexGraphics(e){this._removeVertexGraphics();const t=this._createVertexGraphics(e);this.vertexGraphics.addMany(t),this._storeRelatedVertexIndices(),this.layer.addMany(t)}_createVertexGraphics(e){const{_graphicAttributes:i,symbols:s,view:{spatialReference:o}}=this,r=[];return e?.forEach(((e,n)=>{e.forEach(((e,a)=>{const[h,p]=e;r.push(new t({geometry:new N({x:h,y:p,spatialReference:o}),symbol:s?.vertices?.default,attributes:{...i,pathIndex:n,pointIndex:a}}))}))})),r}_createMidpointGraphics(e){const{_graphicAttributes:i,symbols:s,view:{spatialReference:o}}=this,r=[];return e?.forEach(((e,n)=>{e.forEach(((a,h)=>{const[p,c]=a,l=e[h+1]?h+1:0;if(\"polygon\"===d(this.graphic.geometry,\"type\")||0!==l){const[a,d]=e[l],[m,v]=V([p,c],[a,d]);r.push(new t({geometry:new N({x:m,y:v,spatialReference:o}),symbol:s.midpoints.default,attributes:{...i,pathIndex:n,pointIndexStart:h,pointIndexEnd:l}}))}}))})),r}_storeRelatedVertexIndices(){const e=this.vertexGraphics.items;if(!e)return;const t=e.map((({geometry:e})=>({x:e.x,y:e.y})));for(let i=0;i<t.length;i++){const s=[];for(let e=0;e<t.length;e++){if(i===e)continue;const o=t[i],r=t[e];o.x===r.x&&o.y===r.y&&s.push(e)}e[i].attributes.relatedGraphicIndices=s}}_setupMover(){const{enableMovement:e,graphic:t,midpointGraphics:i,vertexGraphics:s,view:o}=this,r=s.concat(i).items;e&&r.push(t),this._mover=new I({enableMoveAllGraphics:!1,highlightsEnabled:!1,indicatorsEnabled:!1,graphics:r,view:o,callbacks:{onGraphicClick:e=>this._onGraphicClickCallback(e),onGraphicMoveStart:e=>this._onGraphicMoveStartCallback(e),onGraphicMove:e=>this._onGraphicMoveCallback(e),onGraphicMoveStop:e=>this._onGraphicMoveStopCallback(e),onGraphicPointerOver:e=>this._onGraphicPointerOverCallback(e),onGraphicPointerOut:e=>this._onGraphicPointerOutCallback(e)}})}_onGraphicClickCallback(e){e.viewEvent.stopPropagation();const t=e.graphic;if(t===this.graphic)this.clearSelection(),this.emit(\"graphic-click\",e),this.callbacks.onGraphicClick&&this.callbacks.onGraphicClick(e);else if(this._isMidpoint(t)){if(2===e.viewEvent.button)return;const i=this.graphic.clone(),s=this._createVertexFromMidpoint(t);this.refresh(),this._emitVertexAddEvent([t],i,s)}else if(this._isVertex(t))if(e.viewEvent.stopPropagation(),2===e.viewEvent.button)this._removeVertices(t);else{e.viewEvent.native.shiftKey||this._clearSelection(),this.selectedVertices.includes(t)?this._removeFromSelection(t,!0):this._addToSelection(t)}}_setUpOperation(e){const{graphic:t,dx:i,dy:s}=e,o=t===this.graphic;this._resetSnappingStateVars(),this._setUpGeometryHelper(),this._saveSnappingContextForHandle(t,e),this._activeOperationInfo={target:this.graphic,mover:t,operationType:o?\"move\":\"reshape\",totalDx:i,totalDy:s}}_onGraphicMoveStartCallback(e){const{dx:t,dy:i,graphic:s}=e;if(s===this.graphic){const{geometry:o}=s;return this._setUpOperation(e),this._emitMoveStartEvent(t,i),void(c(o)&&\"point\"===o.type&&this._onHandleMove(s,t,i,e,(()=>{this._updateTooltip(this.graphic,e.viewEvent),this._emitMoveEvent(t,i)})))}if(!this.selectedVertices.includes(s)){if(this._clearSelection(),this._isMidpoint(s)){const e=this.graphic.clone(),t=this._createVertexFromMidpoint(s);this._emitVertexAddEvent([s],e,t)}this._addToSelection(s)}this._setUpOperation(e),this._emitReshapeStartEvent(s),this._onHandleMove(s,t,i,e,(()=>{this._updateTooltip(s,e.viewEvent),this._emitReshapeEvent(s)}))}_onGraphicMoveCallback(e){const t=this._activeOperationInfo;if(!t)return;const{dx:i,dy:s,graphic:o}=e;t.totalDx+=i,t.totalDy+=s;const{operationType:r}=t,{geometry:n}=o;if(!p(n))if(\"move\"!==r)this._onHandleMove(o,i,s,e,(()=>{this._updateTooltip(o,e.viewEvent),this._emitReshapeEvent(o)}));else if(\"point\"===n.type)this._onHandleMove(o,i,s,e,(()=>{this._updateTooltip(this.graphic,e.viewEvent),this._emitMoveEvent(i,s)}));else if(\"polyline\"===n.type||\"polygon\"===n.type){const t=this._getCoordinatesForUI(n);this._updateVertexGraphicLocations(t),this._updateTooltip(this.graphic,e.viewEvent),this._emitMoveEvent(i,s)}}_onGraphicMoveStopCallback(e){const t=this._activeOperationInfo;if(!t)return;const{dx:i,dy:s,graphic:o}=e,{operationType:r}=t;t.totalDx+=i,t.totalDy+=s,this._onHandleMove(o,i,s,e,(()=>\"move\"===r?this._emitMoveStopEvent():this._emitReshapeStopEvent(o))),this._isMidpoint(o)?this.refresh():(this._updateTooltip(this._isVertex(o)?o:null),this._resetSnappingStateVars(),this._activeOperationInfo=null)}_updateVertexGraphicLocations(e){const t=this.view.spatialReference;for(const i of this.vertexGraphics){const{pathIndex:s,pointIndex:o}=i.attributes,[r,n]=e[s][o];i.geometry=new N({x:r,y:n,spatialReference:t})}this._updateMidpointGraphicLocations(e)}_updateMidpointGraphicLocations(e){for(const t of this.midpointGraphics){const{pathIndex:i,pointIndexStart:s,pointIndexEnd:o}=t.attributes,[r,n]=e[i][s],[a,h]=e[i][o],[p,c]=V([r,n],[a,h]);t.geometry=new N({x:p,y:c,spatialReference:this.view.spatialReference})}}_getIndicesForVertexGraphic({attributes:e}){return[e?.pathIndex||0,e?.pointIndex||0]}_getVertexFromEditGeometry(e){const[t,i]=this._getIndicesForVertexGraphic(e);return this._editGeometryOperations.data.components[t].vertices[i]}_onHandleMove(e,t,s,o,r){l(this._snappingTask);const n=this._snappingContext;if(!n)return;const a=e.geometry,h=\"graphic-move-stop\"===o.type;if(c(this.snappingManager)&&this.selectedVertices.length<2&&!h){const o=this.snappingManager;this._stagedVertex=o.update({point:a,context:n}),this._syncGeometryAfterVertexMove(e,new N(this._stagedVertex),t,s,h),r(),this._snappingTask=i((async i=>{const p=await o.snap({point:a,context:n,signal:i});p.valid&&(this._stagedVertex=p.apply(),this._syncGeometryAfterVertexMove(e,new N(this._stagedVertex),t,s,h),r())}))}else{const i=c(this._stagedVertex)?new N(this._stagedVertex):a;this._syncGeometryAfterVertexMove(e,i,t,s,h),r()}}async _syncGeometryAfterVertexMove(e,t,i,s,o=!1){const r=this._editGeometryOperations.data.geometry;if(\"point\"===r.type)e.geometry=t;else{const{x:n,y:a}=t,[h,p]=this._getIndicesForVertexGraphic(e);let c=b(r);const l=c[h].length-1;c[h][p]=[n,a],\"polygon\"===r.type&&(0===p?c[h][l]=[n,a]:p===l&&(c[h][0]=[n,a])),this._isVertex(e)&&(c=this._moveRelatedCoordinates(c,e,n,a),c=this._moveSelectedHandleCoordinates(c,e,i,s,\"polygon\"===r.type),this._updateMidpointGraphicLocations(c)),this.graphic.geometry=r.clone();const d=this._getVertexFromEditGeometry(e),m=n-d.pos[0],v=a-d.pos[1];this._editGeometryOperations.moveVertices([d],m,v,0),o&&(this._mover?this._mover.updateGeometry(this._mover.graphics.indexOf(e),t):e.geometry=t)}}_moveRelatedCoordinates(e,t,i,s){const{relatedGraphicIndices:o}=t.attributes;for(const r of o){const o=this.vertexGraphics.getItemAt(r),{pathIndex:n,pointIndex:a}=o.attributes;e[n][a]=[i,s],o.geometry=t.geometry}return e}_moveSelectedHandleCoordinates(e,t,i,s,o){for(const r of this.selectedVertices)if(r!==t){const{pathIndex:t,pointIndex:n,relatedGraphicIndices:a}=r.attributes,h=k(r.geometry,i,s,this.view),p=e[t].length-1;e[t][n]=[h.x,h.y],r.geometry=h,o&&(0===n?e[t][p]=[h.x,h.y]:n===p&&(e[t][0]=[h.x,h.y]));for(const i of a){const t=this.vertexGraphics.getItemAt(i),{pathIndex:s,pointIndex:o}=t.attributes;e[s][o]=[h.x,h.y],t.geometry=h}}return e}_onGraphicPointerOverCallback(e){const t=e.graphic,i=this._isVertex(t);i&&!this._isSelected(t)&&(t.symbol=this.symbols.vertices.hover),this._updateTooltip(i?t:null),this._updateHoverCursor(t)}_onGraphicPointerOutCallback(e){const t=e.graphic;this._isVertex(t)&&!this._isSelected(t)&&(t.symbol=this.symbols.vertices.default),this.view.cursor=\"default\",this._updateTooltip()}_createVertexFromMidpoint(e){const{_graphicAttributes:t,graphic:i}=this,s=i.geometry;if(p(s)||\"polygon\"!==s.type&&\"polyline\"!==s.type)return[];const o=s.clone(),r=[],{pathIndex:n,pointIndexStart:a,pointIndexEnd:h}=e.attributes,{x:c,y:l}=e.geometry,d=0===h?a+1:h,m=b(o);return m[n].splice(d,0,[c,l]),e.attributes={...t,pathIndex:n,pointIndex:d,relatedGraphicIndices:[]},r.push({coordinates:m[n][d],componentIndex:n,vertexIndex:d}),this.graphic.geometry=o,r}_addToSelection(e){e instanceof t&&(e=[e]);for(const t of e)t.symbol=this.symbols.vertices.selected;this._set(\"selectedVertices\",this.selectedVertices.concat(e)),this._emitSelectEvent(e)}_removeFromSelection(e,i){const{vertices:s}=this.symbols,o=i?s.hover:s.default;e instanceof t&&(e=[e]);for(const t of e)this.selectedVertices.splice(this.selectedVertices.indexOf(t),1),this._set(\"selectedVertices\",this.selectedVertices),t.set(\"symbol\",o);this._emitDeselectEvent(e)}_clearSelection(){if(this.selectedVertices.length){const e=this.selectedVertices;for(const t of this.selectedVertices)t.set(\"symbol\",this.symbols.vertices.default);this._set(\"selectedVertices\",[]),this._emitDeselectEvent(e)}}_keyDownHandler(e){R.delete.includes(e.key)&&!e.repeat&&this.selectedVertices.length&&this._removeVertices(this.selectedVertices)}_removeVertices(e){const i=this.graphic.geometry;if(p(i)||\"polygon\"!==i.type&&\"polyline\"!==i.type)return;if(\"polygon\"===i.type&&this.vertexGraphics.length<4||this.vertexGraphics.length<3)return;e instanceof t&&(e=[e]);const s=this.graphic.clone(),o=i.clone();let r=b(o);const n=[];e instanceof t&&(e=[e]);for(const t of e){const{x:e,y:i}=t.geometry;for(let t=0;t<r.length;t++){const s=r[t];for(let o=0;o<s.length;o++){const[a,h]=s[o];e===a&&i===h&&(n.push({coordinates:r[t][o],componentIndex:t,vertexIndex:o}),r[t].splice(Number(o),1))}}}if(\"polygon\"===o.type)r=r.filter((e=>{if(e.length<2)return!1;const[t,i]=e[0],[s,o]=e[e.length-1];return(2!==e.length||t!==s||i!==o)&&(t===s&&i===o||e.push(e[0]),!0)})),o.rings=r;else{for(const e of r)1===e.length&&r.splice(r.indexOf(e),1);o.paths=r}this.graphic.geometry=o,this.refresh(),this._emitVertexRemoveEvent(e,s,n)}_isVertex(e){return this.vertexGraphics.includes(e)}_isSelected(e){return this._isVertex(e)&&this.selectedVertices.includes(e)}_isMidpoint(e){return this.midpointGraphics.includes(e)}_updateHoverCursor(e){this.view.cursor=this._isMidpoint(e)?\"copy\":\"move\"}_updateTooltip(e,t){p(this._tooltip)||(e?e===this.graphic?this._updateMoveGraphicTooltip(t):this._updateMoveVertexTooltip(t):this._tooltip.clear())}_updateMoveGraphicTooltip(e){const{_tooltip:t,tooltipOptions:i,view:s}=this;if(p(t))return;const o=new F({tooltipOptions:i});if(e){const{x:t,y:i}=e.origin,r=s.toMap(e),n=s.toMap(u(t,i)),a=P(n,r);o.distance=c(a)?a:m}t.info=o}_updateMoveVertexTooltip(e){const{_tooltip:t,graphic:{geometry:i},tooltipOptions:s,view:o}=this;if(p(t))return;const r=new D({tooltipOptions:s});if(c(i)&&(\"polygon\"===i.type?r.area=z(i):\"polyline\"===i.type&&(r.totalLength=q(i))),e){const{x:t,y:i}=e.origin,s=o.toMap(e),n=o.toMap(u(t,i)),a=P(n,s);r.distance=c(a)?a:m}t.info=r}_emitMoveStartEvent(e,t){const i=new W(this.graphic,e,t);this.emit(\"move-start\",i),this.callbacks.onMoveStart&&this.callbacks.onMoveStart(i)}_emitMoveEvent(e,t){const i=new X(this.graphic,e,t);this.emit(\"move\",i),this.callbacks.onMove&&this.callbacks.onMove(i)}_emitMoveStopEvent(){const e=this._activeOperationInfo;if(!e)return;const{totalDx:t,totalDy:i}=e,s=new Y(this.graphic,t,i);this.emit(\"move-stop\",s),this.callbacks.onMoveStop&&this.callbacks.onMoveStop(s)}_emitReshapeStartEvent(e){const t=new B(this.graphic,e,this.selectedVertices);this.emit(\"reshape-start\",t),this.callbacks.onReshapeStart&&this.callbacks.onReshapeStart(t)}_emitReshapeEvent(e){const t=new J(this.graphic,e,this.selectedVertices);this.emit(\"reshape\",t),this.callbacks.onReshape&&this.callbacks.onReshape(t)}_emitReshapeStopEvent(e){const t=new Q(this.graphic,e,this.selectedVertices);this.emit(\"reshape-stop\",t),this.callbacks.onReshapeStop&&this.callbacks.onReshapeStop(t)}_emitSelectEvent(e){const t=new Z(e);this.emit(\"select\",t),this.callbacks.onVertexSelect&&this.callbacks.onVertexSelect(t)}_emitDeselectEvent(e){const t=new $(e);this.emit(\"deselect\",t),this.callbacks.onVertexDeselect&&this.callbacks.onVertexDeselect(t)}_emitVertexAddEvent(e,t,i){const s=new ee(e,this.graphic,t,i);this.emit(\"vertex-add\",s),this.callbacks.onVertexAdd&&this.callbacks.onVertexAdd(s)}_emitVertexRemoveEvent(e,t,i){const s=new te(e,this.graphic,t,i);this.emit(\"vertex-remove\",s),this.callbacks.onVertexRemove&&this.callbacks.onVertexRemove(s)}_logGeometryTypeError(){a.getLogger(this.declaredClass).error(new o(\"reshape:invalid-geometry\",\"Reshape operation not supported for the provided graphic. The geometry type is not supported.\"))}};e([f()],oe.prototype,\"_tooltip\",void 0),e([f()],oe.prototype,\"callbacks\",void 0),e([f()],oe.prototype,\"enableMidpoints\",void 0),e([f()],oe.prototype,\"enableMovement\",void 0),e([f()],oe.prototype,\"enableVertices\",void 0),e([f()],oe.prototype,\"graphic\",void 0),e([f({value:!0})],oe.prototype,\"highlightsEnabled\",null),e([f()],oe.prototype,\"layer\",void 0),e([f({readOnly:!0})],oe.prototype,\"midpointGraphics\",void 0),e([f()],oe.prototype,\"midpointSymbol\",void 0),e([f({readOnly:!0})],oe.prototype,\"selectedVertices\",void 0),e([f()],oe.prototype,\"snappingManager\",void 0),e([f({readOnly:!0})],oe.prototype,\"state\",null),e([f({value:se})],oe.prototype,\"symbols\",null),e([f({type:U})],oe.prototype,\"tooltipOptions\",void 0),e([f({readOnly:!0})],oe.prototype,\"type\",void 0),e([f({readOnly:!0})],oe.prototype,\"vertexGraphics\",void 0),e([f()],oe.prototype,\"view\",void 0),oe=e([x(\"esri.views.draw.support.Reshape\")],oe);const re=oe;export{re as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI8yE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAE,GAAE;AAAC,SAAK,UAAQD,IAAE,KAAK,QAAMC,IAAE,KAAK,WAAS,GAAE,KAAK,OAAK;AAAA,EAAe;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEC,IAAE,GAAE;AAAC,SAAK,UAAQD,IAAE,KAAK,QAAMC,IAAE,KAAK,WAAS,GAAE,KAAK,OAAK;AAAA,EAAS;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEC,IAAE,GAAE;AAAC,SAAK,UAAQD,IAAE,KAAK,QAAMC,IAAE,KAAK,WAAS,GAAE,KAAK,OAAK;AAAA,EAAc;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEC,IAAE,GAAE;AAAC,SAAK,QAAMD,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAG,GAAE,KAAK,OAAK;AAAA,EAAY;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEC,IAAE,GAAE;AAAC,SAAK,QAAMD,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAG,GAAE,KAAK,OAAK;AAAA,EAAM;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEC,IAAE,GAAE;AAAC,SAAK,QAAMD,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAG,GAAE,KAAK,OAAK;AAAA,EAAW;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAE;AAAC,SAAK,QAAMA,IAAE,KAAK,OAAK;AAAA,EAAe;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,UAAQA,IAAE,KAAK,OAAK;AAAA,EAAiB;AAAC;AAAC,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYA,IAAEC,IAAE,GAAEC,IAAE;AAAC,SAAK,QAAMF,IAAE,KAAK,UAAQC,IAAE,KAAK,aAAW,GAAE,KAAK,WAASC,IAAE,KAAK,OAAK;AAAA,EAAY;AAAC;AAAC,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYF,IAAEC,IAAE,GAAEC,IAAE;AAAC,SAAK,UAAQF,IAAE,KAAK,UAAQC,IAAE,KAAK,aAAW,GAAE,KAAK,WAASC,IAAE,KAAK,OAAK;AAAA,EAAe;AAAC;AAAC,IAAM,KAAGC,GAAE;AAAX,IAA2B,KAAG,EAAC,UAAS,EAAC,SAAQ,IAAIC,GAAE,EAAC,OAAM,UAAS,MAAK,GAAG,OAAO,MAAK,OAAM,GAAG,OAAO,OAAM,SAAQ,EAAC,OAAM,GAAG,OAAO,cAAa,OAAM,EAAC,EAAC,CAAC,GAAE,OAAM,IAAIA,GAAE,EAAC,OAAM,UAAS,MAAK,GAAG,OAAO,WAAU,OAAM,GAAG,OAAO,YAAW,SAAQ,EAAC,OAAM,GAAG,OAAO,mBAAkB,OAAM,EAAC,EAAC,CAAC,GAAE,UAAS,IAAIA,GAAE,EAAC,OAAM,UAAS,MAAK,GAAG,SAAS,MAAK,OAAM,GAAG,SAAS,OAAM,SAAQ,EAAC,OAAM,GAAG,SAAS,cAAa,OAAM,EAAC,EAAC,CAAC,EAAC,GAAE,WAAU,EAAC,SAAQ,IAAIA,GAAE,EAAC,OAAM,UAAS,MAAK,GAAG,SAAS,MAAK,OAAM,GAAG,SAAS,OAAM,SAAQ,EAAC,OAAM,GAAG,SAAS,cAAa,OAAM,EAAC,EAAC,CAAC,GAAE,OAAM,IAAIA,GAAE,EAAC,OAAM,UAAS,MAAK,GAAG,SAAS,MAAK,OAAM,GAAG,SAAS,OAAM,SAAQ,EAAC,OAAM,GAAG,SAAS,cAAa,OAAM,EAAC,EAAC,CAAC,EAAC,EAAC;AAAE,IAAI,KAAG,cAAc,EAAE,gBAAe;AAAA,EAAC,YAAYJ,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,uBAAqB,MAAK,KAAK,0BAAwB,MAAK,KAAK,WAAS,IAAIC,MAAE,KAAK,qBAAmB,EAAC,gBAAe,MAAK,GAAE,KAAK,SAAO,MAAK,KAAK,mBAAiB,MAAK,KAAK,gBAAc,MAAK,KAAK,gBAAc,MAAK,KAAK,WAAS,MAAK,KAAK,eAAa,IAAIA,MAAE,KAAK,YAAU,EAAC,iBAAgB;AAAA,IAAC,GAAE,YAAW;AAAA,IAAC,GAAE,gBAAe;AAAA,IAAC,GAAE,cAAa;AAAA,IAAC,GAAE,SAAQ;AAAA,IAAC,GAAE,aAAY;AAAA,IAAC,GAAE,iBAAgB;AAAA,IAAC,EAAC,GAAE,KAAK,kBAAgB,MAAG,KAAK,iBAAe,MAAG,KAAK,iBAAe,MAAG,KAAK,UAAQ,MAAK,KAAK,QAAM,MAAK,KAAK,mBAAiB,IAAI,KAAE,KAAK,iBAAe,IAAIG,GAAE,EAAC,OAAM,UAAS,MAAK,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,SAAQ,EAAC,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,OAAM,EAAC,EAAC,CAAC,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,kBAAgB,MAAK,KAAK,iBAAe,IAAI,KAAE,KAAK,OAAK,WAAU,KAAK,iBAAe,IAAI,KAAE,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,UAAMJ,KAAE,KAAK;AAAK,SAAK,mBAAiB,IAAIG,GAAE,EAAC,MAAKH,GAAC,CAAC,GAAE,KAAK,OAAO,GAAE,KAAK,SAAS,IAAI,CAACK,GAAG,MAAIL,MAAA,gBAAAA,GAAG,OAAQ,MAAI;AAAC,YAAK,EAAC,OAAMA,IAAE,MAAKC,GAAC,IAAE;AAAK,MAAAD,GAAEC,IAAED,EAAC,GAAE,KAAK,aAAa,IAAIC,GAAE,GAAG,YAAY,CAAAD,OAAG,KAAK,gBAAgBA,EAAC,GAAGM,GAAE,IAAI,CAAC;AAAA,IAAC,GAAG,EAAC,MAAK,MAAG,SAAQ,KAAE,CAAC,GAAE,EAAG,MAAI,KAAK,SAAU,MAAI,KAAK,QAAQ,CAAE,GAAE,EAAG,MAAI,KAAK,OAAQ,CAACN,IAAEC,OAAI;AAAC,MAAAA,OAAI,KAAK,gBAAgB,GAAE,KAAK,eAAeA,EAAC,IAAG,KAAK,QAAQ;AAAA,IAAC,CAAE,GAAE,EAAG,MAAI,KAAK,iBAAkB,MAAI,KAAK,QAAQ,CAAE,GAAE,EAAG,MAAI,KAAK,eAAe,SAAU,CAAAD,OAAG;AAAC,WAAK,WAASA,KAAE,IAAIO,GAAE,EAAC,MAAK,KAAK,KAAI,CAAC,IAAE,EAAE,KAAK,QAAQ;AAAA,IAAC,GAAGC,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJ7tK;AAI8tK,SAAK,OAAO,IAAE,UAAK,WAAL,mBAAa,WAAU,KAAK,SAAO,MAAK,KAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,eAAa,EAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAkBR,IAAE;AAJj5K;AAIk5K,eAAK,qBAAL,mBAAuB,aAAY,KAAK,KAAK,qBAAoBA,EAAC,GAAE,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,UAAMA,KAAE,CAAC,CAAC,KAAK,IAAI,YAAY,GAAEC,KAAE,EAAE,CAAC,KAAK,IAAI,SAAS,KAAG,CAAC,KAAK,IAAI,OAAO;AAAG,WAAOD,MAAGC,KAAE,WAASD,KAAE,UAAQ;AAAA,EAAU;AAAA,EAAC,IAAI,QAAQA,IAAE;AAAC,UAAK,EAAC,WAAUC,KAAE,GAAG,WAAU,UAAS,IAAE,GAAG,SAAQ,IAAED,MAAG,CAAC;AAAE,SAAK,KAAK,WAAU,EAAC,WAAUC,IAAE,UAAS,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,WAAO,KAAK,WAASA,GAAE,KAAK,KAAK,OAAO,GAAEA,GAAE,OAAO,KAAK,eAAe,OAAM,KAAK,iBAAiB,KAAK,GAAEA,GAAE,SAAO,KAAGA,GAAE,SAASD,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,OAAO,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,UAAQ;AAAA,EAAI;AAAA,EAAC,iBAAgB;AAAC,SAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,SAAK,iBAAiB,UAAQ,KAAK,gBAAgB,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAK,EAAC,SAAQA,IAAE,OAAMC,GAAC,IAAE;AAAK,QAAG,CAACA,MAAG,CAACD,MAAG,EAAEA,GAAE,QAAQ,EAAE;AAAO,UAAM,IAAEA,GAAE;AAAS,eAAS,EAAE,QAAM,aAAW,EAAE,QAAM,cAAY,EAAE,QAAM,EAAE,CAAC,GAAE,KAAK,iBAAiB,GAAE,KAAK,eAAe,GAAE,KAAK,YAAY,KAAG,KAAK,sBAAsB;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,qBAAmB,KAAK,WAAS,KAAK,iBAAiB,IAAI,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,UAAMA,KAAE,KAAK,QAAQ;AAAS,QAAG,EAAEA,EAAC,KAAG,WAASA,GAAE,QAAM,aAAWA,GAAE,KAAK,QAAO,KAAK,KAAK,sBAAsB;AAAE,UAAMC,KAAE,iBAAeD,GAAE,OAAK,IAAI,EAAE,EAAC,OAAMA,GAAE,QAAO,kBAAiBA,GAAE,iBAAgB,CAAC,IAAEA;AAAE,SAAK,0BAAwB,EAAE,aAAaC,IAAEQ,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,8BAA8BT,IAAEC,IAAE;AAJ5uN;AAI6uN,SAAK,yBAAuB,IAAI,EAAE,EAAC,UAAS,QAAO,UAAS,MAAG,OAAM,yBAAwB,CAAC,GAAE,KAAK,KAAK,IAAI,OAAO,IAAI,KAAK,sBAAsB,GAAE,KAAK,mBAAiB,IAAID,GAAE,EAAC,wBAAuB,KAAK,yBAAwB,eAAc,EAAC,MAAK,iBAAgB,QAAO,EAAC,GAAE,WAAQ,KAAAC,GAAE,cAAF,mBAAa,gBAAa,SAAQ,gBAAe,KAAK,SAAQ,YAAW,IAAI,EAAE,KAAK,sBAAsB,GAAE,cAAa,KAAK,2BAA2BD,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,gBAAgB,GAAE,KAAK,iBAAiB,UAAU,GAAE,KAAK,eAAe,GAAE,KAAK,eAAe,GAAE,KAAK,wBAAwB,GAAE,KAAK,uBAAqB,MAAK,KAAK,UAAQ,KAAK,OAAO,QAAQ,GAAE,KAAK,SAAO,MAAK,KAAK,KAAK,SAAO;AAAA,EAAS;AAAA,EAAC,0BAAyB;AAJ57O;AAI67O,MAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,aAAa,GAAE,EAAE,KAAK,sBAAsB,QAAI,UAAK,SAAL,mBAAW,QAAK,KAAK,KAAK,IAAI,OAAO,OAAO,KAAK,sBAAsB,GAAE,KAAK,uBAAuB,QAAQ,IAAG,KAAK,0BAAwB,EAAE,KAAK,uBAAuB,GAAE,KAAK,gBAAc,EAAE,KAAK,aAAa,GAAE,KAAK,gBAAc,MAAK,KAAK,mBAAiB,MAAK,KAAK,gBAAc;AAAA,EAAI;AAAA,EAAC,eAAeA,IAAE;AAAC,SAAK,wBAAwBA,EAAC,GAAE,KAAK,sBAAsBA,EAAC,GAAE,KAAK,KAAK,oBAAmB,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAE;AAAC,UAAMC,KAAED,MAAG,KAAK;AAAM,IAAAC,MAAGA,GAAE,WAAW,KAAK,iBAAiB,KAAK,GAAE,KAAK,iBAAiB,MAAM,QAAS,CAAAD,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,iBAAiB,UAAU;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,UAAMC,KAAED,MAAG,KAAK;AAAM,IAAAC,MAAGA,GAAE,WAAW,KAAK,eAAe,KAAK,GAAE,KAAK,eAAe,MAAM,QAAS,CAAAD,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,eAAe,UAAU;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAE;AAAC,UAAMC,KAAED,GAAEA,GAAE,MAAM,CAAC;AAAE,QAAG,cAAYA,GAAE,KAAK,YAAU,KAAKC,IAAE;AAAC,YAAMD,KAAE,EAAE,EAAE,SAAO,CAAC;AAAE,QAAE,CAAC,EAAE,CAAC,MAAIA,GAAE,CAAC,KAAG,EAAE,CAAC,EAAE,CAAC,MAAIA,GAAE,CAAC,KAAG,EAAE,SAAO,KAAG,EAAE,IAAI;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,UAAMD,KAAE,KAAK,QAAQ;AAAS,QAAG,EAAEA,EAAC,MAAI,eAAaA,GAAE,QAAM,cAAYA,GAAE,OAAM;AAAC,YAAMC,KAAE,KAAK,qBAAqBD,EAAC;AAAE,WAAK,mBAAiB,KAAK,uBAAuBC,EAAC,GAAE,KAAK,kBAAgB,KAAK,qBAAqBA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBD,IAAE;AAAC,SAAK,wBAAwB;AAAE,UAAMC,KAAE,KAAK,wBAAwBD,EAAC;AAAE,SAAK,iBAAiB,QAAQC,EAAC,GAAE,KAAK,MAAM,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBD,IAAE;AAAC,SAAK,sBAAsB;AAAE,UAAMC,KAAE,KAAK,sBAAsBD,EAAC;AAAE,SAAK,eAAe,QAAQC,EAAC,GAAE,KAAK,2BAA2B,GAAE,KAAK,MAAM,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAE;AAAC,UAAK,EAAC,oBAAmB,GAAE,SAAQE,IAAE,MAAK,EAAC,kBAAiB,EAAC,EAAC,IAAE,MAAKQ,KAAE,CAAC;AAAE,WAAOV,MAAA,gBAAAA,GAAG,QAAS,CAACA,IAAEW,OAAI;AAAC,MAAAX,GAAE,QAAS,CAACA,IAAEY,OAAI;AAJnoS;AAIooS,cAAK,CAACT,IAAEU,EAAC,IAAEb;AAAE,QAAAU,GAAE,KAAK,IAAII,GAAE,EAAC,UAAS,IAAIN,GAAE,EAAC,GAAEL,IAAE,GAAEU,IAAE,kBAAiB,EAAC,CAAC,GAAE,SAAO,KAAAX,MAAA,gBAAAA,GAAG,aAAH,mBAAa,SAAQ,YAAW,EAAC,GAAG,GAAE,WAAUS,IAAE,YAAWC,GAAC,EAAC,CAAC,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,IAAIF;AAAA,EAAC;AAAA,EAAC,wBAAwBV,IAAE;AAAC,UAAK,EAAC,oBAAmB,GAAE,SAAQE,IAAE,MAAK,EAAC,kBAAiB,EAAC,EAAC,IAAE,MAAKQ,KAAE,CAAC;AAAE,WAAOV,MAAA,gBAAAA,GAAG,QAAS,CAACA,IAAEW,OAAI;AAAC,MAAAX,GAAE,QAAS,CAACY,IAAET,OAAI;AAAC,cAAK,CAACU,IAAEE,EAAC,IAAEH,IAAEH,KAAET,GAAEG,KAAE,CAAC,IAAEA,KAAE,IAAE;AAAE,YAAG,cAAY,EAAE,KAAK,QAAQ,UAAS,MAAM,KAAG,MAAIM,IAAE;AAAC,gBAAK,CAACG,IAAE,CAAC,IAAEZ,GAAES,EAAC,GAAE,CAACF,IAAE,CAAC,IAAE,EAAE,CAACM,IAAEE,EAAC,GAAE,CAACH,IAAE,CAAC,CAAC;AAAE,UAAAF,GAAE,KAAK,IAAII,GAAE,EAAC,UAAS,IAAIN,GAAE,EAAC,GAAED,IAAE,GAAE,GAAE,kBAAiB,EAAC,CAAC,GAAE,QAAOL,GAAE,UAAU,SAAQ,YAAW,EAAC,GAAG,GAAE,WAAUS,IAAE,iBAAgBR,IAAE,eAAcM,GAAC,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,IAAIC;AAAA,EAAC;AAAA,EAAC,6BAA4B;AAAC,UAAMV,KAAE,KAAK,eAAe;AAAM,QAAG,CAACA,GAAE;AAAO,UAAMC,KAAED,GAAE,IAAK,CAAC,EAAC,UAASA,GAAC,OAAK,EAAC,GAAEA,GAAE,GAAE,GAAEA,GAAE,EAAC,EAAG;AAAE,aAAQ,IAAE,GAAE,IAAEC,GAAE,QAAO,KAAI;AAAC,YAAMC,KAAE,CAAC;AAAE,eAAQF,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAI;AAAC,YAAG,MAAIA,GAAE;AAAS,cAAM,IAAEC,GAAE,CAAC,GAAES,KAAET,GAAED,EAAC;AAAE,UAAE,MAAIU,GAAE,KAAG,EAAE,MAAIA,GAAE,KAAGR,GAAE,KAAKF,EAAC;AAAA,MAAC;AAAC,MAAAA,GAAE,CAAC,EAAE,WAAW,wBAAsBE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAK,EAAC,gBAAeF,IAAE,SAAQC,IAAE,kBAAiB,GAAE,gBAAeC,IAAE,MAAK,EAAC,IAAE,MAAKQ,KAAER,GAAE,OAAO,CAAC,EAAE;AAAM,IAAAF,MAAGU,GAAE,KAAKT,EAAC,GAAE,KAAK,SAAO,IAAI,EAAE,EAAC,uBAAsB,OAAG,mBAAkB,OAAG,mBAAkB,OAAG,UAASS,IAAE,MAAK,GAAE,WAAU,EAAC,gBAAe,CAAAV,OAAG,KAAK,wBAAwBA,EAAC,GAAE,oBAAmB,CAAAA,OAAG,KAAK,4BAA4BA,EAAC,GAAE,eAAc,CAAAA,OAAG,KAAK,uBAAuBA,EAAC,GAAE,mBAAkB,CAAAA,OAAG,KAAK,2BAA2BA,EAAC,GAAE,sBAAqB,CAAAA,OAAG,KAAK,8BAA8BA,EAAC,GAAE,qBAAoB,CAAAA,OAAG,KAAK,6BAA6BA,EAAC,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAE;AAAC,IAAAA,GAAE,UAAU,gBAAgB;AAAE,UAAMC,KAAED,GAAE;AAAQ,QAAGC,OAAI,KAAK,QAAQ,MAAK,eAAe,GAAE,KAAK,KAAK,iBAAgBD,EAAC,GAAE,KAAK,UAAU,kBAAgB,KAAK,UAAU,eAAeA,EAAC;AAAA,aAAU,KAAK,YAAYC,EAAC,GAAE;AAAC,UAAG,MAAID,GAAE,UAAU,OAAO;AAAO,YAAM,IAAE,KAAK,QAAQ,MAAM,GAAEE,KAAE,KAAK,0BAA0BD,EAAC;AAAE,WAAK,QAAQ,GAAE,KAAK,oBAAoB,CAACA,EAAC,GAAE,GAAEC,EAAC;AAAA,IAAC,WAAS,KAAK,UAAUD,EAAC,EAAE,KAAGD,GAAE,UAAU,gBAAgB,GAAE,MAAIA,GAAE,UAAU,OAAO,MAAK,gBAAgBC,EAAC;AAAA,SAAM;AAAC,MAAAD,GAAE,UAAU,OAAO,YAAU,KAAK,gBAAgB,GAAE,KAAK,iBAAiB,SAASC,EAAC,IAAE,KAAK,qBAAqBA,IAAE,IAAE,IAAE,KAAK,gBAAgBA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBD,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,IAAG,GAAE,IAAGC,GAAC,IAAEF,IAAE,IAAEC,OAAI,KAAK;AAAQ,SAAK,wBAAwB,GAAE,KAAK,qBAAqB,GAAE,KAAK,8BAA8BA,IAAED,EAAC,GAAE,KAAK,uBAAqB,EAAC,QAAO,KAAK,SAAQ,OAAMC,IAAE,eAAc,IAAE,SAAO,WAAU,SAAQ,GAAE,SAAQC,GAAC;AAAA,EAAC;AAAA,EAAC,4BAA4BF,IAAE;AAAC,UAAK,EAAC,IAAGC,IAAE,IAAG,GAAE,SAAQC,GAAC,IAAEF;AAAE,QAAGE,OAAI,KAAK,SAAQ;AAAC,YAAK,EAAC,UAAS,EAAC,IAAEA;AAAE,aAAO,KAAK,gBAAgBF,EAAC,GAAE,KAAK,oBAAoBC,IAAE,CAAC,GAAE,MAAK,EAAE,CAAC,KAAG,YAAU,EAAE,QAAM,KAAK,cAAcC,IAAED,IAAE,GAAED,IAAG,MAAI;AAAC,aAAK,eAAe,KAAK,SAAQA,GAAE,SAAS,GAAE,KAAK,eAAeC,IAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAE;AAAC,QAAG,CAAC,KAAK,iBAAiB,SAASC,EAAC,GAAE;AAAC,UAAG,KAAK,gBAAgB,GAAE,KAAK,YAAYA,EAAC,GAAE;AAAC,cAAMF,KAAE,KAAK,QAAQ,MAAM,GAAEC,KAAE,KAAK,0BAA0BC,EAAC;AAAE,aAAK,oBAAoB,CAACA,EAAC,GAAEF,IAAEC,EAAC;AAAA,MAAC;AAAC,WAAK,gBAAgBC,EAAC;AAAA,IAAC;AAAC,SAAK,gBAAgBF,EAAC,GAAE,KAAK,uBAAuBE,EAAC,GAAE,KAAK,cAAcA,IAAED,IAAE,GAAED,IAAG,MAAI;AAAC,WAAK,eAAeE,IAAEF,GAAE,SAAS,GAAE,KAAK,kBAAkBE,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAqB,QAAG,CAACA,GAAE;AAAO,UAAK,EAAC,IAAG,GAAE,IAAGC,IAAE,SAAQ,EAAC,IAAEF;AAAE,IAAAC,GAAE,WAAS,GAAEA,GAAE,WAASC;AAAE,UAAK,EAAC,eAAcQ,GAAC,IAAET,IAAE,EAAC,UAASU,GAAC,IAAE;AAAE,QAAG,CAAC,EAAEA,EAAC;AAAE,UAAG,WAASD,GAAE,MAAK,cAAc,GAAE,GAAER,IAAEF,IAAG,MAAI;AAAC,aAAK,eAAe,GAAEA,GAAE,SAAS,GAAE,KAAK,kBAAkB,CAAC;AAAA,MAAC,CAAE;AAAA,eAAU,YAAUW,GAAE,KAAK,MAAK,cAAc,GAAE,GAAET,IAAEF,IAAG,MAAI;AAAC,aAAK,eAAe,KAAK,SAAQA,GAAE,SAAS,GAAE,KAAK,eAAe,GAAEE,EAAC;AAAA,MAAC,CAAE;AAAA,eAAU,eAAaS,GAAE,QAAM,cAAYA,GAAE,MAAK;AAAC,cAAMV,KAAE,KAAK,qBAAqBU,EAAC;AAAE,aAAK,8BAA8BV,EAAC,GAAE,KAAK,eAAe,KAAK,SAAQD,GAAE,SAAS,GAAE,KAAK,eAAe,GAAEE,EAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAA,EAAC,2BAA2BF,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAqB,QAAG,CAACA,GAAE;AAAO,UAAK,EAAC,IAAG,GAAE,IAAGC,IAAE,SAAQ,EAAC,IAAEF,IAAE,EAAC,eAAcU,GAAC,IAAET;AAAE,IAAAA,GAAE,WAAS,GAAEA,GAAE,WAASC,IAAE,KAAK,cAAc,GAAE,GAAEA,IAAEF,IAAG,MAAI,WAASU,KAAE,KAAK,mBAAmB,IAAE,KAAK,sBAAsB,CAAC,CAAE,GAAE,KAAK,YAAY,CAAC,IAAE,KAAK,QAAQ,KAAG,KAAK,eAAe,KAAK,UAAU,CAAC,IAAE,IAAE,IAAI,GAAE,KAAK,wBAAwB,GAAE,KAAK,uBAAqB;AAAA,EAAK;AAAA,EAAC,8BAA8BV,IAAE;AAAC,UAAMC,KAAE,KAAK,KAAK;AAAiB,eAAU,KAAK,KAAK,gBAAe;AAAC,YAAK,EAAC,WAAUC,IAAE,YAAW,EAAC,IAAE,EAAE,YAAW,CAACQ,IAAEC,EAAC,IAAEX,GAAEE,EAAC,EAAE,CAAC;AAAE,QAAE,WAAS,IAAIM,GAAE,EAAC,GAAEE,IAAE,GAAEC,IAAE,kBAAiBV,GAAC,CAAC;AAAA,IAAC;AAAC,SAAK,gCAAgCD,EAAC;AAAA,EAAC;AAAA,EAAC,gCAAgCA,IAAE;AAAC,eAAUC,MAAK,KAAK,kBAAiB;AAAC,YAAK,EAAC,WAAU,GAAE,iBAAgBC,IAAE,eAAc,EAAC,IAAED,GAAE,YAAW,CAACS,IAAEC,EAAC,IAAEX,GAAE,CAAC,EAAEE,EAAC,GAAE,CAACU,IAAET,EAAC,IAAEH,GAAE,CAAC,EAAE,CAAC,GAAE,CAACa,IAAEE,EAAC,IAAE,EAAE,CAACL,IAAEC,EAAC,GAAE,CAACC,IAAET,EAAC,CAAC;AAAE,MAAAF,GAAE,WAAS,IAAIO,GAAE,EAAC,GAAEK,IAAE,GAAEE,IAAE,kBAAiB,KAAK,KAAK,iBAAgB,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,4BAA4B,EAAC,YAAWf,GAAC,GAAE;AAAC,WAAM,EAACA,MAAA,gBAAAA,GAAG,cAAW,IAAEA,MAAA,gBAAAA,GAAG,eAAY,CAAC;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAE;AAAC,UAAK,CAACC,IAAE,CAAC,IAAE,KAAK,4BAA4BD,EAAC;AAAE,WAAO,KAAK,wBAAwB,KAAK,WAAWC,EAAC,EAAE,SAAS,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAEC,IAAE,GAAEQ,IAAE;AAAC,MAAE,KAAK,aAAa;AAAE,UAAMC,KAAE,KAAK;AAAiB,QAAG,CAACA,GAAE;AAAO,UAAMC,KAAEZ,GAAE,UAASG,KAAE,wBAAsB,EAAE;AAAK,QAAG,EAAE,KAAK,eAAe,KAAG,KAAK,iBAAiB,SAAO,KAAG,CAACA,IAAE;AAAC,YAAMa,KAAE,KAAK;AAAgB,WAAK,gBAAcA,GAAE,OAAO,EAAC,OAAMJ,IAAE,SAAQD,GAAC,CAAC,GAAE,KAAK,6BAA6BX,IAAE,IAAIQ,GAAE,KAAK,aAAa,GAAEP,IAAEC,IAAEC,EAAC,GAAEO,GAAE,GAAE,KAAK,gBAAcO,GAAG,OAAM,MAAG;AAAC,cAAMJ,KAAE,MAAMG,GAAE,KAAK,EAAC,OAAMJ,IAAE,SAAQD,IAAE,QAAO,EAAC,CAAC;AAAE,QAAAE,GAAE,UAAQ,KAAK,gBAAcA,GAAE,MAAM,GAAE,KAAK,6BAA6Bb,IAAE,IAAIQ,GAAE,KAAK,aAAa,GAAEP,IAAEC,IAAEC,EAAC,GAAEO,GAAE;AAAA,MAAE,CAAE;AAAA,IAAC,OAAK;AAAC,YAAM,IAAE,EAAE,KAAK,aAAa,IAAE,IAAIF,GAAE,KAAK,aAAa,IAAEI;AAAE,WAAK,6BAA6BZ,IAAE,GAAEC,IAAEC,IAAEC,EAAC,GAAEO,GAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,6BAA6BV,IAAEC,IAAE,GAAEC,IAAE,IAAE,OAAG;AAAC,UAAMQ,KAAE,KAAK,wBAAwB,KAAK;AAAS,QAAG,YAAUA,GAAE,KAAK,CAAAV,GAAE,WAASC;AAAA,SAAM;AAAC,YAAK,EAAC,GAAEU,IAAE,GAAEC,GAAC,IAAEX,IAAE,CAACE,IAAEU,EAAC,IAAE,KAAK,4BAA4Bb,EAAC;AAAE,UAAIe,KAAEf,GAAEU,EAAC;AAAE,YAAMD,KAAEM,GAAEZ,EAAC,EAAE,SAAO;AAAE,MAAAY,GAAEZ,EAAC,EAAEU,EAAC,IAAE,CAACF,IAAEC,EAAC,GAAE,cAAYF,GAAE,SAAO,MAAIG,KAAEE,GAAEZ,EAAC,EAAEM,EAAC,IAAE,CAACE,IAAEC,EAAC,IAAEC,OAAIJ,OAAIM,GAAEZ,EAAC,EAAE,CAAC,IAAE,CAACQ,IAAEC,EAAC,KAAI,KAAK,UAAUZ,EAAC,MAAIe,KAAE,KAAK,wBAAwBA,IAAEf,IAAEW,IAAEC,EAAC,GAAEG,KAAE,KAAK,+BAA+BA,IAAEf,IAAE,GAAEE,IAAE,cAAYQ,GAAE,IAAI,GAAE,KAAK,gCAAgCK,EAAC,IAAG,KAAK,QAAQ,WAASL,GAAE,MAAM;AAAE,YAAM,IAAE,KAAK,2BAA2BV,EAAC,GAAEO,KAAEI,KAAE,EAAE,IAAI,CAAC,GAAE,IAAEC,KAAE,EAAE,IAAI,CAAC;AAAE,WAAK,wBAAwB,aAAa,CAAC,CAAC,GAAEL,IAAE,GAAE,CAAC,GAAE,MAAI,KAAK,SAAO,KAAK,OAAO,eAAe,KAAK,OAAO,SAAS,QAAQP,EAAC,GAAEC,EAAC,IAAED,GAAE,WAASC;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBD,IAAEC,IAAE,GAAEC,IAAE;AAAC,UAAK,EAAC,uBAAsB,EAAC,IAAED,GAAE;AAAW,eAAUS,MAAK,GAAE;AAAC,YAAMM,KAAE,KAAK,eAAe,UAAUN,EAAC,GAAE,EAAC,WAAUC,IAAE,YAAWC,GAAC,IAAEI,GAAE;AAAW,MAAAhB,GAAEW,EAAC,EAAEC,EAAC,IAAE,CAAC,GAAEV,EAAC,GAAEc,GAAE,WAASf,GAAE;AAAA,IAAQ;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,+BAA+BA,IAAEC,IAAE,GAAEC,IAAE,GAAE;AAAC,eAAUQ,MAAK,KAAK,iBAAiB,KAAGA,OAAIT,IAAE;AAAC,YAAK,EAAC,WAAUA,IAAE,YAAWU,IAAE,uBAAsBC,GAAC,IAAEF,GAAE,YAAWP,KAAEO,GAAEA,GAAE,UAAS,GAAER,IAAE,KAAK,IAAI,GAAEW,KAAEb,GAAEC,EAAC,EAAE,SAAO;AAAE,MAAAD,GAAEC,EAAC,EAAEU,EAAC,IAAE,CAACR,GAAE,GAAEA,GAAE,CAAC,GAAEO,GAAE,WAASP,IAAE,MAAI,MAAIQ,KAAEX,GAAEC,EAAC,EAAEY,EAAC,IAAE,CAACV,GAAE,GAAEA,GAAE,CAAC,IAAEQ,OAAIE,OAAIb,GAAEC,EAAC,EAAE,CAAC,IAAE,CAACE,GAAE,GAAEA,GAAE,CAAC;AAAI,iBAAUe,MAAKN,IAAE;AAAC,cAAMX,KAAE,KAAK,eAAe,UAAUiB,EAAC,GAAE,EAAC,WAAUhB,IAAE,YAAWc,GAAC,IAAEf,GAAE;AAAW,QAAAD,GAAEE,EAAC,EAAEc,EAAC,IAAE,CAACb,GAAE,GAAEA,GAAE,CAAC,GAAEF,GAAE,WAASE;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOH;AAAA,EAAC;AAAA,EAAC,8BAA8BA,IAAE;AAAC,UAAMC,KAAED,GAAE,SAAQ,IAAE,KAAK,UAAUC,EAAC;AAAE,SAAG,CAAC,KAAK,YAAYA,EAAC,MAAIA,GAAE,SAAO,KAAK,QAAQ,SAAS,QAAO,KAAK,eAAe,IAAEA,KAAE,IAAI,GAAE,KAAK,mBAAmBA,EAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BD,IAAE;AAAC,UAAMC,KAAED,GAAE;AAAQ,SAAK,UAAUC,EAAC,KAAG,CAAC,KAAK,YAAYA,EAAC,MAAIA,GAAE,SAAO,KAAK,QAAQ,SAAS,UAAS,KAAK,KAAK,SAAO,WAAU,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,0BAA0BD,IAAE;AAAC,UAAK,EAAC,oBAAmBC,IAAE,SAAQ,EAAC,IAAE,MAAKC,KAAE,EAAE;AAAS,QAAG,EAAEA,EAAC,KAAG,cAAYA,GAAE,QAAM,eAAaA,GAAE,KAAK,QAAM,CAAC;AAAE,UAAM,IAAEA,GAAE,MAAM,GAAEQ,KAAE,CAAC,GAAE,EAAC,WAAUC,IAAE,iBAAgBC,IAAE,eAAcT,GAAC,IAAEH,GAAE,YAAW,EAAC,GAAEe,IAAE,GAAEN,GAAC,IAAET,GAAE,UAAS,IAAE,MAAIG,KAAES,KAAE,IAAET,IAAEI,KAAEP,GAAE,CAAC;AAAE,WAAOO,GAAEI,EAAC,EAAE,OAAO,GAAE,GAAE,CAACI,IAAEN,EAAC,CAAC,GAAET,GAAE,aAAW,EAAC,GAAGC,IAAE,WAAUU,IAAE,YAAW,GAAE,uBAAsB,CAAC,EAAC,GAAED,GAAE,KAAK,EAAC,aAAYH,GAAEI,EAAC,EAAE,CAAC,GAAE,gBAAeA,IAAE,aAAY,EAAC,CAAC,GAAE,KAAK,QAAQ,WAAS,GAAED;AAAA,EAAC;AAAA,EAAC,gBAAgBV,IAAE;AAAC,IAAAA,cAAac,OAAId,KAAE,CAACA,EAAC;AAAG,eAAUC,MAAKD,GAAE,CAAAC,GAAE,SAAO,KAAK,QAAQ,SAAS;AAAS,SAAK,KAAK,oBAAmB,KAAK,iBAAiB,OAAOD,EAAC,CAAC,GAAE,KAAK,iBAAiBA,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAE,GAAE;AAAC,UAAK,EAAC,UAASE,GAAC,IAAE,KAAK,SAAQ,IAAE,IAAEA,GAAE,QAAMA,GAAE;AAAQ,IAAAF,cAAac,OAAId,KAAE,CAACA,EAAC;AAAG,eAAUC,MAAKD,GAAE,MAAK,iBAAiB,OAAO,KAAK,iBAAiB,QAAQC,EAAC,GAAE,CAAC,GAAE,KAAK,KAAK,oBAAmB,KAAK,gBAAgB,GAAEA,GAAE,IAAI,UAAS,CAAC;AAAE,SAAK,mBAAmBD,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,QAAG,KAAK,iBAAiB,QAAO;AAAC,YAAMA,KAAE,KAAK;AAAiB,iBAAUC,MAAK,KAAK,iBAAiB,CAAAA,GAAE,IAAI,UAAS,KAAK,QAAQ,SAAS,OAAO;AAAE,WAAK,KAAK,oBAAmB,CAAC,CAAC,GAAE,KAAK,mBAAmBD,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,IAAAA,GAAE,OAAO,SAASA,GAAE,GAAG,KAAG,CAACA,GAAE,UAAQ,KAAK,iBAAiB,UAAQ,KAAK,gBAAgB,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,UAAM,IAAE,KAAK,QAAQ;AAAS,QAAG,EAAE,CAAC,KAAG,cAAY,EAAE,QAAM,eAAa,EAAE,KAAK;AAAO,QAAG,cAAY,EAAE,QAAM,KAAK,eAAe,SAAO,KAAG,KAAK,eAAe,SAAO,EAAE;AAAO,IAAAA,cAAac,OAAId,KAAE,CAACA,EAAC;AAAG,UAAME,KAAE,KAAK,QAAQ,MAAM,GAAE,IAAE,EAAE,MAAM;AAAE,QAAIQ,KAAEV,GAAE,CAAC;AAAE,UAAMW,KAAE,CAAC;AAAE,IAAAX,cAAac,OAAId,KAAE,CAACA,EAAC;AAAG,eAAUC,MAAKD,IAAE;AAAC,YAAK,EAAC,GAAEA,IAAE,GAAEkB,GAAC,IAAEjB,GAAE;AAAS,eAAQA,KAAE,GAAEA,KAAES,GAAE,QAAOT,MAAI;AAAC,cAAMC,KAAEQ,GAAET,EAAC;AAAE,iBAAQe,KAAE,GAAEA,KAAEd,GAAE,QAAOc,MAAI;AAAC,gBAAK,CAACJ,IAAET,EAAC,IAAED,GAAEc,EAAC;AAAE,UAAAhB,OAAIY,MAAGM,OAAIf,OAAIQ,GAAE,KAAK,EAAC,aAAYD,GAAET,EAAC,EAAEe,EAAC,GAAE,gBAAef,IAAE,aAAYe,GAAC,CAAC,GAAEN,GAAET,EAAC,EAAE,OAAO,OAAOe,EAAC,GAAE,CAAC;AAAA,QAAE;AAAA,MAAC;AAAA,IAAC;AAAC,QAAG,cAAY,EAAE,KAAK,CAAAN,KAAEA,GAAE,OAAQ,CAAAV,OAAG;AAAC,UAAGA,GAAE,SAAO,EAAE,QAAM;AAAG,YAAK,CAACC,IAAEiB,EAAC,IAAElB,GAAE,CAAC,GAAE,CAACE,IAAEc,EAAC,IAAEhB,GAAEA,GAAE,SAAO,CAAC;AAAE,cAAO,MAAIA,GAAE,UAAQC,OAAIC,MAAGgB,OAAIF,QAAKf,OAAIC,MAAGgB,OAAIF,MAAGhB,GAAE,KAAKA,GAAE,CAAC,CAAC,GAAE;AAAA,IAAG,CAAE,GAAE,EAAE,QAAMU;AAAA,SAAM;AAAC,iBAAUV,MAAKU,GAAE,OAAIV,GAAE,UAAQU,GAAE,OAAOA,GAAE,QAAQV,EAAC,GAAE,CAAC;AAAE,QAAE,QAAMU;AAAA,IAAC;AAAC,SAAK,QAAQ,WAAS,GAAE,KAAK,QAAQ,GAAE,KAAK,uBAAuBV,IAAEE,IAAES,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUX,IAAE;AAAC,WAAO,KAAK,eAAe,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAO,KAAK,UAAUA,EAAC,KAAG,KAAK,iBAAiB,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAO,KAAK,iBAAiB,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,KAAK,SAAO,KAAK,YAAYA,EAAC,IAAE,SAAO;AAAA,EAAM;AAAA,EAAC,eAAeA,IAAEC,IAAE;AAAC,MAAE,KAAK,QAAQ,MAAID,KAAEA,OAAI,KAAK,UAAQ,KAAK,0BAA0BC,EAAC,IAAE,KAAK,yBAAyBA,EAAC,IAAE,KAAK,SAAS,MAAM;AAAA,EAAE;AAAA,EAAC,0BAA0BD,IAAE;AAAC,UAAK,EAAC,UAASC,IAAE,gBAAe,GAAE,MAAKC,GAAC,IAAE;AAAK,QAAG,EAAED,EAAC,EAAE;AAAO,UAAM,IAAE,IAAIS,GAAE,EAAC,gBAAe,EAAC,CAAC;AAAE,QAAGV,IAAE;AAAC,YAAK,EAAC,GAAEC,IAAE,GAAEiB,GAAC,IAAElB,GAAE,QAAOU,KAAER,GAAE,MAAMF,EAAC,GAAEW,KAAET,GAAE,MAAM,EAAED,IAAEiB,EAAC,CAAC,GAAEN,KAAET,GAAEQ,IAAED,EAAC;AAAE,QAAE,WAAS,EAAEE,EAAC,IAAEA,KAAEK;AAAA,IAAC;AAAC,IAAAhB,GAAE,OAAK;AAAA,EAAC;AAAA,EAAC,yBAAyBD,IAAE;AAAC,UAAK,EAAC,UAASC,IAAE,SAAQ,EAAC,UAAS,EAAC,GAAE,gBAAeC,IAAE,MAAK,EAAC,IAAE;AAAK,QAAG,EAAED,EAAC,EAAE;AAAO,UAAMS,KAAE,IAAIC,GAAE,EAAC,gBAAeT,GAAC,CAAC;AAAE,QAAG,EAAE,CAAC,MAAI,cAAY,EAAE,OAAKQ,GAAE,OAAKS,GAAE,CAAC,IAAE,eAAa,EAAE,SAAOT,GAAE,cAAYD,GAAE,CAAC,KAAIT,IAAE;AAAC,YAAK,EAAC,GAAEC,IAAE,GAAEiB,GAAC,IAAElB,GAAE,QAAOE,KAAE,EAAE,MAAMF,EAAC,GAAEW,KAAE,EAAE,MAAM,EAAEV,IAAEiB,EAAC,CAAC,GAAEN,KAAET,GAAEQ,IAAET,EAAC;AAAE,MAAAQ,GAAE,WAAS,EAAEE,EAAC,IAAEA,KAAEK;AAAA,IAAC;AAAC,IAAAhB,GAAE,OAAKS;AAAA,EAAC;AAAA,EAAC,oBAAoBV,IAAEC,IAAE;AAAC,UAAM,IAAE,IAAI,EAAE,KAAK,SAAQD,IAAEC,EAAC;AAAE,SAAK,KAAK,cAAa,CAAC,GAAE,KAAK,UAAU,eAAa,KAAK,UAAU,YAAY,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAEC,IAAE;AAAC,UAAM,IAAE,IAAI,EAAE,KAAK,SAAQD,IAAEC,EAAC;AAAE,SAAK,KAAK,QAAO,CAAC,GAAE,KAAK,UAAU,UAAQ,KAAK,UAAU,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMD,KAAE,KAAK;AAAqB,QAAG,CAACA,GAAE;AAAO,UAAK,EAAC,SAAQC,IAAE,SAAQ,EAAC,IAAED,IAAEE,KAAE,IAAI,EAAE,KAAK,SAAQD,IAAE,CAAC;AAAE,SAAK,KAAK,aAAYC,EAAC,GAAE,KAAK,UAAU,cAAY,KAAK,UAAU,WAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAE;AAAC,UAAMC,KAAE,IAAI,EAAE,KAAK,SAAQD,IAAE,KAAK,gBAAgB;AAAE,SAAK,KAAK,iBAAgBC,EAAC,GAAE,KAAK,UAAU,kBAAgB,KAAK,UAAU,eAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAE;AAAC,UAAMC,KAAE,IAAI,EAAE,KAAK,SAAQD,IAAE,KAAK,gBAAgB;AAAE,SAAK,KAAK,WAAUC,EAAC,GAAE,KAAK,UAAU,aAAW,KAAK,UAAU,UAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAE;AAAC,UAAMC,KAAE,IAAI,EAAE,KAAK,SAAQD,IAAE,KAAK,gBAAgB;AAAE,SAAK,KAAK,gBAAeC,EAAC,GAAE,KAAK,UAAU,iBAAe,KAAK,UAAU,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAE;AAAC,UAAMC,KAAE,IAAI,EAAED,EAAC;AAAE,SAAK,KAAK,UAASC,EAAC,GAAE,KAAK,UAAU,kBAAgB,KAAK,UAAU,eAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAE;AAAC,UAAMC,KAAE,IAAI,EAAED,EAAC;AAAE,SAAK,KAAK,YAAWC,EAAC,GAAE,KAAK,UAAU,oBAAkB,KAAK,UAAU,iBAAiBA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAEC,IAAE,GAAE;AAAC,UAAMC,KAAE,IAAI,GAAGF,IAAE,KAAK,SAAQC,IAAE,CAAC;AAAE,SAAK,KAAK,cAAaC,EAAC,GAAE,KAAK,UAAU,eAAa,KAAK,UAAU,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAEC,IAAE,GAAE;AAAC,UAAMC,KAAE,IAAI,GAAGF,IAAE,KAAK,SAAQC,IAAE,CAAC;AAAE,SAAK,KAAK,iBAAgBC,EAAC,GAAE,KAAK,UAAU,kBAAgB,KAAK,UAAU,eAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAuB;AAAC,MAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIA,GAAE,4BAA2B,+FAA+F,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,KAAE,CAAC,CAAC,GAAE,GAAG,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,GAAG,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,GAAG,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,GAAG,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,GAAE,CAAC,CAAC,GAAE,GAAG,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,GAAG,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,GAAG,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,GAAG,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,QAAO,MAAM,GAAE,KAAG,EAAE,CAACU,GAAE,iCAAiC,CAAC,GAAE,EAAE;AAAE,IAAM,KAAG;", "names": ["e", "t", "s", "h", "y", "f", "P", "m", "w", "l", "r", "n", "a", "p", "g", "c", "o", "j", "i", "u"]}