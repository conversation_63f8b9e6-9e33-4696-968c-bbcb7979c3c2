{"version": 3, "sources": ["../../@esri/calcite-components/dist/components/progress.js", "../../@esri/calcite-components/dist/components/math.js", "../../@esri/calcite-components/dist/components/input.js"], "sourcesContent": ["/*!\n * All material copyright ESRI, All Rights Reserved, unless otherwise specified.\n * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.\n * v1.0.8-next.4\n */\nimport { proxyCustomElement, HTMLElement, h } from '@stencil/core/internal/client/index.js';\n\nconst progressCss = \"@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host{position:relative;display:block;inline-size:100%}.track,.bar{position:absolute;inset-block-start:0px;block-size:2px}.track{z-index:1;inline-size:100%;overflow:hidden;background:var(--calcite-ui-border-3)}.bar{z-index:1;background-color:var(--calcite-ui-brand)}@media (forced-colors: active){.track{background-color:highlightText}.bar{background-color:linkText}}.indeterminate{inline-size:20%;animation:looping-progress-bar-ani calc(var(--calcite-internal-animation-timing-medium) * 11) linear infinite}.reversed{animation-direction:reverse}.text{padding-inline:0px;padding-block:1rem 0px;text-align:center;font-size:var(--calcite-font-size--2);line-height:1rem;font-weight:var(--calcite-font-weight-medium);color:var(--calcite-ui-text-2)}@keyframes looping-progress-bar-ani{0%{transform:translate3d(-100%, 0, 0)}50%{inline-size:40%}100%{transform:translate3d(600%, 0, 0)}}\";\n\nconst Progress = /*@__PURE__*/ proxyCustomElement(class extends HTMLElement {\n  constructor() {\n    super();\n    this.__registerHost();\n    this.__attachShadow();\n    this.type = \"determinate\";\n    this.value = 0;\n    this.label = undefined;\n    this.text = undefined;\n    this.reversed = false;\n  }\n  render() {\n    const isDeterminate = this.type === \"determinate\";\n    const barStyles = isDeterminate ? { width: `${this.value * 100}%` } : {};\n    return (h(\"div\", { \"aria-label\": this.label || this.text, \"aria-valuemax\": 1, \"aria-valuemin\": 0, \"aria-valuenow\": this.value, role: \"progressbar\" }, h(\"div\", { class: \"track\" }, h(\"div\", { class: {\n        bar: true,\n        indeterminate: this.type === \"indeterminate\",\n        reversed: this.reversed\n      }, style: barStyles })), this.text ? h(\"div\", { class: \"text\" }, this.text) : null));\n  }\n  get el() { return this; }\n  static get style() { return progressCss; }\n}, [1, \"calcite-progress\", {\n    \"type\": [513],\n    \"value\": [2],\n    \"label\": [1],\n    \"text\": [1],\n    \"reversed\": [516]\n  }]);\nfunction defineCustomElement() {\n  if (typeof customElements === \"undefined\") {\n    return;\n  }\n  const components = [\"calcite-progress\"];\n  components.forEach(tagName => { switch (tagName) {\n    case \"calcite-progress\":\n      if (!customElements.get(tagName)) {\n        customElements.define(tagName, Progress);\n      }\n      break;\n  } });\n}\ndefineCustomElement();\n\nexport { Progress as P, defineCustomElement as d };\n", "/*!\n * All material copyright ESRI, All Rights Reserved, unless otherwise specified.\n * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.\n * v1.0.8-next.4\n */\nconst clamp = (value, min, max) => Math.max(min, Math.min(value, max));\nconst decimalPlaces = (value) => {\n  const match = (\"\" + value).match(/(?:\\.(\\d+))?(?:[eE]([+-]?\\d+))?$/);\n  if (!match) {\n    return 0;\n  }\n  return Math.max(0, \n  // Number of digits right of decimal point.\n  (match[1] ? match[1].length : 0) -\n    // Adjust for scientific notation.\n    (match[2] ? +match[2] : 0));\n};\n\nexport { clamp as c, decimalPlaces as d };\n", "/*!\n * All material copyright ESRI, All Rights Reserved, unless otherwise specified.\n * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.\n * v1.0.8-next.4\n */\nimport { proxyCustomElement, HTMLElement, createEvent, h, Host } from '@stencil/core/internal/client/index.js';\nimport { b as getSlotted, i as isPrimaryPointerButton, e as setRequestedIcon, g as getElementProp, c as getElementDir, C as CSS_UTILITY } from './dom.js';\nimport { s as submitForm, c as connectForm, d as disconnectForm, H as HiddenFormInputSlot } from './form.js';\nimport { u as updateHostInteraction } from './interactive.js';\nimport { n as numberKeys } from './key.js';\nimport { c as connectLabel, d as disconnectLabel, g as getLabelText } from './label2.js';\nimport { a as setUpLoadableComponent, s as setComponentLoaded, c as componentLoaded } from './loadable.js';\nimport { n as numberStringFormatter, i as isValidNumber, p as parseNumberString, s as sanitizeNumberString, a as defaultNumberingSystem, c as connectLocalized, d as disconnectLocalized } from './locale.js';\nimport { d as decimalPlaces } from './math.js';\nimport { c as createObserver } from './observers.js';\nimport { u as updateMessages, c as connectMessages, d as disconnectMessages, s as setUpMessages } from './t9n.js';\nimport { d as defineCustomElement$2 } from './icon.js';\nimport { d as defineCustomElement$1 } from './progress.js';\n\nconst CSS = {\n  loader: \"loader\",\n  clearButton: \"clear-button\",\n  editingEnabled: \"editing-enabled\",\n  inlineChild: \"inline-child\",\n  inputIcon: \"icon\",\n  prefix: \"prefix\",\n  suffix: \"suffix\",\n  numberButtonWrapper: \"number-button-wrapper\",\n  buttonItemHorizontal: \"number-button-item--horizontal\",\n  wrapper: \"element-wrapper\",\n  inputWrapper: \"wrapper\",\n  actionWrapper: \"action-wrapper\",\n  resizeIconWrapper: \"resize-icon-wrapper\",\n  numberButtonItem: \"number-button-item\"\n};\nconst INPUT_TYPE_ICONS = {\n  tel: \"phone\",\n  password: \"lock\",\n  email: \"email-address\",\n  date: \"calendar\",\n  time: \"clock\",\n  search: \"search\"\n};\nconst SLOTS = {\n  action: \"action\"\n};\n\nconst inputCss = \"@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host([disabled]){pointer-events:none;cursor:default;-webkit-user-select:none;user-select:none;opacity:var(--calcite-ui-opacity-disabled)}:host{display:block}:host([scale=s]) input,:host([scale=s]) .prefix,:host([scale=s]) .suffix{block-size:1.5rem;padding-inline:0.5rem;font-size:var(--calcite-font-size--2);line-height:1rem}:host([scale=s]) textarea{block-size:1.5rem;min-block-size:1.5rem}:host([scale=s]) .number-button-wrapper,:host([scale=s]) .action-wrapper calcite-button,:host([scale=s]) .action-wrapper calcite-button button{block-size:1.5rem}:host([scale=s]) input[type=file]{block-size:1.5rem}:host([scale=s]) .clear-button{min-block-size:1.5rem;min-inline-size:1.5rem}:host([scale=s]) textarea{block-size:auto;padding-block:0.25rem;padding-inline:0.5rem;font-size:var(--calcite-font-size--2);line-height:1rem}:host([scale=m]) input,:host([scale=m]) .prefix,:host([scale=m]) .suffix{block-size:2rem;padding-inline:0.75rem;font-size:var(--calcite-font-size--1);line-height:1rem}:host([scale=m]) textarea{min-block-size:2rem}:host([scale=m]) .number-button-wrapper,:host([scale=m]) .action-wrapper calcite-button,:host([scale=m]) .action-wrapper calcite-button button{block-size:2rem}:host([scale=m]) input[type=file]{block-size:2rem}:host([scale=m]) .clear-button{min-block-size:2rem;min-inline-size:2rem}:host([scale=m]) textarea{block-size:auto;padding-block:0.5rem;padding-inline:0.75rem;font-size:var(--calcite-font-size--1);line-height:1rem}:host([scale=l]) input,:host([scale=l]) .prefix,:host([scale=l]) .suffix{block-size:2.75rem;padding-inline:1rem;font-size:var(--calcite-font-size-0);line-height:1.25rem}:host([scale=l]) textarea{min-block-size:2.75rem}:host([scale=l]) .number-button-wrapper,:host([scale=l]) .action-wrapper calcite-button,:host([scale=l]) .action-wrapper calcite-button button{block-size:2.75rem}:host([scale=l]) input[type=file]{block-size:2.75rem}:host([scale=l]) .clear-button{min-block-size:2.75rem;min-inline-size:2.75rem}:host([scale=l]) textarea{block-size:auto;padding-block:0.75rem;padding-inline:1rem;font-size:var(--calcite-font-size-0);line-height:1.25rem}:host([disabled]) textarea{resize:none}:host([disabled]) ::slotted([calcite-hydrated][disabled]),:host([disabled]) [calcite-hydrated][disabled]{opacity:1}:host textarea,:host input{transition:var(--calcite-animation-timing), block-size 0, outline-offset 0s;-webkit-appearance:none;position:relative;margin:0px;box-sizing:border-box;display:flex;max-block-size:100%;inline-size:100%;max-inline-size:100%;flex:1 1 0%;border-radius:0px;background-color:var(--calcite-ui-foreground-1);font-family:inherit;font-weight:var(--calcite-font-weight-normal);color:var(--calcite-ui-text-1)}:host input[type=search]::-webkit-search-decoration{-webkit-appearance:none}:host input,:host textarea{border-width:1px;border-style:solid;border-color:var(--calcite-ui-border-input);color:var(--calcite-ui-text-1)}:host input::placeholder,:host input:-ms-input-placeholder,:host input::-ms-input-placeholder,:host textarea::placeholder,:host textarea:-ms-input-placeholder,:host textarea::-ms-input-placeholder{font-weight:var(--calcite-font-weight-normal);color:var(--calcite-ui-text-3)}:host input:focus,:host textarea:focus{border-color:var(--calcite-ui-brand);color:var(--calcite-ui-text-1)}:host input[readonly],:host textarea[readonly]{background-color:var(--calcite-ui-background);font-weight:var(--calcite-font-weight-medium)}:host input[readonly]:focus,:host textarea[readonly]:focus{color:var(--calcite-ui-text-1)}:host calcite-icon{color:var(--calcite-ui-text-3)}:host textarea,:host input{outline-color:transparent}:host textarea:focus,:host input:focus{outline:2px solid var(--calcite-ui-brand);outline-offset:-2px}:host([status=invalid]) input,:host([status=invalid]) textarea{border-color:var(--calcite-ui-danger)}:host([status=invalid]) input:focus,:host([status=invalid]) textarea:focus{outline:2px solid var(--calcite-ui-danger);outline-offset:-2px}:host([scale=s]) .icon{inset-inline-start:0.5rem}:host([scale=m]) .icon{inset-inline-start:0.75rem}:host([scale=l]) .icon{inset-inline-start:1rem}:host([icon][scale=s]) input{padding-inline-start:2rem}:host([icon][scale=m]) input{padding-inline-start:2.5rem}:host([icon][scale=l]) input{padding-inline-start:3.5rem}.element-wrapper{position:relative;order:3;display:inline-flex;flex:1 1 0%;align-items:center}.icon{pointer-events:none;position:absolute;display:block;transition:all var(--calcite-animation-timing) ease-in-out 0s, outline 0s, outline-offset 0s}.icon,.resize-icon-wrapper{z-index:1}input[type=text]::-ms-clear,input[type=text]::-ms-reveal{display:none;block-size:0px;inline-size:0px}input[type=search]::-webkit-search-decoration,input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-results-button,input[type=search]::-webkit-search-results-decoration,input[type=date]::-webkit-clear-button,input[type=time]::-webkit-clear-button{display:none}.clear-button{pointer-events:initial;order:4;margin:0px;box-sizing:border-box;display:flex;min-block-size:100%;cursor:pointer;align-items:center;justify-content:center;align-self:stretch;border-width:1px;border-style:solid;border-color:var(--calcite-ui-border-input);background-color:var(--calcite-ui-foreground-1);outline-color:transparent;border-inline-start-width:0px}.clear-button:hover{background-color:var(--calcite-ui-foreground-2);transition:all var(--calcite-animation-timing) ease-in-out 0s, outline 0s, outline-offset 0s}.clear-button:hover calcite-icon{color:var(--calcite-ui-text-1);transition:all var(--calcite-animation-timing) ease-in-out 0s, outline 0s, outline-offset 0s}.clear-button:active{background-color:var(--calcite-ui-foreground-3)}.clear-button:active calcite-icon{color:var(--calcite-ui-text-1)}.clear-button:focus{outline:2px solid var(--calcite-ui-brand);outline-offset:-2px}.clear-button:disabled{opacity:var(--calcite-ui-opacity-disabled)}.loader{inset-block-start:1px;inset-inline:1px;pointer-events:none;position:absolute;display:block}.action-wrapper{order:7;display:flex}.prefix,.suffix{box-sizing:border-box;display:flex;block-size:auto;min-block-size:100%;-webkit-user-select:none;user-select:none;align-content:center;align-items:center;overflow-wrap:break-word;border-width:1px;border-style:solid;border-color:var(--calcite-ui-border-input);background-color:var(--calcite-ui-background);font-weight:var(--calcite-font-weight-medium);line-height:1;color:var(--calcite-ui-text-2)}.prefix{order:2;border-inline-end-width:0px}.suffix{order:5;border-inline-start-width:0px}:host([alignment=start]) textarea,:host([alignment=start]) input{text-align:start}:host([alignment=end]) textarea,:host([alignment=end]) input{text-align:end}:host input[type=number]{-moz-appearance:textfield}:host input[type=number]::-webkit-inner-spin-button,:host input[type=number]::-webkit-outer-spin-button{-webkit-appearance:none;-moz-appearance:textfield;margin:0px}.number-button-wrapper{pointer-events:none;order:6;box-sizing:border-box;display:flex;flex-direction:column;transition:all var(--calcite-animation-timing) ease-in-out 0s, outline 0s, outline-offset 0s}:host([number-button-type=vertical]) .wrapper{flex-direction:row;display:flex}:host([number-button-type=vertical]) input,:host([number-button-type=vertical]) textarea{order:2}:host([number-button-type=horizontal]) .calcite--rtl .number-button-item[data-adjustment=down] calcite-icon{transform:rotate(-90deg)}:host([number-button-type=horizontal]) .calcite--rtl .number-button-item[data-adjustment=up] calcite-icon{transform:rotate(-90deg)}.number-button-item.number-button-item--horizontal[data-adjustment=down],.number-button-item.number-button-item--horizontal[data-adjustment=up]{order:1;max-block-size:100%;min-block-size:100%;align-self:stretch}.number-button-item.number-button-item--horizontal[data-adjustment=down] calcite-icon,.number-button-item.number-button-item--horizontal[data-adjustment=up] calcite-icon{transform:rotate(90deg)}.number-button-item.number-button-item--horizontal[data-adjustment=down]{border-width:1px;border-style:solid;border-color:var(--calcite-ui-border-input);border-inline-end-width:0px}.number-button-item.number-button-item--horizontal[data-adjustment=down]:hover{background-color:var(--calcite-ui-foreground-2)}.number-button-item.number-button-item--horizontal[data-adjustment=down]:hover calcite-icon{color:var(--calcite-ui-text-1)}.number-button-item.number-button-item--horizontal[data-adjustment=up]{order:5}.number-button-item.number-button-item--horizontal[data-adjustment=up]:hover{background-color:var(--calcite-ui-foreground-2)}.number-button-item.number-button-item--horizontal[data-adjustment=up]:hover calcite-icon{color:var(--calcite-ui-text-1)}:host([number-button-type=vertical]) .number-button-item[data-adjustment=down]:hover{background-color:var(--calcite-ui-foreground-2)}:host([number-button-type=vertical]) .number-button-item[data-adjustment=down]:hover calcite-icon{color:var(--calcite-ui-text-1)}:host([number-button-type=vertical]) .number-button-item[data-adjustment=up]:hover{background-color:var(--calcite-ui-foreground-2)}:host([number-button-type=vertical]) .number-button-item[data-adjustment=up]:hover calcite-icon{color:var(--calcite-ui-text-1)}:host([number-button-type=vertical]) .number-button-item[data-adjustment=down]{border-block-start-width:0px}.number-button-item{max-block-size:50%;min-block-size:50%;pointer-events:initial;margin:0px;box-sizing:border-box;display:flex;cursor:pointer;align-items:center;align-self:center;border-width:1px;border-style:solid;border-color:var(--calcite-ui-border-input);background-color:var(--calcite-ui-foreground-1);padding-block:0px;padding-inline:0.5rem;transition:all var(--calcite-animation-timing) ease-in-out 0s, outline 0s, outline-offset 0s;border-inline-start-width:0px}.number-button-item calcite-icon{pointer-events:none;transition:all var(--calcite-animation-timing) ease-in-out 0s, outline 0s, outline-offset 0s}.number-button-item:focus{background-color:var(--calcite-ui-foreground-2)}.number-button-item:focus calcite-icon{color:var(--calcite-ui-text-1)}.number-button-item:active{background-color:var(--calcite-ui-foreground-3)}.number-button-item:disabled{pointer-events:none}.wrapper{position:relative;display:flex;flex-direction:row;align-items:center}:host input::-webkit-calendar-picker-indicator{display:none}:host input[type=date]::-webkit-input-placeholder{visibility:hidden !important}:host textarea::-webkit-resizer{position:absolute;inset-block-end:0px;box-sizing:border-box;padding-block:0px;padding-inline:0.25rem;inset-inline-end:0}.resize-icon-wrapper{inset-block-end:2px;inset-inline-end:2px;pointer-events:none;position:absolute;block-size:0.75rem;inline-size:0.75rem;background-color:var(--calcite-ui-foreground-1);color:var(--calcite-ui-text-3)}.resize-icon-wrapper calcite-icon{inset-block-end:0.25rem;inset-inline-end:0.25rem;transform:rotate(-45deg)}.calcite--rtl .resize-icon-wrapper calcite-icon{transform:rotate(45deg)}:host([type=color]) input{padding:0.25rem}:host([type=file]) input{cursor:pointer;border-width:1px;border-style:dashed;border-color:var(--calcite-ui-border-input);background-color:var(--calcite-ui-foreground-1);text-align:center}:host([type=file][scale=s]) input{padding-block:1px;padding-inline:0.5rem}:host([type=file][scale=m]) input{padding-block:0.25rem;padding-inline:0.75rem}:host([type=file][scale=l]) input{padding-block:0.5rem;padding-inline:1rem}:host(.no-bottom-border) input{border-block-end-width:0px}:host(.border-top-color-one) input{border-block-start-color:var(--calcite-ui-border-1)}:host .inline-child{background-color:transparent;transition:all var(--calcite-animation-timing) ease-in-out 0s, outline 0s, outline-offset 0s}:host .inline-child .editing-enabled{background-color:inherit}:host .inline-child:not(.editing-enabled){display:flex;cursor:pointer;text-overflow:ellipsis;border-color:transparent;padding-inline-start:0}::slotted(input[slot=hidden-form-input]){margin:0 !important;opacity:0 !important;outline:none !important;padding:0 !important;position:absolute !important;inset:0 !important;transform:none !important;-webkit-appearance:none !important;z-index:-1 !important}\";\n\nconst Input = /*@__PURE__*/ proxyCustomElement(class extends HTMLElement {\n  constructor() {\n    super();\n    this.__registerHost();\n    this.__attachShadow();\n    this.calciteInternalInputFocus = createEvent(this, \"calciteInternalInputFocus\", 6);\n    this.calciteInternalInputBlur = createEvent(this, \"calciteInternalInputBlur\", 6);\n    this.calciteInputInput = createEvent(this, \"calciteInputInput\", 7);\n    this.calciteInputChange = createEvent(this, \"calciteInputChange\", 6);\n    /** keep track of the rendered child type */\n    this.childElType = \"input\";\n    this.previousValueOrigin = \"initial\";\n    this.mutationObserver = createObserver(\"mutation\", () => this.setDisabledAction());\n    this.userChangedValue = false;\n    //--------------------------------------------------------------------------\n    //\n    //  Private Methods\n    //\n    //--------------------------------------------------------------------------\n    this.keyDownHandler = (event) => {\n      if (this.readOnly || this.disabled) {\n        return;\n      }\n      if (this.isClearable && event.key === \"Escape\") {\n        this.clearInputValue(event);\n        event.preventDefault();\n      }\n      if (event.key === \"Enter\" && !event.defaultPrevented) {\n        if (submitForm(this)) {\n          event.preventDefault();\n        }\n      }\n    };\n    this.clearInputValue = (nativeEvent) => {\n      this.setValue({\n        committing: true,\n        nativeEvent,\n        origin: \"user\",\n        value: \"\"\n      });\n    };\n    this.emitChangeIfUserModified = () => {\n      if (this.previousValueOrigin === \"user\" && this.value !== this.previousEmittedValue) {\n        this.calciteInputChange.emit();\n        this.setPreviousEmittedValue(this.value);\n      }\n    };\n    this.inputBlurHandler = () => {\n      this.calciteInternalInputBlur.emit();\n      this.emitChangeIfUserModified();\n    };\n    this.clickHandler = (event) => {\n      const slottedActionEl = getSlotted(this.el, \"action\");\n      if (event.target !== slottedActionEl) {\n        this.setFocus();\n      }\n    };\n    this.inputFocusHandler = () => {\n      this.calciteInternalInputFocus.emit();\n    };\n    this.inputInputHandler = (nativeEvent) => {\n      if (this.disabled || this.readOnly) {\n        return;\n      }\n      this.setValue({\n        nativeEvent,\n        origin: \"user\",\n        value: nativeEvent.target.value\n      });\n    };\n    this.inputKeyDownHandler = (event) => {\n      if (this.disabled || this.readOnly) {\n        return;\n      }\n      if (event.key === \"Enter\") {\n        this.emitChangeIfUserModified();\n      }\n    };\n    this.inputNumberInputHandler = (nativeEvent) => {\n      if (this.disabled || this.readOnly) {\n        return;\n      }\n      const value = nativeEvent.target.value;\n      numberStringFormatter.numberFormatOptions = {\n        locale: this.effectiveLocale,\n        numberingSystem: this.numberingSystem,\n        useGrouping: this.groupSeparator\n      };\n      const delocalizedValue = numberStringFormatter.delocalize(value);\n      if (nativeEvent.inputType === \"insertFromPaste\") {\n        if (!isValidNumber(delocalizedValue)) {\n          nativeEvent.preventDefault();\n        }\n        this.setValue({\n          nativeEvent,\n          origin: \"user\",\n          value: parseNumberString(delocalizedValue)\n        });\n        this.childNumberEl.value = this.localizedValue;\n      }\n      else {\n        this.setValue({\n          nativeEvent,\n          origin: \"user\",\n          value: delocalizedValue\n        });\n      }\n    };\n    this.inputNumberKeyDownHandler = (event) => {\n      if (this.type !== \"number\" || this.disabled || this.readOnly) {\n        return;\n      }\n      if (event.key === \"ArrowUp\") {\n        /* prevent default behavior of moving cursor to the beginning of the input when holding down ArrowUp */\n        event.preventDefault();\n        this.nudgeNumberValue(\"up\", event);\n        return;\n      }\n      if (event.key === \"ArrowDown\") {\n        this.nudgeNumberValue(\"down\", event);\n        return;\n      }\n      const supportedKeys = [\n        ...numberKeys,\n        \"ArrowLeft\",\n        \"ArrowRight\",\n        \"Backspace\",\n        \"Delete\",\n        \"Enter\",\n        \"Escape\",\n        \"Tab\"\n      ];\n      if (event.altKey || event.ctrlKey || event.metaKey) {\n        return;\n      }\n      const isShiftTabEvent = event.shiftKey && event.key === \"Tab\";\n      if (supportedKeys.includes(event.key) && (!event.shiftKey || isShiftTabEvent)) {\n        if (event.key === \"Enter\") {\n          this.emitChangeIfUserModified();\n        }\n        return;\n      }\n      numberStringFormatter.numberFormatOptions = {\n        locale: this.effectiveLocale,\n        numberingSystem: this.numberingSystem,\n        useGrouping: this.groupSeparator\n      };\n      if (event.key === numberStringFormatter.decimal) {\n        if (!this.value && !this.childNumberEl.value) {\n          return;\n        }\n        if (this.value && this.childNumberEl.value.indexOf(numberStringFormatter.decimal) === -1) {\n          return;\n        }\n      }\n      if (/[eE]/.test(event.key)) {\n        if (!this.value && !this.childNumberEl.value) {\n          return;\n        }\n        if (this.value && !/[eE]/.test(this.childNumberEl.value)) {\n          return;\n        }\n      }\n      if (event.key === \"-\") {\n        if (!this.value && !this.childNumberEl.value) {\n          return;\n        }\n        if (this.value && this.childNumberEl.value.split(\"-\").length <= 2) {\n          return;\n        }\n      }\n      event.preventDefault();\n    };\n    this.nudgeNumberValue = (direction, nativeEvent) => {\n      if ((nativeEvent instanceof KeyboardEvent && nativeEvent.repeat) || this.type !== \"number\") {\n        return;\n      }\n      const inputMax = this.maxString ? parseFloat(this.maxString) : null;\n      const inputMin = this.minString ? parseFloat(this.minString) : null;\n      const valueNudgeDelayInMs = 150;\n      this.incrementOrDecrementNumberValue(direction, inputMax, inputMin, nativeEvent);\n      if (this.nudgeNumberValueIntervalId) {\n        window.clearInterval(this.nudgeNumberValueIntervalId);\n      }\n      let firstValueNudge = true;\n      this.nudgeNumberValueIntervalId = window.setInterval(() => {\n        if (firstValueNudge) {\n          firstValueNudge = false;\n          return;\n        }\n        this.incrementOrDecrementNumberValue(direction, inputMax, inputMin, nativeEvent);\n      }, valueNudgeDelayInMs);\n    };\n    this.numberButtonPointerUpAndOutHandler = () => {\n      window.clearInterval(this.nudgeNumberValueIntervalId);\n    };\n    this.numberButtonPointerDownHandler = (event) => {\n      if (!isPrimaryPointerButton(event)) {\n        return;\n      }\n      event.preventDefault();\n      const direction = event.target.dataset.adjustment;\n      if (!this.disabled) {\n        this.nudgeNumberValue(direction, event);\n      }\n    };\n    this.hiddenInputChangeHandler = (event) => {\n      if (event.target.name === this.name) {\n        this.setValue({\n          value: event.target.value,\n          origin: \"direct\"\n        });\n      }\n      event.stopPropagation();\n    };\n    this.setChildElRef = (el) => {\n      this.childEl = el;\n    };\n    this.setChildNumberElRef = (el) => {\n      this.childNumberEl = el;\n    };\n    this.setInputValue = (newInputValue) => {\n      if (this.type === \"text\" && !this.childEl) {\n        return;\n      }\n      if (this.type === \"number\" && !this.childNumberEl) {\n        return;\n      }\n      this[`child${this.type === \"number\" ? \"Number\" : \"\"}El`].value = newInputValue;\n    };\n    this.setPreviousEmittedValue = (value) => {\n      this.previousEmittedValue = this.normalizeValue(value);\n    };\n    this.setPreviousValue = (value) => {\n      this.previousValue = this.normalizeValue(value);\n    };\n    this.setValue = ({ committing = false, nativeEvent, origin, previousValue, value }) => {\n      this.setPreviousValue(previousValue ?? this.value);\n      this.previousValueOrigin = origin;\n      if (this.type === \"number\") {\n        numberStringFormatter.numberFormatOptions = {\n          locale: this.effectiveLocale,\n          numberingSystem: this.numberingSystem,\n          useGrouping: this.groupSeparator,\n          signDisplay: \"never\"\n        };\n        const sanitizedValue = sanitizeNumberString(\n        // no need to delocalize a string that ia already in latn numerals\n        (this.numberingSystem && this.numberingSystem !== \"latn\") ||\n          defaultNumberingSystem !== \"latn\"\n          ? numberStringFormatter.delocalize(value)\n          : value);\n        const newValue = value && !sanitizedValue\n          ? isValidNumber(this.previousValue)\n            ? this.previousValue\n            : \"\"\n          : sanitizedValue;\n        const newLocalizedValue = numberStringFormatter.localize(newValue);\n        this.localizedValue = newLocalizedValue;\n        this.userChangedValue = origin === \"user\" && this.value !== newValue;\n        // don't sanitize the start of negative/decimal numbers, but\n        // don't set value to an invalid number\n        this.value = [\"-\", \".\"].includes(newValue) ? \"\" : newValue;\n      }\n      else {\n        this.userChangedValue = origin === \"user\" && this.value !== value;\n        this.value = value;\n      }\n      if (origin === \"direct\") {\n        this.setInputValue(value);\n        this.previousEmittedValue = value;\n      }\n      if (nativeEvent) {\n        const calciteInputInputEvent = this.calciteInputInput.emit();\n        if (calciteInputInputEvent.defaultPrevented) {\n          this.value = this.previousValue;\n          this.localizedValue =\n            this.type === \"number\"\n              ? numberStringFormatter.localize(this.previousValue)\n              : this.previousValue;\n        }\n        else if (committing) {\n          this.emitChangeIfUserModified();\n        }\n      }\n    };\n    this.inputKeyUpHandler = () => {\n      window.clearInterval(this.nudgeNumberValueIntervalId);\n    };\n    this.alignment = \"start\";\n    this.autofocus = false;\n    this.clearable = false;\n    this.disabled = false;\n    this.groupSeparator = false;\n    this.hidden = false;\n    this.icon = undefined;\n    this.iconFlipRtl = false;\n    this.label = undefined;\n    this.loading = false;\n    this.numberingSystem = undefined;\n    this.localeFormat = false;\n    this.max = undefined;\n    this.min = undefined;\n    this.maxLength = undefined;\n    this.minLength = undefined;\n    this.name = undefined;\n    this.numberButtonType = \"vertical\";\n    this.placeholder = undefined;\n    this.prefixText = undefined;\n    this.readOnly = false;\n    this.required = false;\n    this.scale = \"m\";\n    this.status = \"idle\";\n    this.step = undefined;\n    this.autocomplete = undefined;\n    this.pattern = undefined;\n    this.accept = undefined;\n    this.multiple = false;\n    this.inputMode = \"text\";\n    this.enterKeyHint = undefined;\n    this.suffixText = undefined;\n    this.editingEnabled = false;\n    this.type = \"text\";\n    this.value = \"\";\n    this.messages = undefined;\n    this.messageOverrides = undefined;\n    this.effectiveLocale = \"\";\n    this.defaultMessages = undefined;\n    this.localizedValue = undefined;\n    this.slottedActionElDisabledInternally = false;\n  }\n  disabledWatcher() {\n    this.setDisabledAction();\n  }\n  /** watcher to update number-to-string for max */\n  maxWatcher() {\n    this.maxString = this.max?.toString() || null;\n  }\n  /** watcher to update number-to-string for min */\n  minWatcher() {\n    this.minString = this.min?.toString() || null;\n  }\n  onMessagesChange() {\n    /* wired up by t9n util */\n  }\n  valueWatcher(newValue, previousValue) {\n    if (!this.userChangedValue) {\n      this.setValue({\n        origin: \"direct\",\n        previousValue,\n        value: newValue == null || newValue == \"\"\n          ? \"\"\n          : this.type === \"number\"\n            ? isValidNumber(newValue)\n              ? newValue\n              : this.previousValue || \"\"\n            : newValue\n      });\n      this.warnAboutInvalidNumberValue(newValue);\n    }\n    this.userChangedValue = false;\n  }\n  updateRequestedIcon() {\n    this.requestedIcon = setRequestedIcon(INPUT_TYPE_ICONS, this.icon, this.type);\n  }\n  get isClearable() {\n    return !this.isTextarea && (this.clearable || this.type === \"search\") && this.value.length > 0;\n  }\n  get isTextarea() {\n    return this.childElType === \"textarea\";\n  }\n  effectiveLocaleChange() {\n    updateMessages(this, this.effectiveLocale);\n  }\n  //--------------------------------------------------------------------------\n  //\n  //  Lifecycle\n  //\n  //--------------------------------------------------------------------------\n  connectedCallback() {\n    connectLocalized(this);\n    connectMessages(this);\n    this.scale = getElementProp(this.el, \"scale\", this.scale);\n    this.status = getElementProp(this.el, \"status\", this.status);\n    this.inlineEditableEl = this.el.closest(\"calcite-inline-editable\");\n    if (this.inlineEditableEl) {\n      this.editingEnabled = this.inlineEditableEl.editingEnabled || false;\n    }\n    connectLabel(this);\n    connectForm(this);\n    this.setPreviousEmittedValue(this.value);\n    this.setPreviousValue(this.value);\n    if (this.type === \"number\") {\n      this.warnAboutInvalidNumberValue(this.value);\n      this.setValue({\n        origin: \"connected\",\n        value: isValidNumber(this.value) ? this.value : \"\"\n      });\n    }\n    this.mutationObserver?.observe(this.el, { childList: true });\n    this.setDisabledAction();\n    this.el.addEventListener(\"calciteInternalHiddenInputChange\", this.hiddenInputChangeHandler);\n  }\n  disconnectedCallback() {\n    disconnectLabel(this);\n    disconnectForm(this);\n    disconnectLocalized(this);\n    disconnectMessages(this);\n    this.mutationObserver?.disconnect();\n    this.el.removeEventListener(\"calciteInternalHiddenInputChange\", this.hiddenInputChangeHandler);\n  }\n  async componentWillLoad() {\n    setUpLoadableComponent(this);\n    this.childElType = this.type === \"textarea\" ? \"textarea\" : \"input\";\n    this.maxString = this.max?.toString();\n    this.minString = this.min?.toString();\n    this.requestedIcon = setRequestedIcon(INPUT_TYPE_ICONS, this.icon, this.type);\n    await setUpMessages(this);\n  }\n  componentDidLoad() {\n    setComponentLoaded(this);\n  }\n  componentShouldUpdate(newValue, oldValue, property) {\n    if (this.type === \"number\" && property === \"value\" && newValue && !isValidNumber(newValue)) {\n      this.setValue({\n        origin: \"reset\",\n        value: oldValue\n      });\n      return false;\n    }\n    return true;\n  }\n  componentDidRender() {\n    updateHostInteraction(this);\n  }\n  //--------------------------------------------------------------------------\n  //\n  //  Public Methods\n  //\n  //--------------------------------------------------------------------------\n  /** Sets focus on the component. */\n  async setFocus() {\n    await componentLoaded(this);\n    if (this.type === \"number\") {\n      this.childNumberEl?.focus();\n    }\n    else {\n      this.childEl?.focus();\n    }\n  }\n  /** Selects all text of the component's `value`. */\n  async selectText() {\n    if (this.type === \"number\") {\n      this.childNumberEl?.select();\n    }\n    else {\n      this.childEl?.select();\n    }\n  }\n  // TODO: refactor so we don't need to sync the internals in color-picker\n  // https://github.com/Esri/calcite-components/issues/6100\n  /** @internal */\n  async internalSyncChildElValue() {\n    if (this.type === \"number\") {\n      this.childNumberEl.value = this.value;\n    }\n    else {\n      this.childEl.value = this.value;\n    }\n  }\n  onLabelClick() {\n    this.setFocus();\n  }\n  incrementOrDecrementNumberValue(direction, inputMax, inputMin, nativeEvent) {\n    const { value } = this;\n    const inputStep = this.step === \"any\" ? 1 : Math.abs(this.step || 1);\n    const inputVal = value && value !== \"\" ? parseFloat(value) : 0;\n    const adjustment = direction === \"up\" ? 1 : -1;\n    const nudgedValue = inputVal + inputStep * adjustment;\n    const finalValue = typeof inputMin === \"number\" && !isNaN(inputMin) && nudgedValue < inputMin\n      ? inputMin\n      : typeof inputMax === \"number\" && !isNaN(inputMax) && nudgedValue > inputMax\n        ? inputMax\n        : nudgedValue;\n    const inputValPlaces = decimalPlaces(inputVal);\n    const inputStepPlaces = decimalPlaces(inputStep);\n    this.setValue({\n      committing: true,\n      nativeEvent,\n      origin: \"user\",\n      value: finalValue.toFixed(Math.max(inputValPlaces, inputStepPlaces))\n    });\n  }\n  onFormReset() {\n    this.setValue({\n      origin: \"reset\",\n      value: this.defaultValue\n    });\n  }\n  syncHiddenFormInput(input) {\n    const { type } = this;\n    input.type = type;\n    if (type === \"number\") {\n      input.min = this.min?.toString(10) ?? \"\";\n      input.max = this.max?.toString(10) ?? \"\";\n    }\n    else if (type === \"text\") {\n      if (this.minLength != null) {\n        input.minLength = this.minLength;\n      }\n      if (this.maxLength != null) {\n        input.maxLength = this.maxLength;\n      }\n    }\n  }\n  setDisabledAction() {\n    const slottedActionEl = getSlotted(this.el, \"action\");\n    if (!slottedActionEl) {\n      return;\n    }\n    if (this.disabled) {\n      if (slottedActionEl.getAttribute(\"disabled\") == null) {\n        this.slottedActionElDisabledInternally = true;\n      }\n      slottedActionEl.setAttribute(\"disabled\", \"\");\n    }\n    else if (this.slottedActionElDisabledInternally) {\n      slottedActionEl.removeAttribute(\"disabled\");\n      this.slottedActionElDisabledInternally = false;\n    }\n  }\n  normalizeValue(value) {\n    return this.type === \"number\" ? (isValidNumber(value) ? value : \"\") : value;\n  }\n  warnAboutInvalidNumberValue(value) {\n    if (this.type === \"number\" && value && !isValidNumber(value)) {\n      console.warn(`The specified value \"${value}\" cannot be parsed, or is out of range.`);\n    }\n  }\n  // --------------------------------------------------------------------------\n  //\n  //  Render Methods\n  //\n  // --------------------------------------------------------------------------\n  render() {\n    const dir = getElementDir(this.el);\n    const loader = (h(\"div\", { class: CSS.loader }, h(\"calcite-progress\", { label: this.messages.loading, type: \"indeterminate\" })));\n    const inputClearButton = (h(\"button\", { \"aria-label\": this.messages.clear, class: CSS.clearButton, disabled: this.disabled || this.readOnly, onClick: this.clearInputValue, tabIndex: -1, type: \"button\" }, h(\"calcite-icon\", { icon: \"x\", scale: this.scale === \"l\" ? \"m\" : \"s\" })));\n    const iconEl = (h(\"calcite-icon\", { class: CSS.inputIcon, flipRtl: this.iconFlipRtl, icon: this.requestedIcon, scale: this.scale === \"l\" ? \"m\" : \"s\" }));\n    const isHorizontalNumberButton = this.numberButtonType === \"horizontal\";\n    const numberButtonsHorizontalUp = (h(\"button\", { \"aria-hidden\": \"true\", class: {\n        [CSS.numberButtonItem]: true,\n        [CSS.buttonItemHorizontal]: isHorizontalNumberButton\n      }, \"data-adjustment\": \"up\", disabled: this.disabled || this.readOnly, onPointerDown: this.numberButtonPointerDownHandler, onPointerOut: this.numberButtonPointerUpAndOutHandler, onPointerUp: this.numberButtonPointerUpAndOutHandler, tabIndex: -1, type: \"button\" }, h(\"calcite-icon\", { icon: \"chevron-up\", scale: this.scale === \"l\" ? \"m\" : \"s\" })));\n    const numberButtonsHorizontalDown = (h(\"button\", { \"aria-hidden\": \"true\", class: {\n        [CSS.numberButtonItem]: true,\n        [CSS.buttonItemHorizontal]: isHorizontalNumberButton\n      }, \"data-adjustment\": \"down\", disabled: this.disabled || this.readOnly, onPointerDown: this.numberButtonPointerDownHandler, onPointerOut: this.numberButtonPointerUpAndOutHandler, onPointerUp: this.numberButtonPointerUpAndOutHandler, tabIndex: -1, type: \"button\" }, h(\"calcite-icon\", { icon: \"chevron-down\", scale: this.scale === \"l\" ? \"m\" : \"s\" })));\n    const numberButtonsVertical = (h(\"div\", { class: CSS.numberButtonWrapper }, numberButtonsHorizontalUp, numberButtonsHorizontalDown));\n    const prefixText = h(\"div\", { class: CSS.prefix }, this.prefixText);\n    const suffixText = h(\"div\", { class: CSS.suffix }, this.suffixText);\n    const localeNumberInput = this.type === \"number\" ? (h(\"input\", { accept: this.accept, \"aria-label\": getLabelText(this), autocomplete: this.autocomplete, autofocus: this.autofocus ? true : null, defaultValue: this.defaultValue, disabled: this.disabled ? true : null, enterKeyHint: this.enterKeyHint, inputMode: this.inputMode, key: \"localized-input\", maxLength: this.maxLength, minLength: this.minLength, multiple: this.multiple, name: undefined, onBlur: this.inputBlurHandler, onFocus: this.inputFocusHandler, onInput: this.inputNumberInputHandler, onKeyDown: this.inputNumberKeyDownHandler, onKeyUp: this.inputKeyUpHandler, pattern: this.pattern, placeholder: this.placeholder || \"\", readOnly: this.readOnly, ref: this.setChildNumberElRef, type: \"text\", value: this.localizedValue })) : null;\n    const childEl = this.type !== \"number\"\n      ? [\n        h(this.childElType, { accept: this.accept, \"aria-label\": getLabelText(this), autocomplete: this.autocomplete, autofocus: this.autofocus ? true : null, class: {\n            [CSS.editingEnabled]: this.editingEnabled,\n            [CSS.inlineChild]: !!this.inlineEditableEl\n          }, defaultValue: this.defaultValue, disabled: this.disabled ? true : null, enterKeyHint: this.enterKeyHint, inputMode: this.inputMode, max: this.maxString, maxLength: this.maxLength, min: this.minString, minLength: this.minLength, multiple: this.multiple, name: this.name, onBlur: this.inputBlurHandler, onFocus: this.inputFocusHandler, onInput: this.inputInputHandler, onKeyDown: this.inputKeyDownHandler, onKeyUp: this.inputKeyUpHandler, pattern: this.pattern, placeholder: this.placeholder || \"\", readOnly: this.readOnly, ref: this.setChildElRef, required: this.required ? true : null, step: this.step, tabIndex: this.disabled || (this.inlineEditableEl && !this.editingEnabled) ? -1 : null, type: this.type, value: this.value }),\n        this.isTextarea ? (h(\"div\", { class: CSS.resizeIconWrapper }, h(\"calcite-icon\", { icon: \"chevron-down\", scale: this.scale === \"l\" ? \"m\" : \"s\" }))) : null\n      ]\n      : null;\n    return (h(Host, { onClick: this.clickHandler, onKeyDown: this.keyDownHandler }, h(\"div\", { class: { [CSS.inputWrapper]: true, [CSS_UTILITY.rtl]: dir === \"rtl\" } }, this.type === \"number\" && this.numberButtonType === \"horizontal\" && !this.readOnly\n      ? numberButtonsHorizontalDown\n      : null, this.prefixText ? prefixText : null, h(\"div\", { class: CSS.wrapper }, localeNumberInput, childEl, this.isClearable ? inputClearButton : null, this.requestedIcon ? iconEl : null, this.loading ? loader : null), h(\"div\", { class: CSS.actionWrapper }, h(\"slot\", { name: SLOTS.action })), this.type === \"number\" && this.numberButtonType === \"vertical\" && !this.readOnly\n      ? numberButtonsVertical\n      : null, this.suffixText ? suffixText : null, this.type === \"number\" && this.numberButtonType === \"horizontal\" && !this.readOnly\n      ? numberButtonsHorizontalUp\n      : null, h(HiddenFormInputSlot, { component: this }))));\n  }\n  static get assetsDirs() { return [\"assets\"]; }\n  get el() { return this; }\n  static get watchers() { return {\n    \"disabled\": [\"disabledWatcher\"],\n    \"max\": [\"maxWatcher\"],\n    \"min\": [\"minWatcher\"],\n    \"messageOverrides\": [\"onMessagesChange\"],\n    \"value\": [\"valueWatcher\"],\n    \"icon\": [\"updateRequestedIcon\"],\n    \"type\": [\"updateRequestedIcon\"],\n    \"effectiveLocale\": [\"effectiveLocaleChange\"]\n  }; }\n  static get style() { return inputCss; }\n}, [1, \"calcite-input\", {\n    \"alignment\": [513],\n    \"autofocus\": [516],\n    \"clearable\": [516],\n    \"disabled\": [516],\n    \"groupSeparator\": [516, \"group-separator\"],\n    \"hidden\": [516],\n    \"icon\": [520],\n    \"iconFlipRtl\": [516, \"icon-flip-rtl\"],\n    \"label\": [1],\n    \"loading\": [516],\n    \"numberingSystem\": [513, \"numbering-system\"],\n    \"localeFormat\": [4, \"locale-format\"],\n    \"max\": [514],\n    \"min\": [514],\n    \"maxLength\": [514, \"max-length\"],\n    \"minLength\": [514, \"min-length\"],\n    \"name\": [513],\n    \"numberButtonType\": [513, \"number-button-type\"],\n    \"placeholder\": [1],\n    \"prefixText\": [1, \"prefix-text\"],\n    \"readOnly\": [516, \"read-only\"],\n    \"required\": [516],\n    \"scale\": [1537],\n    \"status\": [1537],\n    \"step\": [520],\n    \"autocomplete\": [1],\n    \"pattern\": [1],\n    \"accept\": [1],\n    \"multiple\": [4],\n    \"inputMode\": [1, \"input-mode\"],\n    \"enterKeyHint\": [1, \"enter-key-hint\"],\n    \"suffixText\": [1, \"suffix-text\"],\n    \"editingEnabled\": [1540, \"editing-enabled\"],\n    \"type\": [513],\n    \"value\": [1025],\n    \"messages\": [1040],\n    \"messageOverrides\": [1040],\n    \"effectiveLocale\": [32],\n    \"defaultMessages\": [32],\n    \"localizedValue\": [32],\n    \"slottedActionElDisabledInternally\": [32],\n    \"setFocus\": [64],\n    \"selectText\": [64],\n    \"internalSyncChildElValue\": [64]\n  }]);\nfunction defineCustomElement() {\n  if (typeof customElements === \"undefined\") {\n    return;\n  }\n  const components = [\"calcite-input\", \"calcite-icon\", \"calcite-progress\"];\n  components.forEach(tagName => { switch (tagName) {\n    case \"calcite-input\":\n      if (!customElements.get(tagName)) {\n        customElements.define(tagName, Input);\n      }\n      break;\n    case \"calcite-icon\":\n      if (!customElements.get(tagName)) {\n        defineCustomElement$2();\n      }\n      break;\n    case \"calcite-progress\":\n      if (!customElements.get(tagName)) {\n        defineCustomElement$1();\n      }\n      break;\n  } });\n}\ndefineCustomElement();\n\nexport { Input as I, defineCustomElement as d };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,cAAc;AAEpB,IAAM,WAAyB,mBAAmB,cAAc,EAAY;AAAA,EAC1E,cAAc;AACZ,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,SAAS;AACP,UAAM,gBAAgB,KAAK,SAAS;AACpC,UAAM,YAAY,gBAAgB,EAAE,OAAO,GAAG,KAAK,QAAQ,GAAG,IAAI,IAAI,CAAC;AACvE,WAAQ,EAAE,OAAO,EAAE,cAAc,KAAK,SAAS,KAAK,MAAM,iBAAiB,GAAG,iBAAiB,GAAG,iBAAiB,KAAK,OAAO,MAAM,cAAc,GAAG,EAAE,OAAO,EAAE,OAAO,QAAQ,GAAG,EAAE,OAAO,EAAE,OAAO;AAAA,MACjM,KAAK;AAAA,MACL,eAAe,KAAK,SAAS;AAAA,MAC7B,UAAU,KAAK;AAAA,IACjB,GAAG,OAAO,UAAU,CAAC,CAAC,GAAG,KAAK,OAAO,EAAE,OAAO,EAAE,OAAO,OAAO,GAAG,KAAK,IAAI,IAAI,IAAI;AAAA,EACtF;AAAA,EACA,IAAI,KAAK;AAAE,WAAO;AAAA,EAAM;AAAA,EACxB,WAAW,QAAQ;AAAE,WAAO;AAAA,EAAa;AAC3C,GAAG,CAAC,GAAG,oBAAoB;AAAA,EACvB,QAAQ,CAAC,GAAG;AAAA,EACZ,SAAS,CAAC,CAAC;AAAA,EACX,SAAS,CAAC,CAAC;AAAA,EACX,QAAQ,CAAC,CAAC;AAAA,EACV,YAAY,CAAC,GAAG;AAClB,CAAC,CAAC;AACJ,SAASA,uBAAsB;AAC7B,MAAI,OAAO,mBAAmB,aAAa;AACzC;AAAA,EACF;AACA,QAAM,aAAa,CAAC,kBAAkB;AACtC,aAAW,QAAQ,aAAW;AAAE,YAAQ,SAAS;AAAA,MAC/C,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,yBAAe,OAAO,SAAS,QAAQ;AAAA,QACzC;AACA;AAAA,IACJ;AAAA,EAAE,CAAC;AACL;AACAA,qBAAoB;;;AC7CpB,IAAM,gBAAgB,CAAC,UAAU;AAC/B,QAAM,SAAS,KAAK,OAAO,MAAM,kCAAkC;AACnE,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,KAAK;AAAA,IAAI;AAAA;AAAA,KAEf,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,SAAS;AAAA,KAE3B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI;AAAA,EAAE;AAC9B;;;ACGA,IAAM,MAAM;AAAA,EACV,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AACpB;AACA,IAAM,mBAAmB;AAAA,EACvB,KAAK;AAAA,EACL,UAAU;AAAA,EACV,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AACV;AACA,IAAM,QAAQ;AAAA,EACZ,QAAQ;AACV;AAEA,IAAM,WAAW;AAEjB,IAAM,QAAsB,mBAAmB,cAAc,EAAY;AAAA,EACvE,cAAc;AACZ,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,4BAA4B,YAAY,MAAM,6BAA6B,CAAC;AACjF,SAAK,2BAA2B,YAAY,MAAM,4BAA4B,CAAC;AAC/E,SAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AACjE,SAAK,qBAAqB,YAAY,MAAM,sBAAsB,CAAC;AAEnE,SAAK,cAAc;AACnB,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB,eAAe,YAAY,MAAM,KAAK,kBAAkB,CAAC;AACjF,SAAK,mBAAmB;AAMxB,SAAK,iBAAiB,CAAC,UAAU;AAC/B,UAAI,KAAK,YAAY,KAAK,UAAU;AAClC;AAAA,MACF;AACA,UAAI,KAAK,eAAe,MAAM,QAAQ,UAAU;AAC9C,aAAK,gBAAgB,KAAK;AAC1B,cAAM,eAAe;AAAA,MACvB;AACA,UAAI,MAAM,QAAQ,WAAW,CAAC,MAAM,kBAAkB;AACpD,YAAI,WAAW,IAAI,GAAG;AACpB,gBAAM,eAAe;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,kBAAkB,CAAC,gBAAgB;AACtC,WAAK,SAAS;AAAA,QACZ,YAAY;AAAA,QACZ;AAAA,QACA,QAAQ;AAAA,QACR,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,SAAK,2BAA2B,MAAM;AACpC,UAAI,KAAK,wBAAwB,UAAU,KAAK,UAAU,KAAK,sBAAsB;AACnF,aAAK,mBAAmB,KAAK;AAC7B,aAAK,wBAAwB,KAAK,KAAK;AAAA,MACzC;AAAA,IACF;AACA,SAAK,mBAAmB,MAAM;AAC5B,WAAK,yBAAyB,KAAK;AACnC,WAAK,yBAAyB;AAAA,IAChC;AACA,SAAK,eAAe,CAAC,UAAU;AAC7B,YAAM,kBAAkB,WAAW,KAAK,IAAI,QAAQ;AACpD,UAAI,MAAM,WAAW,iBAAiB;AACpC,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AACA,SAAK,oBAAoB,MAAM;AAC7B,WAAK,0BAA0B,KAAK;AAAA,IACtC;AACA,SAAK,oBAAoB,CAAC,gBAAgB;AACxC,UAAI,KAAK,YAAY,KAAK,UAAU;AAClC;AAAA,MACF;AACA,WAAK,SAAS;AAAA,QACZ;AAAA,QACA,QAAQ;AAAA,QACR,OAAO,YAAY,OAAO;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,SAAK,sBAAsB,CAAC,UAAU;AACpC,UAAI,KAAK,YAAY,KAAK,UAAU;AAClC;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,SAAS;AACzB,aAAK,yBAAyB;AAAA,MAChC;AAAA,IACF;AACA,SAAK,0BAA0B,CAAC,gBAAgB;AAC9C,UAAI,KAAK,YAAY,KAAK,UAAU;AAClC;AAAA,MACF;AACA,YAAM,QAAQ,YAAY,OAAO;AACjC,4BAAsB,sBAAsB;AAAA,QAC1C,QAAQ,KAAK;AAAA,QACb,iBAAiB,KAAK;AAAA,QACtB,aAAa,KAAK;AAAA,MACpB;AACA,YAAM,mBAAmB,sBAAsB,WAAW,KAAK;AAC/D,UAAI,YAAY,cAAc,mBAAmB;AAC/C,YAAI,CAAC,cAAc,gBAAgB,GAAG;AACpC,sBAAY,eAAe;AAAA,QAC7B;AACA,aAAK,SAAS;AAAA,UACZ;AAAA,UACA,QAAQ;AAAA,UACR,OAAO,kBAAkB,gBAAgB;AAAA,QAC3C,CAAC;AACD,aAAK,cAAc,QAAQ,KAAK;AAAA,MAClC,OACK;AACH,aAAK,SAAS;AAAA,UACZ;AAAA,UACA,QAAQ;AAAA,UACR,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,4BAA4B,CAAC,UAAU;AAC1C,UAAI,KAAK,SAAS,YAAY,KAAK,YAAY,KAAK,UAAU;AAC5D;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,WAAW;AAE3B,cAAM,eAAe;AACrB,aAAK,iBAAiB,MAAM,KAAK;AACjC;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,aAAa;AAC7B,aAAK,iBAAiB,QAAQ,KAAK;AACnC;AAAA,MACF;AACA,YAAM,gBAAgB;AAAA,QACpB,GAAG;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,UAAI,MAAM,UAAU,MAAM,WAAW,MAAM,SAAS;AAClD;AAAA,MACF;AACA,YAAM,kBAAkB,MAAM,YAAY,MAAM,QAAQ;AACxD,UAAI,cAAc,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,YAAY,kBAAkB;AAC7E,YAAI,MAAM,QAAQ,SAAS;AACzB,eAAK,yBAAyB;AAAA,QAChC;AACA;AAAA,MACF;AACA,4BAAsB,sBAAsB;AAAA,QAC1C,QAAQ,KAAK;AAAA,QACb,iBAAiB,KAAK;AAAA,QACtB,aAAa,KAAK;AAAA,MACpB;AACA,UAAI,MAAM,QAAQ,sBAAsB,SAAS;AAC/C,YAAI,CAAC,KAAK,SAAS,CAAC,KAAK,cAAc,OAAO;AAC5C;AAAA,QACF;AACA,YAAI,KAAK,SAAS,KAAK,cAAc,MAAM,QAAQ,sBAAsB,OAAO,MAAM,IAAI;AACxF;AAAA,QACF;AAAA,MACF;AACA,UAAI,OAAO,KAAK,MAAM,GAAG,GAAG;AAC1B,YAAI,CAAC,KAAK,SAAS,CAAC,KAAK,cAAc,OAAO;AAC5C;AAAA,QACF;AACA,YAAI,KAAK,SAAS,CAAC,OAAO,KAAK,KAAK,cAAc,KAAK,GAAG;AACxD;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,KAAK;AACrB,YAAI,CAAC,KAAK,SAAS,CAAC,KAAK,cAAc,OAAO;AAC5C;AAAA,QACF;AACA,YAAI,KAAK,SAAS,KAAK,cAAc,MAAM,MAAM,GAAG,EAAE,UAAU,GAAG;AACjE;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAe;AAAA,IACvB;AACA,SAAK,mBAAmB,CAAC,WAAW,gBAAgB;AAClD,UAAK,uBAAuB,iBAAiB,YAAY,UAAW,KAAK,SAAS,UAAU;AAC1F;AAAA,MACF;AACA,YAAM,WAAW,KAAK,YAAY,WAAW,KAAK,SAAS,IAAI;AAC/D,YAAM,WAAW,KAAK,YAAY,WAAW,KAAK,SAAS,IAAI;AAC/D,YAAM,sBAAsB;AAC5B,WAAK,gCAAgC,WAAW,UAAU,UAAU,WAAW;AAC/E,UAAI,KAAK,4BAA4B;AACnC,eAAO,cAAc,KAAK,0BAA0B;AAAA,MACtD;AACA,UAAI,kBAAkB;AACtB,WAAK,6BAA6B,OAAO,YAAY,MAAM;AACzD,YAAI,iBAAiB;AACnB,4BAAkB;AAClB;AAAA,QACF;AACA,aAAK,gCAAgC,WAAW,UAAU,UAAU,WAAW;AAAA,MACjF,GAAG,mBAAmB;AAAA,IACxB;AACA,SAAK,qCAAqC,MAAM;AAC9C,aAAO,cAAc,KAAK,0BAA0B;AAAA,IACtD;AACA,SAAK,iCAAiC,CAAC,UAAU;AAC/C,UAAI,CAAC,uBAAuB,KAAK,GAAG;AAClC;AAAA,MACF;AACA,YAAM,eAAe;AACrB,YAAM,YAAY,MAAM,OAAO,QAAQ;AACvC,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,iBAAiB,WAAW,KAAK;AAAA,MACxC;AAAA,IACF;AACA,SAAK,2BAA2B,CAAC,UAAU;AACzC,UAAI,MAAM,OAAO,SAAS,KAAK,MAAM;AACnC,aAAK,SAAS;AAAA,UACZ,OAAO,MAAM,OAAO;AAAA,UACpB,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB;AAAA,IACxB;AACA,SAAK,gBAAgB,CAAC,OAAO;AAC3B,WAAK,UAAU;AAAA,IACjB;AACA,SAAK,sBAAsB,CAAC,OAAO;AACjC,WAAK,gBAAgB;AAAA,IACvB;AACA,SAAK,gBAAgB,CAAC,kBAAkB;AACtC,UAAI,KAAK,SAAS,UAAU,CAAC,KAAK,SAAS;AACzC;AAAA,MACF;AACA,UAAI,KAAK,SAAS,YAAY,CAAC,KAAK,eAAe;AACjD;AAAA,MACF;AACA,WAAK,QAAQ,KAAK,SAAS,WAAW,WAAW,EAAE,IAAI,EAAE,QAAQ;AAAA,IACnE;AACA,SAAK,0BAA0B,CAAC,UAAU;AACxC,WAAK,uBAAuB,KAAK,eAAe,KAAK;AAAA,IACvD;AACA,SAAK,mBAAmB,CAAC,UAAU;AACjC,WAAK,gBAAgB,KAAK,eAAe,KAAK;AAAA,IAChD;AACA,SAAK,WAAW,CAAC,EAAE,aAAa,OAAO,aAAa,QAAQ,eAAe,MAAM,MAAM;AACrF,WAAK,iBAAiB,iBAAiB,KAAK,KAAK;AACjD,WAAK,sBAAsB;AAC3B,UAAI,KAAK,SAAS,UAAU;AAC1B,8BAAsB,sBAAsB;AAAA,UAC1C,QAAQ,KAAK;AAAA,UACb,iBAAiB,KAAK;AAAA,UACtB,aAAa,KAAK;AAAA,UAClB,aAAa;AAAA,QACf;AACA,cAAM,iBAAiB;AAAA;AAAA,UAEtB,KAAK,mBAAmB,KAAK,oBAAoB,UAChD,2BAA2B,SACzB,sBAAsB,WAAW,KAAK,IACtC;AAAA,QAAK;AACT,cAAM,WAAW,SAAS,CAAC,iBACvB,cAAc,KAAK,aAAa,IAC9B,KAAK,gBACL,KACF;AACJ,cAAM,oBAAoB,sBAAsB,SAAS,QAAQ;AACjE,aAAK,iBAAiB;AACtB,aAAK,mBAAmB,WAAW,UAAU,KAAK,UAAU;AAG5D,aAAK,QAAQ,CAAC,KAAK,GAAG,EAAE,SAAS,QAAQ,IAAI,KAAK;AAAA,MACpD,OACK;AACH,aAAK,mBAAmB,WAAW,UAAU,KAAK,UAAU;AAC5D,aAAK,QAAQ;AAAA,MACf;AACA,UAAI,WAAW,UAAU;AACvB,aAAK,cAAc,KAAK;AACxB,aAAK,uBAAuB;AAAA,MAC9B;AACA,UAAI,aAAa;AACf,cAAM,yBAAyB,KAAK,kBAAkB,KAAK;AAC3D,YAAI,uBAAuB,kBAAkB;AAC3C,eAAK,QAAQ,KAAK;AAClB,eAAK,iBACH,KAAK,SAAS,WACV,sBAAsB,SAAS,KAAK,aAAa,IACjD,KAAK;AAAA,QACb,WACS,YAAY;AACnB,eAAK,yBAAyB;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,SAAK,oBAAoB,MAAM;AAC7B,aAAO,cAAc,KAAK,0BAA0B;AAAA,IACtD;AACA,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,oCAAoC;AAAA,EAC3C;AAAA,EACA,kBAAkB;AAChB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA,EAEA,aAAa;AAhYf;AAiYI,SAAK,cAAY,UAAK,QAAL,mBAAU,eAAc;AAAA,EAC3C;AAAA;AAAA,EAEA,aAAa;AApYf;AAqYI,SAAK,cAAY,UAAK,QAAL,mBAAU,eAAc;AAAA,EAC3C;AAAA,EACA,mBAAmB;AAAA,EAEnB;AAAA,EACA,aAAa,UAAU,eAAe;AACpC,QAAI,CAAC,KAAK,kBAAkB;AAC1B,WAAK,SAAS;AAAA,QACZ,QAAQ;AAAA,QACR;AAAA,QACA,OAAO,YAAY,QAAQ,YAAY,KACnC,KACA,KAAK,SAAS,WACZ,cAAc,QAAQ,IACpB,WACA,KAAK,iBAAiB,KACxB;AAAA,MACR,CAAC;AACD,WAAK,4BAA4B,QAAQ;AAAA,IAC3C;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,sBAAsB;AACpB,SAAK,gBAAgB,iBAAiB,kBAAkB,KAAK,MAAM,KAAK,IAAI;AAAA,EAC9E;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,CAAC,KAAK,eAAe,KAAK,aAAa,KAAK,SAAS,aAAa,KAAK,MAAM,SAAS;AAAA,EAC/F;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,wBAAwB;AACtB,mBAAe,MAAM,KAAK,eAAe;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AA5atB;AA6aI,qBAAiB,IAAI;AACrB,oBAAgB,IAAI;AACpB,SAAK,QAAQ,eAAe,KAAK,IAAI,SAAS,KAAK,KAAK;AACxD,SAAK,SAAS,eAAe,KAAK,IAAI,UAAU,KAAK,MAAM;AAC3D,SAAK,mBAAmB,KAAK,GAAG,QAAQ,yBAAyB;AACjE,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,KAAK,iBAAiB,kBAAkB;AAAA,IAChE;AACA,iBAAa,IAAI;AACjB,gBAAY,IAAI;AAChB,SAAK,wBAAwB,KAAK,KAAK;AACvC,SAAK,iBAAiB,KAAK,KAAK;AAChC,QAAI,KAAK,SAAS,UAAU;AAC1B,WAAK,4BAA4B,KAAK,KAAK;AAC3C,WAAK,SAAS;AAAA,QACZ,QAAQ;AAAA,QACR,OAAO,cAAc,KAAK,KAAK,IAAI,KAAK,QAAQ;AAAA,MAClD,CAAC;AAAA,IACH;AACA,eAAK,qBAAL,mBAAuB,QAAQ,KAAK,IAAI,EAAE,WAAW,KAAK;AAC1D,SAAK,kBAAkB;AACvB,SAAK,GAAG,iBAAiB,oCAAoC,KAAK,wBAAwB;AAAA,EAC5F;AAAA,EACA,uBAAuB;AApczB;AAqcI,oBAAgB,IAAI;AACpB,mBAAe,IAAI;AACnB,wBAAoB,IAAI;AACxB,uBAAmB,IAAI;AACvB,eAAK,qBAAL,mBAAuB;AACvB,SAAK,GAAG,oBAAoB,oCAAoC,KAAK,wBAAwB;AAAA,EAC/F;AAAA,EACA,MAAM,oBAAoB;AA5c5B;AA6cI,2BAAuB,IAAI;AAC3B,SAAK,cAAc,KAAK,SAAS,aAAa,aAAa;AAC3D,SAAK,aAAY,UAAK,QAAL,mBAAU;AAC3B,SAAK,aAAY,UAAK,QAAL,mBAAU;AAC3B,SAAK,gBAAgB,iBAAiB,kBAAkB,KAAK,MAAM,KAAK,IAAI;AAC5E,UAAM,cAAc,IAAI;AAAA,EAC1B;AAAA,EACA,mBAAmB;AACjB,uBAAmB,IAAI;AAAA,EACzB;AAAA,EACA,sBAAsB,UAAU,UAAU,UAAU;AAClD,QAAI,KAAK,SAAS,YAAY,aAAa,WAAW,YAAY,CAAC,cAAc,QAAQ,GAAG;AAC1F,WAAK,SAAS;AAAA,QACZ,QAAQ;AAAA,QACR,OAAO;AAAA,MACT,CAAC;AACD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB;AACnB,0BAAsB,IAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW;AA1enB;AA2eI,UAAM,gBAAgB,IAAI;AAC1B,QAAI,KAAK,SAAS,UAAU;AAC1B,iBAAK,kBAAL,mBAAoB;AAAA,IACtB,OACK;AACH,iBAAK,YAAL,mBAAc;AAAA,IAChB;AAAA,EACF;AAAA;AAAA,EAEA,MAAM,aAAa;AApfrB;AAqfI,QAAI,KAAK,SAAS,UAAU;AAC1B,iBAAK,kBAAL,mBAAoB;AAAA,IACtB,OACK;AACH,iBAAK,YAAL,mBAAc;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,2BAA2B;AAC/B,QAAI,KAAK,SAAS,UAAU;AAC1B,WAAK,cAAc,QAAQ,KAAK;AAAA,IAClC,OACK;AACH,WAAK,QAAQ,QAAQ,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,gCAAgC,WAAW,UAAU,UAAU,aAAa;AAC1E,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,YAAY,KAAK,SAAS,QAAQ,IAAI,KAAK,IAAI,KAAK,QAAQ,CAAC;AACnE,UAAM,WAAW,SAAS,UAAU,KAAK,WAAW,KAAK,IAAI;AAC7D,UAAM,aAAa,cAAc,OAAO,IAAI;AAC5C,UAAM,cAAc,WAAW,YAAY;AAC3C,UAAM,aAAa,OAAO,aAAa,YAAY,CAAC,MAAM,QAAQ,KAAK,cAAc,WACjF,WACA,OAAO,aAAa,YAAY,CAAC,MAAM,QAAQ,KAAK,cAAc,WAChE,WACA;AACN,UAAM,iBAAiB,cAAc,QAAQ;AAC7C,UAAM,kBAAkB,cAAc,SAAS;AAC/C,SAAK,SAAS;AAAA,MACZ,YAAY;AAAA,MACZ;AAAA,MACA,QAAQ;AAAA,MACR,OAAO,WAAW,QAAQ,KAAK,IAAI,gBAAgB,eAAe,CAAC;AAAA,IACrE,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,OAAO;AApiB7B;AAqiBI,UAAM,EAAE,KAAK,IAAI;AACjB,UAAM,OAAO;AACb,QAAI,SAAS,UAAU;AACrB,YAAM,QAAM,UAAK,QAAL,mBAAU,SAAS,QAAO;AACtC,YAAM,QAAM,UAAK,QAAL,mBAAU,SAAS,QAAO;AAAA,IACxC,WACS,SAAS,QAAQ;AACxB,UAAI,KAAK,aAAa,MAAM;AAC1B,cAAM,YAAY,KAAK;AAAA,MACzB;AACA,UAAI,KAAK,aAAa,MAAM;AAC1B,cAAM,YAAY,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,UAAM,kBAAkB,WAAW,KAAK,IAAI,QAAQ;AACpD,QAAI,CAAC,iBAAiB;AACpB;AAAA,IACF;AACA,QAAI,KAAK,UAAU;AACjB,UAAI,gBAAgB,aAAa,UAAU,KAAK,MAAM;AACpD,aAAK,oCAAoC;AAAA,MAC3C;AACA,sBAAgB,aAAa,YAAY,EAAE;AAAA,IAC7C,WACS,KAAK,mCAAmC;AAC/C,sBAAgB,gBAAgB,UAAU;AAC1C,WAAK,oCAAoC;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,WAAO,KAAK,SAAS,WAAY,cAAc,KAAK,IAAI,QAAQ,KAAM;AAAA,EACxE;AAAA,EACA,4BAA4B,OAAO;AACjC,QAAI,KAAK,SAAS,YAAY,SAAS,CAAC,cAAc,KAAK,GAAG;AAC5D,cAAQ,KAAK,wBAAwB,KAAK,yCAAyC;AAAA,IACrF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,UAAM,MAAM,cAAc,KAAK,EAAE;AACjC,UAAM,SAAU,EAAE,OAAO,EAAE,OAAO,IAAI,OAAO,GAAG,EAAE,oBAAoB,EAAE,OAAO,KAAK,SAAS,SAAS,MAAM,gBAAgB,CAAC,CAAC;AAC9H,UAAM,mBAAoB,EAAE,UAAU,EAAE,cAAc,KAAK,SAAS,OAAO,OAAO,IAAI,aAAa,UAAU,KAAK,YAAY,KAAK,UAAU,SAAS,KAAK,iBAAiB,UAAU,IAAI,MAAM,SAAS,GAAG,EAAE,gBAAgB,EAAE,MAAM,KAAK,OAAO,KAAK,UAAU,MAAM,MAAM,IAAI,CAAC,CAAC;AACnR,UAAM,SAAU,EAAE,gBAAgB,EAAE,OAAO,IAAI,WAAW,SAAS,KAAK,aAAa,MAAM,KAAK,eAAe,OAAO,KAAK,UAAU,MAAM,MAAM,IAAI,CAAC;AACtJ,UAAM,2BAA2B,KAAK,qBAAqB;AAC3D,UAAM,4BAA6B,EAAE,UAAU,EAAE,eAAe,QAAQ,OAAO;AAAA,MAC3E,CAAC,IAAI,gBAAgB,GAAG;AAAA,MACxB,CAAC,IAAI,oBAAoB,GAAG;AAAA,IAC9B,GAAG,mBAAmB,MAAM,UAAU,KAAK,YAAY,KAAK,UAAU,eAAe,KAAK,gCAAgC,cAAc,KAAK,oCAAoC,aAAa,KAAK,oCAAoC,UAAU,IAAI,MAAM,SAAS,GAAG,EAAE,gBAAgB,EAAE,MAAM,cAAc,OAAO,KAAK,UAAU,MAAM,MAAM,IAAI,CAAC,CAAC;AACzV,UAAM,8BAA+B,EAAE,UAAU,EAAE,eAAe,QAAQ,OAAO;AAAA,MAC7E,CAAC,IAAI,gBAAgB,GAAG;AAAA,MACxB,CAAC,IAAI,oBAAoB,GAAG;AAAA,IAC9B,GAAG,mBAAmB,QAAQ,UAAU,KAAK,YAAY,KAAK,UAAU,eAAe,KAAK,gCAAgC,cAAc,KAAK,oCAAoC,aAAa,KAAK,oCAAoC,UAAU,IAAI,MAAM,SAAS,GAAG,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,OAAO,KAAK,UAAU,MAAM,MAAM,IAAI,CAAC,CAAC;AAC7V,UAAM,wBAAyB,EAAE,OAAO,EAAE,OAAO,IAAI,oBAAoB,GAAG,2BAA2B,2BAA2B;AAClI,UAAM,aAAa,EAAE,OAAO,EAAE,OAAO,IAAI,OAAO,GAAG,KAAK,UAAU;AAClE,UAAM,aAAa,EAAE,OAAO,EAAE,OAAO,IAAI,OAAO,GAAG,KAAK,UAAU;AAClE,UAAM,oBAAoB,KAAK,SAAS,WAAY,EAAE,SAAS,EAAE,QAAQ,KAAK,QAAQ,cAAc,aAAa,IAAI,GAAG,cAAc,KAAK,cAAc,WAAW,KAAK,YAAY,OAAO,MAAM,cAAc,KAAK,cAAc,UAAU,KAAK,WAAW,OAAO,MAAM,cAAc,KAAK,cAAc,WAAW,KAAK,WAAW,KAAK,mBAAmB,WAAW,KAAK,WAAW,WAAW,KAAK,WAAW,UAAU,KAAK,UAAU,MAAM,QAAW,QAAQ,KAAK,kBAAkB,SAAS,KAAK,mBAAmB,SAAS,KAAK,yBAAyB,WAAW,KAAK,2BAA2B,SAAS,KAAK,mBAAmB,SAAS,KAAK,SAAS,aAAa,KAAK,eAAe,IAAI,UAAU,KAAK,UAAU,KAAK,KAAK,qBAAqB,MAAM,QAAQ,OAAO,KAAK,eAAe,CAAC,IAAK;AACpxB,UAAM,UAAU,KAAK,SAAS,WAC1B;AAAA,MACA,EAAE,KAAK,aAAa,EAAE,QAAQ,KAAK,QAAQ,cAAc,aAAa,IAAI,GAAG,cAAc,KAAK,cAAc,WAAW,KAAK,YAAY,OAAO,MAAM,OAAO;AAAA,QAC1J,CAAC,IAAI,cAAc,GAAG,KAAK;AAAA,QAC3B,CAAC,IAAI,WAAW,GAAG,CAAC,CAAC,KAAK;AAAA,MAC5B,GAAG,cAAc,KAAK,cAAc,UAAU,KAAK,WAAW,OAAO,MAAM,cAAc,KAAK,cAAc,WAAW,KAAK,WAAW,KAAK,KAAK,WAAW,WAAW,KAAK,WAAW,KAAK,KAAK,WAAW,WAAW,KAAK,WAAW,UAAU,KAAK,UAAU,MAAM,KAAK,MAAM,QAAQ,KAAK,kBAAkB,SAAS,KAAK,mBAAmB,SAAS,KAAK,mBAAmB,WAAW,KAAK,qBAAqB,SAAS,KAAK,mBAAmB,SAAS,KAAK,SAAS,aAAa,KAAK,eAAe,IAAI,UAAU,KAAK,UAAU,KAAK,KAAK,eAAe,UAAU,KAAK,WAAW,OAAO,MAAM,MAAM,KAAK,MAAM,UAAU,KAAK,YAAa,KAAK,oBAAoB,CAAC,KAAK,iBAAkB,KAAK,MAAM,MAAM,KAAK,MAAM,OAAO,KAAK,MAAM,CAAC;AAAA,MAC5tB,KAAK,aAAc,EAAE,OAAO,EAAE,OAAO,IAAI,kBAAkB,GAAG,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,OAAO,KAAK,UAAU,MAAM,MAAM,IAAI,CAAC,CAAC,IAAK;AAAA,IACvJ,IACE;AACJ,WAAQ,EAAE,MAAM,EAAE,SAAS,KAAK,cAAc,WAAW,KAAK,eAAe,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,IAAI,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,GAAG,QAAQ,MAAM,EAAE,GAAG,KAAK,SAAS,YAAY,KAAK,qBAAqB,gBAAgB,CAAC,KAAK,WAC1O,8BACA,MAAM,KAAK,aAAa,aAAa,MAAM,EAAE,OAAO,EAAE,OAAO,IAAI,QAAQ,GAAG,mBAAmB,SAAS,KAAK,cAAc,mBAAmB,MAAM,KAAK,gBAAgB,SAAS,MAAM,KAAK,UAAU,SAAS,IAAI,GAAG,EAAE,OAAO,EAAE,OAAO,IAAI,cAAc,GAAG,EAAE,QAAQ,EAAE,MAAM,MAAM,OAAO,CAAC,CAAC,GAAG,KAAK,SAAS,YAAY,KAAK,qBAAqB,cAAc,CAAC,KAAK,WAC1W,wBACA,MAAM,KAAK,aAAa,aAAa,MAAM,KAAK,SAAS,YAAY,KAAK,qBAAqB,gBAAgB,CAAC,KAAK,WACrH,4BACA,MAAM,EAAE,qBAAqB,EAAE,WAAW,KAAK,CAAC,CAAC,CAAC;AAAA,EACxD;AAAA,EACA,WAAW,aAAa;AAAE,WAAO,CAAC,QAAQ;AAAA,EAAG;AAAA,EAC7C,IAAI,KAAK;AAAE,WAAO;AAAA,EAAM;AAAA,EACxB,WAAW,WAAW;AAAE,WAAO;AAAA,MAC7B,YAAY,CAAC,iBAAiB;AAAA,MAC9B,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO,CAAC,YAAY;AAAA,MACpB,oBAAoB,CAAC,kBAAkB;AAAA,MACvC,SAAS,CAAC,cAAc;AAAA,MACxB,QAAQ,CAAC,qBAAqB;AAAA,MAC9B,QAAQ,CAAC,qBAAqB;AAAA,MAC9B,mBAAmB,CAAC,uBAAuB;AAAA,IAC7C;AAAA,EAAG;AAAA,EACH,WAAW,QAAQ;AAAE,WAAO;AAAA,EAAU;AACxC,GAAG,CAAC,GAAG,iBAAiB;AAAA,EACpB,aAAa,CAAC,GAAG;AAAA,EACjB,aAAa,CAAC,GAAG;AAAA,EACjB,aAAa,CAAC,GAAG;AAAA,EACjB,YAAY,CAAC,GAAG;AAAA,EAChB,kBAAkB,CAAC,KAAK,iBAAiB;AAAA,EACzC,UAAU,CAAC,GAAG;AAAA,EACd,QAAQ,CAAC,GAAG;AAAA,EACZ,eAAe,CAAC,KAAK,eAAe;AAAA,EACpC,SAAS,CAAC,CAAC;AAAA,EACX,WAAW,CAAC,GAAG;AAAA,EACf,mBAAmB,CAAC,KAAK,kBAAkB;AAAA,EAC3C,gBAAgB,CAAC,GAAG,eAAe;AAAA,EACnC,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,GAAG;AAAA,EACX,aAAa,CAAC,KAAK,YAAY;AAAA,EAC/B,aAAa,CAAC,KAAK,YAAY;AAAA,EAC/B,QAAQ,CAAC,GAAG;AAAA,EACZ,oBAAoB,CAAC,KAAK,oBAAoB;AAAA,EAC9C,eAAe,CAAC,CAAC;AAAA,EACjB,cAAc,CAAC,GAAG,aAAa;AAAA,EAC/B,YAAY,CAAC,KAAK,WAAW;AAAA,EAC7B,YAAY,CAAC,GAAG;AAAA,EAChB,SAAS,CAAC,IAAI;AAAA,EACd,UAAU,CAAC,IAAI;AAAA,EACf,QAAQ,CAAC,GAAG;AAAA,EACZ,gBAAgB,CAAC,CAAC;AAAA,EAClB,WAAW,CAAC,CAAC;AAAA,EACb,UAAU,CAAC,CAAC;AAAA,EACZ,YAAY,CAAC,CAAC;AAAA,EACd,aAAa,CAAC,GAAG,YAAY;AAAA,EAC7B,gBAAgB,CAAC,GAAG,gBAAgB;AAAA,EACpC,cAAc,CAAC,GAAG,aAAa;AAAA,EAC/B,kBAAkB,CAAC,MAAM,iBAAiB;AAAA,EAC1C,QAAQ,CAAC,GAAG;AAAA,EACZ,SAAS,CAAC,IAAI;AAAA,EACd,YAAY,CAAC,IAAI;AAAA,EACjB,oBAAoB,CAAC,IAAI;AAAA,EACzB,mBAAmB,CAAC,EAAE;AAAA,EACtB,mBAAmB,CAAC,EAAE;AAAA,EACtB,kBAAkB,CAAC,EAAE;AAAA,EACrB,qCAAqC,CAAC,EAAE;AAAA,EACxC,YAAY,CAAC,EAAE;AAAA,EACf,cAAc,CAAC,EAAE;AAAA,EACjB,4BAA4B,CAAC,EAAE;AACjC,CAAC,CAAC;AACJ,SAASC,uBAAsB;AAC7B,MAAI,OAAO,mBAAmB,aAAa;AACzC;AAAA,EACF;AACA,QAAM,aAAa,CAAC,iBAAiB,gBAAgB,kBAAkB;AACvE,aAAW,QAAQ,aAAW;AAAE,YAAQ,SAAS;AAAA,MAC/C,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,yBAAe,OAAO,SAAS,KAAK;AAAA,QACtC;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,8BAAsB;AAAA,QACxB;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,UAAAA,qBAAsB;AAAA,QACxB;AACA;AAAA,IACJ;AAAA,EAAE,CAAC;AACL;AACAA,qBAAoB;", "names": ["defineCustomElement", "defineCustomElement"]}