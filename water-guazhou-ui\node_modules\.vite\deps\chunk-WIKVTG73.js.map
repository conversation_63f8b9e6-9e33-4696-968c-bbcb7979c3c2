{"version": 3, "sources": ["../../@arcgis/core/support/requestPresets.js", "../../@arcgis/core/layers/support/fetchService.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../request.js\";async function t(t,o){const{data:r}=await e(t,{responseType:\"json\",query:{f:\"json\",...o?.customParameters,token:o?.apiKey}});return r}export{t as fetchArcGISServiceJSON};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{fetchArcGISServiceJSON as e}from\"../../support/requestPresets.js\";async function r(r,s){const a=await e(r,s);a.layers=a.layers.filter(t);const n={serviceJSON:a};if((a.currentVersion??0)<10.5)return n;const i=await e(r+\"/layers\",s);return n.layersJSON={layers:i.layers.filter(t),tables:i.tables},n}function t(e){return!e.type||\"Feature Layer\"===e.type}export{r as fetchFeatureService};\n"], "mappings": ";;;;;AAI6B,eAAe,EAAEA,IAAE,GAAE;AAAC,QAAK,EAAC,MAAKC,GAAC,IAAE,MAAM,EAAED,IAAE,EAAC,cAAa,QAAO,OAAM,EAAC,GAAE,QAAO,GAAG,uBAAG,kBAAiB,OAAM,uBAAG,OAAM,EAAC,CAAC;AAAE,SAAOC;AAAC;;;ACAzF,eAAe,EAAEC,IAAE,GAAE;AAAC,QAAM,IAAE,MAAM,EAAEA,IAAE,CAAC;AAAE,IAAE,SAAO,EAAE,OAAO,OAAOC,EAAC;AAAE,QAAM,IAAE,EAAC,aAAY,EAAC;AAAE,OAAI,EAAE,kBAAgB,KAAG,KAAK,QAAO;AAAE,QAAM,IAAE,MAAM,EAAED,KAAE,WAAU,CAAC;AAAE,SAAO,EAAE,aAAW,EAAC,QAAO,EAAE,OAAO,OAAOC,EAAC,GAAE,QAAO,EAAE,OAAM,GAAE;AAAC;AAAC,SAASA,GAAE,GAAE;AAAC,SAAM,CAAC,EAAE,QAAM,oBAAkB,EAAE;AAAI;", "names": ["t", "r", "r", "t"]}