import {
  s as s2,
  t as t3
} from "./chunk-YKSBY2H7.js";
import {
  t as t2
} from "./chunk-QIGFPFKT.js";
import {
  T
} from "./chunk-VX64NK2J.js";
import {
  A,
  B as B2,
  C,
  D,
  E,
  F,
  H,
  J as J2,
  K,
  L,
  M,
  N,
  O,
  P,
  R,
  S,
  U as U2,
  W,
  b,
  d,
  g,
  h,
  j,
  k,
  m as m2,
  p as p2,
  v as v3,
  w as w3,
  x,
  y
} from "./chunk-3A5PKKOG.js";
import {
  e as e2,
  l,
  s,
  u as u2
} from "./chunk-ATXSW42W.js";
import {
  a as a2,
  c,
  r
} from "./chunk-CF4Y76HG.js";
import {
  B,
  G,
  J,
  Ne,
  Se,
  Te,
  U,
  V,
  Z,
  Ze,
  be,
  ce,
  fe,
  le,
  re
} from "./chunk-REVHHZEO.js";
import {
  e,
  t
} from "./chunk-YFVPK4WM.js";
import {
  a
} from "./chunk-WXFAAYJL.js";
import {
  v as v2
} from "./chunk-ZACBBT3Y.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  u
} from "./chunk-3WCHZJQK.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  p,
  w
} from "./chunk-63M4K32A.js";
import {
  $
} from "./chunk-JXLVNWKF.js";

// node_modules/@arcgis/core/arcade/functions/geomasync.js
function wn(t4) {
  return 0 === a.indexOf("4.") ? v.fromExtent(t4) : new v({ spatialReference: t4.spatialReference, rings: [[[t4.xmin, t4.ymin], [t4.xmin, t4.ymax], [t4.xmax, t4.ymax], [t4.xmax, t4.ymin], [t4.xmin, t4.ymin]]] });
}
function hn(n, t4, e3) {
  if (B(n, 2, 2, t4, e3), n[0] instanceof p && n[1] instanceof p) ;
  else if (n[0] instanceof p && null === n[1]) ;
  else if (n[1] instanceof p && null === n[0]) ;
  else if (null !== n[0] || null !== n[1]) throw new t(t4, e.InvalidParameter, e3);
}
async function yn(n, t4) {
  if ("polygon" !== n.type && "polyline" !== n.type && "extent" !== n.type) return 0;
  let e3 = 1;
  if (n.spatialReference.vcsWkid || n.spatialReference.latestVcsWkid) {
    e3 = s(n.spatialReference) / $(n.spatialReference);
  }
  let r2 = 0;
  if ("polyline" === n.type) for (const a3 of n.paths) for (let n2 = 1; n2 < a3.length; n2++) r2 += e2(a3[n2], a3[n2 - 1], e3);
  else if ("polygon" === n.type) for (const a3 of n.rings) {
    for (let n2 = 1; n2 < a3.length; n2++) r2 += e2(a3[n2], a3[n2 - 1], e3);
    (a3[0][0] !== a3[a3.length - 1][0] || a3[0][1] !== a3[a3.length - 1][1] || void 0 !== a3[0][2] && a3[0][2] !== a3[a3.length - 1][2]) && (r2 += e2(a3[0], a3[a3.length - 1], e3));
  }
  else "extent" === n.type && (r2 += 2 * e2([n.xmin, n.ymin, 0], [n.xmax, n.ymin, 0], e3), r2 += 2 * e2([n.xmin, n.ymin, 0], [n.xmin, n.ymax, 0], e3), r2 *= 2, r2 += 4 * Math.abs(Z(n.zmax, 0) * e3 - Z(n.zmin, 0) * e3));
  const i = new m({ hasZ: false, hasM: false, spatialReference: n.spatialReference, paths: [[0, 0], [0, r2]] });
  return F(i, t4);
}
function pn(n) {
  "async" === n.mode && (n.functions.disjoint = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, a3) => (hn(a3 = be(a3), t4, e3), null === a3[0] || null === a3[1] || A(a3[0], a3[1])));
  }, n.functions.intersects = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, a3) => (hn(a3 = be(a3), t4, e3), null !== a3[0] && null !== a3[1] && h(a3[0], a3[1])));
  }, n.functions.touches = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, a3) => (hn(a3 = be(a3), t4, e3), null !== a3[0] && null !== a3[1] && S(a3[0], a3[1])));
  }, n.functions.crosses = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, a3) => (hn(a3 = be(a3), t4, e3), null !== a3[0] && null !== a3[1] && m2(a3[0], a3[1])));
  }, n.functions.within = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, a3) => (hn(a3 = be(a3), t4, e3), null !== a3[0] && null !== a3[1] && x(a3[0], a3[1])));
  }, n.functions.contains = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, a3) => (hn(a3 = be(a3), t4, e3), null !== a3[0] && null !== a3[1] && p2(a3[0], a3[1])));
  }, n.functions.overlaps = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, a3) => (hn(a3 = be(a3), t4, e3), null !== a3[0] && null !== a3[1] && O(a3[0], a3[1])));
  }, n.functions.equals = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, i) => (B(i, 2, 2, t4, e3), i[0] === i[1] || (i[0] instanceof p && i[1] instanceof p ? g(i[0], i[1]) : !(!U(i[0]) || !U(i[1])) && i[0].equals(i[1]))));
  }, n.functions.relate = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, o) => {
      if (o = be(o), B(o, 3, 3, t4, e3), o[0] instanceof p && o[1] instanceof p) return R(o[0], o[1], re(o[2]));
      if (o[0] instanceof p && null === o[1]) return false;
      if (o[1] instanceof p && null === o[0]) return false;
      if (null === o[0] && null === o[1]) return false;
      throw new t(t4, e.InvalidParameter, e3);
    });
  }, n.functions.intersection = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, a3) => (hn(a3 = be(a3), t4, e3), null === a3[0] || null === a3[1] ? null : D(a3[0], a3[1])));
  }, n.functions.union = function(e3, r2) {
    return n.standardFunctionAsync(e3, r2, (n2, a3, o) => {
      const s3 = [];
      if (0 === (o = be(o)).length) throw new t(e3, e.WrongNumberOfParameters, r2);
      if (1 === o.length) if (J(o[0])) {
        const n3 = be(o[0]);
        for (let t4 = 0; t4 < n3.length; t4++) if (null !== n3[t4]) {
          if (!(n3[t4] instanceof p)) throw new t(e3, e.InvalidParameter, r2);
          s3.push(n3[t4]);
        }
      } else {
        if (!V(o[0])) {
          if (o[0] instanceof p) return ce(c(o[0]), e3.spatialReference);
          if (null === o[0]) return null;
          throw new t(e3, e.InvalidParameter, r2);
        }
        {
          const n3 = be(o[0].toArray());
          for (let t4 = 0; t4 < n3.length; t4++) if (null !== n3[t4]) {
            if (!(n3[t4] instanceof p)) throw new t(e3, e.InvalidParameter, r2);
            s3.push(n3[t4]);
          }
        }
      }
      else for (let t4 = 0; t4 < o.length; t4++) if (null !== o[t4]) {
        if (!(o[t4] instanceof p)) throw new t(e3, e.InvalidParameter, r2);
        s3.push(o[t4]);
      }
      return 0 === s3.length ? null : b(s3);
    });
  }, n.functions.difference = function(e3, r2) {
    return n.standardFunctionAsync(e3, r2, (n2, a3, o) => (hn(o = be(o), e3, r2), null !== o[0] && null === o[1] ? c(o[0]) : null === o[0] ? null : E(o[0], o[1])));
  }, n.functions.symmetricdifference = function(e3, r2) {
    return n.standardFunctionAsync(e3, r2, (n2, a3, o) => (hn(o = be(o), e3, r2), null === o[0] && null === o[1] ? null : null === o[0] ? c(o[1]) : null === o[1] ? c(o[0]) : k(o[0], o[1])));
  }, n.functions.clip = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, o) => {
      if (o = be(o), B(o, 2, 2, t4, e3), !(o[1] instanceof w2) && null !== o[1]) throw new t(t4, e.InvalidParameter, e3);
      if (null === o[0]) return null;
      if (!(o[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      return null === o[1] ? null : y(o[0], o[1]);
    });
  }, n.functions.cut = function(e3, r2) {
    return n.standardFunctionAsync(e3, r2, (n2, o, s3) => {
      if (s3 = be(s3), B(s3, 2, 2, e3, r2), !(s3[1] instanceof m) && null !== s3[1]) throw new t(e3, e.InvalidParameter, r2);
      if (null === s3[0]) return [];
      if (!(s3[0] instanceof p)) throw new t(e3, e.InvalidParameter, r2);
      return null === s3[1] ? [c(s3[0])] : w3(s3[0], s3[1]);
    });
  }, n.functions.area = function(t4, r2) {
    return n.standardFunctionAsync(t4, r2, async (n2, o, s3) => {
      if (B(s3, 1, 2, t4, r2), null === (s3 = be(s3))[0]) return 0;
      if (G(s3[0])) {
        const n3 = await s3[0].sumArea(r(Z(s3[1], -1)), false, t4.abortSignal);
        if (t4.abortSignal.aborted) throw new t(t4, e.Cancelled, r2);
        return n3;
      }
      if (J(s3[0]) || V(s3[0])) {
        const n3 = Se(s3[0], t4.spatialReference);
        return null === n3 ? 0 : W(n3, r(Z(s3[1], -1)));
      }
      if (!(s3[0] instanceof p)) throw new t(t4, e.InvalidParameter, r2);
      return W(s3[0], r(Z(s3[1], -1)));
    });
  }, n.functions.areageodetic = function(t4, r2) {
    return n.standardFunctionAsync(t4, r2, async (n2, o, s3) => {
      if (B(s3, 1, 2, t4, r2), null === (s3 = be(s3))[0]) return 0;
      if (G(s3[0])) {
        const n3 = await s3[0].sumArea(r(Z(s3[1], -1)), true, t4.abortSignal);
        if (t4.abortSignal.aborted) throw new t(t4, e.Cancelled, r2);
        return n3;
      }
      if (J(s3[0]) || V(s3[0])) {
        const n3 = Se(s3[0], t4.spatialReference);
        return null === n3 ? 0 : K(n3, r(Z(s3[1], -1)));
      }
      if (!(s3[0] instanceof p)) throw new t(t4, e.InvalidParameter, r2);
      return K(s3[0], r(Z(s3[1], -1)));
    });
  }, n.functions.length = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, async (n2, o, s3) => {
      if (B(s3, 1, 2, t4, e3), null === (s3 = be(s3))[0]) return 0;
      if (G(s3[0])) {
        const n3 = await s3[0].sumLength(a2(Z(s3[1], -1)), false, t4.abortSignal);
        if (t4.abortSignal.aborted) throw new t(t4, e.Cancelled, e3);
        return n3;
      }
      if (J(s3[0]) || V(s3[0])) {
        const n3 = Ne(s3[0], t4.spatialReference);
        return null === n3 ? 0 : F(n3, a2(Z(s3[1], -1)));
      }
      if (!(s3[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      return F(s3[0], a2(Z(s3[1], -1)));
    });
  }, n.functions.length3d = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, o, s3) => {
      if (B(s3, 1, 2, t4, e3), null === (s3 = be(s3))[0]) return 0;
      if (J(s3[0]) || V(s3[0])) {
        const n3 = Ne(s3[0], t4.spatialReference);
        return null === n3 ? 0 : true === n3.hasZ ? yn(n3, a2(Z(s3[1], -1))) : F(n3, a2(Z(s3[1], -1)));
      }
      if (!(s3[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      return true === s3[0].hasZ ? yn(s3[0], a2(Z(s3[1], -1))) : F(s3[0], a2(Z(s3[1], -1)));
    });
  }, n.functions.lengthgeodetic = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, async (n2, o, s3) => {
      if (B(s3, 1, 2, t4, e3), null === (s3 = be(s3))[0]) return 0;
      if (G(s3[0])) {
        const n3 = await s3[0].sumLength(a2(Z(s3[1], -1)), true, t4.abortSignal);
        if (t4.abortSignal.aborted) throw new t(t4, e.Cancelled, e3);
        return n3;
      }
      if (J(s3[0]) || V(s3[0])) {
        const n3 = Ne(s3[0], t4.spatialReference);
        return null === n3 ? 0 : M(n3, a2(Z(s3[1], -1)));
      }
      if (!(s3[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      return M(s3[0], a2(Z(s3[1], -1)));
    });
  }, n.functions.distance = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, o, s3) => {
      s3 = be(s3), B(s3, 2, 3, t4, e3);
      let c2 = s3[0];
      (J(s3[0]) || V(s3[0])) && (c2 = Te(s3[0], t4.spatialReference));
      let f = s3[1];
      if ((J(s3[1]) || V(s3[1])) && (f = Te(s3[1], t4.spatialReference)), !(c2 instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      if (!(f instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      return d(c2, f, a2(Z(s3[2], -1)));
    });
  }, n.functions.distancegeodetic = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, o, s3) => {
      s3 = be(s3), B(s3, 2, 3, t4, e3);
      const l2 = s3[0], u3 = s3[1];
      if (!(l2 instanceof w)) throw new t(t4, e.InvalidParameter, e3);
      if (!(u3 instanceof w)) throw new t(t4, e.InvalidParameter, e3);
      const c2 = new m({ paths: [], spatialReference: l2.spatialReference });
      return c2.addPath([l2, u3]), M(c2, a2(Z(s3[2], -1)));
    });
  }, n.functions.densify = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, o, s3) => {
      if (s3 = be(s3), B(s3, 2, 3, t4, e3), null === s3[0]) return null;
      if (!(s3[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      const l2 = le(s3[1]);
      if (isNaN(l2)) throw new t(t4, e.InvalidParameter, e3);
      if (l2 <= 0) throw new t(t4, e.InvalidParameter, e3);
      return s3[0] instanceof v || s3[0] instanceof m ? C(s3[0], l2, a2(Z(s3[2], -1))) : s3[0] instanceof w2 ? C(wn(s3[0]), l2, a2(Z(s3[2], -1))) : s3[0];
    });
  }, n.functions.densifygeodetic = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, o, s3) => {
      if (s3 = be(s3), B(s3, 2, 3, t4, e3), null === s3[0]) return null;
      if (!(s3[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      const l2 = le(s3[1]);
      if (isNaN(l2)) throw new t(t4, e.InvalidParameter, e3);
      if (l2 <= 0) throw new t(t4, e.InvalidParameter, e3);
      return s3[0] instanceof v || s3[0] instanceof m ? U2(s3[0], l2, a2(Z(s3[2], -1))) : s3[0] instanceof w2 ? U2(wn(s3[0]), l2, a2(Z(s3[2], -1))) : s3[0];
    });
  }, n.functions.generalize = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, o, s3) => {
      if (s3 = be(s3), B(s3, 2, 4, t4, e3), null === s3[0]) return null;
      if (!(s3[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      const l2 = le(s3[1]);
      if (isNaN(l2)) throw new t(t4, e.InvalidParameter, e3);
      return B2(s3[0], l2, fe(Z(s3[2], true)), a2(Z(s3[3], -1)));
    });
  }, n.functions.buffer = function(e3, o) {
    return n.standardFunctionAsync(e3, o, (n2, s3, l2) => {
      if (l2 = be(l2), B(l2, 2, 3, e3, o), null === l2[0]) return null;
      if (!(l2[0] instanceof p)) throw new t(e3, e.InvalidParameter, o);
      const u3 = le(l2[1]);
      if (isNaN(u3)) throw new t(e3, e.InvalidParameter, o);
      return 0 === u3 ? c(l2[0]) : L(l2[0], u3, a2(Z(l2[2], -1)));
    });
  }, n.functions.buffergeodetic = function(e3, o) {
    return n.standardFunctionAsync(e3, o, (n2, s3, l2) => {
      if (l2 = be(l2), B(l2, 2, 3, e3, o), null === l2[0]) return null;
      if (!(l2[0] instanceof p)) throw new t(e3, e.InvalidParameter, o);
      const u3 = le(l2[1]);
      if (isNaN(u3)) throw new t(e3, e.InvalidParameter, o);
      return 0 === u3 ? c(l2[0]) : P(l2[0], u3, a2(Z(l2[2], -1)));
    });
  }, n.functions.offset = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, o, l2) => {
      if (l2 = be(l2), B(l2, 2, 6, t4, e3), null === l2[0]) return null;
      if (!(l2[0] instanceof v || l2[0] instanceof m)) throw new t(t4, e.InvalidParameter, e3);
      const u3 = le(l2[1]);
      if (isNaN(u3)) throw new t(t4, e.InvalidParameter, e3);
      const c2 = le(Z(l2[4], 10));
      if (isNaN(c2)) throw new t(t4, e.InvalidParameter, e3);
      const f = le(Z(l2[5], 0));
      if (isNaN(f)) throw new t(t4, e.InvalidParameter, e3);
      return v3(l2[0], u3, a2(Z(l2[2], -1)), re(Z(l2[3], "round")).toLowerCase(), c2, f);
    });
  }, n.functions.rotate = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, o) => {
      o = be(o), B(o, 2, 3, t4, e3);
      let s3 = o[0];
      if (null === s3) return null;
      if (!(s3 instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      s3 instanceof w2 && (s3 = v.fromExtent(s3));
      const l2 = le(o[1]);
      if (isNaN(l2)) throw new t(t4, e.InvalidParameter, e3);
      const u3 = Z(o[2], null);
      if (null === u3) return H(s3, l2);
      if (u3 instanceof w) return H(s3, l2, u3);
      throw new t(t4, e.InvalidParameter, e3);
    });
  }, n.functions.centroid = function(e3, r2) {
    return n.standardFunctionAsync(e3, r2, (n2, o, s3) => {
      if (s3 = be(s3), B(s3, 1, 1, e3, r2), null === s3[0]) return null;
      let f = s3[0];
      if ((J(s3[0]) || V(s3[0])) && (f = Te(s3[0], e3.spatialReference)), null === f) return null;
      if (!(f instanceof p)) throw new t(e3, e.InvalidParameter, r2);
      return f instanceof w ? ce(c(s3[0]), e3.spatialReference) : f instanceof v ? f.centroid : f instanceof m ? l(f) : f instanceof u ? u2(f) : f instanceof w2 ? f.center : null;
    });
  }, n.functions.multiparttosinglepart = function(e3, r2) {
    return n.standardFunctionAsync(e3, r2, async (n2, o, s3) => {
      s3 = be(s3), B(s3, 1, 1, e3, r2);
      const l2 = [];
      if (null === s3[0]) return null;
      if (!(s3[0] instanceof p)) throw new t(e3, e.InvalidParameter, r2);
      if (s3[0] instanceof w) return [ce(c(s3[0]), e3.spatialReference)];
      if (s3[0] instanceof w2) return [ce(c(s3[0]), e3.spatialReference)];
      const u3 = await N(s3[0]);
      if (u3 instanceof v) {
        const n3 = [], t4 = [];
        for (let e4 = 0; e4 < u3.rings.length; e4++) if (u3.isClockwise(u3.rings[e4])) {
          const t5 = v2({ rings: [u3.rings[e4]], hasZ: true === u3.hasZ, hazM: true === u3.hasM, spatialReference: u3.spatialReference.toJSON() });
          n3.push(t5);
        } else t4.push({ ring: u3.rings[e4], pt: u3.getPoint(e4, 0) });
        for (let e4 = 0; e4 < t4.length; e4++) for (let r3 = 0; r3 < n3.length; r3++) if (n3[r3].contains(t4[e4].pt)) {
          n3[r3].addRing(t4[e4].ring);
          break;
        }
        return n3;
      }
      if (u3 instanceof m) {
        const n3 = [];
        for (let t4 = 0; t4 < u3.paths.length; t4++) {
          const e4 = v2({ paths: [u3.paths[t4]], hasZ: true === u3.hasZ, hazM: true === u3.hasM, spatialReference: u3.spatialReference.toJSON() });
          n3.push(e4);
        }
        return n3;
      }
      if (s3[0] instanceof u) {
        const n3 = ce(c(s3[0]), e3.spatialReference);
        for (let t4 = 0; t4 < n3.points.length; t4++) l2.push(n3.getPoint(t4));
        return l2;
      }
      return null;
    });
  }, n.functions.issimple = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, o) => {
      if (o = be(o), B(o, 1, 1, t4, e3), null === o[0]) return true;
      if (!(o[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      return J2(o[0]);
    });
  }, n.functions.simplify = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, o) => {
      if (o = be(o), B(o, 1, 1, t4, e3), null === o[0]) return null;
      if (!(o[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      return N(o[0]);
    });
  }, n.functions.convexhull = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, (n2, r2, o) => {
      if (o = be(o), B(o, 1, 1, t4, e3), null === o[0]) return null;
      if (!(o[0] instanceof p)) throw new t(t4, e.InvalidParameter, e3);
      return j(o[0]);
    });
  }, n.functions.getuser = function(t4, e3) {
    return n.standardFunctionAsync(t4, e3, async (n2, r2, i) => {
      B(i, 0, 2, t4, e3);
      let o = Z(i[1], ""), l2 = true === o;
      if (o = true === o || false === o ? "" : re(o), 0 === i.length || i[0] instanceof t2) {
        let n3 = null;
        t4.services && t4.services.portal && (n3 = t4.services.portal), i.length > 0 && (n3 = t3(i[0], n3));
        const e4 = await s2(n3, o, l2);
        if (e4) {
          const n4 = JSON.parse(JSON.stringify(e4));
          for (const t5 of ["lastLogin", "created", "modified"]) void 0 !== n4[t5] && null !== n4[t5] && (n4[t5] = new Date(n4[t5]));
          return T.convertObjectToArcadeDictionary(n4, Ze(t4));
        }
        return null;
      }
      let u3 = null;
      if (G(i[0]) && (u3 = i[0]), u3) {
        if (l2 = false, o) return null;
        await u3.load();
        const n3 = await u3.getOwningSystemUrl();
        if (!n3) {
          if (!o) {
            const n4 = await u3.getIdentityUser();
            return n4 ? T.convertObjectToArcadeDictionary({ username: n4 }, Ze(t4)) : null;
          }
          return null;
        }
        let e4 = null;
        t4.services && t4.services.portal && (e4 = t4.services.portal), e4 = t3(new t2(n3), e4);
        const r3 = await s2(e4, o, l2);
        if (r3) {
          const n4 = JSON.parse(JSON.stringify(r3));
          for (const t5 of ["lastLogin", "created", "modified"]) void 0 !== n4[t5] && null !== n4[t5] && (n4[t5] = new Date(n4[t5]));
          return T.convertObjectToArcadeDictionary(n4, Ze(t4));
        }
        return null;
      }
      throw new t(t4, e.InvalidParameter, e3);
    });
  });
}

export {
  pn
};
//# sourceMappingURL=chunk-UOPBWEND.js.map
