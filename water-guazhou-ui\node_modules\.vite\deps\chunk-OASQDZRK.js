import {
  r as r2
} from "./chunk-REGYRSW7.js";
import {
  n
} from "./chunk-Y424ZXTG.js";
import {
  e as e2
} from "./chunk-UB5FTTH5.js";
import {
  o as o4
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  v
} from "./chunk-N3S5O3YO.js";
import {
  o as o3
} from "./chunk-BPRRRPC3.js";
import {
  o as o5
} from "./chunk-TUB4N6LD.js";
import {
  e,
  f
} from "./chunk-YV4RKNU4.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";
import {
  i
} from "./chunk-FOE4ICAJ.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/chunks/ImageMaterial.glsl.js
function f2(f3) {
  const w2 = new o2(), { vertex: h2, fragment: b } = w2;
  return v(h2, f3), w2.include(r2, f3), w2.attributes.add(O.POSITION, "vec3"), w2.attributes.add(O.UV0, "vec2"), w2.varyings.add("vpos", "vec3"), f3.hasMultipassTerrain && w2.varyings.add("depth", "float"), h2.uniforms.add(new e("textureCoordinateScaleFactor", (o6) => r(o6.texture) && r(o6.texture.descriptor.textureCoordinateScaleFactor) ? o6.texture.descriptor.textureCoordinateScaleFactor : i)), h2.code.add(o`
    void main(void) {
      vpos = position;
      ${f3.hasMultipassTerrain ? "depth = (view * vec4(vpos, 1.0)).z;" : ""}
      vTexCoord = uv0 * textureCoordinateScaleFactor;
      gl_Position = transformPosition(proj, view, vpos);
    }
  `), w2.include(u, f3), w2.include(n, f3), b.uniforms.add([new f("tex", (e3) => e3.texture), new o5("opacity", (e3) => e3.opacity)]), w2.varyings.add("vTexCoord", "vec2"), f3.output === h.Alpha ? b.code.add(o`
    void main() {
      discardBySlice(vpos);
      ${f3.hasMultipassTerrain ? "terrainDepthTest(gl_FragCoord, depth);" : ""}

      float alpha = texture2D(tex, vTexCoord).a * opacity;
      if (alpha  < ${o.float(o4)}) {
        discard;
      }

      gl_FragColor = vec4(alpha);
    }
    `) : (b.include(e2), b.code.add(o`
    void main() {
      discardBySlice(vpos);
      ${f3.hasMultipassTerrain ? "terrainDepthTest(gl_FragCoord, depth);" : ""}
      gl_FragColor = texture2D(tex, vTexCoord) * opacity;

      if (gl_FragColor.a < ${o.float(o4)}) {
        discard;
      }

      gl_FragColor = highlightSlice(gl_FragColor, vpos);
      ${f3.transparencyPassType === o3.Color ? "gl_FragColor = premultiplyAlpha(gl_FragColor);" : ""}
    }
    `)), w2;
}
var w = Object.freeze(Object.defineProperty({ __proto__: null, build: f2 }, Symbol.toStringTag, { value: "Module" }));

export {
  f2 as f,
  w
};
//# sourceMappingURL=chunk-OASQDZRK.js.map
