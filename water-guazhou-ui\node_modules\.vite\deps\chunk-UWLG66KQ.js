import {
  i2 as i,
  o as o2,
  r as r3
} from "./chunk-VCDD3IVD.js";
import {
  S
} from "./chunk-7GPM2ZU5.js";
import {
  r as r2
} from "./chunk-PWCXATLS.js";
import {
  k
} from "./chunk-XXXEFHEN.js";
import {
  E,
  K,
  Y,
  j
} from "./chunk-6IU6DQRF.js";
import {
  w
} from "./chunk-4RJYWSAT.js";
import {
  Wn,
  jn
} from "./chunk-UYAKJRPP.js";
import {
  f2 as f,
  se
} from "./chunk-JXLVNWKF.js";
import {
  o,
  q,
  v
} from "./chunk-MQAXMQFG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/support/euclideanAreaMeasurementUtils.js
function h(t2, o3 = U()) {
  return y(t2, o3);
}
function j2(t2, o3 = U()) {
  return y(t2, o3, false);
}
function y(n2, h2, j3 = n2.hasZ) {
  const y2 = i(n2.spatialReference), U2 = se(y2);
  if (t(U2)) return null;
  const C2 = (t2, o3) => !(o3.length < 2) && (o(t2, o3[0], o3[1], j3 && o3[2] || 0), true);
  let R = 0;
  for (const t2 of n2.rings) {
    const o3 = t2.length;
    if (o3 < 3) continue;
    const { positionsWorldCoords: r4 } = h2;
    for (; r4.length < o3; ) r4.push(n());
    const g = d, j4 = o(k2, 0, 0, 0), U3 = 1 / o3;
    for (let e = 0; e < o3; e++) {
      if (!C2(g, t2[e])) return null;
      if (!jn(g, n2.spatialReference, r4[e], y2)) return null;
      q(j4, j4, r4[e], U3);
    }
    const W = j(r4[0], r4[1], j4, E());
    if (0 === v(Y(W))) continue;
    for (let t3 = 0; t3 < o3; t3++) K(W, j4, r4[t3], r4[t3]);
    const b = v2(r4);
    for (let t3 = 0; t3 < b.length; t3 += 3) R += S(r4[b[t3]], r4[b[t3 + 1]], r4[b[t3 + 2]]);
  }
  return o2(R, U2);
}
var d = n();
var k2 = n();
function U() {
  return { positionsWorldCoords: [] };
}
function v2(t2) {
  return r2(C(t2), [], 2);
}
function C(t2) {
  const o3 = new Float64Array(2 * t2.length);
  for (let r4 = 0; r4 < t2.length; ++r4) {
    const n2 = t2[r4], e = 2 * r4;
    o3[e + 0] = n2[0], o3[e + 1] = n2[1];
  }
  return o3;
}

// node_modules/@arcgis/core/views/support/geodesicAreaMeasurementUtils.js
function m(e) {
  const { spatialReference: r4 } = e;
  return r3(r4, i2, a, u, e);
}
function i2(r4) {
  return o2(Math.abs(w([r4], "square-meters")[0]), "square-meters");
}
function a(t2) {
  try {
    return o2(Math.abs(k(t2, "square-meters")), "square-meters");
  } catch (s) {
    return null;
  }
}
function u(r4) {
  const o3 = [];
  return Wn(r4, o3) ? o2(Math.abs(w([{ type: "polygon", rings: o3, spatialReference: f.WGS84 }], "square-meters")[0]), "square-meters") : null;
}

// node_modules/@arcgis/core/views/support/automaticAreaMeasurementUtils.js
function i3(i4, u3, m2 = U()) {
  if ("on-the-ground" === u3) {
    const o3 = m(i4);
    return r(o3) ? o3 : j2(i4, m2);
  }
  return h(i4, m2);
}
function u2(e, r4 = U()) {
  return i3(e, "on-the-ground", r4);
}

export {
  i3 as i,
  u2 as u
};
//# sourceMappingURL=chunk-UWLG66KQ.js.map
