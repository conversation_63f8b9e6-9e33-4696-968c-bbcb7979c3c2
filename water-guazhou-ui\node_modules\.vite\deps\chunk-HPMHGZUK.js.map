{"version": 3, "sources": ["../../@arcgis/core/rest/geometryService/cut.js", "../../@arcgis/core/rest/geometryService/simplify.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../geometry.js\";import e from\"../../request.js\";import{getJsonType as t,fromJSON as r}from\"../../geometry/support/jsonUtils.js\";import{parseUrl as s}from\"../utils.js\";async function o(o,i,n,m){const a=s(o),p=i[0].spatialReference,u={...m,query:{...a.query,f:\"json\",sr:JSON.stringify(p),target:JSON.stringify({geometryType:t(i[0]),geometries:i}),cutter:JSON.stringify(n)}},c=await e(a.path+\"/cut\",u),{cutIndexes:f,geometries:g=[]}=c.data;return{cutIndexes:f,geometries:g.map((e=>{const t=r(e);return t.spatialReference=p,t}))}}export{o as cut};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"../../request.js\";import{urlToObject as t}from\"../../core/urlUtils.js\";import{getJsonType as i}from\"../../geometry/support/jsonUtils.js\";import{encodeGeometries as s,decodeGeometries as e}from\"./utils.js\";async function o(o,m,f){const n=\"string\"==typeof o?t(o):o,p=m[0].spatialReference,a=i(m[0]),u={...f,query:{...n.query,f:\"json\",sr:p.wkid?p.wkid:JSON.stringify(p),geometries:JSON.stringify(s(m))}},{data:y}=await r(n.path+\"/simplify\",u);return e(y.geometries,a,p)}export{o as simplify};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIiL,eAAeA,GAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAEA,EAAC,GAAE,IAAE,EAAE,CAAC,EAAE,kBAAiB,IAAE,EAAC,GAAG,GAAE,OAAM,EAAC,GAAG,EAAE,OAAM,GAAE,QAAO,IAAG,KAAK,UAAU,CAAC,GAAE,QAAO,KAAK,UAAU,EAAC,cAAa,EAAE,EAAE,CAAC,CAAC,GAAE,YAAW,EAAC,CAAC,GAAE,QAAO,KAAK,UAAU,CAAC,EAAC,EAAC,GAAEC,KAAE,MAAM,EAAE,EAAE,OAAK,QAAO,CAAC,GAAE,EAAC,YAAWC,IAAE,YAAW,IAAE,CAAC,EAAC,IAAED,GAAE;AAAK,SAAM,EAAC,YAAWC,IAAE,YAAW,EAAE,IAAK,OAAG;AAAC,UAAM,IAAE,EAAE,CAAC;AAAE,WAAO,EAAE,mBAAiB,GAAE;AAAA,EAAC,CAAE,EAAC;AAAC;;;ACA5T,eAAeC,GAAEA,IAAE,GAAEC,IAAE;AAAC,QAAM,IAAE,YAAU,OAAOD,KAAE,EAAEA,EAAC,IAAEA,IAAE,IAAE,EAAE,CAAC,EAAE,kBAAiB,IAAE,EAAE,EAAE,CAAC,CAAC,GAAE,IAAE,EAAC,GAAGC,IAAE,OAAM,EAAC,GAAG,EAAE,OAAM,GAAE,QAAO,IAAG,EAAE,OAAK,EAAE,OAAK,KAAK,UAAU,CAAC,GAAE,YAAW,KAAK,UAAU,EAAE,CAAC,CAAC,EAAC,EAAC,GAAE,EAAC,MAAK,EAAC,IAAE,MAAM,EAAE,EAAE,OAAK,aAAY,CAAC;AAAE,SAAO,EAAE,EAAE,YAAW,GAAE,CAAC;AAAC;", "names": ["o", "c", "f", "o", "f"]}