{"version": 3, "sources": ["../../@arcgis/core/renderers/visualVariables/support/ColorStop.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import o from\"../../../Color.js\";import{JSONSupport as e}from\"../../../core/JSONSupport.js\";import{property as t}from\"../../../core/accessorSupport/decorators/property.js\";import{Integer as s}from\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as l}from\"../../../core/accessorSupport/decorators/subclass.js\";import{writer as p}from\"../../../core/accessorSupport/decorators/writer.js\";var i;let c=i=class extends e{constructor(r){super(r),this.color=null,this.label=null,this.value=null}writeValue(r,o,e){o[e]=r??0}clone(){return new i({color:this.color&&this.color.clone(),label:this.label,value:this.value})}};r([t({type:o,json:{type:[s],write:!0}})],c.prototype,\"color\",void 0),r([t({type:String,json:{write:!0}})],c.prototype,\"label\",void 0),r([t({type:Number,json:{write:{writerEnsuresNonNull:!0}}})],c.prototype,\"value\",void 0),r([p(\"value\")],c.prototype,\"writeValue\",null),c=i=r([l(\"esri.renderers.visualVariables.support.ColorStop\")],c);const a=c;export{a as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIme,IAAI;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,QAAM,MAAK,KAAK,QAAM,MAAK,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,WAAWA,IAAE,GAAEC,IAAE;AAAC,MAAEA,EAAC,IAAED,MAAG;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,OAAM,KAAK,SAAO,KAAK,MAAM,MAAM,GAAE,OAAM,KAAK,OAAM,OAAM,KAAK,MAAK,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKE,IAAE,MAAK,EAAC,MAAK,CAAC,CAAC,GAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,sBAAqB,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,kDAAkD,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;", "names": ["r", "e", "l", "a"]}