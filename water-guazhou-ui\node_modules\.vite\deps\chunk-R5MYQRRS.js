import {
  E,
  G,
  f2 as f,
  k
} from "./chunk-JXLVNWKF.js";
import {
  s
} from "./chunk-LJHVXLBF.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/webMercatorUtils.js
var o = 57.29577951308232;
var u = 0.017453292519943;
function l(n) {
  return n * o;
}
function p2(n) {
  return n * u;
}
function f2(n) {
  return n / s.radius;
}
function c(n) {
  return Math.PI / 2 - 2 * Math.atan(Math.exp(-n / s.radius));
}
function h(n) {
  return null != n.wkid || null != n.wkt;
}
var m = [0, 0];
function x(n, t2, e, i, r) {
  const s2 = n, a = r;
  if (a.spatialReference = e, "x" in s2 && "x" in a) [a.x, a.y] = t2(s2.x, s2.y, m, i);
  else if ("xmin" in s2 && "xmin" in a) [a.xmin, a.ymin] = t2(s2.xmin, s2.ymin, m, i), [a.xmax, a.ymax] = t2(s2.xmax, s2.ymax, m, i);
  else if ("paths" in s2 && "paths" in a || "rings" in s2 && "rings" in a) {
    const n2 = "paths" in s2 ? s2.paths : s2.rings, e2 = [];
    let r2;
    for (let s3 = 0; s3 < n2.length; s3++) {
      const a2 = n2[s3];
      r2 = [], e2.push(r2);
      for (let n3 = 0; n3 < a2.length; n3++) r2.push(t2(a2[n3][0], a2[n3][1], [0, 0], i)), a2[n3].length > 2 && r2[n3].push(a2[n3][2]), a2[n3].length > 3 && r2[n3].push(a2[n3][3]);
    }
    "paths" in a ? a.paths = e2 : a.rings = e2;
  } else if ("points" in s2 && "points" in a) {
    const n2 = s2.points, e2 = [];
    for (let r2 = 0; r2 < n2.length; r2++) e2[r2] = t2(n2[r2][0], n2[r2][1], [0, 0], i), n2[r2].length > 2 && e2[r2].push(n2[r2][2]), n2[r2].length > 3 && e2[r2].push(n2[r2][3]);
    a.points = e2;
  }
  return r;
}
function g(n, t2) {
  const e = n && (h(n) ? n : n.spatialReference), i = t2 && (h(t2) ? t2 : t2.spatialReference);
  return !(n && "type" in n && "mesh" === n.type || t2 && "type" in t2 && "mesh" === t2.type || !e || !i) && (!!E(i, e) || (k(i) && G(e) || k(e) && G(i)));
}
function M(i, o2) {
  if (t(i)) return null;
  const u2 = i.spatialReference, l2 = o2 && (h(o2) ? o2 : o2.spatialReference);
  return g(u2, l2) ? E(u2, l2) ? p(i) : k(l2) ? x(i, y, f.WebMercator, false, p(i)) : G(l2) ? x(i, d, f.WGS84, false, p(i)) : null : null;
}
function y(n, t2, e = [0, 0]) {
  t2 > 89.99999 ? t2 = 89.99999 : t2 < -89.99999 && (t2 = -89.99999);
  const r = p2(t2);
  return e[0] = p2(n) * s.radius, e[1] = s.halfSemiMajorAxis * Math.log((1 + Math.sin(r)) / (1 - Math.sin(r))), e;
}
function d(n, t2, e = [0, 0], r = false) {
  const s2 = l(n / s.radius);
  return e[0] = r ? s2 : s2 - 360 * Math.floor((s2 + 180) / 360), e[1] = l(Math.PI / 2 - 2 * Math.atan(Math.exp(-t2 / s.radius))), e;
}
function R(t2, i = false, r = p(t2)) {
  return x(t2, y, f.WebMercator, i, r);
}
function j(t2, i = false, r = p(t2)) {
  return x(t2, d, f.WGS84, i, r);
}

export {
  f2 as f,
  c,
  g,
  M,
  y,
  d,
  R,
  j
};
//# sourceMappingURL=chunk-R5MYQRRS.js.map
