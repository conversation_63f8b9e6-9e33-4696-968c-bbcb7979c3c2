import {
  b
} from "./chunk-QMNV7QQK.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  r
} from "./chunk-WXFAAYJL.js";

// node_modules/@arcgis/core/arcade/portalUtils.js
function t(r2, e) {
  if (null === r2) return e;
  return new b({ url: r2.field("url") });
}
async function s(n, t2, s2) {
  var _a;
  const u = (_a = r) == null ? void 0 : _a.findCredential(n.restUrl);
  if (!u) return null;
  if ("loaded" === n.loadStatus && "" === t2 && n.user && n.user.sourceJSON && false === s2) return n.user.sourceJSON;
  if ("" === t2) {
    const r2 = await U(n.restUrl + "/community/self", { responseType: "json", query: { f: "json", ...false === s2 ? {} : { returnUserLicenseTypeExtensions: true } } });
    if (r2.data) {
      const e = r2.data;
      if (e && e.username) return e;
    }
    return null;
  }
  const o = await U(n.restUrl + "/community/users/" + t2, { responseType: "json", query: { f: "json" } });
  if (o.data) {
    const r2 = o.data;
    return r2.error ? null : r2;
  }
  return null;
}

export {
  t,
  s
};
//# sourceMappingURL=chunk-YKSBY2H7.js.map
