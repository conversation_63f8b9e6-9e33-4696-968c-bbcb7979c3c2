/**
 * 格式化工具函数
 */

/**
 * 格式化日期时间
 * @param date 日期字符串或时间戳
 * @param format 格式化模板
 * @returns 格式化后的日期字符串
 */
export function formatDateTime(date: string | number | Date | undefined, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期
 * @param date 日期字符串或时间戳
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: string | number | Date | undefined): string {
  return formatDateTime(date, 'YYYY-MM-DD')
}

/**
 * 格式化时间
 * @param date 日期字符串或时间戳
 * @returns 格式化后的时间字符串
 */
export function formatTime(date: string | number | Date | undefined): string {
  return formatDateTime(date, 'HH:mm:ss')
}

/**
 * 格式化数字
 * @param value 数值
 * @param precision 精度
 * @param defaultValue 默认值
 * @returns 格式化后的数字字符串
 */
export function formatNumber(value: number | string | undefined, precision = 2, defaultValue = '--'): string {
  if (value === undefined || value === null || value === '') return defaultValue
  
  const num = Number(value)
  if (isNaN(num)) return defaultValue
  
  return num.toFixed(precision)
}

/**
 * 格式化百分比
 * @param value 数值
 * @param precision 精度
 * @param defaultValue 默认值
 * @returns 格式化后的百分比字符串
 */
export function formatPercent(value: number | string | undefined, precision = 1, defaultValue = '--'): string {
  if (value === undefined || value === null || value === '') return defaultValue
  
  const num = Number(value)
  if (isNaN(num)) return defaultValue
  
  return `${num.toFixed(precision)}%`
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number | undefined): string {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
}

/**
 * 格式化货币
 * @param value 数值
 * @param currency 货币符号
 * @param precision 精度
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(value: number | string | undefined, currency = '¥', precision = 2): string {
  if (value === undefined || value === null || value === '') return '--'
  
  const num = Number(value)
  if (isNaN(num)) return '--'
  
  return `${currency}${num.toLocaleString('zh-CN', { minimumFractionDigits: precision, maximumFractionDigits: precision })}`
}

/**
 * 格式化千分位数字
 * @param value 数值
 * @param precision 精度
 * @returns 格式化后的千分位数字字符串
 */
export function formatThousands(value: number | string | undefined, precision = 2): string {
  if (value === undefined || value === null || value === '') return '--'
  
  const num = Number(value)
  if (isNaN(num)) return '--'
  
  return num.toLocaleString('zh-CN', { minimumFractionDigits: precision, maximumFractionDigits: precision })
}

/**
 * 格式化相对时间
 * @param date 日期字符串或时间戳
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: string | number | Date | undefined): string {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`
  } else {
    return `${Math.floor(diff / year)}年前`
  }
}

/**
 * 格式化持续时间
 * @param milliseconds 毫秒数
 * @returns 持续时间字符串
 */
export function formatDuration(milliseconds: number | undefined): string {
  if (!milliseconds || milliseconds <= 0) return '0秒'
  
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天${hours % 24}小时${minutes % 60}分钟`
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

/**
 * 格式化手机号
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
export function formatPhone(phone: string | undefined): string {
  if (!phone) return ''
  
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
  }
  
  return phone
}

/**
 * 格式化身份证号
 * @param idCard 身份证号
 * @returns 格式化后的身份证号
 */
export function formatIdCard(idCard: string | undefined): string {
  if (!idCard) return ''
  
  if (idCard.length === 18) {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  } else if (idCard.length === 15) {
    return idCard.replace(/(\d{6})\d{6}(\d{3})/, '$1******$2')
  }
  
  return idCard
}

/**
 * 格式化银行卡号
 * @param cardNumber 银行卡号
 * @returns 格式化后的银行卡号
 */
export function formatBankCard(cardNumber: string | undefined): string {
  if (!cardNumber) return ''
  
  const cleaned = cardNumber.replace(/\D/g, '')
  if (cleaned.length >= 16) {
    return cleaned.replace(/(\d{4})\d*(\d{4})/, '$1****$2')
  }
  
  return cardNumber
}

/**
 * 格式化地址
 * @param address 地址
 * @param maxLength 最大长度
 * @returns 格式化后的地址
 */
export function formatAddress(address: string | undefined, maxLength = 50): string {
  if (!address) return ''
  
  if (address.length > maxLength) {
    return `${address.substring(0, maxLength)}...`
  }
  
  return address
}
