{"version": 3, "sources": ["../../@arcgis/core/rest/support/IdentifyParameters.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{geometryTypes as e}from\"../../geometry.js\";import o from\"../../TimeExtent.js\";import{JSONSupport as r}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import{ensureClass as i}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as p}from\"../../core/accessorSupport/decorators/subclass.js\";import{fromJSON as n}from\"../../geometry/support/jsonUtils.js\";import l from\"../../geometry/Extent.js\";import y from\"../../geometry/SpatialReference.js\";var m;let a=m=class extends r{static from(t){return i(m,t)}constructor(t){super(t),this.dpi=96,this.floors=null,this.gdbVersion=null,this.geometry=null,this.geometryPrecision=null,this.height=400,this.layerIds=null,this.layerOption=\"top\",this.mapExtent=null,this.maxAllowableOffset=null,this.returnFieldName=!0,this.returnGeometry=!1,this.returnM=!1,this.returnUnformattedValues=!0,this.returnZ=!1,this.spatialReference=null,this.sublayers=null,this.timeExtent=null,this.tolerance=null,this.width=400}};t([s({type:Number,json:{write:!0}})],a.prototype,\"dpi\",void 0),t([s()],a.prototype,\"floors\",void 0),t([s({type:String,json:{write:!0}})],a.prototype,\"gdbVersion\",void 0),t([s({types:e,json:{read:n,write:!0}})],a.prototype,\"geometry\",void 0),t([s({type:Number,json:{write:!0}})],a.prototype,\"geometryPrecision\",void 0),t([s({type:Number,json:{write:!0}})],a.prototype,\"height\",void 0),t([s({type:[Number],json:{write:!0}})],a.prototype,\"layerIds\",void 0),t([s({type:[\"top\",\"visible\",\"all\",\"popup\"],json:{write:!0}})],a.prototype,\"layerOption\",void 0),t([s({type:l,json:{write:!0}})],a.prototype,\"mapExtent\",void 0),t([s({type:Number,json:{write:!0}})],a.prototype,\"maxAllowableOffset\",void 0),t([s({type:Boolean,json:{write:!0}})],a.prototype,\"returnFieldName\",void 0),t([s({type:Boolean,json:{write:!0}})],a.prototype,\"returnGeometry\",void 0),t([s({type:Boolean,json:{write:!0}})],a.prototype,\"returnM\",void 0),t([s({type:Boolean,json:{write:!0}})],a.prototype,\"returnUnformattedValues\",void 0),t([s({type:Boolean,json:{write:!0}})],a.prototype,\"returnZ\",void 0),t([s({type:y,json:{write:!0}})],a.prototype,\"spatialReference\",void 0),t([s()],a.prototype,\"sublayers\",void 0),t([s({type:o,json:{write:!0}})],a.prototype,\"timeExtent\",void 0),t([s({type:Number,json:{write:!0}})],a.prototype,\"tolerance\",void 0),t([s({type:Number,json:{write:!0}})],a.prototype,\"width\",void 0),a=m=t([p(\"esri.rest.support.IdentifyParameters\")],a);const u=a;export{u as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIslB,IAAI;AAAE,IAAIA,KAAE,IAAE,cAAc,EAAC;AAAA,EAAC,OAAO,KAAK,GAAE;AAAC,WAAO,EAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,MAAI,IAAG,KAAK,SAAO,MAAK,KAAK,aAAW,MAAK,KAAK,WAAS,MAAK,KAAK,oBAAkB,MAAK,KAAK,SAAO,KAAI,KAAK,WAAS,MAAK,KAAK,cAAY,OAAM,KAAK,YAAU,MAAK,KAAK,qBAAmB,MAAK,KAAK,kBAAgB,MAAG,KAAK,iBAAe,OAAG,KAAK,UAAQ,OAAG,KAAK,0BAAwB,MAAG,KAAK,UAAQ,OAAG,KAAK,mBAAiB,MAAK,KAAK,YAAU,MAAK,KAAK,aAAW,MAAK,KAAK,YAAU,MAAK,KAAK,QAAM;AAAA,EAAG;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,GAAE,MAAK,EAAC,MAAKC,IAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,OAAM,WAAU,OAAM,OAAO,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAEA,KAAE,IAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["a", "v"]}