import {
  p
} from "./chunk-5G7LCOCX.js";
import {
  f
} from "./chunk-XBS7QZIQ.js";
import {
  v
} from "./chunk-6GKVSPTV.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";

// node_modules/@arcgis/core/rest/query/executeForTopExtents.js
async function m(m2, s, n) {
  const p2 = f(m2), a = await p(p2, v.from(s), { ...n });
  return { count: a.data.count, extent: w.fromJSON(a.data.extent) };
}

export {
  m
};
//# sourceMappingURL=chunk-YZG2GSYP.js.map
