import {
  I,
  a2,
  o as o2,
  o2 as o3,
  t as t2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T,
  g as g2
} from "./chunk-C5VMWMBD.js";
import {
  e as e2
} from "./chunk-TUM6KUQZ.js";
import {
  b,
  e,
  f,
  g,
  i,
  i2,
  k,
  o3 as o
} from "./chunk-2CM7MIII.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  h,
  p
} from "./chunk-REW33H3I.js";
import {
  F
} from "./chunk-GZGAQUSK.js";
import {
  N,
  a,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/chunks/tslib.es6.js
function e3(e4, t7, r4, o5) {
  var c3, f3 = arguments.length, n3 = f3 < 3 ? t7 : null === o5 ? o5 = Object.getOwnPropertyDescriptor(t7, r4) : o5;
  if ("object" == typeof Reflect && "function" == typeof Reflect.decorate) n3 = Reflect.decorate(e4, t7, r4, o5);
  else for (var l2 = e4.length - 1; l2 >= 0; l2--) (c3 = e4[l2]) && (n3 = (f3 < 3 ? c3(n3) : f3 > 3 ? c3(t7, r4, n3) : c3(t7, r4)) || n3);
  return f3 > 3 && n3 && Object.defineProperty(t7, r4, n3), n3;
}

// node_modules/@arcgis/core/core/iteratorUtils.js
function n(n3) {
  const o5 = [];
  return function* () {
    yield* o5;
    for (const t7 of n3) o5.push(t7), yield t7;
  };
}
function o4(n3, o5) {
  for (const t7 of n3) if (null != t7 && o5(t7)) return t7;
}
function t3(n3) {
  return null != n3 && "function" == typeof n3[Symbol.iterator];
}

// node_modules/@arcgis/core/core/Handles.js
var t4 = class {
  constructor() {
    this._groups = /* @__PURE__ */ new Map();
  }
  destroy() {
    this.removeAll();
  }
  get size() {
    let r4 = 0;
    return this._groups.forEach((e4) => {
      r4 += e4.length;
    }), r4;
  }
  add(e4, t7) {
    if (t3(e4)) {
      const r4 = this._getOrCreateGroup(t7);
      for (const t8 of e4) this._isHandle(t8) && r4.push(t8);
    } else if (this._isHandle(e4)) {
      this._getOrCreateGroup(t7).push(e4);
    }
    return this;
  }
  forEach(r4, e4) {
    if ("function" == typeof r4) this._groups.forEach((e5) => e5.forEach(r4));
    else {
      const t7 = this._getGroup(r4);
      t7 && e4 && t7.forEach(e4);
    }
  }
  has(r4) {
    return this._groups.has(this._ensureGroupKey(r4));
  }
  remove(e4) {
    if ("string" != typeof e4 && t3(e4)) {
      for (const r4 of e4) this.remove(r4);
      return this;
    }
    return this.has(e4) ? (this._removeAllFromGroup(this._getGroup(e4)), this._groups.delete(this._ensureGroupKey(e4)), this) : this;
  }
  removeAll() {
    return this._groups.forEach((r4) => this._removeAllFromGroup(r4)), this._groups.clear(), this;
  }
  _isHandle(r4) {
    return r4 && !!r4.remove;
  }
  _getOrCreateGroup(r4) {
    if (this.has(r4)) return this._getGroup(r4);
    const e4 = [];
    return this._groups.set(this._ensureGroupKey(r4), e4), e4;
  }
  _getGroup(r4) {
    return N(this._groups.get(this._ensureGroupKey(r4)));
  }
  _ensureGroupKey(r4) {
    return r4 || "_default_";
  }
  _removeAllFromGroup(r4) {
    r4.forEach((r5) => r5.remove());
  }
};

// node_modules/@arcgis/core/core/accessorSupport/PropertyOrigin.js
var r2;
!function(e4) {
  e4[e4.DEFAULTS = 0] = "DEFAULTS", e4[e4.COMPUTED = 1] = "COMPUTED", e4[e4.SERVICE = 2] = "SERVICE", e4[e4.PORTAL_ITEM = 3] = "PORTAL_ITEM", e4[e4.WEB_SCENE = 4] = "WEB_SCENE", e4[e4.WEB_MAP = 5] = "WEB_MAP", e4[e4.USER = 6] = "USER";
}(r2 || (r2 = {}));
var E = r2.USER + 1;
function t5(e4) {
  switch (e4) {
    case "defaults":
      return r2.DEFAULTS;
    case "service":
      return r2.SERVICE;
    case "portal-item":
      return r2.PORTAL_ITEM;
    case "web-scene":
      return r2.WEB_SCENE;
    case "web-map":
      return r2.WEB_MAP;
    case "user":
      return r2.USER;
    default:
      return null;
  }
}
function n2(E2) {
  switch (E2) {
    case r2.DEFAULTS:
      return "defaults";
    case r2.SERVICE:
      return "service";
    case r2.PORTAL_ITEM:
      return "portal-item";
    case r2.WEB_SCENE:
      return "web-scene";
    case r2.WEB_MAP:
      return "web-map";
    case r2.USER:
      return "user";
  }
  return N(void 0);
}
function c(e4) {
  return n2(e4);
}

// node_modules/@arcgis/core/core/accessorSupport/tracking/ObservationHandle.js
var s2 = class {
  constructor(r4, s4) {
    this._observers = r4, this._observer = s4;
  }
  remove() {
    F(this._observers, this._observer);
  }
};

// node_modules/@arcgis/core/core/accessorSupport/ObservableBase.js
var s3 = class {
  constructor() {
    this._observers = null, this.destroyed = false;
  }
  observe(s4) {
    if (this.destroyed || s4.destroyed) return r3;
    null == this._observers && (this._observers = []);
    const t7 = this._observers;
    let o5 = false, n3 = false;
    const i3 = t7.length;
    for (let e4 = 0; e4 < i3; ++e4) {
      const r4 = t7[e4];
      if (r4.destroyed) n3 = true;
      else if (r4 === s4) {
        o5 = true;
        break;
      }
    }
    return o5 || (t7.push(s4), n3 && this._removeDestroyedObservers()), new s2(t7, s4);
  }
  _removeDestroyedObservers() {
    const e4 = this._observers;
    if (!e4 || 0 === e4.length) return;
    const s4 = e4.length;
    let r4 = 0;
    for (let t7 = 0; t7 < s4; ++t7) {
      for (; t7 + r4 < s4; ) {
        if (!e4[t7 + r4].destroyed) break;
        ++r4;
      }
      if (r4 > 0) {
        if (!(t7 + r4 < s4)) break;
        e4[t7] = e4[t7 + r4];
      }
    }
    e4.length = s4 - r4;
  }
  destroy() {
    if (this.destroyed) return;
    this.destroyed = true;
    const e4 = this._observers;
    if (null != e4) {
      for (const s4 of e4) s4.onCommitted();
      this._observers = null;
    }
  }
};
var r3 = { remove: () => {
} };

// node_modules/@arcgis/core/core/accessorSupport/Property.js
var a3 = class {
  constructor(t7, e4, s4) {
    this.properties = t7, this.propertyName = e4, this.metadata = s4, this.observerObject = new l(), this._accessed = null, this._handles = null, this.observerObject.flags = i.Dirty | (s4.nonNullable ? i.NonNullable : 0) | (s4.hasOwnProperty("value") ? i.HasDefaultValue : 0) | (void 0 === s4.get ? i.DepTrackingInitialized : 0) | (void 0 === s4.dependsOn ? i.AutoTracked : 0), c2.register(this, this.observerObject);
  }
  destroy() {
    this.observerObject.destroy(), this._accessed = null, this._clearObservationHandles(), c2.unregister(this);
  }
  getComputed() {
    const t7 = this.observerObject;
    i2(t7);
    const a4 = this.properties.store, l2 = this.propertyName, c3 = t7.flags, h2 = a4.get(l2);
    if (c3 & i.Computing) return h2;
    if (~c3 & i.Dirty && a4.has(l2)) return h2;
    t7.flags |= i.Computing;
    const d3 = this.properties.host;
    let g3;
    c3 & i.AutoTracked ? g3 = f(this, this.metadata.get, d3) : (k(d3, this), g3 = this.metadata.get.call(d3)), a4.set(l2, g3, r2.COMPUTED);
    const b2 = a4.get(l2);
    return b2 === h2 ? t7.flags &= ~i.Dirty : g(this.commit, this), t7.flags &= ~i.Computing, b2;
  }
  onObservableAccessed(t7) {
    t7 !== this.observerObject && (null === this._accessed && (this._accessed = []), this._accessed.includes(t7) || this._accessed.push(t7));
  }
  onTrackingEnd() {
    this._clearObservationHandles();
    const t7 = this.observerObject;
    t7.flags |= i.DepTrackingInitialized;
    const e4 = this._accessed;
    if (null === e4) return;
    let s4 = this._handles;
    null === s4 && (s4 = this._handles = []);
    for (let i3 = 0; i3 < e4.length; ++i3) s4.push(e4[i3].observe(t7));
    e4.length = 0;
  }
  notifyChange() {
    const t7 = this.observerObject;
    t7.onInvalidated(), t7.onCommitted();
  }
  invalidate() {
    this.observerObject.onInvalidated();
  }
  commit() {
    const t7 = this.observerObject;
    t7.flags &= ~i.Dirty, t7.onCommitted();
  }
  _clearObservationHandles() {
    const t7 = this._handles;
    if (null !== t7) {
      for (let e4 = 0; e4 < t7.length; ++e4) t7[e4].remove();
      t7.length = 0;
    }
  }
};
var l = class extends s3 {
  constructor() {
    super(...arguments), this.flags = 0;
  }
  onInvalidated() {
    ~this.flags & i.Overriden && (this.flags |= i.Dirty);
    const t7 = this._observers;
    if (t7 && t7.length > 0) for (const e4 of t7) e4.onInvalidated();
  }
  onCommitted() {
    const t7 = this._observers;
    if (t7 && t7.length > 0) {
      const e4 = t7.slice();
      for (const t8 of e4) t8.onCommitted();
    }
  }
  destroy() {
    this.flags & i.Dirty && this.onCommitted(), super.destroy();
  }
};
var c2 = new FinalizationRegistry((t7) => {
  t7.destroy();
});

// node_modules/@arcgis/core/core/accessorSupport/Store.js
var t6 = class _t {
  constructor() {
    this._values = /* @__PURE__ */ new Map(), this.multipleOriginsSupported = false;
  }
  clone(s4) {
    const r4 = new _t();
    return this._values.forEach((t7, i3) => {
      s4 && s4.has(i3) || r4.set(i3, p(t7));
    }), r4;
  }
  get(e4) {
    return this._values.get(e4);
  }
  originOf() {
    return r2.USER;
  }
  keys() {
    return [...this._values.keys()];
  }
  set(e4, s4) {
    this._values.set(e4, s4);
  }
  delete(e4) {
    this._values.delete(e4);
  }
  has(e4) {
    return this._values.has(e4);
  }
  forEach(e4) {
    this._values.forEach(e4);
  }
};

// node_modules/@arcgis/core/core/accessorSupport/Properties.js
function d(t7, e4, s4) {
  return void 0 !== t7;
}
function m(t7, e4, s4, r4) {
  return void 0 !== t7 && (!(null == s4 && t7.observerObject.flags & i.NonNullable) || (r4.lifecycle, I.INITIALIZING, false));
}
function v(t7) {
  return t7 && "function" == typeof t7.destroy;
}
s.getLogger("esri.core.accessorSupport.Properties");
var O = class {
  constructor(t7) {
    this.host = t7, this.properties = /* @__PURE__ */ new Map(), this.ctorArgs = null, this.destroyed = false, this.lifecycle = I.INITIALIZING, this.store = new t6(), this._origin = r2.USER;
    const e4 = N(this.host.constructor.__accessorMetadata__);
    for (const s4 in e4) {
      const t8 = new a3(this, s4, e4[s4]);
      this.properties.set(s4, t8);
    }
    this.metadatas = e4;
  }
  initialize() {
    this.lifecycle = I.CONSTRUCTING;
  }
  constructed() {
    this.lifecycle = I.CONSTRUCTED;
  }
  destroy() {
    this.destroyed = true;
    for (const [t7, e4] of this.properties) {
      if (e4.metadata.autoDestroy) {
        const s4 = this.internalGet(t7);
        s4 && v(s4) && (s4.destroy(), ~e4.observerObject.flags & i.NonNullable && this._internalSet(e4, null));
      }
      e4.destroy();
    }
  }
  get initialized() {
    return this.lifecycle !== I.INITIALIZING;
  }
  get(t7) {
    const e4 = this.properties.get(t7);
    if (e4.metadata.get) return e4.getComputed();
    i2(e4.observerObject);
    const s4 = this.store;
    return s4.has(t7) ? s4.get(t7) : e4.metadata.value;
  }
  originOf(t7) {
    const e4 = this.store.originOf(t7);
    if (void 0 === e4) {
      const e5 = this.properties.get(t7);
      if (void 0 !== e5 && e5.observerObject.flags & i.HasDefaultValue) return "defaults";
    }
    return n2(e4);
  }
  has(t7) {
    return !!this.properties.has(t7) && this.store.has(t7);
  }
  keys() {
    return [...this.properties.keys()];
  }
  internalGet(t7) {
    const e4 = this.properties.get(t7);
    if (d(e4)) return this.store.has(t7) ? this.store.get(t7) : e4.metadata.value;
  }
  internalSet(t7, e4) {
    const s4 = this.properties.get(t7);
    d(s4) && this._internalSet(s4, e4);
  }
  getDependsInfo(t7, e4, s4) {
    const r4 = this.properties.get(e4);
    if (!d(r4)) return "";
    const i3 = /* @__PURE__ */ new Set(), n3 = f({ onObservableAccessed: (t8) => i3.add(t8), onTrackingEnd: () => {
    } }, () => {
      var _a;
      return (_a = r4.metadata.get) == null ? void 0 : _a.call(t7);
    });
    let a4 = `${s4}${t7.declaredClass.split(".").pop()}.${e4}: ${n3}
`;
    if (0 === i3.size) return a4;
    s4 += "  ";
    for (const c3 of i3) {
      if (!(c3 instanceof a3)) continue;
      const t8 = c3.properties.host, e5 = c3.propertyName, r5 = e(t8);
      a4 += r5 ? r5.getDependsInfo(t8, e5, s4) : `${s4}${e5}: undefined
`;
    }
    return a4;
  }
  setAtOrigin(t7, e4, s4) {
    const r4 = this.properties.get(t7);
    if (d(r4)) return this._setAtOrigin(r4, e4, s4);
  }
  isOverridden(t7) {
    const e4 = this.properties.get(t7);
    return void 0 !== e4 && !!(e4.observerObject.flags & i.Overriden);
  }
  clearOverride(t7) {
    const e4 = this.properties.get(t7), s4 = e4 == null ? void 0 : e4.observerObject;
    s4 && s4.flags & i.Overriden && (s4.flags &= ~i.Overriden, e4.notifyChange());
  }
  override(t7, e4) {
    const s4 = this.properties.get(t7);
    if (!m(s4, t7, e4, this)) return;
    const r4 = s4.metadata.cast;
    if (r4) {
      const t8 = this._cast(r4, e4), { valid: s5, value: i3 } = t8;
      if (I2.release(t8), !s5) return;
      e4 = i3;
    }
    s4.observerObject.flags |= i.Overriden, this._internalSet(s4, e4);
  }
  set(t7, e4) {
    const s4 = this.properties.get(t7);
    if (!m(s4, t7, e4, this)) return;
    const r4 = s4.metadata.cast;
    if (r4) {
      const t8 = this._cast(r4, e4), { valid: s5, value: i4 } = t8;
      if (I2.release(t8), !s5) return;
      e4 = i4;
    }
    const i3 = s4.metadata.set;
    i3 ? i3.call(this.host, e4) : this._internalSet(s4, e4);
  }
  setDefaultOrigin(t7) {
    this._origin = t5(t7);
  }
  getDefaultOrigin() {
    return n2(this._origin);
  }
  notifyChange(t7) {
    const e4 = this.properties.get(t7);
    void 0 !== e4 && e4.notifyChange();
  }
  invalidate(t7) {
    const e4 = this.properties.get(t7);
    void 0 !== e4 && e4.invalidate();
  }
  commit(t7) {
    const e4 = this.properties.get(t7);
    void 0 !== e4 && e4.commit();
  }
  _internalSet(t7, e4) {
    const s4 = this.lifecycle !== I.INITIALIZING ? this._origin : r2.DEFAULTS;
    this._setAtOrigin(t7, e4, s4);
  }
  _setAtOrigin(e4, s4, r4) {
    const i3 = this.store, o5 = e4.propertyName;
    i3.has(o5, r4) && h(s4, i3.get(o5)) && ~e4.observerObject.flags & i.Overriden && r4 === i3.originOf(o5) || (e4.invalidate(), i3.set(o5, s4, r4), e4.commit(), b(this.host, e4));
  }
  _cast(t7, e4) {
    const s4 = I2.acquire();
    return s4.valid = true, s4.value = e4, t7 && (s4.value = t7.call(this.host, e4, s4)), s4;
  }
};
var y2 = class {
  constructor() {
    this.value = null, this.valid = true;
  }
  acquire() {
    this.valid = true;
  }
  release() {
    this.value = null;
  }
};
var I2 = new e2(y2);

// node_modules/@arcgis/core/core/Accessor.js
var p2;
var d2;
function y3(t7) {
  var _a;
  if (null == t7) return { value: t7 };
  if (Array.isArray(t7)) return { type: [t7[0]], value: null };
  switch (typeof t7) {
    case "object":
      return ((_a = t7.constructor) == null ? void 0 : _a.__accessorMetadata__) || t7 instanceof Date ? { type: t7.constructor, value: t7 } : t7;
    case "boolean":
      return { type: Boolean, value: t7 };
    case "string":
      return { type: String, value: t7 };
    case "number":
      return { type: Number, value: t7 };
    case "function":
      return { type: t7, value: null };
    default:
      return;
  }
}
var f2 = Symbol("Accessor-Handles");
var m2 = Symbol("Accessor-Initialized");
var v2 = class _v {
  static createSubclass(t7 = {}) {
    if (Array.isArray(t7)) throw new Error("Multi-inheritance unsupported since 4.16");
    const { properties: r4, declaredClass: e4, constructor: s4 } = t7;
    delete t7.declaredClass, delete t7.properties, delete t7.constructor;
    const o5 = this;
    class i3 extends o5 {
      constructor(...t8) {
        super(...t8), this.inherited = null, s4 && s4.apply(this, t8);
      }
    }
    o2(i3.prototype);
    for (const c3 in t7) {
      const r5 = t7[c3];
      i3.prototype[c3] = "function" == typeof r5 ? function(...t8) {
        const e5 = this.inherited;
        let s5;
        this.inherited = function(...t9) {
          if (o5.prototype[c3]) return o5.prototype[c3].apply(this, t9);
        };
        try {
          s5 = r5.apply(this, t8);
        } catch (i4) {
          throw this.inherited = e5, i4;
        }
        return this.inherited = e5, s5;
      } : t7[c3];
    }
    for (const c3 in r4) {
      const t8 = y3(r4[c3]);
      y(t8)(i3.prototype, c3);
    }
    return a2(e4)(i3);
  }
  constructor(...t7) {
    if (this[p2] = null, this[d2] = false, this.constructor === _v) throw new Error("[accessor] cannot instantiate Accessor. This can be fixed by creating a subclass of Accessor");
    Object.defineProperty(this, "__accessor__", { enumerable: false, value: new O(this) }), t7.length > 0 && this.normalizeCtorArgs && (this.__accessor__.ctorArgs = this.normalizeCtorArgs.apply(this, t7));
  }
  postscript(t7) {
    const r4 = this.__accessor__, e4 = r4.ctorArgs || t7;
    r4.initialize(), e4 && (this.set(e4), r4.ctorArgs = null), r4.constructed(), this.initialize(), this[m2] = true;
  }
  initialize() {
  }
  [o3]() {
    this[f2] = a(this[f2]);
  }
  destroy() {
    this.destroyed || (g2(this), this.__accessor__.destroy());
  }
  get constructed() {
    return this.__accessor__ && this.__accessor__.initialized || false;
  }
  get initialized() {
    return this[m2];
  }
  get destroyed() {
    return this.__accessor__ && this.__accessor__.destroyed || false;
  }
  commitProperty(t7) {
    this.get(t7);
  }
  get(t7) {
    return o(this, t7);
  }
  hasOwnProperty(t7) {
    return this.__accessor__ ? this.__accessor__.has(t7) : Object.prototype.hasOwnProperty.call(this, t7);
  }
  keys() {
    return this.__accessor__ ? this.__accessor__.keys() : [];
  }
  set(t7, r4) {
    return t2(this, t7, r4), this;
  }
  watch(t7, r4, e4) {
    return T(this, t7, r4, e4);
  }
  own(t7) {
    this.addHandles(t7);
  }
  addHandles(r4, s4) {
    let o5 = this[f2];
    t(o5) && (o5 = this[f2] = new t4()), o5.add(r4, s4);
  }
  removeHandles(t7) {
    const r4 = this[f2];
    t(r4) || r4.remove(t7);
  }
  hasHandles(t7) {
    const r4 = this[f2];
    return !!r(r4) && r4.has(t7);
  }
  _override(t7, r4) {
    void 0 === r4 ? this.__accessor__.clearOverride(t7) : this.__accessor__.override(t7, r4);
  }
  _clearOverride(t7) {
    return this.__accessor__.clearOverride(t7);
  }
  _overrideIfSome(t7, r4) {
    null == r4 ? this.__accessor__.clearOverride(t7) : this.__accessor__.override(t7, r4);
  }
  _isOverridden(t7) {
    return this.__accessor__.isOverridden(t7);
  }
  notifyChange(t7) {
    this.__accessor__.notifyChange(t7);
  }
  _get(t7) {
    return this.__accessor__.internalGet(t7);
  }
  _set(t7, r4) {
    return this.__accessor__.internalSet(t7, r4), this;
  }
};
p2 = f2, d2 = m2;

export {
  e3 as e,
  n,
  o4 as o,
  t3 as t,
  t4 as t2,
  s3 as s,
  r2 as r,
  E,
  t5 as t3,
  n2,
  c,
  v2 as v
};
//# sourceMappingURL=chunk-NDCSRZLO.js.map
