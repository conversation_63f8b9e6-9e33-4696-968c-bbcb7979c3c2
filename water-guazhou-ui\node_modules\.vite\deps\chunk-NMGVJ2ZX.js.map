{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/candidates/DrapedEdgeSnappingCandidate.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{DrapedLineConstraint as t}from\"../SnappingConstraint.js\";import{LineSegmentHintType as n}from\"../snappingUtils.js\";import{FeatureSnappingCandidate as i}from\"./FeatureSnappingCandidate.js\";import{LineSnappingHint as r}from\"../hints/LineSnappingHint.js\";class s extends i{constructor(n){super({...n,isDraped:!0,constraint:new t(n.edgeStart,n.edgeEnd,n.getGroundElevation)})}get hints(){return[new r(n.REFERENCE,this.constraint.start,this.constraint.end,this.isDraped,this.domain)]}}export{s as DrapedEdgeSnappingCandidate};\n"], "mappings": ";;;;;;;;;;;;AAImQ,IAAM,IAAN,cAAgBA,GAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAM,EAAC,GAAGA,IAAE,UAAS,MAAG,YAAW,IAAI,EAAEA,GAAE,WAAUA,GAAE,SAAQA,GAAE,kBAAkB,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAM,CAAC,IAAI,EAAE,EAAE,WAAU,KAAK,WAAW,OAAM,KAAK,WAAW,KAAI,KAAK,UAAS,KAAK,MAAM,CAAC;AAAA,EAAC;AAAC;", "names": ["n"]}