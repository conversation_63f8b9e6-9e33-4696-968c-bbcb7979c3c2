import {
  o as o2
} from "./chunk-J6VS6FXY.js";
import {
  i as i2,
  r as r2
} from "./chunk-CVN5SSWT.js";
import {
  C
} from "./chunk-SZNZM2TR.js";
import {
  t as t3
} from "./chunk-5SYMUP5B.js";
import {
  T,
  l as l2,
  m as m2,
  t as t5
} from "./chunk-EGZXDK64.js";
import {
  E,
  I
} from "./chunk-2B52LX6T.js";
import {
  a,
  l,
  m,
  n as n3,
  o,
  p as p2,
  u
} from "./chunk-53FPJYCC.js";
import {
  N,
  _,
  e,
  e2,
  f,
  h,
  h2,
  i as i3,
  n as n2,
  r as r4,
  t as t2,
  t2 as t4
} from "./chunk-N73MYEJE.js";
import "./chunk-IEBU4QQL.js";
import {
  c as c2
} from "./chunk-JCXMTMKU.js";
import {
  r as r3
} from "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-VCFT2OOI.js";
import "./chunk-O2BYTJI4.js";
import "./chunk-RURSJOSG.js";
import {
  n
} from "./chunk-TNP2LXZZ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-HP475EI3.js";
import {
  t
} from "./chunk-TUM6KUQZ.js";
import {
  j,
  p
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import {
  c
} from "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  i,
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/ScriptUtils.js
function e3(e5) {
  return 746 === e5 || 747 === e5 || !(e5 < 4352) && (e5 >= 12704 && e5 <= 12735 || (e5 >= 12544 && e5 <= 12591 || (e5 >= 65072 && e5 <= 65103 && !(e5 >= 65097 && e5 <= 65103) || (e5 >= 63744 && e5 <= 64255 || (e5 >= 13056 && e5 <= 13311 || (e5 >= 11904 && e5 <= 12031 || (e5 >= 12736 && e5 <= 12783 || (e5 >= 12288 && e5 <= 12351 && !(e5 >= 12296 && e5 <= 12305 || e5 >= 12308 && e5 <= 12319 || 12336 === e5) || (e5 >= 13312 && e5 <= 19903 || (e5 >= 19968 && e5 <= 40959 || (e5 >= 12800 && e5 <= 13055 || (e5 >= 12592 && e5 <= 12687 || (e5 >= 43360 && e5 <= 43391 || (e5 >= 55216 && e5 <= 55295 || (e5 >= 4352 && e5 <= 4607 || (e5 >= 44032 && e5 <= 55215 || (e5 >= 12352 && e5 <= 12447 || (e5 >= 12272 && e5 <= 12287 || (e5 >= 12688 && e5 <= 12703 || (e5 >= 12032 && e5 <= 12255 || (e5 >= 12784 && e5 <= 12799 || (e5 >= 12448 && e5 <= 12543 && 12540 !== e5 || (e5 >= 65280 && e5 <= 65519 && !(65288 === e5 || 65289 === e5 || 65293 === e5 || e5 >= 65306 && e5 <= 65310 || 65339 === e5 || 65341 === e5 || 65343 === e5 || e5 >= 65371 && e5 <= 65503 || 65507 === e5 || e5 >= 65512 && e5 <= 65519) || (e5 >= 65104 && e5 <= 65135 && !(e5 >= 65112 && e5 <= 65118 || e5 >= 65123 && e5 <= 65126) || (e5 >= 5120 && e5 <= 5759 || (e5 >= 6320 && e5 <= 6399 || (e5 >= 65040 && e5 <= 65055 || (e5 >= 19904 && e5 <= 19967 || (e5 >= 40960 && e5 <= 42127 || e5 >= 42128 && e5 <= 42191)))))))))))))))))))))))))))));
}
function c3(e5) {
  return !(e5 < 11904) && (e5 >= 12704 && e5 <= 12735 || (e5 >= 12544 && e5 <= 12591 || (e5 >= 65072 && e5 <= 65103 || (e5 >= 63744 && e5 <= 64255 || (e5 >= 13056 && e5 <= 13311 || (e5 >= 11904 && e5 <= 12031 || (e5 >= 12736 && e5 <= 12783 || (e5 >= 12288 && e5 <= 12351 || (e5 >= 13312 && e5 <= 19903 || (e5 >= 19968 && e5 <= 40959 || (e5 >= 12800 && e5 <= 13055 || (e5 >= 65280 && e5 <= 65519 || (e5 >= 12352 && e5 <= 12447 || (e5 >= 12272 && e5 <= 12287 || (e5 >= 12032 && e5 <= 12255 || (e5 >= 12784 && e5 <= 12799 || (e5 >= 12448 && e5 <= 12543 || (e5 >= 65040 && e5 <= 65055 || (e5 >= 42128 && e5 <= 42191 || e5 >= 40960 && e5 <= 42127)))))))))))))))))));
}
function s(e5) {
  switch (e5) {
    case 10:
    case 32:
    case 38:
    case 40:
    case 41:
    case 43:
    case 45:
    case 47:
    case 173:
    case 183:
    case 8203:
    case 8208:
    case 8211:
    case 8231:
      return true;
  }
  return false;
}
function a2(e5) {
  switch (e5) {
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 32:
      return true;
  }
  return false;
}

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/TextShaping.js
var c4 = 24;
var h3 = 17;
var a3 = class {
  constructor(t8, e5, i4, s5, o5, c5, h5) {
    this._glyphItems = t8, this._maxWidth = e5, this._lineHeight = i4, this._letterSpacing = s5, this._hAnchor = o5, this._vAnchor = c5, this._justify = h5;
  }
  getShaping(o5, c5, h5) {
    const a6 = this._letterSpacing, l4 = this._lineHeight, r9 = this._justify, n5 = this._maxWidth, m3 = [];
    let f3 = 0, p4 = 0;
    const g2 = o5.length;
    for (let t8 = 0; t8 < g2; t8++) {
      const e5 = o5.charCodeAt(t8), i4 = h5 && e3(e5);
      let c6;
      for (const t9 of this._glyphItems) if (c6 = t9[e5], c6) break;
      m3.push({ codePoint: e5, x: f3, y: p4, vertical: i4, glyphMosaicItem: c6 }), c6 && (f3 += c6.metrics.advance + a6);
    }
    let y2 = f3;
    if (n5 > 0) {
      y2 = f3 / Math.max(1, Math.ceil(f3 / n5));
    }
    const d = o5.includes("​"), x2 = [];
    for (let e5 = 0; e5 < g2 - 1; e5++) {
      const s5 = m3[e5].codePoint, o6 = c3(s5);
      if (s(s5) || o6) {
        let t8 = 0;
        if (10 === s5) t8 -= 1e4;
        else if (o6 && d) t8 += 150;
        else {
          40 !== s5 && 65288 !== s5 || (t8 += 50);
          const i4 = m3[e5 + 1].codePoint;
          41 !== i4 && 65289 !== i4 || (t8 += 50);
        }
        x2.push(this._buildBreak(e5 + 1, m3[e5].x, y2, x2, t8, false));
      }
    }
    const M = this._optimalBreaks(this._buildBreak(g2, f3, y2, x2, 0, true));
    let u4 = 0;
    const _2 = c5 ? -l4 : l4;
    let I4 = 0;
    for (let t8 = 0; t8 < M.length; t8++) {
      const i4 = M[t8];
      let s5 = I4;
      for (; s5 < i4 && a2(m3[s5].codePoint); ) m3[s5].glyphMosaicItem = null, ++s5;
      let o6 = i4 - 1;
      for (; o6 > s5 && a2(m3[o6].codePoint); ) m3[o6].glyphMosaicItem = null, --o6;
      if (s5 <= o6) {
        const t9 = m3[s5].x;
        for (let i5 = s5; i5 <= o6; i5++) m3[i5].x -= t9, m3[i5].y = p4;
        let e5 = m3[o6].x;
        m3[o6].glyphMosaicItem && (e5 += m3[o6].glyphMosaicItem.metrics.advance), u4 = Math.max(e5, u4), r9 && this._applyJustification(m3, s5, o6);
      }
      I4 = i4, p4 += _2;
    }
    if (m3.length > 0) {
      const t8 = M.length - 1, e5 = (r9 - this._hAnchor) * u4;
      let i4 = (-this._vAnchor * (t8 + 1) + 0.5) * l4;
      c5 && t8 && (i4 += t8 * l4);
      for (const s5 of m3) s5.x += e5, s5.y += i4;
    }
    return m3.filter((t8) => t8.glyphMosaicItem);
  }
  static getTextBox(t8, e5) {
    if (!t8.length) return null;
    let i4 = 1 / 0, s5 = 1 / 0, o5 = 0, c5 = 0;
    for (const a6 of t8) {
      const t9 = a6.glyphMosaicItem.metrics.advance, l4 = a6.x, r9 = a6.y - h3, n5 = l4 + t9, m3 = r9 + e5;
      i4 = Math.min(i4, l4), o5 = Math.max(o5, n5), s5 = Math.min(s5, r9), c5 = Math.max(c5, m3);
    }
    return { x: i4, y: s5, width: o5 - i4, height: c5 - s5 };
  }
  static getBox(t8) {
    if (!t8.length) return null;
    let e5 = 1 / 0, i4 = 1 / 0, s5 = 0, o5 = 0;
    for (const c5 of t8) {
      const { height: t9, left: h5, top: a6, width: l4 } = c5.glyphMosaicItem.metrics, r9 = c5.x, n5 = c5.y - (t9 - Math.abs(a6)), m3 = r9 + l4 + h5, f3 = n5 + t9;
      e5 = Math.min(e5, r9), s5 = Math.max(s5, m3), i4 = Math.min(i4, n5), o5 = Math.max(o5, f3);
    }
    return { x: e5, y: i4, width: s5 - e5, height: o5 - i4 };
  }
  static addDecoration(t8, e5) {
    const i4 = t8.length;
    if (0 === i4) return;
    const s5 = 3;
    let c5 = t8[0].x + t8[0].glyphMosaicItem.metrics.left, h5 = t8[0].y;
    for (let l4 = 1; l4 < i4; l4++) {
      const i5 = t8[l4];
      if (i5.y !== h5) {
        const a7 = t8[l4 - 1].x + t8[l4 - 1].glyphMosaicItem.metrics.left + t8[l4 - 1].glyphMosaicItem.metrics.width;
        t8.push({ codePoint: 0, x: c5, y: h5 + e5 - s5, vertical: false, glyphMosaicItem: { sdf: true, rect: new t3(4, 0, 4, 8), metrics: { width: a7 - c5, height: 2 + 2 * s5, left: 0, top: 0, advance: 0 }, page: 0, code: 0 } }), h5 = i5.y, c5 = i5.x + i5.glyphMosaicItem.metrics.left;
      }
    }
    const a6 = t8[i4 - 1].x + t8[i4 - 1].glyphMosaicItem.metrics.left + t8[i4 - 1].glyphMosaicItem.metrics.width;
    t8.push({ codePoint: 0, x: c5, y: h5 + e5 - s5, vertical: false, glyphMosaicItem: { sdf: true, rect: new t3(4, 0, 4, 8), metrics: { width: a6 - c5, height: 2 + 2 * s5, left: 0, top: 0, advance: 0 }, page: 0, code: 0 } });
  }
  _breakScore(t8, e5, i4, s5) {
    const o5 = (t8 - e5) * (t8 - e5);
    return s5 ? t8 < e5 ? o5 / 2 : 2 * o5 : o5 + Math.abs(i4) * i4;
  }
  _buildBreak(t8, e5, i4, s5, o5, c5) {
    let h5 = null, a6 = this._breakScore(e5, i4, o5, c5);
    for (const l4 of s5) {
      const t9 = e5 - l4.x, s6 = this._breakScore(t9, i4, o5, c5) + l4.score;
      s6 <= a6 && (h5 = l4, a6 = s6);
    }
    return { index: t8, x: e5, score: a6, previousBreak: h5 };
  }
  _optimalBreaks(t8) {
    return t8 ? this._optimalBreaks(t8.previousBreak).concat(t8.index) : [];
  }
  _applyJustification(t8, e5, i4) {
    const s5 = t8[i4], o5 = s5.vertical ? c4 : s5.glyphMosaicItem ? s5.glyphMosaicItem.metrics.advance : 0, h5 = (s5.x + o5) * this._justify;
    for (let c5 = e5; c5 <= i4; c5++) t8[c5].x -= h5;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/Placement.js
var x = 4096;
var p3 = 8;
var w = 0.5;
var T2 = 2;
var y = class {
  constructor(e5, t8, i4 = 0, n5 = -1, s5 = w) {
    this.x = e5, this.y = t8, this.angle = i4, this.segment = n5, this.minzoom = s5;
  }
};
var f2 = class {
  constructor(e5, t8, i4, s5, o5, a6 = w, l4 = n2) {
    this.anchor = e5, this.labelAngle = t8, this.glyphAngle = i4, this.page = s5, this.alternateVerticalGlyph = o5, this.minzoom = a6, this.maxzoom = l4;
  }
};
var I2 = class {
  constructor(e5, t8, i4, n5, s5, o5, a6, l4, h5, r9, c5, g2) {
    this.tl = e5, this.tr = t8, this.bl = i4, this.br = n5, this.mosaicRect = s5, this.labelAngle = o5, this.minAngle = a6, this.maxAngle = l4, this.anchor = h5, this.minzoom = r9, this.maxzoom = c5, this.page = g2;
  }
};
var u2 = class {
  constructor(e5) {
    this.shapes = e5;
  }
};
var b = class {
  getIconPlacement(n5, s5, o5) {
    const a6 = new h(n5.x, n5.y), l4 = o5.rotationAlignment === l.MAP, h5 = o5.keepUpright;
    let r9 = o5.rotate * e2;
    l4 && (r9 += n5.angle);
    const m3 = new u2([]);
    return o5.allowOverlap && o5.ignorePlacement || !o2 || (m3.iconColliders = []), this._addIconPlacement(m3, a6, s5, o5, r9), l4 && h5 && this._addIconPlacement(m3, a6, s5, o5, r9 + t4), m3;
  }
  _addIconPlacement(t8, i4, s5, o5, a6) {
    const l4 = s5.pixelRatio, h5 = s5.width / l4, r9 = s5.height / l4, g2 = o5.offset;
    let x2 = g2[0], d = g2[1];
    switch (o5.anchor) {
      case m.CENTER:
        x2 -= h5 / 2, d -= r9 / 2;
        break;
      case m.LEFT:
        d -= r9 / 2;
        break;
      case m.RIGHT:
        x2 -= h5, d -= r9 / 2;
        break;
      case m.TOP:
        x2 -= h5 / 2;
        break;
      case m.BOTTOM:
        x2 -= h5 / 2, d -= r9;
        break;
      case m.TOP_LEFT:
        break;
      case m.BOTTOM_LEFT:
        d -= r9;
        break;
      case m.TOP_RIGHT:
        x2 -= h5;
        break;
      case m.BOTTOM_RIGHT:
        x2 -= h5, d -= r9;
    }
    const p4 = s5.rect, T4 = 2 / l4, y2 = x2 - T4, f3 = d - T4, u4 = y2 + p4.width / l4, b3 = f3 + p4.height / l4, P = new h(y2, f3), O = new h(u4, b3), _2 = new h(y2, b3), k = new h(u4, f3);
    if (0 !== a6) {
      const e5 = Math.cos(a6), t9 = Math.sin(a6);
      P.rotate(e5, t9), O.rotate(e5, t9), _2.rotate(e5, t9), k.rotate(e5, t9);
    }
    const M = new I2(P, k, _2, O, p4, a6, 0, 256, i4, w, n2, 0);
    if (t8.shapes.push(M), (!o5.allowOverlap || !o5.ignorePlacement) && o2) {
      const e5 = o5.size, s6 = o5.padding, l5 = { xTile: i4.x, yTile: i4.y, dxPixels: x2 * e5 - s6, dyPixels: d * e5 - s6, hard: !o5.optional, partIndex: 0, width: h5 * e5 + 2 * s6, height: r9 * e5 + 2 * s6, angle: a6, minLod: w, maxLod: n2 };
      t8.iconColliders.push(l5);
    }
  }
  getTextPlacement(s5, o5, a6, c5) {
    const x2 = new h(s5.x, s5.y), d = c5.rotate * e2, y2 = c5.rotationAlignment === l.MAP, b3 = c5.keepUpright, P = c5.padding;
    let O = w;
    const _2 = !y2 ? 0 : s5.angle, k = s5.segment >= 0 && y2, M = c5.allowOverlap && c5.ignorePlacement ? null : [], E2 = [], G = 4, N2 = !k;
    let A2 = Number.POSITIVE_INFINITY, L2 = Number.NEGATIVE_INFINITY, z = A2, F = L2;
    const v = (k || y2) && b3, R = c5.size / c4;
    let B2 = false;
    for (const e5 of o5) if (e5.vertical) {
      B2 = true;
      break;
    }
    let H, V = 0, j2 = 0;
    if (!k && B2) {
      const e5 = a3.getTextBox(o5, c5.lineHeight * c4);
      switch (c5.anchor) {
        case m.LEFT:
          V = e5.height / 2, j2 = -e5.width / 2;
          break;
        case m.RIGHT:
          V = -e5.height / 2, j2 = e5.width / 2;
          break;
        case m.TOP:
          V = e5.height / 2, j2 = e5.width / 2;
          break;
        case m.BOTTOM:
          V = -e5.height / 2, j2 = -e5.width / 2;
          break;
        case m.TOP_LEFT:
          V = e5.height;
          break;
        case m.BOTTOM_LEFT:
          j2 = -e5.width;
          break;
        case m.TOP_RIGHT:
          j2 = e5.width;
          break;
        case m.BOTTOM_RIGHT:
          V = -e5.height;
      }
    }
    V += c5.offset[0] * c4, j2 += c5.offset[1] * c4;
    for (const t8 of o5) {
      const o6 = t8.glyphMosaicItem;
      if (!o6 || o6.rect.isEmpty) continue;
      const l4 = o6.rect, h5 = o6.metrics, g2 = o6.page;
      if (M && N2) {
        if (void 0 !== H && H !== t8.y) {
          let e5, t9, i4, o7;
          B2 ? (e5 = -F + V, t9 = A2 + j2, i4 = F - z, o7 = L2 - A2) : (e5 = A2 + V, t9 = z + j2, i4 = L2 - A2, o7 = F - z);
          const a7 = { xTile: s5.x, yTile: s5.y, dxPixels: e5 * R - P, dyPixels: t9 * R - P, hard: !c5.optional, partIndex: 1, width: i4 * R + 2 * P, height: o7 * R + 2 * P, angle: d, minLod: w, maxLod: n2 };
          M.push(a7), A2 = Number.POSITIVE_INFINITY, L2 = Number.NEGATIVE_INFINITY, z = A2, F = L2;
        }
        H = t8.y;
      }
      const m3 = [];
      if (k) {
        const e5 = 0.5 * o6.metrics.width, i4 = (t8.x + h5.left - G + e5) * R * p3;
        if (O = this._placeGlyph(s5, O, i4, a6, s5.segment, 1, t8.vertical, g2, m3), b3 && (O = this._placeGlyph(s5, O, i4, a6, s5.segment, -1, t8.vertical, g2, m3)), O >= T2) break;
      } else m3.push(new f2(x2, _2, _2, g2, false)), y2 && b3 && m3.push(new f2(x2, _2 + t4, _2 + t4, g2, false));
      const u4 = t8.x + h5.left, C3 = t8.y - h3 - h5.top, S = u4 + h5.width, Y = C3 + h5.height;
      let q, U, D, J, K, Q, W, X;
      if (!k && B2) if (t8.vertical) {
        const t9 = (u4 + S) / 2 - h5.height / 2, i4 = (C3 + Y) / 2 + h5.width / 2;
        q = new h(-i4 - G + V, t9 - G + j2), U = new h(q.x + l4.width, q.y + l4.height), D = new h(q.x, U.y), J = new h(U.x, q.y);
      } else q = new h(-C3 + G + V, u4 - G + j2), U = new h(q.x - l4.height, q.y + l4.width), D = new h(U.x, q.y), J = new h(q.x, U.y);
      else q = new h(u4 - G + V, C3 - G + j2), U = new h(q.x + l4.width, q.y + l4.height), D = new h(q.x, U.y), J = new h(U.x, q.y);
      for (const i4 of m3) {
        let n5, o7, a7, r9;
        if (i4.alternateVerticalGlyph) {
          if (!K) {
            const t9 = (C3 + Y) / 2 + j2;
            K = new h((u4 + S) / 2 + V - h5.height / 2 - G, t9 + h5.width / 2 + G), Q = new h(K.x + l4.height, K.y - l4.width), W = new h(Q.x, K.y), X = new h(K.x, Q.y);
          }
          n5 = K, o7 = W, a7 = X, r9 = Q;
        } else n5 = q, o7 = D, a7 = J, r9 = U;
        const g3 = C3, m4 = Y, x3 = i4.glyphAngle + d;
        if (0 !== x3) {
          const e5 = Math.cos(x3), t9 = Math.sin(x3);
          n5 = n5.clone(), o7 = o7 == null ? void 0 : o7.clone(), a7 = a7 == null ? void 0 : a7.clone(), r9 = r9 == null ? void 0 : r9.clone(), n5.rotate(e5, t9), r9 == null ? void 0 : r9.rotate(e5, t9), o7 == null ? void 0 : o7.rotate(e5, t9), a7 == null ? void 0 : a7.rotate(e5, t9);
        }
        let p4 = 0, w3 = 256;
        if (k && B2 ? t8.vertical ? i4.alternateVerticalGlyph ? (p4 = 32, w3 = 96) : (p4 = 224, w3 = 32) : (p4 = 224, w3 = 96) : (p4 = 192, w3 = 64), E2.push(new I2(n5, a7, o7, r9, l4, i4.labelAngle, p4, w3, i4.anchor, i4.minzoom, i4.maxzoom, i4.page)), M && (!v || this._legible(i4.labelAngle))) {
          if (N2) u4 < A2 && (A2 = u4), g3 < z && (z = g3), S > L2 && (L2 = S), m4 > F && (F = m4);
          else if (i4.minzoom < T2) {
            const e5 = { xTile: s5.x, yTile: s5.y, dxPixels: (u4 + V) * R - P, dyPixels: (g3 + V) * R - P, hard: !c5.optional, partIndex: 1, width: (S - u4) * R + 2 * P, height: (m4 - g3) * R + 2 * P, angle: x3, minLod: i4.minzoom, maxLod: i4.maxzoom };
            M.push(e5);
          }
        }
      }
    }
    if (O >= T2) return null;
    if (M && N2) {
      let e5, t8, i4, o6;
      B2 ? (e5 = -F + V, t8 = A2 + j2, i4 = F - z, o6 = L2 - A2) : (e5 = A2 + V, t8 = z + j2, i4 = L2 - A2, o6 = F - z);
      const a7 = { xTile: s5.x, yTile: s5.y, dxPixels: e5 * R - P, dyPixels: t8 * R - P, hard: !c5.optional, partIndex: 1, width: i4 * R + 2 * P, height: o6 * R + 2 * P, angle: d, minLod: w, maxLod: n2 };
      M.push(a7);
    }
    const C2 = new u2(E2);
    return M && M.length > 0 && (C2.textColliders = M), C2;
  }
  _legible(e5) {
    const t8 = i3(e5);
    return t8 < 65 || t8 >= 193;
  }
  _placeGlyph(t8, s5, l4, h5, r9, c5, g2, m3, x2) {
    let d = c5;
    const p4 = d < 0 ? f(t8.angle + t4, r4) : t8.angle;
    let w3 = 0;
    l4 < 0 && (d *= -1, l4 *= -1, w3 = t4), d > 0 && ++r9;
    let T4 = new h(t8.x, t8.y), y2 = h5[r9], I4 = n2;
    if (h5.length <= r9) return I4;
    for (; ; ) {
      const e5 = y2.x - T4.x, t9 = y2.y - T4.y, i4 = Math.sqrt(e5 * e5 + t9 * t9), n5 = Math.max(l4 / i4, s5), c6 = e5 / i4, u4 = t9 / i4, b3 = f(Math.atan2(u4, c6) + w3, r4);
      if (x2.push(new f2(T4, p4, b3, m3, false, n5, I4)), g2 && x2.push(new f2(T4, p4, b3, m3, true, n5, I4)), n5 <= s5) return n5;
      T4 = y2.clone();
      do {
        if (r9 += d, h5.length <= r9 || r9 < 0) return n5;
        y2 = h5[r9];
      } while (T4.isEqual(y2));
      let P = y2.x - T4.x, O = y2.y - T4.y;
      const _2 = Math.sqrt(P * P + O * O);
      P *= i4 / _2, O *= i4 / _2, T4.x -= P, T4.y -= O, I4 = n5;
    }
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/Feature.js
var t6;
!function(e5) {
  e5[e5.moveTo = 1] = "moveTo", e5[e5.lineTo = 2] = "lineTo", e5[e5.close = 7] = "close";
}(t6 || (t6 = {}));
var s2 = class {
  constructor(e5, t8) {
    this.values = {}, this._geometry = void 0, this._pbfGeometry = null;
    const s5 = t8.keys, o5 = t8.values, r9 = e5.asUnsafe();
    for (; r9.next(); ) switch (r9.tag()) {
      case 1:
        this.id = r9.getUInt64();
        break;
      case 2: {
        const e6 = r9.getMessage().asUnsafe(), t9 = this.values;
        for (; !e6.empty(); ) {
          const r10 = e6.getUInt32(), a6 = e6.getUInt32();
          t9[s5[r10]] = o5[a6];
        }
        e6.release();
        break;
      }
      case 3:
        this.type = r9.getUInt32();
        break;
      case 4:
        this._pbfGeometry = r9.getMessage();
        break;
      default:
        r9.skip();
    }
  }
  getGeometry(s5) {
    if (void 0 !== this._geometry) return this._geometry;
    if (!this._pbfGeometry) return null;
    const o5 = this._pbfGeometry.asUnsafe();
    let r9, a6;
    this._pbfGeometry = null, s5 ? s5.reset(this.type) : r9 = [];
    let n5, i4 = t6.moveTo, l4 = 0, c5 = 0, h5 = 0;
    for (; !o5.empty(); ) {
      if (0 === l4) {
        const e5 = o5.getUInt32();
        i4 = 7 & e5, l4 = e5 >> 3;
      }
      switch (l4--, i4) {
        case t6.moveTo:
          c5 += o5.getSInt32(), h5 += o5.getSInt32(), s5 ? s5.moveTo(c5, h5) : r9 && (a6 && r9.push(a6), a6 = [], a6.push(new h(c5, h5)));
          break;
        case t6.lineTo:
          c5 += o5.getSInt32(), h5 += o5.getSInt32(), s5 ? s5.lineTo(c5, h5) : a6 && a6.push(new h(c5, h5));
          break;
        case t6.close:
          s5 ? s5.close() : a6 && !a6[0].equals(c5, h5) && a6.push(a6[0].clone());
          break;
        default:
          throw o5.release(), new Error("Invalid path operation");
      }
    }
    return s5 ? n5 = s5.result() : r9 && (a6 && r9.push(a6), n5 = r9), o5.release(), this._geometry = n5, n5;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/IndexMemoryBuffer.js
var r5 = class extends t5 {
  constructor() {
    super(12);
  }
  add(s5, r9, t8) {
    const e5 = this.array;
    e5.push(s5), e5.push(r9), e5.push(t8);
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/SourceLayerData.js
var e4 = class _e {
  constructor(t8) {
    this.extent = 4096, this.keys = [], this.values = [], this._pbfLayer = t8.clone();
    const s5 = t8.asUnsafe();
    for (; s5.next(); ) switch (s5.tag()) {
      case 1:
        this.name = s5.getString();
        break;
      case 3:
        this.keys.push(s5.getString());
        break;
      case 4:
        this.values.push(s5.processMessage(_e._parseValue));
        break;
      case 5:
        this.extent = s5.getUInt32();
        break;
      default:
        s5.skip();
    }
  }
  getData() {
    return this._pbfLayer;
  }
  static _parseValue(e5) {
    for (; e5.next(); ) switch (e5.tag()) {
      case 1:
        return e5.getString();
      case 2:
        return e5.getFloat();
      case 3:
        return e5.getDouble();
      case 4:
        return e5.getInt64();
      case 5:
        return e5.getUInt64();
      case 6:
        return e5.getSInt64();
      case 7:
        return e5.getBool();
      default:
        e5.skip();
    }
    return null;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/VertexMemoryBuffer.js
var o3 = class extends t5 {
  constructor(t8) {
    super(t8);
  }
  add(t8, o5, r9, u4, h5, a6, n5, d, e5, p4, i4, c5) {
    const M = this.array;
    let l4 = t5.i1616to32(t8, o5);
    M.push(l4);
    const m3 = 31;
    l4 = t5.i8888to32(Math.round(m3 * r9), Math.round(m3 * u4), Math.round(m3 * h5), Math.round(m3 * a6)), M.push(l4), l4 = t5.i8888to32(Math.round(m3 * n5), Math.round(m3 * d), Math.round(m3 * e5), Math.round(m3 * p4)), M.push(l4), l4 = t5.i1616to32(i4, 0), M.push(l4), c5 && M.push(...c5);
  }
};
var r6 = class extends t5 {
  constructor(t8) {
    super(t8);
  }
  add(t8, o5, r9) {
    const u4 = this.array;
    u4.push(t5.i1616to32(t8, o5)), r9 && u4.push(...r9);
  }
};
var u3 = class extends t5 {
  constructor(t8) {
    super(t8);
  }
  add(t8, o5, r9, u4, h5, a6, n5) {
    const d = this.array, e5 = this.index;
    let p4 = t5.i1616to32(t8, o5);
    d.push(p4);
    const i4 = 15;
    return p4 = t5.i8888to32(Math.round(i4 * r9), Math.round(i4 * u4), h5, a6), d.push(p4), n5 && d.push(...n5), e5;
  }
};
var h4 = class extends t5 {
  constructor(t8) {
    super(t8);
  }
  add(o5, r9, u4, h5, a6, n5, d, e5, p4, i4, c5, M) {
    const l4 = this.array;
    let m3 = t5.i1616to32(o5, r9);
    l4.push(m3), m3 = t5.i1616to32(Math.round(8 * u4), Math.round(8 * h5)), l4.push(m3), m3 = t5.i8888to32(a6 / 4, n5 / 4, e5, p4), l4.push(m3), m3 = t5.i8888to32(0, i3(d), 10 * i4, Math.min(10 * c5, 255)), l4.push(m3), M && l4.push(...M);
  }
};
var a4 = class extends t5 {
  constructor(t8) {
    super(t8);
  }
  add(t8, o5, r9, u4, h5) {
    const a6 = this.array, n5 = t5.i1616to32(2 * t8 + r9, 2 * o5 + u4);
    a6.push(n5), h5 && a6.push(...h5);
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/buckets/BaseBucket.js
var t7 = class {
  constructor(t8, e5, s5) {
    this.layerExtent = 4096, this._features = [], this.layer = t8, this.zoom = e5, this._spriteInfo = s5, this._filter = t8.getFeatureFilter();
  }
  pushFeature(t8) {
    this._filter && !this._filter.filter(t8, this.zoom) || this._features.push(t8);
  }
  hasFeatures() {
    return this._features.length > 0;
  }
  getResources(t8, e5, s5) {
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/buckets/CircleBucket.js
var r7 = class extends t7 {
  constructor(t8, r9, i4, c5, s5) {
    super(t8, r9, i4), this.type = E.CIRCLE, this._circleVertexBuffer = c5, this._circleIndexBuffer = s5;
  }
  get circleIndexStart() {
    return this._circleIndexStart;
  }
  get circleIndexCount() {
    return this._circleIndexCount;
  }
  processFeatures(e5) {
    const t8 = this._circleVertexBuffer, r9 = this._circleIndexBuffer;
    this._circleIndexStart = 3 * r9.index, this._circleIndexCount = 0;
    const i4 = this.layer, c5 = this.zoom;
    e5 && e5.setExtent(this.layerExtent);
    for (const s5 of this._features) {
      const n5 = s5.getGeometry(e5);
      if (!n5) continue;
      const l4 = i4.circleMaterial.encodeAttributes(s5, c5, i4);
      for (const e6 of n5) if (e6) for (const i5 of e6) {
        const e7 = t8.index;
        t8.add(i5.x, i5.y, 0, 0, l4), t8.add(i5.x, i5.y, 0, 1, l4), t8.add(i5.x, i5.y, 1, 0, l4), t8.add(i5.x, i5.y, 1, 1, l4), r9.add(e7 + 0, e7 + 1, e7 + 2), r9.add(e7 + 1, e7 + 2, e7 + 3), this._circleIndexCount += 6;
      }
    }
  }
  serialize() {
    let e5 = 6;
    e5 += this.layerUIDs.length, e5 += this._circleVertexBuffer.array.length, e5 += this._circleIndexBuffer.array.length;
    const t8 = new Uint32Array(e5), r9 = new Int32Array(t8.buffer);
    let i4 = 0;
    t8[i4++] = this.type, t8[i4++] = this.layerUIDs.length;
    for (let c5 = 0; c5 < this.layerUIDs.length; c5++) t8[i4++] = this.layerUIDs[c5];
    t8[i4++] = this._circleIndexStart, t8[i4++] = this._circleIndexCount, t8[i4++] = this._circleVertexBuffer.array.length;
    for (let c5 = 0; c5 < this._circleVertexBuffer.array.length; c5++) r9[i4++] = this._circleVertexBuffer.array[c5];
    t8[i4++] = this._circleIndexBuffer.array.length;
    for (let c5 = 0; c5 < this._circleIndexBuffer.array.length; c5++) t8[i4++] = this._circleIndexBuffer.array[c5];
    return t8.buffer;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/buckets/FillBucket.js
var s3 = class _s extends t7 {
  constructor(t8, e5, i4, r9, n5, s5, o5) {
    super(t8, e5, i4), this.type = E.FILL, this._patternMap = /* @__PURE__ */ new Map(), this._fillVertexBuffer = r9, this._fillIndexBuffer = n5, this._outlineVertexBuffer = s5, this._outlineIndexBuffer = o5;
  }
  get fillIndexStart() {
    return this._fillIndexStart;
  }
  get fillIndexCount() {
    return this._fillIndexCount;
  }
  get outlineIndexStart() {
    return this._outlineIndexStart;
  }
  get outlineIndexCount() {
    return this._outlineIndexCount;
  }
  getResources(t8, e5, i4) {
    const r9 = this.layer, l4 = this.zoom, n5 = r9.getPaintProperty("fill-pattern");
    if (n5) if (n5.isDataDriven) for (const s5 of this._features) e5(n5.getValue(l4, s5), true);
    else e5(n5.getValue(l4), true);
  }
  processFeatures(t8) {
    this._fillIndexStart = 3 * this._fillIndexBuffer.index, this._fillIndexCount = 0, this._outlineIndexStart = 3 * this._outlineIndexBuffer.index, this._outlineIndexCount = 0;
    const e5 = this.layer, i4 = this.zoom, { fillMaterial: r9, outlineMaterial: l4, hasDataDrivenFill: n5, hasDataDrivenOutline: s5 } = e5;
    t8 && t8.setExtent(this.layerExtent);
    const o5 = e5.getPaintProperty("fill-pattern"), a6 = o5 == null ? void 0 : o5.isDataDriven;
    let f3 = !o5 && e5.getPaintValue("fill-antialias", i4);
    if (e5.outlineUsesFillColor) {
      if (f3 && !e5.hasDataDrivenOpacity) {
        const t9 = e5.getPaintValue("fill-opacity", i4), r10 = e5.getPaintValue("fill-opacity", i4 + 1);
        t9 < 1 && r10 < 1 && (f3 = false);
      }
      if (f3 && !e5.hasDataDrivenColor) {
        const t9 = e5.getPaintValue("fill-color", i4), r10 = e5.getPaintValue("fill-color", i4 + 1);
        t9[3] < 1 && r10[3] < 1 && (f3 = false);
      }
    }
    const u4 = this._features, d = t8 == null ? void 0 : t8.validateTessellation;
    if (a6) {
      const n6 = [];
      for (const a7 of u4) {
        const u5 = o5.getValue(i4, a7), h5 = this._spriteInfo[u5];
        if (!h5 || !h5.rect) continue;
        const x2 = r9.encodeAttributes(a7, i4, e5, h5), c5 = f3 && s5 ? l4.encodeAttributes(a7, i4, e5) : [], _2 = a7.getGeometry(t8);
        n6.push({ ddFillAttributes: x2, ddOutlineAttributes: c5, page: h5.page, geometry: _2 }), n6.sort((t9, e6) => t9.page - e6.page);
        for (const { ddFillAttributes: t9, ddOutlineAttributes: i5, page: r10, geometry: l5 } of n6) this._processFeature(l5, f3, e5.outlineUsesFillColor, t9, i5, d, r10);
      }
    } else for (const h5 of u4) {
      const o6 = n5 ? r9.encodeAttributes(h5, i4, e5) : null, a7 = f3 && s5 ? l4.encodeAttributes(h5, i4, e5) : null, u5 = h5.getGeometry(t8);
      this._processFeature(u5, f3, e5.outlineUsesFillColor, o6, a7, d);
    }
  }
  serialize() {
    let t8 = 10;
    t8 += this.layerUIDs.length, t8 += this._fillVertexBuffer.array.length, t8 += this._fillIndexBuffer.array.length, t8 += this._outlineVertexBuffer.array.length, t8 += this._outlineIndexBuffer.array.length, t8 += 3 * this._patternMap.size + 1;
    const e5 = new Uint32Array(t8), i4 = new Int32Array(e5.buffer);
    let r9 = 0;
    e5[r9++] = this.type, e5[r9++] = this.layerUIDs.length;
    for (let s5 = 0; s5 < this.layerUIDs.length; s5++) e5[r9++] = this.layerUIDs[s5];
    e5[r9++] = this._fillIndexStart, e5[r9++] = this._fillIndexCount, e5[r9++] = this._outlineIndexStart, e5[r9++] = this._outlineIndexCount;
    const l4 = this._patternMap, n5 = l4.size;
    if (e5[r9++] = n5, n5 > 0) for (const [s5, [o5, a6]] of l4) e5[r9++] = s5, e5[r9++] = o5, e5[r9++] = a6;
    e5[r9++] = this._fillVertexBuffer.array.length;
    for (let s5 = 0; s5 < this._fillVertexBuffer.array.length; s5++) i4[r9++] = this._fillVertexBuffer.array[s5];
    e5[r9++] = this._fillIndexBuffer.array.length;
    for (let s5 = 0; s5 < this._fillIndexBuffer.array.length; s5++) e5[r9++] = this._fillIndexBuffer.array[s5];
    e5[r9++] = this._outlineVertexBuffer.array.length;
    for (let s5 = 0; s5 < this._outlineVertexBuffer.array.length; s5++) i4[r9++] = this._outlineVertexBuffer.array[s5];
    e5[r9++] = this._outlineIndexBuffer.array.length;
    for (let s5 = 0; s5 < this._outlineIndexBuffer.array.length; s5++) e5[r9++] = this._outlineIndexBuffer.array[s5];
    return e5.buffer;
  }
  _processFeature(t8, e5, i4, r9, l4, n5, o5) {
    if (!t8) return;
    const a6 = t8.length, f3 = !l4 || 0 === l4.length;
    if (e5 && (!i4 || f3)) for (let s5 = 0; s5 < a6; s5++) this._processOutline(t8[s5], l4);
    const u4 = 32;
    let d;
    for (let h5 = 0; h5 < a6; h5++) {
      const e6 = _s._area(t8[h5]);
      e6 > u4 ? (void 0 !== d && this._processFill(t8, d, r9, n5, o5), d = [h5]) : e6 < -u4 && void 0 !== d && d.push(h5);
    }
    void 0 !== d && this._processFill(t8, d, r9, n5, o5);
  }
  _processOutline(t8, e5) {
    const i4 = this._outlineVertexBuffer, l4 = this._outlineIndexBuffer, n5 = l4.index;
    let s5, o5, a6;
    const f3 = new h(0, 0), u4 = new h(0, 0), d = new h(0, 0);
    let h5 = -1, x2 = -1, c5 = -1, _2 = -1, y2 = -1, g2 = false;
    const p4 = 0;
    let I4 = t8.length;
    if (I4 < 2) return;
    const B2 = t8[p4];
    let m3 = t8[I4 - 1];
    for (; I4 && m3.isEqual(B2); ) --I4, m3 = t8[I4 - 1];
    if (!(I4 - p4 < 2)) {
      for (let r9 = p4; r9 < I4; ++r9) {
        r9 === p4 ? (s5 = t8[I4 - 1], o5 = t8[p4], a6 = t8[p4 + 1], f3.assignSub(o5, s5), f3.normalize(), f3.rightPerpendicular()) : (s5 = o5, o5 = a6, a6 = r9 !== I4 - 1 ? t8[r9 + 1] : t8[p4], f3.assign(u4));
        const n6 = this._isClipEdge(s5, o5);
        -1 === _2 && (g2 = n6), u4.assignSub(a6, o5), u4.normalize(), u4.rightPerpendicular();
        const B3 = f3.x * u4.y - f3.y * u4.x;
        d.assignAdd(f3, u4), d.normalize();
        const m4 = -d.x * -f3.x + -d.y * -f3.y;
        let V = Math.abs(0 !== m4 ? 1 / m4 : 1);
        V > 8 && (V = 8), B3 >= 0 ? (c5 = i4.add(o5.x, o5.y, f3.x, f3.y, 0, 1, e5), -1 === _2 && (_2 = c5), h5 >= 0 && x2 >= 0 && c5 >= 0 && !n6 && l4.add(h5, x2, c5), x2 = i4.add(o5.x, o5.y, V * -d.x, V * -d.y, 0, -1, e5), -1 === y2 && (y2 = x2), h5 >= 0 && x2 >= 0 && c5 >= 0 && !n6 && l4.add(h5, x2, c5), h5 = x2, x2 = c5, c5 = i4.add(o5.x, o5.y, d.x, d.y, 0, 1, e5), h5 >= 0 && x2 >= 0 && c5 >= 0 && !n6 && l4.add(h5, x2, c5), x2 = i4.add(o5.x, o5.y, u4.x, u4.y, 0, 1, e5), h5 >= 0 && x2 >= 0 && c5 >= 0 && !n6 && l4.add(h5, x2, c5)) : (c5 = i4.add(o5.x, o5.y, V * d.x, V * d.y, 0, 1, e5), -1 === _2 && (_2 = c5), h5 >= 0 && x2 >= 0 && c5 >= 0 && !n6 && l4.add(h5, x2, c5), x2 = i4.add(o5.x, o5.y, -f3.x, -f3.y, 0, -1, e5), -1 === y2 && (y2 = x2), h5 >= 0 && x2 >= 0 && c5 >= 0 && !n6 && l4.add(h5, x2, c5), h5 = x2, x2 = c5, c5 = i4.add(o5.x, o5.y, -d.x, -d.y, 0, -1, e5), h5 >= 0 && x2 >= 0 && c5 >= 0 && !n6 && l4.add(h5, x2, c5), h5 = i4.add(o5.x, o5.y, -u4.x, -u4.y, 0, -1, e5), h5 >= 0 && x2 >= 0 && c5 >= 0 && !n6 && l4.add(h5, x2, c5));
      }
      h5 >= 0 && x2 >= 0 && _2 >= 0 && !g2 && l4.add(h5, x2, _2), h5 >= 0 && _2 >= 0 && y2 >= 0 && !g2 && l4.add(h5, y2, _2), this._outlineIndexCount += 3 * (l4.index - n5);
    }
  }
  _processFill(r9, l4, n5, s5, o5) {
    s5 = true;
    let a6;
    l4.length > 1 && (a6 = []);
    let f3 = 0;
    for (const t8 of l4) 0 !== f3 && a6.push(f3), f3 += r9[t8].length;
    const u4 = 2 * f3, d = t.acquire();
    for (const t8 of l4) {
      const e5 = r9[t8], i4 = e5.length;
      for (let t9 = 0; t9 < i4; ++t9) d.push(e5[t9].x, e5[t9].y);
    }
    const h5 = r3(d, a6, 2);
    if (s5 && r3.deviation(d, a6, 2, h5) > 0) {
      const t8 = l4.map((t9) => r9[t9].length), { buffer: e5, vertexCount: s6 } = r2(d, t8);
      if (s6 > 0) {
        const t9 = this._fillVertexBuffer.index;
        for (let i4 = 0; i4 < s6; i4++) this._fillVertexBuffer.add(e5[2 * i4], e5[2 * i4 + 1], n5);
        for (let e6 = 0; e6 < s6; e6 += 3) {
          const i4 = t9 + e6;
          this._fillIndexBuffer.add(i4, i4 + 1, i4 + 2);
        }
        if (void 0 !== o5) {
          const t10 = this._patternMap, e6 = t10.get(o5);
          e6 ? e6[1] += s6 : t10.set(o5, [this._fillIndexStart + this._fillIndexCount, s6]);
        }
        this._fillIndexCount += s6;
      }
    } else {
      const t8 = h5.length;
      if (t8 > 0) {
        const e5 = this._fillVertexBuffer.index;
        let i4 = 0;
        for (; i4 < u4; ) this._fillVertexBuffer.add(d[i4++], d[i4++], n5);
        let r10 = 0;
        for (; r10 < t8; ) this._fillIndexBuffer.add(e5 + h5[r10++], e5 + h5[r10++], e5 + h5[r10++]);
        if (void 0 !== o5) {
          const e6 = this._patternMap, i5 = e6.get(o5);
          i5 ? i5[1] += t8 : e6.set(o5, [this._fillIndexStart + this._fillIndexCount, t8]);
        }
        this._fillIndexCount += t8;
      }
    }
    t.release(d);
  }
  _isClipEdge(t8, e5) {
    return t8.x === e5.x ? t8.x <= -64 || t8.x >= 4160 : t8.y === e5.y && (t8.y <= -64 || t8.y >= 4160);
  }
  static _area(t8) {
    let e5 = 0;
    const i4 = t8.length - 1;
    for (let r9 = 0; r9 < i4; r9++) e5 += (t8[r9].x - t8[r9 + 1].x) * (t8[r9].y + t8[r9 + 1].y);
    return e5 += (t8[i4].x - t8[0].x) * (t8[i4].y + t8[0].y), 0.5 * e5;
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/buckets/LineBucket.js
var s4 = 65535;
var n4 = class extends t7 {
  constructor(t8, s5, n5, a6, o5) {
    super(t8, s5, n5), this.type = E.LINE, this._tessellationOptions = { pixelCoordRatio: 8, halfWidth: 0, offset: 0 }, this._patternMap = /* @__PURE__ */ new Map(), this.tessellationProperties = { _lineVertexBuffer: null, _lineIndexBuffer: null, _ddValues: null }, this.tessellationProperties._lineVertexBuffer = a6, this.tessellationProperties._lineIndexBuffer = o5, this._lineTessellator = new c2(r8(this.tessellationProperties), l3(this.tessellationProperties), t8.canUseThinTessellation);
  }
  get lineIndexStart() {
    return this._lineIndexStart;
  }
  get lineIndexCount() {
    return this._lineIndexCount;
  }
  getResources(e5, t8, i4) {
    const s5 = this.layer, n5 = this.zoom, r9 = s5.getPaintProperty("line-pattern"), l4 = s5.getPaintProperty("line-dasharray"), a6 = s5.getLayoutProperty("line-cap");
    if (!r9 && !l4) return;
    const o5 = (a6 == null ? void 0 : a6.getValue(n5)) || 0, u4 = a6 == null ? void 0 : a6.isDataDriven, f3 = r9 == null ? void 0 : r9.isDataDriven, h5 = l4 == null ? void 0 : l4.isDataDriven;
    if (f3 || h5) for (const p4 of this._features) t8(f3 ? r9.getValue(n5, p4) : this._getDashArrayKey(p4, n5, s5, l4, u4, a6, o5));
    else if (r9) t8(r9.getValue(n5));
    else if (l4) {
      const e6 = l4.getValue(n5);
      t8(s5.getDashKey(e6, o5));
    }
  }
  processFeatures(e5) {
    this._lineIndexStart = 3 * this.tessellationProperties._lineIndexBuffer.index, this._lineIndexCount = 0;
    const t8 = this.layer, i4 = this.zoom, s5 = this._features, n5 = this._tessellationOptions, { hasDataDrivenLine: r9, lineMaterial: l4 } = t8;
    e5 && e5.setExtent(this.layerExtent);
    const a6 = t8.getPaintProperty("line-pattern"), o5 = t8.getPaintProperty("line-dasharray"), u4 = a6 == null ? void 0 : a6.isDataDriven, f3 = o5 == null ? void 0 : o5.isDataDriven;
    let h5;
    h5 = t8.getLayoutProperty("line-cap");
    const p4 = (h5 == null ? void 0 : h5.isDataDriven) ? h5 : null, g2 = p4 ? null : t8.getLayoutValue("line-cap", i4), y2 = g2 || 0, d = !!p4;
    h5 = t8.getLayoutProperty("line-join");
    const c5 = (h5 == null ? void 0 : h5.isDataDriven) ? h5 : null, _2 = c5 ? null : t8.getLayoutValue("line-join", i4);
    h5 = t8.getLayoutProperty("line-miter-limit");
    const x2 = (h5 == null ? void 0 : h5.isDataDriven) ? h5 : null, V = x2 ? null : t8.getLayoutValue("line-miter-limit", i4);
    h5 = t8.getLayoutProperty("line-round-limit");
    const m3 = (h5 == null ? void 0 : h5.isDataDriven) ? h5 : null, D = m3 ? null : t8.getLayoutValue("line-round-limit", i4);
    h5 = t8.getPaintProperty("line-width");
    const P = (h5 == null ? void 0 : h5.isDataDriven) ? h5 : null, I4 = P ? null : t8.getPaintValue("line-width", i4);
    h5 = t8.getPaintProperty("line-offset");
    const L2 = (h5 == null ? void 0 : h5.isDataDriven) ? h5 : null, B2 = L2 ? null : t8.getPaintValue("line-offset", i4);
    if (u4 || f3) {
      const r10 = [];
      for (const n6 of s5) {
        const s6 = u4 ? a6.getValue(i4, n6) : this._getDashArrayKey(n6, i4, t8, o5, d, p4, y2), f4 = this._spriteInfo[s6];
        if (!f4 || !f4.rect) continue;
        const h6 = l4.encodeAttributes(n6, i4, t8, f4), v = n6.getGeometry(e5);
        r10.push({ ddAttributes: h6, page: f4.page, cap: p4 ? p4.getValue(i4, n6) : g2, join: c5 ? c5.getValue(i4, n6) : _2, miterLimit: x2 ? x2.getValue(i4, n6) : V, roundLimit: m3 ? m3.getValue(i4, n6) : D, halfWidth: 0.5 * (P ? P.getValue(i4, n6) : I4), offset: L2 ? L2.getValue(i4, n6) : B2, geometry: v });
      }
      r10.sort((e6, t9) => e6.page - t9.page), n5.textured = true;
      for (const { ddAttributes: e6, page: t9, cap: i5, join: s6, miterLimit: l5, roundLimit: a7, halfWidth: o6, offset: u5, geometry: f4 } of r10) n5.capType = i5, n5.joinType = s6, n5.miterLimit = l5, n5.roundLimit = a7, n5.halfWidth = o6, n5.offset = u5, this._processFeature(f4, e6, t9);
    } else {
      if (a6) {
        const e6 = a6.getValue(i4), t9 = this._spriteInfo[e6];
        if (!t9 || !t9.rect) return;
      }
      n5.textured = !(!a6 && !o5), n5.capType = g2, n5.joinType = _2, n5.miterLimit = V, n5.roundLimit = D, n5.halfWidth = 0.5 * I4, n5.offset = B2;
      for (const a7 of s5) {
        const s6 = r9 ? l4.encodeAttributes(a7, i4, t8) : null;
        p4 && (n5.capType = p4.getValue(i4, a7)), c5 && (n5.joinType = c5.getValue(i4, a7)), x2 && (n5.miterLimit = x2.getValue(i4, a7)), m3 && (n5.roundLimit = m3.getValue(i4, a7)), P && (n5.halfWidth = 0.5 * P.getValue(i4, a7)), L2 && (n5.offset = L2.getValue(i4, a7));
        const o6 = a7.getGeometry(e5);
        this._processFeature(o6, s6);
      }
    }
  }
  serialize() {
    let e5 = 6;
    e5 += this.layerUIDs.length, e5 += this.tessellationProperties._lineVertexBuffer.array.length, e5 += this.tessellationProperties._lineIndexBuffer.array.length, e5 += 3 * this._patternMap.size + 1;
    const t8 = new Uint32Array(e5), i4 = new Int32Array(t8.buffer);
    let s5 = 0;
    t8[s5++] = this.type, t8[s5++] = this.layerUIDs.length;
    for (let l4 = 0; l4 < this.layerUIDs.length; l4++) t8[s5++] = this.layerUIDs[l4];
    t8[s5++] = this._lineIndexStart, t8[s5++] = this._lineIndexCount;
    const n5 = this._patternMap, r9 = n5.size;
    if (t8[s5++] = r9, r9 > 0) for (const [l4, [a6, o5]] of n5) t8[s5++] = l4, t8[s5++] = a6, t8[s5++] = o5;
    t8[s5++] = this.tessellationProperties._lineVertexBuffer.array.length;
    for (let l4 = 0; l4 < this.tessellationProperties._lineVertexBuffer.array.length; l4++) i4[s5++] = this.tessellationProperties._lineVertexBuffer.array[l4];
    t8[s5++] = this.tessellationProperties._lineIndexBuffer.array.length;
    for (let l4 = 0; l4 < this.tessellationProperties._lineIndexBuffer.array.length; l4++) t8[s5++] = this.tessellationProperties._lineIndexBuffer.array[l4];
    return t8.buffer;
  }
  _processFeature(e5, t8, i4) {
    if (!e5) return;
    const s5 = e5.length;
    for (let n5 = 0; n5 < s5; n5++) this._processGeometry(e5[n5], t8, i4);
  }
  _processGeometry(e5, t8, i4) {
    if (e5.length < 2) return;
    const n5 = 1e-3;
    let r9, l4, a6 = e5[0], o5 = 1;
    for (; o5 < e5.length; ) r9 = e5[o5].x - a6.x, l4 = e5[o5].y - a6.y, r9 * r9 + l4 * l4 < n5 * n5 ? e5.splice(o5, 1) : (a6 = e5[o5], ++o5);
    if (e5.length < 2) return;
    const u4 = this.tessellationProperties._lineIndexBuffer, f3 = 3 * u4.index;
    this._tessellationOptions.initialDistance = 0, this._tessellationOptions.wrapDistance = s4, this.tessellationProperties._ddValues = t8, this._lineTessellator.tessellate(e5, this._tessellationOptions);
    const h5 = 3 * u4.index - f3;
    if (void 0 !== i4) {
      const e6 = this._patternMap, t9 = e6.get(i4);
      t9 ? t9[1] += h5 : e6.set(i4, [f3 + this._lineIndexCount, h5]);
    }
    this._lineIndexCount += h5;
  }
  _getDashArrayKey(e5, t8, i4, s5, n5, r9, l4) {
    const a6 = n5 ? r9.getValue(t8, e5) : l4, o5 = s5.getValue(t8, e5);
    return i4.getDashKey(o5, a6);
  }
};
var r8 = (e5) => (t8, i4, s5, n5, r9, l4, a6, o5, u4, f3, h5) => (e5._lineVertexBuffer.add(t8, i4, a6, o5, s5, n5, r9, l4, u4, f3, h5, e5._ddValues), e5._lineVertexBuffer.index - 1);
var l3 = (e5) => (t8, i4, s5) => {
  e5._lineIndexBuffer.add(t8, i4, s5);
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/buckets/SymbolBucket.js
var b2 = 10;
function A(e5, t8) {
  return e5.iconMosaicItem && t8.iconMosaicItem ? e5.iconMosaicItem.page === t8.iconMosaicItem.page ? 0 : e5.iconMosaicItem.page - t8.iconMosaicItem.page : e5.iconMosaicItem && !t8.iconMosaicItem ? 1 : !e5.iconMosaicItem && t8.iconMosaicItem ? -1 : 0;
}
var L = class _L extends t7 {
  constructor(e5, t8, n5, i4, s5, o5, r9, l4) {
    super(e5, t8, l4.getSpriteItems()), this.type = E.SYMBOL, this._markerMap = /* @__PURE__ */ new Map(), this._glyphMap = /* @__PURE__ */ new Map(), this._glyphBufferDataStorage = /* @__PURE__ */ new Map(), this._isIconSDF = false, this._iconVertexBuffer = n5, this._iconIndexBuffer = i4, this._textVertexBuffer = s5, this._textIndexBuffer = o5, this._placementEngine = r9, this._workerTileHandler = l4;
  }
  get markerPageMap() {
    return this._markerMap;
  }
  get glyphsPageMap() {
    return this._glyphMap;
  }
  get symbolInstances() {
    return this._symbolInstances;
  }
  getResources(e5, t8, i4) {
    const s5 = this.layer, a6 = this.zoom;
    e5 && e5.setExtent(this.layerExtent);
    const o5 = s5.getLayoutProperty("icon-image"), r9 = s5.getLayoutProperty("text-field");
    let l4 = s5.getLayoutProperty("text-transform"), h5 = s5.getLayoutProperty("text-font");
    const x2 = [];
    let c5, d, g2, f3;
    o5 && !o5.isDataDriven && (c5 = o5.getValue(a6)), r9 && !r9.isDataDriven && (d = r9.getValue(a6)), l4 && l4.isDataDriven || (g2 = s5.getLayoutValue("text-transform", a6), l4 = null), h5 && h5.isDataDriven || (f3 = s5.getLayoutValue("text-font", a6), h5 = null);
    for (const m3 of this._features) {
      const u4 = m3.getGeometry(e5);
      if (!u4 || 0 === u4.length) continue;
      let p4, _2;
      o5 && (p4 = o5.isDataDriven ? o5.getValue(a6, m3) : this._replaceKeys(c5, m3.values), p4 && t8(p4));
      let M = false;
      if (r9 && (_2 = r9.isDataDriven ? r9.getValue(a6, m3) : this._replaceKeys(d, m3.values), _2)) {
        switch (_2 = _2.replace(/\\n/g, "\n"), l4 && (g2 = l4.getValue(a6, m3)), g2) {
          case o.LOWERCASE:
            _2 = _2.toLowerCase();
            break;
          case o.UPPERCASE:
            _2 = _2.toUpperCase();
        }
        if (_L._bidiEngine.hasBidiChar(_2)) {
          let e7;
          e7 = "rtl" === _L._bidiEngine.checkContextual(_2) ? "IDNNN" : "ICNNN", _2 = _L._bidiEngine.bidiTransform(_2, e7, "VLYSN"), M = true;
        }
        const e6 = _2.length;
        if (e6 > 0) {
          h5 && (f3 = h5.getValue(a6, m3));
          for (const t9 of f3) {
            let n5 = i4[t9];
            n5 || (n5 = i4[t9] = /* @__PURE__ */ new Set());
            for (let t10 = 0; t10 < e6; t10++) {
              const e7 = _2.charCodeAt(t10);
              n5.add(e7);
            }
          }
        }
      }
      if (!p4 && !_2) continue;
      const P = s5.getLayoutValue("symbol-sort-key", a6, m3), I4 = { feature: m3, sprite: p4, label: _2, rtl: M, geometry: u4, hash: (_2 ? c(_2) : 0) ^ (p4 ? c(p4) : 0), priority: P, textFont: f3 };
      x2.push(I4);
    }
    this._symbolFeatures = x2;
  }
  processFeatures(e5) {
    e5 && e5.setExtent(this.layerExtent);
    const n5 = this.layer, s5 = this.zoom, a6 = n5.getLayoutValue("symbol-placement", s5), r9 = a6 !== n3.POINT, l4 = n5.getLayoutValue("symbol-spacing", s5) * p3, f3 = n5.getLayoutProperty("icon-image"), y2 = n5.getLayoutProperty("text-field"), b3 = f3 ? new T(n5, s5, r9) : null, V = y2 ? new m2(n5, s5, r9) : null, T4 = this._workerTileHandler;
    let w3;
    f3 && (w3 = T4.getSpriteItems()), this._iconIndexStart = 3 * this._iconIndexBuffer.index, this._textIndexStart = 3 * this._textIndexBuffer.index, this._iconIndexCount = 0, this._textIndexCount = 0, this._markerMap.clear(), this._glyphMap.clear();
    const B2 = [];
    let C2 = 1;
    V && V.size && (C2 = V.size / c4);
    const R = V ? V.maxAngle * e2 : 0, D = V ? V.size * p3 : 0;
    for (const o5 of this._symbolFeatures) {
      let e6;
      b3 && w3 && o5.sprite && (e6 = w3[o5.sprite], e6 && e6.sdf && (this._isIconSDF = true));
      let n6;
      !!e6 && b3.update(s5, o5.feature);
      let f4 = 0;
      const y3 = o5.label;
      if (y3) {
        i(V), V.update(s5, o5.feature);
        const e7 = r9 && V.rotationAlignment === l.MAP ? V.keepUpright : V.writingMode && V.writingMode.includes(p2.VERTICAL);
        let i4 = 0.5;
        switch (V.anchor) {
          case m.TOP_LEFT:
          case m.LEFT:
          case m.BOTTOM_LEFT:
            i4 = 0;
            break;
          case m.TOP_RIGHT:
          case m.RIGHT:
          case m.BOTTOM_RIGHT:
            i4 = 1;
        }
        let a7 = 0.5;
        switch (V.anchor) {
          case m.TOP_LEFT:
          case m.TOP:
          case m.TOP_RIGHT:
            a7 = 0;
            break;
          case m.BOTTOM_LEFT:
          case m.BOTTOM:
          case m.BOTTOM_RIGHT:
            a7 = 1;
        }
        let l5 = 0.5;
        switch (V.justify) {
          case u.AUTO:
            l5 = i4;
            break;
          case u.LEFT:
            l5 = 0;
            break;
          case u.RIGHT:
            l5 = 1;
        }
        const x2 = V.letterSpacing * c4, c5 = r9 ? 0 : V.maxWidth * c4, m3 = V.lineHeight * c4, P = o5.textFont.map((e8) => T4.getGlyphItems(e8));
        if (n6 = new a3(P, c5, m3, x2, i4, a7, l5).getShaping(y3, o5.rtl, e7), n6 && n6.length > 0) {
          let e8 = 1e30, t8 = -1e30;
          for (const i5 of n6) e8 = Math.min(e8, i5.x), t8 = Math.max(t8, i5.x);
          f4 = (t8 - e8 + 2 * c4) * C2 * p3;
        }
      }
      for (let t8 of o5.geometry) {
        const s6 = [];
        if (a6 === n3.LINE) {
          if ((n6 == null ? void 0 : n6.length) && (V == null ? void 0 : V.size)) {
            const e7 = V.size * p3 * (2 + Math.min(2, 4 * Math.abs(V.offset[1])));
            t8 = _L._smoothVertices(t8, e7);
          }
          _L._pushAnchors(s6, t8, l4, f4);
        } else a6 === n3.LINE_CENTER ? _L._pushCenterAnchor(s6, t8) : o5.feature.type === t2.Polygon ? _L._pushCentroid(s6, t8) : s6.push(new y(t8[0].x, t8[0].y));
        for (const i4 of s6) {
          if (i4.x < 0 || i4.x > x || i4.y < 0 || i4.y > x) continue;
          if (r9 && f4 > 0 && (V == null ? void 0 : V.rotationAlignment) === l.MAP && !_L._honorsTextMaxAngle(t8, i4, f4, R, D)) continue;
          const s7 = { shaping: n6, line: t8, iconMosaicItem: e6, anchor: i4, symbolFeature: o5, textColliders: [], iconColliders: [], textVertexRanges: [], iconVertexRanges: [] };
          B2.push(s7), this._processFeature(s7, b3, V);
        }
      }
    }
    B2.sort(A), this._addPlacedGlyphs(), this._symbolInstances = B2;
  }
  serialize() {
    let e5 = 11;
    e5 += this.layerUIDs.length, e5 += 3 * this.markerPageMap.size, e5 += 3 * this.glyphsPageMap.size, e5 += _L._symbolsSerializationLength(this._symbolInstances), e5 += this._iconVertexBuffer.array.length, e5 += this._iconIndexBuffer.array.length, e5 += this._textVertexBuffer.array.length, e5 += this._textIndexBuffer.array.length;
    const t8 = new Uint32Array(e5), n5 = new Int32Array(t8.buffer), i4 = new Float32Array(t8.buffer);
    let s5 = 0;
    t8[s5++] = this.type, t8[s5++] = this.layerUIDs.length;
    for (let a6 = 0; a6 < this.layerUIDs.length; a6++) t8[s5++] = this.layerUIDs[a6];
    t8[s5++] = this._isIconSDF ? 1 : 0, t8[s5++] = this.markerPageMap.size;
    for (const [a6, [o5, r9]] of this.markerPageMap) t8[s5++] = a6, t8[s5++] = o5, t8[s5++] = r9;
    t8[s5++] = this.glyphsPageMap.size;
    for (const [a6, [o5, r9]] of this.glyphsPageMap) t8[s5++] = a6, t8[s5++] = o5, t8[s5++] = r9;
    t8[s5++] = this._iconVertexBuffer.index / 4, t8[s5++] = this._textVertexBuffer.index / 4, s5 = _L.serializeSymbols(t8, n5, i4, s5, this._symbolInstances), t8[s5++] = this._iconVertexBuffer.array.length;
    for (let a6 = 0; a6 < this._iconVertexBuffer.array.length; a6++) n5[s5++] = this._iconVertexBuffer.array[a6];
    t8[s5++] = this._iconIndexBuffer.array.length;
    for (let a6 = 0; a6 < this._iconIndexBuffer.array.length; a6++) t8[s5++] = this._iconIndexBuffer.array[a6];
    t8[s5++] = this._textVertexBuffer.array.length;
    for (let a6 = 0; a6 < this._textVertexBuffer.array.length; a6++) n5[s5++] = this._textVertexBuffer.array[a6];
    t8[s5++] = this._textIndexBuffer.array.length;
    for (let a6 = 0; a6 < this._textIndexBuffer.array.length; a6++) t8[s5++] = this._textIndexBuffer.array[a6];
    return t8.buffer;
  }
  static _symbolsSerializationLength(e5) {
    let t8 = 0;
    t8 += 1;
    for (const n5 of e5 || []) {
      t8 += 4, t8 += 1;
      for (const e6 of n5.textColliders) t8 += b2;
      for (const e6 of n5.iconColliders) t8 += b2;
      t8 += 1, t8 += 2 * n5.textVertexRanges.length, t8 += 1, t8 += 2 * n5.iconVertexRanges.length;
    }
    return t8;
  }
  static serializeSymbols(e5, t8, n5, i4, s5) {
    s5 = s5 || [], t8[i4++] = s5.length;
    for (const a6 of s5) {
      t8[i4++] = a6.anchor.x, t8[i4++] = a6.anchor.y, t8[i4++] = a6.symbolFeature.hash, t8[i4++] = a6.symbolFeature.priority, t8[i4++] = a6.textColliders.length + a6.iconColliders.length;
      for (const e6 of a6.textColliders) t8[i4++] = e6.xTile, t8[i4++] = e6.yTile, t8[i4++] = e6.dxPixels, t8[i4++] = e6.dyPixels, t8[i4++] = e6.hard ? 1 : 0, t8[i4++] = e6.partIndex, n5[i4++] = e6.minLod, n5[i4++] = e6.maxLod, t8[i4++] = e6.width, t8[i4++] = e6.height;
      for (const e6 of a6.iconColliders) t8[i4++] = e6.xTile, t8[i4++] = e6.yTile, t8[i4++] = e6.dxPixels, t8[i4++] = e6.dyPixels, t8[i4++] = e6.hard ? 1 : 0, t8[i4++] = e6.partIndex, n5[i4++] = e6.minLod, n5[i4++] = e6.maxLod, t8[i4++] = e6.width, t8[i4++] = e6.height;
      t8[i4++] = a6.textVertexRanges.length;
      for (const [e6, n6] of a6.textVertexRanges) t8[i4++] = e6, t8[i4++] = n6;
      t8[i4++] = a6.iconVertexRanges.length;
      for (const [e6, n6] of a6.iconVertexRanges) t8[i4++] = e6, t8[i4++] = n6;
    }
    return i4;
  }
  _replaceKeys(e5, t8) {
    return e5.replace(/{([^{}]+)}/g, (e6, n5) => n5 in t8 ? t8[n5] : "");
  }
  _processFeature(e5, t8, n5) {
    const { line: i4, iconMosaicItem: s5, shaping: a6, anchor: o5 } = e5, l4 = this.zoom, h5 = this.layer, x2 = !!s5;
    let c5 = true;
    x2 && (c5 = (t8 == null ? void 0 : t8.optional) || !s5);
    const d = a6 && a6.length > 0, g2 = !d || (n5 == null ? void 0 : n5.optional);
    let f3, y2;
    if (x2 && (f3 = this._placementEngine.getIconPlacement(o5, s5, t8)), (f3 || c5) && (d && (y2 = this._placementEngine.getTextPlacement(o5, a6, i4, n5)), y2 || g2)) {
      if (f3 && y2 || (g2 || c5 ? g2 || y2 ? c5 || f3 || (y2 = null) : f3 = null : (f3 = null, y2 = null)), y2) {
        const t9 = h5.hasDataDrivenText ? h5.textMaterial.encodeAttributes(e5.symbolFeature.feature, l4, h5) : null;
        if (this._storePlacedGlyphs(e5, y2.shapes, l4, n5.rotationAlignment, t9), y2.textColliders) {
          e5.textColliders = y2.textColliders;
          for (const e6 of y2.textColliders) {
            e6.minLod = Math.max(l4 + N(e6.minLod), 0), e6.maxLod = Math.min(l4 + N(e6.maxLod), 25);
            const t10 = e6.angle;
            if (t10) {
              const n6 = Math.cos(t10), i5 = Math.sin(t10), s6 = e6.dxPixels * n6 - e6.dyPixels * i5, a7 = e6.dxPixels * i5 + e6.dyPixels * n6, o6 = (e6.dxPixels + e6.width) * n6 - e6.dyPixels * i5, r9 = (e6.dxPixels + e6.width) * i5 + e6.dyPixels * n6, l5 = e6.dxPixels * n6 - (e6.dyPixels + e6.height) * i5, h6 = e6.dxPixels * i5 + (e6.dyPixels + e6.height) * n6, x3 = (e6.dxPixels + e6.width) * n6 - (e6.dyPixels + e6.height) * i5, c6 = (e6.dxPixels + e6.width) * i5 + (e6.dyPixels + e6.height) * n6, d2 = Math.min(s6, o6, l5, x3), g3 = Math.max(s6, o6, l5, x3), f4 = Math.min(a7, r9, h6, c6), y3 = Math.max(a7, r9, h6, c6);
              e6.dxPixels = d2, e6.dyPixels = f4, e6.width = g3 - d2, e6.height = y3 - f4;
            }
          }
        }
      }
      if (f3) {
        const n6 = h5.hasDataDrivenIcon ? h5.iconMaterial.encodeAttributes(e5.symbolFeature.feature, l4, h5) : null;
        if (this._addPlacedIcons(e5, f3.shapes, l4, s5.page, t8.rotationAlignment === l.VIEWPORT, n6), f3.iconColliders) {
          e5.iconColliders = f3.iconColliders;
          for (const e6 of f3.iconColliders) {
            e6.minLod = Math.max(l4 + N(e6.minLod), 0), e6.maxLod = Math.min(l4 + N(e6.maxLod), 25);
            const t9 = e6.angle;
            if (t9) {
              const n7 = Math.cos(t9), i5 = Math.sin(t9), s6 = e6.dxPixels * n7 - e6.dyPixels * i5, a7 = e6.dxPixels * i5 + e6.dyPixels * n7, o6 = (e6.dxPixels + e6.width) * n7 - e6.dyPixels * i5, r9 = (e6.dxPixels + e6.width) * i5 + e6.dyPixels * n7, l5 = e6.dxPixels * n7 - (e6.dyPixels + e6.height) * i5, h6 = e6.dxPixels * i5 + (e6.dyPixels + e6.height) * n7, x3 = (e6.dxPixels + e6.width) * n7 - (e6.dyPixels + e6.height) * i5, c6 = (e6.dxPixels + e6.width) * i5 + (e6.dyPixels + e6.height) * n7, d2 = Math.min(s6, o6, l5, x3), g3 = Math.max(s6, o6, l5, x3), f4 = Math.min(a7, r9, h6, c6), y3 = Math.max(a7, r9, h6, c6);
              e6.dxPixels = d2, e6.dyPixels = f4, e6.width = g3 - d2, e6.height = y3 - f4;
            }
          }
        }
      }
    }
  }
  _addPlacedIcons(e5, t8, n5, i4, s5, a6) {
    const o5 = Math.max(n5 - 1, 0), l4 = this._iconVertexBuffer, h5 = this._iconIndexBuffer, x2 = this._markerMap;
    for (const c5 of t8) {
      const t9 = s5 ? 0 : Math.max(n5 + N(c5.minzoom), o5), d = s5 ? 25 : Math.min(n5 + N(c5.maxzoom), 25);
      if (d <= t9) continue;
      const g2 = c5.tl, f3 = c5.tr, y2 = c5.bl, m3 = c5.br, u4 = c5.mosaicRect, p4 = c5.labelAngle, _2 = c5.minAngle, M = c5.maxAngle, P = c5.anchor, I4 = l4.index, b3 = u4.x, A2 = u4.y, L2 = b3 + u4.width, V = A2 + u4.height, T4 = l4.index;
      l4.add(P.x, P.y, g2.x, g2.y, b3, A2, p4, _2, M, t9, d, a6), l4.add(P.x, P.y, f3.x, f3.y, L2, A2, p4, _2, M, t9, d, a6), l4.add(P.x, P.y, y2.x, y2.y, b3, V, p4, _2, M, t9, d, a6), l4.add(P.x, P.y, m3.x, m3.y, L2, V, p4, _2, M, t9, d, a6), e5.iconVertexRanges.length > 0 && e5.iconVertexRanges[0][0] + e5.iconVertexRanges[0][1] === T4 ? e5.iconVertexRanges[0][1] += 4 : e5.iconVertexRanges.push([T4, 4]), h5.add(I4 + 0, I4 + 1, I4 + 2), h5.add(I4 + 1, I4 + 2, I4 + 3), x2.has(i4) ? x2.get(i4)[1] += 6 : x2.set(i4, [this._iconIndexStart + this._iconIndexCount, 6]), this._iconIndexCount += 6;
    }
  }
  _addPlacedGlyphs() {
    const e5 = this._textVertexBuffer, t8 = this._textIndexBuffer, n5 = this._glyphMap;
    for (const [i4, s5] of this._glyphBufferDataStorage) for (const a6 of s5) {
      const s6 = e5.index, o5 = a6.symbolInstance, r9 = a6.ddAttributes, l4 = e5.index;
      e5.add(a6.glyphAnchor[0], a6.glyphAnchor[1], a6.tl[0], a6.tl[1], a6.xmin, a6.ymin, a6.labelAngle, a6.minAngle, a6.maxAngle, a6.minLod, a6.maxLod, r9), e5.add(a6.glyphAnchor[0], a6.glyphAnchor[1], a6.tr[0], a6.tr[1], a6.xmax, a6.ymin, a6.labelAngle, a6.minAngle, a6.maxAngle, a6.minLod, a6.maxLod, r9), e5.add(a6.glyphAnchor[0], a6.glyphAnchor[1], a6.bl[0], a6.bl[1], a6.xmin, a6.ymax, a6.labelAngle, a6.minAngle, a6.maxAngle, a6.minLod, a6.maxLod, r9), e5.add(a6.glyphAnchor[0], a6.glyphAnchor[1], a6.br[0], a6.br[1], a6.xmax, a6.ymax, a6.labelAngle, a6.minAngle, a6.maxAngle, a6.minLod, a6.maxLod, r9), o5.textVertexRanges.length > 0 && o5.textVertexRanges[0][0] + o5.textVertexRanges[0][1] === l4 ? o5.textVertexRanges[0][1] += 4 : o5.textVertexRanges.push([l4, 4]), t8.add(s6 + 0, s6 + 1, s6 + 2), t8.add(s6 + 1, s6 + 2, s6 + 3), n5.has(i4) ? n5.get(i4)[1] += 6 : n5.set(i4, [this._textIndexStart + this._textIndexCount, 6]), this._textIndexCount += 6;
    }
    this._glyphBufferDataStorage.clear();
  }
  _storePlacedGlyphs(e5, t8, n5, i4, s5) {
    const a6 = Math.max(n5 - 1, 0), o5 = i4 === l.VIEWPORT;
    let l4, h5, x2, c5, d, g2, f3, y2, m3, p4, _2;
    for (const u4 of t8) {
      if (l4 = o5 ? 0 : Math.max(n5 + N(u4.minzoom), a6), h5 = o5 ? 25 : Math.min(n5 + N(u4.maxzoom), 25), h5 <= l4) continue;
      x2 = u4.tl, c5 = u4.tr, d = u4.bl, g2 = u4.br, f3 = u4.labelAngle, y2 = u4.minAngle, m3 = u4.maxAngle, p4 = u4.anchor, _2 = u4.mosaicRect, this._glyphBufferDataStorage.has(u4.page) || this._glyphBufferDataStorage.set(u4.page, []);
      this._glyphBufferDataStorage.get(u4.page).push({ glyphAnchor: [p4.x, p4.y], tl: [x2.x, x2.y], tr: [c5.x, c5.y], bl: [d.x, d.y], br: [g2.x, g2.y], xmin: _2.x, ymin: _2.y, xmax: _2.x + _2.width, ymax: _2.y + _2.height, labelAngle: f3, minAngle: y2, maxAngle: m3, minLod: l4, maxLod: h5, placementLod: a6, symbolInstance: e5, ddAttributes: s5 });
    }
  }
  static _pushAnchors(e5, t8, n5, i4) {
    n5 += i4;
    let a6 = 0;
    const o5 = t8.length - 1;
    for (let l4 = 0; l4 < o5; l4++) a6 += h.distance(t8[l4], t8[l4 + 1]);
    let r9 = i4 || n5;
    if (r9 *= 0.5, a6 <= r9) return;
    const h5 = r9 / a6;
    let c5 = 0, d = -(n5 = a6 / Math.max(Math.round(a6 / n5), 1)) / 2;
    const g2 = t8.length - 1;
    for (let s5 = 0; s5 < g2; s5++) {
      const i5 = t8[s5], a7 = t8[s5 + 1], o6 = a7.x - i5.x, r10 = a7.y - i5.y, g3 = Math.sqrt(o6 * o6 + r10 * r10);
      let f3;
      for (; d + n5 < c5 + g3; ) {
        d += n5;
        const t9 = (d - c5) / g3, y2 = h2(i5.x, a7.x, t9), m3 = h2(i5.y, a7.y, t9);
        void 0 === f3 && (f3 = Math.atan2(r10, o6)), e5.push(new y(y2, m3, f3, s5, h5));
      }
      c5 += g3;
    }
  }
  static _pushCenterAnchor(e5, t8) {
    let n5 = 0;
    const i4 = t8.length - 1;
    for (let l4 = 0; l4 < i4; l4++) n5 += h.distance(t8[l4], t8[l4 + 1]);
    const a6 = n5 / 2;
    let o5 = 0;
    const r9 = t8.length - 1;
    for (let s5 = 0; s5 < r9; s5++) {
      const n6 = t8[s5], i5 = t8[s5 + 1], r10 = i5.x - n6.x, h5 = i5.y - n6.y, c5 = Math.sqrt(r10 * r10 + h5 * h5);
      if (a6 < o5 + c5) {
        const t9 = (a6 - o5) / c5, d = h2(n6.x, i5.x, t9), g2 = h2(n6.y, i5.y, t9), f3 = Math.atan2(h5, r10);
        return void e5.push(new y(d, g2, f3, s5, 0));
      }
      o5 += c5;
    }
  }
  static _deviation(e5, t8, n5) {
    const i4 = (t8.x - e5.x) * (n5.x - t8.x) + (t8.y - e5.y) * (n5.y - t8.y), s5 = (t8.x - e5.x) * (n5.y - t8.y) - (t8.y - e5.y) * (n5.x - t8.x);
    return Math.atan2(s5, i4);
  }
  static _honorsTextMaxAngle(e5, t8, n5, i4, a6) {
    let o5 = 0;
    const r9 = n5 / 2;
    let l4 = new h(t8.x, t8.y), h5 = t8.segment + 1;
    for (; o5 > -r9; ) {
      if (--h5, h5 < 0) return false;
      o5 -= h.distance(e5[h5], l4), l4 = e5[h5];
    }
    o5 += h.distance(e5[h5], e5[h5 + 1]);
    const x2 = [];
    let c5 = 0;
    const d = e5.length;
    for (; o5 < r9; ) {
      const t9 = e5[h5];
      let n6, r10 = h5;
      do {
        if (++r10, r10 === d) return false;
        n6 = e5[r10];
      } while (n6.isEqual(t9));
      let l5, g2 = r10;
      do {
        if (++g2, g2 === d) return false;
        l5 = e5[g2];
      } while (l5.isEqual(n6));
      const f3 = this._deviation(t9, n6, l5);
      for (x2.push({ deviation: f3, distToAnchor: o5 }), c5 += f3; o5 - x2[0].distToAnchor > a6; ) c5 -= x2.shift().deviation;
      if (Math.abs(c5) > i4) return false;
      o5 += h.distance(n6, l5), h5 = r10;
    }
    return true;
  }
  static _smoothVertices(e5, t8) {
    if (t8 <= 0) return e5;
    let n5 = e5.length;
    if (n5 < 3) return e5;
    const i4 = [];
    let a6 = 0, o5 = 0;
    i4.push(0);
    for (let y2 = 1; y2 < n5; y2++) {
      const t9 = h.distance(e5[y2], e5[y2 - 1]);
      t9 > 0 && (a6 += t9, i4.push(a6), o5++, o5 !== y2 && (e5[o5] = e5[y2]));
    }
    if (n5 = o5 + 1, n5 < 3) return e5;
    t8 = Math.min(t8, 0.2 * a6);
    const r9 = e5[0].x, l4 = e5[0].y, h5 = e5[n5 - 1].x, x2 = e5[n5 - 1].y, c5 = h.sub(e5[0], e5[1]);
    c5.normalize(), e5[0].x += t8 * c5.x, e5[0].y += t8 * c5.y, c5.assignSub(e5[n5 - 1], e5[n5 - 2]), c5.normalize(), e5[n5 - 1].x += t8 * c5.x, e5[n5 - 1].y += t8 * c5.y, i4[0] -= t8, i4[n5 - 1] += t8;
    const d = [];
    d.push(new h(r9, l4));
    const g2 = 1e-6, f3 = 0.5 * t8;
    for (let y2 = 1; y2 < n5 - 1; y2++) {
      let a7 = 0, o6 = 0, r10 = 0;
      for (let n6 = y2 - 1; n6 >= 0; n6--) {
        const s5 = f3 + i4[n6 + 1] - i4[y2];
        if (s5 < 0) break;
        const l5 = i4[n6 + 1] - i4[n6], h6 = i4[y2] - i4[n6] < f3 ? 1 : s5 / l5;
        if (h6 < g2) break;
        const x3 = h6 * h6, c6 = h6 * s5 - 0.5 * x3 * l5, d2 = h6 * l5 / t8, m3 = e5[n6 + 1], u4 = e5[n6].x - m3.x, p4 = e5[n6].y - m3.y;
        a7 += d2 / c6 * (m3.x * h6 * s5 + 0.5 * x3 * (s5 * u4 - l5 * m3.x) - x3 * h6 * l5 * u4 / 3), o6 += d2 / c6 * (m3.y * h6 * s5 + 0.5 * x3 * (s5 * p4 - l5 * m3.y) - x3 * h6 * l5 * p4 / 3), r10 += d2;
      }
      for (let s5 = y2 + 1; s5 < n5; s5++) {
        const n6 = f3 - i4[s5 - 1] + i4[y2];
        if (n6 < 0) break;
        const l5 = i4[s5] - i4[s5 - 1], h6 = i4[s5] - i4[y2] < f3 ? 1 : n6 / l5;
        if (h6 < g2) break;
        const x3 = h6 * h6, c6 = h6 * n6 - 0.5 * x3 * l5, d2 = h6 * l5 / t8, m3 = e5[s5 - 1], u4 = e5[s5].x - m3.x, p4 = e5[s5].y - m3.y;
        a7 += d2 / c6 * (m3.x * h6 * n6 + 0.5 * x3 * (n6 * u4 - l5 * m3.x) - x3 * h6 * l5 * u4 / 3), o6 += d2 / c6 * (m3.y * h6 * n6 + 0.5 * x3 * (n6 * p4 - l5 * m3.y) - x3 * h6 * l5 * p4 / 3), r10 += d2;
      }
      d.push(new h(a7 / r10, o6 / r10));
    }
    return d.push(new h(h5, x2)), e5[0].x = r9, e5[0].y = l4, e5[n5 - 1].x = h5, e5[n5 - 1].y = x2, d;
  }
  static _pushCentroid(e5, t8) {
    const n5 = 0, i4 = 0, s5 = 4096, a6 = 4096, o5 = t8.length - 1;
    let r9 = 0, l4 = 0, h5 = 0, c5 = t8[0].x, d = t8[0].y;
    c5 > s5 && (c5 = s5), c5 < n5 && (c5 = n5), d > a6 && (d = a6), d < i4 && (d = i4);
    for (let x2 = 1; x2 < o5; x2++) {
      let e6 = t8[x2].x, o6 = t8[x2].y, g2 = t8[x2 + 1].x, f3 = t8[x2 + 1].y;
      e6 > s5 && (e6 = s5), e6 < n5 && (e6 = n5), o6 > a6 && (o6 = a6), o6 < i4 && (o6 = i4), g2 > s5 && (g2 = s5), g2 < n5 && (g2 = n5), f3 > a6 && (f3 = a6), f3 < i4 && (f3 = i4);
      const y2 = (e6 - c5) * (f3 - d) - (g2 - c5) * (o6 - d);
      r9 += y2 * (c5 + e6 + g2), l4 += y2 * (d + o6 + f3), h5 += y2;
    }
    r9 /= 3 * h5, l4 /= 3 * h5, isNaN(r9) || isNaN(l4) || e5.push(new y(r9, l4));
  }
};
L._bidiEngine = new C();

// node_modules/@arcgis/core/views/2d/tiling/enums.js
var I3;
!function(I4) {
  I4[I4.INITIALIZED = 0] = "INITIALIZED", I4[I4.NO_DATA = 1] = "NO_DATA", I4[I4.READY = 2] = "READY", I4[I4.MODIFIED = 3] = "MODIFIED", I4[I4.INVALID = 4] = "INVALID";
}(I3 || (I3 = {}));

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/TileParser.js
var T3 = 8;
var g = 14;
var w2 = 5;
var B = class {
  constructor(t8, r9, o5, n5, c5) {
    var _a;
    if (this._pbfTiles = {}, this._tileClippers = {}, this._client = o5, this._tile = r9, c5) {
      this._styleLayerUIDs = /* @__PURE__ */ new Set();
      for (const e5 of c5) this._styleLayerUIDs.add(e5);
    }
    this._styleRepository = n5, this._layers = ((_a = this._styleRepository) == null ? void 0 : _a.layers) ?? [];
    const [l4, a6, u4] = r9.tileKey.split("/").map(parseFloat);
    this._level = l4;
    const f3 = T3 + Math.max((this._level - g) * w2, 0);
    for (const p4 of Object.keys(t8)) {
      const r10 = t8[p4];
      this._pbfTiles[p4] = new n(new Uint8Array(r10.protobuff), new DataView(r10.protobuff));
      if (r10.refKey) {
        const [e5] = r10.refKey.split("/").map(parseFloat), t9 = l4 - e5;
        if (t9 > 0) {
          const e6 = (1 << t9) - 1, r11 = a6 & e6, i4 = u4 & e6;
          this._tileClippers[p4] = new e(t9, r11, i4, 8, f3);
        }
      }
      this._tileClippers[p4] || (this._tileClippers[p4] = new _());
    }
  }
  _canParseStyleLayer(e5) {
    return !this._styleLayerUIDs || this._styleLayerUIDs.has(e5);
  }
  async parse(e5) {
    const t8 = i2(), s5 = this._initialize(e5), { returnedBuckets: i4 } = s5;
    this._processLayers(s5), this._linkReferences(s5), this._filterFeatures(s5);
    const o5 = [], n5 = /* @__PURE__ */ new Set(), c5 = (e6, t9) => {
      n5.has(e6) || (o5.push({ name: e6, repeat: t9 }), n5.add(e6));
    }, l4 = {};
    for (const r9 of i4) r9.getResources(r9.tileClipper, c5, l4);
    if (this._tile.status === I3.INVALID) return [];
    const a6 = this._fetchResources(o5, l4, e5);
    return Promise.all([...a6, t8]).then(() => this._processFeatures(s5.returnedBuckets));
  }
  _initialize(e5) {
    return { signal: e5 && e5.signal, sourceNameToTileData: this._parseTileData(this._pbfTiles), layers: this._layers, zoom: this._level, sourceNameToTileClipper: this._tileClippers, sourceNameToUniqueSourceLayerBuckets: {}, sourceNameToUniqueSourceLayers: {}, returnedBuckets: [], layerIdToBucket: {}, referencerUIDToReferencedId: /* @__PURE__ */ new Map() };
  }
  _processLayers(e5) {
    const { sourceNameToTileData: t8, layers: r9, zoom: s5, sourceNameToTileClipper: i4, sourceNameToUniqueSourceLayerBuckets: o5, sourceNameToUniqueSourceLayers: n5, returnedBuckets: c5, layerIdToBucket: l4, referencerUIDToReferencedId: a6 } = e5;
    for (let u4 = r9.length - 1; u4 >= 0; u4--) {
      const e6 = r9[u4];
      if (!this._canParseStyleLayer(e6.uid) || e6.minzoom && s5 < Math.floor(e6.minzoom) || e6.maxzoom && s5 >= e6.maxzoom || e6.type === a.BACKGROUND) continue;
      if (!t8[e6.source] || !i4[e6.source]) continue;
      const f3 = t8[e6.source], p4 = i4[e6.source], h5 = e6.sourceLayer, m3 = f3[h5];
      if (m3) {
        let t9 = n5[e6.source];
        if (t9 || (t9 = n5[e6.source] = /* @__PURE__ */ new Set()), t9.add(e6.sourceLayer), e6.refLayerId) a6.set(e6.uid, e6.refLayerId);
        else {
          const t10 = this._createBucket(e6);
          if (t10) {
            t10.layerUIDs = [e6.uid], t10.layerExtent = m3.extent, t10.tileClipper = p4;
            let r10 = o5[e6.source];
            r10 || (r10 = o5[e6.source] = {});
            let s6 = r10[h5];
            s6 || (s6 = r10[h5] = []), s6.push(t10), c5.push(t10), l4[e6.id] = t10;
          }
        }
      }
    }
  }
  _linkReferences(e5) {
    const { layerIdToBucket: t8, referencerUIDToReferencedId: r9 } = e5;
    r9.forEach((e6, r10) => {
      t8[e6] && t8[e6].layerUIDs.push(r10);
    });
  }
  _filterFeatures(e5) {
    const { signal: r9, sourceNameToTileData: s5, sourceNameToUniqueSourceLayerBuckets: i4, sourceNameToUniqueSourceLayers: n5 } = e5, c5 = 10 * this._level, l4 = 10 * (this._level + 1), a6 = [], u4 = [];
    for (const t8 of Object.keys(n5)) {
      n5[t8].forEach((e6) => {
        a6.push(e6), u4.push(t8);
      });
    }
    for (let f3 = 0; f3 < a6.length; f3++) {
      const e6 = u4[f3], n6 = a6[f3];
      if (!s5[e6] || !i4[e6]) continue;
      const p4 = s5[e6][n6], h5 = i4[e6][n6];
      if (!h5 || 0 === h5.length) continue;
      if (p(r9)) return;
      const m3 = p4.getData();
      for (; m3.nextTag(2); ) {
        const e7 = m3.getMessage(), t8 = new s2(e7, p4);
        e7.release();
        const r10 = t8.values;
        if (r10) {
          const e8 = r10._minzoom;
          if (e8 && e8 >= l4) continue;
          const t9 = r10._maxzoom;
          if (t9 && t9 <= c5) continue;
        }
        for (const s6 of h5) s6.pushFeature(t8);
      }
    }
  }
  _fetchResources(e5, t8, r9) {
    const s5 = [], i4 = this._tile.getWorkerTileHandler();
    let o5, n5;
    e5.length > 0 && (o5 = i4.fetchSprites(e5, this._client, r9), s5.push(o5));
    for (const c5 in t8) {
      const e6 = t8[c5];
      e6.size > 0 && (n5 = i4.fetchGlyphs(this._tile.tileKey, c5, e6, this._client, r9), s5.push(n5));
    }
    return s5;
  }
  _processFeatures(e5) {
    const t8 = e5.filter((e6) => e6.hasFeatures() || this._canParseStyleLayer(e6.layer.uid));
    for (const r9 of t8) r9.processFeatures(r9.tileClipper);
    return t8;
  }
  _parseTileData(e5) {
    const t8 = {};
    for (const r9 of Object.keys(e5)) {
      const s5 = e5[r9], i4 = {};
      for (; s5.next(); ) switch (s5.tag()) {
        case 3: {
          const e6 = s5.getMessage(), t9 = new e4(e6);
          e6.release(), i4[t9.name] = t9;
          break;
        }
        default:
          s5.skip();
      }
      t8[r9] = i4;
    }
    return t8;
  }
  _createBucket(e5) {
    switch (e5.type) {
      case a.BACKGROUND:
        return null;
      case a.FILL:
        return this._createFillBucket(e5);
      case a.LINE:
        return this._createLineBucket(e5);
      case a.CIRCLE:
        return this._createCircleBucket(e5);
      case a.SYMBOL:
        return this._createSymbolBucket(e5);
    }
  }
  _createFillBucket(e5) {
    return new s3(e5, this._level, this._tile.getWorkerTileHandler().getSpriteItems(), new r6(e5.fillMaterial.getStride()), new r5(), new u3(e5.outlineMaterial.getStride()), new r5());
  }
  _createLineBucket(e5) {
    return new n4(e5, this._level, this._tile.getWorkerTileHandler().getSpriteItems(), new o3(e5.lineMaterial.getStride()), new r5());
  }
  _createCircleBucket(e5) {
    return new r7(e5, this._level, this._tile.getWorkerTileHandler().getSpriteItems(), new a4(e5.circleMaterial.getStride()), new r5());
  }
  _createSymbolBucket(e5) {
    const t8 = this._tile;
    return new L(e5, this._level, new h4(e5.iconMaterial.getStride()), new r5(), new h4(e5.textMaterial.getStride()), new r5(), t8.placementEngine, t8.getWorkerTileHandler());
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/WorkerTile.js
var a5 = class {
  constructor(t8, e5, r9, a6) {
    this.status = I3.INITIALIZED, this.placementEngine = new b(), this.tileKey = t8, this.refKeys = e5, this._workerTileHandler = r9, this._styleRepository = a6;
  }
  release() {
    this.tileKey = "", this.refKeys = null, this.status = I3.INITIALIZED, this._workerTileHandler = null;
  }
  async parse(s5, r9) {
    const a6 = r9 && r9.signal;
    if (r(a6)) {
      const t8 = () => {
        a6.removeEventListener("abort", t8), this.status = I3.INVALID;
      };
      a6.addEventListener("abort", t8);
    }
    let n5;
    const l4 = { bucketsWithData: [], emptyBuckets: null };
    try {
      n5 = await this._parse(s5, r9);
    } catch (y2) {
      if (j(y2)) throw y2;
      return { result: l4, transferList: [] };
    }
    this.status = I3.READY;
    const o5 = l4.bucketsWithData, u4 = [];
    for (const t8 of n5) if (t8.hasFeatures()) {
      const e5 = t8.serialize();
      o5.push(e5);
    } else u4.push(t8.layer.uid);
    const h5 = [...o5];
    let c5 = null;
    return u4.length > 0 && (c5 = Uint32Array.from(u4), h5.push(c5.buffer)), l4.emptyBuckets = c5, { result: l4, transferList: h5 };
  }
  setObsolete() {
    this.status = I3.INVALID;
  }
  getLayers() {
    return this._workerTileHandler.getLayers();
  }
  getWorkerTileHandler() {
    return this._workerTileHandler;
  }
  async _parse(t8, e5) {
    const s5 = t8.sourceName2DataAndRefKey;
    if (0 === Object.keys(s5).length) return [];
    this.status = I3.MODIFIED;
    return new B(s5, this, e5.client, this._styleRepository, t8.styleLayerUIDs).parse(e5);
  }
};

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/WorkerTileHandler.js
var o4 = class {
  constructor() {
    this._spriteInfo = {}, this._glyphInfo = {};
  }
  reset() {
    return this._spriteInfo = {}, this._glyphInfo = {}, Promise.resolve();
  }
  getLayers() {
    var _a;
    return ((_a = this._styleRepository) == null ? void 0 : _a.layers) ?? [];
  }
  async createTileAndParse(t8, r9) {
    const { key: o5 } = t8, i4 = {};
    for (const e5 of Object.keys(t8.sourceName2DataAndRefKey)) {
      const s5 = t8.sourceName2DataAndRefKey[e5];
      i4[e5] = s5.refKey;
    }
    const n5 = new a5(o5, i4, this, this._styleRepository);
    try {
      return await n5.parse(t8, r9);
    } catch (l4) {
      if (n5.setObsolete(), n5.release(), !j(l4)) throw l4;
      return null;
    }
  }
  updateStyle(e5) {
    if (!e5 || 0 === e5.length || !this._styleRepository) return;
    const s5 = this._styleRepository;
    for (const r9 of e5) {
      const e6 = r9.type, o5 = r9.data;
      switch (e6) {
        case I.PAINTER_CHANGED:
          s5.setPaintProperties(o5.layer, o5.paint);
          break;
        case I.LAYOUT_CHANGED:
          s5.setLayoutProperties(o5.layer, o5.layout);
          break;
        case I.LAYER_REMOVED:
          s5.deleteStyleLayer(o5.layer);
          break;
        case I.LAYER_CHANGED:
          s5.setStyleLayer(o5.layer, o5.index);
          break;
        case I.SPRITES_CHANGED:
          this._spriteInfo = {};
      }
    }
  }
  setStyle(e5) {
    this._styleRepository = new l2(e5), this._spriteInfo = {}, this._glyphInfo = {};
  }
  fetchSprites(e5, t8, s5) {
    const r9 = [], o5 = this._spriteInfo;
    for (const i4 of e5) {
      void 0 === o5[i4.name] && r9.push(i4);
    }
    return 0 === r9.length ? Promise.resolve() : t8.invoke("getSprites", r9, { signal: s5 && s5.signal }).then((e6) => {
      for (const t9 in e6) {
        const s6 = e6[t9];
        o5[t9] = s6;
      }
    });
  }
  getSpriteItems() {
    return this._spriteInfo;
  }
  fetchGlyphs(e5, t8, s5, r9, o5) {
    const i4 = [];
    let n5 = this._glyphInfo[t8];
    return n5 ? s5.forEach((e6) => {
      n5[e6] || i4.push(e6);
    }) : (n5 = this._glyphInfo[t8] = [], s5.forEach((e6) => i4.push(e6))), 0 === i4.length ? Promise.resolve() : r9.invoke("getGlyphs", { tileID: e5, font: t8, codePoints: i4 }, o5).then((e6) => {
      for (let t9 = 0; t9 < e6.length; t9++) e6[t9] && (n5[t9] = e6[t9]);
    });
  }
  getGlyphItems(e5) {
    return this._glyphInfo[e5];
  }
};
export {
  o4 as default
};
//# sourceMappingURL=WorkerTileHandler-G3NKJYFE.js.map
