{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/tileUtils.js", "../../@arcgis/core/views/2d/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTiles2D.js", "../../@arcgis/core/views/3d/layers/support/FeatureTileDescriptor3D.js", "../../@arcgis/core/views/3d/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTiles3D.js", "../../@arcgis/core/views/support/TileTreeDebugger.js", "../../@arcgis/core/views/interactive/snapping/featureSources/WorkerTileTreeDebugger.js", "../../@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceSnappingSourceWorkerHandle.js", "../../@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTilesSimple.js", "../../@arcgis/core/views/interactive/snapping/featureSources/FeatureServiceSnappingSource.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{s}from\"../../../../../chunks/vec3.js\";import{c as n}from\"../../../../../chunks/vec4f64.js\";import{a as o,g as r,h as t}from\"../../../../../chunks/boundedPlane.js\";function c(n,o){return r(o.extent,e),t(e,s(a,n.x,n.y,0))}const e=o(),a=n();export{c as distanceToTile};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../../chunks/tslib.es6.js\";import t from\"../../../../../../core/Accessor.js\";import{isSome as r,isNone as i}from\"../../../../../../core/maybe.js\";import{watch as o,sync as s}from\"../../../../../../core/reactiveUtils.js\";import{property as l}from\"../../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../../core/arrayUtils.js\";import{subclass as n}from\"../../../../../../core/accessorSupport/decorators/subclass.js\";import{Tile<PERSON>ey as a}from\"../../../../../../layers/support/TileKey.js\";import{distanceToTile as p}from\"../../../../../interactive/snapping/featureSources/featureServiceSource/tileUtils.js\";import{scaleBoundsPredicate as c}from\"../../../../../support/layerViewUtils.js\";let u=class extends t{get tiles(){const e=this.tilesCoveringView,t=r(this.pointOfInterest)?this.pointOfInterest:this.view.center;return e.sort(((e,r)=>p(t,e)-p(t,r))),e}_scaleEnabled(){return c(this.view.scale,this.layer.minScale||0,this.layer.maxScale||0)}get tilesCoveringView(){if(!this.view.ready||!this.view.featuresTilingScheme||!this.view.state||i(this.tileInfo))return[];if(!this._scaleEnabled)return[];const{spans:e,lodInfo:t}=this.view.featuresTilingScheme.getTileCoverage(this.view.state,0),{level:r}=t,o=[];for(const{row:i,colFrom:s,colTo:l}of e)for(let e=s;e<=l;e++){const s=t.normalizeCol(e),l=new a(null,r,i,s);this.tileInfo.updateTileInfo(l),o.push(l)}return o}get tileInfo(){return this.view.featuresTilingScheme?.tileInfo??null}get tileSize(){return r(this.tileInfo)?this.tileInfo.size[0]:256}constructor(e){super(e),this.pointOfInterest=null}initialize(){this.addHandles(o((()=>this.view?.state?.viewpoint),(()=>this.notifyChange(\"tilesCoveringView\")),s))}};e([l({readOnly:!0})],u.prototype,\"tiles\",null),e([l({readOnly:!0})],u.prototype,\"_scaleEnabled\",null),e([l({readOnly:!0})],u.prototype,\"tilesCoveringView\",null),e([l({readOnly:!0})],u.prototype,\"tileInfo\",null),e([l({readOnly:!0})],u.prototype,\"tileSize\",null),e([l({constructOnly:!0})],u.prototype,\"view\",void 0),e([l({constructOnly:!0})],u.prototype,\"layer\",void 0),e([l()],u.prototype,\"pointOfInterest\",void 0),u=e([n(\"esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiles2D\")],u);export{u as FeatureServiceTiles2D};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{create as i,copy as e}from\"../../../../geometry/support/aaBoundingRect.js\";class t{constructor(e,t,h,r=null){this.lij=[0,0,0],this.extent=i(),this.resolution=0,this.loadPriority=0,this.measures={visibility:s.VISIBLE_ON_SURFACE,screenRect:i(),distance:0,shouldSplit:!1},this.used=!1,r&&this.acquire(e,t,h,r)}acquire(i,e,s,h){this.tilingScheme=h,this.id=t.id(i,e,s),this.lij[0]=i,this.lij[1]=e,this.lij[2]=s,h.getExtent(i,e,s,this.extent),this.resolution=h.resolutionAtLevel(i)}release(){this.tilingScheme=null}getChildren(i){const e=this.lij[0]+1,s=2*this.lij[1],h=2*this.lij[2];return i?(i[0].acquire(e,s,h,this.tilingScheme),i[1].acquire(e,s+1,h,this.tilingScheme),i[2].acquire(e,s,h+1,this.tilingScheme),i[3].acquire(e,s+1,h+1,this.tilingScheme),i):[new t(e,s,h,this.tilingScheme),new t(e,s+1,h,this.tilingScheme),new t(e,s,h+1,this.tilingScheme),new t(e,s+1,h+1,this.tilingScheme)]}copyMeasurementsFrom(i){this.measures.visibility=i.measures.visibility,this.measures.shouldSplit=i.measures.shouldSplit,this.measures.distance=i.measures.distance,e(i.measures.screenRect,this.measures.screenRect)}static id(i,e,t){return`${i}/${e}/${t}`}}var s;!function(i){i[i.INVISIBLE=0]=\"INVISIBLE\",i[i.VISIBLE_WHEN_EXTENDED=1]=\"VISIBLE_WHEN_EXTENDED\",i[i.VISIBLE_ON_SURFACE=2]=\"VISIBLE_ON_SURFACE\"}(s||(s={}));export{t as FeatureTileDescriptor3D,s as Visibility};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../../chunks/tslib.es6.js\";import{HandleOwner as t}from\"../../../../../../core/HandleOwner.js\";import{isSome as r,isNone as i}from\"../../../../../../core/maybe.js\";import{watch as s,initial as o}from\"../../../../../../core/reactiveUtils.js\";import{property as n}from\"../../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../../core/arrayUtils.js\";import{subclass as l}from\"../../../../../../core/accessorSupport/decorators/subclass.js\";import{TileKey as p}from\"../../../../../../layers/support/TileKey.js\";import{Visibility as a}from\"../../../../layers/support/FeatureTileDescriptor3D.js\";import{distanceToTile as c}from\"../../../../../interactive/snapping/featureSources/featureServiceSource/tileUtils.js\";let u=class extends t{get tiles(){const e=this.tilesCoveringView,t=this._effectivePointOfInterest;if(r(t)){const r=e.map((e=>c(t,e)));for(let i=1;i<r.length;i++)if(r[i-1]>r[i])return e.sort(((e,r)=>c(t,e)-c(t,r))),e.slice()}return e}get tilesCoveringView(){return this._filterTiles(this.view.featureTiles?.tiles?.toArray()).map(f)}get tileInfo(){return this.view.featureTiles?.tilingScheme.toTileInfo()??null}get tileSize(){return this.view.featureTiles?.tileSize??256}get _effectivePointOfInterest(){const e=this.pointOfInterest;return r(e)?e:this.view.pointsOfInterest?.focus.location}constructor(e){super(e),this.pointOfInterest=null}initialize(){this.handles.add(s((()=>this.view.featureTiles),(e=>{this.handles.remove(v),e&&this.handles.add(e.addClient(),v)}),o))}_filterTiles(e){if(i(e))return[];return e.filter((e=>Math.abs(e.measures.screenRect[3]-e.measures.screenRect[1])>m&&e.measures.visibility===a.VISIBLE_ON_SURFACE))}};function f({lij:[e,t,r],extent:i}){return new p(`${e}/${t}/${r}`,e,t,r,i)}e([n({readOnly:!0})],u.prototype,\"tiles\",null),e([n({readOnly:!0})],u.prototype,\"tilesCoveringView\",null),e([n({readOnly:!0})],u.prototype,\"tileInfo\",null),e([n({readOnly:!0})],u.prototype,\"tileSize\",null),e([n({constructOnly:!0})],u.prototype,\"view\",void 0),e([n()],u.prototype,\"pointOfInterest\",void 0),e([n()],u.prototype,\"_effectivePointOfInterest\",null),u=e([l(\"esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiles3D\")],u);const m=50,v=\"feature-tiles\";export{u as FeatureServiceTiles3D};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import s from\"../../Color.js\";import o from\"../../Graphic.js\";import t from\"../../core/Accessor.js\";import{lerp as r}from\"../../core/mathUtils.js\";import{isSome as l,isNone as i}from\"../../core/maybe.js\";import{property as a}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as n}from\"../../core/accessorSupport/decorators/subclass.js\";import h from\"../../symbols/PointSymbol3D.js\";import c from\"../../symbols/SimpleFillSymbol.js\";import p from\"../../symbols/TextSymbol.js\";import y from\"../../symbols/TextSymbol3DLayer.js\";const m=[[0,179,255],[117,62,128],[0,104,255],[215,189,166],[32,0,193],[98,162,206],[102,112,129],[52,125,0],[142,118,246],[138,83,0],[92,122,255],[122,55,83],[0,142,255],[81,40,179],[0,200,244],[13,24,127],[0,170,147],[19,58,241],[22,44,35]];let b=class extends t{constructor(e){super(e),this.updating=!1,this.enablePolygons=!0,this.enableLabels=!0,this._polygons=new Map,this._labels=new Map,this._enabled=!0}initialize(){this._symbols=m.map((e=>new c({color:[e[0],e[1],e[2],.6],outline:{color:\"black\",width:1}}))),this.update()}destroy(){this._enabled=!1,this.clear()}get enabled(){return this._enabled}set enabled(e){this._enabled!==e&&(this._enabled=e,this.update())}update(){if(!this._enabled)return void this.clear();const e=e=>{if(l(e.label))return e.label;let s=e.lij.toString();return l(e.loadPriority)&&(s+=` (${e.loadPriority})`),s},t=this.getTiles(),a=new Array,n=new Set((this._labels.size,this._labels.keys()));t.forEach(((c,m)=>{const b=c.lij.toString();n.delete(b);const d=c.lij[0],g=c.geometry;if(this.enablePolygons&&!this._polygons.has(b)){const e=new o({geometry:g,symbol:this._symbols[d%this._symbols.length]});this._polygons.set(b,e),a.push(e)}if(this.enableLabels){const n=e(c),d=m/(t.length-1),u=r(0,200,d),_=r(20,6,d)/.75,f=l(c.loadPriority)&&c.loadPriority>=t.length,w=new s([u,f?0:u,f?0:u]),j=\"3d\"===this.view.type?()=>new h({verticalOffset:{screenLength:40/.75},callout:{type:\"line\",color:\"white\",border:{color:\"black\"}},symbolLayers:[new y({text:n,halo:{color:\"white\",size:1/.75},material:{color:w},size:_})]}):()=>new p({text:n,haloColor:\"white\",haloSize:1/.75,color:w,size:_}),v=this._labels.get(b);if(v){const e=j();(i(v.symbol)||JSON.stringify(e)!==JSON.stringify(v.symbol))&&(v.symbol=e)}else{const e=new o({geometry:g.extent.center,symbol:j()});this._labels.set(b,e),a.push(e)}}}));const c=new Array;n.forEach((e=>{const s=this._polygons.get(e);null!=s&&(c.push(s),this._polygons.delete(e));const o=this._labels.get(e);null!=o&&(c.push(o),this._labels.delete(e))})),this.view.graphics.removeMany(c),this.view.graphics.addMany(a)}clear(){this.view.graphics.removeMany(Array.from(this._polygons.values())),this.view.graphics.removeMany(Array.from(this._labels.values())),this._polygons.clear(),this._labels.clear()}};e([a({constructOnly:!0})],b.prototype,\"view\",void 0),e([a({readOnly:!0})],b.prototype,\"updating\",void 0),e([a()],b.prototype,\"enabled\",null),b=e([n(\"esri.views.support.TileTreeDebugger\")],b);export{b as TileTreeDebugger};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import t from\"../../../../core/Handles.js\";import{makeHandle as r}from\"../../../../core/handleUtils.js\";import{property as o}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as s}from\"../../../../core/accessorSupport/decorators/subclass.js\";import a from\"../../../../geometry/Polygon.js\";import{toExtent as i}from\"../../../../geometry/support/aaBoundingRect.js\";import{TileTreeDebugger as n}from\"../../../support/TileTreeDebugger.js\";let d=class extends n{constructor(e){super(e),this._handles=new t}initialize(){const e=setInterval((()=>this._fetchDebugInfo()),2e3);this._handles.add(r((()=>clearInterval(e))))}destroy(){this._handles.destroy()}getTiles(){if(!this._debugInfo)return[];const e=new Map,t=new Map;this._debugInfo.storedTiles.forEach((t=>{e.set(t.data.id,t.featureCount)})),this._debugInfo.pendingTiles.forEach((r=>{e.set(r.data.id,r.featureCount),t.set(r.data.id,r.state)}));const r=r=>{const o=t.get(r),s=e.get(r)??\"?\";return o?`${o}:${s}\\n${r}`:`store:${s}\\n${r}`},o=new Map;return this._debugInfo.storedTiles.forEach((e=>{o.set(e.data.id,e.data)})),this._debugInfo.pendingTiles.forEach((e=>{o.set(e.data.id,e.data)})),Array.from(o.values()).map((e=>({lij:[e.level,e.row,e.col],geometry:a.fromExtent(i(e.extent,this.view.spatialReference)),label:r(e.id)})))}_fetchDebugInfo(){this.handle.getDebugInfo(null).then((e=>{this._debugInfo=e,this.update()}))}};e([o({constructOnly:!0})],d.prototype,\"handle\",void 0),d=e([s(\"esri.views.interactive.snapping.featureSources.WorkerTileTreeDebugger\")],d);export{d as WorkerTileTreeDebugger};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import{HandleOwner as t}from\"../../../../../core/HandleOwner.js\";import{isNone as i,isSome as o}from\"../../../../../core/maybe.js\";import{whenOrAbort as a}from\"../../../../../core/promiseUtils.js\";import{property as n}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as r}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{WorkerHandle as s}from\"../../../../../core/workers/WorkerHandle.js\";import{featureGeometryTypeKebabDictionary as d}from\"../../../../../geometry/support/typeUtils.js\";import{makeDehydratedPoint as l}from\"../../../../../layers/graphics/dehydratedFeatures.js\";let p=class extends t{get updating(){return this.updatingHandles.updating||this._workerHandleUpdating}constructor(e){super(e),this.schedule=null,this.hasZ=!1,this.elevationAlignPointsInFeatures=async e=>{const t=[];for(const{points:i}of e)for(const{z:e}of i)t.push(e);return{elevations:t,drapedObjectIds:new Set,failedObjectIds:new Set}},this.queryForSymbologySnapping=async()=>({candidates:[],sourceCandidateIndices:[]}),this.availability=0,this._workerHandleUpdating=!0,this._editId=0}destroy(){this._workerHandle.destroy()}initialize(){this._workerHandle=new c(this.schedule,{alignElevation:async(e,{signal:t})=>({result:await this.elevationAlignPointsInFeatures(e.points,t)}),getSymbologyCandidates:async(e,{signal:t})=>({result:await this.queryForSymbologySnapping(e,t)})}),this.handles.add([this._workerHandle.on(\"notify-updating\",(({updating:e})=>this._workerHandleUpdating=e)),this._workerHandle.on(\"notify-availability\",(({availability:e})=>this._set(\"availability\",e)))])}async setup(e,t){const o=this._serviceInfoFromLayer(e.layer);if(i(o))return;const a={configuration:this._convertConfiguration(e.configuration),serviceInfo:o,spatialReference:e.spatialReference.toJSON(),hasZ:this.hasZ,elevationInfo:e.layer.elevationInfo?.toJSON()};await this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"setup\",a,t)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"whenNotUpdating\",{},t))}async configure(e,t){const i=this._convertConfiguration(e);await this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"configure\",i,t)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"whenNotUpdating\",{},t))}async refresh(e){await this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"refresh\",{},e)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"whenNotUpdating\",{},e))}async fetchCandidates(e,t){const i=e.point,a={distance:e.distance,mode:e.mode,point:l(i[0],i[1],i[2],e.coordinateHelper.spatialReference.toJSON()),types:e.types,filter:o(e.filter)?e.filter.toJSON():null};return this._workerHandle.invoke(a,t)}async updateTiles(e,t){const i={tiles:e.tiles,tileInfo:o(e.tileInfo)?e.tileInfo.toJSON():null,tileSize:e.tileSize};await this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"updateTiles\",i,t)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"whenNotUpdating\",{},t))}async applyEdits(e,t){const i=this._editId++,n={id:i};await this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"beginApplyEdits\",n,t));const r=await this.updatingHandles.addPromise(a(e.result,t)),s={id:i,edits:{addedFeatures:r.addedFeatures?.map((({objectId:e})=>e)).filter(o)??[],deletedFeatures:r.deletedFeatures?.map((({objectId:e,globalId:t})=>({objectId:e,globalId:t})))??[],updatedFeatures:r.updatedFeatures?.map((({objectId:e})=>e)).filter(o)??[]}};await this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"endApplyEdits\",s,t)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod(\"whenNotUpdating\",{},t))}getDebugInfo(e){return this._workerHandle.invokeMethod(\"getDebugInfo\",{},e)}async notifyElevationSourceChange(){await this._workerHandle.invokeMethod(\"notifyElevationSourceChange\",{})}async notifySymbologyChange(){await this._workerHandle.invokeMethod(\"notifySymbologyChange\",{})}async setSymbologySnappingSupported(e){await this._workerHandle.invokeMethod(\"setSymbologySnappingSupported\",e)}_convertConfiguration(e){return{filter:o(e.filter)?e.filter.toJSON():null,customParameters:e.customParameters,viewType:e.viewType}}_serviceInfoFromLayer(e){return\"multipatch\"===e.geometryType||\"mesh\"===e.geometryType?null:{url:e.parsedUrl?.path??\"\",fields:e.fields.map((e=>e.toJSON())),geometryType:d.toJSON(e.geometryType),capabilities:e.capabilities,objectIdField:e.objectIdField,globalIdField:e.globalIdField,spatialReference:e.spatialReference.toJSON(),timeInfo:e.timeInfo?.toJSON()}}};e([n({constructOnly:!0})],p.prototype,\"schedule\",void 0),e([n({constructOnly:!0})],p.prototype,\"hasZ\",void 0),e([n({constructOnly:!0})],p.prototype,\"elevationAlignPointsInFeatures\",void 0),e([n({constructOnly:!0})],p.prototype,\"queryForSymbologySnapping\",void 0),e([n({readOnly:!0})],p.prototype,\"updating\",null),e([n({readOnly:!0})],p.prototype,\"availability\",void 0),e([n()],p.prototype,\"_workerHandleUpdating\",void 0),p=e([r(\"esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceSnappingSourceWorkerHandle\")],p);class c extends s{constructor(e,t){super(\"FeatureServiceSnappingSourceWorker\",\"fetchCandidates\",{},e,{strategy:\"dedicated\",client:t})}}export{p as FeatureServiceSnappingSourceWorkerHandle};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import r from\"../../../../../core/Accessor.js\";import{property as t}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as o}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import s from\"../../../../../geometry/Point.js\";import{fromValues as i}from\"../../../../../geometry/support/aaBoundingRect.js\";import p from\"../../../../../layers/support/LOD.js\";import l from\"../../../../../layers/support/TileInfo.js\";import{TileKey as n}from\"../../../../../layers/support/TileKey.js\";let a=class extends r{get tiles(){return[new n(\"0/0/0\",0,0,0,i(-1e8,-1e8,1e8,1e8))]}get tileInfo(){return new l({origin:new s({x:-1e8,y:1e8,spatialReference:this.layer.spatialReference}),size:[512,512],lods:[new p({level:0,scale:1,resolution:390625})],spatialReference:this.layer.spatialReference})}get tileSize(){return this.tileInfo.size[0]}constructor(e){super(e),this.pointOfInterest=null}};e([t({readOnly:!0})],a.prototype,\"tiles\",null),e([t({readOnly:!0})],a.prototype,\"tileInfo\",null),e([t({readOnly:!0})],a.prototype,\"tileSize\",null),e([t({constructOnly:!0})],a.prototype,\"layer\",void 0),e([t()],a.prototype,\"pointOfInterest\",void 0),a=e([o(\"esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTilesSimple\")],a);export{a as FeatureServiceTilesSimple};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import t from\"../../../../core/Accessor.js\";import{HandleOwnerMixin as r}from\"../../../../core/HandleOwner.js\";import{destroyHandle as i}from\"../../../../core/handleUtils.js\";import{isSome as s,applySome as o,mapOr as a,toNullable as n}from\"../../../../core/maybe.js\";import{throwIfAborted as l,ignoreAbortErrors as u}from\"../../../../core/promiseUtils.js\";import{watch as p,on as c,initial as d,sync as h}from\"../../../../core/reactiveUtils.js\";import{property as y}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as g}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{ObservableValue as f}from\"../../../../core/accessorSupport/tracking/ObservableValue.js\";import{elevationContextAffectsAlignment as m}from\"../../../../support/elevationInfoUtils.js\";import{FeatureServiceTiles2D as S}from\"../../../2d/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTiles2D.js\";import{FeatureServiceTiles3D as _}from\"../../../3d/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTiles3D.js\";import{convertSnappingCandidate as v,makeGetGroundElevation as w}from\"./queryEngineUtils.js\";import{WorkerTileTreeDebugger as b}from\"./WorkerTileTreeDebugger.js\";import{FeatureServiceSnappingSourceWorkerHandle as I}from\"./featureServiceSource/FeatureServiceSnappingSourceWorkerHandle.js\";import{FeatureServiceTilesSimple as j}from\"./featureServiceSource/FeatureServiceTilesSimple.js\";import O from\"../../../support/debugFlags.js\";let T=class extends(r(t)){get _updateTilesParameters(){return{tiles:this._tilesOfInterest.tiles,tileInfo:this._tilesOfInterest.tileInfo,tileSize:this._tilesOfInterest.tileSize}}get updating(){return this._workerHandle?.updating||this.updatingHandles.updating}get configuration(){const{view:e}=this,t=s(e)?e.type:\"2d\";return{filter:this._layer.createQuery(),customParameters:this._layer.customParameters,viewType:t}}get availability(){return this._workerHandle?.availability??0}get _layer(){return this.layerSource.layer}constructor(e){super(e),this._workerHandle=null,this._debug=null}initialize(){let e;const t=this.view;if(s(t))switch(t.type){case\"2d\":this._tilesOfInterest=new S({view:t,layer:this._layer}),e=this._workerHandle=new I;break;case\"3d\":{const{resourceController:r}=t,i=this._layer,s=t.whenLayerView(i);this._tilesOfInterest=new _({view:t}),e=this._workerHandle=new I({schedule:e=>r.immediate.schedule(e),hasZ:this._layer.hasZ&&(this._layer.returnZ??!0),elevationAlignPointsInFeatures:async(e,t)=>{const r=await s;return l(t),r.elevationAlignPointsInFeatures(e,t)},queryForSymbologySnapping:async(e,t)=>{const r=await s;return l(t),r.queryForSymbologySnapping(e,t)}});const h=new f(null);s.then((e=>h.set(e))),this.addHandles([t.elevationProvider.on(\"elevation-change\",(({context:t})=>{const{elevationInfo:r}=i;m(t,r)&&u(e.notifyElevationSourceChange())})),p((()=>i.elevationInfo),(()=>u(e.notifyElevationSourceChange())),d),p((()=>o(h.get(),(({processor:e})=>e?.renderer))),(()=>u(e.notifySymbologyChange())),d),p((()=>a(h.get(),!1,(e=>e.symbologySnappingSupported))),(t=>u(e.setSymbologySnappingSupported(t))),d),c((()=>n(h.get())?.layer),[\"edits\",\"apply-edits\",\"graphic-update\"],(()=>e.notifySymbologyChange()))]);break}}else this._tilesOfInterest=new j({layer:this._layer}),e=this._workerHandle=new I;this.handles.add([i(e)]),u(e.setup({layer:this._layer,spatialReference:this.spatialReference,configuration:this.configuration},null)),this.updatingHandles.add((()=>this._updateTilesParameters),(()=>u(e.updateTiles(this._updateTilesParameters,null))),d),this.handles.add([p((()=>this.configuration),(t=>u(e.configure(t,null))),h)]),s(t)&&this.handles.add(p((()=>O.FEATURE_SERVICE_SNAPPING_SOURCE_TILE_TREE_SHOW_TILES),(r=>{r&&!this._debug?(this._debug=new b({view:t,handle:e}),this.handles.add(i(this._debug),\"debug\")):!r&&this._debug&&this.handles.remove(\"debug\")}),d)),this.handles.add(this.layerSource.layer.on(\"apply-edits\",(t=>{u(e.applyEdits(t,null))})))}refresh(){this._workerHandle?.refresh(null)}async fetchCandidates(e,t){const{coordinateHelper:r,point:i}=e;this._tilesOfInterest.pointOfInterest=r.arrayToPoint(i);const s=this._getGroundElevation;return(await this._workerHandle.fetchCandidates({...e},t)).candidates.map((e=>v(e,s)))}getDebugInfo(e){return this._workerHandle.getDebugInfo(e)}get _getGroundElevation(){return w(this.view)}};e([y({constructOnly:!0})],T.prototype,\"spatialReference\",void 0),e([y({constructOnly:!0})],T.prototype,\"layerSource\",void 0),e([y({constructOnly:!0})],T.prototype,\"view\",void 0),e([y()],T.prototype,\"_tilesOfInterest\",void 0),e([y({readOnly:!0})],T.prototype,\"_updateTilesParameters\",null),e([y({readOnly:!0})],T.prototype,\"updating\",null),e([y({readOnly:!0})],T.prototype,\"configuration\",null),e([y({readOnly:!0})],T.prototype,\"availability\",null),e([y()],T.prototype,\"_getGroundElevation\",null),T=e([g(\"esri.views.interactive.snapping.featureSources.FeatureServiceSnappingSource\")],T);export{T as FeatureServiceSnappingSource};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0K,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAO,EAAEA,GAAE,QAAOC,EAAC,GAAE,GAAEA,IAAED,GAAEE,IAAEH,GAAE,GAAEA,GAAE,GAAE,CAAC,CAAC;AAAC;AAAC,IAAME,KAAE,EAAE;AAAV,IAAYC,KAAEH,GAAE;;;ACAgjB,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,UAAMI,KAAE,KAAK,mBAAkBC,KAAE,EAAE,KAAK,eAAe,IAAE,KAAK,kBAAgB,KAAK,KAAK;AAAO,WAAOD,GAAE,KAAM,CAACA,IAAEE,OAAI,EAAED,IAAED,EAAC,IAAE,EAAEC,IAAEC,EAAC,CAAE,GAAEF;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAOG,GAAE,KAAK,KAAK,OAAM,KAAK,MAAM,YAAU,GAAE,KAAK,MAAM,YAAU,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,oBAAmB;AAAC,QAAG,CAAC,KAAK,KAAK,SAAO,CAAC,KAAK,KAAK,wBAAsB,CAAC,KAAK,KAAK,SAAO,EAAE,KAAK,QAAQ,EAAE,QAAM,CAAC;AAAE,QAAG,CAAC,KAAK,cAAc,QAAM,CAAC;AAAE,UAAK,EAAC,OAAMH,IAAE,SAAQC,GAAC,IAAE,KAAK,KAAK,qBAAqB,gBAAgB,KAAK,KAAK,OAAM,CAAC,GAAE,EAAC,OAAMC,GAAC,IAAED,IAAEE,KAAE,CAAC;AAAE,eAAS,EAAC,KAAIC,IAAE,SAAQC,IAAE,OAAMC,GAAC,KAAIN,GAAE,UAAQA,KAAEK,IAAEL,MAAGM,IAAEN,MAAI;AAAC,YAAMK,KAAEJ,GAAE,aAAaD,EAAC,GAAEM,KAAE,IAAIL,GAAE,MAAKC,IAAEE,IAAEC,EAAC;AAAE,WAAK,SAAS,eAAeC,EAAC,GAAEH,GAAE,KAAKG,EAAC;AAAA,IAAC;AAAC,WAAOH;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAJt9C;AAIu9C,aAAO,UAAK,KAAK,yBAAV,mBAAgC,aAAU;AAAA,EAAI;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,EAAE,KAAK,QAAQ,IAAE,KAAK,SAAS,KAAK,CAAC,IAAE;AAAA,EAAG;AAAA,EAAC,YAAYH,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,kBAAgB;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,WAAW,EAAG,MAAE;AAJlqD;AAIoqD,8BAAK,SAAL,mBAAW,UAAX,mBAAkB;AAAA,OAAY,MAAI,KAAK,aAAa,mBAAmB,GAAG,CAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,2FAA2F,CAAC,GAAE,CAAC;;;ACA9nC,IAAI;AAAE,CAAC,SAASO,IAAE;AAAC,EAAAA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,wBAAsB,CAAC,IAAE,yBAAwBA,GAAEA,GAAE,qBAAmB,CAAC,IAAE;AAAoB,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACA7d,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,UAAMC,KAAE,KAAK,mBAAkBC,KAAE,KAAK;AAA0B,QAAG,EAAEA,EAAC,GAAE;AAAC,YAAMC,KAAEF,GAAE,IAAK,CAAAA,OAAG,EAAEC,IAAED,EAAC,CAAE;AAAE,eAAQG,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,KAAGD,GAAEC,KAAE,CAAC,IAAED,GAAEC,EAAC,EAAE,QAAOH,GAAE,KAAM,CAACA,IAAEE,OAAI,EAAED,IAAED,EAAC,IAAE,EAAEC,IAAEC,EAAC,CAAE,GAAEF,GAAE,MAAM;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,IAAI,oBAAmB;AAJ3jC;AAI4jC,WAAO,KAAK,cAAa,gBAAK,KAAK,iBAAV,mBAAwB,UAAxB,mBAA+B,SAAS,EAAE,IAAII,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAJppC;AAIqpC,aAAO,UAAK,KAAK,iBAAV,mBAAwB,aAAa,iBAAc;AAAA,EAAI;AAAA,EAAC,IAAI,WAAU;AAJluC;AAImuC,aAAO,UAAK,KAAK,iBAAV,mBAAwB,aAAU;AAAA,EAAG;AAAA,EAAC,IAAI,4BAA2B;AAJ/yC;AAIgzC,UAAMJ,KAAE,KAAK;AAAgB,WAAO,EAAEA,EAAC,IAAEA,MAAE,UAAK,KAAK,qBAAV,mBAA4B,MAAM;AAAA,EAAQ;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,kBAAgB;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,QAAQ,IAAI,EAAG,MAAI,KAAK,KAAK,cAAe,CAAAA,OAAG;AAAC,WAAK,QAAQ,OAAOK,EAAC,GAAEL,MAAG,KAAK,QAAQ,IAAIA,GAAE,UAAU,GAAEK,EAAC;AAAA,IAAC,GAAGC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaN,IAAE;AAAC,QAAG,EAAEA,EAAC,EAAE,QAAM,CAAC;AAAE,WAAOA,GAAE,OAAQ,CAAAA,OAAG,KAAK,IAAIA,GAAE,SAAS,WAAW,CAAC,IAAEA,GAAE,SAAS,WAAW,CAAC,CAAC,IAAEO,MAAGP,GAAE,SAAS,eAAa,EAAE,kBAAmB;AAAA,EAAC;AAAC;AAAE,SAASI,GAAE,EAAC,KAAI,CAACJ,IAAEC,IAAEC,EAAC,GAAE,QAAOC,GAAC,GAAE;AAAC,SAAO,IAAIF,GAAE,GAAGD,EAAC,IAAIC,EAAC,IAAIC,EAAC,IAAGF,IAAEC,IAAEC,IAAEC,EAAC;AAAC;AAAC,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEJ,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,6BAA4B,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,2FAA2F,CAAC,GAAEA,EAAC;AAAE,IAAMQ,KAAE;AAAR,IAAWF,KAAE;;;ACArmD,IAAMG,KAAE,CAAC,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,KAAI,IAAG,GAAG,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,IAAG,GAAE,GAAG,GAAE,CAAC,IAAG,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,IAAG,KAAI,GAAG,GAAE,CAAC,KAAI,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,GAAG,GAAE,CAAC,IAAG,IAAG,EAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,OAAG,KAAK,iBAAe,MAAG,KAAK,eAAa,MAAG,KAAK,YAAU,oBAAI,OAAI,KAAK,UAAQ,oBAAI,OAAI,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,aAAY;AAAC,SAAK,WAASD,GAAE,IAAK,CAAAC,OAAG,IAAI,EAAE,EAAC,OAAM,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,SAAQ,EAAC,OAAM,SAAQ,OAAM,EAAC,EAAC,CAAC,CAAE,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,OAAG,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,QAAQA,IAAE;AAAC,SAAK,aAAWA,OAAI,KAAK,WAASA,IAAE,KAAK,OAAO;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,QAAG,CAAC,KAAK,SAAS,QAAO,KAAK,KAAK,MAAM;AAAE,UAAMA,KAAE,CAAAA,OAAG;AAAC,UAAG,EAAEA,GAAE,KAAK,EAAE,QAAOA,GAAE;AAAM,UAAIC,KAAED,GAAE,IAAI,SAAS;AAAE,aAAO,EAAEA,GAAE,YAAY,MAAIC,MAAG,KAAKD,GAAE,YAAY,MAAKC;AAAA,IAAC,GAAEC,KAAE,KAAK,SAAS,GAAEC,KAAE,IAAI,SAAMC,KAAE,IAAI,KAAK,KAAK,QAAQ,MAAK,KAAK,QAAQ,KAAK,EAAE;AAAE,IAAAF,GAAE,QAAS,CAACG,IAAEN,OAAI;AAAC,YAAMO,KAAED,GAAE,IAAI,SAAS;AAAE,MAAAD,GAAE,OAAOE,EAAC;AAAE,YAAMC,KAAEF,GAAE,IAAI,CAAC,GAAEG,KAAEH,GAAE;AAAS,UAAG,KAAK,kBAAgB,CAAC,KAAK,UAAU,IAAIC,EAAC,GAAE;AAAC,cAAMN,KAAE,IAAIQ,GAAE,EAAC,UAASA,IAAE,QAAO,KAAK,SAASD,KAAE,KAAK,SAAS,MAAM,EAAC,CAAC;AAAE,aAAK,UAAU,IAAID,IAAEN,EAAC,GAAEG,GAAE,KAAKH,EAAC;AAAA,MAAC;AAAC,UAAG,KAAK,cAAa;AAAC,cAAMI,KAAEJ,GAAEK,EAAC,GAAEE,KAAER,MAAGG,GAAE,SAAO,IAAGO,KAAE,EAAE,GAAE,KAAIF,EAAC,GAAE,IAAE,EAAE,IAAG,GAAEA,EAAC,IAAE,MAAIG,KAAE,EAAEL,GAAE,YAAY,KAAGA,GAAE,gBAAcH,GAAE,QAAOS,KAAE,IAAIC,GAAE,CAACH,IAAEC,KAAE,IAAED,IAAEC,KAAE,IAAED,EAAC,CAAC,GAAEI,KAAE,SAAO,KAAK,KAAK,OAAK,MAAI,IAAIC,GAAE,EAAC,gBAAe,EAAC,cAAa,KAAG,KAAG,GAAE,SAAQ,EAAC,MAAK,QAAO,OAAM,SAAQ,QAAO,EAAC,OAAM,QAAO,EAAC,GAAE,cAAa,CAAC,IAAID,GAAE,EAAC,MAAKT,IAAE,MAAK,EAAC,OAAM,SAAQ,MAAK,IAAE,KAAG,GAAE,UAAS,EAAC,OAAMO,GAAC,GAAE,MAAK,EAAC,CAAC,CAAC,EAAC,CAAC,IAAE,MAAI,IAAI,EAAE,EAAC,MAAKP,IAAE,WAAU,SAAQ,UAAS,IAAE,MAAI,OAAMO,IAAE,MAAK,EAAC,CAAC,GAAEI,KAAE,KAAK,QAAQ,IAAIT,EAAC;AAAE,YAAGS,IAAE;AAAC,gBAAMf,KAAEa,GAAE;AAAE,WAAC,EAAEE,GAAE,MAAM,KAAG,KAAK,UAAUf,EAAC,MAAI,KAAK,UAAUe,GAAE,MAAM,OAAKA,GAAE,SAAOf;AAAA,QAAE,OAAK;AAAC,gBAAMA,KAAE,IAAIQ,GAAE,EAAC,UAASA,GAAE,OAAO,QAAO,QAAOK,GAAE,EAAC,CAAC;AAAE,eAAK,QAAQ,IAAIP,IAAEN,EAAC,GAAEG,GAAE,KAAKH,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAE,UAAMK,KAAE,IAAI;AAAM,IAAAD,GAAE,QAAS,CAAAJ,OAAG;AAAC,YAAMC,KAAE,KAAK,UAAU,IAAID,EAAC;AAAE,cAAMC,OAAII,GAAE,KAAKJ,EAAC,GAAE,KAAK,UAAU,OAAOD,EAAC;AAAG,YAAMgB,KAAE,KAAK,QAAQ,IAAIhB,EAAC;AAAE,cAAMgB,OAAIX,GAAE,KAAKW,EAAC,GAAE,KAAK,QAAQ,OAAOhB,EAAC;AAAA,IAAE,CAAE,GAAE,KAAK,KAAK,SAAS,WAAWK,EAAC,GAAE,KAAK,KAAK,SAAS,QAAQF,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAK,SAAS,WAAW,MAAM,KAAK,KAAK,UAAU,OAAO,CAAC,CAAC,GAAE,KAAK,KAAK,SAAS,WAAW,MAAM,KAAK,KAAK,QAAQ,OAAO,CAAC,CAAC,GAAE,KAAK,UAAU,MAAM,GAAE,KAAK,QAAQ,MAAM;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,GAAE,CAAC;;;ACAz8E,IAAIc,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,IAAIC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,UAAMD,KAAE,YAAa,MAAI,KAAK,gBAAgB,GAAG,GAAG;AAAE,SAAK,SAAS,IAAI,EAAG,MAAI,cAAcA,EAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAS,QAAQ;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,QAAG,CAAC,KAAK,WAAW,QAAM,CAAC;AAAE,UAAMA,KAAE,oBAAI,OAAIC,KAAE,oBAAI;AAAI,SAAK,WAAW,YAAY,QAAS,CAAAA,OAAG;AAAC,MAAAD,GAAE,IAAIC,GAAE,KAAK,IAAGA,GAAE,YAAY;AAAA,IAAC,CAAE,GAAE,KAAK,WAAW,aAAa,QAAS,CAAAC,OAAG;AAAC,MAAAF,GAAE,IAAIE,GAAE,KAAK,IAAGA,GAAE,YAAY,GAAED,GAAE,IAAIC,GAAE,KAAK,IAAGA,GAAE,KAAK;AAAA,IAAC,CAAE;AAAE,UAAMA,KAAE,CAAAA,OAAG;AAAC,YAAMC,KAAEF,GAAE,IAAIC,EAAC,GAAEE,KAAEJ,GAAE,IAAIE,EAAC,KAAG;AAAI,aAAOC,KAAE,GAAGA,EAAC,IAAIC,EAAC;AAAA,EAAKF,EAAC,KAAG,SAASE,EAAC;AAAA,EAAKF,EAAC;AAAA,IAAE,GAAEC,KAAE,oBAAI;AAAI,WAAO,KAAK,WAAW,YAAY,QAAS,CAAAH,OAAG;AAAC,MAAAG,GAAE,IAAIH,GAAE,KAAK,IAAGA,GAAE,IAAI;AAAA,IAAC,CAAE,GAAE,KAAK,WAAW,aAAa,QAAS,CAAAA,OAAG;AAAC,MAAAG,GAAE,IAAIH,GAAE,KAAK,IAAGA,GAAE,IAAI;AAAA,IAAC,CAAE,GAAE,MAAM,KAAKG,GAAE,OAAO,CAAC,EAAE,IAAK,CAAAH,QAAI,EAAC,KAAI,CAACA,GAAE,OAAMA,GAAE,KAAIA,GAAE,GAAG,GAAE,UAASK,GAAE,WAAWC,GAAEN,GAAE,QAAO,KAAK,KAAK,gBAAgB,CAAC,GAAE,OAAME,GAAEF,GAAE,EAAE,EAAC,EAAG;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,SAAK,OAAO,aAAa,IAAI,EAAE,KAAM,CAAAA,OAAG;AAAC,WAAK,aAAWA,IAAE,KAAK,OAAO;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAED,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,uEAAuE,CAAC,GAAEA,EAAC;;;ACA/3B,IAAIQ,KAAE,cAAc,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,gBAAgB,YAAU,KAAK;AAAA,EAAqB;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,MAAK,KAAK,OAAK,OAAG,KAAK,iCAA+B,OAAMA,OAAG;AAAC,YAAMC,KAAE,CAAC;AAAE,iBAAS,EAAC,QAAOC,GAAC,KAAIF,GAAE,YAAS,EAAC,GAAEA,GAAC,KAAIE,GAAE,CAAAD,GAAE,KAAKD,EAAC;AAAE,aAAM,EAAC,YAAWC,IAAE,iBAAgB,oBAAI,OAAI,iBAAgB,oBAAI,MAAG;AAAA,IAAC,GAAE,KAAK,4BAA0B,aAAU,EAAC,YAAW,CAAC,GAAE,wBAAuB,CAAC,EAAC,IAAG,KAAK,eAAa,GAAE,KAAK,wBAAsB,MAAG,KAAK,UAAQ;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,gBAAc,IAAIE,GAAE,KAAK,UAAS,EAAC,gBAAe,OAAMH,IAAE,EAAC,QAAOC,GAAC,OAAK,EAAC,QAAO,MAAM,KAAK,+BAA+BD,GAAE,QAAOC,EAAC,EAAC,IAAG,wBAAuB,OAAMD,IAAE,EAAC,QAAOC,GAAC,OAAK,EAAC,QAAO,MAAM,KAAK,0BAA0BD,IAAEC,EAAC,EAAC,GAAE,CAAC,GAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,cAAc,GAAG,mBAAmB,CAAC,EAAC,UAASD,GAAC,MAAI,KAAK,wBAAsBA,EAAE,GAAE,KAAK,cAAc,GAAG,uBAAuB,CAAC,EAAC,cAAaA,GAAC,MAAI,KAAK,KAAK,gBAAeA,EAAC,CAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,MAAMA,IAAEC,IAAE;AAJ1vD;AAI2vD,UAAMG,KAAE,KAAK,sBAAsBJ,GAAE,KAAK;AAAE,QAAG,EAAEI,EAAC,EAAE;AAAO,UAAMC,KAAE,EAAC,eAAc,KAAK,sBAAsBL,GAAE,aAAa,GAAE,aAAYI,IAAE,kBAAiBJ,GAAE,iBAAiB,OAAO,GAAE,MAAK,KAAK,MAAK,gBAAc,KAAAA,GAAE,MAAM,kBAAR,mBAAuB,SAAQ;AAAE,UAAM,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,SAAQK,IAAEJ,EAAC,CAAC,GAAE,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,mBAAkB,CAAC,GAAEA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,UAAUD,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,sBAAsBF,EAAC;AAAE,UAAM,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,aAAYE,IAAED,EAAC,CAAC,GAAE,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,mBAAkB,CAAC,GAAEA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQD,IAAE;AAAC,UAAM,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,WAAU,CAAC,GAAEA,EAAC,CAAC,GAAE,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,mBAAkB,CAAC,GAAEA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBA,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAE,OAAMK,KAAE,EAAC,UAASL,GAAE,UAAS,MAAKA,GAAE,MAAK,OAAM,EAAEE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEF,GAAE,iBAAiB,iBAAiB,OAAO,CAAC,GAAE,OAAMA,GAAE,OAAM,QAAO,EAAEA,GAAE,MAAM,IAAEA,GAAE,OAAO,OAAO,IAAE,KAAI;AAAE,WAAO,KAAK,cAAc,OAAOK,IAAEJ,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYD,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAC,OAAMF,GAAE,OAAM,UAAS,EAAEA,GAAE,QAAQ,IAAEA,GAAE,SAAS,OAAO,IAAE,MAAK,UAASA,GAAE,SAAQ;AAAE,UAAM,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,eAAcE,IAAED,EAAC,CAAC,GAAE,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,mBAAkB,CAAC,GAAEA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,WAAWD,IAAEC,IAAE;AAJznG;AAI0nG,UAAMC,KAAE,KAAK,WAAUI,KAAE,EAAC,IAAGJ,GAAC;AAAE,UAAM,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,mBAAkBI,IAAEL,EAAC,CAAC;AAAE,UAAMM,KAAE,MAAM,KAAK,gBAAgB,WAAWC,GAAER,GAAE,QAAOC,EAAC,CAAC,GAAEQ,KAAE,EAAC,IAAGP,IAAE,OAAM,EAAC,iBAAc,KAAAK,GAAE,kBAAF,mBAAiB,IAAK,CAAC,EAAC,UAASP,GAAC,MAAIA,IAAI,OAAO,OAAI,CAAC,GAAE,mBAAgB,KAAAO,GAAE,oBAAF,mBAAmB,IAAK,CAAC,EAAC,UAASP,IAAE,UAASC,GAAC,OAAK,EAAC,UAASD,IAAE,UAASC,GAAC,QAAM,CAAC,GAAE,mBAAgB,KAAAM,GAAE,oBAAF,mBAAmB,IAAK,CAAC,EAAC,UAASP,GAAC,MAAIA,IAAI,OAAO,OAAI,CAAC,EAAC,EAAC;AAAE,UAAM,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,iBAAgBS,IAAER,EAAC,CAAC,GAAE,KAAK,gBAAgB,WAAW,KAAK,cAAc,aAAa,mBAAkB,CAAC,GAAEA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAE;AAAC,WAAO,KAAK,cAAc,aAAa,gBAAe,CAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,8BAA6B;AAAC,UAAM,KAAK,cAAc,aAAa,+BAA8B,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAuB;AAAC,UAAM,KAAK,cAAc,aAAa,yBAAwB,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,8BAA8BA,IAAE;AAAC,UAAM,KAAK,cAAc,aAAa,iCAAgCA,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,WAAM,EAAC,QAAO,EAAEA,GAAE,MAAM,IAAEA,GAAE,OAAO,OAAO,IAAE,MAAK,kBAAiBA,GAAE,kBAAiB,UAASA,GAAE,SAAQ;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAJjxI;AAIkxI,WAAM,iBAAeA,GAAE,gBAAc,WAASA,GAAE,eAAa,OAAK,EAAC,OAAI,KAAAA,GAAE,cAAF,mBAAa,SAAM,IAAG,QAAOA,GAAE,OAAO,IAAK,CAAAA,OAAGA,GAAE,OAAO,CAAE,GAAE,cAAaI,GAAE,OAAOJ,GAAE,YAAY,GAAE,cAAaA,GAAE,cAAa,eAAcA,GAAE,eAAc,eAAcA,GAAE,eAAc,kBAAiBA,GAAE,iBAAiB,OAAO,GAAE,WAAS,KAAAA,GAAE,aAAF,mBAAY,SAAQ;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAED,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,kCAAiC,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,8GAA8G,CAAC,GAAEA,EAAC;AAAE,IAAMI,KAAN,cAAgBO,GAAC;AAAA,EAAC,YAAYV,IAAEC,IAAE;AAAC,UAAM,sCAAqC,mBAAkB,CAAC,GAAED,IAAE,EAAC,UAAS,aAAY,QAAOC,GAAC,CAAC;AAAA,EAAC;AAAC;;;ACAjmJ,IAAIU,KAAE,cAAc,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAM,CAAC,IAAIC,GAAE,SAAQ,GAAE,GAAE,GAAEC,GAAE,MAAK,MAAK,KAAI,GAAG,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,IAAI,EAAE,EAAC,QAAO,IAAI,EAAE,EAAC,GAAE,MAAK,GAAE,KAAI,kBAAiB,KAAK,MAAM,iBAAgB,CAAC,GAAE,MAAK,CAAC,KAAI,GAAG,GAAE,MAAK,CAAC,IAAIC,GAAE,EAAC,OAAM,GAAE,OAAM,GAAE,YAAW,OAAM,CAAC,CAAC,GAAE,kBAAiB,KAAK,MAAM,iBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,SAAS,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,kBAAgB;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEJ,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,+FAA+F,CAAC,GAAEA,EAAC;;;ACA2N,IAAI,IAAE,cAAcK,GAAE,CAAC,EAAE;AAAA,EAAC,IAAI,yBAAwB;AAAC,WAAM,EAAC,OAAM,KAAK,iBAAiB,OAAM,UAAS,KAAK,iBAAiB,UAAS,UAAS,KAAK,iBAAiB,SAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAJ3yD;AAI4yD,aAAO,UAAK,kBAAL,mBAAoB,aAAU,KAAK,gBAAgB;AAAA,EAAQ;AAAA,EAAC,IAAI,gBAAe;AAAC,UAAK,EAAC,MAAKC,GAAC,IAAE,MAAKC,KAAE,EAAED,EAAC,IAAEA,GAAE,OAAK;AAAK,WAAM,EAAC,QAAO,KAAK,OAAO,YAAY,GAAE,kBAAiB,KAAK,OAAO,kBAAiB,UAASC,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAJ7hE;AAI8hE,aAAO,UAAK,kBAAL,mBAAoB,iBAAc;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,YAAY;AAAA,EAAK;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,gBAAc,MAAK,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,QAAIA;AAAE,UAAMC,KAAE,KAAK;AAAK,QAAG,EAAEA,EAAC,EAAE,SAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAK,aAAK,mBAAiB,IAAI,EAAE,EAAC,MAAKA,IAAE,OAAM,KAAK,OAAM,CAAC,GAAED,KAAE,KAAK,gBAAc,IAAIE;AAAE;AAAA,MAAM,KAAI,MAAK;AAAC,cAAK,EAAC,oBAAmBC,GAAC,IAAEF,IAAEG,KAAE,KAAK,QAAOC,KAAEJ,GAAE,cAAcG,EAAC;AAAE,aAAK,mBAAiB,IAAIE,GAAE,EAAC,MAAKL,GAAC,CAAC,GAAED,KAAE,KAAK,gBAAc,IAAIE,GAAE,EAAC,UAAS,CAAAF,OAAGG,GAAE,UAAU,SAASH,EAAC,GAAE,MAAK,KAAK,OAAO,SAAO,KAAK,OAAO,WAAS,OAAI,gCAA+B,OAAMA,IAAEC,OAAI;AAAC,gBAAME,KAAE,MAAME;AAAE,iBAAOE,GAAEN,EAAC,GAAEE,GAAE,+BAA+BH,IAAEC,EAAC;AAAA,QAAC,GAAE,2BAA0B,OAAMD,IAAEC,OAAI;AAAC,gBAAME,KAAE,MAAME;AAAE,iBAAOE,GAAEN,EAAC,GAAEE,GAAE,0BAA0BH,IAAEC,EAAC;AAAA,QAAC,EAAC,CAAC;AAAE,cAAMO,KAAE,IAAIP,GAAE,IAAI;AAAE,QAAAI,GAAE,KAAM,CAAAL,OAAGQ,GAAE,IAAIR,EAAC,CAAE,GAAE,KAAK,WAAW,CAACC,GAAE,kBAAkB,GAAG,oBAAoB,CAAC,EAAC,SAAQA,GAAC,MAAI;AAAC,gBAAK,EAAC,eAAcE,GAAC,IAAEC;AAAE,UAAAK,GAAER,IAAEE,EAAC,KAAGO,GAAEV,GAAE,4BAA4B,CAAC;AAAA,QAAC,CAAE,GAAE,EAAG,MAAII,GAAE,eAAgB,MAAIM,GAAEV,GAAE,4BAA4B,CAAC,GAAGQ,EAAC,GAAE,EAAG,MAAI,EAAEA,GAAE,IAAI,GAAG,CAAC,EAAC,WAAUR,GAAC,MAAIA,MAAA,gBAAAA,GAAG,QAAS,GAAI,MAAIU,GAAEV,GAAE,sBAAsB,CAAC,GAAGQ,EAAC,GAAE,EAAG,MAAI,EAAEA,GAAE,IAAI,GAAE,OAAI,CAAAR,OAAGA,GAAE,0BAA2B,GAAI,CAAAC,OAAGS,GAAEV,GAAE,8BAA8BC,EAAC,CAAC,GAAGO,EAAC,GAAET,GAAG,MAAE;AAJ/sG;AAIitG,yBAAES,GAAE,IAAI,CAAC,MAAT,mBAAY;AAAA,WAAO,CAAC,SAAQ,eAAc,gBAAgB,GAAG,MAAIR,GAAE,sBAAsB,CAAE,CAAC,CAAC;AAAE;AAAA,MAAK;AAAA,IAAC;AAAA,QAAM,MAAK,mBAAiB,IAAID,GAAE,EAAC,OAAM,KAAK,OAAM,CAAC,GAAEC,KAAE,KAAK,gBAAc,IAAIE;AAAE,SAAK,QAAQ,IAAI,CAACD,GAAED,EAAC,CAAC,CAAC,GAAEU,GAAEV,GAAE,MAAM,EAAC,OAAM,KAAK,QAAO,kBAAiB,KAAK,kBAAiB,eAAc,KAAK,cAAa,GAAE,IAAI,CAAC,GAAE,KAAK,gBAAgB,IAAK,MAAI,KAAK,wBAAyB,MAAIU,GAAEV,GAAE,YAAY,KAAK,wBAAuB,IAAI,CAAC,GAAGQ,EAAC,GAAE,KAAK,QAAQ,IAAI,CAAC,EAAG,MAAI,KAAK,eAAgB,CAAAP,OAAGS,GAAEV,GAAE,UAAUC,IAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAE,EAAEA,EAAC,KAAG,KAAK,QAAQ,IAAI,EAAG,MAAI,EAAE,sDAAuD,CAAAE,OAAG;AAAC,MAAAA,MAAG,CAAC,KAAK,UAAQ,KAAK,SAAO,IAAIQ,GAAE,EAAC,MAAKV,IAAE,QAAOD,GAAC,CAAC,GAAE,KAAK,QAAQ,IAAIC,GAAE,KAAK,MAAM,GAAE,OAAO,KAAG,CAACE,MAAG,KAAK,UAAQ,KAAK,QAAQ,OAAO,OAAO;AAAA,IAAC,GAAGK,EAAC,CAAC,GAAE,KAAK,QAAQ,IAAI,KAAK,YAAY,MAAM,GAAG,eAAe,CAAAP,OAAG;AAAC,MAAAS,GAAEV,GAAE,WAAWC,IAAE,IAAI,CAAC;AAAA,IAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJriI;AAIsiI,eAAK,kBAAL,mBAAoB,QAAQ;AAAA,EAAK;AAAA,EAAC,MAAM,gBAAgBD,IAAEC,IAAE;AAAC,UAAK,EAAC,kBAAiBE,IAAE,OAAMC,GAAC,IAAEJ;AAAE,SAAK,iBAAiB,kBAAgBG,GAAE,aAAaC,EAAC;AAAE,UAAMC,KAAE,KAAK;AAAoB,YAAO,MAAM,KAAK,cAAc,gBAAgB,EAAC,GAAGL,GAAC,GAAEC,EAAC,GAAG,WAAW,IAAK,CAAAD,OAAG,EAAEA,IAAEK,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAaL,IAAE;AAAC,WAAO,KAAK,cAAc,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAOE,GAAE,KAAK,IAAI;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,0BAAyB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,uBAAsB,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,6EAA6E,CAAC,GAAE,CAAC;", "names": ["n", "o", "e", "a", "e", "t", "r", "o", "i", "s", "l", "i", "u", "e", "t", "r", "i", "f", "v", "h", "m", "m", "e", "s", "t", "a", "n", "c", "b", "d", "g", "u", "f", "w", "l", "j", "h", "v", "o", "d", "e", "t", "r", "o", "s", "v", "f", "p", "e", "t", "i", "c", "o", "a", "n", "r", "y", "s", "h", "a", "t", "o", "p", "e", "a", "e", "t", "p", "r", "i", "s", "u", "f", "h", "y", "g", "d"]}