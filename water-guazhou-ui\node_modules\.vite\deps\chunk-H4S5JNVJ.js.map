{"version": 3, "sources": ["../../@arcgis/core/core/accessorSupport/decorators/reader.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{propertyJSONMeta as r}from\"./property.js\";function o(o,e,t){let a,c;return void 0===e||Array.isArray(e)?(c=o,t=e,a=[void 0]):(c=e,a=Array.isArray(o)?o:[o]),(o,e)=>{const d=o.constructor.prototype;a.forEach((a=>{const s=r(o,a,c);s.read&&\"object\"==typeof s.read||(s.read={}),s.read.reader=d[e],t&&(s.read.source=(s.read.source||[]).concat(t))}))}}export{o as reader};\n"], "mappings": ";;;;;AAIiD,SAAS,EAAEA,IAAE,GAAE,GAAE;AAAC,MAAI,GAAE;AAAE,SAAO,WAAS,KAAG,MAAM,QAAQ,CAAC,KAAG,IAAEA,IAAE,IAAE,GAAE,IAAE,CAAC,MAAM,MAAI,IAAE,GAAE,IAAE,MAAM,QAAQA,EAAC,IAAEA,KAAE,CAACA,EAAC,IAAG,CAACA,IAAEC,OAAI;AAAC,UAAMC,KAAEF,GAAE,YAAY;AAAU,MAAE,QAAS,CAAAG,OAAG;AAAC,YAAM,IAAE,EAAEH,IAAEG,IAAE,CAAC;AAAE,QAAE,QAAM,YAAU,OAAO,EAAE,SAAO,EAAE,OAAK,CAAC,IAAG,EAAE,KAAK,SAAOD,GAAED,EAAC,GAAE,MAAI,EAAE,KAAK,UAAQ,EAAE,KAAK,UAAQ,CAAC,GAAG,OAAO,CAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAC;", "names": ["o", "e", "d", "a"]}