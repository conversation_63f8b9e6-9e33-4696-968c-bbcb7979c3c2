{"version": 3, "sources": ["../../@arcgis/core/renderers/support/styleUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{result as e}from\"../../core/asyncUtils.js\";import{throwIfAborted as r}from\"../../core/promiseUtils.js\";import o from\"../../core/Warning.js\";async function t(t,i,n){const s=t&&t.getAtOrigin&&t.getAtOrigin(\"renderer\",i.origin);if(s&&\"unique-value\"===s.type&&s.styleOrigin){const a=await e(s.populateFromStyle());if(r(n),!1===a.ok){const e=a.error;i&&i.messages&&i.messages.push(new o(\"renderer:style-reference\",`Failed to create unique value renderer from style reference: ${e.message}`,{error:e,context:i})),t.clear(\"renderer\",i?.origin)}}}export{t as loadStyleRenderer};\n"], "mappings": ";;;;;;;;;;;AAImJ,eAAeA,GAAEA,IAAE,GAAE,GAAE;AAAC,QAAM,IAAEA,MAAGA,GAAE,eAAaA,GAAE,YAAY,YAAW,EAAE,MAAM;AAAE,MAAG,KAAG,mBAAiB,EAAE,QAAM,EAAE,aAAY;AAAC,UAAM,IAAE,MAAM,EAAE,EAAE,kBAAkB,CAAC;AAAE,QAAG,EAAE,CAAC,GAAE,UAAK,EAAE,IAAG;AAAC,YAAM,IAAE,EAAE;AAAM,WAAG,EAAE,YAAU,EAAE,SAAS,KAAK,IAAI,EAAE,4BAA2B,gEAAgE,EAAE,OAAO,IAAG,EAAC,OAAM,GAAE,SAAQ,EAAC,CAAC,CAAC,GAAEA,GAAE,MAAM,YAAW,uBAAG,MAAM;AAAA,IAAC;AAAA,EAAC;AAAC;", "names": ["t"]}