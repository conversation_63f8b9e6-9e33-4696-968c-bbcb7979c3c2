import {
  s as s2
} from "./chunk-5ZZCQR67.js";
import {
  s
} from "./chunk-P2G4OGHI.js";
import {
  m
} from "./chunk-RFYOGM4H.js";
import {
  A2 as A,
  J,
  K,
  N,
  O2 as O,
  R3 as R,
  T2 as T
} from "./chunk-JXLVNWKF.js";
import {
  a
} from "./chunk-EIGTETCG.js";
import {
  r
} from "./chunk-LTKA6OKA.js";
import {
  l
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/unitFormatUtils.js
function l2(t, n, r2) {
  return t.units[n][r2];
}
function g(t, n, r2, i = 2, e = "abbr") {
  return `${m(n, { minimumFractionDigits: i, maximumFractionDigits: i, signDisplay: n > 0 ? "never" : "exceptZero" })} ${l2(t, r2, e)}`;
}
function p(t, n, r2, i = 2, e = "abbr") {
  return `${m(n, { minimumFractionDigits: i, maximumFractionDigits: i, signDisplay: "exceptZero" })} ${l2(t, r2, e)}`;
}
function D(t, n, r2, i = 2, e = "abbr") {
  const s3 = O(n, r2);
  return g(t, N(n, r2, s3), s3, i, e);
}
function x(t, n, r2, i = 2, e = "abbr") {
  const s3 = O(n, r2);
  return p(t, N(n, r2, s3), s3, i, e);
}
function y(t, n, r2, i = 2, e = "abbr") {
  const o = J(n, r2);
  return g(t, N(n, r2, o), o, i, e);
}
function F(t, n, r2, i = 2, e = "abbr") {
  const o = J(n, r2);
  return p(t, N(n, r2, o), o, i, e);
}
function h(t, n, r2, i = 2, e = "abbr") {
  const o = K(n, r2);
  return g(t, N(n, r2, o), o, i, e);
}
function d(t, n, r2, i = 2, e = "abbr") {
  const o = K(n, r2);
  return p(t, N(n, r2, o), o, i, e);
}
function j(t, n, r2, i = 2, e = "abbr") {
  const o = R(n, r2);
  return g(t, N(n, r2, o), o, i, e);
}
function B(t, n, r2, i = 2, e = "abbr") {
  const o = R(n, r2);
  return p(t, N(n, r2, o), o, i, e);
}
function M(t, n, r2, i = 2, e = "abbr") {
  const o = A(n, r2);
  return g(t, N(n, r2, o), o, i, e);
}
function $(t, n, r2, i = 2, e = "abbr") {
  const o = T(n, r2);
  return g(t, N(n, r2, o), o, i, e);
}
function w(t, r2, e, o, s3) {
  s3 = l(s3, 2);
  let c = s2.normalize(I(N(t, r2, "degrees"), e, o), 0, true);
  const a2 = { style: "unit", unitDisplay: "narrow", unit: "degree", maximumFractionDigits: s3, minimumFractionDigits: s3, signDisplay: c > 0 ? "never" : "exceptZero" };
  return c = E(c, a2), m(c, a2);
}
function Z(t, n, r2, e, o) {
  r2 !== e && (t = -t);
  const s3 = { style: "unit", unitDisplay: "narrow", unit: "degree", maximumFractionDigits: o = l(o, 2), minimumFractionDigits: o, signDisplay: "exceptZero" };
  let c = N(t, n, "degrees") % 360;
  return c = E(c, s3), m(c, s3);
}
var z = /* @__PURE__ */ new Map();
function E(t, n) {
  const r2 = JSON.stringify(n);
  let i = z.get(r2);
  return i || (i = new Intl.NumberFormat("en-US", n), z.set(r2, i)), /\-?\+?360/.test(i.format(t)) ? 0 : t;
}
function I(t, n, r2) {
  if (n === r2) return t;
  switch (r2) {
    case "arithmetic":
      return O2(t);
    case "geographic":
      return T2(t);
  }
}
function O2(t) {
  return 90 - t;
}
function T2(t) {
  return -t - 90;
}
var U = ["B", "kB", "MB", "GB", "TB"];
function v(n, i) {
  let o = 0 === i ? 0 : Math.floor(Math.log(i) / Math.log(s.KILOBYTES));
  o = a(o, 0, U.length - 1);
  const u = m(i / s.KILOBYTES ** o, { maximumFractionDigits: 2 });
  return r(n.units.bytes[U[o]], { fileSize: u });
}

export {
  g,
  p,
  D,
  x,
  y,
  F,
  h,
  d,
  j,
  B,
  M,
  $,
  w,
  Z,
  v
};
//# sourceMappingURL=chunk-OY3C7FMJ.js.map
