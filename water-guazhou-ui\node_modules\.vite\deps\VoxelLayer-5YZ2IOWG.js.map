{"version": 3, "sources": ["../../@arcgis/core/layers/voxel/voxelPlaneUtils.js", "../../@arcgis/core/layers/voxel/VoxelSlice.js", "../../@arcgis/core/layers/voxel/VoxelSection.js", "../../@arcgis/core/layers/voxel/VoxelSimpleShading.js", "../../@arcgis/core/layers/voxel/VoxelFormat.js", "../../@arcgis/core/layers/voxel/VoxelVariable.js", "../../@arcgis/core/layers/voxel/VoxelIsosurface.js", "../../@arcgis/core/layers/voxel/VoxelColorStop.js", "../../@arcgis/core/layers/voxel/VoxelOpacityStop.js", "../../@arcgis/core/layers/voxel/VoxelRangeFilter.js", "../../@arcgis/core/layers/voxel/VoxelTransferFunctionStyle.js", "../../@arcgis/core/layers/voxel/VoxelUniqueValue.js", "../../@arcgis/core/layers/voxel/VoxelVariableStyle.js", "../../@arcgis/core/layers/voxel/VoxelIrregularSpacing.js", "../../@arcgis/core/layers/voxel/VoxelRegularSpacing.js", "../../@arcgis/core/layers/voxel/VoxelDimension.js", "../../@arcgis/core/layers/voxel/VoxelVolume.js", "../../@arcgis/core/layers/voxel/VoxelVolumeIndex.js", "../../@arcgis/core/layers/voxel/VoxelDynamicSection.js", "../../@arcgis/core/layers/voxel/VoxelVolumeStyle.js", "../../@arcgis/core/layers/VoxelLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{s,m as a}from\"../../chunks/quat.js\";import{a as t}from\"../../chunks/quatf64.js\";import{c as n,f as o}from\"../../chunks/vec3f64.js\";import{a as c,t as r}from\"../../chunks/common.js\";import{c as m,n as u,q as f}from\"../../chunks/vec3.js\";const h=n(),i=t(),p=t(),e=t(),j=o(0,0,1),k=o(0,1,0),q=o(1,0,0);function v(a){m(h,a),u(h,h);const n=Math.atan2(h[1],h[0]),o=s(t(),j,-n);f(h,h,o);const r=-1*Math.atan2(h[2],h[0]);return[c(n)+270,c(r)+90]}function M(t,n){return s(p,j,r(t-270)),s(e,k,r(n-90)),a(i,p,e),m(h,q),f(h,h,i),u(h,h),[h[0],h[1],h[2]]}export{M as computeNormalFromOrientationTilt,v as computeOrientationTiltFromNormal};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{ClonableMixin as o}from\"../../core/Clonable.js\";import{cyclicalDegrees as r}from\"../../core/Cyclical.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import{cast as i}from\"../../core/accessorSupport/decorators/cast.js\";import\"../../core/arrayUtils.js\";import{subclass as n}from\"../../core/accessorSupport/decorators/subclass.js\";import{ensureNumber as l}from\"../../core/accessorSupport/ensureType.js\";import{computeOrientationTiltFromNormal as a,computeNormalFromOrientationTilt as p}from\"./voxelPlaneUtils.js\";let m=class extends(o(e)){constructor(t){super(t),this.enabled=!0,this.label=\"\",this.normal=null,this.point=null}get orientation(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[t,o]=a(this.normal);return r.normalize(l(t),0,!0)}set orientation(t){const o=p(t,this.tilt);this._set(\"normal\",o),this._set(\"orientation\",t)}get tilt(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[t,o]=a(this.normal);return r.normalize(l(o),0,!0)}set tilt(t){const o=p(this.orientation,t);this._set(\"normal\",o),this._set(\"tilt\",t)}};t([s({type:Boolean,json:{write:!0}})],m.prototype,\"enabled\",void 0),t([s({type:String,json:{write:!0}})],m.prototype,\"label\",void 0),t([s({type:Number,json:{read:!1},clonable:!1,range:{min:0,max:360}}),i((t=>r.normalize(l(t),0,!0)))],m.prototype,\"orientation\",null),t([s({type:Number,json:{read:!1},clonable:!1,range:{min:0,max:360}}),i((t=>r.normalize(l(t),0,!0)))],m.prototype,\"tilt\",null),t([s({type:[Number],json:{write:!0}})],m.prototype,\"normal\",void 0),t([s({type:[Number],json:{write:!0}})],m.prototype,\"point\",void 0),m=t([n(\"esri.layers.voxel.VoxelSlice\")],m);const c=m;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{ClonableMixin as r}from\"../../core/Clonable.js\";import{cyclicalDegrees as o}from\"../../core/Cyclical.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as i}from\"../../core/accessorSupport/decorators/property.js\";import{ensureNumber as s,Integer as l}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as n}from\"../../core/accessorSupport/decorators/subclass.js\";import{persistable as p}from\"../../core/accessorSupport/decorators/persistable.js\";import{computeOrientationTiltFromNormal as a}from\"./voxelPlaneUtils.js\";import m from\"./VoxelSlice.js\";import{r as c}from\"../../chunks/persistableUrlUtils.js\";let d=class extends(r(t)){constructor(){super(...arguments),this.enabled=!0,this.href=null,this.id=null,this.label=\"\",this.normal=null,this.point=null,this.sizeInPixel=null,this.slices=null,this.timeId=0,this.variableId=null}get orientation(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[e,r]=a(this.normal);return o.normalize(s(e),0,!0)}get tilt(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[e,r]=a(this.normal);return o.normalize(s(r),0,!0)}};e([i({type:Boolean,json:{default:!0,write:!0}})],d.prototype,\"enabled\",void 0),e([i({type:String,json:{origins:{service:{read:c}},write:{enabled:!0,isRequired:!0}}}),p({origins:[\"web-scene\"],type:\"resource\",prefix:\"sections\",compress:!0})],d.prototype,\"href\",void 0),e([i({type:l,json:{write:{enabled:!0,isRequired:!0}}})],d.prototype,\"id\",void 0),e([i({type:String,json:{write:!0}})],d.prototype,\"label\",void 0),e([i({type:Number,clonable:!1,readOnly:!0,range:{min:0,max:360}})],d.prototype,\"orientation\",null),e([i({type:Number,clonable:!1,readOnly:!0,range:{min:0,max:360}})],d.prototype,\"tilt\",null),e([i({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],d.prototype,\"normal\",void 0),e([i({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],d.prototype,\"point\",void 0),e([i({type:[l],json:{write:{enabled:!0,isRequired:!0}}})],d.prototype,\"sizeInPixel\",void 0),e([i({type:[m],json:{write:!0}})],d.prototype,\"slices\",void 0),e([i({type:l,json:{default:0,write:!0}})],d.prototype,\"timeId\",void 0),e([i({type:l,json:{write:{enabled:!0,isRequired:!0}}})],d.prototype,\"variableId\",void 0),d=e([n(\"esri.layers.voxel.VoxelSection\")],d);const u=d;export{u as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";let t=class extends o{constructor(){super(...arguments),this.diffuseFactor=.5,this.specularFactor=.5}};r([e({type:Number,range:{min:0,max:1},json:{default:.5,write:!0}})],t.prototype,\"diffuseFactor\",void 0),r([e({type:Number,range:{min:0,max:1},json:{default:.5,write:!0}})],t.prototype,\"specularFactor\",void 0),t=r([s(\"esri.layers.voxel.VoxelSimpleShading\")],t);const p=t;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as r}from\"../../core/accessorSupport/decorators/subclass.js\";let s=class extends t{constructor(){super(...arguments),this.continuity=null,this.hasNoData=!1,this.noData=0,this.offset=0,this.scale=1,this.type=null}};o([e({type:[\"discrete\",\"continuous\"],json:{write:!0}})],s.prototype,\"continuity\",void 0),o([e({type:Bo<PERSON><PERSON>,json:{write:!0}})],s.prototype,\"hasNoData\",void 0),o([e({type:Number,json:{write:!0}})],s.prototype,\"noData\",void 0),o([e({type:Number,json:{write:!0}})],s.prototype,\"offset\",void 0),o([e({type:Number,json:{write:!0}})],s.prototype,\"scale\",void 0),o([e({type:String,json:{write:{enabled:!0,isRequired:!0}}})],s.prototype,\"type\",void 0),s=o([r(\"esri.layers.voxel.VoxelFormat\")],s);const p=s;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as o}from\"../../core/accessorSupport/decorators/subclass.js\";import s from\"./VoxelFormat.js\";let i=class extends e{constructor(){super(...arguments),this.id=null,this.description=\"\",this.name=null,this.originalFormat=null,this.renderingFormat=null,this.unit=\"\",this.volumeId=0,this.type=null}};t([r({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],i.prototype,\"id\",void 0),t([r({type:String,json:{write:!0}})],i.prototype,\"description\",void 0),t([r({type:String,json:{write:{enabled:!0,isRequired:!0}}})],i.prototype,\"name\",void 0),t([r({type:s,json:{write:!0}})],i.prototype,\"originalFormat\",void 0),t([r({type:s,json:{write:{enabled:!0,isRequired:!0}}})],i.prototype,\"renderingFormat\",void 0),t([r({type:String,json:{write:!0}})],i.prototype,\"unit\",void 0),t([r({type:Number,json:{write:!0}})],i.prototype,\"volumeId\",void 0),t([r({type:[\"stc-hot-spot-results\",\"stc-cluster-outlier-results\",\"stc-estimated-bin\",\"generic-nearest-interpolated\"],json:{write:!0}})],i.prototype,\"type\",void 0),i=t([o(\"esri.layers.voxel.VoxelVariable\")],i);const p=i;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import e from\"../../Color.js\";import{ClonableMixin as r}from\"../../core/Clonable.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import{Integer as p}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as i}from\"../../core/accessorSupport/decorators/subclass.js\";let l=class extends(r(t)){constructor(){super(...arguments),this.color=e.fromArray([0,0,0,0]),this.value=0,this.enabled=!0,this.label=\"\",this.colorLocked=!1}};o([s({type:e,json:{type:[p],write:{enabled:!0,isRequired:!0}}})],l.prototype,\"color\",void 0),o([s({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],l.prototype,\"value\",void 0),o([s({type:Boolean,json:{default:!0,write:!0}})],l.prototype,\"enabled\",void 0),o([s({type:String,json:{write:!0}})],l.prototype,\"label\",void 0),o([s({type:Boolean,json:{default:!1,write:!0}})],l.prototype,\"colorLocked\",void 0),l=o([i(\"esri.layers.voxel.VoxelIsosurface\")],l);const a=l;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import r from\"../../Color.js\";import{ClonableMixin as e}from\"../../core/Clonable.js\";import{JSONSupport as s}from\"../../core/JSONSupport.js\";import{property as t}from\"../../core/accessorSupport/decorators/property.js\";import{Integer as p}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as i}from\"../../core/accessorSupport/decorators/subclass.js\";let c=class extends(e(s)){constructor(){super(...arguments),this.color=null,this.position=0}};o([t({type:r,json:{type:[p],write:{enabled:!0,isRequired:!0}}})],c.prototype,\"color\",void 0),o([t({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],c.prototype,\"position\",void 0),c=o([i(\"esri.layers.voxel.VoxelColorStop\")],c);const l=c;export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import{ClonableMixin as r}from\"../../core/Clonable.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";let p=class extends(r(e)){constructor(){super(...arguments),this.opacity=1,this.position=0}};o([s({type:Number,json:{name:\"alpha\",write:{enabled:!0,isRequired:!0}}})],p.prototype,\"opacity\",void 0),o([s({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],p.prototype,\"position\",void 0),p=o([t(\"esri.layers.voxel.VoxelOpacityStop\")],p);const i=p;export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{ClonableMixin as o}from\"../../core/Clonable.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";let p=class extends(o(e)){constructor(){super(...arguments),this.enabled=!1,this.range=null}};r([s({type:<PERSON><PERSON><PERSON>,json:{default:!1,write:!0}})],p.prototype,\"enabled\",void 0),r([s({type:[Number],json:{write:!0}})],p.prototype,\"range\",void 0),p=r([t(\"esri.layers.voxel.VoxelRangeFilter\")],p);const a=p;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import t from\"../../Color.js\";import{ClonableMixin as r}from\"../../core/Clonable.js\";import e from\"../../core/Collection.js\";import{referenceSetter as s}from\"../../core/collectionUtils.js\";import{JSONSupport as i}from\"../../core/JSONSupport.js\";import{clamp as p,lerp as n}from\"../../core/mathUtils.js\";import{property as l}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as c}from\"../../core/accessorSupport/decorators/subclass.js\";import a from\"./VoxelColorStop.js\";import h from\"./VoxelOpacityStop.js\";import f from\"./VoxelRangeFilter.js\";var u;!function(o){o[o.Color=1]=\"Color\",o[o.Alpha=2]=\"Alpha\",o[o.Both=3]=\"Both\"}(u||(u={}));let y=class extends(r(i)){constructor(o){super(o),this.interpolation=null,this.stretchRange=null,this.rangeFilter=null,this._colorMapSize=256,this.colorStops=new(e.ofType(a)),this.opacityStops=new(e.ofType(h))}set colorStops(o){this._set(\"colorStops\",s(o,this._get(\"colorStops\"),e.ofType(a)))}set opacityStops(o){this._set(\"opacityStops\",s(o,this._get(\"opacityStops\"),e.ofType(h)))}getPreviousNext(o,t,r){let e=o;for(;--e>0&&t[e].type!==r&&t[e].type!==u.Both;);let s=o;const i=t.length;for(;++s<i&&t[s].type!==r&&t[s].type!==u.Both;);return[e,s]}get rasterizedTransferFunction(){const o=[];if(this.colorStops.length<2)return o;const r=[],e=[],s=1e-5;for(const t of this.colorStops){if(!t.color)return o;e.push({color:{r:t.color.r,g:t.color.g,b:t.color.b,a:Math.round(255*(1-t.color.a))},position:t.position,type:u.Color})}if(0===this.opacityStops.length)for(const t of e)r.push({color:t.color,position:t.position});else{for(const t of this.opacityStops){const o=p(t.position,0,1),r=Math.round(255*p(1-t.opacity,0,1));let i=!1;for(const t of e)if(t.type===u.Color&&Math.abs(t.position-o)<s){t.color.a=r,t.type=u.Both,i=!0;break}i||e.push({color:{r:0,g:0,b:0,a:r},position:t.position,type:u.Alpha})}e.sort(((o,t)=>o.position<t.position?-1:1));const o=e.length;for(let t=0;t<o;++t){const r=e[t];if(r.type!==u.Both)if(r.type===u.Color){const[s,i]=this.getPreviousNext(t,e,u.Alpha);if(-1!==s&&i!==o){const o=(r.position-e[s].position)/(e[i].position-e[s].position);r.color.a=Math.round(n(e[s].color.a,e[i].color.a,o))}else r.color.a=-1!==s?e[s].color.a:e[i].color.a}else{const[s,i]=this.getPreviousNext(t,e,u.Color);if(-1!==s&&i!==o){const o=(r.position-e[s].position)/(e[i].position-e[s].position),t=e[s].color,p=e[i].color;[\"r\",\"g\",\"b\"].forEach((e=>{r.color[e]=Math.round(n(t[e],p[e],o))}))}else[\"r\",\"g\",\"b\"].forEach(-1!==s?o=>{r.color[o]=e[s][o]}:o=>{r.color[o]=e[i][o]})}}for(const t of e)r.push({color:t.color,position:t.position})}r[0].position=0,r[r.length-1].position=1;let i=0,l=1;for(let c=0;c<this._colorMapSize;++c){const e=c/this._colorMapSize;for(;e>r[l].position;)i=l++;const s=(e-r[i].position)/(r[l].position-r[i].position),a=r[i].color,h=r[l].color,f=new t;[\"r\",\"g\",\"b\"].forEach((o=>{f[o]=Math.round(n(a[o],h[o],s))})),f.a=p(1-n(a.a,h.a,s)/255,0,1),o.push(f)}return o}getColorForContinuousDataValue(o,t){const r=this.rasterizedTransferFunction;if(this.colorStops.length<2||!Array.isArray(this.stretchRange)||this.stretchRange.length<2||r.length<256)return null;let e=this.stretchRange[0],s=this.stretchRange[1];if(e>s){const o=e;e=s,s=o}o=p(o,e,s);const i=r[Math.round((o-e)/(s-e)*(this._colorMapSize-1))].clone();return t||(i.a=1),i}};o([l({type:[\"linear\",\"nearest\"],json:{write:!0}})],y.prototype,\"interpolation\",void 0),o([l({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],y.prototype,\"stretchRange\",void 0),o([l({type:e.ofType(a),json:{write:{enabled:!0,overridePolicy(){return{enabled:!!this.colorStops&&this.colorStops.length>0}}}}})],y.prototype,\"colorStops\",null),o([l({type:e.ofType(h),json:{read:{source:\"alphaStops\"},write:{enabled:!0,target:\"alphaStops\",overridePolicy(){return{enabled:!!this.opacityStops&&this.opacityStops.length>0}}}}})],y.prototype,\"opacityStops\",null),o([l({type:f,json:{write:!0}})],y.prototype,\"rangeFilter\",void 0),o([l({type:[t],clonable:!1,json:{read:!1}})],y.prototype,\"rasterizedTransferFunction\",null),y=o([c(\"esri.layers.voxel.VoxelTransferFunctionStyle\")],y);const g=y;export{g as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import e from\"../../Color.js\";import{ClonableMixin as r}from\"../../core/Clonable.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import{Integer as p}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as i}from\"../../core/accessorSupport/decorators/subclass.js\";let l=class extends(r(t)){constructor(){super(...arguments),this.color=e.fromArray([0,0,0,0]),this.value=0,this.enabled=!0,this.label=\"\"}};o([s({type:e,json:{type:[p],write:{enabled:!0,isRequired:!0}}})],l.prototype,\"color\",void 0),o([s({type:p,json:{write:{enabled:!0,isRequired:!0}}})],l.prototype,\"value\",void 0),o([s({type:Boolean,json:{default:!0,write:!0}})],l.prototype,\"enabled\",void 0),o([s({type:String,json:{write:!0}})],l.prototype,\"label\",void 0),l=o([i(\"esri.layers.voxel.VoxelUniqueValue\")],l);const a=l;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import s from\"../../core/Collection.js\";import{JSONSupport as r}from\"../../core/JSONSupport.js\";import{clone as o}from\"../../core/lang.js\";import{property as t}from\"../../core/accessorSupport/decorators/property.js\";import{Integer as i}from\"../../core/accessorSupport/ensureType.js\";import{subclass as u}from\"../../core/accessorSupport/decorators/subclass.js\";import n from\"./VoxelIsosurface.js\";import l from\"./VoxelTransferFunctionStyle.js\";import a from\"./VoxelUniqueValue.js\";var p;let c=p=class extends r{constructor(e){super(e),this.variableId=0,this.label=\"\",this.transferFunction=null,this.uniqueValues=null,this.isosurfaces=null,this.uniqueValues=new(s.ofType(a)),this.isosurfaces=new(s.ofType(n))}clone(){return new p({variableId:this.variableId,label:this.label,transferFunction:o(this.transferFunction),uniqueValues:o(this.uniqueValues),isosurfaces:o(this.isosurfaces)})}};e([t({type:i,json:{write:{enabled:!0,isRequired:!0}}})],c.prototype,\"variableId\",void 0),e([t({type:String,json:{write:!0}})],c.prototype,\"label\",void 0),e([t({type:l,json:{write:{enabled:!0,overridePolicy(){return{enabled:!this.uniqueValues||this.uniqueValues.length<1}}}}})],c.prototype,\"transferFunction\",void 0),e([t({type:s.ofType(a),json:{write:{enabled:!0,overridePolicy(){return{enabled:!!this.uniqueValues&&this.uniqueValues.length>0}}}}})],c.prototype,\"uniqueValues\",void 0),e([t({type:s.ofType(n),json:{write:{enabled:!0,overridePolicy(){const e=!this.uniqueValues||this.uniqueValues.length<1,s=!!this.isosurfaces&&this.isosurfaces.length>0;return{enabled:e&&s}}}}})],c.prototype,\"isosurfaces\",void 0),c=p=e([u(\"esri.layers.voxel.VoxelVariableStyle\")],c);const f=c;export{f as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as e}from\"../../core/accessorSupport/decorators/subclass.js\";let t=class extends o{constructor(){super(...arguments),this.values=null}};r([s({type:[Number],json:{write:!0}})],t.prototype,\"values\",void 0),t=r([e(\"esri.layers.voxel.VoxelIrregularSpacing\")],t);const p=t;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as e}from\"../../core/accessorSupport/decorators/subclass.js\";let t=class extends o{constructor(){super(...arguments),this.scale=1,this.offset=0}};r([s({type:Number,json:{write:!0}})],t.prototype,\"scale\",void 0),r([s({type:Number,json:{write:!0}})],t.prototype,\"offset\",void 0),t=r([e(\"esri.layers.voxel.VoxelRegularSpacing\")],t);const p=t;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{property as t}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as i}from\"../../core/accessorSupport/decorators/subclass.js\";import o from\"./VoxelIrregularSpacing.js\";import s from\"./VoxelRegularSpacing.js\";let p=class extends e{constructor(){super(...arguments),this.irregularSpacing=null,this.isPositiveUp=!0,this.isWrappedDateLine=!1,this.label=null,this.name=null,this.quantity=null,this.regularSpacing=null,this.size=0,this.unit=null}get isRegular(){return(null==this.irregularSpacing||void 0===this.irregularSpacing)&&null!==this.regularSpacing}getRange(){return this.isRegular?[this.regularSpacing.offset,this.regularSpacing.offset+this.regularSpacing.scale*(this.size-1)]:Array.isArray(this.irregularSpacing?.values)&&this.irregularSpacing.values.length>1?[this.irregularSpacing.values[0],this.irregularSpacing.values[this.irregularSpacing.values.length-1]]:[0,0]}};r([t({type:o,json:{write:!0}})],p.prototype,\"irregularSpacing\",void 0),r([t({type:Boolean,json:{write:!0}})],p.prototype,\"isPositiveUp\",void 0),r([t({type:Boolean,json:{write:!0}})],p.prototype,\"isWrappedDateLine\",void 0),r([t({type:String,json:{write:!0}})],p.prototype,\"label\",void 0),r([t({type:String,json:{write:!0}})],p.prototype,\"name\",void 0),r([t({type:String,json:{write:!0}})],p.prototype,\"quantity\",void 0),r([t({type:s,json:{write:!0}})],p.prototype,\"regularSpacing\",void 0),r([t({type:Number,json:{write:!0}})],p.prototype,\"size\",void 0),r([t({type:String,json:{write:!0}})],p.prototype,\"unit\",void 0),r([t({type:Boolean,json:{read:!1}})],p.prototype,\"isRegular\",null),p=r([i(\"esri.layers.voxel.VoxelDimension\")],p);const a=p;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{JSONSupport as i}from\"../../core/JSONSupport.js\";import s from\"../../core/Logger.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";import{b as n,C as o,B as a,a as l}from\"../../chunks/vec3.js\";import{f as p,d as u}from\"../../chunks/vec3f64.js\";import m from\"../../geometry/Point.js\";import c from\"../../geometry/SpatialReference.js\";import{equals as h}from\"../../geometry/support/spatialReferenceUtils.js\";import g from\"./VoxelDimension.js\";const d=\"esri.layers.voxel.VoxelVolume\",y=s.getLogger(d);let f=class extends i{constructor(e){super(e),this.id=0,this.dimensions=null,this.spatialReference=c.WGS84}get zDimension(){if(!this.dimensions)return-1;if(!Array.isArray(this.dimensions))return-1;if(4!==this.dimensions.length)return-1;for(let e=2;e<4;++e)if(this.dimensions[e].size>0)return e;return-1}get isValid(){return!!this.dimensions&&(!!Array.isArray(this.dimensions)&&(4===this.dimensions.length&&(!(this.dimensions[0].size<1||this.dimensions[1].size<1)&&!(-1===this.zDimension||this.dimensions[this.zDimension].size<1))))}get originInLayerSpace3D(){if(!this.isValid||\"xyt\"===this.volumeType)return[0,0,0];const e=this.dimensions[0].getRange(),i=this.dimensions[1].getRange(),s=this.dimensions[2],r=s.isRegular?s.getRange():[0,s.size];return[e[0],i[0],r[0]]}get voxelSizeInLayerSpaceSigned(){if(!this.isValid||\"xyt\"===this.volumeType)return[0,0,0];const e=this.dimensions[0].getRange(),i=this.dimensions[1].getRange(),s=this.dimensions[2],r=s.isRegular?s.getRange():[0,s.size],t=[this.sizeInVoxels[0],this.sizeInVoxels[1],this.sizeInVoxels[2]];for(let n=0;n<3;++n)t[n]<2?t[n]=1:t[n]-=1;return s.isRegular&&!s.isPositiveUp&&(t[2]*=-1),[(e[1]-e[0])/t[0],(i[1]-i[0])/t[1],(r[1]-r[0])/t[2]]}get volumeType(){if(this.isValid){const e=this.dimensions[2].size>0,i=this.dimensions[3].size>0;if(!e&&i)return\"xyt\";if(e&&i)return\"xyzt\"}return\"xyz\"}get sizeInVoxels(){if(!this.isValid)return[0,0,0];const e=this.zDimension;return[this.dimensions[0].size,this.dimensions[1].size,this.dimensions[e].size]}computeVoxelSpaceLocation(e){if(!this.isValid)return[0,0,0];if(\"xyt\"===this.volumeType)return y.error(\"computeVoxelSpacePosition cannot be used with XYT volumes.\"),[0,0,0];if(!h(this.spatialReference,e.spatialReference))return y.error(\"pos argument should have the same spatial reference as the VoxelLayer.\"),[0,0,0];const i=p(e.x,e.y,e.z??0);n(i,i,this.originInLayerSpace3D),o(i,i,this.voxelSizeInLayerSpaceSigned);const s=this.dimensions[this.zDimension];if(!s.isRegular&&Array.isArray(s.irregularSpacing?.values)&&s.irregularSpacing.values.length>1){const r=e.z??0,t=s.irregularSpacing.values,n=s.isPositiveUp?1:-1,o=t.reduce(((e,i)=>Math.abs(n*i-r)<Math.abs(n*e-r)?i:e));for(let e=0;e<t.length;++e)if(t[e]===o){i[2]=e;break}}return[i[0],i[1],i[2]]}computeLayerSpaceLocation(e){if(!this.isValid)return new m({x:0,y:0,spatialReference:this.spatialReference});const i=u(e);if(a(i,i,this.voxelSizeInLayerSpaceSigned),l(i,i,this.originInLayerSpace3D),\"xyt\"===this.volumeType)return new m({x:i[0],y:i[1],spatialReference:this.spatialReference});const s=this.dimensions[this.zDimension];return s.isRegular||Array.isArray(s.irregularSpacing?.values)&&(e[2]<0?i[2]=s.irregularSpacing.values[0]:e[2]<s.irregularSpacing.values.length?i[2]=s.irregularSpacing.values[e[2]]:i[2]=s.irregularSpacing.values[s.irregularSpacing.values.length-1],s.isPositiveUp||(i[2]*=-1)),new m({x:i[0],y:i[1],z:i[2],spatialReference:this.spatialReference})}};e([r({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],f.prototype,\"id\",void 0),e([r({type:[g],json:{write:{enabled:!0,isRequired:!0}}})],f.prototype,\"dimensions\",void 0),e([r({type:c,json:{read:{enabled:!1}}})],f.prototype,\"spatialReference\",void 0),e([r({type:Number,json:{read:!1}})],f.prototype,\"zDimension\",null),e([r({type:[Boolean],json:{read:!1}})],f.prototype,\"isValid\",null),e([r({type:[Number],json:{read:!1}})],f.prototype,\"originInLayerSpace3D\",null),e([r({type:[Number],json:{read:!1}})],f.prototype,\"voxelSizeInLayerSpaceSigned\",null),e([r({type:[\"xyz\",\"xyzt\",\"xyt\"],json:{read:{enabled:!1}}})],f.prototype,\"volumeType\",null),e([r({type:[Number],json:{read:!1}})],f.prototype,\"sizeInVoxels\",null),f=e([t(d)],f);const S=f;export{S as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{JSONSupport as r}from\"../../core/JSONSupport.js\";import{property as i}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as o}from\"../../core/accessorSupport/decorators/subclass.js\";var t;let s=t=class extends r{constructor(){super(...arguments),this.apronWidth=1,this.brickSize=[32,32,32],this.maxLodLevel=0,this.nodeSize=[4,4,4]}isValid(){const e=new t;return e.apronWidth===this.apronWidth&&e.maxLodLevel===this.maxLodLevel&&(!!this.brickSize&&(!!this.nodeSize&&(!(!Array.isArray(this.brickSize)||!Array.isArray(this.nodeSize))&&(3===this.brickSize.length&&3===this.nodeSize.length&&(32===this.brickSize[0]&&32===this.brickSize[1]&&32===this.brickSize[2]&&(4===this.nodeSize[0]&&4===this.nodeSize[1]&&4===this.nodeSize[2]))))))}};e([i({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],s.prototype,\"apronWidth\",void 0),e([i({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],s.prototype,\"brickSize\",void 0),e([i({type:Number,json:{write:{enabled:!0,isRequired:!0}}})],s.prototype,\"maxLodLevel\",void 0),e([i({type:[Number],json:{write:{enabled:!0,isRequired:!0}}})],s.prototype,\"nodeSize\",void 0),s=t=e([o(\"esri.layers.voxel.VoxelVolumeIndex\")],s);const p=s;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{ClonableMixin as o}from\"../../core/Clonable.js\";import{cyclicalDegrees as r}from\"../../core/Cyclical.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import{cast as i}from\"../../core/accessorSupport/decorators/cast.js\";import\"../../core/arrayUtils.js\";import{subclass as n}from\"../../core/accessorSupport/decorators/subclass.js\";import{ensureNumber as a}from\"../../core/accessorSupport/ensureType.js\";import{computeOrientationTiltFromNormal as l,computeNormalFromOrientationTilt as p}from\"./voxelPlaneUtils.js\";let m=class extends(o(e)){constructor(t){super(t),this.enabled=!0,this.label=\"\",this.normal=null,this.point=null}get orientation(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[t,o]=l(this.normal);return r.normalize(a(t),0,!0)}set orientation(t){const o=p(t,this.tilt);this._set(\"normal\",o),this._set(\"orientation\",t)}get tilt(){if(!Array.isArray(this.normal)||3!==this.normal.length)return 0;const[t,o]=l(this.normal);return r.normalize(a(o),0,!0)}set tilt(t){const o=p(this.orientation,t);this._set(\"normal\",o),this._set(\"tilt\",t)}};t([s({type:Boolean,json:{default:!0,write:!0}})],m.prototype,\"enabled\",void 0),t([s({type:String,json:{write:!0}})],m.prototype,\"label\",void 0),t([s({type:Number,json:{read:!1},clonable:!1,range:{min:0,max:360}}),i((t=>r.normalize(a(t),0,!0)))],m.prototype,\"orientation\",null),t([s({type:Number,json:{read:!1},clonable:!1,range:{min:0,max:360}}),i((t=>r.normalize(a(t),0,!0)))],m.prototype,\"tilt\",null),t([s({type:[Number],json:{write:!0}})],m.prototype,\"normal\",void 0),t([s({type:[Number],json:{write:!0}})],m.prototype,\"point\",void 0),m=t([n(\"esri.layers.voxel.VoxelDynamicSection\")],m);const c=m;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../core/Collection.js\";import{referenceSetter as o}from\"../../core/collectionUtils.js\";import{JSONSupport as s}from\"../../core/JSONSupport.js\";import{clone as i}from\"../../core/lang.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import{Integer as c}from\"../../core/accessorSupport/ensureType.js\";import{subclass as l}from\"../../core/accessorSupport/decorators/subclass.js\";import a from\"./VoxelDynamicSection.js\";import n from\"./VoxelSlice.js\";var p;let m=p=class extends s{constructor(e){super(e),this.volumeId=0,this.verticalExaggeration=1,this.exaggerationMode=\"scale-height\",this.verticalOffset=0,this.slices=new(t.ofType(n)),this.dynamicSections=new(t.ofType(a))}set slices(e){this._set(\"slices\",o(e,this._get(\"slices\"),t.ofType(n)))}set dynamicSections(e){this._set(\"dynamicSections\",o(e,this._get(\"dynamicSections\"),t.ofType(a)))}clone(){return new p({volumeId:this.volumeId,verticalExaggeration:this.verticalExaggeration,exaggerationMode:this.exaggerationMode,verticalOffset:this.verticalOffset,slices:i(this.slices),dynamicSections:i(this.dynamicSections)})}};e([r({type:c,json:{write:{enabled:!0,isRequired:!0}}})],m.prototype,\"volumeId\",void 0),e([r({type:Number,json:{default:1,write:!0}})],m.prototype,\"verticalExaggeration\",void 0),e([r({type:[\"scale-position\",\"scale-height\"],json:{default:\"scale-height\",write:!0}})],m.prototype,\"exaggerationMode\",void 0),e([r({type:Number,json:{default:0,write:!0}})],m.prototype,\"verticalOffset\",void 0),e([r({type:t.ofType(n),json:{write:{enabled:!0,overridePolicy(){return{enabled:!!this.slices&&this.slices.length>0}}}}})],m.prototype,\"slices\",null),e([r({type:t.ofType(a),json:{write:{enabled:!0,overridePolicy(){return{enabled:!!this.dynamicSections&&this.dynamicSections.length>0}}}}})],m.prototype,\"dynamicSections\",null),m=p=e([l(\"esri.layers.voxel.VoxelVolumeStyle\")],m);const d=m;export{d as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import t from\"../core/Collection.js\";import i from\"../core/Error.js\";import o from\"../core/Logger.js\";import{isSome as r,isNone as s}from\"../core/maybe.js\";import{MultiOriginJSONMixin as n}from\"../core/MultiOriginJSONSupport.js\";import{throwIfAbortError as l}from\"../core/promiseUtils.js\";import{property as a}from\"../core/accessorSupport/decorators/property.js\";import{Integer as u}from\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{reader as p}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as c}from\"../core/accessorSupport/decorators/subclass.js\";import{n as m}from\"../chunks/vec3.js\";import{f as y}from\"../chunks/vec3f64.js\";import d from\"../geometry/Extent.js\";import f from\"./Layer.js\";import{APIKeyMixin as v}from\"./mixins/APIKeyMixin.js\";import{ArcGISService as h}from\"./mixins/ArcGISService.js\";import{OperationalLayer as b}from\"./mixins/OperationalLayer.js\";import{PortalLayer as g}from\"./mixins/PortalLayer.js\";import{ScaleRangeLayer as S}from\"./mixins/ScaleRangeLayer.js\";import{SceneService as V}from\"./mixins/SceneService.js\";import{sanitizeUrl as x}from\"./support/arcgisLayerUrl.js\";import{legendEnabled as j,url as w,popupEnabled as I}from\"./support/commonProperties.js\";import T from\"./support/Field.js\";import D from\"./voxel/VoxelSection.js\";import F from\"./voxel/VoxelSimpleShading.js\";import N from\"./voxel/VoxelVariable.js\";import L from\"./voxel/VoxelVariableStyle.js\";import P from\"./voxel/VoxelVolume.js\";import A from\"./voxel/VoxelVolumeIndex.js\";import E from\"./voxel/VoxelVolumeStyle.js\";import{createPopupTemplate as O}from\"../support/popupUtils.js\";const R=\"esri.layers.VoxelLayer\",M=o.getLogger(R);let z=class extends(V(h(b(g(S(n(v(f)))))))){constructor(e){super(e),this.serviceRoot=\"\",this.operationalLayerType=\"Voxel\",this.legendEnabled=!0,this.title=null,this.sections=null,this.currentVariableId=0,this.volumeStyles=null,this.renderMode=\"volume\",this.variableStyles=null,this.enableSlices=!0,this.enableSections=!0,this.enableDynamicSections=!0,this.enableIsosurfaces=!0,this.shading=new F,this.opacity=1,this.variables=new t,this.volumes=new t,this.index=null,this.minScale=0,this.maxScale=0,this.type=\"voxel\",this.version={major:Number.NaN,minor:Number.NaN,versionString:\"\"},this.fullExtent=null,this.popupEnabled=!0,this.popupTemplate=null,this.test=null,this.volumeStyles=new(t.ofType(E)),this.variableStyles=new(t.ofType(L)),this.sections=new(t.ofType(D)),e?.constantUpscaling&&(this.test={constantUpscaling:!0})}set url(e){this._set(\"url\",x(e,M))}load(e){const t=r(e)?e.signal:null,i=this.loadFromPortal({supportedTypes:[\"Scene Service\"]},e).catch(l).then((()=>this._fetchService(t))).then((()=>this.serviceRoot=this.url));return this.addResolvingPromise(i),Promise.resolve(this)}read(e,t){super.read(e,t);for(const i of this.volumes)i.spatialReference=this.spatialReference}readVersion(e,t){return super.parseVersionString(e)}validateLayer(e){if(e.layerType&&e.layerType!==this.operationalLayerType)throw new i(\"voxel-layer:layer-type-not-supported\",\"VoxelLayer does not support this layer type\",{layerType:e.layerType});if(isNaN(this.version.major)||isNaN(this.version.minor)||this.version.major<3)throw new i(\"layer:service-version-not-supported\",\"Service version is not supported.\",{serviceVersion:this.version.versionString,supportedVersions:\"3.x\"});if(this.version.major>3)throw new i(\"layer:service-version-too-new\",\"Service version is too new.\",{serviceVersion:this.version.versionString,supportedVersions:\"3.x\"})}readFullExtent(e,t,i){if(null!=e&&\"object\"==typeof e){const o=d.fromJSON(e,i);if(0===o.zmin&&0===o.zmax&&Array.isArray(t.volumes)){const e=P.fromJSON(t.volumes[0]);if(e.isValid&&\"xyt\"!==e.volumeType){const t=e.dimensions[2];if(t.isRegular){let e=t.regularSpacing.offset,i=t.regularSpacing.offset+t.regularSpacing.scale*(t.size-1);if(e>i){const t=e;e=i,i=t}o.zmin=e,o.zmax=i}}}return o}return null}get voxelFields(){const e=[new T({name:\"Voxel.ServiceValue\",alias:\"Value\",domain:null,editable:!1,length:128,type:\"string\"}),new T({name:\"Voxel.ServiceVariableLabel\",alias:\"Variable\",domain:null,editable:!1,length:128,type:\"string\"}),new T({name:\"Voxel.Position\",alias:\"Voxel Position\",domain:null,editable:!1,length:128,type:\"string\"})],t=this.getVolume(null);if(r(t)){if(\"xyzt\"===t.volumeType||\"xyt\"===t.volumeType){const t=new T({name:\"Voxel.ServiceLocalTime\",alias:\"Local Time\",domain:null,editable:!1,length:128,type:\"string\"});e.push(t);const i=new T({name:\"Voxel.ServiceNativeTime\",alias:\"Native Time\",domain:null,editable:!1,length:128,type:\"string\"});e.push(i)}if(\"xyt\"!==t.volumeType){const t=new T({name:\"Voxel.ServiceDepth\",alias:\"Depth\",domain:null,editable:!1,length:128,type:\"string\"});e.push(t)}}return e}get defaultPopupTemplate(){return this.createPopupTemplate()}createPopupTemplate(e){const t=this.voxelFields,i=this.title;return O({fields:t,title:i},e)}getConfiguration(){const e={layerType:this.operationalLayerType,version:this.version.versionString,name:this.title,spatialReference:this.spatialReference,fullExtent:this.fullExtent,volumes:this.volumes.toJSON(),variables:this.variables.toJSON(),index:this.index?.toJSON(),sections:this.getSections(),style:{volumeStyles:this.getVolumeStyles(),currentVariableId:this.currentVariableId,renderMode:this.renderMode,variableStyles:this.getVariableStyles(),enableSections:this.enableSections,enableDynamicSections:this.enableDynamicSections,enableIsosurfaces:this.enableIsosurfaces,enableSlices:this.enableSlices,shading:this.shading}};return e.index&&this.index?.isValid()?JSON.stringify(e):\"\"}getVariableStyle(e){let t=-1;t=r(e)?e:this.currentVariableId;if(!this.variableStyles||-1===t)return null;const i=this.variableStyles.findIndex((e=>e.variableId===t));return i<0?null:this.variableStyles.getItemAt(i)}getVariable(e){let t=-1;if(t=r(e)?e:this.currentVariableId,!this.variables||-1===t)return null;const i=this.variables.findIndex((e=>e.id===t));return i<0?null:this.variables.getItemAt(i)}getVolume(e){const t=this.getVariable(e);return r(t)?this.volumes.find((({id:e})=>e===t.volumeId)):null}getVolumeStyle(e){const t=this.getVariable(e);return r(t)?this.volumeStyles.find((({volumeId:e})=>e===t.volumeId)):null}getColorForContinuousDataValue(e,t,i){const o=this.getVariable(e);if(s(o)||\"continuous\"!==o.renderingFormat?.continuity)return null;if(!this.variableStyles)return null;const r=this.variableStyles.findIndex((t=>t.variableId===e));if(r<0)return null;const n=this.variableStyles.getItemAt(r);return n.transferFunction?n.transferFunction.getColorForContinuousDataValue(t,i):null}getSections(){const e=[];for(const t of this.sections)e.push(new D({enabled:t.enabled,href:t.href,id:t.id,label:t.label,normal:t.normal,point:t.point,sizeInPixel:t.sizeInPixel,slices:t.slices,timeId:t.timeId,variableId:t.variableId}));return e}getVariableStyles(){const e=[];for(const t of this.variableStyles){const i=this._getVariable(t);if(r(i)){const o=t.clone();o.isosurfaces.length>4&&(o.isosurfaces=o.isosurfaces.slice(0,3),M.error(\"A maximum of 4 isosurfaces are supported for Voxel Layers.\"));for(const e of o.isosurfaces)if(!e.colorLocked){const t=this.getColorForContinuousDataValue(o.variableId,e.value,!1);null===t||t.equals(e.color)||(e.color=t)}if(\"continuous\"===i.renderingFormat.continuity)(null===o.transferFunction||o.transferFunction.colorStops.length<2)&&M.error(`VoxelVariableStyle for variable ${i.id} is invalid. At least 2 color stops are required in the transferFunction for continuous Voxel Layer variables.`),null!==o.transferFunction&&(Array.isArray(o.transferFunction.stretchRange)&&2===o.transferFunction.stretchRange.length||(M.error(`VoxelVariableStyle for variable ${i.id} is invalid. The stretchRange of the transferFunction for continuous Voxel Layer variables must be of the form [minimumDataValue, maximumDataValue].`),o.transferFunction.stretchRange=[0,1],o.transferFunction.colorStops.removeAll()));else if(\"discrete\"===i.renderingFormat.continuity)if(0===t.uniqueValues.length)M.error(`VoxelVariableStyle for variable ${i.id} is invalid. Unique values are required for discrete Voxel Layer variables.`);else for(const e of t.uniqueValues)null!==e.label&&void 0!==e.label||null===e.value||void 0===e.value||(e.label=e.value.toString());e.push(o)}else M.error(`VoxelVariable ID=${t.variableId} doesn't exist, VoxelVariableStyle for this VoxelVariable will be ignored.`)}return e}getVolumeStyles(){const e=[];for(const t of this.volumeStyles){const i=this._getVolumeFromVolumeId(t.volumeId);if(r(i)){const o=t.clone();for(const e of o.slices)this._isPlaneValid(e,[0,1,i.zDimension],i.dimensions)||(e.enabled=!1,e.label=\"invalid\");for(const e of o.dynamicSections)this._isPlaneValid(e,[0,1,i.zDimension],i.dimensions)||(e.enabled=!1,e.label=\"invalid\");e.push(o)}else M.error(`VoxelVolume ID=${t.volumeId} doesn't exist, VoxelVolumeStyle for this VoxelVolume will be ignored.`)}return e}_getVariable(e){const t=e.variableId;for(const i of this.variables)if(i.id===t)return i;return null}_getVolumeFromVolumeId(e){for(const t of this.volumes)if(t.id===e)return t;return null}_isPlaneValid(e,t,i){if(!e.point)return!1;if(!Array.isArray(e.point)||3!==e.point.length)return!1;if(!e.normal)return!1;if(!Array.isArray(e.normal)||3!==e.normal.length)return!1;for(let s=0;s<3;++s){const o=e.point[s];if(o<0||o>=i[t[s]].size)return!1}const o=y(e.normal[0],e.normal[1],e.normal[2]);m(o,o);const r=1e-6;return!(Math.abs(o[0])+Math.abs(o[1])+Math.abs(o[2])<r)&&(e.normal[0]=o[0],e.normal[1]=o[1],e.normal[2]=o[2],!0)}};e([a({type:[\"Voxel\"]})],z.prototype,\"operationalLayerType\",void 0),e([a(j)],z.prototype,\"legendEnabled\",void 0),e([a({json:{write:!0}})],z.prototype,\"title\",void 0),e([a(w)],z.prototype,\"url\",null),e([a({type:t.ofType(D),json:{origins:{\"web-scene\":{name:\"layerDefinition.sections\",write:!0}}}})],z.prototype,\"sections\",void 0),e([a({type:u,json:{origins:{\"web-scene\":{name:\"layerDefinition.style.currentVariableId\",write:{enabled:!0,isRequired:!0,ignoreOrigin:!0}},service:{name:\"style.currentVariableId\"}}}})],z.prototype,\"currentVariableId\",void 0),e([a({type:t.ofType(E),json:{origins:{\"web-scene\":{name:\"layerDefinition.style.volumeStyles\",write:!0},service:{name:\"style.volumeStyles\"}}}})],z.prototype,\"volumeStyles\",void 0),e([a({type:[\"volume\",\"surfaces\"],json:{origins:{\"web-scene\":{name:\"layerDefinition.style.renderMode\",write:!0},service:{name:\"style.renderMode\"}}}})],z.prototype,\"renderMode\",void 0),e([a({type:t.ofType(L),json:{origins:{\"web-scene\":{name:\"layerDefinition.style.variableStyles\",write:!0},service:{name:\"style.variableStyles\"}}}})],z.prototype,\"variableStyles\",void 0),e([a({type:Boolean,json:{origins:{\"web-scene\":{name:\"layerDefinition.style.enableSlices\",write:!0},service:{name:\"style.enableSlices\"}}}})],z.prototype,\"enableSlices\",void 0),e([a({type:Boolean,json:{origins:{\"web-scene\":{name:\"layerDefinition.style.enableSections\",write:!0},service:{name:\"style.enableSections\"}}}})],z.prototype,\"enableSections\",void 0),e([a({type:Boolean,json:{origins:{\"web-scene\":{name:\"layerDefinition.style.enableDynamicSections\",write:!0},service:{name:\"style.enableDynamicSections\"}}}})],z.prototype,\"enableDynamicSections\",void 0),e([a({type:Boolean,json:{origins:{\"web-scene\":{name:\"layerDefinition.style.enableIsosurfaces\",write:!0},service:{name:\"style.enableIsosurfaces\"}}}})],z.prototype,\"enableIsosurfaces\",void 0),e([a({type:F,json:{origins:{\"web-scene\":{name:\"layerDefinition.style.shading\",write:!0},service:{name:\"style.shading\"}}}})],z.prototype,\"shading\",void 0),e([a({type:[\"show\",\"hide\"]})],z.prototype,\"listMode\",void 0),e([a({type:Number,range:{min:0,max:1},nonNullable:!0,json:{read:!1,write:!1,origins:{\"web-scene\":{read:!1,write:!1},\"portal-item\":{read:!1,write:!1}}}})],z.prototype,\"opacity\",void 0),e([a({type:t.ofType(N)})],z.prototype,\"variables\",void 0),e([a({type:t.ofType(P)})],z.prototype,\"volumes\",void 0),e([a({type:A})],z.prototype,\"index\",void 0),e([a({type:Number,json:{name:\"layerDefinition.minScale\",read:!1,write:!1,origins:{service:{read:!1,write:!1}}}})],z.prototype,\"minScale\",void 0),e([a({type:Number,json:{name:\"layerDefinition.maxScale\",read:!1,write:!1,origins:{service:{read:!1,write:!1}}}})],z.prototype,\"maxScale\",void 0),e([a({json:{read:!1},readOnly:!0})],z.prototype,\"type\",void 0),e([a({readOnly:!0,json:{name:\"serviceVersion\"}})],z.prototype,\"version\",void 0),e([p(\"service\",\"version\")],z.prototype,\"readVersion\",null),e([a({type:d})],z.prototype,\"fullExtent\",void 0),e([p(\"service\",\"fullExtent\",[\"fullExtent\"])],z.prototype,\"readFullExtent\",null),e([a({readOnly:!0,clonable:!1,json:{read:!1}})],z.prototype,\"voxelFields\",null),e([a(I)],z.prototype,\"popupEnabled\",void 0),e([a({readOnly:!0})],z.prototype,\"defaultPopupTemplate\",null),z=e([c(R)],z);const _=z;export{_ as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAImP,IAAMA,KAAE,EAAE;AAAV,IAAYC,KAAEC,GAAE;AAAhB,IAAkBC,KAAED,GAAE;AAAtB,IAAwBA,KAAEA,GAAE;AAA5B,IAA8BE,KAAEC,GAAE,GAAE,GAAE,CAAC;AAAvC,IAAyC,IAAEA,GAAE,GAAE,GAAE,CAAC;AAAlD,IAAoD,IAAEA,GAAE,GAAE,GAAE,CAAC;AAAE,SAASC,GAAEC,IAAE;AAAC,EAAAF,GAAEL,IAAEO,EAAC,GAAE,EAAEP,IAAEA,EAAC;AAAE,QAAMQ,KAAE,KAAK,MAAMR,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAES,KAAE,EAAEP,GAAE,GAAEE,IAAE,CAACI,EAAC;AAAE,EAAAE,GAAEV,IAAEA,IAAES,EAAC;AAAE,QAAMJ,KAAE,KAAG,KAAK,MAAML,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,SAAM,CAAC,EAAEQ,EAAC,IAAE,KAAI,EAAEH,EAAC,IAAE,EAAE;AAAC;AAAC,SAAS,EAAEM,IAAEH,IAAE;AAAC,SAAO,EAAEL,IAAEC,IAAEC,GAAEM,KAAE,GAAG,CAAC,GAAE,EAAET,IAAE,GAAEG,GAAEG,KAAE,EAAE,CAAC,GAAEI,GAAEX,IAAEE,IAAED,EAAC,GAAEG,GAAEL,IAAE,CAAC,GAAEU,GAAEV,IAAEA,IAAEC,EAAC,GAAE,EAAED,IAAEA,EAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAC;;;ACAyG,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAYa,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,MAAG,KAAK,QAAM,IAAG,KAAK,SAAO,MAAK,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,QAAG,CAAC,MAAM,QAAQ,KAAK,MAAM,KAAG,MAAI,KAAK,OAAO,OAAO,QAAO;AAAE,UAAK,CAACA,IAAEC,EAAC,IAAEC,GAAE,KAAK,MAAM;AAAE,WAAOC,GAAE,UAAU,EAAEH,EAAC,GAAE,GAAE,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,UAAMC,KAAE,EAAED,IAAE,KAAK,IAAI;AAAE,SAAK,KAAK,UAASC,EAAC,GAAE,KAAK,KAAK,eAAcD,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,QAAG,CAAC,MAAM,QAAQ,KAAK,MAAM,KAAG,MAAI,KAAK,OAAO,OAAO,QAAO;AAAE,UAAK,CAACA,IAAEC,EAAC,IAAEC,GAAE,KAAK,MAAM;AAAE,WAAOC,GAAE,UAAU,EAAEF,EAAC,GAAE,GAAE,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,KAAKD,IAAE;AAAC,UAAMC,KAAE,EAAE,KAAK,aAAYD,EAAC;AAAE,SAAK,KAAK,UAASC,EAAC,GAAE,KAAK,KAAK,QAAOD,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,OAAG,OAAM,EAAC,KAAI,GAAE,KAAI,IAAG,EAAC,CAAC,GAAEG,GAAG,CAAAH,OAAGG,GAAE,UAAU,EAAEH,EAAC,GAAE,GAAE,IAAE,CAAE,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,OAAG,OAAM,EAAC,KAAI,GAAE,KAAI,IAAG,EAAC,CAAC,GAAEG,GAAG,CAAAH,OAAGG,GAAE,UAAU,EAAEH,EAAC,GAAE,GAAE,IAAE,CAAE,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,IAAE,EAAE,CAACI,GAAE,8BAA8B,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;;;ACAriC,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,MAAG,KAAK,OAAK,MAAK,KAAK,KAAG,MAAK,KAAK,QAAM,IAAG,KAAK,SAAO,MAAK,KAAK,QAAM,MAAK,KAAK,cAAY,MAAK,KAAK,SAAO,MAAK,KAAK,SAAO,GAAE,KAAK,aAAW;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,QAAG,CAAC,MAAM,QAAQ,KAAK,MAAM,KAAG,MAAI,KAAK,OAAO,OAAO,QAAO;AAAE,UAAK,CAACC,IAAEC,EAAC,IAAEC,GAAE,KAAK,MAAM;AAAE,WAAOC,GAAE,UAAU,EAAEH,EAAC,GAAE,GAAE,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,QAAG,CAAC,MAAM,QAAQ,KAAK,MAAM,KAAG,MAAI,KAAK,OAAO,OAAO,QAAO;AAAE,UAAK,CAACA,IAAEC,EAAC,IAAEC,GAAE,KAAK,MAAM;AAAE,WAAOC,GAAE,UAAU,EAAEF,EAAC,GAAE,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,MAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAKG,GAAC,EAAC,GAAE,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,GAAE,EAAE,EAAC,SAAQ,CAAC,WAAW,GAAE,MAAK,YAAW,QAAO,YAAW,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,OAAG,UAAS,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,IAAG,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,OAAG,UAAS,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,IAAG,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACC,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,SAAQ,GAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,IAAE,EAAE,CAACC,GAAE,gCAAgC,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;;;ACAz/D,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,KAAG,KAAK,iBAAe;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,MAAK,EAAC,SAAQ,KAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,MAAK,EAAC,SAAQ,KAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAEA,KAAE,EAAE,CAACC,GAAE,sCAAsC,CAAC,GAAED,EAAC;AAAE,IAAME,KAAEF;;;ACAnX,IAAIG,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,aAAW,MAAK,KAAK,YAAU,OAAG,KAAK,SAAO,GAAE,KAAK,SAAO,GAAE,KAAK,QAAM,GAAE,KAAK,OAAK;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,YAAW,YAAY,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAACC,GAAE,+BAA+B,CAAC,GAAED,EAAC;AAAE,IAAME,KAAEF;;;ACAzmB,IAAIG,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,KAAG,MAAK,KAAK,cAAY,IAAG,KAAK,OAAK,MAAK,KAAK,iBAAe,MAAK,KAAK,kBAAgB,MAAK,KAAK,OAAK,IAAG,KAAK,WAAS,GAAE,KAAK,OAAK;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,wBAAuB,+BAA8B,qBAAoB,8BAA8B,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAACE,GAAE,iCAAiC,CAAC,GAAEF,EAAC;AAAE,IAAMC,KAAED;;;ACAv3B,IAAIG,KAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAMA,GAAE,UAAU,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,KAAK,QAAM,GAAE,KAAK,UAAQ,MAAG,KAAK,QAAM,IAAG,KAAK,cAAY;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,IAAE,MAAK,EAAC,MAAK,CAAC,CAAC,GAAE,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,MAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,OAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAEA,KAAE,EAAE,CAACC,GAAE,mCAAmC,CAAC,GAAED,EAAC;AAAE,IAAMC,KAAED;;;ACAhnB,IAAIE,KAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAM,MAAK,KAAK,WAAS;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,MAAK,EAAC,MAAK,CAAC,CAAC,GAAE,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAACE,GAAE,kCAAkC,CAAC,GAAEF,EAAC;AAAE,IAAMC,KAAED;;;ACA9X,IAAIG,MAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,GAAE,KAAK,WAAS;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,SAAQ,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,YAAW,MAAM,GAAEA,MAAE,EAAE,CAACC,GAAE,oCAAoC,CAAC,GAAED,GAAC;AAAE,IAAME,KAAEF;;;ACA1V,IAAIG,MAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,OAAG,KAAK,QAAM;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,OAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,SAAQ,MAAM,GAAEA,MAAE,EAAE,CAACC,GAAE,oCAAoC,CAAC,GAAED,GAAC;AAAE,IAAMC,KAAED;;;ACAI,IAAIE;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,OAAK,CAAC,IAAE;AAAM,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAIE,KAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,gBAAc,MAAK,KAAK,eAAa,MAAK,KAAK,cAAY,MAAK,KAAK,gBAAc,KAAI,KAAK,aAAW,KAAI,EAAE,OAAOE,EAAC,MAAG,KAAK,eAAa,KAAI,EAAE,OAAOC,EAAC;AAAA,EAAE;AAAA,EAAC,IAAI,WAAWH,IAAE;AAAC,SAAK,KAAK,cAAaI,GAAEJ,IAAE,KAAK,KAAK,YAAY,GAAE,EAAE,OAAOE,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAaF,IAAE;AAAC,SAAK,KAAK,gBAAeI,GAAEJ,IAAE,KAAK,KAAK,cAAc,GAAE,EAAE,OAAOG,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBH,IAAEK,IAAEC,IAAE;AAAC,QAAIC,KAAEP;AAAE,WAAK,EAAEO,KAAE,KAAGF,GAAEE,EAAC,EAAE,SAAOD,MAAGD,GAAEE,EAAC,EAAE,SAAOR,GAAE,OAAM;AAAC,QAAIS,KAAER;AAAE,UAAMG,KAAEE,GAAE;AAAO,WAAK,EAAEG,KAAEL,MAAGE,GAAEG,EAAC,EAAE,SAAOF,MAAGD,GAAEG,EAAC,EAAE,SAAOT,GAAE,OAAM;AAAC,WAAM,CAACQ,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,6BAA4B;AAAC,UAAMR,KAAE,CAAC;AAAE,QAAG,KAAK,WAAW,SAAO,EAAE,QAAOA;AAAE,UAAMM,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEC,KAAE;AAAK,eAAUH,MAAK,KAAK,YAAW;AAAC,UAAG,CAACA,GAAE,MAAM,QAAOL;AAAE,MAAAO,GAAE,KAAK,EAAC,OAAM,EAAC,GAAEF,GAAE,MAAM,GAAE,GAAEA,GAAE,MAAM,GAAE,GAAEA,GAAE,MAAM,GAAE,GAAE,KAAK,MAAM,OAAK,IAAEA,GAAE,MAAM,EAAE,EAAC,GAAE,UAASA,GAAE,UAAS,MAAKN,GAAE,MAAK,CAAC;AAAA,IAAC;AAAC,QAAG,MAAI,KAAK,aAAa,OAAO,YAAUM,MAAKE,GAAE,CAAAD,GAAE,KAAK,EAAC,OAAMD,GAAE,OAAM,UAASA,GAAE,SAAQ,CAAC;AAAA,SAAM;AAAC,iBAAUA,MAAK,KAAK,cAAa;AAAC,cAAML,KAAES,GAAEJ,GAAE,UAAS,GAAE,CAAC,GAAEC,KAAE,KAAK,MAAM,MAAIG,GAAE,IAAEJ,GAAE,SAAQ,GAAE,CAAC,CAAC;AAAE,YAAIF,KAAE;AAAG,mBAAUE,MAAKE,GAAE,KAAGF,GAAE,SAAON,GAAE,SAAO,KAAK,IAAIM,GAAE,WAASL,EAAC,IAAEQ,IAAE;AAAC,UAAAH,GAAE,MAAM,IAAEC,IAAED,GAAE,OAAKN,GAAE,MAAKI,KAAE;AAAG;AAAA,QAAK;AAAC,QAAAA,MAAGI,GAAE,KAAK,EAAC,OAAM,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAED,GAAC,GAAE,UAASD,GAAE,UAAS,MAAKN,GAAE,MAAK,CAAC;AAAA,MAAC;AAAC,MAAAQ,GAAE,KAAM,CAACP,IAAEK,OAAIL,GAAE,WAASK,GAAE,WAAS,KAAG,CAAE;AAAE,YAAML,KAAEO,GAAE;AAAO,eAAQF,KAAE,GAAEA,KAAEL,IAAE,EAAEK,IAAE;AAAC,cAAMC,KAAEC,GAAEF,EAAC;AAAE,YAAGC,GAAE,SAAOP,GAAE,KAAK,KAAGO,GAAE,SAAOP,GAAE,OAAM;AAAC,gBAAK,CAACS,IAAEL,EAAC,IAAE,KAAK,gBAAgBE,IAAEE,IAAER,GAAE,KAAK;AAAE,cAAG,OAAKS,MAAGL,OAAIH,IAAE;AAAC,kBAAMA,MAAGM,GAAE,WAASC,GAAEC,EAAC,EAAE,aAAWD,GAAEJ,EAAC,EAAE,WAASI,GAAEC,EAAC,EAAE;AAAU,YAAAF,GAAE,MAAM,IAAE,KAAK,MAAM,EAAEC,GAAEC,EAAC,EAAE,MAAM,GAAED,GAAEJ,EAAC,EAAE,MAAM,GAAEH,EAAC,CAAC;AAAA,UAAC,MAAM,CAAAM,GAAE,MAAM,IAAE,OAAKE,KAAED,GAAEC,EAAC,EAAE,MAAM,IAAED,GAAEJ,EAAC,EAAE,MAAM;AAAA,QAAC,OAAK;AAAC,gBAAK,CAACK,IAAEL,EAAC,IAAE,KAAK,gBAAgBE,IAAEE,IAAER,GAAE,KAAK;AAAE,cAAG,OAAKS,MAAGL,OAAIH,IAAE;AAAC,kBAAMA,MAAGM,GAAE,WAASC,GAAEC,EAAC,EAAE,aAAWD,GAAEJ,EAAC,EAAE,WAASI,GAAEC,EAAC,EAAE,WAAUH,KAAEE,GAAEC,EAAC,EAAE,OAAME,MAAEH,GAAEJ,EAAC,EAAE;AAAM,aAAC,KAAI,KAAI,GAAG,EAAE,QAAS,CAAAI,OAAG;AAAC,cAAAD,GAAE,MAAMC,EAAC,IAAE,KAAK,MAAM,EAAEF,GAAEE,EAAC,GAAEG,IAAEH,EAAC,GAAEP,EAAC,CAAC;AAAA,YAAC,CAAE;AAAA,UAAC,MAAK,EAAC,KAAI,KAAI,GAAG,EAAE,QAAQ,OAAKQ,KAAE,CAAAR,OAAG;AAAC,YAAAM,GAAE,MAAMN,EAAC,IAAEO,GAAEC,EAAC,EAAER,EAAC;AAAA,UAAC,IAAE,CAAAA,OAAG;AAAC,YAAAM,GAAE,MAAMN,EAAC,IAAEO,GAAEJ,EAAC,EAAEH,EAAC;AAAA,UAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,iBAAUK,MAAKE,GAAE,CAAAD,GAAE,KAAK,EAAC,OAAMD,GAAE,OAAM,UAASA,GAAE,SAAQ,CAAC;AAAA,IAAC;AAAC,IAAAC,GAAE,CAAC,EAAE,WAAS,GAAEA,GAAEA,GAAE,SAAO,CAAC,EAAE,WAAS;AAAE,QAAIH,KAAE,GAAED,KAAE;AAAE,aAAQS,KAAE,GAAEA,KAAE,KAAK,eAAc,EAAEA,IAAE;AAAC,YAAMJ,KAAEI,KAAE,KAAK;AAAc,aAAKJ,KAAED,GAAEJ,EAAC,EAAE,WAAU,CAAAC,KAAED;AAAI,YAAMM,MAAGD,KAAED,GAAEH,EAAC,EAAE,aAAWG,GAAEJ,EAAC,EAAE,WAASI,GAAEH,EAAC,EAAE,WAAUM,KAAEH,GAAEH,EAAC,EAAE,OAAMS,KAAEN,GAAEJ,EAAC,EAAE,OAAMW,KAAE,IAAIX;AAAE,OAAC,KAAI,KAAI,GAAG,EAAE,QAAS,CAAAF,OAAG;AAAC,QAAAa,GAAEb,EAAC,IAAE,KAAK,MAAM,EAAES,GAAET,EAAC,GAAEY,GAAEZ,EAAC,GAAEQ,EAAC,CAAC;AAAA,MAAC,CAAE,GAAEK,GAAE,IAAEJ,GAAE,IAAE,EAAEA,GAAE,GAAEG,GAAE,GAAEJ,EAAC,IAAE,KAAI,GAAE,CAAC,GAAER,GAAE,KAAKa,EAAC;AAAA,IAAC;AAAC,WAAOb;AAAA,EAAC;AAAA,EAAC,+BAA+BA,IAAEK,IAAE;AAAC,UAAMC,KAAE,KAAK;AAA2B,QAAG,KAAK,WAAW,SAAO,KAAG,CAAC,MAAM,QAAQ,KAAK,YAAY,KAAG,KAAK,aAAa,SAAO,KAAGA,GAAE,SAAO,IAAI,QAAO;AAAK,QAAIC,KAAE,KAAK,aAAa,CAAC,GAAEC,KAAE,KAAK,aAAa,CAAC;AAAE,QAAGD,KAAEC,IAAE;AAAC,YAAMR,KAAEO;AAAE,MAAAA,KAAEC,IAAEA,KAAER;AAAA,IAAC;AAAC,IAAAA,KAAES,GAAET,IAAEO,IAAEC,EAAC;AAAE,UAAML,KAAEG,GAAE,KAAK,OAAON,KAAEO,OAAIC,KAAED,OAAI,KAAK,gBAAc,EAAE,CAAC,EAAE,MAAM;AAAE,WAAOF,OAAIF,GAAE,IAAE,IAAGA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,UAAS,SAAS,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOC,EAAC,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,CAAC,KAAK,cAAY,KAAK,WAAW,SAAO,EAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOE,EAAC,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,aAAY,GAAE,OAAM,EAAC,SAAQ,MAAG,QAAO,cAAa,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,CAAC,KAAK,gBAAc,KAAK,aAAa,SAAO,EAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKQ,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACC,EAAC,GAAE,UAAS,OAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,8BAA6B,IAAI,GAAEA,KAAE,EAAE,CAACQ,GAAE,8CAA8C,CAAC,GAAER,EAAC;AAAE,IAAMa,KAAEb;;;ACAnsH,IAAIc,KAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAMA,GAAE,UAAU,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,KAAK,QAAM,GAAE,KAAK,UAAQ,MAAG,KAAK,QAAM;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,IAAE,MAAK,EAAC,MAAK,CAAC,CAAC,GAAE,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,MAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAEA,KAAE,EAAE,CAACC,GAAE,oCAAoC,CAAC,GAAED,EAAC;AAAE,IAAMC,KAAED;;;ACAhb,IAAIE;AAAE,IAAIC,KAAED,MAAE,cAAc,EAAC;AAAA,EAAC,YAAYE,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,GAAE,KAAK,QAAM,IAAG,KAAK,mBAAiB,MAAK,KAAK,eAAa,MAAK,KAAK,cAAY,MAAK,KAAK,eAAa,KAAI,EAAE,OAAOC,EAAC,MAAG,KAAK,cAAY,KAAI,EAAE,OAAOA,EAAC;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIH,IAAE,EAAC,YAAW,KAAK,YAAW,OAAM,KAAK,OAAM,kBAAiB,EAAE,KAAK,gBAAgB,GAAE,cAAa,EAAE,KAAK,YAAY,GAAE,aAAY,EAAE,KAAK,WAAW,EAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKG,IAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,KAAK,gBAAc,KAAK,aAAa,SAAO,EAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOE,EAAC,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,CAAC,KAAK,gBAAc,KAAK,aAAa,SAAO,EAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOE,EAAC,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,iBAAgB;AAAC,QAAMD,KAAE,CAAC,KAAK,gBAAc,KAAK,aAAa,SAAO,GAAEG,KAAE,CAAC,CAAC,KAAK,eAAa,KAAK,YAAY,SAAO;AAAE,SAAM,EAAC,SAAQH,MAAGG,GAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEJ,GAAE,WAAU,eAAc,MAAM,GAAEA,KAAED,MAAE,EAAE,CAACG,GAAE,sCAAsC,CAAC,GAAEF,EAAC;AAAE,IAAMK,KAAEL;;;ACAv1C,IAAIM,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAO;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAE,EAAE,CAACC,GAAE,yCAAyC,CAAC,GAAED,EAAC;AAAE,IAAME,MAAEF;;;ACA7M,IAAIG,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAM,GAAE,KAAK,SAAO;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAE,EAAE,CAACC,GAAE,uCAAuC,CAAC,GAAED,EAAC;AAAE,IAAME,MAAEF;;;ACAlM,IAAIG,MAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,mBAAiB,MAAK,KAAK,eAAa,MAAG,KAAK,oBAAkB,OAAG,KAAK,QAAM,MAAK,KAAK,OAAK,MAAK,KAAK,WAAS,MAAK,KAAK,iBAAe,MAAK,KAAK,OAAK,GAAE,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,IAAI,YAAW;AAAC,YAAO,QAAM,KAAK,oBAAkB,WAAS,KAAK,qBAAmB,SAAO,KAAK;AAAA,EAAc;AAAA,EAAC,WAAU;AAJtwB;AAIuwB,WAAO,KAAK,YAAU,CAAC,KAAK,eAAe,QAAO,KAAK,eAAe,SAAO,KAAK,eAAe,SAAO,KAAK,OAAK,EAAE,IAAE,MAAM,SAAQ,UAAK,qBAAL,mBAAuB,MAAM,KAAG,KAAK,iBAAiB,OAAO,SAAO,IAAE,CAAC,KAAK,iBAAiB,OAAO,CAAC,GAAE,KAAK,iBAAiB,OAAO,KAAK,iBAAiB,OAAO,SAAO,CAAC,CAAC,IAAE,CAAC,GAAE,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,KAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,KAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,aAAY,IAAI,GAAEA,MAAE,EAAE,CAACC,GAAE,kCAAkC,CAAC,GAAED,GAAC;AAAE,IAAMC,KAAED;;;ACArnC,IAAME,KAAE;AAAR,IAAwCC,KAAE,EAAE,UAAUD,EAAC;AAAE,IAAIE,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,KAAG,GAAE,KAAK,aAAW,MAAK,KAAK,mBAAiB,EAAE;AAAA,EAAK;AAAA,EAAC,IAAI,aAAY;AAAC,QAAG,CAAC,KAAK,WAAW,QAAM;AAAG,QAAG,CAAC,MAAM,QAAQ,KAAK,UAAU,EAAE,QAAM;AAAG,QAAG,MAAI,KAAK,WAAW,OAAO,QAAM;AAAG,aAAQA,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,KAAG,KAAK,WAAWA,EAAC,EAAE,OAAK,EAAE,QAAOA;AAAE,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,UAAS;AAAC,WAAM,CAAC,CAAC,KAAK,eAAa,CAAC,CAAC,MAAM,QAAQ,KAAK,UAAU,MAAI,MAAI,KAAK,WAAW,WAAS,EAAE,KAAK,WAAW,CAAC,EAAE,OAAK,KAAG,KAAK,WAAW,CAAC,EAAE,OAAK,MAAI,EAAE,OAAK,KAAK,cAAY,KAAK,WAAW,KAAK,UAAU,EAAE,OAAK;AAAA,EAAK;AAAA,EAAC,IAAI,uBAAsB;AAAC,QAAG,CAAC,KAAK,WAAS,UAAQ,KAAK,WAAW,QAAM,CAAC,GAAE,GAAE,CAAC;AAAE,UAAMA,KAAE,KAAK,WAAW,CAAC,EAAE,SAAS,GAAEC,KAAE,KAAK,WAAW,CAAC,EAAE,SAAS,GAAEC,KAAE,KAAK,WAAW,CAAC,GAAEC,KAAED,GAAE,YAAUA,GAAE,SAAS,IAAE,CAAC,GAAEA,GAAE,IAAI;AAAE,WAAM,CAACF,GAAE,CAAC,GAAEC,GAAE,CAAC,GAAEE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,8BAA6B;AAAC,QAAG,CAAC,KAAK,WAAS,UAAQ,KAAK,WAAW,QAAM,CAAC,GAAE,GAAE,CAAC;AAAE,UAAMH,KAAE,KAAK,WAAW,CAAC,EAAE,SAAS,GAAEC,KAAE,KAAK,WAAW,CAAC,EAAE,SAAS,GAAEC,KAAE,KAAK,WAAW,CAAC,GAAEC,KAAED,GAAE,YAAUA,GAAE,SAAS,IAAE,CAAC,GAAEA,GAAE,IAAI,GAAEE,KAAE,CAAC,KAAK,aAAa,CAAC,GAAE,KAAK,aAAa,CAAC,GAAE,KAAK,aAAa,CAAC,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAD,GAAEC,EAAC,IAAE,IAAED,GAAEC,EAAC,IAAE,IAAED,GAAEC,EAAC,KAAG;AAAE,WAAOH,GAAE,aAAW,CAACA,GAAE,iBAAeE,GAAE,CAAC,KAAG,KAAI,EAAEJ,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGI,GAAE,CAAC,IAAGH,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGG,GAAE,CAAC,IAAGD,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGC,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,QAAG,KAAK,SAAQ;AAAC,YAAMJ,KAAE,KAAK,WAAW,CAAC,EAAE,OAAK,GAAEC,KAAE,KAAK,WAAW,CAAC,EAAE,OAAK;AAAE,UAAG,CAACD,MAAGC,GAAE,QAAM;AAAM,UAAGD,MAAGC,GAAE,QAAM;AAAA,IAAM;AAAC,WAAM;AAAA,EAAK;AAAA,EAAC,IAAI,eAAc;AAAC,QAAG,CAAC,KAAK,QAAQ,QAAM,CAAC,GAAE,GAAE,CAAC;AAAE,UAAMD,KAAE,KAAK;AAAW,WAAM,CAAC,KAAK,WAAW,CAAC,EAAE,MAAK,KAAK,WAAW,CAAC,EAAE,MAAK,KAAK,WAAWA,EAAC,EAAE,IAAI;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAE;AAJ7tE;AAI8tE,QAAG,CAAC,KAAK,QAAQ,QAAM,CAAC,GAAE,GAAE,CAAC;AAAE,QAAG,UAAQ,KAAK,WAAW,QAAOF,GAAE,MAAM,4DAA4D,GAAE,CAAC,GAAE,GAAE,CAAC;AAAE,QAAG,CAAC,EAAE,KAAK,kBAAiBE,GAAE,gBAAgB,EAAE,QAAOF,GAAE,MAAM,wEAAwE,GAAE,CAAC,GAAE,GAAE,CAAC;AAAE,UAAMG,KAAEE,GAAEH,GAAE,GAAEA,GAAE,GAAEA,GAAE,KAAG,CAAC;AAAE,IAAAA,GAAEC,IAAEA,IAAE,KAAK,oBAAoB,GAAEA,GAAEA,IAAEA,IAAE,KAAK,2BAA2B;AAAE,UAAMC,KAAE,KAAK,WAAW,KAAK,UAAU;AAAE,QAAG,CAACA,GAAE,aAAW,MAAM,SAAQ,KAAAA,GAAE,qBAAF,mBAAoB,MAAM,KAAGA,GAAE,iBAAiB,OAAO,SAAO,GAAE;AAAC,YAAMC,KAAEH,GAAE,KAAG,GAAEI,KAAEF,GAAE,iBAAiB,QAAOG,KAAEH,GAAE,eAAa,IAAE,IAAGI,KAAEF,GAAE,OAAQ,CAACJ,IAAEC,OAAI,KAAK,IAAII,KAAEJ,KAAEE,EAAC,IAAE,KAAK,IAAIE,KAAEL,KAAEG,EAAC,IAAEF,KAAED,EAAE;AAAE,eAAQA,KAAE,GAAEA,KAAEI,GAAE,QAAO,EAAEJ,GAAE,KAAGI,GAAEJ,EAAC,MAAIM,IAAE;AAAC,QAAAL,GAAE,CAAC,IAAED;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,WAAM,CAACC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BD,IAAE;AAJ78F;AAI88F,QAAG,CAAC,KAAK,QAAQ,QAAO,IAAIO,GAAE,EAAC,GAAE,GAAE,GAAE,GAAE,kBAAiB,KAAK,iBAAgB,CAAC;AAAE,UAAMN,KAAED,GAAEA,EAAC;AAAE,QAAG,EAAEC,IAAEA,IAAE,KAAK,2BAA2B,GAAEO,GAAEP,IAAEA,IAAE,KAAK,oBAAoB,GAAE,UAAQ,KAAK,WAAW,QAAO,IAAIM,GAAE,EAAC,GAAEN,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiB,KAAK,iBAAgB,CAAC;AAAE,UAAMC,KAAE,KAAK,WAAW,KAAK,UAAU;AAAE,WAAOA,GAAE,aAAW,MAAM,SAAQ,KAAAA,GAAE,qBAAF,mBAAoB,MAAM,MAAIF,GAAE,CAAC,IAAE,IAAEC,GAAE,CAAC,IAAEC,GAAE,iBAAiB,OAAO,CAAC,IAAEF,GAAE,CAAC,IAAEE,GAAE,iBAAiB,OAAO,SAAOD,GAAE,CAAC,IAAEC,GAAE,iBAAiB,OAAOF,GAAE,CAAC,CAAC,IAAEC,GAAE,CAAC,IAAEC,GAAE,iBAAiB,OAAOA,GAAE,iBAAiB,OAAO,SAAO,CAAC,GAAEA,GAAE,iBAAeD,GAAE,CAAC,KAAG,MAAK,IAAIM,GAAE,EAAC,GAAEN,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiB,KAAK,iBAAgB,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACU,EAAC,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEV,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,MAAK,EAAC,SAAQ,MAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,OAAO,GAAE,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,+BAA8B,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,OAAM,QAAO,KAAK,GAAE,MAAK,EAAC,MAAK,EAAC,SAAQ,MAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,IAAI,GAAEA,KAAE,EAAE,CAACU,GAAEZ,EAAC,CAAC,GAAEE,EAAC;AAAE,IAAMW,KAAEX;;;ACAz+H,IAAIY;AAAE,IAAIC,KAAED,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,aAAW,GAAE,KAAK,YAAU,CAAC,IAAG,IAAG,EAAE,GAAE,KAAK,cAAY,GAAE,KAAK,WAAS,CAAC,GAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAME,KAAE,IAAIF;AAAE,WAAOE,GAAE,eAAa,KAAK,cAAYA,GAAE,gBAAc,KAAK,gBAAc,CAAC,CAAC,KAAK,cAAY,CAAC,CAAC,KAAK,aAAW,EAAE,CAAC,MAAM,QAAQ,KAAK,SAAS,KAAG,CAAC,MAAM,QAAQ,KAAK,QAAQ,OAAK,MAAI,KAAK,UAAU,UAAQ,MAAI,KAAK,SAAS,WAAS,OAAK,KAAK,UAAU,CAAC,KAAG,OAAK,KAAK,UAAU,CAAC,KAAG,OAAK,KAAK,UAAU,CAAC,MAAI,MAAI,KAAK,SAAS,CAAC,KAAG,MAAI,KAAK,SAAS,CAAC,KAAG,MAAI,KAAK,SAAS,CAAC;AAAA,EAAO;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAED,KAAE,EAAE,CAACG,GAAE,oCAAoC,CAAC,GAAEF,EAAC;AAAE,IAAMG,MAAEH;;;ACAlqB,IAAII,KAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,MAAG,KAAK,QAAM,IAAG,KAAK,SAAO,MAAK,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,QAAG,CAAC,MAAM,QAAQ,KAAK,MAAM,KAAG,MAAI,KAAK,OAAO,OAAO,QAAO;AAAE,UAAK,CAACA,IAAEC,EAAC,IAAEC,GAAE,KAAK,MAAM;AAAE,WAAOC,GAAE,UAAU,EAAEH,EAAC,GAAE,GAAE,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,UAAMC,KAAE,EAAED,IAAE,KAAK,IAAI;AAAE,SAAK,KAAK,UAASC,EAAC,GAAE,KAAK,KAAK,eAAcD,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,QAAG,CAAC,MAAM,QAAQ,KAAK,MAAM,KAAG,MAAI,KAAK,OAAO,OAAO,QAAO;AAAE,UAAK,CAACA,IAAEC,EAAC,IAAEC,GAAE,KAAK,MAAM;AAAE,WAAOC,GAAE,UAAU,EAAEF,EAAC,GAAE,GAAE,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,KAAKD,IAAE;AAAC,UAAMC,KAAE,EAAE,KAAK,aAAYD,EAAC;AAAE,SAAK,KAAK,UAASC,EAAC,GAAE,KAAK,KAAK,QAAOD,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,MAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,OAAG,OAAM,EAAC,KAAI,GAAE,KAAI,IAAG,EAAC,CAAC,GAAEI,GAAG,CAAAH,OAAGG,GAAE,UAAU,EAAEH,EAAC,GAAE,GAAE,IAAE,CAAE,CAAC,GAAED,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,OAAG,OAAM,EAAC,KAAI,GAAE,KAAI,IAAG,EAAC,CAAC,GAAEI,GAAG,CAAAH,OAAGG,GAAE,UAAU,EAAEH,EAAC,GAAE,GAAE,IAAE,CAAE,CAAC,GAAED,GAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAEA,KAAE,EAAE,CAACK,GAAE,uCAAuC,CAAC,GAAEL,EAAC;AAAE,IAAMM,KAAEN;;;ACApvC,IAAIO;AAAE,IAAIC,KAAED,MAAE,cAAc,EAAC;AAAA,EAAC,YAAYE,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,GAAE,KAAK,uBAAqB,GAAE,KAAK,mBAAiB,gBAAe,KAAK,iBAAe,GAAE,KAAK,SAAO,KAAI,EAAE,OAAOC,EAAC,MAAG,KAAK,kBAAgB,KAAI,EAAE,OAAOA,EAAC;AAAA,EAAE;AAAA,EAAC,IAAI,OAAOD,IAAE;AAAC,SAAK,KAAK,UAASE,GAAEF,IAAE,KAAK,KAAK,QAAQ,GAAE,EAAE,OAAOC,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAgBD,IAAE;AAAC,SAAK,KAAK,mBAAkBE,GAAEF,IAAE,KAAK,KAAK,iBAAiB,GAAE,EAAE,OAAOC,EAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIH,IAAE,EAAC,UAAS,KAAK,UAAS,sBAAqB,KAAK,sBAAqB,kBAAiB,KAAK,kBAAiB,gBAAe,KAAK,gBAAe,QAAO,EAAE,KAAK,MAAM,GAAE,iBAAgB,EAAE,KAAK,eAAe,EAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,GAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,kBAAiB,cAAc,GAAE,MAAK,EAAC,SAAQ,gBAAe,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,GAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOE,EAAC,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,CAAC,KAAK,UAAQ,KAAK,OAAO,SAAO,EAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOE,EAAC,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,CAAC,KAAK,mBAAiB,KAAK,gBAAgB,SAAO,EAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,mBAAkB,IAAI,GAAEA,KAAED,MAAE,EAAE,CAACK,GAAE,oCAAoC,CAAC,GAAEJ,EAAC;AAAE,IAAMK,KAAEL;;;ACA1Q,IAAM,IAAE;AAAR,IAAiCM,KAAE,EAAE,UAAU,CAAC;AAAE,IAAIC,KAAE,cAAcC,GAAEC,GAAEC,GAAE,EAAEC,GAAE,EAAEC,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,cAAY,IAAG,KAAK,uBAAqB,SAAQ,KAAK,gBAAc,MAAG,KAAK,QAAM,MAAK,KAAK,WAAS,MAAK,KAAK,oBAAkB,GAAE,KAAK,eAAa,MAAK,KAAK,aAAW,UAAS,KAAK,iBAAe,MAAK,KAAK,eAAa,MAAG,KAAK,iBAAe,MAAG,KAAK,wBAAsB,MAAG,KAAK,oBAAkB,MAAG,KAAK,UAAQ,IAAIJ,MAAE,KAAK,UAAQ,GAAE,KAAK,YAAU,IAAI,KAAE,KAAK,UAAQ,IAAI,KAAE,KAAK,QAAM,MAAK,KAAK,WAAS,GAAE,KAAK,WAAS,GAAE,KAAK,OAAK,SAAQ,KAAK,UAAQ,EAAC,OAAM,OAAO,KAAI,OAAM,OAAO,KAAI,eAAc,GAAE,GAAE,KAAK,aAAW,MAAK,KAAK,eAAa,MAAG,KAAK,gBAAc,MAAK,KAAK,OAAK,MAAK,KAAK,eAAa,KAAI,EAAE,OAAOK,EAAC,MAAG,KAAK,iBAAe,KAAI,EAAE,OAAOC,EAAC,MAAG,KAAK,WAAS,KAAI,EAAE,OAAOC,EAAC,OAAGH,MAAA,gBAAAA,GAAG,uBAAoB,KAAK,OAAK,EAAC,mBAAkB,KAAE;AAAA,EAAE;AAAA,EAAC,IAAI,IAAIA,IAAE;AAAC,SAAK,KAAK,OAAM,EAAEA,IAAEP,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKO,IAAE;AAAC,UAAMF,KAAE,EAAEE,EAAC,IAAEA,GAAE,SAAO,MAAKD,KAAE,KAAK,eAAe,EAAC,gBAAe,CAAC,eAAe,EAAC,GAAEC,EAAC,EAAE,MAAM,CAAC,EAAE,KAAM,MAAI,KAAK,cAAcF,EAAC,CAAE,EAAE,KAAM,MAAI,KAAK,cAAY,KAAK,GAAI;AAAE,WAAO,KAAK,oBAAoBC,EAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAEF,IAAE;AAAC,UAAM,KAAKE,IAAEF,EAAC;AAAE,eAAUC,MAAK,KAAK,QAAQ,CAAAA,GAAE,mBAAiB,KAAK;AAAA,EAAgB;AAAA,EAAC,YAAYC,IAAEF,IAAE;AAAC,WAAO,MAAM,mBAAmBE,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,QAAGA,GAAE,aAAWA,GAAE,cAAY,KAAK,qBAAqB,OAAM,IAAII,GAAE,wCAAuC,+CAA8C,EAAC,WAAUJ,GAAE,UAAS,CAAC;AAAE,QAAG,MAAM,KAAK,QAAQ,KAAK,KAAG,MAAM,KAAK,QAAQ,KAAK,KAAG,KAAK,QAAQ,QAAM,EAAE,OAAM,IAAII,GAAE,uCAAsC,qCAAoC,EAAC,gBAAe,KAAK,QAAQ,eAAc,mBAAkB,MAAK,CAAC;AAAE,QAAG,KAAK,QAAQ,QAAM,EAAE,OAAM,IAAIA,GAAE,iCAAgC,+BAA8B,EAAC,gBAAe,KAAK,QAAQ,eAAc,mBAAkB,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeJ,IAAEF,IAAEC,IAAE;AAAC,QAAG,QAAMC,MAAG,YAAU,OAAOA,IAAE;AAAC,YAAMK,KAAEC,GAAE,SAASN,IAAED,EAAC;AAAE,UAAG,MAAIM,GAAE,QAAM,MAAIA,GAAE,QAAM,MAAM,QAAQP,GAAE,OAAO,GAAE;AAAC,cAAME,KAAEO,GAAE,SAAST,GAAE,QAAQ,CAAC,CAAC;AAAE,YAAGE,GAAE,WAAS,UAAQA,GAAE,YAAW;AAAC,gBAAMF,KAAEE,GAAE,WAAW,CAAC;AAAE,cAAGF,GAAE,WAAU;AAAC,gBAAIE,KAAEF,GAAE,eAAe,QAAOC,KAAED,GAAE,eAAe,SAAOA,GAAE,eAAe,SAAOA,GAAE,OAAK;AAAG,gBAAGE,KAAED,IAAE;AAAC,oBAAMD,KAAEE;AAAE,cAAAA,KAAED,IAAEA,KAAED;AAAA,YAAC;AAAC,YAAAO,GAAE,OAAKL,IAAEK,GAAE,OAAKN;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,aAAOM;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,UAAML,KAAE,CAAC,IAAIQ,GAAE,EAAC,MAAK,sBAAqB,OAAM,SAAQ,QAAO,MAAK,UAAS,OAAG,QAAO,KAAI,MAAK,SAAQ,CAAC,GAAE,IAAIA,GAAE,EAAC,MAAK,8BAA6B,OAAM,YAAW,QAAO,MAAK,UAAS,OAAG,QAAO,KAAI,MAAK,SAAQ,CAAC,GAAE,IAAIA,GAAE,EAAC,MAAK,kBAAiB,OAAM,kBAAiB,QAAO,MAAK,UAAS,OAAG,QAAO,KAAI,MAAK,SAAQ,CAAC,CAAC,GAAEV,KAAE,KAAK,UAAU,IAAI;AAAE,QAAG,EAAEA,EAAC,GAAE;AAAC,UAAG,WAASA,GAAE,cAAY,UAAQA,GAAE,YAAW;AAAC,cAAMA,KAAE,IAAIU,GAAE,EAAC,MAAK,0BAAyB,OAAM,cAAa,QAAO,MAAK,UAAS,OAAG,QAAO,KAAI,MAAK,SAAQ,CAAC;AAAE,QAAAR,GAAE,KAAKF,EAAC;AAAE,cAAMC,KAAE,IAAIS,GAAE,EAAC,MAAK,2BAA0B,OAAM,eAAc,QAAO,MAAK,UAAS,OAAG,QAAO,KAAI,MAAK,SAAQ,CAAC;AAAE,QAAAR,GAAE,KAAKD,EAAC;AAAA,MAAC;AAAC,UAAG,UAAQD,GAAE,YAAW;AAAC,cAAMA,KAAE,IAAIU,GAAE,EAAC,MAAK,sBAAqB,OAAM,SAAQ,QAAO,MAAK,UAAS,OAAG,QAAO,KAAI,MAAK,SAAQ,CAAC;AAAE,QAAAR,GAAE,KAAKF,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,UAAMF,KAAE,KAAK,aAAYC,KAAE,KAAK;AAAM,WAAOH,GAAE,EAAC,QAAOE,IAAE,OAAMC,GAAC,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAJh1J;AAIi1J,UAAMA,KAAE,EAAC,WAAU,KAAK,sBAAqB,SAAQ,KAAK,QAAQ,eAAc,MAAK,KAAK,OAAM,kBAAiB,KAAK,kBAAiB,YAAW,KAAK,YAAW,SAAQ,KAAK,QAAQ,OAAO,GAAE,WAAU,KAAK,UAAU,OAAO,GAAE,QAAM,UAAK,UAAL,mBAAY,UAAS,UAAS,KAAK,YAAY,GAAE,OAAM,EAAC,cAAa,KAAK,gBAAgB,GAAE,mBAAkB,KAAK,mBAAkB,YAAW,KAAK,YAAW,gBAAe,KAAK,kBAAkB,GAAE,gBAAe,KAAK,gBAAe,uBAAsB,KAAK,uBAAsB,mBAAkB,KAAK,mBAAkB,cAAa,KAAK,cAAa,SAAQ,KAAK,QAAO,EAAC;AAAE,WAAOA,GAAE,WAAO,UAAK,UAAL,mBAAY,aAAU,KAAK,UAAUA,EAAC,IAAE;AAAA,EAAE;AAAA,EAAC,iBAAiBA,IAAE;AAAC,QAAIF,KAAE;AAAG,IAAAA,KAAE,EAAEE,EAAC,IAAEA,KAAE,KAAK;AAAkB,QAAG,CAAC,KAAK,kBAAgB,OAAKF,GAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,eAAe,UAAW,CAAAC,OAAGA,GAAE,eAAaF,EAAE;AAAE,WAAOC,KAAE,IAAE,OAAK,KAAK,eAAe,UAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,QAAIF,KAAE;AAAG,QAAGA,KAAE,EAAEE,EAAC,IAAEA,KAAE,KAAK,mBAAkB,CAAC,KAAK,aAAW,OAAKF,GAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,UAAU,UAAW,CAAAC,OAAGA,GAAE,OAAKF,EAAE;AAAE,WAAOC,KAAE,IAAE,OAAK,KAAK,UAAU,UAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUC,IAAE;AAAC,UAAMF,KAAE,KAAK,YAAYE,EAAC;AAAE,WAAO,EAAEF,EAAC,IAAE,KAAK,QAAQ,KAAM,CAAC,EAAC,IAAGE,GAAC,MAAIA,OAAIF,GAAE,QAAS,IAAE;AAAA,EAAI;AAAA,EAAC,eAAeE,IAAE;AAAC,UAAMF,KAAE,KAAK,YAAYE,EAAC;AAAE,WAAO,EAAEF,EAAC,IAAE,KAAK,aAAa,KAAM,CAAC,EAAC,UAASE,GAAC,MAAIA,OAAIF,GAAE,QAAS,IAAE;AAAA,EAAI;AAAA,EAAC,+BAA+BE,IAAEF,IAAEC,IAAE;AAJtoM;AAIuoM,UAAMM,KAAE,KAAK,YAAYL,EAAC;AAAE,QAAG,EAAEK,EAAC,KAAG,mBAAe,KAAAA,GAAE,oBAAF,mBAAmB,YAAW,QAAO;AAAK,QAAG,CAAC,KAAK,eAAe,QAAO;AAAK,UAAMI,KAAE,KAAK,eAAe,UAAW,CAAAX,OAAGA,GAAE,eAAaE,EAAE;AAAE,QAAGS,KAAE,EAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,eAAe,UAAUD,EAAC;AAAE,WAAOC,GAAE,mBAAiBA,GAAE,iBAAiB,+BAA+BZ,IAAEC,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,cAAa;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAUF,MAAK,KAAK,SAAS,CAAAE,GAAE,KAAK,IAAIG,GAAE,EAAC,SAAQL,GAAE,SAAQ,MAAKA,GAAE,MAAK,IAAGA,GAAE,IAAG,OAAMA,GAAE,OAAM,QAAOA,GAAE,QAAO,OAAMA,GAAE,OAAM,aAAYA,GAAE,aAAY,QAAOA,GAAE,QAAO,QAAOA,GAAE,QAAO,YAAWA,GAAE,WAAU,CAAC,CAAC;AAAE,WAAOE;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,UAAMA,KAAE,CAAC;AAAE,eAAUF,MAAK,KAAK,gBAAe;AAAC,YAAMC,KAAE,KAAK,aAAaD,EAAC;AAAE,UAAG,EAAEC,EAAC,GAAE;AAAC,cAAMM,KAAEP,GAAE,MAAM;AAAE,QAAAO,GAAE,YAAY,SAAO,MAAIA,GAAE,cAAYA,GAAE,YAAY,MAAM,GAAE,CAAC,GAAEZ,GAAE,MAAM,4DAA4D;AAAG,mBAAUO,MAAKK,GAAE,YAAY,KAAG,CAACL,GAAE,aAAY;AAAC,gBAAMF,KAAE,KAAK,+BAA+BO,GAAE,YAAWL,GAAE,OAAM,KAAE;AAAE,mBAAOF,MAAGA,GAAE,OAAOE,GAAE,KAAK,MAAIA,GAAE,QAAMF;AAAA,QAAE;AAAC,YAAG,iBAAeC,GAAE,gBAAgB,WAAW,EAAC,SAAOM,GAAE,oBAAkBA,GAAE,iBAAiB,WAAW,SAAO,MAAIZ,GAAE,MAAM,mCAAmCM,GAAE,EAAE,gHAAgH,GAAE,SAAOM,GAAE,qBAAmB,MAAM,QAAQA,GAAE,iBAAiB,YAAY,KAAG,MAAIA,GAAE,iBAAiB,aAAa,WAASZ,GAAE,MAAM,mCAAmCM,GAAE,EAAE,sJAAsJ,GAAEM,GAAE,iBAAiB,eAAa,CAAC,GAAE,CAAC,GAAEA,GAAE,iBAAiB,WAAW,UAAU;AAAA,iBAAY,eAAaN,GAAE,gBAAgB,WAAW,KAAG,MAAID,GAAE,aAAa,OAAO,CAAAL,GAAE,MAAM,mCAAmCM,GAAE,EAAE,6EAA6E;AAAA,YAAO,YAAUC,MAAKF,GAAE,aAAa,UAAOE,GAAE,SAAO,WAASA,GAAE,SAAO,SAAOA,GAAE,SAAO,WAASA,GAAE,UAAQA,GAAE,QAAMA,GAAE,MAAM,SAAS;AAAG,QAAAA,GAAE,KAAKK,EAAC;AAAA,MAAC,MAAM,CAAAZ,GAAE,MAAM,oBAAoBK,GAAE,UAAU,4EAA4E;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,UAAMA,KAAE,CAAC;AAAE,eAAUF,MAAK,KAAK,cAAa;AAAC,YAAMC,KAAE,KAAK,uBAAuBD,GAAE,QAAQ;AAAE,UAAG,EAAEC,EAAC,GAAE;AAAC,cAAMM,KAAEP,GAAE,MAAM;AAAE,mBAAUE,MAAKK,GAAE,OAAO,MAAK,cAAcL,IAAE,CAAC,GAAE,GAAED,GAAE,UAAU,GAAEA,GAAE,UAAU,MAAIC,GAAE,UAAQ,OAAGA,GAAE,QAAM;AAAW,mBAAUA,MAAKK,GAAE,gBAAgB,MAAK,cAAcL,IAAE,CAAC,GAAE,GAAED,GAAE,UAAU,GAAEA,GAAE,UAAU,MAAIC,GAAE,UAAQ,OAAGA,GAAE,QAAM;AAAW,QAAAA,GAAE,KAAKK,EAAC;AAAA,MAAC,MAAM,CAAAZ,GAAE,MAAM,kBAAkBK,GAAE,QAAQ,wEAAwE;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAMF,KAAEE,GAAE;AAAW,eAAUD,MAAK,KAAK,UAAU,KAAGA,GAAE,OAAKD,GAAE,QAAOC;AAAE,WAAO;AAAA,EAAI;AAAA,EAAC,uBAAuBC,IAAE;AAAC,eAAUF,MAAK,KAAK,QAAQ,KAAGA,GAAE,OAAKE,GAAE,QAAOF;AAAE,WAAO;AAAA,EAAI;AAAA,EAAC,cAAcE,IAAEF,IAAEC,IAAE;AAAC,QAAG,CAACC,GAAE,MAAM,QAAM;AAAG,QAAG,CAAC,MAAM,QAAQA,GAAE,KAAK,KAAG,MAAIA,GAAE,MAAM,OAAO,QAAM;AAAG,QAAG,CAACA,GAAE,OAAO,QAAM;AAAG,QAAG,CAAC,MAAM,QAAQA,GAAE,MAAM,KAAG,MAAIA,GAAE,OAAO,OAAO,QAAM;AAAG,aAAQI,KAAE,GAAEA,KAAE,GAAE,EAAEA,IAAE;AAAC,YAAMC,KAAEL,GAAE,MAAMI,EAAC;AAAE,UAAGC,KAAE,KAAGA,MAAGN,GAAED,GAAEM,EAAC,CAAC,EAAE,KAAK,QAAM;AAAA,IAAE;AAAC,UAAMC,KAAEI,GAAET,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC;AAAE,MAAEK,IAAEA,EAAC;AAAE,UAAMI,KAAE;AAAK,WAAM,EAAE,KAAK,IAAIJ,GAAE,CAAC,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,CAAC,IAAEI,QAAKT,GAAE,OAAO,CAAC,IAAEK,GAAE,CAAC,GAAEL,GAAE,OAAO,CAAC,IAAEK,GAAE,CAAC,GAAEL,GAAE,OAAO,CAAC,IAAEK,GAAE,CAAC,GAAE;AAAA,EAAG;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,OAAO,EAAC,CAAC,CAAC,GAAEX,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAEG,EAAC,CAAC,GAAEH,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAEQ,EAAC,CAAC,GAAER,GAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOS,EAAC,GAAE,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,4BAA2B,OAAM,KAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAET,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,2CAA0C,OAAM,EAAC,SAAQ,MAAG,YAAW,MAAG,cAAa,KAAE,EAAC,GAAE,SAAQ,EAAC,MAAK,0BAAyB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOO,EAAC,GAAE,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,sCAAqC,OAAM,KAAE,GAAE,SAAQ,EAAC,MAAK,qBAAoB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,UAAS,UAAU,GAAE,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,oCAAmC,OAAM,KAAE,GAAE,SAAQ,EAAC,MAAK,mBAAkB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOQ,EAAC,GAAE,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,wCAAuC,OAAM,KAAE,GAAE,SAAQ,EAAC,MAAK,uBAAsB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,sCAAqC,OAAM,KAAE,GAAE,SAAQ,EAAC,MAAK,qBAAoB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,wCAAuC,OAAM,KAAE,GAAE,SAAQ,EAAC,MAAK,uBAAsB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,+CAA8C,OAAM,KAAE,GAAE,SAAQ,EAAC,MAAK,8BAA6B,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,2CAA0C,OAAM,KAAE,GAAE,SAAQ,EAAC,MAAK,0BAAyB,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKE,IAAE,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,iCAAgC,OAAM,KAAE,GAAE,SAAQ,EAAC,MAAK,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,aAAY,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,aAAY,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,eAAc,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOE,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOa,EAAC,EAAC,CAAC,CAAC,GAAEb,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKE,IAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,4BAA2B,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,SAAQ,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,4BAA2B,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,SAAQ,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,SAAS,CAAC,GAAEA,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKY,GAAC,CAAC,CAAC,GAAEZ,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,cAAa,CAAC,YAAY,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,UAAS,OAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAEE,EAAC,CAAC,GAAEF,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,IAAI,GAAEA,KAAE,EAAE,CAACiB,GAAE,CAAC,CAAC,GAAEjB,EAAC;AAAE,IAAMkB,KAAElB;", "names": ["h", "i", "e", "p", "j", "r", "v", "a", "n", "o", "E", "t", "y", "t", "o", "v", "s", "a", "c", "e", "r", "v", "s", "p", "c", "a", "u", "t", "a", "p", "s", "a", "p", "i", "p", "a", "l", "a", "c", "l", "a", "p", "a", "i", "p", "a", "u", "o", "y", "l", "i", "n", "t", "r", "e", "s", "a", "p", "c", "h", "f", "g", "l", "a", "p", "c", "e", "a", "g", "s", "f", "t", "a", "p", "t", "a", "p", "p", "a", "d", "y", "f", "e", "i", "s", "r", "t", "n", "o", "w", "u", "a", "S", "t", "s", "e", "a", "p", "m", "t", "o", "v", "s", "a", "c", "p", "m", "e", "c", "n", "a", "d", "M", "z", "E", "p", "c", "t", "i", "e", "d", "f", "u", "s", "o", "w", "S", "y", "r", "n", "a", "_"]}