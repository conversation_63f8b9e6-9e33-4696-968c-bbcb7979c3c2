import {
  i
} from "./chunk-DHWMTT76.js";
import {
  o as o2
} from "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T
} from "./chunk-HP475EI3.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/support/CodedValue.js
var t;
var p2 = t = class extends l {
  constructor(r) {
    super(r), this.name = null, this.code = null;
  }
  clone() {
    return new t({ name: this.name, code: this.code });
  }
};
e([y({ type: String, json: { write: true } })], p2.prototype, "name", void 0), e([y({ type: [String, Number], json: { write: true } })], p2.prototype, "code", void 0), p2 = t = e([a("esri.layers.support.CodedValue")], p2);

// node_modules/@arcgis/core/layers/support/Domain.js
var c = new s({ inherited: "inherited", codedValue: "coded-value", range: "range" });
var a2 = class extends l {
  constructor(r) {
    super(r), this.name = null, this.type = null;
  }
};
e([y({ type: String, json: { write: true } })], a2.prototype, "name", void 0), e([o2(c)], a2.prototype, "type", void 0), a2 = e([a("esri.layers.support.Domain")], a2);
var i2 = a2;

// node_modules/@arcgis/core/layers/support/CodedValueDomain.js
var d;
var p3 = d = class extends i2 {
  constructor(e2) {
    super(e2), this.codedValues = null, this.type = "coded-value";
  }
  getName(e2) {
    let o3 = null;
    if (this.codedValues) {
      const r = String(e2);
      this.codedValues.some((e3) => (String(e3.code) === r && (o3 = e3.name), !!o3));
    }
    return o3;
  }
  clone() {
    return new d({ codedValues: p(this.codedValues), name: this.name });
  }
};
e([y({ type: [p2], json: { write: true } })], p3.prototype, "codedValues", void 0), e([o2({ codedValue: "coded-value" })], p3.prototype, "type", void 0), p3 = d = e([a("esri.layers.support.CodedValueDomain")], p3);
var u = p3;

// node_modules/@arcgis/core/layers/support/InheritedDomain.js
var t2;
var i3 = t2 = class extends i2 {
  constructor(r) {
    super(r), this.type = "inherited";
  }
  clone() {
    return new t2();
  }
};
e([o2({ inherited: "inherited" })], i3.prototype, "type", void 0), i3 = t2 = e([a("esri.layers.support.InheritedDomain")], i3);
var p4 = i3;

// node_modules/@arcgis/core/layers/support/RangeDomain.js
var s2;
var n = s2 = class extends i2 {
  constructor(e2) {
    super(e2), this.maxValue = null, this.minValue = null, this.type = "range";
  }
  clone() {
    return new s2({ maxValue: this.maxValue, minValue: this.minValue, name: this.name });
  }
};
e([y({ type: Number, json: { type: [Number], read: { source: "range", reader: (e2, r) => r.range && r.range[1] }, write: { enabled: false, overridePolicy() {
  return { enabled: null != this.maxValue && null == this.minValue };
}, target: "range", writer(e2, r, a3) {
  r[a3] = [this.minValue || 0, e2];
} } } })], n.prototype, "maxValue", void 0), e([y({ type: Number, json: { type: [Number], read: { source: "range", reader: (e2, r) => r.range && r.range[0] }, write: { target: "range", writer(e2, r, a3) {
  r[a3] = [e2, this.maxValue || 0];
} } } })], n.prototype, "minValue", void 0), e([o2({ range: "range" })], n.prototype, "type", void 0), n = s2 = e([a("esri.layers.support.RangeDomain")], n);
var i4 = n;

// node_modules/@arcgis/core/layers/support/domains.js
var n2 = { key: "type", base: i2, typeMap: { range: i4, "coded-value": u, inherited: p4 } };
function i5(r) {
  if (!r || !r.type) return null;
  switch (r.type) {
    case "range":
      return i4.fromJSON(r);
    case "codedValue":
      return u.fromJSON(r);
    case "inherited":
      return p4.fromJSON(r);
  }
  return null;
}

// node_modules/@arcgis/core/layers/support/Field.js
var u2;
var c2 = new s({ binary: "binary", coordinate: "coordinate", countOrAmount: "count-or-amount", dateAndTime: "date-and-time", description: "description", locationOrPlaceName: "location-or-place-name", measurement: "measurement", nameOrTitle: "name-or-title", none: "none", orderedOrRanked: "ordered-or-ranked", percentageOrRatio: "percentage-or-ratio", typeOrCategory: "type-or-category", uniqueIdentifier: "unique-identifier" });
var m = u2 = class extends l {
  constructor(e2) {
    super(e2), this.alias = null, this.defaultValue = void 0, this.description = null, this.domain = null, this.editable = true, this.length = -1, this.name = null, this.nullable = true, this.type = null, this.valueType = null, this.visible = true;
  }
  readDescription(e2, { description: t3 }) {
    let o3 = null;
    try {
      o3 = t3 ? JSON.parse(t3) : null;
    } catch (r) {
    }
    return (o3 == null ? void 0 : o3.value) ?? null;
  }
  readValueType(e2, { description: t3 }) {
    let o3 = null;
    try {
      o3 = t3 ? JSON.parse(t3) : null;
    } catch (r) {
    }
    return o3 ? c2.fromJSON(o3.fieldValueType) : null;
  }
  clone() {
    return new u2({ alias: this.alias, defaultValue: this.defaultValue, description: this.description, domain: this.domain && this.domain.clone() || null, editable: this.editable, length: this.length, name: this.name, nullable: this.nullable, type: this.type, valueType: this.valueType, visible: this.visible });
  }
};
e([y({ type: String, json: { write: true } })], m.prototype, "alias", void 0), e([y({ type: [String, Number], json: { write: { allowNull: true } } })], m.prototype, "defaultValue", void 0), e([y()], m.prototype, "description", void 0), e([o("description")], m.prototype, "readDescription", null), e([y({ types: n2, json: { read: { reader: i5 }, write: true } })], m.prototype, "domain", void 0), e([y({ type: Boolean, json: { write: true } })], m.prototype, "editable", void 0), e([y({ type: T, json: { write: true } })], m.prototype, "length", void 0), e([y({ type: String, json: { write: true } })], m.prototype, "name", void 0), e([y({ type: Boolean, json: { write: true } })], m.prototype, "nullable", void 0), e([o2(i)], m.prototype, "type", void 0), e([y()], m.prototype, "valueType", void 0), e([o("valueType", ["description"])], m.prototype, "readValueType", null), e([y({ type: Boolean, json: { read: false } })], m.prototype, "visible", void 0), m = u2 = e([a("esri.layers.support.Field")], m);
var y2 = m;

export {
  n2 as n,
  i5 as i,
  y2 as y
};
//# sourceMappingURL=chunk-NZB6EMKN.js.map
