import {
  e as e2,
  l,
  s,
  u as u2
} from "./chunk-ATXSW42W.js";
import {
  a as a2,
  c,
  r
} from "./chunk-CF4Y76HG.js";
import {
  B,
  J,
  Ne,
  Se,
  Te,
  U,
  V,
  Z,
  be,
  ce,
  fe,
  le,
  re
} from "./chunk-REVHHZEO.js";
import {
  e,
  t
} from "./chunk-YFVPK4WM.js";
import {
  a
} from "./chunk-WXFAAYJL.js";
import {
  v as v2
} from "./chunk-ZACBBT3Y.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  u
} from "./chunk-3WCHZJQK.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  p,
  w
} from "./chunk-63M4K32A.js";
import {
  $
} from "./chunk-JXLVNWKF.js";

// node_modules/@arcgis/core/arcade/functions/geomsync.js
var A = null;
function q(e3) {
  return 0 === a.indexOf("4.") ? v.fromExtent(e3) : new v({ spatialReference: e3.spatialReference, rings: [[[e3.xmin, e3.ymin], [e3.xmin, e3.ymax], [e3.xmax, e3.ymax], [e3.xmax, e3.ymin], [e3.xmin, e3.ymin]]] });
}
function z(n) {
  A = n;
}
function E(n, e3) {
  if ("polygon" !== n.type && "polyline" !== n.type && "extent" !== n.type) return 0;
  let t2 = 1;
  if (n.spatialReference.vcsWkid || n.spatialReference.latestVcsWkid) {
    t2 = s(n.spatialReference) / $(n.spatialReference);
  }
  let r2 = 0;
  if ("polyline" === n.type) for (const a3 of n.paths) for (let n2 = 1; n2 < a3.length; n2++) r2 += e2(a3[n2], a3[n2 - 1], t2);
  else if ("polygon" === n.type) for (const a3 of n.rings) {
    for (let n2 = 1; n2 < a3.length; n2++) r2 += e2(a3[n2], a3[n2 - 1], t2);
    (a3[0][0] !== a3[a3.length - 1][0] || a3[0][1] !== a3[a3.length - 1][1] || void 0 !== a3[0][2] && a3[0][2] !== a3[a3.length - 1][2]) && (r2 += e2(a3[0], a3[a3.length - 1], t2));
  }
  else "extent" === n.type && (r2 += 2 * e2([n.xmin, n.ymin, 0], [n.xmax, n.ymin, 0], t2), r2 += 2 * e2([n.xmin, n.ymin, 0], [n.xmin, n.ymax, 0], t2), r2 *= 2, r2 += 4 * Math.abs(Z(n.zmax, 0) * t2 - Z(n.zmin, 0) * t2));
  const i = new m({ hasZ: false, hasM: false, spatialReference: n.spatialReference, paths: [[0, 0], [0, r2]] });
  return A.planarLength(i, e3);
}
function O(n, v3) {
  function I(n2, e3, t2) {
    if (B(t2, 2, 2, n2, e3), t2[0] instanceof p && t2[1] instanceof p) ;
    else if (t2[0] instanceof p && null === t2[1]) ;
    else if (t2[1] instanceof p && null === t2[0]) ;
    else if (null !== t2[0] || null !== t2[1]) throw new t(n2, e.InvalidParameter, e3);
  }
  n.disjoint = function(n2, e3) {
    return v3(n2, e3, (t2, r2, a3) => (a3 = be(a3), I(n2, e3, a3), null === a3[0] || null === a3[1] || A.disjoint(a3[0], a3[1])));
  }, n.intersects = function(n2, e3) {
    return v3(n2, e3, (t2, r2, a3) => (a3 = be(a3), I(n2, e3, a3), null !== a3[0] && null !== a3[1] && A.intersects(a3[0], a3[1])));
  }, n.touches = function(n2, e3) {
    return v3(n2, e3, (t2, r2, a3) => (a3 = be(a3), I(n2, e3, a3), null !== a3[0] && null !== a3[1] && A.touches(a3[0], a3[1])));
  }, n.crosses = function(n2, e3) {
    return v3(n2, e3, (t2, r2, a3) => (a3 = be(a3), I(n2, e3, a3), null !== a3[0] && null !== a3[1] && A.crosses(a3[0], a3[1])));
  }, n.within = function(n2, e3) {
    return v3(n2, e3, (t2, r2, a3) => (a3 = be(a3), I(n2, e3, a3), null !== a3[0] && null !== a3[1] && A.within(a3[0], a3[1])));
  }, n.contains = function(n2, e3) {
    return v3(n2, e3, (t2, r2, a3) => (a3 = be(a3), I(n2, e3, a3), null !== a3[0] && null !== a3[1] && A.contains(a3[0], a3[1])));
  }, n.overlaps = function(n2, e3) {
    return v3(n2, e3, (t2, r2, a3) => (a3 = be(a3), I(n2, e3, a3), null !== a3[0] && null !== a3[1] && A.overlaps(a3[0], a3[1])));
  }, n.equals = function(n2, e3) {
    return v3(n2, e3, (t2, r2, i) => (B(i, 2, 2, n2, e3), i[0] === i[1] || (i[0] instanceof p && i[1] instanceof p ? A.equals(i[0], i[1]) : !(!U(i[0]) || !U(i[1])) && i[0].equals(i[1]))));
  }, n.relate = function(n2, e3) {
    return v3(n2, e3, (t2, r2, l2) => {
      if (l2 = be(l2), B(l2, 3, 3, n2, e3), l2[0] instanceof p && l2[1] instanceof p) return A.relate(l2[0], l2[1], re(l2[2]));
      if (l2[0] instanceof p && null === l2[1]) return false;
      if (l2[1] instanceof p && null === l2[0]) return false;
      if (null === l2[0] && null === l2[1]) return false;
      throw new t(n2, e.InvalidParameter, e3);
    });
  }, n.intersection = function(n2, e3) {
    return v3(n2, e3, (t2, r2, a3) => (a3 = be(a3), I(n2, e3, a3), null === a3[0] || null === a3[1] ? null : A.intersect(a3[0], a3[1])));
  }, n.union = function(n2, t2) {
    return v3(n2, t2, (r2, a3, l2) => {
      const o = [];
      if (0 === (l2 = be(l2)).length) throw new t(n2, e.WrongNumberOfParameters, t2);
      if (1 === l2.length) if (J(l2[0])) {
        const e3 = be(l2[0]);
        for (let r3 = 0; r3 < e3.length; r3++) if (null !== e3[r3]) {
          if (!(e3[r3] instanceof p)) throw new t(n2, e.InvalidParameter, t2);
          o.push(e3[r3]);
        }
      } else {
        if (!V(l2[0])) {
          if (l2[0] instanceof p) return ce(c(l2[0]), n2.spatialReference);
          if (null === l2[0]) return null;
          throw new t(n2, e.InvalidParameter, t2);
        }
        {
          const e3 = be(l2[0].toArray());
          for (let r3 = 0; r3 < e3.length; r3++) if (null !== e3[r3]) {
            if (!(e3[r3] instanceof p)) throw new t(n2, e.InvalidParameter, t2);
            o.push(e3[r3]);
          }
        }
      }
      else for (let e3 = 0; e3 < l2.length; e3++) if (null !== l2[e3]) {
        if (!(l2[e3] instanceof p)) throw new t(n2, e.InvalidParameter, t2);
        o.push(l2[e3]);
      }
      return 0 === o.length ? null : A.union(o);
    });
  }, n.difference = function(n2, t2) {
    return v3(n2, t2, (r2, a3, l2) => (l2 = be(l2), I(n2, t2, l2), null !== l2[0] && null === l2[1] ? c(l2[0]) : null === l2[0] ? null : A.difference(l2[0], l2[1])));
  }, n.symmetricdifference = function(n2, t2) {
    return v3(n2, t2, (r2, a3, l2) => (l2 = be(l2), I(n2, t2, l2), null === l2[0] && null === l2[1] ? null : null === l2[0] ? c(l2[1]) : null === l2[1] ? c(l2[0]) : A.symmetricDifference(l2[0], l2[1])));
  }, n.clip = function(n2, e3) {
    return v3(n2, e3, (t2, r2, l2) => {
      if (l2 = be(l2), B(l2, 2, 2, n2, e3), !(l2[1] instanceof w2) && null !== l2[1]) throw new t(n2, e.InvalidParameter, e3);
      if (null === l2[0]) return null;
      if (!(l2[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return null === l2[1] ? null : A.clip(l2[0], l2[1]);
    });
  }, n.cut = function(n2, t2) {
    return v3(n2, t2, (r2, l2, o) => {
      if (o = be(o), B(o, 2, 2, n2, t2), !(o[1] instanceof m) && null !== o[1]) throw new t(n2, e.InvalidParameter, t2);
      if (null === o[0]) return [];
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, t2);
      return null === o[1] ? [c(o[0])] : A.cut(o[0], o[1]);
    });
  }, n.area = function(n2, e3) {
    return v3(n2, e3, (r2, l2, o) => {
      if (B(o, 1, 2, n2, e3), null === (o = be(o))[0]) return 0;
      if (J(o[0]) || V(o[0])) {
        const e4 = Se(o[0], n2.spatialReference);
        return null === e4 ? 0 : A.planarArea(e4, r(Z(o[1], -1)));
      }
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return A.planarArea(o[0], r(Z(o[1], -1)));
    });
  }, n.areageodetic = function(n2, e3) {
    return v3(n2, e3, (r2, l2, o) => {
      if (B(o, 1, 2, n2, e3), null === (o = be(o))[0]) return 0;
      if (J(o[0]) || V(o[0])) {
        const e4 = Se(o[0], n2.spatialReference);
        return null === e4 ? 0 : A.geodesicArea(e4, r(Z(o[1], -1)));
      }
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return A.geodesicArea(o[0], r(Z(o[1], -1)));
    });
  }, n.length = function(n2, e3) {
    return v3(n2, e3, (t2, l2, o) => {
      if (B(o, 1, 2, n2, e3), null === (o = be(o))[0]) return 0;
      if (J(o[0]) || V(o[0])) {
        const e4 = Ne(o[0], n2.spatialReference);
        return null === e4 ? 0 : A.planarLength(e4, a2(Z(o[1], -1)));
      }
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return A.planarLength(o[0], a2(Z(o[1], -1)));
    });
  }, n.length3d = function(n2, e3) {
    return v3(n2, e3, (t2, l2, o) => {
      if (B(o, 1, 2, n2, e3), null === (o = be(o))[0]) return 0;
      if (J(o[0]) || V(o[0])) {
        const e4 = Ne(o[0], n2.spatialReference);
        return null === e4 ? 0 : true === e4.hasZ ? E(e4, a2(Z(o[1], -1))) : A.planarLength(e4, a2(Z(o[1], -1)));
      }
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return true === o[0].hasZ ? E(o[0], a2(Z(o[1], -1))) : A.planarLength(o[0], a2(Z(o[1], -1)));
    });
  }, n.lengthgeodetic = function(n2, e3) {
    return v3(n2, e3, (t2, l2, o) => {
      if (B(o, 1, 2, n2, e3), null === (o = be(o))[0]) return 0;
      if (J(o[0]) || V(o[0])) {
        const e4 = Ne(o[0], n2.spatialReference);
        return null === e4 ? 0 : A.geodesicLength(e4, a2(Z(o[1], -1)));
      }
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return A.geodesicLength(o[0], a2(Z(o[1], -1)));
    });
  }, n.distance = function(n2, e3) {
    return v3(n2, e3, (t2, l2, o) => {
      o = be(o), B(o, 2, 3, n2, e3);
      let u3 = o[0];
      (J(o[0]) || V(o[0])) && (u3 = Te(o[0], n2.spatialReference));
      let c2 = o[1];
      if ((J(o[1]) || V(o[1])) && (c2 = Te(o[1], n2.spatialReference)), !(u3 instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      if (!(c2 instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return A.distance(u3, c2, a2(Z(o[2], -1)));
    });
  }, n.distancegeodetic = function(n2, e3) {
    return v3(n2, e3, (t2, l2, o) => {
      o = be(o), B(o, 2, 3, n2, e3);
      const f = o[0], s2 = o[1];
      if (!(f instanceof w)) throw new t(n2, e.InvalidParameter, e3);
      if (!(s2 instanceof w)) throw new t(n2, e.InvalidParameter, e3);
      const u3 = new m({ paths: [], spatialReference: f.spatialReference });
      return u3.addPath([f, s2]), A.geodesicLength(u3, a2(Z(o[2], -1)));
    });
  }, n.densify = function(n2, e3) {
    return v3(n2, e3, (t2, l2, o) => {
      if (o = be(o), B(o, 2, 3, n2, e3), null === o[0]) return null;
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      const f = le(o[1]);
      if (isNaN(f)) throw new t(n2, e.InvalidParameter, e3);
      if (f <= 0) throw new t(n2, e.InvalidParameter, e3);
      return o[0] instanceof v || o[0] instanceof m ? A.densify(o[0], f, a2(Z(o[2], -1))) : o[0] instanceof w2 ? A.densify(q(o[0]), f, a2(Z(o[2], -1))) : o[0];
    });
  }, n.densifygeodetic = function(n2, e3) {
    return v3(n2, e3, (t2, l2, o) => {
      if (o = be(o), B(o, 2, 3, n2, e3), null === o[0]) return null;
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      const f = le(o[1]);
      if (isNaN(f)) throw new t(n2, e.InvalidParameter, e3);
      if (f <= 0) throw new t(n2, e.InvalidParameter, e3);
      return o[0] instanceof v || o[0] instanceof m ? A.geodesicDensify(o[0], f, a2(Z(o[2], -1))) : o[0] instanceof w2 ? A.geodesicDensify(q(o[0]), f, a2(Z(o[2], -1))) : o[0];
    });
  }, n.generalize = function(n2, e3) {
    return v3(n2, e3, (t2, l2, o) => {
      if (o = be(o), B(o, 2, 4, n2, e3), null === o[0]) return null;
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      const f = le(o[1]);
      if (isNaN(f)) throw new t(n2, e.InvalidParameter, e3);
      return A.generalize(o[0], f, fe(Z(o[2], true)), a2(Z(o[3], -1)));
    });
  }, n.buffer = function(n2, t2) {
    return v3(n2, t2, (l2, o, f) => {
      if (f = be(f), B(f, 2, 3, n2, t2), null === f[0]) return null;
      if (!(f[0] instanceof p)) throw new t(n2, e.InvalidParameter, t2);
      const s2 = le(f[1]);
      if (isNaN(s2)) throw new t(n2, e.InvalidParameter, t2);
      return 0 === s2 ? c(f[0]) : A.buffer(f[0], s2, a2(Z(f[2], -1)));
    });
  }, n.buffergeodetic = function(n2, t2) {
    return v3(n2, t2, (l2, o, f) => {
      if (f = be(f), B(f, 2, 3, n2, t2), null === f[0]) return null;
      if (!(f[0] instanceof p)) throw new t(n2, e.InvalidParameter, t2);
      const s2 = le(f[1]);
      if (isNaN(s2)) throw new t(n2, e.InvalidParameter, t2);
      return 0 === s2 ? c(f[0]) : A.geodesicBuffer(f[0], s2, a2(Z(f[2], -1)));
    });
  }, n.offset = function(n2, e3) {
    return v3(n2, e3, (t2, l2, f) => {
      if (f = be(f), B(f, 2, 6, n2, e3), null === f[0]) return null;
      if (!(f[0] instanceof v || f[0] instanceof m)) throw new t(n2, e.InvalidParameter, e3);
      const s2 = le(f[1]);
      if (isNaN(s2)) throw new t(n2, e.InvalidParameter, e3);
      const u3 = le(Z(f[4], 10));
      if (isNaN(u3)) throw new t(n2, e.InvalidParameter, e3);
      const c2 = le(Z(f[5], 0));
      if (isNaN(c2)) throw new t(n2, e.InvalidParameter, e3);
      return A.offset(f[0], s2, a2(Z(f[2], -1)), re(Z(f[3], "round")).toLowerCase(), u3, c2);
    });
  }, n.rotate = function(n2, e3) {
    return v3(n2, e3, (t2, r2, l2) => {
      l2 = be(l2), B(l2, 2, 3, n2, e3);
      let o = l2[0];
      if (null === o) return null;
      if (!(o instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      o instanceof w2 && (o = v.fromExtent(o));
      const f = le(l2[1]);
      if (isNaN(f)) throw new t(n2, e.InvalidParameter, e3);
      const s2 = Z(l2[2], null);
      if (null === s2) return A.rotate(o, f);
      if (s2 instanceof w) return A.rotate(o, f, s2);
      throw new t(n2, e.InvalidParameter, e3);
    });
  }, n.centroid = function(n2, t2) {
    return v3(n2, t2, (r2, l2, o) => {
      if (o = be(o), B(o, 1, 1, n2, t2), null === o[0]) return null;
      let c2 = o[0];
      if ((J(o[0]) || V(o[0])) && (c2 = Te(o[0], n2.spatialReference)), null === c2) return null;
      if (!(c2 instanceof p)) throw new t(n2, e.InvalidParameter, t2);
      return c2 instanceof w ? ce(c(o[0]), n2.spatialReference) : c2 instanceof v ? c2.centroid : c2 instanceof m ? l(c2) : c2 instanceof u ? u2(c2) : c2 instanceof w2 ? c2.center : null;
    });
  }, n.multiparttosinglepart = function(n2, t2) {
    return v3(n2, t2, (r2, l2, o) => {
      o = be(o), B(o, 1, 1, n2, t2);
      const f = [];
      if (null === o[0]) return null;
      if (!(o[0] instanceof p)) throw new t(n2, e.InvalidParameter, t2);
      if (o[0] instanceof w) return [ce(c(o[0]), n2.spatialReference)];
      if (o[0] instanceof w2) return [ce(c(o[0]), n2.spatialReference)];
      const s2 = A.simplify(o[0]);
      if (s2 instanceof v) {
        const n3 = [], e3 = [];
        for (let t3 = 0; t3 < s2.rings.length; t3++) if (s2.isClockwise(s2.rings[t3])) {
          const e4 = v2({ rings: [s2.rings[t3]], hasZ: true === s2.hasZ, hasM: true === s2.hasM, spatialReference: s2.spatialReference.toJSON() });
          n3.push(e4);
        } else e3.push({ ring: s2.rings[t3], pt: s2.getPoint(t3, 0) });
        for (let t3 = 0; t3 < e3.length; t3++) for (let r3 = 0; r3 < n3.length; r3++) if (n3[r3].contains(e3[t3].pt)) {
          n3[r3].addRing(e3[t3].ring);
          break;
        }
        return n3;
      }
      if (s2 instanceof m) {
        const n3 = [];
        for (let e3 = 0; e3 < s2.paths.length; e3++) {
          const t3 = v2({ paths: [s2.paths[e3]], hasZ: true === s2.hasZ, hasM: true === s2.hasM, spatialReference: s2.spatialReference.toJSON() });
          n3.push(t3);
        }
        return n3;
      }
      if (o[0] instanceof u) {
        const t3 = ce(c(o[0]), n2.spatialReference);
        for (let n3 = 0; n3 < t3.points.length; n3++) f.push(t3.getPoint(n3));
        return f;
      }
      return null;
    });
  }, n.issimple = function(n2, e3) {
    return v3(n2, e3, (t2, r2, l2) => {
      if (l2 = be(l2), B(l2, 1, 1, n2, e3), null === l2[0]) return true;
      if (!(l2[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return A.isSimple(l2[0]);
    });
  }, n.simplify = function(n2, e3) {
    return v3(n2, e3, (t2, r2, l2) => {
      if (l2 = be(l2), B(l2, 1, 1, n2, e3), null === l2[0]) return null;
      if (!(l2[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return A.simplify(l2[0]);
    });
  }, n.convexhull = function(n2, e3) {
    return v3(n2, e3, (t2, r2, l2) => {
      if (l2 = be(l2), B(l2, 1, 1, n2, e3), null === l2[0]) return null;
      if (!(l2[0] instanceof p)) throw new t(n2, e.InvalidParameter, e3);
      return A.convexHull(l2[0]);
    });
  };
}

export {
  z,
  O
};
//# sourceMappingURL=chunk-ZB2RNRLO.js.map
