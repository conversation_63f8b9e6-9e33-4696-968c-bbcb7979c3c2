{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/graphics/HighlightGraphicContainer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import\"../../../../core/Logger.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import\"../../../../core/Error.js\";import\"../../../../core/has.js\";import{subclass as r}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{WGLDrawPhase as s}from\"../../engine/webgl/enums.js\";import t from\"./BaseGraphicContainer.js\";import{ClearBufferBit as o}from\"../../../webgl/enums.js\";let i=class extends t{doRender(e){e.drawPhase===s.HIGHLIGHT&&super.doRender(e)}renderChildren(e){if(this.attributeView.update(),!this.children.some((e=>e.hasData)))return;this.attributeView.bindTextures(e.context),super.renderChildren(e);const{painter:r}=e,s=r.effects.highlight;s.bind(e),e.context.setColorMask(!0,!0,!0,!0),e.context.clear(o.COLOR_BUFFER_BIT),this._renderChildren(e,s.defines.concat([\"highlightAll\"])),s.draw(e),s.unbind()}};i=e([r(\"esri.views.2d.layers.support.HighlightGraphicContainer\")],i);const n=i;export{n as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAIue,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,IAAAA,GAAE,cAAY,EAAE,aAAW,MAAM,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,QAAG,KAAK,cAAc,OAAO,GAAE,CAAC,KAAK,SAAS,KAAM,CAAAA,OAAGA,GAAE,OAAQ,EAAE;AAAO,SAAK,cAAc,aAAaA,GAAE,OAAO,GAAE,MAAM,eAAeA,EAAC;AAAE,UAAK,EAAC,SAAQ,EAAC,IAAEA,IAAE,IAAE,EAAE,QAAQ;AAAU,MAAE,KAAKA,EAAC,GAAEA,GAAE,QAAQ,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,GAAE,QAAQ,MAAM,EAAE,gBAAgB,GAAE,KAAK,gBAAgBA,IAAE,EAAE,QAAQ,OAAO,CAAC,cAAc,CAAC,CAAC,GAAE,EAAE,KAAKA,EAAC,GAAE,EAAE,OAAO;AAAA,EAAC;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,wDAAwD,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;", "names": ["e", "n"]}