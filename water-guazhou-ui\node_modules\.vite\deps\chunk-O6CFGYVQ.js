import {
  t
} from "./chunk-MI2Z635K.js";
import {
  W,
  e2
} from "./chunk-NQQSL2QK.js";
import {
  n2 as n
} from "./chunk-IZLLLMFE.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  g
} from "./chunk-EKX3LLYN.js";
import {
  a,
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/widgets/Zoom/IconButton.js
var c = { button: "esri-widget--button esri-widget", disabled: "esri-disabled", interactive: "esri-interactive", iconText: "esri-icon-font-fallback-text", icon: "esri-icon" };
var n2 = class extends W {
  constructor() {
    super(...arguments), this.enabled = true, this.iconClass = "", this.title = "";
  }
  render() {
    const t3 = this.enabled ? 0 : -1, s2 = { [c.disabled]: !this.enabled, [c.interactive]: this.enabled }, i2 = { [this.iconClass]: !!this.iconClass };
    return n("div", { bind: this, class: this.classes(c.button, s2), onclick: this._triggerAction, onkeydown: this._triggerAction, role: "button", tabIndex: t3, title: this.title }, n("span", { "aria-hidden": "true", role: "presentation", class: this.classes(c.icon, i2) }), n("span", { class: c.iconText }, this.title));
  }
  _triggerAction() {
    this.action.call(this);
  }
};
e([y()], n2.prototype, "action", void 0), e([y()], n2.prototype, "enabled", void 0), e([y()], n2.prototype, "iconClass", void 0), e([y()], n2.prototype, "title", void 0), e([t()], n2.prototype, "_triggerAction", null), n2 = e([a2("esri.widgets.IconButton")], n2);
var a3 = n2;

// node_modules/@arcgis/core/widgets/Zoom/ZoomConditions2D.js
var s = class extends v {
  get canZoomIn() {
    if (!this.get("view.ready")) return false;
    const e3 = this.get("view.animation.target.scale") || this.get("view.scale"), t3 = this.get("view.constraints.effectiveMaxScale");
    return 0 === t3 || e3 > t3;
  }
  get canZoomOut() {
    if (!this.get("view.ready")) return false;
    const e3 = this.get("view.animation.target.scale") || this.get("view.scale"), t3 = this.get("view.constraints.effectiveMinScale");
    return 0 === t3 || e3 < t3;
  }
};
e([y({ readOnly: true })], s.prototype, "canZoomIn", null), e([y({ readOnly: true })], s.prototype, "canZoomOut", null), e([y()], s.prototype, "view", void 0), s = e([a2("esri.widgets.Zoom.ZoomConditions2D")], s);
var i = s;

// node_modules/@arcgis/core/widgets/Zoom/ZoomConditions3D.js
var t2 = class extends v {
  get canZoomIn() {
    return !!this.view.ready;
  }
  get canZoomOut() {
    return !!this.view.ready;
  }
};
e([y({ readOnly: true })], t2.prototype, "canZoomIn", null), e([y({ readOnly: true })], t2.prototype, "canZoomOut", null), e([y()], t2.prototype, "view", void 0), t2 = e([a2("esri.widgets.Zoom.ZoomConditions3D")], t2);
var c2 = t2;

// node_modules/@arcgis/core/widgets/Zoom/ZoomViewModel.js
var p = class extends v {
  constructor(o) {
    super(o);
  }
  destroy() {
    this.view = null;
  }
  get canZoomIn() {
    return r(this._zoomConditions) && this._zoomConditions.canZoomIn;
  }
  get canZoomOut() {
    var _a;
    return r(this._zoomConditions) && ((_a = this._zoomConditions) == null ? void 0 : _a.canZoomOut);
  }
  get state() {
    var _a;
    return ((_a = this.view) == null ? void 0 : _a.ready) ? "ready" : "disabled";
  }
  set view(o) {
    o ? "2d" === o.type ? this._zoomConditions = new i({ view: o }) : "3d" === o.type && (this._zoomConditions = new c2({ view: o })) : this._zoomConditions = null, this._set("view", o);
  }
  zoomIn() {
    if (!this.canZoomIn) return;
    const o = this.view;
    "2d" === o.type ? o.mapViewNavigation.zoomIn() : g(o.goTo({ zoomFactor: 2 }));
  }
  zoomOut() {
    if (!this.canZoomOut) return;
    const o = this.view;
    "2d" === o.type ? o.mapViewNavigation.zoomOut() : g(o.goTo({ zoomFactor: 0.5 }));
  }
};
e([y()], p.prototype, "_zoomConditions", void 0), e([y()], p.prototype, "canZoomIn", null), e([y()], p.prototype, "canZoomOut", null), e([y({ readOnly: true })], p.prototype, "state", null), e([y()], p.prototype, "view", null), p = e([a2("esri.widgets.Zoom.ZoomViewModel")], p);
var a4 = p;

// node_modules/@arcgis/core/widgets/Zoom.js
var u = { base: "esri-zoom esri-widget", horizontalLayout: "esri-zoom--horizontal", zoomInIcon: "esri-icon-plus", zoomOutIcon: "esri-icon-minus", widgetIcon: "esri-icon-zoom-in-magnifying-glass" };
var a5 = class extends W {
  constructor(o, t3) {
    super(o, t3), this.iconClass = u.widgetIcon, this.messages = null, this.viewModel = new a4();
  }
  initialize() {
    this._zoomInButton = new a3({ action: this.zoomIn.bind(this), iconClass: u.zoomInIcon }), this._zoomOutButton = new a3({ action: this.zoomOut.bind(this), iconClass: u.zoomOutIcon });
  }
  destroy() {
    this._zoomInButton = a(this._zoomInButton), this._zoomOutButton = a(this._zoomOutButton);
  }
  get label() {
    var _a;
    return ((_a = this.messages) == null ? void 0 : _a.widgetLabel) ?? "";
  }
  set label(o) {
    this._overrideIfSome("label", o);
  }
  set layout(o) {
    "horizontal" !== o && (o = "vertical"), this._set("layout", o);
  }
  set view(o) {
    this.viewModel.view = o;
  }
  get view() {
    return this.viewModel.view;
  }
  render() {
    const o = this.viewModel, t3 = { [u.horizontalLayout]: "horizontal" === this.layout }, { canZoomIn: s2, canZoomOut: e3 } = o;
    this._zoomInButton.enabled = s2, this._zoomOutButton.enabled = e3;
    const { zoomIn: i2, zoomOut: r2 } = this.messages;
    return this._zoomInButton.title = i2, this._zoomOutButton.title = r2, n("div", { class: this.classes(u.base, t3) }, this._zoomInButton.render(), this._zoomOutButton.render());
  }
  zoomIn() {
    return this.viewModel.zoomIn();
  }
  zoomOut() {
    return this.viewModel.zoomOut();
  }
};
e([y()], a5.prototype, "iconClass", void 0), e([y()], a5.prototype, "label", null), e([y({ value: "vertical" })], a5.prototype, "layout", null), e([y(), e2("esri/widgets/Zoom/t9n/Zoom")], a5.prototype, "messages", void 0), e([y()], a5.prototype, "view", null), e([y({ type: a4 })], a5.prototype, "viewModel", void 0), a5 = e([a2("esri.widgets.Zoom")], a5);
var c3 = a5;

export {
  c3 as c
};
//# sourceMappingURL=chunk-O6CFGYVQ.js.map
