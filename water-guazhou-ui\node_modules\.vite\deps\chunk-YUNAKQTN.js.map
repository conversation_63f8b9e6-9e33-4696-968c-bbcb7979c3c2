{"version": 3, "sources": ["../../@arcgis/core/webdoc/support/writeUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Error.js\";import{clone as t}from\"../../core/lang.js\";import{isSome as i}from\"../../core/maybe.js\";import{getDeepValue as n}from\"../../core/object.js\";import{numberToJSON as r}from\"../../core/accessorSupport/extensions/serializableProperty/writer.js\";import{isFeatureCollectionLayer as o}from\"../../layers/support/layerUtils.js\";const l=new Set([\"bing-maps\",\"imagery\",\"imagery-tile\",\"map-image\",\"open-street-map\",\"tile\",\"unknown\",\"unsupported\",\"vector-tile\",\"web-tile\",\"wms\",\"wmts\"]),a=new Set([\"csv\",\"feature\",\"geo-rss\",\"geojson\",\"group\",\"imagery\",\"imagery-tile\",\"kml\",\"map-image\",\"map-notes\",\"media\",\"ogc-feature\",\"route\",\"subtype-group\",\"tile\",\"unknown\",\"unsupported\",\"vector-tile\",\"web-tile\",\"wfs\",\"wms\",\"wmts\"]);function s(e){l.delete(e),a.delete(e)}function m(e){l.add(e),a.add(e)}function p(e){return\"basemap\"===e.layerContainerType?l:\"operational-layers\"===e.layerContainerType?a:null}function c(e,t){if(t.restrictedWebMapWriting){const n=p(t);return!i(n)||n.has(e.type)&&!o(e)}return!0}function d(e,t){if(t)if(o(e)){const i=n(\"featureCollection.layers\",t),r=i&&i[0]&&i[0].layerDefinition;r&&u(e,r)}else if(\"stream\"===e.type){u(e,t.layerDefinition=t.layerDefinition||{})}else\"group\"!==e.type&&u(e,t)}function u(e,t){\"maxScale\"in e&&(t.maxScale=r(e.maxScale)??void 0),\"minScale\"in e&&(t.minScale=r(e.minScale)??void 0)}function f(e,t){if(d(e,t),t&&(\"blendMode\"in e&&(t.blendMode=e.blendMode,\"normal\"===t.blendMode&&delete t.blendMode),t.opacity=r(e.opacity)??void 0,t.title=e.title||\"Layer\",t.visibility=e.visible,\"legendEnabled\"in e&&\"wmts\"!==e.type))if(o(e)){const i=t.featureCollection;i&&(i.showLegend=e.legendEnabled)}else t.showLegend=e.legendEnabled}function y(n,r,o){if(!(\"write\"in n)||!n.write)return o&&o.messages&&o.messages.push(new e(\"layer:unsupported\",`Layers (${n.title}, ${n.id}) of type '${n.declaredClass}' cannot be persisted`,{layer:n})),null;if(c(n,o)){const e={};return n.write(e,o)?e:null}return i(r)&&f(n,r=t(r)),r}export{m as disableRestrictedWriting,s as enableRestrictedWriting,y as getLayerJSON};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAIiW,IAAM,IAAE,oBAAI,IAAI,CAAC,aAAY,WAAU,gBAAe,aAAY,mBAAkB,QAAO,WAAU,eAAc,eAAc,YAAW,OAAM,MAAM,CAAC;AAAzJ,IAA2J,IAAE,oBAAI,IAAI,CAAC,OAAM,WAAU,WAAU,WAAU,SAAQ,WAAU,gBAAe,OAAM,aAAY,aAAY,SAAQ,eAAc,SAAQ,iBAAgB,QAAO,WAAU,eAAc,eAAc,YAAW,OAAM,OAAM,MAAM,CAAC;AAAwE,SAASA,GAAE,GAAE;AAAC,SAAM,cAAY,EAAE,qBAAmB,IAAE,yBAAuB,EAAE,qBAAmB,IAAE;AAAI;AAAC,SAAS,EAAE,GAAEC,IAAE;AAAC,MAAGA,GAAE,yBAAwB;AAAC,UAAM,IAAED,GAAEC,EAAC;AAAE,WAAM,CAAC,EAAE,CAAC,KAAG,EAAE,IAAI,EAAE,IAAI,KAAG,CAAC,EAAE,CAAC;AAAA,EAAC;AAAC,SAAM;AAAE;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAGA,GAAE,KAAG,EAAE,CAAC,GAAE;AAAC,UAAM,IAAE,EAAE,4BAA2BA,EAAC,GAAEC,KAAE,KAAG,EAAE,CAAC,KAAG,EAAE,CAAC,EAAE;AAAgB,IAAAA,MAAG,EAAE,GAAEA,EAAC;AAAA,EAAC,WAAS,aAAW,EAAE,MAAK;AAAC,MAAE,GAAED,GAAE,kBAAgBA,GAAE,mBAAiB,CAAC,CAAC;AAAA,EAAC,MAAK,aAAU,EAAE,QAAM,EAAE,GAAEA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,gBAAa,MAAIA,GAAE,WAAS,EAAE,EAAE,QAAQ,KAAG,SAAQ,cAAa,MAAIA,GAAE,WAAS,EAAE,EAAE,QAAQ,KAAG;AAAO;AAAC,SAASE,GAAE,GAAEF,IAAE;AAAC,MAAG,EAAE,GAAEA,EAAC,GAAEA,OAAI,eAAc,MAAIA,GAAE,YAAU,EAAE,WAAU,aAAWA,GAAE,aAAW,OAAOA,GAAE,YAAWA,GAAE,UAAQ,EAAE,EAAE,OAAO,KAAG,QAAOA,GAAE,QAAM,EAAE,SAAO,SAAQA,GAAE,aAAW,EAAE,SAAQ,mBAAkB,KAAG,WAAS,EAAE,MAAM,KAAG,EAAE,CAAC,GAAE;AAAC,UAAM,IAAEA,GAAE;AAAkB,UAAI,EAAE,aAAW,EAAE;AAAA,EAAc,MAAM,CAAAA,GAAE,aAAW,EAAE;AAAa;AAAC,SAASG,GAAE,GAAEF,IAAE,GAAE;AAAC,MAAG,EAAE,WAAU,MAAI,CAAC,EAAE,MAAM,QAAO,KAAG,EAAE,YAAU,EAAE,SAAS,KAAK,IAAI,EAAE,qBAAoB,WAAW,EAAE,KAAK,KAAK,EAAE,EAAE,cAAc,EAAE,aAAa,yBAAwB,EAAC,OAAM,EAAC,CAAC,CAAC,GAAE;AAAK,MAAG,EAAE,GAAE,CAAC,GAAE;AAAC,UAAM,IAAE,CAAC;AAAE,WAAO,EAAE,MAAM,GAAE,CAAC,IAAE,IAAE;AAAA,EAAI;AAAC,SAAO,EAAEA,EAAC,KAAGC,GAAE,GAAED,KAAE,EAAEA,EAAC,CAAC,GAAEA;AAAC;", "names": ["p", "t", "r", "f", "y"]}