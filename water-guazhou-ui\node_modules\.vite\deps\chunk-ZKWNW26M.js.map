{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/EvaluateAmbientOcclusion.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/EvaluateSceneLighting.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{textureSize as e}from\"../util/WebGL2Utils.js\";import{glsl as r}from\"../../shaderModules/interfaces.js\";import{createTexture2DPassSizeUniforms as s}from\"../../shaderModules/Texture2DPassUniform.js\";import{TextureSizeUniformType as o}from\"../../shaderModules/TextureSizeUniformType.js\";import{blurSizePixels as t}from\"../../../lib/SSAOHelper.js\";function n(n,a){const i=n.fragment;a.receiveAmbientOcclusion?(i.uniforms.add(s(\"ssaoTex\",((e,r)=>r.ssaoHelper.colorTexture),a.hasWebGL2Context?o.None:o.InvSize)),i.constants.add(\"blurSizePixelsInverse\",\"float\",1/t),i.code.add(r`\n      float evaluateAmbientOcclusionInverse() {\n        vec2 ssaoTextureSizeInverse = ${e(a,\"ssaoTex\",!0)};\n        return texture2D(ssaoTex, gl_FragCoord.xy * blurSizePixelsInverse * ssaoTextureSizeInverse).a;\n      }\n\n      float evaluateAmbientOcclusion() {\n        return 1.0 - evaluateAmbientOcclusionInverse();\n      }\n    `)):i.code.add(r`float evaluateAmbientOcclusionInverse() { return 1.0; }\nfloat evaluateAmbientOcclusion() { return 0.0; }`)}export{n as EvaluateAmbientOcclusion};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{neverReached as n}from\"../../../../../../core/compilerUtils.js\";import{EvaluateAmbientLighting as i}from\"./EvaluateAmbientLighting.glsl.js\";import{EvaluateAmbientOcclusion as e}from\"./EvaluateAmbientOcclusion.glsl.js\";import{addMainLightDirection as o,addMainLightIntensity as t,MainLighting as a}from\"./MainLighting.glsl.js\";import{PhysicallyBasedRendering as r}from\"./PhysicallyBasedRendering.glsl.js\";import{PBRMode as l}from\"./PhysicallyBasedRenderingParameters.glsl.js\";import{PiUtils as c}from\"./PiUtils.glsl.js\";import{BooleanPassUniform as d}from\"../../shaderModules/BooleanPassUniform.js\";import{FloatPassUniform as s}from\"../../shaderModules/FloatPassUniform.js\";import{glsl as m}from\"../../shaderModules/interfaces.js\";import{ambientBoost as g}from\"../../../lighting/SceneLighting.js\";function h(n){n.constants.add(\"ambientBoostFactor\",\"float\",g)}function u(n){n.uniforms.add(new s(\"lightingGlobalFactor\",((n,i)=>i.lighting.globalFactor)))}function p(g,p){const v=g.fragment;switch(g.include(e,p),p.pbrMode!==l.Disabled&&g.include(r,p),g.include(i,p),g.include(c),v.code.add(m`\n    const float GAMMA_SRGB = 2.1;\n    const float INV_GAMMA_SRGB = 0.4761904;\n    ${p.pbrMode===l.Disabled?\"\":\"const vec3 GROUND_REFLECTANCE = vec3(0.2);\"}\n  `),h(v),u(v),o(v),v.code.add(m`\n    float additionalDirectedAmbientLight(vec3 vPosWorld) {\n      float vndl = dot(${p.spherical?m`normalize(vPosWorld)`:m`vec3(0.0, 0.0, 1.0)`}, mainLightDirection);\n      return smoothstep(0.0, 1.0, clamp(vndl * 2.5, 0.0, 1.0));\n    }\n  `),t(v),v.code.add(m`vec3 evaluateAdditionalLighting(float ambientOcclusion, vec3 vPosWorld) {\nfloat additionalAmbientScale = additionalDirectedAmbientLight(vPosWorld);\nreturn (1.0 - ambientOcclusion) * additionalAmbientScale * ambientBoostFactor * lightingGlobalFactor * mainLightIntensity;\n}`),p.pbrMode){case l.Disabled:case l.WaterOnIntegratedMesh:case l.Water:g.include(a,p),v.code.add(m`vec3 evaluateSceneLighting(vec3 normalWorld, vec3 albedo, float shadow, float ssao, vec3 additionalLight)\n{\nvec3 mainLighting = evaluateMainLighting(normalWorld, shadow);\nvec3 ambientLighting = calculateAmbientIrradiance(normalWorld, ssao);\nvec3 albedoLinear = pow(albedo, vec3(GAMMA_SRGB));\nvec3 totalLight = mainLighting + ambientLighting + additionalLight;\ntotalLight = min(totalLight, vec3(PI));\nvec3 outColor = vec3((albedoLinear / PI) * totalLight);\nreturn pow(outColor, vec3(INV_GAMMA_SRGB));\n}`);break;case l.Normal:case l.Schematic:v.code.add(m`const float fillLightIntensity = 0.25;\nconst float horizonLightDiffusion = 0.4;\nconst float additionalAmbientIrradianceFactor = 0.02;\nvec3 evaluateSceneLightingPBR(vec3 normal, vec3 albedo, float shadow, float ssao, vec3 additionalLight, vec3 viewDir, vec3 normalGround, vec3 mrr, vec3 _emission, float additionalAmbientIrradiance)\n{\nvec3 viewDirection = -viewDir;\nvec3 h = normalize(viewDirection + mainLightDirection);\nPBRShadingInfo inputs;\ninputs.NdotL = clamp(dot(normal, mainLightDirection), 0.001, 1.0);\ninputs.NdotV = clamp(abs(dot(normal, viewDirection)), 0.001, 1.0);\ninputs.NdotH = clamp(dot(normal, h), 0.0, 1.0);\ninputs.VdotH = clamp(dot(viewDirection, h), 0.0, 1.0);\ninputs.NdotNG = clamp(dot(normal, normalGround), -1.0, 1.0);\nvec3 reflectedView = normalize(reflect(viewDirection, normal));\ninputs.RdotNG = clamp(dot(reflectedView, normalGround), -1.0, 1.0);\ninputs.albedoLinear = pow(albedo, vec3(GAMMA_SRGB));\ninputs.ssao = ssao;\ninputs.metalness = mrr[0];\ninputs.roughness = clamp(mrr[1] * mrr[1], 0.001, 0.99);`),v.code.add(m`inputs.f0 = (0.16 * mrr[2] * mrr[2]) * (1.0 - inputs.metalness) + inputs.albedoLinear * inputs.metalness;\ninputs.f90 = vec3(clamp(dot(inputs.f0, vec3(50.0 * 0.33)), 0.0, 1.0));\ninputs.diffuseColor = inputs.albedoLinear * (vec3(1.0) - inputs.f0) * (1.0 - inputs.metalness);`),p.useFillLights?v.uniforms.add(new d(\"hasFillLights\",((n,i)=>i.enableFillLights))):v.constants.add(\"hasFillLights\",\"bool\",!1),v.code.add(m`vec3 ambientDir = vec3(5.0 * normalGround[1] - normalGround[0] * normalGround[2], - 5.0 * normalGround[0] - normalGround[2] * normalGround[1], normalGround[1] * normalGround[1] + normalGround[0] * normalGround[0]);\nambientDir = ambientDir != vec3(0.0)? normalize(ambientDir) : normalize(vec3(5.0, -1.0, 0.0));\ninputs.NdotAmbDir = hasFillLights ? abs(dot(normal, ambientDir)) : 1.0;\nvec3 mainLightIrradianceComponent = inputs.NdotL * (1.0 - shadow) * mainLightIntensity;\nvec3 fillLightsIrradianceComponent = inputs.NdotAmbDir * mainLightIntensity * fillLightIntensity;\nvec3 ambientLightIrradianceComponent = calculateAmbientIrradiance(normal, ssao) + additionalLight;\ninputs.skyIrradianceToSurface = ambientLightIrradianceComponent + mainLightIrradianceComponent + fillLightsIrradianceComponent ;\ninputs.groundIrradianceToSurface = GROUND_REFLECTANCE * ambientLightIrradianceComponent + mainLightIrradianceComponent + fillLightsIrradianceComponent ;`),v.uniforms.add([new s(\"lightingSpecularStrength\",((n,i)=>i.lighting.mainLight.specularStrength)),new s(\"lightingEnvironmentStrength\",((n,i)=>i.lighting.mainLight.environmentStrength))]),v.code.add(m`vec3 horizonRingDir = inputs.RdotNG * normalGround - reflectedView;\nvec3 horizonRingH = normalize(viewDirection + horizonRingDir);\ninputs.NdotH_Horizon = dot(normal, horizonRingH);\nvec3 mainLightRadianceComponent = lightingSpecularStrength * normalDistribution(inputs.NdotH, inputs.roughness) * mainLightIntensity * (1.0 - shadow);\nvec3 horizonLightRadianceComponent = lightingEnvironmentStrength * normalDistribution(inputs.NdotH_Horizon, min(inputs.roughness + horizonLightDiffusion, 1.0)) * mainLightIntensity * fillLightIntensity;\nvec3 ambientLightRadianceComponent = lightingEnvironmentStrength * calculateAmbientRadiance(ssao) + additionalLight;\ninputs.skyRadianceToSurface = ambientLightRadianceComponent + mainLightRadianceComponent + horizonLightRadianceComponent;\ninputs.groundRadianceToSurface = GROUND_REFLECTANCE * (ambientLightRadianceComponent + horizonLightRadianceComponent) + mainLightRadianceComponent;\ninputs.averageAmbientRadiance = ambientLightIrradianceComponent[1] * (1.0 + GROUND_REFLECTANCE[1]);`),v.code.add(m`\n        vec3 reflectedColorComponent = evaluateEnvironmentIllumination(inputs);\n        vec3 additionalMaterialReflectanceComponent = inputs.albedoLinear * additionalAmbientIrradiance;\n        vec3 emissionComponent = pow(_emission, vec3(GAMMA_SRGB));\n        vec3 outColorLinear = reflectedColorComponent + additionalMaterialReflectanceComponent + emissionComponent;\n        ${p.pbrMode===l.Schematic?m`vec3 outColor = pow(max(vec3(0.0), outColorLinear - 0.005 * inputs.averageAmbientRadiance), vec3(INV_GAMMA_SRGB));`:m`vec3 outColor = pow(blackLevelSoftCompression(outColorLinear, inputs), vec3(INV_GAMMA_SRGB));`}\n        return outColor;\n      }\n    `);break;case l.Terrain:case l.TerrainWithWater:g.include(a,p),v.code.add(m`const float roughnessTerrain = 0.5;\nconst float specularityTerrain = 0.5;\nconst vec3 fresnelReflectionTerrain = vec3(0.04);\nvec3 evaluateTerrainLighting(vec3 n, vec3 c, float shadow, float ssao, vec3 al, vec3 vd, vec3 nup) {\nvec3 viewDirection = -vd;\nvec3 h = normalize(viewDirection + mainLightDirection);\nfloat NdotL = clamp(dot(n, mainLightDirection), 0.001, 1.0);\nfloat NdotV = clamp(abs(dot(n, viewDirection)), 0.001, 1.0);\nfloat NdotH = clamp(dot(n, h), 0.0, 1.0);\nfloat NdotNG = clamp(dot(n, nup), -1.0, 1.0);\nvec3 albedoLinear = pow(c, vec3(GAMMA_SRGB));\nfloat lightness = 0.3 * albedoLinear[0] + 0.5 * albedoLinear[1] + 0.2 * albedoLinear[2];\nvec3 f0 = (0.85 * lightness + 0.15) * fresnelReflectionTerrain;\nvec3 f90 =  vec3(clamp(dot(f0, vec3(50.0 * 0.33)), 0.0, 1.0));\nvec3 mainLightIrradianceComponent = (1. - shadow) * NdotL * mainLightIntensity;\nvec3 ambientLightIrradianceComponent = calculateAmbientIrradiance(n, ssao) + al;\nvec3 ambientSky = ambientLightIrradianceComponent + mainLightIrradianceComponent;\nvec3 indirectDiffuse = ((1.0 - NdotNG) * mainLightIrradianceComponent + (1.0 + NdotNG ) * ambientSky) * 0.5;\nvec3 outDiffColor = albedoLinear * (1.0 - f0) * indirectDiffuse / PI;\nvec3 mainLightRadianceComponent = normalDistribution(NdotH, roughnessTerrain) * mainLightIntensity;\nvec2 dfg = prefilteredDFGAnalytical(roughnessTerrain, NdotV);\nvec3 specularColor = f0 * dfg.x + f90 * dfg.y;\nvec3 specularComponent = specularityTerrain * specularColor * mainLightRadianceComponent;\nvec3 outColorLinear = outDiffColor + specularComponent;\nvec3 outColor = pow(outColorLinear, vec3(INV_GAMMA_SRGB));\nreturn outColor;\n}`);break;default:n(p.pbrMode);case l.COUNT:}}export{p as EvaluateSceneLighting,h as addAmbientBoostFactor,u as addLightingGlobalFactor};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+V,SAASA,GAAEA,IAAEC,IAAE;AAAC,QAAM,IAAED,GAAE;AAAS,EAAAC,GAAE,2BAAyB,EAAE,SAAS,IAAI,EAAE,WAAW,CAACC,IAAEC,OAAIA,GAAE,WAAW,cAAcF,GAAE,mBAAiB,EAAE,OAAK,EAAE,OAAO,CAAC,GAAE,EAAE,UAAU,IAAI,yBAAwB,SAAQ,IAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,wCAEzhB,EAAEA,IAAE,WAAU,IAAE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAOpD,KAAG,EAAE,KAAK,IAAI;AAAA,iDAC8B;AAAC;;;ACVivB,SAAS,EAAEG,IAAE;AAAC,EAAAA,GAAE,UAAU,IAAI,sBAAqB,SAAQ,CAAC;AAAC;AAAC,SAASC,GAAED,IAAE;AAAC,EAAAA,GAAE,SAAS,IAAI,IAAIE,GAAE,wBAAwB,CAACF,IAAE,MAAI,EAAE,SAAS,YAAa,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEG,IAAE;AAAC,QAAM,IAAE,EAAE;AAAS,UAAO,EAAE,QAAQH,IAAEG,EAAC,GAAEA,GAAE,YAAU,EAAE,YAAU,EAAE,QAAQH,IAAEG,EAAC,GAAE,EAAE,QAAQ,GAAEA,EAAC,GAAE,EAAE,QAAQ,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,MAG/jCA,GAAE,YAAU,EAAE,WAAS,KAAG,4CAA4C;AAAA,GACzE,GAAE,EAAE,CAAC,GAAEF,GAAE,CAAC,GAAEC,GAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,yBAENC,GAAE,YAAU,0BAAwB,sBAAsB;AAAA;AAAA;AAAA,GAGhF,GAAE,EAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAGnB,GAAEA,GAAE,SAAQ;AAAA,IAAC,KAAK,EAAE;AAAA,IAAS,KAAK,EAAE;AAAA,IAAsB,KAAK,EAAE;AAAM,QAAE,QAAQC,IAAED,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjG;AAAE;AAAA,IAAM,KAAK,EAAE;AAAA,IAAO,KAAK,EAAE;AAAU,QAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wDAkBI,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,gGAE2B,GAAEA,GAAE,gBAAc,EAAE,SAAS,IAAI,IAAI,EAAE,iBAAiB,CAACH,IAAE,MAAI,EAAE,gBAAiB,CAAC,IAAE,EAAE,UAAU,IAAI,iBAAgB,QAAO,KAAE,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yJAOlF,GAAE,EAAE,SAAS,IAAI,CAAC,IAAIE,GAAE,4BAA4B,CAACF,IAAE,MAAI,EAAE,SAAS,UAAU,gBAAiB,GAAE,IAAIE,GAAE,+BAA+B,CAACF,IAAE,MAAI,EAAE,SAAS,UAAU,mBAAoB,CAAC,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oGAQ5P,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,UAKvGG,GAAE,YAAU,EAAE,YAAU,wHAAsH,gGAAgG;AAAA;AAAA;AAAA,KAGnP;AAAE;AAAA,IAAM,KAAK,EAAE;AAAA,IAAQ,KAAK,EAAE;AAAiB,QAAE,QAAQC,IAAED,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0B5E;AAAE;AAAA,IAAM;AAAQ,QAAEA,GAAE,OAAO;AAAA,IAAE,KAAK,EAAE;AAAA,EAAM;AAAC;", "names": ["n", "a", "e", "r", "n", "u", "o", "p", "r"]}