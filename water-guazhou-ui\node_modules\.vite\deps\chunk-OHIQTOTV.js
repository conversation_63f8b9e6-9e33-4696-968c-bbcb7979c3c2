import {
  a as a5,
  c as c2,
  u as u3
} from "./chunk-7GO2KORS.js";
import {
  e as e6
} from "./chunk-M3F5NDOQ.js";
import {
  s
} from "./chunk-TDGDXUFC.js";
import {
  c,
  i as i3,
  t as t2
} from "./chunk-F26DNX7C.js";
import {
  e2 as e4,
  o2 as o5,
  o3 as o6
} from "./chunk-TOYJMVHA.js";
import {
  d
} from "./chunk-BOT4BSSB.js";
import {
  a as a3
} from "./chunk-QB6AUIQ2.js";
import {
  e as e5
} from "./chunk-UB5FTTH5.js";
import {
  o as o4,
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u as u2
} from "./chunk-IRHOIB3A.js";
import {
  o as o3
} from "./chunk-BPRRRPC3.js";
import {
  e
} from "./chunk-GXMOAZWH.js";
import {
  a as a4
} from "./chunk-WL2F66AK.js";
import {
  a as a2
} from "./chunk-6OHWWYET.js";
import {
  o as o7
} from "./chunk-TUB4N6LD.js";
import {
  e as e2,
  e2 as e3,
  f,
  i as i2,
  u
} from "./chunk-YV4RKNU4.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";
import {
  a,
  r as r2
} from "./chunk-SROTSYJS.js";
import {
  i,
  n
} from "./chunk-FOE4ICAJ.js";
import {
  l
} from "./chunk-NOZFLZZL.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/hud/HUDOcclusionPass.glsl.js
function p(p2, l2) {
  const { vertex: d2, fragment: c3 } = p2;
  l2.hasMultipassGeometry && d2.include(a5), l2.hasMultipassTerrain && p2.varyings.add("depth", "float"), d2.code.add(o`
  void main(void) {
    vec4 posProjCenter;
    if (dot(position, position) > 0.0) {
      // Render single point to center of the pixel to avoid subpixel
      // filtering to affect the marker color
      ProjectHUDAux projectAux;
      vec4 posProj = projectPositionHUD(projectAux);
      posProjCenter = alignToPixelCenter(posProj, viewport.zw);

      ${l2.hasMultipassGeometry ? o`
        // Don't draw vertices behind geometry
        if(geometryDepthTest(.5 + .5 * posProjCenter.xy / posProjCenter.w, projectAux.posView.z)){
          posProjCenter = vec4(1e038, 1e038, 1e038, 1.0);
        }` : ""}

      ${l2.hasMultipassTerrain ? "depth = projectAux.posView.z;" : ""}
      vec3 vpos = projectAux.posModel;
      if (rejectBySlice(vpos)) {
        // Project out of clip space
        posProjCenter = vec4(1e038, 1e038, 1e038, 1.0);
      }

    } else {
      // Project out of clip space
      posProjCenter = vec4(1e038, 1e038, 1e038, 1.0);
    }

    gl_Position = posProjCenter;
    gl_PointSize = 1.0;
  }
  `), l2.hasMultipassTerrain && c3.include(a4), l2.hasMultipassTerrain && c3.uniforms.add([...u("terrainDepthTexture", (e7, r3) => r3.multipassTerrain.linearDepthTexture, l2.hasWebGL2Context ? e3.None : e3.InvSize), new e2("nearFar", (e7, r3) => r3.camera.nearFar)]), c3.include(a2), c3.code.add(o`
  void main() {
    gl_FragColor = vec4(1, 1, 1, 1);
    ${l2.hasMultipassTerrain ? o`
          vec2 uv = gl_FragCoord.xy;

          // Read the rgba data from the texture linear depth
          vec4 terrainDepthData = ${i2(l2, "terrainDepthTexture", "uv")};

          float terrainDepth = linearDepthFromFloat(rgba2float(terrainDepthData), nearFar);

          // If HUD vertex is behind terrain and the terrain depth is not the initialize value (e.g. we are not looking at the sky)
          // Mark the HUD vertex as occluded by transparent terrain
          if(depth < terrainDepth && terrainDepthData != vec4(0,0,0,1)){
            gl_FragColor.g = 0.5;
          }` : ""}
  }
  `);
}

// node_modules/@arcgis/core/chunks/HUDMaterial.glsl.js
function B(r3) {
  const l2 = new o2(), B2 = r3.signedDistanceFieldEnabled;
  if (l2.include(c2), l2.include(u3, r3), l2.include(u2, r3), r3.occlusionPass) return l2.include(p, r3), l2;
  const { vertex: T2, fragment: V2 } = l2;
  l2.include(c), V2.include(a2), V2.include(e5), l2.include(s, r3), l2.include(d, r3), l2.varyings.add("vcolor", "vec4"), l2.varyings.add("vtc", "vec2"), l2.varyings.add("vsize", "vec2"), r3.binaryHighlightOcclusionEnabled && l2.varyings.add("voccluded", "float"), T2.uniforms.add([new e("viewport", (e7, o8) => o8.camera.fullViewport), new e2("screenOffset", (e7, r4) => r2(U, 2 * e7.screenOffset[0] * r4.camera.pixelRatio, 2 * e7.screenOffset[1] * r4.camera.pixelRatio)), new e2("anchorPosition", (e7) => H(e7)), new e("materialColor", (e7) => e7.color), new o7("pixelRatio", (e7, o8) => o8.camera.pixelRatio)]), B2 && (T2.uniforms.add(new e("outlineColor", (e7) => e7.outlineColor)), V2.uniforms.add([new e("outlineColor", (e7) => _(e7) ? e7.outlineColor : l), new o7("outlineSize", (e7) => _(e7) ? e7.outlineSize : 0)])), r3.hasScreenSizePerspective && (t2(T2), i3(T2)), (r3.debugDrawLabelBorder || r3.binaryHighlightOcclusionEnabled) && l2.varyings.add("debugBorderCoords", "vec4"), l2.attributes.add(O.UV0, "vec2"), l2.attributes.add(O.COLOR, "vec4"), l2.attributes.add(O.SIZE, "vec2"), l2.attributes.add(O.AUXPOS2, "vec4"), T2.code.add(o`
    void main(void) {
      ProjectHUDAux projectAux;
      vec4 posProj = projectPositionHUD(projectAux);
      forwardObjectAndLayerIdColor();

      if (rejectBySlice(projectAux.posModel)) {
        // Project outside of clip plane
        gl_Position = vec4(1e038, 1e038, 1e038, 1.0);
        return;
      }
      vec2 inputSize;
      ${r3.hasScreenSizePerspective ? o`
      inputSize = screenSizePerspectiveScaleVec2(size, projectAux.absCosAngle, projectAux.distanceToCamera, screenSizePerspective);
      vec2 screenOffsetScaled = screenSizePerspectiveScaleVec2(screenOffset, projectAux.absCosAngle, projectAux.distanceToCamera, screenSizePerspectiveAlignment);
         ` : o`
      inputSize = size;
      vec2 screenOffsetScaled = screenOffset;`}

      ${r3.vvSize ? "inputSize *= vvScale(auxpos2).xx;" : ""}

      vec2 combinedSize = inputSize * pixelRatio;
      vec4 quadOffset = vec4(0.0);

      ${r3.occlusionTestEnabled || r3.binaryHighlightOcclusionEnabled ? "bool visible = testVisibilityHUD(posProj);" : ""}

      ${r3.binaryHighlightOcclusionEnabled ? "voccluded = visible ? 0.0 : 1.0;" : ""}
    `);
  const E = o`vec2 uv01 = floor(uv0);
vec2 uv = uv0 - uv01;
quadOffset.xy = ((uv01 - anchorPosition) * 2.0 * combinedSize + screenOffsetScaled) / viewport.zw * posProj.w;`, M = r3.pixelSnappingEnabled ? B2 ? o`posProj = alignToPixelOrigin(posProj, viewport.zw) + quadOffset;` : o`posProj += quadOffset;
if (inputSize.x == size.x) {
posProj = alignToPixelOrigin(posProj, viewport.zw);
}` : o`posProj += quadOffset;`;
  r3.vvColor && T2.uniforms.add([new e4("vvColorColors", (e7) => e7.vvColorColors, o6), new o5("vvColorValues", (e7) => e7.vvColorValues, o6)]), T2.uniforms.add(new e2("textureCoordinateScaleFactor", (o8) => r(o8.texture) && r(o8.texture.descriptor.textureCoordinateScaleFactor) ? o8.texture.descriptor.textureCoordinateScaleFactor : i)), T2.code.add(o`
    ${r3.occlusionTestEnabled ? "if (visible) {" : ""}
    ${E}
    ${r3.vvColor ? "vcolor = vvGetColor(auxpos2, vvColorValues, vvColorColors) * materialColor;" : "vcolor = color / 255.0 * materialColor;"}

    ${r3.output === h.ObjectAndLayerIdColor ? o`vcolor.a = 1.0;` : ""}

    bool alphaDiscard = vcolor.a < ${o.float(t)};
    ${B2 ? `alphaDiscard = alphaDiscard && outlineColor.a < ${o.float(t)};` : ""}
    if (alphaDiscard) {
      // "early discard" if both symbol color (= fill) and outline color (if applicable) are transparent
      gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
      return;
    } else {
      ${M}
      gl_Position = posProj;
    }

    vtc = uv * textureCoordinateScaleFactor;

    ${r3.debugDrawLabelBorder ? "debugBorderCoords = vec4(uv01, 1.5 / combinedSize);" : ""}
    vsize = inputSize;
    ${r3.occlusionTestEnabled ? o`} else { vtc = vec2(0.0);
      ${r3.debugDrawLabelBorder ? "debugBorderCoords = vec4(0.5, 0.5, 1.5 / combinedSize);}" : "}"}` : ""}
  }
  `), V2.uniforms.add(new f("tex", (e7) => e7.texture));
  const I = r3.debugDrawLabelBorder ? o`(isBorder > 0.0 ? 0.0 : ${o.float(o4)})` : o.float(o4), R = o`
    ${r3.debugDrawLabelBorder ? o`
      float isBorder = float(any(lessThan(debugBorderCoords.xy, debugBorderCoords.zw)) || any(greaterThan(debugBorderCoords.xy, 1.0 - debugBorderCoords.zw)));` : ""}

    ${B2 ? o`
      vec4 fillPixelColor = vcolor;

      // Attempt to sample texel centers to avoid that thin cross outlines
      // disappear with large symbol sizes.
      // see: https://devtopia.esri.com/WebGIS/arcgis-js-api/issues/7058#issuecomment-603041
      const float txSize = ${o.float(e6)};
      const float texelSize = 1.0 / txSize;
      // Calculate how much we have to add/subtract to/from each texel to reach the size of an onscreen pixel
      vec2 scaleFactor = (vsize - txSize) * texelSize;
      vec2 samplePos = vtc + (vec2(1.0, -1.0) * texelSize) * scaleFactor;

      // Get distance and map it into [-0.5, 0.5]
      float d = rgba2float(texture2D(tex, samplePos)) - 0.5;

      // Distance in output units (i.e. pixels)
      float dist = d * vsize.x;

      // Create smooth transition from the icon into its outline
      float fillAlphaFactor = clamp(0.5 - dist, 0.0, 1.0);
      fillPixelColor.a *= fillAlphaFactor;

      if (outlineSize > 0.25) {
        vec4 outlinePixelColor = outlineColor;
        float clampedOutlineSize = min(outlineSize, 0.5*vsize.x);

        // Create smooth transition around outline
        float outlineAlphaFactor = clamp(0.5 - (abs(dist) - 0.5*clampedOutlineSize), 0.0, 1.0);
        outlinePixelColor.a *= outlineAlphaFactor;

        if (
          outlineAlphaFactor + fillAlphaFactor < ${I} ||
          fillPixelColor.a + outlinePixelColor.a < ${o.float(t)}
        ) {
          discard;
        }

        // perform un-premultiplied over operator (see https://en.wikipedia.org/wiki/Alpha_compositing#Description)
        float compositeAlpha = outlinePixelColor.a + fillPixelColor.a * (1.0 - outlinePixelColor.a);
        vec3 compositeColor = vec3(outlinePixelColor) * outlinePixelColor.a +
          vec3(fillPixelColor) * fillPixelColor.a * (1.0 - outlinePixelColor.a);

        gl_FragColor = vec4(compositeColor, compositeAlpha);
      } else {
        if (fillAlphaFactor < ${I}) {
          discard;
        }

        gl_FragColor = premultiplyAlpha(fillPixelColor);
      }

      // visualize SDF:
      // gl_FragColor = vec4(clamp(-dist/vsize.x*2.0, 0.0, 1.0), clamp(dist/vsize.x*2.0, 0.0, 1.0), 0.0, 1.0);
      ` : o`
          vec4 texColor = texture2D(tex, vtc, -0.5);
          if (texColor.a < ${I}) {
            discard;
          }
          gl_FragColor = texColor * premultiplyAlpha(vcolor);
          `}

    // Draw debug border with transparency, so that original texels along border are still partially visible
    ${r3.debugDrawLabelBorder ? o`gl_FragColor = mix(gl_FragColor, vec4(1.0, 0.0, 1.0, 1.0), isBorder * 0.5);` : ""}
  `;
  return r3.output === h.Alpha && V2.code.add(o`
      void main() {
        ${R}
        gl_FragColor = vec4(gl_FragColor.a);
      }
      `), r3.output === h.ObjectAndLayerIdColor && V2.code.add(o`
      void main() {
        ${R}
        outputObjectAndLayerIdColor();
      }
      `), r3.output === h.Color && V2.code.add(o`
    void main() {
      ${R}
      ${r3.transparencyPassType === o3.FrontFace ? "gl_FragColor.rgb /= gl_FragColor.a;" : ""}
    }
    `), r3.output === h.Highlight && (l2.include(a3, r3), V2.code.add(o`
    void main() {
      ${R}
      ${r3.binaryHighlightOcclusionEnabled ? o`
          if (voccluded == 1.0) {
            gl_FragColor = vec4(1.0, 1.0, 0.0, 1.0);
          } else {
            gl_FragColor = vec4(1.0, 0.0, 1.0, 1.0);
          }` : "outputHighlight();"}
    }
    `)), l2;
}
function _(e7) {
  return e7.outlineColor[3] > 0 && e7.outlineSize > 0;
}
function H(e7, o8 = U) {
  return e7.textureIsSignedDistanceField ? T(e7.anchorPosition, e7.distanceFieldBoundingBox, o8) : a(o8, e7.anchorPosition), o8;
}
function T(r3, i4, l2) {
  r(i4) ? r2(l2, r3[0] * (i4[2] - i4[0]) + i4[0], r3[1] * (i4[3] - i4[1]) + i4[1]) : r2(l2, 0, 0);
}
var U = n();
var V = Object.freeze(Object.defineProperty({ __proto__: null, build: B, calculateAnchorPosForRendering: H }, Symbol.toStringTag, { value: "Module" }));

export {
  B,
  H,
  V
};
//# sourceMappingURL=chunk-OHIQTOTV.js.map
