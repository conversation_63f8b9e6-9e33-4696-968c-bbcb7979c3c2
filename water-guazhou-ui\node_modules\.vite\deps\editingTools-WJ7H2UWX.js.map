{"version": 3, "sources": ["../../@arcgis/core/views/2d/interactive/editingTools/draw/symbols.js", "../../@arcgis/core/views/2d/interactive/editingTools/draw/DrawGraphicTool2D.js", "../../@arcgis/core/core/analysisThemeUtils.js", "../../@arcgis/core/views/2d/interactive/editingTools/manipulations/Manipulation.js", "../../@arcgis/core/views/2d/interactive/editingTools/manipulations/DragManipulation.js", "../../@arcgis/core/views/2d/interactive/editingTools/ControlPointsTransformTool.js", "../../@arcgis/core/views/2d/interactive/editingTools/manipulations/utils.js", "../../@arcgis/core/views/2d/interactive/editingTools/manipulations/RotateManipulation.js", "../../@arcgis/core/views/2d/interactive/editingTools/manipulations/ScaleManipulation.js", "../../@arcgis/core/views/2d/interactive/editingTools/TransformTool.js", "../../@arcgis/core/views/2d/interactive/editingTools/MediaTransformToolsWrapper.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../../symbols.js\";import e from\"../../../../../symbols/CIMSymbol.js\";import o from\"../../../../../symbols/SimpleMarkerSymbol.js\";const t=new e({data:{type:\"CIMSymbolReference\",symbol:{type:\"CIMLineSymbol\",symbolLayers:[{type:\"CIMSolidStroke\",effects:[{type:\"CIMGeometricEffectDashes\",dashTemplate:[3.75,3.75],lineDashEnding:\"HalfPattern\",controlPointEnding:\"NoConstraint\"}],enable:!0,capStyle:\"Butt\",joinStyle:\"Round\",miterLimit:10,width:1.6,color:[255,255,255,255]},{type:\"CIMSolidStroke\",enable:!0,capStyle:\"Butt\",joinStyle:\"Round\",miterLimit:10,width:2,color:[0,0,0,255]}]}}}),l=new o({style:\"circle\",size:6,color:[127,127,127,1],outline:{color:[50,50,50],width:1}}),i=new o({style:\"circle\",size:6,color:[255,127,0,1],outline:{color:[50,50,50],width:1}});export{i as activeVertex,t as outline,l as regularVertices};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import\"../../../../../geometry.js\";import t from\"../../../../../Graphic.js\";import{makeHandle as i}from\"../../../../../core/handleUtils.js\";import{unwrapOr as r,unwrap as s,isSome as a,destroyMaybe as l}from\"../../../../../core/maybe.js\";import{property as n}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as o}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{SnappingVisualizer2D as c}from\"../../SnappingVisualizer2D.js\";import{outline as p,regularVertices as h,activeVertex as m}from\"./symbols.js\";import{DrawGraphicTool as u,geometryTypeToDrawOperationGeometryType as y}from\"../../../../draw/DrawGraphicTool.js\";import{DrawOperation as v}from\"../../../../draw/DrawOperation.js\";import{MapDrawSurface as d}from\"../../../../draw/drawSurfaces.js\";import g from\"../../../../../geometry/Point.js\";import G from\"../../../../../geometry/Multipoint.js\";let E=class extends u{constructor(e){super(e),this._visualElementGraphics={outline:null,regularVertices:null,activeVertex:null},this.activeFillSymbol=null,this.type=\"draw-2d\",this._visualElementSymbols={outline:r(e.activeLineSymbol,p),regularVertices:r(e.regularVerticesSymbol,h),activeVertex:r(e.activeVertexSymbol,m),fill:s(e.activeFillSymbol)}}normalizeCtorArgs(e){const t={...e};return delete t.activeFillSymbol,delete t.activeVertexSymbol,delete t.regularVerticesSymbol,delete t.activeLineSymbol,t}initializeGraphic(e){return a(this._visualElementSymbols.fill)&&(e.symbol=this._visualElementSymbols.fill),null}makeDrawOperation(){const{defaultZ:e,hasZ:t,view:i}=this;return new v({view:i,manipulators:this.manipulators,geometryType:y(this.geometryType),drawingMode:this.mode,hasZ:t,defaultZ:e,snapToSceneEnabled:this.snapToScene,drawSurface:new d(i,t,e),hasM:!1,snappingManager:this.snappingManager,snappingVisualizer:new c(this.internalGraphicsLayer),tooltipOptions:this.tooltipOptions})}onActiveVertexChanged(e){if(\"point\"===this.geometryType)return null;const[r,s]=e,n=new g({x:r,y:s,spatialReference:this.view.spatialReference});return a(this._visualElementGraphics.activeVertex)?(this._visualElementGraphics.activeVertex.geometry=n,null):(this._visualElementGraphics.activeVertex=new t({geometry:n,symbol:this._visualElementSymbols.activeVertex,attributes:{displayOrder:2}}),this.internalGraphicsLayer.add(this._visualElementGraphics.activeVertex),this.internalGraphicsLayer.graphics.sort(_),i((()=>{a(this._visualElementGraphics.activeVertex)&&(this.internalGraphicsLayer.remove(this._visualElementGraphics.activeVertex),this._visualElementGraphics.activeVertex=l(this._visualElementGraphics.activeVertex))})))}onOutlineChanged(e){const r=e.clone();if(\"polyline\"===r.type){const e=r.paths[r.paths.length-1];e.splice(0,e.length-2)}return a(this._visualElementGraphics.outline)?(this._visualElementGraphics.outline.geometry=r,null):(this._visualElementGraphics.outline=new t({geometry:r,symbol:this._visualElementSymbols.outline,attributes:{displayOrder:0}}),this.internalGraphicsLayer.add(this._visualElementGraphics.outline),this.internalGraphicsLayer.graphics.sort(_),i((()=>{a(this._visualElementGraphics.outline)&&(this.internalGraphicsLayer.remove(this._visualElementGraphics.outline),this._visualElementGraphics.outline=l(this._visualElementGraphics.outline))})))}onRegularVerticesChanged(e){const r=new G({points:e,spatialReference:this.view.spatialReference});return a(this._visualElementGraphics.regularVertices)?(this._visualElementGraphics.regularVertices.geometry=r,null):(this._visualElementGraphics.regularVertices=new t({geometry:r,symbol:this._visualElementSymbols.regularVertices,attributes:{displayOrder:1}}),this.internalGraphicsLayer.add(this._visualElementGraphics.regularVertices),this.internalGraphicsLayer.graphics.sort(_),i((()=>{a(this._visualElementGraphics.regularVertices)&&(this.internalGraphicsLayer.remove(this._visualElementGraphics.regularVertices),this._visualElementGraphics.regularVertices=l(this._visualElementGraphics.regularVertices))})))}};function _(e,t){return(e.attributes?.displayOrder??-1/0)-(t.attributes?.displayOrder??-1/0)}e([n()],E.prototype,\"activeFillSymbol\",void 0),e([n({readOnly:!0})],E.prototype,\"type\",void 0),e([n({constructOnly:!0,nonNullable:!0})],E.prototype,\"view\",void 0),E=e([o(\"esri.views.2d.interactive.draw.DrawGraphicTool2D\")],E);export{E as DrawGraphicTool2D};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport n from\"../Color.js\";import o from\"../config.js\";import{getColorLuminance as r}from\"../views/support/colorUtils.js\";function t(o,t){const e=o.a*t;return r(o)>225?new n([0,0,0,e]):new n([255,255,255,e])}function e(o,r){const t=new n(o);return t.a*=r,t}function i(n=1){return e(o.analysisTheme.accentColor,n)}function u(n=1){return t(i(),n)}function c(n=1){return e(o.analysisTheme.textColor,n)}function s(n=1){return t(c(),n)}export{i as getAccentColor,u as getContrastColor,c as getTextColor,s as getTextHaloColor};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass r{get hovering(){return this.someManipulator((r=>r.hovering))}get grabbing(){return this.someManipulator((r=>r.grabbing))}get dragging(){return this.someManipulator((r=>r.dragging))}hasManipulator(r){return this.someManipulator((t=>t===r))}someManipulator(r){let t=!1;return this.forEachManipulator((a=>{!t&&r(a)&&(t=!0)})),t}}var t;!function(r){r[r.TRANSLATE_XY=0]=\"TRANSLATE_XY\",r[r.SCALE=1]=\"SCALE\",r[r.ROTATE=2]=\"ROTATE\"}(t||(t={}));export{r as Manipulation,t as ManipulatorType};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrap as t}from\"../../../../../core/maybe.js\";import{Manipulation as i,ManipulatorType as r}from\"./Manipulation.js\";import{createManipulatorDragEventPipeline as o,screenToMap as a}from\"../../../../interactive/dragEventPipeline.js\";import{GraphicManipulator as e}from\"../../../../interactive/GraphicManipulator.js\";import{AccumulationBehaviour as s}from\"../../../../interactive/editGeometry/interfaces.js\";import{getSymbolInfo as n}from\"../../../../interactive/support/utils.js\";class c extends i{constructor(t){super(),this._view=t.view,this._tool=t.tool,this._graphic=t.graphic,this._manipulator=this._createManipulator(),this.forEachManipulator((t=>this._tool.manipulators.add(t)))}destroy(){this.forEachManipulator((t=>{this._tool.manipulators.remove(t),t.destroy()})),this._tool=null,this._view=null,this._manipulator=null,this._graphic=null}forEachManipulator(t){t(this._manipulator,r.TRANSLATE_XY)}createDragPipeline(i,r){let e=null,c=null,p=0,l=0,m=0;const{offsetX:h,offsetY:u,size:_}=n(t(this._graphic.symbol));return o(this._manipulator,((t,o)=>{o.next((t=>{if(\"start\"===t.action){const t=i();e=t.editGeometryOperations,c=t.constraints}return t})).next(a(this._view)).next((t=>{const{x:i,y:o,z:a}=t.mapEnd;if(c&&(i+h<c.xmin||o+u-_<c.ymin||i+h>c.xmax||o+u-_>c.ymax))return t;\"start\"===t.action&&(p=t.mapStart.x,l=t.mapStart.y,m=t.mapStart.z);const n=i-p,f=o-l,v=a-m;p=i,l=o,m=a;const x=[];for(const r of e.data.components)x.push(...r.vertices);const d=\"start\"===t.action?s.NEW_STEP:s.ACCUMULATE_STEPS,y=e.moveVertices(x,n,f,v,d);return r(t,y),t}))}))}_createManipulator(){const t=this._view,i=this._graphic;return new e({view:t,graphic:i,selectable:!0,cursor:\"move\"})}}export{c as DragManipulation};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../../chunks/tslib.es6.js\";import e from\"../../../../Color.js\";import i from\"../../../../Graphic.js\";import\"../../../../symbols.js\";import{getAccentColor as o}from\"../../../../core/analysisThemeUtils.js\";import{destroyMaybe as r,isNone as s,unwrap as a}from\"../../../../core/maybe.js\";import{watch as n}from\"../../../../core/reactiveUtils.js\";import{property as c}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as h}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{project as l,initializeProjection as p}from\"../../../../geometry/projection.js\";import{isValid as d}from\"../../../../geometry/support/spatialReferenceUtils.js\";import m from\"../../../../layers/GraphicsLayer.js\";import{applyCIMSymbolColor as y}from\"../../../../symbols/support/cimSymbolUtils.js\";import{ViewingMode as _}from\"../../../ViewingMode.js\";import{DragManipulation as g}from\"./manipulations/DragManipulation.js\";import{primaryKey as f}from\"../../../input/keys.js\";import{InteractiveToolBase as u}from\"../../../interactive/InteractiveToolBase.js\";import{EditGeometryOperations as P}from\"../../../interactive/editGeometry/EditGeometryOperations.js\";import{AccumulationBehaviour as v}from\"../../../interactive/editGeometry/interfaces.js\";import S from\"../../../../symbols/CIMSymbol.js\";const k={up:\"ArrowUp\",down:\"ArrowDown\",left:\"ArrowLeft\",right:\"ArrowRight\",toggleOpacity:\"t\",shift:\"Shift\",primaryKey:f},b=1,G=10,M=new e(\"#009AF2\");let w=class extends u{constructor(t){super(t),this._isOpacityToggled=!1,this._isModifierActive=!1,this._factor=1,this._initialControlPoints=null,this._graphicsLayer=new m({internal:!0,listMode:\"hide\",visible:!1,effect:\"drop-shadow(0px, 0px, 3px)\"}),this._undoStack=[],this._redoStack=[],this._sharedUndoStack=[],this._sharedRedoStack=[],this._highlightHandle=null,this.activeHandle=0}initialize(){this._initialize()}destroy(){const{map:t}=this.view;this._controlPointManipulations.forEach((t=>t.destroy())),this._controlPointEditGeometryOperations.forEach((t=>t.destroy())),t.removeMany([this._graphicsLayer]),this._graphicsLayer.removeAll(),this._graphicsLayer=r(this._graphicsLayer),this._georeference=null,this._controlPointGraphics=null,this._controlPointManipulations=null,this._graphicsLayer=null,this._controlPointEditGeometryOperations=null,this._undoStack=null,this._redoStack=null,this._initialControlPoints=null,this._sharedUndoStack=null,this._sharedRedoStack=null}get _hasValidSpatialReference(){return d(this.view.spatialReference)}onActivate(){this.visible=!0}onDeactivate(){this.visible=!1}onShow(){this._graphicsLayer.visible=!0}onHide(){this._graphicsLayer.visible=!1}canUndo(){const t=this._undoStack[this._undoStack.length-1];return null!=t&&this._controlPointEditGeometryOperations[t].canUndo}canRedo(){const t=this._redoStack[this._redoStack.length-1];return null!=t&&this._controlPointEditGeometryOperations[t].canRedo}undo(){if(this._undoStack.length>0){const t=this._undoStack.pop();this._controlPointEditGeometryOperations[t].undo(),this.updateGraphics(),this._redoStack.push(t)}}redo(){if(this._redoStack.length>0){const t=this._redoStack.pop();this._controlPointEditGeometryOperations[t].redo(),this.updateGraphics(),this._undoStack.push(t)}}refresh(){const{mediaElement:t}=this;if(s(t.georeference))return;const e=t.georeference;\"control-points\"!==e.type||s(e.coords)||(this._georeference=e,a(this._georeference.controlPoints).forEach((({mapPoint:t},e)=>{const i=this._controlPointEditGeometryOperations[e],o=i.data.components[0].vertices[0];i.setVertexPosition(o,i.data.coordinateHelper.pointToVector(t))})),this.updateGraphics())}reset(){this._georeference.controlPoints=this._initialControlPoints,this.refresh(),this._sharedUndoStack.length=0,this._sharedRedoStack.length=0}updateGraphics(){const t=this._georeference,e=a(t.controlPoints),i=a(e[0].mapPoint).spatialReference,o=this._hasValidSpatialReference;this._georeference.controlPoints=this._controlPointEditGeometryOperations.map(((r,s)=>{const n=r.data.geometry;return this._controlPointGraphics[s].geometry=n,{mapPoint:l(n,i),sourcePoint:o?a(e[s]).sourcePoint:t.toSource(n)}}))}updateActiveHandle(t){if(this.activeHandle===t)return;const e=a(this._controlPointGraphics[this.activeHandle].symbol).clone();y(e,o()),this._controlPointGraphics[this.activeHandle].symbol=e;const i=a(this._controlPointGraphics[t].symbol).clone();y(i,M),this._controlPointGraphics[t].symbol=i,this.activeHandle=t,this.view.surface===document.activeElement&&this.highlightActiveHandle()}async highlightActiveHandle(){this.removeHighlightActiveHandle();const t=await this.view.whenLayerView(this._graphicsLayer);this._highlightHandle=t.highlight(this._controlPointGraphics[this.activeHandle])}removeHighlightActiveHandle(){this._highlightHandle&&this._highlightHandle.remove()}setSharedUndoStack(t){this._sharedUndoStack=t}setSharedRedoStack(t){this._sharedRedoStack=t}async _initialize(){const{view:t,mediaElement:e}=this;if(s(e.georeference))return;const r=e.georeference;\"control-points\"!==r.type||s(r.coords)||(this._georeference=r,this._initialControlPoints=a(this._georeference.controlPoints),t.map.addMany([this._graphicsLayer]),t.focus(),this.visible=!1,this.finishToolCreation(),await this._loadProjectionEngine(),this._controlPointEditGeometryOperations=a(this._georeference.controlPoints).map((({mapPoint:e})=>P.fromGeometry(l(e,t.spatialReference),_.Local))),this._controlPointGraphics=this._controlPointEditGeometryOperations.map(((t,e)=>new i({symbol:new S({data:{type:\"CIMSymbolReference\",symbol:{type:\"CIMPointSymbol\",symbolLayers:[{type:\"CIMVectorMarker\",enable:!0,colorLocked:!0,anchorPoint:{x:0,y:-15.75},anchorPointUnits:\"Absolute\",dominantSizeAxis3D:\"Y\",size:9,billboardMode3D:\"FaceNearPlane\",frame:{xmin:0,ymin:0,xmax:84.3,ymax:84.3},markerGraphics:[{type:\"CIMMarkerGraphic\",geometry:{rings:[[[83.2,32.5],[84.3,40.7],[83.8,48.9],[81.7,56.9],[78.1,64.3],[73,70.9],[66.9,76.4],[59.7,80.5],[51.9,83.2],[43.7,84.3],[35.4,83.8],[27.4,81.7],[20,78],[13.4,73],[7.9,66.8],[3.8,59.7],[1.1,51.9],[0,43.7],[.5,35.4],[2.6,27.4],[6.3,20],[11.3,13.4],[17.5,7.9],[24.7,3.8],[32.5,1.1],[39.8,.1],[47.1,.3],[54.3,1.8],[61.1,4.5],[67.4,8.4],[72.9,13.3],[77.4,19.1],[80.9,25.5],[83.2,32.5]]]},symbol:{type:\"CIMPolygonSymbol\",symbolLayers:[{type:\"CIMSolidFill\",enable:!0,color:[255,255,255,255]}]}}],scaleSymbolsProportionally:!0,respectFrame:!0,clippingPath:{type:\"CIMClippingPath\",clippingType:\"Intersect\",path:{rings:[[[0,0],[84.3,0],[84.3,84.3],[0,84.3],[0,0]]]}},rotation:0},{type:\"CIMVectorMarker\",enable:!0,anchorPoint:{x:0,y:-11.25},anchorPointUnits:\"Absolute\",dominantSizeAxis3D:\"Y\",size:22.5,billboardMode3D:\"FaceNearPlane\",frame:{xmin:0,ymin:0,xmax:197.7,ymax:294.7},markerGraphics:[{type:\"CIMMarkerGraphic\",geometry:{rings:[[[98.9,0],[119.4,23.2],[139.4,49.3],[156.8,75.2],[171.2,100.8],[182.4,125.3],[190.6,148.8],[195.7,171.4],[197.7,192.9],[197.7,195.8],[197.7,200.3],[197.6,202.5],[197.5,204.8],[197.3,207.1],[197,209.4],[196.7,211.7],[196.4,214.1],[196,216.4],[195.5,218.7],[195,221.1],[194.4,223.4],[193.7,225.8],[193,228.1],[192.2,230.5],[191.4,232.8],[190.5,235.1],[189.5,237.5],[188.5,239.7],[187.4,242],[186.2,244.3],[185,246.5],[183.7,248.7],[182.4,250.9],[181,253.1],[179.5,255.2],[178,257.3],[176.4,259.4],[174.7,261.4],[173.1,263.3],[171.3,265.3],[169.5,267.2],[167.7,269],[165.8,270.8],[163.9,272.5],[161.9,274.2],[159.9,275.8],[157.8,277.4],[155.7,278.9],[153.6,280.4],[151.4,281.7],[149.2,283.1],[147,284.4],[144.8,285.6],[142.5,286.7],[140.3,287.8],[138,288.8],[135.7,289.8],[133.4,290.7],[131,291.5],[128.7,292.3],[126.4,293],[124,293.6],[121.7,294.2],[119.4,294.7],[117,295.2],[114.7,295.6],[112.4,296],[110.1,296.3],[107.8,296.5],[105.5,296.7],[103.3,296.8],[101.1,296.9],[98.8,296.9],[83.1,295.7],[67.8,292],[53.3,285.9],[39.9,277.5],[28.1,267.2],[18,255.1],[10,241.5],[4.2,226.9],[.9,211.5],[0,195.8],[.1,192.9],[2.1,171.4],[7.2,148.8],[15.4,125.3],[26.6,100.8],[41,75.2],[58.4,49.3],[78.4,23.2],[98.9,0]]]},symbol:{type:\"CIMPolygonSymbol\",symbolLayers:[{type:\"CIMSolidFill\",enable:!0,color:e===this.activeHandle?M.toArray():o().toArray()}]}}],scaleSymbolsProportionally:!0,respectFrame:!0,clippingPath:{type:\"CIMClippingPath\",clippingType:\"Intersect\",path:{rings:[[[0,0],[197.7,0],[197.7,294.7],[0,294.7],[0,0]]]}},rotation:0}],haloSize:1,scaleX:1,angleAlignment:\"Display\",angle:0}}}),geometry:t.data.geometry}))),this._graphicsLayer.graphics.addMany([...this._controlPointGraphics]),this._controlPointManipulations=this._controlPointGraphics.map((e=>new g({tool:this,view:t,graphic:e}))),this.addHandles([...this._controlPointManipulations.map(((t,e)=>t.createDragPipeline(this._getInfo.bind(this,e),((t,i)=>{\"start\"===t.action&&(this._undoStack.push(e),this._redoStack=[],this._sharedUndoStack.push({tool:this,operation:i}),this._sharedRedoStack.length=0),this.updateGraphics()})))),n((()=>this.view.scale),(()=>this.active?this.updateGraphics():null))]),this._controlPointManipulations.forEach(((t,e)=>{const i=t=>{this.addHandles([t.events.on([\"click\",\"grab-changed\"],(t=>this.updateActiveHandle(e)))])};t.forEachManipulator(i)})),this.addHandles([t.on(\"key-down\",(i=>{t.activeTool===this&&(i.key!==k.shift||i.repeat||(this._isModifierActive=!0,i.stopPropagation()),i.key!==k.toggleOpacity||i.repeat||(e.opacity*=this._isOpacityToggled?2:.5,this._isOpacityToggled=!this._isOpacityToggled,i.stopPropagation()),i.key!==k.primaryKey||i.repeat||(this._factor=G,i.stopPropagation()),this._isModifierActive&&(i.key===k.up&&(this._move(0,this._factor),i.stopPropagation()),i.key===k.down&&(this._move(0,-this._factor),i.stopPropagation()),i.key===k.left&&(this._move(-this._factor,0),i.stopPropagation()),i.key===k.right&&(this._move(this._factor,0),i.stopPropagation())))})),t.on(\"key-up\",(e=>{t.activeTool===this&&(e.key===k.shift&&(this._isModifierActive=!1,e.stopPropagation()),e.key===k.primaryKey&&(this._factor=b,e.stopPropagation()))}))]))}async _loadProjectionEngine(){const t=a(a(this._georeference.controlPoints)[0].mapPoint);return p(t.spatialReference,this.view.spatialReference)}_getInfo(t){return{editGeometryOperations:this._controlPointEditGeometryOperations[t],constraints:this._hasValidSpatialReference?null:{xmin:0,ymin:0,xmax:this._georeference.width,ymax:this._georeference.height}}}_move(t,e){const i=this._controlPointEditGeometryOperations[this.activeHandle],o=[];for(const s of i.data.components)o.push(...s.vertices);const r=i.moveVertices(o,t*this.view.resolution,e*this.view.resolution,0,v.NEW_STEP);this._sharedUndoStack.push({tool:this,operation:r}),this._sharedRedoStack.length=0,this.updateGraphics()}};t([c()],w.prototype,\"_hasValidSpatialReference\",null),t([c()],w.prototype,\"activeHandle\",void 0),t([c({constructOnly:!0,nonNullable:!0})],w.prototype,\"mediaElement\",void 0),t([c({constructOnly:!0})],w.prototype,\"view\",void 0),w=t([h(\"esri.views.2d.interactive.editingTools.ControlPointsTransformTool\")],w);export{w as ControlPointsTransformTool,k as KEYS};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{rad2deg as t,deg2rad as e}from\"../../../../../core/mathUtils.js\";import{isSome as a}from\"../../../../../core/maybe.js\";import{EventPipeline as n}from\"../../../../interactive/dragEventPipeline.js\";function s(t,e){\"start\"===t.action?e.cursor=\"grabbing\":e.cursor=\"grab\"}class r{constructor(){this._lastDragEvent=null,this.next=new n,this._enabled=!1}get enabled(){return this._enabled}set enabled(t){if(this._enabled!==t&&a(this._lastDragEvent)){const e={...this._lastDragEvent,action:\"update\"};t&&this._adjustScaleFactors(e),this.next.execute(e)}this._enabled=t}createDragEventPipelineStep(){return this._lastDragEvent=null,t=>(this._lastDragEvent=\"end\"!==t.action?{...t}:null,this._enabled&&this._adjustScaleFactors(t),t)}_adjustScaleFactors(t){const e=0!==t.direction[0]&&0!==t.direction[1]?Math.max(Math.abs(t.factor1),Math.abs(t.factor2)):0===t.direction[0]?Math.abs(t.factor2):Math.abs(t.factor1);t.factor1=t.factor1<0?-e:e,t.factor2=t.factor2<0?-e:e}}class i{constructor(){this._lastDragEvent=null,this.next=new n,this._enabled=!1}get enabled(){return this._enabled}set enabled(t){if(this._enabled!==t&&a(this._lastDragEvent)){const e={...this._lastDragEvent,action:\"update\"};t&&this._adjustRotateAngle(e),this.next.execute(e)}this._enabled=t}createDragEventPipelineStep(){return this._lastDragEvent=null,t=>(this._lastDragEvent=\"end\"!==t.action?{...t}:null,this._enabled&&this._adjustRotateAngle(t),t)}_adjustRotateAngle(a){const n=t(a.rotateAngle);a.rotateAngle=e(5*Math.round(n/5))}}export{r as PreserveAspectRatio,i as SnapRotation,s as onGrabChangedHandle};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../../core/Handles.js\";import{c as i}from\"../../../../../chunks/vec3.js\";import{c as r}from\"../../../../../chunks/vec3f64.js\";import{Manipulation as e,ManipulatorType as o}from\"./Manipulation.js\";import{onGrabChangedHandle as a}from\"./utils.js\";import{getRotationAngle as n}from\"../../../../draw/support/drawUtils.js\";import{createManipulatorDragEventPipeline as s,screenToMap as l}from\"../../../../interactive/dragEventPipeline.js\";import{GraphicManipulator as p}from\"../../../../interactive/GraphicManipulator.js\";import{AccumulationBehaviour as h}from\"../../../../interactive/editGeometry/interfaces.js\";import{AccumulationType as c}from\"../../../../interactive/editGeometry/operations/UpdateVertices.js\";import{apply as u}from\"../../../../interactive/editGeometry/support/editPlaneUtils.js\";class m extends e{constructor(i){super(),this._handles=new t,this._originCache=r(),this._view=i.view,this._tool=i.tool,this._graphic=i.graphic,this._snapRotation=i.snapRotation,this._manipulator=this._createManipulator(),this._handles.add([this._manipulator.events.on(\"grab-changed\",(t=>a(t,this._manipulator)))]),this.forEachManipulator((t=>this._tool.manipulators.add(t)))}destroy(){this._handles.destroy(),this.forEachManipulator((t=>{this._tool.manipulators.remove(t),t.destroy()})),this._tool=null,this._view=null,this._manipulator=null,this._snapRotation=null,this._graphic=null,this._handles=null,this._originCache=null}forEachManipulator(t){t(this._manipulator,o.ROTATE)}createDragPipeline(t,r){let e=null,o=null;return s(this._manipulator,((a,s)=>{s.next((i=>{if(\"start\"===i.action){a.cursor=\"grabbing\";const i=t();e=i.plane,o=i.editGeometryOperations}return i})).next(l(this._view)).next((t=>({...t,rotateAngle:n(t.mapStart,t.mapEnd,{x:e.origin[0],y:e.origin[1]},!0)}))).next(this._snapRotation.createDragEventPipelineStep(),this._snapRotation.next).next((t=>{const a=i(this._originCache,e.origin),n=[];for(const i of o.data.components)n.push(...i.vertices);const s=\"start\"===t.action?h.NEW_STEP:h.ACCUMULATE_STEPS,l=o.rotateVertices(n,a,t.rotateAngle,s,c.REPLACE);return u(l,e),r(t,l),t})).next((t=>(\"end\"===t.action&&(a.cursor=\"grab\"),t)))}))}_createManipulator(){const t=this._view,i=this._graphic;return new p({view:t,graphic:i,selectable:!0,cursor:\"grab\"})}}export{m as RotateManipulation};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../../core/Handles.js\";import{s as i,f as e}from\"../../../../../chunks/vec2.js\";import{a as s}from\"../../../../../chunks/vec2f64.js\";import{l as r,c as a,z as n,b as o,e as l}from\"../../../../../chunks/vec3.js\";import{c}from\"../../../../../chunks/vec3f64.js\";import{a as h,c as p}from\"../../../../../chunks/boundedPlane.js\";import{getInfo as u}from\"../../../../../geometry/support/spatialReferenceUtils.js\";import{Manipulation as _,ManipulatorType as d}from\"./Manipulation.js\";import{onGrabChangedHandle as m}from\"./utils.js\";import{createManipulatorDragEventPipeline as f,screenToMap as v}from\"../../../../interactive/dragEventPipeline.js\";import{GraphicManipulator as g}from\"../../../../interactive/GraphicManipulator.js\";import{AccumulationBehaviour as C}from\"../../../../interactive/editGeometry/interfaces.js\";import{AccumulationType as b}from\"../../../../interactive/editGeometry/operations/UpdateVertices.js\";import{apply as x}from\"../../../../interactive/editGeometry/support/editPlaneUtils.js\";const E=10,S=1e-6,y=.3;function j(t){const i=r(t.basis1),e=r(t.basis2);return y*Math.min(i,e)}class w extends _{constructor(i){super(),this._handles=new t,this._planeStart=h(),this._displayPlaneStart=h(),this._originCache=c(),this._axisCache=s(),this._renderStartCache=c(),this._renderEndCache=c(),this._resizeOriginCache=c(),this._view=i.view,this._tool=i.tool,this._graphic=i.graphic,this._direction=i.direction,this._preserveAspectRatio=i.preserveAspectRatio,this._manipulator=this._createManipulator(),this._handles.add([this._manipulator.events.on(\"grab-changed\",(t=>m(t,this._manipulator)))]),this.forEachManipulator((t=>this._tool.manipulators.add(t)))}destroy(){this._handles.destroy(),this.forEachManipulator((t=>{this._tool.manipulators.remove(t),t.destroy()})),this._tool=null,this._view=null,this._graphic=null,this._manipulator=null,this._direction=null,this._handles=null,this._planeStart=null,this._displayPlaneStart=null,this._originCache=null,this._axisCache=null,this._renderStartCache=null,this._renderEndCache=null,this._resizeOriginCache=null,this._preserveAspectRatio=null}forEachManipulator(t){t(this._manipulator,d.SCALE)}createDragPipeline(t,s){let c=null,h=null,_=null,d=0,m=null,g=null;const y=this._planeStart,w=this._displayPlaneStart,P=this._direction;return f(this._manipulator,((f,M)=>{M.next((i=>{if(\"start\"===i.action){f.cursor=\"grabbing\";const i=t();c=i.plane,h=i.displayPlane,_=i.editGeometryOperations,d=E*this._view.resolution,p(c,y),p(h,w);const e=u(_.data.spatialReference);m=e?e.valid[1]-e.valid[0]-3*E*this._view.resolution:null}return i})).next(v(this._view)).next((t=>{const i=a(this._renderStartCache,[t.mapStart.x,t.mapStart.y,0]),e=a(this._renderEndCache,[t.mapEnd.x,t.mapEnd.y,0]),s=a(this._resizeOriginCache,w.origin);n(s,s,w.basis1,-P[0]),n(s,s,w.basis2,-P[1]),o(e,e,s),o(i,i,s);const c=0!==P[0]&&0!==P[1],p=j(w),u=j(h)/p,_=(t,s)=>{if(0===t)return 1;let a=r(s),n=.5*t*l(s,e)/a;const o=n<0?-1:1;if(c){n+=(a-.5*t*l(s,i)/a)*o*u}const h=a<1.5*d?1:S;return a=Math.max(a-d,S),o>0&&(n-=E*this._view.resolution),o*Math.max(o*(n/a),h)},m=_(P[0],w.basis1),f=_(P[1],w.basis2);return{...t,direction:P,factor1:m,factor2:f}})).next(this._preserveAspectRatio.createDragEventPipelineStep(),this._preserveAspectRatio.next).next((t=>{const r=a(this._originCache,y.origin);n(r,r,y.basis1,-P[0]),n(r,r,y.basis2,-P[1]);const o=i(this._axisCache,y.basis1[0],y.basis1[1]);e(o,o);const l=[];for(const i of _.data.components)l.push(...i.vertices);const h=\"start\"===t.action?C.NEW_STEP:C.ACCUMULATE_STEPS,u=_.scaleVertices(l,r,o,t.factor1,t.factor2,h,b.REPLACE);return m&&m<_.data.geometry.extent.width&&g?_.updateVertices(l,g):(p(y,c),x(u,c),g=u.operation,s(t,u)),t})).next((t=>(\"end\"===t.action&&(f.cursor=\"grab\"),t)))}))}_createManipulator(){return new g({view:this._view,graphic:this._graphic,selectable:!0,cursor:\"grab\"})}}export{w as ScaleManipulation,j as calculateDiagonalResizeHandleScale};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../../chunks/tslib.es6.js\";import\"../../../../geometry.js\";import e from\"../../../../Graphic.js\";import\"../../../../symbols.js\";import{getAccentColor as i,getContrastColor as s}from\"../../../../core/analysisThemeUtils.js\";import{destroyMaybe as o,isSome as r,unwrap as a}from\"../../../../core/maybe.js\";import{watch as n}from\"../../../../core/reactiveUtils.js\";import{getMetersPerUnitForSR as h}from\"../../../../core/unitUtils.js\";import{property as c}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as p}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{a as l,f as _}from\"../../../../chunks/vec2.js\";import{a as m,U as d}from\"../../../../chunks/vec2f64.js\";import{g as y,l as u,n as g,a as f,h as v,b as G}from\"../../../../chunks/vec3.js\";import{c as w}from\"../../../../chunks/vec3f64.js\";import{project as R,initializeProjection as b}from\"../../../../geometry/projection.js\";import{a as k,c as j}from\"../../../../chunks/boundedPlane.js\";import{equals as P}from\"../../../../geometry/support/spatialReferenceUtils.js\";import A from\"../../../../layers/GraphicsLayer.js\";import{ViewingMode as C}from\"../../../ViewingMode.js\";import{DragManipulation as S}from\"./manipulations/DragManipulation.js\";import{RotateManipulation as M}from\"./manipulations/RotateManipulation.js\";import{ScaleManipulation as T}from\"./manipulations/ScaleManipulation.js\";import{PreserveAspectRatio as O,SnapRotation as U}from\"./manipulations/utils.js\";import{primaryKey as D}from\"../../../input/keys.js\";import{InteractiveToolBase as x}from\"../../../interactive/InteractiveToolBase.js\";import{EditGeometry as E}from\"../../../interactive/editGeometry/EditGeometry.js\";import{EditGeometryOperations as L}from\"../../../interactive/editGeometry/EditGeometryOperations.js\";import{AccumulationBehaviour as B}from\"../../../interactive/editGeometry/interfaces.js\";import{AccumulationType as H}from\"../../../interactive/editGeometry/operations/UpdateVertices.js\";import{calculateOrientedBounds as V}from\"../../../interactive/editGeometry/support/editPlaneUtils.js\";import{findFirstGraphicHit as I}from\"../../../support/hitTestSelectUtils.js\";import{createScreenPointFromEvent as z}from\"../../../support/screenUtils.js\";import K from\"../../../../geometry/Polygon.js\";import N from\"../../../../geometry/Point.js\";import W from\"../../../../symbols/SimpleFillSymbol.js\";import q from\"../../../../symbols/SimpleMarkerSymbol.js\";const F={up:\"ArrowUp\",down:\"ArrowDown\",left:\"ArrowLeft\",right:\"ArrowRight\",plus:\"+\",minus:\"-\",toggleOpacity:\"t\",shift:\"Shift\",primaryKey:D},J=80,Q=10,X=30,Y=[[1,1],[1,-1],[-1,-1],[-1,1],[1,0],[0,-1],[-1,0],[0,1]],Z=1,$=10;let tt=class extends x{constructor(t){super(t),this._initialControlPoints=null,this._initialGeometry=null,this._graphic=null,this._planeCache=k(),this._displayPlaneCache=k(),this._mainAxisCache=m(),this._rotationHandleCache=w(),this._cornerA=w(),this._cornerB=w(),this._cornerC=w(),this._cornerD=w(),this._avgAB=w(),this._avgBC=w(),this._avgCD=w(),this._avgDA=w(),this._preserveAspectRatio=new O,this._snapRotation=new U,this._graphicsLayer=new A({internal:!0,listMode:\"hide\",visible:!1}),this._sharedUndoStack=[],this._sharedRedoStack=[],this._isOpacityToggled=!1,this._isModifierActive=!1,this._factor=1,this.preserveAspectRatio=null,this.snapRotation=null}initialize(){this._initialize()}destroy(){const{map:t}=this.view;this._dragManipulation.destroy(),this._rotateManipulation.destroy(),this._scaleManipulations.forEach((t=>t.destroy())),this._editGeometryOperations.destroy(),t.removeMany([this._graphicsLayer]),this._graphicsLayer.removeAll(),this._graphicsLayer=o(this._graphicsLayer),this._initialControlPoints=null,this._initialGeometry=null,this._graphic=null,this._preserveAspectRatio=null,this._snapRotation=null,this._planeCache=null,this._displayPlaneCache=null,this._rotationHandleCache=null,this._mainAxisCache=null,this._cornerA=null,this._cornerB=null,this._cornerC=null,this._cornerD=null,this._avgAB=null,this._avgBC=null,this._avgCD=null,this._avgDA=null,this._sharedUndoStack=null,this._sharedRedoStack=null}get _plane(){const t=this._graphic.geometry;if(!r(t))return null;const e=this._editGeometryOperations.data,i=e.components[0].edges[0],s=l(this._mainAxisCache,i.leftVertex.pos,i.rightVertex.pos);_(s,s);let o=J*this.view.resolution;const a=this.view.spatialReference;return P(a,t.spatialReference)&&(o*=h(a)/h(t.spatialReference)),V(s,e,o,this._planeCache)}get _displayPlane(){const t=this._plane;if(!t)return null;const e=this._displayPlaneCache;j(t,e);const i=Q*this.view.resolution;return y(e.basis1,e.basis1,1+i/u(e.basis1)),y(e.basis2,e.basis2,1+i/u(e.basis2)),e}get _backgroundGraphicGeometry(){const t=this._displayPlane;if(!t)return null;const e=this.view.spatialReference;return this._updateDisplayPlaneConrers(t),new K({spatialReference:e,rings:[[this._cornerA,this._cornerB,this._cornerC,this._cornerD,this._cornerA]]})}get _rotateGraphicGeometry(){const t=this._plane;if(!t)return null;const e=this._rotationHandleCache;return g(e,t.basis1),y(e,e,X*this.view.resolution),f(e,e,t.origin),f(e,e,t.basis1),new N({x:e[0],y:e[1],spatialReference:this.view.spatialReference})}get _scaleGraphicGeometries(){const t=this._displayPlane;if(!t)return[];const e=this.view.spatialReference;this._updateDisplayPlaneConrers(t);const{_cornerA:i,_cornerB:s,_cornerC:o,_cornerD:r}=this,a=v(this._avgAB,i,s,.5),n=v(this._avgBC,s,o,.5),h=v(this._avgCD,o,r,.5),c=v(this._avgDA,r,i,.5);return[new N({x:i[0],y:i[1],spatialReference:e}),new N({x:s[0],y:s[1],spatialReference:e}),new N({x:o[0],y:o[1],spatialReference:e}),new N({x:r[0],y:r[1],spatialReference:e}),new N({x:a[0],y:a[1],spatialReference:e}),new N({x:n[0],y:n[1],spatialReference:e}),new N({x:h[0],y:h[1],spatialReference:e}),new N({x:c[0],y:c[1],spatialReference:e})]}onActivate(){this.visible=!0}onDeactivate(){this.visible=!1}onShow(){this._graphicsLayer.visible=!0}onHide(){this._graphicsLayer.visible=!1}canUndo(){return this._editGeometryOperations.canUndo}canRedo(){return this._editGeometryOperations.canRedo}undo(){this._editGeometryOperations.undo(),this.updateGraphics()}redo(){this._editGeometryOperations.redo(),this.updateGraphics()}refresh(){const{view:t,target:e}=this,i=\"georeference\"in e?a(a(e.georeference).coords):e.geometry,s=this._editGeometryOperations,o=s.data.components[0].vertices,r=E.fromGeometry(R(i,t.spatialReference),C.Local).components[0].vertices;o.forEach(((t,e)=>{s.setVertexPosition(t,r[e].pos)})),this.updateGraphics()}reset(){const{target:t}=this;if(\"georeference\"in t){const e=a(t.georeference);\"control-points\"===e.type&&(e.controlPoints=this._initialControlPoints)}else t.geometry=this._initialGeometry;this.refresh(),this._sharedUndoStack.length=0,this._sharedRedoStack.length=0}updateGraphics(){const t=this._editGeometryOperations.data.geometry;if(\"georeference\"in this.target){a(this.target.georeference).coords=t}this._graphic.geometry=t,this._backgroundGraphic.geometry=this._backgroundGraphicGeometry,this._rotateGraphic.geometry=this._rotateGraphicGeometry,this._scaleGraphicGeometries.forEach(((t,e)=>{this._scaleGraphics[e].geometry=t}))}setSharedUndoStack(t){this._sharedUndoStack=t}setSharedRedoStack(t){this._sharedRedoStack=t}async _initialize(){const{view:t,target:o}=this;if(\"georeference\"in o){const t=a(o.georeference);this._graphic=new e({geometry:a(t.coords)}),this._initialControlPoints=\"control-points\"===t.type?t.controlPoints:null}else this._graphic=o,this._initialGeometry=a(o.geometry);t.map.addMany([this._graphicsLayer]),t.focus(),this.visible=!1,this.finishToolCreation(),await this._loadProjectionEngine(),this._editGeometryOperations=L.fromGeometry(R(this._graphic.geometry,t.spatialReference),C.Local),this._backgroundGraphic=new e({symbol:new W({color:\"transparent\",outline:{type:\"simple-line\",color:i(),width:2}}),geometry:this._backgroundGraphicGeometry}),this._rotateGraphic=new e({symbol:new q({color:s(),outline:{type:\"simple-line\",color:i(),width:1}}),geometry:this._rotateGraphicGeometry}),this._scaleGraphics=this._scaleGraphicGeometries.map((t=>new e({symbol:new q({size:6,style:\"square\",color:s(),outline:{type:\"simple-line\",color:i(),width:1}}),geometry:t}))),this._graphicsLayer.graphics.addMany([this._backgroundGraphic,this._rotateGraphic,...this._scaleGraphics]),this._dragManipulation=new S({tool:this,view:t,graphic:this._graphic}),this._rotateManipulation=new M({tool:this,view:t,graphic:this._rotateGraphic,snapRotation:this._snapRotation}),this._scaleManipulations=this._scaleGraphics.map(((e,i)=>new T({tool:this,view:t,graphic:e,direction:Y[i],preserveAspectRatio:this._preserveAspectRatio}))),this.addHandles([this._dragManipulation.createDragPipeline(this._getInfo.bind(this),this._updateGraphics.bind(this)),this._rotateManipulation.createDragPipeline(this._getInfo.bind(this),this._updateGraphics.bind(this)),...this._scaleManipulations.map((t=>t.createDragPipeline(this._getInfo.bind(this),this._updateGraphics.bind(this)))),n((()=>this.view.scale),(()=>this.active?this.updateGraphics():null)),t.on(\"click\",(async e=>{if(null!=t.activeTool&&t.activeTool!==this)return;const i=z(e),s=[];t.map.allLayers.forEach((t=>{\"vector-tile\"!==t.type&&\"imagery\"!==t.type||s.push(t)}));const a=await this.view.hitTest(i,{exclude:s}),n=a.results;if(0===n.length)t.activeTool=null;else{const e=I(a.results),i=\"georeference\"in o,s=n.map((t=>\"media\"===t.type?t.element:null)).filter(Boolean),h=[...this._graphicsLayer.graphics,i?null:o].filter(Boolean);i&&s.includes(o)||r(e)&&h.includes(e.graphic)?null==t.activeTool&&(t.activeTool=this):t.activeTool=null}}))]);const h=t=>{this.addHandles(t.events.on(\"grab-changed\",(t=>{\"georeference\"in o&&(\"start\"===t.action?o.opacity*=.5:\"end\"===t.action&&(o.opacity*=2))})))};this._dragManipulation.forEachManipulator(h),this._rotateManipulation.forEachManipulator(h),this._scaleManipulations.forEach((t=>t.forEachManipulator(h))),this.addHandles([t.on(\"key-down\",(e=>{t.activeTool===this&&(e.key!==F.shift||e.repeat||(null==this.preserveAspectRatio&&(this._preserveAspectRatio.enabled=!this._preserveAspectRatio.enabled),null==this.snapRotation&&(this._snapRotation.enabled=!this._snapRotation.enabled),this._isModifierActive=!0,e.stopPropagation()),e.key!==F.toggleOpacity||e.repeat||(\"georeference\"in o&&(o.opacity*=this._isOpacityToggled?2:.5,this._isOpacityToggled=!this._isOpacityToggled),e.stopPropagation()),e.key!==F.primaryKey||e.repeat||(this._factor=$,e.stopPropagation()),this._isModifierActive&&(e.key===F.plus&&(this._scale(this._factor),e.stopPropagation()),e.key===F.minus&&(this._scale(-this._factor),e.stopPropagation()),e.key===F.up&&(this._move(0,this._factor),e.stopPropagation()),e.key===F.down&&(this._move(0,-this._factor),e.stopPropagation()),e.key===F.left&&(this._move(-this._factor,0),e.stopPropagation()),e.key===F.right&&(this._move(this._factor,0),e.stopPropagation())))})),t.on(\"key-up\",(e=>{t.activeTool===this&&(e.key===F.shift&&(null==this.preserveAspectRatio&&(this._preserveAspectRatio.enabled=!this._preserveAspectRatio.enabled),null==this.snapRotation&&(this._snapRotation.enabled=!this._snapRotation.enabled),this._isModifierActive=!1,e.stopPropagation()),e.key===F.primaryKey&&(this._factor=Z,e.stopPropagation()))}))])}async _loadProjectionEngine(){const t=a(this._graphic.geometry);return b(t.spatialReference,this.view.spatialReference)}_updateDisplayPlaneConrers(t){const{basis1:e,basis2:i,origin:s}=t,o=this._cornerA;f(o,s,e),f(o,o,i);const r=this._cornerB;f(r,s,e),G(r,r,i);const a=this._cornerC;G(a,s,e),G(a,a,i);const n=this._cornerD;G(n,s,e),f(n,n,i)}_getInfo(){return{editGeometryOperations:this._editGeometryOperations,plane:this._plane,displayPlane:this._displayPlane}}_updateGraphics(t,e){\"start\"===t.action&&(this._sharedUndoStack.push({tool:this,operation:e}),this._sharedRedoStack.length=0),this.updateGraphics()}_scale(t){const e=this._editGeometryOperations,i=[];for(const a of e.data.components)i.push(...a.vertices);const s=e.data.geometry.extent?.width,o=(s+t*this.view.resolution)/s,r=e.scaleVertices(i,this._plane.origin,d,o,o,B.NEW_STEP,H.REPLACE);this._sharedUndoStack.push({tool:this,operation:r}),this._sharedRedoStack.length=0,this.updateGraphics()}_move(t,e){const i=this._editGeometryOperations,s=[];for(const r of i.data.components)s.push(...r.vertices);const o=i.moveVertices(s,t*this.view.resolution,e*this.view.resolution,0,B.NEW_STEP);this._sharedUndoStack.push({tool:this,operation:o}),this._sharedRedoStack.length=0,this.updateGraphics()}};t([c()],tt.prototype,\"_plane\",null),t([c()],tt.prototype,\"_backgroundGraphicGeometry\",null),t([c()],tt.prototype,\"_rotateGraphicGeometry\",null),t([c()],tt.prototype,\"_scaleGraphicGeometries\",null),t([c()],tt.prototype,\"preserveAspectRatio\",void 0),t([c()],tt.prototype,\"snapRotation\",void 0),t([c({constructOnly:!0,nonNullable:!0})],tt.prototype,\"target\",void 0),t([c({constructOnly:!0})],tt.prototype,\"view\",void 0),tt=t([p(\"esri.views.2d.interactive.editingTools.TransformTool\")],tt);export{F as KEYS,tt as TransformTool};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../../chunks/tslib.es6.js\";import t from\"../../../../core/Accessor.js\";import{unwrap as e}from\"../../../../core/maybe.js\";import{watch as s}from\"../../../../core/reactiveUtils.js\";import{property as i}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as a}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{ControlPointsTransformTool as r}from\"./ControlPointsTransformTool.js\";import{TransformTool as n}from\"./TransformTool.js\";const d={redo:\"r\",undo:\"z\"};let l=class extends t{constructor(o){super(o),this._transformTool=null,this._controlPointsTransformTool=null,this._advancedModeTransformTool=null,this._activeTool=null,this._sharedUndoStack=[],this._sharedRedoStack=[],this._originalOpacity=null,this.activeHandle=0}initialize(){const{view:o,mediaElement:t,preserveAspectRatio:i,snapRotation:a,advancedMode:l}=this;this._originalOpacity=t.opacity,this._transformTool=new n({target:t,view:o,preserveAspectRatio:i,snapRotation:a}),this._controlPointsTransformTool=new r({mediaElement:t,view:o}),this._advancedModeTransformTool=new r({mediaElement:l.mediaElement,view:l.view}),this._transformTool.setSharedUndoStack(this._sharedUndoStack),this._transformTool.setSharedRedoStack(this._sharedRedoStack),this._controlPointsTransformTool.setSharedUndoStack(this._sharedUndoStack),this._controlPointsTransformTool.setSharedRedoStack(this._sharedRedoStack),this._advancedModeTransformTool.setSharedUndoStack(this._sharedUndoStack),this._advancedModeTransformTool.setSharedRedoStack(this._sharedRedoStack);const c=e(t.georeference),h=e(l.mediaElement.georeference);l.view.tools.addMany([this._advancedModeTransformTool]),\"controlPoints\"in h&&\"controlPoints\"in c&&this.addHandles([l.view.on(\"key-down\",(o=>{o.key===d.undo&&this.canUndo()&&(this.undo(),o.stopPropagation()),o.key===d.redo&&this.canRedo()&&(this.redo(),o.stopPropagation())})),l.view.on(\"focus\",(async o=>{this._controlPointsTransformTool.removeHighlightActiveHandle(),this._advancedModeTransformTool.highlightActiveHandle()})),s((()=>h.controlPoints),(o=>{c.controlPoints=e(o).map((({sourcePoint:o},t)=>({sourcePoint:o,mapPoint:e(c.controlPoints)[t].mapPoint}))),this._activeTool?.refresh()})),s((()=>this._controlPointsTransformTool.activeHandle),(o=>{this._advancedModeTransformTool.updateActiveHandle(o),this.activeHandle=o})),s((()=>this._advancedModeTransformTool.activeHandle),(o=>{this._controlPointsTransformTool.updateActiveHandle(o),this.activeHandle=o}))]),this.addHandles([o.on(\"key-down\",(o=>{o.key===d.undo&&this.canUndo()&&(this.undo(),o.stopPropagation()),o.key===d.redo&&this.canRedo()&&(this.redo(),o.stopPropagation())})),o.on(\"focus\",(async o=>{this._advancedModeTransformTool.removeHighlightActiveHandle(),this._controlPointsTransformTool.highlightActiveHandle()}))]),o.tools.addMany([this._transformTool,this._controlPointsTransformTool]),o.activeTool=this._transformTool,this._activeTool=this._transformTool,o.focus()}destroy(){this._transformTool?.destroy(),this._controlPointsTransformTool?.destroy(),this._transformTool=null,this._controlPointsTransformTool=null,this._advancedModeTransformTool=null,this._activeTool=null,this._sharedUndoStack=null,this._sharedRedoStack=null}canUndo(){return this._sharedUndoStack.length>0}canRedo(){return this._sharedRedoStack.length>0}undo(){if(this._sharedUndoStack.length>0){const{tool:o,operation:t}=this._sharedUndoStack.pop();o!==this._activeTool&&o.refresh(),t.undo(),o.updateGraphics(),this._sharedRedoStack.push({tool:o,operation:t}),this._activeTool!==o&&this._activeTool?.refresh()}}redo(){if(this._sharedRedoStack.length>0){const{tool:o,operation:t}=this._sharedRedoStack.pop();o!==this._activeTool&&o.refresh(),t.apply(),o.updateGraphics(),this._sharedUndoStack.push({tool:o,operation:t}),this._activeTool!==o&&this._activeTool?.refresh()}}refresh(){this._activeTool.refresh()}reset(){this._activeTool.reset(),this._advancedModeTransformTool.reset()}async enableAdvancedMode(){this.view.activeTool=this._controlPointsTransformTool,this._activeTool=this._controlPointsTransformTool,this._activeTool.refresh(),await this.advancedMode.view.when(),this.advancedMode.view.activeTool=this._advancedModeTransformTool,this._originalOpacity=this._controlPointsTransformTool.mediaElement.opacity,this._controlPointsTransformTool.mediaElement.opacity=.25*this._originalOpacity}disableAdvancedMode(){this.view.activeTool=this._transformTool,this._activeTool=this._transformTool,this._activeTool.refresh(),this.advancedMode.view.activeTool=null,this._controlPointsTransformTool.mediaElement.opacity=this._originalOpacity}};o([i()],l.prototype,\"activeHandle\",void 0),o([i({constructOnly:!0})],l.prototype,\"advancedMode\",void 0),o([i()],l.prototype,\"preserveAspectRatio\",void 0),o([i()],l.prototype,\"snapRotation\",void 0),o([i({constructOnly:!0,nonNullable:!0})],l.prototype,\"mediaElement\",void 0),o([i({constructOnly:!0})],l.prototype,\"view\",void 0),l=o([a(\"esri.views.2d.interactive.editingTools.MediaTransformToolsWrapper\")],l);export{d as KEYS,l as MediaTransformToolsWrapper};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIiJ,IAAMA,KAAE,IAAI,EAAE,EAAC,MAAK,EAAC,MAAK,sBAAqB,QAAO,EAAC,MAAK,iBAAgB,cAAa,CAAC,EAAC,MAAK,kBAAiB,SAAQ,CAAC,EAAC,MAAK,4BAA2B,cAAa,CAAC,MAAK,IAAI,GAAE,gBAAe,eAAc,oBAAmB,eAAc,CAAC,GAAE,QAAO,MAAG,UAAS,QAAO,WAAU,SAAQ,YAAW,IAAG,OAAM,KAAI,OAAM,CAAC,KAAI,KAAI,KAAI,GAAG,EAAC,GAAE,EAAC,MAAK,kBAAiB,QAAO,MAAG,UAAS,QAAO,WAAU,SAAQ,YAAW,IAAG,OAAM,GAAE,OAAM,CAAC,GAAE,GAAE,GAAE,GAAG,EAAC,CAAC,EAAC,EAAC,EAAC,CAAC;AAAjc,IAAmcC,KAAE,IAAIC,GAAE,EAAC,OAAM,UAAS,MAAK,GAAE,OAAM,CAAC,KAAI,KAAI,KAAI,CAAC,GAAE,SAAQ,EAAC,OAAM,CAAC,IAAG,IAAG,EAAE,GAAE,OAAM,EAAC,EAAC,CAAC;AAA3hB,IAA6hBC,KAAE,IAAID,GAAE,EAAC,OAAM,UAAS,MAAK,GAAE,OAAM,CAAC,KAAI,KAAI,GAAE,CAAC,GAAE,SAAQ,EAAC,OAAM,CAAC,IAAG,IAAG,EAAE,GAAE,OAAM,EAAC,EAAC,CAAC;;;ACAgS,IAAIE,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,yBAAuB,EAAC,SAAQ,MAAK,iBAAgB,MAAK,cAAa,KAAI,GAAE,KAAK,mBAAiB,MAAK,KAAK,OAAK,WAAU,KAAK,wBAAsB,EAAC,SAAQ,EAAEA,GAAE,kBAAiBC,EAAC,GAAE,iBAAgB,EAAED,GAAE,uBAAsBE,EAAC,GAAE,cAAa,EAAEF,GAAE,oBAAmBG,EAAC,GAAE,MAAKH,GAAEA,GAAE,gBAAgB,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,UAAMC,KAAE,EAAC,GAAGD,GAAC;AAAE,WAAO,OAAOC,GAAE,kBAAiB,OAAOA,GAAE,oBAAmB,OAAOA,GAAE,uBAAsB,OAAOA,GAAE,kBAAiBA;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAE;AAAC,WAAO,EAAE,KAAK,sBAAsB,IAAI,MAAIA,GAAE,SAAO,KAAK,sBAAsB,OAAM;AAAA,EAAI;AAAA,EAAC,oBAAmB;AAAC,UAAK,EAAC,UAASA,IAAE,MAAKC,IAAE,MAAKE,GAAC,IAAE;AAAK,WAAO,IAAI,EAAE,EAAC,MAAKA,IAAE,cAAa,KAAK,cAAa,cAAa,EAAE,KAAK,YAAY,GAAE,aAAY,KAAK,MAAK,MAAKF,IAAE,UAASD,IAAE,oBAAmB,KAAK,aAAY,aAAY,IAAII,GAAED,IAAEF,IAAED,EAAC,GAAE,MAAK,OAAG,iBAAgB,KAAK,iBAAgB,oBAAmB,IAAIK,GAAE,KAAK,qBAAqB,GAAE,gBAAe,KAAK,eAAc,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBL,IAAE;AAAC,QAAG,YAAU,KAAK,aAAa,QAAO;AAAK,UAAK,CAACM,IAAEC,EAAC,IAAEP,IAAEQ,KAAE,IAAI,EAAE,EAAC,GAAEF,IAAE,GAAEC,IAAE,kBAAiB,KAAK,KAAK,iBAAgB,CAAC;AAAE,WAAO,EAAE,KAAK,uBAAuB,YAAY,KAAG,KAAK,uBAAuB,aAAa,WAASC,IAAE,SAAO,KAAK,uBAAuB,eAAa,IAAIC,GAAE,EAAC,UAASD,IAAE,QAAO,KAAK,sBAAsB,cAAa,YAAW,EAAC,cAAa,EAAC,EAAC,CAAC,GAAE,KAAK,sBAAsB,IAAI,KAAK,uBAAuB,YAAY,GAAE,KAAK,sBAAsB,SAAS,KAAK,CAAC,GAAE,EAAG,MAAI;AAAC,QAAE,KAAK,uBAAuB,YAAY,MAAI,KAAK,sBAAsB,OAAO,KAAK,uBAAuB,YAAY,GAAE,KAAK,uBAAuB,eAAa,EAAE,KAAK,uBAAuB,YAAY;AAAA,IAAE,CAAE;AAAA,EAAE;AAAA,EAAC,iBAAiBR,IAAE;AAAC,UAAMM,KAAEN,GAAE,MAAM;AAAE,QAAG,eAAaM,GAAE,MAAK;AAAC,YAAMN,KAAEM,GAAE,MAAMA,GAAE,MAAM,SAAO,CAAC;AAAE,MAAAN,GAAE,OAAO,GAAEA,GAAE,SAAO,CAAC;AAAA,IAAC;AAAC,WAAO,EAAE,KAAK,uBAAuB,OAAO,KAAG,KAAK,uBAAuB,QAAQ,WAASM,IAAE,SAAO,KAAK,uBAAuB,UAAQ,IAAIG,GAAE,EAAC,UAASH,IAAE,QAAO,KAAK,sBAAsB,SAAQ,YAAW,EAAC,cAAa,EAAC,EAAC,CAAC,GAAE,KAAK,sBAAsB,IAAI,KAAK,uBAAuB,OAAO,GAAE,KAAK,sBAAsB,SAAS,KAAK,CAAC,GAAE,EAAG,MAAI;AAAC,QAAE,KAAK,uBAAuB,OAAO,MAAI,KAAK,sBAAsB,OAAO,KAAK,uBAAuB,OAAO,GAAE,KAAK,uBAAuB,UAAQ,EAAE,KAAK,uBAAuB,OAAO;AAAA,IAAE,CAAE;AAAA,EAAE;AAAA,EAAC,yBAAyBN,IAAE;AAAC,UAAMM,KAAE,IAAI,EAAE,EAAC,QAAON,IAAE,kBAAiB,KAAK,KAAK,iBAAgB,CAAC;AAAE,WAAO,EAAE,KAAK,uBAAuB,eAAe,KAAG,KAAK,uBAAuB,gBAAgB,WAASM,IAAE,SAAO,KAAK,uBAAuB,kBAAgB,IAAIG,GAAE,EAAC,UAASH,IAAE,QAAO,KAAK,sBAAsB,iBAAgB,YAAW,EAAC,cAAa,EAAC,EAAC,CAAC,GAAE,KAAK,sBAAsB,IAAI,KAAK,uBAAuB,eAAe,GAAE,KAAK,sBAAsB,SAAS,KAAK,CAAC,GAAE,EAAG,MAAI;AAAC,QAAE,KAAK,uBAAuB,eAAe,MAAI,KAAK,sBAAsB,OAAO,KAAK,uBAAuB,eAAe,GAAE,KAAK,uBAAuB,kBAAgB,EAAE,KAAK,uBAAuB,eAAe;AAAA,IAAE,CAAE;AAAA,EAAE;AAAC;AAAE,SAAS,EAAEN,IAAEC,IAAE;AAJtkI;AAIukI,YAAO,KAAAD,GAAE,eAAF,mBAAc,iBAAc,KAAG,QAAI,KAAAC,GAAE,eAAF,mBAAc,iBAAc,KAAG;AAAE;AAAC,EAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,MAAG,aAAY,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAACK,GAAE,kDAAkD,CAAC,GAAEL,EAAC;;;ACAzvI,SAASW,GAAEC,IAAED,IAAE;AAAC,QAAME,KAAED,GAAE,IAAED;AAAE,SAAOG,GAAEF,EAAC,IAAE,MAAI,IAAIG,GAAE,CAAC,GAAE,GAAE,GAAEF,EAAC,CAAC,IAAE,IAAIE,GAAE,CAAC,KAAI,KAAI,KAAIF,EAAC,CAAC;AAAC;AAAC,SAASA,GAAED,IAAEI,IAAE;AAAC,QAAML,KAAE,IAAII,GAAEH,EAAC;AAAE,SAAOD,GAAE,KAAGK,IAAEL;AAAC;AAAC,SAASM,GAAEC,KAAE,GAAE;AAAC,SAAOL,GAAE,EAAE,cAAc,aAAYK,EAAC;AAAC;AAAC,SAASJ,GAAEI,KAAE,GAAE;AAAC,SAAOP,GAAEM,GAAE,GAAEC,EAAC;AAAC;;;ACAxV,IAAMC,KAAN,MAAO;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,gBAAiB,CAAAA,OAAGA,GAAE,QAAS;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,gBAAiB,CAAAA,OAAGA,GAAE,QAAS;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,gBAAiB,CAAAA,OAAGA,GAAE,QAAS;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,WAAO,KAAK,gBAAiB,CAAAC,OAAGA,OAAID,EAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,QAAIC,KAAE;AAAG,WAAO,KAAK,mBAAoB,CAAAC,OAAG;AAAC,OAACD,MAAGD,GAAEE,EAAC,MAAID,KAAE;AAAA,IAAG,CAAE,GAAEA;AAAA,EAAC;AAAC;AAAC,IAAIA;AAAE,CAAC,SAASD,IAAE;AAAC,EAAAA,GAAEA,GAAE,eAAa,CAAC,IAAE,gBAAeA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,SAAO,CAAC,IAAE;AAAQ,EAAEC,OAAIA,KAAE,CAAC,EAAE;;;ACA6C,IAAM,IAAN,cAAgBE,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,GAAE,KAAK,QAAMA,GAAE,MAAK,KAAK,QAAMA,GAAE,MAAK,KAAK,WAASA,GAAE,SAAQ,KAAK,eAAa,KAAK,mBAAmB,GAAE,KAAK,mBAAoB,CAAAA,OAAG,KAAK,MAAM,aAAa,IAAIA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,mBAAoB,CAAAA,OAAG;AAAC,WAAK,MAAM,aAAa,OAAOA,EAAC,GAAEA,GAAE,QAAQ;AAAA,IAAC,CAAE,GAAE,KAAK,QAAM,MAAK,KAAK,QAAM,MAAK,KAAK,eAAa,MAAK,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,mBAAmBA,IAAE;AAAC,IAAAA,GAAE,KAAK,cAAaA,GAAE,YAAY;AAAA,EAAC;AAAA,EAAC,mBAAmBC,IAAEF,IAAE;AAAC,QAAIG,KAAE,MAAKC,KAAE,MAAKC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,UAAK,EAAC,SAAQC,IAAE,SAAQC,IAAE,MAAKC,GAAC,IAAET,GAAEE,GAAE,KAAK,SAAS,MAAM,CAAC;AAAE,WAAO,EAAE,KAAK,cAAc,CAACF,IAAEU,OAAI;AAAC,MAAAA,GAAE,KAAM,CAAAV,OAAG;AAAC,YAAG,YAAUA,GAAE,QAAO;AAAC,gBAAMA,KAAEC,GAAE;AAAE,UAAAC,KAAEF,GAAE,wBAAuBG,KAAEH,GAAE;AAAA,QAAW;AAAC,eAAOA;AAAA,MAAC,CAAE,EAAE,KAAKW,GAAE,KAAK,KAAK,CAAC,EAAE,KAAM,CAAAX,OAAG;AAAC,cAAK,EAAC,GAAEC,IAAE,GAAES,IAAE,GAAEE,GAAC,IAAEZ,GAAE;AAAO,YAAGG,OAAIF,KAAEM,KAAEJ,GAAE,QAAMO,KAAEF,KAAEC,KAAEN,GAAE,QAAMF,KAAEM,KAAEJ,GAAE,QAAMO,KAAEF,KAAEC,KAAEN,GAAE,MAAM,QAAOH;AAAE,oBAAUA,GAAE,WAASI,KAAEJ,GAAE,SAAS,GAAEK,KAAEL,GAAE,SAAS,GAAEM,KAAEN,GAAE,SAAS;AAAG,cAAMa,KAAEZ,KAAEG,IAAE,IAAEM,KAAEL,IAAEM,KAAEC,KAAEN;AAAE,QAAAF,KAAEH,IAAEI,KAAEK,IAAEJ,KAAEM;AAAE,cAAME,KAAE,CAAC;AAAE,mBAAUf,MAAKG,GAAE,KAAK,WAAW,CAAAY,GAAE,KAAK,GAAGf,GAAE,QAAQ;AAAE,cAAMgB,KAAE,YAAUf,GAAE,SAAOgB,GAAE,WAASA,GAAE,kBAAiBC,KAAEf,GAAE,aAAaY,IAAED,IAAE,GAAEF,IAAEI,EAAC;AAAE,eAAOhB,GAAEC,IAAEiB,EAAC,GAAEjB;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMA,KAAE,KAAK,OAAMC,KAAE,KAAK;AAAS,WAAO,IAAI,EAAE,EAAC,MAAKD,IAAE,SAAQC,IAAE,YAAW,MAAG,QAAO,OAAM,CAAC;AAAA,EAAC;AAAC;;;ACAlQ,IAAMiB,KAAE,EAAC,IAAG,WAAU,MAAK,aAAY,MAAK,aAAY,OAAM,cAAa,eAAc,KAAI,OAAM,SAAQ,YAAWC,GAAC;AAAvH,IAAyHC,KAAE;AAA3H,IAA6H,IAAE;AAA/H,IAAkI,IAAE,IAAIC,GAAE,SAAS;AAAE,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,oBAAkB,OAAG,KAAK,oBAAkB,OAAG,KAAK,UAAQ,GAAE,KAAK,wBAAsB,MAAK,KAAK,iBAAe,IAAI,EAAE,EAAC,UAAS,MAAG,UAAS,QAAO,SAAQ,OAAG,QAAO,6BAA4B,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,mBAAiB,MAAK,KAAK,eAAa;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,YAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAK,EAAC,KAAIA,GAAC,IAAE,KAAK;AAAK,SAAK,2BAA2B,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,oCAAoC,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAEA,GAAE,WAAW,CAAC,KAAK,cAAc,CAAC,GAAE,KAAK,eAAe,UAAU,GAAE,KAAK,iBAAe,EAAE,KAAK,cAAc,GAAE,KAAK,gBAAc,MAAK,KAAK,wBAAsB,MAAK,KAAK,6BAA2B,MAAK,KAAK,iBAAe,MAAK,KAAK,sCAAoC,MAAK,KAAK,aAAW,MAAK,KAAK,aAAW,MAAK,KAAK,wBAAsB,MAAK,KAAK,mBAAiB,MAAK,KAAK,mBAAiB;AAAA,EAAI;AAAA,EAAC,IAAI,4BAA2B;AAAC,WAAO,EAAE,KAAK,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,eAAc;AAAC,SAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,SAAK,eAAe,UAAQ;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,SAAK,eAAe,UAAQ;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,UAAMA,KAAE,KAAK,WAAW,KAAK,WAAW,SAAO,CAAC;AAAE,WAAO,QAAMA,MAAG,KAAK,oCAAoCA,EAAC,EAAE;AAAA,EAAO;AAAA,EAAC,UAAS;AAAC,UAAMA,KAAE,KAAK,WAAW,KAAK,WAAW,SAAO,CAAC;AAAE,WAAO,QAAMA,MAAG,KAAK,oCAAoCA,EAAC,EAAE;AAAA,EAAO;AAAA,EAAC,OAAM;AAAC,QAAG,KAAK,WAAW,SAAO,GAAE;AAAC,YAAMA,KAAE,KAAK,WAAW,IAAI;AAAE,WAAK,oCAAoCA,EAAC,EAAE,KAAK,GAAE,KAAK,eAAe,GAAE,KAAK,WAAW,KAAKA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAG,KAAK,WAAW,SAAO,GAAE;AAAC,YAAMA,KAAE,KAAK,WAAW,IAAI;AAAE,WAAK,oCAAoCA,EAAC,EAAE,KAAK,GAAE,KAAK,eAAe,GAAE,KAAK,WAAW,KAAKA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAK,EAAC,cAAaA,GAAC,IAAE;AAAK,QAAG,EAAEA,GAAE,YAAY,EAAE;AAAO,UAAMJ,KAAEI,GAAE;AAAa,yBAAmBJ,GAAE,QAAM,EAAEA,GAAE,MAAM,MAAI,KAAK,gBAAcA,IAAEA,GAAE,KAAK,cAAc,aAAa,EAAE,QAAS,CAAC,EAAC,UAASI,GAAC,GAAEJ,OAAI;AAAC,YAAMK,KAAE,KAAK,oCAAoCL,EAAC,GAAEM,KAAED,GAAE,KAAK,WAAW,CAAC,EAAE,SAAS,CAAC;AAAE,MAAAA,GAAE,kBAAkBC,IAAED,GAAE,KAAK,iBAAiB,cAAcD,EAAC,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK,eAAe;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,SAAK,cAAc,gBAAc,KAAK,uBAAsB,KAAK,QAAQ,GAAE,KAAK,iBAAiB,SAAO,GAAE,KAAK,iBAAiB,SAAO;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,UAAMA,KAAE,KAAK,eAAcJ,KAAEA,GAAEI,GAAE,aAAa,GAAEC,KAAEL,GAAEA,GAAE,CAAC,EAAE,QAAQ,EAAE,kBAAiBM,KAAE,KAAK;AAA0B,SAAK,cAAc,gBAAc,KAAK,oCAAoC,IAAK,CAACC,IAAEC,OAAI;AAAC,YAAMC,KAAEF,GAAE,KAAK;AAAS,aAAO,KAAK,sBAAsBC,EAAC,EAAE,WAASC,IAAE,EAAC,UAAS,GAAEA,IAAEJ,EAAC,GAAE,aAAYC,KAAEN,GAAEA,GAAEQ,EAAC,CAAC,EAAE,cAAYJ,GAAE,SAASK,EAAC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBL,IAAE;AAAC,QAAG,KAAK,iBAAeA,GAAE;AAAO,UAAMJ,KAAEA,GAAE,KAAK,sBAAsB,KAAK,YAAY,EAAE,MAAM,EAAE,MAAM;AAAE,IAAAU,GAAEV,IAAEK,GAAE,CAAC,GAAE,KAAK,sBAAsB,KAAK,YAAY,EAAE,SAAOL;AAAE,UAAMK,KAAEL,GAAE,KAAK,sBAAsBI,EAAC,EAAE,MAAM,EAAE,MAAM;AAAE,IAAAM,GAAEL,IAAE,CAAC,GAAE,KAAK,sBAAsBD,EAAC,EAAE,SAAOC,IAAE,KAAK,eAAaD,IAAE,KAAK,KAAK,YAAU,SAAS,iBAAe,KAAK,sBAAsB;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAuB;AAAC,SAAK,4BAA4B;AAAE,UAAMA,KAAE,MAAM,KAAK,KAAK,cAAc,KAAK,cAAc;AAAE,SAAK,mBAAiBA,GAAE,UAAU,KAAK,sBAAsB,KAAK,YAAY,CAAC;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAC,SAAK,oBAAkB,KAAK,iBAAiB,OAAO;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,MAAM,cAAa;AAAC,UAAK,EAAC,MAAKA,IAAE,cAAaJ,GAAC,IAAE;AAAK,QAAG,EAAEA,GAAE,YAAY,EAAE;AAAO,UAAMO,KAAEP,GAAE;AAAa,yBAAmBO,GAAE,QAAM,EAAEA,GAAE,MAAM,MAAI,KAAK,gBAAcA,IAAE,KAAK,wBAAsBP,GAAE,KAAK,cAAc,aAAa,GAAEI,GAAE,IAAI,QAAQ,CAAC,KAAK,cAAc,CAAC,GAAEA,GAAE,MAAM,GAAE,KAAK,UAAQ,OAAG,KAAK,mBAAmB,GAAE,MAAM,KAAK,sBAAsB,GAAE,KAAK,sCAAoCJ,GAAE,KAAK,cAAc,aAAa,EAAE,IAAK,CAAC,EAAC,UAASA,GAAC,MAAI,EAAE,aAAa,GAAEA,IAAEI,GAAE,gBAAgB,GAAEF,GAAE,KAAK,CAAE,GAAE,KAAK,wBAAsB,KAAK,oCAAoC,IAAK,CAACE,IAAEJ,OAAI,IAAIW,GAAE,EAAC,QAAO,IAAI,EAAE,EAAC,MAAK,EAAC,MAAK,sBAAqB,QAAO,EAAC,MAAK,kBAAiB,cAAa,CAAC,EAAC,MAAK,mBAAkB,QAAO,MAAG,aAAY,MAAG,aAAY,EAAC,GAAE,GAAE,GAAE,OAAM,GAAE,kBAAiB,YAAW,oBAAmB,KAAI,MAAK,GAAE,iBAAgB,iBAAgB,OAAM,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,MAAK,MAAK,KAAI,GAAE,gBAAe,CAAC,EAAC,MAAK,oBAAmB,UAAS,EAAC,OAAM,CAAC,CAAC,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,IAAG,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,MAAK,EAAE,GAAE,CAAC,KAAI,IAAI,GAAE,CAAC,KAAI,IAAI,GAAE,CAAC,KAAI,IAAI,GAAE,CAAC,GAAE,IAAI,GAAE,CAAC,KAAG,IAAI,GAAE,CAAC,KAAI,IAAI,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,GAAG,GAAE,CAAC,MAAK,GAAG,GAAE,CAAC,MAAK,GAAG,GAAE,CAAC,MAAK,GAAE,GAAE,CAAC,MAAK,GAAE,GAAE,CAAC,MAAK,GAAG,GAAE,CAAC,MAAK,GAAG,GAAE,CAAC,MAAK,GAAG,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,CAAC,CAAC,EAAC,GAAE,QAAO,EAAC,MAAK,oBAAmB,cAAa,CAAC,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAM,CAAC,KAAI,KAAI,KAAI,GAAG,EAAC,CAAC,EAAC,EAAC,CAAC,GAAE,4BAA2B,MAAG,cAAa,MAAG,cAAa,EAAC,MAAK,mBAAkB,cAAa,aAAY,MAAK,EAAC,OAAM,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,MAAK,CAAC,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,GAAE,IAAI,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC,EAAC,EAAC,GAAE,UAAS,EAAC,GAAE,EAAC,MAAK,mBAAkB,QAAO,MAAG,aAAY,EAAC,GAAE,GAAE,GAAE,OAAM,GAAE,kBAAiB,YAAW,oBAAmB,KAAI,MAAK,MAAK,iBAAgB,iBAAgB,OAAM,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,OAAM,MAAK,MAAK,GAAE,gBAAe,CAAC,EAAC,MAAK,oBAAmB,UAAS,EAAC,OAAM,CAAC,CAAC,CAAC,MAAK,CAAC,GAAE,CAAC,OAAM,IAAI,GAAE,CAAC,OAAM,IAAI,GAAE,CAAC,OAAM,IAAI,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,GAAG,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,GAAG,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,GAAG,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,GAAG,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,MAAK,KAAK,GAAE,CAAC,MAAK,KAAK,GAAE,CAAC,MAAK,GAAG,GAAE,CAAC,MAAK,KAAK,GAAE,CAAC,MAAK,KAAK,GAAE,CAAC,MAAK,KAAK,GAAE,CAAC,IAAG,KAAK,GAAE,CAAC,IAAG,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,KAAG,KAAK,GAAE,CAAC,GAAE,KAAK,GAAE,CAAC,KAAG,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,KAAI,KAAK,GAAE,CAAC,MAAK,KAAK,GAAE,CAAC,MAAK,KAAK,GAAE,CAAC,IAAG,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,CAAC,CAAC,CAAC,EAAC,GAAE,QAAO,EAAC,MAAK,oBAAmB,cAAa,CAAC,EAAC,MAAK,gBAAe,QAAO,MAAG,OAAMX,OAAI,KAAK,eAAa,EAAE,QAAQ,IAAEK,GAAE,EAAE,QAAQ,EAAC,CAAC,EAAC,EAAC,CAAC,GAAE,4BAA2B,MAAG,cAAa,MAAG,cAAa,EAAC,MAAK,mBAAkB,cAAa,aAAY,MAAK,EAAC,OAAM,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,OAAM,CAAC,GAAE,CAAC,OAAM,KAAK,GAAE,CAAC,GAAE,KAAK,GAAE,CAAC,GAAE,CAAC,CAAC,CAAC,EAAC,EAAC,GAAE,UAAS,EAAC,CAAC,GAAE,UAAS,GAAE,QAAO,GAAE,gBAAe,WAAU,OAAM,EAAC,EAAC,EAAC,CAAC,GAAE,UAASD,GAAE,KAAK,SAAQ,CAAC,CAAE,GAAE,KAAK,eAAe,SAAS,QAAQ,CAAC,GAAG,KAAK,qBAAqB,CAAC,GAAE,KAAK,6BAA2B,KAAK,sBAAsB,IAAK,CAAAJ,OAAG,IAAI,EAAE,EAAC,MAAK,MAAK,MAAKI,IAAE,SAAQJ,GAAC,CAAC,CAAE,GAAE,KAAK,WAAW,CAAC,GAAG,KAAK,2BAA2B,IAAK,CAACI,IAAEJ,OAAII,GAAE,mBAAmB,KAAK,SAAS,KAAK,MAAKJ,EAAC,GAAG,CAACI,IAAEC,OAAI;AAAC,kBAAUD,GAAE,WAAS,KAAK,WAAW,KAAKJ,EAAC,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,iBAAiB,KAAK,EAAC,MAAK,MAAK,WAAUK,GAAC,CAAC,GAAE,KAAK,iBAAiB,SAAO,IAAG,KAAK,eAAe;AAAA,IAAC,CAAE,CAAE,GAAEH,GAAG,MAAI,KAAK,KAAK,OAAQ,MAAI,KAAK,SAAO,KAAK,eAAe,IAAE,IAAK,CAAC,CAAC,GAAE,KAAK,2BAA2B,QAAS,CAACE,IAAEJ,OAAI;AAAC,YAAMK,KAAE,CAAAD,OAAG;AAAC,aAAK,WAAW,CAACA,GAAE,OAAO,GAAG,CAAC,SAAQ,cAAc,GAAG,CAAAA,QAAG,KAAK,mBAAmBJ,EAAC,CAAE,CAAC,CAAC;AAAA,MAAC;AAAE,MAAAI,GAAE,mBAAmBC,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,WAAW,CAACD,GAAE,GAAG,YAAY,CAAAC,OAAG;AAAC,MAAAD,GAAE,eAAa,SAAOC,GAAE,QAAMN,GAAE,SAAOM,GAAE,WAAS,KAAK,oBAAkB,MAAGA,GAAE,gBAAgB,IAAGA,GAAE,QAAMN,GAAE,iBAAeM,GAAE,WAASL,GAAE,WAAS,KAAK,oBAAkB,IAAE,KAAG,KAAK,oBAAkB,CAAC,KAAK,mBAAkBK,GAAE,gBAAgB,IAAGA,GAAE,QAAMN,GAAE,cAAYM,GAAE,WAAS,KAAK,UAAQ,GAAEA,GAAE,gBAAgB,IAAG,KAAK,sBAAoBA,GAAE,QAAMN,GAAE,OAAK,KAAK,MAAM,GAAE,KAAK,OAAO,GAAEM,GAAE,gBAAgB,IAAGA,GAAE,QAAMN,GAAE,SAAO,KAAK,MAAM,GAAE,CAAC,KAAK,OAAO,GAAEM,GAAE,gBAAgB,IAAGA,GAAE,QAAMN,GAAE,SAAO,KAAK,MAAM,CAAC,KAAK,SAAQ,CAAC,GAAEM,GAAE,gBAAgB,IAAGA,GAAE,QAAMN,GAAE,UAAQ,KAAK,MAAM,KAAK,SAAQ,CAAC,GAAEM,GAAE,gBAAgB;AAAA,IAAI,CAAE,GAAED,GAAE,GAAG,UAAU,CAAAJ,OAAG;AAAC,MAAAI,GAAE,eAAa,SAAOJ,GAAE,QAAMD,GAAE,UAAQ,KAAK,oBAAkB,OAAGC,GAAE,gBAAgB,IAAGA,GAAE,QAAMD,GAAE,eAAa,KAAK,UAAQE,IAAED,GAAE,gBAAgB;AAAA,IAAG,CAAE,CAAC,CAAC;AAAA,EAAE;AAAA,EAAC,MAAM,wBAAuB;AAAC,UAAMI,KAAEJ,GAAEA,GAAE,KAAK,cAAc,aAAa,EAAE,CAAC,EAAE,QAAQ;AAAE,WAAO,GAAEI,GAAE,kBAAiB,KAAK,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,WAAM,EAAC,wBAAuB,KAAK,oCAAoCA,EAAC,GAAE,aAAY,KAAK,4BAA0B,OAAK,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,KAAK,cAAc,OAAM,MAAK,KAAK,cAAc,OAAM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAEJ,IAAE;AAAC,UAAMK,KAAE,KAAK,oCAAoC,KAAK,YAAY,GAAEC,KAAE,CAAC;AAAE,eAAUE,MAAKH,GAAE,KAAK,WAAW,CAAAC,GAAE,KAAK,GAAGE,GAAE,QAAQ;AAAE,UAAMD,KAAEF,GAAE,aAAaC,IAAEF,KAAE,KAAK,KAAK,YAAWJ,KAAE,KAAK,KAAK,YAAW,GAAEY,GAAE,QAAQ;AAAE,SAAK,iBAAiB,KAAK,EAAC,MAAK,MAAK,WAAUL,GAAC,CAAC,GAAE,KAAK,iBAAiB,SAAO,GAAE,KAAK,eAAe;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEJ,GAAE,WAAU,6BAA4B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,MAAG,aAAY,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAACU,GAAE,mEAAmE,CAAC,GAAEV,EAAC;;;ACA5jV,SAASW,GAAEC,IAAEC,IAAE;AAAC,cAAUD,GAAE,SAAOC,GAAE,SAAO,aAAWA,GAAE,SAAO;AAAM;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,iBAAe,MAAK,KAAK,OAAK,IAAI,KAAE,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,QAAQF,IAAE;AAAC,QAAG,KAAK,aAAWA,MAAG,EAAE,KAAK,cAAc,GAAE;AAAC,YAAMC,KAAE,EAAC,GAAG,KAAK,gBAAe,QAAO,SAAQ;AAAE,MAAAD,MAAG,KAAK,oBAAoBC,EAAC,GAAE,KAAK,KAAK,QAAQA,EAAC;AAAA,IAAC;AAAC,SAAK,WAASD;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAC,WAAO,KAAK,iBAAe,MAAK,CAAAA,QAAI,KAAK,iBAAe,UAAQA,GAAE,SAAO,EAAC,GAAGA,GAAC,IAAE,MAAK,KAAK,YAAU,KAAK,oBAAoBA,EAAC,GAAEA;AAAA,EAAE;AAAA,EAAC,oBAAoBA,IAAE;AAAC,UAAMC,KAAE,MAAID,GAAE,UAAU,CAAC,KAAG,MAAIA,GAAE,UAAU,CAAC,IAAE,KAAK,IAAI,KAAK,IAAIA,GAAE,OAAO,GAAE,KAAK,IAAIA,GAAE,OAAO,CAAC,IAAE,MAAIA,GAAE,UAAU,CAAC,IAAE,KAAK,IAAIA,GAAE,OAAO,IAAE,KAAK,IAAIA,GAAE,OAAO;AAAE,IAAAA,GAAE,UAAQA,GAAE,UAAQ,IAAE,CAACC,KAAEA,IAAED,GAAE,UAAQA,GAAE,UAAQ,IAAE,CAACC,KAAEA;AAAA,EAAC;AAAC;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,iBAAe,MAAK,KAAK,OAAK,IAAI,KAAE,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,QAAQH,IAAE;AAAC,QAAG,KAAK,aAAWA,MAAG,EAAE,KAAK,cAAc,GAAE;AAAC,YAAMC,KAAE,EAAC,GAAG,KAAK,gBAAe,QAAO,SAAQ;AAAE,MAAAD,MAAG,KAAK,mBAAmBC,EAAC,GAAE,KAAK,KAAK,QAAQA,EAAC;AAAA,IAAC;AAAC,SAAK,WAASD;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAC,WAAO,KAAK,iBAAe,MAAK,CAAAA,QAAI,KAAK,iBAAe,UAAQA,GAAE,SAAO,EAAC,GAAGA,GAAC,IAAE,MAAK,KAAK,YAAU,KAAK,mBAAmBA,EAAC,GAAEA;AAAA,EAAE;AAAA,EAAC,mBAAmBI,IAAE;AAAC,UAAMC,KAAE,EAAED,GAAE,WAAW;AAAE,IAAAA,GAAE,cAAY,EAAE,IAAE,KAAK,MAAMC,KAAE,CAAC,CAAC;AAAA,EAAC;AAAC;;;ACAtqB,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,GAAE,KAAK,WAAS,IAAIC,MAAE,KAAK,eAAaC,GAAE,GAAE,KAAK,QAAMF,GAAE,MAAK,KAAK,QAAMA,GAAE,MAAK,KAAK,WAASA,GAAE,SAAQ,KAAK,gBAAcA,GAAE,cAAa,KAAK,eAAa,KAAK,mBAAmB,GAAE,KAAK,SAAS,IAAI,CAAC,KAAK,aAAa,OAAO,GAAG,gBAAgB,CAAAC,OAAGE,GAAEF,IAAE,KAAK,YAAY,CAAE,CAAC,CAAC,GAAE,KAAK,mBAAoB,CAAAA,OAAG,KAAK,MAAM,aAAa,IAAIA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAS,QAAQ,GAAE,KAAK,mBAAoB,CAAAA,OAAG;AAAC,WAAK,MAAM,aAAa,OAAOA,EAAC,GAAEA,GAAE,QAAQ;AAAA,IAAC,CAAE,GAAE,KAAK,QAAM,MAAK,KAAK,QAAM,MAAK,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAK,KAAK,WAAS,MAAK,KAAK,WAAS,MAAK,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,mBAAmBA,IAAE;AAAC,IAAAA,GAAE,KAAK,cAAaA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEF,IAAE;AAAC,QAAIK,KAAE,MAAKC,KAAE;AAAK,WAAO,EAAE,KAAK,cAAc,CAACC,IAAEH,OAAI;AAAC,MAAAA,GAAE,KAAM,CAAAH,OAAG;AAAC,YAAG,YAAUA,GAAE,QAAO;AAAC,UAAAM,GAAE,SAAO;AAAW,gBAAMN,KAAEC,GAAE;AAAE,UAAAG,KAAEJ,GAAE,OAAMK,KAAEL,GAAE;AAAA,QAAsB;AAAC,eAAOA;AAAA,MAAC,CAAE,EAAE,KAAKO,GAAE,KAAK,KAAK,CAAC,EAAE,KAAM,CAAAN,QAAI,EAAC,GAAGA,IAAE,aAAYK,GAAEL,GAAE,UAASA,GAAE,QAAO,EAAC,GAAEG,GAAE,OAAO,CAAC,GAAE,GAAEA,GAAE,OAAO,CAAC,EAAC,GAAE,IAAE,EAAC,EAAG,EAAE,KAAK,KAAK,cAAc,4BAA4B,GAAE,KAAK,cAAc,IAAI,EAAE,KAAM,CAAAH,OAAG;AAAC,cAAMK,KAAEP,GAAE,KAAK,cAAaK,GAAE,MAAM,GAAEF,KAAE,CAAC;AAAE,mBAAUF,MAAKK,GAAE,KAAK,WAAW,CAAAH,GAAE,KAAK,GAAGF,GAAE,QAAQ;AAAE,cAAMG,KAAE,YAAUF,GAAE,SAAOO,GAAE,WAASA,GAAE,kBAAiBC,KAAEJ,GAAE,eAAeH,IAAEI,IAAEL,GAAE,aAAYE,IAAEC,GAAE,OAAO;AAAE,eAAOM,GAAED,IAAEL,EAAC,GAAEL,GAAEE,IAAEQ,EAAC,GAAER;AAAA,MAAC,CAAE,EAAE,KAAM,CAAAA,QAAI,UAAQA,GAAE,WAASK,GAAE,SAAO,SAAQL,GAAG;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMA,KAAE,KAAK,OAAMD,KAAE,KAAK;AAAS,WAAO,IAAI,EAAE,EAAC,MAAKC,IAAE,SAAQD,IAAE,YAAW,MAAG,QAAO,OAAM,CAAC;AAAA,EAAC;AAAC;;;ACAhvC,IAAMW,KAAE;AAAR,IAAWC,KAAE;AAAb,IAAkBC,KAAE;AAAG,SAASC,GAAEC,IAAE;AAAC,QAAMC,KAAEC,GAAEF,GAAE,MAAM,GAAEG,KAAED,GAAEF,GAAE,MAAM;AAAE,SAAOF,KAAE,KAAK,IAAIG,IAAEE,EAAC;AAAC;AAAC,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYJ,IAAE;AAAC,UAAM,GAAE,KAAK,WAAS,IAAID,MAAE,KAAK,cAAY,EAAE,GAAE,KAAK,qBAAmB,EAAE,GAAE,KAAK,eAAaM,GAAE,GAAE,KAAK,aAAWA,GAAE,GAAE,KAAK,oBAAkBA,GAAE,GAAE,KAAK,kBAAgBA,GAAE,GAAE,KAAK,qBAAmBA,GAAE,GAAE,KAAK,QAAML,GAAE,MAAK,KAAK,QAAMA,GAAE,MAAK,KAAK,WAASA,GAAE,SAAQ,KAAK,aAAWA,GAAE,WAAU,KAAK,uBAAqBA,GAAE,qBAAoB,KAAK,eAAa,KAAK,mBAAmB,GAAE,KAAK,SAAS,IAAI,CAAC,KAAK,aAAa,OAAO,GAAG,gBAAgB,CAAAD,OAAGE,GAAEF,IAAE,KAAK,YAAY,CAAE,CAAC,CAAC,GAAE,KAAK,mBAAoB,CAAAA,OAAG,KAAK,MAAM,aAAa,IAAIA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAS,QAAQ,GAAE,KAAK,mBAAoB,CAAAA,OAAG;AAAC,WAAK,MAAM,aAAa,OAAOA,EAAC,GAAEA,GAAE,QAAQ;AAAA,IAAC,CAAE,GAAE,KAAK,QAAM,MAAK,KAAK,QAAM,MAAK,KAAK,WAAS,MAAK,KAAK,eAAa,MAAK,KAAK,aAAW,MAAK,KAAK,WAAS,MAAK,KAAK,cAAY,MAAK,KAAK,qBAAmB,MAAK,KAAK,eAAa,MAAK,KAAK,aAAW,MAAK,KAAK,oBAAkB,MAAK,KAAK,kBAAgB,MAAK,KAAK,qBAAmB,MAAK,KAAK,uBAAqB;AAAA,EAAI;AAAA,EAAC,mBAAmBA,IAAE;AAAC,IAAAA,GAAE,KAAK,cAAaA,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEE,IAAE;AAAC,QAAIK,KAAE,MAAKC,KAAE,MAAKC,KAAE,MAAKC,KAAE,GAAEC,KAAE,MAAKC,KAAE;AAAK,UAAMd,KAAE,KAAK,aAAYM,KAAE,KAAK,oBAAmBS,KAAE,KAAK;AAAW,WAAO,EAAE,KAAK,cAAc,CAAC,GAAEC,OAAI;AAAC,MAAAA,GAAE,KAAM,CAAAb,OAAG;AAAC,YAAG,YAAUA,GAAE,QAAO;AAAC,YAAE,SAAO;AAAW,gBAAMA,KAAED,GAAE;AAAE,UAAAO,KAAEN,GAAE,OAAMO,KAAEP,GAAE,cAAaQ,KAAER,GAAE,wBAAuBS,KAAEd,KAAE,KAAK,MAAM,YAAW,EAAEW,IAAET,EAAC,GAAE,EAAEU,IAAEJ,EAAC;AAAE,gBAAMD,KAAE,EAAEM,GAAE,KAAK,gBAAgB;AAAE,UAAAE,KAAER,KAAEA,GAAE,MAAM,CAAC,IAAEA,GAAE,MAAM,CAAC,IAAE,IAAEP,KAAE,KAAK,MAAM,aAAW;AAAA,QAAI;AAAC,eAAOK;AAAA,MAAC,CAAE,EAAE,KAAKc,GAAE,KAAK,KAAK,CAAC,EAAE,KAAM,CAAAf,OAAG;AAAC,cAAMC,KAAEI,GAAE,KAAK,mBAAkB,CAACL,GAAE,SAAS,GAAEA,GAAE,SAAS,GAAE,CAAC,CAAC,GAAEG,KAAEE,GAAE,KAAK,iBAAgB,CAACL,GAAE,OAAO,GAAEA,GAAE,OAAO,GAAE,CAAC,CAAC,GAAEE,KAAEG,GAAE,KAAK,oBAAmBD,GAAE,MAAM;AAAE,UAAEF,IAAEA,IAAEE,GAAE,QAAO,CAACS,GAAE,CAAC,CAAC,GAAE,EAAEX,IAAEA,IAAEE,GAAE,QAAO,CAACS,GAAE,CAAC,CAAC,GAAEV,GAAEA,IAAEA,IAAED,EAAC,GAAEC,GAAEF,IAAEA,IAAEC,EAAC;AAAE,cAAMK,KAAE,MAAIM,GAAE,CAAC,KAAG,MAAIA,GAAE,CAAC,GAAEG,KAAEjB,GAAEK,EAAC,GAAEa,KAAElB,GAAES,EAAC,IAAEQ,IAAEP,KAAE,CAACT,IAAEE,OAAI;AAAC,cAAG,MAAIF,GAAE,QAAO;AAAE,cAAIkB,KAAEhB,GAAEA,EAAC,GAAEI,KAAE,MAAGN,KAAE,EAAEE,IAAEC,EAAC,IAAEe;AAAE,gBAAMC,KAAEb,KAAE,IAAE,KAAG;AAAE,cAAGC,IAAE;AAAC,YAAAD,OAAIY,KAAE,MAAGlB,KAAE,EAAEE,IAAED,EAAC,IAAEiB,MAAGC,KAAEF;AAAA,UAAC;AAAC,gBAAMT,KAAEU,KAAE,MAAIR,KAAE,IAAEb;AAAE,iBAAOqB,KAAE,KAAK,IAAIA,KAAER,IAAEb,EAAC,GAAEsB,KAAE,MAAIb,MAAGV,KAAE,KAAK,MAAM,aAAYuB,KAAE,KAAK,IAAIA,MAAGb,KAAEY,KAAGV,EAAC;AAAA,QAAC,GAAEG,KAAEF,GAAEI,GAAE,CAAC,GAAET,GAAE,MAAM,GAAEgB,KAAEX,GAAEI,GAAE,CAAC,GAAET,GAAE,MAAM;AAAE,eAAM,EAAC,GAAGJ,IAAE,WAAUa,IAAE,SAAQF,IAAE,SAAQS,GAAC;AAAA,MAAC,CAAE,EAAE,KAAK,KAAK,qBAAqB,4BAA4B,GAAE,KAAK,qBAAqB,IAAI,EAAE,KAAM,CAAApB,OAAG;AAAC,cAAMK,KAAEA,GAAE,KAAK,cAAaP,GAAE,MAAM;AAAE,UAAEO,IAAEA,IAAEP,GAAE,QAAO,CAACe,GAAE,CAAC,CAAC,GAAE,EAAER,IAAEA,IAAEP,GAAE,QAAO,CAACe,GAAE,CAAC,CAAC;AAAE,cAAMM,KAAEd,GAAE,KAAK,YAAWP,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC;AAAE,QAAAiB,GAAEI,IAAEA,EAAC;AAAE,cAAME,KAAE,CAAC;AAAE,mBAAUpB,MAAKQ,GAAE,KAAK,WAAW,CAAAY,GAAE,KAAK,GAAGpB,GAAE,QAAQ;AAAE,cAAMO,KAAE,YAAUR,GAAE,SAAOJ,GAAE,WAASA,GAAE,kBAAiBqB,KAAER,GAAE,cAAcY,IAAEhB,IAAEc,IAAEnB,GAAE,SAAQA,GAAE,SAAQQ,IAAEL,GAAE,OAAO;AAAE,eAAOQ,MAAGA,KAAEF,GAAE,KAAK,SAAS,OAAO,SAAOG,KAAEH,GAAE,eAAeY,IAAET,EAAC,KAAG,EAAEd,IAAES,EAAC,GAAEV,GAAEoB,IAAEV,EAAC,GAAEK,KAAEK,GAAE,WAAUf,GAAEF,IAAEiB,EAAC,IAAGjB;AAAA,MAAC,CAAE,EAAE,KAAM,CAAAA,QAAI,UAAQA,GAAE,WAAS,EAAE,SAAO,SAAQA,GAAG;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,IAAI,EAAE,EAAC,MAAK,KAAK,OAAM,SAAQ,KAAK,UAAS,YAAW,MAAG,QAAO,OAAM,CAAC;AAAA,EAAC;AAAC;;;ACA3yC,IAAM,IAAE,EAAC,IAAG,WAAU,MAAK,aAAY,MAAK,aAAY,OAAM,cAAa,MAAK,KAAI,OAAM,KAAI,eAAc,KAAI,OAAM,SAAQ,YAAWsB,GAAC;AAA1I,IAA4I,IAAE;AAA9I,IAAiJ,IAAE;AAAnJ,IAAsJ,IAAE;AAAxJ,IAA2J,IAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAnN,IAAqNC,KAAE;AAAvN,IAAyNC,KAAE;AAAG,IAAI,KAAG,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,wBAAsB,MAAK,KAAK,mBAAiB,MAAK,KAAK,WAAS,MAAK,KAAK,cAAY,EAAE,GAAE,KAAK,qBAAmB,EAAE,GAAE,KAAK,iBAAeC,GAAE,GAAE,KAAK,uBAAqBA,GAAE,GAAE,KAAK,WAASA,GAAE,GAAE,KAAK,WAASA,GAAE,GAAE,KAAK,WAASA,GAAE,GAAE,KAAK,WAASA,GAAE,GAAE,KAAK,SAAOA,GAAE,GAAE,KAAK,SAAOA,GAAE,GAAE,KAAK,SAAOA,GAAE,GAAE,KAAK,SAAOA,GAAE,GAAE,KAAK,uBAAqB,IAAIC,MAAE,KAAK,gBAAc,IAAIC,MAAE,KAAK,iBAAe,IAAI,EAAE,EAAC,UAAS,MAAG,UAAS,QAAO,SAAQ,MAAE,CAAC,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,oBAAkB,OAAG,KAAK,oBAAkB,OAAG,KAAK,UAAQ,GAAE,KAAK,sBAAoB,MAAK,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,YAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAK,EAAC,KAAIH,GAAC,IAAE,KAAK;AAAK,SAAK,kBAAkB,QAAQ,GAAE,KAAK,oBAAoB,QAAQ,GAAE,KAAK,oBAAoB,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,wBAAwB,QAAQ,GAAEA,GAAE,WAAW,CAAC,KAAK,cAAc,CAAC,GAAE,KAAK,eAAe,UAAU,GAAE,KAAK,iBAAe,EAAE,KAAK,cAAc,GAAE,KAAK,wBAAsB,MAAK,KAAK,mBAAiB,MAAK,KAAK,WAAS,MAAK,KAAK,uBAAqB,MAAK,KAAK,gBAAc,MAAK,KAAK,cAAY,MAAK,KAAK,qBAAmB,MAAK,KAAK,uBAAqB,MAAK,KAAK,iBAAe,MAAK,KAAK,WAAS,MAAK,KAAK,WAAS,MAAK,KAAK,WAAS,MAAK,KAAK,WAAS,MAAK,KAAK,SAAO,MAAK,KAAK,SAAO,MAAK,KAAK,SAAO,MAAK,KAAK,SAAO,MAAK,KAAK,mBAAiB,MAAK,KAAK,mBAAiB;AAAA,EAAI;AAAA,EAAC,IAAI,SAAQ;AAAC,UAAMA,KAAE,KAAK,SAAS;AAAS,QAAG,CAAC,EAAEA,EAAC,EAAE,QAAO;AAAK,UAAMH,KAAE,KAAK,wBAAwB,MAAKM,KAAEN,GAAE,WAAW,CAAC,EAAE,MAAM,CAAC,GAAEO,KAAE,EAAE,KAAK,gBAAeD,GAAE,WAAW,KAAIA,GAAE,YAAY,GAAG;AAAE,IAAAE,GAAED,IAAEA,EAAC;AAAE,QAAIE,KAAE,IAAE,KAAK,KAAK;AAAW,UAAMC,KAAE,KAAK,KAAK;AAAiB,WAAO,EAAEA,IAAEP,GAAE,gBAAgB,MAAIM,MAAG,EAAEC,EAAC,IAAE,EAAEP,GAAE,gBAAgB,IAAGQ,GAAEJ,IAAEP,IAAES,IAAE,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,UAAMN,KAAE,KAAK;AAAO,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMH,KAAE,KAAK;AAAmB,MAAEG,IAAEH,EAAC;AAAE,UAAMM,KAAE,IAAE,KAAK,KAAK;AAAW,WAAO,EAAEN,GAAE,QAAOA,GAAE,QAAO,IAAEM,KAAEC,GAAEP,GAAE,MAAM,CAAC,GAAE,EAAEA,GAAE,QAAOA,GAAE,QAAO,IAAEM,KAAEC,GAAEP,GAAE,MAAM,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,IAAI,6BAA4B;AAAC,UAAMG,KAAE,KAAK;AAAc,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMH,KAAE,KAAK,KAAK;AAAiB,WAAO,KAAK,2BAA2BG,EAAC,GAAE,IAAIK,GAAE,EAAC,kBAAiBR,IAAE,OAAM,CAAC,CAAC,KAAK,UAAS,KAAK,UAAS,KAAK,UAAS,KAAK,UAAS,KAAK,QAAQ,CAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,yBAAwB;AAAC,UAAMG,KAAE,KAAK;AAAO,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMH,KAAE,KAAK;AAAqB,WAAO,EAAEA,IAAEG,GAAE,MAAM,GAAE,EAAEH,IAAEA,IAAE,IAAE,KAAK,KAAK,UAAU,GAAEY,GAAEZ,IAAEA,IAAEG,GAAE,MAAM,GAAES,GAAEZ,IAAEA,IAAEG,GAAE,MAAM,GAAE,IAAI,EAAE,EAAC,GAAEH,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiB,KAAK,KAAK,iBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,0BAAyB;AAAC,UAAMG,KAAE,KAAK;AAAc,QAAG,CAACA,GAAE,QAAM,CAAC;AAAE,UAAMH,KAAE,KAAK,KAAK;AAAiB,SAAK,2BAA2BG,EAAC;AAAE,UAAK,EAAC,UAASG,IAAE,UAASC,IAAE,UAASE,IAAE,UAASJ,GAAC,IAAE,MAAKK,KAAE,EAAE,KAAK,QAAOJ,IAAEC,IAAE,GAAE,GAAEH,KAAE,EAAE,KAAK,QAAOG,IAAEE,IAAE,GAAE,GAAEI,KAAE,EAAE,KAAK,QAAOJ,IAAEJ,IAAE,GAAE,GAAES,KAAE,EAAE,KAAK,QAAOT,IAAEC,IAAE,GAAE;AAAE,WAAM,CAAC,IAAI,EAAE,EAAC,GAAEA,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBN,GAAC,CAAC,GAAE,IAAI,EAAE,EAAC,GAAEO,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBP,GAAC,CAAC,GAAE,IAAI,EAAE,EAAC,GAAES,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBT,GAAC,CAAC,GAAE,IAAI,EAAE,EAAC,GAAEK,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBL,GAAC,CAAC,GAAE,IAAI,EAAE,EAAC,GAAEU,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBV,GAAC,CAAC,GAAE,IAAI,EAAE,EAAC,GAAEI,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBJ,GAAC,CAAC,GAAE,IAAI,EAAE,EAAC,GAAEa,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBb,GAAC,CAAC,GAAE,IAAI,EAAE,EAAC,GAAEc,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBd,GAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,eAAc;AAAC,SAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,SAAK,eAAe,UAAQ;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,SAAK,eAAe,UAAQ;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,wBAAwB;AAAA,EAAO;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,wBAAwB;AAAA,EAAO;AAAA,EAAC,OAAM;AAAC,SAAK,wBAAwB,KAAK,GAAE,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,wBAAwB,KAAK,GAAE,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAK,EAAC,MAAKG,IAAE,QAAOH,GAAC,IAAE,MAAKM,KAAE,kBAAiBN,KAAEA,GAAEA,GAAEA,GAAE,YAAY,EAAE,MAAM,IAAEA,GAAE,UAASO,KAAE,KAAK,yBAAwBE,KAAEF,GAAE,KAAK,WAAW,CAAC,EAAE,UAASF,KAAEU,GAAE,aAAa,GAAET,IAAEH,GAAE,gBAAgB,GAAEa,GAAE,KAAK,EAAE,WAAW,CAAC,EAAE;AAAS,IAAAP,GAAE,QAAS,CAACN,IAAEH,OAAI;AAAC,MAAAO,GAAE,kBAAkBJ,IAAEE,GAAEL,EAAC,EAAE,GAAG;AAAA,IAAC,CAAE,GAAE,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAK,EAAC,QAAOG,GAAC,IAAE;AAAK,QAAG,kBAAiBA,IAAE;AAAC,YAAMH,KAAEA,GAAEG,GAAE,YAAY;AAAE,2BAAmBH,GAAE,SAAOA,GAAE,gBAAc,KAAK;AAAA,IAAsB,MAAM,CAAAG,GAAE,WAAS,KAAK;AAAiB,SAAK,QAAQ,GAAE,KAAK,iBAAiB,SAAO,GAAE,KAAK,iBAAiB,SAAO;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,UAAMA,KAAE,KAAK,wBAAwB,KAAK;AAAS,QAAG,kBAAiB,KAAK,QAAO;AAAC,MAAAH,GAAE,KAAK,OAAO,YAAY,EAAE,SAAOG;AAAA,IAAC;AAAC,SAAK,SAAS,WAASA,IAAE,KAAK,mBAAmB,WAAS,KAAK,4BAA2B,KAAK,eAAe,WAAS,KAAK,wBAAuB,KAAK,wBAAwB,QAAS,CAACA,IAAEH,OAAI;AAAC,WAAK,eAAeA,EAAC,EAAE,WAASG;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,MAAM,cAAa;AAAC,UAAK,EAAC,MAAKA,IAAE,QAAOM,GAAC,IAAE;AAAK,QAAG,kBAAiBA,IAAE;AAAC,YAAMN,KAAEH,GAAES,GAAE,YAAY;AAAE,WAAK,WAAS,IAAIM,GAAE,EAAC,UAASf,GAAEG,GAAE,MAAM,EAAC,CAAC,GAAE,KAAK,wBAAsB,qBAAmBA,GAAE,OAAKA,GAAE,gBAAc;AAAA,IAAI,MAAM,MAAK,WAASM,IAAE,KAAK,mBAAiBT,GAAES,GAAE,QAAQ;AAAE,IAAAN,GAAE,IAAI,QAAQ,CAAC,KAAK,cAAc,CAAC,GAAEA,GAAE,MAAM,GAAE,KAAK,UAAQ,OAAG,KAAK,mBAAmB,GAAE,MAAM,KAAK,sBAAsB,GAAE,KAAK,0BAAwB,EAAE,aAAa,GAAE,KAAK,SAAS,UAASA,GAAE,gBAAgB,GAAEa,GAAE,KAAK,GAAE,KAAK,qBAAmB,IAAID,GAAE,EAAC,QAAO,IAAI,EAAE,EAAC,OAAM,eAAc,SAAQ,EAAC,MAAK,eAAc,OAAMT,GAAE,GAAE,OAAM,EAAC,EAAC,CAAC,GAAE,UAAS,KAAK,2BAA0B,CAAC,GAAE,KAAK,iBAAe,IAAIS,GAAE,EAAC,QAAO,IAAIE,GAAE,EAAC,OAAML,GAAE,GAAE,SAAQ,EAAC,MAAK,eAAc,OAAMN,GAAE,GAAE,OAAM,EAAC,EAAC,CAAC,GAAE,UAAS,KAAK,uBAAsB,CAAC,GAAE,KAAK,iBAAe,KAAK,wBAAwB,IAAK,CAAAH,OAAG,IAAIY,GAAE,EAAC,QAAO,IAAIE,GAAE,EAAC,MAAK,GAAE,OAAM,UAAS,OAAML,GAAE,GAAE,SAAQ,EAAC,MAAK,eAAc,OAAMN,GAAE,GAAE,OAAM,EAAC,EAAC,CAAC,GAAE,UAASH,GAAC,CAAC,CAAE,GAAE,KAAK,eAAe,SAAS,QAAQ,CAAC,KAAK,oBAAmB,KAAK,gBAAe,GAAG,KAAK,cAAc,CAAC,GAAE,KAAK,oBAAkB,IAAI,EAAE,EAAC,MAAK,MAAK,MAAKA,IAAE,SAAQ,KAAK,SAAQ,CAAC,GAAE,KAAK,sBAAoB,IAAIe,GAAE,EAAC,MAAK,MAAK,MAAKf,IAAE,SAAQ,KAAK,gBAAe,cAAa,KAAK,cAAa,CAAC,GAAE,KAAK,sBAAoB,KAAK,eAAe,IAAK,CAACH,IAAEM,OAAI,IAAIa,GAAE,EAAC,MAAK,MAAK,MAAKhB,IAAE,SAAQH,IAAE,WAAU,EAAEM,EAAC,GAAE,qBAAoB,KAAK,qBAAoB,CAAC,CAAE,GAAE,KAAK,WAAW,CAAC,KAAK,kBAAkB,mBAAmB,KAAK,SAAS,KAAK,IAAI,GAAE,KAAK,gBAAgB,KAAK,IAAI,CAAC,GAAE,KAAK,oBAAoB,mBAAmB,KAAK,SAAS,KAAK,IAAI,GAAE,KAAK,gBAAgB,KAAK,IAAI,CAAC,GAAE,GAAG,KAAK,oBAAoB,IAAK,CAAAH,OAAGA,GAAE,mBAAmB,KAAK,SAAS,KAAK,IAAI,GAAE,KAAK,gBAAgB,KAAK,IAAI,CAAC,CAAE,GAAEa,GAAG,MAAI,KAAK,KAAK,OAAQ,MAAI,KAAK,SAAO,KAAK,eAAe,IAAE,IAAK,GAAEb,GAAE,GAAG,SAAS,OAAMH,OAAG;AAAC,UAAG,QAAMG,GAAE,cAAYA,GAAE,eAAa,KAAK;AAAO,YAAMG,KAAEF,GAAEJ,EAAC,GAAEO,KAAE,CAAC;AAAE,MAAAJ,GAAE,IAAI,UAAU,QAAS,CAAAA,OAAG;AAAC,0BAAgBA,GAAE,QAAM,cAAYA,GAAE,QAAMI,GAAE,KAAKJ,EAAC;AAAA,MAAC,CAAE;AAAE,YAAMO,KAAE,MAAM,KAAK,KAAK,QAAQJ,IAAE,EAAC,SAAQC,GAAC,CAAC,GAAEH,KAAEM,GAAE;AAAQ,UAAG,MAAIN,GAAE,OAAO,CAAAD,GAAE,aAAW;AAAA,WAAS;AAAC,cAAMH,KAAE,EAAEU,GAAE,OAAO,GAAEJ,KAAE,kBAAiBG,IAAEF,KAAEH,GAAE,IAAK,CAAAD,OAAG,YAAUA,GAAE,OAAKA,GAAE,UAAQ,IAAK,EAAE,OAAO,OAAO,GAAEU,KAAE,CAAC,GAAG,KAAK,eAAe,UAASP,KAAE,OAAKG,EAAC,EAAE,OAAO,OAAO;AAAE,QAAAH,MAAGC,GAAE,SAASE,EAAC,KAAG,EAAET,EAAC,KAAGa,GAAE,SAASb,GAAE,OAAO,IAAE,QAAMG,GAAE,eAAaA,GAAE,aAAW,QAAMA,GAAE,aAAW;AAAA,MAAI;AAAA,IAAC,CAAE,CAAC,CAAC;AAAE,UAAMU,KAAE,CAAAV,OAAG;AAAC,WAAK,WAAWA,GAAE,OAAO,GAAG,gBAAgB,CAAAA,OAAG;AAAC,0BAAiBM,OAAI,YAAUN,GAAE,SAAOM,GAAE,WAAS,MAAG,UAAQN,GAAE,WAASM,GAAE,WAAS;AAAA,MAAG,CAAE,CAAC;AAAA,IAAC;AAAE,SAAK,kBAAkB,mBAAmBI,EAAC,GAAE,KAAK,oBAAoB,mBAAmBA,EAAC,GAAE,KAAK,oBAAoB,QAAS,CAAAV,OAAGA,GAAE,mBAAmBU,EAAC,CAAE,GAAE,KAAK,WAAW,CAACV,GAAE,GAAG,YAAY,CAAAH,OAAG;AAAC,MAAAG,GAAE,eAAa,SAAOH,GAAE,QAAM,EAAE,SAAOA,GAAE,WAAS,QAAM,KAAK,wBAAsB,KAAK,qBAAqB,UAAQ,CAAC,KAAK,qBAAqB,UAAS,QAAM,KAAK,iBAAe,KAAK,cAAc,UAAQ,CAAC,KAAK,cAAc,UAAS,KAAK,oBAAkB,MAAGA,GAAE,gBAAgB,IAAGA,GAAE,QAAM,EAAE,iBAAeA,GAAE,WAAS,kBAAiBS,OAAIA,GAAE,WAAS,KAAK,oBAAkB,IAAE,KAAG,KAAK,oBAAkB,CAAC,KAAK,oBAAmBT,GAAE,gBAAgB,IAAGA,GAAE,QAAM,EAAE,cAAYA,GAAE,WAAS,KAAK,UAAQE,IAAEF,GAAE,gBAAgB,IAAG,KAAK,sBAAoBA,GAAE,QAAM,EAAE,SAAO,KAAK,OAAO,KAAK,OAAO,GAAEA,GAAE,gBAAgB,IAAGA,GAAE,QAAM,EAAE,UAAQ,KAAK,OAAO,CAAC,KAAK,OAAO,GAAEA,GAAE,gBAAgB,IAAGA,GAAE,QAAM,EAAE,OAAK,KAAK,MAAM,GAAE,KAAK,OAAO,GAAEA,GAAE,gBAAgB,IAAGA,GAAE,QAAM,EAAE,SAAO,KAAK,MAAM,GAAE,CAAC,KAAK,OAAO,GAAEA,GAAE,gBAAgB,IAAGA,GAAE,QAAM,EAAE,SAAO,KAAK,MAAM,CAAC,KAAK,SAAQ,CAAC,GAAEA,GAAE,gBAAgB,IAAGA,GAAE,QAAM,EAAE,UAAQ,KAAK,MAAM,KAAK,SAAQ,CAAC,GAAEA,GAAE,gBAAgB;AAAA,IAAI,CAAE,GAAEG,GAAE,GAAG,UAAU,CAAAH,OAAG;AAAC,MAAAG,GAAE,eAAa,SAAOH,GAAE,QAAM,EAAE,UAAQ,QAAM,KAAK,wBAAsB,KAAK,qBAAqB,UAAQ,CAAC,KAAK,qBAAqB,UAAS,QAAM,KAAK,iBAAe,KAAK,cAAc,UAAQ,CAAC,KAAK,cAAc,UAAS,KAAK,oBAAkB,OAAGA,GAAE,gBAAgB,IAAGA,GAAE,QAAM,EAAE,eAAa,KAAK,UAAQC,IAAED,GAAE,gBAAgB;AAAA,IAAG,CAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAuB;AAAC,UAAMG,KAAEH,GAAE,KAAK,SAAS,QAAQ;AAAE,WAAO,GAAEG,GAAE,kBAAiB,KAAK,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAE;AAAC,UAAK,EAAC,QAAOH,IAAE,QAAOM,IAAE,QAAOC,GAAC,IAAEJ,IAAEM,KAAE,KAAK;AAAS,IAAAG,GAAEH,IAAEF,IAAEP,EAAC,GAAEY,GAAEH,IAAEA,IAAEH,EAAC;AAAE,UAAMD,KAAE,KAAK;AAAS,IAAAO,GAAEP,IAAEE,IAAEP,EAAC,GAAEA,GAAEK,IAAEA,IAAEC,EAAC;AAAE,UAAMI,KAAE,KAAK;AAAS,IAAAV,GAAEU,IAAEH,IAAEP,EAAC,GAAEA,GAAEU,IAAEA,IAAEJ,EAAC;AAAE,UAAMF,KAAE,KAAK;AAAS,IAAAJ,GAAEI,IAAEG,IAAEP,EAAC,GAAEY,GAAER,IAAEA,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAM,EAAC,wBAAuB,KAAK,yBAAwB,OAAM,KAAK,QAAO,cAAa,KAAK,cAAa;AAAA,EAAC;AAAA,EAAC,gBAAgBH,IAAEH,IAAE;AAAC,gBAAUG,GAAE,WAAS,KAAK,iBAAiB,KAAK,EAAC,MAAK,MAAK,WAAUH,GAAC,CAAC,GAAE,KAAK,iBAAiB,SAAO,IAAG,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,OAAOG,IAAE;AAJzrX;AAI0rX,UAAMH,KAAE,KAAK,yBAAwBM,KAAE,CAAC;AAAE,eAAUI,MAAKV,GAAE,KAAK,WAAW,CAAAM,GAAE,KAAK,GAAGI,GAAE,QAAQ;AAAE,UAAMH,MAAE,KAAAP,GAAE,KAAK,SAAS,WAAhB,mBAAwB,OAAMS,MAAGF,KAAEJ,KAAE,KAAK,KAAK,cAAYI,IAAEF,KAAEL,GAAE,cAAcM,IAAE,KAAK,OAAO,QAAOU,IAAEP,IAAEA,IAAEW,GAAE,UAASpB,GAAE,OAAO;AAAE,SAAK,iBAAiB,KAAK,EAAC,MAAK,MAAK,WAAUK,GAAC,CAAC,GAAE,KAAK,iBAAiB,SAAO,GAAE,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,MAAMF,IAAEH,IAAE;AAAC,UAAMM,KAAE,KAAK,yBAAwBC,KAAE,CAAC;AAAE,eAAUF,MAAKC,GAAE,KAAK,WAAW,CAAAC,GAAE,KAAK,GAAGF,GAAE,QAAQ;AAAE,UAAMI,KAAEH,GAAE,aAAaC,IAAEJ,KAAE,KAAK,KAAK,YAAWH,KAAE,KAAK,KAAK,YAAW,GAAEoB,GAAE,QAAQ;AAAE,SAAK,iBAAiB,KAAK,EAAC,MAAK,MAAK,WAAUX,GAAC,CAAC,GAAE,KAAK,iBAAiB,SAAO,GAAE,KAAK,eAAe;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,8BAA6B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,0BAAyB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,2BAA0B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,GAAG,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,MAAG,aAAY,KAAE,CAAC,CAAC,GAAE,GAAG,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,GAAG,WAAU,QAAO,MAAM,GAAE,KAAG,EAAE,CAACC,GAAE,sDAAsD,CAAC,GAAE,EAAE;;;ACA3sY,IAAMW,KAAE,EAAC,MAAK,KAAI,MAAK,IAAG;AAAE,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,iBAAe,MAAK,KAAK,8BAA4B,MAAK,KAAK,6BAA2B,MAAK,KAAK,cAAY,MAAK,KAAK,mBAAiB,CAAC,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,mBAAiB,MAAK,KAAK,eAAa;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,UAAK,EAAC,MAAKA,IAAE,cAAaC,IAAE,qBAAoBC,IAAE,cAAaC,IAAE,cAAaJ,GAAC,IAAE;AAAK,SAAK,mBAAiBE,GAAE,SAAQ,KAAK,iBAAe,IAAI,GAAE,EAAC,QAAOA,IAAE,MAAKD,IAAE,qBAAoBE,IAAE,cAAaC,GAAC,CAAC,GAAE,KAAK,8BAA4B,IAAIC,GAAE,EAAC,cAAaH,IAAE,MAAKD,GAAC,CAAC,GAAE,KAAK,6BAA2B,IAAII,GAAE,EAAC,cAAaL,GAAE,cAAa,MAAKA,GAAE,KAAI,CAAC,GAAE,KAAK,eAAe,mBAAmB,KAAK,gBAAgB,GAAE,KAAK,eAAe,mBAAmB,KAAK,gBAAgB,GAAE,KAAK,4BAA4B,mBAAmB,KAAK,gBAAgB,GAAE,KAAK,4BAA4B,mBAAmB,KAAK,gBAAgB,GAAE,KAAK,2BAA2B,mBAAmB,KAAK,gBAAgB,GAAE,KAAK,2BAA2B,mBAAmB,KAAK,gBAAgB;AAAE,UAAMM,KAAEC,GAAEL,GAAE,YAAY,GAAEM,KAAED,GAAEP,GAAE,aAAa,YAAY;AAAE,IAAAA,GAAE,KAAK,MAAM,QAAQ,CAAC,KAAK,0BAA0B,CAAC,GAAE,mBAAkBQ,MAAG,mBAAkBF,MAAG,KAAK,WAAW,CAACN,GAAE,KAAK,GAAG,YAAY,CAAAC,OAAG;AAAC,MAAAA,GAAE,QAAMF,GAAE,QAAM,KAAK,QAAQ,MAAI,KAAK,KAAK,GAAEE,GAAE,gBAAgB,IAAGA,GAAE,QAAMF,GAAE,QAAM,KAAK,QAAQ,MAAI,KAAK,KAAK,GAAEE,GAAE,gBAAgB;AAAA,IAAE,CAAE,GAAED,GAAE,KAAK,GAAG,SAAS,OAAMC,OAAG;AAAC,WAAK,4BAA4B,4BAA4B,GAAE,KAAK,2BAA2B,sBAAsB;AAAA,IAAC,CAAE,GAAED,GAAG,MAAIQ,GAAE,eAAgB,CAAAP,OAAG;AAJpoE;AAIqoE,MAAAK,GAAE,gBAAcC,GAAEN,EAAC,EAAE,IAAK,CAAC,EAAC,aAAYA,GAAC,GAAEC,QAAK,EAAC,aAAYD,IAAE,UAASM,GAAED,GAAE,aAAa,EAAEJ,EAAC,EAAE,SAAQ,EAAG,IAAE,UAAK,gBAAL,mBAAkB;AAAA,IAAS,CAAE,GAAEF,GAAG,MAAI,KAAK,4BAA4B,cAAe,CAAAC,OAAG;AAAC,WAAK,2BAA2B,mBAAmBA,EAAC,GAAE,KAAK,eAAaA;AAAA,IAAC,CAAE,GAAED,GAAG,MAAI,KAAK,2BAA2B,cAAe,CAAAC,OAAG;AAAC,WAAK,4BAA4B,mBAAmBA,EAAC,GAAE,KAAK,eAAaA;AAAA,IAAC,CAAE,CAAC,CAAC,GAAE,KAAK,WAAW,CAACA,GAAE,GAAG,YAAY,CAAAA,OAAG;AAAC,MAAAA,GAAE,QAAMF,GAAE,QAAM,KAAK,QAAQ,MAAI,KAAK,KAAK,GAAEE,GAAE,gBAAgB,IAAGA,GAAE,QAAMF,GAAE,QAAM,KAAK,QAAQ,MAAI,KAAK,KAAK,GAAEE,GAAE,gBAAgB;AAAA,IAAE,CAAE,GAAEA,GAAE,GAAG,SAAS,OAAMA,OAAG;AAAC,WAAK,2BAA2B,4BAA4B,GAAE,KAAK,4BAA4B,sBAAsB;AAAA,IAAC,CAAE,CAAC,CAAC,GAAEA,GAAE,MAAM,QAAQ,CAAC,KAAK,gBAAe,KAAK,2BAA2B,CAAC,GAAEA,GAAE,aAAW,KAAK,gBAAe,KAAK,cAAY,KAAK,gBAAeA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,UAAS;AAJngG;AAIogG,eAAK,mBAAL,mBAAqB,YAAU,UAAK,gCAAL,mBAAkC,WAAU,KAAK,iBAAe,MAAK,KAAK,8BAA4B,MAAK,KAAK,6BAA2B,MAAK,KAAK,cAAY,MAAK,KAAK,mBAAiB,MAAK,KAAK,mBAAiB;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,iBAAiB,SAAO;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,iBAAiB,SAAO;AAAA,EAAC;AAAA,EAAC,OAAM;AAJr2G;AAIs2G,QAAG,KAAK,iBAAiB,SAAO,GAAE;AAAC,YAAK,EAAC,MAAKA,IAAE,WAAUC,GAAC,IAAE,KAAK,iBAAiB,IAAI;AAAE,MAAAD,OAAI,KAAK,eAAaA,GAAE,QAAQ,GAAEC,GAAE,KAAK,GAAED,GAAE,eAAe,GAAE,KAAK,iBAAiB,KAAK,EAAC,MAAKA,IAAE,WAAUC,GAAC,CAAC,GAAE,KAAK,gBAAcD,QAAG,UAAK,gBAAL,mBAAkB;AAAA,IAAS;AAAA,EAAC;AAAA,EAAC,OAAM;AAJvmH;AAIwmH,QAAG,KAAK,iBAAiB,SAAO,GAAE;AAAC,YAAK,EAAC,MAAKA,IAAE,WAAUC,GAAC,IAAE,KAAK,iBAAiB,IAAI;AAAE,MAAAD,OAAI,KAAK,eAAaA,GAAE,QAAQ,GAAEC,GAAE,MAAM,GAAED,GAAE,eAAe,GAAE,KAAK,iBAAiB,KAAK,EAAC,MAAKA,IAAE,WAAUC,GAAC,CAAC,GAAE,KAAK,gBAAcD,QAAG,UAAK,gBAAL,mBAAkB;AAAA,IAAS;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,YAAY,QAAQ;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,YAAY,MAAM,GAAE,KAAK,2BAA2B,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAoB;AAAC,SAAK,KAAK,aAAW,KAAK,6BAA4B,KAAK,cAAY,KAAK,6BAA4B,KAAK,YAAY,QAAQ,GAAE,MAAM,KAAK,aAAa,KAAK,KAAK,GAAE,KAAK,aAAa,KAAK,aAAW,KAAK,4BAA2B,KAAK,mBAAiB,KAAK,4BAA4B,aAAa,SAAQ,KAAK,4BAA4B,aAAa,UAAQ,OAAI,KAAK;AAAA,EAAgB;AAAA,EAAC,sBAAqB;AAAC,SAAK,KAAK,aAAW,KAAK,gBAAe,KAAK,cAAY,KAAK,gBAAe,KAAK,YAAY,QAAQ,GAAE,KAAK,aAAa,KAAK,aAAW,MAAK,KAAK,4BAA4B,aAAa,UAAQ,KAAK;AAAA,EAAgB;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,MAAG,aAAY,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAACI,GAAE,mEAAmE,CAAC,GAAEJ,EAAC;", "names": ["t", "l", "y", "i", "E", "e", "t", "l", "i", "a", "u", "r", "s", "n", "g", "t", "o", "e", "u", "l", "r", "i", "n", "r", "t", "a", "r", "t", "i", "e", "c", "p", "l", "m", "h", "u", "_", "o", "v", "a", "n", "x", "d", "E", "y", "k", "e", "b", "l", "w", "t", "i", "o", "r", "s", "n", "I", "g", "E", "a", "s", "t", "e", "r", "i", "a", "n", "m", "r", "i", "t", "n", "s", "e", "o", "a", "v", "E", "l", "S", "E", "S", "y", "j", "t", "i", "s", "e", "w", "r", "n", "c", "h", "_", "d", "m", "g", "P", "M", "v", "p", "u", "a", "o", "f", "l", "e", "Z", "$", "t", "n", "r", "i", "s", "v", "o", "a", "A", "u", "h", "c", "g", "l", "y", "m", "w", "E", "d", "l", "o", "t", "i", "a", "w", "c", "e", "h"]}