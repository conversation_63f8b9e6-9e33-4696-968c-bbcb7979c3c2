{"version": 3, "sources": ["../../@arcgis/core/rest/query/executeTopFeaturesQuery.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{parseUrl as r}from\"../utils.js\";import{executeTopFeaturesQuery as o}from\"./operations/queryTopFeatures.js\";import t from\"../support/FeatureSet.js\";import e from\"../support/TopFeaturesQuery.js\";async function s(s,p,u,a){const m=r(s),i={...a},{data:f}=await o(m,e.from(p),u,i);return t.fromJSON(f)}export{s as executeTopFeaturesQuery};\n"], "mappings": ";;;;;;;;;;;;;;AAIwM,eAAe,EAAEA,IAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAEA,EAAC,GAAE,IAAE,EAAC,GAAG,EAAC,GAAE,EAAC,MAAKC,GAAC,IAAE,MAAM,EAAE,GAAE,EAAE,KAAK,CAAC,GAAE,GAAE,CAAC;AAAE,SAAO,EAAE,SAASA,EAAC;AAAC;", "names": ["s", "f"]}