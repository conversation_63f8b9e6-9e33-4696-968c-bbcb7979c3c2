{"version": 3, "sources": ["../../@arcgis/core/layers/GeoRSSLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import o from\"../config.js\";import r from\"../request.js\";import\"../symbols.js\";import{isSome as t}from\"../core/maybe.js\";import{MultiOriginJSONMixin as s}from\"../core/MultiOriginJSONSupport.js\";import{throwIfAbortError as i}from\"../core/promiseUtils.js\";import{getFilename as l}from\"../core/urlUtils.js\";import{property as p}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{reader as n}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as a}from\"../core/accessorSupport/decorators/subclass.js\";import m from\"../geometry/Extent.js\";import{isWGS84 as y}from\"../geometry/support/spatialReferenceUtils.js\";import c from\"./Layer.js\";import{BlendLayer as u}from\"./mixins/BlendLayer.js\";import{OperationalLayer as d}from\"./mixins/OperationalLayer.js\";import{PortalLayer as f}from\"./mixins/PortalLayer.js\";import{RefreshableLayer as h}from\"./mixins/RefreshableLayer.js\";import{ScaleRangeLayer as S}from\"./mixins/ScaleRangeLayer.js\";import{id as g,legendEnabled as j,url as b}from\"./support/commonProperties.js\";import v from\"../symbols/Symbol.js\";import x from\"../symbols/SimpleLineSymbol.js\";import C from\"../symbols/PictureMarkerSymbol.js\";import P from\"../symbols/SimpleMarkerSymbol.js\";import G from\"../symbols/SimpleFillSymbol.js\";const L=[\"atom\",\"xml\"],R={base:v,key:\"type\",typeMap:{\"simple-line\":x},errorContext:\"symbol\"},k={base:v,key:\"type\",typeMap:{\"picture-marker\":C,\"simple-marker\":P},errorContext:\"symbol\"},_={base:v,key:\"type\",typeMap:{\"simple-fill\":G},errorContext:\"symbol\"};let w=class extends(u(h(d(f(S(s(c))))))){constructor(...e){super(...e),this.description=null,this.fullExtent=null,this.legendEnabled=!0,this.lineSymbol=null,this.pointSymbol=null,this.polygonSymbol=null,this.operationalLayerType=\"GeoRSS\",this.url=null,this.type=\"geo-rss\"}normalizeCtorArgs(e,o){return\"string\"==typeof e?{url:e,...o}:e}readFeatureCollections(e,o){return o.featureCollection.layers.forEach((e=>{const o=e.layerDefinition.drawingInfo.renderer.symbol;o&&\"esriSFS\"===o.type&&o.outline?.style.includes(\"esriSFS\")&&(o.outline.style=\"esriSLSSolid\")})),o.featureCollection.layers}get hasPoints(){return this._hasGeometry(\"esriGeometryPoint\")}get hasPolylines(){return this._hasGeometry(\"esriGeometryPolyline\")}get hasPolygons(){return this._hasGeometry(\"esriGeometryPolygon\")}get title(){const e=this._get(\"title\");return e&&\"defaults\"!==this.originOf(\"title\")?e:this.url?l(this.url,L)||\"GeoRSS\":e||\"\"}set title(e){this._set(\"title\",e)}load(e){const o=t(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:[\"Map Service\",\"Feature Service\",\"Feature Collection\",\"Scene Service\"]},e).catch(i).then((()=>this._fetchService(o))).then((e=>{this.read(e,{origin:\"service\"})}))),Promise.resolve(this)}async hasDataChanged(){const e=await this._fetchService();return this.read(e,{origin:\"service\",ignoreDefaults:!0}),!0}async _fetchService(e){const t=this.spatialReference,{data:s}=await r(o.geoRSSServiceUrl,{query:{url:this.url,refresh:!!this.loaded||void 0,outSR:y(t)?void 0:t.wkid??JSON.stringify(t)},signal:e});return s}_hasGeometry(e){return this.featureCollections?.some((o=>o.featureSet?.geometryType===e&&o.featureSet.features?.length>0))??!1}};e([p()],w.prototype,\"description\",void 0),e([p()],w.prototype,\"featureCollections\",void 0),e([n(\"service\",\"featureCollections\",[\"featureCollection.layers\"])],w.prototype,\"readFeatureCollections\",null),e([p({type:m,json:{name:\"lookAtExtent\"}})],w.prototype,\"fullExtent\",void 0),e([p(g)],w.prototype,\"id\",void 0),e([p(j)],w.prototype,\"legendEnabled\",void 0),e([p({types:R,json:{write:!0}})],w.prototype,\"lineSymbol\",void 0),e([p({type:[\"show\",\"hide\"]})],w.prototype,\"listMode\",void 0),e([p({types:k,json:{write:!0}})],w.prototype,\"pointSymbol\",void 0),e([p({types:_,json:{write:!0}})],w.prototype,\"polygonSymbol\",void 0),e([p({type:[\"GeoRSS\"]})],w.prototype,\"operationalLayerType\",void 0),e([p(b)],w.prototype,\"url\",void 0),e([p({json:{origins:{service:{read:{source:\"name\",reader:e=>e||void 0}}}}})],w.prototype,\"title\",null),e([p({readOnly:!0,json:{read:!1},value:\"geo-rss\"})],w.prototype,\"type\",void 0),w=e([a(\"esri.layers.GeoRSSLayer\")],w);const F=w;export{F as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+1C,IAAM,IAAE,CAAC,QAAO,KAAK;AAArB,IAAuB,IAAE,EAAC,MAAKA,IAAE,KAAI,QAAO,SAAQ,EAAC,eAAc,EAAC,GAAE,cAAa,SAAQ;AAA3F,IAA6F,IAAE,EAAC,MAAKA,IAAE,KAAI,QAAO,SAAQ,EAAC,kBAAiBC,IAAE,iBAAgBC,GAAC,GAAE,cAAa,SAAQ;AAAtL,IAAwLC,KAAE,EAAC,MAAKH,IAAE,KAAI,QAAO,SAAQ,EAAC,eAAc,EAAC,GAAE,cAAa,SAAQ;AAAE,IAAII,KAAE,cAAc,EAAE,EAAEC,GAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,cAAY,MAAK,KAAK,aAAW,MAAK,KAAK,gBAAc,MAAG,KAAK,aAAW,MAAK,KAAK,cAAY,MAAK,KAAK,gBAAc,MAAK,KAAK,uBAAqB,UAAS,KAAK,MAAI,MAAK,KAAK,OAAK;AAAA,EAAS;AAAA,EAAC,kBAAkBA,IAAEC,IAAE;AAAC,WAAM,YAAU,OAAOD,KAAE,EAAC,KAAIA,IAAE,GAAGC,GAAC,IAAED;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAEC,IAAE;AAAC,WAAOA,GAAE,kBAAkB,OAAO,QAAS,CAAAD,OAAG;AAJt/D;AAIu/D,YAAMC,KAAED,GAAE,gBAAgB,YAAY,SAAS;AAAO,MAAAC,MAAG,cAAYA,GAAE,UAAM,KAAAA,GAAE,YAAF,mBAAW,MAAM,SAAS,gBAAaA,GAAE,QAAQ,QAAM;AAAA,IAAe,CAAE,GAAEA,GAAE,kBAAkB;AAAA,EAAM;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,aAAa,mBAAmB;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,aAAa,sBAAsB;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,aAAa,qBAAqB;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,UAAMD,KAAE,KAAK,KAAK,OAAO;AAAE,WAAOA,MAAG,eAAa,KAAK,SAAS,OAAO,IAAEA,KAAE,KAAK,MAAI,GAAE,KAAK,KAAI,CAAC,KAAG,WAASA,MAAG;AAAA,EAAE;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,KAAK,SAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC,IAAEA,GAAE,SAAO;AAAK,WAAO,KAAK,oBAAoB,KAAK,eAAe,EAAC,gBAAe,CAAC,eAAc,mBAAkB,sBAAqB,eAAe,EAAC,GAAEA,EAAC,EAAE,MAAM,CAAC,EAAE,KAAM,MAAI,KAAK,cAAcC,EAAC,CAAE,EAAE,KAAM,CAAAD,OAAG;AAAC,WAAK,KAAKA,IAAE,EAAC,QAAO,UAAS,CAAC;AAAA,IAAC,CAAE,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAgB;AAAC,UAAMA,KAAE,MAAM,KAAK,cAAc;AAAE,WAAO,KAAK,KAAKA,IAAE,EAAC,QAAO,WAAU,gBAAe,KAAE,CAAC,GAAE;AAAA,EAAE;AAAA,EAAC,MAAM,cAAcA,IAAE;AAAC,UAAME,KAAE,KAAK,kBAAiB,EAAC,MAAKC,GAAC,IAAE,MAAM,EAAE,EAAE,kBAAiB,EAAC,OAAM,EAAC,KAAI,KAAK,KAAI,SAAQ,CAAC,CAAC,KAAK,UAAQ,QAAO,OAAM,EAAED,EAAC,IAAE,SAAOA,GAAE,QAAM,KAAK,UAAUA,EAAC,EAAC,GAAE,QAAOF,GAAC,CAAC;AAAE,WAAOG;AAAA,EAAC;AAAA,EAAC,aAAaH,IAAE;AAJhoG;AAIioG,aAAO,UAAK,uBAAL,mBAAyB,KAAM,CAAAC,OAAC;AAJxqG,UAAAG,KAAA;AAI0qG,eAAAA,MAAAH,GAAE,eAAF,gBAAAG,IAAc,kBAAeJ,QAAG,KAAAC,GAAE,WAAW,aAAb,mBAAuB,UAAO;AAAA,WAAK;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,sBAAqB,CAAC,0BAA0B,CAAC,CAAC,GAAEA,GAAE,WAAU,0BAAyB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,IAAE,MAAK,EAAC,MAAK,eAAc,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMD,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAQ,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,QAAO,QAAO,CAAAE,OAAGA,MAAG,OAAM,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,GAAE,OAAM,UAAS,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,yBAAyB,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["a", "n", "y", "_", "w", "c", "e", "o", "t", "s", "_a"]}