{"version": 3, "sources": ["../../@arcgis/core/geometry/support/jsonUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as o}from\"../../core/maybe.js\";import r from\"../Extent.js\";import e from\"../Geometry.js\";import t from\"../Multipoint.js\";import i from\"../Point.js\";import n from\"../Polygon.js\";import m from\"../Polyline.js\";function u(o){return void 0!==o.xmin&&void 0!==o.ymin&&void 0!==o.xmax&&void 0!==o.ymax}function l(o){return void 0!==o.points}function s(o){return void 0!==o.x&&void 0!==o.y}function f(o){return void 0!==o.paths}function y(o){return void 0!==o.rings}function p(o){return void 0!==o.vertexAttributes}function v(p){return o(p)?null:p instanceof e?p:s(p)?i.fromJSON(p):f(p)?m.fromJSON(p):y(p)?n.fromJSON(p):l(p)?t.fromJSON(p):u(p)?r.fromJSON(p):null}function c(o){return o?s(o)?\"esriGeometryPoint\":f(o)?\"esriGeometryPolyline\":y(o)?\"esriGeometryPolygon\":u(o)?\"esriGeometryEnvelope\":l(o)?\"esriGeometryMultipoint\":null:null}const G={esriGeometryPoint:i,esriGeometryPolyline:m,esriGeometryPolygon:n,esriGeometryEnvelope:r,esriGeometryMultipoint:t};function d(o){return o&&G[o]||null}export{v as fromJSON,d as getGeometryType,c as getJsonType,u as isExtent,p as isMesh,l as isMultipoint,s as isPoint,y as isPolygon,f as isPolyline};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAI6N,SAASA,GAAE,GAAE;AAAC,SAAO,WAAS,EAAE,QAAM,WAAS,EAAE,QAAM,WAAS,EAAE,QAAM,WAAS,EAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,WAAS,EAAE;AAAM;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,WAAS,EAAE,KAAG,WAAS,EAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,WAAS,EAAE;AAAK;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,WAAS,EAAE;AAAK;AAAkD,SAASC,GAAEC,IAAE;AAAC,SAAO,EAAEA,EAAC,IAAE,OAAKA,cAAa,IAAEA,KAAE,EAAEA,EAAC,IAAE,EAAE,SAASA,EAAC,IAAE,EAAEA,EAAC,IAAE,EAAE,SAASA,EAAC,IAAE,EAAEA,EAAC,IAAE,EAAE,SAASA,EAAC,IAAE,EAAEA,EAAC,IAAE,EAAE,SAASA,EAAC,IAAEC,GAAED,EAAC,IAAEE,GAAE,SAASF,EAAC,IAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,IAAE,EAAE,CAAC,IAAE,sBAAoB,EAAE,CAAC,IAAE,yBAAuB,EAAE,CAAC,IAAE,wBAAsBC,GAAE,CAAC,IAAE,yBAAuB,EAAE,CAAC,IAAE,2BAAyB,OAAK;AAAI;AAAC,IAAM,IAAE,EAAC,mBAAkB,GAAE,sBAAqB,GAAE,qBAAoB,GAAE,sBAAqBC,IAAE,wBAAuB,EAAC;AAAE,SAAS,EAAE,GAAE;AAAC,SAAO,KAAG,EAAE,CAAC,KAAG;AAAI;", "names": ["u", "v", "p", "u", "w"]}