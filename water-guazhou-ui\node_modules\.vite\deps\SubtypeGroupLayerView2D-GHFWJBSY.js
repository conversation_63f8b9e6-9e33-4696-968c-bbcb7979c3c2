import {
  D
} from "./chunk-4VL2P762.js";
import "./chunk-3MBH7CQT.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-JJZTA23S.js";
import "./chunk-VXAO6YJP.js";
import "./chunk-BI4P4NAQ.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-6NIKJYUX.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-5JDQNIY4.js";
import "./chunk-NEPFZ7PM.js";
import "./chunk-HWB4LNSZ.js";
import "./chunk-JSZR3BUH.js";
import "./chunk-QKWIBVLD.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-22FAZXOH.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-RURSJOSG.js";
import "./chunk-77E52HT5.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-YBNKNHCD.js";
import {
  y
} from "./chunk-CIHGHHEZ.js";
import "./chunk-G3QAWKCD.js";
import "./chunk-IKGI4J4I.js";
import "./chunk-MNWHGD3K.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-WL6G2MRC.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-KE7SPCM7.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-VJW7RCN7.js";
import "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  h,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/SubtypeGroupLayerView2D.js
function l2(e2, r2) {
  return !e2.visible || 0 !== e2.minScale && r2 > e2.minScale || 0 !== e2.maxScale && r2 < e2.maxScale;
}
var o = class extends D {
  initialize() {
    this.addHandles([l(() => this.view.scale, () => this._update(), h)], "constructor");
  }
  isUpdating() {
    var _a;
    const e2 = this.layer.sublayers.some((e3) => null != e3.renderer), r2 = this._commandsQueue.updating, s = null != this._updatingRequiredFieldsPromise, i = !this._proxy || !this._proxy.isReady, t = this._pipelineIsUpdating, a2 = null == this.tileRenderer || ((_a = this.tileRenderer) == null ? void 0 : _a.updating), n = e2 && (r2 || s || i || t || a2);
    return has("esri-2d-log-updating") && console.log(`Updating FLV2D: ${n}
  -> hasRenderer ${e2}
  -> hasPendingCommand ${r2}
  -> updatingRequiredFields ${s}
  -> updatingProxy ${i}
  -> updatingPipeline ${t}
  -> updatingTileRenderer ${a2}
`), n;
  }
  _injectOverrides(e2) {
    let s = super._injectOverrides(e2);
    const i = this.view.scale, t = this.layer.sublayers.filter((e3) => l2(e3, i)).map((e3) => e3.subtypeCode);
    if (!t.length) return s;
    s = r(s) ? s : new y().toJSON();
    const n = `NOT ${this.layer.subtypeField} IN (${t.join(",")})`;
    return s.where = s.where ? `(${s.where}) AND (${n})` : n, s;
  }
  _setLayersForFeature(e2) {
    const r2 = this.layer.fieldsIndex.get(this.layer.subtypeField), s = e2.attributes[r2.name], i = this.layer.sublayers.find((e3) => e3.subtypeCode === s);
    e2.layer = e2.sourceLayer = i;
  }
  _createSchemaConfig() {
    const e2 = { subtypeField: this.layer.subtypeField, sublayers: Array.from(this.layer.sublayers).map((e3) => ({ featureReduction: null, geometryType: this.layer.geometryType, labelingInfo: e3.labelingInfo, labelsVisible: e3.labelsVisible, renderer: e3.renderer, subtypeCode: e3.subtypeCode, orderBy: null })) }, r2 = this.layer.sublayers.map((e3) => e3.subtypeCode).join(","), s = this.layer.sublayers.length ? `${this.layer.subtypeField} IN (${r2})` : "1=2";
    let i = this.layer.definitionExpression ? this.layer.definitionExpression + " AND " : "";
    return i += s, { ...super._createSchemaConfig(), ...e2, definitionExpression: i };
  }
};
o = e([a("esri.views.2d.layers.SubtypeGroupLayerView2D")], o);
var u = o;
export {
  u as default
};
//# sourceMappingURL=SubtypeGroupLayerView2D-GHFWJBSY.js.map
