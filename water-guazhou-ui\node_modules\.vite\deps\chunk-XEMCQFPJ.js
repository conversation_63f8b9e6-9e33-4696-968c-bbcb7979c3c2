import {
  i
} from "./chunk-T5TRCNG4.js";
import {
  n
} from "./chunk-YBNKNHCD.js";
import {
  t
} from "./chunk-JEANRG5Q.js";
import {
  a as a2
} from "./chunk-Q4VCSCSY.js";
import {
  b
} from "./chunk-VJW7RCN7.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/ExportImageParameters.js
var m = { visible: "visibleSublayers", definitionExpression: "layerDefs", labelingInfo: "hasDynamicLayers", labelsVisible: "hasDynamicLayers", opacity: "hasDynamicLayers", minScale: "visibleSublayers", maxScale: "visibleSublayers", renderer: "hasDynamicLayers", source: "hasDynamicLayers" };
var c = class extends a2(v) {
  constructor(e2) {
    super(e2), this.floors = null, this.scale = 0;
  }
  destroy() {
    this.layer = null;
  }
  get dynamicLayers() {
    if (!this.hasDynamicLayers) return null;
    const e2 = this.visibleSublayers.map((e3) => {
      const r2 = n(this.floors, e3);
      return e3.toExportImageJSON(r2);
    });
    return e2.length ? JSON.stringify(e2) : null;
  }
  get hasDynamicLayers() {
    return this.layer && i(this.visibleSublayers, this.layer.serviceSublayers, this.layer.gdbVersion);
  }
  set layer(e2) {
    this._get("layer") !== e2 && (this._set("layer", e2), this.handles.remove("layer"), e2 && this.handles.add([e2.allSublayers.on("change", () => this.notifyChange("visibleSublayers")), e2.on("sublayer-update", (e3) => this.notifyChange(m[e3.propertyName]))], "layer"));
  }
  get layers() {
    const e2 = this.visibleSublayers;
    return e2 ? e2.length ? "show:" + e2.map((e3) => e3.id).join(",") : "show:-1" : null;
  }
  get layerDefs() {
    var _a;
    const e2 = !!((_a = this.floors) == null ? void 0 : _a.length), r2 = this.visibleSublayers.filter((r3) => null != r3.definitionExpression || e2 && null != r3.floorInfo);
    return r2.length ? JSON.stringify(r2.reduce((e3, r3) => {
      const s = n(this.floors, r3), o = t(s, r3.definitionExpression);
      return r(o) && (e3[r3.id] = o), e3;
    }, {})) : null;
  }
  get version() {
    this.commitProperty("layers"), this.commitProperty("layerDefs"), this.commitProperty("dynamicLayers"), this.commitProperty("timeExtent");
    const e2 = this.layer;
    return e2 && (e2.commitProperty("dpi"), e2.commitProperty("imageFormat"), e2.commitProperty("imageTransparency"), e2.commitProperty("gdbVersion")), (this._get("version") || 0) + 1;
  }
  get visibleSublayers() {
    const e2 = [];
    if (!this.layer) return e2;
    const r2 = this.layer.sublayers, s = (r3) => {
      const t3 = this.scale, i2 = 0 === t3, o = 0 === r3.minScale || t3 <= r3.minScale, a3 = 0 === r3.maxScale || t3 >= r3.maxScale;
      r3.visible && (i2 || o && a3) && (r3.sublayers ? r3.sublayers.forEach(s) : e2.unshift(r3));
    };
    r2 && r2.forEach(s);
    const t2 = this._get("visibleSublayers");
    return !t2 || t2.length !== e2.length || t2.some((r3, s2) => e2[s2] !== r3) ? e2 : t2;
  }
  toJSON() {
    const e2 = this.layer;
    let r2 = { dpi: e2.dpi, format: e2.imageFormat, transparent: e2.imageTransparency, gdbVersion: e2.gdbVersion || null };
    return this.hasDynamicLayers && this.dynamicLayers ? r2.dynamicLayers = this.dynamicLayers : r2 = { ...r2, layers: this.layers, layerDefs: this.layerDefs }, r2;
  }
};
e([y({ readOnly: true })], c.prototype, "dynamicLayers", null), e([y()], c.prototype, "floors", void 0), e([y({ readOnly: true })], c.prototype, "hasDynamicLayers", null), e([y()], c.prototype, "layer", null), e([y({ readOnly: true })], c.prototype, "layers", null), e([y({ readOnly: true })], c.prototype, "layerDefs", null), e([y({ type: Number })], c.prototype, "scale", void 0), e([y(b)], c.prototype, "timeExtent", void 0), e([y({ readOnly: true })], c.prototype, "version", null), e([y({ readOnly: true })], c.prototype, "visibleSublayers", null), c = e([a("esri.layers.mixins.ExportImageParameters")], c);

export {
  c
};
//# sourceMappingURL=chunk-XEMCQFPJ.js.map
