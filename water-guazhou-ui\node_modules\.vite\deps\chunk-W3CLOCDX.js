import {
  K
} from "./chunk-554JGJWA.js";
import {
  c
} from "./chunk-6T5FEO66.js";
import {
  T
} from "./chunk-N7ADFPOO.js";
import {
  n
} from "./chunk-7THWOTCY.js";
import {
  v as v2
} from "./chunk-ZACBBT3Y.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  s as s2
} from "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-PEEUPDEG.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  v
} from "./chunk-HP475EI3.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/rest/support/QuantizationParameters.js
var n2;
var c2 = new s({ upperLeft: "upper-left", lowerLeft: "lower-left" });
var m = n2 = class extends l {
  constructor(e2) {
    super(e2), this.extent = null, this.mode = "view", this.originPosition = "upper-left", this.tolerance = 1;
  }
  clone() {
    return new n2(p({ extent: this.extent, mode: this.mode, originPosition: this.originPosition, tolerance: this.tolerance }));
  }
};
e([y({ type: w2, json: { write: { overridePolicy() {
  return { enabled: "view" === this.mode };
} } } })], m.prototype, "extent", void 0), e([y({ type: ["view", "edit"], json: { write: true } })], m.prototype, "mode", void 0), e([y({ type: String, json: { read: c2.read, write: c2.write } })], m.prototype, "originPosition", void 0), e([y({ type: Number, json: { write: { overridePolicy() {
  return { enabled: "view" === this.mode };
} } } })], m.prototype, "tolerance", void 0), m = n2 = e([a("esri.rest.support.QuantizationParameters")], m);
var a2 = m;

// node_modules/@arcgis/core/rest/support/StatisticDefinition.js
var c3;
var n3 = new s({ count: "count", sum: "sum", min: "min", max: "max", avg: "avg", stddev: "stddev", var: "var", exceedslimit: "exceedslimit", percentile_cont: "percentile-continuous", percentile_disc: "percentile-discrete", EnvelopeAggregate: "envelope-aggregate", CentroidAggregate: "centroid-aggregate", ConvexHullAggregate: "convex-hull-aggregate" });
var p2 = c3 = class extends l {
  constructor(t) {
    super(t), this.maxPointCount = void 0, this.maxRecordCount = void 0, this.maxVertexCount = void 0, this.onStatisticField = null, this.outStatisticFieldName = null, this.statisticType = null, this.statisticParameters = null;
  }
  writeStatisticParameters(t, e2) {
    "percentile-continuous" !== this.statisticType && "percentile-discrete" !== this.statisticType || (e2.statisticParameters = p(t));
  }
  clone() {
    return new c3({ maxPointCount: this.maxPointCount, maxRecordCount: this.maxRecordCount, maxVertexCount: this.maxVertexCount, onStatisticField: this.onStatisticField, outStatisticFieldName: this.outStatisticFieldName, statisticType: this.statisticType, statisticParameters: p(this.statisticParameters) });
  }
};
e([y({ type: Number, json: { write: true } })], p2.prototype, "maxPointCount", void 0), e([y({ type: Number, json: { write: true } })], p2.prototype, "maxRecordCount", void 0), e([y({ type: Number, json: { write: true } })], p2.prototype, "maxVertexCount", void 0), e([y({ type: String, json: { write: true } })], p2.prototype, "onStatisticField", void 0), e([y({ type: String, json: { write: true } })], p2.prototype, "outStatisticFieldName", void 0), e([y({ type: String, json: { read: { source: "statisticType", reader: n3.read }, write: { target: "statisticType", writer: n3.write } } })], p2.prototype, "statisticType", void 0), e([y({ type: Object })], p2.prototype, "statisticParameters", void 0), e([r2("statisticParameters")], p2.prototype, "writeStatisticParameters", null), p2 = c3 = e([a("esri.rest.support.StatisticDefinition")], p2);
var m2 = p2;

// node_modules/@arcgis/core/rest/support/Query.js
var v3;
var g = new s({ esriSpatialRelIntersects: "intersects", esriSpatialRelContains: "contains", esriSpatialRelCrosses: "crosses", esriSpatialRelDisjoint: "disjoint", esriSpatialRelEnvelopeIntersects: "envelope-intersects", esriSpatialRelIndexIntersects: "index-intersects", esriSpatialRelOverlaps: "overlaps", esriSpatialRelTouches: "touches", esriSpatialRelWithin: "within", esriSpatialRelRelation: "relation" });
var R = new s({ esriSRUnit_Meter: "meters", esriSRUnit_Kilometer: "kilometers", esriSRUnit_Foot: "feet", esriSRUnit_StatuteMile: "miles", esriSRUnit_NauticalMile: "nautical-miles", esriSRUnit_USNauticalMile: "us-nautical-miles" });
var b = v3 = class extends l {
  static from(t) {
    return v(v3, t);
  }
  constructor(t) {
    super(t), this.aggregateIds = null, this.cacheHint = void 0, this.compactGeometryEnabled = false, this.datumTransformation = null, this.defaultSpatialReferenceEnabled = false, this.distance = void 0, this.dynamicDataSource = void 0, this.formatOf3DObjects = null, this.fullText = null, this.gdbVersion = null, this.geometry = null, this.geometryPrecision = void 0, this.groupByFieldsForStatistics = null, this.having = null, this.historicMoment = null, this.maxAllowableOffset = void 0, this.maxRecordCountFactor = 1, this.multipatchOption = null, this.num = void 0, this.objectIds = null, this.orderByFields = null, this.outFields = null, this.outSpatialReference = null, this.outStatistics = null, this.parameterValues = null, this.pixelSize = null, this.quantizationParameters = null, this.rangeValues = null, this.relationParameter = null, this.resultType = null, this.returnCentroid = false, this.returnDistinctValues = false, this.returnExceededLimitFeatures = true, this.returnGeometry = false, this.returnQueryGeometry = false, this.returnM = void 0, this.returnZ = void 0, this.sourceSpatialReference = null, this.spatialRelationship = "intersects", this.start = void 0, this.sqlFormat = null, this.text = null, this.timeExtent = null, this.timeReferenceUnknownClient = false, this.units = null, this.where = null;
  }
  castDatumTransformation(t) {
    return "number" == typeof t || "object" == typeof t ? t : null;
  }
  writeHistoricMoment(t, e2) {
    e2.historicMoment = t && t.getTime();
  }
  writeParameterValues(t, e2) {
    if (t) {
      const r3 = {};
      for (const e3 in t) {
        const o2 = t[e3];
        Array.isArray(o2) ? r3[e3] = o2.map((t2) => t2 instanceof Date ? t2.getTime() : t2) : o2 instanceof Date ? r3[e3] = o2.getTime() : r3[e3] = o2;
      }
      e2.parameterValues = r3;
    }
  }
  writeStart(t, e2) {
    e2.resultOffset = this.start, e2.resultRecordCount = this.num || 10, e2.where = "1=1";
  }
  writeWhere(t, e2) {
    e2.where = t || "1=1";
  }
  clone() {
    return new v3(p({ aggregateIds: this.aggregateIds, cacheHint: this.cacheHint, compactGeometryEnabled: this.compactGeometryEnabled, datumTransformation: this.datumTransformation, defaultSpatialReferenceEnabled: this.defaultSpatialReferenceEnabled, distance: this.distance, fullText: this.fullText, gdbVersion: this.gdbVersion, geometry: this.geometry, geometryPrecision: this.geometryPrecision, groupByFieldsForStatistics: this.groupByFieldsForStatistics, having: this.having, historicMoment: r(this.historicMoment) ? new Date(this.historicMoment.getTime()) : null, maxAllowableOffset: this.maxAllowableOffset, maxRecordCountFactor: this.maxRecordCountFactor, multipatchOption: this.multipatchOption, num: this.num, objectIds: this.objectIds, orderByFields: this.orderByFields, outFields: this.outFields, outSpatialReference: this.outSpatialReference, outStatistics: this.outStatistics, parameterValues: this.parameterValues, pixelSize: this.pixelSize, quantizationParameters: this.quantizationParameters, rangeValues: this.rangeValues, relationParameter: this.relationParameter, resultType: this.resultType, returnDistinctValues: this.returnDistinctValues, returnGeometry: this.returnGeometry, returnCentroid: this.returnCentroid, returnExceededLimitFeatures: this.returnExceededLimitFeatures, returnQueryGeometry: this.returnQueryGeometry, returnM: this.returnM, returnZ: this.returnZ, dynamicDataSource: this.dynamicDataSource, sourceSpatialReference: this.sourceSpatialReference, spatialRelationship: this.spatialRelationship, start: this.start, sqlFormat: this.sqlFormat, text: this.text, timeExtent: this.timeExtent, timeReferenceUnknownClient: this.timeReferenceUnknownClient, units: this.units, where: this.where }));
  }
};
b.MAX_MAX_RECORD_COUNT_FACTOR = 5, e([y({ json: { write: true } })], b.prototype, "aggregateIds", void 0), e([y({ type: Boolean, json: { write: true } })], b.prototype, "cacheHint", void 0), e([y({ type: Boolean, json: { default: false, write: true } })], b.prototype, "compactGeometryEnabled", void 0), e([y({ json: { write: true } })], b.prototype, "datumTransformation", void 0), e([s2("datumTransformation")], b.prototype, "castDatumTransformation", null), e([y({ type: Boolean, json: { default: false, write: true } })], b.prototype, "defaultSpatialReferenceEnabled", void 0), e([y({ type: Number, json: { write: { overridePolicy: (t) => ({ enabled: t > 0 }) } } })], b.prototype, "distance", void 0), e([y({ type: K, json: { write: true } })], b.prototype, "dynamicDataSource", void 0), e([y({ type: String, json: { write: true } })], b.prototype, "formatOf3DObjects", void 0), e([y({ type: [c], json: { write: { enabled: true, overridePolicy() {
  return { enabled: r(this.fullText) && this.fullText.length > 0 };
} } } })], b.prototype, "fullText", void 0), e([y({ type: String, json: { write: true } })], b.prototype, "gdbVersion", void 0), e([y({ types: n, json: { read: v2, write: true } })], b.prototype, "geometry", void 0), e([y({ type: Number, json: { write: true } })], b.prototype, "geometryPrecision", void 0), e([y({ type: [String], json: { write: true } })], b.prototype, "groupByFieldsForStatistics", void 0), e([y({ type: String, json: { write: true } })], b.prototype, "having", void 0), e([y({ type: Date })], b.prototype, "historicMoment", void 0), e([r2("historicMoment")], b.prototype, "writeHistoricMoment", null), e([y({ type: Number, json: { write: true } })], b.prototype, "maxAllowableOffset", void 0), e([y({ type: Number, cast: (t) => t < 1 ? 1 : t > v3.MAX_MAX_RECORD_COUNT_FACTOR ? v3.MAX_MAX_RECORD_COUNT_FACTOR : t, json: { write: { overridePolicy: (t) => ({ enabled: t > 1 }) } } })], b.prototype, "maxRecordCountFactor", void 0), e([y({ type: ["xyFootprint"], json: { write: true } })], b.prototype, "multipatchOption", void 0), e([y({ type: Number, json: { read: { source: "resultRecordCount" } } })], b.prototype, "num", void 0), e([y({ json: { write: true } })], b.prototype, "objectIds", void 0), e([y({ type: [String], json: { write: true } })], b.prototype, "orderByFields", void 0), e([y({ type: [String], json: { write: true } })], b.prototype, "outFields", void 0), e([y({ type: f, json: { name: "outSR", write: true } })], b.prototype, "outSpatialReference", void 0), e([y({ type: [m2], json: { write: { enabled: true, overridePolicy() {
  return { enabled: r(this.outStatistics) && this.outStatistics.length > 0 };
} } } })], b.prototype, "outStatistics", void 0), e([y({ json: { write: true } })], b.prototype, "parameterValues", void 0), e([r2("parameterValues")], b.prototype, "writeParameterValues", null), e([y({ type: w, json: { write: true } })], b.prototype, "pixelSize", void 0), e([y({ type: a2, json: { write: true } })], b.prototype, "quantizationParameters", void 0), e([y({ type: [Object], json: { write: true } })], b.prototype, "rangeValues", void 0), e([y({ type: String, json: { read: { source: "relationParam" }, write: { target: "relationParam", overridePolicy() {
  return { enabled: "relation" === this.spatialRelationship };
} } } })], b.prototype, "relationParameter", void 0), e([y({ type: String, json: { write: true } })], b.prototype, "resultType", void 0), e([y({ type: Boolean, json: { default: false, write: true } })], b.prototype, "returnCentroid", void 0), e([y({ type: Boolean, json: { default: false, write: true } })], b.prototype, "returnDistinctValues", void 0), e([y({ type: Boolean, json: { default: true, write: true } })], b.prototype, "returnExceededLimitFeatures", void 0), e([y({ type: Boolean, json: { write: true } })], b.prototype, "returnGeometry", void 0), e([y({ type: Boolean, json: { default: false, write: true } })], b.prototype, "returnQueryGeometry", void 0), e([y({ type: Boolean, json: { default: false, write: true } })], b.prototype, "returnM", void 0), e([y({ type: Boolean, json: { write: { overridePolicy: (t) => ({ enabled: t }) } } })], b.prototype, "returnZ", void 0), e([y({ type: f, json: { write: true } })], b.prototype, "sourceSpatialReference", void 0), e([o(g, { ignoreUnknown: false, name: "spatialRel" })], b.prototype, "spatialRelationship", void 0), e([y({ type: Number, json: { read: { source: "resultOffset" } } })], b.prototype, "start", void 0), e([r2("start"), r2("num")], b.prototype, "writeStart", null), e([y({ type: String, json: { write: true } })], b.prototype, "sqlFormat", void 0), e([y({ type: String, json: { write: true } })], b.prototype, "text", void 0), e([y({ type: T, json: { write: true } })], b.prototype, "timeExtent", void 0), e([y({ type: Boolean, json: { default: false, write: true } })], b.prototype, "timeReferenceUnknownClient", void 0), e([o(R, { ignoreUnknown: false }), y({ json: { write: { overridePolicy(t) {
  return { enabled: !!t && null != this.distance && this.distance > 0 };
} } } })], b.prototype, "units", void 0), e([y({ type: String, json: { write: { overridePolicy(t) {
  return { enabled: null != t || null != this.start && this.start > 0 };
} } } })], b.prototype, "where", void 0), e([r2("where")], b.prototype, "writeWhere", null), b = v3 = e([a("esri.rest.support.Query")], b);
var x = b;

export {
  a2 as a,
  m2 as m,
  x
};
//# sourceMappingURL=chunk-W3CLOCDX.js.map
