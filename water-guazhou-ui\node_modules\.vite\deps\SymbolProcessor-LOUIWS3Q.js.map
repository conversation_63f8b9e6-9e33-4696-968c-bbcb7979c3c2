{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/features/support/ResourceManagerProxy.js", "../../@arcgis/core/views/2d/layers/features/processors/SymbolProcessor.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isAbortError as e}from\"../../../../../core/promiseUtils.js\";class t{constructor(e){this._remoteClient=e,this._resourceMap=new Map,this._inFlightResourceMap=new Map,this.geometryEngine=null,this.geometryEnginePromise=null}destroy(){}async fetchResource(t,r){const s=this._resourceMap,i=s.get(t);if(i)return i;let n=this._inFlightResourceMap.get(t);if(n)return n;try{n=this._remoteClient.invoke(\"tileRenderer.fetchResource\",{url:t},{...r}),this._inFlightResourceMap.set(t,n),n.then((e=>(this._inFlightResourceMap.delete(t),s.set(t,e),e)))}catch(o){return e(o)?null:{width:0,height:0}}return n}getResource(e){return this._resourceMap.get(e)??null}}export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import{bidiText as t}from\"../../../../../core/BidiText.js\";import\"../../../../../core/Error.js\";import\"../../../../../core/has.js\";import\"../../../../../core/Logger.js\";import{applySome as s,isNone as r,isSome as i,unwrapOrThrow as a,unwrap as o}from\"../../../../../core/maybe.js\";import{throwIfAborted as n,isAbortError as l}from\"../../../../../core/promiseUtils.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as c}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{diff as d,hasDiff as h}from\"../../../../../core/accessorSupport/diffUtils.js\";import f from\"../../../../../geometry/SpatialReference.js\";import{isAggregateId as m}from\"../../../engine/webgl/DisplayId.js\";import{WGLSymbologyType as u}from\"../../../engine/webgl/enums.js\";import{MeshData as g}from\"../../../engine/webgl/mesh/MeshData.js\";import{WGLMeshFactory as p}from\"../../../engine/webgl/mesh/factories/WGLMeshFactory.js\";import{WGLTemplateStore as y}from\"../../../engine/webgl/mesh/templates/WGLTemplateStore.js\";import{createMatcher as _}from\"../../../engine/webgl/util/Matcher.js\";import{codepoints as b}from\"../textUtils.js\";import S from\"./BaseProcessor.js\";import w from\"../support/ResourceManagerProxy.js\";function I(e,t){return(!e.minScale||e.minScale>=t)&&(!e.maxScale||e.maxScale<=t)}function v(e){const t=e.message,r={message:{data:{},tileKey:t.tileKey,tileKeyOrigin:t.tileKeyOrigin,version:t.version},transferList:new Array};for(const a in t.data){const e=t.data[a];if(r.message.data[a]=null,i(e)){const t=e.stride,i=e.indices.slice(0),o=e.vertices.slice(0),n=e.records.slice(0),l={stride:t,indices:i,vertices:o,records:n,metrics:s(e.metrics,(e=>e.slice(0)))};r.transferList.push(i,o,n),r.message.data[a]=l}}return r}let M=class extends S{constructor(){super(...arguments),this.type=\"symbol\",this._matchers={feature:null,aggregate:null},this._bufferData=new Map,this._bufferIds=new Map}initialize(){this.handles.add([this.tileStore.on(\"update\",this.onTileUpdate.bind(this))]),this._resourceManagerProxy=new w(this.remoteClient)}destroy(){this._resourceManagerProxy.destroy()}get supportsTileUpdates(){return!0}forEachBufferId(e){this._bufferIds.forEach((t=>{t.forEach(e)}))}async update(e,t){const s=t.schema.processors[0];if(\"symbol\"!==s.type)return;const r=d(this._schema,s);(h(r,\"mesh\")||h(r,\"target\"))&&(e.mesh=!0,e.why?.mesh.push(\"Symbology changed\"),this._schema=s,this._factory=this._createFactory(s),this._factory.update(s,this.tileStore.tileScheme.tileInfo))}onTileMessage(e,t,s,r){return n(r),this._onTileData(e,t,s,r)}onTileClear(e){const t={clear:!0};return this._bufferData.delete(e.key.id),this._bufferIds.delete(e.key.id),this.remoteClient.invoke(\"tileRenderer.onTileData\",{tileKey:e.id,data:t})}onTileError(e,t,s){const r=s.signal,i={tileKey:e.id,error:t};return this.remoteClient.invoke(\"tileRenderer.onTileError\",i,{signal:r})}onTileUpdate(e){for(const t of e.removed)this._bufferData.has(t.key.id)&&this._bufferData.delete(t.key.id),this._bufferIds.has(t.key.id)&&this._bufferIds.delete(t.key.id);for(const t of e.added)this._bufferData.forEach((e=>{for(const s of e)s.message.tileKey===t.id&&this._updateTileMesh(\"append\",t,v(s),[],!1,!1,null)}))}_addBufferData(e,t){this._bufferData.has(e)||this._bufferData.set(e,[]),this._bufferData.get(e)?.push(v(t))}_createFactory(e){const{geometryType:t,objectIdField:r,fields:i}=this.service,a=(e,t)=>this.remoteClient.invoke(\"tileRenderer.getMaterialItems\",e,t),o={geometryType:t,fields:i,spatialReference:f.fromJSON(this.spatialReference)},n=new y(a,this.tileStore.tileScheme.tileInfo),{matcher:l,aggregateMatcher:c}=e.mesh;return this._store=n,this._matchers.feature=_(l,n,o,this._resourceManagerProxy),this._matchers.aggregate=s(c,(e=>_(e,n,o,this._resourceManagerProxy))),new p(t,r,n)}async _onTileData(e,t,s,a){n(a);const{type:o,addOrUpdate:l,remove:c,clear:d,end:h}=t,f=!!this._schema.mesh.sortKey;if(!l){const t={type:o,addOrUpdate:null,remove:c,clear:d,end:h,sort:f};return this.remoteClient.invoke(\"tileRenderer.onTileData\",{tileKey:e.id,data:t},a)}const m=this._processFeatures(e,l,s,a,t.status?.version);try{const s=await m;if(r(s)){const t={type:o,addOrUpdate:null,remove:c,clear:d,end:h,sort:f};return this.remoteClient.invoke(\"tileRenderer.onTileData\",{tileKey:e.id,data:t},a)}const n=[];for(const t of s){let s=!1;const r=t.message.bufferIds,a=e.key.id,o=t.message.tileKey;if(a!==o&&i(r)){if(!this.tileStore.get(o)){this._addBufferData(a,t),n.push(t);continue}let e=this._bufferIds.get(o);e||(e=new Set,this._bufferIds.set(o,e));const i=Array.from(r);for(const t of i){if(e.has(t)){s=!0;break}e.add(t)}}s||(this._addBufferData(a,t),n.push(t))}await Promise.all(n.map((s=>{const r=e.key.id===s.message.tileKey,i=r?t.remove:[],n=r&&t.end;return this._updateTileMesh(o,e,s,i,n,!!t.clear,a.signal)})))}catch(u){this._handleError(e,u,a)}}async _updateTileMesh(e,t,r,i,a,l,c){const d=e,h=r.message.tileKey,f=!!this._schema.mesh.sortKey;h!==t.key.id&&(a=!1);const m=s(r,(e=>e.message)),u=s(r,(e=>e.transferList))||[],g={type:d,addOrUpdate:m,remove:i,clear:l,end:a,sort:f},p={transferList:o(u)||[],signal:c};return n(p),this.remoteClient.invoke(\"tileRenderer.onTileData\",{tileKey:h,data:g},p)}async _processFeatures(e,t,s,i,a){if(r(t)||!t.hasFeatures)return null;const o={transform:e.transform,hasZ:!1,hasM:!1},l=this._factory,c={viewingMode:\"\",scale:e.scale},d=await this._matchers.feature,h=await this._matchers.aggregate;n(i);const f=this._getLabelInfos(e,t);return await l.analyze(t.getCursor(),this._resourceManagerProxy,d,h,o,c),n(i),this._writeFeatureSet(e,t,o,f,l,s,a)}_writeFeatureSet(e,t,s,r,a,o,n){const l=t.getSize(),c=this._schema.mesh.matcher.symbologyType,d=new g(e.key.id,{features:l,records:l,metrics:0},c,o,c!==u.HEATMAP,n),h={viewingMode:\"\",scale:e.scale},f=t.getCursor();for(;f.next();)try{const t=f.getDisplayId(),o=i(r)?r.get(t):null;a.writeCursor(d,f,s,h,e.level,o,this._resourceManagerProxy)}catch(p){}const m=e.tileInfoView.tileInfo.isWrappable;return d.serialize(m)}_handleError(e,t,s){if(!l(t)){const r={tileKey:e.id,error:t.message};return this.remoteClient.invoke(\"tileRenderer.onTileError\",r,{signal:s.signal})}return Promise.resolve()}_getLabelingSchemaForScale(e){const t=this._schema.mesh.labels;if(r(t))return null;if(\"subtype\"===t.type){const s={type:\"subtype\",classes:{}};let r=!1;for(const i in t.classes){const a=t.classes[i].filter((t=>I(t,e.scale)));r=r||!!a.length,s.classes[i]=a}return r?s:null}const s=t.classes.filter((t=>I(t,e.scale)));return s.length?{type:\"simple\",classes:s}:null}_getLabels(e,t){if(\"subtype\"===t.type){const s=this.service.subtypeField,r=a(s,\"Expected to find subtype Field\"),i=e.readAttribute(r);return null==i?[]:t.classes[i]??[]}return t.classes}_getLabelInfos(e,s){const i=this._getLabelingSchemaForScale(e);if(r(i))return null;const a=new Map,o=s.getCursor();for(;o.next();){const e=o.getDisplayId(),s=[],r=m(e),n=r&&1!==o.readAttribute(\"cluster_count\")?\"aggregate\":\"feature\",l=this._getLabels(o,i);for(const i of l){if(i.target!==n)continue;const a=o.getStorage(),l=r&&\"feature\"===n?a.getComputedStringAtIndex(o.readAttribute(\"referenceId\"),i.fieldIndex):a.getComputedStringAtIndex(e,i.fieldIndex);if(!l)continue;const c=t(l.toString()),d=c[0],h=c[1];this._store.getMosaicItem(i.symbol,b(d)).then((e=>{s[i.index]={glyphs:e.glyphMosaicItems??[],rtl:h,index:i.index}}))}a.set(e,s)}return a}};M=e([c(\"esri.views.2d.layers.features.processors.SymbolProcessor\")],M);const T=M;export{T as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAImE,IAAMA,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,gBAAcA,IAAE,KAAK,eAAa,oBAAI,OAAI,KAAK,uBAAqB,oBAAI,OAAI,KAAK,iBAAe,MAAK,KAAK,wBAAsB;AAAA,EAAI;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcD,IAAEE,IAAE;AAAC,UAAM,IAAE,KAAK,cAAaC,KAAE,EAAE,IAAIH,EAAC;AAAE,QAAGG,GAAE,QAAOA;AAAE,QAAIC,KAAE,KAAK,qBAAqB,IAAIJ,EAAC;AAAE,QAAGI,GAAE,QAAOA;AAAE,QAAG;AAAC,MAAAA,KAAE,KAAK,cAAc,OAAO,8BAA6B,EAAC,KAAIJ,GAAC,GAAE,EAAC,GAAGE,GAAC,CAAC,GAAE,KAAK,qBAAqB,IAAIF,IAAEI,EAAC,GAAEA,GAAE,KAAM,CAAAH,QAAI,KAAK,qBAAqB,OAAOD,EAAC,GAAE,EAAE,IAAIA,IAAEC,EAAC,GAAEA,GAAG;AAAA,IAAC,SAAOI,IAAE;AAAC,aAAO,EAAEA,EAAC,IAAE,OAAK,EAAC,OAAM,GAAE,QAAO,EAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAE;AAAC,WAAO,KAAK,aAAa,IAAIA,EAAC,KAAG;AAAA,EAAI;AAAC;;;ACAwqB,SAAS,EAAEK,IAAEC,IAAE;AAAC,UAAO,CAACD,GAAE,YAAUA,GAAE,YAAUC,QAAK,CAACD,GAAE,YAAUA,GAAE,YAAUC;AAAE;AAAC,SAAS,EAAED,IAAE;AAAC,QAAMC,KAAED,GAAE,SAAQE,KAAE,EAAC,SAAQ,EAAC,MAAK,CAAC,GAAE,SAAQD,GAAE,SAAQ,eAAcA,GAAE,eAAc,SAAQA,GAAE,QAAO,GAAE,cAAa,IAAI,QAAK;AAAE,aAAUE,MAAKF,GAAE,MAAK;AAAC,UAAMD,KAAEC,GAAE,KAAKE,EAAC;AAAE,QAAGD,GAAE,QAAQ,KAAKC,EAAC,IAAE,MAAK,EAAEH,EAAC,GAAE;AAAC,YAAMC,KAAED,GAAE,QAAOI,KAAEJ,GAAE,QAAQ,MAAM,CAAC,GAAEK,KAAEL,GAAE,SAAS,MAAM,CAAC,GAAEM,KAAEN,GAAE,QAAQ,MAAM,CAAC,GAAE,IAAE,EAAC,QAAOC,IAAE,SAAQG,IAAE,UAASC,IAAE,SAAQC,IAAE,SAAQ,EAAEN,GAAE,SAAS,CAAAA,OAAGA,GAAE,MAAM,CAAC,CAAE,EAAC;AAAE,MAAAE,GAAE,aAAa,KAAKE,IAAEC,IAAEC,EAAC,GAAEJ,GAAE,QAAQ,KAAKC,EAAC,IAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,UAAS,KAAK,YAAU,EAAC,SAAQ,MAAK,WAAU,KAAI,GAAE,KAAK,cAAY,oBAAI,OAAI,KAAK,aAAW,oBAAI;AAAA,EAAG;AAAA,EAAC,aAAY;AAAC,SAAK,QAAQ,IAAI,CAAC,KAAK,UAAU,GAAG,UAAS,KAAK,aAAa,KAAK,IAAI,CAAC,CAAC,CAAC,GAAE,KAAK,wBAAsB,IAAID,GAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,sBAAsB,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,gBAAgBD,IAAE;AAAC,SAAK,WAAW,QAAS,CAAAC,OAAG;AAAC,MAAAA,GAAE,QAAQD,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOA,IAAEC,IAAE;AAJjxE;AAIkxE,UAAM,IAAEA,GAAE,OAAO,WAAW,CAAC;AAAE,QAAG,aAAW,EAAE,KAAK;AAAO,UAAMC,KAAE,EAAE,KAAK,SAAQ,CAAC;AAAE,KAACC,GAAED,IAAE,MAAM,KAAGC,GAAED,IAAE,QAAQ,OAAKF,GAAE,OAAK,OAAG,KAAAA,GAAE,QAAF,mBAAO,KAAK,KAAK,sBAAqB,KAAK,UAAQ,GAAE,KAAK,WAAS,KAAK,eAAe,CAAC,GAAE,KAAK,SAAS,OAAO,GAAE,KAAK,UAAU,WAAW,QAAQ;AAAA,EAAE;AAAA,EAAC,cAAcA,IAAEC,IAAE,GAAEC,IAAE;AAAC,WAAO,EAAEA,EAAC,GAAE,KAAK,YAAYF,IAAEC,IAAE,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAE;AAAC,UAAMC,KAAE,EAAC,OAAM,KAAE;AAAE,WAAO,KAAK,YAAY,OAAOD,GAAE,IAAI,EAAE,GAAE,KAAK,WAAW,OAAOA,GAAE,IAAI,EAAE,GAAE,KAAK,aAAa,OAAO,2BAA0B,EAAC,SAAQA,GAAE,IAAG,MAAKC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAEC,IAAE,GAAE;AAAC,UAAMC,KAAE,EAAE,QAAOE,KAAE,EAAC,SAAQJ,GAAE,IAAG,OAAMC,GAAC;AAAE,WAAO,KAAK,aAAa,OAAO,4BAA2BG,IAAE,EAAC,QAAOF,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaF,IAAE;AAAC,eAAUC,MAAKD,GAAE,QAAQ,MAAK,YAAY,IAAIC,GAAE,IAAI,EAAE,KAAG,KAAK,YAAY,OAAOA,GAAE,IAAI,EAAE,GAAE,KAAK,WAAW,IAAIA,GAAE,IAAI,EAAE,KAAG,KAAK,WAAW,OAAOA,GAAE,IAAI,EAAE;AAAE,eAAUA,MAAKD,GAAE,MAAM,MAAK,YAAY,QAAS,CAAAA,OAAG;AAAC,iBAAU,KAAKA,GAAE,GAAE,QAAQ,YAAUC,GAAE,MAAI,KAAK,gBAAgB,UAASA,IAAE,EAAE,CAAC,GAAE,CAAC,GAAE,OAAG,OAAG,IAAI;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAEC,IAAE;AAJpvG;AAIqvG,SAAK,YAAY,IAAID,EAAC,KAAG,KAAK,YAAY,IAAIA,IAAE,CAAC,CAAC,IAAE,UAAK,YAAY,IAAIA,EAAC,MAAtB,mBAAyB,KAAK,EAAEC,EAAC;AAAA,EAAE;AAAA,EAAC,eAAeD,IAAE;AAAC,UAAK,EAAC,cAAaC,IAAE,eAAcC,IAAE,QAAOE,GAAC,IAAE,KAAK,SAAQD,KAAE,CAACH,IAAEC,OAAI,KAAK,aAAa,OAAO,iCAAgCD,IAAEC,EAAC,GAAEI,KAAE,EAAC,cAAaJ,IAAE,QAAOG,IAAE,kBAAiBG,GAAE,SAAS,KAAK,gBAAgB,EAAC,GAAED,KAAE,IAAI,EAAEH,IAAE,KAAK,UAAU,WAAW,QAAQ,GAAE,EAAC,SAAQ,GAAE,kBAAiBK,GAAC,IAAER,GAAE;AAAK,WAAO,KAAK,SAAOM,IAAE,KAAK,UAAU,UAAQD,GAAE,GAAEC,IAAED,IAAE,KAAK,qBAAqB,GAAE,KAAK,UAAU,YAAU,EAAEG,IAAG,CAAAR,OAAGK,GAAEL,IAAEM,IAAED,IAAE,KAAK,qBAAqB,CAAE,GAAE,IAAIC,GAAEL,IAAEC,IAAEI,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYN,IAAEC,IAAE,GAAEE,IAAE;AAJn0H;AAIo0H,MAAEA,EAAC;AAAE,UAAK,EAAC,MAAKE,IAAE,aAAY,GAAE,QAAOG,IAAE,OAAM,GAAE,KAAI,EAAC,IAAEP,IAAEM,KAAE,CAAC,CAAC,KAAK,QAAQ,KAAK;AAAQ,QAAG,CAAC,GAAE;AAAC,YAAMN,KAAE,EAAC,MAAKI,IAAE,aAAY,MAAK,QAAOG,IAAE,OAAM,GAAE,KAAI,GAAE,MAAKD,GAAC;AAAE,aAAO,KAAK,aAAa,OAAO,2BAA0B,EAAC,SAAQP,GAAE,IAAG,MAAKC,GAAC,GAAEE,EAAC;AAAA,IAAC;AAAC,UAAMM,KAAE,KAAK,iBAAiBT,IAAE,GAAE,GAAEG,KAAE,KAAAF,GAAE,WAAF,mBAAU,OAAO;AAAE,QAAG;AAAC,YAAMS,KAAE,MAAMD;AAAE,UAAG,EAAEC,EAAC,GAAE;AAAC,cAAMT,KAAE,EAAC,MAAKI,IAAE,aAAY,MAAK,QAAOG,IAAE,OAAM,GAAE,KAAI,GAAE,MAAKD,GAAC;AAAE,eAAO,KAAK,aAAa,OAAO,2BAA0B,EAAC,SAAQP,GAAE,IAAG,MAAKC,GAAC,GAAEE,EAAC;AAAA,MAAC;AAAC,YAAMG,KAAE,CAAC;AAAE,iBAAUL,MAAKS,IAAE;AAAC,YAAIA,KAAE;AAAG,cAAMR,KAAED,GAAE,QAAQ,WAAUE,KAAEH,GAAE,IAAI,IAAGK,KAAEJ,GAAE,QAAQ;AAAQ,YAAGE,OAAIE,MAAG,EAAEH,EAAC,GAAE;AAAC,cAAG,CAAC,KAAK,UAAU,IAAIG,EAAC,GAAE;AAAC,iBAAK,eAAeF,IAAEF,EAAC,GAAEK,GAAE,KAAKL,EAAC;AAAE;AAAA,UAAQ;AAAC,cAAID,KAAE,KAAK,WAAW,IAAIK,EAAC;AAAE,UAAAL,OAAIA,KAAE,oBAAI,OAAI,KAAK,WAAW,IAAIK,IAAEL,EAAC;AAAG,gBAAMI,KAAE,MAAM,KAAKF,EAAC;AAAE,qBAAUD,MAAKG,IAAE;AAAC,gBAAGJ,GAAE,IAAIC,EAAC,GAAE;AAAC,cAAAS,KAAE;AAAG;AAAA,YAAK;AAAC,YAAAV,GAAE,IAAIC,EAAC;AAAA,UAAC;AAAA,QAAC;AAAC,QAAAS,OAAI,KAAK,eAAeP,IAAEF,EAAC,GAAEK,GAAE,KAAKL,EAAC;AAAA,MAAE;AAAC,YAAM,QAAQ,IAAIK,GAAE,IAAK,CAAAI,OAAG;AAAC,cAAMR,KAAEF,GAAE,IAAI,OAAKU,GAAE,QAAQ,SAAQN,KAAEF,KAAED,GAAE,SAAO,CAAC,GAAEK,KAAEJ,MAAGD,GAAE;AAAI,eAAO,KAAK,gBAAgBI,IAAEL,IAAEU,IAAEN,IAAEE,IAAE,CAAC,CAACL,GAAE,OAAME,GAAE,MAAM;AAAA,MAAC,CAAE,CAAC;AAAA,IAAC,SAAO,GAAE;AAAC,WAAK,aAAaH,IAAE,GAAEG,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBH,IAAEC,IAAEC,IAAEE,IAAED,IAAE,GAAEK,IAAE;AAAC,UAAM,IAAER,IAAE,IAAEE,GAAE,QAAQ,SAAQK,KAAE,CAAC,CAAC,KAAK,QAAQ,KAAK;AAAQ,UAAIN,GAAE,IAAI,OAAKE,KAAE;AAAI,UAAMM,KAAE,EAAEP,IAAG,CAAAF,OAAGA,GAAE,OAAQ,GAAE,IAAE,EAAEE,IAAG,CAAAF,OAAGA,GAAE,YAAa,KAAG,CAAC,GAAE,IAAE,EAAC,MAAK,GAAE,aAAYS,IAAE,QAAOL,IAAE,OAAM,GAAE,KAAID,IAAE,MAAKI,GAAC,GAAEI,KAAE,EAAC,cAAaX,GAAE,CAAC,KAAG,CAAC,GAAE,QAAOQ,GAAC;AAAE,WAAO,EAAEG,EAAC,GAAE,KAAK,aAAa,OAAO,2BAA0B,EAAC,SAAQ,GAAE,MAAK,EAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBX,IAAEC,IAAE,GAAEG,IAAED,IAAE;AAAC,QAAG,EAAEF,EAAC,KAAG,CAACA,GAAE,YAAY,QAAO;AAAK,UAAMI,KAAE,EAAC,WAAUL,GAAE,WAAU,MAAK,OAAG,MAAK,MAAE,GAAE,IAAE,KAAK,UAASQ,KAAE,EAAC,aAAY,IAAG,OAAMR,GAAE,MAAK,GAAE,IAAE,MAAM,KAAK,UAAU,SAAQ,IAAE,MAAM,KAAK,UAAU;AAAU,MAAEI,EAAC;AAAE,UAAMG,KAAE,KAAK,eAAeP,IAAEC,EAAC;AAAE,WAAO,MAAM,EAAE,QAAQA,GAAE,UAAU,GAAE,KAAK,uBAAsB,GAAE,GAAEI,IAAEG,EAAC,GAAE,EAAEJ,EAAC,GAAE,KAAK,iBAAiBJ,IAAEC,IAAEI,IAAEE,IAAE,GAAE,GAAEJ,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBH,IAAEC,IAAE,GAAEC,IAAEC,IAAEE,IAAEC,IAAE;AAAC,UAAM,IAAEL,GAAE,QAAQ,GAAEO,KAAE,KAAK,QAAQ,KAAK,QAAQ,eAAc,IAAE,IAAI,EAAER,GAAE,IAAI,IAAG,EAAC,UAAS,GAAE,SAAQ,GAAE,SAAQ,EAAC,GAAEQ,IAAEH,IAAEG,OAAI,EAAE,SAAQF,EAAC,GAAE,IAAE,EAAC,aAAY,IAAG,OAAMN,GAAE,MAAK,GAAEO,KAAEN,GAAE,UAAU;AAAE,WAAKM,GAAE,KAAK,IAAG,KAAG;AAAC,YAAMN,KAAEM,GAAE,aAAa,GAAEF,KAAE,EAAEH,EAAC,IAAEA,GAAE,IAAID,EAAC,IAAE;AAAK,MAAAE,GAAE,YAAY,GAAEI,IAAE,GAAE,GAAEP,GAAE,OAAMK,IAAE,KAAK,qBAAqB;AAAA,IAAC,SAAOM,IAAE;AAAA,IAAC;AAAC,UAAMF,KAAET,GAAE,aAAa,SAAS;AAAY,WAAO,EAAE,UAAUS,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaT,IAAEC,IAAE,GAAE;AAAC,QAAG,CAAC,EAAEA,EAAC,GAAE;AAAC,YAAMC,KAAE,EAAC,SAAQF,GAAE,IAAG,OAAMC,GAAE,QAAO;AAAE,aAAO,KAAK,aAAa,OAAO,4BAA2BC,IAAE,EAAC,QAAO,EAAE,OAAM,CAAC;AAAA,IAAC;AAAC,WAAO,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,2BAA2BF,IAAE;AAAC,UAAMC,KAAE,KAAK,QAAQ,KAAK;AAAO,QAAG,EAAEA,EAAC,EAAE,QAAO;AAAK,QAAG,cAAYA,GAAE,MAAK;AAAC,YAAMS,KAAE,EAAC,MAAK,WAAU,SAAQ,CAAC,EAAC;AAAE,UAAIR,KAAE;AAAG,iBAAUE,MAAKH,GAAE,SAAQ;AAAC,cAAME,KAAEF,GAAE,QAAQG,EAAC,EAAE,OAAQ,CAAAH,OAAG,EAAEA,IAAED,GAAE,KAAK,CAAE;AAAE,QAAAE,KAAEA,MAAG,CAAC,CAACC,GAAE,QAAOO,GAAE,QAAQN,EAAC,IAAED;AAAA,MAAC;AAAC,aAAOD,KAAEQ,KAAE;AAAA,IAAI;AAAC,UAAM,IAAET,GAAE,QAAQ,OAAQ,CAAAA,OAAG,EAAEA,IAAED,GAAE,KAAK,CAAE;AAAE,WAAO,EAAE,SAAO,EAAC,MAAK,UAAS,SAAQ,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,WAAWA,IAAEC,IAAE;AAAC,QAAG,cAAYA,GAAE,MAAK;AAAC,YAAM,IAAE,KAAK,QAAQ,cAAaC,KAAE,EAAE,GAAE,gCAAgC,GAAEE,KAAEJ,GAAE,cAAcE,EAAC;AAAE,aAAO,QAAME,KAAE,CAAC,IAAEH,GAAE,QAAQG,EAAC,KAAG,CAAC;AAAA,IAAC;AAAC,WAAOH,GAAE;AAAA,EAAO;AAAA,EAAC,eAAeD,IAAE,GAAE;AAAC,UAAMI,KAAE,KAAK,2BAA2BJ,EAAC;AAAE,QAAG,EAAEI,EAAC,EAAE,QAAO;AAAK,UAAMD,KAAE,oBAAI,OAAIE,KAAE,EAAE,UAAU;AAAE,WAAKA,GAAE,KAAK,KAAG;AAAC,YAAML,KAAEK,GAAE,aAAa,GAAEK,KAAE,CAAC,GAAER,KAAES,GAAEX,EAAC,GAAEM,KAAEJ,MAAG,MAAIG,GAAE,cAAc,eAAe,IAAE,cAAY,WAAU,IAAE,KAAK,WAAWA,IAAED,EAAC;AAAE,iBAAUA,MAAK,GAAE;AAAC,YAAGA,GAAE,WAASE,GAAE;AAAS,cAAMH,KAAEE,GAAE,WAAW,GAAEO,KAAEV,MAAG,cAAYI,KAAEH,GAAE,yBAAyBE,GAAE,cAAc,aAAa,GAAED,GAAE,UAAU,IAAED,GAAE,yBAAyBH,IAAEI,GAAE,UAAU;AAAE,YAAG,CAACQ,GAAE;AAAS,cAAMJ,KAAE,EAAEI,GAAE,SAAS,CAAC,GAAE,IAAEJ,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC;AAAE,aAAK,OAAO,cAAcJ,GAAE,QAAO,EAAE,CAAC,CAAC,EAAE,KAAM,CAAAJ,OAAG;AAAC,UAAAU,GAAEN,GAAE,KAAK,IAAE,EAAC,QAAOJ,GAAE,oBAAkB,CAAC,GAAE,KAAI,GAAE,OAAMI,GAAE,MAAK;AAAA,QAAC,CAAE;AAAA,MAAC;AAAC,MAAAD,GAAE,IAAIH,IAAEU,EAAC;AAAA,IAAC;AAAC,WAAOP;AAAA,EAAC;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,0DAA0D,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["t", "e", "r", "i", "n", "o", "e", "t", "r", "a", "i", "o", "n", "f", "c", "m", "s", "p", "l"]}