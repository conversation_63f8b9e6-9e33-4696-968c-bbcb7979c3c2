{"version": 3, "sources": ["../../@arcgis/core/geometry/geometryAdapters/hydrated.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../Extent.js\";import n from\"../Multipoint.js\";import t from\"../Point.js\";import o from\"../Polygon.js\";import i from\"../Polyline.js\";const r={convertToGEGeometry:s,exportPoint:a,exportPolygon:c,exportPolyline:h,exportMultipoint:m,exportExtent:x};function s(e,n){if(null==n)return null;let t=\"cache\"in n?n.cache._geVersion:void 0;return null==t&&(t=e.convertJSONToGeometry(n),\"cache\"in n&&(n.cache._geVersion=t)),t}function a(e,n,o){const i=e.hasZ(n),r=e.hasM(n),s=new t({x:e.getPointX(n),y:e.getPointY(n),spatialReference:o});return i&&(s.z=e.getPointZ(n)),r&&(s.m=e.getPointM(n)),s.cache._geVersion=n,s}function c(e,n,t){const i=new o({rings:e.exportPaths(n),hasZ:e.hasZ(n),hasM:e.hasM(n),spatialReference:t});return i.cache._geVersion=n,i}function h(e,n,t){const o=new i({paths:e.exportPaths(n),hasZ:e.hasZ(n),hasM:e.hasM(n),spatialReference:t});return o.cache._geVersion=n,o}function m(e,t,o){const i=new n({hasZ:e.hasZ(t),hasM:e.hasM(t),points:e.exportPoints(t),spatialReference:o});return i.cache._geVersion=t,i}function x(n,t,o){const i=n.hasZ(t),r=n.hasM(t),s=new e({xmin:n.getXMin(t),ymin:n.getYMin(t),xmax:n.getXMax(t),ymax:n.getYMax(t),spatialReference:o});if(i){const e=n.getZExtent(t);s.zmin=e.vmin,s.zmax=e.vmax}if(r){const e=n.getMExtent(t);s.mmin=e.vmin,s.mmax=e.vmax}return s.cache._geVersion=t,s}export{r as hydratedAdapter};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAIkJ,IAAM,IAAE,EAAC,qBAAoB,GAAE,aAAY,GAAE,eAAc,GAAE,gBAAe,GAAE,kBAAiBA,IAAE,cAAa,EAAC;AAAE,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,QAAM,EAAE,QAAO;AAAK,MAAI,IAAE,WAAU,IAAE,EAAE,MAAM,aAAW;AAAO,SAAO,QAAM,MAAI,IAAE,EAAE,sBAAsB,CAAC,GAAE,WAAU,MAAI,EAAE,MAAM,aAAW,KAAI;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAE,KAAK,CAAC,GAAEC,KAAE,EAAE,KAAK,CAAC,GAAEC,KAAE,IAAI,EAAE,EAAC,GAAE,EAAE,UAAU,CAAC,GAAE,GAAE,EAAE,UAAU,CAAC,GAAE,kBAAiB,EAAC,CAAC;AAAE,SAAO,MAAIA,GAAE,IAAE,EAAE,UAAU,CAAC,IAAGD,OAAIC,GAAE,IAAE,EAAE,UAAU,CAAC,IAAGA,GAAE,MAAM,aAAW,GAAEA;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,IAAI,EAAE,EAAC,OAAM,EAAE,YAAY,CAAC,GAAE,MAAK,EAAE,KAAK,CAAC,GAAE,MAAK,EAAE,KAAK,CAAC,GAAE,kBAAiB,EAAC,CAAC;AAAE,SAAO,EAAE,MAAM,aAAW,GAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,IAAI,EAAE,EAAC,OAAM,EAAE,YAAY,CAAC,GAAE,MAAK,EAAE,KAAK,CAAC,GAAE,MAAK,EAAE,KAAK,CAAC,GAAE,kBAAiB,EAAC,CAAC;AAAE,SAAO,EAAE,MAAM,aAAW,GAAE;AAAC;AAAC,SAASF,GAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,IAAI,EAAE,EAAC,MAAK,EAAE,KAAK,CAAC,GAAE,MAAK,EAAE,KAAK,CAAC,GAAE,QAAO,EAAE,aAAa,CAAC,GAAE,kBAAiB,EAAC,CAAC;AAAE,SAAO,EAAE,MAAM,aAAW,GAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAE,KAAK,CAAC,GAAEC,KAAE,EAAE,KAAK,CAAC,GAAEC,KAAE,IAAIC,GAAE,EAAC,MAAK,EAAE,QAAQ,CAAC,GAAE,MAAK,EAAE,QAAQ,CAAC,GAAE,MAAK,EAAE,QAAQ,CAAC,GAAE,MAAK,EAAE,QAAQ,CAAC,GAAE,kBAAiB,EAAC,CAAC;AAAE,MAAG,GAAE;AAAC,UAAM,IAAE,EAAE,WAAW,CAAC;AAAE,IAAAD,GAAE,OAAK,EAAE,MAAKA,GAAE,OAAK,EAAE;AAAA,EAAI;AAAC,MAAGD,IAAE;AAAC,UAAM,IAAE,EAAE,WAAW,CAAC;AAAE,IAAAC,GAAE,OAAK,EAAE,MAAKA,GAAE,OAAK,EAAE;AAAA,EAAI;AAAC,SAAOA,GAAE,MAAM,aAAW,GAAEA;AAAC;", "names": ["m", "r", "s", "w"]}