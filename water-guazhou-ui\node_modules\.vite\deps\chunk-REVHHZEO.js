import {
  c,
  u as u2
} from "./chunk-6OFWBRK2.js";
import {
  p as p2,
  s
} from "./chunk-WC4DQSYX.js";
import {
  e,
  t as t2
} from "./chunk-YFVPK4WM.js";
import {
  DateTime
} from "./chunk-3HW44BD3.js";
import {
  y
} from "./chunk-NZB6EMKN.js";
import {
  l
} from "./chunk-5GX2JMCX.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  u
} from "./chunk-3WCHZJQK.js";
import {
  h,
  v
} from "./chunk-X7FOCGBC.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  p,
  w
} from "./chunk-63M4K32A.js";
import {
  C
} from "./chunk-EKX3LLYN.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/arcade/ImmutableArray.js
var t3 = class {
  constructor(t4 = []) {
    this._elements = t4;
  }
  length() {
    return this._elements.length;
  }
  get(t4) {
    return this._elements[t4];
  }
  toArray() {
    const t4 = [];
    for (let e3 = 0; e3 < this.length(); e3++) t4.push(this.get(e3));
    return t4;
  }
};

// node_modules/@arcgis/core/arcade/FunctionWrapper.js
var r = class {
  constructor() {
  }
};
function n(t4, n3, e3) {
  if (t4 instanceof r && !(t4 instanceof s2)) {
    const r2 = new s2();
    return r2.fn = t4, r2.parameterEvaluator = e3, r2.context = n3, r2;
  }
  return t4;
}
var e2 = class extends r {
  constructor(t4) {
    super(), this.fn = t4;
  }
  createFunction(t4) {
    return (...r2) => this.fn(t4, { preparsed: true, arguments: r2 });
  }
  call(t4, r2) {
    return this.fn(t4, r2);
  }
  marshalledCall(e3, a2, l3, c3) {
    return c3(e3, a2, (a3, o2, i3) => {
      i3 = i3.map((t4) => t4 instanceof r && !(t4 instanceof s2) ? n(t4, e3, c3) : t4);
      const u4 = this.call(l3, { args: i3 });
      return C(u4) ? u4.then((t4) => n(t4, l3, c3)) : u4;
    });
  }
};
var s2 = class extends r {
  constructor() {
    super(...arguments), this.fn = null, this.context = null;
  }
  createFunction(t4) {
    return this.fn.createFunction(this.context);
  }
  call(t4, r2) {
    return this.fn.marshalledCall(t4, r2, this.context, this.parameterEvaluator);
  }
  marshalledCall(t4, r2, n3) {
    return this.fn.marshalledCall(t4, r2, this.context, this.parameterEvaluator);
  }
};

// node_modules/@arcgis/core/arcade/featureset/support/shared.js
var i;
var n2;
function o(e3) {
  return y.fromJSON(e3.toJSON());
}
function l2(e3) {
  return e3.toJSON ? e3.toJSON() : e3;
}
function s3(e3) {
  return "string" == typeof e3 || e3 instanceof String;
}
function u3(e3) {
  return "number" == typeof e3;
}
function c2(e3) {
  return e3 instanceof Date;
}
function a(r2) {
  return r2 instanceof c;
}
function m2(e3, r2) {
  return e3 === r2 || !(!c2(e3) && !a(e3) || !c2(r2) && !a(r2)) && e3.getTime() === r2.getTime();
}
function d(e3) {
  if (null == e3) return null;
  if ("number" == typeof e3) return e3;
  switch (e3.toLowerCase()) {
    case "meters":
    case "meter":
      return 109404;
    case "miles":
    case "mile":
      return 109439;
    case "kilometers":
    case "kilometer":
    case "km":
      return 109414;
  }
  return null;
}
function g(e3) {
  if (null == e3) return null;
  switch (e3.type) {
    case "polygon":
    case "multipoint":
    case "polyline":
      return e3.extent;
    case "point":
      return new w2({ xmin: e3.x, ymin: e3.y, xmax: e3.x, ymax: e3.y, spatialReference: e3.spatialReference });
    case "extent":
      return e3;
  }
  return null;
}
function F(e3) {
  if (null == e3) return null;
  if ("number" == typeof e3) return e3;
  if ("number" == typeof e3) return e3;
  switch (e3.toLowerCase()) {
    case "meters":
    case "meter":
      return 9001;
    case "miles":
    case "mile":
      return 9093;
    case "kilometers":
    case "kilometer":
    case "km":
      return 9036;
  }
  return null;
}
!function(e3) {
  e3[e3.Standardised = 0] = "Standardised", e3[e3.StandardisedNoInterval = 1] = "StandardisedNoInterval", e3[e3.SqlServer = 2] = "SqlServer", e3[e3.Oracle = 3] = "Oracle", e3[e3.Postgres = 4] = "Postgres", e3[e3.PGDB = 5] = "PGDB", e3[e3.FILEGDB = 6] = "FILEGDB", e3[e3.NotEvaluated = 7] = "NotEvaluated";
}(i || (i = {})), function(e3) {
  e3[e3.InFeatureSet = 0] = "InFeatureSet", e3[e3.NotInFeatureSet = 1] = "NotInFeatureSet", e3[e3.Unknown = 2] = "Unknown";
}(n2 || (n2 = {}));
var T = 1e3;
var P = { point: "point", polygon: "polygon", polyline: "polyline", multipoint: "multipoint", extent: "extent", esriGeometryPoint: "point", esriGeometryPolygon: "polygon", esriGeometryPolyline: "polyline", esriGeometryMultipoint: "multipoint", esriGeometryEnvelope: "extent", envelope: "extent" };
var I = { point: "esriGeometryPoint", polygon: "esriGeometryPolygon", polyline: "esriGeometryPolyline", multipoint: "esriGeometryMultipoint", extent: "esriGeometryEnvelope", esriGeometryPoint: "esriGeometryPoint", esriGeometryPolygon: "esriGeometryPolygon", esriGeometryPolyline: "esriGeometryPolyline", esriGeometryMultipoint: "esriGeometryMultipoint", esriGeometryEnvelope: "esriGeometryEnvelope", envelope: "esriGeometryEnvelope" };
var b = { "small-integer": "esriFieldTypeSmallInteger", integer: "esriFieldTypeInteger", long: "esriFieldTypeLong", single: "esriFieldTypeSingle", double: "esriFieldTypeDouble", string: "esriFieldTypeString", date: "esriFieldTypeDate", oid: "esriFieldTypeOID", geometry: "esriFieldTypeGeometry", blob: "esriFieldTypeBlob", raster: "esriFieldTypeRaster", guid: "esriFieldTypeGUID", "global-id": "esriFieldTypeGlobalID", xml: "eesriFieldTypeXML", esriFieldTypeSmallInteger: "esriFieldTypeSmallInteger", esriFieldTypeInteger: "esriFieldTypeInteger", esriFieldTypeLong: "esriFieldTypeLong", esriFieldTypeSingle: "esriFieldTypeSingle", esriFieldTypeDouble: "esriFieldTypeDouble", esriFieldTypeString: "esriFieldTypeString", esriFieldTypeDate: "esriFieldTypeDate", esriFieldTypeOID: "esriFieldTypeOID", esriFieldTypeGeometry: "esriFieldTypeGeometry", esriFieldTypeBlob: "esriFieldTypeBlob", esriFieldTypeRaster: "esriFieldTypeRaster", esriFieldTypeGUID: "esriFieldTypeGUID", esriFieldTypeGlobalID: "esriFieldTypeGlobalID", esriFieldTypeXML: "eesriFieldTypeXML" };
function x(e3) {
  return void 0 === e3 ? "" : e3 = (e3 = (e3 = e3.replace(/\/featureserver\/[0-9]*/i, "/FeatureServer")).replace(/\/mapserver\/[0-9]*/i, "/MapServer")).split("?")[0];
}
function N(e3, r2) {
  r2 || (r2 = {}), "function" == typeof r2 && (r2 = { cmp: r2 });
  const t4 = "boolean" == typeof r2.cycles && r2.cycles, i3 = r2.cmp && (n3 = r2.cmp, function(e4) {
    return function(r3, t5) {
      const i4 = { key: r3, value: e4[r3] }, o3 = { key: t5, value: e4[t5] };
      return n3(i4, o3);
    };
  });
  var n3;
  const o2 = [];
  return function e4(r3) {
    if (r3 && r3.toJSON && "function" == typeof r3.toJSON && (r3 = r3.toJSON()), void 0 === r3) return;
    if ("number" == typeof r3) return isFinite(r3) ? "" + r3 : "null";
    if ("object" != typeof r3) return JSON.stringify(r3);
    let n4, l3;
    if (Array.isArray(r3)) {
      for (l3 = "[", n4 = 0; n4 < r3.length; n4++) n4 && (l3 += ","), l3 += e4(r3[n4]) || "null";
      return l3 + "]";
    }
    if (null === r3) return "null";
    if (o2.includes(r3)) {
      if (t4) return JSON.stringify("__cycle__");
      throw new TypeError("Converting circular structure to JSON");
    }
    const s5 = o2.push(r3) - 1, y2 = Object.keys(r3).sort(i3 && i3(r3));
    for (l3 = "", n4 = 0; n4 < y2.length; n4++) {
      const t5 = y2[n4], i4 = e4(r3[t5]);
      i4 && (l3 && (l3 += ","), l3 += JSON.stringify(t5) + ":" + i4);
    }
    return o2.splice(s5, 1), "{" + l3 + "}";
  }(e3);
}

// node_modules/@arcgis/core/arcade/ArcadeModule.js
var s4 = class {
  constructor(s5) {
    this.source = s5;
  }
};

// node_modules/@arcgis/core/arcade/ImmutablePointArray.js
var i2 = class _i extends t3 {
  constructor(t4, s5, i3, e3, h3, a2) {
    super(t4), this._lazyPt = [], this._hasZ = false, this._hasM = false, this._spRef = s5, this._hasZ = i3, this._hasM = e3, this._cacheId = h3, this._partId = a2;
  }
  get(t4) {
    if (void 0 === this._lazyPt[t4]) {
      const i3 = this._elements[t4];
      if (void 0 === i3) return;
      const e3 = this._hasZ, h3 = this._hasM;
      let a2 = null;
      a2 = e3 && !h3 ? new w(i3[0], i3[1], i3[2], void 0, this._spRef) : h3 && !e3 ? new w(i3[0], i3[1], void 0, i3[2], this._spRef) : e3 && h3 ? new w(i3[0], i3[1], i3[2], i3[3], this._spRef) : new w(i3[0], i3[1], this._spRef), a2.cache._arcadeCacheId = this._cacheId.toString() + "-" + this._partId.toString() + "-" + t4.toString(), this._lazyPt[t4] = a2;
    }
    return this._lazyPt[t4];
  }
  equalityTest(t4) {
    return t4 === this || null !== t4 && (t4 instanceof _i != false && t4.getUniqueHash() === this.getUniqueHash());
  }
  getUniqueHash() {
    return this._cacheId.toString() + "-" + this._partId.toString();
  }
};

// node_modules/@arcgis/core/arcade/ImmutablePathArray.js
var h2 = class _h extends t3 {
  constructor(t4, s5, h3, i3, e3) {
    super(t4), this._lazyPath = [], this._hasZ = false, this._hasM = false, this._hasZ = h3, this._hasM = i3, this._spRef = s5, this._cacheId = e3;
  }
  get(t4) {
    if (void 0 === this._lazyPath[t4]) {
      const h3 = this._elements[t4];
      if (void 0 === h3) return;
      this._lazyPath[t4] = new i2(h3, this._spRef, this._hasZ, this._hasM, this._cacheId, t4);
    }
    return this._lazyPath[t4];
  }
  equalityTest(t4) {
    return t4 === this || null !== t4 && (t4 instanceof _h != false && t4.getUniqueHash() === this.getUniqueHash());
  }
  getUniqueHash() {
    return this._cacheId.toString();
  }
};

// node_modules/@arcgis/core/chunks/languageUtils.js
var M = class {
  constructor(e3) {
    this.value = e3;
  }
};
var k = class {
  constructor(e3) {
    this.value = e3;
  }
};
var j = k;
var A = M;
var R = { type: "VOID" };
var D = { type: "BREAK" };
var F2 = { type: "CONTINUE" };
function I2(e3, n3, t4) {
  return "" === n3 || null == n3 || n3 === t4 || n3 === t4 ? e3 : e3 = e3.split(n3).join(t4);
}
function C2(n3) {
  return n3 instanceof r;
}
function w3(e3) {
  return e3 instanceof s4;
}
function O(e3) {
  return !!v2(e3) || (!!Y(e3) || (!!U(e3) || (!!L(e3) || (null === e3 || (e3 === R || "number" == typeof e3)))));
}
function Z(e3, n3) {
  return void 0 === e3 ? n3 : e3;
}
function _(e3) {
  return null == e3 ? "" : J(e3) || V(e3) ? "Array" : U(e3) ? "Date" : v2(e3) ? "String" : L(e3) ? "Boolean" : Y(e3) ? "Number" : "esri.arcade.Attachment" === (e3 == null ? void 0 : e3.declaredClass) ? "Attachment" : "esri.arcade.Portal" === (e3 == null ? void 0 : e3.declaredClass) ? "Portal" : "esri.arcade.Dictionary" === (e3 == null ? void 0 : e3.declaredClass) ? "Dictionary" : e3 instanceof s4 ? "Module" : z(e3) ? "Feature" : e3 instanceof w ? "Point" : e3 instanceof v ? "Polygon" : e3 instanceof m ? "Polyline" : e3 instanceof u ? "Multipoint" : e3 instanceof w2 ? "Extent" : C2(e3) ? "Function" : G(e3) ? "FeatureSet" : E(e3) ? "FeatureSetCollection" : e3 === R ? "" : "number" == typeof e3 && isNaN(e3) ? "Number" : "Unrecognised Type";
}
function v2(e3) {
  return "string" == typeof e3 || e3 instanceof String;
}
function L(e3) {
  return "boolean" == typeof e3;
}
function Y(e3) {
  return "number" == typeof e3;
}
function P2(e3) {
  return "number" == typeof e3 && isFinite(e3) && Math.floor(e3) === e3;
}
function J(e3) {
  return e3 instanceof Array;
}
function z(e3) {
  return "esri.arcade.Feature" === (e3 == null ? void 0 : e3.arcadeDeclaredClass);
}
function G(e3) {
  return "esri.arcade.featureset.support.FeatureSet" === (e3 == null ? void 0 : e3.declaredRootClass);
}
function E(e3) {
  return "esri.arcade.featureSetCollection" === (e3 == null ? void 0 : e3.declaredRootClass);
}
function V(e3) {
  return e3 instanceof t3;
}
function U(e3) {
  return e3 instanceof c;
}
function H(e3) {
  return null != e3 && "object" == typeof e3;
}
function q(e3) {
  return e3 instanceof Date;
}
function B(e3, n3, t4, r2, a2) {
  if (e3.length < n3 || e3.length > t4) throw new t2(r2, e.WrongNumberOfParameters, a2);
}
function W(e3) {
  return e3 < 0 ? -Math.round(-e3) : Math.round(e3);
}
function $() {
  let e3 = Date.now();
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (n3) => {
    const t4 = (e3 + 16 * Math.random()) % 16 | 0;
    return e3 = Math.floor(e3 / 16), ("x" === n3 ? t4 : 3 & t4 | 8).toString(16);
  });
}
function Q(e3, n3) {
  return isNaN(e3) || null == n3 || "" === n3 ? e3.toString() : (n3 = I2(n3, "‰", ""), n3 = I2(n3, "¤", ""), s(e3, { pattern: n3 }));
}
function X(e3, n3) {
  return null == n3 || "" === n3 ? e3.toISOString(true) : e3.toFormat(K(n3), { locale: l(), numberingSystem: "latn" });
}
function K(e3) {
  e3 = e3.replace(/LTS|LT|LL?L?L?|l{1,4}/g, "[$&]");
  let n3 = "";
  const t4 = /(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;
  for (const r2 of e3.match(t4) || []) switch (r2) {
    case "D":
      n3 += "d";
      break;
    case "DD":
      n3 += "dd";
      break;
    case "DDD":
      n3 += "o";
      break;
    case "d":
      n3 += "c";
      break;
    case "ddd":
      n3 += "ccc";
      break;
    case "dddd":
      n3 += "cccc";
      break;
    case "M":
      n3 += "L";
      break;
    case "MM":
      n3 += "LL";
      break;
    case "MMM":
      n3 += "LLL";
      break;
    case "MMMM":
      n3 += "LLLL";
      break;
    case "YY":
      n3 += "yy";
      break;
    case "Y":
    case "YYYY":
      n3 += "yyyy";
      break;
    case "Q":
      n3 += "q";
      break;
    case "Z":
      n3 += "ZZ";
      break;
    case "ZZ":
      n3 += "ZZZ";
      break;
    case "S":
      n3 += "'S'";
      break;
    case "SS":
      n3 += "'SS'";
      break;
    case "SSS":
      n3 += "u";
      break;
    case "A":
    case "a":
      n3 += "a";
      break;
    case "m":
    case "mm":
    case "h":
    case "hh":
    case "H":
    case "HH":
    case "s":
    case "ss":
    case "X":
    case "x":
      n3 += r2;
      break;
    default:
      r2.length >= 2 && "[" === r2.slice(0, 1) && "]" === r2.slice(-1) ? n3 += `'${r2.slice(1, -1)}'` : n3 += `'${r2}'`;
  }
  return n3;
}
function ee(e3, n3, t4) {
  switch (t4) {
    case ">":
      return e3 > n3;
    case "<":
      return e3 < n3;
    case ">=":
      return e3 >= n3;
    case "<=":
      return e3 <= n3;
  }
  return false;
}
function ne(e3, n3, t4) {
  if (null === e3) {
    if (null === n3 || n3 === R) return ee(null, null, t4);
    if (Y(n3)) return ee(0, n3, t4);
    if (v2(n3)) return ee(0, le(n3), t4);
    if (L(n3)) return ee(0, le(n3), t4);
    if (U(n3)) return ee(0, n3.toNumber(), t4);
  }
  if (e3 === R) {
    if (null === n3 || n3 === R) return ee(null, null, t4);
    if (Y(n3)) return ee(0, n3, t4);
    if (v2(n3)) return ee(0, le(n3), t4);
    if (L(n3)) return ee(0, le(n3), t4);
    if (U(n3)) return ee(0, n3.toNumber(), t4);
  } else if (Y(e3)) {
    if (Y(n3)) return ee(e3, n3, t4);
    if (L(n3)) return ee(e3, le(n3), t4);
    if (null === n3 || n3 === R) return ee(e3, 0, t4);
    if (v2(n3)) return ee(e3, le(n3), t4);
    if (U(n3)) return ee(e3, n3.toNumber(), t4);
  } else if (v2(e3)) {
    if (v2(n3)) return ee(re(e3), re(n3), t4);
    if (U(n3)) return ee(le(e3), n3.toNumber(), t4);
    if (Y(n3)) return ee(le(e3), n3, t4);
    if (null === n3 || n3 === R) return ee(le(e3), 0, t4);
    if (L(n3)) return ee(le(e3), le(n3), t4);
  } else if (U(e3)) {
    if (U(n3)) return ee(e3.toNumber(), n3.toNumber(), t4);
    if (null === n3 || n3 === R) return ee(e3.toNumber(), 0, t4);
    if (Y(n3)) return ee(e3.toNumber(), n3, t4);
    if (L(n3)) return ee(e3.toNumber(), le(n3), t4);
    if (v2(n3)) return ee(e3.toNumber(), le(n3), t4);
  } else if (L(e3)) {
    if (L(n3)) return ee(e3, n3, t4);
    if (Y(n3)) return ee(le(e3), le(n3), t4);
    if (U(n3)) return ee(le(e3), n3.toNumber(), t4);
    if (null === n3 || n3 === R) return ee(le(e3), 0, t4);
    if (v2(n3)) return ee(le(e3), le(n3), t4);
  }
  return !!te(e3, n3) && ("<=" === t4 || ">=" === t4);
}
function te(e3, n3) {
  if (e3 === n3) return true;
  if (null === e3 && n3 === R || null === n3 && e3 === R) return true;
  if (U(e3) && U(n3)) return e3.equals(n3);
  if (e3 instanceof h2) return e3.equalityTest(n3);
  if (e3 instanceof i2) return e3.equalityTest(n3);
  if (e3 instanceof w && n3 instanceof w) {
    const t4 = e3.cache._arcadeCacheId, r2 = n3.cache._arcadeCacheId;
    if (null != t4) return t4 === r2;
  }
  if (H(e3) && H(n3)) {
    if (e3._arcadeCacheId === n3._arcadeCacheId && void 0 !== e3._arcadeCacheId && null !== e3._arcadeCacheId) return true;
    if (e3._underlyingGraphic === n3._underlyingGraphic && void 0 !== e3._underlyingGraphic && null !== e3._underlyingGraphic) return true;
  }
  return false;
}
function re(e3, t4) {
  if (v2(e3)) return e3;
  if (null === e3) return "";
  if (Y(e3)) return Q(e3, t4);
  if (L(e3)) return e3.toString();
  if (U(e3)) return X(e3, t4);
  if (e3 instanceof p) return JSON.stringify(e3.toJSON());
  if (J(e3)) {
    const n3 = [];
    for (let t5 = 0; t5 < e3.length; t5++) n3[t5] = oe(e3[t5]);
    return "[" + n3.join(",") + "]";
  }
  if (e3 instanceof t3) {
    const n3 = [];
    for (let t5 = 0; t5 < e3.length(); t5++) n3[t5] = oe(e3.get(t5));
    return "[" + n3.join(",") + "]";
  }
  return null !== e3 && "object" == typeof e3 && void 0 !== e3.castToText ? e3.castToText() : C2(e3) ? "object, Function" : e3 === R ? "" : w3(e3) ? "object, Module" : "";
}
function ae(e3) {
  const t4 = [];
  if (!J(e3)) return null;
  if (e3 instanceof t3) {
    for (let n3 = 0; n3 < e3.length(); n3++) t4[n3] = le(e3.get(n3));
    return t4;
  }
  for (let n3 = 0; n3 < e3.length; n3++) t4[n3] = le(e3[n3]);
  return t4;
}
function ie(e3, t4, r2 = false) {
  if (v2(e3)) return e3;
  if (null === e3) return "";
  if (Y(e3)) return Q(e3, t4);
  if (L(e3)) return e3.toString();
  if (U(e3)) return X(e3, t4);
  if (e3 instanceof p) return e3 instanceof w2 ? '{"xmin":' + e3.xmin.toString() + ',"ymin":' + e3.ymin.toString() + "," + (e3.hasZ ? '"zmin":' + e3.zmin.toString() + "," : "") + (e3.hasM ? '"mmin":' + e3.mmin.toString() + "," : "") + '"xmax":' + e3.xmax.toString() + ',"ymax":' + e3.ymax.toString() + "," + (e3.hasZ ? '"zmax":' + e3.zmax.toString() + "," : "") + (e3.hasM ? '"mmax":' + e3.mmax.toString() + "," : "") + '"spatialReference":' + ge(e3.spatialReference) + "}" : ge(e3.toJSON(), (e4, n3) => e4.key === n3.key ? 0 : "spatialReference" === e4.key ? 1 : "spatialReference" === n3.key || e4.key < n3.key ? -1 : e4.key > n3.key ? 1 : 0);
  if (J(e3)) {
    const n3 = [];
    for (let t5 = 0; t5 < e3.length; t5++) n3[t5] = oe(e3[t5], r2);
    return "[" + n3.join(",") + "]";
  }
  if (e3 instanceof t3) {
    const n3 = [];
    for (let t5 = 0; t5 < e3.length(); t5++) n3[t5] = oe(e3.get(t5), r2);
    return "[" + n3.join(",") + "]";
  }
  return null !== e3 && "object" == typeof e3 && void 0 !== e3.castToText ? e3.castToText(r2) : C2(e3) ? "object, Function" : e3 === R ? "" : w3(e3) ? "object, Module" : "";
}
function oe(e3, t4 = false) {
  if (null === e3) return "null";
  if (L(e3) || Y(e3) || v2(e3)) return JSON.stringify(e3);
  if (e3 instanceof p) return ie(e3, null, t4);
  if (e3 instanceof t3) return ie(e3, null, t4);
  if (e3 instanceof Array) return ie(e3, null, t4);
  if (U(e3)) return t4 ? JSON.stringify(e3.getTime()) : JSON.stringify(X(e3, ""));
  if (null !== e3 && "object" == typeof e3) {
    if (void 0 !== e3.castToText) return e3.castToText(t4);
  } else if (e3 === R) return "null";
  return "null";
}
function le(e3, n3) {
  return Y(e3) ? e3 : null === e3 || "" === e3 ? 0 : U(e3) ? NaN : L(e3) ? e3 ? 1 : 0 : J(e3) || "" === e3 || void 0 === e3 ? NaN : void 0 !== n3 && v2(e3) ? (n3 = I2(n3, "‰", ""), n3 = I2(n3, "¤", ""), p2(e3, { pattern: n3 })) : e3 === R ? 0 : Number(e3);
}
function ue(e3, n3) {
  if (U(e3)) return e3;
  if (v2(e3)) {
    const t4 = se(e3, n3);
    if (t4) return c.dateTimeToArcadeDate(t4);
  }
  return null;
}
function se(e3, n3) {
  const t4 = / (\d\d)/, r2 = u2(n3);
  let a2 = DateTime.fromISO(e3, { zone: r2 });
  return a2.isValid || t4.test(e3) && (e3 = e3.replace(t4, "T$1"), a2 = DateTime.fromISO(e3, { zone: n3 }), a2.isValid) ? a2 : null;
}
function fe(e3) {
  return L(e3) ? e3 : v2(e3) ? "true" === (e3 = e3.toLowerCase()) : !!Y(e3) && (0 !== e3 && !isNaN(e3));
}
function ce(e3, n3) {
  return t(e3) ? null : (null !== e3.spatialReference && void 0 !== e3.spatialReference || (e3.spatialReference = n3), e3);
}
function me(e3) {
  if (null === e3) return null;
  if (e3 instanceof w) return "NaN" === e3.x || null === e3.x || isNaN(e3.x) ? null : e3;
  if (e3 instanceof v) {
    if (0 === e3.rings.length) return null;
    for (const n3 of e3.rings) if (n3.length > 0) return e3;
    return null;
  }
  if (e3 instanceof m) {
    if (0 === e3.paths.length) return null;
    for (const n3 of e3.paths) if (n3.length > 0) return e3;
    return null;
  }
  return e3 instanceof u ? 0 === e3.points.length ? null : e3 : e3 instanceof w2 ? "NaN" === e3.xmin || null === e3.xmin || isNaN(e3.xmin) ? null : e3 : null;
}
function de(e3, n3) {
  if (!e3) return n3;
  if (!e3.domain) return n3;
  let t4 = null;
  if ("string" === e3.field.type || "esriFieldTypeString" === e3.field.type) n3 = re(n3);
  else {
    if (null == n3) return null;
    if ("" === n3) return n3;
    n3 = le(n3);
  }
  for (let r2 = 0; r2 < e3.domain.codedValues.length; r2++) {
    const a2 = e3.domain.codedValues[r2];
    a2.code === n3 && (t4 = a2);
  }
  return null === t4 ? n3 : t4.name;
}
function ye(e3, n3) {
  if (!e3) return n3;
  if (!e3.domain) return n3;
  let t4 = null;
  n3 = re(n3);
  for (let r2 = 0; r2 < e3.domain.codedValues.length; r2++) {
    const a2 = e3.domain.codedValues[r2];
    a2.name === n3 && (t4 = a2);
  }
  return null === t4 ? n3 : t4.code;
}
function pe(e3, n3, t4 = null, r2 = null) {
  if (!n3) return null;
  if (!n3.fields) return null;
  let a2, i3, o2 = null;
  for (let l3 = 0; l3 < n3.fields.length; l3++) {
    const t5 = n3.fields[l3];
    t5.name.toLowerCase() === e3.toString().toLowerCase() && (o2 = t5);
  }
  if (null === o2) throw new t2(null, e.FieldNotFound, null, { key: e3 });
  return null === r2 && t4 && n3.typeIdField && (r2 = t4.hasField(n3.typeIdField) ? t4.field(n3.typeIdField) : null), null != r2 && n3.types.some((e4) => e4.id === r2 && (a2 = e4.domains && e4.domains[o2.name], a2 && "inherited" === a2.type && (a2 = he(o2.name, n3), i3 = true), true)), i3 || a2 || (a2 = he(e3, n3)), { field: o2, domain: a2 };
}
function he(e3, n3) {
  let t4;
  return n3.fields.some((n4) => (n4.name.toLowerCase() === e3.toLowerCase() && (t4 = n4.domain), !!t4)), t4;
}
function ge(e3, n3) {
  n3 || (n3 = {}), "function" == typeof n3 && (n3 = { cmp: n3 });
  const t4 = "boolean" == typeof n3.cycles && n3.cycles, r2 = n3.cmp && (a2 = n3.cmp, function(e4) {
    return function(n4, t5) {
      const r3 = { key: n4, value: e4[n4] }, i4 = { key: t5, value: e4[t5] };
      return a2(r3, i4);
    };
  });
  var a2;
  const i3 = [];
  return function e4(n4) {
    if (n4 && n4.toJSON && "function" == typeof n4.toJSON && (n4 = n4.toJSON()), void 0 === n4) return;
    if ("number" == typeof n4) return isFinite(n4) ? "" + n4 : "null";
    if ("object" != typeof n4) return JSON.stringify(n4);
    let a3, o2;
    if (Array.isArray(n4)) {
      for (o2 = "[", a3 = 0; a3 < n4.length; a3++) a3 && (o2 += ","), o2 += e4(n4[a3]) || "null";
      return o2 + "]";
    }
    if (null === n4) return "null";
    if (i3.includes(n4)) {
      if (t4) return JSON.stringify("__cycle__");
      throw new TypeError("Converting circular structure to JSON");
    }
    const l3 = i3.push(n4) - 1, u4 = Object.keys(n4).sort(r2 && r2(n4));
    for (o2 = "", a3 = 0; a3 < u4.length; a3++) {
      const t5 = u4[a3], r3 = e4(n4[t5]);
      r3 && (o2 && (o2 += ","), o2 += JSON.stringify(t5) + ":" + r3);
    }
    return i3.splice(l3, 1), "{" + o2 + "}";
  }(e3);
}
function be(e3) {
  if (null === e3) return null;
  const n3 = [];
  for (const t4 of e3) t4 && t4.arcadeDeclaredClass && "esri.arcade.Feature" === t4.arcadeDeclaredClass ? n3.push(t4.geometry()) : n3.push(t4);
  return n3;
}
function xe(e3, n3) {
  if (!(n3 instanceof w)) throw new t2(null, e.InvalidParameter, null);
  e3.push(n3.hasZ ? n3.hasM ? [n3.x, n3.y, n3.z, n3.m] : [n3.x, n3.y, n3.z] : [n3.x, n3.y]);
}
function Se(e3, n3) {
  if (J(e3) || V(e3)) {
    let t4 = false, a2 = false, i3 = [], o2 = n3;
    if (J(e3)) {
      for (const n4 of e3) xe(i3, n4);
      i3.length > 0 && (o2 = e3[0].spatialReference, t4 = e3[0].hasZ, a2 = e3[0].hasM);
    } else if (e3 instanceof i2) i3 = e3._elements, i3.length > 0 && (t4 = e3._hasZ, a2 = e3._hasM, o2 = e3.get(0).spatialReference);
    else {
      if (!V(e3)) throw new t2(null, e.InvalidParameter, null);
      for (const n4 of e3.toArray()) xe(i3, n4);
      i3.length > 0 && (o2 = e3.get(0).spatialReference, t4 = true === e3.get(0).hasZ, a2 = true === e3.get(0).hasM);
    }
    if (0 === i3.length) return null;
    return h(i3, a2, t4) || (i3 = i3.slice(0).reverse()), new v({ rings: [i3], spatialReference: o2, hasZ: t4, hasM: a2 });
  }
  return e3;
}
function Ne(e3, n3) {
  if (J(e3) || V(e3)) {
    let t4 = false, a2 = false, i3 = [], o2 = n3;
    if (J(e3)) {
      for (const n4 of e3) xe(i3, n4);
      i3.length > 0 && (o2 = e3[0].spatialReference, t4 = true === e3[0].hasZ, a2 = true === e3[0].hasM);
    } else if (e3 instanceof i2) i3 = e3._elements, i3.length > 0 && (t4 = e3._hasZ, a2 = e3._hasM, o2 = e3.get(0).spatialReference);
    else if (V(e3)) {
      for (const n4 of e3.toArray()) xe(i3, n4);
      i3.length > 0 && (o2 = e3.get(0).spatialReference, t4 = true === e3.get(0).hasZ, a2 = true === e3.get(0).hasM);
    }
    return 0 === i3.length ? null : new m({ paths: [i3], spatialReference: o2, hasZ: t4, hasM: a2 });
  }
  return e3;
}
function Te(e3, n3) {
  if (J(e3) || V(e3)) {
    let t4 = false, a2 = false, i3 = [], o2 = n3;
    if (J(e3)) {
      for (const n4 of e3) xe(i3, n4);
      i3.length > 0 && (o2 = e3[0].spatialReference, t4 = true === e3[0].hasZ, a2 = true === e3[0].hasM);
    } else if (e3 instanceof i2) i3 = e3._elements, i3.length > 0 && (t4 = e3._hasZ, a2 = e3._hasM, o2 = e3.get(0).spatialReference);
    else if (V(e3)) {
      for (const n4 of e3.toArray()) xe(i3, n4);
      i3.length > 0 && (o2 = e3.get(0).spatialReference, t4 = true === e3.get(0).hasZ, a2 = true === e3.get(0).hasM);
    }
    return 0 === i3.length ? null : new u({ points: i3, spatialReference: o2, hasZ: t4, hasM: a2 });
  }
  return e3;
}
function Me(e3, t4 = false) {
  const r2 = [];
  if (null === e3) return r2;
  if (true === J(e3)) {
    for (let n3 = 0; n3 < e3.length; n3++) {
      const a2 = re(e3[n3]);
      "" === a2 && true !== t4 || r2.push(a2);
    }
    return r2;
  }
  if (e3 instanceof t3) {
    for (let n3 = 0; n3 < e3.length(); n3++) {
      const a2 = re(e3.get(n3));
      "" === a2 && true !== t4 || r2.push(a2);
    }
    return r2;
  }
  if (O(e3)) {
    const n3 = re(e3);
    return "" === n3 && true !== t4 || r2.push(n3), r2;
  }
  return [];
}
var ke = 0;
function je(e3) {
  return ke++, ke % 100 == 0 ? (ke = 0, new Promise((n3) => {
    setTimeout(() => {
      n3(e3);
    }, 0);
  })) : e3;
}
function Ae(e3, n3, t4) {
  switch (t4) {
    case "&":
      return e3 & n3;
    case "|":
      return e3 | n3;
    case "^":
      return e3 ^ n3;
    case "<<":
      return e3 << n3;
    case ">>":
      return e3 >> n3;
    case ">>>":
      return e3 >>> n3;
  }
}
function Re(e3, t4 = null) {
  return null == e3 ? null : L(e3) || Y(e3) || v2(e3) ? e3 : e3 instanceof p ? true === (t4 == null ? void 0 : t4.keepGeometryType) ? e3 : e3.toJSON() : e3 instanceof t3 ? e3.toArray().map((e4) => Re(e4, t4)) : e3 instanceof Array ? e3.map((e4) => Re(e4, t4)) : q(e3) ? e3 : U(e3) ? e3.toJSDate() : null !== e3 && "object" == typeof e3 && void 0 !== e3.castAsJson ? e3.castAsJson(t4) : null;
}
async function De(e3, n3, t4, r2, a2) {
  const i3 = await Fe(e3, n3, t4);
  a2[r2] = i3;
}
async function Fe(e3, t4 = null, r2 = null) {
  if (e3 instanceof t3 && (e3 = e3.toArray()), null == e3) return null;
  if (O(e3) || e3 instanceof p || q(e3) || U(e3)) return Re(e3, r2);
  if (e3 instanceof Array) {
    const n3 = [], a2 = [];
    for (const i3 of e3) null === i3 || O(i3) || i3 instanceof p || q(i3) || U(i3) ? a2.push(Re(i3, r2)) : (a2.push(null), n3.push(De(i3, t4, r2, a2.length - 1, a2)));
    return n3.length > 0 && await Promise.all(n3), a2;
  }
  return null !== e3 && "object" == typeof e3 && void 0 !== e3.castAsJsonAsync ? e3.castAsJsonAsync(t4, r2) : null;
}
function Ie(e3, n3, t4) {
  const r2 = e3.fullSchema();
  if (null === r2) return null;
  if (!r2.fields) return null;
  return pe(n3, r2, e3, t4);
}
function Ce(e3) {
  const n3 = e3.fullSchema();
  return null === n3 ? null : n3.fields && n3.typeIdField ? { subtypeField: n3.typeIdField, subtypes: n3.types ? n3.types.map((e4) => ({ name: e4.name, code: e4.id })) : [] } : null;
}
function we(e3, n3, t4, r2) {
  const a2 = e3.fullSchema();
  if (null === a2) return null;
  if (!a2.fields) return null;
  const i3 = pe(n3, a2, e3, r2);
  if (void 0 === t4) try {
    t4 = e3.field(n3);
  } catch (o2) {
    return null;
  }
  return de(i3, t4);
}
function Oe(e3, n3, t4, r2) {
  const a2 = e3.fullSchema();
  if (null === a2) return null;
  if (!a2.fields) return null;
  if (void 0 === t4) {
    try {
      t4 = e3.field(n3);
    } catch (i3) {
      return null;
    }
    return t4;
  }
  return ye(pe(n3, a2, e3, r2), t4);
}
function Ze(e3) {
  var _a, _b;
  return ((_a = e3 == null ? void 0 : e3.timeReference) == null ? void 0 : _a.timeZone) ? (_b = e3 == null ? void 0 : e3.timeReference) == null ? void 0 : _b.timeZone : "system";
}
function _e(e3) {
  const n3 = e3.fullSchema();
  if (null === n3) return null;
  if (!n3.fields) return null;
  const t4 = [];
  for (const r2 of n3.fields) t4.push(l2(r2));
  return { objectIdField: n3.objectIdField, globalIdField: n3.globalIdField, geometryType: void 0 === I[n3.geometryType] ? "" : I[n3.geometryType], fields: t4, datesInUnknownTimezone: true === n3.datesInUnknownTimezone, preferredTimeReference: n3.preferredTimeReference || null, editFieldsInfo: n3.editFieldsInfo || null, timeInfo: n3.timeInfo || null, dateFieldsTimeReference: n3.dateFieldsTimeReference || null };
}
var ve = Object.freeze(Object.defineProperty({ __proto__: null, ImplicitResult: j, ImplicitResultE: k, ReturnResult: A, ReturnResultE: M, absRound: W, autoCastArrayOfPointsToMultiPoint: Te, autoCastArrayOfPointsToPolygon: Se, autoCastArrayOfPointsToPolyline: Ne, autoCastFeatureToGeometry: be, binaryOperator: Ae, breakResult: D, castAsJson: Re, castAsJsonAsync: Fe, continueResult: F2, defaultTimeZone: Ze, defaultUndefined: Z, equalityTest: te, featureDomainCodeLookup: Oe, featureDomainValueLookup: we, featureFullDomain: Ie, featureSchema: _e, featureSubtypes: Ce, fixNullGeometry: me, fixSpatialReference: ce, formatDate: X, formatNumber: Q, generateUUID: $, getDomain: pe, getDomainCode: ye, getDomainValue: de, getType: _, greaterThanLessThan: ne, isArray: J, isBoolean: L, isDate: U, isFeature: z, isFeatureSet: G, isFeatureSetCollection: E, isFunctionParameter: C2, isImmutableArray: V, isInteger: P2, isJsDate: q, isModule: w3, isNumber: Y, isObject: H, isSimpleType: O, isString: v2, multiReplace: I2, pcCheck: B, stableStringify: ge, standardiseDateFormat: K, tick: je, toBoolean: fe, toDate: ue, toNumber: le, toNumberArray: ae, toString: re, toStringArray: Me, toStringExplicit: ie, voidOperation: R }, Symbol.toStringTag, { value: "Module" }));

export {
  t3 as t,
  r,
  n,
  e2 as e,
  s2 as s,
  i2 as i,
  h2 as h,
  i as i2,
  n2,
  o,
  l2 as l,
  s3 as s2,
  u3 as u,
  c2 as c,
  a,
  m2 as m,
  d,
  g,
  F,
  T,
  P,
  I,
  b,
  x,
  N,
  s4 as s3,
  j,
  A,
  R,
  D,
  F2,
  I2,
  C2 as C,
  O,
  Z,
  _,
  v2 as v,
  L,
  Y,
  P2,
  J,
  z,
  G,
  E,
  V,
  U,
  B,
  W,
  $,
  K,
  ne,
  te,
  re,
  ae,
  ie,
  le,
  ue,
  fe,
  ce,
  me,
  de,
  ye,
  pe,
  be,
  Se,
  Ne,
  Te,
  Me,
  je,
  Ae,
  Re,
  Fe,
  Ie,
  Ce,
  we,
  Oe,
  Ze,
  _e,
  ve
};
//# sourceMappingURL=chunk-REVHHZEO.js.map
