import {
  S,
  l,
  o,
  r,
  s,
  t as t2,
  y
} from "./chunk-4GVJIP3E.js";
import {
  S as S2
} from "./chunk-R3VLALN5.js";
import {
  y as y2
} from "./chunk-VX6YUKFM.js";
import {
  m
} from "./chunk-6ILWLF72.js";
import {
  m3 as m2
} from "./chunk-YD3YIZNH.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/symbols/support/defaults.js
var c = y2.fromJSON(l);
var u = m.fromJSON(o);
var a = S2.fromJSON(S);
var y3 = m2.fromJSON(t2);
function J(o2) {
  if (t(o2)) return null;
  switch (o2.type) {
    case "mesh":
      return null;
    case "point":
    case "multipoint":
      return c;
    case "polyline":
      return u;
    case "polygon":
    case "extent":
      return a;
  }
  return null;
}
var N = y2.fromJSON(r);
var O = m.fromJSON(s);
var j = S2.fromJSON(y);

export {
  c,
  u,
  a,
  y3 as y,
  J,
  N,
  O,
  j
};
//# sourceMappingURL=chunk-FIVMDF4P.js.map
