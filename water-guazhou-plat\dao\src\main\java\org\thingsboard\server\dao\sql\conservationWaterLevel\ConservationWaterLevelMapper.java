package org.thingsboard.server.dao.sql.conservationWaterLevel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationWaterLevel;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 涵养水位数据Mapper
 */
@Mapper
public interface ConservationWaterLevelMapper extends BaseMapper<ConservationWaterLevel> {

    /**
     * 分页查询涵养水位数据
     */
    List<ConservationWaterLevel> getList(@Param("params") Map<String, Object> params);

    /**
     * 查询涵养水位数据总数
     */
    int getCount(@Param("params") Map<String, Object> params);

    /**
     * 根据测点和时间范围查询水位变化数据
     */
    List<ConservationWaterLevel> getWaterLevelChangeData(
            @Param("stationId") String stationId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 获取测点最新的水位数据
     */
    ConservationWaterLevel getLatestWaterLevel(@Param("stationId") String stationId);

    /**
     * 获取测点指定时间段的统计数据
     */
    Map<String, Object> getStatisticsData(
            @Param("stationId") String stationId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime
    );

    /**
     * 批量插入水位数据
     */
    int batchInsert(@Param("list") List<ConservationWaterLevel> list);
}
