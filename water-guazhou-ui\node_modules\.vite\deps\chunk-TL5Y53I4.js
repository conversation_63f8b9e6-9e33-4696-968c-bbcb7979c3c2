import {
  M
} from "./chunk-VHLK35TF.js";
import {
  g
} from "./chunk-TLKX5XIJ.js";
import {
  v
} from "./chunk-ZACBBT3Y.js";
import {
  f2
} from "./chunk-JXLVNWKF.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  f,
  i2 as i
} from "./chunk-GZGAQUSK.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/graphics/hydratedFeatures.js
function m(e) {
  return "declaredClass" in e;
}
function c(e) {
  return "declaredClass" in e;
}
function l(e) {
  return "declaredClass" in e;
}
function u(r, n) {
  return r ? l(r) ? r : new g({ layer: n, sourceLayer: n, visible: r.visible, symbol: p(r.symbol), attributes: p(r.attributes), geometry: f3(r.geometry) }) : null;
}
function f3(e) {
  return t(e) ? null : m(e) ? e : v(p2(e));
}
function p2(e) {
  const { wkid: t2, wkt: r, latestWkid: n } = e.spatialReference, a = { wkid: t2, wkt: r, latestWkid: n };
  switch (e.type) {
    case "point": {
      const { x: t3, y: r2, z: n2, m: s } = e;
      return { x: t3, y: r2, z: n2, m: s, spatialReference: a };
    }
    case "polygon": {
      const { rings: t3, hasZ: r2, hasM: n2 } = e;
      return { rings: h(t3), hasZ: r2, hasM: n2, spatialReference: a };
    }
    case "polyline": {
      const { paths: t3, hasZ: r2, hasM: n2 } = e;
      return { paths: h(t3), hasZ: r2, hasM: n2, spatialReference: a };
    }
    case "extent": {
      const { xmin: t3, xmax: r2, ymin: n2, ymax: s, zmin: i2, zmax: o, mmin: m2, mmax: c2, hasZ: l2, hasM: u2 } = e;
      return { xmin: t3, xmax: r2, ymin: n2, ymax: s, zmin: i2, zmax: o, mmin: m2, mmax: c2, hasZ: l2, hasM: u2, spatialReference: a };
    }
    case "multipoint": {
      const { points: t3, hasZ: r2, hasM: n2 } = e;
      return { points: x(t3) ? y(t3) : t3, hasZ: r2, hasM: n2, spatialReference: a };
    }
    default:
      return;
  }
}
function h(e) {
  return d(e) ? e.map((e2) => y(e2)) : e;
}
function y(e) {
  return e.map((e2) => Array.from(e2));
}
function d(e) {
  for (const t2 of e) if (0 !== t2.length) return x(t2);
  return false;
}
function x(e) {
  return e.length > 0 && (f(e[0]) || i(e[0]));
}
function M2(e, t2) {
  if (!e) return null;
  let r;
  if (c(e)) {
    if (null == t2) return e.clone();
    if (c(t2)) return t2.copy(e);
  }
  return null != t2 ? (r = t2, r.x = e.x, r.y = e.y, r.spatialReference = e.spatialReference, e.hasZ ? (r.z = e.z, r.hasZ = e.hasZ) : (r.z = void 0, r.hasZ = false), e.hasM ? (r.m = e.m, r.hasM = true) : (r.m = void 0, r.hasM = false)) : (r = M(e.x, e.y, e.z, e.spatialReference), e.hasM && (r.m = e.m, r.hasM = true)), r;
}
function k(e) {
  const { wkid: t2, wkt: r, latestWkid: n } = e, a = { wkid: t2, wkt: r, latestWkid: n };
  return f2.fromJSON(a);
}

export {
  m,
  u,
  f3 as f,
  M2 as M,
  k
};
//# sourceMappingURL=chunk-TL5Y53I4.js.map
