import {
  e as e2
} from "./chunk-C2ZE76VJ.js";
import {
  o as o4
} from "./chunk-Q6BEUTMN.js";
import {
  a
} from "./chunk-QB6AUIQ2.js";
import {
  r
} from "./chunk-REGYRSW7.js";
import {
  n
} from "./chunk-Y424ZXTG.js";
import {
  e as e3
} from "./chunk-UB5FTTH5.js";
import {
  i,
  t as t2
} from "./chunk-6GW7M2AQ.js";
import {
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  c,
  v
} from "./chunk-N3S5O3YO.js";
import {
  o as o3
} from "./chunk-BPRRRPC3.js";
import {
  e
} from "./chunk-GXMOAZWH.js";
import {
  o as o5
} from "./chunk-TUB4N6LD.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/PatternStyle.js
var a2;
!function(a3) {
  a3[a3.Horizontal = 0] = "Horizontal", a3[a3.Vertical = 1] = "Vertical", a3[a3.Cross = 2] = "Cross", a3[a3.ForwardDiagonal = 3] = "ForwardDiagonal", a3[a3.BackwardDiagonal = 4] = "BackwardDiagonal", a3[a3.DiagonalCross = 5] = "DiagonalCross", a3[a3.COUNT = 6] = "COUNT";
}(a2 || (a2 = {}));

// node_modules/@arcgis/core/chunks/Pattern.glsl.js
var y = 0.70710678118;
var S = y;
var j = 0.08715574274;
function x(x2) {
  const T2 = new o2(), C = x2.hasMultipassTerrain && (x2.output === h.Color || x2.output === h.Alpha);
  x2.draped || T2.extensions.add("GL_OES_standard_derivatives");
  const { vertex: R, fragment: $ } = T2;
  v(R, x2), T2.include(r, x2), T2.include(e2, x2), x2.draped ? R.uniforms.add(new o5("worldToScreenRatio", (e4, o6) => 1 / o6.screenToPCSRatio)) : T2.attributes.add(O.BOUNDINGRECT, "mat3"), T2.attributes.add(O.POSITION, "vec3"), T2.attributes.add(O.UVMAPSPACE, "vec4"), T2.varyings.add("vpos", "vec3"), T2.varyings.add("vuv", "vec2"), C && T2.varyings.add("depth", "float");
  const D = x2.style === a2.ForwardDiagonal || x2.style === a2.BackwardDiagonal || x2.style === a2.DiagonalCross;
  D && R.code.add(o`
      const mat2 rotate45 = mat2(${o.float(y)}, ${o.float(-S)},
                                 ${o.float(S)}, ${o.float(y)});
    `), x2.draped || (c(R, x2), R.uniforms.add(new o5("worldToScreenPerDistanceRatio", (e4, o6) => 1 / o6.camera.perScreenPixelRatio)), R.code.add(o`vec3 projectPointToLineSegment(vec3 center, vec3 halfVector, vec3 point) {
float projectedLength = dot(halfVector, point - center) / dot(halfVector, halfVector);
return center + halfVector * clamp(projectedLength, -1.0, 1.0);
}`), R.code.add(o`vec3 intersectRayPlane(vec3 rayDir, vec3 rayOrigin, vec3 planeNormal, vec3 planePoint) {
float d = dot(planeNormal, planePoint);
float t = (d - dot(planeNormal, rayOrigin)) / dot(planeNormal, rayDir);
return rayOrigin + t * rayDir;
}`), R.code.add(o`
      float boundingRectDistanceToCamera() {
        vec3 center = vec3(boundingRect[0][0], boundingRect[0][1], boundingRect[0][2]);
        vec3 halfU = vec3(boundingRect[1][0], boundingRect[1][1], boundingRect[1][2]);
        vec3 halfV = vec3(boundingRect[2][0], boundingRect[2][1], boundingRect[2][2]);
        vec3 n = normalize(cross(halfU, halfV));

        vec3 viewDir = - vec3(view[0][2], view[1][2], view[2][2]);

        float viewAngle = dot(viewDir, n);
        float minViewAngle = ${o.float(j)};

        if (abs(viewAngle) < minViewAngle) {
          // view direction is (almost) parallel to plane -> clamp it to min angle
          float normalComponent = sign(viewAngle) * minViewAngle - viewAngle;
          viewDir = normalize(viewDir + normalComponent * n);
        }

        // intersect view direction with infinite plane that contains bounding rect
        vec3 planeProjected = intersectRayPlane(viewDir, cameraPosition, n, center);

        // clip to bounds by projecting to u and v line segments individually
        vec3 uProjected = projectPointToLineSegment(center, halfU, planeProjected);
        vec3 vProjected = projectPointToLineSegment(center, halfV, planeProjected);

        // use to calculate the closest point to camera on bounding rect
        vec3 closestPoint = uProjected + vProjected - center;

        return length(closestPoint - cameraPosition);
      }
    `)), R.code.add(o`
    vec2 scaledUV() {
      vec2 uv = uvMapSpace.xy ${D ? " * rotate45" : ""};
      vec2 uvCellOrigin = uvMapSpace.zw ${D ? " * rotate45" : ""};

      ${x2.draped ? "" : o`
            float distanceToCamera = boundingRectDistanceToCamera();
            float worldToScreenRatio = worldToScreenPerDistanceRatio / distanceToCamera;
          `}

      // Logarithmically discretize ratio to avoid jittering
      float step = 0.1;
      float discreteWorldToScreenRatio = log(worldToScreenRatio);
      discreteWorldToScreenRatio = ceil(discreteWorldToScreenRatio / step) * step;
      discreteWorldToScreenRatio = exp(discreteWorldToScreenRatio);

      vec2 uvOffset = mod(uvCellOrigin * discreteWorldToScreenRatio, ${o.float(x2.patternSpacing)});
      return uvOffset + (uv * discreteWorldToScreenRatio);
    }
  `);
  const V = x2.output === h.Depth;
  return V && (T2.include(o4, x2), i(T2), t2(T2)), R.code.add(o`
    void main(void) {
      vuv = scaledUV();
      vpos = position;
      ${C ? "depth = (view * vec4(vpos, 1.0)).z;" : ""}
      forwardNormalizedVertexColor();
      gl_Position = ${V ? o`transformPositionWithDepth(proj, view, vpos, nearFar, linearDepth);` : o`transformPosition(proj, view, vpos);`}
    }
  `), T2.include(u, x2), $.include(e3), x2.draped && $.uniforms.add(new o5("texelSize", (e4, o6) => 1 / o6.camera.pixelRatio)), x2.output === h.Highlight && T2.include(a, x2), C && T2.include(n, x2), x2.output !== h.Highlight && ($.code.add(o`
      const float lineWidth = ${o.float(x2.lineWidth)};
      const float spacing = ${o.float(x2.patternSpacing)};
      const float spacingINV = ${o.float(1 / x2.patternSpacing)};

      float coverage(float p, float txlSize) {
        p = mod(p, spacing);

        float halfTxlSize = txlSize / 2.0;

        float start = p - halfTxlSize;
        float end = p + halfTxlSize;

        float coverage = (ceil(end * spacingINV) - floor(start * spacingINV)) * lineWidth;
        coverage -= min(lineWidth, mod(start, spacing));
        coverage -= max(lineWidth - mod(end, spacing), 0.0);

        return coverage / txlSize;
      }
    `), x2.draped || $.code.add(o`const int maxSamples = 5;
float sample(float p) {
vec2 dxdy = abs(vec2(dFdx(p), dFdy(p)));
float fwidth = dxdy.x + dxdy.y;
ivec2 samples = 1 + ivec2(clamp(dxdy, 0.0, float(maxSamples - 1)));
vec2 invSamples = 1.0 / vec2(samples);
float accumulator = 0.0;
for (int j = 0; j < maxSamples; j++) {
if(j >= samples.y) {
break;
}
for (int i = 0; i < maxSamples; i++) {
if(i >= samples.x) {
break;
}
vec2 step = vec2(i,j) * invSamples - 0.5;
accumulator += coverage(p + step.x * dxdy.x + step.y * dxdy.y, fwidth);
}
}
accumulator /= float(samples.x * samples.y);
return accumulator;
}`)), $.uniforms.add(new e("uColor", (e4) => e4.color)), $.code.add(o`
    void main() {
      discardBySlice(vpos);
      ${C ? "terrainDepthTest(gl_FragCoord, depth);" : ""}
      vec4 color = ${x2.hasVertexColors ? "vColor * uColor;" : "uColor;"}
      color = highlightSlice(color, vpos);

      ${x2.output !== h.Highlight ? o`color.a *= ${P(x2)};` : ""}

      ${x2.output === h.ObjectAndLayerIdColor ? o`color.a = 1.0;` : ""}

      if (color.a < ${o.float(t)}) {
        discard;
      }

      ${x2.output === h.Alpha ? o`gl_FragColor = vec4(color.a);` : ""}

      ${x2.output === h.Color ? o`gl_FragColor = color; ${x2.transparencyPassType === o3.Color ? "gl_FragColor = premultiplyAlpha(gl_FragColor);" : ""}` : ""}
      ${x2.output === h.Highlight ? o`outputHighlight();` : ""}
      ${x2.output === h.Depth ? o`outputDepth(linearDepth);` : ""};
    }
  `), T2;
}
function P(e4) {
  function o6(o7) {
    return e4.draped ? o`coverage(vuv.${o7}, texelSize)` : o`sample(vuv.${o7})`;
  }
  switch (e4.style) {
    case a2.ForwardDiagonal:
    case a2.Horizontal:
      return o6("y");
    case a2.BackwardDiagonal:
    case a2.Vertical:
      return o6("x");
    case a2.DiagonalCross:
    case a2.Cross:
      return o`
        1.0 - (1.0 - ${o6("x")}) * (1.0 - ${o6("y")})
      `;
    default:
      return "0.0";
  }
}
var T = Object.freeze(Object.defineProperty({ __proto__: null, build: x }, Symbol.toStringTag, { value: "Module" }));

export {
  a2 as a,
  x,
  T
};
//# sourceMappingURL=chunk-QZXCL7E5.js.map
