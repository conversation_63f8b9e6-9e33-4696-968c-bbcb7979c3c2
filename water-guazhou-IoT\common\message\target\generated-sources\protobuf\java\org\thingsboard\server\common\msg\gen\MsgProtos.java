// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tbmsg.proto

package org.thingsboard.server.common.msg.gen;

public final class MsgProtos {
  private MsgProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TbMsgMetaDataProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:msgqueue.TbMsgMetaDataProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */
    int getDataCount();
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */
    boolean containsData(
        java.lang.String key);
    /**
     * Use {@link #getDataMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getData();
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getDataMap();
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    java.lang.String getDataOrDefault(
        java.lang.String key,
        java.lang.String defaultValue);
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    java.lang.String getDataOrThrow(
        java.lang.String key);
  }
  /**
   * Protobuf type {@code msgqueue.TbMsgMetaDataProto}
   */
  public  static final class TbMsgMetaDataProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:msgqueue.TbMsgMetaDataProto)
      TbMsgMetaDataProtoOrBuilder {
    // Use TbMsgMetaDataProto.newBuilder() to construct.
    private TbMsgMetaDataProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TbMsgMetaDataProto() {
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private TbMsgMetaDataProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                data_ = com.google.protobuf.MapField.newMapField(
                    DataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              data = input.readMessage(
                  DataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              data_.getMutableMap().put(data.getKey(), data.getValue());
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgMetaDataProto_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetData();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgMetaDataProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.class, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.Builder.class);
    }

    public static final int DATA_FIELD_NUMBER = 1;
    private static final class DataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgMetaDataProto_DataEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> data_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetData() {
      if (data_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DataDefaultEntryHolder.defaultEntry);
      }
      return data_;
    }

    public int getDataCount() {
      return internalGetData().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    public boolean containsData(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      return internalGetData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDataMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getData() {
      return getDataMap();
    }
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    public java.util.Map<java.lang.String, java.lang.String> getDataMap() {
      return internalGetData().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    public java.lang.String getDataOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;string, string&gt; data = 1;</code>
     */

    public java.lang.String getDataOrThrow(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetData().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        data = DataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        output.writeMessage(1, data);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        data = DataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, data);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto other = (org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto) obj;

      boolean result = true;
      result = result && internalGetData().equals(
          other.internalGetData());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      if (!internalGetData().getMap().isEmpty()) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code msgqueue.TbMsgMetaDataProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:msgqueue.TbMsgMetaDataProto)
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgMetaDataProto_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgMetaDataProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.class, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.Builder.class);
      }

      // Construct using org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        internalGetMutableData().clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgMetaDataProto_descriptor;
      }

      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto getDefaultInstanceForType() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.getDefaultInstance();
      }

      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto build() {
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto buildPartial() {
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto result = new org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto(this);
        int from_bitField0_ = bitField0_;
        result.data_ = internalGetData();
        result.data_.makeImmutable();
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto) {
          return mergeFrom((org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto other) {
        if (other == org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.getDefaultInstance()) return this;
        internalGetMutableData().mergeFrom(
            other.internalGetData());
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> data_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetData() {
        if (data_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DataDefaultEntryHolder.defaultEntry);
        }
        return data_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableData() {
        onChanged();;
        if (data_ == null) {
          data_ = com.google.protobuf.MapField.newMapField(
              DataDefaultEntryHolder.defaultEntry);
        }
        if (!data_.isMutable()) {
          data_ = data_.copy();
        }
        return data_;
      }

      public int getDataCount() {
        return internalGetData().getMap().size();
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public boolean containsData(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        return internalGetData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDataMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getData() {
        return getDataMap();
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public java.util.Map<java.lang.String, java.lang.String> getDataMap() {
        return internalGetData().getMap();
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public java.lang.String getDataOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public java.lang.String getDataOrThrow(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetData().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearData() {
        getMutableData().clear();
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public Builder removeData(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        getMutableData().remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableData() {
        return internalGetMutableData().getMutableMap();
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */
      public Builder putData(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        if (value == null) { throw new java.lang.NullPointerException(); }
        getMutableData().put(key, value);
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; data = 1;</code>
       */

      public Builder putAllData(
          java.util.Map<java.lang.String, java.lang.String> values) {
        getMutableData().putAll(values);
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:msgqueue.TbMsgMetaDataProto)
    }

    // @@protoc_insertion_point(class_scope:msgqueue.TbMsgMetaDataProto)
    private static final org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto();
    }

    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TbMsgMetaDataProto>
        PARSER = new com.google.protobuf.AbstractParser<TbMsgMetaDataProto>() {
      public TbMsgMetaDataProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new TbMsgMetaDataProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TbMsgMetaDataProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TbMsgMetaDataProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TbMsgTransactionDataProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:msgqueue.TbMsgTransactionDataProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string id = 1;</code>
     */
    java.lang.String getId();
    /**
     * <code>optional string id = 1;</code>
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <code>optional string entityType = 2;</code>
     */
    java.lang.String getEntityType();
    /**
     * <code>optional string entityType = 2;</code>
     */
    com.google.protobuf.ByteString
        getEntityTypeBytes();

    /**
     * <code>optional int64 entityIdMSB = 3;</code>
     */
    long getEntityIdMSB();

    /**
     * <code>optional int64 entityIdLSB = 4;</code>
     */
    long getEntityIdLSB();
  }
  /**
   * Protobuf type {@code msgqueue.TbMsgTransactionDataProto}
   */
  public  static final class TbMsgTransactionDataProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:msgqueue.TbMsgTransactionDataProto)
      TbMsgTransactionDataProtoOrBuilder {
    // Use TbMsgTransactionDataProto.newBuilder() to construct.
    private TbMsgTransactionDataProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TbMsgTransactionDataProto() {
      id_ = "";
      entityType_ = "";
      entityIdMSB_ = 0L;
      entityIdLSB_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private TbMsgTransactionDataProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              id_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              entityType_ = s;
              break;
            }
            case 24: {

              entityIdMSB_ = input.readInt64();
              break;
            }
            case 32: {

              entityIdLSB_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgTransactionDataProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgTransactionDataProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.class, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private volatile java.lang.Object id_;
    /**
     * <code>optional string id = 1;</code>
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <code>optional string id = 1;</code>
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENTITYTYPE_FIELD_NUMBER = 2;
    private volatile java.lang.Object entityType_;
    /**
     * <code>optional string entityType = 2;</code>
     */
    public java.lang.String getEntityType() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entityType_ = s;
        return s;
      }
    }
    /**
     * <code>optional string entityType = 2;</code>
     */
    public com.google.protobuf.ByteString
        getEntityTypeBytes() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entityType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENTITYIDMSB_FIELD_NUMBER = 3;
    private long entityIdMSB_;
    /**
     * <code>optional int64 entityIdMSB = 3;</code>
     */
    public long getEntityIdMSB() {
      return entityIdMSB_;
    }

    public static final int ENTITYIDLSB_FIELD_NUMBER = 4;
    private long entityIdLSB_;
    /**
     * <code>optional int64 entityIdLSB = 4;</code>
     */
    public long getEntityIdLSB() {
      return entityIdLSB_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
      }
      if (!getEntityTypeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, entityType_);
      }
      if (entityIdMSB_ != 0L) {
        output.writeInt64(3, entityIdMSB_);
      }
      if (entityIdLSB_ != 0L) {
        output.writeInt64(4, entityIdLSB_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
      }
      if (!getEntityTypeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, entityType_);
      }
      if (entityIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, entityIdMSB_);
      }
      if (entityIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, entityIdLSB_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto other = (org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto) obj;

      boolean result = true;
      result = result && getId()
          .equals(other.getId());
      result = result && getEntityType()
          .equals(other.getEntityType());
      result = result && (getEntityIdMSB()
          == other.getEntityIdMSB());
      result = result && (getEntityIdLSB()
          == other.getEntityIdLSB());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + ENTITYTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getEntityType().hashCode();
      hash = (37 * hash) + ENTITYIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEntityIdMSB());
      hash = (37 * hash) + ENTITYIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEntityIdLSB());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code msgqueue.TbMsgTransactionDataProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:msgqueue.TbMsgTransactionDataProto)
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgTransactionDataProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgTransactionDataProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.class, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.Builder.class);
      }

      // Construct using org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        id_ = "";

        entityType_ = "";

        entityIdMSB_ = 0L;

        entityIdLSB_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgTransactionDataProto_descriptor;
      }

      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto getDefaultInstanceForType() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.getDefaultInstance();
      }

      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto build() {
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto buildPartial() {
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto result = new org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto(this);
        result.id_ = id_;
        result.entityType_ = entityType_;
        result.entityIdMSB_ = entityIdMSB_;
        result.entityIdLSB_ = entityIdLSB_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto) {
          return mergeFrom((org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto other) {
        if (other == org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          onChanged();
        }
        if (!other.getEntityType().isEmpty()) {
          entityType_ = other.entityType_;
          onChanged();
        }
        if (other.getEntityIdMSB() != 0L) {
          setEntityIdMSB(other.getEntityIdMSB());
        }
        if (other.getEntityIdLSB() != 0L) {
          setEntityIdLSB(other.getEntityIdLSB());
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object id_ = "";
      /**
       * <code>optional string id = 1;</code>
       */
      public java.lang.String getId() {
        java.lang.Object ref = id_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string id = 1;</code>
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        java.lang.Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string id = 1;</code>
       */
      public Builder setId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string id = 1;</code>
       */
      public Builder clearId() {
        
        id_ = getDefaultInstance().getId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string id = 1;</code>
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        id_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object entityType_ = "";
      /**
       * <code>optional string entityType = 2;</code>
       */
      public java.lang.String getEntityType() {
        java.lang.Object ref = entityType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          entityType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string entityType = 2;</code>
       */
      public com.google.protobuf.ByteString
          getEntityTypeBytes() {
        java.lang.Object ref = entityType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          entityType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string entityType = 2;</code>
       */
      public Builder setEntityType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        entityType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 2;</code>
       */
      public Builder clearEntityType() {
        
        entityType_ = getDefaultInstance().getEntityType();
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 2;</code>
       */
      public Builder setEntityTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        entityType_ = value;
        onChanged();
        return this;
      }

      private long entityIdMSB_ ;
      /**
       * <code>optional int64 entityIdMSB = 3;</code>
       */
      public long getEntityIdMSB() {
        return entityIdMSB_;
      }
      /**
       * <code>optional int64 entityIdMSB = 3;</code>
       */
      public Builder setEntityIdMSB(long value) {
        
        entityIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityIdMSB = 3;</code>
       */
      public Builder clearEntityIdMSB() {
        
        entityIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long entityIdLSB_ ;
      /**
       * <code>optional int64 entityIdLSB = 4;</code>
       */
      public long getEntityIdLSB() {
        return entityIdLSB_;
      }
      /**
       * <code>optional int64 entityIdLSB = 4;</code>
       */
      public Builder setEntityIdLSB(long value) {
        
        entityIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityIdLSB = 4;</code>
       */
      public Builder clearEntityIdLSB() {
        
        entityIdLSB_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:msgqueue.TbMsgTransactionDataProto)
    }

    // @@protoc_insertion_point(class_scope:msgqueue.TbMsgTransactionDataProto)
    private static final org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto();
    }

    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TbMsgTransactionDataProto>
        PARSER = new com.google.protobuf.AbstractParser<TbMsgTransactionDataProto>() {
      public TbMsgTransactionDataProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new TbMsgTransactionDataProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TbMsgTransactionDataProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TbMsgTransactionDataProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TbMsgProtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:msgqueue.TbMsgProto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string id = 1;</code>
     */
    java.lang.String getId();
    /**
     * <code>optional string id = 1;</code>
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <code>optional string type = 2;</code>
     */
    java.lang.String getType();
    /**
     * <code>optional string type = 2;</code>
     */
    com.google.protobuf.ByteString
        getTypeBytes();

    /**
     * <code>optional string entityType = 3;</code>
     */
    java.lang.String getEntityType();
    /**
     * <code>optional string entityType = 3;</code>
     */
    com.google.protobuf.ByteString
        getEntityTypeBytes();

    /**
     * <code>optional int64 entityIdMSB = 4;</code>
     */
    long getEntityIdMSB();

    /**
     * <code>optional int64 entityIdLSB = 5;</code>
     */
    long getEntityIdLSB();

    /**
     * <code>optional int64 ruleChainIdMSB = 6;</code>
     */
    long getRuleChainIdMSB();

    /**
     * <code>optional int64 ruleChainIdLSB = 7;</code>
     */
    long getRuleChainIdLSB();

    /**
     * <code>optional int64 ruleNodeIdMSB = 8;</code>
     */
    long getRuleNodeIdMSB();

    /**
     * <code>optional int64 ruleNodeIdLSB = 9;</code>
     */
    long getRuleNodeIdLSB();

    /**
     * <code>optional int64 clusterPartition = 10;</code>
     */
    long getClusterPartition();

    /**
     * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
     */
    boolean hasMetaData();
    /**
     * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
     */
    org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto getMetaData();
    /**
     * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
     */
    org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProtoOrBuilder getMetaDataOrBuilder();

    /**
     * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
     */
    boolean hasTransactionData();
    /**
     * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
     */
    org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto getTransactionData();
    /**
     * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
     */
    org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProtoOrBuilder getTransactionDataOrBuilder();

    /**
     * <code>optional int32 dataType = 13;</code>
     */
    int getDataType();

    /**
     * <code>optional string data = 14;</code>
     */
    java.lang.String getData();
    /**
     * <code>optional string data = 14;</code>
     */
    com.google.protobuf.ByteString
        getDataBytes();
  }
  /**
   * Protobuf type {@code msgqueue.TbMsgProto}
   */
  public  static final class TbMsgProto extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:msgqueue.TbMsgProto)
      TbMsgProtoOrBuilder {
    // Use TbMsgProto.newBuilder() to construct.
    private TbMsgProto(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TbMsgProto() {
      id_ = "";
      type_ = "";
      entityType_ = "";
      entityIdMSB_ = 0L;
      entityIdLSB_ = 0L;
      ruleChainIdMSB_ = 0L;
      ruleChainIdLSB_ = 0L;
      ruleNodeIdMSB_ = 0L;
      ruleNodeIdLSB_ = 0L;
      clusterPartition_ = 0L;
      dataType_ = 0;
      data_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }
    private TbMsgProto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!input.skipField(tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              id_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              type_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              entityType_ = s;
              break;
            }
            case 32: {

              entityIdMSB_ = input.readInt64();
              break;
            }
            case 40: {

              entityIdLSB_ = input.readInt64();
              break;
            }
            case 48: {

              ruleChainIdMSB_ = input.readInt64();
              break;
            }
            case 56: {

              ruleChainIdLSB_ = input.readInt64();
              break;
            }
            case 64: {

              ruleNodeIdMSB_ = input.readInt64();
              break;
            }
            case 72: {

              ruleNodeIdLSB_ = input.readInt64();
              break;
            }
            case 80: {

              clusterPartition_ = input.readInt64();
              break;
            }
            case 90: {
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.Builder subBuilder = null;
              if (metaData_ != null) {
                subBuilder = metaData_.toBuilder();
              }
              metaData_ = input.readMessage(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(metaData_);
                metaData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 98: {
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.Builder subBuilder = null;
              if (transactionData_ != null) {
                subBuilder = transactionData_.toBuilder();
              }
              transactionData_ = input.readMessage(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(transactionData_);
                transactionData_ = subBuilder.buildPartial();
              }

              break;
            }
            case 104: {

              dataType_ = input.readInt32();
              break;
            }
            case 114: {
              java.lang.String s = input.readStringRequireUtf8();

              data_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgProto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgProto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto.class, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private volatile java.lang.Object id_;
    /**
     * <code>optional string id = 1;</code>
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <code>optional string id = 1;</code>
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private volatile java.lang.Object type_;
    /**
     * <code>optional string type = 2;</code>
     */
    public java.lang.String getType() {
      java.lang.Object ref = type_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        type_ = s;
        return s;
      }
    }
    /**
     * <code>optional string type = 2;</code>
     */
    public com.google.protobuf.ByteString
        getTypeBytes() {
      java.lang.Object ref = type_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        type_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENTITYTYPE_FIELD_NUMBER = 3;
    private volatile java.lang.Object entityType_;
    /**
     * <code>optional string entityType = 3;</code>
     */
    public java.lang.String getEntityType() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entityType_ = s;
        return s;
      }
    }
    /**
     * <code>optional string entityType = 3;</code>
     */
    public com.google.protobuf.ByteString
        getEntityTypeBytes() {
      java.lang.Object ref = entityType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entityType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENTITYIDMSB_FIELD_NUMBER = 4;
    private long entityIdMSB_;
    /**
     * <code>optional int64 entityIdMSB = 4;</code>
     */
    public long getEntityIdMSB() {
      return entityIdMSB_;
    }

    public static final int ENTITYIDLSB_FIELD_NUMBER = 5;
    private long entityIdLSB_;
    /**
     * <code>optional int64 entityIdLSB = 5;</code>
     */
    public long getEntityIdLSB() {
      return entityIdLSB_;
    }

    public static final int RULECHAINIDMSB_FIELD_NUMBER = 6;
    private long ruleChainIdMSB_;
    /**
     * <code>optional int64 ruleChainIdMSB = 6;</code>
     */
    public long getRuleChainIdMSB() {
      return ruleChainIdMSB_;
    }

    public static final int RULECHAINIDLSB_FIELD_NUMBER = 7;
    private long ruleChainIdLSB_;
    /**
     * <code>optional int64 ruleChainIdLSB = 7;</code>
     */
    public long getRuleChainIdLSB() {
      return ruleChainIdLSB_;
    }

    public static final int RULENODEIDMSB_FIELD_NUMBER = 8;
    private long ruleNodeIdMSB_;
    /**
     * <code>optional int64 ruleNodeIdMSB = 8;</code>
     */
    public long getRuleNodeIdMSB() {
      return ruleNodeIdMSB_;
    }

    public static final int RULENODEIDLSB_FIELD_NUMBER = 9;
    private long ruleNodeIdLSB_;
    /**
     * <code>optional int64 ruleNodeIdLSB = 9;</code>
     */
    public long getRuleNodeIdLSB() {
      return ruleNodeIdLSB_;
    }

    public static final int CLUSTERPARTITION_FIELD_NUMBER = 10;
    private long clusterPartition_;
    /**
     * <code>optional int64 clusterPartition = 10;</code>
     */
    public long getClusterPartition() {
      return clusterPartition_;
    }

    public static final int METADATA_FIELD_NUMBER = 11;
    private org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto metaData_;
    /**
     * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
     */
    public boolean hasMetaData() {
      return metaData_ != null;
    }
    /**
     * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
     */
    public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto getMetaData() {
      return metaData_ == null ? org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.getDefaultInstance() : metaData_;
    }
    /**
     * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
     */
    public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProtoOrBuilder getMetaDataOrBuilder() {
      return getMetaData();
    }

    public static final int TRANSACTIONDATA_FIELD_NUMBER = 12;
    private org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto transactionData_;
    /**
     * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
     */
    public boolean hasTransactionData() {
      return transactionData_ != null;
    }
    /**
     * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
     */
    public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto getTransactionData() {
      return transactionData_ == null ? org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.getDefaultInstance() : transactionData_;
    }
    /**
     * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
     */
    public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProtoOrBuilder getTransactionDataOrBuilder() {
      return getTransactionData();
    }

    public static final int DATATYPE_FIELD_NUMBER = 13;
    private int dataType_;
    /**
     * <code>optional int32 dataType = 13;</code>
     */
    public int getDataType() {
      return dataType_;
    }

    public static final int DATA_FIELD_NUMBER = 14;
    private volatile java.lang.Object data_;
    /**
     * <code>optional string data = 14;</code>
     */
    public java.lang.String getData() {
      java.lang.Object ref = data_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        data_ = s;
        return s;
      }
    }
    /**
     * <code>optional string data = 14;</code>
     */
    public com.google.protobuf.ByteString
        getDataBytes() {
      java.lang.Object ref = data_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        data_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
      }
      if (!getTypeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, type_);
      }
      if (!getEntityTypeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, entityType_);
      }
      if (entityIdMSB_ != 0L) {
        output.writeInt64(4, entityIdMSB_);
      }
      if (entityIdLSB_ != 0L) {
        output.writeInt64(5, entityIdLSB_);
      }
      if (ruleChainIdMSB_ != 0L) {
        output.writeInt64(6, ruleChainIdMSB_);
      }
      if (ruleChainIdLSB_ != 0L) {
        output.writeInt64(7, ruleChainIdLSB_);
      }
      if (ruleNodeIdMSB_ != 0L) {
        output.writeInt64(8, ruleNodeIdMSB_);
      }
      if (ruleNodeIdLSB_ != 0L) {
        output.writeInt64(9, ruleNodeIdLSB_);
      }
      if (clusterPartition_ != 0L) {
        output.writeInt64(10, clusterPartition_);
      }
      if (metaData_ != null) {
        output.writeMessage(11, getMetaData());
      }
      if (transactionData_ != null) {
        output.writeMessage(12, getTransactionData());
      }
      if (dataType_ != 0) {
        output.writeInt32(13, dataType_);
      }
      if (!getDataBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, data_);
      }
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
      }
      if (!getTypeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, type_);
      }
      if (!getEntityTypeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, entityType_);
      }
      if (entityIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, entityIdMSB_);
      }
      if (entityIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, entityIdLSB_);
      }
      if (ruleChainIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, ruleChainIdMSB_);
      }
      if (ruleChainIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, ruleChainIdLSB_);
      }
      if (ruleNodeIdMSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, ruleNodeIdMSB_);
      }
      if (ruleNodeIdLSB_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, ruleNodeIdLSB_);
      }
      if (clusterPartition_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(10, clusterPartition_);
      }
      if (metaData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, getMetaData());
      }
      if (transactionData_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, getTransactionData());
      }
      if (dataType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, dataType_);
      }
      if (!getDataBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, data_);
      }
      memoizedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto)) {
        return super.equals(obj);
      }
      org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto other = (org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto) obj;

      boolean result = true;
      result = result && getId()
          .equals(other.getId());
      result = result && getType()
          .equals(other.getType());
      result = result && getEntityType()
          .equals(other.getEntityType());
      result = result && (getEntityIdMSB()
          == other.getEntityIdMSB());
      result = result && (getEntityIdLSB()
          == other.getEntityIdLSB());
      result = result && (getRuleChainIdMSB()
          == other.getRuleChainIdMSB());
      result = result && (getRuleChainIdLSB()
          == other.getRuleChainIdLSB());
      result = result && (getRuleNodeIdMSB()
          == other.getRuleNodeIdMSB());
      result = result && (getRuleNodeIdLSB()
          == other.getRuleNodeIdLSB());
      result = result && (getClusterPartition()
          == other.getClusterPartition());
      result = result && (hasMetaData() == other.hasMetaData());
      if (hasMetaData()) {
        result = result && getMetaData()
            .equals(other.getMetaData());
      }
      result = result && (hasTransactionData() == other.hasTransactionData());
      if (hasTransactionData()) {
        result = result && getTransactionData()
            .equals(other.getTransactionData());
      }
      result = result && (getDataType()
          == other.getDataType());
      result = result && getData()
          .equals(other.getData());
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptorForType().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType().hashCode();
      hash = (37 * hash) + ENTITYTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getEntityType().hashCode();
      hash = (37 * hash) + ENTITYIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEntityIdMSB());
      hash = (37 * hash) + ENTITYIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEntityIdLSB());
      hash = (37 * hash) + RULECHAINIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRuleChainIdMSB());
      hash = (37 * hash) + RULECHAINIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRuleChainIdLSB());
      hash = (37 * hash) + RULENODEIDMSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRuleNodeIdMSB());
      hash = (37 * hash) + RULENODEIDLSB_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRuleNodeIdLSB());
      hash = (37 * hash) + CLUSTERPARTITION_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getClusterPartition());
      if (hasMetaData()) {
        hash = (37 * hash) + METADATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetaData().hashCode();
      }
      if (hasTransactionData()) {
        hash = (37 * hash) + TRANSACTIONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getTransactionData().hashCode();
      }
      hash = (37 * hash) + DATATYPE_FIELD_NUMBER;
      hash = (53 * hash) + getDataType();
      hash = (37 * hash) + DATA_FIELD_NUMBER;
      hash = (53 * hash) + getData().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code msgqueue.TbMsgProto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:msgqueue.TbMsgProto)
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgProto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgProto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto.class, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto.Builder.class);
      }

      // Construct using org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        id_ = "";

        type_ = "";

        entityType_ = "";

        entityIdMSB_ = 0L;

        entityIdLSB_ = 0L;

        ruleChainIdMSB_ = 0L;

        ruleChainIdLSB_ = 0L;

        ruleNodeIdMSB_ = 0L;

        ruleNodeIdLSB_ = 0L;

        clusterPartition_ = 0L;

        if (metaDataBuilder_ == null) {
          metaData_ = null;
        } else {
          metaData_ = null;
          metaDataBuilder_ = null;
        }
        if (transactionDataBuilder_ == null) {
          transactionData_ = null;
        } else {
          transactionData_ = null;
          transactionDataBuilder_ = null;
        }
        dataType_ = 0;

        data_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.internal_static_msgqueue_TbMsgProto_descriptor;
      }

      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto getDefaultInstanceForType() {
        return org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto.getDefaultInstance();
      }

      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto build() {
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto buildPartial() {
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto result = new org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto(this);
        result.id_ = id_;
        result.type_ = type_;
        result.entityType_ = entityType_;
        result.entityIdMSB_ = entityIdMSB_;
        result.entityIdLSB_ = entityIdLSB_;
        result.ruleChainIdMSB_ = ruleChainIdMSB_;
        result.ruleChainIdLSB_ = ruleChainIdLSB_;
        result.ruleNodeIdMSB_ = ruleNodeIdMSB_;
        result.ruleNodeIdLSB_ = ruleNodeIdLSB_;
        result.clusterPartition_ = clusterPartition_;
        if (metaDataBuilder_ == null) {
          result.metaData_ = metaData_;
        } else {
          result.metaData_ = metaDataBuilder_.build();
        }
        if (transactionDataBuilder_ == null) {
          result.transactionData_ = transactionData_;
        } else {
          result.transactionData_ = transactionDataBuilder_.build();
        }
        result.dataType_ = dataType_;
        result.data_ = data_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto) {
          return mergeFrom((org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto other) {
        if (other == org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          onChanged();
        }
        if (!other.getType().isEmpty()) {
          type_ = other.type_;
          onChanged();
        }
        if (!other.getEntityType().isEmpty()) {
          entityType_ = other.entityType_;
          onChanged();
        }
        if (other.getEntityIdMSB() != 0L) {
          setEntityIdMSB(other.getEntityIdMSB());
        }
        if (other.getEntityIdLSB() != 0L) {
          setEntityIdLSB(other.getEntityIdLSB());
        }
        if (other.getRuleChainIdMSB() != 0L) {
          setRuleChainIdMSB(other.getRuleChainIdMSB());
        }
        if (other.getRuleChainIdLSB() != 0L) {
          setRuleChainIdLSB(other.getRuleChainIdLSB());
        }
        if (other.getRuleNodeIdMSB() != 0L) {
          setRuleNodeIdMSB(other.getRuleNodeIdMSB());
        }
        if (other.getRuleNodeIdLSB() != 0L) {
          setRuleNodeIdLSB(other.getRuleNodeIdLSB());
        }
        if (other.getClusterPartition() != 0L) {
          setClusterPartition(other.getClusterPartition());
        }
        if (other.hasMetaData()) {
          mergeMetaData(other.getMetaData());
        }
        if (other.hasTransactionData()) {
          mergeTransactionData(other.getTransactionData());
        }
        if (other.getDataType() != 0) {
          setDataType(other.getDataType());
        }
        if (!other.getData().isEmpty()) {
          data_ = other.data_;
          onChanged();
        }
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object id_ = "";
      /**
       * <code>optional string id = 1;</code>
       */
      public java.lang.String getId() {
        java.lang.Object ref = id_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string id = 1;</code>
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        java.lang.Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string id = 1;</code>
       */
      public Builder setId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string id = 1;</code>
       */
      public Builder clearId() {
        
        id_ = getDefaultInstance().getId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string id = 1;</code>
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        id_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object type_ = "";
      /**
       * <code>optional string type = 2;</code>
       */
      public java.lang.String getType() {
        java.lang.Object ref = type_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          type_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string type = 2;</code>
       */
      public com.google.protobuf.ByteString
          getTypeBytes() {
        java.lang.Object ref = type_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          type_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string type = 2;</code>
       */
      public Builder setType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string type = 2;</code>
       */
      public Builder clearType() {
        
        type_ = getDefaultInstance().getType();
        onChanged();
        return this;
      }
      /**
       * <code>optional string type = 2;</code>
       */
      public Builder setTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        type_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object entityType_ = "";
      /**
       * <code>optional string entityType = 3;</code>
       */
      public java.lang.String getEntityType() {
        java.lang.Object ref = entityType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          entityType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string entityType = 3;</code>
       */
      public com.google.protobuf.ByteString
          getEntityTypeBytes() {
        java.lang.Object ref = entityType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          entityType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string entityType = 3;</code>
       */
      public Builder setEntityType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        entityType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 3;</code>
       */
      public Builder clearEntityType() {
        
        entityType_ = getDefaultInstance().getEntityType();
        onChanged();
        return this;
      }
      /**
       * <code>optional string entityType = 3;</code>
       */
      public Builder setEntityTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        entityType_ = value;
        onChanged();
        return this;
      }

      private long entityIdMSB_ ;
      /**
       * <code>optional int64 entityIdMSB = 4;</code>
       */
      public long getEntityIdMSB() {
        return entityIdMSB_;
      }
      /**
       * <code>optional int64 entityIdMSB = 4;</code>
       */
      public Builder setEntityIdMSB(long value) {
        
        entityIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityIdMSB = 4;</code>
       */
      public Builder clearEntityIdMSB() {
        
        entityIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long entityIdLSB_ ;
      /**
       * <code>optional int64 entityIdLSB = 5;</code>
       */
      public long getEntityIdLSB() {
        return entityIdLSB_;
      }
      /**
       * <code>optional int64 entityIdLSB = 5;</code>
       */
      public Builder setEntityIdLSB(long value) {
        
        entityIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityIdLSB = 5;</code>
       */
      public Builder clearEntityIdLSB() {
        
        entityIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private long ruleChainIdMSB_ ;
      /**
       * <code>optional int64 ruleChainIdMSB = 6;</code>
       */
      public long getRuleChainIdMSB() {
        return ruleChainIdMSB_;
      }
      /**
       * <code>optional int64 ruleChainIdMSB = 6;</code>
       */
      public Builder setRuleChainIdMSB(long value) {
        
        ruleChainIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ruleChainIdMSB = 6;</code>
       */
      public Builder clearRuleChainIdMSB() {
        
        ruleChainIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long ruleChainIdLSB_ ;
      /**
       * <code>optional int64 ruleChainIdLSB = 7;</code>
       */
      public long getRuleChainIdLSB() {
        return ruleChainIdLSB_;
      }
      /**
       * <code>optional int64 ruleChainIdLSB = 7;</code>
       */
      public Builder setRuleChainIdLSB(long value) {
        
        ruleChainIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ruleChainIdLSB = 7;</code>
       */
      public Builder clearRuleChainIdLSB() {
        
        ruleChainIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private long ruleNodeIdMSB_ ;
      /**
       * <code>optional int64 ruleNodeIdMSB = 8;</code>
       */
      public long getRuleNodeIdMSB() {
        return ruleNodeIdMSB_;
      }
      /**
       * <code>optional int64 ruleNodeIdMSB = 8;</code>
       */
      public Builder setRuleNodeIdMSB(long value) {
        
        ruleNodeIdMSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ruleNodeIdMSB = 8;</code>
       */
      public Builder clearRuleNodeIdMSB() {
        
        ruleNodeIdMSB_ = 0L;
        onChanged();
        return this;
      }

      private long ruleNodeIdLSB_ ;
      /**
       * <code>optional int64 ruleNodeIdLSB = 9;</code>
       */
      public long getRuleNodeIdLSB() {
        return ruleNodeIdLSB_;
      }
      /**
       * <code>optional int64 ruleNodeIdLSB = 9;</code>
       */
      public Builder setRuleNodeIdLSB(long value) {
        
        ruleNodeIdLSB_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ruleNodeIdLSB = 9;</code>
       */
      public Builder clearRuleNodeIdLSB() {
        
        ruleNodeIdLSB_ = 0L;
        onChanged();
        return this;
      }

      private long clusterPartition_ ;
      /**
       * <code>optional int64 clusterPartition = 10;</code>
       */
      public long getClusterPartition() {
        return clusterPartition_;
      }
      /**
       * <code>optional int64 clusterPartition = 10;</code>
       */
      public Builder setClusterPartition(long value) {
        
        clusterPartition_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clusterPartition = 10;</code>
       */
      public Builder clearClusterPartition() {
        
        clusterPartition_ = 0L;
        onChanged();
        return this;
      }

      private org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto metaData_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.Builder, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProtoOrBuilder> metaDataBuilder_;
      /**
       * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
       */
      public boolean hasMetaData() {
        return metaDataBuilder_ != null || metaData_ != null;
      }
      /**
       * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
       */
      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto getMetaData() {
        if (metaDataBuilder_ == null) {
          return metaData_ == null ? org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.getDefaultInstance() : metaData_;
        } else {
          return metaDataBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
       */
      public Builder setMetaData(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto value) {
        if (metaDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          metaData_ = value;
          onChanged();
        } else {
          metaDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
       */
      public Builder setMetaData(
          org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.Builder builderForValue) {
        if (metaDataBuilder_ == null) {
          metaData_ = builderForValue.build();
          onChanged();
        } else {
          metaDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
       */
      public Builder mergeMetaData(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto value) {
        if (metaDataBuilder_ == null) {
          if (metaData_ != null) {
            metaData_ =
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.newBuilder(metaData_).mergeFrom(value).buildPartial();
          } else {
            metaData_ = value;
          }
          onChanged();
        } else {
          metaDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
       */
      public Builder clearMetaData() {
        if (metaDataBuilder_ == null) {
          metaData_ = null;
          onChanged();
        } else {
          metaData_ = null;
          metaDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
       */
      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.Builder getMetaDataBuilder() {
        
        onChanged();
        return getMetaDataFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
       */
      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProtoOrBuilder getMetaDataOrBuilder() {
        if (metaDataBuilder_ != null) {
          return metaDataBuilder_.getMessageOrBuilder();
        } else {
          return metaData_ == null ?
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.getDefaultInstance() : metaData_;
        }
      }
      /**
       * <code>optional .msgqueue.TbMsgMetaDataProto metaData = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.Builder, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProtoOrBuilder> 
          getMetaDataFieldBuilder() {
        if (metaDataBuilder_ == null) {
          metaDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProto.Builder, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgMetaDataProtoOrBuilder>(
                  getMetaData(),
                  getParentForChildren(),
                  isClean());
          metaData_ = null;
        }
        return metaDataBuilder_;
      }

      private org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto transactionData_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.Builder, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProtoOrBuilder> transactionDataBuilder_;
      /**
       * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
       */
      public boolean hasTransactionData() {
        return transactionDataBuilder_ != null || transactionData_ != null;
      }
      /**
       * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
       */
      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto getTransactionData() {
        if (transactionDataBuilder_ == null) {
          return transactionData_ == null ? org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.getDefaultInstance() : transactionData_;
        } else {
          return transactionDataBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
       */
      public Builder setTransactionData(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto value) {
        if (transactionDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          transactionData_ = value;
          onChanged();
        } else {
          transactionDataBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
       */
      public Builder setTransactionData(
          org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.Builder builderForValue) {
        if (transactionDataBuilder_ == null) {
          transactionData_ = builderForValue.build();
          onChanged();
        } else {
          transactionDataBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
       */
      public Builder mergeTransactionData(org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto value) {
        if (transactionDataBuilder_ == null) {
          if (transactionData_ != null) {
            transactionData_ =
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.newBuilder(transactionData_).mergeFrom(value).buildPartial();
          } else {
            transactionData_ = value;
          }
          onChanged();
        } else {
          transactionDataBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
       */
      public Builder clearTransactionData() {
        if (transactionDataBuilder_ == null) {
          transactionData_ = null;
          onChanged();
        } else {
          transactionData_ = null;
          transactionDataBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
       */
      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.Builder getTransactionDataBuilder() {
        
        onChanged();
        return getTransactionDataFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
       */
      public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProtoOrBuilder getTransactionDataOrBuilder() {
        if (transactionDataBuilder_ != null) {
          return transactionDataBuilder_.getMessageOrBuilder();
        } else {
          return transactionData_ == null ?
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.getDefaultInstance() : transactionData_;
        }
      }
      /**
       * <code>optional .msgqueue.TbMsgTransactionDataProto transactionData = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.Builder, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProtoOrBuilder> 
          getTransactionDataFieldBuilder() {
        if (transactionDataBuilder_ == null) {
          transactionDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProto.Builder, org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgTransactionDataProtoOrBuilder>(
                  getTransactionData(),
                  getParentForChildren(),
                  isClean());
          transactionData_ = null;
        }
        return transactionDataBuilder_;
      }

      private int dataType_ ;
      /**
       * <code>optional int32 dataType = 13;</code>
       */
      public int getDataType() {
        return dataType_;
      }
      /**
       * <code>optional int32 dataType = 13;</code>
       */
      public Builder setDataType(int value) {
        
        dataType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 dataType = 13;</code>
       */
      public Builder clearDataType() {
        
        dataType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object data_ = "";
      /**
       * <code>optional string data = 14;</code>
       */
      public java.lang.String getData() {
        java.lang.Object ref = data_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          data_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string data = 14;</code>
       */
      public com.google.protobuf.ByteString
          getDataBytes() {
        java.lang.Object ref = data_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          data_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string data = 14;</code>
       */
      public Builder setData(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        data_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string data = 14;</code>
       */
      public Builder clearData() {
        
        data_ = getDefaultInstance().getData();
        onChanged();
        return this;
      }
      /**
       * <code>optional string data = 14;</code>
       */
      public Builder setDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        data_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return this;
      }


      // @@protoc_insertion_point(builder_scope:msgqueue.TbMsgProto)
    }

    // @@protoc_insertion_point(class_scope:msgqueue.TbMsgProto)
    private static final org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto();
    }

    public static org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TbMsgProto>
        PARSER = new com.google.protobuf.AbstractParser<TbMsgProto>() {
      public TbMsgProto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new TbMsgProto(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TbMsgProto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TbMsgProto> getParserForType() {
      return PARSER;
    }

    public org.thingsboard.server.common.msg.gen.MsgProtos.TbMsgProto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_msgqueue_TbMsgMetaDataProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_msgqueue_TbMsgMetaDataProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_msgqueue_TbMsgMetaDataProto_DataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_msgqueue_TbMsgMetaDataProto_DataEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_msgqueue_TbMsgTransactionDataProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_msgqueue_TbMsgTransactionDataProto_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_msgqueue_TbMsgProto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_msgqueue_TbMsgProto_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013tbmsg.proto\022\010msgqueue\"w\n\022TbMsgMetaData" +
      "Proto\0224\n\004data\030\001 \003(\0132&.msgqueue.TbMsgMeta" +
      "DataProto.DataEntry\032+\n\tDataEntry\022\013\n\003key\030" +
      "\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"e\n\031TbMsgTransac" +
      "tionDataProto\022\n\n\002id\030\001 \001(\t\022\022\n\nentityType\030" +
      "\002 \001(\t\022\023\n\013entityIdMSB\030\003 \001(\003\022\023\n\013entityIdLS" +
      "B\030\004 \001(\003\"\352\002\n\nTbMsgProto\022\n\n\002id\030\001 \001(\t\022\014\n\004ty" +
      "pe\030\002 \001(\t\022\022\n\nentityType\030\003 \001(\t\022\023\n\013entityId" +
      "MSB\030\004 \001(\003\022\023\n\013entityIdLSB\030\005 \001(\003\022\026\n\016ruleCh" +
      "ainIdMSB\030\006 \001(\003\022\026\n\016ruleChainIdLSB\030\007 \001(\003\022\025",
      "\n\rruleNodeIdMSB\030\010 \001(\003\022\025\n\rruleNodeIdLSB\030\t" +
      " \001(\003\022\030\n\020clusterPartition\030\n \001(\003\022.\n\010metaDa" +
      "ta\030\013 \001(\0132\034.msgqueue.TbMsgMetaDataProto\022<" +
      "\n\017transactionData\030\014 \001(\0132#.msgqueue.TbMsg" +
      "TransactionDataProto\022\020\n\010dataType\030\r \001(\005\022\014" +
      "\n\004data\030\016 \001(\tB2\n%org.thingsboard.server.c" +
      "ommon.msg.genB\tMsgProtosb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_msgqueue_TbMsgMetaDataProto_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_msgqueue_TbMsgMetaDataProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_msgqueue_TbMsgMetaDataProto_descriptor,
        new java.lang.String[] { "Data", });
    internal_static_msgqueue_TbMsgMetaDataProto_DataEntry_descriptor =
      internal_static_msgqueue_TbMsgMetaDataProto_descriptor.getNestedTypes().get(0);
    internal_static_msgqueue_TbMsgMetaDataProto_DataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_msgqueue_TbMsgMetaDataProto_DataEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_msgqueue_TbMsgTransactionDataProto_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_msgqueue_TbMsgTransactionDataProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_msgqueue_TbMsgTransactionDataProto_descriptor,
        new java.lang.String[] { "Id", "EntityType", "EntityIdMSB", "EntityIdLSB", });
    internal_static_msgqueue_TbMsgProto_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_msgqueue_TbMsgProto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_msgqueue_TbMsgProto_descriptor,
        new java.lang.String[] { "Id", "Type", "EntityType", "EntityIdMSB", "EntityIdLSB", "RuleChainIdMSB", "RuleChainIdLSB", "RuleNodeIdMSB", "RuleNodeIdLSB", "ClusterPartition", "MetaData", "TransactionData", "DataType", "Data", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
