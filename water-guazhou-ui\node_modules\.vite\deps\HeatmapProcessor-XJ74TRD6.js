import {
  p
} from "./chunk-MMSLNVBL.js";
import {
  o as o3
} from "./chunk-TFWV44LH.js";
import {
  o as o2
} from "./chunk-RRNRSHX3.js";
import "./chunk-Q4VCSCSY.js";
import {
  m
} from "./chunk-GE5PSQPZ.js";
import {
  s
} from "./chunk-2ILOD42U.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  o,
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/features/processors/HeatmapProcessor.js
var p2 = class {
  constructor(e2, t) {
    this.offset = e2, this.extent = t;
  }
};
function c(e2) {
  const t = e2.key, s2 = /* @__PURE__ */ new Map(), r2 = 256, i = o2, o4 = e2.tileInfoView.tileInfo.isWrappable;
  return s2.set(o3(t, -1, -1, o4).id, new p2([-i, -i], [i - r2, i - r2, i, i])), s2.set(o3(t, 0, -1, o4).id, new p2([0, -i], [0, i - r2, i, i])), s2.set(o3(t, 1, -1, o4).id, new p2([i, -i], [0, i - r2, r2, i])), s2.set(o3(t, -1, 0, o4).id, new p2([-i, 0], [i - r2, 0, i, i])), s2.set(o3(t, 1, 0, o4).id, new p2([i, 0], [0, 0, r2, i])), s2.set(o3(t, -1, 1, o4).id, new p2([-i, i], [i - r2, 0, i, r2])), s2.set(o3(t, 0, 1, o4).id, new p2([0, i], [0, 0, i, r2])), s2.set(o3(t, 1, 1, o4).id, new p2([i, i], [0, 0, r2, r2])), s2;
}
var l = class extends p {
  constructor() {
    super(...arguments), this.type = "heatmap", this._tileKeyToFeatureSets = /* @__PURE__ */ new Map();
  }
  initialize() {
    this.handles.add([this.tileStore.on("update", this.onTileUpdate.bind(this))]);
  }
  async update(e2, t) {
    const s2 = t.schema.processors[0];
    if ("heatmap" !== s2.type) return;
    m(this._schema, s2) && (e2.mesh = true, this._schema = s2);
  }
  onTileUpdate(e2) {
    for (const t of e2.removed) this._tileKeyToFeatureSets.delete(t.key.id);
  }
  onTileClear(e2) {
    const t = { clear: true };
    return this._tileKeyToFeatureSets.delete(e2.key.id), this.remoteClient.invoke("tileRenderer.onTileData", { tileKey: e2.id, data: t });
  }
  async onTileMessage(e2, r2, i) {
    this._tileKeyToFeatureSets.has(e2.key.id) || this._tileKeyToFeatureSets.set(e2.key.id, /* @__PURE__ */ new Map());
    const a2 = this._tileKeyToFeatureSets.get(e2.key.id);
    if (a2 && r(r2.addOrUpdate) && r2.addOrUpdate.hasFeatures && a2.set(r2.addOrUpdate.instance, r2), r2.end) {
      const t = [], r3 = c(e2);
      this._tileKeyToFeatureSets.forEach((i2, o4) => {
        if (o4 === e2.key.id) i2.forEach((e3) => o(e3.addOrUpdate, (e4) => t.push(e4)));
        else if (r3.has(o4)) {
          const e3 = r3.get(o4), [a4, n2] = e3.offset;
          i2.forEach((e4) => o(e4.addOrUpdate, (e5) => {
            const s2 = e5.transform(a4, n2, 1, 1);
            t.push(s2);
          }));
        }
      });
      const a3 = s(t, this._schema.mesh, 512, 512), n = { tileKey: e2.key.id, intensityInfo: a3 }, d = [a3.matrix];
      return this.remoteClient.invoke("tileRenderer.onTileData", n, { ...i, transferList: d });
    }
  }
  onTileError(e2, t, s2) {
    return this.remoteClient.invoke("tileRenderer.onTileError", { tileKey: e2.id, error: t }, s2);
  }
};
l = e([a("esri.views.2d.layers.features.processors.HeatmapProcessor")], l);
var h = l;
export {
  h as default
};
//# sourceMappingURL=HeatmapProcessor-XJ74TRD6.js.map
