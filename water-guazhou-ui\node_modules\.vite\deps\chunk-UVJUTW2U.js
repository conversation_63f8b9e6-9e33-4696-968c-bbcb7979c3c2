import {
  d,
  g
} from "./chunk-HTXGAKOK.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/LayerFloorInfo.js
var l2;
var i = l2 = class extends l {
  constructor(o2) {
    super(o2), this.floorField = null, this.viewAllMode = false, this.viewAllLevelIds = new j();
  }
  clone() {
    return new l2({ floorField: this.floorField, viewAllMode: this.viewAllMode, viewAllLevelIds: this.viewAllLevelIds });
  }
};
e([y({ type: String, json: { write: true } })], i.prototype, "floorField", void 0), e([y({ json: { read: false, write: false } })], i.prototype, "viewAllMode", void 0), e([y({ json: { read: false, write: false } })], i.prototype, "viewAllLevelIds", void 0), i = l2 = e([a("esri.layers.support.LayerFloorInfo")], i);
var p = i;

// node_modules/@arcgis/core/layers/support/serviceCapabilitiesUtils.js
var r2 = { name: "supportsName", size: "supportsSize", contentType: "supportsContentType", keywords: "supportsKeywords", exifInfo: "supportsExifInfo" };
function p2(t, s, e2) {
  return !!(t && t.hasOwnProperty(s) ? t[s] : e2);
}
function o(t, s, e2) {
  return t && t.hasOwnProperty(s) ? t[s] : e2;
}
function u(t) {
  var _a;
  const s = (_a = t == null ? void 0 : t.supportedSpatialAggregationStatistics) == null ? void 0 : _a.map((t2) => t2.toLowerCase());
  return { envelope: !!(s == null ? void 0 : s.includes("envelopeaggregate")), centroid: !!(s == null ? void 0 : s.includes("centroidaggregate")), convexHull: !!(s == null ? void 0 : s.includes("convexhullaggregate")) };
}
function a2(t, s) {
  var _a;
  const e2 = (_a = t == null ? void 0 : t.supportedOperationsWithCacheHint) == null ? void 0 : _a.map((t2) => t2.toLowerCase());
  return !!(e2 == null ? void 0 : e2.includes(s.toLowerCase()));
}
function n(t, s) {
  return { analytics: i2(t), attachment: c(t), data: d2(t), metadata: l3(t), operations: y2(t.capabilities, t, s), query: m(t, s), queryRelated: h(t), queryTopFeatures: g2(t), editing: C(t) };
}
function i2(t) {
  return { supportsCacheHint: a2(t.advancedQueryCapabilities, "queryAnalytics") };
}
function c(t) {
  const s = t.attachmentProperties, e2 = { supportsName: false, supportsSize: false, supportsContentType: false, supportsKeywords: false, supportsExifInfo: false, supportsCacheHint: a2(t.advancedQueryCapabilities, "queryAttachments"), supportsResize: p2(t, "supportsAttachmentsResizing", false) };
  return s && Array.isArray(s) && s.forEach((t2) => {
    const s2 = r2[t2.name];
    s2 && (e2[s2] = !!t2.isEnabled);
  }), e2;
}
function d2(t) {
  return { isVersioned: p2(t, "isDataVersioned", false), supportsAttachment: p2(t, "hasAttachments", false), supportsM: p2(t, "hasM", false), supportsZ: p2(t, "hasZ", false) };
}
function l3(t) {
  return { supportsAdvancedFieldProperties: p2(t, "supportsFieldDescriptionProperty", false) };
}
function y2(e2, r3, o2) {
  const u2 = e2 ? e2.toLowerCase().split(",").map((t) => t.trim()) : [], a3 = o2 ? d(o2) : null, n2 = u2.includes(r(a3) && "MapServer" === a3.serverType ? "data" : "query"), i3 = u2.includes("editing") && !r3.datesInUnknownTimezone;
  let c2 = i3 && u2.includes("create"), d3 = i3 && u2.includes("delete"), l4 = i3 && u2.includes("update");
  const y3 = u2.includes("changetracking"), m2 = r3.advancedQueryCapabilities;
  return i3 && !(c2 || d3 || l4) && (c2 = d3 = l4 = true), { supportsCalculate: p2(r3, "supportsCalculate", false), supportsTruncate: p2(r3, "supportsTruncate", false), supportsValidateSql: p2(r3, "supportsValidateSql", false), supportsAdd: c2, supportsDelete: d3, supportsEditing: i3, supportsChangeTracking: y3, supportsQuery: n2, supportsQueryAnalytics: p2(m2, "supportsQueryAnalytic", false), supportsQueryAttachments: p2(m2, "supportsQueryAttachments", false), supportsQueryTopFeatures: p2(m2, "supportsTopFeaturesQuery", false), supportsResizeAttachments: p2(r3, "supportsAttachmentsResizing", false), supportsSync: u2.includes("sync"), supportsUpdate: l4, supportsExceedsLimitStatistics: p2(r3, "supportsExceedsLimitStatistics", false) };
}
function m(t, s) {
  const r3 = t.advancedQueryCapabilities, n2 = t.ownershipBasedAccessControlForFeatures, i3 = t.archivingInfo, c2 = t.currentVersion, d3 = s == null ? void 0 : s.includes("MapServer"), l4 = !d3 || c2 >= has("mapserver-pbf-version-support"), y3 = g(s), m2 = new Set((t.supportedQueryFormats ?? "").split(",").map((t2) => t2.toLowerCase().trim()));
  return { supportsStatistics: p2(r3, "supportsStatistics", t.supportsStatistics), supportsPercentileStatistics: p2(r3, "supportsPercentileStatistics", false), supportsSpatialAggregationStatistics: p2(r3, "supportsSpatialAggregationStatistics", false), supportedSpatialAggregationStatistics: u(r3), supportsCentroid: p2(r3, "supportsReturningGeometryCentroid", false), supportsDistance: p2(r3, "supportsQueryWithDistance", false), supportsDistinct: p2(r3, "supportsDistinct", t.supportsAdvancedQueries), supportsExtent: p2(r3, "supportsReturningQueryExtent", false), supportsGeometryProperties: p2(r3, "supportsReturningGeometryProperties", false), supportsHavingClause: p2(r3, "supportsHavingClause", false), supportsOrderBy: p2(r3, "supportsOrderBy", t.supportsAdvancedQueries), supportsPagination: p2(r3, "supportsPagination", false), supportsQuantization: p2(t, "supportsCoordinatesQuantization", false), supportsQuantizationEditMode: p2(t, "supportsQuantizationEditMode", false), supportsQueryGeometry: p2(t, "supportsReturningQueryGeometry", false), supportsResultType: p2(r3, "supportsQueryWithResultType", false), supportsMaxRecordCountFactor: p2(r3, "supportsMaxRecordCountFactor", false), supportsSqlExpression: p2(r3, "supportsSqlExpression", false), supportsStandardizedQueriesOnly: p2(t, "useStandardizedQueries", false), supportsTopFeaturesQuery: p2(r3, "supportsTopFeaturesQuery", false), supportsQueryByOthers: p2(n2, "allowOthersToQuery", true), supportsHistoricMoment: p2(i3, "supportsQueryWithHistoricMoment", false), supportsFormatPBF: l4 && m2.has("pbf"), supportsDisjointSpatialRelationship: p2(r3, "supportsDisjointSpatialRel", false), supportsCacheHint: p2(r3, "supportsQueryWithCacheHint", false) || a2(r3, "query"), supportsDefaultSpatialReference: p2(r3, "supportsDefaultSR", false), supportsCompactGeometry: y3, supportsFullTextSearch: p2(r3, "supportsFullTextSearch", false), maxRecordCountFactor: o(t, "maxRecordCountFactor", void 0), maxRecordCount: o(t, "maxRecordCount", void 0), standardMaxRecordCount: o(t, "standardMaxRecordCount", void 0), tileMaxRecordCount: o(t, "tileMaxRecordCount", void 0) };
}
function h(t) {
  const s = t.advancedQueryCapabilities, e2 = p2(s, "supportsAdvancedQueryRelated", false);
  return { supportsPagination: p2(s, "supportsQueryRelatedPagination", false), supportsCount: e2, supportsOrderBy: e2, supportsCacheHint: a2(s, "queryRelated") };
}
function g2(t) {
  return { supportsCacheHint: a2(t.advancedQueryCapabilities, "queryTopFilter") };
}
function C(t) {
  const s = t.ownershipBasedAccessControlForFeatures;
  return { supportsGeometryUpdate: p2(t, "allowGeometryUpdates", true), supportsGlobalId: p2(t, "supportsApplyEditsWithGlobalIds", false), supportsReturnServiceEditsInSourceSpatialReference: p2(t, "supportsReturnServiceEditsInSourceSR", false), supportsRollbackOnFailure: p2(t, "supportsRollbackOnFailureParameter", false), supportsUpdateWithoutM: p2(t, "allowUpdateWithoutMValues", false), supportsUploadWithItemId: p2(t, "supportsAttachmentsByUploadId", false), supportsDeleteByAnonymous: p2(s, "allowAnonymousToDelete", true), supportsDeleteByOthers: p2(s, "allowOthersToDelete", true), supportsUpdateByAnonymous: p2(s, "allowAnonymousToUpdate", true), supportsUpdateByOthers: p2(s, "allowOthersToUpdate", true) };
}

export {
  p,
  n
};
//# sourceMappingURL=chunk-UVJUTW2U.js.map
