<template>
  <div class="conservation-water-level">
    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="80px">
        <el-form-item label="测点名称" prop="stationName">
          <el-input
            v-model="queryParams.stationName"
            placeholder="请输入测点名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="数据来源" prop="dataSource">
          <el-select v-model="queryParams.dataSource" placeholder="请选择数据来源" clearable style="width: 150px">
            <el-option label="手动录入" :value="1" />
            <el-option label="设备采集" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="记录时间" prop="recordTime">
          <el-date-picker
            v-model="recordTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="x"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <div class="table-title">涵养水位数据列表</div>
        <div class="table-actions">
          <el-button type="primary" icon="Plus" @click="handleAdd">新增数据</el-button>
          <el-button type="success" icon="Upload" @click="handleImport">批量导入</el-button>
          <el-button type="info" icon="Download" @click="handleExport">导出数据</el-button>
          <el-button type="warning" icon="DataAnalysis" @click="handleAnalysis">智能分析</el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="waterLevelList"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="stationName" label="测点名称" width="120">
          <template #default="scope">
            {{ scope.row.stationName || scope.row.stationId || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="stationLocation" label="测点位置" width="150" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.stationLocation || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="rawWaterLevel" label="原水液位(m)" width="120" align="center">
          <template #default="scope">
            <span class="level-value">{{ formatNumber(scope.row.rawWaterLevel) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="groundwaterLevel" label="地下水位(m)" width="120" align="center">
          <template #default="scope">
            <span class="level-value">{{ formatNumber(scope.row.groundwaterLevel) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="levelChange" label="液位变化(m)" width="120" align="center">
          <template #default="scope">
            <span :class="getLevelChangeClass(scope.row.levelChange)">
              {{ formatNumber(scope.row.levelChange) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="rainfallAmount" label="降雨量(mm)" width="100" align="center">
          <template #default="scope">
            {{ formatNumber(scope.row.rainfallAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="evaporationAmount" label="蒸发量(mm)" width="100" align="center">
          <template #default="scope">
            {{ formatNumber(scope.row.evaporationAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="extractionAmount" label="开采量(m³)" width="120" align="center">
          <template #default="scope">
            {{ formatNumber(scope.row.extractionAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="dataSource" label="数据来源" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.dataSource === 1 ? 'primary' : 'success'">
              {{ scope.row.dataSource === 1 ? '手动录入' : '设备采集' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="recordTime" label="记录时间" width="160" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.recordTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="creatorName" label="创建人" width="100" align="center">
          <template #default="scope">
            {{ scope.row.creatorName || scope.row.creator || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" link icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <WaterLevelDialog
      v-model:visible="dialogVisible"
      :form-data="formData"
      :dialog-type="dialogType"
      @confirm="handleDialogConfirm"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importVisible"
      @confirm="handleImportConfirm"
    />

    <!-- 智能分析对话框 -->
    <AnalysisDialog
      v-model:visible="analysisVisible"
      @confirm="handleAnalysisConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import {
  getWaterLevelList,
  deleteWaterLevel,
  type ConservationWaterLevel,
  type WaterLevelQueryParams
} from '@/api/waterSource/conservationWaterLevel'
import WaterLevelDialog from './components/WaterLevelDialog.vue'
import ImportDialog from './components/ImportDialog.vue'
import AnalysisDialog from './components/AnalysisDialog.vue'
import { formatDate } from '@/utils/DateFormatter'
import { dwnai } from '@/utils/processNumber'

// 格式化函数
const formatDateTime = (date: string | number | Date | undefined) => {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
}

const formatNumber = (value: number | string | undefined, precision = 2) => {
  if (value === undefined || value === null || value === '') return '--'
  return dwnai(value)
}

// 响应式数据
const loading = ref(false)
const waterLevelList = ref<ConservationWaterLevel[]>([])
const total = ref(0)
const dialogVisible = ref(false)
const importVisible = ref(false)
const analysisVisible = ref(false)
const dialogType = ref<'add' | 'edit' | 'view'>('add')
const formData = ref<ConservationWaterLevel>({} as ConservationWaterLevel)

// 查询表单引用
const queryFormRef = ref<FormInstance>()

// 查询参数
const queryParams = reactive<WaterLevelQueryParams>({
  pageNum: 1,
  pageSize: 10,
  stationName: '',
  dataSource: undefined
})

// 记录时间范围
const recordTimeRange = ref<[number, number] | null>(null)

// 计算属性
const recordTimeComputed = computed(() => {
  if (recordTimeRange.value) {
    return {
      startTime: recordTimeRange.value[0],
      endTime: recordTimeRange.value[1]
    }
  }
  return { startTime: undefined, endTime: undefined }
})

// 获取液位变化样式类
const getLevelChangeClass = (levelChange: number | undefined) => {
  if (!levelChange) return ''
  if (levelChange > 0) return 'level-increase'
  if (levelChange < 0) return 'level-decrease'
  return ''
}

// 生命周期
onMounted(() => {
  getList()
})

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      ...recordTimeComputed.value
    }
    const response = await getWaterLevelList(params)
    if (response.data.code === 200) {
      waterLevelList.value = response.data.data.list
      total.value = response.data.data.total
    }
  } catch (error) {
    console.error('获取涵养水位数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  recordTimeRange.value = null
  queryParams.pageNum = 1
  getList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  getList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  getList()
}

// 新增
const handleAdd = () => {
  dialogType.value = 'add'
  formData.value = {
    dataSource: 1 // 默认手动录入
  } as ConservationWaterLevel
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: ConservationWaterLevel) => {
  dialogType.value = 'edit'
  formData.value = { ...row }
  dialogVisible.value = true
}

// 查看
const handleView = (row: ConservationWaterLevel) => {
  dialogType.value = 'view'
  formData.value = { ...row }
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: ConservationWaterLevel) => {
  try {
    await ElMessageBox.confirm('确定要删除这条涵养水位数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteWaterLevel(row.id!)
    if (response.data.code === 200) {
      ElMessage.success('删除成功')
      getList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 对话框确认
const handleDialogConfirm = () => {
  getList()
}

// 导入
const handleImport = () => {
  importVisible.value = true
}

// 导入确认
const handleImportConfirm = () => {
  getList()
}

// 智能分析
const handleAnalysis = () => {
  analysisVisible.value = true
}

// 分析确认
const handleAnalysisConfirm = () => {
  // 可以跳转到分析结果页面或刷新数据
  ElMessage.success('分析任务已启动')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}
</script>

<style scoped>
.conservation-water-level {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.level-value {
  font-weight: 600;
}

.level-increase {
  color: #67c23a;
  font-weight: 600;
}

.level-decrease {
  color: #f56c6c;
  font-weight: 600;
}
</style>
