<template>
  <div class="conservation-water-level">
    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="80px">
        <el-form-item label="测点名称" prop="stationId">
          <el-select
            v-model="queryParams.stationId"
            placeholder="请选择测点"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option label="全部测点" value="" />
            <el-option
              v-for="station in stationList"
              :key="station.id"
              :label="station.name"
              :value="station.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据来源" prop="dataSource">
          <el-select v-model="queryParams.dataSource" placeholder="请选择数据来源" clearable style="width: 150px">
            <el-option label="手动录入" :value="1" />
            <el-option label="设备采集" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="记录时间" prop="recordTime">
          <el-date-picker
            v-model="recordTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="x"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <div class="table-title">
          <h3>涵养水位数据列表</h3>
          <p>地下水涵养水位监测和分析数据管理</p>
        </div>
        <div class="table-actions">
          <el-button type="primary" icon="Plus" size="default" @click="handleAdd">
            新增数据
          </el-button>
          <el-button type="success" icon="Upload" size="default" @click="handleImport">
            批量导入
          </el-button>
          <el-button type="info" icon="Download" size="default" @click="handleExport">
            导出数据
          </el-button>
          <el-button type="warning" icon="DataAnalysis" size="default" @click="handleAnalysis">
            智能分析
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="waterLevelList"
        border
        stripe
        style="width: 100%"
        table-layout="auto"
      >
        <el-table-column prop="stationName" label="测点名称" min-width="140">
          <template #default="scope">
            {{ getStationName(scope.row.stationId) }}
          </template>
        </el-table-column>
        <el-table-column prop="stationLocation" label="测点位置" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ getStationLocation(scope.row.stationId) }}
          </template>
        </el-table-column>
        <el-table-column prop="rawWaterLevel" label="原水液位(m)" min-width="130" align="center">
          <template #default="scope">
            <span class="level-value">{{ formatNumber(scope.row.rawWaterLevel) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="groundwaterLevel" label="地下水位(m)" min-width="130" align="center">
          <template #default="scope">
            <span class="level-value">{{ formatNumber(scope.row.groundwaterLevel) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="levelChange" label="液位变化(m)" min-width="130" align="center">
          <template #default="scope">
            <span :class="getLevelChangeClass(scope.row.levelChange)">
              {{ formatNumber(scope.row.levelChange) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="rainfallAmount" label="降雨量(mm)" min-width="120" align="center">
          <template #default="scope">
            {{ formatNumber(scope.row.rainfallAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="evaporationAmount" label="蒸发量(mm)" min-width="120" align="center">
          <template #default="scope">
            {{ formatNumber(scope.row.evaporationAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="extractionAmount" label="开采量(m³)" min-width="130" align="center">
          <template #default="scope">
            {{ formatNumber(scope.row.extractionAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="dataSource" label="数据来源" min-width="110" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.dataSource === 1 ? 'primary' : 'success'">
              {{ scope.row.dataSource === 1 ? '手动录入' : '设备采集' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="recordTime" label="记录时间" min-width="170" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.recordTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="creatorName" label="创建人" min-width="100" align="center">
          <template #default="scope">
            {{ scope.row.creatorName || scope.row.creator || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" align="center" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" link icon="View" size="small" @click="handleView(scope.row)">
                查看
              </el-button>
              <el-button type="success" link icon="Edit" size="small" @click="handleEdit(scope.row)">
                编辑
              </el-button>
              <el-button type="danger" link icon="Delete" size="small" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <WaterLevelDialog
      v-model:visible="dialogVisible"
      :form-data="formData"
      :dialog-type="dialogType"
      @confirm="handleDialogConfirm"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importVisible"
      @confirm="handleImportConfirm"
    />

    <!-- 智能分析对话框 -->
    <AnalysisDialog
      v-model:visible="analysisVisible"
      @confirm="handleAnalysisConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import {
  getWaterLevelList,
  deleteWaterLevel,
  type ConservationWaterLevel,
  type WaterLevelQueryParams
} from '@/api/waterSource/conservationWaterLevel'
import { getAllStations } from '@/api/station'
import WaterLevelDialog from './components/WaterLevelDialog.vue'
import ImportDialog from './components/ImportDialog.vue'
import AnalysisDialog from './components/AnalysisDialog.vue'
import { formatDate } from '@/utils/DateFormatter'
import { dwnai } from '@/utils/processNumber'

// 格式化函数
const formatDateTime = (date: string | number | Date | undefined) => {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
}

const formatNumber = (value: number | string | undefined, precision = 2) => {
  if (value === undefined || value === null || value === '') return '--'
  return dwnai(value)
}

// 响应式数据
const loading = ref(false)
const waterLevelList = ref<ConservationWaterLevel[]>([])
const total = ref(0)
const dialogVisible = ref(false)
const importVisible = ref(false)
const analysisVisible = ref(false)
const dialogType = ref<'add' | 'edit' | 'view'>('add')
const formData = ref<ConservationWaterLevel>({} as ConservationWaterLevel)

// 站点数据
const stationsMap = ref<Map<string, any>>(new Map())
const stationList = ref<any[]>([])

// 查询表单引用
const queryFormRef = ref<FormInstance>()

// 查询参数
const queryParams = reactive<WaterLevelQueryParams>({
  pageNum: 1,
  pageSize: 10,
  stationId: '',
  dataSource: undefined
})

// 记录时间范围
const recordTimeRange = ref<[number, number] | []>([])

// 计算属性
const recordTimeComputed = computed(() => {
  if (recordTimeRange.value) {
    return {
      startTime: recordTimeRange.value[0],
      endTime: recordTimeRange.value[1]
    }
  }
  return { startTime: undefined, endTime: undefined }
})

// 获取液位变化样式类
const getLevelChangeClass = (levelChange: number | undefined) => {
  if (!levelChange) return ''
  if (levelChange > 0) return 'level-increase'
  if (levelChange < 0) return 'level-decrease'
  return ''
}

// 获取站点名称
const getStationName = (stationId: string) => {
  const station = stationsMap.value.get(stationId)
  return station?.name || stationId || '--'
}

// 获取站点位置
const getStationLocation = (stationId: string) => {
  const station = stationsMap.value.get(stationId)
  return station?.location || '--'
}

// 生命周期
onMounted(() => {
  loadStations()
  getList()
})

// 加载站点数据
const loadStations = async () => {
  try {
    const response = await getAllStations({ type: '水源地' })
    if (response.status === 200) {
      // 后端返回的是PageData格式，数据在data字段中
      const stations = response.data.data || []
      stationsMap.value.clear()
      stationList.value = stations
      stations.forEach((station: any) => {
        stationsMap.value.set(station.id, station)
      })
    }
  } catch (error) {
    console.error('获取站点数据失败:', error)
  }
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      ...recordTimeComputed.value
    }
    const response = await getWaterLevelList(params)
    if (response.data.code === 200) {
      waterLevelList.value = response.data.data.list
      total.value = response.data.data.total
    }
  } catch (error) {
    console.error('获取涵养水位数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  recordTimeRange.value = []
  queryParams.pageNum = 1
  getList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  getList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  getList()
}

// 新增
const handleAdd = () => {
  dialogType.value = 'add'
  formData.value = {
    dataSource: 1 // 默认手动录入
  } as ConservationWaterLevel
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: ConservationWaterLevel) => {
  dialogType.value = 'edit'
  formData.value = { ...row }
  dialogVisible.value = true
}

// 查看
const handleView = (row: ConservationWaterLevel) => {
  dialogType.value = 'view'
  formData.value = { ...row }
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: ConservationWaterLevel) => {
  try {
    await ElMessageBox.confirm('确定要删除这条涵养水位数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteWaterLevel(row.id!)
    if (response.data.code === 200) {
      ElMessage.success('删除成功')
      getList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 对话框确认
const handleDialogConfirm = () => {
  getList()
}

// 导入
const handleImport = () => {
  importVisible.value = true
}

// 导入确认
const handleImportConfirm = () => {
  getList()
}

// 智能分析
const handleAnalysis = () => {
  analysisVisible.value = true
}

// 分析确认
const handleAnalysisConfirm = () => {
  // 可以跳转到分析结果页面或刷新数据
  ElMessage.success('分析任务已启动')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}
</script>

<style scoped>
.conservation-water-level {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
}

.table-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.table-title p {
  margin: 0;
  font-size: 14px;
  color: #909399;
  line-height: 1.4;
}

.table-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.table-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.table-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.pagination-container :deep(.el-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-button-width: 32px;
  --el-pagination-button-height: 32px;
}

.pagination-container :deep(.el-pagination .btn-prev),
.pagination-container :deep(.el-pagination .btn-next),
.pagination-container :deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.2s ease;
}

.pagination-container :deep(.el-pagination .btn-prev:hover),
.pagination-container :deep(.el-pagination .btn-next:hover),
.pagination-container :deep(.el-pagination .el-pager li:hover) {
  transform: translateY(-1px);
}

.level-value {
  font-weight: 600;
}

.level-increase {
  color: #67c23a;
  font-weight: 600;
}

.level-decrease {
  color: #f56c6c;
  font-weight: 600;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-1px);
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table .el-table__header-wrapper) {
  background-color: #fafafa;
}

:deep(.el-table .el-table__header th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table .el-table__body-wrapper) {
  overflow-x: auto;
}

:deep(.el-table .cell) {
  padding: 0 8px;
  word-break: break-word;
}

/* 响应式表格 */
@media (max-width: 1200px) {
  :deep(.el-table .el-table__body-wrapper) {
    overflow-x: scroll;
  }
}

/* 卡片样式 */
:deep(.el-card__body) {
  padding: 20px;
}

/* 表格容器 */
.table-card {
  min-height: 600px;
}

.table-card :deep(.el-card__body) {
  padding: 20px;
  height: 100%;
}
</style>
