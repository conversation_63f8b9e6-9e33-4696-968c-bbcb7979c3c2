import {
  u
} from "./chunk-THUK4WUF.js";
import {
  e as e2
} from "./chunk-OYGWWPGZ.js";
import {
  M,
  c,
  e,
  f as f2,
  h,
  i,
  r as r4
} from "./chunk-77E52HT5.js";
import {
  C,
  a,
  g,
  j as j2,
  l,
  o,
  q,
  r as r3,
  s,
  v,
  y,
  z
} from "./chunk-SROTSYJS.js";
import {
  n,
  r as r5
} from "./chunk-FOE4ICAJ.js";
import {
  An,
  en,
  rn,
  tn
} from "./chunk-UYAKJRPP.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  p,
  w
} from "./chunk-63M4K32A.js";
import {
  $,
  E,
  I,
  R,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  r as r2
} from "./chunk-EGHLQERQ.js";
import {
  r,
  t,
  x
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/viewpointUtils.js
var Q = 96;
var U = 39.37;
var B = 180 / Math.PI;
function D(t2) {
  return t2.wkid ? t2 : t2.spatialReference || f.WGS84;
}
function T(t2, e3) {
  return e3.type ? r3(t2, e3.x, e3.y) : a(t2, e3);
}
function W(t2) {
  return $(t2);
}
function H(t2, e3) {
  const n2 = Math.max(1, e3[0]), r6 = Math.max(1, e3[1]);
  return Math.max(t2.width / n2, t2.height / r6) * ct(t2.spatialReference);
}
async function J(t2, a2, i2, c2) {
  let s2, u2;
  if (!t2) return null;
  if (Array.isArray(t2) && !t2.length) return null;
  if (j.isCollection(t2) && (t2 = t2.toArray()), Array.isArray(t2) && t2.length && "object" == typeof t2[0]) {
    const e3 = t2.every((t3) => "attributes" in t3), r6 = t2.some((t3) => !t3.geometry);
    let o2 = t2;
    if (e3 && r6 && a2 && a2.allLayerViews) {
      const e4 = /* @__PURE__ */ new Map();
      for (const n2 of t2) {
        const t3 = n2.layer, r8 = e4.get(t3) || [], o3 = n2.attributes[t3.objectIdField];
        null != o3 && r8.push(o3), e4.set(t3, r8);
      }
      const r7 = [];
      e4.forEach((t3, e5) => {
        const n2 = a2.allLayerViews.find((t4) => t4.layer.id === e5.id);
        if (n2 && "queryFeatures" in n2) {
          const o3 = e5.createQuery();
          o3.objectIds = t3, o3.returnGeometry = true, r7.push(n2.queryFeatures(o3));
        }
      });
      const i3 = await Promise.all(r7), c3 = [];
      for (const t3 of i3) if (t3 && t3.features && t3.features.length) for (const e5 of t3.features) r(e5.geometry) && c3.push(e5.geometry);
      o2 = c3;
    }
    for (const t3 of o2) c2 = await J(t3, a2, i2, c2);
    return c2;
  }
  if (Array.isArray(t2) && 2 === t2.length && "number" == typeof t2[0] && "number" == typeof t2[1]) s2 = new w(t2);
  else if (t2 instanceof p) s2 = t2;
  else if ("geometry" in t2) {
    if (t2.geometry) s2 = t2.geometry;
    else if (t2.layer) {
      const e3 = t2.layer, n2 = a2.allLayerViews.find((t3) => t3.layer.id === e3.id);
      if (n2 && "queryFeatures" in n2) {
        const o2 = e3.createQuery();
        o2.objectIds = [t2.attributes[e3.objectIdField]], o2.returnGeometry = true;
        const a3 = await n2.queryFeatures(o2);
        s2 = x(a3, "features", 0, "geometry");
      }
    }
  }
  if (t(s2)) return null;
  if (u2 = "point" === s2.type ? new w2({ xmin: s2.x, ymin: s2.y, xmax: s2.x, ymax: s2.y, spatialReference: s2.spatialReference }) : s2.extent, !u2) return null;
  en() || An(u2.spatialReference, i2) || await tn();
  const f3 = rn(u2, i2);
  return f3 ? c2 = c2 ? c2.union(f3) : f3 : null;
}
function K(t2) {
  if (t2 && (!Array.isArray(t2) || "number" != typeof t2[0]) && ("object" == typeof t2 || Array.isArray(t2) && "object" == typeof t2[0])) {
    if ("layer" in t2 && t2.layer && t2.layer.minScale && t2.layer.maxScale) {
      const e3 = t2.layer;
      return { min: e3.minScale, max: e3.maxScale };
    }
    if (Array.isArray(t2) && t2.length && t2.every((t3) => "layer" in t3)) {
      let e3 = 0, n2 = 0;
      for (const r6 of t2) {
        const t3 = r6.layer;
        t3 && t3.minScale && t3.maxScale && (e3 = t3.minScale < e3 ? t3.minScale : e3, n2 = t3.maxScale > n2 ? t3.maxScale : n2);
      }
      return e3 && n2 ? { min: e3, max: n2 } : null;
    }
  }
}
function X(t2, e3) {
  return E(D(t2), e3) ? t2 : rn(t2, e3);
}
async function Y(e3, r6) {
  if (!e3 || !r6) return new u({ targetGeometry: new w(), scale: 0, rotation: 0 });
  let o2 = r6.spatialReference;
  const { constraints: a2, padding: i2, viewpoint: c2, size: s2 } = r6, u2 = [i2 ? s2[0] - i2.left - i2.right : s2[0], i2 ? s2[1] - i2.top - i2.bottom : s2[1]];
  let f3 = null;
  e3 instanceof u ? f3 = e3 : e3.viewpoint ? f3 = e3.viewpoint : e3.target && "esri.Viewpoint" === e3.target.declaredClass && (f3 = e3.target);
  let l2 = null;
  f3 && f3.targetGeometry ? l2 = f3.targetGeometry : e3 instanceof w2 ? l2 = e3 : (e3 || e3 && ("center" in e3 || "extent" in e3 || "target" in e3)) && (l2 = await J(e3.center, r6, o2) || await J(e3.extent, r6, o2) || await J(e3.target, r6, o2) || await J(e3, r6, o2)), !l2 && c2 && c2.targetGeometry ? l2 = c2.targetGeometry : !l2 && r6.extent && (l2 = r6.extent), o2 || (o2 = D(r6.spatialReference || r6.extent || l2)), en() || E(l2.spatialReference, o2) || An(l2, o2) || await tn();
  const m = X(l2.center ? l2.center : l2, o2);
  let y2 = 0;
  if (f3 && r(f3.targetGeometry) && "point" === f3.targetGeometry.type) y2 = f3.scale;
  else if ("scale" in e3 && e3.scale) y2 = e3.scale;
  else if ("zoom" in e3 && -1 !== e3.zoom && a2 && a2.effectiveLODs) y2 = a2.zoomToScale(e3.zoom);
  else if (Array.isArray(l2) || "point" === l2.type || "extent" === l2.type && 0 === l2.width && 0 === l2.height) {
    const t2 = X(r6.extent, o2);
    y2 = r(t2) ? H(t2, u2) : r6.extent ? H(r6.extent, u2) : c2.scale;
  } else y2 = H(X(l2.extent, o2), u2);
  const p2 = K(e3);
  p2 && (p2.min && p2.min > y2 ? y2 = p2.min : p2.max && p2.max < y2 && (y2 = p2.max));
  let g2 = 0;
  f3 ? g2 = f3.rotation : e3.hasOwnProperty("rotation") ? g2 = e3.rotation : c2 && (g2 = c2.rotation);
  let x2 = new u({ targetGeometry: m, scale: y2, rotation: g2 });
  return a2 && (x2 = a2.fit(x2), a2.constrainByGeometry(x2), a2.rotationEnabled || (x2.rotation = g2)), x2;
}
function Z(t2, e3) {
  const n2 = t2.targetGeometry, r6 = e3.targetGeometry;
  return n2.x = r6.x, n2.y = r6.y, n2.spatialReference = r6.spatialReference, t2.scale = e3.scale, t2.rotation = e3.rotation, t2;
}
function $2(t2, e3, n2) {
  return n2 ? r3(t2, 0.5 * (e3[0] - n2.right + n2.left), 0.5 * (e3[1] - n2.bottom + n2.top)) : l(t2, e3, 0.5);
}
var _ = function() {
  const t2 = n();
  return function(e3, n2, r6) {
    const o2 = n2.targetGeometry;
    T(t2, o2);
    const a2 = 0.5 * ot(n2);
    return e3.xmin = t2[0] - a2 * r6[0], e3.ymin = t2[1] - a2 * r6[1], e3.xmax = t2[0] + a2 * r6[0], e3.ymax = t2[1] + a2 * r6[1], e3.spatialReference = o2.spatialReference, e3;
  };
}();
function tt(t2, e3, n2, r6, o2) {
  return xt(t2, e3, n2.center), t2.scale = H(n2, r6), o2 && o2.constraints && o2.constraints.constrain(t2), t2;
}
function et(t2, e3, n2, r6) {
  return ft(t2, e3, n2, r6), r4(t2, t2);
}
var nt = function() {
  const t2 = n();
  return function(e3, n2, r6) {
    return C(e3, st(e3, n2), $2(t2, n2, r6));
  };
}();
var rt = function() {
  const t2 = e2(), e3 = n();
  return function(n2, r6, o2, a2) {
    const i2 = ot(r6), f3 = it(r6);
    return r3(e3, i2, i2), M(t2, e3), e(t2, t2, f3), i(t2, t2, nt(e3, o2, a2)), i(t2, t2, [0, a2.top - a2.bottom]), r3(n2, t2[4], t2[5]);
  };
}();
function ot(t2) {
  return t2.scale * at(t2.targetGeometry);
}
function at(t2) {
  return r(t2) && I(t2.spatialReference) ? 1 / (W(t2.spatialReference) * U * Q) : 1;
}
function it(t2) {
  return r2(t2.rotation) || 0;
}
function ct(t2) {
  return I(t2) ? W(t2) * U * Q : 1;
}
function st(t2, e3) {
  return l(t2, e3, 0.5);
}
var ut = function() {
  const t2 = n(), e3 = n(), n2 = n();
  return function(r6, o2, a2, i2, c2, m) {
    return g(t2, o2), l(e3, a2, 0.5 * m), r3(n2, 1 / i2 * m, -1 / i2 * m), f2(r6, e3), c2 && e(r6, r6, c2), c(r6, r6, n2), i(r6, r6, t2), r6;
  };
}();
var ft = function() {
  const t2 = n();
  return function(e3, n2, r6, o2) {
    const a2 = ot(n2), i2 = it(n2);
    return T(t2, n2.targetGeometry), ut(e3, t2, r6, a2, i2, o2);
  };
}();
var lt = function() {
  const t2 = n();
  return function(e3, n2, r6, o2) {
    const a2 = ot(n2);
    return T(t2, n2.targetGeometry), ut(e3, t2, r6, a2, 0, o2);
  };
}();
function mt(t2) {
  const e3 = R(t2);
  return e3 ? e3.valid[1] - e3.valid[0] : 0;
}
function yt(t2, e3) {
  return Math.round(mt(t2) / e3);
}
var pt = function() {
  const t2 = n(), e3 = n(), n2 = [0, 0, 0];
  return function(r6, o2, a2) {
    o(t2, r6, o2), v(t2, t2), o(e3, r6, a2), v(e3, e3), y(n2, t2, e3);
    let i2 = Math.acos(j2(t2, e3) / (q(t2) * q(e3))) * B;
    return n2[2] < 0 && (i2 = -i2), isNaN(i2) && (i2 = 0), i2;
  };
}();
var gt = function() {
  const t2 = n();
  return function(e3, n2, r6, o2) {
    const a2 = e3.targetGeometry;
    return Z(e3, n2), rt(t2, n2, r6, o2), a2.x += t2[0], a2.y += t2[1], e3;
  };
}();
var xt = function(t2, e3, n2) {
  Z(t2, e3);
  const r6 = t2.targetGeometry;
  return r6.x = n2.x, r6.y = n2.y, r6.spatialReference = n2.spatialReference, t2;
};
var ht = function() {
  const t2 = n();
  return function(e3, n2, r6, o2, a2) {
    a2 || (a2 = "center"), C(t2, r6, o2), l(t2, t2, 0.5);
    const i2 = t2[0], c2 = t2[1];
    switch (a2) {
      case "center":
        r3(t2, 0, 0);
        break;
      case "left":
        r3(t2, -i2, 0);
        break;
      case "top":
        r3(t2, 0, c2);
        break;
      case "right":
        r3(t2, i2, 0);
        break;
      case "bottom":
        r3(t2, 0, -c2);
        break;
      case "top-left":
        r3(t2, -i2, c2);
        break;
      case "bottom-left":
        r3(t2, -i2, -c2);
        break;
      case "top-right":
        r3(t2, i2, c2);
        break;
      case "bottom-right":
        r3(t2, i2, -c2);
    }
    return St(e3, n2, t2), e3;
  };
}();
function bt(t2, e3, n2) {
  return Z(t2, e3), t2.rotation += n2, t2;
}
function wt(t2, e3, n2) {
  return Z(t2, e3), t2.rotation = n2, t2;
}
var dt = function() {
  const t2 = n();
  return function(e3, n2, r6, o2, a2) {
    return Z(e3, n2), isNaN(r6) || 0 === r6 || (At(t2, o2, n2, a2), e3.scale = n2.scale * r6, kt(t2, t2, e3, a2), St(e3, e3, r3(t2, t2[0] - o2[0], o2[1] - t2[1]))), e3;
  };
}();
function jt(t2, e3, n2) {
  return Z(t2, e3), t2.scale = n2, t2;
}
var Gt = function() {
  const t2 = n();
  return function(e3, n2, r6, o2, a2, i2) {
    return Z(e3, n2), isNaN(r6) || 0 === r6 || (At(t2, a2, n2, i2), e3.scale = n2.scale * r6, e3.rotation += o2, kt(t2, t2, e3, i2), St(e3, e3, r3(t2, t2[0] - a2[0], a2[1] - t2[1]))), e3;
  };
}();
var Rt = function() {
  const t2 = n(), e3 = n();
  return function(n2, r6, o2, a2, i2, c2, s2) {
    return nt(e3, c2, s2), s(t2, i2, e3), a2 ? Gt(n2, r6, o2, a2, t2, c2) : dt(n2, r6, o2, t2, c2);
  };
}();
var At = function() {
  const t2 = e2();
  return function(e3, n2, r6, o2) {
    return z(e3, n2, et(t2, r6, o2, 1));
  };
}();
var kt = function() {
  const t2 = e2();
  return function(e3, n2, r6, o2) {
    return z(e3, n2, ft(t2, r6, o2, 1));
  };
}();
var St = function() {
  const t2 = n(), e3 = e2();
  return function(n2, r6, o2) {
    Z(n2, r6);
    const a2 = ot(r6), i2 = n2.targetGeometry;
    return h(e3, it(r6)), c(e3, e3, r5(a2, a2)), z(t2, o2, e3), i2.x += t2[0], i2.y += t2[1], n2;
  };
}();

export {
  H,
  Y,
  Z,
  $2 as $,
  _,
  tt,
  nt,
  ot,
  ut,
  ft,
  lt,
  mt,
  yt,
  pt,
  gt,
  xt,
  ht,
  bt,
  wt,
  jt,
  Gt,
  Rt,
  St
};
//# sourceMappingURL=chunk-T7HWQQFI.js.map
