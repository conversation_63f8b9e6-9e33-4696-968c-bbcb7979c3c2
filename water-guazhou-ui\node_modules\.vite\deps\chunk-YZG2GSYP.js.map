{"version": 3, "sources": ["../../@arcgis/core/rest/query/executeForTopExtents.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../geometry.js\";import{parseUrl as t}from\"../utils.js\";import{executeQueryForTopExtents as o}from\"./operations/queryTopFeatures.js\";import r from\"../support/TopFeaturesQuery.js\";import e from\"../../geometry/Extent.js\";async function m(m,s,n){const p=t(m),a=await o(p,r.from(s),{...n});return{count:a.data.count,extent:e.fromJSON(a.data.extent)}}export{m as executeForTopExtents};\n"], "mappings": ";;;;;;;;;;;;;;AAIoO,eAAe,EAAEA,IAAE,GAAE,GAAE;AAAC,QAAMC,KAAE,EAAED,EAAC,GAAE,IAAE,MAAM,EAAEC,IAAE,EAAE,KAAK,CAAC,GAAE,EAAC,GAAG,EAAC,CAAC;AAAE,SAAM,EAAC,OAAM,EAAE,KAAK,OAAM,QAAO,EAAE,SAAS,EAAE,KAAK,MAAM,EAAC;AAAC;", "names": ["m", "p"]}