{"version": 3, "sources": ["../../@arcgis/core/views/2d/viewpointUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../Viewpoint.js\";import e from\"../../core/Collection.js\";import{isSome as n,get as r,isNone as o}from\"../../core/maybe.js\";import{getMetersPerUnitForSR as a}from\"../../core/unitUtils.js\";import{t as i}from\"../../chunks/common.js\";import{f as c,r as s,t as u,a as f,s as l,b as m,c as y}from\"../../chunks/mat2d.js\";import{c as p}from\"../../chunks/mat2df64.js\";import{b as g,s as x,e as h,n as b,a as w,f as d,g as j,h as G,i as R,j as A,t as k,c as S}from\"../../chunks/vec2.js\";import{f as v,a as M}from\"../../chunks/vec2f64.js\";import F from\"../../geometry/Extent.js\";import N from\"../../geometry/Geometry.js\";import z from\"../../geometry/Point.js\";import{isLoaded as I,canProjectWithoutEngine as V,load as q,project as L}from\"../../geometry/projection.js\";import P from\"../../geometry/SpatialReference.js\";import{equals as C,isValid as E,getInfo as O}from\"../../geometry/support/spatialReferenceUtils.js\";const Q=96,U=39.37,B=180/Math.PI;function D(t){return t.wkid?t:t.spatialReference||P.WGS84}function T(t,e){return e.type?x(t,e.x,e.y):S(t,e)}function W(t){return a(t)}function H(t,e){const n=Math.max(1,e[0]),r=Math.max(1,e[1]);return Math.max(t.width/n,t.height/r)*ct(t.spatialReference)}async function J(t,a,i,c){let s,u;if(!t)return null;if(Array.isArray(t)&&!t.length)return null;if(e.isCollection(t)&&(t=t.toArray()),Array.isArray(t)&&t.length&&\"object\"==typeof t[0]){const e=t.every((t=>\"attributes\"in t)),r=t.some((t=>!t.geometry));let o=t;if(e&&r&&a&&a.allLayerViews){const e=new Map;for(const n of t){const t=n.layer,r=e.get(t)||[],o=n.attributes[t.objectIdField];null!=o&&r.push(o),e.set(t,r)}const r=[];e.forEach(((t,e)=>{const n=a.allLayerViews.find((t=>t.layer.id===e.id));if(n&&\"queryFeatures\"in n){const o=e.createQuery();o.objectIds=t,o.returnGeometry=!0,r.push(n.queryFeatures(o))}}));const i=await Promise.all(r),c=[];for(const t of i)if(t&&t.features&&t.features.length)for(const e of t.features)n(e.geometry)&&c.push(e.geometry);o=c}for(const t of o)c=await J(t,a,i,c);return c}if(Array.isArray(t)&&2===t.length&&\"number\"==typeof t[0]&&\"number\"==typeof t[1])s=new z(t);else if(t instanceof N)s=t;else if(\"geometry\"in t)if(t.geometry)s=t.geometry;else if(t.layer){const e=t.layer,n=a.allLayerViews.find((t=>t.layer.id===e.id));if(n&&\"queryFeatures\"in n){const o=e.createQuery();o.objectIds=[t.attributes[e.objectIdField]],o.returnGeometry=!0;const a=await n.queryFeatures(o);s=r(a,\"features\",0,\"geometry\")}}if(o(s))return null;if(u=\"point\"===s.type?new F({xmin:s.x,ymin:s.y,xmax:s.x,ymax:s.y,spatialReference:s.spatialReference}):s.extent,!u)return null;I()||V(u.spatialReference,i)||await q();const f=L(u,i);return f?c=c?c.union(f):f:null}function K(t){if(t&&(!Array.isArray(t)||\"number\"!=typeof t[0])&&(\"object\"==typeof t||Array.isArray(t)&&\"object\"==typeof t[0])){if(\"layer\"in t&&t.layer&&t.layer.minScale&&t.layer.maxScale){const e=t.layer;return{min:e.minScale,max:e.maxScale}}if(Array.isArray(t)&&t.length&&t.every((t=>\"layer\"in t))){let e=0,n=0;for(const r of t){const t=r.layer;t&&t.minScale&&t.maxScale&&(e=t.minScale<e?t.minScale:e,n=t.maxScale>n?t.maxScale:n)}return e&&n?{min:e,max:n}:null}}}function X(t,e){return C(D(t),e)?t:L(t,e)}async function Y(e,r){if(!e||!r)return new t({targetGeometry:new z,scale:0,rotation:0});let o=r.spatialReference;const{constraints:a,padding:i,viewpoint:c,size:s}=r,u=[i?s[0]-i.left-i.right:s[0],i?s[1]-i.top-i.bottom:s[1]];let f=null;e instanceof t?f=e:e.viewpoint?f=e.viewpoint:e.target&&\"esri.Viewpoint\"===e.target.declaredClass&&(f=e.target);let l=null;f&&f.targetGeometry?l=f.targetGeometry:e instanceof F?l=e:(e||e&&(\"center\"in e||\"extent\"in e||\"target\"in e))&&(l=await J(e.center,r,o)||await J(e.extent,r,o)||await J(e.target,r,o)||await J(e,r,o)),!l&&c&&c.targetGeometry?l=c.targetGeometry:!l&&r.extent&&(l=r.extent),o||(o=D(r.spatialReference||r.extent||l)),I()||C(l.spatialReference,o)||V(l,o)||await q();const m=X(l.center?l.center:l,o);let y=0;if(f&&n(f.targetGeometry)&&\"point\"===f.targetGeometry.type)y=f.scale;else if(\"scale\"in e&&e.scale)y=e.scale;else if(\"zoom\"in e&&-1!==e.zoom&&a&&a.effectiveLODs)y=a.zoomToScale(e.zoom);else if(Array.isArray(l)||\"point\"===l.type||\"extent\"===l.type&&0===l.width&&0===l.height){const t=X(r.extent,o);y=n(t)?H(t,u):r.extent?H(r.extent,u):c.scale}else y=H(X(l.extent,o),u);const p=K(e);p&&(p.min&&p.min>y?y=p.min:p.max&&p.max<y&&(y=p.max));let g=0;f?g=f.rotation:e.hasOwnProperty(\"rotation\")?g=e.rotation:c&&(g=c.rotation);let x=new t({targetGeometry:m,scale:y,rotation:g});return a&&(x=a.fit(x),a.constrainByGeometry(x),a.rotationEnabled||(x.rotation=g)),x}function Z(t,e){const n=t.targetGeometry,r=e.targetGeometry;return n.x=r.x,n.y=r.y,n.spatialReference=r.spatialReference,t.scale=e.scale,t.rotation=e.rotation,t}function $(t,e,n){return n?x(t,.5*(e[0]-n.right+n.left),.5*(e[1]-n.bottom+n.top)):g(t,e,.5)}const _=function(){const t=M();return function(e,n,r){const o=n.targetGeometry;T(t,o);const a=.5*ot(n);return e.xmin=t[0]-a*r[0],e.ymin=t[1]-a*r[1],e.xmax=t[0]+a*r[0],e.ymax=t[1]+a*r[1],e.spatialReference=o.spatialReference,e}}();function tt(t,e,n,r,o){return xt(t,e,n.center),t.scale=H(n,r),o&&o.constraints&&o.constraints.constrain(t),t}function et(t,e,n,r){return ft(t,e,n,r),y(t,t)}const nt=function(){const t=M();return function(e,n,r){return h(e,st(e,n),$(t,n,r))}}(),rt=function(){const t=p(),e=M();return function(n,r,o,a){const i=ot(r),f=it(r);return x(e,i,i),c(t,e),s(t,t,f),u(t,t,nt(e,o,a)),u(t,t,[0,a.top-a.bottom]),x(n,t[4],t[5])}}();function ot(t){return t.scale*at(t.targetGeometry)}function at(t){return n(t)&&E(t.spatialReference)?1/(W(t.spatialReference)*U*Q):1}function it(t){return i(t.rotation)||0}function ct(t){return E(t)?W(t)*U*Q:1}function st(t,e){return g(t,e,.5)}const ut=function(){const t=M(),e=M(),n=M();return function(r,o,a,i,c,m){return b(t,o),g(e,a,.5*m),x(n,1/i*m,-1/i*m),f(r,e),c&&s(r,r,c),l(r,r,n),u(r,r,t),r}}(),ft=function(){const t=M();return function(e,n,r,o){const a=ot(n),i=it(n);return T(t,n.targetGeometry),ut(e,t,r,a,i,o)}}(),lt=function(){const t=M();return function(e,n,r,o){const a=ot(n);return T(t,n.targetGeometry),ut(e,t,r,a,0,o)}}();function mt(t){const e=O(t);return e?e.valid[1]-e.valid[0]:0}function yt(t,e){return Math.round(mt(t)/e)}const pt=function(){const t=M(),e=M(),n=[0,0,0];return function(r,o,a){w(t,r,o),d(t,t),w(e,r,a),d(e,e),j(n,t,e);let i=Math.acos(G(t,e)/(R(t)*R(e)))*B;return n[2]<0&&(i=-i),isNaN(i)&&(i=0),i}}(),gt=function(){const t=M();return function(e,n,r,o){const a=e.targetGeometry;return Z(e,n),rt(t,n,r,o),a.x+=t[0],a.y+=t[1],e}}(),xt=function(t,e,n){Z(t,e);const r=t.targetGeometry;return r.x=n.x,r.y=n.y,r.spatialReference=n.spatialReference,t},ht=function(){const t=M();return function(e,n,r,o,a){a||(a=\"center\"),h(t,r,o),g(t,t,.5);const i=t[0],c=t[1];switch(a){case\"center\":x(t,0,0);break;case\"left\":x(t,-i,0);break;case\"top\":x(t,0,c);break;case\"right\":x(t,i,0);break;case\"bottom\":x(t,0,-c);break;case\"top-left\":x(t,-i,c);break;case\"bottom-left\":x(t,-i,-c);break;case\"top-right\":x(t,i,c);break;case\"bottom-right\":x(t,i,-c)}return St(e,n,t),e}}();function bt(t,e,n){return Z(t,e),t.rotation+=n,t}function wt(t,e,n){return Z(t,e),t.rotation=n,t}const dt=function(){const t=M();return function(e,n,r,o,a){return Z(e,n),isNaN(r)||0===r||(At(t,o,n,a),e.scale=n.scale*r,kt(t,t,e,a),St(e,e,x(t,t[0]-o[0],o[1]-t[1]))),e}}();function jt(t,e,n){return Z(t,e),t.scale=n,t}const Gt=function(){const t=M();return function(e,n,r,o,a,i){return Z(e,n),isNaN(r)||0===r||(At(t,a,n,i),e.scale=n.scale*r,e.rotation+=o,kt(t,t,e,i),St(e,e,x(t,t[0]-a[0],a[1]-t[1]))),e}}(),Rt=function(){const t=M(),e=M();return function(n,r,o,a,i,c,s){return nt(e,c,s),A(t,i,e),a?Gt(n,r,o,a,t,c):dt(n,r,o,t,c)}}(),At=function(){const t=p();return function(e,n,r,o){return k(e,n,et(t,r,o,1))}}(),kt=function(){const t=p();return function(e,n,r,o){return k(e,n,ft(t,r,o,1))}}(),St=function(){const t=M(),e=p();return function(n,r,o){Z(n,r);const a=ot(r),i=n.targetGeometry;return m(e,it(r)),l(e,e,v(a,a)),k(t,o,e),i.x+=t[0],i.y+=t[1],n}}();export{gt as addPadding,pt as angleBetween,xt as centerAt,Z as copy,Y as create,H as extentToScale,$ as getAnchor,_ as getExtent,ut as getMatrix,rt as getPaddingMapTranslation,nt as getPaddingScreenTranslation,ot as getResolution,ct as getResolutionToScaleFactor,ft as getTransform,lt as getTransformNoRotation,yt as getWorldScreenWidth,mt as getWorldWidth,Rt as padAndScaleAndRotateBy,ht as resize,bt as rotateBy,wt as rotateTo,Gt as scaleAndRotateBy,jt as scaleTo,tt as setExtent,At as toMap,kt as toScreen,St as translateBy};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI25B,IAAM,IAAE;AAAR,IAAW,IAAE;AAAb,IAAmB,IAAE,MAAI,KAAK;AAAG,SAAS,EAAEA,IAAE;AAAC,SAAOA,GAAE,OAAKA,KAAEA,GAAE,oBAAkB,EAAE;AAAK;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAOA,GAAE,OAAKC,GAAEF,IAAEC,GAAE,GAAEA,GAAE,CAAC,IAAE,EAAED,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,EAAEA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,QAAME,KAAE,KAAK,IAAI,GAAEF,GAAE,CAAC,CAAC,GAAEC,KAAE,KAAK,IAAI,GAAED,GAAE,CAAC,CAAC;AAAE,SAAO,KAAK,IAAID,GAAE,QAAMG,IAAEH,GAAE,SAAOE,EAAC,IAAE,GAAGF,GAAE,gBAAgB;AAAC;AAAC,eAAe,EAAEA,IAAEI,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,MAAG,CAACR,GAAE,QAAO;AAAK,MAAG,MAAM,QAAQA,EAAC,KAAG,CAACA,GAAE,OAAO,QAAO;AAAK,MAAG,EAAE,aAAaA,EAAC,MAAIA,KAAEA,GAAE,QAAQ,IAAG,MAAM,QAAQA,EAAC,KAAGA,GAAE,UAAQ,YAAU,OAAOA,GAAE,CAAC,GAAE;AAAC,UAAMC,KAAED,GAAE,MAAO,CAAAA,OAAG,gBAAeA,EAAE,GAAEE,KAAEF,GAAE,KAAM,CAAAA,OAAG,CAACA,GAAE,QAAS;AAAE,QAAIS,KAAET;AAAE,QAAGC,MAAGC,MAAGE,MAAGA,GAAE,eAAc;AAAC,YAAMH,KAAE,oBAAI;AAAI,iBAAUE,MAAKH,IAAE;AAAC,cAAMA,KAAEG,GAAE,OAAMD,KAAED,GAAE,IAAID,EAAC,KAAG,CAAC,GAAES,KAAEN,GAAE,WAAWH,GAAE,aAAa;AAAE,gBAAMS,MAAGP,GAAE,KAAKO,EAAC,GAAER,GAAE,IAAID,IAAEE,EAAC;AAAA,MAAC;AAAC,YAAMA,KAAE,CAAC;AAAE,MAAAD,GAAE,QAAS,CAACD,IAAEC,OAAI;AAAC,cAAME,KAAEC,GAAE,cAAc,KAAM,CAAAJ,OAAGA,GAAE,MAAM,OAAKC,GAAE,EAAG;AAAE,YAAGE,MAAG,mBAAkBA,IAAE;AAAC,gBAAMM,KAAER,GAAE,YAAY;AAAE,UAAAQ,GAAE,YAAUT,IAAES,GAAE,iBAAe,MAAGP,GAAE,KAAKC,GAAE,cAAcM,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAE,YAAMJ,KAAE,MAAM,QAAQ,IAAIH,EAAC,GAAEI,KAAE,CAAC;AAAE,iBAAUN,MAAKK,GAAE,KAAGL,MAAGA,GAAE,YAAUA,GAAE,SAAS,OAAO,YAAUC,MAAKD,GAAE,SAAS,GAAEC,GAAE,QAAQ,KAAGK,GAAE,KAAKL,GAAE,QAAQ;AAAE,MAAAQ,KAAEH;AAAA,IAAC;AAAC,eAAUN,MAAKS,GAAE,CAAAH,KAAE,MAAM,EAAEN,IAAEI,IAAEC,IAAEC,EAAC;AAAE,WAAOA;AAAA,EAAC;AAAC,MAAG,MAAM,QAAQN,EAAC,KAAG,MAAIA,GAAE,UAAQ,YAAU,OAAOA,GAAE,CAAC,KAAG,YAAU,OAAOA,GAAE,CAAC,EAAE,CAAAO,KAAE,IAAI,EAAEP,EAAC;AAAA,WAAUA,cAAa,EAAE,CAAAO,KAAEP;AAAA,WAAU,cAAaA;AAAE,QAAGA,GAAE,SAAS,CAAAO,KAAEP,GAAE;AAAA,aAAiBA,GAAE,OAAM;AAAC,YAAMC,KAAED,GAAE,OAAMG,KAAEC,GAAE,cAAc,KAAM,CAAAJ,OAAGA,GAAE,MAAM,OAAKC,GAAE,EAAG;AAAE,UAAGE,MAAG,mBAAkBA,IAAE;AAAC,cAAMM,KAAER,GAAE,YAAY;AAAE,QAAAQ,GAAE,YAAU,CAACT,GAAE,WAAWC,GAAE,aAAa,CAAC,GAAEQ,GAAE,iBAAe;AAAG,cAAML,KAAE,MAAMD,GAAE,cAAcM,EAAC;AAAE,QAAAF,KAAE,EAAEH,IAAE,YAAW,GAAE,UAAU;AAAA,MAAC;AAAA,IAAC;AAAA;AAAC,MAAG,EAAEG,EAAC,EAAE,QAAO;AAAK,MAAGC,KAAE,YAAUD,GAAE,OAAK,IAAIG,GAAE,EAAC,MAAKH,GAAE,GAAE,MAAKA,GAAE,GAAE,MAAKA,GAAE,GAAE,MAAKA,GAAE,GAAE,kBAAiBA,GAAE,iBAAgB,CAAC,IAAEA,GAAE,QAAO,CAACC,GAAE,QAAO;AAAK,KAAE,KAAG,GAAEA,GAAE,kBAAiBH,EAAC,KAAG,MAAM,GAAE;AAAE,QAAMM,KAAE,GAAEH,IAAEH,EAAC;AAAE,SAAOM,KAAEL,KAAEA,KAAEA,GAAE,MAAMK,EAAC,IAAEA,KAAE;AAAI;AAAC,SAAS,EAAEX,IAAE;AAAC,MAAGA,OAAI,CAAC,MAAM,QAAQA,EAAC,KAAG,YAAU,OAAOA,GAAE,CAAC,OAAK,YAAU,OAAOA,MAAG,MAAM,QAAQA,EAAC,KAAG,YAAU,OAAOA,GAAE,CAAC,IAAG;AAAC,QAAG,WAAUA,MAAGA,GAAE,SAAOA,GAAE,MAAM,YAAUA,GAAE,MAAM,UAAS;AAAC,YAAMC,KAAED,GAAE;AAAM,aAAM,EAAC,KAAIC,GAAE,UAAS,KAAIA,GAAE,SAAQ;AAAA,IAAC;AAAC,QAAG,MAAM,QAAQD,EAAC,KAAGA,GAAE,UAAQA,GAAE,MAAO,CAAAA,OAAG,WAAUA,EAAE,GAAE;AAAC,UAAIC,KAAE,GAAEE,KAAE;AAAE,iBAAUD,MAAKF,IAAE;AAAC,cAAMA,KAAEE,GAAE;AAAM,QAAAF,MAAGA,GAAE,YAAUA,GAAE,aAAWC,KAAED,GAAE,WAASC,KAAED,GAAE,WAASC,IAAEE,KAAEH,GAAE,WAASG,KAAEH,GAAE,WAASG;AAAA,MAAE;AAAC,aAAOF,MAAGE,KAAE,EAAC,KAAIF,IAAE,KAAIE,GAAC,IAAE;AAAA,IAAI;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAE;AAAC,SAAO,EAAE,EAAED,EAAC,GAAEC,EAAC,IAAED,KAAE,GAAEA,IAAEC,EAAC;AAAC;AAAC,eAAe,EAAEA,IAAEC,IAAE;AAAC,MAAG,CAACD,MAAG,CAACC,GAAE,QAAO,IAAI,EAAE,EAAC,gBAAe,IAAI,KAAE,OAAM,GAAE,UAAS,EAAC,CAAC;AAAE,MAAIO,KAAEP,GAAE;AAAiB,QAAK,EAAC,aAAYE,IAAE,SAAQC,IAAE,WAAUC,IAAE,MAAKC,GAAC,IAAEL,IAAEM,KAAE,CAACH,KAAEE,GAAE,CAAC,IAAEF,GAAE,OAAKA,GAAE,QAAME,GAAE,CAAC,GAAEF,KAAEE,GAAE,CAAC,IAAEF,GAAE,MAAIA,GAAE,SAAOE,GAAE,CAAC,CAAC;AAAE,MAAII,KAAE;AAAK,EAAAV,cAAa,IAAEU,KAAEV,KAAEA,GAAE,YAAUU,KAAEV,GAAE,YAAUA,GAAE,UAAQ,qBAAmBA,GAAE,OAAO,kBAAgBU,KAAEV,GAAE;AAAQ,MAAIW,KAAE;AAAK,EAAAD,MAAGA,GAAE,iBAAeC,KAAED,GAAE,iBAAeV,cAAaS,KAAEE,KAAEX,MAAGA,MAAGA,OAAI,YAAWA,MAAG,YAAWA,MAAG,YAAWA,SAAMW,KAAE,MAAM,EAAEX,GAAE,QAAOC,IAAEO,EAAC,KAAG,MAAM,EAAER,GAAE,QAAOC,IAAEO,EAAC,KAAG,MAAM,EAAER,GAAE,QAAOC,IAAEO,EAAC,KAAG,MAAM,EAAER,IAAEC,IAAEO,EAAC,IAAG,CAACG,MAAGN,MAAGA,GAAE,iBAAeM,KAAEN,GAAE,iBAAe,CAACM,MAAGV,GAAE,WAASU,KAAEV,GAAE,SAAQO,OAAIA,KAAE,EAAEP,GAAE,oBAAkBA,GAAE,UAAQU,EAAC,IAAG,GAAE,KAAG,EAAEA,GAAE,kBAAiBH,EAAC,KAAG,GAAEG,IAAEH,EAAC,KAAG,MAAM,GAAE;AAAE,QAAM,IAAE,EAAEG,GAAE,SAAOA,GAAE,SAAOA,IAAEH,EAAC;AAAE,MAAII,KAAE;AAAE,MAAGF,MAAG,EAAEA,GAAE,cAAc,KAAG,YAAUA,GAAE,eAAe,KAAK,CAAAE,KAAEF,GAAE;AAAA,WAAc,WAAUV,MAAGA,GAAE,MAAM,CAAAY,KAAEZ,GAAE;AAAA,WAAc,UAASA,MAAG,OAAKA,GAAE,QAAMG,MAAGA,GAAE,cAAc,CAAAS,KAAET,GAAE,YAAYH,GAAE,IAAI;AAAA,WAAU,MAAM,QAAQW,EAAC,KAAG,YAAUA,GAAE,QAAM,aAAWA,GAAE,QAAM,MAAIA,GAAE,SAAO,MAAIA,GAAE,QAAO;AAAC,UAAMZ,KAAE,EAAEE,GAAE,QAAOO,EAAC;AAAE,IAAAI,KAAE,EAAEb,EAAC,IAAE,EAAEA,IAAEQ,EAAC,IAAEN,GAAE,SAAO,EAAEA,GAAE,QAAOM,EAAC,IAAEF,GAAE;AAAA,EAAK,MAAM,CAAAO,KAAE,EAAE,EAAED,GAAE,QAAOH,EAAC,GAAED,EAAC;AAAE,QAAMM,KAAE,EAAEb,EAAC;AAAE,EAAAa,OAAIA,GAAE,OAAKA,GAAE,MAAID,KAAEA,KAAEC,GAAE,MAAIA,GAAE,OAAKA,GAAE,MAAID,OAAIA,KAAEC,GAAE;AAAM,MAAIC,KAAE;AAAE,EAAAJ,KAAEI,KAAEJ,GAAE,WAASV,GAAE,eAAe,UAAU,IAAEc,KAAEd,GAAE,WAASK,OAAIS,KAAET,GAAE;AAAU,MAAIU,KAAE,IAAI,EAAE,EAAC,gBAAe,GAAE,OAAMH,IAAE,UAASE,GAAC,CAAC;AAAE,SAAOX,OAAIY,KAAEZ,GAAE,IAAIY,EAAC,GAAEZ,GAAE,oBAAoBY,EAAC,GAAEZ,GAAE,oBAAkBY,GAAE,WAASD,MAAIC;AAAC;AAAC,SAAS,EAAEhB,IAAEC,IAAE;AAAC,QAAME,KAAEH,GAAE,gBAAeE,KAAED,GAAE;AAAe,SAAOE,GAAE,IAAED,GAAE,GAAEC,GAAE,IAAED,GAAE,GAAEC,GAAE,mBAAiBD,GAAE,kBAAiBF,GAAE,QAAMC,GAAE,OAAMD,GAAE,WAASC,GAAE,UAASD;AAAC;AAAC,SAASiB,GAAEjB,IAAEC,IAAEE,IAAE;AAAC,SAAOA,KAAED,GAAEF,IAAE,OAAIC,GAAE,CAAC,IAAEE,GAAE,QAAMA,GAAE,OAAM,OAAIF,GAAE,CAAC,IAAEE,GAAE,SAAOA,GAAE,IAAI,IAAE,EAAEH,IAAEC,IAAE,GAAE;AAAC;AAAC,IAAM,IAAE,WAAU;AAAC,QAAMD,KAAE,EAAE;AAAE,SAAO,SAASC,IAAEE,IAAED,IAAE;AAAC,UAAMO,KAAEN,GAAE;AAAe,MAAEH,IAAES,EAAC;AAAE,UAAML,KAAE,MAAG,GAAGD,EAAC;AAAE,WAAOF,GAAE,OAAKD,GAAE,CAAC,IAAEI,KAAEF,GAAE,CAAC,GAAED,GAAE,OAAKD,GAAE,CAAC,IAAEI,KAAEF,GAAE,CAAC,GAAED,GAAE,OAAKD,GAAE,CAAC,IAAEI,KAAEF,GAAE,CAAC,GAAED,GAAE,OAAKD,GAAE,CAAC,IAAEI,KAAEF,GAAE,CAAC,GAAED,GAAE,mBAAiBQ,GAAE,kBAAiBR;AAAA,EAAC;AAAC,EAAE;AAAE,SAAS,GAAGD,IAAEC,IAAEE,IAAED,IAAEO,IAAE;AAAC,SAAO,GAAGT,IAAEC,IAAEE,GAAE,MAAM,GAAEH,GAAE,QAAM,EAAEG,IAAED,EAAC,GAAEO,MAAGA,GAAE,eAAaA,GAAE,YAAY,UAAUT,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEE,IAAED,IAAE;AAAC,SAAO,GAAGF,IAAEC,IAAEE,IAAED,EAAC,GAAEA,GAAEF,IAAEA,EAAC;AAAC;AAAC,IAAM,KAAG,WAAU;AAAC,QAAMA,KAAE,EAAE;AAAE,SAAO,SAASC,IAAEE,IAAED,IAAE;AAAC,WAAO,EAAED,IAAE,GAAGA,IAAEE,EAAC,GAAEc,GAAEjB,IAAEG,IAAED,EAAC,CAAC;AAAA,EAAC;AAAC,EAAE;AAAtF,IAAwF,KAAG,WAAU;AAAC,QAAMF,KAAEC,GAAE,GAAEA,KAAE,EAAE;AAAE,SAAO,SAASE,IAAED,IAAEO,IAAEL,IAAE;AAAC,UAAMC,KAAE,GAAGH,EAAC,GAAES,KAAE,GAAGT,EAAC;AAAE,WAAOA,GAAED,IAAEI,IAAEA,EAAC,GAAE,EAAEL,IAAEC,EAAC,GAAE,EAAED,IAAEA,IAAEW,EAAC,GAAE,EAAEX,IAAEA,IAAE,GAAGC,IAAEQ,IAAEL,EAAC,CAAC,GAAE,EAAEJ,IAAEA,IAAE,CAAC,GAAEI,GAAE,MAAIA,GAAE,MAAM,CAAC,GAAEF,GAAEC,IAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,EAAC;AAAC,EAAE;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,QAAM,GAAGA,GAAE,cAAc;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG,EAAEA,GAAE,gBAAgB,IAAE,KAAG,EAAEA,GAAE,gBAAgB,IAAE,IAAE,KAAG;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOE,GAAEF,GAAE,QAAQ,KAAG;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,EAAEA,EAAC,IAAE,EAAEA,EAAC,IAAE,IAAE,IAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAO,EAAED,IAAEC,IAAE,GAAE;AAAC;AAAC,IAAM,KAAG,WAAU;AAAC,QAAMD,KAAE,EAAE,GAAEC,KAAE,EAAE,GAAEE,KAAE,EAAE;AAAE,SAAO,SAASD,IAAEO,IAAEL,IAAEC,IAAEC,IAAE,GAAE;AAAC,WAAO,EAAEN,IAAES,EAAC,GAAE,EAAER,IAAEG,IAAE,MAAG,CAAC,GAAEF,GAAEC,IAAE,IAAEE,KAAE,GAAE,KAAGA,KAAE,CAAC,GAAEM,GAAET,IAAED,EAAC,GAAEK,MAAG,EAAEJ,IAAEA,IAAEI,EAAC,GAAE,EAAEJ,IAAEA,IAAEC,EAAC,GAAE,EAAED,IAAEA,IAAEF,EAAC,GAAEE;AAAA,EAAC;AAAC,EAAE;AAA9J,IAAgK,KAAG,WAAU;AAAC,QAAMF,KAAE,EAAE;AAAE,SAAO,SAASC,IAAEE,IAAED,IAAEO,IAAE;AAAC,UAAML,KAAE,GAAGD,EAAC,GAAEE,KAAE,GAAGF,EAAC;AAAE,WAAO,EAAEH,IAAEG,GAAE,cAAc,GAAE,GAAGF,IAAED,IAAEE,IAAEE,IAAEC,IAAEI,EAAC;AAAA,EAAC;AAAC,EAAE;AAAxR,IAA0R,KAAG,WAAU;AAAC,QAAMT,KAAE,EAAE;AAAE,SAAO,SAASC,IAAEE,IAAED,IAAEO,IAAE;AAAC,UAAML,KAAE,GAAGD,EAAC;AAAE,WAAO,EAAEH,IAAEG,GAAE,cAAc,GAAE,GAAGF,IAAED,IAAEE,IAAEE,IAAE,GAAEK,EAAC;AAAA,EAAC;AAAC,EAAE;AAAE,SAAS,GAAGT,IAAE;AAAC,QAAMC,KAAE,EAAED,EAAC;AAAE,SAAOC,KAAEA,GAAE,MAAM,CAAC,IAAEA,GAAE,MAAM,CAAC,IAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,KAAK,MAAM,GAAGD,EAAC,IAAEC,EAAC;AAAC;AAAC,IAAM,KAAG,WAAU;AAAC,QAAMD,KAAE,EAAE,GAAEC,KAAE,EAAE,GAAEE,KAAE,CAAC,GAAE,GAAE,CAAC;AAAE,SAAO,SAASD,IAAEO,IAAEL,IAAE;AAAC,MAAEJ,IAAEE,IAAEO,EAAC,GAAE,EAAET,IAAEA,EAAC,GAAE,EAAEC,IAAEC,IAAEE,EAAC,GAAE,EAAEH,IAAEA,EAAC,GAAE,EAAEE,IAAEH,IAAEC,EAAC;AAAE,QAAII,KAAE,KAAK,KAAKa,GAAElB,IAAEC,EAAC,KAAG,EAAED,EAAC,IAAE,EAAEC,EAAC,EAAE,IAAE;AAAE,WAAOE,GAAE,CAAC,IAAE,MAAIE,KAAE,CAACA,KAAG,MAAMA,EAAC,MAAIA,KAAE,IAAGA;AAAA,EAAC;AAAC,EAAE;AAAhM,IAAkM,KAAG,WAAU;AAAC,QAAML,KAAE,EAAE;AAAE,SAAO,SAASC,IAAEE,IAAED,IAAEO,IAAE;AAAC,UAAML,KAAEH,GAAE;AAAe,WAAO,EAAEA,IAAEE,EAAC,GAAE,GAAGH,IAAEG,IAAED,IAAEO,EAAC,GAAEL,GAAE,KAAGJ,GAAE,CAAC,GAAEI,GAAE,KAAGJ,GAAE,CAAC,GAAEC;AAAA,EAAC;AAAC,EAAE;AAAhU,IAAkU,KAAG,SAASD,IAAEC,IAAEE,IAAE;AAAC,IAAEH,IAAEC,EAAC;AAAE,QAAMC,KAAEF,GAAE;AAAe,SAAOE,GAAE,IAAEC,GAAE,GAAED,GAAE,IAAEC,GAAE,GAAED,GAAE,mBAAiBC,GAAE,kBAAiBH;AAAC;AAAnb,IAAqb,KAAG,WAAU;AAAC,QAAMA,KAAE,EAAE;AAAE,SAAO,SAASC,IAAEE,IAAED,IAAEO,IAAEL,IAAE;AAAC,IAAAA,OAAIA,KAAE,WAAU,EAAEJ,IAAEE,IAAEO,EAAC,GAAE,EAAET,IAAEA,IAAE,GAAE;AAAE,UAAMK,KAAEL,GAAE,CAAC,GAAEM,KAAEN,GAAE,CAAC;AAAE,YAAOI,IAAE;AAAA,MAAC,KAAI;AAAS,QAAAF,GAAEF,IAAE,GAAE,CAAC;AAAE;AAAA,MAAM,KAAI;AAAO,QAAAE,GAAEF,IAAE,CAACK,IAAE,CAAC;AAAE;AAAA,MAAM,KAAI;AAAM,QAAAH,GAAEF,IAAE,GAAEM,EAAC;AAAE;AAAA,MAAM,KAAI;AAAQ,QAAAJ,GAAEF,IAAEK,IAAE,CAAC;AAAE;AAAA,MAAM,KAAI;AAAS,QAAAH,GAAEF,IAAE,GAAE,CAACM,EAAC;AAAE;AAAA,MAAM,KAAI;AAAW,QAAAJ,GAAEF,IAAE,CAACK,IAAEC,EAAC;AAAE;AAAA,MAAM,KAAI;AAAc,QAAAJ,GAAEF,IAAE,CAACK,IAAE,CAACC,EAAC;AAAE;AAAA,MAAM,KAAI;AAAY,QAAAJ,GAAEF,IAAEK,IAAEC,EAAC;AAAE;AAAA,MAAM,KAAI;AAAe,QAAAJ,GAAEF,IAAEK,IAAE,CAACC,EAAC;AAAA,IAAC;AAAC,WAAO,GAAGL,IAAEE,IAAEH,EAAC,GAAEC;AAAA,EAAC;AAAC,EAAE;AAAE,SAAS,GAAGD,IAAEC,IAAEE,IAAE;AAAC,SAAO,EAAEH,IAAEC,EAAC,GAAED,GAAE,YAAUG,IAAEH;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEE,IAAE;AAAC,SAAO,EAAEH,IAAEC,EAAC,GAAED,GAAE,WAASG,IAAEH;AAAC;AAAC,IAAM,KAAG,WAAU;AAAC,QAAMA,KAAE,EAAE;AAAE,SAAO,SAASC,IAAEE,IAAED,IAAEO,IAAEL,IAAE;AAAC,WAAO,EAAEH,IAAEE,EAAC,GAAE,MAAMD,EAAC,KAAG,MAAIA,OAAI,GAAGF,IAAES,IAAEN,IAAEC,EAAC,GAAEH,GAAE,QAAME,GAAE,QAAMD,IAAE,GAAGF,IAAEA,IAAEC,IAAEG,EAAC,GAAE,GAAGH,IAAEA,IAAEC,GAAEF,IAAEA,GAAE,CAAC,IAAES,GAAE,CAAC,GAAEA,GAAE,CAAC,IAAET,GAAE,CAAC,CAAC,CAAC,IAAGC;AAAA,EAAC;AAAC,EAAE;AAAE,SAAS,GAAGD,IAAEC,IAAEE,IAAE;AAAC,SAAO,EAAEH,IAAEC,EAAC,GAAED,GAAE,QAAMG,IAAEH;AAAC;AAAC,IAAM,KAAG,WAAU;AAAC,QAAMA,KAAE,EAAE;AAAE,SAAO,SAASC,IAAEE,IAAED,IAAEO,IAAEL,IAAEC,IAAE;AAAC,WAAO,EAAEJ,IAAEE,EAAC,GAAE,MAAMD,EAAC,KAAG,MAAIA,OAAI,GAAGF,IAAEI,IAAED,IAAEE,EAAC,GAAEJ,GAAE,QAAME,GAAE,QAAMD,IAAED,GAAE,YAAUQ,IAAE,GAAGT,IAAEA,IAAEC,IAAEI,EAAC,GAAE,GAAGJ,IAAEA,IAAEC,GAAEF,IAAEA,GAAE,CAAC,IAAEI,GAAE,CAAC,GAAEA,GAAE,CAAC,IAAEJ,GAAE,CAAC,CAAC,CAAC,IAAGC;AAAA,EAAC;AAAC,EAAE;AAA3L,IAA6L,KAAG,WAAU;AAAC,QAAMD,KAAE,EAAE,GAAEC,KAAE,EAAE;AAAE,SAAO,SAASE,IAAED,IAAEO,IAAEL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAGN,IAAEK,IAAEC,EAAC,GAAE,EAAEP,IAAEK,IAAEJ,EAAC,GAAEG,KAAE,GAAGD,IAAED,IAAEO,IAAEL,IAAEJ,IAAEM,EAAC,IAAE,GAAGH,IAAED,IAAEO,IAAET,IAAEM,EAAC;AAAA,EAAC;AAAC,EAAE;AAAxT,IAA0T,KAAG,WAAU;AAAC,QAAMN,KAAEC,GAAE;AAAE,SAAO,SAASA,IAAEE,IAAED,IAAEO,IAAE;AAAC,WAAO,EAAER,IAAEE,IAAE,GAAGH,IAAEE,IAAEO,IAAE,CAAC,CAAC;AAAA,EAAC;AAAC,EAAE;AAAzY,IAA2Y,KAAG,WAAU;AAAC,QAAMT,KAAEC,GAAE;AAAE,SAAO,SAASA,IAAEE,IAAED,IAAEO,IAAE;AAAC,WAAO,EAAER,IAAEE,IAAE,GAAGH,IAAEE,IAAEO,IAAE,CAAC,CAAC;AAAA,EAAC;AAAC,EAAE;AAA1d,IAA4d,KAAG,WAAU;AAAC,QAAMT,KAAE,EAAE,GAAEC,KAAEA,GAAE;AAAE,SAAO,SAASE,IAAED,IAAEO,IAAE;AAAC,MAAEN,IAAED,EAAC;AAAE,UAAME,KAAE,GAAGF,EAAC,GAAEG,KAAEF,GAAE;AAAe,WAAO,EAAEF,IAAE,GAAGC,EAAC,CAAC,GAAE,EAAED,IAAEA,IAAEC,GAAEE,IAAEA,EAAC,CAAC,GAAE,EAAEJ,IAAES,IAAER,EAAC,GAAEI,GAAE,KAAGL,GAAE,CAAC,GAAEK,GAAE,KAAGL,GAAE,CAAC,GAAEG;AAAA,EAAC;AAAC,EAAE;", "names": ["t", "e", "r", "n", "a", "i", "c", "s", "u", "o", "w", "f", "l", "y", "p", "g", "x", "$", "j"]}