import {
  x as x3
} from "./chunk-5O73RHQV.js";
import {
  r as r5
} from "./chunk-J4KDDSED.js";
import {
  l as l2
} from "./chunk-TDC6MNNF.js";
import {
  t as t3
} from "./chunk-T5TRCNG4.js";
import {
  n as n3,
  p as p4
} from "./chunk-UVJUTW2U.js";
import {
  n as n4
} from "./chunk-FWXA4I6D.js";
import {
  c as c3
} from "./chunk-WL6G2MRC.js";
import {
  p as p5
} from "./chunk-JZKMTUDN.js";
import {
  r as r4
} from "./chunk-UCWK623G.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import {
  t as t2
} from "./chunk-JEANRG5Q.js";
import {
  c as c2
} from "./chunk-ZVU4V5QV.js";
import {
  x
} from "./chunk-W3CLOCDX.js";
import {
  K,
  c
} from "./chunk-554JGJWA.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import {
  a as a2
} from "./chunk-Q4VCSCSY.js";
import {
  n as n2,
  p as p3
} from "./chunk-BS3GJQ77.js";
import {
  p as p2,
  v as v2
} from "./chunk-VJW7RCN7.js";
import {
  C
} from "./chunk-Q7K3J54I.js";
import {
  U,
  l
} from "./chunk-QUHG7NMD.js";
import {
  k
} from "./chunk-MQ2IOGEF.js";
import {
  x as x2
} from "./chunk-ETY52UBV.js";
import {
  s as s5
} from "./chunk-22GGEXM2.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  U as U2
} from "./chunk-AW4AS2UW.js";
import {
  o as o2
} from "./chunk-7CPUVZNS.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  f2
} from "./chunk-JXLVNWKF.js";
import {
  s as s4
} from "./chunk-7SWS36OI.js";
import {
  r as r3
} from "./chunk-6HCWK637.js";
import {
  A,
  L
} from "./chunk-U4SVMKOQ.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  e,
  n2 as n,
  r as r2,
  t3 as t
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T,
  b,
  s as s2,
  v
} from "./chunk-HP475EI3.js";
import {
  e as e2
} from "./chunk-2CM7MIII.js";
import {
  f
} from "./chunk-EKX3LLYN.js";
import {
  s as s3
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has,
  p
} from "./chunk-REW33H3I.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/mixins/ArcGISMapService.js
var y3 = (y4) => {
  let d = class extends y4 {
    constructor() {
      super(...arguments), this.capabilities = void 0, this.copyright = null, this.fullExtent = null, this.legendEnabled = true, this.spatialReference = null, this.version = void 0, this._allLayersAndTablesPromise = null, this._allLayersAndTablesMap = null;
    }
    readCapabilities(e3, r6) {
      const s6 = r6.capabilities && r6.capabilities.split(",").map((e4) => e4.toLowerCase().trim());
      if (!s6) return { operations: { supportsExportMap: false, supportsExportTiles: false, supportsIdentify: false, supportsQuery: false, supportsTileMap: false }, exportMap: null, exportTiles: null };
      const t4 = this.type, i = "tile" !== t4 && !!r6.supportsDynamicLayers, p6 = s6.includes("query"), a3 = s6.includes("map"), l3 = !!r6.exportTilesAllowed, n5 = s6.includes("tilemap"), u = s6.includes("data"), c4 = "tile" !== t4 && (!r6.tileInfo || i), y5 = "tile" !== t4 && (!r6.tileInfo || i), d2 = "tile" !== t4, m3 = r6.cimVersion && r5.parse(r6.cimVersion), h2 = (m3 == null ? void 0 : m3.since(1, 4)) ?? false, b2 = (m3 == null ? void 0 : m3.since(2, 0)) ?? false;
      return { operations: { supportsExportMap: a3, supportsExportTiles: l3, supportsIdentify: p6, supportsQuery: u, supportsTileMap: n5 }, exportMap: a3 ? { supportsArcadeExpressionForLabeling: h2, supportsSublayersChanges: d2, supportsDynamicLayers: i, supportsSublayerVisibility: c4, supportsSublayerDefinitionExpression: y5, supportsCIMSymbols: b2 } : null, exportTiles: l3 ? { maxExportTilesCount: +r6.maxExportTilesCount } : null };
    }
    readVersion(e3, r6) {
      let s6 = r6.currentVersion;
      return s6 || (s6 = r6.hasOwnProperty("capabilities") || r6.hasOwnProperty("tables") ? 10 : r6.hasOwnProperty("supportedImageFormatTypes") ? 9.31 : 9.3), s6;
    }
    async fetchSublayerInfo(e3, r6) {
      var _a;
      try {
        return await this.fetchAllLayersAndTables(r6), (_a = this._allLayersAndTablesMap) == null ? void 0 : _a.get(e3);
      } catch {
        return;
      }
    }
    async fetchAllLayersAndTables(e3) {
      await this.load(e3), this._allLayersAndTablesPromise || (this._allLayersAndTablesPromise = U2(L(this.url).path + "/layers", { responseType: "json", query: { f: "json", ...this.customParameters, token: this.apiKey } }).then((e4) => {
        this._allLayersAndTablesMap = /* @__PURE__ */ new Map();
        for (const r6 of e4.data.layers) this._allLayersAndTablesMap.set(r6.id, r6);
        return { result: e4.data };
      }, (e4) => ({ error: e4 })));
      const o3 = await this._allLayersAndTablesPromise;
      if (f(e3), "result" in o3) return o3.result;
      throw o3.error;
    }
  };
  return e([y({ readOnly: true })], d.prototype, "capabilities", void 0), e([o("service", "capabilities", ["capabilities", "exportTilesAllowed", "maxExportTilesCount", "supportsDynamicLayers", "tileInfo"])], d.prototype, "readCapabilities", null), e([y({ json: { read: { source: "copyrightText" } } })], d.prototype, "copyright", void 0), e([y({ type: w })], d.prototype, "fullExtent", void 0), e([y(v2)], d.prototype, "id", void 0), e([y({ type: Boolean, json: { origins: { service: { read: { enabled: false } } }, read: { source: "showLegend" }, write: { target: "showLegend" } } })], d.prototype, "legendEnabled", void 0), e([y(p2)], d.prototype, "popupEnabled", void 0), e([y({ type: f2 })], d.prototype, "spatialReference", void 0), e([y({ readOnly: true })], d.prototype, "version", void 0), e([o("version", ["currentVersion", "capabilities", "tables", "supportedImageFormatTypes"])], d.prototype, "readVersion", null), d = e([a("esri.layers.mixins.ArcGISMapService")], d), d;
};

// node_modules/@arcgis/core/layers/support/Sublayer.js
var J;
function $(e3) {
  return null != e3 && "esriSMS" === e3.type;
}
function G(e3, r6, t4) {
  var _a;
  const i = this.originIdOf(r6) >= t(t4.origin);
  return { ignoreOrigin: true, allowNull: i, enabled: !!t4 && ("map-image" === ((_a = t4.layer) == null ? void 0 : _a.type) && (t4.writeSublayerStructure || i)) };
}
function H(e3, r6, t4) {
  var _a;
  return { enabled: !!t4 && ("tile" === ((_a = t4.layer) == null ? void 0 : _a.type) && this._isOverridden(r6)) };
}
function z(e3, r6, t4) {
  return { ignoreOrigin: true, enabled: t4 && t4.writeSublayerStructure || false };
}
function K2(e3, r6, t4) {
  return { ignoreOrigin: true, enabled: !!t4 && (t4.writeSublayerStructure || this.originIdOf(r6) >= t(t4.origin)) };
}
var W = 0;
var X = /* @__PURE__ */ new Set();
X.add("layer"), X.add("parent"), X.add("loaded"), X.add("loadStatus"), X.add("loadError"), X.add("loadWarnings");
var Y = J = class extends a2(O(s5(m))) {
  constructor(e3) {
    super(e3), this.capabilities = void 0, this.fields = null, this.fullExtent = null, this.geometryType = null, this.globalIdField = null, this.legendEnabled = true, this.objectIdField = null, this.popupEnabled = true, this.popupTemplate = null, this.sourceJSON = null, this.title = null, this.typeIdField = null, this.types = null, this._lastParsedUrl = null;
  }
  async load(e3) {
    return this.addResolvingPromise((async () => {
      const { layer: r6, source: t4, url: i } = this;
      if (!r6 && !i) throw new s3("sublayer:missing-layer", "Sublayer can't be loaded without being part of a layer", { sublayer: this });
      let s6 = null;
      if (!r6 || this.originIdOf("url") > r2.SERVICE || "data-layer" === (t4 == null ? void 0 : t4.type)) {
        s6 = (await U2(i, { responseType: "json", query: { f: "json" }, ...e3 })).data;
      } else {
        let i2 = this.id;
        "map-layer" === (t4 == null ? void 0 : t4.type) && (i2 = t4.mapLayerId), s6 = await r6.fetchSublayerInfo(i2, e3);
      }
      s6 && (this.sourceJSON = s6, this.read({ layerDefinition: s6 }, { origin: "service" }));
    })()), this;
  }
  readCapabilities(e3, r6) {
    r6 = r6.layerDefinition || r6;
    const { operations: { supportsQuery: t4, supportsQueryAttachments: i }, query: { supportsFormatPBF: o3 }, data: { supportsAttachment: s6 } } = n3(r6, this.url);
    return { exportMap: { supportsModification: !!r6.canModifyLayer }, operations: { supportsQuery: t4, supportsQueryAttachments: i }, data: { supportsAttachment: s6 }, query: { supportsFormatPBF: o3 } };
  }
  get defaultPopupTemplate() {
    return this.createPopupTemplate();
  }
  set definitionExpression(e3) {
    this._setAndNotifyLayer("definitionExpression", e3);
  }
  get fieldsIndex() {
    return new r4(this.fields || []);
  }
  set floorInfo(e3) {
    this._setAndNotifyLayer("floorInfo", e3);
  }
  readGlobalIdFieldFromService(e3, r6) {
    if ((r6 = r6.layerDefinition || r6).globalIdField) return r6.globalIdField;
    if (r6.fields) {
      for (const t4 of r6.fields) if ("esriFieldTypeGlobalID" === t4.type) return t4.name;
    }
  }
  get id() {
    const e3 = this._get("id");
    return e3 ?? W++;
  }
  set id(e3) {
    var _a, _b, _c;
    this._get("id") !== e3 && (false !== ((_c = (_b = (_a = this.layer) == null ? void 0 : _a.capabilities) == null ? void 0 : _b.exportMap) == null ? void 0 : _c.supportsDynamicLayers) ? this._set("id", e3) : this._logLockedError("id", "capability not available 'layer.capabilities.exportMap.supportsDynamicLayers'"));
  }
  set labelingInfo(e3) {
    this._setAndNotifyLayer("labelingInfo", e3);
  }
  writeLabelingInfo(e3, r6, t4, i) {
    e3 && e3.length && (r6.layerDefinition = { drawingInfo: { labelingInfo: e3.map((e4) => e4.write({}, i)) } });
  }
  set labelsVisible(e3) {
    this._setAndNotifyLayer("labelsVisible", e3);
  }
  set layer(e3) {
    this._set("layer", e3), this.sublayers && this.sublayers.forEach((r6) => r6.layer = e3);
  }
  set listMode(e3) {
    this._set("listMode", e3);
  }
  set minScale(e3) {
    this._setAndNotifyLayer("minScale", e3);
  }
  readMinScale(e3, r6) {
    return r6.minScale || r6.layerDefinition && r6.layerDefinition.minScale || 0;
  }
  set maxScale(e3) {
    this._setAndNotifyLayer("maxScale", e3);
  }
  readMaxScale(e3, r6) {
    return r6.maxScale || r6.layerDefinition && r6.layerDefinition.maxScale || 0;
  }
  get effectiveScaleRange() {
    const { minScale: e3, maxScale: r6 } = this;
    return { minScale: e3, maxScale: r6 };
  }
  readObjectIdFieldFromService(e3, r6) {
    if ((r6 = r6.layerDefinition || r6).objectIdField) return r6.objectIdField;
    if (r6.fields) {
      for (const t4 of r6.fields) if ("esriFieldTypeOID" === t4.type) return t4.name;
    }
  }
  set opacity(e3) {
    this._setAndNotifyLayer("opacity", e3);
  }
  readOpacity(e3, r6) {
    var _a;
    const t4 = r6.layerDefinition;
    return 1 - 0.01 * ((null != (t4 == null ? void 0 : t4.transparency) ? t4.transparency : (_a = t4 == null ? void 0 : t4.drawingInfo) == null ? void 0 : _a.transparency) ?? 0);
  }
  writeOpacity(e3, r6, t4, i) {
    r6.layerDefinition = { drawingInfo: { transparency: 100 - 100 * e3 } };
  }
  writeParent(e3, r6) {
    this.parent && this.parent !== this.layer ? r6.parentLayerId = s2(this.parent.id) : r6.parentLayerId = -1;
  }
  get queryTask() {
    var _a;
    if (!this.layer) return null;
    const { spatialReference: e3 } = this.layer, r6 = "gdbVersion" in this.layer ? this.layer.gdbVersion : void 0, { capabilities: t4, fieldsIndex: i } = this, o3 = has("featurelayer-pbf") && (t4 == null ? void 0 : t4.query.supportsFormatPBF), s6 = ((_a = t4 == null ? void 0 : t4.operations) == null ? void 0 : _a.supportsQueryAttachments) ?? false;
    return new x3({ url: this.url, pbfSupported: o3, fieldsIndex: i, gdbVersion: r6, sourceSpatialReference: e3, queryAttachmentsSupported: s6 });
  }
  set renderer(e3) {
    if (e3) {
      for (const r6 of e3.getSymbols()) if (x2(r6)) {
        s.getLogger(this.declaredClass).warn("Sublayer renderer should use 2D symbols");
        break;
      }
    }
    this._setAndNotifyLayer("renderer", e3);
  }
  get source() {
    return this._get("source") || new c({ mapLayerId: this.id });
  }
  set source(e3) {
    this._setAndNotifyLayer("source", e3);
  }
  set sublayers(e3) {
    this._handleSublayersChange(e3, this._get("sublayers")), this._set("sublayers", e3);
  }
  castSublayers(e3) {
    return b(j.ofType(J), e3);
  }
  writeSublayers(e3, r6, t4) {
    var _a;
    ((_a = this.sublayers) == null ? void 0 : _a.length) && (r6[t4] = this.sublayers.map((e4) => e4.id).toArray().reverse());
  }
  readTypeIdField(e3, r6) {
    let t4 = (r6 = r6.layerDefinition || r6).typeIdField;
    if (t4 && r6.fields) {
      t4 = t4.toLowerCase();
      const e4 = r6.fields.find((e5) => e5.name.toLowerCase() === t4);
      e4 && (t4 = e4.name);
    }
    return t4;
  }
  get url() {
    var _a;
    const e3 = ((_a = this.layer) == null ? void 0 : _a.parsedUrl) ?? this._lastParsedUrl, r6 = this.source;
    if (!e3) return null;
    if (this._lastParsedUrl = e3, "map-layer" === (r6 == null ? void 0 : r6.type)) return `${e3.path}/${r6.mapLayerId}`;
    const t4 = { layer: JSON.stringify({ source: this.source }) };
    return `${e3.path}/dynamicLayer?${A(t4)}`;
  }
  set url(e3) {
    this._overrideIfSome("url", e3);
  }
  set visible(e3) {
    this._setAndNotifyLayer("visible", e3);
  }
  writeVisible(e3, r6, t4, i) {
    r6[t4] = this.getAtOrigin("defaultVisibility", "service") || e3;
  }
  clone() {
    const { store: e3 } = e2(this), r6 = new J();
    return e2(r6).store = e3.clone(X), this.commitProperty("url"), r6._lastParsedUrl = this._lastParsedUrl, r6;
  }
  createPopupTemplate(e3) {
    return p5(this, e3);
  }
  createQuery() {
    return new x({ returnGeometry: true, where: this.definitionExpression || "1=1" });
  }
  async createFeatureLayer() {
    var _a;
    if (this.hasOwnProperty("sublayers")) return null;
    const { layer: e3 } = this, r6 = e3 == null ? void 0 : e3.parsedUrl, t4 = new (0, (await import("./@arcgis_core_layers_FeatureLayer__js.js")).default)({ url: r6 == null ? void 0 : r6.path });
    return r6 && this.source && ("map-layer" === this.source.type ? t4.layerId = this.source.mapLayerId : t4.dynamicDataSource = this.source), null != (e3 == null ? void 0 : e3.refreshInterval) && (t4.refreshInterval = e3.refreshInterval), this.definitionExpression && (t4.definitionExpression = this.definitionExpression), this.floorInfo && (t4.floorInfo = p(this.floorInfo)), this.originIdOf("labelingInfo") > r2.SERVICE && (t4.labelingInfo = p(this.labelingInfo)), this.originIdOf("labelsVisible") > r2.DEFAULTS && (t4.labelsVisible = this.labelsVisible), this.originIdOf("legendEnabled") > r2.DEFAULTS && (t4.legendEnabled = this.legendEnabled), this.originIdOf("visible") > r2.DEFAULTS && (t4.visible = this.visible), this.originIdOf("minScale") > r2.DEFAULTS && (t4.minScale = this.minScale), this.originIdOf("maxScale") > r2.DEFAULTS && (t4.maxScale = this.maxScale), this.originIdOf("opacity") > r2.DEFAULTS && (t4.opacity = this.opacity), this.originIdOf("popupTemplate") > r2.DEFAULTS && (t4.popupTemplate = p(this.popupTemplate)), this.originIdOf("renderer") > r2.SERVICE && (t4.renderer = p(this.renderer)), "data-layer" === ((_a = this.source) == null ? void 0 : _a.type) && (t4.dynamicDataSource = this.source.clone()), this.originIdOf("title") > r2.DEFAULTS && (t4.title = this.title), "map-image" === (e3 == null ? void 0 : e3.type) && e3.originIdOf("customParameters") > r2.DEFAULTS && (t4.customParameters = e3.customParameters), "tile" === (e3 == null ? void 0 : e3.type) && e3.originIdOf("customParameters") > r2.DEFAULTS && (t4.customParameters = e3.customParameters), t4;
  }
  getField(e3) {
    return this.fieldsIndex.get(e3);
  }
  getFeatureType(e3) {
    const { typeIdField: r6, types: t4 } = this;
    if (!r6 || !e3) return null;
    const i = e3.attributes ? e3.attributes[r6] : void 0;
    if (null == i) return null;
    let o3 = null;
    return t4 == null ? void 0 : t4.some((e4) => {
      const { id: r7 } = e4;
      return null != r7 && (r7.toString() === i.toString() && (o3 = e4), !!o3);
    }), o3;
  }
  getFieldDomain(e3, r6) {
    const t4 = r6 && r6.feature, i = this.getFeatureType(t4);
    if (i) {
      const r7 = i.domains && i.domains[e3];
      if (r7 && "inherited" !== r7.type) return r7;
    }
    return this._getLayerDomain(e3);
  }
  async queryAttachments(e3, r6) {
    var _a, _b;
    await this.load(), e3 = c2.from(e3);
    const t4 = this.capabilities;
    if (!((_a = t4 == null ? void 0 : t4.data) == null ? void 0 : _a.supportsAttachment)) throw new s3("queryAttachments:not-supported", "this layer doesn't support attachments");
    const { attachmentTypes: i, objectIds: o3, globalIds: s6, num: a3, size: n5, start: p6, where: y4 } = e3;
    if (!((_b = t4 == null ? void 0 : t4.operations) == null ? void 0 : _b.supportsQueryAttachments)) {
      if ((i == null ? void 0 : i.length) > 0 || (s6 == null ? void 0 : s6.length) > 0 || (n5 == null ? void 0 : n5.length) > 0 || a3 || p6 || y4) throw new s3("queryAttachments:option-not-supported", "when 'capabilities.operations.supportsQueryAttachments' is false, only objectIds is supported", e3);
    }
    if (!((o3 == null ? void 0 : o3.length) || (s6 == null ? void 0 : s6.length) || y4)) throw new s3("queryAttachments:invalid-query", "'objectIds', 'globalIds', or 'where' are required to perform attachment query", e3);
    return this.queryTask.executeAttachmentQuery(e3, r6);
  }
  async queryFeatures(e3 = this.createQuery(), r6) {
    var _a, _b;
    if (await this.load(), !this.capabilities.operations.supportsQuery) throw new s3("queryFeatures:not-supported", "this layer doesn't support queries.");
    if (!this.url) throw new s3("queryFeatures:not-supported", "this layer has no url.");
    const t4 = await this.queryTask.execute(e3, { ...r6, query: { ...(_a = this.layer) == null ? void 0 : _a.customParameters, token: (_b = this.layer) == null ? void 0 : _b.apiKey } });
    if (t4 == null ? void 0 : t4.features) for (const i of t4.features) i.sourceLayer = this;
    return t4;
  }
  toExportImageJSON(e3) {
    var _a;
    const r6 = { id: this.id, source: ((_a = this.source) == null ? void 0 : _a.toJSON()) || { mapLayerId: this.id, type: "mapLayer" } }, t4 = t2(e3, this.definitionExpression);
    r(t4) && (r6.definitionExpression = t4);
    const i = ["renderer", "labelingInfo", "opacity", "labelsVisible"].reduce((e4, r7) => (e4[r7] = this.originIdOf(r7), e4), {}), o3 = Object.keys(i).some((e4) => i[e4] > r2.SERVICE);
    if (o3) {
      const e4 = r6.drawingInfo = {};
      if (i.renderer > r2.SERVICE && (e4.renderer = this.renderer ? this.renderer.toJSON() : null), i.labelsVisible > r2.SERVICE && (e4.showLabels = this.labelsVisible), this.labelsVisible && i.labelingInfo > r2.SERVICE) {
        !this.loaded && this.labelingInfo.some((e5) => !e5.labelPlacement) && s.getLogger(this.declaredClass).warnOnce(`A Sublayer (title: ${this.title}, id: ${this.id}) has an undefined 'labelPlacement' and so labels cannot be displayed. Either define a valid 'labelPlacement' or call Sublayer.load() to use a default value based on geometry type.`, { sublayer: this });
        let r7 = this.labelingInfo;
        r(this.geometryType) && (r7 = c3(this.labelingInfo, o2.toJSON(this.geometryType))), e4.labelingInfo = r7.filter((e5) => e5.labelPlacement).map((e5) => e5.toJSON({ origin: "service", layer: this.layer })), e4.showLabels = true;
      }
      i.opacity > r2.SERVICE && (e4.transparency = 100 - 100 * this.opacity), this._assignDefaultSymbolColors(e4.renderer);
    }
    return r6;
  }
  _assignDefaultSymbolColors(e3) {
    this._forEachSimpleMarkerSymbols(e3, (e4) => {
      e4.color || "esriSMSX" !== e4.style && "esriSMSCross" !== e4.style || (e4.outline && e4.outline.color ? e4.color = e4.outline.color : e4.color = [0, 0, 0, 0]);
    });
  }
  _forEachSimpleMarkerSymbols(e3, r6) {
    if (e3) {
      const t4 = ("uniqueValueInfos" in e3 ? e3.uniqueValueInfos : "classBreakInfos" in e3 ? e3.classBreakInfos : null) ?? [];
      for (const e4 of t4) $(e4.symbol) && r6(e4.symbol);
      "symbol" in e3 && $(e3.symbol) && r6(e3.symbol), "defaultSymbol" in e3 && $(e3.defaultSymbol) && r6(e3.defaultSymbol);
    }
  }
  _setAndNotifyLayer(e3, r6) {
    var _a, _b, _c, _d;
    const t4 = this.layer, i = this._get(e3);
    let o3, s6;
    switch (e3) {
      case "definitionExpression":
      case "floorInfo":
        o3 = "supportsSublayerDefinitionExpression";
        break;
      case "minScale":
      case "maxScale":
      case "visible":
        o3 = "supportsSublayerVisibility";
        break;
      case "labelingInfo":
      case "labelsVisible":
      case "opacity":
      case "renderer":
      case "source":
        o3 = "supportsDynamicLayers", s6 = "supportsModification";
    }
    const a3 = e2(this).getDefaultOrigin();
    if ("service" !== a3) {
      if (o3 && false === ((_c = (_b = (_a = this.layer) == null ? void 0 : _a.capabilities) == null ? void 0 : _b.exportMap) == null ? void 0 : _c[o3])) return void this._logLockedError(e3, `capability not available 'layer.capabilities.exportMap.${o3}'`);
      if (s6 && false === ((_d = this.capabilities) == null ? void 0 : _d.exportMap[s6])) return void this._logLockedError(e3, `capability not available 'capabilities.exportMap.${s6}'`);
    }
    "source" !== e3 || "not-loaded" === this.loadStatus ? (this._set(e3, r6), "service" !== a3 && i !== r6 && t4 && t4.emit && t4.emit("sublayer-update", { propertyName: e3, target: this })) : this._logLockedError(e3, "'source' can't be changed after calling sublayer.load()");
  }
  _handleSublayersChange(e3, r6) {
    r6 && (r6.forEach((e4) => {
      e4.parent = null, e4.layer = null;
    }), this.handles.removeAll()), e3 && (e3.forEach((e4) => {
      e4.parent = this, e4.layer = this.layer;
    }), this.handles.add([e3.on("after-add", ({ item: e4 }) => {
      e4.parent = this, e4.layer = this.layer;
    }), e3.on("after-remove", ({ item: e4 }) => {
      e4.parent = null, e4.layer = null;
    }), e3.on("before-changes", (e4) => {
      var _a, _b, _c;
      const r7 = (_c = (_b = (_a = this.layer) == null ? void 0 : _a.capabilities) == null ? void 0 : _b.exportMap) == null ? void 0 : _c.supportsSublayersChanges;
      null == r7 || r7 || (s.getLogger(this.declaredClass).error(new s3("sublayer:sublayers-non-modifiable", "Sublayer can't be added, moved, or removed from the layer's sublayers", { sublayer: this, layer: this.layer })), e4.preventDefault());
    })]));
  }
  _logLockedError(e3, r6) {
    const { layer: t4, declaredClass: i } = this;
    s.getLogger(i).error(new s3("sublayer:locked", `Property '${String(e3)}' can't be changed on Sublayer from the layer '${t4 == null ? void 0 : t4.id}'`, { reason: r6, sublayer: this, layer: t4 }));
  }
  _getLayerDomain(e3) {
    const r6 = this.fieldsIndex.get(e3);
    return r6 ? r6.domain : null;
  }
};
Y.test = { isMapImageLayerOverridePolicy: (e3) => e3 === z || e3 === G, isTileImageLayerOverridePolicy: (e3) => e3 === H }, e([y({ readOnly: true })], Y.prototype, "capabilities", void 0), e([o("service", "capabilities", ["layerDefinition.canModifyLayer", "layerDefinition.capabilities"])], Y.prototype, "readCapabilities", null), e([y()], Y.prototype, "defaultPopupTemplate", null), e([y({ type: String, value: null, json: { name: "layerDefinition.definitionExpression", write: { allowNull: true, overridePolicy: G } } })], Y.prototype, "definitionExpression", null), e([y({ type: [y2], json: { origins: { service: { read: { source: "layerDefinition.fields" } } } } })], Y.prototype, "fields", void 0), e([y({ readOnly: true })], Y.prototype, "fieldsIndex", null), e([y({ type: p4, value: null, json: { name: "layerDefinition.floorInfo", read: { source: "layerDefinition.floorInfo" }, write: { target: "layerDefinition.floorInfo", overridePolicy: G }, origins: { "web-scene": { read: false, write: false } } } })], Y.prototype, "floorInfo", null), e([y({ type: w, json: { read: { source: "layerDefinition.extent" } } })], Y.prototype, "fullExtent", void 0), e([y({ type: o2.apiValues, json: { origins: { service: { name: "layerDefinition.geometryType", read: { reader: o2.read } } } } })], Y.prototype, "geometryType", void 0), e([y({ type: String })], Y.prototype, "globalIdField", void 0), e([o("service", "globalIdField", ["layerDefinition.globalIdField", "layerDefinition.fields"])], Y.prototype, "readGlobalIdFieldFromService", null), e([y({ type: T, json: { write: { ignoreOrigin: true } } })], Y.prototype, "id", null), e([y({ value: null, type: [C], json: { read: { source: "layerDefinition.drawingInfo.labelingInfo" }, write: { target: "layerDefinition.drawingInfo.labelingInfo", overridePolicy: z } } })], Y.prototype, "labelingInfo", null), e([r3("labelingInfo")], Y.prototype, "writeLabelingInfo", null), e([y({ type: Boolean, value: true, json: { read: { source: "layerDefinition.drawingInfo.showLabels" }, write: { target: "layerDefinition.drawingInfo.showLabels", overridePolicy: z } } })], Y.prototype, "labelsVisible", null), e([y({ value: null })], Y.prototype, "layer", null), e([y({ type: Boolean, value: true, json: { origins: { service: { read: { enabled: false } } }, read: { source: "showLegend" }, write: { target: "showLegend", overridePolicy: K2 } } })], Y.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide", "hide-children"], value: "show", json: { read: false, write: false, origins: { "web-scene": { read: true, write: true } } } })], Y.prototype, "listMode", null), e([y({ type: Number, value: 0, json: { write: { overridePolicy: z } } })], Y.prototype, "minScale", null), e([o("minScale", ["minScale", "layerDefinition.minScale"])], Y.prototype, "readMinScale", null), e([y({ type: Number, value: 0, json: { write: { overridePolicy: z } } })], Y.prototype, "maxScale", null), e([o("maxScale", ["maxScale", "layerDefinition.maxScale"])], Y.prototype, "readMaxScale", null), e([y({ readOnly: true })], Y.prototype, "effectiveScaleRange", null), e([y({ type: String })], Y.prototype, "objectIdField", void 0), e([o("service", "objectIdField", ["layerDefinition.objectIdField", "layerDefinition.fields"])], Y.prototype, "readObjectIdFieldFromService", null), e([y({ type: Number, value: 1, json: { write: { target: "layerDefinition.drawingInfo.transparency", overridePolicy: z } } })], Y.prototype, "opacity", null), e([o("opacity", ["layerDefinition.drawingInfo.transparency", "layerDefinition.transparency"])], Y.prototype, "readOpacity", null), e([r3("opacity")], Y.prototype, "writeOpacity", null), e([y({ json: { type: T, write: { target: "parentLayerId", writerEnsuresNonNull: true, overridePolicy: z } } })], Y.prototype, "parent", void 0), e([r3("parent")], Y.prototype, "writeParent", null), e([y({ type: Boolean, value: true, json: { read: { source: "disablePopup", reader: (e3, r6) => !r6.disablePopup }, write: { target: "disablePopup", overridePolicy: K2, writer(e3, r6, t4) {
  r6[t4] = !e3;
} } } })], Y.prototype, "popupEnabled", void 0), e([y({ type: k, json: { read: { source: "popupInfo" }, write: { target: "popupInfo", overridePolicy: K2 } } })], Y.prototype, "popupTemplate", void 0), e([y({ readOnly: true })], Y.prototype, "queryTask", null), e([y({ types: p3, value: null, json: { name: "layerDefinition.drawingInfo.renderer", write: { overridePolicy: z }, origins: { "web-scene": { types: n2, name: "layerDefinition.drawingInfo.renderer", write: { overridePolicy: z } } } } })], Y.prototype, "renderer", null), e([y({ types: { key: "type", base: null, typeMap: { "data-layer": K, "map-layer": c } }, cast(e3) {
  if (e3) {
    if ("mapLayerId" in e3) return v(c, e3);
    if ("dataSource" in e3) return v(K, e3);
  }
  return e3;
}, json: { name: "layerDefinition.source", write: { overridePolicy: z } } })], Y.prototype, "source", null), e([y()], Y.prototype, "sourceJSON", void 0), e([y({ value: null, json: { type: [T], write: { target: "subLayerIds", allowNull: true, overridePolicy: z } } })], Y.prototype, "sublayers", null), e([s4("sublayers")], Y.prototype, "castSublayers", null), e([r3("sublayers")], Y.prototype, "writeSublayers", null), e([y({ type: String, json: { name: "name", write: { overridePolicy: K2 } } })], Y.prototype, "title", void 0), e([y({ type: String })], Y.prototype, "typeIdField", void 0), e([o("typeIdField", ["layerDefinition.typeIdField"])], Y.prototype, "readTypeIdField", null), e([y({ type: [n4], json: { origins: { service: { read: { source: "layerDefinition.types" } } } } })], Y.prototype, "types", void 0), e([y({ type: String, json: { read: { source: "layerUrl" }, write: { target: "layerUrl", overridePolicy: H } } })], Y.prototype, "url", null), e([y({ type: Boolean, value: true, json: { read: { source: "defaultVisibility" }, write: { target: "defaultVisibility", overridePolicy: z } } })], Y.prototype, "visible", null), e([r3("visible")], Y.prototype, "writeVisible", null), Y = J = e([a("esri.layers.support.Sublayer")], Y);
var Z = Y;

// node_modules/@arcgis/core/layers/mixins/SublayersOwner.js
var f3 = s.getLogger("esri.layers.TileLayer");
function S(e3, r6) {
  const s6 = [], t4 = {};
  return e3 ? (e3.forEach((e4) => {
    const o3 = new Z();
    if (o3.read(e4, r6), t4[o3.id] = o3, null != e4.parentLayerId && -1 !== e4.parentLayerId) {
      const r7 = t4[e4.parentLayerId];
      r7.sublayers || (r7.sublayers = []), r7.sublayers.unshift(o3);
    } else s6.unshift(o3);
  }), s6) : s6;
}
var h = j.ofType(Z);
function m2(e3, r6) {
  e3 && e3.forEach((e4) => {
    r6(e4), e4.sublayers && e4.sublayers.length && m2(e4.sublayers, r6);
  });
}
var E = (o3) => {
  let E2 = class extends o3 {
    constructor(...e3) {
      super(...e3), this.allSublayers = new l2({ getCollections: () => [this.sublayers], getChildrenFunction: (e4) => e4.sublayers }), this.sublayersSourceJSON = { [r2.SERVICE]: {}, [r2.PORTAL_ITEM]: {}, [r2.WEB_SCENE]: {}, [r2.WEB_MAP]: {} }, this.addHandles(l(() => this.sublayers, (e4, r6) => this._handleSublayersChange(e4, r6), U));
    }
    readSublayers(e3, r6) {
      if (!r6 || !e3) return;
      const { sublayersSourceJSON: s6 } = this, t4 = t(r6.origin);
      if (t4 < r2.SERVICE) return;
      if (s6[t4] = { context: r6, visibleLayers: e3.visibleLayers || s6[t4].visibleLayers, layers: e3.layers || s6[t4].layers }, t4 > r2.SERVICE) return;
      this._set("serviceSublayers", this.createSublayersForOrigin("service").sublayers);
      const { sublayers: o4, origin: a3 } = this.createSublayersForOrigin("web-document"), i = e2(this);
      i.setDefaultOrigin(a3), this._set("sublayers", new h(o4)), i.setDefaultOrigin("user");
    }
    findSublayerById(e3) {
      return this.allSublayers.find((r6) => r6.id === e3);
    }
    createServiceSublayers() {
      return this.createSublayersForOrigin("service").sublayers;
    }
    createSublayersForOrigin(e3) {
      const r6 = t("web-document" === e3 ? "web-map" : e3);
      let s6 = r2.SERVICE, t4 = this.sublayersSourceJSON[r2.SERVICE].layers, o4 = this.sublayersSourceJSON[r2.SERVICE].context, a3 = null;
      const i = [r2.PORTAL_ITEM, r2.WEB_SCENE, r2.WEB_MAP].filter((e4) => e4 <= r6);
      for (const y4 of i) {
        const e4 = this.sublayersSourceJSON[y4];
        t3(e4.layers) && (s6 = y4, t4 = e4.layers, o4 = e4.context, e4.visibleLayers && (a3 = { visibleLayers: e4.visibleLayers, context: e4.context }));
      }
      const l3 = [r2.PORTAL_ITEM, r2.WEB_SCENE, r2.WEB_MAP].filter((e4) => e4 > s6 && e4 <= r6);
      let n5 = null;
      for (const y4 of l3) {
        const { layers: e4, visibleLayers: r7, context: s7 } = this.sublayersSourceJSON[y4];
        e4 && (n5 = { layers: e4, context: s7 }), r7 && (a3 = { visibleLayers: r7, context: s7 });
      }
      const u = S(t4, o4), p6 = /* @__PURE__ */ new Map(), f4 = /* @__PURE__ */ new Set();
      if (n5) for (const y4 of n5.layers) p6.set(y4.id, y4);
      if (a3 == null ? void 0 : a3.visibleLayers) for (const y4 of a3.visibleLayers) f4.add(y4);
      return m2(u, (e4) => {
        n5 && e4.read(p6.get(e4.id), n5.context), a3 && e4.read({ defaultVisibility: f4.has(e4.id) }, a3.context);
      }), { origin: n(s6), sublayers: new h({ items: u }) };
    }
    read(e3, r6) {
      super.read(e3, r6), this.readSublayers(e3, r6);
    }
    _handleSublayersChange(e3, r6) {
      r6 && (r6.forEach((e4) => {
        e4.parent = null, e4.layer = null;
      }), this.handles.remove("sublayers-owner")), e3 && (e3.forEach((e4) => {
        e4.parent = this, e4.layer = this;
      }), this.handles.add([e3.on("after-add", ({ item: e4 }) => {
        e4.parent = this, e4.layer = this;
      }), e3.on("after-remove", ({ item: e4 }) => {
        e4.parent = null, e4.layer = null;
      })], "sublayers-owner"), "tile" === this.type && this.handles.add(e3.on("before-changes", (e4) => {
        f3.error(new s3("tilelayer:sublayers-non-modifiable", "ISublayer can't be added, moved, or removed from the layer's sublayers", { layer: this })), e4.preventDefault();
      }), "sublayers-owner"));
    }
  };
  return e([y({ readOnly: true })], E2.prototype, "allSublayers", void 0), e([y({ readOnly: true, type: j.ofType(Z) })], E2.prototype, "serviceSublayers", void 0), e([y({ value: null, type: h, json: { read: false, write: { allowNull: true, ignoreOrigin: true } } })], E2.prototype, "sublayers", void 0), e([y({ readOnly: true })], E2.prototype, "sublayersSourceJSON", void 0), E2 = e([a("esri.layers.mixins.SublayersOwner")], E2), E2;
};

export {
  y3 as y,
  Z,
  E
};
//# sourceMappingURL=chunk-LHPAMQPJ.js.map
