{"version": 3, "sources": ["../../@arcgis/core/arcade/Feature.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"./Dictionary.js\";import t from\"./ImmutableArray.js\";import{a as i,b as r,c as s,t as n,k as a,f as o,d as l}from\"../chunks/languageUtils.js\";import u from\"../geometry/Geometry.js\";import d from\"../geometry/Point.js\";import{fromJSON as f}from\"../geometry/support/jsonUtils.js\";import{convertToGeometry as c}from\"../layers/graphics/featureConversionUtils.js\";import{isSome as h,unwrap as m}from\"../core/maybe.js\";import{ArcadeDate as y}from\"./ArcadeDate.js\";import{ArcadeExecutionError as _,ExecutionErrorCodes as b}from\"./executionError.js\";import{DateTimeReferenceFieldIndex as p}from\"./arcadeTimeUtils.js\";class g{constructor(){this.arcadeDeclaredClass=\"esri.arcade.Feature\",this._optimizedGeomDefinition=null,this._geometry=null,this.attributes=null,this._layer=null,this._datesfixed=!0,this.dateTimeReferenceFieldIndex=null,this.contextTimeReference=null,this.immutable=!0,this._datefields=null,this.immutable=!0}static createFromGraphic(e,t){const i=new g;return i.contextTimeReference=t??null,i._geometry=h(e.geometry)?e.geometry:null,void 0===e.attributes||null===e.attributes?i.attributes={}:i.attributes=e.attributes,e._sourceLayer?(i._layer=e._sourceLayer,i._datesfixed=!1):e._layer?(i._layer=e._layer,i._datesfixed=!1):e.layer&&\"fields\"in e.layer?(i._layer=e.layer,i._datesfixed=!1):e.sourceLayer&&\"fields\"in e.sourceLayer&&(i._layer=e.sourceLayer,i._datesfixed=!1),i._layer&&!1===i._datesfixed&&(void 0!==i._layer.dateTimeReferenceFieldIndex?i.dateTimeReferenceFieldIndex=i._layer.dateTimeReferenceFieldIndex:i.dateTimeReferenceFieldIndex=p.createFromLayer(i._layer)),i}static createFromArcadeFeature(e){const t=new g;return t._datesfixed=e._datesfixed,t.attributes=e.attributes,t._geometry=e._geometry,t._optimizedGeomDefinition=e._optimizedGeomDefinition,e._layer&&(t._layer=e._layer),t.dateTimeReferenceFieldIndex=e.dateTimeReferenceFieldIndex,t.contextTimeReference=e.contextTimeReference,t}static createFromOptimisedFeature(e,t,i){const r=new g;return r._geometry=e.geometry?{geometry:e.geometry}:null,r._optimizedGeomDefinition=i,r.attributes=e.attributes||{},r._layer=t,r._datesfixed=!1,r}static createFromArcadeDictionary(t){const i=new g;return i.attributes=t.field(\"attributes\"),null!==i.attributes&&i.attributes instanceof e?(i.attributes=i.attributes.attributes,null===i.attributes&&(i.attributes={})):i.attributes={},i._geometry=t.field(\"geometry\"),null!==i._geometry&&(i._geometry instanceof e?i._geometry=g.parseGeometryFromDictionary(i._geometry):i._geometry instanceof u||(i._geometry=null)),i}static createFromGraphicLikeObject(e,t,i=null,r){const s=new g;return s.contextTimeReference=r??null,null===t&&(t={}),s.attributes=t,s._geometry=h(e)?e:null,s._layer=i,s._layer&&(s._datesfixed=!1,void 0!==s._layer.dateTimeReferenceFieldIndex?s.dateTimeReferenceFieldIndex=s._layer.dateTimeReferenceFieldIndex:s.dateTimeReferenceFieldIndex=p.createFromLayer(s._layer)),s}repurposeFromGraphicLikeObject(e,t,i=null){null===t&&(t={}),this.attributes=t,this._geometry=e||null,this._layer=i,this._layer?this._datesfixed=!1:this._datesfixed=!0}get layerPreferredTimeZone(){return this.dateTimeReferenceFieldIndex?.layerPreferredTimeZone??\"\"}fieldSourceTimeZone(e){return this.dateTimeReferenceFieldIndex?.fieldTimeZone(e)??\"\"}castToText(e=!1){let a=\"\";!1===this._datesfixed&&this._fixDates();for(const o in this.attributes){\"\"!==a&&(a+=\",\");const l=this.attributes[o];null==l?a+=JSON.stringify(o)+\":null\":i(l)||r(l)||s(l)?a+=JSON.stringify(o)+\":\"+JSON.stringify(l):l instanceof u?a+=JSON.stringify(o)+\":\"+n(l):l instanceof t||l instanceof Array?a+=JSON.stringify(o)+\":\"+n(l,null,e):l instanceof y?a+=e?JSON.stringify(o)+\":\"+JSON.stringify(l.getTime()):JSON.stringify(o)+\":\"+l.stringify():null!==l&&\"object\"==typeof l&&void 0!==l.castToText&&(a+=JSON.stringify(o)+\":\"+l.castToText(e))}return'{\"geometry\":'+(null===this.geometry()?\"null\":n(this.geometry()))+',\"attributes\":{'+a+\"}}\"}_fixDates(){if(null!==this._datefields)return this._datefields.length>0&&this._fixDateFields(this._datefields),void(this._datesfixed=!0);const e=[],t=this._layer.fields;for(let i=0;i<t.length;i++){const r=t[i],s=r.type;\"date\"!==s&&\"esriFieldTypeDate\"!==s||e.push(r.name)}this._datefields=e,e.length>0&&this._fixDateFields(e),this._datesfixed=!0}isUnknownDateTimeField(e){return\"unknown\"===this.dateTimeReferenceFieldIndex?.fieldTimeZone(e)}_fixDateFields(e){this.attributes={...this.attributes};const t=this.contextTimeReference?.timeZone??\"system\";for(let i=0;i<e.length;i++){let r=this.attributes[e[i]];if(null===r);else if(void 0===r){for(const s in this.attributes)if(s.toLowerCase()===e[i].toLowerCase()){if(r=this.attributes[s],null!==r){const e=this.isUnknownDateTimeField(s);a(r)?this.attributes[s]=r:r instanceof Date?this.attributes[s]=e?y.unknownDateJSToArcadeDate(r):y.dateJSAndZoneToArcadeDate(r,t):this.attributes[s]=e?y.unknownEpochToArcadeDate(r):y.epochToArcadeDate(r,t)}break}}else{const s=this.isUnknownDateTimeField(e[i]);a(r)?this.attributes[e[i]]=r:r instanceof Date?this.attributes[e[i]]=s?y.unknownDateJSToArcadeDate(r):y.dateJSAndZoneToArcadeDate(r,t):this.attributes[e[i]]=s?y.unknownEpochToArcadeDate(r):y.epochToArcadeDate(r,t)}}}geometry(){return null===this._geometry||this._geometry instanceof u||(this._optimizedGeomDefinition?(this._geometry=m(f(c(this._geometry,this._optimizedGeomDefinition.geometryType,this._optimizedGeomDefinition.hasZ,this._optimizedGeomDefinition.hasM))),this._geometry.spatialReference=this._optimizedGeomDefinition.spatialReference):this._geometry=m(f(this._geometry))),this._geometry}field(e){!1===this._datesfixed&&this._fixDates();const t=this.attributes[e];if(void 0!==t)return t;const i=e.toLowerCase();for(const r in this.attributes)if(r.toLowerCase()===i)return this.attributes[r];if(this._hasFieldDefinition(i))return null;throw new _(null,b.FieldNotFound,null,{key:e})}_hasFieldDefinition(e){if(null===this._layer)return!1;for(let t=0;t<this._layer.fields.length;t++){if(this._layer.fields[t].name.toLowerCase()===e)return!0}return!1}setField(e,t){if(this.immutable)throw new _(null,b.Immutable,null);if(t instanceof Date&&(t=this.isUnknownDateTimeField(e)?y.unknownDateJSToArcadeDate(t):y.dateJSToArcadeDate(t)),!1===o(t))throw new _(null,b.TypeNotAllowedInFeature,null);const i=e.toLowerCase();if(void 0===this.attributes[e]){for(const e in this.attributes)if(e.toLowerCase()===i)return void(this.attributes[e]=t);this.attributes[e]=t}else this.attributes[e]=t}hasField(e){const t=e.toLowerCase();if(void 0!==this.attributes[e])return!0;for(const i in this.attributes)if(i.toLowerCase()===t)return!0;return!!this._hasFieldDefinition(t)}keys(){let e=[];const t={};for(const i in this.attributes)e.push(i),t[i.toLowerCase()]=1;if(null!==this._layer)for(let i=0;i<this._layer.fields.length;i++){const r=this._layer.fields[i];1!==t[r.name.toLowerCase()]&&e.push(r.name)}return e=e.sort(),e}static parseGeometryFromDictionary(e){const t=g._convertDictionaryToJson(e,!0);return void 0!==t.hasm&&(t.hasM=t.hasm,delete t.hasm),void 0!==t.hasz&&(t.hasZ=t.hasz,delete t.hasz),void 0!==t.spatialreference&&(t.spatialReference=t.spatialreference,delete t.spatialreference),void 0!==t.rings&&(t.rings=this._fixPathArrays(t.rings,!0===t.hasZ,!0===t.hasZ)),void 0!==t.paths&&(t.paths=this._fixPathArrays(t.paths,!0===t.hasZ,!0===t.hasM)),void 0!==t.points&&(t.points=this._fixPointArrays(t.points,!0===t.hasZ,!0===t.hasM)),f(t)}static _fixPathArrays(e,i,r){const s=[];if(e instanceof Array)for(let t=0;t<e.length;t++)s.push(this._fixPointArrays(e[t],i,r));else if(e instanceof t)for(let t=0;t<e.length();t++)s.push(this._fixPointArrays(e.get(t),i,r));return s}static _fixPointArrays(e,i,r){const s=[];if(e instanceof Array)for(let n=0;n<e.length;n++){const a=e[n];a instanceof d?i&&r?s.push([a.x,a.y,a.z,a.m]):i?s.push([a.x,a.y,a.z]):r?s.push([a.x,a.y,a.m]):s.push([a.x,a.y]):a instanceof t?s.push(a.toArray()):s.push(a)}else if(e instanceof t)for(let n=0;n<e.length();n++){const a=e.get(n);a instanceof d?i&&r?s.push([a.x,a.y,a.z,a.m]):i?s.push([a.x,a.y,a.z]):r?s.push([a.x,a.y,a.m]):s.push([a.x,a.y]):a instanceof t?s.push(a.toArray()):s.push(a)}return s}static _convertDictionaryToJson(t,i=!1){const r={};for(const s in t.attributes){let n=t.attributes[s];n instanceof e&&(n=g._convertDictionaryToJson(n)),i?r[s.toLowerCase()]=n:r[s]=n}return r}static parseAttributesFromDictionary(e){const t={};for(const i in e.attributes){const r=e.attributes[i];if(!o(r))throw new _(null,b.InvalidParameter,null);t[i]=r}return t}static fromJson(e,t){let n=null;null!==e.geometry&&void 0!==e.geometry&&(n=f(e.geometry));const o={};if(null!==e.attributes&&void 0!==e.attributes)for(const l in e.attributes){const t=e.attributes[l];if(null===t)o[l]=t;else{if(!(s(t)||r(t)||i(t)||a(t)))throw new _(null,b.InvalidParameter,null);o[l]=t}}return g.createFromGraphicLikeObject(n,o,null,t??null)}fullSchema(){return this._layer}gdbVersion(){if(null===this._layer)return\"\";const e=this._layer.gdbVersion;return void 0===e?\"\":\"\"===e&&this._layer.capabilities&&this._layer.capabilities.isVersioned?\"SDE.DEFAULT\":e}castAsJson(e){const t={attributes:{},geometry:!0===e?.keepGeometryType?this.geometry():this.geometry()?.toJSON()??null};for(const i in this.attributes){const r=this.attributes[i];void 0!==r&&(t.attributes[i]=l(r,e))}return t}async castAsJsonAsync(e=null,t){return this.castAsJson(t)}}export{g as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI6mB,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,cAAa;AAAC,SAAK,sBAAoB,uBAAsB,KAAK,2BAAyB,MAAK,KAAK,YAAU,MAAK,KAAK,aAAW,MAAK,KAAK,SAAO,MAAK,KAAK,cAAY,MAAG,KAAK,8BAA4B,MAAK,KAAK,uBAAqB,MAAK,KAAK,YAAU,MAAG,KAAK,cAAY,MAAK,KAAK,YAAU;AAAA,EAAE;AAAA,EAAC,OAAO,kBAAkBA,IAAEC,IAAE;AAAC,UAAM,IAAE,IAAI;AAAE,WAAO,EAAE,uBAAqBA,MAAG,MAAK,EAAE,YAAU,EAAED,GAAE,QAAQ,IAAEA,GAAE,WAAS,MAAK,WAASA,GAAE,cAAY,SAAOA,GAAE,aAAW,EAAE,aAAW,CAAC,IAAE,EAAE,aAAWA,GAAE,YAAWA,GAAE,gBAAc,EAAE,SAAOA,GAAE,cAAa,EAAE,cAAY,SAAIA,GAAE,UAAQ,EAAE,SAAOA,GAAE,QAAO,EAAE,cAAY,SAAIA,GAAE,SAAO,YAAWA,GAAE,SAAO,EAAE,SAAOA,GAAE,OAAM,EAAE,cAAY,SAAIA,GAAE,eAAa,YAAWA,GAAE,gBAAc,EAAE,SAAOA,GAAE,aAAY,EAAE,cAAY,QAAI,EAAE,UAAQ,UAAK,EAAE,gBAAc,WAAS,EAAE,OAAO,8BAA4B,EAAE,8BAA4B,EAAE,OAAO,8BAA4B,EAAE,8BAA4BE,GAAE,gBAAgB,EAAE,MAAM,IAAG;AAAA,EAAC;AAAA,EAAC,OAAO,wBAAwBF,IAAE;AAAC,UAAMC,KAAE,IAAI;AAAE,WAAOA,GAAE,cAAYD,GAAE,aAAYC,GAAE,aAAWD,GAAE,YAAWC,GAAE,YAAUD,GAAE,WAAUC,GAAE,2BAAyBD,GAAE,0BAAyBA,GAAE,WAASC,GAAE,SAAOD,GAAE,SAAQC,GAAE,8BAA4BD,GAAE,6BAA4BC,GAAE,uBAAqBD,GAAE,sBAAqBC;AAAA,EAAC;AAAA,EAAC,OAAO,2BAA2BD,IAAEC,IAAE,GAAE;AAAC,UAAME,KAAE,IAAI;AAAE,WAAOA,GAAE,YAAUH,GAAE,WAAS,EAAC,UAASA,GAAE,SAAQ,IAAE,MAAKG,GAAE,2BAAyB,GAAEA,GAAE,aAAWH,GAAE,cAAY,CAAC,GAAEG,GAAE,SAAOF,IAAEE,GAAE,cAAY,OAAGA;AAAA,EAAC;AAAA,EAAC,OAAO,2BAA2BF,IAAE;AAAC,UAAM,IAAE,IAAI;AAAE,WAAO,EAAE,aAAWA,GAAE,MAAM,YAAY,GAAE,SAAO,EAAE,cAAY,EAAE,sBAAsB,KAAG,EAAE,aAAW,EAAE,WAAW,YAAW,SAAO,EAAE,eAAa,EAAE,aAAW,CAAC,MAAI,EAAE,aAAW,CAAC,GAAE,EAAE,YAAUA,GAAE,MAAM,UAAU,GAAE,SAAO,EAAE,cAAY,EAAE,qBAAqB,IAAE,EAAE,YAAU,GAAE,4BAA4B,EAAE,SAAS,IAAE,EAAE,qBAAqB,MAAI,EAAE,YAAU,QAAO;AAAA,EAAC;AAAA,EAAC,OAAO,4BAA4BD,IAAEC,IAAE,IAAE,MAAKE,IAAE;AAAC,UAAM,IAAE,IAAI;AAAE,WAAO,EAAE,uBAAqBA,MAAG,MAAK,SAAOF,OAAIA,KAAE,CAAC,IAAG,EAAE,aAAWA,IAAE,EAAE,YAAU,EAAED,EAAC,IAAEA,KAAE,MAAK,EAAE,SAAO,GAAE,EAAE,WAAS,EAAE,cAAY,OAAG,WAAS,EAAE,OAAO,8BAA4B,EAAE,8BAA4B,EAAE,OAAO,8BAA4B,EAAE,8BAA4BE,GAAE,gBAAgB,EAAE,MAAM,IAAG;AAAA,EAAC;AAAA,EAAC,+BAA+BF,IAAEC,IAAE,IAAE,MAAK;AAAC,aAAOA,OAAIA,KAAE,CAAC,IAAG,KAAK,aAAWA,IAAE,KAAK,YAAUD,MAAG,MAAK,KAAK,SAAO,GAAE,KAAK,SAAO,KAAK,cAAY,QAAG,KAAK,cAAY;AAAA,EAAE;AAAA,EAAC,IAAI,yBAAwB;AAJ7hG;AAI8hG,aAAO,UAAK,gCAAL,mBAAkC,2BAAwB;AAAA,EAAE;AAAA,EAAC,oBAAoBA,IAAE;AAJxnG;AAIynG,aAAO,UAAK,gCAAL,mBAAkC,cAAcA,QAAI;AAAA,EAAE;AAAA,EAAC,WAAWA,KAAE,OAAG;AAAC,QAAI,IAAE;AAAG,cAAK,KAAK,eAAa,KAAK,UAAU;AAAE,eAAU,KAAK,KAAK,YAAW;AAAC,aAAK,MAAI,KAAG;AAAK,YAAM,IAAE,KAAK,WAAW,CAAC;AAAE,cAAM,IAAE,KAAG,KAAK,UAAU,CAAC,IAAE,UAAQ,EAAE,CAAC,KAAG,EAAE,CAAC,KAAGI,GAAE,CAAC,IAAE,KAAG,KAAK,UAAU,CAAC,IAAE,MAAI,KAAK,UAAU,CAAC,IAAE,aAAa,IAAE,KAAG,KAAK,UAAU,CAAC,IAAE,MAAI,GAAE,CAAC,IAAE,aAAaH,MAAG,aAAa,QAAM,KAAG,KAAK,UAAU,CAAC,IAAE,MAAI,GAAE,GAAE,MAAKD,EAAC,IAAE,aAAa,IAAE,KAAGA,KAAE,KAAK,UAAU,CAAC,IAAE,MAAI,KAAK,UAAU,EAAE,QAAQ,CAAC,IAAE,KAAK,UAAU,CAAC,IAAE,MAAI,EAAE,UAAU,IAAE,SAAO,KAAG,YAAU,OAAO,KAAG,WAAS,EAAE,eAAa,KAAG,KAAK,UAAU,CAAC,IAAE,MAAI,EAAE,WAAWA,EAAC;AAAA,IAAE;AAAC,WAAM,kBAAgB,SAAO,KAAK,SAAS,IAAE,SAAO,GAAE,KAAK,SAAS,CAAC,KAAG,oBAAkB,IAAE;AAAA,EAAI;AAAA,EAAC,YAAW;AAAC,QAAG,SAAO,KAAK,YAAY,QAAO,KAAK,YAAY,SAAO,KAAG,KAAK,eAAe,KAAK,WAAW,GAAE,MAAK,KAAK,cAAY;AAAI,UAAMA,KAAE,CAAC,GAAEC,KAAE,KAAK,OAAO;AAAO,aAAQ,IAAE,GAAE,IAAEA,GAAE,QAAO,KAAI;AAAC,YAAME,KAAEF,GAAE,CAAC,GAAE,IAAEE,GAAE;AAAK,iBAAS,KAAG,wBAAsB,KAAGH,GAAE,KAAKG,GAAE,IAAI;AAAA,IAAC;AAAC,SAAK,cAAYH,IAAEA,GAAE,SAAO,KAAG,KAAK,eAAeA,EAAC,GAAE,KAAK,cAAY;AAAA,EAAE;AAAA,EAAC,uBAAuBA,IAAE;AAJxrI;AAIyrI,WAAM,gBAAY,UAAK,gCAAL,mBAAkC,cAAcA;AAAA,EAAE;AAAA,EAAC,eAAeA,IAAE;AAJ/wI;AAIgxI,SAAK,aAAW,EAAC,GAAG,KAAK,WAAU;AAAE,UAAMC,OAAE,UAAK,yBAAL,mBAA2B,aAAU;AAAS,aAAQ,IAAE,GAAE,IAAED,GAAE,QAAO,KAAI;AAAC,UAAIG,KAAE,KAAK,WAAWH,GAAE,CAAC,CAAC;AAAE,UAAG,SAAOG,GAAE;AAAA,eAAS,WAASA,IAAE;AAAC,mBAAU,KAAK,KAAK,WAAW,KAAG,EAAE,YAAY,MAAIH,GAAE,CAAC,EAAE,YAAY,GAAE;AAAC,cAAGG,KAAE,KAAK,WAAW,CAAC,GAAE,SAAOA,IAAE;AAAC,kBAAMH,KAAE,KAAK,uBAAuB,CAAC;AAAE,cAAEG,EAAC,IAAE,KAAK,WAAW,CAAC,IAAEA,KAAEA,cAAa,OAAK,KAAK,WAAW,CAAC,IAAEH,KAAE,EAAE,0BAA0BG,EAAC,IAAE,EAAE,0BAA0BA,IAAEF,EAAC,IAAE,KAAK,WAAW,CAAC,IAAED,KAAE,EAAE,yBAAyBG,EAAC,IAAE,EAAE,kBAAkBA,IAAEF,EAAC;AAAA,UAAC;AAAC;AAAA,QAAK;AAAA,MAAC,OAAK;AAAC,cAAM,IAAE,KAAK,uBAAuBD,GAAE,CAAC,CAAC;AAAE,UAAEG,EAAC,IAAE,KAAK,WAAWH,GAAE,CAAC,CAAC,IAAEG,KAAEA,cAAa,OAAK,KAAK,WAAWH,GAAE,CAAC,CAAC,IAAE,IAAE,EAAE,0BAA0BG,EAAC,IAAE,EAAE,0BAA0BA,IAAEF,EAAC,IAAE,KAAK,WAAWD,GAAE,CAAC,CAAC,IAAE,IAAE,EAAE,yBAAyBG,EAAC,IAAE,EAAE,kBAAkBA,IAAEF,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,SAAO,KAAK,aAAW,KAAK,qBAAqB,MAAI,KAAK,4BAA0B,KAAK,YAAU,EAAE,EAAE,GAAE,KAAK,WAAU,KAAK,yBAAyB,cAAa,KAAK,yBAAyB,MAAK,KAAK,yBAAyB,IAAI,CAAC,CAAC,GAAE,KAAK,UAAU,mBAAiB,KAAK,yBAAyB,oBAAkB,KAAK,YAAU,EAAE,EAAE,KAAK,SAAS,CAAC,IAAG,KAAK;AAAA,EAAS;AAAA,EAAC,MAAMD,IAAE;AAAC,cAAK,KAAK,eAAa,KAAK,UAAU;AAAE,UAAMC,KAAE,KAAK,WAAWD,EAAC;AAAE,QAAG,WAASC,GAAE,QAAOA;AAAE,UAAM,IAAED,GAAE,YAAY;AAAE,eAAUG,MAAK,KAAK,WAAW,KAAGA,GAAE,YAAY,MAAI,EAAE,QAAO,KAAK,WAAWA,EAAC;AAAE,QAAG,KAAK,oBAAoB,CAAC,EAAE,QAAO;AAAK,UAAM,IAAI,EAAE,MAAKH,GAAE,eAAc,MAAK,EAAC,KAAIA,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,QAAG,SAAO,KAAK,OAAO,QAAM;AAAG,aAAQC,KAAE,GAAEA,KAAE,KAAK,OAAO,OAAO,QAAOA,MAAI;AAAC,UAAG,KAAK,OAAO,OAAOA,EAAC,EAAE,KAAK,YAAY,MAAID,GAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,SAASA,IAAEC,IAAE;AAAC,QAAG,KAAK,UAAU,OAAM,IAAI,EAAE,MAAKD,GAAE,WAAU,IAAI;AAAE,QAAGC,cAAa,SAAOA,KAAE,KAAK,uBAAuBD,EAAC,IAAE,EAAE,0BAA0BC,EAAC,IAAE,EAAE,mBAAmBA,EAAC,IAAG,UAAK,EAAEA,EAAC,EAAE,OAAM,IAAI,EAAE,MAAKD,GAAE,yBAAwB,IAAI;AAAE,UAAM,IAAEA,GAAE,YAAY;AAAE,QAAG,WAAS,KAAK,WAAWA,EAAC,GAAE;AAAC,iBAAUA,MAAK,KAAK,WAAW,KAAGA,GAAE,YAAY,MAAI,EAAE,QAAO,MAAK,KAAK,WAAWA,EAAC,IAAEC;AAAG,WAAK,WAAWD,EAAC,IAAEC;AAAA,IAAC,MAAM,MAAK,WAAWD,EAAC,IAAEC;AAAA,EAAC;AAAA,EAAC,SAASD,IAAE;AAAC,UAAMC,KAAED,GAAE,YAAY;AAAE,QAAG,WAAS,KAAK,WAAWA,EAAC,EAAE,QAAM;AAAG,eAAU,KAAK,KAAK,WAAW,KAAG,EAAE,YAAY,MAAIC,GAAE,QAAM;AAAG,WAAM,CAAC,CAAC,KAAK,oBAAoBA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAID,KAAE,CAAC;AAAE,UAAMC,KAAE,CAAC;AAAE,eAAU,KAAK,KAAK,WAAW,CAAAD,GAAE,KAAK,CAAC,GAAEC,GAAE,EAAE,YAAY,CAAC,IAAE;AAAE,QAAG,SAAO,KAAK,OAAO,UAAQ,IAAE,GAAE,IAAE,KAAK,OAAO,OAAO,QAAO,KAAI;AAAC,YAAME,KAAE,KAAK,OAAO,OAAO,CAAC;AAAE,YAAIF,GAAEE,GAAE,KAAK,YAAY,CAAC,KAAGH,GAAE,KAAKG,GAAE,IAAI;AAAA,IAAC;AAAC,WAAOH,KAAEA,GAAE,KAAK,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAO,4BAA4BA,IAAE;AAAC,UAAMC,KAAE,GAAE,yBAAyBD,IAAE,IAAE;AAAE,WAAO,WAASC,GAAE,SAAOA,GAAE,OAAKA,GAAE,MAAK,OAAOA,GAAE,OAAM,WAASA,GAAE,SAAOA,GAAE,OAAKA,GAAE,MAAK,OAAOA,GAAE,OAAM,WAASA,GAAE,qBAAmBA,GAAE,mBAAiBA,GAAE,kBAAiB,OAAOA,GAAE,mBAAkB,WAASA,GAAE,UAAQA,GAAE,QAAM,KAAK,eAAeA,GAAE,OAAM,SAAKA,GAAE,MAAK,SAAKA,GAAE,IAAI,IAAG,WAASA,GAAE,UAAQA,GAAE,QAAM,KAAK,eAAeA,GAAE,OAAM,SAAKA,GAAE,MAAK,SAAKA,GAAE,IAAI,IAAG,WAASA,GAAE,WAASA,GAAE,SAAO,KAAK,gBAAgBA,GAAE,QAAO,SAAKA,GAAE,MAAK,SAAKA,GAAE,IAAI,IAAG,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,eAAeD,IAAE,GAAEG,IAAE;AAAC,UAAM,IAAE,CAAC;AAAE,QAAGH,cAAa,MAAM,UAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,GAAE,KAAK,KAAK,gBAAgBD,GAAEC,EAAC,GAAE,GAAEE,EAAC,CAAC;AAAA,aAAUH,cAAaC,GAAE,UAAQA,KAAE,GAAEA,KAAED,GAAE,OAAO,GAAEC,KAAI,GAAE,KAAK,KAAK,gBAAgBD,GAAE,IAAIC,EAAC,GAAE,GAAEE,EAAC,CAAC;AAAE,WAAO;AAAA,EAAC;AAAA,EAAC,OAAO,gBAAgBH,IAAE,GAAEG,IAAE;AAAC,UAAM,IAAE,CAAC;AAAE,QAAGH,cAAa,MAAM,UAAQ,IAAE,GAAE,IAAEA,GAAE,QAAO,KAAI;AAAC,YAAM,IAAEA,GAAE,CAAC;AAAE,mBAAa,IAAE,KAAGG,KAAE,EAAE,KAAK,CAAC,EAAE,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC,CAAC,IAAE,IAAE,EAAE,KAAK,CAAC,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC,CAAC,IAAEA,KAAE,EAAE,KAAK,CAAC,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,KAAK,CAAC,EAAE,GAAE,EAAE,CAAC,CAAC,IAAE,aAAaF,KAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,IAAE,EAAE,KAAK,CAAC;AAAA,IAAC;AAAA,aAASD,cAAaC,GAAE,UAAQ,IAAE,GAAE,IAAED,GAAE,OAAO,GAAE,KAAI;AAAC,YAAM,IAAEA,GAAE,IAAI,CAAC;AAAE,mBAAa,IAAE,KAAGG,KAAE,EAAE,KAAK,CAAC,EAAE,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC,CAAC,IAAE,IAAE,EAAE,KAAK,CAAC,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC,CAAC,IAAEA,KAAE,EAAE,KAAK,CAAC,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,KAAK,CAAC,EAAE,GAAE,EAAE,CAAC,CAAC,IAAE,aAAaF,KAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,IAAE,EAAE,KAAK,CAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,OAAO,yBAAyBA,IAAE,IAAE,OAAG;AAAC,UAAME,KAAE,CAAC;AAAE,eAAU,KAAKF,GAAE,YAAW;AAAC,UAAI,IAAEA,GAAE,WAAW,CAAC;AAAE,mBAAa,MAAI,IAAE,GAAE,yBAAyB,CAAC,IAAG,IAAEE,GAAE,EAAE,YAAY,CAAC,IAAE,IAAEA,GAAE,CAAC,IAAE;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,OAAO,8BAA8BH,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAU,KAAKD,GAAE,YAAW;AAAC,YAAMG,KAAEH,GAAE,WAAW,CAAC;AAAE,UAAG,CAAC,EAAEG,EAAC,EAAE,OAAM,IAAI,EAAE,MAAKH,GAAE,kBAAiB,IAAI;AAAE,MAAAC,GAAE,CAAC,IAAEE;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAA,EAAC,OAAO,SAASD,IAAEC,IAAE;AAAC,QAAI,IAAE;AAAK,aAAOD,GAAE,YAAU,WAASA,GAAE,aAAW,IAAE,EAAEA,GAAE,QAAQ;AAAG,UAAM,IAAE,CAAC;AAAE,QAAG,SAAOA,GAAE,cAAY,WAASA,GAAE,WAAW,YAAU,KAAKA,GAAE,YAAW;AAAC,YAAMC,KAAED,GAAE,WAAW,CAAC;AAAE,UAAG,SAAOC,GAAE,GAAE,CAAC,IAAEA;AAAA,WAAM;AAAC,YAAG,EAAEG,GAAEH,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,GAAG,OAAM,IAAI,EAAE,MAAKD,GAAE,kBAAiB,IAAI;AAAE,UAAE,CAAC,IAAEC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO,GAAE,4BAA4B,GAAE,GAAE,MAAKA,MAAG,IAAI;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,aAAY;AAAC,QAAG,SAAO,KAAK,OAAO,QAAM;AAAG,UAAMD,KAAE,KAAK,OAAO;AAAW,WAAO,WAASA,KAAE,KAAG,OAAKA,MAAG,KAAK,OAAO,gBAAc,KAAK,OAAO,aAAa,cAAY,gBAAcA;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAJ92R;AAI+2R,UAAMC,KAAE,EAAC,YAAW,CAAC,GAAE,UAAS,UAAKD,MAAA,gBAAAA,GAAG,oBAAiB,KAAK,SAAS,MAAE,UAAK,SAAS,MAAd,mBAAiB,aAAU,KAAI;AAAE,eAAU,KAAK,KAAK,YAAW;AAAC,YAAMG,KAAE,KAAK,WAAW,CAAC;AAAE,iBAASA,OAAIF,GAAE,WAAW,CAAC,IAAE,GAAEE,IAAEH,EAAC;AAAA,IAAE;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBD,KAAE,MAAKC,IAAE;AAAC,WAAO,KAAK,WAAWA,EAAC;AAAA,EAAC;AAAC;", "names": ["e", "t", "T", "r", "v"]}