{"version": 3, "sources": ["../../@arcgis/core/views/2d/tiling/TileCache.js", "../../@arcgis/core/views/2d/tiling/TileStrategy.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{k as e}from\"../../../chunks/vec2.js\";class t{constructor(e,t,i){this.maxSize=e,this._tileInfoView=t,this._removedFunc=i,this._tilePerId=new Map,this._tileKeysPerLevel=[]}has(e){return this._tilePerId.has(e)}get(e){return this._tilePerId.get(e)}pop(e){const t=this._tilePerId.get(e);if(!t)return;const s=t.key.level,r=this._tileKeysPerLevel[s];i(this._tilePerId,e);for(let i=0;i<r.length;i++)if(r[i].id===e){r.splice(i,1);break}return t.visible=!0,t}add(e){e.visible=!1;const t=e.key,i=t.id;if(this._tilePerId.has(i))return;this._tilePerId.set(i,e);const s=t.level;this._tileKeysPerLevel[s]||(this._tileKeysPerLevel[s]=[]),this._tileKeysPerLevel[s].push(t)}prune(e,t,i){let s=this._tilePerId.size;if(s<=this.maxSize)return;let r=this._tileKeysPerLevel.length-1;for(;s>this.maxSize&&r>=0;)r!==e&&(s=this._pruneAroundCenterTile(s,t,i,r)),r--;s>this.maxSize&&(s=this._pruneAroundCenterTile(s,t,i,e))}_pruneAroundCenterTile(t,i,s,r){const l=this._tileKeysPerLevel[r];if(!l||0===l.length)return t;const{size:n,origin:h}=this._tileInfoView.tileInfo,o=s*n[0],_=s*n[1],d=[0,0],u=[0,0];for(l.sort(((t,s)=>(d[0]=h.x+o*(t.col+.5),d[1]=h.y-_*(t.row+.5),u[0]=h.x+o*(s.col+.5),u[1]=h.y-_*(s.row+.5),e(d,i)-e(u,i))));l.length>0;){const e=l.pop();if(this._removeTile(e.id),--t===this.maxSize)break}return t}_removeTile(e){const t=this._tilePerId.get(e);this._removedFunc&&t&&this._removedFunc(t),i(this._tilePerId,e)}}function i(e,t){e.delete(t)}export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{create as e,contains as i}from\"../../../geometry/support/aaBoundingRect.js\";import t from\"./TileCache.js\";import s from\"./TileCoverage.js\";import l from\"./TileKey.js\";const a=new l(0,0,0,0),h=new Map,o=[],c=[];class r{constructor(e){this._previousScale=Number.POSITIVE_INFINITY,this.cachePolicy=\"keep\",this.coveragePolicy=\"closest\",this.resampling=!0,this.tileIndex=new Map,this.tiles=[],this.buffer=192,this.acquireTile=e.acquireTile,this.releaseTile=e.releaseTile,this.tileInfoView=e.tileInfoView,this.resampling=null==e.resampling||!!e.resampling,e.cachePolicy&&(this.cachePolicy=e.cachePolicy),e.coveragePolicy&&(this.coveragePolicy=e.coveragePolicy),null!=e.buffer&&(this.buffer=e.buffer),e.cacheSize&&(this._tileCache=new t(e.cacheSize,this.tileInfoView,(e=>{this.releaseTile(e)})))}destroy(){this.tileIndex.clear()}update(e){const{resampling:i,tileIndex:t}=this,l=this.tileInfoView.getTileCoverage(e.state,this.buffer,this.coveragePolicy);if(c.length=0,o.length=0,h.clear(),!l)return;const{minScale:r,maxScale:n}=this.tileInfoView.tileInfo,{spans:f,lodInfo:d}=l,{level:u}=d,{scale:p,center:g,resolution:y}=e.state,I=!e.stationary&&p>this._previousScale;if(this._previousScale=p,this.tiles.length=0,!i&&(p>r||p<n))return this.tiles.length=0,h.clear(),t.forEach((e=>{this.releaseTile(e)})),t.clear(),c.length=0,o.length=0,h.clear(),s.pool.release(l),!0;t.forEach((e=>e.visible=!0));let T=0,_=0;if(f.length>0)for(const{row:s,colFrom:o,colTo:c}of f)for(let e=o;e<=c;e++){T++;const i=a.set(u,s,d.normalizeCol(e),d.getWorldForColumn(e)).id;if(t.has(i)){const e=t.get(i);e.isReady?(h.set(i,e),_++):I||this._addParentTile(i,h)}else{let e;if(this._tileCache&&this._tileCache.has(i)){if(e=this._tileCache.pop(i),this.tileIndex.set(i,e),e.isReady){h.set(i,e),_++;continue}}else e=this.acquireTile(a),this.tileIndex.set(i,e);I||this._addParentTile(i,h)}}const m=_===T;t.forEach(((e,i)=>{if(a.set(i),h.has(i))return;const t=this.tileInfoView.intersects(l,a),s=\"purge\"===this.cachePolicy?a.level!==u:a.level>u;!t||!I&&m?!s&&t||o.push(i):e.isReady?s&&\"purge\"===this.cachePolicy&&this._hasReadyAncestor(a,u)?o.push(i):c.push(i):s&&o.push(i)}));for(const s of c){const e=t.get(s);e&&e.isReady&&h.set(s,e)}for(const s of o){const e=t.get(s);this._tileCache?this._tileCache.add(e):this.releaseTile(e),t.delete(s)}return h.forEach((e=>this.tiles.push(e))),t.forEach((e=>{h.has(e.key.id)||(e.visible=!1)})),this._tileCache&&this._tileCache.prune(u,g,y),s.pool.release(l),h.clear(),m}clear(e=!0){const{tileIndex:i}=this;e&&i.forEach((e=>{this.releaseTile(e)})),i.clear()}updateCacheSize(e){this._tileCache&&(this._tileCache.maxSize=e)}_addParentTile(e,i){let t=e,s=null;for(;t=this.tileInfoView.getTileParentId(t),t;)if(this.tileIndex.has(t)){if(s=this.tileIndex.get(t),s&&s.isReady){i.has(s.key.id)||i.set(s.key.id,s);break}}else if(this._tileCache&&this._tileCache.has(t)&&(s=this._tileCache.pop(t),this.tileIndex.set(t,s),s&&s.isReady)){i.has(s.key.id)||i.set(s.key.id,s);break}}_hasReadyAncestor(t,s){const l=e();this.tileInfoView.getTileBounds(l,t,!0);for(const a of this.tileIndex.values())if(a.isReady&&a.key.level>=s&&a.key.level<t.level){const t=e();if(this.tileInfoView.getTileBounds(t,a.key,!0),i(t,l))return!0}return!1}}export{r as default};\n"], "mappings": ";;;;;;;;;;;;;;;AAI4C,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQF,IAAE,KAAK,gBAAcC,IAAE,KAAK,eAAaC,IAAE,KAAK,aAAW,oBAAI,OAAI,KAAK,oBAAkB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAIF,IAAE;AAAC,WAAO,KAAK,WAAW,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,WAAW,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,UAAMC,KAAE,KAAK,WAAW,IAAID,EAAC;AAAE,QAAG,CAACC,GAAE;AAAO,UAAM,IAAEA,GAAE,IAAI,OAAME,KAAE,KAAK,kBAAkB,CAAC;AAAE,MAAE,KAAK,YAAWH,EAAC;AAAE,aAAQE,KAAE,GAAEA,KAAEC,GAAE,QAAOD,KAAI,KAAGC,GAAED,EAAC,EAAE,OAAKF,IAAE;AAAC,MAAAG,GAAE,OAAOD,IAAE,CAAC;AAAE;AAAA,IAAK;AAAC,WAAOD,GAAE,UAAQ,MAAGA;AAAA,EAAC;AAAA,EAAC,IAAID,IAAE;AAAC,IAAAA,GAAE,UAAQ;AAAG,UAAMC,KAAED,GAAE,KAAIE,KAAED,GAAE;AAAG,QAAG,KAAK,WAAW,IAAIC,EAAC,EAAE;AAAO,SAAK,WAAW,IAAIA,IAAEF,EAAC;AAAE,UAAM,IAAEC,GAAE;AAAM,SAAK,kBAAkB,CAAC,MAAI,KAAK,kBAAkB,CAAC,IAAE,CAAC,IAAG,KAAK,kBAAkB,CAAC,EAAE,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMD,IAAEC,IAAEC,IAAE;AAAC,QAAI,IAAE,KAAK,WAAW;AAAK,QAAG,KAAG,KAAK,QAAQ;AAAO,QAAIC,KAAE,KAAK,kBAAkB,SAAO;AAAE,WAAK,IAAE,KAAK,WAASA,MAAG,IAAG,CAAAA,OAAIH,OAAI,IAAE,KAAK,uBAAuB,GAAEC,IAAEC,IAAEC,EAAC,IAAGA;AAAI,QAAE,KAAK,YAAU,IAAE,KAAK,uBAAuB,GAAEF,IAAEC,IAAEF,EAAC;AAAA,EAAE;AAAA,EAAC,uBAAuBC,IAAEC,IAAE,GAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,kBAAkBD,EAAC;AAAE,QAAG,CAACC,MAAG,MAAIA,GAAE,OAAO,QAAOH;AAAE,UAAK,EAAC,MAAK,GAAE,QAAOI,GAAC,IAAE,KAAK,cAAc,UAASC,KAAE,IAAE,EAAE,CAAC,GAAE,IAAE,IAAE,EAAE,CAAC,GAAE,IAAE,CAAC,GAAE,CAAC,GAAEC,KAAE,CAAC,GAAE,CAAC;AAAE,SAAIH,GAAE,KAAM,CAACH,IAAEO,QAAK,EAAE,CAAC,IAAEH,GAAE,IAAEC,MAAGL,GAAE,MAAI,MAAI,EAAE,CAAC,IAAEI,GAAE,IAAE,KAAGJ,GAAE,MAAI,MAAIM,GAAE,CAAC,IAAEF,GAAE,IAAEC,MAAGE,GAAE,MAAI,MAAID,GAAE,CAAC,IAAEF,GAAE,IAAE,KAAGG,GAAE,MAAI,MAAI,EAAE,GAAEN,EAAC,IAAE,EAAEK,IAAEL,EAAC,EAAG,GAAEE,GAAE,SAAO,KAAG;AAAC,YAAMJ,KAAEI,GAAE,IAAI;AAAE,UAAG,KAAK,YAAYJ,GAAE,EAAE,GAAE,EAAEC,OAAI,KAAK,QAAQ;AAAA,IAAK;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMC,KAAE,KAAK,WAAW,IAAID,EAAC;AAAE,SAAK,gBAAcC,MAAG,KAAK,aAAaA,EAAC,GAAE,EAAE,KAAK,YAAWD,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,EAAAD,GAAE,OAAOC,EAAC;AAAC;;;ACA7uC,IAAM,IAAE,IAAI,EAAE,GAAE,GAAE,GAAE,CAAC;AAArB,IAAuB,IAAE,oBAAI;AAA7B,IAAiC,IAAE,CAAC;AAApC,IAAsC,IAAE,CAAC;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYQ,IAAE;AAAC,SAAK,iBAAe,OAAO,mBAAkB,KAAK,cAAY,QAAO,KAAK,iBAAe,WAAU,KAAK,aAAW,MAAG,KAAK,YAAU,oBAAI,OAAI,KAAK,QAAM,CAAC,GAAE,KAAK,SAAO,KAAI,KAAK,cAAYA,GAAE,aAAY,KAAK,cAAYA,GAAE,aAAY,KAAK,eAAaA,GAAE,cAAa,KAAK,aAAW,QAAMA,GAAE,cAAY,CAAC,CAACA,GAAE,YAAWA,GAAE,gBAAc,KAAK,cAAYA,GAAE,cAAaA,GAAE,mBAAiB,KAAK,iBAAeA,GAAE,iBAAgB,QAAMA,GAAE,WAAS,KAAK,SAAOA,GAAE,SAAQA,GAAE,cAAY,KAAK,aAAW,IAAI,EAAEA,GAAE,WAAU,KAAK,cAAc,CAAAA,OAAG;AAAC,WAAK,YAAYA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,UAAU,MAAM;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAK,EAAC,YAAWC,IAAE,WAAUC,GAAC,IAAE,MAAKC,KAAE,KAAK,aAAa,gBAAgBH,GAAE,OAAM,KAAK,QAAO,KAAK,cAAc;AAAE,QAAG,EAAE,SAAO,GAAE,EAAE,SAAO,GAAE,EAAE,MAAM,GAAE,CAACG,GAAE;AAAO,UAAK,EAAC,UAASC,IAAE,UAAS,EAAC,IAAE,KAAK,aAAa,UAAS,EAAC,OAAM,GAAE,SAAQ,EAAC,IAAED,IAAE,EAAC,OAAME,GAAC,IAAE,GAAE,EAAC,OAAM,GAAE,QAAO,GAAE,YAAW,EAAC,IAAEL,GAAE,OAAM,IAAE,CAACA,GAAE,cAAY,IAAE,KAAK;AAAe,QAAG,KAAK,iBAAe,GAAE,KAAK,MAAM,SAAO,GAAE,CAACC,OAAI,IAAEG,MAAG,IAAE,GAAG,QAAO,KAAK,MAAM,SAAO,GAAE,EAAE,MAAM,GAAEF,GAAE,QAAS,CAAAF,OAAG;AAAC,WAAK,YAAYA,EAAC;AAAA,IAAC,CAAE,GAAEE,GAAE,MAAM,GAAE,EAAE,SAAO,GAAE,EAAE,SAAO,GAAE,EAAE,MAAM,GAAE,EAAE,KAAK,QAAQC,EAAC,GAAE;AAAG,IAAAD,GAAE,QAAS,CAAAF,OAAGA,GAAE,UAAQ,IAAG;AAAE,QAAI,IAAE,GAAE,IAAE;AAAE,QAAG,EAAE,SAAO,EAAE,YAAS,EAAC,KAAI,GAAE,SAAQM,IAAE,OAAMC,GAAC,KAAI,EAAE,UAAQP,KAAEM,IAAEN,MAAGO,IAAEP,MAAI;AAAC;AAAI,YAAMC,KAAE,EAAE,IAAII,IAAE,GAAE,EAAE,aAAaL,EAAC,GAAE,EAAE,kBAAkBA,EAAC,CAAC,EAAE;AAAG,UAAGE,GAAE,IAAID,EAAC,GAAE;AAAC,cAAMD,KAAEE,GAAE,IAAID,EAAC;AAAE,QAAAD,GAAE,WAAS,EAAE,IAAIC,IAAED,EAAC,GAAE,OAAK,KAAG,KAAK,eAAeC,IAAE,CAAC;AAAA,MAAC,OAAK;AAAC,YAAID;AAAE,YAAG,KAAK,cAAY,KAAK,WAAW,IAAIC,EAAC,GAAE;AAAC,cAAGD,KAAE,KAAK,WAAW,IAAIC,EAAC,GAAE,KAAK,UAAU,IAAIA,IAAED,EAAC,GAAEA,GAAE,SAAQ;AAAC,cAAE,IAAIC,IAAED,EAAC,GAAE;AAAI;AAAA,UAAQ;AAAA,QAAC,MAAM,CAAAA,KAAE,KAAK,YAAY,CAAC,GAAE,KAAK,UAAU,IAAIC,IAAED,EAAC;AAAE,aAAG,KAAK,eAAeC,IAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,UAAM,IAAE,MAAI;AAAE,IAAAC,GAAE,QAAS,CAACF,IAAEC,OAAI;AAAC,UAAG,EAAE,IAAIA,EAAC,GAAE,EAAE,IAAIA,EAAC,EAAE;AAAO,YAAMC,KAAE,KAAK,aAAa,WAAWC,IAAE,CAAC,GAAE,IAAE,YAAU,KAAK,cAAY,EAAE,UAAQE,KAAE,EAAE,QAAMA;AAAE,OAACH,MAAG,CAAC,KAAG,IAAE,CAAC,KAAGA,MAAG,EAAE,KAAKD,EAAC,IAAED,GAAE,UAAQ,KAAG,YAAU,KAAK,eAAa,KAAK,kBAAkB,GAAEK,EAAC,IAAE,EAAE,KAAKJ,EAAC,IAAE,EAAE,KAAKA,EAAC,IAAE,KAAG,EAAE,KAAKA,EAAC;AAAA,IAAC,CAAE;AAAE,eAAU,KAAK,GAAE;AAAC,YAAMD,KAAEE,GAAE,IAAI,CAAC;AAAE,MAAAF,MAAGA,GAAE,WAAS,EAAE,IAAI,GAAEA,EAAC;AAAA,IAAC;AAAC,eAAU,KAAK,GAAE;AAAC,YAAMA,KAAEE,GAAE,IAAI,CAAC;AAAE,WAAK,aAAW,KAAK,WAAW,IAAIF,EAAC,IAAE,KAAK,YAAYA,EAAC,GAAEE,GAAE,OAAO,CAAC;AAAA,IAAC;AAAC,WAAO,EAAE,QAAS,CAAAF,OAAG,KAAK,MAAM,KAAKA,EAAC,CAAE,GAAEE,GAAE,QAAS,CAAAF,OAAG;AAAC,QAAE,IAAIA,GAAE,IAAI,EAAE,MAAIA,GAAE,UAAQ;AAAA,IAAG,CAAE,GAAE,KAAK,cAAY,KAAK,WAAW,MAAMK,IAAE,GAAE,CAAC,GAAE,EAAE,KAAK,QAAQF,EAAC,GAAE,EAAE,MAAM,GAAE;AAAA,EAAC;AAAA,EAAC,MAAMH,KAAE,MAAG;AAAC,UAAK,EAAC,WAAUC,GAAC,IAAE;AAAK,IAAAD,MAAGC,GAAE,QAAS,CAAAD,OAAG;AAAC,WAAK,YAAYA,EAAC;AAAA,IAAC,CAAE,GAAEC,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,gBAAgBD,IAAE;AAAC,SAAK,eAAa,KAAK,WAAW,UAAQA;AAAA,EAAE;AAAA,EAAC,eAAeA,IAAEC,IAAE;AAAC,QAAIC,KAAEF,IAAE,IAAE;AAAK,WAAKE,KAAE,KAAK,aAAa,gBAAgBA,EAAC,GAAEA,KAAG,KAAG,KAAK,UAAU,IAAIA,EAAC,GAAE;AAAC,UAAG,IAAE,KAAK,UAAU,IAAIA,EAAC,GAAE,KAAG,EAAE,SAAQ;AAAC,QAAAD,GAAE,IAAI,EAAE,IAAI,EAAE,KAAGA,GAAE,IAAI,EAAE,IAAI,IAAG,CAAC;AAAE;AAAA,MAAK;AAAA,IAAC,WAAS,KAAK,cAAY,KAAK,WAAW,IAAIC,EAAC,MAAI,IAAE,KAAK,WAAW,IAAIA,EAAC,GAAE,KAAK,UAAU,IAAIA,IAAE,CAAC,GAAE,KAAG,EAAE,UAAS;AAAC,MAAAD,GAAE,IAAI,EAAE,IAAI,EAAE,KAAGA,GAAE,IAAI,EAAE,IAAI,IAAG,CAAC;AAAE;AAAA,IAAK;AAAA,EAAC;AAAA,EAAC,kBAAkBC,IAAE,GAAE;AAAC,UAAMC,KAAE,EAAE;AAAE,SAAK,aAAa,cAAcA,IAAED,IAAE,IAAE;AAAE,eAAUM,MAAK,KAAK,UAAU,OAAO,EAAE,KAAGA,GAAE,WAASA,GAAE,IAAI,SAAO,KAAGA,GAAE,IAAI,QAAMN,GAAE,OAAM;AAAC,YAAMA,KAAE,EAAE;AAAE,UAAG,KAAK,aAAa,cAAcA,IAAEM,GAAE,KAAI,IAAE,GAAE,EAAEN,IAAEC,EAAC,EAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAC;", "names": ["e", "t", "i", "r", "l", "h", "o", "u", "s", "e", "i", "t", "l", "r", "u", "o", "c", "a"]}