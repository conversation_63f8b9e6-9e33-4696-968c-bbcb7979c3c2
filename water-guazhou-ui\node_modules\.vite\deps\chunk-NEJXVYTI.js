import {
  e
} from "./chunk-64RWCMSJ.js";
import {
  d
} from "./chunk-ADTC77YB.js";
import {
  i,
  p
} from "./chunk-57XIOVP5.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  l
} from "./chunk-T23PB46T.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  c
} from "./chunk-LTKA6OKA.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/renderers/support/DictionaryLoader.js
var h = "esri.renderers.support.DictionaryLoader";
var y = { type: "CIMSimpleLineCallout", lineSymbol: { type: "CIMLineSymbol", symbolLayers: [{ type: "CIMSolidStroke", width: 0.5, color: [0, 0, 0, 255] }] } };
var u = class {
  constructor(e2, t2, s3) {
    this.config = null, this.fieldMap = null, this.url = null, this._ongoingRequests = /* @__PURE__ */ new Map(), this._symbolCache = new e(100), this._dictionaryPromise = null, this.url = e2, this.config = t2, this.fieldMap = s3;
  }
  getSymbolFields() {
    return this._symbolFields;
  }
  async getSymbolAsync(t2, s3) {
    let o;
    this._dictionaryPromise || (this._dictionaryPromise = this.fetchResources(s3));
    try {
      o = await this._dictionaryPromise;
    } catch (g2) {
      if (j(g2)) return this._dictionaryPromise = null, null;
    }
    const i2 = {};
    if (this.fieldMap) for (const e2 of this._symbolFields) {
      const s4 = this.fieldMap[e2];
      if (s4 && null != t2.attributes[s4]) {
        const o2 = "" + t2.attributes[s4];
        i2[e2] = o2;
      } else i2[e2] = "";
    }
    const n = o == null ? void 0 : o(i2, s3);
    if (!n || "string" != typeof n) return null;
    const c2 = c(n).toString(), m = this._symbolCache.get(c2);
    if (m) return m.catch(() => {
      this._symbolCache.pop(c2);
    }), m;
    const f = n.split(";"), h2 = [], y2 = [];
    for (const r2 of f) if (r2) if (r2.includes("po:")) {
      const t3 = r2.substr(3).split("|");
      if (3 === t3.length) {
        const s4 = t3[0], o2 = t3[1];
        let i3 = t3[2];
        if ("DashTemplate" === o2) i3 = i3.split(" ").map((e2) => Number(e2));
        else if ("Color" === o2) {
          const t4 = new l(i3).toRgba();
          i3 = [t4[0], t4[1], t4[2], 255 * t4[3]];
        } else i3 = Number(i3);
        y2.push({ primitiveName: s4, propertyName: o2, value: i3 });
      }
    } else if (r2.includes("|")) {
      for (const e2 of r2.split("|")) if (this._itemNames.has(e2)) {
        h2.push(e2);
        break;
      }
    } else this._itemNames.has(r2) && h2.push(r2);
    const u2 = !r(t2.geometry) || !t2.geometry.hasZ && "point" === t2.geometry.type, p3 = this._cimPartsToCIMSymbol(h2, y2, u2, s3);
    return this._symbolCache.put(c2, p3, 1), p3;
  }
  async fetchResources(e2) {
    if (this._dictionaryPromise) return this._dictionaryPromise;
    if (!this.url) return void s.getLogger(h).error("no valid URL!");
    const i2 = U(this.url + "/resources/styles/dictionary-info.json", { responseType: "json", query: { f: "json" }, signal: r(e2) ? e2.signal : null }), [{ data: l2 }] = await Promise.all([i2, i()]);
    if (!l2) throw this._dictionaryPromise = null, new s2("esri.renderers.DictionaryRenderer", "Bad dictionary data!");
    const a = l2.expression, f = l2.authoringInfo;
    this._refSymbolUrlTemplate = this.url + "/" + l2.cimRefTemplateUrl, this._itemNames = new Set(l2.itemsNames), this._symbolFields = f.symbol;
    const y2 = {};
    if (this.config) {
      const e3 = this.config;
      for (const t2 in e3) y2[t2] = e3[t2];
    }
    if (f.configuration) for (const t2 of f.configuration) y2.hasOwnProperty(t2.name) || (y2[t2.name] = t2.value);
    const u2 = [];
    if (r(e2) && e2.fields && this.fieldMap) for (const t2 of this._symbolFields) {
      const s3 = this.fieldMap[t2], o = e2.fields.filter((e3) => e3.name === s3);
      o.length > 0 && u2.push({ ...o[0], name: t2 });
    }
    const p3 = p(a, r(e2) ? e2.spatialReference : null, u2, y2).then((e3) => {
      const t2 = { scale: 0 };
      return (s3, o) => {
        if (t(e3)) return null;
        const i3 = e3.repurposeFeature({ geometry: null, attributes: s3 });
        return t2.scale = r(o) ? o.scale ?? void 0 : void 0, e3.evaluate({ $feature: i3, $view: t2 });
      };
    }).catch((e3) => (s.getLogger(h).error("Creating dictinoary expression failed:", e3), null));
    return this._dictionaryPromise = p3, p3;
  }
  async _cimPartsToCIMSymbol(e2, t2, s3, o) {
    const i2 = new Array(e2.length);
    for (let l2 = 0; l2 < e2.length; l2++) i2[l2] = this._getSymbolPart(e2[l2], o);
    const r2 = await Promise.all(i2), n = this.fieldMap;
    if (n) for (const l2 of r2) p2(l2, n);
    return new d({ data: this._combineSymbolParts(r2, t2, s3) });
  }
  async _getSymbolPart(e2, s3) {
    if (this._ongoingRequests.has(e2)) return this._ongoingRequests.get(e2).then((e3) => e3.data);
    const o = this._refSymbolUrlTemplate.replace(/\{itemName\}/gi, e2), i2 = U(o, { responseType: "json", query: { f: "json" }, ...s3 });
    this._ongoingRequests.set(e2, i2);
    try {
      return (await i2).data;
    } catch (r2) {
      throw this._ongoingRequests.delete(e2), r2;
    }
  }
  _combineSymbolParts(e2, t2, s3) {
    if (!e2 || 0 === e2.length) return null;
    const o = { ...e2[0] };
    if (e2.length > 1) {
      o.symbolLayers = [];
      for (const t3 of e2) {
        const e3 = t3;
        o.symbolLayers.unshift(...e3.symbolLayers);
      }
    }
    return s3 && (o.callout = y), { type: "CIMSymbolReference", symbol: o, primitiveOverrides: t2 };
  }
};
function p2(e2, t2) {
  if (!e2) return;
  const s3 = e2.symbolLayers;
  if (!s3) return;
  let o = s3.length;
  for (; o--; ) {
    const e3 = s3[o];
    if (e3 && false !== e3.enable && "CIMVectorMarker" === e3.type) g(e3, t2);
  }
}
function g(e2, t2) {
  const s3 = e2.markerGraphics;
  if (s3) for (const o of s3) {
    if (!o) continue;
    const e3 = o.symbol;
    if (e3) switch (e3.type) {
      case "CIMPointSymbol":
      case "CIMLineSymbol":
      case "CIMPolygonSymbol":
        p2(e3, t2);
        break;
      case "CIMTextSymbol":
        e3.fieldMap = t2;
    }
  }
}

export {
  u
};
//# sourceMappingURL=chunk-NEJXVYTI.js.map
