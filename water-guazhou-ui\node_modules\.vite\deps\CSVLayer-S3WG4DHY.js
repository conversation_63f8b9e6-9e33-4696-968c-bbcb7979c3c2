import {
  l
} from "./chunk-D7BTYVTV.js";
import "./chunk-JLELSJK5.js";
import {
  He
} from "./chunk-33Z6JDMT.js";
import "./chunk-L7LRY3AT.js";
import "./chunk-ZJC3GHA7.js";
import "./chunk-TVTTDN54.js";
import "./chunk-O4T45CJC.js";
import "./chunk-NNKS4NNY.js";
import "./chunk-LNCHRZJI.js";
import "./chunk-OA2XSLRZ.js";
import "./chunk-CIHGHHEZ.js";
import "./chunk-G3QAWKCD.js";
import "./chunk-FZKLUDSB.js";
import {
  u
} from "./chunk-IKGI4J4I.js";
import "./chunk-MNWHGD3K.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-6GKVSPTV.js";
import "./chunk-4FIRBBKR.js";
import "./chunk-EKOSN3EW.js";
import "./chunk-RZCOX454.js";
import "./chunk-2WMCP27R.js";
import "./chunk-UVJUTW2U.js";
import "./chunk-RR74IWZB.js";
import "./chunk-KTB2COPC.js";
import "./chunk-FWXA4I6D.js";
import "./chunk-NQ3OACUM.js";
import "./chunk-HTXGAKOK.js";
import "./chunk-WL6G2MRC.js";
import "./chunk-OQK7L3JR.js";
import "./chunk-JZKMTUDN.js";
import "./chunk-UCWK623G.js";
import "./chunk-JV6TBH5W.js";
import "./chunk-77ZF73NA.js";
import "./chunk-55WN4LCX.js";
import "./chunk-VSFGOST3.js";
import "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-ZVU4V5QV.js";
import "./chunk-2AZSZWPE.js";
import "./chunk-PTIRBOGQ.js";
import {
  x as x2
} from "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import {
  x as x3
} from "./chunk-KE7SPCM7.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-MIA6BJ32.js";
import "./chunk-NGPCXWDX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FBVKALLT.js";
import "./chunk-6PEIQDFP.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-BS3GJQ77.js";
import "./chunk-IOBN373Z.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-NEJXVYTI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-VJW7RCN7.js";
import "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-ZL6CFFJK.js";
import "./chunk-TWFTBWXP.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import {
  f as f2,
  p
} from "./chunk-XVA5SA7P.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  w,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/graphics/sources/CSVSource.js
var u2 = class extends m {
  constructor(t) {
    super(t), this.type = "csv", this.refresh = x(async (t2) => {
      await this.load();
      const { extent: e2, timeExtent: o2 } = await this._connection.invoke("refresh", t2);
      return e2 && (this.sourceJSON.extent = e2), o2 && (this.sourceJSON.timeInfo.timeExtent = [o2.start, o2.end]), { dataChanged: true, updates: { extent: this.sourceJSON.extent, timeInfo: this.sourceJSON.timeInfo } };
    });
  }
  load(t) {
    const e2 = r(t) ? t.signal : null;
    return this.addResolvingPromise(this._startWorker(e2)), Promise.resolve(this);
  }
  destroy() {
    var _a;
    (_a = this._connection) == null ? void 0 : _a.close(), this._connection = null;
  }
  async openPorts() {
    return await this.load(), this._connection.openPorts();
  }
  async queryFeatures(t, e2 = {}) {
    await this.load(e2);
    const o2 = await this._connection.invoke("queryFeatures", t ? t.toJSON() : null, e2);
    return x3.fromJSON(o2);
  }
  async queryFeaturesJSON(t, e2 = {}) {
    return await this.load(e2), this._connection.invoke("queryFeatures", t ? t.toJSON() : null, e2);
  }
  async queryFeatureCount(t, e2 = {}) {
    return await this.load(e2), this._connection.invoke("queryFeatureCount", t ? t.toJSON() : null, e2);
  }
  async queryObjectIds(t, e2 = {}) {
    return await this.load(e2), this._connection.invoke("queryObjectIds", t ? t.toJSON() : null, e2);
  }
  async queryExtent(t, e2 = {}) {
    await this.load(e2);
    const o2 = await this._connection.invoke("queryExtent", t ? t.toJSON() : null, e2);
    return { count: o2.count, extent: w2.fromJSON(o2.extent) };
  }
  async querySnapping(t, e2 = {}) {
    return await this.load(e2), this._connection.invoke("querySnapping", t, e2);
  }
  async _startWorker(t) {
    this._connection = await u("CSVSourceWorker", { strategy: has("feature-layers-workers") ? "dedicated" : "local", signal: t });
    const { url: e2, delimiter: o2, fields: r2, latitudeField: i, longitudeField: n, spatialReference: a2, timeInfo: c } = this.loadOptions, u3 = await this._connection.invoke("load", { url: e2, customParameters: this.customParameters, parsingOptions: { delimiter: o2, fields: r2 == null ? void 0 : r2.map((t2) => t2.toJSON()), latitudeField: i, longitudeField: n, spatialReference: a2 == null ? void 0 : a2.toJSON(), timeInfo: c == null ? void 0 : c.toJSON() } }, { signal: t });
    this.locationInfo = u3.locationInfo, this.sourceJSON = u3.layerDefinition, this.delimiter = u3.delimiter;
  }
};
e([y()], u2.prototype, "type", void 0), e([y()], u2.prototype, "loadOptions", void 0), e([y()], u2.prototype, "customParameters", void 0), e([y()], u2.prototype, "locationInfo", void 0), e([y()], u2.prototype, "sourceJSON", void 0), e([y()], u2.prototype, "delimiter", void 0), u2 = e([a("esri.layers.graphics.sources.CSVSource")], u2);

// node_modules/@arcgis/core/layers/CSVLayer.js
function m2(e2, r2) {
  throw new s(r2, `CSVLayer (title: ${e2.title}, id: ${e2.id}) cannot be saved to a portal item`);
}
var f3 = class extends He {
  constructor(...e2) {
    super(...e2), this.geometryType = "point", this.capabilities = l(false, false), this.delimiter = null, this.editingEnabled = false, this.fields = null, this.latitudeField = null, this.locationType = "coordinates", this.longitudeField = null, this.operationalLayerType = "CSV", this.outFields = ["*"], this.path = null, this.spatialReference = f.WGS84, this.source = null, this.type = "csv";
  }
  normalizeCtorArgs(e2, t) {
    return "string" == typeof e2 ? { url: e2, ...t } : e2;
  }
  load(e2) {
    const t = r(e2) ? e2.signal : null, o2 = this.loadFromPortal({ supportedTypes: ["CSV"], supportsData: false }, e2).catch(w).then(async () => this.initLayerProperties(await this.createGraphicsSource(t)));
    return this.addResolvingPromise(o2), Promise.resolve(this);
  }
  get isTable() {
    return this.loaded && null == this.geometryType;
  }
  readWebMapLabelsVisible(e2, t) {
    return null != t.showLabels ? t.showLabels : !!(t.layerDefinition && t.layerDefinition.drawingInfo && t.layerDefinition.drawingInfo.labelingInfo);
  }
  set url(e2) {
    if (!e2) return void this._set("url", e2);
    const t = L(e2);
    this._set("url", t.path), t.query && (this.customParameters = { ...this.customParameters, ...t.query });
  }
  async createGraphicsSource(e2) {
    const t = new u2({ loadOptions: { delimiter: this.delimiter, fields: this.fields, latitudeField: this.latitudeField ?? void 0, longitudeField: this.longitudeField ?? void 0, spatialReference: this.spatialReference ?? void 0, timeInfo: this.timeInfo ?? void 0, url: this.url }, customParameters: this.customParameters ?? void 0 });
    return this._set("source", t), await t.load({ signal: e2 }), this.read({ locationInfo: t.locationInfo, columnDelimiter: t.delimiter }, { origin: "service", url: this.parsedUrl }), t;
  }
  queryFeatures(e2, t) {
    return this.load().then(() => this.source.queryFeatures(x2.from(e2) || this.createQuery())).then((e3) => {
      if (e3 == null ? void 0 : e3.features) for (const t2 of e3.features) t2.layer = t2.sourceLayer = this;
      return e3;
    });
  }
  queryObjectIds(e2, t) {
    return this.load().then(() => this.source.queryObjectIds(x2.from(e2) || this.createQuery()));
  }
  queryFeatureCount(e2, t) {
    return this.load().then(() => this.source.queryFeatureCount(x2.from(e2) || this.createQuery()));
  }
  queryExtent(e2, t) {
    return this.load().then(() => this.source.queryExtent(x2.from(e2) || this.createQuery()));
  }
  read(e2, t) {
    super.read(e2, t), t && "service" === t.origin && this.revert(["latitudeField", "longitudeField"], "service");
  }
  write(e2, t) {
    return super.write(e2, { ...t, writeLayerSchema: true });
  }
  clone() {
    throw new s("csv-layer:clone", `CSVLayer (title: ${this.title}, id: ${this.id}) cannot be cloned`);
  }
  async save(e2) {
    return m2(this, "csv-layer:save");
  }
  async saveAs(e2, t) {
    return m2(this, "csv-layer:save-as");
  }
  async hasDataChanged() {
    try {
      const { dataChanged: e2, updates: t } = await this.source.refresh(this.customParameters);
      return r(t) && this.read(t, { origin: "service", url: this.parsedUrl, ignoreDefaults: true }), e2;
    } catch {
    }
    return false;
  }
  _verifyFields() {
  }
  _verifySource() {
  }
  _hasMemorySource() {
    return false;
  }
};
e([y({ readOnly: true, json: { read: false, write: false } })], f3.prototype, "capabilities", void 0), e([y({ type: [",", " ", ";", "|", "	"], json: { read: { source: "columnDelimiter" }, write: { target: "columnDelimiter", ignoreOrigin: true } } })], f3.prototype, "delimiter", void 0), e([y({ readOnly: true, type: Boolean, json: { origins: { "web-scene": { read: false, write: false } } } })], f3.prototype, "editingEnabled", void 0), e([y({ json: { read: { source: "layerDefinition.fields" }, write: { target: "layerDefinition.fields" } } })], f3.prototype, "fields", void 0), e([y({ type: Boolean, readOnly: true })], f3.prototype, "isTable", null), e([o("web-map", "labelsVisible", ["layerDefinition.drawingInfo.labelingInfo", "showLabels"])], f3.prototype, "readWebMapLabelsVisible", null), e([y({ type: String, json: { read: { source: "locationInfo.latitudeFieldName" }, write: { target: "locationInfo.latitudeFieldName", ignoreOrigin: true } } })], f3.prototype, "latitudeField", void 0), e([y({ type: ["show", "hide"] })], f3.prototype, "listMode", void 0), e([y({ type: ["coordinates"], json: { read: { source: "locationInfo.locationType" }, write: { target: "locationInfo.locationType", ignoreOrigin: true, isRequired: true } } })], f3.prototype, "locationType", void 0), e([y({ type: String, json: { read: { source: "locationInfo.longitudeFieldName" }, write: { target: "locationInfo.longitudeFieldName", ignoreOrigin: true } } })], f3.prototype, "longitudeField", void 0), e([y({ type: ["CSV"] })], f3.prototype, "operationalLayerType", void 0), e([y()], f3.prototype, "outFields", void 0), e([y({ type: String, json: { origins: { "web-scene": { read: false, write: false } }, read: false, write: false } })], f3.prototype, "path", void 0), e([y({ json: { read: false }, cast: null, type: u2, readOnly: true })], f3.prototype, "source", void 0), e([y({ json: { read: false }, value: "csv", readOnly: true })], f3.prototype, "type", void 0), e([y({ json: { read: p, write: { isRequired: true, ignoreOrigin: true, writer: f2 } } })], f3.prototype, "url", null), f3 = e([a("esri.layers.CSVLayer")], f3);
var g = f3;
export {
  g as default
};
//# sourceMappingURL=CSVLayer-S3WG4DHY.js.map
