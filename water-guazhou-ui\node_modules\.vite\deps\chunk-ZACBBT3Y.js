import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  u
} from "./chunk-3WCHZJQK.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  p,
  w
} from "./chunk-63M4K32A.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/jsonUtils.js
function u2(o) {
  return void 0 !== o.xmin && void 0 !== o.ymin && void 0 !== o.xmax && void 0 !== o.ymax;
}
function l(o) {
  return void 0 !== o.points;
}
function s(o) {
  return void 0 !== o.x && void 0 !== o.y;
}
function f(o) {
  return void 0 !== o.paths;
}
function y(o) {
  return void 0 !== o.rings;
}
function v2(p2) {
  return t(p2) ? null : p2 instanceof p ? p2 : s(p2) ? w.fromJSON(p2) : f(p2) ? m.fromJSON(p2) : y(p2) ? v.fromJSON(p2) : l(p2) ? u.fromJSON(p2) : u2(p2) ? w2.fromJSON(p2) : null;
}
function c(o) {
  return o ? s(o) ? "esriGeometryPoint" : f(o) ? "esriGeometryPolyline" : y(o) ? "esriGeometryPolygon" : u2(o) ? "esriGeometryEnvelope" : l(o) ? "esriGeometryMultipoint" : null : null;
}
var G = { esriGeometryPoint: w, esriGeometryPolyline: m, esriGeometryPolygon: v, esriGeometryEnvelope: w2, esriGeometryMultipoint: u };
function d(o) {
  return o && G[o] || null;
}

export {
  u2 as u,
  l,
  s,
  f,
  y,
  v2 as v,
  c,
  d
};
//# sourceMappingURL=chunk-ZACBBT3Y.js.map
