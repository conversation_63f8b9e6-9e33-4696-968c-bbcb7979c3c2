import {
  i
} from "./chunk-57XIOVP5.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  o,
  t as t2
} from "./chunk-REW33H3I.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/domainUtils.js
var e;
function n(n2, a2) {
  switch (n2.type) {
    case "range": {
      const r2 = "range" in n2 ? n2.range[0] : n2.minValue, u2 = "range" in n2 ? n2.range[1] : n2.maxValue;
      if (null != r2 && +a2 < r2 || null != u2 && +a2 > u2) return e.VALUE_OUT_OF_RANGE;
      break;
    }
    case "coded-value":
    case "codedValue":
      if (null == n2.codedValues || n2.codedValues.every((e2) => null == e2 || e2.code !== a2)) return e.INVALID_CODED_VALUE;
  }
  return null;
}
!function(e2) {
  e2.VALUE_OUT_OF_RANGE = "domain-validation-error::value-out-of-range", e2.INVALID_CODED_VALUE = "domain-validation-error::invalid-coded-value";
}(e || (e = {}));

// node_modules/@arcgis/core/layers/support/fieldUtils.js
var f = /^([0-9])/;
var u = /[^a-z0-9_\u0080-\uffff]/gi;
var c = /_{2,}/g;
var d = /^_/;
var m = /_$/;
function p(e2) {
  if (null == e2) return null;
  const n2 = e2.trim().replace(u, "_").replace(c, "_").replace(d, "").replace(m, "").replace(f, "F$1");
  return n2 || null;
}
var y = ["field", "field2", "field3", "normalizationField", "rotationInfo.field", "proportionalSymbolInfo.field", "proportionalSymbolInfo.normalizationField", "colorInfo.field", "colorInfo.normalizationField"];
var g = ["field", "normalizationField"];
function F(e2, n2) {
  if (null != e2 && null != n2) {
    for (const i2 of Array.isArray(e2) ? e2 : [e2]) if (I(y, i2, n2), "visualVariables" in i2 && i2.visualVariables) for (const e3 of i2.visualVariables) I(g, e3, n2);
  }
}
function I(e2, n2, i2) {
  if (e2) for (const l of e2) {
    const e3 = t2(l, n2), o2 = e3 && "function" != typeof e3 && i2.get(e3);
    o2 && o(l, o2.name, n2);
  }
}
function x(e2, n2) {
  var _a;
  if (null != e2 && ((_a = n2 == null ? void 0 : n2.fields) == null ? void 0 : _a.length)) if ("startField" in e2) {
    const i2 = n2.get(e2.startField), t3 = n2.get(e2.endField);
    e2.startField = (i2 == null ? void 0 : i2.name) ?? null, e2.endField = (t3 == null ? void 0 : t3.name) ?? null;
  } else {
    const i2 = n2.get(e2.startTimeField), t3 = n2.get(e2.endTimeField);
    e2.startTimeField = (i2 == null ? void 0 : i2.name) ?? null, e2.endTimeField = (t3 == null ? void 0 : t3.name) ?? null;
  }
}
var b = /* @__PURE__ */ new Set();
function T(e2, n2) {
  return e2 && n2 ? (b.clear(), h(b, e2, n2), Array.from(b).sort()) : [];
}
function h(e2, n2, i2) {
  var _a;
  if (i2) if ((_a = n2 == null ? void 0 : n2.fields) == null ? void 0 : _a.length) if (i2.includes("*")) for (const { name: t3 } of n2.fields) e2.add(t3);
  else for (const t3 of i2) w(e2, n2, t3);
  else {
    if (i2.includes("*")) return e2.clear(), void e2.add("*");
    for (const n3 of i2) null != n3 && e2.add(n3);
  }
}
function w(e2, n2, i2) {
  if ("string" == typeof i2) if (n2) {
    const t3 = n2.get(i2);
    t3 && e2.add(t3.name);
  } else e2.add(i2);
}
function v(e2, i2) {
  return t(i2) || t(e2) ? [] : i2.includes("*") ? (e2.fields ?? []).map((e3) => e3.name) : i2;
}
async function S(e2, n2, i2) {
  var _a;
  if (!i2) return;
  const { arcadeUtils: t3 } = await i(), r2 = t3.extractFieldNames(i2, (_a = n2 == null ? void 0 : n2.fields) == null ? void 0 : _a.map((e3) => e3.name));
  for (const l of r2) w(e2, n2, l);
}
async function _(n2, i2, t3) {
  if (t3 && "1=1" !== t3) {
    const r2 = (await import("./WhereClause-3PEEQH24.js")).WhereClause.create(t3, i2);
    if (!r2.isStandardized) throw new s("fieldUtils:collectFilterFields", "Where clause is not standardized", { where: t3 });
    h(n2, i2, r2.fieldNames);
  }
}
function E({ displayField: e2, fields: n2 }) {
  return e2 || (n2 && n2.length ? $(n2, "name-or-title") || $(n2, "unique-identifier") || $(n2, "type-or-category") || N(n2) : null);
}
function N(e2) {
  for (const n2 of e2) {
    if (!n2 || !n2.name) continue;
    const e3 = n2.name.toLowerCase();
    if (e3.includes("name") || e3.includes("title")) return n2.name;
  }
  return null;
}
function $(e2, n2) {
  for (const i2 of e2) if (i2 && i2.valueType && i2.valueType === n2) return i2.name;
  return null;
}
async function D(e2, n2) {
  var _a;
  if (!n2) return;
  const i2 = (_a = n2.elevationInfo) == null ? void 0 : _a.featureExpressionInfo;
  return i2 ? i2.collectRequiredFields(e2, n2.fieldsIndex) : void 0;
}
function L(e2, n2, i2) {
  i2.onStatisticExpression ? S(e2, n2, i2.onStatisticExpression.expression) : e2.add(i2.onStatisticField);
}
async function O(e2, n2, i2) {
  if (!n2 || !i2 || !("fields" in i2)) return;
  const t3 = [], r2 = i2.popupTemplate;
  t3.push(U(e2, n2, r2)), i2.fields && t3.push(...i2.fields.map(async (i3) => L(e2, n2.fieldsIndex, i3))), await Promise.all(t3);
}
async function U(e2, n2, i2) {
  const t3 = [];
  (i2 == null ? void 0 : i2.expressionInfos) && t3.push(...i2.expressionInfos.map((i3) => S(e2, n2.fieldsIndex, i3.expression)));
  const r2 = i2 == null ? void 0 : i2.content;
  if (Array.isArray(r2)) for (const l of r2) "expression" === l.type && l.expressionInfo && t3.push(S(e2, n2.fieldsIndex, l.expressionInfo.expression));
  await Promise.all(t3);
}
async function j(e2, n2, t3) {
  n2 && (n2.timeInfo && r(t3) && t3.timeExtent && h(e2, n2.fieldsIndex, [n2.timeInfo.startField, n2.timeInfo.endField]), n2.floorInfo && h(e2, n2.fieldsIndex, [n2.floorInfo.floorField]), r(t3) && r(t3.where) && await _(e2, n2.fieldsIndex, t3.where));
}
async function z(e2, n2, i2) {
  n2 && i2 && await Promise.all(i2.map((i3) => P(e2, n2, i3)));
}
async function P(e2, n2, i2) {
  n2 && i2 && (i2.valueExpression ? await S(e2, n2.fieldsIndex, i2.valueExpression) : i2.field && w(e2, n2.fieldsIndex, i2.field));
}
function C(e2) {
  if (!e2) return [];
  const n2 = "editFieldsInfo" in e2 && e2.editFieldsInfo;
  return n2 ? T(e2.fieldsIndex, [n2 && n2.creatorField, n2 && n2.creationDateField, n2 && n2.editorField, n2 && n2.editDateField]) : [];
}
async function W(e2, n2) {
  const { labelingInfo: i2, fieldsIndex: t3 } = n2;
  i2 && i2.length && await Promise.all(i2.map((n3) => q(e2, t3, n3)));
}
async function q(e2, n2, i2) {
  if (!i2) return;
  const t3 = i2.getLabelExpression(), r2 = i2.where;
  if ("arcade" === t3.type) await S(e2, n2, t3.expression);
  else {
    const i3 = t3.expression.match(/{[^}]*}/g);
    i3 && i3.forEach((i4) => {
      w(e2, n2, i4.slice(1, -1));
    });
  }
  await _(e2, n2, r2);
}
function M(e2) {
  const n2 = e2.defaultValue;
  return void 0 !== n2 && Z(e2, n2) ? n2 : e2.nullable ? null : void 0;
}
function Y(e2) {
  return "number" == typeof e2 && !isNaN(e2) && isFinite(e2);
}
function J(e2) {
  return null === e2 || Y(e2);
}
var X = "isInteger" in Number ? Number.isInteger : (e2) => "number" == typeof e2 && isFinite(e2) && Math.floor(e2) === e2;
function B(e2) {
  return null === e2 || X(e2);
}
function H(e2) {
  return null != e2 && "string" == typeof e2;
}
function K(e2) {
  return null === e2 || H(e2);
}
function Q() {
  return true;
}
function Z(e2, n2) {
  let i2;
  switch (e2.type) {
    case "date":
    case "integer":
    case "long":
    case "small-integer":
    case "esriFieldTypeDate":
    case "esriFieldTypeInteger":
    case "esriFieldTypeLong":
    case "esriFieldTypeSmallInteger":
      i2 = e2.nullable ? B : X;
      break;
    case "double":
    case "single":
    case "esriFieldTypeSingle":
    case "esriFieldTypeDouble":
      i2 = e2.nullable ? J : Y;
      break;
    case "string":
    case "esriFieldTypeString":
      i2 = e2.nullable ? K : H;
      break;
    default:
      i2 = Q;
  }
  return 1 === arguments.length ? i2 : i2(n2);
}
var ee = ["integer", "small-integer", "single", "double"];
var ne = /* @__PURE__ */ new Set([...ee, "esriFieldTypeInteger", "esriFieldTypeSmallInteger", "esriFieldTypeSingle", "esriFieldTypeDouble"]);
function ie(e2) {
  return null != e2 && ne.has(e2.type);
}
function te(e2) {
  return null != e2 && ("string" === e2.type || "esriFieldTypeString" === e2.type);
}
function re(e2) {
  return null != e2 && ("date" === e2.type || "esriFieldTypeDate" === e2.type);
}
var oe;
var se;
function ae(e2) {
  return null == e2 || "number" == typeof e2 && isNaN(e2) ? null : e2;
}
function fe(e2, n2) {
  return null == e2 || e2.nullable && null === n2 ? null : ie(e2) && !ue(e2.type, Number(n2)) ? oe.OUT_OF_RANGE : Z(e2, n2) ? e2.domain ? n(e2.domain, n2) : null : se.INVALID_TYPE;
}
function ue(e2, n2) {
  const i2 = "string" == typeof e2 ? de(e2) : e2;
  if (!i2) return false;
  const t3 = i2.min, r2 = i2.max;
  return i2.isInteger ? X(n2) && n2 >= t3 && n2 <= r2 : n2 >= t3 && n2 <= r2;
}
function de(e2) {
  switch (e2) {
    case "esriFieldTypeSmallInteger":
    case "small-integer":
      return pe;
    case "esriFieldTypeInteger":
    case "integer":
      return ye;
    case "esriFieldTypeSingle":
    case "single":
      return ge;
    case "esriFieldTypeDouble":
    case "double":
      return Fe;
  }
}
!function(e2) {
  e2.OUT_OF_RANGE = "numeric-range-validation-error::out-of-range";
}(oe || (oe = {})), function(e2) {
  e2.INVALID_TYPE = "type-validation-error::invalid-type";
}(se || (se = {}));
var pe = { min: -32768, max: 32767, isInteger: true };
var ye = { min: -2147483648, max: 2147483647, isInteger: true };
var ge = { min: -34e37, max: 12e37, isInteger: false };
var Fe = { min: -Number.MAX_VALUE, max: Number.MAX_VALUE, isInteger: false };
function Ie(e2, n2, i2) {
  switch (e2) {
    case e.INVALID_CODED_VALUE:
      return `Value ${i2} is not in the coded domain - field: ${n2.name}, domain: ${JSON.stringify(n2.domain)}`;
    case e.VALUE_OUT_OF_RANGE:
      return `Value ${i2} is out of the range of valid values - field: ${n2.name}, domain: ${JSON.stringify(n2.domain)}`;
    case se.INVALID_TYPE:
      return `Value ${i2} is not a valid value for the field type - field: ${n2.name}, type: ${n2.type}, nullable: ${n2.nullable}`;
    case oe.OUT_OF_RANGE: {
      const { min: e3, max: t3 } = de(n2.type);
      return `Value ${i2} is out of range for the number type - field: ${n2.name}, type: ${n2.type}, value range is ${e3} to ${t3}`;
    }
  }
}
function xe(e2, n2) {
  return !be(e2, n2, null);
}
function be(e2, n2, t3) {
  if (!n2 || !n2.attributes || !e2) {
    if (r(t3)) for (const n3 of e2 ?? []) t3.add(n3);
    return true;
  }
  const r2 = n2.attributes;
  let l = false;
  for (const o2 of e2) if (!(o2 in r2)) {
    if (l = true, !r(t3)) break;
    t3.add(o2);
  }
  return l;
}
function he(e2) {
  return !!e2 && ["raster.itempixelvalue", "raster.servicepixelvalue"].some((n2) => e2.toLowerCase().startsWith(n2));
}

export {
  p,
  F,
  x,
  T,
  h,
  w,
  v,
  S,
  E,
  D,
  O,
  j,
  z,
  C,
  W,
  M,
  ie,
  te,
  re,
  ae,
  fe,
  Ie,
  xe,
  he
};
//# sourceMappingURL=chunk-VNYCO3JG.js.map
