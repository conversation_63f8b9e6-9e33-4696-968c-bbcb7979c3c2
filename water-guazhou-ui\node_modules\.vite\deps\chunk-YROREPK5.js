import {
  l,
  n as n2,
  r as r3
} from "./chunk-RY6ZYWKC.js";
import {
  c as c2,
  e as e3,
  f,
  i,
  n,
  s as s3,
  u
} from "./chunk-223SE4BY.js";
import {
  W
} from "./chunk-AOYBG2OC.js";
import {
  b as b2,
  t as t2
} from "./chunk-EVADT7ME.js";
import {
  N,
  O,
  P,
  S,
  T,
  c
} from "./chunk-RRNRSHX3.js";
import {
  G
} from "./chunk-4M3AMTD4.js";
import {
  Tt,
  at,
  nt,
  st,
  ut,
  wt
} from "./chunk-B4KDIR4O.js";
import {
  e as e2
} from "./chunk-SEO6KEGF.js";
import {
  r as r2
} from "./chunk-UCWK623G.js";
import {
  m
} from "./chunk-GE5PSQPZ.js";
import {
  a
} from "./chunk-EIGTETCG.js";
import {
  D,
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  E,
  b,
  e,
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/layers/features/support/FeatureSetReaderJSON.js
function d({ coords: t3, lengths: e4 }) {
  let r6 = 0;
  for (const s4 of e4) {
    for (let e5 = 1; e5 < s4; e5++) t3[2 * (r6 + e5)] += t3[2 * (r6 + e5) - 2], t3[2 * (r6 + e5) + 1] += t3[2 * (r6 + e5) - 1];
    r6 += s4;
  }
}
var c3 = class _c extends b2 {
  static fromFeatures(t3, e4) {
    const { objectIdField: s4, geometryType: i2 } = e4, n4 = nt([], t3, i2, false, false, s4);
    for (let r6 = 0; r6 < n4.length; r6++) n4[r6].displayId = t3[r6].displayId;
    return _c.fromOptimizedFeatures(n4, e4);
  }
  static fromFeatureSet(t3, e4) {
    const r6 = at(t3, e4.objectIdField);
    return _c.fromOptimizedFeatureSet(r6, e4);
  }
  static fromOptimizedFeatureSet(t3, e4) {
    const { features: r6 } = t3, s4 = _c.fromOptimizedFeatures(r6, e4);
    s4._exceededTransferLimit = t3.exceededTransferLimit, s4._transform = t3.transform;
    for (const i2 of t3.fields) "esriFieldTypeDate" === i2.type && s4._dateFields.add(i2.name);
    return s4;
  }
  static fromOptimizedFeatures(t3, e4, r6) {
    const s4 = b2.createInstance(), i2 = new _c(s4, t3, e4);
    return i2._transform = r6, i2;
  }
  constructor(t3, e4, r6) {
    super(t3, r6), this._exceededTransferLimit = false, this._featureIndex = -1, this._dateFields = /* @__PURE__ */ new Set(), this._geometryType = r6 == null ? void 0 : r6.geometryType, this._features = e4;
  }
  get _current() {
    return this._features[this._featureIndex];
  }
  get geometryType() {
    return this._geometryType;
  }
  get hasFeatures() {
    return !!this._features.length;
  }
  get hasNext() {
    return this._featureIndex + 1 < this._features.length;
  }
  get exceededTransferLimit() {
    return this._exceededTransferLimit;
  }
  get hasZ() {
    return false;
  }
  get hasM() {
    return false;
  }
  removeIds(t3) {
    const e4 = new Set(t3);
    this._features = this._features.filter((t4) => !(t4.objectId && e4.has(t4.objectId)));
  }
  append(t3) {
    for (const e4 of t3) this._features.push(e4);
  }
  getSize() {
    return this._features.length;
  }
  getCursor() {
    return this.copy();
  }
  getQuantizationTransform() {
    return this._transform;
  }
  getAttributeHash() {
    let t3 = "";
    for (const e4 in this._current.attributes) t3 += this._current.attributes[e4];
    return t3;
  }
  getIndex() {
    return this._featureIndex;
  }
  setIndex(t3) {
    this._featureIndex = t3;
  }
  getObjectId() {
    var _a;
    return (_a = this._current) == null ? void 0 : _a.objectId;
  }
  getDisplayId() {
    return this._current.displayId;
  }
  setDisplayId(t3) {
    this._current.displayId = t3;
  }
  getGroupId() {
    return this._current.groupId;
  }
  setGroupId(t3) {
    this._current.groupId = t3;
  }
  copy() {
    const t3 = new _c(this.instance, this._features, this.fullSchema());
    return this.copyInto(t3), t3;
  }
  next() {
    for (; ++this._featureIndex < this._features.length && !this._getExists(); ) ;
    return this._featureIndex < this._features.length;
  }
  readLegacyFeature() {
    return st(this._current, this.geometryType, this.hasZ, this.hasM);
  }
  readOptimizedFeature() {
    return this._current;
  }
  readLegacyPointGeometry() {
    return this.readGeometry() ? { x: this.getX(), y: this.getY() } : null;
  }
  readLegacyGeometry() {
    const t3 = this.readGeometry();
    return ut(t3, this.geometryType, this.hasZ, this.hasM);
  }
  readLegacyCentroid() {
    const e4 = this.readCentroid();
    return t(e4) ? null : { x: e4.coords[0] * this._sx + this._tx, y: e4.coords[1] * this._sy + this._ty };
  }
  readGeometryArea() {
    return e2(this._current) ? Tt(this._current.geometry, 2) : 0;
  }
  readUnquantizedGeometry() {
    const t3 = this.readGeometry();
    if ("esriGeometryPoint" === this.geometryType || !t3) return t3;
    const e4 = t3.clone();
    return d(e4), e4;
  }
  readHydratedGeometry() {
    const r6 = this._current.geometry;
    if (t(r6)) return null;
    const s4 = r6.clone();
    return r(this._transform) && wt(s4, s4, this.hasZ, this.hasM, this._transform), s4;
  }
  getXHydrated() {
    if (!e2(this._current)) return 0;
    const e4 = this._current.geometry.coords[0], r6 = this.getQuantizationTransform();
    return t(r6) ? e4 : e4 * r6.scale[0] + r6.translate[0];
  }
  getYHydrated() {
    if (!e2(this._current)) return 0;
    const e4 = this._current.geometry.coords[1], r6 = this.getQuantizationTransform();
    return t(r6) ? e4 : r6.translate[1] - e4 * r6.scale[1];
  }
  getX() {
    return e2(this._current) ? this._current.geometry.coords[0] * this._sx + this._tx : 0;
  }
  getY() {
    return e2(this._current) ? this._current.geometry.coords[1] * this._sy + this._ty : 0;
  }
  readGeometry() {
    if (!e2(this._current)) {
      if (r(this._current.centroid)) {
        const [t4, e4] = this._current.centroid.coords;
        return this.createQuantizedExtrudedQuad(t4, e4);
      }
      return null;
    }
    const t3 = this._current.geometry.clone();
    if (t3.isPoint) return t3.coords[0] = t3.coords[0] * this._sx + this._tx, t3.coords[1] = t3.coords[1] * this._sy + this._ty, t3;
    let r6 = 0;
    for (const e4 of t3.lengths) t3.coords[2 * r6] = t3.coords[2 * r6] * this._sx + this._tx, t3.coords[2 * r6 + 1] = t3.coords[2 * r6 + 1] * this._sy + this._ty, r6 += e4;
    return t3;
  }
  readCentroid() {
    return e2(this._current) ? this._computeCentroid() : this._current.centroid;
  }
  hasField(t3) {
    if (t3 in this._current.attributes) return true;
    return this.getFieldNames().map((t4) => t4.toLowerCase()).includes(t3.toLowerCase());
  }
  getFieldNames() {
    return Object.keys(this._current.attributes);
  }
  _readAttribute(t3, e4) {
    const r6 = this._current.attributes[t3];
    if (void 0 !== r6) return null != r6 && e4 && this._dateFields.has(t3) ? new Date(r6) : r6;
    const s4 = this.readAttributes(), i2 = t3 == null ? void 0 : t3.toLocaleLowerCase().trim();
    for (const n4 in s4) if (n4.toLocaleLowerCase().trim() === i2) {
      const t4 = this._current.attributes[n4];
      return null != t4 && e4 && this._dateFields.has(n4) ? new Date(t4) : t4;
    }
  }
  copyInto(t3) {
    super.copyInto(t3), t3._featureIndex = this._featureIndex, t3._transform = this._transform, t3._dateFields = this._dateFields;
  }
  _readAttributes() {
    return this._current.attributes;
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/support/AttributeStore.js
var k = s.getLogger("esri.views.layers.2d.features.support.AttributeStore");
var F = n2(l, k);
var I = { sharedArrayBuffer: has("esri-shared-array-buffer"), atomics: has("esri-atomics") };
function B(t3, e4) {
  return (i2) => e4(t3(i2));
}
var C = class {
  constructor(t3, e4, i2, s4) {
    this.size = 0, this.texelSize = 4, this.dirtyStart = 0, this.dirtyEnd = 0;
    const { pixelType: r6, layout: a2, textureOnly: n4 } = s4;
    this.textureOnly = n4 || false, this.pixelType = r6, this._ctype = e4, this.layout = a2, this._resetRange(), this._shared = t3, this.size = i2, n4 || (this.data = this._initData(r6, i2, t3, e4));
  }
  get buffer() {
    return o(this.data, (t3) => t3.buffer);
  }
  unsetComponentAllTexels(t3, e4) {
    const i2 = e(this.data);
    for (let s4 = 0; s4 < this.size * this.size; s4++) i2[s4 * this.texelSize + t3] &= ~e4;
    this.dirtyStart = 0, this.dirtyEnd = this.size * this.size - 1;
  }
  setComponentAllTexels(t3, e4) {
    const i2 = e(this.data);
    for (let s4 = 0; s4 < this.size * this.size; s4++) i2[s4 * this.texelSize + t3] |= 255 & e4;
    this.dirtyStart = 0, this.dirtyEnd = this.size * this.size - 1;
  }
  setComponent(t3, e4, i2) {
    const s4 = e(this.data);
    for (const r6 of i2) s4[r6 * this.texelSize + t3] |= e4, this.dirtyStart = Math.min(this.dirtyStart, r6), this.dirtyEnd = Math.max(this.dirtyEnd, r6);
  }
  setComponentTexel(t3, e4, i2) {
    e(this.data)[i2 * this.texelSize + t3] |= e4, this.dirtyStart = Math.min(this.dirtyStart, i2), this.dirtyEnd = Math.max(this.dirtyEnd, i2);
  }
  unsetComponentTexel(t3, e4, i2) {
    e(this.data)[i2 * this.texelSize + t3] &= ~e4, this.dirtyStart = Math.min(this.dirtyStart, i2), this.dirtyEnd = Math.max(this.dirtyEnd, i2);
  }
  getData(t3, e4) {
    const i2 = f(t3);
    return e(this.data)[i2 * this.texelSize + e4];
  }
  setData(t3, e4, i2) {
    const r6 = f(t3), a2 = 1 << e4;
    0 != (this.layout & a2) ? t(this.data) || (this.data[r6 * this.texelSize + e4] = i2, this.dirtyStart = Math.min(this.dirtyStart, r6), this.dirtyEnd = Math.max(this.dirtyEnd, r6)) : k.error("mapview-attributes-store", "Tried to set a value for a texel's readonly component");
  }
  lock() {
    this.pixelType === G.UNSIGNED_BYTE && this._shared && I.atomics && "local" !== this._ctype && Atomics.store(this.data, 0, 1);
  }
  unlock() {
    this.pixelType === G.UNSIGNED_BYTE && this._shared && I.atomics && "local" !== this._ctype && Atomics.store(this.data, 0, 0);
  }
  expand(t3) {
    if (this.size = t3, !this.textureOnly) {
      const e4 = this._initData(this.pixelType, t3, this._shared, this._ctype), i2 = e(this.data);
      e4.set(i2), this.data = e4;
    }
  }
  toMessage() {
    const t3 = this.dirtyStart, e4 = this.dirtyEnd, i2 = this.texelSize;
    if (t3 > e4) return null;
    this._resetRange();
    const s4 = !(this._shared || "local" === this._ctype), r6 = this.pixelType, a2 = this.layout, n4 = e(this.data);
    return { start: t3, end: e4, data: s4 && n4.slice(t3 * i2, (e4 + 1) * i2) || null, pixelType: r6, layout: a2 };
  }
  _initData(t3, e4, i2, s4) {
    const r6 = i2 && "local" !== s4 ? SharedArrayBuffer : ArrayBuffer, a2 = W(t3), n4 = new a2(new r6(e4 * e4 * 4 * a2.BYTES_PER_ELEMENT));
    for (let o2 = 0; o2 < n4.length; o2 += 4) n4[o2 + 1] = 255;
    return n4;
  }
  _resetRange() {
    this.dirtyStart = 2147483647, this.dirtyEnd = 0;
  }
};
var M = class {
  constructor(t3, e4, i2 = () => {
  }) {
    this._client = t3, this.config = e4, this._notifyChange = i2, this._blocks = new Array(), this._filters = new Array(S), this._attributeComputeInfo = null, this._targetType = 0, this._abortController = new AbortController(), this._hasScaleExpr = false, this._size = 32, this._nextUpdate = null, this._currUpdate = null, this._idsToHighlight = /* @__PURE__ */ new Set();
    const s4 = e4.supportsTextureFloat ? G.FLOAT : G.UNSIGNED_BYTE;
    F(`Creating AttributeStore ${I.sharedArrayBuffer ? "with" : "without"} shared memory`), this._blockDescriptors = [{ pixelType: G.UNSIGNED_BYTE, layout: 1 }, { pixelType: G.UNSIGNED_BYTE, layout: 15, textureOnly: true }, { pixelType: G.UNSIGNED_BYTE, layout: 15, textureOnly: true }, { pixelType: s4, layout: 15 }, { pixelType: s4, layout: 15 }, { pixelType: s4, layout: 15 }, { pixelType: s4, layout: 15 }], this._blocks = this._blockDescriptors.map(() => null);
  }
  destroy() {
    this._abortController.abort();
  }
  get hasScaleExpr() {
    return this._hasScaleExpr;
  }
  get _signal() {
    return this._abortController.signal;
  }
  get hasHighlight() {
    return this._idsToHighlight.size > 0;
  }
  isUpdating() {
    return !!this._currUpdate || !!this._nextUpdate;
  }
  update(t3, e4) {
    this.config = e4;
    const i2 = e4.schema.processors[0].storage, a2 = m(this._schema, i2);
    if ((t3.targets.feature || t3.targets.aggregate) && (t3.storage.data = true), a2 && (has("esri-2d-update-debug") && console.debug("Applying Update - AttributeStore:", a2), t3.storage.data = true, this._schema = i2, this._attributeComputeInfo = null, !t(i2))) {
      switch (i2.target) {
        case "feature":
          this._targetType = u;
          break;
        case "aggregate":
          this._targetType = c2;
      }
      if ("subtype" === i2.type) {
        this._attributeComputeInfo = { isSubtype: true, subtypeField: i2.subtypeField, map: /* @__PURE__ */ new Map() };
        for (const t4 in i2.mapping) {
          const e5 = i2.mapping[t4];
          if (r(e5) && r(e5.vvMapping)) for (const i3 of e5.vvMapping) this._bindAttribute(i3, parseInt(t4, 10));
        }
      } else {
        if (this._attributeComputeInfo = { isSubtype: false, map: /* @__PURE__ */ new Map() }, r(i2.vvMapping)) for (const t4 of i2.vvMapping) this._bindAttribute(t4);
        if (r(i2.attributeMapping)) for (const t4 of i2.attributeMapping) this._bindAttribute(t4);
      }
    }
  }
  onTileData(t3, e4) {
    if (t(e4.addOrUpdate)) return;
    const i2 = e4.addOrUpdate.getCursor();
    for (; i2.next(); ) {
      const t4 = i2.getDisplayId();
      this.setAttributeData(t4, i2);
    }
  }
  async setHighlight(t3, e4) {
    const i2 = 1, s4 = this._getBlock(0), r6 = e4.map((t4) => f(t4));
    s4.lock(), s4.unsetComponentAllTexels(0, i2), s4.setComponent(0, i2, r6), s4.unlock(), this._idsToHighlight.clear();
    for (const a2 of t3) this._idsToHighlight.add(a2);
    await this.sendUpdates();
  }
  async updateFilters(t3, e4, i2) {
    const { service: s4, spatialReference: r6 } = i2, { filters: a2 } = e4, n4 = a2.map((t4, e5) => this._updateFilter(t4, e5, s4, r6));
    (await Promise.all(n4)).some((t4) => t4) && (t3.storage.filters = true, has("esri-2d-update-debug") && console.debug("Applying Update - AttributeStore:", "Filters changed"));
  }
  setData(t3, e4, i2, s4) {
    const r6 = f(t3);
    this._ensureSizeForTexel(r6), this._getBlock(e4).setData(t3, i2, s4);
  }
  getData(t3, e4, i2) {
    return this._getBlock(e4).getData(t3, i2);
  }
  getHighlightFlag(t3) {
    return this._idsToHighlight.has(t3) ? T : 0;
  }
  unsetAttributeData(t3) {
    const e4 = f(t3);
    this._getBlock(0).setData(e4, 0, 0);
  }
  setAttributeData(t3, e4) {
    const s4 = f(t3);
    if (this._ensureSizeForTexel(s4), this._getBlock(0).setData(s4, 0, this.getFilterFlags(e4)), this._targetType !== e3(t3)) return;
    const r6 = this._attributeComputeInfo, a2 = this.config.supportsTextureFloat ? 1 : 2, n4 = 4;
    let o2 = null;
    r6 && (o2 = r6.isSubtype ? r6.map.get(e4.readAttribute(r6.subtypeField)) : r6.map, o2 && o2.size && o2.forEach((t4, r7) => {
      const o3 = r7 * a2 % n4, h = Math.floor(r7 * a2 / n4), l2 = this._getBlock(h + P), u2 = t4(e4);
      if (this.config.supportsTextureFloat) l2.setData(s4, o3, u2);
      else if (u2 === c) l2.setData(s4, o3, 255), l2.setData(s4, o3 + 1, 255);
      else {
        const t5 = a(Math.round(u2), -32767, 32766) + 32768, e5 = 255 & t5, r8 = (65280 & t5) >> 8;
        l2.setData(s4, o3, e5), l2.setData(s4, o3 + 1, r8);
      }
    }));
  }
  sendUpdates() {
    if (has("esri-2d-update-debug") && console.debug("AttributeStore::sendUpdate"), this._notifyChange(), this._nextUpdate) return this._nextUpdate.promise;
    if (this._currUpdate) return this._nextUpdate = D(), this._nextUpdate.promise;
    const e4 = { blocks: this._blocks.map((t3) => r(t3) ? t3.toMessage() : null) };
    return this._currUpdate = this._createResources().then(() => {
      const t3 = () => {
        if (this._currUpdate = null, this._nextUpdate) {
          const t4 = this._nextUpdate;
          this._nextUpdate = null, this.sendUpdates().then(() => t4.resolve());
        } else has("esri-2d-update-debug") && console.debug("AttributeStore::sendUpdate::No additional updates queued");
        this._notifyChange();
      };
      has("esri-2d-update-debug") && console.debug("AttributeStore::sendUpdate::client.update");
      const i2 = this._client.update(e4, this._signal).then(t3).catch(t3);
      return this._client.render(this._signal), i2;
    }).catch((e5) => {
      if (j(e5)) return this._createResourcesPromise = null, this._createResources();
      this._notifyChange(), k.error(new s2("mapview-attribute-store", "Encountered an error during client update", e5));
    }), this._currUpdate;
  }
  _ensureSizeForTexel(t3) {
    for (; t3 >= this._size * this._size; ) if (this._expand()) return;
  }
  _bindAttribute(t3, e4) {
    function i2() {
      const { normalizationField: e5 } = t3;
      return e5 ? (i3) => {
        const s5 = i3.readAttribute(e5);
        if (!s5) return null;
        return i3.readAttribute(t3.field) / s5;
      } : (e6) => e6.readAttribute(t3.field);
    }
    function s4() {
      return t3.normalizationField && k.warn("mapview-arcade", "Ignoring normalizationField specified with an arcade expression which is not supported."), (e5) => e5.getComputedNumericAtIndex(t3.fieldIndex);
    }
    let r6;
    if (null != t3.fieldIndex) r6 = s4();
    else {
      if (!t3.field) return;
      r6 = i2();
    }
    const { valueRepresentation: a2 } = t3;
    if (a2) {
      r6 = B(r6, (t4) => r3(t4, a2));
    }
    const n4 = (t4) => null === t4 || isNaN(t4) || t4 === 1 / 0 || t4 === -1 / 0 ? c : t4, o2 = this._attributeComputeInfo;
    if (o2.isSubtype) {
      const i3 = o2.map.get(e4) ?? /* @__PURE__ */ new Map();
      i3.set(t3.binding, B(r6, n4)), o2.map.set(e4, i3);
    } else o2.map.set(t3.binding, B(r6, n4));
  }
  _createResources() {
    if (r(this._createResourcesPromise)) return this._createResourcesPromise;
    this._getBlock(N), this._getBlock(O), F("Initializing AttributeStore");
    const e4 = { shared: I.sharedArrayBuffer && !("local" === this._client.type), size: this._size, blocks: E(this._blocks, (t3) => ({ textureOnly: t3.textureOnly, buffer: t3.buffer, pixelType: t3.pixelType })) }, i2 = this._client.initialize(e4, this._signal).catch((e5) => {
      j(e5) ? this._createResourcesPromise = null : k.error(new s2("mapview-attribute-store", "Encountered an error during client initialization", e5));
    });
    return this._createResourcesPromise = i2, i2.then(() => t(this._createResourcesPromise) ? this._createResources() : void 0), i2;
  }
  _getBlock(t3) {
    const e4 = this._blocks[t3];
    if (r(e4)) return e4;
    F(`Initializing AttributeBlock at index ${t3}`);
    const i2 = I.sharedArrayBuffer, s4 = this._client.type, a2 = new C(i2, s4, this._size, this._blockDescriptors[t3]);
    return this._blocks[t3] = a2, this._createResourcesPromise = null, a2;
  }
  _expand() {
    if (this._size < this.config.maxTextureSize) {
      const t3 = this._size <<= 1;
      return F("Expanding block size to", t3, this._blocks), b(this._blocks, (e4) => e4.expand(t3)), this._createResourcesPromise = null, this._size = t3, 0;
    }
    return k.error(new s2("mapview-limitations", "Maximum number of onscreen features exceeded.")), -1;
  }
  async _updateFilter(t3, e4, i2, a2) {
    const n4 = this._filters[e4], o2 = r(n4) && n4.hash;
    if (!n4 && !t3) return false;
    if (o2 === JSON.stringify(t3)) return false;
    if (t(t3)) {
      if (!n4) return false;
      const t4 = 1 << e4 + 1, i3 = this._getBlock(0);
      return this._filters[e4] = null, i3.setComponentAllTexels(0, t4), this.sendUpdates(), true;
    }
    const h = await this._getFilter(e4, i2);
    return await h.update(t3, a2), true;
  }
  async _getFilter(t3, e4) {
    const i2 = this._filters[t3];
    if (r(i2)) return i2;
    const { default: s4 } = await import("./FeatureFilter-DZW33ECO.js"), a2 = new s4({ geometryType: e4.geometryType, hasM: false, hasZ: false, timeInfo: e4.timeInfo, fieldsIndex: new r2(e4.fields) });
    return this._filters[t3] = a2, a2;
  }
  isVisible(t3) {
    return !!(2 & this._getBlock(0).getData(t3, 0));
  }
  getFilterFlags(t3) {
    let e4 = 0;
    const i2 = i(t3.getDisplayId());
    for (let a2 = 0; a2 < this._filters.length; a2++) {
      const r7 = !!(i2 & 1 << a2), n4 = this._filters[a2];
      e4 |= (!r7 || t(n4) || n4.check(t3) ? 1 : 0) << a2;
    }
    let r6 = 0;
    if (this._idsToHighlight.size) {
      const e5 = t3.getObjectId();
      r6 = this.getHighlightFlag(e5);
    }
    return e4 << 1 | r6;
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/support/DisplayIdGenerator.js
var r4 = class {
  constructor() {
    this._freeIds = [], this._idCounter = 1;
  }
  createId(r6 = false) {
    return s3(this._getFreeId(), r6);
  }
  releaseId(e4) {
    this._freeIds.push(e4);
  }
  _getFreeId() {
    return this._freeIds.length ? this._freeIds.pop() : this._idCounter++;
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/support/ComputedAttributeStorage.js
function n3(t3, e4, s4) {
  if (!(t3.length > e4)) for (; t3.length <= e4; ) t3.push(s4);
}
var r5 = class {
  constructor() {
    this._numerics = [], this._strings = [], this._idGenerator = new r4(), this._allocatedSize = 256, this._bitsets = [], this._instanceIds = [], this._bounds = [];
  }
  createBitset() {
    const e4 = this._bitsets.length;
    return this._bitsets.push(t2.create(this._allocatedSize, n)), e4 + 1;
  }
  getBitset(t3) {
    return this._bitsets[t3 - 1];
  }
  _expand() {
    this._allocatedSize <<= 1;
    for (const t3 of this._bitsets) t3.resize(this._allocatedSize);
  }
  _ensureNumeric(t3, e4) {
    this._numerics[t3] || (this._numerics[t3] = []);
    n3(this._numerics[t3], e4, 0);
  }
  _ensureInstanceId(t3) {
    n3(this._instanceIds, t3, 0);
  }
  _ensureString(t3, e4) {
    this._strings[t3] || (this._strings[t3] = []);
    n3(this._strings[t3], e4, null);
  }
  createDisplayId(t3 = false) {
    const s4 = this._idGenerator.createId();
    return s4 > this._allocatedSize && this._expand(), s3(s4, t3);
  }
  releaseDisplayId(e4) {
    for (const t3 of this._bitsets) t3.unset(e4);
    return this._idGenerator.releaseId(e4 & n);
  }
  getComputedNumeric(e4, s4) {
    return this.getComputedNumericAtIndex(e4 & n, 0);
  }
  setComputedNumeric(e4, s4, i2) {
    return this.setComputedNumericAtIndex(e4 & n, i2, 0);
  }
  getComputedString(e4, s4) {
    return this.getComputedStringAtIndex(e4 & n, 0);
  }
  setComputedString(e4, s4, i2) {
    return this.setComputedStringAtIndex(e4 & n, 0, i2);
  }
  getComputedNumericAtIndex(e4, s4) {
    const i2 = e4 & n;
    return this._ensureNumeric(s4, i2), this._numerics[s4][i2];
  }
  setComputedNumericAtIndex(e4, s4, i2) {
    const n4 = e4 & n;
    this._ensureNumeric(s4, n4), this._numerics[s4][n4] = i2;
  }
  getInstanceId(e4) {
    const s4 = e4 & n;
    return this._ensureInstanceId(s4), this._instanceIds[s4];
  }
  setInstanceId(e4, s4) {
    const i2 = e4 & n;
    this._ensureInstanceId(i2), this._instanceIds[i2] = s4;
  }
  getComputedStringAtIndex(e4, s4) {
    const i2 = e4 & n;
    return this._ensureString(s4, i2), this._strings[s4][i2];
  }
  setComputedStringAtIndex(e4, s4, i2) {
    const n4 = e4 & n;
    this._ensureString(s4, n4), this._strings[s4][n4] = i2;
  }
  getXMin(e4) {
    return this._bounds[4 * (e4 & n)];
  }
  getYMin(e4) {
    return this._bounds[4 * (e4 & n) + 1];
  }
  getXMax(e4) {
    return this._bounds[4 * (e4 & n) + 2];
  }
  getYMax(e4) {
    return this._bounds[4 * (e4 & n) + 3];
  }
  setBounds(e4, s4) {
    const i2 = s4.readHydratedGeometry();
    if (!i2 || !i2.coords.length) return false;
    let r6 = 1 / 0, u2 = 1 / 0, o2 = -1 / 0, h = -1 / 0;
    i2.forEachVertex((t3, e5) => {
      r6 = Math.min(r6, t3), u2 = Math.min(u2, e5), o2 = Math.max(o2, t3), h = Math.max(h, e5);
    });
    const d2 = e4 & n;
    return n3(this._bounds, 4 * d2 + 4, 0), this._bounds[4 * d2] = r6, this._bounds[4 * d2 + 1] = u2, this._bounds[4 * d2 + 2] = o2, this._bounds[4 * d2 + 3] = h, true;
  }
};

export {
  c3 as c,
  M,
  r5 as r
};
//# sourceMappingURL=chunk-YROREPK5.js.map
