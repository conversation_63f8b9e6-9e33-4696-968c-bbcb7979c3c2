import {
  j
} from "./chunk-JOV46W3N.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  l2 as l
} from "./chunk-C5VMWMBD.js";
import {
  g,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-RV4I37UI.js";

// node_modules/@arcgis/core/layers/mixins/refresh.js
var t = new j();
var o = /* @__PURE__ */ new WeakMap();
function n(e2) {
  c(e2) && t.push(e2);
}
function s2(e2) {
  c(e2) && t.includes(e2) && t.remove(e2);
}
function c(e2) {
  return null != e2 && "object" == typeof e2 && "refreshInterval" in e2 && "refresh" in e2;
}
function i(e2, r) {
  return Number.isFinite(e2) && Number.isFinite(r) ? r <= 0 ? e2 : i(r, e2 % r) : 0;
}
var f = 0;
var a2 = 0;
function l2() {
  const e2 = Date.now();
  for (const r of t) if (r.refreshInterval) {
    e2 - (o.get(r) ?? 0) + 5 >= 6e4 * r.refreshInterval && (o.set(r, e2), r.refresh(e2));
  }
}
l(() => {
  const e2 = Date.now();
  let r = 0;
  for (const n2 of t) r = i(Math.round(6e4 * n2.refreshInterval), r), n2.refreshInterval ? o.get(n2) || o.set(n2, e2) : o.delete(n2);
  if (r !== a2) {
    if (a2 = r, clearInterval(f), 0 === a2) return void (f = 0);
    f = setInterval(l2, a2);
  }
});

// node_modules/@arcgis/core/layers/mixins/RefreshableLayer.js
var p = (n2) => {
  let p2 = class extends n2 {
    constructor(...e2) {
      super(...e2), this.refreshInterval = 0, this.refreshTimestamp = 0, this._debounceHasDataChanged = x(() => this.hasDataChanged()), this.when().then(() => {
        n(this);
      }, () => {
      });
    }
    destroy() {
      s2(this);
    }
    get refreshParameters() {
      return { _ts: this.refreshTimestamp || null };
    }
    refresh(e2 = Date.now()) {
      g(this._debounceHasDataChanged()).then((r) => {
        r && this._set("refreshTimestamp", e2), this.emit("refresh", { dataChanged: r });
      }, (e3) => {
        s.getLogger(this.declaredClass).error(e3), this.emit("refresh", { dataChanged: false, error: e3 });
      });
    }
    async hasDataChanged() {
      return true;
    }
  };
  return e([y({ type: Number, cast: (e2) => e2 >= 0.1 ? e2 : e2 <= 0 ? 0 : 0.1, json: { write: true } })], p2.prototype, "refreshInterval", void 0), e([y({ readOnly: true })], p2.prototype, "refreshTimestamp", void 0), e([y()], p2.prototype, "refreshParameters", null), p2 = e([a("esri.layers.mixins.RefreshableLayer")], p2), p2;
};

export {
  p
};
//# sourceMappingURL=chunk-JV6TBH5W.js.map
