import {
  i
} from "./chunk-EM4JSU7Z.js";
import {
  A as A2,
  H,
  j,
  p2 as p3
} from "./chunk-6IU6DQRF.js";
import {
  R,
  T,
  V,
  _,
  d,
  f,
  k,
  k2,
  p as p2,
  r as r5,
  s
} from "./chunk-YELYN22P.js";
import {
  c,
  h
} from "./chunk-YEODPCXQ.js";
import {
  r as r4
} from "./chunk-NOZFLZZL.js";
import {
  A,
  g,
  o,
  p,
  r as r3,
  u
} from "./chunk-MQAXMQFG.js";
import {
  n,
  r as r2
} from "./chunk-36FLFRUE.js";
import {
  w
} from "./chunk-RQXGVG3K.js";
import {
  l
} from "./chunk-C5VMWMBD.js";
import {
  e
} from "./chunk-TUM6KUQZ.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/clipRay.js
function a(r6) {
  return r6 ? { ray: d(r6.ray), c0: r6.c0, c1: r6.c1 } : { ray: d(), c0: 0, c1: Number.MAX_VALUE };
}
function y(r6, c2 = a()) {
  return k(r6, c2.ray), c2.c0 = 0, c2.c1 = Number.MAX_VALUE, c2;
}
function p4(r6, c2) {
  return j2(r6, r6.c0, c2);
}
function g2(r6, c2) {
  return j2(r6, r6.c1, c2);
}
function j2(r6, c2, o2) {
  return u(o2, r6.ray.origin, g(o2, r6.ray.direction, c2));
}
var A3 = new s(() => a());

// node_modules/@arcgis/core/geometry/support/frustum.js
function H2(T3) {
  return T3 ? [p3(T3[0]), p3(T3[1]), p3(T3[2]), p3(T3[3]), p3(T3[4]), p3(T3[5])] : [p3(), p3(), p3(), p3(), p3(), p3()];
}
function I() {
  return [n(), n(), n(), n(), n(), n(), n(), n()];
}
function u2(T3, _2) {
  for (let O2 = 0; O2 < v2.NUM; O2++) A2(T3[O2], _2[O2]);
}
function s3(T3, t, E2, F2 = y2) {
  const r6 = c(f.get(), t, T3);
  h(r6, r6);
  for (let _2 = 0; _2 < h2.NUM; ++_2) {
    const T4 = w(r5.get(), g3[_2], r6);
    o(F2[_2], T4[0] / T4[3], T4[1] / T4[3], T4[2] / T4[3]);
  }
  L(E2, F2);
}
function L(T3, _2) {
  j(_2[l2.FAR_BOTTOM_LEFT], _2[l2.NEAR_BOTTOM_LEFT], _2[l2.NEAR_TOP_LEFT], T3[U.LEFT]), j(_2[l2.NEAR_BOTTOM_RIGHT], _2[l2.FAR_BOTTOM_RIGHT], _2[l2.FAR_TOP_RIGHT], T3[U.RIGHT]), j(_2[l2.FAR_BOTTOM_LEFT], _2[l2.FAR_BOTTOM_RIGHT], _2[l2.NEAR_BOTTOM_RIGHT], T3[U.BOTTOM]), j(_2[l2.NEAR_TOP_LEFT], _2[l2.NEAR_TOP_RIGHT], _2[l2.FAR_TOP_RIGHT], T3[U.TOP]), j(_2[l2.NEAR_BOTTOM_LEFT], _2[l2.NEAR_BOTTOM_RIGHT], _2[l2.NEAR_TOP_RIGHT], T3[U.NEAR]), j(_2[l2.FAR_BOTTOM_RIGHT], _2[l2.FAR_BOTTOM_LEFT], _2[l2.FAR_TOP_LEFT], T3[U.FAR]);
}
function i2(T3, _2) {
  for (let O2 = 0; O2 < v2.NUM; O2++) {
    const R3 = T3[O2];
    if (R3[0] * _2[0] + R3[1] * _2[1] + R3[2] * _2[2] + R3[3] >= _2[3]) return false;
  }
  return true;
}
function a2(T3, _2) {
  for (let O2 = 0; O2 < v2.NUM; O2++) {
    const R3 = T3[O2];
    if (!H(R3, _2)) return false;
  }
  return true;
}
var U;
var l2;
!function(T3) {
  T3[T3.LEFT = 0] = "LEFT", T3[T3.RIGHT = 1] = "RIGHT", T3[T3.BOTTOM = 2] = "BOTTOM", T3[T3.TOP = 3] = "TOP", T3[T3.NEAR = 4] = "NEAR", T3[T3.FAR = 5] = "FAR";
}(U || (U = {})), function(T3) {
  T3[T3.NEAR_BOTTOM_LEFT = 0] = "NEAR_BOTTOM_LEFT", T3[T3.NEAR_BOTTOM_RIGHT = 1] = "NEAR_BOTTOM_RIGHT", T3[T3.NEAR_TOP_RIGHT = 2] = "NEAR_TOP_RIGHT", T3[T3.NEAR_TOP_LEFT = 3] = "NEAR_TOP_LEFT", T3[T3.FAR_BOTTOM_LEFT = 4] = "FAR_BOTTOM_LEFT", T3[T3.FAR_BOTTOM_RIGHT = 5] = "FAR_BOTTOM_RIGHT", T3[T3.FAR_TOP_RIGHT = 6] = "FAR_TOP_RIGHT", T3[T3.FAR_TOP_LEFT = 7] = "FAR_TOP_LEFT";
}(l2 || (l2 = {}));
var k3 = { bottom: [l2.FAR_BOTTOM_RIGHT, l2.NEAR_BOTTOM_RIGHT, l2.NEAR_BOTTOM_LEFT, l2.FAR_BOTTOM_LEFT], near: [l2.NEAR_BOTTOM_LEFT, l2.NEAR_BOTTOM_RIGHT, l2.NEAR_TOP_RIGHT, l2.NEAR_TOP_LEFT], far: [l2.FAR_BOTTOM_RIGHT, l2.FAR_BOTTOM_LEFT, l2.FAR_TOP_LEFT, l2.FAR_TOP_RIGHT], right: [l2.NEAR_BOTTOM_RIGHT, l2.FAR_BOTTOM_RIGHT, l2.FAR_TOP_RIGHT, l2.NEAR_TOP_RIGHT], left: [l2.FAR_BOTTOM_LEFT, l2.NEAR_BOTTOM_LEFT, l2.NEAR_TOP_LEFT, l2.FAR_TOP_LEFT], top: [l2.FAR_TOP_LEFT, l2.NEAR_TOP_LEFT, l2.NEAR_TOP_RIGHT, l2.FAR_TOP_RIGHT] };
var v2;
var h2;
!function(T3) {
  T3[T3.NUM = 6] = "NUM";
}(v2 || (v2 = {})), function(T3) {
  T3[T3.NUM = 8] = "NUM";
}(h2 || (h2 = {}));
var g3 = [r4(-1, -1, -1, 1), r4(1, -1, -1, 1), r4(1, 1, -1, 1), r4(-1, 1, -1, 1), r4(-1, -1, 1, 1), r4(1, -1, 1, 1), r4(1, 1, 1, 1), r4(-1, 1, 1, 1)];
var b = new s(a);
var y2 = I();

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/Octree.js
var g4 = class _g {
  get bounds() {
    return this._root.bounds;
  }
  get halfSize() {
    return this._root.halfSize;
  }
  get root() {
    return this._root.node;
  }
  get maximumObjectsPerNode() {
    return this._maximumObjectsPerNode;
  }
  get maximumDepth() {
    return this._maximumDepth;
  }
  get objectCount() {
    return this._objectCount;
  }
  constructor(e2, t) {
    this._objectToBoundingSphere = e2, this._maximumObjectsPerNode = 10, this._maximumDepth = 20, this._degenerateObjects = /* @__PURE__ */ new Set(), this._root = new S(), this._objectCount = 0, t && (void 0 !== t.maximumObjectsPerNode && (this._maximumObjectsPerNode = t.maximumObjectsPerNode), void 0 !== t.maximumDepth && (this._maximumDepth = t.maximumDepth));
  }
  destroy() {
    this._degenerateObjects.clear(), S.clearPool(), R2[0] = null, w2.prune(), K.prune();
  }
  add(e2, t = e2.length) {
    this._objectCount += t, this._grow(e2, t);
    const n2 = S.acquire();
    for (let o2 = 0; o2 < t; o2++) {
      const t2 = e2[o2];
      this._isDegenerate(t2) ? this._degenerateObjects.add(t2) : (n2.init(this._root), this._add(t2, n2));
    }
    S.release(n2);
  }
  remove(t, n2 = null) {
    this._objectCount -= t.length;
    const o2 = S.acquire();
    for (const s4 of t) {
      const t2 = r(n2) ? n2 : _(this._objectToBoundingSphere(s4), C);
      M(t2[3]) ? (o2.init(this._root), this._remove(s4, t2, o2)) : this._degenerateObjects.delete(s4);
    }
    S.release(o2), this._shrink();
  }
  update(e2, t) {
    if (!M(t[3]) && this._isDegenerate(e2)) return;
    const n2 = v3(e2);
    this.remove(n2, t), this.add(n2);
  }
  forEachAlongRay(e2, t, n2) {
    const o2 = p2(e2, t);
    this._forEachNode(this._root, (e3) => {
      if (!this._intersectsNode(o2, e3)) return false;
      const t2 = e3.node;
      return t2.terminals.forAll((e4) => {
        this._intersectsObject(o2, e4) && n2(e4);
      }), null !== t2.residents && t2.residents.forAll((e4) => {
        this._intersectsObject(o2, e4) && n2(e4);
      }), true;
    });
  }
  forEachAlongRayWithVerticalOffset(e2, t, n2, o2) {
    const s4 = p2(e2, t);
    this._forEachNode(this._root, (e3) => {
      if (!this._intersectsNodeWithOffset(s4, e3, o2)) return false;
      const t2 = e3.node;
      return t2.terminals.forAll((e4) => {
        this._intersectsObjectWithOffset(s4, e4, o2) && n2(e4);
      }), null !== t2.residents && t2.residents.forAll((e4) => {
        this._intersectsObjectWithOffset(s4, e4, o2) && n2(e4);
      }), true;
    });
  }
  forEach(e2) {
    this._forEachNode(this._root, (t) => {
      const n2 = t.node;
      return n2.terminals.forAll(e2), null !== n2.residents && n2.residents.forAll(e2), true;
    }), this._degenerateObjects.forEach(e2);
  }
  forEachDegenerateObject(e2) {
    this._degenerateObjects.forEach(e2);
  }
  findClosest(e2, t, n2, i3 = () => true, r6 = 1 / 0) {
    let h3 = 1 / 0, a3 = 1 / 0, d2 = null;
    const u3 = z(e2, t), c2 = (o2) => {
      if (--r6, !i3(o2)) return;
      const s4 = this._objectToBoundingSphere(o2);
      if (!i2(n2, s4)) return;
      const u4 = E(e2, t, k2(s4)), c3 = u4 - s4[3], f2 = u4 + s4[3];
      c3 < h3 && (h3 = c3, a3 = f2, d2 = o2);
    };
    return this._forEachNodeDepthOrdered(this._root, (i4) => {
      if (r6 <= 0 || !i2(n2, i4.bounds)) return false;
      g(y3, u3, i4.halfSize), u(y3, y3, i4.bounds);
      if (E(e2, t, y3) > a3) return false;
      const h4 = i4.node;
      return h4.terminals.forAll((e3) => c2(e3)), null !== h4.residents && h4.residents.forAll((e3) => c2(e3)), true;
    }, e2, t), d2;
  }
  forEachInDepthRange(e2, t, n2, i3, r6, h3, a3) {
    let d2 = -1 / 0, u3 = 1 / 0;
    const c2 = { setRange: (e3) => {
      n2 === _g.DepthOrder.FRONT_TO_BACK ? (d2 = Math.max(d2, e3.near), u3 = Math.min(u3, e3.far)) : (d2 = Math.max(d2, -e3.far), u3 = Math.min(u3, -e3.near));
    } };
    c2.setRange(i3);
    const f2 = E(t, n2, e2), m2 = z(t, n2), p5 = z(t, -n2), b2 = (e3) => {
      if (!a3(e3)) return;
      const o2 = this._objectToBoundingSphere(e3), s4 = k2(o2), i4 = E(t, n2, s4) - f2, m3 = i4 - o2[3], p6 = i4 + o2[3];
      m3 > u3 || p6 < d2 || !i2(h3, o2) || r6(e3, c2);
    };
    this._forEachNodeDepthOrdered(this._root, (e3) => {
      if (!i2(h3, e3.bounds)) return false;
      g(y3, m2, e3.halfSize), u(y3, y3, e3.bounds);
      if (E(t, n2, y3) - f2 > u3) return false;
      g(y3, p5, e3.halfSize), u(y3, y3, e3.bounds);
      if (E(t, n2, y3) - f2 < d2) return false;
      const i4 = e3.node;
      return i4.terminals.forAll((e4) => b2(e4)), null !== i4.residents && i4.residents.forAll((e4) => b2(e4)), true;
    }, t, n2);
  }
  forEachNode(e2) {
    this._forEachNode(this._root, (t) => e2(t.node, t.bounds, t.halfSize));
  }
  forEachNeighbor(e2, t) {
    const n2 = T(t), o2 = k2(t), s4 = (t2) => {
      const s5 = this._objectToBoundingSphere(t2), r7 = T(s5), h4 = n2 + r7;
      return !(p(k2(s5), o2) - h4 * h4 <= 0) || e2(t2);
    };
    let r6 = true;
    const h3 = (e3) => {
      r6 && (r6 = s4(e3));
    };
    this._forEachNode(this._root, (e3) => {
      const t2 = T(e3.bounds), s5 = n2 + t2;
      if (p(k2(e3.bounds), o2) - s5 * s5 > 0) return false;
      const a3 = e3.node;
      return a3.terminals.forAll(h3), r6 && null !== a3.residents && a3.residents.forAll(h3), r6;
    }), r6 && this.forEachDegenerateObject(h3);
  }
  _intersectsNode(e2, t) {
    return x(t.bounds, 2 * -t.halfSize, k4), x(t.bounds, 2 * t.halfSize, q), i(e2.origin, e2.direction, k4, q);
  }
  _intersectsNodeWithOffset(e2, t, n2) {
    return x(t.bounds, 2 * -t.halfSize, k4), x(t.bounds, 2 * t.halfSize, q), n2.applyToMinMax(k4, q), i(e2.origin, e2.direction, k4, q);
  }
  _intersectsObject(e2, t) {
    const n2 = this._objectToBoundingSphere(t);
    return !(n2[3] > 0) || V(n2, e2);
  }
  _intersectsObjectWithOffset(e2, t, n2) {
    const o2 = this._objectToBoundingSphere(t);
    return !(o2[3] > 0) || V(n2.applyToBoundingSphere(o2), e2);
  }
  _forEachNode(e2, t) {
    let n2 = S.acquire().init(e2);
    const o2 = [n2];
    for (; 0 !== o2.length; ) {
      if (n2 = o2.pop(), t(n2) && !n2.isLeaf()) for (let e3 = 0; e3 < n2.node.children.length; e3++) {
        n2.node.children[e3] && o2.push(S.acquire().init(n2).advance(e3));
      }
      S.release(n2);
    }
  }
  _forEachNodeDepthOrdered(e2, t, n2, o2 = _g.DepthOrder.FRONT_TO_BACK) {
    let s4 = S.acquire().init(e2);
    const i3 = [s4];
    for (T2(n2, o2, W); 0 !== i3.length; ) {
      if (s4 = i3.pop(), t(s4) && !s4.isLeaf()) for (let e3 = 7; e3 >= 0; --e3) {
        const t2 = W[e3];
        s4.node.children[t2] && i3.push(S.acquire().init(s4).advance(t2));
      }
      S.release(s4);
    }
  }
  _remove(e2, t, n2) {
    w2.clear();
    const o2 = n2.advanceTo(t, (e3, t2) => {
      w2.push(e3.node), w2.push(t2);
    }) ? n2.node.terminals : n2.node.residents;
    if (o2.removeUnordered(e2), 0 === o2.length) for (let s4 = w2.length - 2; s4 >= 0; s4 -= 2) {
      const e3 = w2.data[s4], t2 = w2.data[s4 + 1];
      if (!this._purge(e3, t2)) break;
    }
  }
  _nodeIsEmpty(e2) {
    if (0 !== e2.terminals.length) return false;
    if (null !== e2.residents) return 0 === e2.residents.length;
    for (let t = 0; t < e2.children.length; t++) if (e2.children[t]) return false;
    return true;
  }
  _purge(e2, t) {
    return t >= 0 && (e2.children[t] = null), !!this._nodeIsEmpty(e2) && (null === e2.residents && (e2.residents = new l({ shrink: true })), true);
  }
  _add(e2, t) {
    t.advanceTo(this._objectToBoundingSphere(e2)) ? t.node.terminals.push(e2) : (t.node.residents.push(e2), t.node.residents.length > this._maximumObjectsPerNode && t.depth < this._maximumDepth && this._split(t));
  }
  _split(e2) {
    const t = e2.node.residents;
    e2.node.residents = null;
    for (let n2 = 0; n2 < t.length; n2++) {
      const o2 = S.acquire().init(e2);
      this._add(t.getItemAt(n2), o2), S.release(o2);
    }
  }
  _grow(e2, t) {
    if (0 !== t && (N(e2, t, (e3) => this._objectToBoundingSphere(e3), I2), M(I2[3]) && !this._fitsInsideTree(I2))) if (this._nodeIsEmpty(this._root.node)) _(I2, this._root.bounds), this._root.halfSize = 1.25 * this._root.bounds[3], this._root.updateBoundsRadiusFromHalfSize();
    else {
      const e3 = this._rootBoundsForRootAsSubNode(I2);
      this._placingRootViolatesMaxDepth(e3) ? this._rebuildTree(I2, e3) : this._growRootAsSubNode(e3), S.release(e3);
    }
  }
  _rebuildTree(e2, t) {
    r3(P, t.bounds), P[3] = t.halfSize, N([e2, P], 2, (e3) => e3, L2);
    const n2 = S.acquire().init(this._root);
    this._root.initFrom(null, L2, L2[3]), this._root.increaseHalfSize(1.25), this._forEachNode(n2, (e3) => (this.add(e3.node.terminals.data, e3.node.terminals.length), null !== e3.node.residents && this.add(e3.node.residents.data, e3.node.residents.length), true)), S.release(n2);
  }
  _placingRootViolatesMaxDepth(e2) {
    const t = Math.log(e2.halfSize / this._root.halfSize) * Math.LOG2E;
    let n2 = 0;
    return this._forEachNode(this._root, (e3) => (n2 = Math.max(n2, e3.depth), n2 + t <= this._maximumDepth)), n2 + t > this._maximumDepth;
  }
  _rootBoundsForRootAsSubNode(e2) {
    const t = e2[3], n2 = e2;
    let o2 = -1 / 0;
    const s4 = this._root.bounds, i3 = this._root.halfSize;
    for (let h3 = 0; h3 < 3; h3++) {
      const e3 = s4[h3] - i3 - (n2[h3] - t), r7 = n2[h3] + t - (s4[h3] + i3), a3 = Math.max(0, Math.ceil(e3 / (2 * i3))), d2 = Math.max(0, Math.ceil(r7 / (2 * i3))) + 1, l3 = 2 ** Math.ceil(Math.log(a3 + d2) * Math.LOG2E);
      o2 = Math.max(o2, l3), H3[h3].min = a3, H3[h3].max = d2;
    }
    for (let h3 = 0; h3 < 3; h3++) {
      let e3 = H3[h3].min, t2 = H3[h3].max;
      const n3 = (o2 - (e3 + t2)) / 2;
      e3 += Math.ceil(n3), t2 += Math.floor(n3);
      const r7 = s4[h3] - i3 - e3 * i3 * 2;
      F[h3] = r7 + (t2 + e3) * i3;
    }
    const r6 = o2 * i3;
    return F[3] = r6 * B, S.acquire().initFrom(null, F, r6, 0);
  }
  _growRootAsSubNode(e2) {
    const t = this._root.node;
    r3(I2, this._root.bounds), I2[3] = this._root.halfSize, this._root.init(e2), e2.advanceTo(I2, null, true), e2.node.children = t.children, e2.node.residents = t.residents, e2.node.terminals = t.terminals;
  }
  _shrink() {
    for (; ; ) {
      const e2 = this._findShrinkIndex();
      if (-1 === e2) break;
      this._root.advance(e2), this._root.depth = 0;
    }
  }
  _findShrinkIndex() {
    if (0 !== this._root.node.terminals.length || this._root.isLeaf()) return -1;
    let e2 = null;
    const t = this._root.node.children;
    let n2 = 0, o2 = 0;
    for (; o2 < t.length && null == e2; ) n2 = o2++, e2 = t[n2];
    for (; o2 < t.length; ) if (t[o2++]) return -1;
    return n2;
  }
  _isDegenerate(e2) {
    return !M(this._objectToBoundingSphere(e2)[3]);
  }
  _fitsInsideTree(e2) {
    const t = this._root.bounds, n2 = this._root.halfSize;
    return e2[3] <= n2 && e2[0] >= t[0] - n2 && e2[0] <= t[0] + n2 && e2[1] >= t[1] - n2 && e2[1] <= t[1] + n2 && e2[2] >= t[2] - n2 && e2[2] <= t[2] + n2;
  }
};
var S = class _S {
  constructor() {
    this.bounds = R(), this.halfSize = 0, this.initFrom(null, null, 0, 0);
  }
  init(e2) {
    return this.initFrom(e2.node, e2.bounds, e2.halfSize, e2.depth);
  }
  initFrom(t, n2, o2, s4 = this.depth) {
    return this.node = r(t) ? t : _S.createEmptyNode(), r(n2) && _(n2, this.bounds), this.halfSize = o2, this.depth = s4, this;
  }
  increaseHalfSize(e2) {
    this.halfSize *= e2, this.updateBoundsRadiusFromHalfSize();
  }
  updateBoundsRadiusFromHalfSize() {
    this.bounds[3] = this.halfSize * B;
  }
  advance(e2) {
    let t = this.node.children[e2];
    t || (t = _S.createEmptyNode(), this.node.children[e2] = t), this.node = t, this.halfSize /= 2, this.depth++;
    const n2 = A4[e2];
    return this.bounds[0] += n2[0] * this.halfSize, this.bounds[1] += n2[1] * this.halfSize, this.bounds[2] += n2[2] * this.halfSize, this.updateBoundsRadiusFromHalfSize(), this;
  }
  advanceTo(e2, t, n2 = false) {
    for (; ; ) {
      if (this.isTerminalFor(e2)) return t && t(this, -1), true;
      if (this.isLeaf()) {
        if (!n2) return t && t(this, -1), false;
        this.node.residents = null;
      }
      const o2 = this._childIndex(e2);
      t && t(this, o2), this.advance(o2);
    }
  }
  isLeaf() {
    return null != this.node.residents;
  }
  isTerminalFor(e2) {
    return e2[3] > this.halfSize / 2;
  }
  _childIndex(e2) {
    const t = this.bounds;
    return (t[0] < e2[0] ? 1 : 0) + (t[1] < e2[1] ? 2 : 0) + (t[2] < e2[2] ? 4 : 0);
  }
  static createEmptyNode() {
    return { children: [null, null, null, null, null, null, null, null], terminals: new l({ shrink: true }), residents: new l({ shrink: true }) };
  }
  static acquire() {
    return _S._pool.acquire();
  }
  static release(e2) {
    _S._pool.release(e2);
  }
  static clearPool() {
    _S._pool.prune();
  }
};
function O(e2, t) {
  e2[0] = Math.min(e2[0], t[0] - t[3]), e2[1] = Math.min(e2[1], t[1] - t[3]), e2[2] = Math.min(e2[2], t[2] - t[3]);
}
function j3(e2, t) {
  e2[0] = Math.max(e2[0], t[0] + t[3]), e2[1] = Math.max(e2[1], t[1] + t[3]), e2[2] = Math.max(e2[2], t[2] + t[3]);
}
function x(e2, t, n2) {
  n2[0] = e2[0] + t, n2[1] = e2[1] + t, n2[2] = e2[2] + t;
}
function N(e2, t, n2, o2) {
  if (1 === t) {
    const t2 = n2(e2[0]);
    _(t2, o2);
  } else {
    k4[0] = 1 / 0, k4[1] = 1 / 0, k4[2] = 1 / 0, q[0] = -1 / 0, q[1] = -1 / 0, q[2] = -1 / 0;
    for (let o3 = 0; o3 < t; o3++) {
      const t2 = n2(e2[o3]);
      M(t2[3]) && (O(k4, t2), j3(q, t2));
    }
    A(o2, k4, q, 0.5), o2[3] = Math.max(q[0] - k4[0], q[1] - k4[1], q[2] - k4[2]) / 2;
  }
}
function T2(e2, t, n2) {
  if (!K.length) for (let o2 = 0; o2 < 8; ++o2) K.push({ index: 0, distance: 0 });
  for (let o2 = 0; o2 < 8; ++o2) {
    const n3 = A4[o2];
    K.data[o2].index = o2, K.data[o2].distance = E(e2, t, n3);
  }
  K.sort((e3, t2) => e3.distance - t2.distance);
  for (let o2 = 0; o2 < 8; ++o2) n2[o2] = K.data[o2].index;
}
function z(e2, t) {
  let n2, o2 = 1 / 0;
  for (let s4 = 0; s4 < 8; ++s4) {
    const i3 = E(e2, t, D[s4]);
    i3 < o2 && (o2 = i3, n2 = D[s4]);
  }
  return n2;
}
function E(e2, t, n2) {
  return t * (e2[0] * n2[0] + e2[1] * n2[1] + e2[2] * n2[2]);
}
function M(e2) {
  return !isNaN(e2) && e2 !== -1 / 0 && e2 !== 1 / 0 && e2 > 0;
}
S._pool = new e(S), function(e2) {
  var t;
  (t = e2.DepthOrder || (e2.DepthOrder = {}))[t.FRONT_TO_BACK = 1] = "FRONT_TO_BACK", t[t.BACK_TO_FRONT = -1] = "BACK_TO_FRONT";
}(g4 || (g4 = {}));
var A4 = [r2(-1, -1, -1), r2(1, -1, -1), r2(-1, 1, -1), r2(1, 1, -1), r2(-1, -1, 1), r2(1, -1, 1), r2(-1, 1, 1), r2(1, 1, 1)];
var D = [r2(-1, -1, -1), r2(-1, -1, 1), r2(-1, 1, -1), r2(-1, 1, 1), r2(1, -1, -1), r2(1, -1, 1), r2(1, 1, -1), r2(1, 1, 1)];
var B = Math.sqrt(3);
var R2 = [null];
function v3(e2) {
  return R2[0] = e2, R2;
}
var F = R();
var y3 = n();
var k4 = n();
var q = n();
var w2 = new l();
var C = R();
var I2 = R();
var P = R();
var L2 = R();
var H3 = [{ min: 0, max: 0 }, { min: 0, max: 0 }, { min: 0, max: 0 }];
var K = new l();
var W = [0, 0, 0, 0, 0, 0, 0, 0];
var V3 = g4;

export {
  a,
  y,
  p4 as p,
  g2 as g,
  H2 as H,
  u2 as u,
  s3 as s,
  a2,
  U,
  V3 as V
};
//# sourceMappingURL=chunk-PTYT4A5P.js.map
