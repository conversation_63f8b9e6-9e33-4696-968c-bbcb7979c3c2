import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/views/interactive/sketch/SketchLabelOptions.js
var t = class extends v {
  constructor(o) {
    super(o), this.enabled = false;
  }
};
e([y({ type: Boolean, nonNullable: true })], t.prototype, "enabled", void 0), t = e([a("esri.views.interactive.sketch.SketchLabelOptions")], t);
var c = t;

export {
  c
};
//# sourceMappingURL=chunk-T6GIT4YI.js.map
