import {
  e as e2
} from "./chunk-32BGXH4N.js";
import {
  n
} from "./chunk-HTESBPT2.js";
import {
  a
} from "./chunk-WL2F66AK.js";
import {
  o as o2
} from "./chunk-TUB4N6LD.js";
import {
  e,
  f
} from "./chunk-YV4RKNU4.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/Laserline.glsl.js
function i(i2, s) {
  i2.extensions.add("GL_OES_standard_derivatives");
  const p = i2.fragment;
  p.include(a), i2.include(n), p.uniforms.add([new o2("globalAlpha", (o3) => o3.globalAlpha), new e2("glowColor", (o3) => o3.glowColor), new o2("glowWidth", (o3, e3) => o3.glowWidth * e3.camera.pixelRatio), new o2("glowFalloff", (o3) => o3.glowFalloff), new e2("innerColor", (o3) => o3.innerColor), new o2("innerWidth", (o3, e3) => o3.innerWidth * e3.camera.pixelRatio), new f("depthMap", (o3, e3) => e3.linearDepthTexture), new e("nearFar", (o3, e3) => e3.camera.nearFar), new f("frameColor", (o3, e3) => e3.mainColorTexture)]), p.code.add(o`vec4 blendPremultiplied(vec4 source, vec4 dest) {
float oneMinusSourceAlpha = 1.0 - source.a;
return vec4(
source.rgb + dest.rgb * oneMinusSourceAlpha,
source.a + dest.a * oneMinusSourceAlpha
);
}`), p.code.add(o`vec4 premultipliedColor(vec3 rgb, float alpha) {
return vec4(rgb * alpha, alpha);
}`), p.code.add(o`vec4 laserlineProfile(float dist) {
if (dist > glowWidth) {
return vec4(0.0);
}
float innerAlpha = (1.0 - smoothstep(0.0, innerWidth, dist));
float glowAlpha = pow(max(0.0, 1.0 - dist / glowWidth), glowFalloff);
return blendPremultiplied(
premultipliedColor(innerColor, innerAlpha),
premultipliedColor(glowColor, glowAlpha)
);
}`), p.code.add(o`bool laserlineReconstructFromDepth(out vec3 pos, out vec3 normal, out float depthDiscontinuityAlpha) {
float depth = linearDepthFromTexture(depthMap, uv, nearFar);
if (-depth == nearFar[0]) {
return false;
}
pos = reconstructPosition(gl_FragCoord.xy, depth);
normal = normalize(cross(dFdx(pos), dFdy(pos)));
float ddepth = fwidth(depth);
depthDiscontinuityAlpha = 1.0 - smoothstep(0.0, 0.01, -ddepth / depth);
return true;
}`), s.contrastControlEnabled ? (p.uniforms.add(new o2("globalAlphaContrastBoost", (e3) => r(e3.globalAlphaContrastBoost) ? e3.globalAlphaContrastBoost : 1)), p.code.add(o`float rgbToLuminance(vec3 color) {
return dot(vec3(0.2126, 0.7152, 0.0722), color);
}
vec4 laserlineOutput(vec4 color) {
float backgroundLuminance = rgbToLuminance(texture2D(frameColor, uv).rgb);
float alpha = clamp(globalAlpha * max(backgroundLuminance * globalAlphaContrastBoost, 1.0), 0.0, 1.0);
return color * alpha;
}`)) : p.code.add(o`vec4 laserlineOutput(vec4 color) {
return color * globalAlpha;
}`);
}

export {
  i
};
//# sourceMappingURL=chunk-TFXOTB3E.js.map
