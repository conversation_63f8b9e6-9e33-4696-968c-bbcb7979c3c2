import {
  n as n2,
  o,
  t
} from "./chunk-Z5QBIDP2.js";
import {
  P,
  S
} from "./chunk-B7KJZ7XS.js";
import {
  a as a2
} from "./chunk-4SOW7BSD.js";
import {
  n
} from "./chunk-V7D5NWEL.js";
import "./chunk-HOZO6JEJ.js";
import "./chunk-3HDXUZDA.js";
import "./chunk-EXOD2YGT.js";
import {
  ae
} from "./chunk-OL4BOHG7.js";
import "./chunk-ARVYW5FF.js";
import "./chunk-TJC57NLL.js";
import "./chunk-BXNIX5HP.js";
import "./chunk-6BXQDUKY.js";
import "./chunk-5L4KKQ6Z.js";
import "./chunk-R7EXIHGC.js";
import "./chunk-V5RJCYWT.js";
import "./chunk-FSU3GUUE.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-ZJMR67LT.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-M46W6WAV.js";
import "./chunk-FYL27KLS.js";
import "./chunk-OBW4AQOU.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-C65HMCEM.js";
import "./chunk-PRPYE2YO.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-RCQLO7DH.js";
import "./chunk-ARNVPEMS.js";
import "./chunk-HSGVCYPR.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-CJ522TQY.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-MY4EPN25.js";
import "./chunk-CV76WXPW.js";
import "./chunk-664WG4NR.js";
import "./chunk-2B52LX6T.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-LGZKVOWE.js";
import "./chunk-WKBMFG6J.js";
import "./chunk-BPRRRPC3.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-RFTQI4ZD.js";
import "./chunk-ES4OMWUC.js";
import "./chunk-UHA44FM7.js";
import "./chunk-MDHXGN24.js";
import "./chunk-6ZZUUGXX.js";
import "./chunk-TFWV44LH.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-6OFWBRK2.js";
import {
  i as i2
} from "./chunk-BNOUIPKE.js";
import {
  f
} from "./chunk-7TISIISL.js";
import {
  u
} from "./chunk-74RIZBAU.js";
import "./chunk-JSZR3BUH.js";
import "./chunk-6W6ECZU2.js";
import "./chunk-JCXMTMKU.js";
import "./chunk-WAPZ634R.js";
import "./chunk-QR72MZRQ.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-REFSHSQW.js";
import "./chunk-RURSJOSG.js";
import "./chunk-2WS4DQ5K.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-TNVLSUQU.js";
import "./chunk-LFDODH6Z.js";
import "./chunk-CNABSQRP.js";
import {
  r
} from "./chunk-ZVGXJHEK.js";
import {
  y as y2
} from "./chunk-U6IEQ6CF.js";
import {
  h
} from "./chunk-ZL54NZ7B.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-5JCRZXRL.js";
import {
  e as e2
} from "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-OYGWWPGZ.js";
import "./chunk-77E52HT5.js";
import "./chunk-GNVA6PUD.js";
import "./chunk-LN7EIT6D.js";
import "./chunk-WJ2X25V3.js";
import "./chunk-5ZZCQR67.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-MZVT2AF5.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-HQDK2TLZ.js";
import "./chunk-TBXMDTMJ.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-KED5GTQZ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-HDM6HCKB.js";
import {
  i
} from "./chunk-FO3K6ZLO.js";
import "./chunk-C54XJR7K.js";
import "./chunk-ED5KTCBE.js";
import "./chunk-TNP2LXZZ.js";
import "./chunk-ICW345PU.js";
import "./chunk-4HYRJ2BC.js";
import "./chunk-WCGNZXE2.js";
import "./chunk-AZTQJ4L4.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-GDMKZBSE.js";
import "./chunk-KCSQWRUD.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-6TZFJURO.js";
import "./chunk-T4DDX2RP.js";
import "./chunk-T72XIVTW.js";
import "./chunk-6YK77SK5.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MCIIPWB6.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-JZBEMQMW.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-OMAP5EFR.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-YBNKNHCD.js";
import "./chunk-23IHUT3O.js";
import "./chunk-SQPHHXCT.js";
import "./chunk-KOD47KEX.js";
import "./chunk-L73TUWZV.js";
import "./chunk-RN2VFGAQ.js";
import "./chunk-VP6XUPJO.js";
import "./chunk-VYC4DNQO.js";
import "./chunk-LVWRJMBJ.js";
import "./chunk-3HW44BD3.js";
import "./chunk-JSANYNBO.js";
import "./chunk-TPRZH2SY.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-V6NQCXYQ.js";
import "./chunk-XAKEPYSQ.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-2X4OO2CJ.js";
import "./chunk-HRASNGES.js";
import "./chunk-I4BKZ7SD.js";
import "./chunk-S5FBFNAP.js";
import "./chunk-VBAEC53F.js";
import "./chunk-C67OD7TM.js";
import "./chunk-5A4WR2SR.js";
import "./chunk-SJ35WMYN.js";
import "./chunk-UV4E33V4.js";
import "./chunk-PBQFTVHM.js";
import "./chunk-ZJKAJ76S.js";
import "./chunk-46HTCESL.js";
import "./chunk-NE5KC6IQ.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-Q6JATJLO.js";
import "./chunk-CV3OR36A.js";
import "./chunk-JX2QAMUH.js";
import "./chunk-DVUUHX3W.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import "./chunk-SJRT3EVN.js";
import "./chunk-RCNP3U5T.js";
import "./chunk-2CFIAWMM.js";
import "./chunk-3MWB7OGY.js";
import "./chunk-MURG32WB.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-LZMNPMOO.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-3BEYEFLH.js";
import "./chunk-NM5RTWYY.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-B2DWQPEO.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import {
  E
} from "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-IJ6FZE6K.js";
import "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2 as a,
  y
} from "./chunk-R4CPW7J5.js";
import "./chunk-2CM7MIII.js";
import "./chunk-HP475EI3.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/TileLayerView2D.js
var T = [0, 0];
var v = class extends i2(t(f(u))) {
  constructor() {
    super(...arguments), this._fetchQueue = null, this._highlightGraphics = new i(), this._highlightView = null, this._popupHighlightHelper = null, this._tileStrategy = null, this.layer = null;
  }
  get resampling() {
    return !("resampling" in this.layer) || false !== this.layer.resampling;
  }
  update(e3) {
    var _a;
    this._fetchQueue.pause(), this._fetchQueue.state = e3.state, this._tileStrategy.update(e3), this._fetchQueue.resume(), (_a = this._highlightView) == null ? void 0 : _a.processUpdate(e3);
  }
  attach() {
    const e3 = "tileServers" in this.layer ? this.layer.tileServers : null;
    if (this._tileInfoView = new h(this.layer.tileInfo, this.layer.fullExtent), this._fetchQueue = new y2({ tileInfoView: this._tileInfoView, concurrency: e3 && 10 * e3.length || 10, process: (e4, i3) => this.fetchTile(e4, i3) }), this._tileStrategy = new r({ cachePolicy: "keep", resampling: this.resampling, acquireTile: (e4) => this.acquireTile(e4), releaseTile: (e4) => this.releaseTile(e4), tileInfoView: this._tileInfoView }), P(this, this.layer)) {
      const e4 = this._highlightView = new ae({ view: this.view, graphics: this._highlightGraphics, requestUpdateCallback: () => this.requestUpdate(), container: new n(this.view.featuresTilingScheme), defaultPointSymbolEnabled: false });
      this.container.addChild(this._highlightView.container), this._popupHighlightHelper = new S({ createFetchPopupFeaturesQueryGeometry: (e5, i3) => a2(e5, i3, this.view), highlightGraphics: this._highlightGraphics, highlightGraphicUpdated: (i3, t2) => {
        e4.graphicUpdateHandler({ graphic: i3, property: t2 });
      }, layerView: this, updatingHandles: this.updatingHandles });
    }
    this.requestUpdate(), this.addAttachHandles(l(() => this.resampling, () => {
      this.doRefresh();
    })), super.attach();
  }
  detach() {
    var _a;
    super.detach(), this._tileStrategy.destroy(), this._fetchQueue.clear(), this.container.removeAllChildren(), (_a = this._popupHighlightHelper) == null ? void 0 : _a.destroy(), this._fetchQueue = this._tileStrategy = this._tileInfoView = this._popupHighlightHelper = null;
  }
  async fetchPopupFeatures(e3, i3) {
    return this._popupHighlightHelper ? this._popupHighlightHelper.fetchPopupFeatures(e3, i3) : [];
  }
  highlight(e3) {
    return this._popupHighlightHelper ? this._popupHighlightHelper.highlight(e3) : { remove() {
    } };
  }
  moveStart() {
    this.requestUpdate();
  }
  viewChange() {
    this.requestUpdate();
  }
  moveEnd() {
    this.requestUpdate();
  }
  supportsSpatialReference(e3) {
    var _a;
    return E((_a = this.layer.tileInfo) == null ? void 0 : _a.spatialReference, e3);
  }
  async doRefresh() {
    !this.attached || this.updateRequested || this.suspended || (this._fetchQueue.reset(), this._tileStrategy.tiles.forEach((e3) => this._enqueueTileFetch(e3)));
  }
  isUpdating() {
    var _a;
    return ((_a = this._fetchQueue) == null ? void 0 : _a.updating) ?? false;
  }
  acquireTile(e3) {
    const i3 = this._bitmapView.createTile(e3), t2 = i3.bitmap;
    return [t2.x, t2.y] = this._tileInfoView.getTileCoords(T, i3.key), t2.resolution = this._tileInfoView.getTileResolution(i3.key), [t2.width, t2.height] = this._tileInfoView.tileInfo.size, this._enqueueTileFetch(i3), this._bitmapView.addChild(i3), this.requestUpdate(), i3;
  }
  releaseTile(e3) {
    this._fetchQueue.abort(e3.key.id), this._bitmapView.removeChild(e3), e3.once("detach", () => e3.destroy()), this.requestUpdate();
  }
  async fetchTile(e3, i3 = {}) {
    const r2 = "tilemapCache" in this.layer ? this.layer.tilemapCache : null, { signal: s2, resamplingLevel: o2 = 0 } = i3;
    if (!r2) try {
      return await this._fetchImage(e3, s2);
    } catch (a3) {
      if (!j(a3) && !this.resampling) return o(this._tileInfoView.tileInfo.size);
      if (o2 < 3) {
        const t2 = this._tileInfoView.getTileParentId(e3.id);
        if (t2) {
          const r3 = new e2(t2), s3 = await this.fetchTile(r3, { ...i3, resamplingLevel: o2 + 1 });
          return n2(this._tileInfoView, s3, r3, e3);
        }
      }
      throw a3;
    }
    const l2 = new e2(0, 0, 0, 0);
    let h2;
    try {
      if (await r2.fetchAvailabilityUpsample(e3.level, e3.row, e3.col, l2, { signal: s2 }), l2.level !== e3.level && !this.resampling) return o(this._tileInfoView.tileInfo.size);
      h2 = await this._fetchImage(l2, s2);
    } catch (a3) {
      if (j(a3)) throw a3;
      h2 = await this._fetchImage(e3, s2);
    }
    return this.resampling ? n2(this._tileInfoView, h2, l2, e3) : h2;
  }
  async _enqueueTileFetch(e3) {
    if (!this._fetchQueue.has(e3.key.id)) {
      try {
        const i3 = await this._fetchQueue.push(e3.key);
        e3.bitmap.source = i3, e3.bitmap.width = this._tileInfoView.tileInfo.size[0], e3.bitmap.height = this._tileInfoView.tileInfo.size[1], e3.once("attach", () => this.requestUpdate());
      } catch (r2) {
        j(r2) || s.getLogger(this.declaredClass).error(r2);
      }
      this.requestUpdate();
    }
  }
  async _fetchImage(e3, i3) {
    return this.layer.fetchImageBitmapTile(e3.level, e3.row, e3.col, { signal: i3 });
  }
};
e([y()], v.prototype, "_fetchQueue", void 0), e([y()], v.prototype, "resampling", null), v = e([a("esri.views.2d.layers.TileLayerView2D")], v);
var I = v;
export {
  I as default
};
//# sourceMappingURL=TileLayerView2D-GXX5SF4Y.js.map
