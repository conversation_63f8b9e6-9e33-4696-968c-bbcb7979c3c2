import {
  j
} from "./chunk-WJFUUMLN.js";
import {
  t as t3
} from "./chunk-RVGLVPCD.js";
import {
  r as r2
} from "./chunk-ZOIBK6WV.js";
import {
  e as e2,
  n as n3
} from "./chunk-LOPMKCZI.js";
import {
  n as n2
} from "./chunk-HAEVWZ5B.js";
import {
  P
} from "./chunk-HURTVQSL.js";
import {
  h
} from "./chunk-4YSFMXMT.js";
import {
  d
} from "./chunk-Q4VCSCSY.js";
import {
  f,
  l
} from "./chunk-QUHG7NMD.js";
import {
  S
} from "./chunk-R3VLALN5.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  y as y2
} from "./chunk-VX6YUKFM.js";
import {
  m
} from "./chunk-6ILWLF72.js";
import {
  e,
  t2
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  a,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/draw/support/HighlightHelper.js
var l2 = class extends d {
  constructor(r4) {
    super(r4), this.view = null;
  }
  get count() {
    return this.handles.size;
  }
  add(r4) {
    const e4 = Array.isArray(r4) ? r4 : [r4];
    null != r4 && e4 && e4.length && e4.forEach((r5) => this._highlight(r5));
  }
  remove(r4) {
    const e4 = Array.isArray(r4) ? r4 : [r4];
    null != r4 && e4 && e4.length && e4.forEach((r5) => this._unhighlight(r5));
  }
  removeAll() {
    this.handles.removeAll();
  }
  _highlight(r4) {
    const e4 = n3(this.view, r4.layer);
    t3(e4) && this.handles.add(e4.highlight(r4), `feature-${r4.getObjectId()}`);
  }
  _unhighlight(r4) {
    r4 && this.handles.remove(`feature-${r4.getObjectId()}`);
  }
};
e([y({ readOnly: true })], l2.prototype, "count", null), e([y()], l2.prototype, "view", void 0), e([y()], l2.prototype, "add", null), l2 = e([a2("esri.views.draw.support.HighlightHelper")], l2);
var h2 = l2;

// node_modules/@arcgis/core/views/draw/support/input/GraphicMoverEvents.js
var t4 = class {
  constructor(t5, i2, s2, h4, e4) {
    this.graphic = t5, this.index = i2, this.x = s2, this.y = h4, this.viewEvent = e4, this.type = "graphic-click";
  }
};
var i = class {
  constructor(t5, i2, s2, h4, e4) {
    this.graphic = t5, this.index = i2, this.x = s2, this.y = h4, this.viewEvent = e4, this.type = "graphic-double-click";
  }
};
var s = class {
  constructor(t5, i2, s2, h4, e4, r4, c2, a4, n5, p2) {
    this.graphic = t5, this.allGraphics = i2, this.index = s2, this.x = h4, this.y = e4, this.dx = r4, this.dy = c2, this.totalDx = a4, this.totalDy = n5, this.viewEvent = p2, this.defaultPrevented = false, this.type = "graphic-move-start";
  }
  preventDefault() {
    this.defaultPrevented = true;
  }
};
var h3 = class {
  constructor(t5, i2, s2, h4, e4, r4, c2, a4, n5, p2) {
    this.graphic = t5, this.allGraphics = i2, this.index = s2, this.x = h4, this.y = e4, this.dx = r4, this.dy = c2, this.totalDx = a4, this.totalDy = n5, this.viewEvent = p2, this.defaultPrevented = false, this.type = "graphic-move";
  }
  preventDefault() {
    this.defaultPrevented = true;
  }
};
var e3 = class {
  constructor(t5, i2, s2, h4, e4, r4, c2, a4, n5, p2) {
    this.graphic = t5, this.allGraphics = i2, this.index = s2, this.x = h4, this.y = e4, this.dx = r4, this.dy = c2, this.totalDx = a4, this.totalDy = n5, this.viewEvent = p2, this.defaultPrevented = false, this.type = "graphic-move-stop";
  }
  preventDefault() {
    this.defaultPrevented = true;
  }
};
var r3 = class {
  constructor(t5, i2, s2, h4, e4) {
    this.graphic = t5, this.index = i2, this.x = s2, this.y = h4, this.viewEvent = e4, this.type = "graphic-pointer-over";
  }
};
var c = class {
  constructor(t5, i2, s2, h4, e4) {
    this.graphic = t5, this.index = i2, this.x = s2, this.y = h4, this.viewEvent = e4, this.type = "graphic-pointer-out";
  }
};
var a3 = class {
  constructor(t5, i2, s2, h4, e4) {
    this.graphic = t5, this.index = i2, this.x = s2, this.y = h4, this.viewEvent = e4, this.type = "graphic-pointer-down";
  }
};
var n4 = class {
  constructor(t5, i2, s2, h4, e4) {
    this.graphic = t5, this.index = i2, this.x = s2, this.y = h4, this.viewEvent = e4, this.type = "graphic-pointer-up";
  }
};

// node_modules/@arcgis/core/views/draw/support/GraphicMover.js
var P2 = "indicator-symbols";
var S2 = class extends n.EventedAccessor {
  constructor(i2) {
    super(i2), this._activeGraphic = null, this._dragEvent = null, this._handles = new t2(), this._hoverGraphic = null, this._indicators = [], this._initialDragGeometry = null, this._viewHandles = new t2(), this._manipulators = [], this._layerViews = null, this.type = "graphic-mover", this.callbacks = { onGraphicClick() {
    }, onGraphicDoubleClick() {
    }, onGraphicMoveStart() {
    }, onGraphicMove() {
    }, onGraphicMoveStop() {
    }, onGraphicPointerOver() {
    }, onGraphicPointerOut() {
    }, onGraphicPointerDown() {
    }, onGraphicPointerUp() {
    } }, this.enableMoveAllGraphics = false, this.graphics = [], this.indicatorsEnabled = false, this.layer = new h({ listMode: "hide", internal: true, title: "GraphicMover highlight layer" }), this.view = null;
  }
  initialize() {
    e2(this.view, this.layer), this._highlightHelper = new h2({ view: this.view }), this.refresh(), this._handles.add([l(() => {
      var _a;
      return [this.graphics, (_a = this.graphics) == null ? void 0 : _a.length];
    }, () => this.refresh()), f(() => {
      var _a;
      return (_a = this.view) == null ? void 0 : _a.ready;
    }, () => {
      this._viewHandles.add([this.view.on("immediate-click", (i2) => this._clickHandler(i2), P.TOOL), this.view.on("double-click", (i2) => this._doubleClickHandler(i2), P.TOOL), this.view.on("pointer-down", (i2) => this._pointerDownHandler(i2), P.TOOL), this.view.on("pointer-move", (i2) => this._pointerMoveHandler(i2), P.TOOL), this.view.on("pointer-up", (i2) => this._pointerUpHandler(i2), P.TOOL), this.view.on("drag", (i2) => this._dragHandler(i2), P.TOOL), this.view.on("key-down", (i2) => this._keyDownHandler(i2), P.TOOL)]);
    }, { once: true, initial: true }), l(() => this.view, (i2) => {
      this._highlightHelper.removeAll(), this._highlightHelper.view = i2;
    })]);
  }
  destroy() {
    var _a;
    this._removeIndicators(), (_a = this.view.map) == null ? void 0 : _a.remove(this.layer), this.layer.destroy(), this.reset(), this._manipulators.forEach((i2) => i2.destroy()), this._manipulators = null, this._handles = a(this._handles), this._viewHandles = a(this._viewHandles);
  }
  set highlightsEnabled(i2) {
    var _a, _b;
    (_a = this._highlightHelper) == null ? void 0 : _a.removeAll(), this._set("highlightsEnabled", i2), i2 && ((_b = this._highlightHelper) == null ? void 0 : _b.add(this.graphics));
  }
  get state() {
    const i2 = !!this.get("view.ready"), t5 = !!this.get("graphics.length"), e4 = this._activeGraphic;
    return i2 && t5 ? e4 ? "moving" : "active" : i2 ? "ready" : "disabled";
  }
  refresh() {
    this.reset(), this._setup();
  }
  reset() {
    this._activeGraphic = null, this._hoverGraphic = null, this._dragEvent = null, this._highlightHelper.removeAll();
  }
  updateGeometry(i2, t5) {
    const e4 = this.graphics[i2];
    e4 && (e4.set("geometry", t5), this._setUpIndicators());
  }
  _setup() {
    this._setUpHighlights(), this._setUpIndicators(), this._setUpManipulators(), this._syncLayerViews();
  }
  _clickHandler(i2) {
    const t5 = this._findTargetGraphic(n2(i2));
    if (t5) {
      const e4 = new t4(t5, this.graphics.indexOf(t5), i2.x, i2.y, i2);
      this.emit("graphic-click", e4), this.callbacks.onGraphicClick && this.callbacks.onGraphicClick(e4);
    }
  }
  _doubleClickHandler(i2) {
    const t5 = this._findTargetGraphic(n2(i2));
    if (t5) {
      const e4 = new i(t5, this.graphics.indexOf(t5), i2.x, i2.y, i2);
      this.emit("graphic-double-click", e4), this.callbacks.onGraphicDoubleClick && this.callbacks.onGraphicDoubleClick(e4);
    }
  }
  _pointerDownHandler(i2) {
    const t5 = this._findTargetGraphic(n2(i2));
    if (t5) {
      this._activeGraphic = t5;
      const { x: e4, y: s2 } = i2, r4 = new a3(t5, this.graphics.indexOf(t5), e4, s2, i2);
      this.emit("graphic-pointer-down", r4), this.callbacks.onGraphicPointerDown && this.callbacks.onGraphicPointerDown(r4);
    } else this._activeGraphic = null;
  }
  _pointerUpHandler(i2) {
    if (this._activeGraphic) {
      const { x: t5, y: e4 } = i2, s2 = this.graphics.indexOf(this._activeGraphic), r4 = new n4(this._activeGraphic, s2, t5, e4, i2);
      this.emit("graphic-pointer-up", r4), this.callbacks.onGraphicPointerUp && this.callbacks.onGraphicPointerUp(r4);
    }
  }
  _pointerMoveHandler(i2) {
    if (this._dragEvent) return;
    const t5 = this._findTargetGraphic(n2(i2));
    if (t5) {
      const { x: e4, y: s2 } = i2;
      if (this._hoverGraphic) {
        if (this._hoverGraphic === t5) return;
        const r5 = this.graphics.indexOf(this._hoverGraphic), h5 = new c(this.graphics[r5], r5, e4, s2, i2);
        this._hoverGraphic = null, this.emit("graphic-pointer-out", h5), this.callbacks.onGraphicPointerOut && this.callbacks.onGraphicPointerOut(h5);
      }
      const r4 = this.graphics.indexOf(t5), h4 = new r3(t5, r4, e4, s2, i2);
      return this._hoverGraphic = t5, this.emit("graphic-pointer-over", h4), void (this.callbacks.onGraphicPointerOver && this.callbacks.onGraphicPointerOver(h4));
    }
    if (this._hoverGraphic) {
      const { x: t6, y: e4 } = i2, s2 = this.graphics.indexOf(this._hoverGraphic), r4 = new c(this.graphics[s2], s2, t6, e4, i2);
      this._hoverGraphic = null, this.emit("graphic-pointer-out", r4), this.callbacks.onGraphicPointerOut && this.callbacks.onGraphicPointerOut(r4);
    }
  }
  _dragHandler(i2) {
    if ("start" !== i2.action && !this._dragEvent || !this._activeGraphic || !this._activeGraphic.geometry) return;
    "start" === i2.action && this._removeIndicators(), i2.stopPropagation();
    const { action: t5, x: e4, y: r4 } = i2, h4 = this.graphics.indexOf(this._activeGraphic), a4 = this._dragEvent ? e4 - this._dragEvent.x : 0, o = this._dragEvent ? r4 - this._dragEvent.y : 0, c2 = e4 - i2.origin.x, n5 = r4 - i2.origin.y, l3 = "start" === t5 ? this._activeGraphic.geometry : this._initialDragGeometry, p2 = r2(l3, c2, n5, this.view);
    if (this._activeGraphic.geometry = p2, this.enableMoveAllGraphics && this.graphics.forEach((i3) => {
      i3 !== this._activeGraphic && (i3.geometry = r2(i3.geometry, a4, o, this.view));
    }), this._dragEvent = i2, "start" === t5) {
      this._initialDragGeometry = p(l3);
      const t6 = new s(this._activeGraphic, this.graphics, h4, e4, r4, a4, o, c2, n5, i2);
      this.emit("graphic-move-start", t6), this.callbacks.onGraphicMoveStart && this.callbacks.onGraphicMoveStart(t6), t6.defaultPrevented && this._activeGraphic.set("geometry", l3);
    } else if ("update" === t5) {
      const t6 = new h3(this._activeGraphic, this.graphics, h4, e4, r4, a4, o, c2, n5, i2);
      this.emit("graphic-move", t6), this.callbacks.onGraphicMove && this.callbacks.onGraphicMove(t6), t6.defaultPrevented && (this._activeGraphic.geometry = l3);
    } else {
      const t6 = new e3(this._activeGraphic, this.graphics, h4, e4, r4, a4, o, c2, n5, i2);
      this._dragEvent = null, this._activeGraphic = null, this._setUpIndicators(), this.emit("graphic-move-stop", t6), this.callbacks.onGraphicMoveStop && this.callbacks.onGraphicMoveStop(t6), t6.defaultPrevented && (this.graphics[h4].set("geometry", this._initialDragGeometry), this._setUpIndicators()), this._initialDragGeometry = null;
    }
  }
  _keyDownHandler(i2) {
    "a" !== i2.key && "d" !== i2.key && "n" !== i2.key || "moving" !== this.state || i2.stopPropagation();
  }
  _findTargetGraphic(i2) {
    const t5 = this.view.toMap(i2);
    let e4 = null, s2 = Number.MAX_VALUE;
    this._syncLayerViews();
    const r4 = this._layerViews.flatMap((i3) => "graphicsViews" in i3 ? Array.from(i3.graphicsViews(), (i4) => i4.hitTest(t5)).flat() : i3.graphicsView.hitTest(t5)).filter((i3) => this.graphics.includes(i3));
    return r4.length ? r4[0] : (this._manipulators.forEach((t6) => {
      const r5 = t6.intersectionDistance(i2);
      r(r5) && r5 < s2 && (s2 = r5, e4 = t6.graphic);
    }), e4);
  }
  _syncLayerViews() {
    this._layerViews = [];
    const i2 = /* @__PURE__ */ new Set();
    for (const t5 of this.graphics) {
      const e4 = n3(this.view, t5.layer);
      e4 && i2.add(e4);
    }
    this._layerViews = [...i2];
  }
  _setUpManipulators() {
    const { graphics: i2, view: t5 } = this;
    this._manipulators.forEach((i3) => i3.destroy()), this._manipulators = (i2 == null ? void 0 : i2.length) ? i2.map((i3) => new j({ graphic: i3, view: t5 })) : [];
  }
  _setUpHighlights() {
    this.highlightsEnabled && this._highlightHelper.add(this.graphics);
  }
  _setUpIndicators() {
    if (this._removeIndicators(), this.indicatorsEnabled) {
      for (const i2 of this.graphics) {
        const t5 = i2.clone();
        t5.symbol = this._getSymbolForIndicator(i2), this._indicators.push(t5), this._handles.add(l(() => i2.symbol, () => this._setUpIndicators()), P2);
      }
      this.layer.addMany(this._indicators);
    }
  }
  _removeIndicators() {
    this._handles.remove(P2), this._indicators.length && (this.layer.removeMany(this._indicators), this._indicators.forEach((i2) => i2.destroy()), this._indicators = []);
  }
  _getSymbolForIndicator(i2) {
    const t5 = 12;
    if (t(i2.symbol)) return null;
    switch (i2.symbol.type) {
      case "cim":
        return new y2({ style: "circle", size: t5, color: [0, 0, 0, 0], outline: { color: [255, 127, 0, 1], width: 1 } });
      case "picture-marker": {
        const { xoffset: t6, yoffset: e4, height: s2, width: r4 } = i2.symbol, h4 = s2 === r4 ? r4 : Math.max(s2, r4);
        return new y2({ xoffset: t6, yoffset: e4, size: h4, style: "square", color: [0, 0, 0, 0], outline: { color: [255, 127, 0, 1], width: 1 } });
      }
      case "simple-marker": {
        const { xoffset: t6, yoffset: e4, size: s2, style: r4 } = i2.symbol;
        return new y2({ xoffset: t6, yoffset: e4, style: "circle" === r4 ? "circle" : "square", size: s2 + 2, color: [0, 0, 0, 0], outline: { color: [255, 127, 0, 1], width: 1 } });
      }
      case "simple-fill":
        return new S({ color: [0, 0, 0, 0], outline: { style: "dash", color: [255, 127, 0, 1], width: 1 } });
      case "simple-line":
        return new m({ color: [255, 127, 0, 1], style: "dash", width: 1 });
      case "text": {
        const { xoffset: e4, yoffset: s2 } = i2.symbol;
        return new y2({ xoffset: e4, yoffset: s2, size: t5, color: [0, 0, 0, 0], outline: { color: [255, 127, 0, 1], width: 1 } });
      }
      default:
        return null;
    }
  }
};
e([y()], S2.prototype, "_activeGraphic", void 0), e([y({ readOnly: true })], S2.prototype, "type", void 0), e([y()], S2.prototype, "callbacks", void 0), e([y()], S2.prototype, "enableMoveAllGraphics", void 0), e([y()], S2.prototype, "graphics", void 0), e([y({ value: false })], S2.prototype, "highlightsEnabled", null), e([y()], S2.prototype, "indicatorsEnabled", void 0), e([y()], S2.prototype, "layer", void 0), e([y({ readOnly: true })], S2.prototype, "state", null), e([y()], S2.prototype, "view", void 0), S2 = e([a2("esri.views.draw.support.GraphicMover")], S2);
var D = S2;

export {
  h2 as h,
  D
};
//# sourceMappingURL=chunk-XFUNHKWB.js.map
