import {
  g
} from "./chunk-VD4TYNIV.js";
import "./chunk-XX6IKIRW.js";
import {
  l as l4,
  t as t8,
  ue
} from "./chunk-QLIMNVCT.js";
import "./chunk-CK22CKHH.js";
import {
  d as d2,
  s as s5
} from "./chunk-6NIKJYUX.js";
import "./chunk-7QFVH5MN.js";
import {
  l as l3,
  m as m2,
  s as s4,
  u as u2
} from "./chunk-WYBCZXCQ.js";
import {
  E,
  L
} from "./chunk-HG37JYSR.js";
import "./chunk-IUNR7SKI.js";
import "./chunk-3CEVKZPD.js";
import "./chunk-UHA44FM7.js";
import "./chunk-I73RJE3H.js";
import "./chunk-OWSDEANX.js";
import "./chunk-SJMI5Q5M.js";
import "./chunk-CPQSD22U.js";
import "./chunk-IKOX2HGY.js";
import "./chunk-ST2RRB55.js";
import "./chunk-PCLDCFRI.js";
import "./chunk-6IU6DQRF.js";
import "./chunk-YELYN22P.js";
import "./chunk-ZIKXCGU7.js";
import "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import "./chunk-A7PY25IH.js";
import "./chunk-JJ3NE6DY.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import "./chunk-33Z6JDMT.js";
import "./chunk-L7LRY3AT.js";
import "./chunk-ZJC3GHA7.js";
import "./chunk-TVTTDN54.js";
import {
  a as a3
} from "./chunk-O4T45CJC.js";
import {
  t as t4
} from "./chunk-NNKS4NNY.js";
import "./chunk-LNCHRZJI.js";
import {
  p2 as p5,
  t as t7
} from "./chunk-OA2XSLRZ.js";
import "./chunk-CIHGHHEZ.js";
import "./chunk-G3QAWKCD.js";
import {
  s as s3
} from "./chunk-FZKLUDSB.js";
import "./chunk-IKGI4J4I.js";
import "./chunk-MNWHGD3K.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-6GKVSPTV.js";
import "./chunk-4FIRBBKR.js";
import "./chunk-EKOSN3EW.js";
import "./chunk-RZCOX454.js";
import "./chunk-2WMCP27R.js";
import {
  p as p4
} from "./chunk-UVJUTW2U.js";
import {
  i
} from "./chunk-RR74IWZB.js";
import {
  p as p3
} from "./chunk-KTB2COPC.js";
import "./chunk-FWXA4I6D.js";
import "./chunk-NQ3OACUM.js";
import "./chunk-HTXGAKOK.js";
import {
  i as i2
} from "./chunk-WL6G2MRC.js";
import "./chunk-OQK7L3JR.js";
import {
  p as p6
} from "./chunk-JZKMTUDN.js";
import {
  r as r3
} from "./chunk-UCWK623G.js";
import "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import "./chunk-55WN4LCX.js";
import {
  c as c3
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import {
  An,
  mn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-ZVU4V5QV.js";
import "./chunk-2AZSZWPE.js";
import "./chunk-PTIRBOGQ.js";
import {
  x as x2
} from "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-KE7SPCM7.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-MIA6BJ32.js";
import {
  t as t5
} from "./chunk-NGPCXWDX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import "./chunk-6PEIQDFP.js";
import "./chunk-ORU3OGKZ.js";
import {
  n
} from "./chunk-BS3GJQ77.js";
import "./chunk-IOBN373Z.js";
import {
  t as t6
} from "./chunk-WJPDYSRI.js";
import "./chunk-NEJXVYTI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import {
  c as c2,
  d,
  l as l2,
  m,
  p as p2
} from "./chunk-VJW7RCN7.js";
import {
  C
} from "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  U,
  a as a2,
  j as j3
} from "./chunk-QUHG7NMD.js";
import "./chunk-ZL6CFFJK.js";
import "./chunk-TWFTBWXP.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import {
  g as g2
} from "./chunk-TLKX5XIJ.js";
import {
  k
} from "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import {
  F,
  v as v2
} from "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import {
  r as r4
} from "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j as j2
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import {
  U as U2
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import {
  c
} from "./chunk-XVA5SA7P.js";
import {
  V
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l,
  u2 as u
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  r as r2,
  t2 as t3
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  t2,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  e as e2
} from "./chunk-2CM7MIII.js";
import {
  j,
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  p
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t,
  x
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/RangeInfo.js
var s6 = class extends l {
  constructor() {
    super(...arguments), this.name = null, this.field = null, this.currentRangeExtent = null, this.fullRangeExtent = null, this.type = "rangeInfo";
  }
};
e([y({ type: String, json: { read: true, write: true } })], s6.prototype, "name", void 0), e([y({ type: String, json: { read: true, write: true } })], s6.prototype, "field", void 0), e([y({ type: [Number], json: { read: true, write: true } })], s6.prototype, "currentRangeExtent", void 0), e([y({ type: [Number], json: { read: true, write: true } })], s6.prototype, "fullRangeExtent", void 0), e([y({ type: ["rangeInfo"], readOnly: true, json: { read: false, write: true } })], s6.prototype, "type", void 0), s6 = e([a("esri.layers.support.RangeInfo")], s6);

// node_modules/@arcgis/core/layers/support/PolygonCollection.js
var c4;
var p7 = c4 = class extends u(j2.ofType(v)) {
  constructor(e3) {
    super(e3);
  }
  clone() {
    return new c4(this.items.map((e3) => e3.clone()));
  }
  write(e3, r5) {
    return this.toJSON(r5);
  }
  toJSON(e3) {
    var _a;
    const r5 = (_a = e3 == null ? void 0 : e3.layer) == null ? void 0 : _a.spatialReference;
    return r5 ? this.toArray().map((t9) => {
      if (!r5.equals(t9.spatialReference)) {
        if (!An(t9.spatialReference, r5)) return e3 && e3.messages && e3.messages.push(new t2("scenefilter:unsupported", "Scene filters with incompatible spatial references are not supported", { modification: this, spatialReference: e3.layer.spatialReference, context: e3 })), null;
        const s8 = new v();
        mn(t9, s8, r5), t9 = s8;
      }
      const s7 = t9.toJSON(e3);
      return delete s7.spatialReference, s7;
    }).filter((e4) => null != e4) : ((e3 == null ? void 0 : e3.messages) && e3.messages.push(new t2("scenefilter:unsupported", "Writing Scene filters without context layer is not supported", { modification: this, spatialReference: e3.layer.spatialReference, context: e3 })), this.toArray().map((r6) => r6.toJSON(e3)));
  }
  static fromJSON(e3, r5) {
    const t9 = new c4();
    return e3.forEach((e4) => t9.add(v.fromJSON(e4, r5))), t9;
  }
};
p7 = c4 = e([a("esri.layers.support.PolygonCollection")], p7);
var l5 = p7;

// node_modules/@arcgis/core/layers/support/SceneFilter.js
var g3;
var f = g3 = class extends l {
  constructor(e3) {
    super(e3), this.spatialRelationship = "disjoint", this.geometries = new l5(), this._geometriesSource = null, this._handles = new t3();
  }
  initialize() {
    this._handles.add(a2(() => this.geometries, "after-changes", () => this.geometries = this.geometries, U));
  }
  destroy() {
    this._handles.destroy();
  }
  readGeometries(e3, o2, r5) {
    this._geometriesSource = { url: c(e3, r5), context: r5 };
  }
  async loadGeometries(e3, r5) {
    if (t(this._geometriesSource)) return;
    const { url: s7, context: t9 } = this._geometriesSource, p8 = await U2(s7, { responseType: "json", signal: x(r5, "signal") }), n2 = e3.toJSON(), c5 = p8.data.map((e4) => ({ ...e4, spatialReference: n2 }));
    this.geometries = l5.fromJSON(c5, t9), this._geometriesSource = null;
  }
  clone() {
    return new g3({ geometries: p(this.geometries), spatialRelationship: this.spatialRelationship });
  }
};
e([y({ type: ["disjoint", "contains"], nonNullable: true, json: { write: true } })], f.prototype, "spatialRelationship", void 0), e([y({ type: l5, nonNullable: true, json: { write: true } }), g({ origins: ["web-scene", "portal-item"], type: "resource", prefix: "geometries" })], f.prototype, "geometries", void 0), e([o(["web-scene", "portal-item"], "geometries")], f.prototype, "readGeometries", null), f = g3 = e([a("esri.layers.support.SceneFilter")], f);
var j4 = f;

// node_modules/@arcgis/core/layers/SceneLayer.js
var oe = ["3DObject", "Point"];
var ae = s3();
var ne = class extends a3(E(p3(c3(_(t5(O(i(b)))))))) {
  constructor(...e3) {
    super(...e3), this.featureReduction = null, this.rangeInfos = null, this.operationalLayerType = "ArcGISSceneServiceLayer", this.type = "scene", this.fields = null, this.floorInfo = null, this.outFields = null, this.nodePages = null, this.materialDefinitions = null, this.textureSetDefinitions = null, this.geometryDefinitions = null, this.serviceUpdateTimeStamp = null, this.excludeObjectIds = new j2(), this.definitionExpression = null, this.filter = null, this.path = null, this.labelsVisible = true, this.labelingInfo = null, this.legendEnabled = true, this.priority = null, this.semantic = null, this.cachedDrawingInfo = { color: false }, this.popupEnabled = true, this.popupTemplate = null, this.objectIdField = null, this.globalIdField = null, this._fieldUsageInfo = {}, this.screenSizePerspectiveEnabled = true;
  }
  normalizeCtorArgs(e3, t9) {
    return "string" == typeof e3 ? { url: e3, ...t9 } : e3;
  }
  getField(e3) {
    return this.fieldsIndex.get(e3);
  }
  getFieldDomain(e3, t9) {
    var _a, _b, _c;
    const r5 = (_b = (_a = this.getFeatureType(t9 == null ? void 0 : t9.feature)) == null ? void 0 : _a.domains) == null ? void 0 : _b[e3];
    return r5 && "inherited" !== r5.type ? r5 : ((_c = this.getField(e3)) == null ? void 0 : _c.domain) ?? null;
  }
  getFeatureType(e3) {
    return null != e3 && r(this.associatedLayer) ? this.associatedLayer.getFeatureType(e3) : null;
  }
  get types() {
    return r(this.associatedLayer) ? this.associatedLayer.types ?? [] : [];
  }
  get typeIdField() {
    return r(this.associatedLayer) ? this.associatedLayer.typeIdField : null;
  }
  get formTemplate() {
    return r(this.associatedLayer) ? this.associatedLayer.formTemplate : null;
  }
  get fieldsIndex() {
    return new r3(this.fields);
  }
  readNodePages(e3, t9, r5) {
    return "Point" === t9.layerType && (e3 = t9.pointNodePages), null == e3 || "object" != typeof e3 ? null : s4.fromJSON(e3, r5);
  }
  set elevationInfo(e3) {
    this._set("elevationInfo", e3), this.loaded && this._validateElevationInfo();
  }
  get geometryType() {
    return le[this.profile] || "mesh";
  }
  set renderer(e3) {
    F(e3, this.fieldsIndex), this._set("renderer", e3);
  }
  readCachedDrawingInfo(e3) {
    return null != e3 && "object" == typeof e3 || (e3 = {}), null == e3.color && (e3.color = false), e3;
  }
  get capabilities() {
    const e3 = r(this.associatedLayer) && this.associatedLayer.capabilities ? this.associatedLayer.capabilities : t8, { query: t9, editing: { supportsGlobalId: r5, supportsRollbackOnFailure: s7, supportsUploadWithItemId: i3, supportsGeometryUpdate: o2, supportsReturnServiceEditsInSourceSpatialReference: a4 }, data: { supportsZ: n2, supportsM: l6, isVersioned: d3, supportsAttachment: y2 }, operations: { supportsEditing: u3, supportsAdd: c5, supportsUpdate: h, supportsDelete: f2, supportsQuery: m3, supportsQueryAttachments: g4 } } = e3, v3 = e3.operations.supportsChangeTracking, b2 = r(this.associatedLayer) && r(this.associatedLayer.infoFor3D) && t6();
    return { query: t9, editing: { supportsGlobalId: r5, supportsReturnServiceEditsInSourceSpatialReference: a4, supportsRollbackOnFailure: s7, supportsGeometryUpdate: b2 && o2, supportsUploadWithItemId: i3 }, data: { supportsAttachment: y2, supportsZ: n2, supportsM: l6, isVersioned: d3 }, operations: { supportsQuery: m3, supportsQueryAttachments: g4, supportsEditing: u3 && v3, supportsAdd: b2 && c5 && v3, supportsDelete: b2 && f2 && v3, supportsUpdate: h && v3 } };
  }
  get editingEnabled() {
    return this._isOverridden("editingEnabled") ? this._get("editingEnabled") : this.userHasEditingPrivileges;
  }
  set editingEnabled(e3) {
    this._overrideIfSome("editingEnabled", e3);
  }
  get infoFor3D() {
    return r(this.associatedLayer) ? this.associatedLayer.infoFor3D : null;
  }
  get defaultPopupTemplate() {
    return r(this.associatedLayer) || this.attributeStorageInfo ? this.createPopupTemplate() : null;
  }
  readObjectIdField(e3, t9) {
    return !e3 && t9.fields && t9.fields.some((t10) => ("esriFieldTypeOID" === t10.type && (e3 = t10.name), !!e3)), e3 || void 0;
  }
  readGlobalIdField(e3, t9) {
    return !e3 && t9.fields && t9.fields.some((t10) => ("esriFieldTypeGlobalID" === t10.type && (e3 = t10.name), !!e3)), e3 || void 0;
  }
  get displayField() {
    return r(this.associatedLayer) ? this.associatedLayer.displayField : null;
  }
  readProfile(e3, t9) {
    const r5 = t9.store.profile;
    return null != r5 && pe[r5] ? pe[r5] : (s.getLogger(this.declaredClass).error("Unknown or missing profile", { profile: r5, layer: this }), "mesh-pyramids");
  }
  load(e3) {
    const t9 = r(e3) ? e3.signal : null, r5 = this.loadFromPortal({ supportedTypes: ["Scene Service"] }, e3).catch(w).then(() => this._fetchService(t9)).then(() => Promise.all([this._fetchIndexAndUpdateExtent(this.nodePages, t9), this._setAssociatedFeatureLayer(t9), r(this.filter) ? this.filter.loadGeometries(this.spatialReference) : null])).then(() => this._validateElevationInfo()).then(() => this._applyAssociatedLayerOverrides()).then(() => this._populateFieldUsageInfo()).then(() => t4(this, { origin: "service" }, t9)).then(() => F(this.renderer, this.fieldsIndex)).then(() => this.finishLoadEditablePortalLayer(e3));
    return this.addResolvingPromise(r5), Promise.resolve(this);
  }
  async beforeSave() {
    r(this.filter) && await this.load();
  }
  createQuery() {
    const e3 = new x2();
    return "mesh" !== this.geometryType && (e3.returnGeometry = true, e3.returnZ = true), e3.where = this.definitionExpression || "1=1", e3.sqlFormat = "standard", e3.outFields = ["*"], e3;
  }
  queryExtent(e3, t9) {
    return this._getAssociatedLayerForQuery().then((r5) => r5.queryExtent(e3 || this.createQuery(), t9));
  }
  queryFeatureCount(e3, t9) {
    return this._getAssociatedLayerForQuery().then((r5) => r5.queryFeatureCount(e3 || this.createQuery(), t9));
  }
  queryFeatures(e3, t9) {
    return this._getAssociatedLayerForQuery().then((r5) => r5.queryFeatures(e3 || this.createQuery(), t9)).then((e4) => {
      if (e4 == null ? void 0 : e4.features) for (const t10 of e4.features) t10.layer = this, t10.sourceLayer = this;
      return e4;
    });
  }
  async queryCachedAttributes(e3, t9) {
    const r5 = v2(this.fieldsIndex, await d2(this, s5(this)));
    return ue(this.parsedUrl.path, this.attributeStorageInfo ?? [], e3, t9, r5);
  }
  async queryCachedFeature(e3, r5) {
    const s7 = await this.queryCachedAttributes(e3, [r5]);
    if (!s7 || 0 === s7.length) throw new s2("scenelayer:feature-not-in-cached-data", "Feature not found in cached data");
    const i3 = new g2();
    return i3.attributes = s7[0], i3.layer = this, i3.sourceLayer = this, i3;
  }
  queryObjectIds(e3, t9) {
    return this._getAssociatedLayerForQuery().then((r5) => r5.queryObjectIds(e3 || this.createQuery(), t9));
  }
  queryAttachments(e3, t9) {
    return this._getAssociatedLayerForQuery().then((r5) => r5.queryAttachments(e3, t9));
  }
  getFieldUsageInfo(e3) {
    const t9 = { supportsLabelingInfo: false, supportsRenderer: false, supportsPopupTemplate: false, supportsLayerQuery: false };
    return this.loaded ? this._fieldUsageInfo[e3] || t9 : (s.getLogger(this.declaredClass).error("#getFieldUsageInfo()", "Unavailable until layer is loaded"), t9);
  }
  createPopupTemplate(e3) {
    return p6(this, e3);
  }
  _getAssociatedLayerForQuery() {
    const e3 = this.associatedLayer;
    return r(e3) && e3.loaded ? Promise.resolve(e3) : this._loadAssociatedLayerForQuery();
  }
  async _loadAssociatedLayerForQuery() {
    if (await this.load(), t(this.associatedLayer)) throw new s2("scenelayer:query-not-available", "SceneLayer queries are not available without an associated feature layer", { layer: this });
    try {
      await this.associatedLayer.load();
    } catch (e3) {
      throw new s2("scenelayer:query-not-available", "SceneLayer associated feature layer could not be loaded", { layer: this, error: e3 });
    }
    return this.associatedLayer;
  }
  hasCachedStatistics(e3) {
    return null != this.statisticsInfo && this.statisticsInfo.some((t9) => t9.name === e3);
  }
  async queryCachedStatistics(e3, t9) {
    if (await this.load(t9), !this.statisticsInfo) throw new s2("scenelayer:no-cached-statistics", "Cached statistics are not available for this layer");
    const r5 = this.fieldsIndex.get(e3);
    if (!r5) throw new s2("scenelayer:field-unexisting", `Field '${e3}' does not exist on the layer`);
    for (const s7 of this.statisticsInfo) if (s7.name === r5.name) {
      const e4 = V(this.parsedUrl.path, s7.href);
      return U2(e4, { query: { f: "json", token: this.apiKey }, responseType: "json", signal: t9 ? t9.signal : null }).then((e5) => e5.data);
    }
    throw new s2("scenelayer:no-cached-statistics", "Cached statistics for this attribute are not available");
  }
  async saveAs(e3, t9) {
    return this._debouncedSaveOperations(L.SAVE_AS, { ...t9, getTypeKeywords: () => this._getTypeKeywords(), portalItemLayerType: "scene" }, e3);
  }
  async save() {
    const e3 = { getTypeKeywords: () => this._getTypeKeywords(), portalItemLayerType: "scene" };
    return this._debouncedSaveOperations(L.SAVE, e3);
  }
  async applyEdits(e3, t9) {
    const r5 = await import("./editingSupport-IX2FTKWT.js");
    if (await this.load(), t(this.associatedLayer)) throw new s2(`${this.type}-layer:not-editable`, "Service is not editable");
    return await this.associatedLayer.load(), r5.applyEdits(this, this.associatedLayer.source, e3, t9);
  }
  on(e3, t9) {
    return super.on(e3, t9);
  }
  validateLayer(e3) {
    if (e3.layerType && !oe.includes(e3.layerType)) throw new s2("scenelayer:layer-type-not-supported", "SceneLayer does not support this layer type", { layerType: e3.layerType });
    if (isNaN(this.version.major) || isNaN(this.version.minor)) throw new s2("layer:service-version-not-supported", "Service version is not supported.", { serviceVersion: this.version.versionString, supportedVersions: "1.x, 2.x" });
    if (this.version.major > 2) throw new s2("layer:service-version-too-new", "Service version is too new.", { serviceVersion: this.version.versionString, supportedVersions: "1.x, 2.x" });
    function t9(e4, t10) {
      let r5 = false, s7 = false;
      if (null == e4) r5 = true, s7 = true;
      else {
        const i3 = t10 && t10.isGeographic;
        switch (e4) {
          case "east-north-up":
          case "earth-centered":
            r5 = true, s7 = i3;
            break;
          case "vertex-reference-frame":
            r5 = true, s7 = !i3;
            break;
          default:
            r5 = false;
        }
      }
      if (!r5) throw new s2("scenelayer:unsupported-normal-reference-frame", "Normal reference frame is invalid.");
      if (!s7) throw new s2("scenelayer:incompatible-normal-reference-frame", "Normal reference frame is incompatible with layer spatial reference.");
    }
    t9(this.normalReferenceFrame, this.spatialReference);
  }
  _getTypeKeywords() {
    const e3 = [];
    if ("points" === this.profile) e3.push("Point");
    else {
      if ("mesh-pyramids" !== this.profile) throw new s2("scenelayer:unknown-profile", "SceneLayer:save() encountered an unknown SceneLayer profile: " + this.profile);
      e3.push("3DObject");
    }
    return e3;
  }
  _populateFieldUsageInfo() {
    if (this._fieldUsageInfo = {}, this.fields) for (const e3 of this.fields) {
      const t9 = !(!this.attributeStorageInfo || !this.attributeStorageInfo.some((t10) => t10.name === e3.name)), r5 = !!(r(this.associatedLayer) && this.associatedLayer.fields && this.associatedLayer.fields.some((t10) => t10 && e3.name === t10.name)), s7 = { supportsLabelingInfo: t9, supportsRenderer: t9, supportsPopupTemplate: t9 || r5, supportsLayerQuery: r5 };
      this._fieldUsageInfo[e3.name] = s7;
    }
  }
  _applyAssociatedLayerOverrides() {
    this._applyAssociatedLayerFieldsOverrides(), this._applyAssociatedLayerPopupOverrides();
  }
  _applyAssociatedLayerFieldsOverrides() {
    if (t(this.associatedLayer) || !this.associatedLayer.fields) return;
    let e3 = null;
    for (const t9 of this.associatedLayer.fields) {
      const r5 = this.getField(t9.name);
      r5 ? (!r5.domain && t9.domain && (r5.domain = t9.domain.clone()), r5.editable = t9.editable, r5.nullable = t9.nullable, r5.length = t9.length) : (e3 || (e3 = this.fields ? this.fields.slice() : []), e3.push(t9.clone()));
    }
    e3 && this._set("fields", e3);
  }
  _applyAssociatedLayerPopupOverrides() {
    if (t(this.associatedLayer)) return;
    const e3 = ["popupTemplate", "popupEnabled"], t9 = e2(this);
    for (let r5 = 0; r5 < e3.length; r5++) {
      const s7 = e3[r5], i3 = this.originIdOf(s7), o2 = this.associatedLayer.originIdOf(s7);
      i3 < o2 && (o2 === r2.SERVICE || o2 === r2.PORTAL_ITEM) && t9.setAtOrigin(s7, this.associatedLayer[s7], o2);
    }
  }
  async _setAssociatedFeatureLayer(e3) {
    if (!["mesh-pyramids", "points"].includes(this.profile)) return;
    const t9 = new l4(this.parsedUrl, this.portalItem, this.apiKey, e3);
    try {
      this.associatedLayer = await t9.fetch();
    } catch (r5) {
      j(r5) || this._logWarningOnPopupEnabled();
    }
  }
  async _logWarningOnPopupEnabled() {
    await j3(() => this.popupEnabled && null != this.popupTemplate);
    const e3 = `this SceneLayer: ${this.title}`;
    null == this.attributeStorageInfo ? s.getLogger(this.declaredClass).warn(`Associated FeatureLayer could not be loaded and no binary attributes found. Popups will not work on ${e3}`) : s.getLogger(this.declaredClass).info(`Associated FeatureLayer could not be loaded. Falling back to binary attributes for Popups on ${e3}`);
  }
  _validateElevationInfo() {
    const e3 = this.elevationInfo;
    e3 && ("mesh-pyramids" === this.profile && "relative-to-scene" === e3.mode && s.getLogger(this.declaredClass).warn(".elevationInfo=", "Mesh scene layers don't support relative-to-scene elevation mode"), e3.featureExpressionInfo && "0" !== e3.featureExpressionInfo.expression && s.getLogger(this.declaredClass).warn(".elevationInfo=", "Scene layers do not support featureExpressionInfo"));
  }
};
e([y({ types: { key: "type", base: t7, typeMap: { selection: p5 } }, json: { origins: { "web-scene": { name: "layerDefinition.featureReduction", write: true }, "portal-item": { name: "layerDefinition.featureReduction", write: true } } } })], ne.prototype, "featureReduction", void 0), e([y({ type: [s6], json: { read: false, origins: { "web-scene": { name: "layerDefinition.rangeInfos", write: true }, "portal-item": { name: "layerDefinition.rangeInfos", write: true } } } })], ne.prototype, "rangeInfos", void 0), e([y({ json: { read: false } })], ne.prototype, "associatedLayer", void 0), e([y({ type: ["show", "hide"] })], ne.prototype, "listMode", void 0), e([y({ type: ["ArcGISSceneServiceLayer"] })], ne.prototype, "operationalLayerType", void 0), e([y({ json: { read: false }, readOnly: true })], ne.prototype, "type", void 0), e([y({ ...ae.fields, readOnly: true, json: { read: false, origins: { service: { read: true } } } })], ne.prototype, "fields", void 0), e([y()], ne.prototype, "types", null), e([y()], ne.prototype, "typeIdField", null), e([y()], ne.prototype, "formTemplate", null), e([y({ readOnly: true })], ne.prototype, "fieldsIndex", null), e([y({ type: p4, json: { read: { source: "layerDefinition.floorInfo" }, write: { target: "layerDefinition.floorInfo" } } })], ne.prototype, "floorInfo", void 0), e([y(ae.outFields)], ne.prototype, "outFields", void 0), e([y({ type: s4, readOnly: true, json: { read: false } })], ne.prototype, "nodePages", void 0), e([o("service", "nodePages", ["nodePages", "pointNodePages"])], ne.prototype, "readNodePages", null), e([y({ type: [l3], readOnly: true })], ne.prototype, "materialDefinitions", void 0), e([y({ type: [u2], readOnly: true })], ne.prototype, "textureSetDefinitions", void 0), e([y({ type: [m2], readOnly: true })], ne.prototype, "geometryDefinitions", void 0), e([y({ readOnly: true })], ne.prototype, "serviceUpdateTimeStamp", void 0), e([y({ readOnly: true })], ne.prototype, "attributeStorageInfo", void 0), e([y({ readOnly: true })], ne.prototype, "statisticsInfo", void 0), e([y({ type: j2.ofType(Number), nonNullable: true, json: { origins: { service: { read: false, write: false } }, name: "layerDefinition.excludeObjectIds", write: { enabled: true } } })], ne.prototype, "excludeObjectIds", void 0), e([y({ type: String, json: { origins: { service: { read: false, write: false } }, name: "layerDefinition.definitionExpression", write: { enabled: true, allowNull: true } } })], ne.prototype, "definitionExpression", void 0), e([y({ type: j4, json: { name: "layerDefinition.polygonFilter", write: { enabled: true, allowNull: true }, origins: { service: { read: false, write: false } } } })], ne.prototype, "filter", void 0), e([y({ type: String, json: { origins: { "web-scene": { read: true, write: true } }, read: false } })], ne.prototype, "path", void 0), e([y(d)], ne.prototype, "elevationInfo", null), e([y({ type: String })], ne.prototype, "geometryType", null), e([y(m)], ne.prototype, "labelsVisible", void 0), e([y({ type: [C], json: { origins: { service: { name: "drawingInfo.labelingInfo", read: { reader: i2 }, write: false } }, name: "layerDefinition.drawingInfo.labelingInfo", read: { reader: i2 }, write: true } })], ne.prototype, "labelingInfo", void 0), e([y(c2)], ne.prototype, "legendEnabled", void 0), e([y({ type: Number, json: { origins: { "web-document": { default: 1, write: { enabled: true, target: { opacity: { type: Number }, "layerDefinition.drawingInfo.transparency": { type: Number } } }, read: { source: ["opacity", "layerDefinition.drawingInfo.transparency"], reader(e3, t9) {
  var _a, _b;
  if ("number" == typeof e3 && e3 >= 0 && e3 <= 1) return e3;
  const r5 = (_b = (_a = t9.layerDefinition) == null ? void 0 : _a.drawingInfo) == null ? void 0 : _b.transparency;
  return void 0 !== r5 ? r4(r5) : void 0;
} } }, "portal-item": { write: true }, service: { read: false } } } })], ne.prototype, "opacity", void 0), e([y({ type: ["Low", "High"], readOnly: true, json: { read: false, origins: { service: { read: true } } } })], ne.prototype, "priority", void 0), e([y({ type: ["Labels"], readOnly: true, json: { read: false, origins: { service: { read: true } } } })], ne.prototype, "semantic", void 0), e([y({ types: n, json: { origins: { service: { read: { source: "drawingInfo.renderer" } } }, name: "layerDefinition.drawingInfo.renderer", write: true }, value: null })], ne.prototype, "renderer", null), e([y({ json: { read: false } })], ne.prototype, "cachedDrawingInfo", void 0), e([o("service", "cachedDrawingInfo")], ne.prototype, "readCachedDrawingInfo", null), e([y({ readOnly: true, json: { read: false } })], ne.prototype, "capabilities", null), e([y({ type: Boolean, json: { read: false } })], ne.prototype, "editingEnabled", null), e([y({ readOnly: true, json: { write: false, read: false } })], ne.prototype, "infoFor3D", null), e([y(p2)], ne.prototype, "popupEnabled", void 0), e([y({ type: k, json: { name: "popupInfo", write: true } })], ne.prototype, "popupTemplate", void 0), e([y({ readOnly: true, json: { read: false } })], ne.prototype, "defaultPopupTemplate", null), e([y({ type: String, json: { read: false } })], ne.prototype, "objectIdField", void 0), e([o("service", "objectIdField", ["objectIdField", "fields"])], ne.prototype, "readObjectIdField", null), e([y({ type: String, json: { read: false } })], ne.prototype, "globalIdField", void 0), e([o("service", "globalIdField", ["globalIdField", "fields"])], ne.prototype, "readGlobalIdField", null), e([y({ readOnly: true, type: String, json: { read: false } })], ne.prototype, "displayField", null), e([y({ type: String, json: { read: false } })], ne.prototype, "profile", void 0), e([o("service", "profile", ["store.profile"])], ne.prototype, "readProfile", null), e([y({ readOnly: true, type: String, json: { origins: { service: { read: { source: "store.normalReferenceFrame" } } }, read: false } })], ne.prototype, "normalReferenceFrame", void 0), e([y(l2)], ne.prototype, "screenSizePerspectiveEnabled", void 0), ne = e([a("esri.layers.SceneLayer")], ne);
var pe = { "mesh-pyramids": "mesh-pyramids", meshpyramids: "mesh-pyramids", "features-meshes": "mesh-pyramids", points: "points", "features-points": "points", lines: "lines", "features-lines": "lines", polygons: "polygons", "features-polygons": "polygons" };
var le = { "mesh-pyramids": "mesh", points: "point", lines: "polyline", polygons: "polygon" };
var de = ne;
export {
  de as default
};
//# sourceMappingURL=SceneLayer-SR54AOZO.js.map
