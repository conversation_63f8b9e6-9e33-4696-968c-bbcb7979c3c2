import {
  o
} from "./chunk-4W7HU754.js";
import {
  l as l2
} from "./chunk-CDZ24ELJ.js";
import {
  b
} from "./chunk-SROTSYJS.js";
import {
  An,
  gn,
  rn
} from "./chunk-UYAKJRPP.js";
import {
  J
} from "./chunk-FIVMDF4P.js";
import {
  x as x2
} from "./chunk-FSNYK4TH.js";
import {
  U,
  l
} from "./chunk-QUHG7NMD.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  d,
  i,
  u
} from "./chunk-G5KX4JSG.js";
import {
  x
} from "./chunk-MQAXMQFG.js";
import {
  n as n2
} from "./chunk-36FLFRUE.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/interactive/support/utils.js
function t2(t3) {
  let e2 = 0, a2 = 0, r2 = 0;
  return t3 ? ("cim" === t3.type && t3.data.symbol && "symbolLayers" in t3.data.symbol && t3.data.symbol.symbolLayers && t3.data.symbol.symbolLayers.map((s) => {
    "CIMVectorMarker" === s.type && s.anchorPoint && (Math.abs(s.anchorPoint.x) > e2 && (e2 = s.anchorPoint.x), Math.abs(s.anchorPoint.y) > a2 && (a2 = s.anchorPoint.y), r(s.size) && s.size > r2 && (r2 = s.size));
  }), e2 = u(e2), a2 = u(a2), r2 = u(r2), { offsetX: e2, offsetY: a2, size: r2 }) : { offsetX: e2, offsetY: a2, size: r2 };
}

// node_modules/@arcgis/core/views/interactive/GraphicManipulator.js
var j = class extends v {
  set graphic(t3) {
    this._circleCollisionCache = null, this._originalSymbol = t3.symbol, this._set("graphic", t3), this.attachSymbolChanged();
  }
  get elevationInfo() {
    const { layer: t3 } = this.graphic, e2 = t3 && "elevationInfo" in t3 ? t3.elevationInfo : null, o2 = l2(this.graphic), i2 = e2 ? e2.offset : 0;
    return new x2({ mode: o2, offset: i2 });
  }
  set focusedSymbol(t3) {
    t3 !== this._get("focusedSymbol") && (this._set("focusedSymbol", t3), this._updateGraphicSymbol(), this._circleCollisionCache = null);
  }
  grabbableForEvent() {
    return true;
  }
  set grabbing(t3) {
    t3 !== this._get("grabbing") && (this._set("grabbing", t3), this._updateGraphicSymbol());
  }
  set hovering(t3) {
    t3 !== this._get("hovering") && (this._set("hovering", t3), this._updateGraphicSymbol());
  }
  set selected(t3) {
    t3 !== this._get("selected") && (this._set("selected", t3), this._updateGraphicSymbol(), this.events.emit("select-changed", { action: t3 ? "select" : "deselect" }));
  }
  get _focused() {
    return this._get("hovering") || this._get("grabbing");
  }
  constructor(t3) {
    super(t3), this.layer = null, this.interactive = true, this.selectable = false, this.grabbable = true, this.dragging = false, this.cursor = null, this.events = new n.EventEmitter(), this._circleCollisionCache = null, this._graphicSymbolChangedHandle = null, this._originalSymbol = null;
  }
  destroy() {
    this.detachSymbolChanged(), this._resetGraphicSymbol(), this._set("view", null);
  }
  intersectionDistance(t3) {
    const e2 = this.graphic;
    if (false === e2.visible) return null;
    const o2 = e2.geometry;
    if (t(o2)) return null;
    const r2 = this._get("focusedSymbol"), l3 = r(r2) ? r2 : e2.symbol;
    return "2d" === this.view.type ? this._intersectDistance2D(this.view, t3, o2, l3) : this._intersectDistance3D(this.view, t3, e2);
  }
  attach() {
    this.attachSymbolChanged(), r(this.layer) && this.layer.add(this.graphic);
  }
  detach() {
    this.detachSymbolChanged(), this._resetGraphicSymbol(), r(this.layer) && this.layer.remove(this.graphic);
  }
  attachSymbolChanged() {
    this.detachSymbolChanged(), this._graphicSymbolChangedHandle = l(() => {
      var _a;
      return (_a = this.graphic) == null ? void 0 : _a.symbol;
    }, (t3) => {
      r(t3) && t3 !== this.focusedSymbol && t3 !== this._originalSymbol && (this._originalSymbol = t3, this._focused && r(this.focusedSymbol) && (this.graphic.symbol = this.focusedSymbol));
    }, U);
  }
  detachSymbolChanged() {
    r(this._graphicSymbolChangedHandle) && (this._graphicSymbolChangedHandle.remove(), this._graphicSymbolChangedHandle = null);
  }
  onElevationChange() {
  }
  onViewChange() {
  }
  _updateGraphicSymbol() {
    this.graphic.symbol = this._focused && r(this.focusedSymbol) ? this.focusedSymbol : this._originalSymbol;
  }
  _resetGraphicSymbol() {
    this.graphic.symbol = this._originalSymbol;
  }
  _intersectDistance2D(t3, e2, o2, r2) {
    var _a, _b;
    if (r2 = r2 || J(o2), t(r2)) return null;
    const l3 = 1;
    let c = this._circleCollisionCache;
    if ("point" === o2.type && "cim" === r2.type && "CIMPointSymbol" === ((_a = r2.data.symbol) == null ? void 0 : _a.type) && r2.data.symbol.symbolLayers) {
      const { offsetX: i2, offsetY: s, size: a2 } = t2(r2), c2 = d(e2, D), h = a2 / 2, p = t3.toScreen(o2), u2 = p.x + i2, y2 = p.y + s;
      return b(c2, [u2, y2]) < h * h ? l3 : null;
    }
    if ("point" !== o2.type || "simple-marker" !== r2.type) return o(e2, o2, t3) ? l3 : null;
    if (t(c) || !c.originalPoint.equals(o2)) {
      const e3 = o2, i2 = t3.spatialReference;
      if (An(e3.spatialReference, i2)) {
        const t4 = rn(e3, i2);
        c = { originalPoint: e3.clone(), mapPoint: t4, radiusPx: u(r2.size) }, this._circleCollisionCache = c;
      }
    }
    if (r(c)) {
      const o3 = d(e2, D), i2 = (_b = t3.toScreen) == null ? void 0 : _b.call(t3, c.mapPoint);
      if (!i2) return null;
      const s = c.radiusPx, h = i2.x + u(r2.xoffset), p = i2.y - u(r2.yoffset);
      return b(o3, [h, p]) < s * s ? l3 : null;
    }
    return null;
  }
  _intersectDistance3D(t3, e2, o2) {
    const i2 = t3.toMap(e2, { include: [o2] });
    return i2 && gn(i2, w, t3.renderSpatialReference) ? x(w, t3.state.camera.eye) : null;
  }
};
e([y({ constructOnly: true, nonNullable: true })], j.prototype, "graphic", null), e([y()], j.prototype, "elevationInfo", null), e([y({ constructOnly: true, nonNullable: true })], j.prototype, "view", void 0), e([y({ value: null })], j.prototype, "focusedSymbol", null), e([y({ constructOnly: true })], j.prototype, "layer", void 0), e([y()], j.prototype, "interactive", void 0), e([y()], j.prototype, "selectable", void 0), e([y()], j.prototype, "grabbable", void 0), e([y({ value: false })], j.prototype, "grabbing", null), e([y()], j.prototype, "dragging", void 0), e([y()], j.prototype, "hovering", null), e([y({ value: false })], j.prototype, "selected", null), e([y()], j.prototype, "cursor", void 0), j = e([a("esri.views.interactive.GraphicManipulator")], j);
var w = n2();
var D = i();

export {
  t2 as t,
  j
};
//# sourceMappingURL=chunk-WJFUUMLN.js.map
