import {
  n as n3,
  r as r2,
  r2 as r3
} from "./chunk-TNXJBJO3.js";
import {
  g
} from "./chunk-5HHROPN6.js";
import "./chunk-TZ4YPYDT.js";
import {
  ee
} from "./chunk-D6BU5EUE.js";
import "./chunk-QYYMKGDW.js";
import "./chunk-TMGUQ6KD.js";
import {
  o
} from "./chunk-ZK5O2DLX.js";
import "./chunk-CCFNWAA2.js";
import "./chunk-JLELSJK5.js";
import "./chunk-FRO3RSRO.js";
import {
  j as j3
} from "./chunk-IEIKQ72S.js";
import {
  t as t3
} from "./chunk-3IDKVHSA.js";
import "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import "./chunk-HLLJFAS4.js";
import "./chunk-6DAQTVXB.js";
import "./chunk-FUIIMETN.js";
import "./chunk-2CLVPBYJ.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-M4ZUXRA3.js";
import "./chunk-WJKHSSMC.js";
import "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-ONE6GLG5.js";
import {
  c as c4,
  s as s2,
  t as t4
} from "./chunk-P2G4OGHI.js";
import {
  S,
  c as c3,
  f as f4,
  x as x3
} from "./chunk-XZ2UVSB4.js";
import "./chunk-NSJUSNRV.js";
import {
  a as a2
} from "./chunk-YMY3DTA5.js";
import "./chunk-TNP2LXZZ.js";
import {
  It,
  at,
  ht
} from "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import {
  g as g2
} from "./chunk-HTXGAKOK.js";
import "./chunk-UCWK623G.js";
import {
  d,
  j as j2
} from "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-2AZSZWPE.js";
import "./chunk-PTIRBOGQ.js";
import {
  x
} from "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  c as c2,
  d as d2
} from "./chunk-Q4VCSCSY.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import {
  x as x2
} from "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  U,
  j,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import {
  E,
  c,
  f as f3,
  u,
  y as y2
} from "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import {
  n as n2
} from "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  t2,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  n
} from "./chunk-2CM7MIII.js";
import {
  A,
  f,
  w
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import {
  i
} from "./chunk-GZGAQUSK.js";
import {
  e as e2,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/core/AsyncSequence.js
var r4 = class extends v {
  constructor() {
    super(...arguments), this.updating = false, this._pending = [];
  }
  push(s3, t6) {
    this._pending.push({ promise: s3, callback: t6 }), 1 === this._pending.length && this._process();
  }
  _process() {
    if (!this._pending.length) return void (this.updating = false);
    this.updating = true;
    const s3 = this._pending[0];
    s3.promise.then((t6) => s3.callback(t6)).catch(() => {
    }).then(() => {
      this._pending.shift(), this._process();
    });
  }
};
e([y()], r4.prototype, "updating", void 0), r4 = e([a("esri.core.AsyncSequence")], r4);

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/PendingFeatureTile.js
var o2 = class {
  constructor(t6, e3) {
    this.data = t6, this.resolution = e3, this.state = { type: u2.CREATED }, this.alive = true;
  }
  process(t6) {
    switch (this.state.type) {
      case u2.CREATED:
        return this.state = this._gotoFetchCount(this.state, t6), this.state.task.promise.then(t6.resume, t6.resume);
      case u2.FETCH_COUNT:
        break;
      case u2.FETCHED_COUNT:
        return this.state = this._gotoFetchFeatures(this.state, t6), this.state.task.promise.then(t6.resume, t6.resume);
      case u2.FETCH_FEATURES:
        break;
      case u2.FETCHED_FEATURES:
        this.state = this._goToDone(this.state, t6);
      case u2.DONE:
    }
    return null;
  }
  get debugInfo() {
    return { data: this.data, featureCount: this._featureCount, state: this._stateToString };
  }
  get _featureCount() {
    switch (this.state.type) {
      case u2.CREATED:
      case u2.FETCH_COUNT:
        return 0;
      case u2.FETCHED_COUNT:
        return this.state.featureCount;
      case u2.FETCH_FEATURES:
        return this.state.previous.featureCount;
      case u2.FETCHED_FEATURES:
        return this.state.features.length;
      case u2.DONE:
        return this.state.previous.features.length;
    }
  }
  get _stateToString() {
    switch (this.state.type) {
      case u2.CREATED:
        return "created";
      case u2.FETCH_COUNT:
        return "fetch-count";
      case u2.FETCHED_COUNT:
        return "fetched-count";
      case u2.FETCH_FEATURES:
        return "fetch-features";
      case u2.FETCHED_FEATURES:
        return "fetched-features";
      case u2.DONE:
        return "done";
    }
  }
  _gotoFetchCount(s3, a3) {
    return { type: u2.FETCH_COUNT, previous: s3, task: j2(async (t6) => {
      const s4 = await d(a3.fetchCount(this, t6));
      this.state.type === u2.FETCH_COUNT && (this.state = this._gotoFetchedCount(this.state, s4.ok ? s4.value : 1 / 0));
    }) };
  }
  _gotoFetchedCount(t6, e3) {
    return { type: u2.FETCHED_COUNT, featureCount: e3, previous: t6 };
  }
  _gotoFetchFeatures(s3, a3) {
    return { type: u2.FETCH_FEATURES, previous: s3, task: j2(async (t6) => {
      const r5 = await d(a3.fetchFeatures(this, s3.featureCount, t6));
      this.state.type === u2.FETCH_FEATURES && (this.state = this._gotoFetchedFeatures(this.state, r5.ok ? r5.value : []));
    }) };
  }
  _gotoFetchedFeatures(t6, e3) {
    return { type: u2.FETCHED_FEATURES, previous: t6, features: e3 };
  }
  _goToDone(t6, e3) {
    return e3.finish(this, t6.features), { type: u2.DONE, previous: t6 };
  }
  reset() {
    const t6 = this.state;
    switch (this.state = { type: u2.CREATED }, t6.type) {
      case u2.CREATED:
      case u2.FETCHED_COUNT:
      case u2.FETCHED_FEATURES:
      case u2.DONE:
        break;
      case u2.FETCH_COUNT:
      case u2.FETCH_FEATURES:
        t6.task.abort();
    }
  }
  intersects(t6) {
    return !(!t(t6) && this.data.extent) || (c(t6, T), E(this.data.extent, T));
  }
};
var u2;
!function(t6) {
  t6[t6.CREATED = 0] = "CREATED", t6[t6.FETCH_COUNT = 1] = "FETCH_COUNT", t6[t6.FETCHED_COUNT = 2] = "FETCHED_COUNT", t6[t6.FETCH_FEATURES = 3] = "FETCH_FEATURES", t6[t6.FETCHED_FEATURES = 4] = "FETCHED_FEATURES", t6[t6.DONE = 5] = "DONE";
}(u2 || (u2 = {}));
var T = u();

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTiledFetcher.js
var C = class extends d2 {
  get _minimumVerticesPerFeature() {
    var _a;
    switch ((_a = this.store) == null ? void 0 : _a.featureStore.geometryType) {
      case "esriGeometryPoint":
      case "esriGeometryMultipoint":
        return 1;
      case "esriGeometryPolygon":
        return 4;
      case "esriGeometryPolyline":
        return 2;
    }
  }
  set filter(e3) {
    const t6 = this._get("filter"), i2 = this._filterProperties(e3);
    JSON.stringify(t6) !== JSON.stringify(i2) && this._set("filter", i2);
  }
  set customParameters(e3) {
    const t6 = this._get("customParameters");
    JSON.stringify(t6) !== JSON.stringify(e3) && this._set("customParameters", e3);
  }
  get _configuration() {
    return { filter: this.filter, customParameters: this.customParameters, tileInfo: this.tileInfo, tileSize: this.tileSize };
  }
  set tileInfo(e3) {
    const t6 = this._get("tileInfo");
    t6 !== e3 && (r(e3) && r(t6) && JSON.stringify(e3) === JSON.stringify(t6) || (this._set("tileInfo", e3), this.store.tileInfo = e3));
  }
  set tileSize(e3) {
    this._get("tileSize") !== e3 && this._set("tileSize", e3);
  }
  get updating() {
    return this.updatingExcludingEdits || this._pendingEdits.updating;
  }
  get updatingExcludingEdits() {
    return this.updatingHandles.updating;
  }
  get hasZ() {
    return this.store.featureStore.hasZ;
  }
  constructor(e3) {
    super(e3), this.tilesOfInterest = [], this.availability = 0, this._pendingTiles = /* @__PURE__ */ new Map(), this._pendingEdits = new r4(), this._pendingEditsAbortController = new AbortController();
  }
  initialize() {
    this._initializeFetchExtent(), this.updatingHandles.add(() => this._configuration, () => this.refresh()), this.updatingHandles.add(() => this.tilesOfInterest, (e3, i2) => {
      i(e3, i2, ({ id: e4 }, { id: t6 }) => e4 === t6) || this._process();
    }, U);
  }
  destroy() {
    this._pendingTiles.forEach((e3) => this._deletePendingTile(e3)), this._pendingTiles.clear(), this.store.destroy(), this.tilesOfInterest.length = 0, this._pendingEditsAbortController.abort(), this._pendingEditsAbortController = null;
  }
  refresh() {
    this.store.refresh(), this._pendingTiles.forEach((e3) => this._deletePendingTile(e3)), this._process();
  }
  applyEdits(e3) {
    this._pendingEdits.push(e3, async (e4) => {
      if (0 === e4.addedFeatures.length && 0 === e4.updatedFeatures.length && 0 === e4.deletedFeatures.length) return;
      for (const [, i2] of this._pendingTiles) i2.reset();
      const t6 = { ...e4, deletedFeatures: e4.deletedFeatures.map(({ objectId: e5, globalId: t7 }) => e5 && -1 !== e5 ? e5 : this._lookupObjectIdByGlobalId(t7)) };
      await this.updatingHandles.addPromise(this.store.processEdits(t6, (e5, t7) => this._queryFeaturesById(e5, t7), this._pendingEditsAbortController.signal)), this._processPendingTiles();
    });
  }
  _initializeFetchExtent() {
    if (!this.capabilities.query.supportsExtent || !g2(this.url)) return;
    const e3 = j2(async (e4) => {
      var _a;
      try {
        const t6 = await x3(this.url, new x({ where: "1=1", outSpatialReference: this.spatialReference, cacheHint: !!this.capabilities.query.supportsCacheHint || void 0 }), { query: this._configuration.customParameters, signal: e4 });
        this.store.extent = w2.fromJSON((_a = t6.data) == null ? void 0 : _a.extent);
      } catch (t6) {
        w(t6), s.getLogger(this.declaredClass).warn("Failed to fetch data extent", t6);
      }
    });
    this.updatingHandles.addPromise(e3.promise.then(() => this._process())), this.handles.add(n(() => e3.abort()));
  }
  get debugInfo() {
    return { numberOfFeatures: this.store.featureStore.numFeatures, tilesOfInterest: this.tilesOfInterest, pendingTiles: Array.from(this._pendingTiles.values()).map((e3) => e3.debugInfo), storedTiles: this.store.debugInfo };
  }
  _process() {
    this._markTilesNotAlive(), this._createPendingTiles(), this._deletePendingTiles(), this._processPendingTiles();
  }
  _markTilesNotAlive() {
    for (const [, e3] of this._pendingTiles) e3.alive = false;
  }
  _createPendingTiles() {
    const e3 = this._collectMissingTilesInfo();
    if (this._setAvailability(t(e3) ? 1 : e3.coveredArea / e3.fullArea), !t(e3)) for (const { data: t6, resolution: i2 } of e3.missingTiles) {
      const e4 = this._pendingTiles.get(t6.id);
      e4 ? (e4.resolution = i2, e4.alive = true) : this._createPendingTile(t6, i2);
    }
  }
  _collectMissingTilesInfo() {
    let e3 = null;
    for (let t6 = this.tilesOfInterest.length - 1; t6 >= 0; t6--) {
      const i2 = this.tilesOfInterest[t6], s3 = this.store.process(i2, (e4, t7) => this._verifyTileComplexity(e4, t7));
      t(e3) ? e3 = s3 : e3.prepend(s3);
    }
    return e3;
  }
  _deletePendingTiles() {
    for (const [, e3] of this._pendingTiles) e3.alive || this._deletePendingTile(e3);
  }
  _processPendingTiles() {
    const e3 = { fetchCount: (e4, t6) => this._fetchCount(e4, t6), fetchFeatures: (e4, t6, i2) => this._fetchFeatures(e4, t6, i2), finish: (e4, t6) => this._finishPendingTile(e4, t6), resume: () => this._processPendingTiles() };
    if (this._ensureFetchAllCounts(e3)) for (const [, t6] of this._pendingTiles) this._verifyTileComplexity(this.store.getFeatureCount(t6.data), t6.resolution) && this.updatingHandles.addPromise(t6.process(e3));
  }
  _verifyTileComplexity(e3, t6) {
    return this._verifyVertexComplexity(e3) && this._verifyFeatureDensity(e3, t6);
  }
  _verifyVertexComplexity(e3) {
    return e3 * this._minimumVerticesPerFeature < x4;
  }
  _verifyFeatureDensity(e3, t6) {
    if (t(this.tileInfo)) return false;
    const i2 = this.tileSize * t6;
    return e3 * (j4 / (i2 * i2)) < w3;
  }
  _ensureFetchAllCounts(e3) {
    let t6 = true;
    for (const [, i2] of this._pendingTiles) i2.state.type < u2.FETCHED_COUNT && this.updatingHandles.addPromise(i2.process(e3)), i2.state.type <= u2.FETCH_COUNT && (t6 = false);
    return t6;
  }
  _finishPendingTile(e3, t6) {
    this.store.add(e3.data, t6), this._deletePendingTile(e3), this._updateAvailability();
  }
  _updateAvailability() {
    const e3 = this._collectMissingTilesInfo();
    this._setAvailability(t(e3) ? 1 : e3.coveredArea / e3.fullArea);
  }
  _setAvailability(e3) {
    this._set("availability", e3);
  }
  _createPendingTile(e3, t6) {
    const i2 = new o2(e3, t6);
    return this._pendingTiles.set(e3.id, i2), i2;
  }
  _deletePendingTile(e3) {
    e3.reset(), this._pendingTiles.delete(e3.data.id);
  }
  async _fetchCount(e3, t6) {
    return this.store.fetchCount(e3.data, this.url, this._createCountQuery(e3), { query: this.customParameters, timeout: S2, signal: t6 });
  }
  async _fetchFeatures(e3, t6, i2) {
    let s3 = 0;
    const r5 = [];
    let o3 = 0, n4 = t6;
    for (; ; ) {
      const a3 = this._createFeaturesQuery(e3), l2 = this._setPagingParameters(a3, s3, n4), { features: c5, exceededTransferLimit: d3 } = await this._queryFeatures(a3, i2);
      l2 && (s3 += e2(a3.num)), o3 += c5.length;
      for (const e4 of c5) r5.push(e4);
      if (n4 = t6 - o3, !l2 || !d3 || n4 <= 0) return r5;
    }
  }
  _filterProperties(e3) {
    return t(e3) ? { where: "1=1", gdbVersion: void 0, timeExtent: void 0 } : { where: e3.where || "1=1", timeExtent: e3.timeExtent, gdbVersion: e3.gdbVersion };
  }
  _lookupObjectIdByGlobalId(e3) {
    const t6 = this.globalIdField, i2 = this.objectIdField;
    if (t(t6)) throw new Error("Expected globalIdField to be defined");
    let s3 = null;
    if (this.store.featureStore.forEach((r5) => {
      e3 === r5.attributes[t6] && (s3 = r5.objectId ?? r5.attributes[i2]);
    }), t(s3)) throw new Error(`Expected to find a feature with globalId ${e3}`);
    return s3;
  }
  _queryFeaturesById(e3, t6) {
    const i2 = this._createFeaturesQuery();
    return i2.objectIds = e3, this._queryFeatures(i2, t6);
  }
  _queryFeatures(e3, t6) {
    return this.capabilities.query.supportsFormatPBF ? this._queryFeaturesPBF(e3, t6) : this._queryFeaturesJSON(e3, t6);
  }
  async _queryFeaturesPBF(e3, t6) {
    const { sourceSpatialReference: i2 } = this, { data: s3 } = await f4(this.url, e3, new a2({ sourceSpatialReference: i2 }), { query: this._configuration.customParameters, timeout: S2, signal: t6 });
    return ht(s3);
  }
  async _queryFeaturesJSON(e3, t6) {
    const { sourceSpatialReference: i2 } = this, { data: s3 } = await c3(this.url, e3, i2, { query: this._configuration.customParameters, timeout: S2, signal: t6 });
    return at(s3, this.objectIdField);
  }
  _createCountQuery(e3) {
    const t6 = this._createBaseQuery(e3);
    return this.capabilities.query.supportsCacheHint && (t6.cacheHint = true), t6;
  }
  _createFeaturesQuery(e3 = null) {
    const t6 = this._createBaseQuery(e3);
    return t6.outFields = this.globalIdField ? [this.globalIdField, this.objectIdField] : [this.objectIdField], t6.returnGeometry = true, r(e3) && (this.capabilities.query.supportsResultType ? t6.resultType = "tile" : this.capabilities.query.supportsCacheHint && (t6.cacheHint = true)), t6;
  }
  _createBaseQuery(e3) {
    const t6 = new x({ returnZ: this.hasZ, returnM: false, geometry: r(this.tileInfo) && r(e3) ? f3(e3.data.extent, this.tileInfo.spatialReference) : void 0 }), i2 = this._configuration.filter;
    return r(i2) && (t6.where = i2.where, t6.gdbVersion = i2.gdbVersion, t6.timeExtent = i2.timeExtent), t6.outSpatialReference = this.spatialReference, t6;
  }
  _setPagingParameters(e3, t6, i2) {
    if (!this.capabilities.query.supportsPagination) return false;
    const { supportsMaxRecordCountFactor: s3, supportsCacheHint: r5, tileMaxRecordCount: o3, maxRecordCount: n4, supportsResultType: a3 } = this.capabilities.query, l2 = s3 ? x.MAX_MAX_RECORD_COUNT_FACTOR : 1, u3 = l2 * ((a3 || r5) && o3 ? o3 : n4 || E2);
    return e3.start = t6, s3 ? (e3.maxRecordCountFactor = Math.min(l2, Math.ceil(i2 / u3)), e3.num = Math.min(i2, e3.maxRecordCountFactor * u3)) : e3.num = Math.min(i2, u3), true;
  }
};
e([y({ constructOnly: true })], C.prototype, "url", void 0), e([y({ constructOnly: true })], C.prototype, "objectIdField", void 0), e([y({ constructOnly: true })], C.prototype, "globalIdField", void 0), e([y({ constructOnly: true })], C.prototype, "capabilities", void 0), e([y({ constructOnly: true })], C.prototype, "sourceSpatialReference", void 0), e([y({ constructOnly: true })], C.prototype, "spatialReference", void 0), e([y({ constructOnly: true })], C.prototype, "store", void 0), e([y({ readOnly: true })], C.prototype, "_minimumVerticesPerFeature", null), e([y()], C.prototype, "filter", null), e([y()], C.prototype, "customParameters", null), e([y({ readOnly: true })], C.prototype, "_configuration", null), e([y()], C.prototype, "tileInfo", null), e([y()], C.prototype, "tileSize", null), e([y()], C.prototype, "tilesOfInterest", void 0), e([y({ readOnly: true })], C.prototype, "updating", null), e([y({ readOnly: true })], C.prototype, "updatingExcludingEdits", null), e([y({ readOnly: true })], C.prototype, "availability", void 0), e([y()], C.prototype, "hasZ", null), C = e([a("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiledFetcher")], C);
var E2 = 2e3;
var S2 = 6e5;
var x4 = 1e6;
var j4 = 25;
var w3 = 1;

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTileCache.js
var t5 = class {
  constructor() {
    this._store = /* @__PURE__ */ new Map(), this._byteSize = 0;
  }
  set(t6, e3) {
    this.delete(t6), this._store.set(t6, e3), this._byteSize += e3.byteSize;
  }
  delete(t6) {
    const e3 = this._store.get(t6);
    return !!this._store.delete(t6) && (null != e3 && (this._byteSize -= e3.byteSize), true);
  }
  get(t6) {
    return this._used(t6), this._store.get(t6);
  }
  has(t6) {
    return this._used(t6), this._store.has(t6);
  }
  clear() {
    this._store.clear();
  }
  applyByteSizeLimit(t6, e3) {
    for (const [s3, r5] of this._store) {
      if (this._byteSize <= t6) break;
      this.delete(s3), e3(r5);
    }
  }
  values() {
    return this._store.values();
  }
  [Symbol.iterator]() {
    return this._store[Symbol.iterator]();
  }
  _used(t6) {
    const e3 = this._store.get(t6);
    e3 && (this._store.delete(t6), this._store.set(t6, e3));
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceTileStore.js
var v2 = class extends v {
  constructor(e3) {
    super(e3), this.tileInfo = null, this.extent = null, this.maximumByteSize = 10 * s2.MEGABYTES, this._tileBounds = new o(), this._tiles = new t5(), this._refCounts = /* @__PURE__ */ new Map(), this._tileFeatureCounts = /* @__PURE__ */ new Map(), this._tmpBoundingRect = u();
  }
  add(e3, t6) {
    const s3 = [];
    for (const i2 of t6) this._referenceFeature(i2.objectId) === x5.ADDED && s3.push(i2);
    this._addTileStorage(e3, new Set(t6.map((e4) => e4.objectId)), C2(t6)), this.featureStore.addMany(s3), this._tiles.applyByteSizeLimit(this.maximumByteSize, (e4) => this._removeTileStorage(e4));
  }
  destroy() {
    this.clear(), this._tileFeatureCounts.clear();
  }
  clear() {
    this.featureStore.clear(), this._tileBounds.clear(), this._tiles.clear(), this._refCounts.clear();
  }
  refresh() {
    this.clear(), this._tileFeatureCounts.clear();
  }
  processEdits(e3, t6, s3) {
    return this._processEditsDelete(e3.deletedFeatures.concat(e3.updatedFeatures)), this._processEditsRefetch(e3.addedFeatures.concat(e3.updatedFeatures), t6, s3);
  }
  _addTileStorage(e3, t6, s3) {
    const i2 = e3.id;
    this._tiles.set(i2, new T2(e3, t6, s3)), this._tileBounds.set(i2, e3.extent), this._tileFeatureCounts.set(i2, t6.size);
  }
  _remove({ id: e3 }) {
    const t6 = this._tiles.get(e3);
    t6 && this._removeTileStorage(t6);
  }
  _removeTileStorage(e3) {
    const t6 = [];
    for (const i2 of e3.objectIds) this._unreferenceFeature(i2) === x5.REMOVED && t6.push(i2);
    this.featureStore.removeManyById(t6);
    const s3 = e3.data.id;
    this._tiles.delete(s3), this._tileBounds.delete(s3);
  }
  _processEditsDelete(e3) {
    this.featureStore.removeManyById(e3);
    for (const [, t6] of this._tiles) {
      for (const s3 of e3) t6.objectIds.delete(s3);
      this._tileFeatureCounts.set(t6.data.id, t6.objectIds.size);
    }
    for (const t6 of e3) this._refCounts.delete(t6);
  }
  async _processEditsRefetch(e3, t6, s3) {
    const i2 = (await t6(e3, s3)).features, { hasZ: r5, hasM: n4 } = this.featureStore;
    for (const l2 of i2) {
      const e4 = It(this._tmpBoundingRect, l2.geometry, r5, n4);
      t(e4) || this._tileBounds.forEachInBounds(e4, (e5) => {
        const t7 = this._tiles.get(e5);
        this.featureStore.add(l2);
        const s4 = l2.objectId;
        t7.objectIds.has(s4) || (t7.objectIds.add(s4), this._referenceFeature(s4), this._tileFeatureCounts.set(t7.data.id, t7.objectIds.size));
      });
    }
  }
  process(e3, t6 = () => true) {
    if (t(this.tileInfo) || !e3.extent || r(this.extent) && !E(c(this.extent, this._tmpBoundingRect), e3.extent)) return new I(e3);
    if (this._tiles.has(e3.id)) return new I(e3);
    const s3 = this._createTileTree(e3, this.tileInfo);
    return this._simplify(s3, t6, null, 0, 1), this._collectMissingTiles(e3, s3, this.tileInfo);
  }
  get debugInfo() {
    return Array.from(this._tiles.values()).map(({ data: e3 }) => ({ data: e3, featureCount: this._tileFeatureCounts.get(e3.id) || 0 }));
  }
  getFeatureCount(e3) {
    return this._tileFeatureCounts.get(e3.id) ?? 0;
  }
  async fetchCount(e3, t6, s3, i2) {
    const r5 = this._tileFeatureCounts.get(e3.id);
    if (null != r5) return r5;
    const o3 = await S(t6, s3, i2);
    return this._tileFeatureCounts.set(e3.id, o3.data.count), o3.data.count;
  }
  _createTileTree(e3, t6) {
    const s3 = new F(e3.level, e3.row, e3.col);
    return t6.updateTileInfo(s3, j3.ExtrapolateOptions.POWER_OF_TWO), this._tileBounds.forEachInBounds(e3.extent, (i2) => {
      var _a;
      const r5 = (_a = this._tiles.get(i2)) == null ? void 0 : _a.data;
      r5 && this._tilesAreRelated(e3, r5) && this._populateChildren(s3, r5, t6, this._tileFeatureCounts.get(r5.id) || 0);
    }), s3;
  }
  _tilesAreRelated(e3, t6) {
    if (!e3 || !t6) return false;
    if (e3.level === t6.level) return e3.row === t6.row && e3.col === t6.col;
    const s3 = e3.level < t6.level, i2 = s3 ? e3 : t6, r5 = s3 ? t6 : e3, o3 = 1 << r5.level - i2.level;
    return Math.floor(r5.row / o3) === i2.row && Math.floor(r5.col / o3) === i2.col;
  }
  _populateChildren(e3, t6, s3, i2) {
    const r5 = t6.level - e3.level - 1;
    if (r5 < 0) return void (e3.isLeaf = true);
    const o3 = t6.row >> r5, l2 = t6.col >> r5, a3 = e3.row << 1, c5 = l2 - (e3.col << 1) + (o3 - a3 << 1), h = e3.children[c5];
    if (r(h)) this._populateChildren(h, t6, s3, i2);
    else {
      const r6 = new F(e3.level + 1, o3, l2);
      s3.updateTileInfo(r6, j3.ExtrapolateOptions.POWER_OF_TWO), e3.children[c5] = r6, this._populateChildren(r6, t6, s3, i2);
    }
  }
  _simplify(e3, t6, s3, i2, r5) {
    const o3 = r5 * r5;
    if (e3.isLeaf) return t6(this.getFeatureCount(e3), r5) ? 0 : (this._remove(e3), r(s3) && (s3.children[i2] = null), o3);
    const l2 = r5 / 2, a3 = l2 * l2;
    let c5 = 0;
    for (let h = 0; h < e3.children.length; h++) {
      const s4 = e3.children[h];
      c5 += r(s4) ? this._simplify(s4, t6, e3, h, l2) : a3;
    }
    return 0 === c5 ? this._mergeChildren(e3) : 1 - c5 / o3 < w4 && (this._purge(e3), r(s3) && (s3.children[i2] = null), c5 = o3), c5;
  }
  _mergeChildren(e3) {
    const t6 = /* @__PURE__ */ new Set();
    let s3 = 0;
    this._forEachLeaf(e3, (e4) => {
      const i2 = this._tiles.get(e4.id);
      if (i2) {
        s3 += i2.byteSize;
        for (const e5 of i2.objectIds) t6.has(e5) || (t6.add(e5), this._referenceFeature(e5));
        this._remove(e4);
      }
    }), this._addTileStorage(e3, t6, s3), e3.isLeaf = true, e3.children[0] = e3.children[1] = e3.children[2] = e3.children[3] = null, this._tileFeatureCounts.set(e3.id, t6.size);
  }
  _forEachLeaf(e3, t6) {
    for (const s3 of e3.children) t(s3) || (s3.isLeaf ? t6(s3) : this._forEachLeaf(s3, t6));
  }
  _purge(e3) {
    if (!t(e3)) if (e3.isLeaf) this._remove(e3);
    else for (let t6 = 0; t6 < e3.children.length; t6++) {
      const s3 = e3.children[t6];
      this._purge(s3), e3.children[t6] = null;
    }
  }
  _collectMissingTiles(e3, t6, s3) {
    const i2 = new j5(s3, e3, this.extent);
    return this._collectMissingTilesRecurse(t6, i2, 1), i2.info;
  }
  _collectMissingTilesRecurse(e3, t6, s3) {
    if (e3.isLeaf) return;
    if (!e3.hasChildren) return void t6.addMissing(e3.level, e3.row, e3.col, s3);
    const i2 = s3 / 2;
    for (let r5 = 0; r5 < e3.children.length; r5++) {
      const s4 = e3.children[r5];
      t(s4) ? t6.addMissing(e3.level + 1, (e3.row << 1) + ((2 & r5) >> 1), (e3.col << 1) + (1 & r5), i2) : this._collectMissingTilesRecurse(s4, t6, i2);
    }
  }
  _referenceFeature(e3) {
    const t6 = (this._refCounts.get(e3) || 0) + 1;
    return this._refCounts.set(e3, t6), 1 === t6 ? x5.ADDED : x5.UNCHANGED;
  }
  _unreferenceFeature(e3) {
    const t6 = (this._refCounts.get(e3) || 0) - 1;
    return 0 === t6 ? (this._refCounts.delete(e3), x5.REMOVED) : (t6 > 0 && this._refCounts.set(e3, t6), x5.UNCHANGED);
  }
  get test() {
    return { tiles: Array.from(this._tiles.values()).map((e3) => `${e3.data.id}:[${Array.from(e3.objectIds)}]`), featureReferences: Array.from(this._refCounts.keys()).map((e3) => `${e3}:${this._refCounts.get(e3)}`) };
  }
};
function C2(e3) {
  return e3.reduce((e4, t6) => e4 + E3(t6), 0);
}
function E3(e3) {
  return 32 + S3(e3.geometry) + t4(e3.attributes);
}
function S3(e3) {
  if (t(e3)) return 0;
  const t6 = c4(e3.lengths, 4);
  return 32 + c4(e3.coords, 8) + t6;
}
e([y({ constructOnly: true })], v2.prototype, "featureStore", void 0), e([y()], v2.prototype, "tileInfo", void 0), e([y()], v2.prototype, "extent", void 0), e([y()], v2.prototype, "maximumByteSize", void 0), v2 = e([a("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTileStore")], v2);
var T2 = class {
  constructor(e3, t6, s3) {
    this.data = e3, this.objectIds = t6, this.byteSize = s3;
  }
};
var F = class {
  constructor(e3, t6, s3) {
    this.level = e3, this.row = t6, this.col = s3, this.isLeaf = false, this.extent = null, this.children = [null, null, null, null];
  }
  get hasChildren() {
    return !this.isLeaf && (r(this.children[0]) || r(this.children[1]) || r(this.children[2]) || r(this.children[3]));
  }
};
var I = class {
  constructor(e3, t6 = []) {
    this.missingTiles = t6, this.fullArea = 0, this.coveredArea = 0, this.fullArea = y2(e3.extent), this.coveredArea = this.fullArea;
  }
  prepend(e3) {
    this.missingTiles = e3.missingTiles.concat(this.missingTiles), this.coveredArea += e3.coveredArea, this.fullArea += e3.fullArea;
  }
};
var j5 = class {
  constructor(e3, t6, s3) {
    this._tileInfo = e3, this._extent = null, this.info = new I(t6), r(s3) && (this._extent = c(s3));
  }
  addMissing(e3, t6, s3, i2) {
    const r5 = new t3(null, e3, t6, s3);
    this._tileInfo.updateTileInfo(r5, j3.ExtrapolateOptions.POWER_OF_TWO), t(r5.extent) || r(this._extent) && !E(this._extent, r5.extent) || (this.info.missingTiles.push({ data: r5, resolution: i2 }), this.info.coveredArea -= y2(r5.extent));
  }
};
var w4 = 0.18751;
var x5;
!function(e3) {
  e3[e3.ADDED = 0] = "ADDED", e3[e3.REMOVED = 1] = "REMOVED", e3[e3.UNCHANGED = 2] = "UNCHANGED";
}(x5 || (x5 = {}));

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceSnappingSourceWorker.js
var w5 = class extends n2.EventedAccessor {
  constructor() {
    super(...arguments), this._isInitializing = true, this.remoteClient = null, this._whenSetup = A(), this._elevationAligner = r2(), this._elevationFilter = r3(), this._symbologyCandidatesFetcher = n3(), this._handles = new t2(), this._updatingHandles = new c2(), this._editsUpdatingHandles = new c2(), this._pendingApplyEdits = /* @__PURE__ */ new Map(), this._alignPointsInFeatures = async (e3, t6) => {
      const i2 = { points: e3 }, s3 = await this.remoteClient.invoke("alignElevation", i2, { signal: t6 });
      return f(t6), s3;
    }, this._getSymbologyCandidates = async (e3, t6) => {
      const i2 = { candidates: e3, spatialReference: this._spatialReference.toJSON() }, s3 = await this.remoteClient.invoke("getSymbologyCandidates", i2, { signal: t6 });
      return f(t6), s3;
    };
  }
  get updating() {
    return this.updatingExcludingEdits || this._editsUpdatingHandles.updating || this._featureFetcher.updating;
  }
  get updatingExcludingEdits() {
    return this._featureFetcher.updatingExcludingEdits || this._isInitializing || this._updatingHandles.updating;
  }
  destroy() {
    var _a, _b, _c, _d;
    (_a = this._featureFetcher) == null ? void 0 : _a.destroy(), (_b = this._queryEngine) == null ? void 0 : _b.destroy(), (_c = this._featureStore) == null ? void 0 : _c.clear(), (_d = this._handles) == null ? void 0 : _d.destroy();
  }
  async setup(e3) {
    if (this.destroyed) return { result: {} };
    const { geometryType: t6, objectIdField: i2, timeInfo: r5, fields: n4 } = e3.serviceInfo, { hasZ: o3 } = e3, d3 = f2.fromJSON(e3.spatialReference);
    this._spatialReference = d3, this._featureStore = new g({ ...e3.serviceInfo, hasZ: o3, hasM: false }), this._queryEngine = new ee({ spatialReference: e3.spatialReference, featureStore: this._featureStore, geometryType: t6, fields: n4, hasZ: o3, hasM: false, objectIdField: i2, timeInfo: r5 }), this._featureFetcher = new C({ store: new v2({ featureStore: this._featureStore }), url: e3.serviceInfo.url, objectIdField: e3.serviceInfo.objectIdField, globalIdField: e3.serviceInfo.globalIdField, capabilities: e3.serviceInfo.capabilities, spatialReference: d3, sourceSpatialReference: f2.fromJSON(e3.serviceInfo.spatialReference) });
    const p = "3d" === e3.configuration.viewType;
    return this._elevationAligner = r2(p, { elevationInfo: r(e3.elevationInfo) ? x2.fromJSON(e3.elevationInfo) : null, alignPointsInFeatures: this._alignPointsInFeatures, spatialReference: d3 }), this._elevationFilter = r3(p), this._handles.add([l(() => this._featureFetcher.availability, (e4) => this.emit("notify-availability", { availability: e4 }), U), l(() => this.updating, () => this._notifyUpdating())]), this._whenSetup.resolve(), this._isInitializing = false, this.configure(e3.configuration);
  }
  async configure(e3) {
    return await this._updatingHandles.addPromise(this._whenSetup.promise), this._updateFeatureFetcherConfiguration(e3), { result: {} };
  }
  async fetchCandidates(e3, t6) {
    await this._whenSetup.promise, f(t6);
    const i2 = E4(e3), r5 = r(t6) ? t6.signal : null, a3 = await this._queryEngine.executeQueryForSnapping(i2, r5);
    f(r5);
    const o3 = await this._elevationAligner.alignCandidates(a3.candidates, r5);
    f(r5);
    const l2 = await this._symbologyCandidatesFetcher.fetch(o3, r5);
    f(r5);
    const d3 = 0 === l2.length ? o3 : o3.concat(l2);
    return { result: { candidates: this._elevationFilter.filter(i2, d3) } };
  }
  async updateTiles(e3, t6) {
    return await this._updatingHandles.addPromise(this._whenSetup.promise), f(t6), this._featureFetcher.tileSize = e3.tileSize, this._featureFetcher.tilesOfInterest = e3.tiles, this._featureFetcher.tileInfo = r(e3.tileInfo) ? j3.fromJSON(e3.tileInfo) : null, j6;
  }
  async refresh(e3, t6) {
    return await this._updatingHandles.addPromise(this._whenSetup.promise), f(t6), this._featureFetcher.refresh(), j6;
  }
  async whenNotUpdating(e3, t6) {
    return await this._updatingHandles.addPromise(this._whenSetup.promise), f(t6), await j(() => !this.updatingExcludingEdits, t6), f(t6), j6;
  }
  async getDebugInfo(e3, t6) {
    return f(t6), { result: this._featureFetcher.debugInfo };
  }
  async beginApplyEdits(e3, t6) {
    this._updatingHandles.addPromise(this._whenSetup.promise), f(t6);
    const i2 = A();
    return this._pendingApplyEdits.set(e3.id, i2), this._featureFetcher.applyEdits(i2.promise), this._editsUpdatingHandles.addPromise(i2.promise), j6;
  }
  async endApplyEdits(e3, t6) {
    const i2 = this._pendingApplyEdits.get(e3.id);
    return i2 && i2.resolve(e3.edits), f(t6), j6;
  }
  async notifyElevationSourceChange(e3, t6) {
    return this._elevationAligner.notifyElevationSourceChange(), j6;
  }
  async notifySymbologyChange(e3, t6) {
    return this._symbologyCandidatesFetcher.notifySymbologyChange(), j6;
  }
  async setSymbologySnappingSupported(e3) {
    return this._symbologyCandidatesFetcher = n3(e3, this._getSymbologyCandidates), j6;
  }
  _updateFeatureFetcherConfiguration(e3) {
    this._featureFetcher.filter = r(e3.filter) ? x.fromJSON(e3.filter) : null, this._featureFetcher.customParameters = e3.customParameters;
  }
  _notifyUpdating() {
    this.emit("notify-updating", { updating: this.updating });
  }
};
e([y({ readOnly: true })], w5.prototype, "updating", null), e([y({ readOnly: true })], w5.prototype, "updatingExcludingEdits", null), e([y()], w5.prototype, "_isInitializing", void 0), w5 = e([a("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceSnappingSourceWorker")], w5);
var b = w5;
function E4(e3) {
  return { point: e3.point, mode: e3.mode, distance: e3.distance, types: e3.types, query: r(e3.filter) ? e3.filter : { where: "1=1" } };
}
var j6 = { result: {} };
export {
  b as default
};
//# sourceMappingURL=FeatureServiceSnappingSourceWorker-BD5TAARB.js.map
