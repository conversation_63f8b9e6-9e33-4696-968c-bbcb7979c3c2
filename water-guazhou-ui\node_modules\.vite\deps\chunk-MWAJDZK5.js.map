{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/features/support/Tile.js", "../../@arcgis/core/views/2d/layers/features/support/TileStore.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../../core/ArrayPool.js\";import e from\"../../../../../geometry/Extent.js\";import{create as i}from\"../../../../../geometry/support/aaBoundingRect.js\";import s from\"../../../../../rest/support/QuantizationParameters.js\";import o from\"../../../tiling/TileKey.js\";class n{constructor(t,e){this.key=new o(0,0,0,0),this.bounds=i(),this.objectIds=new Set,this.key.set(e);const s=t.getLODInfoAt(this.key);this.tileInfoView=t,this.tileInfoView.getTileBounds(this.bounds,this.key,!0),this.resolution=s.resolution,this.scale=s.scale,this.level=s.level}get id(){return this.key.id}get extent(){return e.fromBounds(this.bounds,this.tileInfoView.tileInfo.spatialReference)}get transform(){return{originPosition:\"upperLeft\",scale:[this.resolution,this.resolution],translate:[this.bounds[0],this.bounds[3]]}}createChildTiles(){const e=this.key.getChildKeys(),i=t.acquire();for(let t=0;t<e.length;t++)i[t]=new n(this.tileInfoView,e[t]);return i}getQuantizationParameters(){return s.fromJSON({mode:\"view\",originPosition:\"upperLeft\",tolerance:this.resolution,extent:{xmin:this.bounds[0],ymin:this.bounds[1],xmax:this.bounds[2],ymax:this.bounds[3],spatialReference:this.tileInfoView.tileInfo.spatialReference}})}}export{n as Tile};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/Evented.js\";import has from\"../../../../../core/has.js\";import{r as t}from\"../../../../../chunks/rbush.js\";import{Tile as s}from\"./Tile.js\";import i from\"../../../tiling/TileCoverage.js\";import o from\"../../../tiling/TileKey.js\";const h={added:[],removed:[]},n=new Set,r=new o(0,0,0,0);class d extends e{constructor(e){super(),this._tiles=new Map,this._index=t(9,has(\"esri-csp-restrictions\")?e=>({minX:e.bounds[0],minY:e.bounds[1],maxX:e.bounds[2],maxY:e.bounds[3]}):[\".bounds[0]\",\".bounds[1]\",\".bounds[2]\",\".bounds[3]\"]),this.tiles=[],this.tileScheme=e}destroy(){this.clear()}clear(){this.tiles.length=0,this._tiles.clear(),this._index.clear()}has(e){return this._tiles.has(e)}get(e){return this._tiles.get(e)}boundsIntersections(e){return this._index.search({minX:e[0],minY:e[1],maxX:e[2],maxY:e[3]})}updateTiles(e){const t={added:[],removed:[]};for(const i of e.added)if(!this.has(i)){const e=new s(this.tileScheme,i);this._tiles.set(i,e),this._index.insert(e),t.added.push(e)}for(const s of e.removed)if(this.has(s)){const e=this.get(s);this._tiles.delete(s),this._index.remove(e),t.removed.push(e)}this.tiles.length=0,this._tiles.forEach((e=>this.tiles.push(e))),(t.added.length||t.removed.length)&&this.emit(\"update\",t)}setViewState(e){const t=this.tileScheme.getTileCoverage(e,0);if(!t)return;const{spans:o,lodInfo:d}=t,{level:l}=d;if(o.length>0)for(const{row:i,colFrom:a,colTo:m}of o)for(let e=a;e<=m;e++){const t=r.set(l,i,d.normalizeCol(e),d.getWorldForColumn(e)).id;if(n.add(t),!this.has(t)){const e=new s(this.tileScheme,t);this._tiles.set(t,e),this._index.insert(e),this.tiles.push(e),h.added.push(e)}}for(let s=this.tiles.length-1;s>=0;s--){const e=this.tiles[s];n.has(e.id)||(this._tiles.delete(e.id),this.tiles.splice(s,1),this._index.remove(e),h.removed.push(e))}(h.added.length||h.removed.length)&&this.emit(\"update\",h),i.pool.release(t),n.clear(),h.added.length=0,h.removed.length=0}}export{d as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2R,IAAMA,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,MAAI,IAAI,EAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,SAAO,EAAE,GAAE,KAAK,YAAU,oBAAI,OAAI,KAAK,IAAI,IAAIA,EAAC;AAAE,UAAM,IAAED,GAAE,aAAa,KAAK,GAAG;AAAE,SAAK,eAAaA,IAAE,KAAK,aAAa,cAAc,KAAK,QAAO,KAAK,KAAI,IAAE,GAAE,KAAK,aAAW,EAAE,YAAW,KAAK,QAAM,EAAE,OAAM,KAAK,QAAM,EAAE;AAAA,EAAK;AAAA,EAAC,IAAI,KAAI;AAAC,WAAO,KAAK,IAAI;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,EAAE,WAAW,KAAK,QAAO,KAAK,aAAa,SAAS,gBAAgB;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAM,EAAC,gBAAe,aAAY,OAAM,CAAC,KAAK,YAAW,KAAK,UAAU,GAAE,WAAU,CAAC,KAAK,OAAO,CAAC,GAAE,KAAK,OAAO,CAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,UAAMC,KAAE,KAAK,IAAI,aAAa,GAAEC,KAAE,EAAE,QAAQ;AAAE,aAAQF,KAAE,GAAEA,KAAEC,GAAE,QAAOD,KAAI,CAAAE,GAAEF,EAAC,IAAE,IAAI,GAAE,KAAK,cAAaC,GAAED,EAAC,CAAC;AAAE,WAAOE;AAAA,EAAC;AAAA,EAAC,4BAA2B;AAAC,WAAO,EAAE,SAAS,EAAC,MAAK,QAAO,gBAAe,aAAY,WAAU,KAAK,YAAW,QAAO,EAAC,MAAK,KAAK,OAAO,CAAC,GAAE,MAAK,KAAK,OAAO,CAAC,GAAE,MAAK,KAAK,OAAO,CAAC,GAAE,MAAK,KAAK,OAAO,CAAC,GAAE,kBAAiB,KAAK,aAAa,SAAS,iBAAgB,EAAC,CAAC;AAAA,EAAC;AAAC;;;ACAx7B,IAAM,IAAE,EAAC,OAAM,CAAC,GAAE,SAAQ,CAAC,EAAC;AAA5B,IAA8BC,KAAE,oBAAI;AAApC,IAAwC,IAAE,IAAI,EAAE,GAAE,GAAE,GAAE,CAAC;AAAE,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,GAAE,KAAK,SAAO,oBAAI,OAAI,KAAK,SAAO,EAAE,GAAE,IAAI,uBAAuB,IAAE,CAAAA,QAAI,EAAC,MAAKA,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,OAAO,CAAC,EAAC,KAAG,CAAC,cAAa,cAAa,cAAa,YAAY,CAAC,GAAE,KAAK,QAAM,CAAC,GAAE,KAAK,aAAWA;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,MAAM,SAAO,GAAE,KAAK,OAAO,MAAM,GAAE,KAAK,OAAO,MAAM;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,OAAO,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,OAAO,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,WAAO,KAAK,OAAO,OAAO,EAAC,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMC,KAAE,EAAC,OAAM,CAAC,GAAE,SAAQ,CAAC,EAAC;AAAE,eAAUC,MAAKF,GAAE,MAAM,KAAG,CAAC,KAAK,IAAIE,EAAC,GAAE;AAAC,YAAMF,KAAE,IAAID,GAAE,KAAK,YAAWG,EAAC;AAAE,WAAK,OAAO,IAAIA,IAAEF,EAAC,GAAE,KAAK,OAAO,OAAOA,EAAC,GAAEC,GAAE,MAAM,KAAKD,EAAC;AAAA,IAAC;AAAC,eAAU,KAAKA,GAAE,QAAQ,KAAG,KAAK,IAAI,CAAC,GAAE;AAAC,YAAMA,KAAE,KAAK,IAAI,CAAC;AAAE,WAAK,OAAO,OAAO,CAAC,GAAE,KAAK,OAAO,OAAOA,EAAC,GAAEC,GAAE,QAAQ,KAAKD,EAAC;AAAA,IAAC;AAAC,SAAK,MAAM,SAAO,GAAE,KAAK,OAAO,QAAS,CAAAA,OAAG,KAAK,MAAM,KAAKA,EAAC,CAAE,IAAGC,GAAE,MAAM,UAAQA,GAAE,QAAQ,WAAS,KAAK,KAAK,UAASA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAE;AAAC,UAAMC,KAAE,KAAK,WAAW,gBAAgBD,IAAE,CAAC;AAAE,QAAG,CAACC,GAAE;AAAO,UAAK,EAAC,OAAM,GAAE,SAAQE,GAAC,IAAEF,IAAE,EAAC,OAAMG,GAAC,IAAED;AAAE,QAAG,EAAE,SAAO,EAAE,YAAS,EAAC,KAAID,IAAE,SAAQG,IAAE,OAAM,EAAC,KAAI,EAAE,UAAQL,KAAEK,IAAEL,MAAG,GAAEA,MAAI;AAAC,YAAMC,KAAE,EAAE,IAAIG,IAAEF,IAAEC,GAAE,aAAaH,EAAC,GAAEG,GAAE,kBAAkBH,EAAC,CAAC,EAAE;AAAG,UAAGD,GAAE,IAAIE,EAAC,GAAE,CAAC,KAAK,IAAIA,EAAC,GAAE;AAAC,cAAMD,KAAE,IAAID,GAAE,KAAK,YAAWE,EAAC;AAAE,aAAK,OAAO,IAAIA,IAAED,EAAC,GAAE,KAAK,OAAO,OAAOA,EAAC,GAAE,KAAK,MAAM,KAAKA,EAAC,GAAE,EAAE,MAAM,KAAKA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAQ,IAAE,KAAK,MAAM,SAAO,GAAE,KAAG,GAAE,KAAI;AAAC,YAAMA,KAAE,KAAK,MAAM,CAAC;AAAE,MAAAD,GAAE,IAAIC,GAAE,EAAE,MAAI,KAAK,OAAO,OAAOA,GAAE,EAAE,GAAE,KAAK,MAAM,OAAO,GAAE,CAAC,GAAE,KAAK,OAAO,OAAOA,EAAC,GAAE,EAAE,QAAQ,KAAKA,EAAC;AAAA,IAAE;AAAC,KAAC,EAAE,MAAM,UAAQ,EAAE,QAAQ,WAAS,KAAK,KAAK,UAAS,CAAC,GAAE,EAAE,KAAK,QAAQC,EAAC,GAAEF,GAAE,MAAM,GAAE,EAAE,MAAM,SAAO,GAAE,EAAE,QAAQ,SAAO;AAAA,EAAC;AAAC;", "names": ["n", "t", "e", "i", "n", "e", "t", "i", "d", "l", "a"]}