import {
  a as a2,
  a2 as a3,
  c
} from "./chunk-24NZLSKM.js";
import {
  L,
  S as S2,
  m,
  u
} from "./chunk-RFYOGM4H.js";
import {
  S as S3,
  h
} from "./chunk-VNYCO3JG.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  s as s3
} from "./chunk-7SWS36OI.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  o as o3
} from "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  o as o2,
  s as s2
} from "./chunk-KUPAGB4V.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  S,
  T
} from "./chunk-HP475EI3.js";
import {
  C
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/popup/ExpressionInfo.js
var s4;
var p2 = s4 = class extends l {
  constructor(r3) {
    super(r3), this.name = null, this.title = null, this.expression = null, this.returnType = null;
  }
  clone() {
    return new s4({ name: this.name, title: this.title, expression: this.expression, returnType: this.returnType });
  }
};
e([y({ type: String, json: { write: true } })], p2.prototype, "name", void 0), e([y({ type: String, json: { write: true } })], p2.prototype, "title", void 0), e([y({ type: String, json: { write: true } })], p2.prototype, "expression", void 0), e([y({ type: ["string", "number"], json: { write: true } })], p2.prototype, "returnType", void 0), p2 = s4 = e([a("esri.popup.ExpressionInfo")], p2);
var i2 = p2;

// node_modules/@arcgis/core/popup/content/Content.js
var e2 = class extends l {
  constructor(r3) {
    super(r3), this.type = null;
  }
};
e([y({ type: ["attachments", "custom", "fields", "media", "text", "expression", "relationship"], readOnly: true, json: { read: false, write: true } })], e2.prototype, "type", void 0), e2 = e([a("esri.popup.content.Content")], e2);
var p3 = e2;

// node_modules/@arcgis/core/popup/content/AttachmentsContent.js
var s5;
var p4 = s5 = class extends p3 {
  constructor(t2) {
    super(t2), this.description = null, this.displayType = "auto", this.title = null, this.type = "attachments";
  }
  clone() {
    return new s5({ description: this.description, displayType: this.displayType, title: this.title });
  }
};
e([y({ type: String, json: { write: true } })], p4.prototype, "description", void 0), e([y({ type: ["auto", "preview", "list"], json: { write: true } })], p4.prototype, "displayType", void 0), e([y({ type: String, json: { write: true } })], p4.prototype, "title", void 0), e([y({ type: ["attachments"], readOnly: true, json: { read: false, write: true } })], p4.prototype, "type", void 0), p4 = s5 = e([a("esri.popup.content.AttachmentsContent")], p4);
var i3 = p4;

// node_modules/@arcgis/core/popup/content/CustomContent.js
var p5;
var c2 = p5 = class extends p3 {
  constructor(o5) {
    super(o5), this.creator = null, this.destroyer = null, this.outFields = null, this.type = "custom";
  }
  clone() {
    return new p5({ creator: this.creator, destroyer: this.destroyer, outFields: Array.isArray(this.outFields) ? p(this.outFields) : null });
  }
};
e([y()], c2.prototype, "creator", void 0), e([y()], c2.prototype, "destroyer", void 0), e([y()], c2.prototype, "outFields", void 0), e([y({ type: ["custom"], readOnly: true })], c2.prototype, "type", void 0), c2 = p5 = e([a("esri.popup.content.CustomContent")], c2);
var i4 = c2;

// node_modules/@arcgis/core/popup/ElementExpressionInfo.js
var s6;
var p6 = s6 = class extends l {
  constructor(r3) {
    super(r3), this.title = null, this.expression = null, this.returnType = "dictionary";
  }
  clone() {
    return new s6({ title: this.title, expression: this.expression });
  }
};
e([y({ type: String, json: { write: true } })], p6.prototype, "title", void 0), e([y({ type: String, json: { write: true } })], p6.prototype, "expression", void 0), e([y({ type: ["dictionary"], readOnly: true, json: { read: false, write: true } })], p6.prototype, "returnType", void 0), p6 = s6 = e([a("esri.popup.ElementExpressionInfo")], p6);
var i5 = p6;

// node_modules/@arcgis/core/popup/content/ExpressionContent.js
var p7;
var n = p7 = class extends p3 {
  constructor(o5) {
    super(o5), this.expressionInfo = null, this.type = "expression";
  }
  clone() {
    var _a;
    return new p7({ expressionInfo: (_a = this.expressionInfo) == null ? void 0 : _a.clone() });
  }
};
e([y({ type: i5, json: { write: true } })], n.prototype, "expressionInfo", void 0), e([y({ type: ["expression"], readOnly: true, json: { read: false, write: true } })], n.prototype, "type", void 0), n = p7 = e([a("esri.popup.content.ExpressionContent")], n);
var i6 = n;

// node_modules/@arcgis/core/core/date.js
var a4 = o2()({ shortDate: "short-date", shortDateShortTime: "short-date-short-time", shortDateShortTime24: "short-date-short-time-24", shortDateLongTime: "short-date-long-time", shortDateLongTime24: "short-date-long-time-24", shortDateLE: "short-date-le", shortDateLEShortTime: "short-date-le-short-time", shortDateLEShortTime24: "short-date-le-short-time-24", shortDateLELongTime: "short-date-le-long-time", shortDateLELongTime24: "short-date-le-long-time-24", longMonthDayYear: "long-month-day-year", longMonthDayYearShortTime: "long-month-day-year-short-time", longMonthDayYearShortTime24: "long-month-day-year-short-time-24", longMonthDayYearLongTime: "long-month-day-year-long-time", longMonthDayYearLongTime24: "long-month-day-year-long-time-24", dayShortMonthYear: "day-short-month-year", dayShortMonthYearShortTime: "day-short-month-year-short-time", dayShortMonthYearShortTime24: "day-short-month-year-short-time-24", dayShortMonthYearLongTime: "day-short-month-year-long-time", dayShortMonthYearLongTime24: "day-short-month-year-long-time-24", longDate: "long-date", longDateShortTime: "long-date-short-time", longDateShortTime24: "long-date-short-time-24", longDateLongTime: "long-date-long-time", longDateLongTime24: "long-date-long-time-24", longMonthYear: "long-month-year", shortMonthYear: "short-month-year", year: "year" });
var o4 = a4.toJSON.bind(a4);
var r2 = a4.fromJSON.bind(a4);

// node_modules/@arcgis/core/popup/support/FieldInfoFormat.js
var d;
var l2 = d = class extends l {
  constructor(t2) {
    super(t2), this.dateFormat = null, this.dateTimeFormatOptions = null, this.digitSeparator = false, this.places = null;
  }
  clone() {
    return new d({ dateFormat: this.dateFormat, digitSeparator: this.digitSeparator, places: this.places });
  }
  format(t2) {
    return this.dateFormat ? L(t2, { ...S2(this.dateFormat), ...this.dateTimeFormatOptions }) : m(t2, u(this));
  }
  formatRasterPixelValue(t2) {
    if (t2.includes("-")) return t2;
    let r3, o5;
    return t2.trim().includes(",") ? (r3 = ",", o5 = r3 + " ", this._formatDelimitedString(t2, r3, o5, this)) : t2.trim().includes(";") ? (r3 = ";", o5 = r3 + " ", this._formatDelimitedString(t2, r3, o5, this)) : t2.trim().includes(" ") ? (r3 = o5 = " ", this._formatDelimitedString(t2, r3, o5, this)) : this.format(Number(t2));
  }
  _formatDelimitedString(t2, r3, o5, e5) {
    return t2 && r3 && o5 && e5 ? t2.trim().split(r3).map((t3) => this.format(Number(t3))).join(o5) : t2;
  }
};
e([o3(a4)], l2.prototype, "dateFormat", void 0), e([y({ type: Object, json: { read: false } })], l2.prototype, "dateTimeFormatOptions", void 0), e([y({ type: Boolean, json: { write: true } })], l2.prototype, "digitSeparator", void 0), e([y({ type: T, json: { write: true } })], l2.prototype, "places", void 0), l2 = d = e([a("esri.popup.support.FieldInfoFormat")], l2);
var u2 = l2;

// node_modules/@arcgis/core/popup/FieldInfo.js
var a5;
var n2 = a5 = class extends l {
  constructor(t2) {
    super(t2), this.fieldName = null, this.format = null, this.isEditable = false, this.label = null, this.stringFieldOption = "text-box", this.statisticType = null, this.tooltip = null, this.visible = true;
  }
  clone() {
    return new a5({ fieldName: this.fieldName, format: this.format ? p(this.format) : null, isEditable: this.isEditable, label: this.label, stringFieldOption: this.stringFieldOption, statisticType: this.statisticType, tooltip: this.tooltip, visible: this.visible });
  }
};
e([y({ type: String, json: { write: true } })], n2.prototype, "fieldName", void 0), e([y({ type: u2, json: { write: true } })], n2.prototype, "format", void 0), e([y({ type: Boolean, json: { write: true, default: false } })], n2.prototype, "isEditable", void 0), e([y({ type: String, json: { write: true } })], n2.prototype, "label", void 0), e([o3(new s2({ richtext: "rich-text", textarea: "text-area", textbox: "text-box" }), { default: "text-box" })], n2.prototype, "stringFieldOption", void 0), e([y({ type: ["count", "sum", "min", "max", "avg", "stddev", "var"], json: { write: true } })], n2.prototype, "statisticType", void 0), e([y({ type: String, json: { write: true } })], n2.prototype, "tooltip", void 0), e([y({ type: Boolean, json: { write: true } })], n2.prototype, "visible", void 0), n2 = a5 = e([a("esri.popup.FieldInfo")], n2);
var c3 = n2;

// node_modules/@arcgis/core/popup/content/FieldsContent.js
var n3;
var l3 = n3 = class extends p3 {
  constructor(t2) {
    super(t2), this.attributes = null, this.description = null, this.fieldInfos = null, this.title = null, this.type = "fields";
  }
  writeFieldInfos(t2, o5) {
    o5.fieldInfos = t2 && t2.map((t3) => t3.toJSON());
  }
  clone() {
    return new n3(p({ attributes: this.attributes, description: this.description, fieldInfos: this.fieldInfos, title: this.title }));
  }
};
e([y({ type: Object, json: { write: true } })], l3.prototype, "attributes", void 0), e([y({ type: String, json: { write: true } })], l3.prototype, "description", void 0), e([y({ type: [c3] })], l3.prototype, "fieldInfos", void 0), e([r("fieldInfos")], l3.prototype, "writeFieldInfos", null), e([y({ type: String, json: { write: true } })], l3.prototype, "title", void 0), e([y({ type: ["fields"], readOnly: true, json: { read: false, write: true } })], l3.prototype, "type", void 0), l3 = n3 = e([a("esri.popup.content.FieldsContent")], l3);
var c4 = l3;

// node_modules/@arcgis/core/popup/content/mixins/MediaInfo.js
var s7 = class extends l {
  constructor(t2) {
    super(t2), this.altText = null, this.caption = "", this.title = "", this.type = null;
  }
};
e([y({ type: String, json: { write: true } })], s7.prototype, "altText", void 0), e([y({ type: String, json: { write: true } })], s7.prototype, "caption", void 0), e([y({ type: String, json: { write: true } })], s7.prototype, "title", void 0), e([y({ type: ["image", "bar-chart", "column-chart", "line-chart", "pie-chart"], readOnly: true, json: { read: false, write: true } })], s7.prototype, "type", void 0), s7 = e([a("esri.popup.content.mixins.MediaInfo")], s7);
var p8 = s7;

// node_modules/@arcgis/core/popup/content/support/ChartMediaInfoValueSeries.js
var s8;
var p9 = s8 = class extends v {
  constructor(o5) {
    super(o5), this.fieldName = null, this.tooltip = null, this.value = null;
  }
  clone() {
    return new s8({ fieldName: this.fieldName, tooltip: this.tooltip, value: this.value });
  }
};
e([y()], p9.prototype, "fieldName", void 0), e([y()], p9.prototype, "tooltip", void 0), e([y()], p9.prototype, "value", void 0), p9 = s8 = e([a("esri.popup.content.support.ChartMediaInfoValueSeries")], p9);
var i7 = p9;

// node_modules/@arcgis/core/popup/content/support/ChartMediaInfoValue.js
var p10;
var l4 = p10 = class extends l {
  constructor(o5) {
    super(o5), this.fields = [], this.normalizeField = null, this.series = [], this.tooltipField = null;
  }
  clone() {
    return new p10({ fields: p(this.fields), normalizeField: this.normalizeField, tooltipField: this.tooltipField });
  }
};
e([y({ type: [String], json: { write: true } })], l4.prototype, "fields", void 0), e([y({ type: String, json: { write: true } })], l4.prototype, "normalizeField", void 0), e([y({ type: [i7], json: { read: false } })], l4.prototype, "series", void 0), e([y({ type: String, json: { write: true } })], l4.prototype, "tooltipField", void 0), l4 = p10 = e([a("esri.popup.content.support.ChartMediaInfoValue")], l4);
var n4 = l4;

// node_modules/@arcgis/core/popup/content/mixins/ChartMediaInfo.js
var p11 = class extends p8 {
  constructor(r3) {
    super(r3), this.type = null, this.value = null;
  }
};
e([y({ type: ["bar-chart", "column-chart", "line-chart", "pie-chart"], readOnly: true, json: { read: false, write: true } })], p11.prototype, "type", void 0), e([y({ type: n4, json: { write: true } })], p11.prototype, "value", void 0), p11 = e([a("esri.popup.content.mixins.ChartMediaInfo")], p11);
var a6 = p11;

// node_modules/@arcgis/core/popup/content/support/chartMediaInfoUtils.js
var c5 = o2()({ barchart: "bar-chart", columnchart: "column-chart", linechart: "line-chart", piechart: "pie-chart" });

// node_modules/@arcgis/core/popup/content/BarChartMediaInfo.js
var a7;
var p12 = a7 = class extends a6 {
  constructor(t2) {
    super(t2), this.type = "bar-chart";
  }
  clone() {
    return new a7({ altText: this.altText, title: this.title, caption: this.caption, value: this.value ? this.value.clone() : null });
  }
};
e([y({ type: ["bar-chart"], readOnly: true, json: { type: ["barchart"], read: false, write: c5.write } })], p12.prototype, "type", void 0), p12 = a7 = e([a("esri.popup.content.BarChartMediaInfo")], p12);
var i8 = p12;

// node_modules/@arcgis/core/popup/content/ColumnChartMediaInfo.js
var a8;
var p13 = a8 = class extends a6 {
  constructor(t2) {
    super(t2), this.type = "column-chart";
  }
  clone() {
    return new a8({ altText: this.altText, title: this.title, caption: this.caption, value: this.value ? this.value.clone() : null });
  }
};
e([y({ type: ["column-chart"], readOnly: true, json: { type: ["columnchart"], read: false, write: c5.write } })], p13.prototype, "type", void 0), p13 = a8 = e([a("esri.popup.content.ColumnChartMediaInfo")], p13);
var c6 = p13;

// node_modules/@arcgis/core/popup/content/support/ImageMediaInfoValue.js
var e3;
var p14 = e3 = class extends l {
  constructor(r3) {
    super(r3), this.linkURL = null, this.sourceURL = null;
  }
  clone() {
    return new e3({ linkURL: this.linkURL, sourceURL: this.sourceURL });
  }
};
e([y({ type: String, json: { write: true } })], p14.prototype, "linkURL", void 0), e([y({ type: String, json: { write: true } })], p14.prototype, "sourceURL", void 0), p14 = e3 = e([a("esri.popup.content.support.ImageMediaInfoValue")], p14);
var c7 = p14;

// node_modules/@arcgis/core/popup/content/ImageMediaInfo.js
var a9;
var i9 = a9 = class extends p8 {
  constructor(e5) {
    super(e5), this.refreshInterval = null, this.type = "image", this.value = null;
  }
  clone() {
    return new a9({ altText: this.altText, title: this.title, caption: this.caption, refreshInterval: this.refreshInterval, value: this.value ? this.value.clone() : null });
  }
};
e([y({ type: Number, json: { write: true } })], i9.prototype, "refreshInterval", void 0), e([y({ type: ["image"], readOnly: true, json: { read: false, write: true } })], i9.prototype, "type", void 0), e([y({ type: c7, json: { write: true } })], i9.prototype, "value", void 0), i9 = a9 = e([a("esri.popup.content.ImageMediaInfo")], i9);
var p15 = i9;

// node_modules/@arcgis/core/popup/content/LineChartMediaInfo.js
var i10;
var a10 = i10 = class extends a6 {
  constructor(t2) {
    super(t2), this.type = "line-chart";
  }
  clone() {
    return new i10({ altText: this.altText, title: this.title, caption: this.caption, value: this.value ? this.value.clone() : null });
  }
};
e([y({ type: ["line-chart"], readOnly: true, json: { type: ["linechart"], read: false, write: c5.write } })], a10.prototype, "type", void 0), a10 = i10 = e([a("esri.popup.content.LineChartMediaInfo")], a10);
var p16 = a10;

// node_modules/@arcgis/core/popup/content/PieChartMediaInfo.js
var p17;
var i11 = p17 = class extends a6 {
  constructor(t2) {
    super(t2), this.type = "pie-chart";
  }
  clone() {
    return new p17({ altText: this.altText, title: this.title, caption: this.caption, value: this.value ? this.value.clone() : null });
  }
};
e([y({ type: ["pie-chart"], readOnly: true, json: { type: ["piechart"], read: false, write: c5.write } })], i11.prototype, "type", void 0), i11 = p17 = e([a("esri.popup.content.PieChartMediaInfo")], i11);
var a11 = i11;

// node_modules/@arcgis/core/popup/content/support/mediaInfoTypes.js
var m2 = { base: p8, key: "type", defaultKeyValue: "image", typeMap: { "bar-chart": i8, "column-chart": c6, "line-chart": p16, "pie-chart": a11, image: p15 } };

// node_modules/@arcgis/core/popup/content/MediaContent.js
var l5;
var u3 = l5 = class extends p3 {
  constructor(t2) {
    super(t2), this.activeMediaInfoIndex = null, this.attributes = null, this.description = null, this.mediaInfos = null, this.title = null, this.type = "media";
  }
  readMediaInfos(t2) {
    return t2 && t2.map((t3) => "image" === t3.type ? p15.fromJSON(t3) : "barchart" === t3.type ? i8.fromJSON(t3) : "columnchart" === t3.type ? c6.fromJSON(t3) : "linechart" === t3.type ? p16.fromJSON(t3) : "piechart" === t3.type ? a11.fromJSON(t3) : void 0).filter(Boolean);
  }
  writeMediaInfos(t2, o5) {
    o5.mediaInfos = t2 && t2.map((t3) => t3.toJSON());
  }
  clone() {
    return new l5(p({ activeMediaInfoIndex: this.activeMediaInfoIndex, attributes: this.attributes, description: this.description, mediaInfos: this.mediaInfos, title: this.title }));
  }
};
e([y()], u3.prototype, "activeMediaInfoIndex", void 0), e([y({ type: Object, json: { write: true } })], u3.prototype, "attributes", void 0), e([y({ type: String, json: { write: true } })], u3.prototype, "description", void 0), e([y({ types: [m2] })], u3.prototype, "mediaInfos", void 0), e([o("mediaInfos")], u3.prototype, "readMediaInfos", null), e([r("mediaInfos")], u3.prototype, "writeMediaInfos", null), e([y({ type: String, json: { write: true } })], u3.prototype, "title", void 0), e([y({ type: ["media"], readOnly: true, json: { read: false, write: true } })], u3.prototype, "type", void 0), u3 = l5 = e([a("esri.popup.content.MediaContent")], u3);
var I = u3;

// node_modules/@arcgis/core/popup/support/RelatedRecordsInfoFieldOrder.js
var t;
var p18 = t = class extends l {
  constructor(r3) {
    super(r3), this.field = null, this.order = null;
  }
  clone() {
    return new t({ field: this.field, order: this.order });
  }
};
e([y({ type: String, json: { write: true } })], p18.prototype, "field", void 0), e([y({ type: ["asc", "desc"], json: { write: true } })], p18.prototype, "order", void 0), p18 = t = e([a("esri.popup.support.RelatedRecordsInfoFieldOrder")], p18);
var c8 = p18;

// node_modules/@arcgis/core/popup/content/RelationshipContent.js
var n5 = class extends i(p3) {
  constructor(t2) {
    super(t2), this.description = null, this.displayCount = null, this.displayType = "list", this.orderByFields = null, this.relationshipId = null, this.title = null, this.type = "relationship";
  }
};
e([y({ type: String, json: { write: true } })], n5.prototype, "description", void 0), e([y({ type: Number, json: { type: T, write: true } })], n5.prototype, "displayCount", void 0), e([y({ type: ["list"], json: { write: true } })], n5.prototype, "displayType", void 0), e([y({ type: [c8], json: { write: true } })], n5.prototype, "orderByFields", void 0), e([y({ type: Number, json: { type: T, write: true } })], n5.prototype, "relationshipId", void 0), e([y({ type: String, json: { write: true } })], n5.prototype, "title", void 0), e([y({ type: ["relationship"], readOnly: true, json: { read: false, write: true } })], n5.prototype, "type", void 0), n5 = e([a("esri.popup.content.RelationshipContent")], n5);
var l6 = n5;

// node_modules/@arcgis/core/popup/content/TextContent.js
var s9;
var p19 = s9 = class extends p3 {
  constructor(t2) {
    super(t2), this.text = null, this.type = "text";
  }
  clone() {
    return new s9({ text: this.text });
  }
};
e([y({ type: String, json: { write: true } })], p19.prototype, "text", void 0), e([y({ type: ["text"], readOnly: true, json: { read: false, write: true } })], p19.prototype, "type", void 0), p19 = s9 = e([a("esri.popup.content.TextContent")], p19);
var c9 = p19;

// node_modules/@arcgis/core/popup/content.js
var p20 = { base: null, key: "type", typeMap: { attachment: i3, media: I, text: c9, expression: i6, field: c4, relationship: l6 } };

// node_modules/@arcgis/core/popup/LayerOptions.js
var e4;
var p21 = e4 = class extends l {
  constructor(o5) {
    super(o5), this.returnTopmostRaster = null, this.showNoDataRecords = null;
  }
  clone() {
    return new e4({ showNoDataRecords: this.showNoDataRecords, returnTopmostRaster: this.returnTopmostRaster });
  }
};
e([y({ type: Boolean, json: { write: true } })], p21.prototype, "returnTopmostRaster", void 0), e([y({ type: Boolean, json: { write: true } })], p21.prototype, "showNoDataRecords", void 0), p21 = e4 = e([a("esri.popup.LayerOptions")], p21);
var a12 = p21;

// node_modules/@arcgis/core/popup/RelatedRecordsInfo.js
var d2;
var c10 = d2 = class extends l {
  constructor(o5) {
    super(o5), this.showRelatedRecords = null, this.orderByFields = null;
  }
  clone() {
    return new d2({ showRelatedRecords: this.showRelatedRecords, orderByFields: this.orderByFields ? p(this.orderByFields) : null });
  }
};
e([y({ type: Boolean, json: { write: true } })], c10.prototype, "showRelatedRecords", void 0), e([y({ type: [c8], json: { write: true } })], c10.prototype, "orderByFields", void 0), c10 = d2 = e([a("esri.popup.RelatedRecordsInfo")], c10);
var l7 = c10;

// node_modules/@arcgis/core/PopupTemplate.js
var T2 = "esri.PopupTemplate";
var b = s.getLogger(T2);
var J = "relationships/";
var L2 = "expression/";
var B = j.ofType({ key: "type", defaultKeyValue: "button", base: c, typeMap: { button: a2, toggle: a3 } });
var M = { base: p3, key: "type", typeMap: { media: I, custom: i4, text: c9, attachments: i3, fields: c4, expression: i6, relationship: l6 } };
var P = ["attachments", "fields", "media", "text", "expression", "relationship"];
var $ = class extends i(l) {
  constructor() {
    super(...arguments), this.actions = null, this.content = "", this.expressionInfos = null, this.fieldInfos = null, this.layerOptions = null, this.lastEditInfoEnabled = true, this.outFields = null, this.overwriteActions = false, this.returnGeometry = false, this.title = "";
  }
  castContent(t2) {
    return Array.isArray(t2) ? t2.map((t3) => S(M, t3)) : "string" == typeof t2 || "function" == typeof t2 || t2 instanceof HTMLElement || C(t2) ? t2 : (b.error("content error", "unsupported content value", { value: t2 }), null);
  }
  readContent(t2, e5) {
    const { popupElements: o5 } = e5;
    return Array.isArray(o5) && o5.length > 0 ? this._readPopupInfoElements(e5.description, e5.mediaInfos, o5) : this._readPopupInfo(e5);
  }
  writeContent(t2, e5, o5, s10) {
    "string" != typeof t2 ? Array.isArray(t2) && (e5.popupElements = t2.filter((t3) => P.includes(t3.type)).map((t3) => t3 && t3.toJSON(s10)), e5.popupElements.forEach((t3) => {
      "attachments" === t3.type ? this._writeAttachmentContent(e5) : "media" === t3.type ? this._writeMediaContent(t3, e5) : "text" === t3.type ? this._writeTextContent(t3, e5) : "relationship" === t3.type && this._writeRelationshipContent(t3, e5);
    })) : e5.description = t2;
  }
  writeFieldInfos(t2, e5, o5, s10) {
    const { content: r3 } = this, i12 = Array.isArray(r3) ? r3 : null;
    if (t2) {
      const o6 = i12 ? i12.filter((t3) => "fields" === t3.type) : [], r4 = o6.length && o6.every((t3) => {
        var _a;
        return (_a = t3.fieldInfos) == null ? void 0 : _a.length;
      });
      e5.fieldInfos = t2.filter(Boolean).map((t3) => {
        const e6 = t3.toJSON(s10);
        return r4 && (e6.visible = false), e6;
      });
    }
    if (i12) for (const n6 of i12) "fields" === n6.type && this._writeFieldsContent(n6, e5);
  }
  writeLayerOptions(t2, e5, o5, s10) {
    e5[o5] = !t2 || null === t2.showNoDataRecords && null === t2.returnTopmostRaster ? null : t2.toJSON(s10);
  }
  writeTitle(t2, e5) {
    e5.title = t2 || "";
  }
  async collectRequiredFields(t2, e5) {
    const o5 = this.expressionInfos || [];
    await this._collectExpressionInfoFields(t2, e5, [...o5, ...this._getContentExpressionInfos(this.content, o5)]), h(t2, e5, [...this.outFields || [], ...this._getActionsFields(this.actions), ...this._getTitleFields(this.title), ...this._getContentFields(this.content)]);
  }
  async getRequiredFields(t2) {
    const e5 = /* @__PURE__ */ new Set();
    return await this.collectRequiredFields(e5, t2), [...e5].sort();
  }
  _writeFieldsContent(t2, e5) {
    if (!Array.isArray(t2.fieldInfos) || !t2.fieldInfos.length) return;
    const o5 = p(t2.fieldInfos);
    Array.isArray(e5.fieldInfos) ? o5.forEach((t3) => {
      const o6 = e5.fieldInfos.find((e6) => e6.fieldName.toLowerCase() === t3.fieldName.toLowerCase());
      o6 ? o6.visible = true : e5.fieldInfos.push(t3);
    }) : e5.fieldInfos = o5;
  }
  _writeAttachmentContent(t2) {
    t2.showAttachments || (t2.showAttachments = true);
  }
  _writeRelationshipContent(t2, e5) {
    var _a, _b;
    const o5 = ((_a = t2.orderByFields) == null ? void 0 : _a.map((e6) => this._toFieldOrderJSON(e6, t2.relationshipId))) || [], s10 = [...((_b = e5.relatedRecordsInfo) == null ? void 0 : _b.orderByFields) || [], ...o5];
    e5.relatedRecordsInfo = { showRelatedRecords: true, ...(s10 == null ? void 0 : s10.length) && { orderByFields: s10 } };
  }
  _writeTextContent(t2, e5) {
    !e5.description && t2.text && (e5.description = t2.text);
  }
  _writeMediaContent(t2, e5) {
    if (!Array.isArray(t2.mediaInfos) || !t2.mediaInfos.length) return;
    const o5 = p(t2.mediaInfos);
    Array.isArray(e5.mediaInfos) ? e5.mediaInfos = [...e5.mediaInfos, ...o5] : e5.mediaInfos = o5;
  }
  _readPopupInfoElements(t2, e5, o5) {
    const s10 = { description: false, mediaInfos: false };
    return o5.map((o6) => "media" === o6.type ? (o6.mediaInfos || !e5 || s10.mediaInfos || (o6.mediaInfos = e5, s10.mediaInfos = true), I.fromJSON(o6)) : "text" === o6.type ? (o6.text || !t2 || s10.description || (o6.text = t2, s10.description = true), c9.fromJSON(o6)) : "attachments" === o6.type ? i3.fromJSON(o6) : "fields" === o6.type ? c4.fromJSON(o6) : "expression" === o6.type ? i6.fromJSON(o6) : "relationship" === o6.type ? l6.fromJSON(o6) : void 0).filter(Boolean);
  }
  _toRelationshipContent(t2) {
    const { field: e5, order: o5 } = t2;
    if (!(e5 == null ? void 0 : e5.startsWith(J))) return null;
    const s10 = e5.replace(J, "").split("/");
    if (2 !== s10.length) return null;
    const r3 = parseInt(s10[0], 10), i12 = s10[1];
    return "number" == typeof r3 && i12 ? l6.fromJSON({ relationshipId: r3, orderByFields: [{ field: i12, order: o5 }] }) : null;
  }
  _toFieldOrderJSON(t2, e5) {
    const { order: o5, field: s10 } = t2;
    return { field: `${J}${e5}/${s10}`, order: o5 };
  }
  _readPopupInfo({ description: t2, mediaInfos: e5, showAttachments: o5, relatedRecordsInfo: s10 = { showRelatedRecords: false } }) {
    const r3 = [];
    t2 ? r3.push(new c9({ text: t2 })) : r3.push(new c4()), Array.isArray(e5) && e5.length && r3.push(I.fromJSON({ mediaInfos: e5 })), o5 && r3.push(i3.fromJSON({ displayType: "auto" }));
    const { showRelatedRecords: i12, orderByFields: n6 } = s10;
    return i12 && (n6 == null ? void 0 : n6.length) && n6.forEach((t3) => {
      const e6 = this._toRelationshipContent(t3);
      e6 && r3.push(e6);
    }), r3.length ? r3 : t2;
  }
  _getContentElementFields(t2) {
    const e5 = t2 == null ? void 0 : t2.type;
    if ("attachments" === e5) return [...this._extractFieldNames(t2.title), ...this._extractFieldNames(t2.description)];
    if ("custom" === e5) return t2.outFields || [];
    if ("fields" === e5) return [...this._extractFieldNames(t2.title), ...this._extractFieldNames(t2.description), ...this._getFieldInfoFields(t2.fieldInfos ?? this.fieldInfos)];
    if ("media" === e5) {
      const e6 = t2.mediaInfos || [];
      return [...this._extractFieldNames(t2.title), ...this._extractFieldNames(t2.description), ...e6.reduce((t3, e7) => [...t3, ...this._getMediaInfoFields(e7)], [])];
    }
    return "text" === e5 ? this._extractFieldNames(t2.text) : [];
  }
  _getMediaInfoFields(t2) {
    const { caption: e5, title: o5, value: s10 } = t2, r3 = s10 || {}, { fields: i12, normalizeField: n6, tooltipField: p22, sourceURL: l8, linkURL: a13 } = r3, d3 = [...this._extractFieldNames(o5), ...this._extractFieldNames(e5), ...this._extractFieldNames(l8), ...this._extractFieldNames(a13), ...i12 ?? []];
    return n6 && d3.push(n6), p22 && d3.push(p22), d3;
  }
  _getContentExpressionInfos(t2, e5) {
    return Array.isArray(t2) ? t2.reduce((t3, e6) => [...t3, ..."expression" === e6.type && e6.expressionInfo ? [e6.expressionInfo] : []], e5) : [];
  }
  _getContentFields(t2) {
    return "string" == typeof t2 ? this._extractFieldNames(t2) : Array.isArray(t2) ? t2.reduce((t3, e5) => [...t3, ...this._getContentElementFields(e5)], []) : [];
  }
  async _collectExpressionInfoFields(t2, e5, o5) {
    o5 && await Promise.all(o5.map((o6) => S3(t2, e5, o6.expression)));
  }
  _getFieldInfoFields(t2) {
    return t2 ? t2.filter((t3) => void 0 === t3.visible || !!t3.visible).map((t3) => t3.fieldName).filter((t3) => !t3.startsWith(J) && !t3.startsWith(L2)) : [];
  }
  _getActionsFields(t2) {
    return t2 ? t2.toArray().reduce((t3, e5) => [...t3, ...this._getActionFields(e5)], []) : [];
  }
  _getActionFields(t2) {
    const { className: e5, title: o5, type: s10 } = t2, r3 = "button" === s10 || "toggle" === s10 ? t2.image : "";
    return [...this._extractFieldNames(o5), ...this._extractFieldNames(e5), ...this._extractFieldNames(r3)];
  }
  _getTitleFields(t2) {
    return "string" == typeof t2 ? this._extractFieldNames(t2) : [];
  }
  _extractFieldNames(t2) {
    if (!t2 || "string" != typeof t2) return [];
    const e5 = /{[^}]*}/g, o5 = t2.match(e5);
    if (!o5) return [];
    const s10 = /\{(\w+):.+\}/, r3 = o5.filter((t3) => !(0 === t3.indexOf(`{${J}`) || 0 === t3.indexOf(`{${L2}`))).map((t3) => t3.replace(s10, "{$1}"));
    return r3 ? r3.map((t3) => t3.slice(1, -1)) : [];
  }
};
e([y({ type: B })], $.prototype, "actions", void 0), e([y()], $.prototype, "content", void 0), e([s3("content")], $.prototype, "castContent", null), e([o("content", ["description", "fieldInfos", "popupElements", "mediaInfos", "showAttachments", "relatedRecordsInfo"])], $.prototype, "readContent", null), e([r("content", { popupElements: { type: j.ofType(p20) }, showAttachments: { type: Boolean }, mediaInfos: { type: j.ofType(m2) }, description: { type: String }, relatedRecordsInfo: { type: l7 } })], $.prototype, "writeContent", null), e([y({ type: [i2], json: { write: true } })], $.prototype, "expressionInfos", void 0), e([y({ type: [c3] })], $.prototype, "fieldInfos", void 0), e([r("fieldInfos")], $.prototype, "writeFieldInfos", null), e([y({ type: a12 })], $.prototype, "layerOptions", void 0), e([r("layerOptions")], $.prototype, "writeLayerOptions", null), e([y({ type: Boolean, json: { read: { source: "showLastEditInfo" }, write: { target: "showLastEditInfo" }, default: true } })], $.prototype, "lastEditInfoEnabled", void 0), e([y()], $.prototype, "outFields", void 0), e([y()], $.prototype, "overwriteActions", void 0), e([y()], $.prototype, "returnGeometry", void 0), e([y({ json: { type: String } })], $.prototype, "title", void 0), e([r("title")], $.prototype, "writeTitle", null), $ = e([a("esri.PopupTemplate")], $);
var k = $;

export {
  c8 as c,
  i3 as i,
  i5 as i2,
  u2 as u,
  c3 as c2,
  c4 as c3,
  i7 as i3,
  I,
  c9 as c4,
  i2 as i4,
  k
};
//# sourceMappingURL=chunk-MQ2IOGEF.js.map
