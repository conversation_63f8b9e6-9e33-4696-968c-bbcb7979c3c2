import {
  x
} from "./chunk-KE7SPCM7.js";
import {
  t
} from "./chunk-BS3GJQ77.js";
import {
  k as k2
} from "./chunk-MQ2IOGEF.js";
import {
  D,
  a,
  f as f3,
  k
} from "./chunk-AVKOL7OR.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  r
} from "./chunk-WXFAAYJL.js";
import {
  f as f2
} from "./chunk-SRBBUKOI.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  Et
} from "./chunk-U4SVMKOQ.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/support/kmlUtils.js
var c = { esriGeometryPoint: "points", esriGeometryPolyline: "polylines", esriGeometryPolygon: "polygons" };
function d(e) {
  const o = e.folders || [], t2 = o.slice(), r2 = /* @__PURE__ */ new Map(), n = /* @__PURE__ */ new Map(), i = /* @__PURE__ */ new Map(), f4 = /* @__PURE__ */ new Map(), a2 = /* @__PURE__ */ new Map(), l = { esriGeometryPoint: n, esriGeometryPolyline: i, esriGeometryPolygon: f4 };
  (e.featureCollection && e.featureCollection.layers || []).forEach((e2) => {
    const o2 = p(e2);
    o2.featureSet.features = [];
    const t3 = e2.featureSet.geometryType;
    r2.set(t3, o2);
    const a3 = e2.layerDefinition.objectIdField;
    "esriGeometryPoint" === t3 ? G(n, a3, e2.featureSet.features) : "esriGeometryPolyline" === t3 ? G(i, a3, e2.featureSet.features) : "esriGeometryPolygon" === t3 && G(f4, a3, e2.featureSet.features);
  }), e.groundOverlays && e.groundOverlays.forEach((e2) => {
    a2.set(e2.id, e2);
  }), o.forEach((o2) => {
    o2.networkLinkIds.forEach((r3) => {
      const s2 = P(r3, o2.id, e.networkLinks);
      s2 && t2.push(s2);
    });
  }), t2.forEach((e2) => {
    if (e2.featureInfos) {
      e2.points = p(r2.get("esriGeometryPoint")), e2.polylines = p(r2.get("esriGeometryPolyline")), e2.polygons = p(r2.get("esriGeometryPolygon")), e2.mapImages = [];
      for (const o2 of e2.featureInfos) switch (o2.type) {
        case "esriGeometryPoint":
        case "esriGeometryPolyline":
        case "esriGeometryPolygon": {
          const t3 = l[o2.type].get(o2.id);
          t3 && e2[c[o2.type]].featureSet.features.push(t3);
          break;
        }
        case "GroundOverlay": {
          const t3 = a2.get(o2.id);
          t3 && e2.mapImages.push(t3);
          break;
        }
      }
      e2.fullExtent = j([e2]);
    }
  });
  const u = j(t2);
  return { folders: o, sublayers: t2, extent: u };
}
function g(t2, s2, i, f4) {
  const a2 = r && r.findCredential(t2);
  t2 = Et(t2, { token: a2 && a2.token });
  const l = s.kmlServiceUrl;
  return U(l, { query: { url: t2, model: "simple", folders: "", refresh: 0 !== i || void 0, outSR: JSON.stringify(s2) }, responseType: "json", signal: f4 });
}
function S(e, o, t2 = null, r2 = []) {
  const s2 = [], n = {}, i = o.sublayers, f4 = o.folders.map((e2) => e2.id);
  return i.forEach((o2) => {
    var _a;
    const i2 = new e();
    if (t2 ? i2.read(o2, t2) : i2.read(o2), r2.length && f4.includes(i2.id) && (i2.visible = r2.includes(i2.id)), n[o2.id] = i2, null != o2.parentFolderId && -1 !== o2.parentFolderId) {
      const e2 = n[o2.parentFolderId];
      e2.sublayers || (e2.sublayers = []), (_a = e2.sublayers) == null ? void 0 : _a.unshift(i2);
    } else s2.unshift(i2);
  }), s2;
}
function G(e, o, t2) {
  t2.forEach((t3) => {
    e.set(t3.attributes[o], t3);
  });
}
function h(e, o) {
  let t2;
  return o.some((o2) => o2.id === e && (t2 = o2, true)), t2;
}
function P(e, o, t2) {
  const r2 = h(e, t2);
  return r2 && (r2.parentFolderId = o, r2.networkLink = r2), r2;
}
async function b(e) {
  const o = x.fromJSON(e.featureSet).features, r2 = e.layerDefinition, s2 = t(r2.drawingInfo.renderer), n = k2.fromJSON(e.popupInfo), i = [];
  for (const t2 of o) {
    const e2 = await s2.getSymbolAsync(t2);
    t2.symbol = e2, t2.popupTemplate = n, t2.visible = true, i.push(t2);
  }
  return i;
}
function j(e) {
  const o = a(D), t2 = a(D);
  for (const r2 of e) {
    if (r2.polygons && r2.polygons.featureSet && r2.polygons.featureSet.features) for (const e2 of r2.polygons.featureSet.features) f2(o, e2.geometry), f3(t2, o);
    if (r2.polylines && r2.polylines.featureSet && r2.polylines.featureSet.features) for (const e2 of r2.polylines.featureSet.features) f2(o, e2.geometry), f3(t2, o);
    if (r2.points && r2.points.featureSet && r2.points.featureSet.features) for (const e2 of r2.points.featureSet.features) f2(o, e2.geometry), f3(t2, o);
    if (r2.mapImages) for (const e2 of r2.mapImages) f2(o, e2.extent), f3(t2, o);
  }
  return k(t2, D) ? void 0 : { xmin: t2[0], ymin: t2[1], zmin: t2[2], xmax: t2[3], ymax: t2[4], zmax: t2[5], spatialReference: f.WGS84 };
}

export {
  d,
  g,
  S,
  b,
  j
};
//# sourceMappingURL=chunk-HJ36YGUG.js.map
