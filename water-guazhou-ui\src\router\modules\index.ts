// import Layout from '@/views/layout/Layout'

import sysAdmin from './sysAdmin';
import { r_locale } from './localRoutes';
import { constantRoutes } from './constantRoutes/constantRoutes';
import test from './test';

/** 静态路由 */
export const constantRouterMap: Array<any> = [
  {
    path: '/',
    hidden: true,
    redirect: '/home',
    component: () => import(/** Layout */ '@/views/layout/frame/Layout.vue'),
    children: [
      {
        hidden: true,
        path: 'home',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          icon: 'iconfont icon-shezhi',
          roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
        }
      },
      constantRoutes,
      {
        hidden: true,
        path: '404withlayout',
        name: 'NotFoundWithLayout',
        component: () => import('@/views/404.vue')
      }
    ]
  },
  {
    path: '/navigation',
    name: 'Navigation',
    component: () => import('@/views/navigation/index.vue'),
  },
  {
    path: '/accountManage',
    component: () => import('@/views/layout/frame/Layout.vue'),
    hidden: true,
    meta: {
      roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
    },
    children: [
      {
        path: 'index',
        name: 'accountManage',
        component: () => import('@/views/accountManage/index.vue'),
        meta: {
          title: '个人中心',
          icon: 'form',
          roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
        }
      },
      {
        path: 'news/:id',
        name: 'news',
        component: () => import('@/views/universal/information/index.vue'),
        meta: {
          title: '消息中心',
          icon: 'form',
          roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
        }
      }
    ]
  },
  { path: '/app', component: () => import('@/views/portal/portal.vue') },
  {
    path: '/application',
    component: () => import('@/views/layout/frame/Layout.vue'),
    hidden: true,
    meta: { roles: ['SYS_ADMIN'] },
    children: [
      {
        path: 'index',
        name: 'application',
        component: () => import('@/views/application/index.vue'),
        meta: { title: '应用管理', icon: 'form', roles: ['SYS_ADMIN'] }
      }
    ]
  },
  {
    path: '/Full',
    component: () => import('@/views/layout/frame/LayoutFull.vue'),
    hidden: true,
    meta: {
      roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
    },
    children: [
      {
        path: 'InsideFrame',
        name: 'InsideFrame',
        component: () =>
          import('@/views/layout/frame/components/InsideFrame.vue'),
        meta: { roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT'] },
        props: true
      }
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    props: (route) => ({ query: route.query }),
    children: []
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue')
  },
  {
    path: '/cockpit',
    hidden: true,
    component: () => import(/** Layout */ '@/views/cockpit/index.vue'),
    // children: [
    //   {
    //     hidden: true,
    //     path: 'smartProduction',
    //     name: 'smartProduction',
    //     component: () => import('@/views/cockpit/smartProduction/index.vue'),
    //   },
    //   {
    //     hidden: true,
    //     path: 'smartOperations',
    //     name: 'smartOperations',
    //     component: () => import('@/views/cockpit/smartOperations/index.vue'),
    //   }
    // ]
  },
];
/** 顶部路由 */
export const asyncRouterTop = [
  {
    path: '/extendPage',
    name: 'sysExtendPage',
    component: () => import('@/views/layout/frame/Layout.vue'),
    hidden: true,
    meta: {
      title: '大屏监控',
      icon: 'icon-daping',
      roles: ['SYS_ADMIN', 'TENANT_ADMIN']
    },
    children: [
      {
        path: 'sysPage',
        name: 'sysPage',
        component: () => import('@/views/extendPage/sysPage.vue'),
        meta: { title: '大屏监控', icon: 'icon-daping', roles: ['SYS_ADMIN'] }
      }
    ]
  }
];
/** 本地路由 */
export const asyncRouterMap: Array<any> = [...sysAdmin, ...r_locale, ...test];
