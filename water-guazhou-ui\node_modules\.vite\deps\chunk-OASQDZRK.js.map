{"version": 3, "sources": ["../../@arcgis/core/chunks/ImageMaterial.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../core/maybe.js\";import{O as r}from\"./vec2f64.js\";import{ShaderOutput as o}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as i}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{Transform as a}from\"../views/3d/webgl-engine/core/shaderLibrary/Transform.glsl.js\";import{multipassTerrainTest as t}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{defaultMaskAlphaCutoff as s}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{ColorConversion as d}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ColorConversion.glsl.js\";import{addProjViewLocalOrigin as l}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float2PassUniform as n}from\"../views/3d/webgl-engine/core/shaderModules/Float2PassUniform.js\";import{FloatPassUniform as g}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as p}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as c}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{Texture2DPassUniform as u}from\"../views/3d/webgl-engine/core/shaderModules/Texture2DPassUniform.js\";import{TransparencyPassType as m}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";import{VertexAttribute as v}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";function f(f){const w=new c,{vertex:h,fragment:b}=w;return l(h,f),w.include(a,f),w.attributes.add(v.POSITION,\"vec3\"),w.attributes.add(v.UV0,\"vec2\"),w.varyings.add(\"vpos\",\"vec3\"),f.hasMultipassTerrain&&w.varyings.add(\"depth\",\"float\"),h.uniforms.add(new n(\"textureCoordinateScaleFactor\",(o=>e(o.texture)&&e(o.texture.descriptor.textureCoordinateScaleFactor)?o.texture.descriptor.textureCoordinateScaleFactor:r))),h.code.add(p`\n    void main(void) {\n      vpos = position;\n      ${f.hasMultipassTerrain?\"depth = (view * vec4(vpos, 1.0)).z;\":\"\"}\n      vTexCoord = uv0 * textureCoordinateScaleFactor;\n      gl_Position = transformPosition(proj, view, vpos);\n    }\n  `),w.include(i,f),w.include(t,f),b.uniforms.add([new u(\"tex\",(e=>e.texture)),new g(\"opacity\",(e=>e.opacity))]),w.varyings.add(\"vTexCoord\",\"vec2\"),f.output===o.Alpha?b.code.add(p`\n    void main() {\n      discardBySlice(vpos);\n      ${f.hasMultipassTerrain?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n\n      float alpha = texture2D(tex, vTexCoord).a * opacity;\n      if (alpha  < ${p.float(s)}) {\n        discard;\n      }\n\n      gl_FragColor = vec4(alpha);\n    }\n    `):(b.include(d),b.code.add(p`\n    void main() {\n      discardBySlice(vpos);\n      ${f.hasMultipassTerrain?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n      gl_FragColor = texture2D(tex, vTexCoord) * opacity;\n\n      if (gl_FragColor.a < ${p.float(s)}) {\n        discard;\n      }\n\n      gl_FragColor = highlightSlice(gl_FragColor, vpos);\n      ${f.transparencyPassType===m.Color?\"gl_FragColor = premultiplyAlpha(gl_FragColor);\":\"\"}\n    }\n    `)),w}const w=Object.freeze(Object.defineProperty({__proto__:null,build:f},Symbol.toStringTag,{value:\"Module\"}));export{w as I,f as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIw5C,SAASA,GAAEA,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAE,EAAC,QAAOC,IAAE,UAAS,EAAC,IAAEF;AAAE,SAAO,EAAEE,IAAEH,EAAC,GAAEC,GAAE,QAAQG,IAAEJ,EAAC,GAAEC,GAAE,WAAW,IAAI,EAAE,UAAS,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,KAAI,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAED,GAAE,uBAAqBC,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAEE,GAAE,SAAS,IAAI,IAAI,EAAE,gCAAgC,CAAAD,OAAG,EAAEA,GAAE,OAAO,KAAG,EAAEA,GAAE,QAAQ,WAAW,4BAA4B,IAAEA,GAAE,QAAQ,WAAW,+BAA6B,CAAE,CAAC,GAAEC,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA,QAGtzDH,GAAE,sBAAoB,wCAAsC,EAAE;AAAA;AAAA;AAAA;AAAA,GAInE,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAE,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE,OAAO,CAAAK,OAAGA,GAAE,OAAQ,GAAE,IAAIH,GAAE,WAAW,CAAAG,OAAGA,GAAE,OAAQ,CAAC,CAAC,GAAEJ,GAAE,SAAS,IAAI,aAAY,MAAM,GAAED,GAAE,WAAS,EAAE,QAAM,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,QAG1KA,GAAE,sBAAoB,2CAAyC,EAAE;AAAA;AAAA;AAAA,qBAGpD,EAAE,MAAME,EAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAM1B,KAAG,EAAE,QAAQG,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,QAGxBL,GAAE,sBAAoB,2CAAyC,EAAE;AAAA;AAAA;AAAA,6BAG5C,EAAE,MAAME,EAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,QAK/BF,GAAE,yBAAuBE,GAAE,QAAM,mDAAiD,EAAE;AAAA;AAAA,KAEvF,IAAGD;AAAC;AAAC,IAAM,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAMD,GAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["f", "w", "o", "h", "r", "e"]}