{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/hud/HUDOcclusionPass.glsl.js", "../../@arcgis/core/chunks/HUDMaterial.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ReadLinearDepth as e}from\"../output/ReadLinearDepth.glsl.js\";import{multipassGeometryTest as r}from\"../shading/MultipassGeometryTest.glsl.js\";import{RgbaFloatEncoding as t}from\"../util/RgbaFloatEncoding.glsl.js\";import{texelFetch as o}from\"../util/WebGL2Utils.js\";import{Float2PassUniform as a}from\"../../shaderModules/Float2PassUniform.js\";import{glsl as i}from\"../../shaderModules/interfaces.js\";import{createTexture2DPassSizeUniforms as s}from\"../../shaderModules/Texture2DPassUniform.js\";import{TextureSizeUniformType as n}from\"../../shaderModules/TextureSizeUniformType.js\";function p(p,l){const{vertex:d,fragment:c}=p;l.hasMultipassGeometry&&d.include(r),l.hasMultipassTerrain&&p.varyings.add(\"depth\",\"float\"),d.code.add(i`\n  void main(void) {\n    vec4 posProjCenter;\n    if (dot(position, position) > 0.0) {\n      // Render single point to center of the pixel to avoid subpixel\n      // filtering to affect the marker color\n      ProjectHUDAux projectAux;\n      vec4 posProj = projectPositionHUD(projectAux);\n      posProjCenter = alignToPixelCenter(posProj, viewport.zw);\n\n      ${l.hasMultipassGeometry?i`\n        // Don't draw vertices behind geometry\n        if(geometryDepthTest(.5 + .5 * posProjCenter.xy / posProjCenter.w, projectAux.posView.z)){\n          posProjCenter = vec4(1e038, 1e038, 1e038, 1.0);\n        }`:\"\"}\n\n      ${l.hasMultipassTerrain?\"depth = projectAux.posView.z;\":\"\"}\n      vec3 vpos = projectAux.posModel;\n      if (rejectBySlice(vpos)) {\n        // Project out of clip space\n        posProjCenter = vec4(1e038, 1e038, 1e038, 1.0);\n      }\n\n    } else {\n      // Project out of clip space\n      posProjCenter = vec4(1e038, 1e038, 1e038, 1.0);\n    }\n\n    gl_Position = posProjCenter;\n    gl_PointSize = 1.0;\n  }\n  `),l.hasMultipassTerrain&&c.include(e),l.hasMultipassTerrain&&c.uniforms.add([...s(\"terrainDepthTexture\",((e,r)=>r.multipassTerrain.linearDepthTexture),l.hasWebGL2Context?n.None:n.InvSize),new a(\"nearFar\",((e,r)=>r.camera.nearFar))]),c.include(t),c.code.add(i`\n  void main() {\n    gl_FragColor = vec4(1, 1, 1, 1);\n    ${l.hasMultipassTerrain?i`\n          vec2 uv = gl_FragCoord.xy;\n\n          // Read the rgba data from the texture linear depth\n          vec4 terrainDepthData = ${o(l,\"terrainDepthTexture\",\"uv\")};\n\n          float terrainDepth = linearDepthFromFloat(rgba2float(terrainDepthData), nearFar);\n\n          // If HUD vertex is behind terrain and the terrain depth is not the initialize value (e.g. we are not looking at the sky)\n          // Mark the HUD vertex as occluded by transparent terrain\n          if(depth < terrainDepth && terrainDepthData != vec4(0,0,0,1)){\n            gl_FragColor.g = 0.5;\n          }`:\"\"}\n  }\n  `)}export{p as HUDOcclusionPass};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../core/maybe.js\";import{s as o,c as r}from\"./vec2.js\";import{O as i,a as l}from\"./vec2f64.js\";import{Z as t}from\"./vec4f64.js\";import{DEFAULT_TEX_SIZE as a}from\"../views/3d/support/engineContent/sdfPrimitives.js\";import{ShaderOutput as s}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as n}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{ObjectAndLayerIdColor as c}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/ObjectAndLayerIdColor.glsl.js\";import{AlignPixel as d}from\"../views/3d/webgl-engine/core/shaderLibrary/hud/AlignPixel.glsl.js\";import{HUD as u}from\"../views/3d/webgl-engine/core/shaderLibrary/hud/HUD.glsl.js\";import{HUDOcclusionPass as p}from\"../views/3d/webgl-engine/core/shaderLibrary/hud/HUDOcclusionPass.glsl.js\";import{OutputHighlight as v}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputHighlight.glsl.js\";import{VisualVariables as g}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/VisualVariables.glsl.js\";import{symbolAlphaCutoff as f,defaultMaskAlphaCutoff as m}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{ColorConversion as b}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ColorConversion.glsl.js\";import{RgbaFloatEncoding as h}from\"../views/3d/webgl-engine/core/shaderLibrary/util/RgbaFloatEncoding.glsl.js\";import{ScreenSizePerspective as w,addScreenSizePerspective as C,addScreenSizePerspectiveAlignment as x}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ScreenSizePerspective.glsl.js\";import{Float2PassUniform as P}from\"../views/3d/webgl-engine/core/shaderModules/Float2PassUniform.js\";import{Float4PassUniform as j}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{Float4sPassUniform as S}from\"../views/3d/webgl-engine/core/shaderModules/Float4sPassUniform.js\";import{FloatPassUniform as z}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{FloatsPassUniform as y}from\"../views/3d/webgl-engine/core/shaderModules/FloatsPassUniform.js\";import{glsl as F}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as O}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{Texture2DPassUniform as A}from\"../views/3d/webgl-engine/core/shaderModules/Texture2DPassUniform.js\";import{TransparencyPassType as $}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";import{VertexAttribute as D}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";import{vvColorNumber as L}from\"../views/3d/webgl-engine/materials/VisualVariablePassParameters.js\";function B(r){const l=new O,B=r.signedDistanceFieldEnabled;if(l.include(d),l.include(u,r),l.include(n,r),r.occlusionPass)return l.include(p,r),l;const{vertex:T,fragment:V}=l;l.include(w),V.include(h),V.include(b),l.include(g,r),l.include(c,r),l.varyings.add(\"vcolor\",\"vec4\"),l.varyings.add(\"vtc\",\"vec2\"),l.varyings.add(\"vsize\",\"vec2\"),r.binaryHighlightOcclusionEnabled&&l.varyings.add(\"voccluded\",\"float\"),T.uniforms.add([new j(\"viewport\",((e,o)=>o.camera.fullViewport)),new P(\"screenOffset\",((e,r)=>o(U,2*e.screenOffset[0]*r.camera.pixelRatio,2*e.screenOffset[1]*r.camera.pixelRatio))),new P(\"anchorPosition\",(e=>H(e))),new j(\"materialColor\",(e=>e.color)),new z(\"pixelRatio\",((e,o)=>o.camera.pixelRatio))]),B&&(T.uniforms.add(new j(\"outlineColor\",(e=>e.outlineColor))),V.uniforms.add([new j(\"outlineColor\",(e=>_(e)?e.outlineColor:t)),new z(\"outlineSize\",(e=>_(e)?e.outlineSize:0))])),r.hasScreenSizePerspective&&(C(T),x(T)),(r.debugDrawLabelBorder||r.binaryHighlightOcclusionEnabled)&&l.varyings.add(\"debugBorderCoords\",\"vec4\"),l.attributes.add(D.UV0,\"vec2\"),l.attributes.add(D.COLOR,\"vec4\"),l.attributes.add(D.SIZE,\"vec2\"),l.attributes.add(D.AUXPOS2,\"vec4\"),T.code.add(F`\n    void main(void) {\n      ProjectHUDAux projectAux;\n      vec4 posProj = projectPositionHUD(projectAux);\n      forwardObjectAndLayerIdColor();\n\n      if (rejectBySlice(projectAux.posModel)) {\n        // Project outside of clip plane\n        gl_Position = vec4(1e038, 1e038, 1e038, 1.0);\n        return;\n      }\n      vec2 inputSize;\n      ${r.hasScreenSizePerspective?F`\n      inputSize = screenSizePerspectiveScaleVec2(size, projectAux.absCosAngle, projectAux.distanceToCamera, screenSizePerspective);\n      vec2 screenOffsetScaled = screenSizePerspectiveScaleVec2(screenOffset, projectAux.absCosAngle, projectAux.distanceToCamera, screenSizePerspectiveAlignment);\n         `:F`\n      inputSize = size;\n      vec2 screenOffsetScaled = screenOffset;`}\n\n      ${r.vvSize?\"inputSize *= vvScale(auxpos2).xx;\":\"\"}\n\n      vec2 combinedSize = inputSize * pixelRatio;\n      vec4 quadOffset = vec4(0.0);\n\n      ${r.occlusionTestEnabled||r.binaryHighlightOcclusionEnabled?\"bool visible = testVisibilityHUD(posProj);\":\"\"}\n\n      ${r.binaryHighlightOcclusionEnabled?\"voccluded = visible ? 0.0 : 1.0;\":\"\"}\n    `);const E=F`vec2 uv01 = floor(uv0);\nvec2 uv = uv0 - uv01;\nquadOffset.xy = ((uv01 - anchorPosition) * 2.0 * combinedSize + screenOffsetScaled) / viewport.zw * posProj.w;`,M=r.pixelSnappingEnabled?B?F`posProj = alignToPixelOrigin(posProj, viewport.zw) + quadOffset;`:F`posProj += quadOffset;\nif (inputSize.x == size.x) {\nposProj = alignToPixelOrigin(posProj, viewport.zw);\n}`:F`posProj += quadOffset;`;r.vvColor&&T.uniforms.add([new S(\"vvColorColors\",(e=>e.vvColorColors),L),new y(\"vvColorValues\",(e=>e.vvColorValues),L)]),T.uniforms.add(new P(\"textureCoordinateScaleFactor\",(o=>e(o.texture)&&e(o.texture.descriptor.textureCoordinateScaleFactor)?o.texture.descriptor.textureCoordinateScaleFactor:i))),T.code.add(F`\n    ${r.occlusionTestEnabled?\"if (visible) {\":\"\"}\n    ${E}\n    ${r.vvColor?\"vcolor = vvGetColor(auxpos2, vvColorValues, vvColorColors) * materialColor;\":\"vcolor = color / 255.0 * materialColor;\"}\n\n    ${r.output===s.ObjectAndLayerIdColor?F`vcolor.a = 1.0;`:\"\"}\n\n    bool alphaDiscard = vcolor.a < ${F.float(f)};\n    ${B?`alphaDiscard = alphaDiscard && outlineColor.a < ${F.float(f)};`:\"\"}\n    if (alphaDiscard) {\n      // \"early discard\" if both symbol color (= fill) and outline color (if applicable) are transparent\n      gl_Position = vec4(1e38, 1e38, 1e38, 1.0);\n      return;\n    } else {\n      ${M}\n      gl_Position = posProj;\n    }\n\n    vtc = uv * textureCoordinateScaleFactor;\n\n    ${r.debugDrawLabelBorder?\"debugBorderCoords = vec4(uv01, 1.5 / combinedSize);\":\"\"}\n    vsize = inputSize;\n    ${r.occlusionTestEnabled?F`} else { vtc = vec2(0.0);\n      ${r.debugDrawLabelBorder?\"debugBorderCoords = vec4(0.5, 0.5, 1.5 / combinedSize);}\":\"}\"}`:\"\"}\n  }\n  `),V.uniforms.add(new A(\"tex\",(e=>e.texture)));const I=r.debugDrawLabelBorder?F`(isBorder > 0.0 ? 0.0 : ${F.float(m)})`:F.float(m),R=F`\n    ${r.debugDrawLabelBorder?F`\n      float isBorder = float(any(lessThan(debugBorderCoords.xy, debugBorderCoords.zw)) || any(greaterThan(debugBorderCoords.xy, 1.0 - debugBorderCoords.zw)));`:\"\"}\n\n    ${B?F`\n      vec4 fillPixelColor = vcolor;\n\n      // Attempt to sample texel centers to avoid that thin cross outlines\n      // disappear with large symbol sizes.\n      // see: https://devtopia.esri.com/WebGIS/arcgis-js-api/issues/7058#issuecomment-603041\n      const float txSize = ${F.float(a)};\n      const float texelSize = 1.0 / txSize;\n      // Calculate how much we have to add/subtract to/from each texel to reach the size of an onscreen pixel\n      vec2 scaleFactor = (vsize - txSize) * texelSize;\n      vec2 samplePos = vtc + (vec2(1.0, -1.0) * texelSize) * scaleFactor;\n\n      // Get distance and map it into [-0.5, 0.5]\n      float d = rgba2float(texture2D(tex, samplePos)) - 0.5;\n\n      // Distance in output units (i.e. pixels)\n      float dist = d * vsize.x;\n\n      // Create smooth transition from the icon into its outline\n      float fillAlphaFactor = clamp(0.5 - dist, 0.0, 1.0);\n      fillPixelColor.a *= fillAlphaFactor;\n\n      if (outlineSize > 0.25) {\n        vec4 outlinePixelColor = outlineColor;\n        float clampedOutlineSize = min(outlineSize, 0.5*vsize.x);\n\n        // Create smooth transition around outline\n        float outlineAlphaFactor = clamp(0.5 - (abs(dist) - 0.5*clampedOutlineSize), 0.0, 1.0);\n        outlinePixelColor.a *= outlineAlphaFactor;\n\n        if (\n          outlineAlphaFactor + fillAlphaFactor < ${I} ||\n          fillPixelColor.a + outlinePixelColor.a < ${F.float(f)}\n        ) {\n          discard;\n        }\n\n        // perform un-premultiplied over operator (see https://en.wikipedia.org/wiki/Alpha_compositing#Description)\n        float compositeAlpha = outlinePixelColor.a + fillPixelColor.a * (1.0 - outlinePixelColor.a);\n        vec3 compositeColor = vec3(outlinePixelColor) * outlinePixelColor.a +\n          vec3(fillPixelColor) * fillPixelColor.a * (1.0 - outlinePixelColor.a);\n\n        gl_FragColor = vec4(compositeColor, compositeAlpha);\n      } else {\n        if (fillAlphaFactor < ${I}) {\n          discard;\n        }\n\n        gl_FragColor = premultiplyAlpha(fillPixelColor);\n      }\n\n      // visualize SDF:\n      // gl_FragColor = vec4(clamp(-dist/vsize.x*2.0, 0.0, 1.0), clamp(dist/vsize.x*2.0, 0.0, 1.0), 0.0, 1.0);\n      `:F`\n          vec4 texColor = texture2D(tex, vtc, -0.5);\n          if (texColor.a < ${I}) {\n            discard;\n          }\n          gl_FragColor = texColor * premultiplyAlpha(vcolor);\n          `}\n\n    // Draw debug border with transparency, so that original texels along border are still partially visible\n    ${r.debugDrawLabelBorder?F`gl_FragColor = mix(gl_FragColor, vec4(1.0, 0.0, 1.0, 1.0), isBorder * 0.5);`:\"\"}\n  `;return r.output===s.Alpha&&V.code.add(F`\n      void main() {\n        ${R}\n        gl_FragColor = vec4(gl_FragColor.a);\n      }\n      `),r.output===s.ObjectAndLayerIdColor&&V.code.add(F`\n      void main() {\n        ${R}\n        outputObjectAndLayerIdColor();\n      }\n      `),r.output===s.Color&&V.code.add(F`\n    void main() {\n      ${R}\n      ${r.transparencyPassType===$.FrontFace?\"gl_FragColor.rgb /= gl_FragColor.a;\":\"\"}\n    }\n    `),r.output===s.Highlight&&(l.include(v,r),V.code.add(F`\n    void main() {\n      ${R}\n      ${r.binaryHighlightOcclusionEnabled?F`\n          if (voccluded == 1.0) {\n            gl_FragColor = vec4(1.0, 1.0, 0.0, 1.0);\n          } else {\n            gl_FragColor = vec4(1.0, 0.0, 1.0, 1.0);\n          }`:\"outputHighlight();\"}\n    }\n    `)),l}function _(e){return e.outlineColor[3]>0&&e.outlineSize>0}function H(e,o=U){return e.textureIsSignedDistanceField?T(e.anchorPosition,e.distanceFieldBoundingBox,o):r(o,e.anchorPosition),o}function T(r,i,l){e(i)?o(l,r[0]*(i[2]-i[0])+i[0],r[1]*(i[3]-i[1])+i[1]):o(l,0,0)}const U=l(),V=Object.freeze(Object.defineProperty({__proto__:null,build:B,calculateAnchorPosForRendering:H},Symbol.toStringTag,{value:\"Module\"}));export{V as H,B as b,H as c};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0kB,SAAS,EAAEA,IAAEC,IAAE;AAAC,QAAK,EAAC,QAAOC,IAAE,UAASC,GAAC,IAAEH;AAAE,EAAAC,GAAE,wBAAsBC,GAAE,QAAQE,EAAC,GAAEH,GAAE,uBAAqBD,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAEE,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUttBD,GAAE,uBAAqB;AAAA;AAAA;AAAA;AAAA,aAIpB,EAAE;AAAA;AAAA,QAELA,GAAE,sBAAoB,kCAAgC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAe7D,GAAEA,GAAE,uBAAqBE,GAAE,QAAQC,EAAC,GAAEH,GAAE,uBAAqBE,GAAE,SAAS,IAAI,CAAC,GAAG,EAAE,uBAAuB,CAACE,IAAEC,OAAIA,GAAE,iBAAiB,oBAAoBL,GAAE,mBAAiBI,GAAE,OAAKA,GAAE,OAAO,GAAE,IAAIA,GAAE,WAAW,CAACA,IAAEC,OAAIA,GAAE,OAAO,OAAQ,CAAC,CAAC,GAAEH,GAAE,QAAQC,EAAC,GAAED,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA,MAG9PF,GAAE,sBAAoB;AAAA;AAAA;AAAA;AAAA,oCAIQM,GAAEN,IAAE,uBAAsB,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAQtD,EAAE;AAAA;AAAA,GAEZ;AAAC;;;AChD0kF,SAAS,EAAEO,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAEC,KAAEH,GAAE;AAA2B,MAAGC,GAAE,QAAQG,EAAC,GAAEH,GAAE,QAAQI,IAAEL,EAAC,GAAEC,GAAE,QAAQI,IAAEL,EAAC,GAAEA,GAAE,cAAc,QAAOC,GAAE,QAAQ,GAAED,EAAC,GAAEC;AAAE,QAAK,EAAC,QAAOK,IAAE,UAASC,GAAC,IAAEN;AAAE,EAAAA,GAAE,QAAQ,CAAC,GAAEM,GAAE,QAAQC,EAAC,GAAED,GAAE,QAAQE,EAAC,GAAER,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,SAAS,IAAI,UAAS,MAAM,GAAEA,GAAE,SAAS,IAAI,OAAM,MAAM,GAAEA,GAAE,SAAS,IAAI,SAAQ,MAAM,GAAED,GAAE,mCAAiCC,GAAE,SAAS,IAAI,aAAY,OAAO,GAAEK,GAAE,SAAS,IAAI,CAAC,IAAI,EAAE,YAAY,CAACG,IAAEP,OAAIA,GAAE,OAAO,YAAa,GAAE,IAAIO,GAAE,gBAAgB,CAACA,IAAET,OAAIA,GAAE,GAAE,IAAES,GAAE,aAAa,CAAC,IAAET,GAAE,OAAO,YAAW,IAAES,GAAE,aAAa,CAAC,IAAET,GAAE,OAAO,UAAU,CAAE,GAAE,IAAIS,GAAE,kBAAkB,CAAAA,OAAG,EAAEA,EAAC,CAAE,GAAE,IAAI,EAAE,iBAAiB,CAAAA,OAAGA,GAAE,KAAM,GAAE,IAAIP,GAAE,cAAc,CAACO,IAAEP,OAAIA,GAAE,OAAO,UAAW,CAAC,CAAC,GAAEC,OAAIG,GAAE,SAAS,IAAI,IAAI,EAAE,gBAAgB,CAAAG,OAAGA,GAAE,YAAa,CAAC,GAAEF,GAAE,SAAS,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAAE,OAAG,EAAEA,EAAC,IAAEA,GAAE,eAAa,CAAE,GAAE,IAAIP,GAAE,eAAe,CAAAO,OAAG,EAAEA,EAAC,IAAEA,GAAE,cAAY,CAAE,CAAC,CAAC,IAAGT,GAAE,6BAA2BU,GAAEJ,EAAC,GAAEK,GAAEL,EAAC,KAAIN,GAAE,wBAAsBA,GAAE,oCAAkCC,GAAE,SAAS,IAAI,qBAAoB,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,KAAI,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,OAAM,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,MAAK,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,SAAQ,MAAM,GAAEK,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAYztHN,GAAE,2BAAyB;AAAA;AAAA;AAAA,aAGxB;AAAA;AAAA,8CAEmC;AAAA;AAAA,QAEtCA,GAAE,SAAO,sCAAoC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,QAK/CA,GAAE,wBAAsBA,GAAE,kCAAgC,+CAA6C,EAAE;AAAA;AAAA,QAEzGA,GAAE,kCAAgC,qCAAmC,EAAE;AAAA,KAC1E;AAAE,QAAM,IAAE;AAAA;AAAA,iHAEiG,IAAEA,GAAE,uBAAqBG,KAAE,sEAAoE;AAAA;AAAA;AAAA,KAG5M;AAA0B,EAAAH,GAAE,WAASM,GAAE,SAAS,IAAI,CAAC,IAAIG,GAAE,iBAAiB,CAAAA,OAAGA,GAAE,eAAeP,EAAC,GAAE,IAAIA,GAAE,iBAAiB,CAAAO,OAAGA,GAAE,eAAeP,EAAC,CAAC,CAAC,GAAEI,GAAE,SAAS,IAAI,IAAIG,GAAE,gCAAgC,CAAAP,OAAG,EAAEA,GAAE,OAAO,KAAG,EAAEA,GAAE,QAAQ,WAAW,4BAA4B,IAAEA,GAAE,QAAQ,WAAW,+BAA6B,CAAE,CAAC,GAAEI,GAAE,KAAK,IAAI;AAAA,MAC7UN,GAAE,uBAAqB,mBAAiB,EAAE;AAAA,MAC1C,CAAC;AAAA,MACDA,GAAE,UAAQ,gFAA8E,yCAAyC;AAAA;AAAA,MAEjIA,GAAE,WAAS,EAAE,wBAAsB,qBAAmB,EAAE;AAAA;AAAA,qCAEzB,EAAE,MAAM,CAAC,CAAC;AAAA,MACzCG,KAAE,mDAAmD,EAAE,MAAM,CAAC,CAAC,MAAI,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMnE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMHH,GAAE,uBAAqB,wDAAsD,EAAE;AAAA;AAAA,MAE/EA,GAAE,uBAAqB;AAAA,QACrBA,GAAE,uBAAqB,6DAA2D,GAAG,KAAG,EAAE;AAAA;AAAA,GAE/F,GAAEO,GAAE,SAAS,IAAI,IAAI,EAAE,OAAO,CAAAE,OAAGA,GAAE,OAAQ,CAAC;AAAE,QAAM,IAAET,GAAE,uBAAqB,4BAA4B,EAAE,MAAME,EAAC,CAAC,MAAI,EAAE,MAAMA,EAAC,GAAE,IAAE;AAAA,MACjIF,GAAE,uBAAqB;AAAA,kKACmI,EAAE;AAAA;AAAA,MAE5JG,KAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6BAMqB,EAAE,MAAMM,EAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mDAyBY,CAAC;AAAA,qDACC,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAY/B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASzB;AAAA;AAAA,6BAEqB,CAAC;AAAA;AAAA;AAAA;AAAA,WAInB;AAAA;AAAA;AAAA,MAGLT,GAAE,uBAAqB,iFAA+E,EAAE;AAAA;AAC1G,SAAOA,GAAE,WAAS,EAAE,SAAOO,GAAE,KAAK,IAAI;AAAA;AAAA,UAEhC,CAAC;AAAA;AAAA;AAAA,OAGJ,GAAEP,GAAE,WAAS,EAAE,yBAAuBO,GAAE,KAAK,IAAI;AAAA;AAAA,UAE9C,CAAC;AAAA;AAAA;AAAA,OAGJ,GAAEP,GAAE,WAAS,EAAE,SAAOO,GAAE,KAAK,IAAI;AAAA;AAAA,QAEhC,CAAC;AAAA,QACDP,GAAE,yBAAuBE,GAAE,YAAU,wCAAsC,EAAE;AAAA;AAAA,KAEhF,GAAEF,GAAE,WAAS,EAAE,cAAYC,GAAE,QAAQO,IAAER,EAAC,GAAEO,GAAE,KAAK,IAAI;AAAA;AAAA,QAElD,CAAC;AAAA,QACDP,GAAE,kCAAgC;AAAA;AAAA;AAAA;AAAA;AAAA,eAK7B,oBAAoB;AAAA;AAAA,KAE5B,IAAGC;AAAC;AAAC,SAAS,EAAEQ,IAAE;AAAC,SAAOA,GAAE,aAAa,CAAC,IAAE,KAAGA,GAAE,cAAY;AAAC;AAAC,SAAS,EAAEA,IAAEP,KAAE,GAAE;AAAC,SAAOO,GAAE,+BAA6B,EAAEA,GAAE,gBAAeA,GAAE,0BAAyBP,EAAC,IAAE,EAAEA,IAAEO,GAAE,cAAc,GAAEP;AAAC;AAAC,SAAS,EAAEF,IAAEW,IAAEV,IAAE;AAAC,IAAEU,EAAC,IAAEX,GAAEC,IAAED,GAAE,CAAC,KAAGW,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGA,GAAE,CAAC,GAAEX,GAAE,CAAC,KAAGW,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGA,GAAE,CAAC,CAAC,IAAEX,GAAEC,IAAE,GAAE,CAAC;AAAC;AAAC,IAAM,IAAE,EAAE;AAAV,IAAY,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAM,GAAE,gCAA+B,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["p", "l", "d", "c", "a", "e", "r", "i", "r", "l", "o", "B", "c", "u", "T", "V", "a", "e", "t", "i"]}