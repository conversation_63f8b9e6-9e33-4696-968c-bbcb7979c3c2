import {
  d,
  g
} from "./chunk-VHLK35TF.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-P2G4OGHI.js";
import {
  t as t2
} from "./chunk-YMY3DTA5.js";
import "./chunk-TNP2LXZZ.js";
import {
  ut,
  wt
} from "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import {
  o
} from "./chunk-PTIRBOGQ.js";
import {
  y
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  n
} from "./chunk-CCAF47ZU.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import {
  e2 as e
} from "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/rest/query/operations/pbfDehydratedFeatureSet.js
function u(t4, e2) {
  return e2;
}
function p(t4, e2, r, s2) {
  switch (r) {
    case 0:
      return m(t4, e2 + s2, 0);
    case 1:
      return "lowerLeft" === t4.originPosition ? m(t4, e2 + s2, 1) : y2(t4, e2 + s2, 1);
  }
}
function c(t4, e2, r, s2) {
  return 2 === r ? m(t4, e2, 2) : p(t4, e2, r, s2);
}
function d2(t4, e2, r, s2) {
  return 2 === r ? m(t4, e2, 3) : p(t4, e2, r, s2);
}
function f2(t4, e2, r, s2) {
  return 3 === r ? m(t4, e2, 3) : c(t4, e2, r, s2);
}
function m({ translate: t4, scale: e2 }, r, s2) {
  return t4[s2] + r * e2[s2];
}
function y2({ translate: t4, scale: e2 }, r, s2) {
  return t4[s2] - r * e2[s2];
}
var _ = class {
  constructor(t4) {
    this._options = t4, this.geometryTypes = ["point", "multipoint", "polyline", "polygon"], this._previousCoordinate = [0, 0], this._transform = null, this._applyTransform = u, this._lengths = [], this._currentLengthIndex = 0, this._toAddInCurrentPath = 0, this._vertexDimension = 0, this._coordinateBuffer = null, this._coordinateBufferPtr = 0, this._attributesConstructor = class {
    };
  }
  createFeatureResult() {
    return new d();
  }
  finishFeatureResult(t4) {
    if (this._options.applyTransform && (t4.transform = null), this._attributesConstructor = class {
    }, this._coordinateBuffer = null, this._lengths.length = 0, !t4.hasZ) return;
    const r = o(t4.geometryType, this._options.sourceSpatialReference, t4.spatialReference);
    if (!t(r)) for (const e2 of t4.features) r(e2.geometry);
  }
  createSpatialReference() {
    return new f();
  }
  addField(t4, e2) {
    t4.fields.push(y.fromJSON(e2));
    const r = t4.fields.map((t5) => t5.name);
    this._attributesConstructor = function() {
      for (const t5 of r) this[t5] = null;
    };
  }
  addFeature(t4, e2) {
    const r = this._options.maxStringAttributeLength ? this._options.maxStringAttributeLength : 0;
    if (r > 0) for (const s2 in e2.attributes) {
      const t5 = e2.attributes[s2];
      "string" == typeof t5 && t5.length > r && (e2.attributes[s2] = "");
    }
    t4.features.push(e2);
  }
  addQueryGeometry(t4, e2) {
    const { queryGeometry: r, queryGeometryType: s2 } = e2, o2 = wt(r.clone(), r, false, false, this._transform), i = ut(o2, s2, false, false);
    let n2 = null;
    switch (s2) {
      case "esriGeometryPoint":
        n2 = "point";
        break;
      case "esriGeometryPolygon":
        n2 = "polygon";
        break;
      case "esriGeometryPolyline":
        n2 = "polyline";
        break;
      case "esriGeometryMultipoint":
        n2 = "multipoint";
    }
    i.type = n2, t4.queryGeometryType = s2, t4.queryGeometry = i;
  }
  prepareFeatures(e2) {
    switch (this._transform = e2.transform ?? null, this._options.applyTransform && e2.transform && (this._applyTransform = this._deriveApplyTransform(e2)), this._vertexDimension = 2, e2.hasZ && this._vertexDimension++, e2.hasM && this._vertexDimension++, e2.geometryType) {
      case "point":
        this.addCoordinate = (t4, e3, r) => this.addCoordinatePoint(t4, e3, r), this.createGeometry = (t4) => this.createPointGeometry(t4);
        break;
      case "polygon":
        this.addCoordinate = (t4, e3, r) => this._addCoordinatePolygon(t4, e3, r), this.createGeometry = (t4) => this._createPolygonGeometry(t4);
        break;
      case "polyline":
        this.addCoordinate = (t4, e3, r) => this._addCoordinatePolyline(t4, e3, r), this.createGeometry = (t4) => this._createPolylineGeometry(t4);
        break;
      case "multipoint":
        this.addCoordinate = (t4, e3, r) => this._addCoordinateMultipoint(t4, e3, r), this.createGeometry = (t4) => this._createMultipointGeometry(t4);
        break;
      case "mesh":
      case "extent":
        break;
      default:
        n(e2.geometryType);
    }
  }
  createFeature() {
    return this._lengths.length = 0, this._currentLengthIndex = 0, this._previousCoordinate[0] = 0, this._previousCoordinate[1] = 0, new g(e(), null, new this._attributesConstructor());
  }
  allocateCoordinates() {
    const t4 = this._lengths.reduce((t5, e2) => t5 + e2, 0);
    this._coordinateBuffer = new Float64Array(t4 * this._vertexDimension), this._coordinateBufferPtr = 0;
  }
  addLength(t4, e2) {
    0 === this._lengths.length && (this._toAddInCurrentPath = e2), this._lengths.push(e2);
  }
  createPointGeometry(t4) {
    const e2 = { type: "point", x: 0, y: 0, spatialReference: t4.spatialReference, hasZ: !!t4.hasZ, hasM: !!t4.hasM };
    return e2.hasZ && (e2.z = 0), e2.hasM && (e2.m = 0), e2;
  }
  addCoordinatePoint(t4, e2, r) {
    const s2 = this._transform ? this._applyTransform(this._transform, e2, r, 0) : e2;
    if (null != s2) switch (r) {
      case 0:
        t4.x = s2;
        break;
      case 1:
        t4.y = s2;
        break;
      case 2:
        t4.hasZ ? t4.z = s2 : t4.m = s2;
        break;
      case 3:
        t4.m = s2;
    }
  }
  _transformPathLikeValue(t4, e2) {
    let r = 0;
    return e2 <= 1 && (r = this._previousCoordinate[e2], this._previousCoordinate[e2] += t4), this._transform ? this._applyTransform(this._transform, t4, e2, r) : t4;
  }
  _addCoordinatePolyline(t4, e2, r) {
    this._dehydratedAddPointsCoordinate(t4.paths, e2, r);
  }
  _addCoordinatePolygon(t4, e2, r) {
    this._dehydratedAddPointsCoordinate(t4.rings, e2, r);
  }
  _addCoordinateMultipoint(t4, e2, r) {
    0 === r && t4.points.push([]);
    const s2 = this._transformPathLikeValue(e2, r);
    t4.points[t4.points.length - 1].push(s2);
  }
  _createPolygonGeometry(t4) {
    return { type: "polygon", rings: [[]], spatialReference: t4.spatialReference, hasZ: !!t4.hasZ, hasM: !!t4.hasM };
  }
  _createPolylineGeometry(t4) {
    return { type: "polyline", paths: [[]], spatialReference: t4.spatialReference, hasZ: !!t4.hasZ, hasM: !!t4.hasM };
  }
  _createMultipointGeometry(t4) {
    return { type: "multipoint", points: [], spatialReference: t4.spatialReference, hasZ: !!t4.hasZ, hasM: !!t4.hasM };
  }
  _dehydratedAddPointsCoordinate(t4, e2, r) {
    0 === r && 0 == this._toAddInCurrentPath-- && (t4.push([]), this._toAddInCurrentPath = this._lengths[++this._currentLengthIndex] - 1, this._previousCoordinate[0] = 0, this._previousCoordinate[1] = 0);
    const s2 = this._transformPathLikeValue(e2, r), o2 = t4[t4.length - 1], i = this._coordinateBuffer;
    if (i) {
      if (0 === r) {
        const t5 = this._coordinateBufferPtr * Float64Array.BYTES_PER_ELEMENT;
        o2.push(new Float64Array(i.buffer, t5, this._vertexDimension));
      }
      i[this._coordinateBufferPtr++] = s2;
    }
  }
  _deriveApplyTransform(t4) {
    const { hasZ: e2, hasM: r } = t4;
    return e2 && r ? f2 : e2 ? c : r ? d2 : p;
  }
};

// node_modules/@arcgis/core/views/3d/support/PBFDecoderWorker.js
var t3 = class {
  _parseFeatureQuery(t4) {
    var _a;
    const s2 = t2(t4.buffer, new _(t4.options)), o2 = { ...s2, spatialReference: (_a = s2.spatialReference) == null ? void 0 : _a.toJSON(), fields: s2.fields ? s2.fields.map((e2) => e2.toJSON()) : void 0 };
    return Promise.resolve(o2);
  }
};
function s() {
  return new t3();
}
export {
  s as default
};
//# sourceMappingURL=PBFDecoderWorker-NCR2U5LA.js.map
