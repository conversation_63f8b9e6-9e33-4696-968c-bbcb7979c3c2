import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  i
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/widgets/support/GoTo.js
var t = (t2) => {
  let i2 = class extends t2 {
    constructor(...o) {
      super(...o), this.goToOverride = null, this.view = null;
    }
    callGoTo(o) {
      const { view: e2 } = this;
      return i(e2), this.goToOverride ? this.goToOverride(e2, o) : e2.goTo(o.target, o.options);
    }
  };
  return e([y()], i2.prototype, "goToOverride", void 0), e([y()], i2.prototype, "view", void 0), i2 = e([a("esri.widgets.support.GoTo")], i2), i2;
};

export {
  t
};
//# sourceMappingURL=chunk-RYZGQKBF.js.map
