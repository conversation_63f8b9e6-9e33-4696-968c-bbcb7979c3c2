import {
  s as s5
} from "./chunk-6OHGIAG7.js";
import {
  o as o3
} from "./chunk-PTIRBOGQ.js";
import {
  a as a3
} from "./chunk-FZ7BG3VX.js";
import {
  o as o2,
  q,
  r as r2
} from "./chunk-YEODPCXQ.js";
import {
  a as a2
} from "./chunk-I7WHRVHF.js";
import {
  s as s4
} from "./chunk-UVNYHPLJ.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  u
} from "./chunk-3WCHZJQK.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  $,
  A,
  E,
  E2,
  G,
  I,
  I2,
  P,
  R2 as R,
  S,
  T,
  k,
  s as s3
} from "./chunk-JXLVNWKF.js";
import {
  e as e2,
  s as s2,
  t as t2
} from "./chunk-LJHVXLBF.js";
import {
  N,
  a,
  b,
  m as m2
} from "./chunk-EIGTETCG.js";
import {
  o
} from "./chunk-MQAXMQFG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import {
  i2 as i
} from "./chunk-2CM7MIII.js";
import {
  O,
  f
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  e,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/GeographicTransformationStep.js
var s6 = 0;
var t3 = class _t {
  static fromGE(s10) {
    const i3 = new _t();
    return i3._wkt = s10.wkt, i3._wkid = s10.wkid, i3._isInverse = s10.isInverse, i3;
  }
  constructor(t5) {
    this.uid = s6++, t5 ? (this._wkt = null != t5.wkt ? t5.wkt : null, this._wkid = null != t5.wkid ? t5.wkid : -1, this._isInverse = null != t5.isInverse && true === t5.isInverse) : (this._wkt = null, this._wkid = -1, this._isInverse = false);
  }
  get wkt() {
    return this._wkt;
  }
  set wkt(t5) {
    this._wkt = t5, this.uid = s6++;
  }
  get wkid() {
    return this._wkid;
  }
  set wkid(t5) {
    this._wkid = t5, this.uid = s6++;
  }
  get isInverse() {
    return this._isInverse;
  }
  set isInverse(t5) {
    this._isInverse = t5, this.uid = s6++;
  }
  getInverse() {
    const s10 = new _t();
    return s10._wkt = this.wkt, s10._wkid = this._wkid, s10._isInverse = !this.isInverse, s10;
  }
};

// node_modules/@arcgis/core/geometry/support/GeographicTransformation.js
var s7 = class _s {
  static cacheKey(t5, s10) {
    return [void 0 !== t5.wkid && null !== t5.wkid ? t5.wkid.toString() : "-1", void 0 !== t5.wkt && null !== t5.wkt ? t5.wkt.toString() : "", void 0 !== s10.wkid && null !== s10.wkid ? s10.wkid.toString() : "-1", void 0 !== s10.wkt && null !== s10.wkt ? s10.wkt.toString() : ""].join(",");
  }
  static fromGE(i3) {
    const e4 = new _s();
    let n3 = "";
    for (const s10 of i3.steps) {
      const i4 = t3.fromGE(s10);
      e4.steps.push(i4), n3 += i4.uid.toString() + ",";
    }
    return e4._cachedProjection = {}, e4._gtlistentry = null, e4._chain = n3, e4;
  }
  constructor(s10) {
    if (this.steps = [], this._cachedProjection = {}, this._chain = "", this._gtlistentry = null, s10 && s10.steps) for (const i3 of s10.steps) i3 instanceof t3 ? this.steps.push(i3) : this.steps.push(new t3({ wkid: i3.wkid, wkt: i3.wkt, isInverse: i3.isInverse }));
  }
  getInverse() {
    const t5 = new _s();
    t5.steps = [];
    for (let s10 = this.steps.length - 1; s10 >= 0; s10--) {
      const i3 = this.steps[s10];
      t5.steps.push(i3.getInverse());
    }
    return t5;
  }
  getGTListEntry() {
    let t5 = "";
    for (const s10 of this.steps) t5 += s10.uid.toString() + ",";
    return t5 !== this._chain && (this._gtlistentry = null, this._cachedProjection = {}, this._chain = t5), this._gtlistentry;
  }
  assignCachedGe(t5, i3, e4) {
    this._cachedProjection[_s.cacheKey(t5, i3)] = e4;
  }
  getCachedGeTransformation(t5, i3) {
    let e4 = "";
    for (const s10 of this.steps) e4 += s10.uid.toString() + ",";
    e4 !== this._chain && (this._gtlistentry = null, this._cachedProjection = {}, this._chain = e4);
    const n3 = this._cachedProjection[_s.cacheKey(t5, i3)];
    return void 0 === n3 ? null : n3;
  }
};

// node_modules/@arcgis/core/chunks/pe.js
var t4;
var o4 = null;
function r3() {
  return !!o4;
}
function n2() {
  return !!has("esri-wasm");
}
function _() {
  return t4 || (t4 = import("./pe-wasm-TXCD73JG.js").then((e4) => e4.p).then(({ default: t5 }) => t5({ locateFile: (t6) => a3(`esri/geometry/support/${t6}`) })).then((e4) => {
    S2(e4);
  }), t4);
}
var P2;
var s8;
var E3;
!function(e4) {
  function t5(e5, t6, r6) {
    o4.ensureCache.prepare();
    const n4 = M(r6), _3 = r6 === n4, P3 = o4.ensureFloat64(n4), s10 = o4._pe_geog_to_proj(o4.getPointer(e5), t6, P3);
    return s10 && A2(r6, t6, P3, _3), s10;
  }
  function r5(e5, o5, r6, _3) {
    switch (_3) {
      case s8.PE_TRANSFORM_P_TO_G:
        return n3(e5, o5, r6);
      case s8.PE_TRANSFORM_G_TO_P:
        return t5(e5, o5, r6);
    }
    return 0;
  }
  function n3(e5, t6, o5) {
    return _2(e5, t6, o5, 0);
  }
  function _2(e5, t6, r6, n4) {
    o4.ensureCache.prepare();
    const _3 = M(r6), P3 = r6 === _3, s10 = o4.ensureFloat64(_3), E4 = o4._pe_proj_to_geog_center(o4.getPointer(e5), t6, s10, n4);
    return E4 && A2(r6, t6, s10, P3), E4;
  }
  e4.geogToProj = t5, e4.projGeog = r5, e4.projToGeog = n3, e4.projToGeogCenter = _2;
}(P2 || (P2 = {})), function(e4) {
  function t5() {
    e4.PE_BUFFER_MAX = o4.PeDefs.prototype.PE_BUFFER_MAX, e4.PE_NAME_MAX = o4.PeDefs.prototype.PE_NAME_MAX, e4.PE_MGRS_MAX = o4.PeDefs.prototype.PE_MGRS_MAX, e4.PE_USNG_MAX = o4.PeDefs.prototype.PE_USNG_MAX, e4.PE_DD_MAX = o4.PeDefs.prototype.PE_DD_MAX, e4.PE_DDM_MAX = o4.PeDefs.prototype.PE_DDM_MAX, e4.PE_DMS_MAX = o4.PeDefs.prototype.PE_DMS_MAX, e4.PE_UTM_MAX = o4.PeDefs.prototype.PE_UTM_MAX, e4.PE_PARM_MAX = o4.PeDefs.prototype.PE_PARM_MAX, e4.PE_TYPE_NONE = o4.PeDefs.prototype.PE_TYPE_NONE, e4.PE_TYPE_GEOGCS = o4.PeDefs.prototype.PE_TYPE_GEOGCS, e4.PE_TYPE_PROJCS = o4.PeDefs.prototype.PE_TYPE_PROJCS, e4.PE_TYPE_GEOGTRAN = o4.PeDefs.prototype.PE_TYPE_GEOGTRAN, e4.PE_TYPE_COORDSYS = o4.PeDefs.prototype.PE_TYPE_COORDSYS, e4.PE_TYPE_UNIT = o4.PeDefs.prototype.PE_TYPE_UNIT, e4.PE_TYPE_LINUNIT = o4.PeDefs.prototype.PE_TYPE_LINUNIT, e4.PE_STR_OPTS_NONE = o4.PeDefs.prototype.PE_STR_OPTS_NONE, e4.PE_STR_AUTH_NONE = o4.PeDefs.prototype.PE_STR_AUTH_NONE, e4.PE_STR_AUTH_TOP = o4.PeDefs.prototype.PE_STR_AUTH_TOP, e4.PE_STR_NAME_CANON = o4.PeDefs.prototype.PE_STR_NAME_CANON, e4.PE_PARM_X0 = o4.PeDefs.prototype.PE_PARM_X0, e4.PE_PARM_ND = o4.PeDefs.prototype.PE_PARM_ND, e4.PE_TRANSFORM_1_TO_2 = o4.PeDefs.prototype.PE_TRANSFORM_1_TO_2, e4.PE_TRANSFORM_2_TO_1 = o4.PeDefs.prototype.PE_TRANSFORM_2_TO_1, e4.PE_TRANSFORM_P_TO_G = o4.PeDefs.prototype.PE_TRANSFORM_P_TO_G, e4.PE_TRANSFORM_G_TO_P = o4.PeDefs.prototype.PE_TRANSFORM_G_TO_P, e4.PE_HORIZON_RECT = o4.PeDefs.prototype.PE_HORIZON_RECT, e4.PE_HORIZON_POLY = o4.PeDefs.prototype.PE_HORIZON_POLY, e4.PE_HORIZON_LINE = o4.PeDefs.prototype.PE_HORIZON_LINE, e4.PE_HORIZON_DELTA = o4.PeDefs.prototype.PE_HORIZON_DELTA;
  }
  e4.init = t5;
}(s8 || (s8 = {})), function(e4) {
  const t5 = {}, r5 = {}, n3 = (e5) => {
    if (e5) {
      const t6 = e5.getType();
      switch (t6) {
        case s8.PE_TYPE_GEOGCS:
          e5 = o4.castObject(e5, o4.PeGeogcs);
          break;
        case s8.PE_TYPE_PROJCS:
          e5 = o4.castObject(e5, o4.PeProjcs);
          break;
        case s8.PE_TYPE_GEOGTRAN:
          e5 = o4.castObject(e5, o4.PeGeogtran);
          break;
        default:
          t6 & s8.PE_TYPE_UNIT && (e5 = o4.castObject(e5, o4.PeUnit));
      }
    }
    return e5;
  };
  function _2() {
    o4.PeFactory.prototype.initialize(null);
  }
  function P3(e5) {
    return E4(s8.PE_TYPE_COORDSYS, e5);
  }
  function E4(e5, r6) {
    let _3 = null, P4 = t5[e5];
    if (P4 || (P4 = {}, t5[e5] = P4), P4.hasOwnProperty(String(r6))) _3 = P4[r6];
    else {
      const t6 = o4.PeFactory.prototype.factoryByType(e5, r6);
      o4.compare(t6, o4.NULL) || (_3 = t6, P4[r6] = _3);
    }
    return _3 = n3(_3), _3;
  }
  function i3(e5, t6) {
    let _3 = null, P4 = r5[e5];
    if (P4 || (P4 = {}, r5[e5] = P4), P4.hasOwnProperty(t6)) _3 = P4[t6];
    else {
      const r6 = o4.PeFactory.prototype.fromString(e5, t6);
      o4.compare(r6, o4.NULL) || (_3 = r6, P4[t6] = _3);
    }
    return _3 = n3(_3), _3;
  }
  function p2(e5) {
    return E4(s8.PE_TYPE_GEOGCS, e5);
  }
  function u4(e5) {
    return E4(s8.PE_TYPE_GEOGTRAN, e5);
  }
  function a5(e5) {
    return o4.PeFactory.prototype.getCode(e5);
  }
  function c2(e5) {
    return E4(s8.PE_TYPE_PROJCS, e5);
  }
  function g2(e5) {
    return E4(s8.PE_TYPE_UNIT, e5);
  }
  e4.initialize = _2, e4.coordsys = P3, e4.factoryByType = E4, e4.fromString = i3, e4.geogcs = p2, e4.geogtran = u4, e4.getCode = a5, e4.projcs = c2, e4.unit = g2;
}(E3 || (E3 = {}));
var i2 = null;
var p;
var u2;
var a4;
var c;
var g;
var T2;
var f2;
var O2;
var l;
function S2(e4) {
  function t5(e5, t6, o5) {
    e5[t6] = o5(e5[t6]);
  }
  o4 = e4, s8.init(), p.init(), g.init(), f2.init(), O2.init(), i2 = class extends o4.PeGCSExtent {
    destroy() {
      o4.destroy(this);
    }
  };
  const r5 = [o4.PeDatum, o4.PeGeogcs, o4.PeGeogtran, o4.PeObject, o4.PeParameter, o4.PePrimem, o4.PeProjcs, o4.PeSpheroid, o4.PeUnit];
  for (const o5 of r5) t5(o5.prototype, "getName", (e5) => function() {
    return e5.call(this, new Array(s8.PE_NAME_MAX));
  });
  for (const P3 of [o4.PeGeogtran, o4.PeProjcs]) t5(P3.prototype, "getParameters", (e5) => function() {
    const t6 = new Array(s8.PE_PARM_MAX);
    let r6 = e5.call(this);
    for (let e6 = 0; e6 < t6.length; e6++) {
      const n4 = o4.getValue(r6, "*");
      t6[e6] = n4 ? o4.wrapPointer(n4, o4.PeParameter) : null, r6 += Int32Array.BYTES_PER_ELEMENT;
    }
    return t6;
  });
  t5(o4.PeHorizon.prototype, "getCoord", (e5) => function() {
    const t6 = this.getSize();
    if (!t6) return null;
    const o5 = [];
    return A2(o5, t6, e5.call(this)), o5;
  }), t5(o4.PeGTlistExtendedEntry.prototype, "getEntries", (e5) => {
    const t6 = o4._pe_getPeGTlistExtendedGTsSize();
    return function() {
      let r6 = null;
      const n4 = e5.call(this);
      if (!o4.compare(n4, o4.NULL)) {
        r6 = [n4];
        const e6 = this.getSteps();
        if (e6 > 1) {
          const _3 = o4.getPointer(n4);
          for (let n5 = 1; n5 < e6; n5++) r6.push(o4.wrapPointer(_3 + t6 * n5, o4.PeGTlistExtendedGTs));
        }
      }
      return r6;
    };
  });
  const n3 = o4._pe_getPeHorizonSize(), _2 = (e5) => function() {
    let t6 = this._cache;
    if (t6 || (t6 = /* @__PURE__ */ new Map(), this._cache = t6), t6.has(e5)) return t6.get(e5);
    let r6 = null;
    const _3 = e5.call(this);
    if (!o4.compare(_3, o4.NULL)) {
      r6 = [_3];
      const e6 = _3.getNump();
      if (e6 > 1) {
        const t7 = o4.getPointer(_3);
        for (let _4 = 1; _4 < e6; _4++) r6.push(o4.wrapPointer(t7 + n3 * _4, o4.PeHorizon));
      }
    }
    return t6.set(e5, r6), r6;
  };
  t5(o4.PeProjcs.prototype, "horizonGcsGenerate", _2), t5(o4.PeProjcs.prototype, "horizonPcsGenerate", _2), o4.PeObject.prototype.toString = function(e5 = s8.PE_STR_OPTS_NONE) {
    o4.ensureCache.prepare();
    const t6 = o4.getPointer(this), r6 = o4.ensureInt8(new Array(s8.PE_BUFFER_MAX));
    return o4.UTF8ToString(o4._pe_object_to_string_ext(t6, e5, r6));
  };
}
function N2(e4) {
  if (!e4) return;
  const t5 = o4.getClass(e4);
  if (!t5) return;
  const r5 = o4.getCache(t5);
  if (!r5) return;
  const n3 = o4.getPointer(e4);
  n3 && delete r5[n3];
}
function y(e4, t5) {
  const r5 = [], n3 = new Array(t5);
  for (let _2 = 0; _2 < e4; _2++) r5.push(o4.ensureInt8(n3));
  return r5;
}
function M(e4) {
  let t5;
  return Array.isArray(e4[0]) ? (t5 = [], e4.forEach((e5) => {
    t5.push(e5[0], e5[1]);
  })) : t5 = e4, t5;
}
function A2(e4, t5, r5, n3 = false) {
  if (n3) for (let _2 = 0; _2 < 2 * t5; _2++) e4[_2] = o4.getValue(r5 + _2 * Float64Array.BYTES_PER_ELEMENT, "double");
  else {
    const n4 = 0 === e4.length;
    for (let _2 = 0; _2 < t5; _2++) n4 && (e4[_2] = new Array(2)), e4[_2][0] = o4.getValue(r5, "double"), e4[_2][1] = o4.getValue(r5 + Float64Array.BYTES_PER_ELEMENT, "double"), r5 += 2 * Float64Array.BYTES_PER_ELEMENT;
  }
}
!function(e4) {
  let t5;
  function r5() {
    e4.PE_GTLIST_OPTS_COMMON = o4.PeGTlistExtended.prototype.PE_GTLIST_OPTS_COMMON, t5 = o4._pe_getPeGTlistExtendedEntrySize();
  }
  function n3(e5, r6, n4, _2, P3, s10) {
    let E4 = null;
    const i3 = new o4.PeInteger(s10);
    try {
      const p2 = o4.PeGTlistExtended.prototype.getGTlist(e5, r6, n4, _2, P3, i3);
      if ((s10 = i3.val) && (E4 = [p2], s10 > 1)) {
        const e6 = o4.getPointer(p2);
        for (let r7 = 1; r7 < s10; r7++) E4.push(o4.wrapPointer(e6 + t5 * r7, o4.PeGTlistExtendedEntry));
      }
    } finally {
      o4.destroy(i3);
    }
    return E4;
  }
  e4.init = r5, e4.getGTlist = n3;
}(p || (p = {})), function(e4) {
  function t5(e5) {
    if (e5 && e5.length) {
      for (const t6 of e5) N2(t6), t6.getEntries().forEach((e6) => {
        N2(e6);
        const t7 = e6.getGeogtran();
        N2(t7), t7.getParameters().forEach(N2), [t7.getGeogcs1(), t7.getGeogcs2()].forEach((e7) => {
          N2(e7);
          const t8 = e7.getDatum();
          N2(t8), N2(t8.getSpheroid()), N2(e7.getPrimem()), N2(e7.getUnit());
        });
      });
      o4.PeGTlistExtendedEntry.prototype.Delete(e5[0]);
    }
  }
  e4.destroy = t5;
}(u2 || (u2 = {})), function(e4) {
  function t5(e5, t6, r5, n3, _2) {
    o4.ensureCache.prepare();
    const P3 = M(r5), s10 = r5 === P3, E4 = o4.ensureFloat64(P3);
    let i3 = 0;
    n3 && (i3 = o4.ensureFloat64(n3));
    const p2 = o4._pe_geog_to_geog(o4.getPointer(e5), t6, E4, i3, _2);
    return p2 && A2(r5, t6, E4, s10), p2;
  }
  e4.geogToGeog = t5;
}(a4 || (a4 = {})), function(e4) {
  const t5 = (e5, t6, r6, n4, _3, P4) => {
    let E5, i4;
    switch (o4.ensureCache.prepare(), e5) {
      case "dd":
        E5 = o4._pe_geog_to_dd, i4 = s8.PE_DD_MAX;
        break;
      case "ddm":
        E5 = o4._pe_geog_to_ddm, i4 = s8.PE_DDM_MAX;
        break;
      case "dms":
        E5 = o4._pe_geog_to_dms, i4 = s8.PE_DMS_MAX;
    }
    let p3 = 0;
    t6 && (p3 = o4.getPointer(t6));
    const u4 = M(n4), a5 = o4.ensureFloat64(u4), c2 = y(r6, i4), g2 = E5(p3, r6, a5, _3, o4.ensureInt32(c2));
    if (g2) for (let s10 = 0; s10 < r6; s10++) P4[s10] = o4.UTF8ToString(c2[s10]);
    return g2;
  }, r5 = (e5, t6, r6, n4, _3) => {
    let P4;
    switch (o4.ensureCache.prepare(), e5) {
      case "dd":
        P4 = o4._pe_dd_to_geog;
        break;
      case "ddm":
        P4 = o4._pe_ddm_to_geog;
        break;
      case "dms":
        P4 = o4._pe_dms_to_geog;
    }
    let s10 = 0;
    t6 && (s10 = o4.getPointer(t6));
    const E5 = n4.map((e6) => o4.ensureString(e6)), i4 = o4.ensureInt32(E5), p3 = o4.ensureFloat64(new Array(2 * r6)), u4 = P4(s10, r6, i4, p3);
    return u4 && A2(_3, r6, p3), u4;
  };
  function n3(e5, o5, r6, n4, _3) {
    return t5("dms", e5, o5, r6, n4, _3);
  }
  function _2(e5, t6, o5, n4) {
    return r5("dms", e5, t6, o5, n4);
  }
  function P3(e5, o5, r6, n4, _3) {
    return t5("ddm", e5, o5, r6, n4, _3);
  }
  function E4(e5, t6, o5, n4) {
    return r5("ddm", e5, t6, o5, n4);
  }
  function i3(e5, o5, r6, n4, _3) {
    return t5("dd", e5, o5, r6, n4, _3);
  }
  function p2(e5, t6, o5, n4) {
    return r5("dd", e5, t6, o5, n4);
  }
  e4.geogToDms = n3, e4.dmsToGeog = _2, e4.geogToDdm = P3, e4.ddmToGeog = E4, e4.geogToDd = i3, e4.ddToGeog = p2;
}(c || (c = {})), function(e4) {
  function t5() {
    e4.PE_MGRS_STYLE_NEW = o4.PeNotationMgrs.prototype.PE_MGRS_STYLE_NEW, e4.PE_MGRS_STYLE_OLD = o4.PeNotationMgrs.prototype.PE_MGRS_STYLE_OLD, e4.PE_MGRS_STYLE_AUTO = o4.PeNotationMgrs.prototype.PE_MGRS_STYLE_AUTO, e4.PE_MGRS_180_ZONE_1_PLUS = o4.PeNotationMgrs.prototype.PE_MGRS_180_ZONE_1_PLUS, e4.PE_MGRS_ADD_SPACES = o4.PeNotationMgrs.prototype.PE_MGRS_ADD_SPACES;
  }
  function r5(e5, t6, r6, n4, _2, P3, E4) {
    o4.ensureCache.prepare();
    let i3 = 0;
    e5 && (i3 = o4.getPointer(e5));
    const p2 = M(r6), u4 = o4.ensureFloat64(p2), a5 = y(t6, s8.PE_MGRS_MAX), c2 = o4.ensureInt32(a5), g2 = o4._pe_geog_to_mgrs_extended(i3, t6, u4, n4, _2, P3, c2);
    if (g2) for (let s10 = 0; s10 < t6; s10++) E4[s10] = o4.UTF8ToString(a5[s10]);
    return g2;
  }
  function n3(e5, t6, r6, n4, _2) {
    o4.ensureCache.prepare();
    let P3 = 0;
    e5 && (P3 = o4.getPointer(e5));
    const s10 = r6.map((e6) => o4.ensureString(e6)), E4 = o4.ensureInt32(s10), i3 = o4.ensureFloat64(new Array(2 * t6)), p2 = o4._pe_mgrs_to_geog_extended(P3, t6, E4, n4, i3);
    return p2 && A2(_2, t6, i3), p2;
  }
  e4.init = t5, e4.geogToMgrsExtended = r5, e4.mgrsToGeogExtended = n3;
}(g || (g = {})), function(e4) {
  function t5(e5, t6, r6, n3, _2, P3, E4) {
    o4.ensureCache.prepare();
    let i3 = 0;
    e5 && (i3 = o4.getPointer(e5));
    const p2 = M(r6), u4 = o4.ensureFloat64(p2), a5 = y(t6, s8.PE_MGRS_MAX), c2 = o4.ensureInt32(a5), g2 = o4._pe_geog_to_usng(i3, t6, u4, n3, _2, P3, c2);
    if (g2) for (let s10 = 0; s10 < t6; s10++) E4[s10] = o4.UTF8ToString(a5[s10]);
    return g2;
  }
  function r5(e5, t6, r6, n3) {
    o4.ensureCache.prepare();
    let _2 = 0;
    e5 && (_2 = o4.getPointer(e5));
    const P3 = r6.map((e6) => o4.ensureString(e6)), s10 = o4.ensureInt32(P3), E4 = o4.ensureFloat64(new Array(2 * t6)), i3 = o4._pe_usng_to_geog(_2, t6, s10, E4);
    return i3 && A2(n3, t6, E4), i3;
  }
  e4.geogToUsng = t5, e4.usngToGeog = r5;
}(T2 || (T2 = {})), function(e4) {
  function t5() {
    e4.PE_UTM_OPTS_NONE = o4.PeNotationUtm.prototype.PE_UTM_OPTS_NONE, e4.PE_UTM_OPTS_ADD_SPACES = o4.PeNotationUtm.prototype.PE_UTM_OPTS_ADD_SPACES, e4.PE_UTM_OPTS_NS = o4.PeNotationUtm.prototype.PE_UTM_OPTS_NS;
  }
  function r5(e5, t6, r6, n4, _2) {
    o4.ensureCache.prepare();
    let P3 = 0;
    e5 && (P3 = o4.getPointer(e5));
    const E4 = M(r6), i3 = o4.ensureFloat64(E4), p2 = y(t6, s8.PE_UTM_MAX), u4 = o4.ensureInt32(p2), a5 = o4._pe_geog_to_utm(P3, t6, i3, n4, u4);
    if (a5) for (let s10 = 0; s10 < t6; s10++) _2[s10] = o4.UTF8ToString(p2[s10]);
    return a5;
  }
  function n3(e5, t6, r6, n4, _2) {
    o4.ensureCache.prepare();
    let P3 = 0;
    e5 && (P3 = o4.getPointer(e5));
    const s10 = r6.map((e6) => o4.ensureString(e6)), E4 = o4.ensureInt32(s10), i3 = o4.ensureFloat64(new Array(2 * t6)), p2 = o4._pe_utm_to_geog(P3, t6, E4, n4, i3);
    return p2 && A2(_2, t6, i3), p2;
  }
  e4.init = t5, e4.geogToUtm = r5, e4.utmToGeog = n3;
}(f2 || (f2 = {})), function(e4) {
  const t5 = /* @__PURE__ */ new Map();
  function r5() {
    e4.PE_PCSINFO_OPTION_NONE = o4.PePCSInfo.prototype.PE_PCSINFO_OPTION_NONE, e4.PE_PCSINFO_OPTION_DOMAIN = o4.PePCSInfo.prototype.PE_PCSINFO_OPTION_DOMAIN, e4.PE_POLE_OUTSIDE_BOUNDARY = o4.PePCSInfo.prototype.PE_POLE_OUTSIDE_BOUNDARY, e4.PE_POLE_POINT = o4.PePCSInfo.prototype.PE_POLE_POINT;
  }
  function n3(r6, n4 = e4.PE_PCSINFO_OPTION_DOMAIN) {
    let _2 = null, P3 = null;
    return t5.has(r6) && (P3 = t5.get(r6), P3[n4] && (_2 = P3[n4])), _2 || (_2 = o4.PePCSInfo.prototype.generate(r6, n4), P3 || (P3 = [], t5.set(r6, P3)), P3[n4] = _2), _2;
  }
  e4.init = r5, e4.generate = n3;
}(O2 || (O2 = {})), function(e4) {
  function t5() {
    return o4.PeVersion.prototype.version_string();
  }
  e4.versionString = t5;
}(l || (l = {}));
var d = Object.freeze(Object.defineProperty({ __proto__: null, get PeCSTransformations() {
  return P2;
}, get PeDefs() {
  return s8;
}, get PeFactory() {
  return E3;
}, get PeGCSExtent() {
  return i2;
}, get PeGTTransformations() {
  return a4;
}, get PeGTlistExtended() {
  return p;
}, get PeGTlistExtendedEntry() {
  return u2;
}, get PeNotationDms() {
  return c;
}, get PeNotationMgrs() {
  return g;
}, get PeNotationUsng() {
  return T2;
}, get PeNotationUtm() {
  return f2;
}, get PePCSInfo() {
  return O2;
}, get PeVersion() {
  return l;
}, _init: S2, get _pe() {
  return o4;
}, isLoaded: r3, isSupported: n2, load: _ }, Symbol.toStringTag, { value: "Module" }));

// node_modules/@arcgis/core/geometry/projection.js
var J = null;
var Q = null;
var Y = null;
var $2 = {};
var nn = new s4();
function en() {
  return !!J && r3();
}
function tn(n3) {
  return t(Y) && (Y = Promise.all([_(), import("./geometryEngineBase-LMAF6D5K.js").then((n4) => n4.g), import("./hydrated-3ZBMPGNL.js")])), Y.then(([, e4, { hydratedAdapter: t5 }]) => {
    f(n3), Q = t5, J = e4.default, J._enableProjection(d), nn.notify();
  });
}
function rn(n3, e4, t5 = null, r5 = null) {
  return Array.isArray(n3) ? 0 === n3.length ? [] : ln(Q, n3, n3[0].spatialReference, e4, t5, r5) : ln(Q, [n3], n3.spatialReference, e4, t5, r5)[0];
}
function ln(n3, e4, t5, r5, l2 = null, i3 = null) {
  if (t(t5) || t(r5)) return e4;
  if (An(t5, r5, l2)) return e4.map((n4) => e(Pn(n4, t5, r5)));
  if (t(l2)) {
    const n4 = s7.cacheKey(t5, r5);
    void 0 !== $2[n4] ? l2 = $2[n4] : (l2 = sn(t5, r5, void 0), t(l2) && (l2 = new s7()), $2[n4] = l2);
  }
  if (t(J) || t(n3)) throw new cn();
  return r(i3) ? J._project(n3, e4, t5, r5, l2, i3) : J._project(n3, e4, t5, r5, l2);
}
function un(n3, e4) {
  const t5 = on([n3], e4);
  return r(t5.pending) ? { pending: t5.pending, geometry: null } : r(t5.geometries) ? { pending: null, geometry: t5.geometries[0] } : { pending: null, geometry: null };
}
function on(n3, e4) {
  if (!en()) {
    for (const t5 of n3) if (r(t5) && !E(t5.spatialReference, e4) && I(t5.spatialReference) && I(e4) && !An(t5.spatialReference, e4)) return i(nn), { pending: tn(), geometries: null };
  }
  return { pending: null, geometries: n3.map((n4) => t(n4) ? null : E(n4.spatialReference, e4) ? n4 : I(n4.spatialReference) && I(e4) ? fn(n4, e4) : null) };
}
function sn(n3, e4, t5 = null) {
  if (t(n3) || t(e4)) return null;
  if (t(J) || t(Q)) throw new cn();
  const r5 = J._getTransformation(Q, n3, e4, t5, t5 == null ? void 0 : t5.spatialReference);
  return null !== r5 ? s7.fromGE(r5) : null;
}
var cn = class extends s {
  constructor() {
    super("projection:not-loaded", "projection engine not fully loaded yet, please call load()");
  }
};
var En;
!function(n3) {
  n3[n3.UNKNOWN = 0] = "UNKNOWN", n3[n3.SPHERICAL_ECEF = 1] = "SPHERICAL_ECEF", n3[n3.WGS84 = 2] = "WGS84", n3[n3.WEB_MERCATOR = 3] = "WEB_MERCATOR", n3[n3.WGS84_ECEF = 4] = "WGS84_ECEF", n3[n3.CGCS2000 = 5] = "CGCS2000", n3[n3.WGS84_COMPARABLE_LON_LAT = 6] = "WGS84_COMPARABLE_LON_LAT", n3[n3.SPHERICAL_MARS_PCPF = 7] = "SPHERICAL_MARS_PCPF", n3[n3.GCSMARS2000 = 8] = "GCSMARS2000", n3[n3.SPHERICAL_MOON_PCPF = 9] = "SPHERICAL_MOON_PCPF", n3[n3.GCSMOON2000 = 10] = "GCSMOON2000", n3[n3.LON_LAT = 11] = "LON_LAT", n3[n3.PLATE_CARREE = 12] = "PLATE_CARREE";
}(En || (En = {}));
function fn(n3, e4) {
  try {
    const t5 = rn(n3, e4);
    if (null == t5) return null;
    "xmin" in n3 && "xmin" in t5 && (t5.zmin = n3.zmin, t5.zmax = n3.zmax);
    const r5 = o3(t5.type, n3.spatialReference, e4);
    return r(r5) && r5(t5), t5;
  } catch (t5) {
    if (!(t5 instanceof cn)) throw t5;
    return null;
  }
}
function An(n3, e4, t5) {
  return !t5 && (!!E(n3, e4) || I(n3) && I(e4) && !!de(n3, e4, ye));
}
async function _n(n3, e4, t5, r5) {
  if (en()) return O(r5);
  if (Array.isArray(n3)) {
    for (const { source: l2, dest: u4, geographicTransformation: o5 } of n3) if (!An(l2, u4, o5)) return tn(r5);
  } else if (!An(n3, e4, t5)) return tn(r5);
  return O(r5);
}
function Pn(n3, e4, t5) {
  return n3 ? "x" in n3 ? Ln(n3, e4, new w(), t5, 0) : "xmin" in n3 ? In(n3, e4, new w2(), t5, 0) : "rings" in n3 ? Tn(n3, e4, new v(), t5, 0) : "paths" in n3 ? Gn(n3, e4, new m(), t5, 0) : "points" in n3 ? Mn(n3, e4, new u(), t5, 0) : null : null;
}
function pn(n3, e4, t5 = e4.spatialReference, r5 = 0) {
  return r(t5) && r(n3.spatialReference) && r(Ln(n3, n3.spatialReference, e4, t5, r5));
}
function Ln(n3, e4, t5, r5, l2) {
  ze[0] = n3.x, ze[1] = n3.y;
  const u4 = n3.z;
  return ze[2] = void 0 !== u4 ? u4 : l2, xn(ze, e4, 0, ze, r5, 0, 1) ? (t5.x = ze[0], t5.y = ze[1], t5.spatialReference = r5, void 0 === u4 ? (t5.z = void 0, t5.hasZ = false) : (t5.z = ze[2], t5.hasZ = true), void 0 === n3.m ? (t5.m = void 0, t5.hasM = false) : (t5.m = n3.m, t5.hasM = true), t5) : null;
}
function Mn(n3, e4, t5, r5, l2) {
  const { points: u4, hasZ: o5, hasM: s10 } = n3, i3 = [], a5 = u4.length, c2 = [];
  for (const E4 of u4) c2.push(E4[0], E4[1], o5 ? E4[2] : l2);
  if (!xn(c2, e4, 0, c2, r5, 0, a5)) return null;
  for (let E4 = 0; E4 < a5; ++E4) {
    const n4 = 3 * E4, e5 = c2[n4], t6 = c2[n4 + 1];
    o5 && s10 ? i3.push([e5, t6, c2[n4 + 2], u4[E4][3]]) : o5 ? i3.push([e5, t6, c2[n4 + 2]]) : s10 ? i3.push([e5, t6, u4[E4][2]]) : i3.push([e5, t6]);
  }
  return t5.points = i3, t5.spatialReference = r5, t5.hasZ = o5, t5.hasM = s10, t5;
}
function Gn(n3, e4, t5, r5, l2) {
  const { paths: u4, hasZ: o5, hasM: s10 } = n3, i3 = [];
  return bn(u4, o5 ?? false, s10 ?? false, e4, i3, r5, l2) ? (t5.paths = i3, t5.spatialReference = r5, t5.hasZ = o5, t5.hasM = s10, t5) : null;
}
function hn({ hasZ: n3, spatialReference: e4, paths: t5 }, r5, l2 = 0) {
  const o5 = Jn(e4, Be), s10 = Ie[o5][En.WGS84_COMPARABLE_LON_LAT];
  if (t(s10)) return false;
  const i3 = n3 ? (n4) => n4 : (n4) => o(ze, n4[0], n4[1], l2);
  for (const u4 of t5) {
    const n4 = [];
    for (const e5 of u4) {
      const t6 = [0, 0, l2];
      s10(i3(e5), 0, t6, 0), n4.push(t6);
    }
    r5.push(n4);
  }
  return true;
}
function Wn({ hasZ: n3, spatialReference: e4, rings: t5 }, r5, l2 = 0) {
  const o5 = Jn(e4, Be), s10 = Ie[o5][En.WGS84_COMPARABLE_LON_LAT];
  if (t(s10)) return false;
  const i3 = n3 ? (n4) => n4 : (n4) => o(ze, n4[0], n4[1], l2);
  for (const u4 of t5) {
    const n4 = [];
    for (const e5 of u4) {
      const t6 = [0, 0, l2];
      s10(i3(e5), 0, t6, 0), n4.push(t6);
    }
    r5.push(n4);
  }
  return true;
}
function mn(n3, e4, t5 = e4.spatialReference, r5 = 0) {
  return r(n3.spatialReference) && r(t5) && r(Tn(n3, n3.spatialReference, e4, t5, r5));
}
function Tn(n3, e4, t5, r5, l2) {
  const { rings: u4, hasZ: o5, hasM: s10 } = n3, i3 = [];
  return bn(u4, o5 ?? false, s10 ?? false, e4, i3, r5, l2) ? (t5.rings = i3, t5.spatialReference = r5, t5.hasZ = o5, t5.hasM = s10, t5) : null;
}
function In(n3, e4, t5, r5, l2) {
  const { xmin: u4, ymin: o5, xmax: s10, ymax: i3, hasZ: a5, hasM: c2 } = n3;
  if (!Bn(u4, o5, a5 ? n3.zmin : l2, e4, ze, r5)) return null;
  t5.xmin = ze[0], t5.ymin = ze[1], a5 && (t5.zmin = ze[2]);
  return Bn(s10, i3, a5 ? n3.zmax : l2, e4, ze, r5) ? (t5.xmax = ze[0], t5.ymax = ze[1], a5 && (t5.zmax = ze[2]), c2 && (t5.mmin = n3.mmin, t5.mmax = n3.mmax), t5.spatialReference = r5, t5) : null;
}
function Hn(n3, e4, t5) {
  return xn(n3, e4, 0, ze, t5.spatialReference, 0, 1) ? (t5.x = ze[0], t5.y = ze[1], t5.z = ze[2], t5) : null;
}
function gn(n3, e4, t5, r5 = 0) {
  ze[0] = n3.x, ze[1] = n3.y;
  const l2 = n3.z;
  return ze[2] = void 0 !== l2 ? l2 : r5, xn(ze, n3.spatialReference, 0, e4, t5, 0, 1);
}
function Bn(n3, e4, t5, r5, l2, u4) {
  return Ke[0] = n3, Ke[1] = e4, Ke[2] = t5, xn(Ke, r5, 0, l2, u4, 0, 1);
}
function jn(n3, e4, t5, r5) {
  return !(t(e4) || t(r5) || n3.length < 2) && (2 === n3.length && (Ke[0] = n3[0], Ke[1] = n3[1], Ke[2] = 0, n3 = Ke), xn(n3, e4, 0, t5, r5, 0, 1));
}
function Un(n3, e4, t5) {
  return wn(n3, e4, t5);
}
function wn(n3, e4, t5) {
  if (t(e4)) return false;
  const r5 = Jn(e4, Be), l2 = Ie[r5][En.WGS84_COMPARABLE_LON_LAT];
  return !t(l2) && (l2(n3, 0, Ke, 0), t5 !== Ke && (t5[0] = Ke[0], t5[1] = Ke[1], t5.length > 2 && (t5[2] = Ke[2])), true);
}
function xn(n3, e4, t5, r5, l2, o5, s10 = 1) {
  const i3 = de(e4, l2, ye);
  if (t(i3)) return false;
  if (i3 === Qn) {
    if (n3 === r5 && t5 === o5) return true;
    const e5 = t5 + 3 * s10;
    for (let l3 = t5, u4 = o5; l3 < e5; l3++, u4++) r5[u4] = n3[l3];
    return true;
  }
  const a5 = t5 + 3 * s10;
  for (let u4 = t5, c2 = o5; u4 < a5; u4 += 3, c2 += 3) i3(n3, u4, r5, c2);
  return true;
}
function bn(n3, e4, t5, r5, l2, u4, o5 = 0) {
  const s10 = new Array();
  for (const a5 of n3) for (const n4 of a5) s10.push(n4[0], n4[1], e4 ? n4[2] : o5);
  if (!xn(s10, r5, 0, s10, u4, 0, s10.length / 3)) return false;
  let i3 = 0;
  l2.length = 0;
  for (const a5 of n3) {
    const n4 = new Array();
    for (const r6 of a5) e4 && t5 ? n4.push([s10[i3++], s10[i3++], s10[i3++], r6[3]]) : e4 ? n4.push([s10[i3++], s10[i3++], s10[i3++]]) : t5 ? (n4.push([s10[i3++], s10[i3++], r6[2]]), i3++) : (n4.push([s10[i3++], s10[i3++]]), i3++);
    l2.push(n4);
  }
  return true;
}
function vn(n3, e4, t5, r5) {
  return null != n3 && (E(e4, r5) ? (a2(t5, n3), true) : (Ke[0] = n3[0], Ke[1] = n3[1], Ke[2] = 0, !!xn(Ke, e4, 0, Ke, r5, 0, 1) && (t5[0] = Ke[0], t5[1] = Ke[1], Ke[0] = n3[2], Ke[1] = n3[3], Ke[2] = 0, !!xn(Ke, e4, 0, Ke, r5, 0, 1) && (t5[2] = Ke[0], t5[3] = Ke[1], true))));
}
function Zn(n3, e4, t5, r5) {
  if (t(n3) || t(r5)) return false;
  const l2 = Jn(n3, Be), o5 = Jn(r5, je);
  if (l2 === o5 && !Vn(o5) && (l2 !== En.UNKNOWN || E(n3, r5))) return q(t5, e4), true;
  if (Vn(o5)) {
    const n4 = Ie[l2][En.LON_LAT], r6 = Ie[En.LON_LAT][o5];
    return !t(n4) && !t(r6) && (n4(e4, 0, be, 0), r6(be, 0, ke, 0), Xn(we * be[0], we * be[1], t5), t5[12] = ke[0], t5[13] = ke[1], t5[14] = ke[2], true);
  }
  if ((o5 === En.WEB_MERCATOR || o5 === En.PLATE_CARREE) && (l2 === En.WGS84 || l2 === En.CGCS2000 && o5 === En.PLATE_CARREE || l2 === En.SPHERICAL_ECEF || l2 === En.WEB_MERCATOR)) {
    const n4 = Ie[l2][En.LON_LAT], r6 = Ie[En.LON_LAT][o5];
    return !t(n4) && !t(r6) && (n4(e4, 0, be, 0), r6(be, 0, ke, 0), l2 === En.SPHERICAL_ECEF ? Dn(we * be[0], we * be[1], t5) : r2(t5), t5[12] = ke[0], t5[13] = ke[1], t5[14] = ke[2], true);
  }
  return false;
}
function Vn(n3) {
  return n3 === En.SPHERICAL_ECEF || n3 === En.SPHERICAL_MARS_PCPF || n3 === En.SPHERICAL_MOON_PCPF;
}
function Xn(n3, e4, t5) {
  const r5 = Math.sin(n3), l2 = Math.cos(n3), u4 = Math.sin(e4), o5 = Math.cos(e4), s10 = t5;
  return s10[0] = -r5, s10[4] = -u4 * l2, s10[8] = o5 * l2, s10[12] = 0, s10[1] = l2, s10[5] = -u4 * r5, s10[9] = o5 * r5, s10[13] = 0, s10[2] = 0, s10[6] = o5, s10[10] = u4, s10[14] = 0, s10[3] = 0, s10[7] = 0, s10[11] = 0, s10[15] = 1, s10;
}
function Dn(n3, e4, t5) {
  return Xn(n3, e4, t5), o2(t5, t5), t5;
}
function Jn(n3, e4) {
  return n3 ? e4.spatialReference === n3 ? e4.spatialReferenceId : (e4.spatialReference = n3, "metersPerUnit" in e4 && (e4.metersPerUnit = $(n3, 1)), n3.wkt === I2.wkt ? e4.spatialReferenceId = En.SPHERICAL_ECEF : G(n3) ? e4.spatialReferenceId = En.WGS84 : k(n3) ? e4.spatialReferenceId = En.WEB_MERCATOR : T(n3) ? e4.spatialReferenceId = En.PLATE_CARREE : n3.wkt === A.wkt ? e4.spatialReferenceId = En.WGS84_ECEF : n3.wkid === S.CGCS2000 ? e4.spatialReferenceId = En.CGCS2000 : n3.wkt === E2.wkt ? e4.spatialReferenceId = En.SPHERICAL_MARS_PCPF : n3.wkt === R.wkt ? e4.spatialReferenceId = En.SPHERICAL_MOON_PCPF : P(n3) ? e4.spatialReferenceId = En.GCSMARS2000 : s3(n3) ? e4.spatialReferenceId = En.GCSMOON2000 : e4.spatialReferenceId = En.UNKNOWN) : En.UNKNOWN;
}
function Qn(n3, e4, t5, r5) {
  n3 !== t5 && (t5[r5++] = n3[e4++], t5[r5++] = n3[e4++], t5[r5] = n3[e4]);
}
function Yn(n3, e4, t5, r5) {
  t5[r5++] = xe * (n3[e4++] / s2.radius), t5[r5++] = xe * (Math.PI / 2 - 2 * Math.atan(Math.exp(-n3[e4++] / s2.radius))), t5[r5] = n3[e4];
}
function $n(n3, e4, t5, r5) {
  Yn(n3, e4, t5, r5), Pe(t5, r5, t5, r5);
}
function ne(n3, e4, t5, r5) {
  Yn(n3, e4, t5, r5), We(t5, r5, t5, r5);
}
function ee(n3, t5, r5, l2, u4) {
  const o5 = 0.4999999 * Math.PI, s10 = a(we * n3[t5 + 1], -o5, o5), i3 = Math.sin(s10);
  r5[l2++] = we * n3[t5] * u4.radius, r5[l2++] = u4.halfSemiMajorAxis * Math.log((1 + i3) / (1 - i3)), r5[l2] = n3[t5 + 2];
}
function te(n3, e4, t5, r5) {
  ee(n3, e4, t5, r5, s2);
}
var re = s2.radius * Math.PI / 180;
var le = 180 / (s2.radius * Math.PI);
function ue(n3, e4, t5, r5) {
  t5[r5] = n3[e4] * re, t5[r5 + 1] = n3[e4 + 1] * re, t5[r5 + 2] = n3[e4 + 2];
}
function oe(n3, e4, t5, r5) {
  t5[r5] = n3[e4] * le, t5[r5 + 1] = n3[e4 + 1] * le, t5[r5 + 2] = n3[e4 + 2];
}
function se(n3, e4, t5, r5) {
  Yn(n3, e4, t5, r5), ue(t5, r5, t5, r5);
}
function ie(n3, e4, t5, r5) {
  me(n3, e4, t5, r5), ue(t5, r5, t5, r5);
}
function ae(n3, e4, t5, r5) {
  Me(n3, e4, t5, r5), ue(t5, r5, t5, r5);
}
function ce(n3, e4, t5, r5) {
  oe(n3, e4, t5, r5), Pe(t5, r5, t5, r5);
}
function Ee(n3, e4, t5, r5) {
  oe(n3, e4, t5, r5), te(t5, r5, t5, r5);
}
function Ce(n3, e4, t5, r5) {
  oe(n3, e4, t5, r5), We(t5, r5, t5, r5);
}
function Re(n3) {
  if (t(n3)) return false;
  const e4 = Jn(n3, Be);
  return !!Ie[e4][En.WGS84_COMPARABLE_LON_LAT];
}
function Ae(n3, e4, t5, r5, l2) {
  const u4 = l2 + n3[e4 + 2], o5 = we * n3[e4 + 1], s10 = we * n3[e4], i3 = Math.cos(o5);
  t5[r5++] = Math.cos(s10) * i3 * u4, t5[r5++] = Math.sin(s10) * i3 * u4, t5[r5] = Math.sin(o5) * u4;
}
function _e(n3, e4, t5, r5) {
  Ae(n3, e4, t5, r5, e2.radius);
}
function Se(n3, e4, t5, r5) {
  Ae(n3, e4, t5, r5, t2.radius);
}
function Pe(n3, e4, t5, r5) {
  Ae(n3, e4, t5, r5, s2.radius);
}
function pe(n3, e4, t5, r5, u4) {
  const o5 = n3[e4], s10 = n3[e4 + 1], i3 = n3[e4 + 2], a5 = Math.sqrt(o5 * o5 + s10 * s10 + i3 * i3), c2 = N(i3 / (0 === a5 ? 1 : a5)), E4 = Math.atan2(s10, o5);
  t5[r5++] = xe * E4, t5[r5++] = xe * c2, t5[r5] = a5 - u4;
}
function Le(n3, e4, t5, r5) {
  pe(n3, e4, t5, r5, e2.radius);
}
function Oe(n3, e4, t5, r5) {
  pe(n3, e4, t5, r5, t2.radius);
}
function Me(n3, e4, t5, r5) {
  pe(n3, e4, t5, r5, s2.radius);
}
function Ne(n3, e4, t5, r5) {
  Me(n3, e4, t5, r5), te(t5, r5, t5, r5);
}
function Ge(n3, e4, t5, r5) {
  Me(n3, e4, t5, r5), We(t5, r5, t5, r5);
}
function he(n3, e4, t5, r5, l2) {
  const u4 = we * n3[e4], o5 = we * n3[e4 + 1], s10 = n3[e4 + 2], i3 = Math.sin(o5), a5 = Math.cos(o5), c2 = l2.radius / Math.sqrt(1 - l2.eccentricitySquared * i3 * i3);
  t5[r5++] = (c2 + s10) * a5 * Math.cos(u4), t5[r5++] = (c2 + s10) * a5 * Math.sin(u4), t5[r5++] = (c2 * (1 - l2.eccentricitySquared) + s10) * i3;
}
function We(n3, e4, t5, r5) {
  he(n3, e4, t5, r5, s2);
}
function me(n3, e4, t5, r5) {
  const l2 = s5, u4 = n3[e4], o5 = n3[e4 + 1], s10 = n3[e4 + 2];
  let i3, a5, c2, E4, C, R2, f3, A3, _2, S3, P3, p2, L, O3, M2, N3, G2, h, W, m3, T3;
  i3 = Math.abs(s10), a5 = u4 * u4 + o5 * o5, c2 = Math.sqrt(a5), E4 = a5 + s10 * s10, C = Math.sqrt(E4), m3 = Math.atan2(o5, u4), R2 = s10 * s10 / E4, f3 = a5 / E4, O3 = l2.a2 / C, M2 = l2.a3 - l2.a4 / C, f3 > 0.3 ? (A3 = i3 / C * (1 + f3 * (l2.a1 + O3 + R2 * M2) / C), W = Math.asin(A3), S3 = A3 * A3, _2 = Math.sqrt(1 - S3)) : (_2 = c2 / C * (1 - R2 * (l2.a5 - O3 - f3 * M2) / C), W = Math.acos(_2), S3 = 1 - _2 * _2, A3 = Math.sqrt(S3)), P3 = 1 - s2.eccentricitySquared * S3, p2 = s2.radius / Math.sqrt(P3), L = l2.a6 * p2, O3 = c2 - p2 * _2, M2 = i3 - L * A3, G2 = _2 * O3 + A3 * M2, N3 = _2 * M2 - A3 * O3, h = N3 / (L / P3 + G2), W += h, T3 = G2 + N3 * h / 2, s10 < 0 && (W = -W), t5[r5++] = xe * m3, t5[r5++] = xe * W, t5[r5] = T3;
}
function Te(n3, e4, t5, r5) {
  me(n3, e4, t5, r5), Pe(t5, r5, t5, r5);
}
function Fe(n3, e4, t5, r5) {
  me(n3, e4, t5, r5), te(t5, r5, t5, r5);
}
var Ie = { [En.WGS84]: { [En.CGCS2000]: null, [En.GCSMARS2000]: null, [En.GCSMOON2000]: null, [En.LON_LAT]: Qn, [En.WGS84_COMPARABLE_LON_LAT]: Qn, [En.SPHERICAL_ECEF]: Pe, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: te, [En.PLATE_CARREE]: ue, [En.WGS84]: Qn, [En.WGS84_ECEF]: We }, [En.CGCS2000]: { [En.CGCS2000]: Qn, [En.GCSMARS2000]: null, [En.GCSMOON2000]: null, [En.LON_LAT]: Qn, [En.WGS84_COMPARABLE_LON_LAT]: Qn, [En.SPHERICAL_ECEF]: Pe, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: null, [En.PLATE_CARREE]: ue, [En.WGS84]: null, [En.WGS84_ECEF]: We }, [En.GCSMARS2000]: { [En.CGCS2000]: null, [En.GCSMARS2000]: Qn, [En.GCSMOON2000]: null, [En.LON_LAT]: Qn, [En.WGS84_COMPARABLE_LON_LAT]: null, [En.SPHERICAL_ECEF]: null, [En.SPHERICAL_MARS_PCPF]: Se, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: null, [En.PLATE_CARREE]: null, [En.WGS84]: null, [En.WGS84_ECEF]: null }, [En.GCSMOON2000]: { [En.CGCS2000]: null, [En.GCSMARS2000]: null, [En.GCSMOON2000]: Qn, [En.LON_LAT]: Qn, [En.WGS84_COMPARABLE_LON_LAT]: null, [En.SPHERICAL_ECEF]: null, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: _e, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: null, [En.PLATE_CARREE]: null, [En.WGS84]: null, [En.WGS84_ECEF]: null }, [En.WEB_MERCATOR]: { [En.CGCS2000]: null, [En.GCSMARS2000]: null, [En.GCSMOON2000]: null, [En.LON_LAT]: Yn, [En.WGS84_COMPARABLE_LON_LAT]: Yn, [En.SPHERICAL_ECEF]: $n, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: Qn, [En.PLATE_CARREE]: se, [En.WGS84]: Yn, [En.WGS84_ECEF]: ne }, [En.WGS84_ECEF]: { [En.CGCS2000]: me, [En.GCSMARS2000]: null, [En.GCSMOON2000]: null, [En.LON_LAT]: me, [En.WGS84_COMPARABLE_LON_LAT]: me, [En.SPHERICAL_ECEF]: Te, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: Fe, [En.PLATE_CARREE]: ie, [En.WGS84]: me, [En.WGS84_ECEF]: Qn }, [En.SPHERICAL_ECEF]: { [En.CGCS2000]: Me, [En.GCSMARS2000]: null, [En.GCSMOON2000]: null, [En.LON_LAT]: Me, [En.WGS84_COMPARABLE_LON_LAT]: Me, [En.SPHERICAL_ECEF]: Qn, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: Ne, [En.PLATE_CARREE]: ae, [En.WGS84]: Me, [En.WGS84_ECEF]: Ge }, [En.SPHERICAL_MARS_PCPF]: { [En.CGCS2000]: null, [En.GCSMARS2000]: Oe, [En.GCSMOON2000]: null, [En.LON_LAT]: Oe, [En.WGS84_COMPARABLE_LON_LAT]: null, [En.SPHERICAL_ECEF]: null, [En.SPHERICAL_MARS_PCPF]: Qn, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: null, [En.PLATE_CARREE]: null, [En.WGS84]: null, [En.WGS84_ECEF]: null }, [En.SPHERICAL_MOON_PCPF]: { [En.CGCS2000]: null, [En.GCSMARS2000]: null, [En.GCSMOON2000]: Le, [En.LON_LAT]: Le, [En.WGS84_COMPARABLE_LON_LAT]: null, [En.SPHERICAL_ECEF]: null, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: Qn, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: null, [En.PLATE_CARREE]: null, [En.WGS84]: null, [En.WGS84_ECEF]: null }, [En.UNKNOWN]: { [En.CGCS2000]: null, [En.GCSMARS2000]: null, [En.GCSMOON2000]: null, [En.LON_LAT]: null, [En.WGS84_COMPARABLE_LON_LAT]: null, [En.SPHERICAL_ECEF]: null, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: Qn, [En.WEB_MERCATOR]: null, [En.PLATE_CARREE]: null, [En.WGS84]: null, [En.WGS84_ECEF]: null }, [En.LON_LAT]: { [En.CGCS2000]: Qn, [En.GCSMARS2000]: Qn, [En.GCSMOON2000]: Qn, [En.LON_LAT]: Qn, [En.WGS84_COMPARABLE_LON_LAT]: Qn, [En.SPHERICAL_ECEF]: Pe, [En.SPHERICAL_MARS_PCPF]: Se, [En.SPHERICAL_MOON_PCPF]: _e, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: te, [En.PLATE_CARREE]: ue, [En.WGS84]: Qn, [En.WGS84_ECEF]: We }, [En.WGS84_COMPARABLE_LON_LAT]: { [En.CGCS2000]: null, [En.GCSMARS2000]: null, [En.GCSMOON2000]: null, [En.LON_LAT]: Qn, [En.WGS84_COMPARABLE_LON_LAT]: Qn, [En.SPHERICAL_ECEF]: Pe, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: null, [En.PLATE_CARREE]: ue, [En.WGS84]: Qn, [En.WGS84_ECEF]: We }, [En.PLATE_CARREE]: { [En.CGCS2000]: oe, [En.GCSMARS2000]: null, [En.GCSMOON2000]: null, [En.LON_LAT]: oe, [En.WGS84_COMPARABLE_LON_LAT]: oe, [En.SPHERICAL_ECEF]: ce, [En.SPHERICAL_MARS_PCPF]: null, [En.SPHERICAL_MOON_PCPF]: null, [En.UNKNOWN]: null, [En.WEB_MERCATOR]: Ee, [En.PLATE_CARREE]: Qn, [En.WGS84]: oe, [En.WGS84_ECEF]: Ce } };
function de(n3, e4, t5 = ge()) {
  return t(n3) || t(e4) ? null : He(n3, e4, t5).projector;
}
function He(n3, e4, t5) {
  if (t(n3) || t(e4) || t5.source.spatialReference === n3 && t5.dest.spatialReference === e4) return t5;
  const r5 = Jn(n3, t5.source), l2 = Jn(e4, t5.dest);
  return r5 === En.UNKNOWN && l2 === En.UNKNOWN ? E(n3, e4) ? t5.projector = Qn : t5.projector = null : t5.projector = Ie[r5][l2], t5;
}
function ge() {
  return { source: { spatialReference: null, spatialReferenceId: En.UNKNOWN, metersPerUnit: 1 }, dest: { spatialReference: null, spatialReferenceId: En.UNKNOWN, metersPerUnit: 1 }, projector: Qn };
}
var Be = { spatialReference: null, spatialReferenceId: En.UNKNOWN };
var je = { spatialReference: null, spatialReferenceId: En.UNKNOWN };
var ye = ge();
var Ue = ge();
var we = m2(1);
var xe = b(1);
var Ke = n();
var be = n();
var ke = n();
var ze = n();
var ve = n();

export {
  P2 as P,
  s8 as s,
  E3 as E,
  a4 as a,
  c,
  g,
  T2 as T,
  f2 as f,
  O2 as O,
  s7 as s2,
  en,
  tn,
  rn,
  ln,
  un,
  on,
  sn,
  An,
  _n,
  pn,
  hn,
  Wn,
  mn,
  Hn,
  gn,
  Bn,
  jn,
  Un,
  xn,
  vn,
  Zn,
  Xn,
  ee,
  Re,
  pe
};
//# sourceMappingURL=chunk-UYAKJRPP.js.map
