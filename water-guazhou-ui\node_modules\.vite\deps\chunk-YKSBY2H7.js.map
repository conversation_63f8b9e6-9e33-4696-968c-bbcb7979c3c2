{"version": 3, "sources": ["../../@arcgis/core/arcade/portalUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{id as r}from\"../kernel.js\";import e from\"../request.js\";import n from\"../portal/Portal.js\";function t(r,e){if(null===r)return e;return new n({url:r.field(\"url\")})}async function s(n,t,s){const u=r?.findCredential(n.restUrl);if(!u)return null;if(\"loaded\"===n.loadStatus&&\"\"===t&&n.user&&n.user.sourceJSON&&!1===s)return n.user.sourceJSON;if(\"\"===t){const r=await e(n.restUrl+\"/community/self\",{responseType:\"json\",query:{f:\"json\",...!1===s?{}:{returnUserLicenseTypeExtensions:!0}}});if(r.data){const e=r.data;if(e&&e.username)return e}return null}const o=await e(n.restUrl+\"/community/users/\"+t,{responseType:\"json\",query:{f:\"json\"}});if(o.data){const r=o.data;return r.error?null:r}return null}export{t as getPortal,s as lookupUser};\n"], "mappings": ";;;;;;;;;;;AAIkG,SAAS,EAAEA,IAAE,GAAE;AAAC,MAAG,SAAOA,GAAE,QAAO;AAAE,SAAO,IAAI,EAAE,EAAC,KAAIA,GAAE,MAAM,KAAK,EAAC,CAAC;AAAC;AAAC,eAAe,EAAE,GAAEC,IAAEC,IAAE;AAJjM;AAIkM,QAAM,KAAE,8BAAG,eAAe,EAAE;AAAS,MAAG,CAAC,EAAE,QAAO;AAAK,MAAG,aAAW,EAAE,cAAY,OAAKD,MAAG,EAAE,QAAM,EAAE,KAAK,cAAY,UAAKC,GAAE,QAAO,EAAE,KAAK;AAAW,MAAG,OAAKD,IAAE;AAAC,UAAMD,KAAE,MAAM,EAAE,EAAE,UAAQ,mBAAkB,EAAC,cAAa,QAAO,OAAM,EAAC,GAAE,QAAO,GAAG,UAAKE,KAAE,CAAC,IAAE,EAAC,iCAAgC,KAAE,EAAC,EAAC,CAAC;AAAE,QAAGF,GAAE,MAAK;AAAC,YAAM,IAAEA,GAAE;AAAK,UAAG,KAAG,EAAE,SAAS,QAAO;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAC,QAAM,IAAE,MAAM,EAAE,EAAE,UAAQ,sBAAoBC,IAAE,EAAC,cAAa,QAAO,OAAM,EAAC,GAAE,OAAM,EAAC,CAAC;AAAE,MAAG,EAAE,MAAK;AAAC,UAAMD,KAAE,EAAE;AAAK,WAAOA,GAAE,QAAM,OAAKA;AAAA,EAAC;AAAC,SAAO;AAAI;", "names": ["r", "t", "s"]}