import {
  b
} from "./chunk-QMNV7QQK.js";
import {
  d
} from "./chunk-22HLMDJ5.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  Ct,
  K
} from "./chunk-U4SVMKOQ.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/support/featureFlags.js
var e = () => !!has("enable-feature:force-wosr");
var t = () => !!has("enable-feature:SceneLayer-editing");

// node_modules/@arcgis/core/symbols/support/styleUtils.js
var f = {};
async function m(e2, r2) {
  try {
    return { data: (await j(e2, r2)).data, baseUrl: Ct(e2), styleUrl: e2 };
  } catch (t2) {
    return w(t2), null;
  }
}
function i(e2, r2, o) {
  const s2 = r(r2.portal) ? r2.portal : b.getDefault();
  let n;
  const a = `${s2.url} - ${s2.user && s2.user.username} - ${e2}`;
  return f[a] || (f[a] = c(e2, s2, o).then((e3) => (n = e3, e3.fetchData())).then((r3) => ({ data: r3, baseUrl: n.itemUrl ?? "", styleName: e2 }))), f[a];
}
function c(e2, t2, o) {
  return t2.load(o).then(() => {
    const r2 = new d({ disableExtraQuery: true, query: `owner:${h} AND type:${w2} AND typekeywords:"${e2}"` });
    return t2.queryItems(r2, o);
  }).then(({ results: t3 }) => {
    var _a;
    let s2 = null;
    const n = e2.toLowerCase();
    if (t3 && Array.isArray(t3)) for (const e3 of t3) {
      const r2 = (_a = e3.typeKeywords) == null ? void 0 : _a.some((e4) => e4.toLowerCase() === n);
      if (r2 && e3.type === w2 && e3.owner === h) {
        s2 = e3;
        break;
      }
    }
    if (!s2) throw new s("symbolstyleutils:style-not-found", `The style '${e2}' could not be found`, { styleName: e2 });
    return s2.load(o);
  });
}
function p(e2, o, s2) {
  return e2 && r(e2.styleUrl) ? m(e2.styleUrl, s2) : e2 && r(e2.styleName) ? i(e2.styleName, o, s2) : Promise.reject(new s("symbolstyleutils:style-url-and-name-missing", "Either styleUrl or styleName is required to resolve a style"));
}
function d2(e2) {
  return null === e2 || "CIMSymbolReference" === e2.type ? e2 : { type: "CIMSymbolReference", symbol: e2 };
}
function b2(e2, r2) {
  if ("cimRef" === r2) return e2.cimRef;
  if (e2.formatInfos && !e()) {
    for (const t2 of e2.formatInfos) if ("gltf" === t2.type) return t2.href;
  }
  return e2.webRef;
}
function j(r2, t2) {
  const o = { responseType: "json", query: { f: "json" }, ...t2 };
  return U(K(r2), o);
}
var h = "esri_en";
var w2 = "Style";
var U2 = "https://cdn.arcgis.com/sharing/rest/content/items/220936cc6ed342c9937abd8f180e7d1e/resources/styles/cim/{SymbolName}.json?f=json";

export {
  t,
  p,
  d2 as d,
  b2 as b,
  j,
  U2 as U
};
//# sourceMappingURL=chunk-WJPDYSRI.js.map
