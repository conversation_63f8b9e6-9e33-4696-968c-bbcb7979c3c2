{"version": 3, "sources": ["../../@arcgis/core/layers/support/ExpressionInfo.js", "../../@arcgis/core/layers/support/AggregateField.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{ClonableMixin as o}from\"../../core/Clonable.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as e}from\"../../core/accessorSupport/decorators/subclass.js\";let p=class extends(o(t)){constructor(r){super(r),this.expression=null,this.title=null,this.returnType=null}};r([s({type:String,json:{write:!0}})],p.prototype,\"expression\",void 0),r([s({type:String,json:{write:!0}})],p.prototype,\"title\",void 0),r([s({type:String,json:{write:!0}})],p.prototype,\"returnType\",void 0),p=r([e(\"esri.layers.support.ExpressionInfo\")],p);const i=p;export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{JSONSupport as s}from\"../../core/JSONSupport.js\";import{clone as o}from\"../../core/lang.js\";import{property as i}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import{subclass as e}from\"../../core/accessorSupport/decorators/subclass.js\";import r from\"./ExpressionInfo.js\";var p;let n=p=class extends s{constructor(t){super(t),this.isAutoGenerated=!1,this.name=null,this.alias=null,this.onStatisticField=null,this.onStatisticExpression=null,this.statisticType=null}clone(){return new p({name:this.name,alias:this.alias,isAutoGenerated:this.isAutoGenerated,onStatisticExpression:o(this.onStatisticExpression),onStatisticField:this.onStatisticField,statisticType:this.statisticType})}};t([i({type:Boolean,json:{write:!0}})],n.prototype,\"isAutoGenerated\",void 0),t([i({type:String,json:{write:!0}})],n.prototype,\"name\",void 0),t([i({type:String,json:{write:!0}})],n.prototype,\"alias\",void 0),t([i({type:String,json:{write:!0}})],n.prototype,\"onStatisticField\",void 0),t([i({type:r,json:{write:!0}})],n.prototype,\"onStatisticExpression\",void 0),t([i({type:String,json:{write:!0}})],n.prototype,\"statisticType\",void 0),n=p=t([e(\"esri.layers.support.AggregateField\")],n);const a=n;export{a as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAIyY,IAAIA,KAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,aAAW,MAAK,KAAK,QAAM,MAAK,KAAK,aAAW;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,GAAEA,EAAC;AAAE,IAAMC,KAAED;;;ACA9X,IAAIE;AAAE,IAAI,IAAEA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,kBAAgB,OAAG,KAAK,OAAK,MAAK,KAAK,QAAM,MAAK,KAAK,mBAAiB,MAAK,KAAK,wBAAsB,MAAK,KAAK,gBAAc;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIA,GAAE,EAAC,MAAK,KAAK,MAAK,OAAM,KAAK,OAAM,iBAAgB,KAAK,iBAAgB,uBAAsB,EAAE,KAAK,qBAAqB,GAAE,kBAAiB,KAAK,kBAAiB,eAAc,KAAK,cAAa,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,IAAED,KAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,GAAE,CAAC;AAAE,IAAME,KAAE;", "names": ["p", "i", "p", "i", "a"]}