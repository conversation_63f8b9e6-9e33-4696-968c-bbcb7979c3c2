{"version": 3, "sources": ["../../@arcgis/core/support/basemapUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../Basemap.js\";import r from\"../config.js\";import a from\"../core/Collection.js\";import t from\"../core/Logger.js\";import{isNone as n,isSome as i}from\"../core/maybe.js\";import{Url as s,makeAbsolute as l,normalize as u}from\"../core/urlUtils.js\";import{ensureType as c}from\"../core/accessorSupport/ensureType.js\";import{esriBasemapDefinitions as o}from\"./basemapDefinitions.js\";const f=t.getLogger(\"esri.support.basemapUtils\");function p(){return{}}function y(e){for(const r in e){const a=e[r];!1===a?.destroyed&&a.destroy(),delete e[r]}}function m(a,t){let n;if(\"string\"==typeof a){if(!(a in o)){const e=Object.entries(o).filter((([e,a])=>r.apiKey&&!a.classic||!r.apiKey&&a.classic&&!a.deprecated)).map((([e])=>`\"${e}\"`)).join(\", \");return f.warn(`Unable to find basemap definition for: ${a}. Try one of these: ${e}`),null}t&&(n=t[a]),n||(n=e.fromId(a),t&&(t[a]=n))}else n=c(e,a);return n?.destroyed&&(f.warn(\"The provided basemap is already destroyed\",{basemap:n}),n=null),n}function d(r,a=null){const t=m(r);if(!t)return null;const n=new e({id:t.id,title:t.title,baseLayers:t.baseLayers.slice(),referenceLayers:t.referenceLayers.slice()});return a&&(n.baseLayers=g(n.baseLayers,a.baseLayers),n.referenceLayers=g(n.referenceLayers,a.referenceLayers)),n.load().catch((()=>{})),n.portalItem=t.portalItem,n}function b(e){let r=null;const a=T(e),t=!a?.baseLayers.length;for(const n in o){const e=M(a,q(o[n]),{mustMatchReferences:t});if(\"equal\"===e){r=n;break}\"base-layers-equal\"===e&&(r=n)}return r}function L(e,r){if(e===r)return!0;return\"equal\"===M(T(e),T(r),{mustMatchReferences:!0})}function g(e,r){const t=new a;return e.forEach((e=>{const a=r.find((r=>$(j(e),j(r))))||e;t.includes(a)?t.push(e):t.push(a)})),t}function v(e){return!!e?.baseLayers.concat(e.referenceLayers).some(S)}function S(e){if(I(e.url))return!0;if(\"vector-tile\"===e.type)for(const r in e.sourceNameToSource){if(I(e.sourceNameToSource[r]?.sourceUrl))return!0}return!1}function h(e,r){if(n(r)||n(e))return{spatialReference:null,updating:!1};if(\"not-loaded\"===r.loadStatus)return r.load(),{spatialReference:null,updating:!0};if(r.spatialReference)return{spatialReference:r.spatialReference,updating:!1};if(0===r.baseLayers.length)return{spatialReference:null,updating:!1};const a=r.baseLayers.getItemAt(0);switch(a.loadStatus){case\"not-loaded\":a.load();case\"loading\":return{spatialReference:null,updating:!0};case\"failed\":return{spatialReference:null,updating:!1}}const t=((\"supportedSpatialReferences\"in a?a.supportedSpatialReferences:null)||[\"tileInfo\"in a?a.tileInfo?.spatialReference:a.spatialReference]).filter(Boolean),i=e.spatialReference;return i?{spatialReference:t.find((e=>i.equals(e)))??t[0]??null,updating:!1}:{spatialReference:t[0],updating:!1}}const R=/^(basemaps|ibasemaps).*-api\\.arcgis\\.com$/i;function I(e){if(!e)return!1;const r=new s(l(e));return!!r.authority&&R.test(r.authority)}function T(e){return e?!e.loaded&&e.resourceInfo?q(e.resourceInfo.data):{baseLayers:w(e.baseLayers),referenceLayers:w(e.referenceLayers)}:null}function w(e){return(a.isCollection(e)?e.toArray():e).map(j)}function j(e){return{type:e.type,url:A(\"urlTemplate\"in e&&e.urlTemplate||e.url||\"styleUrl\"in e&&e.styleUrl||\"\"),minScale:\"minScale\"in e&&null!=e.minScale?e.minScale:0,maxScale:\"maxScale\"in e&&null!=e.maxScale?e.maxScale:0,opacity:null!=e.opacity?e.opacity:1,visible:null==e.visible||!!e.visible,sublayers:\"map-image\"!==e.type&&\"wms\"!==e.type||!i(e.sublayers)?void 0:e.sublayers?.map((e=>({id:e.id,visible:e.visible}))),activeLayerId:\"wmts\"===e.type?e.activeLayer?.id:void 0}}function q(e){return e?{baseLayers:x((e.baseMapLayers??[]).filter((e=>!e.isReference))),referenceLayers:x((e.baseMapLayers??[]).filter((e=>e.isReference)))}:null}function x(e){return e.map((e=>U(e)))}function U(e){let r;switch(e.layerType){case\"VectorTileLayer\":r=\"vector-tile\";break;case\"ArcGISTiledMapServiceLayer\":r=\"tile\";break;default:r=\"unknown\"}return{type:r,url:A(e.templateUrl||e.urlTemplate||e.styleUrl||e.url),minScale:null!=e.minScale?e.minScale:0,maxScale:null!=e.maxScale?e.maxScale:0,opacity:null!=e.opacity?e.opacity:1,visible:null==e.visibility||!!e.visibility,sublayers:void 0,activeLayerId:void 0}}function M(e,r,a){if(null!=e!=(null!=r))return\"not-equal\";if(!e||!r)return\"equal\";if(!k(e.baseLayers,r.baseLayers))return\"not-equal\";return k(e.referenceLayers,r.referenceLayers)?\"equal\":a.mustMatchReferences?\"not-equal\":\"base-layers-equal\"}function k(e,r){if(e.length!==r.length)return!1;for(let a=0;a<e.length;a++)if(!$(e[a],r[a]))return!1;return!0}function $(e,r){if(e.type!==r.type||\"scene\"===e.type||e.url!==r.url||e.minScale!==r.minScale||e.maxScale!==r.maxScale||e.visible!==r.visible||e.opacity!==r.opacity)return!1;if(i(e.activeLayerId)||i(r.activeLayerId))return e.activeLayerId===r.activeLayerId;if(i(e.sublayers)||i(r.sublayers)){if(n(e.sublayers)||n(r.sublayers)||e.sublayers.length!==r.sublayers.length)return!1;for(let a=0;a<e.sublayers.length;a++){const t=e.sublayers.at(a),n=r.sublayers.at(a);if(t?.id!==n?.id||t?.visible!==n?.visible)return!1}}return!0}function A(e){return e?u(e).replace(/^\\s*https?:/i,\"\").toLowerCase():\"\"}export{d as clonePreservingTiledLayers,L as contentEquals,p as createCache,y as destroyCache,m as ensureType,h as findSpatialReference,b as getWellKnownBasemapId,v as hasDeveloperBasemapLayer,S as isDeveloperBasemapLayer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAIoY,IAAM,IAAEA,GAAE,UAAU,2BAA2B;AAAE,SAAS,IAAG;AAAC,SAAM,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,aAAUC,MAAK,GAAE;AAAC,UAAM,IAAE,EAAEA,EAAC;AAAE,eAAK,uBAAG,cAAW,EAAE,QAAQ,GAAE,OAAO,EAAEA,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEC,IAAE;AAAC,MAAI;AAAE,MAAG,YAAU,OAAO,GAAE;AAAC,QAAG,EAAE,KAAKF,KAAG;AAAC,YAAM,IAAE,OAAO,QAAQA,EAAC,EAAE,OAAQ,CAAC,CAACG,IAAEC,EAAC,MAAI,EAAE,UAAQ,CAACA,GAAE,WAAS,CAAC,EAAE,UAAQA,GAAE,WAAS,CAACA,GAAE,UAAW,EAAE,IAAK,CAAC,CAACD,EAAC,MAAI,IAAIA,EAAC,GAAI,EAAE,KAAK,IAAI;AAAE,aAAO,EAAE,KAAK,0CAA0C,CAAC,uBAAuB,CAAC,EAAE,GAAE;AAAA,IAAI;AAAC,IAAAD,OAAI,IAAEA,GAAE,CAAC,IAAG,MAAI,IAAE,EAAE,OAAO,CAAC,GAAEA,OAAIA,GAAE,CAAC,IAAE;AAAA,EAAG,MAAM,KAAE,EAAE,GAAE,CAAC;AAAE,UAAO,uBAAG,eAAY,EAAE,KAAK,6CAA4C,EAAC,SAAQ,EAAC,CAAC,GAAE,IAAE,OAAM;AAAC;AAAygB,SAAS,EAAE,GAAEG,IAAE;AAAC,MAAG,MAAIA,GAAE,QAAM;AAAG,SAAM,YAAU,EAAE,EAAE,CAAC,GAAE,EAAEA,EAAC,GAAE,EAAC,qBAAoB,KAAE,CAAC;AAAC;AAAoW,SAAS,EAAE,GAAEC,IAAE;AAJ76D;AAI86D,MAAG,EAAEA,EAAC,KAAG,EAAE,CAAC,EAAE,QAAM,EAAC,kBAAiB,MAAK,UAAS,MAAE;AAAE,MAAG,iBAAeA,GAAE,WAAW,QAAOA,GAAE,KAAK,GAAE,EAAC,kBAAiB,MAAK,UAAS,KAAE;AAAE,MAAGA,GAAE,iBAAiB,QAAM,EAAC,kBAAiBA,GAAE,kBAAiB,UAAS,MAAE;AAAE,MAAG,MAAIA,GAAE,WAAW,OAAO,QAAM,EAAC,kBAAiB,MAAK,UAAS,MAAE;AAAE,QAAM,IAAEA,GAAE,WAAW,UAAU,CAAC;AAAE,UAAO,EAAE,YAAW;AAAA,IAAC,KAAI;AAAa,QAAE,KAAK;AAAA,IAAE,KAAI;AAAU,aAAM,EAAC,kBAAiB,MAAK,UAAS,KAAE;AAAA,IAAE,KAAI;AAAS,aAAM,EAAC,kBAAiB,MAAK,UAAS,MAAE;AAAA,EAAC;AAAC,QAAMC,OAAI,gCAA+B,IAAE,EAAE,6BAA2B,SAAO,CAAC,cAAa,KAAE,OAAE,aAAF,mBAAY,mBAAiB,EAAE,gBAAgB,GAAG,OAAO,OAAO,GAAE,IAAE,EAAE;AAAiB,SAAO,IAAE,EAAC,kBAAiBA,GAAE,KAAM,CAAAC,OAAG,EAAE,OAAOA,EAAC,CAAE,KAAGD,GAAE,CAAC,KAAG,MAAK,UAAS,MAAE,IAAE,EAAC,kBAAiBA,GAAE,CAAC,GAAE,UAAS,MAAE;AAAC;AAAgJ,SAAS,EAAE,GAAE;AAAC,SAAO,IAAE,CAAC,EAAE,UAAQ,EAAE,eAAa,EAAE,EAAE,aAAa,IAAI,IAAE,EAAC,YAAW,EAAE,EAAE,UAAU,GAAE,iBAAgB,EAAE,EAAE,eAAe,EAAC,IAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,UAAO,EAAE,aAAa,CAAC,IAAE,EAAE,QAAQ,IAAE,GAAG,IAAIE,EAAC;AAAC;AAAC,SAASA,GAAE,GAAE;AAJ3hG;AAI4hG,SAAM,EAAC,MAAK,EAAE,MAAK,KAAI,EAAE,iBAAgB,KAAG,EAAE,eAAa,EAAE,OAAK,cAAa,KAAG,EAAE,YAAU,EAAE,GAAE,UAAS,cAAa,KAAG,QAAM,EAAE,WAAS,EAAE,WAAS,GAAE,UAAS,cAAa,KAAG,QAAM,EAAE,WAAS,EAAE,WAAS,GAAE,SAAQ,QAAM,EAAE,UAAQ,EAAE,UAAQ,GAAE,SAAQ,QAAM,EAAE,WAAS,CAAC,CAAC,EAAE,SAAQ,WAAU,gBAAc,EAAE,QAAM,UAAQ,EAAE,QAAM,CAAC,EAAE,EAAE,SAAS,IAAE,UAAO,OAAE,cAAF,mBAAa,IAAK,CAAAC,QAAI,EAAC,IAAGA,GAAE,IAAG,SAAQA,GAAE,QAAO,KAAK,eAAc,WAAS,EAAE,QAAK,OAAE,gBAAF,mBAAe,KAAG,OAAM;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,IAAE,EAAC,YAAWC,IAAG,EAAE,iBAAe,CAAC,GAAG,OAAQ,CAAAD,OAAG,CAACA,GAAE,WAAY,CAAC,GAAE,iBAAgBC,IAAG,EAAE,iBAAe,CAAC,GAAG,OAAQ,CAAAD,OAAGA,GAAE,WAAY,CAAC,EAAC,IAAE;AAAI;AAAC,SAASC,GAAE,GAAE;AAAC,SAAO,EAAE,IAAK,CAAAD,OAAG,EAAEA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAIE;AAAE,UAAO,EAAE,WAAU;AAAA,IAAC,KAAI;AAAkB,MAAAA,KAAE;AAAc;AAAA,IAAM,KAAI;AAA6B,MAAAA,KAAE;AAAO;AAAA,IAAM;AAAQ,MAAAA,KAAE;AAAA,EAAS;AAAC,SAAM,EAAC,MAAKA,IAAE,KAAI,EAAE,EAAE,eAAa,EAAE,eAAa,EAAE,YAAU,EAAE,GAAG,GAAE,UAAS,QAAM,EAAE,WAAS,EAAE,WAAS,GAAE,UAAS,QAAM,EAAE,WAAS,EAAE,WAAS,GAAE,SAAQ,QAAM,EAAE,UAAQ,EAAE,UAAQ,GAAE,SAAQ,QAAM,EAAE,cAAY,CAAC,CAAC,EAAE,YAAW,WAAU,QAAO,eAAc,OAAM;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,MAAG,QAAM,MAAI,QAAMA,IAAG,QAAM;AAAY,MAAG,CAAC,KAAG,CAACA,GAAE,QAAM;AAAQ,MAAG,CAAC,EAAE,EAAE,YAAWA,GAAE,UAAU,EAAE,QAAM;AAAY,SAAO,EAAE,EAAE,iBAAgBA,GAAE,eAAe,IAAE,UAAQ,EAAE,sBAAoB,cAAY;AAAmB;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAG,EAAE,WAASA,GAAE,OAAO,QAAM;AAAG,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,KAAG,CAACC,GAAE,EAAE,CAAC,GAAED,GAAE,CAAC,CAAC,EAAE,QAAM;AAAG,SAAM;AAAE;AAAC,SAASC,GAAE,GAAED,IAAE;AAAC,MAAG,EAAE,SAAOA,GAAE,QAAM,YAAU,EAAE,QAAM,EAAE,QAAMA,GAAE,OAAK,EAAE,aAAWA,GAAE,YAAU,EAAE,aAAWA,GAAE,YAAU,EAAE,YAAUA,GAAE,WAAS,EAAE,YAAUA,GAAE,QAAQ,QAAM;AAAG,MAAG,EAAE,EAAE,aAAa,KAAG,EAAEA,GAAE,aAAa,EAAE,QAAO,EAAE,kBAAgBA,GAAE;AAAc,MAAG,EAAE,EAAE,SAAS,KAAG,EAAEA,GAAE,SAAS,GAAE;AAAC,QAAG,EAAE,EAAE,SAAS,KAAG,EAAEA,GAAE,SAAS,KAAG,EAAE,UAAU,WAASA,GAAE,UAAU,OAAO,QAAM;AAAG,aAAQ,IAAE,GAAE,IAAE,EAAE,UAAU,QAAO,KAAI;AAAC,YAAME,KAAE,EAAE,UAAU,GAAG,CAAC,GAAE,IAAEF,GAAE,UAAU,GAAG,CAAC;AAAE,WAAGE,MAAA,gBAAAA,GAAG,SAAK,uBAAG,QAAIA,MAAA,gBAAAA,GAAG,cAAU,uBAAG,SAAQ,QAAM;AAAA,IAAE;AAAA,EAAC;AAAC,SAAM;AAAE;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,IAAE,EAAE,CAAC,EAAE,QAAQ,gBAAe,EAAE,EAAE,YAAY,IAAE;AAAE;", "names": ["s", "r", "t", "e", "a", "r", "r", "t", "e", "j", "e", "x", "r", "$", "t"]}