import {
  e as e5,
  i,
  o as o6,
  p,
  s,
  s2,
  t as t3
} from "./chunk-A4XIY547.js";
import {
  o as o4
} from "./chunk-Q6BEUTMN.js";
import {
  n as n2
} from "./chunk-Y424ZXTG.js";
import {
  e as e4
} from "./chunk-UB5FTTH5.js";
import {
  n,
  t as t2
} from "./chunk-6GW7M2AQ.js";
import {
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  d,
  v
} from "./chunk-N3S5O3YO.js";
import {
  e as e3
} from "./chunk-JETZLJ6M.js";
import {
  o as o3
} from "./chunk-BPRRRPC3.js";
import {
  e
} from "./chunk-GXMOAZWH.js";
import {
  a
} from "./chunk-6OHWWYET.js";
import {
  o as o5
} from "./chunk-TUB4N6LD.js";
import {
  e as e2,
  f
} from "./chunk-YV4RKNU4.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";

// node_modules/@arcgis/core/chunks/LineMarker.glsl.js
function C(C2) {
  const M2 = new o2(), D = C2.hasMultipassTerrain && (C2.output === h.Color || C2.output === h.Alpha), k = C2.space === p.World;
  C2.hasTip && k && M2.extensions.add("GL_OES_standard_derivatives"), M2.include(s2, C2), M2.include(i, C2), C2.output === h.Depth && M2.include(o4, C2);
  const { vertex: N, fragment: T } = M2;
  return T.include(a), v(N, C2), M2.attributes.add(O.POSITION, "vec3"), M2.attributes.add(O.UV0, "vec2"), M2.attributes.add(O.AUXPOS1, "vec3"), M2.varyings.add("vColor", "vec4"), M2.varyings.add("vpos", "vec3"), M2.varyings.add("vUV", "vec2"), M2.varyings.add("vSize", "float"), t2(M2), D && M2.varyings.add("depth", "float"), C2.hasTip && M2.varyings.add("vLineWidth", "float"), N.uniforms.add([new e2("nearFar", (e6, r) => r.camera.nearFar), new e("viewport", (e6, r) => r.camera.fullViewport)]), N.code.add(o`vec4 projectAndScale(vec4 pos) {
vec4 posNdc = proj * pos;
posNdc.xy *= viewport.zw / posNdc.w;
return posNdc;
}`), N.code.add(o`void clip(vec4 pos, inout vec4 prev) {
float vnp = nearFar[0] * 0.99;
if (prev.z > -nearFar[0]) {
float interpolation = (-vnp - pos.z) / (prev.z - pos.z);
prev = mix(pos, prev, interpolation);
}
}`), k ? (M2.attributes.add(O.NORMAL, "vec3"), d(N), N.constants.add("tiltThreshold", "float", 0.7), N.code.add(o`vec3 perpendicular(vec3 v) {
vec3 n = (viewNormal * vec4(normal.xyz, 1.0)).xyz;
vec3 n2 = cross(v, n);
vec3 forward = vec3(0.0, 0.0, 1.0);
float tiltDot = dot(forward, n);
return abs(tiltDot) < tiltThreshold ? n : n2;
}`)) : N.code.add(o`vec2 perpendicular(vec2 v) {
return vec2(v.y, -v.x);
}`), N.code.add(o`
      #define vecN ${k ? "vec3" : "vec2"}

      vecN normalizedSegment(vecN pos, vecN prev) {
        vecN segment = pos - prev;
        float segmentLen = length(segment);

        // normalize or zero if too short
        return (segmentLen > 0.001) ? segment / segmentLen : ${k ? "vec3(0.0, 0.0, 0.0)" : "vec2(0.0, 0.0)"};
      }

      vecN displace(vecN pos, vecN prev, float displacementLen) {
        vecN segment = normalizedSegment(pos, prev);

        vecN displacementDirU = perpendicular(segment);
        vecN displacementDirV = segment;

        ${C2.anchor === s.Tip ? "pos -= 0.5 * displacementLen * displacementDirV;" : ""}

        return pos + displacementLen * (uv0.x * displacementDirU + uv0.y * displacementDirV);
      }
    `), C2.space === p.Screen && (N.uniforms.add(new e3("inverseProjectionMatrix", (e6, r) => r.camera.inverseProjectionMatrix)), N.code.add(o`vec3 inverseProject(vec4 posScreen) {
posScreen.xy = (posScreen.xy / viewport.zw) * posScreen.w;
return (inverseProjectionMatrix * posScreen).xyz;
}`), N.code.add(o`bool rayIntersectPlane(vec3 rayDir, vec3 planeOrigin, vec3 planeNormal, out vec3 intersection) {
float cos = dot(rayDir, planeNormal);
float t = dot(planeOrigin, planeNormal) / cos;
intersection = t * rayDir;
return abs(cos) > 0.001 && t > 0.0;
}`), N.uniforms.add(new o5("perScreenPixelRatio", (e6, r) => r.camera.perScreenPixelRatio)), N.code.add(o`
      vec4 toFront(vec4 displacedPosScreen, vec3 posLeft, vec3 posRight, vec3 prev, float lineWidth) {
        // Project displaced position back to camera space
        vec3 displacedPos = inverseProject(displacedPosScreen);

        // Calculate the plane that we want the marker to lie in. Note that this will always be an approximation since ribbon lines are generally
        // not planar and we do not know the actual position of the displaced prev vertices (they are offset in screen space, too).
        vec3 planeNormal = normalize(cross(posLeft - posRight, posLeft - prev));
        vec3 planeOrigin = posLeft;

        ${C2.hasCap ? "\n                if(prev.z > posLeft.z) {\n                  vec2 diff = posLeft.xy - posRight.xy;\n                  planeOrigin.xy += perpendicular(diff) / 2.0;\n                }\n              " : ""};

        // Move the plane towards the camera by a margin dependent on the line width (approximated in world space). This tolerance corrects for the
        // non-planarity in most cases, but sharp joins can place the prev vertices at arbitrary positions so markers can still clip.
        float offset = lineWidth * perScreenPixelRatio;
        planeOrigin *= (1.0 - offset);

        // Intersect camera ray with the plane and make sure it is within clip space
        vec3 rayDir = normalize(displacedPos);
        vec3 intersection;
        if (rayIntersectPlane(rayDir, planeOrigin, planeNormal, intersection) && intersection.z < -nearFar[0] && intersection.z > -nearFar[1]) {
          return vec4(intersection.xyz, 1.0);
        }

        // Fallback: use depth of pos or prev, whichever is closer to the camera
        float minDepth = planeOrigin.z > prev.z ? length(planeOrigin) : length(prev);
        displacedPos *= minDepth / length(displacedPos);
        return vec4(displacedPos.xyz, 1.0);
      }
  `)), N.uniforms.add(new o5("pixelRatio", (e6, r) => r.camera.pixelRatio)), n(M2), N.code.add(o`void main(void) {
if (uv0.y == 0.0) {
gl_Position = vec4(1e038, 1e038, 1e038, 1.0);
}
else {
float lineWidth = getLineWidth();
float screenMarkerSize = getScreenMarkerSize();
vec4 pos  = view * vec4(position.xyz, 1.0);
vec4 prev = view * vec4(auxpos1.xyz, 1.0);
clip(pos, prev);`), k ? (C2.hideOnShortSegments && N.code.add(o`if (areWorldMarkersHidden(pos, prev)) {
gl_Position = vec4(1e038, 1e038, 1e038, 1.0);
return;
}`), N.code.add(o`pos.xyz = displace(pos.xyz, prev.xyz, getWorldMarkerSize(pos));
vec4 displacedPosScreen = projectAndScale(pos);`)) : (N.code.add(o`vec4 posScreen = projectAndScale(pos);
vec4 prevScreen = projectAndScale(prev);
vec4 displacedPosScreen = posScreen;
displacedPosScreen.xy = displace(posScreen.xy, prevScreen.xy, screenMarkerSize);`), C2.space === p.Screen && N.code.add(o`vec2 displacementDirU = perpendicular(normalizedSegment(posScreen.xy, prevScreen.xy));
vec3 lineRight = inverseProject(posScreen + lineWidth * vec4(displacementDirU.xy, 0.0, 0.0));
vec3 lineLeft = pos.xyz + (pos.xyz - lineRight);
pos = toFront(displacedPosScreen, lineLeft, lineRight, prev.xyz, lineWidth);
displacedPosScreen = projectAndScale(pos);`)), N.code.add(o`
        ${D ? "depth = pos.z;" : ""}
        linearDepth = calculateLinearDepth(nearFar,pos.z);

        // Convert back into NDC
        displacedPosScreen.xy = (displacedPosScreen.xy / viewport.zw) * displacedPosScreen.w;

        // Convert texture coordinate into [0,1]
        vUV = (uv0 + 1.0) / 2.0;

        ${k ? "" : "vUV *= displacedPosScreen.w;"}

        ${C2.hasTip ? "vLineWidth = lineWidth;" : ""}

        vSize = screenMarkerSize;
        vColor = getColor();

        // Use camera space for slicing
        vpos = pos.xyz;

        gl_Position = displacedPosScreen;
      }
    }
  `), D && M2.include(n2, C2), M2.include(u, C2), T.uniforms.add([new e("intrinsicColor", (e6) => e6.color), new f("tex", (e6) => e6.texture)]), T.include(e4), M2.constants.add("texelSize", "float", 1 / t3), T.code.add(o`float markerAlpha(vec2 samplePos) {
samplePos += vec2(0.5, -0.5) * texelSize;
float sdf = rgba2float(texture2D(tex, samplePos)) - 0.5;
float distance = sdf * vSize;
distance -= 0.5;
return clamp(0.5 - distance, 0.0, 1.0);
}`), C2.hasTip && (M2.constants.add("relativeMarkerSize", "float", o6 / t3), M2.constants.add("relativeTipLineWidth", "float", e5), T.code.add(o`
    float tipAlpha(vec2 samplePos) {
      // Convert coordinates s.t. they are in pixels and relative to the tip of an arrow marker
      samplePos -= vec2(0.5, 0.5 + 0.5 * relativeMarkerSize);
      samplePos *= vSize;

      float halfMarkerSize = 0.5 * relativeMarkerSize * vSize;
      float halfTipLineWidth = 0.5 * max(1.0, relativeTipLineWidth * vLineWidth);

      ${k ? "halfTipLineWidth *= fwidth(samplePos.y);" : ""}

      float distance = max(abs(samplePos.x) - halfMarkerSize, abs(samplePos.y) - halfTipLineWidth);
      return clamp(0.5 - distance, 0.0, 1.0);
    }
  `)), M2.constants.add("symbolAlphaCutoff", "float", t), T.code.add(o`
  void main() {
    discardBySlice(vpos);
    ${D ? "terrainDepthTest(gl_FragCoord, depth);" : ""}

    vec4 finalColor = intrinsicColor * vColor;

    ${k ? "vec2 samplePos = vUV;" : "vec2 samplePos = vUV * gl_FragCoord.w;"}

    ${C2.hasTip ? "finalColor.a *= max(markerAlpha(samplePos), tipAlpha(samplePos));" : "finalColor.a *= markerAlpha(samplePos);"}

    ${C2.output === h.ObjectAndLayerIdColor ? o`finalColor.a = 1.0;` : ""}

    if (finalColor.a < symbolAlphaCutoff) {
      discard;
    }

    ${C2.output === h.Alpha ? o`gl_FragColor = vec4(finalColor.a);` : ""}
    ${C2.output === h.Color ? o`gl_FragColor = highlightSlice(finalColor, vpos);` : ""}
    ${C2.output === h.Color && C2.transparencyPassType === o3.Color ? "gl_FragColor = premultiplyAlpha(gl_FragColor);" : ""}
    ${C2.output === h.Highlight ? o`gl_FragColor = vec4(1.0);` : ""}
    ${C2.output === h.Depth ? o`outputDepth(linearDepth);` : ""}
  }
  `), M2;
}
var M = Object.freeze(Object.defineProperty({ __proto__: null, build: C }, Symbol.toStringTag, { value: "Module" }));

export {
  C,
  M
};
//# sourceMappingURL=chunk-NVZWP4JL.js.map
