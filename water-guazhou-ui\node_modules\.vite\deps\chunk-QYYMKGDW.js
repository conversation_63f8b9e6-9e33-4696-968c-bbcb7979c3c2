import {
  v
} from "./chunk-FUIIMETN.js";
import {
  f
} from "./chunk-U4SDSCWW.js";
import {
  W
} from "./chunk-B4KDIR4O.js";
import {
  t
} from "./chunk-SEO6KEGF.js";
import {
  c,
  u,
  y
} from "./chunk-ZACBBT3Y.js";
import {
  Z,
  i,
  o,
  r
} from "./chunk-XTO3XXZ3.js";
import {
  I
} from "./chunk-JXLVNWKF.js";
import {
  s
} from "./chunk-4RZONHOY.js";

// node_modules/@arcgis/core/geometry/support/intersects.js
function s2(s3) {
  return "mesh" === s3 ? o : Z(s3);
}

// node_modules/@arcgis/core/layers/graphics/contains.js
function n(n3, t4) {
  return n3 ? t4 ? 4 : 3 : t4 ? 3 : 2;
}
function t2(n3, t4, r3, e3) {
  return o2(n3, t4, r3, e3.coords[0], e3.coords[1]);
}
function r2(t4, r3, e3, c3, u3, f3) {
  const s3 = n(u3, f3), { coords: i3, lengths: l2 } = c3;
  if (!l2) return false;
  for (let n3 = 0, d = 0; n3 < l2.length; n3++, d += s3) if (!o2(t4, r3, e3, i3[d], i3[d + 1])) return false;
  return true;
}
function o2(t4, r3, o3, c3, u3) {
  if (!t4) return false;
  const f3 = n(r3, o3), { coords: s3, lengths: i3 } = t4;
  let l2 = false, d = 0;
  for (const n3 of i3) l2 = e(l2, s3, f3, d, n3, c3, u3), d += n3 * f3;
  return l2;
}
function e(n3, t4, r3, o3, e3, c3, u3) {
  let f3 = n3, s3 = o3;
  for (let i3 = o3, l2 = o3 + e3 * r3; i3 < l2; i3 += r3) {
    s3 = i3 + r3, s3 === l2 && (s3 = o3);
    const n4 = t4[i3], e4 = t4[i3 + 1], d = t4[s3], g2 = t4[s3 + 1];
    (e4 < u3 && g2 >= u3 || g2 < u3 && e4 >= u3) && n4 + (u3 - e4) / (g2 - e4) * (d - n4) < c3 && (f3 = !f3);
  }
  return f3;
}

// node_modules/@arcgis/core/layers/graphics/data/spatialQuerySupport.js
var c2 = "feature-store:unsupported-query";
var R = { esriSpatialRelIntersects: "intersects", esriSpatialRelContains: "contains", esriSpatialRelCrosses: "crosses", esriSpatialRelDisjoint: "disjoint", esriSpatialRelEnvelopeIntersects: "intersects", esriSpatialRelIndexIntersects: null, esriSpatialRelOverlaps: "overlaps", esriSpatialRelTouches: "touches", esriSpatialRelWithin: "within", esriSpatialRelRelation: null };
var S = { spatialRelationship: { esriSpatialRelIntersects: true, esriSpatialRelContains: true, esriSpatialRelWithin: true, esriSpatialRelCrosses: true, esriSpatialRelDisjoint: true, esriSpatialRelTouches: true, esriSpatialRelOverlaps: true, esriSpatialRelEnvelopeIntersects: true, esriSpatialRelIndexIntersects: false, esriSpatialRelRelation: false }, queryGeometry: { esriGeometryPoint: true, esriGeometryMultipoint: true, esriGeometryPolyline: true, esriGeometryPolygon: true, esriGeometryEnvelope: true }, layerGeometry: { esriGeometryPoint: true, esriGeometryMultipoint: true, esriGeometryPolyline: true, esriGeometryPolygon: true, esriGeometryEnvelope: false } };
function G2(e3) {
  return null != e3 && true === S.spatialRelationship[e3];
}
function g(e3) {
  return null != e3 && true === S.queryGeometry[c(e3)];
}
function j(e3) {
  return null != e3 && true === S.layerGeometry[e3];
}
function h2() {
  return import("./geometryEngineJSON-2XCCV7A5.js");
}
function v2(e3, n3, l2, y3, c3) {
  if (y(n3) && "esriGeometryPoint" === l2 && ("esriSpatialRelIntersects" === e3 || "esriSpatialRelContains" === e3)) {
    const e4 = W(new t(), n3, false, false);
    return Promise.resolve((r3) => t2(e4, false, false, r3));
  }
  if (y(n3) && "esriGeometryMultipoint" === l2) {
    const r3 = W(new t(), n3, false, false);
    if ("esriSpatialRelContains" === e3) return Promise.resolve((e4) => r2(r3, false, false, e4, y3, c3));
  }
  if (u(n3) && "esriGeometryPoint" === l2 && ("esriSpatialRelIntersects" === e3 || "esriSpatialRelContains" === e3)) return Promise.resolve((e4) => r(n3, v(l2, y3, c3, e4)));
  if (u(n3) && "esriGeometryMultipoint" === l2 && "esriSpatialRelContains" === e3) return Promise.resolve((e4) => i(n3, v(l2, y3, c3, e4)));
  if (u(n3) && "esriSpatialRelIntersects" === e3) {
    const e4 = s2(l2);
    return Promise.resolve((r3) => e4(n3, v(l2, y3, c3, r3)));
  }
  return h2().then((r3) => {
    const t4 = r3[R[e3]].bind(null, n3.spatialReference, n3);
    return (e4) => t4(v(l2, y3, c3, e4));
  });
}
async function P(r3, t4, i3) {
  const { spatialRel: s3, geometry: o3 } = r3;
  if (o3) {
    if (!G2(s3)) throw new s(c2, "Unsupported query spatial relationship", { query: r3 });
    if (I(o3.spatialReference) && I(i3)) {
      if (!g(o3)) throw new s(c2, "Unsupported query geometry type", { query: r3 });
      if (!j(t4)) throw new s(c2, "Unsupported layer geometry type", { query: r3 });
      if (r3.outSR) return f(r3.geometry && r3.geometry.spatialReference, r3.outSR);
    }
  }
}
function I2(e3) {
  if (u(e3)) return true;
  if (y(e3)) {
    for (const r3 of e3.rings) {
      if (5 !== r3.length) return false;
      if (r3[0][0] !== r3[1][0] || r3[0][0] !== r3[4][0] || r3[2][0] !== r3[3][0] || r3[0][1] !== r3[3][1] || r3[0][1] !== r3[4][1] || r3[1][1] !== r3[2][1]) return false;
    }
    return true;
  }
  return false;
}

// node_modules/@arcgis/core/layers/graphics/data/timeSupport.js
async function t3(t4, n3) {
  if (!t4) return null;
  const e3 = n3.featureAdapter, { startTimeField: u3, endTimeField: l2 } = t4;
  let r3 = Number.POSITIVE_INFINITY, i3 = Number.NEGATIVE_INFINITY;
  if (u3 && l2) await n3.forEach((t5) => {
    const n4 = e3.getAttribute(t5, u3), a = e3.getAttribute(t5, l2);
    null == n4 || isNaN(n4) || (r3 = Math.min(r3, n4)), null == a || isNaN(a) || (i3 = Math.max(i3, a));
  });
  else {
    const t5 = u3 || l2;
    await n3.forEach((n4) => {
      const u4 = e3.getAttribute(n4, t5);
      null == u4 || isNaN(u4) || (r3 = Math.min(r3, u4), i3 = Math.max(i3, u4));
    });
  }
  return { start: r3, end: i3 };
}
function n2(t4, n3, r3) {
  if (!n3 || !t4) return null;
  const { startTimeField: i3, endTimeField: a } = t4;
  if (!i3 && !a) return null;
  const { start: o3, end: s3 } = n3;
  return null === o3 && null === s3 ? null : void 0 === o3 && void 0 === s3 ? l() : i3 && a ? e2(r3, i3, a, o3, s3) : u2(r3, i3 || a, o3, s3);
}
function e2(t4, n3, e3, u3, l2) {
  return null != u3 && null != l2 ? (r3) => {
    const i3 = t4.getAttribute(r3, n3), a = t4.getAttribute(r3, e3);
    return (null == i3 || i3 <= l2) && (null == a || a >= u3);
  } : null != u3 ? (n4) => {
    const l3 = t4.getAttribute(n4, e3);
    return null == l3 || l3 >= u3;
  } : null != l2 ? (e4) => {
    const u4 = t4.getAttribute(e4, n3);
    return null == u4 || u4 <= l2;
  } : void 0;
}
function u2(t4, n3, e3, u3) {
  return null != e3 && null != u3 && e3 === u3 ? (u4) => t4.getAttribute(u4, n3) === e3 : null != e3 && null != u3 ? (l2) => {
    const r3 = t4.getAttribute(l2, n3);
    return r3 >= e3 && r3 <= u3;
  } : null != e3 ? (u4) => t4.getAttribute(u4, n3) >= e3 : null != u3 ? (e4) => t4.getAttribute(e4, n3) <= u3 : void 0;
}
function l() {
  return () => false;
}

export {
  v2 as v,
  P,
  I2 as I,
  t3 as t,
  n2 as n
};
//# sourceMappingURL=chunk-QYYMKGDW.js.map
