import {
  f as f2,
  l as l2,
  u
} from "./chunk-UQWZJZ2S.js";
import {
  E,
  b as b2,
  n as n3
} from "./chunk-5S4W3ME5.js";
import {
  C,
  b,
  d,
  j,
  o as o2,
  p,
  q as q2
} from "./chunk-SROTSYJS.js";
import {
  n as n2,
  t as t2
} from "./chunk-FOE4ICAJ.js";
import {
  g
} from "./chunk-EIGTETCG.js";
import {
  F,
  J,
  P,
  Z,
  _,
  e,
  o,
  q
} from "./chunk-MQAXMQFG.js";
import {
  n,
  r as r2,
  t
} from "./chunk-36FLFRUE.js";
import {
  f,
  l,
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/support/geometry3dUtils.js
function Y2({ start: t4, end: o3, type: c }, u2, a) {
  const i = [], p2 = o2(C2, o3, t4), A2 = o2(G, t4, u2), L = p(p2), h = 2 * j(p2, A2), E3 = h * h - 4 * L * (p(A2) - a * a);
  if (0 === E3) {
    const n5 = -h / (2 * L);
    (c === z.PLANE || n5 >= 0) && i.push(d(n2(), t4, p2, n5));
  } else if (E3 > 0) {
    const n5 = Math.sqrt(E3), s = (-h + n5) / (2 * L);
    (c === z.PLANE || s >= 0) && i.push(d(n2(), t4, p2, s));
    const r4 = (-h - n5) / (2 * L);
    (c === z.PLANE || r4 >= 0) && i.push(d(n2(), t4, p2, r4));
  }
  return i;
}
function j2(t4, s) {
  const e2 = t4.start, c = t4.end, u2 = o2(C2, c, e2), a = o(G, -u2[1], u2[0], 0), f3 = s.start, i = s.end, E3 = J(J2, i, f3), N2 = P(E3, a), d2 = o(K, e2[0], e2[1], 0), m2 = J(O, d2, f3), y = P(m2, a);
  if (Math.abs(N2) < B) return Math.abs(y), [];
  const P3 = q(Q, f3, E3, y / N2);
  if (s.type === b2.RAY) {
    const t5 = J(S, P3, f3);
    if (P(t5, E3) < -B) return [];
  }
  if (t4.type === z.HALF_PLANE) {
    const t5 = C(S, P3, e2);
    if (j(t5, u2) < -B) return [];
  }
  return [t(P3)];
}
function v(t4, n5) {
  return x2(g2(T, n5[2], t4), n5);
}
function k(t4, n5) {
  const s = 0, r4 = q3(g2(T, s, t4), g2(V, s, n5)), e2 = [];
  for (const o3 of r4) e2.push(t2(o3));
  return e2;
}
function M(t4, n5) {
  return _2(t4, g2(T, t4[2], n5));
}
function F2(t4, s, r4) {
  const o3 = o2(C2, t4, s), u2 = r4 / q2(o3), a = d(n(), s, o3, u2);
  return a[2] = t4[2], a;
}
function _2(t4, { start: n5, end: s, type: r4 }) {
  const e2 = J(C2, t4, n5), o3 = J(G, s, n5), c = P(e2, o3) / P(o3, o3);
  return q(n(), n5, o3, r4 === b2.RAY ? Math.max(c, 0) : c);
}
function w({ start: t4, end: e2, type: o3 }, c, u2) {
  const a = [], f3 = e(C2, e2, t4), i = o2(G, t4, c), p2 = p(f3), A2 = 2 * j(f3, i), L = A2 * A2 - 4 * p2 * (p(i) - u2 * u2);
  if (0 === L) {
    const n5 = -A2 / (2 * p2);
    (o3 === b2.LINE || n5 >= 0) && a.push(q(n(), t4, f3, n5));
  } else if (L > 0) {
    const n5 = Math.sqrt(L), s = (-A2 + n5) / (2 * p2);
    (o3 === b2.LINE || s >= 0) && a.push(q(n(), t4, f3, s));
    const r4 = (-A2 - n5) / (2 * p2);
    (o3 === b2.LINE || r4 >= 0) && a.push(q(n(), t4, f3, r4));
  }
  return a;
}
function q3(n5, s) {
  const r4 = n5.start, e2 = n5.end, o3 = s.start, c = s.end, u2 = J(C2, e2, r4), a = J(G, c, o3), f3 = J(J2, o3, r4), i = _(K, u2, a), p2 = P(f3, i);
  if (!g(p2, 0, B)) return [];
  const E3 = Z(i);
  if (g(E3, 0, B)) return [];
  const d2 = _(O, f3, a), m2 = P(d2, i) / E3, P3 = q(Q, r4, u2, m2);
  if (n5.type === b2.RAY) {
    const t4 = J(S, P3, r4);
    if (P(u2, t4) < -B) return [];
  }
  if (s.type === b2.RAY) {
    const t4 = J(S, P3, o3);
    if (P(a, t4) < -B) return [];
  }
  return [t(P3)];
}
function x2({ start: t4, end: n5, type: s }, r4) {
  const e2 = J(C2, r4, t4), o3 = J(G, n5, t4), c = _(J2, o3, e2);
  if (Z(c) / Z(o3) < B) switch (s) {
    case b2.LINE:
      return [t(r4)];
    case b2.RAY:
      return P(o3, e2) < -B ? [] : [t(r4)];
  }
  return [];
}
function U(n5, s, r4) {
  return g(b(r4, n5), s * s, B) ? [t(r4)] : [];
}
function g2(t4, n5, { start: s, end: r4, type: e2 }) {
  return o(t4.start, s[0], s[1], n5), o(t4.end, r4[0], r4[1], n5), t4.type = D[e2], t4;
}
var z;
!function(t4) {
  t4[t4.PLANE = 0] = "PLANE", t4[t4.HALF_PLANE = 1] = "HALF_PLANE";
}(z || (z = {}));
var D = { [z.PLANE]: b2.LINE, [z.HALF_PLANE]: b2.RAY };
var B = 1e-6;
var C2 = n();
var G = n();
var J2 = n();
var K = n();
var O = n();
var Q = n();
var S = n();
var T = { start: n(), end: n(), type: b2.LINE };
var V = { start: n(), end: n(), type: b2.LINE };

// node_modules/@arcgis/core/views/interactive/snapping/SnappingConstraint.js
var P2 = class {
  intersect(t4) {
    return H(this, t4);
  }
};
var Z2 = class extends P2 {
  constructor(t4) {
    super(), this.point = t4;
  }
  equals(t4) {
    return _3(t4) && F(this.point, t4.point);
  }
  closestTo() {
    return f2(this.point);
  }
};
var A = class extends P2 {
  constructor(t4, s, e2) {
    super(), this.start = t4, this.end = s, this.type = e2, this.lineLike = { start: this.start, end: this.end, type: this.type };
  }
  equals(t4) {
    return S2(t4) && this.type === t4.type && F(this.start, t4.start) && F(this.end, t4.end);
  }
  closestTo(t4) {
    const s = _2(t4, this.lineLike);
    return l2(s);
  }
};
var E2 = class extends A {
  constructor(t4, s) {
    super(t4, s, b2.LINE);
  }
};
var b3 = class _b extends P2 {
  constructor(t4, s, e2) {
    super(), this.intersection = t4, this.first = s, this.second = e2;
  }
  equals(t4) {
    return t4 instanceof _b && this.first.equals(t4.first) && this.second.equals(t4.second);
  }
  closestTo() {
    return f2(this.intersection);
  }
};
var v2 = class _v extends P2 {
  constructor(t4, s, e2) {
    super(), this.basePoint = t4, this.first = s, this.second = e2;
  }
  equals(t4) {
    return t4 instanceof _v && this.first.equals(t4.first) && this.second.equals(t4.second);
  }
  closestTo(t4) {
    const s = this.basePoint;
    return l2(r2(s[0], s[1], t4[2]));
  }
};
var w2 = class extends P2 {
  constructor(t4, s) {
    super(), this.center = t4, this.radius = s;
  }
  equals(t4) {
    return z2(t4) && this.center[0] === t4.center[0] && this.center[1] === t4.center[1] && this.radius === t4.radius;
  }
  closestTo(t4) {
    const s = F2(t4, this.center, this.radius);
    return l2(s);
  }
};
var I = class extends P2 {
  constructor(t4, s, e2) {
    super(), this.start = t4, this.end = s, this.type = e2, this.planeLike = { start: t4, end: s, type: e2 };
  }
  equals(t4) {
    return B2(t4) && this.type === t4.type && F(this.start, t4.start) && F(this.end, t4.end);
  }
  closestTo(t4) {
    return l2(M(t4, this.planeLike));
  }
  closestEndTo(t4) {
    const { start: s, end: e2 } = this;
    return Math.sign(j(o2(D2, e2, s), o2(J3, t4, s))) > 0 ? e2 : s;
  }
};
var N = class extends I {
  constructor(t4, s) {
    super(t4, s, z.HALF_PLANE);
  }
};
var G2 = class extends I {
  constructor(t4, s) {
    super(t4, s, z.PLANE);
  }
};
var O2 = class extends P2 {
  constructor(t4, s, e2) {
    super(), this.start = t4, this.end = s, this.getZ = e2, this.planeLike = { start: t4, end: s, type: z.HALF_PLANE };
  }
  equals(t4) {
    return C3(t4) && F(this.start, t4.start) && F(this.end, t4.end) && this.getZ === t4.getZ;
  }
  closestTo(t4) {
    return F3(this, t4);
  }
  addIfOnTheGround(s, e2) {
    for (const n5 of e2) {
      const e3 = l(this.getZ(n5[0], n5[1], n5[2]), 0);
      Math.abs(n5[2] - e3) < B && (n5[2] = e3, s.push(n5));
    }
  }
};
function F3(t4, e2) {
  const n5 = M(e2, t4.planeLike);
  return n5[2] = f(t4.getZ(e2[0], e2[1], e2[2])) ?? K2, l2(n5);
}
function H(t4, s) {
  let e2 = [];
  if (_3(t4)) {
    const { point: n5 } = t4;
    S2(s) ? e2 = x2(s.lineLike, n5) : z2(s) ? e2 = U(s.center, s.radius, n5) : B2(s) ? e2 = v(s.planeLike, n5) : C3(s) && (e2 = M2(s, t4));
  } else if (S2(t4)) {
    const { lineLike: n5 } = t4;
    _3(s) ? e2 = x2(n5, s.point) : S2(s) ? e2 = q3(n5, s.lineLike) : z2(s) ? e2 = w(n5, s.center, s.radius) : B2(s) ? e2 = j2(s.planeLike, n5) : C3(s) && (e2 = M2(s, t4));
  } else if (z2(t4)) {
    const { center: n5, radius: r4 } = t4;
    if (S2(s)) e2 = w(s.lineLike, n5, r4);
    else if (_3(s)) e2 = U(n5, r4, s.point);
    else {
      if (B2(s)) return Y2(s.planeLike, n5, r4).map((e3) => new v2(e3, t4, s));
      C3(s) && (e2 = M2(s, t4));
    }
  } else if (B2(t4)) {
    const { planeLike: n5 } = t4;
    if (B2(s)) return k(n5, s.planeLike).map((e3) => new v2(e3, t4, s));
    if (_3(s)) e2 = v(n5, s.point);
    else if (S2(s)) e2 = j2(n5, s.lineLike);
    else {
      if (z2(s)) return Y2(n5, s.center, s.radius).map((e3) => new v2(e3, t4, s));
      C3(s) && (e2 = M2(s, t4));
    }
  } else C3(t4) && (e2 = M2(t4, s));
  return U2(e2, t4, s);
}
function M2(t4, n5) {
  const { planeLike: r4, getZ: i } = t4, o3 = [];
  if (_3(n5)) t4.addIfOnTheGround(o3, v(r4, n5.point));
  else if (S2(n5)) t4.addIfOnTheGround(o3, j2(r4, n5.lineLike));
  else if (z2(n5)) for (const [s, u2] of Y2(r4, n5.center, n5.radius)) {
    const t5 = i(s, u2, 0);
    r(t5) && o3.push(r2(s, u2, t5));
  }
  else if (B2(n5) || C3(n5)) for (const [e2, u2] of k(r4, n5.planeLike)) {
    const t5 = f(i(e2, u2, 0)) ?? K2;
    o3.push(r2(e2, u2, t5));
  }
  return o3;
}
function U2(t4, s, e2) {
  return t4.map((t5) => new b3(l2(t5), s, e2));
}
function _3(t4) {
  return t4 instanceof Z2;
}
function S2(t4) {
  return t4 instanceof A;
}
function z2(t4) {
  return t4 instanceof w2;
}
function B2(t4) {
  return t4 instanceof I;
}
function C3(t4) {
  return t4 instanceof O2;
}
var D2 = n2();
var J3 = n2();
var K2 = 0;

// node_modules/@arcgis/core/views/interactive/snapping/candidates/SnappingCandidate.js
var t3 = class {
  constructor(t4, s, i, o3) {
    this.targetPoint = t4, this.constraint = s, this.isDraped = i, this.domain = o3;
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/candidates/FeatureSnappingCandidate.js
var n4 = class extends t3 {
  constructor({ targetPoint: o3, objectId: n5, constraint: r4, isDraped: i }) {
    super(o3, r4, i, E.FEATURE), this.objectId = n5;
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/candidates/EdgeSnappingCandidate.js
var r3 = class extends n4 {
  constructor(n5) {
    super({ ...n5, constraint: new E2(n5.edgeStart, n5.edgeEnd) });
  }
  get hints() {
    return [new n3(u.REFERENCE, this.constraint.start, this.constraint.end, this.isDraped, this.domain)];
  }
};

export {
  _2 as _,
  Z2 as Z,
  b3 as b,
  w2 as w,
  N,
  G2 as G,
  O2 as O,
  t3 as t,
  n4 as n,
  r3 as r
};
//# sourceMappingURL=chunk-INH5JU5P.js.map
