{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/TextureCoordinateAttribute.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/TextureAtlasLookup.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/VertexTextureCoordinates.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/lib/GLMaterial.js", "../../@arcgis/core/views/3d/webgl-engine/lib/GLTextureMaterial.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/PhysicallyBasedRenderingParameters.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/EvaluateAmbientLighting.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/MainLighting.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/AnalyticalSkyModel.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/PhysicallyBasedRendering.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderModules/IntegerPassUniform.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderModules/Matrix4sDrawUniform.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderModules/Matrix4sPassUniform.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/ReadShadowMap.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderModules/BooleanPassUniform.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{neverReached as e}from\"../../../../../../core/compilerUtils.js\";import{glsl as r}from\"../../shaderModules/interfaces.js\";import{VertexAttribute as t}from\"../../../lib/VertexAttribute.js\";var d;function o(o,v){switch(v.textureCoordinateType){case d.Default:return o.attributes.add(t.UV0,\"vec2\"),o.varyings.add(\"vuv0\",\"vec2\"),void o.vertex.code.add(r`void forwardTextureCoordinates() {\nvuv0 = uv0;\n}`);case d.Compressed:return o.attributes.add(t.UV0,\"vec2\"),o.varyings.add(\"vuv0\",\"vec2\"),void o.vertex.code.add(r`vec2 getUV0() {\nreturn uv0 / 16384.0;\n}\nvoid forwardTextureCoordinates() {\nvuv0 = getUV0();\n}`);case d.Atlas:return o.attributes.add(t.UV0,\"vec2\"),o.varyings.add(\"vuv0\",\"vec2\"),o.attributes.add(t.UVREGION,\"vec4\"),o.varyings.add(\"vuvRegion\",\"vec4\"),void o.vertex.code.add(r`void forwardTextureCoordinates() {\nvuv0 = uv0;\nvuvRegion = uvRegion;\n}`);default:e(v.textureCoordinateType);case d.None:return void o.vertex.code.add(r`void forwardTextureCoordinates() {}`);case d.COUNT:return}}!function(e){e[e.None=0]=\"None\",e[e.Default=1]=\"Default\",e[e.Atlas=2]=\"Atlas\",e[e.Compressed=3]=\"Compressed\",e[e.COUNT=4]=\"COUNT\"}(d||(d={}));export{o as TextureCoordinateAttribute,d as TextureCoordinateAttributeType};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{glsl as e}from\"../../shaderModules/interfaces.js\";function t(t){t.extensions.add(\"GL_EXT_shader_texture_lod\"),t.extensions.add(\"GL_OES_standard_derivatives\"),t.fragment.code.add(e`#ifndef GL_EXT_shader_texture_lod\nfloat calcMipMapLevel(const vec2 ddx, const vec2 ddy) {\nfloat deltaMaxSqr = max(dot(ddx, ddx), dot(ddy, ddy));\nreturn max(0.0, 0.5 * log2(deltaMaxSqr));\n}\n#endif\nvec4 textureAtlasLookup(sampler2D texture, vec2 textureSize, vec2 textureCoordinates, vec4 atlasRegion) {\nvec2 atlasScale = atlasRegion.zw - atlasRegion.xy;\nvec2 uvAtlas = fract(textureCoordinates) * atlasScale + atlasRegion.xy;\nfloat maxdUV = 0.125;\nvec2 dUVdx = clamp(dFdx(textureCoordinates), -maxdUV, maxdUV) * atlasScale;\nvec2 dUVdy = clamp(dFdy(textureCoordinates), -maxdUV, maxdUV) * atlasScale;\n#ifdef GL_EXT_shader_texture_lod\nreturn texture2DGradEXT(texture, uvAtlas, dUVdx, dUVdy);\n#else\nvec2 dUVdxAuto = dFdx(uvAtlas);\nvec2 dUVdyAuto = dFdy(uvAtlas);\nfloat mipMapLevel = calcMipMapLevel(dUVdx * textureSize, dUVdy * textureSize);\nfloat autoMipMapLevel = calcMipMapLevel(dUVdxAuto * textureSize, dUVdyAuto * textureSize);\nreturn texture2D(texture, uvAtlas, mipMapLevel - autoMipMapLevel);\n#endif\n}`)}export{t as TextureAtlasLookup};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{neverReached as e}from\"../../../../../../core/compilerUtils.js\";import{TextureCoordinateAttribute as t,TextureCoordinateAttributeType as r}from\"./TextureCoordinateAttribute.glsl.js\";import{TextureAtlasLookup as o}from\"../util/TextureAtlasLookup.glsl.js\";import{glsl as u}from\"../../shaderModules/interfaces.js\";function a(a,s){switch(a.include(t,s),a.fragment.code.add(u`\n  struct TextureLookupParameter {\n    vec2 uv;\n    ${s.supportsTextureAtlas?\"vec2 size;\":\"\"}\n  } vtc;\n  `),s.textureCoordinateType){case r.Default:case r.Compressed:return void a.fragment.code.add(u`vec4 textureLookup(sampler2D texture, TextureLookupParameter params) {\nreturn texture2D(texture, params.uv);\n}`);case r.Atlas:return a.include(o),void a.fragment.code.add(u`vec4 textureLookup(sampler2D texture, TextureLookupParameter params) {\nreturn textureAtlasLookup(texture, params.size, params.uv, vuvRegion);\n}`);default:e(s.textureCoordinateType);case r.None:case r.COUNT:return}}export{a as VertexTextureCoordinates};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ResourceState as e}from\"./basicInterfaces.js\";class t{constructor(e){this._material=e.material,this._techniqueRepository=e.techniqueRep,this._output=e.output}dispose(){this._techniqueRepository.release(this._technique)}get technique(){return this._technique}get _stippleTextureRepository(){return this._techniqueRepository.constructionContext.stippleTextureRepository}ensureTechnique(e,t){return this._technique=this._techniqueRepository.releaseAndAcquire(e,this._material.getConfiguration(this._output,t),this._technique),this._technique}ensureResources(t){return e.LOADED}}export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{releaseMaybe as t,isSome as e,isNone as s}from\"../../../../core/maybe.js\";import{isPromiseLike as i}from\"../../../../core/promiseUtils.js\";import{NoParameters as r}from\"../core/shaderModules/interfaces.js\";import{ResourceState as u}from\"./basicInterfaces.js\";import l from\"./GLMaterial.js\";class h extends l{constructor(t){super(t),this._numLoading=0,this._disposed=!1,this._textureRepository=t.textureRep,this._textureId=t.textureId,this._acquire(t.textureId,(t=>this._texture=t)),this._acquire(t.normalTextureId,(t=>this._textureNormal=t)),this._acquire(t.emissiveTextureId,(t=>this._textureEmissive=t)),this._acquire(t.occlusionTextureId,(t=>this._textureOcclusion=t)),this._acquire(t.metallicRoughnessTextureId,(t=>this._textureMetallicRoughness=t))}dispose(){this._texture=t(this._texture),this._textureNormal=t(this._textureNormal),this._textureEmissive=t(this._textureEmissive),this._textureOcclusion=t(this._textureOcclusion),this._textureMetallicRoughness=t(this._textureMetallicRoughness),this._disposed=!0}ensureResources(t){return 0===this._numLoading?u.LOADED:u.LOADING}get textureBindParameters(){return new o(e(this._texture)?this._texture.glTexture:null,e(this._textureNormal)?this._textureNormal.glTexture:null,e(this._textureEmissive)?this._textureEmissive.glTexture:null,e(this._textureOcclusion)?this._textureOcclusion.glTexture:null,e(this._textureMetallicRoughness)?this._textureMetallicRoughness.glTexture:null)}updateTexture(e){(s(this._texture)||e!==this._texture.id)&&(this._texture=t(this._texture),this._textureId=e,this._acquire(this._textureId,(t=>this._texture=t)))}_acquire(e,r){if(s(e))return void r(null);const u=this._textureRepository.acquire(e);if(i(u))return++this._numLoading,void u.then((e=>{if(this._disposed)return t(e),void r(null);r(e)})).finally((()=>--this._numLoading));r(u)}}class o extends r{constructor(t=null,e=null,s=null,i=null,r=null){super(),this.texture=t,this.textureNormal=e,this.textureEmissive=s,this.textureOcclusion=i,this.textureMetallicRoughness=r}}export{h as GLTextureMaterial,o as GLTextureMaterialBindParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{f as e}from\"../../../../../../chunks/vec3f32.js\";import{VertexTextureCoordinates as s}from\"../attributes/VertexTextureCoordinates.glsl.js\";import{textureSize as r}from\"../util/WebGL2Utils.js\";import{Float3DrawUniform as o}from\"../../shaderModules/Float3DrawUniform.js\";import{Float3PassUniform as t}from\"../../shaderModules/Float3PassUniform.js\";import{glsl as a}from\"../../shaderModules/interfaces.js\";import{createTexture2DDrawSizeUniforms as i}from\"../../shaderModules/Texture2DDrawUniform.js\";import{createTexture2DPassSizeUniforms as c}from\"../../shaderModules/Texture2DPassUniform.js\";import{TextureSizeUniformType as n}from\"../../shaderModules/TextureSizeUniformType.js\";import{BindType as u}from\"../../shaderTechnique/BindType.js\";import{GLTextureMaterialBindParameters as l}from\"../../../lib/GLTextureMaterial.js\";const m=e(0,.6,.2);var d;!function(e){e[e.Disabled=0]=\"Disabled\",e[e.Normal=1]=\"Normal\",e[e.Schematic=2]=\"Schematic\",e[e.Water=3]=\"Water\",e[e.WaterOnIntegratedMesh=4]=\"WaterOnIntegratedMesh\",e[e.Terrain=5]=\"Terrain\",e[e.TerrainWithWater=6]=\"TerrainWithWater\",e[e.COUNT=7]=\"COUNT\"}(d||(d={}));class p extends l{}function x(e,l){const m=e.fragment,p=l.hasMetallicRoughnessTexture||l.hasEmissionTexture||l.hasOcclusionTexture;if(l.pbrMode===d.Normal&&p&&e.include(s,l),l.pbrMode!==d.Schematic)if(l.pbrMode!==d.Disabled){if(l.pbrMode===d.Normal){m.code.add(a`vec3 mrr;\nvec3 emission;\nfloat occlusion;`);const e=l.supportsTextureAtlas?l.hasWebGL2Context?n.None:n.Size:n.None,s=l.pbrTextureBindType;l.hasMetallicRoughnessTexture&&(m.uniforms.add(s===u.Pass?c(\"texMetallicRoughness\",(e=>e.textureMetallicRoughness),e):i(\"texMetallicRoughness\",(e=>e.textureMetallicRoughness),e)),m.code.add(a`void applyMetallnessAndRoughness(TextureLookupParameter params) {\nvec3 metallicRoughness = textureLookup(texMetallicRoughness, params).rgb;\nmrr[0] *= metallicRoughness.b;\nmrr[1] *= metallicRoughness.g;\n}`)),l.hasEmissionTexture&&(m.uniforms.add(s===u.Pass?c(\"texEmission\",(e=>e.textureEmissive),e):i(\"texEmission\",(e=>e.textureEmissive),e)),m.code.add(a`void applyEmission(TextureLookupParameter params) {\nemission *= textureLookup(texEmission, params).rgb;\n}`)),l.hasOcclusionTexture?(m.uniforms.add(s===u.Pass?c(\"texOcclusion\",(e=>e.textureOcclusion),e):i(\"texOcclusion\",(e=>e.textureOcclusion),e)),m.code.add(a`void applyOcclusion(TextureLookupParameter params) {\nocclusion *= textureLookup(texOcclusion, params).r;\n}\nfloat getBakedOcclusion() {\nreturn occlusion;\n}`)):m.code.add(a`float getBakedOcclusion() { return 1.0; }`),m.uniforms.add(s===u.Pass?[new t(\"emissionFactor\",(e=>e.emissiveFactor)),new t(\"mrrFactors\",(e=>e.mrrFactors))]:[new o(\"emissionFactor\",(e=>e.emissiveFactor)),new o(\"mrrFactors\",(e=>e.mrrFactors))]),m.code.add(a`\n    void applyPBRFactors() {\n      mrr = mrrFactors;\n      emission = emissionFactor;\n      occlusion = 1.0;\n      ${p?a`vtc.uv = vuv0;`:\"\"}\n      ${l.hasMetallicRoughnessTextureTransform?a`vtc.uv = metallicRoughnessUV;`:\"\"}\n      ${l.hasMetallicRoughnessTexture?l.supportsTextureAtlas?a`\n                vtc.size = ${r(l,\"texMetallicRoughness\")};\n                applyMetallnessAndRoughness(vtc);`:a`applyMetallnessAndRoughness(vtc);`:\"\"}\n      ${l.hasEmissiveTextureTransform?a`vtc.uv = emissiveUV;`:\"\"}\n      ${l.hasEmissionTexture?l.supportsTextureAtlas?a`\n                vtc.size = ${r(l,\"texEmission\")};\n                applyEmission(vtc);`:a`applyEmission(vtc);`:\"\"}\n      ${l.hasOcclusionTextureTransform?a`vtc.uv = occlusionUV;`:\"\"}\n      ${l.hasOcclusionTexture?l.supportsTextureAtlas?a`\n                vtc.size = ${r(l,\"texOcclusion\")};\n                applyOcclusion(vtc);`:a`applyOcclusion(vtc);`:\"\"}\n    }\n  `)}}else m.code.add(a`float getBakedOcclusion() { return 1.0; }`);else m.code.add(a`vec3 mrr = vec3(0.0, 0.6, 0.2);\nvec3 emission = vec3(0.0);\nfloat occlusion = 1.0;\nvoid applyPBRFactors() {}\nfloat getBakedOcclusion() { return 1.0; }`)}export{p as PBRBindParameters,d as PBRMode,m as PBRSchematicMRRValues,x as PhysicallyBasedRenderingParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{s as i}from\"../../../../../../chunks/vec3.js\";import{c as n}from\"../../../../../../chunks/vec3f64.js\";import{s as t}from\"../../../../../../chunks/vec4.js\";import{c as g}from\"../../../../../../chunks/vec4f64.js\";import{PBRMode as h}from\"./PhysicallyBasedRenderingParameters.glsl.js\";import{Float3PassUniform as e}from\"../../shaderModules/Float3PassUniform.js\";import{Float4PassUniform as l}from\"../../shaderModules/Float4PassUniform.js\";import{glsl as s}from\"../../shaderModules/interfaces.js\";function m(n,g){const m=n.fragment,o=void 0!==g.lightingSphericalHarmonicsOrder?g.lightingSphericalHarmonicsOrder:2;0===o?(m.uniforms.add(new e(\"lightingAmbientSH0\",((n,t)=>i(a,t.lighting.sh.r[0],t.lighting.sh.g[0],t.lighting.sh.b[0])))),m.code.add(s`vec3 calculateAmbientIrradiance(vec3 normal, float ambientOcclusion) {\nvec3 ambientLight = 0.282095 * lightingAmbientSH0;\nreturn ambientLight * (1.0 - ambientOcclusion);\n}`)):1===o?(m.uniforms.add([new l(\"lightingAmbientSH_R\",((i,n)=>t(r,n.lighting.sh.r[0],n.lighting.sh.r[1],n.lighting.sh.r[2],n.lighting.sh.r[3]))),new l(\"lightingAmbientSH_G\",((i,n)=>t(r,n.lighting.sh.g[0],n.lighting.sh.g[1],n.lighting.sh.g[2],n.lighting.sh.g[3]))),new l(\"lightingAmbientSH_B\",((i,n)=>t(r,n.lighting.sh.b[0],n.lighting.sh.b[1],n.lighting.sh.b[2],n.lighting.sh.b[3])))]),m.code.add(s`vec3 calculateAmbientIrradiance(vec3 normal, float ambientOcclusion) {\nvec4 sh0 = vec4(\n0.282095,\n0.488603 * normal.x,\n0.488603 * normal.z,\n0.488603 * normal.y\n);\nvec3 ambientLight = vec3(\ndot(lightingAmbientSH_R, sh0),\ndot(lightingAmbientSH_G, sh0),\ndot(lightingAmbientSH_B, sh0)\n);\nreturn ambientLight * (1.0 - ambientOcclusion);\n}`)):2===o&&(m.uniforms.add([new e(\"lightingAmbientSH0\",((n,t)=>i(a,t.lighting.sh.r[0],t.lighting.sh.g[0],t.lighting.sh.b[0]))),new l(\"lightingAmbientSH_R1\",((i,n)=>t(r,n.lighting.sh.r[1],n.lighting.sh.r[2],n.lighting.sh.r[3],n.lighting.sh.r[4]))),new l(\"lightingAmbientSH_G1\",((i,n)=>t(r,n.lighting.sh.g[1],n.lighting.sh.g[2],n.lighting.sh.g[3],n.lighting.sh.g[4]))),new l(\"lightingAmbientSH_B1\",((i,n)=>t(r,n.lighting.sh.b[1],n.lighting.sh.b[2],n.lighting.sh.b[3],n.lighting.sh.b[4]))),new l(\"lightingAmbientSH_R2\",((i,n)=>t(r,n.lighting.sh.r[5],n.lighting.sh.r[6],n.lighting.sh.r[7],n.lighting.sh.r[8]))),new l(\"lightingAmbientSH_G2\",((i,n)=>t(r,n.lighting.sh.g[5],n.lighting.sh.g[6],n.lighting.sh.g[7],n.lighting.sh.g[8]))),new l(\"lightingAmbientSH_B2\",((i,n)=>t(r,n.lighting.sh.b[5],n.lighting.sh.b[6],n.lighting.sh.b[7],n.lighting.sh.b[8])))]),m.code.add(s`vec3 calculateAmbientIrradiance(vec3 normal, float ambientOcclusion) {\nvec3 ambientLight = 0.282095 * lightingAmbientSH0;\nvec4 sh1 = vec4(\n0.488603 * normal.x,\n0.488603 * normal.z,\n0.488603 * normal.y,\n1.092548 * normal.x * normal.y\n);\nvec4 sh2 = vec4(\n1.092548 * normal.y * normal.z,\n0.315392 * (3.0 * normal.z * normal.z - 1.0),\n1.092548 * normal.x * normal.z,\n0.546274 * (normal.x * normal.x - normal.y * normal.y)\n);\nambientLight += vec3(\ndot(lightingAmbientSH_R1, sh1),\ndot(lightingAmbientSH_G1, sh1),\ndot(lightingAmbientSH_B1, sh1)\n);\nambientLight += vec3(\ndot(lightingAmbientSH_R2, sh2),\ndot(lightingAmbientSH_G2, sh2),\ndot(lightingAmbientSH_B2, sh2)\n);\nreturn ambientLight * (1.0 - ambientOcclusion);\n}`),g.pbrMode!==h.Normal&&g.pbrMode!==h.Schematic||m.code.add(s`const vec3 skyTransmittance = vec3(0.9, 0.9, 1.0);\nvec3 calculateAmbientRadiance(float ambientOcclusion)\n{\nvec3 ambientLight = 1.2 * (0.282095 * lightingAmbientSH0) - 0.2;\nreturn ambientLight *= (1.0 - ambientOcclusion) * skyTransmittance;\n}`))}const a=n(),r=g();export{m as EvaluateAmbientLighting};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Float3PassUniform as i}from\"../../shaderModules/Float3PassUniform.js\";import{FloatPassUniform as n}from\"../../shaderModules/FloatPassUniform.js\";import{glsl as t}from\"../../shaderModules/interfaces.js\";function o(n){n.uniforms.add(new i(\"mainLightDirection\",((i,n)=>n.lighting.mainLight.direction)))}function a(n){n.uniforms.add(new i(\"mainLightIntensity\",((i,n)=>n.lighting.mainLight.intensity)))}function e(i,t){t.useLegacyTerrainShading?i.uniforms.add(new n(\"lightingFixedFactor\",((i,n)=>n.lighting.noonFactor*(1-n.lighting.globalFactor)))):i.constants.add(\"lightingFixedFactor\",\"float\",0)}function r(i,n){const r=i.fragment;o(r),a(r),e(r,n),r.code.add(t`vec3 evaluateMainLighting(vec3 normal_global, float shadowing) {\nfloat dotVal = clamp(dot(normal_global, mainLightDirection), 0.0, 1.0);\ndotVal = mix(dotVal, 1.0, lightingFixedFactor);\nreturn mainLightIntensity * ((1.0 - shadowing) * dotVal);\n}`)}export{r as MainLighting,o as addMainLightDirection,a as addMainLightIntensity};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{glsl as e}from\"../../shaderModules/interfaces.js\";function t(t){const a=t.fragment.code;a.add(e`vec3 evaluateDiffuseIlluminationHemisphere(vec3 ambientGround, vec3 ambientSky, float NdotNG)\n{\nreturn ((1.0 - NdotNG) * ambientGround + (1.0 + NdotNG) * ambientSky) * 0.5;\n}`),a.add(e`float integratedRadiance(float cosTheta2, float roughness)\n{\nreturn (cosTheta2 - 1.0) / (cosTheta2 * (1.0 - roughness * roughness) - 1.0);\n}`),a.add(e`vec3 evaluateSpecularIlluminationHemisphere(vec3 ambientGround, vec3 ambientSky, float RdotNG, float roughness)\n{\nfloat cosTheta2 = 1.0 - RdotNG * RdotNG;\nfloat intRadTheta = integratedRadiance(cosTheta2, roughness);\nfloat ground = RdotNG < 0.0 ? 1.0 - intRadTheta : 1.0 + intRadTheta;\nfloat sky = 2.0 - ground;\nreturn (ground * ambientGround + sky * ambientSky) * 0.5;\n}`)}export{t as AnalyticalSkyModel};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{AnalyticalSkyModel as e}from\"./AnalyticalSkyModel.glsl.js\";import{PBRMode as o}from\"./PhysicallyBasedRenderingParameters.glsl.js\";import{PiUtils as t}from\"./PiUtils.glsl.js\";import{glsl as a}from\"../../shaderModules/interfaces.js\";function n(n,r){const l=n.fragment.code;n.include(t),r.pbrMode!==o.Normal&&r.pbrMode!==o.Schematic&&r.pbrMode!==o.Terrain&&r.pbrMode!==o.TerrainWithWater||(l.add(a`float normalDistribution(float NdotH, float roughness)\n{\nfloat a = NdotH * roughness;\nfloat b = roughness / (1.0 - NdotH * NdotH + a * a);\nreturn b * b * INV_PI;\n}`),l.add(a`const vec4 c0 = vec4(-1.0, -0.0275, -0.572,  0.022);\nconst vec4 c1 = vec4( 1.0,  0.0425,  1.040, -0.040);\nconst vec2 c2 = vec2(-1.04, 1.04);\nvec2 prefilteredDFGAnalytical(float roughness, float NdotV) {\nvec4 r = roughness * c0 + c1;\nfloat a004 = min(r.x * r.x, exp2(-9.28 * NdotV)) * r.x + r.y;\nreturn c2 * a004 + r.zw;\n}`)),r.pbrMode!==o.Normal&&r.pbrMode!==o.Schematic||(n.include(e),l.add(a`struct PBRShadingInfo\n{\nfloat NdotL;\nfloat NdotV;\nfloat NdotH;\nfloat VdotH;\nfloat LdotH;\nfloat NdotNG;\nfloat RdotNG;\nfloat NdotAmbDir;\nfloat NdotH_Horizon;\nvec3 skyRadianceToSurface;\nvec3 groundRadianceToSurface;\nvec3 skyIrradianceToSurface;\nvec3 groundIrradianceToSurface;\nfloat averageAmbientRadiance;\nfloat ssao;\nvec3 albedoLinear;\nvec3 f0;\nvec3 f90;\nvec3 diffuseColor;\nfloat metalness;\nfloat roughness;\n};`),l.add(a`vec3 evaluateEnvironmentIllumination(PBRShadingInfo inputs) {\nvec3 indirectDiffuse = evaluateDiffuseIlluminationHemisphere(inputs.groundIrradianceToSurface, inputs.skyIrradianceToSurface, inputs.NdotNG);\nvec3 indirectSpecular = evaluateSpecularIlluminationHemisphere(inputs.groundRadianceToSurface, inputs.skyRadianceToSurface, inputs.RdotNG, inputs.roughness);\nvec3 diffuseComponent = inputs.diffuseColor * indirectDiffuse * INV_PI;\nvec2 dfg = prefilteredDFGAnalytical(inputs.roughness, inputs.NdotV);\nvec3 specularColor = inputs.f0 * dfg.x + inputs.f90 * dfg.y;\nvec3 specularComponent = specularColor * indirectSpecular;\nreturn (diffuseComponent + specularComponent);\n}`),l.add(a`float gamutMapChanel(float x, vec2 p){\nreturn (x < p.x) ? mix(0.0, p.y, x/p.x) : mix(p.y, 1.0, (x - p.x) / (1.0 - p.x) );\n}`),l.add(a`vec3 blackLevelSoftCompression(vec3 inColor, PBRShadingInfo inputs){\nvec3 outColor;\nvec2 p = vec2(0.02 * (inputs.averageAmbientRadiance), 0.0075 * (inputs.averageAmbientRadiance));\noutColor.x = gamutMapChanel(inColor.x, p) ;\noutColor.y = gamutMapChanel(inColor.y, p) ;\noutColor.z = gamutMapChanel(inColor.z, p) ;\nreturn outColor;\n}`))}function r(e,o){const n=e.fragment.code;e.include(t),n.add(a`\n  struct PBRShadingWater\n  {\n      float NdotL;   // cos angle between normal and light direction\n      float NdotV;   // cos angle between normal and view direction\n      float NdotH;   // cos angle between normal and half vector\n      float VdotH;   // cos angle between view direction and half vector\n      float LdotH;   // cos angle between light direction and half vector\n      float VdotN;   // cos angle between view direction and normal vector\n  };\n\n  float dtrExponent = ${o.useCustomDTRExponentForWater?\"2.2\":\"2.0\"};\n  `),n.add(a`vec3 fresnelReflection(float angle, vec3 f0, float f90) {\nreturn f0 + (f90 - f0) * pow(1.0 - angle, 5.0);\n}`),n.add(a`float normalDistributionWater(float NdotH, float roughness)\n{\nfloat r2 = roughness * roughness;\nfloat NdotH2 = NdotH * NdotH;\nfloat denom = pow((NdotH2 * (r2 - 1.0) + 1.0), dtrExponent) * PI;\nreturn r2 / denom;\n}`),n.add(a`float geometricOcclusionKelemen(float LoH)\n{\nreturn 0.25 / (LoH * LoH);\n}`),n.add(a`vec3 brdfSpecularWater(in PBRShadingWater props, float roughness, vec3 F0, float F0Max)\n{\nvec3  F = fresnelReflection(props.VdotH, F0, F0Max);\nfloat dSun = normalDistributionWater(props.NdotH, roughness);\nfloat V = geometricOcclusionKelemen(props.LdotH);\nfloat diffusionSunHaze = mix(roughness + 0.045, roughness + 0.385, 1.0 - props.VdotH);\nfloat strengthSunHaze  = 1.2;\nfloat dSunHaze = normalDistributionWater(props.NdotH, diffusionSunHaze)*strengthSunHaze;\nreturn ((dSun + dSunHaze) * V) * F;\n}\nvec3 tonemapACES(const vec3 x) {\nreturn (x * (2.51 * x + 0.03)) / (x * (2.43 * x + 0.59) + 0.14);\n}`)}export{n as PhysicallyBasedRendering,r as PhysicallyBasedRenderingWater};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Uniform as r}from\"./Uniform.js\";import{BindType as s}from\"../shaderTechnique/BindType.js\";class e extends r{constructor(r,e){super(r,\"int\",s.Pass,((s,o,i)=>s.setUniform1i(r,e(o,i))))}}export{e as IntegerPassUniform};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Uniform as r}from\"./Uniform.js\";import{BindType as e}from\"../shaderTechnique/BindType.js\";class o extends r{constructor(r,o,s){super(r,\"mat4\",e.Draw,((e,s,t)=>e.setUniformMatrix4fv(r,o(s,t))),s)}}export{o as Matrix4sDrawUniform};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Uniform as r}from\"./Uniform.js\";import{BindType as s}from\"../shaderTechnique/BindType.js\";class e extends r{constructor(r,e,o){super(r,\"mat4\",s.Pass,((s,o,t)=>s.setUniformMatrix4fv(r,e(o,t))),o)}}export{e as Matrix4sPassUniform};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as e}from\"../../../../../../chunks/vec3f64.js\";import{RgbaFloatEncoding as a}from\"../util/RgbaFloatEncoding.glsl.js\";import{textureSize as s}from\"../util/WebGL2Utils.js\";import{Float4PassUniform as o}from\"../../shaderModules/Float4PassUniform.js\";import{IntegerPassUniform as t}from\"../../shaderModules/IntegerPassUniform.js\";import{NoParameters as i,glsl as r}from\"../../shaderModules/interfaces.js\";import{Matrix4sDrawUniform as d}from\"../../shaderModules/Matrix4sDrawUniform.js\";import{Matrix4sPassUniform as l}from\"../../shaderModules/Matrix4sPassUniform.js\";import{createTexture2DPassSizeUniforms as p}from\"../../shaderModules/Texture2DPassUniform.js\";import{TextureSizeUniformType as c}from\"../../shaderModules/TextureSizeUniformType.js\";class n extends i{constructor(){super(...arguments),this.origin=e()}}function h(e,a){a.receiveShadows&&(e.fragment.uniforms.add(new l(\"shadowMapMatrix\",((e,a)=>a.shadowMap.getShadowMapMatrices(e.origin)),4)),f(e,a))}function v(e,a){a.receiveShadows&&(e.fragment.uniforms.add(new d(\"shadowMapMatrix\",((e,a)=>a.shadowMap.getShadowMapMatrices(e.origin)),4)),f(e,a))}function f(e,i){const d=e.fragment;d.include(a),d.uniforms.add([...p(\"shadowMapTex\",((e,a)=>a.shadowMap.depthTexture),i.hasWebGL2Context?c.None:c.Size),new t(\"numCascades\",((e,a)=>a.shadowMap.numCascades)),new o(\"cascadeDistances\",((e,a)=>a.shadowMap.cascadeDistances))]),d.code.add(r`\n    int chooseCascade(float depth, out mat4 mat) {\n      vec4 distance = cascadeDistances;\n\n      // choose correct cascade\n      int i = depth < distance[1] ? 0 : depth < distance[2] ? 1 : depth < distance[3] ? 2 : 3;\n\n      mat = i == 0 ? shadowMapMatrix[0] : i == 1 ? shadowMapMatrix[1] : i == 2 ? shadowMapMatrix[2] : shadowMapMatrix[3];\n\n      return i;\n    }\n\n    vec3 lightSpacePosition(vec3 _vpos, mat4 mat) {\n      vec4 lv = mat * vec4(_vpos, 1.0);\n      lv.xy /= lv.w;\n      return 0.5 * lv.xyz + vec3(0.5);\n    }\n\n    vec2 cascadeCoordinates(int i, vec3 lvpos) {\n      return vec2(float(i - 2 * (i / 2)) * 0.5, float(i / 2) * 0.5) + 0.5 * lvpos.xy;\n    }\n\n    float readShadowMapDepth(vec2 uv, sampler2D _depthTex) {\n      return rgba2float(texture2D(_depthTex, uv));\n    }\n\n    float posIsInShadow(vec2 uv, vec3 lvpos, sampler2D _depthTex) {\n      return readShadowMapDepth(uv, _depthTex) < lvpos.z ? 1.0 : 0.0;\n    }\n\n    float filterShadow(vec2 uv, vec3 lvpos, float textureSize, sampler2D _depthTex) {\n      float halfPixelSize = 0.5 / textureSize;\n\n      // filter, offset by half pixels\n      vec2 st = fract((vec2(halfPixelSize) + uv) * textureSize);\n\n      float s00 = posIsInShadow(uv + vec2(-halfPixelSize, -halfPixelSize), lvpos, _depthTex);\n      float s10 = posIsInShadow(uv + vec2(halfPixelSize, -halfPixelSize), lvpos, _depthTex);\n      float s11 = posIsInShadow(uv + vec2(halfPixelSize, halfPixelSize), lvpos, _depthTex);\n      float s01 = posIsInShadow(uv + vec2(-halfPixelSize, halfPixelSize), lvpos, _depthTex);\n\n      return mix(mix(s00, s10, st.x), mix(s01, s11, st.x), st.y);\n    }\n\n    float readShadowMap(const in vec3 _vpos, float _linearDepth) {\n      mat4 mat;\n      int i = chooseCascade(_linearDepth, mat);\n\n      if (i >= numCascades) { return 0.0; }\n\n      vec3 lvpos = lightSpacePosition(_vpos, mat);\n\n      // vertex completely outside? -> no shadow\n      if (lvpos.z >= 1.0) { return 0.0; }\n      if (lvpos.x < 0.0 || lvpos.x > 1.0 || lvpos.y < 0.0 || lvpos.y > 1.0) { return 0.0; }\n\n      // calc coord in cascade texture\n      vec2 uv = cascadeCoordinates(i, lvpos);\n\n      vec2 textureSize = ${s(i,\"shadowMapTex\")};\n\n      return filterShadow(uv, lvpos, textureSize.x, shadowMapTex);\n    }\n  `)}export{n as ReadShadowMapBindParameters,v as ReadShadowMapDraw,h as ReadShadowMapPass};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Uniform as o}from\"./Uniform.js\";import{BindType as r}from\"../shaderTechnique/BindType.js\";class s extends o{constructor(o,s){super(o,\"bool\",r.Pass,((r,e,t)=>r.setUniform1b(o,s(e,t))))}}export{s as BooleanPassUniform};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIkM,IAAI;AAAE,SAASA,GAAEA,IAAEC,IAAE;AAAC,UAAOA,GAAE,uBAAsB;AAAA,IAAC,KAAK,EAAE;AAAQ,aAAOD,GAAE,WAAW,IAAI,EAAE,KAAI,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAE,KAAKA,GAAE,OAAO,KAAK,IAAIA;AAAA;AAAA,EAEhW;AAAA,IAAE,KAAK,EAAE;AAAW,aAAOA,GAAE,WAAW,IAAI,EAAE,KAAI,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAE,KAAKA,GAAE,OAAO,KAAK,IAAIA;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/G;AAAA,IAAE,KAAK,EAAE;AAAM,aAAOA,GAAE,WAAW,IAAI,EAAE,KAAI,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,UAAS,MAAM,GAAEA,GAAE,SAAS,IAAI,aAAY,MAAM,GAAE,KAAKA,GAAE,OAAO,KAAK,IAAIA;AAAA;AAAA;AAAA,EAGjL;AAAA,IAAE;AAAQ,MAAAE,GAAED,GAAE,qBAAqB;AAAA,IAAE,KAAK,EAAE;AAAK,aAAO,KAAKD,GAAE,OAAO,KAAK,IAAIA,uCAAsC;AAAA,IAAE,KAAK,EAAE;AAAM;AAAA,EAAM;AAAC;AAAC,CAAC,SAASG,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,GAAEA,GAAE,QAAM,CAAC,IAAE;AAAO,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACVjO,SAASC,GAAEA,IAAE;AAAC,EAAAA,GAAE,WAAW,IAAI,2BAA2B,GAAEA,GAAE,WAAW,IAAI,6BAA6B,GAAEA,GAAE,SAAS,KAAK,IAAIC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBvL;AAAC;;;ACrB2T,SAASC,GAAEA,IAAEC,IAAE;AAAC,UAAOD,GAAE,QAAQE,IAAED,EAAC,GAAED,GAAE,SAAS,KAAK,IAAIE;AAAA;AAAA;AAAA,MAGlXD,GAAE,uBAAqB,eAAa,EAAE;AAAA;AAAA,GAEzC,GAAEA,GAAE,uBAAsB;AAAA,IAAC,KAAK,EAAE;AAAA,IAAQ,KAAK,EAAE;AAAW,aAAO,KAAKD,GAAE,SAAS,KAAK,IAAIE;AAAA;AAAA,EAE7F;AAAA,IAAE,KAAK,EAAE;AAAM,aAAOF,GAAE,QAAQG,EAAC,GAAE,KAAKH,GAAE,SAAS,KAAK,IAAIE;AAAA;AAAA,EAE5D;AAAA,IAAE;AAAQ,MAAAE,GAAEH,GAAE,qBAAqB;AAAA,IAAE,KAAK,EAAE;AAAA,IAAK,KAAK,EAAE;AAAM;AAAA,EAAM;AAAC;;;ACTlB,IAAMI,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,YAAUA,GAAE,UAAS,KAAK,uBAAqBA,GAAE,cAAa,KAAK,UAAQA,GAAE;AAAA,EAAM;AAAA,EAAC,UAAS;AAAC,SAAK,qBAAqB,QAAQ,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,4BAA2B;AAAC,WAAO,KAAK,qBAAqB,oBAAoB;AAAA,EAAwB;AAAA,EAAC,gBAAgBA,IAAED,IAAE;AAAC,WAAO,KAAK,aAAW,KAAK,qBAAqB,kBAAkBC,IAAE,KAAK,UAAU,iBAAiB,KAAK,SAAQD,EAAC,GAAE,KAAK,UAAU,GAAE,KAAK;AAAA,EAAU;AAAA,EAAC,gBAAgBA,IAAE;AAAC,WAAO,EAAE;AAAA,EAAM;AAAC;;;ACA5R,IAAM,IAAN,cAAgBE,GAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,cAAY,GAAE,KAAK,YAAU,OAAG,KAAK,qBAAmBA,GAAE,YAAW,KAAK,aAAWA,GAAE,WAAU,KAAK,SAASA,GAAE,WAAW,CAAAA,OAAG,KAAK,WAASA,EAAE,GAAE,KAAK,SAASA,GAAE,iBAAiB,CAAAA,OAAG,KAAK,iBAAeA,EAAE,GAAE,KAAK,SAASA,GAAE,mBAAmB,CAAAA,OAAG,KAAK,mBAAiBA,EAAE,GAAE,KAAK,SAASA,GAAE,oBAAoB,CAAAA,OAAG,KAAK,oBAAkBA,EAAE,GAAE,KAAK,SAASA,GAAE,4BAA4B,CAAAA,OAAG,KAAK,4BAA0BA,EAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,iBAAe,EAAE,KAAK,cAAc,GAAE,KAAK,mBAAiB,EAAE,KAAK,gBAAgB,GAAE,KAAK,oBAAkB,EAAE,KAAK,iBAAiB,GAAE,KAAK,4BAA0B,EAAE,KAAK,yBAAyB,GAAE,KAAK,YAAU;AAAA,EAAE;AAAA,EAAC,gBAAgBA,IAAE;AAAC,WAAO,MAAI,KAAK,cAAY,EAAE,SAAO,EAAE;AAAA,EAAO;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAO,IAAIC,GAAE,EAAE,KAAK,QAAQ,IAAE,KAAK,SAAS,YAAU,MAAK,EAAE,KAAK,cAAc,IAAE,KAAK,eAAe,YAAU,MAAK,EAAE,KAAK,gBAAgB,IAAE,KAAK,iBAAiB,YAAU,MAAK,EAAE,KAAK,iBAAiB,IAAE,KAAK,kBAAkB,YAAU,MAAK,EAAE,KAAK,yBAAyB,IAAE,KAAK,0BAA0B,YAAU,IAAI;AAAA,EAAC;AAAA,EAAC,cAAcC,IAAE;AAAC,KAAC,EAAE,KAAK,QAAQ,KAAGA,OAAI,KAAK,SAAS,QAAM,KAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,aAAWA,IAAE,KAAK,SAAS,KAAK,YAAY,CAAAF,OAAG,KAAK,WAASA,EAAE;AAAA,EAAE;AAAA,EAAC,SAASE,IAAEC,IAAE;AAAC,QAAG,EAAED,EAAC,EAAE,QAAO,KAAKC,GAAE,IAAI;AAAE,UAAMC,KAAE,KAAK,mBAAmB,QAAQF,EAAC;AAAE,QAAG,EAAEE,EAAC,EAAE,QAAM,EAAE,KAAK,aAAY,KAAKA,GAAE,KAAM,CAAAF,OAAG;AAAC,UAAG,KAAK,UAAU,QAAO,EAAEA,EAAC,GAAE,KAAKC,GAAE,IAAI;AAAE,MAAAA,GAAED,EAAC;AAAA,IAAC,CAAE,EAAE,QAAS,MAAI,EAAE,KAAK,WAAY;AAAE,IAAAC,GAAEC,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMH,KAAN,cAAgBI,GAAC;AAAA,EAAC,YAAYL,KAAE,MAAKE,KAAE,MAAKI,KAAE,MAAKC,KAAE,MAAKJ,KAAE,MAAK;AAAC,UAAM,GAAE,KAAK,UAAQH,IAAE,KAAK,gBAAcE,IAAE,KAAK,kBAAgBI,IAAE,KAAK,mBAAiBC,IAAE,KAAK,2BAAyBJ;AAAA,EAAC;AAAC;;;ACArqC,IAAM,IAAEK,GAAE,GAAE,KAAG,GAAE;AAAE,IAAIC;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,wBAAsB,CAAC,IAAE,yBAAwBA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,QAAM,CAAC,IAAE;AAAO,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAqB,SAAS,EAAEE,IAAE,GAAE;AAAC,QAAMC,KAAED,GAAE,UAAS,IAAE,EAAE,+BAA6B,EAAE,sBAAoB,EAAE;AAAoB,MAAG,EAAE,YAAUE,GAAE,UAAQ,KAAGF,GAAE,QAAQG,IAAE,CAAC,GAAE,EAAE,YAAUD,GAAE,UAAU,KAAG,EAAE,YAAUA,GAAE,UAAS;AAAC,QAAG,EAAE,YAAUA,GAAE,QAAO;AAAC,MAAAD,GAAE,KAAK,IAAIG;AAAA;AAAA,iBAEt1C;AAAE,YAAMJ,KAAE,EAAE,uBAAqB,EAAE,mBAAiBA,GAAE,OAAKA,GAAE,OAAKA,GAAE,MAAKK,KAAE,EAAE;AAAmB,QAAE,gCAA8BJ,GAAE,SAAS,IAAII,OAAI,EAAE,OAAK,EAAE,wBAAwB,CAAAL,OAAGA,GAAE,0BAA0BA,EAAC,IAAEM,GAAE,wBAAwB,CAAAN,OAAGA,GAAE,0BAA0BA,EAAC,CAAC,GAAEC,GAAE,KAAK,IAAIG;AAAA;AAAA;AAAA;AAAA,EAI7S,IAAG,EAAE,uBAAqBH,GAAE,SAAS,IAAII,OAAI,EAAE,OAAK,EAAE,eAAe,CAAAL,OAAGA,GAAE,iBAAiBA,EAAC,IAAEM,GAAE,eAAe,CAAAN,OAAGA,GAAE,iBAAiBA,EAAC,CAAC,GAAEC,GAAE,KAAK,IAAIG;AAAA;AAAA,EAEpJ,IAAG,EAAE,uBAAqBH,GAAE,SAAS,IAAII,OAAI,EAAE,OAAK,EAAE,gBAAgB,CAAAL,OAAGA,GAAE,kBAAkBA,EAAC,IAAEM,GAAE,gBAAgB,CAAAN,OAAGA,GAAE,kBAAkBA,EAAC,CAAC,GAAEC,GAAE,KAAK,IAAIG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxJ,KAAGH,GAAE,KAAK,IAAIG,6CAA4C,GAAEH,GAAE,SAAS,IAAII,OAAI,EAAE,OAAK,CAAC,IAAIL,GAAE,kBAAkB,CAAAA,OAAGA,GAAE,cAAe,GAAE,IAAIA,GAAE,cAAc,CAAAA,OAAGA,GAAE,UAAW,CAAC,IAAE,CAAC,IAAII,GAAE,kBAAkB,CAAAJ,OAAGA,GAAE,cAAe,GAAE,IAAII,GAAE,cAAc,CAAAJ,OAAGA,GAAE,UAAW,CAAC,CAAC,GAAEC,GAAE,KAAK,IAAIG;AAAA;AAAA;AAAA;AAAA;AAAA,QAKxQ,IAAEA,qBAAkB,EAAE;AAAA,QACtB,EAAE,uCAAqCA,oCAAiC,EAAE;AAAA,QAC1E,EAAE,8BAA4B,EAAE,uBAAqBA;AAAA,6BAChCG,GAAE,GAAE,sBAAsB,CAAC;AAAA,qDACLH,wCAAqC,EAAE;AAAA,QAClF,EAAE,8BAA4BA,2BAAwB,EAAE;AAAA,QACxD,EAAE,qBAAmB,EAAE,uBAAqBA;AAAA,6BACvBG,GAAE,GAAE,aAAa,CAAC;AAAA,uCACVH,0BAAuB,EAAE;AAAA,QACtD,EAAE,+BAA6BA,4BAAyB,EAAE;AAAA,QAC1D,EAAE,sBAAoB,EAAE,uBAAqBA;AAAA,6BACxBG,GAAE,GAAE,cAAc,CAAC;AAAA,wCACVH,2BAAwB,EAAE;AAAA;AAAA,GAE7D;AAAA,IAAC;AAAA,EAAC,MAAM,CAAAH,GAAE,KAAK,IAAIG,6CAA4C;AAAA,MAAO,CAAAH,GAAE,KAAK,IAAIG;AAAA;AAAA;AAAA;AAAA,0CAI1C;AAAC;;;ACpCyc,SAASI,GAAEC,IAAE,GAAE;AAAC,QAAMD,KAAEC,GAAE,UAASC,KAAE,WAAS,EAAE,kCAAgC,EAAE,kCAAgC;AAAE,QAAIA,MAAGF,GAAE,SAAS,IAAI,IAAIG,GAAE,sBAAsB,CAACF,IAAEG,OAAI,EAAEC,IAAED,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,CAAC,GAAEJ,GAAE,KAAK,IAAIE;AAAA;AAAA;AAAA,EAG3uB,KAAG,MAAIA,MAAGF,GAAE,SAAS,IAAI,CAAC,IAAI,EAAE,uBAAuB,CAACM,IAAEL,OAAIM,GAAEA,IAAEN,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,GAAE,IAAI,EAAE,uBAAuB,CAACK,IAAEL,OAAIM,GAAEA,IAAEN,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,GAAE,IAAI,EAAE,uBAAuB,CAACK,IAAEL,OAAIM,GAAEA,IAAEN,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC,GAAED,GAAE,KAAK,IAAIE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAa5Y,KAAG,MAAIA,OAAIF,GAAE,SAAS,IAAI,CAAC,IAAIG,GAAE,sBAAsB,CAACF,IAAEG,OAAI,EAAEC,IAAED,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,GAAE,IAAI,EAAE,wBAAwB,CAACE,IAAEL,OAAIM,GAAEA,IAAEN,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,GAAE,IAAI,EAAE,wBAAwB,CAACK,IAAEL,OAAIM,GAAEA,IAAEN,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,GAAE,IAAI,EAAE,wBAAwB,CAACK,IAAEL,OAAIM,GAAEA,IAAEN,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,GAAE,IAAI,EAAE,wBAAwB,CAACK,IAAEL,OAAIM,GAAEA,IAAEN,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,GAAE,IAAI,EAAE,wBAAwB,CAACK,IAAEL,OAAIM,GAAEA,IAAEN,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,GAAE,IAAI,EAAE,wBAAwB,CAACK,IAAEL,OAAIM,GAAEA,IAAEN,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,GAAEA,GAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC,GAAED,GAAE,KAAK,IAAIE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyB31B,GAAE,EAAE,YAAUM,GAAE,UAAQ,EAAE,YAAUA,GAAE,aAAWR,GAAE,KAAK,IAAIE;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D;AAAE;AAAC,IAAMG,KAAE,EAAE;AAAV,IAAYE,KAAEN,GAAE;;;AC9C4L,SAASQ,GAAEC,IAAE;AAAC,EAAAA,GAAE,SAAS,IAAI,IAAIC,GAAE,sBAAsB,CAACC,IAAEF,OAAIA,GAAE,SAAS,UAAU,SAAU,CAAC;AAAC;AAAC,SAASG,GAAEH,IAAE;AAAC,EAAAA,GAAE,SAAS,IAAI,IAAIC,GAAE,sBAAsB,CAACC,IAAEF,OAAIA,GAAE,SAAS,UAAU,SAAU,CAAC;AAAC;AAAC,SAASC,GAAEC,IAAEE,IAAE;AAAC,EAAAA,GAAE,0BAAwBF,GAAE,SAAS,IAAI,IAAIH,GAAE,uBAAuB,CAACG,IAAEF,OAAIA,GAAE,SAAS,cAAY,IAAEA,GAAE,SAAS,aAAc,CAAC,IAAEE,GAAE,UAAU,IAAI,uBAAsB,SAAQ,CAAC;AAAC;AAAC,SAASG,GAAEH,IAAEF,IAAE;AAAC,QAAMK,KAAEH,GAAE;AAAS,EAAAH,GAAEM,EAAC,GAAEF,GAAEE,EAAC,GAAEJ,GAAEI,IAAEL,EAAC,GAAEK,GAAE,KAAK,IAAIN;AAAA;AAAA;AAAA;AAAA,EAIrpB;AAAC;;;ACJsD,SAASO,GAAEA,IAAE;AAAC,QAAMC,KAAED,GAAE,SAAS;AAAK,EAAAC,GAAE,IAAIC;AAAA;AAAA;AAAA,EAGnG,GAAED,GAAE,IAAIC;AAAA;AAAA;AAAA,EAGR,GAAED,GAAE,IAAIC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOR;AAAC;;;ACb2O,SAASC,GAAEA,IAAEC,IAAE;AAAC,QAAM,IAAED,GAAE,SAAS;AAAK,EAAAA,GAAE,QAAQE,EAAC,GAAED,GAAE,YAAUE,GAAE,UAAQF,GAAE,YAAUE,GAAE,aAAWF,GAAE,YAAUE,GAAE,WAASF,GAAE,YAAUE,GAAE,qBAAmB,EAAE,IAAIC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9Y,GAAE,EAAE,IAAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOR,IAAGH,GAAE,YAAUE,GAAE,UAAQF,GAAE,YAAUE,GAAE,cAAYH,GAAE,QAAQE,EAAC,GAAE,EAAE,IAAIE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAuBrE,GAAE,EAAE,IAAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQT,GAAE,EAAE,IAAIA;AAAA;AAAA,EAER,GAAE,EAAE,IAAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOR;AAAE;AAAC,SAASH,GAAEI,IAAED,IAAE;AAAC,QAAMJ,KAAEK,GAAE,SAAS;AAAK,EAAAA,GAAE,QAAQH,EAAC,GAAEF,GAAE,IAAII;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAWxCA,GAAE,+BAA6B,QAAM,KAAK;AAAA,GAC/D,GAAEJ,GAAE,IAAII;AAAA;AAAA,EAET,GAAEJ,GAAE,IAAII;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,GAAEJ,GAAE,IAAII;AAAA;AAAA;AAAA,EAGR,GAAEJ,GAAE,IAAII;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYR;AAAC;;;ACvF8F,IAAME,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAED,IAAE;AAAC,UAAMC,IAAE,OAAM,EAAE,MAAM,CAACC,IAAEC,IAAEC,OAAIF,GAAE,aAAaD,IAAED,GAAEG,IAAEC,EAAC,CAAC,CAAE;AAAA,EAAC;AAAC;;;ACA7F,IAAMC,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAED,IAAEE,IAAE;AAAC,UAAMD,IAAE,QAAO,EAAE,MAAM,CAACE,IAAED,IAAEE,OAAID,GAAE,oBAAoBF,IAAED,GAAEE,IAAEE,EAAC,CAAC,GAAGF,EAAC;AAAA,EAAC;AAAC;;;ACAzG,IAAMG,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAED,IAAEE,IAAE;AAAC,UAAMD,IAAE,QAAO,EAAE,MAAM,CAACE,IAAED,KAAEE,OAAID,GAAE,oBAAoBF,IAAED,GAAEE,KAAEE,EAAC,CAAC,GAAGF,EAAC;AAAA,EAAC;AAAC;;;ACA4mB,SAASG,GAAEC,IAAEC,IAAE;AAAC,EAAAA,GAAE,mBAAiBD,GAAE,SAAS,SAAS,IAAI,IAAIA,GAAE,mBAAmB,CAACA,IAAEC,OAAIA,GAAE,UAAU,qBAAqBD,GAAE,MAAM,GAAG,CAAC,CAAC,GAAE,EAAEA,IAAEC,EAAC;AAAE;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,EAAAA,GAAE,mBAAiBD,GAAE,SAAS,SAAS,IAAI,IAAIE,GAAE,mBAAmB,CAACF,IAAEC,OAAIA,GAAE,UAAU,qBAAqBD,GAAE,MAAM,GAAG,CAAC,CAAC,GAAE,EAAEA,IAAEC,EAAC;AAAE;AAAC,SAAS,EAAED,IAAEG,IAAE;AAAC,QAAMC,KAAEJ,GAAE;AAAS,EAAAI,GAAE,QAAQH,EAAC,GAAEG,GAAE,SAAS,IAAI,CAAC,GAAG,EAAE,gBAAgB,CAACJ,IAAEC,OAAIA,GAAE,UAAU,cAAcE,GAAE,mBAAiBH,GAAE,OAAKA,GAAE,IAAI,GAAE,IAAIA,GAAE,eAAe,CAACA,IAAEC,OAAIA,GAAE,UAAU,WAAY,GAAE,IAAI,EAAE,oBAAoB,CAACD,IAAEC,OAAIA,GAAE,UAAU,gBAAiB,CAAC,CAAC,GAAEG,GAAE,KAAK,IAAIF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BA2D51CG,GAAEF,IAAE,cAAc,CAAC;AAAA;AAAA;AAAA;AAAA,GAI3C;AAAC;;;AC/D6F,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYG,IAAEC,IAAE;AAAC,UAAMD,IAAE,QAAO,EAAE,MAAM,CAACE,IAAEC,IAAEC,OAAIF,GAAE,aAAaF,IAAEC,GAAEE,IAAEC,EAAC,CAAC,CAAE;AAAA,EAAC;AAAC;", "names": ["o", "v", "n", "e", "t", "o", "a", "s", "o", "t", "n", "t", "e", "t", "o", "e", "r", "u", "n", "s", "i", "r", "d", "e", "e", "m", "d", "a", "o", "s", "u", "r", "m", "n", "o", "e", "t", "a", "i", "r", "d", "o", "n", "e", "i", "a", "t", "r", "t", "a", "o", "n", "r", "t", "d", "o", "e", "e", "r", "s", "o", "i", "o", "r", "s", "e", "t", "e", "r", "o", "s", "t", "h", "e", "a", "o", "i", "d", "r", "o", "s", "r", "e", "t"]}