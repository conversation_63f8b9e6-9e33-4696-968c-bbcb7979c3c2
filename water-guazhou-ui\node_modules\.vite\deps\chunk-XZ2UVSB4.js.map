{"version": 3, "sources": ["../../@arcgis/core/rest/query/operations/query.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../request.js\";import{isSome as t}from\"../../../core/maybe.js\";import{urlToObject as r,join as n}from\"../../../core/urlUtils.js\";import{getJsonType as i}from\"../../../geometry/support/jsonUtils.js\";import{normalizeCentralMeridian as o}from\"../../../geometry/support/normalizeUtils.js\";import{mapParameters as a}from\"../../operations/urlUtils.js\";import{parsePBFFeatureQuery as s}from\"./pbfQueryUtils.js\";import{applyFeatureSetZUnitScaling as u}from\"./queryZScale.js\";const l=\"Layer does not support extent calculation.\";function m(e,t){if(t&&\"extent\"===e.type)return`${e.xmin},${e.ymin},${e.xmax},${e.ymax}`;if(t&&\"point\"===e.type)return`${e.x},${e.y}`;const r=e.toJSON();return delete r.spatialReference,JSON.stringify(r)}function y(e,r){const n=e.geometry,o=e.toJSON();delete o.compactGeometryEnabled,delete o.defaultSpatialReferenceEnabled;const a=o;let s,u,l;if(t(n)&&(u=n.spatialReference,l=n.spatialReference.wkid||JSON.stringify(n.spatialReference),a.geometryType=i(n),a.geometry=m(n,e.compactGeometryEnabled),a.inSR=l),o.groupByFieldsForStatistics&&(a.groupByFieldsForStatistics=o.groupByFieldsForStatistics.join(\",\")),o.objectIds&&(a.objectIds=o.objectIds.join(\",\")),o.orderByFields&&(a.orderByFields=o.orderByFields.join(\",\")),!o.outFields||!o.returnDistinctValues&&(r?.returnCountOnly||r?.returnExtentOnly||r?.returnIdsOnly)?delete a.outFields:o.outFields.includes(\"*\")?a.outFields=\"*\":a.outFields=o.outFields.join(\",\"),o.outSR?(a.outSR=o.outSR.wkid||JSON.stringify(o.outSR),s=e.outSpatialReference):n&&(o.returnGeometry||o.returnCentroid)&&(a.outSR=a.inSR,s=u),o.returnGeometry&&delete o.returnGeometry,o.outStatistics&&(a.outStatistics=JSON.stringify(o.outStatistics)),o.fullText&&(a.fullText=JSON.stringify(o.fullText)),o.pixelSize&&(a.pixelSize=JSON.stringify(o.pixelSize)),o.quantizationParameters&&(e.defaultSpatialReferenceEnabled&&t(u)&&t(e.quantizationParameters)&&t(e.quantizationParameters.extent)&&u.equals(e.quantizationParameters.extent.spatialReference)&&delete o.quantizationParameters.extent.spatialReference,a.quantizationParameters=JSON.stringify(o.quantizationParameters)),o.parameterValues&&(a.parameterValues=JSON.stringify(o.parameterValues)),o.rangeValues&&(a.rangeValues=JSON.stringify(o.rangeValues)),o.dynamicDataSource&&(a.layer=JSON.stringify({source:o.dynamicDataSource}),delete o.dynamicDataSource),o.timeExtent){const e=o.timeExtent,{start:t,end:r}=e;null==t&&null==r||(a.time=t===r?t:`${t??\"null\"},${r??\"null\"}`),delete o.timeExtent}return e.defaultSpatialReferenceEnabled&&t(u)&&t(s)&&u.equals(s)&&(a.defaultSR=a.inSR,delete a.inSR,delete a.outSR),a}async function c(e,r,n,i){const o=t(r.timeExtent)&&r.timeExtent.isEmpty?{data:{features:[]}}:await E(e,r,\"json\",i);return u(r,n,o.data),o}async function f(e,r,n,i){if(t(r.timeExtent)&&r.timeExtent.isEmpty)return{data:n.createFeatureResult()};const o=await d(e,r,i),a=o;return a.data=s(o.data,n),a}function d(e,t,r){return E(e,t,\"pbf\",r)}function p(e,r,n){return t(r.timeExtent)&&r.timeExtent.isEmpty?Promise.resolve({data:{objectIds:[]}}):E(e,r,\"json\",n,{returnIdsOnly:!0})}function S(e,r,n){return t(r.timeExtent)&&r.timeExtent.isEmpty?Promise.resolve({data:{count:0}}):E(e,r,\"json\",n,{returnIdsOnly:!0,returnCountOnly:!0})}function x(e,r,n){return t(r.timeExtent)&&r.timeExtent.isEmpty?Promise.resolve({data:{count:0,extent:null}}):E(e,r,\"json\",n,{returnExtentOnly:!0,returnCountOnly:!0}).then((e=>{const t=e.data;if(t.hasOwnProperty(\"extent\"))return e;if(t.features)throw new Error(l);if(t.hasOwnProperty(\"count\"))throw new Error(l);return e}))}function E(i,s,u,l={},m={}){const c=\"string\"==typeof i?r(i):i,f=s.geometry?[s.geometry]:[];return l.responseType=\"pbf\"===u?\"array-buffer\":\"json\",o(f,null,l).then((r=>{const i=r&&r[0];t(i)&&((s=s.clone()).geometry=i);const o=a({...c.query,f:u,...m,...y(s,m)});return e(n(c.path,\"query\"),{...l,query:{...o,...l.query}})}))}export{m as encodeGeometry,c as executeQuery,S as executeQueryForCount,x as executeQueryForExtent,p as executeQueryForIds,f as executeQueryPBF,d as executeQueryPBFBuffer,y as queryToQueryStringParameters,E as runQuery};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAIue,IAAM,IAAE;AAA6C,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAGA,MAAG,aAAW,EAAE,KAAK,QAAM,GAAG,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI;AAAG,MAAGA,MAAG,YAAU,EAAE,KAAK,QAAM,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AAAG,QAAMC,KAAE,EAAE,OAAO;AAAE,SAAO,OAAOA,GAAE,kBAAiB,KAAK,UAAUA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,QAAM,IAAE,EAAE,UAAS,IAAE,EAAE,OAAO;AAAE,SAAO,EAAE,wBAAuB,OAAO,EAAE;AAA+B,QAAM,IAAE;AAAE,MAAI,GAAE,GAAEC;AAAE,MAAG,EAAE,CAAC,MAAI,IAAE,EAAE,kBAAiBA,KAAE,EAAE,iBAAiB,QAAM,KAAK,UAAU,EAAE,gBAAgB,GAAE,EAAE,eAAa,EAAE,CAAC,GAAE,EAAE,WAAS,EAAE,GAAE,EAAE,sBAAsB,GAAE,EAAE,OAAKA,KAAG,EAAE,+BAA6B,EAAE,6BAA2B,EAAE,2BAA2B,KAAK,GAAG,IAAG,EAAE,cAAY,EAAE,YAAU,EAAE,UAAU,KAAK,GAAG,IAAG,EAAE,kBAAgB,EAAE,gBAAc,EAAE,cAAc,KAAK,GAAG,IAAG,CAAC,EAAE,aAAW,CAAC,EAAE,0BAAuBD,MAAA,gBAAAA,GAAG,qBAAiBA,MAAA,gBAAAA,GAAG,sBAAkBA,MAAA,gBAAAA,GAAG,kBAAe,OAAO,EAAE,YAAU,EAAE,UAAU,SAAS,GAAG,IAAE,EAAE,YAAU,MAAI,EAAE,YAAU,EAAE,UAAU,KAAK,GAAG,GAAE,EAAE,SAAO,EAAE,QAAM,EAAE,MAAM,QAAM,KAAK,UAAU,EAAE,KAAK,GAAE,IAAE,EAAE,uBAAqB,MAAI,EAAE,kBAAgB,EAAE,oBAAkB,EAAE,QAAM,EAAE,MAAK,IAAE,IAAG,EAAE,kBAAgB,OAAO,EAAE,gBAAe,EAAE,kBAAgB,EAAE,gBAAc,KAAK,UAAU,EAAE,aAAa,IAAG,EAAE,aAAW,EAAE,WAAS,KAAK,UAAU,EAAE,QAAQ,IAAG,EAAE,cAAY,EAAE,YAAU,KAAK,UAAU,EAAE,SAAS,IAAG,EAAE,2BAAyB,EAAE,kCAAgC,EAAE,CAAC,KAAG,EAAE,EAAE,sBAAsB,KAAG,EAAE,EAAE,uBAAuB,MAAM,KAAG,EAAE,OAAO,EAAE,uBAAuB,OAAO,gBAAgB,KAAG,OAAO,EAAE,uBAAuB,OAAO,kBAAiB,EAAE,yBAAuB,KAAK,UAAU,EAAE,sBAAsB,IAAG,EAAE,oBAAkB,EAAE,kBAAgB,KAAK,UAAU,EAAE,eAAe,IAAG,EAAE,gBAAc,EAAE,cAAY,KAAK,UAAU,EAAE,WAAW,IAAG,EAAE,sBAAoB,EAAE,QAAM,KAAK,UAAU,EAAC,QAAO,EAAE,kBAAiB,CAAC,GAAE,OAAO,EAAE,oBAAmB,EAAE,YAAW;AAAC,UAAME,KAAE,EAAE,YAAW,EAAC,OAAMH,IAAE,KAAIC,GAAC,IAAEE;AAAE,YAAMH,MAAG,QAAMC,OAAI,EAAE,OAAKD,OAAIC,KAAED,KAAE,GAAGA,MAAG,MAAM,IAAIC,MAAG,MAAM,KAAI,OAAO,EAAE;AAAA,EAAU;AAAC,SAAO,EAAE,kCAAgC,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,EAAE,OAAO,CAAC,MAAI,EAAE,YAAU,EAAE,MAAK,OAAO,EAAE,MAAK,OAAO,EAAE,QAAO;AAAC;AAAC,eAAeG,GAAE,GAAEH,IAAE,GAAE,GAAE;AAAC,QAAM,IAAE,EAAEA,GAAE,UAAU,KAAGA,GAAE,WAAW,UAAQ,EAAC,MAAK,EAAC,UAAS,CAAC,EAAC,EAAC,IAAE,MAAM,EAAE,GAAEA,IAAE,QAAO,CAAC;AAAE,SAAOA,GAAEA,IAAE,GAAE,EAAE,IAAI,GAAE;AAAC;AAAC,eAAe,EAAE,GAAEA,IAAE,GAAE,GAAE;AAAC,MAAG,EAAEA,GAAE,UAAU,KAAGA,GAAE,WAAW,QAAQ,QAAM,EAAC,MAAK,EAAE,oBAAoB,EAAC;AAAE,QAAM,IAAE,MAAM,EAAE,GAAEA,IAAE,CAAC,GAAE,IAAE;AAAE,SAAO,EAAE,OAAKD,GAAE,EAAE,MAAK,CAAC,GAAE;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAEC,IAAE;AAAC,SAAO,EAAE,GAAED,IAAE,OAAMC,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,SAAO,EAAEA,GAAE,UAAU,KAAGA,GAAE,WAAW,UAAQ,QAAQ,QAAQ,EAAC,MAAK,EAAC,WAAU,CAAC,EAAC,EAAC,CAAC,IAAE,EAAE,GAAEA,IAAE,QAAO,GAAE,EAAC,eAAc,KAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,SAAO,EAAEA,GAAE,UAAU,KAAGA,GAAE,WAAW,UAAQ,QAAQ,QAAQ,EAAC,MAAK,EAAC,OAAM,EAAC,EAAC,CAAC,IAAE,EAAE,GAAEA,IAAE,QAAO,GAAE,EAAC,eAAc,MAAG,iBAAgB,KAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,SAAO,EAAEA,GAAE,UAAU,KAAGA,GAAE,WAAW,UAAQ,QAAQ,QAAQ,EAAC,MAAK,EAAC,OAAM,GAAE,QAAO,KAAI,EAAC,CAAC,IAAE,EAAE,GAAEA,IAAE,QAAO,GAAE,EAAC,kBAAiB,MAAG,iBAAgB,KAAE,CAAC,EAAE,KAAM,CAAAE,OAAG;AAAC,UAAMH,KAAEG,GAAE;AAAK,QAAGH,GAAE,eAAe,QAAQ,EAAE,QAAOG;AAAE,QAAGH,GAAE,SAAS,OAAM,IAAI,MAAM,CAAC;AAAE,QAAGA,GAAE,eAAe,OAAO,EAAE,OAAM,IAAI,MAAM,CAAC;AAAE,WAAOG;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAED,KAAE,CAAC,GAAEG,KAAE,CAAC,GAAE;AAAC,QAAMD,KAAE,YAAU,OAAO,IAAE,EAAE,CAAC,IAAE,GAAEE,KAAE,EAAE,WAAS,CAAC,EAAE,QAAQ,IAAE,CAAC;AAAE,SAAOJ,GAAE,eAAa,UAAQ,IAAE,iBAAe,QAAO,EAAEI,IAAE,MAAKJ,EAAC,EAAE,KAAM,CAAAD,OAAG;AAAC,UAAMM,KAAEN,MAAGA,GAAE,CAAC;AAAE,MAAEM,EAAC,OAAK,IAAE,EAAE,MAAM,GAAG,WAASA;AAAG,UAAM,IAAE,EAAE,EAAC,GAAGH,GAAE,OAAM,GAAE,GAAE,GAAGC,IAAE,GAAG,EAAE,GAAEA,EAAC,EAAC,CAAC;AAAE,WAAO,EAAE,EAAED,GAAE,MAAK,OAAO,GAAE,EAAC,GAAGF,IAAE,OAAM,EAAC,GAAG,GAAE,GAAGA,GAAE,MAAK,EAAC,CAAC;AAAA,EAAC,CAAE;AAAC;", "names": ["t", "r", "l", "e", "c", "m", "f", "i"]}