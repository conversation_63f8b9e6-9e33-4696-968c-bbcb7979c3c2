{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/Laserline.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as o}from\"../../../../../core/maybe.js\";import{ReadLinearDepth as e}from\"./output/ReadLinearDepth.glsl.js\";import{CameraSpace as r}from\"./util/CameraSpace.glsl.js\";import{Float2PassUniform as l}from\"../shaderModules/Float2PassUniform.js\";import{Float3PassUniform as a}from\"../shaderModules/Float3PassUniform.js\";import{FloatPassUniform as t}from\"../shaderModules/FloatPassUniform.js\";import{glsl as n}from\"../shaderModules/interfaces.js\";import{Texture2DPassUniform as d}from\"../shaderModules/Texture2DPassUniform.js\";function i(i,s){i.extensions.add(\"GL_OES_standard_derivatives\");const p=i.fragment;p.include(e),i.include(r),p.uniforms.add([new t(\"globalAlpha\",(o=>o.globalAlpha)),new a(\"glowColor\",(o=>o.glowColor)),new t(\"glowWidth\",((o,e)=>o.glowWidth*e.camera.pixelRatio)),new t(\"glowFalloff\",(o=>o.glowFalloff)),new a(\"innerColor\",(o=>o.innerColor)),new t(\"innerWidth\",((o,e)=>o.innerWidth*e.camera.pixelRatio)),new d(\"depthMap\",((o,e)=>e.linearDepthTexture)),new l(\"nearFar\",((o,e)=>e.camera.nearFar)),new d(\"frameColor\",((o,e)=>e.mainColorTexture))]),p.code.add(n`vec4 blendPremultiplied(vec4 source, vec4 dest) {\nfloat oneMinusSourceAlpha = 1.0 - source.a;\nreturn vec4(\nsource.rgb + dest.rgb * oneMinusSourceAlpha,\nsource.a + dest.a * oneMinusSourceAlpha\n);\n}`),p.code.add(n`vec4 premultipliedColor(vec3 rgb, float alpha) {\nreturn vec4(rgb * alpha, alpha);\n}`),p.code.add(n`vec4 laserlineProfile(float dist) {\nif (dist > glowWidth) {\nreturn vec4(0.0);\n}\nfloat innerAlpha = (1.0 - smoothstep(0.0, innerWidth, dist));\nfloat glowAlpha = pow(max(0.0, 1.0 - dist / glowWidth), glowFalloff);\nreturn blendPremultiplied(\npremultipliedColor(innerColor, innerAlpha),\npremultipliedColor(glowColor, glowAlpha)\n);\n}`),p.code.add(n`bool laserlineReconstructFromDepth(out vec3 pos, out vec3 normal, out float depthDiscontinuityAlpha) {\nfloat depth = linearDepthFromTexture(depthMap, uv, nearFar);\nif (-depth == nearFar[0]) {\nreturn false;\n}\npos = reconstructPosition(gl_FragCoord.xy, depth);\nnormal = normalize(cross(dFdx(pos), dFdy(pos)));\nfloat ddepth = fwidth(depth);\ndepthDiscontinuityAlpha = 1.0 - smoothstep(0.0, 0.01, -ddepth / depth);\nreturn true;\n}`),s.contrastControlEnabled?(p.uniforms.add(new t(\"globalAlphaContrastBoost\",(e=>o(e.globalAlphaContrastBoost)?e.globalAlphaContrastBoost:1))),p.code.add(n`float rgbToLuminance(vec3 color) {\nreturn dot(vec3(0.2126, 0.7152, 0.0722), color);\n}\nvec4 laserlineOutput(vec4 color) {\nfloat backgroundLuminance = rgbToLuminance(texture2D(frameColor, uv).rgb);\nfloat alpha = clamp(globalAlpha * max(backgroundLuminance * globalAlphaContrastBoost, 1.0), 0.0, 1.0);\nreturn color * alpha;\n}`)):p.code.add(n`vec4 laserlineOutput(vec4 color) {\nreturn color * globalAlpha;\n}`)}export{i as Laserline};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAIohB,SAAS,EAAEA,IAAE,GAAE;AAAC,EAAAA,GAAE,WAAW,IAAI,6BAA6B;AAAE,QAAM,IAAEA,GAAE;AAAS,IAAE,QAAQ,CAAC,GAAEA,GAAE,QAAQ,CAAC,GAAE,EAAE,SAAS,IAAI,CAAC,IAAIC,GAAE,eAAe,CAAAA,OAAGA,GAAE,WAAY,GAAE,IAAIC,GAAE,aAAa,CAAAD,OAAGA,GAAE,SAAU,GAAE,IAAIA,GAAE,aAAa,CAACA,IAAEC,OAAID,GAAE,YAAUC,GAAE,OAAO,UAAW,GAAE,IAAID,GAAE,eAAe,CAAAA,OAAGA,GAAE,WAAY,GAAE,IAAIC,GAAE,cAAc,CAAAD,OAAGA,GAAE,UAAW,GAAE,IAAIA,GAAE,cAAc,CAACA,IAAEC,OAAID,GAAE,aAAWC,GAAE,OAAO,UAAW,GAAE,IAAI,EAAE,YAAY,CAACD,IAAEC,OAAIA,GAAE,kBAAmB,GAAE,IAAI,EAAE,WAAW,CAACD,IAAEC,OAAIA,GAAE,OAAO,OAAQ,GAAE,IAAI,EAAE,cAAc,CAACD,IAAEC,OAAIA,GAAE,gBAAiB,CAAC,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3jC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,EAEb,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUb,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUb,GAAE,EAAE,0BAAwB,EAAE,SAAS,IAAI,IAAID,GAAE,4BAA4B,CAAAC,OAAG,EAAEA,GAAE,wBAAwB,IAAEA,GAAE,2BAAyB,CAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzJ,KAAG,EAAE,KAAK,IAAI;AAAA;AAAA,EAEd;AAAC;", "names": ["i", "o", "e"]}