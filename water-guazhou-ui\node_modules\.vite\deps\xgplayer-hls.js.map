{"version": 3, "sources": ["../../xgplayer-hls/node_modules/eventemitter3/index.js", "../../xgplayer-hls/es/_virtual/_rollupPluginBabelHelpers.js", "../../xgplayer-hls/es/hls/index.js", "../../xgplayer-hls/es/hls/buffer-service/decrypt/index.js", "../../xgplayer-hls/es/hls/constants.js", "../../xgplayer-hls/es/hls/buffer-service/transmuxer/index.js", "../../xgplayer-hls/es/hls/buffer-service/index.js", "../../xgplayer-hls/es/hls/config.js", "../../xgplayer-hls/es/hls/manifest-loader/parser/model.js", "../../xgplayer-hls/es/hls/manifest-loader/parser/utils.js", "../../xgplayer-hls/es/hls/manifest-loader/parser/master.js", "../../xgplayer-hls/es/hls/manifest-loader/parser/media.js", "../../xgplayer-hls/es/hls/manifest-loader/parser/index.js", "../../xgplayer-hls/es/hls/manifest-loader/index.js", "../../xgplayer-hls/es/hls/utils.js", "../../xgplayer-hls/es/hls/playlist/stream.js", "../../xgplayer-hls/es/hls/playlist/index.js", "../../xgplayer-hls/es/hls/segment-loader/index.js", "../../xgplayer-hls/es/plugin-extension.js", "../../xgplayer-hls/es/plugin.js"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "function _iterableToArrayLimit(arr, i) {\n  var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (null != _i) {\n    var _s, _e, _x, _r, _arr = [], _n = true, _d = false;\n    try {\n      if (_x = (_i = _i.call(arr)).next, 0 === i) {\n        if (Object(_i) !== _i)\n          return;\n        _n = false;\n      } else\n        for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = true)\n          ;\n    } catch (err) {\n      _d = true, _e = err;\n    } finally {\n      try {\n        if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r))\n          return;\n      } finally {\n        if (_d)\n          throw _e;\n      }\n    }\n    return _arr;\n  }\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function(sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), true).forEach(function(key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _regeneratorRuntime() {\n  _regeneratorRuntime = function() {\n    return exports;\n  };\n  var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function(obj, key, desc) {\n    obj[key] = desc.value;\n  }, $Symbol = \"function\" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || \"@@iterator\", asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\", toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n  function define(obj, key, value) {\n    return Object.defineProperty(obj, key, {\n      value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    }), obj[key];\n  }\n  try {\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []);\n    return defineProperty(generator, \"_invoke\", {\n      value: makeInvokeMethod(innerFn, self, context)\n    }), generator;\n  }\n  function tryCatch(fn, obj, arg) {\n    try {\n      return {\n        type: \"normal\",\n        arg: fn.call(obj, arg)\n      };\n    } catch (err) {\n      return {\n        type: \"throw\",\n        arg: err\n      };\n    }\n  }\n  exports.wrap = wrap;\n  var ContinueSentinel = {};\n  function Generator() {\n  }\n  function GeneratorFunction() {\n  }\n  function GeneratorFunctionPrototype() {\n  }\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function() {\n    return this;\n  });\n  var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype);\n  var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype);\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (\"throw\" !== record.type) {\n        var result = record.arg, value = result.value;\n        return value && \"object\" == typeof value && hasOwn.call(value, \"__await\") ? PromiseImpl.resolve(value.__await).then(function(value2) {\n          invoke(\"next\", value2, resolve, reject);\n        }, function(err) {\n          invoke(\"throw\", err, resolve, reject);\n        }) : PromiseImpl.resolve(value).then(function(unwrapped) {\n          result.value = unwrapped, resolve(result);\n        }, function(error) {\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n      reject(record.arg);\n    }\n    var previousPromise;\n    defineProperty(this, \"_invoke\", {\n      value: function(method, arg) {\n        function callInvokeWithMethodAndArg() {\n          return new PromiseImpl(function(resolve, reject) {\n            invoke(method, arg, resolve, reject);\n          });\n        }\n        return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n      }\n    });\n  }\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = \"suspendedStart\";\n    return function(method, arg) {\n      if (\"executing\" === state)\n        throw new Error(\"Generator is already running\");\n      if (\"completed\" === state) {\n        if (\"throw\" === method)\n          throw arg;\n        return doneResult();\n      }\n      for (context.method = method, context.arg = arg; ; ) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel)\n              continue;\n            return delegateResult;\n          }\n        }\n        if (\"next\" === context.method)\n          context.sent = context._sent = context.arg;\n        else if (\"throw\" === context.method) {\n          if (\"suspendedStart\" === state)\n            throw state = \"completed\", context.arg;\n          context.dispatchException(context.arg);\n        } else\n          \"return\" === context.method && context.abrupt(\"return\", context.arg);\n        state = \"executing\";\n        var record = tryCatch(innerFn, self, context);\n        if (\"normal\" === record.type) {\n          if (state = context.done ? \"completed\" : \"suspendedYield\", record.arg === ContinueSentinel)\n            continue;\n          return {\n            value: record.arg,\n            done: context.done\n          };\n        }\n        \"throw\" === record.type && (state = \"completed\", context.method = \"throw\", context.arg = record.arg);\n      }\n    };\n  }\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method, method = delegate.iterator[methodName];\n    if (void 0 === method)\n      return context.delegate = null, \"throw\" === methodName && delegate.iterator.return && (context.method = \"return\", context.arg = void 0, maybeInvokeDelegate(delegate, context), \"throw\" === context.method) || \"return\" !== methodName && (context.method = \"throw\", context.arg = new TypeError(\"The iterator does not provide a '\" + methodName + \"' method\")), ContinueSentinel;\n    var record = tryCatch(method, delegate.iterator, context.arg);\n    if (\"throw\" === record.type)\n      return context.method = \"throw\", context.arg = record.arg, context.delegate = null, ContinueSentinel;\n    var info = record.arg;\n    return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, \"return\" !== context.method && (context.method = \"next\", context.arg = void 0), context.delegate = null, ContinueSentinel) : info : (context.method = \"throw\", context.arg = new TypeError(\"iterator result is not an object\"), context.delegate = null, ContinueSentinel);\n  }\n  function pushTryEntry(locs) {\n    var entry = {\n      tryLoc: locs[0]\n    };\n    1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry);\n  }\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\", delete record.arg, entry.completion = record;\n  }\n  function Context(tryLocsList) {\n    this.tryEntries = [{\n      tryLoc: \"root\"\n    }], tryLocsList.forEach(pushTryEntry, this), this.reset(true);\n  }\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod)\n        return iteratorMethod.call(iterable);\n      if (\"function\" == typeof iterable.next)\n        return iterable;\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next2() {\n          for (; ++i < iterable.length; )\n            if (hasOwn.call(iterable, i))\n              return next2.value = iterable[i], next2.done = false, next2;\n          return next2.value = void 0, next2.done = true, next2;\n        };\n        return next.next = next;\n      }\n    }\n    return {\n      next: doneResult\n    };\n  }\n  function doneResult() {\n    return {\n      value: void 0,\n      done: true\n    };\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, \"constructor\", {\n    value: GeneratorFunctionPrototype,\n    configurable: true\n  }), defineProperty(GeneratorFunctionPrototype, \"constructor\", {\n    value: GeneratorFunction,\n    configurable: true\n  }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, \"GeneratorFunction\"), exports.isGeneratorFunction = function(genFun) {\n    var ctor = \"function\" == typeof genFun && genFun.constructor;\n    return !!ctor && (ctor === GeneratorFunction || \"GeneratorFunction\" === (ctor.displayName || ctor.name));\n  }, exports.mark = function(genFun) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, \"GeneratorFunction\")), genFun.prototype = Object.create(Gp), genFun;\n  }, exports.awrap = function(arg) {\n    return {\n      __await: arg\n    };\n  }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function() {\n    return this;\n  }), exports.AsyncIterator = AsyncIterator, exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    void 0 === PromiseImpl && (PromiseImpl = Promise);\n    var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl);\n    return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function(result) {\n      return result.done ? result.value : iter.next();\n    });\n  }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, \"Generator\"), define(Gp, iteratorSymbol, function() {\n    return this;\n  }), define(Gp, \"toString\", function() {\n    return \"[object Generator]\";\n  }), exports.keys = function(val) {\n    var object = Object(val), keys = [];\n    for (var key in object)\n      keys.push(key);\n    return keys.reverse(), function next() {\n      for (; keys.length; ) {\n        var key2 = keys.pop();\n        if (key2 in object)\n          return next.value = key2, next.done = false, next;\n      }\n      return next.done = true, next;\n    };\n  }, exports.values = values, Context.prototype = {\n    constructor: Context,\n    reset: function(skipTempReset) {\n      if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = false, this.delegate = null, this.method = \"next\", this.arg = void 0, this.tryEntries.forEach(resetTryEntry), !skipTempReset)\n        for (var name in this)\n          \"t\" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = void 0);\n    },\n    stop: function() {\n      this.done = true;\n      var rootRecord = this.tryEntries[0].completion;\n      if (\"throw\" === rootRecord.type)\n        throw rootRecord.arg;\n      return this.rval;\n    },\n    dispatchException: function(exception) {\n      if (this.done)\n        throw exception;\n      var context = this;\n      function handle(loc, caught) {\n        return record.type = \"throw\", record.arg = exception, context.next = loc, caught && (context.method = \"next\", context.arg = void 0), !!caught;\n      }\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i], record = entry.completion;\n        if (\"root\" === entry.tryLoc)\n          return handle(\"end\");\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\"), hasFinally = hasOwn.call(entry, \"finallyLoc\");\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc)\n              return handle(entry.catchLoc, true);\n            if (this.prev < entry.finallyLoc)\n              return handle(entry.finallyLoc);\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc)\n              return handle(entry.catchLoc, true);\n          } else {\n            if (!hasFinally)\n              throw new Error(\"try statement without catch or finally\");\n            if (this.prev < entry.finallyLoc)\n              return handle(entry.finallyLoc);\n          }\n        }\n      }\n    },\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev && hasOwn.call(entry, \"finallyLoc\") && this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n      finallyEntry && (\"break\" === type || \"continue\" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null);\n      var record = finallyEntry ? finallyEntry.completion : {};\n      return record.type = type, record.arg = arg, finallyEntry ? (this.method = \"next\", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record);\n    },\n    complete: function(record, afterLoc) {\n      if (\"throw\" === record.type)\n        throw record.arg;\n      return \"break\" === record.type || \"continue\" === record.type ? this.next = record.arg : \"return\" === record.type ? (this.rval = this.arg = record.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel;\n    },\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc)\n          return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel;\n      }\n    },\n    catch: function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (\"throw\" === record.type) {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n      throw new Error(\"illegal catch attempt\");\n    },\n    delegateYield: function(iterable, resultName, nextLoc) {\n      return this.delegate = {\n        iterator: values(iterable),\n        resultName,\n        nextLoc\n      }, \"next\" === this.method && (this.arg = void 0), ContinueSentinel;\n    }\n  }, exports;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(obj2) {\n    return typeof obj2;\n  } : function(obj2) {\n    return obj2 && \"function\" == typeof Symbol && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? \"symbol\" : typeof obj2;\n  }, _typeof(obj);\n}\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\nfunction _asyncToGenerator(fn) {\n  return function() {\n    var self = this, args = arguments;\n    return new Promise(function(resolve, reject) {\n      var gen = fn.apply(self, args);\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n      _next(void 0);\n    });\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor)\n      descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps)\n    _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps)\n    _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass)\n    _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf2(o2) {\n    return o2.__proto__ || Object.getPrototypeOf(o2);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf2(o2, p2) {\n    o2.__proto__ = p2;\n    return o2;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct)\n    return false;\n  if (Reflect.construct.sham)\n    return false;\n  if (typeof Proxy === \"function\")\n    return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {\n    }));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null)\n    return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0)\n      continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null)\n    return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0)\n        continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key))\n        continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived), result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr))\n    return _arrayLikeToArray(arr);\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr))\n    return arr;\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null)\n    return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o)\n    return;\n  if (typeof o === \"string\")\n    return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor)\n    n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\")\n    return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))\n    return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length)\n    len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++)\n    arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null)\n    return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== void 0) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\")\n      return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _arrayWithoutHoles as arrayWithoutHoles, _assertThisInitialized as assertThisInitialized, _asyncToGenerator as asyncToGenerator, _classCallCheck as classCallCheck, _createClass as createClass, _createSuper as createSuper, _defineProperty as defineProperty, _getPrototypeOf as getPrototypeOf, _inherits as inherits, _isNativeReflectConstruct as isNativeReflectConstruct, _iterableToArray as iterableToArray, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _nonIterableSpread as nonIterableSpread, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _possibleConstructorReturn as possibleConstructorReturn, _regeneratorRuntime as regeneratorRuntime, _setPrototypeOf as setPrototypeOf, _slicedToArray as slicedToArray, _toConsumableArray as toConsumableArray, _toPrimitive as toPrimitive, _toPropertyKey as toPropertyKey, _typeof as typeof, _unsupportedIterableToArray as unsupportedIterableToArray };\n", "import { defineProperty as _defineProperty, inherits as _inherits, createSuper as _createSuper, createClass as _createClass, classCallCheck as _classCallCheck, assertThisInitialized as _assertThisInitialized, asyncToGenerator as _asyncToGenerator, slicedToArray as _slicedToArray, regeneratorRuntime as _regeneratorRuntime, typeof as _typeof, toConsumableArray as _toConsumableArray } from \"../_virtual/_rollupPluginBabelHelpers.js\";\nimport EventEmitter from \"eventemitter3\";\nimport { Logger, Buffer, StreamingError, isMediaPlaying, SeiService, GapService, MediaStatsService, MSE, getVideoPlaybackQuality, ERR } from \"xgplayer-streaming-shared\";\nimport { Logger as Logger$1 } from \"xgplayer-transmuxer\";\nimport { BufferService } from \"./buffer-service/index.js\";\nimport { getConfig } from \"./config.js\";\nexport { getConfig } from \"./config.js\";\nimport { Event } from \"./constants.js\";\nimport { ManifestLoader } from \"./manifest-loader/index.js\";\nexport { ManifestLoader } from \"./manifest-loader/index.js\";\nimport { Playlist } from \"./playlist/index.js\";\nexport { Playlist } from \"./playlist/index.js\";\nimport { SegmentLoader } from \"./segment-loader/index.js\";\nexport { SegmentLoader } from \"./segment-loader/index.js\";\nimport { clamp } from \"./utils.js\";\nvar logger = new Logger(\"hls\");\nvar Hls = /* @__PURE__ */ function(_EventEmitter) {\n  _inherits(Hls2, _EventEmitter);\n  var _super = _createSuper(Hls2);\n  function Hls2(_cfg) {\n    var _this;\n    _classCallCheck(this, Hls2);\n    _this = _super.call(this);\n    _defineProperty(_assertThisInitialized(_this), \"version\", Hls2.version);\n    _defineProperty(_assertThisInitialized(_this), \"media\", null);\n    _defineProperty(_assertThisInitialized(_this), \"config\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_manifestLoader\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_segmentLoader\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_playlist\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_bufferService\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_gapService\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_seiService\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_stats\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_prevSegSn\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_prevSegCc\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_tickTimer\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_tickInterval\", 500);\n    _defineProperty(_assertThisInitialized(_this), \"_segmentProcessing\", false);\n    _defineProperty(_assertThisInitialized(_this), \"_reloadOnPlay\", false);\n    _defineProperty(_assertThisInitialized(_this), \"_switchUrlOpts\", null);\n    _defineProperty(_assertThisInitialized(_this), \"_isProcessQuotaExceeded\", false);\n    _defineProperty(_assertThisInitialized(_this), \"_loadSegment\", /* @__PURE__ */ _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee() {\n      var _this$_playlist, nextSegment, lastSegment, _assertThisInitialize, config, minFrameDuration, maxBufferThroughout, bInfo, bufferThroughout;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1)\n          switch (_context.prev = _context.next) {\n            case 0:\n              if (!(_this._segmentProcessing || !_this.media)) {\n                _context.next = 2;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 2:\n              _this$_playlist = _this._playlist, nextSegment = _this$_playlist.nextSegment, lastSegment = _this$_playlist.lastSegment;\n              _assertThisInitialize = _assertThisInitialized(_this), config = _assertThisInitialize.config;\n              minFrameDuration = 0.016;\n              maxBufferThroughout = Math.min(Math.max((lastSegment === null || lastSegment === void 0 ? void 0 : lastSegment.duration) - minFrameDuration / 2 || 0, minFrameDuration), 0.1);\n              if (nextSegment) {\n                _context.next = 8;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 8:\n              if (_this.isLive) {\n                _context.next = 18;\n                break;\n              }\n              bInfo = _this.bufferInfo();\n              if (_this.media.paused && !_this.media.currentTime) {\n                bInfo = _this.bufferInfo(bInfo.nextStart || 0.5);\n              }\n              bufferThroughout = Math.abs(bInfo.end - _this.media.duration) < maxBufferThroughout;\n              if (!(bInfo.remaining >= config.preloadTime || bufferThroughout)) {\n                _context.next = 15;\n                break;\n              }\n              _this._tryEos();\n              return _context.abrupt(\"return\");\n            case 15:\n              if (!(config.preferMMSStreaming && !_this._bufferService.msStreaming)) {\n                _context.next = 17;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 17:\n              if (!_this._urlSwitching && _this._prevSegSn !== nextSegment.sn - 1 && bInfo.end && Math.abs(nextSegment.start - bInfo.end) > 1) {\n                _this._playlist.setNextSegmentByIndex(_this._playlist.findSegmentIndexByTime(bInfo.end + 0.1));\n              }\n            case 18:\n              return _context.abrupt(\"return\", _this._loadSegmentDirect());\n            case 19:\n            case \"end\":\n              return _context.stop();\n          }\n      }, _callee);\n    })));\n    _defineProperty(_assertThisInitialized(_this), \"_onLoadeddata\", function() {\n      if (_this.isLive && !_this.config.mseLowLatency) {\n        if (_this.media.duration !== Infinity) {\n          _this._bufferService.updateDuration(Infinity).catch(function(e) {\n          });\n        }\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_onPlay\", /* @__PURE__ */ _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee2() {\n      return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n        while (1)\n          switch (_context2.prev = _context2.next) {\n            case 0:\n              if (!(_this.media.seeking && _this.media.currentTime === 0)) {\n                _context2.next = 3;\n                break;\n              }\n              logger.debug(\"replay currentTime 0, return\");\n              return _context2.abrupt(\"return\");\n            case 3:\n              clearTimeout(_this._disconnectTimer);\n              if (!_this._reloadOnPlay) {\n                _context2.next = 9;\n                break;\n              }\n              _this._reloadOnPlay = false;\n              _this.replay(true);\n              _context2.next = 12;\n              break;\n            case 9:\n              _context2.next = 11;\n              return _this._loadSegment();\n            case 11:\n              _this._startTick();\n            case 12:\n            case \"end\":\n              return _context2.stop();\n          }\n      }, _callee2);\n    })));\n    _defineProperty(_assertThisInitialized(_this), \"_onPause\", function() {\n      if (_this.isLive) {\n        if (!_this._reloadOnPlay) {\n          var disconnectTime = _this.config.disconnectTime;\n          if (disconnectTime === null || disconnectTime === void 0)\n            disconnectTime = _this._playlist.dvrWindow;\n          if (!Number.isFinite(disconnectTime))\n            return;\n          clearTimeout(_this._disconnectTimer);\n          _this._disconnectTimer = setTimeout(function() {\n            _this._reloadOnPlay = true;\n            _this._clear();\n          }, disconnectTime || 0);\n        }\n      } else {\n        _this._stopTick();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_onSeeking\", /* @__PURE__ */ _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee3() {\n      var seekTime, seekRange, newSeekTime, curSeg, info, segIndex, seg;\n      return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n        while (1)\n          switch (_context3.prev = _context3.next) {\n            case 0:\n              if (_this.media) {\n                _context3.next = 2;\n                break;\n              }\n              return _context3.abrupt(\"return\");\n            case 2:\n              _this._onCheckQuotaExceeded();\n              seekTime = _this.media.currentTime;\n              seekRange = _this._playlist.seekRange;\n              if (!seekRange) {\n                _context3.next = 10;\n                break;\n              }\n              newSeekTime = clamp(seekTime, seekRange[0], _this.isLive ? seekRange[1] : _this.media.duration);\n              if (!(newSeekTime >= 0 && Math.abs(seekTime - newSeekTime) >= 0.1)) {\n                _context3.next = 10;\n                break;\n              }\n              _this.media.currentTime = newSeekTime;\n              return _context3.abrupt(\"return\");\n            case 10:\n              curSeg = _this._playlist.currentSegment;\n              info = Buffer.info(Buffer.get(_this.media), seekTime, 0.1);\n              if (!curSeg) {\n                _context3.next = 17;\n                break;\n              }\n              if (!(info.end && Math.abs(info.end - curSeg.end) < 0.2)) {\n                _context3.next = 15;\n                break;\n              }\n              return _context3.abrupt(\"return\");\n            case 15:\n              if (!(_this.isLive && info.end)) {\n                _context3.next = 17;\n                break;\n              }\n              return _context3.abrupt(\"return\");\n            case 17:\n              segIndex = _this._playlist.findSegmentIndexByTime(seekTime);\n              seg = _this._playlist.getSegmentByIndex(segIndex);\n              if (!(segIndex === null || segIndex === void 0 || !seg || _this._segmentProcessing && seg === _this._playlist.nextSegment)) {\n                _context3.next = 21;\n                break;\n              }\n              return _context3.abrupt(\"return\");\n            case 21:\n              logger.debug(\"seek to\", seekTime, seg);\n              _this._playlist.setNextSegmentByIndex(segIndex);\n              _this._stopTick();\n              _context3.next = 26;\n              return _this._segmentLoader.cancel();\n            case 26:\n              _this._segmentProcessing = false;\n              if (!(!info.end || _this.isLive)) {\n                _context3.next = 30;\n                break;\n              }\n              _context3.next = 30;\n              return _this._loadSegmentDirect(true);\n            case 30:\n              _this._startTick();\n            case 31:\n            case \"end\":\n              return _context3.stop();\n          }\n      }, _callee3);\n    })));\n    _defineProperty(_assertThisInitialized(_this), \"_onTimeupdate\", function() {\n      if (!_this.media)\n        return;\n      var cfg = _this.config;\n      if (cfg.isLive && cfg.maxLatency && cfg.targetLatency && _this.media) {\n        var liveEdge = _this._playlist.liveEdge;\n        if (!liveEdge)\n          return;\n        var latency = liveEdge - _this.media.currentTime;\n        if (latency >= cfg.maxLatency) {\n          logger.debug(\"latency jump, currentTime:\".concat(_this.media.currentTime, \", liveEdge:\").concat(liveEdge, \",  latency=\").concat(latency));\n          _this.media.currentTime = liveEdge - cfg.targetLatency;\n        }\n      }\n      if (cfg.seiInTime) {\n        var _this$_seiService;\n        (_this$_seiService = _this._seiService) === null || _this$_seiService === void 0 ? void 0 : _this$_seiService.throw(_this.media.currentTime);\n      }\n      if (_this.config.allowedStreamTrackChange && !_this.config.softDecode) {\n        _this._checkStreamTrackChange(_this.media.currentTime);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_tick\", function() {\n      if (!_this.media)\n        return;\n      _this._startTick();\n      var media = _this.media;\n      var buffered = Buffer.get(media);\n      var segLoaderError = _this._segmentLoader.error;\n      _this._onCheckQuotaExceeded();\n      if (_this._isProcessQuotaExceeded) {\n        if (!_this._bufferService.isFull()) {\n          _this._isProcessQuotaExceeded = false;\n          _this._segmentProcessing = false;\n        }\n      }\n      if (segLoaderError) {\n        var bufferMaxHoleTolerance = 0.5;\n        if (!media.readyState || _this.bufferInfo(bufferMaxHoleTolerance).remaining < 1) {\n          segLoaderError.fatal = true;\n          _this._emitError(StreamingError.network(segLoaderError));\n        }\n        return;\n      }\n      if (Buffer.end(buffered) >= 0.1 && media.readyState) {\n        if (isMediaPlaying(media)) {\n          _this._loadSegment();\n          if (_this._gapService) {\n            _this._gapService.do(media, _this.config.maxJumpDistance, _this.isLive);\n          }\n        } else {\n          if (media.readyState < 2 && _this._gapService) {\n            _this._gapService.do(media, _this.config.maxJumpDistance, !media.currentTime ? true : _this.isLive);\n          }\n        }\n      }\n      if (!_this.isLive) {\n        _this._tryEos();\n      }\n    });\n    _this.config = _cfg = getConfig(_cfg);\n    _this.media = _this.config.media;\n    _this._manifestLoader = new ManifestLoader(_assertThisInitialized(_this));\n    _this._segmentLoader = new SegmentLoader(_assertThisInitialized(_this));\n    _this._playlist = new Playlist(_assertThisInitialized(_this));\n    _this._bufferService = new BufferService(_assertThisInitialized(_this));\n    if (_cfg.seiInTime) {\n      _this._seiService = new SeiService(_assertThisInitialized(_this));\n    }\n    if (!_cfg.softDecode)\n      _this._gapService = new GapService();\n    _this._stats = new MediaStatsService(_assertThisInitialized(_this), 9e4);\n    _this.media.addEventListener(\"loadeddata\", _this._onLoadeddata);\n    _this.media.addEventListener(\"play\", _this._onPlay);\n    _this.media.addEventListener(\"pause\", _this._onPause);\n    _this.media.addEventListener(\"seeking\", _this._onSeeking);\n    _this.media.addEventListener(\"timeupdate\", _this._onTimeupdate);\n    return _this;\n  }\n  _createClass(Hls2, [{\n    key: \"isLive\",\n    get: function get() {\n      return this._playlist.isLive;\n    }\n  }, {\n    key: \"streams\",\n    get: function get() {\n      return this._playlist.streams;\n    }\n  }, {\n    key: \"currentStream\",\n    get: function get() {\n      return this._playlist.currentStream;\n    }\n  }, {\n    key: \"hasSubtitle\",\n    get: function get() {\n      return this._playlist.hasSubtitle;\n    }\n  }, {\n    key: \"totalDuration\",\n    get: function get() {\n      return this._playlist.totalDuration;\n    }\n  }, {\n    key: \"baseDts\",\n    get: function get() {\n      var _this$_bufferService;\n      return (_this$_bufferService = this._bufferService) === null || _this$_bufferService === void 0 ? void 0 : _this$_bufferService.baseDts;\n    }\n  }, {\n    key: \"abrSwitchPoint\",\n    get: function get() {\n      var targetSeg = this._urlSwitching ? this._playlist.currentSegment : this._playlist.nextSegment;\n      return targetSeg ? targetSeg.start + targetSeg.duration / 2 : null;\n    }\n  }, {\n    key: \"speedInfo\",\n    value: function speedInfo() {\n      return this._segmentLoader.speedInfo();\n    }\n  }, {\n    key: \"bufferInfo\",\n    value: function bufferInfo() {\n      var _this$media;\n      var maxHole = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0.1;\n      return Buffer.info(Buffer.get(this.media), (_this$media = this.media) === null || _this$media === void 0 ? void 0 : _this$media.currentTime, maxHole);\n    }\n  }, {\n    key: \"getStats\",\n    value: function getStats() {\n      return this._stats.getStats();\n    }\n  }, {\n    key: \"playbackQuality\",\n    value: function playbackQuality() {\n      return getVideoPlaybackQuality(this.media);\n    }\n  }, {\n    key: \"load\",\n    value: function() {\n      var _load = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee4() {\n        var url, options, reuseMse, _args4 = arguments;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1)\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                url = _args4.length > 0 && _args4[0] !== void 0 ? _args4[0] : \"\";\n                options = _args4.length > 1 && _args4[1] !== void 0 ? _args4[1] : {};\n                reuseMse = typeof options === \"boolean\" ? options : !!(options !== null && options !== void 0 && options.reuseMse);\n                if (_typeof(options) === \"object\" && options !== null && options !== void 0 && options.clearSwitchStatus) {\n                  this._urlSwitching = false;\n                  this._switchUrlOpts = null;\n                  this.config.startTime = void 0;\n                }\n                if (url)\n                  this.config.url = url;\n                url = this.config.url;\n                _context4.next = 8;\n                return this._reset(reuseMse);\n              case 8:\n                _context4.next = 10;\n                return this._loadData(url);\n              case 10:\n                this._startTick();\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n        }, _callee4, this);\n      }));\n      function load() {\n        return _load.apply(this, arguments);\n      }\n      return load;\n    }()\n  }, {\n    key: \"_loadData\",\n    value: function() {\n      var _loadData2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee5(url) {\n        var manifest, currentStream, preIndex, _this$_switchUrlOpts, _this$_switchUrlOpts3, _this$_switchUrlOpts4, _this$_switchUrlOpts2, switchTimePoint, segIdx, nextSeg, bufferClearStartPoint, startTime, _this$_switchUrlOpts5;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1)\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                try {\n                  if (url)\n                    url = url.trim();\n                } catch (e) {\n                }\n                if (url) {\n                  _context5.next = 3;\n                  break;\n                }\n                throw this._emitError(new StreamingError(ERR.OTHER, ERR.SUB_TYPES.OPTION, null, null, \"m3u8 url is missing\"));\n              case 3:\n                _context5.next = 5;\n                return this._loadM3U8(url);\n              case 5:\n                manifest = _context5.sent;\n                currentStream = this._playlist.currentStream;\n                if (!this._urlSwitching) {\n                  _context5.next = 23;\n                  break;\n                }\n                if (!this.isLive) {\n                  _context5.next = 14;\n                  break;\n                }\n                preIndex = this._playlist.setNextSegmentBySN(this._prevSegSn);\n                logger.log(\"segment nb=\".concat(this._prevSegSn, \" index of \").concat(preIndex, \" in the new playlist\"));\n                if (preIndex === -1) {\n                  this._prevSegCc = null;\n                  this._prevSegSn = null;\n                }\n                _context5.next = 23;\n                break;\n              case 14:\n                if (currentStream.bitrate === 0 && (_this$_switchUrlOpts = this._switchUrlOpts) !== null && _this$_switchUrlOpts !== void 0 && _this$_switchUrlOpts.bitrate) {\n                  currentStream.bitrate = (_this$_switchUrlOpts2 = this._switchUrlOpts) === null || _this$_switchUrlOpts2 === void 0 ? void 0 : _this$_switchUrlOpts2.bitrate;\n                }\n                switchTimePoint = typeof ((_this$_switchUrlOpts3 = this._switchUrlOpts) === null || _this$_switchUrlOpts3 === void 0 ? void 0 : _this$_switchUrlOpts3.startTime) === \"number\" ? (_this$_switchUrlOpts4 = this._switchUrlOpts) === null || _this$_switchUrlOpts4 === void 0 ? void 0 : _this$_switchUrlOpts4.startTime : this._getSeamlessSwitchPoint();\n                this.config.startTime = switchTimePoint;\n                segIdx = this._playlist.findSegmentIndexByTime(switchTimePoint);\n                nextSeg = this._playlist.getSegmentByIndex(segIdx + 1);\n                if (!nextSeg) {\n                  _context5.next = 23;\n                  break;\n                }\n                bufferClearStartPoint = nextSeg.start;\n                _context5.next = 23;\n                return this._bufferService.removeBuffer(bufferClearStartPoint);\n              case 23:\n                if (manifest) {\n                  _context5.next = 25;\n                  break;\n                }\n                return _context5.abrupt(\"return\");\n              case 25:\n                if (!this.isLive) {\n                  _context5.next = 36;\n                  break;\n                }\n                this._bufferService.setLiveSeekableRange(0, 4294967295);\n                logger.log(\"totalDuration first time got:\", this._playlist.totalDuration);\n                logger.log(\"nb segments got:\", this._playlist.nbSegments);\n                if (this.config.targetLatency < this._playlist.totalDuration) {\n                  this.config.targetLatency = this._playlist.totalDuration;\n                  this.config.maxLatency = 1.5 * this.config.targetLatency;\n                }\n                if (!manifest.isMaster)\n                  this._pollM3U8(url);\n                if (!(this._playlist.nbSegments < this.config.minSegmentsStartPlay)) {\n                  _context5.next = 33;\n                  break;\n                }\n                return _context5.abrupt(\"return\");\n              case 33:\n                _context5.next = 35;\n                return this._loadSegment();\n              case 35:\n                return _context5.abrupt(\"return\");\n              case 36:\n                _context5.next = 38;\n                return this._bufferService.updateDuration(currentStream.totalDuration);\n              case 38:\n                startTime = this.config.startTime;\n                if (startTime) {\n                  if (!((_this$_switchUrlOpts5 = this._switchUrlOpts) !== null && _this$_switchUrlOpts5 !== void 0 && _this$_switchUrlOpts5.seamless)) {\n                    this.media.currentTime = startTime;\n                  }\n                  this._playlist.setNextSegmentByIndex(this._playlist.findSegmentIndexByTime(startTime) || 0);\n                }\n                _context5.next = 42;\n                return this._loadSegment();\n              case 42:\n              case \"end\":\n                return _context5.stop();\n            }\n        }, _callee5, this);\n      }));\n      function _loadData(_x) {\n        return _loadData2.apply(this, arguments);\n      }\n      return _loadData;\n    }()\n  }, {\n    key: \"replay\",\n    value: function() {\n      var _replay = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee6(isPlayEmit) {\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1)\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                this.config.startTime = 0;\n                this._urlSwitching = false;\n                this._switchUrlOpts = null;\n                _context6.next = 5;\n                return this.load();\n              case 5:\n                this._reloadOnPlay = false;\n                return _context6.abrupt(\"return\", this.media.play(!isPlayEmit));\n              case 7:\n              case \"end\":\n                return _context6.stop();\n            }\n        }, _callee6, this);\n      }));\n      function replay(_x2) {\n        return _replay.apply(this, arguments);\n      }\n      return replay;\n    }()\n  }, {\n    key: \"switchURL\",\n    value: function() {\n      var _switchURL = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee7(url) {\n        var options, defaultOpts, key, _options, seamless, startTime, appended, _args7 = arguments;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1)\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                options = _args7.length > 1 && _args7[1] !== void 0 ? _args7[1] : {};\n                defaultOpts = {\n                  seamless: false,\n                  startTime: 0,\n                  bitrate: 0\n                };\n                _context7.t0 = _typeof(options);\n                _context7.next = _context7.t0 === \"number\" ? 5 : _context7.t0 === \"boolean\" ? 7 : _context7.t0 === \"object\" ? 9 : 11;\n                break;\n              case 5:\n                options = {\n                  startTime: options\n                };\n                return _context7.abrupt(\"break\", 12);\n              case 7:\n                options = {\n                  seamless: options\n                };\n                return _context7.abrupt(\"break\", 12);\n              case 9:\n                for (key in options) {\n                  if (options[key] === void 0 || options[key] === null) {\n                    delete options[key];\n                  }\n                }\n                return _context7.abrupt(\"break\", 12);\n              case 11:\n                throw \"unsupported switchURL args: \".concat(options);\n              case 12:\n                options = Object.assign({}, defaultOpts, options);\n                _options = options, seamless = _options.seamless, startTime = _options.startTime;\n                this.config.url = url;\n                this.config.startTime = startTime;\n                this._switchUrlOpts = options;\n                if (seamless) {\n                  _context7.next = 38;\n                  break;\n                }\n                _context7.prev = 18;\n                if (!this.config.softDecode) {\n                  _context7.next = 23;\n                  break;\n                }\n                _context7.t1 = this.load(url);\n                _context7.next = 26;\n                break;\n              case 23:\n                _context7.next = 25;\n                return this.load(url);\n              case 25:\n                _context7.t1 = _context7.sent;\n              case 26:\n                appended = _context7.t1;\n                _context7.next = 33;\n                break;\n              case 29:\n                _context7.prev = 29;\n                _context7.t2 = _context7[\"catch\"](18);\n                this.emit(Event.SWITCH_URL_FAILED, _context7.t2);\n                throw _context7.t2;\n              case 33:\n                this._reloadOnPlay = false;\n                if (appended) {\n                  this.emit(Event.SWITCH_URL_SUCCESS, {\n                    url\n                  });\n                }\n                return _context7.abrupt(\"return\", this.media.play(true));\n              case 38:\n                this._urlSwitching = true;\n                if (!this.isLive) {\n                  this._prevSegSn = null;\n                  this._prevSegCc = null;\n                }\n                this._playlist.reset();\n                this._bufferService.seamlessSwitch();\n                _context7.next = 44;\n                return this._clear();\n              case 44:\n                _context7.next = 46;\n                return this._loadData(url);\n              case 46:\n                this._startTick();\n              case 47:\n                this._switchUrlOpts = null;\n              case 48:\n              case \"end\":\n                return _context7.stop();\n            }\n        }, _callee7, this, [[18, 29]]);\n      }));\n      function switchURL(_x3) {\n        return _switchURL.apply(this, arguments);\n      }\n      return switchURL;\n    }()\n  }, {\n    key: \"switchStream\",\n    value: function() {\n      var _switchStream = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee8(id) {\n        var force, curStream, streams, toSwitch, curId, _args8 = arguments;\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1)\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                force = _args8.length > 1 && _args8[1] !== void 0 ? _args8[1] : true;\n                curStream = this.currentStream;\n                streams = this.streams;\n                if (!(!curStream || curStream.id === id || !streams || streams.length < 2)) {\n                  _context8.next = 5;\n                  break;\n                }\n                return _context8.abrupt(\"return\");\n              case 5:\n                toSwitch = streams.find(function(x) {\n                  return x.id === id;\n                });\n                if (toSwitch) {\n                  _context8.next = 8;\n                  break;\n                }\n                return _context8.abrupt(\"return\");\n              case 8:\n                _context8.prev = 8;\n                _context8.next = 11;\n                return this._clear();\n              case 11:\n                if (!force) {\n                  _context8.next = 14;\n                  break;\n                }\n                _context8.next = 14;\n                return this._bufferService.clearAllBuffer();\n              case 14:\n                _context8.next = 19;\n                break;\n              case 16:\n                _context8.prev = 16;\n                _context8.t0 = _context8[\"catch\"](8);\n                throw this._emitError(StreamingError.create(_context8.t0));\n              case 19:\n                if (curStream.currentAudioStream && toSwitch.audioStreams.length > 2) {\n                  curId = curStream.currentAudioStream.id;\n                  toSwitch.currentAudioStream = toSwitch.audioStreams.find(function(x) {\n                    return x.id === curId;\n                  }) || toSwitch.currentAudioStream;\n                }\n                this._playlist.currentStream = toSwitch;\n                _context8.prev = 21;\n                if (!(this.isLive || !toSwitch.segments.length)) {\n                  _context8.next = 25;\n                  break;\n                }\n                _context8.next = 25;\n                return this._refreshM3U8();\n              case 25:\n                this._playlist.setNextSegmentByIndex(this._playlist.findSegmentIndexByTime(this.media.currentTime) || 0);\n                this._prevSegCc = null;\n                _context8.next = 29;\n                return this._loadSegmentDirect();\n              case 29:\n                _context8.next = 35;\n                break;\n              case 31:\n                _context8.prev = 31;\n                _context8.t1 = _context8[\"catch\"](21);\n                this._playlist.currentStream = curStream;\n                throw _context8.t1;\n              case 35:\n                this._startTick();\n                return _context8.abrupt(\"return\", toSwitch);\n              case 37:\n              case \"end\":\n                return _context8.stop();\n            }\n        }, _callee8, this, [[8, 16], [21, 31]]);\n      }));\n      function switchStream(_x4) {\n        return _switchStream.apply(this, arguments);\n      }\n      return switchStream;\n    }()\n  }, {\n    key: \"switchAudioStream\",\n    value: function() {\n      var _switchAudioStream = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee9(id) {\n        var force, curStream, audioStream, toSwitch, _args9 = arguments;\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1)\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                force = _args9.length > 1 && _args9[1] !== void 0 ? _args9[1] : true;\n                curStream = this.currentStream;\n                if (curStream) {\n                  _context9.next = 4;\n                  break;\n                }\n                return _context9.abrupt(\"return\");\n              case 4:\n                audioStream = curStream.currentAudioStream;\n                if (!(!audioStream || audioStream.id === id || curStream.audioStreams.length < 2)) {\n                  _context9.next = 7;\n                  break;\n                }\n                return _context9.abrupt(\"return\");\n              case 7:\n                toSwitch = curStream.audioStreams.find(function(x) {\n                  return x.id === id;\n                });\n                if (toSwitch) {\n                  _context9.next = 10;\n                  break;\n                }\n                return _context9.abrupt(\"return\");\n              case 10:\n                _context9.prev = 10;\n                _context9.next = 13;\n                return this._clear();\n              case 13:\n                if (!force) {\n                  _context9.next = 16;\n                  break;\n                }\n                _context9.next = 16;\n                return this._bufferService.clearAllBuffer();\n              case 16:\n                _context9.next = 21;\n                break;\n              case 18:\n                _context9.prev = 18;\n                _context9.t0 = _context9[\"catch\"](10);\n                throw this._emitError(StreamingError.create(_context9.t0));\n              case 21:\n                curStream.currentAudioStream = toSwitch;\n                _context9.prev = 22;\n                if (!(this.isLive || !toSwitch.segments.length)) {\n                  _context9.next = 26;\n                  break;\n                }\n                _context9.next = 26;\n                return this._refreshM3U8();\n              case 26:\n                this._playlist.setNextSegmentByIndex(this._playlist.findSegmentIndexByTime(this.media.currentTime) || 0);\n                this._prevSegCc = null;\n                _context9.next = 30;\n                return this._loadSegmentDirect();\n              case 30:\n                _context9.next = 36;\n                break;\n              case 32:\n                _context9.prev = 32;\n                _context9.t1 = _context9[\"catch\"](22);\n                curStream.currentAudioStream = audioStream;\n                throw _context9.t1;\n              case 36:\n                this._startTick();\n                return _context9.abrupt(\"return\", toSwitch);\n              case 38:\n              case \"end\":\n                return _context9.stop();\n            }\n        }, _callee9, this, [[10, 18], [22, 32]]);\n      }));\n      function switchAudioStream(_x5) {\n        return _switchAudioStream.apply(this, arguments);\n      }\n      return switchAudioStream;\n    }()\n  }, {\n    key: \"switchSubtitleStream\",\n    value: function() {\n      var _switchSubtitleStream = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee10(lang) {\n        return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n          while (1)\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                this._playlist.switchSubtitle(lang);\n                _context10.next = 3;\n                return this._manifestLoader.stopPoll();\n              case 3:\n                _context10.next = 5;\n                return this._refreshM3U8();\n              case 5:\n              case \"end\":\n                return _context10.stop();\n            }\n        }, _callee10, this);\n      }));\n      function switchSubtitleStream(_x6) {\n        return _switchSubtitleStream.apply(this, arguments);\n      }\n      return switchSubtitleStream;\n    }()\n  }, {\n    key: \"detachMedia\",\n    value: function() {\n      var _detachMedia = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee11() {\n        return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n          while (1)\n            switch (_context11.prev = _context11.next) {\n              case 0:\n                if (!this._bufferService) {\n                  _context11.next = 3;\n                  break;\n                }\n                _context11.next = 3;\n                return this._bufferService.detachMedia();\n              case 3:\n              case \"end\":\n                return _context11.stop();\n            }\n        }, _callee11, this);\n      }));\n      function detachMedia() {\n        return _detachMedia.apply(this, arguments);\n      }\n      return detachMedia;\n    }()\n  }, {\n    key: \"destroy\",\n    value: function() {\n      var _destroy = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee12() {\n        var _this$_seiService2;\n        return _regeneratorRuntime().wrap(function _callee12$(_context12) {\n          while (1)\n            switch (_context12.prev = _context12.next) {\n              case 0:\n                if (this.media) {\n                  _context12.next = 2;\n                  break;\n                }\n                return _context12.abrupt(\"return\");\n              case 2:\n                this.removeAllListeners();\n                this._playlist.reset();\n                this._segmentLoader.reset();\n                (_this$_seiService2 = this._seiService) === null || _this$_seiService2 === void 0 ? void 0 : _this$_seiService2.reset();\n                this.media.removeEventListener(\"loadeddata\", this._onLoadeddata);\n                this.media.removeEventListener(\"play\", this._onPlay);\n                this.media.removeEventListener(\"pause\", this._onPause);\n                this.media.removeEventListener(\"seeking\", this._onSeeking);\n                this.media.removeEventListener(\"timeupdate\", this._onTimeupdate);\n                _context12.next = 13;\n                return Promise.all([this._clear(), this._bufferService.destroy()]);\n              case 13:\n                this.media = null;\n              case 14:\n              case \"end\":\n                return _context12.stop();\n            }\n        }, _callee12, this);\n      }));\n      function destroy() {\n        return _destroy.apply(this, arguments);\n      }\n      return destroy;\n    }()\n  }, {\n    key: \"_loadM3U8\",\n    value: function() {\n      var _loadM3U = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee13(url) {\n        var playlist, _this$config$manifest, _this$config$manifest2, manifest, _ref4, _ref5, _this$_playlist$curre;\n        return _regeneratorRuntime().wrap(function _callee13$(_context13) {\n          while (1)\n            switch (_context13.prev = _context13.next) {\n              case 0:\n                _context13.prev = 0;\n                manifest = (_this$config$manifest = this.config.manifestList) === null || _this$config$manifest === void 0 ? void 0 : (_this$config$manifest2 = _this$config$manifest.filter(function(x) {\n                  return x.url === url;\n                })[0]) === null || _this$config$manifest2 === void 0 ? void 0 : _this$config$manifest2.manifest;\n                if (!manifest) {\n                  _context13.next = 6;\n                  break;\n                }\n                _context13.t0 = this._manifestLoader.parseText(manifest, url);\n                _context13.next = 9;\n                break;\n              case 6:\n                _context13.next = 8;\n                return this._manifestLoader.load(url);\n              case 8:\n                _context13.t0 = _context13.sent;\n              case 9:\n                _ref4 = _context13.t0;\n                _ref5 = _slicedToArray(_ref4, 1);\n                playlist = _ref5[0];\n                _context13.next = 17;\n                break;\n              case 14:\n                _context13.prev = 14;\n                _context13.t1 = _context13[\"catch\"](0);\n                throw this._emitError(StreamingError.create(_context13.t1));\n              case 17:\n                if (playlist) {\n                  _context13.next = 19;\n                  break;\n                }\n                return _context13.abrupt(\"return\");\n              case 19:\n                this._playlist.upsertPlaylist(playlist);\n                if (!playlist.isMaster) {\n                  _context13.next = 24;\n                  break;\n                }\n                if ((_this$_playlist$curre = this._playlist.currentStream.subtitleStreams) !== null && _this$_playlist$curre !== void 0 && _this$_playlist$curre.length) {\n                  this.emit(Event.SUBTITLE_PLAYLIST, {\n                    list: this._playlist.currentStream.subtitleStreams\n                  });\n                }\n                _context13.next = 24;\n                return this._refreshM3U8();\n              case 24:\n                this.emit(Event.STREAM_PARSED);\n                return _context13.abrupt(\"return\", playlist);\n              case 26:\n              case \"end\":\n                return _context13.stop();\n            }\n        }, _callee13, this, [[0, 14]]);\n      }));\n      function _loadM3U8(_x7) {\n        return _loadM3U.apply(this, arguments);\n      }\n      return _loadM3U8;\n    }()\n  }, {\n    key: \"_refreshM3U8\",\n    value: function _refreshM3U8() {\n      var _stream$currentAudioS, _stream$currentSubtit, _this2 = this;\n      var stream = this._playlist.currentStream;\n      if (!stream || !stream.url)\n        throw this._emitError(StreamingError.create(null, null, new Error(\"m3u8 url is not defined\")));\n      var url = stream.url;\n      var audioUrl = (_stream$currentAudioS = stream.currentAudioStream) === null || _stream$currentAudioS === void 0 ? void 0 : _stream$currentAudioS.url;\n      var subtitleUrl = (_stream$currentSubtit = stream.currentSubtitleStream) === null || _stream$currentSubtit === void 0 ? void 0 : _stream$currentSubtit.url;\n      return this._manifestLoader.load(url, audioUrl, subtitleUrl).then(function(_ref6) {\n        var _ref7 = _slicedToArray(_ref6, 3), mediaPlaylist = _ref7[0], audioPlaylist = _ref7[1], subtitlePlaylist = _ref7[2];\n        if (!mediaPlaylist)\n          return;\n        _this2._playlist.upsertPlaylist(mediaPlaylist, audioPlaylist, subtitlePlaylist);\n        if (!_this2.isLive)\n          return;\n        _this2._pollM3U8(url, audioUrl, subtitleUrl);\n      }).catch(function(err) {\n        throw _this2._emitError(StreamingError.create(err));\n      });\n    }\n  }, {\n    key: \"_pollM3U8\",\n    value: function _pollM3U8(url, audioUrl, subtitleUrl) {\n      var _this3 = this;\n      var isEmpty = this._playlist.isEmpty;\n      var pollInterval;\n      if (this._playlist.lowLatency) {\n        pollInterval = (this._playlist.currentStream.partTargetDuration || 0) * 1e3;\n      } else {\n        var _this$_playlist$lastS;\n        pollInterval = (((_this$_playlist$lastS = this._playlist.lastSegment) === null || _this$_playlist$lastS === void 0 ? void 0 : _this$_playlist$lastS.duration) || 0) * 1e3;\n      }\n      this._manifestLoader.poll(\n        url,\n        audioUrl,\n        subtitleUrl,\n        function(p1, p2, p3) {\n          _this3._playlist.upsertPlaylist(p1, p2, p3);\n          _this3._playlist.clearOldSegment();\n          var switchToNoEmpty = p1 && isEmpty && !_this3._playlist.isEmpty;\n          if (switchToNoEmpty || !_this3._playlist.hadSegmentLoaded && _this3._playlist.nbSegments >= _this3.config.minSegmentsStartPlay) {\n            _this3._loadSegment();\n          }\n          if (isEmpty)\n            isEmpty = _this3._playlist.isEmpty;\n        },\n        function(err) {\n          _this3._emitError(StreamingError.create(err));\n        },\n        pollInterval\n      );\n    }\n  }, {\n    key: \"_loadSegmentDirect\",\n    value: function() {\n      var _loadSegmentDirect2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee14(loadOnce) {\n        var seg, appended, cachedError, _this$_playlist$curre2, bufferEnd, sameStream;\n        return _regeneratorRuntime().wrap(function _callee14$(_context14) {\n          while (1)\n            switch (_context14.prev = _context14.next) {\n              case 0:\n                seg = this._playlist.nextSegment;\n                if (seg) {\n                  _context14.next = 3;\n                  break;\n                }\n                return _context14.abrupt(\"return\");\n              case 3:\n                appended = false;\n                cachedError = null;\n                _context14.prev = 5;\n                this._segmentProcessing = true;\n                logger.log(\"load segment, sn:\".concat(seg.sn, \", [\").concat(seg.start, \", \").concat(seg.end, \"], partIndex:\").concat(seg.partIndex));\n                _context14.next = 10;\n                return this._reqAndBufferSegment(seg, this._playlist.getAudioSegment(seg));\n              case 10:\n                appended = _context14.sent;\n                _context14.next = 16;\n                break;\n              case 13:\n                _context14.prev = 13;\n                _context14.t0 = _context14[\"catch\"](5);\n                cachedError = _context14.t0;\n              case 16:\n                _context14.prev = 16;\n                this._segmentProcessing = false;\n                return _context14.finish(16);\n              case 19:\n                if (!cachedError) {\n                  _context14.next = 26;\n                  break;\n                }\n                if (!this._bufferService.isFull()) {\n                  _context14.next = 25;\n                  break;\n                }\n                logger.log(\"load segment, sn:\".concat(seg.sn, \", partIndex:\").concat(seg.partIndex));\n                this._segmentProcessing = true;\n                this._isProcessQuotaExceeded = true;\n                return _context14.abrupt(\"return\", false);\n              case 25:\n                return _context14.abrupt(\"return\", this._emitError(StreamingError.create(cachedError)));\n              case 26:\n                if (appended) {\n                  bufferEnd = this.bufferInfo().end;\n                  if (this.isLive && !this.media.seeking && bufferEnd && Math.abs(seg.end - bufferEnd) > 1) {\n                    logger.warn(\"segment: \".concat(seg.sn, \" expected end=\").concat(seg.end, \", real end=\").concat(bufferEnd));\n                    this._playlist.feedbackLiveEdge(seg, bufferEnd);\n                  }\n                  sameStream = ((_this$_playlist$curre2 = this._playlist.currentStream) === null || _this$_playlist$curre2 === void 0 ? void 0 : _this$_playlist$curre2.url) === seg.parentUrl;\n                  if (this._urlSwitching && !sameStream) {\n                    logger.warn(\"pre playlist segment appended!\");\n                    this._bufferService.seamlessSwitch();\n                  }\n                  if (this.isLive && this._urlSwitching && sameStream) {\n                    this._urlSwitching = false;\n                    this.emit(Event.SWITCH_URL_SUCCESS, {\n                      url: this.config.url\n                    });\n                  }\n                  this._playlist.moveSegmentPointer();\n                  if (seg.isLast) {\n                    this._end();\n                  } else if (!loadOnce) {\n                    this._loadSegment();\n                  }\n                }\n                return _context14.abrupt(\"return\", appended);\n              case 28:\n              case \"end\":\n                return _context14.stop();\n            }\n        }, _callee14, this, [[5, 13, 16, 19]]);\n      }));\n      function _loadSegmentDirect(_x8) {\n        return _loadSegmentDirect2.apply(this, arguments);\n      }\n      return _loadSegmentDirect;\n    }()\n  }, {\n    key: \"_reqAndBufferSegment\",\n    value: function() {\n      var _reqAndBufferSegment2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee15(seg, audioSeg) {\n        var _this$_bufferService2;\n        var cc, discontinuity, responses, data, sn, start, stream, before, contiguous, segStart;\n        return _regeneratorRuntime().wrap(function _callee15$(_context15) {\n          while (1)\n            switch (_context15.prev = _context15.next) {\n              case 0:\n                cc = seg ? seg.cc : audioSeg.cc;\n                discontinuity = this._prevSegCc !== cc;\n                responses = [];\n                _context15.prev = 3;\n                _context15.next = 6;\n                return this._segmentLoader.load(seg, audioSeg, discontinuity);\n              case 6:\n                responses = _context15.sent;\n                _context15.next = 14;\n                break;\n              case 9:\n                _context15.prev = 9;\n                _context15.t0 = _context15[\"catch\"](3);\n                _context15.t0.fatal = false;\n                this._segmentLoader.error = _context15.t0;\n                throw _context15.t0;\n              case 14:\n                if (responses[0]) {\n                  _context15.next = 16;\n                  break;\n                }\n                return _context15.abrupt(\"return\");\n              case 16:\n                _context15.next = 18;\n                return (_this$_bufferService2 = this._bufferService).decryptBuffer.apply(_this$_bufferService2, _toConsumableArray(responses));\n              case 18:\n                data = _context15.sent;\n                if (data) {\n                  _context15.next = 21;\n                  break;\n                }\n                return _context15.abrupt(\"return\");\n              case 21:\n                sn = seg ? seg.sn : audioSeg.sn;\n                start = seg ? seg.start : audioSeg.start;\n                stream = this._playlist.currentStream;\n                this._bufferService.createSource(data[0], data[1], stream === null || stream === void 0 ? void 0 : stream.videoCodec, stream === null || stream === void 0 ? void 0 : stream.audioCodec);\n                before = Date.now();\n                contiguous = this._prevSegSn === sn - 1;\n                if (this.isLive && this._urlSwitching) {\n                  segStart = this.bufferInfo().end;\n                  this._playlist.updateSegmentsRanges(sn, segStart);\n                  logger.warn(\"update the new playlist liveEdge, segment id=\".concat(sn, \", buffer start=\").concat(segStart, \", liveEdge=\").concat(this._playlist.liveEdge));\n                  start = segStart;\n                }\n                _context15.next = 30;\n                return this._bufferService.appendBuffer(seg, audioSeg, data[0], data[1], discontinuity, contiguous, start);\n              case 30:\n                this.emit(Event.APPEND_COST, {\n                  elapsed: Date.now() - before,\n                  url: seg.url\n                });\n                _context15.next = 33;\n                return this._bufferService.evictBuffer(this.config.bufferBehind);\n              case 33:\n                this._prevSegCc = cc;\n                this._prevSegSn = sn;\n                return _context15.abrupt(\"return\", true);\n              case 36:\n              case \"end\":\n                return _context15.stop();\n            }\n        }, _callee15, this, [[3, 9]]);\n      }));\n      function _reqAndBufferSegment(_x9, _x10) {\n        return _reqAndBufferSegment2.apply(this, arguments);\n      }\n      return _reqAndBufferSegment;\n    }()\n  }, {\n    key: \"_onCheckQuotaExceeded\",\n    value: function() {\n      var _onCheckQuotaExceeded2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee16() {\n        var seekTime, buffered, inBuffered, i, bufferBehind, mediaTime;\n        return _regeneratorRuntime().wrap(function _callee16$(_context16) {\n          while (1)\n            switch (_context16.prev = _context16.next) {\n              case 0:\n                seekTime = this.media.currentTime;\n                buffered = this.media.buffered;\n                inBuffered = false;\n                i = 0;\n              case 4:\n                if (!(i < buffered.length)) {\n                  _context16.next = 11;\n                  break;\n                }\n                if (!(buffered.start(0) >= seekTime && seekTime < buffered.end(i))) {\n                  _context16.next = 8;\n                  break;\n                }\n                inBuffered = true;\n                return _context16.abrupt(\"break\", 11);\n              case 8:\n                i++;\n                _context16.next = 4;\n                break;\n              case 11:\n                if (!this._bufferService.isFull()) {\n                  _context16.next = 17;\n                  break;\n                }\n                bufferBehind = inBuffered ? this.config.bufferBehind : 5;\n                mediaTime = this.media.currentTime;\n                if (!(mediaTime - bufferBehind > 0)) {\n                  _context16.next = 17;\n                  break;\n                }\n                _context16.next = 17;\n                return this._bufferService.removeBuffer(0, mediaTime - bufferBehind);\n              case 17:\n              case \"end\":\n                return _context16.stop();\n            }\n        }, _callee16, this);\n      }));\n      function _onCheckQuotaExceeded() {\n        return _onCheckQuotaExceeded2.apply(this, arguments);\n      }\n      return _onCheckQuotaExceeded;\n    }()\n  }, {\n    key: \"_checkStreamTrackChange\",\n    value: function _checkStreamTrackChange(time) {\n      var changedSeg = this._playlist.checkSegmentTrackChange(time, this._bufferService.nbSb);\n      if (!changedSeg)\n        return;\n      this.switchURL(this.config.url, changedSeg.start + 0.2);\n    }\n  }, {\n    key: \"_clear\",\n    value: function() {\n      var _clear2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee17() {\n        return _regeneratorRuntime().wrap(function _callee17$(_context17) {\n          while (1)\n            switch (_context17.prev = _context17.next) {\n              case 0:\n                clearTimeout(this._disconnectTimer);\n                this._stopTick();\n                _context17.next = 4;\n                return Promise.all([this._segmentLoader.cancel(), this._manifestLoader.stopPoll()]);\n              case 4:\n                this._segmentProcessing = false;\n              case 5:\n              case \"end\":\n                return _context17.stop();\n            }\n        }, _callee17, this);\n      }));\n      function _clear() {\n        return _clear2.apply(this, arguments);\n      }\n      return _clear;\n    }()\n  }, {\n    key: \"_reset\",\n    value: function() {\n      var _reset2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee18() {\n        var _this$_seiService3;\n        var reuseMse, _args18 = arguments;\n        return _regeneratorRuntime().wrap(function _callee18$(_context18) {\n          while (1)\n            switch (_context18.prev = _context18.next) {\n              case 0:\n                reuseMse = _args18.length > 0 && _args18[0] !== void 0 ? _args18[0] : false;\n                this._reloadOnPlay = false;\n                this._prevSegSn = null;\n                this._prevSegCc = null;\n                this._switchUrlOpts = null;\n                this._playlist.reset();\n                this._segmentLoader.reset();\n                (_this$_seiService3 = this._seiService) === null || _this$_seiService3 === void 0 ? void 0 : _this$_seiService3.reset();\n                this._stats.reset();\n                _context18.next = 11;\n                return this._clear();\n              case 11:\n                return _context18.abrupt(\"return\", this._bufferService.reset(reuseMse));\n              case 12:\n              case \"end\":\n                return _context18.stop();\n            }\n        }, _callee18, this);\n      }));\n      function _reset() {\n        return _reset2.apply(this, arguments);\n      }\n      return _reset;\n    }()\n  }, {\n    key: \"_end\",\n    value: function _end() {\n      this._clear();\n      this._bufferService.endOfStream();\n      if (this.media.readyState <= 2 || this.media.buffered.length > 1) {\n        this._startTick();\n      }\n    }\n  }, {\n    key: \"_stopTick\",\n    value: function _stopTick() {\n      if (this._tickTimer) {\n        clearTimeout(this._tickTimer);\n      }\n      this._tickTimer = null;\n    }\n  }, {\n    key: \"_startTick\",\n    value: function _startTick() {\n      this._stopTick();\n      this._tickTimer = setTimeout(this._tick, this._tickInterval);\n    }\n  }, {\n    key: \"_emitError\",\n    value: function _emitError(error) {\n      var _error$originError;\n      var endOfStream = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n      if (((_error$originError = error.originError) === null || _error$originError === void 0 ? void 0 : _error$originError.fatal) === false) {\n        logger.warn(error);\n      } else {\n        var _this$media2, _this$media3, _this$_seiService4;\n        logger.table(error);\n        logger.error(error);\n        logger.error((_this$media2 = this.media) === null || _this$media2 === void 0 ? void 0 : _this$media2.error);\n        if ((_this$media3 = this.media) !== null && _this$media3 !== void 0 && _this$media3.readyState) {\n          this.media.pause();\n        }\n        this._stopTick();\n        if (this._urlSwitching) {\n          this._urlSwitching = false;\n          this.emit(Event.SWITCH_URL_FAILED, error);\n        }\n        this.emit(Event.ERROR, error);\n        if (endOfStream)\n          this._end();\n        (_this$_seiService4 = this._seiService) === null || _this$_seiService4 === void 0 ? void 0 : _this$_seiService4.reset();\n      }\n      return error;\n    }\n  }, {\n    key: \"_getSeamlessSwitchPoint\",\n    value: function _getSeamlessSwitchPoint() {\n      var media = this.media;\n      var nextLoadPoint = media.currentTime;\n      if (!media.paused) {\n        var _this$_stats;\n        var segIdx = this._playlist.findSegmentIndexByTime(media.currentTime);\n        var curSeg = this._playlist.getSegmentByIndex(segIdx);\n        var latestKbps = (_this$_stats = this._stats) === null || _this$_stats === void 0 ? void 0 : _this$_stats.getStats().downloadSpeed;\n        if (latestKbps && curSeg) {\n          var delay = curSeg.duration * this._playlist.currentStream.bitrate / latestKbps + 1;\n          nextLoadPoint += delay;\n        } else {\n          nextLoadPoint += 5;\n        }\n      }\n      return nextLoadPoint;\n    }\n  }, {\n    key: \"_tryEos\",\n    value: function _tryEos() {\n      var _this$_bufferService3, _this$_bufferService4;\n      var media = this.media;\n      var _this$_playlist2 = this._playlist, nextSegment = _this$_playlist2.nextSegment, lastSegment = _this$_playlist2.lastSegment;\n      var eosAllowed = (!nextSegment || lastSegment && Buffer.isBuffered(media, lastSegment.start + lastSegment.duration / 2)) && media.readyState && media.duration > 0 && ((_this$_bufferService3 = this._bufferService) === null || _this$_bufferService3 === void 0 ? void 0 : _this$_bufferService3.msIsOpened) && !((_this$_bufferService4 = this._bufferService) !== null && _this$_bufferService4 !== void 0 && _this$_bufferService4.msHasOpTasks);\n      if (!eosAllowed) {\n        return;\n      }\n      var bInfo = this.bufferInfo();\n      if (media.paused && !media.currentTime) {\n        bInfo = this.bufferInfo(bInfo.nextStart || 0.5);\n      }\n      var bufferThroughout = Math.abs(bInfo.end - media.duration) < 0.1 || !this.isLive && lastSegment && bInfo.end >= lastSegment.start + lastSegment.duration;\n      if (bufferThroughout) {\n        this._bufferService.endOfStream();\n      }\n    }\n  }], [{\n    key: \"isSupported\",\n    value: function isSupported(mediaType) {\n      if (!mediaType || mediaType === \"video\" || mediaType === \"audio\") {\n        return MSE.isSupported();\n      }\n      return typeof WebAssembly !== \"undefined\";\n    }\n  }, {\n    key: \"enableLogger\",\n    value: function enableLogger() {\n      Logger.enable();\n      Logger$1.enable();\n    }\n  }, {\n    key: \"disableLogger\",\n    value: function disableLogger() {\n      Logger.disable();\n      Logger$1.disable();\n    }\n  }]);\n  return Hls2;\n}(EventEmitter);\n_defineProperty(Hls, \"version\", \"3.0.20\");\ntry {\n  if (localStorage.getItem(\"xgd\")) {\n    Hls.enableLogger();\n  } else {\n    Hls.disableLogger();\n  }\n} catch (error) {\n}\nexport { Hls, logger };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck, asyncToGenerator as _asyncToGenerator, regeneratorRuntime as _regeneratorRuntime } from \"../../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { concatUint8Array } from \"xgplayer-streaming-shared\";\nvar Decryptor = /* @__PURE__ */ function() {\n  function Decryptor2() {\n    _classCallCheck(this, Decryptor2);\n    var crypto = window.crypto || window.msCrypto;\n    this.subtle = crypto && (crypto.subtle || crypto.webkitSubtle);\n    this.externalDecryptor = null;\n  }\n  _createClass(Decryptor2, [{\n    key: \"destroy\",\n    value: function destroy() {\n      var _this$externalDecrypt;\n      if ((_this$externalDecrypt = this.externalDecryptor) !== null && _this$externalDecrypt !== void 0 && _this$externalDecrypt.destroy) {\n        this.externalDecryptor.destroy();\n      }\n    }\n  }, {\n    key: \"decrypt\",\n    value: function decrypt(video, audio) {\n      if (!video && !audio)\n        return;\n      var ret = [];\n      if (video) {\n        ret[0] = this._decryptSegment(video);\n      }\n      if (audio) {\n        ret[1] = this._decryptSegment(audio);\n      }\n      return Promise.all(ret);\n    }\n  }, {\n    key: \"_decryptSegment\",\n    value: function() {\n      var _decryptSegment2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee2(seg) {\n        var data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1)\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                data = seg.data;\n                if (!seg.key) {\n                  _context2.next = 5;\n                  break;\n                }\n                _context2.next = 4;\n                return this._decryptData(seg.data, seg.key, seg.keyIv);\n              case 4:\n                data = _context2.sent;\n              case 5:\n                if (seg.map) {\n                  _context2.next = 7;\n                  break;\n                }\n                return _context2.abrupt(\"return\", data);\n              case 7:\n                return _context2.abrupt(\"return\", concatUint8Array(seg.map, data));\n              case 8:\n              case \"end\":\n                return _context2.stop();\n            }\n        }, _callee2, this);\n      }));\n      function _decryptSegment(_x4) {\n        return _decryptSegment2.apply(this, arguments);\n      }\n      return _decryptSegment;\n    }()\n  }, {\n    key: \"_decryptData\",\n    value: function() {\n      var _decryptData2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee3(data, key, iv) {\n        var aesKey;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1)\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                if (!this.externalDecryptor) {\n                  _context3.next = 6;\n                  break;\n                }\n                _context3.next = 3;\n                return this.externalDecryptor.decrypt(data, key, iv);\n              case 3:\n                return _context3.abrupt(\"return\", _context3.sent);\n              case 6:\n                if (this.subtle) {\n                  _context3.next = 8;\n                  break;\n                }\n                throw new Error(\"crypto is not defined\");\n              case 8:\n                _context3.next = 10;\n                return this.subtle.importKey(\"raw\", key, {\n                  name: \"AES-CBC\"\n                }, false, [\"encrypt\", \"decrypt\"]);\n              case 10:\n                aesKey = _context3.sent;\n                _context3.t0 = Uint8Array;\n                _context3.next = 14;\n                return this.subtle.decrypt({\n                  name: \"AES-CBC\",\n                  iv\n                }, aesKey, data);\n              case 14:\n                _context3.t1 = _context3.sent;\n                return _context3.abrupt(\"return\", new _context3.t0(_context3.t1));\n              case 16:\n              case \"end\":\n                return _context3.stop();\n            }\n        }, _callee3, this);\n      }));\n      function _decryptData(_x5, _x6, _x7) {\n        return _decryptData2.apply(this, arguments);\n      }\n      return _decryptData;\n    }()\n  }]);\n  return Decryptor2;\n}();\nexport { Decryptor };\n", "import { objectSpread2 as _objectSpread2 } from \"../_virtual/_rollupPluginBabelHelpers.js\";\nimport { EVENT } from \"xgplayer-streaming-shared\";\nvar Event = _objectSpread2(_objectSpread2({}, EVENT), {}, {\n  STREAM_PARSED: \"core.streamparsed\",\n  NO_AUDIO_TRACK: \"core.noaudiotrack\",\n  SUBTITLE_SEGMENTS: \"core.subtitlesegments\",\n  SUBTITLE_PLAYLIST: \"core.subtitleplaylist\",\n  SEI_PAYLOAD_TIME: \"core.seipayloadtime\",\n  APPEND_COST: \"core.appendcost\"\n});\nexport { Event };\n", "import { createClass as _createClass, objectSpread2 as _objectSpread2, classCallCheck as _classCallCheck, defineProperty as _defineProperty } from \"../../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { Logger, concatUint8Array, StreamingError, ERR } from \"xgplayer-streaming-shared\";\nimport { TrackType, WarningType, FMP4Demuxer, TsDemuxer, FMP4Remuxer } from \"xgplayer-transmuxer\";\nimport { Event } from \"../../constants.js\";\nvar logger = new Logger(\"Transmuxer\");\nvar Transmuxer = /* @__PURE__ */ function() {\n  function Transmuxer2(hls, isMP4, needRemux, fixerConfig) {\n    _classCallCheck(this, Transmuxer2);\n    _defineProperty(this, \"_initSegmentId\", \"\");\n    this.hls = hls;\n    this._demuxer = isMP4 ? new FMP4Demuxer() : new TsDemuxer(null, null, null, fixerConfig);\n    this._isMP4 = isMP4;\n    if (needRemux)\n      this._remuxer = new FMP4Remuxer(this._demuxer.videoTrack, this._demuxer.audioTrack);\n  }\n  _createClass(Transmuxer2, [{\n    key: \"transmux\",\n    value: function transmux(videoChunk, audioChunk, discontinuity, contiguous, startTime, needInit) {\n      var demuxer = this._demuxer;\n      try {\n        if (this._isMP4) {\n          demuxer.demux(videoChunk, audioChunk);\n        } else {\n          demuxer.demuxAndFix(concatUint8Array(videoChunk, audioChunk), discontinuity, contiguous, startTime);\n        }\n      } catch (error) {\n        throw new StreamingError(ERR.DEMUX, ERR.SUB_TYPES.HLS, error);\n      }\n      var videoTrack = demuxer.videoTrack, audioTrack = demuxer.audioTrack, metadataTrack = demuxer.metadataTrack;\n      var vParsed = {\n        codec: videoTrack.codec,\n        timescale: videoTrack.timescale,\n        firstDts: videoTrack.firstDts / videoTrack.timescale,\n        firstPts: videoTrack.firstPts / videoTrack.timescale,\n        duration: videoTrack.samplesDuration / videoTrack.timescale\n      };\n      var aParsed = {\n        codec: audioTrack.codec,\n        timescale: audioTrack.timescale,\n        firstDts: audioTrack.firstDts / videoTrack.timescale,\n        firstPts: audioTrack.firstPts / videoTrack.timescale,\n        duration: audioTrack.samplesDuration / videoTrack.timescale\n      };\n      var newId = \"\".concat(videoTrack.codec, \"/\").concat(videoTrack.width, \"/\").concat(videoTrack.height, \"/\").concat(audioTrack.codec, \"/\").concat(audioTrack.config);\n      if (newId !== this._initSegmentId) {\n        this._initSegmentId = newId;\n        needInit = true;\n      }\n      this._fireEvents(videoTrack, audioTrack, metadataTrack, discontinuity || needInit);\n      this.hls.emit(Event.DEMUXED_TRACK, {\n        videoTrack,\n        audioTrack\n      });\n      if (this._remuxer) {\n        if (needInit && this.hls.isLive && !this.hls.config.mseLowLatency) {\n          videoTrack.duration = this.hls.totalDuration * videoTrack.timescale;\n          audioTrack.duration = this.hls.totalDuration * audioTrack.timescale;\n        }\n        try {\n          var _this$_remuxer$remux = this._remuxer.remux(needInit), videoInitSegment = _this$_remuxer$remux.videoInitSegment, videoSegment = _this$_remuxer$remux.videoSegment, audioInitSegment = _this$_remuxer$remux.audioInitSegment, audioSegment = _this$_remuxer$remux.audioSegment;\n          var v = concatUint8Array(videoInitSegment, videoSegment);\n          var a = concatUint8Array(audioInitSegment, audioSegment);\n          return [v ? _objectSpread2(_objectSpread2({}, vParsed), {}, {\n            data: v\n          }) : void 0, a ? _objectSpread2(_objectSpread2({}, aParsed), {}, {\n            data: a\n          }) : void 0];\n        } catch (error) {\n          throw new StreamingError(ERR.REMUX, ERR.SUB_TYPES.FMP4, error);\n        }\n      } else {\n        return [videoTrack, audioTrack];\n      }\n    }\n  }, {\n    key: \"_fireEvents\",\n    value: function _fireEvents(videoTrack, audioTrack, metadataTrack, discontinuity) {\n      var _this = this;\n      var tracks = [videoTrack, audioTrack];\n      var logCC = \"discontinuity: \".concat(discontinuity);\n      tracks.forEach(function(track) {\n        var _track$samples;\n        if ((_track$samples = track.samples) !== null && _track$samples !== void 0 && _track$samples.length) {\n          logCC += \"; \".concat(track.samples.length, \" \").concat(track.type === TrackType.VIDEO ? \"video\" : \"audio\", \" samples, firstDts/firstPts/duration: \").concat((track.firstDts / track.timescale).toFixed(3), \"/\").concat((track.firstPts / track.timescale).toFixed(3), \"/\").concat((track.samplesDuration / track.timescale).toFixed(3));\n        }\n        if (discontinuity && track.exist()) {\n          _this.hls.emit(Event.METADATA_PARSED, {\n            type: track.type,\n            track,\n            meta: _objectSpread2({\n              codec: track.codec,\n              timescale: track.timescale,\n              baseDts: track.baseDts\n            }, track.type === TrackType.VIDEO ? {\n              width: track.width,\n              height: track.height,\n              sarRatio: track.sarRatio\n            } : {\n              codec: track.codec,\n              channelCount: track.channelCount,\n              sampleRate: track.sampleRate\n            })\n          });\n        }\n      });\n      logger.debug(logCC);\n      videoTrack.warnings.forEach(function(warn) {\n        var type;\n        switch (warn.type) {\n          case WarningType.LARGE_AV_SHIFT:\n            type = Event.LARGE_AV_FIRST_FRAME_GAP_DETECT;\n            break;\n          case WarningType.LARGE_VIDEO_GAP:\n            type = Event.LARGE_VIDEO_DTS_GAP_DETECT;\n            break;\n          case WarningType.LARGE_VIDEO_GAP_BETWEEN_CHUNK:\n            type = Event.MAX_DTS_DELTA_WITH_NEXT_SEGMENT_DETECT;\n            break;\n        }\n        if (type)\n          _this.hls.emit(Event.STREAM_EXCEPTION, _objectSpread2(_objectSpread2({}, warn), {}, {\n            type\n          }));\n        logger.warn(\"video exception\", warn);\n      });\n      audioTrack.warnings.forEach(function(warn) {\n        var type;\n        switch (warn.type) {\n          case WarningType.LARGE_AUDIO_GAP:\n            type = Event.LARGE_AUDIO_DTS_GAP_DETECT;\n            break;\n          case WarningType.AUDIO_FILLED:\n            type = Event.AUDIO_GAP_DETECT;\n            break;\n          case WarningType.AUDIO_DROPPED:\n            type = Event.AUDIO_OVERLAP_DETECT;\n            break;\n        }\n        if (type)\n          _this.hls.emit(Event.STREAM_EXCEPTION, _objectSpread2(_objectSpread2({}, warn), {}, {\n            type\n          }));\n        logger.warn(\"audio exception\", warn);\n      });\n      videoTrack.samples.forEach(function(sample) {\n        if (sample.keyframe) {\n          _this.hls.emit(Event.KEYFRAME, {\n            pts: sample.pts\n          });\n        }\n      });\n      metadataTrack.seiSamples.forEach(function(sei) {\n        _this.hls.emit(Event.SEI, _objectSpread2(_objectSpread2({}, sei), {}, {\n          originPts: sei.originPts / 90,\n          sei: {\n            code: sei.data.type,\n            content: sei.data.payload,\n            dts: sei.pts\n          }\n        }));\n      });\n    }\n  }]);\n  return Transmuxer2;\n}();\nexport { Transmuxer };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck, defineProperty as _defineProperty, asyncToGenerator as _asyncToGenerator, regeneratorRuntime as _regeneratorRuntime, slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from \"../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { TsDemuxer, MP4Parser } from \"xgplayer-transmuxer\";\nimport { Logger, StreamingError, ERR, MSE, EVENT, Buffer } from \"xgplayer-streaming-shared\";\nimport { Decryptor } from \"./decrypt/index.js\";\nimport { Transmuxer } from \"./transmuxer/index.js\";\nimport { Event } from \"../constants.js\";\nvar _excluded = [\"data\"], _excluded2 = [\"data\"];\nvar logger = new Logger(\"BufferService\");\nvar BufferService = /* @__PURE__ */ function() {\n  function BufferService2(hls) {\n    var _this = this;\n    _classCallCheck(this, BufferService2);\n    _defineProperty(this, \"_decryptor\", new Decryptor());\n    _defineProperty(this, \"_transmuxer\", null);\n    _defineProperty(this, \"_mse\", null);\n    _defineProperty(this, \"_softVideo\", null);\n    _defineProperty(this, \"_sourceCreated\", false);\n    _defineProperty(this, \"_needInitSegment\", true);\n    _defineProperty(this, \"_directAppend\", false);\n    this.hls = hls;\n    if (hls.config.softDecode) {\n      this._softVideo = hls.media;\n    } else {\n      this._mse = new MSE(null, {\n        preferMMS: hls.config.preferMMS\n      });\n      if (hls.config.url) {\n        this._mse.bindMedia(hls.media).then(function(e) {\n          _this.hls.emit(EVENT.MEDIASOURCE_OPENED, e);\n        });\n      }\n    }\n    if (hls.config.decryptor) {\n      this._decryptor.externalDecryptor = hls.config.decryptor;\n    }\n  }\n  _createClass(BufferService2, [{\n    key: \"baseDts\",\n    get: function get() {\n      var _this$_transmuxer, _this$_transmuxer$_de, _this$_transmuxer$_de2;\n      return (_this$_transmuxer = this._transmuxer) === null || _this$_transmuxer === void 0 ? void 0 : (_this$_transmuxer$_de = _this$_transmuxer._demuxer) === null || _this$_transmuxer$_de === void 0 ? void 0 : (_this$_transmuxer$_de2 = _this$_transmuxer$_de._fixer) === null || _this$_transmuxer$_de2 === void 0 ? void 0 : _this$_transmuxer$_de2._baseDts;\n    }\n  }, {\n    key: \"nbSb\",\n    get: function get() {\n      var _this$_mse;\n      if (!((_this$_mse = this._mse) !== null && _this$_mse !== void 0 && _this$_mse._sourceBuffer))\n        return 0;\n      return Object.keys(this._mse._sourceBuffer).length;\n    }\n  }, {\n    key: \"msIsOpened\",\n    get: function get() {\n      var _this$_mse2;\n      return (_this$_mse2 = this._mse) === null || _this$_mse2 === void 0 ? void 0 : _this$_mse2.isOpened;\n    }\n  }, {\n    key: \"msHasOpTasks\",\n    get: function get() {\n      var _this$_mse3;\n      return (_this$_mse3 = this._mse) === null || _this$_mse3 === void 0 ? void 0 : _this$_mse3.hasOpTasks;\n    }\n  }, {\n    key: \"msStreaming\",\n    get: function get() {\n      var _this$_mse4;\n      return (_this$_mse4 = this._mse) === null || _this$_mse4 === void 0 ? void 0 : _this$_mse4.streaming;\n    }\n  }, {\n    key: \"updateDuration\",\n    value: function() {\n      var _updateDuration = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee(duration) {\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1)\n            switch (_context.prev = _context.next) {\n              case 0:\n                logger.debug(\"update duration\", duration);\n                if (!this._mse) {\n                  _context.next = 9;\n                  break;\n                }\n                if (this._mse.isOpened) {\n                  _context.next = 5;\n                  break;\n                }\n                _context.next = 5;\n                return this._mse.open();\n              case 5:\n                _context.next = 7;\n                return this._mse.updateDuration(duration);\n              case 7:\n                _context.next = 10;\n                break;\n              case 9:\n                if (this._softVideo) {\n                  this._softVideo.duration = duration;\n                }\n              case 10:\n              case \"end\":\n                return _context.stop();\n            }\n        }, _callee, this);\n      }));\n      function updateDuration(_x) {\n        return _updateDuration.apply(this, arguments);\n      }\n      return updateDuration;\n    }()\n  }, {\n    key: \"createSource\",\n    value: function createSource(videoChunk, audioChunk, videoCodec, audioCodec) {\n      if (this._sourceCreated)\n        return;\n      var chunk = videoChunk || audioChunk;\n      if (!chunk)\n        return;\n      if (TsDemuxer.probe(chunk)) {\n        if (!this._transmuxer)\n          this._transmuxer = new Transmuxer(this.hls, false, !this._softVideo, this.hls.config.fixerConfig);\n      } else if (MP4Parser.probe(chunk)) {\n        if (this._softVideo) {\n          if (!this._transmuxer)\n            this._transmuxer = new Transmuxer(this.hls, true, null, this.hls.config.fixerConfig);\n        } else {\n          this._directAppend = true;\n          var mix = false;\n          if (videoChunk && !videoCodec) {\n            MP4Parser.findBox(videoChunk, [\"moov\", \"trak\"]).forEach(function(t) {\n              var box = MP4Parser.findBox(t.data, [\"trak\", \"mdia\", \"minf\", \"stbl\", \"stsd\"])[0];\n              if (box) {\n                var e = MP4Parser.stsd(box).entries[0];\n                if (e) {\n                  if (e.hvcC) {\n                    videoCodec = e.hvcC.codec || \"hev1.1.6.L93.B0\";\n                  } else if (e.avcC) {\n                    videoCodec = e.avcC.codec;\n                  } else if (e.sampleRate || e.esds) {\n                    var _e$esds;\n                    audioCodec = ((_e$esds = e.esds) === null || _e$esds === void 0 ? void 0 : _e$esds.codec) || \"mp4a.40.2\";\n                    mix = true;\n                  }\n                }\n              }\n            });\n          }\n          if (audioChunk && !audioCodec) {\n            MP4Parser.findBox(audioChunk, [\"moov\", \"trak\", \"mdia\", \"minf\", \"stbl\", \"stsd\"]).forEach(function(stsd) {\n              var e = MP4Parser.stsd(stsd).entries[0];\n              if (e && e.esds)\n                audioCodec = e.esds.codec;\n            });\n          }\n          if (videoChunk && !videoCodec)\n            videoCodec = \"avc1.42e01e\";\n          if (audioChunk && !audioCodec)\n            audioCodec = \"mp4a.40.2\";\n          if (mix) {\n            videoCodec += \", \".concat(audioCodec);\n            audioCodec = \"\";\n          }\n          this._createMseSource(videoCodec, audioCodec);\n        }\n      } else {\n        throw new StreamingError(ERR.OTHER, null, null, null, \"unsupported stream\");\n      }\n      if (this._softVideo)\n        this._sourceCreated = true;\n    }\n  }, {\n    key: \"appendBuffer\",\n    value: function() {\n      var _appendBuffer = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee2(segment, audioSegment, videoChunk, audioChunk, discontinuity, contiguous, startTime) {\n        var _this2 = this;\n        var afterAppend, p, needInit, _this$_transmuxer$tra, _this$_transmuxer$tra2, video, audio, isFirstAppend, mse, _p, videoData, videoRest, audioData, audioRest;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1)\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                if (!(!(videoChunk !== null && videoChunk !== void 0 && videoChunk.length) && !(audioChunk !== null && audioChunk !== void 0 && audioChunk.length))) {\n                  _context2.next = 2;\n                  break;\n                }\n                return _context2.abrupt(\"return\");\n              case 2:\n                afterAppend = function afterAppend2() {\n                  var _this2$hls;\n                  if ((_this2$hls = _this2.hls) !== null && _this2$hls !== void 0 && _this2$hls.emit) {\n                    var _this2$hls2;\n                    (_this2$hls2 = _this2.hls) === null || _this2$hls2 === void 0 ? void 0 : _this2$hls2.emit(EVENT.APPEND_BUFFER, {\n                      start: segment.start,\n                      end: segment.end\n                    });\n                  }\n                };\n                if (!this._directAppend) {\n                  _context2.next = 8;\n                  break;\n                }\n                p = [];\n                if (videoChunk)\n                  p.push(this._mse.append(MSE.VIDEO, videoChunk));\n                if (audioChunk)\n                  p.push(this._mse.append(MSE.AUDIO, audioChunk));\n                return _context2.abrupt(\"return\", Promise.all(p).then(afterAppend));\n              case 8:\n                needInit = this._needInitSegment || discontinuity;\n                _this$_transmuxer$tra = this._transmuxer.transmux(videoChunk, audioChunk, needInit, contiguous, startTime, this._needInitSegment || discontinuity), _this$_transmuxer$tra2 = _slicedToArray(_this$_transmuxer$tra, 2), video = _this$_transmuxer$tra2[0], audio = _this$_transmuxer$tra2[1];\n                if (audioChunk && audioSegment) {\n                  audioSegment === null || audioSegment === void 0 ? void 0 : audioSegment.setTrackExist(false, true);\n                }\n                if (audioChunk && segment) {\n                  segment === null || segment === void 0 ? void 0 : segment.setTrackExist(true, false);\n                }\n                if (!audioSegment) {\n                  segment === null || segment === void 0 ? void 0 : segment.setTrackExist(!!video, !!audio);\n                }\n                if (video && !audio) {\n                  this.hls.emit(Event.NO_AUDIO_TRACK);\n                }\n                if (!this._softVideo) {\n                  _context2.next = 20;\n                  break;\n                }\n                this._softVideo.appendBuffer(video, audio);\n                this._needInitSegment = false;\n                afterAppend();\n                _context2.next = 30;\n                break;\n              case 20:\n                if (!this._mse) {\n                  _context2.next = 30;\n                  break;\n                }\n                isFirstAppend = !this._sourceCreated;\n                if (isFirstAppend) {\n                  this._createMseSource(video === null || video === void 0 ? void 0 : video.codec, audio === null || audio === void 0 ? void 0 : audio.codec);\n                }\n                this._needInitSegment = false;\n                mse = this._mse;\n                _p = [];\n                if (needInit && !isFirstAppend) {\n                  this._handleCodecChange(video, audio).forEach(function(task) {\n                    return _p.push(task);\n                  });\n                }\n                if (video) {\n                  videoData = video.data, videoRest = _objectWithoutProperties(video, _excluded);\n                  _p.push(mse.append(MSE.VIDEO, videoData, videoRest));\n                }\n                if (audio) {\n                  audioData = audio.data, audioRest = _objectWithoutProperties(audio, _excluded2);\n                  _p.push(mse.append(MSE.AUDIO, audioData, audioRest));\n                }\n                return _context2.abrupt(\"return\", Promise.all(_p).then(afterAppend));\n              case 30:\n              case \"end\":\n                return _context2.stop();\n            }\n        }, _callee2, this);\n      }));\n      function appendBuffer(_x2, _x3, _x4, _x5, _x6, _x7, _x8) {\n        return _appendBuffer.apply(this, arguments);\n      }\n      return appendBuffer;\n    }()\n  }, {\n    key: \"removeBuffer\",\n    value: function() {\n      var _removeBuffer = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee3() {\n        var _this3 = this;\n        var start, end, media, _args3 = arguments;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1)\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                start = _args3.length > 0 && _args3[0] !== void 0 ? _args3[0] : 0;\n                end = _args3.length > 1 && _args3[1] !== void 0 ? _args3[1] : Infinity;\n                media = this.hls.media;\n                if (!(!this._mse || !media || start < 0 || end < start || start >= this._mse.duration)) {\n                  _context3.next = 5;\n                  break;\n                }\n                return _context3.abrupt(\"return\");\n              case 5:\n                return _context3.abrupt(\"return\", this._mse.clearBuffer(start, end).then(function() {\n                  return _this3.hls.emit(EVENT.REMOVE_BUFFER, {\n                    start,\n                    end,\n                    removeEnd: end\n                  });\n                }));\n              case 6:\n              case \"end\":\n                return _context3.stop();\n            }\n        }, _callee3, this);\n      }));\n      function removeBuffer() {\n        return _removeBuffer.apply(this, arguments);\n      }\n      return removeBuffer;\n    }()\n  }, {\n    key: \"evictBuffer\",\n    value: function() {\n      var _evictBuffer = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee4(bufferBehind) {\n        var media, currentTime, removeEnd, start;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1)\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                media = this.hls.media;\n                if (!(!this._mse || !media || !bufferBehind || bufferBehind < 0)) {\n                  _context4.next = 3;\n                  break;\n                }\n                return _context4.abrupt(\"return\");\n              case 3:\n                currentTime = media.currentTime;\n                removeEnd = currentTime - bufferBehind;\n                if (!(removeEnd <= 0)) {\n                  _context4.next = 7;\n                  break;\n                }\n                return _context4.abrupt(\"return\");\n              case 7:\n                start = Buffer.start(Buffer.get(media));\n                if (!(start + 1 >= removeEnd)) {\n                  _context4.next = 10;\n                  break;\n                }\n                return _context4.abrupt(\"return\");\n              case 10:\n                return _context4.abrupt(\"return\", this.removeBuffer(0, removeEnd));\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n        }, _callee4, this);\n      }));\n      function evictBuffer(_x9) {\n        return _evictBuffer.apply(this, arguments);\n      }\n      return evictBuffer;\n    }()\n  }, {\n    key: \"clearAllBuffer\",\n    value: function() {\n      var _clearAllBuffer = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee5() {\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1)\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                if (!this._mse) {\n                  _context5.next = 2;\n                  break;\n                }\n                return _context5.abrupt(\"return\", this._mse.clearAllBuffer());\n              case 2:\n              case \"end\":\n                return _context5.stop();\n            }\n        }, _callee5, this);\n      }));\n      function clearAllBuffer() {\n        return _clearAllBuffer.apply(this, arguments);\n      }\n      return clearAllBuffer;\n    }()\n  }, {\n    key: \"decryptBuffer\",\n    value: function decryptBuffer(video, audio) {\n      return this._decryptor.decrypt(video, audio);\n    }\n  }, {\n    key: \"reset\",\n    value: function() {\n      var _reset = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee6() {\n        var reuseMse, _args6 = arguments;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1)\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                reuseMse = _args6.length > 0 && _args6[0] !== void 0 ? _args6[0] : false;\n                if (!(this._mse && !reuseMse)) {\n                  _context6.next = 8;\n                  break;\n                }\n                this._transmuxer = null;\n                this._sourceCreated = false;\n                _context6.next = 6;\n                return this._mse.unbindMedia();\n              case 6:\n                _context6.next = 8;\n                return this._mse.bindMedia(this.hls.media);\n              case 8:\n                this._needInitSegment = true;\n                this._directAppend = false;\n              case 10:\n              case \"end\":\n                return _context6.stop();\n            }\n        }, _callee6, this);\n      }));\n      function reset() {\n        return _reset.apply(this, arguments);\n      }\n      return reset;\n    }()\n  }, {\n    key: \"endOfStream\",\n    value: function() {\n      var _endOfStream = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee7() {\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1)\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                if (!this._mse) {\n                  _context7.next = 5;\n                  break;\n                }\n                if (!this._sourceCreated) {\n                  _context7.next = 5;\n                  break;\n                }\n                _context7.next = 4;\n                return this._mse.endOfStream();\n              case 4:\n                this.hls.emit(EVENT.BUFFEREOS);\n              case 5:\n                if (this._softVideo) {\n                  this._softVideo.endOfStream();\n                }\n              case 6:\n              case \"end\":\n                return _context7.stop();\n            }\n        }, _callee7, this);\n      }));\n      function endOfStream() {\n        return _endOfStream.apply(this, arguments);\n      }\n      return endOfStream;\n    }()\n  }, {\n    key: \"setLiveSeekableRange\",\n    value: function() {\n      var _setLiveSeekableRange = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee8(start, end) {\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1)\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                if (this._mse)\n                  this._mse.setLiveSeekableRange(start, end);\n              case 1:\n              case \"end\":\n                return _context8.stop();\n            }\n        }, _callee8, this);\n      }));\n      function setLiveSeekableRange(_x10, _x11) {\n        return _setLiveSeekableRange.apply(this, arguments);\n      }\n      return setLiveSeekableRange;\n    }()\n  }, {\n    key: \"detachMedia\",\n    value: function() {\n      var _detachMedia = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee9() {\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1)\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                if (!this._mse) {\n                  _context9.next = 3;\n                  break;\n                }\n                _context9.next = 3;\n                return this._mse.unbindMedia();\n              case 3:\n              case \"end\":\n                return _context9.stop();\n            }\n        }, _callee9, this);\n      }));\n      function detachMedia() {\n        return _detachMedia.apply(this, arguments);\n      }\n      return detachMedia;\n    }()\n  }, {\n    key: \"destroy\",\n    value: function() {\n      var _destroy = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee10() {\n        var _this$_decryptor;\n        return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n          while (1)\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                (_this$_decryptor = this._decryptor) === null || _this$_decryptor === void 0 ? void 0 : _this$_decryptor.destroy();\n                _context10.next = 3;\n                return this.detachMedia();\n              case 3:\n                this._decryptor = null;\n                this._mse = null;\n                this._softVideo = null;\n              case 6:\n              case \"end\":\n                return _context10.stop();\n            }\n        }, _callee10, this);\n      }));\n      function destroy() {\n        return _destroy.apply(this, arguments);\n      }\n      return destroy;\n    }()\n  }, {\n    key: \"_createMseSource\",\n    value: function _createMseSource(videoCodec, audioCodec) {\n      logger.debug(\"create mse source, videoCodec=\".concat(videoCodec, \", audioCodec=\").concat(audioCodec));\n      var mse = this._mse;\n      if (!mse)\n        return;\n      if (videoCodec) {\n        mse.createSource(MSE.VIDEO, \"video/mp4;codecs=\".concat(videoCodec));\n        this._sourceCreated = true;\n      }\n      if (audioCodec) {\n        mse.createSource(MSE.AUDIO, \"audio/mp4;codecs=\".concat(audioCodec));\n        this._sourceCreated = true;\n      }\n      this.hls.emit(EVENT.SOURCEBUFFER_CREATED);\n    }\n  }, {\n    key: \"_handleCodecChange\",\n    value: function _handleCodecChange(video, audio) {\n      var tasks = [];\n      var mse = this._mse;\n      var codecList = [{\n        type: MSE.VIDEO,\n        codecs: video === null || video === void 0 ? void 0 : video.codec\n      }, {\n        type: MSE.AUDIO,\n        codecs: audio === null || audio === void 0 ? void 0 : audio.codec\n      }];\n      codecList.filter(function(item) {\n        return !!item.codecs;\n      }).forEach(function(_ref) {\n        var type = _ref.type, codecs = _ref.codecs;\n        var sourceBuffer = mse.getSourceBuffer(type);\n        if (sourceBuffer) {\n          var codec = codecs.split(\",\")[0];\n          if (!new RegExp(codec, \"ig\").test(sourceBuffer.mimeType)) {\n            tasks.push(mse.changeType(type, \"\".concat(type, \"/mp4;codecs=\").concat(codecs)));\n          }\n        }\n      });\n      return tasks;\n    }\n  }, {\n    key: \"seamlessSwitch\",\n    value: function seamlessSwitch() {\n      this._needInitSegment = true;\n    }\n  }, {\n    key: \"isFull\",\n    value: function isFull() {\n      var _this$_mse5;\n      var mediaType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : MSE.VIDEO;\n      return (_this$_mse5 = this._mse) === null || _this$_mse5 === void 0 ? void 0 : _this$_mse5.isFull(mediaType);\n    }\n  }]);\n  return BufferService2;\n}();\nexport { BufferService };\n", "import { objectSpread2 as _objectSpread2 } from \"../_virtual/_rollupPluginBabelHelpers.js\";\nfunction getConfig(cfg) {\n  var media = (cfg === null || cfg === void 0 ? void 0 : cfg.media) || document.createElement(\"video\");\n  return _objectSpread2(_objectSpread2({\n    maxPlaylistSize: 50,\n    retryCount: 3,\n    retryDelay: 1e3,\n    pollRetryCount: 2,\n    loadTimeout: 1e4,\n    manifestLoadTimeout: 1e4,\n    preloadTime: 30,\n    softDecode: false,\n    bufferBehind: 10,\n    maxJumpDistance: 3,\n    startTime: 0,\n    useLowLatency: true,\n    targetLatency: 10,\n    maxLatency: 20,\n    allowedStreamTrackChange: true,\n    seiInTime: false,\n    manifestList: [],\n    minSegmentsStartPlay: 3,\n    preferMMS: false,\n    preferMMSStreaming: false,\n    mseLowLatency: true,\n    fixerConfig: {\n      forceFixLargeGap: false,\n      largeGapThreshold: 5\n    }\n  }, cfg), {}, {\n    media\n  });\n}\nexport { getConfig };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck, defineProperty as _defineProperty, inherits as _inherits, createSuper as _createSuper, assertThisInitialized as _assertThisInitialized } from \"../../../_virtual/_rollupPluginBabelHelpers.js\";\nvar MasterPlaylist = /* @__PURE__ */ _createClass(function MasterPlaylist2() {\n  _classCallCheck(this, MasterPlaylist2);\n  _defineProperty(this, \"version\", 0);\n  _defineProperty(this, \"streams\", []);\n  _defineProperty(this, \"isMaster\", true);\n});\nvar MediaType = {\n  Audio: \"AUDIO\",\n  Video: \"VIDEO\",\n  SubTitle: \"SUBTITLE\",\n  ClosedCaptions: \"CLOSED-CAPTIONS\"\n};\nvar KeySystems = {\n  CLEAR_KEY: \"org.w3.clearkey\",\n  FAIRPLAY: [\"urn:uuid:94ce86fb-07ff-4f43-adb8-93d2fa968ca2\", \"com.apple.streamingkeydelivery\"],\n  WIDEVINE: [\"urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed\", \"com.widevine.alpha\", \"com.widevine\"],\n  PLAYREADY: [\"urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95\", \"com.microsoft.playready\"]\n};\nfunction flatArray(arr) {\n  var ret = [];\n  for (var i = 0; i < arr.length; i++) {\n    if (Array.isArray(arr[i])) {\n      ret = ret.concat(flatArray(arr[i]));\n    } else {\n      ret.push(arr[i]);\n    }\n  }\n  return ret;\n}\nvar MediaStream = /* @__PURE__ */ _createClass(function MediaStream2() {\n  _classCallCheck(this, MediaStream2);\n  _defineProperty(this, \"id\", 0);\n  _defineProperty(this, \"url\", \"\");\n  _defineProperty(this, \"default\", false);\n  _defineProperty(this, \"autoSelect\", false);\n  _defineProperty(this, \"forced\", false);\n  _defineProperty(this, \"group\", \"\");\n  _defineProperty(this, \"name\", \"\");\n  _defineProperty(this, \"lang\", \"\");\n  _defineProperty(this, \"segments\", []);\n  _defineProperty(this, \"endSN\", 0);\n});\nvar AudioStream = /* @__PURE__ */ function(_MediaStream) {\n  _inherits(AudioStream2, _MediaStream);\n  var _super = _createSuper(AudioStream2);\n  function AudioStream2() {\n    var _this;\n    _classCallCheck(this, AudioStream2);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"mediaType\", MediaType.Audio);\n    _defineProperty(_assertThisInitialized(_this), \"channels\", 0);\n    return _this;\n  }\n  return _createClass(AudioStream2);\n}(MediaStream);\nvar SubTitleStream = /* @__PURE__ */ function(_MediaStream3) {\n  _inherits(SubTitleStream2, _MediaStream3);\n  var _super3 = _createSuper(SubTitleStream2);\n  function SubTitleStream2() {\n    var _this3;\n    _classCallCheck(this, SubTitleStream2);\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    _this3 = _super3.call.apply(_super3, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this3), \"mediaType\", MediaType.SubTitle);\n    return _this3;\n  }\n  return _createClass(SubTitleStream2);\n}(MediaStream);\nvar MasterStream = /* @__PURE__ */ _createClass(function MasterStream2() {\n  _classCallCheck(this, MasterStream2);\n  _defineProperty(this, \"id\", 0);\n  _defineProperty(this, \"bitrate\", 0);\n  _defineProperty(this, \"width\", 0);\n  _defineProperty(this, \"height\", 0);\n  _defineProperty(this, \"name\", \"\");\n  _defineProperty(this, \"url\", \"\");\n  _defineProperty(this, \"audioCodec\", \"\");\n  _defineProperty(this, \"videoCodec\", \"\");\n  _defineProperty(this, \"textCodec\", \"\");\n  _defineProperty(this, \"audioGroup\", \"\");\n  _defineProperty(this, \"audioStreams\", []);\n  _defineProperty(this, \"subtitleStreams\", []);\n  _defineProperty(this, \"closedCaptionsStream\", []);\n});\nvar MediaPlaylist = /* @__PURE__ */ _createClass(function MediaPlaylist2() {\n  _classCallCheck(this, MediaPlaylist2);\n  _defineProperty(this, \"version\", 0);\n  _defineProperty(this, \"url\", \"\");\n  _defineProperty(this, \"type\", \"\");\n  _defineProperty(this, \"startCC\", 0);\n  _defineProperty(this, \"endCC\", 0);\n  _defineProperty(this, \"startSN\", 0);\n  _defineProperty(this, \"endSN\", 0);\n  _defineProperty(this, \"totalDuration\", 0);\n  _defineProperty(this, \"targetDuration\", 0);\n  _defineProperty(this, \"partTargetDuration\", 0);\n  _defineProperty(this, \"canSkipUntil\", 0);\n  _defineProperty(this, \"canSkipDateRanges\", false);\n  _defineProperty(this, \"skippedSegments\", 0);\n  _defineProperty(this, \"canBlockReload\", false);\n  _defineProperty(this, \"partHoldBack\", 0);\n  _defineProperty(this, \"live\", true);\n  _defineProperty(this, \"lowLatency\", false);\n  _defineProperty(this, \"endPartIndex\", 0);\n  _defineProperty(this, \"segments\", []);\n  _defineProperty(this, \"dateRanges\", {});\n  _defineProperty(this, \"skippedSegments\", 0);\n});\nvar MediaSegment = /* @__PURE__ */ function() {\n  function MediaSegment2(parentUrl) {\n    _classCallCheck(this, MediaSegment2);\n    _defineProperty(this, \"sn\", 0);\n    _defineProperty(this, \"cc\", 0);\n    _defineProperty(this, \"url\", \"\");\n    _defineProperty(this, \"parentUrl\", \"\");\n    _defineProperty(this, \"title\", \"\");\n    _defineProperty(this, \"start\", 0);\n    _defineProperty(this, \"duration\", 0);\n    _defineProperty(this, \"dataTime\", \"\");\n    _defineProperty(this, \"key\", null);\n    _defineProperty(this, \"byteRange\", null);\n    _defineProperty(this, \"isInitSegment\", false);\n    _defineProperty(this, \"initSegment\", null);\n    _defineProperty(this, \"isLast\", false);\n    _defineProperty(this, \"hasAudio\", false);\n    _defineProperty(this, \"hasVideo\", false);\n    _defineProperty(this, \"independent\", false);\n    _defineProperty(this, \"partIndex\", 0);\n    this.parentUrl = parentUrl;\n  }\n  _createClass(MediaSegment2, [{\n    key: \"end\",\n    get: function get() {\n      return this.start + this.duration;\n    }\n  }, {\n    key: \"setTrackExist\",\n    value: function setTrackExist(v, a) {\n      this.hasVideo = v;\n      this.hasAudio = a;\n    }\n  }, {\n    key: \"setByteRange\",\n    value: function setByteRange(data, prevSegment) {\n      this.byteRange = [0];\n      var bytes = data.split(\"@\");\n      if (bytes.length === 1 && prevSegment && prevSegment.byteRange) {\n        this.byteRange[0] = prevSegment.byteRange[1] || 0;\n        if (this.byteRange[0])\n          this.byteRange[0] += 1;\n      } else {\n        this.byteRange[0] = parseInt(bytes[1]);\n      }\n      this.byteRange[1] = this.byteRange[0] + parseInt(bytes[0]) - 1;\n    }\n  }]);\n  return MediaSegment2;\n}();\nvar MediaSegmentKey = /* @__PURE__ */ function() {\n  function MediaSegmentKey2(segKey) {\n    _classCallCheck(this, MediaSegmentKey2);\n    _defineProperty(this, \"method\", \"\");\n    _defineProperty(this, \"url\", \"\");\n    _defineProperty(this, \"iv\", null);\n    _defineProperty(this, \"keyFormat\", \"\");\n    _defineProperty(this, \"keyFormatVersions\", \"\");\n    if (segKey instanceof MediaSegmentKey2) {\n      this.method = segKey.method;\n      this.url = segKey.url;\n      this.keyFormat = segKey.keyFormat;\n      this.keyFormatVersions = segKey.keyFormatVersions;\n      if (segKey.iv)\n        this.iv = new Uint8Array(segKey.iv);\n    }\n  }\n  _createClass(MediaSegmentKey2, [{\n    key: \"clone\",\n    value: function clone(sn) {\n      var key = new MediaSegmentKey2(this);\n      if (sn !== null && sn !== void 0)\n        key.setIVFromSN(sn);\n      return key;\n    }\n  }, {\n    key: \"setIVFromSN\",\n    value: function setIVFromSN(sn) {\n      if (!this.iv && this.method === \"AES-128\" && typeof sn === \"number\" && this.url) {\n        this.iv = new Uint8Array(16);\n        for (var i = 12; i < 16; i++) {\n          this.iv[i] = sn >> 8 * (15 - i) & 255;\n        }\n      }\n    }\n  }, {\n    key: \"isSegmentEncrypted\",\n    value: function isSegmentEncrypted() {\n      var method = this.method;\n      return method === \"AES-128\";\n    }\n  }, {\n    key: \"isValidKeySystem\",\n    value: function isValidKeySystem() {\n      var isKeyFormatValid = flatArray([KeySystems.CLEAR_KEY, KeySystems.FAIRPLAY, KeySystems.WIDEVINE, KeySystems.PLAYREADY]).indexOf(this.keyFormat) > -1;\n      if (!isKeyFormatValid) {\n        return false;\n      }\n      var isMethodValid = [\"SAMPLE-AES\", \"SAMPLE-AES-CENC\", \"SAMPLE-AES-CTR\"].indexOf(this.method) > -1;\n      if (!isMethodValid) {\n        return false;\n      }\n      return true;\n    }\n  }, {\n    key: \"isSupported\",\n    value: function isSupported() {\n      if (!this.method) {\n        return false;\n      }\n      if (this.isSegmentEncrypted()) {\n        return true;\n      } else if (this.isValidKeySystem()) {\n        return true;\n      }\n      return false;\n    }\n  }]);\n  return MediaSegmentKey2;\n}();\nexport { AudioStream, MasterPlaylist, MasterStream, MediaPlaylist, MediaSegment, MediaSegmentKey, MediaStream, SubTitleStream };\n", "var REGEXP_TAG = /^#(EXT[^:]*)(?::(.*))?$/;\nvar REGEXP_ATTR = /([^=]+)=(?:\"([^\"]*)\"|([^\",]*))(?:,|$)/g;\nvar REGEXP_ABSOLUTE_URL = /^(?:[a-zA-Z0-9+\\-.]+:)?\\/\\//;\nvar REGEXP_URL_PAIR = /^((?:[a-zA-Z0-9+\\-.]+:)?\\/\\/[^/?#]*)?([^?#]*\\/)?/;\nfunction getLines(text) {\n  return text.split(/[\\r\\n]/).map(function(x) {\n    return x.trim();\n  }).filter(Boolean);\n}\nfunction parseTag(text) {\n  var ret = text.match(REGEXP_TAG);\n  if (!ret || !ret[1])\n    return;\n  return [ret[1].replace(\"EXT-X-\", \"\"), ret[2]];\n}\nfunction parseAttr(text) {\n  var ret = {};\n  var match = REGEXP_ATTR.exec(text);\n  while (match) {\n    ret[match[1]] = match[2] || match[3];\n    match = REGEXP_ATTR.exec(text);\n  }\n  return ret;\n}\nfunction getAbsoluteUrl(url, parentUrl) {\n  if (!parentUrl || !url || REGEXP_ABSOLUTE_URL.test(url))\n    return url;\n  var pairs = REGEXP_URL_PAIR.exec(parentUrl);\n  if (!pairs)\n    return url;\n  if (url[0] === \"/\")\n    return pairs[1] + url;\n  return pairs[1] + pairs[2] + url;\n}\nvar CODECS_REGEXP = {\n  audio: [/^mp4a/, /^vorbis$/, /^opus$/, /^flac$/, /^[ae]c-3$/],\n  video: [/^avc/, /^hev/, /^hvc/, /^vp0?[89]/, /^av1$/],\n  text: [/^vtt$/, /^wvtt/, /^stpp/]\n};\nfunction getCodecs(type, codecs) {\n  var re = CODECS_REGEXP[type];\n  if (!re || !codecs || !codecs.length)\n    return;\n  for (var i = 0; i < re.length; i++) {\n    for (var j = 0; j < codecs.length; j++) {\n      if (re[i].test(codecs[j]))\n        return codecs[j];\n    }\n  }\n}\nfunction isValidDaterange(attr, dateRangeWithSameId) {\n  var _badValueForSameId;\n  if (dateRangeWithSameId) {\n    for (var key in dateRangeWithSameId) {\n      if (Object.prototype.hasOwnProperty.call(dateRangeWithSameId, key) && attr[key] !== dateRangeWithSameId[key]) {\n        _badValueForSameId = key;\n        break;\n      }\n    }\n  }\n  var duration = null;\n  if (attr.DURATION) {\n    duration = parseFloat(attr.DURATION);\n    if (!Number.isFinite(duration)) {\n      duration = null;\n    } else if (attr._endDate) {\n      duration = (attr._endDate.getTime() - attr._startDate.getTime()) / 1e3;\n    }\n  }\n  var cue = enumeratedStringList(attr.CUE || attr[\"X-CUE\"], {\n    pre: false,\n    post: false,\n    once: false\n  });\n  return !!attr.ID && !_badValueForSameId && Number.isFinite(attr._startDate.getTime()) && (duration === null || duration >= 0) && (!(attr.END_ON_NEXT === \"YES\") || !!attr.CLASS) && (!attr.CUE || !cue.pre && !cue.post || cue.pre !== cue.post) && (!(attr.CLASS === \"com.apple.hls.interstitial\") || \"X-ASSET-URI\" in attr || \"X-ASSET-LIST\" in attr);\n}\nfunction enumeratedStringList(attrValue, dict) {\n  return (attrValue ? attrValue.split(/[ ,]+/) : []).reduce(function(result, identifier) {\n    result[identifier.toLowerCase()] = true;\n    return result;\n  }, dict);\n}\nexport { getAbsoluteUrl, getCodecs, getLines, isValidDaterange, parseAttr, parseTag };\n", "import { slicedToArray as _slicedToArray } from \"../../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { MasterPlaylist, MediaStream, SubTitleStream, AudioStream, MasterStream } from \"./model.js\";\nimport { parseTag, parseAttr, getAbsoluteUrl, getCodecs } from \"./utils.js\";\nfunction parseMasterPlaylist(lines, parentUrl) {\n  var master = new MasterPlaylist();\n  var index = 0;\n  var line;\n  var audioStreams = [];\n  var subtitleStreams = [];\n  while (line = lines[index++]) {\n    var tag = parseTag(line);\n    if (!tag)\n      continue;\n    var _tag = _slicedToArray(tag, 2), name = _tag[0], data = _tag[1];\n    if (name === \"VERSION\") {\n      master.version = parseInt(data);\n    } else if (name === \"MEDIA\" && data) {\n      var attr = parseAttr(data);\n      var stream = void 0;\n      switch (attr.TYPE) {\n        case \"AUDIO\":\n          stream = new AudioStream();\n          break;\n        case \"SUBTITLES\":\n          stream = new SubTitleStream();\n          break;\n        default:\n          stream = new MediaStream();\n      }\n      stream.url = getAbsoluteUrl(attr.URI, parentUrl);\n      stream.default = attr.DEFAULT === \"YES\";\n      stream.autoSelect = attr.AUTOSELECT === \"YES\";\n      stream.group = attr[\"GROUP-ID\"];\n      stream.name = attr.NAME;\n      stream.lang = attr.LANGUAGE;\n      if (attr.CHANNELS) {\n        stream.channels = Number(attr.CHANNELS.split(\"/\")[0]);\n        if (Number.isNaN(stream.channels))\n          stream.channels = 0;\n      }\n      if (attr.TYPE === \"AUDIO\" && attr.URI) {\n        audioStreams.push(stream);\n      }\n      if (attr.TYPE === \"SUBTITLES\") {\n        subtitleStreams.push(stream);\n      }\n    } else if (name === \"STREAM-INF\" && data) {\n      var _stream = new MasterStream();\n      var _attr = parseAttr(data);\n      _stream.bitrate = parseInt(_attr[\"AVERAGE-BANDWIDTH\"] || _attr.BANDWIDTH);\n      _stream.name = _attr.NAME;\n      _stream.url = getAbsoluteUrl(lines[index++], parentUrl);\n      if (_attr.RESOLUTION) {\n        var _attr$RESOLUTION$spli = _attr.RESOLUTION.split(\"x\"), _attr$RESOLUTION$spli2 = _slicedToArray(_attr$RESOLUTION$spli, 2), w = _attr$RESOLUTION$spli2[0], h = _attr$RESOLUTION$spli2[1];\n        _stream.width = parseInt(w);\n        _stream.height = parseInt(h);\n      }\n      if (_attr.CODECS) {\n        var codecs = _attr.CODECS.split(/[ ,]+/).filter(Boolean);\n        _stream.videoCodec = getCodecs(\"video\", codecs);\n        _stream.audioCodec = getCodecs(\"audio\", codecs);\n        _stream.textCodec = getCodecs(\"text\", codecs);\n      }\n      _stream.audioGroup = _attr.AUDIO;\n      _stream.subtitleGroup = _attr.SUBTITLES;\n      master.streams.push(_stream);\n    }\n  }\n  master.streams.forEach(function(s, i) {\n    s.id = i;\n  });\n  if (audioStreams.length) {\n    audioStreams.forEach(function(s, i) {\n      s.id = i;\n    });\n    master.streams.forEach(function(stream2) {\n      if (stream2.audioGroup) {\n        stream2.audioStreams = audioStreams.filter(function(x) {\n          return x.group === stream2.audioGroup;\n        });\n      }\n    });\n  }\n  if (subtitleStreams.length) {\n    subtitleStreams.forEach(function(s, i) {\n      s.id = i;\n    });\n    master.streams.forEach(function(stream2) {\n      if (stream2.subtitleGroup) {\n        stream2.subtitleStreams = subtitleStreams.filter(function(x) {\n          return x.group === stream2.subtitleGroup;\n        });\n      }\n    });\n  }\n  return master;\n}\nexport { parseMasterPlaylist };\n", "import { slicedToArray as _slicedToArray } from \"../../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { MediaPlaylist, MediaSegment, MediaSegmentKey } from \"./model.js\";\nimport { getAbsoluteUrl, parseTag, parseAttr, isValidDaterange } from \"./utils.js\";\nfunction parseMediaPlaylist(lines, parentUrl, useLowLatency) {\n  var media = new MediaPlaylist();\n  media.url = parentUrl;\n  var curSegment = new MediaSegment(parentUrl);\n  var curInitSegment = null;\n  var curKey = null;\n  var totalDuration = 0;\n  var curSN = 0;\n  var curCC = 0;\n  var index = 0;\n  var line;\n  var endOfList = false;\n  var partSegmentIndex = 0;\n  while (line = lines[index++]) {\n    if (line[0] !== \"#\") {\n      if (media.lowLatency) {\n        curSN++;\n        continue;\n      }\n      curSegment.sn = curSN;\n      curSegment.cc = curCC;\n      curSegment.url = getAbsoluteUrl(line, parentUrl);\n      if (curKey)\n        curSegment.key = curKey.clone(curSN);\n      if (curInitSegment)\n        curSegment.initSegment = curInitSegment;\n      media.segments.push(curSegment);\n      curSegment = new MediaSegment(parentUrl);\n      curSN++;\n      continue;\n    }\n    var tag = parseTag(line);\n    if (!tag)\n      continue;\n    var _tag = _slicedToArray(tag, 2), name = _tag[0], data = _tag[1];\n    switch (name) {\n      case \"VERSION\":\n        media.version = parseInt(data);\n        break;\n      case \"PLAYLIST-TYPE\":\n        media.type = data === null || data === void 0 ? void 0 : data.toUpperCase();\n        break;\n      case \"TARGETDURATION\":\n        media.targetDuration = parseFloat(data);\n        break;\n      case \"PART-INF\":\n        {\n          if (useLowLatency) {\n            media.lowLatency = true;\n          }\n          var attr = parseAttr(data);\n          if (attr[\"PART-TARGET\"]) {\n            media.partTargetDuration = parseFloat(attr[\"PART-TARGET\"]);\n          }\n        }\n        break;\n      case \"SERVER-CONTROL\":\n        {\n          var _attr = parseAttr(data);\n          media.canBlockReload = _attr[\"CAN-BLOCK-RELOAD\"] === \"YES\";\n          media.partHoldBack = parseFloat(_attr[\"PART-HOLD-BACK\"] || 0);\n          media.canSkipUntil = parseFloat(_attr[\"CAN-SKIP-UNTIL\"] || 0);\n          media.canSkipDateRanges = _attr[\"CAN-SKIP-DATERANGES\"] === \"YES\";\n        }\n        break;\n      case \"ENDLIST\":\n        {\n          endOfList = true;\n        }\n        break;\n      case \"MEDIA-SEQUENCE\":\n        curSN = media.startSN = parseInt(data);\n        break;\n      case \"DISCONTINUITY-SEQUENCE\":\n        curCC = media.startCC = parseInt(data);\n        break;\n      case \"DISCONTINUITY\":\n        curCC++;\n        break;\n      case \"BYTERANGE\":\n        curSegment.setByteRange(data, media.segments[media.segments.length - 1]);\n        break;\n      case \"PART\":\n        {\n          if (!media.lowLatency)\n            break;\n          var _attr2 = parseAttr(data);\n          curSegment.duration = parseFloat(_attr2[\"DURATION\"]);\n          curSegment.independent = _attr2[\"INDEPENDENT\"] === \"YES\";\n          curSegment.sn = curSN;\n          curSegment.cc = curCC;\n          curSegment.partIndex = partSegmentIndex;\n          curSegment.start = totalDuration;\n          curSegment.duration = parseFloat(_attr2[\"DURATION\"]);\n          totalDuration += curSegment.duration;\n          curSegment.url = getAbsoluteUrl(_attr2[\"URI\"], parentUrl);\n          if (curKey)\n            curSegment.key = curKey.clone(curSN);\n          if (curInitSegment)\n            curSegment.initSegment = curInitSegment;\n          media.segments.push(curSegment);\n          curSegment = new MediaSegment(parentUrl);\n          partSegmentIndex++;\n        }\n        break;\n      case \"PRELOAD-HINT\":\n        break;\n      case \"PROGRAM-DATE-TIME\":\n        curSegment.dataTime = data;\n        break;\n      case \"EXTINF\":\n        {\n          if (media.lowLatency) {\n            partSegmentIndex = 0;\n            break;\n          }\n          var _data$split = data.split(\",\"), _data$split2 = _slicedToArray(_data$split, 2), duration = _data$split2[0], title = _data$split2[1];\n          curSegment.start = totalDuration;\n          curSegment.duration = parseFloat(duration);\n          totalDuration += curSegment.duration;\n          curSegment.title = title;\n        }\n        break;\n      case \"KEY\":\n        {\n          var _attr3 = parseAttr(data);\n          if (_attr3.METHOD === \"NONE\") {\n            curKey = null;\n            break;\n          }\n          curKey = new MediaSegmentKey();\n          curKey.method = _attr3.METHOD;\n          curKey.url = /^blob:/.test(_attr3.URI) ? _attr3.URI : getAbsoluteUrl(_attr3.URI, parentUrl);\n          curKey.keyFormat = _attr3.KEYFORMAT || \"identity\";\n          curKey.keyFormatVersions = _attr3.KEYFORMATVERSIONS;\n          if (!curKey.isSupported()) {\n            throw new Error(\"encrypt \".concat(_attr3.METHOD, \"/\").concat(_attr3.KEYFORMAT, \" is not supported\"));\n          }\n          if (_attr3.IV) {\n            var str = _attr3.IV.slice(2);\n            str = (str.length & 1 ? \"0\" : \"\") + str;\n            curKey.iv = new Uint8Array(str.length / 2);\n            for (var i = 0, l = str.length / 2; i < l; i++) {\n              curKey.iv[i] = parseInt(str.slice(i * 2, i * 2 + 2), 16);\n            }\n          }\n        }\n        break;\n      case \"MAP\":\n        {\n          var _attr4 = parseAttr(data);\n          curSegment.url = getAbsoluteUrl(_attr4.URI, parentUrl);\n          if (_attr4.BYTERANGE)\n            curSegment.setByteRange(_attr4.BYTERANGE);\n          curSegment.isInitSegment = true;\n          curSegment.sn = 0;\n          if (curKey) {\n            curSegment.key = curKey.clone(0);\n          }\n          curInitSegment = curSegment;\n          curSegment = new MediaSegment(parentUrl);\n        }\n        break;\n      case \"SKIP\":\n        {\n          var _attr5 = parseAttr(data);\n          var skippedSegments = parseInt(_attr5[\"SKIPPED-SEGMENTS\"], 10);\n          if (skippedSegments <= Number.MAX_SAFE_INTEGER) {\n            media.skippedSegments += skippedSegments;\n          }\n        }\n        break;\n      case \"DATERANGE\":\n        {\n          var _attr6 = parseAttr(data);\n          var dateRangeWithSameId = media.dateRanges[_attr6.ID];\n          _attr6._startDate = dateRangeWithSameId ? dateRangeWithSameId._startDate : new Date(_attr6[\"START-DATE\"]);\n          var endDate = (dateRangeWithSameId === null || dateRangeWithSameId === void 0 ? void 0 : dateRangeWithSameId._endDate) || new Date(_attr6.END_DATE);\n          if (Number.isFinite(endDate)) {\n            _attr6._endDate = endDate;\n          }\n          if (isValidDaterange(_attr6, dateRangeWithSameId) || media.skippedSegments) {\n            media.dateRanges[_attr6.ID] = _attr6;\n          }\n        }\n        break;\n    }\n  }\n  media.segments = media.segments.filter(function(x) {\n    return x.duration !== 0;\n  });\n  var lastSegment = media.segments[media.segments.length - 1];\n  if (lastSegment) {\n    if (endOfList) {\n      lastSegment.isLast = true;\n    }\n    media.endSN = lastSegment.sn;\n    media.endPartIndex = lastSegment.partIndex;\n  }\n  if (endOfList) {\n    media.live = false;\n  }\n  media.totalDuration = totalDuration;\n  media.endCC = curCC;\n  return media;\n}\nexport { parseMediaPlaylist };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck } from \"../../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { parseMasterPlaylist } from \"./master.js\";\nimport { parseMediaPlaylist } from \"./media.js\";\nimport { getLines } from \"./utils.js\";\nvar M3U8Parser = /* @__PURE__ */ function() {\n  function M3U8Parser2() {\n    _classCallCheck(this, M3U8Parser2);\n  }\n  _createClass(M3U8Parser2, null, [{\n    key: \"parse\",\n    value: function parse() {\n      var text = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"\";\n      var parentUrl = arguments.length > 1 ? arguments[1] : void 0;\n      var useLowLatency = arguments.length > 2 ? arguments[2] : void 0;\n      if (!text.includes(\"#EXTM3U\"))\n        throw new Error(\"Invalid m3u8 file\");\n      var lines = getLines(text);\n      if (M3U8Parser2.isMediaPlaylist(text)) {\n        return parseMediaPlaylist(lines, parentUrl, useLowLatency);\n      }\n      return parseMasterPlaylist(lines, parentUrl);\n    }\n  }, {\n    key: \"isMediaPlaylist\",\n    value: function isMediaPlaylist(text) {\n      return text.includes(\"#EXTINF:\") || text.includes(\"#EXT-X-TARGETDURATION:\");\n    }\n  }]);\n  return M3U8Parser2;\n}();\nexport { M3U8Parser };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck, defineProperty as _defineProperty, objectSpread2 as _objectSpread2, asyncToGenerator as _asyncToGenerator, regeneratorRuntime as _regeneratorRuntime, slicedToArray as _slicedToArray } from \"../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { EVENT, StreamingError, NetLoader, ERR } from \"xgplayer-streaming-shared\";\nimport { M3U8Parser } from \"./parser/index.js\";\nimport { Event } from \"../constants.js\";\nvar ManifestLoader = /* @__PURE__ */ function() {\n  function ManifestLoader2(hls) {\n    var _this = this;\n    _classCallCheck(this, ManifestLoader2);\n    _defineProperty(this, \"_emitOnLoaded\", function(res, url) {\n      var response = res.response, options = res.options;\n      var _ref = options || {}, firstByteTime = _ref.firstByteTime, startTime = _ref.startTime, endTime = _ref.endTime, contentLength = _ref.contentLength;\n      var time = endTime - startTime;\n      _this.hls.emit(EVENT.SPEED, {\n        time,\n        byteLength: contentLength,\n        url\n      });\n      _this.hls.emit(EVENT.LOAD_COMPLETE, {\n        url,\n        elapsed: time || 0\n      });\n      _this.hls.emit(EVENT.TTFB, {\n        url,\n        responseUrl: response.url,\n        elapsed: firstByteTime - startTime\n      });\n      _this.hls.emit(EVENT.LOAD_RESPONSE_HEADERS, {\n        headers: response.headers,\n        url\n      });\n    });\n    _defineProperty(this, \"_onLoaderRetry\", function(error, retryTime) {\n      _this.hls.emit(Event.LOAD_RETRY, {\n        error: StreamingError.network(error),\n        retryTime\n      });\n    });\n    this.hls = hls;\n    this._timer = null;\n    this._useLowLatency = hls.config.useLowLatency;\n    var _this$hls$config = this.hls.config, retryCount = _this$hls$config.retryCount, retryDelay = _this$hls$config.retryDelay, manifestLoadTimeout = _this$hls$config.manifestLoadTimeout, fetchOptions = _this$hls$config.fetchOptions;\n    this._loader = new NetLoader(_objectSpread2(_objectSpread2({}, fetchOptions), {}, {\n      responseType: \"text\",\n      retry: retryCount,\n      retryDelay,\n      timeout: manifestLoadTimeout,\n      onRetryError: this._onLoaderRetry\n    }));\n    this._audioLoader = new NetLoader(_objectSpread2(_objectSpread2({}, fetchOptions), {}, {\n      responseType: \"text\",\n      retry: retryCount,\n      retryDelay,\n      timeout: manifestLoadTimeout,\n      onRetryError: this._onLoaderRetry\n    }));\n    this._subtitleLoader = new NetLoader(_objectSpread2(_objectSpread2({}, fetchOptions), {}, {\n      responseType: \"text\",\n      retry: retryCount,\n      retryDelay,\n      timeout: manifestLoadTimeout,\n      onRetryError: this._onLoaderRetry\n    }));\n  }\n  _createClass(ManifestLoader2, [{\n    key: \"load\",\n    value: function() {\n      var _load = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee(url, audioUrl, subtitleUrl) {\n        var toLoad, videoText, audioText, subtitleText, videoResUrl, audioResUrl, subtitleResUrl, _yield$Promise$all, _yield$Promise$all2, video, audio, subtitle, _audio$response, _subtitle$response, _audio$response2, onPreM3U8Parse, playlist, audioPlaylist, subtitlePlaylist, _playlist;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1)\n            switch (_context.prev = _context.next) {\n              case 0:\n                toLoad = [this._loader.load(url)];\n                if (audioUrl) {\n                  toLoad.push(this._audioLoader.load(audioUrl));\n                }\n                if (subtitleUrl) {\n                  toLoad.push(this._subtitleLoader.load(subtitleUrl));\n                }\n                _context.prev = 3;\n                _context.next = 6;\n                return Promise.all(toLoad);\n              case 6:\n                _yield$Promise$all = _context.sent;\n                _yield$Promise$all2 = _slicedToArray(_yield$Promise$all, 3);\n                video = _yield$Promise$all2[0];\n                audio = _yield$Promise$all2[1];\n                subtitle = _yield$Promise$all2[2];\n                if (video) {\n                  _context.next = 13;\n                  break;\n                }\n                return _context.abrupt(\"return\", []);\n              case 13:\n                this._emitOnLoaded(video, url);\n                videoText = video.data;\n                videoResUrl = video.response.url || url;\n                if (audioUrl) {\n                  audioText = audio === null || audio === void 0 ? void 0 : audio.data;\n                  subtitleText = subtitle === null || subtitle === void 0 ? void 0 : subtitle.data;\n                  audioResUrl = (audio === null || audio === void 0 ? void 0 : (_audio$response = audio.response) === null || _audio$response === void 0 ? void 0 : _audio$response.url) || audioUrl;\n                  subtitleResUrl = (subtitle === null || subtitle === void 0 ? void 0 : (_subtitle$response = subtitle.response) === null || _subtitle$response === void 0 ? void 0 : _subtitle$response.url) || subtitleUrl;\n                  audioText && this._emitOnLoaded(audio, audioUrl);\n                  subtitleText && this._emitOnLoaded(subtitle, subtitleUrl);\n                } else {\n                  subtitleText = audio === null || audio === void 0 ? void 0 : audio.data;\n                  subtitleResUrl = (audio === null || audio === void 0 ? void 0 : (_audio$response2 = audio.response) === null || _audio$response2 === void 0 ? void 0 : _audio$response2.url) || subtitleUrl;\n                  subtitleText && this._emitOnLoaded(audio, subtitleUrl);\n                }\n                _context.next = 22;\n                break;\n              case 19:\n                _context.prev = 19;\n                _context.t0 = _context[\"catch\"](3);\n                throw StreamingError.network(_context.t0);\n              case 22:\n                onPreM3U8Parse = this.hls.config.onPreM3U8Parse;\n                _context.prev = 23;\n                if (onPreM3U8Parse) {\n                  videoText = onPreM3U8Parse(videoText) || videoText;\n                  if (audioText)\n                    audioText = onPreM3U8Parse(audioText, true) || audioText;\n                  if (subtitleText)\n                    subtitleText = onPreM3U8Parse(subtitleText, true) || subtitleText;\n                }\n                playlist = M3U8Parser.parse(videoText, videoResUrl, this._useLowLatency);\n                if (!(((_playlist = playlist) === null || _playlist === void 0 ? void 0 : _playlist.live) === false && playlist.segments && !playlist.segments.length)) {\n                  _context.next = 28;\n                  break;\n                }\n                throw new Error(\"empty segments list\");\n              case 28:\n                if (audioText) {\n                  audioPlaylist = M3U8Parser.parse(audioText, audioResUrl, this._useLowLatency);\n                }\n                if (subtitleText) {\n                  subtitlePlaylist = M3U8Parser.parse(subtitleText, subtitleResUrl, this._useLowLatency);\n                }\n                _context.next = 35;\n                break;\n              case 32:\n                _context.prev = 32;\n                _context.t1 = _context[\"catch\"](23);\n                throw new StreamingError(ERR.MANIFEST, ERR.SUB_TYPES.HLS, _context.t1);\n              case 35:\n                if (playlist) {\n                  if (playlist.isMaster) {\n                    this.hls.emit(Event.HLS_MANIFEST_LOADED, {\n                      playlist\n                    });\n                  } else {\n                    this.hls.emit(Event.HLS_LEVEL_LOADED, {\n                      playlist\n                    });\n                  }\n                }\n                return _context.abrupt(\"return\", [playlist, audioPlaylist, subtitlePlaylist]);\n              case 37:\n              case \"end\":\n                return _context.stop();\n            }\n        }, _callee, this, [[3, 19], [23, 32]]);\n      }));\n      function load(_x, _x2, _x3) {\n        return _load.apply(this, arguments);\n      }\n      return load;\n    }()\n  }, {\n    key: \"parseText\",\n    value: function parseText(videoText, url) {\n      var onPreM3U8Parse = this.hls.config.onPreM3U8Parse;\n      var playlist;\n      try {\n        var _playlist2;\n        if (onPreM3U8Parse) {\n          videoText = onPreM3U8Parse(videoText) || videoText;\n        }\n        playlist = M3U8Parser.parse(videoText, url, this._useLowLatency);\n        if (((_playlist2 = playlist) === null || _playlist2 === void 0 ? void 0 : _playlist2.live) === false && playlist.segments && !playlist.segments.length) {\n          throw new Error(\"empty segments list\");\n        }\n      } catch (error) {\n        throw new StreamingError(ERR.MANIFEST, ERR.SUB_TYPES.HLS, error);\n      }\n      if (playlist) {\n        if (playlist.isMaster) {\n          this.hls.emit(Event.HLS_MANIFEST_LOADED, {\n            playlist\n          });\n        } else {\n          this.hls.emit(Event.HLS_LEVEL_LOADED, {\n            playlist\n          });\n        }\n      }\n      return [playlist];\n    }\n  }, {\n    key: \"poll\",\n    value: function poll(url, audioUrl, subtitleUrl, cb, errorCb, time) {\n      var _this2 = this;\n      clearTimeout(this._timer);\n      time = time || 3e3;\n      var retryCount = this.hls.config.pollRetryCount;\n      var fn = /* @__PURE__ */ function() {\n        var _ref2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee2() {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n            while (1)\n              switch (_context2.prev = _context2.next) {\n                case 0:\n                  clearTimeout(_this2._timer);\n                  _context2.prev = 1;\n                  _context2.next = 4;\n                  return _this2.load(url, audioUrl, subtitleUrl);\n                case 4:\n                  res = _context2.sent;\n                  if (res[0]) {\n                    _context2.next = 7;\n                    break;\n                  }\n                  return _context2.abrupt(\"return\");\n                case 7:\n                  retryCount = _this2.hls.config.pollRetryCount;\n                  cb(res[0], res[1], res[2]);\n                  _context2.next = 15;\n                  break;\n                case 11:\n                  _context2.prev = 11;\n                  _context2.t0 = _context2[\"catch\"](1);\n                  retryCount--;\n                  if (retryCount <= 0) {\n                    errorCb(_context2.t0);\n                  }\n                case 15:\n                  _this2._timer = setTimeout(fn, time);\n                case 16:\n                case \"end\":\n                  return _context2.stop();\n              }\n          }, _callee2, null, [[1, 11]]);\n        }));\n        return function fn2() {\n          return _ref2.apply(this, arguments);\n        };\n      }();\n      this._timer = setTimeout(fn, time);\n    }\n  }, {\n    key: \"stopPoll\",\n    value: function stopPoll() {\n      clearTimeout(this._timer);\n      return this.cancel();\n    }\n  }, {\n    key: \"cancel\",\n    value: function cancel() {\n      return Promise.all([this._loader.cancel(), this._audioLoader.cancel()]);\n    }\n  }]);\n  return ManifestLoader2;\n}();\nexport { ManifestLoader };\n", "function clamp(num, min, max) {\n  if (min > max) {\n    max = min;\n  }\n  return Math.min(Math.max(num, min), max);\n}\nexport { clamp };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck, defineProperty as _defineProperty } from \"../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { Logger } from \"xgplayer-streaming-shared\";\nvar logger = new Logger(\"playlist\");\nvar Stream = /* @__PURE__ */ function() {\n  function Stream2(playlist, audioPlaylist, subtitlePlaylist) {\n    _classCallCheck(this, Stream2);\n    _defineProperty(this, \"live\", void 0);\n    _defineProperty(this, \"id\", 0);\n    _defineProperty(this, \"bitrate\", 0);\n    _defineProperty(this, \"width\", 0);\n    _defineProperty(this, \"height\", 0);\n    _defineProperty(this, \"name\", \"\");\n    _defineProperty(this, \"url\", \"\");\n    _defineProperty(this, \"audioCodec\", \"\");\n    _defineProperty(this, \"videoCodec\", \"\");\n    _defineProperty(this, \"textCodec\", \"\");\n    _defineProperty(this, \"startCC\", 0);\n    _defineProperty(this, \"endCC\", 0);\n    _defineProperty(this, \"startSN\", 0);\n    _defineProperty(this, \"endSN\", -1);\n    _defineProperty(this, \"totalDuration\", 0);\n    _defineProperty(this, \"targetDuration\", 0);\n    _defineProperty(this, \"partTargetDuration\", 0);\n    _defineProperty(this, \"canSkipUntil\", 0);\n    _defineProperty(this, \"canSkipDateRanges\", false);\n    _defineProperty(this, \"skippedSegments\", 0);\n    _defineProperty(this, \"canBlockReload\", false);\n    _defineProperty(this, \"partHoldBack\", 0);\n    _defineProperty(this, \"lowLatency\", false);\n    _defineProperty(this, \"endPartIndex\", 0);\n    _defineProperty(this, \"snDiff\", null);\n    _defineProperty(this, \"segments\", []);\n    _defineProperty(this, \"audioStreams\", []);\n    _defineProperty(this, \"subtitleStreams\", []);\n    _defineProperty(this, \"closedCaptions\", []);\n    _defineProperty(this, \"currentAudioStream\", null);\n    _defineProperty(this, \"currentSubtitleStream\", null);\n    this.update(this._setLLPlaybackPoint(playlist), audioPlaylist, subtitlePlaylist);\n  }\n  _createClass(Stream2, [{\n    key: \"lastSegment\",\n    get: function get() {\n      if (this.segments.length) {\n        return this.segments[this.segments.length - 1];\n      }\n      return null;\n    }\n  }, {\n    key: \"segmentDuration\",\n    get: function get() {\n      var _this$segments$;\n      return this.targetDuration || ((_this$segments$ = this.segments[0]) === null || _this$segments$ === void 0 ? void 0 : _this$segments$.duration) || 0;\n    }\n  }, {\n    key: \"liveEdge\",\n    get: function get() {\n      return this.endTime;\n    },\n    set: function set(end) {\n      this.endTime = end;\n    }\n  }, {\n    key: \"endTime\",\n    get: function get() {\n      var _this$lastSegment;\n      return ((_this$lastSegment = this.lastSegment) === null || _this$lastSegment === void 0 ? void 0 : _this$lastSegment.end) || 0;\n    },\n    set: function set(end) {\n      var lastSeg = this.lastSegment;\n      if (lastSeg)\n        lastSeg.duration = end - lastSeg.start;\n    }\n  }, {\n    key: \"currentSubtitleEndSn\",\n    get: function get() {\n      var _this$currentSubtitle;\n      return ((_this$currentSubtitle = this.currentSubtitleStream) === null || _this$currentSubtitle === void 0 ? void 0 : _this$currentSubtitle.endSN) || 0;\n    }\n  }, {\n    key: \"clearOldSegment\",\n    value: function clearOldSegment(startTime, pointer) {\n      if (this.currentAudioStream) {\n        this._clearSegments(startTime, pointer);\n      }\n      return this._clearSegments(startTime, pointer);\n    }\n  }, {\n    key: \"getAudioSegment\",\n    value: function getAudioSegment(seg) {\n      if (!seg || !this.currentAudioStream)\n        return;\n      var sn = seg.sn - this.snDiff;\n      return this.currentAudioStream.segments.find(function(x) {\n        return x.sn === sn;\n      });\n    }\n  }, {\n    key: \"update\",\n    value: function update(playlist, audioPlaylist) {\n      this.url = playlist.url;\n      if (Array.isArray(playlist.segments)) {\n        if (this.live === null || this.live === void 0)\n          this.live = playlist.live;\n        this._updateSegments(playlist, this);\n        this.startCC = playlist.startCC;\n        this.endCC = playlist.endCC;\n        this.startSN = playlist.startSN;\n        this.endSN = playlist.endSN || -1;\n        this.totalDuration = playlist.totalDuration;\n        this.targetDuration = playlist.targetDuration;\n        this.live = playlist.live;\n        this.lowLatency = playlist.lowLatency;\n        this.canBlockReload = playlist.canBlockReload;\n        this.canSkipDateRanges = playlist.canSkipDateRanges;\n        this.canSkipUntil = playlist.canSkipUntil;\n        this.partHoldBack = playlist.partHoldBack;\n        this.partTargetDuration = playlist.partTargetDuration;\n        this.skippedSegments = playlist.skippedSegments;\n        this.endPartIndex = playlist.endPartIndex;\n        if (audioPlaylist && this.currentAudioStream && Array.isArray(audioPlaylist.segments)) {\n          this._updateSegments(audioPlaylist, this.currentAudioStream);\n          if ((this.snDiff === null || this.snDiff === void 0) && playlist.segments.length && audioPlaylist.segments.length) {\n            this.snDiff = playlist.segments[0].sn - audioPlaylist.segments[0].sn;\n          }\n        }\n      } else {\n        this.id = playlist.id;\n        this.bitrate = playlist.bitrate;\n        this.width = playlist.width;\n        this.height = playlist.height;\n        this.name = playlist.name;\n        this.audioCodec = playlist.audioCodec;\n        this.videoCodec = playlist.videoCodec;\n        this.textCodec = playlist.textCodec;\n        this.audioStreams = playlist.audioStreams;\n        this.subtitleStreams = playlist.subtitleStreams;\n        if (!this.currentAudioStream && this.audioStreams.length) {\n          this.currentAudioStream = this.audioStreams.find(function(x) {\n            return x.default;\n          }) || this.audioStreams[0];\n        }\n        if (!this.currentSubtitleStream && this.subtitleStreams.length) {\n          this.currentSubtitleStream = this.subtitleStreams.find(function(x) {\n            return x.default;\n          }) || this.subtitleStreams[0];\n        }\n      }\n    }\n  }, {\n    key: \"updateSubtitle\",\n    value: function updateSubtitle(subtitlePlaylist) {\n      var _this = this;\n      if (!(subtitlePlaylist && this.currentSubtitleStream && Array.isArray(subtitlePlaylist.segments)))\n        return;\n      var newSegs = this._updateSegments(subtitlePlaylist, this.currentSubtitleStream);\n      var segs = this.currentSubtitleStream.segments;\n      if (segs.length > 100) {\n        this.currentSubtitleStream.segments = segs.slice(100);\n      }\n      if (!newSegs)\n        return;\n      return newSegs.map(function(x) {\n        return {\n          sn: x.sn,\n          url: x.url,\n          duration: x.duration,\n          start: x.start,\n          end: x.end,\n          lang: _this.currentSubtitleStream.lang\n        };\n      });\n    }\n  }, {\n    key: \"switchSubtitle\",\n    value: function switchSubtitle(lang) {\n      var toSwitch = this.subtitleStreams.find(function(x) {\n        return x.lang === lang;\n      });\n      var origin = this.currentSubtitleStream;\n      if (toSwitch) {\n        this.currentSubtitleStream = toSwitch;\n        origin.segments = [];\n      }\n    }\n  }, {\n    key: \"_setLLPlaybackPoint\",\n    value: function _setLLPlaybackPoint(playlist) {\n      if (!playlist.lowLatency || !playlist.segments.length)\n        return playlist;\n      var maxStartPoint = playlist.totalDuration - playlist.partHoldBack;\n      var segs = playlist.segments;\n      var index = 0;\n      for (var i = 0, l = segs.length; i < l; i++) {\n        if (segs[i].start <= maxStartPoint && segs[i].independent) {\n          index = i;\n        }\n      }\n      var usefulSegs = segs.slice(index);\n      var endTime = 0;\n      usefulSegs.forEach(function(s) {\n        s.start = endTime;\n        endTime = s.end;\n      });\n      playlist.segments = usefulSegs;\n      playlist.totalDuration = endTime;\n      playlist.startSN = usefulSegs[0].sn;\n      playlist.startCC = usefulSegs[0].cc;\n      logger.log(\"set ll-hls playback point: SN=\".concat(playlist.startSN, \" partIndex=\").concat(usefulSegs[0].partIndex, \", duration=\").concat(endTime));\n      return playlist;\n    }\n  }, {\n    key: \"_clearSegments\",\n    value: function _clearSegments(startTime, pointer) {\n      var sliceStart = 0;\n      var segments = this.segments;\n      for (var i = 0, l = segments.length; i < l; i++) {\n        if (segments[i].end >= startTime) {\n          sliceStart = i;\n          break;\n        }\n      }\n      if (sliceStart > pointer) {\n        sliceStart = pointer;\n      }\n      if (sliceStart) {\n        this.segments = this.segments.slice(sliceStart);\n        if (this.currentAudioStream) {\n          this.currentAudioStream.segments = this.currentAudioStream.segments.slice(sliceStart);\n        }\n      }\n      return pointer - sliceStart;\n    }\n  }, {\n    key: \"_updateSegments\",\n    value: function _updateSegments(playlist, segObj) {\n      var segments = segObj.segments;\n      if (this.live) {\n        var _endSeg$sn;\n        var lowLatency = playlist.lowLatency;\n        var endSeg = segments[segments.length - 1];\n        var endSN = (_endSeg$sn = endSeg === null || endSeg === void 0 ? void 0 : endSeg.sn) !== null && _endSeg$sn !== void 0 ? _endSeg$sn : -1;\n        var endPartIndex = (endSeg === null || endSeg === void 0 ? void 0 : endSeg.partIndex) || 0;\n        var hasNew = endSN < playlist.endSN && playlist.segments.length;\n        if (lowLatency) {\n          hasNew = hasNew || endPartIndex < playlist.endPartIndex;\n        }\n        if (hasNew) {\n          logger.log(\"update segments: endSN:\".concat(endSN, \", partIndex:\").concat(endPartIndex, \" --> endSN:\").concat(playlist.endSN, \", partIndex:\").concat(playlist.endPartIndex));\n          var index = playlist.segments.findIndex(function(x) {\n            return x.sn === endSN && x.partIndex === endPartIndex;\n          });\n          var toAppend = index < 0 ? playlist.segments : playlist.segments.slice(index + 1);\n          if (segments.length && toAppend.length) {\n            var endTime = endSeg.end;\n            var endTimeBeforeAppend = endTime;\n            toAppend.forEach(function(seg) {\n              seg.start = endTime;\n              endTime = seg.end;\n            });\n            logger.log(\"liveEdge: \".concat(endTimeBeforeAppend, \" -> \").concat(endTime));\n            var lastCC = (endSeg === null || endSeg === void 0 ? void 0 : endSeg.cc) || -1;\n            if (lastCC > toAppend[0].cc) {\n              toAppend.forEach(function(seg) {\n                return seg.cc += lastCC;\n              });\n            }\n          }\n          segObj.endSN = playlist.endSN;\n          segObj.segments = segments.concat(toAppend);\n          return toAppend;\n        }\n      } else {\n        segObj.segments = playlist.segments;\n      }\n    }\n  }]);\n  return Stream2;\n}();\nexport { Stream };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck, defineProperty as _defineProperty } from \"../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { clamp } from \"../utils.js\";\nimport { Stream } from \"./stream.js\";\nimport { Event } from \"../constants.js\";\nvar Playlist = /* @__PURE__ */ function() {\n  function Playlist2(hls) {\n    _classCallCheck(this, Playlist2);\n    _defineProperty(this, \"streams\", []);\n    _defineProperty(this, \"currentStream\", null);\n    _defineProperty(this, \"dvrWindow\", 0);\n    _defineProperty(this, \"_segmentPointer\", -1);\n    this.hls = hls;\n  }\n  _createClass(Playlist2, [{\n    key: \"lowLatency\",\n    get: function get() {\n      var _this$currentStream;\n      return (_this$currentStream = this.currentStream) === null || _this$currentStream === void 0 ? void 0 : _this$currentStream.lowLatency;\n    }\n  }, {\n    key: \"lastSegment\",\n    get: function get() {\n      var _this$currentStream2;\n      return (_this$currentStream2 = this.currentStream) === null || _this$currentStream2 === void 0 ? void 0 : _this$currentStream2.lastSegment;\n    }\n  }, {\n    key: \"currentSegment\",\n    get: function get() {\n      var _this$currentSegments;\n      return (_this$currentSegments = this.currentSegments) === null || _this$currentSegments === void 0 ? void 0 : _this$currentSegments[this._segmentPointer];\n    }\n  }, {\n    key: \"nextSegment\",\n    get: function get() {\n      var _this$currentSegments2;\n      return (_this$currentSegments2 = this.currentSegments) === null || _this$currentSegments2 === void 0 ? void 0 : _this$currentSegments2[this._segmentPointer + 1];\n    }\n  }, {\n    key: \"currentSegments\",\n    get: function get() {\n      var _this$currentStream3;\n      return (_this$currentStream3 = this.currentStream) === null || _this$currentStream3 === void 0 ? void 0 : _this$currentStream3.segments;\n    }\n  }, {\n    key: \"currentSubtitleEndSn\",\n    get: function get() {\n      var _this$currentStream4;\n      return (_this$currentStream4 = this.currentStream) === null || _this$currentStream4 === void 0 ? void 0 : _this$currentStream4.currentSubtitleEndSn;\n    }\n  }, {\n    key: \"liveEdge\",\n    get: function get() {\n      var _this$currentStream5;\n      return (_this$currentStream5 = this.currentStream) === null || _this$currentStream5 === void 0 ? void 0 : _this$currentStream5.liveEdge;\n    },\n    set: function set(end) {\n      if (this.currentStream) {\n        this.currentStream.liveEdge = end;\n      }\n    }\n  }, {\n    key: \"totalDuration\",\n    get: function get() {\n      var _this$currentStream6;\n      return ((_this$currentStream6 = this.currentStream) === null || _this$currentStream6 === void 0 ? void 0 : _this$currentStream6.totalDuration) || 0;\n    }\n  }, {\n    key: \"seekRange\",\n    get: function get() {\n      var segments = this.currentSegments;\n      if (!segments || !segments.length)\n        return;\n      return [segments[0].start, segments[segments.length - 1].end];\n    }\n  }, {\n    key: \"nbSegments\",\n    get: function get() {\n      var _this$currentSegments3;\n      return ((_this$currentSegments3 = this.currentSegments) === null || _this$currentSegments3 === void 0 ? void 0 : _this$currentSegments3.length) || 0;\n    }\n  }, {\n    key: \"isEmpty\",\n    get: function get() {\n      var _this$currentSegments4;\n      return !((_this$currentSegments4 = this.currentSegments) !== null && _this$currentSegments4 !== void 0 && _this$currentSegments4.length);\n    }\n  }, {\n    key: \"isLive\",\n    get: function get() {\n      var _this$currentStream7;\n      return (_this$currentStream7 = this.currentStream) === null || _this$currentStream7 === void 0 ? void 0 : _this$currentStream7.live;\n    }\n  }, {\n    key: \"hadSegmentLoaded\",\n    get: function get() {\n      return this._segmentPointer !== -1;\n    }\n  }, {\n    key: \"hasSubtitle\",\n    get: function get() {\n      var _this$currentStream8;\n      return !!((_this$currentStream8 = this.currentStream) !== null && _this$currentStream8 !== void 0 && _this$currentStream8.currentSubtitleStream);\n    }\n  }, {\n    key: \"getAudioSegment\",\n    value: function getAudioSegment(seg) {\n      var _this$currentStream9;\n      return (_this$currentStream9 = this.currentStream) === null || _this$currentStream9 === void 0 ? void 0 : _this$currentStream9.getAudioSegment(seg);\n    }\n  }, {\n    key: \"moveSegmentPointer\",\n    value: function moveSegmentPointer(pos) {\n      var _this$currentSegments5;\n      if (pos === null || pos === void 0)\n        pos = this._segmentPointer + 1;\n      this._segmentPointer = clamp(pos, -1, (_this$currentSegments5 = this.currentSegments) === null || _this$currentSegments5 === void 0 ? void 0 : _this$currentSegments5.length);\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.streams = [];\n      this.currentStream = null;\n      this.dvrWindow = 0;\n      this._segmentPointer = -1;\n    }\n  }, {\n    key: \"getSegmentByIndex\",\n    value: function getSegmentByIndex(index) {\n      var _this$currentSegments6;\n      return (_this$currentSegments6 = this.currentSegments) === null || _this$currentSegments6 === void 0 ? void 0 : _this$currentSegments6[index];\n    }\n  }, {\n    key: \"setNextSegmentByIndex\",\n    value: function setNextSegmentByIndex() {\n      var index = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n      this._segmentPointer = index - 1;\n    }\n  }, {\n    key: \"setNextSegmentBySN\",\n    value: function setNextSegmentBySN() {\n      var _this$currentSegments7;\n      var sn = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n      var preIndex = (_this$currentSegments7 = this.currentSegments) === null || _this$currentSegments7 === void 0 ? void 0 : _this$currentSegments7.findIndex(function(x) {\n        return x.sn === sn;\n      });\n      if (preIndex !== -1) {\n        this.setNextSegmentByIndex(preIndex + 1);\n      }\n      return preIndex;\n    }\n  }, {\n    key: \"findSegmentIndexByTime\",\n    value: function findSegmentIndexByTime(time) {\n      var segments = this.currentSegments;\n      if (segments) {\n        for (var i = 0, l = segments.length, seg; i < l; i++) {\n          seg = segments[i];\n          if (time >= seg.start && time < seg.end) {\n            return i;\n          }\n        }\n        var lastSegment = segments[segments.length - 1];\n        if (Math.abs(time - (lastSegment === null || lastSegment === void 0 ? void 0 : lastSegment.end)) < 0.2)\n          return segments.length - 1;\n      }\n    }\n  }, {\n    key: \"upsertPlaylist\",\n    value: function upsertPlaylist(playlist, audioPlaylist, subtitlePlaylist) {\n      var _this = this;\n      if (!playlist)\n        return;\n      if (playlist.isMaster) {\n        this.streams.length = playlist.streams.length;\n        playlist.streams.filter(function(x) {\n          return x.url;\n        }).forEach(function(stream2, i) {\n          if (_this.streams[i]) {\n            _this.streams[i].update(stream2);\n          } else {\n            _this.streams[i] = new Stream(stream2);\n          }\n        });\n        this.currentStream = this.streams[0];\n      } else if (Array.isArray(playlist.segments)) {\n        var stream = this.currentStream;\n        if (stream) {\n          stream.update(playlist, audioPlaylist, subtitlePlaylist);\n          var newSubtitleSegs = stream.updateSubtitle(subtitlePlaylist);\n          if (newSubtitleSegs) {\n            this.hls.emit(Event.SUBTITLE_SEGMENTS, {\n              list: newSubtitleSegs\n            });\n          }\n        } else {\n          this.reset();\n          this.currentStream = this.streams[0] = new Stream(playlist, audioPlaylist, subtitlePlaylist);\n        }\n      }\n      var currentStream = this.currentStream;\n      if (currentStream) {\n        if (this.hls.isLive && !this.dvrWindow) {\n          this.dvrWindow = this.currentSegments.reduce(function(a, c) {\n            a += c.duration;\n            return a;\n          }, 0);\n        }\n      }\n    }\n  }, {\n    key: \"updateSegmentsRanges\",\n    value: function updateSegmentsRanges(sn, start) {\n      var _this$currentSegments8;\n      var segs = (_this$currentSegments8 = this.currentSegments) === null || _this$currentSegments8 === void 0 ? void 0 : _this$currentSegments8.filter(function(x) {\n        return x.sn >= sn;\n      });\n      segs.forEach(function(s) {\n        s.start = start;\n        start = s.end;\n      });\n    }\n  }, {\n    key: \"switchSubtitle\",\n    value: function switchSubtitle(lang) {\n      var _this$currentStream10;\n      (_this$currentStream10 = this.currentStream) === null || _this$currentStream10 === void 0 ? void 0 : _this$currentStream10.switchSubtitle(lang);\n    }\n  }, {\n    key: \"clearOldSegment\",\n    value: function clearOldSegment() {\n      var maxPlaylistSize = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50;\n      var stream = this.currentStream;\n      if (!this.dvrWindow || !stream)\n        return;\n      var startTime = stream.endTime - this.dvrWindow;\n      if (startTime <= 0)\n        return;\n      var segments = stream.segments;\n      if (segments.length <= maxPlaylistSize)\n        return;\n      this._segmentPointer = stream.clearOldSegment(startTime, this._segmentPointer);\n    }\n  }, {\n    key: \"checkSegmentTrackChange\",\n    value: function checkSegmentTrackChange(cTime, nbSb) {\n      var index = this.findSegmentIndexByTime(cTime);\n      var seg = this.getSegmentByIndex(index);\n      if (!seg)\n        return;\n      if (!seg.hasAudio && !seg.hasVideo)\n        return;\n      if (nbSb !== 2 && seg.hasAudio && seg.hasVideo)\n        return seg;\n      if (seg.end - cTime > 0.3)\n        return;\n      var next = this.getSegmentByIndex(index + 1);\n      if (!next)\n        return;\n      if (!next.hasAudio && !next.hasVideo)\n        return;\n      if (next.hasAudio !== seg.hasAudio || next.hasVideo !== seg.hasVideo)\n        return next;\n    }\n  }, {\n    key: \"feedbackLiveEdge\",\n    value: function feedbackLiveEdge(segment, bufferEnd) {\n      var _this$lastSegment;\n      var segs = this.currentSegments;\n      if (!segs)\n        return;\n      var isLast = ((_this$lastSegment = this.lastSegment) === null || _this$lastSegment === void 0 ? void 0 : _this$lastSegment.sn) === segment.sn;\n      if (isLast) {\n        this.liveEdge = bufferEnd;\n        return;\n      }\n      this.updateSegmentsRanges(segment.sn + 1, bufferEnd);\n    }\n  }]);\n  return Playlist2;\n}();\nexport { Playlist };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck, defineProperty as _defineProperty, objectSpread2 as _objectSpread2, asyncToGenerator as _asyncToGenerator, regeneratorRuntime as _regeneratorRuntime, slicedToArray as _slicedToArray } from \"../../_virtual/_rollupPluginBabelHelpers.js\";\nimport { EVENT, StreamingError, BandwidthService, NetLoader } from \"xgplayer-streaming-shared\";\nvar SegmentLoader = /* @__PURE__ */ function() {\n  function SegmentLoader2(hls) {\n    var _this = this;\n    _classCallCheck(this, SegmentLoader2);\n    _defineProperty(this, \"error\", null);\n    _defineProperty(this, \"_emitOnLoaded\", function(res, url) {\n      var data = res.data, response = res.response, options = res.options;\n      var _ref = options || {}, firstByteTime = _ref.firstByteTime, startTime = _ref.startTime, endTime = _ref.endTime, contentLength = _ref.contentLength;\n      var time = endTime - startTime;\n      _this._bandwidthService.addRecord(contentLength || data.byteLength, time);\n      _this.hls.emit(EVENT.SPEED, {\n        time,\n        byteLength: contentLength,\n        url\n      });\n      _this.hls.emit(EVENT.LOAD_COMPLETE, {\n        url,\n        elapsed: time || 0\n      });\n      _this.hls.emit(EVENT.TTFB, {\n        url,\n        responseUrl: response.url,\n        elapsed: firstByteTime - startTime\n      });\n      _this.hls.emit(EVENT.LOAD_RESPONSE_HEADERS, {\n        headers: response.headers,\n        url\n      });\n    });\n    _defineProperty(this, \"_onLoaderRetry\", function(error, retryTime) {\n      _this.hls.emit(EVENT.LOAD_RETRY, {\n        error: StreamingError.network(error),\n        retryTime\n      });\n    });\n    this.hls = hls;\n    this._bandwidthService = new BandwidthService();\n    this._mapCache = {};\n    this._keyCache = {};\n    var _this$hls$config = this.hls.config, retryCount = _this$hls$config.retryCount, retryDelay = _this$hls$config.retryDelay, loadTimeout = _this$hls$config.loadTimeout, fetchOptions = _this$hls$config.fetchOptions;\n    this._segmentLoader = new NetLoader(_objectSpread2(_objectSpread2({}, fetchOptions), {}, {\n      responseType: \"arraybuffer\",\n      retry: retryCount,\n      retryDelay,\n      timeout: loadTimeout,\n      onRetryError: this._onLoaderRetry\n    }));\n    this._audioSegmentLoader = new NetLoader(_objectSpread2(_objectSpread2({}, fetchOptions), {}, {\n      responseType: \"arraybuffer\",\n      retry: retryCount,\n      retryDelay,\n      timeout: loadTimeout,\n      onRetryError: this._onLoaderRetry\n    }));\n    this._keyLoader = new NetLoader(_objectSpread2(_objectSpread2({}, fetchOptions), {}, {\n      responseType: \"arraybuffer\",\n      retry: retryCount,\n      retryDelay,\n      timeout: loadTimeout,\n      onRetryError: this._onLoaderRetry\n    }));\n  }\n  _createClass(SegmentLoader2, [{\n    key: \"speedInfo\",\n    value: function speedInfo() {\n      return {\n        speed: this._bandwidthService.getLatestSpeed(),\n        avgSpeed: this._bandwidthService.getAvgSpeed()\n      };\n    }\n  }, {\n    key: \"load\",\n    value: function load(seg, audioSeg, loadInit) {\n      var loadAudioInit = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : loadInit;\n      var toLoad = [];\n      if (seg)\n        toLoad[0] = this.loadVideoSegment(seg, loadInit);\n      if (audioSeg)\n        toLoad[1] = this.loadAudioSegment(audioSeg, loadAudioInit);\n      return Promise.all(toLoad);\n    }\n  }, {\n    key: \"loadVideoSegment\",\n    value: function loadVideoSegment(seg, loadInit) {\n      return this._loadSegment(this._segmentLoader, seg, loadInit);\n    }\n  }, {\n    key: \"loadAudioSegment\",\n    value: function loadAudioSegment(seg, loadInit) {\n      return this._loadSegment(this._audioSegmentLoader, seg, loadInit);\n    }\n  }, {\n    key: \"_loadSegment\",\n    value: function() {\n      var _loadSegment2 = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee(segLoader, seg, loadInit) {\n        var _this2 = this, _seg$key;\n        var map, key, keyIv, mapKey, mapKeyIv, toLoad, _seg$initSegment$key, mapUrl, _keyUrl, keyUrl, _yield$Promise$all, _yield$Promise$all2, s, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1)\n            switch (_context.prev = _context.next) {\n              case 0:\n                toLoad = [];\n                this.hls.emit(EVENT.LOAD_START, {\n                  url: seg.url\n                });\n                toLoad[0] = segLoader.load(seg.url);\n                if (loadInit && seg.initSegment) {\n                  mapUrl = seg.initSegment.url;\n                  map = this._mapCache[mapUrl];\n                  if (!map) {\n                    this.hls.emit(EVENT.LOAD_START, {\n                      url: mapUrl\n                    });\n                    toLoad[1] = segLoader.load(mapUrl).then(function(r) {\n                      if (r) {\n                        var l = Object.keys(_this2._mapCache);\n                        if (l > 30)\n                          _this2._mapCache = {};\n                        map = _this2._mapCache[mapUrl] = r.data;\n                        _this2._emitOnLoaded(r, mapUrl);\n                      }\n                    });\n                  }\n                  _keyUrl = (_seg$initSegment$key = seg.initSegment.key) === null || _seg$initSegment$key === void 0 ? void 0 : _seg$initSegment$key.url;\n                  if (_keyUrl) {\n                    mapKeyIv = seg.initSegment.key.iv;\n                    mapKey = this._keyCache[_keyUrl];\n                    if (!mapKey) {\n                      this.hls.emit(EVENT.LOAD_START, {\n                        url: _keyUrl\n                      });\n                      toLoad[2] = this._keyLoader.load(_keyUrl).then(function(r) {\n                        if (r) {\n                          mapKey = _this2._keyCache[_keyUrl] = r.data;\n                          _this2._emitOnLoaded(r, _keyUrl);\n                        }\n                      });\n                    }\n                  }\n                }\n                keyUrl = (_seg$key = seg.key) === null || _seg$key === void 0 ? void 0 : _seg$key.url;\n                if (keyUrl && seg.key.isSegmentEncrypted()) {\n                  keyIv = seg.key.iv;\n                  key = this._keyCache[keyUrl];\n                  if (!key) {\n                    this.hls.emit(EVENT.LOAD_START, {\n                      url: keyUrl\n                    });\n                    toLoad[3] = this._keyLoader.load(keyUrl).then(function(r) {\n                      if (r) {\n                        key = _this2._keyCache[keyUrl] = r.data;\n                        _this2._emitOnLoaded(r, keyUrl);\n                      }\n                    });\n                  }\n                }\n                _context.next = 8;\n                return Promise.all(toLoad);\n              case 8:\n                _yield$Promise$all = _context.sent;\n                _yield$Promise$all2 = _slicedToArray(_yield$Promise$all, 1);\n                s = _yield$Promise$all2[0];\n                if (s) {\n                  _context.next = 13;\n                  break;\n                }\n                return _context.abrupt(\"return\");\n              case 13:\n                data = s.data;\n                this._emitOnLoaded(s, seg.url);\n                return _context.abrupt(\"return\", {\n                  data,\n                  map,\n                  key,\n                  mapKey,\n                  keyIv,\n                  mapKeyIv\n                });\n              case 16:\n              case \"end\":\n                return _context.stop();\n            }\n        }, _callee, this);\n      }));\n      function _loadSegment(_x, _x2, _x3) {\n        return _loadSegment2.apply(this, arguments);\n      }\n      return _loadSegment;\n    }()\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.error = null;\n      this._mapCache = {};\n      this._keyCache = {};\n      this._bandwidthService.reset();\n    }\n  }, {\n    key: \"cancel\",\n    value: function() {\n      var _cancel = _asyncToGenerator(/* @__PURE__ */ _regeneratorRuntime().mark(function _callee2() {\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1)\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.next = 2;\n                return Promise.all([this._keyLoader.cancel(), this._segmentLoader.cancel(), this._audioSegmentLoader.cancel()]);\n              case 2:\n              case \"end\":\n                return _context2.stop();\n            }\n        }, _callee2, this);\n      }));\n      function cancel() {\n        return _cancel.apply(this, arguments);\n      }\n      return cancel;\n    }()\n  }]);\n  return SegmentLoader2;\n}();\nexport { SegmentLoader };\n", "import { createClass as _createClass, classCallCheck as _classCallCheck, defineProperty as _defineProperty, objectSpread2 as _objectSpread2 } from \"./_virtual/_rollupPluginBabelHelpers.js\";\nimport { Event } from \"./hls/constants.js\";\nvar PluginExtension = /* @__PURE__ */ function() {\n  function PluginExtension2(opts, plugin) {\n    var _this = this;\n    _classCallCheck(this, PluginExtension2);\n    _defineProperty(this, \"_opts\", null);\n    _defineProperty(this, \"_plugin\", null);\n    _defineProperty(this, \"_onLowDecode\", function() {\n      var _this$_plugin, _this$_plugin$player, _this$_plugin2, _this$_plugin2$player;\n      var _this$_opts = _this._opts, media = _this$_opts.media, innerDegrade = _this$_opts.innerDegrade;\n      (_this$_plugin = _this._plugin) === null || _this$_plugin === void 0 ? void 0 : (_this$_plugin$player = _this$_plugin.player) === null || _this$_plugin$player === void 0 ? void 0 : _this$_plugin$player.emit(\"lowdecode\", media.degradeInfo);\n      (_this$_plugin2 = _this._plugin) === null || _this$_plugin2 === void 0 ? void 0 : (_this$_plugin2$player = _this$_plugin2.player) === null || _this$_plugin2$player === void 0 ? void 0 : _this$_plugin2$player.emit(\"core_event\", _objectSpread2(_objectSpread2({}, media.degradeInfo), {}, {\n        eventName: Event.LOWDECODE\n      }));\n      if (innerDegrade === 1) {\n        _this._degrade(media.src);\n      }\n    });\n    _defineProperty(this, \"_degrade\", function(url) {\n      var player = _this._plugin.player;\n      var originVideo = player.video;\n      if (originVideo && originVideo.TAG !== \"MVideo\")\n        return;\n      var newVideo = player.video.degradeVideo;\n      player.video = newVideo;\n      originVideo.degrade(url);\n      if (url) {\n        player.config.url = url;\n      }\n      var firstChild = player.root.firstChild;\n      if (firstChild.TAG === \"MVideo\") {\n        player.root.replaceChild(newVideo, firstChild);\n      }\n      var hlsPlugin = _this._plugin.constructor.pluginName.toLowerCase();\n      player.unRegisterPlugin(hlsPlugin);\n      player.once(\"canplay\", function() {\n        player.play();\n      });\n    });\n    _defineProperty(this, \"forceDegradeToVideo\", function(url) {\n      var innerDegrade = _this._opts.innerDegrade;\n      if (innerDegrade === 1) {\n        _this._degrade(url);\n      }\n    });\n    this._opts = opts;\n    this._plugin = plugin;\n    this._init();\n  }\n  _createClass(PluginExtension2, [{\n    key: \"_init\",\n    value: function _init() {\n      var _this$_opts2 = this._opts, media = _this$_opts2.media, preloadTime = _this$_opts2.preloadTime, innerDegrade = _this$_opts2.innerDegrade, isLive = _this$_opts2.isLive;\n      if (!media)\n        return;\n      if (!isLive && media.setPlayMode) {\n        media.setPlayMode(\"VOD\");\n        return;\n      }\n      if (innerDegrade) {\n        media.setAttribute(\"innerdegrade\", innerDegrade);\n      }\n      if (preloadTime) {\n        media.setAttribute(\"preloadtime\", preloadTime);\n      }\n      this._bindEvents();\n    }\n  }, {\n    key: \"_bindEvents\",\n    value: function _bindEvents() {\n      var media = this._opts.media;\n      media.addEventListener(\"lowdecode\", this._onLowDecode);\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      var _this$_opts3, _this$_opts3$media;\n      (_this$_opts3 = this._opts) === null || _this$_opts3 === void 0 ? void 0 : (_this$_opts3$media = _this$_opts3.media) === null || _this$_opts3$media === void 0 ? void 0 : _this$_opts3$media.removeEventListener(\"lowdecode\", this._onLowDecode);\n      this._plugin = null;\n    }\n  }]);\n  return PluginExtension2;\n}();\nexport { PluginExtension as default };\n", "import { defineProperty as _defineProperty, typeof as _typeof, objectWithoutProperties as _objectWithoutProperties, inherits as _inherits, createSuper as _createSuper, createClass as _createClass, classCallCheck as _classCallCheck, assertThisInitialized as _assertThisInitialized, objectSpread2 as _objectSpread2 } from \"./_virtual/_rollupPluginBabelHelpers.js\";\nimport { BasePlugin, Events, Errors } from \"xgplayer\";\nimport { MSE, EVENT } from \"xgplayer-streaming-shared\";\nimport { Hls, logger } from \"./hls/index.js\";\nimport { Event } from \"./hls/constants.js\";\nimport PluginExtension from \"./plugin-extension.js\";\nvar _excluded = [\"currentTime\"];\nfunction parseSwitchUrlArgs(args, plugin) {\n  var player = plugin.player;\n  var curTime = player.currentTime;\n  var options = {\n    startTime: curTime\n  };\n  switch (_typeof(args)) {\n    case \"boolean\":\n      options.seamless = args;\n      break;\n    case \"object\": {\n      var currentTime = args.currentTime, rest = _objectWithoutProperties(args, _excluded);\n      Object.assign(options, rest);\n      if (typeof currentTime === \"number\") {\n        options.startTime = currentTime;\n      }\n      break;\n    }\n  }\n  return options;\n}\nvar HlsPlugin = /* @__PURE__ */ function(_BasePlugin) {\n  _inherits(HlsPlugin2, _BasePlugin);\n  var _super = _createSuper(HlsPlugin2);\n  function HlsPlugin2() {\n    var _this;\n    _classCallCheck(this, HlsPlugin2);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"logger\", logger);\n    _defineProperty(_assertThisInitialized(_this), \"hls\", null);\n    _defineProperty(_assertThisInitialized(_this), \"pluginExtension\", null);\n    _defineProperty(_assertThisInitialized(_this), \"getStats\", function() {\n      var _this$hls;\n      return (_this$hls = _this.hls) === null || _this$hls === void 0 ? void 0 : _this$hls.getStats();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_onSwitchSubtitle\", function(_ref) {\n      var _this$hls2;\n      var lang = _ref.lang;\n      (_this$hls2 = _this.hls) === null || _this$hls2 === void 0 ? void 0 : _this$hls2.switchSubtitleStream(lang);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"_keepPauseStatus\", function() {\n      var paused = _this.player.paused;\n      if (!paused)\n        return;\n      _this.player.once(\"canplay\", function() {\n        _this.player.pause();\n      });\n    });\n    return _this;\n  }\n  _createClass(HlsPlugin2, [{\n    key: \"core\",\n    get: function get() {\n      return this.hls;\n    }\n  }, {\n    key: \"version\",\n    get: function get() {\n      var _this$hls3;\n      return (_this$hls3 = this.hls) === null || _this$hls3 === void 0 ? void 0 : _this$hls3.version;\n    }\n  }, {\n    key: \"softDecode\",\n    get: function get() {\n      var _this$player, _this$player$config;\n      var mediaType = (_this$player = this.player) === null || _this$player === void 0 ? void 0 : (_this$player$config = _this$player.config) === null || _this$player$config === void 0 ? void 0 : _this$player$config.mediaType;\n      return !!mediaType && mediaType !== \"video\" && mediaType !== \"audio\";\n    }\n  }, {\n    key: \"beforePlayerInit\",\n    value: function beforePlayerInit() {\n      var _this2 = this;\n      var config = this.player.config;\n      var mediaElem = this.player.media || this.player.video;\n      var hlsOpts = config.hls || {};\n      if (!config.url && !config.__allowHlsEmptyUrl__ || !hlsOpts.preferMMS && MSE.isMMSOnly()) {\n        return;\n      }\n      if (this.hls)\n        this.hls.destroy();\n      var descriptor = Object.getOwnPropertyDescriptor(this.player, \"switchURL\");\n      if (!descriptor || descriptor.writable) {\n        this.player.switchURL = function(url, args) {\n          return new Promise(function(resolve, reject) {\n            var player = _this2.player, hls = _this2.hls;\n            if (hls) {\n              var _this2$player$config, _this2$player$config$;\n              var options = parseSwitchUrlArgs(args, _this2);\n              player.config.url = url;\n              hls.switchURL(url, options).then(function() {\n                return resolve(true);\n              }).catch(reject);\n              if (!options.seamless && (_this2$player$config = _this2.player.config) !== null && _this2$player$config !== void 0 && (_this2$player$config$ = _this2$player$config.hls) !== null && _this2$player$config$ !== void 0 && _this2$player$config$.keepStatusAfterSwitch) {\n                _this2._keepPauseStatus();\n              }\n            } else {\n              reject();\n            }\n          });\n        };\n      }\n      var onSwitchUrl = this.player.switchURL;\n      this.player.handleSource = false;\n      hlsOpts.innerDegrade = hlsOpts.innerDegrade || config.innerDegrade;\n      if (hlsOpts.disconnectTime === null || hlsOpts.disconnectTime === void 0)\n        hlsOpts.disconnectTime = 0;\n      this.hls = new Hls(_objectSpread2({\n        softDecode: this.softDecode,\n        isLive: config.isLive,\n        media: mediaElem,\n        startTime: config.startTime,\n        url: config.url\n      }, hlsOpts));\n      if (!this.softDecode) {\n        BasePlugin.defineGetterOrSetter(this.player, {\n          url: {\n            get: function get() {\n              var _this2$hls, _this2$hls$media;\n              return (_this2$hls = _this2.hls) === null || _this2$hls === void 0 ? void 0 : (_this2$hls$media = _this2$hls.media) === null || _this2$hls$media === void 0 ? void 0 : _this2$hls$media.src;\n            },\n            configurable: true\n          }\n        });\n      }\n      if (this.softDecode) {\n        this.pluginExtension = new PluginExtension(_objectSpread2({\n          isLive: config.isLive,\n          media: mediaElem\n        }, hlsOpts), this);\n        this.player.forceDegradeToVideo = function() {\n          var _this2$pluginExtensio;\n          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n            args[_key2] = arguments[_key2];\n          }\n          return (_this2$pluginExtensio = _this2.pluginExtension) === null || _this2$pluginExtensio === void 0 ? void 0 : _this2$pluginExtensio.forceDegradeToVideo.apply(_this2$pluginExtensio, args);\n        };\n      }\n      if (config.isLive) {\n        var _this$player2;\n        (_this$player2 = this.player) === null || _this$player2 === void 0 ? void 0 : _this$player2.useHooks(\"replay\", function() {\n          var _this2$hls2;\n          return (_this2$hls2 = _this2.hls) === null || _this2$hls2 === void 0 ? void 0 : _this2$hls2.replay();\n        });\n      }\n      this.on(Events.URL_CHANGE, onSwitchUrl);\n      this.on(Events.SWITCH_SUBTITLE || \"switch_subtitle\", this._onSwitchSubtitle);\n      this.on(Events.DESTROY, this.destroy.bind(this));\n      this._transError();\n      this._transCoreEvent(EVENT.TTFB);\n      this._transCoreEvent(EVENT.LOAD_START);\n      this._transCoreEvent(EVENT.LOAD_RESPONSE_HEADERS);\n      this._transCoreEvent(EVENT.LOAD_COMPLETE);\n      this._transCoreEvent(EVENT.LOAD_RETRY);\n      this._transCoreEvent(EVENT.SOURCEBUFFER_CREATED);\n      this._transCoreEvent(EVENT.MEDIASOURCE_OPENED);\n      this._transCoreEvent(EVENT.APPEND_BUFFER);\n      this._transCoreEvent(EVENT.REMOVE_BUFFER);\n      this._transCoreEvent(EVENT.BUFFEREOS);\n      this._transCoreEvent(EVENT.KEYFRAME);\n      this._transCoreEvent(EVENT.METADATA_PARSED);\n      this._transCoreEvent(EVENT.DEMUXED_TRACK);\n      this._transCoreEvent(EVENT.SEI);\n      this._transCoreEvent(EVENT.SEI_IN_TIME);\n      this._transCoreEvent(EVENT.SPEED);\n      this._transCoreEvent(EVENT.HLS_MANIFEST_LOADED);\n      this._transCoreEvent(EVENT.HLS_LEVEL_LOADED);\n      this._transCoreEvent(EVENT.STREAM_EXCEPTION);\n      this._transCoreEvent(EVENT.SWITCH_URL_SUCCESS);\n      this._transCoreEvent(EVENT.SWITCH_URL_FAILED);\n      this._transCoreEvent(Event.NO_AUDIO_TRACK);\n      this._transCoreEvent(Event.STREAM_PARSED);\n      this._transCoreEvent(Event.SUBTITLE_SEGMENTS);\n      this._transCoreEvent(Event.SUBTITLE_PLAYLIST);\n      this._transCoreEvent(Event.APPEND_COST);\n      if (config.url) {\n        this.hls.load(config.url, {\n          reuseMse: true\n        }).catch(function(e) {\n        });\n      }\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      var _this$pluginExtension;\n      if (this.hls) {\n        this.hls.destroy();\n        this.hls = null;\n      }\n      (_this$pluginExtension = this.pluginExtension) === null || _this$pluginExtension === void 0 ? void 0 : _this$pluginExtension.destroy();\n      this.pluginExtension = null;\n    }\n  }, {\n    key: \"_transError\",\n    value: function _transError() {\n      var _this3 = this;\n      this.hls.on(Event.ERROR, function(err) {\n        if (_this3.player) {\n          _this3.player.emit(Events.ERROR, new Errors(_this3.player, err));\n        }\n      });\n    }\n  }, {\n    key: \"_transCoreEvent\",\n    value: function _transCoreEvent(eventName) {\n      var _this4 = this;\n      this.hls.on(eventName, function(e) {\n        if (_this4.player) {\n          _this4.player.emit(\"core_event\", _objectSpread2(_objectSpread2({}, e), {}, {\n            eventName\n          }));\n          if (eventName === EVENT.SEI_IN_TIME && _this4.hls.hasSubtitle) {\n            _this4._emitSeiPaylodTime(e);\n          }\n        }\n      });\n    }\n  }, {\n    key: \"_emitSeiPaylodTime\",\n    value: function _emitSeiPaylodTime(e) {\n      try {\n        var seiJson = JSON.parse(Array.from(e.data.payload).map(function(x) {\n          return String.fromCharCode(x);\n        }).join(\"\").slice(0, -1));\n        if (!seiJson[\"rtmp_dts\"])\n          return;\n        this.player.emit(\"core_event\", {\n          eventName: Event.SEI_PAYLOAD_TIME,\n          time: seiJson[\"rtmp_dts\"]\n        });\n      } catch (e2) {\n      }\n    }\n  }], [{\n    key: \"pluginName\",\n    get: function get() {\n      return \"hls\";\n    }\n  }, {\n    key: \"isSupported\",\n    value: function isSupported(mediaType, codec) {\n      return Hls.isSupported(mediaType, codec);\n    }\n  }]);\n  return HlsPlugin2;\n}(BasePlugin);\n_defineProperty(HlsPlugin, \"Hls\", Hls);\n_defineProperty(HlsPlugin, \"EVENT\", Event);\nexport { HlsPlugin, parseSwitchUrlArgs };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE,UAAW,UAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG,EAAG,SAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE,GAAI,SAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA,UAChE,SAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB,EAAG,SAAQ,UAAU,IAAI,OAAO;AAAA,UAC1D,QAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB,EAAG,QAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI,EAAG,OAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC,SAAU,QAAO,CAAC;AACvB,UAAI,SAAS,GAAI,QAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC,UAAW,QAAO;AACvB,UAAI,UAAU,GAAI,QAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU,KAAM,MAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE,KAAM,MAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC,KAAM,MAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,qBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,cAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO,OAAQ,MAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA,YACpE,YAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG,EAAG,YAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AC/UA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,KAAK,QAAQ,MAAM,OAAO,eAAe,OAAO,UAAU,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AACtG,MAAI,QAAQ,IAAI;AACd,QAAI,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,KAAK,MAAM,KAAK;AAC/C,QAAI;AACF,UAAI,MAAM,KAAK,GAAG,KAAK,GAAG,GAAG,MAAM,MAAM,GAAG;AAC1C,YAAI,OAAO,EAAE,MAAM;AACjB;AACF,aAAK;AAAA,MACP;AACE,eAAO,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,WAAW,IAAI,KAAK;AACvF;AAAA,IACN,SAAS,KAAK;AACZ,WAAK,MAAM,KAAK;AAAA,IAClB,UAAE;AACA,UAAI;AACF,YAAI,CAAC,MAAM,QAAQ,GAAG,WAAW,KAAK,GAAG,OAAO,GAAG,OAAO,EAAE,MAAM;AAChE;AAAA,MACJ,UAAE;AACA,YAAI;AACF,gBAAM;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAS,KAAK;AACxD,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAS,KAAK;AAC1D,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAS,KAAK;AAChK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,sBAAsB;AAC7B,wBAAsB,WAAW;AAC/B,WAAO;AAAA,EACT;AACA,MAAI,UAAU,CAAC,GAAG,KAAK,OAAO,WAAW,SAAS,GAAG,gBAAgB,iBAAiB,OAAO,kBAAkB,SAAS,KAAK,KAAK,MAAM;AACtI,QAAI,GAAG,IAAI,KAAK;AAAA,EAClB,GAAG,UAAU,cAAc,OAAO,SAAS,SAAS,CAAC,GAAG,iBAAiB,QAAQ,YAAY,cAAc,sBAAsB,QAAQ,iBAAiB,mBAAmB,oBAAoB,QAAQ,eAAe;AACxN,WAAS,OAAO,KAAK,KAAK,OAAO;AAC/B,WAAO,OAAO,eAAe,KAAK,KAAK;AAAA,MACrC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,IAAI,GAAG;AAAA,EACb;AACA,MAAI;AACF,WAAO,CAAC,GAAG,EAAE;AAAA,EACf,SAAS,KAAK;AACZ,aAAS,SAAS,KAAK,KAAK,OAAO;AACjC,aAAO,IAAI,GAAG,IAAI;AAAA,IACpB;AAAA,EACF;AACA,WAAS,KAAK,SAAS,SAAS,MAAM,aAAa;AACjD,QAAI,iBAAiB,WAAW,QAAQ,qBAAqB,YAAY,UAAU,WAAW,YAAY,OAAO,OAAO,eAAe,SAAS,GAAG,UAAU,IAAI,QAAQ,eAAe,CAAC,CAAC;AAC1L,WAAO,eAAe,WAAW,WAAW;AAAA,MAC1C,OAAO,iBAAiB,SAAS,MAAM,OAAO;AAAA,IAChD,CAAC,GAAG;AAAA,EACN;AACA,WAAS,SAAS,IAAI,KAAK,KAAK;AAC9B,QAAI;AACF,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK,GAAG,KAAK,KAAK,GAAG;AAAA,MACvB;AAAA,IACF,SAAS,KAAK;AACZ,aAAO;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACA,UAAQ,OAAO;AACf,MAAI,mBAAmB,CAAC;AACxB,WAAS,YAAY;AAAA,EACrB;AACA,WAAS,oBAAoB;AAAA,EAC7B;AACA,WAAS,6BAA6B;AAAA,EACtC;AACA,MAAI,oBAAoB,CAAC;AACzB,SAAO,mBAAmB,gBAAgB,WAAW;AACnD,WAAO;AAAA,EACT,CAAC;AACD,MAAI,WAAW,OAAO,gBAAgB,0BAA0B,YAAY,SAAS,SAAS,OAAO,CAAC,CAAC,CAAC,CAAC;AACzG,6BAA2B,4BAA4B,MAAM,OAAO,KAAK,yBAAyB,cAAc,MAAM,oBAAoB;AAC1I,MAAI,KAAK,2BAA2B,YAAY,UAAU,YAAY,OAAO,OAAO,iBAAiB;AACrG,WAAS,sBAAsB,WAAW;AACxC,KAAC,QAAQ,SAAS,QAAQ,EAAE,QAAQ,SAAS,QAAQ;AACnD,aAAO,WAAW,QAAQ,SAAS,KAAK;AACtC,eAAO,KAAK,QAAQ,QAAQ,GAAG;AAAA,MACjC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,WAAS,cAAc,WAAW,aAAa;AAC7C,aAAS,OAAO,QAAQ,KAAK,SAAS,QAAQ;AAC5C,UAAI,SAAS,SAAS,UAAU,MAAM,GAAG,WAAW,GAAG;AACvD,UAAI,YAAY,OAAO,MAAM;AAC3B,YAAI,SAAS,OAAO,KAAK,QAAQ,OAAO;AACxC,eAAO,SAAS,YAAY,OAAO,SAAS,OAAO,KAAK,OAAO,SAAS,IAAI,YAAY,QAAQ,MAAM,OAAO,EAAE,KAAK,SAAS,QAAQ;AACnI,iBAAO,QAAQ,QAAQ,SAAS,MAAM;AAAA,QACxC,GAAG,SAAS,KAAK;AACf,iBAAO,SAAS,KAAK,SAAS,MAAM;AAAA,QACtC,CAAC,IAAI,YAAY,QAAQ,KAAK,EAAE,KAAK,SAAS,WAAW;AACvD,iBAAO,QAAQ,WAAW,QAAQ,MAAM;AAAA,QAC1C,GAAG,SAAS,OAAO;AACjB,iBAAO,OAAO,SAAS,OAAO,SAAS,MAAM;AAAA,QAC/C,CAAC;AAAA,MACH;AACA,aAAO,OAAO,GAAG;AAAA,IACnB;AACA,QAAI;AACJ,mBAAe,MAAM,WAAW;AAAA,MAC9B,OAAO,SAAS,QAAQ,KAAK;AAC3B,iBAAS,6BAA6B;AACpC,iBAAO,IAAI,YAAY,SAAS,SAAS,QAAQ;AAC/C,mBAAO,QAAQ,KAAK,SAAS,MAAM;AAAA,UACrC,CAAC;AAAA,QACH;AACA,eAAO,kBAAkB,kBAAkB,gBAAgB,KAAK,4BAA4B,0BAA0B,IAAI,2BAA2B;AAAA,MACvJ;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,iBAAiB,SAAS,MAAM,SAAS;AAChD,QAAI,QAAQ;AACZ,WAAO,SAAS,QAAQ,KAAK;AAC3B,UAAI,gBAAgB;AAClB,cAAM,IAAI,MAAM,8BAA8B;AAChD,UAAI,gBAAgB,OAAO;AACzB,YAAI,YAAY;AACd,gBAAM;AACR,eAAO,WAAW;AAAA,MACpB;AACA,WAAK,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAS;AACnD,YAAI,WAAW,QAAQ;AACvB,YAAI,UAAU;AACZ,cAAI,iBAAiB,oBAAoB,UAAU,OAAO;AAC1D,cAAI,gBAAgB;AAClB,gBAAI,mBAAmB;AACrB;AACF,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,WAAW,QAAQ;AACrB,kBAAQ,OAAO,QAAQ,QAAQ,QAAQ;AAAA,iBAChC,YAAY,QAAQ,QAAQ;AACnC,cAAI,qBAAqB;AACvB,kBAAM,QAAQ,aAAa,QAAQ;AACrC,kBAAQ,kBAAkB,QAAQ,GAAG;AAAA,QACvC;AACE,uBAAa,QAAQ,UAAU,QAAQ,OAAO,UAAU,QAAQ,GAAG;AACrE,gBAAQ;AACR,YAAI,SAAS,SAAS,SAAS,MAAM,OAAO;AAC5C,YAAI,aAAa,OAAO,MAAM;AAC5B,cAAI,QAAQ,QAAQ,OAAO,cAAc,kBAAkB,OAAO,QAAQ;AACxE;AACF,iBAAO;AAAA,YACL,OAAO,OAAO;AAAA,YACd,MAAM,QAAQ;AAAA,UAChB;AAAA,QACF;AACA,oBAAY,OAAO,SAAS,QAAQ,aAAa,QAAQ,SAAS,SAAS,QAAQ,MAAM,OAAO;AAAA,MAClG;AAAA,IACF;AAAA,EACF;AACA,WAAS,oBAAoB,UAAU,SAAS;AAC9C,QAAI,aAAa,QAAQ,QAAQ,SAAS,SAAS,SAAS,UAAU;AACtE,QAAI,WAAW;AACb,aAAO,QAAQ,WAAW,MAAM,YAAY,cAAc,SAAS,SAAS,WAAW,QAAQ,SAAS,UAAU,QAAQ,MAAM,QAAQ,oBAAoB,UAAU,OAAO,GAAG,YAAY,QAAQ,WAAW,aAAa,eAAe,QAAQ,SAAS,SAAS,QAAQ,MAAM,IAAI,UAAU,sCAAsC,aAAa,UAAU,IAAI;AACpW,QAAI,SAAS,SAAS,QAAQ,SAAS,UAAU,QAAQ,GAAG;AAC5D,QAAI,YAAY,OAAO;AACrB,aAAO,QAAQ,SAAS,SAAS,QAAQ,MAAM,OAAO,KAAK,QAAQ,WAAW,MAAM;AACtF,QAAI,OAAO,OAAO;AAClB,WAAO,OAAO,KAAK,QAAQ,QAAQ,SAAS,UAAU,IAAI,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,aAAa,QAAQ,WAAW,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAS,QAAQ,WAAW,MAAM,oBAAoB,QAAQ,QAAQ,SAAS,SAAS,QAAQ,MAAM,IAAI,UAAU,kCAAkC,GAAG,QAAQ,WAAW,MAAM;AAAA,EAClW;AACA,WAAS,aAAa,MAAM;AAC1B,QAAI,QAAQ;AAAA,MACV,QAAQ,KAAK,CAAC;AAAA,IAChB;AACA,SAAK,SAAS,MAAM,WAAW,KAAK,CAAC,IAAI,KAAK,SAAS,MAAM,aAAa,KAAK,CAAC,GAAG,MAAM,WAAW,KAAK,CAAC,IAAI,KAAK,WAAW,KAAK,KAAK;AAAA,EAC1I;AACA,WAAS,cAAc,OAAO;AAC5B,QAAI,SAAS,MAAM,cAAc,CAAC;AAClC,WAAO,OAAO,UAAU,OAAO,OAAO,KAAK,MAAM,aAAa;AAAA,EAChE;AACA,WAAS,QAAQ,aAAa;AAC5B,SAAK,aAAa,CAAC;AAAA,MACjB,QAAQ;AAAA,IACV,CAAC,GAAG,YAAY,QAAQ,cAAc,IAAI,GAAG,KAAK,MAAM,IAAI;AAAA,EAC9D;AACA,WAAS,OAAO,UAAU;AACxB,QAAI,UAAU;AACZ,UAAI,iBAAiB,SAAS,cAAc;AAC5C,UAAI;AACF,eAAO,eAAe,KAAK,QAAQ;AACrC,UAAI,cAAc,OAAO,SAAS;AAChC,eAAO;AACT,UAAI,CAAC,MAAM,SAAS,MAAM,GAAG;AAC3B,YAAI,IAAI,IAAI,OAAO,SAAS,QAAQ;AAClC,iBAAO,EAAE,IAAI,SAAS;AACpB,gBAAI,OAAO,KAAK,UAAU,CAAC;AACzB,qBAAO,MAAM,QAAQ,SAAS,CAAC,GAAG,MAAM,OAAO,OAAO;AAC1D,iBAAO,MAAM,QAAQ,QAAQ,MAAM,OAAO,MAAM;AAAA,QAClD;AACA,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AACA,WAAS,aAAa;AACpB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO,kBAAkB,YAAY,4BAA4B,eAAe,IAAI,eAAe;AAAA,IACjG,OAAO;AAAA,IACP,cAAc;AAAA,EAChB,CAAC,GAAG,eAAe,4BAA4B,eAAe;AAAA,IAC5D,OAAO;AAAA,IACP,cAAc;AAAA,EAChB,CAAC,GAAG,kBAAkB,cAAc,OAAO,4BAA4B,mBAAmB,mBAAmB,GAAG,QAAQ,sBAAsB,SAAS,QAAQ;AAC7J,QAAI,OAAO,cAAc,OAAO,UAAU,OAAO;AACjD,WAAO,CAAC,CAAC,SAAS,SAAS,qBAAqB,yBAAyB,KAAK,eAAe,KAAK;AAAA,EACpG,GAAG,QAAQ,OAAO,SAAS,QAAQ;AACjC,WAAO,OAAO,iBAAiB,OAAO,eAAe,QAAQ,0BAA0B,KAAK,OAAO,YAAY,4BAA4B,OAAO,QAAQ,mBAAmB,mBAAmB,IAAI,OAAO,YAAY,OAAO,OAAO,EAAE,GAAG;AAAA,EAC5O,GAAG,QAAQ,QAAQ,SAAS,KAAK;AAC/B,WAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,EACF,GAAG,sBAAsB,cAAc,SAAS,GAAG,OAAO,cAAc,WAAW,qBAAqB,WAAW;AACjH,WAAO;AAAA,EACT,CAAC,GAAG,QAAQ,gBAAgB,eAAe,QAAQ,QAAQ,SAAS,SAAS,SAAS,MAAM,aAAa,aAAa;AACpH,eAAW,gBAAgB,cAAc;AACzC,QAAI,OAAO,IAAI,cAAc,KAAK,SAAS,SAAS,MAAM,WAAW,GAAG,WAAW;AACnF,WAAO,QAAQ,oBAAoB,OAAO,IAAI,OAAO,KAAK,KAAK,EAAE,KAAK,SAAS,QAAQ;AACrF,aAAO,OAAO,OAAO,OAAO,QAAQ,KAAK,KAAK;AAAA,IAChD,CAAC;AAAA,EACH,GAAG,sBAAsB,EAAE,GAAG,OAAO,IAAI,mBAAmB,WAAW,GAAG,OAAO,IAAI,gBAAgB,WAAW;AAC9G,WAAO;AAAA,EACT,CAAC,GAAG,OAAO,IAAI,YAAY,WAAW;AACpC,WAAO;AAAA,EACT,CAAC,GAAG,QAAQ,OAAO,SAAS,KAAK;AAC/B,QAAI,SAAS,OAAO,GAAG,GAAG,OAAO,CAAC;AAClC,aAAS,OAAO;AACd,WAAK,KAAK,GAAG;AACf,WAAO,KAAK,QAAQ,GAAG,SAAS,OAAO;AACrC,aAAO,KAAK,UAAU;AACpB,YAAI,OAAO,KAAK,IAAI;AACpB,YAAI,QAAQ;AACV,iBAAO,KAAK,QAAQ,MAAM,KAAK,OAAO,OAAO;AAAA,MACjD;AACA,aAAO,KAAK,OAAO,MAAM;AAAA,IAC3B;AAAA,EACF,GAAG,QAAQ,SAAS,QAAQ,QAAQ,YAAY;AAAA,IAC9C,aAAa;AAAA,IACb,OAAO,SAAS,eAAe;AAC7B,UAAI,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,QAAQ,KAAK,OAAO,OAAO,KAAK,WAAW,MAAM,KAAK,SAAS,QAAQ,KAAK,MAAM,QAAQ,KAAK,WAAW,QAAQ,aAAa,GAAG,CAAC;AAC5L,iBAAS,QAAQ;AACf,kBAAQ,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,IAAI;AAAA,IACnG;AAAA,IACA,MAAM,WAAW;AACf,WAAK,OAAO;AACZ,UAAI,aAAa,KAAK,WAAW,CAAC,EAAE;AACpC,UAAI,YAAY,WAAW;AACzB,cAAM,WAAW;AACnB,aAAO,KAAK;AAAA,IACd;AAAA,IACA,mBAAmB,SAAS,WAAW;AACrC,UAAI,KAAK;AACP,cAAM;AACR,UAAI,UAAU;AACd,eAAS,OAAO,KAAK,QAAQ;AAC3B,eAAO,OAAO,OAAO,SAAS,OAAO,MAAM,WAAW,QAAQ,OAAO,KAAK,WAAW,QAAQ,SAAS,QAAQ,QAAQ,MAAM,SAAS,CAAC,CAAC;AAAA,MACzI;AACA,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC,GAAG,SAAS,MAAM;AAC/C,YAAI,WAAW,MAAM;AACnB,iBAAO,OAAO,KAAK;AACrB,YAAI,MAAM,UAAU,KAAK,MAAM;AAC7B,cAAI,WAAW,OAAO,KAAK,OAAO,UAAU,GAAG,aAAa,OAAO,KAAK,OAAO,YAAY;AAC3F,cAAI,YAAY,YAAY;AAC1B,gBAAI,KAAK,OAAO,MAAM;AACpB,qBAAO,OAAO,MAAM,UAAU,IAAI;AACpC,gBAAI,KAAK,OAAO,MAAM;AACpB,qBAAO,OAAO,MAAM,UAAU;AAAA,UAClC,WAAW,UAAU;AACnB,gBAAI,KAAK,OAAO,MAAM;AACpB,qBAAO,OAAO,MAAM,UAAU,IAAI;AAAA,UACtC,OAAO;AACL,gBAAI,CAAC;AACH,oBAAM,IAAI,MAAM,wCAAwC;AAC1D,gBAAI,KAAK,OAAO,MAAM;AACpB,qBAAO,OAAO,MAAM,UAAU;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ,SAAS,MAAM,KAAK;AAC1B,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,YAAI,MAAM,UAAU,KAAK,QAAQ,OAAO,KAAK,OAAO,YAAY,KAAK,KAAK,OAAO,MAAM,YAAY;AACjG,cAAI,eAAe;AACnB;AAAA,QACF;AAAA,MACF;AACA,uBAAiB,YAAY,QAAQ,eAAe,SAAS,aAAa,UAAU,OAAO,OAAO,aAAa,eAAe,eAAe;AAC7I,UAAI,SAAS,eAAe,aAAa,aAAa,CAAC;AACvD,aAAO,OAAO,OAAO,MAAM,OAAO,MAAM,KAAK,gBAAgB,KAAK,SAAS,QAAQ,KAAK,OAAO,aAAa,YAAY,oBAAoB,KAAK,SAAS,MAAM;AAAA,IAClK;AAAA,IACA,UAAU,SAAS,QAAQ,UAAU;AACnC,UAAI,YAAY,OAAO;AACrB,cAAM,OAAO;AACf,aAAO,YAAY,OAAO,QAAQ,eAAe,OAAO,OAAO,KAAK,OAAO,OAAO,MAAM,aAAa,OAAO,QAAQ,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,KAAK,SAAS,UAAU,KAAK,OAAO,SAAS,aAAa,OAAO,QAAQ,aAAa,KAAK,OAAO,WAAW;AAAA,IACtQ;AAAA,IACA,QAAQ,SAAS,YAAY;AAC3B,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,YAAI,MAAM,eAAe;AACvB,iBAAO,KAAK,SAAS,MAAM,YAAY,MAAM,QAAQ,GAAG,cAAc,KAAK,GAAG;AAAA,MAClF;AAAA,IACF;AAAA,IACA,OAAO,SAAS,QAAQ;AACtB,eAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,YAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,YAAI,MAAM,WAAW,QAAQ;AAC3B,cAAI,SAAS,MAAM;AACnB,cAAI,YAAY,OAAO,MAAM;AAC3B,gBAAI,SAAS,OAAO;AACpB,0BAAc,KAAK;AAAA,UACrB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AAAA,IACA,eAAe,SAAS,UAAU,YAAY,SAAS;AACrD,aAAO,KAAK,WAAW;AAAA,QACrB,UAAU,OAAO,QAAQ;AAAA,QACzB;AAAA,QACA;AAAA,MACF,GAAG,WAAW,KAAK,WAAW,KAAK,MAAM,SAAS;AAAA,IACpD;AAAA,EACF,GAAG;AACL;AACA,SAAS,QAAQ,KAAK;AACpB;AACA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAS,MAAM;AAClG,WAAO,OAAO;AAAA,EAChB,IAAI,SAAS,MAAM;AACjB,WAAO,QAAQ,cAAc,OAAO,UAAU,KAAK,gBAAgB,UAAU,SAAS,OAAO,YAAY,WAAW,OAAO;AAAA,EAC7H,GAAG,QAAQ,GAAG;AAChB;AACA,SAAS,mBAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,KAAK,KAAK;AACzE,MAAI;AACF,QAAI,OAAO,IAAI,GAAG,EAAE,GAAG;AACvB,QAAI,QAAQ,KAAK;AAAA,EACnB,SAAS,OAAO;AACd,WAAO,KAAK;AACZ;AAAA,EACF;AACA,MAAI,KAAK,MAAM;AACb,YAAQ,KAAK;AAAA,EACf,OAAO;AACL,YAAQ,QAAQ,KAAK,EAAE,KAAK,OAAO,MAAM;AAAA,EAC3C;AACF;AACA,SAAS,kBAAkB,IAAI;AAC7B,SAAO,WAAW;AAChB,QAAI,OAAO,MAAM,OAAO;AACxB,WAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,UAAI,MAAM,GAAG,MAAM,MAAM,IAAI;AAC7B,eAAS,MAAM,OAAO;AACpB,2BAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,QAAQ,KAAK;AAAA,MACvE;AACA,eAAS,OAAO,KAAK;AACnB,2BAAmB,KAAK,SAAS,QAAQ,OAAO,QAAQ,SAAS,GAAG;AAAA,MACtE;AACA,YAAM,MAAM;AAAA,IACd,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AACA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW;AACb,iBAAW,WAAW;AACxB,WAAO,eAAe,QAAQ,eAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAC1E;AACF;AACA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI;AACF,sBAAkB,YAAY,WAAW,UAAU;AACrD,MAAI;AACF,sBAAkB,aAAa,WAAW;AAC5C,SAAO,eAAe,aAAa,aAAa;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,QAAM,eAAe,GAAG;AACxB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AACA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO,eAAe,UAAU,aAAa;AAAA,IAC3C,UAAU;AAAA,EACZ,CAAC;AACD,MAAI;AACF,oBAAgB,UAAU,UAAU;AACxC;AACA,SAAS,gBAAgB,GAAG;AAC1B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAS,iBAAiB,IAAI;AACrG,WAAO,GAAG,aAAa,OAAO,eAAe,EAAE;AAAA,EACjD;AACA,SAAO,gBAAgB,CAAC;AAC1B;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAS,iBAAiB,IAAI,IAAI;AACzG,OAAG,YAAY;AACf,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;AACA,SAAS,4BAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ;AAC7C,WAAO;AACT,MAAI,QAAQ,UAAU;AACpB,WAAO;AACT,MAAI,OAAO,UAAU;AACnB,WAAO;AACT,MAAI;AACF,YAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAW;AAAA,IACzE,CAAC,CAAC;AACF,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AACA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU;AACZ,WAAO,CAAC;AACV,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AACT,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK;AAC3B;AACF,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU;AACZ,WAAO,CAAC;AACV,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAC3D,MAAI,KAAK;AACT,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAC1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK;AAC3B;AACF,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG;AACzD;AACF,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,MAAM;AACpC,MAAI,SAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AACA,SAAO;AACT;AACA,SAAS,2BAA2B,MAAM,MAAM;AAC9C,MAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT,WAAW,SAAS,QAAQ;AAC1B,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAChF;AACA,SAAO,uBAAuB,IAAI;AACpC;AACA,SAAS,aAAa,SAAS;AAC7B,MAAI,4BAA4B,0BAA0B;AAC1D,SAAO,SAAS,uBAAuB;AACrC,QAAI,QAAQ,gBAAgB,OAAO,GAAG;AACtC,QAAI,2BAA2B;AAC7B,UAAI,YAAY,gBAAgB,IAAI,EAAE;AACtC,eAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,IACxD,OAAO;AACL,eAAS,MAAM,MAAM,MAAM,SAAS;AAAA,IACtC;AACA,WAAO,2BAA2B,MAAM,MAAM;AAAA,EAChD;AACF;AACA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AACA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AACA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG;AACnB,WAAO,kBAAkB,GAAG;AAChC;AACA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG;AACnB,WAAO;AACX;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK;AAC1F,WAAO,MAAM,KAAK,IAAI;AAC1B;AACA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC;AACH;AACF,MAAI,OAAO,MAAM;AACf,WAAO,kBAAkB,GAAG,MAAM;AACpC,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE;AACtB,QAAI,EAAE,YAAY;AACpB,MAAI,MAAM,SAAS,MAAM;AACvB,WAAO,MAAM,KAAK,CAAC;AACrB,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AACxE,WAAO,kBAAkB,GAAG,MAAM;AACtC;AACA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI;AAC3B,UAAM,IAAI;AACZ,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK;AAC9C,SAAK,CAAC,IAAI,IAAI,CAAC;AACjB,SAAO;AACT;AACA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AACA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;AACA,SAAS,aAAa,OAAO,MAAM;AACjC,MAAI,OAAO,UAAU,YAAY,UAAU;AACzC,WAAO;AACT,MAAI,OAAO,MAAM,OAAO,WAAW;AACnC,MAAI,SAAS,QAAQ;AACnB,QAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAC5C,QAAI,OAAO,QAAQ;AACjB,aAAO;AACT,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AACpD;AACA,SAAS,eAAe,KAAK;AAC3B,MAAI,MAAM,aAAa,KAAK,QAAQ;AACpC,SAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AACnD;;;ACzlBA,2BAAyB;;;ACCzB,IAAI,YAA4B,WAAW;AACzC,WAAS,aAAa;AACpB,oBAAgB,MAAM,UAAU;AAChC,QAAI,SAAS,OAAO,UAAU,OAAO;AACrC,SAAK,SAAS,WAAW,OAAO,UAAU,OAAO;AACjD,SAAK,oBAAoB;AAAA,EAC3B;AACA,eAAa,YAAY,CAAC;AAAA,IACxB,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,UAAI;AACJ,WAAK,wBAAwB,KAAK,uBAAuB,QAAQ,0BAA0B,UAAU,sBAAsB,SAAS;AAClI,aAAK,kBAAkB,QAAQ;AAAA,MACjC;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ,OAAO,OAAO;AACpC,UAAI,CAAC,SAAS,CAAC;AACb;AACF,UAAI,MAAM,CAAC;AACX,UAAI,OAAO;AACT,YAAI,CAAC,IAAI,KAAK,gBAAgB,KAAK;AAAA,MACrC;AACA,UAAI,OAAO;AACT,YAAI,CAAC,IAAI,KAAK,gBAAgB,KAAK;AAAA,MACrC;AACA,aAAO,QAAQ,IAAI,GAAG;AAAA,IACxB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,mBAAmB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,KAAK;AACzG,YAAI;AACJ,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,uBAAO,IAAI;AACX,oBAAI,CAAC,IAAI,KAAK;AACZ,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,aAAa,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,cACvD,KAAK;AACH,uBAAO,UAAU;AAAA,cACnB,KAAK;AACH,oBAAI,IAAI,KAAK;AACX,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,UAAU,IAAI;AAAA,cACxC,KAAK;AACH,uBAAO,UAAU,OAAO,UAAU,iBAAiB,IAAI,KAAK,IAAI,CAAC;AAAA,cACnE,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,gBAAgB,KAAK;AAC5B,eAAO,iBAAiB,MAAM,MAAM,SAAS;AAAA,MAC/C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,gBAAgB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,MAAM,KAAK,IAAI;AAChH,YAAI;AACJ,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,CAAC,KAAK,mBAAmB;AAC3B,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,kBAAkB,QAAQ,MAAM,KAAK,EAAE;AAAA,cACrD,KAAK;AACH,uBAAO,UAAU,OAAO,UAAU,UAAU,IAAI;AAAA,cAClD,KAAK;AACH,oBAAI,KAAK,QAAQ;AACf,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,sBAAM,IAAI,MAAM,uBAAuB;AAAA,cACzC,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO,UAAU,OAAO,KAAK;AAAA,kBACvC,MAAM;AAAA,gBACR,GAAG,OAAO,CAAC,WAAW,SAAS,CAAC;AAAA,cAClC,KAAK;AACH,yBAAS,UAAU;AACnB,0BAAU,KAAK;AACf,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO,QAAQ;AAAA,kBACzB,MAAM;AAAA,kBACN;AAAA,gBACF,GAAG,QAAQ,IAAI;AAAA,cACjB,KAAK;AACH,0BAAU,KAAK,UAAU;AACzB,uBAAO,UAAU,OAAO,UAAU,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;AAAA,cAClE,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,aAAa,KAAK,KAAK,KAAK;AACnC,eAAO,cAAc,MAAM,MAAM,SAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;ACtHF,IAAI,QAAQ,eAAe,eAAe,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,EACxD,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,aAAa;AACf,CAAC;;;ACLD,IAAI,SAAS,IAAI,OAAO,YAAY;AACpC,IAAI,aAA6B,WAAW;AAC1C,WAAS,YAAY,KAAK,OAAO,WAAW,aAAa;AACvD,oBAAgB,MAAM,WAAW;AACjC,oBAAgB,MAAM,kBAAkB,EAAE;AAC1C,SAAK,MAAM;AACX,SAAK,WAAW,QAAQ,IAAI,YAAY,IAAI,IAAI,UAAU,MAAM,MAAM,MAAM,WAAW;AACvF,SAAK,SAAS;AACd,QAAI;AACF,WAAK,WAAW,IAAI,YAAY,KAAK,SAAS,YAAY,KAAK,SAAS,UAAU;AAAA,EACtF;AACA,eAAa,aAAa,CAAC;AAAA,IACzB,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,YAAY,YAAY,eAAe,YAAY,WAAW,UAAU;AAC/F,UAAI,UAAU,KAAK;AACnB,UAAI;AACF,YAAI,KAAK,QAAQ;AACf,kBAAQ,MAAM,YAAY,UAAU;AAAA,QACtC,OAAO;AACL,kBAAQ,YAAY,iBAAiB,YAAY,UAAU,GAAG,eAAe,YAAY,SAAS;AAAA,QACpG;AAAA,MACF,SAAS,OAAO;AACd,cAAM,IAAI,eAAe,IAAI,OAAO,IAAI,UAAU,KAAK,KAAK;AAAA,MAC9D;AACA,UAAI,aAAa,QAAQ,YAAY,aAAa,QAAQ,YAAY,gBAAgB,QAAQ;AAC9F,UAAI,UAAU;AAAA,QACZ,OAAO,WAAW;AAAA,QAClB,WAAW,WAAW;AAAA,QACtB,UAAU,WAAW,WAAW,WAAW;AAAA,QAC3C,UAAU,WAAW,WAAW,WAAW;AAAA,QAC3C,UAAU,WAAW,kBAAkB,WAAW;AAAA,MACpD;AACA,UAAI,UAAU;AAAA,QACZ,OAAO,WAAW;AAAA,QAClB,WAAW,WAAW;AAAA,QACtB,UAAU,WAAW,WAAW,WAAW;AAAA,QAC3C,UAAU,WAAW,WAAW,WAAW;AAAA,QAC3C,UAAU,WAAW,kBAAkB,WAAW;AAAA,MACpD;AACA,UAAI,QAAQ,GAAG,OAAO,WAAW,OAAO,GAAG,EAAE,OAAO,WAAW,OAAO,GAAG,EAAE,OAAO,WAAW,QAAQ,GAAG,EAAE,OAAO,WAAW,OAAO,GAAG,EAAE,OAAO,WAAW,MAAM;AAChK,UAAI,UAAU,KAAK,gBAAgB;AACjC,aAAK,iBAAiB;AACtB,mBAAW;AAAA,MACb;AACA,WAAK,YAAY,YAAY,YAAY,eAAe,iBAAiB,QAAQ;AACjF,WAAK,IAAI,KAAK,MAAM,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI,KAAK,UAAU;AACjB,YAAI,YAAY,KAAK,IAAI,UAAU,CAAC,KAAK,IAAI,OAAO,eAAe;AACjE,qBAAW,WAAW,KAAK,IAAI,gBAAgB,WAAW;AAC1D,qBAAW,WAAW,KAAK,IAAI,gBAAgB,WAAW;AAAA,QAC5D;AACA,YAAI;AACF,cAAI,uBAAuB,KAAK,SAAS,MAAM,QAAQ,GAAG,mBAAmB,qBAAqB,kBAAkB,eAAe,qBAAqB,cAAc,mBAAmB,qBAAqB,kBAAkB,eAAe,qBAAqB;AACpQ,cAAI,IAAI,iBAAiB,kBAAkB,YAAY;AACvD,cAAI,IAAI,iBAAiB,kBAAkB,YAAY;AACvD,iBAAO,CAAC,IAAI,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,YAC1D,MAAM;AAAA,UACR,CAAC,IAAI,QAAQ,IAAI,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,YAC/D,MAAM;AAAA,UACR,CAAC,IAAI,MAAM;AAAA,QACb,SAAS,OAAO;AACd,gBAAM,IAAI,eAAe,IAAI,OAAO,IAAI,UAAU,MAAM,KAAK;AAAA,QAC/D;AAAA,MACF,OAAO;AACL,eAAO,CAAC,YAAY,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,YAAY,YAAY,eAAe,eAAe;AAChF,UAAI,QAAQ;AACZ,UAAI,SAAS,CAAC,YAAY,UAAU;AACpC,UAAI,QAAQ,kBAAkB,OAAO,aAAa;AAClD,aAAO,QAAQ,SAAS,OAAO;AAC7B,YAAI;AACJ,aAAK,iBAAiB,MAAM,aAAa,QAAQ,mBAAmB,UAAU,eAAe,QAAQ;AACnG,mBAAS,KAAK,OAAO,MAAM,QAAQ,QAAQ,GAAG,EAAE,OAAO,MAAM,SAAS,UAAU,QAAQ,UAAU,SAAS,wCAAwC,EAAE,QAAQ,MAAM,WAAW,MAAM,WAAW,QAAQ,CAAC,GAAG,GAAG,EAAE,QAAQ,MAAM,WAAW,MAAM,WAAW,QAAQ,CAAC,GAAG,GAAG,EAAE,QAAQ,MAAM,kBAAkB,MAAM,WAAW,QAAQ,CAAC,CAAC;AAAA,QACxU;AACA,YAAI,iBAAiB,MAAM,MAAM,GAAG;AAClC,gBAAM,IAAI,KAAK,MAAM,iBAAiB;AAAA,YACpC,MAAM,MAAM;AAAA,YACZ;AAAA,YACA,MAAM,eAAe;AAAA,cACnB,OAAO,MAAM;AAAA,cACb,WAAW,MAAM;AAAA,cACjB,SAAS,MAAM;AAAA,YACjB,GAAG,MAAM,SAAS,UAAU,QAAQ;AAAA,cAClC,OAAO,MAAM;AAAA,cACb,QAAQ,MAAM;AAAA,cACd,UAAU,MAAM;AAAA,YAClB,IAAI;AAAA,cACF,OAAO,MAAM;AAAA,cACb,cAAc,MAAM;AAAA,cACpB,YAAY,MAAM;AAAA,YACpB,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,MAAM,KAAK;AAClB,iBAAW,SAAS,QAAQ,SAAS,MAAM;AACzC,YAAI;AACJ,gBAAQ,KAAK,MAAM;AAAA,UACjB,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,UACF,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,UACF,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,QACJ;AACA,YAAI;AACF,gBAAM,IAAI,KAAK,MAAM,kBAAkB,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YAClF;AAAA,UACF,CAAC,CAAC;AACJ,eAAO,KAAK,mBAAmB,IAAI;AAAA,MACrC,CAAC;AACD,iBAAW,SAAS,QAAQ,SAAS,MAAM;AACzC,YAAI;AACJ,gBAAQ,KAAK,MAAM;AAAA,UACjB,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,UACF,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,UACF,KAAK,YAAY;AACf,mBAAO,MAAM;AACb;AAAA,QACJ;AACA,YAAI;AACF,gBAAM,IAAI,KAAK,MAAM,kBAAkB,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YAClF;AAAA,UACF,CAAC,CAAC;AACJ,eAAO,KAAK,mBAAmB,IAAI;AAAA,MACrC,CAAC;AACD,iBAAW,QAAQ,QAAQ,SAAS,QAAQ;AAC1C,YAAI,OAAO,UAAU;AACnB,gBAAM,IAAI,KAAK,MAAM,UAAU;AAAA,YAC7B,KAAK,OAAO;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,oBAAc,WAAW,QAAQ,SAAS,KAAK;AAC7C,cAAM,IAAI,KAAK,MAAM,KAAK,eAAe,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;AAAA,UACpE,WAAW,IAAI,YAAY;AAAA,UAC3B,KAAK;AAAA,YACH,MAAM,IAAI,KAAK;AAAA,YACf,SAAS,IAAI,KAAK;AAAA,YAClB,KAAK,IAAI;AAAA,UACX;AAAA,QACF,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;AC9JF,IAAI,YAAY,CAAC,MAAM;AAAvB,IAA0B,aAAa,CAAC,MAAM;AAC9C,IAAIC,UAAS,IAAI,OAAO,eAAe;AACvC,IAAI,gBAAgC,WAAW;AAC7C,WAAS,eAAe,KAAK;AAC3B,QAAI,QAAQ;AACZ,oBAAgB,MAAM,cAAc;AACpC,oBAAgB,MAAM,cAAc,IAAI,UAAU,CAAC;AACnD,oBAAgB,MAAM,eAAe,IAAI;AACzC,oBAAgB,MAAM,QAAQ,IAAI;AAClC,oBAAgB,MAAM,cAAc,IAAI;AACxC,oBAAgB,MAAM,kBAAkB,KAAK;AAC7C,oBAAgB,MAAM,oBAAoB,IAAI;AAC9C,oBAAgB,MAAM,iBAAiB,KAAK;AAC5C,SAAK,MAAM;AACX,QAAI,IAAI,OAAO,YAAY;AACzB,WAAK,aAAa,IAAI;AAAA,IACxB,OAAO;AACL,WAAK,OAAO,IAAI,IAAI,MAAM;AAAA,QACxB,WAAW,IAAI,OAAO;AAAA,MACxB,CAAC;AACD,UAAI,IAAI,OAAO,KAAK;AAClB,aAAK,KAAK,UAAU,IAAI,KAAK,EAAE,KAAK,SAAS,GAAG;AAC9C,gBAAM,IAAI,KAAK,MAAM,oBAAoB,CAAC;AAAA,QAC5C,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,IAAI,OAAO,WAAW;AACxB,WAAK,WAAW,oBAAoB,IAAI,OAAO;AAAA,IACjD;AAAA,EACF;AACA,eAAa,gBAAgB,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI,mBAAmB,uBAAuB;AAC9C,cAAQ,oBAAoB,KAAK,iBAAiB,QAAQ,sBAAsB,SAAS,UAAU,wBAAwB,kBAAkB,cAAc,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,YAAY,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB;AAAA,IACzV;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,UAAI,GAAG,aAAa,KAAK,UAAU,QAAQ,eAAe,UAAU,WAAW;AAC7E,eAAO;AACT,aAAO,OAAO,KAAK,KAAK,KAAK,aAAa,EAAE;AAAA,IAC9C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,cAAc,KAAK,UAAU,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,IAC7F;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,cAAc,KAAK,UAAU,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,IAC7F;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,cAAc,KAAK,UAAU,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,IAC7F;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,kBAAkB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,QAAQ,UAAU;AAC5G,eAAO,oBAAoB,EAAE,KAAK,SAAS,SAAS,UAAU;AAC5D,iBAAO;AACL,oBAAQ,SAAS,OAAO,SAAS,MAAM;AAAA,cACrC,KAAK;AACH,gBAAAA,QAAO,MAAM,mBAAmB,QAAQ;AACxC,oBAAI,CAAC,KAAK,MAAM;AACd,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,oBAAI,KAAK,KAAK,UAAU;AACtB,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,yBAAS,OAAO;AAChB,uBAAO,KAAK,KAAK,KAAK;AAAA,cACxB,KAAK;AACH,yBAAS,OAAO;AAChB,uBAAO,KAAK,KAAK,eAAe,QAAQ;AAAA,cAC1C,KAAK;AACH,yBAAS,OAAO;AAChB;AAAA,cACF,KAAK;AACH,oBAAI,KAAK,YAAY;AACnB,uBAAK,WAAW,WAAW;AAAA,gBAC7B;AAAA,cACF,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,SAAS,KAAK;AAAA,YACzB;AAAA,QACJ,GAAG,SAAS,IAAI;AAAA,MAClB,CAAC,CAAC;AACF,eAAS,eAAe,IAAI;AAC1B,eAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,MAC9C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,YAAY,YAAY,YAAY,YAAY;AAC3E,UAAI,KAAK;AACP;AACF,UAAI,QAAQ,cAAc;AAC1B,UAAI,CAAC;AACH;AACF,UAAI,UAAU,MAAM,KAAK,GAAG;AAC1B,YAAI,CAAC,KAAK;AACR,eAAK,cAAc,IAAI,WAAW,KAAK,KAAK,OAAO,CAAC,KAAK,YAAY,KAAK,IAAI,OAAO,WAAW;AAAA,MACpG,WAAW,UAAU,MAAM,KAAK,GAAG;AACjC,YAAI,KAAK,YAAY;AACnB,cAAI,CAAC,KAAK;AACR,iBAAK,cAAc,IAAI,WAAW,KAAK,KAAK,MAAM,MAAM,KAAK,IAAI,OAAO,WAAW;AAAA,QACvF,OAAO;AACL,eAAK,gBAAgB;AACrB,cAAI,MAAM;AACV,cAAI,cAAc,CAAC,YAAY;AAC7B,sBAAU,QAAQ,YAAY,CAAC,QAAQ,MAAM,CAAC,EAAE,QAAQ,SAAS,GAAG;AAClE,kBAAI,MAAM,UAAU,QAAQ,EAAE,MAAM,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC,EAAE,CAAC;AAC/E,kBAAI,KAAK;AACP,oBAAI,IAAI,UAAU,KAAK,GAAG,EAAE,QAAQ,CAAC;AACrC,oBAAI,GAAG;AACL,sBAAI,EAAE,MAAM;AACV,iCAAa,EAAE,KAAK,SAAS;AAAA,kBAC/B,WAAW,EAAE,MAAM;AACjB,iCAAa,EAAE,KAAK;AAAA,kBACtB,WAAW,EAAE,cAAc,EAAE,MAAM;AACjC,wBAAI;AACJ,mCAAe,UAAU,EAAE,UAAU,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU;AAC7F,0BAAM;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AACA,cAAI,cAAc,CAAC,YAAY;AAC7B,sBAAU,QAAQ,YAAY,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC,EAAE,QAAQ,SAAS,MAAM;AACrG,kBAAI,IAAI,UAAU,KAAK,IAAI,EAAE,QAAQ,CAAC;AACtC,kBAAI,KAAK,EAAE;AACT,6BAAa,EAAE,KAAK;AAAA,YACxB,CAAC;AAAA,UACH;AACA,cAAI,cAAc,CAAC;AACjB,yBAAa;AACf,cAAI,cAAc,CAAC;AACjB,yBAAa;AACf,cAAI,KAAK;AACP,0BAAc,KAAK,OAAO,UAAU;AACpC,yBAAa;AAAA,UACf;AACA,eAAK,iBAAiB,YAAY,UAAU;AAAA,QAC9C;AAAA,MACF,OAAO;AACL,cAAM,IAAI,eAAe,IAAI,OAAO,MAAM,MAAM,MAAM,oBAAoB;AAAA,MAC5E;AACA,UAAI,KAAK;AACP,aAAK,iBAAiB;AAAA,IAC1B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,gBAAgB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,SAAS,cAAc,YAAY,YAAY,eAAe,YAAY,WAAW;AACtL,YAAI,SAAS;AACb,YAAI,aAAa,GAAG,UAAU,uBAAuB,wBAAwB,OAAO,OAAO,eAAe,KAAK,IAAI,WAAW,WAAW,WAAW;AACpJ,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,EAAE,EAAE,eAAe,QAAQ,eAAe,UAAU,WAAW,WAAW,EAAE,eAAe,QAAQ,eAAe,UAAU,WAAW,UAAU;AACnJ,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,8BAAc,SAAS,eAAe;AACpC,sBAAI;AACJ,uBAAK,aAAa,OAAO,SAAS,QAAQ,eAAe,UAAU,WAAW,MAAM;AAClF,wBAAI;AACJ,qBAAC,cAAc,OAAO,SAAS,QAAQ,gBAAgB,SAAS,SAAS,YAAY,KAAK,MAAM,eAAe;AAAA,sBAC7G,OAAO,QAAQ;AAAA,sBACf,KAAK,QAAQ;AAAA,oBACf,CAAC;AAAA,kBACH;AAAA,gBACF;AACA,oBAAI,CAAC,KAAK,eAAe;AACvB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,oBAAI,CAAC;AACL,oBAAI;AACF,oBAAE,KAAK,KAAK,KAAK,OAAO,IAAI,OAAO,UAAU,CAAC;AAChD,oBAAI;AACF,oBAAE,KAAK,KAAK,KAAK,OAAO,IAAI,OAAO,UAAU,CAAC;AAChD,uBAAO,UAAU,OAAO,UAAU,QAAQ,IAAI,CAAC,EAAE,KAAK,WAAW,CAAC;AAAA,cACpE,KAAK;AACH,2BAAW,KAAK,oBAAoB;AACpC,wCAAwB,KAAK,YAAY,SAAS,YAAY,YAAY,UAAU,YAAY,WAAW,KAAK,oBAAoB,aAAa,GAAG,yBAAyB,eAAe,uBAAuB,CAAC,GAAG,QAAQ,uBAAuB,CAAC,GAAG,QAAQ,uBAAuB,CAAC;AAC1R,oBAAI,cAAc,cAAc;AAC9B,mCAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,cAAc,OAAO,IAAI;AAAA,gBACpG;AACA,oBAAI,cAAc,SAAS;AACzB,8BAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,MAAM,KAAK;AAAA,gBACrF;AACA,oBAAI,CAAC,cAAc;AACjB,8BAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK;AAAA,gBAC1F;AACA,oBAAI,SAAS,CAAC,OAAO;AACnB,uBAAK,IAAI,KAAK,MAAM,cAAc;AAAA,gBACpC;AACA,oBAAI,CAAC,KAAK,YAAY;AACpB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,qBAAK,WAAW,aAAa,OAAO,KAAK;AACzC,qBAAK,mBAAmB;AACxB,4BAAY;AACZ,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,oBAAI,CAAC,KAAK,MAAM;AACd,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,gCAAgB,CAAC,KAAK;AACtB,oBAAI,eAAe;AACjB,uBAAK,iBAAiB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,OAAO,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,KAAK;AAAA,gBAC5I;AACA,qBAAK,mBAAmB;AACxB,sBAAM,KAAK;AACX,qBAAK,CAAC;AACN,oBAAI,YAAY,CAAC,eAAe;AAC9B,uBAAK,mBAAmB,OAAO,KAAK,EAAE,QAAQ,SAAS,MAAM;AAC3D,2BAAO,GAAG,KAAK,IAAI;AAAA,kBACrB,CAAC;AAAA,gBACH;AACA,oBAAI,OAAO;AACT,8BAAY,MAAM,MAAM,YAAY,yBAAyB,OAAO,SAAS;AAC7E,qBAAG,KAAK,IAAI,OAAO,IAAI,OAAO,WAAW,SAAS,CAAC;AAAA,gBACrD;AACA,oBAAI,OAAO;AACT,8BAAY,MAAM,MAAM,YAAY,yBAAyB,OAAO,UAAU;AAC9E,qBAAG,KAAK,IAAI,OAAO,IAAI,OAAO,WAAW,SAAS,CAAC;AAAA,gBACrD;AACA,uBAAO,UAAU,OAAO,UAAU,QAAQ,IAAI,EAAE,EAAE,KAAK,WAAW,CAAC;AAAA,cACrE,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACvD,eAAO,cAAc,MAAM,MAAM,SAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,gBAAgB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AACnG,YAAI,SAAS;AACb,YAAI,OAAO,KAAK,OAAO,SAAS;AAChC,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,wBAAQ,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI;AAChE,sBAAM,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI;AAC9D,wBAAQ,KAAK,IAAI;AACjB,oBAAI,EAAE,CAAC,KAAK,QAAQ,CAAC,SAAS,QAAQ,KAAK,MAAM,SAAS,SAAS,KAAK,KAAK,WAAW;AACtF,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,uBAAO,UAAU,OAAO,UAAU,KAAK,KAAK,YAAY,OAAO,GAAG,EAAE,KAAK,WAAW;AAClF,yBAAO,OAAO,IAAI,KAAK,MAAM,eAAe;AAAA,oBAC1C;AAAA,oBACA;AAAA,oBACA,WAAW;AAAA,kBACb,CAAC;AAAA,gBACH,CAAC,CAAC;AAAA,cACJ,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,eAAe;AACtB,eAAO,cAAc,MAAM,MAAM,SAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,eAAe,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,cAAc;AAC9G,YAAI,OAAO,aAAa,WAAW;AACnC,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,wBAAQ,KAAK,IAAI;AACjB,oBAAI,EAAE,CAAC,KAAK,QAAQ,CAAC,SAAS,CAAC,gBAAgB,eAAe,IAAI;AAChE,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,8BAAc,MAAM;AACpB,4BAAY,cAAc;AAC1B,oBAAI,EAAE,aAAa,IAAI;AACrB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,wBAAQ,OAAO,MAAM,OAAO,IAAI,KAAK,CAAC;AACtC,oBAAI,EAAE,QAAQ,KAAK,YAAY;AAC7B,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,uBAAO,UAAU,OAAO,UAAU,KAAK,aAAa,GAAG,SAAS,CAAC;AAAA,cACnE,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,YAAY,KAAK;AACxB,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,kBAAkB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AACrG,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,CAAC,KAAK,MAAM;AACd,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,UAAU,KAAK,KAAK,eAAe,CAAC;AAAA,cAC9D,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,iBAAiB;AACxB,eAAO,gBAAgB,MAAM,MAAM,SAAS;AAAA,MAC9C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc,OAAO,OAAO;AAC1C,aAAO,KAAK,WAAW,QAAQ,OAAO,KAAK;AAAA,IAC7C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,SAAS,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC5F,YAAI,UAAU,SAAS;AACvB,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,2BAAW,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI;AACnE,oBAAI,EAAE,KAAK,QAAQ,CAAC,WAAW;AAC7B,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,qBAAK,cAAc;AACnB,qBAAK,iBAAiB;AACtB,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,YAAY;AAAA,cAC/B,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,UAAU,KAAK,IAAI,KAAK;AAAA,cAC3C,KAAK;AACH,qBAAK,mBAAmB;AACxB,qBAAK,gBAAgB;AAAA,cACvB,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,QAAQ;AACf,eAAO,OAAO,MAAM,MAAM,SAAS;AAAA,MACrC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,eAAe,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAClG,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,CAAC,KAAK,MAAM;AACd,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,oBAAI,CAAC,KAAK,gBAAgB;AACxB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,YAAY;AAAA,cAC/B,KAAK;AACH,qBAAK,IAAI,KAAK,MAAM,SAAS;AAAA,cAC/B,KAAK;AACH,oBAAI,KAAK,YAAY;AACnB,uBAAK,WAAW,YAAY;AAAA,gBAC9B;AAAA,cACF,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,cAAc;AACrB,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,wBAAwB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,OAAO,KAAK;AACrH,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,KAAK;AACP,uBAAK,KAAK,qBAAqB,OAAO,GAAG;AAAA,cAC7C,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,qBAAqB,MAAM,MAAM;AACxC,eAAO,sBAAsB,MAAM,MAAM,SAAS;AAAA,MACpD;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,eAAe,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAClG,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI,CAAC,KAAK,MAAM;AACd,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,YAAY;AAAA,cAC/B,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,cAAc;AACrB,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,WAAW,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,YAAY;AAC/F,YAAI;AACJ,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,iBAAC,mBAAmB,KAAK,gBAAgB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,QAAQ;AACjH,2BAAW,OAAO;AAClB,uBAAO,KAAK,YAAY;AAAA,cAC1B,KAAK;AACH,qBAAK,aAAa;AAClB,qBAAK,OAAO;AACZ,qBAAK,aAAa;AAAA,cACpB,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,IAAI;AAAA,MACpB,CAAC,CAAC;AACF,eAAS,UAAU;AACjB,eAAO,SAAS,MAAM,MAAM,SAAS;AAAA,MACvC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB,YAAY,YAAY;AACvD,MAAAA,QAAO,MAAM,iCAAiC,OAAO,YAAY,eAAe,EAAE,OAAO,UAAU,CAAC;AACpG,UAAI,MAAM,KAAK;AACf,UAAI,CAAC;AACH;AACF,UAAI,YAAY;AACd,YAAI,aAAa,IAAI,OAAO,oBAAoB,OAAO,UAAU,CAAC;AAClE,aAAK,iBAAiB;AAAA,MACxB;AACA,UAAI,YAAY;AACd,YAAI,aAAa,IAAI,OAAO,oBAAoB,OAAO,UAAU,CAAC;AAClE,aAAK,iBAAiB;AAAA,MACxB;AACA,WAAK,IAAI,KAAK,MAAM,oBAAoB;AAAA,IAC1C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB,OAAO,OAAO;AAC/C,UAAI,QAAQ,CAAC;AACb,UAAI,MAAM,KAAK;AACf,UAAI,YAAY,CAAC;AAAA,QACf,MAAM,IAAI;AAAA,QACV,QAAQ,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,MAC9D,GAAG;AAAA,QACD,MAAM,IAAI;AAAA,QACV,QAAQ,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,MAC9D,CAAC;AACD,gBAAU,OAAO,SAAS,MAAM;AAC9B,eAAO,CAAC,CAAC,KAAK;AAAA,MAChB,CAAC,EAAE,QAAQ,SAAS,MAAM;AACxB,YAAI,OAAO,KAAK,MAAM,SAAS,KAAK;AACpC,YAAI,eAAe,IAAI,gBAAgB,IAAI;AAC3C,YAAI,cAAc;AAChB,cAAI,QAAQ,OAAO,MAAM,GAAG,EAAE,CAAC;AAC/B,cAAI,CAAC,IAAI,OAAO,OAAO,IAAI,EAAE,KAAK,aAAa,QAAQ,GAAG;AACxD,kBAAM,KAAK,IAAI,WAAW,MAAM,GAAG,OAAO,MAAM,cAAc,EAAE,OAAO,MAAM,CAAC,CAAC;AAAA,UACjF;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB;AAC/B,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI;AACJ,UAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI,IAAI;AACrF,cAAQ,cAAc,KAAK,UAAU,QAAQ,gBAAgB,SAAS,SAAS,YAAY,OAAO,SAAS;AAAA,IAC7G;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;AC7jBF,SAAS,UAAU,KAAK;AACtB,MAAI,SAAS,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,UAAU,SAAS,cAAc,OAAO;AACnG,SAAO,eAAe,eAAe;AAAA,IACnC,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,0BAA0B;AAAA,IAC1B,WAAW;AAAA,IACX,cAAc,CAAC;AAAA,IACf,sBAAsB;AAAA,IACtB,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,IACrB;AAAA,EACF,GAAG,GAAG,GAAG,CAAC,GAAG;AAAA,IACX;AAAA,EACF,CAAC;AACH;;;AC/BA,IAAI,iBAAiC,aAAa,SAAS,kBAAkB;AAC3E,kBAAgB,MAAM,eAAe;AACrC,kBAAgB,MAAM,WAAW,CAAC;AAClC,kBAAgB,MAAM,WAAW,CAAC,CAAC;AACnC,kBAAgB,MAAM,YAAY,IAAI;AACxC,CAAC;AACD,IAAI,YAAY;AAAA,EACd,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,gBAAgB;AAClB;AACA,IAAI,aAAa;AAAA,EACf,WAAW;AAAA,EACX,UAAU,CAAC,iDAAiD,gCAAgC;AAAA,EAC5F,UAAU,CAAC,iDAAiD,sBAAsB,cAAc;AAAA,EAChG,WAAW,CAAC,iDAAiD,yBAAyB;AACxF;AACA,SAAS,UAAU,KAAK;AACtB,MAAI,MAAM,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,GAAG;AACzB,YAAM,IAAI,OAAO,UAAU,IAAI,CAAC,CAAC,CAAC;AAAA,IACpC,OAAO;AACL,UAAI,KAAK,IAAI,CAAC,CAAC;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,cAA8B,aAAa,SAAS,eAAe;AACrE,kBAAgB,MAAM,YAAY;AAClC,kBAAgB,MAAM,MAAM,CAAC;AAC7B,kBAAgB,MAAM,OAAO,EAAE;AAC/B,kBAAgB,MAAM,WAAW,KAAK;AACtC,kBAAgB,MAAM,cAAc,KAAK;AACzC,kBAAgB,MAAM,UAAU,KAAK;AACrC,kBAAgB,MAAM,SAAS,EAAE;AACjC,kBAAgB,MAAM,QAAQ,EAAE;AAChC,kBAAgB,MAAM,QAAQ,EAAE;AAChC,kBAAgB,MAAM,YAAY,CAAC,CAAC;AACpC,kBAAgB,MAAM,SAAS,CAAC;AAClC,CAAC;AACD,IAAI,cAA8B,SAAS,cAAc;AACvD,YAAU,cAAc,YAAY;AACpC,MAAI,SAAS,aAAa,YAAY;AACtC,WAAS,eAAe;AACtB,QAAI;AACJ,oBAAgB,MAAM,YAAY;AAClC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AACA,YAAQ,OAAO,KAAK,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC;AACrD,oBAAgB,uBAAuB,KAAK,GAAG,aAAa,UAAU,KAAK;AAC3E,oBAAgB,uBAAuB,KAAK,GAAG,YAAY,CAAC;AAC5D,WAAO;AAAA,EACT;AACA,SAAO,aAAa,YAAY;AAClC,EAAE,WAAW;AACb,IAAI,iBAAiC,SAAS,eAAe;AAC3D,YAAU,iBAAiB,aAAa;AACxC,MAAI,UAAU,aAAa,eAAe;AAC1C,WAAS,kBAAkB;AACzB,QAAI;AACJ,oBAAgB,MAAM,eAAe;AACrC,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AACA,aAAS,QAAQ,KAAK,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC;AACxD,oBAAgB,uBAAuB,MAAM,GAAG,aAAa,UAAU,QAAQ;AAC/E,WAAO;AAAA,EACT;AACA,SAAO,aAAa,eAAe;AACrC,EAAE,WAAW;AACb,IAAI,eAA+B,aAAa,SAAS,gBAAgB;AACvE,kBAAgB,MAAM,aAAa;AACnC,kBAAgB,MAAM,MAAM,CAAC;AAC7B,kBAAgB,MAAM,WAAW,CAAC;AAClC,kBAAgB,MAAM,SAAS,CAAC;AAChC,kBAAgB,MAAM,UAAU,CAAC;AACjC,kBAAgB,MAAM,QAAQ,EAAE;AAChC,kBAAgB,MAAM,OAAO,EAAE;AAC/B,kBAAgB,MAAM,cAAc,EAAE;AACtC,kBAAgB,MAAM,cAAc,EAAE;AACtC,kBAAgB,MAAM,aAAa,EAAE;AACrC,kBAAgB,MAAM,cAAc,EAAE;AACtC,kBAAgB,MAAM,gBAAgB,CAAC,CAAC;AACxC,kBAAgB,MAAM,mBAAmB,CAAC,CAAC;AAC3C,kBAAgB,MAAM,wBAAwB,CAAC,CAAC;AAClD,CAAC;AACD,IAAI,gBAAgC,aAAa,SAAS,iBAAiB;AACzE,kBAAgB,MAAM,cAAc;AACpC,kBAAgB,MAAM,WAAW,CAAC;AAClC,kBAAgB,MAAM,OAAO,EAAE;AAC/B,kBAAgB,MAAM,QAAQ,EAAE;AAChC,kBAAgB,MAAM,WAAW,CAAC;AAClC,kBAAgB,MAAM,SAAS,CAAC;AAChC,kBAAgB,MAAM,WAAW,CAAC;AAClC,kBAAgB,MAAM,SAAS,CAAC;AAChC,kBAAgB,MAAM,iBAAiB,CAAC;AACxC,kBAAgB,MAAM,kBAAkB,CAAC;AACzC,kBAAgB,MAAM,sBAAsB,CAAC;AAC7C,kBAAgB,MAAM,gBAAgB,CAAC;AACvC,kBAAgB,MAAM,qBAAqB,KAAK;AAChD,kBAAgB,MAAM,mBAAmB,CAAC;AAC1C,kBAAgB,MAAM,kBAAkB,KAAK;AAC7C,kBAAgB,MAAM,gBAAgB,CAAC;AACvC,kBAAgB,MAAM,QAAQ,IAAI;AAClC,kBAAgB,MAAM,cAAc,KAAK;AACzC,kBAAgB,MAAM,gBAAgB,CAAC;AACvC,kBAAgB,MAAM,YAAY,CAAC,CAAC;AACpC,kBAAgB,MAAM,cAAc,CAAC,CAAC;AACtC,kBAAgB,MAAM,mBAAmB,CAAC;AAC5C,CAAC;AACD,IAAI,eAA+B,WAAW;AAC5C,WAAS,cAAc,WAAW;AAChC,oBAAgB,MAAM,aAAa;AACnC,oBAAgB,MAAM,MAAM,CAAC;AAC7B,oBAAgB,MAAM,MAAM,CAAC;AAC7B,oBAAgB,MAAM,OAAO,EAAE;AAC/B,oBAAgB,MAAM,aAAa,EAAE;AACrC,oBAAgB,MAAM,SAAS,EAAE;AACjC,oBAAgB,MAAM,SAAS,CAAC;AAChC,oBAAgB,MAAM,YAAY,CAAC;AACnC,oBAAgB,MAAM,YAAY,EAAE;AACpC,oBAAgB,MAAM,OAAO,IAAI;AACjC,oBAAgB,MAAM,aAAa,IAAI;AACvC,oBAAgB,MAAM,iBAAiB,KAAK;AAC5C,oBAAgB,MAAM,eAAe,IAAI;AACzC,oBAAgB,MAAM,UAAU,KAAK;AACrC,oBAAgB,MAAM,YAAY,KAAK;AACvC,oBAAgB,MAAM,YAAY,KAAK;AACvC,oBAAgB,MAAM,eAAe,KAAK;AAC1C,oBAAgB,MAAM,aAAa,CAAC;AACpC,SAAK,YAAY;AAAA,EACnB;AACA,eAAa,eAAe,CAAC;AAAA,IAC3B,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,QAAQ,KAAK;AAAA,IAC3B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc,GAAG,GAAG;AAClC,WAAK,WAAW;AAChB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,MAAM,aAAa;AAC9C,WAAK,YAAY,CAAC,CAAC;AACnB,UAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,UAAI,MAAM,WAAW,KAAK,eAAe,YAAY,WAAW;AAC9D,aAAK,UAAU,CAAC,IAAI,YAAY,UAAU,CAAC,KAAK;AAChD,YAAI,KAAK,UAAU,CAAC;AAClB,eAAK,UAAU,CAAC,KAAK;AAAA,MACzB,OAAO;AACL,aAAK,UAAU,CAAC,IAAI,SAAS,MAAM,CAAC,CAAC;AAAA,MACvC;AACA,WAAK,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,SAAS,MAAM,CAAC,CAAC,IAAI;AAAA,IAC/D;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;AACF,IAAI,kBAAkC,WAAW;AAC/C,WAAS,iBAAiB,QAAQ;AAChC,oBAAgB,MAAM,gBAAgB;AACtC,oBAAgB,MAAM,UAAU,EAAE;AAClC,oBAAgB,MAAM,OAAO,EAAE;AAC/B,oBAAgB,MAAM,MAAM,IAAI;AAChC,oBAAgB,MAAM,aAAa,EAAE;AACrC,oBAAgB,MAAM,qBAAqB,EAAE;AAC7C,QAAI,kBAAkB,kBAAkB;AACtC,WAAK,SAAS,OAAO;AACrB,WAAK,MAAM,OAAO;AAClB,WAAK,YAAY,OAAO;AACxB,WAAK,oBAAoB,OAAO;AAChC,UAAI,OAAO;AACT,aAAK,KAAK,IAAI,WAAW,OAAO,EAAE;AAAA,IACtC;AAAA,EACF;AACA,eAAa,kBAAkB,CAAC;AAAA,IAC9B,KAAK;AAAA,IACL,OAAO,SAAS,MAAM,IAAI;AACxB,UAAI,MAAM,IAAI,iBAAiB,IAAI;AACnC,UAAI,OAAO,QAAQ,OAAO;AACxB,YAAI,YAAY,EAAE;AACpB,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,IAAI;AAC9B,UAAI,CAAC,KAAK,MAAM,KAAK,WAAW,aAAa,OAAO,OAAO,YAAY,KAAK,KAAK;AAC/E,aAAK,KAAK,IAAI,WAAW,EAAE;AAC3B,iBAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAC5B,eAAK,GAAG,CAAC,IAAI,MAAM,KAAK,KAAK,KAAK;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,qBAAqB;AACnC,UAAI,SAAS,KAAK;AAClB,aAAO,WAAW;AAAA,IACpB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB;AACjC,UAAI,mBAAmB,UAAU,CAAC,WAAW,WAAW,WAAW,UAAU,WAAW,UAAU,WAAW,SAAS,CAAC,EAAE,QAAQ,KAAK,SAAS,IAAI;AACnJ,UAAI,CAAC,kBAAkB;AACrB,eAAO;AAAA,MACT;AACA,UAAI,gBAAgB,CAAC,cAAc,mBAAmB,gBAAgB,EAAE,QAAQ,KAAK,MAAM,IAAI;AAC/F,UAAI,CAAC,eAAe;AAClB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,CAAC,KAAK,QAAQ;AAChB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,mBAAmB,GAAG;AAC7B,eAAO;AAAA,MACT,WAAW,KAAK,iBAAiB,GAAG;AAClC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;ACzOF,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,sBAAsB;AAC1B,IAAI,kBAAkB;AACtB,SAAS,SAAS,MAAM;AACtB,SAAO,KAAK,MAAM,QAAQ,EAAE,IAAI,SAAS,GAAG;AAC1C,WAAO,EAAE,KAAK;AAAA,EAChB,CAAC,EAAE,OAAO,OAAO;AACnB;AACA,SAAS,SAAS,MAAM;AACtB,MAAI,MAAM,KAAK,MAAM,UAAU;AAC/B,MAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB;AACF,SAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC;AAC9C;AACA,SAAS,UAAU,MAAM;AACvB,MAAI,MAAM,CAAC;AACX,MAAI,QAAQ,YAAY,KAAK,IAAI;AACjC,SAAO,OAAO;AACZ,QAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,MAAM,CAAC;AACnC,YAAQ,YAAY,KAAK,IAAI;AAAA,EAC/B;AACA,SAAO;AACT;AACA,SAAS,eAAe,KAAK,WAAW;AACtC,MAAI,CAAC,aAAa,CAAC,OAAO,oBAAoB,KAAK,GAAG;AACpD,WAAO;AACT,MAAI,QAAQ,gBAAgB,KAAK,SAAS;AAC1C,MAAI,CAAC;AACH,WAAO;AACT,MAAI,IAAI,CAAC,MAAM;AACb,WAAO,MAAM,CAAC,IAAI;AACpB,SAAO,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI;AAC/B;AACA,IAAI,gBAAgB;AAAA,EAClB,OAAO,CAAC,SAAS,YAAY,UAAU,UAAU,WAAW;AAAA,EAC5D,OAAO,CAAC,QAAQ,QAAQ,QAAQ,aAAa,OAAO;AAAA,EACpD,MAAM,CAAC,SAAS,SAAS,OAAO;AAClC;AACA,SAAS,UAAU,MAAM,QAAQ;AAC/B,MAAI,KAAK,cAAc,IAAI;AAC3B,MAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO;AAC5B;AACF,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,GAAG,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;AACtB,eAAO,OAAO,CAAC;AAAA,IACnB;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,MAAM,qBAAqB;AACnD,MAAI;AACJ,MAAI,qBAAqB;AACvB,aAAS,OAAO,qBAAqB;AACnC,UAAI,OAAO,UAAU,eAAe,KAAK,qBAAqB,GAAG,KAAK,KAAK,GAAG,MAAM,oBAAoB,GAAG,GAAG;AAC5G,6BAAqB;AACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,WAAW;AACf,MAAI,KAAK,UAAU;AACjB,eAAW,WAAW,KAAK,QAAQ;AACnC,QAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC9B,iBAAW;AAAA,IACb,WAAW,KAAK,UAAU;AACxB,kBAAY,KAAK,SAAS,QAAQ,IAAI,KAAK,WAAW,QAAQ,KAAK;AAAA,IACrE;AAAA,EACF;AACA,MAAI,MAAM,qBAAqB,KAAK,OAAO,KAAK,OAAO,GAAG;AAAA,IACxD,KAAK;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,EACR,CAAC;AACD,SAAO,CAAC,CAAC,KAAK,MAAM,CAAC,sBAAsB,OAAO,SAAS,KAAK,WAAW,QAAQ,CAAC,MAAM,aAAa,QAAQ,YAAY,OAAO,EAAE,KAAK,gBAAgB,UAAU,CAAC,CAAC,KAAK,WAAW,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,QAAQ,IAAI,UAAU,EAAE,KAAK,UAAU,iCAAiC,iBAAiB,QAAQ,kBAAkB;AACpV;AACA,SAAS,qBAAqB,WAAW,MAAM;AAC7C,UAAQ,YAAY,UAAU,MAAM,OAAO,IAAI,CAAC,GAAG,OAAO,SAAS,QAAQ,YAAY;AACrF,WAAO,WAAW,YAAY,CAAC,IAAI;AACnC,WAAO;AAAA,EACT,GAAG,IAAI;AACT;;;AC9EA,SAAS,oBAAoB,OAAO,WAAW;AAC7C,MAAI,SAAS,IAAI,eAAe;AAChC,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI,eAAe,CAAC;AACpB,MAAI,kBAAkB,CAAC;AACvB,SAAO,OAAO,MAAM,OAAO,GAAG;AAC5B,QAAI,MAAM,SAAS,IAAI;AACvB,QAAI,CAAC;AACH;AACF,QAAI,OAAO,eAAe,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC;AAChE,QAAI,SAAS,WAAW;AACtB,aAAO,UAAU,SAAS,IAAI;AAAA,IAChC,WAAW,SAAS,WAAW,MAAM;AACnC,UAAI,OAAO,UAAU,IAAI;AACzB,UAAI,SAAS;AACb,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AACH,mBAAS,IAAI,YAAY;AACzB;AAAA,QACF,KAAK;AACH,mBAAS,IAAI,eAAe;AAC5B;AAAA,QACF;AACE,mBAAS,IAAI,YAAY;AAAA,MAC7B;AACA,aAAO,MAAM,eAAe,KAAK,KAAK,SAAS;AAC/C,aAAO,UAAU,KAAK,YAAY;AAClC,aAAO,aAAa,KAAK,eAAe;AACxC,aAAO,QAAQ,KAAK,UAAU;AAC9B,aAAO,OAAO,KAAK;AACnB,aAAO,OAAO,KAAK;AACnB,UAAI,KAAK,UAAU;AACjB,eAAO,WAAW,OAAO,KAAK,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC;AACpD,YAAI,OAAO,MAAM,OAAO,QAAQ;AAC9B,iBAAO,WAAW;AAAA,MACtB;AACA,UAAI,KAAK,SAAS,WAAW,KAAK,KAAK;AACrC,qBAAa,KAAK,MAAM;AAAA,MAC1B;AACA,UAAI,KAAK,SAAS,aAAa;AAC7B,wBAAgB,KAAK,MAAM;AAAA,MAC7B;AAAA,IACF,WAAW,SAAS,gBAAgB,MAAM;AACxC,UAAI,UAAU,IAAI,aAAa;AAC/B,UAAI,QAAQ,UAAU,IAAI;AAC1B,cAAQ,UAAU,SAAS,MAAM,mBAAmB,KAAK,MAAM,SAAS;AACxE,cAAQ,OAAO,MAAM;AACrB,cAAQ,MAAM,eAAe,MAAM,OAAO,GAAG,SAAS;AACtD,UAAI,MAAM,YAAY;AACpB,YAAI,wBAAwB,MAAM,WAAW,MAAM,GAAG,GAAG,yBAAyB,eAAe,uBAAuB,CAAC,GAAG,IAAI,uBAAuB,CAAC,GAAG,IAAI,uBAAuB,CAAC;AACvL,gBAAQ,QAAQ,SAAS,CAAC;AAC1B,gBAAQ,SAAS,SAAS,CAAC;AAAA,MAC7B;AACA,UAAI,MAAM,QAAQ;AAChB,YAAI,SAAS,MAAM,OAAO,MAAM,OAAO,EAAE,OAAO,OAAO;AACvD,gBAAQ,aAAa,UAAU,SAAS,MAAM;AAC9C,gBAAQ,aAAa,UAAU,SAAS,MAAM;AAC9C,gBAAQ,YAAY,UAAU,QAAQ,MAAM;AAAA,MAC9C;AACA,cAAQ,aAAa,MAAM;AAC3B,cAAQ,gBAAgB,MAAM;AAC9B,aAAO,QAAQ,KAAK,OAAO;AAAA,IAC7B;AAAA,EACF;AACA,SAAO,QAAQ,QAAQ,SAAS,GAAG,GAAG;AACpC,MAAE,KAAK;AAAA,EACT,CAAC;AACD,MAAI,aAAa,QAAQ;AACvB,iBAAa,QAAQ,SAAS,GAAG,GAAG;AAClC,QAAE,KAAK;AAAA,IACT,CAAC;AACD,WAAO,QAAQ,QAAQ,SAAS,SAAS;AACvC,UAAI,QAAQ,YAAY;AACtB,gBAAQ,eAAe,aAAa,OAAO,SAAS,GAAG;AACrD,iBAAO,EAAE,UAAU,QAAQ;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,gBAAgB,QAAQ;AAC1B,oBAAgB,QAAQ,SAAS,GAAG,GAAG;AACrC,QAAE,KAAK;AAAA,IACT,CAAC;AACD,WAAO,QAAQ,QAAQ,SAAS,SAAS;AACvC,UAAI,QAAQ,eAAe;AACzB,gBAAQ,kBAAkB,gBAAgB,OAAO,SAAS,GAAG;AAC3D,iBAAO,EAAE,UAAU,QAAQ;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;;;AC7FA,SAAS,mBAAmB,OAAO,WAAW,eAAe;AAC3D,MAAI,QAAQ,IAAI,cAAc;AAC9B,QAAM,MAAM;AACZ,MAAI,aAAa,IAAI,aAAa,SAAS;AAC3C,MAAI,iBAAiB;AACrB,MAAI,SAAS;AACb,MAAI,gBAAgB;AACpB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI,YAAY;AAChB,MAAI,mBAAmB;AACvB,SAAO,OAAO,MAAM,OAAO,GAAG;AAC5B,QAAI,KAAK,CAAC,MAAM,KAAK;AACnB,UAAI,MAAM,YAAY;AACpB;AACA;AAAA,MACF;AACA,iBAAW,KAAK;AAChB,iBAAW,KAAK;AAChB,iBAAW,MAAM,eAAe,MAAM,SAAS;AAC/C,UAAI;AACF,mBAAW,MAAM,OAAO,MAAM,KAAK;AACrC,UAAI;AACF,mBAAW,cAAc;AAC3B,YAAM,SAAS,KAAK,UAAU;AAC9B,mBAAa,IAAI,aAAa,SAAS;AACvC;AACA;AAAA,IACF;AACA,QAAI,MAAM,SAAS,IAAI;AACvB,QAAI,CAAC;AACH;AACF,QAAI,OAAO,eAAe,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC;AAChE,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,cAAM,UAAU,SAAS,IAAI;AAC7B;AAAA,MACF,KAAK;AACH,cAAM,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,YAAY;AAC1E;AAAA,MACF,KAAK;AACH,cAAM,iBAAiB,WAAW,IAAI;AACtC;AAAA,MACF,KAAK;AACH;AACE,cAAI,eAAe;AACjB,kBAAM,aAAa;AAAA,UACrB;AACA,cAAI,OAAO,UAAU,IAAI;AACzB,cAAI,KAAK,aAAa,GAAG;AACvB,kBAAM,qBAAqB,WAAW,KAAK,aAAa,CAAC;AAAA,UAC3D;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH;AACE,cAAI,QAAQ,UAAU,IAAI;AAC1B,gBAAM,iBAAiB,MAAM,kBAAkB,MAAM;AACrD,gBAAM,eAAe,WAAW,MAAM,gBAAgB,KAAK,CAAC;AAC5D,gBAAM,eAAe,WAAW,MAAM,gBAAgB,KAAK,CAAC;AAC5D,gBAAM,oBAAoB,MAAM,qBAAqB,MAAM;AAAA,QAC7D;AACA;AAAA,MACF,KAAK;AACH;AACE,sBAAY;AAAA,QACd;AACA;AAAA,MACF,KAAK;AACH,gBAAQ,MAAM,UAAU,SAAS,IAAI;AACrC;AAAA,MACF,KAAK;AACH,gBAAQ,MAAM,UAAU,SAAS,IAAI;AACrC;AAAA,MACF,KAAK;AACH;AACA;AAAA,MACF,KAAK;AACH,mBAAW,aAAa,MAAM,MAAM,SAAS,MAAM,SAAS,SAAS,CAAC,CAAC;AACvE;AAAA,MACF,KAAK;AACH;AACE,cAAI,CAAC,MAAM;AACT;AACF,cAAI,SAAS,UAAU,IAAI;AAC3B,qBAAW,WAAW,WAAW,OAAO,UAAU,CAAC;AACnD,qBAAW,cAAc,OAAO,aAAa,MAAM;AACnD,qBAAW,KAAK;AAChB,qBAAW,KAAK;AAChB,qBAAW,YAAY;AACvB,qBAAW,QAAQ;AACnB,qBAAW,WAAW,WAAW,OAAO,UAAU,CAAC;AACnD,2BAAiB,WAAW;AAC5B,qBAAW,MAAM,eAAe,OAAO,KAAK,GAAG,SAAS;AACxD,cAAI;AACF,uBAAW,MAAM,OAAO,MAAM,KAAK;AACrC,cAAI;AACF,uBAAW,cAAc;AAC3B,gBAAM,SAAS,KAAK,UAAU;AAC9B,uBAAa,IAAI,aAAa,SAAS;AACvC;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH;AAAA,MACF,KAAK;AACH,mBAAW,WAAW;AACtB;AAAA,MACF,KAAK;AACH;AACE,cAAI,MAAM,YAAY;AACpB,+BAAmB;AACnB;AAAA,UACF;AACA,cAAI,cAAc,KAAK,MAAM,GAAG,GAAG,eAAe,eAAe,aAAa,CAAC,GAAG,WAAW,aAAa,CAAC,GAAG,QAAQ,aAAa,CAAC;AACpI,qBAAW,QAAQ;AACnB,qBAAW,WAAW,WAAW,QAAQ;AACzC,2BAAiB,WAAW;AAC5B,qBAAW,QAAQ;AAAA,QACrB;AACA;AAAA,MACF,KAAK;AACH;AACE,cAAI,SAAS,UAAU,IAAI;AAC3B,cAAI,OAAO,WAAW,QAAQ;AAC5B,qBAAS;AACT;AAAA,UACF;AACA,mBAAS,IAAI,gBAAgB;AAC7B,iBAAO,SAAS,OAAO;AACvB,iBAAO,MAAM,SAAS,KAAK,OAAO,GAAG,IAAI,OAAO,MAAM,eAAe,OAAO,KAAK,SAAS;AAC1F,iBAAO,YAAY,OAAO,aAAa;AACvC,iBAAO,oBAAoB,OAAO;AAClC,cAAI,CAAC,OAAO,YAAY,GAAG;AACzB,kBAAM,IAAI,MAAM,WAAW,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,OAAO,WAAW,mBAAmB,CAAC;AAAA,UACrG;AACA,cAAI,OAAO,IAAI;AACb,gBAAI,MAAM,OAAO,GAAG,MAAM,CAAC;AAC3B,mBAAO,IAAI,SAAS,IAAI,MAAM,MAAM;AACpC,mBAAO,KAAK,IAAI,WAAW,IAAI,SAAS,CAAC;AACzC,qBAAS,IAAI,GAAG,IAAI,IAAI,SAAS,GAAG,IAAI,GAAG,KAAK;AAC9C,qBAAO,GAAG,CAAC,IAAI,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH;AACE,cAAI,SAAS,UAAU,IAAI;AAC3B,qBAAW,MAAM,eAAe,OAAO,KAAK,SAAS;AACrD,cAAI,OAAO;AACT,uBAAW,aAAa,OAAO,SAAS;AAC1C,qBAAW,gBAAgB;AAC3B,qBAAW,KAAK;AAChB,cAAI,QAAQ;AACV,uBAAW,MAAM,OAAO,MAAM,CAAC;AAAA,UACjC;AACA,2BAAiB;AACjB,uBAAa,IAAI,aAAa,SAAS;AAAA,QACzC;AACA;AAAA,MACF,KAAK;AACH;AACE,cAAI,SAAS,UAAU,IAAI;AAC3B,cAAI,kBAAkB,SAAS,OAAO,kBAAkB,GAAG,EAAE;AAC7D,cAAI,mBAAmB,OAAO,kBAAkB;AAC9C,kBAAM,mBAAmB;AAAA,UAC3B;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH;AACE,cAAI,SAAS,UAAU,IAAI;AAC3B,cAAI,sBAAsB,MAAM,WAAW,OAAO,EAAE;AACpD,iBAAO,aAAa,sBAAsB,oBAAoB,aAAa,IAAI,KAAK,OAAO,YAAY,CAAC;AACxG,cAAI,WAAW,wBAAwB,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,aAAa,IAAI,KAAK,OAAO,QAAQ;AAClJ,cAAI,OAAO,SAAS,OAAO,GAAG;AAC5B,mBAAO,WAAW;AAAA,UACpB;AACA,cAAI,iBAAiB,QAAQ,mBAAmB,KAAK,MAAM,iBAAiB;AAC1E,kBAAM,WAAW,OAAO,EAAE,IAAI;AAAA,UAChC;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF;AACA,QAAM,WAAW,MAAM,SAAS,OAAO,SAAS,GAAG;AACjD,WAAO,EAAE,aAAa;AAAA,EACxB,CAAC;AACD,MAAI,cAAc,MAAM,SAAS,MAAM,SAAS,SAAS,CAAC;AAC1D,MAAI,aAAa;AACf,QAAI,WAAW;AACb,kBAAY,SAAS;AAAA,IACvB;AACA,UAAM,QAAQ,YAAY;AAC1B,UAAM,eAAe,YAAY;AAAA,EACnC;AACA,MAAI,WAAW;AACb,UAAM,OAAO;AAAA,EACf;AACA,QAAM,gBAAgB;AACtB,QAAM,QAAQ;AACd,SAAO;AACT;;;AC5MA,IAAI,aAA6B,WAAW;AAC1C,WAAS,cAAc;AACrB,oBAAgB,MAAM,WAAW;AAAA,EACnC;AACA,eAAa,aAAa,MAAM,CAAC;AAAA,IAC/B,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,UAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AAC5E,UAAI,YAAY,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACtD,UAAI,gBAAgB,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAC1D,UAAI,CAAC,KAAK,SAAS,SAAS;AAC1B,cAAM,IAAI,MAAM,mBAAmB;AACrC,UAAI,QAAQ,SAAS,IAAI;AACzB,UAAI,YAAY,gBAAgB,IAAI,GAAG;AACrC,eAAO,mBAAmB,OAAO,WAAW,aAAa;AAAA,MAC3D;AACA,aAAO,oBAAoB,OAAO,SAAS;AAAA,IAC7C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,MAAM;AACpC,aAAO,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS,wBAAwB;AAAA,IAC5E;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;ACzBF,IAAI,iBAAiC,WAAW;AAC9C,WAAS,gBAAgB,KAAK;AAC5B,QAAI,QAAQ;AACZ,oBAAgB,MAAM,eAAe;AACrC,oBAAgB,MAAM,iBAAiB,SAAS,KAAK,KAAK;AACxD,UAAI,WAAW,IAAI,UAAU,UAAU,IAAI;AAC3C,UAAI,OAAO,WAAW,CAAC,GAAG,gBAAgB,KAAK,eAAe,YAAY,KAAK,WAAW,UAAU,KAAK,SAAS,gBAAgB,KAAK;AACvI,UAAI,OAAO,UAAU;AACrB,YAAM,IAAI,KAAK,MAAM,OAAO;AAAA,QAC1B;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,MACF,CAAC;AACD,YAAM,IAAI,KAAK,MAAM,eAAe;AAAA,QAClC;AAAA,QACA,SAAS,QAAQ;AAAA,MACnB,CAAC;AACD,YAAM,IAAI,KAAK,MAAM,MAAM;AAAA,QACzB;AAAA,QACA,aAAa,SAAS;AAAA,QACtB,SAAS,gBAAgB;AAAA,MAC3B,CAAC;AACD,YAAM,IAAI,KAAK,MAAM,uBAAuB;AAAA,QAC1C,SAAS,SAAS;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,oBAAgB,MAAM,kBAAkB,SAAS,OAAO,WAAW;AACjE,YAAM,IAAI,KAAK,MAAM,YAAY;AAAA,QAC/B,OAAO,eAAe,QAAQ,KAAK;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,iBAAiB,IAAI,OAAO;AACjC,QAAI,mBAAmB,KAAK,IAAI,QAAQ,aAAa,iBAAiB,YAAY,aAAa,iBAAiB,YAAY,sBAAsB,iBAAiB,qBAAqB,eAAe,iBAAiB;AACxN,SAAK,UAAU,IAAI,UAAU,eAAe,eAAe,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,MAChF,cAAc;AAAA,MACd,OAAO;AAAA,MACP;AAAA,MACA,SAAS;AAAA,MACT,cAAc,KAAK;AAAA,IACrB,CAAC,CAAC;AACF,SAAK,eAAe,IAAI,UAAU,eAAe,eAAe,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,MACrF,cAAc;AAAA,MACd,OAAO;AAAA,MACP;AAAA,MACA,SAAS;AAAA,MACT,cAAc,KAAK;AAAA,IACrB,CAAC,CAAC;AACF,SAAK,kBAAkB,IAAI,UAAU,eAAe,eAAe,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,MACxF,cAAc;AAAA,MACd,OAAO;AAAA,MACP;AAAA,MACA,SAAS;AAAA,MACT,cAAc,KAAK;AAAA,IACrB,CAAC,CAAC;AAAA,EACJ;AACA,eAAa,iBAAiB,CAAC;AAAA,IAC7B,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,QAAQ,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,QAAQ,KAAK,UAAU,aAAa;AACpH,YAAI,QAAQ,WAAW,WAAW,cAAc,aAAa,aAAa,gBAAgB,oBAAoB,qBAAqB,OAAO,OAAO,UAAU,iBAAiB,oBAAoB,kBAAkB,gBAAgB,UAAU,eAAe,kBAAkB;AAC7Q,eAAO,oBAAoB,EAAE,KAAK,SAAS,SAAS,UAAU;AAC5D,iBAAO;AACL,oBAAQ,SAAS,OAAO,SAAS,MAAM;AAAA,cACrC,KAAK;AACH,yBAAS,CAAC,KAAK,QAAQ,KAAK,GAAG,CAAC;AAChC,oBAAI,UAAU;AACZ,yBAAO,KAAK,KAAK,aAAa,KAAK,QAAQ,CAAC;AAAA,gBAC9C;AACA,oBAAI,aAAa;AACf,yBAAO,KAAK,KAAK,gBAAgB,KAAK,WAAW,CAAC;AAAA,gBACpD;AACA,yBAAS,OAAO;AAChB,yBAAS,OAAO;AAChB,uBAAO,QAAQ,IAAI,MAAM;AAAA,cAC3B,KAAK;AACH,qCAAqB,SAAS;AAC9B,sCAAsB,eAAe,oBAAoB,CAAC;AAC1D,wBAAQ,oBAAoB,CAAC;AAC7B,wBAAQ,oBAAoB,CAAC;AAC7B,2BAAW,oBAAoB,CAAC;AAChC,oBAAI,OAAO;AACT,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,uBAAO,SAAS,OAAO,UAAU,CAAC,CAAC;AAAA,cACrC,KAAK;AACH,qBAAK,cAAc,OAAO,GAAG;AAC7B,4BAAY,MAAM;AAClB,8BAAc,MAAM,SAAS,OAAO;AACpC,oBAAI,UAAU;AACZ,8BAAY,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAChE,iCAAe,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAC5E,iCAAe,UAAU,QAAQ,UAAU,SAAS,UAAU,kBAAkB,MAAM,cAAc,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,QAAQ;AAC1K,oCAAkB,aAAa,QAAQ,aAAa,SAAS,UAAU,qBAAqB,SAAS,cAAc,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,QAAQ;AAC/L,+BAAa,KAAK,cAAc,OAAO,QAAQ;AAC/C,kCAAgB,KAAK,cAAc,UAAU,WAAW;AAAA,gBAC1D,OAAO;AACL,iCAAe,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AACnE,oCAAkB,UAAU,QAAQ,UAAU,SAAS,UAAU,mBAAmB,MAAM,cAAc,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,QAAQ;AAChL,kCAAgB,KAAK,cAAc,OAAO,WAAW;AAAA,gBACvD;AACA,yBAAS,OAAO;AAChB;AAAA,cACF,KAAK;AACH,yBAAS,OAAO;AAChB,yBAAS,KAAK,SAAS,OAAO,EAAE,CAAC;AACjC,sBAAM,eAAe,QAAQ,SAAS,EAAE;AAAA,cAC1C,KAAK;AACH,iCAAiB,KAAK,IAAI,OAAO;AACjC,yBAAS,OAAO;AAChB,oBAAI,gBAAgB;AAClB,8BAAY,eAAe,SAAS,KAAK;AACzC,sBAAI;AACF,gCAAY,eAAe,WAAW,IAAI,KAAK;AACjD,sBAAI;AACF,mCAAe,eAAe,cAAc,IAAI,KAAK;AAAA,gBACzD;AACA,2BAAW,WAAW,MAAM,WAAW,aAAa,KAAK,cAAc;AACvE,oBAAI,IAAI,YAAY,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,UAAU,SAAS,SAAS,YAAY,CAAC,SAAS,SAAS,SAAS;AACtJ,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,sBAAM,IAAI,MAAM,qBAAqB;AAAA,cACvC,KAAK;AACH,oBAAI,WAAW;AACb,kCAAgB,WAAW,MAAM,WAAW,aAAa,KAAK,cAAc;AAAA,gBAC9E;AACA,oBAAI,cAAc;AAChB,qCAAmB,WAAW,MAAM,cAAc,gBAAgB,KAAK,cAAc;AAAA,gBACvF;AACA,yBAAS,OAAO;AAChB;AAAA,cACF,KAAK;AACH,yBAAS,OAAO;AAChB,yBAAS,KAAK,SAAS,OAAO,EAAE,EAAE;AAClC,sBAAM,IAAI,eAAe,IAAI,UAAU,IAAI,UAAU,KAAK,SAAS,EAAE;AAAA,cACvE,KAAK;AACH,oBAAI,UAAU;AACZ,sBAAI,SAAS,UAAU;AACrB,yBAAK,IAAI,KAAK,MAAM,qBAAqB;AAAA,sBACvC;AAAA,oBACF,CAAC;AAAA,kBACH,OAAO;AACL,yBAAK,IAAI,KAAK,MAAM,kBAAkB;AAAA,sBACpC;AAAA,oBACF,CAAC;AAAA,kBACH;AAAA,gBACF;AACA,uBAAO,SAAS,OAAO,UAAU,CAAC,UAAU,eAAe,gBAAgB,CAAC;AAAA,cAC9E,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,SAAS,KAAK;AAAA,YACzB;AAAA,QACJ,GAAG,SAAS,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AAAA,MACvC,CAAC,CAAC;AACF,eAAS,KAAK,IAAI,KAAK,KAAK;AAC1B,eAAO,MAAM,MAAM,MAAM,SAAS;AAAA,MACpC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,WAAW,KAAK;AACxC,UAAI,iBAAiB,KAAK,IAAI,OAAO;AACrC,UAAI;AACJ,UAAI;AACF,YAAI;AACJ,YAAI,gBAAgB;AAClB,sBAAY,eAAe,SAAS,KAAK;AAAA,QAC3C;AACA,mBAAW,WAAW,MAAM,WAAW,KAAK,KAAK,cAAc;AAC/D,cAAM,aAAa,cAAc,QAAQ,eAAe,SAAS,SAAS,WAAW,UAAU,SAAS,SAAS,YAAY,CAAC,SAAS,SAAS,QAAQ;AACtJ,gBAAM,IAAI,MAAM,qBAAqB;AAAA,QACvC;AAAA,MACF,SAAS,OAAO;AACd,cAAM,IAAI,eAAe,IAAI,UAAU,IAAI,UAAU,KAAK,KAAK;AAAA,MACjE;AACA,UAAI,UAAU;AACZ,YAAI,SAAS,UAAU;AACrB,eAAK,IAAI,KAAK,MAAM,qBAAqB;AAAA,YACvC;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,eAAK,IAAI,KAAK,MAAM,kBAAkB;AAAA,YACpC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO,CAAC,QAAQ;AAAA,IAClB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,KAAK,UAAU,aAAa,IAAI,SAAS,MAAM;AAClE,UAAI,SAAS;AACb,mBAAa,KAAK,MAAM;AACxB,aAAO,QAAQ;AACf,UAAI,aAAa,KAAK,IAAI,OAAO;AACjC,UAAI,KAAqB,WAAW;AAClC,YAAI,QAAQ,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC3F,cAAI;AACJ,iBAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,mBAAO;AACL,sBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,gBACvC,KAAK;AACH,+BAAa,OAAO,MAAM;AAC1B,4BAAU,OAAO;AACjB,4BAAU,OAAO;AACjB,yBAAO,OAAO,KAAK,KAAK,UAAU,WAAW;AAAA,gBAC/C,KAAK;AACH,wBAAM,UAAU;AAChB,sBAAI,IAAI,CAAC,GAAG;AACV,8BAAU,OAAO;AACjB;AAAA,kBACF;AACA,yBAAO,UAAU,OAAO,QAAQ;AAAA,gBAClC,KAAK;AACH,+BAAa,OAAO,IAAI,OAAO;AAC/B,qBAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACzB,4BAAU,OAAO;AACjB;AAAA,gBACF,KAAK;AACH,4BAAU,OAAO;AACjB,4BAAU,KAAK,UAAU,OAAO,EAAE,CAAC;AACnC;AACA,sBAAI,cAAc,GAAG;AACnB,4BAAQ,UAAU,EAAE;AAAA,kBACtB;AAAA,gBACF,KAAK;AACH,yBAAO,SAAS,WAAW,IAAI,IAAI;AAAA,gBACrC,KAAK;AAAA,gBACL,KAAK;AACH,yBAAO,UAAU,KAAK;AAAA,cAC1B;AAAA,UACJ,GAAG,UAAU,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,QAC9B,CAAC,CAAC;AACF,eAAO,SAAS,MAAM;AACpB,iBAAO,MAAM,MAAM,MAAM,SAAS;AAAA,QACpC;AAAA,MACF,EAAE;AACF,WAAK,SAAS,WAAW,IAAI,IAAI;AAAA,IACnC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,mBAAa,KAAK,MAAM;AACxB,aAAO,KAAK,OAAO;AAAA,IACrB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,aAAO,QAAQ,IAAI,CAAC,KAAK,QAAQ,OAAO,GAAG,KAAK,aAAa,OAAO,CAAC,CAAC;AAAA,IACxE;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;ACtQF,SAAS,MAAM,KAAK,KAAK,KAAK;AAC5B,MAAI,MAAM,KAAK;AACb,UAAM;AAAA,EACR;AACA,SAAO,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG;AACzC;;;ACHA,IAAIC,UAAS,IAAI,OAAO,UAAU;AAClC,IAAI,SAAyB,WAAW;AACtC,WAAS,QAAQ,UAAU,eAAe,kBAAkB;AAC1D,oBAAgB,MAAM,OAAO;AAC7B,oBAAgB,MAAM,QAAQ,MAAM;AACpC,oBAAgB,MAAM,MAAM,CAAC;AAC7B,oBAAgB,MAAM,WAAW,CAAC;AAClC,oBAAgB,MAAM,SAAS,CAAC;AAChC,oBAAgB,MAAM,UAAU,CAAC;AACjC,oBAAgB,MAAM,QAAQ,EAAE;AAChC,oBAAgB,MAAM,OAAO,EAAE;AAC/B,oBAAgB,MAAM,cAAc,EAAE;AACtC,oBAAgB,MAAM,cAAc,EAAE;AACtC,oBAAgB,MAAM,aAAa,EAAE;AACrC,oBAAgB,MAAM,WAAW,CAAC;AAClC,oBAAgB,MAAM,SAAS,CAAC;AAChC,oBAAgB,MAAM,WAAW,CAAC;AAClC,oBAAgB,MAAM,SAAS,EAAE;AACjC,oBAAgB,MAAM,iBAAiB,CAAC;AACxC,oBAAgB,MAAM,kBAAkB,CAAC;AACzC,oBAAgB,MAAM,sBAAsB,CAAC;AAC7C,oBAAgB,MAAM,gBAAgB,CAAC;AACvC,oBAAgB,MAAM,qBAAqB,KAAK;AAChD,oBAAgB,MAAM,mBAAmB,CAAC;AAC1C,oBAAgB,MAAM,kBAAkB,KAAK;AAC7C,oBAAgB,MAAM,gBAAgB,CAAC;AACvC,oBAAgB,MAAM,cAAc,KAAK;AACzC,oBAAgB,MAAM,gBAAgB,CAAC;AACvC,oBAAgB,MAAM,UAAU,IAAI;AACpC,oBAAgB,MAAM,YAAY,CAAC,CAAC;AACpC,oBAAgB,MAAM,gBAAgB,CAAC,CAAC;AACxC,oBAAgB,MAAM,mBAAmB,CAAC,CAAC;AAC3C,oBAAgB,MAAM,kBAAkB,CAAC,CAAC;AAC1C,oBAAgB,MAAM,sBAAsB,IAAI;AAChD,oBAAgB,MAAM,yBAAyB,IAAI;AACnD,SAAK,OAAO,KAAK,oBAAoB,QAAQ,GAAG,eAAe,gBAAgB;AAAA,EACjF;AACA,eAAa,SAAS,CAAC;AAAA,IACrB,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI,KAAK,SAAS,QAAQ;AACxB,eAAO,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AAAA,MAC/C;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,aAAO,KAAK,oBAAoB,kBAAkB,KAAK,SAAS,CAAC,OAAO,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,aAAa;AAAA,IACrJ;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK;AAAA,IACd;AAAA,IACA,KAAK,SAAS,IAAI,KAAK;AACrB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,eAAS,oBAAoB,KAAK,iBAAiB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,QAAQ;AAAA,IAC/H;AAAA,IACA,KAAK,SAAS,IAAI,KAAK;AACrB,UAAI,UAAU,KAAK;AACnB,UAAI;AACF,gBAAQ,WAAW,MAAM,QAAQ;AAAA,IACrC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,eAAS,wBAAwB,KAAK,2BAA2B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,UAAU;AAAA,IACvJ;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,WAAW,SAAS;AAClD,UAAI,KAAK,oBAAoB;AAC3B,aAAK,eAAe,WAAW,OAAO;AAAA,MACxC;AACA,aAAO,KAAK,eAAe,WAAW,OAAO;AAAA,IAC/C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,KAAK;AACnC,UAAI,CAAC,OAAO,CAAC,KAAK;AAChB;AACF,UAAI,KAAK,IAAI,KAAK,KAAK;AACvB,aAAO,KAAK,mBAAmB,SAAS,KAAK,SAAS,GAAG;AACvD,eAAO,EAAE,OAAO;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,UAAU,eAAe;AAC9C,WAAK,MAAM,SAAS;AACpB,UAAI,MAAM,QAAQ,SAAS,QAAQ,GAAG;AACpC,YAAI,KAAK,SAAS,QAAQ,KAAK,SAAS;AACtC,eAAK,OAAO,SAAS;AACvB,aAAK,gBAAgB,UAAU,IAAI;AACnC,aAAK,UAAU,SAAS;AACxB,aAAK,QAAQ,SAAS;AACtB,aAAK,UAAU,SAAS;AACxB,aAAK,QAAQ,SAAS,SAAS;AAC/B,aAAK,gBAAgB,SAAS;AAC9B,aAAK,iBAAiB,SAAS;AAC/B,aAAK,OAAO,SAAS;AACrB,aAAK,aAAa,SAAS;AAC3B,aAAK,iBAAiB,SAAS;AAC/B,aAAK,oBAAoB,SAAS;AAClC,aAAK,eAAe,SAAS;AAC7B,aAAK,eAAe,SAAS;AAC7B,aAAK,qBAAqB,SAAS;AACnC,aAAK,kBAAkB,SAAS;AAChC,aAAK,eAAe,SAAS;AAC7B,YAAI,iBAAiB,KAAK,sBAAsB,MAAM,QAAQ,cAAc,QAAQ,GAAG;AACrF,eAAK,gBAAgB,eAAe,KAAK,kBAAkB;AAC3D,eAAK,KAAK,WAAW,QAAQ,KAAK,WAAW,WAAW,SAAS,SAAS,UAAU,cAAc,SAAS,QAAQ;AACjH,iBAAK,SAAS,SAAS,SAAS,CAAC,EAAE,KAAK,cAAc,SAAS,CAAC,EAAE;AAAA,UACpE;AAAA,QACF;AAAA,MACF,OAAO;AACL,aAAK,KAAK,SAAS;AACnB,aAAK,UAAU,SAAS;AACxB,aAAK,QAAQ,SAAS;AACtB,aAAK,SAAS,SAAS;AACvB,aAAK,OAAO,SAAS;AACrB,aAAK,aAAa,SAAS;AAC3B,aAAK,aAAa,SAAS;AAC3B,aAAK,YAAY,SAAS;AAC1B,aAAK,eAAe,SAAS;AAC7B,aAAK,kBAAkB,SAAS;AAChC,YAAI,CAAC,KAAK,sBAAsB,KAAK,aAAa,QAAQ;AACxD,eAAK,qBAAqB,KAAK,aAAa,KAAK,SAAS,GAAG;AAC3D,mBAAO,EAAE;AAAA,UACX,CAAC,KAAK,KAAK,aAAa,CAAC;AAAA,QAC3B;AACA,YAAI,CAAC,KAAK,yBAAyB,KAAK,gBAAgB,QAAQ;AAC9D,eAAK,wBAAwB,KAAK,gBAAgB,KAAK,SAAS,GAAG;AACjE,mBAAO,EAAE;AAAA,UACX,CAAC,KAAK,KAAK,gBAAgB,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,kBAAkB;AAC/C,UAAI,QAAQ;AACZ,UAAI,EAAE,oBAAoB,KAAK,yBAAyB,MAAM,QAAQ,iBAAiB,QAAQ;AAC7F;AACF,UAAI,UAAU,KAAK,gBAAgB,kBAAkB,KAAK,qBAAqB;AAC/E,UAAI,OAAO,KAAK,sBAAsB;AACtC,UAAI,KAAK,SAAS,KAAK;AACrB,aAAK,sBAAsB,WAAW,KAAK,MAAM,GAAG;AAAA,MACtD;AACA,UAAI,CAAC;AACH;AACF,aAAO,QAAQ,IAAI,SAAS,GAAG;AAC7B,eAAO;AAAA,UACL,IAAI,EAAE;AAAA,UACN,KAAK,EAAE;AAAA,UACP,UAAU,EAAE;AAAA,UACZ,OAAO,EAAE;AAAA,UACT,KAAK,EAAE;AAAA,UACP,MAAM,MAAM,sBAAsB;AAAA,QACpC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,MAAM;AACnC,UAAI,WAAW,KAAK,gBAAgB,KAAK,SAAS,GAAG;AACnD,eAAO,EAAE,SAAS;AAAA,MACpB,CAAC;AACD,UAAI,SAAS,KAAK;AAClB,UAAI,UAAU;AACZ,aAAK,wBAAwB;AAC7B,eAAO,WAAW,CAAC;AAAA,MACrB;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB,UAAU;AAC5C,UAAI,CAAC,SAAS,cAAc,CAAC,SAAS,SAAS;AAC7C,eAAO;AACT,UAAI,gBAAgB,SAAS,gBAAgB,SAAS;AACtD,UAAI,OAAO,SAAS;AACpB,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,YAAI,KAAK,CAAC,EAAE,SAAS,iBAAiB,KAAK,CAAC,EAAE,aAAa;AACzD,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,aAAa,KAAK,MAAM,KAAK;AACjC,UAAI,UAAU;AACd,iBAAW,QAAQ,SAAS,GAAG;AAC7B,UAAE,QAAQ;AACV,kBAAU,EAAE;AAAA,MACd,CAAC;AACD,eAAS,WAAW;AACpB,eAAS,gBAAgB;AACzB,eAAS,UAAU,WAAW,CAAC,EAAE;AACjC,eAAS,UAAU,WAAW,CAAC,EAAE;AACjC,MAAAA,QAAO,IAAI,iCAAiC,OAAO,SAAS,SAAS,aAAa,EAAE,OAAO,WAAW,CAAC,EAAE,WAAW,aAAa,EAAE,OAAO,OAAO,CAAC;AAClJ,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,WAAW,SAAS;AACjD,UAAI,aAAa;AACjB,UAAI,WAAW,KAAK;AACpB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,YAAI,SAAS,CAAC,EAAE,OAAO,WAAW;AAChC,uBAAa;AACb;AAAA,QACF;AAAA,MACF;AACA,UAAI,aAAa,SAAS;AACxB,qBAAa;AAAA,MACf;AACA,UAAI,YAAY;AACd,aAAK,WAAW,KAAK,SAAS,MAAM,UAAU;AAC9C,YAAI,KAAK,oBAAoB;AAC3B,eAAK,mBAAmB,WAAW,KAAK,mBAAmB,SAAS,MAAM,UAAU;AAAA,QACtF;AAAA,MACF;AACA,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,UAAU,QAAQ;AAChD,UAAI,WAAW,OAAO;AACtB,UAAI,KAAK,MAAM;AACb,YAAI;AACJ,YAAI,aAAa,SAAS;AAC1B,YAAI,SAAS,SAAS,SAAS,SAAS,CAAC;AACzC,YAAI,SAAS,aAAa,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,QAAQ,QAAQ,eAAe,SAAS,aAAa;AACtI,YAAI,gBAAgB,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,cAAc;AACzF,YAAI,SAAS,QAAQ,SAAS,SAAS,SAAS,SAAS;AACzD,YAAI,YAAY;AACd,mBAAS,UAAU,eAAe,SAAS;AAAA,QAC7C;AACA,YAAI,QAAQ;AACV,UAAAA,QAAO,IAAI,0BAA0B,OAAO,OAAO,cAAc,EAAE,OAAO,cAAc,aAAa,EAAE,OAAO,SAAS,OAAO,cAAc,EAAE,OAAO,SAAS,YAAY,CAAC;AAC3K,cAAI,QAAQ,SAAS,SAAS,UAAU,SAAS,GAAG;AAClD,mBAAO,EAAE,OAAO,SAAS,EAAE,cAAc;AAAA,UAC3C,CAAC;AACD,cAAI,WAAW,QAAQ,IAAI,SAAS,WAAW,SAAS,SAAS,MAAM,QAAQ,CAAC;AAChF,cAAI,SAAS,UAAU,SAAS,QAAQ;AACtC,gBAAI,UAAU,OAAO;AACrB,gBAAI,sBAAsB;AAC1B,qBAAS,QAAQ,SAAS,KAAK;AAC7B,kBAAI,QAAQ;AACZ,wBAAU,IAAI;AAAA,YAChB,CAAC;AACD,YAAAA,QAAO,IAAI,aAAa,OAAO,qBAAqB,MAAM,EAAE,OAAO,OAAO,CAAC;AAC3E,gBAAI,UAAU,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,OAAO;AAC5E,gBAAI,SAAS,SAAS,CAAC,EAAE,IAAI;AAC3B,uBAAS,QAAQ,SAAS,KAAK;AAC7B,uBAAO,IAAI,MAAM;AAAA,cACnB,CAAC;AAAA,YACH;AAAA,UACF;AACA,iBAAO,QAAQ,SAAS;AACxB,iBAAO,WAAW,SAAS,OAAO,QAAQ;AAC1C,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,eAAO,WAAW,SAAS;AAAA,MAC7B;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;ACjRF,IAAI,WAA2B,WAAW;AACxC,WAAS,UAAU,KAAK;AACtB,oBAAgB,MAAM,SAAS;AAC/B,oBAAgB,MAAM,WAAW,CAAC,CAAC;AACnC,oBAAgB,MAAM,iBAAiB,IAAI;AAC3C,oBAAgB,MAAM,aAAa,CAAC;AACpC,oBAAgB,MAAM,mBAAmB,EAAE;AAC3C,SAAK,MAAM;AAAA,EACb;AACA,eAAa,WAAW,CAAC;AAAA,IACvB,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,sBAAsB,KAAK,mBAAmB,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB;AAAA,IAC9H;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,uBAAuB,KAAK,mBAAmB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB;AAAA,IACjI;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,wBAAwB,KAAK,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK,eAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,yBAAyB,KAAK,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,kBAAkB,CAAC;AAAA,IACjK;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,uBAAuB,KAAK,mBAAmB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB;AAAA,IACjI;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,uBAAuB,KAAK,mBAAmB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB;AAAA,IACjI;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,uBAAuB,KAAK,mBAAmB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB;AAAA,IACjI;AAAA,IACA,KAAK,SAAS,IAAI,KAAK;AACrB,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,WAAW;AAAA,MAChC;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,eAAS,uBAAuB,KAAK,mBAAmB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,kBAAkB;AAAA,IACpJ;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI,WAAW,KAAK;AACpB,UAAI,CAAC,YAAY,CAAC,SAAS;AACzB;AACF,aAAO,CAAC,SAAS,CAAC,EAAE,OAAO,SAAS,SAAS,SAAS,CAAC,EAAE,GAAG;AAAA,IAC9D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,eAAS,yBAAyB,KAAK,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,WAAW;AAAA,IACrJ;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,aAAO,GAAG,yBAAyB,KAAK,qBAAqB,QAAQ,2BAA2B,UAAU,uBAAuB;AAAA,IACnI;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,uBAAuB,KAAK,mBAAmB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB;AAAA,IACjI;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,oBAAoB;AAAA,IAClC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,aAAO,CAAC,GAAG,uBAAuB,KAAK,mBAAmB,QAAQ,yBAAyB,UAAU,qBAAqB;AAAA,IAC5H;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,KAAK;AACnC,UAAI;AACJ,cAAQ,uBAAuB,KAAK,mBAAmB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,gBAAgB,GAAG;AAAA,IACpJ;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB,KAAK;AACtC,UAAI;AACJ,UAAI,QAAQ,QAAQ,QAAQ;AAC1B,cAAM,KAAK,kBAAkB;AAC/B,WAAK,kBAAkB,MAAM,KAAK,KAAK,yBAAyB,KAAK,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,MAAM;AAAA,IAC9K;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,WAAK,UAAU,CAAC;AAChB,WAAK,gBAAgB;AACrB,WAAK,YAAY;AACjB,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB,OAAO;AACvC,UAAI;AACJ,cAAQ,yBAAyB,KAAK,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,wBAAwB;AACtC,UAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AAC7E,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,qBAAqB;AACnC,UAAI;AACJ,UAAI,KAAK,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AAC1E,UAAI,YAAY,yBAAyB,KAAK,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,UAAU,SAAS,GAAG;AACnK,eAAO,EAAE,OAAO;AAAA,MAClB,CAAC;AACD,UAAI,aAAa,IAAI;AACnB,aAAK,sBAAsB,WAAW,CAAC;AAAA,MACzC;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,uBAAuB,MAAM;AAC3C,UAAI,WAAW,KAAK;AACpB,UAAI,UAAU;AACZ,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,GAAG,KAAK;AACpD,gBAAM,SAAS,CAAC;AAChB,cAAI,QAAQ,IAAI,SAAS,OAAO,IAAI,KAAK;AACvC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,cAAc,SAAS,SAAS,SAAS,CAAC;AAC9C,YAAI,KAAK,IAAI,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,IAAI,IAAI;AACjG,iBAAO,SAAS,SAAS;AAAA,MAC7B;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,UAAU,eAAe,kBAAkB;AACxE,UAAI,QAAQ;AACZ,UAAI,CAAC;AACH;AACF,UAAI,SAAS,UAAU;AACrB,aAAK,QAAQ,SAAS,SAAS,QAAQ;AACvC,iBAAS,QAAQ,OAAO,SAAS,GAAG;AAClC,iBAAO,EAAE;AAAA,QACX,CAAC,EAAE,QAAQ,SAAS,SAAS,GAAG;AAC9B,cAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,kBAAM,QAAQ,CAAC,EAAE,OAAO,OAAO;AAAA,UACjC,OAAO;AACL,kBAAM,QAAQ,CAAC,IAAI,IAAI,OAAO,OAAO;AAAA,UACvC;AAAA,QACF,CAAC;AACD,aAAK,gBAAgB,KAAK,QAAQ,CAAC;AAAA,MACrC,WAAW,MAAM,QAAQ,SAAS,QAAQ,GAAG;AAC3C,YAAI,SAAS,KAAK;AAClB,YAAI,QAAQ;AACV,iBAAO,OAAO,UAAU,eAAe,gBAAgB;AACvD,cAAI,kBAAkB,OAAO,eAAe,gBAAgB;AAC5D,cAAI,iBAAiB;AACnB,iBAAK,IAAI,KAAK,MAAM,mBAAmB;AAAA,cACrC,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,eAAK,MAAM;AACX,eAAK,gBAAgB,KAAK,QAAQ,CAAC,IAAI,IAAI,OAAO,UAAU,eAAe,gBAAgB;AAAA,QAC7F;AAAA,MACF;AACA,UAAI,gBAAgB,KAAK;AACzB,UAAI,eAAe;AACjB,YAAI,KAAK,IAAI,UAAU,CAAC,KAAK,WAAW;AACtC,eAAK,YAAY,KAAK,gBAAgB,OAAO,SAAS,GAAG,GAAG;AAC1D,iBAAK,EAAE;AACP,mBAAO;AAAA,UACT,GAAG,CAAC;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,qBAAqB,IAAI,OAAO;AAC9C,UAAI;AACJ,UAAI,QAAQ,yBAAyB,KAAK,qBAAqB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,OAAO,SAAS,GAAG;AAC5J,eAAO,EAAE,MAAM;AAAA,MACjB,CAAC;AACD,WAAK,QAAQ,SAAS,GAAG;AACvB,UAAE,QAAQ;AACV,gBAAQ,EAAE;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe,MAAM;AACnC,UAAI;AACJ,OAAC,wBAAwB,KAAK,mBAAmB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,eAAe,IAAI;AAAA,IAChJ;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB;AAChC,UAAI,kBAAkB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AACvF,UAAI,SAAS,KAAK;AAClB,UAAI,CAAC,KAAK,aAAa,CAAC;AACtB;AACF,UAAI,YAAY,OAAO,UAAU,KAAK;AACtC,UAAI,aAAa;AACf;AACF,UAAI,WAAW,OAAO;AACtB,UAAI,SAAS,UAAU;AACrB;AACF,WAAK,kBAAkB,OAAO,gBAAgB,WAAW,KAAK,eAAe;AAAA,IAC/E;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,wBAAwB,OAAO,MAAM;AACnD,UAAI,QAAQ,KAAK,uBAAuB,KAAK;AAC7C,UAAI,MAAM,KAAK,kBAAkB,KAAK;AACtC,UAAI,CAAC;AACH;AACF,UAAI,CAAC,IAAI,YAAY,CAAC,IAAI;AACxB;AACF,UAAI,SAAS,KAAK,IAAI,YAAY,IAAI;AACpC,eAAO;AACT,UAAI,IAAI,MAAM,QAAQ;AACpB;AACF,UAAI,OAAO,KAAK,kBAAkB,QAAQ,CAAC;AAC3C,UAAI,CAAC;AACH;AACF,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK;AAC1B;AACF,UAAI,KAAK,aAAa,IAAI,YAAY,KAAK,aAAa,IAAI;AAC1D,eAAO;AAAA,IACX;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB,SAAS,WAAW;AACnD,UAAI;AACJ,UAAI,OAAO,KAAK;AAChB,UAAI,CAAC;AACH;AACF,UAAI,WAAW,oBAAoB,KAAK,iBAAiB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,QAAQ,QAAQ;AAC3I,UAAI,QAAQ;AACV,aAAK,WAAW;AAChB;AAAA,MACF;AACA,WAAK,qBAAqB,QAAQ,KAAK,GAAG,SAAS;AAAA,IACrD;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;ACrRF,IAAI,gBAAgC,WAAW;AAC7C,WAAS,eAAe,KAAK;AAC3B,QAAI,QAAQ;AACZ,oBAAgB,MAAM,cAAc;AACpC,oBAAgB,MAAM,SAAS,IAAI;AACnC,oBAAgB,MAAM,iBAAiB,SAAS,KAAK,KAAK;AACxD,UAAI,OAAO,IAAI,MAAM,WAAW,IAAI,UAAU,UAAU,IAAI;AAC5D,UAAI,OAAO,WAAW,CAAC,GAAG,gBAAgB,KAAK,eAAe,YAAY,KAAK,WAAW,UAAU,KAAK,SAAS,gBAAgB,KAAK;AACvI,UAAI,OAAO,UAAU;AACrB,YAAM,kBAAkB,UAAU,iBAAiB,KAAK,YAAY,IAAI;AACxE,YAAM,IAAI,KAAK,MAAM,OAAO;AAAA,QAC1B;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,MACF,CAAC;AACD,YAAM,IAAI,KAAK,MAAM,eAAe;AAAA,QAClC;AAAA,QACA,SAAS,QAAQ;AAAA,MACnB,CAAC;AACD,YAAM,IAAI,KAAK,MAAM,MAAM;AAAA,QACzB;AAAA,QACA,aAAa,SAAS;AAAA,QACtB,SAAS,gBAAgB;AAAA,MAC3B,CAAC;AACD,YAAM,IAAI,KAAK,MAAM,uBAAuB;AAAA,QAC1C,SAAS,SAAS;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,oBAAgB,MAAM,kBAAkB,SAAS,OAAO,WAAW;AACjE,YAAM,IAAI,KAAK,MAAM,YAAY;AAAA,QAC/B,OAAO,eAAe,QAAQ,KAAK;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,SAAK,MAAM;AACX,SAAK,oBAAoB,IAAI,iBAAiB;AAC9C,SAAK,YAAY,CAAC;AAClB,SAAK,YAAY,CAAC;AAClB,QAAI,mBAAmB,KAAK,IAAI,QAAQ,aAAa,iBAAiB,YAAY,aAAa,iBAAiB,YAAY,cAAc,iBAAiB,aAAa,eAAe,iBAAiB;AACxM,SAAK,iBAAiB,IAAI,UAAU,eAAe,eAAe,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,MACvF,cAAc;AAAA,MACd,OAAO;AAAA,MACP;AAAA,MACA,SAAS;AAAA,MACT,cAAc,KAAK;AAAA,IACrB,CAAC,CAAC;AACF,SAAK,sBAAsB,IAAI,UAAU,eAAe,eAAe,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,MAC5F,cAAc;AAAA,MACd,OAAO;AAAA,MACP;AAAA,MACA,SAAS;AAAA,MACT,cAAc,KAAK;AAAA,IACrB,CAAC,CAAC;AACF,SAAK,aAAa,IAAI,UAAU,eAAe,eAAe,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,MACnF,cAAc;AAAA,MACd,OAAO;AAAA,MACP;AAAA,MACA,SAAS;AAAA,MACT,cAAc,KAAK;AAAA,IACrB,CAAC,CAAC;AAAA,EACJ;AACA,eAAa,gBAAgB,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,OAAO,SAAS,YAAY;AAC1B,aAAO;AAAA,QACL,OAAO,KAAK,kBAAkB,eAAe;AAAA,QAC7C,UAAU,KAAK,kBAAkB,YAAY;AAAA,MAC/C;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,KAAK,UAAU,UAAU;AAC5C,UAAI,gBAAgB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AACrF,UAAI,SAAS,CAAC;AACd,UAAI;AACF,eAAO,CAAC,IAAI,KAAK,iBAAiB,KAAK,QAAQ;AACjD,UAAI;AACF,eAAO,CAAC,IAAI,KAAK,iBAAiB,UAAU,aAAa;AAC3D,aAAO,QAAQ,IAAI,MAAM;AAAA,IAC3B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB,KAAK,UAAU;AAC9C,aAAO,KAAK,aAAa,KAAK,gBAAgB,KAAK,QAAQ;AAAA,IAC7D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,iBAAiB,KAAK,UAAU;AAC9C,aAAO,KAAK,aAAa,KAAK,qBAAqB,KAAK,QAAQ;AAAA,IAClE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,gBAAgB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,QAAQ,WAAW,KAAK,UAAU;AAC1H,YAAI,SAAS,MAAM;AACnB,YAAI,KAAK,KAAK,OAAO,QAAQ,UAAU,QAAQ,sBAAsB,QAAQ,SAAS,QAAQ,oBAAoB,qBAAqB,GAAG;AAC1I,eAAO,oBAAoB,EAAE,KAAK,SAAS,SAAS,UAAU;AAC5D,iBAAO;AACL,oBAAQ,SAAS,OAAO,SAAS,MAAM;AAAA,cACrC,KAAK;AACH,yBAAS,CAAC;AACV,qBAAK,IAAI,KAAK,MAAM,YAAY;AAAA,kBAC9B,KAAK,IAAI;AAAA,gBACX,CAAC;AACD,uBAAO,CAAC,IAAI,UAAU,KAAK,IAAI,GAAG;AAClC,oBAAI,YAAY,IAAI,aAAa;AAC/B,2BAAS,IAAI,YAAY;AACzB,wBAAM,KAAK,UAAU,MAAM;AAC3B,sBAAI,CAAC,KAAK;AACR,yBAAK,IAAI,KAAK,MAAM,YAAY;AAAA,sBAC9B,KAAK;AAAA,oBACP,CAAC;AACD,2BAAO,CAAC,IAAI,UAAU,KAAK,MAAM,EAAE,KAAK,SAAS,GAAG;AAClD,0BAAI,GAAG;AACL,4BAAI,IAAI,OAAO,KAAK,OAAO,SAAS;AACpC,4BAAI,IAAI;AACN,iCAAO,YAAY,CAAC;AACtB,8BAAM,OAAO,UAAU,MAAM,IAAI,EAAE;AACnC,+BAAO,cAAc,GAAG,MAAM;AAAA,sBAChC;AAAA,oBACF,CAAC;AAAA,kBACH;AACA,6BAAW,uBAAuB,IAAI,YAAY,SAAS,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB;AACnI,sBAAI,SAAS;AACX,+BAAW,IAAI,YAAY,IAAI;AAC/B,6BAAS,KAAK,UAAU,OAAO;AAC/B,wBAAI,CAAC,QAAQ;AACX,2BAAK,IAAI,KAAK,MAAM,YAAY;AAAA,wBAC9B,KAAK;AAAA,sBACP,CAAC;AACD,6BAAO,CAAC,IAAI,KAAK,WAAW,KAAK,OAAO,EAAE,KAAK,SAAS,GAAG;AACzD,4BAAI,GAAG;AACL,mCAAS,OAAO,UAAU,OAAO,IAAI,EAAE;AACvC,iCAAO,cAAc,GAAG,OAAO;AAAA,wBACjC;AAAA,sBACF,CAAC;AAAA,oBACH;AAAA,kBACF;AAAA,gBACF;AACA,0BAAU,WAAW,IAAI,SAAS,QAAQ,aAAa,SAAS,SAAS,SAAS;AAClF,oBAAI,UAAU,IAAI,IAAI,mBAAmB,GAAG;AAC1C,0BAAQ,IAAI,IAAI;AAChB,wBAAM,KAAK,UAAU,MAAM;AAC3B,sBAAI,CAAC,KAAK;AACR,yBAAK,IAAI,KAAK,MAAM,YAAY;AAAA,sBAC9B,KAAK;AAAA,oBACP,CAAC;AACD,2BAAO,CAAC,IAAI,KAAK,WAAW,KAAK,MAAM,EAAE,KAAK,SAAS,GAAG;AACxD,0BAAI,GAAG;AACL,8BAAM,OAAO,UAAU,MAAM,IAAI,EAAE;AACnC,+BAAO,cAAc,GAAG,MAAM;AAAA,sBAChC;AAAA,oBACF,CAAC;AAAA,kBACH;AAAA,gBACF;AACA,yBAAS,OAAO;AAChB,uBAAO,QAAQ,IAAI,MAAM;AAAA,cAC3B,KAAK;AACH,qCAAqB,SAAS;AAC9B,sCAAsB,eAAe,oBAAoB,CAAC;AAC1D,oBAAI,oBAAoB,CAAC;AACzB,oBAAI,GAAG;AACL,2BAAS,OAAO;AAChB;AAAA,gBACF;AACA,uBAAO,SAAS,OAAO,QAAQ;AAAA,cACjC,KAAK;AACH,uBAAO,EAAE;AACT,qBAAK,cAAc,GAAG,IAAI,GAAG;AAC7B,uBAAO,SAAS,OAAO,UAAU;AAAA,kBAC/B;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF,CAAC;AAAA,cACH,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,SAAS,KAAK;AAAA,YACzB;AAAA,QACJ,GAAG,SAAS,IAAI;AAAA,MAClB,CAAC,CAAC;AACF,eAAS,aAAa,IAAI,KAAK,KAAK;AAClC,eAAO,cAAc,MAAM,MAAM,SAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,WAAK,QAAQ;AACb,WAAK,YAAY,CAAC;AAClB,WAAK,YAAY,CAAC;AAClB,WAAK,kBAAkB,MAAM;AAAA,IAC/B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,UAAU,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC7F,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,QAAQ,IAAI,CAAC,KAAK,WAAW,OAAO,GAAG,KAAK,eAAe,OAAO,GAAG,KAAK,oBAAoB,OAAO,CAAC,CAAC;AAAA,cAChH,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,SAAS;AAChB,eAAO,QAAQ,MAAM,MAAM,SAAS;AAAA,MACtC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;Af/MF,IAAIC,UAAS,IAAI,OAAO,KAAK;AAC7B,IAAI,MAAsB,SAAS,eAAe;AAChD,YAAU,MAAM,aAAa;AAC7B,MAAI,SAAS,aAAa,IAAI;AAC9B,WAAS,KAAK,MAAM;AAClB,QAAI;AACJ,oBAAgB,MAAM,IAAI;AAC1B,YAAQ,OAAO,KAAK,IAAI;AACxB,oBAAgB,uBAAuB,KAAK,GAAG,WAAW,KAAK,OAAO;AACtE,oBAAgB,uBAAuB,KAAK,GAAG,SAAS,IAAI;AAC5D,oBAAgB,uBAAuB,KAAK,GAAG,UAAU,IAAI;AAC7D,oBAAgB,uBAAuB,KAAK,GAAG,mBAAmB,IAAI;AACtE,oBAAgB,uBAAuB,KAAK,GAAG,kBAAkB,IAAI;AACrE,oBAAgB,uBAAuB,KAAK,GAAG,aAAa,IAAI;AAChE,oBAAgB,uBAAuB,KAAK,GAAG,kBAAkB,IAAI;AACrE,oBAAgB,uBAAuB,KAAK,GAAG,eAAe,IAAI;AAClE,oBAAgB,uBAAuB,KAAK,GAAG,eAAe,IAAI;AAClE,oBAAgB,uBAAuB,KAAK,GAAG,UAAU,IAAI;AAC7D,oBAAgB,uBAAuB,KAAK,GAAG,cAAc,IAAI;AACjE,oBAAgB,uBAAuB,KAAK,GAAG,cAAc,IAAI;AACjE,oBAAgB,uBAAuB,KAAK,GAAG,cAAc,IAAI;AACjE,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,GAAG;AACnE,oBAAgB,uBAAuB,KAAK,GAAG,sBAAsB,KAAK;AAC1E,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,KAAK;AACrE,oBAAgB,uBAAuB,KAAK,GAAG,kBAAkB,IAAI;AACrE,oBAAgB,uBAAuB,KAAK,GAAG,2BAA2B,KAAK;AAC/E,oBAAgB,uBAAuB,KAAK,GAAG,gBAAgC,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,UAAU;AAC7J,UAAI,iBAAiB,aAAa,aAAa,uBAAuB,QAAQ,kBAAkB,qBAAqB,OAAO;AAC5H,aAAO,oBAAoB,EAAE,KAAK,SAAS,SAAS,UAAU;AAC5D,eAAO;AACL,kBAAQ,SAAS,OAAO,SAAS,MAAM;AAAA,YACrC,KAAK;AACH,kBAAI,EAAE,MAAM,sBAAsB,CAAC,MAAM,QAAQ;AAC/C,yBAAS,OAAO;AAChB;AAAA,cACF;AACA,qBAAO,SAAS,OAAO,QAAQ;AAAA,YACjC,KAAK;AACH,gCAAkB,MAAM,WAAW,cAAc,gBAAgB,aAAa,cAAc,gBAAgB;AAC5G,sCAAwB,uBAAuB,KAAK,GAAG,SAAS,sBAAsB;AACtF,iCAAmB;AACnB,oCAAsB,KAAK,IAAI,KAAK,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,YAAY,mBAAmB,KAAK,GAAG,gBAAgB,GAAG,GAAG;AAC5K,kBAAI,aAAa;AACf,yBAAS,OAAO;AAChB;AAAA,cACF;AACA,qBAAO,SAAS,OAAO,QAAQ;AAAA,YACjC,KAAK;AACH,kBAAI,MAAM,QAAQ;AAChB,yBAAS,OAAO;AAChB;AAAA,cACF;AACA,sBAAQ,MAAM,WAAW;AACzB,kBAAI,MAAM,MAAM,UAAU,CAAC,MAAM,MAAM,aAAa;AAClD,wBAAQ,MAAM,WAAW,MAAM,aAAa,GAAG;AAAA,cACjD;AACA,iCAAmB,KAAK,IAAI,MAAM,MAAM,MAAM,MAAM,QAAQ,IAAI;AAChE,kBAAI,EAAE,MAAM,aAAa,OAAO,eAAe,mBAAmB;AAChE,yBAAS,OAAO;AAChB;AAAA,cACF;AACA,oBAAM,QAAQ;AACd,qBAAO,SAAS,OAAO,QAAQ;AAAA,YACjC,KAAK;AACH,kBAAI,EAAE,OAAO,sBAAsB,CAAC,MAAM,eAAe,cAAc;AACrE,yBAAS,OAAO;AAChB;AAAA,cACF;AACA,qBAAO,SAAS,OAAO,QAAQ;AAAA,YACjC,KAAK;AACH,kBAAI,CAAC,MAAM,iBAAiB,MAAM,eAAe,YAAY,KAAK,KAAK,MAAM,OAAO,KAAK,IAAI,YAAY,QAAQ,MAAM,GAAG,IAAI,GAAG;AAC/H,sBAAM,UAAU,sBAAsB,MAAM,UAAU,uBAAuB,MAAM,MAAM,GAAG,CAAC;AAAA,cAC/F;AAAA,YACF,KAAK;AACH,qBAAO,SAAS,OAAO,UAAU,MAAM,mBAAmB,CAAC;AAAA,YAC7D,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,SAAS,KAAK;AAAA,UACzB;AAAA,MACJ,GAAG,OAAO;AAAA,IACZ,CAAC,CAAC,CAAC;AACH,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,WAAW;AACzE,UAAI,MAAM,UAAU,CAAC,MAAM,OAAO,eAAe;AAC/C,YAAI,MAAM,MAAM,aAAa,UAAU;AACrC,gBAAM,eAAe,eAAe,QAAQ,EAAE,MAAM,SAAS,GAAG;AAAA,UAChE,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,WAA2B,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AACzJ,aAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,eAAO;AACL,kBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,YACvC,KAAK;AACH,kBAAI,EAAE,MAAM,MAAM,WAAW,MAAM,MAAM,gBAAgB,IAAI;AAC3D,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,cAAAA,QAAO,MAAM,8BAA8B;AAC3C,qBAAO,UAAU,OAAO,QAAQ;AAAA,YAClC,KAAK;AACH,2BAAa,MAAM,gBAAgB;AACnC,kBAAI,CAAC,MAAM,eAAe;AACxB,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,oBAAM,gBAAgB;AACtB,oBAAM,OAAO,IAAI;AACjB,wBAAU,OAAO;AACjB;AAAA,YACF,KAAK;AACH,wBAAU,OAAO;AACjB,qBAAO,MAAM,aAAa;AAAA,YAC5B,KAAK;AACH,oBAAM,WAAW;AAAA,YACnB,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,UAAU,KAAK;AAAA,UAC1B;AAAA,MACJ,GAAG,QAAQ;AAAA,IACb,CAAC,CAAC,CAAC;AACH,oBAAgB,uBAAuB,KAAK,GAAG,YAAY,WAAW;AACpE,UAAI,MAAM,QAAQ;AAChB,YAAI,CAAC,MAAM,eAAe;AACxB,cAAI,iBAAiB,MAAM,OAAO;AAClC,cAAI,mBAAmB,QAAQ,mBAAmB;AAChD,6BAAiB,MAAM,UAAU;AACnC,cAAI,CAAC,OAAO,SAAS,cAAc;AACjC;AACF,uBAAa,MAAM,gBAAgB;AACnC,gBAAM,mBAAmB,WAAW,WAAW;AAC7C,kBAAM,gBAAgB;AACtB,kBAAM,OAAO;AAAA,UACf,GAAG,kBAAkB,CAAC;AAAA,QACxB;AAAA,MACF,OAAO;AACL,cAAM,UAAU;AAAA,MAClB;AAAA,IACF,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,cAA8B,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC5J,UAAI,UAAU,WAAW,aAAa,QAAQ,MAAM,UAAU;AAC9D,aAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,eAAO;AACL,kBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,YACvC,KAAK;AACH,kBAAI,MAAM,OAAO;AACf,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,qBAAO,UAAU,OAAO,QAAQ;AAAA,YAClC,KAAK;AACH,oBAAM,sBAAsB;AAC5B,yBAAW,MAAM,MAAM;AACvB,0BAAY,MAAM,UAAU;AAC5B,kBAAI,CAAC,WAAW;AACd,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,4BAAc,MAAM,UAAU,UAAU,CAAC,GAAG,MAAM,SAAS,UAAU,CAAC,IAAI,MAAM,MAAM,QAAQ;AAC9F,kBAAI,EAAE,eAAe,KAAK,KAAK,IAAI,WAAW,WAAW,KAAK,MAAM;AAClE,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,oBAAM,MAAM,cAAc;AAC1B,qBAAO,UAAU,OAAO,QAAQ;AAAA,YAClC,KAAK;AACH,uBAAS,MAAM,UAAU;AACzB,qBAAO,OAAO,KAAK,OAAO,IAAI,MAAM,KAAK,GAAG,UAAU,GAAG;AACzD,kBAAI,CAAC,QAAQ;AACX,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,kBAAI,EAAE,KAAK,OAAO,KAAK,IAAI,KAAK,MAAM,OAAO,GAAG,IAAI,MAAM;AACxD,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,qBAAO,UAAU,OAAO,QAAQ;AAAA,YAClC,KAAK;AACH,kBAAI,EAAE,MAAM,UAAU,KAAK,MAAM;AAC/B,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,qBAAO,UAAU,OAAO,QAAQ;AAAA,YAClC,KAAK;AACH,yBAAW,MAAM,UAAU,uBAAuB,QAAQ;AAC1D,oBAAM,MAAM,UAAU,kBAAkB,QAAQ;AAChD,kBAAI,EAAE,aAAa,QAAQ,aAAa,UAAU,CAAC,OAAO,MAAM,sBAAsB,QAAQ,MAAM,UAAU,cAAc;AAC1H,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,qBAAO,UAAU,OAAO,QAAQ;AAAA,YAClC,KAAK;AACH,cAAAA,QAAO,MAAM,WAAW,UAAU,GAAG;AACrC,oBAAM,UAAU,sBAAsB,QAAQ;AAC9C,oBAAM,UAAU;AAChB,wBAAU,OAAO;AACjB,qBAAO,MAAM,eAAe,OAAO;AAAA,YACrC,KAAK;AACH,oBAAM,qBAAqB;AAC3B,kBAAI,EAAE,CAAC,KAAK,OAAO,MAAM,SAAS;AAChC,0BAAU,OAAO;AACjB;AAAA,cACF;AACA,wBAAU,OAAO;AACjB,qBAAO,MAAM,mBAAmB,IAAI;AAAA,YACtC,KAAK;AACH,oBAAM,WAAW;AAAA,YACnB,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,UAAU,KAAK;AAAA,UAC1B;AAAA,MACJ,GAAG,QAAQ;AAAA,IACb,CAAC,CAAC,CAAC;AACH,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,WAAW;AACzE,UAAI,CAAC,MAAM;AACT;AACF,UAAI,MAAM,MAAM;AAChB,UAAI,IAAI,UAAU,IAAI,cAAc,IAAI,iBAAiB,MAAM,OAAO;AACpE,YAAI,WAAW,MAAM,UAAU;AAC/B,YAAI,CAAC;AACH;AACF,YAAI,UAAU,WAAW,MAAM,MAAM;AACrC,YAAI,WAAW,IAAI,YAAY;AAC7B,UAAAA,QAAO,MAAM,6BAA6B,OAAO,MAAM,MAAM,aAAa,aAAa,EAAE,OAAO,UAAU,aAAa,EAAE,OAAO,OAAO,CAAC;AACxI,gBAAM,MAAM,cAAc,WAAW,IAAI;AAAA,QAC3C;AAAA,MACF;AACA,UAAI,IAAI,WAAW;AACjB,YAAI;AACJ,SAAC,oBAAoB,MAAM,iBAAiB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,MAAM,MAAM,MAAM,WAAW;AAAA,MAC7I;AACA,UAAI,MAAM,OAAO,4BAA4B,CAAC,MAAM,OAAO,YAAY;AACrE,cAAM,wBAAwB,MAAM,MAAM,WAAW;AAAA,MACvD;AAAA,IACF,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,SAAS,WAAW;AACjE,UAAI,CAAC,MAAM;AACT;AACF,YAAM,WAAW;AACjB,UAAI,QAAQ,MAAM;AAClB,UAAI,WAAW,OAAO,IAAI,KAAK;AAC/B,UAAI,iBAAiB,MAAM,eAAe;AAC1C,YAAM,sBAAsB;AAC5B,UAAI,MAAM,yBAAyB;AACjC,YAAI,CAAC,MAAM,eAAe,OAAO,GAAG;AAClC,gBAAM,0BAA0B;AAChC,gBAAM,qBAAqB;AAAA,QAC7B;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,YAAI,yBAAyB;AAC7B,YAAI,CAAC,MAAM,cAAc,MAAM,WAAW,sBAAsB,EAAE,YAAY,GAAG;AAC/E,yBAAe,QAAQ;AACvB,gBAAM,WAAW,eAAe,QAAQ,cAAc,CAAC;AAAA,QACzD;AACA;AAAA,MACF;AACA,UAAI,OAAO,IAAI,QAAQ,KAAK,OAAO,MAAM,YAAY;AACnD,YAAI,eAAe,KAAK,GAAG;AACzB,gBAAM,aAAa;AACnB,cAAI,MAAM,aAAa;AACrB,kBAAM,YAAY,GAAG,OAAO,MAAM,OAAO,iBAAiB,MAAM,MAAM;AAAA,UACxE;AAAA,QACF,OAAO;AACL,cAAI,MAAM,aAAa,KAAK,MAAM,aAAa;AAC7C,kBAAM,YAAY,GAAG,OAAO,MAAM,OAAO,iBAAiB,CAAC,MAAM,cAAc,OAAO,MAAM,MAAM;AAAA,UACpG;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,MAAM,QAAQ;AACjB,cAAM,QAAQ;AAAA,MAChB;AAAA,IACF,CAAC;AACD,UAAM,SAAS,OAAO,UAAU,IAAI;AACpC,UAAM,QAAQ,MAAM,OAAO;AAC3B,UAAM,kBAAkB,IAAI,eAAe,uBAAuB,KAAK,CAAC;AACxE,UAAM,iBAAiB,IAAI,cAAc,uBAAuB,KAAK,CAAC;AACtE,UAAM,YAAY,IAAI,SAAS,uBAAuB,KAAK,CAAC;AAC5D,UAAM,iBAAiB,IAAI,cAAc,uBAAuB,KAAK,CAAC;AACtE,QAAI,KAAK,WAAW;AAClB,YAAM,cAAc,IAAI,WAAW,uBAAuB,KAAK,CAAC;AAAA,IAClE;AACA,QAAI,CAAC,KAAK;AACR,YAAM,cAAc,IAAI,WAAW;AACrC,UAAM,SAAS,IAAI,kBAAkB,uBAAuB,KAAK,GAAG,GAAG;AACvE,UAAM,MAAM,iBAAiB,cAAc,MAAM,aAAa;AAC9D,UAAM,MAAM,iBAAiB,QAAQ,MAAM,OAAO;AAClD,UAAM,MAAM,iBAAiB,SAAS,MAAM,QAAQ;AACpD,UAAM,MAAM,iBAAiB,WAAW,MAAM,UAAU;AACxD,UAAM,MAAM,iBAAiB,cAAc,MAAM,aAAa;AAC9D,WAAO;AAAA,EACT;AACA,eAAa,MAAM,CAAC;AAAA,IAClB,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,uBAAuB,KAAK,oBAAoB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI,YAAY,KAAK,gBAAgB,KAAK,UAAU,iBAAiB,KAAK,UAAU;AACpF,aAAO,YAAY,UAAU,QAAQ,UAAU,WAAW,IAAI;AAAA,IAChE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY;AAC1B,aAAO,KAAK,eAAe,UAAU;AAAA,IACvC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,UAAI;AACJ,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AAC/E,aAAO,OAAO,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI,cAAc,KAAK,WAAW,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa,OAAO;AAAA,IACtJ;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,aAAO,KAAK,OAAO,SAAS;AAAA,IAC9B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB;AAChC,aAAO,wBAAwB,KAAK,KAAK;AAAA,IAC3C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,QAAQ,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,WAAW;AAC3F,YAAI,KAAK,SAAS,UAAU,SAAS;AACrC,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,sBAAM,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI;AAC9D,0BAAU,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC;AACnE,2BAAW,OAAO,YAAY,YAAY,UAAU,CAAC,EAAE,YAAY,QAAQ,YAAY,UAAU,QAAQ;AACzG,oBAAI,QAAQ,OAAO,MAAM,YAAY,YAAY,QAAQ,YAAY,UAAU,QAAQ,mBAAmB;AACxG,uBAAK,gBAAgB;AACrB,uBAAK,iBAAiB;AACtB,uBAAK,OAAO,YAAY;AAAA,gBAC1B;AACA,oBAAI;AACF,uBAAK,OAAO,MAAM;AACpB,sBAAM,KAAK,OAAO;AAClB,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO,QAAQ;AAAA,cAC7B,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,UAAU,GAAG;AAAA,cAC3B,KAAK;AACH,qBAAK,WAAW;AAAA,cAClB,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,OAAO;AACd,eAAO,MAAM,MAAM,MAAM,SAAS;AAAA,MACpC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,aAAa,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,KAAK;AACnG,YAAI,UAAU,eAAe,UAAU,sBAAsB,uBAAuB,uBAAuB,uBAAuB,iBAAiB,QAAQ,SAAS,uBAAuB,WAAW;AACtM,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,oBAAI;AACF,sBAAI;AACF,0BAAM,IAAI,KAAK;AAAA,gBACnB,SAAS,GAAG;AAAA,gBACZ;AACA,oBAAI,KAAK;AACP,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,sBAAM,KAAK,WAAW,IAAI,eAAe,IAAI,OAAO,IAAI,UAAU,QAAQ,MAAM,MAAM,qBAAqB,CAAC;AAAA,cAC9G,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,UAAU,GAAG;AAAA,cAC3B,KAAK;AACH,2BAAW,UAAU;AACrB,gCAAgB,KAAK,UAAU;AAC/B,oBAAI,CAAC,KAAK,eAAe;AACvB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,oBAAI,CAAC,KAAK,QAAQ;AAChB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,2BAAW,KAAK,UAAU,mBAAmB,KAAK,UAAU;AAC5D,gBAAAA,QAAO,IAAI,cAAc,OAAO,KAAK,YAAY,YAAY,EAAE,OAAO,UAAU,sBAAsB,CAAC;AACvG,oBAAI,aAAa,IAAI;AACnB,uBAAK,aAAa;AAClB,uBAAK,aAAa;AAAA,gBACpB;AACA,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,oBAAI,cAAc,YAAY,MAAM,uBAAuB,KAAK,oBAAoB,QAAQ,yBAAyB,UAAU,qBAAqB,SAAS;AAC3J,gCAAc,WAAW,wBAAwB,KAAK,oBAAoB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAAA,gBACtJ;AACA,kCAAkB,SAAS,wBAAwB,KAAK,oBAAoB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,eAAe,YAAY,wBAAwB,KAAK,oBAAoB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,YAAY,KAAK,wBAAwB;AACrV,qBAAK,OAAO,YAAY;AACxB,yBAAS,KAAK,UAAU,uBAAuB,eAAe;AAC9D,0BAAU,KAAK,UAAU,kBAAkB,SAAS,CAAC;AACrD,oBAAI,CAAC,SAAS;AACZ,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,wCAAwB,QAAQ;AAChC,0BAAU,OAAO;AACjB,uBAAO,KAAK,eAAe,aAAa,qBAAqB;AAAA,cAC/D,KAAK;AACH,oBAAI,UAAU;AACZ,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,oBAAI,CAAC,KAAK,QAAQ;AAChB,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,qBAAK,eAAe,qBAAqB,GAAG,UAAU;AACtD,gBAAAA,QAAO,IAAI,iCAAiC,KAAK,UAAU,aAAa;AACxE,gBAAAA,QAAO,IAAI,oBAAoB,KAAK,UAAU,UAAU;AACxD,oBAAI,KAAK,OAAO,gBAAgB,KAAK,UAAU,eAAe;AAC5D,uBAAK,OAAO,gBAAgB,KAAK,UAAU;AAC3C,uBAAK,OAAO,aAAa,MAAM,KAAK,OAAO;AAAA,gBAC7C;AACA,oBAAI,CAAC,SAAS;AACZ,uBAAK,UAAU,GAAG;AACpB,oBAAI,EAAE,KAAK,UAAU,aAAa,KAAK,OAAO,uBAAuB;AACnE,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,aAAa;AAAA,cAC3B,KAAK;AACH,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,eAAe,eAAe,cAAc,aAAa;AAAA,cACvE,KAAK;AACH,4BAAY,KAAK,OAAO;AACxB,oBAAI,WAAW;AACb,sBAAI,GAAG,wBAAwB,KAAK,oBAAoB,QAAQ,0BAA0B,UAAU,sBAAsB,WAAW;AACnI,yBAAK,MAAM,cAAc;AAAA,kBAC3B;AACA,uBAAK,UAAU,sBAAsB,KAAK,UAAU,uBAAuB,SAAS,KAAK,CAAC;AAAA,gBAC5F;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,aAAa;AAAA,cAC3B,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,UAAU,IAAI;AACrB,eAAO,WAAW,MAAM,MAAM,SAAS;AAAA,MACzC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,UAAU,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,YAAY;AACvG,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,qBAAK,OAAO,YAAY;AACxB,qBAAK,gBAAgB;AACrB,qBAAK,iBAAiB;AACtB,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK;AAAA,cACnB,KAAK;AACH,qBAAK,gBAAgB;AACrB,uBAAO,UAAU,OAAO,UAAU,KAAK,MAAM,KAAK,CAAC,UAAU,CAAC;AAAA,cAChE,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,IAAI;AAAA,MACnB,CAAC,CAAC;AACF,eAAS,OAAO,KAAK;AACnB,eAAO,QAAQ,MAAM,MAAM,SAAS;AAAA,MACtC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,aAAa,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,KAAK;AACnG,YAAI,SAAS,aAAa,KAAK,UAAU,UAAU,WAAW,UAAU,SAAS;AACjF,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,0BAAU,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC;AACnE,8BAAc;AAAA,kBACZ,UAAU;AAAA,kBACV,WAAW;AAAA,kBACX,SAAS;AAAA,gBACX;AACA,0BAAU,KAAK,QAAQ,OAAO;AAC9B,0BAAU,OAAO,UAAU,OAAO,WAAW,IAAI,UAAU,OAAO,YAAY,IAAI,UAAU,OAAO,WAAW,IAAI;AAClH;AAAA,cACF,KAAK;AACH,0BAAU;AAAA,kBACR,WAAW;AAAA,gBACb;AACA,uBAAO,UAAU,OAAO,SAAS,EAAE;AAAA,cACrC,KAAK;AACH,0BAAU;AAAA,kBACR,UAAU;AAAA,gBACZ;AACA,uBAAO,UAAU,OAAO,SAAS,EAAE;AAAA,cACrC,KAAK;AACH,qBAAK,OAAO,SAAS;AACnB,sBAAI,QAAQ,GAAG,MAAM,UAAU,QAAQ,GAAG,MAAM,MAAM;AACpD,2BAAO,QAAQ,GAAG;AAAA,kBACpB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,SAAS,EAAE;AAAA,cACrC,KAAK;AACH,sBAAM,+BAA+B,OAAO,OAAO;AAAA,cACrD,KAAK;AACH,0BAAU,OAAO,OAAO,CAAC,GAAG,aAAa,OAAO;AAChD,2BAAW,SAAS,WAAW,SAAS,UAAU,YAAY,SAAS;AACvE,qBAAK,OAAO,MAAM;AAClB,qBAAK,OAAO,YAAY;AACxB,qBAAK,iBAAiB;AACtB,oBAAI,UAAU;AACZ,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,oBAAI,CAAC,KAAK,OAAO,YAAY;AAC3B,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,KAAK,KAAK,KAAK,GAAG;AAC5B,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,KAAK,GAAG;AAAA,cACtB,KAAK;AACH,0BAAU,KAAK,UAAU;AAAA,cAC3B,KAAK;AACH,2BAAW,UAAU;AACrB,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,KAAK,UAAU,OAAO,EAAE,EAAE;AACpC,qBAAK,KAAK,MAAM,mBAAmB,UAAU,EAAE;AAC/C,sBAAM,UAAU;AAAA,cAClB,KAAK;AACH,qBAAK,gBAAgB;AACrB,oBAAI,UAAU;AACZ,uBAAK,KAAK,MAAM,oBAAoB;AAAA,oBAClC;AAAA,kBACF,CAAC;AAAA,gBACH;AACA,uBAAO,UAAU,OAAO,UAAU,KAAK,MAAM,KAAK,IAAI,CAAC;AAAA,cACzD,KAAK;AACH,qBAAK,gBAAgB;AACrB,oBAAI,CAAC,KAAK,QAAQ;AAChB,uBAAK,aAAa;AAClB,uBAAK,aAAa;AAAA,gBACpB;AACA,qBAAK,UAAU,MAAM;AACrB,qBAAK,eAAe,eAAe;AACnC,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO;AAAA,cACrB,KAAK;AACH,0BAAU,OAAO;AACjB,uBAAO,KAAK,UAAU,GAAG;AAAA,cAC3B,KAAK;AACH,qBAAK,WAAW;AAAA,cAClB,KAAK;AACH,qBAAK,iBAAiB;AAAA,cACxB,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAAA,MAC/B,CAAC,CAAC;AACF,eAAS,UAAU,KAAK;AACtB,eAAO,WAAW,MAAM,MAAM,SAAS;AAAA,MACzC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,gBAAgB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,IAAI;AACrG,YAAI,OAAO,WAAW,SAAS,UAAU,OAAO,SAAS;AACzD,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,wBAAQ,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI;AAChE,4BAAY,KAAK;AACjB,0BAAU,KAAK;AACf,oBAAI,EAAE,CAAC,aAAa,UAAU,OAAO,MAAM,CAAC,WAAW,QAAQ,SAAS,IAAI;AAC1E,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,2BAAW,QAAQ,KAAK,SAAS,GAAG;AAClC,yBAAO,EAAE,OAAO;AAAA,gBAClB,CAAC;AACD,oBAAI,UAAU;AACZ,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO;AAAA,cACrB,KAAK;AACH,oBAAI,CAAC,OAAO;AACV,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,eAAe,eAAe;AAAA,cAC5C,KAAK;AACH,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,KAAK,UAAU,OAAO,EAAE,CAAC;AACnC,sBAAM,KAAK,WAAW,eAAe,OAAO,UAAU,EAAE,CAAC;AAAA,cAC3D,KAAK;AACH,oBAAI,UAAU,sBAAsB,SAAS,aAAa,SAAS,GAAG;AACpE,0BAAQ,UAAU,mBAAmB;AACrC,2BAAS,qBAAqB,SAAS,aAAa,KAAK,SAAS,GAAG;AACnE,2BAAO,EAAE,OAAO;AAAA,kBAClB,CAAC,KAAK,SAAS;AAAA,gBACjB;AACA,qBAAK,UAAU,gBAAgB;AAC/B,0BAAU,OAAO;AACjB,oBAAI,EAAE,KAAK,UAAU,CAAC,SAAS,SAAS,SAAS;AAC/C,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,aAAa;AAAA,cAC3B,KAAK;AACH,qBAAK,UAAU,sBAAsB,KAAK,UAAU,uBAAuB,KAAK,MAAM,WAAW,KAAK,CAAC;AACvG,qBAAK,aAAa;AAClB,0BAAU,OAAO;AACjB,uBAAO,KAAK,mBAAmB;AAAA,cACjC,KAAK;AACH,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,KAAK,UAAU,OAAO,EAAE,EAAE;AACpC,qBAAK,UAAU,gBAAgB;AAC/B,sBAAM,UAAU;AAAA,cAClB,KAAK;AACH,qBAAK,WAAW;AAChB,uBAAO,UAAU,OAAO,UAAU,QAAQ;AAAA,cAC5C,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AAAA,MACxC,CAAC,CAAC;AACF,eAAS,aAAa,KAAK;AACzB,eAAO,cAAc,MAAM,MAAM,SAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,qBAAqB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,SAAS,IAAI;AAC1G,YAAI,OAAO,WAAW,aAAa,UAAU,SAAS;AACtD,eAAO,oBAAoB,EAAE,KAAK,SAAS,UAAU,WAAW;AAC9D,iBAAO;AACL,oBAAQ,UAAU,OAAO,UAAU,MAAM;AAAA,cACvC,KAAK;AACH,wBAAQ,OAAO,SAAS,KAAK,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI;AAChE,4BAAY,KAAK;AACjB,oBAAI,WAAW;AACb,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,8BAAc,UAAU;AACxB,oBAAI,EAAE,CAAC,eAAe,YAAY,OAAO,MAAM,UAAU,aAAa,SAAS,IAAI;AACjF,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,2BAAW,UAAU,aAAa,KAAK,SAAS,GAAG;AACjD,yBAAO,EAAE,OAAO;AAAA,gBAClB,CAAC;AACD,oBAAI,UAAU;AACZ,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,uBAAO,UAAU,OAAO,QAAQ;AAAA,cAClC,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,OAAO;AACjB,uBAAO,KAAK,OAAO;AAAA,cACrB,KAAK;AACH,oBAAI,CAAC,OAAO;AACV,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,eAAe,eAAe;AAAA,cAC5C,KAAK;AACH,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,KAAK,UAAU,OAAO,EAAE,EAAE;AACpC,sBAAM,KAAK,WAAW,eAAe,OAAO,UAAU,EAAE,CAAC;AAAA,cAC3D,KAAK;AACH,0BAAU,qBAAqB;AAC/B,0BAAU,OAAO;AACjB,oBAAI,EAAE,KAAK,UAAU,CAAC,SAAS,SAAS,SAAS;AAC/C,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA,0BAAU,OAAO;AACjB,uBAAO,KAAK,aAAa;AAAA,cAC3B,KAAK;AACH,qBAAK,UAAU,sBAAsB,KAAK,UAAU,uBAAuB,KAAK,MAAM,WAAW,KAAK,CAAC;AACvG,qBAAK,aAAa;AAClB,0BAAU,OAAO;AACjB,uBAAO,KAAK,mBAAmB;AAAA,cACjC,KAAK;AACH,0BAAU,OAAO;AACjB;AAAA,cACF,KAAK;AACH,0BAAU,OAAO;AACjB,0BAAU,KAAK,UAAU,OAAO,EAAE,EAAE;AACpC,0BAAU,qBAAqB;AAC/B,sBAAM,UAAU;AAAA,cAClB,KAAK;AACH,qBAAK,WAAW;AAChB,uBAAO,UAAU,OAAO,UAAU,QAAQ;AAAA,cAC5C,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,UAAU,KAAK;AAAA,YAC1B;AAAA,QACJ,GAAG,UAAU,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AAAA,MACzC,CAAC,CAAC;AACF,eAAS,kBAAkB,KAAK;AAC9B,eAAO,mBAAmB,MAAM,MAAM,SAAS;AAAA,MACjD;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,wBAAwB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,UAAU,MAAM;AAChH,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,qBAAK,UAAU,eAAe,IAAI;AAClC,2BAAW,OAAO;AAClB,uBAAO,KAAK,gBAAgB,SAAS;AAAA,cACvC,KAAK;AACH,2BAAW,OAAO;AAClB,uBAAO,KAAK,aAAa;AAAA,cAC3B,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,IAAI;AAAA,MACpB,CAAC,CAAC;AACF,eAAS,qBAAqB,KAAK;AACjC,eAAO,sBAAsB,MAAM,MAAM,SAAS;AAAA,MACpD;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,eAAe,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,YAAY;AACnG,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,oBAAI,CAAC,KAAK,gBAAgB;AACxB,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,2BAAW,OAAO;AAClB,uBAAO,KAAK,eAAe,YAAY;AAAA,cACzC,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,IAAI;AAAA,MACpB,CAAC,CAAC;AACF,eAAS,cAAc;AACrB,eAAO,aAAa,MAAM,MAAM,SAAS;AAAA,MAC3C;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,WAAW,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,YAAY;AAC/F,YAAI;AACJ,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,oBAAI,KAAK,OAAO;AACd,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,uBAAO,WAAW,OAAO,QAAQ;AAAA,cACnC,KAAK;AACH,qBAAK,mBAAmB;AACxB,qBAAK,UAAU,MAAM;AACrB,qBAAK,eAAe,MAAM;AAC1B,iBAAC,qBAAqB,KAAK,iBAAiB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,MAAM;AACtH,qBAAK,MAAM,oBAAoB,cAAc,KAAK,aAAa;AAC/D,qBAAK,MAAM,oBAAoB,QAAQ,KAAK,OAAO;AACnD,qBAAK,MAAM,oBAAoB,SAAS,KAAK,QAAQ;AACrD,qBAAK,MAAM,oBAAoB,WAAW,KAAK,UAAU;AACzD,qBAAK,MAAM,oBAAoB,cAAc,KAAK,aAAa;AAC/D,2BAAW,OAAO;AAClB,uBAAO,QAAQ,IAAI,CAAC,KAAK,OAAO,GAAG,KAAK,eAAe,QAAQ,CAAC,CAAC;AAAA,cACnE,KAAK;AACH,qBAAK,QAAQ;AAAA,cACf,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,IAAI;AAAA,MACpB,CAAC,CAAC;AACF,eAAS,UAAU;AACjB,eAAO,SAAS,MAAM,MAAM,SAAS;AAAA,MACvC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,WAAW,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,UAAU,KAAK;AAClG,YAAI,UAAU,uBAAuB,wBAAwB,UAAU,OAAO,OAAO;AACrF,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,2BAAW,OAAO;AAClB,4BAAY,wBAAwB,KAAK,OAAO,kBAAkB,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,OAAO,SAAS,GAAG;AACvL,yBAAO,EAAE,QAAQ;AAAA,gBACnB,CAAC,EAAE,CAAC,OAAO,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB;AACvF,oBAAI,CAAC,UAAU;AACb,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,2BAAW,KAAK,KAAK,gBAAgB,UAAU,UAAU,GAAG;AAC5D,2BAAW,OAAO;AAClB;AAAA,cACF,KAAK;AACH,2BAAW,OAAO;AAClB,uBAAO,KAAK,gBAAgB,KAAK,GAAG;AAAA,cACtC,KAAK;AACH,2BAAW,KAAK,WAAW;AAAA,cAC7B,KAAK;AACH,wBAAQ,WAAW;AACnB,wBAAQ,eAAe,OAAO,CAAC;AAC/B,2BAAW,MAAM,CAAC;AAClB,2BAAW,OAAO;AAClB;AAAA,cACF,KAAK;AACH,2BAAW,OAAO;AAClB,2BAAW,KAAK,WAAW,OAAO,EAAE,CAAC;AACrC,sBAAM,KAAK,WAAW,eAAe,OAAO,WAAW,EAAE,CAAC;AAAA,cAC5D,KAAK;AACH,oBAAI,UAAU;AACZ,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,uBAAO,WAAW,OAAO,QAAQ;AAAA,cACnC,KAAK;AACH,qBAAK,UAAU,eAAe,QAAQ;AACtC,oBAAI,CAAC,SAAS,UAAU;AACtB,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,qBAAK,wBAAwB,KAAK,UAAU,cAAc,qBAAqB,QAAQ,0BAA0B,UAAU,sBAAsB,QAAQ;AACvJ,uBAAK,KAAK,MAAM,mBAAmB;AAAA,oBACjC,MAAM,KAAK,UAAU,cAAc;AAAA,kBACrC,CAAC;AAAA,gBACH;AACA,2BAAW,OAAO;AAClB,uBAAO,KAAK,aAAa;AAAA,cAC3B,KAAK;AACH,qBAAK,KAAK,MAAM,aAAa;AAC7B,uBAAO,WAAW,OAAO,UAAU,QAAQ;AAAA,cAC7C,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,MAC/B,CAAC,CAAC;AACF,eAAS,UAAU,KAAK;AACtB,eAAO,SAAS,MAAM,MAAM,SAAS;AAAA,MACvC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,UAAI,uBAAuB,uBAAuB,SAAS;AAC3D,UAAI,SAAS,KAAK,UAAU;AAC5B,UAAI,CAAC,UAAU,CAAC,OAAO;AACrB,cAAM,KAAK,WAAW,eAAe,OAAO,MAAM,MAAM,IAAI,MAAM,yBAAyB,CAAC,CAAC;AAC/F,UAAI,MAAM,OAAO;AACjB,UAAI,YAAY,wBAAwB,OAAO,wBAAwB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AACjJ,UAAI,eAAe,wBAAwB,OAAO,2BAA2B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AACvJ,aAAO,KAAK,gBAAgB,KAAK,KAAK,UAAU,WAAW,EAAE,KAAK,SAAS,OAAO;AAChF,YAAI,QAAQ,eAAe,OAAO,CAAC,GAAG,gBAAgB,MAAM,CAAC,GAAG,gBAAgB,MAAM,CAAC,GAAG,mBAAmB,MAAM,CAAC;AACpH,YAAI,CAAC;AACH;AACF,eAAO,UAAU,eAAe,eAAe,eAAe,gBAAgB;AAC9E,YAAI,CAAC,OAAO;AACV;AACF,eAAO,UAAU,KAAK,UAAU,WAAW;AAAA,MAC7C,CAAC,EAAE,MAAM,SAAS,KAAK;AACrB,cAAM,OAAO,WAAW,eAAe,OAAO,GAAG,CAAC;AAAA,MACpD,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,KAAK,UAAU,aAAa;AACpD,UAAI,SAAS;AACb,UAAI,UAAU,KAAK,UAAU;AAC7B,UAAI;AACJ,UAAI,KAAK,UAAU,YAAY;AAC7B,wBAAgB,KAAK,UAAU,cAAc,sBAAsB,KAAK;AAAA,MAC1E,OAAO;AACL,YAAI;AACJ,0BAAkB,wBAAwB,KAAK,UAAU,iBAAiB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,aAAa,KAAK;AAAA,MACxK;AACA,WAAK,gBAAgB;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,IAAI,IAAI,IAAI;AACnB,iBAAO,UAAU,eAAe,IAAI,IAAI,EAAE;AAC1C,iBAAO,UAAU,gBAAgB;AACjC,cAAI,kBAAkB,MAAM,WAAW,CAAC,OAAO,UAAU;AACzD,cAAI,mBAAmB,CAAC,OAAO,UAAU,oBAAoB,OAAO,UAAU,cAAc,OAAO,OAAO,sBAAsB;AAC9H,mBAAO,aAAa;AAAA,UACtB;AACA,cAAI;AACF,sBAAU,OAAO,UAAU;AAAA,QAC/B;AAAA,QACA,SAAS,KAAK;AACZ,iBAAO,WAAW,eAAe,OAAO,GAAG,CAAC;AAAA,QAC9C;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,sBAAsB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,UAAU,UAAU;AAClH,YAAI,KAAK,UAAU,aAAa,wBAAwB,WAAW;AACnE,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,sBAAM,KAAK,UAAU;AACrB,oBAAI,KAAK;AACP,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,uBAAO,WAAW,OAAO,QAAQ;AAAA,cACnC,KAAK;AACH,2BAAW;AACX,8BAAc;AACd,2BAAW,OAAO;AAClB,qBAAK,qBAAqB;AAC1B,gBAAAA,QAAO,IAAI,oBAAoB,OAAO,IAAI,IAAI,KAAK,EAAE,OAAO,IAAI,OAAO,IAAI,EAAE,OAAO,IAAI,KAAK,eAAe,EAAE,OAAO,IAAI,SAAS,CAAC;AACnI,2BAAW,OAAO;AAClB,uBAAO,KAAK,qBAAqB,KAAK,KAAK,UAAU,gBAAgB,GAAG,CAAC;AAAA,cAC3E,KAAK;AACH,2BAAW,WAAW;AACtB,2BAAW,OAAO;AAClB;AAAA,cACF,KAAK;AACH,2BAAW,OAAO;AAClB,2BAAW,KAAK,WAAW,OAAO,EAAE,CAAC;AACrC,8BAAc,WAAW;AAAA,cAC3B,KAAK;AACH,2BAAW,OAAO;AAClB,qBAAK,qBAAqB;AAC1B,uBAAO,WAAW,OAAO,EAAE;AAAA,cAC7B,KAAK;AACH,oBAAI,CAAC,aAAa;AAChB,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,oBAAI,CAAC,KAAK,eAAe,OAAO,GAAG;AACjC,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,gBAAAA,QAAO,IAAI,oBAAoB,OAAO,IAAI,IAAI,cAAc,EAAE,OAAO,IAAI,SAAS,CAAC;AACnF,qBAAK,qBAAqB;AAC1B,qBAAK,0BAA0B;AAC/B,uBAAO,WAAW,OAAO,UAAU,KAAK;AAAA,cAC1C,KAAK;AACH,uBAAO,WAAW,OAAO,UAAU,KAAK,WAAW,eAAe,OAAO,WAAW,CAAC,CAAC;AAAA,cACxF,KAAK;AACH,oBAAI,UAAU;AACZ,8BAAY,KAAK,WAAW,EAAE;AAC9B,sBAAI,KAAK,UAAU,CAAC,KAAK,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,MAAM,SAAS,IAAI,GAAG;AACxF,oBAAAA,QAAO,KAAK,YAAY,OAAO,IAAI,IAAI,gBAAgB,EAAE,OAAO,IAAI,KAAK,aAAa,EAAE,OAAO,SAAS,CAAC;AACzG,yBAAK,UAAU,iBAAiB,KAAK,SAAS;AAAA,kBAChD;AACA,iCAAe,yBAAyB,KAAK,UAAU,mBAAmB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,SAAS,IAAI;AACnK,sBAAI,KAAK,iBAAiB,CAAC,YAAY;AACrC,oBAAAA,QAAO,KAAK,gCAAgC;AAC5C,yBAAK,eAAe,eAAe;AAAA,kBACrC;AACA,sBAAI,KAAK,UAAU,KAAK,iBAAiB,YAAY;AACnD,yBAAK,gBAAgB;AACrB,yBAAK,KAAK,MAAM,oBAAoB;AAAA,sBAClC,KAAK,KAAK,OAAO;AAAA,oBACnB,CAAC;AAAA,kBACH;AACA,uBAAK,UAAU,mBAAmB;AAClC,sBAAI,IAAI,QAAQ;AACd,yBAAK,KAAK;AAAA,kBACZ,WAAW,CAAC,UAAU;AACpB,yBAAK,aAAa;AAAA,kBACpB;AAAA,gBACF;AACA,uBAAO,WAAW,OAAO,UAAU,QAAQ;AAAA,cAC7C,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,MAAM,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC;AAAA,MACvC,CAAC,CAAC;AACF,eAAS,mBAAmB,KAAK;AAC/B,eAAO,oBAAoB,MAAM,MAAM,SAAS;AAAA,MAClD;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,wBAAwB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,UAAU,KAAK,UAAU;AACzH,YAAI;AACJ,YAAI,IAAI,eAAe,WAAW,MAAM,IAAI,OAAO,QAAQ,QAAQ,YAAY;AAC/E,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,qBAAK,MAAM,IAAI,KAAK,SAAS;AAC7B,gCAAgB,KAAK,eAAe;AACpC,4BAAY,CAAC;AACb,2BAAW,OAAO;AAClB,2BAAW,OAAO;AAClB,uBAAO,KAAK,eAAe,KAAK,KAAK,UAAU,aAAa;AAAA,cAC9D,KAAK;AACH,4BAAY,WAAW;AACvB,2BAAW,OAAO;AAClB;AAAA,cACF,KAAK;AACH,2BAAW,OAAO;AAClB,2BAAW,KAAK,WAAW,OAAO,EAAE,CAAC;AACrC,2BAAW,GAAG,QAAQ;AACtB,qBAAK,eAAe,QAAQ,WAAW;AACvC,sBAAM,WAAW;AAAA,cACnB,KAAK;AACH,oBAAI,UAAU,CAAC,GAAG;AAChB,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,uBAAO,WAAW,OAAO,QAAQ;AAAA,cACnC,KAAK;AACH,2BAAW,OAAO;AAClB,wBAAQ,wBAAwB,KAAK,gBAAgB,cAAc,MAAM,uBAAuB,mBAAmB,SAAS,CAAC;AAAA,cAC/H,KAAK;AACH,uBAAO,WAAW;AAClB,oBAAI,MAAM;AACR,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,uBAAO,WAAW,OAAO,QAAQ;AAAA,cACnC,KAAK;AACH,qBAAK,MAAM,IAAI,KAAK,SAAS;AAC7B,wBAAQ,MAAM,IAAI,QAAQ,SAAS;AACnC,yBAAS,KAAK,UAAU;AACxB,qBAAK,eAAe,aAAa,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU;AACvL,yBAAS,KAAK,IAAI;AAClB,6BAAa,KAAK,eAAe,KAAK;AACtC,oBAAI,KAAK,UAAU,KAAK,eAAe;AACrC,6BAAW,KAAK,WAAW,EAAE;AAC7B,uBAAK,UAAU,qBAAqB,IAAI,QAAQ;AAChD,kBAAAA,QAAO,KAAK,gDAAgD,OAAO,IAAI,iBAAiB,EAAE,OAAO,UAAU,aAAa,EAAE,OAAO,KAAK,UAAU,QAAQ,CAAC;AACzJ,0BAAQ;AAAA,gBACV;AACA,2BAAW,OAAO;AAClB,uBAAO,KAAK,eAAe,aAAa,KAAK,UAAU,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,eAAe,YAAY,KAAK;AAAA,cAC3G,KAAK;AACH,qBAAK,KAAK,MAAM,aAAa;AAAA,kBAC3B,SAAS,KAAK,IAAI,IAAI;AAAA,kBACtB,KAAK,IAAI;AAAA,gBACX,CAAC;AACD,2BAAW,OAAO;AAClB,uBAAO,KAAK,eAAe,YAAY,KAAK,OAAO,YAAY;AAAA,cACjE,KAAK;AACH,qBAAK,aAAa;AAClB,qBAAK,aAAa;AAClB,uBAAO,WAAW,OAAO,UAAU,IAAI;AAAA,cACzC,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,MAC9B,CAAC,CAAC;AACF,eAAS,qBAAqB,KAAK,MAAM;AACvC,eAAO,sBAAsB,MAAM,MAAM,SAAS;AAAA,MACpD;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,yBAAyB,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,YAAY;AAC7G,YAAI,UAAU,UAAU,YAAY,GAAG,cAAc;AACrD,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,2BAAW,KAAK,MAAM;AACtB,2BAAW,KAAK,MAAM;AACtB,6BAAa;AACb,oBAAI;AAAA,cACN,KAAK;AACH,oBAAI,EAAE,IAAI,SAAS,SAAS;AAC1B,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,oBAAI,EAAE,SAAS,MAAM,CAAC,KAAK,YAAY,WAAW,SAAS,IAAI,CAAC,IAAI;AAClE,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,6BAAa;AACb,uBAAO,WAAW,OAAO,SAAS,EAAE;AAAA,cACtC,KAAK;AACH;AACA,2BAAW,OAAO;AAClB;AAAA,cACF,KAAK;AACH,oBAAI,CAAC,KAAK,eAAe,OAAO,GAAG;AACjC,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,+BAAe,aAAa,KAAK,OAAO,eAAe;AACvD,4BAAY,KAAK,MAAM;AACvB,oBAAI,EAAE,YAAY,eAAe,IAAI;AACnC,6BAAW,OAAO;AAClB;AAAA,gBACF;AACA,2BAAW,OAAO;AAClB,uBAAO,KAAK,eAAe,aAAa,GAAG,YAAY,YAAY;AAAA,cACrE,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,IAAI;AAAA,MACpB,CAAC,CAAC;AACF,eAAS,wBAAwB;AAC/B,eAAO,uBAAuB,MAAM,MAAM,SAAS;AAAA,MACrD;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,wBAAwB,MAAM;AAC5C,UAAI,aAAa,KAAK,UAAU,wBAAwB,MAAM,KAAK,eAAe,IAAI;AACtF,UAAI,CAAC;AACH;AACF,WAAK,UAAU,KAAK,OAAO,KAAK,WAAW,QAAQ,GAAG;AAAA,IACxD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,UAAU,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,YAAY;AAC9F,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,6BAAa,KAAK,gBAAgB;AAClC,qBAAK,UAAU;AACf,2BAAW,OAAO;AAClB,uBAAO,QAAQ,IAAI,CAAC,KAAK,eAAe,OAAO,GAAG,KAAK,gBAAgB,SAAS,CAAC,CAAC;AAAA,cACpF,KAAK;AACH,qBAAK,qBAAqB;AAAA,cAC5B,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,IAAI;AAAA,MACpB,CAAC,CAAC;AACF,eAAS,SAAS;AAChB,eAAO,QAAQ,MAAM,MAAM,SAAS;AAAA,MACtC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,UAAU,kBAAkC,oBAAoB,EAAE,KAAK,SAAS,YAAY;AAC9F,YAAI;AACJ,YAAI,UAAU,UAAU;AACxB,eAAO,oBAAoB,EAAE,KAAK,SAAS,WAAW,YAAY;AAChE,iBAAO;AACL,oBAAQ,WAAW,OAAO,WAAW,MAAM;AAAA,cACzC,KAAK;AACH,2BAAW,QAAQ,SAAS,KAAK,QAAQ,CAAC,MAAM,SAAS,QAAQ,CAAC,IAAI;AACtE,qBAAK,gBAAgB;AACrB,qBAAK,aAAa;AAClB,qBAAK,aAAa;AAClB,qBAAK,iBAAiB;AACtB,qBAAK,UAAU,MAAM;AACrB,qBAAK,eAAe,MAAM;AAC1B,iBAAC,qBAAqB,KAAK,iBAAiB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,MAAM;AACtH,qBAAK,OAAO,MAAM;AAClB,2BAAW,OAAO;AAClB,uBAAO,KAAK,OAAO;AAAA,cACrB,KAAK;AACH,uBAAO,WAAW,OAAO,UAAU,KAAK,eAAe,MAAM,QAAQ,CAAC;AAAA,cACxE,KAAK;AAAA,cACL,KAAK;AACH,uBAAO,WAAW,KAAK;AAAA,YAC3B;AAAA,QACJ,GAAG,WAAW,IAAI;AAAA,MACpB,CAAC,CAAC;AACF,eAAS,SAAS;AAChB,eAAO,QAAQ,MAAM,MAAM,SAAS;AAAA,MACtC;AACA,aAAO;AAAA,IACT,EAAE;AAAA,EACJ,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,WAAK,OAAO;AACZ,WAAK,eAAe,YAAY;AAChC,UAAI,KAAK,MAAM,cAAc,KAAK,KAAK,MAAM,SAAS,SAAS,GAAG;AAChE,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY;AAC1B,UAAI,KAAK,YAAY;AACnB,qBAAa,KAAK,UAAU;AAAA,MAC9B;AACA,WAAK,aAAa;AAAA,IACpB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,WAAK,UAAU;AACf,WAAK,aAAa,WAAW,KAAK,OAAO,KAAK,aAAa;AAAA,IAC7D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,OAAO;AAChC,UAAI;AACJ,UAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AACnF,YAAM,qBAAqB,MAAM,iBAAiB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,WAAW,OAAO;AACtI,QAAAA,QAAO,KAAK,KAAK;AAAA,MACnB,OAAO;AACL,YAAI,cAAc,cAAc;AAChC,QAAAA,QAAO,MAAM,KAAK;AAClB,QAAAA,QAAO,MAAM,KAAK;AAClB,QAAAA,QAAO,OAAO,eAAe,KAAK,WAAW,QAAQ,iBAAiB,SAAS,SAAS,aAAa,KAAK;AAC1G,aAAK,eAAe,KAAK,WAAW,QAAQ,iBAAiB,UAAU,aAAa,YAAY;AAC9F,eAAK,MAAM,MAAM;AAAA,QACnB;AACA,aAAK,UAAU;AACf,YAAI,KAAK,eAAe;AACtB,eAAK,gBAAgB;AACrB,eAAK,KAAK,MAAM,mBAAmB,KAAK;AAAA,QAC1C;AACA,aAAK,KAAK,MAAM,OAAO,KAAK;AAC5B,YAAI;AACF,eAAK,KAAK;AACZ,SAAC,qBAAqB,KAAK,iBAAiB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,MAAM;AAAA,MACxH;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,0BAA0B;AACxC,UAAI,QAAQ,KAAK;AACjB,UAAI,gBAAgB,MAAM;AAC1B,UAAI,CAAC,MAAM,QAAQ;AACjB,YAAI;AACJ,YAAI,SAAS,KAAK,UAAU,uBAAuB,MAAM,WAAW;AACpE,YAAI,SAAS,KAAK,UAAU,kBAAkB,MAAM;AACpD,YAAI,cAAc,eAAe,KAAK,YAAY,QAAQ,iBAAiB,SAAS,SAAS,aAAa,SAAS,EAAE;AACrH,YAAI,cAAc,QAAQ;AACxB,cAAI,QAAQ,OAAO,WAAW,KAAK,UAAU,cAAc,UAAU,aAAa;AAClF,2BAAiB;AAAA,QACnB,OAAO;AACL,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,UAAI,uBAAuB;AAC3B,UAAI,QAAQ,KAAK;AACjB,UAAI,mBAAmB,KAAK,WAAW,cAAc,iBAAiB,aAAa,cAAc,iBAAiB;AAClH,UAAI,cAAc,CAAC,eAAe,eAAe,OAAO,WAAW,OAAO,YAAY,QAAQ,YAAY,WAAW,CAAC,MAAM,MAAM,cAAc,MAAM,WAAW,OAAO,wBAAwB,KAAK,oBAAoB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,eAAe,GAAG,wBAAwB,KAAK,oBAAoB,QAAQ,0BAA0B,UAAU,sBAAsB;AACxa,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AACA,UAAI,QAAQ,KAAK,WAAW;AAC5B,UAAI,MAAM,UAAU,CAAC,MAAM,aAAa;AACtC,gBAAQ,KAAK,WAAW,MAAM,aAAa,GAAG;AAAA,MAChD;AACA,UAAI,mBAAmB,KAAK,IAAI,MAAM,MAAM,MAAM,QAAQ,IAAI,OAAO,CAAC,KAAK,UAAU,eAAe,MAAM,OAAO,YAAY,QAAQ,YAAY;AACjJ,UAAI,kBAAkB;AACpB,aAAK,eAAe,YAAY;AAAA,MAClC;AAAA,IACF;AAAA,EACF,CAAC,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,WAAW;AACrC,UAAI,CAAC,aAAa,cAAc,WAAW,cAAc,SAAS;AAChE,eAAO,IAAI,YAAY;AAAA,MACzB;AACA,aAAO,OAAO,gBAAgB;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,aAAO,OAAO;AACd,MAAAC,QAAS,OAAO;AAAA,IAClB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,aAAO,QAAQ;AACf,MAAAA,QAAS,QAAQ;AAAA,IACnB;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE,qBAAAC,OAAY;AACd,gBAAgB,KAAK,WAAW,QAAQ;AACxC,IAAI;AACF,MAAI,aAAa,QAAQ,KAAK,GAAG;AAC/B,QAAI,aAAa;AAAA,EACnB,OAAO;AACL,QAAI,cAAc;AAAA,EACpB;AACF,SAAS,OAAO;AAChB;;;AgBv5CA,IAAI,kBAAkC,WAAW;AAC/C,WAAS,iBAAiB,MAAM,QAAQ;AACtC,QAAI,QAAQ;AACZ,oBAAgB,MAAM,gBAAgB;AACtC,oBAAgB,MAAM,SAAS,IAAI;AACnC,oBAAgB,MAAM,WAAW,IAAI;AACrC,oBAAgB,MAAM,gBAAgB,WAAW;AAC/C,UAAI,eAAe,sBAAsB,gBAAgB;AACzD,UAAI,cAAc,MAAM,OAAO,QAAQ,YAAY,OAAO,eAAe,YAAY;AACrF,OAAC,gBAAgB,MAAM,aAAa,QAAQ,kBAAkB,SAAS,UAAU,uBAAuB,cAAc,YAAY,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,aAAa,MAAM,WAAW;AAC7O,OAAC,iBAAiB,MAAM,aAAa,QAAQ,mBAAmB,SAAS,UAAU,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK,cAAc,eAAe,eAAe,CAAC,GAAG,MAAM,WAAW,GAAG,CAAC,GAAG;AAAA,QAC3R,WAAW,MAAM;AAAA,MACnB,CAAC,CAAC;AACF,UAAI,iBAAiB,GAAG;AACtB,cAAM,SAAS,MAAM,GAAG;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,oBAAgB,MAAM,YAAY,SAAS,KAAK;AAC9C,UAAI,SAAS,MAAM,QAAQ;AAC3B,UAAI,cAAc,OAAO;AACzB,UAAI,eAAe,YAAY,QAAQ;AACrC;AACF,UAAI,WAAW,OAAO,MAAM;AAC5B,aAAO,QAAQ;AACf,kBAAY,QAAQ,GAAG;AACvB,UAAI,KAAK;AACP,eAAO,OAAO,MAAM;AAAA,MACtB;AACA,UAAI,aAAa,OAAO,KAAK;AAC7B,UAAI,WAAW,QAAQ,UAAU;AAC/B,eAAO,KAAK,aAAa,UAAU,UAAU;AAAA,MAC/C;AACA,UAAI,YAAY,MAAM,QAAQ,YAAY,WAAW,YAAY;AACjE,aAAO,iBAAiB,SAAS;AACjC,aAAO,KAAK,WAAW,WAAW;AAChC,eAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH,CAAC;AACD,oBAAgB,MAAM,uBAAuB,SAAS,KAAK;AACzD,UAAI,eAAe,MAAM,MAAM;AAC/B,UAAI,iBAAiB,GAAG;AACtB,cAAM,SAAS,GAAG;AAAA,MACpB;AAAA,IACF,CAAC;AACD,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,MAAM;AAAA,EACb;AACA,eAAa,kBAAkB,CAAC;AAAA,IAC9B,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,UAAI,eAAe,KAAK,OAAO,QAAQ,aAAa,OAAO,cAAc,aAAa,aAAa,eAAe,aAAa,cAAc,SAAS,aAAa;AACnK,UAAI,CAAC;AACH;AACF,UAAI,CAAC,UAAU,MAAM,aAAa;AAChC,cAAM,YAAY,KAAK;AACvB;AAAA,MACF;AACA,UAAI,cAAc;AAChB,cAAM,aAAa,gBAAgB,YAAY;AAAA,MACjD;AACA,UAAI,aAAa;AACf,cAAM,aAAa,eAAe,WAAW;AAAA,MAC/C;AACA,WAAK,YAAY;AAAA,IACnB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,iBAAiB,aAAa,KAAK,YAAY;AAAA,IACvD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,UAAI,cAAc;AAClB,OAAC,eAAe,KAAK,WAAW,QAAQ,iBAAiB,SAAS,UAAU,qBAAqB,aAAa,WAAW,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,oBAAoB,aAAa,KAAK,YAAY;AAC/O,WAAK,UAAU;AAAA,IACjB;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE;;;AC7EF,IAAIC,aAAY,CAAC,aAAa;AAC9B,SAAS,mBAAmB,MAAM,QAAQ;AACxC,MAAI,SAAS,OAAO;AACpB,MAAI,UAAU,OAAO;AACrB,MAAI,UAAU;AAAA,IACZ,WAAW;AAAA,EACb;AACA,UAAQ,QAAQ,IAAI,GAAG;AAAA,IACrB,KAAK;AACH,cAAQ,WAAW;AACnB;AAAA,IACF,KAAK,UAAU;AACb,UAAI,cAAc,KAAK,aAAa,OAAO,yBAAyB,MAAMA,UAAS;AACnF,aAAO,OAAO,SAAS,IAAI;AAC3B,UAAI,OAAO,gBAAgB,UAAU;AACnC,gBAAQ,YAAY;AAAA,MACtB;AACA;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,YAA4B,SAAS,aAAa;AACpD,YAAU,YAAY,WAAW;AACjC,MAAI,SAAS,aAAa,UAAU;AACpC,WAAS,aAAa;AACpB,QAAI;AACJ,oBAAgB,MAAM,UAAU;AAChC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AACA,YAAQ,OAAO,KAAK,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC;AACrD,oBAAgB,uBAAuB,KAAK,GAAG,UAAUC,OAAM;AAC/D,oBAAgB,uBAAuB,KAAK,GAAG,OAAO,IAAI;AAC1D,oBAAgB,uBAAuB,KAAK,GAAG,mBAAmB,IAAI;AACtE,oBAAgB,uBAAuB,KAAK,GAAG,YAAY,WAAW;AACpE,UAAI;AACJ,cAAQ,YAAY,MAAM,SAAS,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS;AAAA,IAChG,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,qBAAqB,SAAS,MAAM;AACjF,UAAI;AACJ,UAAI,OAAO,KAAK;AAChB,OAAC,aAAa,MAAM,SAAS,QAAQ,eAAe,SAAS,SAAS,WAAW,qBAAqB,IAAI;AAAA,IAC5G,CAAC;AACD,oBAAgB,uBAAuB,KAAK,GAAG,oBAAoB,WAAW;AAC5E,UAAI,SAAS,MAAM,OAAO;AAC1B,UAAI,CAAC;AACH;AACF,YAAM,OAAO,KAAK,WAAW,WAAW;AACtC,cAAM,OAAO,MAAM;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EACT;AACA,eAAa,YAAY,CAAC;AAAA,IACxB,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI;AACJ,cAAQ,aAAa,KAAK,SAAS,QAAQ,eAAe,SAAS,SAAS,WAAW;AAAA,IACzF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,UAAI,cAAc;AAClB,UAAI,aAAa,eAAe,KAAK,YAAY,QAAQ,iBAAiB,SAAS,UAAU,sBAAsB,aAAa,YAAY,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB;AAClN,aAAO,CAAC,CAAC,aAAa,cAAc,WAAW,cAAc;AAAA,IAC/D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB;AACjC,UAAI,SAAS;AACb,UAAI,SAAS,KAAK,OAAO;AACzB,UAAI,YAAY,KAAK,OAAO,SAAS,KAAK,OAAO;AACjD,UAAI,UAAU,OAAO,OAAO,CAAC;AAC7B,UAAI,CAAC,OAAO,OAAO,CAAC,OAAO,wBAAwB,CAAC,QAAQ,aAAa,IAAI,UAAU,GAAG;AACxF;AAAA,MACF;AACA,UAAI,KAAK;AACP,aAAK,IAAI,QAAQ;AACnB,UAAI,aAAa,OAAO,yBAAyB,KAAK,QAAQ,WAAW;AACzE,UAAI,CAAC,cAAc,WAAW,UAAU;AACtC,aAAK,OAAO,YAAY,SAAS,KAAK,MAAM;AAC1C,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,gBAAI,SAAS,OAAO,QAAQ,MAAM,OAAO;AACzC,gBAAI,KAAK;AACP,kBAAI,sBAAsB;AAC1B,kBAAI,UAAU,mBAAmB,MAAM,MAAM;AAC7C,qBAAO,OAAO,MAAM;AACpB,kBAAI,UAAU,KAAK,OAAO,EAAE,KAAK,WAAW;AAC1C,uBAAO,QAAQ,IAAI;AAAA,cACrB,CAAC,EAAE,MAAM,MAAM;AACf,kBAAI,CAAC,QAAQ,aAAa,uBAAuB,OAAO,OAAO,YAAY,QAAQ,yBAAyB,WAAW,wBAAwB,qBAAqB,SAAS,QAAQ,0BAA0B,UAAU,sBAAsB,uBAAuB;AACpQ,uBAAO,iBAAiB;AAAA,cAC1B;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,UAAI,cAAc,KAAK,OAAO;AAC9B,WAAK,OAAO,eAAe;AAC3B,cAAQ,eAAe,QAAQ,gBAAgB,OAAO;AACtD,UAAI,QAAQ,mBAAmB,QAAQ,QAAQ,mBAAmB;AAChE,gBAAQ,iBAAiB;AAC3B,WAAK,MAAM,IAAI,IAAI,eAAe;AAAA,QAChC,YAAY,KAAK;AAAA,QACjB,QAAQ,OAAO;AAAA,QACf,OAAO;AAAA,QACP,WAAW,OAAO;AAAA,QAClB,KAAK,OAAO;AAAA,MACd,GAAG,OAAO,CAAC;AACX,UAAI,CAAC,KAAK,YAAY;AACpB,mBAAW,qBAAqB,KAAK,QAAQ;AAAA,UAC3C,KAAK;AAAA,YACH,KAAK,SAAS,MAAM;AAClB,kBAAI,YAAY;AAChB,sBAAQ,aAAa,OAAO,SAAS,QAAQ,eAAe,SAAS,UAAU,mBAAmB,WAAW,WAAW,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB;AAAA,YAC1L;AAAA,YACA,cAAc;AAAA,UAChB;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,KAAK,YAAY;AACnB,aAAK,kBAAkB,IAAI,gBAAgB,eAAe;AAAA,UACxD,QAAQ,OAAO;AAAA,UACf,OAAO;AAAA,QACT,GAAG,OAAO,GAAG,IAAI;AACjB,aAAK,OAAO,sBAAsB,WAAW;AAC3C,cAAI;AACJ,mBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,iBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,UAC/B;AACA,kBAAQ,wBAAwB,OAAO,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,oBAAoB,MAAM,uBAAuB,IAAI;AAAA,QAC7L;AAAA,MACF;AACA,UAAI,OAAO,QAAQ;AACjB,YAAI;AACJ,SAAC,gBAAgB,KAAK,YAAY,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,UAAU,WAAW;AACxH,cAAI;AACJ,kBAAQ,cAAc,OAAO,SAAS,QAAQ,gBAAgB,SAAS,SAAS,YAAY,OAAO;AAAA,QACrG,CAAC;AAAA,MACH;AACA,WAAK,GAAG,eAAO,YAAY,WAAW;AACtC,WAAK,GAAG,eAAO,mBAAmB,mBAAmB,KAAK,iBAAiB;AAC3E,WAAK,GAAG,eAAO,SAAS,KAAK,QAAQ,KAAK,IAAI,CAAC;AAC/C,WAAK,YAAY;AACjB,WAAK,gBAAgB,MAAM,IAAI;AAC/B,WAAK,gBAAgB,MAAM,UAAU;AACrC,WAAK,gBAAgB,MAAM,qBAAqB;AAChD,WAAK,gBAAgB,MAAM,aAAa;AACxC,WAAK,gBAAgB,MAAM,UAAU;AACrC,WAAK,gBAAgB,MAAM,oBAAoB;AAC/C,WAAK,gBAAgB,MAAM,kBAAkB;AAC7C,WAAK,gBAAgB,MAAM,aAAa;AACxC,WAAK,gBAAgB,MAAM,aAAa;AACxC,WAAK,gBAAgB,MAAM,SAAS;AACpC,WAAK,gBAAgB,MAAM,QAAQ;AACnC,WAAK,gBAAgB,MAAM,eAAe;AAC1C,WAAK,gBAAgB,MAAM,aAAa;AACxC,WAAK,gBAAgB,MAAM,GAAG;AAC9B,WAAK,gBAAgB,MAAM,WAAW;AACtC,WAAK,gBAAgB,MAAM,KAAK;AAChC,WAAK,gBAAgB,MAAM,mBAAmB;AAC9C,WAAK,gBAAgB,MAAM,gBAAgB;AAC3C,WAAK,gBAAgB,MAAM,gBAAgB;AAC3C,WAAK,gBAAgB,MAAM,kBAAkB;AAC7C,WAAK,gBAAgB,MAAM,iBAAiB;AAC5C,WAAK,gBAAgB,MAAM,cAAc;AACzC,WAAK,gBAAgB,MAAM,aAAa;AACxC,WAAK,gBAAgB,MAAM,iBAAiB;AAC5C,WAAK,gBAAgB,MAAM,iBAAiB;AAC5C,WAAK,gBAAgB,MAAM,WAAW;AACtC,UAAI,OAAO,KAAK;AACd,aAAK,IAAI,KAAK,OAAO,KAAK;AAAA,UACxB,UAAU;AAAA,QACZ,CAAC,EAAE,MAAM,SAAS,GAAG;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,UAAI;AACJ,UAAI,KAAK,KAAK;AACZ,aAAK,IAAI,QAAQ;AACjB,aAAK,MAAM;AAAA,MACb;AACA,OAAC,wBAAwB,KAAK,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,QAAQ;AACrI,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC5B,UAAI,SAAS;AACb,WAAK,IAAI,GAAG,MAAM,OAAO,SAAS,KAAK;AACrC,YAAI,OAAO,QAAQ;AACjB,iBAAO,OAAO,KAAK,eAAO,OAAO,IAAI,OAAO,OAAO,QAAQ,GAAG,CAAC;AAAA,QACjE;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB,WAAW;AACzC,UAAI,SAAS;AACb,WAAK,IAAI,GAAG,WAAW,SAAS,GAAG;AACjC,YAAI,OAAO,QAAQ;AACjB,iBAAO,OAAO,KAAK,cAAc,eAAe,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AAAA,YACzE;AAAA,UACF,CAAC,CAAC;AACF,cAAI,cAAc,MAAM,eAAe,OAAO,IAAI,aAAa;AAC7D,mBAAO,mBAAmB,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB,GAAG;AACpC,UAAI;AACF,YAAI,UAAU,KAAK,MAAM,MAAM,KAAK,EAAE,KAAK,OAAO,EAAE,IAAI,SAAS,GAAG;AAClE,iBAAO,OAAO,aAAa,CAAC;AAAA,QAC9B,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC;AACxB,YAAI,CAAC,QAAQ,UAAU;AACrB;AACF,aAAK,OAAO,KAAK,cAAc;AAAA,UAC7B,WAAW,MAAM;AAAA,UACjB,MAAM,QAAQ,UAAU;AAAA,QAC1B,CAAC;AAAA,MACH,SAAS,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF,CAAC,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,WAAW,OAAO;AAC5C,aAAO,IAAI,YAAY,WAAW,KAAK;AAAA,IACzC;AAAA,EACF,CAAC,CAAC;AACF,SAAO;AACT,EAAE,UAAU;AACZ,gBAAgB,WAAW,OAAO,GAAG;AACrC,gBAAgB,WAAW,SAAS,KAAK;", "names": ["EventEmitter", "logger", "logger", "logger", "<PERSON><PERSON>", "EventEmitter", "_excluded", "logger"]}