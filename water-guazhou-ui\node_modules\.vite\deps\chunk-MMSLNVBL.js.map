{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/features/processors/BaseProcessor.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import{HandleOwner as t}from\"../../../../../core/HandleOwner.js\";import{property as r}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as o}from\"../../../../../core/accessorSupport/decorators/subclass.js\";let s=class extends t{initialize(){}destroy(){}get supportsTileUpdates(){return!1}get spatialReference(){const e=this.get(\"tileStore.tileScheme.spatialReference\");return e&&e.toJSON()||null}};e([r({readOnly:!0})],s.prototype,\"supportsTileUpdates\",null),e([r({constructOnly:!0})],s.prototype,\"remoteClient\",void 0),e([r({constructOnly:!0})],s.prototype,\"service\",void 0),e([r()],s.prototype,\"spatialReference\",null),e([r({constructOnly:!0})],s.prototype,\"tileInfo\",void 0),e([r({constructOnly:!0})],s.prototype,\"tileStore\",void 0),s=e([o(\"esri.views.2d.layers.features.processors.BaseProcessor\")],s);const p=s;export{p as default};\n"], "mappings": ";;;;;;;;;;;;AAIwY,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,aAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,mBAAkB;AAAC,UAAMA,KAAE,KAAK,IAAI,uCAAuC;AAAE,WAAOA,MAAGA,GAAE,OAAO,KAAG;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,uBAAsB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,wDAAwD,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["e"]}