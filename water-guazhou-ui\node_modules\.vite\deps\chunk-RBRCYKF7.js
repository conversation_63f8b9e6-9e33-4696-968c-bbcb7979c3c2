import {
  s as s3,
  x
} from "./chunk-EVGSPNRI.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  K
} from "./chunk-U4SVMKOQ.js";
import {
  b
} from "./chunk-HP475EI3.js";
import {
  s as s2
} from "./chunk-RV4I37UI.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/support/basemapUtils.js
var f = s2.getLogger("esri.support.basemapUtils");
function p() {
  return {};
}
function y(e) {
  for (const r2 in e) {
    const a = e[r2];
    false === (a == null ? void 0 : a.destroyed) && a.destroy(), delete e[r2];
  }
}
function m(a, t2) {
  let n;
  if ("string" == typeof a) {
    if (!(a in s3)) {
      const e = Object.entries(s3).filter(([e2, a2]) => s.apiKey && !a2.classic || !s.apiKey && a2.classic && !a2.deprecated).map(([e2]) => `"${e2}"`).join(", ");
      return f.warn(`Unable to find basemap definition for: ${a}. Try one of these: ${e}`), null;
    }
    t2 && (n = t2[a]), n || (n = x.fromId(a), t2 && (t2[a] = n));
  } else n = b(x, a);
  return (n == null ? void 0 : n.destroyed) && (f.warn("The provided basemap is already destroyed", { basemap: n }), n = null), n;
}
function L(e, r2) {
  if (e === r2) return true;
  return "equal" === M(T(e), T(r2), { mustMatchReferences: true });
}
function h(e, r2) {
  var _a;
  if (t(r2) || t(e)) return { spatialReference: null, updating: false };
  if ("not-loaded" === r2.loadStatus) return r2.load(), { spatialReference: null, updating: true };
  if (r2.spatialReference) return { spatialReference: r2.spatialReference, updating: false };
  if (0 === r2.baseLayers.length) return { spatialReference: null, updating: false };
  const a = r2.baseLayers.getItemAt(0);
  switch (a.loadStatus) {
    case "not-loaded":
      a.load();
    case "loading":
      return { spatialReference: null, updating: true };
    case "failed":
      return { spatialReference: null, updating: false };
  }
  const t2 = (("supportedSpatialReferences" in a ? a.supportedSpatialReferences : null) || ["tileInfo" in a ? (_a = a.tileInfo) == null ? void 0 : _a.spatialReference : a.spatialReference]).filter(Boolean), i = e.spatialReference;
  return i ? { spatialReference: t2.find((e2) => i.equals(e2)) ?? t2[0] ?? null, updating: false } : { spatialReference: t2[0], updating: false };
}
function T(e) {
  return e ? !e.loaded && e.resourceInfo ? q(e.resourceInfo.data) : { baseLayers: w(e.baseLayers), referenceLayers: w(e.referenceLayers) } : null;
}
function w(e) {
  return (j.isCollection(e) ? e.toArray() : e).map(j2);
}
function j2(e) {
  var _a, _b;
  return { type: e.type, url: A("urlTemplate" in e && e.urlTemplate || e.url || "styleUrl" in e && e.styleUrl || ""), minScale: "minScale" in e && null != e.minScale ? e.minScale : 0, maxScale: "maxScale" in e && null != e.maxScale ? e.maxScale : 0, opacity: null != e.opacity ? e.opacity : 1, visible: null == e.visible || !!e.visible, sublayers: "map-image" !== e.type && "wms" !== e.type || !r(e.sublayers) ? void 0 : (_a = e.sublayers) == null ? void 0 : _a.map((e2) => ({ id: e2.id, visible: e2.visible })), activeLayerId: "wmts" === e.type ? (_b = e.activeLayer) == null ? void 0 : _b.id : void 0 };
}
function q(e) {
  return e ? { baseLayers: x2((e.baseMapLayers ?? []).filter((e2) => !e2.isReference)), referenceLayers: x2((e.baseMapLayers ?? []).filter((e2) => e2.isReference)) } : null;
}
function x2(e) {
  return e.map((e2) => U(e2));
}
function U(e) {
  let r2;
  switch (e.layerType) {
    case "VectorTileLayer":
      r2 = "vector-tile";
      break;
    case "ArcGISTiledMapServiceLayer":
      r2 = "tile";
      break;
    default:
      r2 = "unknown";
  }
  return { type: r2, url: A(e.templateUrl || e.urlTemplate || e.styleUrl || e.url), minScale: null != e.minScale ? e.minScale : 0, maxScale: null != e.maxScale ? e.maxScale : 0, opacity: null != e.opacity ? e.opacity : 1, visible: null == e.visibility || !!e.visibility, sublayers: void 0, activeLayerId: void 0 };
}
function M(e, r2, a) {
  if (null != e != (null != r2)) return "not-equal";
  if (!e || !r2) return "equal";
  if (!k(e.baseLayers, r2.baseLayers)) return "not-equal";
  return k(e.referenceLayers, r2.referenceLayers) ? "equal" : a.mustMatchReferences ? "not-equal" : "base-layers-equal";
}
function k(e, r2) {
  if (e.length !== r2.length) return false;
  for (let a = 0; a < e.length; a++) if (!$2(e[a], r2[a])) return false;
  return true;
}
function $2(e, r2) {
  if (e.type !== r2.type || "scene" === e.type || e.url !== r2.url || e.minScale !== r2.minScale || e.maxScale !== r2.maxScale || e.visible !== r2.visible || e.opacity !== r2.opacity) return false;
  if (r(e.activeLayerId) || r(r2.activeLayerId)) return e.activeLayerId === r2.activeLayerId;
  if (r(e.sublayers) || r(r2.sublayers)) {
    if (t(e.sublayers) || t(r2.sublayers) || e.sublayers.length !== r2.sublayers.length) return false;
    for (let a = 0; a < e.sublayers.length; a++) {
      const t2 = e.sublayers.at(a), n = r2.sublayers.at(a);
      if ((t2 == null ? void 0 : t2.id) !== (n == null ? void 0 : n.id) || (t2 == null ? void 0 : t2.visible) !== (n == null ? void 0 : n.visible)) return false;
    }
  }
  return true;
}
function A(e) {
  return e ? K(e).replace(/^\s*https?:/i, "").toLowerCase() : "";
}

export {
  p,
  y,
  m,
  L,
  h
};
//# sourceMappingURL=chunk-RBRCYKF7.js.map
