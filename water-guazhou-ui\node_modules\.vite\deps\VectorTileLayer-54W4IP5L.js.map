{"version": 3, "sources": ["../../@arcgis/core/layers/support/imageUtils.js", "../../@arcgis/core/layers/support/SpriteSource.js", "../../@arcgis/core/views/2d/engine/vectorTiles/TilemapIndex.js", "../../@arcgis/core/views/2d/engine/vectorTiles/TilemapRequest.js", "../../@arcgis/core/views/2d/engine/vectorTiles/style/VectorTilesRequestManager.js", "../../@arcgis/core/views/2d/engine/vectorTiles/style/VectorTileSource.js", "../../@arcgis/core/layers/support/vectorTileLayerLoader.js", "../../@arcgis/core/views/2d/engine/vectorTiles/tileInfoUtils.js", "../../@arcgis/core/layers/VectorTileLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nlet A=null;function o(o){if(A)return A;const l={lossy:\"UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA\",lossless:\"UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==\",alpha:\"UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==\",animation:\"UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA\"};return A=new Promise((A=>{const n=new Image;n.onload=()=>{n.onload=n.onerror=null,A(n.width>0&&n.height>0)},n.onerror=()=>{n.onload=n.onerror=null,A(!1)},n.src=\"data:image/webp;base64,\"+l[o]})),A}export{o as checkWebPSupport};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../request.js\";import e from\"../../core/Error.js\";import i from\"../../core/Logger.js\";import{isArrayBuffer as s,isUint8ClampedArray as r}from\"../../core/typedArrayUtil.js\";import{urlToObject as a,objectToQuery as o}from\"../../core/urlUtils.js\";const h=1.15;class n{constructor(t,e){this._spriteSource=t,this._maxTextureSize=e,this.devicePixelRatio=1,this._spriteImageFormat=\"png\",this._isRetina=!1,this._spritesData={},this.image=null,this.width=null,this.height=null,this.loadStatus=\"not-loaded\",\"url\"===t.type&&t.spriteFormat&&(this._spriteImageFormat=t.spriteFormat),t.pixelRatio&&(this.devicePixelRatio=t.pixelRatio),this.baseURL=t.spriteUrl}get spriteNames(){const t=[];for(const e in this._spritesData)t.push(e);return t.sort(),t}getSpriteInfo(t){return this._spritesData?this._spritesData[t]:null}async load(t){if(this.baseURL){this.loadStatus=\"loading\";try{await this._loadSprites(t),this.loadStatus=\"loaded\"}catch{this.loadStatus=\"failed\"}}else this.loadStatus=\"failed\"}async _loadSprites(t){this._isRetina=this.devicePixelRatio>h;const{width:s,height:r,data:a,json:o}=await this._getSpriteData(this._spriteSource,t),n=Object.keys(o);if(!n||0===n.length||!a)return this._spritesData=this.image=null,void(this.width=this.height=0);this._spritesData=o,this.width=s,this.height=r;const d=Math.max(this._maxTextureSize,4096);if(s>d||r>d){const t=`Sprite resource for style ${this.baseURL} is bigger than the maximum allowed of ${d} pixels}`;throw i.getLogger(\"esri.layers.support.SpriteSource\").error(t),new e(\"SpriteSource\",t)}let p;for(let e=0;e<a.length;e+=4)p=a[e+3]/255,a[e]=a[e]*p,a[e+1]=a[e+1]*p,a[e+2]=a[e+2]*p;this.image=a}async _getSpriteData(i,n){if(\"image\"===i.type){let t,a;if(this.devicePixelRatio<h){if(!i.spriteSource1x)throw new e(\"SpriteSource\",\"no image data provided for low resolution sprites!\");t=i.spriteSource1x.image,a=i.spriteSource1x.json}else{if(!i.spriteSource2x)throw new e(\"SpriteSource\",\"no image data provided for high resolution sprites!\");t=i.spriteSource2x.image,a=i.spriteSource2x.json}return\"width\"in t&&\"height\"in t&&\"data\"in t&&(s(t.data)||r(t.data))?{width:t.width,height:t.height,data:new Uint8Array(t.data),json:a}:{...d(t),json:a}}const p=a(this.baseURL),l=p.query?\"?\"+o(p.query):\"\",g=this._isRetina?\"@2x\":\"\",u=`${p.path}${g}.${this._spriteImageFormat}${l}`,c=`${p.path}${g}.json${l}`,[m,S]=await Promise.all([t(c,n),t(u,{responseType:\"image\",...n})]);return{...d(S.data),json:m.data}}}function d(t){const e=document.createElement(\"canvas\"),i=e.getContext(\"2d\");e.width=t.width,e.height=t.height,i.drawImage(t,0,0,t.width,t.height);const s=i.getImageData(0,0,t.width,t.height);return{width:t.width,height:t.height,data:new Uint8Array(s.data)}}export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../request.js\";import{throwIfAborted as t}from\"../../../../core/promiseUtils.js\";import r from\"../../tiling/TileKey.js\";class l{constructor(e){this.url=e}async fetchTileIndex(){return this._tileIndexPromise||(this._tileIndexPromise=e(this.url).then((e=>e.data.index))),this._tileIndexPromise}async dataKey(e,r){const l=await this.fetchTileIndex();return t(r),this._getIndexedDataKey(l,e)}_getIndexedDataKey(e,t){const l=[t];if(t.level<0||t.row<0||t.col<0||t.row>>t.level>0||t.col>>t.level>0)return null;let i=t;for(;0!==i.level;)i=new r(i.level-1,i.row>>1,i.col>>1,i.world),l.push(i);let o,s,n=e,a=l.pop();if(1===n)return a;for(;l.length;)if(o=l.pop(),s=(1&o.col)+((1&o.row)<<1),n){if(0===n[s]){a=null;break}if(1===n[s]){a=o;break}a=o,n=n[s]}return a}}export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../request.js\";import{isAbortError as t}from\"../../../../core/promiseUtils.js\";import i from\"../../tiling/TileKey.js\";class r{constructor(e,t){this._tilemap=e,this._tileIndexUrl=t}async fetchTileIndex(t){return this._tileIndexPromise||(this._tileIndexPromise=e(this._tileIndexUrl,{query:{...t?.query}}).then((e=>e.data.index))),this._tileIndexPromise}dataKey(e,r){const{level:l,row:s,col:o}=e,n=new i(e);return this._tilemap.fetchAvailabilityUpsample(l,s,o,n,r).then((()=>(n.world=e.world,n))).catch((e=>{if(t(e))throw e;return null}))}}export{r as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../../request.js\";import{clone as r}from\"../../../../../core/lang.js\";import{getOrCreateMapValue as e}from\"../../../../../core/MapUtils.js\";import{onAbort as o,isAborted as s}from\"../../../../../core/promiseUtils.js\";class i{constructor(t){this._tileUrl=t,this._promise=null,this._abortController=null,this._abortOptions=[]}getData(t){null===this._promise&&(this._abortController=new AbortController,this._promise=this._makeRequest(this._tileUrl,this._abortController.signal));const e=this._abortOptions;return e.push(t),o(t,(()=>{e.every((t=>s(t)))&&this._abortController.abort()})),this._promise.then((t=>r(t)))}async _makeRequest(r,e){const{data:o}=await t(r,{responseType:\"array-buffer\",signal:e});return o}}const n=new Map;function l(t,r,e,o,s){return a(t.replace(/\\{z\\}/gi,r.toString()).replace(/\\{y\\}/gi,e.toString()).replace(/\\{x\\}/gi,o.toString()),s)}function a(t,r){return e(n,t,(()=>new i(t))).getData(r).then((r=>(n.delete(t),r))).catch((r=>{throw n.delete(t),r}))}export{l as request};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as t}from\"../../../../../core/lang.js\";import{urlToObject as e,isAbsolute as i,join as r,addQueryParameters as l}from\"../../../../../core/urlUtils.js\";import s from\"../../../../../geometry/Extent.js\";import{readServiceTileInfo as o}from\"../../../../../layers/support/serviceTileInfoProperty.js\";import{TilemapCache as a}from\"../../../../../layers/support/TilemapCache.js\";import n from\"../TilemapIndex.js\";import p from\"../TilemapRequest.js\";import{request as u}from\"./VectorTilesRequestManager.js\";class h{constructor(u,h,m){this.tilemap=null,this.tileInfo=null,this.capabilities=null,this.fullExtent=null,this.name=u,this.sourceUrl=h;const f=e(this.sourceUrl),c=t(m),x=c.tiles;if(f)for(let t=0;t<x.length;t++){const s=e(x[t]);s&&(i(s.path)||(s.path=r(f.path,s.path)),x[t]=l(s.path,{...f.query,...s.query}))}this.tileServers=x;const d=m.capabilities&&m.capabilities.split(\",\").map((t=>t.toLowerCase().trim())),y=!0===m?.exportTilesAllowed,T=!0===d?.includes(\"tilemap\"),g=y&&m.hasOwnProperty(\"maxExportTilesCount\")?m.maxExportTilesCount:0;this.capabilities={operations:{supportsExportTiles:y,supportsTileMap:T},exportTiles:y?{maxExportTilesCount:+g}:null},this.tileInfo=o(c.tileInfo,c,null,{ignoreMinMaxLOD:!0});const M=m.tileMap?l(r(f.path,m.tileMap),f.query??{}):null;T?(this.type=\"vector-tile\",this.tilemap=new p(new a({layer:{parsedUrl:f,tileInfo:this.tileInfo,type:\"vector-tile\",tileServers:this.tileServers}}),M)):M&&(this.tilemap=new n(M)),this.fullExtent=s.fromJSON(m.fullExtent)}destroy(){}async getRefKey(t,e){return await(this.tilemap?.dataKey(t,e))??t}requestTile(t,e,i,r){const l=this.tileServers[e%this.tileServers.length];return u(l,t,e,i,r)}isCompatibleWith(t){const e=this.tileInfo,i=t.tileInfo;if(!e.spatialReference.equals(i.spatialReference))return!1;if(!e.origin.equals(i.origin))return!1;if(Math.round(e.dpi)!==Math.round(i.dpi))return!1;const r=e.lods,l=i.lods,s=Math.min(r.length,l.length);for(let o=0;o<s;o++){const t=r[o],e=l[o];if(t.level!==e.level||Math.round(t.scale)!==Math.round(e.scale))return!1}return!0}}export{h as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../config.js\";import r from\"../../request.js\";import{throwIfAborted as t}from\"../../core/promiseUtils.js\";import{getOrigin as s,isProtocolRelative as o,isAbsolute as l,join as n,removeTrailingSlash as i,normalize as u,removeFile as c,getAppBaseUrl as a,addQueryParameters as f}from\"../../core/urlUtils.js\";import p from\"../../views/2d/engine/vectorTiles/style/VectorTileSource.js\";const y=e.defaults&&e.defaults.io.corsEnabledServers;async function m(e,r){const t={source:null,sourceBase:null,sourceUrl:null,validatedSource:null,style:null,styleBase:null,styleUrl:null,sourceNameToSource:{},primarySourceName:\"\",spriteFormat:\"png\"},[s,o]=\"string\"==typeof e?[e,null]:[null,e.jsonUrl];await h(t,\"esri\",e,o,r);const l={layerDefinition:t.validatedSource,url:s,serviceUrl:t.sourceUrl,style:t.style,styleUrl:t.styleUrl,spriteUrl:t.style.sprite&&S(t.styleBase,t.style.sprite),spriteFormat:t.spriteFormat,glyphsUrl:t.style.glyphs&&S(t.styleBase,t.style.glyphs),sourceNameToSource:t.sourceNameToSource,primarySourceName:t.primarySourceName};return d(l.spriteUrl),d(l.glyphsUrl),l}function d(e){if(!e)return;const r=s(e);y&&!y.includes(r)&&y.push(r)}function S(...e){let r;for(const t of e)if(null!=t)if(o(t)){if(r){const e=r.split(\"://\")[0];r=e+\":\"+t.trim()}}else r=l(t)?t:n(r,t);return r?i(r):void 0}async function h(e,s,o,l,n){let i,c,a;if(t(n),\"string\"==typeof o){const e=u(o);d(e),a=await r(e,{...n,responseType:\"json\",query:{f:\"json\",...n?.query}}),a.ssl&&(i&&(i=i.replace(/^http:/i,\"https:\")),c&&(c=c.replace(/^http:/i,\"https:\"))),i=e,c=e}else null!=o&&(a={data:o},i=o.jsonUrl||null,c=l);const f=a?.data;if(U(f))return e.styleUrl=i||null,x(e,f,c,n);if(w(f))return e.sourceUrl?v(e,f,c,!1,s,n):(e.sourceUrl=i||null,v(e,f,c,!0,s,n));throw new Error(\"You must specify the URL or the JSON for a service or for a style.\")}function U(e){return!!e?.sources}function w(e){return!U(e)}async function x(e,r,t,s){const o=t?c(t):a();e.styleBase=o,e.style=r,e.styleUrl&&d(e.styleUrl),r[\"sprite-format\"]&&\"webp\"===r[\"sprite-format\"].toLowerCase()&&(e.spriteFormat=\"webp\");const l=[];if(r.sources&&r.sources.esri){const t=r.sources.esri;t.url?await h(e,\"esri\",S(o,t.url),void 0,s):l.push(h(e,\"esri\",t,o,s))}for(const n of Object.keys(r.sources))\"esri\"!==n&&\"vector\"===r.sources[n].type&&(r.sources[n].url?l.push(h(e,n,S(o,r.sources[n].url),void 0,s)):r.sources[n].tiles&&l.push(h(e,n,r.sources[n],o,s)));await Promise.all(l)}async function v(e,r,t,s,o,l){const n=t?i(t)+\"/\":a(),u=g(r,n),c=new p(o,f(n,l?.query??{}),u);if(!s&&e.primarySourceName in e.sourceNameToSource){const r=e.sourceNameToSource[e.primarySourceName];if(!r.isCompatibleWith(c))return;null!=c.fullExtent&&(null!=r.fullExtent?r.fullExtent.union(c.fullExtent):r.fullExtent=c.fullExtent.clone()),r.tileInfo&&c.tileInfo&&r.tileInfo.lods.length<c.tileInfo.lods.length&&(r.tileInfo=c.tileInfo)}if(s?(e.sourceBase=n,e.source=r,e.validatedSource=u,e.primarySourceName=o,e.sourceUrl&&d(e.sourceUrl)):d(n),e.sourceNameToSource[o]=c,!e.style){if(null==r.defaultStyles)throw new Error;return\"string\"==typeof r.defaultStyles?h(e,\"\",S(n,r.defaultStyles,\"root.json\"),void 0,l):h(e,\"\",r.defaultStyles,S(n,\"root.json\"),l)}}function g(e,r){if(e.hasOwnProperty(\"tileInfo\"))return e;const t={xmin:-20037507.067161843,ymin:-20037507.067161843,xmax:20037507.067161843,ymax:20037507.067161843,spatialReference:{wkid:102100}},s=512;let o=78271.51696400007,l=295828763.7957775;const n=[],i=e.hasOwnProperty(\"minzoom\")?+e.minzoom:0,u=e.hasOwnProperty(\"maxzoom\")?+e.maxzoom:22;for(let c=0;c<=u;c++)c>=i&&n.push({level:c,scale:l,resolution:o}),o/=2,l/=2;for(const c of e.tiles??[])d(S(r,c));return{capabilities:\"TilesOnly\",initialExtent:t,fullExtent:t,minScale:0,maxScale:0,tiles:e.tiles,tileInfo:{rows:s,cols:s,dpi:96,format:\"pbf\",origin:{x:-20037508.342787,y:20037508.342787},lods:n,spatialReference:{wkid:102100}}}}export{m as loadMetadata};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport l from\"../../../../geometry/Point.js\";import e from\"../../../../layers/support/LOD.js\";import n from\"../../../../layers/support/TileInfo.js\";const s=1e-6;function r(l,e){if(l===e)return!0;if(null==l&&null!=e)return!1;if(null!=l&&null==e)return!1;if(null==l||null==e)return!1;if(!l.spatialReference.equals(e.spatialReference)||l.dpi!==e.dpi)return!1;const n=l.origin,r=e.origin;if(Math.abs(n.x-r.x)>=s||Math.abs(n.y-r.y)>=s)return!1;let o,t;l.lods[0].scale>e.lods[0].scale?(o=l,t=e):(t=l,o=e);for(let i=o.lods[0].scale;i>=t.lods[t.lods.length-1].scale-s;i/=2)if(Math.abs(i-t.lods[0].scale)<s)return!0;return!1}function o(s,r){if(s===r)return s;if(null==s&&null!=r)return r;if(null!=s&&null==r)return s;if(null==s||null==r)return null;const o=s.size[0],t=s.format,i=s.dpi,u=new l({x:s.origin.x,y:s.origin.y}),a=s.spatialReference,f=s.lods[0].scale>r.lods[0].scale?s.lods[0]:r.lods[0],d=s.lods[s.lods.length-1].scale<=r.lods[r.lods.length-1].scale?s.lods[s.lods.length-1]:r.lods[r.lods.length-1],c=f.scale,p=f.resolution,g=d.scale,h=[];let m=c,y=p,x=0;for(;m>g;)h.push(new e({level:x,resolution:y,scale:m})),x++,m/=2,y/=2;return new n({size:[o,o],dpi:i,format:t||\"pbf\",origin:u,lods:h,spatialReference:a})}export{r as areSchemasOverlapping,o as unionTileInfos};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import t from\"../request.js\";import{createTask as r}from\"../core/asyncUtils.js\";import o from\"../core/Error.js\";import{clone as s}from\"../core/lang.js\";import{unwrap as i}from\"../core/maybe.js\";import{MultiOriginJSONMixin as l}from\"../core/MultiOriginJSONSupport.js\";import{throwIfAbortError as a}from\"../core/promiseUtils.js\";import{urlToObject as n,addQueryParameters as p,isProtocolRelative as y,normalize as u}from\"../core/urlUtils.js\";import{property as c}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{reader as m}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as h}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as d}from\"../core/accessorSupport/decorators/writer.js\";import S from\"../geometry/Extent.js\";import f from\"../geometry/SpatialReference.js\";import g from\"./Layer.js\";import{APIKeyMixin as v}from\"./mixins/APIKeyMixin.js\";import{ArcGISCachedService as j}from\"./mixins/ArcGISCachedService.js\";import{ArcGISService as _}from\"./mixins/ArcGISService.js\";import{BlendLayer as w}from\"./mixins/BlendLayer.js\";import{CustomParametersMixin as b}from\"./mixins/CustomParametersMixin.js\";import{OperationalLayer as U}from\"./mixins/OperationalLayer.js\";import{PortalLayer as T}from\"./mixins/PortalLayer.js\";import{ScaleRangeLayer as x}from\"./mixins/ScaleRangeLayer.js\";import{checkWebPSupport as L}from\"./support/imageUtils.js\";import I from\"./support/SpriteSource.js\";import R from\"./support/TileInfo.js\";import{loadMetadata as P}from\"./support/vectorTileLayerLoader.js\";import{createForItemRead as O}from\"../portal/support/jsonContext.js\";import{parseKnownArcGISOnlineDomain as M}from\"../portal/support/urlUtils.js\";import{e as V}from\"../chunks/persistableUrlUtils.js\";import{areSchemasOverlapping as D,unionTileInfos as k}from\"../views/2d/engine/vectorTiles/tileInfoUtils.js\";import C from\"../views/2d/engine/vectorTiles/style/StyleRepository.js\";import{getWebGLCapabilities as N}from\"../views/webgl/capabilities.js\";let B=class extends(w(x(j(_(U(T(b(v(l(g)))))))))){constructor(...e){super(...e),this._spriteSourceMap=new Map,this.currentStyleInfo=null,this.style=null,this.isReference=null,this.operationalLayerType=\"VectorTileLayer\",this.type=\"vector-tile\",this.url=null,this.showCollisionBoxes=\"none\",this.path=null}normalizeCtorArgs(e,t){return\"string\"==typeof e?{url:e,...t}:e}destroy(){if(this.sourceNameToSource)for(const e of Object.values(this.sourceNameToSource))e?.destroy();this._spriteSourceMap.clear()}async prefetchResources(e){await this.loadSpriteSource(globalThis.devicePixelRatio||1,e)}load(e){const r=this.loadFromPortal({supportedTypes:[\"Vector Tile Service\"],supportsData:!1},e).catch(a).then((async()=>{if(!this.portalItem||!this.portalItem.id)return;const r=`${this.portalItem.itemUrl}/resources/styles/root.json`;(await t(r,{...e,query:{f:\"json\",...this.customParameters,token:this.apiKey}})).data&&this.read({url:r},O(this.portalItem))})).catch(a).then((()=>this._loadStyle(e)));return this.addResolvingPromise(r),Promise.resolve(this)}get attributionDataUrl(){const e=this.currentStyleInfo,t=e&&e.serviceUrl&&n(e.serviceUrl);if(!t)return null;const r=this._getDefaultAttribution(t.path);return r?p(r,{...this.customParameters,token:this.apiKey}):null}get capabilities(){const e=this.primarySource;return e?e.capabilities:{operations:{supportsExportTiles:!1,supportsTileMap:!1},exportTiles:null}}get fullExtent(){return this.primarySource?.fullExtent||null}get parsedUrl(){return this.serviceUrl?n(this.serviceUrl):null}get serviceUrl(){return this.currentStyleInfo&&this.currentStyleInfo.serviceUrl||null}get spatialReference(){return this.tileInfo?.spatialReference??null}get styleUrl(){return this.currentStyleInfo&&this.currentStyleInfo.styleUrl||null}writeStyleUrl(e,t){e&&y(e)&&(e=`https:${e}`);const r=i(M(e));t.styleUrl=V(e,r)}get tileInfo(){const e=[];for(const r in this.sourceNameToSource)e.push(this.sourceNameToSource[r]);let t=this.primarySource?.tileInfo||new R;if(e.length>1)for(let r=0;r<e.length;r++)D(t,e[r].tileInfo)&&(t=k(t,e[r].tileInfo));return t}readVersion(e,t){return t.version?parseFloat(t.version):parseFloat(t.currentVersion)}async loadSpriteSource(e=1,t){if(!this._spriteSourceMap.has(e)){const r=N(\"2d\").maxTextureSize,o=this.currentStyleInfo?.spriteUrl?p(this.currentStyleInfo.spriteUrl,{...this.customParameters,token:this.apiKey}):null,s=new I({type:\"url\",spriteUrl:o,pixelRatio:e,spriteFormat:this.currentStyleInfo?.spriteFormat},r);await s.load(t),this._spriteSourceMap.set(e,s)}return this._spriteSourceMap.get(e)}async setSpriteSource(e,t){if(!e)return null;const r=N(\"2d\").maxTextureSize,o=e.spriteUrl,s=o?p(o,{...this.customParameters,token:this.apiKey}):null;if(!s&&\"url\"===e.type)return null;const i=new I(e,r);try{await i.load(t);const r=e.pixelRatio||1;return this._spriteSourceMap.clear(),this._spriteSourceMap.set(r,i),s&&this.currentStyleInfo&&(this.currentStyleInfo.spriteUrl=s),this.emit(\"spriteSource-change\",{spriteSource:i}),i}catch(l){a(l)}return null}async loadStyle(e,t){const o=e||this.style||this.url;return this._loadingTask&&\"string\"==typeof o&&this.url===o||(this._loadingTask?.abort(),this._loadingTask=r((e=>(this._spriteSourceMap.clear(),this._getSourceAndStyle(o,{signal:e}))),t)),this._loadingTask.promise}getStyleLayerId(e){return this.styleRepository.getStyleLayerId(e)}getStyleLayerIndex(e){return this.styleRepository.getStyleLayerIndex(e)}getPaintProperties(e){return s(this.styleRepository.getPaintProperties(e))}setPaintProperties(e,t){const r=this.styleRepository.isPainterDataDriven(e);this.styleRepository.setPaintProperties(e,t);const o=this.styleRepository.isPainterDataDriven(e);this.emit(\"paint-change\",{layer:e,paint:t,isDataDriven:r||o})}getStyleLayer(e){return s(this.styleRepository.getStyleLayer(e))}setStyleLayer(e,t){this.styleRepository.setStyleLayer(e,t),this.emit(\"style-layer-change\",{layer:e,index:t})}deleteStyleLayer(e){this.styleRepository.deleteStyleLayer(e),this.emit(\"delete-style-layer\",{layer:e})}getLayoutProperties(e){return s(this.styleRepository.getLayoutProperties(e))}setLayoutProperties(e,t){this.styleRepository.setLayoutProperties(e,t),this.emit(\"layout-change\",{layer:e,layout:t})}setStyleLayerVisibility(e,t){this.styleRepository.setStyleLayerVisibility(e,t),this.emit(\"style-layer-visibility-change\",{layer:e,visibility:t})}getStyleLayerVisibility(e){return this.styleRepository.getStyleLayerVisibility(e)}write(e,t){return t?.origin&&!this.styleUrl?(t.messages&&t.messages.push(new o(\"vectortilelayer:unsupported\",`VectorTileLayer (${this.title}, ${this.id}) with style defined by JSON only are not supported`,{layer:this})),null):super.write(e,t)}getTileUrl(e,t,r){return null}async _getSourceAndStyle(e,t){if(!e)throw new Error(\"invalid style!\");const r=await P(e,{...t,query:{...this.customParameters,token:this.apiKey}});if(\"webp\"===r.spriteFormat){await L(\"lossy\")||(r.spriteFormat=\"png\")}this._set(\"currentStyleInfo\",{...r}),\"string\"==typeof e?(this.url=e,this.style=null):(this.url=null,this.style=e),this._set(\"sourceNameToSource\",r.sourceNameToSource),this._set(\"primarySource\",r.sourceNameToSource[r.primarySourceName]),this._set(\"styleRepository\",new C(r.style)),this.read(r.layerDefinition,{origin:\"service\"}),this.emit(\"load-style\")}_getDefaultAttribution(e){const t=e.match(/^https?:\\/\\/(?:basemaps|basemapsbeta|basemapsdev)(?:-api)?\\.arcgis\\.com(\\/[^\\/]+)?\\/arcgis\\/rest\\/services\\/([^\\/]+(\\/[^\\/]+)*)\\/vectortileserver/i),r=[\"OpenStreetMap_v2\",\"OpenStreetMap_Daylight_v2\",\"OpenStreetMap_Export_v2\",\"OpenStreetMap_FTS_v2\",\"OpenStreetMap_GCS_v2\",\"World_Basemap\",\"World_Basemap_v2\",\"World_Basemap_Export_v2\",\"World_Basemap_GCS_v2\",\"World_Basemap_WGS84\",\"World_Contours_v2\"];if(!t)return;const o=t[2]&&t[2].toLowerCase();if(!o)return;const s=t[1]||\"\";for(const i of r)if(i.toLowerCase().includes(o))return u(`//static.arcgis.com/attribution/Vector${s}/${i}`)}async _loadStyle(e){return this._loadingTask?.promise??this.loadStyle(null,e)}};e([c({readOnly:!0})],B.prototype,\"attributionDataUrl\",null),e([c({type:[\"show\",\"hide\"]})],B.prototype,\"listMode\",void 0),e([c({json:{read:!0,write:!0}})],B.prototype,\"blendMode\",void 0),e([c({readOnly:!0,json:{read:!1}})],B.prototype,\"capabilities\",null),e([c({readOnly:!0})],B.prototype,\"currentStyleInfo\",void 0),e([c({json:{read:!1},readOnly:!0,type:S})],B.prototype,\"fullExtent\",null),e([c()],B.prototype,\"style\",void 0),e([c({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],B.prototype,\"isReference\",void 0),e([c({type:[\"VectorTileLayer\"]})],B.prototype,\"operationalLayerType\",void 0),e([c({readOnly:!0})],B.prototype,\"parsedUrl\",null),e([c({readOnly:!0})],B.prototype,\"serviceUrl\",null),e([c({type:f,readOnly:!0})],B.prototype,\"spatialReference\",null),e([c({readOnly:!0})],B.prototype,\"styleRepository\",void 0),e([c({readOnly:!0})],B.prototype,\"sourceNameToSource\",void 0),e([c({readOnly:!0})],B.prototype,\"primarySource\",void 0),e([c({type:String,readOnly:!0,json:{write:{ignoreOrigin:!0},origins:{\"web-document\":{write:{ignoreOrigin:!0,isRequired:!0}}}}})],B.prototype,\"styleUrl\",null),e([d([\"portal-item\",\"web-document\"],\"styleUrl\")],B.prototype,\"writeStyleUrl\",null),e([c({json:{read:!1,origins:{service:{read:!1}}},readOnly:!0,type:R})],B.prototype,\"tileInfo\",null),e([c({json:{read:!1},readOnly:!0,value:\"vector-tile\"})],B.prototype,\"type\",void 0),e([c({json:{origins:{\"web-document\":{read:{source:\"styleUrl\"}},\"portal-item\":{read:{source:\"url\"}}},write:!1,read:!1}})],B.prototype,\"url\",void 0),e([c({readOnly:!0})],B.prototype,\"version\",void 0),e([m(\"version\",[\"version\",\"currentVersion\"])],B.prototype,\"readVersion\",null),e([c({type:String})],B.prototype,\"showCollisionBoxes\",void 0),e([c({type:String,json:{origins:{\"web-scene\":{read:!0,write:!0}},read:!1}})],B.prototype,\"path\",void 0),B=e([h(\"esri.layers.VectorTileLayer\")],B);const E=B;export{E as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAIA,KAAE;AAAK,SAASC,GAAEA,IAAE;AAAC,MAAGD,GAAE,QAAOA;AAAE,QAAME,KAAE,EAAC,OAAM,4DAA2D,UAAS,oDAAmD,OAAM,oHAAmH,WAAU,2HAA0H;AAAE,SAAOF,KAAE,IAAI,QAAS,CAAAA,OAAG;AAAC,UAAMG,KAAE,IAAI;AAAM,IAAAA,GAAE,SAAO,MAAI;AAAC,MAAAA,GAAE,SAAOA,GAAE,UAAQ,MAAKH,GAAEG,GAAE,QAAM,KAAGA,GAAE,SAAO,CAAC;AAAA,IAAC,GAAEA,GAAE,UAAQ,MAAI;AAAC,MAAAA,GAAE,SAAOA,GAAE,UAAQ,MAAKH,GAAE,KAAE;AAAA,IAAC,GAAEG,GAAE,MAAI,4BAA0BD,GAAED,EAAC;AAAA,EAAC,CAAE,GAAED;AAAC;;;ACA1W,IAAM,IAAE;AAAK,IAAMI,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,gBAAcD,IAAE,KAAK,kBAAgBC,IAAE,KAAK,mBAAiB,GAAE,KAAK,qBAAmB,OAAM,KAAK,YAAU,OAAG,KAAK,eAAa,CAAC,GAAE,KAAK,QAAM,MAAK,KAAK,QAAM,MAAK,KAAK,SAAO,MAAK,KAAK,aAAW,cAAa,UAAQD,GAAE,QAAMA,GAAE,iBAAe,KAAK,qBAAmBA,GAAE,eAAcA,GAAE,eAAa,KAAK,mBAAiBA,GAAE,aAAY,KAAK,UAAQA,GAAE;AAAA,EAAS;AAAA,EAAC,IAAI,cAAa;AAAC,UAAMA,KAAE,CAAC;AAAE,eAAUC,MAAK,KAAK,aAAa,CAAAD,GAAE,KAAKC,EAAC;AAAE,WAAOD,GAAE,KAAK,GAAEA;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,WAAO,KAAK,eAAa,KAAK,aAAaA,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,MAAM,KAAKA,IAAE;AAAC,QAAG,KAAK,SAAQ;AAAC,WAAK,aAAW;AAAU,UAAG;AAAC,cAAM,KAAK,aAAaA,EAAC,GAAE,KAAK,aAAW;AAAA,MAAQ,QAAM;AAAC,aAAK,aAAW;AAAA,MAAQ;AAAA,IAAC,MAAM,MAAK,aAAW;AAAA,EAAQ;AAAA,EAAC,MAAM,aAAaA,IAAE;AAAC,SAAK,YAAU,KAAK,mBAAiB;AAAE,UAAK,EAAC,OAAME,IAAE,QAAOC,IAAE,MAAKC,IAAE,MAAKC,GAAC,IAAE,MAAM,KAAK,eAAe,KAAK,eAAcL,EAAC,GAAED,KAAE,OAAO,KAAKM,EAAC;AAAE,QAAG,CAACN,MAAG,MAAIA,GAAE,UAAQ,CAACK,GAAE,QAAO,KAAK,eAAa,KAAK,QAAM,MAAK,MAAK,KAAK,QAAM,KAAK,SAAO;AAAG,SAAK,eAAaC,IAAE,KAAK,QAAMH,IAAE,KAAK,SAAOC;AAAE,UAAMG,KAAE,KAAK,IAAI,KAAK,iBAAgB,IAAI;AAAE,QAAGJ,KAAEI,MAAGH,KAAEG,IAAE;AAAC,YAAMN,KAAE,6BAA6B,KAAK,OAAO,0CAA0CM,EAAC;AAAW,YAAMJ,GAAE,UAAU,kCAAkC,EAAE,MAAMF,EAAC,GAAE,IAAIE,GAAE,gBAAeF,EAAC;AAAA,IAAC;AAAC,QAAIO;AAAE,aAAQN,KAAE,GAAEA,KAAEG,GAAE,QAAOH,MAAG,EAAE,CAAAM,KAAEH,GAAEH,KAAE,CAAC,IAAE,KAAIG,GAAEH,EAAC,IAAEG,GAAEH,EAAC,IAAEM,IAAEH,GAAEH,KAAE,CAAC,IAAEG,GAAEH,KAAE,CAAC,IAAEM,IAAEH,GAAEH,KAAE,CAAC,IAAEG,GAAEH,KAAE,CAAC,IAAEM;AAAE,SAAK,QAAMH;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeI,IAAET,IAAE;AAAC,QAAG,YAAUS,GAAE,MAAK;AAAC,UAAIR,IAAEI;AAAE,UAAG,KAAK,mBAAiB,GAAE;AAAC,YAAG,CAACI,GAAE,eAAe,OAAM,IAAIN,GAAE,gBAAe,oDAAoD;AAAE,QAAAF,KAAEQ,GAAE,eAAe,OAAMJ,KAAEI,GAAE,eAAe;AAAA,MAAI,OAAK;AAAC,YAAG,CAACA,GAAE,eAAe,OAAM,IAAIN,GAAE,gBAAe,qDAAqD;AAAE,QAAAF,KAAEQ,GAAE,eAAe,OAAMJ,KAAEI,GAAE,eAAe;AAAA,MAAI;AAAC,aAAM,WAAUR,MAAG,YAAWA,MAAG,UAASA,OAAI,EAAEA,GAAE,IAAI,KAAG,EAAEA,GAAE,IAAI,KAAG,EAAC,OAAMA,GAAE,OAAM,QAAOA,GAAE,QAAO,MAAK,IAAI,WAAWA,GAAE,IAAI,GAAE,MAAKI,GAAC,IAAE,EAAC,GAAG,EAAEJ,EAAC,GAAE,MAAKI,GAAC;AAAA,IAAC;AAAC,UAAMG,KAAE,EAAE,KAAK,OAAO,GAAEE,KAAEF,GAAE,QAAM,MAAI,EAAEA,GAAE,KAAK,IAAE,IAAGG,KAAE,KAAK,YAAU,QAAM,IAAG,IAAE,GAAGH,GAAE,IAAI,GAAGG,EAAC,IAAI,KAAK,kBAAkB,GAAGD,EAAC,IAAGE,KAAE,GAAGJ,GAAE,IAAI,GAAGG,EAAC,QAAQD,EAAC,IAAG,CAACG,IAAEC,EAAC,IAAE,MAAM,QAAQ,IAAI,CAAC,EAAEF,IAAEZ,EAAC,GAAE,EAAE,GAAE,EAAC,cAAa,SAAQ,GAAGA,GAAC,CAAC,CAAC,CAAC;AAAE,WAAM,EAAC,GAAG,EAAEc,GAAE,IAAI,GAAE,MAAKD,GAAE,KAAI;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEZ,IAAE;AAAC,QAAMC,KAAE,SAAS,cAAc,QAAQ,GAAEO,KAAEP,GAAE,WAAW,IAAI;AAAE,EAAAA,GAAE,QAAMD,GAAE,OAAMC,GAAE,SAAOD,GAAE,QAAOQ,GAAE,UAAUR,IAAE,GAAE,GAAEA,GAAE,OAAMA,GAAE,MAAM;AAAE,QAAME,KAAEM,GAAE,aAAa,GAAE,GAAER,GAAE,OAAMA,GAAE,MAAM;AAAE,SAAM,EAAC,OAAMA,GAAE,OAAM,QAAOA,GAAE,QAAO,MAAK,IAAI,WAAWE,GAAE,IAAI,EAAC;AAAC;;;ACArgF,IAAMY,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,MAAIA;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAgB;AAAC,WAAO,KAAK,sBAAoB,KAAK,oBAAkB,EAAE,KAAK,GAAG,EAAE,KAAM,CAAAA,OAAGA,GAAE,KAAK,KAAM,IAAG,KAAK;AAAA,EAAiB;AAAA,EAAC,MAAM,QAAQA,IAAEC,IAAE;AAAC,UAAMF,KAAE,MAAM,KAAK,eAAe;AAAE,WAAO,EAAEE,EAAC,GAAE,KAAK,mBAAmBF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEE,IAAE;AAAC,UAAMH,KAAE,CAACG,EAAC;AAAE,QAAGA,GAAE,QAAM,KAAGA,GAAE,MAAI,KAAGA,GAAE,MAAI,KAAGA,GAAE,OAAKA,GAAE,QAAM,KAAGA,GAAE,OAAKA,GAAE,QAAM,EAAE,QAAO;AAAK,QAAIC,KAAED;AAAE,WAAK,MAAIC,GAAE,QAAO,CAAAA,KAAE,IAAIH,GAAEG,GAAE,QAAM,GAAEA,GAAE,OAAK,GAAEA,GAAE,OAAK,GAAEA,GAAE,KAAK,GAAEJ,GAAE,KAAKI,EAAC;AAAE,QAAIC,IAAEC,IAAEC,KAAEN,IAAEO,KAAER,GAAE,IAAI;AAAE,QAAG,MAAIO,GAAE,QAAOC;AAAE,WAAKR,GAAE,SAAQ,KAAGK,KAAEL,GAAE,IAAI,GAAEM,MAAG,IAAED,GAAE,SAAO,IAAEA,GAAE,QAAM,IAAGE,IAAE;AAAC,UAAG,MAAIA,GAAED,EAAC,GAAE;AAAC,QAAAE,KAAE;AAAK;AAAA,MAAK;AAAC,UAAG,MAAID,GAAED,EAAC,GAAE;AAAC,QAAAE,KAAEH;AAAE;AAAA,MAAK;AAAC,MAAAG,KAAEH,IAAEE,KAAEA,GAAED,EAAC;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAC;;;ACAznB,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,WAASD,IAAE,KAAK,gBAAcC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeA,IAAE;AAAC,WAAO,KAAK,sBAAoB,KAAK,oBAAkB,EAAE,KAAK,eAAc,EAAC,OAAM,EAAC,GAAGA,MAAA,gBAAAA,GAAG,MAAK,EAAC,CAAC,EAAE,KAAM,CAAAD,OAAGA,GAAE,KAAK,KAAM,IAAG,KAAK;AAAA,EAAiB;AAAA,EAAC,QAAQA,IAAED,IAAE;AAAC,UAAK,EAAC,OAAMG,IAAE,KAAIC,IAAE,KAAIC,GAAC,IAAEJ,IAAEK,KAAE,IAAIL,GAAEA,EAAC;AAAE,WAAO,KAAK,SAAS,0BAA0BE,IAAEC,IAAEC,IAAEC,IAAEN,EAAC,EAAE,KAAM,OAAKM,GAAE,QAAML,GAAE,OAAMK,GAAG,EAAE,MAAO,CAAAL,OAAG;AAAC,UAAG,EAAEA,EAAC,EAAE,OAAMA;AAAE,aAAO;AAAA,IAAI,CAAE;AAAA,EAAC;AAAC;;;ACA/T,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,WAASA,IAAE,KAAK,WAAS,MAAK,KAAK,mBAAiB,MAAK,KAAK,gBAAc,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,aAAO,KAAK,aAAW,KAAK,mBAAiB,IAAI,mBAAgB,KAAK,WAAS,KAAK,aAAa,KAAK,UAAS,KAAK,iBAAiB,MAAM;AAAG,UAAMC,KAAE,KAAK;AAAc,WAAOA,GAAE,KAAKD,EAAC,GAAE,EAAEA,IAAG,MAAI;AAAC,MAAAC,GAAE,MAAO,CAAAD,OAAGE,GAAEF,EAAC,CAAE,KAAG,KAAK,iBAAiB,MAAM;AAAA,IAAC,CAAE,GAAE,KAAK,SAAS,KAAM,CAAAA,OAAG,EAAEA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaG,IAAEF,IAAE;AAAC,UAAK,EAAC,MAAKG,GAAC,IAAE,MAAM,EAAED,IAAE,EAAC,cAAa,gBAAe,QAAOF,GAAC,CAAC;AAAE,WAAOG;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAE,oBAAI;AAAI,SAASC,GAAEN,IAAEG,IAAEF,IAAEG,IAAEG,IAAE;AAAC,SAAOC,GAAER,GAAE,QAAQ,WAAUG,GAAE,SAAS,CAAC,EAAE,QAAQ,WAAUF,GAAE,SAAS,CAAC,EAAE,QAAQ,WAAUG,GAAE,SAAS,CAAC,GAAEG,EAAC;AAAC;AAAC,SAASC,GAAER,IAAEG,IAAE;AAAC,SAAO,EAAEE,IAAEL,IAAG,MAAI,IAAID,GAAEC,EAAC,CAAE,EAAE,QAAQG,EAAC,EAAE,KAAM,CAAAA,QAAIE,GAAE,OAAOL,EAAC,GAAEG,GAAG,EAAE,MAAO,CAAAA,OAAG;AAAC,UAAME,GAAE,OAAOL,EAAC,GAAEG;AAAA,EAAC,CAAE;AAAC;;;ACAve,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAY,GAAEA,IAAEC,IAAE;AAAC,SAAK,UAAQ,MAAK,KAAK,WAAS,MAAK,KAAK,eAAa,MAAK,KAAK,aAAW,MAAK,KAAK,OAAK,GAAE,KAAK,YAAUD;AAAE,UAAME,KAAE,EAAE,KAAK,SAAS,GAAEC,KAAE,EAAEF,EAAC,GAAEG,KAAED,GAAE;AAAM,QAAGD,GAAE,UAAQG,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,YAAMC,KAAE,EAAEF,GAAEC,EAAC,CAAC;AAAE,MAAAC,OAAI,EAAEA,GAAE,IAAI,MAAIA,GAAE,OAAK,EAAEJ,GAAE,MAAKI,GAAE,IAAI,IAAGF,GAAEC,EAAC,IAAE,GAAEC,GAAE,MAAK,EAAC,GAAGJ,GAAE,OAAM,GAAGI,GAAE,MAAK,CAAC;AAAA,IAAE;AAAC,SAAK,cAAYF;AAAE,UAAMG,KAAEN,GAAE,gBAAcA,GAAE,aAAa,MAAM,GAAG,EAAE,IAAK,CAAAI,OAAGA,GAAE,YAAY,EAAE,KAAK,CAAE,GAAEG,KAAE,UAAKP,MAAA,gBAAAA,GAAG,qBAAmB,IAAE,UAAKM,MAAA,gBAAAA,GAAG,SAAS,aAAWE,KAAED,MAAGP,GAAE,eAAe,qBAAqB,IAAEA,GAAE,sBAAoB;AAAE,SAAK,eAAa,EAAC,YAAW,EAAC,qBAAoBO,IAAE,iBAAgB,EAAC,GAAE,aAAYA,KAAE,EAAC,qBAAoB,CAACC,GAAC,IAAE,KAAI,GAAE,KAAK,WAASC,GAAEP,GAAE,UAASA,IAAE,MAAK,EAAC,iBAAgB,KAAE,CAAC;AAAE,UAAM,IAAEF,GAAE,UAAQ,GAAE,EAAEC,GAAE,MAAKD,GAAE,OAAO,GAAEC,GAAE,SAAO,CAAC,CAAC,IAAE;AAAK,SAAG,KAAK,OAAK,eAAc,KAAK,UAAQ,IAAIS,GAAE,IAAI,EAAE,EAAC,OAAM,EAAC,WAAUT,IAAE,UAAS,KAAK,UAAS,MAAK,eAAc,aAAY,KAAK,YAAW,EAAC,CAAC,GAAE,CAAC,KAAG,MAAI,KAAK,UAAQ,IAAIU,GAAE,CAAC,IAAG,KAAK,aAAWC,GAAE,SAASZ,GAAE,UAAU;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,MAAM,UAAUI,IAAES,IAAE;AAJ5/C;AAI6/C,WAAO,QAAM,UAAK,YAAL,mBAAc,QAAQT,IAAES,QAAKT;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAES,IAAEC,IAAEJ,IAAE;AAAC,UAAMC,KAAE,KAAK,YAAYE,KAAE,KAAK,YAAY,MAAM;AAAE,WAAOF,GAAEA,IAAEP,IAAES,IAAEC,IAAEJ,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBN,IAAE;AAAC,UAAMS,KAAE,KAAK,UAASC,KAAEV,GAAE;AAAS,QAAG,CAACS,GAAE,iBAAiB,OAAOC,GAAE,gBAAgB,EAAE,QAAM;AAAG,QAAG,CAACD,GAAE,OAAO,OAAOC,GAAE,MAAM,EAAE,QAAM;AAAG,QAAG,KAAK,MAAMD,GAAE,GAAG,MAAI,KAAK,MAAMC,GAAE,GAAG,EAAE,QAAM;AAAG,UAAMJ,KAAEG,GAAE,MAAKF,KAAEG,GAAE,MAAKT,KAAE,KAAK,IAAIK,GAAE,QAAOC,GAAE,MAAM;AAAE,aAAQI,KAAE,GAAEA,KAAEV,IAAEU,MAAI;AAAC,YAAMX,KAAEM,GAAEK,EAAC,GAAEF,KAAEF,GAAEI,EAAC;AAAE,UAAGX,GAAE,UAAQS,GAAE,SAAO,KAAK,MAAMT,GAAE,KAAK,MAAI,KAAK,MAAMS,GAAE,KAAK,EAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAC;;;ACApnD,IAAMG,KAAE,EAAE,YAAU,EAAE,SAAS,GAAG;AAAmB,eAAe,EAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAC,QAAO,MAAK,YAAW,MAAK,WAAU,MAAK,iBAAgB,MAAK,OAAM,MAAK,WAAU,MAAK,UAAS,MAAK,oBAAmB,CAAC,GAAE,mBAAkB,IAAG,cAAa,MAAK,GAAE,CAACC,IAAEC,EAAC,IAAE,YAAU,OAAOJ,KAAE,CAACA,IAAE,IAAI,IAAE,CAAC,MAAKA,GAAE,OAAO;AAAE,QAAMK,GAAEH,IAAE,QAAOF,IAAEI,IAAEH,EAAC;AAAE,QAAMK,KAAE,EAAC,iBAAgBJ,GAAE,iBAAgB,KAAIC,IAAE,YAAWD,GAAE,WAAU,OAAMA,GAAE,OAAM,UAASA,GAAE,UAAS,WAAUA,GAAE,MAAM,UAAQ,EAAEA,GAAE,WAAUA,GAAE,MAAM,MAAM,GAAE,cAAaA,GAAE,cAAa,WAAUA,GAAE,MAAM,UAAQ,EAAEA,GAAE,WAAUA,GAAE,MAAM,MAAM,GAAE,oBAAmBA,GAAE,oBAAmB,mBAAkBA,GAAE,kBAAiB;AAAE,SAAOK,GAAED,GAAE,SAAS,GAAEC,GAAED,GAAE,SAAS,GAAEA;AAAC;AAAC,SAASC,GAAEP,IAAE;AAAC,MAAG,CAACA,GAAE;AAAO,QAAMC,KAAE,EAAED,EAAC;AAAE,EAAAD,MAAG,CAACA,GAAE,SAASE,EAAC,KAAGF,GAAE,KAAKE,EAAC;AAAC;AAAC,SAAS,KAAKD,IAAE;AAAC,MAAIC;AAAE,aAAUC,MAAKF,GAAE,KAAG,QAAME,GAAE,KAAG,GAAEA,EAAC,GAAE;AAAC,QAAGD,IAAE;AAAC,YAAMD,KAAEC,GAAE,MAAM,KAAK,EAAE,CAAC;AAAE,MAAAA,KAAED,KAAE,MAAIE,GAAE,KAAK;AAAA,IAAC;AAAA,EAAC,MAAM,CAAAD,KAAE,EAAEC,EAAC,IAAEA,KAAE,EAAED,IAAEC,EAAC;AAAE,SAAOD,KAAE,GAAEA,EAAC,IAAE;AAAM;AAAC,eAAeI,GAAEL,IAAEG,IAAEC,IAAEE,IAAEE,IAAE;AAAC,MAAIC,IAAEC,IAAEC;AAAE,MAAG,EAAEH,EAAC,GAAE,YAAU,OAAOJ,IAAE;AAAC,UAAMJ,KAAE,EAAEI,EAAC;AAAE,IAAAG,GAAEP,EAAC,GAAEW,KAAE,MAAM,EAAEX,IAAE,EAAC,GAAGQ,IAAE,cAAa,QAAO,OAAM,EAAC,GAAE,QAAO,GAAGA,MAAA,gBAAAA,GAAG,MAAK,EAAC,CAAC,GAAEG,GAAE,QAAMF,OAAIA,KAAEA,GAAE,QAAQ,WAAU,QAAQ,IAAGC,OAAIA,KAAEA,GAAE,QAAQ,WAAU,QAAQ,KAAID,KAAET,IAAEU,KAAEV;AAAA,EAAC,MAAM,SAAMI,OAAIO,KAAE,EAAC,MAAKP,GAAC,GAAEK,KAAEL,GAAE,WAAS,MAAKM,KAAEJ;AAAG,QAAMM,KAAED,MAAA,gBAAAA,GAAG;AAAK,MAAGE,GAAED,EAAC,EAAE,QAAOZ,GAAE,WAASS,MAAG,MAAK,EAAET,IAAEY,IAAEF,IAAEF,EAAC;AAAE,MAAGM,GAAEF,EAAC,EAAE,QAAOZ,GAAE,YAAUe,GAAEf,IAAEY,IAAEF,IAAE,OAAGP,IAAEK,EAAC,KAAGR,GAAE,YAAUS,MAAG,MAAKM,GAAEf,IAAEY,IAAEF,IAAE,MAAGP,IAAEK,EAAC;AAAG,QAAM,IAAI,MAAM,oEAAoE;AAAC;AAAC,SAASK,GAAEb,IAAE;AAAC,SAAM,CAAC,EAACA,MAAA,gBAAAA,GAAG;AAAO;AAAC,SAASc,GAAEd,IAAE;AAAC,SAAM,CAACa,GAAEb,EAAC;AAAC;AAAC,eAAe,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEF,KAAE,GAAEA,EAAC,IAAE,EAAE;AAAE,EAAAF,GAAE,YAAUI,IAAEJ,GAAE,QAAMC,IAAED,GAAE,YAAUO,GAAEP,GAAE,QAAQ,GAAEC,GAAE,eAAe,KAAG,WAASA,GAAE,eAAe,EAAE,YAAY,MAAID,GAAE,eAAa;AAAQ,QAAMM,KAAE,CAAC;AAAE,MAAGL,GAAE,WAASA,GAAE,QAAQ,MAAK;AAAC,UAAMC,KAAED,GAAE,QAAQ;AAAK,IAAAC,GAAE,MAAI,MAAMG,GAAEL,IAAE,QAAO,EAAEI,IAAEF,GAAE,GAAG,GAAE,QAAOC,EAAC,IAAEG,GAAE,KAAKD,GAAEL,IAAE,QAAOE,IAAEE,IAAED,EAAC,CAAC;AAAA,EAAC;AAAC,aAAUK,MAAK,OAAO,KAAKP,GAAE,OAAO,EAAE,YAASO,MAAG,aAAWP,GAAE,QAAQO,EAAC,EAAE,SAAOP,GAAE,QAAQO,EAAC,EAAE,MAAIF,GAAE,KAAKD,GAAEL,IAAEQ,IAAE,EAAEJ,IAAEH,GAAE,QAAQO,EAAC,EAAE,GAAG,GAAE,QAAOL,EAAC,CAAC,IAAEF,GAAE,QAAQO,EAAC,EAAE,SAAOF,GAAE,KAAKD,GAAEL,IAAEQ,IAAEP,GAAE,QAAQO,EAAC,GAAEJ,IAAED,EAAC,CAAC;AAAG,QAAM,QAAQ,IAAIG,EAAC;AAAC;AAAC,eAAeS,GAAEf,IAAEC,IAAEC,IAAEC,IAAEC,IAAEE,IAAE;AAAC,QAAME,KAAEN,KAAE,GAAEA,EAAC,IAAE,MAAI,EAAE,GAAE,IAAE,EAAED,IAAEO,EAAC,GAAEE,KAAE,IAAIL,GAAED,IAAE,GAAEI,KAAEF,MAAA,gBAAAA,GAAG,UAAO,CAAC,CAAC,GAAE,CAAC;AAAE,MAAG,CAACH,MAAGH,GAAE,qBAAqBA,GAAE,oBAAmB;AAAC,UAAMC,KAAED,GAAE,mBAAmBA,GAAE,iBAAiB;AAAE,QAAG,CAACC,GAAE,iBAAiBS,EAAC,EAAE;AAAO,YAAMA,GAAE,eAAa,QAAMT,GAAE,aAAWA,GAAE,WAAW,MAAMS,GAAE,UAAU,IAAET,GAAE,aAAWS,GAAE,WAAW,MAAM,IAAGT,GAAE,YAAUS,GAAE,YAAUT,GAAE,SAAS,KAAK,SAAOS,GAAE,SAAS,KAAK,WAAST,GAAE,WAASS,GAAE;AAAA,EAAS;AAAC,MAAGP,MAAGH,GAAE,aAAWQ,IAAER,GAAE,SAAOC,IAAED,GAAE,kBAAgB,GAAEA,GAAE,oBAAkBI,IAAEJ,GAAE,aAAWO,GAAEP,GAAE,SAAS,KAAGO,GAAEC,EAAC,GAAER,GAAE,mBAAmBI,EAAC,IAAEM,IAAE,CAACV,GAAE,OAAM;AAAC,QAAG,QAAMC,GAAE,cAAc,OAAM,IAAI;AAAM,WAAM,YAAU,OAAOA,GAAE,gBAAcI,GAAEL,IAAE,IAAG,EAAEQ,IAAEP,GAAE,eAAc,WAAW,GAAE,QAAOK,EAAC,IAAED,GAAEL,IAAE,IAAGC,GAAE,eAAc,EAAEO,IAAE,WAAW,GAAEF,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEN,IAAEC,IAAE;AAAC,MAAGD,GAAE,eAAe,UAAU,EAAE,QAAOA;AAAE,QAAME,KAAE,EAAC,MAAK,uBAAoB,MAAK,uBAAoB,MAAK,sBAAmB,MAAK,sBAAmB,kBAAiB,EAAC,MAAK,OAAM,EAAC,GAAEC,KAAE;AAAI,MAAIC,KAAE,mBAAkBE,KAAE;AAAkB,QAAME,KAAE,CAAC,GAAEC,KAAET,GAAE,eAAe,SAAS,IAAE,CAACA,GAAE,UAAQ,GAAE,IAAEA,GAAE,eAAe,SAAS,IAAE,CAACA,GAAE,UAAQ;AAAG,WAAQU,KAAE,GAAEA,MAAG,GAAEA,KAAI,CAAAA,MAAGD,MAAGD,GAAE,KAAK,EAAC,OAAME,IAAE,OAAMJ,IAAE,YAAWF,GAAC,CAAC,GAAEA,MAAG,GAAEE,MAAG;AAAE,aAAUI,MAAKV,GAAE,SAAO,CAAC,EAAE,CAAAO,GAAE,EAAEN,IAAES,EAAC,CAAC;AAAE,SAAM,EAAC,cAAa,aAAY,eAAcR,IAAE,YAAWA,IAAE,UAAS,GAAE,UAAS,GAAE,OAAMF,GAAE,OAAM,UAAS,EAAC,MAAKG,IAAE,MAAKA,IAAE,KAAI,IAAG,QAAO,OAAM,QAAO,EAAC,GAAE,oBAAiB,GAAE,kBAAe,GAAE,MAAKK,IAAE,kBAAiB,EAAC,MAAK,OAAM,EAAC,EAAC;AAAC;;;ACA3nH,IAAMQ,KAAE;AAAK,SAASC,GAAEC,IAAEC,IAAE;AAAC,MAAGD,OAAIC,GAAE,QAAM;AAAG,MAAG,QAAMD,MAAG,QAAMC,GAAE,QAAM;AAAG,MAAG,QAAMD,MAAG,QAAMC,GAAE,QAAM;AAAG,MAAG,QAAMD,MAAG,QAAMC,GAAE,QAAM;AAAG,MAAG,CAACD,GAAE,iBAAiB,OAAOC,GAAE,gBAAgB,KAAGD,GAAE,QAAMC,GAAE,IAAI,QAAM;AAAG,QAAMC,KAAEF,GAAE,QAAOD,KAAEE,GAAE;AAAO,MAAG,KAAK,IAAIC,GAAE,IAAEH,GAAE,CAAC,KAAGD,MAAG,KAAK,IAAII,GAAE,IAAEH,GAAE,CAAC,KAAGD,GAAE,QAAM;AAAG,MAAIK,IAAEC;AAAE,EAAAJ,GAAE,KAAK,CAAC,EAAE,QAAMC,GAAE,KAAK,CAAC,EAAE,SAAOE,KAAEH,IAAEI,KAAEH,OAAIG,KAAEJ,IAAEG,KAAEF;AAAG,WAAQI,KAAEF,GAAE,KAAK,CAAC,EAAE,OAAME,MAAGD,GAAE,KAAKA,GAAE,KAAK,SAAO,CAAC,EAAE,QAAMN,IAAEO,MAAG,EAAE,KAAG,KAAK,IAAIA,KAAED,GAAE,KAAK,CAAC,EAAE,KAAK,IAAEN,GAAE,QAAM;AAAG,SAAM;AAAE;AAAC,SAASK,GAAEL,IAAEC,IAAE;AAAC,MAAGD,OAAIC,GAAE,QAAOD;AAAE,MAAG,QAAMA,MAAG,QAAMC,GAAE,QAAOA;AAAE,MAAG,QAAMD,MAAG,QAAMC,GAAE,QAAOD;AAAE,MAAG,QAAMA,MAAG,QAAMC,GAAE,QAAO;AAAK,QAAMI,KAAEL,GAAE,KAAK,CAAC,GAAEM,KAAEN,GAAE,QAAOO,KAAEP,GAAE,KAAI,IAAE,IAAIQ,GAAE,EAAC,GAAER,GAAE,OAAO,GAAE,GAAEA,GAAE,OAAO,EAAC,CAAC,GAAES,KAAET,GAAE,kBAAiBU,KAAEV,GAAE,KAAK,CAAC,EAAE,QAAMC,GAAE,KAAK,CAAC,EAAE,QAAMD,GAAE,KAAK,CAAC,IAAEC,GAAE,KAAK,CAAC,GAAEU,KAAEX,GAAE,KAAKA,GAAE,KAAK,SAAO,CAAC,EAAE,SAAOC,GAAE,KAAKA,GAAE,KAAK,SAAO,CAAC,EAAE,QAAMD,GAAE,KAAKA,GAAE,KAAK,SAAO,CAAC,IAAEC,GAAE,KAAKA,GAAE,KAAK,SAAO,CAAC,GAAEW,KAAEF,GAAE,OAAMG,KAAEH,GAAE,YAAWI,KAAEH,GAAE,OAAMI,KAAE,CAAC;AAAE,MAAIC,KAAEJ,IAAEK,KAAEJ,IAAEK,KAAE;AAAE,SAAKF,KAAEF,KAAG,CAAAC,GAAE,KAAK,IAAIF,GAAE,EAAC,OAAMK,IAAE,YAAWD,IAAE,OAAMD,GAAC,CAAC,CAAC,GAAEE,MAAIF,MAAG,GAAEC,MAAG;AAAE,SAAO,IAAIE,GAAE,EAAC,MAAK,CAACd,IAAEA,EAAC,GAAE,KAAIE,IAAE,QAAOD,MAAG,OAAM,QAAO,GAAE,MAAKS,IAAE,kBAAiBN,GAAC,CAAC;AAAC;;;ACAy1B,IAAI,IAAE,cAAc,EAAEW,GAAEC,GAAEC,GAAEC,GAAE,EAAEC,GAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,mBAAiB,oBAAI,OAAI,KAAK,mBAAiB,MAAK,KAAK,QAAM,MAAK,KAAK,cAAY,MAAK,KAAK,uBAAqB,mBAAkB,KAAK,OAAK,eAAc,KAAK,MAAI,MAAK,KAAK,qBAAmB,QAAO,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,kBAAkBA,IAAEL,IAAE;AAAC,WAAM,YAAU,OAAOK,KAAE,EAAC,KAAIA,IAAE,GAAGL,GAAC,IAAEK;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,KAAK,mBAAmB,YAAUA,MAAK,OAAO,OAAO,KAAK,kBAAkB,EAAE,CAAAA,MAAA,gBAAAA,GAAG;AAAU,SAAK,iBAAiB,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAkBA,IAAE;AAAC,UAAM,KAAK,iBAAiB,WAAW,oBAAkB,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,UAAMC,KAAE,KAAK,eAAe,EAAC,gBAAe,CAAC,qBAAqB,GAAE,cAAa,MAAE,GAAED,EAAC,EAAE,MAAM,CAAC,EAAE,KAAM,YAAS;AAAC,UAAG,CAAC,KAAK,cAAY,CAAC,KAAK,WAAW,GAAG;AAAO,YAAMC,KAAE,GAAG,KAAK,WAAW,OAAO;AAA8B,OAAC,MAAM,EAAEA,IAAE,EAAC,GAAGD,IAAE,OAAM,EAAC,GAAE,QAAO,GAAG,KAAK,kBAAiB,OAAM,KAAK,OAAM,EAAC,CAAC,GAAG,QAAM,KAAK,KAAK,EAAC,KAAIC,GAAC,GAAED,GAAE,KAAK,UAAU,CAAC;AAAA,IAAC,CAAE,EAAE,MAAM,CAAC,EAAE,KAAM,MAAI,KAAK,WAAWA,EAAC,CAAE;AAAE,WAAO,KAAK,oBAAoBC,EAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAoB;AAAC,UAAMD,KAAE,KAAK,kBAAiBL,KAAEK,MAAGA,GAAE,cAAY,EAAEA,GAAE,UAAU;AAAE,QAAG,CAACL,GAAE,QAAO;AAAK,UAAMM,KAAE,KAAK,uBAAuBN,GAAE,IAAI;AAAE,WAAOM,KAAE,GAAEA,IAAE,EAAC,GAAG,KAAK,kBAAiB,OAAM,KAAK,OAAM,CAAC,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,eAAc;AAAC,UAAMD,KAAE,KAAK;AAAc,WAAOA,KAAEA,GAAE,eAAa,EAAC,YAAW,EAAC,qBAAoB,OAAG,iBAAgB,MAAE,GAAE,aAAY,KAAI;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAJh6G;AAIi6G,aAAO,UAAK,kBAAL,mBAAoB,eAAY;AAAA,EAAI;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,aAAW,EAAE,KAAK,UAAU,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,oBAAkB,KAAK,iBAAiB,cAAY;AAAA,EAAI;AAAA,EAAC,IAAI,mBAAkB;AAJxnH;AAIynH,aAAO,UAAK,aAAL,mBAAe,qBAAkB;AAAA,EAAI;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,oBAAkB,KAAK,iBAAiB,YAAU;AAAA,EAAI;AAAA,EAAC,cAAcA,IAAEL,IAAE;AAAC,IAAAK,MAAG,GAAEA,EAAC,MAAIA,KAAE,SAASA,EAAC;AAAI,UAAMC,KAAED,GAAEE,GAAEF,EAAC,CAAC;AAAE,IAAAL,GAAE,WAASQ,GAAEH,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAJr1H;AAIs1H,UAAMD,KAAE,CAAC;AAAE,eAAUC,MAAK,KAAK,mBAAmB,CAAAD,GAAE,KAAK,KAAK,mBAAmBC,EAAC,CAAC;AAAE,QAAIN,OAAE,UAAK,kBAAL,mBAAoB,aAAU,IAAIS;AAAE,QAAGJ,GAAE,SAAO,EAAE,UAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAA,GAAEN,IAAEK,GAAEC,EAAC,EAAE,QAAQ,MAAIN,KAAEI,GAAEJ,IAAEK,GAAEC,EAAC,EAAE,QAAQ;AAAG,WAAON;AAAA,EAAC;AAAA,EAAC,YAAYK,IAAEL,IAAE;AAAC,WAAOA,GAAE,UAAQ,WAAWA,GAAE,OAAO,IAAE,WAAWA,GAAE,cAAc;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBK,KAAE,GAAEL,IAAE;AAJpqI;AAIqqI,QAAG,CAAC,KAAK,iBAAiB,IAAIK,EAAC,GAAE;AAAC,YAAMC,KAAEI,GAAE,IAAI,EAAE,gBAAeN,OAAE,UAAK,qBAAL,mBAAuB,aAAU,GAAE,KAAK,iBAAiB,WAAU,EAAC,GAAG,KAAK,kBAAiB,OAAM,KAAK,OAAM,CAAC,IAAE,MAAKH,KAAE,IAAIU,GAAE,EAAC,MAAK,OAAM,WAAUP,IAAE,YAAWC,IAAE,eAAa,UAAK,qBAAL,mBAAuB,aAAY,GAAEC,EAAC;AAAE,YAAML,GAAE,KAAKD,EAAC,GAAE,KAAK,iBAAiB,IAAIK,IAAEJ,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK,iBAAiB,IAAII,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBA,IAAEL,IAAE;AAAC,QAAG,CAACK,GAAE,QAAO;AAAK,UAAMC,KAAEI,GAAE,IAAI,EAAE,gBAAeN,KAAEC,GAAE,WAAUJ,KAAEG,KAAE,GAAEA,IAAE,EAAC,GAAG,KAAK,kBAAiB,OAAM,KAAK,OAAM,CAAC,IAAE;AAAK,QAAG,CAACH,MAAG,UAAQI,GAAE,KAAK,QAAO;AAAK,UAAMO,KAAE,IAAID,GAAEN,IAAEC,EAAC;AAAE,QAAG;AAAC,YAAMM,GAAE,KAAKZ,EAAC;AAAE,YAAMM,KAAED,GAAE,cAAY;AAAE,aAAO,KAAK,iBAAiB,MAAM,GAAE,KAAK,iBAAiB,IAAIC,IAAEM,EAAC,GAAEX,MAAG,KAAK,qBAAmB,KAAK,iBAAiB,YAAUA,KAAG,KAAK,KAAK,uBAAsB,EAAC,cAAaW,GAAC,CAAC,GAAEA;AAAA,IAAC,SAAOF,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,MAAM,UAAUL,IAAEL,IAAE;AAJ7+J;AAI8+J,UAAMI,KAAEC,MAAG,KAAK,SAAO,KAAK;AAAI,WAAO,KAAK,gBAAc,YAAU,OAAOD,MAAG,KAAK,QAAMA,QAAI,UAAK,iBAAL,mBAAmB,SAAQ,KAAK,eAAaK,GAAG,CAAAJ,QAAI,KAAK,iBAAiB,MAAM,GAAE,KAAK,mBAAmBD,IAAE,EAAC,QAAOC,GAAC,CAAC,IAAIL,EAAC,IAAG,KAAK,aAAa;AAAA,EAAO;AAAA,EAAC,gBAAgBK,IAAE;AAAC,WAAO,KAAK,gBAAgB,gBAAgBA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,WAAO,KAAK,gBAAgB,mBAAmBA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,WAAO,EAAE,KAAK,gBAAgB,mBAAmBA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEL,IAAE;AAAC,UAAMM,KAAE,KAAK,gBAAgB,oBAAoBD,EAAC;AAAE,SAAK,gBAAgB,mBAAmBA,IAAEL,EAAC;AAAE,UAAMI,KAAE,KAAK,gBAAgB,oBAAoBC,EAAC;AAAE,SAAK,KAAK,gBAAe,EAAC,OAAMA,IAAE,OAAML,IAAE,cAAaM,MAAGF,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcC,IAAE;AAAC,WAAO,EAAE,KAAK,gBAAgB,cAAcA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEL,IAAE;AAAC,SAAK,gBAAgB,cAAcK,IAAEL,EAAC,GAAE,KAAK,KAAK,sBAAqB,EAAC,OAAMK,IAAE,OAAML,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBK,IAAE;AAAC,SAAK,gBAAgB,iBAAiBA,EAAC,GAAE,KAAK,KAAK,sBAAqB,EAAC,OAAMA,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,WAAO,EAAE,KAAK,gBAAgB,oBAAoBA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEL,IAAE;AAAC,SAAK,gBAAgB,oBAAoBK,IAAEL,EAAC,GAAE,KAAK,KAAK,iBAAgB,EAAC,OAAMK,IAAE,QAAOL,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBK,IAAEL,IAAE;AAAC,SAAK,gBAAgB,wBAAwBK,IAAEL,EAAC,GAAE,KAAK,KAAK,iCAAgC,EAAC,OAAMK,IAAE,YAAWL,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBK,IAAE;AAAC,WAAO,KAAK,gBAAgB,wBAAwBA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAEL,IAAE;AAAC,YAAOA,MAAA,gBAAAA,GAAG,WAAQ,CAAC,KAAK,YAAUA,GAAE,YAAUA,GAAE,SAAS,KAAK,IAAIC,GAAE,+BAA8B,oBAAoB,KAAK,KAAK,KAAK,KAAK,EAAE,uDAAsD,EAAC,OAAM,KAAI,CAAC,CAAC,GAAE,QAAM,MAAM,MAAMI,IAAEL,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWK,IAAEL,IAAEM,IAAE;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,MAAM,mBAAmBD,IAAEL,IAAE;AAAC,QAAG,CAACK,GAAE,OAAM,IAAI,MAAM,gBAAgB;AAAE,UAAMC,KAAE,MAAM,EAAED,IAAE,EAAC,GAAGL,IAAE,OAAM,EAAC,GAAG,KAAK,kBAAiB,OAAM,KAAK,OAAM,EAAC,CAAC;AAAE,QAAG,WAASM,GAAE,cAAa;AAAC,YAAMF,GAAE,OAAO,MAAIE,GAAE,eAAa;AAAA,IAAM;AAAC,SAAK,KAAK,oBAAmB,EAAC,GAAGA,GAAC,CAAC,GAAE,YAAU,OAAOD,MAAG,KAAK,MAAIA,IAAE,KAAK,QAAM,SAAO,KAAK,MAAI,MAAK,KAAK,QAAMA,KAAG,KAAK,KAAK,sBAAqBC,GAAE,kBAAkB,GAAE,KAAK,KAAK,iBAAgBA,GAAE,mBAAmBA,GAAE,iBAAiB,CAAC,GAAE,KAAK,KAAK,mBAAkB,IAAI,EAAEA,GAAE,KAAK,CAAC,GAAE,KAAK,KAAKA,GAAE,iBAAgB,EAAC,QAAO,UAAS,CAAC,GAAE,KAAK,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,uBAAuBD,IAAE;AAAC,UAAML,KAAEK,GAAE,MAAM,oJAAoJ,GAAEC,KAAE,CAAC,oBAAmB,6BAA4B,2BAA0B,wBAAuB,wBAAuB,iBAAgB,oBAAmB,2BAA0B,wBAAuB,uBAAsB,mBAAmB;AAAE,QAAG,CAACN,GAAE;AAAO,UAAMI,KAAEJ,GAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,YAAY;AAAE,QAAG,CAACI,GAAE;AAAO,UAAMH,KAAED,GAAE,CAAC,KAAG;AAAG,eAAUY,MAAKN,GAAE,KAAGM,GAAE,YAAY,EAAE,SAASR,EAAC,EAAE,QAAO,EAAE,yCAAyCH,EAAC,IAAIW,EAAC,EAAE;AAAA,EAAC;AAAA,EAAC,MAAM,WAAWP,IAAE;AAJ1yP;AAI2yP,aAAO,UAAK,iBAAL,mBAAmB,YAAS,KAAK,UAAU,MAAKA,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,MAAG,MAAKQ,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,OAAG,OAAM,EAAC,SAAQ,MAAG,gBAAe,OAAK,EAAC,SAAQ,MAAE,GAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,iBAAiB,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,MAAG,MAAK,EAAC,OAAM,EAAC,cAAa,KAAE,GAAE,SAAQ,EAAC,gBAAe,EAAC,OAAM,EAAC,cAAa,MAAG,YAAW,KAAE,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAACR,GAAE,CAAC,eAAc,cAAc,GAAE,UAAU,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,OAAG,SAAQ,EAAC,SAAQ,EAAC,MAAK,MAAE,EAAC,EAAC,GAAE,UAAS,MAAG,MAAKG,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,MAAG,OAAM,cAAa,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,SAAQ,EAAC,gBAAe,EAAC,MAAK,EAAC,QAAO,WAAU,EAAC,GAAE,eAAc,EAAC,MAAK,EAAC,QAAO,MAAK,EAAC,EAAC,GAAE,OAAM,OAAG,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,CAAC,WAAU,gBAAgB,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,GAAE,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,6BAA6B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["A", "o", "l", "n", "n", "t", "e", "s", "r", "a", "o", "d", "p", "i", "l", "g", "c", "m", "S", "l", "e", "r", "t", "i", "o", "s", "n", "a", "r", "e", "t", "l", "s", "o", "n", "i", "t", "e", "p", "r", "o", "n", "l", "s", "a", "h", "m", "f", "c", "x", "t", "s", "d", "y", "g", "n", "r", "l", "w", "e", "i", "o", "y", "e", "r", "t", "s", "o", "h", "l", "d", "n", "i", "c", "a", "f", "U", "w", "v", "s", "r", "l", "e", "n", "o", "t", "i", "w", "a", "f", "d", "c", "p", "g", "h", "m", "y", "x", "j", "t", "s", "p", "c", "o", "e", "r", "a", "y", "j", "l", "n", "i", "w", "f"]}