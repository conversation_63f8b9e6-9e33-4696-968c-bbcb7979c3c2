import {
  E,
  L
} from "./chunk-JXLVNWKF.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/rest/query/operations/editsZScale.js
function t2(e, n, o) {
  if (null == e.hasM || e.hasZ) for (const t3 of n) for (const e2 of t3) e2.length > 2 && (e2[2] *= o);
}
function i(e, o, t3) {
  if (!e && !o || !t3) return;
  const i2 = L(t3);
  s(e, t3, i2), s(o, t3, i2);
}
function s(e, n, o) {
  if (e) for (const t3 of e) f(t3.geometry, n, o);
}
function f(i2, s2, f2) {
  if (t(i2) || !i2.spatialReference || E(i2.spatialReference, s2)) return;
  const r = L(i2.spatialReference) / f2;
  if (1 !== r) {
    if ("x" in i2) null != i2.z && (i2.z *= r);
    else if ("rings" in i2) t2(i2, i2.rings, r);
    else if ("paths" in i2) t2(i2, i2.paths, r);
    else if ("points" in i2 && (null == i2.hasM || i2.hasZ)) for (const e of i2.points) e.length > 2 && (e[2] *= r);
  }
}

export {
  i
};
//# sourceMappingURL=chunk-TVTTDN54.js.map
