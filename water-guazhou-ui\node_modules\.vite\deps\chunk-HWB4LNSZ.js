import {
  a as a2
} from "./chunk-Q4VCSCSY.js";
import {
  s as s2
} from "./chunk-22GGEXM2.js";
import {
  m
} from "./chunk-EDS4WCRT.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  l
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/layers/LayerView.js
var d = class extends a2(s2(m(n.EventedMixin(v)))) {
  constructor(e2) {
    super(e2), this.layer = null, this.parent = null;
  }
  initialize() {
    this.when().catch((e2) => {
      if ("layerview:create-error" !== e2.name) {
        const t = this.layer && this.layer.id || "no id", r = this.layer && this.layer.title || "no title";
        s.getLogger(this.declaredClass).error("#resolve()", `Failed to resolve layer view (layer title: '${r}', id: '${t}')`, e2);
      }
    });
  }
  get fullOpacity() {
    return l(this.get("layer.opacity"), 1) * l(this.get("parent.fullOpacity"), 1);
  }
  get suspended() {
    return !this.canResume();
  }
  get suspendInfo() {
    return this.getSuspendInfo();
  }
  get legendEnabled() {
    var _a;
    return !this.suspended && true === ((_a = this.layer) == null ? void 0 : _a.legendEnabled);
  }
  get updating() {
    var _a;
    return !(!((_a = this.updatingHandles) == null ? void 0 : _a.updating) && !this.isUpdating());
  }
  get updatingProgress() {
    return this.updating ? 0 : 1;
  }
  get visible() {
    var _a;
    return true === ((_a = this.layer) == null ? void 0 : _a.visible);
  }
  set visible(e2) {
    this._overrideIfSome("visible", e2);
  }
  canResume() {
    var _a, _b, _c;
    return this.visible && ((_a = this.layer) == null ? void 0 : _a.loaded) && !((_b = this.parent) == null ? void 0 : _b.suspended) && ((_c = this.view) == null ? void 0 : _c.ready) || false;
  }
  getSuspendInfo() {
    const e2 = this.parent && this.parent.suspended ? this.parent.suspendInfo : {}, t = this;
    return t.view && t.view.ready || (e2.viewNotReady = true), this.layer && this.layer.loaded || (e2.layerNotLoaded = true), this.visible || (e2.layerInvisible = true), e2;
  }
  isUpdating() {
    return false;
  }
};
e([y()], d.prototype, "fullOpacity", null), e([y()], d.prototype, "layer", void 0), e([y()], d.prototype, "parent", void 0), e([y({ readOnly: true })], d.prototype, "suspended", null), e([y({ readOnly: true })], d.prototype, "suspendInfo", null), e([y({ readOnly: true })], d.prototype, "legendEnabled", null), e([y({ type: Boolean, readOnly: true })], d.prototype, "updating", null), e([y({ readOnly: true })], d.prototype, "updatingProgress", null), e([y()], d.prototype, "visible", null), e([y()], d.prototype, "view", void 0), d = e([a("esri.views.layers.LayerView")], d);
var u = d;

export {
  u
};
//# sourceMappingURL=chunk-HWB4LNSZ.js.map
