import {
  $,
  L as L2,
  c as c4,
  c2 as c5,
  d,
  f as f5,
  i as i2,
  j as j3,
  k,
  l as l3,
  m as m2,
  n as n6,
  o as o4,
  w as w4
} from "./chunk-EF4DPHSA.js";
import {
  c as c6,
  h,
  p as p6,
  v as v6
} from "./chunk-JI2BFAR3.js";
import {
  C,
  S,
  T as T3,
  _ as _2,
  j as j2,
  n2 as n4,
  n3 as n5,
  u as u3,
  v as v5,
  y as y3
} from "./chunk-6EHSQNHE.js";
import {
  f2 as f4,
  f3 as f6,
  m
} from "./chunk-Z27XYQOC.js";
import {
  s as s8
} from "./chunk-YACF4WM5.js";
import "./chunk-6KZTVN32.js";
import "./chunk-ANH6666P.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-O2BYTJI4.js";
import "./chunk-WJKHSSMC.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-5VSS44JR.js";
import {
  e as e3
} from "./chunk-WJWRKQWS.js";
import {
  n as n3,
  s as s6
} from "./chunk-PX6TFO4X.js";
import {
  s as s7
} from "./chunk-UMW4I2EJ.js";
import "./chunk-XZ2UVSB4.js";
import "./chunk-NSJUSNRV.js";
import "./chunk-YMY3DTA5.js";
import "./chunk-TNP2LXZZ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import {
  v as v4
} from "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import {
  f as f2,
  i,
  s as s5
} from "./chunk-XBS7QZIQ.js";
import "./chunk-IKGI4J4I.js";
import "./chunk-MNWHGD3K.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-6GKVSPTV.js";
import "./chunk-4FIRBBKR.js";
import {
  e as e4
} from "./chunk-EKOSN3EW.js";
import {
  a as a3
} from "./chunk-RZCOX454.js";
import "./chunk-2WMCP27R.js";
import {
  p as p4
} from "./chunk-KTB2COPC.js";
import "./chunk-HTXGAKOK.js";
import {
  o as o3
} from "./chunk-OQK7L3JR.js";
import {
  p as p5
} from "./chunk-JZKMTUDN.js";
import {
  r as r3
} from "./chunk-UCWK623G.js";
import {
  p as p3
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import "./chunk-55WN4LCX.js";
import {
  c as c3
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-ZVU4V5QV.js";
import "./chunk-2AZSZWPE.js";
import "./chunk-PTIRBOGQ.js";
import {
  x
} from "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import {
  x as x2
} from "./chunk-KE7SPCM7.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  n as n2
} from "./chunk-MIA6BJ32.js";
import {
  t as t2
} from "./chunk-NGPCXWDX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import "./chunk-IOBN373Z.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-GE5PSQPZ.js";
import {
  c as c2,
  f as f3,
  p as p2,
  u as u2
} from "./chunk-VJW7RCN7.js";
import "./chunk-MUYX6GXF.js";
import {
  T as T2
} from "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  l as l2
} from "./chunk-QUHG7NMD.js";
import "./chunk-TWFTBWXP.js";
import {
  b as b2
} from "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-WFXIWNQB.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import {
  g
} from "./chunk-TLKX5XIJ.js";
import {
  k as k2
} from "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import {
  n
} from "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import {
  c,
  v as v3
} from "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import {
  u
} from "./chunk-3WCHZJQK.js";
import {
  v as v2
} from "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w3
} from "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import {
  w as w2
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f,
  fe,
  le,
  me
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import {
  s as s4
} from "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  o as o2,
  s as s3
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  j,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T,
  a,
  v
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  p
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e as e2,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/rasterFunctionUtils.js
var e5 = { StretchFunction: { arguments: { ComputeGamma: { isDataset: false, isPublic: false, name: "ComputeGamma", type: "RasterFunctionVariable", value: false }, DRA: { isDataset: false, isPublic: false, name: "DRA", type: "RasterFunctionVariable", value: false }, EstimateStatsHistogram: { isDataset: false, isPublic: false, name: "EstimateStatsHistogram", type: "RasterFunctionVariable", value: false }, Gamma: { displayName: "Gamma", isDataset: false, isPublic: false, name: "Gamma", type: "RasterFunctionVariable" }, Histograms: { isDataset: false, isPublic: false, name: "Histograms", type: "RasterFunctionVariable" }, Max: { isDataset: false, isPublic: false, name: "Max", type: "RasterFunctionVariable", value: 255 }, MaxPercent: { isDataset: false, isPublic: false, name: "MaxPercent", type: "RasterFunctionVariable", value: 0.5 }, Min: { isDataset: false, isPublic: false, name: "Min", type: "RasterFunctionVariable", value: 0 }, MinPercent: { isDataset: false, isPublic: false, name: "MinPercent", type: "RasterFunctionVariable", value: 0.25 }, NumberOfStandardDeviations: { isDataset: false, isPublic: false, name: "NumberOfStandardDeviation", type: "RasterFunctionVariable", value: 2 }, Raster: { isDataset: true, isPublic: false, name: "Raster", type: "RasterFunctionVariable" }, SigmoidStrengthLevel: { isDataset: false, isPublic: false, name: "SigmoidStrengthLevel", type: "RasterFunctionVariable", value: 2 }, Statistics: { isDataset: false, isPublic: false, name: "Statistics", type: "RasterFunctionVariable" }, StretchType: { isDataset: false, isPublic: false, name: "StretchType", type: "RasterFunctionVariable", value: 0 }, type: "StretchFunctionArguments", UseGamma: { isDataset: false, isPublic: false, name: "UseGamma", type: "RasterFunctionVariable", value: false } }, description: "Enhances an image by adjusting the range of values displayed. This does not alter the underlying pixel values. If a pixel has a value outside of the specified range, it will appear as either the minimum or maximum value.", function: { description: "Enhances an image by adjusting the range of values displayed. This does not alter the underlying pixel values. If a pixel has a value outside of the specified range, it will appear as either the minimum or maximum value.", name: "Stretch", pixelType: "UNKNOWN", type: "StretchFunction" }, functionType: 0, name: "Stretch", thumbnail: "" }, RemapFunction: { name: "Remap", description: "Changes pixel values by assigning new values to ranges of pixel values or using an external table.", function: { type: "RemapFunction", pixelType: "UNKNOWN", name: "Remap", description: "Changes pixel values by assigning new values to ranges of pixel values or using an external table." }, arguments: { Raster: { name: "Raster", isPublic: false, isDataset: true, type: "RasterFunctionVariable" }, UseTable: { name: "UseTable", isPublic: false, isDataset: false, value: false, type: "RasterFunctionVariable" }, InputRanges: { name: "InputRanges", isPublic: false, isDataset: false, type: "RasterFunctionVariable", displayName: "Input Ranges" }, OutputValues: { name: "OutputValues", isPublic: false, isDataset: false, type: "RasterFunctionVariable", displayName: "Output Values" }, NoDataRanges: { name: "NoDataRanges", isPublic: false, isDataset: false, type: "RasterFunctionVariable", displayName: "NoData Ranges" }, Table: { name: "Table", isPublic: false, isDataset: false, type: "RasterFunctionVariable" }, InputField: { name: "InputField", isPublic: false, isDataset: false, type: "RasterFunctionVariable" }, OutputField: { name: "OutputField", isPublic: false, isDataset: false, type: "RasterFunctionVariable" }, InputMaxField: { name: "InputMaxField", isPublic: false, isDataset: false, type: "RasterFunctionVariable" }, RemapTableType: { name: "RemapTableType", isPublic: false, isDataset: false, value: 1, type: "RasterFunctionVariable" }, AllowUnmatched: { name: "AllowUnmatched", isPublic: false, isDataset: false, value: true, type: "RasterFunctionVariable" }, type: "RemapFunctionArguments" }, functionType: 0, thumbnail: "" }, ColormapFunction: { name: "Colormap", description: "Changes pixel values to display the raster data as either a grayscale or a red, green, blue (RGB) image, based on a colormap or a color ramp.", function: { type: "ColormapFunction", pixelType: "UNKNOWN", name: "Colormap", description: "Changes pixel values to display the raster data as either a grayscale or a red, green, blue (RGB) image, based on a colormap or a color ramp." }, arguments: { Raster: { name: "Raster", isPublic: false, isDataset: true, type: "RasterFunctionVariable" }, ColorSchemeType: { name: "ColorSchemeType", isPublic: false, isDataset: false, value: 1, type: "RasterFunctionVariable" }, Colormap: { name: "Colormap", isPublic: false, isDataset: false, type: "RasterFunctionVariable" }, ColormapName: { name: "ColormapName", isPublic: false, isDataset: false, value: "Gray", type: "RasterFunctionVariable" }, ColorRamp: { name: "ColorRamp", isPublic: false, isDataset: false, type: "RasterFunctionVariable" }, type: "ColormapFunctionArguments" }, functionType: 0, thumbnail: "" }, ShadedReliefFunction: { name: "Shaded Relief", description: "Creates a multiband, color coded, 3D representation of the surface, with the sun's relative position taken into account for shading the image.", function: { type: "ShadedReliefFunction", pixelType: "UNKNOWN", name: "Shaded Relief", description: "Creates a multiband, color coded, 3D representation of the surface, with the sun's relative position taken into account for shading the image." }, arguments: { Raster: { name: "Raster", isPublic: false, isDataset: true, type: "RasterFunctionVariable" }, ColorSchemeType: { name: "ColorSchemeType", isPublic: false, isDataset: false, value: 1, type: "RasterFunctionVariable" }, ColorRamp: { name: "ColorRamp", isPublic: false, isDataset: false, type: "RasterFunctionVariable" }, HillshadeType: { name: "HillshadeType", isPublic: false, isDataset: false, value: 0, type: "RasterFunctionVariable" }, Colormap: { name: "Colormap", isPublic: false, isDataset: false, type: "RasterFunctionVariable" }, Azimuth: { name: "Azimuth", isPublic: false, isDataset: false, value: 315, type: "RasterFunctionVariable" }, Altitude: { name: "Altitude", isPublic: false, isDataset: false, value: 45, type: "RasterFunctionVariable" }, SlopeType: { name: "SlopeType", isPublic: false, isDataset: false, value: 1, type: "RasterFunctionVariable" }, ZFactor: { name: "ZFactor", isPublic: false, isDataset: false, value: 1, type: "RasterFunctionVariable" }, PSPower: { name: "PSPower", isPublic: false, isDataset: false, value: 0.664, type: "RasterFunctionVariable" }, PSZFactor: { name: "PSZFactor", isPublic: false, isDataset: false, value: 0.024, type: "RasterFunctionVariable" }, RemoveEdgeEffect: { name: "RemoveEdgeEffect", isPublic: false, isDataset: false, value: false, type: "RasterFunctionVariable" }, type: "ShadedReliefFunctionArguments" }, functionType: 0, thumbnail: "" }, HillshadeFunction: { name: "Hillshade", description: "Creates a 3D representation of the surface, with the sun's relative position taken into account for shading the image", function: { type: "HillshadeFunction", pixelType: "UNKNOWN", name: "Hillshade", description: "Creates a 3D representation of the surface, with the sun's relative position taken into account for shading the image" }, arguments: { DEM: { name: "DEM", isPublic: false, isDataset: true, type: "RasterFunctionVariable" }, HillshadeType: { name: "HillshadeType", isPublic: false, isDataset: false, value: 0, type: "RasterFunctionVariable" }, Azimuth: { name: "Azimuth", isPublic: false, isDataset: false, value: 315, type: "RasterFunctionVariable" }, Altitude: { name: "Altitude", isPublic: false, isDataset: false, value: 45, type: "RasterFunctionVariable" }, SlopeType: { name: "SlopeType", isPublic: false, isDataset: false, value: 1, type: "RasterFunctionVariable" }, ZFactor: { name: "ZFactor", isPublic: false, isDataset: false, value: 1, type: "RasterFunctionVariable" }, PSPower: { name: "PSPower", isPublic: false, isDataset: false, value: 0.664, type: "RasterFunctionVariable" }, PSZFactor: { name: "PSZFactor", isPublic: false, isDataset: false, value: 0.024, type: "RasterFunctionVariable" }, RemoveEdgeEffect: { name: "RemoveEdgeEffect", isPublic: false, isDataset: false, value: false, type: "RasterFunctionVariable" }, type: "HillshadeFunctionArguments" }, functionType: 0, thumbnail: "" }, ResampleFunction: { name: "Resample", description: "Changes the cell size of a raster.", function: { type: "ResampleFunction", pixelType: "UNKNOWN", name: "Resample", description: "Changes the cell size of a raster." }, arguments: { Raster: { name: "Raster", isPublic: false, isDataset: true, type: "RasterFunctionVariable" }, ResamplingType: { name: "ResamplingType", isPublic: false, isDataset: false, value: 0, type: "RasterFunctionVariable" }, InputCellSize: { name: "InputCellsize", isPublic: false, isDataset: false, value: { x: 0, y: 0 }, type: "RasterFunctionVariable" }, OutputCellSize: { name: "OutputCellsize", isPublic: false, isDataset: false, value: { x: 0, y: 0 }, type: "RasterFunctionVariable" }, type: "ResampleFunctionArguments" }, functionType: 0, thumbnail: "" } };

// node_modules/@arcgis/core/layers/support/imageryRendererUtils.js
var m3 = /* @__PURE__ */ new Set(["u1", "u2", "u4", "u8", "s8", "u16", "s16"]);
var p7 = { simple_scalar: "Simple Scalar", wind_barb: "Wind Barb", single_arrow: "Single Arrow", beaufort_kn: "Beaufort Wind (Knots)", beaufort_m: "Beaufort Wind (MetersPerSecond)", ocean_current_m: "Ocean Current (MetersPerSecond)", ocean_current_kn: "Ocean Current (Knots)" };
var f7 = /* @__PURE__ */ new Set(["raster-stretch", "unique-value", "class-breaks", "raster-shaded-relief", "vector-field", "raster-colormap"]);
function g2(e6) {
  return f7.has(e6.type);
}
function d2(n10, t4) {
  var _a;
  if (!n10 || !t4) return p(n10 || t4);
  const r4 = p(n10);
  if (r4.rasterFunctionDefinition && t4.rasterFunctionDefinition) {
    const e6 = t4.rasterFunctionDefinition;
    (e6.thumbnail || e6.thumbnailEx) && (e6.thumbnail = e6.thumbnailEx = void 0), h2(r4.rasterFunctionDefinition.arguments, t4);
  } else if ("none" !== ((_a = t4.functionName) == null ? void 0 : _a.toLowerCase())) {
    R(r4.functionArguments).Raster = t4;
  }
  return r4;
}
function h2(e6, n10) {
  for (const t4 in e6) "raster" === t4.toLowerCase() && ("RasterFunctionVariable" === e6[t4].type ? (e6[t4] = n10.rasterFunctionDefinition, e6[t4].type = "RasterFunctionTemplate") : "RasterFunctionTemplate" === e6[t4].type && h2(e6[t4].arguments, n10));
}
function y4(n10) {
  const t4 = p(e5[n10.functionName + "Function"]), o5 = n10.functionArguments;
  for (const e6 in o5) "raster" === e6.toLowerCase() ? (t4.arguments[e6] = y4(o5[e6]), t4.arguments[e6].type = "RasterFunctionTemplate") : "colormap" === e6.toLowerCase() ? (t4.arguments[e6].value = D(o5[e6]), t4.arguments.ColorSchemeType.value = 0) : t4.arguments[e6].value = o5[e6];
  return t4;
}
function b3(e6, n10) {
  switch (n10 = n10 || {}, e6.type) {
    case "raster-stretch":
      return x3(e6, n10);
    case "class-breaks":
      return S2(e6, n10);
    case "unique-value":
      return A(e6, n10);
    case "raster-colormap":
      return N(e6, n10);
    case "vector-field":
      return w5(e6, n10);
    case "raster-shaded-relief":
      return v7(e6, n10);
    case "flow":
      throw new Error("Unsupported rendering rule.");
  }
}
function R(e6) {
  const n10 = e6 == null ? void 0 : e6.Raster;
  return n10 && "esri.layers.support.RasterFunction" === n10.declaredClass ? R(n10.functionArguments) : e6;
}
var T4 = { none: 0, standardDeviation: 3, histogramEqualization: 4, minMax: 5, percentClip: 6, sigmoid: 9 };
function w5(e6, n10) {
  const r4 = new w4();
  r4.functionName = "VectorFieldRenderer";
  const { dataType: o5, bandProperties: a9 } = n10, i6 = "vector-uv" === o5;
  let s9, u9;
  a9 && 2 === a9.length && (s9 = a9.map((e7) => e7.BandName.toLowerCase()).indexOf("magnitude"), u9 = a9.map((e7) => e7.BandName.toLowerCase()).indexOf("direction")), -1 !== s9 && null !== s9 || (s9 = 0, u9 = 1);
  const l9 = "arithmetic" === e6.rotationType ? 1 : 2, m7 = "flow-from" === e6.flowRepresentation ? 0 : 1, f9 = e6.visualVariables ? e6.visualVariables.find((e7) => "Magnitude" === e7.field) : new b2(), g3 = { magnitudeBandID: s9, directionBandID: u9, isUVComponents: i6, referenceSystem: l9, massFlowAngleRepresentation: m7, symbolTileSize: 50, symbolTileSizeUnits: 100, calculationMethod: "Vector Average", symbologyName: p7[e6.style.toLowerCase().replace("-", "_")], minimumMagnitude: f9.minDataValue, maximumMagnitude: f9.maxDataValue, minimumSymbolSize: f9.minSize, maximumSymbolSize: f9.maxSize };
  return r4.functionArguments = g3, n10.convertToRFT ? new w4({ rasterFunctionDefinition: y4(r4) }) : r4;
}
function v7(e6, n10) {
  const r4 = n10.convertToRFT;
  if ("elevation" !== n10.dataType && ("generic" !== n10.dataType || 1 !== n10.bandCount || "s16" !== n10.pixelType && "s32" !== n10.pixelType && "f32" !== n10.pixelType && "f64" !== n10.pixelType)) return new w4();
  const o5 = new w4();
  o5.functionName = "Hillshade";
  const s9 = "traditional" === e6.hillshadeType ? 0 : 1, u9 = "none" === e6.scalingType ? 1 : 3, l9 = { HillshadeType: s9, SlopeType: u9, ZFactor: e6.zFactor };
  return 0 === s9 && (l9.Azimuth = e6.azimuth, l9.Altitude = e6.altitude), 3 === u9 && (l9.PSPower = e6.pixelSizePower, l9.PSZFactor = e6.pixelSizeFactor), o5.functionArguments = l9, o5.variableName = "Raster", e6.colorRamp && (o5.functionName = "ShadedRelief", r4 ? l9.ColorRamp = v5(e6.colorRamp) : l9.Colormap = _2(e6.colorRamp)), r4 ? new w4({ rasterFunctionDefinition: y4(o5) }) : o5;
}
function x3(e6, n10) {
  var _a;
  const r4 = n10.convertToRFT, o5 = new w4();
  o5.functionName = "Stretch";
  const u9 = T4[n4.toJSON(e6.stretchType)], c16 = "u8", m7 = { StretchType: u9, Statistics: V(e6.statistics ?? []), DRA: e6.dynamicRangeAdjustment, UseGamma: e6.useGamma, Gamma: e6.gamma, ComputeGamma: e6.computeGamma };
  if (null != e6.outputMin && (m7.Min = e6.outputMin), null != e6.outputMax && (m7.Max = e6.outputMax), u9 === T4.standardDeviation ? (m7.NumberOfStandardDeviations = e6.numberOfStandardDeviations, o5.outputPixelType = c16) : u9 === T4.percentClip ? (m7.MinPercent = e6.minPercent, m7.MaxPercent = e6.maxPercent, o5.outputPixelType = c16) : u9 === T4.minMax ? o5.outputPixelType = c16 : u9 === T4.sigmoid && (m7.SigmoidStrengthLevel = e6.sigmoidStrengthLevel), o5.functionArguments = m7, o5.variableName = "Raster", e6.colorRamp) {
    const u10 = e6.colorRamp, l9 = new w4();
    if (r4) l9.functionArguments = { ColorRamp: v5(u10) };
    else {
      const t4 = C(u10);
      if (t4) l9.functionArguments = { colorRamp: t4 };
      else if (!n10.convertColorRampToColormap || "algorithmic" !== u10.type && "multipart" !== u10.type) {
        const n11 = e6.colorRamp.toJSON();
        "algorithmic" === n11.type ? n11.algorithm = n11.algorithm || "esriCIELabAlgorithm" : "multipart" === n11.type && ((_a = n11.colorRamps) == null ? void 0 : _a.length) && n11.colorRamps.forEach((e7) => e7.algorithm = e7.algorithm || "esriCIELabAlgorithm"), l9.functionArguments = { colorRamp: n11 };
      } else l9.functionArguments = { Colormap: _2(u10) };
    }
    return l9.variableName = "Raster", l9.functionName = "Colormap", l9.functionArguments.Raster = o5, r4 ? new w4({ rasterFunctionDefinition: y4(l9) }) : l9;
  }
  return r4 ? new w4({ rasterFunctionDefinition: y4(o5) }) : o5;
}
function S2(e6, r4) {
  const o5 = [], a9 = [], i6 = [], s9 = [], u9 = 1e-6, { pixelType: l9, rasterAttributeTable: c16 } = r4, m7 = t(c16) ? null : c16.features, p16 = F(c16);
  if (p16 && m7 && Array.isArray(m7) && e6.classBreakInfos) {
    e6.classBreakInfos.forEach((n11, t4) => {
      var _a;
      const r5 = (_a = n11.symbol) == null ? void 0 : _a.color;
      let o7;
      (r5 == null ? void 0 : r5.a) && null != n11.minValue && null != n11.maxValue && m7.forEach((a10) => {
        null != n11.minValue && null != n11.maxValue && (o7 = a10.attributes[e6.field], (o7 >= n11.minValue && o7 < n11.maxValue || t4 === e6.classBreakInfos.length - 1 && o7 >= n11.minValue) && s9.push([a10.attributes[p16], r5.r, r5.g, r5.b]));
      });
    });
    const n10 = l9 ? C2(s9, l9) : s9, o6 = new w4();
    return o6.functionName = "Colormap", o6.functionArguments = {}, o6.functionArguments.Colormap = n10, o6.variableName = "Raster", r4.convertToRFT ? new w4({ rasterFunctionDefinition: y4(o6) }) : o6;
  }
  e6.classBreakInfos.forEach((e7, n10) => {
    if (null == e7.minValue || null == e7.maxValue) return;
    const t4 = e7.symbol && e7.symbol.color;
    (t4 == null ? void 0 : t4.a) ? (0 === n10 ? o5.push(e7.minValue, e7.maxValue + u9) : o5.push(e7.minValue + u9, e7.maxValue + u9), a9.push(n10), s9.push([n10, t4.r, t4.g, t4.b])) : i6.push(e7.minValue, e7.maxValue);
  });
  const f9 = l9 ? C2(s9, l9) : s9, g3 = new w4();
  g3.functionName = "Remap", g3.functionArguments = { InputRanges: o5, OutputValues: a9, NoDataRanges: i6 }, g3.variableName = "Raster";
  const d8 = new w4();
  return d8.functionName = "Colormap", d8.functionArguments = { Colormap: f9, Raster: g3 }, r4.convertToRFT ? new w4({ rasterFunctionDefinition: y4(d8) }) : d8;
}
function C2(e6, n10) {
  const t4 = m3.has(n10) ? s8(n10) : null;
  return t4 && e6.push([Math.floor(t4[0] - 1), 0, 0, 0], [Math.ceil(t4[1] + 1), 0, 0, 0]), e6;
}
function F(e6) {
  if (t(e6)) return;
  const { fields: t4 } = e6, r4 = t4 && t4.find((e7) => e7 && e7.name && "value" === e7.name.toLowerCase());
  return r4 && r4.name;
}
function A(e6, r4) {
  var _a, _b, _c;
  const o5 = [], { pixelType: a9, rasterAttributeTable: i6 } = r4, s9 = t(i6) ? null : i6.features, u9 = F(i6), l9 = (_b = (_a = e6.defaultSymbol) == null ? void 0 : _a.color) == null ? void 0 : _b.toRgb(), c16 = e6.uniqueValueInfos;
  if (c16) if (s9) {
    if (u9) {
      const n10 = /* @__PURE__ */ new Map();
      c16.forEach((e7) => {
        var _a2;
        const t5 = e7.value, r5 = (_a2 = e7.symbol) == null ? void 0 : _a2.color;
        null != t5 && r5 && r5.a && n10.set(String(t5), r5.toRgb());
      });
      const t4 = e6.field;
      s9.forEach(({ attributes: e7 }) => {
        const r5 = String(e7[t4]), a10 = e7[u9], i7 = n10.get(r5);
        i7 ? o5.push([a10, ...i7]) : l9 && o5.push([a10, ...l9]);
      });
    }
  } else for (let n10 = 0; n10 < c16.length; n10++) {
    const e7 = c16[n10], t4 = (_c = e7.symbol) == null ? void 0 : _c.color, r5 = +e7.value;
    if (t4 == null ? void 0 : t4.a) {
      if (isNaN(r5)) return null;
      o5.push([r5, t4.r, t4.g, t4.b]);
    }
  }
  const m7 = a9 && o5.length > 0 ? C2(o5, a9) : o5, p16 = new w4();
  return p16.functionName = "Colormap", p16.functionArguments = {}, p16.functionArguments.Colormap = m7, p16.variableName = "Raster", r4.convertToRFT ? new w4({ rasterFunctionDefinition: y4(p16) }) : p16;
}
function N(e6, n10) {
  const r4 = e6.extractColormap();
  if (!r4 || 0 === r4.length) return null;
  const { pixelType: o5 } = n10, a9 = o5 ? C2(r4, o5) : r4, i6 = new w4();
  return i6.functionName = "Colormap", i6.functionArguments = {}, i6.functionArguments.Colormap = a9, n10.convertToRFT ? new w4({ rasterFunctionDefinition: y4(i6) }) : i6;
}
function V(e6) {
  const n10 = [];
  return e6 == null ? void 0 : e6.forEach((e7) => {
    const t4 = e7;
    if (Array.isArray(t4)) n10.push(t4);
    else {
      if (null == t4.min || null == t4.max) return;
      const e8 = [t4.min, t4.max, t4.avg || 0, t4.stddev || 0];
      n10.push(e8);
    }
  }), n10;
}
function D(e6) {
  const n10 = [], t4 = [];
  return e6.forEach((e7) => {
    n10.push(e7[0]), t4.push(y3([...e7.slice(1), 255]));
  }), { type: "RasterColormap", values: n10, colors: t4 };
}

// node_modules/@arcgis/core/layers/support/MosaicRule.js
var m4;
var h3 = o2()({ MT_FIRST: "first", MT_LAST: "last", MT_MIN: "min", MT_MAX: "max", MT_MEAN: "mean", MT_BLEND: "blend", MT_SUM: "sum" });
var w6 = o2()({ esriMosaicNone: "none", esriMosaicCenter: "center", esriMosaicNadir: "nadir", esriMosaicViewpoint: "viewpoint", esriMosaicAttribute: "attribute", esriMosaicLockRaster: "lock-raster", esriMosaicNorthwest: "northwest", esriMosaicSeamline: "seamline" });
function M(e6) {
  let t4;
  switch (e6 ? e6.toLowerCase().replace("esrimosaic", "") : "") {
    case "byattribute":
    case "attribute":
      t4 = "esriMosaicAttribute";
      break;
    case "lockraster":
      t4 = "esriMosaicLockRaster";
      break;
    case "center":
      t4 = "esriMosaicCenter";
      break;
    case "northwest":
      t4 = "esriMosaicNorthwest";
      break;
    case "nadir":
      t4 = "esriMosaicNadir";
      break;
    case "viewpoint":
      t4 = "esriMosaicViewpoint";
      break;
    case "seamline":
      t4 = "esriMosaicSeamline";
      break;
    default:
      t4 = "esriMosaicNone";
  }
  return w6.fromJSON(t4);
}
var y5 = m4 = class extends l {
  constructor(e6) {
    super(e6), this.ascending = true, this.itemRenderingRule = null, this.lockRasterIds = null, this.method = null, this.multidimensionalDefinition = null, this.objectIds = null, this.operation = null, this.sortField = null, this.sortValue = null, this.viewpoint = null, this.where = null;
  }
  readAscending(e6, t4) {
    return null != t4.ascending ? t4.ascending : null == t4.sortAscending || t4.sortAscending;
  }
  readMethod(e6, t4) {
    return M(t4.mosaicMethod || t4.defaultMosaicMethod);
  }
  writeMultidimensionalDefinition(e6, t4, o5) {
    null != e6 && (e6 = e6.filter(({ variableName: e7, dimensionName: t5 }) => e7 && "*" !== e7 || t5)).length && (t4[o5] = e6.map((e7) => e7.toJSON()));
  }
  readOperation(e6, t4) {
    const o5 = t4.mosaicOperation, r4 = t4.mosaicOperator && t4.mosaicOperator.toLowerCase(), i6 = o5 || (r4 ? h3.toJSON(r4) : null);
    return h3.fromJSON(i6) || "first";
  }
  castSortValue(e6) {
    return null == e6 || "string" == typeof e6 || "number" == typeof e6 ? e6 : `${e6}`;
  }
  clone() {
    return new m4({ ascending: this.ascending, itemRenderingRule: p(this.itemRenderingRule), lockRasterIds: p(this.lockRasterIds), method: this.method, multidimensionalDefinition: p(this.multidimensionalDefinition), objectIds: p(this.objectIds), operation: this.operation, sortField: this.sortField, sortValue: this.sortValue, viewpoint: p(this.viewpoint), where: this.where });
  }
};
e([y({ type: Boolean, json: { write: true } })], y5.prototype, "ascending", void 0), e([o("ascending", ["ascending", "sortAscending"])], y5.prototype, "readAscending", null), e([y({ type: w4, json: { write: true } })], y5.prototype, "itemRenderingRule", void 0), e([y({ type: [T], json: { write: { overridePolicy() {
  return { enabled: "lock-raster" === this.method };
} } } })], y5.prototype, "lockRasterIds", void 0), e([y({ type: String, json: { type: w6.jsonValues, write: { target: "mosaicMethod", writer: w6.write } } })], y5.prototype, "method", void 0), e([o("method", ["mosaicMethod", "defaultMosaicMethod"])], y5.prototype, "readMethod", null), e([y({ type: [p6], json: { write: true } })], y5.prototype, "multidimensionalDefinition", void 0), e([r2("multidimensionalDefinition")], y5.prototype, "writeMultidimensionalDefinition", null), e([y({ type: [T], json: { name: "fids", write: true } })], y5.prototype, "objectIds", void 0), e([y({ json: { type: h3.jsonValues, read: { reader: h3.read }, write: { target: "mosaicOperation", writer: h3.write } } })], y5.prototype, "operation", void 0), e([o("operation", ["mosaicOperation", "mosaicOperator"])], y5.prototype, "readOperation", null), e([y({ type: String, json: { write: { overridePolicy() {
  return { enabled: "attribute" === this.method };
} } } })], y5.prototype, "sortField", void 0), e([y({ type: [String, Number], json: { write: { allowNull: true, overridePolicy() {
  return { enabled: "attribute" === this.method, allowNull: true };
} } } })], y5.prototype, "sortValue", void 0), e([s4("sortValue")], y5.prototype, "castSortValue", null), e([y({ type: w2, json: { write: true } })], y5.prototype, "viewpoint", void 0), e([y({ type: String, json: { write: true } })], y5.prototype, "where", void 0), y5 = m4 = e([a2("esri.layers.support.MosaicRule")], y5);
var f8 = y5;

// node_modules/@arcgis/core/layers/support/ExportImageServiceParameters.js
var y6 = class extends l {
  constructor() {
    super(...arguments), this.layer = null, this.compression = void 0, this.pixelType = void 0, this.lercVersion = 2;
  }
  get adjustAspectRatio() {
    return this.layer.adjustAspectRatio;
  }
  writeAdjustAspectRatio(e6, r4, t4) {
    this.layer.version < 10.3 || (r4[t4] = e6);
  }
  get bandIds() {
    return this.layer.bandIds;
  }
  get compressionQuality() {
    return this.layer.compressionQuality;
  }
  writeCompressionQuality(e6, r4, t4) {
    this.format && this.format.toLowerCase().includes("jpg") && null != e6 && (r4[t4] = e6);
  }
  get compressionTolerance() {
    return this.layer.compressionTolerance;
  }
  writeCompressionTolerance(e6, r4, t4) {
    "lerc" === this.format && null != e6 && (r4[t4] = e6);
  }
  get format() {
    var _a;
    return "vector-field" === ((_a = this.layer.renderer) == null ? void 0 : _a.type) ? "lerc" : this.layer.format;
  }
  get interpolation() {
    return this.layer.interpolation;
  }
  get noData() {
    return this.layer.noData;
  }
  get noDataInterpretation() {
    return this.layer.noDataInterpretation;
  }
  writeLercVersion(e6, r4, t4) {
    "lerc" === this.format && this.layer.version >= 10.5 && (r4[t4] = e6);
  }
  get version() {
    const e6 = this.layer;
    return e6.commitProperty("bandIds"), e6.commitProperty("format"), e6.commitProperty("compressionQuality"), e6.commitProperty("compressionTolerance"), e6.commitProperty("interpolation"), e6.commitProperty("noData"), e6.commitProperty("noDataInterpretation"), e6.commitProperty("mosaicRule"), e6.commitProperty("renderingRule"), e6.commitProperty("adjustAspectRatio"), e6.commitProperty("pixelFilter"), e6.commitProperty("definitionExpression"), e6.commitProperty("multidimensionalSubset"), (this._get("version") || 0) + 1;
  }
  set version(e6) {
    this._set("version", e6);
  }
  get mosaicRule() {
    const e6 = this.layer;
    let r4 = e6.mosaicRule;
    const t4 = e6.definitionExpression;
    return r4 ? t4 && t4 !== r4.where && (r4 = r4.clone(), r4.where = t4) : t4 && (r4 = new f8({ where: t4 })), r4;
  }
  get renderingRule() {
    var _a, _b;
    const e6 = this.layer;
    let r4 = e6.renderingRule;
    const t4 = e6.pixelFilter, o5 = !e6.format || e6.format.includes("jpg") || e6.format.includes("png");
    r4 = this._addResampleRasterFunction(r4);
    const i6 = (_a = e6.multidimensionalSubset) == null ? void 0 : _a.areaOfInterest;
    return i6 && (r4 = this._addClipFunction(r4, i6)), o5 && !t4 && "vector-field" !== ((_b = e6.renderer) == null ? void 0 : _b.type) && (r4 = this.combineRendererWithRenderingRule(r4)), r4;
  }
  combineRendererWithRenderingRule(e6) {
    var _a;
    const r4 = this.layer, { rasterInfo: t4, renderer: o5 } = r4;
    if (e6 = e6 || r4.renderingRule, !o5 || !g2(o5)) return e6;
    return d2(b3(o5, { rasterAttributeTable: t4.attributeTable, pixelType: t4.pixelType, dataType: t4.dataType, bandProperties: (_a = t4.keyProperties) == null ? void 0 : _a.BandProperties, convertColorRampToColormap: r4.version < 10.6, convertToRFT: !!(e6 == null ? void 0 : e6.rasterFunctionDefinition), bandCount: t4.bandCount }), e6);
  }
  _addResampleRasterFunction(e6) {
    var _a;
    if (!("vector-field" === ((_a = this.layer.renderer) == null ? void 0 : _a.type)) || "Resample" === (e6 == null ? void 0 : e6.functionName)) return e6;
    const r4 = "esriImageServiceDataTypeVector-UV" === this.layer.serviceDataType ? 7 : 10, t4 = this.layer.serviceRasterInfo.pixelSize;
    let o5 = new w4({ functionName: "Resample", functionArguments: { ResamplingType: r4, InputCellSize: t4 } });
    return o5 = (e6 == null ? void 0 : e6.rasterFunctionDefinition) ? new w4({ rasterFunctionDefinition: y4(o5) }) : o5, d2(o5, e6);
  }
  _addClipFunction(e6, r4) {
    const t4 = new w4({ functionName: "Clip", functionArguments: { ClippingGeometry: r4.toJSON(), ClippingType: 1 } });
    return d2(t4, e6);
  }
};
e([y()], y6.prototype, "layer", void 0), e([y({ json: { write: true } })], y6.prototype, "adjustAspectRatio", null), e([r2("adjustAspectRatio")], y6.prototype, "writeAdjustAspectRatio", null), e([y({ json: { write: true } })], y6.prototype, "bandIds", null), e([y({ json: { write: true } })], y6.prototype, "compression", void 0), e([y({ json: { write: true } })], y6.prototype, "compressionQuality", null), e([r2("compressionQuality")], y6.prototype, "writeCompressionQuality", null), e([y({ json: { write: true } })], y6.prototype, "compressionTolerance", null), e([r2("compressionTolerance")], y6.prototype, "writeCompressionTolerance", null), e([y({ json: { write: true } })], y6.prototype, "format", null), e([y({ type: String, json: { read: { reader: o4.read }, write: { writer: o4.write } } })], y6.prototype, "interpolation", null), e([y({ json: { write: true } })], y6.prototype, "noData", null), e([y({ type: String, json: { read: { reader: i2.read }, write: { writer: i2.write } } })], y6.prototype, "noDataInterpretation", null), e([y({ json: { write: true } })], y6.prototype, "pixelType", void 0), e([y({ json: { write: true } })], y6.prototype, "lercVersion", void 0), e([r2("lercVersion")], y6.prototype, "writeLercVersion", null), e([y({ type: Number })], y6.prototype, "version", null), e([y({ json: { write: true } })], y6.prototype, "mosaicRule", null), e([y({ json: { write: true } })], y6.prototype, "renderingRule", null), y6 = e([a2("esri.layers.mixins.ExportImageServiceParameters")], y6);

// node_modules/@arcgis/core/rest/support/ImageAngleResult.js
var p8 = class extends l {
  constructor(r4) {
    super(r4), this.north = null, this.up = null, this.spatialReference = null;
  }
};
e([y({ type: Number, json: { write: true } })], p8.prototype, "north", void 0), e([y({ type: Number, json: { write: true } })], p8.prototype, "up", void 0), e([y({ type: f, json: { write: true } })], p8.prototype, "spatialReference", void 0), p8 = e([a2("esri.rest.support.ImageAngleResult")], p8);
var i3 = p8;

// node_modules/@arcgis/core/rest/support/BaseImageMeasureResult.js
var u4 = class extends l {
  constructor() {
    super(...arguments), this.value = null, this.displayValue = null, this.uncertainty = null;
  }
};
e([y({ type: Number, json: { read: true, write: true } })], u4.prototype, "value", void 0), e([y({ type: String, json: { read: true, write: true } })], u4.prototype, "displayValue", void 0), e([y({ type: Number, json: { read: true, write: true } })], u4.prototype, "uncertainty", void 0), u4 = e([a2("esri.rest.support.ImageMeasureResultValue")], u4);
var a4 = class extends u4 {
  constructor() {
    super(...arguments), this.unit = null;
  }
};
e([y({ type: String, json: { read: me.read, write: me.write } })], a4.prototype, "unit", void 0), a4 = e([a2("esri.rest.support.ImageMeasureResultLengthValue")], a4);
var n7 = class extends u4 {
  constructor() {
    super(...arguments), this.unit = null;
  }
};
e([y({ type: String, json: { read: le.read, write: le.write } })], n7.prototype, "unit", void 0), n7 = e([a2("esri.rest.support.ImageMeasureResultAreaValue")], n7);
var l4 = class extends u4 {
  constructor() {
    super(...arguments), this.unit = null;
  }
};
e([y({ type: String, json: { read: fe.read, write: fe.write } })], l4.prototype, "unit", void 0), l4 = e([a2("esri.rest.support.ImageMeasureResultAngleValue")], l4);
var c7 = class extends l {
  constructor() {
    super(...arguments), this.name = null, this.sensorName = null;
  }
};
e([y({ type: String, json: { read: true, write: true } })], c7.prototype, "name", void 0), e([y({ type: String, json: { read: true, write: true } })], c7.prototype, "sensorName", void 0), c7 = e([a2("esri.rest.support.BaseImageMeasureResult")], c7);

// node_modules/@arcgis/core/rest/support/ImageAreaResult.js
var a5 = class extends c7 {
  constructor() {
    super(...arguments), this.area = null, this.perimeter = null;
  }
};
e([y({ type: n7, json: { read: true, write: true } })], a5.prototype, "area", void 0), e([y({ type: a4, json: { read: true, write: true } })], a5.prototype, "perimeter", void 0), a5 = e([a2("esri.rest.support.ImageAreaResult")], a5);
var c8 = a5;

// node_modules/@arcgis/core/rest/support/ImageDistanceResult.js
var a6 = class extends c7 {
  constructor() {
    super(...arguments), this.distance = null, this.azimuthAngle = null, this.elevationAngle = null;
  }
};
e([y({ type: a4, json: { read: true, write: true } })], a6.prototype, "distance", void 0), e([y({ type: l4, json: { read: true, write: true } })], a6.prototype, "azimuthAngle", void 0), e([y({ type: l4, json: { read: true, write: true } })], a6.prototype, "elevationAngle", void 0), a6 = e([a2("esri.rest.support.ImageDistanceResult")], a6);
var i4 = a6;

// node_modules/@arcgis/core/rest/support/ImageHeightResult.js
var p9 = class extends c7 {
  constructor() {
    super(...arguments), this.height = null;
  }
};
e([y({ type: a4, json: { read: true, write: true } })], p9.prototype, "height", void 0), p9 = e([a2("esri.rest.support.ImageHeightResult")], p9);
var c9 = p9;

// node_modules/@arcgis/core/rest/support/ImageIdentifyResult.js
var p10 = class extends l {
  constructor() {
    super(...arguments), this.catalogItemVisibilities = null, this.catalogItems = null, this.location = null, this.name = null, this.objectId = null, this.processedValues = null, this.properties = null, this.value = null;
  }
};
e([y({ json: { write: true } })], p10.prototype, "catalogItemVisibilities", void 0), e([y({ type: x2, json: { write: true } })], p10.prototype, "catalogItems", void 0), e([y({ type: w2, json: { write: true } })], p10.prototype, "location", void 0), e([y({ json: { write: true } })], p10.prototype, "name", void 0), e([y({ json: { write: true } })], p10.prototype, "objectId", void 0), e([y({ json: { write: true } })], p10.prototype, "processedValues", void 0), e([y({ json: { write: true } })], p10.prototype, "properties", void 0), e([y({ json: { write: true } })], p10.prototype, "value", void 0), p10 = e([a2("esri.rest.support.ImageIdentifyResult")], p10);
var l5 = p10;

// node_modules/@arcgis/core/rest/support/ImagePixelLocationResult.js
var t3 = class extends l {
  constructor() {
    super(...arguments), this.geometries = null;
  }
};
e([y({ json: { write: true } })], t3.prototype, "geometries", void 0), t3 = e([a2("esri.rest.support.ImagePixelLocationResult")], t3);
var p11 = t3;

// node_modules/@arcgis/core/rest/support/ImagePointResult.js
var p12 = class extends c7 {
  constructor() {
    super(...arguments), this.point = null;
  }
};
e([y({ type: w2, json: { name: "point.value", read: true, write: true } })], p12.prototype, "point", void 0), p12 = e([a2("esri.rest.support.ImagePointResult")], p12);
var a7 = p12;

// node_modules/@arcgis/core/rest/support/ImageSample.js
var i5 = class extends l {
  constructor() {
    super(...arguments), this.attributes = null, this.location = null, this.locationId = null, this.rasterId = null, this.resolution = null, this.pixelValue = null;
  }
};
e([y({ json: { write: true } })], i5.prototype, "attributes", void 0), e([y({ type: w2, json: { write: true } })], i5.prototype, "location", void 0), e([y({ json: { write: true } })], i5.prototype, "locationId", void 0), e([y({ json: { write: true } })], i5.prototype, "rasterId", void 0), e([y({ json: { write: true } })], i5.prototype, "resolution", void 0), e([y({ json: { write: true } })], i5.prototype, "pixelValue", void 0), i5 = e([a2("esri.rest.support.ImageSample")], i5);
var p13 = i5;

// node_modules/@arcgis/core/rest/support/ImageSampleResult.js
var p14 = class extends l {
  constructor() {
    super(...arguments), this.samples = null;
  }
};
e([y({ type: [p13], json: { write: true } })], p14.prototype, "samples", void 0), p14 = e([a2("esri.rest.support.ImageSampleResult")], p14);
var c10 = p14;

// node_modules/@arcgis/core/rest/imageService.js
function y7(t4) {
  const e6 = t4 == null ? void 0 : t4.time;
  if (e6 && (null != e6.start || null != e6.end)) {
    const o5 = [];
    null != e6.start && o5.push(e6.start), null == e6.end || o5.includes(e6.end) || o5.push(e6.end), t4.time = o5.join(",");
  }
}
async function S3(t4, s9, i6) {
  const m7 = f2(t4), c16 = s9.geometry ? [s9.geometry] : [], u9 = await v4(c16), l9 = s9.toJSON();
  y7(l9);
  const f9 = u9 && u9[0];
  r(f9) && (l9.geometry = f9.toJSON());
  const p16 = s5({ ...m7.query, f: "json", ...l9 });
  return i(p16, i6);
}
async function d3(o5, i6, m7) {
  var _a;
  const c16 = i6.toJSON();
  r(c16.angleName) && (c16.angleName = c16.angleName.join(",")), r(i6.point) && ((_a = i6.point.spatialReference) == null ? void 0 : _a.imageCoordinateSystem) && (c16.point.spatialReference = G(i6.point.spatialReference)), r(i6.spatialReference) && i6.spatialReference.imageCoordinateSystem && (c16.spatialReference = $2(i6.spatialReference));
  const u9 = f2(o5), l9 = s5({ ...u9.query, f: "json", ...c16 }), f9 = i(l9, m7), { data: p16 } = await U(`${u9.path}/computeAngles`, f9);
  return p16.spatialReference = p16.spatialReference ? null != p16.spatialReference.geodataXform ? new f({ wkid: 0, imageCoordinateSystem: p16.spatialReference }) : f.fromJSON(p16.spatialReference) : null, "NaN" === p16.north && (p16.north = null), "NaN" === p16.up && (p16.up = null), new i3(p16);
}
async function N2(e6, o5, s9) {
  var _a;
  const i6 = o5.toJSON(), { geometries: m7 } = o5;
  if (m7) for (let t4 = 0; t4 < m7.length; t4++) ((_a = m7[t4].spatialReference) == null ? void 0 : _a.imageCoordinateSystem) && (i6.geometries.geometries[t4].spatialReference = G(m7[t4].spatialReference));
  const c16 = f2(e6), u9 = s5({ ...c16.query, f: "json", ...i6 }), f9 = i(u9, s9), { data: p16 } = await U(`${c16.path}/computePixelLocation`, f9);
  return p11.fromJSON(p16);
}
async function R2(e6, o5, a9) {
  const r4 = await S3(e6, o5, a9), s9 = f2(e6), { data: i6 } = await U(`${s9.path}/computeStatisticsHistograms`, r4), { statistics: m7 } = i6;
  return (m7 == null ? void 0 : m7.length) && m7.forEach((t4) => {
    t4.avg = t4.mean, t4.stddev = t4.standardDeviation;
  }), { statistics: m7, histograms: i6.histograms };
}
async function J(e6, o5, a9) {
  const r4 = await S3(e6, o5, a9), s9 = f2(e6), { data: i6 } = await U(`${s9.path}/computeHistograms`, r4);
  return { histograms: i6.histograms };
}
async function O2(s9, i6, m7) {
  var _a, _b, _c;
  const c16 = i6.toJSON();
  y7(c16), ((_a = c16.outFields) == null ? void 0 : _a.length) && (c16.outFields = c16.outFields.join(","));
  const u9 = (_b = await v4(i6.geometry)) == null ? void 0 : _b[0];
  r(u9) && (c16.geometry = u9.toJSON());
  const l9 = f2(s9), f9 = s5({ ...l9.query, f: "json", ...c16 }), g3 = i(f9, m7), { data: S4 } = await U(`${l9.path}/getSamples`, g3), d8 = (_c = S4 == null ? void 0 : S4.samples) == null ? void 0 : _c.map((t4) => {
    const e6 = "NaN" === t4.value || "" === t4.value ? null : t4.value.split(" ").map((t5) => Number(t5));
    return { ...t4, pixelValue: e6 };
  });
  return c10.fromJSON({ samples: d8 });
}
async function j4(s9, i6, m7) {
  const c16 = f2(s9), l9 = i6.geometry ? [i6.geometry] : [];
  return v4(l9).then((o5) => {
    const n10 = i6.toJSON(), s10 = o5 && o5[0];
    r(s10) && (n10.geometry = JSON.stringify(s10.toJSON()));
    const u9 = s5({ ...c16.query, f: "json", ...n10 }), l10 = i(u9, m7);
    return U(c16.path + "/identify", l10);
  }).then((t4) => l5.fromJSON(t4.data));
}
async function h4(t4, e6, o5) {
  const n10 = await q(t4, e6, [e6.fromGeometry, e6.toGeometry], o5);
  return c9.fromJSON(n10);
}
async function w7(t4, e6, o5) {
  const n10 = await q(t4, e6, [e6.geometry], o5);
  return c8.fromJSON(n10);
}
async function I(t4, e6, o5) {
  const n10 = await q(t4, e6, [e6.geometry], o5);
  return a7.fromJSON(n10);
}
async function C3(t4, e6, o5) {
  const n10 = await q(t4, e6, [e6.fromGeometry, e6.toGeometry], o5);
  return i4.fromJSON(n10);
}
async function q(s9, i6, m7, c16) {
  const u9 = f2(s9), l9 = await v4(m7), f9 = i6.toJSON();
  r(l9[0]) && (f9.fromGeometry = JSON.stringify(v8(l9[0]))), r(l9[1]) && (f9.toGeometry = JSON.stringify(v8(l9[1])));
  const p16 = s5({ ...u9.query, f: "json", ...f9 }), g3 = i(p16, c16), { data: y13 } = await U(u9.path + "/measure", g3);
  return y13;
}
function v8(t4) {
  var _a;
  const e6 = t4.toJSON();
  return ((_a = t4.spatialReference) == null ? void 0 : _a.imageCoordinateSystem) && (e6.spatialReference = G(t4.spatialReference)), e6;
}
function G(t4) {
  const { imageCoordinateSystem: e6 } = t4;
  if (e6) {
    const { id: t5, referenceServiceName: o5 } = e6;
    return null != t5 ? o5 ? { icsid: t5, icsns: o5 } : { icsid: t5 } : { ics: e6 };
  }
  return t4.toJSON();
}
function $2(t4, e6) {
  const o5 = G(t4), { icsid: n10, icsns: a9, wkid: r4 } = o5;
  return null != n10 ? null == a9 || (e6 == null ? void 0 : e6.toLowerCase().includes("/" + a9.toLowerCase() + "/")) ? `0:${n10}` : JSON.stringify(o5) : r4 ? r4.toString() : JSON.stringify(o5);
}

// node_modules/@arcgis/core/rest/imageService/getCatalogItemRasterInfo.js
async function n8(n10, m7, p16) {
  var _a, _b;
  const f9 = f2(n10), u9 = s5({ ...f9 == null ? void 0 : f9.query, f: "json" }), h6 = i(u9, p16), d8 = `${f9 == null ? void 0 : f9.path}/${m7}/info`, c16 = U(`${d8}`, h6), g3 = U(`${d8}/keyProperties`, h6), x4 = await Promise.allSettled([c16, g3]), y13 = "fulfilled" === x4[0].status ? x4[0].value.data : null, v9 = "fulfilled" === x4[1].status ? x4[1].value.data : null;
  let P = null;
  ((_a = y13.statistics) == null ? void 0 : _a.length) && (P = y13.statistics.map((e6) => ({ min: e6[0], max: e6[1], avg: e6[2], stddev: e6[3] })));
  const S4 = w3.fromJSON(y13.extent), j7 = Math.ceil(S4.width / y13.pixelSizeX - 0.1), w8 = Math.ceil(S4.height / y13.pixelSizeY - 0.1), b4 = S4.spatialReference, k3 = new w2({ x: y13.pixelSizeX, y: y13.pixelSizeY, spatialReference: b4 }), z = ((_b = y13.histograms) == null ? void 0 : _b.length) ? y13.histograms : null, L4 = new n5({ origin: y13.origin, blockWidth: y13.blockWidth, blockHeight: y13.blockHeight, firstPyramidLevel: y13.firstPyramidLevel, maximumPyramidLevel: y13.maxPyramidLevel });
  return new u3({ width: j7, height: w8, bandCount: y13.bandCount, extent: S4, spatialReference: b4, pixelSize: k3, pixelType: y13.pixelType.toLowerCase(), statistics: P, histograms: z, keyProperties: v9, storageInfo: L4 });
}

// node_modules/@arcgis/core/rest/support/ImageAngleParameters.js
var n9;
var m5 = n9 = class extends l {
  constructor(e6) {
    super(e6), this.angleNames = null, this.point = null, this.spatialReference = null, this.rasterId = null;
  }
  clone() {
    return new n9(p({ angleNames: this.angleNames, point: this.point, spatialReference: this.spatialReference, rasterId: this.rasterId }));
  }
};
e([y({ type: [String], json: { name: "angleName", write: true } })], m5.prototype, "angleNames", void 0), e([y({ type: w2, json: { write: true } })], m5.prototype, "point", void 0), e([y({ type: f, json: { write: true } })], m5.prototype, "spatialReference", void 0), e([y({ type: T, json: { write: true } })], m5.prototype, "rasterId", void 0), m5 = n9 = e([a2("esri.rest.support.ImageAngleParameters")], m5);
var l6 = m5;

// node_modules/@arcgis/core/rest/support/BaseImageMeasureParameters.js
var p15 = new s3({ esriMensurationPoint: "point", esriMensurationCentroid: "centroid", esriMensurationDistanceAndAngle: "distance-and-angle", esriMensurationAreaAndPerimeter: "area-and-perimeter", esriMensurationHeightFromBaseAndTop: "base-and-top", esriMensurationHeightFromBaseAndTopShadow: "base-and-top-shadow", esriMensurationHeightFromTopAndTopShadow: "top-and-top-shadow", esriMensurationPoint3D: "point-3D", esriMensurationCentroid3D: "centroid-3D", esriMensurationDistanceAndAngle3D: "distance-and-angle-3D", esriMensurationAreaAndPerimeter3D: "area-and-perimeter-3D" });
var d4 = class extends l {
  constructor() {
    super(...arguments), this.type = null, this.measureOperation = null, this.mosaicRule = null, this.renderingRule = null, this.pixelSize = null, this.raster = void 0;
  }
};
e([y()], d4.prototype, "type", void 0), e([y({ type: p15.apiValues, json: { read: p15.read, write: p15.write } })], d4.prototype, "measureOperation", void 0), e([y({ type: f8, json: { write: true } })], d4.prototype, "mosaicRule", void 0), e([y({ type: w4, json: { write: true } })], d4.prototype, "renderingRule", void 0), e([y({ type: w2, json: { write: true } })], d4.prototype, "pixelSize", void 0), e([y({ json: { write: true } })], d4.prototype, "raster", void 0), d4 = e([a2("esri.rest.support.BaseImageMeasureParameters")], d4);

// node_modules/@arcgis/core/rest/support/ImageAreaParameters.js
var u5;
var c11 = u5 = class extends d4 {
  constructor() {
    super(...arguments), this.type = "area-perimeter", this.geometry = null, this.is3D = false, this.linearUnit = "meters", this.areaUnit = "square-meters";
  }
  writeGeometry(e6, r4, t4) {
    null != e6 && (r4.geometryType = c(e6), r4[t4] = e6.toJSON());
  }
  get measureOperation() {
    return this.is3D ? "area-and-perimeter-3D" : "area-and-perimeter";
  }
  clone() {
    return new u5(p({ geometry: this.geometry, is3D: this.is3D, linearUnit: this.linearUnit, areaUnit: this.areaUnit, mosaicRule: this.mosaicRule, renderingRule: this.renderingRule, pixelSize: this.pixelSize, raster: this.raster }));
  }
};
e([y({ types: n, json: { name: "fromGeometry", read: true, write: true } })], c11.prototype, "geometry", void 0), e([r2("geometry")], c11.prototype, "writeGeometry", null), e([y({ type: p15.apiValues, json: { write: p15.write } })], c11.prototype, "measureOperation", null), e([y({ json: { read: true } })], c11.prototype, "is3D", void 0), e([y({ type: String, json: { read: me.read, write: me.write } })], c11.prototype, "linearUnit", void 0), e([y({ type: String, json: { read: le.read, write: le.write } })], c11.prototype, "areaUnit", void 0), c11 = u5 = e([a2("esri.rest.support.ImageAreaParameters")], c11);
var y8 = c11;

// node_modules/@arcgis/core/rest/support/ImageDistanceParameters.js
var u6;
var y9 = u6 = class extends d4 {
  constructor() {
    super(...arguments), this.type = "distance-angle", this.fromGeometry = null, this.toGeometry = null, this.is3D = false, this.linearUnit = "meters", this.angularUnit = "degrees";
  }
  writeFromGeometry(e6, r4, t4) {
    null != e6 && (r4.geometryType = c(e6), r4[t4] = e6.toJSON());
  }
  get measureOperation() {
    return this.is3D ? "distance-and-angle-3D" : "distance-and-angle";
  }
  clone() {
    return new u6(p({ fromGeometry: this.fromGeometry, toGeometry: this.toGeometry, is3D: this.is3D, linearUnit: this.linearUnit, angularUnit: this.angularUnit, mosaicRule: this.mosaicRule, renderingRule: this.renderingRule, pixelSize: this.pixelSize, raster: this.raster }));
  }
};
e([y({ type: w2, json: { read: true, write: true } })], y9.prototype, "fromGeometry", void 0), e([r2("fromGeometry")], y9.prototype, "writeFromGeometry", null), e([y({ type: w2, json: { read: true, write: true } })], y9.prototype, "toGeometry", void 0), e([y({ type: p15.apiValues, json: { write: p15.write } })], y9.prototype, "measureOperation", null), e([y({ json: { read: true } })], y9.prototype, "is3D", void 0), e([y({ type: String, json: { read: me.read, write: me.write } })], y9.prototype, "linearUnit", void 0), e([y({ type: String, json: { read: fe.read, write: fe.write } })], y9.prototype, "angularUnit", void 0), y9 = u6 = e([a2("esri.rest.support.ImageDistanceParameters")], y9);
var c12 = y9;

// node_modules/@arcgis/core/rest/support/ImageHeightParameters.js
var y10;
var l7 = y10 = class extends d4 {
  constructor() {
    super(...arguments), this.type = "height", this.fromGeometry = null, this.toGeometry = null, this.operationType = "base-and-top", this.linearUnit = "meters";
  }
  writeFromGeometry(e6, r4, t4) {
    null != e6 && (r4.geometryType = c(e6), r4[t4] = e6.toJSON());
  }
  get measureOperation() {
    return this.operationType;
  }
  clone() {
    return new y10(p({ fromGeometry: this.fromGeometry, toGeometry: this.toGeometry, operationType: this.operationType, linearUnit: this.linearUnit, mosaicRule: this.mosaicRule, renderingRule: this.renderingRule, pixelSize: this.pixelSize, raster: this.raster }));
  }
};
e([y({ type: w2, json: { read: true } })], l7.prototype, "fromGeometry", void 0), e([r2("fromGeometry")], l7.prototype, "writeFromGeometry", null), e([y({ type: w2, json: { read: true, write: true } })], l7.prototype, "toGeometry", void 0), e([y({ type: p15.apiValues, json: { write: p15.write } })], l7.prototype, "measureOperation", null), e([y({ json: { read: true } })], l7.prototype, "operationType", void 0), e([y({ type: String, json: { read: me.read, write: me.write } })], l7.prototype, "linearUnit", void 0), l7 = y10 = e([a2("esri.rest.support.ImageHeightParameters")], l7);
var u7 = l7;

// node_modules/@arcgis/core/rest/support/ImageHistogramParameters.js
var y11;
var j5 = y11 = class extends l {
  constructor() {
    super(...arguments), this.geometry = null, this.mosaicRule = null, this.renderingRule = null, this.pixelSize = null, this.raster = void 0, this.timeExtent = null;
  }
  writeGeometry(e6, r4, t4) {
    null != e6 && (r4.geometryType = c(e6), r4[t4] = e6.toJSON());
  }
  clone() {
    return new y11(p({ geometry: this.geometry, mosaicRule: this.mosaicRule, renderingRule: this.renderingRule, pixelSize: this.pixelSize, raster: this.raster, timeExtent: this.timeExtent }));
  }
};
e([y({ types: n, json: { read: v3 } })], j5.prototype, "geometry", void 0), e([r2("geometry")], j5.prototype, "writeGeometry", null), e([y({ type: f8, json: { write: true } })], j5.prototype, "mosaicRule", void 0), e([y({ type: w4, json: { write: true } })], j5.prototype, "renderingRule", void 0), e([y({ type: w2, json: { write: true } })], j5.prototype, "pixelSize", void 0), e([y({ json: { write: true } })], j5.prototype, "raster", void 0), e([y({ type: T2, json: { read: { source: "time" }, write: { target: "time" } } })], j5.prototype, "timeExtent", void 0), j5 = y11 = e([a2("esri.rest.support.ImageHistogramParameters")], j5);
var d5 = j5;

// node_modules/@arcgis/core/rest/support/ImageIdentifyParameters.js
var y12;
var c13 = y12 = class extends l {
  constructor() {
    super(...arguments), this.geometry = null, this.renderingRules = null, this.pixelSize = null, this.returnGeometry = true, this.returnCatalogItems = true, this.returnPixelValues = true, this.maxItemCount = null, this.timeExtent = null, this.raster = void 0, this.viewId = void 0, this.processAsMultidimensional = false;
  }
  writeGeometry(e6, t4, r4) {
    null != e6 && (t4.geometryType = c(e6), t4[r4] = JSON.stringify(e6.toJSON()));
  }
  set mosaicRule(e6) {
    let t4 = e6;
    t4 && t4.mosaicMethod && (t4 = f8.fromJSON({ ...t4.toJSON(), mosaicMethod: t4.mosaicMethod, mosaicOperation: t4.mosaicOperation })), this._set("mosaicRule", t4);
  }
  writeMosaicRule(e6, t4, r4) {
    null != e6 && (t4[r4] = JSON.stringify(e6.toJSON()));
  }
  set renderingRule(e6) {
    let t4 = e6;
    t4 && t4.rasterFunction && (t4 = w4.fromJSON({ ...t4.toJSON(), rasterFunction: t4.rasterFunction, rasterFunctionArguments: t4.rasterFunctionArguments })), this._set("renderingRule", t4);
  }
  writeRenderingRule(e6, t4, r4) {
    null != e6 && (t4[r4] = JSON.stringify(e6.toJSON())), e6.rasterFunctionDefinition && (t4[r4] = JSON.stringify(e6.rasterFunctionDefinition));
  }
  writeRenderingRules(e6, t4, r4) {
    null != e6 && (t4[r4] = JSON.stringify(e6.map((e7) => e7.rasterFunctionDefinition || e7.toJSON())));
  }
  writePixelSize(e6, t4, r4) {
    null != e6 && (t4[r4] = JSON.stringify(e6));
  }
  writeTimeExtent(e6, t4, r4) {
    if (null != e6) {
      const o5 = r(e6.start) ? e6.start.getTime() : null, s9 = r(e6.end) ? e6.end.getTime() : null;
      t4[r4] = null != o5 ? null != s9 ? `${o5},${s9}` : `${o5}` : null;
    }
  }
  clone() {
    return new y12(p({ geometry: this.geometry, mosaicRule: this.mosaicRule, renderingRule: this.renderingRule, pixelSize: this.pixelSize, returnGeometry: this.returnGeometry, returnCatalogItems: this.returnCatalogItems, returnPixelValues: this.returnPixelValues, maxItemCount: this.maxItemCount, processAsMultidimensional: this.processAsMultidimensional, raster: this.raster, viewId: this.viewId, timeExtent: this.timeExtent }));
  }
};
e([y({ json: { write: true } })], c13.prototype, "geometry", void 0), e([r2("geometry")], c13.prototype, "writeGeometry", null), e([y({ type: f8, json: { write: true } })], c13.prototype, "mosaicRule", null), e([r2("mosaicRule")], c13.prototype, "writeMosaicRule", null), e([y({ type: w4, json: { write: true } })], c13.prototype, "renderingRule", null), e([r2("renderingRule")], c13.prototype, "writeRenderingRule", null), e([y({ type: [w4], json: { write: true } })], c13.prototype, "renderingRules", void 0), e([r2("renderingRules")], c13.prototype, "writeRenderingRules", null), e([y({ type: w2, json: { write: true } })], c13.prototype, "pixelSize", void 0), e([r2("pixelSize")], c13.prototype, "writePixelSize", null), e([y({ type: Boolean, json: { write: true } })], c13.prototype, "returnGeometry", void 0), e([y({ type: Boolean, json: { write: true } })], c13.prototype, "returnCatalogItems", void 0), e([y({ type: Boolean, json: { write: true } })], c13.prototype, "returnPixelValues", void 0), e([y({ type: Number, json: { write: true } })], c13.prototype, "maxItemCount", void 0), e([y({ type: T2, json: { write: { target: "time" } } })], c13.prototype, "timeExtent", void 0), e([r2("timeExtent")], c13.prototype, "writeTimeExtent", null), e([y({ json: { write: true } })], c13.prototype, "raster", void 0), e([y({ json: { write: true } })], c13.prototype, "viewId", void 0), e([y({ type: Boolean, json: { write: true } })], c13.prototype, "processAsMultidimensional", void 0), c13 = y12 = e([a2("esri.rest.support.ImageIdentifyParameters")], c13);
var d6 = c13;

// node_modules/@arcgis/core/rest/support/ImagePixelLocationParameters.js
var m6;
var c14 = m6 = class extends l {
  constructor() {
    super(...arguments), this.geometries = null, this.rasterId = null;
  }
  writeGeometry(r4, e6, o5) {
    e6.geometries = { geometryType: "esriGeometryPoint", geometries: r4.map((r5) => r5.toJSON()) };
  }
  clone() {
    var _a;
    return new m6({ geometries: ((_a = this.geometries) == null ? void 0 : _a.map((r4) => r4.clone())) ?? [], rasterId: this.rasterId });
  }
};
e([y({ type: [w2], json: { write: true } })], c14.prototype, "geometries", void 0), e([r2("geometries")], c14.prototype, "writeGeometry", null), e([y({ type: T, json: { write: true } })], c14.prototype, "rasterId", void 0), c14 = m6 = e([a2("esri.rest.support.ImagePixelLocationParameters")], c14);
var a8 = c14;

// node_modules/@arcgis/core/rest/support/ImagePointParameters.js
var c15;
var u8 = c15 = class extends d4 {
  constructor() {
    super(...arguments), this.type = "point", this.geometry = null, this.is3D = false;
  }
  writeGeometry(e6, r4, t4) {
    null != e6 && (r4.geometryType = c(e6), r4[t4] = e6.toJSON());
  }
  get measureOperation() {
    const { is3D: e6, geometry: r4 } = this;
    return "point" === r4.type ? e6 ? "point-3D" : "point" : e6 ? "centroid-3D" : "centroid";
  }
  clone() {
    return new c15(p({ geometry: this.geometry, is3D: this.is3D, mosaicRule: this.mosaicRule, renderingRule: this.renderingRule, pixelSize: this.pixelSize, raster: this.raster }));
  }
};
e([y({ types: n, json: { name: "fromGeometry", read: v3 } })], u8.prototype, "geometry", void 0), e([r2("geometry")], u8.prototype, "writeGeometry", null), e([y({ type: p15.apiValues, json: { read: p15.read, write: p15.write } })], u8.prototype, "measureOperation", null), e([y({ json: { read: true } })], u8.prototype, "is3D", void 0), u8 = c15 = e([a2("esri.rest.support.ImagePointParameters")], u8);
var l8 = u8;

// node_modules/@arcgis/core/rest/support/ImageSampleParameters.js
var d7;
var j6 = d7 = class extends l {
  constructor() {
    super(...arguments), this.geometry = null, this.interpolation = "nearest", this.mosaicRule = null, this.outFields = null, this.pixelSize = null, this.returnFirstValueOnly = true, this.sampleDistance = null, this.sampleCount = null, this.sliceId = null, this.timeExtent = null;
  }
  writeGeometry(t4, e6, o5) {
    null != t4 && (e6.geometryType = c(t4), e6[o5] = t4.toJSON());
  }
  set locations(t4) {
    if (t4 == null ? void 0 : t4.length) {
      const e6 = new u({ spatialReference: t4[0].spatialReference });
      e6.points = t4.map((t5) => [t5.x, t5.y]), this._set("locations", t4), this.geometry = e6;
    }
  }
  clone() {
    return new d7(p({ geometry: this.geometry, locations: this.locations, interpolation: this.interpolation, mosaicRule: this.mosaicRule, outFields: this.outFields, raster: this.raster, returnFirstValueOnly: this.returnFirstValueOnly, sampleDistance: this.sampleDistance, sampleCount: this.sampleCount, sliceId: this.sliceId, pixelSize: this.pixelSize, timeExtent: this.timeExtent }));
  }
};
e([y({ types: n, json: { read: v3 } })], j6.prototype, "geometry", void 0), e([r2("geometry")], j6.prototype, "writeGeometry", null), e([y()], j6.prototype, "locations", null), e([y({ type: String, json: { type: o4.jsonValues, read: o4.read, write: o4.write } })], j6.prototype, "interpolation", void 0), e([y({ type: f8, json: { write: true } })], j6.prototype, "mosaicRule", void 0), e([y({ type: [String], json: { write: true } })], j6.prototype, "outFields", void 0), e([y({ type: w2, json: { write: true } })], j6.prototype, "pixelSize", void 0), e([y({ type: String, json: { write: true } })], j6.prototype, "raster", void 0), e([y({ type: Boolean, json: { write: true } })], j6.prototype, "returnFirstValueOnly", void 0), e([y({ type: Number, json: { write: true } })], j6.prototype, "sampleDistance", void 0), e([y({ type: Number, json: { write: true } })], j6.prototype, "sampleCount", void 0), e([y({ type: Number, json: { write: true } })], j6.prototype, "sliceId", void 0), e([y({ type: T2, json: { read: { source: "time" }, write: { target: "time" } } })], j6.prototype, "timeExtent", void 0), j6 = d7 = e([a2("esri.rest.support.ImageSampleParameters")], j6);
var h5 = j6;

// node_modules/@arcgis/core/layers/mixins/ArcGISImageService.js
var je = o2()({ U1: "u1", U2: "u2", U4: "u4", U8: "u8", S8: "s8", U16: "u16", S16: "s16", U32: "u32", S32: "s32", F32: "f32", F64: "f64", C64: "c64", C128: "c128", UNKNOWN: "unknown" });
var Pe = /* @__PURE__ */ new Set(["png", "png8", "png24", "png32", "jpg", "bmp", "gif", "jpgpng", "lerc", "tiff"]);
var Te = j(a, { min: 0, max: 255 });
function Oe(e6) {
  var _a;
  if (!e6) return null;
  const t4 = (_a = JSON.stringify(e6).match(/"rasterFunction":"(.*?")/gi)) == null ? void 0 : _a.map((e7) => e7.replace('"rasterFunction":"', "").replace('"', ""));
  return t4 ? t4.join("/") : null;
}
var Ne = (a9) => {
  let g3 = class extends a9 {
    constructor() {
      super(...arguments), this._functionRasterInfos = {}, this._rasterJobHandler = { instance: null, refCount: 0, connectionPromise: null }, this._cachedRendererJson = null, this._serviceSupportsMosaicRule = null, this._rasterAttributeTableFieldPrefix = "Raster.", this.adjustAspectRatio = null, this.bandIds = void 0, this.capabilities = null, this.compressionQuality = void 0, this.compressionTolerance = 0.01, this.copyright = null, this.defaultMosaicRule = null, this.definitionExpression = null, this.exportImageServiceParameters = null, this.rasterInfo = null, this.fields = null, this.fullExtent = null, this.hasMultidimensions = false, this.imageMaxHeight = 4100, this.imageMaxWidth = 4100, this.interpolation = void 0, this.minScale = 0, this.maxScale = 0, this.multidimensionalInfo = null, this.multidimensionalSubset = null, this.noData = null, this.noDataInterpretation = void 0, this.objectIdField = null, this.geometryType = "polygon", this.typeIdField = null, this.types = [], this.pixelFilter = null, this.raster = void 0, this.sourceType = null, this.viewId = void 0, this.symbolizer = null, this.rasterFunctionInfos = null, this.serviceDataType = null, this.spatialReference = null, this.pixelType = null, this.serviceRasterInfo = null, this.sourceJSON = null, this.url = null, this.version = void 0;
    }
    initialize() {
      this._set("exportImageServiceParameters", new y6({ layer: this }));
    }
    readServiceSupportsMosaicRule(e6, t4) {
      return this._isMosaicRuleSupported(t4);
    }
    get _rasterFunctionNamesIndex() {
      const e6 = /* @__PURE__ */ new Map();
      return !this.rasterFunctionInfos || r(this.rasterFunctionInfos) && this.rasterFunctionInfos.length < 1 || r(this.rasterFunctionInfos) && this.rasterFunctionInfos.forEach((t4) => {
        e6.set(t4.name.toLowerCase().replace(/ /gi, "_"), t4.name);
      }), e6;
    }
    readBandIds(e6, t4) {
      if (Array.isArray(e6) && e6.length > 0 && e6.every((e7) => "number" == typeof e7)) return e6;
    }
    readCapabilities(e6, t4) {
      return this._readCapabilities(t4);
    }
    writeCompressionQuality(e6, t4, i6) {
      null != e6 && "lerc" !== this.format && (t4[i6] = e6);
    }
    writeCompressionTolerance(e6, t4, i6) {
      "lerc" === this.format && null != e6 && (t4[i6] = e6);
    }
    readDefaultMosaicRule(e6, t4) {
      return this._serviceSupportsMosaicRule ? f8.fromJSON(t4) : null;
    }
    get fieldsIndex() {
      return this.fields ? new r3(this.fields) : null;
    }
    set format(e6) {
      e6 && Pe.has(e6.toLowerCase()) && this._set("format", e6.toLowerCase());
    }
    readFormat(e6, t4) {
      return "esriImageServiceDataTypeVector-UV" === t4.serviceDataType || "esriImageServiceDataTypeVector-MagDir" === t4.serviceDataType || null != this.pixelFilter ? "lerc" : "jpgpng";
    }
    readMinScale(e6, t4) {
      return null != t4.minLOD && null != t4.maxLOD ? e6 : 0;
    }
    readMaxScale(e6, t4) {
      return null != t4.minLOD && null != t4.maxLOD ? e6 : 0;
    }
    set mosaicRule(e6) {
      let t4 = e6;
      t4 && t4.mosaicMethod && (t4 = f8.fromJSON({ ...t4.toJSON(), mosaicMethod: t4.mosaicMethod, mosaicOperation: t4.mosaicOperation })), this._set("mosaicRule", t4);
    }
    readMosaicRule(e6, t4) {
      const i6 = e6 || t4.mosaicRule;
      return i6 ? f8.fromJSON(i6) : this._isMosaicRuleSupported(t4) ? f8.fromJSON(t4) : null;
    }
    writeMosaicRule(e6, t4, i6) {
      let r4 = this.mosaicRule;
      const s9 = this.definitionExpression;
      r4 ? s9 && s9 !== r4.where && (r4 = r4.clone(), r4.where = s9) : s9 && (r4 = new f8({ where: s9 })), this._isValidCustomizedMosaicRule(r4) && (t4[i6] = r4.toJSON());
    }
    writeNoData(e6, t4, i6) {
      null != e6 && "number" == typeof e6 && (t4[i6] = Te(e6));
    }
    readObjectIdField(e6, t4) {
      if (!e6) {
        const i6 = t4.fields.filter((e7) => "esriFieldTypeOID" === e7.type || "oid" === e7.type);
        e6 = i6 && i6[0] && i6[0].name;
      }
      return e6;
    }
    get parsedUrl() {
      return L(this.url);
    }
    readSourceType(e6, t4) {
      return this._isMosaicDataset(t4) ? "mosaic-dataset" : "raster-dataset";
    }
    set renderer(e6) {
      this.loaded && (e6 = this._configRenderer(e6)), this._set("renderer", e6);
    }
    readRenderer(e6, t4, i6) {
      var _a, _b;
      const r4 = (_b = (_a = t4 == null ? void 0 : t4.layerDefinition) == null ? void 0 : _a.drawingInfo) == null ? void 0 : _b.renderer, n10 = c4(r4, i6);
      return null == n10 ? null : ("vector-field" === n10.type && t4.symbolTileSize && !r4.symbolTileSize && (n10.symbolTileSize = t4.symbolTileSize), g2(n10) || s.getLogger(this.declaredClass).warn("ArcGISImageService", "Imagery layer doesn't support given renderer type."), n10);
    }
    writeRenderer(e6, t4, i6) {
      t4.layerDefinition = t4.layerDefinition || {}, t4.layerDefinition.drawingInfo = t4.layerDefinition.drawingInfo || {}, t4.layerDefinition.drawingInfo.renderer = e6.toJSON(), "vector-field" === e6.type && (t4.symbolTileSize = e6.symbolTileSize);
    }
    get rasterFields() {
      var _a;
      const e6 = this._rasterAttributeTableFieldPrefix || "Raster.", t4 = new y2({ name: "Raster.ItemPixelValue", alias: "Item Pixel Value", domain: null, editable: false, length: 50, type: "string" }), i6 = new y2({ name: "Raster.ServicePixelValue", alias: "Service Pixel Value", domain: null, editable: false, length: 50, type: "string" }), r4 = new y2({ name: "Raster.ServicePixelValue.Raw", alias: "Raw Service Pixel Value", domain: null, editable: false, length: 50, type: "string" });
      let s9 = this.fields ? p(this.fields) : [];
      s9.push(i6), ((_a = this.capabilities) == null ? void 0 : _a.operations.supportsQuery) && this.fields && this.fields.length > 0 && s9.push(t4), this.version >= 10.4 && r(this.rasterFunctionInfos) && this.rasterFunctionInfos.some((e7) => "none" === e7.name.toLowerCase()) && s9.push(r4), r(this.rasterFunctionInfos) && this.rasterFunctionInfos.filter((e7) => "none" !== e7.name.toLowerCase()).forEach((e7) => {
        s9.push(new y2({ name: "Raster.ServicePixelValue." + e7.name, alias: e7.name, domain: null, editable: false, length: 50, type: "string" }));
      }), this._isVectorDataSet() && (s9.push(new y2({ name: "Raster.Magnitude", alias: "Magnitude", domain: null, editable: false, type: "double" })), s9.push(new y2({ name: "Raster.Direction", alias: "Direction", domain: null, editable: false, type: "double" })));
      const { attributeTable: n10 } = this.rasterInfo ?? {};
      if (r(n10)) {
        const t5 = n10.fields.filter((e7) => "esriFieldTypeOID" !== e7.type && "value" !== e7.name.toLowerCase()).map((t6) => {
          const i7 = p(t6);
          return i7.name = e6 + t6.name, i7;
        });
        s9 = s9.concat(t5);
      }
      return s9;
    }
    set renderingRule(e6) {
      let t4 = e6;
      t4 && t4.rasterFunction && (t4 = w4.fromJSON({ ...t4.toJSON(), rasterFunction: t4.rasterFunction, rasterFunctionArguments: t4.rasterFunctionArguments })), this._set("renderingRule", t4);
    }
    readRenderingRule(e6, t4) {
      const i6 = t4.rasterFunctionInfos;
      return t4.renderingRule || i6 && i6.length && "None" !== i6[0].name ? this._isRFTJson(t4.renderingRule) ? w4.fromJSON({ rasterFunctionDefinition: t4.renderingRule }) : w4.fromJSON(t4.renderingRule || { rasterFunctionInfos: t4.rasterFunctionInfos }) : null;
    }
    writeRenderingRule(e6, t4, i6) {
      const r4 = e6.toJSON();
      r4.rasterFunctionDefinition ? t4[i6] = r4.rasterFunctionDefinition : t4[i6] = r4;
    }
    readSpatialReference(e6, t4) {
      const i6 = e6 || t4.extent.spatialReference;
      return i6 ? f.fromJSON(i6) : null;
    }
    readPixelType(e6) {
      return je.fromJSON(e6) || e6;
    }
    writePixelType(e6, t4, i6) {
      (t(this.serviceRasterInfo) || this.pixelType !== this.serviceRasterInfo.pixelType) && (t4[i6] = je.toJSON(e6));
    }
    readVersion(e6, t4) {
      let i6 = t4.currentVersion;
      return i6 || (i6 = t4.hasOwnProperty("fields") || t4.hasOwnProperty("timeInfo") ? 10 : 9.3), i6;
    }
    applyFilter(e6) {
      let t4 = e6;
      return this.pixelFilter && (t4 = this._clonePixelData(e6), this.pixelFilter(t4)), t4;
    }
    async applyRenderer(e6, t4) {
      let i6 = e6;
      const { renderer: r4, symbolizer: s9, pixelFilter: n10, bandIds: o5 } = this;
      if (!this._isPicture() && r4 && s9 && !n10) {
        const n11 = JSON.stringify(this._cachedRendererJson) !== JSON.stringify(r4.toJSON()), a10 = this._rasterJobHandler.instance;
        if (a10) {
          n11 && (s9.bind(), await a10.updateSymbolizer(s9, t4), this._cachedRendererJson = r4.toJSON());
          const l9 = await a10.symbolize({ bandIds: o5, ...e6 }, t4);
          i6 = { extent: e6.extent, pixelBlock: l9 };
        } else i6 = { extent: e6.extent, pixelBlock: s9.symbolize({ bandIds: o5, ...e6 }) };
      }
      return i6;
    }
    destroy() {
      this._shutdownJobHandler();
    }
    increaseRasterJobHandlerUsage() {
      this._rasterJobHandler.refCount++;
    }
    decreaseRasterJobHandlerUsage() {
      this._rasterJobHandler.refCount--, this._rasterJobHandler.refCount <= 0 && this._shutdownJobHandler();
    }
    async computeAngles(e6, t4) {
      if (!(await this._fetchCapabilities(t4 == null ? void 0 : t4.signal)).operations.supportsComputeAngles) throw new s2("imagery-layer:compute-angles", "this operation is not supported on the input image service");
      return e6 = v(l6, e6).clone(), d3(this.url, e6, this._getRequestOptions(t4));
    }
    async computePixelSpaceLocations(e6, t4) {
      if (!(await this._fetchCapabilities(t4 == null ? void 0 : t4.signal)).operations.supportsComputePixelLocation) throw new s2("imagery-layer:compute-pixel-space-locations", "this operation is not supported on the input image service");
      return e6 = v(a8, e6).clone(), N2(this.url, e6, this._getRequestOptions(t4));
    }
    async computeHistograms(e6, t4) {
      if (!(await this._fetchCapabilities(t4 == null ? void 0 : t4.signal)).operations.supportsComputeHistograms) throw new s2("imagery-layer:compute-histograms", "this operation is not supported on the input image service");
      return e6 = v(d5, e6).clone(), this._applyMosaicAndRenderingRules(e6), J(this.url, e6, this._getRequestOptions(t4));
    }
    async computeStatisticsHistograms(e6, t4) {
      if (!(await this._fetchCapabilities(t4 == null ? void 0 : t4.signal)).operations.supportsComputeStatisticsHistograms) throw new s2("imagery-layer:compute-statistics-histograms", "this operation is not supported on the input image service");
      return e6 = v(d5, e6).clone(), this._applyMosaicAndRenderingRules(e6), R2(this.url, e6, this._getRequestOptions(t4));
    }
    async measureHeight(e6, t4) {
      const i6 = await this._fetchCapabilities(t4 == null ? void 0 : t4.signal);
      if (!("base-and-top" === e6.operationType ? i6.mensuration.supportsHeightFromBaseAndTop : "base-and-top-shadow" === e6.operationType ? i6.mensuration.supportsHeightFromBaseAndTopShadow : i6.mensuration.supportsHeightFromTopAndTopShadow)) throw new s2("imagery-layer:measure-height", "this operation is not supported on the input image service");
      return e6 = v(u7, e6).clone(), this._applyMosaicAndRenderingRules(e6), h4(this.url, e6, this._getRequestOptions(t4));
    }
    async measureAreaAndPerimeter(e6, t4) {
      const i6 = await this._fetchCapabilities(t4 == null ? void 0 : t4.signal);
      if (!(i6.mensuration.supportsAreaAndPerimeter && (!e6.is3D || i6.mensuration.supports3D))) throw new s2("imagery-layer:measure-area-and-perimeter", "this operation is not supported on the input image service");
      return e6 = v(y8, e6).clone(), this._applyMosaicAndRenderingRules(e6), w7(this.url, e6, this._getRequestOptions(t4));
    }
    async measureDistanceAndAngle(e6, t4) {
      const i6 = await this._fetchCapabilities(t4 == null ? void 0 : t4.signal);
      if (!(i6.mensuration.supportsDistanceAndAngle && (!e6.is3D || i6.mensuration.supports3D))) throw new s2("imagery-layer:measure-distance-and-angle", "this operation is not supported on the input image service");
      return e6 = v(c12, e6).clone(), this._applyMosaicAndRenderingRules(e6), C3(this.url, e6, this._getRequestOptions(t4));
    }
    async measurePointOrCentroid(e6, t4) {
      const i6 = await this._fetchCapabilities(t4 == null ? void 0 : t4.signal);
      if (!(i6.mensuration.supportsPointOrCentroid && (!e6.is3D || i6.mensuration.supports3D))) throw new s2("imagery-layer:measure-point-or-centroid", "this operation is not supported on the input image service");
      return e6 = v(l8, e6).clone(), this._applyMosaicAndRenderingRules(e6), I(this.url, e6, this._getRequestOptions(t4));
    }
    getField(e6) {
      const { fieldsIndex: t4 } = this;
      return r(t4) ? t4.get(e6) : void 0;
    }
    getFieldDomain(e6, t4) {
      const i6 = this.getField(e6);
      return i6 ? i6.domain : null;
    }
    async fetchImage(e6, t4, i6, r4 = {}) {
      if (null == e6 || null == t4 || null == i6) throw new s2("imagery-layer:fetch-image", "Insufficient parameters for requesting an image. A valid extent, width and height values are required.");
      if (this.renderer || this.symbolizer) {
        const e7 = await this.generateRasterInfo(this.renderingRule, { signal: r4.signal });
        e7 && (this.rasterInfo = e7);
      }
      const s9 = this.getExportImageServiceParameters(e6, t4, i6, r4.timeExtent);
      if (null == s9) {
        if (r4.requestAsImageElement && this._canRequestImageElement(this.format)) {
          const e7 = document.createElement("canvas");
          if (e7.width = t4, e7.height = i6, r4.returnImageBitmap) {
            return { imageBitmap: await e3(e7, `${b4(this.parsedUrl)}/exportImage`) };
          }
          return { imageOrCanvasElement: e7 };
        }
        const { bandIds: s10, rasterInfo: n11 } = this, o5 = ((s10 == null ? void 0 : s10.length) || n11.bandCount) ?? 0, a11 = t4 * i6, l10 = n11.pixelType, u9 = [];
        for (let e7 = 0; e7 < o5; e7++) u9.push(m.createEmptyBand(l10, a11));
        return { pixelData: { pixelBlock: new m({ width: t4, height: i6, pixels: u9, mask: new Uint8Array(a11), pixelType: l10 }), extent: e6 } };
      }
      const n10 = !!r4.requestAsImageElement && !this.pixelFilter, a10 = n10 && !!r4.returnImageBitmap, l9 = { imageServiceParameters: s9, imageProps: { extent: e6, width: t4, height: i6, format: this.format }, requestAsImageElement: n10, returnImageBitmap: a10, signal: r4.signal };
      return this._requestArrayBuffer(l9);
    }
    fetchKeyProperties(e6) {
      return U(b4(this.parsedUrl) + "/keyProperties", { query: this._getQueryParams({ renderingRule: this.version >= 10.3 ? e6 == null ? void 0 : e6.renderingRule : null }) }).then((e7) => e7.data);
    }
    fetchRasterAttributeTable(e6) {
      return this.version < 10.1 ? Promise.reject(new s2("#fetchRasterAttributeTable()", "Failed to get rasterAttributeTable")) : U(b4(this.parsedUrl) + "/rasterAttributeTable", { query: this._getQueryParams({ renderingRule: this.version >= 10.3 ? e6 == null ? void 0 : e6.renderingRule : null }) }).then((e7) => x2.fromJSON(e7.data));
    }
    getCatalogItemRasterInfo(e6, t4) {
      const i6 = { ...t4, query: this._getQueryParams() };
      return n8(b4(this.parsedUrl), e6, i6);
    }
    async getCatalogItemICSInfo(e6, t4) {
      var _a, _b, _c;
      const { data: i6 } = await U(b4(this.parsedUrl) + "/" + e6 + "/info/ics", { query: this._getQueryParams(), ...t4 }), r4 = i6 && i6.ics;
      if (!r4) return;
      let s9 = null;
      try {
        s9 = (await U(b4(this.parsedUrl) + "/" + e6 + "/info", { query: this._getQueryParams(), ...t4 })).data.extent;
      } catch {
      }
      if (!s9 || !s9.spatialReference) return { ics: r4, icsToPixelTransform: null, icsExtent: null, northDirection: null };
      const o5 = this.version >= 10.7 ? U(b4(this.parsedUrl) + "/" + e6 + "/info/icstopixel", { query: this._getQueryParams(), ...t4 }).then((e7) => e7.data).catch(() => ({})) : {}, a10 = s9.spatialReference, l9 = { geometries: JSON.stringify({ geometryType: "esriGeometryEnvelope", geometries: [s9] }), inSR: a10.wkid || JSON.stringify(a10), outSR: "0:" + e6 }, u9 = U(b4(this.parsedUrl) + "/project", { query: this._getQueryParams(l9), ...t4 }).then((e7) => e7.data).catch(() => ({})), p16 = 5, c16 = (s9.xmin + s9.xmax) / 2, m7 = (s9.ymax - s9.ymin) / (p16 + 1), d8 = s9.ymin + m7, h6 = [];
      for (let n10 = 0; n10 < p16; n10++) h6.push({ x: c16, y: d8 + m7 * n10 });
      const f9 = { geometries: JSON.stringify({ geometryType: "esriGeometryPoint", geometries: h6 }), inSR: a10.wkid || JSON.stringify(a10), outSR: "0:" + e6 }, g4 = U(b4(this.parsedUrl) + "/project", { query: this._getQueryParams(f9), ...t4 }).then((e7) => e7.data).catch(() => ({})), y13 = await Promise.all([o5, u9, g4]);
      let R3 = y13[0].ipxf;
      if (null == R3) {
        const e7 = (_a = r4.geodataXform) == null ? void 0 : _a.xf_0;
        "topup" === ((_b = e7 == null ? void 0 : e7.name) == null ? void 0 : _b.toLowerCase()) && 6 === ((_c = e7 == null ? void 0 : e7.coefficients) == null ? void 0 : _c.length) && (R3 = { affine: { name: "ics [sensor: Frame] to pixel (column, row) transformation", coefficients: e7.coefficients, cellsizeRatio: 0, type: "GeometricXform" } });
      }
      const S4 = w3.fromJSON(y13[1] && y13[1].geometries && y13[1].geometries[0]);
      S4 && (S4.spatialReference = new f({ wkid: 0, imageCoordinateSystem: r4 }));
      const v9 = y13[2].geometries ? y13[2].geometries.filter((e7) => null != e7 && null != e7.x && null != e7.y && "NaN" !== e7.x && "NaN" !== e7.y) : [], x4 = v9.length;
      if (x4 < 3) return { ics: r4, icsToPixelTransform: R3, icsExtent: S4, northDirection: null };
      let I2 = 0, w8 = 0, _3 = 0, F2 = 0;
      for (let n10 = 0; n10 < x4; n10++) I2 += v9[n10].x, w8 += v9[n10].y, _3 += v9[n10].x * v9[n10].x, F2 += v9[n10].x * v9[n10].y;
      const D2 = (x4 * F2 - I2 * w8) / (x4 * _3 - I2 * I2);
      let j7 = 0;
      const P = v9[p16 - 1].x > v9[0].x, T6 = v9[p16 - 1].y > v9[0].y;
      return D2 === 1 / 0 ? j7 = T6 ? 90 : 270 : 0 === D2 ? j7 = P ? 0 : 180 : D2 > 0 ? j7 = P ? 180 * Math.atan(D2) / Math.PI : 180 * Math.atan(D2) / Math.PI + 180 : D2 < 0 && (j7 = T6 ? 180 + 180 * Math.atan(D2) / Math.PI : 360 + 180 * Math.atan(D2) / Math.PI), { ics: r4, icsToPixelTransform: R3, icsExtent: S4, northDirection: j7 };
    }
    async generateRasterInfo(e6, t4) {
      var _a;
      if (e6 = v(w4, e6), this.serviceRasterInfo && (!e6 || "none" === ((_a = e6.functionName) == null ? void 0 : _a.toLowerCase()) || this._isVectorFieldResampleFunction(e6))) return this.serviceRasterInfo;
      const i6 = Oe(e6);
      if (!i6) return null;
      if (this._functionRasterInfos[i6]) return this._functionRasterInfos[i6];
      const r4 = this._generateRasterInfo(e6, t4);
      this._functionRasterInfos[i6] = r4;
      try {
        return await r4;
      } catch {
        return this._functionRasterInfos[i6] = null, null;
      }
    }
    getExportImageServiceParameters(e6, t4, i6, r4) {
      var _a;
      e6 = e6.clone().shiftCentralMeridian();
      const s9 = $2(e6.spatialReference, b4(this.parsedUrl));
      this.pixelType !== this.serviceRasterInfo.pixelType && (this.exportImageServiceParameters.pixelType = this.pixelType);
      const n10 = this.exportImageServiceParameters.toJSON(), { bandIds: o5, noData: a10 } = n10;
      let { renderingRule: l9 } = n10;
      const u9 = (_a = this.renderingRule) == null ? void 0 : _a.rasterFunctionDefinition, c16 = !this.renderer || "raster-stretch" === this.renderer.type;
      if ((o5 == null ? void 0 : o5.length) && this._hasRenderingRule(this.renderingRule) && !u9 && c16) {
        const e7 = { rasterFunction: "ExtractBand", rasterFunctionArguments: { BandIds: o5 } };
        if ("Stretch" === l9.rasterFunction) e7.rasterFunctionArguments.Raster = l9.rasterFunctionArguments.Raster, l9.rasterFunctionArguments.Raster = e7;
        else if ("Colormap" === l9.rasterFunction) {
          const t5 = l9.rasterFunctionArguments.Raster;
          "Stretch" === (t5 == null ? void 0 : t5.rasterFunction) ? (e7.rasterFunctionArguments.Raster = t5.rasterFunctionArguments.Raster, t5.rasterFunctionArguments.Raster = e7) : (e7.rasterFunctionArguments.Raster = t5, l9.rasterFunctionArguments.Raster = e7);
        } else e7.rasterFunctionArguments.Raster = l9, l9 = e7;
        n10.bandIds = void 0;
      } else n10.bandIds = o5 == null ? void 0 : o5.join(",");
      a10 instanceof Array && a10.length > 0 && (n10.noData = a10.join(","));
      const m7 = this._processMultidimensionalIntersection(null, r4, this.exportImageServiceParameters.mosaicRule);
      if (m7.isOutSide) return null;
      n10.mosaicRule = r(m7.mosaicRule) ? JSON.stringify(m7.mosaicRule) : null, r4 = m7.timeExtent, n10.renderingRule = this._getRenderingRuleString(w4.fromJSON(l9));
      const d8 = {};
      if (r(r4)) {
        const { start: e7, end: t5 } = r4.toJSON();
        e7 && t5 && e7 === t5 ? d8.time = "" + e7 : null == e7 && null == t5 || (d8.time = `${e7 ?? "null"},${t5 ?? "null"}`);
      }
      return { bbox: e6.xmin + "," + e6.ymin + "," + e6.xmax + "," + e6.ymax, bboxSR: s9, imageSR: s9, size: t4 + "," + i6, ...n10, ...d8 };
    }
    async getSamples(e6, t4) {
      var _a;
      if (!((_a = await this._fetchCapabilities(t4 == null ? void 0 : t4.signal)) == null ? void 0 : _a.operations.supportsGetSamples)) throw new s2("imagery-layer:get-samples", "getSamples operation is not supported on the input image service");
      e6 = v(h5, e6).clone();
      const { raster: i6 } = this;
      return i6 && null == e6.raster && (e6.raster = i6), O2(this.url, e6, this._getRequestOptions(t4));
    }
    async identify(e6, t4) {
      if (!(await this._fetchCapabilities(t4 == null ? void 0 : t4.signal)).operations.supportsIdentify) throw new s2("imagery-layer:identify", "identify operation is not supported on the input image service");
      e6 = v(d6, e6).clone();
      const i6 = this._processMultidimensionalIntersection(e6.geometry, e6.timeExtent, e6.mosaicRule || this.mosaicRule);
      if (i6.isOutSide) throw new s2("imagery-layer:identify", "the request cannot be fulfilled when falling outside of the multidimensional subset");
      e6.timeExtent = e2(i6.timeExtent), e6.mosaicRule = e2(i6.mosaicRule);
      const { raster: r4, renderingRule: s9 } = this;
      return s9 && null == e6.renderingRule && (e6.renderingRule = s9), r4 && null == e6.raster && (e6.raster = r4), j4(this.url, e6, this._getRequestOptions(t4));
    }
    createQuery() {
      const e6 = new x();
      return e6.outFields = ["*"], e6.returnGeometry = true, e6.where = this.definitionExpression || "1=1", e6;
    }
    async queryRasters(e6, t4) {
      return { query: e6, requestOptions: t4 } = await this._prepareForQuery(e6, t4), s7(this.url, e6, t4);
    }
    async queryObjectIds(e6, t4) {
      return { query: e6, requestOptions: t4 } = await this._prepareForQuery(e6, t4), s6(this.url, e6, t4);
    }
    async queryRasterCount(e6, t4) {
      return { query: e6, requestOptions: t4 } = await this._prepareForQuery(e6, t4), n3(this.url, e6, t4);
    }
    async queryVisibleRasters(e6, t4) {
      var _a, _b, _c, _d;
      if (!e6) throw new s2("imagery-layer: query-visible-rasters", "missing query parameter");
      await this.load();
      const { pixelSize: i6, returnDomainValues: r4, returnTopmostRaster: s9, showNoDataRecords: n10 } = t4 || { pixelSize: null, returnDomainValues: false, returnTopmostRaster: false, showNoDataRecords: false };
      let a10 = false, l9 = null, u9 = null;
      const d8 = "raster.servicepixelvalue", h6 = this._rasterFunctionNamesIndex;
      if (r(e6.outFields) && (a10 = e6.outFields.some((e7) => !e7.toLowerCase().includes(d8)), this.version >= 10.4)) {
        const t5 = e6.outFields.filter((e7) => e7.toLowerCase().includes(d8) && e7.length > d8.length).map((e7) => {
          const t6 = e7.slice(d8.length + 1);
          return [this._updateRenderingRulesFunctionName(t6, h6), t6];
        });
        l9 = t5.map((e7) => new w4({ functionName: e7[0] })), u9 = t5.map((e7) => e7[1]);
        const { renderingRule: i7 } = this;
        0 === l9.length ? (i7 == null ? void 0 : i7.functionName) ? (l9.push(i7), u9.push(i7.functionName)) : l9 = null : (i7 == null ? void 0 : i7.functionName) && !l9.some((e7) => e7.functionName === i7.functionName) && (l9.push(i7), u9.push(i7.functionName));
      }
      const f9 = t(e6.outSpatialReference) || e6.outSpatialReference.equals(this.spatialReference), { multidimensionalSubset: g4 } = this;
      let y13 = e6.timeExtent || this.timeExtent;
      if (g4) {
        const { isOutside: t5, intersection: i7 } = c6(g4, { geometry: e2(e6.geometry), timeExtent: e2(e6.timeExtent), multidimensionalDefinition: (_a = this.exportImageServiceParameters.mosaicRule) == null ? void 0 : _a.multidimensionalDefinition });
        if (t5) throw new s2("imagery-layer:query-visible-rasters", "the request cannot be fulfilled when falling outside of the multidimensional subset");
        i7 && r(i7.timeExtent) && (y13 = i7.timeExtent);
      }
      const R3 = this._combineMosaicRuleWithTimeExtent(this.exportImageServiceParameters.mosaicRule, y13), b5 = this._getQueryParams({ geometry: e6.geometry, timeExtent: y13, mosaicRule: R3, renderingRule: this.version < 10.4 ? this.renderingRule : null, renderingRules: l9, pixelSize: i6, returnCatalogItems: a10, returnGeometry: f9, raster: this.raster, maxItemCount: s9 ? 1 : null });
      delete b5.f;
      const S4 = new d6(b5);
      try {
        await this.generateRasterInfo(this.renderingRule);
        const i7 = await j4(this.url, S4, { signal: t4 == null ? void 0 : t4.signal, query: { ...this.customParameters } }), s10 = e6.outFields, o5 = null != i7.value && i7.value.toLowerCase().includes("nodata");
        if (!(a10 && !f9 && ((_b = i7 == null ? void 0 : i7.catalogItems) == null ? void 0 : _b.features.length) && (n10 || !o5))) return this._processVisibleRastersResponse(i7, { returnDomainValues: r4, templateRRFunctionNames: u9, showNoDataRecords: n10, templateFields: s10 });
        const l10 = this.objectIdField || "ObjectId", c16 = ((_c = i7.catalogItems) == null ? void 0 : _c.features) ?? [], m7 = c16.map((e7) => {
          var _a2;
          return (_a2 = e7.attributes) == null ? void 0 : _a2[l10];
        }), d9 = new x({ objectIds: m7, returnGeometry: true, outSpatialReference: e6.outSpatialReference, outFields: [l10] }), h7 = await this.queryRasters(d9);
        return ((_d = h7 == null ? void 0 : h7.features) == null ? void 0 : _d.length) && h7.features.forEach((t5) => {
          c16.forEach((i8) => {
            i8.attributes[l10] === t5.attributes[l10] && (i8.geometry = new v2(t5.geometry), r(e6.outSpatialReference) && (i8.geometry.spatialReference = e6.outSpatialReference));
          });
        }), this._processVisibleRastersResponse(i7, { returnDomainValues: r4, templateRRFunctionNames: u9, showNoDataRecords: n10, templateFields: s10 });
      } catch {
        throw new s2("imagery-layer:query-visible-rasters", "encountered error when querying visible rasters");
      }
    }
    async fetchVariableStatisticsHistograms(e6, t4) {
      const i6 = U(b4(this.parsedUrl) + "/statistics", { query: this._getQueryParams({ variable: e6 }), signal: t4 }).then((e7) => {
        var _a;
        return (_a = e7.data) == null ? void 0 : _a.statistics;
      }), r4 = U(b4(this.parsedUrl) + "/histograms", { query: this._getQueryParams({ variable: e6 }), signal: t4 }).then((e7) => {
        var _a;
        return (_a = e7.data) == null ? void 0 : _a.histograms;
      }), s9 = await Promise.all([i6, r4]);
      return s9[0] && s9[0].forEach((e7) => {
        e7.avg = e7.mean, e7.stddev = e7.standardDeviation;
      }), { statistics: s9[0] || null, histograms: s9[1] || null };
    }
    async createFlowMesh(e6, t4) {
      const i6 = this._rasterJobHandler.instance;
      return i6 ? i6.createFlowMesh(e6, t4) : f6(e6.meshType, e6.simulationSettings, e6.flowData, r(t4.signal) ? t4.signal : new AbortController().signal);
    }
    getMultidimensionalSubsetVariables(e6) {
      const t4 = e6 ?? this.serviceRasterInfo.multidimensionalInfo;
      return v6(this.multidimensionalSubset, t4);
    }
    async _fetchService(e6) {
      await this._fetchServiceInfo(e6), this.rasterInfo || (this.rasterInfo = this.serviceRasterInfo);
      const t4 = this.sourceJSON, i6 = r(this.serviceRasterInfo) ? Promise.resolve(this.serviceRasterInfo) : m2(b4(this.parsedUrl), t4, { signal: e6, query: this._getQueryParams() }).then((e7) => (this._set("serviceRasterInfo", e7), this._set("multidimensionalInfo", e7.multidimensionalInfo), e7)), r4 = this._hasRenderingRule(this.renderingRule) ? this.generateRasterInfo(this.renderingRule, { signal: e6 }) : null, s9 = this._getRasterFunctionInfos();
      return Promise.all([i6, r4, s9]).then((e7) => {
        e7[1] ? this._set("rasterInfo", e7[1]) : this._set("rasterInfo", e7[0]), e7[2] && this._set("rasterFunctionInfos", e7[2]), this.renderer && !this._isSupportedRenderer(this.renderer) && (this._set("renderer", null), s.getLogger(this.declaredClass).warn("ArcGISImageService", "Switching to the default renderer. Renderer applied is not valid for this Imagery Layer")), this._set("renderer", this._configRenderer(this.renderer)), this.addHandles([l2(() => this.renderingRule, (e8) => {
          (this.renderer || this.symbolizer || this.popupEnabled && this.popupTemplate) && this.generateRasterInfo(e8).then((e9) => {
            e9 && (this.rasterInfo = e9);
          });
        })]);
        const { serviceRasterInfo: t5 } = this;
        r(t5.multidimensionalInfo) && this._updateMultidimensionalDefinition(t5);
      });
    }
    _combineMosaicRuleWithTimeExtent(e6, t4) {
      var _a;
      const i6 = this.timeInfo, { multidimensionalInfo: r4 } = this.serviceRasterInfo;
      if (t(e6) || t(r4) || t(t4) || t(i6 == null ? void 0 : i6.startField)) return e6;
      const { startField: s9 } = i6, n10 = r4.variables.some((e7) => e7.dimensions.some((e8) => e8.name === s9)) ? s9 : "StdTime";
      if (e6 = e6.clone(), "mosaic-dataset" === this.sourceType) return e6.multidimensionalDefinition = (_a = e6.multidimensionalDefinition) == null ? void 0 : _a.filter((e7) => e7.dimensionName !== n10), this._cleanupMultidimensionalDefinition(e6);
      e6.multidimensionalDefinition = e6.multidimensionalDefinition || [];
      const o5 = e6.multidimensionalDefinition.filter((e7) => e7.dimensionName === n10), a10 = r(t4.start) ? t4.start.getTime() : null, l9 = r(t4.end) ? t4.end.getTime() : null, u9 = null == a10 || null == l9 || a10 === l9, m7 = u9 ? [a10 || l9] : [[a10, l9]], d8 = this.version >= 10.8;
      if (o5.length) o5.forEach((e7) => {
        e7.dimensionName === n10 && (d8 ? (e7.dimensionName = null, e7.isSlice = false, e7.values = []) : (e7.isSlice = u9, e7.values = m7));
      });
      else if (!d8) {
        const t5 = e6.multidimensionalDefinition.filter((e7) => null != e7.variableName && null == e7.dimensionName);
        t5.length ? t5.forEach((e7) => {
          e7.dimensionName = n10, e7.isSlice = u9, e7.values = m7;
        }) : e6.multidimensionalDefinition.push(new p6({ variableName: "", dimensionName: n10, isSlice: u9, values: m7 }));
      }
      return this._cleanupMultidimensionalDefinition(e6);
    }
    _cleanupMultidimensionalDefinition(e6) {
      return t(e6) ? null : (e6.multidimensionalDefinition && (e6.multidimensionalDefinition = e6.multidimensionalDefinition.filter((e7) => !(!e7.variableName && !e7.dimensionName)), 0 === e6.multidimensionalDefinition.length && (e6.multidimensionalDefinition = null)), "mosaic-dataset" !== this.sourceType && null == e6.multidimensionalDefinition ? null : e6);
    }
    async _prepareForQuery(e6, t4) {
      if (!(await this._fetchCapabilities(t4 == null ? void 0 : t4.signal)).operations.supportsQuery) throw new s2("imagery-layer:query-rasters", "query operation is not supported on the input image service");
      return e6 = r(e6) ? v(x, e6) : this.createQuery(), t4 = this._getRequestOptions(t4), this.raster && (t4.query = { ...t4.query, raster: this.raster }), { query: e6, requestOptions: t4 };
    }
    async _initJobHandler() {
      if (null != this._rasterJobHandler.connectionPromise) return this._rasterJobHandler.connectionPromise;
      const e6 = new n6();
      this._rasterJobHandler.connectionPromise = e6.initialize().then(() => {
        this._rasterJobHandler.instance = e6;
      }, () => {
      }), await this._rasterJobHandler.connectionPromise;
    }
    _shutdownJobHandler() {
      this._rasterJobHandler.instance && this._rasterJobHandler.instance.destroy(), this._rasterJobHandler.instance = null, this._rasterJobHandler.connectionPromise = null, this._rasterJobHandler.refCount = 0, this._cachedRendererJson = null;
    }
    _isSupportedRenderer(e6) {
      const { rasterInfo: t4, renderingRule: i6 } = this;
      return "unique-value" === e6.type && this._hasRenderingRule(i6) && 1 === (t4 == null ? void 0 : t4.bandCount) && ["u8", "s8"].includes(t4.pixelType) || null != t4 && null != e6 && k(t4).includes(e6.type);
    }
    async _fetchCapabilities(e6) {
      return this.capabilities || await this._fetchServiceInfo(e6), this.capabilities;
    }
    async _fetchServiceInfo(e6) {
      var _a;
      let t4 = this.sourceJSON;
      if (!t4) {
        const { data: i6, ssl: r4 } = await U(b4(this.parsedUrl), { query: this._getQueryParams(), signal: e6 });
        t4 = i6, this.sourceJSON = t4, r4 && (this.url = this.url.replace(/^http:/i, "https:"));
      }
      if (((_a = t4.capabilities) == null ? void 0 : _a.toLowerCase().split(",").map((e7) => e7.trim()).indexOf("tilesonly")) > -1) throw new s2("imagery-layer:fetch-service-info", "use ImageryTileLayer to open tiles-only image services");
      this.read(t4, { origin: "service", url: this.parsedUrl });
    }
    _isMosaicDataset(e6) {
      var _a;
      return e6.serviceSourceType ? "esriImageServiceSourceTypeMosaicDataset" === e6.serviceSourceType : ((_a = e6.fields) == null ? void 0 : _a.length) > 0;
    }
    _isMosaicRuleSupported(e6) {
      var _a;
      if (!e6) return false;
      const t4 = this._isMosaicDataset(e6), i6 = e6.currentVersion >= 10.71 && e6.hasMultidimensions && !(((_a = e6.fields) == null ? void 0 : _a.length) > 1);
      return t4 || i6;
    }
    _isVectorFieldResampleFunction(e6) {
      if (t(e6)) return false;
      const { functionName: t4, functionArguments: i6 } = e6, r4 = "resample" === (t4 == null ? void 0 : t4.toLowerCase()), s9 = (i6 == null ? void 0 : i6.ResampleType) || (i6 == null ? void 0 : i6.resampleType);
      return r4 && (7 === s9 || 10 === s9);
    }
    _isPicture() {
      return !this.format || this.format.includes("jpg") || this.format.includes("png");
    }
    _configRenderer(e6) {
      var _a, _b;
      const t4 = this._isPicture(), { rasterInfo: i6 } = this;
      if (!t4 && !this.pixelFilter || this._isVectorDataSet()) {
        if (!this.bandIds && i6.bandCount >= 3) {
          const e7 = L2(i6);
          !e7 || 3 === i6.bandCount && 0 === e7[0] && 1 === e7[1] && 2 === e7[2] || (this.bandIds = e7);
        }
        e6 || (e6 = j3(i6, { bandIds: this.bandIds, variableName: this.renderingRule ? null : (_b = (_a = this.mosaicRule) == null ? void 0 : _a.multidimensionalDefinition) == null ? void 0 : _b[0].variableName }));
        const t5 = $(e6.toJSON());
        this.symbolizer ? (this.symbolizer.rendererJSON = t5, this.symbolizer.rasterInfo = i6) : this.symbolizer = new T3({ rendererJSON: t5, rasterInfo: i6 }), this.symbolizer.bind().success || (this.symbolizer = null);
      }
      return e6;
    }
    _clonePixelData(e6) {
      return null == e6 ? e6 : { extent: e6.extent && e6.extent.clone(), pixelBlock: r(e6.pixelBlock) ? e6.pixelBlock.clone() : null };
    }
    _getQueryParams(e6) {
      e6 && r(e6.renderingRule) && "string" != typeof e6.renderingRule && (e6.renderingRule = this._getRenderingRuleString(e6.renderingRule));
      const { raster: t4, viewId: i6 } = this;
      return { raster: t4, viewId: i6, f: "json", ...e6, ...this.customParameters };
    }
    _getRequestOptions(e6) {
      return { ...e6, query: { ...e6 == null ? void 0 : e6.query, ...this.customParameters } };
    }
    _decodePixelBlock(e6, t4, i6) {
      return this._rasterJobHandler.instance ? this._rasterJobHandler.instance.decode({ data: e6, options: t4 }) : S(e6, t4, i6);
    }
    async _getRasterFunctionInfos(e6) {
      var _a;
      const t4 = this.sourceJSON.rasterFunctionInfos;
      if (this.loaded) return t4;
      if (t4 && this.version >= 10.3) {
        if (1 === t4.length && "none" === t4[0].name.toLowerCase()) return t4;
        return (_a = (await U(b4(this.parsedUrl) + "/rasterFunctionInfos", { query: this._getQueryParams(), signal: e6 })).data) == null ? void 0 : _a.rasterFunctionInfos;
      }
      return null;
    }
    _canRequestImageElement(e6) {
      return !this.pixelFilter && (!e6 || e6.includes("png"));
    }
    async _requestArrayBuffer(e6) {
      const { imageProps: t4, requestAsImageElement: i6, returnImageBitmap: r4, signal: s9 } = e6;
      if (i6 && this._canRequestImageElement(t4.format)) {
        const i7 = `${b4(this.parsedUrl)}/exportImage`, { data: o5 } = await U(i7, { responseType: r4 ? "blob" : "image", query: this._getQueryParams({ f: "image", ...this.refreshParameters, ...e6.imageServiceParameters }), signal: s9 });
        if (o5 instanceof Blob) {
          return { imageBitmap: await e3(o5, i7), params: t4 };
        }
        return { imageOrCanvasElement: o5, params: t4 };
      }
      const a10 = this._initJobHandler(), l9 = U(b4(this.parsedUrl) + "/exportImage", { responseType: "array-buffer", query: this._getQueryParams({ f: "image", ...e6.imageServiceParameters }), signal: s9 }), u9 = (await Promise.all([l9, a10]))[0].data, p16 = t4.format || "jpgpng";
      let c16 = p16;
      if ("bsq" !== c16 && "bip" !== c16 && (c16 = j2(u9)), !c16) throw new s2("imagery-layer:fetch-image", "unsupported format signature " + String.fromCharCode.apply(null, new Uint8Array(u9)));
      const m7 = { signal: s9 }, d8 = "gif" === p16 || "bmp" === p16 || p16.includes("png") && ("png" === c16 || "jpg" === c16) ? S(u9, { useCanvas: true, ...t4 }, m7) : this._decodePixelBlock(u9, { width: t4.width, height: t4.height, planes: null, pixelType: null, noDataValue: null, format: p16 }, m7);
      return { pixelData: { pixelBlock: await d8, extent: t4.extent }, params: t4 };
    }
    _generateRasterInfo(e6, t4) {
      const i6 = { ...t4, query: this._getQueryParams() };
      return f5(b4(this.parsedUrl), e6, i6);
    }
    _isValidCustomizedMosaicRule(e6) {
      var _a;
      return e6 && JSON.stringify(e6.toJSON()) !== JSON.stringify((_a = this.defaultMosaicRule) == null ? void 0 : _a.toJSON());
    }
    _updateMultidimensionalDefinition(e6) {
      var _a;
      if (this._isValidCustomizedMosaicRule(this.mosaicRule)) return;
      let t4 = h(e6, { multidimensionalSubset: this.multidimensionalSubset });
      if (r(t4) && t4.length > 0) {
        this.mosaicRule = this.mosaicRule || new f8();
        const e7 = this.mosaicRule.multidimensionalDefinition;
        !this.sourceJSON.defaultVariableName && this.renderingRule && "none" !== ((_a = this.renderingRule.functionName) == null ? void 0 : _a.toLowerCase()) && t4.forEach((e8) => e8.variableName = ""), t4 = t4.filter(({ variableName: e8, dimensionName: t5 }) => e8 && "*" !== e8 || t5), !(e7 == null ? void 0 : e7.length) && t4.length && (this.mosaicRule.multidimensionalDefinition = t4);
      }
    }
    _processVisibleRastersResponse(e6, i6) {
      i6 = i6 || {};
      const r4 = e6.value, { templateRRFunctionNames: s9, showNoDataRecords: n10, returnDomainValues: o5, templateFields: a10 } = i6, l9 = e6.processedValues;
      let u9 = e6.catalogItems && e6.catalogItems.features, c16 = e6.properties && e6.properties.Values && e6.properties.Values.map((e7) => e7.replace(/ /gi, ", ")) || [];
      const m7 = this.objectIdField || "ObjectId", d8 = "string" == typeof r4 && r4.toLowerCase().includes("nodata"), h6 = [];
      if (r4 && !u9 && !d8) {
        const e7 = {};
        e7[m7] = 0;
        c16 = [r4], u9 = [new g(this.fullExtent, null, e7)];
      }
      if (!u9) return [];
      let f9, g4, y13;
      this._updateResponseFieldNames(u9, a10), d8 && !n10 && (u9 = []);
      for (let t4 = 0; t4 < u9.length; t4++) {
        if (f9 = u9[t4], null != r4) {
          if (g4 = c16[t4], y13 = this.renderingRule && l9 && l9.length > 0 && s9 && s9.length > 0 && s9.includes(this.renderingRule.functionName) ? l9[s9.indexOf(this.renderingRule.functionName)] : r4, "nodata" === g4.toLowerCase() && !n10) continue;
          const e7 = "Raster.ItemPixelValue", i7 = "Raster.ServicePixelValue";
          f9.attributes[e7] = g4, f9.attributes[i7] = y13, this._updateFeatureWithMagDirValues(f9, g4);
          const o6 = this.fields && this.fields.length > 0;
          let a11 = this.renderingRule && r(this.serviceRasterInfo.attributeTable) ? o6 ? g4 : r4 : y13;
          this.renderingRule || (a11 = o6 ? g4 : r4), this._updateFeatureWithRasterAttributeTableValues(f9, a11);
        }
        if (f9.sourceLayer = f9.layer = this, o5 && this._updateFeatureWithDomainValues(f9), s9 && l9 && s9.length === l9.length) for (let e7 = 0; e7 < s9.length; e7++) {
          const t5 = "Raster.ServicePixelValue." + s9[e7];
          f9.attributes[t5] = l9[e7];
        }
        h6.push(u9[t4]);
      }
      return h6;
    }
    _processMultidimensionalIntersection(e6, t4, i6) {
      const { multidimensionalSubset: r4 } = this;
      if (!r4) return { isOutSide: false, timeExtent: t4, mosaicRule: i6 = this._combineMosaicRuleWithTimeExtent(i6, t4) };
      if (r4) {
        const { isOutside: i7, intersection: s9 } = c6(r4, { geometry: e6, timeExtent: t4 });
        if (i7) return { isOutSide: true, timeExtent: null, mosaicRule: null };
        s9 && r(s9.timeExtent) && (t4 = s9.timeExtent);
      }
      if (i6 = this._combineMosaicRuleWithTimeExtent(i6, t4), r(i6) && i6.multidimensionalDefinition) {
        const { isOutside: e7 } = c6(r4, { multidimensionalDefinition: i6.multidimensionalDefinition });
        if (e7) return { isOutSide: true, timeExtent: null, mosaicRule: null };
      }
      return { isOutSide: false, timeExtent: t4, mosaicRule: i6 };
    }
    _updateFeatureWithRasterAttributeTableValues(e6, t4) {
      const i6 = this.rasterInfo.attributeTable || this.serviceRasterInfo.attributeTable;
      if (t(i6)) return;
      const { features: r4, fields: s9 } = i6, n10 = s9.map((e7) => e7.name).filter((e7) => "value" === e7.toLowerCase()), o5 = n10 && n10[0];
      if (!o5) return;
      const a10 = r4.filter((e7) => e7.attributes[o5] === (null != t4 ? parseInt(t4, 10) : null));
      a10 && a10[0] && s9.forEach((t5) => {
        const i7 = this._rasterAttributeTableFieldPrefix + t5.name;
        e6.attributes[i7] = a10[0].attributes[t5.name];
      });
    }
    _updateFeatureWithMagDirValues(e6, t4) {
      if (!this._isVectorDataSet()) return;
      const i6 = t4.split(/,\s*/).map((e7) => parseFloat(e7)), r4 = i6.map((e7) => [e7]), s9 = i6.map((e7) => ({ minValue: e7, maxValue: e7, noDataValue: null })), n10 = new m({ height: 1, width: 1, pixelType: "f32", pixels: r4, statistics: s9 });
      null != this.pixelFilter && this.pixelFilter({ pixelBlock: n10, extent: new w3(0, 0, 0, 0, this.spatialReference) });
      const o5 = "esriImageServiceDataTypeVector-MagDir" === this.serviceDataType ? [n10.pixels[0][0], n10.pixels[1][0]] : f4([n10.pixels[0][0], n10.pixels[1][0]]);
      e6.attributes["Raster.Magnitude"] = o5[0], e6.attributes["Raster.Direction"] = o5[1];
    }
    _updateFeatureWithDomainValues(e6) {
      const t4 = this.fields && this.fields.filter((e7) => e7.domain && "coded-value" === e7.domain.type);
      null != t4 && t4.forEach((t5) => {
        const i6 = e6.attributes[t5.name];
        if (null != i6) {
          const r4 = t5.domain.codedValues.find((e7) => e7.code === i6);
          r4 && (e6.attributes[t5.name] = r4.name);
        }
      });
    }
    _updateResponseFieldNames(e6, t4) {
      if (!t4 || t4.length < 1) return;
      const i6 = this.fieldsIndex;
      t(i6) || e6.forEach((e7) => {
        var _a;
        if (e7 && e7.attributes) for (const r4 of t4) {
          const t5 = (_a = i6.get(r4)) == null ? void 0 : _a.name;
          t5 && t5 !== r4 && (e7.attributes[r4] = e7.attributes[t5], delete e7.attributes[t5]);
        }
      });
    }
    _getRenderingRuleString(e6) {
      if (e6) {
        let t4 = e6.toJSON();
        return t4 = t4.rasterFunctionDefinition ?? t4, (t4.thumbnail || t4.thumbnailEx) && (t4.thumbnail = t4.thumbnailEx = null), JSON.stringify(t4);
      }
      return null;
    }
    _hasRenderingRule(e6) {
      return null != e6 && null != e6.functionName && "none" !== e6.functionName.toLowerCase();
    }
    _updateRenderingRulesFunctionName(e6, t4) {
      if (!e6 || e6.length < 1) return;
      if ("Raw" === e6) return e6.replace("Raw", "None");
      const i6 = e6.toLowerCase().replace(/ /gi, "_");
      return t4.has(i6) ? t4.get(i6) : e6;
    }
    _isRFTJson(e6) {
      return e6 && e6.name && e6.arguments && e6.function && e6.hasOwnProperty("functionType");
    }
    _isVectorDataSet() {
      return "esriImageServiceDataTypeVector-UV" === this.serviceDataType || "esriImageServiceDataTypeVector-MagDir" === this.serviceDataType;
    }
    _applyMosaicAndRenderingRules(e6) {
      const { raster: t4, mosaicRule: i6, renderingRule: r4 } = this;
      r4 && null == e6.renderingRule && (e6.renderingRule = r4), i6 && null == e6.mosaicRule && (e6.mosaicRule = i6), t4 && null == e6.raster && (e6.raster = t4);
    }
    _readCapabilities(e6) {
      const t4 = e6.capabilities ? e6.capabilities.toLowerCase().split(",").map((e7) => e7.trim()) : ["image", "catalog"], { currentVersion: i6, advancedQueryCapabilities: r4, maxRecordCount: s9 } = e6, n10 = t4.includes("image"), o5 = "esriImageServiceDataTypeElevation" === e6.serviceDataType, a10 = !!(e6.spatialReference || e6.extent && e6.extent.spatialReference), l9 = t4.includes("edit"), u9 = t4.includes("mensuration") && a10, p16 = null == e6.mensurationCapabilities ? [] : e6.mensurationCapabilities.toLowerCase().split(",").map((e7) => e7.trim()), c16 = u9 && p16.includes("basic");
      return { data: { supportsAttachment: false }, operations: { supportsComputeHistograms: n10, supportsExportImage: n10, supportsIdentify: n10, supportsMeasure: u9, supportsDownload: t4.includes("download"), supportsQuery: t4.includes("catalog") && e6.fields && e6.fields.length > 0, supportsGetSamples: i6 >= 10.2 && n10, supportsProject: i6 >= 10.3 && n10, supportsComputeStatisticsHistograms: i6 >= 10.4 && n10, supportsQueryBoundary: i6 >= 10.6 && n10, supportsCalculateVolume: i6 >= 10.7 && o5, supportsComputePixelLocation: i6 >= 10.7 && t4.includes("catalog"), supportsComputeAngles: i6 >= 10.91, supportsAdd: l9, supportsDelete: l9, supportsEditing: l9, supportsUpdate: l9, supportsCalculate: false, supportsTruncate: false, supportsValidateSql: false, supportsChangeTracking: false, supportsQueryAttachments: false, supportsResizeAttachments: false, supportsSync: false, supportsExceedsLimitStatistics: false, supportsQueryAnalytics: false, supportsQueryTopFeatures: false }, query: { maxRecordCount: s9, maxRecordCountFactor: void 0, supportsStatistics: !!(r4 == null ? void 0 : r4.supportsStatistics), supportsOrderBy: !!(r4 == null ? void 0 : r4.supportsOrderBy), supportsDistinct: !!(r4 == null ? void 0 : r4.supportsDistinct), supportsPagination: !!(r4 == null ? void 0 : r4.supportsPagination), supportsStandardizedQueriesOnly: !!(r4 == null ? void 0 : r4.useStandardizedQueries), supportsPercentileStatistics: !!(r4 == null ? void 0 : r4.supportsPercentileStatistics), supportsCentroid: !!(r4 == null ? void 0 : r4.supportsReturningGeometryCentroid), supportsDistance: !!(r4 == null ? void 0 : r4.supportsQueryWithDistance), supportsExtent: !!(r4 == null ? void 0 : r4.supportsReturningQueryExtent), supportsGeometryProperties: !!(r4 == null ? void 0 : r4.supportsReturningGeometryProperties), supportsHavingClause: !!(r4 == null ? void 0 : r4.supportsHavingClause), supportsQuantization: false, supportsQuantizationEditMode: false, supportsQueryGeometry: false, supportsResultType: false, supportsMaxRecordCountFactor: false, supportsSqlExpression: false, supportsTopFeaturesQuery: false, supportsQueryByOthers: false, supportsHistoricMoment: false, supportsFormatPBF: false, supportsDisjointSpatialRelationship: false, supportsCacheHint: false, supportsSpatialAggregationStatistics: false, supportedSpatialAggregationStatistics: { envelope: false, centroid: false, convexHull: false }, supportsDefaultSpatialReference: !!(r4 == null ? void 0 : r4.supportsDefaultSR), supportsFullTextSearch: false, supportsCompactGeometry: false, standardMaxRecordCount: void 0, tileMaxRecordCount: void 0 }, mensuration: { supportsDistanceAndAngle: c16, supportsAreaAndPerimeter: c16, supportsPointOrCentroid: c16, supportsHeightFromBaseAndTop: u9 && p16.includes("base-top height"), supportsHeightFromBaseAndTopShadow: u9 && p16.includes("base-top shadow height"), supportsHeightFromTopAndTopShadow: u9 && p16.includes("top-top shadow height"), supports3D: u9 && p16.includes("3d") } };
    }
  };
  function b4(e6) {
    return (e6 == null ? void 0 : e6.path) ?? "";
  }
  return e([y()], g3.prototype, "_functionRasterInfos", void 0), e([y()], g3.prototype, "_rasterJobHandler", void 0), e([y()], g3.prototype, "_cachedRendererJson", void 0), e([y({ readOnly: true })], g3.prototype, "_serviceSupportsMosaicRule", void 0), e([o("_serviceSupportsMosaicRule", ["currentVersion", "fields"])], g3.prototype, "readServiceSupportsMosaicRule", null), e([y()], g3.prototype, "_rasterAttributeTableFieldPrefix", void 0), e([y({ readOnly: true })], g3.prototype, "_rasterFunctionNamesIndex", null), e([y()], g3.prototype, "adjustAspectRatio", void 0), e([y({ type: [T], json: { write: true } })], g3.prototype, "bandIds", void 0), e([o("bandIds")], g3.prototype, "readBandIds", null), e([y({ readOnly: true, json: { read: false } })], g3.prototype, "capabilities", void 0), e([o("service", "capabilities", ["capabilities", "currentVersion", "serviceDataType"])], g3.prototype, "readCapabilities", null), e([y({ type: Number })], g3.prototype, "compressionQuality", void 0), e([r2("compressionQuality")], g3.prototype, "writeCompressionQuality", null), e([y({ type: Number })], g3.prototype, "compressionTolerance", void 0), e([r2("compressionTolerance")], g3.prototype, "writeCompressionTolerance", null), e([y({ json: { read: { source: "copyrightText" } } })], g3.prototype, "copyright", void 0), e([y({ readOnly: true, dependsOn: ["_serviceSupportsMosaicRule"] })], g3.prototype, "defaultMosaicRule", void 0), e([o("defaultMosaicRule", ["defaultMosaicMethod"])], g3.prototype, "readDefaultMosaicRule", null), e([y({ type: String, json: { name: "layerDefinition.definitionExpression", write: { enabled: true, allowNull: true } } })], g3.prototype, "definitionExpression", void 0), e([y({ readOnly: true, constructOnly: true })], g3.prototype, "exportImageServiceParameters", void 0), e([y()], g3.prototype, "rasterInfo", void 0), e([y({ readOnly: true, type: [y2] })], g3.prototype, "fields", void 0), e([y({ readOnly: true })], g3.prototype, "fieldsIndex", null), e([y({ type: ["png", "png8", "png24", "png32", "jpg", "bmp", "gif", "jpgpng", "lerc", "tiff"], json: { write: true } })], g3.prototype, "format", null), e([o("service", "format", ["serviceDataType"])], g3.prototype, "readFormat", null), e([y({ type: w3 })], g3.prototype, "fullExtent", void 0), e([y({ readOnly: true })], g3.prototype, "hasMultidimensions", void 0), e([y({ json: { read: { source: "maxImageHeight" } } })], g3.prototype, "imageMaxHeight", void 0), e([y({ json: { read: { source: "maxImageWidth" } } })], g3.prototype, "imageMaxWidth", void 0), e([y({ type: String, json: { type: o4.jsonValues, read: o4.read, write: o4.write } })], g3.prototype, "interpolation", void 0), e([y()], g3.prototype, "minScale", void 0), e([o("service", "minScale")], g3.prototype, "readMinScale", null), e([y()], g3.prototype, "maxScale", void 0), e([o("service", "maxScale")], g3.prototype, "readMaxScale", null), e([y({ type: f8 })], g3.prototype, "mosaicRule", null), e([o("mosaicRule", ["mosaicRule", "defaultMosaicMethod"])], g3.prototype, "readMosaicRule", null), e([r2("mosaicRule")], g3.prototype, "writeMosaicRule", null), e([y()], g3.prototype, "multidimensionalInfo", void 0), e([y({ type: c5, json: { write: true } })], g3.prototype, "multidimensionalSubset", void 0), e([y({ json: { type: T } })], g3.prototype, "noData", void 0), e([r2("noData")], g3.prototype, "writeNoData", null), e([y({ type: String, json: { type: i2.jsonValues, read: i2.read, write: i2.write } })], g3.prototype, "noDataInterpretation", void 0), e([y({ type: String, readOnly: true, json: { read: { source: ["fields"] } } })], g3.prototype, "objectIdField", void 0), e([o("objectIdField")], g3.prototype, "readObjectIdField", null), e([y({})], g3.prototype, "geometryType", void 0), e([y({})], g3.prototype, "typeIdField", void 0), e([y({})], g3.prototype, "types", void 0), e([y({ readOnly: true })], g3.prototype, "parsedUrl", null), e([y({ type: Function })], g3.prototype, "pixelFilter", void 0), e([y()], g3.prototype, "raster", void 0), e([y({ readOnly: true })], g3.prototype, "sourceType", void 0), e([o("sourceType", ["serviceSourceType", "fields"])], g3.prototype, "readSourceType", null), e([y()], g3.prototype, "viewId", void 0), e([y({ types: l3, json: { name: "layerDefinition.drawingInfo.renderer", origins: { "web-scene": { types: d, name: "layerDefinition.drawingInfo.renderer", write: { overridePolicy: (e6) => ({ enabled: e6 && "vector-field" !== e6.type && "flow" !== e6.type }) } } } } })], g3.prototype, "renderer", null), e([o("renderer")], g3.prototype, "readRenderer", null), e([r2("renderer")], g3.prototype, "writeRenderer", null), e([y()], g3.prototype, "symbolizer", void 0), e([y(u2)], g3.prototype, "opacity", void 0), e([y({ readOnly: true })], g3.prototype, "rasterFields", null), e([y({ constructOnly: true })], g3.prototype, "rasterFunctionInfos", void 0), e([y({ type: w4 })], g3.prototype, "renderingRule", null), e([o("renderingRule", ["renderingRule", "rasterFunctionInfos"])], g3.prototype, "readRenderingRule", null), e([r2("renderingRule")], g3.prototype, "writeRenderingRule", null), e([y()], g3.prototype, "serviceDataType", void 0), e([y({ readOnly: true, type: f })], g3.prototype, "spatialReference", void 0), e([o("spatialReference", ["spatialReference", "extent"])], g3.prototype, "readSpatialReference", null), e([y({ json: { type: je.jsonValues } })], g3.prototype, "pixelType", void 0), e([o("pixelType")], g3.prototype, "readPixelType", null), e([r2("pixelType")], g3.prototype, "writePixelType", null), e([y({ constructOnly: true, type: u3 })], g3.prototype, "serviceRasterInfo", void 0), e([y()], g3.prototype, "sourceJSON", void 0), e([y(f3)], g3.prototype, "url", void 0), e([y({ readOnly: true })], g3.prototype, "version", void 0), e([o("version", ["currentVersion", "fields", "timeInfo"])], g3.prototype, "readVersion", null), g3 = e([a2("esri.layers.mixins.ArcGISImageService")], g3), g3;
};

// node_modules/@arcgis/core/layers/ImageryLayer.js
var L3 = class extends n2(a3(t2(c3(_(Ne(p3(o3(p4(O(b)))))))))) {
  constructor(...e6) {
    super(...e6), this.legendEnabled = true, this.isReference = null, this.operationalLayerType = "ArcGISImageServiceLayer", this.popupEnabled = true, this.popupTemplate = null, this.type = "imagery";
  }
  normalizeCtorArgs(e6, r4) {
    return "string" == typeof e6 ? { url: e6, ...r4 } : e6;
  }
  load(e6) {
    const r4 = r(e6) ? e6.signal : null;
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Image Service"] }, e6).catch(w).then(() => this._fetchService(r4))), Promise.resolve(this);
  }
  writeOperationalLayerType(e6, r4, t4) {
    var _a;
    const o5 = "vector-field" === ((_a = this.renderer) == null ? void 0 : _a.type);
    r4[t4] = o5 ? "ArcGISImageServiceVectorLayer" : "ArcGISImageServiceLayer";
  }
  get defaultPopupTemplate() {
    return this.createPopupTemplate();
  }
  createPopupTemplate(e6) {
    const r4 = this.rasterFields, t4 = this.title, o5 = /* @__PURE__ */ new Set();
    let i6 = false, s9 = false;
    this.capabilities && (i6 = this.capabilities.operations.supportsQuery && this.fields && this.fields.length > 0, s9 = "esriImageServiceDataTypeVector-UV" === this.serviceDataType || "esriImageServiceDataTypeVector-MagDir" === this.serviceDataType);
    const a9 = /* @__PURE__ */ new Set();
    i6 && a9.add("raster.itempixelvalue");
    for (const p16 of r4) {
      const e7 = p16.name.toLowerCase();
      a9.has(e7) || e7.includes("raster.servicepixelvalue.") || o5.add(p16.name);
    }
    return s9 && o5.add("raster.magnitude").add("raster.direction"), p5({ fields: r4, title: t4 }, { ...e6, visibleFieldNames: o5 });
  }
  queryFeatures(e6, r4) {
    return this.queryRasters(e6, r4).then((e7) => {
      if (e7 == null ? void 0 : e7.features) for (const r5 of e7.features) r5.layer = r5.sourceLayer = this;
      return e7;
    });
  }
  queryFeatureCount(e6, r4) {
    return this.queryRasterCount(e6, r4);
  }
  redraw() {
    this.emit("redraw");
  }
  serviceSupportsSpatialReference(e6) {
    return e4(this, e6);
  }
};
e([y(c2)], L3.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide"] })], L3.prototype, "listMode", void 0), e([y({ type: Boolean, json: { read: false, write: { enabled: true, overridePolicy: () => ({ enabled: false }) } } })], L3.prototype, "isReference", void 0), e([y({ type: ["ArcGISImageServiceLayer"], json: { origins: { "web-map": { type: ["ArcGISImageServiceLayer", "ArcGISImageServiceVectorLayer"], read: false, write: { target: "layerType", ignoreOrigin: true } } } } })], L3.prototype, "operationalLayerType", void 0), e([r2("web-map", "operationalLayerType")], L3.prototype, "writeOperationalLayerType", null), e([y(p2)], L3.prototype, "popupEnabled", void 0), e([y({ type: k2, json: { read: { source: "popupInfo" }, write: { target: "popupInfo" } } })], L3.prototype, "popupTemplate", void 0), e([y({ readOnly: true })], L3.prototype, "defaultPopupTemplate", null), e([y({ readOnly: true, json: { read: false } })], L3.prototype, "type", void 0), L3 = e([a2("esri.layers.ImageryLayer")], L3);
var T5 = L3;
export {
  T5 as default
};
//# sourceMappingURL=ImageryLayer-FHOOLL5U.js.map
