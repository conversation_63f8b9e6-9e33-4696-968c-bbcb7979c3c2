import {
  f as f2,
  h,
  o,
  r,
  s as s5,
  t as t2,
  w
} from "./chunk-MNWHGD3K.js";
import {
  a
} from "./chunk-FZ7BG3VX.js";
import {
  l
} from "./chunk-5GX2JMCX.js";
import {
  a as a2,
  c,
  e,
  s as s4
} from "./chunk-WXFAAYJL.js";
import {
  F
} from "./chunk-U4SVMKOQ.js";
import {
  t
} from "./chunk-JEDE7445.js";
import {
  C,
  d,
  f,
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s3
} from "./chunk-4RZONHOY.js";
import {
  s as s2
} from "./chunk-RV4I37UI.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  p
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/workers/loaderConfig.js
var a3 = {};
function e2(s7) {
  const e3 = { async: s7.async, isDebug: s7.isDebug, locale: s7.locale, baseUrl: s7.baseUrl, has: { ...s7.has }, map: { ...s7.map }, packages: s7.packages && s7.packages.concat() || [], paths: { ...s7.paths } };
  return s7.hasOwnProperty("async") || (e3.async = true), s7.hasOwnProperty("isDebug") || (e3.isDebug = false), s7.baseUrl || (e3.baseUrl = a3.baseUrl), e3;
}

// node_modules/@arcgis/core/core/workers/WorkerFallback.js
var n = class {
  constructor() {
    const e3 = document.createDocumentFragment();
    ["addEventListener", "dispatchEvent", "removeEventListener"].forEach((s7) => {
      this[s7] = (...r2) => e3[s7](...r2);
    });
  }
};
var a4 = class {
  constructor() {
    this._dispatcher = new n(), this._workerPostMessage({ type: t2.HANDSHAKE });
  }
  terminate() {
  }
  get onmessage() {
    return this._onmessageHandler;
  }
  set onmessage(e3) {
    this._onmessageHandler && this.removeEventListener("message", this._onmessageHandler), this._onmessageHandler = e3, e3 && this.addEventListener("message", e3);
  }
  get onmessageerror() {
    return this._onmessageerrorHandler;
  }
  set onmessageerror(e3) {
    this._onmessageerrorHandler && this.removeEventListener("messageerror", this._onmessageerrorHandler), this._onmessageerrorHandler = e3, e3 && this.addEventListener("messageerror", e3);
  }
  get onerror() {
    return this._onerrorHandler;
  }
  set onerror(e3) {
    this._onerrorHandler && this.removeEventListener("error", this._onerrorHandler), this._onerrorHandler = e3, e3 && this.addEventListener("error", e3);
  }
  postMessage(s7) {
    t(() => {
      this._workerMessageHandler(new MessageEvent("message", { data: s7 }));
    });
  }
  dispatchEvent(e3) {
    return this._dispatcher.dispatchEvent(e3);
  }
  addEventListener(e3, s7, r2) {
    this._dispatcher.addEventListener(e3, s7, r2);
  }
  removeEventListener(e3, s7, r2) {
    this._dispatcher.removeEventListener(e3, s7, r2);
  }
  _workerPostMessage(s7) {
    t(() => {
      this.dispatchEvent(new MessageEvent("message", { data: s7 }));
    });
  }
  async _workerMessageHandler(e3) {
    const n2 = f2(e3);
    if (n2 && n2.type === t2.OPEN) {
      const { modulePath: e4, jobId: t3 } = n2;
      let a6 = await w.loadWorker(e4);
      a6 || (a6 = await import(
        /* @vite-ignore */
        /* webpackIgnore: true */
        e4
      ));
      const o2 = w.connect(a6);
      this._workerPostMessage({ type: t2.OPENED, jobId: t3, data: o2 });
    }
  }
};

// node_modules/@arcgis/core/core/workers/workerFactory.js
var u = s2.getLogger("esri.core.workers.workerFactory");
var { HANDSHAKE: m } = t2;
var p2 = 'let globalId=0;const outgoing=new Map,configuration=JSON.parse("{CONFIGURATION}");self.esriConfig=configuration.esriConfig;const workerPath=self.esriConfig.workers.workerPath,HANDSHAKE=0,OPEN=1,OPENED=2,RESPONSE=3,INVOKE=4,ABORT=5;function createAbortError(){const e=new Error("Aborted");return e.name="AbortError",e}function receiveMessage(e){return e&&e.data?"string"==typeof e.data?JSON.parse(e.data):e.data:null}function invokeStaticMessage(e,o,r){const t=r&&r.signal,n=globalId++;return new Promise(((r,i)=>{if(t){if(t.aborted)return i(createAbortError());t.addEventListener("abort",(()=>{outgoing.get(n)&&(outgoing.delete(n),self.postMessage({type:5,jobId:n}),i(createAbortError()))}))}outgoing.set(n,{resolve:r,reject:i}),self.postMessage({type:4,jobId:n,methodName:e,abortable:null!=t,data:o})}))}let workerRevisionChecked=!1;function checkWorkerRevision(e){if(!workerRevisionChecked&&e.kernelInfo){workerRevisionChecked=!0;const{revision:o,version:r}=configuration.kernelInfo,{revision:t,version:n}=e.kernelInfo;esriConfig.assetsPath!==esriConfig.defaultAssetsPath&&o!==t&&console.warn(`Version mismatch detected between ArcGIS API for JavaScript modules and assets. For more information visit https://bit.ly/3QnsuSo.\\nModules version: ${r}\\nAssets version: ${n}`)}}function messageHandler(e){const o=receiveMessage(e);if(!o)return;const r=o.jobId;switch(o.type){case 1:let n;function t(e){const o=n.connect(e);self.postMessage({type:2,jobId:r,data:o},[o])}"function"==typeof define&&define.amd?require([workerPath],(e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||new Promise((e=>{require([o.modulePath],e)})))).then(t)})):"System"in self&&"function"==typeof System.import?System.import(workerPath).then((e=>(n=e.default,checkWorkerRevision(n),n.loadWorker(o.modulePath)))).then((e=>e||System.import(o.modulePath))).then(t):esriConfig.workers.useDynamicImport?import(workerPath).then((e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||import(o.modulePath))).then(t)})):(self.RemoteClient||importScripts(workerPath),n=self.RemoteClient.default||self.RemoteClient,checkWorkerRevision(n),n.loadWorker(o.modulePath).then(t));break;case 3:if(outgoing.has(r)){const i=outgoing.get(r);outgoing.delete(r),o.error?i.reject(JSON.parse(o.error)):i.resolve(o.data)}}}self.dojoConfig=configuration.loaderConfig,esriConfig.workers.loaderUrl&&(self.importScripts(esriConfig.workers.loaderUrl),"function"==typeof require&&"function"==typeof require.config&&require.config(configuration.loaderConfig)),self.addEventListener("message",messageHandler),self.postMessage({type:0});';
var g;
var k;
var h2 = "Failed to create Worker. Fallback to execute module in main thread";
async function w2() {
  if (!has("esri-workers") || (has("mozilla"), 0)) return y(new a4());
  if (!g && !k) try {
    const e4 = p2.split('"{CONFIGURATION}"').join(`'${b()}'`);
    g = URL.createObjectURL(new Blob([e4], { type: "text/javascript" }));
  } catch (r2) {
    k = r2 || {};
  }
  let e3;
  if (g) try {
    e3 = new Worker(g, { name: "esri-worker-" + v++ });
  } catch (r2) {
    u.warn(h2, k), e3 = new a4();
  }
  else u.warn(h2, k), e3 = new a4();
  return y(e3);
}
async function y(e3) {
  return new Promise((r2) => {
    function o2(s7) {
      const n2 = f2(s7);
      n2 && n2.type === m && (e3.removeEventListener("message", o2), e3.removeEventListener("error", t3), r2(e3));
    }
    function t3(r3) {
      r3.preventDefault(), e3.removeEventListener("message", o2), e3.removeEventListener("error", t3), u.warn("Failed to create Worker. Fallback to execute module in main thread", r3), (e3 = new a4()).addEventListener("message", o2), e3.addEventListener("error", t3);
    }
    e3.addEventListener("message", o2), e3.addEventListener("error", t3);
  });
}
function b() {
  let t3;
  if (null != s.default) {
    const e3 = { ...s };
    delete e3.default, t3 = JSON.parse(JSON.stringify(e3));
  } else t3 = JSON.parse(JSON.stringify(s));
  t3.assetsPath = F(t3.assetsPath), t3.defaultAssetsPath = t3.defaultAssetsPath ? F(t3.defaultAssetsPath) : void 0, t3.request.interceptors = [], t3.log.interceptors = [], t3.locale = l(), t3.has = { "esri-csp-restrictions": has("esri-csp-restrictions"), "esri-2d-debug": false, "esri-2d-update-debug": has("esri-2d-update-debug"), "featurelayer-pbf": has("featurelayer-pbf"), "featurelayer-simplify-thresholds": has("featurelayer-simplify-thresholds"), "featurelayer-simplify-payload-size-factors": has("featurelayer-simplify-payload-size-factors"), "featurelayer-simplify-mobile-factor": has("featurelayer-simplify-mobile-factor"), "esri-atomics": has("esri-atomics"), "esri-shared-array-buffer": has("esri-shared-array-buffer"), "esri-tiles-debug": has("esri-tiles-debug"), "esri-workers-arraybuffer-transfer": has("esri-workers-arraybuffer-transfer"), "feature-polyline-generalization-factor": has("feature-polyline-generalization-factor"), "host-webworker": 1, "polylabel-placement-enabled": has("polylabel-placement-enabled") }, t3.workers.loaderUrl && (t3.workers.loaderUrl = F(t3.workers.loaderUrl)), t3.workers.workerPath ? t3.workers.workerPath = F(t3.workers.workerPath) : t3.workers.workerPath = F(a("esri/core/workers/RemoteClient.js")), t3.workers.useDynamicImport = false;
  const i2 = s.workers.loaderConfig, a6 = e2({ baseUrl: i2 == null ? void 0 : i2.baseUrl, locale: l(), has: { "csp-restrictions": 1, "dojo-test-sniff": 0, "host-webworker": 1, ...i2 == null ? void 0 : i2.has }, map: { ...i2 == null ? void 0 : i2.map }, paths: { ...i2 == null ? void 0 : i2.paths }, packages: (i2 == null ? void 0 : i2.packages) || [] }), l2 = { version: a2, buildDate: c, revision: e };
  return JSON.stringify({ esriConfig: t3, loaderConfig: a6, kernelInfo: l2 });
}
var v = 0;

// node_modules/@arcgis/core/core/workers/WorkerOwner.js
var { ABORT: b2, INVOKE: m2, OPEN: _, OPENED: g2, RESPONSE: u2 } = t2;
var j2 = class _j {
  static async create(e3) {
    const t3 = await w2();
    return new _j(t3, e3);
  }
  constructor(e3, t3) {
    this._outJobs = /* @__PURE__ */ new Map(), this._inJobs = /* @__PURE__ */ new Map(), this.worker = e3, this.id = t3, e3.addEventListener("message", this._onMessage.bind(this)), e3.addEventListener("error", (e4) => {
      e4.preventDefault(), s2.getLogger("esri.core.workers.WorkerOwner").error(e4);
    });
  }
  terminate() {
    this.worker.terminate();
  }
  async open(e3, t3 = {}) {
    const { signal: o2 } = t3, s7 = r();
    return new Promise((t4, n2) => {
      const a6 = { resolve: t4, reject: n2, abortHandle: d(o2, () => {
        this._outJobs.delete(s7), this._post({ type: b2, jobId: s7 });
      }) };
      this._outJobs.set(s7, a6), this._post({ type: _, jobId: s7, modulePath: e3 });
    });
  }
  _onMessage(e3) {
    const t3 = f2(e3);
    if (t3) switch (t3.type) {
      case g2:
        this._onOpenedMessage(t3);
        break;
      case u2:
        this._onResponseMessage(t3);
        break;
      case b2:
        this._onAbortMessage(t3);
        break;
      case m2:
        this._onInvokeMessage(t3);
    }
  }
  _onAbortMessage(e3) {
    const t3 = this._inJobs, o2 = e3.jobId, s7 = t3.get(o2);
    s7 && (s7.controller && s7.controller.abort(), t3.delete(o2));
  }
  _onInvokeMessage(t3) {
    const { methodName: o2, jobId: s7, data: r2, abortable: i2 } = t3, l2 = i2 ? new AbortController() : null, h3 = this._inJobs, c3 = s4[o2];
    let p4;
    try {
      if ("function" != typeof c3) throw new TypeError(`${o2} is not a function`);
      p4 = c3.call(null, r2, { signal: l2 ? l2.signal : null });
    } catch (b3) {
      return void this._post({ type: u2, jobId: s7, error: s5(b3) });
    }
    C(p4) ? (h3.set(s7, { controller: l2, promise: p4 }), p4.then((e3) => {
      h3.has(s7) && (h3.delete(s7), this._post({ type: u2, jobId: s7 }, e3));
    }, (e3) => {
      h3.has(s7) && (h3.delete(s7), e3 || (e3 = { message: "Error encountered at method" + o2 }), j(e3) || this._post({ type: u2, jobId: s7, error: s5(e3 || { message: `Error encountered at method ${o2}` }) }));
    })) : this._post({ type: u2, jobId: s7 }, p4);
  }
  _onOpenedMessage(e3) {
    const { jobId: t3, data: o2 } = e3, r2 = this._outJobs.get(t3);
    r2 && (this._outJobs.delete(t3), p(r2.abortHandle), r2.resolve(o2));
  }
  _onResponseMessage(e3) {
    const { jobId: o2, error: r2, data: n2 } = e3, a6 = this._outJobs.get(o2);
    a6 && (this._outJobs.delete(o2), p(a6.abortHandle), r2 ? a6.reject(s3.fromJSON(JSON.parse(r2))) : a6.resolve(n2));
  }
  _post(e3, t3, o2) {
    return o(this.worker, e3, t3, o2);
  }
};

// node_modules/@arcgis/core/core/workers/workers.js
var i = has("esri-workers-debug") ? 1 : has("esri-mobile") ? Math.min(navigator.hardwareConcurrency - 1, 3) : has("host-browser") ? navigator.hardwareConcurrency - 1 : 0;
i || (i = has("safari") && has("mac") ? 7 : 2);
var a5 = 0;
var s6 = [];
function c2() {
  d2();
}
async function m3(r2, t3) {
  const n2 = new h();
  return await n2.open(r2, t3), n2;
}
async function u3(e3, o2 = {}) {
  if ("string" != typeof e3) throw new s3("workers:undefined-module", "modulePath is missing");
  let c3 = o2.strategy || "distributed";
  if (has("host-webworker") && !has("esri-workers") && (c3 = "local"), "local" === c3) {
    let r2 = await w.loadWorker(e3);
    r2 || (r2 = await import(
      /* @vite-ignore */
      /* webpackIgnore: true */
      e3
    )), f(o2.signal);
    const i2 = o2.client || r2;
    return m3([w.connect(r2)], { ...o2, client: i2 });
  }
  if (await d2(), f(o2.signal), "dedicated" === c3) {
    const r2 = a5++ % i;
    return m3([await s6[r2].open(e3, o2)], o2);
  }
  if (o2.maxNumWorkers && o2.maxNumWorkers > 0) {
    const r2 = Math.min(o2.maxNumWorkers, i);
    if (r2 < i) {
      const t3 = new Array(r2);
      for (let n2 = 0; n2 < r2; ++n2) {
        const r3 = a5++ % i;
        t3[n2] = s6[r3].open(e3, o2);
      }
      return m3(t3, o2);
    }
  }
  return m3(s6.map((r2) => r2.open(e3, o2)), o2);
}
var w3;
var p3 = null;
async function d2() {
  if (p3) return p3;
  w3 = new AbortController();
  const r2 = [];
  for (let t3 = 0; t3 < i; t3++) {
    const e3 = j2.create(t3).then((r3) => (s6[t3] = r3, r3));
    r2.push(e3);
  }
  return p3 = Promise.all(r2), p3;
}

export {
  c2 as c,
  u3 as u
};
//# sourceMappingURL=chunk-IKGI4J4I.js.map
