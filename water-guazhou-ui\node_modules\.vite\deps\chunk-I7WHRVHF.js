import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/aaBoundingRect.js
function i(n) {
  return n;
}
function u(n = L) {
  return i([n[0], n[1], n[2], n[3]]);
}
function e(n) {
  return i([n[0], n[1], n[2], n[3]]);
}
function a2(n, t2) {
  return n !== t2 && (n[0] = t2[0], n[1] = t2[1], n[2] = t2[2], n[3] = t2[3]), n;
}
function o(n, t2, r, i2, e2 = u()) {
  return e2[0] = n, e2[1] = t2, e2[2] = r, e2[3] = i2, e2;
}
function c(n, t2 = u()) {
  return t2[0] = n.xmin, t2[1] = n.ymin, t2[2] = n.xmax, t2[3] = n.ymax, t2;
}
function f(n, t2) {
  return new w({ xmin: n[0], ymin: n[1], xmax: n[2], ymax: n[3], spatialReference: t2 });
}
function m(n, t2) {
  t2[0] < n[0] && (n[0] = t2[0]), t2[0] > n[2] && (n[2] = t2[0]), t2[1] < n[1] && (n[1] = t2[1]), t2[1] > n[3] && (n[3] = t2[1]);
}
function h(n, r, i2) {
  if (t(r)) a2(i2, n);
  else if ("length" in r) G(r) ? (i2[0] = Math.min(n[0], r[0]), i2[1] = Math.min(n[1], r[1]), i2[2] = Math.max(n[2], r[2]), i2[3] = Math.max(n[3], r[3])) : 2 !== r.length && 3 !== r.length || (i2[0] = Math.min(n[0], r[0]), i2[1] = Math.min(n[1], r[1]), i2[2] = Math.max(n[2], r[0]), i2[3] = Math.max(n[3], r[1]));
  else switch (r.type) {
    case "extent":
      i2[0] = Math.min(n[0], r.xmin), i2[1] = Math.min(n[1], r.ymin), i2[2] = Math.max(n[2], r.xmax), i2[3] = Math.max(n[3], r.ymax);
      break;
    case "point":
      i2[0] = Math.min(n[0], r.x), i2[1] = Math.min(n[1], r.y), i2[2] = Math.max(n[2], r.x), i2[3] = Math.max(n[3], r.y);
  }
}
function x(n, t2, r = n) {
  const i2 = t2.length;
  let u2 = n[0], e2 = n[1], a3 = n[2], o2 = n[3];
  for (let c2 = 0; c2 < i2; c2++) {
    const n2 = t2[c2];
    u2 = Math.min(u2, n2[0]), e2 = Math.min(e2, n2[1]), a3 = Math.max(a3, n2[0]), o2 = Math.max(o2, n2[1]);
  }
  return r[0] = u2, r[1] = e2, r[2] = a3, r[3] = o2, r;
}
function M(n) {
  for (let t2 = 0; t2 < 4; t2++) if (!isFinite(n[t2])) return false;
  return true;
}
function s(n) {
  return t(n) || n[0] >= n[2] ? 0 : n[2] - n[0];
}
function l(n) {
  return n[1] >= n[3] ? 0 : n[3] - n[1];
}
function y(n) {
  return s(n) * l(n);
}
function p(n, t2 = [0, 0]) {
  return t2[0] = (n[0] + n[2]) / 2, t2[1] = (n[1] + n[3]) / 2, t2;
}
function b(n, t2) {
  return w2(n, t2[0], t2[1]);
}
function F(n, t2) {
  return w2(n, t2.x, t2.y);
}
function w2(n, t2, r) {
  return t2 >= n[0] && r >= n[1] && t2 <= n[2] && r <= n[3];
}
function q(n, t2, r) {
  return t2[0] >= n[0] - r && t2[1] >= n[1] - r && t2[0] <= n[2] + r && t2[1] <= n[3] + r;
}
function E(n, t2) {
  return Math.max(t2[0], n[0]) <= Math.min(t2[2], n[2]) && Math.max(t2[1], n[1]) <= Math.min(t2[3], n[3]);
}
function R(n, t2) {
  return t2[0] >= n[0] && t2[2] <= n[2] && t2[1] >= n[1] && t2[3] <= n[3];
}
function z(n, t2, r, i2 = n) {
  return i2[0] = n[0] + t2, i2[1] = n[1] + r, i2[2] = n[2] + t2, i2[3] = n[3] + r, i2;
}
function D(n) {
  return n ? a2(n, K) : u(K);
}
function G(n) {
  return null != n && 4 === n.length;
}
function I(n, t2) {
  return G(n) && G(t2) ? n[0] === t2[0] && n[1] === t2[1] && n[2] === t2[2] && n[3] === t2[3] : n === t2;
}
var J = i([-1 / 0, -1 / 0, 1 / 0, 1 / 0]);
var K = i([1 / 0, 1 / 0, -1 / 0, -1 / 0]);
var L = i([0, 0, 0, 0]);
var N = i([0, 0, 1, 1]);

export {
  u,
  e,
  a2 as a,
  o,
  c,
  f,
  m,
  h,
  x,
  M,
  s,
  l,
  y,
  p,
  b,
  F,
  w2 as w,
  q,
  E,
  R,
  z,
  D,
  I,
  K
};
//# sourceMappingURL=chunk-I7WHRVHF.js.map
