import {
  w
} from "./chunk-CIHGHHEZ.js";
import {
  d
} from "./chunk-G3QAWKCD.js";
import {
  z
} from "./chunk-6PEIQDFP.js";
import {
  a as a2
} from "./chunk-ORU3OGKZ.js";
import {
  o as o4,
  p as p4
} from "./chunk-BS3GJQ77.js";
import {
  T
} from "./chunk-IOBN373Z.js";
import {
  m,
  p as p2
} from "./chunk-VJW7RCN7.js";
import {
  C
} from "./chunk-Q7K3J54I.js";
import {
  p as p3
} from "./chunk-ZL6CFFJK.js";
import {
  k
} from "./chunk-MQ2IOGEF.js";
import {
  M
} from "./chunk-ETY52UBV.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  o as o3
} from "./chunk-PEEUPDEG.js";
import {
  o as o2
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  u,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  o,
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/mixins/FeatureEffectLayer.js
var s = { write: { allowNull: true } };
var p5 = (p7) => {
  let c2 = class extends p7 {
    constructor() {
      super(...arguments), this.featureEffect = null;
    }
  };
  return e([y({ type: w, json: { origins: { "web-map": s, "portal-item": s } } })], c2.prototype, "featureEffect", void 0), c2 = e([a("esri.layers.mixins.FeatureEffectLayer")], c2), c2;
};

// node_modules/@arcgis/core/layers/support/FeatureReduction.js
var t = class extends l {
  constructor() {
    super(...arguments), this.type = null;
  }
};
e([y({ type: ["selection", "cluster", "binning"], readOnly: true, json: { read: false, write: true } })], t.prototype, "type", void 0), t = e([a("esri.layers.support.FeatureReduction")], t);

// node_modules/@arcgis/core/layers/support/FeatureReductionSelection.js
var t2;
var c = t2 = class extends t {
  constructor(r3) {
    super(r3), this.type = "selection";
  }
  clone() {
    return new t2();
  }
};
e([y({ type: ["selection"] })], c.prototype, "type", void 0), c = t2 = e([a("esri.layers.support.FeatureReductionSelection")], c);
var p6 = c;

// node_modules/@arcgis/core/layers/support/FeatureReductionBinning.js
var v;
var S = u({ types: M });
var w2 = "esri.layers.support.FeatureReductionBinning";
var I = v = class extends t {
  constructor(e2) {
    super(e2), this.type = "binning", this.binType = "geohash", this.fixedBinLevel = 3, this.labelingInfo = null, this.labelsVisible = true, this.maxScale = 0, this.popupEnabled = true, this.popupTemplate = null, this.fields = [], this.renderer = null;
  }
  writeFields(e2, r3, o6) {
    const t3 = e2.filter((e3) => "avg_angle" !== e3.statisticType).map((e3) => e3.toJSON());
    o(o6, t3, r3);
  }
  readRenderer(e2, r3, i) {
    var _a;
    const p7 = (_a = r3.drawingInfo) == null ? void 0 : _a.renderer;
    return p7 ? o4(p7, r3, i) ?? void 0 : r3.defaultSymbol ? r3.types && r3.types.length ? new T({ defaultSymbol: S(r3.defaultSymbol, r3, i), field: r3.typeIdField, uniqueValueInfos: r3.types.map((e3) => ({ id: e3.id, symbol: S(e3.symbol, e3, i) })) }) : new p3({ symbol: S(r3.defaultSymbol, r3, i) }) : null;
  }
  clone() {
    return new v({ fields: p(this.fields), fixedBinLevel: this.fixedBinLevel, labelingInfo: p(this.labelingInfo), labelsVisible: this.labelsVisible, maxScale: this.maxScale, popupEnabled: this.popupEnabled, popupTemplate: p(this.popupTemplate), renderer: p(this.renderer) });
  }
};
e([o3({ binning: "binning" })], I.prototype, "type", void 0), e([o3({ geohash: "geohash" })], I.prototype, "binType", void 0), e([y({ type: Number, range: { min: 1, max: 9 }, json: { write: true } })], I.prototype, "fixedBinLevel", void 0), e([y({ type: [C], json: { read: { source: "drawingInfo.labelingInfo" }, write: { target: "drawingInfo.labelingInfo" } } })], I.prototype, "labelingInfo", void 0), e([y(m)], I.prototype, "labelsVisible", void 0), e([y({ type: Number, json: { default: 0, name: "visibilityInfo.maxScale" } })], I.prototype, "maxScale", void 0), e([y(p2)], I.prototype, "popupEnabled", void 0), e([y({ type: k, json: { name: "popupInfo", write: true } })], I.prototype, "popupTemplate", void 0), e([y({ type: [a2], json: { write: true } })], I.prototype, "fields", void 0), e([r("fields")], I.prototype, "writeFields", null), e([y({ types: p4, json: { write: { target: "drawingInfo.renderer" } } })], I.prototype, "renderer", void 0), e([o2("renderer", ["drawingInfo.renderer"])], I.prototype, "readRenderer", null), I = v = e([a(w2)], I);
var x = I;

// node_modules/@arcgis/core/layers/support/featureReductionUtils.js
var o5 = { key: "type", base: t, typeMap: { cluster: z, binning: x } };
var r2 = { types: { key: "type", base: t, typeMap: { selection: p6, cluster: z, binning: x } }, json: { name: "layerDefinition.featureReduction", write: { allowNull: true }, origins: { "web-map": { types: o5 }, "portal-item": { types: o5 }, "web-scene": { types: { key: "type", base: t, typeMap: { selection: p6 } } } } } };

// node_modules/@arcgis/core/layers/mixins/FeatureReductionLayer.js
var n = (n2) => {
  let u2 = class extends n2 {
    constructor(...e2) {
      super(...e2), this.own(this.watch("renderer", () => {
        if (this.featureReduction) {
          const e3 = this._normalizeFeatureReduction(this.featureReduction);
          this._set("featureReduction", e3);
        }
      }, true));
    }
    set featureReduction(e2) {
      const r3 = this._normalizeFeatureReduction(e2);
      this._set("featureReduction", r3);
    }
    set renderer(e2) {
    }
    _normalizeFeatureReduction(e2) {
      var _a;
      if ("cluster" !== (e2 == null ? void 0 : e2.type)) return e2;
      const r3 = e2.clone(), t3 = [new a2({ name: "cluster_count", isAutoGenerated: true, statisticType: "count" })], o6 = (r3.fields ?? []).filter((e3) => !e3.isAutoGenerated);
      if (e2.renderer && !((_a = e2.renderer.authoringInfo) == null ? void 0 : _a.isAutoGenerated)) return r3.fields = [...t3, ...o6], r3;
      if (e2.symbol) return r3.fields = [...t3, ...o6], r3.renderer = null, r3;
      if (!this.renderer) return e2;
      const n3 = d(t3, this.renderer, e2, null, false);
      return r3.fields = [...t3, ...o6], r3.renderer = n3, r3;
    }
  };
  return e([y(r2)], u2.prototype, "featureReduction", null), u2 = e([a("esri.layers.mixins.FeatureReductionLayer")], u2), u2;
};

export {
  p5 as p,
  t,
  p6 as p2,
  n
};
//# sourceMappingURL=chunk-OA2XSLRZ.js.map
