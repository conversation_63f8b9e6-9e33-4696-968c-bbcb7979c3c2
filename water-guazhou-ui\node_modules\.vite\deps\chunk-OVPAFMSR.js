// node_modules/@arcgis/core/layers/support/lazyLayerLoader.js
var a = { BingMapsLayer: async () => (await import("./BingMapsLayer-57NOR4NW.js")).default, BuildingSceneLayer: async () => (await import("./BuildingSceneLayer-WVOPOLLY.js")).default, CSVLayer: async () => (await import("./CSVLayer-S3WG4DHY.js")).default, DimensionLayer: async () => (await import("./DimensionLayer-KLZLTVID.js")).default, ElevationLayer: async () => (await import("./ElevationLayer-CKIV3SWN.js")).default, FeatureLayer: async () => (await import("./@arcgis_core_layers_FeatureLayer__js.js")).default, GeoJSONLayer: async () => (await import("./GeoJSONLayer-WQD2TC7L.js")).default, GeoRSSLayer: async () => (await import("./GeoRSSLayer-65DZP4NC.js")).default, GroupLayer: async () => (await import("./@arcgis_core_layers_GroupLayer.js")).default, ImageryLayer: async () => (await import("./ImageryLayer-FHOOLL5U.js")).default, ImageryTileLayer: async () => (await import("./ImageryTileLayer-M6XMXYEF.js")).default, IntegratedMeshLayer: async () => (await import("./IntegratedMeshLayer-7W7JHHLT.js")).default, KMLLayer: async () => (await import("./KMLLayer-GZZVWKTD.js")).default, LineOfSightLayer: async () => (await import("./LineOfSightLayer-535PVS6D.js")).default, MapImageLayer: async () => (await import("./@arcgis_core_layers_MapImageLayer.js")).default, MapNotesLayer: async () => (await import("./MapNotesLayer-2S5ATZDX.js")).default, MediaLayer: async () => (await import("./MediaLayer-R4LTLOYU.js")).default, OGCFeatureLayer: async () => (await import("./OGCFeatureLayer-SEDHKXHP.js")).default, OpenStreetMapLayer: async () => (await import("./OpenStreetMapLayer-AQPXDPCF.js")).default, OrientedImageryLayer: async () => (await import("./OrientedImageryLayer-6D54CIP3.js")).default, PointCloudLayer: async () => (await import("./PointCloudLayer-VHBLYRCG.js")).default, RouteLayer: async () => (await import("./RouteLayer-2NFXX5RP.js")).default, SceneLayer: async () => (await import("./SceneLayer-SR54AOZO.js")).default, StreamLayer: async () => (await import("./StreamLayer-TE24GZ4L.js")).default, SubtypeGroupLayer: async () => (await import("./SubtypeGroupLayer-62CUUIEU.js")).default, TileLayer: async () => (await import("./@arcgis_core_layers_TileLayer__js.js")).default, UnknownLayer: async () => (await import("./UnknownLayer-N43KPMNR.js")).default, UnsupportedLayer: async () => (await import("./UnsupportedLayer-ZWQ72X5P.js")).default, VectorTileLayer: async () => (await import("./VectorTileLayer-54W4IP5L.js")).default, VoxelLayer: async () => (await import("./VoxelLayer-5YZ2IOWG.js")).default, WFSLayer: async () => (await import("./@arcgis_core_layers_WFSLayer.js")).default, WMSLayer: async () => (await import("./@arcgis_core_layers_WMSLayer__js.js")).default, WMTSLayer: async () => (await import("./WMTSLayer-AWD5ACL5.js")).default, WebTileLayer: async () => (await import("./WebTileLayer-U3PD34MQ.js")).default };

export {
  a
};
//# sourceMappingURL=chunk-OVPAFMSR.js.map
