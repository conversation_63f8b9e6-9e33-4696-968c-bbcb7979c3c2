{"version": 3, "sources": ["../../@arcgis/core/rest/support/FeatureSet.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{geometryTypes as t}from\"../../geometry.js\";import r from\"../../Graphic.js\";import{JSONMap as o}from\"../../core/jsonMap.js\";import{JSONSupport as s}from\"../../core/JSONSupport.js\";import{clone as i}from\"../../core/lang.js\";import{isSome as n,unwrap as a}from\"../../core/maybe.js\";import{property as p}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import{reader as l}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as y}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as m}from\"../../core/accessorSupport/decorators/writer.js\";import c from\"../../geometry/SpatialReference.js\";import{fromJSON as u,isPolygon as f}from\"../../geometry/support/jsonUtils.js\";import h from\"../../layers/support/Field.js\";var d;const g=new o({esriGeometryPoint:\"point\",esriGeometryMultipoint:\"multipoint\",esriGeometryPolyline:\"polyline\",esriGeometryPolygon:\"polygon\",esriGeometryEnvelope:\"extent\",mesh:\"mesh\",\"\":null});let j=d=class extends s{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const o=c.fromJSON(t.spatialReference),s=[];for(let i=0;i<e.length;i++){const t=e[i],a=r.fromJSON(t),p=t.geometry&&t.geometry.spatialReference;n(a.geometry)&&!p&&(a.geometry.spatialReference=o);const l=t.aggregateGeometries,y=a.aggregateGeometries;if(l&&n(y))for(const e in y){const t=y[e],r=l[e]?.spatialReference;n(t)&&!r&&(t.spatialReference=o)}s.push(a)}return s}writeGeometryType(e,t,r,o){if(e)return void g.write(e,t,r,o);const{features:s}=this;if(s)for(const i of s)if(i&&n(i.geometry))return void g.write(i.geometry.type,t,r,o)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,o=u(e);return o&&!r&&t.spatialReference&&(o.spatialReference=c.fromJSON(t.spatialReference)),o}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const o of r)if(o&&n(o.geometry)&&o.geometry.spatialReference)return void(t.spatialReference=o.geometry.spatialReference.toJSON())}clone(){return new d(this.cloneProperties())}cloneProperties(){return i({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const o=t.features[r];if(o.geometry){const t=e&&e[r];o.geometry=t&&t.toJSON()||o.geometry}}return t}quantize(e){const{scale:[t,r],translate:[o,s]}=e,i=e=>Math.round((e-o)/t),n=e=>Math.round((s-e)/r),p=this.features,l=this._getQuantizationFunction(this.geometryType,i,n);for(let y=0,m=p.length;y<m;y++)l?.(a(p[y].geometry))||(p.splice(y,1),y--,m--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[o,s],scale:[i,a]}=r,p=e=>e*i+o,l=e=>s-e*a,y=this._getHydrationFunction(e,p,l);for(const{geometry:m}of t)n(m)&&y&&y(m);return this.transform=null,this}_quantizePoints(e,t,r){let o,s;const i=[];for(let n=0,a=e.length;n<a;n++){const a=e[n];if(n>0){const e=t(a[0]),n=r(a[1]);e===o&&n===s||(i.push([e-o,n-s]),o=e,s=n)}else o=t(a[0]),s=r(a[1]),i.push([o,s])}return i.length>0?i:null}_getQuantizationFunction(e,t,r){return\"point\"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):\"polyline\"===e||\"polygon\"===e?e=>{const o=f(e)?e.rings:e.paths,s=[];for(let i=0,n=o.length;i<n;i++){const e=o[i],n=this._quantizePoints(e,t,r);n&&s.push(n)}return s.length>0?(f(e)?e.rings=s:e.paths=s,e):null}:\"multipoint\"===e?e=>{const o=this._quantizePoints(e.points,t,r);return o&&o.length>0?(e.points=o,e):null}:\"extent\"===e?e=>e:null}_getHydrationFunction(e,t,r){return\"point\"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:\"polyline\"===e||\"polygon\"===e?e=>{const o=f(e)?e.rings:e.paths;let s,i;for(let n=0,a=o.length;n<a;n++){const e=o[n];for(let o=0,n=e.length;o<n;o++){const n=e[o];o>0?(s+=n[0],i+=n[1]):(s=n[0],i=n[1]),n[0]=t(s),n[1]=r(i)}}}:\"extent\"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:\"multipoint\"===e?e=>{const o=e.points;let s,i;for(let n=0,a=o.length;n<a;n++){const e=o[n];n>0?(s+=e[0],i+=e[1]):(s=e[0],i=e[1]),e[0]=t(s),e[1]=r(i)}}:null}};e([p({type:String,json:{write:!0}})],j.prototype,\"displayFieldName\",void 0),e([p({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],j.prototype,\"exceededTransferLimit\",void 0),e([p({type:[r],json:{write:!0}})],j.prototype,\"features\",void 0),e([l(\"features\")],j.prototype,\"readFeatures\",null),e([p({type:[h],json:{write:!0}})],j.prototype,\"fields\",void 0),e([p({type:[\"point\",\"multipoint\",\"polyline\",\"polygon\",\"extent\",\"mesh\"],json:{read:{reader:g.read}}})],j.prototype,\"geometryType\",void 0),e([m(\"geometryType\")],j.prototype,\"writeGeometryType\",null),e([p({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],j.prototype,\"hasM\",void 0),e([p({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],j.prototype,\"hasZ\",void 0),e([p({types:t,json:{write:!0}})],j.prototype,\"queryGeometry\",void 0),e([l(\"queryGeometry\")],j.prototype,\"readQueryGeometry\",null),e([p({type:c,json:{write:!0}})],j.prototype,\"spatialReference\",void 0),e([m(\"spatialReference\")],j.prototype,\"writeSpatialReference\",null),e([p({json:{write:!0}})],j.prototype,\"transform\",void 0),j=d=e([y(\"esri.rest.support.FeatureSet\")],j),j.prototype.toJSON.isDefaultToJSON=!0;const x=j;export{x as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIs1B,IAAI;AAAE,IAAMA,KAAE,IAAI,EAAE,EAAC,mBAAkB,SAAQ,wBAAuB,cAAa,sBAAqB,YAAW,qBAAoB,WAAU,sBAAqB,UAAS,MAAK,QAAO,IAAG,KAAI,CAAC;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,mBAAiB,MAAK,KAAK,wBAAsB,OAAG,KAAK,WAAS,CAAC,GAAE,KAAK,SAAO,MAAK,KAAK,eAAa,MAAK,KAAK,OAAK,OAAG,KAAK,OAAK,OAAG,KAAK,gBAAc,MAAK,KAAK,mBAAiB;AAAA,EAAI;AAAA,EAAC,aAAaA,IAAE,GAAE;AAJ3xC;AAI4xC,UAAMC,KAAE,EAAE,SAAS,EAAE,gBAAgB,GAAEC,KAAE,CAAC;AAAE,aAAQ,IAAE,GAAE,IAAEF,GAAE,QAAO,KAAI;AAAC,YAAMG,KAAEH,GAAE,CAAC,GAAEI,KAAE,EAAE,SAASD,EAAC,GAAEE,KAAEF,GAAE,YAAUA,GAAE,SAAS;AAAiB,QAAEC,GAAE,QAAQ,KAAG,CAACC,OAAID,GAAE,SAAS,mBAAiBH;AAAG,YAAMK,KAAEH,GAAE,qBAAoBI,KAAEH,GAAE;AAAoB,UAAGE,MAAG,EAAEC,EAAC,EAAE,YAAUP,MAAKO,IAAE;AAAC,cAAMJ,KAAEI,GAAEP,EAAC,GAAEQ,MAAE,KAAAF,GAAEN,EAAC,MAAH,mBAAM;AAAiB,UAAEG,EAAC,KAAG,CAACK,OAAIL,GAAE,mBAAiBF;AAAA,MAAE;AAAC,MAAAC,GAAE,KAAKE,EAAC;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAE,GAAEQ,IAAEP,IAAE;AAAC,QAAGD,GAAE,QAAO,KAAKD,GAAE,MAAMC,IAAE,GAAEQ,IAAEP,EAAC;AAAE,UAAK,EAAC,UAASC,GAAC,IAAE;AAAK,QAAGA;AAAE,iBAAU,KAAKA,GAAE,KAAG,KAAG,EAAE,EAAE,QAAQ,EAAE,QAAO,KAAKH,GAAE,MAAM,EAAE,SAAS,MAAK,GAAES,IAAEP,EAAC;AAAA;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAE,GAAE;AAAC,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMQ,KAAE,CAAC,CAACR,GAAE,kBAAiBC,KAAE,EAAED,EAAC;AAAE,WAAOC,MAAG,CAACO,MAAG,EAAE,qBAAmBP,GAAE,mBAAiB,EAAE,SAAS,EAAE,gBAAgB,IAAGA;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAE,GAAE;AAAC,QAAGA,GAAE,QAAO,MAAK,EAAE,mBAAiBA,GAAE,OAAO;AAAG,UAAK,EAAC,UAASQ,GAAC,IAAE;AAAK,QAAGA;AAAE,iBAAUP,MAAKO,GAAE,KAAGP,MAAG,EAAEA,GAAE,QAAQ,KAAGA,GAAE,SAAS,iBAAiB,QAAO,MAAK,EAAE,mBAAiBA,GAAE,SAAS,iBAAiB,OAAO;AAAA;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,KAAK,gBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAO,EAAE,EAAC,kBAAiB,KAAK,kBAAiB,uBAAsB,KAAK,uBAAsB,UAAS,KAAK,UAAS,QAAO,KAAK,QAAO,cAAa,KAAK,cAAa,MAAK,KAAK,MAAK,MAAK,KAAK,MAAK,eAAc,KAAK,eAAc,kBAAiB,KAAK,kBAAiB,WAAU,KAAK,UAAS,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,UAAM,IAAE,KAAK,MAAM;AAAE,QAAG,EAAE,YAAU,MAAM,QAAQA,EAAC,KAAGA,GAAE,SAAO,EAAE,UAAQQ,KAAE,GAAEA,KAAE,EAAE,SAAS,QAAOA,MAAI;AAAC,YAAMP,KAAE,EAAE,SAASO,EAAC;AAAE,UAAGP,GAAE,UAAS;AAAC,cAAME,KAAEH,MAAGA,GAAEQ,EAAC;AAAE,QAAAP,GAAE,WAASE,MAAGA,GAAE,OAAO,KAAGF,GAAE;AAAA,MAAQ;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,SAASD,IAAE;AAAC,UAAK,EAAC,OAAM,CAAC,GAAEQ,EAAC,GAAE,WAAU,CAACP,IAAEC,EAAC,EAAC,IAAEF,IAAE,IAAE,CAAAA,OAAG,KAAK,OAAOA,KAAEC,MAAG,CAAC,GAAEQ,KAAE,CAAAT,OAAG,KAAK,OAAOE,KAAEF,MAAGQ,EAAC,GAAEH,KAAE,KAAK,UAASC,KAAE,KAAK,yBAAyB,KAAK,cAAa,GAAEG,EAAC;AAAE,aAAQF,KAAE,GAAE,IAAEF,GAAE,QAAOE,KAAE,GAAEA,KAAI,EAAAD,MAAA,gBAAAA,GAAIN,GAAEK,GAAEE,EAAC,EAAE,QAAQ,QAAKF,GAAE,OAAOE,IAAE,CAAC,GAAEA,MAAI;AAAK,WAAO,KAAK,YAAUP,IAAE;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,UAAK,EAAC,cAAaA,IAAE,UAAS,GAAE,WAAUQ,GAAC,IAAE;AAAK,QAAG,CAACA,GAAE,QAAO;AAAK,UAAK,EAAC,WAAU,CAACP,IAAEC,EAAC,GAAE,OAAM,CAAC,GAAEE,EAAC,EAAC,IAAEI,IAAEH,KAAE,CAAAL,OAAGA,KAAE,IAAEC,IAAEK,KAAE,CAAAN,OAAGE,KAAEF,KAAEI,IAAEG,KAAE,KAAK,sBAAsBP,IAAEK,IAAEC,EAAC;AAAE,eAAS,EAAC,UAAS,EAAC,KAAI,EAAE,GAAE,CAAC,KAAGC,MAAGA,GAAE,CAAC;AAAE,WAAO,KAAK,YAAU,MAAK;AAAA,EAAI;AAAA,EAAC,gBAAgBP,IAAE,GAAEQ,IAAE;AAAC,QAAIP,IAAEC;AAAE,UAAM,IAAE,CAAC;AAAE,aAAQO,KAAE,GAAEL,KAAEJ,GAAE,QAAOS,KAAEL,IAAEK,MAAI;AAAC,YAAML,KAAEJ,GAAES,EAAC;AAAE,UAAGA,KAAE,GAAE;AAAC,cAAMT,KAAE,EAAEI,GAAE,CAAC,CAAC,GAAEK,KAAED,GAAEJ,GAAE,CAAC,CAAC;AAAE,QAAAJ,OAAIC,MAAGQ,OAAIP,OAAI,EAAE,KAAK,CAACF,KAAEC,IAAEQ,KAAEP,EAAC,CAAC,GAAED,KAAED,IAAEE,KAAEO;AAAA,MAAE,MAAM,CAAAR,KAAE,EAAEG,GAAE,CAAC,CAAC,GAAEF,KAAEM,GAAEJ,GAAE,CAAC,CAAC,GAAE,EAAE,KAAK,CAACH,IAAEC,EAAC,CAAC;AAAA,IAAC;AAAC,WAAO,EAAE,SAAO,IAAE,IAAE;AAAA,EAAI;AAAA,EAAC,yBAAyBF,IAAE,GAAEQ,IAAE;AAAC,WAAM,YAAUR,KAAE,CAAAA,QAAIA,GAAE,IAAE,EAAEA,GAAE,CAAC,GAAEA,GAAE,IAAEQ,GAAER,GAAE,CAAC,GAAEA,MAAG,eAAaA,MAAG,cAAYA,KAAE,CAAAA,OAAG;AAAC,YAAMC,KAAEM,GAAEP,EAAC,IAAEA,GAAE,QAAMA,GAAE,OAAME,KAAE,CAAC;AAAE,eAAQ,IAAE,GAAEO,KAAER,GAAE,QAAO,IAAEQ,IAAE,KAAI;AAAC,cAAMT,KAAEC,GAAE,CAAC,GAAEQ,KAAE,KAAK,gBAAgBT,IAAE,GAAEQ,EAAC;AAAE,QAAAC,MAAGP,GAAE,KAAKO,EAAC;AAAA,MAAC;AAAC,aAAOP,GAAE,SAAO,KAAGK,GAAEP,EAAC,IAAEA,GAAE,QAAME,KAAEF,GAAE,QAAME,IAAEF,MAAG;AAAA,IAAI,IAAE,iBAAeA,KAAE,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,gBAAgBD,GAAE,QAAO,GAAEQ,EAAC;AAAE,aAAOP,MAAGA,GAAE,SAAO,KAAGD,GAAE,SAAOC,IAAED,MAAG;AAAA,IAAI,IAAE,aAAWA,KAAE,CAAAA,OAAGA,KAAE;AAAA,EAAI;AAAA,EAAC,sBAAsBA,IAAE,GAAEQ,IAAE;AAAC,WAAM,YAAUR,KAAE,CAAAA,OAAG;AAAC,MAAAA,GAAE,IAAE,EAAEA,GAAE,CAAC,GAAEA,GAAE,IAAEQ,GAAER,GAAE,CAAC;AAAA,IAAC,IAAE,eAAaA,MAAG,cAAYA,KAAE,CAAAA,OAAG;AAAC,YAAMC,KAAEM,GAAEP,EAAC,IAAEA,GAAE,QAAMA,GAAE;AAAM,UAAIE,IAAE;AAAE,eAAQO,KAAE,GAAEL,KAAEH,GAAE,QAAOQ,KAAEL,IAAEK,MAAI;AAAC,cAAMT,KAAEC,GAAEQ,EAAC;AAAE,iBAAQR,KAAE,GAAEQ,KAAET,GAAE,QAAOC,KAAEQ,IAAER,MAAI;AAAC,gBAAMQ,KAAET,GAAEC,EAAC;AAAE,UAAAA,KAAE,KAAGC,MAAGO,GAAE,CAAC,GAAE,KAAGA,GAAE,CAAC,MAAIP,KAAEO,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,IAAGA,GAAE,CAAC,IAAE,EAAEP,EAAC,GAAEO,GAAE,CAAC,IAAED,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,IAAE,aAAWR,KAAE,CAAAA,OAAG;AAAC,MAAAA,GAAE,OAAK,EAAEA,GAAE,IAAI,GAAEA,GAAE,OAAKQ,GAAER,GAAE,IAAI,GAAEA,GAAE,OAAK,EAAEA,GAAE,IAAI,GAAEA,GAAE,OAAKQ,GAAER,GAAE,IAAI;AAAA,IAAC,IAAE,iBAAeA,KAAE,CAAAA,OAAG;AAAC,YAAMC,KAAED,GAAE;AAAO,UAAIE,IAAE;AAAE,eAAQO,KAAE,GAAEL,KAAEH,GAAE,QAAOQ,KAAEL,IAAEK,MAAI;AAAC,cAAMT,KAAEC,GAAEQ,EAAC;AAAE,QAAAA,KAAE,KAAGP,MAAGF,GAAE,CAAC,GAAE,KAAGA,GAAE,CAAC,MAAIE,KAAEF,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,IAAGA,GAAE,CAAC,IAAE,EAAEE,EAAC,GAAEF,GAAE,CAAC,IAAEQ,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,IAAE;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,EAAC,gBAAe,CAAAR,QAAI,EAAC,SAAQA,GAAC,GAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,UAAU,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACO,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,SAAQ,cAAa,YAAW,WAAU,UAAS,MAAM,GAAE,MAAK,EAAC,MAAK,EAAC,QAAOR,GAAE,KAAI,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACS,GAAE,cAAc,CAAC,GAAE,EAAE,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,EAAC,gBAAe,CAAAR,QAAI,EAAC,SAAQA,GAAC,GAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,EAAC,gBAAe,CAAAA,QAAI,EAAC,SAAQA,GAAC,GAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,eAAe,CAAC,GAAE,EAAE,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAACQ,GAAE,kBAAkB,CAAC,GAAE,EAAE,WAAU,yBAAwB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,8BAA8B,CAAC,GAAE,CAAC,GAAE,EAAE,UAAU,OAAO,kBAAgB;AAAG,IAAM,IAAE;", "names": ["g", "e", "o", "s", "t", "a", "p", "l", "y", "r", "n"]}