import request from '@/plugins/axios'

/**
 * 获取站点列表
 * @param params 查询参数
 * @returns 
 */
export function getStationList(params: {
  page: number
  size: number
  type?: string
  projectId?: string
}) {
  return request({
    url: '/api/station/list',
    method: 'get',
    params
  })
}

/**
 * 获取站点详情
 * @param id 站点ID
 * @returns
 */
export function getStationById(id: string) {
  return request({
    url: `/api/station/${id}`,
    method: 'get'
  })
}

/**
 * 获取所有站点（不分页）
 * @param params 查询参数
 * @returns
 */
export function getAllStations(params?: {
  type?: string
  projectId?: string
}) {
  return request({
    url: '/api/station/list',
    method: 'get',
    params: {
      page: 1,
      size: 1000, // 使用大的页面大小来获取所有数据
      ...params
    }
  })
}

/**
 * 根据IDs批量获取站点信息
 * @param ids 站点ID数组
 * @returns
 */
export function getStationsByIds(ids: string[]) {
  return request({
    url: '/api/station/batch',
    method: 'post',
    data: { ids }
  })
}
