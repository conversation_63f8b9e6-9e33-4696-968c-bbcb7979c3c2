{"version": 3, "sources": ["../../@arcgis/core/geometry/support/aaBoundingRect.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../core/has.js\";import{clamp as n}from\"../../core/mathUtils.js\";import{isNone as t}from\"../../core/maybe.js\";import r from\"../Extent.js\";function i(n){return n}function u(n=L){return i([n[0],n[1],n[2],n[3]])}function e(n){return i([n[0],n[1],n[2],n[3]])}function a(n,t){return n!==t&&(n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3]),n}function o(n,t,r,i,e=u()){return e[0]=n,e[1]=t,e[2]=r,e[3]=i,e}function c(n,t=u()){return t[0]=n.xmin,t[1]=n.ymin,t[2]=n.xmax,t[3]=n.ymax,t}function f(n,t){return new r({xmin:n[0],ymin:n[1],xmax:n[2],ymax:n[3],spatialReference:t})}function m(n,t){t[0]<n[0]&&(n[0]=t[0]),t[0]>n[2]&&(n[2]=t[0]),t[1]<n[1]&&(n[1]=t[1]),t[1]>n[3]&&(n[3]=t[1])}function h(n,r,i){if(t(r))a(i,n);else if(\"length\"in r)G(r)?(i[0]=Math.min(n[0],r[0]),i[1]=Math.min(n[1],r[1]),i[2]=Math.max(n[2],r[2]),i[3]=Math.max(n[3],r[3])):2!==r.length&&3!==r.length||(i[0]=Math.min(n[0],r[0]),i[1]=Math.min(n[1],r[1]),i[2]=Math.max(n[2],r[0]),i[3]=Math.max(n[3],r[1]));else switch(r.type){case\"extent\":i[0]=Math.min(n[0],r.xmin),i[1]=Math.min(n[1],r.ymin),i[2]=Math.max(n[2],r.xmax),i[3]=Math.max(n[3],r.ymax);break;case\"point\":i[0]=Math.min(n[0],r.x),i[1]=Math.min(n[1],r.y),i[2]=Math.max(n[2],r.x),i[3]=Math.max(n[3],r.y)}}function x(n,t,r=n){const i=t.length;let u=n[0],e=n[1],a=n[2],o=n[3];for(let c=0;c<i;c++){const n=t[c];u=Math.min(u,n[0]),e=Math.min(e,n[1]),a=Math.max(a,n[0]),o=Math.max(o,n[1])}return r[0]=u,r[1]=e,r[2]=a,r[3]=o,r}function M(n){for(let t=0;t<4;t++)if(!isFinite(n[t]))return!1;return!0}function s(n){return t(n)||n[0]>=n[2]?0:n[2]-n[0]}function l(n){return n[1]>=n[3]?0:n[3]-n[1]}function y(n){return s(n)*l(n)}function p(n,t=[0,0]){return t[0]=(n[0]+n[2])/2,t[1]=(n[1]+n[3])/2,t}function b(n,t){return w(n,t[0],t[1])}function g(n,t){const r=t[3],i=.5*(n[0]+n[2]),u=Math.abs(t[0]-i),e=.5*(n[2]-n[0]);if(u>r+e)return!1;const a=.5*(n[1]+n[3]),o=.5*(n[3]-n[1]),c=Math.abs(t[1]-a);if(c>r+o)return!1;if(u<e||c<o)return!0;const f=u-e,m=c-o;return f*f+m*m<=r*r}function j(n,t,r){const i=n[0],u=n[1],e=n[2],a=n[3],{x:o,y:c}=t,{x:f,y:m}=r,h=(n,t)=>(m-c)*n+(o-f)*t+(f*c-o*m)<0,x=h(i,a),M=h(e,a),s=h(e,u),l=h(i,u);return!(x===M&&M===s&&s===l&&l===x||o<i&&f<i||o>e&&f>e||c>a&&m>a||c<u&&m<u)}function F(n,t){return w(n,t.x,t.y)}function w(n,t,r){return t>=n[0]&&r>=n[1]&&t<=n[2]&&r<=n[3]}function k(n,t,r,i){return t>=n[0]-i&&r>=n[1]-i&&t<=n[2]+i&&r<=n[3]+i}function q(n,t,r){return t[0]>=n[0]-r&&t[1]>=n[1]-r&&t[0]<=n[2]+r&&t[1]<=n[3]+r}function E(n,t){return Math.max(t[0],n[0])<=Math.min(t[2],n[2])&&Math.max(t[1],n[1])<=Math.min(t[3],n[3])}function R(n,t){return t[0]>=n[0]&&t[2]<=n[2]&&t[1]>=n[1]&&t[3]<=n[3]}function U(r,i,u){if(t(i))return a(u,r);const e=i[0],o=i[1],c=i[2],f=i[3];return u[0]=n(r[0],e,c),u[1]=n(r[1],o,f),u[2]=n(r[2],e,c),u[3]=n(r[3],o,f),u}function d(n,t){const r=(n[0]+n[2])/2,i=(n[1]+n[3])/2,u=Math.max(Math.abs(t[0]-r)-s(n)/2,0),e=Math.max(Math.abs(t[1]-i)-l(n)/2,0);return Math.sqrt(u*u+e*e)}function v(n,t){t[0]=n[2]-n[0],t[1]=n[3]-n[1]}function z(n,t,r,i=n){return i[0]=n[0]+t,i[1]=n[1]+r,i[2]=n[2]+t,i[3]=n[3]+r,i}function A(n,t,r=n){return r[0]=n[0]-t,r[1]=n[1]-t,r[2]=n[2]+t,r[3]=n[3]+t,r}function B(n,t,r=n){return r[0]=t[0],r[1]=t[1],r!==n&&(r[2]=n[2],r[3]=n[3]),r}function C(n,t,r=n){return r[2]=t[0],r[3]=t[1],r!==n&&(r[0]=n[0],r[1]=n[1]),n}function D(n){return n?a(n,K):u(K)}function G(n){return null!=n&&4===n.length}function H(n){return!(0!==s(n)&&isFinite(n[0])||0!==l(n)&&isFinite(n[1]))}function I(n,t){return G(n)&&G(t)?n[0]===t[0]&&n[1]===t[1]&&n[2]===t[2]&&n[3]===t[3]:n===t}const J=i([-1/0,-1/0,1/0,1/0]),K=i([1/0,1/0,-1/0,-1/0]),L=i([0,0,0,0]),N=i([0,0,1,1]);export{K as NEGATIVE_INFINITY,J as POSITIVE_INFINITY,N as UNIT,L as ZERO,M as allFinite,y as area,p as center,e as clone,R as contains,b as containsPoint,F as containsPointObject,q as containsPointWithMargin,w as containsXY,k as containsXYWithMargin,a as copy,u as create,d as distance,D as empty,I as equals,h as expand,m as expandPointInPlace,x as expandWithNestedArray,c as fromExtent,o as fromValues,l as height,U as intersection,E as intersects,j as intersectsSegment,g as intersectsSphere,G as is,H as isPoint,z as offset,A as pad,C as setMax,B as setMin,v as size,f as toExtent,s as width};\n"], "mappings": ";;;;;;;;AAImJ,SAAS,EAAE,GAAE;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,IAAE,GAAE;AAAC,SAAO,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,CAAC;AAAC;AAAC,SAASA,GAAE,GAAEC,IAAE;AAAC,SAAO,MAAIA,OAAI,EAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,IAAG;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAEC,IAAEC,KAAE,EAAE,GAAE;AAAC,SAAOA,GAAE,CAAC,IAAE,GAAEA,GAAE,CAAC,IAAEF,IAAEE,GAAE,CAAC,IAAE,GAAEA,GAAE,CAAC,IAAED,IAAEC;AAAC;AAAC,SAAS,EAAE,GAAEF,KAAE,EAAE,GAAE;AAAC,SAAOA,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE,MAAKA,GAAE,CAAC,IAAE,EAAE,MAAKA;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,SAAO,IAAI,EAAE,EAAC,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,CAAC,GAAE,kBAAiBA,GAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,EAAAA,GAAE,CAAC,IAAE,EAAE,CAAC,MAAI,EAAE,CAAC,IAAEA,GAAE,CAAC,IAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,MAAI,EAAE,CAAC,IAAEA,GAAE,CAAC,IAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,MAAI,EAAE,CAAC,IAAEA,GAAE,CAAC,IAAGA,GAAE,CAAC,IAAE,EAAE,CAAC,MAAI,EAAE,CAAC,IAAEA,GAAE,CAAC;AAAE;AAAC,SAAS,EAAE,GAAE,GAAEC,IAAE;AAAC,MAAG,EAAE,CAAC,EAAE,CAAAF,GAAEE,IAAE,CAAC;AAAA,WAAU,YAAW,EAAE,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,KAAG,MAAI,EAAE,UAAQ,MAAI,EAAE,WAASA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAQ,SAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAS,MAAAA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,IAAI,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,IAAI,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,IAAI,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,IAAI;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAED,IAAE,IAAE,GAAE;AAAC,QAAMC,KAAED,GAAE;AAAO,MAAIG,KAAE,EAAE,CAAC,GAAED,KAAE,EAAE,CAAC,GAAEH,KAAE,EAAE,CAAC,GAAEK,KAAE,EAAE,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAEJ,IAAEI,MAAI;AAAC,UAAMC,KAAEN,GAAEK,EAAC;AAAE,IAAAF,KAAE,KAAK,IAAIA,IAAEG,GAAE,CAAC,CAAC,GAAEJ,KAAE,KAAK,IAAIA,IAAEI,GAAE,CAAC,CAAC,GAAEP,KAAE,KAAK,IAAIA,IAAEO,GAAE,CAAC,CAAC,GAAEF,KAAE,KAAK,IAAIA,IAAEE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,CAAC,IAAEH,IAAE,EAAE,CAAC,IAAED,IAAE,EAAE,CAAC,IAAEH,IAAE,EAAE,CAAC,IAAEK,IAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,WAAQJ,KAAE,GAAEA,KAAE,GAAEA,KAAI,KAAG,CAAC,SAAS,EAAEA,EAAC,CAAC,EAAE,QAAM;AAAG,SAAM;AAAE;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,CAAC,IAAE,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,KAAE,CAAC,GAAE,CAAC,GAAE;AAAC,SAAOA,GAAE,CAAC,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,KAAG,GAAEA,GAAE,CAAC,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,KAAG,GAAEA;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,SAAOO,GAAE,GAAEP,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAC;AAA8c,SAAS,EAAE,GAAEQ,IAAE;AAAC,SAAOC,GAAE,GAAED,GAAE,GAAEA,GAAE,CAAC;AAAC;AAAC,SAASC,GAAE,GAAED,IAAE,GAAE;AAAC,SAAOA,MAAG,EAAE,CAAC,KAAG,KAAG,EAAE,CAAC,KAAGA,MAAG,EAAE,CAAC,KAAG,KAAG,EAAE,CAAC;AAAC;AAAuE,SAAS,EAAE,GAAEE,IAAE,GAAE;AAAC,SAAOA,GAAE,CAAC,KAAG,EAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,KAAG,EAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,KAAG,EAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,KAAG,EAAE,CAAC,IAAE;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,SAAO,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,KAAG,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,KAAG,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,KAAG,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,SAAOA,GAAE,CAAC,KAAG,EAAE,CAAC,KAAGA,GAAE,CAAC,KAAG,EAAE,CAAC,KAAGA,GAAE,CAAC,KAAG,EAAE,CAAC,KAAGA,GAAE,CAAC,KAAG,EAAE,CAAC;AAAC;AAAkW,SAAS,EAAE,GAAEC,IAAE,GAAEC,KAAE,GAAE;AAAC,SAAOA,GAAE,CAAC,IAAE,EAAE,CAAC,IAAED,IAAEC,GAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAEA,GAAE,CAAC,IAAE,EAAE,CAAC,IAAED,IAAEC,GAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAEA;AAAC;AAA0O,SAAS,EAAE,GAAE;AAAC,SAAO,IAAEC,GAAE,GAAE,CAAC,IAAE,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,QAAM,KAAG,MAAI,EAAE;AAAM;AAA2E,SAAS,EAAE,GAAEC,IAAE;AAAC,SAAO,EAAE,CAAC,KAAG,EAAEA,EAAC,IAAE,EAAE,CAAC,MAAIA,GAAE,CAAC,KAAG,EAAE,CAAC,MAAIA,GAAE,CAAC,KAAG,EAAE,CAAC,MAAIA,GAAE,CAAC,KAAG,EAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,MAAIA;AAAC;AAAC,IAAM,IAAE,EAAE,CAAC,KAAG,GAAE,KAAG,GAAE,IAAE,GAAE,IAAE,CAAC,CAAC;AAA7B,IAA+B,IAAE,EAAE,CAAC,IAAE,GAAE,IAAE,GAAE,KAAG,GAAE,KAAG,CAAC,CAAC;AAAtD,IAAwD,IAAE,EAAE,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC;AAArE,IAAuE,IAAE,EAAE,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC;", "names": ["a", "t", "i", "e", "u", "o", "c", "n", "w", "t", "w", "t", "t", "i", "a", "t"]}