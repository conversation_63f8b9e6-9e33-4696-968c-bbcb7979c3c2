{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/ArcGISMapService.js", "../../@arcgis/core/layers/support/Sublayer.js", "../../@arcgis/core/layers/mixins/SublayersOwner.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import r from\"../../request.js\";import{throwIfAborted as s}from\"../../core/promiseUtils.js\";import{urlToObject as t}from\"../../core/urlUtils.js\";import{Version as o}from\"../../core/Version.js\";import{property as i}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as p}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as a}from\"../../core/accessorSupport/decorators/subclass.js\";import l from\"../../geometry/Extent.js\";import n from\"../../geometry/SpatialReference.js\";import{id as u,popupEnabled as c}from\"../support/commonProperties.js\";const y=y=>{let d=class extends y{constructor(){super(...arguments),this.capabilities=void 0,this.copyright=null,this.fullExtent=null,this.legendEnabled=!0,this.spatialReference=null,this.version=void 0,this._allLayersAndTablesPromise=null,this._allLayersAndTablesMap=null}readCapabilities(e,r){const s=r.capabilities&&r.capabilities.split(\",\").map((e=>e.toLowerCase().trim()));if(!s)return{operations:{supportsExportMap:!1,supportsExportTiles:!1,supportsIdentify:!1,supportsQuery:!1,supportsTileMap:!1},exportMap:null,exportTiles:null};const t=this.type,i=\"tile\"!==t&&!!r.supportsDynamicLayers,p=s.includes(\"query\"),a=s.includes(\"map\"),l=!!r.exportTilesAllowed,n=s.includes(\"tilemap\"),u=s.includes(\"data\"),c=\"tile\"!==t&&(!r.tileInfo||i),y=\"tile\"!==t&&(!r.tileInfo||i),d=\"tile\"!==t,m=r.cimVersion&&o.parse(r.cimVersion),h=m?.since(1,4)??!1,b=m?.since(2,0)??!1;return{operations:{supportsExportMap:a,supportsExportTiles:l,supportsIdentify:p,supportsQuery:u,supportsTileMap:n},exportMap:a?{supportsArcadeExpressionForLabeling:h,supportsSublayersChanges:d,supportsDynamicLayers:i,supportsSublayerVisibility:c,supportsSublayerDefinitionExpression:y,supportsCIMSymbols:b}:null,exportTiles:l?{maxExportTilesCount:+r.maxExportTilesCount}:null}}readVersion(e,r){let s=r.currentVersion;return s||(s=r.hasOwnProperty(\"capabilities\")||r.hasOwnProperty(\"tables\")?10:r.hasOwnProperty(\"supportedImageFormatTypes\")?9.31:9.3),s}async fetchSublayerInfo(e,r){try{return await this.fetchAllLayersAndTables(r),this._allLayersAndTablesMap?.get(e)}catch{return}}async fetchAllLayersAndTables(e){await this.load(e),this._allLayersAndTablesPromise||(this._allLayersAndTablesPromise=r(t(this.url).path+\"/layers\",{responseType:\"json\",query:{f:\"json\",...this.customParameters,token:this.apiKey}}).then((e=>{this._allLayersAndTablesMap=new Map;for(const r of e.data.layers)this._allLayersAndTablesMap.set(r.id,r);return{result:e.data}}),(e=>({error:e}))));const o=await this._allLayersAndTablesPromise;if(s(e),\"result\"in o)return o.result;throw o.error}};return e([i({readOnly:!0})],d.prototype,\"capabilities\",void 0),e([p(\"service\",\"capabilities\",[\"capabilities\",\"exportTilesAllowed\",\"maxExportTilesCount\",\"supportsDynamicLayers\",\"tileInfo\"])],d.prototype,\"readCapabilities\",null),e([i({json:{read:{source:\"copyrightText\"}}})],d.prototype,\"copyright\",void 0),e([i({type:l})],d.prototype,\"fullExtent\",void 0),e([i(u)],d.prototype,\"id\",void 0),e([i({type:Boolean,json:{origins:{service:{read:{enabled:!1}}},read:{source:\"showLegend\"},write:{target:\"showLegend\"}}})],d.prototype,\"legendEnabled\",void 0),e([i(c)],d.prototype,\"popupEnabled\",void 0),e([i({type:n})],d.prototype,\"spatialReference\",void 0),e([i({readOnly:!0})],d.prototype,\"version\",void 0),e([p(\"version\",[\"currentVersion\",\"capabilities\",\"tables\",\"supportedImageFormatTypes\"])],d.prototype,\"readVersion\",null),d=e([a(\"esri.layers.mixins.ArcGISMapService\")],d),d};export{y as ArcGISMapService};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import\"../../geometry.js\";import r from\"../../PopupTemplate.js\";import\"../../renderers/ClassBreaksRenderer.js\";import\"../../renderers/DictionaryRenderer.js\";import\"../../renderers/DotDensityRenderer.js\";import\"../../renderers/HeatmapRenderer.js\";import\"../../renderers/PieChartRenderer.js\";import\"../../renderers/Renderer.js\";import\"../../renderers/SimpleRenderer.js\";import\"../../renderers/UniqueValueRenderer.js\";import\"../../renderers/support/jsonUtils.js\";import{rendererTypes as t,webSceneRendererTypes as i}from\"../../renderers/support/types.js\";import o from\"../../request.js\";import{isSymbol3D as s}from\"../../symbols.js\";import a from\"../../core/Collection.js\";import l from\"../../core/Error.js\";import{HandleOwnerMixin as n}from\"../../core/HandleOwner.js\";import has from\"../../core/has.js\";import{IdentifiableMixin as p}from\"../../core/Identifiable.js\";import{clone as y}from\"../../core/lang.js\";import d from\"../../core/Loadable.js\";import u from\"../../core/Logger.js\";import{isSome as c}from\"../../core/maybe.js\";import{MultiOriginJSONMixin as f}from\"../../core/MultiOriginJSONSupport.js\";import{sqlAnd as m}from\"../../core/sql.js\";import{objectToQuery as h}from\"../../core/urlUtils.js\";import{property as b}from\"../../core/accessorSupport/decorators/property.js\";import{cast as g}from\"../../core/accessorSupport/decorators/cast.js\";import{reader as S}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as I}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as w}from\"../../core/accessorSupport/decorators/writer.js\";import{ensureInteger as v,ensureType as j,Integer as L,ensureClass as D}from\"../../core/accessorSupport/ensureType.js\";import{OriginId as E,nameToId as F}from\"../../core/accessorSupport/PropertyOrigin.js\";import{getProperties as P}from\"../../core/accessorSupport/utils.js\";import x from\"../graphics/sources/support/QueryTask.js\";import O from\"./FeatureType.js\";import T from\"./Field.js\";import _ from\"./FieldsIndex.js\";import A from\"./LabelClass.js\";import{validateLabelingInfo as V}from\"./labelingInfo.js\";import N from\"./LayerFloorInfo.js\";import{getFeatureLayerCapabilities as M}from\"./serviceCapabilitiesUtils.js\";import{DataLayerSource as C}from\"./source/DataLayerSource.js\";import{MapLayerSource as q}from\"./source/MapLayerSource.js\";import U from\"../../rest/support/AttachmentQuery.js\";import k from\"../../rest/support/Query.js\";import{createPopupTemplate as R}from\"../../support/popupUtils.js\";import Q from\"../../geometry/Extent.js\";import{featureGeometryTypeKebabDictionary as B}from\"../../geometry/support/typeUtils.js\";var J;function $(e){return null!=e&&\"esriSMS\"===e.type}function G(e,r,t){const i=this.originIdOf(r)>=F(t.origin);return{ignoreOrigin:!0,allowNull:i,enabled:!!t&&(\"map-image\"===t.layer?.type&&(t.writeSublayerStructure||i))}}function H(e,r,t){return{enabled:!!t&&(\"tile\"===t.layer?.type&&this._isOverridden(r))}}function z(e,r,t){return{ignoreOrigin:!0,enabled:t&&t.writeSublayerStructure||!1}}function K(e,r,t){return{ignoreOrigin:!0,enabled:!!t&&(t.writeSublayerStructure||this.originIdOf(r)>=F(t.origin))}}let W=0;const X=new Set;X.add(\"layer\"),X.add(\"parent\"),X.add(\"loaded\"),X.add(\"loadStatus\"),X.add(\"loadError\"),X.add(\"loadWarnings\");let Y=J=class extends(n(f(p(d)))){constructor(e){super(e),this.capabilities=void 0,this.fields=null,this.fullExtent=null,this.geometryType=null,this.globalIdField=null,this.legendEnabled=!0,this.objectIdField=null,this.popupEnabled=!0,this.popupTemplate=null,this.sourceJSON=null,this.title=null,this.typeIdField=null,this.types=null,this._lastParsedUrl=null}async load(e){return this.addResolvingPromise((async()=>{const{layer:r,source:t,url:i}=this;if(!r&&!i)throw new l(\"sublayer:missing-layer\",\"Sublayer can't be loaded without being part of a layer\",{sublayer:this});let s=null;if(!r||this.originIdOf(\"url\")>E.SERVICE||\"data-layer\"===t?.type){s=(await o(i,{responseType:\"json\",query:{f:\"json\"},...e})).data}else{let i=this.id;\"map-layer\"===t?.type&&(i=t.mapLayerId),s=await r.fetchSublayerInfo(i,e)}s&&(this.sourceJSON=s,this.read({layerDefinition:s},{origin:\"service\"}))})()),this}readCapabilities(e,r){r=r.layerDefinition||r;const{operations:{supportsQuery:t,supportsQueryAttachments:i},query:{supportsFormatPBF:o},data:{supportsAttachment:s}}=M(r,this.url);return{exportMap:{supportsModification:!!r.canModifyLayer},operations:{supportsQuery:t,supportsQueryAttachments:i},data:{supportsAttachment:s},query:{supportsFormatPBF:o}}}get defaultPopupTemplate(){return this.createPopupTemplate()}set definitionExpression(e){this._setAndNotifyLayer(\"definitionExpression\",e)}get fieldsIndex(){return new _(this.fields||[])}set floorInfo(e){this._setAndNotifyLayer(\"floorInfo\",e)}readGlobalIdFieldFromService(e,r){if((r=r.layerDefinition||r).globalIdField)return r.globalIdField;if(r.fields)for(const t of r.fields)if(\"esriFieldTypeGlobalID\"===t.type)return t.name}get id(){const e=this._get(\"id\");return e??W++}set id(e){this._get(\"id\")!==e&&(!1!==this.layer?.capabilities?.exportMap?.supportsDynamicLayers?this._set(\"id\",e):this._logLockedError(\"id\",\"capability not available 'layer.capabilities.exportMap.supportsDynamicLayers'\"))}set labelingInfo(e){this._setAndNotifyLayer(\"labelingInfo\",e)}writeLabelingInfo(e,r,t,i){e&&e.length&&(r.layerDefinition={drawingInfo:{labelingInfo:e.map((e=>e.write({},i)))}})}set labelsVisible(e){this._setAndNotifyLayer(\"labelsVisible\",e)}set layer(e){this._set(\"layer\",e),this.sublayers&&this.sublayers.forEach((r=>r.layer=e))}set listMode(e){this._set(\"listMode\",e)}set minScale(e){this._setAndNotifyLayer(\"minScale\",e)}readMinScale(e,r){return r.minScale||r.layerDefinition&&r.layerDefinition.minScale||0}set maxScale(e){this._setAndNotifyLayer(\"maxScale\",e)}readMaxScale(e,r){return r.maxScale||r.layerDefinition&&r.layerDefinition.maxScale||0}get effectiveScaleRange(){const{minScale:e,maxScale:r}=this;return{minScale:e,maxScale:r}}readObjectIdFieldFromService(e,r){if((r=r.layerDefinition||r).objectIdField)return r.objectIdField;if(r.fields)for(const t of r.fields)if(\"esriFieldTypeOID\"===t.type)return t.name}set opacity(e){this._setAndNotifyLayer(\"opacity\",e)}readOpacity(e,r){const t=r.layerDefinition;return 1-.01*((null!=t?.transparency?t.transparency:t?.drawingInfo?.transparency)??0)}writeOpacity(e,r,t,i){r.layerDefinition={drawingInfo:{transparency:100-100*e}}}writeParent(e,r){this.parent&&this.parent!==this.layer?r.parentLayerId=v(this.parent.id):r.parentLayerId=-1}get queryTask(){if(!this.layer)return null;const{spatialReference:e}=this.layer,r=\"gdbVersion\"in this.layer?this.layer.gdbVersion:void 0,{capabilities:t,fieldsIndex:i}=this,o=has(\"featurelayer-pbf\")&&t?.query.supportsFormatPBF,s=t?.operations?.supportsQueryAttachments??!1;return new x({url:this.url,pbfSupported:o,fieldsIndex:i,gdbVersion:r,sourceSpatialReference:e,queryAttachmentsSupported:s})}set renderer(e){if(e)for(const r of e.getSymbols())if(s(r)){u.getLogger(this.declaredClass).warn(\"Sublayer renderer should use 2D symbols\");break}this._setAndNotifyLayer(\"renderer\",e)}get source(){return this._get(\"source\")||new q({mapLayerId:this.id})}set source(e){this._setAndNotifyLayer(\"source\",e)}set sublayers(e){this._handleSublayersChange(e,this._get(\"sublayers\")),this._set(\"sublayers\",e)}castSublayers(e){return j(a.ofType(J),e)}writeSublayers(e,r,t){this.sublayers?.length&&(r[t]=this.sublayers.map((e=>e.id)).toArray().reverse())}readTypeIdField(e,r){let t=(r=r.layerDefinition||r).typeIdField;if(t&&r.fields){t=t.toLowerCase();const e=r.fields.find((e=>e.name.toLowerCase()===t));e&&(t=e.name)}return t}get url(){const e=this.layer?.parsedUrl??this._lastParsedUrl,r=this.source;if(!e)return null;if(this._lastParsedUrl=e,\"map-layer\"===r?.type)return`${e.path}/${r.mapLayerId}`;const t={layer:JSON.stringify({source:this.source})};return`${e.path}/dynamicLayer?${h(t)}`}set url(e){this._overrideIfSome(\"url\",e)}set visible(e){this._setAndNotifyLayer(\"visible\",e)}writeVisible(e,r,t,i){r[t]=this.getAtOrigin(\"defaultVisibility\",\"service\")||e}clone(){const{store:e}=P(this),r=new J;return P(r).store=e.clone(X),this.commitProperty(\"url\"),r._lastParsedUrl=this._lastParsedUrl,r}createPopupTemplate(e){return R(this,e)}createQuery(){return new k({returnGeometry:!0,where:this.definitionExpression||\"1=1\"})}async createFeatureLayer(){if(this.hasOwnProperty(\"sublayers\"))return null;const{layer:e}=this,r=e?.parsedUrl,t=new(0,(await import(\"../FeatureLayer.js\")).default)({url:r?.path});return r&&this.source&&(\"map-layer\"===this.source.type?t.layerId=this.source.mapLayerId:t.dynamicDataSource=this.source),null!=e?.refreshInterval&&(t.refreshInterval=e.refreshInterval),this.definitionExpression&&(t.definitionExpression=this.definitionExpression),this.floorInfo&&(t.floorInfo=y(this.floorInfo)),this.originIdOf(\"labelingInfo\")>E.SERVICE&&(t.labelingInfo=y(this.labelingInfo)),this.originIdOf(\"labelsVisible\")>E.DEFAULTS&&(t.labelsVisible=this.labelsVisible),this.originIdOf(\"legendEnabled\")>E.DEFAULTS&&(t.legendEnabled=this.legendEnabled),this.originIdOf(\"visible\")>E.DEFAULTS&&(t.visible=this.visible),this.originIdOf(\"minScale\")>E.DEFAULTS&&(t.minScale=this.minScale),this.originIdOf(\"maxScale\")>E.DEFAULTS&&(t.maxScale=this.maxScale),this.originIdOf(\"opacity\")>E.DEFAULTS&&(t.opacity=this.opacity),this.originIdOf(\"popupTemplate\")>E.DEFAULTS&&(t.popupTemplate=y(this.popupTemplate)),this.originIdOf(\"renderer\")>E.SERVICE&&(t.renderer=y(this.renderer)),\"data-layer\"===this.source?.type&&(t.dynamicDataSource=this.source.clone()),this.originIdOf(\"title\")>E.DEFAULTS&&(t.title=this.title),\"map-image\"===e?.type&&e.originIdOf(\"customParameters\")>E.DEFAULTS&&(t.customParameters=e.customParameters),\"tile\"===e?.type&&e.originIdOf(\"customParameters\")>E.DEFAULTS&&(t.customParameters=e.customParameters),t}getField(e){return this.fieldsIndex.get(e)}getFeatureType(e){const{typeIdField:r,types:t}=this;if(!r||!e)return null;const i=e.attributes?e.attributes[r]:void 0;if(null==i)return null;let o=null;return t?.some((e=>{const{id:r}=e;return null!=r&&(r.toString()===i.toString()&&(o=e),!!o)})),o}getFieldDomain(e,r){const t=r&&r.feature,i=this.getFeatureType(t);if(i){const r=i.domains&&i.domains[e];if(r&&\"inherited\"!==r.type)return r}return this._getLayerDomain(e)}async queryAttachments(e,r){await this.load(),e=U.from(e);const t=this.capabilities;if(!t?.data?.supportsAttachment)throw new l(\"queryAttachments:not-supported\",\"this layer doesn't support attachments\");const{attachmentTypes:i,objectIds:o,globalIds:s,num:a,size:n,start:p,where:y}=e;if(!t?.operations?.supportsQueryAttachments){if(i?.length>0||s?.length>0||n?.length>0||a||p||y)throw new l(\"queryAttachments:option-not-supported\",\"when 'capabilities.operations.supportsQueryAttachments' is false, only objectIds is supported\",e)}if(!(o?.length||s?.length||y))throw new l(\"queryAttachments:invalid-query\",\"'objectIds', 'globalIds', or 'where' are required to perform attachment query\",e);return this.queryTask.executeAttachmentQuery(e,r)}async queryFeatures(e=this.createQuery(),r){if(await this.load(),!this.capabilities.operations.supportsQuery)throw new l(\"queryFeatures:not-supported\",\"this layer doesn't support queries.\");if(!this.url)throw new l(\"queryFeatures:not-supported\",\"this layer has no url.\");const t=await this.queryTask.execute(e,{...r,query:{...this.layer?.customParameters,token:this.layer?.apiKey}});if(t?.features)for(const i of t.features)i.sourceLayer=this;return t}toExportImageJSON(e){const r={id:this.id,source:this.source?.toJSON()||{mapLayerId:this.id,type:\"mapLayer\"}},t=m(e,this.definitionExpression);c(t)&&(r.definitionExpression=t);const i=[\"renderer\",\"labelingInfo\",\"opacity\",\"labelsVisible\"].reduce(((e,r)=>(e[r]=this.originIdOf(r),e)),{}),o=Object.keys(i).some((e=>i[e]>E.SERVICE));if(o){const e=r.drawingInfo={};if(i.renderer>E.SERVICE&&(e.renderer=this.renderer?this.renderer.toJSON():null),i.labelsVisible>E.SERVICE&&(e.showLabels=this.labelsVisible),this.labelsVisible&&i.labelingInfo>E.SERVICE){!this.loaded&&this.labelingInfo.some((e=>!e.labelPlacement))&&u.getLogger(this.declaredClass).warnOnce(`A Sublayer (title: ${this.title}, id: ${this.id}) has an undefined 'labelPlacement' and so labels cannot be displayed. Either define a valid 'labelPlacement' or call Sublayer.load() to use a default value based on geometry type.`,{sublayer:this});let r=this.labelingInfo;c(this.geometryType)&&(r=V(this.labelingInfo,B.toJSON(this.geometryType))),e.labelingInfo=r.filter((e=>e.labelPlacement)).map((e=>e.toJSON({origin:\"service\",layer:this.layer}))),e.showLabels=!0}i.opacity>E.SERVICE&&(e.transparency=100-100*this.opacity),this._assignDefaultSymbolColors(e.renderer)}return r}_assignDefaultSymbolColors(e){this._forEachSimpleMarkerSymbols(e,(e=>{e.color||\"esriSMSX\"!==e.style&&\"esriSMSCross\"!==e.style||(e.outline&&e.outline.color?e.color=e.outline.color:e.color=[0,0,0,0])}))}_forEachSimpleMarkerSymbols(e,r){if(e){const t=(\"uniqueValueInfos\"in e?e.uniqueValueInfos:\"classBreakInfos\"in e?e.classBreakInfos:null)??[];for(const e of t)$(e.symbol)&&r(e.symbol);\"symbol\"in e&&$(e.symbol)&&r(e.symbol),\"defaultSymbol\"in e&&$(e.defaultSymbol)&&r(e.defaultSymbol)}}_setAndNotifyLayer(e,r){const t=this.layer,i=this._get(e);let o,s;switch(e){case\"definitionExpression\":case\"floorInfo\":o=\"supportsSublayerDefinitionExpression\";break;case\"minScale\":case\"maxScale\":case\"visible\":o=\"supportsSublayerVisibility\";break;case\"labelingInfo\":case\"labelsVisible\":case\"opacity\":case\"renderer\":case\"source\":o=\"supportsDynamicLayers\",s=\"supportsModification\"}const a=P(this).getDefaultOrigin();if(\"service\"!==a){if(o&&!1===this.layer?.capabilities?.exportMap?.[o])return void this._logLockedError(e,`capability not available 'layer.capabilities.exportMap.${o}'`);if(s&&!1===this.capabilities?.exportMap[s])return void this._logLockedError(e,`capability not available 'capabilities.exportMap.${s}'`)}\"source\"!==e||\"not-loaded\"===this.loadStatus?(this._set(e,r),\"service\"!==a&&i!==r&&t&&t.emit&&t.emit(\"sublayer-update\",{propertyName:e,target:this})):this._logLockedError(e,\"'source' can't be changed after calling sublayer.load()\")}_handleSublayersChange(e,r){r&&(r.forEach((e=>{e.parent=null,e.layer=null})),this.handles.removeAll()),e&&(e.forEach((e=>{e.parent=this,e.layer=this.layer})),this.handles.add([e.on(\"after-add\",(({item:e})=>{e.parent=this,e.layer=this.layer})),e.on(\"after-remove\",(({item:e})=>{e.parent=null,e.layer=null})),e.on(\"before-changes\",(e=>{const r=this.layer?.capabilities?.exportMap?.supportsSublayersChanges;null==r||r||(u.getLogger(this.declaredClass).error(new l(\"sublayer:sublayers-non-modifiable\",\"Sublayer can't be added, moved, or removed from the layer's sublayers\",{sublayer:this,layer:this.layer})),e.preventDefault())}))]))}_logLockedError(e,r){const{layer:t,declaredClass:i}=this;u.getLogger(i).error(new l(\"sublayer:locked\",`Property '${String(e)}' can't be changed on Sublayer from the layer '${t?.id}'`,{reason:r,sublayer:this,layer:t}))}_getLayerDomain(e){const r=this.fieldsIndex.get(e);return r?r.domain:null}};Y.test={isMapImageLayerOverridePolicy:e=>e===z||e===G,isTileImageLayerOverridePolicy:e=>e===H},e([b({readOnly:!0})],Y.prototype,\"capabilities\",void 0),e([S(\"service\",\"capabilities\",[\"layerDefinition.canModifyLayer\",\"layerDefinition.capabilities\"])],Y.prototype,\"readCapabilities\",null),e([b()],Y.prototype,\"defaultPopupTemplate\",null),e([b({type:String,value:null,json:{name:\"layerDefinition.definitionExpression\",write:{allowNull:!0,overridePolicy:G}}})],Y.prototype,\"definitionExpression\",null),e([b({type:[T],json:{origins:{service:{read:{source:\"layerDefinition.fields\"}}}}})],Y.prototype,\"fields\",void 0),e([b({readOnly:!0})],Y.prototype,\"fieldsIndex\",null),e([b({type:N,value:null,json:{name:\"layerDefinition.floorInfo\",read:{source:\"layerDefinition.floorInfo\"},write:{target:\"layerDefinition.floorInfo\",overridePolicy:G},origins:{\"web-scene\":{read:!1,write:!1}}}})],Y.prototype,\"floorInfo\",null),e([b({type:Q,json:{read:{source:\"layerDefinition.extent\"}}})],Y.prototype,\"fullExtent\",void 0),e([b({type:B.apiValues,json:{origins:{service:{name:\"layerDefinition.geometryType\",read:{reader:B.read}}}}})],Y.prototype,\"geometryType\",void 0),e([b({type:String})],Y.prototype,\"globalIdField\",void 0),e([S(\"service\",\"globalIdField\",[\"layerDefinition.globalIdField\",\"layerDefinition.fields\"])],Y.prototype,\"readGlobalIdFieldFromService\",null),e([b({type:L,json:{write:{ignoreOrigin:!0}}})],Y.prototype,\"id\",null),e([b({value:null,type:[A],json:{read:{source:\"layerDefinition.drawingInfo.labelingInfo\"},write:{target:\"layerDefinition.drawingInfo.labelingInfo\",overridePolicy:z}}})],Y.prototype,\"labelingInfo\",null),e([w(\"labelingInfo\")],Y.prototype,\"writeLabelingInfo\",null),e([b({type:Boolean,value:!0,json:{read:{source:\"layerDefinition.drawingInfo.showLabels\"},write:{target:\"layerDefinition.drawingInfo.showLabels\",overridePolicy:z}}})],Y.prototype,\"labelsVisible\",null),e([b({value:null})],Y.prototype,\"layer\",null),e([b({type:Boolean,value:!0,json:{origins:{service:{read:{enabled:!1}}},read:{source:\"showLegend\"},write:{target:\"showLegend\",overridePolicy:K}}})],Y.prototype,\"legendEnabled\",void 0),e([b({type:[\"show\",\"hide\",\"hide-children\"],value:\"show\",json:{read:!1,write:!1,origins:{\"web-scene\":{read:!0,write:!0}}}})],Y.prototype,\"listMode\",null),e([b({type:Number,value:0,json:{write:{overridePolicy:z}}})],Y.prototype,\"minScale\",null),e([S(\"minScale\",[\"minScale\",\"layerDefinition.minScale\"])],Y.prototype,\"readMinScale\",null),e([b({type:Number,value:0,json:{write:{overridePolicy:z}}})],Y.prototype,\"maxScale\",null),e([S(\"maxScale\",[\"maxScale\",\"layerDefinition.maxScale\"])],Y.prototype,\"readMaxScale\",null),e([b({readOnly:!0})],Y.prototype,\"effectiveScaleRange\",null),e([b({type:String})],Y.prototype,\"objectIdField\",void 0),e([S(\"service\",\"objectIdField\",[\"layerDefinition.objectIdField\",\"layerDefinition.fields\"])],Y.prototype,\"readObjectIdFieldFromService\",null),e([b({type:Number,value:1,json:{write:{target:\"layerDefinition.drawingInfo.transparency\",overridePolicy:z}}})],Y.prototype,\"opacity\",null),e([S(\"opacity\",[\"layerDefinition.drawingInfo.transparency\",\"layerDefinition.transparency\"])],Y.prototype,\"readOpacity\",null),e([w(\"opacity\")],Y.prototype,\"writeOpacity\",null),e([b({json:{type:L,write:{target:\"parentLayerId\",writerEnsuresNonNull:!0,overridePolicy:z}}})],Y.prototype,\"parent\",void 0),e([w(\"parent\")],Y.prototype,\"writeParent\",null),e([b({type:Boolean,value:!0,json:{read:{source:\"disablePopup\",reader:(e,r)=>!r.disablePopup},write:{target:\"disablePopup\",overridePolicy:K,writer(e,r,t){r[t]=!e}}}})],Y.prototype,\"popupEnabled\",void 0),e([b({type:r,json:{read:{source:\"popupInfo\"},write:{target:\"popupInfo\",overridePolicy:K}}})],Y.prototype,\"popupTemplate\",void 0),e([b({readOnly:!0})],Y.prototype,\"queryTask\",null),e([b({types:t,value:null,json:{name:\"layerDefinition.drawingInfo.renderer\",write:{overridePolicy:z},origins:{\"web-scene\":{types:i,name:\"layerDefinition.drawingInfo.renderer\",write:{overridePolicy:z}}}}})],Y.prototype,\"renderer\",null),e([b({types:{key:\"type\",base:null,typeMap:{\"data-layer\":C,\"map-layer\":q}},cast(e){if(e){if(\"mapLayerId\"in e)return D(q,e);if(\"dataSource\"in e)return D(C,e)}return e},json:{name:\"layerDefinition.source\",write:{overridePolicy:z}}})],Y.prototype,\"source\",null),e([b()],Y.prototype,\"sourceJSON\",void 0),e([b({value:null,json:{type:[L],write:{target:\"subLayerIds\",allowNull:!0,overridePolicy:z}}})],Y.prototype,\"sublayers\",null),e([g(\"sublayers\")],Y.prototype,\"castSublayers\",null),e([w(\"sublayers\")],Y.prototype,\"writeSublayers\",null),e([b({type:String,json:{name:\"name\",write:{overridePolicy:K}}})],Y.prototype,\"title\",void 0),e([b({type:String})],Y.prototype,\"typeIdField\",void 0),e([S(\"typeIdField\",[\"layerDefinition.typeIdField\"])],Y.prototype,\"readTypeIdField\",null),e([b({type:[O],json:{origins:{service:{read:{source:\"layerDefinition.types\"}}}}})],Y.prototype,\"types\",void 0),e([b({type:String,json:{read:{source:\"layerUrl\"},write:{target:\"layerUrl\",overridePolicy:H}}})],Y.prototype,\"url\",null),e([b({type:Boolean,value:!0,json:{read:{source:\"defaultVisibility\"},write:{target:\"defaultVisibility\",overridePolicy:z}}})],Y.prototype,\"visible\",null),e([w(\"visible\")],Y.prototype,\"writeVisible\",null),Y=J=e([I(\"esri.layers.support.Sublayer\")],Y);const Z=Y;export{Z as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import r from\"../../core/Collection.js\";import s from\"../../core/CollectionFlattener.js\";import t from\"../../core/Error.js\";import o from\"../../core/Logger.js\";import{watch as a,sync as i}from\"../../core/reactiveUtils.js\";import{property as l}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{getProperties as n}from\"../../core/accessorSupport/utils.js\";import{subclass as u}from\"../../core/accessorSupport/decorators/subclass.js\";import{OriginId as y,nameToId as c,idToName as b}from\"../../core/accessorSupport/PropertyOrigin.js\";import p from\"../support/Sublayer.js\";import{isSublayerOverhaul as d}from\"../support/sublayerUtils.js\";const f=o.getLogger(\"esri.layers.TileLayer\");function S(e,r){const s=[],t={};return e?(e.forEach((e=>{const o=new p;if(o.read(e,r),t[o.id]=o,null!=e.parentLayerId&&-1!==e.parentLayerId){const r=t[e.parentLayerId];r.sublayers||(r.sublayers=[]),r.sublayers.unshift(o)}else s.unshift(o)})),s):s}const h=r.ofType(p);function m(e,r){e&&e.forEach((e=>{r(e),e.sublayers&&e.sublayers.length&&m(e.sublayers,r)}))}const E=o=>{let E=class extends o{constructor(...e){super(...e),this.allSublayers=new s({getCollections:()=>[this.sublayers],getChildrenFunction:e=>e.sublayers}),this.sublayersSourceJSON={[y.SERVICE]:{},[y.PORTAL_ITEM]:{},[y.WEB_SCENE]:{},[y.WEB_MAP]:{}},this.addHandles(a((()=>this.sublayers),((e,r)=>this._handleSublayersChange(e,r)),i))}readSublayers(e,r){if(!r||!e)return;const{sublayersSourceJSON:s}=this,t=c(r.origin);if(t<y.SERVICE)return;if(s[t]={context:r,visibleLayers:e.visibleLayers||s[t].visibleLayers,layers:e.layers||s[t].layers},t>y.SERVICE)return;this._set(\"serviceSublayers\",this.createSublayersForOrigin(\"service\").sublayers);const{sublayers:o,origin:a}=this.createSublayersForOrigin(\"web-document\"),i=n(this);i.setDefaultOrigin(a),this._set(\"sublayers\",new h(o)),i.setDefaultOrigin(\"user\")}findSublayerById(e){return this.allSublayers.find((r=>r.id===e))}createServiceSublayers(){return this.createSublayersForOrigin(\"service\").sublayers}createSublayersForOrigin(e){const r=c(\"web-document\"===e?\"web-map\":e);let s=y.SERVICE,t=this.sublayersSourceJSON[y.SERVICE].layers,o=this.sublayersSourceJSON[y.SERVICE].context,a=null;const i=[y.PORTAL_ITEM,y.WEB_SCENE,y.WEB_MAP].filter((e=>e<=r));for(const y of i){const e=this.sublayersSourceJSON[y];d(e.layers)&&(s=y,t=e.layers,o=e.context,e.visibleLayers&&(a={visibleLayers:e.visibleLayers,context:e.context}))}const l=[y.PORTAL_ITEM,y.WEB_SCENE,y.WEB_MAP].filter((e=>e>s&&e<=r));let n=null;for(const y of l){const{layers:e,visibleLayers:r,context:s}=this.sublayersSourceJSON[y];e&&(n={layers:e,context:s}),r&&(a={visibleLayers:r,context:s})}const u=S(t,o),p=new Map,f=new Set;if(n)for(const y of n.layers)p.set(y.id,y);if(a?.visibleLayers)for(const y of a.visibleLayers)f.add(y);return m(u,(e=>{n&&e.read(p.get(e.id),n.context),a&&e.read({defaultVisibility:f.has(e.id)},a.context)})),{origin:b(s),sublayers:new h({items:u})}}read(e,r){super.read(e,r),this.readSublayers(e,r)}_handleSublayersChange(e,r){r&&(r.forEach((e=>{e.parent=null,e.layer=null})),this.handles.remove(\"sublayers-owner\")),e&&(e.forEach((e=>{e.parent=this,e.layer=this})),this.handles.add([e.on(\"after-add\",(({item:e})=>{e.parent=this,e.layer=this})),e.on(\"after-remove\",(({item:e})=>{e.parent=null,e.layer=null}))],\"sublayers-owner\"),\"tile\"===this.type&&this.handles.add(e.on(\"before-changes\",(e=>{f.error(new t(\"tilelayer:sublayers-non-modifiable\",\"ISublayer can't be added, moved, or removed from the layer's sublayers\",{layer:this})),e.preventDefault()})),\"sublayers-owner\"))}};return e([l({readOnly:!0})],E.prototype,\"allSublayers\",void 0),e([l({readOnly:!0,type:r.ofType(p)})],E.prototype,\"serviceSublayers\",void 0),e([l({value:null,type:h,json:{read:!1,write:{allowNull:!0,ignoreOrigin:!0}}})],E.prototype,\"sublayers\",void 0),e([l({readOnly:!0})],E.prototype,\"sublayersSourceJSON\",void 0),E=e([u(\"esri.layers.mixins.SublayersOwner\")],E),E};export{E as SublayersOwner,m as forEachSublayer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIosB,IAAMA,KAAE,CAAAA,OAAG;AAAC,MAAI,IAAE,cAAcA,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,eAAa,QAAO,KAAK,YAAU,MAAK,KAAK,aAAW,MAAK,KAAK,gBAAc,MAAG,KAAK,mBAAiB,MAAK,KAAK,UAAQ,QAAO,KAAK,6BAA2B,MAAK,KAAK,yBAAuB;AAAA,IAAI;AAAA,IAAC,iBAAiBC,IAAEC,IAAE;AAAC,YAAMC,KAAED,GAAE,gBAAcA,GAAE,aAAa,MAAM,GAAG,EAAE,IAAK,CAAAD,OAAGA,GAAE,YAAY,EAAE,KAAK,CAAE;AAAE,UAAG,CAACE,GAAE,QAAM,EAAC,YAAW,EAAC,mBAAkB,OAAG,qBAAoB,OAAG,kBAAiB,OAAG,eAAc,OAAG,iBAAgB,MAAE,GAAE,WAAU,MAAK,aAAY,KAAI;AAAE,YAAMC,KAAE,KAAK,MAAK,IAAE,WAASA,MAAG,CAAC,CAACF,GAAE,uBAAsBG,KAAEF,GAAE,SAAS,OAAO,GAAEG,KAAEH,GAAE,SAAS,KAAK,GAAEI,KAAE,CAAC,CAACL,GAAE,oBAAmBM,KAAEL,GAAE,SAAS,SAAS,GAAE,IAAEA,GAAE,SAAS,MAAM,GAAEM,KAAE,WAASL,OAAI,CAACF,GAAE,YAAU,IAAGF,KAAE,WAASI,OAAI,CAACF,GAAE,YAAU,IAAGQ,KAAE,WAASN,IAAEO,KAAET,GAAE,cAAYA,GAAE,MAAMA,GAAE,UAAU,GAAEU,MAAED,MAAA,gBAAAA,GAAG,MAAM,GAAE,OAAI,OAAGE,MAAEF,MAAA,gBAAAA,GAAG,MAAM,GAAE,OAAI;AAAG,aAAM,EAAC,YAAW,EAAC,mBAAkBL,IAAE,qBAAoBC,IAAE,kBAAiBF,IAAE,eAAc,GAAE,iBAAgBG,GAAC,GAAE,WAAUF,KAAE,EAAC,qCAAoCM,IAAE,0BAAyBF,IAAE,uBAAsB,GAAE,4BAA2BD,IAAE,sCAAqCT,IAAE,oBAAmBa,GAAC,IAAE,MAAK,aAAYN,KAAE,EAAC,qBAAoB,CAACL,GAAE,oBAAmB,IAAE,KAAI;AAAA,IAAC;AAAA,IAAC,YAAYD,IAAEC,IAAE;AAAC,UAAIC,KAAED,GAAE;AAAe,aAAOC,OAAIA,KAAED,GAAE,eAAe,cAAc,KAAGA,GAAE,eAAe,QAAQ,IAAE,KAAGA,GAAE,eAAe,2BAA2B,IAAE,OAAK,MAAKC;AAAA,IAAC;AAAA,IAAC,MAAM,kBAAkBF,IAAEC,IAAE;AAJpmE;AAIqmE,UAAG;AAAC,eAAO,MAAM,KAAK,wBAAwBA,EAAC,IAAE,UAAK,2BAAL,mBAA6B,IAAID;AAAA,MAAE,QAAM;AAAC;AAAA,MAAM;AAAA,IAAC;AAAA,IAAC,MAAM,wBAAwBA,IAAE;AAAC,YAAM,KAAK,KAAKA,EAAC,GAAE,KAAK,+BAA6B,KAAK,6BAA2Ba,GAAE,EAAE,KAAK,GAAG,EAAE,OAAK,WAAU,EAAC,cAAa,QAAO,OAAM,EAAC,GAAE,QAAO,GAAG,KAAK,kBAAiB,OAAM,KAAK,OAAM,EAAC,CAAC,EAAE,KAAM,CAAAb,OAAG;AAAC,aAAK,yBAAuB,oBAAI;AAAI,mBAAUC,MAAKD,GAAE,KAAK,OAAO,MAAK,uBAAuB,IAAIC,GAAE,IAAGA,EAAC;AAAE,eAAM,EAAC,QAAOD,GAAE,KAAI;AAAA,MAAC,GAAI,CAAAA,QAAI,EAAC,OAAMA,GAAC,EAAG;AAAG,YAAMc,KAAE,MAAM,KAAK;AAA2B,UAAG,EAAEd,EAAC,GAAE,YAAWc,GAAE,QAAOA,GAAE;AAAO,YAAMA,GAAE;AAAA,IAAK;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,gBAAe,CAAC,gBAAe,sBAAqB,uBAAsB,yBAAwB,UAAU,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,gBAAe,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAEC,EAAC,CAAC,GAAE,EAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,SAAQ,MAAE,EAAC,EAAC,GAAE,MAAK,EAAC,QAAO,aAAY,GAAE,OAAM,EAAC,QAAO,aAAY,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAEX,EAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKY,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,CAAC,kBAAiB,gBAAe,UAAS,2BAA2B,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,GAAE,CAAC,GAAE;AAAC;;;ACA57B,IAAI;AAAE,SAAS,EAAEC,IAAE;AAAC,SAAO,QAAMA,MAAG,cAAYA,GAAE;AAAI;AAAC,SAAS,EAAEA,IAAEC,IAAEC,IAAE;AAJ9pF;AAI+pF,QAAM,IAAE,KAAK,WAAWD,EAAC,KAAG,EAAEC,GAAE,MAAM;AAAE,SAAM,EAAC,cAAa,MAAG,WAAU,GAAE,SAAQ,CAAC,CAACA,OAAI,kBAAc,KAAAA,GAAE,UAAF,mBAAS,UAAOA,GAAE,0BAAwB,IAAG;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAEC,IAAE;AAJt0F;AAIu0F,SAAM,EAAC,SAAQ,CAAC,CAACA,OAAI,aAAS,KAAAA,GAAE,UAAF,mBAAS,SAAM,KAAK,cAAcD,EAAC,GAAE;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,SAAM,EAAC,cAAa,MAAG,SAAQA,MAAGA,GAAE,0BAAwB,MAAE;AAAC;AAAC,SAASC,GAAEH,IAAEC,IAAEC,IAAE;AAAC,SAAM,EAAC,cAAa,MAAG,SAAQ,CAAC,CAACA,OAAIA,GAAE,0BAAwB,KAAK,WAAWD,EAAC,KAAG,EAAEC,GAAE,MAAM,GAAE;AAAC;AAAC,IAAI,IAAE;AAAE,IAAM,IAAE,oBAAI;AAAI,EAAE,IAAI,OAAO,GAAE,EAAE,IAAI,QAAQ,GAAE,EAAE,IAAI,QAAQ,GAAE,EAAE,IAAI,YAAY,GAAE,EAAE,IAAI,WAAW,GAAE,EAAE,IAAI,cAAc;AAAE,IAAI,IAAE,IAAE,cAAcE,GAAE,EAAEC,GAAE,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYL,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,QAAO,KAAK,SAAO,MAAK,KAAK,aAAW,MAAK,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAK,KAAK,gBAAc,MAAG,KAAK,gBAAc,MAAK,KAAK,eAAa,MAAG,KAAK,gBAAc,MAAK,KAAK,aAAW,MAAK,KAAK,QAAM,MAAK,KAAK,cAAY,MAAK,KAAK,QAAM,MAAK,KAAK,iBAAe;AAAA,EAAI;AAAA,EAAC,MAAM,KAAKA,IAAE;AAAC,WAAO,KAAK,qBAAqB,YAAS;AAAC,YAAK,EAAC,OAAMC,IAAE,QAAOC,IAAE,KAAI,EAAC,IAAE;AAAK,UAAG,CAACD,MAAG,CAAC,EAAE,OAAM,IAAII,GAAE,0BAAyB,0DAAyD,EAAC,UAAS,KAAI,CAAC;AAAE,UAAIA,KAAE;AAAK,UAAG,CAACJ,MAAG,KAAK,WAAW,KAAK,IAAEA,GAAE,WAAS,kBAAeC,MAAA,gBAAAA,GAAG,OAAK;AAAC,QAAAG,MAAG,MAAMC,GAAE,GAAE,EAAC,cAAa,QAAO,OAAM,EAAC,GAAE,OAAM,GAAE,GAAGN,GAAC,CAAC,GAAG;AAAA,MAAI,OAAK;AAAC,YAAIO,KAAE,KAAK;AAAG,yBAAcL,MAAA,gBAAAA,GAAG,UAAOK,KAAEL,GAAE,aAAYG,KAAE,MAAMJ,GAAE,kBAAkBM,IAAEP,EAAC;AAAA,MAAC;AAAC,MAAAK,OAAI,KAAK,aAAWA,IAAE,KAAK,KAAK,EAAC,iBAAgBA,GAAC,GAAE,EAAC,QAAO,UAAS,CAAC;AAAA,IAAE,GAAG,CAAC,GAAE;AAAA,EAAI;AAAA,EAAC,iBAAiBL,IAAEC,IAAE;AAAC,IAAAA,KAAEA,GAAE,mBAAiBA;AAAE,UAAK,EAAC,YAAW,EAAC,eAAcC,IAAE,0BAAyB,EAAC,GAAE,OAAM,EAAC,mBAAkBM,GAAC,GAAE,MAAK,EAAC,oBAAmBH,GAAC,EAAC,IAAEI,GAAER,IAAE,KAAK,GAAG;AAAE,WAAM,EAAC,WAAU,EAAC,sBAAqB,CAAC,CAACA,GAAE,eAAc,GAAE,YAAW,EAAC,eAAcC,IAAE,0BAAyB,EAAC,GAAE,MAAK,EAAC,oBAAmBG,GAAC,GAAE,OAAM,EAAC,mBAAkBG,GAAC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,IAAI,qBAAqBR,IAAE;AAAC,SAAK,mBAAmB,wBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,IAAIC,GAAE,KAAK,UAAQ,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAUD,IAAE;AAAC,SAAK,mBAAmB,aAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BA,IAAEC,IAAE;AAAC,SAAIA,KAAEA,GAAE,mBAAiBA,IAAG,cAAc,QAAOA,GAAE;AAAc,QAAGA,GAAE;AAAO,iBAAUC,MAAKD,GAAE,OAAO,KAAG,4BAA0BC,GAAE,KAAK,QAAOA,GAAE;AAAA;AAAA,EAAI;AAAA,EAAC,IAAI,KAAI;AAAC,UAAMF,KAAE,KAAK,KAAK,IAAI;AAAE,WAAOA,MAAG;AAAA,EAAG;AAAA,EAAC,IAAI,GAAGA,IAAE;AAJ94J;AAI+4J,SAAK,KAAK,IAAI,MAAIA,OAAI,YAAK,sBAAK,UAAL,mBAAY,iBAAZ,mBAA0B,cAA1B,mBAAqC,yBAAsB,KAAK,KAAK,MAAKA,EAAC,IAAE,KAAK,gBAAgB,MAAK,+EAA+E;AAAA,EAAE;AAAA,EAAC,IAAI,aAAaA,IAAE;AAAC,SAAK,mBAAmB,gBAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,IAAEC,IAAE,GAAE;AAAC,IAAAF,MAAGA,GAAE,WAASC,GAAE,kBAAgB,EAAC,aAAY,EAAC,cAAaD,GAAE,IAAK,CAAAA,OAAGA,GAAE,MAAM,CAAC,GAAE,CAAC,CAAE,EAAC,EAAC;AAAA,EAAE;AAAA,EAAC,IAAI,cAAcA,IAAE;AAAC,SAAK,mBAAmB,iBAAgBA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,KAAK,SAAQA,EAAC,GAAE,KAAK,aAAW,KAAK,UAAU,QAAS,CAAAC,OAAGA,GAAE,QAAMD,EAAE;AAAA,EAAC;AAAA,EAAC,IAAI,SAASA,IAAE;AAAC,SAAK,KAAK,YAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAASA,IAAE;AAAC,SAAK,mBAAmB,YAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAE;AAAC,WAAOA,GAAE,YAAUA,GAAE,mBAAiBA,GAAE,gBAAgB,YAAU;AAAA,EAAC;AAAA,EAAC,IAAI,SAASD,IAAE;AAAC,SAAK,mBAAmB,YAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAE;AAAC,WAAOA,GAAE,YAAUA,GAAE,mBAAiBA,GAAE,gBAAgB,YAAU;AAAA,EAAC;AAAA,EAAC,IAAI,sBAAqB;AAAC,UAAK,EAAC,UAASD,IAAE,UAASC,GAAC,IAAE;AAAK,WAAM,EAAC,UAASD,IAAE,UAASC,GAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BD,IAAEC,IAAE;AAAC,SAAIA,KAAEA,GAAE,mBAAiBA,IAAG,cAAc,QAAOA,GAAE;AAAc,QAAGA,GAAE;AAAO,iBAAUC,MAAKD,GAAE,OAAO,KAAG,uBAAqBC,GAAE,KAAK,QAAOA,GAAE;AAAA;AAAA,EAAI;AAAA,EAAC,IAAI,QAAQF,IAAE;AAAC,SAAK,mBAAmB,WAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAJ/jM;AAIgkM,UAAMC,KAAED,GAAE;AAAgB,WAAO,IAAE,SAAM,SAAMC,MAAA,gBAAAA,GAAG,gBAAaA,GAAE,gBAAa,KAAAA,MAAA,gBAAAA,GAAG,gBAAH,mBAAgB,iBAAe;AAAA,EAAE;AAAA,EAAC,aAAaF,IAAEC,IAAEC,IAAE,GAAE;AAAC,IAAAD,GAAE,kBAAgB,EAAC,aAAY,EAAC,cAAa,MAAI,MAAID,GAAC,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,UAAQ,KAAK,WAAS,KAAK,QAAMA,GAAE,gBAAcI,GAAE,KAAK,OAAO,EAAE,IAAEJ,GAAE,gBAAc;AAAA,EAAE;AAAA,EAAC,IAAI,YAAW;AAJ13M;AAI23M,QAAG,CAAC,KAAK,MAAM,QAAO;AAAK,UAAK,EAAC,kBAAiBD,GAAC,IAAE,KAAK,OAAMC,KAAE,gBAAe,KAAK,QAAM,KAAK,MAAM,aAAW,QAAO,EAAC,cAAaC,IAAE,aAAY,EAAC,IAAE,MAAKM,KAAE,IAAI,kBAAkB,MAAGN,MAAA,gBAAAA,GAAG,MAAM,oBAAkBG,OAAE,KAAAH,MAAA,gBAAAA,GAAG,eAAH,mBAAe,6BAA0B;AAAG,WAAO,IAAIQ,GAAE,EAAC,KAAI,KAAK,KAAI,cAAaF,IAAE,aAAY,GAAE,YAAWP,IAAE,wBAAuBD,IAAE,2BAA0BK,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAASL,IAAE;AAAC,QAAGA;AAAE,iBAAUC,MAAKD,GAAE,WAAW,EAAE,KAAGU,GAAET,EAAC,GAAE;AAAC,UAAE,UAAU,KAAK,aAAa,EAAE,KAAK,yCAAyC;AAAE;AAAA,MAAK;AAAA;AAAC,SAAK,mBAAmB,YAAWD,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,KAAK,QAAQ,KAAG,IAAI,EAAE,EAAC,YAAW,KAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAOA,IAAE;AAAC,SAAK,mBAAmB,UAASA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAUA,IAAE;AAAC,SAAK,uBAAuBA,IAAE,KAAK,KAAK,WAAW,CAAC,GAAE,KAAK,KAAK,aAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,WAAO,EAAE,EAAE,OAAO,CAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,IAAEC,IAAE;AAJrsO;AAIssO,gBAAK,cAAL,mBAAgB,YAASD,GAAEC,EAAC,IAAE,KAAK,UAAU,IAAK,CAAAF,OAAGA,GAAE,EAAG,EAAE,QAAQ,EAAE,QAAQ;AAAA,EAAE;AAAA,EAAC,gBAAgBA,IAAEC,IAAE;AAAC,QAAIC,MAAGD,KAAEA,GAAE,mBAAiBA,IAAG;AAAY,QAAGC,MAAGD,GAAE,QAAO;AAAC,MAAAC,KAAEA,GAAE,YAAY;AAAE,YAAMF,KAAEC,GAAE,OAAO,KAAM,CAAAD,OAAGA,GAAE,KAAK,YAAY,MAAIE,EAAE;AAAE,MAAAF,OAAIE,KAAEF,GAAE;AAAA,IAAK;AAAC,WAAOE;AAAA,EAAC;AAAA,EAAC,IAAI,MAAK;AAJ98O;AAI+8O,UAAMF,OAAE,UAAK,UAAL,mBAAY,cAAW,KAAK,gBAAeC,KAAE,KAAK;AAAO,QAAG,CAACD,GAAE,QAAO;AAAK,QAAG,KAAK,iBAAeA,IAAE,iBAAcC,MAAA,gBAAAA,GAAG,MAAK,QAAM,GAAGD,GAAE,IAAI,IAAIC,GAAE,UAAU;AAAG,UAAMC,KAAE,EAAC,OAAM,KAAK,UAAU,EAAC,QAAO,KAAK,OAAM,CAAC,EAAC;AAAE,WAAM,GAAGF,GAAE,IAAI,iBAAiB,EAAEE,EAAC,CAAC;AAAA,EAAE;AAAA,EAAC,IAAI,IAAIF,IAAE;AAAC,SAAK,gBAAgB,OAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAQA,IAAE;AAAC,SAAK,mBAAmB,WAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAEC,IAAE,GAAE;AAAC,IAAAD,GAAEC,EAAC,IAAE,KAAK,YAAY,qBAAoB,SAAS,KAAGF;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAK,EAAC,OAAMA,GAAC,IAAEA,GAAE,IAAI,GAAEC,KAAE,IAAI;AAAE,WAAOD,GAAEC,EAAC,EAAE,QAAMD,GAAE,MAAM,CAAC,GAAE,KAAK,eAAe,KAAK,GAAEC,GAAE,iBAAe,KAAK,gBAAeA;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAE;AAAC,WAAOW,GAAE,MAAKX,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,IAAI,EAAE,EAAC,gBAAe,MAAG,OAAM,KAAK,wBAAsB,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAoB;AAJzpQ;AAI0pQ,QAAG,KAAK,eAAe,WAAW,EAAE,QAAO;AAAK,UAAK,EAAC,OAAMA,GAAC,IAAE,MAAKC,KAAED,MAAA,gBAAAA,GAAG,WAAUE,KAAE,KAAI,IAAG,MAAM,OAAO,2CAAoB,GAAG,SAAS,EAAC,KAAID,MAAA,gBAAAA,GAAG,KAAI,CAAC;AAAE,WAAOA,MAAG,KAAK,WAAS,gBAAc,KAAK,OAAO,OAAKC,GAAE,UAAQ,KAAK,OAAO,aAAWA,GAAE,oBAAkB,KAAK,SAAQ,SAAMF,MAAA,gBAAAA,GAAG,qBAAkBE,GAAE,kBAAgBF,GAAE,kBAAiB,KAAK,yBAAuBE,GAAE,uBAAqB,KAAK,uBAAsB,KAAK,cAAYA,GAAE,YAAU,EAAE,KAAK,SAAS,IAAG,KAAK,WAAW,cAAc,IAAED,GAAE,YAAUC,GAAE,eAAa,EAAE,KAAK,YAAY,IAAG,KAAK,WAAW,eAAe,IAAED,GAAE,aAAWC,GAAE,gBAAc,KAAK,gBAAe,KAAK,WAAW,eAAe,IAAED,GAAE,aAAWC,GAAE,gBAAc,KAAK,gBAAe,KAAK,WAAW,SAAS,IAAED,GAAE,aAAWC,GAAE,UAAQ,KAAK,UAAS,KAAK,WAAW,UAAU,IAAED,GAAE,aAAWC,GAAE,WAAS,KAAK,WAAU,KAAK,WAAW,UAAU,IAAED,GAAE,aAAWC,GAAE,WAAS,KAAK,WAAU,KAAK,WAAW,SAAS,IAAED,GAAE,aAAWC,GAAE,UAAQ,KAAK,UAAS,KAAK,WAAW,eAAe,IAAED,GAAE,aAAWC,GAAE,gBAAc,EAAE,KAAK,aAAa,IAAG,KAAK,WAAW,UAAU,IAAED,GAAE,YAAUC,GAAE,WAAS,EAAE,KAAK,QAAQ,IAAG,mBAAe,UAAK,WAAL,mBAAa,UAAOA,GAAE,oBAAkB,KAAK,OAAO,MAAM,IAAG,KAAK,WAAW,OAAO,IAAED,GAAE,aAAWC,GAAE,QAAM,KAAK,QAAO,iBAAcF,MAAA,gBAAAA,GAAG,SAAMA,GAAE,WAAW,kBAAkB,IAAEC,GAAE,aAAWC,GAAE,mBAAiBF,GAAE,mBAAkB,YAASA,MAAA,gBAAAA,GAAG,SAAMA,GAAE,WAAW,kBAAkB,IAAEC,GAAE,aAAWC,GAAE,mBAAiBF,GAAE,mBAAkBE;AAAA,EAAC;AAAA,EAAC,SAASF,IAAE;AAAC,WAAO,KAAK,YAAY,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,UAAK,EAAC,aAAYC,IAAE,OAAMC,GAAC,IAAE;AAAK,QAAG,CAACD,MAAG,CAACD,GAAE,QAAO;AAAK,UAAM,IAAEA,GAAE,aAAWA,GAAE,WAAWC,EAAC,IAAE;AAAO,QAAG,QAAM,EAAE,QAAO;AAAK,QAAIO,KAAE;AAAK,WAAON,MAAA,gBAAAA,GAAG,KAAM,CAAAF,OAAG;AAAC,YAAK,EAAC,IAAGC,GAAC,IAAED;AAAE,aAAO,QAAMC,OAAIA,GAAE,SAAS,MAAI,EAAE,SAAS,MAAIO,KAAER,KAAG,CAAC,CAACQ;AAAA,IAAE,IAAIA;AAAA,EAAC;AAAA,EAAC,eAAeR,IAAEC,IAAE;AAAC,UAAMC,KAAED,MAAGA,GAAE,SAAQ,IAAE,KAAK,eAAeC,EAAC;AAAE,QAAG,GAAE;AAAC,YAAMD,KAAE,EAAE,WAAS,EAAE,QAAQD,EAAC;AAAE,UAAGC,MAAG,gBAAcA,GAAE,KAAK,QAAOA;AAAA,IAAC;AAAC,WAAO,KAAK,gBAAgBD,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBA,IAAEC,IAAE;AAJlkU;AAImkU,UAAM,KAAK,KAAK,GAAED,KAAEY,GAAE,KAAKZ,EAAC;AAAE,UAAME,KAAE,KAAK;AAAa,QAAG,GAAC,KAAAA,MAAA,gBAAAA,GAAG,SAAH,mBAAS,oBAAmB,OAAM,IAAIG,GAAE,kCAAiC,wCAAwC;AAAE,UAAK,EAAC,iBAAgB,GAAE,WAAUG,IAAE,WAAUH,IAAE,KAAID,IAAE,MAAKK,IAAE,OAAME,IAAE,OAAME,GAAC,IAAEb;AAAE,QAAG,GAAC,KAAAE,MAAA,gBAAAA,GAAG,eAAH,mBAAe,2BAAyB;AAAC,WAAG,uBAAG,UAAO,MAAGG,MAAA,gBAAAA,GAAG,UAAO,MAAGI,MAAA,gBAAAA,GAAG,UAAO,KAAGL,MAAGO,MAAGE,GAAE,OAAM,IAAIR,GAAE,yCAAwC,iGAAgGL,EAAC;AAAA,IAAC;AAAC,QAAG,GAAEQ,MAAA,gBAAAA,GAAG,YAAQH,MAAA,gBAAAA,GAAG,WAAQQ,IAAG,OAAM,IAAIR,GAAE,kCAAiC,iFAAgFL,EAAC;AAAE,WAAO,KAAK,UAAU,uBAAuBA,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcD,KAAE,KAAK,YAAY,GAAEC,IAAE;AAJnzV;AAIozV,QAAG,MAAM,KAAK,KAAK,GAAE,CAAC,KAAK,aAAa,WAAW,cAAc,OAAM,IAAII,GAAE,+BAA8B,qCAAqC;AAAE,QAAG,CAAC,KAAK,IAAI,OAAM,IAAIA,GAAE,+BAA8B,wBAAwB;AAAE,UAAMH,KAAE,MAAM,KAAK,UAAU,QAAQF,IAAE,EAAC,GAAGC,IAAE,OAAM,EAAC,IAAG,UAAK,UAAL,mBAAY,kBAAiB,QAAM,UAAK,UAAL,mBAAY,OAAM,EAAC,CAAC;AAAE,QAAGC,MAAA,gBAAAA,GAAG,SAAS,YAAU,KAAKA,GAAE,SAAS,GAAE,cAAY;AAAK,WAAOA;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAE;AAJhuW;AAIiuW,UAAMC,KAAE,EAAC,IAAG,KAAK,IAAG,UAAO,UAAK,WAAL,mBAAa,aAAU,EAAC,YAAW,KAAK,IAAG,MAAK,WAAU,EAAC,GAAEC,KAAEA,GAAEF,IAAE,KAAK,oBAAoB;AAAE,MAAEE,EAAC,MAAID,GAAE,uBAAqBC;AAAG,UAAM,IAAE,CAAC,YAAW,gBAAe,WAAU,eAAe,EAAE,OAAQ,CAACF,IAAEC,QAAKD,GAAEC,EAAC,IAAE,KAAK,WAAWA,EAAC,GAAED,KAAI,CAAC,CAAC,GAAEQ,KAAE,OAAO,KAAK,CAAC,EAAE,KAAM,CAAAR,OAAG,EAAEA,EAAC,IAAEC,GAAE,OAAQ;AAAE,QAAGO,IAAE;AAAC,YAAMR,KAAEC,GAAE,cAAY,CAAC;AAAE,UAAG,EAAE,WAASA,GAAE,YAAUD,GAAE,WAAS,KAAK,WAAS,KAAK,SAAS,OAAO,IAAE,OAAM,EAAE,gBAAcC,GAAE,YAAUD,GAAE,aAAW,KAAK,gBAAe,KAAK,iBAAe,EAAE,eAAaC,GAAE,SAAQ;AAAC,SAAC,KAAK,UAAQ,KAAK,aAAa,KAAM,CAAAD,OAAG,CAACA,GAAE,cAAe,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,SAAS,sBAAsB,KAAK,KAAK,SAAS,KAAK,EAAE,wLAAuL,EAAC,UAAS,KAAI,CAAC;AAAE,YAAIC,KAAE,KAAK;AAAa,UAAE,KAAK,YAAY,MAAIA,KAAEW,GAAE,KAAK,cAAaJ,GAAE,OAAO,KAAK,YAAY,CAAC,IAAGR,GAAE,eAAaC,GAAE,OAAQ,CAAAD,OAAGA,GAAE,cAAe,EAAE,IAAK,CAAAA,OAAGA,GAAE,OAAO,EAAC,QAAO,WAAU,OAAM,KAAK,MAAK,CAAC,CAAE,GAAEA,GAAE,aAAW;AAAA,MAAE;AAAC,QAAE,UAAQC,GAAE,YAAUD,GAAE,eAAa,MAAI,MAAI,KAAK,UAAS,KAAK,2BAA2BA,GAAE,QAAQ;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,2BAA2BD,IAAE;AAAC,SAAK,4BAA4BA,IAAG,CAAAA,OAAG;AAAC,MAAAA,GAAE,SAAO,eAAaA,GAAE,SAAO,mBAAiBA,GAAE,UAAQA,GAAE,WAASA,GAAE,QAAQ,QAAMA,GAAE,QAAMA,GAAE,QAAQ,QAAMA,GAAE,QAAM,CAAC,GAAE,GAAE,GAAE,CAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,4BAA4BA,IAAEC,IAAE;AAAC,QAAGD,IAAE;AAAC,YAAME,MAAG,sBAAqBF,KAAEA,GAAE,mBAAiB,qBAAoBA,KAAEA,GAAE,kBAAgB,SAAO,CAAC;AAAE,iBAAUA,MAAKE,GAAE,GAAEF,GAAE,MAAM,KAAGC,GAAED,GAAE,MAAM;AAAE,kBAAWA,MAAG,EAAEA,GAAE,MAAM,KAAGC,GAAED,GAAE,MAAM,GAAE,mBAAkBA,MAAG,EAAEA,GAAE,aAAa,KAAGC,GAAED,GAAE,aAAa;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEC,IAAE;AAJj5Z;AAIk5Z,UAAMC,KAAE,KAAK,OAAM,IAAE,KAAK,KAAKF,EAAC;AAAE,QAAIQ,IAAEH;AAAE,YAAOL,IAAE;AAAA,MAAC,KAAI;AAAA,MAAuB,KAAI;AAAY,QAAAQ,KAAE;AAAuC;AAAA,MAAM,KAAI;AAAA,MAAW,KAAI;AAAA,MAAW,KAAI;AAAU,QAAAA,KAAE;AAA6B;AAAA,MAAM,KAAI;AAAA,MAAe,KAAI;AAAA,MAAgB,KAAI;AAAA,MAAU,KAAI;AAAA,MAAW,KAAI;AAAS,QAAAA,KAAE,yBAAwBH,KAAE;AAAA,IAAsB;AAAC,UAAMD,KAAEJ,GAAE,IAAI,EAAE,iBAAiB;AAAE,QAAG,cAAYI,IAAE;AAAC,UAAGI,MAAG,YAAK,sBAAK,UAAL,mBAAY,iBAAZ,mBAA0B,cAA1B,mBAAsCA,KAAG,QAAO,KAAK,KAAK,gBAAgBR,IAAE,0DAA0DQ,EAAC,GAAG;AAAE,UAAGH,MAAG,YAAK,UAAK,iBAAL,mBAAmB,UAAUA,KAAG,QAAO,KAAK,KAAK,gBAAgBL,IAAE,oDAAoDK,EAAC,GAAG;AAAA,IAAC;AAAC,iBAAWL,MAAG,iBAAe,KAAK,cAAY,KAAK,KAAKA,IAAEC,EAAC,GAAE,cAAYG,MAAG,MAAIH,MAAGC,MAAGA,GAAE,QAAMA,GAAE,KAAK,mBAAkB,EAAC,cAAaF,IAAE,QAAO,KAAI,CAAC,KAAG,KAAK,gBAAgBA,IAAE,yDAAyD;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAEC,IAAE;AAAC,IAAAA,OAAIA,GAAE,QAAS,CAAAD,OAAG;AAAC,MAAAA,GAAE,SAAO,MAAKA,GAAE,QAAM;AAAA,IAAI,CAAE,GAAE,KAAK,QAAQ,UAAU,IAAGA,OAAIA,GAAE,QAAS,CAAAA,OAAG;AAAC,MAAAA,GAAE,SAAO,MAAKA,GAAE,QAAM,KAAK;AAAA,IAAK,CAAE,GAAE,KAAK,QAAQ,IAAI,CAACA,GAAE,GAAG,aAAa,CAAC,EAAC,MAAKA,GAAC,MAAI;AAAC,MAAAA,GAAE,SAAO,MAAKA,GAAE,QAAM,KAAK;AAAA,IAAK,CAAE,GAAEA,GAAE,GAAG,gBAAgB,CAAC,EAAC,MAAKA,GAAC,MAAI;AAAC,MAAAA,GAAE,SAAO,MAAKA,GAAE,QAAM;AAAA,IAAI,CAAE,GAAEA,GAAE,GAAG,kBAAkB,CAAAA,OAAG;AAJ9nc;AAI+nc,YAAMC,MAAE,sBAAK,UAAL,mBAAY,iBAAZ,mBAA0B,cAA1B,mBAAqC;AAAyB,cAAMA,MAAGA,OAAI,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAII,GAAE,qCAAoC,yEAAwE,EAAC,UAAS,MAAK,OAAM,KAAK,MAAK,CAAC,CAAC,GAAEL,GAAE,eAAe;AAAA,IAAE,CAAE,CAAC,CAAC;AAAA,EAAE;AAAA,EAAC,gBAAgBA,IAAEC,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,eAAc,EAAC,IAAE;AAAK,MAAE,UAAU,CAAC,EAAE,MAAM,IAAIG,GAAE,mBAAkB,aAAa,OAAOL,EAAC,CAAC,kDAAkDE,MAAA,gBAAAA,GAAG,EAAE,KAAI,EAAC,QAAOD,IAAE,UAAS,MAAK,OAAMC,GAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAE;AAAC,UAAMC,KAAE,KAAK,YAAY,IAAID,EAAC;AAAE,WAAOC,KAAEA,GAAE,SAAO;AAAA,EAAI;AAAC;AAAE,EAAE,OAAK,EAAC,+BAA8B,CAAAD,OAAGA,OAAI,KAAGA,OAAI,GAAE,gCAA+B,CAAAA,OAAGA,OAAI,EAAC,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,gBAAe,CAAC,kCAAiC,8BAA8B,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,MAAK,MAAK,EAAC,MAAK,wCAAuC,OAAM,EAAC,WAAU,MAAG,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACa,EAAC,GAAE,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,yBAAwB,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKF,IAAE,OAAM,MAAK,MAAK,EAAC,MAAK,6BAA4B,MAAK,EAAC,QAAO,4BAA2B,GAAE,OAAM,EAAC,QAAO,6BAA4B,gBAAe,EAAC,GAAE,SAAQ,EAAC,aAAY,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,yBAAwB,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKH,GAAE,WAAU,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,gCAA+B,MAAK,EAAC,QAAOA,GAAE,KAAI,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,iBAAgB,CAAC,iCAAgC,wBAAwB,CAAC,CAAC,GAAE,EAAE,WAAU,gCAA+B,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,EAAC,cAAa,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,MAAK,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAK,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,2CAA0C,GAAE,OAAM,EAAC,QAAO,4CAA2C,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAACP,GAAE,cAAc,CAAC,GAAE,EAAE,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,OAAM,MAAG,MAAK,EAAC,MAAK,EAAC,QAAO,yCAAwC,GAAE,OAAM,EAAC,QAAO,0CAAyC,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,KAAI,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,OAAM,MAAG,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,SAAQ,MAAE,EAAC,EAAC,GAAE,MAAK,EAAC,QAAO,aAAY,GAAE,OAAM,EAAC,QAAO,cAAa,gBAAeE,GAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,QAAO,eAAe,GAAE,OAAM,QAAO,MAAK,EAAC,MAAK,OAAG,OAAM,OAAG,SAAQ,EAAC,aAAY,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,GAAE,MAAK,EAAC,OAAM,EAAC,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,YAAW,CAAC,YAAW,0BAA0B,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,GAAE,MAAK,EAAC,OAAM,EAAC,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,YAAW,CAAC,YAAW,0BAA0B,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,uBAAsB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,iBAAgB,CAAC,iCAAgC,wBAAwB,CAAC,CAAC,GAAE,EAAE,WAAU,gCAA+B,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,GAAE,MAAK,EAAC,OAAM,EAAC,QAAO,4CAA2C,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,WAAU,CAAC,4CAA2C,8BAA8B,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAACF,GAAE,SAAS,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,GAAE,OAAM,EAAC,QAAO,iBAAgB,sBAAqB,MAAG,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACA,GAAE,QAAQ,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,OAAM,MAAG,MAAK,EAAC,MAAK,EAAC,QAAO,gBAAe,QAAO,CAACD,IAAEC,OAAI,CAACA,GAAE,aAAY,GAAE,OAAM,EAAC,QAAO,gBAAe,gBAAeE,IAAE,OAAOH,IAAEC,IAAEC,IAAE;AAAC,EAAAD,GAAEC,EAAC,IAAE,CAACF;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,YAAW,GAAE,OAAM,EAAC,QAAO,aAAY,gBAAeG,GAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMQ,IAAE,OAAM,MAAK,MAAK,EAAC,MAAK,wCAAuC,OAAM,EAAC,gBAAe,EAAC,GAAE,SAAQ,EAAC,aAAY,EAAC,OAAMF,IAAE,MAAK,wCAAuC,OAAM,EAAC,gBAAe,EAAC,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,EAAC,KAAI,QAAO,MAAK,MAAK,SAAQ,EAAC,cAAa,GAAE,aAAY,EAAC,EAAC,GAAE,KAAKT,IAAE;AAAC,MAAGA,IAAE;AAAC,QAAG,gBAAeA,GAAE,QAAO,EAAE,GAAEA,EAAC;AAAE,QAAG,gBAAeA,GAAE,QAAO,EAAE,GAAEA,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC,GAAE,MAAK,EAAC,MAAK,0BAAyB,OAAM,EAAC,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAK,MAAK,EAAC,MAAK,CAAC,CAAC,GAAE,OAAM,EAAC,QAAO,eAAc,WAAU,MAAG,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAACK,GAAE,WAAW,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAACJ,GAAE,WAAW,CAAC,GAAE,EAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,QAAO,OAAM,EAAC,gBAAeE,GAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,eAAc,CAAC,6BAA6B,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACM,EAAC,GAAE,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,wBAAuB,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAC,QAAO,WAAU,GAAE,OAAM,EAAC,QAAO,YAAW,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,OAAM,MAAG,MAAK,EAAC,MAAK,EAAC,QAAO,oBAAmB,GAAE,OAAM,EAAC,QAAO,qBAAoB,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAACR,GAAE,SAAS,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,8BAA8B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACAtjmB,IAAMa,KAAE,EAAE,UAAU,uBAAuB;AAAE,SAAS,EAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,SAAOH,MAAGA,GAAE,QAAS,CAAAA,OAAG;AAAC,UAAMI,KAAE,IAAI;AAAE,QAAGA,GAAE,KAAKJ,IAAEC,EAAC,GAAEE,GAAEC,GAAE,EAAE,IAAEA,IAAE,QAAMJ,GAAE,iBAAe,OAAKA,GAAE,eAAc;AAAC,YAAMC,KAAEE,GAAEH,GAAE,aAAa;AAAE,MAAAC,GAAE,cAAYA,GAAE,YAAU,CAAC,IAAGA,GAAE,UAAU,QAAQG,EAAC;AAAA,IAAC,MAAM,CAAAF,GAAE,QAAQE,EAAC;AAAA,EAAC,CAAE,GAAEF,MAAGA;AAAC;AAAC,IAAM,IAAE,EAAE,OAAO,CAAC;AAAE,SAASG,GAAEL,IAAEC,IAAE;AAAC,EAAAD,MAAGA,GAAE,QAAS,CAAAA,OAAG;AAAC,IAAAC,GAAED,EAAC,GAAEA,GAAE,aAAWA,GAAE,UAAU,UAAQK,GAAEL,GAAE,WAAUC,EAAC;AAAA,EAAC,CAAE;AAAC;AAAC,IAAM,IAAE,CAAAG,OAAG;AAAC,MAAIE,KAAE,cAAcF,GAAC;AAAA,IAAC,eAAeJ,IAAE;AAAC,YAAM,GAAGA,EAAC,GAAE,KAAK,eAAa,IAAIO,GAAE,EAAC,gBAAe,MAAI,CAAC,KAAK,SAAS,GAAE,qBAAoB,CAAAP,OAAGA,GAAE,UAAS,CAAC,GAAE,KAAK,sBAAoB,EAAC,CAACC,GAAE,OAAO,GAAE,CAAC,GAAE,CAACA,GAAE,WAAW,GAAE,CAAC,GAAE,CAACA,GAAE,SAAS,GAAE,CAAC,GAAE,CAACA,GAAE,OAAO,GAAE,CAAC,EAAC,GAAE,KAAK,WAAW,EAAG,MAAI,KAAK,WAAY,CAACD,IAAEC,OAAI,KAAK,uBAAuBD,IAAEC,EAAC,GAAG,CAAC,CAAC;AAAA,IAAC;AAAA,IAAC,cAAcD,IAAEC,IAAE;AAAC,UAAG,CAACA,MAAG,CAACD,GAAE;AAAO,YAAK,EAAC,qBAAoBE,GAAC,IAAE,MAAKC,KAAE,EAAEF,GAAE,MAAM;AAAE,UAAGE,KAAEF,GAAE,QAAQ;AAAO,UAAGC,GAAEC,EAAC,IAAE,EAAC,SAAQF,IAAE,eAAcD,GAAE,iBAAeE,GAAEC,EAAC,EAAE,eAAc,QAAOH,GAAE,UAAQE,GAAEC,EAAC,EAAE,OAAM,GAAEA,KAAEF,GAAE,QAAQ;AAAO,WAAK,KAAK,oBAAmB,KAAK,yBAAyB,SAAS,EAAE,SAAS;AAAE,YAAK,EAAC,WAAUG,IAAE,QAAOI,GAAC,IAAE,KAAK,yBAAyB,cAAc,GAAE,IAAER,GAAE,IAAI;AAAE,QAAE,iBAAiBQ,EAAC,GAAE,KAAK,KAAK,aAAY,IAAI,EAAEJ,EAAC,CAAC,GAAE,EAAE,iBAAiB,MAAM;AAAA,IAAC;AAAA,IAAC,iBAAiBJ,IAAE;AAAC,aAAO,KAAK,aAAa,KAAM,CAAAC,OAAGA,GAAE,OAAKD,EAAE;AAAA,IAAC;AAAA,IAAC,yBAAwB;AAAC,aAAO,KAAK,yBAAyB,SAAS,EAAE;AAAA,IAAS;AAAA,IAAC,yBAAyBA,IAAE;AAAC,YAAMC,KAAE,EAAE,mBAAiBD,KAAE,YAAUA,EAAC;AAAE,UAAIE,KAAED,GAAE,SAAQE,KAAE,KAAK,oBAAoBF,GAAE,OAAO,EAAE,QAAOG,KAAE,KAAK,oBAAoBH,GAAE,OAAO,EAAE,SAAQO,KAAE;AAAK,YAAM,IAAE,CAACP,GAAE,aAAYA,GAAE,WAAUA,GAAE,OAAO,EAAE,OAAQ,CAAAD,OAAGA,MAAGC,EAAE;AAAE,iBAAUQ,MAAK,GAAE;AAAC,cAAMT,KAAE,KAAK,oBAAoBS,EAAC;AAAE,QAAAN,GAAEH,GAAE,MAAM,MAAIE,KAAEO,IAAEN,KAAEH,GAAE,QAAOI,KAAEJ,GAAE,SAAQA,GAAE,kBAAgBQ,KAAE,EAAC,eAAcR,GAAE,eAAc,SAAQA,GAAE,QAAO;AAAA,MAAG;AAAC,YAAMO,KAAE,CAACN,GAAE,aAAYA,GAAE,WAAUA,GAAE,OAAO,EAAE,OAAQ,CAAAD,OAAGA,KAAEE,MAAGF,MAAGC,EAAE;AAAE,UAAIS,KAAE;AAAK,iBAAUD,MAAKF,IAAE;AAAC,cAAK,EAAC,QAAOP,IAAE,eAAcC,IAAE,SAAQC,GAAC,IAAE,KAAK,oBAAoBO,EAAC;AAAE,QAAAT,OAAIU,KAAE,EAAC,QAAOV,IAAE,SAAQE,GAAC,IAAGD,OAAIO,KAAE,EAAC,eAAcP,IAAE,SAAQC,GAAC;AAAA,MAAE;AAAC,YAAM,IAAE,EAAEC,IAAEC,EAAC,GAAEO,KAAE,oBAAI,OAAIZ,KAAE,oBAAI;AAAI,UAAGW,GAAE,YAAUD,MAAKC,GAAE,OAAO,CAAAC,GAAE,IAAIF,GAAE,IAAGA,EAAC;AAAE,UAAGD,MAAA,gBAAAA,GAAG,cAAc,YAAUC,MAAKD,GAAE,cAAc,CAAAT,GAAE,IAAIU,EAAC;AAAE,aAAOJ,GAAE,GAAG,CAAAL,OAAG;AAAC,QAAAU,MAAGV,GAAE,KAAKW,GAAE,IAAIX,GAAE,EAAE,GAAEU,GAAE,OAAO,GAAEF,MAAGR,GAAE,KAAK,EAAC,mBAAkBD,GAAE,IAAIC,GAAE,EAAE,EAAC,GAAEQ,GAAE,OAAO;AAAA,MAAC,CAAE,GAAE,EAAC,QAAO,EAAEN,EAAC,GAAE,WAAU,IAAI,EAAE,EAAC,OAAM,EAAC,CAAC,EAAC;AAAA,IAAC;AAAA,IAAC,KAAKF,IAAEC,IAAE;AAAC,YAAM,KAAKD,IAAEC,EAAC,GAAE,KAAK,cAAcD,IAAEC,EAAC;AAAA,IAAC;AAAA,IAAC,uBAAuBD,IAAEC,IAAE;AAAC,MAAAA,OAAIA,GAAE,QAAS,CAAAD,OAAG;AAAC,QAAAA,GAAE,SAAO,MAAKA,GAAE,QAAM;AAAA,MAAI,CAAE,GAAE,KAAK,QAAQ,OAAO,iBAAiB,IAAGA,OAAIA,GAAE,QAAS,CAAAA,OAAG;AAAC,QAAAA,GAAE,SAAO,MAAKA,GAAE,QAAM;AAAA,MAAI,CAAE,GAAE,KAAK,QAAQ,IAAI,CAACA,GAAE,GAAG,aAAa,CAAC,EAAC,MAAKA,GAAC,MAAI;AAAC,QAAAA,GAAE,SAAO,MAAKA,GAAE,QAAM;AAAA,MAAI,CAAE,GAAEA,GAAE,GAAG,gBAAgB,CAAC,EAAC,MAAKA,GAAC,MAAI;AAAC,QAAAA,GAAE,SAAO,MAAKA,GAAE,QAAM;AAAA,MAAI,CAAE,CAAC,GAAE,iBAAiB,GAAE,WAAS,KAAK,QAAM,KAAK,QAAQ,IAAIA,GAAE,GAAG,kBAAkB,CAAAA,OAAG;AAAC,QAAAD,GAAE,MAAM,IAAIG,GAAE,sCAAqC,0EAAyE,EAAC,OAAM,KAAI,CAAC,CAAC,GAAEF,GAAE,eAAe;AAAA,MAAC,CAAE,GAAE,iBAAiB;AAAA,IAAE;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEM,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAE,OAAO,CAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAK,MAAK,GAAE,MAAK,EAAC,MAAK,OAAG,OAAM,EAAC,WAAU,MAAG,cAAa,KAAE,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,mCAAmC,CAAC,GAAEA,EAAC,GAAEA;AAAC;", "names": ["y", "e", "r", "s", "t", "p", "a", "l", "n", "c", "d", "m", "h", "b", "U", "o", "v", "f", "e", "r", "t", "K", "a", "s", "U", "i", "o", "n", "x", "p", "c", "y", "f", "e", "r", "s", "t", "o", "m", "E", "l", "a", "y", "n", "p"]}