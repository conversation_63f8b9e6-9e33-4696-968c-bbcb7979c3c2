import {
  E,
  s
} from "./chunk-5S4W3ME5.js";
import {
  l as l2
} from "./chunk-T23PB46T.js";
import {
  F
} from "./chunk-MQAXMQFG.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/views/interactive/snapping/Settings.js
var o = class extends l {
  constructor() {
    super(...arguments), this.enabled = true;
  }
};
e([y({ type: Boolean })], o.prototype, "enabled", void 0), o = e([a("esri.views.interactive.snapping.Settings.DefaultSnappingAlgorithm")], o);
var l3 = class extends l {
  constructor(e2) {
    super(e2), this.lineSnapper = new o(), this.parallelLineSnapper = new o(), this.rightAngleSnapper = new o(), this.rightAngleTriangleSnapper = new o(), this.shortLineThreshold = 15, this.distance = 5, this.pointThreshold = 1e-6, this.intersectionParallelLineThreshold = 1e-6, this.parallelLineThreshold = 1e-6, this.verticalLineThreshold = 0.1, this.touchSensitivityMultiplier = 1.5, this.pointOnLineThreshold = 1e-6, this.orange = new l2([255, 127, 0]), this.orangeTransparent = new l2([255, 127, 0, 0.5]), this.lineHintWidthReference = 3, this.lineHintWidthTarget = 3, this.lineHintFadedExtensions = 0.3, this.parallelLineHintWidth = 2, this.parallelLineHintLength = 24, this.parallelLineHintOffset = 1.5, this.rightAngleHintSize = 24, this.rightAngleHintOutlineSize = 1.5, this.satisfiesConstraintScreenThreshold = 1;
  }
};
e([y({ type: o, constructOnly: true, nonNullable: true, json: { write: true } })], l3.prototype, "lineSnapper", void 0), e([y({ type: o, constructOnly: true, nonNullable: true, json: { write: true } })], l3.prototype, "parallelLineSnapper", void 0), e([y({ type: o, constructOnly: true, nonNullable: true, json: { write: true } })], l3.prototype, "rightAngleSnapper", void 0), e([y({ type: o, constructOnly: true, nonNullable: true, json: { write: true } })], l3.prototype, "rightAngleTriangleSnapper", void 0), e([y({ type: Number, nonNullable: true, range: { min: -1, max: 50, step: 1 }, json: { write: true } })], l3.prototype, "shortLineThreshold", void 0), e([y({ type: Number, nonNullable: true, range: { min: -1, max: 50, step: 1 }, json: { write: true } })], l3.prototype, "distance", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 1e-5 }, json: { write: true } })], l3.prototype, "pointThreshold", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 1e-5 }, json: { write: true } })], l3.prototype, "intersectionParallelLineThreshold", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 1e-5 }, json: { write: true } })], l3.prototype, "parallelLineThreshold", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 1 }, json: { write: true } })], l3.prototype, "verticalLineThreshold", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 10 }, json: { write: true } })], l3.prototype, "touchSensitivityMultiplier", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 1e-5 }, json: { write: true } })], l3.prototype, "pointOnLineThreshold", void 0), e([y({ type: l2, nonNullable: true })], l3.prototype, "orange", void 0), e([y({ type: l2, nonNullable: true })], l3.prototype, "orangeTransparent", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 10 }, json: { write: true } })], l3.prototype, "lineHintWidthReference", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 10 }, json: { write: true } })], l3.prototype, "lineHintWidthTarget", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 1 }, json: { write: true } })], l3.prototype, "lineHintFadedExtensions", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 10 }, json: { write: true } })], l3.prototype, "parallelLineHintWidth", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 50 }, json: { write: true } })], l3.prototype, "parallelLineHintLength", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 5 }, json: { write: true } })], l3.prototype, "parallelLineHintOffset", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 46 }, json: { write: true } })], l3.prototype, "rightAngleHintSize", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 6 }, json: { write: true } })], l3.prototype, "rightAngleHintOutlineSize", void 0), e([y({ type: Number, nonNullable: true, range: { min: 0, max: 5 }, json: { write: true } })], l3.prototype, "satisfiesConstraintScreenThreshold", void 0), l3 = e([a("esri.views.interactive.snapping.Settings.Defaults")], l3);
var p = new l3();

// node_modules/@arcgis/core/views/interactive/snapping/hints/IntersectionSnappingHint.js
var o2 = class _o extends s {
  constructor(n, i, o3 = E.ALL) {
    super(i, o3), this.intersectionPoint = n;
  }
  equals(t) {
    return t instanceof _o && F(this.intersectionPoint, t.intersectionPoint);
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/hints/ParallelSnappingHint.js
var r = class _r extends s {
  constructor(t, i, r2, s3 = E.ALL) {
    super(r2, s3), this.lineStart = t, this.lineEnd = i;
  }
  equals(n) {
    return n instanceof _r && (F(this.lineStart, n.lineStart) && F(this.lineEnd, n.lineEnd));
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/hints/RightAngleSnappingHint.js
var s2 = class _s extends s {
  constructor(e2, r2, s3, i, o3 = E.ALL) {
    super(i, o3), this.previousVertex = e2, this.centerVertex = r2, this.nextVertex = s3;
  }
  equals(t) {
    return t instanceof _s && (F(this.previousVertex, t.previousVertex) && F(this.centerVertex, t.centerVertex) && F(this.nextVertex, t.nextVertex));
  }
};

export {
  p,
  o2 as o,
  r,
  s2 as s
};
//# sourceMappingURL=chunk-PHEIXDVR.js.map
