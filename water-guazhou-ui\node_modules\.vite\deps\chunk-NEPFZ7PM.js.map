{"version": 3, "sources": ["../../@arcgis/core/views/layers/support/Geometry.js", "../../@arcgis/core/views/layers/support/Path.js", "../../@arcgis/core/views/2d/layers/LayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import{property as e}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import t from\"../../../geometry/Geometry.js\";import{fromJSON as s}from\"../../../geometry/support/jsonUtils.js\";import p from\"./ClipArea.js\";import m from\"../../../geometry/Extent.js\";import i from\"../../../geometry/Polygon.js\";var y;const c={base:t,key:\"type\",typeMap:{extent:m,polygon:i}};let n=y=class extends p{constructor(r){super(r),this.type=\"geometry\",this.geometry=null}clone(){return new y({geometry:this.geometry?.clone()??null})}commitVersionProperties(){this.commitProperty(\"geometry\")}};r([e({types:c,json:{read:s,write:!0}})],n.prototype,\"geometry\",void 0),n=y=r([o(\"esri.views.layers.support.Geometry\")],n);const a=n;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{property as s}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import t from\"./ClipArea.js\";let e=class extends t{constructor(r){super(r),this.type=\"path\",this.path=[]}commitVersionProperties(){this.commitProperty(\"path\")}};r([s({type:[[[Number]]],json:{write:!0}})],e.prototype,\"path\",void 0),e=r([o(\"esri.views.layers.support.Path\")],e);const p=e;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import t from\"../../../core/Collection.js\";import{referenceSetter as s}from\"../../../core/collectionUtils.js\";import i from\"../../../core/Error.js\";import{watch as r,syncAndInitial as a,on as o}from\"../../../core/reactiveUtils.js\";import{property as n}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as p}from\"../../../core/accessorSupport/decorators/subclass.js\";import{Container as h}from\"../engine/Container.js\";import c from\"../support/HighlightOptions.js\";import l from\"../../layers/support/ClipRect.js\";import d from\"../../layers/support/Geometry.js\";import u from\"../../layers/support/Path.js\";const y=t.ofType({key:\"type\",base:null,typeMap:{rect:l,path:u,geometry:d}}),f=t=>{let l=class extends t{constructor(){super(...arguments),this.attached=!1,this.clips=new y,this.lastUpdateId=-1,this.moving=!1,this.updateRequested=!1,this.visibleAtCurrentScale=!1,this.highlightOptions=null}initialize(){const e=this.view?.spatialReferenceLocked??!0,t=this.view?.spatialReference;t&&e&&!this.spatialReferenceSupported?this.addResolvingPromise(Promise.reject(new i(\"layerview:spatial-reference-incompatible\",\"The spatial reference of this layer does not meet the requirements of the view\",{layer:this.layer}))):(this.container||(this.container=new h),this.container.fadeTransitionEnabled=!0,this.container.visible=!1,this.container.endTransitions(),this.addHandles([r((()=>this.suspended),(e=>{this.container&&(this.container.visible=!e),this.view&&!e&&this.updateRequested&&this.view.requestUpdate()}),a),r((()=>this.layer?.opacity??1),(e=>{this.container&&(this.container.opacity=e)}),a),r((()=>this.layer&&\"blendMode\"in this.layer?this.layer.blendMode:\"normal\"),(e=>{this.container&&(this.container.blendMode=e)}),a),r((()=>this.layer&&\"effect\"in this.layer?this.layer.effect:null),(e=>{this.container&&(this.container.effect=e)}),a),r((()=>this.highlightOptions),(e=>this.container.highlightOptions=e),a),o((()=>this.clips),\"change\",(()=>{this.container&&(this.container.clips=this.clips)}),a),r((()=>({scale:this.view?.scale,scaleRange:this.layer&&\"effectiveScaleRange\"in this.layer?this.layer.effectiveScaleRange:null})),(({scale:e})=>{const t=null!=e&&this.isVisibleAtScale(e);t!==this.visibleAtCurrentScale&&this._set(\"visibleAtCurrentScale\",t)}),a)],\"constructor\"),this.view?.whenLayerView?this.view.whenLayerView(this.layer).then((e=>{e===this&&this.processAttach()}),(()=>{})):this.when().then((()=>{this.processAttach()}),(()=>{})))}destroy(){this.processDetach(),this.updateRequested=!1}get spatialReferenceSupported(){const e=this.view?.spatialReference;return null==e||this.supportsSpatialReference(e)}get updating(){return this.spatialReferenceSupported&&(!this.attached||!this.suspended&&(this.updateRequested||this.isUpdating())||!!this.updatingHandles?.updating)}processAttach(){this.isResolved()&&!this.attached&&!this.destroyed&&this.spatialReferenceSupported&&(this.attach(),this.attached=!0,this.requestUpdate())}processDetach(){this.attached&&(this.attached=!1,this.removeHandles(\"attach\"),this.detach(),this.updateRequested=!1)}isVisibleAtScale(e){const t=this.layer&&\"effectiveScaleRange\"in this.layer?this.layer.effectiveScaleRange:null;if(!t)return!0;const{minScale:s,maxScale:i}=t;return(0===s||e<=s)&&(0===i||e>=i)}requestUpdate(){this.destroyed||this.updateRequested||(this.updateRequested=!0,this.suspended||this.view.requestUpdate())}processUpdate(e){!this.isFulfilled()||this.isResolved()?(this._set(\"updateParameters\",e),this.updateRequested&&!this.suspended&&(this.updateRequested=!1,this.update(e))):this.updateRequested=!1}hitTest(e,t){return Promise.resolve(null)}supportsSpatialReference(e){return!0}canResume(){return!!this.spatialReferenceSupported&&(!!super.canResume()&&this.visibleAtCurrentScale)}getSuspendInfo(){const e=super.getSuspendInfo(),t=!this.spatialReferenceSupported,s=this.visibleAtCurrentScale;return t&&(e.spatialReferenceNotSupported=t),s&&(e.outsideScaleRange=s),e}addAttachHandles(e){this.addHandles(e,\"attach\")}};return e([n()],l.prototype,\"attached\",void 0),e([n({type:y,set(e){const t=s(e,this._get(\"clips\"),y);this._set(\"clips\",t)}})],l.prototype,\"clips\",void 0),e([n()],l.prototype,\"container\",void 0),e([n()],l.prototype,\"moving\",void 0),e([n({readOnly:!0})],l.prototype,\"spatialReferenceSupported\",null),e([n({readOnly:!0})],l.prototype,\"updateParameters\",void 0),e([n()],l.prototype,\"updateRequested\",void 0),e([n()],l.prototype,\"updating\",null),e([n()],l.prototype,\"view\",void 0),e([n({readOnly:!0})],l.prototype,\"visibleAtCurrentScale\",void 0),e([n({type:c})],l.prototype,\"highlightOptions\",void 0),l=e([p(\"esri.views.2d.layers.LayerView2D\")],l),l};export{f as LayerView2DMixin};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIyiB,IAAIA;AAAE,IAAMC,KAAE,EAAC,MAAK,GAAE,KAAI,QAAO,SAAQ,EAAC,QAAO,GAAE,SAAQ,EAAC,EAAC;AAAE,IAAIC,KAAEF,KAAE,cAAcG,GAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,OAAK,YAAW,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,QAAO;AAJvsB;AAIwsB,WAAO,IAAIH,GAAE,EAAC,YAAS,UAAK,aAAL,mBAAe,YAAS,KAAI,CAAC;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,SAAK,eAAe,UAAU;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,OAAMC,IAAE,MAAK,EAAC,MAAKG,IAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAEF,KAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,GAAEE,EAAC;AAAE,IAAMG,KAAEH;;;ACAtnB,IAAII,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,OAAK,QAAO,KAAK,OAAK,CAAC;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,SAAK,eAAe,MAAM;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,gCAAgC,CAAC,GAAEA,EAAC;AAAE,IAAMC,KAAED;;;ACAwL,IAAME,KAAE,EAAE,OAAO,EAAC,KAAI,QAAO,MAAK,MAAK,SAAQ,EAAC,MAAKC,IAAE,MAAKA,IAAE,UAASC,GAAC,EAAC,CAAC;AAA1E,IAA4E,IAAE,OAAG;AAAC,MAAIC,KAAE,cAAc,EAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,WAAS,OAAG,KAAK,QAAM,IAAIH,MAAE,KAAK,eAAa,IAAG,KAAK,SAAO,OAAG,KAAK,kBAAgB,OAAG,KAAK,wBAAsB,OAAG,KAAK,mBAAiB;AAAA,IAAI;AAAA,IAAC,aAAY;AAJ1iC;AAI2iC,YAAMI,OAAE,UAAK,SAAL,mBAAW,2BAAwB,MAAGC,MAAE,UAAK,SAAL,mBAAW;AAAiB,MAAAA,MAAGD,MAAG,CAAC,KAAK,4BAA0B,KAAK,oBAAoB,QAAQ,OAAO,IAAI,EAAE,4CAA2C,kFAAiF,EAAC,OAAM,KAAK,MAAK,CAAC,CAAC,CAAC,KAAG,KAAK,cAAY,KAAK,YAAU,IAAI,MAAG,KAAK,UAAU,wBAAsB,MAAG,KAAK,UAAU,UAAQ,OAAG,KAAK,UAAU,eAAe,GAAE,KAAK,WAAW,CAAC,EAAG,MAAI,KAAK,WAAY,CAAAA,OAAG;AAAC,aAAK,cAAY,KAAK,UAAU,UAAQ,CAACA,KAAG,KAAK,QAAM,CAACA,MAAG,KAAK,mBAAiB,KAAK,KAAK,cAAc;AAAA,MAAC,GAAGE,EAAC,GAAE,EAAG,MAAE;AAJzoD,YAAAC;AAI2oD,iBAAAA,MAAA,KAAK,UAAL,gBAAAA,IAAY,YAAS;AAAA,SAAI,CAAAH,OAAG;AAAC,aAAK,cAAY,KAAK,UAAU,UAAQA;AAAA,MAAE,GAAGE,EAAC,GAAE,EAAG,MAAI,KAAK,SAAO,eAAc,KAAK,QAAM,KAAK,MAAM,YAAU,UAAW,CAAAF,OAAG;AAAC,aAAK,cAAY,KAAK,UAAU,YAAUA;AAAA,MAAE,GAAGE,EAAC,GAAE,EAAG,MAAI,KAAK,SAAO,YAAW,KAAK,QAAM,KAAK,MAAM,SAAO,MAAO,CAAAF,OAAG;AAAC,aAAK,cAAY,KAAK,UAAU,SAAOA;AAAA,MAAE,GAAGE,EAAC,GAAE,EAAG,MAAI,KAAK,kBAAmB,CAAAF,OAAG,KAAK,UAAU,mBAAiBA,IAAGE,EAAC,GAAEJ,GAAG,MAAI,KAAK,OAAO,UAAU,MAAI;AAAC,aAAK,cAAY,KAAK,UAAU,QAAM,KAAK;AAAA,MAAM,GAAGI,EAAC,GAAE,EAAG,MAAE;AAJrnE,YAAAC;AAIwnE,iBAAC,QAAMA,MAAA,KAAK,SAAL,gBAAAA,IAAW,OAAM,YAAW,KAAK,SAAO,yBAAwB,KAAK,QAAM,KAAK,MAAM,sBAAoB,KAAI;AAAA,SAAK,CAAC,EAAC,OAAMH,GAAC,MAAI;AAAC,cAAMC,KAAE,QAAMD,MAAG,KAAK,iBAAiBA,EAAC;AAAE,QAAAC,OAAI,KAAK,yBAAuB,KAAK,KAAK,yBAAwBA,EAAC;AAAA,MAAC,GAAGC,EAAC,CAAC,GAAE,aAAa,KAAE,UAAK,SAAL,mBAAW,iBAAc,KAAK,KAAK,cAAc,KAAK,KAAK,EAAE,KAAM,CAAAF,OAAG;AAAC,QAAAA,OAAI,QAAM,KAAK,cAAc;AAAA,MAAC,GAAI,MAAI;AAAA,MAAC,CAAE,IAAE,KAAK,KAAK,EAAE,KAAM,MAAI;AAAC,aAAK,cAAc;AAAA,MAAC,GAAI,MAAI;AAAA,MAAC,CAAE;AAAA,IAAE;AAAA,IAAC,UAAS;AAAC,WAAK,cAAc,GAAE,KAAK,kBAAgB;AAAA,IAAE;AAAA,IAAC,IAAI,4BAA2B;AAJroF;AAIsoF,YAAMA,MAAE,UAAK,SAAL,mBAAW;AAAiB,aAAO,QAAMA,MAAG,KAAK,yBAAyBA,EAAC;AAAA,IAAC;AAAA,IAAC,IAAI,WAAU;AAJzuF;AAI0uF,aAAO,KAAK,8BAA4B,CAAC,KAAK,YAAU,CAAC,KAAK,cAAY,KAAK,mBAAiB,KAAK,WAAW,MAAI,CAAC,GAAC,UAAK,oBAAL,mBAAsB;AAAA,IAAS;AAAA,IAAC,gBAAe;AAAC,WAAK,WAAW,KAAG,CAAC,KAAK,YAAU,CAAC,KAAK,aAAW,KAAK,8BAA4B,KAAK,OAAO,GAAE,KAAK,WAAS,MAAG,KAAK,cAAc;AAAA,IAAE;AAAA,IAAC,gBAAe;AAAC,WAAK,aAAW,KAAK,WAAS,OAAG,KAAK,cAAc,QAAQ,GAAE,KAAK,OAAO,GAAE,KAAK,kBAAgB;AAAA,IAAG;AAAA,IAAC,iBAAiBA,IAAE;AAAC,YAAMC,KAAE,KAAK,SAAO,yBAAwB,KAAK,QAAM,KAAK,MAAM,sBAAoB;AAAK,UAAG,CAACA,GAAE,QAAM;AAAG,YAAK,EAAC,UAASG,IAAE,UAAS,EAAC,IAAEH;AAAE,cAAO,MAAIG,MAAGJ,MAAGI,QAAK,MAAI,KAAGJ,MAAG;AAAA,IAAE;AAAA,IAAC,gBAAe;AAAC,WAAK,aAAW,KAAK,oBAAkB,KAAK,kBAAgB,MAAG,KAAK,aAAW,KAAK,KAAK,cAAc;AAAA,IAAE;AAAA,IAAC,cAAcA,IAAE;AAAC,OAAC,KAAK,YAAY,KAAG,KAAK,WAAW,KAAG,KAAK,KAAK,oBAAmBA,EAAC,GAAE,KAAK,mBAAiB,CAAC,KAAK,cAAY,KAAK,kBAAgB,OAAG,KAAK,OAAOA,EAAC,MAAI,KAAK,kBAAgB;AAAA,IAAE;AAAA,IAAC,QAAQA,IAAEC,IAAE;AAAC,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAAC;AAAA,IAAC,yBAAyBD,IAAE;AAAC,aAAM;AAAA,IAAE;AAAA,IAAC,YAAW;AAAC,aAAM,CAAC,CAAC,KAAK,8BAA4B,CAAC,CAAC,MAAM,UAAU,KAAG,KAAK;AAAA,IAAsB;AAAA,IAAC,iBAAgB;AAAC,YAAMA,KAAE,MAAM,eAAe,GAAEC,KAAE,CAAC,KAAK,2BAA0BG,KAAE,KAAK;AAAsB,aAAOH,OAAID,GAAE,+BAA6BC,KAAGG,OAAIJ,GAAE,oBAAkBI,KAAGJ;AAAA,IAAC;AAAA,IAAC,iBAAiBA,IAAE;AAAC,WAAK,WAAWA,IAAE,QAAQ;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKH,IAAE,IAAII,IAAE;AAAC,UAAMC,KAAE,EAAED,IAAE,KAAK,KAAK,OAAO,GAAEJ,EAAC;AAAE,SAAK,KAAK,SAAQK,EAAC;AAAA,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,6BAA4B,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAEA,EAAC,GAAEA;AAAC;", "names": ["y", "c", "n", "p", "v", "a", "e", "p", "y", "p", "a", "l", "e", "t", "w", "_a", "s"]}