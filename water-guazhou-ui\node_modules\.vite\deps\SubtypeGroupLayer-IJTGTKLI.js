import {
  C,
  F,
  I as I2,
  R,
  T as T2,
  b as b2,
  d,
  f,
  g,
  h,
  j as j2,
  m as m3,
  p as p7,
  q,
  w as w2,
  y as y4,
  y2 as y5
} from "./chunk-JD6ZSX7S.js";
import "./chunk-RWPKFCRE.js";
import {
  a as a4
} from "./chunk-MHNIPO2F.js";
import {
  s as s4
} from "./chunk-BMXLXOQI.js";
import "./chunk-ZTJYM5FW.js";
import {
  e as e2
} from "./chunk-EKOSN3EW.js";
import "./chunk-OVWJ36QX.js";
import {
  i as i2
} from "./chunk-JI2EKKOE.js";
import {
  p as p6
} from "./chunk-7FKSPW4T.js";
import {
  p as p8
} from "./chunk-YCWCPPNH.js";
import {
  i as i3
} from "./chunk-ICW345PU.js";
import {
  o as o3
} from "./chunk-YAIU7YWS.js";
import {
  p as p9
} from "./chunk-3TGCMFWU.js";
import "./chunk-4HYRJ2BC.js";
import {
  C as C2
} from "./chunk-WCGNZXE2.js";
import {
  a as a5,
  c as c3,
  u as u2
} from "./chunk-AZTQJ4L4.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-O6B5U6ZZ.js";
import {
  T,
  _ as _2
} from "./chunk-PIXWSNML.js";
import "./chunk-GDMKZBSE.js";
import "./chunk-CB74CU6U.js";
import "./chunk-KCSQWRUD.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-2ILOD42U.js";
import {
  p as p5
} from "./chunk-OQ7AZTS4.js";
import {
  p as p4
} from "./chunk-3WHT6SV3.js";
import "./chunk-6TZFJURO.js";
import "./chunk-T4DDX2RP.js";
import "./chunk-4TTTQ6NZ.js";
import "./chunk-T72XIVTW.js";
import "./chunk-6YK77SK5.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-34GESIPX.js";
import {
  x
} from "./chunk-NOEG2P2J.js";
import "./chunk-WGNECGZE.js";
import {
  l as l4
} from "./chunk-6WHFZE3R.js";
import "./chunk-YGSDGECR.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-JZBEMQMW.js";
import "./chunk-HM62IZSE.js";
import {
  i
} from "./chunk-4CFWXJIK.js";
import "./chunk-DHWMTT76.js";
import "./chunk-CCAF47ZU.js";
import {
  t as t4
} from "./chunk-JEANRG5Q.js";
import {
  a as a6,
  v as v2
} from "./chunk-RK6FD6JL.js";
import "./chunk-VP6XUPJO.js";
import {
  y as y3
} from "./chunk-HTXGAKOK.js";
import {
  _
} from "./chunk-FDBF4TJR.js";
import "./chunk-YUDXR4IE.js";
import {
  c as c2
} from "./chunk-WTHPVARW.js";
import {
  O
} from "./chunk-B3Q27ZSC.js";
import {
  p as p3
} from "./chunk-XFYW3BMZ.js";
import {
  D,
  I,
  c,
  m as m2,
  p as p2,
  v
} from "./chunk-C3LWQPIC.js";
import "./chunk-VYC4DNQO.js";
import "./chunk-LVWRJMBJ.js";
import "./chunk-3HW44BD3.js";
import "./chunk-JSANYNBO.js";
import "./chunk-TPRZH2SY.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-P6BL3OFI.js";
import "./chunk-FZ7BG3VX.js";
import {
  n
} from "./chunk-5IKCVZDA.js";
import {
  t as t3
} from "./chunk-AHLG6PXW.js";
import "./chunk-72RC7KC7.js";
import "./chunk-PGSBPPQ2.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  a as a3
} from "./chunk-V6NQCXYQ.js";
import {
  b
} from "./chunk-S2ZSC2TN.js";
import "./chunk-XAKEPYSQ.js";
import {
  U,
  l as l3
} from "./chunk-QUHG7NMD.js";
import "./chunk-HRASNGES.js";
import "./chunk-I4BKZ7SD.js";
import {
  k
} from "./chunk-S5FBFNAP.js";
import "./chunk-VBAEC53F.js";
import "./chunk-C67OD7TM.js";
import "./chunk-5A4WR2SR.js";
import "./chunk-SJ35WMYN.js";
import "./chunk-UV4E33V4.js";
import {
  F as F2,
  x as x2
} from "./chunk-PBQFTVHM.js";
import "./chunk-ZJKAJ76S.js";
import "./chunk-46HTCESL.js";
import "./chunk-NE5KC6IQ.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-Q6JATJLO.js";
import "./chunk-CV3OR36A.js";
import "./chunk-JX2QAMUH.js";
import "./chunk-DVUUHX3W.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import {
  m
} from "./chunk-SJRT3EVN.js";
import "./chunk-RCNP3U5T.js";
import {
  s as s3
} from "./chunk-2CFIAWMM.js";
import {
  j
} from "./chunk-3MWB7OGY.js";
import "./chunk-MURG32WB.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-LZMNPMOO.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-3BEYEFLH.js";
import "./chunk-NM5RTWYY.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-B2DWQPEO.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import {
  L,
  V
} from "./chunk-U4SVMKOQ.js";
import "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import {
  o as o2
} from "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import {
  r as r3
} from "./chunk-IJ6FZE6K.js";
import {
  l as l2
} from "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  r as r2,
  t2 as t,
  t3 as t2
} from "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2,
  u,
  y
} from "./chunk-R4CPW7J5.js";
import "./chunk-2CM7MIII.js";
import "./chunk-HP475EI3.js";
import {
  w,
  y as y2
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  o,
  p
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  a,
  l,
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/Subtype.js
var a7 = class extends l2 {
  constructor() {
    super(...arguments), this.code = null, this.defaultValues = {}, this.domains = null, this.name = null;
  }
  readDomains(o4) {
    if (!o4) return null;
    const r4 = {};
    for (const t5 of Object.keys(o4)) r4[t5] = i(o4[t5]);
    return r4;
  }
  writeDomains(o4, r4) {
    var _a;
    if (!o4) return;
    const t5 = {};
    for (const s5 of Object.keys(o4)) o4[s5] && (t5[s5] = (_a = o4[s5]) == null ? void 0 : _a.toJSON());
    r4.domains = t5;
  }
};
e([y({ type: Number, json: { write: true } })], a7.prototype, "code", void 0), e([y({ type: Object, json: { write: true } })], a7.prototype, "defaultValues", void 0), e([y({ json: { write: true } })], a7.prototype, "domains", void 0), e([o2("domains")], a7.prototype, "readDomains", null), e([r3("domains")], a7.prototype, "writeDomains", null), e([y({ type: String, json: { write: true } })], a7.prototype, "name", void 0), a7 = e([a2("esri.layers.support.Subtype")], a7);
var c4 = a7;

// node_modules/@arcgis/core/layers/support/SubtypeSublayer.js
var $ = ["charts", "editingEnabled", "formTemplate", "labelsVisible", "labelingInfo", "legendEnabled", "minScale", "maxScale", "opacity", "popupEnabled", "popupTemplate", "renderer", "subtypeCode", "templates", "title", "visible"];
var k2 = { key: "type", base: p4, errorContext: "renderer", typeMap: { simple: p5, "unique-value": T, "class-breaks": _2 } };
var M = s4();
var _3 = u({ types: k2 });
var H = 0;
function B(e3) {
  const t5 = e3.json.write;
  return "object" == typeof t5 ? t5.ignoreOrigin = true : e3.json.write = { ignoreOrigin: true }, e3;
}
function Q(e3) {
  return new p5({ symbol: z(e3) });
}
function z(e3) {
  switch (e3) {
    case "point":
    case "multipoint":
      return c3.clone();
    case "polyline":
      return u2.clone();
    case "polygon":
    case "multipatch":
      return a5.clone();
    default:
      return null;
  }
}
function J(e3, t5) {
  return !!t5 && ("unique-value" === (e3 == null ? void 0 : e3.type) && "string" == typeof e3.field && e3.field.toLowerCase() === t5.toLowerCase() && !e3.field2 && !e3.field3 && !e3.valueExpression);
}
function K(e3, t5) {
  var _a;
  return null == e3 ? null : (_a = t5.subtypes) == null ? void 0 : _a.find((t6) => t6.code === e3);
}
function W(e3, t5) {
  let r4 = null;
  switch (t5.geometryType) {
    case "esriGeometryPoint":
    case "esriGeometryMultipoint":
      r4 = "point";
      break;
    case "esriGeometryPolyline":
      r4 = "line";
      break;
    case "esriGeometryPolygon":
    case "esriGeometryMultiPatch":
      r4 = "polygon";
      break;
    default:
      t5.type, r4 = null;
  }
  const i4 = {}, o4 = K(e3, t5);
  if (r(o4)) {
    const { defaultValues: e4 } = o4;
    for (const t6 in e4) i4[t6] = e4[t6];
  }
  return i4[t5.subtypeField] = e3, new p8({ name: "New Feature", drawingTool: r4, prototype: { attributes: i4 } });
}
var X = "esri.layers.support.SubtypeSublayer";
var Y = class extends a3(O(s3(m))) {
  constructor(e3) {
    super(e3), this.charts = null, this.editingEnabled = true, this.fieldOverrides = null, this.fieldsIndex = null, this.formTemplate = null, this.id = `${Date.now().toString(16)}-subtype-sublayer-${H++}`, this.type = "subtype-sublayer", this.labelsVisible = true, this.labelingInfo = null, this.layerType = "ArcGISFeatureLayer", this.legendEnabled = true, this.listMode = "show", this.minScale = 0, this.maxScale = 0, this.opacity = 1, this.popupEnabled = true, this.popupTemplate = null, this.subtypeCode = null, this.templates = null, this.title = null, this.visible = true;
  }
  get capabilities() {
    var _a;
    return (_a = this.parent) == null ? void 0 : _a.capabilities;
  }
  get effectiveCapabilities() {
    var _a;
    return (_a = this.parent) == null ? void 0 : _a.effectiveCapabilities;
  }
  get effectiveEditingEnabled() {
    const { parent: e3 } = this;
    return e3 ? e3.effectiveEditingEnabled && this.editingEnabled : this.editingEnabled;
  }
  get elevationInfo() {
    var _a;
    return (_a = this.parent) == null ? void 0 : _a.elevationInfo;
  }
  writeFieldOverrides(e3, t5, r4) {
    const { fields: i4, parent: o4 } = this;
    let n2;
    if (i4) {
      n2 = [];
      let e4 = 0;
      i4.forEach(({ name: t6, alias: r5, editable: i5, visible: s5 }) => {
        var _a;
        if (!s5) return;
        const l5 = (_a = o4 == null ? void 0 : o4.fields) == null ? void 0 : _a.find((e5) => e5.name === t6);
        if (!l5) return;
        const a8 = { name: t6 };
        let p10 = false;
        r5 !== l5.alias && (a8.alias = r5, p10 = true), i5 !== l5.editable && (a8.editable = i5, p10 = true), n2.push(a8), p10 && e4++;
      }), 0 === e4 && n2.length === i4.length && (n2 = null);
    } else n2 = p(e3);
    (n2 == null ? void 0 : n2.length) && o(r4, n2, t5);
  }
  get fields() {
    const { parent: e3, fieldOverrides: t5, subtypeCode: r4 } = this, i4 = e3 == null ? void 0 : e3.fields;
    if (!e3 || !(i4 == null ? void 0 : i4.length)) return null;
    const { subtypes: o4, subtypeField: n2 } = e3, s5 = o4 == null ? void 0 : o4.find((e4) => e4.code === r4), l5 = s5 == null ? void 0 : s5.defaultValues, a8 = s5 == null ? void 0 : s5.domains, p10 = [];
    for (const d2 of i4) {
      const e4 = d2.clone(), { name: i5 } = e4, o5 = t5 == null ? void 0 : t5.find((e5) => e5.name === i5);
      if (e4.visible = !t5 || !!o5, o5) {
        const { alias: t6, editable: r5 } = o5;
        t6 && (e4.alias = t6), false === r5 && (e4.editable = false);
      }
      const s6 = (l5 == null ? void 0 : l5[i5]) ?? null;
      e4.defaultValue = i5 === n2 ? r4 : s6;
      const u3 = (a8 == null ? void 0 : a8[i5]) ?? null;
      e4.domain = i5 === n2 ? null : u3 ? "inherited" === u3.type ? e4.domain : u3.clone() : null, p10.push(e4);
    }
    return p10;
  }
  get geometryType() {
    var _a;
    return (_a = this.parent) == null ? void 0 : _a.geometryType;
  }
  get effectiveScaleRange() {
    const { minScale: e3, maxScale: t5 } = this;
    return { minScale: e3, maxScale: t5 };
  }
  get objectIdField() {
    var _a;
    return this.parent || s.getLogger(X).error(ee("objectIdField")), (_a = this.parent) == null ? void 0 : _a.objectIdField;
  }
  get defaultPopupTemplate() {
    return this.createPopupTemplate();
  }
  set renderer(e3) {
    F2(e3, this.fieldsIndex), this._override("renderer", e3);
  }
  get renderer() {
    if (this._isOverridden("renderer")) return this._get("renderer");
    const { parent: e3 } = this;
    return e3 && !e3.isTable && "mesh" !== e3.geometryType ? Q(e3.geometryType) : null;
  }
  readRendererFromService(e3, t5, r4) {
    var _a, _b, _c;
    if ("Table" === t5.type) return null;
    const i4 = (_a = t5.drawingInfo) == null ? void 0 : _a.renderer, n2 = _3(i4, t5, r4);
    let s5;
    const { subtypeCode: l5 } = this;
    if (null != l5 && J(n2, t5.subtypeField)) {
      const e4 = (_b = n2.uniqueValueInfos) == null ? void 0 : _b.find(({ value: e5 }) => (e5 = "number" == typeof e5 ? String(e5) : e5) === String(l5));
      e4 && (s5 = new p5({ symbol: e4.symbol }));
    } else "simple" !== (n2 == null ? void 0 : n2.type) || ((_c = n2.visualVariables) == null ? void 0 : _c.length) || (s5 = n2);
    return s5;
  }
  readRenderer(e3, t5, r4) {
    var _a, _b, _c;
    const i4 = (_b = (_a = t5 == null ? void 0 : t5.layerDefinition) == null ? void 0 : _a.drawingInfo) == null ? void 0 : _b.renderer;
    if (!i4) return;
    const o4 = (_c = i4.visualVariables) == null ? void 0 : _c.some((e4) => "rotationInfo" !== e4.type);
    return o4 ? void 0 : _3(i4, t5, r4) || void 0;
  }
  get spatialReference() {
    var _a;
    return (_a = this.parent) == null ? void 0 : _a.spatialReference;
  }
  readTemplatesFromService(e3, t5) {
    return [W(this.subtypeCode, t5)];
  }
  readTitleFromService(e3, t5) {
    const r4 = K(this.subtypeCode, t5);
    return r(r4) ? r4.name : null;
  }
  get url() {
    var _a;
    return (_a = this.parent) == null ? void 0 : _a.url;
  }
  get userHasUpdateItemPrivileges() {
    var _a;
    return !!((_a = this.parent) == null ? void 0 : _a.userHasUpdateItemPrivileges);
  }
  async addAttachment(e3, t5) {
    const { parent: r4 } = this;
    if (!r4) throw ee("addAttachment");
    if (e3.getAttribute(r4.subtypeField) !== this.subtypeCode) throw new s2("subtype-sublayer:addAttachment", "The feature provided does not belong to this SubtypeSublayer");
    return r4.addAttachment(e3, t5);
  }
  async updateAttachment(e3, t5, r4) {
    const { parent: i4 } = this;
    if (!i4) throw ee("updateAttachment");
    if (e3.getAttribute(i4.subtypeField) !== this.subtypeCode) throw new s2("subtype-sublayer:updateAttachment", "The feature provided does not belong to this SubtypeSublayer");
    return i4.updateAttachment(e3, t5, r4);
  }
  async deleteAttachments(e3, t5) {
    const { parent: r4 } = this;
    if (!r4) throw ee("deleteAttachments");
    if (e3.getAttribute(r4.subtypeField) !== this.subtypeCode) throw new s2("subtype-sublayer:deleteAttachments", "The feature provided does not belong to this SubtypeSublayer");
    return r4.deleteAttachments(e3, t5);
  }
  async applyEdits(e3, t5) {
    if (!this.parent) throw ee("applyEdits");
    return this.parent.applyEdits(e3, t5);
  }
  createPopupTemplate(e3) {
    let t5 = this;
    const { parent: r4, fields: i4, title: o4 } = this;
    if (r4) {
      const { displayField: e4, editFieldsInfo: n2, objectIdField: s5 } = r4;
      t5 = { displayField: e4, editFieldsInfo: n2, fields: i4, objectIdField: s5, title: o4 };
    }
    return p9(t5, e3);
  }
  createQuery() {
    if (!this.parent) throw ee("createQuery");
    const e3 = I2(this.parent), t5 = `${this.parent.subtypeField}=${this.subtypeCode}`;
    return e3.where = t4(t5, this.parent.definitionExpression), e3;
  }
  getField(e3) {
    return this.fieldsIndex.get(e3);
  }
  getFieldDomain(e3) {
    return this._getLayerDomain(e3);
  }
  hasUserOverrides() {
    return $.some((e3) => this.originIdOf(e3) === r2.USER);
  }
  async queryAttachments(e3, t5) {
    const r4 = await this.load();
    if (!r4.parent) throw ee("queryAttachments");
    const i4 = e3.clone();
    return i4.where = Z(i4.where, r4.parent.subtypeField, r4.subtypeCode), r4.parent.queryAttachments(e3, t5);
  }
  async queryFeatures(e3, t5) {
    const r4 = await this.load();
    if (!r4.parent) throw ee("queryFeatures");
    const i4 = x.from(e3) ?? r4.createQuery();
    return r(e3) && (i4.where = Z(i4.where, r4.parent.subtypeField, r4.subtypeCode)), r4.parent.queryFeatures(i4, t5);
  }
  _getLayerDomain(e3) {
    const t5 = this.fieldsIndex.get(e3);
    return t5 ? t5.domain : null;
  }
};
e([y({ readOnly: true, json: { read: false } })], Y.prototype, "capabilities", null), e([y({ readOnly: true, json: { read: false } })], Y.prototype, "effectiveCapabilities", null), e([y({ json: { write: { ignoreOrigin: true } } })], Y.prototype, "charts", void 0), e([y({ type: Boolean, nonNullable: true, json: { name: "enableEditing", write: { ignoreOrigin: true } } })], Y.prototype, "editingEnabled", void 0), e([y({ type: Boolean, readOnly: true })], Y.prototype, "effectiveEditingEnabled", null), e([y({ readOnly: true, json: { read: false } })], Y.prototype, "elevationInfo", null), e([y({ readOnly: true, json: { name: "layerDefinition.fieldOverrides", origins: { service: { read: false } }, write: { ignoreOrigin: true, allowNull: true } } })], Y.prototype, "fieldOverrides", void 0), e([r3("fieldOverrides")], Y.prototype, "writeFieldOverrides", null), e([y({ ...M.fields, readOnly: true, json: { read: false } })], Y.prototype, "fields", null), e([y(M.fieldsIndex)], Y.prototype, "fieldsIndex", void 0), e([y({ type: y4, json: { name: "formInfo", write: { ignoreOrigin: true } } })], Y.prototype, "formTemplate", void 0), e([y({ type: String, readOnly: true, json: { origins: { service: { read: false } }, write: { ignoreOrigin: true } } })], Y.prototype, "id", void 0), e([y({ readOnly: true, json: { read: false } })], Y.prototype, "geometryType", null), e([y({ readOnly: true, json: { read: false } })], Y.prototype, "type", void 0), e([y(B(p(m2)))], Y.prototype, "labelsVisible", void 0), e([y({ type: [C2], json: { name: "layerDefinition.drawingInfo.labelingInfo", origins: { service: { read: false } }, read: { reader: i3 }, write: { ignoreOrigin: true } } })], Y.prototype, "labelingInfo", void 0), e([y({ type: ["ArcGISFeatureLayer"], readOnly: true, json: { read: false, write: { ignoreOrigin: true } } })], Y.prototype, "layerType", void 0), e([y(B(p(c)))], Y.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide"] })], Y.prototype, "listMode", void 0), e([y((() => {
  const e3 = p(I);
  return e3.json.origins.service.read = false, B(e3);
})())], Y.prototype, "minScale", void 0), e([y((() => {
  const e3 = p(D);
  return e3.json.origins.service.read = false, B(e3);
})())], Y.prototype, "maxScale", void 0), e([y({ readOnly: true })], Y.prototype, "effectiveScaleRange", null), e([y({ readOnly: true, json: { read: false } })], Y.prototype, "objectIdField", null), e([y({ type: Number, range: { min: 0, max: 1 }, nonNullable: true, json: { write: { ignoreOrigin: true } } })], Y.prototype, "opacity", void 0), e([y()], Y.prototype, "parent", void 0), e([y(B(p(p2)))], Y.prototype, "popupEnabled", void 0), e([y({ type: k, json: { name: "popupInfo", write: { ignoreOrigin: true } } })], Y.prototype, "popupTemplate", void 0), e([y({ readOnly: true })], Y.prototype, "defaultPopupTemplate", null), e([y({ types: k2, json: { write: { target: "layerDefinition.drawingInfo.renderer", ignoreOrigin: true } } })], Y.prototype, "renderer", null), e([o2("service", "renderer", ["drawingInfo.renderer", "subtypeField", "type"])], Y.prototype, "readRendererFromService", null), e([o2("renderer", ["layerDefinition.drawingInfo.renderer"])], Y.prototype, "readRenderer", null), e([y({ readOnly: true, json: { read: false } })], Y.prototype, "spatialReference", null), e([y({ type: Number, json: { origins: { service: { read: false } }, write: { ignoreOrigin: true } } })], Y.prototype, "subtypeCode", void 0), e([y({ type: [p8], json: { name: "layerDefinition.templates", write: { ignoreOrigin: true } } })], Y.prototype, "templates", void 0), e([o2("service", "templates", ["geometryType", "subtypeField", "subtypes", "type"])], Y.prototype, "readTemplatesFromService", null), e([y({ type: String, json: { write: { ignoreOrigin: true } } })], Y.prototype, "title", void 0), e([o2("service", "title", ["subtypes"])], Y.prototype, "readTitleFromService", null), e([y({ readOnly: true, json: { read: false } })], Y.prototype, "url", null), e([y({ readOnly: true })], Y.prototype, "userHasUpdateItemPrivileges", null), e([y({ type: Boolean, nonNullable: true, json: { name: "visibility", write: { ignoreOrigin: true } } })], Y.prototype, "visible", void 0), Y = e([a2(X)], Y);
var Z = (e3, t5, r4) => {
  const i4 = new RegExp(`${t5}=[0-9]`), o4 = `${t5}=${r4}`, n2 = l(e3, "");
  return i4.test(n2) ? n2.replace(i4, o4) : t4(o4, n2);
};
var ee = (e3) => new s2(`This sublayer must have a parent SubtypeGroupLayer in order to use ${e3}`);
var te = Y;

// node_modules/@arcgis/core/layers/SubtypeGroupLayer.js
var ie = "SubtypeGroupLayer";
var oe = "esri.layers.SubtypeGroupLayer";
function ae(e3, r4) {
  return new s2("layer:unsupported", `Layer (${e3.title}, ${e3.id}) of type '${e3.declaredClass}' ${r4}`, { layer: e3 });
}
var ne = s4();
var le = class extends T2(a4(n(a6(t3(p3(p6(c2(_(O(o3(i2(a3(b))))))))))))) {
  constructor(...e3) {
    super(...e3), this._handles = new t(), this._sublayersCollectionChanged = false, this._sublayerLookup = /* @__PURE__ */ new Map(), this.fields = null, this.fieldsIndex = null, this.outFields = null, this.subtypes = null, this.sublayers = new (j.ofType(te))(), this.timeInfo = null, this.title = "Layer", this.type = "subtype-group", this.addHandles(l3(() => this.sublayers, (e4, r4) => this._handleSublayersChange(e4, r4), U));
  }
  destroy() {
    var _a;
    (_a = this.source) == null ? void 0 : _a.destroy(), this._handles = a(this._handles);
  }
  normalizeCtorArgs(e3, r4) {
    return "string" == typeof e3 ? { url: e3, ...r4 } : e3;
  }
  load(e3) {
    const r4 = r(e3) ? e3.signal : null, s5 = this.loadFromPortal({ supportedTypes: ["Feature Service"] }, e3).catch(w).then(async () => {
      if (!this.url) throw new s2("subtype-grouplayer:missing-url-or-source", "SubtypeGroupLayer must be created with either a url or a portal item");
      if (null == this.layerId) throw new s2("subtype-grouplayer:missing-layerid", "layerId is required for a SubtypeGroupLayer created with url");
      return this._initLayerProperties(await this.createGraphicsSource(r4));
    }).then(() => this._setUserPrivileges(this.serviceItemId, e3)).then(() => R(this, e3));
    return this.addResolvingPromise(s5), Promise.resolve(this);
  }
  get createQueryVersion() {
    return this.commitProperty("definitionExpression"), this.commitProperty("timeExtent"), this.commitProperty("timeOffset"), this.commitProperty("geometryType"), this.commitProperty("gdbVersion"), this.commitProperty("historicMoment"), this.commitProperty("returnZ"), this.commitProperty("capabilities"), this.commitProperty("returnM"), (this._get("createQueryVersion") ?? 0) + 1;
  }
  get editingEnabled() {
    return this.loaded && null != this.capabilities && this.capabilities.operations.supportsEditing && this.userHasEditingPrivileges;
  }
  get effectiveEditingEnabled() {
    return C(this);
  }
  get parsedUrl() {
    const e3 = L(this.url);
    return null != e3 && null != this.layerId && (e3.path = V(e3.path, this.layerId.toString())), e3;
  }
  set source(e3) {
    this._get("source") !== e3 && this._set("source", e3);
  }
  readTitleFromService(e3, { name: r4 }) {
    return this.url ? y3(this.url, r4) : r4;
  }
  async addAttachment(e3, r4) {
    return p7(this, e3, r4, ie);
  }
  async updateAttachment(e3, r4, t5) {
    return y5(this, e3, r4, t5, ie);
  }
  async applyEdits(e3, r4) {
    return d(this, e3, r4);
  }
  on(e3, r4) {
    return super.on(e3, r4);
  }
  async createGraphicsSource(e3) {
    const { default: r4 } = await y2(import("./FeatureLayerSource-WM4ZGI6X.js"), e3);
    return new r4({ layer: this }).load({ signal: e3 });
  }
  createQuery() {
    const e3 = I2(this), r4 = this.sublayers.map((e4) => e4.subtypeCode);
    return e3.where = t4(`${this.subtypeField} IN (${r4.join(",")})`, this.definitionExpression), e3;
  }
  async deleteAttachments(e3, r4) {
    return f(this, e3, r4, ie);
  }
  async fetchRecomputedExtents(e3) {
    return m3(this, e3, ie);
  }
  getFieldDomain(e3, r4) {
    return this._getLayerDomain(e3);
  }
  getField(e3) {
    return this.fieldsIndex.get(e3);
  }
  findSublayerForFeature(e3) {
    const r4 = this.fieldsIndex.get(this.subtypeField), t5 = e3.attributes[r4.name];
    return this._sublayerLookup.get(t5);
  }
  loadAll() {
    return l4(this, (e3) => {
      e3(this.sublayers);
    });
  }
  async queryAttachments(e3, r4) {
    return h(this, e3, r4, ie);
  }
  async queryFeatures(e3, r4) {
    const t5 = await this.load(), s5 = x.from(e3) ?? t5.createQuery(), i4 = l(s5.outFields, []);
    i4.includes(this.subtypeField) || (i4.push(this.subtypeField), s5.outFields = i4);
    const o4 = await t5.source.queryFeatures(s5, r4);
    if (o4 == null ? void 0 : o4.features) for (const a8 of o4.features) a8.layer = a8.sourceLayer = this.findSublayerForFeature(a8);
    return o4;
  }
  async queryObjectIds(e3, r4) {
    return w2(this, e3, r4, ie);
  }
  async queryFeatureCount(e3, r4) {
    return b2(this, e3, r4, ie);
  }
  async queryExtent(e3, r4) {
    return g(this, e3, r4, ie);
  }
  async queryRelatedFeatures(e3, r4) {
    return q(this, e3, r4, ie);
  }
  async queryRelatedFeaturesCount(e3, r4) {
    return j2(this, e3, r4, ie);
  }
  write(e3, r4) {
    var _a;
    const { origin: s5, layerContainerType: i4, messages: o4 } = r4;
    if (this.isTable) {
      if ("web-scene" === s5 || "web-map" === s5 && "tables" !== i4) return o4 == null ? void 0 : o4.push(ae(this, "using a table source cannot be written to web scenes and web maps")), null;
    } else if (this.loaded && "web-map" === s5 && "tables" === i4) return o4 == null ? void 0 : o4.push(ae(this, "using a non-table source cannot be written to tables in web maps")), null;
    return ((_a = this.sublayers) == null ? void 0 : _a.length) ? super.write(e3, r4) : (o4 == null ? void 0 : o4.push(new s2("web-document-write:invalid-property", `Layer (${this.title}, ${this.id}) of type '${this.declaredClass}' has invalid value for 'sublayers' property. 'sublayers' collection should contain at least one sublayer`, { layer: this })), null);
  }
  serviceSupportsSpatialReference(e3) {
    return !!this.loaded && e2(this, e3);
  }
  _getLayerDomain(e3) {
    const r4 = this.fieldsIndex.get(e3);
    return r4 ? r4.domain : null;
  }
  async _initLayerProperties(e3) {
    var _a;
    this._set("source", e3);
    const { sourceJSON: r4 } = e3;
    if (r4 && (this.sourceJSON = r4, this.read(r4, { origin: "service", url: this.parsedUrl })), this.isTable) throw new s2("subtype-grouplayer:unsupported-source", "SubtypeGroupLayer cannot be created using a layer with table source");
    if (!((_a = this.subtypes) == null ? void 0 : _a.length)) throw new s2("subtype-grouplayer:missing-subtypes", "SubtypeGroupLayer must be created using a layer with subtypes");
    this._verifyFields(), x2(this.timeInfo, this.fieldsIndex);
  }
  async hasDataChanged() {
    return F(this);
  }
  _verifyFields() {
    var _a, _b;
    const e3 = ((_a = this.parsedUrl) == null ? void 0 : _a.path) ?? "undefined";
    this.objectIdField || console.log("SubtypeGroupLayer: 'objectIdField' property is not defined (url: " + e3 + ")"), this.isTable || -1 !== e3.search(/\/FeatureServer\//i) || ((_b = this.fields) == null ? void 0 : _b.some((e4) => "geometry" === e4.type)) || console.log("SubtypeGroupLayer: unable to find field of type 'geometry' in the layer 'fields' list. If you are using a map service layer, features will not have geometry (url: " + e3 + ")");
  }
  _handleSublayersChange(e3, r4) {
    r4 && (r4.forEach((e4) => {
      e4.parent = null;
    }), this.handles.remove("sublayers-owner"), this._sublayerLookup.clear()), e3 && (e3.forEach((e4) => {
      e4.parent = this, this._sublayerLookup.set(e4.subtypeCode, e4);
    }), this._sublayersCollectionChanged = false, this.handles.add([e3.on("after-add", ({ item: e4 }) => {
      e4.parent = this, this._sublayerLookup.set(e4.subtypeCode, e4);
    }), e3.on("after-remove", ({ item: e4 }) => {
      e4.parent = null, this._sublayerLookup.delete(e4.subtypeCode);
    }), e3.on("after-changes", () => {
      this._sublayersCollectionChanged = true;
    })], "sublayers-owner"));
  }
};
e([y({ readOnly: true })], le.prototype, "createQueryVersion", null), e([y({ readOnly: true })], le.prototype, "editingEnabled", null), e([y({ readOnly: true })], le.prototype, "effectiveEditingEnabled", null), e([y({ ...ne.fields, readOnly: true, json: { origins: { service: { read: true } }, read: false } })], le.prototype, "fields", void 0), e([y(ne.fieldsIndex)], le.prototype, "fieldsIndex", void 0), e([y(v)], le.prototype, "id", void 0), e([y({ type: ["show", "hide", "hide-children"] })], le.prototype, "listMode", void 0), e([y({ value: "SubtypeGroupLayer", type: ["SubtypeGroupLayer"] })], le.prototype, "operationalLayerType", void 0), e([y(ne.outFields)], le.prototype, "outFields", void 0), e([y({ readOnly: true })], le.prototype, "parsedUrl", null), e([y()], le.prototype, "source", null), e([y({ type: [c4], readOnly: true, json: { read: false, origins: { service: { read: true } } } })], le.prototype, "subtypes", void 0), e([y({ type: j.ofType(te), json: { origins: { service: { read: { source: "subtypes", reader: (e3, t5, s5) => {
  const i4 = e3.map(({ code: e4 }) => {
    const r4 = new te({ subtypeCode: e4 });
    return r4.read(t5, s5), r4;
  });
  return new (j.ofType(te))(i4);
} } } }, name: "layers", write: { overridePolicy(e3, r4, t5) {
  const s5 = this.originOf("sublayers"), i4 = r2.PORTAL_ITEM;
  let o4 = true;
  if (t2(s5) === i4 && t2(t5.origin) > i4) {
    const r5 = e3.some((e4) => e4.hasUserOverrides());
    o4 = this._sublayersCollectionChanged || r5;
  }
  return { enabled: o4, ignoreOrigin: true };
} } } })], le.prototype, "sublayers", void 0), e([y({ type: v2 })], le.prototype, "timeInfo", void 0), e([y({ json: { origins: { "portal-item": { write: { ignoreOrigin: true, writerEnsuresNonNull: true } } } } })], le.prototype, "title", void 0), e([o2("service", "title", ["name"])], le.prototype, "readTitleFromService", null), e([y({ json: { read: false } })], le.prototype, "type", void 0), le = e([a2(oe)], le);
var ue = le;
export {
  ue as default
};
//# sourceMappingURL=SubtypeGroupLayer-IJTGTKLI.js.map
