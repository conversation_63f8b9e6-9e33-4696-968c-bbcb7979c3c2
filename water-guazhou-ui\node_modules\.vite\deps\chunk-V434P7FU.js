import {
  a as a2
} from "./chunk-WFXIWNQB.js";
import {
  p as p2
} from "./chunk-UYJR3ZHF.js";
import {
  l as l2
} from "./chunk-T23PB46T.js";
import {
  o as o2
} from "./chunk-PEEUPDEG.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  o,
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T
} from "./chunk-HP475EI3.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/renderers/support/pointCloud/ColorModulation.js
var s2;
var i = s2 = class extends l {
  constructor() {
    super(...arguments), this.field = null, this.minValue = 0, this.maxValue = 255;
  }
  clone() {
    return new s2({ field: this.field, minValue: this.minValue, maxValue: this.maxValue });
  }
};
e([y({ type: String, json: { write: true } })], i.prototype, "field", void 0), e([y({ type: Number, nonNullable: true, json: { write: true } })], i.prototype, "minValue", void 0), e([y({ type: Number, nonNullable: true, json: { write: true } })], i.prototype, "maxValue", void 0), i = s2 = e([a("esri.renderers.support.pointCloud.ColorModulation")], i);
var p3 = i;

// node_modules/@arcgis/core/renderers/support/pointCloud/PointSizeAlgorithm.js
var p4 = new s({ pointCloudFixedSizeAlgorithm: "fixed-size", pointCloudSplatAlgorithm: "splat" });
var i2 = class extends l {
};
e([y({ type: p4.apiValues, readOnly: true, nonNullable: true, json: { type: p4.jsonValues, read: false, write: p4.write } })], i2.prototype, "type", void 0), i2 = e([a("esri.renderers.support.pointCloud.PointSizeAlgorithm")], i2);
var a3 = i2;

// node_modules/@arcgis/core/renderers/support/pointCloud/PointSizeFixedSizeAlgorithm.js
var i3;
var p5 = i3 = class extends a3 {
  constructor() {
    super(...arguments), this.type = "fixed-size", this.size = 0, this.useRealWorldSymbolSizes = null;
  }
  clone() {
    return new i3({ size: this.size, useRealWorldSymbolSizes: this.useRealWorldSymbolSizes });
  }
};
e([o2({ pointCloudFixedSizeAlgorithm: "fixed-size" })], p5.prototype, "type", void 0), e([y({ type: Number, nonNullable: true, json: { write: true } })], p5.prototype, "size", void 0), e([y({ type: Boolean, json: { write: true } })], p5.prototype, "useRealWorldSymbolSizes", void 0), p5 = i3 = e([a("esri.renderers.support.pointCloud.PointSizeFixedSizeAlgorithm")], p5);
var l3 = p5;

// node_modules/@arcgis/core/renderers/support/pointCloud/PointSizeSplatAlgorithm.js
var p6;
var c = p6 = class extends a3 {
  constructor() {
    super(...arguments), this.type = "splat", this.scaleFactor = 1;
  }
  clone() {
    return new p6({ scaleFactor: this.scaleFactor });
  }
};
e([o2({ pointCloudSplatAlgorithm: "splat" })], c.prototype, "type", void 0), e([y({ type: Number, value: 1, nonNullable: true, json: { write: true } })], c.prototype, "scaleFactor", void 0), c = p6 = e([a("esri.renderers.support.pointCloud.PointSizeSplatAlgorithm")], c);
var a4 = c;

// node_modules/@arcgis/core/renderers/support/pointCloud/pointSizeAlgorithmTypeUtils.js
var e2 = { key: "type", base: a3, typeMap: { "fixed-size": l3, splat: a4 } };

// node_modules/@arcgis/core/renderers/PointCloudRenderer.js
var u = o()({ pointCloudClassBreaksRenderer: "point-cloud-class-breaks", pointCloudRGBRenderer: "point-cloud-rgb", pointCloudStretchRenderer: "point-cloud-stretch", pointCloudUniqueValueRenderer: "point-cloud-unique-value" });
var c2 = class extends l {
  constructor(o3) {
    super(o3), this.type = void 0, this.pointSizeAlgorithm = null, this.colorModulation = null, this.pointsPerInch = 10;
  }
  clone() {
    return console.warn(".clone() is not implemented for " + this.declaredClass), null;
  }
  cloneProperties() {
    return { pointSizeAlgorithm: p(this.pointSizeAlgorithm), colorModulation: p(this.colorModulation), pointsPerInch: p(this.pointsPerInch) };
  }
};
e([y({ type: u.apiValues, readOnly: true, nonNullable: true, json: { type: u.jsonValues, read: false, write: u.write } })], c2.prototype, "type", void 0), e([y({ types: e2, json: { write: true } })], c2.prototype, "pointSizeAlgorithm", void 0), e([y({ type: p3, json: { write: true } })], c2.prototype, "colorModulation", void 0), e([y({ json: { write: true }, nonNullable: true, type: Number })], c2.prototype, "pointsPerInch", void 0), c2 = e([a("esri.renderers.PointCloudRenderer")], c2), function(o3) {
  o3.fieldTransformTypeKebabDict = new s({ none: "none", lowFourBit: "low-four-bit", highFourBit: "high-four-bit", absoluteValue: "absolute-value", moduloTen: "modulo-ten" });
}(c2 || (c2 = {}));
var a5 = c2;

// node_modules/@arcgis/core/renderers/support/pointCloud/ColorClassBreakInfo.js
var a6;
var p7 = a6 = class extends l {
  constructor() {
    super(...arguments), this.description = null, this.label = null, this.minValue = 0, this.maxValue = 0, this.color = null;
  }
  clone() {
    return new a6({ description: this.description, label: this.label, minValue: this.minValue, maxValue: this.maxValue, color: p(this.color) });
  }
};
e([y({ type: String, json: { write: true } })], p7.prototype, "description", void 0), e([y({ type: String, json: { write: true } })], p7.prototype, "label", void 0), e([y({ type: Number, json: { read: { source: "classMinValue" }, write: { target: "classMinValue" } } })], p7.prototype, "minValue", void 0), e([y({ type: Number, json: { read: { source: "classMaxValue" }, write: { target: "classMaxValue" } } })], p7.prototype, "maxValue", void 0), e([y({ type: l2, json: { type: [T], write: true } })], p7.prototype, "color", void 0), p7 = a6 = e([a("esri.renderers.support.pointCloud.ColorClassBreakInfo")], p7);
var c3 = p7;

// node_modules/@arcgis/core/renderers/PointCloudClassBreaksRenderer.js
var l4;
var a7 = l4 = class extends a5 {
  constructor(e3) {
    super(e3), this.type = "point-cloud-class-breaks", this.field = null, this.legendOptions = null, this.fieldTransformType = null, this.colorClassBreakInfos = null;
  }
  clone() {
    return new l4({ ...this.cloneProperties(), field: this.field, fieldTransformType: this.fieldTransformType, colorClassBreakInfos: p(this.colorClassBreakInfos), legendOptions: p(this.legendOptions) });
  }
};
e([o2({ pointCloudClassBreaksRenderer: "point-cloud-class-breaks" })], a7.prototype, "type", void 0), e([y({ json: { write: true }, type: String })], a7.prototype, "field", void 0), e([y({ type: p2, json: { write: true } })], a7.prototype, "legendOptions", void 0), e([y({ type: a5.fieldTransformTypeKebabDict.apiValues, json: { type: a5.fieldTransformTypeKebabDict.jsonValues, read: a5.fieldTransformTypeKebabDict.read, write: a5.fieldTransformTypeKebabDict.write } })], a7.prototype, "fieldTransformType", void 0), e([y({ type: [c3], json: { write: true } })], a7.prototype, "colorClassBreakInfos", void 0), a7 = l4 = e([a("esri.renderers.PointCloudClassBreaksRenderer")], a7);
var d = a7;

// node_modules/@arcgis/core/renderers/PointCloudStretchRenderer.js
var l5;
var d2 = l5 = class extends a5 {
  constructor(e3) {
    super(e3), this.type = "point-cloud-stretch", this.field = null, this.legendOptions = null, this.fieldTransformType = null, this.stops = null;
  }
  clone() {
    return new l5({ ...this.cloneProperties(), field: p(this.field), fieldTransformType: p(this.fieldTransformType), stops: p(this.stops), legendOptions: p(this.legendOptions) });
  }
};
e([o2({ pointCloudStretchRenderer: "point-cloud-stretch" })], d2.prototype, "type", void 0), e([y({ json: { write: true }, type: String })], d2.prototype, "field", void 0), e([y({ type: p2, json: { write: true } })], d2.prototype, "legendOptions", void 0), e([y({ type: a5.fieldTransformTypeKebabDict.apiValues, json: { type: a5.fieldTransformTypeKebabDict.jsonValues, read: a5.fieldTransformTypeKebabDict.read, write: a5.fieldTransformTypeKebabDict.write } })], d2.prototype, "fieldTransformType", void 0), e([y({ type: [a2], json: { write: true } })], d2.prototype, "stops", void 0), d2 = l5 = e([a("esri.renderers.PointCloudStretchRenderer")], d2);
var a8 = d2;

// node_modules/@arcgis/core/renderers/support/pointCloud/ColorUniqueValueInfo.js
var l6;
var c4 = l6 = class extends l {
  constructor() {
    super(...arguments), this.description = null, this.label = null, this.values = null, this.color = null;
  }
  clone() {
    return new l6({ description: this.description, label: this.label, values: p(this.values), color: p(this.color) });
  }
};
e([y({ type: String, json: { write: true } })], c4.prototype, "description", void 0), e([y({ type: String, json: { write: true } })], c4.prototype, "label", void 0), e([y({ type: [String], json: { write: true } })], c4.prototype, "values", void 0), e([y({ type: l2, json: { type: [T], write: true } })], c4.prototype, "color", void 0), c4 = l6 = e([a("esri.renderers.support.pointCloud.ColorUniqueValueInfo")], c4);
var n = c4;

// node_modules/@arcgis/core/renderers/PointCloudUniqueValueRenderer.js
var l7;
var u2 = l7 = class extends a5 {
  constructor(e3) {
    super(e3), this.type = "point-cloud-unique-value", this.field = null, this.fieldTransformType = null, this.colorUniqueValueInfos = null, this.legendOptions = null;
  }
  clone() {
    return new l7({ ...this.cloneProperties(), field: p(this.field), fieldTransformType: p(this.fieldTransformType), colorUniqueValueInfos: p(this.colorUniqueValueInfos), legendOptions: p(this.legendOptions) });
  }
};
e([o2({ pointCloudUniqueValueRenderer: "point-cloud-unique-value" })], u2.prototype, "type", void 0), e([y({ json: { write: true }, type: String })], u2.prototype, "field", void 0), e([y({ type: a5.fieldTransformTypeKebabDict.apiValues, json: { type: a5.fieldTransformTypeKebabDict.jsonValues, read: a5.fieldTransformTypeKebabDict.read, write: a5.fieldTransformTypeKebabDict.write } })], u2.prototype, "fieldTransformType", void 0), e([y({ type: [n], json: { write: true } })], u2.prototype, "colorUniqueValueInfos", void 0), e([y({ type: p2, json: { write: true } })], u2.prototype, "legendOptions", void 0), u2 = l7 = e([a("esri.renderers.PointCloudUniqueValueRenderer")], u2);
var a9 = u2;

export {
  a5 as a,
  d,
  a8 as a2,
  a9 as a3
};
//# sourceMappingURL=chunk-V434P7FU.js.map
