{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/APIKeyMixin.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";function t(e){return\"portalItem\"in e}const i=i=>{let o=class extends i{get apiKey(){return this._isOverridden(\"apiKey\")?this._get(\"apiKey\"):t(this)?this.portalItem?.apiKey:null}set apiKey(e){null!=e?this._override(\"apiKey\",e):(this._clearOverride(\"apiKey\"),this.clear(\"apiKey\",\"user\"))}};return e([r({type:String})],o.prototype,\"apiKey\",null),o=e([s(\"esri.layers.mixins.APIKeyMixin\")],o),o};export{i as APIKeyMixin};\n"], "mappings": ";;;;;;;;;AAI0R,SAAS,EAAEA,IAAE;AAAC,SAAM,gBAAeA;AAAC;AAAC,IAAM,IAAE,CAAAC,OAAG;AAAC,MAAI,IAAE,cAAcA,GAAC;AAAA,IAAC,IAAI,SAAQ;AAJ7W;AAI8W,aAAO,KAAK,cAAc,QAAQ,IAAE,KAAK,KAAK,QAAQ,IAAE,EAAE,IAAI,KAAE,UAAK,eAAL,mBAAiB,SAAO;AAAA,IAAI;AAAA,IAAC,IAAI,OAAOD,IAAE;AAAC,cAAMA,KAAE,KAAK,UAAU,UAASA,EAAC,KAAG,KAAK,eAAe,QAAQ,GAAE,KAAK,MAAM,UAAS,MAAM;AAAA,IAAE;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,gCAAgC,CAAC,GAAE,CAAC,GAAE;AAAC;", "names": ["e", "i"]}