{"version": 3, "sources": ["../../@arcgis/core/core/asyncUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../chunks/tslib.es6.js\";import t from\"./Accessor.js\";import{isSome as o,isNone as e,abort<PERSON>aybe as s,removeMaybe as n}from\"./maybe.js\";import{eachAlways as i,throwIfAbortError as l,onAbort as u}from\"./promiseUtils.js\";import{property as a}from\"./accessorSupport/decorators/property.js\";import\"./accessorSupport/ensureType.js\";import\"./arrayUtils.js\";import{subclass as p}from\"./accessorSupport/decorators/subclass.js\";function c(r,t,o){return i(r.map(((r,e)=>t.apply(o,[r,e]))))}async function h(r,t,o){return(await i(r.map(((r,e)=>t.apply(o,[r,e]))))).map((r=>r.value))}function m(r){return{ok:!0,value:r}}function f(r){return{ok:!1,error:r}}function _(r){return o(r)&&!0===r.ok?r.value:null}function y(r){return o(r)&&!1===r.ok?r.error:null}async function b(r){if(e(r))return{ok:!1,error:new Error(\"no promise provided\")};try{return m(await r)}catch(t){return f(t)}}async function d(r){try{return m(await r)}catch(t){return l(t),f(t)}}function v(r){if(!0===r.ok)return r.value;throw r.error}function j(r,t){return new k(r,t)}let k=class extends t{get value(){return _(this._result)}get error(){return y(this._result)}get finished(){return o(this._result)}constructor(r,t){super({}),this._result=null,this._abortHandle=null,this.abort=()=>{this._abortController=s(this._abortController)},this.remove=this.abort,this._abortController=new AbortController;const{signal:o}=this._abortController;this.promise=r(o),this.promise.then((r=>{this._result=m(r),this._cleanup()}),(r=>{this._result=f(r),this._cleanup()})),this._abortHandle=u(t,this.abort)}normalizeCtorArgs(){return{}}destroy(){this.abort()}_cleanup(){this._abortHandle=n(this._abortHandle),this._abortController=null}};r([a()],k.prototype,\"value\",null),r([a()],k.prototype,\"error\",null),r([a()],k.prototype,\"finished\",null),r([a()],k.prototype,\"promise\",void 0),r([a()],k.prototype,\"_result\",void 0),k=r([p(\"esri.core.asyncUtils.ReactiveTask\")],k);export{v as assertResult,j as createTask,c as forEach,f as makeResultError,m as makeResultOk,h as map,b as result,y as resultError,d as resultOrAbort,_ as resultValue};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIob,SAAS,EAAEA,IAAEC,IAAE,GAAE;AAAC,SAAO,EAAED,GAAE,IAAK,CAACA,IAAEE,OAAID,GAAE,MAAM,GAAE,CAACD,IAAEE,EAAC,CAAC,CAAE,CAAC;AAAC;AAAC,eAAe,EAAEF,IAAEC,IAAE,GAAE;AAAC,UAAO,MAAM,EAAED,GAAE,IAAK,CAACA,IAAEE,OAAID,GAAE,MAAM,GAAE,CAACD,IAAEE,EAAC,CAAC,CAAE,CAAC,GAAG,IAAK,CAAAF,OAAGA,GAAE,KAAM;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAM,EAAC,IAAG,MAAG,OAAMA,GAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAM,EAAC,IAAG,OAAG,OAAMA,GAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG,SAAKA,GAAE,KAAGA,GAAE,QAAM;AAAI;AAAC,SAASG,GAAEH,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG,UAAKA,GAAE,KAAGA,GAAE,QAAM;AAAI;AAAC,eAAe,EAAEA,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAM,EAAC,IAAG,OAAG,OAAM,IAAI,MAAM,qBAAqB,EAAC;AAAE,MAAG;AAAC,WAAO,EAAE,MAAMA,EAAC;AAAA,EAAC,SAAOC,IAAE;AAAC,WAAO,EAAEA,EAAC;AAAA,EAAC;AAAC;AAAC,eAAe,EAAED,IAAE;AAAC,MAAG;AAAC,WAAO,EAAE,MAAMA,EAAC;AAAA,EAAC,SAAOC,IAAE;AAAC,WAAOG,GAAEH,EAAC,GAAE,EAAEA,EAAC;AAAA,EAAC;AAAC;AAAyD,SAAS,EAAEI,IAAEC,IAAE;AAAC,SAAO,IAAI,EAAED,IAAEC,EAAC;AAAC;AAAC,IAAI,IAAE,cAAcC,GAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,EAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAOC,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,EAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAEC,IAAE;AAAC,UAAM,CAAC,CAAC,GAAE,KAAK,UAAQ,MAAK,KAAK,eAAa,MAAK,KAAK,QAAM,MAAI;AAAC,WAAK,mBAAiB,EAAE,KAAK,gBAAgB;AAAA,IAAC,GAAE,KAAK,SAAO,KAAK,OAAM,KAAK,mBAAiB,IAAI;AAAgB,UAAK,EAAC,QAAO,EAAC,IAAE,KAAK;AAAiB,SAAK,UAAQD,GAAE,CAAC,GAAE,KAAK,QAAQ,KAAM,CAAAA,OAAG;AAAC,WAAK,UAAQ,EAAEA,EAAC,GAAE,KAAK,SAAS;AAAA,IAAC,GAAI,CAAAA,OAAG;AAAC,WAAK,UAAQ,EAAEA,EAAC,GAAE,KAAK,SAAS;AAAA,IAAC,CAAE,GAAE,KAAK,eAAa,EAAEC,IAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAM,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,eAAa,EAAE,KAAK,YAAY,GAAE,KAAK,mBAAiB;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,mCAAmC,CAAC,GAAE,CAAC;", "names": ["r", "t", "e", "y", "w", "r", "t", "v", "y"]}