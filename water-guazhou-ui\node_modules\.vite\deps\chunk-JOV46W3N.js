import {
  s
} from "./chunk-UVNYHPLJ.js";
import {
  n as n2
} from "./chunk-7OAX5UZS.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  S,
  b
} from "./chunk-HP475EI3.js";
import {
  v
} from "./chunk-C5VMWMBD.js";
import {
  e as e2,
  t
} from "./chunk-TUM6KUQZ.js";
import {
  i2 as i
} from "./chunk-2CM7MIII.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  N
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/ObservableChangesType.js
var E;
!function(E3) {
  E3[E3.ADD = 1] = "ADD", E3[E3.REMOVE = 2] = "REMOVE", E3[E3.MOVE = 4] = "MOVE";
}(E || (E = {}));

// node_modules/@arcgis/core/core/Collection.js
var g;
var p2 = class {
  constructor() {
    this.target = null, this.cancellable = false, this.defaultPrevented = false, this.item = void 0, this.type = void 0;
  }
  preventDefault() {
    this.cancellable && (this.defaultPrevented = true);
  }
  reset(e3) {
    this.defaultPrevented = false, this.item = e3;
  }
};
var b2 = new e2(p2, void 0, (e3) => {
  e3.item = null, e3.target = null, e3.defaultPrevented = false, e3.cancellable = false;
});
var d = () => {
};
function v2(e3) {
  return e3 ? e3 instanceof V ? e3.toArray() : e3.length ? Array.prototype.slice.apply(e3) : [] : [];
}
function E2(e3) {
  if (e3 && e3.length) return e3[0];
}
function y2(e3, t2, s2, i2) {
  const r = Math.min(e3.length - s2, t2.length - i2);
  let n3 = 0;
  for (; n3 < r && e3[s2 + n3] === t2[i2 + n3]; ) n3++;
  return n3;
}
function C(e3, t2, s2, i2) {
  t2 && t2.forEach((t3, r, n3) => {
    e3.push(t3), C(e3, s2.call(i2, t3, r, n3), s2, i2);
  });
}
var A = /* @__PURE__ */ new Set();
var O = /* @__PURE__ */ new Set();
var M = /* @__PURE__ */ new Set();
var D = /* @__PURE__ */ new Map();
var x = 0;
var V = g = class extends n.EventedAccessor {
  static isCollection(e3) {
    return null != e3 && e3 instanceof g;
  }
  constructor(e3) {
    super(e3), this._chgListeners = [], this._notifications = null, this._timer = null, this._observable = new s(), this.length = 0, this._items = [], Object.defineProperty(this, "uid", { value: x++ });
  }
  normalizeCtorArgs(e3) {
    return e3 ? Array.isArray(e3) || e3 instanceof g ? { items: e3 } : e3 : {};
  }
  destroy() {
    this.removeAll();
  }
  *[Symbol.iterator]() {
    yield* this.items;
  }
  get items() {
    return i(this._observable), this._items;
  }
  set items(e3) {
    this._emitBeforeChanges(E.ADD) || (this._splice(0, this.length, v2(e3)), this._emitAfterChanges(E.ADD));
  }
  hasEventListener(e3) {
    return "change" === e3 ? this._chgListeners.length > 0 : this._emitter.hasEventListener(e3);
  }
  on(e3, t2) {
    if ("change" === e3) {
      const e4 = this._chgListeners, s2 = { removed: false, callback: t2 };
      return e4.push(s2), this._notifications && this._notifications.push({ listeners: e4.slice(), items: this._items.slice(), changes: [] }), { remove() {
        this.remove = d, s2.removed = true, e4.splice(e4.indexOf(s2), 1);
      } };
    }
    return this._emitter.on(e3, t2);
  }
  once(e3, t2) {
    const s2 = this.on(e3, t2);
    return { remove() {
      s2.remove();
    } };
  }
  add(e3, t2) {
    if (i(this._observable), this._emitBeforeChanges(E.ADD)) return this;
    const s2 = this.getNextIndex(t2 ?? null);
    return this._splice(s2, 0, [e3]), this._emitAfterChanges(E.ADD), this;
  }
  addMany(e3, t2 = this._items.length) {
    if (i(this._observable), !e3 || !e3.length) return this;
    if (this._emitBeforeChanges(E.ADD)) return this;
    const s2 = this.getNextIndex(t2);
    return this._splice(s2, 0, v2(e3)), this._emitAfterChanges(E.ADD), this;
  }
  at(e3) {
    if (i(this._observable), (e3 = Math.trunc(e3) || 0) < 0 && (e3 += this.length), !(e3 < 0 || e3 >= this.length)) return this._items[e3];
  }
  removeAll() {
    if (i(this._observable), !this.length || this._emitBeforeChanges(E.REMOVE)) return [];
    const e3 = this._splice(0, this.length) || [];
    return this._emitAfterChanges(E.REMOVE), e3;
  }
  clone() {
    return i(this._observable), this._createNewInstance({ items: this._items.map(p) });
  }
  concat(...e3) {
    i(this._observable);
    const t2 = e3.map(v2);
    return this._createNewInstance({ items: this._items.concat(...t2) });
  }
  drain(e3, t2) {
    if (i(this._observable), !this.length || this._emitBeforeChanges(E.REMOVE)) return;
    const s2 = N(this._splice(0, this.length)), i2 = s2.length;
    for (let r = 0; r < i2; r++) e3.call(t2, s2[r], r, s2);
    this._emitAfterChanges(E.REMOVE);
  }
  every(e3, t2) {
    return i(this._observable), this._items.every(e3, t2);
  }
  filter(e3, t2) {
    let s2;
    return i(this._observable), s2 = 2 === arguments.length ? this._items.filter(e3, t2) : this._items.filter(e3), this._createNewInstance({ items: s2 });
  }
  find(e3, t2) {
    return i(this._observable), this._items.find(e3, t2);
  }
  findIndex(e3, t2) {
    return i(this._observable), this._items.findIndex(e3, t2);
  }
  flatten(e3, t2) {
    i(this._observable);
    const s2 = [];
    return C(s2, this, e3, t2), new g(s2);
  }
  forEach(e3, t2) {
    return i(this._observable), this._items.forEach(e3, t2);
  }
  getItemAt(e3) {
    return i(this._observable), this._items[e3];
  }
  getNextIndex(e3) {
    i(this._observable);
    const t2 = this.length;
    return (e3 = e3 ?? t2) < 0 ? e3 = 0 : e3 > t2 && (e3 = t2), e3;
  }
  includes(e3, t2 = 0) {
    return i(this._observable), this._items.includes(e3, t2);
  }
  indexOf(e3, t2 = 0) {
    return i(this._observable), this._items.indexOf(e3, t2);
  }
  join(e3 = ",") {
    return i(this._observable), this._items.join(e3);
  }
  lastIndexOf(e3, t2 = this.length - 1) {
    return i(this._observable), this._items.lastIndexOf(e3, t2);
  }
  map(e3, t2) {
    i(this._observable);
    const s2 = this._items.map(e3, t2);
    return new g({ items: s2 });
  }
  reorder(e3, t2 = this.length - 1) {
    i(this._observable);
    const s2 = this.indexOf(e3);
    if (-1 !== s2) {
      if (t2 < 0 ? t2 = 0 : t2 >= this.length && (t2 = this.length - 1), s2 !== t2) {
        if (this._emitBeforeChanges(E.MOVE)) return e3;
        this._splice(s2, 1), this._splice(t2, 0, [e3]), this._emitAfterChanges(E.MOVE);
      }
      return e3;
    }
  }
  pop() {
    if (i(this._observable), !this.length || this._emitBeforeChanges(E.REMOVE)) return;
    const e3 = E2(this._splice(this.length - 1, 1));
    return this._emitAfterChanges(E.REMOVE), e3;
  }
  push(...e3) {
    return i(this._observable), this._emitBeforeChanges(E.ADD) || (this._splice(this.length, 0, e3), this._emitAfterChanges(E.ADD)), this.length;
  }
  reduce(e3, t2) {
    i(this._observable);
    const s2 = this._items;
    return 2 === arguments.length ? s2.reduce(e3, t2) : s2.reduce(e3);
  }
  reduceRight(e3, t2) {
    i(this._observable);
    const s2 = this._items;
    return 2 === arguments.length ? s2.reduceRight(e3, t2) : s2.reduceRight(e3);
  }
  remove(e3) {
    return i(this._observable), this.removeAt(this.indexOf(e3));
  }
  removeAt(e3) {
    if (i(this._observable), e3 < 0 || e3 >= this.length || this._emitBeforeChanges(E.REMOVE)) return;
    const t2 = E2(this._splice(e3, 1));
    return this._emitAfterChanges(E.REMOVE), t2;
  }
  removeMany(e3) {
    if (i(this._observable), !e3 || !e3.length || this._emitBeforeChanges(E.REMOVE)) return [];
    const t2 = e3 instanceof g ? e3.toArray() : e3, s2 = this._items, i2 = [], r = t2.length;
    for (let n3 = 0; n3 < r; n3++) {
      const e4 = t2[n3], r2 = s2.indexOf(e4);
      if (r2 > -1) {
        const e5 = 1 + y2(t2, s2, n3 + 1, r2 + 1), h = this._splice(r2, e5);
        h && h.length > 0 && i2.push.apply(i2, h), n3 += e5 - 1;
      }
    }
    return this._emitAfterChanges(E.REMOVE), i2;
  }
  reverse() {
    if (i(this._observable), this._emitBeforeChanges(E.MOVE)) return this;
    const e3 = this._splice(0, this.length);
    return e3 && (e3.reverse(), this._splice(0, 0, e3)), this._emitAfterChanges(E.MOVE), this;
  }
  shift() {
    if (i(this._observable), !this.length || this._emitBeforeChanges(E.REMOVE)) return;
    const e3 = E2(this._splice(0, 1));
    return this._emitAfterChanges(E.REMOVE), e3;
  }
  slice(e3 = 0, t2 = this.length) {
    return i(this._observable), this._createNewInstance({ items: this._items.slice(e3, t2) });
  }
  some(e3, t2) {
    return i(this._observable), this._items.some(e3, t2);
  }
  sort(e3) {
    if (i(this._observable), !this.length || this._emitBeforeChanges(E.MOVE)) return this;
    const t2 = N(this._splice(0, this.length));
    return arguments.length ? t2.sort(e3) : t2.sort(), this._splice(0, 0, t2), this._emitAfterChanges(E.MOVE), this;
  }
  splice(e3, t2, ...s2) {
    i(this._observable);
    const i2 = (t2 ? E.REMOVE : 0) | (s2.length ? E.ADD : 0);
    if (this._emitBeforeChanges(i2)) return [];
    const r = this._splice(e3, t2, s2) || [];
    return this._emitAfterChanges(i2), r;
  }
  toArray() {
    return i(this._observable), this._items.slice();
  }
  toJSON() {
    return i(this._observable), this.toArray();
  }
  toLocaleString() {
    return i(this._observable), this._items.toLocaleString();
  }
  toString() {
    return i(this._observable), this._items.toString();
  }
  unshift(...e3) {
    return i(this._observable), !e3.length || this._emitBeforeChanges(E.ADD) || (this._splice(0, 0, e3), this._emitAfterChanges(E.ADD)), this.length;
  }
  _createNewInstance(e3) {
    return new this.constructor(e3);
  }
  _splice(e3, t2, s2) {
    const i2 = this._items, r = this.itemType;
    let n3, h;
    if (!this._notifications && this.hasEventListener("change") && (this._notifications = [{ listeners: this._chgListeners.slice(), items: this._items.slice(), changes: [] }], this._timer && this._timer.remove(), this._timer = v(() => this._dispatchChange())), t2) {
      if (h = i2.splice(e3, t2), this.hasEventListener("before-remove")) {
        const t3 = b2.acquire();
        t3.target = this, t3.cancellable = true;
        for (let s3 = 0, r2 = h.length; s3 < r2; s3++) n3 = h[s3], t3.reset(n3), this.emit("before-remove", t3), t3.defaultPrevented && (h.splice(s3, 1), i2.splice(e3, 0, n3), e3 += 1, s3 -= 1, r2 -= 1);
        b2.release(t3);
      }
      if (this.length = this._items.length, this.hasEventListener("after-remove")) {
        const e4 = b2.acquire();
        e4.target = this, e4.cancellable = false;
        const t3 = h.length;
        for (let s3 = 0; s3 < t3; s3++) e4.reset(h[s3]), this.emit("after-remove", e4);
        b2.release(e4);
      }
    }
    if (s2 && s2.length) {
      if (r) {
        const e4 = [];
        for (const t4 of s2) {
          const s3 = r.ensureType(t4);
          null == s3 && null != t4 || e4.push(s3);
        }
        s2 = e4;
      }
      const t3 = this.hasEventListener("before-add"), n4 = this.hasEventListener("after-add"), h2 = e3 === this.length;
      if (t3 || n4) {
        const r2 = b2.acquire();
        r2.target = this, r2.cancellable = true;
        const o = b2.acquire();
        o.target = this, o.cancellable = false;
        for (const l of s2) t3 ? (r2.reset(l), this.emit("before-add", r2), r2.defaultPrevented || (h2 ? i2.push(l) : i2.splice(e3++, 0, l), this._set("length", i2.length), n4 && (o.reset(l), this.emit("after-add", o)))) : (h2 ? i2.push(l) : i2.splice(e3++, 0, l), this._set("length", i2.length), o.reset(l), this.emit("after-add", o));
        b2.release(o), b2.release(r2);
      } else {
        if (h2) for (const e4 of s2) i2.push(e4);
        else i2.splice(e3, 0, ...s2);
        this._set("length", i2.length);
      }
    }
    return (s2 && s2.length || h && h.length) && this._notifyChangeEvent(s2, h), h;
  }
  _emitBeforeChanges(e3) {
    let t2 = false;
    if (this.hasEventListener("before-changes")) {
      const s2 = b2.acquire();
      s2.target = this, s2.cancellable = true, s2.type = e3, this.emit("before-changes", s2), t2 = s2.defaultPrevented, b2.release(s2);
    }
    return t2;
  }
  _emitAfterChanges(e3) {
    if (this.hasEventListener("after-changes")) {
      const t2 = b2.acquire();
      t2.target = this, t2.cancellable = false, t2.type = e3, this.emit("after-changes", t2), b2.release(t2);
    }
    this._observable.notify();
  }
  _notifyChangeEvent(e3, t2) {
    this.hasEventListener("change") && this._notifications && this._notifications[this._notifications.length - 1].changes.push({ added: e3, removed: t2 });
  }
  _dispatchChange() {
    if (this._timer && (this._timer.remove(), this._timer = null), !this._notifications) return;
    const e3 = this._notifications;
    this._notifications = null;
    for (const s2 of e3) {
      const e4 = s2.changes;
      A.clear(), O.clear(), M.clear();
      for (const { added: t2, removed: s3 } of e4) {
        if (t2) if (0 === M.size && 0 === O.size) for (const e5 of t2) A.add(e5);
        else for (const e5 of t2) O.has(e5) ? (M.add(e5), O.delete(e5)) : M.has(e5) || A.add(e5);
        if (s3) if (0 === M.size && 0 === A.size) for (const e5 of s3) O.add(e5);
        else for (const e5 of s3) A.has(e5) ? A.delete(e5) : (M.delete(e5), O.add(e5));
      }
      const i2 = t.acquire();
      A.forEach((e5) => {
        i2.push(e5);
      });
      const r = t.acquire();
      O.forEach((e5) => {
        r.push(e5);
      });
      const n3 = this._items, h = s2.items, o = t.acquire();
      if (M.forEach((e5) => {
        h.indexOf(e5) !== n3.indexOf(e5) && o.push(e5);
      }), s2.listeners && (i2.length || r.length || o.length)) {
        const e5 = { target: this, added: i2, removed: r, moved: o }, t2 = s2.listeners.length;
        for (let i3 = 0; i3 < t2; i3++) {
          const t3 = s2.listeners[i3];
          t3.removed || t3.callback.call(this, e5);
        }
      }
      t.release(i2), t.release(r), t.release(o);
    }
    A.clear(), O.clear(), M.clear();
  }
};
V.ofType = (t2) => {
  if (!t2) return g;
  if (D.has(t2)) return D.get(t2);
  let s2 = null;
  if ("function" == typeof t2) s2 = t2.prototype.declaredClass;
  else if (t2.base) s2 = t2.base.prototype.declaredClass;
  else for (const e3 in t2.typeMap) {
    const i3 = t2.typeMap[e3].prototype.declaredClass;
    s2 ? s2 += ` | ${i3}` : s2 = i3;
  }
  let i2 = class extends g {
  };
  return e([n2({ Type: t2, ensureType: "function" == typeof t2 ? b(t2) : S(t2) })], i2.prototype, "itemType", void 0), i2 = e([a(`esri.core.Collection<${s2}>`)], i2), D.set(t2, i2), i2;
}, e([y()], V.prototype, "length", void 0), e([y()], V.prototype, "items", null), V = g = e([a("esri.core.Collection")], V);
var j = V;

export {
  j
};
//# sourceMappingURL=chunk-JOV46W3N.js.map
