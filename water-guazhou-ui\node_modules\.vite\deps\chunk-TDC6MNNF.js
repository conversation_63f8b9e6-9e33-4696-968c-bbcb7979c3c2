import {
  j
} from "./chunk-JOV46W3N.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  l2 as l
} from "./chunk-C5VMWMBD.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/CollectionFlattener.js
var c = class extends j {
  constructor(t2) {
    super(t2), this.getCollections = null;
  }
  initialize() {
    this.own(l(() => this._refresh()));
  }
  destroy() {
    this.getCollections = null;
  }
  _refresh() {
    const t2 = r(this.getCollections) ? this.getCollections() : null;
    if (t(t2)) return void this.removeAll();
    let o = 0;
    for (const r2 of t2) r(r2) && (o = this._processCollection(o, r2));
    this.splice(o, this.length);
  }
  _createNewInstance(t2) {
    return new j(t2);
  }
  _processCollection(t2, o) {
    if (!o) return t2;
    const s = this.itemFilterFunction ? this.itemFilterFunction : (t3) => !!t3;
    for (const r2 of o) if (r2) {
      if (s(r2)) {
        const o2 = this.indexOf(r2, t2);
        o2 >= 0 ? o2 !== t2 && this.reorder(r2, t2) : this.add(r2, t2), ++t2;
      }
      if (this.getChildrenFunction) {
        const o2 = this.getChildrenFunction(r2);
        if (Array.isArray(o2)) for (const s2 of o2) t2 = this._processCollection(t2, s2);
        else t2 = this._processCollection(t2, o2);
      }
    }
    return t2;
  }
};
e([y()], c.prototype, "getCollections", void 0), e([y()], c.prototype, "getChildrenFunction", void 0), e([y()], c.prototype, "itemFilterFunction", void 0), c = e([a("esri.core.CollectionFlattener")], c);
var l2 = c;

export {
  l2 as l
};
//# sourceMappingURL=chunk-TDC6MNNF.js.map
