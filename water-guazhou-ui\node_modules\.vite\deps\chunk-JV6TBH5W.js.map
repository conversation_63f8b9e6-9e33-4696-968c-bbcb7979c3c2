{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/refresh.js", "../../@arcgis/core/layers/mixins/RefreshableLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Collection.js\";import\"../../core/has.js\";import\"../../core/Error.js\";import\"../../core/Logger.js\";import\"../../core/accessorSupport/watch.js\";import{autorun as r}from\"../../core/accessorSupport/trackingUtils.js\";const t=new e,o=new WeakMap;function n(e){c(e)&&t.push(e)}function s(e){c(e)&&t.includes(e)&&t.remove(e)}function c(e){return null!=e&&\"object\"==typeof e&&\"refreshInterval\"in e&&\"refresh\"in e}function i(e,r){return Number.isFinite(e)&&Number.isFinite(r)?r<=0?e:i(r,e%r):0}let f=0,a=0;function l(){const e=Date.now();for(const r of t)if(r.refreshInterval){e-(o.get(r)??0)+5>=6e4*r.refreshInterval&&(o.set(r,e),r.refresh(e))}}r((()=>{const e=Date.now();let r=0;for(const n of t)r=i(Math.round(6e4*n.refreshInterval),r),n.refreshInterval?o.get(n)||o.set(n,e):o.delete(n);if(r!==a){if(a=r,clearInterval(f),0===a)return void(f=0);f=setInterval(l,a)}}));const u={get hasRefreshTimer(){return f>0},get tickInterval(){return a},forceRefresh(){l()},hasLayer:e=>c(e)&&t.includes(e),clear(){for(const e of t)o.delete(e);t.removeAll()}};export{n as registerLayer,u as test,s as unregisterLayer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import r from\"../../core/Logger.js\";import{debounce as s,ignoreAbortErrors as t}from\"../../core/promiseUtils.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as a}from\"../../core/accessorSupport/decorators/subclass.js\";import{registerLayer as i,unregisterLayer as h}from\"./refresh.js\";function n(e){return null!=e&&\"object\"==typeof e&&\"refreshTimestamp\"in e&&\"refresh\"in e}const p=n=>{let p=class extends n{constructor(...e){super(...e),this.refreshInterval=0,this.refreshTimestamp=0,this._debounceHasDataChanged=s((()=>this.hasDataChanged())),this.when().then((()=>{i(this)}),(()=>{}))}destroy(){h(this)}get refreshParameters(){return{_ts:this.refreshTimestamp||null}}refresh(e=Date.now()){t(this._debounceHasDataChanged()).then((r=>{r&&this._set(\"refreshTimestamp\",e),this.emit(\"refresh\",{dataChanged:r})}),(e=>{r.getLogger(this.declaredClass).error(e),this.emit(\"refresh\",{dataChanged:!1,error:e})}))}async hasDataChanged(){return!0}};return e([o({type:Number,cast:e=>e>=.1?e:e<=0?0:.1,json:{write:!0}})],p.prototype,\"refreshInterval\",void 0),e([o({readOnly:!0})],p.prototype,\"refreshTimestamp\",void 0),e([o()],p.prototype,\"refreshParameters\",null),p=e([a(\"esri.layers.mixins.RefreshableLayer\")],p),p};export{p as RefreshableLayer,n as isRefreshableLayer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAI6O,IAAM,IAAE,IAAI;AAAZ,IAAc,IAAE,oBAAI;AAAQ,SAAS,EAAEA,IAAE;AAAC,IAAEA,EAAC,KAAG,EAAE,KAAKA,EAAC;AAAC;AAAC,SAASC,GAAED,IAAE;AAAC,IAAEA,EAAC,KAAG,EAAE,SAASA,EAAC,KAAG,EAAE,OAAOA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,QAAMA,MAAG,YAAU,OAAOA,MAAG,qBAAoBA,MAAG,aAAYA;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,SAAO,OAAO,SAASA,EAAC,KAAG,OAAO,SAAS,CAAC,IAAE,KAAG,IAAEA,KAAE,EAAE,GAAEA,KAAE,CAAC,IAAE;AAAC;AAAC,IAAI,IAAE;AAAN,IAAQE,KAAE;AAAE,SAASC,KAAG;AAAC,QAAMH,KAAE,KAAK,IAAI;AAAE,aAAU,KAAK,EAAE,KAAG,EAAE,iBAAgB;AAAC,IAAAA,MAAG,EAAE,IAAI,CAAC,KAAG,KAAG,KAAG,MAAI,EAAE,oBAAkB,EAAE,IAAI,GAAEA,EAAC,GAAE,EAAE,QAAQA,EAAC;AAAA,EAAE;AAAC;AAAC,EAAG,MAAI;AAAC,QAAMA,KAAE,KAAK,IAAI;AAAE,MAAI,IAAE;AAAE,aAAUI,MAAK,EAAE,KAAE,EAAE,KAAK,MAAM,MAAIA,GAAE,eAAe,GAAE,CAAC,GAAEA,GAAE,kBAAgB,EAAE,IAAIA,EAAC,KAAG,EAAE,IAAIA,IAAEJ,EAAC,IAAE,EAAE,OAAOI,EAAC;AAAE,MAAG,MAAIF,IAAE;AAAC,QAAGA,KAAE,GAAE,cAAc,CAAC,GAAE,MAAIA,GAAE,QAAO,MAAK,IAAE;AAAG,QAAE,YAAYC,IAAED,EAAC;AAAA,EAAC;AAAC,CAAE;;;ACA9U,IAAM,IAAE,CAAAG,OAAG;AAAC,MAAIC,KAAE,cAAcD,GAAC;AAAA,IAAC,eAAeE,IAAE;AAAC,YAAM,GAAGA,EAAC,GAAE,KAAK,kBAAgB,GAAE,KAAK,mBAAiB,GAAE,KAAK,0BAAwB,EAAG,MAAI,KAAK,eAAe,CAAE,GAAE,KAAK,KAAK,EAAE,KAAM,MAAI;AAAC,UAAE,IAAI;AAAA,MAAC,GAAI,MAAI;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,MAAAC,GAAE,IAAI;AAAA,IAAC;AAAA,IAAC,IAAI,oBAAmB;AAAC,aAAM,EAAC,KAAI,KAAK,oBAAkB,KAAI;AAAA,IAAC;AAAA,IAAC,QAAQD,KAAE,KAAK,IAAI,GAAE;AAAC,QAAE,KAAK,wBAAwB,CAAC,EAAE,KAAM,OAAG;AAAC,aAAG,KAAK,KAAK,oBAAmBA,EAAC,GAAE,KAAK,KAAK,WAAU,EAAC,aAAY,EAAC,CAAC;AAAA,MAAC,GAAI,CAAAA,OAAG;AAAC,UAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,EAAC,GAAE,KAAK,KAAK,WAAU,EAAC,aAAY,OAAG,OAAMA,GAAC,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,IAAC,MAAM,iBAAgB;AAAC,aAAM;AAAA,IAAE;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,CAAAA,OAAGA,MAAG,MAAGA,KAAEA,MAAG,IAAE,IAAE,KAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,qBAAoB,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,GAAEA,EAAC,GAAEA;AAAC;", "names": ["e", "s", "a", "l", "n", "n", "p", "e", "s"]}