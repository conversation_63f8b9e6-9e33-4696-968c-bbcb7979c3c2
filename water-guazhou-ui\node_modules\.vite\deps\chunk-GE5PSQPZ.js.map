{"version": 3, "sources": ["../../@arcgis/core/core/accessorSupport/diffUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../Accessor.js\";import t from\"../Collection.js\";import{isNone as n,isSome as o}from\"../maybe.js\";import{getProperties as r}from\"./utils.js\";const f=[\"esri.Color\",\"esri.portal.Portal\",\"esri.symbols.support.Symbol3DAnchorPosition2D\",\"esri.symbols.support.Symbol3DAnchorPosition3D\"];function i(t){return t instanceof e}function c(e){return e instanceof t?Object.keys(e.items):i(e)?r(e).keys():e?Object.keys(e):[]}function l(e,n){return e instanceof t?e.items[n]:e[n]}function u(e,t){return!(!Array.isArray(e)||!Array.isArray(t))&&e.length!==t.length}function s(e){return e?e.declaredClass:null}function p(e,t){const n=e.diff;if(n&&\"function\"==typeof n)return n(e,t);const r=c(e),a=c(t);if(0===r.length&&0===a.length)return;if(!r.length||!a.length||u(e,t))return{type:\"complete\",oldValue:e,newValue:t};const y=a.filter((e=>!r.includes(e))),m=r.filter((e=>!a.includes(e))),d=r.filter((n=>a.includes(n)&&l(e,n)!==l(t,n))).concat(y,m).sort(),b=s(e);if(b&&f.includes(b)&&d.length)return{type:\"complete\",oldValue:e,newValue:t};let h;const g=i(e)&&i(t);for(const f of d){const r=l(e,f),i=l(t,f);let c;if((g||\"function\"!=typeof r&&\"function\"!=typeof i)&&(r!==i&&(null!=r||null!=i))){if(n&&n[f]&&\"function\"==typeof n[f])c=n[f](r,i);else if(r instanceof Date&&i instanceof Date){if(r.getTime()===i.getTime())continue;c={type:\"complete\",oldValue:r,newValue:i}}else c=\"object\"==typeof r&&\"object\"==typeof i&&s(r)===s(i)?p(r,i):{type:\"complete\",oldValue:r,newValue:i};o(c)&&(o(h)?h.diff[f]=c:h={type:\"partial\",diff:{[f]:c}})}}return h}function a(e,t){if(n(e))return!1;const o=t.split(\".\");let r=e;for(const n of o){if(\"complete\"===r.type)return!0;if(\"partial\"!==r.type)return!1;{const e=r.diff[n];if(!e)return!1;r=e}}return!0}function y(e,t){for(const n of t)if(a(e,n))return!0;return!1}function m(e,t){if(!(\"function\"==typeof e||\"function\"==typeof t||n(e)&&n(t)))return n(e)||n(t)||\"object\"==typeof e&&\"object\"==typeof t&&s(e)!==s(t)?{type:\"complete\",oldValue:e,newValue:t}:p(e,t)}function d(e){if(n(e))return!0;switch(e.type){case\"complete\":return!1;case\"collection\":{const t=e;for(const e of t.added)if(!d(e))return!1;for(const e of t.removed)if(!d(e))return!1;for(const e of t.changed)if(!d(e))return!1;return!0}case\"partial\":for(const t in e.diff){if(!d(e.diff[t]))return!1}return!0}}export{m as diff,a as hasDiff,y as hasDiffAny,d as isEmpty};\n"], "mappings": ";;;;;;;;;;;;;;;AAI0J,IAAM,IAAE,CAAC,cAAa,sBAAqB,iDAAgD,+CAA+C;AAAE,SAAS,EAAEA,IAAE;AAAC,SAAOA,cAAa;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,SAAOA,cAAa,IAAE,OAAO,KAAKA,GAAE,KAAK,IAAE,EAAEA,EAAC,IAAE,EAAEA,EAAC,EAAE,KAAK,IAAEA,KAAE,OAAO,KAAKA,EAAC,IAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,SAAOA,cAAa,IAAEA,GAAE,MAAM,CAAC,IAAEA,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAED,IAAE;AAAC,SAAM,EAAE,CAAC,MAAM,QAAQC,EAAC,KAAG,CAAC,MAAM,QAAQD,EAAC,MAAIC,GAAE,WAASD,GAAE;AAAM;AAAC,SAAS,EAAEC,IAAE;AAAC,SAAOA,KAAEA,GAAE,gBAAc;AAAI;AAAC,SAAS,EAAEA,IAAED,IAAE;AAAC,QAAM,IAAEC,GAAE;AAAK,MAAG,KAAG,cAAY,OAAO,EAAE,QAAO,EAAEA,IAAED,EAAC;AAAE,QAAME,KAAE,EAAED,EAAC,GAAEE,KAAE,EAAEH,EAAC;AAAE,MAAG,MAAIE,GAAE,UAAQ,MAAIC,GAAE,OAAO;AAAO,MAAG,CAACD,GAAE,UAAQ,CAACC,GAAE,UAAQ,EAAEF,IAAED,EAAC,EAAE,QAAM,EAAC,MAAK,YAAW,UAASC,IAAE,UAASD,GAAC;AAAE,QAAMI,KAAED,GAAE,OAAQ,CAAAF,OAAG,CAACC,GAAE,SAASD,EAAC,CAAE,GAAEI,KAAEH,GAAE,OAAQ,CAAAD,OAAG,CAACE,GAAE,SAASF,EAAC,CAAE,GAAEK,KAAEJ,GAAE,OAAQ,CAAAK,OAAGJ,GAAE,SAASI,EAAC,KAAG,EAAEN,IAAEM,EAAC,MAAI,EAAEP,IAAEO,EAAC,CAAE,EAAE,OAAOH,IAAEC,EAAC,EAAE,KAAK,GAAE,IAAE,EAAEJ,EAAC;AAAE,MAAG,KAAG,EAAE,SAAS,CAAC,KAAGK,GAAE,OAAO,QAAM,EAAC,MAAK,YAAW,UAASL,IAAE,UAASD,GAAC;AAAE,MAAI;AAAE,QAAM,IAAE,EAAEC,EAAC,KAAG,EAAED,EAAC;AAAE,aAAUQ,MAAKF,IAAE;AAAC,UAAMJ,KAAE,EAAED,IAAEO,EAAC,GAAEC,KAAE,EAAET,IAAEQ,EAAC;AAAE,QAAIE;AAAE,SAAI,KAAG,cAAY,OAAOR,MAAG,cAAY,OAAOO,QAAKP,OAAIO,OAAI,QAAMP,MAAG,QAAMO,MAAI;AAAC,UAAG,KAAG,EAAED,EAAC,KAAG,cAAY,OAAO,EAAEA,EAAC,EAAE,CAAAE,KAAE,EAAEF,EAAC,EAAEN,IAAEO,EAAC;AAAA,eAAUP,cAAa,QAAMO,cAAa,MAAK;AAAC,YAAGP,GAAE,QAAQ,MAAIO,GAAE,QAAQ,EAAE;AAAS,QAAAC,KAAE,EAAC,MAAK,YAAW,UAASR,IAAE,UAASO,GAAC;AAAA,MAAC,MAAM,CAAAC,KAAE,YAAU,OAAOR,MAAG,YAAU,OAAOO,MAAG,EAAEP,EAAC,MAAI,EAAEO,EAAC,IAAE,EAAEP,IAAEO,EAAC,IAAE,EAAC,MAAK,YAAW,UAASP,IAAE,UAASO,GAAC;AAAE,QAAEC,EAAC,MAAI,EAAE,CAAC,IAAE,EAAE,KAAKF,EAAC,IAAEE,KAAE,IAAE,EAAC,MAAK,WAAU,MAAK,EAAC,CAACF,EAAC,GAAEE,GAAC,EAAC;AAAA,IAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAET,IAAED,IAAE;AAAC,MAAG,EAAEC,EAAC,EAAE,QAAM;AAAG,QAAM,IAAED,GAAE,MAAM,GAAG;AAAE,MAAIE,KAAED;AAAE,aAAU,KAAK,GAAE;AAAC,QAAG,eAAaC,GAAE,KAAK,QAAM;AAAG,QAAG,cAAYA,GAAE,KAAK,QAAM;AAAG;AAAC,YAAMD,KAAEC,GAAE,KAAK,CAAC;AAAE,UAAG,CAACD,GAAE,QAAM;AAAG,MAAAC,KAAED;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM;AAAE;AAAC,SAAS,EAAEA,IAAED,IAAE;AAAC,aAAU,KAAKA,GAAE,KAAG,EAAEC,IAAE,CAAC,EAAE,QAAM;AAAG,SAAM;AAAE;AAAC,SAAS,EAAEA,IAAED,IAAE;AAAC,MAAG,EAAE,cAAY,OAAOC,MAAG,cAAY,OAAOD,MAAG,EAAEC,EAAC,KAAG,EAAED,EAAC,GAAG,QAAO,EAAEC,EAAC,KAAG,EAAED,EAAC,KAAG,YAAU,OAAOC,MAAG,YAAU,OAAOD,MAAG,EAAEC,EAAC,MAAI,EAAED,EAAC,IAAE,EAAC,MAAK,YAAW,UAASC,IAAE,UAASD,GAAC,IAAE,EAAEC,IAAED,EAAC;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAM;AAAG,UAAOA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAW,aAAM;AAAA,IAAG,KAAI,cAAa;AAAC,YAAMD,KAAEC;AAAE,iBAAUA,MAAKD,GAAE,MAAM,KAAG,CAAC,EAAEC,EAAC,EAAE,QAAM;AAAG,iBAAUA,MAAKD,GAAE,QAAQ,KAAG,CAAC,EAAEC,EAAC,EAAE,QAAM;AAAG,iBAAUA,MAAKD,GAAE,QAAQ,KAAG,CAAC,EAAEC,EAAC,EAAE,QAAM;AAAG,aAAM;AAAA,IAAE;AAAA,IAAC,KAAI;AAAU,iBAAUD,MAAKC,GAAE,MAAK;AAAC,YAAG,CAAC,EAAEA,GAAE,KAAKD,EAAC,CAAC,EAAE,QAAM;AAAA,MAAE;AAAC,aAAM;AAAA,EAAE;AAAC;", "names": ["t", "e", "r", "a", "y", "m", "d", "n", "f", "i", "c"]}