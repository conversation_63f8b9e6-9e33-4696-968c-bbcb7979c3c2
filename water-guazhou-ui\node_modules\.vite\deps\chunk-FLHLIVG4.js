import {
  e,
  r,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";
import {
  e as e2
} from "./chunk-2CM7MIII.js";
import {
  y
} from "./chunk-REW33H3I.js";
import {
  c
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/Clonable.js
var i = (s) => {
  let i2 = class extends s {
    clone() {
      var _a;
      const o = c(e2(this), "unable to clone instance of non-accessor class"), s2 = o.metadatas, c2 = o.store, i3 = {}, l2 = /* @__PURE__ */ new Map();
      for (const r2 in s2) {
        const o2 = s2[r2], n = c2 == null ? void 0 : c2.originOf(r2), a3 = o2.clonable;
        if (o2.readOnly || false === a3 || n !== r.USER && n !== r.DEFAULTS && n !== r.WEB_MAP && n !== r.WEB_SCENE) continue;
        const p = this[r2];
        let f = null;
        f = "function" == typeof a3 ? a3(p) : "reference" === a3 ? p : y(p), null != p && null == f || (n === r.DEFAULTS ? l2.set(r2, f) : i3[r2] = f);
      }
      const a2 = new (0, Object.getPrototypeOf(this).constructor)(i3);
      if (l2.size) {
        const o2 = (_a = e2(a2)) == null ? void 0 : _a.store;
        if (o2) for (const [s3, t] of l2) o2.set(s3, t, r.DEFAULTS);
      }
      return a2;
    }
  };
  return i2 = e([a("esri.core.Clonable")], i2), i2;
};
var l = class extends i(v) {
};
l = e([a("esri.core.Clonable")], l);

export {
  i,
  l
};
//# sourceMappingURL=chunk-FLHLIVG4.js.map
