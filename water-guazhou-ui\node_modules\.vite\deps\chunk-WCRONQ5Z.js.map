{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/graphics/GraphicBoundsRenderer.js", "../../@arcgis/core/views/2d/layers/graphics/BaseGraphicContainer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{dispose<PERSON><PERSON>be as t}from\"../../../../core/maybe.js\";import{g as e,m as r,h as s,d as i,r as o}from\"../../../../chunks/mat3.js\";import{c as a}from\"../../../../chunks/mat3f32.js\";import{f as n}from\"../../../../chunks/vec2f32.js\";import{f}from\"../../../../chunks/vec3f32.js\";import{normalizeMapX as l}from\"../../../../geometry/support/normalizeUtils.js\";import{DisplayObject as u}from\"../../engine/DisplayObject.js\";import{createProgramDescriptor as _}from\"../../engine/webgl/Utils.js\";import{BufferObject as c}from\"../../../webgl/BufferObject.js\";import{BlendFactor as h,PrimitiveType as m,DataType as d,Usage as p}from\"../../../webgl/enums.js\";import{VertexArrayObject as g}from\"../../../webgl/VertexArrayObject.js\";const v=Math.PI/180,x=4;class b extends u{constructor(t){super(),this._program=null,this._vao=null,this._vertexBuffer=null,this._indexBuffer=null,this._dvsMat3=a(),this._localOrigin={x:0,y:0},this._getBounds=t}destroy(){this._vao&&(this._vao.dispose(!0),this._vao=null,this._vertexBuffer=null,this._indexBuffer=null),this._program=t(this._program)}doRender(t){const{context:e}=t,r=this._getBounds();if(r.length<1)return;this._createShaderProgram(e),this._updateMatricesAndLocalOrigin(t),this._updateBufferData(e,r),e.setBlendingEnabled(!0),e.setDepthTestEnabled(!1),e.setStencilWriteMask(0),e.setStencilTestEnabled(!1),e.setBlendFunction(h.ONE,h.ONE_MINUS_SRC_ALPHA),e.setColorMask(!0,!0,!0,!0);const s=this._program;e.bindVAO(this._vao),e.useProgram(s),s.setUniformMatrix3fv(\"u_dvsMat3\",this._dvsMat3),e.gl.lineWidth(1),e.drawElements(m.LINES,8*r.length,d.UNSIGNED_INT,0),e.bindVAO()}_createTransforms(){return{dvs:a()}}_createShaderProgram(t){if(this._program)return;const e=\"precision highp float;\\n        uniform mat3 u_dvsMat3;\\n\\n        attribute vec2 a_position;\\n\\n        void main() {\\n          mediump vec3 pos = u_dvsMat3 * vec3(a_position, 1.0);\\n          gl_Position = vec4(pos.xy, 0.0, 1.0);\\n        }\",r=\"precision mediump float;\\n      void main() {\\n        gl_FragColor = vec4(0.75, 0.0, 0.0, 0.75);\\n      }\";this._program=t.programCache.acquire(e,r,y().attributes)}_updateMatricesAndLocalOrigin(t){const{state:a}=t,{displayMat3:u,size:_,resolution:c,pixelRatio:h,rotation:m,viewpoint:d}=a,p=v*m,{x:g,y:x}=d.targetGeometry,b=l(g,a.spatialReference);this._localOrigin.x=b,this._localOrigin.y=x;const y=h*_[0],B=h*_[1],M=c*y,j=c*B,A=e(this._dvsMat3);r(A,A,u),s(A,A,n(y/2,B/2)),i(A,A,f(_[0]/M,-B/j,1)),o(A,A,-p)}_updateBufferData(t,e){const{x:r,y:s}=this._localOrigin,i=2*x*e.length,o=new Float32Array(i),a=new Uint32Array(8*e.length);let n=0,f=0;for(const l of e)l&&(o[2*n+0]=l[0]-r,o[2*n+1]=l[1]-s,o[2*n+2]=l[0]-r,o[2*n+3]=l[3]-s,o[2*n+4]=l[2]-r,o[2*n+5]=l[3]-s,o[2*n+6]=l[2]-r,o[2*n+7]=l[1]-s,a[f+0]=n+0,a[f+1]=n+3,a[f+2]=n+3,a[f+3]=n+2,a[f+4]=n+2,a[f+5]=n+1,a[f+6]=n+1,a[f+7]=n+0,n+=4,f+=8);if(this._vertexBuffer?this._vertexBuffer.setData(o.buffer):this._vertexBuffer=c.createVertex(t,p.DYNAMIC_DRAW,o.buffer),this._indexBuffer?this._indexBuffer.setData(a):this._indexBuffer=c.createIndex(t,p.DYNAMIC_DRAW,a),!this._vao){const e=y();this._vao=new g(t,e.attributes,e.bufferLayouts,{geometry:this._vertexBuffer},this._indexBuffer)}}}const y=()=>_(\"bounds\",{geometry:[{location:0,name:\"a_position\",count:2,type:d.FLOAT}]});export{b as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{destroyMaybe as e}from\"../../../../core/maybe.js\";import{FeatureContainer as t}from\"../../engine/FeatureContainer.js\";import r from\"./GraphicBoundsRenderer.js\";import{CompareFunction as s}from\"../../../webgl/enums.js\";let n=class extends t{constructor(e){super(e),this.hasHighlight=()=>!0}destroy(){super.destroy(),this._boundsRenderer=e(this._boundsRenderer)}enableRenderingBounds(e){this._boundsRenderer=new r(e),this.requestRender()}get hasLabels(){return!1}onTileData(e,t){e.patch(t),this.contains(e)||this.addChild(e),this.requestRender()}onTileError(e){e.clear(),this.contains(e)||this.addChild(e)}_renderChildren(e,t){for(const r of this.children)r.isReady&&r.hasData&&(r.commit(e),e.context.setStencilFunction(s.EQUAL,r.stencilRef,255),r.getDisplayList().replay(e,r,t))}};export{n as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgtB,IAAM,IAAE,KAAK,KAAG;AAAhB,IAAoB,IAAE;AAAE,IAAM,IAAN,cAAgBA,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,GAAE,KAAK,WAAS,MAAK,KAAK,OAAK,MAAK,KAAK,gBAAc,MAAK,KAAK,eAAa,MAAK,KAAK,WAAS,EAAE,GAAE,KAAK,eAAa,EAAC,GAAE,GAAE,GAAE,EAAC,GAAE,KAAK,aAAWA;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAO,KAAK,KAAK,QAAQ,IAAE,GAAE,KAAK,OAAK,MAAK,KAAK,gBAAc,MAAK,KAAK,eAAa,OAAM,KAAK,WAAS,EAAE,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,UAAK,EAAC,SAAQC,GAAC,IAAED,IAAED,KAAE,KAAK,WAAW;AAAE,QAAGA,GAAE,SAAO,EAAE;AAAO,SAAK,qBAAqBE,EAAC,GAAE,KAAK,8BAA8BD,EAAC,GAAE,KAAK,kBAAkBC,IAAEF,EAAC,GAAEE,GAAE,mBAAmB,IAAE,GAAEA,GAAE,oBAAoB,KAAE,GAAEA,GAAE,oBAAoB,CAAC,GAAEA,GAAE,sBAAsB,KAAE,GAAEA,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE;AAAE,UAAM,IAAE,KAAK;AAAS,IAAAA,GAAE,QAAQ,KAAK,IAAI,GAAEA,GAAE,WAAW,CAAC,GAAE,EAAE,oBAAoB,aAAY,KAAK,QAAQ,GAAEA,GAAE,GAAG,UAAU,CAAC,GAAEA,GAAE,aAAa,EAAE,OAAM,IAAEF,GAAE,QAAO,EAAE,cAAa,CAAC,GAAEE,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAI,EAAE,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBD,IAAE;AAAC,QAAG,KAAK,SAAS;AAAO,UAAMC,KAAE,uPAAsPF,KAAE;AAA6G,SAAK,WAASC,GAAE,aAAa,QAAQC,IAAEF,IAAE,EAAE,EAAE,UAAU;AAAA,EAAC;AAAA,EAAC,8BAA8BC,IAAE;AAAC,UAAK,EAAC,OAAME,GAAC,IAAEF,IAAE,EAAC,aAAY,GAAE,MAAK,GAAE,YAAW,GAAE,YAAWG,IAAE,UAAS,GAAE,WAAU,EAAC,IAAED,IAAE,IAAE,IAAE,GAAE,EAAC,GAAE,GAAE,GAAEE,GAAC,IAAE,EAAE,gBAAeC,KAAE,EAAE,GAAEH,GAAE,gBAAgB;AAAE,SAAK,aAAa,IAAEG,IAAE,KAAK,aAAa,IAAED;AAAE,UAAME,KAAEH,KAAE,EAAE,CAAC,GAAE,IAAEA,KAAE,EAAE,CAAC,GAAEI,KAAE,IAAED,IAAE,IAAE,IAAE,GAAE,IAAE,EAAE,KAAK,QAAQ;AAAE,MAAE,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,EAAEA,KAAE,GAAE,IAAE,CAAC,CAAC,GAAE,EAAE,GAAE,GAAEP,GAAE,EAAE,CAAC,IAAEQ,IAAE,CAAC,IAAE,GAAE,CAAC,CAAC,GAAEJ,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAEC,IAAE;AAAC,UAAK,EAAC,GAAEF,IAAE,GAAE,EAAC,IAAE,KAAK,cAAaS,KAAE,IAAE,IAAEP,GAAE,QAAOQ,KAAE,IAAI,aAAaD,EAAC,GAAEN,KAAE,IAAI,YAAY,IAAED,GAAE,MAAM;AAAE,QAAIS,KAAE,GAAEC,KAAE;AAAE,eAAU,KAAKV,GAAE,OAAIQ,GAAE,IAAEC,KAAE,CAAC,IAAE,EAAE,CAAC,IAAEX,IAAEU,GAAE,IAAEC,KAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAED,GAAE,IAAEC,KAAE,CAAC,IAAE,EAAE,CAAC,IAAEX,IAAEU,GAAE,IAAEC,KAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAED,GAAE,IAAEC,KAAE,CAAC,IAAE,EAAE,CAAC,IAAEX,IAAEU,GAAE,IAAEC,KAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAED,GAAE,IAAEC,KAAE,CAAC,IAAE,EAAE,CAAC,IAAEX,IAAEU,GAAE,IAAEC,KAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAER,GAAES,KAAE,CAAC,IAAED,KAAE,GAAER,GAAES,KAAE,CAAC,IAAED,KAAE,GAAER,GAAES,KAAE,CAAC,IAAED,KAAE,GAAER,GAAES,KAAE,CAAC,IAAED,KAAE,GAAER,GAAES,KAAE,CAAC,IAAED,KAAE,GAAER,GAAES,KAAE,CAAC,IAAED,KAAE,GAAER,GAAES,KAAE,CAAC,IAAED,KAAE,GAAER,GAAES,KAAE,CAAC,IAAED,KAAE,GAAEA,MAAG,GAAEC,MAAG;AAAG,QAAG,KAAK,gBAAc,KAAK,cAAc,QAAQF,GAAE,MAAM,IAAE,KAAK,gBAAcG,GAAE,aAAaZ,IAAE,EAAE,cAAaS,GAAE,MAAM,GAAE,KAAK,eAAa,KAAK,aAAa,QAAQP,EAAC,IAAE,KAAK,eAAaU,GAAE,YAAYZ,IAAE,EAAE,cAAaE,EAAC,GAAE,CAAC,KAAK,MAAK;AAAC,YAAMD,KAAE,EAAE;AAAE,WAAK,OAAK,IAAIU,GAAEX,IAAEC,GAAE,YAAWA,GAAE,eAAc,EAAC,UAAS,KAAK,cAAa,GAAE,KAAK,YAAY;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,MAAI,GAAE,UAAS,EAAC,UAAS,CAAC,EAAC,UAAS,GAAE,MAAK,cAAa,OAAM,GAAE,MAAK,EAAE,MAAK,CAAC,EAAC,CAAC;;;ACAh+F,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYY,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,MAAI;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,kBAAgB,EAAE,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,SAAK,kBAAgB,IAAI,EAAEA,EAAC,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,WAAWA,IAAEC,IAAE;AAAC,IAAAD,GAAE,MAAMC,EAAC,GAAE,KAAK,SAASD,EAAC,KAAG,KAAK,SAASA,EAAC,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,IAAAA,GAAE,MAAM,GAAE,KAAK,SAASA,EAAC,KAAG,KAAK,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEC,IAAE;AAAC,eAAUC,MAAK,KAAK,SAAS,CAAAA,GAAE,WAASA,GAAE,YAAUA,GAAE,OAAOF,EAAC,GAAEA,GAAE,QAAQ,mBAAmB,EAAE,OAAME,GAAE,YAAW,GAAG,GAAEA,GAAE,eAAe,EAAE,OAAOF,IAAEE,IAAED,EAAC;AAAA,EAAE;AAAC;", "names": ["r", "t", "e", "a", "h", "x", "b", "y", "M", "i", "o", "n", "f", "E", "e", "t", "r"]}