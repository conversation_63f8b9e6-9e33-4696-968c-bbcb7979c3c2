<template>
  <el-dialog
    v-model="dialogVisible"
    title="智能分析"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="analysis-content">
      <!-- 分析说明 -->
      <el-alert
        title="智能分析说明"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      >
        <template #default>
          <div>
            <p>通过原水液位变化反馈，引入智能化算法，分析地下水涵养水位，给出地下水涵养建议</p>
            <p>分析内容包括：涵养系数、涵养潜力、风险评估、涵养建议等</p>
          </div>
        </template>
      </el-alert>

      <!-- 分析参数 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="选择测点" prop="stationId" required>
          <el-select
            v-model="form.stationId"
            placeholder="请选择测点"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="station in stationList"
              :key="station.id"
              :label="station.name"
              :value="station.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分析周期" prop="timeRange" required>
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="x"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="分析说明">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分析说明（可选）"
          />
        </el-form-item>
      </el-form>

      <!-- 数据统计 -->
      <div v-if="dataStats" class="stats-section">
        <div class="stats-title">数据统计</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-label">数据条数</div>
              <div class="stat-value">{{ dataStats.totalRecords }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-label">时间跨度</div>
              <div class="stat-value">{{ dataStats.timeSpan }}天</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-label">平均水位</div>
              <div class="stat-value">{{ dataStats.avgLevel }}m</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handlePreview" :loading="previewLoading">
          数据预览
        </el-button>
        <el-button 
          type="success" 
          @click="handleAnalysis" 
          :loading="loading"
          :disabled="!form.stationId || !timeRange"
        >
          开始分析
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  performIntelligentAnalysis,
  getWaterLevelChangeData,
  getStatisticsData
} from '@/api/waterSource/conservationWaterLevel'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: []
}>()

// 响应式数据
const loading = ref(false)
const previewLoading = ref(false)
const formRef = ref<FormInstance>()
const stationList = ref<any[]>([])
const dataStats = ref<any>(null)

// 表单数据
const form = reactive({
  stationId: '',
  description: ''
})

// 时间范围
const timeRange = ref<[number, number] | null>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单验证规则
const rules: FormRules = {
  stationId: [
    { required: true, message: '请选择测点', trigger: 'change' }
  ],
  timeRange: [
    { required: true, message: '请选择分析周期', trigger: 'change' }
  ]
}

// 生命周期
onMounted(() => {
  loadStationList()
})

// 加载测点列表
const loadStationList = async () => {
  try {
    // 临时模拟数据
    stationList.value = [
      { id: '1', name: '测点1', location: '位置1' },
      { id: '2', name: '测点2', location: '位置2' },
      { id: '3', name: '测点3', location: '位置3' }
    ]
  } catch (error) {
    console.error('加载测点列表失败:', error)
  }
}

// 数据预览
const handlePreview = async () => {
  if (!form.stationId || !timeRange.value) {
    ElMessage.warning('请先选择测点和分析周期')
    return
  }

  try {
    previewLoading.value = true
    const [startTime, endTime] = timeRange.value
    
    // 获取统计数据
    const statsResponse = await getStatisticsData(form.stationId, startTime, endTime)
    if (statsResponse.code === 200) {
      const stats = statsResponse.data
      dataStats.value = {
        totalRecords: stats.totalRecords || 0,
        timeSpan: Math.ceil((endTime - startTime) / (24 * 60 * 60 * 1000)),
        avgLevel: stats.avgGroundwaterLevel ? Number(stats.avgGroundwaterLevel).toFixed(3) : '0.000'
      }
      
      if (stats.totalRecords === 0) {
        ElMessage.warning('选择的时间范围内没有数据，无法进行分析')
      } else {
        ElMessage.success(`找到 ${stats.totalRecords} 条数据，可以进行分析`)
      }
    }
  } catch (error) {
    console.error('数据预览失败:', error)
    ElMessage.error('数据预览失败')
  } finally {
    previewLoading.value = false
  }
}

// 开始分析
const handleAnalysis = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (!timeRange.value) {
      ElMessage.warning('请选择分析周期')
      return
    }

    loading.value = true
    const [startTime, endTime] = timeRange.value
    
    const response = await performIntelligentAnalysis(form.stationId, startTime, endTime)
    if (response.code === 200) {
      ElMessage.success('分析任务已启动，请稍后查看结果')
      emit('confirm')
      handleClose()
    }
  } catch (error) {
    console.error('启动分析失败:', error)
    ElMessage.error('启动分析失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  timeRange.value = null
  dataStats.value = null
  emit('update:visible', false)
}
</script>

<style scoped>
.analysis-content {
  padding: 0 10px;
}

.stats-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stats-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #409EFF;
}

.dialog-footer {
  text-align: right;
}
</style>
