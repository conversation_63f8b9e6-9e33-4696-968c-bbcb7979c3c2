{"version": 3, "sources": ["../../@arcgis/core/arcade/arcadeTimeUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{UnknownTimeZone as e,ArcadeDateError as t,ArcadeDateErrorCodes as i}from\"./ArcadeDate.js\";import n from\"../layers/support/FieldsIndex.js\";import a from\"../time/TimeReference.js\";import{Zone as r,SystemZone as s,FixedOffsetZone as d,IANAZone as o}from\"luxon\";import{convertLegacyTimeZone as f}from\"../time/timeReferenceUtils.js\";function l(e){return e instanceof a}function m(n){if(!n)return null;if(l(n)&&(n=c(n.toJSON())),n instanceof r)return n;if(\"unknown\"===n.toLowerCase())return e.instance;if(\"system\"===n.toLowerCase())return s.instance;if(\"utc\"===n.toLowerCase())return d.utcInstance;if(/^[\\+\\-]?[0-9]{1,2}([:][0-9]{2})?$/.test(n)){const e=d.parseSpecifier(\"UTC\"+(n.startsWith(\"+\")||n.startsWith(\"-\")?\"\":\"+\")+n);if(e)return e}const a=o.create(n);if(!a.isValid)throw new t(i.TimeZoneNotRecognised);return a}function c(e){return e?.timeZoneIANA?e?.timeZoneIANA:e?.timeZone?f(e,\"\"):\"\"}class T{constructor(){this.dateTimeReferenceMetaData=null,this._fieldTimeZoneIndex={},this._fieldIndex=null,this._ianaPreferred=null,this._ianaTimeInfo=null,this._ianaEditFields=null,this._ianaLayerDateFields=null}static create(e,t){const i=new T;return i.dateTimeReferenceMetaData=t,i._fieldIndex=e instanceof n?e:new n(e),i}static createFromLayer(e){if(!e)return null;if(!e.fieldsIndex)return!e.declaredClass&&e.fields?T.create(e.fields,e):null;const t=new T;return t._fieldIndex=e.fieldsIndex,t.dateTimeReferenceMetaData={timeInfo:e?.timeInfo?.toJSON()??null,editFieldsInfo:e?.editFieldsInfo?.toJSON()??null,dateFieldsTimeReference:e?.dateFieldsTimeReference?.toJSON()??null,preferredTimeReference:e?.preferredTimeReference?.toJSON()??null,datesInUnknownTimezone:!0===e?.datesInUnknownTimezone},t}fieldTimeZone(e){const t=this._fieldIndex?.get(e);if(!t)return null;if(\"date\"!==t.type&&\"esriFieldTypeDate\"!==t.type)return null;const i=this._fieldTimeZoneIndex[t.name];if(void 0!==i)return i;const n=[{field:this.dateTimeReferenceMetaData?.editFieldsInfo?.creationDateField,timeReference:this.dateTimeReferenceMetaData?.editFieldsInfo?.dateFieldsTimeReference,isunknown:!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone},{field:this.dateTimeReferenceMetaData?.editFieldsInfo?.editDateField,timeReference:this.dateTimeReferenceMetaData?.editFieldsInfo?.dateFieldsTimeReference,isunknown:!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone},{field:this.dateTimeReferenceMetaData?.timeInfo?.startTimeField,timeReference:this.dateTimeReferenceMetaData?.timeInfo?.timeReference,isunknown:!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone},{field:this.dateTimeReferenceMetaData?.timeInfo?.endTimeField,timeReference:this.dateTimeReferenceMetaData?.timeInfo?.timeReference,isunknown:!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone}];for(const r of n)if(r.field===t.name){const e=this.convertToIANA(r.timeReference,r.isunknown);return this._fieldTimeZoneIndex[t.name]=e,e}const a=this.convertToIANA(this.dateTimeReferenceMetaData?.dateFieldsTimeReference,this.dateTimeReferenceMetaData?.datesInUnknownTimezone);return this._fieldTimeZoneIndex[t.name]=a,a}convertToIANA(e,t){return t?\"unknown\":c(e)}get layerPreferredTimeZone(){if(null!==this._ianaPreferred)return this._ianaPreferred;this._ianaPreferred=\"\";const e=this.dateTimeReferenceMetaData?.preferredTimeReference;return this._ianaPreferred=this.convertToIANA(e,!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone),this._ianaPreferred}get layerTimeInfoTimeZone(){if(null!==this._ianaTimeInfo)return this._ianaTimeInfo;this._ianaTimeInfo=\"\";const e=this.dateTimeReferenceMetaData?.timeInfo?.timeReference;return this._ianaTimeInfo=this.convertToIANA(e,!1),this._ianaTimeInfo}get layerEditFieldsTimeZone(){if(null!==this._ianaEditFields)return this._ianaEditFields;this._ianaEditFields=\"\";const e=this.dateTimeReferenceMetaData?.editFieldsInfo?.dateFieldsTimeReference;return this._ianaEditFields=this.convertToIANA(e,this.dateTimeReferenceMetaData?.datesInUnknownTimezone),this._ianaEditFields}get layerDateFieldsTimeZone(){if(null!==this._ianaLayerDateFields)return this._ianaLayerDateFields;this._ianaLayerDateFields=\"\";const e=this.dateTimeReferenceMetaData?.dateFieldsTimeReference;return this._ianaLayerDateFields=this.convertToIANA(e,!0===this.dateTimeReferenceMetaData?.datesInUnknownTimezone),this._ianaLayerDateFields}}export{T as DateTimeReferenceFieldIndex,m as convertTimeReference,c as convertToIANA};\n"], "mappings": ";;;;;;;;AAIqzB,SAAS,EAAE,GAAE;AAAC,UAAO,uBAAG,gBAAa,uBAAG,gBAAa,uBAAG,YAAS,EAAE,GAAE,EAAE,IAAE;AAAE;AAAC,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,cAAa;AAAC,SAAK,4BAA0B,MAAK,KAAK,sBAAoB,CAAC,GAAE,KAAK,cAAY,MAAK,KAAK,iBAAe,MAAK,KAAK,gBAAc,MAAK,KAAK,kBAAgB,MAAK,KAAK,uBAAqB;AAAA,EAAI;AAAA,EAAC,OAAO,OAAO,GAAE,GAAE;AAAC,UAAMA,KAAE,IAAI;AAAE,WAAOA,GAAE,4BAA0B,GAAEA,GAAE,cAAY,aAAa,IAAE,IAAE,IAAI,EAAE,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAO,gBAAgB,GAAE;AAJhuC;AAIiuC,QAAG,CAAC,EAAE,QAAO;AAAK,QAAG,CAAC,EAAE,YAAY,QAAM,CAAC,EAAE,iBAAe,EAAE,SAAO,GAAE,OAAO,EAAE,QAAO,CAAC,IAAE;AAAK,UAAM,IAAE,IAAI;AAAE,WAAO,EAAE,cAAY,EAAE,aAAY,EAAE,4BAA0B,EAAC,YAAS,4BAAG,aAAH,mBAAa,aAAU,MAAK,kBAAe,4BAAG,mBAAH,mBAAmB,aAAU,MAAK,2BAAwB,4BAAG,4BAAH,mBAA4B,aAAU,MAAK,0BAAuB,4BAAG,2BAAH,mBAA2B,aAAU,MAAK,wBAAuB,UAAK,uBAAG,wBAAsB,GAAE;AAAA,EAAC;AAAA,EAAC,cAAc,GAAE;AAJjrD;AAIkrD,UAAM,KAAE,UAAK,gBAAL,mBAAkB,IAAI;AAAG,QAAG,CAAC,EAAE,QAAO;AAAK,QAAG,WAAS,EAAE,QAAM,wBAAsB,EAAE,KAAK,QAAO;AAAK,UAAMA,KAAE,KAAK,oBAAoB,EAAE,IAAI;AAAE,QAAG,WAASA,GAAE,QAAOA;AAAE,UAAM,IAAE,CAAC,EAAC,QAAM,gBAAK,8BAAL,mBAAgC,mBAAhC,mBAAgD,mBAAkB,gBAAc,gBAAK,8BAAL,mBAAgC,mBAAhC,mBAAgD,yBAAwB,WAAU,WAAK,UAAK,8BAAL,mBAAgC,wBAAsB,GAAE,EAAC,QAAM,gBAAK,8BAAL,mBAAgC,mBAAhC,mBAAgD,eAAc,gBAAc,gBAAK,8BAAL,mBAAgC,mBAAhC,mBAAgD,yBAAwB,WAAU,WAAK,UAAK,8BAAL,mBAAgC,wBAAsB,GAAE,EAAC,QAAM,gBAAK,8BAAL,mBAAgC,aAAhC,mBAA0C,gBAAe,gBAAc,gBAAK,8BAAL,mBAAgC,aAAhC,mBAA0C,eAAc,WAAU,WAAK,UAAK,8BAAL,mBAAgC,wBAAsB,GAAE,EAAC,QAAM,gBAAK,8BAAL,mBAAgC,aAAhC,mBAA0C,cAAa,gBAAc,gBAAK,8BAAL,mBAAgC,aAAhC,mBAA0C,eAAc,WAAU,WAAK,UAAK,8BAAL,mBAAgC,wBAAsB,CAAC;AAAE,eAAUC,MAAK,EAAE,KAAGA,GAAE,UAAQ,EAAE,MAAK;AAAC,YAAMC,KAAE,KAAK,cAAcD,GAAE,eAAcA,GAAE,SAAS;AAAE,aAAO,KAAK,oBAAoB,EAAE,IAAI,IAAEC,IAAEA;AAAA,IAAC;AAAC,UAAMC,KAAE,KAAK,eAAc,UAAK,8BAAL,mBAAgC,0BAAwB,UAAK,8BAAL,mBAAgC,sBAAsB;AAAE,WAAO,KAAK,oBAAoB,EAAE,IAAI,IAAEA,IAAEA;AAAA,EAAC;AAAA,EAAC,cAAc,GAAE,GAAE;AAAC,WAAO,IAAE,YAAU,EAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,yBAAwB;AAJplG;AAIqlG,QAAG,SAAO,KAAK,eAAe,QAAO,KAAK;AAAe,SAAK,iBAAe;AAAG,UAAM,KAAE,UAAK,8BAAL,mBAAgC;AAAuB,WAAO,KAAK,iBAAe,KAAK,cAAc,GAAE,WAAK,UAAK,8BAAL,mBAAgC,uBAAsB,GAAE,KAAK;AAAA,EAAc;AAAA,EAAC,IAAI,wBAAuB;AAJh4G;AAIi4G,QAAG,SAAO,KAAK,cAAc,QAAO,KAAK;AAAc,SAAK,gBAAc;AAAG,UAAM,KAAE,gBAAK,8BAAL,mBAAgC,aAAhC,mBAA0C;AAAc,WAAO,KAAK,gBAAc,KAAK,cAAc,GAAE,KAAE,GAAE,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,0BAAyB;AAJjnH;AAIknH,QAAG,SAAO,KAAK,gBAAgB,QAAO,KAAK;AAAgB,SAAK,kBAAgB;AAAG,UAAM,KAAE,gBAAK,8BAAL,mBAAgC,mBAAhC,mBAAgD;AAAwB,WAAO,KAAK,kBAAgB,KAAK,cAAc,IAAE,UAAK,8BAAL,mBAAgC,sBAAsB,GAAE,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,0BAAyB;AAJh7H;AAIi7H,QAAG,SAAO,KAAK,qBAAqB,QAAO,KAAK;AAAqB,SAAK,uBAAqB;AAAG,UAAM,KAAE,UAAK,8BAAL,mBAAgC;AAAwB,WAAO,KAAK,uBAAqB,KAAK,cAAc,GAAE,WAAK,UAAK,8BAAL,mBAAgC,uBAAsB,GAAE,KAAK;AAAA,EAAoB;AAAC;", "names": ["i", "r", "e", "a"]}