{"version": 3, "sources": ["../../@arcgis/core/layers/save/featureLayerUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{difference as e}from\"../../core/arrayUtils.js\";import t from\"../../core/Error.js\";import r from\"../../core/Logger.js\";import{isNone as a,isSome as o}from\"../../core/maybe.js\";import{debounce as s,eachAlways as l}from\"../../core/promiseUtils.js\";import{updateOrigins as i}from\"../../core/accessorSupport/originUtils.js\";import n from\"../FeatureLayer.js\";import{parse as u}from\"../support/arcgisLayerUrl.js\";import{fetchFeatureService as p}from\"../support/fetchService.js\";import{isFeatureCollectionLayer as c,isFeatureServiceLayer as y}from\"../support/layerUtils.js\";import d from\"../../portal/Portal.js\";import m from\"../../portal/PortalItem.js\";import{createForItemWrite as f}from\"../../portal/support/jsonContext.js\";import{addTypeKeyword as w,TypeKeyword as h,getWGS84ExtentForItem as v,removeTypeKeyword as b}from\"../../portal/support/portalItemUtils.js\";const I=r.getLogger(\"esri.layers.FeatureLayer\"),S=\"Feature Service\";function g(e,t){return`Layer (title: ${e.title}, id: ${e.id}) of type '${e.declaredClass}' ${t}`}function j(e,r){if(r.type!==S)throw new t(\"feature-layer:portal-item-wrong-type\",g(e,`should have portal item of type \"${S}\"`))}async function L(e){if(await e.load(),c(e))throw new t(\"feature-layer:save\",g(e,\"using an in-memory source cannot be saved to a portal item\"))}function P(e,r){let a=(e.messages??[]).filter((({type:e})=>\"error\"===e)).map((({name:e,message:r,details:a})=>new t(e,r,a)));if(r?.ignoreUnsupported&&(a=a.filter((({name:e})=>\"layer:unsupported\"!==e&&\"symbol:unsupported\"!==e&&\"symbol-layer:unsupported\"!==e&&\"property:unsupported\"!==e&&\"url:unsupported\"!==e))),a.length>0)throw new t(\"feature-layer:save\",\"Failed to save feature layer due to unsupported or invalid content. See 'details.errors' for more detailed information\",{errors:a})}async function J(e,t,r){\"beforeSave\"in e&&\"function\"==typeof e.beforeSave&&await e.beforeSave();const a=e.write({},t);return P(t,r),a}function N(e){const{layer:t,layerJSON:r}=e;return t.isTable?{layers:[],tables:[r]}:{layers:[r],tables:[]}}function O(e){w(e,h.JSAPI),e.typeKeywords&&(e.typeKeywords=e.typeKeywords.filter(((e,t,r)=>r.indexOf(e)===t)))}function E(e){const r=e.portalItem;if(!r)throw I.error(\"save: requires the portalItem property to be set\"),new t(\"feature-layer:portal-item-not-set\",g(e,\"requires the portalItem property to be set\"));if(!r.loaded)throw new t(\"feature-layer:portal-item-not-loaded\",g(e,\"cannot be saved to a portal item that does not exist or is inaccessible\"));j(e,r)}async function T(e,t){return/\\/\\d+\\/?$/.test(e.url??\"\")?N(t[0]):$(e,t)}async function $(e,t){const{layer:{url:r,customParameters:a,apiKey:o}}=t[0];let s=await e.fetchData(\"json\");s&&null!=s.layers&&null!=s.tables||(s=await x(s,{url:r??\"\",customParameters:a,apiKey:o},t.map((e=>e.layer.layerId))));for(const l of t)K(l.layer,l.layerJSON,s);return s}async function x(e,t,r){var a,o;e||(e={}),(a=e).layers||(a.layers=[]),(o=e).tables||(o.tables=[]);const{url:s,customParameters:l,apiKey:i}=t,{serviceJSON:n,layersJSON:u}=await p(s,{customParameters:l,apiKey:i}),c=A(e.layers,n.layers,r),y=A(e.tables,n.tables,r);e.layers=c.itemResources,e.tables=y.itemResources;const d=[...c.added,...y.added],m=u?[...u.layers,...u.tables]:[];return await U(e,d,s,m),e}function A(t,r,a){const o=e(t,r,((e,t)=>e.id===t.id));t=t.filter((e=>!o.removed.some((t=>t.id===e.id))));const s=o.added.map((({id:e})=>({id:e})));return s.forEach((({id:e})=>{t.push({id:e})})),{itemResources:t,added:s.filter((({id:e})=>!a.includes(e)))}}async function U(e,t,r,o){const s=t.map((({id:e})=>new n({url:r,layerId:e,sourceJSON:o.find((({id:t})=>t===e))})));await l(s.map((e=>e.load()))),s.forEach((t=>{const{layerId:r,loaded:o,defaultPopupTemplate:s}=t;if(!o||a(s))return;K(t,{id:r,popupInfo:s.toJSON()},e)}))}function K(e,t,r){e.isTable?F(r.tables,t):F(r.layers,t)}function F(e,t){if(!e)return;const r=e.findIndex((({id:e})=>e===t.id));-1===r?e.push(t):e[r]=t}function R(e){const{portalItem:t}=e;return y(e)&&!e.dynamicDataSource&&!!t?.loaded&&t.type===S}async function D(e){if(!e?.length)throw new t(\"feature-layer-utils-saveall:missing-parameters\",\"'layers' array should contain at least one feature layer\");await Promise.all(e.map((e=>e.load())));for(const o of e)if(!R(o))throw new t(\"feature-layer-utils-saveall:invalid-parameters\",`'layers' array should only contain layers or tables in a feature service loaded from 'Feature Service' item. ${g(o,\"does not conform\")}`,{layer:o});const r=e.map((e=>e.portalItem.id));if(new Set(r).size>1)throw new t(\"feature-layer-utils-saveall:invalid-parameters\",\"All layers in the 'layers' array should be loaded from the same portal item\");const a=e.map((e=>e.layerId));if(new Set(a).size!==a.length)throw new t(\"feature-layer-utils-saveall:invalid-parameters\",\"'layers' array should contain only one instance each of layer or table in a feature service\")}function q(e,t){var r,a;let o=m.from(t);return o.id&&(o=o.clone(),o.id=null),(r=o).type??(r.type=S),(a=o).portal??(a.portal=d.getDefault()),j(e,o),o}async function z(e,t){const{url:r,layerId:a,title:s,fullExtent:l,isTable:i}=e,n=u(r),p=o(n)&&\"FeatureServer\"===n.serverType;t.url=p?r:`${r}/${a}`,t.title||(t.title=s),t.extent=null,!i&&o(l)&&(t.extent=await v(l)),b(t,h.METADATA),b(t,h.MULTI_LAYER),w(t,h.SINGLE_LAYER),i&&w(t,h.TABLE),O(t)}async function C(e,t,r){const a=e.portal;await(a?.signIn()),await(a?.user?.addItem({item:e,data:t,folder:r?.folder}))}const M=s(Y);async function Y(e,t){await L(e),E(e);const r=e.portalItem,a=f(r),o=await J(e,a,t),s=await T(r,[{layer:e,layerJSON:o}]);return O(r),await r.update({data:s}),i(a),r}const _=s((async(e,t)=>{await D(e);const r=e[0].portalItem,a=f(r),o=await Promise.all(e.map((e=>J(e,a,t)))),s=await T(r,e.map(((e,t)=>({layer:e,layerJSON:o[t]}))));return O(r),await r.update({data:s}),await Promise.all(e.slice(1).map((e=>e.portalItem.reload()))),i(a),r.clone()})),B=s(G);async function G(e,t,r){await L(e);const a=q(e,t),o=f(a),s=N({layer:e,layerJSON:await J(e,o,r)});return await z(e,a),await C(a,s,r),e.portalItem=a,i(o),a}export{M as save,_ as saveAll,B as saveAs};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIk2B,IAAM,IAAEA,GAAE,UAAU,0BAA0B;AAA9C,IAAgD,IAAE;AAAkB,SAAS,EAAE,GAAEC,IAAE;AAAC,SAAM,iBAAiB,EAAE,KAAK,SAAS,EAAE,EAAE,cAAc,EAAE,aAAa,KAAKA,EAAC;AAAE;AAAC,SAAS,EAAE,GAAEC,IAAE;AAAC,MAAGA,GAAE,SAAO,EAAE,OAAM,IAAIF,GAAE,wCAAuC,EAAE,GAAE,oCAAoC,CAAC,GAAG,CAAC;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,MAAG,MAAM,EAAE,KAAK,GAAE,EAAE,CAAC,EAAE,OAAM,IAAIA,GAAE,sBAAqB,EAAE,GAAE,4DAA4D,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEE,IAAE;AAAC,MAAIC,MAAG,EAAE,YAAU,CAAC,GAAG,OAAQ,CAAC,EAAC,MAAKC,GAAC,MAAI,YAAUA,EAAE,EAAE,IAAK,CAAC,EAAC,MAAKA,IAAE,SAAQF,IAAE,SAAQC,GAAC,MAAI,IAAIH,GAAEI,IAAEF,IAAEC,EAAC,CAAE;AAAE,OAAGD,MAAA,gBAAAA,GAAG,uBAAoBC,KAAEA,GAAE,OAAQ,CAAC,EAAC,MAAKC,GAAC,MAAI,wBAAsBA,MAAG,yBAAuBA,MAAG,+BAA6BA,MAAG,2BAAyBA,MAAG,sBAAoBA,EAAE,IAAGD,GAAE,SAAO,EAAE,OAAM,IAAIH,GAAE,sBAAqB,0HAAyH,EAAC,QAAOG,GAAC,CAAC;AAAC;AAAC,eAAe,EAAE,GAAEF,IAAEC,IAAE;AAAC,kBAAe,KAAG,cAAY,OAAO,EAAE,cAAY,MAAM,EAAE,WAAW;AAAE,QAAMC,KAAE,EAAE,MAAM,CAAC,GAAEF,EAAC;AAAE,SAAO,EAAEA,IAAEC,EAAC,GAAEC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,OAAMF,IAAE,WAAUC,GAAC,IAAE;AAAE,SAAOD,GAAE,UAAQ,EAAC,QAAO,CAAC,GAAE,QAAO,CAACC,EAAC,EAAC,IAAE,EAAC,QAAO,CAACA,EAAC,GAAE,QAAO,CAAC,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,IAAE,GAAE,EAAE,KAAK,GAAE,EAAE,iBAAe,EAAE,eAAa,EAAE,aAAa,OAAQ,CAACE,IAAEH,IAAEC,OAAIA,GAAE,QAAQE,EAAC,MAAIH,EAAE;AAAE;AAAC,SAASI,GAAE,GAAE;AAAC,QAAMH,KAAE,EAAE;AAAW,MAAG,CAACA,GAAE,OAAM,EAAE,MAAM,kDAAkD,GAAE,IAAIF,GAAE,qCAAoC,EAAE,GAAE,4CAA4C,CAAC;AAAE,MAAG,CAACE,GAAE,OAAO,OAAM,IAAIF,GAAE,wCAAuC,EAAE,GAAE,yEAAyE,CAAC;AAAE,IAAE,GAAEE,EAAC;AAAC;AAAC,eAAe,EAAE,GAAED,IAAE;AAAC,SAAM,YAAY,KAAK,EAAE,OAAK,EAAE,IAAE,EAAEA,GAAE,CAAC,CAAC,IAAE,EAAE,GAAEA,EAAC;AAAC;AAAC,eAAe,EAAE,GAAEA,IAAE;AAAC,QAAK,EAAC,OAAM,EAAC,KAAIC,IAAE,kBAAiBC,IAAE,QAAOG,GAAC,EAAC,IAAEL,GAAE,CAAC;AAAE,MAAID,KAAE,MAAM,EAAE,UAAU,MAAM;AAAE,EAAAA,MAAG,QAAMA,GAAE,UAAQ,QAAMA,GAAE,WAASA,KAAE,MAAMO,GAAEP,IAAE,EAAC,KAAIE,MAAG,IAAG,kBAAiBC,IAAE,QAAOG,GAAC,GAAEL,GAAE,IAAK,CAAAG,OAAGA,GAAE,MAAM,OAAQ,CAAC;AAAG,aAAU,KAAKH,GAAE,GAAE,EAAE,OAAM,EAAE,WAAUD,EAAC;AAAE,SAAOA;AAAC;AAAC,eAAeO,GAAE,GAAEN,IAAEC,IAAE;AAAC,MAAIC,IAAEG;AAAE,QAAI,IAAE,CAAC,KAAIH,KAAE,GAAG,WAASA,GAAE,SAAO,CAAC,KAAIG,KAAE,GAAG,WAASA,GAAE,SAAO,CAAC;AAAG,QAAK,EAAC,KAAIN,IAAE,kBAAiB,GAAE,QAAOQ,GAAC,IAAEP,IAAE,EAAC,aAAY,GAAE,YAAWQ,GAAC,IAAE,MAAMP,GAAEF,IAAE,EAAC,kBAAiB,GAAE,QAAOQ,GAAC,CAAC,GAAEE,KAAE,EAAE,EAAE,QAAO,EAAE,QAAOR,EAAC,GAAES,KAAE,EAAE,EAAE,QAAO,EAAE,QAAOT,EAAC;AAAE,IAAE,SAAOQ,GAAE,eAAc,EAAE,SAAOC,GAAE;AAAc,QAAMC,KAAE,CAAC,GAAGF,GAAE,OAAM,GAAGC,GAAE,KAAK,GAAE,IAAEF,KAAE,CAAC,GAAGA,GAAE,QAAO,GAAGA,GAAE,MAAM,IAAE,CAAC;AAAE,SAAO,MAAM,EAAE,GAAEG,IAAEZ,IAAE,CAAC,GAAE;AAAC;AAAC,SAAS,EAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMG,KAAE,EAAEL,IAAEC,IAAG,CAAC,GAAED,OAAI,EAAE,OAAKA,GAAE,EAAG;AAAE,EAAAA,KAAEA,GAAE,OAAQ,OAAG,CAACK,GAAE,QAAQ,KAAM,CAAAL,OAAGA,GAAE,OAAK,EAAE,EAAG,CAAE;AAAE,QAAMD,KAAEM,GAAE,MAAM,IAAK,CAAC,EAAC,IAAG,EAAC,OAAK,EAAC,IAAG,EAAC,EAAG;AAAE,SAAON,GAAE,QAAS,CAAC,EAAC,IAAG,EAAC,MAAI;AAAC,IAAAC,GAAE,KAAK,EAAC,IAAG,EAAC,CAAC;AAAA,EAAC,CAAE,GAAE,EAAC,eAAcA,IAAE,OAAMD,GAAE,OAAQ,CAAC,EAAC,IAAG,EAAC,MAAI,CAACG,GAAE,SAAS,CAAC,CAAE,EAAC;AAAC;AAAC,eAAe,EAAE,GAAEF,IAAEC,IAAEI,IAAE;AAAC,QAAMN,KAAEC,GAAE,IAAK,CAAC,EAAC,IAAGG,GAAC,MAAI,IAAI,GAAE,EAAC,KAAIF,IAAE,SAAQE,IAAE,YAAWE,GAAE,KAAM,CAAC,EAAC,IAAGL,GAAC,MAAIA,OAAIG,EAAE,EAAC,CAAC,CAAE;AAAE,QAAM,EAAEJ,GAAE,IAAK,CAAAI,OAAGA,GAAE,KAAK,CAAE,CAAC,GAAEJ,GAAE,QAAS,CAAAC,OAAG;AAAC,UAAK,EAAC,SAAQC,IAAE,QAAOI,IAAE,sBAAqBN,GAAC,IAAEC;AAAE,QAAG,CAACK,MAAG,EAAEN,EAAC,EAAE;AAAO,MAAEC,IAAE,EAAC,IAAGC,IAAE,WAAUF,GAAE,OAAO,EAAC,GAAE,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAE,GAAEC,IAAEC,IAAE;AAAC,IAAE,UAAQ,EAAEA,GAAE,QAAOD,EAAC,IAAE,EAAEC,GAAE,QAAOD,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAG,CAAC,EAAE;AAAO,QAAMC,KAAE,EAAE,UAAW,CAAC,EAAC,IAAGE,GAAC,MAAIA,OAAIH,GAAE,EAAG;AAAE,SAAKC,KAAE,EAAE,KAAKD,EAAC,IAAE,EAAEC,EAAC,IAAED;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,YAAWA,GAAC,IAAE;AAAE,SAAO,EAAE,CAAC,KAAG,CAAC,EAAE,qBAAmB,CAAC,EAACA,MAAA,gBAAAA,GAAG,WAAQA,GAAE,SAAO;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,MAAG,EAAC,uBAAG,QAAO,OAAM,IAAID,GAAE,kDAAiD,0DAA0D;AAAE,QAAM,QAAQ,IAAI,EAAE,IAAK,CAAAI,OAAGA,GAAE,KAAK,CAAE,CAAC;AAAE,aAAUE,MAAK,EAAE,KAAG,CAAC,EAAEA,EAAC,EAAE,OAAM,IAAIN,GAAE,kDAAiD,gHAAgH,EAAEM,IAAE,kBAAkB,CAAC,IAAG,EAAC,OAAMA,GAAC,CAAC;AAAE,QAAMJ,KAAE,EAAE,IAAK,CAAAE,OAAGA,GAAE,WAAW,EAAG;AAAE,MAAG,IAAI,IAAIF,EAAC,EAAE,OAAK,EAAE,OAAM,IAAIF,GAAE,kDAAiD,6EAA6E;AAAE,QAAMG,KAAE,EAAE,IAAK,CAAAC,OAAGA,GAAE,OAAQ;AAAE,MAAG,IAAI,IAAID,EAAC,EAAE,SAAOA,GAAE,OAAO,OAAM,IAAIH,GAAE,kDAAiD,6FAA6F;AAAC;AAAC,SAAS,EAAE,GAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,MAAIG,KAAEC,GAAE,KAAKN,EAAC;AAAE,SAAOK,GAAE,OAAKA,KAAEA,GAAE,MAAM,GAAEA,GAAE,KAAG,QAAOJ,KAAEI,IAAG,SAAOJ,GAAE,OAAK,KAAIC,KAAEG,IAAG,WAASH,GAAE,SAAO,EAAE,WAAW,IAAG,EAAE,GAAEG,EAAC,GAAEA;AAAC;AAAC,eAAe,EAAE,GAAEL,IAAE;AAAC,QAAK,EAAC,KAAIC,IAAE,SAAQC,IAAE,OAAMH,IAAE,YAAW,GAAE,SAAQQ,GAAC,IAAE,GAAE,IAAE,EAAEN,EAAC,GAAE,IAAE,EAAE,CAAC,KAAG,oBAAkB,EAAE;AAAW,EAAAD,GAAE,MAAI,IAAEC,KAAE,GAAGA,EAAC,IAAIC,EAAC,IAAGF,GAAE,UAAQA,GAAE,QAAMD,KAAGC,GAAE,SAAO,MAAK,CAACO,MAAG,EAAE,CAAC,MAAIP,GAAE,SAAO,MAAM,EAAE,CAAC,IAAG,EAAEA,IAAE,EAAE,QAAQ,GAAE,EAAEA,IAAE,EAAE,WAAW,GAAE,EAAEA,IAAE,EAAE,YAAY,GAAEO,MAAG,EAAEP,IAAE,EAAE,KAAK,GAAE,EAAEA,EAAC;AAAC;AAAC,eAAe,EAAE,GAAEA,IAAEC,IAAE;AAJpsK;AAIqsK,QAAMC,KAAE,EAAE;AAAO,SAAMA,MAAA,gBAAAA,GAAG,WAAU,QAAM,KAAAA,MAAA,gBAAAA,GAAG,SAAH,mBAAS,QAAQ,EAAC,MAAK,GAAE,MAAKF,IAAE,QAAOC,MAAA,gBAAAA,GAAG,OAAM;AAAG;AAAC,IAAM,IAAE,EAAE,CAAC;AAAE,eAAe,EAAE,GAAED,IAAE;AAAC,QAAM,EAAE,CAAC,GAAEI,GAAE,CAAC;AAAE,QAAMH,KAAE,EAAE,YAAWC,KAAE,EAAED,EAAC,GAAEI,KAAE,MAAM,EAAE,GAAEH,IAAEF,EAAC,GAAED,KAAE,MAAM,EAAEE,IAAE,CAAC,EAAC,OAAM,GAAE,WAAUI,GAAC,CAAC,CAAC;AAAE,SAAO,EAAEJ,EAAC,GAAE,MAAMA,GAAE,OAAO,EAAC,MAAKF,GAAC,CAAC,GAAEQ,GAAEL,EAAC,GAAED;AAAC;AAAC,IAAM,IAAE,EAAG,OAAM,GAAED,OAAI;AAAC,QAAM,EAAE,CAAC;AAAE,QAAMC,KAAE,EAAE,CAAC,EAAE,YAAWC,KAAE,EAAED,EAAC,GAAEI,KAAE,MAAM,QAAQ,IAAI,EAAE,IAAK,CAAAF,OAAG,EAAEA,IAAED,IAAEF,EAAC,CAAE,CAAC,GAAED,KAAE,MAAM,EAAEE,IAAE,EAAE,IAAK,CAACE,IAAEH,QAAK,EAAC,OAAMG,IAAE,WAAUE,GAAEL,EAAC,EAAC,EAAG,CAAC;AAAE,SAAO,EAAEC,EAAC,GAAE,MAAMA,GAAE,OAAO,EAAC,MAAKF,GAAC,CAAC,GAAE,MAAM,QAAQ,IAAI,EAAE,MAAM,CAAC,EAAE,IAAK,CAAAI,OAAGA,GAAE,WAAW,OAAO,CAAE,CAAC,GAAEI,GAAEL,EAAC,GAAED,GAAE,MAAM;AAAC,CAAE;AAAvR,IAAyR,IAAE,EAAE,CAAC;AAAE,eAAe,EAAE,GAAED,IAAEC,IAAE;AAAC,QAAM,EAAE,CAAC;AAAE,QAAMC,KAAE,EAAE,GAAEF,EAAC,GAAEK,KAAE,EAAEH,EAAC,GAAEH,KAAE,EAAE,EAAC,OAAM,GAAE,WAAU,MAAM,EAAE,GAAEM,IAAEJ,EAAC,EAAC,CAAC;AAAE,SAAO,MAAM,EAAE,GAAEC,EAAC,GAAE,MAAM,EAAEA,IAAEH,IAAEE,EAAC,GAAE,EAAE,aAAWC,IAAEK,GAAEF,EAAC,GAAEH;AAAC;", "names": ["s", "t", "r", "a", "e", "E", "o", "x", "i", "u", "c", "y", "d"]}