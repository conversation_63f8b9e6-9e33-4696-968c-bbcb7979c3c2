{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/data/BoundsStore.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../core/has.js\";import{PooledRBush as i}from\"../../../core/libs/rbush/PooledRBush.js\";import{empty as s}from\"../../../geometry/support/aaBoundingRect.js\";const d=5e4,n={minX:0,minY:0,maxX:0,maxY:0};function t(i){n.minX=i[0],n.minY=i[1],n.maxX=i[2],n.maxY=i[3]}function e(i,s,d){t(s),i.search(n,d)}class o{constructor(){this._indexInvalid=!1,this._boundsToLoad=[],this._boundsById=new Map,this._idByBounds=new Map,this._index=new i(9,has(\"esri-csp-restrictions\")?i=>({minX:i[0],minY:i[1],maxX:i[2],maxY:i[3]}):[\"[0]\",\"[1]\",\"[2]\",\"[3]\"]),this._loadIndex=()=>{if(this._indexInvalid){const i=new Array(this._idByBounds.size);let s=0;this._idByBounds.forEach(((d,n)=>{i[s++]=n})),this._indexInvalid=!1,this._index.clear(),this._index.load(i)}else this._boundsToLoad.length&&(this._index.load(Array.from(new Set(this._boundsToLoad.filter((i=>this._idByBounds.has(i)))))),this._boundsToLoad.length=0)}}get fullBounds(){if(!this._boundsById.size)return null;const i=s();for(const s of this._boundsById.values())s&&(i[0]=Math.min(s[0],i[0]),i[1]=Math.min(s[1],i[1]),i[2]=Math.max(s[2],i[2]),i[3]=Math.max(s[3],i[3]));return i}get valid(){return!this._indexInvalid}clear(){this._indexInvalid=!1,this._boundsToLoad.length=0,this._boundsById.clear(),this._idByBounds.clear(),this._index.clear()}delete(i){const s=this._boundsById.get(i);this._boundsById.delete(i),s&&(this._idByBounds.delete(s),this._indexInvalid||this._index.remove(s))}forEachInBounds(i,s){this._loadIndex(),e(this._index,i,(i=>s(this._idByBounds.get(i))))}get(i){return this._boundsById.get(i)}has(i){return this._boundsById.has(i)}invalidateIndex(){this._indexInvalid||(this._indexInvalid=!0,this._boundsToLoad.length=0)}set(i,s){if(!this._indexInvalid){const s=this._boundsById.get(i);s&&(this._index.remove(s),this._idByBounds.delete(s))}this._boundsById.set(i,s),s&&(this._idByBounds.set(s,i),this._indexInvalid||(this._boundsToLoad.push(s),this._boundsToLoad.length>d&&this._loadIndex()))}}export{o as BoundsStore};\n"], "mappings": ";;;;;;;;;;;AAIgL,IAAM,IAAE;AAAR,IAAY,IAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC;AAAE,SAAS,EAAE,GAAE;AAAC,IAAE,OAAK,EAAE,CAAC,GAAE,EAAE,OAAK,EAAE,CAAC,GAAE,EAAE,OAAK,EAAE,CAAC,GAAE,EAAE,OAAK,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE;AAAC,IAAE,CAAC,GAAE,EAAE,OAAO,GAAEA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,gBAAc,OAAG,KAAK,gBAAc,CAAC,GAAE,KAAK,cAAY,oBAAI,OAAI,KAAK,cAAY,oBAAI,OAAI,KAAK,SAAO,IAAI,EAAE,GAAE,IAAI,uBAAuB,IAAE,QAAI,EAAC,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,CAAC,GAAE,MAAK,EAAE,CAAC,EAAC,KAAG,CAAC,OAAM,OAAM,OAAM,KAAK,CAAC,GAAE,KAAK,aAAW,MAAI;AAAC,UAAG,KAAK,eAAc;AAAC,cAAM,IAAE,IAAI,MAAM,KAAK,YAAY,IAAI;AAAE,YAAI,IAAE;AAAE,aAAK,YAAY,QAAS,CAACA,IAAEC,OAAI;AAAC,YAAE,GAAG,IAAEA;AAAA,QAAC,CAAE,GAAE,KAAK,gBAAc,OAAG,KAAK,OAAO,MAAM,GAAE,KAAK,OAAO,KAAK,CAAC;AAAA,MAAC,MAAM,MAAK,cAAc,WAAS,KAAK,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK,cAAc,OAAQ,OAAG,KAAK,YAAY,IAAI,CAAC,CAAE,CAAC,CAAC,CAAC,GAAE,KAAK,cAAc,SAAO;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,QAAG,CAAC,KAAK,YAAY,KAAK,QAAO;AAAK,UAAM,IAAE,EAAE;AAAE,eAAU,KAAK,KAAK,YAAY,OAAO,EAAE,OAAI,EAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAG,WAAO;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAM,CAAC,KAAK;AAAA,EAAa;AAAA,EAAC,QAAO;AAAC,SAAK,gBAAc,OAAG,KAAK,cAAc,SAAO,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,OAAO,MAAM;AAAA,EAAC;AAAA,EAAC,OAAO,GAAE;AAAC,UAAM,IAAE,KAAK,YAAY,IAAI,CAAC;AAAE,SAAK,YAAY,OAAO,CAAC,GAAE,MAAI,KAAK,YAAY,OAAO,CAAC,GAAE,KAAK,iBAAe,KAAK,OAAO,OAAO,CAAC;AAAA,EAAE;AAAA,EAAC,gBAAgB,GAAE,GAAE;AAAC,SAAK,WAAW,GAAE,EAAE,KAAK,QAAO,GAAG,CAAAC,OAAG,EAAE,KAAK,YAAY,IAAIA,EAAC,CAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,GAAE;AAAC,WAAO,KAAK,YAAY,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,GAAE;AAAC,WAAO,KAAK,YAAY,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,SAAK,kBAAgB,KAAK,gBAAc,MAAG,KAAK,cAAc,SAAO;AAAA,EAAE;AAAA,EAAC,IAAI,GAAE,GAAE;AAAC,QAAG,CAAC,KAAK,eAAc;AAAC,YAAMC,KAAE,KAAK,YAAY,IAAI,CAAC;AAAE,MAAAA,OAAI,KAAK,OAAO,OAAOA,EAAC,GAAE,KAAK,YAAY,OAAOA,EAAC;AAAA,IAAE;AAAC,SAAK,YAAY,IAAI,GAAE,CAAC,GAAE,MAAI,KAAK,YAAY,IAAI,GAAE,CAAC,GAAE,KAAK,kBAAgB,KAAK,cAAc,KAAK,CAAC,GAAE,KAAK,cAAc,SAAO,KAAG,KAAK,WAAW;AAAA,EAAG;AAAC;", "names": ["d", "n", "i", "s"]}