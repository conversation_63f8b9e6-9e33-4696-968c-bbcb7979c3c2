import {
  E as E3,
  f
} from "./chunk-TOYJMVHA.js";
import {
  W,
  _
} from "./chunk-WKBMFG6J.js";
import {
  n as n5,
  x
} from "./chunk-6G2NLXT7.js";
import {
  u as u2
} from "./chunk-3DGZE3NI.js";
import {
  h as h3,
  p
} from "./chunk-YKLDBJ7V.js";
import {
  a as a2
} from "./chunk-LHO3WKNH.js";
import {
  n as n4
} from "./chunk-RFTQI4ZD.js";
import {
  E as E2,
  a as a3
} from "./chunk-FTRLEBHJ.js";
import {
  D,
  E,
  G,
  L,
  M,
  P,
  V,
  Y
} from "./chunk-4M3AMTD4.js";
import {
  r as r4
} from "./chunk-SROTSYJS.js";
import {
  n as n3
} from "./chunk-FOE4ICAJ.js";
import {
  a,
  h as h2
} from "./chunk-EIGTETCG.js";
import {
  A,
  g,
  j,
  o as o2,
  r as r3,
  u
} from "./chunk-MQAXMQFG.js";
import {
  n as n2,
  r as r2,
  t as t2
} from "./chunk-36FLFRUE.js";
import {
  l,
  n
} from "./chunk-C5VMWMBD.js";
import {
  h,
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderTechnique/ReloadableShaderModule.js
var t3 = class {
  constructor(t5, o5) {
    this._module = t5, this._loadModule = o5;
  }
  get() {
    return this._module;
  }
  async reload() {
    return this._module = await this._loadModule(), this._module;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderTechnique/ShaderTechnique.js
var e = class {
  constructor(i2, t5, e4) {
    this.release = e4, this.initializeConfiguration(i2, t5), this._configuration = t5.snapshot(), this._program = this.initializeProgram(i2), this._pipeline = this.initializePipeline(i2.rctx.capabilities);
  }
  destroy() {
    this._program = h(this._program), this._pipeline = this._configuration = null;
  }
  reload(t5) {
    h(this._program), this._program = this.initializeProgram(t5), this._pipeline = this.initializePipeline(t5.rctx.capabilities);
  }
  get program() {
    return this._program;
  }
  get compiled() {
    return this.program.compiled;
  }
  get key() {
    return this._configuration.key;
  }
  get configuration() {
    return this._configuration;
  }
  bindPipelineState(i2, t5 = null, e4) {
    i2.setPipelineState(this.getPipelineState(t5, e4));
  }
  ensureAttributeLocations(i2) {
    this.program.assertCompatibleVertexAttributeLocations(i2);
  }
  get primitiveType() {
    return E.TRIANGLES;
  }
  getPipelineState(i2, t5) {
    return this._pipeline;
  }
  initializeConfiguration(i2, t5) {
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/Program.js
var o3 = class {
  constructor(t5, e4, o5) {
    this._context = t5, this._locations = o5, this._textures = /* @__PURE__ */ new Map(), this._freeTextureUnits = new l({ deallocator: null }), this._glProgram = t5.programCache.acquire(e4.generate("vertex"), e4.generate("fragment"), o5), this._glProgram.stop = () => {
      throw new Error("Wrapped _glProgram used directly");
    }, this.bindPass = e4.generateBind(a2.Pass, this), this.bindDraw = e4.generateBind(a2.Draw, this), this._fragmentUniforms = a3() ? e4.fragmentUniforms : null;
  }
  dispose() {
    this._glProgram.dispose();
  }
  get glName() {
    return this._glProgram.glName;
  }
  get compiled() {
    return this._glProgram.compiled;
  }
  setUniform1b(t5, e4) {
    this._glProgram.setUniform1i(t5, e4 ? 1 : 0);
  }
  setUniform1i(t5, e4) {
    this._glProgram.setUniform1i(t5, e4);
  }
  setUniform1f(t5, e4) {
    this._glProgram.setUniform1f(t5, e4);
  }
  setUniform2fv(t5, e4) {
    this._glProgram.setUniform2fv(t5, e4);
  }
  setUniform3fv(t5, e4) {
    this._glProgram.setUniform3fv(t5, e4);
  }
  setUniform4fv(t5, e4) {
    this._glProgram.setUniform4fv(t5, e4);
  }
  setUniformMatrix3fv(t5, e4) {
    this._glProgram.setUniformMatrix3fv(t5, e4);
  }
  setUniformMatrix4fv(t5, e4) {
    this._glProgram.setUniformMatrix4fv(t5, e4);
  }
  setUniform1fv(t5, e4) {
    this._glProgram.setUniform1fv(t5, e4);
  }
  setUniform1iv(t5, e4) {
    this._glProgram.setUniform1iv(t5, e4);
  }
  setUniform2iv(t5, e4) {
    this._glProgram.setUniform3iv(t5, e4);
  }
  setUniform3iv(t5, e4) {
    this._glProgram.setUniform3iv(t5, e4);
  }
  setUniform4iv(t5, e4) {
    this._glProgram.setUniform4iv(t5, e4);
  }
  assertCompatibleVertexAttributeLocations(t5) {
    t5.locations !== this._locations && console.error("VertexAttributeLocations are incompatible");
  }
  stop() {
    this._textures.clear(), this._freeTextureUnits.clear();
  }
  bindTexture(e4, r7) {
    if (t(r7) || null == r7.glName) {
      const t5 = this._textures.get(e4);
      return t5 && (this._context.bindTexture(null, t5.unit), this._freeTextureUnit(t5), this._textures.delete(e4)), null;
    }
    let i2 = this._textures.get(e4);
    return null == i2 ? (i2 = this._allocTextureUnit(r7), this._textures.set(e4, i2)) : i2.texture = r7, this._context.useProgram(this), this.setUniform1i(e4, i2.unit), this._context.bindTexture(r7, i2.unit), i2.unit;
  }
  rebindTextures() {
    this._context.useProgram(this), this._textures.forEach((t5, e4) => {
      this._context.bindTexture(t5.texture, t5.unit), this.setUniform1i(e4, t5.unit);
    }), r(this._fragmentUniforms) && this._fragmentUniforms.forEach((t5) => {
      "sampler2D" !== t5.type && "samplerCube" !== t5.type || this._textures.has(t5.name) || console.error(`Texture sampler ${t5.name} has no bound texture`);
    });
  }
  _allocTextureUnit(t5) {
    return { texture: t5, unit: 0 === this._freeTextureUnits.length ? this._textures.size : this._freeTextureUnits.pop() };
  }
  _freeTextureUnit(t5) {
    this._freeTextureUnits.push(t5.unit);
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/SSAOBlurTechnique.js
var l2 = class _l extends e {
  initializeProgram(r7) {
    return new o3(r7.rctx, _l.shader.get().build(), E3);
  }
  initializePipeline() {
    return W({ colorWrite: _ });
  }
};
l2.shader = new t3(u2, () => import("./SSAOBlur.glsl-XWEFHGTK.js"));

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/SSAONoiseData.js
var e2 = "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";

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/SSAOTechnique.js
var l3 = class _l extends e {
  initializeProgram(e4) {
    return new o3(e4.rctx, _l.shader.get().build(), E3);
  }
  initializePipeline() {
    return W({ colorWrite: _ });
  }
};
l3.shader = new t3(h3, () => import("./SSAO.glsl-MAWOSHMF.js"));

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/SSAOParameters.js
var r5 = class extends n4 {
  constructor() {
    super(...arguments), this.projScale = 1;
  }
};
var t4 = class extends r5 {
  constructor() {
    super(...arguments), this.intensity = 1;
  }
};
var c = class extends n4 {
};
var o4 = class extends c {
  constructor() {
    super(...arguments), this.blurSize = n3();
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/SSAOHelper.js
var q = 2;
var P2 = class {
  constructor(e4, s, t5, r7) {
    this._view = e4, this._techniqueRepository = s, this._rctx = t5, this._requestRender = r7, this._quadVAO = null, this._passParameters = new t4(), this._drawParameters = new o4();
  }
  dispose() {
    this.enabled = false, this._quadVAO = h(this._quadVAO);
  }
  disposeOffscreenBuffers() {
    o(this._ssaoFBO, (e4) => e4.resize(0, 0)), o(this._blur0FBO, (e4) => e4.resize(0, 0)), o(this._blur1FBO, (e4) => e4.resize(0, 0));
  }
  set enabled(e4) {
    e4 ? this._enable() : this._disable();
  }
  get enabled() {
    return r(this._enableTime);
  }
  get active() {
    return this.enabled && this._ssaoTechnique.compiled && this._blurTechnique.compiled;
  }
  get colorTexture() {
    return r(this._blur1FBO) ? this._blur1FBO.colorTexture : null;
  }
  render(e4, s, t5, i2) {
    if (t(this._enableTime) || t(t5) || t(i2) || t(this._ssaoFBO) || t(this._blur0FBO) || t(this._blur1FBO)) return;
    if (!this.active) return this._enableTime = s, void this._requestRender();
    0 === this._enableTime && (this._enableTime = s);
    const _3 = this._rctx, o5 = e4.camera, u4 = this._view.qualitySettings.fadeDuration, l7 = u4 > 0 ? Math.min(u4, s - this._enableTime) / u4 : 1;
    this._passParameters.normalTexture = i2, this._passParameters.depthTexture = t5, this._passParameters.projScale = 1 / o5.computeRenderPixelSizeAtDist(1), this._passParameters.intensity = 4 * A2 / p(o5) ** 6 * l7;
    const m3 = o5.fullViewport, c3 = m3[2], d2 = m3[3], p4 = c3 / q, T = d2 / q;
    this._ssaoFBO.resize(c3, d2), this._blur0FBO.resize(p4, T), this._blur1FBO.resize(p4, T), t(this._quadVAO) && (this._quadVAO = f(this._rctx)), _3.bindFramebuffer(this._ssaoFBO), _3.setViewport(0, 0, c3, d2);
    _3.bindTechnique(this._ssaoTechnique, this._passParameters, e4).bindDraw(this._drawParameters, e4, this._passParameters), _3.bindVAO(this._quadVAO);
    const O = n5(this._quadVAO, "geometry");
    _3.drawArrays(E.TRIANGLE_STRIP, 0, O);
    const B = _3.bindTechnique(this._blurTechnique, this._passParameters, e4);
    _3.setViewport(0, 0, p4, T), _3.bindFramebuffer(this._blur0FBO), this._drawParameters.colorTexture = this._ssaoFBO.colorTexture, r4(this._drawParameters.blurSize, 0, q / d2), B.bindDraw(this._drawParameters, e4, this._passParameters), _3.setViewport(0, 0, p4, T), _3.drawArrays(E.TRIANGLE_STRIP, 0, O), _3.bindFramebuffer(this._blur1FBO), this._drawParameters.colorTexture = this._blur0FBO.colorTexture, r4(this._drawParameters.blurSize, q / c3, 0), B.bindDraw(this._drawParameters, e4, this._passParameters), _3.drawArrays(E.TRIANGLE_STRIP, 0, O), _3.setViewport(m3[0], m3[1], m3[2], m3[3]), l7 < 1 && this._requestRender();
  }
  _enable() {
    if (r(this._enableTime)) return;
    const e4 = { target: M.TEXTURE_2D, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, samplingMode: L.LINEAR, wrapMode: D.CLAMP_TO_EDGE, width: 0, height: 0 }, s = { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE };
    this._ssaoFBO = new x(this._rctx, s, e4), this._blur0FBO = new x(this._rctx, s, e4), this._blur1FBO = new x(this._rctx, s, e4);
    const a4 = Uint8Array.from(atob(e2), (e5) => e5.charCodeAt(0));
    this._passParameters.noiseTexture = new E2(this._rctx, { target: M.TEXTURE_2D, pixelFormat: P.RGB, dataType: G.UNSIGNED_BYTE, hasMipmap: true, width: 32, height: 32 }, a4), t(this._ssaoTechnique) && (this._ssaoTechnique = this._techniqueRepository.acquire(l3)), t(this._blurTechnique) && (this._blurTechnique = this._techniqueRepository.acquire(l2)), this._enableTime = n(0), this._requestRender();
  }
  _disable() {
    this._enableTime = null, this._passParameters.noiseTexture = h(this._passParameters.noiseTexture), this._blur1FBO = h(this._blur1FBO), this._blur0FBO = h(this._blur0FBO), this._ssaoFBO = h(this._ssaoFBO);
  }
  get gpuMemoryUsage() {
    return (r(this._blur0FBO) ? this._blur0FBO.gpuMemoryUsage : 0) + (r(this._blur1FBO) ? this._blur1FBO.gpuMemoryUsage : 0) + (r(this._ssaoFBO) ? this._ssaoFBO.gpuMemoryUsage : 0);
  }
  get test() {
    return { ssao: this._ssaoFBO, blur: this._blur1FBO };
  }
};
var A2 = 0.5;

// node_modules/@arcgis/core/views/3d/webgl-engine/lighting/Lightsources.js
var i = class {
  constructor(s = n2()) {
    this.intensity = s;
  }
};
var c2 = class {
  constructor(i2 = n2(), c3 = r2(0.57735, 0.57735, 0.57735)) {
    this.intensity = i2, this.direction = c3;
  }
};
var n6 = class {
  constructor(i2 = n2(), c3 = r2(0.57735, 0.57735, 0.57735), n8 = true, r7 = 1, h4 = 1) {
    this.intensity = i2, this.direction = c3, this.castShadows = n8, this.specularStrength = r7, this.environmentStrength = h4;
  }
};
var r6 = class {
  constructor() {
    this.r = [0], this.g = [0], this.b = [0];
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/lib/LongVectorMath.js
function n7(t5, n8, e4) {
  (e4 = e4 || t5).length = t5.length;
  for (let l7 = 0; l7 < t5.length; l7++) e4[l7] = t5[l7] * n8[l7];
  return e4;
}
function e3(t5, n8, e4) {
  (e4 = e4 || t5).length = t5.length;
  for (let l7 = 0; l7 < t5.length; l7++) e4[l7] = t5[l7] * n8;
  return e4;
}
function l4(t5, n8, e4) {
  (e4 = e4 || t5).length = t5.length;
  for (let l7 = 0; l7 < t5.length; l7++) e4[l7] = t5[l7] + n8[l7];
  return e4;
}

// node_modules/@arcgis/core/views/3d/webgl-engine/lighting/SphericalHarmonics.js
function u3(t5) {
  return (t5 + 1) * (t5 + 1);
}
function l5(n8) {
  return a(Math.floor(Math.sqrt(n8) - 1), 0, 2);
}
function m(t5, n8, o5) {
  const r7 = t5[0], e4 = t5[1], s = t5[2], i2 = o5 || [];
  return i2.length = u3(n8), n8 >= 0 && (i2[0] = 0.28209479177), n8 >= 1 && (i2[1] = 0.4886025119 * r7, i2[2] = 0.4886025119 * s, i2[3] = 0.4886025119 * e4), n8 >= 2 && (i2[4] = 1.09254843059 * r7 * e4, i2[5] = 1.09254843059 * e4 * s, i2[6] = 0.31539156525 * (3 * s * s - 1), i2[7] = 1.09254843059 * r7 * s, i2[8] = 0.54627421529 * (r7 * r7 - e4 * e4)), i2;
}
function p2(t5, n8) {
  const o5 = u3(t5), r7 = n8 || { r: [], g: [], b: [] };
  r7.r.length = r7.g.length = r7.b.length = o5;
  for (let e4 = 0; e4 < o5; e4++) r7.r[e4] = r7.g[e4] = r7.b[e4] = 0;
  return r7;
}
function y(t5, o5) {
  const r7 = l5(o5.r.length);
  for (const e4 of t5) j(P3, e4.direction), m(P3, r7, v), n7(v, k), e3(v, e4.intensity[0], I), l4(o5.r, I), e3(v, e4.intensity[1], I), l4(o5.g, I), e3(v, e4.intensity[2], I), l4(o5.b, I);
  return o5;
}
function b(t5, n8) {
  m(P3, 0, v);
  for (const o5 of t5) n8.r[0] += v[0] * k[0] * o5.intensity[0] * 4 * Math.PI, n8.g[0] += v[0] * k[0] * o5.intensity[1] * 4 * Math.PI, n8.b[0] += v[0] * k[0] * o5.intensity[2] * 4 * Math.PI;
  return n8;
}
function M2(t5, n8, e4, s) {
  p2(n8, s), o2(e4.intensity, 0, 0, 0);
  let i2 = false;
  const u4 = S, l7 = d, m3 = j2;
  u4.length = 0, l7.length = 0, m3.length = 0;
  for (const o5 of t5) o5 instanceof n6 && !i2 ? (r3(e4.direction, o5.direction), r3(e4.intensity, o5.intensity), e4.specularStrength = o5.specularStrength, e4.environmentStrength = o5.environmentStrength, e4.castShadows = o5.castShadows, i2 = true) : o5 instanceof n6 || o5 instanceof c2 ? u4.push(o5) : o5 instanceof i ? l7.push(o5) : o5 instanceof r6 && m3.push(o5);
  y(u4, s), b(l7, s);
  for (const o5 of m3) l4(s.r, o5.r), l4(s.g, o5.g), l4(s.b, o5.b);
}
var S = [];
var d = [];
var j2 = [];
var v = [0];
var I = [0];
var P3 = n2();
var k = [3.141593, 2.094395, 2.094395, 2.094395, 0.785398, 0.785398, 0.785398, 0.785398, 0.785398];

// node_modules/@arcgis/core/views/3d/webgl-engine/lighting/SceneLighting.js
var m2 = class {
  constructor() {
    this.color = n2(), this.intensity = 1;
  }
};
var l6 = class {
  constructor() {
    this.direction = n2(), this.ambient = new m2(), this.diffuse = new m2();
  }
};
var _2 = 0.4;
var L2 = class {
  constructor() {
    this._shOrder = 2, this._legacy = new l6(), this.globalFactor = 0.5, this.noonFactor = 0.5, this._sphericalHarmonics = new r6(), this._mainLight = new n6(n2(), r2(1, 0, 0), false);
  }
  get legacy() {
    return this._legacy;
  }
  get sh() {
    return this._sphericalHarmonics;
  }
  get mainLight() {
    return this._mainLight;
  }
  set(i2) {
    M2(i2, this._shOrder, this._mainLight, this._sphericalHarmonics), r3(this._legacy.direction, this._mainLight.direction);
    const r7 = 1 / Math.PI;
    this._legacy.ambient.color[0] = 0.282095 * this._sphericalHarmonics.r[0] * r7, this._legacy.ambient.color[1] = 0.282095 * this._sphericalHarmonics.g[0] * r7, this._legacy.ambient.color[2] = 0.282095 * this._sphericalHarmonics.b[0] * r7, g(this._legacy.diffuse.color, this._mainLight.intensity, r7), r3(p3, this._legacy.diffuse.color), g(p3, p3, _2 * this.globalFactor), u(this._legacy.ambient.color, this._legacy.ambient.color, p3);
  }
  copyFrom(i2) {
    this._sphericalHarmonics.r = Array.from(i2.sh.r), this._sphericalHarmonics.g = Array.from(i2.sh.g), this._sphericalHarmonics.b = Array.from(i2.sh.b), this._mainLight.direction = t2(i2.mainLight.direction), this._mainLight.intensity = t2(i2.mainLight.intensity), this._mainLight.castShadows = i2.mainLight.castShadows, this._mainLight.specularStrength = i2.mainLight.specularStrength, this._mainLight.environmentStrength = i2.mainLight.environmentStrength, this.globalFactor = i2.globalFactor, this.noonFactor = i2.noonFactor;
  }
  lerpLighting(s, h4, n8) {
    if (A(this._mainLight.intensity, s.mainLight.intensity, h4.mainLight.intensity, n8), this._mainLight.environmentStrength = h2(s.mainLight.environmentStrength, h4.mainLight.environmentStrength, n8), this._mainLight.specularStrength = h2(s.mainLight.specularStrength, h4.mainLight.specularStrength, n8), r3(this._mainLight.direction, h4.mainLight.direction), this._mainLight.castShadows = h4.mainLight.castShadows, this.globalFactor = h2(s.globalFactor, h4.globalFactor, n8), this.noonFactor = h2(s.noonFactor, h4.noonFactor, n8), s.sh.r.length === h4.sh.r.length) for (let t5 = 0; t5 < h4.sh.r.length; t5++) this._sphericalHarmonics.r[t5] = h2(s.sh.r[t5], h4.sh.r[t5], n8), this._sphericalHarmonics.g[t5] = h2(s.sh.g[t5], h4.sh.g[t5], n8), this._sphericalHarmonics.b[t5] = h2(s.sh.b[t5], h4.sh.b[t5], n8);
    else for (let i2 = 0; i2 < h4.sh.r.length; i2++) this._sphericalHarmonics.r[i2] = h4.sh.r[i2], this._sphericalHarmonics.g[i2] = h4.sh.g[i2], this._sphericalHarmonics.b[i2] = h4.sh.b[i2];
  }
};
var p3 = n2();

export {
  t3 as t,
  e,
  o3 as o,
  q,
  P2 as P,
  i,
  _2 as _,
  L2 as L
};
//# sourceMappingURL=chunk-QLHJUIIZ.js.map
