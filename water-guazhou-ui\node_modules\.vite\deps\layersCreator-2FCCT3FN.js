import {
  m
} from "./chunk-PKY57QJP.js";
import "./chunk-OWU4AWBA.js";
import "./chunk-JK6A4R7D.js";
import "./chunk-WIKVTG73.js";
import {
  a
} from "./chunk-OVPAFMSR.js";
import {
  t
} from "./chunk-NNKS4NNY.js";
import "./chunk-HTXGAKOK.js";
import "./chunk-55WN4LCX.js";
import "./chunk-P37TUI4J.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import {
  x
} from "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FBVKALLT.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  E
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/portal/support/featureCollectionUtils.js
function e(e2) {
  return t2(e2, "notes");
}
function r(e2) {
  return t2(e2, "markup");
}
function n(e2) {
  return t2(e2, "route");
}
function t2(e2, r2) {
  return !(!e2.layerType || "ArcGISFeatureLayer" !== e2.layerType) && e2.featureCollectionType === r2;
}

// node_modules/@arcgis/core/layers/support/layersCreator.js
async function c(e2, a2, y) {
  if (!a2) return;
  const t3 = [];
  for (const r2 of a2) {
    const e3 = d(r2, y);
    "GroupLayer" === r2.layerType ? t3.push(M(e3, r2, y)) : t3.push(e3);
  }
  const i = await E(t3);
  for (const r2 of i) r2.value && e2.add(r2.value);
}
var l = { ArcGISDimensionLayer: "DimensionLayer", ArcGISFeatureLayer: "FeatureLayer", ArcGISImageServiceLayer: "ImageryLayer", ArcGISMapServiceLayer: "MapImageLayer", PointCloudLayer: "PointCloudLayer", ArcGISSceneServiceLayer: "SceneLayer", IntegratedMeshLayer: "IntegratedMeshLayer", OGCFeatureLayer: "OGCFeatureLayer", BuildingSceneLayer: "BuildingSceneLayer", ArcGISTiledElevationServiceLayer: "ElevationLayer", ArcGISTiledImageServiceLayer: "ImageryTileLayer", ArcGISTiledMapServiceLayer: "TileLayer", GroupLayer: "GroupLayer", GeoJSON: "GeoJSONLayer", WebTiledLayer: "WebTileLayer", CSV: "CSVLayer", VectorTileLayer: "VectorTileLayer", WFS: "WFSLayer", WMS: "WMSLayer", DefaultTileLayer: "TileLayer", KML: "KMLLayer", RasterDataLayer: "UnsupportedLayer", Voxel: "VoxelLayer", LineOfSightLayer: "LineOfSightLayer" };
var s = { ArcGISTiledElevationServiceLayer: "ElevationLayer", DefaultTileLayer: "ElevationLayer", RasterDataElevationLayer: "UnsupportedLayer" };
var p = { ArcGISTiledMapServiceLayer: "TileLayer", ArcGISTiledImageServiceLayer: "ImageryTileLayer", OpenStreetMap: "OpenStreetMapLayer", WebTiledLayer: "WebTileLayer", VectorTileLayer: "VectorTileLayer", ArcGISImageServiceLayer: "UnsupportedLayer", WMS: "UnsupportedLayer", ArcGISMapServiceLayer: "UnsupportedLayer", ArcGISSceneServiceLayer: "SceneLayer", DefaultTileLayer: "TileLayer" };
var S = { ArcGISAnnotationLayer: "UnsupportedLayer", ArcGISDimensionLayer: "UnsupportedLayer", ArcGISFeatureLayer: "FeatureLayer", ArcGISImageServiceLayer: "ImageryLayer", ArcGISImageServiceVectorLayer: "ImageryLayer", ArcGISMapServiceLayer: "MapImageLayer", ArcGISStreamLayer: "StreamLayer", ArcGISTiledImageServiceLayer: "ImageryTileLayer", ArcGISTiledMapServiceLayer: "TileLayer", BingMapsAerial: "BingMapsLayer", BingMapsRoad: "BingMapsLayer", BingMapsHybrid: "BingMapsLayer", CSV: "CSVLayer", DefaultTileLayer: "TileLayer", GeoRSS: "GeoRSSLayer", GeoJSON: "GeoJSONLayer", GroupLayer: "GroupLayer", KML: "KMLLayer", MediaLayer: "MediaLayer", OGCFeatureLayer: "OGCFeatureLayer", OrientedImageryLayer: "OrientedImageryLayer", SubtypeGroupLayer: "SubtypeGroupLayer", VectorTileLayer: "VectorTileLayer", WFS: "WFSLayer", WMS: "WMSLayer", WebTiledLayer: "WebTileLayer" };
var u = { ArcGISFeatureLayer: "FeatureLayer" };
var I = { ArcGISImageServiceLayer: "ImageryLayer", ArcGISImageServiceVectorLayer: "ImageryLayer", ArcGISMapServiceLayer: "MapImageLayer", ArcGISTiledImageServiceLayer: "ImageryTileLayer", ArcGISTiledMapServiceLayer: "TileLayer", OpenStreetMap: "OpenStreetMapLayer", VectorTileLayer: "VectorTileLayer", WebTiledLayer: "WebTileLayer", BingMapsAerial: "BingMapsLayer", BingMapsRoad: "BingMapsLayer", BingMapsHybrid: "BingMapsLayer", WMS: "WMSLayer", DefaultTileLayer: "TileLayer" };
async function d(e2, r2) {
  return m2(await T(e2, r2), e2, r2);
}
async function m2(e2, r2, a2) {
  const y = new e2();
  return y.read(r2, a2.context), "group" === y.type && g(r2) && await A(y, r2, a2.context), await t(y, a2.context), y;
}
async function T(e2, r2) {
  var _a;
  const o = r2.context, c2 = f(o);
  let l2 = e2.layerType || e2.type;
  !l2 && r2 && r2.defaultLayerType && (l2 = r2.defaultLayerType);
  const s2 = c2[l2];
  let p2 = s2 ? a[s2] : a.UnknownLayer;
  if (G(e2)) {
    const r3 = o == null ? void 0 : o.portal;
    if (e2.itemId) {
      const t3 = new x({ id: e2.itemId, portal: r3 });
      await t3.load();
      const i = (await m(t3)).className || "UnknownLayer";
      p2 = a[i];
    }
  } else "ArcGISFeatureLayer" === l2 ? e(e2) || r(e2) ? p2 = a.MapNotesLayer : n(e2) ? p2 = a.RouteLayer : g(e2) && (p2 = a.GroupLayer) : e2.wmtsInfo && e2.wmtsInfo.url && e2.wmtsInfo.layerIdentifier ? p2 = a.WMTSLayer : "WFS" === l2 && "2.0.0" !== ((_a = e2.wfsInfo) == null ? void 0 : _a.version) && (p2 = a.UnsupportedLayer);
  return p2();
}
function g(e2) {
  var _a, _b;
  if ("ArcGISFeatureLayer" !== e2.layerType || G(e2)) return false;
  return (((_b = (_a = e2.featureCollection) == null ? void 0 : _a.layers) == null ? void 0 : _b.length) ?? 0) > 1;
}
function G(e2) {
  return "Feature Collection" === e2.type;
}
function f(e2) {
  let r2;
  if ("web-scene" === e2.origin) switch (e2.layerContainerType) {
    case "basemap":
      r2 = p;
      break;
    case "ground":
      r2 = s;
      break;
    default:
      r2 = l;
  }
  else switch (e2.layerContainerType) {
    case "basemap":
      r2 = I;
      break;
    case "tables":
      r2 = u;
      break;
    default:
      r2 = S;
  }
  return r2;
}
async function M(r2, a2, y) {
  const t3 = new j(), i = c(t3, Array.isArray(a2.layers) ? a2.layers : [], y), L = await r2;
  if (await i, "group" === L.type) return L.layers.addMany(t3), L;
}
async function A(e2, r2, y) {
  var _a;
  const t3 = a.FeatureLayer, i = await t3(), L = r2.featureCollection, n2 = L == null ? void 0 : L.showLegend, o = (_a = L == null ? void 0 : L.layers) == null ? void 0 : _a.map((a2, t4) => {
    var _a2;
    const L2 = new i();
    L2.read(a2, y);
    const o2 = { ...y, ignoreDefaults: true };
    return L2.read({ id: `${e2.id}-sublayer-${t4}`, visibility: ((_a2 = r2.visibleLayers) == null ? void 0 : _a2.includes(t4)) ?? true }, o2), null != n2 && L2.read({ showLegend: n2 }, o2), L2;
  });
  e2.layers.addMany(o ?? []);
}
export {
  c as populateOperationalLayers
};
//# sourceMappingURL=layersCreator-2FCCT3FN.js.map
