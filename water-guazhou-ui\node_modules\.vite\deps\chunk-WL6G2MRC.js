import {
  C
} from "./chunk-Q7K3J54I.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/support/labelingInfo.js
var n = s.getLogger("esri.layers.support.labelingInfo");
var l = /\[([^\[\]]+)\]/gi;
function i(e, r, o) {
  return e ? e.map((e2) => {
    const n2 = new C();
    if (n2.read(e2, o), n2.labelExpression) {
      const e3 = r.fields || r.layerDefinition && r.layerDefinition.fields || this.fields;
      n2.labelExpression = n2.labelExpression.replace(l, (r2, o2) => `[${s3(o2, e3)}]`);
    }
    return n2;
  }) : null;
}
function s3(e, r) {
  if (!r) return e;
  const o = e.toLowerCase();
  for (let t = 0; t < r.length; t++) {
    const e2 = r[t].name;
    if (e2.toLowerCase() === o) return e2;
  }
  return e;
}
var a = { esriGeometryPoint: ["above-right", "above-center", "above-left", "center-center", "center-left", "center-right", "below-center", "below-left", "below-right"], esriGeometryPolygon: ["always-horizontal"], esriGeometryPolyline: ["center-along"], esriGeometryMultipoint: null };
function c(e, o) {
  const t = p(e);
  return t.some((e2) => f(e2, o)) ? [] : t;
}
function f(r, o) {
  const t = r.labelPlacement, l2 = a[o];
  if (!r.symbol) return n.warn("No ILabelClass symbol specified."), true;
  if (!l2) return n.error(new s2("labeling:unsupported-geometry-type", `Unable to create labels for layer, geometry type '${o}' is not supported`)), true;
  if (!l2.includes(t)) {
    const e = l2[0];
    t && n.warn(`Found invalid label placement type ${t} for ${o}. Defaulting to ${e}`), r.labelPlacement = e;
  }
  return false;
}

export {
  i,
  c
};
//# sourceMappingURL=chunk-WL6G2MRC.js.map
