import {
  T as T2
} from "./chunk-3HDXUZDA.js";
import {
  n as n7,
  n2 as n8
} from "./chunk-SMSZBVG5.js";
import {
  e as e6,
  e2 as e7
} from "./chunk-5LZTDVVY.js";
import {
  c as c3
} from "./chunk-EDV64J6E.js";
import {
  e2 as e4
} from "./chunk-3BVSG4LE.js";
import {
  e as e2
} from "./chunk-AZEN5UFW.js";
import {
  e as e3,
  i as i2,
  o as o4
} from "./chunk-VXAO6YJP.js";
import {
  o as o3
} from "./chunk-VYWZHTOQ.js";
import {
  o as o2
} from "./chunk-KEY2Y5WF.js";
import {
  t as t2
} from "./chunk-5SYMUP5B.js";
import {
  Ie,
  de,
  fe,
  ie,
  le,
  me,
  ne,
  oe,
  pe,
  re,
  se,
  ue
} from "./chunk-AOYBG2OC.js";
import {
  s as s6
} from "./chunk-CV76WXPW.js";
import {
  t as t3
} from "./chunk-EVADT7ME.js";
import {
  c as c2
} from "./chunk-MDHXGN24.js";
import {
  T,
  _
} from "./chunk-WAPZ634R.js";
import {
  E
} from "./chunk-FTRLEBHJ.js";
import {
  U as U2,
  V,
  et,
  jt,
  y as y2,
  z
} from "./chunk-RRNRSHX3.js";
import {
  D,
  G,
  L,
  M,
  P,
  R
} from "./chunk-4M3AMTD4.js";
import {
  e as e5
} from "./chunk-2WS4DQ5K.js";
import {
  $,
  Gt,
  Rt,
  St,
  bt,
  nt,
  pt
} from "./chunk-T7HWQQFI.js";
import {
  l
} from "./chunk-DSTI5UIS.js";
import {
  n as n5
} from "./chunk-DUEDINK5.js";
import {
  u as u2
} from "./chunk-THUK4WUF.js";
import {
  d
} from "./chunk-RURSJOSG.js";
import {
  P as P2
} from "./chunk-HURTVQSL.js";
import {
  a as a5,
  r as r3,
  s as s5
} from "./chunk-SROTSYJS.js";
import {
  n as n6
} from "./chunk-FOE4ICAJ.js";
import {
  n as n4
} from "./chunk-TNP2LXZZ.js";
import {
  a as a6
} from "./chunk-Q4VCSCSY.js";
import {
  f as f3
} from "./chunk-QUHG7NMD.js";
import {
  i as i3
} from "./chunk-HM62IZSE.js";
import {
  m
} from "./chunk-3WEGNHPY.js";
import {
  n as n2
} from "./chunk-SGIJIEHB.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  c,
  i,
  u
} from "./chunk-G5KX4JSG.js";
import {
  a as a4,
  f as f2
} from "./chunk-EIGTETCG.js";
import {
  g,
  o,
  s as s4
} from "./chunk-MQAXMQFG.js";
import {
  n as n3,
  r as r2
} from "./chunk-36FLFRUE.js";
import {
  e,
  v as v2
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  n
} from "./chunk-C5VMWMBD.js";
import {
  a as a3,
  b,
  f,
  j,
  v
} from "./chunk-EKX3LLYN.js";
import {
  s as s3
} from "./chunk-4RZONHOY.js";
import {
  s as s2
} from "./chunk-RV4I37UI.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  a,
  h,
  p,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/shaders/sources/shaderRepository.js
var e8 = { background: { "background.frag": "#ifdef PATTERN\nuniform lowp float u_opacity;\nuniform lowp sampler2D u_texture;\nvarying mediump vec4 v_tlbr;\nvarying mediump vec2 v_tileTextureCoord;\n#else\nuniform lowp vec4 u_color;\n#endif\n#ifdef ID\nvarying mediump vec4 v_id;\n#endif\nvoid main() {\n#ifdef PATTERN\nmediump vec2 normalizedTextureCoord = mod(v_tileTextureCoord, 1.0);\nmediump vec2 samplePos = mix(v_tlbr.xy, v_tlbr.zw, normalizedTextureCoord);\nlowp vec4 color = texture2D(u_texture, samplePos);\ngl_FragColor = u_opacity * color;\n#else\ngl_FragColor = u_color;\n#endif\n#ifdef ID\nif (gl_FragColor.a < 1.0 / 255.0) {\ndiscard;\n}\ngl_FragColor = v_id;\n#endif\n}", "background.vert": "precision mediump float;\nattribute vec2 a_pos;\n#ifdef ID\nuniform mediump vec4 u_id;\nvarying mediump vec4 v_id;\n#endif\nuniform highp mat3 u_dvsMat3;\nuniform mediump float u_coord_range;\nuniform mediump float u_depth;\n#ifdef PATTERN\nuniform mediump mat3 u_pattern_matrix;\nvarying mediump vec2 v_tileTextureCoord;\nuniform mediump vec4 u_tlbr;\nuniform mediump vec2 u_mosaicSize;\nvarying mediump vec4 v_tlbr;\n#endif\nvoid main() {\ngl_Position = vec4((u_dvsMat3 * vec3(u_coord_range * a_pos, 1.0)).xy, u_depth, 1.0);\n#ifdef PATTERN\nv_tileTextureCoord = (u_pattern_matrix * vec3(a_pos, 1.0)).xy;\nv_tlbr             = u_tlbr / u_mosaicSize.xyxy;\n#endif\n#ifdef ID\nv_id = u_id / 255.0;\n#endif\n}" }, circle: { "circle.frag": "precision lowp float;\nvarying lowp vec4 v_color;\nvarying lowp vec4 v_stroke_color;\nvarying mediump float v_blur;\nvarying mediump float v_stroke_width;\nvarying mediump float v_radius;\nvarying mediump vec2 v_offset;\n#ifdef ID\nvarying mediump vec4 v_id;\n#endif\nvoid main()\n{\nmediump float dist = length(v_offset);\nmediump float alpha = smoothstep(0.0, -v_blur, dist - 1.0);\nlowp float color_mix_ratio = v_stroke_width < 0.01 ? 0.0 : smoothstep(-v_blur, 0.0, dist - v_radius / (v_radius + v_stroke_width));\ngl_FragColor = alpha * mix(v_color, v_stroke_color, color_mix_ratio);\n#ifdef ID\nif (gl_FragColor.a < 1.0 / 255.0) {\ndiscard;\n}\ngl_FragColor = v_id;\n#endif\n}", "circle.vert": "precision mediump float;\nattribute vec2 a_pos;\n#pragma header\nvarying lowp vec4 v_color;\nvarying lowp vec4 v_stroke_color;\nvarying mediump float v_blur;\nvarying mediump float v_stroke_width;\nvarying mediump float v_radius;\nvarying mediump vec2 v_offset;\n#ifdef ID\nuniform mediump vec4 u_id;\nvarying mediump vec4 v_id;\n#endif\nuniform highp mat3 u_dvsMat3;\nuniform highp mat3 u_displayMat3;\nuniform mediump vec2 u_circleTranslation;\nuniform mediump float u_depth;\nuniform mediump float u_antialiasingWidth;\nvoid main()\n{\n#pragma main\nv_color = color * opacity;\nv_stroke_color = stroke_color * stroke_opacity;\nv_stroke_width = stroke_width;\nv_radius = radius;\nv_blur = max(blur, u_antialiasingWidth / (radius + stroke_width));\nmediump vec2 offset = vec2(mod(a_pos, 2.0) * 2.0 - 1.0);\nv_offset = offset;\n#ifdef ID\nv_id = u_id / 255.0;\n#endif\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos * 0.5, 1.0) + u_displayMat3 * vec3((v_radius + v_stroke_width) * offset + u_circleTranslation, 0.0);\ngl_Position = vec4(pos.xy, u_depth, 1.0);\n}" }, fill: { "fill.frag": "precision lowp float;\n#ifdef PATTERN\nuniform lowp sampler2D u_texture;\nvarying mediump vec2 v_tileTextureCoord;\nvarying mediump vec4 v_tlbr;\n#endif\n#ifdef ID\nvarying mediump vec4 v_id;\n#endif\nvarying lowp vec4 v_color;\nvec4 mixColors(vec4 color1, vec4 color2) {\nfloat compositeAlpha = color2.a + color1.a * (1.0 - color2.a);\nvec3 compositeColor = color2.rgb + color1.rgb * (1.0 - color2.a);\nreturn vec4(compositeColor, compositeAlpha);\n}\nvoid main()\n{\n#ifdef PATTERN\nmediump vec2 normalizedTextureCoord = fract(v_tileTextureCoord);\nmediump vec2 samplePos = mix(v_tlbr.xy, v_tlbr.zw, normalizedTextureCoord);\nlowp vec4 color = texture2D(u_texture, samplePos);\ngl_FragColor = v_color[3] * color;\n#else\ngl_FragColor = v_color;\n#endif\n#ifdef ID\nif (gl_FragColor.a < 1.0 / 255.0) {\ndiscard;\n}\ngl_FragColor = v_id;\n#endif\n}", "fill.vert": "precision mediump float;\nattribute vec2 a_pos;\n#pragma header\nuniform highp mat3 u_dvsMat3;\nuniform highp mat3 u_displayMat3;\nuniform mediump float u_depth;\nuniform mediump vec2 u_fillTranslation;\n#ifdef PATTERN\n#include <util/util.glsl>\nuniform mediump vec2 u_mosaicSize;\nuniform mediump float u_patternFactor;\nvarying mediump vec2 v_tileTextureCoord;\nvarying mediump vec4 v_tlbr;\n#endif\n#ifdef ID\nuniform mediump vec4 u_id;\nvarying mediump vec4 v_id;\n#endif\nvarying lowp vec4 v_color;\nvoid main()\n{\n#pragma main\nv_color = color * opacity;\n#ifdef ID\nv_id = u_id / 255.0;\n#endif\n#ifdef PATTERN\nfloat patternWidth = nextPOT(tlbr.z - tlbr.x);\nfloat patternHeight = nextPOT(tlbr.w - tlbr.y);\nfloat scaleX = 1.0 / (patternWidth * u_patternFactor);\nfloat scaleY = 1.0 / (patternHeight * u_patternFactor);\nmat3 patterMat = mat3(scaleX, 0.0,    0.0,\n0.0,    -scaleY, 0.0,\n0.0,    0.0,    1.0);\nv_tileTextureCoord = (patterMat * vec3(a_pos, 1.0)).xy;\nv_tlbr             = tlbr / u_mosaicSize.xyxy;\n#endif\nvec3 pos = u_dvsMat3 * vec3(a_pos, 1.0) + u_displayMat3 * vec3(u_fillTranslation, 0.0);\ngl_Position = vec4(pos.xy, u_depth, 1.0);\n}" }, icon: { "icon.frag": "precision mediump float;\nuniform lowp sampler2D u_texture;\n#ifdef SDF\nuniform lowp vec4 u_color;\nuniform lowp vec4 u_outlineColor;\n#endif\nvarying mediump vec2 v_tex;\nvarying lowp float v_opacity;\nvarying mediump vec2 v_size;\nvarying lowp vec4 v_color;\n#ifdef SDF\nvarying mediump flaot v_halo_width;\n#endif\n#ifdef ID\nvarying mediump vec4 v_id;\n#endif\n#include <util/encoding.glsl>\nvec4 mixColors(vec4 color1, vec4 color2) {\nfloat compositeAlpha = color2.a + color1.a * (1.0 - color2.a);\nvec3 compositeColor = color2.rgb + color1.rgb * (1.0 - color2.a);\nreturn vec4(compositeColor, compositeAlpha);\n}\nvoid main()\n{\n#ifdef SDF\nlowp vec4 fillPixelColor = v_color;\nfloat d = rgba2float(texture2D(u_texture, v_tex)) - 0.5;\nconst float softEdgeRatio = 0.248062016;\nfloat size = max(v_size.x, v_size.y);\nfloat dist = d * softEdgeRatio * size;\nfillPixelColor *= clamp(0.5 - dist, 0.0, 1.0);\nif (v_halo_width > 0.25) {\nlowp vec4 outlinePixelColor = u_outlineColor;\nconst float outlineLimitRatio = (16.0 / 86.0);\nfloat clampedOutlineSize = softEdgeRatio * min(v_halo_width, outlineLimitRatio * max(v_size.x, v_size.y));\noutlinePixelColor *= clamp(0.5 - (abs(dist) - clampedOutlineSize), 0.0, 1.0);\ngl_FragColor = v_opacity * mixColors(fillPixelColor, outlinePixelColor);\n}\nelse {\ngl_FragColor = v_opacity * fillPixelColor;\n}\n#else\nlowp vec4 texColor = texture2D(u_texture, v_tex);\ngl_FragColor = v_opacity * texColor;\n#endif\n#ifdef ID\nif (gl_FragColor.a < 1.0 / 255.0) {\ndiscard;\n}\ngl_FragColor = v_id;\n#endif\n}", "icon.vert": "attribute vec2 a_pos;\nattribute vec2 a_vertexOffset;\nattribute vec4 a_texAngleRange;\nattribute vec4 a_levelInfo;\nattribute float a_opacityInfo;\n#pragma header\n#ifdef ID\nuniform mediump vec4 u_id;\nvarying mediump vec4 v_id;\n#endif\nvarying lowp vec4 v_color;\n#ifdef SDF\nvarying mediump float v_halo_width;\n#endif\nuniform highp mat3 u_dvsMat3;\nuniform highp mat3 u_displayMat3;\nuniform highp mat3 u_displayViewMat3;\nuniform mediump vec2 u_iconTranslation;\nuniform vec2 u_mosaicSize;\nuniform mediump float u_depth;\nuniform mediump float u_mapRotation;\nuniform mediump float u_level;\nuniform lowp float u_keepUpright;\nuniform mediump float u_fadeDuration;\nvarying mediump vec2 v_tex;\nvarying lowp float v_opacity;\nvarying mediump vec2 v_size;\nconst float C_OFFSET_PRECISION = 1.0 / 8.0;\nconst float C_256_TO_RAD = 3.14159265359 / 128.0;\nconst float C_DEG_TO_RAD = 3.14159265359 / 180.0;\nconst float tileCoordRatio = 1.0 / 8.0;\nuniform highp float u_time;\nvoid main()\n{\n#pragma main\nv_color = color;\nv_opacity = opacity;\n#ifdef SDF\nv_halo_width = halo_width;\n#endif\nfloat modded = mod(a_opacityInfo, 128.0);\nfloat targetOpacity = (a_opacityInfo - modded) / 128.0;\nfloat startOpacity = modded / 127.0;\nfloat interpolatedOpacity = clamp(startOpacity + 2.0 * (targetOpacity - 0.5) * u_time / u_fadeDuration, 0.0, 1.0);\nv_opacity *= interpolatedOpacity;\nmediump float a_angle         = a_levelInfo[1];\nmediump float a_minLevel      = a_levelInfo[2];\nmediump float a_maxLevel      = a_levelInfo[3];\nmediump vec2 a_tex            = a_texAngleRange.xy;\nmediump float delta_z = 0.0;\nmediump float rotated = mod(a_angle + u_mapRotation, 256.0);\ndelta_z += (1.0 - step(u_keepUpright, 0.0)) * step(64.0, rotated) * (1.0 - step(192.0, rotated));\ndelta_z += 1.0 - step(a_minLevel, u_level);\ndelta_z += step(a_maxLevel, u_level);\ndelta_z += step(v_opacity, 0.0);\nvec2 offset = C_OFFSET_PRECISION * a_vertexOffset;\nv_size = abs(offset);\n#ifdef SDF\noffset = (120.0 / 86.0) * offset;\n#endif\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos, 1.0) + u_displayViewMat3 * vec3(size * offset, 0.0) + u_displayMat3 * vec3(u_iconTranslation, 0.0);\ngl_Position = vec4(pos.xy, u_depth + delta_z, 1.0);\n#ifdef ID\nv_id = u_id / 255.0;\n#endif\nv_tex = a_tex.xy / u_mosaicSize;\n}" }, line: { "line.frag": "precision lowp float;\nvarying mediump vec2 v_normal;\nvarying highp float v_accumulatedDistance;\nvarying mediump float v_lineHalfWidth;\nvarying lowp vec4 v_color;\nvarying mediump float v_blur;\n#if defined (PATTERN) || defined(SDF)\nvarying mediump vec4 v_tlbr;\nvarying mediump vec2 v_patternSize;\nvarying mediump float v_widthRatio;\nuniform sampler2D u_texture;\nuniform mediump float u_antialiasing;\n#endif\n#ifdef SDF\n#include <util/encoding.glsl>\n#endif\n#ifdef ID\nvarying mediump vec4 v_id;\n#endif\nvoid main()\n{\nmediump float fragDist = length(v_normal) * v_lineHalfWidth;\nlowp float alpha = clamp((v_lineHalfWidth - fragDist) / v_blur, 0.0, 1.0);\n#ifdef PATTERN\nmediump float relativeTexX = fract(v_accumulatedDistance / (v_patternSize.x * v_widthRatio));\nmediump float relativeTexY = 0.5 + v_normal.y * v_lineHalfWidth / (v_patternSize.y * v_widthRatio);\nmediump vec2 texCoord = mix(v_tlbr.xy, v_tlbr.zw, vec2(relativeTexX, relativeTexY));\nlowp vec4 color = texture2D(u_texture, texCoord);\ngl_FragColor = alpha * v_color[3] * color;\n#elif defined(SDF)\nmediump float relativeTexX = fract((v_accumulatedDistance * 0.5) / (v_patternSize.x * v_widthRatio));\nmediump float relativeTexY =  0.5 + 0.25 * v_normal.y;\nmediump vec2 texCoord = mix(v_tlbr.xy, v_tlbr.zw, vec2(relativeTexX, relativeTexY));\nmediump float d = rgba2float(texture2D(u_texture, texCoord)) - 0.5;\nfloat dist = d * (v_lineHalfWidth + u_antialiasing / 2.0);\ngl_FragColor = alpha * clamp(0.5 - dist, 0.0, 1.0) * v_color;\n#else\ngl_FragColor = alpha * v_color;\n#endif\n#ifdef ID\nif (gl_FragColor.a < 1.0 / 255.0) {\ndiscard;\n}\ngl_FragColor = v_id;\n#endif\n}", "line.vert": "precision mediump float;\nattribute vec2 a_pos;\nattribute vec4 a_extrude_offset;\nattribute vec4 a_dir_normal;\nattribute vec2 a_accumulatedDistance;\n#pragma header\nuniform highp mat3 u_dvsMat3;\nuniform highp mat3 u_displayMat3;\nuniform highp mat3 u_displayViewMat3;\nuniform mediump float u_zoomFactor;\nuniform mediump vec2 u_lineTranslation;\nuniform mediump float u_antialiasing;\nuniform mediump float u_depth;\nvarying mediump vec2 v_normal;\nvarying highp float v_accumulatedDistance;\nconst float scale = 1.0 / 31.0;\nconst mediump float tileCoordRatio = 8.0;\n#if defined (SDF)\nconst mediump float sdfPatternHalfWidth = 15.5;\n#endif\n#if defined (PATTERN) || defined(SDF)\nuniform mediump vec2 u_mosaicSize;\nvarying mediump vec4 v_tlbr;\nvarying mediump vec2 v_patternSize;\nvarying mediump float v_widthRatio;\n#endif\n#ifdef ID\nuniform mediump vec4 u_id;\nvarying mediump vec4 v_id;\n#endif\nvarying lowp vec4 v_color;\nvarying mediump float v_lineHalfWidth;\nvarying mediump float v_blur;\nvoid main()\n{\n#pragma main\nv_color = color * opacity;\nv_blur = blur + u_antialiasing;\nv_normal = a_dir_normal.zw * scale;\n#if defined (PATTERN) || defined(SDF)\nv_tlbr          = tlbr / u_mosaicSize.xyxy;\nv_patternSize   = vec2(tlbr.z - tlbr.x, tlbr.y - tlbr.w);\n#if defined (PATTERN)\nv_widthRatio = width / v_patternSize.y;\n#else\nv_widthRatio = width / sdfPatternHalfWidth / 2.0;\n#endif\n#endif\nv_lineHalfWidth = (width + u_antialiasing) * 0.5;\nmediump vec2 dir = a_dir_normal.xy * scale;\nmediump vec2 offset_ = a_extrude_offset.zw * scale * offset;\nmediump vec2 dist = v_lineHalfWidth * scale * a_extrude_offset.xy;\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos + offset_ * tileCoordRatio / u_zoomFactor, 1.0) + u_displayViewMat3 * vec3(dist, 0.0) + u_displayMat3 * vec3(u_lineTranslation, 0.0);\ngl_Position = vec4(pos.xy, u_depth, 1.0);\n#if defined (PATTERN) || defined(SDF)\nv_accumulatedDistance = a_accumulatedDistance.x * u_zoomFactor / tileCoordRatio + dot(dir, dist + offset_);\n#endif\n#ifdef ID\nv_id = u_id / 255.0;\n#endif\n}" }, outline: { "outline.frag": "varying lowp vec4 v_color;\nvarying mediump vec2 v_normal;\n#ifdef ID\nvarying mediump vec4 v_id;\n#endif\nvoid main()\n{\nlowp float dist = abs(v_normal.y);\nlowp float alpha = smoothstep(1.0, 0.0, dist);\ngl_FragColor = alpha * v_color;\n#ifdef ID\nif (gl_FragColor.a < 1.0 / 255.0) {\ndiscard;\n}\ngl_FragColor = v_id;\n#endif\n}", "outline.vert": "attribute vec2 a_pos;\nattribute vec2 a_offset;\nattribute vec2 a_xnormal;\n#pragma header\nvarying lowp vec4 v_color;\n#ifdef ID\nuniform mediump vec4 u_id;\nvarying mediump vec4 v_id;\n#endif\nuniform highp mat3 u_dvsMat3;\nuniform highp mat3 u_displayMat3;\nuniform mediump vec2 u_fillTranslation;\nuniform mediump float u_depth;\nuniform mediump float u_outline_width;\nvarying lowp vec2 v_normal;\nconst float scale = 1.0 / 15.0;\nvoid main()\n{\n#pragma main\nv_color = color * opacity;\n#ifdef ID\nv_id = u_id / 255.0;\n#endif\nv_normal = a_xnormal;\nmediump vec2 dist = u_outline_width * scale * a_offset;\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos, 1.0) + u_displayMat3 * vec3(dist + u_fillTranslation, 0.0);\ngl_Position = vec4(pos.xy, u_depth, 1.0);\n}" }, text: { "text.frag": "uniform lowp sampler2D u_texture;\nvarying lowp vec2 v_tex;\nvarying lowp vec4 v_color;\nvarying mediump float v_edgeWidth;\nvarying mediump float v_edgeDistance;\n#ifdef ID\nvarying mediump vec4 v_id;\n#endif\nvoid main()\n{\nlowp float dist = texture2D(u_texture, v_tex).a;\nmediump float alpha = smoothstep(v_edgeDistance - v_edgeWidth, v_edgeDistance + v_edgeWidth, dist);\ngl_FragColor = alpha * v_color;\n#ifdef ID\nif (gl_FragColor.a < 1.0 / 255.0) {\ndiscard;\n}\ngl_FragColor = v_id;\n#endif\n}", "text.vert": "attribute vec2 a_pos;\nattribute vec2 a_vertexOffset;\nattribute vec4 a_texAngleRange;\nattribute vec4 a_levelInfo;\nattribute float a_opacityInfo;\n#pragma header\nvarying lowp vec4 v_color;\n#ifdef ID\nuniform mediump vec4 u_id;\nvarying mediump vec4 v_id;\n#endif\nuniform highp mat3 u_dvsMat3;\nuniform highp mat3 u_displayMat3;\nuniform highp mat3 u_displayViewMat3;\nuniform mediump vec2 u_textTranslation;\nuniform vec2 u_mosaicSize;\nuniform mediump float u_depth;\nuniform mediump float u_mapRotation;\nuniform mediump float u_level;\nuniform lowp float u_keepUpright;\nuniform mediump float u_fadeDuration;\nvarying lowp vec2 v_tex;\nconst float offsetPrecision = 1.0 / 8.0;\nconst mediump float edgePos = 0.75;\nuniform mediump float u_antialiasingWidth;\nvarying mediump float v_edgeDistance;\nvarying mediump float v_edgeWidth;\nuniform lowp float u_halo;\nconst float sdfFontScale = 1.0 / 24.0;\nconst float sdfPixel = 3.0;\nuniform highp float u_time;\nvoid main()\n{\n#pragma main\nif (u_halo > 0.5)\n{\nv_color = halo_color * opacity;\nhalo_width *= sdfPixel;\nhalo_blur *= sdfPixel;\n}\nelse\n{\nv_color = color * opacity;\nhalo_width = 0.0;\nhalo_blur = 0.0;\n}\nfloat modded = mod(a_opacityInfo, 128.0);\nfloat targetOpacity = (a_opacityInfo - modded) / 128.0;\nfloat startOpacity = modded / 127.0;\nfloat interpolatedOpacity = clamp(startOpacity + 2.0 * (targetOpacity - 0.5) * u_time / u_fadeDuration, 0.0, 1.0);\nv_color *= interpolatedOpacity;\nmediump float a_angle       = a_levelInfo[1];\nmediump float a_minLevel    = a_levelInfo[2];\nmediump float a_maxLevel    = a_levelInfo[3];\nmediump vec2 a_tex          = a_texAngleRange.xy;\nmediump float a_visMinAngle    = a_texAngleRange.z;\nmediump float a_visMaxAngle    = a_texAngleRange.w;\nmediump float delta_z = 0.0;\nmediump float angle = mod(a_angle + u_mapRotation, 256.0);\nif (a_visMinAngle < a_visMaxAngle)\n{\ndelta_z += (1.0 - step(u_keepUpright, 0.0)) * (step(a_visMaxAngle, angle) + (1.0 - step(a_visMinAngle, angle)));\n}\nelse\n{\ndelta_z += (1.0 - step(u_keepUpright, 0.0)) * (step(a_visMaxAngle, angle) * (1.0 - step(a_visMinAngle, angle)));\n}\ndelta_z += 1.0 - step(a_minLevel, u_level);\ndelta_z += step(a_maxLevel, u_level);\ndelta_z += step(v_color[3], 0.0);\nv_tex = a_tex.xy / u_mosaicSize;\n#ifdef ID\nv_id = u_id / 255.0;\n#endif\nv_edgeDistance = edgePos - halo_width / size;\nv_edgeWidth = (u_antialiasingWidth + halo_blur) / size;\nmediump vec3 pos = u_dvsMat3 * vec3(a_pos, 1.0) + sdfFontScale * u_displayViewMat3 * vec3(offsetPrecision * size * a_vertexOffset, 0.0) + u_displayMat3 * vec3(u_textTranslation, 0.0);\ngl_Position = vec4(pos.xy, u_depth + delta_z, 1.0);\n}" }, util: { "encoding.glsl": "const vec4 rgba2float_factors = vec4(\n255.0 / (256.0),\n255.0 / (256.0 * 256.0),\n255.0 / (256.0 * 256.0 * 256.0),\n255.0 / (256.0 * 256.0 * 256.0 * 256.0)\n);\nfloat rgba2float(vec4 rgba) {\nreturn dot(rgba, rgba2float_factors);\n}", "util.glsl": "float nextPOT(in float x) {\nreturn pow(2.0, ceil(log2(abs(x))));\n}" } };

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/shaders/sources/resolver.js
function o5(e13) {
  let o10 = e8;
  return e13.split("/").forEach((r12) => {
    o10 && (o10 = o10[r12]);
  }), o10;
}
var t4 = new e6(o5);
function n9(r12) {
  return t4.resolveIncludes(r12);
}

// node_modules/@arcgis/core/views/2d/engine/webgl/shaders/BitBlitPrograms.js
var e9 = { shaders: { vertexShader: n8("bitBlit/bitBlit.vert"), fragmentShader: n8("bitBlit/bitBlit.frag") }, attributes: /* @__PURE__ */ new Map([["a_pos", 0], ["a_tex", 1]]) };

// node_modules/@arcgis/core/views/2d/engine/webgl/RectangleBinPack.js
var t5 = class {
  constructor(t11, e13) {
    this._width = 0, this._height = 0, this._free = [], this._width = t11, this._height = e13, this._free.push(new t2(0, 0, t11, e13));
  }
  get width() {
    return this._width;
  }
  get height() {
    return this._height;
  }
  allocate(t11, e13) {
    if (t11 > this._width || e13 > this._height) return new t2();
    let i8 = null, s12 = -1;
    for (let h6 = 0; h6 < this._free.length; ++h6) {
      const r12 = this._free[h6];
      t11 <= r12.width && e13 <= r12.height && (null === i8 || r12.y <= i8.y && r12.x <= i8.x) && (i8 = r12, s12 = h6);
    }
    return null === i8 ? new t2() : (this._free.splice(s12, 1), i8.width < i8.height ? (i8.width > t11 && this._free.push(new t2(i8.x + t11, i8.y, i8.width - t11, e13)), i8.height > e13 && this._free.push(new t2(i8.x, i8.y + e13, i8.width, i8.height - e13))) : (i8.width > t11 && this._free.push(new t2(i8.x + t11, i8.y, i8.width - t11, i8.height)), i8.height > e13 && this._free.push(new t2(i8.x, i8.y + e13, t11, i8.height - e13))), new t2(i8.x, i8.y, t11, e13));
  }
  release(h6) {
    for (let t11 = 0; t11 < this._free.length; ++t11) {
      const e13 = this._free[t11];
      if (e13.y === h6.y && e13.height === h6.height && e13.x + e13.width === h6.x) e13.width += h6.width;
      else if (e13.x === h6.x && e13.width === h6.width && e13.y + e13.height === h6.y) e13.height += h6.height;
      else if (h6.y === e13.y && h6.height === e13.height && h6.x + h6.width === e13.x) e13.x = h6.x, e13.width += h6.width;
      else {
        if (h6.x !== e13.x || h6.width !== e13.width || h6.y + h6.height !== e13.y) continue;
        e13.y = h6.y, e13.height += h6.height;
      }
      this._free.splice(t11, 1), this.release(h6);
    }
    this._free.push(h6);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/GlyphMosaic.js
var a7 = 256;
var n10 = (t11) => Math.floor(t11 / 256);
function c4(t11) {
  const e13 = /* @__PURE__ */ new Set();
  for (const i8 of t11) e13.add(n10(i8));
  return e13;
}
function o6(e13, i8, h6) {
  return e13.has(i8) || e13.set(i8, h6().then(() => {
    e13.delete(i8);
  }).catch((h7) => {
    e13.delete(i8), b(h7);
  })), e13.get(i8);
}
var l2 = (t11) => ({ rect: new t2(0, 0, 0, 0), page: 0, metrics: { left: 0, width: 0, height: 0, advance: 0, top: 0 }, code: t11, sdf: true });
var g2 = class {
  constructor(t11, e13, h6) {
    this.width = 0, this.height = 0, this._dirties = [], this._glyphData = [], this._currentPage = 0, this._glyphCache = {}, this._textures = [], this._rangePromises = /* @__PURE__ */ new Map(), this.width = t11, this.height = e13, this._glyphSource = h6, this._binPack = new t5(t11 - 4, e13 - 4), this._glyphData.push(new Uint8Array(t11 * e13)), this._dirties.push(true), this._textures.push(null), this._initDecorationGlyph();
  }
  dispose() {
    this._binPack = null;
    for (const t11 of this._textures) t11 && t11.dispose();
    this._textures.length = 0, this._glyphData.length = 0;
  }
  _initDecorationGlyph() {
    const t11 = [117, 149, 181, 207, 207, 181, 149, 117], e13 = [];
    for (let h6 = 0; h6 < t11.length; h6++) {
      const i9 = t11[h6];
      for (let t12 = 0; t12 < 11; t12++) e13.push(i9);
    }
    const i8 = { metrics: { width: 5, height: 2, left: 0, top: 0, advance: 0 }, bitmap: new Uint8Array(e13) };
    this._recordGlyph(i8);
  }
  async getGlyphItems(t11, e13, i8) {
    const h6 = this._getGlyphCache(t11);
    return await this._fetchRanges(t11, e13, i8), e13.map((e14) => this._getMosaicItem(h6, t11, e14));
  }
  bind(t11, e13, i8, h6) {
    const s12 = this._getTexture(t11, i8);
    s12.setSamplingMode(e13), this._dirties[i8] && (s12.setData(this._glyphData[i8]), this._dirties[i8] = false), t11.bindTexture(s12, h6);
  }
  _getGlyphCache(t11) {
    return this._glyphCache[t11] || (this._glyphCache[t11] = {}), this._glyphCache[t11];
  }
  _getTexture(t11, e13) {
    return this._textures[e13] || (this._textures[e13] = new E(t11, { pixelFormat: P.ALPHA, dataType: G.UNSIGNED_BYTE, width: this.width, height: this.height }, new Uint8Array(this.width * this.height))), this._textures[e13];
  }
  _invalidate() {
    this._dirties[this._currentPage] = true;
  }
  async _fetchRanges(t11, e13, i8) {
    const h6 = c4(e13), s12 = [];
    h6.forEach((e14) => {
      s12.push(this._fetchRange(t11, e14, i8));
    }), await Promise.all(s12);
  }
  async _fetchRange(t11, e13, i8) {
    if (e13 > a7) return;
    const h6 = t11 + e13;
    return o6(this._rangePromises, h6, () => this._glyphSource.getRange(t11, e13, i8));
  }
  _getMosaicItem(t11, e13, i8) {
    if (!t11[i8]) {
      const h6 = this._glyphSource.getGlyph(e13, i8);
      if (!h6 || !h6.metrics) return l2(i8);
      const s12 = this._recordGlyph(h6), r12 = this._currentPage, a13 = h6.metrics;
      t11[i8] = { rect: s12, page: r12, metrics: a13, code: i8, sdf: true }, this._invalidate();
    }
    return t11[i8];
  }
  _recordGlyph(t11) {
    const h6 = t11.metrics;
    let s12;
    if (0 === h6.width) s12 = new t2(0, 0, 0, 0);
    else {
      const e13 = 3, r12 = h6.width + 2 * e13, a13 = h6.height + 2 * e13;
      s12 = this._binPack.allocate(r12, a13), s12.isEmpty && (this._dirties[this._currentPage] || (this._glyphData[this._currentPage] = null), this._currentPage = this._glyphData.length, this._glyphData.push(new Uint8Array(this.width * this.height)), this._dirties.push(true), this._textures.push(null), this._initDecorationGlyph(), this._binPack = new t5(this.width - 4, this.height - 4), s12 = this._binPack.allocate(r12, a13));
      const n16 = this._glyphData[this._currentPage], c11 = t11.bitmap;
      let o10, l10;
      if (c11) for (let t12 = 0; t12 < a13; t12++) {
        o10 = r12 * t12, l10 = this.width * (s12.y + t12) + s12.x;
        for (let t13 = 0; t13 < r12; t13++) n16[l10 + t13] = c11[o10 + t13];
      }
      has("esri-glyph-debug") && this._showDebugPage(n16);
    }
    return s12;
  }
  _showDebugPage(t11) {
    const e13 = document.createElement("canvas"), i8 = e13.getContext("2d"), h6 = new ImageData(this.width, this.height), s12 = h6.data;
    e13.width = this.width, e13.height = this.height, e13.style.border = "1px solid black";
    for (let r12 = 0; r12 < t11.length; ++r12) s12[4 * r12 + 0] = t11[r12], s12[4 * r12 + 1] = 0, s12[4 * r12 + 2] = 0, s12[4 * r12 + 3] = 255;
    i8.putImageData(h6, 0, 0), document.body.appendChild(e13);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/GlyphSource.js
var s7 = class {
  constructor(t11) {
    for (this._metrics = [], this._bitmaps = []; t11.next(); ) switch (t11.tag()) {
      case 1: {
        const e13 = t11.getMessage();
        for (; e13.next(); ) switch (e13.tag()) {
          case 3: {
            const t12 = e13.getMessage();
            let s12, a13, r12, n16, i8, c11, g7;
            for (; t12.next(); ) switch (t12.tag()) {
              case 1:
                s12 = t12.getUInt32();
                break;
              case 2:
                a13 = t12.getBytes();
                break;
              case 3:
                r12 = t12.getUInt32();
                break;
              case 4:
                n16 = t12.getUInt32();
                break;
              case 5:
                i8 = t12.getSInt32();
                break;
              case 6:
                c11 = t12.getSInt32();
                break;
              case 7:
                g7 = t12.getUInt32();
                break;
              default:
                t12.skip();
            }
            t12.release(), s12 && (this._metrics[s12] = { width: r12, height: n16, left: i8, top: c11, advance: g7 }, this._bitmaps[s12] = a13);
            break;
          }
          default:
            e13.skip();
        }
        e13.release();
        break;
      }
      default:
        t11.skip();
    }
  }
  getMetrics(t11) {
    return this._metrics[t11];
  }
  getBitmap(t11) {
    return this._bitmaps[t11];
  }
};
var a8 = class {
  constructor() {
    this._ranges = [];
  }
  getRange(t11) {
    return this._ranges[t11];
  }
  addRange(t11, e13) {
    this._ranges[t11] = e13;
  }
};
var r4 = class {
  constructor(t11) {
    this._glyphInfo = {}, this._baseURL = t11;
  }
  getRange(a13, r12, n16) {
    const i8 = this._getFontStack(a13);
    if (i8.getRange(r12)) return Promise.resolve();
    const c11 = 256 * r12, g7 = c11 + 255, o10 = this._baseURL.replace("{fontstack}", a13).replace("{range}", c11 + "-" + g7);
    return U(o10, { responseType: "array-buffer", ...n16 }).then((t11) => {
      i8.addRange(r12, new s7(new n4(new Uint8Array(t11.data), new DataView(t11.data))));
    });
  }
  getGlyph(t11, e13) {
    const s12 = this._getFontStack(t11);
    if (!s12) return;
    const a13 = Math.floor(e13 / 256);
    if (a13 > 256) return;
    const r12 = s12.getRange(a13);
    return r12 ? { metrics: r12.getMetrics(e13), bitmap: r12.getBitmap(e13) } : void 0;
  }
  _getFontStack(t11) {
    let e13 = this._glyphInfo[t11];
    return e13 || (e13 = this._glyphInfo[t11] = new a8()), e13;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/SDFConverter.js
var i4 = 1e20;
var r5 = class {
  constructor(t11) {
    this._svg = null, this.size = t11;
    const e13 = document.createElement("canvas");
    e13.width = e13.height = t11, this._context = e13.getContext("2d"), this._gridOuter = new Float64Array(t11 * t11), this._gridInner = new Float64Array(t11 * t11), this._f = new Float64Array(t11), this._d = new Float64Array(t11), this._z = new Float64Array(t11 + 1), this._v = new Int16Array(t11);
  }
  dispose() {
    this._context = this._gridOuter = this._gridInner = this._f = this._d = this._z = this._v = null, this._svg && (document.body.removeChild(this._svg), this._svg = null);
  }
  draw(r12, h6, n16 = 31) {
    this._initSVG();
    const o10 = this.createSVGString(r12);
    return new Promise((r13, a13) => {
      const d7 = new Image();
      d7.src = "data:image/svg+xml; charset=utf8, " + encodeURIComponent(o10), d7.onload = () => {
        d7.onload = null, this._context.clearRect(0, 0, this.size, this.size), this._context.drawImage(d7, 0, 0, this.size, this.size);
        const e13 = this._context.getImageData(0, 0, this.size, this.size), s12 = new Uint8Array(this.size * this.size * 4);
        for (let t11 = 0; t11 < this.size * this.size; t11++) {
          const s13 = e13.data[4 * t11 + 3] / 255;
          this._gridOuter[t11] = 1 === s13 ? 0 : 0 === s13 ? i4 : Math.max(0, 0.5 - s13) ** 2, this._gridInner[t11] = 1 === s13 ? i4 : 0 === s13 ? 0 : Math.max(0, s13 - 0.5) ** 2;
        }
        this._edt(this._gridOuter, this.size, this.size), this._edt(this._gridInner, this.size, this.size);
        for (let i8 = 0; i8 < this.size * this.size; i8++) {
          const e14 = this._gridOuter[i8] - this._gridInner[i8];
          o3(0.5 - e14 / (2 * n16), s12, 4 * i8);
        }
        r13(s12);
      };
      const l10 = h6 && h6.signal;
      l10 && v(l10, () => a13(a3()));
    });
  }
  _initSVG() {
    if (!this._svg) {
      const t11 = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      t11.setAttribute("style", "position: absolute;"), t11.setAttribute("width", "0"), t11.setAttribute("height", "0"), t11.setAttribute("aria-hidden", "true"), t11.setAttribute("role", "presentation"), document.body.appendChild(t11), this._svg = t11;
    }
    return this._svg;
  }
  createSVGString(t11) {
    const e13 = this._initSVG(), s12 = document.createElementNS("http://www.w3.org/2000/svg", "path");
    s12.setAttribute("d", t11), e13.appendChild(s12);
    const i8 = s12.getBBox(), r12 = i8.width / i8.height, h6 = this.size / 2;
    let n16, o10, a13, d7;
    if (r12 > 1) {
      o10 = n16 = h6 / i8.width;
      const t12 = h6 * (1 / r12);
      a13 = this.size / 4, d7 = h6 - t12 / 2;
    } else {
      n16 = o10 = h6 / i8.height;
      a13 = h6 - h6 * r12 / 2, d7 = this.size / 4;
    }
    const l10 = -i8.x * n16 + a13, _5 = -i8.y * o10 + d7;
    s12.setAttribute("style", `transform: matrix(${n16}, 0, 0, ${o10}, ${l10}, ${_5})`);
    const g7 = `<svg style="fill:red;" height="${this.size}" width="${this.size}" xmlns="http://www.w3.org/2000/svg">${e13.innerHTML}</svg>`;
    return e13.removeChild(s12), g7;
  }
  _edt(t11, e13, s12) {
    const i8 = this._f, r12 = this._d, h6 = this._v, n16 = this._z;
    for (let o10 = 0; o10 < e13; o10++) {
      for (let r13 = 0; r13 < s12; r13++) i8[r13] = t11[r13 * e13 + o10];
      this._edt1d(i8, r12, h6, n16, s12);
      for (let i9 = 0; i9 < s12; i9++) t11[i9 * e13 + o10] = r12[i9];
    }
    for (let o10 = 0; o10 < s12; o10++) {
      for (let s13 = 0; s13 < e13; s13++) i8[s13] = t11[o10 * e13 + s13];
      this._edt1d(i8, r12, h6, n16, e13);
      for (let s13 = 0; s13 < e13; s13++) t11[o10 * e13 + s13] = Math.sqrt(r12[s13]);
    }
  }
  _edt1d(t11, e13, s12, r12, h6) {
    s12[0] = 0, r12[0] = -i4, r12[1] = +i4;
    for (let n16 = 1, o10 = 0; n16 < h6; n16++) {
      let e14 = (t11[n16] + n16 * n16 - (t11[s12[o10]] + s12[o10] * s12[o10])) / (2 * n16 - 2 * s12[o10]);
      for (; e14 <= r12[o10]; ) o10--, e14 = (t11[n16] + n16 * n16 - (t11[s12[o10]] + s12[o10] * s12[o10])) / (2 * n16 - 2 * s12[o10]);
      o10++, s12[o10] = n16, r12[o10] = e14, r12[o10 + 1] = +i4;
    }
    for (let i8 = 0, n16 = 0; i8 < h6; i8++) {
      for (; r12[n16 + 1] < i8; ) n16++;
      e13[i8] = (i8 - s12[n16]) * (i8 - s12[n16]) + t11[s12[n16]];
    }
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/SpriteMosaic.js
function c5(t11) {
  return t11 && "static" === t11.type;
}
var n11 = class _n {
  constructor(t11, e13, i8 = 0) {
    this._mosaicPages = [], this._maxItemSize = 0, this._currentPage = 0, this._pageWidth = 0, this._pageHeight = 0, this._mosaicRects = /* @__PURE__ */ new Map(), this._spriteCopyQueue = [], this.pixelRatio = 1, (t11 <= 0 || e13 <= 0) && console.error("Sprites mosaic defaultWidth and defaultHeight must be greater than zero!"), this._pageWidth = t11, this._pageHeight = e13, i8 > 0 && (this._maxItemSize = i8), this.pixelRatio = window.devicePixelRatio || 1, this._binPack = new t5(this._pageWidth, this._pageHeight);
    const s12 = Math.floor(this._pageWidth), r12 = Math.floor(this._pageHeight);
    this._mosaicPages.push({ mosaicsData: { type: "static", data: new Uint32Array(s12 * r12) }, size: [this._pageWidth, this._pageHeight], dirty: true, texture: void 0 });
  }
  getWidth(t11) {
    return t11 >= this._mosaicPages.length ? -1 : this._mosaicPages[t11].size[0];
  }
  getHeight(t11) {
    return t11 >= this._mosaicPages.length ? -1 : this._mosaicPages[t11].size[1];
  }
  getPageTexture(t11) {
    return t11 < this._mosaicPages.length ? this._mosaicPages[t11].texture : null;
  }
  has(t11) {
    return this._mosaicRects.has(t11);
  }
  get itemCount() {
    return this._mosaicRects.size;
  }
  getSpriteItem(t11) {
    return this._mosaicRects.get(t11);
  }
  addSpriteItem(t11, i8, a13, r12, o10, h6, n16 = 1) {
    if (this._mosaicRects.has(t11)) return this._mosaicRects.get(t11);
    let g7, p4, m5;
    if (c5(a13)) [g7, p4, m5] = this._allocateImage(i8[0], i8[1]);
    else {
      g7 = new t2(0, 0, i8[0], i8[1]), p4 = this._mosaicPages.length;
      const t12 = void 0;
      this._mosaicPages.push({ mosaicsData: a13, size: [i8[0] + 2 * et, i8[1] + 2 * et], dirty: true, texture: t12 });
    }
    if (g7.width <= 0 || g7.height <= 0) return null;
    const _5 = { rect: g7, width: i8[0], height: i8[1], sdf: o10, simplePattern: h6, pixelRatio: n16, page: p4 };
    return this._mosaicRects.set(t11, _5), c5(a13) && this._copy({ rect: g7, spriteSize: i8, spriteData: a13.data, page: p4, pageSize: m5, repeat: r12, sdf: o10 }), _5;
  }
  hasItemsToProcess() {
    return 0 !== this._spriteCopyQueue.length;
  }
  processNextItem() {
    const t11 = this._spriteCopyQueue.pop();
    t11 && this._copy(t11);
  }
  getSpriteItems(t11) {
    const e13 = {};
    for (const i8 of t11) e13[i8] = this.getSpriteItem(i8);
    return e13;
  }
  getMosaicItemPosition(t11) {
    const i8 = this.getSpriteItem(t11), s12 = i8 && i8.rect;
    if (!s12) return null;
    s12.width = i8.width, s12.height = i8.height;
    const a13 = i8.width, r12 = i8.height, o10 = et, h6 = this._mosaicPages[i8.page];
    return { size: [i8.width, i8.height], tl: [(s12.x + o10) / h6[0], (s12.y + o10) / h6[1]], br: [(s12.x + o10 + a13) / h6[0], (s12.y + o10 + r12) / h6[1]], page: i8.page };
  }
  bind(t11, e13, i8 = 0, s12 = 0) {
    const a13 = this._mosaicPages[i8], r12 = a13.mosaicsData;
    let o10 = a13.texture;
    if (o10 || (o10 = g3(t11, a13.size), a13.texture = o10), o10.setSamplingMode(e13), c5(r12)) t11.bindTexture(o10, s12), a13.dirty && (o10.setData(new Uint8Array(r12.data.buffer)), o10.generateMipmap());
    else {
      r12.data.bindFrame(t11, o10, s12), o10.generateMipmap();
    }
    a13.dirty = false;
  }
  static _copyBits(t11, e13, i8, s12, a13, r12, o10, h6, c11, n16, g7) {
    let p4 = s12 * e13 + i8, m5 = h6 * r12 + o10;
    if (g7) {
      m5 -= r12;
      for (let o11 = -1; o11 <= n16; o11++, p4 = ((o11 + n16) % n16 + s12) * e13 + i8, m5 += r12) for (let e14 = -1; e14 <= c11; e14++) a13[m5 + e14] = t11[p4 + (e14 + c11) % c11];
    } else for (let _5 = 0; _5 < n16; _5++) {
      for (let e14 = 0; e14 < c11; e14++) a13[m5 + e14] = t11[p4 + e14];
      p4 += e13, m5 += r12;
    }
  }
  _copy(i8) {
    if (i8.page >= this._mosaicPages.length) return;
    const s12 = this._mosaicPages[i8.page], a13 = s12.mosaicsData;
    if (!c5(s12.mosaicsData)) throw new s3("mapview-invalid-resource", "unsuitable data type!");
    const r12 = i8.spriteData, o10 = a13.data;
    o10 && r12 || console.error("Source or target images are uninitialized!"), _n._copyBits(r12, i8.spriteSize[0], 0, 0, o10, i8.pageSize[0], i8.rect.x + et, i8.rect.y + et, i8.spriteSize[0], i8.spriteSize[1], i8.repeat), s12.dirty = true;
  }
  _allocateImage(t11, r12) {
    t11 += 2 * et, r12 += 2 * et;
    const o10 = Math.max(t11, r12);
    if (this._maxItemSize && this._maxItemSize < o10) {
      const e13 = 2 ** Math.ceil(e2(t11)), a13 = 2 ** Math.ceil(e2(r12)), o11 = new t2(0, 0, t11, r12);
      return this._mosaicPages.push({ mosaicsData: { type: "static", data: new Uint32Array(e13 * a13) }, size: [e13, a13], dirty: true, texture: void 0 }), [o11, this._mosaicPages.length - 1, [e13, a13]];
    }
    const h6 = this._binPack.allocate(t11, r12);
    if (h6.width <= 0) {
      const e13 = this._mosaicPages[this._currentPage];
      return !e13.dirty && c5(e13.mosaicsData) && (e13.mosaicsData.data = null), this._currentPage = this._mosaicPages.length, this._mosaicPages.push({ mosaicsData: { type: "static", data: new Uint32Array(this._pageWidth * this._pageHeight) }, size: [this._pageWidth, this._pageHeight], dirty: true, texture: void 0 }), this._binPack = new t5(this._pageWidth, this._pageHeight), this._allocateImage(t11, r12);
    }
    return [h6, this._currentPage, [this._pageWidth, this._pageHeight]];
  }
  dispose() {
    this._binPack = null;
    for (const t11 of this._mosaicPages) {
      const e13 = t11.texture;
      e13 && e13.dispose();
      const i8 = t11.mosaicsData;
      if (!c5(i8)) {
        i8.data.destroy();
      }
    }
    this._mosaicPages = null, this._mosaicRects.clear();
  }
};
function g3(t11, e13) {
  return new E(t11, { pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, width: e13[0], height: e13[1] }, null);
}

// node_modules/@arcgis/core/views/2d/engine/webgl/animatedFormats/utils.js
function n12(e13) {
  return n(e13.frameDurations.reduce((t11, e14) => t11 + e14, 0));
}
function s8(t11) {
  const { width: e13, height: r12 } = t11;
  return { frameDurations: t11.frameDurations.reverse(), getFrame: (e14) => {
    const r13 = t11.frameDurations.length - 1 - e14;
    return t11.getFrame(r13);
  }, width: e13, height: r12 };
}
function o7(e13, r12) {
  const { width: i8, height: a13, getFrame: s12 } = e13, o10 = r12 / n12(e13);
  return { frameDurations: e13.frameDurations.map((e14) => n(e14 * o10)), getFrame: s12, width: i8, height: a13 };
}
function m2(e13, r12) {
  const { width: i8, height: a13, getFrame: n16 } = e13, s12 = e13.frameDurations.slice(), o10 = s12.shift();
  return s12.unshift(n(o10 + r12)), { frameDurations: s12, getFrame: n16, width: i8, height: a13 };
}
function h2(e13, r12) {
  const { width: i8, height: a13, getFrame: n16 } = e13, s12 = e13.frameDurations.slice(), o10 = s12.pop();
  return s12.push(n(o10 + r12)), { frameDurations: s12, getFrame: n16, width: i8, height: a13 };
}
var c6 = class {
  constructor(t11, e13, r12, i8) {
    this._animation = t11, this._repeatType = r12, this._onFrameData = i8, this._direction = 1, this._currentFrame = 0, this.timeToFrame = this._animation.frameDurations[this._currentFrame];
    let a13 = 0;
    for (; e13 > a13; ) a13 += this.timeToFrame, this.nextFrame();
    const n16 = this._animation.getFrame(this._currentFrame);
    this._onFrameData(n16);
  }
  nextFrame() {
    if (this._currentFrame += this._direction, this._direction > 0) {
      if (this._currentFrame === this._animation.frameDurations.length) switch (this._repeatType) {
        case d.None:
          this._currentFrame -= this._direction;
          break;
        case d.Loop:
          this._currentFrame = 0;
          break;
        case d.Oscillate:
          this._currentFrame -= this._direction, this._direction = -1;
      }
    } else if (-1 === this._currentFrame) switch (this._repeatType) {
      case d.None:
        this._currentFrame -= this._direction;
        break;
      case d.Loop:
        this._currentFrame = this._animation.frameDurations.length - 1;
        break;
      case d.Oscillate:
        this._currentFrame -= this._direction, this._direction = 1;
    }
    this.timeToFrame = this._animation.frameDurations[this._currentFrame];
    const t11 = this._animation.getFrame(this._currentFrame);
    this._onFrameData(t11);
  }
};
function u3(e13, u8, f7, l10) {
  let g7, { repeatType: F2 } = u8;
  if (null == F2 && (F2 = d.Loop), true === u8.reverseAnimation && (e13 = s8(e13)), null != u8.duration && (e13 = o7(e13, n(1e3 * u8.duration))), null != u8.repeatDelay) {
    const i8 = 1e3 * u8.repeatDelay;
    F2 === d.Loop ? e13 = h2(e13, n(i8)) : F2 === d.Oscillate && (e13 = m2(h2(e13, n(i8 / 2)), n(i8 / 2)));
  }
  if (null != u8.startTimeOffset) g7 = n(1e3 * u8.startTimeOffset);
  else if (null != u8.randomizeStartTime) {
    const r12 = o4(f7), s12 = 82749913, o10 = null != u8.randomizeStartSeed ? u8.randomizeStartSeed : s12, m5 = e3(r12, o10);
    g7 = n(m5 * n12(e13));
  } else g7 = n(0);
  return new c6(e13, g7, F2, l10);
}
function f4(t11, e13, r12, i8) {
  const a13 = null == e13.playAnimation || e13.playAnimation, n16 = u3(t11, e13, r12, i8);
  let s12, o10 = n16.timeToFrame;
  function m5() {
    s12 = a13 ? setTimeout(() => {
      n16.nextFrame(), o10 = n16.timeToFrame, m5();
    }, o10) : void 0;
  }
  return m5(), { remove: () => {
    a13 && clearTimeout(s12);
  } };
}
var l3 = document.createElement("canvas");
var g4 = l3.getContext("2d");
function F(t11, r12, i8) {
  l3.width = r12, l3.height = i8;
  const a13 = [], n16 = t11.frameDurations.length;
  for (let s12 = 0; s12 < n16; s12++) {
    const n17 = t11.getFrame(s12);
    g4.clearRect(0, 0, r12, i8), n17 instanceof ImageData ? g4.drawImage(c2(n17), 0, 0, r12, i8) : g4.drawImage(n17, 0, 0, r12, i8), a13.push(g4.getImageData(0, 0, r12, i8));
  }
  return { width: r12, height: i8, frameDurations: t11.frameDurations, getFrame: (t12) => a13[t12] };
}

// node_modules/@arcgis/core/views/2d/engine/webgl/animatedFormats/AnimatableTextureResource.js
var e10 = class {
  constructor(t11, a13, e13, s12) {
    this._animation = t11, this._frameData = null;
    const h6 = (t12) => {
      this._frameData = t12, a13.requestRender();
    };
    this.frameCount = this._animation.frameDurations.length, this.width = this._animation.width, this.height = this._animation.height, this._playHandle = f4(this._animation, e13, s12, h6);
  }
  destroy() {
    this._playHandle.remove();
  }
  bindFrame(i8, e13, s12) {
    i8.bindTexture(e13, s12), t(this._frameData) || (e13.updateData(0, et, et, this._frameData.width, this._frameData.height, this._frameData), this._frameData = null);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/util/symbolUtils.js
function e11(e13) {
  switch (e13.type) {
    case "esriSMS":
      return `${e13.style}.${e13.path}`;
    case "esriSLS":
      return `${e13.style}.${e13.cap}`;
    case "esriSFS":
      return `${e13.style}`;
    case "esriPFS":
    case "esriPMS":
      return e13.imageData ? `${e13.imageData}${e13.width}${e13.height}` : `${e13.url}${e13.width}${e13.height}`;
    default:
      return "mosaicHash" in e13 ? e13.mosaicHash : JSON.stringify(e13);
  }
}

// node_modules/@arcgis/core/views/2d/engine/webgl/TextureManager.js
var k = n5();
var H = "arial-unicode-ms-regular";
var O = 126;
var Q = s2.getLogger("esri.views.2d.engine.webgl.TextureManager");
function V2(e13, t11) {
  const i8 = Math.round(u(t11) * window.devicePixelRatio), s12 = i8 >= 128 ? 2 : 4;
  return Math.min(e13, i8 * s12);
}
var Y = (e13, t11, i8) => Q.error(new s3(e13, t11, i8));
var D2 = class _D {
  static fromMosaic(e13, t11) {
    return new _D(e13, t11.page, t11.sdf);
  }
  constructor(e13, t11, i8) {
    this.mosaicType = e13, this.page = t11, this.sdf = i8;
  }
};
var J = class {
  constructor(i8, r12, o10) {
    this._requestRender = i8, this.resourceManager = r12, this._allowNonPowerOfTwo = o10, this._invalidFontsMap = /* @__PURE__ */ new Map(), this._sdfConverter = new r5(O), this._bindingInfos = new Array(), this._hashToBindingIndex = /* @__PURE__ */ new Map(), this._ongoingRasterizations = /* @__PURE__ */ new Map(), this._imageRequestQueue = new l({ concurrency: 10, process: async (e13, i9) => {
      f(i9);
      try {
        return await U(e13, { responseType: "image", signal: i9 });
      } catch (r13) {
        if (!j(r13)) throw new s3("mapview-invalid-resource", `Could not fetch requested resource at ${e13}`, r13);
        throw r13;
      }
    } }), this._spriteMosaic = new n11(2048, 2048, 500), this._glyphSource = new r4(`${s.fontsUrl}/{fontstack}/{range}.pbf`), this._glyphMosaic = new g2(1024, 1024, this._glyphSource), this._rasterizer = new c3(r12);
  }
  dispose() {
    this._spriteMosaic.dispose(), this._glyphMosaic.dispose(), this._rasterizer.dispose(), this._sdfConverter.dispose(), this._spriteMosaic = null, this._glyphMosaic = null, this._sdfConverter = null, this._hashToBindingIndex.clear(), this._hashToBindingIndex = null, this._bindingInfos = null, this._ongoingRasterizations.clear(), this._ongoingRasterizations = null, this._imageRequestQueue.clear(), this._imageRequestQueue = null;
  }
  get sprites() {
    return this._spriteMosaic;
  }
  get glyphs() {
    return this._glyphMosaic;
  }
  async rasterizeItem(e13, t11, i8, s12) {
    if (t(e13)) return Y("mapview-null-resource", "Unable to rasterize null resource"), null;
    switch (e13.type) {
      case "text":
      case "esriTS": {
        const t12 = await this._rasterizeText(e13, i8, s12);
        return t12.forEach((e14) => this._setTextureBinding(_.GLYPH, e14)), { glyphMosaicItems: t12 };
      }
      default: {
        if (ue(e13)) return Y("mapview-invalid-type", `MapView does not support symbol type: ${e13.type}`, e13), null;
        const i9 = await this._rasterizeSpriteSymbol(e13, t11, s12);
        return e4(i9) && i9 && this._setTextureBinding(_.SPRITE, i9), { spriteMosaicItem: i9 };
      }
    }
  }
  bindTextures(e13, t11, i8, s12 = false) {
    if (0 === i8.textureBinding) return;
    const r12 = this._bindingInfos[i8.textureBinding - 1], o10 = r12.page, n16 = s12 ? L.LINEAR_MIPMAP_LINEAR : L.LINEAR;
    switch (r12.mosaicType) {
      case _.SPRITE: {
        const i9 = this.sprites.getWidth(o10), s13 = this.sprites.getHeight(o10), r13 = r3(k, i9, s13);
        return this._spriteMosaic.bind(e13, n16, o10, y2), t11.setUniform1i("u_texture", y2), void t11.setUniform2fv("u_mosaicSize", r13);
      }
      case _.GLYPH: {
        const i9 = this.glyphs.width, s13 = this.glyphs.height, r13 = r3(k, i9, s13);
        return this._glyphMosaic.bind(e13, n16, o10, z), t11.setUniform1i("u_texture", z), void t11.setUniform2fv("u_mosaicSize", r13);
      }
      default:
        Q.error("mapview-texture-manager", `Cannot handle unknown type ${r12.mosaicType}`);
    }
  }
  _hashMosaic(e13, t11) {
    return 1 | e13 << 1 | (t11.sdf ? 1 : 0) << 2 | t11.page << 3;
  }
  _setTextureBinding(e13, t11) {
    const i8 = this._hashMosaic(e13, t11);
    if (!this._hashToBindingIndex.has(i8)) {
      const s12 = D2.fromMosaic(e13, t11), r12 = this._bindingInfos.length + 1;
      this._hashToBindingIndex.set(i8, r12), this._bindingInfos.push(s12);
    }
    t11.textureBinding = this._hashToBindingIndex.get(i8);
  }
  async _rasterizeText(e13, t11, s12) {
    let o10, n16;
    if ("cim" in e13) {
      const t12 = e13;
      o10 = t12.fontName, n16 = t12.text;
    } else {
      const t12 = e13;
      o10 = o2(t12.font), n16 = t12.text;
    }
    const a13 = this._invalidFontsMap.has(o10), h6 = t11 || pe(i2(n16)[0]);
    try {
      return await this._glyphMosaic.getGlyphItems(a13 ? H : o10, h6, s12);
    } catch (c11) {
      return Y("mapview-invalid-resource", `Couldn't find font ${o10}. Falling back to Arial Unicode MS Regular`), this._invalidFontsMap.set(o10, true), this._glyphMosaic.getGlyphItems(H, h6, s12);
    }
  }
  async _rasterizeSpriteSymbol(e13, t11, i8) {
    if (le(e13)) return;
    const r12 = e11(e13);
    if (this._spriteMosaic.has(r12)) return this._spriteMosaic.getSpriteItem(r12);
    if (ne(e13) || re(e13) && !Ie(e13)) return this._handleAsyncResource(r12, e13, i8);
    const o10 = jt, n16 = this._rasterizer.rasterizeJSONResource(e13, o10);
    if (n16) {
      const { size: t12, image: i9, sdf: s12, simplePattern: o11, rasterizationScale: a13 } = n16;
      return this._addItemToMosaic(r12, t12, { type: "static", data: i9 }, fe(e13), s12, o11, a13);
    }
    return new s3("TextureManager", "unrecognized or null rasterized image");
  }
  async _handleAsyncResource(e13, t11, i8) {
    if (this._ongoingRasterizations.has(e13)) return this._ongoingRasterizations.get(e13);
    let s12;
    s12 = ne(t11) ? this._handleSVG(t11, e13, i8) : this._handleImage(t11, e13, i8), this._ongoingRasterizations.set(e13, s12);
    try {
      await s12, this._ongoingRasterizations.delete(e13);
    } catch {
      this._ongoingRasterizations.delete(e13);
    }
    return s12;
  }
  async _handleSVG(e13, t11, i8) {
    const s12 = [O, O], r12 = await this._sdfConverter.draw(e13.path, i8);
    return this._addItemToMosaic(t11, s12, { type: "static", data: new Uint32Array(r12.buffer) }, false, true, true);
  }
  async _handleGIFOrPNG(e13, t11, i8) {
    const r12 = se(e13);
    await this.resourceManager.fetchResource(r12, i8);
    let o10 = this.resourceManager.getResource(r12);
    if (t(o10)) return new s3("mapview-invalid-resource", `Could not fetch requested resource at ${r12}.`);
    let h6 = o10.width, c11 = o10.height;
    if (o10 instanceof HTMLImageElement) {
      "esriPMS" === e13.type && (h6 = Math.round(V2(o10.width, de(e13))), c11 = Math.round(o10.height * (h6 / o10.width)));
      const i9 = "cim" in e13 ? e13.cim.colorSubstitutions : void 0, { size: s12, sdf: r13, image: n16 } = this._rasterizer.rasterizeImageResource(h6, c11, o10, i9);
      return this._addItemToMosaic(t11, s12, { type: "static", data: n16 }, fe(e13), r13, false);
    }
    this._allowNonPowerOfTwo || (h6 = f2(o10.width + 2 * et) - 2 * et, c11 = f2(o10.height + 2 * et) - 2 * et), h6 === o10.width && c11 === o10.height || (o10 = F(o10, h6, c11));
    const u8 = e13.animatedSymbolProperties || {}, d7 = e13.objectId, m5 = new e10(o10, this._requestRender, u8, d7);
    return this._addItemToMosaic(t11, [m5.width, m5.height], { type: "animated", data: m5 }, fe(e13), false, false);
  }
  async _handleImage(e13, t11, i8) {
    if (ie(e13) || oe(e13)) return this._handleGIFOrPNG(e13, t11, i8);
    const r12 = se(e13);
    try {
      let s12;
      const o10 = this.resourceManager.getResource(r12);
      if (r(o10) && o10 instanceof HTMLImageElement) s12 = o10;
      else {
        const { data: e14 } = await this._imageRequestQueue.push(r12, { ...i8 });
        s12 = e14;
      }
      if (me(r12)) {
        if ("width" in e13 && "height" in e13) s12.width = u(e13.width), s12.height = u(e13.height);
        else if ("cim" in e13) {
          const t12 = e13.cim;
          s12.width = u(t12.width ?? t12.scaleX * t12.size), s12.height = u(t12.size);
        }
      }
      if (!s12.width || !s12.height) return null;
      let n16 = s12.width, a13 = s12.height;
      "esriPMS" === e13.type && (n16 = Math.round(V2(s12.width, de(e13))), a13 = Math.round(s12.height * (n16 / s12.width)));
      const c11 = "cim" in e13 ? e13.cim.colorSubstitutions : void 0, { size: u8, sdf: m5, image: l10 } = this._rasterizer.rasterizeImageResource(n16, a13, s12, c11);
      return this._addItemToMosaic(t11, u8, { type: "static", data: l10 }, fe(e13), m5, false);
    } catch (o10) {
      if (!j(o10)) return new s3("mapview-invalid-resource", `Could not fetch requested resource at ${r12}. ${o10.message}`);
    }
  }
  _addItemToMosaic(e13, t11, i8, s12, r12, o10, n16) {
    return this._spriteMosaic.addSpriteItem(e13, t11, i8, s12, r12, o10, n16);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/shaders/StencilPrograms.js
var r6 = { shaders: { vertexShader: n8("stencil/stencil.vert"), fragmentShader: n8("stencil/stencil.frag") }, attributes: /* @__PURE__ */ new Map([["a_pos", 0]]) };

// node_modules/@arcgis/core/views/2d/engine/webgl/shaders/BlendPrograms.js
var r7 = (e13) => e13.replace("-", "_").toUpperCase();
var t6 = (e13) => `#define ${r7(e13)}
`;
function n13(r12) {
  return { attributes: /* @__PURE__ */ new Map([["a_pos", 0], ["a_tex", 1]]), shaders: { vertexShader: t6(r12) + n8("blend/blend.vert"), fragmentShader: t6(r12) + n8("blend/blend.frag") } };
}

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/BlendEffect.js
var l4 = s2.getLogger("esri.views.2d.engine.webgl.effects.blendEffects.BlendEffect");
var _2 = class {
  constructor() {
    this._size = [0, 0];
  }
  dispose(e13) {
    this._backBufferTexture = h(this._backBufferTexture), this._quad = h(this._quad);
  }
  draw(r12, t11, s12, a13, d7) {
    const { context: u8, drawPhase: f7 } = r12;
    if (this._setupShader(u8), a13 && "normal" !== a13 && f7 !== T.LABEL) return void this._drawBlended(r12, t11, s12, a13, d7);
    const c11 = n13("normal"), m5 = u8.programCache.acquire(c11.shaders.vertexShader, c11.shaders.fragmentShader, c11.attributes);
    if (!m5) return void l4.error(new s3("mapview-BlendEffect", 'Error creating shader program for blend mode "normal"'));
    u8.useProgram(m5), t11.setSamplingMode(s12), u8.bindTexture(t11, 0), m5.setUniform1i("u_layerTexture", 0), m5.setUniform1f("u_opacity", d7), u8.setBlendingEnabled(true), u8.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA);
    const h6 = this._quad;
    h6.draw(), h6.unbind(), m5.dispose();
  }
  _drawBlended(r12, t11, i8, a13, d7) {
    const { context: u8, state: f7, pixelRatio: c11, inFadeTransition: m5 } = r12, { size: h6 } = f7, _5 = u8.getBoundFramebufferObject();
    let p4, b3;
    if (r(_5)) {
      const e13 = _5.descriptor;
      p4 = e13.width, b3 = e13.height;
    } else p4 = Math.round(c11 * h6[0]), b3 = Math.round(c11 * h6[1]);
    this._createOrResizeTexture(r12, p4, b3);
    const g7 = this._backBufferTexture;
    _5.copyToTexture(0, 0, p4, b3, 0, 0, g7), u8.setStencilTestEnabled(false), u8.setStencilWriteMask(0), u8.setBlendingEnabled(true), u8.setDepthTestEnabled(false), u8.setDepthWriteEnabled(false);
    const x = n13(a13), T4 = u8.programCache.acquire(x.shaders.vertexShader, x.shaders.fragmentShader, x.attributes);
    if (!T4) return void l4.error(new s3("mapview-BlendEffect", `Error creating shader program for blend mode ${a13}`));
    u8.useProgram(T4), g7.setSamplingMode(i8), u8.bindTexture(g7, 0), T4.setUniform1i("u_backbufferTexture", 0), t11.setSamplingMode(i8), u8.bindTexture(t11, 1), T4.setUniform1i("u_layerTexture", 1), T4.setUniform1f("u_opacity", d7), T4.setUniform1f("u_inFadeOpacity", m5 ? 1 : 0), u8.setBlendFunction(R.ONE, R.ZERO);
    const E2 = this._quad;
    E2.draw(), E2.unbind(), T4.dispose(), u8.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA);
  }
  _setupShader(e13) {
    this._quad || (this._quad = new n7(e13, [-1, -1, 1, -1, -1, 1, 1, 1]));
  }
  _createOrResizeTexture(e13, r12, t11) {
    const { context: s12 } = e13;
    null !== this._backBufferTexture && r12 === this._size[0] && t11 === this._size[1] || (this._backBufferTexture ? this._backBufferTexture.resize(r12, t11) : this._backBufferTexture = new E(s12, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: r12, height: t11 }), this._size[0] = r12, this._size[1] = t11);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/shaders/HighlightPrograms.js
var t7 = { shaders: { vertexShader: n8("highlight/textured.vert"), fragmentShader: n8("highlight/highlight.frag") }, attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };
var r8 = { shaders: { vertexShader: n8("highlight/textured.vert"), fragmentShader: n8("highlight/blur.frag") }, attributes: /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]) };

// node_modules/@arcgis/core/views/2d/engine/webgl/Profiler.js
var n14 = has("esri-2d-profiler");
var i5 = class {
  constructor(s12, i8) {
    if (this._events = new n2(), this._entries = /* @__PURE__ */ new Map(), this._timings = new s6(10), this._currentContainer = null, this._currentPass = null, this._currentBrush = null, this._currentSummary = null, !n14) return;
    this._ext = T2(s12.gl, {}), this._debugOutput = i8;
    const o10 = s12.gl;
    if (this.enableCommandLogging) {
      for (const e13 in o10) if ("function" == typeof o10[e13]) {
        const t11 = o10[e13], s13 = e13.includes("draw");
        o10[e13] = (...r12) => (this._events.emit("command", { container: this._currentContainer, pass: this._currentPass, brush: this._currentBrush, method: e13, args: r12, isDrawCommand: s13 }), this._currentSummary && (this._currentSummary.commands++, s13 && this._currentSummary.drawCommands++), t11.apply(o10, r12));
      }
    }
  }
  get enableCommandLogging() {
    return !("object" == typeof n14 && n14.disableCommands);
  }
  recordContainerStart(e13) {
    n14 && (this._currentContainer = e13);
  }
  recordContainerEnd() {
    n14 && (this._currentContainer = null);
  }
  recordPassStart(e13) {
    n14 && (this._currentPass = e13, this._initSummary());
  }
  recordPassEnd() {
    n14 && (this._currentPass = null, this._emitSummary());
  }
  recordBrushStart(e13) {
    n14 && (this._currentBrush = e13);
  }
  recordBrushEnd() {
    n14 && (this._currentBrush = null);
  }
  recordStart(e13) {
    if (n14 && r(this._ext)) {
      if (this._entries.has(e13)) {
        const t12 = this._entries.get(e13), r12 = this._ext.resultAvailable(t12.query), n16 = this._ext.disjoint();
        if (r12 && !n16) {
          const r13 = this._ext.getResult(t12.query) / 1e6;
          let n17 = 0;
          if (r(this._timings.enqueue(r13))) {
            const e14 = this._timings.entries, t13 = e14.length;
            let s12 = 0;
            for (const r14 of e14) s12 += r14;
            n17 = s12 / t13;
          }
          const i8 = r13.toFixed(2), o10 = n17 ? n17.toFixed(2) : "--";
          this.enableCommandLogging ? (console.groupCollapsed(`Frame report for ${e13}, ${i8} ms (${o10} last 10 avg)
${t12.commandsLen} Commands (${t12.drawCommands} draw)`), console.log("RenderPass breakdown: "), console.table(t12.summaries), console.log("Commands: ", t12.commands), console.groupEnd()) : console.log(`Frame report for ${e13}, ${i8} ms (${o10} last 10 avg)`), this._debugOutput.innerHTML = `${i8} (${o10})`;
        }
        for (const e14 of t12.handles) e14.remove();
        this._ext.deleteQuery(t12.query), this._entries.delete(e13);
      }
      const t11 = { name: e13, query: this._ext.createQuery(), commands: [], commandsLen: 0, drawCommands: 0, summaries: [], handles: [] };
      this.enableCommandLogging && (t11.handles.push(this._events.on("command", (e14) => {
        t11.commandsLen++, t11.commands.push(e14), e14.isDrawCommand && t11.drawCommands++;
      })), t11.handles.push(this._events.on("summary", (e14) => {
        t11.summaries.push(e14);
      }))), this._ext.beginTimeElapsed(t11.query), this._entries.set(e13, t11);
    }
  }
  recordEnd(e13) {
    n14 && r(this._ext) && this._entries.has(e13) && this._ext.endTimeElapsed();
  }
  _initSummary() {
    this.enableCommandLogging && (this._currentSummary = { container: this._currentContainer, pass: this._currentPass, drawCommands: 0, commands: 0 });
  }
  _emitSummary() {
    this.enableCommandLogging && this._currentSummary && this._events.emit("summary", this._currentSummary);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/collisions/CollisionGrid.js
var e12 = 2;
var i6 = 1;
var o8 = 0;
var h3 = 1;
var r9 = 2;
var l5 = class {
  constructor(t11, e13, o10) {
    this._debugMap = /* @__PURE__ */ new Map(), this._width = t11 * o10, this._height = e13 * o10, this._pixelRatio = o10;
    const h6 = Math.ceil(this._width / i6), r12 = Math.ceil(this._height / i6);
    this._cols = h6, this._rows = r12, this._cells = t3.create(h6 * r12);
  }
  insertMetrics(t11) {
    const s12 = this._hasCollision(t11);
    return s12 === o8 && this._markMetrics(t11), s12;
  }
  getCellId(t11, s12) {
    return t11 + s12 * this._cols;
  }
  has(t11) {
    return this._cells.has(t11);
  }
  hasRange(t11, s12) {
    return this._cells.hasRange(t11, s12);
  }
  set(t11) {
    this._cells.set(t11);
  }
  setRange(t11, s12) {
    this._cells.setRange(t11, s12);
  }
  _collide(s12, e13, l10, n16) {
    const c11 = s12 - l10 / 2, a13 = e13 - n16 / 2, _5 = c11 + l10, d7 = a13 + n16;
    if (_5 < 0 || d7 < 0 || c11 > this._width || a13 > this._height) return h3;
    const u8 = a4(Math.floor(c11 / i6), 0, this._cols), p4 = a4(Math.floor(a13 / i6), 0, this._rows), M3 = a4(Math.ceil(_5 / i6), 0, this._cols), f7 = a4(Math.ceil(d7 / i6), 0, this._rows);
    for (let t11 = p4; t11 <= f7; t11++) for (let s13 = u8; s13 <= M3; s13++) {
      const e14 = this.getCellId(s13, t11);
      if (this.has(e14)) return r9;
    }
    return o8;
  }
  _mark(s12, e13, o10, h6, r12) {
    const l10 = s12 - o10 / 2, n16 = e13 - h6 / 2, c11 = l10 + o10, a13 = n16 + h6, _5 = a4(Math.floor(l10 / i6), 0, this._cols), d7 = a4(Math.floor(n16 / i6), 0, this._rows), u8 = a4(Math.ceil(c11 / i6), 0, this._cols), p4 = a4(Math.ceil(a13 / i6), 0, this._rows);
    for (let t11 = d7; t11 <= p4; t11++) for (let s13 = _5; s13 <= u8; s13++) {
      const e14 = this.getCellId(s13, t11);
      this._debugMap.set(e14, r12), this.set(e14);
    }
    return false;
  }
  _hasCollision(t11) {
    const s12 = t11.id;
    let i8 = 0, l10 = 0;
    t11.save();
    do {
      const s13 = t11.boundsCount;
      i8 += s13;
      for (let i9 = 0; i9 < s13; i9++) {
        const s14 = t11.boundsComputedAnchorX(i9), o10 = t11.boundsComputedAnchorY(i9), n16 = (t11.boundsWidth(i9) + e12) * this._pixelRatio, c11 = (t11.boundsHeight(i9) + e12) * this._pixelRatio;
        switch (this._collide(s14, o10, n16, c11)) {
          case r9:
            return r9;
          case h3:
            l10++;
        }
      }
    } while (t11.peekId() === s12 && t11.next());
    return t11.restore(), i8 === l10 ? h3 : o8;
  }
  _markMetrics(t11) {
    const s12 = t11.id;
    t11.save();
    do {
      const s13 = t11.boundsCount;
      for (let i8 = 0; i8 < s13; i8++) {
        const s14 = t11.boundsComputedAnchorX(i8), o10 = t11.boundsComputedAnchorY(i8), h6 = (t11.boundsWidth(i8) + e12) * this._pixelRatio, r12 = (t11.boundsHeight(i8) + e12) * this._pixelRatio;
        this._mark(s14, o10, h6, r12, t11.id);
      }
    } while (t11.peekId() === s12 && t11.next());
    t11.restore();
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/collisions/visualVariableSimpleUtils.js
var r10 = Math.PI;
function i7(e13, t11) {
  switch (t11.transformationType) {
    case i3.Additive:
      return s9(e13, t11);
    case i3.Constant:
      return o9(t11, e13);
    case i3.ClampedLinear:
      return u4(e13, t11);
    case i3.Proportional:
      return l6(e13, t11);
    case i3.Stops:
      return c7(e13, t11);
    case i3.RealWorldSize:
      return m3(e13, t11);
    case i3.Identity:
      return e13;
    case i3.Unknown:
      return null;
  }
}
function a9(e13, t11) {
  return "number" == typeof e13 ? e13 : i7(t11, e13);
}
function s9(e13, t11) {
  return e13 + (a9(t11.minSize, e13) || t11.minDataValue);
}
function o9(e13, t11) {
  const n16 = e13.stops;
  let r12 = n16 && n16.length && n16[0].size;
  return null == r12 && (r12 = e13.minSize), a9(r12, t11);
}
function u4(e13, t11) {
  const n16 = t11.minDataValue, r12 = t11.maxDataValue, i8 = (e13 - n16) / (r12 - n16), s12 = a9(t11.minSize, e13), o10 = a9(t11.maxSize, e13);
  return e13 <= n16 ? s12 : e13 >= r12 ? o10 : s12 + i8 * (o10 - s12);
}
function l6(t11, n16) {
  const r12 = t11 / n16.minDataValue, i8 = a9(n16.minSize, t11), s12 = a9(n16.maxSize, t11);
  let o10 = null;
  return o10 = r12 * i8, a4(o10, i8, s12);
}
function c7(e13, t11) {
  const [n16, r12, i8] = p2(e13, t11.cache.ipData);
  if (n16 === r12) return a9(t11.stops[n16].size, e13);
  {
    const s12 = a9(t11.stops[n16].size, e13);
    return s12 + (a9(t11.stops[r12].size, e13) - s12) * i8;
  }
}
function m3(n16, i8) {
  const s12 = m[i8.valueUnit], o10 = a9(i8.minSize, n16), u8 = a9(i8.maxSize, n16), { valueRepresentation: l10 } = i8;
  let c11 = null;
  return c11 = "area" === l10 ? 2 * Math.sqrt(n16 / r10) / s12 : "radius" === l10 || "distance" === l10 ? 2 * n16 / s12 : n16 / s12, a4(c11, o10, u8);
}
function p2(e13, t11) {
  if (!t11) return;
  let n16 = 0, r12 = t11.length - 1;
  return t11.some((t12, i8) => e13 < t12 ? (r12 = i8, true) : (n16 = i8, false)), [n16, r12, (e13 - t11[n16]) / (t11[r12] - t11[n16])];
}

// node_modules/@arcgis/core/views/2d/engine/webgl/collisions/CollisionEngine.js
var c8 = 254;
var u5 = 255;
var f5 = 0;
function b2(e13, i8) {
  const o10 = [];
  e13.forEachTile((e14) => o10.push(e14)), o10.sort((e14, t11) => e14.instanceId - t11.instanceId), o10.forEach((e14) => {
    r(e14.labelMetrics) && e14.isReady && i8(e14, e14.labelMetrics.getCursor());
  });
}
function y3(e13) {
  return e13.layer && ("feature" === e13.layer.type || "csv" === e13.layer.type || "geojson" === e13.layer.type || "ogc-feature" === e13.layer.type || "stream" === e13.layer.type || "subtype-group" === e13.layer.type || "wfs" === e13.layer.type);
}
function p3(e13) {
  return (t11) => u(i7(t11, e13));
}
function m4(e13) {
  const t11 = null != e13 && "visualVariables" in e13 && e13.visualVariables;
  if (!t11) return null;
  for (const i8 of t11) if ("size" === i8.type) return p3(i8);
  return null;
}
function h4(e13) {
  for (const t11 of e13) {
    const e14 = "featureReduction" in t11 && t11.featureReduction && "labelingInfo" in t11.featureReduction ? t11.featureReduction : void 0, i8 = [...t11.labelingInfo || [], ...(e14 == null ? void 0 : e14.labelingInfo) || []];
    if (!t11.labelsVisible || !i8.length) continue;
    if (i8.some((e15) => "none" === e15.deconflictionStrategy)) return true;
  }
  return false;
}
function M2(t11, i8) {
  var _a;
  if (!y3(i8)) return;
  const o10 = "subtype-group" === i8.layer.type ? i8.layer.sublayers.items : [i8.layer], r12 = i8.layer.geometryType, n16 = !h4(o10), s12 = {};
  if ("subtype-group" !== i8.layer.type) {
    if ("heatmap" === ((_a = i8.tileRenderer) == null ? void 0 : _a.type)) return;
    const e13 = m4(i8.layer.renderer);
    s12[0] = e13;
  }
  const l10 = i8.tileRenderer;
  if (t(l10)) return;
  const a13 = i8.layer.visible && !i8.suspended;
  t11.push({ tileRenderer: l10, vvEvaluators: s12, deconflictionEnabled: n16, geometryType: r12, visible: a13 });
}
var g5 = class {
  run(e13, t11, i8) {
    const o10 = [];
    for (let r12 = e13.length - 1; r12 >= 0; r12--) {
      M2(o10, e13[r12]);
    }
    this._transformMetrics(o10), this._runCollision(o10, t11, i8);
  }
  _runCollision(e13, t11, i8) {
    const [o10, r12] = t11.state.size, s12 = new l5(o10, r12, t11.pixelRatio);
    for (const { tileRenderer: n16, deconflictionEnabled: l10, visible: a13 } of e13) {
      const e14 = n16.featuresView.attributeView;
      l10 ? a13 ? (this._prepare(n16), this._collideVisible(s12, n16, i8), this._collideInvisible(s12, n16)) : b2(n16, (t12, i9) => {
        for (; i9.nextId(); ) e14.setLabelMinZoom(i9.id, u5);
      }) : b2(n16, (t12, i9) => {
        for (; i9.nextId(); ) e14.setLabelMinZoom(i9.id, f5), a13 && s12.insertMetrics(i9);
      });
    }
  }
  _isFiltered(t11, i8, n16) {
    const s12 = i8.getFilterFlags(t11), l10 = !n16.hasFilter || !!(s12 & U2), a13 = t(n16.featureEffect) || n16.featureEffect.excludedLabelsVisible || !!(s12 & V);
    return !(l10 && a13);
  }
  _prepare(e13) {
    const t11 = e13.featuresView.attributeView, i8 = /* @__PURE__ */ new Set();
    b2(e13, (o10, r12) => {
      for (; r12.nextId(); ) {
        if (i8.has(r12.id)) continue;
        if (i8.add(r12.id), this._isFiltered(r12.id, t11, e13.layerView)) {
          t11.setLabelMinZoom(r12.id, c8);
          continue;
        }
        t11.getLabelMinZoom(r12.id) !== f5 ? t11.setLabelMinZoom(r12.id, u5) : t11.setLabelMinZoom(r12.id, f5);
      }
    });
  }
  _collideVisible(e13, t11, i8) {
    const o10 = t11.featuresView.attributeView, r12 = /* @__PURE__ */ new Set();
    b2(t11, (t12, n16) => {
      for (; n16.nextId(); ) if (!r12.has(n16.id)) if (t12.key.level === i8) {
        if (0 === o10.getLabelMinZoom(n16.id)) {
          switch (e13.insertMetrics(n16)) {
            case h3:
              break;
            case r9:
              o10.setLabelMinZoom(n16.id, c8), r12.add(n16.id);
              break;
            case o8:
              o10.setLabelMinZoom(n16.id, f5), r12.add(n16.id);
          }
        }
      } else o10.setLabelMinZoom(n16.id, c8);
    });
  }
  _collideInvisible(e13, t11) {
    const i8 = t11.featuresView.attributeView, o10 = /* @__PURE__ */ new Set();
    b2(t11, (t12, r12) => {
      for (; r12.nextId(); ) if (!o10.has(r12.id) && i8.getLabelMinZoom(r12.id) === u5) {
        switch (e13.insertMetrics(r12)) {
          case h3:
            break;
          case r9:
            i8.setLabelMinZoom(r12.id, u5), o10.add(r12.id);
            break;
          case o8:
            i8.setLabelMinZoom(r12.id, f5), o10.add(r12.id);
        }
      }
    });
  }
  _transformMetrics(e13) {
    for (const { tileRenderer: i8, geometryType: o10, vvEvaluators: r12 } of e13) b2(i8, (e14, n16) => {
      const s12 = i8.featuresView.attributeView, l10 = e14.transforms.labelMat2d;
      l10[4] = Math.round(l10[4]), l10[5] = Math.round(l10[5]);
      const a13 = "polyline" === o10;
      for (; n16.next(); ) {
        const e15 = n16.boundsCount, i9 = n16.anchorX, o11 = n16.anchorY;
        let d7 = n16.size;
        const c11 = r12[0];
        if (r(c11)) {
          const e16 = c11(s12.getVVSize(n16.id));
          d7 = isNaN(e16) || null == e16 || e16 === 1 / 0 ? d7 : e16;
        }
        const u8 = n16.directionX * (d7 / 2), f7 = n16.directionY * (d7 / 2);
        for (let t11 = 0; t11 < e15; t11++) {
          let e16 = i9, r13 = n16.anchorY;
          if (a13) {
            let i10 = e16 + n16.boundsX(t11) + u8, o12 = r13 + n16.boundsY(t11) + f7;
            i10 = l10[0] * i10 + l10[2] * o12 + l10[4], o12 = l10[1] * i10 + l10[3] * o12 + l10[5], n16.setBoundsComputedAnchorX(t11, Math.floor(i10)), n16.setBoundsComputedAnchorY(t11, Math.floor(o12));
          } else {
            e16 = l10[0] * i9 + l10[2] * o11 + l10[4], r13 = l10[1] * i9 + l10[3] * o11 + l10[5];
            const s13 = e16 + n16.boundsX(t11) + u8, a14 = r13 + n16.boundsY(t11) + f7;
            n16.setBoundsComputedAnchorX(t11, s13), n16.setBoundsComputedAnchorY(t11, a14);
          }
        }
      }
    });
  }
};

// node_modules/@arcgis/core/views/2d/LabelManager.js
var l7 = 32;
var d2 = class extends a6(v2) {
  constructor(e13) {
    super(e13), this._applyVisibilityPassThrottled = e5(this._applyVisibilityPass, l7, this), this.lastUpdateId = -1, this.updateRequested = false, this.view = null;
  }
  initialize() {
    this.collisionEngine = new g5();
  }
  destroy() {
    this.collisionEngine = null, this._applyVisibilityPassThrottled = p(this._applyVisibilityPassThrottled);
  }
  get updating() {
    return has("esri-2d-log-updating") && console.log(`Updating LabelManager ${this.updateRequested}:
-> updateRequested: ${this.updateRequested}`), this.updateRequested;
  }
  update(e13) {
    this._applyVisibilityPassThrottled(e13);
  }
  viewChange() {
    this.requestUpdate();
  }
  requestUpdate() {
    var _a;
    this.updateRequested || (this.updateRequested = true, (_a = this.view) == null ? void 0 : _a.requestUpdate());
  }
  processUpdate(e13) {
    this._set("updateParameters", e13), this.updateRequested && (this.updateRequested = false, this.update(e13));
  }
  _applyVisibilityPass(e13) {
    const t11 = this.view;
    if (t11) try {
      const s12 = t11.featuresTilingScheme.getClosestInfoForScale(e13.state.scale).level;
      this.collisionEngine.run(t11.allLayerViews.items, e13, s12);
    } catch (s12) {
    }
  }
};
e([y()], d2.prototype, "updateRequested", void 0), e([y({ readOnly: true })], d2.prototype, "updateParameters", void 0), e([y()], d2.prototype, "updating", null), e([y()], d2.prototype, "view", void 0), d2 = e([a2("esri.views.2d.layers.labels.LabelManager")], d2);
var u6 = d2;

// node_modules/@arcgis/core/views/2d/navigation/ZoomBox.js
var a10 = "esri-zoom-box";
var n15 = { container: `${a10}__container`, overlay: `${a10}__overlay`, background: `${a10}__overlay-background`, box: `${a10}__outline` };
var h5 = { zoom: "Shift", counter: "Ctrl" };
var l8 = class extends v2 {
  constructor(t11) {
    super(t11), this._container = null, this._overlay = null, this._backgroundShape = null, this._boxShape = null, this._box = { x: 0, y: 0, width: 0, height: 0 }, this._rafId = null, this._handles = null, this._redraw = this._redraw.bind(this);
  }
  destroy() {
    this.view = null;
  }
  set view(t11) {
    this._handles && this._handles.forEach((t12) => {
      t12.remove();
    }), this._handles = null, this._destroyOverlay(), this._set("view", t11), t11 && (t11.on("drag", [h5.zoom], (t12) => this._handleDrag(t12, 1), P2.INTERNAL), t11.on("drag", [h5.zoom, h5.counter], (t12) => this._handleDrag(t12, -1), P2.INTERNAL));
  }
  _start() {
    this._createContainer(), this._createOverlay(), this.navigation.begin();
  }
  _update(t11, e13, i8, r12) {
    this._box.x = t11, this._box.y = e13, this._box.width = i8, this._box.height = r12, this._rafId || (this._rafId = requestAnimationFrame(this._redraw));
  }
  _end(t11, e13, r12, s12, o10) {
    const a13 = this.view, n16 = a13.toMap(c(t11 + 0.5 * r12, e13 + 0.5 * s12));
    let h6 = Math.max(r12 / a13.width, s12 / a13.height);
    -1 === o10 && (h6 = 1 / h6), this._destroyOverlay(), this.navigation.end(), a13.goTo({ center: n16, scale: a13.scale * h6 });
  }
  _updateBox(t11, e13, i8, r12) {
    const s12 = this._boxShape;
    s12.setAttributeNS(null, "x", "" + t11), s12.setAttributeNS(null, "y", "" + e13), s12.setAttributeNS(null, "width", "" + i8), s12.setAttributeNS(null, "height", "" + r12), s12.setAttributeNS(null, "class", n15.box);
  }
  _updateBackground(t11, e13, i8, r12) {
    this._backgroundShape.setAttributeNS(null, "d", this._toSVGPath(t11, e13, i8, r12, this.view.width, this.view.height));
  }
  _createContainer() {
    const t11 = document.createElement("div");
    t11.className = n15.container, this.view.root.appendChild(t11), this._container = t11;
  }
  _createOverlay() {
    const t11 = this.view.width, e13 = this.view.height, i8 = document.createElementNS("http://www.w3.org/2000/svg", "path");
    i8.setAttributeNS(null, "d", "M 0 0 L " + t11 + " 0 L " + t11 + " " + e13 + " L 0 " + e13 + " Z"), i8.setAttributeNS(null, "class", n15.background);
    const r12 = document.createElementNS("http://www.w3.org/2000/svg", "rect"), s12 = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    s12.setAttributeNS("http://www.w3.org/2000/xmlns/", "xmlns:xlink", "http://www.w3.org/1999/xlink"), s12.setAttributeNS(null, "class", n15.overlay), s12.appendChild(i8), s12.appendChild(r12), this._container.appendChild(s12), this._backgroundShape = i8, this._boxShape = r12, this._overlay = s12;
  }
  _destroyOverlay() {
    this._container && this._container.parentNode && this._container.parentNode.removeChild(this._container), this._container = this._backgroundShape = this._boxShape = this._overlay = null;
  }
  _toSVGPath(t11, e13, i8, r12, s12, o10) {
    const a13 = t11 + i8, n16 = e13 + r12;
    return "M 0 0 L " + s12 + " 0 L " + s12 + " " + o10 + " L 0 " + o10 + " ZM " + t11 + " " + e13 + " L " + t11 + " " + n16 + " L " + a13 + " " + n16 + " L " + a13 + " " + e13 + " Z";
  }
  _handleDrag(t11, e13) {
    const i8 = t11.x, r12 = t11.y, s12 = t11.origin.x, o10 = t11.origin.y;
    let a13, n16, h6, l10;
    switch (i8 > s12 ? (a13 = s12, h6 = i8 - s12) : (a13 = i8, h6 = s12 - i8), r12 > o10 ? (n16 = o10, l10 = r12 - o10) : (n16 = r12, l10 = o10 - r12), t11.action) {
      case "start":
        this._start();
        break;
      case "update":
        this._update(a13, n16, h6, l10);
        break;
      case "end":
        this._end(a13, n16, h6, l10, e13);
    }
    t11.stopPropagation();
  }
  _redraw() {
    if (!this._rafId) return;
    if (this._rafId = null, !this._overlay) return;
    const { x: t11, y: e13, width: i8, height: r12 } = this._box;
    this._updateBox(t11, e13, i8, r12), this._updateBackground(t11, e13, i8, r12), this._rafId = requestAnimationFrame(this._redraw);
  }
};
e([y()], l8.prototype, "navigation", void 0), e([y()], l8.prototype, "view", null), l8 = e([a2("esri.views.2d.navigation.ZoomBox")], l8);
var c9 = l8;

// node_modules/@arcgis/core/views/navigation/FilteredFiniteDifference.js
var t8 = class {
  constructor(t11) {
    this._gain = t11, this.lastValue = void 0, this.filteredDelta = void 0;
  }
  update(t11) {
    if (this.hasLastValue()) {
      const e13 = this.computeDelta(t11);
      this._updateDelta(e13);
    }
    this.lastValue = t11;
  }
  reset() {
    this.lastValue = void 0, this.filteredDelta = void 0;
  }
  hasLastValue() {
    return void 0 !== this.lastValue;
  }
  hasFilteredDelta() {
    return void 0 !== this.filteredDelta;
  }
  computeDelta(t11) {
    return void 0 === this.lastValue ? NaN : t11 - this.lastValue;
  }
  _updateDelta(t11) {
    void 0 !== this.filteredDelta ? this.filteredDelta = (1 - this._gain) * this.filteredDelta + this._gain * t11 : this.filteredDelta = t11;
  }
};

// node_modules/@arcgis/core/views/navigation/Momentum.js
var t9 = class {
  constructor(t11, i8, o10) {
    this._initialVelocity = t11, this._stopVelocity = i8, this._friction = o10, this._duration = Math.abs(Math.log(Math.abs(this._initialVelocity) / this._stopVelocity) / Math.log(1 - this._friction));
  }
  get duration() {
    return this._duration;
  }
  isFinished(t11) {
    return t11 > this.duration;
  }
  get friction() {
    return this._friction;
  }
  value(t11) {
    return this.valueFromInitialVelocity(this._initialVelocity, t11);
  }
  valueDelta(t11, i8) {
    const o10 = this.value(t11);
    return this.value(t11 + i8) - o10;
  }
  valueFromInitialVelocity(t11, i8) {
    i8 = Math.min(i8, this.duration);
    const o10 = 1 - this.friction;
    return t11 * (o10 ** i8 - 1) / Math.log(o10);
  }
};

// node_modules/@arcgis/core/views/navigation/PanPlanarMomentumEstimator.js
var c10 = class extends t9 {
  constructor(e13, t11, i8, s12, n16) {
    super(e13, t11, i8), this._sceneVelocity = s12, this.direction = n16;
  }
  value(e13) {
    return super.valueFromInitialVelocity(this._sceneVelocity, e13);
  }
};
var l9 = class {
  constructor(e13 = 300, t11 = 12, i8 = 0.84) {
    this._minimumInitialVelocity = e13, this._stopVelocity = t11, this._friction = i8, this.enabled = true, this._time = new t8(0.6), this._screen = [new t8(0.4), new t8(0.4)], this._scene = [new t8(0.6), new t8(0.6), new t8(0.6)], this._tmpDirection = n3();
  }
  add(e13, t11, i8) {
    if (this.enabled) {
      if (this._time.hasLastValue()) {
        if (this._time.computeDelta(i8) < 0.015) return;
      }
      this._screen[0].update(e13[0]), this._screen[1].update(e13[1]), this._scene[0].update(t11[0]), this._scene[1].update(t11[1]), this._scene[2].update(t11[2]), this._time.update(i8);
    }
  }
  reset() {
    this._screen[0].reset(), this._screen[1].reset(), this._scene[0].reset(), this._scene[1].reset(), this._scene[2].reset(), this._time.reset();
  }
  evaluateMomentum() {
    if (!this.enabled || !this._screen[0].hasFilteredDelta() || !this._time.hasFilteredDelta()) return null;
    const e13 = this._screen[0].filteredDelta, t11 = this._screen[1].filteredDelta, i8 = null == e13 || null == t11 ? 0 : Math.sqrt(e13 * e13 + t11 * t11), s12 = this._time.filteredDelta, n16 = null == s12 || null == i8 ? 0 : i8 / s12;
    return Math.abs(n16) < this._minimumInitialVelocity ? null : this.createMomentum(n16, this._stopVelocity, this._friction);
  }
  createMomentum(s12, n16, r12) {
    o(this._tmpDirection, this._scene[0].filteredDelta ?? 0, this._scene[1].filteredDelta ?? 0, this._scene[2].filteredDelta ?? 0);
    const l10 = s4(this._tmpDirection);
    l10 > 0 && g(this._tmpDirection, this._tmpDirection, 1 / l10);
    const h6 = this._time.filteredDelta;
    return new c10(s12, n16, r12, null == h6 ? 0 : l10 / h6, this._tmpDirection);
  }
};

// node_modules/@arcgis/core/views/2d/navigation/actions/Pan.js
var v3 = class extends v2 {
  constructor(t11) {
    super(t11), this.animationTime = 0, this.momentumEstimator = new l9(500, 6, 0.92), this.momentum = null, this.tmpMomentum = n3(), this.momentumFinished = false, this.viewpoint = new u2({ targetGeometry: new w(), scale: 0, rotation: 0 }), this._previousDrag = null, f3(() => this.momentumFinished, () => this.navigation.stop());
  }
  begin(t11, i8) {
    this.navigation.begin(), this.momentumEstimator.reset(), this.addToEstimator(i8), this._previousDrag = i8;
  }
  update(t11, i8) {
    this.addToEstimator(i8);
    let o10 = i8.center.x, e13 = i8.center.y;
    const s12 = this._previousDrag;
    o10 = s12 ? s12.center.x - o10 : -o10, e13 = s12 ? e13 - s12.center.y : e13, t11.viewpoint = St(this.viewpoint, t11.viewpoint, [o10 || 0, e13 || 0]), this._previousDrag = i8;
  }
  end(t11, i8) {
    this.addToEstimator(i8);
    const o10 = t11.navigation.momentumEnabled;
    this.momentum = o10 ? this.momentumEstimator.evaluateMomentum() : null, this.animationTime = 0, this.momentum && this.onAnimationUpdate(t11), this._previousDrag = null, this.navigation.end();
  }
  addToEstimator(t11) {
    const i8 = t11.center.x, o10 = t11.center.y, e13 = i(-i8, o10), n16 = r2(-i8, o10, 0);
    this.momentumEstimator.add(e13, n16, 1e-3 * t11.timestamp);
  }
  onAnimationUpdate(t11) {
    var _a;
    (_a = this.navigation.animationManager) == null ? void 0 : _a.animateContinous(t11.viewpoint, (i8, o10) => {
      const { momentum: e13, animationTime: s12, tmpMomentum: n16 } = this, m5 = 1e-3 * o10;
      if (!(this.momentumFinished = !e13 || e13.isFinished(s12))) {
        const o11 = e13.valueDelta(s12, m5);
        g(n16, e13.direction, o11), St(i8, i8, n16), t11.constraints.constrainByGeometry(i8);
      }
      this.animationTime += m5;
    });
  }
  stopMomentumNavigation() {
    this.momentum && (this.momentumEstimator.reset(), this.momentum = null, this.navigation.stop());
  }
};
e([y()], v3.prototype, "momentumFinished", void 0), e([y()], v3.prototype, "viewpoint", void 0), e([y()], v3.prototype, "navigation", void 0), v3 = e([a2("esri.views.2d.navigation.actions.Pan")], v3);
var d3 = v3;

// node_modules/@arcgis/core/views/navigation/MomentumEstimator.js
var s10 = class {
  constructor(t11 = 2.5, i8 = 0.01, s12 = 0.95, l10 = 12) {
    this._minimumInitialVelocity = t11, this._stopVelocity = i8, this._friction = s12, this._maxVelocity = l10, this.enabled = true, this.value = new t8(0.8), this.time = new t8(0.3);
  }
  add(t11, e13) {
    if (this.enabled && null != e13) {
      if (this.time.hasLastValue()) {
        if (this.time.computeDelta(e13) < 0.01) return;
        if (this.value.hasFilteredDelta()) {
          const e14 = this.value.computeDelta(t11);
          this.value.filteredDelta * e14 < 0 && this.value.reset();
        }
      }
      this.time.update(e13), this.value.update(t11);
    }
  }
  reset() {
    this.value.reset(), this.time.reset();
  }
  evaluateMomentum() {
    if (!this.enabled || !this.value.hasFilteredDelta() || !this.time.hasFilteredDelta()) return null;
    let e13 = this.value.filteredDelta / this.time.filteredDelta;
    return e13 = a4(e13, -this._maxVelocity, this._maxVelocity), Math.abs(e13) < this._minimumInitialVelocity ? null : this.createMomentum(e13, this._stopVelocity, this._friction);
  }
  createMomentum(t11, e13, s12) {
    return new t9(t11, e13, s12);
  }
};

// node_modules/@arcgis/core/views/navigation/RotationMomentumEstimator.js
var a11 = class extends s10 {
  constructor(t11 = 3, a13 = 0.01, s12 = 0.95, o10 = 12) {
    super(t11, a13, s12, o10);
  }
  add(t11, a13) {
    const s12 = this.value.lastValue;
    if (null != s12) {
      let a14 = t11 - s12;
      for (; a14 > Math.PI; ) a14 -= 2 * Math.PI;
      for (; a14 < -Math.PI; ) a14 += 2 * Math.PI;
      t11 = s12 + a14;
    }
    super.add(t11, a13);
  }
};

// node_modules/@arcgis/core/views/navigation/ZoomMomentumEstimator.js
var r11 = class extends t9 {
  constructor(e13, t11, r12) {
    super(e13, t11, r12);
  }
  value(e13) {
    const t11 = super.value(e13);
    return Math.exp(t11);
  }
  valueDelta(e13, t11) {
    const r12 = super.value(e13), s12 = super.value(e13 + t11) - r12;
    return Math.exp(s12);
  }
};
var s11 = class extends s10 {
  constructor(e13 = 2.5, t11 = 0.01, r12 = 0.95, s12 = 12) {
    super(e13, t11, r12, s12);
  }
  add(e13, t11) {
    super.add(Math.log(e13), t11);
  }
  createMomentum(e13, t11, s12) {
    return new r11(e13, t11, s12);
  }
};

// node_modules/@arcgis/core/views/2d/navigation/actions/Pinch.js
var _3 = class extends v2 {
  constructor(t11) {
    super(t11), this._animationTime = 0, this._momentumFinished = false, this._previousAngle = 0, this._previousRadius = 0, this._previousCenter = null, this._rotationMomentumEstimator = new a11(0.6, 0.15, 0.95), this._rotationDirection = 1, this._startAngle = 0, this._startRadius = 0, this._updateTimestamp = null, this._zoomDirection = 1, this._zoomMomentumEstimator = new s11(), this._zoomOnly = null, this.zoomMomentum = null, this.rotateMomentum = null, this.viewpoint = new u2({ targetGeometry: new w(), scale: 0, rotation: 0 }), this.addHandles(f3(() => this._momentumFinished, () => this.navigation.stop()));
  }
  begin(t11, o10) {
    this.navigation.begin(), this._rotationMomentumEstimator.reset(), this._zoomMomentumEstimator.reset(), this._zoomOnly = null, this._previousAngle = this._startAngle = o10.angle, this._previousRadius = this._startRadius = o10.radius, this._previousCenter = o10.center, this._updateTimestamp = null, t11.constraints.rotationEnabled && this.addToRotateEstimator(0, o10.timestamp), this.addToZoomEstimator(o10, 1);
  }
  update(t11, o10) {
    null === this._updateTimestamp && (this._updateTimestamp = o10.timestamp);
    const i8 = o10.angle, s12 = o10.radius, e13 = o10.center, n16 = Math.abs(180 * (i8 - this._startAngle) / Math.PI), m5 = Math.abs(s12 - this._startRadius), a13 = this._startRadius / s12;
    if (this._previousRadius && this._previousCenter) {
      const r12 = s12 / this._previousRadius;
      let h6 = 180 * (i8 - this._previousAngle) / Math.PI;
      this._rotationDirection = h6 >= 0 ? 1 : -1, this._zoomDirection = r12 >= 1 ? 1 : -1, t11.constraints.rotationEnabled ? (null === this._zoomOnly && o10.timestamp - this._updateTimestamp > 200 && (this._zoomOnly = m5 - n16 > 0), null === this._zoomOnly || this._zoomOnly ? h6 = 0 : this.addToRotateEstimator(i8 - this._startAngle, o10.timestamp)) : h6 = 0, this.addToZoomEstimator(o10, a13), this.navigation.setViewpoint([e13.x, e13.y], 1 / r12, h6, [this._previousCenter.x - e13.x, e13.y - this._previousCenter.y]);
    }
    this._previousAngle = i8, this._previousRadius = s12, this._previousCenter = e13;
  }
  end(t11) {
    this.rotateMomentum = this._rotationMomentumEstimator.evaluateMomentum(), this.zoomMomentum = this._zoomMomentumEstimator.evaluateMomentum(), this._animationTime = 0, (this.rotateMomentum || this.zoomMomentum) && this.onAnimationUpdate(t11), this.navigation.end();
  }
  addToRotateEstimator(t11, o10) {
    this._rotationMomentumEstimator.add(t11, 1e-3 * o10);
  }
  addToZoomEstimator(t11, o10) {
    this._zoomMomentumEstimator.add(o10, 1e-3 * t11.timestamp);
  }
  canZoomIn(t11) {
    const o10 = t11.scale, i8 = t11.constraints.effectiveMaxScale;
    return 0 === i8 || o10 > i8;
  }
  canZoomOut(t11) {
    const o10 = t11.scale, i8 = t11.constraints.effectiveMinScale;
    return 0 === i8 || o10 < i8;
  }
  onAnimationUpdate(t11) {
    var _a;
    (_a = this.navigation.animationManager) == null ? void 0 : _a.animateContinous(t11.viewpoint, (o10, i8) => {
      const s12 = !this.canZoomIn(t11) && this._zoomDirection > 1 || !this.canZoomOut(t11) && this._zoomDirection < 1, e13 = !this.rotateMomentum || this.rotateMomentum.isFinished(this._animationTime), n16 = s12 || !this.zoomMomentum || this.zoomMomentum.isFinished(this._animationTime), p4 = 1e-3 * i8;
      if (this._momentumFinished = e13 && n16, !this._momentumFinished) {
        const i9 = this.rotateMomentum ? Math.abs(this.rotateMomentum.valueDelta(this._animationTime, p4)) * this._rotationDirection * 180 / Math.PI : 0;
        let s13 = this.zoomMomentum ? Math.abs(this.zoomMomentum.valueDelta(this._animationTime, p4)) : 1;
        const e14 = n6(), n17 = n6();
        if (this._previousCenter) {
          r3(e14, this._previousCenter.x, this._previousCenter.y), nt(n17, t11.size, t11.padding), s5(e14, e14, n17);
          const { constraints: r12, scale: p5 } = t11, l10 = p5 * s13;
          s13 < 1 && !r12.canZoomInTo(l10) ? (s13 = p5 / r12.effectiveMaxScale, this.zoomMomentum = null, this.rotateMomentum = null) : s13 > 1 && !r12.canZoomOutTo(l10) && (s13 = p5 / r12.effectiveMinScale, this.zoomMomentum = null, this.rotateMomentum = null), Gt(o10, t11.viewpoint, s13, i9, e14, t11.size), t11.constraints.constrainByGeometry(o10);
        }
      }
      this._animationTime += p4;
    });
  }
  stopMomentumNavigation() {
    (this.rotateMomentum || this.zoomMomentum) && (this.rotateMomentum && (this._rotationMomentumEstimator.reset(), this.rotateMomentum = null), this.zoomMomentum && (this._zoomMomentumEstimator.reset(), this.zoomMomentum = null), this.navigation.stop());
  }
};
e([y()], _3.prototype, "_momentumFinished", void 0), e([y()], _3.prototype, "viewpoint", void 0), e([y()], _3.prototype, "navigation", void 0), _3 = e([a2("esri.views.2d.navigation.actions.Pinch")], _3);
var d4 = _3;

// node_modules/@arcgis/core/views/2d/navigation/actions/Rotate.js
var u7 = n6();
var d5 = n6();
var j2 = class extends v2 {
  constructor(t11) {
    super(t11), this._previousCenter = n6(), this.viewpoint = new u2({ targetGeometry: new w(), scale: 0, rotation: 0 });
  }
  begin(t11, e13) {
    this.navigation.begin(), r3(this._previousCenter, e13.center.x, e13.center.y);
  }
  update(t11, e13) {
    const { state: { size: o10, padding: r12 } } = t11;
    r3(u7, e13.center.x, e13.center.y), $(d5, o10, r12), t11.viewpoint = bt(this.viewpoint, t11.state.paddedViewState.viewpoint, pt(d5, this._previousCenter, u7)), a5(this._previousCenter, u7);
  }
  end() {
    this.navigation.end();
  }
};
e([y()], j2.prototype, "viewpoint", void 0), e([y()], j2.prototype, "navigation", void 0), j2 = e([a2("esri.views.2d.actions.Rotate")], j2);
var f6 = j2;

// node_modules/@arcgis/core/views/2d/navigation/MapViewNavigation.js
var v4 = 10;
var w2 = 1;
var g6 = new u2({ targetGeometry: new w() });
var _4 = [0, 0];
var d6 = 250;
var T3 = class extends v2 {
  constructor(t11) {
    super(t11), this._endTimer = null, this._lastEventTimestamp = null, this.animationManager = null, this.interacting = false;
  }
  initialize() {
    this.pan = new d3({ navigation: this }), this.rotate = new f6({ navigation: this }), this.pinch = new d4({ navigation: this }), this.zoomBox = new c9({ view: this.view, navigation: this });
  }
  destroy() {
    this.pan = a(this.pan), this.rotate = a(this.rotate), this.pinch = a(this.pinch), this.zoomBox = a(this.zoomBox), this.animationManager = null;
  }
  begin() {
    this._set("interacting", true);
  }
  end() {
    this._lastEventTimestamp = performance.now(), this._startTimer(d6);
  }
  async zoom(t11, i8 = this._getDefaultAnchor()) {
    if (this.stop(), this.begin(), this.view.constraints.snapToZoom && this.view.constraints.effectiveLODs) return t11 < 1 ? this.zoomIn(i8) : this.zoomOut(i8);
    this.setViewpoint(i8, t11, 0, [0, 0]);
  }
  async zoomIn(t11) {
    const i8 = this.view, o10 = i8.constraints.snapToNextScale(i8.scale);
    return this._zoomToScale(o10, t11);
  }
  async zoomOut(t11) {
    const i8 = this.view, o10 = i8.constraints.snapToPreviousScale(i8.scale);
    return this._zoomToScale(o10, t11);
  }
  setViewpoint(t11, i8, o10, n16) {
    this.begin(), this.view.state.viewpoint = this._scaleRotateTranslateViewpoint(this.view.viewpoint, t11, i8, o10, n16), this.end();
  }
  setViewpointImmediate(t11, i8 = 0, o10 = [0, 0], n16 = this._getDefaultAnchor()) {
    this.view.state.viewpoint = this._scaleRotateTranslateViewpoint(this.view.viewpoint, n16, t11, i8, o10);
  }
  continousRotateClockwise() {
    var _a;
    const t11 = this.get("view.viewpoint");
    (_a = this.animationManager) == null ? void 0 : _a.animateContinous(t11, (t12) => {
      bt(t12, t12, -w2);
    });
  }
  continousRotateCounterclockwise() {
    var _a;
    const t11 = this.get("view.viewpoint");
    (_a = this.animationManager) == null ? void 0 : _a.animateContinous(t11, (t12) => {
      bt(t12, t12, w2);
    });
  }
  resetRotation() {
    this.view.rotation = 0;
  }
  continousPanLeft() {
    this._continuousPan([-v4, 0]);
  }
  continousPanRight() {
    this._continuousPan([v4, 0]);
  }
  continousPanUp() {
    this._continuousPan([0, v4]);
  }
  continousPanDown() {
    this._continuousPan([0, -v4]);
  }
  stop() {
    var _a;
    this.pan.stopMomentumNavigation(), (_a = this.animationManager) == null ? void 0 : _a.stop(), this.end(), null !== this._endTimer && (clearTimeout(this._endTimer), this._endTimer = null, this._set("interacting", false));
  }
  _continuousPan(t11) {
    var _a;
    const i8 = this.view.viewpoint;
    (_a = this.animationManager) == null ? void 0 : _a.animateContinous(i8, (i9) => {
      St(i9, i9, t11), this.view.constraints.constrainByGeometry(i9);
    });
  }
  _startTimer(t11) {
    return null !== this._endTimer || (this._endTimer = setTimeout(() => {
      this._endTimer = null;
      const t12 = performance.now() - (this._lastEventTimestamp ?? 0);
      t12 < d6 ? this._endTimer = this._startTimer(t12) : this._set("interacting", false);
    }, t11)), this._endTimer;
  }
  _getDefaultAnchor() {
    const { size: t11, padding: { left: i8, right: o10, top: n16, bottom: e13 } } = this.view;
    return _4[0] = 0.5 * (t11[0] - o10 + i8), _4[1] = 0.5 * (t11[1] - e13 + n16), _4;
  }
  async _zoomToScale(t11, i8 = this._getDefaultAnchor()) {
    const { view: o10 } = this, { constraints: n16, scale: e13, viewpoint: s12, size: a13, padding: r12 } = o10, c11 = n16.canZoomInTo(t11), m5 = n16.canZoomOutTo(t11);
    if (!(t11 < e13 && !c11 || t11 > e13 && !m5)) return Rt(g6, s12, t11 / e13, 0, i8, a13, r12), n16.constrainByGeometry(g6), o10.goTo(g6, { animate: true });
  }
  _scaleRotateTranslateViewpoint(t11, i8, o10, n16, e13) {
    const { view: s12 } = this, { size: a13, padding: r12, constraints: m5, scale: p4, viewpoint: u8 } = s12, l10 = p4 * o10, v5 = m5.canZoomInTo(l10), w3 = m5.canZoomOutTo(l10);
    return (o10 < 1 && !v5 || o10 > 1 && !w3) && (o10 = 1), St(u8, u8, e13), Rt(t11, u8, o10, n16, i8, a13, r12), m5.constrainByGeometry(t11);
  }
};
e([y()], T3.prototype, "animationManager", void 0), e([y({ type: Boolean, readOnly: true })], T3.prototype, "interacting", void 0), e([y()], T3.prototype, "pan", void 0), e([y()], T3.prototype, "pinch", void 0), e([y()], T3.prototype, "rotate", void 0), e([y()], T3.prototype, "view", void 0), e([y()], T3.prototype, "zoomBox", void 0), T3 = e([a2("esri.views.2d.navigation.MapViewNavigation")], T3);
var y4 = T3;

// node_modules/@arcgis/core/views/2d/engine/webgl/shaders/MagnifierPrograms.js
var a12 = { shaders: { vertexShader: n8("magnifier/magnifier.vert"), fragmentShader: n8("magnifier/magnifier.frag") }, attributes: /* @__PURE__ */ new Map([["a_pos", 0]]) };
function t10(r12) {
  return e7(r12, a12);
}

export {
  n9 as n,
  e9 as e,
  J,
  r6 as r,
  _2 as _,
  t7 as t,
  r8 as r2,
  i5 as i,
  u6 as u,
  y4 as y,
  a12 as a,
  t10 as t2
};
//# sourceMappingURL=chunk-GHAN7X65.js.map
