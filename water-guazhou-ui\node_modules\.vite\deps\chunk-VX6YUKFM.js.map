{"version": 3, "sources": ["../../@arcgis/core/symbols/SimpleMarkerSymbol.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import o from\"../Color.js\";import{JSONMap as r}from\"../core/jsonMap.js\";import{clone as t}from\"../core/lang.js\";import{toPt as s}from\"../core/screenUtils.js\";import{property as i}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{enumeration as l}from\"../core/accessorSupport/decorators/enumeration.js\";import{subclass as p}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as a}from\"../core/accessorSupport/decorators/writer.js\";import n from\"./MarkerSymbol.js\";import c from\"./SimpleLineSymbol.js\";var h;const m=new r({esriSMSCircle:\"circle\",esriSMSSquare:\"square\",esriSMSCross:\"cross\",esriSMSX:\"x\",esriSMSDiamond:\"diamond\",esriSMSTriangle:\"triangle\",esriSMSPath:\"path\"});let u=h=class extends n{constructor(...e){super(...e),this.color=new o([255,255,255,.25]),this.type=\"simple-marker\",this.size=12,this.style=\"circle\",this.outline=new c}normalizeCtorArgs(e,o,r,t){if(e&&\"string\"!=typeof e)return e;const i={};return e&&(i.style=e),null!=o&&(i.size=s(o)),r&&(i.outline=r),t&&(i.color=t),i}writeColor(e,o){e&&\"x\"!==this.style&&\"cross\"!==this.style&&(o.color=e.toJSON()),null===e&&(o.color=null)}set path(e){this.style=\"path\",this._set(\"path\",e)}clone(){return new h({angle:this.angle,color:t(this.color),outline:this.outline&&this.outline.clone(),path:this.path,size:this.size,style:this.style,xoffset:this.xoffset,yoffset:this.yoffset})}hash(){return`${super.hash()}.${this.color&&this.color.hash()}.${this.path}.${this.style}.${this.outline?.hash()}`}};e([i()],u.prototype,\"color\",void 0),e([a(\"color\")],u.prototype,\"writeColor\",null),e([l({esriSMS:\"simple-marker\"},{readOnly:!0})],u.prototype,\"type\",void 0),e([i()],u.prototype,\"size\",void 0),e([i({type:m.apiValues,json:{read:m.read,write:m.write}})],u.prototype,\"style\",void 0),e([i({type:String,json:{write:!0}})],u.prototype,\"path\",null),e([i({types:{key:\"type\",base:null,defaultKeyValue:\"simple-line\",typeMap:{\"simple-line\":c}},json:{default:null,write:!0}})],u.prototype,\"outline\",void 0),u=h=e([p(\"esri.symbols.SimpleMarkerSymbol\")],u);const y=u;export{y as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIumB,IAAI;AAAE,IAAMA,KAAE,IAAI,EAAE,EAAC,eAAc,UAAS,eAAc,UAAS,cAAa,SAAQ,UAAS,KAAI,gBAAe,WAAU,iBAAgB,YAAW,aAAY,OAAM,CAAC;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,QAAM,IAAI,EAAE,CAAC,KAAI,KAAI,KAAI,IAAG,CAAC,GAAE,KAAK,OAAK,iBAAgB,KAAK,OAAK,IAAG,KAAK,QAAM,UAAS,KAAK,UAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,IAAEC,IAAE,GAAE;AAAC,QAAGF,MAAG,YAAU,OAAOA,GAAE,QAAOA;AAAE,UAAMG,KAAE,CAAC;AAAE,WAAOH,OAAIG,GAAE,QAAMH,KAAG,QAAMC,OAAIE,GAAE,OAAKF,GAAEA,EAAC,IAAGC,OAAIC,GAAE,UAAQD,KAAG,MAAIC,GAAE,QAAM,IAAGA;AAAA,EAAC;AAAA,EAAC,WAAWH,IAAEC,IAAE;AAAC,IAAAD,MAAG,QAAM,KAAK,SAAO,YAAU,KAAK,UAAQC,GAAE,QAAMD,GAAE,OAAO,IAAG,SAAOA,OAAIC,GAAE,QAAM;AAAA,EAAK;AAAA,EAAC,IAAI,KAAKD,IAAE;AAAC,SAAK,QAAM,QAAO,KAAK,KAAK,QAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,OAAM,KAAK,OAAM,OAAM,EAAE,KAAK,KAAK,GAAE,SAAQ,KAAK,WAAS,KAAK,QAAQ,MAAM,GAAE,MAAK,KAAK,MAAK,MAAK,KAAK,MAAK,OAAM,KAAK,OAAM,SAAQ,KAAK,SAAQ,SAAQ,KAAK,QAAO,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAJt7C;AAIu7C,WAAM,GAAG,MAAM,KAAK,CAAC,IAAI,KAAK,SAAO,KAAK,MAAM,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAI,UAAK,YAAL,mBAAc,MAAM;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,SAAQ,gBAAe,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKD,GAAE,WAAU,MAAK,EAAC,MAAKA,GAAE,MAAK,OAAMA,GAAE,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,EAAC,KAAI,QAAO,MAAK,MAAK,iBAAgB,eAAc,SAAQ,EAAC,eAAc,EAAC,EAAC,GAAE,MAAK,EAAC,SAAQ,MAAK,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,iCAAiC,CAAC,GAAE,CAAC;AAAE,IAAMK,KAAE;", "names": ["m", "e", "o", "r", "i", "y"]}