{"version": 3, "sources": ["../../@arcgis/core/support/timeUtils.js", "../../@arcgis/core/layers/support/commonProperties.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../TimeExtent.js\";import{unwrap as t,isSome as n,isNone as i}from\"../core/maybe.js\";import{eachAlways as r}from\"../core/promiseUtils.js\";import{offsetDate as o}from\"../core/timeUtils.js\";function m(e){return void 0!==e.timeInfo}async function s(o,s){if(0===o.length)return e.allTime;const f=o.filter(m);await Promise.all(f.map((e=>e.load({signal:s}))));const a=[],u=[];for(const e of f)\"feature\"!==e?.type&&\"map-image\"!==e?.type||!e.timeInfo?.hasLiveData?u.push(e):a.push(e);const l=e=>i(e)||e.isAllTime,c=u.map((e=>e.timeInfo?.fullTimeExtent));if(c.some(l))return e.allTime;const p=a.map((async e=>{const{timeExtent:n}=await e.fetchRecomputedExtents({signal:s});return n||t(e.timeInfo?.fullTimeExtent)})),T=(await r(p)).map((e=>e.value));if(T.some(l))return e.allTime;return[...T,...c].filter(n).reduce(((e,t)=>e.union(t)))}function f(t){if(!t)return t;const{start:i,end:r}=t;return new e({start:n(i)?o(i,-i.getTimezoneOffset(),\"minutes\"):i,end:n(r)?o(r,-r.getTimezoneOffset(),\"minutes\"):r})}function a(t){if(!t)return t;const{start:i,end:r}=t;return new e({start:n(i)?o(i,i.getTimezoneOffset(),\"minutes\"):i,end:n(r)?o(r,r.getTimezoneOffset(),\"minutes\"):r})}export{s as getTimeExtentFromLayers,a as toLocalTimeExtent,f as toUTCTimeExtent};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../TimeExtent.js\";import r from\"../../geometry/Extent.js\";import i from\"../../geometry/SpatialReference.js\";import{w as n}from\"../../chunks/persistableUrlUtils.js\";import{toLocalTimeExtent as t,toUTCTimeExtent as o}from\"../../support/timeUtils.js\";import a from\"../../symbols/support/ElevationInfo.js\";import{transparencyToOpacity as s}from\"../../webdoc/support/opacityUtils.js\";const l={type:Boolean,value:!0,json:{origins:{service:{read:!1,write:!1},\"web-map\":{read:!1,write:!1}},name:\"screenSizePerspective\",write:!0}},p={type:<PERSON>olean,value:!0,json:{name:\"disablePopup\",read:{reader:(e,r)=>!r.disablePopup},write:{enabled:!0,writer(e,r,i){r[i]=!e}}}},m={type:Boolean,value:!0,nonNullable:!0,json:{name:\"showLabels\",write:!0}},f={type:String,json:{origins:{\"portal-item\":{write:!1}},write:{isRequired:!0,ignoreOrigin:!0,writer:n}}},c={type:Boolean,value:!0,nonNullable:!0,json:{origins:{service:{read:{enabled:!1}}},name:\"showLegend\",write:!0}},d={value:null,type:a,json:{origins:{service:{name:\"elevationInfo\",write:!0}},name:\"layerDefinition.elevationInfo\",write:!0}};function y(e){return{type:e,readOnly:!0,json:{origins:{service:{read:!0}},read:!1}}}const w={write:!0,read:!0},u={type:Number,json:{origins:{\"web-document\":w,\"portal-item\":{write:!0}}}},g={...u,json:{...u.json,origins:{\"web-document\":{...w,write:{enabled:!0,target:{opacity:{type:Number},\"layerDefinition.drawingInfo.transparency\":{type:Number}}}}},read:{source:[\"layerDefinition.drawingInfo.transparency\",\"drawingInfo.transparency\"],reader:(e,r,i)=>i&&\"service\"!==i.origin||!r.drawingInfo||void 0===r.drawingInfo.transparency?r.layerDefinition&&r.layerDefinition.drawingInfo&&void 0!==r.layerDefinition.drawingInfo.transparency?s(r.layerDefinition.drawingInfo.transparency):void 0:s(r.drawingInfo.transparency)}}},b={type:e,readOnly:!0,get(){if(!this.layer?.timeInfo)return null;const{datesInUnknownTimezone:e,timeOffset:r,useViewTime:i}=this.layer,n=this.view?.timeExtent;let a=this.layer.timeExtent;e&&(a=t(a));let s=i?n&&a?n.intersection(a):n||a:a;if(!s||s.isEmpty||s.isAllTime)return s;r&&(s=s.offset(-r.value,r.unit)),e&&(s=o(s));const l=this._get(\"timeExtent\");return s.equals(l)?l:s}},j={type:r,readOnly:!0,json:{origins:{service:{read:{source:[\"fullExtent\",\"spatialReference\"],reader:(e,n)=>{const t=r.fromJSON(e);return null!=n.spatialReference&&\"object\"==typeof n.spatialReference&&(t.spatialReference=i.fromJSON(n.spatialReference)),t}}}},read:!1}},v={type:String,json:{origins:{service:{read:!1},\"portal-item\":{read:!1}}}},I={type:Number,json:{origins:{service:{write:{enabled:!1}}},read:{source:\"layerDefinition.minScale\"},write:{target:\"layerDefinition.minScale\"}}},D={type:Number,json:{origins:{service:{write:{enabled:!1}}},read:{source:\"layerDefinition.maxScale\"},write:{target:\"layerDefinition.maxScale\"}}},S={json:{write:{ignoreOrigin:!0},origins:{\"web-map\":{read:!1,write:!1}}}};export{b as combinedViewLayerTimeExtentProperty,d as elevationInfo,v as id,m as labelsVisible,c as legendEnabled,S as listMode,D as maxScale,I as minScale,u as opacity,g as opacityDrawingInfo,p as popupEnabled,y as readOnlyService,j as sceneLayerFullExtent,l as screenSizePerspectiveEnabled,f as url};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAIu0B,SAASA,GAAEC,IAAE;AAAC,MAAG,CAACA,GAAE,QAAOA;AAAE,QAAK,EAAC,OAAM,GAAE,KAAIC,GAAC,IAAED;AAAE,SAAO,IAAI,EAAE,EAAC,OAAM,EAAE,CAAC,IAAE,EAAE,GAAE,CAAC,EAAE,kBAAkB,GAAE,SAAS,IAAE,GAAE,KAAI,EAAEC,EAAC,IAAE,EAAEA,IAAE,CAACA,GAAE,kBAAkB,GAAE,SAAS,IAAEA,GAAC,CAAC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,MAAG,CAACA,GAAE,QAAOA;AAAE,QAAK,EAAC,OAAM,GAAE,KAAIC,GAAC,IAAED;AAAE,SAAO,IAAI,EAAE,EAAC,OAAM,EAAE,CAAC,IAAE,EAAE,GAAE,EAAE,kBAAkB,GAAE,SAAS,IAAE,GAAE,KAAI,EAAEC,EAAC,IAAE,EAAEA,IAAEA,GAAE,kBAAkB,GAAE,SAAS,IAAEA,GAAC,CAAC;AAAC;;;ACAxwB,IAAM,IAAE,EAAC,MAAK,SAAQ,OAAM,MAAG,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,WAAU,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,GAAE,MAAK,yBAAwB,OAAM,KAAE,EAAC;AAA7I,IAA+I,IAAE,EAAC,MAAK,SAAQ,OAAM,MAAG,MAAK,EAAC,MAAK,gBAAe,MAAK,EAAC,QAAO,CAACC,IAAEC,OAAI,CAACA,GAAE,aAAY,GAAE,OAAM,EAAC,SAAQ,MAAG,OAAOD,IAAEC,IAAE,GAAE;AAAC,EAAAA,GAAE,CAAC,IAAE,CAACD;AAAC,EAAC,EAAC,EAAC;AAAjR,IAAmR,IAAE,EAAC,MAAK,SAAQ,OAAM,MAAG,aAAY,MAAG,MAAK,EAAC,MAAK,cAAa,OAAM,KAAE,EAAC;AAA5V,IAA8VE,KAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,eAAc,EAAC,OAAM,MAAE,EAAC,GAAE,OAAM,EAAC,YAAW,MAAG,cAAa,MAAG,QAAOA,GAAC,EAAC,EAAC;AAArc,IAAuc,IAAE,EAAC,MAAK,SAAQ,OAAM,MAAG,aAAY,MAAG,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,SAAQ,MAAE,EAAC,EAAC,GAAE,MAAK,cAAa,OAAM,KAAE,EAAC;AAAtjB,IAAwjB,IAAE,EAAC,OAAM,MAAK,MAAK,GAAE,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,iBAAgB,OAAM,KAAE,EAAC,GAAE,MAAK,iCAAgC,OAAM,KAAE,EAAC;AAAE,SAAS,EAAEF,IAAE;AAAC,SAAM,EAAC,MAAKA,IAAE,UAAS,MAAG,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,KAAE,EAAC,GAAE,MAAK,MAAE,EAAC;AAAC;AAAC,IAAMG,KAAE,EAAC,OAAM,MAAG,MAAK,KAAE;AAAzB,IAA2B,IAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,gBAAeA,IAAE,eAAc,EAAC,OAAM,KAAE,EAAC,EAAC,EAAC;AAApG,IAAsG,IAAE,EAAC,GAAG,GAAE,MAAK,EAAC,GAAG,EAAE,MAAK,SAAQ,EAAC,gBAAe,EAAC,GAAGA,IAAE,OAAM,EAAC,SAAQ,MAAG,QAAO,EAAC,SAAQ,EAAC,MAAK,OAAM,GAAE,4CAA2C,EAAC,MAAK,OAAM,EAAC,EAAC,EAAC,EAAC,GAAE,MAAK,EAAC,QAAO,CAAC,4CAA2C,0BAA0B,GAAE,QAAO,CAACH,IAAEC,IAAE,MAAI,KAAG,cAAY,EAAE,UAAQ,CAACA,GAAE,eAAa,WAASA,GAAE,YAAY,eAAaA,GAAE,mBAAiBA,GAAE,gBAAgB,eAAa,WAASA,GAAE,gBAAgB,YAAY,eAAaA,GAAEA,GAAE,gBAAgB,YAAY,YAAY,IAAE,SAAOA,GAAEA,GAAE,YAAY,YAAY,EAAC,EAAC,EAAC;AAArnB,IAAunB,IAAE,EAAC,MAAK,GAAE,UAAS,MAAG,MAAK;AAJvyD;AAIwyD,MAAG,GAAC,UAAK,UAAL,mBAAY,UAAS,QAAO;AAAK,QAAK,EAAC,wBAAuBD,IAAE,YAAWC,IAAE,aAAY,EAAC,IAAE,KAAK,OAAMG,MAAE,UAAK,SAAL,mBAAW;AAAW,MAAIC,KAAE,KAAK,MAAM;AAAW,EAAAL,OAAIK,KAAE,EAAEA,EAAC;AAAG,MAAI,IAAE,IAAED,MAAGC,KAAED,GAAE,aAAaC,EAAC,IAAED,MAAGC,KAAEA;AAAE,MAAG,CAAC,KAAG,EAAE,WAAS,EAAE,UAAU,QAAO;AAAE,EAAAJ,OAAI,IAAE,EAAE,OAAO,CAACA,GAAE,OAAMA,GAAE,IAAI,IAAGD,OAAI,IAAEE,GAAE,CAAC;AAAG,QAAMI,KAAE,KAAK,KAAK,YAAY;AAAE,SAAO,EAAE,OAAOA,EAAC,IAAEA,KAAE;AAAC,EAAC;AAA/+B,IAAi/B,IAAE,EAAC,MAAK,GAAE,UAAS,MAAG,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,CAAC,cAAa,kBAAkB,GAAE,QAAO,CAACN,IAAEI,OAAI;AAAC,QAAMG,KAAE,EAAE,SAASP,EAAC;AAAE,SAAO,QAAMI,GAAE,oBAAkB,YAAU,OAAOA,GAAE,qBAAmBG,GAAE,mBAAiB,EAAE,SAASH,GAAE,gBAAgB,IAAGG;AAAC,EAAC,EAAC,EAAC,GAAE,MAAK,MAAE,EAAC;AAA3vC,IAA6vC,IAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,MAAE,GAAE,eAAc,EAAC,MAAK,MAAE,EAAC,EAAC,EAAC;AAAt0C,IAAw0C,IAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,OAAM,EAAC,SAAQ,MAAE,EAAC,EAAC,GAAE,MAAK,EAAC,QAAO,2BAA0B,GAAE,OAAM,EAAC,QAAO,2BAA0B,EAAC,EAAC;AAAv9C,IAAy9C,IAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,OAAM,EAAC,SAAQ,MAAE,EAAC,EAAC,GAAE,MAAK,EAAC,QAAO,2BAA0B,GAAE,OAAM,EAAC,QAAO,2BAA0B,EAAC,EAAC;AAAxmD,IAA0mD,IAAE,EAAC,MAAK,EAAC,OAAM,EAAC,cAAa,KAAE,GAAE,SAAQ,EAAC,WAAU,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC;", "names": ["f", "t", "r", "e", "r", "f", "w", "n", "a", "l", "t"]}