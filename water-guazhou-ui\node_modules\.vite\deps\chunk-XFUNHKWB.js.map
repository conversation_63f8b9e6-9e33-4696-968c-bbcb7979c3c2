{"version": 3, "sources": ["../../@arcgis/core/views/draw/support/HighlightHelper.js", "../../@arcgis/core/views/draw/support/input/GraphicMoverEvents.js", "../../@arcgis/core/views/draw/support/GraphicMover.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{HandleOwner as e}from\"../../../core/HandleOwner.js\";import{property as t}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import{findLayerView as o}from\"./layerUtils.js\";import{highlightsSupported as i}from\"../../support/layerViewUtils.js\";let l=class extends e{constructor(r){super(r),this.view=null}get count(){return this.handles.size}add(r){const e=Array.isArray(r)?r:[r];null!=r&&e&&e.length&&e.forEach((r=>this._highlight(r)))}remove(r){const e=Array.isArray(r)?r:[r];null!=r&&e&&e.length&&e.forEach((r=>this._unhighlight(r)))}removeAll(){this.handles.removeAll()}_highlight(r){const e=o(this.view,r.layer);i(e)&&this.handles.add(e.highlight(r),`feature-${r.getObjectId()}`)}_unhighlight(r){r&&this.handles.remove(`feature-${r.getObjectId()}`)}};r([t({readOnly:!0})],l.prototype,\"count\",null),r([t()],l.prototype,\"view\",void 0),r([t()],l.prototype,\"add\",null),l=r([s(\"esri.views.draw.support.HighlightHelper\")],l);const h=l;export{h as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t,i,s,h,e){this.graphic=t,this.index=i,this.x=s,this.y=h,this.viewEvent=e,this.type=\"graphic-click\"}}class i{constructor(t,i,s,h,e){this.graphic=t,this.index=i,this.x=s,this.y=h,this.viewEvent=e,this.type=\"graphic-double-click\"}}class s{constructor(t,i,s,h,e,r,c,a,n,p){this.graphic=t,this.allGraphics=i,this.index=s,this.x=h,this.y=e,this.dx=r,this.dy=c,this.totalDx=a,this.totalDy=n,this.viewEvent=p,this.defaultPrevented=!1,this.type=\"graphic-move-start\"}preventDefault(){this.defaultPrevented=!0}}class h{constructor(t,i,s,h,e,r,c,a,n,p){this.graphic=t,this.allGraphics=i,this.index=s,this.x=h,this.y=e,this.dx=r,this.dy=c,this.totalDx=a,this.totalDy=n,this.viewEvent=p,this.defaultPrevented=!1,this.type=\"graphic-move\"}preventDefault(){this.defaultPrevented=!0}}class e{constructor(t,i,s,h,e,r,c,a,n,p){this.graphic=t,this.allGraphics=i,this.index=s,this.x=h,this.y=e,this.dx=r,this.dy=c,this.totalDx=a,this.totalDy=n,this.viewEvent=p,this.defaultPrevented=!1,this.type=\"graphic-move-stop\"}preventDefault(){this.defaultPrevented=!0}}class r{constructor(t,i,s,h,e){this.graphic=t,this.index=i,this.x=s,this.y=h,this.viewEvent=e,this.type=\"graphic-pointer-over\"}}class c{constructor(t,i,s,h,e){this.graphic=t,this.index=i,this.x=s,this.y=h,this.viewEvent=e,this.type=\"graphic-pointer-out\"}}class a{constructor(t,i,s,h,e){this.graphic=t,this.index=i,this.x=s,this.y=h,this.viewEvent=e,this.type=\"graphic-pointer-down\"}}class n{constructor(t,i,s,h,e){this.graphic=t,this.index=i,this.x=s,this.y=h,this.viewEvent=e,this.type=\"graphic-pointer-up\"}}export{t as GraphicClickEvent,i as GraphicDoubleClickEvent,h as GraphicMoveEvent,s as GraphicMoveStartEvent,e as GraphicMoveStopEvent,a as GraphicPointerDownEvent,c as GraphicPointerOutEvent,r as GraphicPointerOverEvent,n as GraphicPointerUpEvent};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as i}from\"../../../chunks/tslib.es6.js\";import\"../../../symbols.js\";import t from\"../../../core/Evented.js\";import e from\"../../../core/Handles.js\";import{clone as s}from\"../../../core/lang.js\";import{destroyMaybe as r,isSome as h,isNone as a}from\"../../../core/maybe.js\";import{watch as o,when as c}from\"../../../core/reactiveUtils.js\";import{property as n}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import{subclass as l}from\"../../../core/accessorSupport/decorators/subclass.js\";import p from\"../../../layers/GraphicsLayer.js\";import{cloneMove as d}from\"./drawUtils.js\";import v from\"./HighlightHelper.js\";import{addUniqueLayer as g,findLayerView as _}from\"./layerUtils.js\";import{GraphicClickEvent as m,GraphicDoubleClickEvent as y,GraphicPointerDownEvent as u,GraphicPointerUpEvent as f,GraphicPointerOutEvent as w,GraphicMoveStartEvent as G,GraphicMoveEvent as b,GraphicMoveStopEvent as k,GraphicPointerOverEvent as O}from\"./input/GraphicMoverEvents.js\";import{ViewEventPriorities as H}from\"../../input/InputManager.js\";import{GraphicManipulator as M}from\"../../interactive/GraphicManipulator.js\";import{createScreenPointFromEvent as x}from\"../../support/screenUtils.js\";import E from\"../../../symbols/SimpleMarkerSymbol.js\";import j from\"../../../symbols/SimpleLineSymbol.js\";import U from\"../../../symbols/SimpleFillSymbol.js\";const P=\"indicator-symbols\";let S=class extends t.EventedAccessor{constructor(i){super(i),this._activeGraphic=null,this._dragEvent=null,this._handles=new e,this._hoverGraphic=null,this._indicators=[],this._initialDragGeometry=null,this._viewHandles=new e,this._manipulators=[],this._layerViews=null,this.type=\"graphic-mover\",this.callbacks={onGraphicClick(){},onGraphicDoubleClick(){},onGraphicMoveStart(){},onGraphicMove(){},onGraphicMoveStop(){},onGraphicPointerOver(){},onGraphicPointerOut(){},onGraphicPointerDown(){},onGraphicPointerUp(){}},this.enableMoveAllGraphics=!1,this.graphics=[],this.indicatorsEnabled=!1,this.layer=new p({listMode:\"hide\",internal:!0,title:\"GraphicMover highlight layer\"}),this.view=null}initialize(){g(this.view,this.layer),this._highlightHelper=new v({view:this.view}),this.refresh(),this._handles.add([o((()=>[this.graphics,this.graphics?.length]),(()=>this.refresh())),c((()=>this.view?.ready),(()=>{this._viewHandles.add([this.view.on(\"immediate-click\",(i=>this._clickHandler(i)),H.TOOL),this.view.on(\"double-click\",(i=>this._doubleClickHandler(i)),H.TOOL),this.view.on(\"pointer-down\",(i=>this._pointerDownHandler(i)),H.TOOL),this.view.on(\"pointer-move\",(i=>this._pointerMoveHandler(i)),H.TOOL),this.view.on(\"pointer-up\",(i=>this._pointerUpHandler(i)),H.TOOL),this.view.on(\"drag\",(i=>this._dragHandler(i)),H.TOOL),this.view.on(\"key-down\",(i=>this._keyDownHandler(i)),H.TOOL)])}),{once:!0,initial:!0}),o((()=>this.view),(i=>{this._highlightHelper.removeAll(),this._highlightHelper.view=i}))])}destroy(){this._removeIndicators(),this.view.map?.remove(this.layer),this.layer.destroy(),this.reset(),this._manipulators.forEach((i=>i.destroy())),this._manipulators=null,this._handles=r(this._handles),this._viewHandles=r(this._viewHandles)}set highlightsEnabled(i){this._highlightHelper?.removeAll(),this._set(\"highlightsEnabled\",i),i&&this._highlightHelper?.add(this.graphics)}get state(){const i=!!this.get(\"view.ready\"),t=!!this.get(\"graphics.length\"),e=this._activeGraphic;return i&&t?e?\"moving\":\"active\":i?\"ready\":\"disabled\"}refresh(){this.reset(),this._setup()}reset(){this._activeGraphic=null,this._hoverGraphic=null,this._dragEvent=null,this._highlightHelper.removeAll()}updateGeometry(i,t){const e=this.graphics[i];e&&(e.set(\"geometry\",t),this._setUpIndicators())}_setup(){this._setUpHighlights(),this._setUpIndicators(),this._setUpManipulators(),this._syncLayerViews()}_clickHandler(i){const t=this._findTargetGraphic(x(i));if(t){const e=new m(t,this.graphics.indexOf(t),i.x,i.y,i);this.emit(\"graphic-click\",e),this.callbacks.onGraphicClick&&this.callbacks.onGraphicClick(e)}}_doubleClickHandler(i){const t=this._findTargetGraphic(x(i));if(t){const e=new y(t,this.graphics.indexOf(t),i.x,i.y,i);this.emit(\"graphic-double-click\",e),this.callbacks.onGraphicDoubleClick&&this.callbacks.onGraphicDoubleClick(e)}}_pointerDownHandler(i){const t=this._findTargetGraphic(x(i));if(t){this._activeGraphic=t;const{x:e,y:s}=i,r=new u(t,this.graphics.indexOf(t),e,s,i);this.emit(\"graphic-pointer-down\",r),this.callbacks.onGraphicPointerDown&&this.callbacks.onGraphicPointerDown(r)}else this._activeGraphic=null}_pointerUpHandler(i){if(this._activeGraphic){const{x:t,y:e}=i,s=this.graphics.indexOf(this._activeGraphic),r=new f(this._activeGraphic,s,t,e,i);this.emit(\"graphic-pointer-up\",r),this.callbacks.onGraphicPointerUp&&this.callbacks.onGraphicPointerUp(r)}}_pointerMoveHandler(i){if(this._dragEvent)return;const t=this._findTargetGraphic(x(i));if(t){const{x:e,y:s}=i;if(this._hoverGraphic){if(this._hoverGraphic===t)return;const r=this.graphics.indexOf(this._hoverGraphic),h=new w(this.graphics[r],r,e,s,i);this._hoverGraphic=null,this.emit(\"graphic-pointer-out\",h),this.callbacks.onGraphicPointerOut&&this.callbacks.onGraphicPointerOut(h)}const r=this.graphics.indexOf(t),h=new O(t,r,e,s,i);return this._hoverGraphic=t,this.emit(\"graphic-pointer-over\",h),void(this.callbacks.onGraphicPointerOver&&this.callbacks.onGraphicPointerOver(h))}if(this._hoverGraphic){const{x:t,y:e}=i,s=this.graphics.indexOf(this._hoverGraphic),r=new w(this.graphics[s],s,t,e,i);this._hoverGraphic=null,this.emit(\"graphic-pointer-out\",r),this.callbacks.onGraphicPointerOut&&this.callbacks.onGraphicPointerOut(r)}}_dragHandler(i){if(\"start\"!==i.action&&!this._dragEvent||!this._activeGraphic||!this._activeGraphic.geometry)return;\"start\"===i.action&&this._removeIndicators(),i.stopPropagation();const{action:t,x:e,y:r}=i,h=this.graphics.indexOf(this._activeGraphic),a=this._dragEvent?e-this._dragEvent.x:0,o=this._dragEvent?r-this._dragEvent.y:0,c=e-i.origin.x,n=r-i.origin.y,l=\"start\"===t?this._activeGraphic.geometry:this._initialDragGeometry,p=d(l,c,n,this.view);if(this._activeGraphic.geometry=p,this.enableMoveAllGraphics&&this.graphics.forEach((i=>{i!==this._activeGraphic&&(i.geometry=d(i.geometry,a,o,this.view))})),this._dragEvent=i,\"start\"===t){this._initialDragGeometry=s(l);const t=new G(this._activeGraphic,this.graphics,h,e,r,a,o,c,n,i);this.emit(\"graphic-move-start\",t),this.callbacks.onGraphicMoveStart&&this.callbacks.onGraphicMoveStart(t),t.defaultPrevented&&this._activeGraphic.set(\"geometry\",l)}else if(\"update\"===t){const t=new b(this._activeGraphic,this.graphics,h,e,r,a,o,c,n,i);this.emit(\"graphic-move\",t),this.callbacks.onGraphicMove&&this.callbacks.onGraphicMove(t),t.defaultPrevented&&(this._activeGraphic.geometry=l)}else{const t=new k(this._activeGraphic,this.graphics,h,e,r,a,o,c,n,i);this._dragEvent=null,this._activeGraphic=null,this._setUpIndicators(),this.emit(\"graphic-move-stop\",t),this.callbacks.onGraphicMoveStop&&this.callbacks.onGraphicMoveStop(t),t.defaultPrevented&&(this.graphics[h].set(\"geometry\",this._initialDragGeometry),this._setUpIndicators()),this._initialDragGeometry=null}}_keyDownHandler(i){\"a\"!==i.key&&\"d\"!==i.key&&\"n\"!==i.key||\"moving\"!==this.state||i.stopPropagation()}_findTargetGraphic(i){const t=this.view.toMap(i);let e=null,s=Number.MAX_VALUE;this._syncLayerViews();const r=this._layerViews.flatMap((i=>\"graphicsViews\"in i?Array.from(i.graphicsViews(),(i=>i.hitTest(t))).flat():i.graphicsView.hitTest(t))).filter((i=>this.graphics.includes(i)));return r.length?r[0]:(this._manipulators.forEach((t=>{const r=t.intersectionDistance(i);h(r)&&r<s&&(s=r,e=t.graphic)})),e)}_syncLayerViews(){this._layerViews=[];const i=new Set;for(const t of this.graphics){const e=_(this.view,t.layer);e&&i.add(e)}this._layerViews=[...i]}_setUpManipulators(){const{graphics:i,view:t}=this;this._manipulators.forEach((i=>i.destroy())),this._manipulators=i?.length?i.map((i=>new M({graphic:i,view:t}))):[]}_setUpHighlights(){this.highlightsEnabled&&this._highlightHelper.add(this.graphics)}_setUpIndicators(){if(this._removeIndicators(),this.indicatorsEnabled){for(const i of this.graphics){const t=i.clone();t.symbol=this._getSymbolForIndicator(i),this._indicators.push(t),this._handles.add(o((()=>i.symbol),(()=>this._setUpIndicators())),P)}this.layer.addMany(this._indicators)}}_removeIndicators(){this._handles.remove(P),this._indicators.length&&(this.layer.removeMany(this._indicators),this._indicators.forEach((i=>i.destroy())),this._indicators=[])}_getSymbolForIndicator(i){const t=12;if(a(i.symbol))return null;switch(i.symbol.type){case\"cim\":return new E({style:\"circle\",size:t,color:[0,0,0,0],outline:{color:[255,127,0,1],width:1}});case\"picture-marker\":{const{xoffset:t,yoffset:e,height:s,width:r}=i.symbol,h=s===r?r:Math.max(s,r);return new E({xoffset:t,yoffset:e,size:h,style:\"square\",color:[0,0,0,0],outline:{color:[255,127,0,1],width:1}})}case\"simple-marker\":{const{xoffset:t,yoffset:e,size:s,style:r}=i.symbol;return new E({xoffset:t,yoffset:e,style:\"circle\"===r?\"circle\":\"square\",size:s+2,color:[0,0,0,0],outline:{color:[255,127,0,1],width:1}})}case\"simple-fill\":return new U({color:[0,0,0,0],outline:{style:\"dash\",color:[255,127,0,1],width:1}});case\"simple-line\":return new j({color:[255,127,0,1],style:\"dash\",width:1});case\"text\":{const{xoffset:e,yoffset:s}=i.symbol;return new E({xoffset:e,yoffset:s,size:t,color:[0,0,0,0],outline:{color:[255,127,0,1],width:1}})}default:return null}}};i([n()],S.prototype,\"_activeGraphic\",void 0),i([n({readOnly:!0})],S.prototype,\"type\",void 0),i([n()],S.prototype,\"callbacks\",void 0),i([n()],S.prototype,\"enableMoveAllGraphics\",void 0),i([n()],S.prototype,\"graphics\",void 0),i([n({value:!1})],S.prototype,\"highlightsEnabled\",null),i([n()],S.prototype,\"indicatorsEnabled\",void 0),i([n()],S.prototype,\"layer\",void 0),i([n({readOnly:!0})],S.prototype,\"state\",null),i([n()],S.prototype,\"view\",void 0),S=i([l(\"esri.views.draw.support.GraphicMover\")],S);const D=S;export{D as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0d,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAI;AAAA,EAAC,IAAIA,IAAE;AAAC,UAAMC,KAAE,MAAM,QAAQD,EAAC,IAAEA,KAAE,CAACA,EAAC;AAAE,YAAMA,MAAGC,MAAGA,GAAE,UAAQA,GAAE,QAAS,CAAAD,OAAG,KAAK,WAAWA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAMC,KAAE,MAAM,QAAQD,EAAC,IAAEA,KAAE,CAACA,EAAC;AAAE,YAAMA,MAAGC,MAAGA,GAAE,UAAQA,GAAE,QAAS,CAAAD,OAAG,KAAK,aAAaA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,SAAK,QAAQ,UAAU;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAMC,KAAEC,GAAE,KAAK,MAAKF,GAAE,KAAK;AAAE,IAAAG,GAAEF,EAAC,KAAG,KAAK,QAAQ,IAAIA,GAAE,UAAUD,EAAC,GAAE,WAAWA,GAAE,YAAY,CAAC,EAAE;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,IAAAA,MAAG,KAAK,QAAQ,OAAO,WAAWA,GAAE,YAAY,CAAC,EAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAED,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,OAAM,IAAI,GAAEA,KAAE,EAAE,CAACK,GAAE,yCAAyC,CAAC,GAAEL,EAAC;AAAE,IAAMM,KAAEN;;;ACA1oC,IAAMO,KAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQJ,IAAE,KAAK,QAAMC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,YAAUC,IAAE,KAAK,OAAK;AAAA,EAAe;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQJ,IAAE,KAAK,QAAMC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,YAAUC,IAAE,KAAK,OAAK;AAAA,EAAsB;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQT,IAAE,KAAK,cAAYC,IAAE,KAAK,QAAMC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAGC,IAAE,KAAK,UAAQC,IAAE,KAAK,UAAQC,IAAE,KAAK,YAAUC,IAAE,KAAK,mBAAiB,OAAG,KAAK,OAAK;AAAA,EAAoB;AAAA,EAAC,iBAAgB;AAAC,SAAK,mBAAiB;AAAA,EAAE;AAAC;AAAC,IAAMN,KAAN,MAAO;AAAA,EAAC,YAAYH,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQT,IAAE,KAAK,cAAYC,IAAE,KAAK,QAAMC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAGC,IAAE,KAAK,UAAQC,IAAE,KAAK,UAAQC,IAAE,KAAK,YAAUC,IAAE,KAAK,mBAAiB,OAAG,KAAK,OAAK;AAAA,EAAc;AAAA,EAAC,iBAAgB;AAAC,SAAK,mBAAiB;AAAA,EAAE;AAAC;AAAC,IAAML,KAAN,MAAO;AAAA,EAAC,YAAYJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQT,IAAE,KAAK,cAAYC,IAAE,KAAK,QAAMC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAGC,IAAE,KAAK,UAAQC,IAAE,KAAK,UAAQC,IAAE,KAAK,YAAUC,IAAE,KAAK,mBAAiB,OAAG,KAAK,OAAK;AAAA,EAAmB;AAAA,EAAC,iBAAgB;AAAC,SAAK,mBAAiB;AAAA,EAAE;AAAC;AAAC,IAAMJ,KAAN,MAAO;AAAA,EAAC,YAAYL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQJ,IAAE,KAAK,QAAMC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,YAAUC,IAAE,KAAK,OAAK;AAAA,EAAsB;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQJ,IAAE,KAAK,QAAMC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,YAAUC,IAAE,KAAK,OAAK;AAAA,EAAqB;AAAC;AAAC,IAAMG,KAAN,MAAO;AAAA,EAAC,YAAYP,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQJ,IAAE,KAAK,QAAMC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,YAAUC,IAAE,KAAK,OAAK;AAAA,EAAsB;AAAC;AAAC,IAAMI,KAAN,MAAO;AAAA,EAAC,YAAYR,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAQJ,IAAE,KAAK,QAAMC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,YAAUC,IAAE,KAAK,OAAK;AAAA,EAAoB;AAAC;;;ACA3J,IAAMM,KAAE;AAAoB,IAAIC,KAAE,cAAc,EAAE,gBAAe;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,iBAAe,MAAK,KAAK,aAAW,MAAK,KAAK,WAAS,IAAIC,MAAE,KAAK,gBAAc,MAAK,KAAK,cAAY,CAAC,GAAE,KAAK,uBAAqB,MAAK,KAAK,eAAa,IAAIA,MAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,cAAY,MAAK,KAAK,OAAK,iBAAgB,KAAK,YAAU,EAAC,iBAAgB;AAAA,IAAC,GAAE,uBAAsB;AAAA,IAAC,GAAE,qBAAoB;AAAA,IAAC,GAAE,gBAAe;AAAA,IAAC,GAAE,oBAAmB;AAAA,IAAC,GAAE,uBAAsB;AAAA,IAAC,GAAE,sBAAqB;AAAA,IAAC,GAAE,uBAAsB;AAAA,IAAC,GAAE,qBAAoB;AAAA,IAAC,EAAC,GAAE,KAAK,wBAAsB,OAAG,KAAK,WAAS,CAAC,GAAE,KAAK,oBAAkB,OAAG,KAAK,QAAM,IAAI,EAAE,EAAC,UAAS,QAAO,UAAS,MAAG,OAAM,+BAA8B,CAAC,GAAE,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,IAAAC,GAAE,KAAK,MAAK,KAAK,KAAK,GAAE,KAAK,mBAAiB,IAAIC,GAAE,EAAC,MAAK,KAAK,KAAI,CAAC,GAAE,KAAK,QAAQ,GAAE,KAAK,SAAS,IAAI,CAAC,EAAG,MAAE;AAJ5sE;AAI8sE,cAAC,KAAK,WAAS,UAAK,aAAL,mBAAe,MAAM;AAAA,OAAI,MAAI,KAAK,QAAQ,CAAE,GAAE,EAAG,MAAE;AAJhxE;AAIkxE,wBAAK,SAAL,mBAAW;AAAA,OAAQ,MAAI;AAAC,WAAK,aAAa,IAAI,CAAC,KAAK,KAAK,GAAG,mBAAmB,CAAAH,OAAG,KAAK,cAAcA,EAAC,GAAG,EAAE,IAAI,GAAE,KAAK,KAAK,GAAG,gBAAgB,CAAAA,OAAG,KAAK,oBAAoBA,EAAC,GAAG,EAAE,IAAI,GAAE,KAAK,KAAK,GAAG,gBAAgB,CAAAA,OAAG,KAAK,oBAAoBA,EAAC,GAAG,EAAE,IAAI,GAAE,KAAK,KAAK,GAAG,gBAAgB,CAAAA,OAAG,KAAK,oBAAoBA,EAAC,GAAG,EAAE,IAAI,GAAE,KAAK,KAAK,GAAG,cAAc,CAAAA,OAAG,KAAK,kBAAkBA,EAAC,GAAG,EAAE,IAAI,GAAE,KAAK,KAAK,GAAG,QAAQ,CAAAA,OAAG,KAAK,aAAaA,EAAC,GAAG,EAAE,IAAI,GAAE,KAAK,KAAK,GAAG,YAAY,CAAAA,OAAG,KAAK,gBAAgBA,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAAA,IAAC,GAAG,EAAC,MAAK,MAAG,SAAQ,KAAE,CAAC,GAAE,EAAG,MAAI,KAAK,MAAO,CAAAA,OAAG;AAAC,WAAK,iBAAiB,UAAU,GAAE,KAAK,iBAAiB,OAAKA;AAAA,IAAC,CAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJp4F;AAIq4F,SAAK,kBAAkB,IAAE,UAAK,KAAK,QAAV,mBAAe,OAAO,KAAK,QAAO,KAAK,MAAM,QAAQ,GAAE,KAAK,MAAM,GAAE,KAAK,cAAc,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,gBAAc,MAAK,KAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,eAAa,EAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAkBA,IAAE;AAJroG;AAIsoG,eAAK,qBAAL,mBAAuB,aAAY,KAAK,KAAK,qBAAoBA,EAAC,GAAEA,QAAG,UAAK,qBAAL,mBAAuB,IAAI,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,QAAO;AAAC,UAAMA,KAAE,CAAC,CAAC,KAAK,IAAI,YAAY,GAAEC,KAAE,CAAC,CAAC,KAAK,IAAI,iBAAiB,GAAEC,KAAE,KAAK;AAAe,WAAOF,MAAGC,KAAEC,KAAE,WAAS,WAASF,KAAE,UAAQ;AAAA,EAAU;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,iBAAe,MAAK,KAAK,gBAAc,MAAK,KAAK,aAAW,MAAK,KAAK,iBAAiB,UAAU;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,SAASF,EAAC;AAAE,IAAAE,OAAIA,GAAE,IAAI,YAAWD,EAAC,GAAE,KAAK,iBAAiB;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,SAAK,iBAAiB,GAAE,KAAK,iBAAiB,GAAE,KAAK,mBAAmB,GAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAE;AAAC,UAAMC,KAAE,KAAK,mBAAmBG,GAAEJ,EAAC,CAAC;AAAE,QAAGC,IAAE;AAAC,YAAMC,KAAE,IAAID,GAAEA,IAAE,KAAK,SAAS,QAAQA,EAAC,GAAED,GAAE,GAAEA,GAAE,GAAEA,EAAC;AAAE,WAAK,KAAK,iBAAgBE,EAAC,GAAE,KAAK,UAAU,kBAAgB,KAAK,UAAU,eAAeA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAE;AAAC,UAAMC,KAAE,KAAK,mBAAmBG,GAAEJ,EAAC,CAAC;AAAE,QAAGC,IAAE;AAAC,YAAMC,KAAE,IAAI,EAAED,IAAE,KAAK,SAAS,QAAQA,EAAC,GAAED,GAAE,GAAEA,GAAE,GAAEA,EAAC;AAAE,WAAK,KAAK,wBAAuBE,EAAC,GAAE,KAAK,UAAU,wBAAsB,KAAK,UAAU,qBAAqBA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAE;AAAC,UAAMC,KAAE,KAAK,mBAAmBG,GAAEJ,EAAC,CAAC;AAAE,QAAGC,IAAE;AAAC,WAAK,iBAAeA;AAAE,YAAK,EAAC,GAAEC,IAAE,GAAEG,GAAC,IAAEL,IAAEM,KAAE,IAAIC,GAAEN,IAAE,KAAK,SAAS,QAAQA,EAAC,GAAEC,IAAEG,IAAEL,EAAC;AAAE,WAAK,KAAK,wBAAuBM,EAAC,GAAE,KAAK,UAAU,wBAAsB,KAAK,UAAU,qBAAqBA,EAAC;AAAA,IAAC,MAAM,MAAK,iBAAe;AAAA,EAAI;AAAA,EAAC,kBAAkBN,IAAE;AAAC,QAAG,KAAK,gBAAe;AAAC,YAAK,EAAC,GAAEC,IAAE,GAAEC,GAAC,IAAEF,IAAEK,KAAE,KAAK,SAAS,QAAQ,KAAK,cAAc,GAAEC,KAAE,IAAIF,GAAE,KAAK,gBAAeC,IAAEJ,IAAEC,IAAEF,EAAC;AAAE,WAAK,KAAK,sBAAqBM,EAAC,GAAE,KAAK,UAAU,sBAAoB,KAAK,UAAU,mBAAmBA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBN,IAAE;AAAC,QAAG,KAAK,WAAW;AAAO,UAAMC,KAAE,KAAK,mBAAmBG,GAAEJ,EAAC,CAAC;AAAE,QAAGC,IAAE;AAAC,YAAK,EAAC,GAAEC,IAAE,GAAEG,GAAC,IAAEL;AAAE,UAAG,KAAK,eAAc;AAAC,YAAG,KAAK,kBAAgBC,GAAE;AAAO,cAAMK,KAAE,KAAK,SAAS,QAAQ,KAAK,aAAa,GAAEH,KAAE,IAAI,EAAE,KAAK,SAASG,EAAC,GAAEA,IAAEJ,IAAEG,IAAEL,EAAC;AAAE,aAAK,gBAAc,MAAK,KAAK,KAAK,uBAAsBG,EAAC,GAAE,KAAK,UAAU,uBAAqB,KAAK,UAAU,oBAAoBA,EAAC;AAAA,MAAC;AAAC,YAAMG,KAAE,KAAK,SAAS,QAAQL,EAAC,GAAEE,KAAE,IAAIG,GAAEL,IAAEK,IAAEJ,IAAEG,IAAEL,EAAC;AAAE,aAAO,KAAK,gBAAcC,IAAE,KAAK,KAAK,wBAAuBE,EAAC,GAAE,MAAK,KAAK,UAAU,wBAAsB,KAAK,UAAU,qBAAqBA,EAAC;AAAA,IAAE;AAAC,QAAG,KAAK,eAAc;AAAC,YAAK,EAAC,GAAEF,IAAE,GAAEC,GAAC,IAAEF,IAAEK,KAAE,KAAK,SAAS,QAAQ,KAAK,aAAa,GAAEC,KAAE,IAAI,EAAE,KAAK,SAASD,EAAC,GAAEA,IAAEJ,IAAEC,IAAEF,EAAC;AAAE,WAAK,gBAAc,MAAK,KAAK,KAAK,uBAAsBM,EAAC,GAAE,KAAK,UAAU,uBAAqB,KAAK,UAAU,oBAAoBA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaN,IAAE;AAAC,QAAG,YAAUA,GAAE,UAAQ,CAAC,KAAK,cAAY,CAAC,KAAK,kBAAgB,CAAC,KAAK,eAAe,SAAS;AAAO,gBAAUA,GAAE,UAAQ,KAAK,kBAAkB,GAAEA,GAAE,gBAAgB;AAAE,UAAK,EAAC,QAAOC,IAAE,GAAEC,IAAE,GAAEI,GAAC,IAAEN,IAAEG,KAAE,KAAK,SAAS,QAAQ,KAAK,cAAc,GAAEI,KAAE,KAAK,aAAWL,KAAE,KAAK,WAAW,IAAE,GAAE,IAAE,KAAK,aAAWI,KAAE,KAAK,WAAW,IAAE,GAAEE,KAAEN,KAAEF,GAAE,OAAO,GAAEI,KAAEE,KAAEN,GAAE,OAAO,GAAES,KAAE,YAAUR,KAAE,KAAK,eAAe,WAAS,KAAK,sBAAqBS,KAAEJ,GAAEG,IAAED,IAAEJ,IAAE,KAAK,IAAI;AAAE,QAAG,KAAK,eAAe,WAASM,IAAE,KAAK,yBAAuB,KAAK,SAAS,QAAS,CAAAV,OAAG;AAAC,MAAAA,OAAI,KAAK,mBAAiBA,GAAE,WAASM,GAAEN,GAAE,UAASO,IAAE,GAAE,KAAK,IAAI;AAAA,IAAE,CAAE,GAAE,KAAK,aAAWP,IAAE,YAAUC,IAAE;AAAC,WAAK,uBAAqB,EAAEQ,EAAC;AAAE,YAAMR,KAAE,IAAI,EAAE,KAAK,gBAAe,KAAK,UAASE,IAAED,IAAEI,IAAEC,IAAE,GAAEC,IAAEJ,IAAEJ,EAAC;AAAE,WAAK,KAAK,sBAAqBC,EAAC,GAAE,KAAK,UAAU,sBAAoB,KAAK,UAAU,mBAAmBA,EAAC,GAAEA,GAAE,oBAAkB,KAAK,eAAe,IAAI,YAAWQ,EAAC;AAAA,IAAC,WAAS,aAAWR,IAAE;AAAC,YAAMA,KAAE,IAAIE,GAAE,KAAK,gBAAe,KAAK,UAASA,IAAED,IAAEI,IAAEC,IAAE,GAAEC,IAAEJ,IAAEJ,EAAC;AAAE,WAAK,KAAK,gBAAeC,EAAC,GAAE,KAAK,UAAU,iBAAe,KAAK,UAAU,cAAcA,EAAC,GAAEA,GAAE,qBAAmB,KAAK,eAAe,WAASQ;AAAA,IAAE,OAAK;AAAC,YAAMR,KAAE,IAAIC,GAAE,KAAK,gBAAe,KAAK,UAASC,IAAED,IAAEI,IAAEC,IAAE,GAAEC,IAAEJ,IAAEJ,EAAC;AAAE,WAAK,aAAW,MAAK,KAAK,iBAAe,MAAK,KAAK,iBAAiB,GAAE,KAAK,KAAK,qBAAoBC,EAAC,GAAE,KAAK,UAAU,qBAAmB,KAAK,UAAU,kBAAkBA,EAAC,GAAEA,GAAE,qBAAmB,KAAK,SAASE,EAAC,EAAE,IAAI,YAAW,KAAK,oBAAoB,GAAE,KAAK,iBAAiB,IAAG,KAAK,uBAAqB;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,gBAAgBH,IAAE;AAAC,YAAMA,GAAE,OAAK,QAAMA,GAAE,OAAK,QAAMA,GAAE,OAAK,aAAW,KAAK,SAAOA,GAAE,gBAAgB;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,UAAMC,KAAE,KAAK,KAAK,MAAMD,EAAC;AAAE,QAAIE,KAAE,MAAKG,KAAE,OAAO;AAAU,SAAK,gBAAgB;AAAE,UAAMC,KAAE,KAAK,YAAY,QAAS,CAAAN,OAAG,mBAAkBA,KAAE,MAAM,KAAKA,GAAE,cAAc,GAAG,CAAAA,OAAGA,GAAE,QAAQC,EAAC,CAAE,EAAE,KAAK,IAAED,GAAE,aAAa,QAAQC,EAAC,CAAE,EAAE,OAAQ,CAAAD,OAAG,KAAK,SAAS,SAASA,EAAC,CAAE;AAAE,WAAOM,GAAE,SAAOA,GAAE,CAAC,KAAG,KAAK,cAAc,QAAS,CAAAL,OAAG;AAAC,YAAMK,KAAEL,GAAE,qBAAqBD,EAAC;AAAE,QAAEM,EAAC,KAAGA,KAAED,OAAIA,KAAEC,IAAEJ,KAAED,GAAE;AAAA,IAAQ,CAAE,GAAEC;AAAA,EAAE;AAAA,EAAC,kBAAiB;AAAC,SAAK,cAAY,CAAC;AAAE,UAAMF,KAAE,oBAAI;AAAI,eAAUC,MAAK,KAAK,UAAS;AAAC,YAAMC,KAAEE,GAAE,KAAK,MAAKH,GAAE,KAAK;AAAE,MAAAC,MAAGF,GAAE,IAAIE,EAAC;AAAA,IAAC;AAAC,SAAK,cAAY,CAAC,GAAGF,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAK,EAAC,UAASA,IAAE,MAAKC,GAAC,IAAE;AAAK,SAAK,cAAc,QAAS,CAAAD,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,iBAAcA,MAAA,gBAAAA,GAAG,UAAOA,GAAE,IAAK,CAAAA,OAAG,IAAI,EAAE,EAAC,SAAQA,IAAE,MAAKC,GAAC,CAAC,CAAE,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,qBAAmB,KAAK,iBAAiB,IAAI,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,QAAG,KAAK,kBAAkB,GAAE,KAAK,mBAAkB;AAAC,iBAAUD,MAAK,KAAK,UAAS;AAAC,cAAMC,KAAED,GAAE,MAAM;AAAE,QAAAC,GAAE,SAAO,KAAK,uBAAuBD,EAAC,GAAE,KAAK,YAAY,KAAKC,EAAC,GAAE,KAAK,SAAS,IAAI,EAAG,MAAID,GAAE,QAAS,MAAI,KAAK,iBAAiB,CAAE,GAAEF,EAAC;AAAA,MAAC;AAAC,WAAK,MAAM,QAAQ,KAAK,WAAW;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,SAAK,SAAS,OAAOA,EAAC,GAAE,KAAK,YAAY,WAAS,KAAK,MAAM,WAAW,KAAK,WAAW,GAAE,KAAK,YAAY,QAAS,CAAAE,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,cAAY,CAAC;AAAA,EAAE;AAAA,EAAC,uBAAuBA,IAAE;AAAC,UAAMC,KAAE;AAAG,QAAG,EAAED,GAAE,MAAM,EAAE,QAAO;AAAK,YAAOA,GAAE,OAAO,MAAK;AAAA,MAAC,KAAI;AAAM,eAAO,IAAIW,GAAE,EAAC,OAAM,UAAS,MAAKV,IAAE,OAAM,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,SAAQ,EAAC,OAAM,CAAC,KAAI,KAAI,GAAE,CAAC,GAAE,OAAM,EAAC,EAAC,CAAC;AAAA,MAAE,KAAI,kBAAiB;AAAC,cAAK,EAAC,SAAQA,IAAE,SAAQC,IAAE,QAAOG,IAAE,OAAMC,GAAC,IAAEN,GAAE,QAAOG,KAAEE,OAAIC,KAAEA,KAAE,KAAK,IAAID,IAAEC,EAAC;AAAE,eAAO,IAAIK,GAAE,EAAC,SAAQV,IAAE,SAAQC,IAAE,MAAKC,IAAE,OAAM,UAAS,OAAM,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,SAAQ,EAAC,OAAM,CAAC,KAAI,KAAI,GAAE,CAAC,GAAE,OAAM,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA,MAAC,KAAI,iBAAgB;AAAC,cAAK,EAAC,SAAQF,IAAE,SAAQC,IAAE,MAAKG,IAAE,OAAMC,GAAC,IAAEN,GAAE;AAAO,eAAO,IAAIW,GAAE,EAAC,SAAQV,IAAE,SAAQC,IAAE,OAAM,aAAWI,KAAE,WAAS,UAAS,MAAKD,KAAE,GAAE,OAAM,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,SAAQ,EAAC,OAAM,CAAC,KAAI,KAAI,GAAE,CAAC,GAAE,OAAM,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA,MAAC,KAAI;AAAc,eAAO,IAAI,EAAE,EAAC,OAAM,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,SAAQ,EAAC,OAAM,QAAO,OAAM,CAAC,KAAI,KAAI,GAAE,CAAC,GAAE,OAAM,EAAC,EAAC,CAAC;AAAA,MAAE,KAAI;AAAc,eAAO,IAAI,EAAE,EAAC,OAAM,CAAC,KAAI,KAAI,GAAE,CAAC,GAAE,OAAM,QAAO,OAAM,EAAC,CAAC;AAAA,MAAE,KAAI,QAAO;AAAC,cAAK,EAAC,SAAQH,IAAE,SAAQG,GAAC,IAAEL,GAAE;AAAO,eAAO,IAAIW,GAAE,EAAC,SAAQT,IAAE,SAAQG,IAAE,MAAKJ,IAAE,OAAM,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,SAAQ,EAAC,OAAM,CAAC,KAAI,KAAI,GAAE,CAAC,GAAE,OAAM,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA,MAAC;AAAQ,eAAO;AAAA,IAAI;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAACQ,GAAE,sCAAsC,CAAC,GAAER,EAAC;AAAE,IAAM,IAAEA;", "names": ["l", "r", "e", "n", "t", "a", "h", "t", "i", "s", "h", "e", "r", "c", "a", "n", "p", "P", "S", "i", "t", "e", "h", "n", "s", "r", "a", "c", "l", "p", "y"]}