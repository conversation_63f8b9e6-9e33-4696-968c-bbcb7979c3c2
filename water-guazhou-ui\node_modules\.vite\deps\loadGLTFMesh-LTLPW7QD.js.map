{"version": 3, "sources": ["../../@arcgis/core/geometry/support/meshUtils/loadGLTFMesh.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../Color.js\";import t from\"../../../request.js\";import{getOrCreateMapValue as r}from\"../../../core/MapUtils.js\";import{hasScaling as o}from\"../../../core/mathUtils.js\";import{isSome as n,unwrap as s,applySome as i,isNone as a}from\"../../../core/maybe.js\";import{b as c}from\"../../../chunks/mat3.js\";import{c as u}from\"../../../chunks/mat3f64.js\";import{f as l}from\"../../../chunks/vec3f64.js\";import{f}from\"../../../chunks/vec4f64.js\";import m from\"../MeshComponent.js\";import p from\"../MeshMaterialMetallicRoughness.js\";import d from\"../MeshTexture.js\";import{MeshVertexAttributes as g}from\"../MeshVertexAttributes.js\";import{BufferViewVec4f as x,BufferViewVec4u8 as T,BufferViewVec4u16 as h,BufferViewVec3u8 as v,BufferViewVec3f as w,BufferViewVec3u16 as b,BufferViewVec3f64 as j,BufferViewVec2f as y}from\"../buffer/BufferView.js\";import{t as A,a as M,n as C,s as R,b as E}from\"../../../chunks/vec32.js\";import{t as k,n as B,s as F,a as I}from\"../../../chunks/vec42.js\";import{createBuffer as $}from\"../buffer/utils.js\";import{georeferenceByTransform as L}from\"./georeference.js\";import{DefaultLoadingContext as S}from\"../../../views/3d/glTF/DefaultLoadingContext.js\";import{loadGLTF as N}from\"../../../views/3d/glTF/loader.js\";import{triangleFanToTriangles as O,triangleStripToTriangles as _,trianglesToTriangles as D}from\"../../../views/3d/glTF/internal/indexUtils.js\";import{isEncodedMeshTexture as G}from\"../../../views/3d/glTF/internal/resourceUtils.js\";import{generateIndexArray as P}from\"../../../views/3d/webgl-engine/lib/Indices.js\";import{COLOR_GAMMA as U}from\"../../../views/3d/webgl-engine/materials/DefaultMaterial_COLOR_GAMMA.js\";import{PrimitiveType as V,TextureWrapMode as q}from\"../../../views/webgl/enums.js\";import{f as z,c as K}from\"../../../chunks/vec33.js\";import{f as Q,c as H}from\"../../../chunks/vec43.js\";import{n as J,f as W}from\"../../../chunks/vec22.js\";async function X(e,t,r){const o=new S(Y(r)),s=(await N(o,t,r,!0)).model,i=s.lods.shift(),a=new Map,c=new Map;s.textures.forEach(((e,t)=>a.set(t,re(e)))),s.materials.forEach(((e,t)=>c.set(t,oe(e,a))));const u=te(i);for(const n of u.parts)ne(u,n,c);const{position:l,normal:f,tangent:m,color:p,texCoord0:d}=u.vertexAttributes,x={position:l.typedBuffer,normal:n(f)?f.typedBuffer:null,tangent:n(m)?m.typedBuffer:null,uv:n(d)?d.typedBuffer:null,color:n(p)?p.typedBuffer:null},T=L(x,e,r);return{transform:T.transform,components:u.components,spatialReference:e.spatialReference,vertexAttributes:new g({position:T.vertexAttributes.position,normal:T.vertexAttributes.normal,tangent:T.vertexAttributes.tangent,color:x.color,uv:x.uv})}}function Y(e){const r=e?.resolveFile;return r?{busy:!1,request:async(e,o,s)=>{const i=r(e),a=\"image\"===o?\"image\":\"binary\"===o?\"array-buffer\":\"json\";return(await t(i,{responseType:a,signal:n(s)?s.signal:null})).data}}:null}function Z(e,t){if(a(e))return\"-\";const o=e.typedBuffer;return`${r(t,o.buffer,(()=>t.size))}/${o.byteOffset}/${o.byteLength}`}function ee(e){return n(e)?e.toString():\"-\"}function te(e){let t=0;const has={color:!1,tangent:!1,normal:!1,texCoord0:!1},o=new Map,n=new Map,s=[];for(const i of e.parts){const{attributes:{position:e,normal:a,color:c,tangent:u,texCoord0:l}}=i,f=`\\n      ${Z(e,o)}/\\n      ${Z(a,o)}/\\n      ${Z(c,o)}/\\n      ${Z(u,o)}/\\n      ${Z(l,o)}/\\n      ${ee(i.transform)}\\n    `;let m=!1;const p=r(n,f,(()=>(m=!0,{start:t,length:e.count})));m&&(t+=e.count),a&&(has.normal=!0),c&&(has.color=!0),u&&(has.tangent=!0),l&&(has.texCoord0=!0),s.push({gltf:i,writeVertices:m,region:p})}return{vertexAttributes:{position:$(j,t),normal:has.normal?$(w,t):null,tangent:has.tangent?$(x,t):null,color:has.color?$(T,t):null,texCoord0:has.texCoord0?$(y,t):null},parts:s,components:[]}}function re(e){return new d({data:(G(e.data),e.data),wrap:ce(e.parameters.wrap)})}function oe(t,r){const o=new e(fe(t.color,t.opacity)),n=t.emissiveFactor?new e(me(t.emissiveFactor)):null;return new p({color:o,colorTexture:s(i(t.textureColor,(e=>r.get(e)))),normalTexture:s(i(t.textureNormal,(e=>r.get(e)))),emissiveColor:n,emissiveTexture:s(i(t.textureEmissive,(e=>r.get(e)))),occlusionTexture:s(i(t.textureOcclusion,(e=>r.get(e)))),alphaMode:ae(t.alphaMode),alphaCutoff:t.alphaCutoff,doubleSided:t.doubleSided,metallic:t.metallicFactor,roughness:t.roughnessFactor,metallicRoughnessTexture:s(i(t.textureMetallicRoughness,(e=>r.get(e)))),colorTextureTransform:t.colorTextureTransform,normalTextureTransform:t.normalTextureTransform,occlusionTextureTransform:t.occlusionTextureTransform,emissiveTextureTransform:t.emissiveTextureTransform,metallicRoughnessTextureTransform:t.metallicRoughnessTextureTransform})}function ne(e,t,r){t.writeVertices&&se(e,t);const o=t.gltf,n=ie(o.indices||o.attributes.position.count,o.primitiveType),s=t.region.start;if(s)for(let i=0;i<n.length;i++)n[i]+=s;e.components.push(new m({faces:n,material:r.get(o.material),trustSourceNormals:!0}))}function se(e,t){const{position:r,normal:s,tangent:i,color:a,texCoord0:l}=e.vertexAttributes,f=t.region.start,{attributes:m,transform:p}=t.gltf,d=m.position.count;if(A(r.slice(f,d),m.position,p),n(m.normal)&&n(s)){const e=c(u(),p),t=s.slice(f,d);M(t,m.normal,e),o(e)&&C(t,t)}else n(s)&&z(s,0,0,1,{dstIndex:f,count:d});if(n(m.tangent)&&n(i)){const e=c(u(),p),t=i.slice(f,d);k(t,m.tangent,e),o(e)&&B(t,t)}else n(i)&&Q(i,0,0,1,1,{dstIndex:f,count:d});if(n(m.texCoord0)&&n(l)?J(l.slice(f,d),m.texCoord0):n(l)&&W(l,0,0,{dstIndex:f,count:d}),n(m.color)&&n(a)){const e=m.color,t=a.slice(f,d);if(4===e.elementCount)e instanceof x?F(t,e,255):e instanceof T?H(t,e):e instanceof h&&I(t,e,8);else{Q(t,255,255,255,255);const r=v.fromTypedArray(t.typedBuffer,t.typedBufferStride);e instanceof w?R(r,e,255):e instanceof v?K(r,e):e instanceof b&&E(r,e,8)}}else n(a)&&Q(a.slice(f,d),255,255,255,255)}function ie(e,t){switch(t){case V.TRIANGLES:return D(e,P);case V.TRIANGLE_STRIP:return _(e);case V.TRIANGLE_FAN:return O(e)}}function ae(e){switch(e){case\"OPAQUE\":return\"opaque\";case\"MASK\":return\"mask\";case\"BLEND\":return\"blend\"}}function ce(e){return{horizontal:ue(e.s),vertical:ue(e.t)}}function ue(e){switch(e){case q.CLAMP_TO_EDGE:return\"clamp\";case q.MIRRORED_REPEAT:return\"mirror\";case q.REPEAT:return\"repeat\"}}function le(e){return e**(1/U)*255}function fe(e,t){return f(le(e[0]),le(e[1]),le(e[2]),t)}function me(e){return l(le(e[0]),le(e[1]),le(e[2]))}export{X as loadGLTFMesh};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIi3D,eAAe,EAAEA,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,IAAIC,GAAE,EAAEF,EAAC,CAAC,GAAE,KAAG,MAAMG,GAAEF,IAAEF,IAAEC,IAAE,IAAE,GAAG,OAAMI,KAAE,EAAE,KAAK,MAAM,GAAE,IAAE,oBAAI,OAAIC,KAAE,oBAAI;AAAI,IAAE,SAAS,QAAS,CAACP,IAAEC,OAAI,EAAE,IAAIA,IAAE,GAAGD,EAAC,CAAC,CAAE,GAAE,EAAE,UAAU,QAAS,CAACA,IAAEC,OAAIM,GAAE,IAAIN,IAAE,GAAGD,IAAE,CAAC,CAAC,CAAE;AAAE,QAAMQ,KAAE,GAAGF,EAAC;AAAE,aAAUF,MAAKI,GAAE,MAAM,IAAGA,IAAEJ,IAAEG,EAAC;AAAE,QAAK,EAAC,UAASE,IAAE,QAAOC,IAAE,SAAQL,IAAE,OAAMM,IAAE,WAAUC,GAAC,IAAEJ,GAAE,kBAAiBK,KAAE,EAAC,UAASJ,GAAE,aAAY,QAAO,EAAEC,EAAC,IAAEA,GAAE,cAAY,MAAK,SAAQ,EAAEL,EAAC,IAAEA,GAAE,cAAY,MAAK,IAAG,EAAEO,EAAC,IAAEA,GAAE,cAAY,MAAK,OAAM,EAAED,EAAC,IAAEA,GAAE,cAAY,KAAI,GAAEG,KAAE,EAAED,IAAEb,IAAEE,EAAC;AAAE,SAAM,EAAC,WAAUY,GAAE,WAAU,YAAWN,GAAE,YAAW,kBAAiBR,GAAE,kBAAiB,kBAAiB,IAAI,EAAE,EAAC,UAASc,GAAE,iBAAiB,UAAS,QAAOA,GAAE,iBAAiB,QAAO,SAAQA,GAAE,iBAAiB,SAAQ,OAAMD,GAAE,OAAM,IAAGA,GAAE,GAAE,CAAC,EAAC;AAAC;AAAC,SAAS,EAAEb,IAAE;AAAC,QAAME,KAAEF,MAAA,gBAAAA,GAAG;AAAY,SAAOE,KAAE,EAAC,MAAK,OAAG,SAAQ,OAAMF,IAAEG,IAAE,MAAI;AAAC,UAAMG,KAAEJ,GAAEF,EAAC,GAAE,IAAE,YAAUG,KAAE,UAAQ,aAAWA,KAAE,iBAAe;AAAO,YAAO,MAAM,EAAEG,IAAE,EAAC,cAAa,GAAE,QAAO,EAAE,CAAC,IAAE,EAAE,SAAO,KAAI,CAAC,GAAG;AAAA,EAAI,EAAC,IAAE;AAAI;AAAC,SAAS,EAAEN,IAAEC,IAAE;AAAC,MAAG,EAAED,EAAC,EAAE,QAAM;AAAI,QAAMG,KAAEH,GAAE;AAAY,SAAM,GAAGE,GAAED,IAAEE,GAAE,QAAQ,MAAIF,GAAE,IAAK,CAAC,IAAIE,GAAE,UAAU,IAAIA,GAAE,UAAU;AAAE;AAAC,SAAS,GAAGH,IAAE;AAAC,SAAO,EAAEA,EAAC,IAAEA,GAAE,SAAS,IAAE;AAAG;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAE;AAAE,QAAM,MAAI,EAAC,OAAM,OAAG,SAAQ,OAAG,QAAO,OAAG,WAAU,MAAE,GAAEE,KAAE,oBAAI,OAAIC,KAAE,oBAAI,OAAI,IAAE,CAAC;AAAE,aAAUE,MAAKN,GAAE,OAAM;AAAC,UAAK,EAAC,YAAW,EAAC,UAASA,IAAE,QAAO,GAAE,OAAMO,IAAE,SAAQC,IAAE,WAAUC,GAAC,EAAC,IAAEH,IAAEI,KAAE;AAAA,QAAW,EAAEV,IAAEG,EAAC,CAAC;AAAA,QAAY,EAAE,GAAEA,EAAC,CAAC;AAAA,QAAY,EAAEI,IAAEJ,EAAC,CAAC;AAAA,QAAY,EAAEK,IAAEL,EAAC,CAAC;AAAA,QAAY,EAAEM,IAAEN,EAAC,CAAC;AAAA,QAAY,GAAGG,GAAE,SAAS,CAAC;AAAA;AAAS,QAAID,KAAE;AAAG,UAAMM,KAAET,GAAEE,IAAEM,IAAG,OAAKL,KAAE,MAAG,EAAC,OAAMJ,IAAE,QAAOD,GAAE,MAAK,EAAG;AAAE,IAAAK,OAAIJ,MAAGD,GAAE,QAAO,MAAI,IAAI,SAAO,OAAIO,OAAI,IAAI,QAAM,OAAIC,OAAI,IAAI,UAAQ,OAAIC,OAAI,IAAI,YAAU,OAAI,EAAE,KAAK,EAAC,MAAKH,IAAE,eAAcD,IAAE,QAAOM,GAAC,CAAC;AAAA,EAAC;AAAC,SAAM,EAAC,kBAAiB,EAAC,UAAST,GAAE,GAAED,EAAC,GAAE,QAAO,IAAI,SAAOC,GAAE,GAAED,EAAC,IAAE,MAAK,SAAQ,IAAI,UAAQC,GAAE,GAAED,EAAC,IAAE,MAAK,OAAM,IAAI,QAAMC,GAAE,GAAED,EAAC,IAAE,MAAK,WAAU,IAAI,YAAUC,GAAE,GAAED,EAAC,IAAE,KAAI,GAAE,OAAM,GAAE,YAAW,CAAC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,SAAO,IAAI,EAAE,EAAC,OAAMC,GAAED,GAAE,IAAI,GAAEA,GAAE,OAAM,MAAK,GAAGA,GAAE,WAAW,IAAI,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGC,IAAEC,IAAE;AAAC,QAAMC,KAAE,IAAI,EAAE,GAAGF,GAAE,OAAMA,GAAE,OAAO,CAAC,GAAEG,KAAEH,GAAE,iBAAe,IAAI,EAAE,GAAGA,GAAE,cAAc,CAAC,IAAE;AAAK,SAAO,IAAIM,GAAE,EAAC,OAAMJ,IAAE,cAAa,EAAE,EAAEF,GAAE,cAAc,CAAAD,OAAGE,GAAE,IAAIF,EAAC,CAAE,CAAC,GAAE,eAAc,EAAE,EAAEC,GAAE,eAAe,CAAAD,OAAGE,GAAE,IAAIF,EAAC,CAAE,CAAC,GAAE,eAAcI,IAAE,iBAAgB,EAAE,EAAEH,GAAE,iBAAiB,CAAAD,OAAGE,GAAE,IAAIF,EAAC,CAAE,CAAC,GAAE,kBAAiB,EAAE,EAAEC,GAAE,kBAAkB,CAAAD,OAAGE,GAAE,IAAIF,EAAC,CAAE,CAAC,GAAE,WAAU,GAAGC,GAAE,SAAS,GAAE,aAAYA,GAAE,aAAY,aAAYA,GAAE,aAAY,UAASA,GAAE,gBAAe,WAAUA,GAAE,iBAAgB,0BAAyB,EAAE,EAAEA,GAAE,0BAA0B,CAAAD,OAAGE,GAAE,IAAIF,EAAC,CAAE,CAAC,GAAE,uBAAsBC,GAAE,uBAAsB,wBAAuBA,GAAE,wBAAuB,2BAA0BA,GAAE,2BAA0B,0BAAyBA,GAAE,0BAAyB,mCAAkCA,GAAE,kCAAiC,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,EAAAD,GAAE,iBAAe,GAAGD,IAAEC,EAAC;AAAE,QAAME,KAAEF,GAAE,MAAKG,KAAE,GAAGD,GAAE,WAASA,GAAE,WAAW,SAAS,OAAMA,GAAE,aAAa,GAAE,IAAEF,GAAE,OAAO;AAAM,MAAG,EAAE,UAAQK,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAF,GAAEE,EAAC,KAAG;AAAE,EAAAN,GAAE,WAAW,KAAK,IAAIe,GAAE,EAAC,OAAMX,IAAE,UAASF,GAAE,IAAIC,GAAE,QAAQ,GAAE,oBAAmB,KAAE,CAAC,CAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAE;AAAC,QAAK,EAAC,UAASC,IAAE,QAAO,GAAE,SAAQI,IAAE,OAAM,GAAE,WAAUG,GAAC,IAAET,GAAE,kBAAiBU,KAAET,GAAE,OAAO,OAAM,EAAC,YAAWI,IAAE,WAAUM,GAAC,IAAEV,GAAE,MAAKW,KAAEP,GAAE,SAAS;AAAM,MAAGJ,GAAEC,GAAE,MAAMQ,IAAEE,EAAC,GAAEP,GAAE,UAASM,EAAC,GAAE,EAAEN,GAAE,MAAM,KAAG,EAAE,CAAC,GAAE;AAAC,UAAML,KAAE,EAAEA,GAAE,GAAEW,EAAC,GAAEV,KAAE,EAAE,MAAMS,IAAEE,EAAC;AAAE,IAAAV,GAAED,IAAEI,GAAE,QAAOL,EAAC,GAAE,EAAEA,EAAC,KAAGG,GAAEF,IAAEA,EAAC;AAAA,EAAC,MAAM,GAAE,CAAC,KAAGA,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,UAASS,IAAE,OAAME,GAAC,CAAC;AAAE,MAAG,EAAEP,GAAE,OAAO,KAAG,EAAEC,EAAC,GAAE;AAAC,UAAMN,KAAE,EAAEA,GAAE,GAAEW,EAAC,GAAEV,KAAEK,GAAE,MAAMI,IAAEE,EAAC;AAAE,IAAAV,GAAED,IAAEI,GAAE,SAAQL,EAAC,GAAE,EAAEA,EAAC,KAAGU,GAAET,IAAEA,EAAC;AAAA,EAAC,MAAM,GAAEK,EAAC,KAAGL,GAAEK,IAAE,GAAE,GAAE,GAAE,GAAE,EAAC,UAASI,IAAE,OAAME,GAAC,CAAC;AAAE,MAAG,EAAEP,GAAE,SAAS,KAAG,EAAEI,EAAC,IAAEL,GAAEK,GAAE,MAAMC,IAAEE,EAAC,GAAEP,GAAE,SAAS,IAAE,EAAEI,EAAC,KAAGG,GAAEH,IAAE,GAAE,GAAE,EAAC,UAASC,IAAE,OAAME,GAAC,CAAC,GAAE,EAAEP,GAAE,KAAK,KAAG,EAAE,CAAC,GAAE;AAAC,UAAML,KAAEK,GAAE,OAAMJ,KAAE,EAAE,MAAMS,IAAEE,EAAC;AAAE,QAAG,MAAIZ,GAAE,aAAa,CAAAA,cAAa,IAAEG,GAAEF,IAAED,IAAE,GAAG,IAAEA,cAAa,IAAEA,GAAEC,IAAED,EAAC,IAAEA,cAAa,KAAGI,GAAEH,IAAED,IAAE,CAAC;AAAA,SAAM;AAAC,MAAAC,GAAEA,IAAE,KAAI,KAAI,KAAI,GAAG;AAAE,YAAMC,KAAE,EAAE,eAAeD,GAAE,aAAYA,GAAE,iBAAiB;AAAE,MAAAD,cAAa,IAAE,EAAEE,IAAEF,IAAE,GAAG,IAAEA,cAAa,IAAEA,GAAEE,IAAEF,EAAC,IAAEA,cAAa,KAAG,EAAEE,IAAEF,IAAE,CAAC;AAAA,IAAC;AAAA,EAAC,MAAM,GAAE,CAAC,KAAGC,GAAE,EAAE,MAAMS,IAAEE,EAAC,GAAE,KAAI,KAAI,KAAI,GAAG;AAAC;AAAC,SAAS,GAAGZ,IAAEC,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAKe,GAAE;AAAU,aAAOb,GAAEH,IAAEQ,EAAC;AAAA,IAAE,KAAKQ,GAAE;AAAe,aAAON,GAAEV,EAAC;AAAA,IAAE,KAAKgB,GAAE;AAAa,aAAOV,GAAEN,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAI;AAAS,aAAM;AAAA,IAAS,KAAI;AAAO,aAAM;AAAA,IAAO,KAAI;AAAQ,aAAM;AAAA,EAAO;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAC,YAAW,GAAGA,GAAE,CAAC,GAAE,UAAS,GAAGA,GAAE,CAAC,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK,EAAE;AAAc,aAAM;AAAA,IAAQ,KAAK,EAAE;AAAgB,aAAM;AAAA,IAAS,KAAK,EAAE;AAAO,aAAM;AAAA,EAAQ;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,OAAI,IAAEG,MAAG;AAAG;AAAC,SAAS,GAAGH,IAAEC,IAAE;AAAC,SAAOC,GAAE,GAAGF,GAAE,CAAC,CAAC,GAAE,GAAGA,GAAE,CAAC,CAAC,GAAE,GAAGA,GAAE,CAAC,CAAC,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,SAAOE,GAAE,GAAGF,GAAE,CAAC,CAAC,GAAE,GAAGA,GAAE,CAAC,CAAC,GAAE,GAAGA,GAAE,CAAC,CAAC,CAAC;AAAC;", "names": ["e", "t", "r", "o", "n", "m", "i", "c", "u", "l", "f", "p", "d", "x", "T", "g", "E"]}