{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/lib/UpdatePolicy.js", "../../@arcgis/core/views/3d/layers/interfaces.js", "../../@arcgis/core/views/3d/layers/graphics/graphicUtils.js", "../../@arcgis/core/layers/graphics/dehydratedFeatureUtils.js", "../../@arcgis/core/views/3d/support/ElevationProvider.js", "../../@arcgis/core/views/3d/layers/graphics/elevationAlignmentUtils.js", "../../@arcgis/core/views/3d/layers/graphics/featureExpressionInfoUtils.js", "../../@arcgis/core/views/3d/layers/graphics/ElevationContext.js", "../../@arcgis/core/views/3d/webgl-engine/lib/Object3D.js", "../../@arcgis/core/views/3d/webgl-engine/lib/DirtyEvents.js", "../../@arcgis/core/views/3d/webgl-engine/lib/WebGLLayer.js", "../../@arcgis/core/views/3d/webgl-engine/lib/fov.js", "../../@arcgis/core/views/3d/webgl-engine/lib/Camera.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/RibbonLineTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/materials/RibbonLineMaterial.js", "../../@arcgis/core/views/3d/webgl-engine/lib/LocalOriginFactory.js", "../../@arcgis/core/views/3d/webgl-engine/lib/testUtils.js", "../../@arcgis/core/views/3d/webgl-engine/lib/GridLocalOriginFactory.js", "../../@arcgis/core/views/3d/webgl-engine/lib/IntersectorInterfaces.js", "../../@arcgis/core/views/3d/webgl-engine/lib/IntersectorTarget.js", "../../@arcgis/core/views/3d/webgl-engine/lib/intersectorUtils.js", "../../@arcgis/core/views/3d/webgl-engine/lib/Intersector.js", "../../@arcgis/core/views/3d/terrain/Overlay.js", "../../@arcgis/core/views/3d/terrain/OverlayFramebufferObject.js", "../../@arcgis/core/views/3d/terrain/OverlayRenderTarget.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderTechnique/ShaderTechniqueRepository.js", "../../@arcgis/core/views/3d/webgl-engine/lib/GLMaterialRepository.js", "../../@arcgis/core/views/3d/webgl-engine/lib/BindParameters.js", "../../@arcgis/core/views/3d/webgl-engine/lib/RenderContext.js", "../../@arcgis/core/views/3d/webgl-engine/lib/CascadeCamera.js", "../../@arcgis/core/views/3d/webgl-engine/lib/ShadowMap.js", "../../@arcgis/core/views/3d/terrain/Intersector.js", "../../@arcgis/core/views/3d/webgl-engine/lib/ChangeSet.js", "../../@arcgis/core/views/3d/webgl-engine/lib/ModelDirtyTypes.js", "../../@arcgis/core/views/3d/webgl-engine/lib/rendererUtils.js", "../../@arcgis/core/views/3d/webgl-engine/lib/AppleAmdDriverHelper.js", "../../@arcgis/core/views/3d/webgl-engine/lib/GLMaterials.js", "../../@arcgis/core/views/3d/webgl-engine/materials/DrawParameters.js", "../../@arcgis/core/views/3d/webgl-engine/materials/DefaultLayouts.js", "../../@arcgis/core/views/3d/webgl-engine/materials/TriangleMaterial.js", "../../@arcgis/core/views/3d/webgl-engine/materials/WaterTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/materials/WaterGLMaterial.js", "../../@arcgis/core/views/3d/webgl-engine/materials/WaterMaterial.js", "../../@arcgis/core/views/3d/webgl-engine/materials/renderers/BufferRange.js", "../../@arcgis/core/views/3d/webgl-engine/materials/renderers/Instance.js", "../../@arcgis/core/views/3d/webgl-engine/materials/renderers/DrawCommand.js", "../../@arcgis/core/views/3d/webgl-engine/materials/renderers/PerBufferData.js", "../../@arcgis/core/views/3d/webgl-engine/materials/renderers/PerOriginData.js", "../../@arcgis/core/views/3d/webgl-engine/materials/renderers/VaoCache.js", "../../@arcgis/core/views/3d/webgl-engine/materials/renderers/MergedRenderer.js", "../../@arcgis/core/views/3d/webgl-engine/lib/SortedRenderGeometryRenderer.js", "../../@arcgis/core/views/3d/webgl-engine/lib/TextureTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/lib/TextureTechniqueConfiguration.js", "../../@arcgis/core/views/3d/terrain/OverlayRenderer.js", "../../@arcgis/core/views/3d/webgl-engine/lib/BufferVectorMath.js", "../../@arcgis/core/views/3d/webgl-engine/lib/FloatArray.js", "../../@arcgis/core/views/3d/webgl-engine/lib/GeometryUtil.js", "../../@arcgis/core/views/3d/webgl-engine/lib/RenderGeometry.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/ColorMaterialTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/ColorMaterialTechniqueConfiguration.js", "../../@arcgis/core/views/3d/webgl-engine/materials/ColorMaterial.js", "../../@arcgis/core/views/3d/webgl-engine/materials/lineStippleUtils.js", "../../@arcgis/core/views/3d/support/engineContent/line.js", "../../@arcgis/core/views/3d/support/renderInfoUtils/line.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/NativeLineTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/NativeLineTechniqueConfiguration.js", "../../@arcgis/core/views/3d/webgl-engine/materials/NativeLineMaterial.js", "../../@arcgis/core/core/libs/gl-matrix-2/types/mat4.js", "../../@arcgis/core/views/3d/webgl-engine/materials/ScaleInfo.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/HUDMaterialTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/HUDMaterialTechniqueConfiguration.js", "../../@arcgis/core/views/3d/webgl-engine/materials/HUDMaterial.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar C;!function(C){C[C.ASYNC=0]=\"ASYNC\",C[C.SYNC=1]=\"SYNC\"}(C||(C={}));export{C as UpdatePolicy};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar e,a,t;!function(e){e[e.RasterImage=0]=\"RasterImage\",e[e.Features=1]=\"Features\"}(e||(e={})),function(e){e[e.MapLayer=0]=\"MapLayer\",e[e.ViewLayer=1]=\"ViewLayer\",e[e.Outline=2]=\"Outline\",e[e.SnappingHint=3]=\"SnappingHint\"}(a||(a={})),function(e){e[e.WithRasterImage=0]=\"WithRasterImage\",e[e.WithoutRasterImage=1]=\"WithoutRasterImage\"}(t||(t={}));export{e as DrapeSourceType,t as DrapeTargetType,a as DrapedRenderGroup};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,isNone as e}from\"../../../../core/maybe.js\";import{getMetersPerUnitForSR as r}from\"../../../../core/unitUtils.js\";import{o as n,r as o,n as i}from\"../../../../chunks/mat4.js\";import{c as s}from\"../../../../chunks/mat4f64.js\";import{d as u,O as a}from\"../../../../chunks/vec3f64.js\";import{s as m}from\"../../../../chunks/vec4.js\";import{d as l,O as c}from\"../../../../chunks/vec4f64.js\";import{projectPoint as f}from\"../../../../geometry/projection.js\";import{width as p,depth as h,height as g}from\"../../../../geometry/support/aaBoundingBox.js\";import{create as y}from\"../../../../geometry/support/aaBoundingRect.js\";import{ringsCentroid as d}from\"../../../../geometry/support/centroid.js\";import{getPointOnPath as x,getPathLength as j}from\"../../../../geometry/support/coordsUtils.js\";import{makeDehydratedPoint as b}from\"../../../../layers/graphics/dehydratedFeatures.js\";import{isHydratedGeometry as v,clonePoint as w}from\"../../../../layers/graphics/hydratedFeatures.js\";import{VertexAttribute as z}from\"../../webgl-engine/lib/VertexAttribute.js\";function A(t,e){if(\"point\"===t.type)return P(t,e,!1);if(v(t))switch(t.type){case\"extent\":return P(t.center,e,!1);case\"polygon\":return P(t.centroid,e,!1);case\"polyline\":return P(M(t),e,!0);case\"mesh\":return P(t.origin,e,!1)}else switch(t.type){case\"extent\":return P(R(t),e,!0);case\"polygon\":return P(F(t),e,!0);case\"polyline\":return P(M(t),e,!0)}}function M(t){const e=t.paths[0];if(!e||0===e.length)return null;const r=x(e,j(e)/2);return b(r[0],r[1],r[2],t.spatialReference)}function R(t){return b(.5*(t.xmax+t.xmin),.5*(t.ymax+t.ymin),null!=t.zmin&&null!=t.zmax&&isFinite(t.zmin)&&isFinite(t.zmax)?.5*(t.zmax+t.zmin):void 0,t.spatialReference)}function F(t){const e=t.rings[0];if(!e||0===e.length)return null;const r=d(t.rings,!!t.hasZ);return b(r[0],r[1],r[2],t.spatialReference)}function P(t,e,r){const n=r?t:w(t);return e&&t?f(t,n,e)?n:null:n}function k(t,e,r,n=0){if(t){e||(e=y());const o=t;let i=.5*o.width*(r-1),s=.5*o.height*(r-1);return o.width<1e-7*o.height?i+=s/20:o.height<1e-7*o.width&&(s+=i/20),m(e,o.xmin-i-n,o.ymin-s-n,o.xmax+i+n,o.ymax+s+n),e}return null}function U(t,e){for(let r=0;r<t.geometries.length;++r){const n=t.geometries[r].getMutableAttribute(z.AUXPOS1);n&&n.data[3]!==e&&(n.data[3]=e,t.geometryVertexAttrsUpdated(t.geometries[r]))}}function B(e,r){const n=l(c);return t(e)&&(n[0]=e[0],n[1]=e[1],n[2]=e[2]),t(r)?n[3]=r:t(e)&&e.length>3&&(n[3]=e[3]),n}function D(e,r,n,o,i,s=[0,0,0,0]){for(let u=0;u<3;++u)t(e)&&null!=e[u]?s[u]=e[u]:t(n)&&null!=n[u]?s[u]=n[u]:s[u]=i[u];return t(r)?s[3]=r:t(o)?s[3]=o:s[3]=i[3],s}function I(t=a,r,n,o=1){const i=new Array(3);if(e(r)||e(n))i[0]=1,i[1]=1,i[2]=1;else{let e,o=0;for(let s=2;s>=0;s--){const u=t[s];let a;const m=null!=u,l=0===s&&!e&&!m,c=n[s];\"symbol-value\"===u||l?a=0!==c?r[s]/c:1:m&&\"proportional\"!==u&&isFinite(u)&&(a=0!==c?u/c:1),null!=a&&(i[s]=a,e=a,o=Math.max(o,Math.abs(a)))}for(let t=2;t>=0;t--)null==i[t]?i[t]=e:0===i[t]&&(i[t]=.001*o)}for(let e=2;e>=0;e--)i[e]/=o;return u(i)}function O(t){return null!=t.isPrimitive}function S(t){return V(O(t)?[t.width,t.depth,t.height]:t)?null:\"Symbol sizes may not be negative values\"}function V(t){if(Array.isArray(t)){for(const e of t)if(!V(e))return!1;return!0}return null==t||t>=0}function G(t,e,r,u=s()){const a=t||0,m=e||0,l=r||0;return 0!==a&&n(u,u,-a/180*Math.PI),0!==m&&o(u,u,m/180*Math.PI),0!==l&&i(u,u,l/180*Math.PI),u}function X(t,e,n){if(null!=n.minDemResolution)return n.minDemResolution;const o=r(e),i=p(t)*o,s=h(t)*o,u=g(t)*(e.isGeographic?1:o);return 0===i&&0===s&&0===u?n.minDemResolutionForPoints:.01*Math.max(i,s,u)}export{A as computeCentroid,G as computeObjectRotation,I as computeObjectScale,X as demResolutionForBoundingBox,k as enlargeExtent,V as isValidSize,B as mixinColorAndOpacity,D as overrideColor,U as updateVertexAttributeAuxpos1w,S as validateSymbolLayerSize};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction t(t){return\"point\"===t.type}export{t as isDehydratedPoint};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrapOr as e}from\"../../../core/maybe.js\";import{isDehydratedPoint as r}from\"../../../layers/graphics/dehydratedFeatureUtils.js\";class t{constructor(e,r=null,t=0){this.array=e,this.spatialReference=r,this.offset=t}}function a(e){return\"array\"in e}function i(t,i,n=\"ground\"){if(r(i))return t.getElevation(i.x,i.y,i.z||0,i.spatialReference,n);if(a(i)){let r=i.offset;return t.getElevation(i.array[r++],i.array[r++],i.array[r]||0,e(i.spatialReference,t.spatialReference),n)}return t.getElevation(i[0],i[1],i[2]||0,t.spatialReference,n)}export{t as SamplePosition,i as getElevationAtPoint,a as isSamplePosition};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrapOr as e}from\"../../../../core/maybe.js\";import{c as t}from\"../../../../chunks/mat4.js\";import{c as n}from\"../../../../chunks/mat4f64.js\";import{c as o}from\"../../../../chunks/vec3f64.js\";import{projectBuffer as r,computeTranslationToOriginAndRotation as i}from\"../../../../geometry/projection.js\";import{isDehydratedPoint as s}from\"../../../../layers/graphics/dehydratedFeatureUtils.js\";import{updateVertexAttributeAuxpos1w as a}from\"./graphicUtils.js\";import{isSamplePosition as l,getElevationAtPoint as u}from\"../../support/ElevationProvider.js\";function c(e,t,n,o,i,s,a,l,u,c,f){const d=x[f.mode];let m,p,g=0;if(r(e,t,n,o,u.spatialReference,i,l))return d.requiresAlignment(f)?(g=d.applyElevationAlignmentBuffer(o,i,s,a,l,u,c,f),m=s,p=a):(m=o,p=i),r(m,u.spatialReference,p,s,c.spatialReference,a,l)?g:void 0}function f(t,n,o,r,i){const a=(s(t)?t.z:l(t)?t.array[t.offset+2]:t[2])||0;switch(o.mode){case\"on-the-ground\":{const o=e(u(n,t,\"ground\"),0);return i.verticalDistanceToGround=0,i.sampledElevation=o,void(i.z=o)}case\"relative-to-ground\":{const s=e(u(n,t,\"ground\"),0),l=o.geometryZWithOffset(a,r);return i.verticalDistanceToGround=l,i.sampledElevation=s,void(i.z=l+s)}case\"relative-to-scene\":{const s=e(u(n,t,\"scene\"),0),l=o.geometryZWithOffset(a,r);return i.verticalDistanceToGround=l,i.sampledElevation=s,void(i.z=l+s)}case\"absolute-height\":{const s=o.geometryZWithOffset(a,r),l=e(u(n,t,\"ground\"),0);return i.verticalDistanceToGround=s-l,i.sampledElevation=l,void(i.z=s)}default:return void(i.z=0)}}function d(e,t,n,o){return f(e,t,n,o,z),z.z}function m(e,t,n){return null==t||null==n?e.definedChanged:\"on-the-ground\"===t&&\"on-the-ground\"===n?e.staysOnTheGround:t===n||\"on-the-ground\"!==t&&\"on-the-ground\"!==n?j.UPDATE:e.onTheGroundChanged}function p(e){return\"relative-to-ground\"===e||\"relative-to-scene\"===e}function g(e){return\"absolute-height\"!==e}function v(e,n,o,r,s){f(n,o,s,r,z),a(e,z.verticalDistanceToGround);const l=z.sampledElevation,u=t(O,e.transformation);C[0]=n.x,C[1]=n.y,C[2]=z.z;return i(n.spatialReference,C,u,r.spatialReference)?e.transformation=u:console.warn(\"Could not locate symbol object properly, it might be misplaced\"),l}function h(t,n,o,r,i,s){let a=0;const l=s.spatialReference;n*=3,r*=3;for(let u=0;u<i;++u){const i=t[n+0],u=t[n+1],c=t[n+2],f=e(s.getElevation(i,u,c,l,\"ground\"),0);a+=f,o[r+0]=i,o[r+1]=u,o[r+2]=f,n+=3,r+=3}return a/i}function E(t,n,o,r,i,s,a,l){let u=0;const c=l.calculateOffsetRenderUnits(a),f=l.featureExpressionInfoContext,d=s.spatialReference;n*=3,r*=3;for(let m=0;m<i;++m){const i=t[n+0],a=t[n+1],l=t[n+2],m=e(s.getElevation(i,a,l,d,\"ground\"),0);u+=m,o[r+0]=i,o[r+1]=a,o[r+2]=null==f?l+m+c:m+c,n+=3,r+=3}return u/i}function y(t,n,o,r,i,s,a,l){let u=0;const c=l.calculateOffsetRenderUnits(a),f=l.featureExpressionInfoContext,d=s.spatialReference;n*=3,r*=3;for(let m=0;m<i;++m){const i=t[n+0],a=t[n+1],l=t[n+2],m=e(s.getElevation(i,a,l,d,\"scene\"),0);u+=m,o[r+0]=i,o[r+1]=a,o[r+2]=null==f?l+m+c:m+c,n+=3,r+=3}return u/i}function A(e){const t=e.meterUnitOffset,n=e.featureExpressionInfoContext;return 0!==t||null!=n}function R(e,t,n,o,r,i,s,a){const l=a.calculateOffsetRenderUnits(s),u=a.featureExpressionInfoContext;t*=3,o*=3;for(let c=0;c<r;++c){const r=e[t+0],i=e[t+1],s=e[t+2];n[o+0]=r,n[o+1]=i,n[o+2]=null==u?s+l:l,t+=3,o+=3}return 0}class T{constructor(){this.verticalDistanceToGround=0,this.sampledElevation=0,this.z=0}}var j;!function(e){e[e.NONE=0]=\"NONE\",e[e.UPDATE=1]=\"UPDATE\",e[e.RECREATE=2]=\"RECREATE\"}(j||(j={}));const x={\"absolute-height\":{applyElevationAlignmentBuffer:R,requiresAlignment:A},\"on-the-ground\":{applyElevationAlignmentBuffer:h,requiresAlignment:()=>!0},\"relative-to-ground\":{applyElevationAlignmentBuffer:E,requiresAlignment:()=>!0},\"relative-to-scene\":{applyElevationAlignmentBuffer:y,requiresAlignment:()=>!0}},O=n(),z=new T,C=o();export{T as SampleElevationInfo,j as SymbolUpdateType,v as applyElevationAlignmentForHUD,c as applyPerVertexElevationAlignment,m as elevationModeChangeUpdateType,d as evaluateElevationAlignmentAtPoint,f as evaluateElevationInfoAtPoint,p as needsElevationUpdates2D,g as needsElevationUpdates3D};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/Logger.js\";import{throwIfAborted as t}from\"../../../../core/promiseUtils.js\";import{hydrateGeometry as r}from\"../../../../layers/graphics/hydratedFeatures.js\";import{loadArcade as n}from\"../../../../support/arcadeOnDemand.js\";const c=e.getLogger(\"esri.views.3d.layers.graphics.featureExpressionInfoUtils\");function o(e){return{cachedResult:e.cachedResult,arcade:e.arcade?{func:e.arcade.func,context:e.arcade.modules.arcadeUtils.createExecContext(null,{sr:e.arcade.context.spatialReference}),modules:e.arcade.modules}:null}}function u(e){const t=e&&e.expression;if(\"string\"==typeof t){const e=m(t);if(null!=e)return{cachedResult:e}}return null}async function a(e,r,c,o){const u=e&&e.expression;if(\"string\"!=typeof u)return null;const a=m(u);if(null!=a)return{cachedResult:a};const s=await n();t(c);const l=s.arcadeUtils,i=l.createSyntaxTree(u);return l.dependsOnView(i)?(null!=o&&o.error(\"Expressions containing '$view' are not supported on ElevationInfo\"),{cachedResult:0}):{arcade:{func:l.createFunction(i),context:l.createExecContext(null,{sr:r}),modules:s}}}function s(e,t,r){return e.arcadeUtils.createFeature(t.attributes,t.geometry,r)}function l(e,t){if(null!=e&&!p(e)){if(!t||!e.arcade)return void c.errorOncePerTick(\"Arcade support required but not provided\");const n=t;n._geometry&&(n._geometry=r(n._geometry)),e.arcade.modules.arcadeUtils.updateExecContext(e.arcade.context,t)}}function i(e){if(null!=e){if(p(e))return e.cachedResult;const t=e.arcade;let r=t?.modules.arcadeUtils.executeFunction(t.func,t.context);return\"number\"!=typeof r&&(e.cachedResult=0,r=0),r}return 0}function d(e,t=!1){let r=e&&e.featureExpressionInfo;const n=r&&r.expression;return t||\"0\"===n||(r=null),r??null}const f={cachedResult:0};function p(e){return null!=e.cachedResult}function m(e){return\"0\"===e?0:null}export{o as clone,a as createContext,u as createContextWithoutExpressionSupport,s as createFeature,i as execute,d as extractExpressionInfo,l as setContextFeature,f as zeroContext};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrapOr as t,isNone as e,isSome as n}from\"../../../../core/maybe.js\";import{getMetersPerUnit as s,supportsUnit as i}from\"../../../../symbols/support/unitConversionUtils.js\";import{execute as r,clone as o,setContextFeature as f,createFeature as u}from\"./featureExpressionInfoUtils.js\";class h{constructor(){this._meterUnitOffset=0,this._renderUnitOffset=0,this._unit=\"meters\",this._metersPerElevationInfoUnit=1,this._featureExpressionInfoContext=null,this.centerPointInElevationSR=null,this.mode=null}get featureExpressionInfoContext(){return this._featureExpressionInfoContext}get meterUnitOffset(){return this._meterUnitOffset}get unit(){return this._unit}set unit(t){this._unit=t,this._metersPerElevationInfoUnit=s(t)}get requiresSampledElevationInfo(){return\"absolute-height\"!==this.mode}reset(){this.mode=null,this._meterUnitOffset=0,this._renderUnitOffset=0,this._featureExpressionInfoContext=null,this.unit=\"meters\"}set offsetMeters(t){this._meterUnitOffset=t,this._renderUnitOffset=0}set offsetElevationInfoUnits(t){this._meterUnitOffset=t*this._metersPerElevationInfoUnit,this._renderUnitOffset=0}addOffsetRenderUnits(t){this._renderUnitOffset+=t}geometryZWithOffset(t,e){const n=this.calculateOffsetRenderUnits(e);return null!=this.featureExpressionInfoContext?n:t+n}calculateOffsetRenderUnits(t){let e=this._meterUnitOffset;const n=this.featureExpressionInfoContext;return null!=n&&(e+=r(n)*this._metersPerElevationInfoUnit),e/t.unitInMeters+this._renderUnitOffset}setFromElevationInfo(e){this.mode=e.mode,this.unit=i(e.unit)?e.unit:\"meters\",this.offsetElevationInfoUnits=t(e.offset,0)}updateFeatureExpressionInfoContext(t,s,i){if(e(t))return void(this._featureExpressionInfoContext=null);const r=t&&t.arcade;r&&n(s)&&n(i)?(this._featureExpressionInfoContext=o(t),f(this._featureExpressionInfoContext,u(r.modules,s,i))):this._featureExpressionInfoContext=t}static fromElevationInfo(t){const e=new h;return n(t)&&e.setFromElevationInfo(t),e}}export{h as ElevationContext};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,isNone as e}from\"../../../../core/maybe.js\";import{c as i,m as s,y as o}from\"../../../../chunks/mat4.js\";import{c as r}from\"../../../../chunks/mat4f64.js\";import{h as a,m as n,i as h,s as m,a as c,k as l,c as b}from\"../../../../chunks/vec3.js\";import{f as d,c as _}from\"../../../../chunks/vec3f64.js\";import{c as u,l as f}from\"../../../../chunks/sphere.js\";import{maxScale as g}from\"../../support/mathUtils.js\";import{Object3DState as p}from\"./basicInterfaces.js\";import{ContentObject as v}from\"./ContentObject.js\";import{ContentObjectType as j}from\"./ContentObjectType.js\";import{Object3DStateID as y}from\"./Object3DStateID.js\";import{assert as S}from\"./Util.js\";import{addObject3DStateID as V,removeObject3DStateID as A}from\"../materials/renderers/utils.js\";class x extends v{get geometries(){return this._geometries}get transformation(){return this._transformation}set transformation(t){i(this._transformation,t),this._invalidateBoundingVolume(),this._emit(\"objectTransformation\",this)}constructor(e={}){super(),this.type=j.Object,this._geometries=new Array,this._transformation=r(),this._bvObjectSpace=new L,this._bvWorldSpace=new L,this._bvDirty=!0,this._hasVolatileTransformation=!1,this._visible=!0,this.castShadow=null==e.castShadow||e.castShadow,this.metadata=e.metadata,this.metadata&&this.metadata.isElevationSource&&(this.metadata.lastValidElevationBB=new M);const i=e.geometries;t(i)&&(this._geometries=Array.from(i))}dispose(){this._geometries.length=0}get parentLayer(){return this._parentLayer}set parentLayer(t){S(null==this._parentLayer||null==t,\"Object3D can only be added to a single Layer\"),this._parentLayer=t}addGeometry(t){t.visible=this._visible,this._geometries.push(t),this._hasVolatileTransformation=this._hasVolatileTransformation||t.hasVolatileTransformation,this._emit(\"objectGeometryAdded\",{object:this,geometry:t}),this._invalidateBoundingVolume()}removeGeometry(t){const e=this._geometries.splice(t,1)[0];e&&(this._emit(\"objectGeometryRemoved\",{object:this,geometry:e}),this._invalidateBoundingVolume())}removeAllGeometries(){for(;this._geometries.length>0;)this.removeGeometry(0)}geometryVertexAttrsUpdated(t){this._emit(\"objectGeometryUpdated\",{object:this,geometry:t}),this._invalidateBoundingVolume()}get visible(){return this._visible}set visible(t){if(this._visible!==t){this._visible=t;for(const t of this._geometries)t.visible=this._visible;this._emit(\"visibilityChanged\",this)}}maskOccludee(){const t=new y(p.MaskOccludee);for(const e of this._geometries)e.occludees=V(e.occludees,t);return this._emit(\"occlusionChanged\",this),t}removeOcclude(t){for(const e of this._geometries)e.occludees=A(e.occludees,t);this._emit(\"occlusionChanged\",this)}highlight(){const t=new y(p.Highlight);for(const e of this._geometries)e.highlights=V(e.highlights,t);return this._emit(\"highlightChanged\",this),t}removeHighlight(t){for(const e of this._geometries)e.highlights=A(e.highlights,t);this._emit(\"highlightChanged\",this)}getCombinedStaticTransformation(t,e){return s(e,this.transformation,t.transformation)}_getCombinedShaderTransformation(t){return s(r(),this.transformation,t.shaderTransformation)}hasVolativeTransformation(){return this._hasVolatileTransformation}get boundingVolumeWorldSpace(){return this._validateBoundingVolume(),this._bvWorldSpace}get boundingVolumeObjectSpace(){return this._validateBoundingVolume(),this._bvObjectSpace}_validateBoundingVolume(){if(!this._bvDirty&&!this._hasVolatileTransformation)return;this._bvObjectSpace.init(),this._bvWorldSpace.init();for(const e of this._geometries){const i=e.boundingInfo;t(i)&&(O(i,this._bvObjectSpace,e.shaderTransformation),O(i,this._bvWorldSpace,this._getCombinedShaderTransformation(e)))}a(this._bvObjectSpace.bounds,this._bvObjectSpace.min,this._bvObjectSpace.max,.5),a(this._bvWorldSpace.bounds,this._bvWorldSpace.min,this._bvWorldSpace.max,.5);const i=_(),s=_(),o=g(this.transformation);for(const t of this._geometries){const r=t.boundingInfo;if(e(r))continue;const a=t.shaderTransformation,m=g(a);n(i,r.center,a);const c=h(i,this._bvObjectSpace.bounds),l=r.radius*m;this._bvObjectSpace.bounds[3]=Math.max(this._bvObjectSpace.bounds[3],c+l),n(s,i,this.transformation);const b=h(s,this._bvWorldSpace.bounds),d=l*o;this._bvWorldSpace.bounds[3]=Math.max(this._bvWorldSpace.bounds[3],b+d)}this._bvDirty=!1}_invalidateBoundingVolume(){this._bvDirty=!0,t(this._parentLayer)&&this._parentLayer.notifyObjectBBChanged(this,this._bvWorldSpace.bounds)}_emit(e,i){t(this._parentLayer)&&this._parentLayer.events.emit(e,i)}get test(){const t=this;return{hasGeometry:e=>t._geometries.includes(e),getGeometryIndex:e=>t._geometries.indexOf(e)}}}class M{constructor(){this.min=d(Number.MAX_VALUE,Number.MAX_VALUE,Number.MAX_VALUE),this.max=d(-Number.MAX_VALUE,-Number.MAX_VALUE,-Number.MAX_VALUE)}isEmpty(){return this.max[0]<this.min[0]&&this.max[1]<this.min[1]&&this.max[2]<this.min[2]}}class L extends M{constructor(){super(...arguments),this.bounds=u()}init(){m(this.min,Number.MAX_VALUE,Number.MAX_VALUE,Number.MAX_VALUE),m(this.max,-Number.MAX_VALUE,-Number.MAX_VALUE,-Number.MAX_VALUE),f(this.bounds)}}function O(t,e,i){const s=t.bbMin,r=t.bbMax;if(o(i)){const t=m(U,i[12],i[13],i[14]);c(E,s,t),c(T,r,t);for(let i=0;i<3;++i)e.min[i]=Math.min(e.min[i],E[i]),e.max[i]=Math.max(e.max[i],T[i])}else if(n(E,s,i),l(s,r))for(let o=0;o<3;++o)e.min[o]=Math.min(e.min[o],E[o]),e.max[o]=Math.max(e.max[o],E[o]);else{n(T,r,i);for(let t=0;t<3;++t)e.min[t]=Math.min(e.min[t],E[t],T[t]),e.max[t]=Math.max(e.max[t],E[t],T[t]);for(let t=0;t<3;++t){b(E,s),b(T,r),E[t]=r[t],T[t]=s[t],n(E,E,i),n(T,T,i);for(let t=0;t<3;++t)e.min[t]=Math.min(e.min[t],E[t],T[t]),e.max[t]=Math.max(e.max[t],E[t],T[t])}}}const U=_(),E=_(),T=_();export{x as Object3D};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst e=[\"layerObjectAdded\",\"layerObjectRemoved\",\"layerObjectsAdded\",\"layerObjectsRemoved\",\"shaderTransformationChanged\",\"objectTransformation\",\"visibilityChanged\",\"occlusionChanged\",\"highlightChanged\",\"objectGeometryAdded\",\"objectGeometryRemoved\",\"objectGeometryUpdated\"];export{e as DirtyEventNames};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/Evented.js\";import t from\"../../../../core/Handles.js\";import{isSome as s,isNone as r,destroyMaybe as i}from\"../../../../core/maybe.js\";import o from\"../../../../core/PooledArray.js\";import{ContentObject as a}from\"./ContentObject.js\";import{ContentObjectType as d}from\"./ContentObjectType.js\";import{DirtyEventNames as h}from\"./DirtyEvents.js\";import c from\"./Octree.js\";import{UpdatePolicy as n}from\"./UpdatePolicy.js\";class l extends a{get objects(){return this._objects}constructor(s,r=\"\"){super(),this.apiLayerUid=r,this.type=d.Layer,this.events=new e,this.sliceable=!1,this._objects=new o,this._objectsAdded=new o,this._stageHandles=new t,this.apiLayerUid=r,this.visible=s?.visible??!0,this.pickable=s?.pickable??!0,this.updatePolicy=s?.updatePolicy??n.ASYNC,this._disableOctree=s?.disableOctree??!1}destroy(){this.detachStage(),this._stage=null}attachStage(e){this.detachStage(),this._stage=e;for(const t of h)this._stageHandles.add(this.events.on(t,(s=>e.handleEvent(t,s))))}detachStage(){this._stageHandles.removeAll(),this.invalidateSpatialQueryAccelerator()}add(e){this._objects.push(e),e.parentLayer=this,this.events.emit(\"layerObjectAdded\",{layer:this,object:e}),s(this._octree)&&this._objectsAdded.push(e)}remove(e){this._objects.removeUnordered(e)&&(e.parentLayer=null,this.events.emit(\"layerObjectRemoved\",{layer:this,object:e}),s(this._octree)&&(this._objectsAdded.removeUnordered(e)||this._octree.remove([e])))}addMany(e){this._objects.pushArray(e);for(const t of e)t.parentLayer=this;this.events.emit(\"layerObjectsAdded\",{layer:this,objects:e}),s(this._octree)&&this._objectsAdded.pushArray(e)}removeMany(e){const t=new Array;if(this._objects.removeUnorderedMany(e,e.length,t),0!==t.length){for(const e of t)e.parentLayer=null;if(this.events.emit(\"layerObjectsRemoved\",{layer:this,objects:t}),s(this._octree)){for(let e=0;e<t.length;)this._objectsAdded.removeUnordered(t[e])?(t[e]=t[t.length-1],t.length-=1):++e;this._octree.remove(t)}}}sync(){s(this._stage)&&this.updatePolicy!==n.SYNC&&this._stage.syncLayer(this.id)}notifyObjectBBChanged(e,t){s(this._octree)&&!this._objectsAdded.includes(e)&&this._octree.update(e,t)}getSpatialQueryAccelerator(){return r(this._octree)&&this._objects.length>50&&!this._disableOctree?(this._octree=new c((e=>e.boundingVolumeWorldSpace.bounds)),this._octree.add(this._objects.data,this._objects.length)):s(this._octree)&&this._objectsAdded.length>0&&(this._octree.add(this._objectsAdded.data,this._objectsAdded.length),this._objectsAdded.clear()),this._octree}shaderTransformationChanged(){this.invalidateSpatialQueryAccelerator(),this.events.emit(\"shaderTransformationChanged\",this)}invalidateSpatialQueryAccelerator(){this._octree=i(this._octree),this._objectsAdded.clear()}}function _(e){return s(e)&&e.type===d.Layer}export{l as WebGLLayer,_ as isWebGLLayer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction t(t,a,n){return 2*Math.atan(Math.sqrt(a*a+n*n)*Math.tan(.5*t)/a)}function a(t,a,n){return 2*Math.atan(Math.sqrt(a*a+n*n)*Math.tan(.5*t)/n)}function n(t,a,n){return 2*Math.atan(a*Math.tan(.5*t)/Math.sqrt(a*a+n*n))}function r(t,a,n){return 2*Math.atan(n*Math.tan(.5*t)/Math.sqrt(a*a+n*n))}export{n as fovd2fovx,r as fovd2fovy,t as fovx2fovd,a as fovy2fovd};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../../chunks/tslib.es6.js\";import i from\"../../../../core/Accessor.js\";import e from\"../../../../core/Logger.js\";import{lerp as r}from\"../../../../core/mathUtils.js\";import{createScreenPointArray as s,createRenderScreenPointArray3 as h}from\"../../../../core/screenUtils.js\";import{property as o}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as n}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{c as a,m as p,q as _,h as u,a as v,t as l,u as w}from\"../../../../chunks/mat4.js\";import{c}from\"../../../../chunks/mat4f64.js\";import{c as d,m as y}from\"../../../../chunks/vec2.js\";import{f as g}from\"../../../../chunks/vec2f64.js\";import{b as f,i as m,c as x,k as M,w as T,e as D,D as j,E as R,g as F,l as P,s as O,f as V,n as S,r as C}from\"../../../../chunks/vec3.js\";import{c as I}from\"../../../../chunks/vec3f64.js\";import{b,h as E,g as L,c as k,i as H,t as U}from\"../../../../chunks/vec4.js\";import{f as B,c as G}from\"../../../../chunks/vec4f64.js\";import{create as A,copy as z,fromMatrix as W}from\"../../../../geometry/support/frustum.js\";import{create as X}from\"../../../../geometry/support/ray.js\";import{projectPointSignedLength as Y}from\"../../../../geometry/support/vector.js\";import{ViewingMode as q}from\"../../../ViewingMode.js\";import{fovd2fovx as N,fovx2fovd as J,fovd2fovy as K,fovy2fovd as Q}from\"./fov.js\";var Z;let $=Z=class extends i{constructor(t={}){super(t),this._center=I(),this._up=I(),this._viewUp=I(),this._viewForward=I(),this._viewRight=I(),this._ray=X(),this._viewport=B(0,0,1,1),this._padding=B(0,0,0,0),this._fov=55/180*Math.PI,this._nearFar=g(1,1e3),this._viewDirty=!0,this._viewMatrix=c(),this._viewProjectionDirty=!0,this._viewProjectionMatrix=c(),this._viewInverseTransposeMatrixDirty=!0,this._viewInverseTransposeMatrix=c(),this._frustumDirty=!0,this._frustum=A(),this._fullViewport=G(),this._pixelRatio=1,this.relativeElevation=0}get pixelRatio(){return this._pixelRatio}set pixelRatio(t){this._pixelRatio=t>0?t:1}get eye(){return this._ray.origin}set eye(t){this._compareAndSetView(t,this._ray.origin)}get center(){return this._center}set center(t){this._compareAndSetView(t,this._center,\"_center\")}get ray(){return f(this._ray.direction,this.center,this.eye),this._ray}get up(){return this._up}set up(t){this._compareAndSetView(t,this._up,\"_up\")}get viewMatrix(){return this._ensureViewClean(),this._viewMatrix}set viewMatrix(t){a(this._viewMatrix,t),this._viewDirty=!1,this._viewInverseTransposeMatrixDirty=!0,this._viewProjectionDirty=!0,this._frustumDirty=!0}get viewForward(){return this._ensureViewClean(),this._viewForward}get viewUp(){return this._ensureViewClean(),this._viewUp}get viewRight(){return this._ensureViewClean(),this._viewRight}get nearFar(){return this._nearFar}get near(){return this._nearFar[0]}set near(t){this._nearFar[0]!==t&&(this._nearFar[0]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange(\"_nearFar\"))}get far(){return this._nearFar[1]}set far(t){this._nearFar[1]!==t&&(this._nearFar[1]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange(\"_nearFar\"))}get viewport(){return this._viewport}set viewport(t){this.x=t[0],this.y=t[1],this.width=t[2],this.height=t[3]}get screenViewport(){if(1===this.pixelRatio)return this._viewport;const t=b(G(),this._viewport,1/this.pixelRatio),i=this._get(\"screenViewport\");return i&&E(t,i)?i:t}get x(){return this._viewport[0]}set x(t){t+=this._padding[ht.LEFT],this._viewport[0]!==t&&(this._viewport[0]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange(\"_viewport\"))}get y(){return this._viewport[1]}set y(t){t+=this._padding[ht.BOTTOM],this._viewport[1]!==t&&(this._viewport[1]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange(\"_viewport\"))}get width(){return this._viewport[2]}set width(t){this._viewport[2]!==t&&(this._viewport[2]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange(\"_viewport\"))}get height(){return this._viewport[3]}set height(t){this._viewport[3]!==t&&(this._viewport[3]=t,this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange(\"_viewport\"))}get fullWidth(){return this._viewport[2]+this._padding[ht.RIGHT]+this._padding[ht.LEFT]}set fullWidth(t){this.width=t-(this._padding[ht.RIGHT]+this._padding[ht.LEFT])}get fullHeight(){return this._viewport[3]+this._padding[ht.TOP]+this._padding[ht.BOTTOM]}set fullHeight(t){this.height=t-(this._padding[ht.TOP]+this._padding[ht.BOTTOM])}get fullViewport(){return this._fullViewport[0]=this._viewport[0]-this._padding[ht.LEFT],this._fullViewport[1]=this._viewport[1]-this._padding[ht.BOTTOM],this._fullViewport[2]=this.fullWidth,this._fullViewport[3]=this.fullHeight,this._fullViewport}get _aspect(){return this.width/this.height}get padding(){return this._padding}set padding(t){L(this._padding,t)||(this._viewport[0]+=t[ht.LEFT]-this._padding[ht.LEFT],this._viewport[1]+=t[ht.BOTTOM]-this._padding[ht.BOTTOM],this._viewport[2]-=t[ht.RIGHT]+t[ht.LEFT]-(this._padding[ht.RIGHT]+this._padding[ht.LEFT]),this._viewport[3]-=t[ht.TOP]+t[ht.BOTTOM]-(this._padding[ht.TOP]+this._padding[ht.BOTTOM]),k(this._padding,t),this._viewProjectionDirty=!0,this._frustumDirty=!0,this.notifyChange(\"_padding\"),this.notifyChange(\"_viewport\"))}get viewProjectionMatrix(){return this._viewProjectionDirty&&(p(this._viewProjectionMatrix,this.projectionMatrix,this.viewMatrix),this._viewProjectionDirty=!1),this._viewProjectionMatrix}get projectionMatrix(){const t=this.width,i=this.height,e=this.near*Math.tan(this.fovY/2),r=e*this._aspect,s=_(c(),-r*(1+2*this._padding[ht.LEFT]/t),r*(1+2*this._padding[ht.RIGHT]/t),-e*(1+2*this._padding[ht.BOTTOM]/i),e*(1+2*this._padding[ht.TOP]/i),this.near,this.far),h=this._get(\"projectionMatrix\");return h&&u(h,s)?h:s}get inverseProjectionMatrix(){return v(c(),this.projectionMatrix)||this._get(\"inverseProjectionMatrix\")||c()}get fov(){return this._fov}set fov(t){this._fov=t,this._viewProjectionDirty=!0,this._frustumDirty=!0}get fovX(){return N(this._fov,this.width,this.height)}set fovX(t){this._fov=J(t,this.width,this.height),this._viewProjectionDirty=!0,this._frustumDirty=!0}get fovY(){return K(this._fov,this.width,this.height)}set fovY(t){this._fov=Q(t,this.width,this.height),this._viewProjectionDirty=!0,this._frustumDirty=!0}get distance(){return m(this.center,this.eye)}get frustum(){return this._recomputeFrustum(),this._frustum}get viewInverseTransposeMatrix(){return(this._viewInverseTransposeMatrixDirty||this._viewDirty)&&(v(this._viewInverseTransposeMatrix,this.viewMatrix),l(this._viewInverseTransposeMatrix,this._viewInverseTransposeMatrix),this._viewInverseTransposeMatrixDirty=!1),this._viewInverseTransposeMatrix}depthNDCToWorld(t){const i=2*t-1;return 2*this.near*this.far/(this.far+this.near-i*(this.far-this.near))}get perRenderPixelRatio(){return Math.tan(this.fovX/2)/(this.width/2)}get perScreenPixelRatio(){return this.perRenderPixelRatio*this.pixelRatio}get aboveGround(){return null!=this.relativeElevation&&this.relativeElevation>=0}copyFrom(t){x(this._ray.origin,t.eye),this.center=t.center,this.up=t.up,k(this._viewport,t.viewport),this.notifyChange(\"_viewport\"),k(this._padding,t.padding),this.notifyChange(\"_padding\"),d(this._nearFar,t.nearFar),this.notifyChange(\"_nearFar\"),this._fov=t.fov,this.relativeElevation=t.relativeElevation;const i=t;return this._viewDirty=i._viewDirty,this._viewDirty||(a(this._viewMatrix,t.viewMatrix),x(this._viewRight,t.viewRight),x(this._viewUp,t.viewUp),x(this._viewForward,t.viewForward)),this._viewProjectionDirty=!0,this._frustumDirty=i._frustumDirty,this._frustumDirty||(z(this._frustum,t.frustum),this._frustumDirty=!1),i._viewInverseTransposeMatrixDirty?this._viewInverseTransposeMatrixDirty=!0:(a(this._viewInverseTransposeMatrix,t.viewInverseTransposeMatrix),this._viewInverseTransposeMatrixDirty=!1),k(this._fullViewport,t.fullViewport),this.pixelRatio=t.pixelRatio,this}copyViewFrom(t){this.eye=t.eye,this.center=t.center,this.up=t.up}clone(){return(new Z).copyFrom(this)}equals(t){return M(this.eye,t.eye)&&M(this.center,t.center)&&M(this.up,t.up)&&L(this._viewport,t.viewport)&&L(this._padding,t.padding)&&y(this.nearFar,t.nearFar)&&this._fov===t.fov&&this.pixelRatio===t.pixelRatio&&this.relativeElevation===t.relativeElevation}almostEquals(t){if(Math.abs(t.fov-this._fov)>=.001||H(t.padding,this._padding)>=.5||H(this.screenViewport,t.screenViewport)>=.5)return!1;T(et,t.eye,t.center),T(rt,this.eye,this.center);const i=D(et,rt),e=j(et),r=j(rt),s=5e-4;return i*i>=(1-1e-10)*e*r&&R(t.eye,this.eye)<Math.max(e,r)*s*s}computeRenderPixelSizeAt(t){return this.computeRenderPixelSizeAtDist(this._viewDirectionDistance(t))}computeRenderPixelSizeAtDist(t){return t*this.perRenderPixelRatio}computeScreenPixelSizeAt(t){return this.computeScreenPixelSizeAtDist(this._viewDirectionDistance(t))}_viewDirectionDistance(t){return Math.abs(Y(this.viewForward,f(et,t,this.eye)))}computeScreenPixelSizeAtDist(t){return t*this.perScreenPixelRatio}computeDistanceFromRadius(t,i){return t/Math.tan(Math.min(this.fovX,this.fovY)/(2*(i||1)))}getScreenCenter(t=s()){return t[0]=(this.padding[ht.LEFT]+this.width/2)/this.pixelRatio,t[1]=(this.padding[ht.TOP]+this.height/2)/this.pixelRatio,t}getRenderCenter(t,i=.5,e=.5){return t[0]=this.padding[ht.LEFT]+this.width*i,t[1]=this.padding[ht.BOTTOM]+this.height*e,t[2]=.5,t}setGLViewport(t){const i=this.viewport,e=this.padding;t.setViewport(i[0]-e[3],i[1]-e[2],i[2]+e[1]+e[3],i[3]+e[0]+e[2])}applyProjection(t,i){t!==tt&&x(tt,t),tt[3]=1,U(tt,tt,this.projectionMatrix);const e=Math.abs(tt[3]);F(tt,tt,1/e);const s=this.fullViewport;i[0]=r(0,s[0]+s[2],.5+.5*tt[0]),i[1]=r(0,s[1]+s[3],.5+.5*tt[1]),i[2]=.5*(tt[2]+1),i[3]=e}unapplyProjection(t,i){const e=this.fullViewport;tt[0]=(t[0]/(e[0]+e[2])*2-1)*t[3],tt[1]=(t[1]/(e[1]+e[3])*2-1)*t[3],tt[2]=(2*t[2]-1)*t[3],tt[3]=t[3],null!=this.inverseProjectionMatrix&&(U(tt,tt,this.inverseProjectionMatrix),i[0]=tt[0],i[1]=tt[1],i[2]=tt[2])}projectToScreen(t,i){return this.projectToRenderScreen(t,st),this.renderToScreen(st,i),i}projectToRenderScreen(t,i){if(tt[0]=t[0],tt[1]=t[1],tt[2]=t[2],tt[3]=1,U(tt,tt,this.viewProjectionMatrix),0===tt[3])return null;F(tt,tt,1/Math.abs(tt[3]));const e=this.fullViewport;return\"x\"in i?(i.x=r(0,e[0]+e[2],.5+.5*tt[0]),i.y=r(0,e[1]+e[3],.5+.5*tt[1])):(i[0]=r(0,e[0]+e[2],.5+.5*tt[0]),i[1]=r(0,e[1]+e[3],.5+.5*tt[1]),i.length>2&&(i[2]=.5*(tt[2]+1))),i}unprojectFromScreen(t,i){return this.unprojectFromRenderScreen(this.screenToRender(t,st),i)}unprojectFromRenderScreen(t,i){if(p(it,this.projectionMatrix,this.viewMatrix),!v(it,it))return null;const e=this.fullViewport;return tt[0]=2*(t[0]-e[0])/e[2]-1,tt[1]=2*(t[1]-e[1])/e[3]-1,tt[2]=2*t[2]-1,tt[3]=1,U(tt,tt,it),0===tt[3]?null:(i[0]=tt[0]/tt[3],i[1]=tt[1]/tt[3],i[2]=tt[2]/tt[3],i)}constrainWindowSize(t,i,e,r){const s=t*this.pixelRatio,h=i*this.pixelRatio,o=Math.max(s-e/2,0),n=Math.max(this.fullHeight-h-r/2,0),a=-Math.min(s-e/2,0),p=-Math.min(this.fullHeight-h-r/2,0);return[o,n,e-a- -Math.min(this.fullWidth-s-e/2,0),r-p- -Math.min(h-r/2,0)]}computeUp(t){t===q.Global?this._computeUpGlobal():this._computeUpLocal()}screenToRender(t,i){const e=t[0]*this.pixelRatio,r=this.fullHeight-t[1]*this.pixelRatio;return i[0]=e,i[1]=r,i}renderToScreen(t,i){const e=t[0]/this.pixelRatio,r=(this.fullHeight-t[1])/this.pixelRatio;i[0]=e,i[1]=r}_computeUpGlobal(){f(et,this.center,this.eye);const t=P(this.center);t<1?(O(this._up,0,0,1),this._markViewDirty(),this.notifyChange(\"_up\")):Math.abs(D(et,this.center))>.9999*P(et)*t||(V(this._up,et,this.center),V(this._up,this._up,et),S(this._up,this._up),this.notifyChange(\"_up\"),this._markViewDirty())}_computeUpLocal(){C(et,this.eye,this.center),Math.abs(et[2])<=.9999&&(F(et,et,et[2]),O(this._up,-et[0],-et[1],1-et[2]),S(this._up,this._up),this.notifyChange(\"_up\"),this._markViewDirty())}_compareAndSetView(t,i,r=\"\"){\"number\"==typeof t[0]&&isFinite(t[0])&&\"number\"==typeof t[1]&&isFinite(t[1])&&\"number\"==typeof t[2]&&isFinite(t[2])?M(t,i)||(x(i,t),this._markViewDirty(),r.length&&this.notifyChange(r)):e.getLogger(\"esri.views.3d.webgl-engine.lib.Camera\").warn(\"Camera vector contains invalid number, ignoring value\")}_markViewDirty(){this._viewDirty=!0,this._frustumDirty=!0,this._viewProjectionDirty=!0}_recomputeFrustum(){this._frustumDirty&&(W(this.viewMatrix,this.projectionMatrix,this._frustum),this._frustumDirty=!1)}_ensureViewClean(){this._viewDirty&&(w(this._viewMatrix,this.eye,this.center,this.up),O(this._viewForward,-this._viewMatrix[2],-this._viewMatrix[6],-this._viewMatrix[10]),O(this._viewUp,this._viewMatrix[1],this._viewMatrix[5],this._viewMatrix[9]),O(this._viewRight,this._viewMatrix[0],this._viewMatrix[4],this._viewMatrix[8]),this._viewDirty=!1,this._viewInverseTransposeMatrixDirty=!0)}};t([o()],$.prototype,\"_center\",void 0),t([o()],$.prototype,\"_up\",void 0),t([o()],$.prototype,\"_viewport\",void 0),t([o()],$.prototype,\"_padding\",void 0),t([o()],$.prototype,\"_fov\",void 0),t([o()],$.prototype,\"_nearFar\",void 0),t([o()],$.prototype,\"_pixelRatio\",void 0),t([o()],$.prototype,\"pixelRatio\",null),t([o()],$.prototype,\"eye\",null),t([o()],$.prototype,\"center\",null),t([o()],$.prototype,\"up\",null),t([o({readOnly:!0})],$.prototype,\"nearFar\",null),t([o()],$.prototype,\"near\",null),t([o()],$.prototype,\"far\",null),t([o()],$.prototype,\"viewport\",null),t([o({readOnly:!0})],$.prototype,\"screenViewport\",null),t([o()],$.prototype,\"x\",null),t([o()],$.prototype,\"y\",null),t([o()],$.prototype,\"width\",null),t([o()],$.prototype,\"height\",null),t([o()],$.prototype,\"fullWidth\",null),t([o()],$.prototype,\"fullHeight\",null),t([o({readOnly:!0})],$.prototype,\"_aspect\",null),t([o()],$.prototype,\"padding\",null),t([o({readOnly:!0})],$.prototype,\"projectionMatrix\",null),t([o({readOnly:!0})],$.prototype,\"inverseProjectionMatrix\",null),t([o()],$.prototype,\"fov\",null),t([o()],$.prototype,\"fovX\",null),t([o()],$.prototype,\"fovY\",null),$=Z=t([n(\"esri.views.3d.webgl-engine.lib.Camera\")],$);const tt=G(),it=c(),et=I(),rt=I(),st=h();var ht;!function(t){t[t.TOP=0]=\"TOP\",t[t.RIGHT=1]=\"RIGHT\",t[t.BOTTOM=2]=\"BOTTOM\",t[t.LEFT=3]=\"LEFT\"}(ht||(ht={}));export{$ as Camera,ht as PaddingSide};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ShaderOutput as e}from\"../core/shaderLibrary/ShaderOutput.js\";import{ReloadableShaderModule as t}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{ShaderTechnique as i}from\"../core/shaderTechnique/ShaderTechnique.js\";import{blendingDefault as r,oitBlending as n,oitDepthTest as l,oitDepthWrite as s,OITPolygonOffset as o}from\"../lib/OrderIndependentTransparency.js\";import{Program as c}from\"../lib/Program.js\";import{RenderSlot as a}from\"../lib/RenderSlot.js\";import{stencilWriteMaskOn as p,stencilToolMaskBaseParams as u,stencilBaseAllZerosParams as h,depthCompareAlways as d,stencilToolTransparentOccluderParams as T,stencilWriteMaskOff as f,stencilToolMaskOccluderParams as m,depthCompareLess as O}from\"../lib/StencilUtils.js\";import{TransparencyPassType as g}from\"../lib/TransparencyPassType.js\";import{VertexAttribute as P}from\"../lib/VertexAttribute.js\";import{a as R}from\"../../../../chunks/RibbonLine.glsl.js\";import{PrimitiveType as S}from\"../../../webgl/enums.js\";import{makePipelineState as A,defaultDepthWriteParams as E,defaultColorWriteParams as b}from\"../../../webgl/renderState.js\";const I=new Map([[P.POSITION,0],[P.SUBDIVISIONFACTOR,1],[P.UV0,2],[P.AUXPOS1,3],[P.AUXPOS2,4],[P.COLOR,5],[P.COLORFEATUREATTRIBUTE,5],[P.SIZE,6],[P.SIZEFEATUREATTRIBUTE,6],[P.OPACITYFEATUREATTRIBUTE,7],[P.OBJECTANDLAYERIDCOLOR,8]]);class y extends i{initializeProgram(e){return new c(e.rctx,y.shader.get().build(this.configuration),I)}_makePipelineState(t,i){const c=this.configuration,a=t===g.NONE,d=t===g.FrontFace;return A({blending:c.output===e.Color||c.output===e.Alpha?a?r:n(t):null,depthTest:{func:l(t)},depthWrite:a?c.writeDepth?E:null:s(t),colorWrite:b,stencilWrite:c.hasOccludees?p:null,stencilTest:c.hasOccludees?i?u:h:null,polygonOffset:a||d?c.hasPolygonOffset?_:null:o})}initializePipeline(){const e=this.configuration;if(e.occluder){const t=e.hasPolygonOffset?_:null;this._occluderPipelineTransparent=A({blending:r,polygonOffset:t,depthTest:d,depthWrite:null,colorWrite:b,stencilWrite:null,stencilTest:T}),this._occluderPipelineOpaque=A({blending:r,polygonOffset:t,depthTest:d,depthWrite:null,colorWrite:b,stencilWrite:f,stencilTest:m}),this._occluderPipelineMaskWrite=A({blending:null,polygonOffset:t,depthTest:O,depthWrite:null,colorWrite:null,stencilWrite:p,stencilTest:u})}return this._occludeePipelineState=this._makePipelineState(this.configuration.transparencyPassType,!0),this._makePipelineState(this.configuration.transparencyPassType,!1)}get primitiveType(){return this.configuration.wireframe?S.LINES:S.TRIANGLE_STRIP}getPipelineState(e,t){return t?this._occludeePipelineState:this.configuration.occluder?e===a.TRANSPARENT_OCCLUDER_MATERIAL?this._occluderPipelineTransparent:e===a.OCCLUDER_MATERIAL?this._occluderPipelineOpaque:this._occluderPipelineMaskWrite:super.getPipelineState(e,t)}}y.shader=new t(R,(()=>import(\"./RibbonLine.glsl.js\")));const _={factor:0,units:-4};export{y as RibbonLineTechnique,I as vertexAttributeLocations};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../core/has.js\";import e from\"../../../../core/Logger.js\";import{clamp as t}from\"../../../../core/mathUtils.js\";import{isSome as r}from\"../../../../core/maybe.js\";import{createRenderScreenPointArray3 as i}from\"../../../../core/screenUtils.js\";import{c as s}from\"../../../../chunks/vec2.js\";import{s as a,b as n,e as o,g as l,a as c,c as h,l as u,i as p,m}from\"../../../../chunks/vec3.js\";import{c as f}from\"../../../../chunks/vec3f64.js\";import{O as T}from\"../../../../chunks/vec4f64.js\";import{PlaneIndex as d}from\"../../../../geometry/support/frustum.js\";import{create as _,distance2 as E,fromPoints as A,closestLineSegmentPoint as v}from\"../../../../geometry/support/lineSegment.js\";import{create as R,fromPoints as S,signedDistance as I,normal as g}from\"../../../../geometry/support/plane.js\";import{newLayout as O}from\"../../support/buffer/InterleavedLayout.js\";import{ShaderOutput as b}from\"../core/shaderLibrary/ShaderOutput.js\";import L from\"../lib/GLMaterial.js\";import{Material as P,RenderOccludedFlag as N}from\"../lib/Material.js\";import{RenderSlot as C}from\"../lib/RenderSlot.js\";import{isTranslationMatrix as y}from\"../lib/Util.js\";import{VertexAttribute as j}from\"../lib/VertexAttribute.js\";import{VisualVariablePassParameters as D}from\"./VisualVariablePassParameters.js\";import{LineMarkerAnchor as U}from\"../shaders/LineMarkerTechniqueConfiguration.js\";import{R as F}from\"../../../../chunks/RibbonLine.glsl.js\";import{vertexAttributeLocations as x,RibbonLineTechnique as w}from\"../shaders/RibbonLineTechnique.js\";import{RibbonLineTechniqueConfiguration as M,CapType as J}from\"../shaders/RibbonLineTechniqueConfiguration.js\";var B;!function(e){e[e.LEFT_JOIN_START=-2]=\"LEFT_JOIN_START\",e[e.LEFT_JOIN_END=-1]=\"LEFT_JOIN_END\",e[e.LEFT_CAP_START=-4]=\"LEFT_CAP_START\",e[e.LEFT_CAP_END=-5]=\"LEFT_CAP_END\",e[e.RIGHT_JOIN_START=2]=\"RIGHT_JOIN_START\",e[e.RIGHT_JOIN_END=1]=\"RIGHT_JOIN_END\",e[e.RIGHT_CAP_START=4]=\"RIGHT_CAP_START\",e[e.RIGHT_CAP_END=5]=\"RIGHT_CAP_END\"}(B||(B={}));class z extends P{constructor(e){super(e,new H),this._configuration=new M,this._vertexAttributeLocations=x,this._layout=this.createLayout()}isClosed(e,t){return Z(this.parameters,e,t)}getConfiguration(e,t){this._configuration.output=e,this._configuration.draped=t.slot===C.DRAPED_MATERIAL;const i=r(this.parameters.stipplePattern)&&e!==b.Highlight;return this._configuration.stippleEnabled=i,this._configuration.stippleOffColorEnabled=i&&r(this.parameters.stippleOffColor),this._configuration.stippleScaleWithLineWidth=i&&this.parameters.stippleScaleWithLineWidth,this._configuration.stipplePreferContinuous=i&&this.parameters.stipplePreferContinuous,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.hasOccludees=this.parameters.hasOccludees,this._configuration.roundJoins=\"round\"===this.parameters.join,this._configuration.capType=this.parameters.cap,this._configuration.applyMarkerOffset=!!r(this.parameters.markerParameters)&&q(this.parameters.markerParameters),this._configuration.hasPolygonOffset=this.parameters.hasPolygonOffset,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.vvColor=this.parameters.vvColorEnabled,this._configuration.vvOpacity=this.parameters.vvOpacityEnabled,this._configuration.vvSize=this.parameters.vvSizeEnabled,this._configuration.innerColorEnabled=this.parameters.innerWidth>0&&r(this.parameters.innerColor),this._configuration.falloffEnabled=this.parameters.falloff>0,this._configuration.occluder=this.parameters.renderOccluded===N.OccludeAndTransparentStencil,this._configuration.transparencyPassType=t.transparencyPassType,this._configuration.hasMultipassTerrain=t.multipassTerrain.enabled,this._configuration.cullAboveGround=t.multipassTerrain.cullAboveGround,this._configuration.wireframe=this.parameters.wireframe,this._configuration}intersectDraped(e,r,i,s,a,n){if(!i.options.selectionMode)return;const o=e.vertexAttributes.get(j.POSITION).data,l=e.vertexAttributes.get(j.SIZE);let c=this.parameters.width;if(this.parameters.vvSizeEnabled){const r=e.vertexAttributes.get(j.SIZEFEATUREATTRIBUTE).data[0];c*=t(this.parameters.vvSizeOffset[0]+r*this.parameters.vvSizeFactor[0],this.parameters.vvSizeMinSize[0],this.parameters.vvSizeMaxSize[0])}else l&&(c*=l.data[0]);const h=s[0],u=s[1],p=(c/2+4)*e.screenToWorldRatio;let m=Number.MAX_VALUE,f=0;for(let T=0;T<o.length-5;T+=3){const e=o[T],r=o[T+1],i=h-e,s=u-r,a=o[T+3]-e,n=o[T+4]-r,l=t((a*i+n*s)/(a*a+n*n),0,1),c=a*l-i,p=n*l-s,d=c*c+p*p;d<m&&(m=d,f=T/3)}m<p*p&&a(n.dist,n.normal,f,!1)}intersect(r,i,m,f,T,_){if(!m.options.selectionMode||!r.visible)return;if(!y(i))return void e.getLogger(\"esri.views.3d.webgl-engine.materials.RibbonLineMaterial\").error(\"intersection assumes a translation-only matrix\");const R=r.vertexAttributes,O=R.get(j.POSITION).data;let b=this.parameters.width;if(this.parameters.vvSizeEnabled){const e=R.get(j.SIZEFEATUREATTRIBUTE).data[0];b*=t(this.parameters.vvSizeOffset[0]+e*this.parameters.vvSizeFactor[0],this.parameters.vvSizeMinSize[0],this.parameters.vvSizeMaxSize[0])}else R.has(j.SIZE)&&(b*=R.get(j.SIZE).data[0]);const L=m.camera,P=$;s(P,m.point);const N=b*L.pixelRatio/2+4*L.pixelRatio;a(ce[0],P[0]-N,P[1]+N,0),a(ce[1],P[0]+N,P[1]+N,0),a(ce[2],P[0]+N,P[1]-N,0),a(ce[3],P[0]-N,P[1]-N,0);for(let e=0;e<4;e++)if(!L.unprojectFromRenderScreen(ce[e],he[e]))return;S(L.eye,he[0],he[1],ue),S(L.eye,he[1],he[2],pe),S(L.eye,he[2],he[3],me),S(L.eye,he[3],he[0],fe);let C=Number.MAX_VALUE,D=0;const U=V(this.parameters,R,r.indices)?O.length-2:O.length-5;for(let e=0;e<U;e+=3){X[0]=O[e]+i[12],X[1]=O[e+1]+i[13],X[2]=O[e+2]+i[14];const t=(e+3)%O.length;if(Y[0]=O[t]+i[12],Y[1]=O[t+1]+i[13],Y[2]=O[t+2]+i[14],I(ue,X)<0&&I(ue,Y)<0||I(pe,X)<0&&I(pe,Y)<0||I(me,X)<0&&I(me,Y)<0||I(fe,X)<0&&I(fe,Y)<0)continue;if(L.projectToRenderScreen(X,ee),L.projectToRenderScreen(Y,te),ee[2]<0&&te[2]>0){n(Q,X,Y);const e=L.frustum,t=-I(e[d.NEAR],X)/o(Q,g(e[d.NEAR]));l(Q,Q,t),c(X,X,Q),L.projectToRenderScreen(X,ee)}else if(ee[2]>0&&te[2]<0){n(Q,Y,X);const e=L.frustum,t=-I(e[d.NEAR],Y)/o(Q,g(e[d.NEAR]));l(Q,Q,t),c(Y,Y,Q),L.projectToRenderScreen(Y,te)}else if(ee[2]<0&&te[2]<0)continue;ee[2]=0,te[2]=0;const r=E(A(ee,te,se),P);r<C&&(C=r,h(re,X),h(ie,Y),D=e/3)}const F=m.rayBegin,x=m.rayEnd;if(C<N*N){let e=Number.MAX_VALUE;if(v(A(re,ie,se),A(F,x,ae),K)){n(K,K,F);const t=u(K);l(K,K,1/t),e=t/p(F,x)}_(e,K,D,!1)}}createLayout(){const e=O().vec3f(j.POSITION).f32(j.SUBDIVISIONFACTOR).vec2f(j.UV0).vec3f(j.AUXPOS1).vec3f(j.AUXPOS2);return this.parameters.vvSizeEnabled?e.f32(j.SIZEFEATUREATTRIBUTE):e.f32(j.SIZE),this.parameters.vvColorEnabled?e.f32(j.COLORFEATUREATTRIBUTE):e.vec4f(j.COLOR),this.parameters.vvOpacityEnabled&&e.f32(j.OPACITYFEATUREATTRIBUTE),has(\"enable-feature:objectAndLayerId-rendering\")&&e.vec4u8(j.OBJECTANDLAYERIDCOLOR),e}createBufferWriter(){return new k(this._layout,this.parameters)}requiresSlot(e,t){if(t===b.Color||t===b.Alpha||t===b.Highlight||t===b.Depth||t===b.ObjectAndLayerIdColor){if(e===C.DRAPED_MATERIAL)return!0;if(this.parameters.renderOccluded===N.OccludeAndTransparentStencil)return e===C.OPAQUE_MATERIAL||e===C.OCCLUDER_MATERIAL||e===C.TRANSPARENT_OCCLUDER_MATERIAL;if(t===b.Color||t===b.Alpha){return e===(this.parameters.writeDepth?C.TRANSPARENT_MATERIAL:C.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL)}return e===C.OPAQUE_MATERIAL}return!1}createGLMaterial(e){return new G(e)}validateParameters(e){\"miter\"!==e.join&&(e.miterLimit=0),r(e.markerParameters)&&(e.markerScale=e.markerParameters.width/e.width)}}class G extends L{constructor(){super(...arguments),this._stipplePattern=null}dispose(){super.dispose(),this._stippleTextureRepository.release(this._stipplePattern),this._stipplePattern=null}_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}beginSlot(e){this._output!==b.Color&&this._output!==b.Alpha||this._updateOccludeeState(e);const t=this._material.parameters.stipplePattern;return this._stipplePattern!==t&&(this._material.setParameters(this._stippleTextureRepository.swap(this._stipplePattern,t)),this._stipplePattern=t),this.ensureTechnique(w,e)}}class H extends D{constructor(){super(...arguments),this.width=0,this.color=T,this.join=\"miter\",this.cap=J.BUTT,this.miterLimit=5,this.writeDepth=!0,this.hasPolygonOffset=!1,this.stippleTexture=null,this.stippleScaleWithLineWidth=!1,this.stipplePreferContinuous=!0,this.markerParameters=null,this.markerScale=1,this.hasSlicePlane=!1,this.vvFastUpdate=!1,this.isClosed=!1,this.falloff=0,this.innerWidth=0,this.hasOccludees=!1,this.wireframe=!1}}class k{constructor(e,t){this._parameters=t,this.numJoinSubdivisions=0,this.vertexBufferLayout=e;const r=t.stipplePattern?1:0;switch(this._parameters.join){case\"miter\":case\"bevel\":this.numJoinSubdivisions=r;break;case\"round\":this.numJoinSubdivisions=F+r}}_isClosed(e){return V(this._parameters,e.vertexAttributes,e.indices)}allocate(e){return this.vertexBufferLayout.createBuffer(e)}elementCount(e){const t=2,r=e.indices.get(j.POSITION).length/2+1,i=this._isClosed(e);let s=i?2:2*t;return s+=((i?r:r-1)-(i?0:1))*(2*this.numJoinSubdivisions+4),s+=2,this._parameters.wireframe&&(s=2+4*(s-2)),s}write(e,t,i,s,n){const o=ne,l=oe,c=le,u=i.vertexAttributes.get(j.POSITION).data,f=i.indices&&i.indices.get(j.POSITION),T=i.vertexAttributes.get(j.DISTANCETOSTART)?.data;f&&f.length!==2*(u.length/3-1)&&console.warn(\"RibbonLineMaterial does not support indices\");let d=1,_=0;this._parameters.vvSizeEnabled?_=i.vertexAttributes.get(j.SIZEFEATUREATTRIBUTE).data[0]:i.vertexAttributes.has(j.SIZE)&&(d=i.vertexAttributes.get(j.SIZE).data[0]);let E=[1,1,1,1],A=0;this._parameters.vvColorEnabled?A=i.vertexAttributes.get(j.COLORFEATUREATTRIBUTE).data[0]:i.vertexAttributes.has(j.COLOR)&&(E=i.vertexAttributes.get(j.COLOR).data);const v=has(\"enable-feature:objectAndLayerId-rendering\")?i.objectAndLayerIdColor:null;let R=0;this._parameters.vvOpacityEnabled&&(R=i.vertexAttributes.get(j.OPACITYFEATUREATTRIBUTE).data[0]);const S=u.length/3,I=new Float32Array(s.buffer),g=has(\"enable-feature:objectAndLayerId-rendering\")?new Uint8Array(s.buffer):null,O=this.vertexBufferLayout.stride/4;let b=n*O;const L=b;let P=0;const N=T?(e,t,r)=>P=T[r]:(e,t,r)=>P+=p(e,t),C=has(\"enable-feature:objectAndLayerId-rendering\"),y=(e,t,i,s,a,n,o)=>{if(I[b++]=t[0],I[b++]=t[1],I[b++]=t[2],I[b++]=s,I[b++]=o,I[b++]=a,I[b++]=e[0],I[b++]=e[1],I[b++]=e[2],I[b++]=i[0],I[b++]=i[1],I[b++]=i[2],this._parameters.vvSizeEnabled?I[b++]=_:I[b++]=d,this._parameters.vvColorEnabled)I[b++]=A;else{const e=Math.min(4*n,E.length-4);I[b++]=E[e],I[b++]=E[e+1],I[b++]=E[e+2],I[b++]=E[e+3]}this._parameters.vvOpacityEnabled&&(I[b++]=R),C&&(r(v)&&(g[4*b]=v[0],g[4*b+1]=v[1],g[4*b+2]=v[2],g[4*b+3]=v[3]),b++)};b+=O,a(l,u[0],u[1],u[2]),e&&m(l,l,e);const D=this._isClosed(i);if(D){const t=u.length-3;a(o,u[t],u[t+1],u[t+2]),e&&m(o,o,e)}else a(c,u[3],u[4],u[5]),e&&m(c,c,e),y(l,l,c,1,B.LEFT_CAP_START,0,0),y(l,l,c,1,B.RIGHT_CAP_START,0,0),h(o,l),h(l,c);const U=D?0:1,F=D?S:S-1;for(let r=U;r<F;r++){const t=(r+1)%S*3;a(c,u[t],u[t+1],u[t+2]),e&&m(c,c,e),N(o,l,r),y(o,l,c,0,B.LEFT_JOIN_END,r,P),y(o,l,c,0,B.RIGHT_JOIN_END,r,P);const i=this.numJoinSubdivisions;for(let e=0;e<i;++e){const t=(e+1)/(i+1);y(o,l,c,t,B.LEFT_JOIN_END,r,P),y(o,l,c,t,B.RIGHT_JOIN_END,r,P)}y(o,l,c,1,B.LEFT_JOIN_START,r,P),y(o,l,c,1,B.RIGHT_JOIN_START,r,P),h(o,l),h(l,c)}D?(a(c,u[3],u[4],u[5]),e&&m(c,c,e),P=N(o,l,F),y(o,l,c,0,B.LEFT_JOIN_END,U,P),y(o,l,c,0,B.RIGHT_JOIN_END,U,P)):(P=N(o,l,F),y(o,l,l,0,B.LEFT_CAP_END,F,P),y(o,l,l,0,B.RIGHT_CAP_END,F,P)),W(I,L+O,I,L,O);b=W(I,b-O,I,b,O),this._parameters.wireframe&&this._addWireframeVertices(s,L,b,O)}_addWireframeVertices(e,t,r,i){const s=new Float32Array(e.buffer,r*Float32Array.BYTES_PER_ELEMENT),a=new Float32Array(e.buffer,t*Float32Array.BYTES_PER_ELEMENT,r-t);let n=0;const o=e=>n=W(a,e,s,n,i);for(let l=0;l<a.length-1;l+=2*i)o(l),o(l+2*i),o(l+1*i),o(l+2*i),o(l+1*i),o(l+3*i)}}function W(e,t,r,i,s){for(let a=0;a<s;a++)r[i++]=e[t++];return i}function V(e,t,r){return Z(e,t.get(j.POSITION).data,r?r.get(j.POSITION):null)}function Z(e,t,r){return!!e.isClosed&&(r?r.length>2:t.length>6)}function q(e){return e.anchor===U.Tip&&e.hideOnShortSegments&&\"begin-end\"===e.placement&&e.worldSpace}const X=f(),Y=f(),Q=f(),K=f(),$=f(),ee=i(),te=i(),re=f(),ie=f(),se=_(),ae=_(),ne=f(),oe=f(),le=f(),ce=[i(),i(),i(),i()],he=[f(),f(),f(),f()],ue=R(),pe=R(),me=R(),fe=R();export{H as Parameters,z as RibbonLineMaterial};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{f as s}from\"../../../../chunks/vec3f64.js\";class t{constructor(s,t){this.vec3=s,this.id=t}}function c(c,r,n,o){return new t(s(c,r,n),o)}export{t as LocalOrigin,c as fromValues};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst e={orderedRepackingEnabled:!1},n={rootOrigin:null};export{n as gridLocalOriginFactory,e as textTextureAtlas};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../../core/maybe.js\";import{generateUID as i}from\"../../../../core/uid.js\";import{b as r}from\"../../../../chunks/vec3.js\";import{c as o}from\"../../../../chunks/vec3f64.js\";import{f as s}from\"../../../../chunks/vec4f64.js\";import{projectBuffer as e}from\"../../../../geometry/projection.js\";import{Attribute as n}from\"./Attribute.js\";import{ContentObjectType as a}from\"./ContentObjectType.js\";import{Geometry as h}from\"./Geometry.js\";import{fromValues as m}from\"./LocalOriginFactory.js\";import{Object3D as c}from\"./Object3D.js\";import{gridLocalOriginFactory as d}from\"./testUtils.js\";import{VertexAttribute as g}from\"./VertexAttribute.js\";import{WebGLLayer as _}from\"./WebGLLayer.js\";import{RibbonLineMaterial as f}from\"../materials/RibbonLineMaterial.js\";class p{constructor(t){this._originSR=t,this._origins=new Map,this._objects=new Map,this._gridSize=5e5,this._rootOriginId=\"root/\"+i()}getOrigin(i){const o=this._origins.get(this._rootOriginId);if(null==o){const r=d.rootOrigin;if(t(r))return this._origins.set(this._rootOriginId,m(r[0],r[1],r[2],this._rootOriginId)),this.getOrigin(i);const o=m(i[0]+Math.random()-.5,i[1]+Math.random()-.5,i[2]+Math.random()-.5,this._rootOriginId);return this._origins.set(this._rootOriginId,o),o}const s=this._gridSize,e=Math.round(i[0]/s),n=Math.round(i[1]/s),a=Math.round(i[2]/s),h=`${e}/${n}/${a}`;let c=this._origins.get(h);const g=.5*s;if(r(j,i,o.vec3),j[0]=Math.abs(j[0]),j[1]=Math.abs(j[1]),j[2]=Math.abs(j[2]),j[0]<g&&j[1]<g&&j[2]<g){if(c){const t=Math.max(...j);r(j,i,c.vec3),j[0]=Math.abs(j[0]),j[1]=Math.abs(j[1]),j[2]=Math.abs(j[2]);if(Math.max(...j)<t)return c}return o}return c||(c=m(e*s,n*s,a*s,h),this._origins.set(h,c)),c}_drawOriginBox(t,i=s(1,1,0,1)){const r=window.view,o=r._stage,m=i.toString();if(!this._objects.has(m)){this._material=new f({width:2,color:i}),o.add(this._material);const t=new _({pickable:!1}),r=new c({castShadow:!1});o.add(r),t.add(r),o.add(t),this._objects.set(m,r)}const d=this._objects.get(m),p=[0,1,5,4,0,2,1,7,6,2,0,1,3,7,5,4,6,2,0],j=p.length,b=new Array(3*j),u=new Array,l=.5*this._gridSize;for(let s=0;s<j;s++)b[3*s+0]=t[0]+(1&p[s]?l:-l),b[3*s+1]=t[1]+(2&p[s]?l:-l),b[3*s+2]=t[2]+(4&p[s]?l:-l),s>0&&u.push(s-1,s);e(b,this._originSR,0,b,r.renderSpatialReference,0,j);const M=new h(this._material,[[g.POSITION,new n(b,3,!0)]],[[g.POSITION,u]],null,a.Line);o.add(M),d.addGeometry(M)}get test(){const t=this;return{set gridSize(i){t._gridSize=i}}}}const j=o();export{p as GridLocalOriginFactory};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar i,t;!function(i){i[i.OBJECT=0]=\"OBJECT\",i[i.HUD=1]=\"HUD\",i[i.TERRAIN=2]=\"TERRAIN\",i[i.OVERLAY=3]=\"OVERLAY\",i[i.I3S=4]=\"I3S\",i[i.PCL=5]=\"PCL\",i[i.LOD=6]=\"LOD\",i[i.VOXEL=7]=\"VOXEL\"}(i||(i={}));class e{constructor(){this.verticalOffset=0,this.selectionMode=!1,this.hud=!0,this.selectOpaqueTerrainOnly=!0,this.invisibleTerrain=!1,this.backfacesTerrain=!0,this.isFiltered=!1,this.filteredLayerUids=[],this.store=t.ALL}}!function(i){i[i.MIN=0]=\"MIN\",i[i.MINMAX=1]=\"MINMAX\",i[i.ALL=2]=\"ALL\"}(t||(t={}));export{e as IntersectorOptions,i as IntersectorType,t as StoreResults};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as s}from\"../../../../core/maybe.js\";import{a as t}from\"../../../../chunks/vec3f64.js\";class r{constructor(s,t,r){this.object=s,this.geometryId=t,this.triangleNr=r}}class c extends r{constructor(r,c,e,o){super(r,c,e),this.center=s(o)?t(o):null}}class e extends c{}class o{constructor(s){this.layerUid=s}}class i extends o{constructor(s,t){super(s),this.graphicUid=t}}export{i as Graphic3DTarget,c as HudTarget,o as LayerTarget,r as ObjectTarget,e as ValidHudTarget};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as r}from\"../../../../core/maybe.js\";import{h as t}from\"../../../../chunks/vec3.js\";import{c as e}from\"../../../../chunks/vec3f64.js\";import{e as n}from\"../../../../chunks/boundedPlane.js\";import{IntersectorType as o}from\"./IntersectorInterfaces.js\";function c(t){return r(t)&&r(t.dist)}function s(r){return(e,o,c)=>(t(f,e,o,c),!n(r,f))}function i(r){return c(r)&&r.intersector===o.OBJECT&&!!r.target}function u(t){return c(t)&&t.intersector===o.HUD&&!!t.target&&\"center\"in t.target&&r(t.target.center)}const f=e();export{u as isHudIntersectorResult,i as isObjectIntersectorResult,c as isValidIntersectorResult,s as sliceFilterPredicate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,unwrapOr as r}from\"../../../../core/maybe.js\";import{c as s}from\"../../../../chunks/mat4.js\";import{c as i,I as e}from\"../../../../chunks/mat4f64.js\";import{a,m as n,g as o,l as h,c as d,n as c}from\"../../../../chunks/vec3.js\";import{c as l,U as m}from\"../../../../chunks/vec3f64.js\";import{t as f}from\"../../../../chunks/vec4.js\";import{c as y}from\"../../../../chunks/vec4f64.js\";import{create as u,fromPoints as p,copy as g}from\"../../../../geometry/support/ray.js\";import{ViewingMode as _}from\"../../../ViewingMode.js\";import{IntersectorOptions as O,StoreResults as L,IntersectorType as j}from\"./IntersectorInterfaces.js\";import{HudTarget as v}from\"./IntersectorTarget.js\";import{isValidIntersectorResult as E}from\"./intersectorUtils.js\";import{IntersectorTransform as w,getVerticalOffsetObject3D as b}from\"./verticalOffsetUtils.js\";const A=1e-5;class T{constructor(t){this.options=new O,this._results=new I,this.transform=new w,this.tolerance=A,this.verticalOffset=null,this._ray=u(),this._rayEnd=l(),this._rayBeginTransformed=l(),this._rayEndTransformed=l(),this.viewingMode=t??_.Global}get results(){return this._results}get ray(){return this._ray}get rayBegin(){return this._ray.origin}get rayEnd(){return this._rayEnd}reset(t,r,s){this.resetWithRay(p(t,r,this._ray),s)}resetWithRay(t,r){this.camera=r,t!==this._ray&&g(t,this._ray),0!==this.options.verticalOffset?this.viewingMode===_.Local?this._ray.origin[2]-=this.options.verticalOffset:this.verticalOffset=this.options.verticalOffset:this.verticalOffset=null,a(this._rayEnd,this._ray.origin,this._ray.direction),this._results.init(this._ray)}intersect(r=null,s,i,e,a){this.point=s,this.filterPredicate=e,this.tolerance=i??A;const n=b(this.verticalOffset);if(t(r)&&r.length>0){const s=a?t=>{a(t)&&this.intersectObject(t)}:t=>{this.intersectObject(t)};for(const i of r){const r=i.getSpatialQueryAccelerator&&i.getSpatialQueryAccelerator();t(r)?(t(n)?r.forEachAlongRayWithVerticalOffset(this._ray.origin,this._ray.direction,s,n):r.forEachAlongRay(this._ray.origin,this._ray.direction,s),this.options.selectionMode&&this.options.hud&&r.forEachDegenerateObject(s)):i.objects.forAll((t=>s(t)))}}this.sortResults()}intersectObject(r){const s=r.geometries;if(!s)return;const i=r.transformation,a=b(this.verticalOffset);for(const o of s){if(!o.visible)continue;const{material:s,id:h}=o;this.transform.setAndInvalidateLazyTransforms(i,o.shaderTransformation),n(this._rayBeginTransformed,this.rayBegin,this.transform.inverse),n(this._rayEndTransformed,this.rayEnd,this.transform.inverse);const d=this.transform.transform;t(a)&&(a.objectTransform=this.transform),s.intersect(o,this.transform.transform,this,this._rayBeginTransformed,this._rayEndTransformed,((s,i,a,n,o,c)=>{if(s>=0){if(t(this.filterPredicate)&&!this.filterPredicate(this._ray.origin,this._rayEnd,s))return;const l=n?this._results.hud:this._results,m=n?t=>{const n=new v(r,h,a,c);t.set(j.HUD,n,s,i,e,o)}:t=>t.set(j.OBJECT,{object:r,geometryId:h,triangleNr:a},s,i,d,o);if((null==l.min.drapedLayerOrder||o>=l.min.drapedLayerOrder)&&(null==l.min.dist||s<l.min.dist)&&m(l.min),this.options.store!==L.MIN&&(null==l.max.drapedLayerOrder||o<l.max.drapedLayerOrder)&&(null==l.max.dist||s>l.max.dist)&&m(l.max),this.options.store===L.ALL)if(n){const t=new N(this._ray);m(t),this._results.hud.all.push(t)}else{const t=new M(this._ray);m(t),this._results.all.push(t)}}}))}}sortResults(t=this._results.all){t.sort(((t,s)=>t.dist!==s.dist?r(t.dist,0)-r(s.dist,0):t.drapedLayerOrder!==s.drapedLayerOrder?r(t.drapedLayerOrder,Number.MAX_VALUE)-r(s.drapedLayerOrder,Number.MAX_VALUE):r(s.drapedLayerGraphicOrder,Number.MIN_VALUE)-r(t.drapedLayerGraphicOrder,Number.MIN_VALUE)))}}function x(t){return new T(t)}class I{constructor(){this.min=new M(u()),this.max=new M(u()),this.hud={min:new N(u()),max:new N(u()),all:new Array},this.ground=new M(u()),this.all=[]}init(t){this.min.init(t),this.max.init(t),this.ground.init(t),this.all.length=0,this.hud.min.init(t),this.hud.max.init(t),this.hud.all.length=0}}class M{get ray(){return this._ray}get distanceInRenderSpace(){return t(this.dist)?(o(B,this.ray.direction,this.dist),h(B)):null}getIntersectionPoint(t){return!!E(this)&&(o(B,this.ray.direction,this.dist),a(t,this.ray.origin,B),!0)}getTransformedNormal(t){return d(G,this.normal),G[3]=0,f(G,G,this.transformation),d(t,G),c(t,t)}constructor(t){this.intersector=j.OBJECT,this.normal=l(),this.transformation=i(),this._ray=u(),this.init(t)}init(t){this.dist=null,this.target=null,this.drapedLayerOrder=null,this.drapedLayerGraphicOrder=null,this.intersector=j.OBJECT,g(t,this._ray)}set(t,i,a,n,o,h,c){this.intersector=t,this.dist=a,d(this.normal,r(n,m)),s(this.transformation,r(o,e)),this.target=i,this.drapedLayerOrder=h,this.drapedLayerGraphicOrder=c}copy(t){g(t.ray,this._ray),this.intersector=t.intersector,this.dist=t.dist,this.target=t.target,this.drapedLayerOrder=t.drapedLayerOrder,this.drapedLayerGraphicOrder=t.drapedLayerGraphicOrder,d(this.normal,t.normal),s(this.transformation,t.transformation)}}class N extends M{constructor(){super(...arguments),this.intersector=j.HUD}}function U(t){return new M(t)}const B=l(),G=y();export{A as DEFAULT_TOLERANCE,x as newIntersector,U as newIntersectorResult};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{create as e,offset as t,copy as r}from\"../../../geometry/support/aaBoundingRect.js\";import{RenderTargetType as s,OverlayIndex as i}from\"./interfaces.js\";import{ValidationState as a}from\"../webgl-engine/lib/basicInterfaces.js\";import{fromValues as o}from\"../webgl-engine/lib/LocalOriginFactory.js\";var n;!function(e){e[e.None=0]=\"None\",e[e.ColorAndWater=1]=\"ColorAndWater\",e[e.Highlight=2]=\"Highlight\",e[e.Occluded=3]=\"Occluded\",e[e.ObjectAndLayerIdColor=4]=\"ObjectAndLayerIdColor\"}(n||(n={}));class h{get extent(){return this._extent}constructor(t,r){this.index=t,this.renderTargets=r,this._extent=e(),this.resolution=0,this.renderLocalOrigin=o(0,0,0,\"O\"),this.pixelRatio=1,this.mapUnitsPerPixel=1,this.canvasGeometries=new g,this.hasDrapedFeatureSource=!1,this.hasDrapedRasterSource=!1,this.hasTargetWithoutRasterImage=!1,this.index=t,this.validTargets=new Array(r.renderTargets.length).fill(!1)}getValidTexture(e){return this.validTargets[e]?this.renderTargets.getTarget(e).getTexture():null}get _needsColorWithoutRasterImage(){return this.hasDrapedRasterSource&&this.hasDrapedFeatureSource&&this.hasTargetWithoutRasterImage}getColorTexture(e){const t=e===n.ColorAndWater?this.renderTargets.getTarget(s.Color):e===n.Highlight?this.renderTargets.getTarget(s.Highlight):e===n.ObjectAndLayerIdColor?this.renderTargets.getTarget(s.ObjectAndLayerIdColor):this.renderTargets.getTarget(s.Occluded);return t?t.getTexture():null}getColorTextureNoRasterImage(){return this._needsColorWithoutRasterImage?this.getValidTexture(s.ColorNoRasterImage):this.hasDrapedFeatureSource?this.getValidTexture(s.Color):null}getNormalTexture(e){const t=e===n.ColorAndWater?this.renderTargets.getTarget(s.Water):null;return t?t.getTexture():null}draw(e,t){const r=this.computeRenderTargetValidityBitfield();for(const i of this.renderTargets.renderTargets)i.type!==s.ColorNoRasterImage||this._needsColorWithoutRasterImage?this.validTargets[i.type]=e.drawTarget(this,i,t):this.validTargets[i.type]=!1;return r^this.computeRenderTargetValidityBitfield()?a.CHANGED:a.UNCHANGED}computeRenderTargetValidityBitfield(){const e=this.validTargets;return+e[s.Color]|+e[s.ColorNoRasterImage]<<1|+e[s.Highlight]<<2|+e[s.Water]<<3|+e[s.Occluded]<<4}setupGeometryViewsCyclical(e){this.setupGeometryViewsDirect();const r=.001*e.range;if(this._extent[0]-r<=e.min){const r=this.canvasGeometries.extents[this.canvasGeometries.numViews++];t(this._extent,e.range,0,r)}if(this._extent[2]+r>=e.max){const r=this.canvasGeometries.extents[this.canvasGeometries.numViews++];t(this._extent,-e.range,0,r)}}setupGeometryViewsDirect(){this.canvasGeometries.numViews=1,r(this.canvasGeometries.extents[0],this._extent)}hasSomeSizedView(){for(let e=0;e<this.canvasGeometries.numViews;e++){const t=this.canvasGeometries.extents[e];if(t[0]!==t[2]&&t[1]!==t[3])return!0}return!1}applyViewport(e){e.setViewport(this.index===i.INNER?0:this.resolution,0,this.resolution,this.resolution)}}class g{constructor(){this.extents=[e(),e(),e()],this.numViews=0}}export{h as Overlay,n as OverlaySource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{disposeMaybe as e}from\"../../../core/maybe.js\";import{c as t}from\"../../../chunks/vec2f32.js\";import{TargetType as s,DepthStencilTargetType as i,TextureType as r,PixelFormat as o,PixelType as a,TextureWrapMode as h,TextureSamplingMode as p}from\"../../webgl/enums.js\";import{FramebufferObject as _}from\"../../webgl/FramebufferObject.js\";class f{constructor(e,f){this._size=t(),this._fbo=null,this._fbo=new _(e,{colorTarget:s.TEXTURE,depthStencilTarget:i.NONE},{target:r.TEXTURE_2D,pixelFormat:o.RGBA,dataType:a.UNSIGNED_BYTE,wrapMode:h.CLAMP_TO_EDGE,samplingMode:p.LINEAR_MIPMAP_LINEAR,hasMipmap:f,maxAnisotropy:8,width:0,height:0})}dispose(){this._fbo=e(this._fbo)}getTexture(){return this._fbo?this._fbo.colorTexture:null}isValid(){return null!==this._fbo}resize(e,t){this._size[0]=e,this._size[1]=t,this._fbo.resize(this._size[0],this._size[1])}bind(e){e.bindFramebuffer(this._fbo)}generateMipMap(){const e=this._fbo.colorTexture;e.descriptor.hasMipmap&&e.generateMipmap()}disposeRenderTargetMemory(){this._fbo?.resize(0,0)}get gpuMemoryUsage(){return this._fbo?.gpuMemoryUsage??0}}export{f as OverlayFramebufferObject};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../core/has.js\";import{RenderTargetType as e}from\"./interfaces.js\";import{OverlayFramebufferObject as r}from\"./OverlayFramebufferObject.js\";import{ShaderOutput as t}from\"../webgl-engine/core/shaderLibrary/ShaderOutput.js\";class s{constructor(e,t,s,o=!0){this.output=t,this.type=s,this.valid=!1,this.lastUsed=1/0,this.fbo=new r(e,o)}}class o{constructor(r){this.renderTargets=[new s(r,t.Color,e.Color),new s(r,t.Color,e.ColorNoRasterImage),new s(r,t.Highlight,e.Highlight,!1),new s(r,t.Normal,e.Water),new s(r,t.Color,e.Occluded)],has(\"enable-feature:objectAndLayerId-rendering\")&&this.renderTargets.push(new s(r,t.ObjectAndLayerIdColor,e.ObjectAndLayerIdColor))}getTarget(e){return this.renderTargets[e].fbo}dispose(){for(const e of this.renderTargets)e.fbo.dispose()}disposeRenderTargetMemory(){for(const e of this.renderTargets)e.fbo.disposeRenderTargetMemory()}validateUsageForTarget(e,r,t){if(e)r.lastUsed=t;else if(t-r.lastUsed>a)r.fbo.disposeRenderTargetMemory(),r.lastUsed=1/0;else if(r.lastUsed<1/0)return!0;return!1}get gpuMemoryUsage(){return this.renderTargets.reduce(((e,r)=>e+r.fbo.gpuMemoryUsage),0)}}const a=1e3;export{o as OverlayRenderTarget,s as RenderTargetDescriptor};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e,isSome as t}from\"../../../../../core/maybe.js\";import{NestedMap as r}from\"../../../../../core/NestedMap.js\";import{ShaderTechniqueConfiguration as s}from\"./ShaderTechniqueConfiguration.js\";class n{constructor(e){this._context=e,this._perConstructorInstances=new r,this._frameCounter=0,this._keepAliveFrameCount=c}get viewingMode(){return this._context.viewingMode}get constructionContext(){return this._context}destroy(){this._perConstructorInstances.forEach((e=>e.forEach((e=>e.technique.destroy())))),this._perConstructorInstances.clear()}acquire(t,r=i){const s=r.key;let n=this._perConstructorInstances.get(t,s);if(e(n)){const e=new t(this._context,r,(()=>this.release(e)));n=new o(e),this._perConstructorInstances.set(t,s,n)}return++n.refCount,n.technique}releaseAndAcquire(e,r,s){if(t(s)){if(r.key===s.key)return s;this.release(s)}return this.acquire(e,r)}release(t){if(e(t)||this._perConstructorInstances.empty)return;const r=this._perConstructorInstances.get(t.constructor,t.key);e(r)||(--r.refCount,0===r.refCount&&(r.refZeroFrame=this._frameCounter))}frameUpdate(){this._frameCounter++,this._keepAliveFrameCount!==c&&this._perConstructorInstances.forEach(((e,t)=>{e.forEach(((e,r)=>{0===e.refCount&&e.refZeroFrame+this._keepAliveFrameCount<this._frameCounter&&(e.technique.destroy(),this._perConstructorInstances.delete(t,r))}))}))}async reloadAll(){const e=new Array;this._perConstructorInstances.forEach(((t,r)=>{const s=async(e,t)=>{const r=t.shader;r&&(await r.reload(),e.forEach((e=>e.technique.reload(this._context))))};e.push(s(t,r))})),await Promise.all(e)}}class o{constructor(e){this.technique=e,this.refCount=0,this.refZeroFrame=0}}const c=-1,i=new s;export{n as ShaderTechniqueRepository};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/Logger.js\";import{isNone as t,isSome as r,disposeMaybe as i}from\"../../../../core/maybe.js\";import{NestedMap as s}from\"../../../../core/NestedMap.js\";import{assert as o}from\"./Util.js\";class a{constructor(e,t,r,i){this._textureRepository=e,this._techniqueRepository=t,this.materialChanged=r,this.requestRender=i,this._id2glMaterialRef=new s}dispose(){this._textureRepository.destroy()}acquire(e,r,i){if(this._ownMaterial(e),!e.requiresSlot(r,i))return null;let s=this._id2glMaterialRef.get(i,e.id);if(t(s)){const t=e.createGLMaterial({material:e,techniqueRep:this._techniqueRepository,textureRep:this._textureRepository,output:i});s=new l(t),this._id2glMaterialRef.set(i,e.id,s)}return s.ref(),s.glMaterial}release(e,t){const s=this._id2glMaterialRef.get(t,e.id);r(s)&&(s.unref(),s.referenced||(i(s.glMaterial),this._id2glMaterialRef.delete(t,e.id)))}_ownMaterial(t){r(t.repository)&&t.repository!==this&&e.getLogger(\"esri.views.3d.webgl-engine.lib.GLMaterialRepository\").error(\"Material is already owned by a different material repository\"),t.repository=this}}class l{constructor(e){this.glMaterial=e,this._refCnt=0}ref(){++this._refCnt}unref(){--this._refCnt,o(this._refCnt>=0)}get referenced(){return this._refCnt>0}}export{a as GLMaterialRepository};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{a as i}from\"../../../../chunks/vec2f64.js\";import{CloudsFadeParameters as t}from\"../../environment/CloudsCompositionParameters.js\";import{MultipassGeometryUniforms as s}from\"../core/shaderLibrary/shading/MultipassGeometryTest.glsl.js\";import{MultipassTerrainUniforms as e}from\"../core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{SSRUniforms as r}from\"../core/shaderLibrary/shading/ScreenSpaceReflections.glsl.js\";import{Camera as h}from\"./Camera.js\";import{RenderSlot as n}from\"./RenderSlot.js\";import{TransparencyPassType as a}from\"./TransparencyPassType.js\";import{SceneLighting as g}from\"../lighting/SceneLighting.js\";class o{constructor(o,l,m){this.shadowMap=o,this.ssaoHelper=l,this.slicePlane=m,this.slot=n.OPAQUE_MATERIAL,this.hasOccludees=!1,this.enableFillLights=!0,this.transparencyPassType=a.NONE,this._camera=new h,this._inverseViewport=i(),this.oldLighting=new g,this.newLighting=new g,this._fadedLighting=new g,this._lighting=this.newLighting,this.ssr=new r,this.multipassTerrain=new e,this.multipassGeometry=new s,this.overlays=[],this.cloudsFade=new t}get camera(){return this._camera}set camera(i){this._camera=this.ssr.camera=i,this._inverseViewport[0]=1/i.fullViewport[2],this._inverseViewport[1]=1/i.fullViewport[3]}get inverseViewport(){return this._inverseViewport}get lighting(){return this._lighting}get weatherFading(){return this._lighting===this._fadedLighting}fadeLighting(i){const{oldLighting:t,newLighting:s}=this;i>=1?this._lighting=s:(this._fadedLighting.lerpLighting(t,s,i),this._lighting=this._fadedLighting)}}export{o as BindParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../core/maybe.js\";import{Milliseconds as r}from\"../../../../core/time.js\";import{ShaderOutput as s}from\"../core/shaderLibrary/ShaderOutput.js\";import{BindParameters as t}from\"./BindParameters.js\";import{Camera as c}from\"./Camera.js\";import{RenderOccludedFlag as a}from\"./Material.js\";class i{constructor(r,a,i,o=null){this.rctx=r,this.sliceHelper=o,this.lastFrameCamera=new c,this.output=s.Color,this.renderOccludedMask=n,this.bindParameters=new t(a,i,e(o)?o.plane:null)}resetRenderOccludedMask(){this.renderOccludedMask=n}}class o extends i{constructor(e,s,t,c,a){super(e,t,c,a),this.offscreenRenderingHelper=s,this.sliceHelper=a,this.time=r(0)}}const n=a.Occlude|a.OccludeAndTransparent|a.OccludeAndTransparentStencil;export{i as OverlayRenderContext,o as RenderContext};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../../chunks/tslib.es6.js\";import{property as o}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as t}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{c as e}from\"../../../../chunks/mat4f64.js\";import{Camera as s}from\"./Camera.js\";let p=class extends s{constructor(){super(...arguments),this._projectionMatrix=e()}get projectionMatrix(){return this._projectionMatrix}};r([o()],p.prototype,\"_projectionMatrix\",void 0),r([o({readOnly:!0})],p.prototype,\"projectionMatrix\",null),p=r([t(\"esri.views.3d.webgl-engine.lib.CascadeCamera\")],p);export{p as CascadeCamera};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../core/has.js\";import{clamp as t,lerp as e,acosClamped as s}from\"../../../../core/mathUtils.js\";import{isSome as a,disposeMaybe as i}from\"../../../../core/maybe.js\";import{c as r}from\"../../../../chunks/mat3f64.js\";import{w as h,m as c,a as n,u as o}from\"../../../../chunks/mat4.js\";import{c as _}from\"../../../../chunks/mat4f64.js\";import{s as d,j as u,b as m,l,k as p,a as f,h as x,n as g,f as T,c as b}from\"../../../../chunks/vec2.js\";import{a as w}from\"../../../../chunks/vec2f64.js\";import{k as S,c as M,o as C,m as D,s as E}from\"../../../../chunks/vec3.js\";import{c as j}from\"../../../../chunks/vec3f64.js\";import{s as v,t as R}from\"../../../../chunks/vec4.js\";import{c as z}from\"../../../../chunks/vec4f64.js\";import{ViewingMode as U}from\"../../../ViewingMode.js\";import{CascadeCamera as F}from\"./CascadeCamera.js\";import{assert as y,logWithBase as N,verify as O,rayRay2D as A}from\"./Util.js\";import{TextureType as L,ClearBufferBit as k,PixelFormat as B,PixelType as I,TextureWrapMode as V,TextureSamplingMode as G,TargetType as P,DepthStencilTargetType as X}from\"../../../webgl/enums.js\";import{FramebufferObject as H}from\"../../../webgl/FramebufferObject.js\";import{Texture as Y}from\"../../../webgl/Texture.js\";import{getGpuMemoryUsage as q}from\"../../../webgl/Util.js\";var W;!function(t){t[t.Highlight=0]=\"Highlight\",t[t.Default=1]=\"Default\"}(W||(W={}));class J{constructor(){this.camera=new F,this.lightMat=_()}}class K{get depthTexture(){return this._depthTexture}get textureSize(){return this._textureSize}get numCascades(){return this._numCascades}get cascadeDistances(){return v(this._usedCascadeDistances,this._cascadeDistances[0],this._numCascades>1?this._cascadeDistances[1]:1/0,this._numCascades>2?this._cascadeDistances[2]:1/0,this._numCascades>3?this._cascadeDistances[3]:1/0)}constructor(t,e){this._rctx=t,this._viewingMode=e,this._enabled=!1,this._snapshots=new Array,this._textureSize=0,this._numCascades=1,this._maxNumCascades=4,this._projectionView=_(),this._projectionViewInverse=_(),this._modelViewLight=_(),this._splitSchemeLambda=0,this._cascadeDistances=[0,0,0,0,0],this._usedCascadeDistances=z(),this._cascades=[new J,new J,new J,new J],this._lastOrigin=null,this._maxTextureSize=Math.min(has(\"esri-mobile\")?2048:8192,this._rctx.parameters.maxTextureSize)}dispose(){this.enabled=!1,this.disposeOffscreenBuffers()}disposeOffscreenBuffers(){this._discardDepthTexture(),this._discardAllSnapshots()}set maxCascades(e){this._maxNumCascades=t(Math.floor(e),1,4)}get maxCascades(){return this._maxNumCascades}set enabled(t){this._enabled=t,t||(this._discardDepthTexture(),this._discardAllSnapshots())}get enabled(){return this._enabled}get ready(){return this._enabled&&a(this._depthTexture)}getSnapshot(t){return this.enabled?this._snapshots[t]:null}get cascades(){for(let t=0;t<this._numCascades;++t)ht[t]=this._cascades[t];return ht.length=this._numCascades,ht}start(t,e,s){y(this.enabled),this._textureSize=this._computeTextureSize(t.fullWidth,t.fullHeight),this._ensureDepthTexture();const{near:a,far:i}=this._clampNearFar(s);this._computeCascadeDistances(i,a),this._setupMatrices(t,e);const{viewMatrix:r,projectionMatrix:h}=t;for(let c=0;c<this._numCascades;++c)this._constructCascade(c,h,r,e);this._lastOrigin=null,this.clear()}finish(t){y(this.enabled),this._rctx.bindFramebuffer(t)}getShadowMapMatrices(t){if(!this._lastOrigin||!S(t,this._lastOrigin)){this._lastOrigin=this._lastOrigin||j(),M(this._lastOrigin,t);for(let e=0;e<this._numCascades;++e){h(ct,this._cascades[e].lightMat,t);for(let t=0;t<16;++t)nt[16*e+t]=ct[t]}}return nt}takeCascadeSnapshotTo(t,e){y(this.enabled);const s=this._ensureSnapshot(e);this._bindFbo();const a=this._rctx,i=a.bindTexture(s,Y.TEXTURE_UNIT_FOR_UPDATES);a.gl.copyTexSubImage2D(L.TEXTURE_2D,0,t.camera.viewport[0],t.camera.viewport[1],t.camera.viewport[0],t.camera.viewport[1],t.camera.viewport[2],t.camera.viewport[3]),a.bindTexture(i,Y.TEXTURE_UNIT_FOR_UPDATES)}clear(){const t=this._rctx;this._bindFbo(),t.setClearColor(1,1,1,1),t.clearSafe(k.COLOR_BUFFER_BIT|k.DEPTH_BUFFER_BIT)}_computeTextureSize(t,e){const s=.5*Math.log(t*t+e*e)*Math.LOG2E,a=.35,i=2**Math.round(s+a);return Math.min(this._maxTextureSize,2*i)}_ensureDepthTexture(){if(a(this._depthTexture)&&this._depthTexture.descriptor.width===this._textureSize)return;this._discardDepthTexture();const t={target:L.TEXTURE_2D,pixelFormat:B.RGBA,dataType:I.UNSIGNED_BYTE,wrapMode:V.CLAMP_TO_EDGE,samplingMode:G.NEAREST,flipped:!0,width:this._textureSize,height:this._textureSize};this._depthTexture=new Y(this._rctx,t),this._fbo=new H(this._rctx,{colorTarget:P.TEXTURE,depthStencilTarget:X.DEPTH_RENDER_BUFFER,width:this._textureSize,height:this._textureSize},this._depthTexture)}_ensureSnapshot(t){let e=this._snapshots[t];if(a(e)&&e.descriptor.width===this._textureSize)return e;this._discardSnapshot(t);const s={target:L.TEXTURE_2D,pixelFormat:B.RGBA,dataType:I.UNSIGNED_BYTE,wrapMode:V.CLAMP_TO_EDGE,samplingMode:G.NEAREST,flipped:!0,width:this._textureSize,height:this._textureSize};return e=new Y(this._rctx,s),this._snapshots[t]=e,e}_discardDepthTexture(){this._fbo=i(this._fbo),this._depthTexture=i(this._depthTexture)}_discardSnapshot(t){this._snapshots[t]=i(this._snapshots[t])}_discardAllSnapshots(){for(let t=0;t<this._snapshots.length;++t)this._discardSnapshot(t);this._snapshots.length=0}_bindFbo(){const t=this._rctx;t.unbindTexture(this._depthTexture),t.bindFramebuffer(this._fbo)}_constructCascade(t,e,s,a){const i=this._cascades[t],r=-this._cascadeDistances[t],n=-this._cascadeDistances[t+1],o=(e[10]*r+e[14])/Math.abs(e[11]*r+e[15]),_=(e[10]*n+e[14])/Math.abs(e[11]*n+e[15]);y(o<_);for(let h=0;h<8;++h){v(Z,h%4==0||h%4==3?-1:1,h%4==0||h%4==1?-1:1,h<4?o:_,1);const t=$[h];R(t,Z,this._projectionViewInverse),t[0]/=t[3],t[1]/=t[3],t[2]/=t[3]}C(rt,$[0]),i.camera.viewMatrix=h(Q,this._modelViewLight,rt);for(let h=0;h<8;++h)D($[h],$[h],i.camera.viewMatrix);let d=$[0][2],u=$[0][2];for(let h=1;h<8;++h)d=Math.min(d,$[h][2]),u=Math.max(u,$[h][2]);d-=200,u+=200,i.camera.near=-u,i.camera.far=-d,Et(s,a,d,u,i.camera),c(i.lightMat,i.camera.projectionMatrix,i.camera.viewMatrix);const m=this._textureSize/2;i.camera.viewport=[t%2==0?0:m,0===Math.floor(t/2)?0:m,m,m]}_setupMatrices(t,e){c(this._projectionView,t.projectionMatrix,t.viewMatrix),n(this._projectionViewInverse,this._projectionView);const s=this._viewingMode===U.Global?t.eye:E(rt,0,0,1);o(this._modelViewLight,[0,0,0],[-e[0],-e[1],-e[2]],s)}_clampNearFar(t){let{near:e,far:s}=t;return e<2&&(e=2),s<2&&(s=2),e>=s&&(e=2,s=4),{near:e,far:s}}_computeCascadeDistances(t,s){this._numCascades=Math.min(1+Math.floor(N(t/s,4)),this._maxNumCascades);const a=(t-s)/this._numCascades,i=(t/s)**(1/this._numCascades);let r=s,h=s;for(let c=0;c<this._numCascades+1;++c)this._cascadeDistances[c]=e(r,h,this._splitSchemeLambda),r*=i,h+=a}get gpuMemoryUsage(){return this._snapshots.reduce(((t,e)=>t+q(e)),this._fbo?.gpuMemoryUsage??0)}get test(){const t=this;return{maxNumCascades:this._maxNumCascades,cascades:this._cascades,textureSize:this._textureSize,set splitSchemeLambda(e){t._splitSchemeLambda=e},get splitSchemeLambda(){return t._splitSchemeLambda}}}}const Q=_(),Z=z(),$=[];for(let jt=0;jt<8;++jt)$.push(z());const tt=w(),et=w(),st=w(),at=w(),it=w(),rt=j(),ht=[],ct=_(),nt=new Float32Array(64),ot=w(),_t=w(),dt=[w(),w(),w(),w()],ut=w(),mt=w(),lt=w(),pt=w(),ft=w(),xt=w(),gt=w();function Tt(t,e,s,a,i,r,h,c){d(ot,0,0);for(let d=0;d<4;++d)u(ot,ot,t[d]);m(ot,ot,.25),d(_t,0,0);for(let d=4;d<8;++d)u(_t,_t,t[d]);m(_t,_t,.25),l(dt[0],t[4],t[5],.5),l(dt[1],t[5],t[6],.5),l(dt[2],t[6],t[7],.5),l(dt[3],t[7],t[4],.5);let n=0,o=p(dt[0],ot);for(let d=1;d<4;++d){const t=p(dt[d],ot);t<o&&(o=t,n=d)}f(ut,dt[n],t[n+4]);const _=ut[0];let w,S;ut[0]=-ut[1],ut[1]=_,f(mt,_t,ot),x(mt,ut)<0&&g(ut,ut),l(ut,ut,mt,s),T(ut,ut),w=S=x(f(lt,t[0],ot),ut);for(let d=1;d<8;++d){const e=x(f(lt,t[d],ot),ut);e<w?w=e:e>S&&(S=e)}b(a,ot),m(lt,ut,w-e),u(a,a,lt);let M=-1,C=1,D=0,E=0;for(let d=0;d<8;++d){f(pt,t[d],a),T(pt,pt);const e=ut[0]*pt[1]-ut[1]*pt[0];e>0?e>M&&(M=e,D=d):e<C&&(C=e,E=d)}O(M>0,\"leftArea\"),O(C<0,\"rightArea\"),m(ft,ut,w),u(ft,ft,ot),m(xt,ut,S),u(xt,xt,ot),gt[0]=-ut[1],gt[1]=ut[0];const j=A(a,t[E],xt,u(lt,xt,gt),1,i),v=A(a,t[D],xt,lt,1,r),R=A(a,t[D],ft,u(lt,ft,gt),1,h),z=A(a,t[E],ft,lt,1,c);O(j,\"rayRay\"),O(v,\"rayRay\"),O(R,\"rayRay\"),O(z,\"rayRay\")}function bt(t,e){return 3*e+t}const wt=w();function St(t,e){return d(wt,t[e],t[e+3]),wt}const Mt=w(),Ct=r();function Dt(t,e,s,a,i){f(Mt,s,a),m(Mt,Mt,.5),Ct[0]=Mt[0],Ct[1]=Mt[1],Ct[2]=0,Ct[3]=Mt[1],Ct[4]=-Mt[0],Ct[5]=0,Ct[6]=Mt[0]*Mt[0]+Mt[1]*Mt[1],Ct[7]=Mt[0]*Mt[1]-Mt[1]*Mt[0],Ct[8]=1,Ct[bt(0,2)]=-x(St(Ct,0),t),Ct[bt(1,2)]=-x(St(Ct,1),t);let r=x(St(Ct,0),s)+Ct[bt(0,2)],h=x(St(Ct,1),s)+Ct[bt(1,2)],c=x(St(Ct,0),a)+Ct[bt(0,2)],n=x(St(Ct,1),a)+Ct[bt(1,2)];r=-(r+c)/(h+n),Ct[bt(0,0)]+=Ct[bt(1,0)]*r,Ct[bt(0,1)]+=Ct[bt(1,1)]*r,Ct[bt(0,2)]+=Ct[bt(1,2)]*r,r=1/(x(St(Ct,0),s)+Ct[bt(0,2)]),h=1/(x(St(Ct,1),s)+Ct[bt(1,2)]),Ct[bt(0,0)]*=r,Ct[bt(0,1)]*=r,Ct[bt(0,2)]*=r,Ct[bt(1,0)]*=h,Ct[bt(1,1)]*=h,Ct[bt(1,2)]*=h,Ct[bt(2,0)]=Ct[bt(1,0)],Ct[bt(2,1)]=Ct[bt(1,1)],Ct[bt(2,2)]=Ct[bt(1,2)],Ct[bt(1,2)]+=1,r=x(St(Ct,1),e)+Ct[bt(1,2)],h=x(St(Ct,2),e)+Ct[bt(2,2)],c=x(St(Ct,1),s)+Ct[bt(1,2)],n=x(St(Ct,2),s)+Ct[bt(2,2)],r=-.5*(r/h+c/n),Ct[bt(1,0)]+=Ct[bt(2,0)]*r,Ct[bt(1,1)]+=Ct[bt(2,1)]*r,Ct[bt(1,2)]+=Ct[bt(2,2)]*r,r=x(St(Ct,1),e)+Ct[bt(1,2)],h=x(St(Ct,2),e)+Ct[bt(2,2)],c=-h/r,Ct[bt(1,0)]*=c,Ct[bt(1,1)]*=c,Ct[bt(1,2)]*=c,i[0]=Ct[0],i[1]=Ct[1],i[2]=0,i[3]=Ct[2],i[4]=Ct[3],i[5]=Ct[4],i[6]=0,i[7]=Ct[5],i[8]=0,i[9]=0,i[10]=1,i[11]=0,i[12]=Ct[6],i[13]=Ct[7],i[14]=0,i[15]=Ct[8]}function Et(t,e,a,i,r){const h=1/$[0][3],c=1/$[4][3];y(h<c);let n=h+Math.sqrt(h*c);const o=Math.sin(s(t[2]*e[0]+t[6]*e[1]+t[10]*e[2]));n/=o,Tt($,n,o,tt,et,st,at,it),Dt(tt,et,at,it,r.projectionMatrix),r.projectionMatrix[10]=2/(a-i),r.projectionMatrix[14]=-(a+i)/(a-i)}export{K as ShadowMap,W as SnapshotSlot};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{IntersectorType as e}from\"../webgl-engine/lib/IntersectorInterfaces.js\";import{Graphic3DTarget as r}from\"../webgl-engine/lib/IntersectorTarget.js\";import{isValidIntersectorResult as t}from\"../webgl-engine/lib/intersectorUtils.js\";function n(r){return t(r)&&r.intersector===e.TERRAIN&&!!r.target}class i extends r{constructor(e,r,t){super(e,r),this.triangleNr=t}}function o(r){return t(r)&&r.intersector===e.OVERLAY&&!!r.target}export{i as OverlayTarget,o as isOverlayIntersectorResult,n as isTerrainIntersectorResult};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/PooledArray.js\";class s{constructor(){this.adds=new e,this.removes=new e,this.updates=new e({allocator:e=>e||new r,deallocator:e=>(e.renderGeometry=null,e)})}clear(){this.adds.clear(),this.removes.clear(),this.updates.clear()}prune(){this.adds.prune(),this.removes.prune(),this.updates.prune()}get empty(){return 0===this.adds.length&&0===this.removes.length&&0===this.updates.length}}class r{}class t{constructor(){this.adds=new Array,this.removes=new Array,this.updates=new Array}}export{s as ChangeSet,t as MaterialChangeSet,r as RenderGeometryUpdate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar E,I;!function(E){E[E.ADD=0]=\"ADD\",E[E.UPDATE=1]=\"UPDATE\",E[E.REMOVE=2]=\"REMOVE\"}(E||(E={})),function(E){E[E.NONE=0]=\"NONE\",E[E.VISIBILITY=1]=\"VISIBILITY\",E[E.GEOMETRY=2]=\"GEOMETRY\",E[E.TRANSFORMATION=4]=\"TRANSFORMATION\",E[E.HIGHLIGHT=8]=\"HIGHLIGHT\",E[E.OCCLUDEE=16]=\"OCCLUDEE\"}(I||(I={}));export{E as DirtyOperation,I as DirtyState};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{MaterialChangeSet as e}from\"./ChangeSet.js\";function r(r){const n=new Map,o=r=>{let t=n.get(r);return t||(t=new e,n.set(r,t)),t};return r.removes.forAll((e=>{t(e)&&o(e.material).removes.push(e)})),r.adds.forAll((e=>{t(e)&&o(e.material).adds.push(e)})),r.updates.forAll((e=>{t(e.renderGeometry)&&o(e.renderGeometry.material).updates.push(e)})),n}function t(e){return e.geometry.indexCount>=1}export{r as splitRenderGeometryChangeSetByMaterial};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{BufferObject as r}from\"../../../webgl/BufferObject.js\";import{Usage as e,BufferType as t,PrimitiveType as i,DataType as s}from\"../../../webgl/enums.js\";class n{constructor(r){this._rctx=r,this._indexBuffer=this._createIndexbuffer(),this._program=this._createProgram()}_createProgram(){const r=\"\\n    void main(void) {\\n      gl_Position = vec4(0.0, 0.0, float(gl_VertexID)-2.0, 1.0);\\n    }\",e=\"\\n    void main(void) {\\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\\n    }\";return this._rctx.programCache.acquire(r,e,new Map([]))}_createIndexbuffer(){return r.createIndex(this._rctx,e.STATIC_DRAW,new Uint32Array([0]))}resetIndicesType(){this._program.compiled&&this._indexBuffer&&(this._rctx.bindVAO(null),this._rctx.useProgram(this._program),this._rctx.bindBuffer(this._indexBuffer,t.ELEMENT_ARRAY_BUFFER),this._rctx.drawElements(i.POINTS,1,s.UNSIGNED_INT,0))}dispose(){this._program.dispose(),this._indexBuffer.dispose()}}export{n as AppleAmdDriverHelper};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as r}from\"../../../../core/maybe.js\";import{ResourceState as t}from\"./basicInterfaces.js\";class e{constructor(r,t){this._material=r,this._repository=t,this._map=new Map}destroy(){this._map.forEach(((t,e)=>{r(t)&&this._repository.release(this._material,e)}))}load(e,s,i){if(!this._material.requiresSlot(s,i))return null;this._map.has(i)||this._map.set(i,this._repository.acquire(this._material,s,i));const a=this._map.get(i);if(r(a)){if(a.ensureResources(e)===t.LOADED)return a;this._repository.requestRender()}return null}}export{e as GLMaterials};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as r}from\"../../../../chunks/vec3f64.js\";import{VertexNormalDrawParameters as s}from\"../core/shaderLibrary/attributes/VertexNormal.glsl.js\";class i extends s{constructor(s=r()){super(),this.origin=s,this.slicePlaneLocalOrigin=this.origin}}export{i as DrawParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{newLayout as O}from\"../../support/buffer/InterleavedLayout.js\";import{VertexAttribute as e}from\"../lib/VertexAttribute.js\";const I=O().vec3f(e.POSITION),c=O().vec3f(e.POSITION).vec2f(e.UV0),v=O().vec3f(e.POSITION).vec4u8(e.COLOR),f=O().vec3f(e.POSITION).vec4u8(e.OBJECTANDLAYERIDCOLOR),t=O().vec3f(e.POSITION).vec2f(e.UV0).vec4u8(e.OBJECTANDLAYERIDCOLOR),r=O().vec3f(e.POSITION).vec4u8(e.COLOR).vec4u8(e.OBJECTANDLAYERIDCOLOR);export{v as PositionColorLayout,r as PositionColorLayoutObjectAndLayerId,I as PositionLayout,f as PositionLayoutObjectAndLayerId,c as PositionUVLayout,t as PositionUVLayoutObjectAndLayerId};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Material as r}from\"../lib/Material.js\";import{intersectTriangleGeometry as t}from\"./internal/MaterialUtil.js\";class e extends r{intersect(r,e,i,a,l,o){return t(r,i,a,l,void 0,o)}}export{e as TriangleMaterial};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import{ViewingMode as t}from\"../../../ViewingMode.js\";import{ShaderOutput as o}from\"../core/shaderLibrary/ShaderOutput.js\";import{PBRMode as r}from\"../core/shaderLibrary/shading/PhysicallyBasedRenderingParameters.glsl.js\";import{WaterDistortionPassParameters as i}from\"../core/shaderLibrary/shading/WaterDistortion.glsl.js\";import{ReloadableShaderModule as s}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{ShaderTechnique as a}from\"../core/shaderTechnique/ShaderTechnique.js\";import{parameter as n}from\"../core/shaderTechnique/ShaderTechniqueConfiguration.js\";import{Default3D as p}from\"../lib/DefaultVertexAttributeLocations.js\";import{blendingDefault as l,oitBlending as u,oitDepthTest as c,oitDepthWrite as h,getOITPolygonOffset as d}from\"../lib/OrderIndependentTransparency.js\";import{Program as f}from\"../lib/Program.js\";import{TransparencyPassType as m}from\"../lib/TransparencyPassType.js\";import{DefaultTechniqueConfiguration as y}from\"./DefaultTechniqueConfiguration.js\";import{W as b}from\"../../../../chunks/WaterSurface.glsl.js\";import{ContextType as g}from\"../../../webgl/context-util.js\";import{makePipelineState as v,defaultDepthWriteParams as T,defaultColorWriteParams as S}from\"../../../webgl/renderState.js\";class j extends i{}class P extends a{initializeConfiguration(e,o){o.hasWebGL2Context=e.rctx.type===g.WEBGL2,o.spherical=e.viewingMode===t.Global,o.doublePrecisionRequiresObfuscation=e.rctx.driverTest.doublePrecisionRequiresObfuscation.result}initializeProgram(e){return new f(e.rctx,P.shader.get().build(this.configuration),p)}_setPipelineState(e){const t=this.configuration,r=e===m.NONE,i=e===m.FrontFace;return v({blending:t.output!==o.Normal&&t.output!==o.Highlight&&t.transparent?r?l:u(e):null,depthTest:{func:c(e)},depthWrite:r?t.writeDepth?T:null:h(e),colorWrite:S,polygonOffset:r||i?null:d(t.enableOffset)})}initializePipeline(){return this._setPipelineState(this.configuration.transparencyPassType)}}P.shader=new s(b,(()=>import(\"../shaders/WaterSurface.glsl.js\")));class O extends y{constructor(){super(...arguments),this.output=o.Color,this.transparencyPassType=m.NONE,this.spherical=!1,this.receiveShadows=!1,this.hasSlicePlane=!1,this.transparent=!1,this.enableOffset=!0,this.writeDepth=!1,this.hasScreenSpaceReflections=!1,this.doublePrecisionRequiresObfuscation=!1,this.hasCloudsReflections=!1,this.isDraped=!1,this.hasMultipassTerrain=!1,this.cullAboveGround=!1}}e([n({count:o.COUNT})],O.prototype,\"output\",void 0),e([n({count:m.COUNT})],O.prototype,\"transparencyPassType\",void 0),e([n()],O.prototype,\"spherical\",void 0),e([n()],O.prototype,\"receiveShadows\",void 0),e([n()],O.prototype,\"hasSlicePlane\",void 0),e([n()],O.prototype,\"transparent\",void 0),e([n()],O.prototype,\"enableOffset\",void 0),e([n()],O.prototype,\"writeDepth\",void 0),e([n()],O.prototype,\"hasScreenSpaceReflections\",void 0),e([n()],O.prototype,\"doublePrecisionRequiresObfuscation\",void 0),e([n()],O.prototype,\"hasCloudsReflections\",void 0),e([n()],O.prototype,\"isDraped\",void 0),e([n()],O.prototype,\"hasMultipassTerrain\",void 0),e([n()],O.prototype,\"cullAboveGround\",void 0),e([n({constValue:r.Water})],O.prototype,\"pbrMode\",void 0),e([n({constValue:!0})],O.prototype,\"useCustomDTRExponentForWater\",void 0),e([n({constValue:!0})],O.prototype,\"highStepCount\",void 0),e([n({constValue:!1})],O.prototype,\"useFillLights\",void 0);export{j as WaterMaterialPassParameters,P as WaterTechnique,O as WaterTechniqueConfiguration};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../core/maybe.js\";import{ShaderOutput as t}from\"../core/shaderLibrary/ShaderOutput.js\";import a from\"../lib/GLMaterial.js\";import{WaterTechnique as s}from\"./WaterTechnique.js\";class r extends a{_updateShadowState(e){e.shadowMap.enabled!==this._material.parameters.receiveShadows&&this._material.setParameters({receiveShadows:e.shadowMap.enabled})}_updateSSRState(e){e.ssr.enabled!==this._material.parameters.hasScreenSpaceReflections&&this._material.setParameters({hasScreenSpaceReflections:e.ssr.enabled})}_updateCloudsReflectionState(t){const a=e(t.cloudsFade.data);a!==this._material.parameters.hasCloudsReflections&&this._material.setParameters({hasCloudsReflections:a})}ensureResources(e){return this._techniqueRepository.constructionContext.waterTextureRepository.ensureResources(e)}beginSlot(e){return this._output===t.Color&&(this._updateShadowState(e),this._updateSSRState(e),this._updateCloudsReflectionState(e)),this._material.setParameters(this._techniqueRepository.constructionContext.waterTextureRepository.passParameters),this.ensureTechnique(s,e)}}export{r as WaterGLMaterial};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{secondsFromMilliseconds as e}from\"../../../../core/time.js\";import{f as t}from\"../../../../chunks/vec2f64.js\";import{f as r}from\"../../../../chunks/vec4f64.js\";import{ShaderOutput as i}from\"../core/shaderLibrary/ShaderOutput.js\";import{AnimationTimer as a}from\"../lib/AnimationTimer.js\";import{MaterialParameters as s}from\"../lib/Material.js\";import{OITPolygonOffsetLimit as n}from\"../lib/OrderIndependentTransparency.js\";import{RenderSlot as o}from\"../lib/RenderSlot.js\";import{DefaultBufferWriter as h}from\"./DefaultBufferWriter.js\";import{PositionUVLayout as c}from\"./DefaultLayouts.js\";import{TriangleMaterial as p}from\"./TriangleMaterial.js\";import{WaterGLMaterial as m}from\"./WaterGLMaterial.js\";import{WaterTechniqueConfiguration as l}from\"./WaterTechnique.js\";class u extends p{constructor(e){super(e,new f),this._configuration=new l,this.animation=new a}getConfiguration(e,t){return this._configuration.output=e,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.receiveShadows=this.parameters.receiveShadows,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.transparent=this.parameters.transparent,this._configuration.hasScreenSpaceReflections=this.parameters.hasScreenSpaceReflections,this._configuration.hasCloudsReflections=this.parameters.hasCloudsReflections,this._configuration.isDraped=this.parameters.isDraped,this._configuration.transparencyPassType=t.transparencyPassType,this._configuration.enableOffset=t.camera.relativeElevation<n,this._configuration.hasMultipassTerrain=t.multipassTerrain.enabled,this._configuration.cullAboveGround=t.multipassTerrain.cullAboveGround,this._configuration}update(t){const r=Math.min(t.camera.relativeElevation,t.camera.distance);this.animation.enabled=Math.sqrt(this.parameters.waveTextureRepeat/this.parameters.waveStrength)*r<d;const i=this.animation.advance(t);return this.setParameters({timeElapsed:e(this.animation.time)*this.parameters.animationSpeed},!1),this.animation.enabled&&i}requiresSlot(e,t){switch(t){case i.Normal:return e===o.DRAPED_WATER;case i.Color:if(this.parameters.isDraped)return e===o.DRAPED_MATERIAL;break;case i.Alpha:break;case i.Highlight:return e===o.OPAQUE_MATERIAL||e===o.DRAPED_MATERIAL;default:return!1}let r=o.OPAQUE_MATERIAL;return this.parameters.transparent&&(r=this.parameters.writeDepth?o.TRANSPARENT_MATERIAL:o.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL),e===r}createGLMaterial(e){return new m(e)}createBufferWriter(){return new h(c)}}class f extends s{constructor(){super(...arguments),this.waveStrength=.06,this.waveTextureRepeat=32,this.waveDirection=t(1,0),this.waveVelocity=.05,this.flowStrength=.015,this.flowOffset=-.5,this.animationSpeed=.35,this.timeElapsed=0,this.color=r(0,0,0,0),this.transparent=!0,this.writeDepth=!0,this.hasSlicePlane=!1,this.isDraped=!1,this.receiveShadows=!0,this.hasScreenSpaceReflections=!1,this.hasCloudsReflections=!1}}const d=35e3;export{u as WaterMaterial,f as WaterMaterialParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t=0,o=0){this.from=t,this.to=o}get numElements(){return this.to-this.from}}function o(t){const o=new Map;t.forAll((t=>o.set(t.from,t)));let e=!0;for(;e;)e=!1,t.forEach((r=>{const s=o.get(r.to);s&&(r.to=s.to,o.delete(s.from),t.removeUnordered(s),e=!0)}))}export{t as BufferRange,o as mergeAdjacentRanges};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../../core/maybe.js\";import{BufferRange as s}from\"./BufferRange.js\";class t extends s{constructor(e,s,t){super(s,t),this.geometry=e}get isVisible(){return this.geometry.visible}get hasHighlights(){return e(this.geometry.highlights)&&this.isVisible}get hasOccludees(){return e(this.geometry.occludees)}}export{t as Instance};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(){this.first=0,this.count=0}}export{t as DrawCommand};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as s}from\"../../../../../core/maybe.js\";import t from\"../../../../../core/PooledArray.js\";import{<PERSON>ufferRange as e}from\"./BufferRange.js\";import{DrawCommand as n}from\"./DrawCommand.js\";class i{constructor(){this._numElements=0,this._instances=new Map,this.holes=new t({allocator:s=>s||new e,deallocator:null}),this.hasHiddenInstances=!1,this.hasHighlights=!1,this.hasOccludees=!1,this.drawCommandsDirty=!0,this.drawCommandsDefault=h(),this.drawCommandsHighlight=h(),this.drawCommandsOccludees=h(),this.drawCommandsShadowHighlightRest=h()}get numElements(){return this._numElements}get instances(){return this._instances}addInstance(s,t){this.deleteInstance(s),this._instances.set(s,t),this._numElements+=t.numElements}deleteInstance(s){const t=this._instances.get(s);t&&(this._numElements-=t.numElements,this._instances.delete(s))}updateInstance(s,t,e){const n=this._instances.get(s);n&&(this._numElements-=n.numElements,n.from=t,n.to=e,this._numElements+=n.numElements)}updateDrawState(s){s.isVisible?(s.hasHighlights&&(this.hasHighlights=!0),s.hasOccludees&&(this.hasOccludees=!0)):this.hasHiddenInstances=!0}updateDrawCommands(t){if(this.drawCommandsDefault.clear(),this.drawCommandsHighlight.clear(),this.drawCommandsOccludees.clear(),this.drawCommandsShadowHighlightRest.clear(),this.drawCommandsDirty=!1,0===this._instances.size)return;if(!this.needsMultipleCommands()){const e=this.drawCommandsDefault.pushNew(),n=this.holes.front();return s(this.vao)&&1===this.holes.length&&n.to===Math.floor(this.vao.size/t)?(e.first=0,void(e.count=n.from)):(e.first=1/0,e.count=0,this._instances.forEach((s=>{e.first=Math.min(e.first,s.from),e.count=Math.max(e.count,s.to)})),void(e.count-=e.first))}const e=Array.from(this._instances.values()).sort(((s,t)=>s.from===t.from?s.to-t.to:s.from-t.from));for(const s of e)s.isVisible&&(r(s.hasOccludees?this.drawCommandsOccludees:this.drawCommandsDefault,s),r(s.hasHighlights?this.drawCommandsHighlight:this.drawCommandsShadowHighlightRest,s))}needsMultipleCommands(){return this.hasOccludees||this.hasHighlights||this.hasHiddenInstances}}class a extends i{}function o(t){return s(t.vao)}function h(){return new t({allocator:s=>s||new n,deallocator:s=>s})}function r(s,t){const e=s.back();if(null==e){const e=s.pushNew();return e.first=t.from,void(e.count=t.numElements)}if(m(e,t)){const s=t.from-e.first+t.numElements;e.count=s}else{const e=s.pushNew();e.first=t.from,e.count=t.numElements}}function m(s,t){return s.first+s.count>=t.from}export{i as PerBufferData,a as PerVaoData,o as hasVao};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass s{constructor(s){this.origin=s,this.buffers=new Array}dispose(){this.buffers.forEach((s=>s.vao.dispose())),this.buffers.length=0}findBuffer(s){return this.buffers.find((r=>r.instances.has(s)))}}export{s as PerOriginData};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,isNone as e}from\"../../../../../core/maybe.js\";import{RemoveMode as o,MIN_PRIORITY as r}from\"../../../../../core/MemCache.js\";import{generateUID as s}from\"../../../../../core/uid.js\";import{VertexArrayObject as c}from\"../../lib/VertexArrayObject.js\";import{BufferObject as i}from\"../../../../webgl/BufferObject.js\";import{Usage as h}from\"../../../../webgl/enums.js\";const n=r+1;class a{constructor(t,e,o){this._rctx=t,this._locations=e,this._layout=o,this._cache=t.newCache(`VaoCache ${s()}`,p)}dispose(){this._cache.destroy()}newVao(e){const o=e.toString(),r=this._cache.pop(o);if(t(r)){const t=r.pop();return r.length>0&&this._cache.put(o,r,e*r.length,n),t}const s=new c(this._rctx,this._locations,{geometry:this._layout},{geometry:i.createVertex(this._rctx,h.STATIC_DRAW)});return s.vertexBuffers.geometry.setSize(e),s}deleteVao(o){if(e(o))return null;const r=o.size,s=r.toString(),c=this._cache.pop(s);return t(c)?(c.push(o),this._cache.put(s,c,r*c.length,-1)):this._cache.put(s,[o],r,-1),null}}function p(t,e){if(e===o.ALL)return void t.forEach((t=>t.dispose()));const r=t.pop(),s=t.length*r.size;return r.dispose(),s}export{a as VaoCache};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{removeUnordered as e,filterInPlace as t}from\"../../../../../core/arrayUtils.js\";import{someMap as r}from\"../../../../../core/MapUtils.js\";import{isNone as s,isSome as i}from\"../../../../../core/maybe.js\";import{NestedMap as a}from\"../../../../../core/NestedMap.js\";import o from\"../../../../../core/PooledArray.js\";import{m as n,a as l,t as h}from\"../../../../../chunks/mat4.js\";import{c as u}from\"../../../../../chunks/mat4f64.js\";import{glLayout as f}from\"../../../support/buffer/glUtil.js\";import{ShaderOutput as c}from\"../../core/shaderLibrary/ShaderOutput.js\";import{AppleAmdDriverHelper as d}from\"../../lib/AppleAmdDriverHelper.js\";import{GLMaterials as m}from\"../../lib/GLMaterials.js\";import{RenderOccludedFlag as g}from\"../../lib/Material.js\";import{DirtyState as p}from\"../../lib/ModelDirtyTypes.js\";import{assert as y,setMatrixTranslation3 as _}from\"../../lib/Util.js\";import{DrawParameters as v}from\"../DrawParameters.js\";import{WaterMaterial as w}from\"../WaterMaterial.js\";import{BufferRange as b,mergeAdjacentRanges as O}from\"./BufferRange.js\";import{Instance as A}from\"./Instance.js\";import{PerBufferData as B,hasVao as C}from\"./PerBufferData.js\";import{PerOriginData as E}from\"./PerOriginData.js\";import{VaoCache as D}from\"./VaoCache.js\";class H{constructor(e,t,r){this._rctx=e,this._materialRepository=t,this.material=r,this._dataByOrigin=new Map,this._appleAmdDriverHelper=null,this._hasHighlights=!1,this._hasOccludees=!1,this._glMaterials=new m(this.material,this._materialRepository),this._bufferWriter=r.createBufferWriter(),this._vaoCache=new D(e,r.vertexAttributeLocations,f(this._bufferWriter.vertexBufferLayout)),this._rctx.driverTest.drawArraysRequiresIndicesTypeReset.result&&(this._appleAmdDriverHelper=new d(this._rctx))}dispose(){this._glMaterials.destroy(),this._dataByOrigin.forEach((e=>e.dispose())),this._dataByOrigin.clear(),this._vaoCache.dispose(),this._appleAmdDriverHelper?.dispose()}get isEmpty(){return 0===this._dataByOrigin.size}get hasHighlights(){return this._hasHighlights}get hasOccludees(){return this._hasOccludees}get hasWater(){return!this.isEmpty&&this.material instanceof w}get rendersOccluded(){return!this.isEmpty&&this.material.renderOccluded!==g.Occlude}get numGeometries(){let e=0;return this._dataByOrigin.forEach((t=>e+=t.buffers.reduce(((e,t)=>e+t.instances.size),0))),e}forEachGeometry(e){this._dataByOrigin.forEach((t=>t.buffers.forEach((t=>t.instances.forEach((t=>e(t.geometry)))))))}modify(e){this._updateGeometries(e.updates),this._addAndRemoveGeometries(e.adds,e.removes),this._updateDrawCommands()}_updateGeometries(e){const t=this._bufferWriter,r=t.vertexBufferLayout.stride/4;for(const i of e){const e=i.renderGeometry,a=this._dataByOrigin.get(e.localOrigin.id)?.findBuffer(e.id);if(s(a))return;const o=a.instances.get(e.id);if(i.updateType&(p.GEOMETRY|p.TRANSFORMATION)){const s=z(t.elementCount(o.geometry.geometry)*r),i=t.vertexBufferLayout.createView(s.buffer);this._writeGeometry(e,i,0),a.vao.vertexBuffers.geometry.setSubData(s,o.from*r,0,o.numElements*r)}i.updateType&(p.HIGHLIGHT|p.OCCLUDEE|p.VISIBILITY)&&(a.drawCommandsDirty=!0)}}_computeDeltas(e,t){const r=new a;for(const i of e){const e=i.localOrigin;if(s(e))continue;let t=r.get(e.id,null);s(t)&&(t=new x(e.vec3),r.set(e.id,null,t)),t.changes.push(i)}for(const i of t){const e=i.localOrigin;if(s(e))continue;const t=this._dataByOrigin.get(e.id)?.findBuffer(i.id);if(s(t))continue;let a=r.get(e.id,t);s(a)&&(a=new x(e.vec3),r.set(e.id,t,a)),a.changes.push(i)}return r}_addAndRemoveGeometries(t,r){const{_bufferWriter:a,_dataByOrigin:o}=this,n=a.vertexBufferLayout.stride/4,l=this._computeDeltas(t,r);l.forEach(((t,r)=>{const h=t.get(null),u=i(h)?h.changes:[];l.delete(r,null);let f=o.get(r);if(t.forEach(((t,i)=>{if(l.delete(r,i),s(i))return void y(!1,\"No VAO for removed geometries\");if(i.instances.size===t.changes.length)return this._vaoCache.deleteVao(i.vao),e(f.buffers,i),void(0===f.buffers.length&&0===u.length&&o.delete(r));const h=i.numElements,c=i.vao.size/4,d=u.reduce(((e,t)=>e+a.elementCount(t.geometry)),0),m=t.changes.reduce(((e,t)=>e+a.elementCount(t.geometry)),0),g=Math.min((h+d-m)*n,W),p=g>c;g>L&&g<c/2?(t.changes.forEach((({id:e})=>i.deleteInstance(e))),i.instances.forEach((({geometry:e})=>u.push(e))),this._vaoCache.deleteVao(i.vao),e(f.buffers,i)):p?this._applyAndRebuild(i,u,t):this._applyRemoves(i,t)})),u.length>0)for(s(f)&&(f=new E(h.origin),o.set(r,f)),f.buffers.forEach((e=>this._applyAdds(e,u)));u.length>0;)f.buffers.push(this._applyAndRebuild(new B,u,null))}))}_updateDrawCommands(){this._hasHighlights=!1,this._hasOccludees=!1,this._dataByOrigin.forEach((e=>{e.buffers.forEach((e=>{e.drawCommandsDirty&&(e.hasHiddenInstances=!1,e.hasHighlights=!1,e.hasOccludees=!1,r(e.instances,(t=>(e.updateDrawState(t),e.hasHiddenInstances&&e.hasHighlights&&e.hasOccludees))),e.updateDrawCommands(this._bufferWriter.vertexBufferLayout.stride)),this._hasHighlights=this._hasHighlights||e.hasHighlights,this._hasOccludees=this._hasOccludees||e.hasOccludees}))}))}_applyAndRebuild(e,t,r){if(i(r))for(const i of r.changes)e.deleteInstance(i.id);const s=this._bufferWriter,a=s.vertexBufferLayout.stride,o=a/4,n=Math.floor(W/o);let l=e.numElements;for(;t.length>0;){const r=t.pop(),i=s.elementCount(r.geometry);if(l+i>n&&l>0){t.push(r);break}l+=i;const a=new A(r,0,0);y(null==e.instances.get(r.id)),e.addInstance(r.id,a)}const h=l*o,u=z(h),f=s.vertexBufferLayout.createView(u.buffer);let c=0;e.hasHiddenInstances=!1,e.hasHighlights=!1,e.hasOccludees=!1,e.instances.forEach(((t,r)=>{this._writeGeometry(t.geometry,f,c);const i=c;c+=s.elementCount(t.geometry.geometry),e.updateInstance(r,i,c),e.updateDrawState(t)})),this._vaoCache.deleteVao(e.vao),e.vao=this._vaoCache.newVao(N(h)),e.vao.vertexBuffers.geometry.setSubData(u,0,0,c*o),e.holes.clear();const d=e.holes.pushNew();return d.from=c,d.to=Math.floor(e.vao.size/a),e.updateDrawCommands(a),e}_applyRemoves(e,t){if(0===t.changes.length)return;for(const o of t.changes){const t=o.id,r=e.instances.get(t);if(!r)continue;e.deleteInstance(t);const s=R.back();if(s){if(s.to===r.from){s.to=r.to;continue}if(s.from===r.to){s.from=r.from;continue}}const i=R.pushNew();i.from=r.from,i.to=r.to}O(R);const r=this._bufferWriter.vertexBufferLayout.stride/4,s=R.reduce(((e,t)=>Math.max(e,t.numElements)),0)*r,i=z(s);i.fill(0,0,s);const a=e.vao.vertexBuffers.geometry;R.forAll((e=>a.setSubData(i,e.from*r,0,e.numElements*r))),e.holes.pushArray(R.data,R.length),R.forAll(((e,t)=>R.data[t]=null)),R.clear(),e.drawCommandsDirty=!0}_applyAdds(e,r){if(0===r.length)return;if(!C(e))return void this._applyAndRebuild(e,r,null);const i=this._bufferWriter,a=i.vertexBufferLayout.stride/4,o=e.numElements,n=r.reduce(((e,t)=>e+i.elementCount(t.geometry)),0),l=Math.min((o+n)*a,W),h=4*l;if(e.vao.size<N(W-L)&&h>e.vao.size)return void this._applyAndRebuild(e,r,null);O(e.holes);const u=new Array;for(const t of r){const r=i.elementCount(t.geometry),s=M(e.holes,r);u.push(s)}const f=e.vao.vertexBuffers.geometry;let c=0,d=0,m=0;const g=z(l),p=i.vertexBufferLayout.createView(g.buffer);r.forEach(((t,r)=>{const o=u[r];if(s(o))return;if(!(m===o)){const e=m-d;e>0&&f.setSubData(g,d*a,0,e*a),d=o,c=0}const n=i.elementCount(t.geometry);this._writeGeometry(t,p,c),c+=n,m=o+n;const l=new A(t,o,o+n);y(null==e.instances.get(t.id)),e.addInstance(t.id,l),e.drawCommandsDirty=!0}));const _=m-d;_>0&&f.setSubData(g,d*a,0,_*a),t(r,((e,t)=>s(u[t])))}_writeGeometry(e,t,r){const s=e.localOrigin.vec3;_(j,-s[0],-s[1],-s[2]);const i=n(I,j,e.transformation);l(S,i),h(S,S),this._bufferWriter.write(i,S,e.geometry,t,r)}updateAnimation(e){return this.material.update(e)}requiresSlot(e,t){return this.material.requiresSlot(e,t)}render(e,t){if(!this.requiresSlot(t.slot,e))return!1;const r=e===c.Highlight||e===c.ShadowHighlight;if(r&&!this._hasHighlights)return!1;const a=e===c.ShadowExcludeHighlight,o=!(r||a),n=this._rctx;let l;const h=()=>{if(i(l))return l;const r=this._glMaterials.load(n,t.slot,e);return s(r)?null:(l=r.beginSlot(t),s(l)?null:(n.bindTechnique(l,this.material.parameters,t),l))};this._appleAmdDriverHelper?.resetIndicesType();for(const i of this._dataByOrigin.values())for(const e of i.buffers){if(r&&!e.hasHighlights)continue;const l=(r?e.drawCommandsHighlight:a&&e.needsMultipleCommands()?e.drawCommandsShadowHighlightRest:e.drawCommandsDefault)||null,u=o&&e.drawCommandsOccludees||null;if(l?.length||u?.length){const r=h();if(s(r))return!1;r.program.bindDraw(new v(i.origin),t,this.material.parameters),r.ensureAttributeLocations(e.vao),n.bindVAO(e.vao),l?.length&&(r.bindPipelineState(n,t.slot,!1),l.forAll((e=>n.drawArrays(r.primitiveType,e.first,e.count)))),u?.length&&(r.bindPipelineState(n,t.slot,!0),u.forAll((e=>n.drawArrays(r.primitiveType,e.first,e.count))))}}return i(l)}get test(){return{material:this.material,glMaterials:this._glMaterials,dataByOrigin:this._dataByOrigin}}}class x{constructor(e){this.origin=e,this.changes=new Array}}function M(e,t){let r;if(!e.some((e=>!(e.numElements<t)&&(r=e,!0))))return null;const s=r.from;return r.from+=t,r.from>=r.to&&e.removeUnordered(r),s}const j=u(),I=u(),S=u(),R=new o({allocator:e=>e||new b,deallocator:null}),L=65536,G=4*L,T=16777216,W=T/4;let V=new Float32Array(L);function z(e){return V.length<e&&(V=new Float32Array(e)),V}function N(e){const t=4*e;return t<G?G:Math.max(Math.min(Math.ceil(1.5*t/G)*G,T),t)}export{H as MergedRenderer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import r from\"../../../../core/Accessor.js\";import{someMap as t}from\"../../../../core/MapUtils.js\";import{unwrap as s,isNone as i,isSome as n}from\"../../../../core/maybe.js\";import a from\"../../../../core/PooledArray.js\";import{property as o}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as d}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{a as h}from\"../../../../chunks/vec2f64.js\";import{OverlayTarget as l}from\"../../terrain/Intersector.js\";import{ChangeSet as c}from\"./ChangeSet.js\";import{newIntersectorResult as p}from\"./Intersector.js\";import{StoreResults as m,IntersectorType as g}from\"./IntersectorInterfaces.js\";import{DirtyOperation as u,DirtyState as _}from\"./ModelDirtyTypes.js\";import{splitRenderGeometryChangeSetByMaterial as y}from\"./rendererUtils.js\";import{MergedRenderer as f}from\"../materials/renderers/MergedRenderer.js\";let R=class extends r{constructor(e){super(e),this._pending=new G,this._changes=new c,this._materialRenderers=new Map,this._sortedMaterialRenderers=new a,this._geometries=new Map,this._hasHighlights=!1,this._hasWater=!1}destroy(){this._changes.prune(),this._materialRenderers.forEach((e=>e.dispose())),this._materialRenderers.clear(),this._sortedMaterialRenderers.clear(),this._geometries.clear()}get updating(){return!this._pending.empty||this._changes.updates.length>0}get rctx(){return this.rendererContext.rctx}get _materialRepository(){return this.rendererContext.materialRepository}get _localOriginFactory(){return this.rendererContext.localOriginFactory}get hasHighlights(){return this._hasHighlights}get hasWater(){return this._hasWater}get rendersOccluded(){return t(this._materialRenderers,(e=>e.rendersOccluded))}get isEmpty(){return!this.updating&&0===this._materialRenderers.size&&0===this._geometries.size}commitChanges(){if(!this.updating)return!1;this._processAddsRemoves();const e=y(this._changes);let r=!1,s=!1,i=!1;return e.forEach(((e,t)=>{let n=this._materialRenderers.get(t);if(!n&&e.adds.length>0&&(n=new f(this.rctx,this._materialRepository,t),this._materialRenderers.set(t,n),r=!0,s=!0,i=!0),!n)return;const a=s||n.hasHighlights,o=i||n.hasWater;n.modify(e),s=s||a!==n.hasHighlights,i=i||o!==n.hasWater,n.isEmpty&&(this._materialRenderers.delete(t),n.dispose(),r=!0)})),this._changes.clear(),r&&this._updateSortedMaterialRenderers(),s&&(this._hasHighlights=t(this._materialRenderers,(e=>e.hasHighlights))),i&&(this._hasWater=t(this._materialRenderers,(e=>e.hasWater))),this.notifyChange(\"updating\"),!0}addGeometries(e,r){if(0===e.length)return;const t=this._validateRenderGeometries(e);for(const i of t)this._geometries.set(i.id,i);const s=this._pending.empty;for(const i of t)this._pending.adds.add(i);s&&this.notifyChange(\"updating\"),r===u.UPDATE&&this._notifyGraphicGeometryChanged(e)}removeGeometries(e,r){const t=this._pending.empty,i=this._pending.adds;for(const n of e)i.has(n)?(this._pending.removed.add(n),i.delete(n)):this._pending.removed.has(n)||this._pending.removes.add(n),this._geometries.delete(s(n.id));t&&!this._pending.empty&&this.notifyChange(\"updating\"),r===u.UPDATE&&this._notifyGraphicGeometryChanged(e)}modifyGeometries(e,r){const t=0===this._changes.updates.length;for(const s of e){const e=this._changes.updates.pushNew();e.renderGeometry=this._validateRenderGeometry(s),e.updateType=r}switch(t&&this._changes.updates.length>0&&this.notifyChange(\"updating\"),r){case _.TRANSFORMATION:case _.GEOMETRY:return this._notifyGraphicGeometryChanged(e);case _.VISIBILITY:return this._notifyGraphicVisibilityChanged(e)}}updateAnimation(e){let r=!1;return this._sortedMaterialRenderers.forAll((t=>r=t.updateAnimation(e)||r)),r}render(e){this._sortedMaterialRenderers.forAll((r=>{r.material.shouldRender(e)&&r.render(e.output,e.bindParameters)}))}intersect(e,r,t,s,i){return this._geometries.forEach((n=>{if(s&&!s(n))return;this._intersectRenderGeometry(n,t,r,0,e,i);const a=this.rendererContext.longitudeCyclical;a&&(n.boundingSphere[0]-n.boundingSphere[3]<a.min&&this._intersectRenderGeometry(n,t,r,a.range,e,i),n.boundingSphere[0]+n.boundingSphere[3]>a.max&&this._intersectRenderGeometry(n,t,r,-a.range,e,i)),i++})),i}_updateSortedMaterialRenderers(){this._sortedMaterialRenderers.clear();let e=0;this._materialRenderers.forEach(((r,t)=>{t.insertOrder=e++,this._sortedMaterialRenderers.push(r)})),this._sortedMaterialRenderers.sort(((e,r)=>{const t=r.material.renderPriority-e.material.renderPriority;return 0!==t?t:e.material.insertOrder-r.material.insertOrder}))}_processAddsRemoves(){this._changes.adds.clear(),this._changes.removes.clear(),this._changes.adds.pushArray(Array.from(this._pending.adds)),this._changes.removes.pushArray(Array.from(this._pending.removes));for(let e=0;e<this._changes.updates.length;){const r=this._changes.updates.data[e];this._pending.has(r.renderGeometry)?this._changes.updates.removeUnorderedIndex(e):e++}this._pending.clear()}_intersectRenderGeometry(e,r,t,s,i,n){if(!e.visible)return;let a=0;s+=e.transformation[12],a=e.transformation[13],C[0]=t[0]-s,C[1]=t[1]-a,e.screenToWorldRatio=this.rendererContext.screenToWorldRatio,e.material.intersectDraped(e,null,i,C,((t,s,a)=>{v(r,a,e.material.renderPriority,n,i,e.layerUid,e.graphicUid)}),r)}_notifyGraphicGeometryChanged(e){if(i(this.drapeSource.notifyGraphicGeometryChanged))return;let r;for(const t of e){const e=t.graphicUid;n(e)&&e!==r&&(this.drapeSource.notifyGraphicGeometryChanged(e),r=e)}}_notifyGraphicVisibilityChanged(e){if(i(this.drapeSource.notifyGraphicVisibilityChanged))return;let r;for(const t of e){const e=t.graphicUid;n(e)&&e!==r&&(this.drapeSource.notifyGraphicVisibilityChanged(e),r=e)}}_validateRenderGeometries(e){for(const r of e)this._validateRenderGeometry(r);return e}_validateRenderGeometry(e){return i(e.localOrigin)&&(e.localOrigin=this._localOriginFactory.getOrigin(e.boundingSphere)),e}get test(){return{sortedMaterialRenderers:this._sortedMaterialRenderers}}};e([o()],R.prototype,\"drapeSource\",void 0),e([o()],R.prototype,\"updating\",null),e([o()],R.prototype,\"rctx\",null),e([o()],R.prototype,\"rendererContext\",void 0),e([o()],R.prototype,\"_materialRepository\",null),e([o()],R.prototype,\"_localOriginFactory\",null),e([o({readOnly:!0})],R.prototype,\"isEmpty\",null),e([o()],R.prototype,\"_materialRenderers\",void 0),e([o()],R.prototype,\"_geometries\",void 0),R=e([d(\"esri.views.3d.webgl-engine.lib.SortedRenderGeometryRenderer\")],R);class G{constructor(){this.adds=new Set,this.removes=new Set,this.removed=new Set}get empty(){return 0===this.adds.size&&0===this.removes.size&&0===this.removed.size}has(e){return this.adds.has(e)||this.removes.has(e)||this.removed.has(e)}clear(){this.adds.clear(),this.removes.clear(),this.removed.clear()}}function v(e,r,t,s,i,n,a){const o=new l(n,a,r),d=r=>{r.set(g.OVERLAY,o,e.dist,e.normal,e.transformation,t,s)};if((null==i.results.min.drapedLayerOrder||t>=i.results.min.drapedLayerOrder)&&(null==i.results.min.dist||i.results.ground.dist<=i.results.min.dist)&&d(i.results.min),i.options.store!==m.MIN&&(null==i.results.max.drapedLayerOrder||t<i.results.max.drapedLayerOrder)&&(null==i.results.max.dist||i.results.ground.dist>i.results.max.dist)&&d(i.results.max),i.options.store===m.ALL){const e=p(i.ray);d(e),i.results.all.push(e)}}const C=h();export{R as SortedRenderGeometryRenderer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{a as e}from\"../../../../chunks/TextureOnly.glsl.js\";import{ReloadableShaderModule as r}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{ShaderTechnique as i}from\"../core/shaderTechnique/ShaderTechnique.js\";import{Default3D as o}from\"./DefaultVertexAttributeLocations.js\";import{Program as t}from\"./Program.js\";import{BlendFactor as s}from\"../../../webgl/enums.js\";import{makePipelineState as a,separateBlendingParams as l,defaultColorWriteParams as n}from\"../../../webgl/renderState.js\";class m extends i{initializeProgram(e){return new t(e.rctx,m.shader.get().build(),o)}initializePipeline(){return this.configuration.hasAlpha?a({blending:l(s.SRC_ALPHA,s.ONE,s.ONE_MINUS_SRC_ALPHA,s.ONE_MINUS_SRC_ALPHA),colorWrite:n}):a({colorWrite:n})}}m.shader=new r(e,(()=>import(\"../core/shaderLibrary/util/TextureOnly.glsl.js\")));export{m as TextureTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as s}from\"../../../../chunks/tslib.es6.js\";import{ShaderTechniqueConfiguration as e,parameter as o}from\"../core/shaderTechnique/ShaderTechniqueConfiguration.js\";class r extends e{constructor(){super(...arguments),this.hasAlpha=!1}}s([o()],r.prototype,\"hasAlpha\",void 0);export{r as TextureTechniqueConfiguration};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import r from\"../../../core/Accessor.js\";import t from\"../../../core/Evented.js\";import s from\"../../../core/Handles.js\";import{someMap as i}from\"../../../core/MapUtils.js\";import{releaseMaybe as o,disposeMaybe as a,destroyMaybe as n,isSome as h,isNone as d,unwrapOr as l}from\"../../../core/maybe.js\";import c from\"../../../core/PooledArray.js\";import{watch as p,syncAndInitial as u,on as _}from\"../../../core/reactiveUtils.js\";import{someSet as m}from\"../../../core/SetUtils.js\";import{property as g}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as y}from\"../../../core/accessorSupport/decorators/subclass.js\";import{x as R,f as T}from\"../../../chunks/mat4.js\";import{s as f}from\"../../../chunks/vec3.js\";import{f as w}from\"../../../chunks/vec3f64.js\";import{ViewingMode as b}from\"../../ViewingMode.js\";import{DrapeTargetType as v,DrapeSourceType as x,DrapedRenderGroup as O}from\"../layers/interfaces.js\";import S from\"../support/debugFlags.js\";import{OverlayIndex as D,RenderTargetType as j}from\"./interfaces.js\";import{Overlay as P}from\"./Overlay.js\";import{OverlayFramebufferObject as E}from\"./OverlayFramebufferObject.js\";import{OverlayRenderTarget as A}from\"./OverlayRenderTarget.js\";import{ShaderOutput as C}from\"../webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{T as F}from\"../../../chunks/TextureOnly.glsl.js\";import{ShaderTechniqueRepository as q}from\"../webgl-engine/core/shaderTechnique/ShaderTechniqueRepository.js\";import{Camera as M}from\"../webgl-engine/lib/Camera.js\";import{GLMaterialRepository as U}from\"../webgl-engine/lib/GLMaterialRepository.js\";import{createEmptyDepthTexture as V,createQuadVAO as W}from\"../webgl-engine/lib/glUtil3D.js\";import{GridLocalOriginFactory as L}from\"../webgl-engine/lib/GridLocalOriginFactory.js\";import{RenderOccludedFlag as B}from\"../webgl-engine/lib/Material.js\";import{OverlayRenderContext as H}from\"../webgl-engine/lib/RenderContext.js\";import{RenderSlot as I}from\"../webgl-engine/lib/RenderSlot.js\";import{ShadowMap as N}from\"../webgl-engine/lib/ShadowMap.js\";import{SortedRenderGeometryRenderer as G}from\"../webgl-engine/lib/SortedRenderGeometryRenderer.js\";import{SSAOHelper as k}from\"../webgl-engine/lib/SSAOHelper.js\";import{TextureTechnique as z}from\"../webgl-engine/lib/TextureTechnique.js\";import{TextureTechniqueConfiguration as Y}from\"../webgl-engine/lib/TextureTechniqueConfiguration.js\";import{TransparencyPassType as X}from\"../webgl-engine/lib/TransparencyPassType.js\";import{UpdatePolicy as J}from\"../webgl-engine/lib/UpdatePolicy.js\";import{AmbientLight as K}from\"../webgl-engine/lighting/Lightsources.js\";import{StippleTextureRepository as Q}from\"../webgl-engine/materials/StippleTextureRepository.js\";import{TaskPriority as Z,noBudget as $}from\"../../support/Scheduler.js\";import{ClearBufferBit as ee,PrimitiveType as re,TextureType as te,PixelFormat as se,PixelType as ie,TextureSamplingMode as oe}from\"../../webgl/enums.js\";import{Texture as ae}from\"../../webgl/Texture.js\";import{vertexCount as ne}from\"../../webgl/Util.js\";let he=class extends r{get _bindParameters(){return this._renderContext.bindParameters}get rctx(){return this._rctx}get materialRepository(){return this._materialRepository}get screenToWorldRatio(){return this._screenToWorldRatio}get localOriginFactory(){return this._localOriginFactory}constructor(e){super(e),this._overlays=null,this._overlayRenderTarget=null,this._hasHighlights=!1,this._rendersOccluded=!1,this._hasWater=!1,this._handles=new s,this._renderers=new Map,this._sortedDrapeSourceRenderersDirty=!1,this._sortedRenderers=new c,this._passParameters=new F,this._rctx=null,this._materialRepository=null,this._screenToWorldRatio=1,this._localOriginFactory=null,this._camera=new M,this.worldToPCSRatio=1,this.events=new t,this.longitudeCyclical=null}initialize(){const e=this.view._stage.renderView;this._rctx=e.renderingContext;const r=e.waterTextureRepository;this._stippleTextureRepository=new Q(e.renderingContext),this._shaderTechniqueRepository=new q({rctx:this._rctx,viewingMode:b.Local,stippleTextureRepository:this._stippleTextureRepository,waterTextureRepository:r}),this._renderContext=new H(this._rctx,new N(this._rctx,this.view.state.viewingMode),new k(this.view,this._shaderTechniqueRepository,this._rctx,(()=>{}))),this._handles.add([p((()=>r.updating),(()=>this.events.emit(\"content-changed\")),u),p((()=>this.spatialReference),(e=>this._localOriginFactory=new L(e)),u),_((()=>this.view.allLayerViews),\"after-changes\",(()=>this._sortedDrapeSourceRenderersDirty=!0))]),this._materialRepository=new U(e.textureRepository,this._shaderTechniqueRepository,(e=>{(e.renderOccluded&pe)>0!==this._rendersOccluded&&this._updateRendersOccluded(),this.events.emit(\"content-changed\"),this.notifyChange(\"updating\"),this.notifyChange(\"isEmpty\")}),(()=>this.events.emit(\"content-changed\"))),this._bindParameters.slot=I.DRAPED_MATERIAL,this._bindParameters.highlightDepthTexture=V(this._rctx),this._camera.near=1,this._camera.far=1e4,this._camera.relativeElevation=null,this._bindParameters.camera=this._camera,this._bindParameters.transparencyPassType=X.NONE,this._bindParameters.newLighting.noonFactor=0,this._bindParameters.newLighting.globalFactor=0,this._bindParameters.newLighting.set([new K(w(1,1,1))]),this._handles.add(this.view.resourceController.scheduler.registerTask(Z.STAGE,this))}destroy(){this._handles.destroy(),this._renderers.forEach((e=>e.destroy())),this._renderers.clear(),this._debugTextureTechnique=o(this._debugTextureTechnique),this._passParameters.texture=a(this._passParameters.texture),this._bindParameters.highlightDepthTexture=a(this._bindParameters.highlightDepthTexture),this._shaderTechniqueRepository=n(this._shaderTechniqueRepository),this._temporaryFBO=a(this._temporaryFBO),this._quadVAO=a(this._quadVAO),this.disposeOverlays()}get updating(){return this._sortedDrapeSourceRenderersDirty||i(this._renderers,(e=>e.updating))}get hasOverlays(){return h(this._overlays)&&h(this._overlayRenderTarget)}get gpuMemoryUsage(){return h(this._overlayRenderTarget)?this._overlayRenderTarget.gpuMemoryUsage:0}createGeometryDrapeSourceRenderer(e){return this.createDrapeSourceRenderer(e,G)}createDrapeSourceRenderer(e,r,t){const s=this._renderers.get(e);h(s)&&s.destroy();const i=new r({...t,rendererContext:this,drapeSource:e});return this._renderers.set(e,i),this._sortedDrapeSourceRenderersDirty=!0,\"fullOpacity\"in e&&this._handles.add(p((()=>e.fullOpacity),(()=>this.events.emit(\"content-changed\"))),e),i}removeDrapeSourceRenderer(e){if(d(e))return;const r=this._renderers.get(e);d(r)||(this._sortedDrapeSourceRenderersDirty=!0,this._renderers.delete(e),this._handles.remove(e),r.destroy())}collectUnusedRenderTargetMemory(e){let r=!1;if(h(this._overlayRenderTarget))for(const t of this._overlayRenderTarget.renderTargets){const[s,i]=this.overlays,o=s.validTargets[t.type]||!i.validTargets[t.type];r=this._overlayRenderTarget.validateUsageForTarget(o,t,e)||r}return r}get overlays(){return l(this._overlays,[])}ensureDrapeTargets(e){h(this._overlays)&&this._overlays.forEach((r=>r.hasTargetWithoutRasterImage=m(e,(e=>e.drapeTargetType===v.WithoutRasterImage))))}ensureDrapeSources(e){h(this._overlays)&&this._overlays.forEach((r=>{r.hasDrapedFeatureSource=m(e,(e=>e.drapeSourceType===x.Features)),r.hasDrapedRasterSource=m(e,(e=>e.drapeSourceType===x.RasterImage))}))}ensureOverlays(e,r){d(this._overlays)&&(this._overlayRenderTarget=new A(this._rctx),this._overlays=[new P(D.INNER,this._overlayRenderTarget),new P(D.OUTER,this._overlayRenderTarget)]),this.ensureDrapeTargets(e),this.ensureDrapeSources(r)}disposeOverlays(){this._overlays=null,this._overlayRenderTarget=a(this._overlayRenderTarget),this.events.emit(\"textures-disposed\")}get running(){return this.updating}runTask(e){this._processDrapeSources(e,(()=>!0))}_processDrapeSources(e,r){let t=!1;for(const[s,i]of this._renderers){if(e.done)break;(s.destroyed||r(s))&&(i.commitChanges()&&(t=!0,e.madeProgress()))}this._sortedDrapeSourceRenderersDirty&&(this._sortedDrapeSourceRenderersDirty=!1,t=!0,this._updateSortedDrapeSourceRenderers()),t&&(h(this._overlays)&&0===this._renderers.size&&this.disposeOverlays(),this.notifyChange(\"updating\"),this.notifyChange(\"isEmpty\"),this.events.emit(\"content-changed\"),this._updateHasHighlights(),this._updateRendersOccluded(),this._updateHasWater())}processSyncDrapeSources(){this._processDrapeSources($,(e=>e.updatePolicy===J.SYNC))}get isEmpty(){return!S.OVERLAY_DRAW_DEBUG_TEXTURE&&!i(this._renderers,(e=>!e.isEmpty))}get hasHighlights(){return this._hasHighlights}get hasWater(){return this._hasWater}get rendersOccluded(){return this._rendersOccluded}updateAnimation(e){let r=!1;return this._renderers.forEach((t=>r=t.updateAnimation(e)||r)),r}updateDrapeSourceOrder(){this._sortedDrapeSourceRenderersDirty=!0}drawTarget(e,r,t){const s=e.canvasGeometries;if(0===s.numViews)return!1;this._screenToWorldRatio=t*e.mapUnitsPerPixel;const i=r.output;if(this.isEmpty||i===C.Highlight&&!this.hasHighlights||i===C.Normal&&!this.hasWater||!e.hasSomeSizedView())return!1;const o=r.fbo;if(!o.isValid())return!1;const a=2*e.resolution,n=e.resolution;o.resize(a,n);const d=this._rctx;if(this._camera.pixelRatio=e.pixelRatio*t,this._renderContext.output=i,this._bindParameters.screenToWorldRatio=this._screenToWorldRatio,this._bindParameters.screenToPCSRatio=this._screenToWorldRatio*this.worldToPCSRatio,this._bindParameters.slot=i===C.Normal?I.DRAPED_WATER:I.DRAPED_MATERIAL,e.applyViewport(this._rctx),o.bind(d),e.index===D.INNER&&(d.setClearColor(0,0,0,0),d.clearSafe(ee.COLOR_BUFFER_BIT)),r.type===j.Occluded&&(this._renderContext.renderOccludedMask=pe),S.OVERLAY_DRAW_DEBUG_TEXTURE&&r.type!==j.Occluded)for(let h=0;h<s.numViews;h++)this._setViewParameters(s.extents[h],e),this._drawDebugTexture(e.resolution,le[e.index]);return this._renderers.size>0&&this._sortedRenderers.forAll((({drapeSource:t,renderer:l})=>{if(r.type===j.ColorNoRasterImage&&t.drapeSourceType===x.RasterImage)return;const{fullOpacity:c}=t,p=h(c)&&c<1&&i===C.Color;p&&(this.bindTemporaryFramebuffer(this._rctx,a,n),d.clearSafe(ee.COLOR_BUFFER_BIT));for(let r=0;r<s.numViews;r++)this._setViewParameters(s.extents[r],e),l.render(this._renderContext);p&&h(this._temporaryFBO)&&(o.bind(d),this.view._stage.renderView.compositingHelper.compositeOverlay(this._renderContext.bindParameters,this._temporaryFBO.getTexture(),c,e.index))})),d.bindFramebuffer(null),o.generateMipMap(),this._renderContext.resetRenderOccludedMask(),!0}bindTemporaryFramebuffer(e,r,t){d(this._temporaryFBO)&&(this._temporaryFBO=new E(e,!1)),this._temporaryFBO.resize(r,t),this._temporaryFBO.bind(e)}async reloadShaders(){await this._shaderTechniqueRepository.reloadAll()}notifyContentChanged(){this.events.emit(\"content-changed\")}intersect(e,r,t,s){let i=0;for(const o of this._renderers.values())i=o.intersect?.(e,r,t,s,i)??i}_updateSortedDrapeSourceRenderers(){if(this._sortedRenderers.clear(),0===this._renderers.size)return;const e=this.view.map.allLayers;this._renderers.forEach(((r,t)=>{const s=e.indexOf(t.layer),i=s>=0,o=this._renderers.size*(t.renderGroup??(i?O.MapLayer:O.ViewLayer))+(i?s:0);this._sortedRenderers.push(new de(t,r,o))})),this._sortedRenderers.sort(((e,r)=>e.index-r.index))}_setViewParameters(e,r){const t=this._camera;t.viewport=[0,0,r.resolution,r.resolution],R(t.projectionMatrix,0,e[2]-e[0],0,e[3]-e[1],t.near,t.far),T(t.viewMatrix,[-e[0],-e[1],0])}_updateHasWater(){const e=i(this._renderers,(e=>e.hasWater));e!==this._hasWater&&(this._hasWater=e,this.events.emit(\"has-water\",e))}_updateHasHighlights(){const e=i(this._renderers,(e=>e.hasHighlights));e!==this._hasHighlights&&(this._hasHighlights=e,this.events.emit(\"has-highlights\",e))}_updateRendersOccluded(){const e=i(this._renderers,(e=>e.rendersOccluded));e!==this._rendersOccluded&&(this._rendersOccluded=e,this.events.emit(\"renders-occluded\",e))}_drawDebugTexture(e,r){this._ensureDebugPatternResources(e,e,r);const t=this._rctx;t.bindTechnique(this._debugTextureTechnique,this._passParameters,null),t.bindVAO(this._quadVAO),t.drawArrays(re.TRIANGLE_STRIP,0,ne(this._quadVAO,\"geometry\"))}_ensureDebugPatternResources(e,r,t){if(f(this._passParameters.color,t[0],t[1],t[2]),this._passParameters.texture)return;const s=new Uint8Array(e*r*4);let i=0;for(let a=0;a<r;a++)for(let t=0;t<e;t++){const o=Math.floor(t/10),n=Math.floor(a/10);o<2||n<2||10*o>e-20||10*n>r-20?(s[i++]=255,s[i++]=255,s[i++]=255,s[i++]=255):(s[i++]=255,s[i++]=255,s[i++]=255,s[i++]=1&o&&1&n?1&t^1&a?0:255:1&o^1&n?0:128)}this._passParameters.texture=new ae(this._rctx,{target:te.TEXTURE_2D,pixelFormat:se.RGBA,dataType:ie.UNSIGNED_BYTE,samplingMode:oe.NEAREST,width:e,height:r},s);const o=new Y;o.hasAlpha=!0,this._debugTextureTechnique=this._shaderTechniqueRepository.acquire(z,o),this._quadVAO=W(this._rctx)}get test(){return{drapedRenderers:Array.from(this._renderers.values()),getDrapeSourceRenderer:e=>this._renderers.get(e)}}};e([g()],he.prototype,\"_sortedDrapeSourceRenderersDirty\",void 0),e([g({autoDestroy:!0})],he.prototype,\"_shaderTechniqueRepository\",void 0),e([g({autoDestroy:!0})],he.prototype,\"_stippleTextureRepository\",void 0),e([g({constructOnly:!0})],he.prototype,\"view\",void 0),e([g()],he.prototype,\"worldToPCSRatio\",void 0),e([g()],he.prototype,\"spatialReference\",void 0),e([g({type:Boolean,readOnly:!0})],he.prototype,\"updating\",null),e([g()],he.prototype,\"isEmpty\",null),he=e([y(\"esri.views.3d.terrain.OverlayRenderer\")],he);class de{constructor(e,r,t){this.drapeSource=e,this.renderer=r,this.index=t}}const le=[[1,.5,.5],[.5,.5,1]],ce=-2,pe=B.OccludeAndTransparent;export{ce as DRAPED_Z,he as OverlayRenderer,pe as overlayRenderOccludedFlag};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar n;!function(n){function t(n,t){const c=n[t],o=n[t+1],r=n[t+2];return Math.sqrt(c*c+o*o+r*r)}function c(n,t){const c=n[t],o=n[t+1],r=n[t+2],u=1/Math.sqrt(c*c+o*o+r*r);n[t]*=u,n[t+1]*=u,n[t+2]*=u}function o(n,t,c){n[t]*=c,n[t+1]*=c,n[t+2]*=c}function r(n,t,c,o,r,u=t){(r=r||n)[u]=n[t]+c[o],r[u+1]=n[t+1]+c[o+1],r[u+2]=n[t+2]+c[o+2]}function u(n,t,c,o,r,u=t){(r=r||n)[u]=n[t]-c[o],r[u+1]=n[t+1]-c[o+1],r[u+2]=n[t+2]-c[o+2]}n.length=t,n.normalize=c,n.scale=o,n.add=r,n.subtract=u}(n||(n={}));export{n as Vec3Compact};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{NATIVE_ARRAY_MAX_SIZE as r}from\"../../../../core/typedArrayUtil.js\";function n(n,t=!1){return n<=r?t?new Array(n).fill(0):new Array(n):new Float32Array(n)}function t(n){return length<=r?Array.from(n):new Float32Array(n)}function a(r,n,t){return Array.isArray(r)?r.slice(n,n+t):r.subarray(n,n+t)}export{t as floatArrayFrom,a as floatSubArray,n as newFloatArray};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../../core/maybe.js\";import{b as n,f as e,n as s,g as o,a as r,e as h,s as l,c as u,m as c}from\"../../../../chunks/vec3.js\";import{f as a,c as f,b as p}from\"../../../../chunks/vec3f32.js\";import{c as O}from\"../../../../chunks/vec3f64.js\";import{create as i,fromPositionAndNormal as I,intersectRay as w}from\"../../../../geometry/support/plane.js\";import{wrap as M}from\"../../../../geometry/support/ray.js\";import{Attribute as A}from\"./Attribute.js\";import{Vec3Compact as g}from\"./BufferVectorMath.js\";import{ContentObjectType as m}from\"./ContentObjectType.js\";import{newDoubleArray as N}from\"./DoubleArray.js\";import{newFloatArray as P,floatArrayFrom as y}from\"./FloatArray.js\";import{Geometry as S}from\"./Geometry.js\";import{generateDefaultIndexArray as T}from\"./Indices.js\";import{assert as L}from\"./Util.js\";import{VertexAttribute as R}from\"./VertexAttribute.js\";const b=g,j=[[-.5,-.5,.5],[.5,-.5,.5],[.5,.5,.5],[-.5,.5,.5],[-.5,-.5,-.5],[.5,-.5,-.5],[.5,.5,-.5],[-.5,.5,-.5]],d=[0,0,1,-1,0,0,1,0,0,0,-1,0,0,1,0,0,0,-1],U=[0,0,1,0,1,1,0,1],V=[0,1,2,2,3,0,4,0,3,3,7,4,1,5,6,6,2,1,1,0,4,4,5,1,3,2,6,6,7,3,5,4,7,7,6,5],v=new Array(36);for(let gt=0;gt<6;gt++)for(let t=0;t<6;t++)v[6*gt+t]=gt;const x=new Array(36);for(let gt=0;gt<6;gt++)x[6*gt+0]=0,x[6*gt+1]=1,x[6*gt+2]=2,x[6*gt+3]=2,x[6*gt+4]=3,x[6*gt+5]=0;function C(t,n){Array.isArray(n)||(n=[n,n,n]);const e=new Array(24);for(let s=0;s<8;s++)e[3*s]=j[s][0]*n[0],e[3*s+1]=j[s][1]*n[1],e[3*s+2]=j[s][2]*n[2];return new S(t,[[R.POSITION,new A(e,3,!0)],[R.NORMAL,new A(d,3)],[R.UV0,new A(U,2)]],[[R.POSITION,V],[R.NORMAL,v],[R.UV0,x]])}const F=[[-.5,0,-.5],[.5,0,-.5],[.5,0,.5],[-.5,0,.5],[0,-.5,0],[0,.5,0]],G=[0,1,-1,1,1,0,0,1,1,-1,1,0,0,-1,-1,1,-1,0,0,-1,1,-1,-1,0],k=[5,1,0,5,2,1,5,3,2,5,0,3,4,0,1,4,1,2,4,2,3,4,3,0],E=[0,0,0,1,1,1,2,2,2,3,3,3,4,4,4,5,5,5,6,6,6,7,7,7];function q(t,n){Array.isArray(n)||(n=[n,n,n]);const e=new Array(18);for(let s=0;s<6;s++)e[3*s]=F[s][0]*n[0],e[3*s+1]=F[s][1]*n[1],e[3*s+2]=F[s][2]*n[2];return new S(t,[[R.POSITION,new A(e,3,!0)],[R.NORMAL,new A(G,3)]],[[R.POSITION,k],[R.NORMAL,E]])}const X=a(-.5,0,-.5),z=a(.5,0,-.5),B=a(0,0,.5),D=a(0,.5,0),Z=f(),H=f(),J=f(),K=f(),Q=f();n(Z,X,D),n(H,X,z),e(J,Z,H),s(J,J),n(Z,z,D),n(H,z,B),e(K,Z,H),s(K,K),n(Z,B,D),n(H,B,X),e(Q,Z,H),s(Q,Q);const W=[X,z,B,D],Y=[0,-1,0,J[0],J[1],J[2],K[0],K[1],K[2],Q[0],Q[1],Q[2]],$=[0,1,2,3,1,0,3,2,1,3,0,2],_=[0,0,0,1,1,1,2,2,2,3,3,3];function tt(t,n){Array.isArray(n)||(n=[n,n,n]);const e=new Array(12);for(let s=0;s<4;s++)e[3*s]=W[s][0]*n[0],e[3*s+1]=W[s][1]*n[1],e[3*s+2]=W[s][2]*n[2];return new S(t,[[R.POSITION,new A(e,3,!0)],[R.NORMAL,new A(Y,3)]],[[R.POSITION,$],[R.NORMAL,_]])}function nt(t,n,e,s,o={uv:!0}){const r=-Math.PI,h=2*Math.PI,l=-Math.PI/2,u=Math.PI,c=Math.max(3,Math.floor(e)),a=Math.max(2,Math.floor(s)),f=(c+1)*(a+1),p=P(3*f),O=P(3*f),i=P(2*f),I=[];let w=0;for(let A=0;A<=a;A++){const t=[],e=A/a,s=l+e*u,o=Math.cos(s);for(let l=0;l<=c;l++){const u=l/c,a=r+u*h,f=Math.cos(a)*o,I=Math.sin(s),M=-Math.sin(a)*o;p[3*w]=f*n,p[3*w+1]=I*n,p[3*w+2]=M*n,O[3*w]=f,O[3*w+1]=I,O[3*w+2]=M,i[2*w]=u,i[2*w+1]=e,t.push(w),++w}I.push(t)}const M=new Array;for(let A=0;A<a;A++)for(let t=0;t<c;t++){const n=I[A][t],e=I[A][t+1],s=I[A+1][t+1],o=I[A+1][t];0===A?(M.push(n),M.push(s),M.push(o)):A===a-1?(M.push(n),M.push(e),M.push(s)):(M.push(n),M.push(e),M.push(s),M.push(s),M.push(o),M.push(n))}const g=[[R.POSITION,M],[R.NORMAL,M]],m=[[R.POSITION,new A(p,3,!0)],[R.NORMAL,new A(O,3,!0)]];return o.uv&&(m.push([R.UV0,new A(i,2,!0)]),g.push([R.UV0,M])),o.offset&&(g[0][0]=R.OFFSET,m[0][0]=R.OFFSET,g.push([R.POSITION,new Array(M.length).fill(0)]),m.push([R.POSITION,new A(Float64Array.from(o.offset),3,!0)])),new S(t,m,g)}function et(t,n,e,s){const{vertexAttributes:o,indices:r}=st(n,e,s);return new S(t,o,r)}function st(t,n,e){const s=t;let o,r;if(e)o=[0,-1,0,1,0,0,0,0,1,-1,0,0,0,0,-1,0,1,0],r=[0,1,2,0,2,3,0,3,4,0,4,1,1,5,2,2,5,3,3,5,4,4,5,1];else{const t=s*(1+Math.sqrt(5))/2;o=[-s,t,0,s,t,0,-s,-t,0,s,-t,0,0,-s,t,0,s,t,0,-s,-t,0,s,-t,t,0,-s,t,0,s,-t,0,-s,-t,0,s],r=[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1]}for(let a=0;a<o.length;a+=3)b.scale(o,a,t/b.length(o,a));let h={};function l(n,e){n>e&&([n,e]=[e,n]);const s=n.toString()+\".\"+e.toString();if(h[s])return h[s];let r=o.length;return o.length+=3,b.add(o,3*n,o,3*e,o,r),b.scale(o,r,t/b.length(o,r)),r/=3,h[s]=r,r}for(let a=0;a<n;a++){const t=r.length,n=new Array(4*t);for(let e=0;e<t;e+=3){const t=r[e],s=r[e+1],o=r[e+2],h=l(t,s),u=l(s,o),c=l(o,t),a=4*e;n[a]=t,n[a+1]=h,n[a+2]=c,n[a+3]=s,n[a+4]=u,n[a+5]=h,n[a+6]=o,n[a+7]=c,n[a+8]=u,n[a+9]=h,n[a+10]=u,n[a+11]=c}r=n,h={}}const u=y(o);for(let a=0;a<u.length;a+=3)b.normalize(u,a);const c=[[R.POSITION,r],[R.NORMAL,r]];return{vertexAttributes:[[R.POSITION,new A(y(o),3,!0)],[R.NORMAL,new A(u,3,!0)]],indices:c}}function ot(n,e,s,o,r,h,l,u,c=null){const a=s?[s[0],s[1],s[2]]:[0,0,0],f=e?[e[0],e[1],e[2]]:[0,0,1];l=l||[0,0];const p=o?[255*o[0],255*o[1],255*o[2],o.length>3?255*o[3]:255]:[255,255,255,255],O=t(r)&&2===r.length?r:[1,1],i=[[R.POSITION,new A(a,3,!0)],[R.NORMAL,new A(f,3,!0)],[R.UV0,new A(l,l.length)],[R.COLOR,new A(p,4,!0)],[R.SIZE,new A(O,2)]];if(null!=h){const t=[h[0],h[1],h[2],h[3]];i.push([R.AUXPOS1,new A(t,4)])}if(null!=u){const t=[u[0],u[1],u[2],u[3]];i.push([R.AUXPOS2,new A(t,4)])}return new S(n,i,null,null,m.Point,c)}const rt=[[-1,-1,0],[1,-1,0],[1,1,0],[-1,1,0]];function ht(t,n=rt){const e=new Array(12);for(let a=0;a<4;a++)for(let t=0;t<3;t++)e[3*a+t]=n[a][t];const s=[0,1,2,2,3,0],o=[0,0,1],r=[0,0,0,0,0,0],h=[0,0,1,0,1,1,0,1],l=[255,255,255,255],u=[[R.POSITION,s],[R.NORMAL,r],[R.UV0,s],[R.COLOR,r]],c=[[R.POSITION,new A(e,3,!0)],[R.NORMAL,new A(o,3,!0)],[R.UV0,new A(h,2,!0)],[R.COLOR,new A(l,4,!0)]];return new S(t,c,u)}function lt(t,n,e,s,o,r=!0,h=!0){let l=0;const u=e,c=n;let f=a(0,l,0),p=a(0,l+c,0),O=a(0,-1,0),i=a(0,1,0);o&&(l=c,p=a(0,0,0),f=a(0,l,0),O=a(0,1,0),i=a(0,-1,0));const I=[p,f],w=[O,i],M=s+2,g=Math.sqrt(c*c+u*u);if(o)for(let A=s-1;A>=0;A--){const t=A*(2*Math.PI/s),n=a(Math.cos(t)*u,l,Math.sin(t)*u);I.push(n);const e=a(c*Math.cos(t)/g,-u/g,c*Math.sin(t)/g);w.push(e)}else for(let A=0;A<s;A++){const t=A*(2*Math.PI/s),n=a(Math.cos(t)*u,l,Math.sin(t)*u);I.push(n);const e=a(c*Math.cos(t)/g,u/g,c*Math.sin(t)/g);w.push(e)}const m=new Array,N=new Array;if(r){for(let t=3;t<I.length;t++)m.push(1),m.push(t-1),m.push(t),N.push(0),N.push(0),N.push(0);m.push(I.length-1),m.push(2),m.push(1),N.push(0),N.push(0),N.push(0)}if(h){for(let t=3;t<I.length;t++)m.push(t),m.push(t-1),m.push(0),N.push(t),N.push(t-1),N.push(1);m.push(0),m.push(2),m.push(I.length-1),N.push(1),N.push(2),N.push(w.length-1)}const y=P(3*M);for(let a=0;a<M;a++)y[3*a]=I[a][0],y[3*a+1]=I[a][1],y[3*a+2]=I[a][2];const T=P(3*M);for(let a=0;a<M;a++)T[3*a]=w[a][0],T[3*a+1]=w[a][1],T[3*a+2]=w[a][2];const L=[[R.POSITION,m],[R.NORMAL,N]],b=[[R.POSITION,new A(y,3,!0)],[R.NORMAL,new A(T,3,!0)]];return new S(t,b,L)}function ut(t,n,u,c,O,i,I){const w=O?p(O):a(1,0,0),M=i?p(i):a(0,0,0);I=I??!0;const g=f();s(g,w);const m=f();o(m,g,Math.abs(n));const N=f();o(N,m,-.5),r(N,N,M);const y=a(0,1,0);Math.abs(1-h(g,y))<.2&&l(y,0,0,1);const T=f();e(T,g,y),s(T,T),e(y,T,g);const L=2*c+(I?2:0),b=c+(I?2:0),j=P(3*L),d=P(3*b),U=P(2*L),V=new Array(3*c*(I?4:2)),v=new Array(3*c*(I?4:2));I&&(j[3*(L-2)+0]=N[0],j[3*(L-2)+1]=N[1],j[3*(L-2)+2]=N[2],U[2*(L-2)]=0,U[2*(L-2)+1]=0,j[3*(L-1)+0]=j[3*(L-2)+0]+m[0],j[3*(L-1)+1]=j[3*(L-2)+1]+m[1],j[3*(L-1)+2]=j[3*(L-2)+2]+m[2],U[2*(L-1)]=1,U[2*(L-1)+1]=1,d[3*(b-2)+0]=-g[0],d[3*(b-2)+1]=-g[1],d[3*(b-2)+2]=-g[2],d[3*(b-1)+0]=g[0],d[3*(b-1)+1]=g[1],d[3*(b-1)+2]=g[2]);const x=(t,n,e)=>{V[t]=n,v[t]=e};let C=0;const F=f(),G=f();for(let e=0;e<c;e++){const t=e*(2*Math.PI/c);o(F,y,Math.sin(t)),o(G,T,Math.cos(t)),r(F,F,G),d[3*e+0]=F[0],d[3*e+1]=F[1],d[3*e+2]=F[2],o(F,F,u),r(F,F,N),j[3*e+0]=F[0],j[3*e+1]=F[1],j[3*e+2]=F[2],U[2*e+0]=e/c,U[2*e+1]=0,j[3*(e+c)+0]=j[3*e+0]+m[0],j[3*(e+c)+1]=j[3*e+1]+m[1],j[3*(e+c)+2]=j[3*e+2]+m[2],U[2*(e+c)+0]=e/c,U[2*e+1]=1;const n=(e+1)%c;x(C++,e,e),x(C++,e+c,e),x(C++,n,n),x(C++,n,n),x(C++,e+c,e),x(C++,n+c,n)}if(I){for(let t=0;t<c;t++){const n=(t+1)%c;x(C++,L-2,b-2),x(C++,t,b-2),x(C++,n,b-2)}for(let t=0;t<c;t++){const n=(t+1)%c;x(C++,t+c,b-1),x(C++,L-1,b-1),x(C++,n+c,b-1)}}const k=[[R.POSITION,V],[R.NORMAL,v],[R.UV0,V]],E=[[R.POSITION,new A(j,3,!0)],[R.NORMAL,new A(d,3,!0)],[R.UV0,new A(U,2,!0)]];return new S(t,E,k)}function ct(t,n,e,s,o,r){s=s||10,o=null==o||o,L(n.length>1);const h=[[0,0,0]],l=[],u=[];for(let c=0;c<s;c++){l.push([0,-c-1,-(c+1)%s-1]);const t=c/s*2*Math.PI;u.push([Math.cos(t)*e,Math.sin(t)*e])}return at(t,u,n,h,l,o,r)}function at(t,h,c,p,g,m,N=a(0,0,0)){const y=h.length,T=P(c.length*y*3+(6*p.length||0)),L=P(c.length*y*3+(p?6:0)),b=new Array,j=new Array;let d=0,U=0;const V=f(),v=f(),x=f(),C=f(),F=f(),G=f(),k=f(),E=O(),q=f(),X=f(),z=f(),B=f(),D=f(),Z=i();l(q,0,1,0),n(v,c[1],c[0]),s(v,v),m?(r(E,c[0],N),s(x,E)):l(x,0,0,1),wt(v,x,q,q,F,x,Mt),u(C,x),u(B,F);for(let n=0;n<p.length;n++)o(G,F,p[n][0]),o(E,x,p[n][2]),r(G,G,E),r(G,G,c[0]),T[d++]=G[0],T[d++]=G[1],T[d++]=G[2];L[U++]=-v[0],L[U++]=-v[1],L[U++]=-v[2];for(let n=0;n<g.length;n++)b.push(g[n][0]>0?g[n][0]:-g[n][0]-1+p.length),b.push(g[n][1]>0?g[n][1]:-g[n][1]-1+p.length),b.push(g[n][2]>0?g[n][2]:-g[n][2]-1+p.length),j.push(0),j.push(0),j.push(0);let H=p.length;const J=p.length-1;for(let l=0;l<c.length;l++){let t=!1;if(l>0){u(V,v),l<c.length-1?(n(v,c[l+1],c[l]),s(v,v)):t=!0,r(X,V,v),s(X,X),r(z,c[l-1],C),I(c[l],X,Z);w(Z,M(z,V),E)?(n(E,E,c[l]),s(x,E),e(F,X,x),s(F,F)):wt(X,C,B,q,F,x,Mt),u(C,x),u(B,F)}m&&(r(E,c[l],N),s(D,E));for(let n=0;n<y;n++)if(o(G,F,h[n][0]),o(E,x,h[n][1]),r(G,G,E),s(k,G),L[U++]=k[0],L[U++]=k[1],L[U++]=k[2],r(G,G,c[l]),T[d++]=G[0],T[d++]=G[1],T[d++]=G[2],!t){const t=(n+1)%y;b.push(H+n),b.push(H+y+n),b.push(H+t),b.push(H+t),b.push(H+y+n),b.push(H+y+t);for(let n=0;n<6;n++){const t=b.length-6;j.push(b[t+n]-J)}}H+=y}const K=c[c.length-1];for(let n=0;n<p.length;n++)o(G,F,p[n][0]),o(E,x,p[n][1]),r(G,G,E),r(G,G,K),T[d++]=G[0],T[d++]=G[1],T[d++]=G[2];const Q=U/3;L[U++]=v[0],L[U++]=v[1],L[U++]=v[2];const W=H-y;for(let n=0;n<g.length;n++)b.push(g[n][0]>=0?H+g[n][0]:-g[n][0]-1+W),b.push(g[n][2]>=0?H+g[n][2]:-g[n][2]-1+W),b.push(g[n][1]>=0?H+g[n][1]:-g[n][1]-1+W),j.push(Q),j.push(Q),j.push(Q);const Y=[[R.POSITION,b],[R.NORMAL,j]],$=[[R.POSITION,new A(T,3,!0)],[R.NORMAL,new A(L,3,!0)]];return new S(t,$,Y)}function ft(t,n,e,s){L(n.length>1,\"createPolylineGeometry(): polyline needs at least 2 points\"),L(3===n[0].length,\"createPolylineGeometry(): malformed vertex\"),L(null==e||e.length===n.length,\"createPolylineGeometry: need same number of points and normals\"),L(null==e||3===e[0].length,\"createPolylineGeometry(): malformed normal\");const o=N(3*n.length),r=new Array(2*(n.length-1));let h=0,l=0;for(let a=0;a<n.length;a++){for(let t=0;t<3;t++)o[h++]=n[a][t];a>0&&(r[l++]=a-1,r[l++]=a)}const u=[],c=[];if(u.push([R.POSITION,r]),c.push([R.POSITION,new A(o,3,!0)]),e){const t=P(3*e.length);let s=0;for(let o=0;o<n.length;o++)for(let n=0;n<3;n++)t[s++]=e[o][n];u.push([R.NORMAL,r]),c.push([R.NORMAL,new A(t,3,!0)])}return s&&(c.push([R.COLOR,new A(s,4)]),u.push([R.COLOR,T(s.length/4)])),new S(t,c,u,null,m.Line)}function pt(t,n,e,s,o,r=0){const h=new Array(18),l=[[-e,r,o/2],[s,r,o/2],[0,n+r,o/2],[-e,r,-o/2],[s,r,-o/2],[0,n+r,-o/2]],u=[0,1,2,3,0,2,2,5,3,1,4,5,5,2,1,1,0,3,3,4,1,4,3,5];for(let c=0;c<6;c++)h[3*c]=l[c][0],h[3*c+1]=l[c][1],h[3*c+2]=l[c][2];return new S(t,[[R.POSITION,new A(h,3,!0)]],[[R.POSITION,u]])}function Ot(t,n){const e=t.getMutableAttribute(R.POSITION).data;for(let s=0;s<e.length;s+=3){const t=e[s],o=e[s+1],r=e[s+2];l(At,t,o,r),c(At,At,n),e[s]=At[0],e[s+1]=At[1],e[s+2]=At[2]}}function it(t,n=t){const e=t.vertexAttributes,s=e.get(R.POSITION).data,o=e.get(R.NORMAL).data;if(o){const t=n.getMutableAttribute(R.NORMAL).data;for(let n=0;n<o.length;n+=3){const e=o[n+1];t[n+1]=-o[n+2],t[n+2]=e}}if(s){const t=n.getMutableAttribute(R.POSITION).data;for(let n=0;n<s.length;n+=3){const e=s[n+1];t[n+1]=-s[n+2],t[n+2]=e}}}function It(t,n,o,r,l){return!(Math.abs(h(n,t))>l)&&(e(o,t,n),s(o,o),e(r,o,t),s(r,r),!0)}function wt(t,n,e,s,o,r,h){return It(t,n,o,r,h)||It(t,e,o,r,h)||It(t,s,o,r,h)}const Mt=.99619469809,At=f();export{it as cgToGIS,C as createBoxGeometry,lt as createConeGeometry,ut as createCylinderGeometry,q as createDiamondGeometry,pt as createExtrudedTriangle,at as createPathExtrusionGeometry,ot as createPointGeometry,st as createPolySphereData,et as createPolySphereGeometry,ft as createPolylineGeometry,nt as createSphereGeometry,ht as createSquareGeometry,tt as createTetrahedronGeometry,ct as createTubeGeometry,wt as makeOrthoBasisDirUpFallback,Ot as transformInPlace};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as r,isSome as t}from\"../../../../core/maybe.js\";import{generateUID as s}from\"../../../../core/uid.js\";import{c as i}from\"../../../../chunks/mat4.js\";import{c as e}from\"../../../../chunks/mat4f64.js\";import{m as o}from\"../../../../chunks/vec3.js\";import{c as a}from\"../../../../chunks/vec4f64.js\";import{maxScale as n}from\"../../support/mathUtils.js\";class h{constructor(r,t={}){this.geometry=r,this.boundingSphere=a(),this.screenToWorldRatio=1,this._transformation=e(),this._shaderTransformationDirty=!0,this.id=s(),this.layerUid=t.layerUid,this.graphicUid=t.graphicUid,this.boundingInfo=t.boundingInfo,this.shaderTransformer=t.shaderTransformer,this.castShadow=!!t.castShadow&&t.castShadow}get transformation(){return this._transformation}updateTransformation(r){r(this._transformation),this._shaderTransformationDirty=!0,this.computeBoundingSphere(this._transformation,this.boundingSphere)}shaderTransformationChanged(){this._shaderTransformationDirty=!0}computeBoundingSphere(t,s,i=n(t)){r(this.boundingInfo)||(o(s,this.boundingInfo.center,t),s[3]=this.boundingInfo.radius*i)}get hasShaderTransformation(){return t(this.shaderTransformer)}get material(){return this.geometry.material}get type(){return this.geometry.type}get shaderTransformation(){return r(this.shaderTransformer)?this.transformation:(this._shaderTransformationDirty&&(this._shaderTransformation||(this._shaderTransformation=e()),i(this._shaderTransformation,this.shaderTransformer(this.transformation)),this._shaderTransformationDirty=!1),this._shaderTransformation)}get indices(){return this.geometry.indices}get vertexAttributes(){return this.geometry.vertexAttributes}get highlights(){return this.geometry.highlights}get occludees(){return this.geometry.occludees}get visible(){return this.geometry.visible}set visible(r){this.geometry.visible=r}}class m extends h{}export{h as RenderGeometry,m as ValidatedRenderGeometry};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ShaderOutput as e}from\"../core/shaderLibrary/ShaderOutput.js\";import{ReloadableShaderModule as t}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{ShaderTechnique as r}from\"../core/shaderTechnique/ShaderTechnique.js\";import{Default3D as i}from\"../lib/DefaultVertexAttributeLocations.js\";import{blendingDefault as n,oitBlending as o,oitDepthTest as l,getOITPolygonOffset as s}from\"../lib/OrderIndependentTransparency.js\";import{Program as a}from\"../lib/Program.js\";import{stencilWriteMaskOn as c,stencilToolMaskBaseParams as p,stencilBaseAllZerosParams as u}from\"../lib/StencilUtils.js\";import{TransparencyPassType as h}from\"../lib/TransparencyPassType.js\";import{C as f}from\"../../../../chunks/ColorMaterial.glsl.js\";import{ContextType as m}from\"../../../webgl/context-util.js\";import{makePipelineState as d,cullingParams as g,defaultDepthWriteParams as b,defaultColorWriteParams as P}from\"../../../webgl/renderState.js\";class j extends r{initializeConfiguration(e,t){t.hasWebGL2Context=e.rctx.type===m.WEBGL2}initializeProgram(e){return new a(e.rctx,j.shader.get().build(this.configuration),i)}_createPipeline(t,r){const i=this.configuration,a=t===h.NONE,f=t===h.FrontFace;return d({blending:i.output!==e.Color&&i.output!==e.Alpha||!i.transparent?null:a?n:o(t),culling:g(i.cullFace),depthTest:{func:l(t)},depthWrite:(a||f)&&i.writeDepth?b:null,colorWrite:P,stencilWrite:i.hasOccludees?c:null,stencilTest:i.hasOccludees?r?p:u:null,polygonOffset:a||f?i.polygonOffset?y:null:s(i.enableOffset)})}initializePipeline(){return this._occludeePipelineState=this._createPipeline(this.configuration.transparencyPassType,!0),this._createPipeline(this.configuration.transparencyPassType,!1)}getPipelineState(e,t){return t?this._occludeePipelineState:super.getPipelineState(e,t)}}j.shader=new t(f,(()=>import(\"./ColorMaterial.glsl.js\")));const y={factor:1,units:1};export{j as ColorMaterialTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../../chunks/tslib.es6.js\";import{ShaderOutput as t}from\"../core/shaderLibrary/ShaderOutput.js\";import{parameter as e}from\"../core/shaderTechnique/ShaderTechniqueConfiguration.js\";import{CullFaceOptions as r}from\"../lib/basicInterfaces.js\";import{TransparencyPassType as s}from\"../lib/TransparencyPassType.js\";import{DefaultTechniqueConfiguration as i}from\"../materials/DefaultTechniqueConfiguration.js\";class p extends i{constructor(){super(...arguments),this.output=t.Color,this.cullFace=r.None,this.hasSlicePlane=!1,this.hasVertexColors=!1,this.transparent=!1,this.polygonOffset=!1,this.enableOffset=!0,this.writeDepth=!0,this.hasOccludees=!1,this.transparencyPassType=s.NONE,this.hasMultipassTerrain=!1,this.cullAboveGround=!1,this.objectAndLayerIdColorInstanced=!1}}o([e({count:t.COUNT})],p.prototype,\"output\",void 0),o([e({count:r.COUNT})],p.prototype,\"cullFace\",void 0),o([e()],p.prototype,\"hasSlicePlane\",void 0),o([e()],p.prototype,\"hasVertexColors\",void 0),o([e()],p.prototype,\"transparent\",void 0),o([e()],p.prototype,\"polygonOffset\",void 0),o([e()],p.prototype,\"enableOffset\",void 0),o([e()],p.prototype,\"writeDepth\",void 0),o([e()],p.prototype,\"hasOccludees\",void 0),o([e({count:s.COUNT})],p.prototype,\"transparencyPassType\",void 0),o([e()],p.prototype,\"hasMultipassTerrain\",void 0),o([e()],p.prototype,\"cullAboveGround\",void 0),o([e()],p.prototype,\"objectAndLayerIdColorInstanced\",void 0);export{p as ColorMaterialTechniqueConfiguration};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../core/has.js\";import{Z as e}from\"../../../../chunks/vec4f64.js\";import{ShaderOutput as t}from\"../core/shaderLibrary/ShaderOutput.js\";import{CullFaceOptions as r}from\"../lib/basicInterfaces.js\";import s from\"../lib/GLMaterial.js\";import{MaterialParameters as i}from\"../lib/Material.js\";import{OITPolygonOffsetLimit as a}from\"../lib/OrderIndependentTransparency.js\";import{RenderSlot as o}from\"../lib/RenderSlot.js\";import{DefaultBufferWriter as n}from\"./DefaultBufferWriter.js\";import{PositionColorLayoutObjectAndLayerId as h,PositionColorLayout as l}from\"./DefaultLayouts.js\";import{TriangleMaterial as u}from\"./TriangleMaterial.js\";import{ColorMaterialTechnique as c}from\"../shaders/ColorMaterialTechnique.js\";import{ColorMaterialTechniqueConfiguration as p}from\"../shaders/ColorMaterialTechniqueConfiguration.js\";class f extends u{constructor(e){super(e,new d),this.supportsEdges=!0,this._configuration=new p}getConfiguration(e,t){return this._configuration.output=e,this._configuration.cullFace=this.parameters.cullFace,this._configuration.hasVertexColors=this.parameters.hasVertexColors,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.transparent=this.parameters.transparent,this._configuration.polygonOffset=this.parameters.polygonOffset,this._configuration.writeDepth=this.parameters.writeDepth,this._configuration.hasOccludees=this.parameters.hasOccludees,this._configuration.transparencyPassType=t.transparencyPassType,this._configuration.enableOffset=t.camera.relativeElevation<a,this._configuration.hasMultipassTerrain=t.multipassTerrain.enabled,this._configuration.cullAboveGround=t.multipassTerrain.cullAboveGround,this._configuration}requiresSlot(e,r){if(r===t.Color||r===t.Alpha||r===t.Highlight||r===t.Depth&&this.parameters.writeLinearDepth||r===t.ObjectAndLayerIdColor){if(e===o.DRAPED_MATERIAL)return!0;if(r===t.Highlight)return e===o.OPAQUE_MATERIAL;return e===(this.parameters.transparent?this.parameters.writeDepth?o.TRANSPARENT_MATERIAL:o.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL:o.OPAQUE_MATERIAL)}return!1}createGLMaterial(e){return new m(e)}createBufferWriter(){return new n(has(\"enable-feature:objectAndLayerId-rendering\")?h:l)}}class m extends s{_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}beginSlot(e){return this._output!==t.Color&&this._output!==t.Alpha||this._updateOccludeeState(e),this.ensureTechnique(c,e)}}class d extends i{constructor(){super(...arguments),this.color=e,this.transparent=!1,this.writeDepth=!0,this.writeLinearDepth=!1,this.hasVertexColors=!1,this.polygonOffset=!1,this.hasSlicePlane=!1,this.cullFace=r.None,this.hasOccludees=!1}}export{f as ColorMaterial,d as Parameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as o,isSome as t}from\"../../../../core/maybe.js\";const d={dash:[4,3],dot:[1,3],\"long-dash\":[8,3],\"short-dash\":[4,1],\"short-dot\":[1,1]},s={dash:d.dash,\"dash-dot\":[...d.dash,...d.dot],dot:d.dot,\"long-dash\":d[\"long-dash\"],\"long-dash-dot\":[...d[\"long-dash\"],...d.dot],\"long-dash-dot-dot\":[...d[\"long-dash\"],...d.dot,...d.dot],none:null,\"short-dash\":d[\"short-dash\"],\"short-dash-dot\":[...d[\"short-dash\"],...d[\"short-dot\"]],\"short-dash-dot-dot\":[...d[\"short-dash\"],...d[\"short-dot\"],...d[\"short-dot\"]],\"short-dot\":d[\"short-dot\"],solid:null},h=8;function n(t,d=2){return o(t)?t:{pattern:t.slice(),pixelRatio:d}}function r(o,t=2){return{pattern:[o,o],pixelRatio:t}}function a(o){return t(o)&&\"style\"===o.type?l(o.style):null}function l(o){return t(o)?n(s[o],h):null}export{n as createStipplePattern,r as createStipplePatternSimple,a as getStipplePatternForLinePattern,l as getStipplePatternForPatternStyle};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,unwrapOr as e,isNone as n}from\"../../../../core/maybe.js\";import{p as o}from\"../../../../chunks/vec2.js\";import{s as r}from\"../../../../chunks/vec3.js\";import{c as i}from\"../../../../chunks/vec3f64.js\";import{O as a}from\"../../../../chunks/vec4f64.js\";import{getReferenceEllipsoid as s}from\"../../../../geometry/ellipsoidUtils.js\";import{lonLatToWebMercatorComparable as u}from\"../../../../geometry/projection.js\";import{ViewingMode as l}from\"../../../ViewingMode.js\";import{Attribute as c}from\"../../webgl-engine/lib/Attribute.js\";import{ContentObjectType as p}from\"../../webgl-engine/lib/ContentObjectType.js\";import{newDoubleArray as f}from\"../../webgl-engine/lib/DoubleArray.js\";import{newFloatArray as m}from\"../../webgl-engine/lib/FloatArray.js\";import{Geometry as h}from\"../../webgl-engine/lib/Geometry.js\";import{VertexAttribute as T}from\"../../webgl-engine/lib/VertexAttribute.js\";function b(t,e,n=null){const o=[],r=[],i=e.mapPositions;g(e,r,o);const a=r[0][1].data,s=o[0][1].length,u=new Array(s).fill(0);return E(e,r,o,u),O(e,r,o,u),A(e,r,o,u),I(e,r,o,u),R(e,r,o,u),w(e,r,o,u),j(e,r,o,a),new h(t,r,o,i,p.Line,n)}function g(t,e,n){const{attributeData:{position:o},removeDuplicateStartEnd:r}=t,i=D(o)&&r,a=o.length/3-(i?1:0),s=new Array(2*(a-1)),u=i?o.slice(0,o.length-3):o;let l=0;for(let c=0;c<a-1;c++)s[l++]=c,s[l++]=c+1;e.push([T.POSITION,new c(u,3,i)]),n.push([T.POSITION,s])}function E(n,o,r,i){if(t(n.attributeData.colorFeature))return;const s=n.attributeData.color;o.push([T.COLOR,new c(e(s,a),4)]),r.push([T.COLOR,i])}function A(e,n,o,r){if(!t(e.attributeData.normal))return;const i=e.attributeData.normal;n.push([T.NORMAL,new c(i,3)]),o.push([T.NORMAL,r])}function I(t,e,o,r){const i=t.attributeData.colorFeature;n(i)||(e.push([T.COLORFEATUREATTRIBUTE,new c([i],1,!0)]),o.push([T.COLOR,r]))}function O(n,o,r,i){if(t(n.attributeData.sizeFeature))return;const a=n.attributeData.size;o.push([T.SIZE,new c([e(a,1)],1,!0)]),r.push([T.SIZE,i])}function R(t,e,o,r){const i=t.attributeData.sizeFeature;n(i)||(e.push([T.SIZEFEATUREATTRIBUTE,new c([i],1,!0)]),o.push([T.SIZEFEATUREATTRIBUTE,r]))}function w(t,e,o,r){const i=t.attributeData.opacityFeature;n(i)||(e.push([T.OPACITYFEATUREATTRIBUTE,new c([i],1,!0)]),o.push([T.OPACITYFEATUREATTRIBUTE,r]))}function j(t,e,i,a){if(n(t.overlayInfo)||t.overlayInfo.renderCoordsHelper.viewingMode!==l.Global||!t.overlayInfo.spatialReference.isGeographic)return;const p=f(a.length),h=s(t.overlayInfo.spatialReference);for(let n=0;n<p.length;n+=3)u(a,n,p,n,h);const b=a.length/3,g=m(b+1);let E=y,A=S,I=0,O=0;r(E,p[O++],p[O++],p[O++]),g[0]=0;for(let n=1;n<b+1;++n)n===b&&(O=0),r(A,p[O++],p[O++],p[O++]),I+=o(E,A),g[n]=I,[E,A]=[A,E];e.push([T.DISTANCETOSTART,new c(g,1,!0)]),i.push([T.DISTANCETOSTART,i[0][1]])}function D(t){const e=t.length;return t[0]===t[e-3]&&t[1]===t[e-2]&&t[2]===t[e-1]}const y=i(),S=i();function v(t,e){if(n(t)||0===t.length)return[];const o=[];return t.forEach((t=>{const n=t.length,r=f(3*n);t.forEach(((t,e)=>{r[3*e+0]=t[0],r[3*e+1]=t[1],r[3*e+2]=t[2]}));const i={attributeData:{position:r,normal:e},removeDuplicateStartEnd:!1};o.push(i)})),o}export{b as createGeometry,v as lineStripsToParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as o}from\"../../../../core/maybe.js\";import{projectBuffer as e}from\"../../../../geometry/projection.js\";import{CounterClockwiseMode as n,pathsToTriangulationInfo as t}from\"../../../../geometry/support/triangulationUtils.js\";import{applyPerVertexElevationAlignment as r}from\"../../layers/graphics/elevationAlignmentUtils.js\";import{DRAPED_Z as i}from\"../../terrain/OverlayRenderer.js\";import{newDoubleArray as s,doubleSubArray as l}from\"../../webgl-engine/lib/DoubleArray.js\";function p(o,e,i,l){const p=\"polygon\"===o.type?n.CCW_IS_HOLE:n.NONE,c=\"polygon\"===o.type?o.rings:o.paths,{position:m,outlines:u}=t(c,!!o.hasZ,p),g=s(m.length),f=r(m,o.spatialReference,0,g,0,m,0,m.length/3,e,i,l),y=null!=f;return{lines:y?a(u,m,g):[],projectionSuccess:y,sampledElevation:f}}function c(o,r){const s=\"polygon\"===o.type?n.CCW_IS_HOLE:n.NONE,l=\"polygon\"===o.type?o.rings:o.paths,{position:p,outlines:c}=t(l,!1,s),m=e(p,o.spatialReference,0,p,r,0,p.length/3);for(let e=2;e<p.length;e+=3)p[e]=i;return{lines:m?a(c,p):[],projectionSuccess:m}}function a(e,n,t=null){const r=new Array;for(const{index:i,count:s}of e){if(s<=1)continue;const e=3*i,p=3*s;r.push({position:l(n,3*i,3*s),mapPositions:o(t)?l(t,e,p):void 0})}return r}export{p as geometryToRenderInfo,c as geometryToRenderInfoDraped};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ShaderOutput as e}from\"../core/shaderLibrary/ShaderOutput.js\";import{ReloadableShaderModule as t}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{ShaderTechnique as i}from\"../core/shaderTechnique/ShaderTechnique.js\";import{Default3D as r}from\"../lib/DefaultVertexAttributeLocations.js\";import{Program as l}from\"../lib/Program.js\";import{depthCompareLess as n,stencilWriteMaskOn as o,stencilToolMaskBaseParams as s,stencilBaseAllZerosParams as a}from\"../lib/StencilUtils.js\";import{N as u}from\"../../../../chunks/NativeLine.glsl.js\";import{ContextType as p}from\"../../../webgl/context-util.js\";import{BlendFactor as c,PrimitiveType as h}from\"../../../webgl/enums.js\";import{separateBlendingParams as m,makePipelineState as d,defaultColorWriteParams as g,defaultDepthWriteParams as b}from\"../../../webgl/renderState.js\";class f extends i{get _stippleEnabled(){return this.configuration.stippleEnabled&&this.configuration.output!==e.Highlight}initializeConfiguration(e,t){t.hasWebGL2Context=e.rctx.type===p.WEBGL2}initializeProgram(e){return new l(e.rctx,f.shader.get().build(this.configuration),r)}initializePipeline(){const t=this.configuration,i=m(c.SRC_ALPHA,c.ONE,c.ONE_MINUS_SRC_ALPHA,c.ONE_MINUS_SRC_ALPHA),r=(e,i=null,r=null)=>d({blending:i,depthTest:n,depthWrite:r,colorWrite:g,stencilWrite:t.hasOccludees?o:null,stencilTest:t.hasOccludees?e?s:a:null});return t.output===e.Color?(this._occludeePipelineState=r(!0,t.transparent||this._stippleEnabled?i:null,b),r(!1,t.transparent||this._stippleEnabled?i:null,b)):r(!1)}get primitiveType(){return h.LINES}getPipelineState(e,t){return t?this._occludeePipelineState:super.getPipelineState(e,t)}}f.shader=new t(u,(()=>import(\"./NativeLine.glsl.js\")));export{f as NativeLineTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../../chunks/tslib.es6.js\";import{ShaderOutput as e}from\"../core/shaderLibrary/ShaderOutput.js\";import{parameter as o}from\"../core/shaderTechnique/ShaderTechniqueConfiguration.js\";import{DefaultTechniqueConfiguration as p}from\"../materials/DefaultTechniqueConfiguration.js\";class r extends p{constructor(){super(...arguments),this.output=e.Color,this.hasSlicePlane=!1,this.hasVertexColors=!1,this.transparent=!1,this.draped=!1,this.stippleEnabled=!1,this.stippleOffColorEnabled=!1,this.stipplePreferContinuous=!0,this.hasOccludees=!1}}t([o({count:e.COUNT})],r.prototype,\"output\",void 0),t([o()],r.prototype,\"hasSlicePlane\",void 0),t([o()],r.prototype,\"hasVertexColors\",void 0),t([o()],r.prototype,\"transparent\",void 0),t([o()],r.prototype,\"draped\",void 0),t([o()],r.prototype,\"stippleEnabled\",void 0),t([o()],r.prototype,\"stippleOffColorEnabled\",void 0),t([o()],r.prototype,\"stipplePreferContinuous\",void 0),t([o()],r.prototype,\"hasOccludees\",void 0),t([o({constValue:!1})],r.prototype,\"stippleRequiresClamp\",void 0),t([o({constValue:!1})],r.prototype,\"stippleScaleWithLineWidth\",void 0),t([o({constValue:!1})],r.prototype,\"stippleRequiresStretchMeasure\",void 0);export{r as NativeLineTechniqueConfiguration};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/Logger.js\";import{clamp as t}from\"../../../../core/mathUtils.js\";import{isSome as r,isNone as s}from\"../../../../core/maybe.js\";import{createRenderScreenPointArray3 as i}from\"../../../../core/screenUtils.js\";import{c as o}from\"../../../../chunks/vec2.js\";import{s as a,b as n,e as c,g as l,a as u,c as p,l as f,i as m,m as h}from\"../../../../chunks/vec3.js\";import{c as d}from\"../../../../chunks/vec3f64.js\";import{O as g}from\"../../../../chunks/vec4f64.js\";import{PlaneIndex as A}from\"../../../../geometry/support/frustum.js\";import{create as _,distance2 as O,fromPoints as S,closestLineSegmentPoint as T}from\"../../../../geometry/support/lineSegment.js\";import{create as P,fromPoints as j,signedDistance as b,normal as x}from\"../../../../geometry/support/plane.js\";import{BufferViewVec3f as y,BufferViewVec2f as v}from\"../../../../geometry/support/buffer/BufferView.js\";import{ShaderOutput as R}from\"../core/shaderLibrary/ShaderOutput.js\";import E from\"../lib/GLMaterial.js\";import{Material as N,MaterialParameters as I}from\"../lib/Material.js\";import{RenderSlot as L}from\"../lib/RenderSlot.js\";import{isTranslationMatrix as w}from\"../lib/Util.js\";import{VertexAttribute as C}from\"../lib/VertexAttribute.js\";import{DefaultBufferWriter as U}from\"./DefaultBufferWriter.js\";import{PositionColorLayout as V,PositionLayout as B}from\"./DefaultLayouts.js\";import{writeDefaultAttributes as M}from\"./internal/bufferWriterUtils.js\";import{NativeLineTechnique as D}from\"../shaders/NativeLineTechnique.js\";import{NativeLineTechniqueConfiguration as X}from\"../shaders/NativeLineTechniqueConfiguration.js\";var k;!function(e){e[e.START=0]=\"START\",e[e.END=1]=\"END\"}(k||(k={}));class q extends N{constructor(e){super(e,new G),this._configuration=new X}getConfiguration(e,t){this._configuration.output=e,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.hasVertexColors=this.parameters.hasVertexColors,this._configuration.transparent=this.parameters.color[3]<1||this.parameters.width<1,this._configuration.draped=t.slot===L.DRAPED_MATERIAL;const s=r(this.parameters.stipplePattern);return this._configuration.stippleEnabled=s,this._configuration.stippleOffColorEnabled=s&&r(this.parameters.stippleOffColor),this._configuration.hasOccludees=this.parameters.hasOccludees,this._configuration.stipplePreferContinuous=this.parameters.stipplePreferContinuous,this._configuration}intersect(t,r,s,i,h,d){if(!s.options.selectionMode||!t.visible)return;if(!w(r))return void e.getLogger(\"esri.views.3d.webgl-engine.materials.NativeLineMaterial\").error(\"intersection assumes a translation-only matrix\");const g=t.vertexAttributes.get(C.POSITION).data,_=s.camera,P=re;o(P,s.point);const y=2;a(se[0],P[0]-y,P[1]+y,0),a(se[1],P[0]+y,P[1]+y,0),a(se[2],P[0]+y,P[1]-y,0),a(se[3],P[0]-y,P[1]-y,0);for(let e=0;e<4;e++)if(!_.unprojectFromRenderScreen(se[e],ie[e]))return;j(_.eye,ie[0],ie[1],oe),j(_.eye,ie[1],ie[2],ae),j(_.eye,ie[2],ie[3],ne),j(_.eye,ie[3],ie[0],ce);let v=Number.MAX_VALUE,R=0;for(let e=0;e<g.length-5;e+=3){if(H[0]=g[e]+r[12],H[1]=g[e+1]+r[13],H[2]=g[e+2]+r[14],Q[0]=g[e+3]+r[12],Q[1]=g[e+4]+r[13],Q[2]=g[e+5]+r[14],b(oe,H)<0&&b(oe,Q)<0||b(ae,H)<0&&b(ae,Q)<0||b(ne,H)<0&&b(ne,Q)<0||b(ce,H)<0&&b(ce,Q)<0)continue;if(_.projectToRenderScreen(H,J),_.projectToRenderScreen(Q,K),J[2]<0&&K[2]>0){n(Z,H,Q);const e=_.frustum,t=-b(e[A.NEAR],H)/c(Z,x(e[A.NEAR]));l(Z,Z,t),u(H,H,Z),_.projectToRenderScreen(H,J)}else if(J[2]>0&&K[2]<0){n(Z,Q,H);const e=_.frustum,t=-b(e[A.NEAR],Q)/c(Z,x(e[A.NEAR]));l(Z,Z,t),u(Q,Q,Z),_.projectToRenderScreen(Q,K)}else if(J[2]<0&&K[2]<0)continue;J[2]=0,K[2]=0;const t=O(S(J,K,ee),P);t<v&&(v=t,p(Y,H),p($,Q),R=e/3)}const E=s.rayBegin,N=s.rayEnd;if(v<y*y){let e=Number.MAX_VALUE;if(T(S(Y,$,ee),S(E,N,te),z)){n(z,z,E);const t=f(z);l(z,z,1/t),e=t/m(E,N)}d(e,z,R,!1)}}intersectDraped(e,r,s,i,o,a){if(!s.options.selectionMode)return;const n=e.vertexAttributes.get(C.POSITION).data,c=e.vertexAttributes.get(C.SIZE),l=c?c.data[0]:0,u=i[0],p=i[1],f=((l+1)/2+4)*e.screenToWorldRatio;let m=Number.MAX_VALUE,h=0;for(let d=0;d<n.length-5;d+=3){const e=n[d],r=n[d+1],s=u-e,i=p-r,o=n[d+3]-e,a=n[d+4]-r,c=t((o*s+a*i)/(o*o+a*a),0,1),l=o*c-s,f=a*c-i,g=l*l+f*f;g<m&&(m=g,h=d/3)}m<f*f&&o(a.dist,a.normal,h,!1)}requiresSlot(e,t){return!(t!==R.Color&&t!==R.Highlight&&t!==R.ObjectAndLayerIdColor||e!==L.OPAQUE_MATERIAL&&e!==L.DRAPED_MATERIAL)}createGLMaterial(e){return new W(e)}createBufferWriter(){const e=this.parameters.hasVertexColors?V:B;return s(this.parameters.stipplePattern)?new U(e):new F(e.clone().vec3f(C.AUXPOS1).vec2f(C.UV0))}}class W extends E{constructor(){super(...arguments),this._stipplePattern=null}dispose(){super.dispose(),this._stippleTextureRepository.release(this._stipplePattern),this._stipplePattern=null}_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}beginSlot(e){this._output===R.Color&&this._updateOccludeeState(e);const t=this._material.parameters.stipplePattern;return this._stipplePattern!==t&&(this._material.setParameters(this._stippleTextureRepository.swap(this._stipplePattern,t)),this._stipplePattern=t),this.ensureTechnique(D,e)}}class F{constructor(e){this.vertexBufferLayout=e}allocate(e){return this.vertexBufferLayout.createBuffer(e)}elementCount(e){return e.indices.get(C.POSITION).length}write(e,t,r,s,i){M(r,this.vertexBufferLayout,e,t,s,i),this._writeAuxpos1(e,r,s,i),this._writeUV0(e,r,s,i)}_writeAuxpos1(e,t,r,s){const i=r.getField(C.AUXPOS1,y),o=t.indices.get(C.POSITION),a=t.vertexAttributes.get(C.POSITION).data,n=e,c=i.typedBufferStride,l=i.typedBuffer;s*=c;for(let u=0;u<o.length-1;u+=2)for(const e of[1,0]){const t=3*o[u+e],r=a[t],i=a[t+1],p=a[t+2],f=n[0]*r+n[4]*i+n[8]*p+n[12],m=n[1]*r+n[5]*i+n[9]*p+n[13],h=n[2]*r+n[6]*i+n[10]*p+n[14];l[s]=f,l[s+1]=m,l[s+2]=h,s+=c}}_writeUV0(e,t,r,s){const i=r.getField(C.UV0,v),o=t.indices.get(C.POSITION),n=t.vertexAttributes.get(C.POSITION).data,c=t.vertexAttributes.get(C.DISTANCETOSTART)?.data,l=i.typedBufferStride,u=i.typedBuffer;let f=0;u[s*=l]=k.START,u[s+1]=f,s+=l;const d=3*o[0],g=a(H,n[d],n[d+1],n[d+2]);e&&h(g,g,e);const A=Q,_=o.length-1;let O=1;const S=c?(e,t,r)=>f=c[r]:(e,t,r)=>f+=m(e,t);for(let m=1;m<_;m+=2){const t=3*o[m];a(A,n[t],n[t+1],n[t+2]),e&&h(A,A,e),S(g,A,O++);for(let e=0;e<2;++e)u[s]=1-e,u[s+1]=f,s+=l;p(g,A)}const T=3*o[_];a(A,n[T],n[T+1],n[T+2]),e&&h(A,A,e),S(g,A,O),u[s]=k.END,u[s+1]=f}}class G extends I{constructor(){super(...arguments),this.color=g,this.hasVertexColors=!1,this.hasSlicePlane=!1,this.width=1,this.stipplePreferContinuous=!0,this.hasOccludees=!1,this.stippleTexture=null}}const H=d(),Q=d(),Z=d(),z=d(),J=i(),K=i(),Y=d(),$=d(),ee=_(),te=_(),re=d(),se=[i(),i(),i(),i()],ie=[d(),d(),d(),d()],oe=P(),ae=P(),ne=P(),ce=P();export{q as NativeLineMaterial,G as Parameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction n(n){return n instanceof Float32Array&&n.length>=16}function r(n){return Array.isArray(n)&&n.length>=16}function t(t){return n(t)||r(t)}export{t as isMat4,n as isMat4f32,r as isMat4f64};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(){this.factor=new s,this.factorAlignment=new s}}class s{constructor(){this.scale=0,this.factor=0,this.minPixelSize=0,this.paddingPixels=0}}export{s as ScaleFactor,t as ScaleInfo};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ViewingMode as e}from\"../../../ViewingMode.js\";import{ShaderOutput as t}from\"../core/shaderLibrary/ShaderOutput.js\";import{ReloadableShaderModule as i}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{ShaderTechnique as r}from\"../core/shaderTechnique/ShaderTechnique.js\";import{Default3D as o}from\"../lib/DefaultVertexAttributeLocations.js\";import{oitBlending as n}from\"../lib/OrderIndependentTransparency.js\";import{Program as s}from\"../lib/Program.js\";import{TransparencyPassType as a}from\"../lib/TransparencyPassType.js\";import{H as l}from\"../../../../chunks/HUDMaterial.glsl.js\";import{ContextType as p}from\"../../../webgl/context-util.js\";import{BlendFactor as u,CompareFunction as c,PrimitiveType as h}from\"../../../webgl/enums.js\";import{simpleBlendingParams as m,defaultDepthWriteParams as f,makePipelineState as g,defaultColorWriteParams as d}from\"../../../webgl/renderState.js\";class b extends r{initializeConfiguration(t,i){i.hasWebGL2Context=t.rctx.type===p.WEBGL2,i.spherical=t.viewingMode===e.Global}initializeProgram(e){return new s(e.rctx,b.shader.get().build(this.configuration),o)}_setPipelineState(e){const i=this.configuration,r=e===a.NONE,o=e===a.FrontFace,s=this.configuration.hasPolygonOffset?j:null,l=(r||o)&&i.output!==t.Highlight&&(i.depthEnabled||i.occlusionPass)?f:null;return g({blending:i.output===t.Color||i.output===t.Alpha||i.output===t.Highlight?r?P:n(e):null,depthTest:{func:c.LEQUAL},depthWrite:l,colorWrite:d,polygonOffset:s})}initializePipeline(){return this._setPipelineState(this.configuration.transparencyPassType)}get primitiveType(){return this.configuration.occlusionPass?h.POINTS:h.TRIANGLES}}b.shader=new i(l,(()=>import(\"./HUDMaterial.glsl.js\")));const j={factor:0,units:-4},P=m(u.ONE,u.ONE_MINUS_SRC_ALPHA);export{b as HUDMaterialTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import{ShaderOutput as o}from\"../core/shaderLibrary/ShaderOutput.js\";import{HUDSpace as t}from\"../core/shaderLibrary/hud/HUD.glsl.js\";import{parameter as i}from\"../core/shaderTechnique/ShaderTechniqueConfiguration.js\";import{TransparencyPassType as s}from\"../lib/TransparencyPassType.js\";import{DefaultTechniqueConfiguration as r}from\"../materials/DefaultTechniqueConfiguration.js\";class a extends r{constructor(){super(...arguments),this.output=o.Color,this.screenCenterOffsetUnitsEnabled=t.World,this.transparencyPassType=s.NONE,this.spherical=!1,this.occlusionTestEnabled=!0,this.signedDistanceFieldEnabled=!1,this.vvSize=!1,this.vvColor=!1,this.hasVerticalOffset=!1,this.hasScreenSizePerspective=!1,this.debugDrawLabelBorder=!1,this.binaryHighlightOcclusionEnabled=!0,this.hasSlicePlane=!1,this.hasPolygonOffset=!1,this.depthEnabled=!0,this.pixelSnappingEnabled=!0,this.isDraped=!1,this.hasMultipassGeometry=!1,this.hasMultipassTerrain=!1,this.cullAboveGround=!1,this.occlusionPass=!1,this.objectAndLayerIdColorInstanced=!1}}e([i({count:o.COUNT})],a.prototype,\"output\",void 0),e([i({count:t.COUNT})],a.prototype,\"screenCenterOffsetUnitsEnabled\",void 0),e([i({count:s.COUNT})],a.prototype,\"transparencyPassType\",void 0),e([i()],a.prototype,\"spherical\",void 0),e([i()],a.prototype,\"occlusionTestEnabled\",void 0),e([i()],a.prototype,\"signedDistanceFieldEnabled\",void 0),e([i()],a.prototype,\"vvSize\",void 0),e([i()],a.prototype,\"vvColor\",void 0),e([i()],a.prototype,\"hasVerticalOffset\",void 0),e([i()],a.prototype,\"hasScreenSizePerspective\",void 0),e([i()],a.prototype,\"debugDrawLabelBorder\",void 0),e([i()],a.prototype,\"binaryHighlightOcclusionEnabled\",void 0),e([i()],a.prototype,\"hasSlicePlane\",void 0),e([i()],a.prototype,\"hasPolygonOffset\",void 0),e([i()],a.prototype,\"depthEnabled\",void 0),e([i()],a.prototype,\"pixelSnappingEnabled\",void 0),e([i()],a.prototype,\"isDraped\",void 0),e([i()],a.prototype,\"hasMultipassGeometry\",void 0),e([i()],a.prototype,\"hasMultipassTerrain\",void 0),e([i()],a.prototype,\"cullAboveGround\",void 0),e([i()],a.prototype,\"occlusionPass\",void 0),e([i()],a.prototype,\"objectAndLayerIdColorInstanced\",void 0),e([i({constValue:!0})],a.prototype,\"hasSliceInVertexProgram\",void 0),e([i({constValue:!1})],a.prototype,\"hasVvInstancing\",void 0);export{a as HUDMaterialTechniqueConfiguration};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../core/has.js\";import{clamp as e}from\"../../../../core/mathUtils.js\";import{isSome as t,unwrapOr as r}from\"../../../../core/maybe.js\";import{f as s}from\"../../../../chunks/mat3.js\";import{c as i}from\"../../../../chunks/mat3f64.js\";import{a}from\"../../../../chunks/mat4.js\";import{c as n}from\"../../../../chunks/mat4f64.js\";import{c as o}from\"../../../../chunks/vec2.js\";import{a as c,f as l}from\"../../../../chunks/vec2f64.js\";import{s as f,m as u,n as h,b as p,g as m,c as d,l as g,i as O,t as S,e as v,a as A}from\"../../../../chunks/vec3.js\";import{c as b,f as P}from\"../../../../chunks/vec3f64.js\";import{c as x,f as _}from\"../../../../chunks/vec4f64.js\";import{isMat4 as y}from\"../../../../core/libs/gl-matrix-2/types/mat4.js\";import{create as I}from\"../../../../geometry/support/aaBoundingRect.js\";import{BufferViewVec4u8 as T}from\"../../../../geometry/support/buffer/BufferView.js\";import C from\"../../support/debugFlags.js\";import{newLayout as j}from\"../../support/buffer/InterleavedLayout.js\";import{ShaderOutput as z}from\"../core/shaderLibrary/ShaderOutput.js\";import{HUDSpace as L}from\"../core/shaderLibrary/hud/HUD.glsl.js\";import{GLTextureMaterial as M,GLTextureMaterialBindParameters as E}from\"../lib/GLTextureMaterial.js\";import{Material as U,RenderOccludedFlag as R}from\"../lib/Material.js\";import{RenderSlot as D}from\"../lib/RenderSlot.js\";import{applyScaleFactor as w,applyPrecomputedScaleFactor as V,precomputeScaleFactor as N}from\"../lib/screenSizePerspectiveUtils.js\";import{assert as B}from\"../lib/Util.js\";import{VertexAttribute as X}from\"../lib/VertexAttribute.js\";import{ScaleInfo as H}from\"./ScaleInfo.js\";import{writePosition as F,writeNormal as G,writeColor as k,writeBufferVec4 as W,writeBufferVec4Zeros as q,writeObjectAndLayerIdColor as Z}from\"./internal/bufferWriterUtils.js\";import{verticalOffsetAtDistance as J}from\"./internal/MaterialUtil.js\";import{c as Y}from\"../../../../chunks/HUDMaterial.glsl.js\";import{HUDMaterialTechnique as K}from\"../shaders/HUDMaterialTechnique.js\";import{HUDMaterialTechniqueConfiguration as Q}from\"../shaders/HUDMaterialTechniqueConfiguration.js\";class $ extends U{constructor(e){super(e,new be),this._configuration=new Q}getConfiguration(e,t){return this._configuration.output=e,this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.hasVerticalOffset=!!this.parameters.verticalOffset,this._configuration.hasScreenSizePerspective=!!this.parameters.screenSizePerspective,this._configuration.screenCenterOffsetUnitsEnabled=\"screen\"===this.parameters.centerOffsetUnits?L.Screen:L.World,this._configuration.hasPolygonOffset=this.parameters.polygonOffset,this._configuration.isDraped=this.parameters.isDraped,this._configuration.occlusionTestEnabled=this.parameters.occlusionTest,this._configuration.pixelSnappingEnabled=this.parameters.pixelSnappingEnabled,this._configuration.signedDistanceFieldEnabled=this.parameters.textureIsSignedDistanceField,this._configuration.vvSize=!!this.parameters.vvSizeEnabled,this._configuration.vvColor=!!this.parameters.vvColorEnabled,this._configuration.occlusionPass=t.slot===D.OCCLUSION_PIXELS&&this.parameters.occlusionTest&&(e===z.Color||e===z.Alpha),e===z.Color&&(this._configuration.debugDrawLabelBorder=!!C.LABELS_SHOW_BORDER),e===z.Highlight&&(this._configuration.binaryHighlightOcclusionEnabled=this.parameters.binaryHighlightOcclusion),this._configuration.depthEnabled=this.parameters.depthEnabled,this._configuration.transparencyPassType=t.transparencyPassType,this._configuration.hasMultipassGeometry=t.multipassGeometry.enabled,this._configuration.hasMultipassTerrain=t.multipassTerrain.enabled,this._configuration.cullAboveGround=t.multipassTerrain.cullAboveGround,this._configuration}intersect(e,r,i,n,o,c){if(!i.options.selectionMode||!i.options.hud||!e.visible)return;const l=this.parameters;let S=1,v=1;if(s(ue,r),t(e.shaderTransformer)){const t=e.shaderTransformer(ge);S=t[0],v=t[5],re(ue)}const A=e.vertexAttributes.get(X.POSITION),P=e.vertexAttributes.get(X.SIZE),x=e.vertexAttributes.get(X.NORMAL),_=e.vertexAttributes.get(X.AUXPOS1);B(A.size>=3);const y=i.point,I=i.camera,T=Y(l);S*=I.pixelRatio,v*=I.pixelRatio;const C=\"screen\"===this.parameters.centerOffsetUnits;for(let t=0;t<A.data.length/A.size;t++){const e=t*A.size;f(ne,A.data[e],A.data[e+1],A.data[e+2]),u(ne,ne,r);const s=t*P.size;ve[0]=P.data[s]*S,ve[1]=P.data[s+1]*v,u(ne,ne,I.viewMatrix);const n=t*_.size;if(f(me,_.data[n+0],_.data[n+1],_.data[n+2]),!C&&(ne[0]+=me[0],ne[1]+=me[1],0!==me[2])){const e=me[2];h(me,ne),p(ne,ne,m(me,me,e))}const o=t*x.size;if(f(oe,x.data[o],x.data[o+1],x.data[o+2]),this._normalAndViewAngle(oe,ue,I,de),this._applyVerticalOffsetTransformationView(ne,de,I,ie),I.applyProjection(ne,ce),ce[0]>-1){ce[0]=Math.floor(ce[0]),ce[1]=Math.floor(ce[1]),C&&(me[0]||me[1])&&(ce[0]+=me[0],0!==me[1]&&(ce[1]+=w(me[1],ie.factorAlignment)),I.unapplyProjection(ce,ne)),ce[0]+=this.parameters.screenOffset[0],ce[1]+=this.parameters.screenOffset[1],V(ve,ie.factor,ve);const e=Oe*I.pixelRatio;let t=0;if(l.textureIsSignedDistanceField&&(t=l.outlineSize*I.pixelRatio/2),y&&se(y,ce[0],ce[1],ve,e,t,l,T)){const e=i.ray;if(u(fe,ne,a(pe,I.viewMatrix)),ce[0]=y[0],ce[1]=y[1],I.unprojectFromRenderScreen(ce,ne)){const t=b();d(t,e.direction);const r=1/g(t);m(t,t,r);c(O(e.origin,ne)*r,t,-1,!0,1,fe)}}}}}intersectDraped(e,r,s,i,a,n){const o=e.vertexAttributes.get(X.POSITION),c=e.vertexAttributes.get(X.SIZE),l=this.parameters,f=Y(l);let u=1,h=1;if(t(e.shaderTransformer)){const t=e.shaderTransformer(ge);u=t[0],h=t[5]}u*=e.screenToWorldRatio,h*=e.screenToWorldRatio;const p=Se*e.screenToWorldRatio;for(let t=0;t<o.data.length/o.size;t++){const r=t*o.size,s=o.data[r],m=o.data[r+1],d=t*c.size;ve[0]=c.data[d]*u,ve[1]=c.data[d+1]*h;let g=0;l.textureIsSignedDistanceField&&(g=l.outlineSize*e.screenToWorldRatio/2),se(i,s,m,ve,p,g,l,f)&&a(n.dist,n.normal,-1,!1)}}createBufferWriter(){return new _e(this)}_normalAndViewAngle(e,t,r,i){return y(t)&&(t=s(he,t)),S(i.normal,e,t),u(i.normal,i.normal,r.viewInverseTransposeMatrix),i.cosAngle=v(le,Ae),i}_updateScaleInfo(e,r,s){const i=this.parameters;t(i.screenSizePerspective)?N(s,r,i.screenSizePerspective,e.factor):(e.factor.scale=1,e.factor.factor=0,e.factor.minPixelSize=0,e.factor.paddingPixels=0),t(i.screenSizePerspectiveAlignment)?N(s,r,i.screenSizePerspectiveAlignment,e.factorAlignment):(e.factorAlignment.factor=e.factor.factor,e.factorAlignment.scale=e.factor.scale,e.factorAlignment.minPixelSize=e.factor.minPixelSize,e.factorAlignment.paddingPixels=e.factor.paddingPixels)}applyShaderOffsetsView(e,t,r,s,i,a,n){const o=this._normalAndViewAngle(t,r,i,de);return this._applyVerticalGroundOffsetView(e,o,i,n),this._applyVerticalOffsetTransformationView(n,o,i,a),this._applyPolygonOffsetView(n,o,s[3],i,n),this._applyCenterOffsetView(n,s,n),n}applyShaderOffsetsNDC(e,r,s,i,a){return this._applyCenterOffsetNDC(e,r,s,i),t(a)&&d(a,i),this._applyPolygonOffsetNDC(i,r,s,i),i}_applyPolygonOffsetView(t,r,s,i,a){const n=i.aboveGround?1:-1;let o=Math.sign(s);0===o&&(o=n);const c=n*o;if(this.parameters.shaderPolygonOffset<=0)return d(a,t);const l=e(Math.abs(r.cosAngle),.01,1),f=1-Math.sqrt(1-l*l)/l/i.viewport[2];return m(a,t,c>0?f:1/f),a}_applyVerticalGroundOffsetView(e,t,r,s){const i=g(e),a=r.aboveGround?1:-1,n=.5*r.computeRenderPixelSizeAtDist(i),o=m(ne,t.normal,a*n);return A(s,e,o),s}_applyVerticalOffsetTransformationView(e,t,s,i){const a=this.parameters;if(!a.verticalOffset||!a.verticalOffset.screenLength){if(a.screenSizePerspective||a.screenSizePerspectiveAlignment){const r=g(e);this._updateScaleInfo(i,r,t.cosAngle)}else i.factor.scale=1,i.factorAlignment.scale=1;return e}const n=g(e),o=r(a.screenSizePerspectiveAlignment,a.screenSizePerspective),c=J(s,n,a.verticalOffset,t.cosAngle,o);return this._updateScaleInfo(i,n,t.cosAngle),m(t.normal,t.normal,c),A(e,e,t.normal)}_applyCenterOffsetView(e,t,r){const s=\"screen\"!==this.parameters.centerOffsetUnits;return r!==e&&d(r,e),s&&(r[0]+=t[0],r[1]+=t[1],t[2]&&(h(oe,r),A(r,r,m(oe,oe,t[2])))),r}_applyCenterOffsetNDC(e,t,r,s){const i=\"screen\"!==this.parameters.centerOffsetUnits;return s!==e&&d(s,e),i||(s[0]+=t[0]/r.fullWidth*2,s[1]+=t[1]/r.fullHeight*2),s}_applyPolygonOffsetNDC(e,t,r,s){const i=this.parameters.shaderPolygonOffset;if(e!==s&&d(s,e),i){const e=r.aboveGround?1:-1,a=e*Math.sign(t[3]);s[2]-=(a||e)*i}return s}requiresSlot(e,t){if(t===z.Color||t===z.Alpha||t===z.Highlight||t===z.ObjectAndLayerIdColor){if(e===D.DRAPED_MATERIAL)return!0;const{drawInSecondSlot:t,occlusionTest:r}=this.parameters;return e===(t?D.LABEL_MATERIAL:D.HUD_MATERIAL)||r&&e===D.OCCLUSION_PIXELS}return!1}createGLMaterial(e){return new ee(e)}calculateRelativeScreenBounds(e,t,r=I()){return te(this.parameters,e,t,r),r[2]=r[0]+e[0],r[3]=r[1]+e[1],r}}class ee extends M{constructor(e){super({...e,...e.material.parameters})}selectProgram(e){return this.ensureTechnique(K,e)}beginSlot(e){return this.updateTexture(this._material.parameters.textureId),this._material.setParameters(this.textureBindParameters),this.selectProgram(e)}}function te(e,t,r,s=ae){return o(s,e.anchorPosition),s[0]*=-t[0],s[1]*=-t[1],s[0]+=e.screenOffset[0]*r,s[1]+=e.screenOffset[1]*r,s}function re(e){const t=e[0],r=e[1],s=e[2],i=e[3],a=e[4],n=e[5],o=e[6],c=e[7],l=e[8],f=1/Math.sqrt(t*t+r*r+s*s),u=1/Math.sqrt(i*i+a*a+n*n),h=1/Math.sqrt(o*o+c*c+l*l);return e[0]=t*f,e[1]=r*f,e[2]=s*f,e[3]=i*u,e[4]=a*u,e[5]=n*u,e[6]=o*h,e[7]=c*h,e[8]=l*h,e}function se(e,r,s,i,a,n,o,c){let l=r-a-(c[0]>0?i[0]*c[0]:0),f=l+i[0]+2*a,u=s-a-(c[1]>0?i[1]*c[1]:0),h=u+i[1]+2*a;const p=o.distanceFieldBoundingBox;return o.textureIsSignedDistanceField&&t(p)&&(l+=i[0]*p[0],u+=i[1]*p[1],f-=i[0]*(1-p[2]),h-=i[1]*(1-p[3]),l-=n,f+=n,u-=n,h+=n),e[0]>l&&e[0]<f&&e[1]>u&&e[1]<h}const ie=new H,ae=c(),ne=b(),oe=b(),ce=x(),le=b(),fe=b(),ue=i(),he=i(),pe=n(),me=b(),de={normal:le,cosAngle:0},ge=n(),Oe=1,Se=2,ve=[0,0],Ae=P(0,0,1);class be extends E{constructor(){super(...arguments),this.renderOccluded=R.Occlude,this.color=_(1,1,1,1),this.texCoordScale=[1,1],this.polygonOffset=!1,this.anchorPosition=l(.5,.5),this.screenOffset=[0,0],this.shaderPolygonOffset=1e-5,this.textureIsSignedDistanceField=!1,this.outlineColor=_(1,1,1,1),this.outlineSize=0,this.vvSizeEnabled=!1,this.vvSizeMinSize=[1,1,1],this.vvSizeMaxSize=[100,100,100],this.vvSizeOffset=[0,0,0],this.vvSizeFactor=[1,1,1],this.vvColorEnabled=!1,this.vvColorValues=[0,0,0,0,0,0,0,0],this.vvColorColors=[1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0],this.hasSlicePlane=!1,this.pixelSnappingEnabled=!0,this.occlusionTest=!0,this.binaryHighlightOcclusion=!0,this.centerOffsetUnits=\"world\",this.drawInSecondSlot=!1,this.depthEnabled=!0,this.isDraped=!1}}const Pe=j().vec3f(X.POSITION).vec3f(X.NORMAL).vec2f(X.UV0).vec4u8(X.COLOR).vec2f(X.SIZE).vec4f(X.AUXPOS1).vec4f(X.AUXPOS2),xe=Pe.clone().vec4u8(X.OBJECTANDLAYERIDCOLOR);class _e{constructor(e){this._material=e,this.vertexBufferLayout=has(\"enable-feature:objectAndLayerId-rendering\")?xe:Pe}allocate(e){return this.vertexBufferLayout.createBuffer(e)}elementCount(e){return 6*e.indices.get(X.POSITION).length}write(e,r,s,i,a){F(s.indices.get(X.POSITION),s.vertexAttributes.get(X.POSITION).data,e,i.position,a,6),G(s.indices.get(X.NORMAL),s.vertexAttributes.get(X.NORMAL).data,r,i.normal,a,6);const n=s.vertexAttributes.get(X.UV0).data;let o,c,l,f;if(null==n||n.length<4){const e=this._material.parameters;o=0,c=0,l=e.texCoordScale[0],f=e.texCoordScale[1]}else o=n[0],c=n[1],l=n[2],f=n[3];l=Math.min(1.99999,l+1),f=Math.min(1.99999,f+1);let u=s.indices.get(X.POSITION).length,h=a;const p=i.uv0;for(let t=0;t<u;++t)p.set(h,0,o),p.set(h,1,c),h+=1,p.set(h,0,l),p.set(h,1,c),h+=1,p.set(h,0,l),p.set(h,1,f),h+=1,p.set(h,0,l),p.set(h,1,f),h+=1,p.set(h,0,o),p.set(h,1,f),h+=1,p.set(h,0,o),p.set(h,1,c),h+=1;k(s.indices.get(X.COLOR),s.vertexAttributes.get(X.COLOR).data,4,i.color,a,6);const m=s.indices.get(X.SIZE),d=s.vertexAttributes.get(X.SIZE).data;u=m.length;const g=i.size;h=a;for(let t=0;t<u;++t){const e=d[2*m[t]],r=d[2*m[t]+1];for(let t=0;t<6;++t)g.set(h,0,e),g.set(h,1,r),h+=1}if(s.indices.get(X.AUXPOS1)&&s.vertexAttributes.get(X.AUXPOS1)?W(s.indices.get(X.AUXPOS1),s.vertexAttributes.get(X.AUXPOS1).data,i.auxpos1,a,6):q(i.auxpos1,a,6*u),s.indices.get(X.AUXPOS2)&&s.vertexAttributes.get(X.AUXPOS2)?W(s.indices.get(X.AUXPOS2),s.vertexAttributes.get(X.AUXPOS2).data,i.auxpos2,a,6):q(i.auxpos2,a,6*u),t(s.objectAndLayerIdColor)){if(s.indices.get(X.POSITION)){const e=s.indices.get(X.POSITION).length,t=i.getField(X.OBJECTANDLAYERIDCOLOR,T);Z(s.objectAndLayerIdColor,t,e,a,6)}}}}export{$ as HUDMaterial,be as Parameters};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAIA;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,OAAK,CAAC,IAAE;AAAM,EAAEA,OAAIA,KAAE,CAAC,EAAE;;;ACArE,IAAIC;AAAJ,IAAMC;AAAN,IAAQC;AAAE,CAAC,SAASF,KAAE;AAAC,EAAAA,IAAEA,IAAE,cAAY,CAAC,IAAE,eAAcA,IAAEA,IAAE,WAAS,CAAC,IAAE;AAAU,EAAEA,QAAIA,MAAE,CAAC,EAAE,GAAE,SAASA,KAAE;AAAC,EAAAA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,IAAEA,IAAE,eAAa,CAAC,IAAE;AAAc,EAAEC,QAAIA,MAAE,CAAC,EAAE,GAAE,SAASD,KAAE;AAAC,EAAAA,IAAEA,IAAE,kBAAgB,CAAC,IAAE,mBAAkBA,IAAEA,IAAE,qBAAmB,CAAC,IAAE;AAAoB,EAAEE,QAAIA,MAAE,CAAC,EAAE;;;ACAqtB,SAASC,GAAEC,KAAEC,KAAE;AAAC,MAAG,YAAUD,IAAE,KAAK,QAAOE,GAAEF,KAAEC,KAAE,KAAE;AAAE,MAAGE,GAAEH,GAAC,EAAE,SAAOA,IAAE,MAAK;AAAA,IAAC,KAAI;AAAS,aAAOE,GAAEF,IAAE,QAAOC,KAAE,KAAE;AAAA,IAAE,KAAI;AAAU,aAAOC,GAAEF,IAAE,UAASC,KAAE,KAAE;AAAA,IAAE,KAAI;AAAW,aAAOC,GAAEE,GAAEJ,GAAC,GAAEC,KAAE,IAAE;AAAA,IAAE,KAAI;AAAO,aAAOC,GAAEF,IAAE,QAAOC,KAAE,KAAE;AAAA,EAAC;AAAA,MAAM,SAAOD,IAAE,MAAK;AAAA,IAAC,KAAI;AAAS,aAAOE,GAAEG,GAAEL,GAAC,GAAEC,KAAE,IAAE;AAAA,IAAE,KAAI;AAAU,aAAOC,GAAEI,GAAEN,GAAC,GAAEC,KAAE,IAAE;AAAA,IAAE,KAAI;AAAW,aAAOC,GAAEE,GAAEJ,GAAC,GAAEC,KAAE,IAAE;AAAA,EAAC;AAAC;AAAC,SAASG,GAAEJ,KAAE;AAAC,QAAMC,MAAED,IAAE,MAAM,CAAC;AAAE,MAAG,CAACC,OAAG,MAAIA,IAAE,OAAO,QAAO;AAAK,QAAMM,MAAE,EAAEN,KAAEO,GAAEP,GAAC,IAAE,CAAC;AAAE,SAAOG,GAAEG,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEP,IAAE,gBAAgB;AAAC;AAAC,SAASK,GAAEL,KAAE;AAAC,SAAOI,GAAE,OAAIJ,IAAE,OAAKA,IAAE,OAAM,OAAIA,IAAE,OAAKA,IAAE,OAAM,QAAMA,IAAE,QAAM,QAAMA,IAAE,QAAM,SAASA,IAAE,IAAI,KAAG,SAASA,IAAE,IAAI,IAAE,OAAIA,IAAE,OAAKA,IAAE,QAAM,QAAOA,IAAE,gBAAgB;AAAC;AAAC,SAASM,GAAEN,KAAE;AAAC,QAAMC,MAAED,IAAE,MAAM,CAAC;AAAE,MAAG,CAACC,OAAG,MAAIA,IAAE,OAAO,QAAO;AAAK,QAAMM,MAAE,EAAEP,IAAE,OAAM,CAAC,CAACA,IAAE,IAAI;AAAE,SAAOI,GAAEG,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEP,IAAE,gBAAgB;AAAC;AAAC,SAASE,GAAEF,KAAEC,KAAEM,KAAE;AAAC,QAAME,MAAEF,MAAEP,MAAEI,GAAEJ,GAAC;AAAE,SAAOC,OAAGD,MAAE,GAAEA,KAAES,KAAER,GAAC,IAAEQ,MAAE,OAAKA;AAAC;AAAC,SAASC,GAAEV,KAAEC,KAAEM,KAAEE,MAAE,GAAE;AAAC,MAAGT,KAAE;AAAC,IAAAC,QAAIA,MAAEO,GAAE;AAAG,UAAMG,MAAEX;AAAE,QAAIY,MAAE,MAAGD,IAAE,SAAOJ,MAAE,IAAGM,MAAE,MAAGF,IAAE,UAAQJ,MAAE;AAAG,WAAOI,IAAE,QAAM,OAAKA,IAAE,SAAOC,OAAGC,MAAE,KAAGF,IAAE,SAAO,OAAKA,IAAE,UAAQE,OAAGD,MAAE,KAAIL,GAAEN,KAAEU,IAAE,OAAKC,MAAEH,KAAEE,IAAE,OAAKE,MAAEJ,KAAEE,IAAE,OAAKC,MAAEH,KAAEE,IAAE,OAAKE,MAAEJ,GAAC,GAAER;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAASa,GAAEd,KAAEC,KAAE;AAAC,WAAQM,MAAE,GAAEA,MAAEP,IAAE,WAAW,QAAO,EAAEO,KAAE;AAAC,UAAME,MAAET,IAAE,WAAWO,GAAC,EAAE,oBAAoBQ,GAAE,OAAO;AAAE,IAAAN,OAAGA,IAAE,KAAK,CAAC,MAAIR,QAAIQ,IAAE,KAAK,CAAC,IAAER,KAAED,IAAE,2BAA2BA,IAAE,WAAWO,GAAC,CAAC;AAAA,EAAE;AAAC;AAAC,SAAS,EAAEN,KAAEM,KAAE;AAAC,QAAME,MAAET,GAAEgB,EAAC;AAAE,SAAO,EAAEf,GAAC,MAAIQ,IAAE,CAAC,IAAER,IAAE,CAAC,GAAEQ,IAAE,CAAC,IAAER,IAAE,CAAC,GAAEQ,IAAE,CAAC,IAAER,IAAE,CAAC,IAAG,EAAEM,GAAC,IAAEE,IAAE,CAAC,IAAEF,MAAE,EAAEN,GAAC,KAAGA,IAAE,SAAO,MAAIQ,IAAE,CAAC,IAAER,IAAE,CAAC,IAAGQ;AAAC;AAAkK,SAASQ,GAAEC,MAAEC,IAAEC,KAAEC,KAAEC,MAAE,GAAE;AAAC,QAAMC,MAAE,IAAI,MAAM,CAAC;AAAE,MAAG,EAAEH,GAAC,KAAG,EAAEC,GAAC,EAAE,CAAAE,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE;AAAA,OAAM;AAAC,QAAIC,KAAEF,MAAE;AAAE,aAAQG,MAAE,GAAEA,OAAG,GAAEA,OAAI;AAAC,YAAMC,MAAER,IAAEO,GAAC;AAAE,UAAIE;AAAE,YAAMC,KAAE,QAAMF,KAAEP,MAAE,MAAIM,OAAG,CAACD,OAAG,CAACI,IAAEC,MAAER,IAAEI,GAAC;AAAE,yBAAiBC,OAAGP,MAAEQ,MAAE,MAAIE,MAAET,IAAEK,GAAC,IAAEI,MAAE,IAAED,MAAG,mBAAiBF,OAAG,SAASA,GAAC,MAAIC,MAAE,MAAIE,MAAEH,MAAEG,MAAE,IAAG,QAAMF,QAAIJ,IAAEE,GAAC,IAAEE,KAAEH,MAAEG,KAAEL,MAAE,KAAK,IAAIA,KAAE,KAAK,IAAIK,GAAC,CAAC;AAAA,IAAE;AAAC,aAAQT,MAAE,GAAEA,OAAG,GAAEA,MAAI,SAAMK,IAAEL,GAAC,IAAEK,IAAEL,GAAC,IAAEM,MAAE,MAAID,IAAEL,GAAC,MAAIK,IAAEL,GAAC,IAAE,OAAKI;AAAA,EAAE;AAAC,WAAQE,MAAE,GAAEA,OAAG,GAAEA,MAAI,CAAAD,IAAEC,GAAC,KAAGF;AAAE,SAAOE,GAAED,GAAC;AAAC;AAAC,SAASO,GAAEZ,KAAE;AAAC,SAAO,QAAMA,IAAE;AAAW;AAAC,SAASa,GAAEb,KAAE;AAAC,SAAOc,GAAEF,GAAEZ,GAAC,IAAE,CAACA,IAAE,OAAMA,IAAE,OAAMA,IAAE,MAAM,IAAEA,GAAC,IAAE,OAAK;AAAyC;AAAC,SAASc,GAAEd,KAAE;AAAC,MAAG,MAAM,QAAQA,GAAC,GAAE;AAAC,eAAUM,OAAKN,IAAE,KAAG,CAACc,GAAER,GAAC,EAAE,QAAM;AAAG,WAAM;AAAA,EAAE;AAAC,SAAO,QAAMN,OAAGA,OAAG;AAAC;AAAC,SAASe,GAAEf,KAAEM,KAAEJ,KAAEM,MAAEF,GAAE,GAAE;AAAC,QAAMG,MAAET,OAAG,GAAEU,KAAEJ,OAAG,GAAEL,MAAEC,OAAG;AAAE,SAAO,MAAIO,OAAGO,GAAER,KAAEA,KAAE,CAACC,MAAE,MAAI,KAAK,EAAE,GAAE,MAAIC,MAAGT,GAAEO,KAAEA,KAAEE,KAAE,MAAI,KAAK,EAAE,GAAE,MAAIT,OAAG,EAAEO,KAAEA,KAAEP,MAAE,MAAI,KAAK,EAAE,GAAEO;AAAC;AAAC,SAASS,GAAEjB,KAAEM,KAAEH,KAAE;AAAC,MAAG,QAAMA,IAAE,iBAAiB,QAAOA,IAAE;AAAiB,QAAMC,MAAE,EAAEE,GAAC,GAAED,MAAEJ,GAAED,GAAC,IAAEI,KAAEG,MAAE,EAAEP,GAAC,IAAEI,KAAEI,MAAE,EAAER,GAAC,KAAGM,IAAE,eAAa,IAAEF;AAAG,SAAO,MAAIC,OAAG,MAAIE,OAAG,MAAIC,MAAEL,IAAE,4BAA0B,OAAI,KAAK,IAAIE,KAAEE,KAAEC,GAAC;AAAC;;;ACAxiH,SAASU,IAAEA,KAAE;AAAC,SAAM,YAAUA,IAAE;AAAI;;;ACAqG,IAAMC,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,MAAE,MAAKF,MAAE,GAAE;AAAC,SAAK,QAAMC,KAAE,KAAK,mBAAiBC,KAAE,KAAK,SAAOF;AAAA,EAAC;AAAC;AAAC,SAASG,IAAEF,KAAE;AAAC,SAAM,WAAUA;AAAC;AAAC,SAASG,IAAEJ,KAAEI,KAAEC,MAAE,UAAS;AAAC,MAAGL,IAAEI,GAAC,EAAE,QAAOJ,IAAE,aAAaI,IAAE,GAAEA,IAAE,GAAEA,IAAE,KAAG,GAAEA,IAAE,kBAAiBC,GAAC;AAAE,MAAGF,IAAEC,GAAC,GAAE;AAAC,QAAIF,MAAEE,IAAE;AAAO,WAAOJ,IAAE,aAAaI,IAAE,MAAMF,KAAG,GAAEE,IAAE,MAAMF,KAAG,GAAEE,IAAE,MAAMF,GAAC,KAAG,GAAE,EAAEE,IAAE,kBAAiBJ,IAAE,gBAAgB,GAAEK,GAAC;AAAA,EAAC;AAAC,SAAOL,IAAE,aAAaI,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,KAAG,GAAEJ,IAAE,kBAAiBK,GAAC;AAAC;;;ACAqB,SAASC,GAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAET,KAAEU,KAAE;AAAC,QAAMC,MAAEC,GAAEF,IAAE,IAAI;AAAE,MAAIG,IAAEC,KAAEC,KAAE;AAAE,MAAG,GAAEd,KAAEC,KAAEC,KAAEC,KAAEK,IAAE,kBAAiBJ,KAAEG,GAAC,EAAE,QAAOG,IAAE,kBAAkBD,GAAC,KAAGK,KAAEJ,IAAE,8BAA8BP,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAET,KAAEU,GAAC,GAAEG,KAAEP,KAAEQ,MAAEP,QAAIM,KAAET,KAAEU,MAAET,MAAG,GAAEQ,IAAEJ,IAAE,kBAAiBK,KAAER,KAAEN,IAAE,kBAAiBO,KAAEC,GAAC,IAAEO,KAAE;AAAM;AAAC,SAASL,IAAER,KAAEC,KAAEC,KAAEY,KAAEX,KAAE;AAAC,QAAME,OAAGL,IAAEA,GAAC,IAAEA,IAAE,IAAEK,IAAEL,GAAC,IAAEA,IAAE,MAAMA,IAAE,SAAO,CAAC,IAAEA,IAAE,CAAC,MAAI;AAAE,UAAOE,IAAE,MAAK;AAAA,IAAC,KAAI,iBAAgB;AAAC,YAAMA,MAAE,EAAEC,IAAEF,KAAED,KAAE,QAAQ,GAAE,CAAC;AAAE,aAAOG,IAAE,2BAAyB,GAAEA,IAAE,mBAAiBD,KAAE,MAAKC,IAAE,IAAED;AAAA,IAAE;AAAA,IAAC,KAAI,sBAAqB;AAAC,YAAME,MAAE,EAAED,IAAEF,KAAED,KAAE,QAAQ,GAAE,CAAC,GAAEM,MAAEJ,IAAE,oBAAoBG,KAAES,GAAC;AAAE,aAAOX,IAAE,2BAAyBG,KAAEH,IAAE,mBAAiBC,KAAE,MAAKD,IAAE,IAAEG,MAAEF;AAAA,IAAE;AAAA,IAAC,KAAI,qBAAoB;AAAC,YAAMA,MAAE,EAAED,IAAEF,KAAED,KAAE,OAAO,GAAE,CAAC,GAAEM,MAAEJ,IAAE,oBAAoBG,KAAES,GAAC;AAAE,aAAOX,IAAE,2BAAyBG,KAAEH,IAAE,mBAAiBC,KAAE,MAAKD,IAAE,IAAEG,MAAEF;AAAA,IAAE;AAAA,IAAC,KAAI,mBAAkB;AAAC,YAAMA,MAAEF,IAAE,oBAAoBG,KAAES,GAAC,GAAER,MAAE,EAAEH,IAAEF,KAAED,KAAE,QAAQ,GAAE,CAAC;AAAE,aAAOG,IAAE,2BAAyBC,MAAEE,KAAEH,IAAE,mBAAiBG,KAAE,MAAKH,IAAE,IAAEC;AAAA,IAAE;AAAA,IAAC;AAAQ,aAAO,MAAKD,IAAE,IAAE;AAAA,EAAE;AAAC;AAAC,SAASM,GAAEV,KAAEC,KAAEC,KAAEC,KAAE;AAAC,SAAOM,IAAET,KAAEC,KAAEC,KAAEC,KAAEa,EAAC,GAAEA,GAAE;AAAC;AAAC,SAASJ,GAAEZ,KAAEC,KAAEC,KAAE;AAAC,SAAO,QAAMD,OAAG,QAAMC,MAAEF,IAAE,iBAAe,oBAAkBC,OAAG,oBAAkBC,MAAEF,IAAE,mBAAiBC,QAAIC,OAAG,oBAAkBD,OAAG,oBAAkBC,MAAEe,GAAE,SAAOjB,IAAE;AAAkB;AAAC,SAASa,GAAEb,KAAE;AAAC,SAAM,yBAAuBA,OAAG,wBAAsBA;AAAC;AAAC,SAASc,GAAEd,KAAE;AAAC,SAAM,sBAAoBA;AAAC;AAAC,SAASkB,GAAElB,KAAEE,KAAEC,KAAEY,KAAEV,KAAE;AAAC,EAAAI,IAAEP,KAAEC,KAAEE,KAAEU,KAAEC,EAAC,GAAEG,GAAEnB,KAAEgB,GAAE,wBAAwB;AAAE,QAAMT,MAAES,GAAE,kBAAiBR,MAAEN,GAAEkB,IAAEpB,IAAE,cAAc;AAAE,EAAAqB,GAAE,CAAC,IAAEnB,IAAE,GAAEmB,GAAE,CAAC,IAAEnB,IAAE,GAAEmB,GAAE,CAAC,IAAEL,GAAE;AAAE,SAAO,GAAEd,IAAE,kBAAiBmB,IAAEb,KAAEO,IAAE,gBAAgB,IAAEf,IAAE,iBAAeQ,MAAE,QAAQ,KAAK,gEAAgE,GAAED;AAAC;AAAC,SAASe,IAAErB,KAAEC,KAAEC,KAAEY,KAAEX,KAAEC,KAAE;AAAC,MAAIC,MAAE;AAAE,QAAMC,MAAEF,IAAE;AAAiB,EAAAH,OAAG,GAAEa,OAAG;AAAE,WAAQP,MAAE,GAAEA,MAAEJ,KAAE,EAAEI,KAAE;AAAC,UAAMJ,MAAEH,IAAEC,MAAE,CAAC,GAAEM,MAAEP,IAAEC,MAAE,CAAC,GAAEH,MAAEE,IAAEC,MAAE,CAAC,GAAEO,MAAE,EAAEJ,IAAE,aAAaD,KAAEI,KAAET,KAAEQ,KAAE,QAAQ,GAAE,CAAC;AAAE,IAAAD,OAAGG,KAAEN,IAAEY,MAAE,CAAC,IAAEX,KAAED,IAAEY,MAAE,CAAC,IAAEP,KAAEL,IAAEY,MAAE,CAAC,IAAEN,KAAEP,OAAG,GAAEa,OAAG;AAAA,EAAC;AAAC,SAAOT,MAAEF;AAAC;AAAC,SAASmB,GAAEtB,KAAEC,KAAEC,KAAEY,KAAEX,KAAEC,KAAEC,KAAEC,KAAE;AAAC,MAAIC,MAAE;AAAE,QAAMT,MAAEQ,IAAE,2BAA2BD,GAAC,GAAEG,MAAEF,IAAE,8BAA6BG,MAAEL,IAAE;AAAiB,EAAAH,OAAG,GAAEa,OAAG;AAAE,WAAQH,KAAE,GAAEA,KAAER,KAAE,EAAEQ,IAAE;AAAC,UAAMR,MAAEH,IAAEC,MAAE,CAAC,GAAEI,MAAEL,IAAEC,MAAE,CAAC,GAAEK,MAAEN,IAAEC,MAAE,CAAC,GAAEU,MAAE,EAAEP,IAAE,aAAaD,KAAEE,KAAEC,KAAEG,KAAE,QAAQ,GAAE,CAAC;AAAE,IAAAF,OAAGI,KAAET,IAAEY,MAAE,CAAC,IAAEX,KAAED,IAAEY,MAAE,CAAC,IAAET,KAAEH,IAAEY,MAAE,CAAC,IAAE,QAAMN,MAAEF,MAAEK,MAAEb,MAAEa,MAAEb,KAAEG,OAAG,GAAEa,OAAG;AAAA,EAAC;AAAC,SAAOP,MAAEJ;AAAC;AAAC,SAASoB,GAAEvB,KAAEC,KAAEC,KAAEY,KAAEX,KAAEC,KAAEC,KAAEC,KAAE;AAAC,MAAIC,MAAE;AAAE,QAAMT,MAAEQ,IAAE,2BAA2BD,GAAC,GAAEG,MAAEF,IAAE,8BAA6BG,MAAEL,IAAE;AAAiB,EAAAH,OAAG,GAAEa,OAAG;AAAE,WAAQH,KAAE,GAAEA,KAAER,KAAE,EAAEQ,IAAE;AAAC,UAAMR,MAAEH,IAAEC,MAAE,CAAC,GAAEI,MAAEL,IAAEC,MAAE,CAAC,GAAEK,MAAEN,IAAEC,MAAE,CAAC,GAAEU,MAAE,EAAEP,IAAE,aAAaD,KAAEE,KAAEC,KAAEG,KAAE,OAAO,GAAE,CAAC;AAAE,IAAAF,OAAGI,KAAET,IAAEY,MAAE,CAAC,IAAEX,KAAED,IAAEY,MAAE,CAAC,IAAET,KAAEH,IAAEY,MAAE,CAAC,IAAE,QAAMN,MAAEF,MAAEK,MAAEb,MAAEa,MAAEb,KAAEG,OAAG,GAAEa,OAAG;AAAA,EAAC;AAAC,SAAOP,MAAEJ;AAAC;AAAC,SAASqB,GAAEzB,KAAE;AAAC,QAAMC,MAAED,IAAE,iBAAgBE,MAAEF,IAAE;AAA6B,SAAO,MAAIC,OAAG,QAAMC;AAAC;AAAC,SAASwB,GAAE1B,KAAEC,KAAEC,KAAEC,KAAEY,KAAEX,KAAEC,KAAEC,KAAE;AAAC,QAAMC,MAAED,IAAE,2BAA2BD,GAAC,GAAEG,MAAEF,IAAE;AAA6B,EAAAL,OAAG,GAAEE,OAAG;AAAE,WAAQJ,MAAE,GAAEA,MAAEgB,KAAE,EAAEhB,KAAE;AAAC,UAAMgB,MAAEf,IAAEC,MAAE,CAAC,GAAEG,MAAEJ,IAAEC,MAAE,CAAC,GAAEI,MAAEL,IAAEC,MAAE,CAAC;AAAE,IAAAC,IAAEC,MAAE,CAAC,IAAEY,KAAEb,IAAEC,MAAE,CAAC,IAAEC,KAAEF,IAAEC,MAAE,CAAC,IAAE,QAAMK,MAAEH,MAAEE,MAAEA,KAAEN,OAAG,GAAEE,OAAG;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,IAAMwB,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,2BAAyB,GAAE,KAAK,mBAAiB,GAAE,KAAK,IAAE;AAAA,EAAC;AAAC;AAAC,IAAIV;AAAE,CAAC,SAASjB,KAAE;AAAC,EAAAA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,IAAEA,IAAE,WAAS,CAAC,IAAE;AAAU,EAAEiB,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAMN,KAAE,EAAC,mBAAkB,EAAC,+BAA8Be,IAAE,mBAAkBD,GAAC,GAAE,iBAAgB,EAAC,+BAA8BH,KAAE,mBAAkB,MAAI,KAAE,GAAE,sBAAqB,EAAC,+BAA8BC,IAAE,mBAAkB,MAAI,KAAE,GAAE,qBAAoB,EAAC,+BAA8BC,IAAE,mBAAkB,MAAI,KAAE,EAAC;AAA1T,IAA4TJ,KAAEpB,GAAE;AAAhU,IAAkUgB,KAAE,IAAIW;AAAxU,IAA0UN,KAAEnB,GAAE;;;ACAr/G,IAAM0B,KAAE,EAAE,UAAU,0DAA0D;AAAE,SAASC,IAAEC,KAAE;AAAC,SAAM,EAAC,cAAaA,IAAE,cAAa,QAAOA,IAAE,SAAO,EAAC,MAAKA,IAAE,OAAO,MAAK,SAAQA,IAAE,OAAO,QAAQ,YAAY,kBAAkB,MAAK,EAAC,IAAGA,IAAE,OAAO,QAAQ,iBAAgB,CAAC,GAAE,SAAQA,IAAE,OAAO,QAAO,IAAE,KAAI;AAAC;AAAyH,eAAeC,IAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,QAAMC,MAAEJ,OAAGA,IAAE;AAAW,MAAG,YAAU,OAAOI,IAAE,QAAO;AAAK,QAAML,MAAEM,GAAED,GAAC;AAAE,MAAG,QAAML,IAAE,QAAM,EAAC,cAAaA,IAAC;AAAE,QAAMO,MAAE,MAAMC,IAAE;AAAE,IAAEL,GAAC;AAAE,QAAMM,MAAEF,IAAE,aAAYC,MAAEC,IAAE,iBAAiBJ,GAAC;AAAE,SAAOI,IAAE,cAAcD,GAAC,KAAG,QAAMJ,OAAGA,IAAE,MAAM,mEAAmE,GAAE,EAAC,cAAa,EAAC,KAAG,EAAC,QAAO,EAAC,MAAKK,IAAE,eAAeD,GAAC,GAAE,SAAQC,IAAE,kBAAkB,MAAK,EAAC,IAAGP,IAAC,CAAC,GAAE,SAAQK,IAAC,EAAC;AAAC;AAAC,SAASA,IAAEN,KAAES,KAAER,KAAE;AAAC,SAAOD,IAAE,YAAY,cAAcS,IAAE,YAAWA,IAAE,UAASR,GAAC;AAAC;AAAC,SAASO,IAAER,KAAES,KAAE;AAAC,MAAG,QAAMT,OAAG,CAACU,GAAEV,GAAC,GAAE;AAAC,QAAG,CAACS,OAAG,CAACT,IAAE,OAAO,QAAO,KAAKE,GAAE,iBAAiB,0CAA0C;AAAE,UAAMS,MAAEF;AAAE,IAAAE,IAAE,cAAYA,IAAE,YAAUC,GAAED,IAAE,SAAS,IAAGX,IAAE,OAAO,QAAQ,YAAY,kBAAkBA,IAAE,OAAO,SAAQS,GAAC;AAAA,EAAC;AAAC;AAAC,SAASF,IAAEP,KAAE;AAAC,MAAG,QAAMA,KAAE;AAAC,QAAGU,GAAEV,GAAC,EAAE,QAAOA,IAAE;AAAa,UAAMS,MAAET,IAAE;AAAO,QAAIC,MAAEQ,OAAA,gBAAAA,IAAG,QAAQ,YAAY,gBAAgBA,IAAE,MAAKA,IAAE;AAAS,WAAM,YAAU,OAAOR,QAAID,IAAE,eAAa,GAAEC,MAAE,IAAGA;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAASY,GAAEb,KAAES,MAAE,OAAG;AAAC,MAAIR,MAAED,OAAGA,IAAE;AAAsB,QAAMW,MAAEV,OAAGA,IAAE;AAAW,SAAOQ,OAAG,QAAME,QAAIV,MAAE,OAAMA,OAAG;AAAI;AAAC,IAAMW,MAAE,EAAC,cAAa,EAAC;AAAE,SAASF,GAAEV,KAAE;AAAC,SAAO,QAAMA,IAAE;AAAY;AAAC,SAASK,GAAEL,KAAE;AAAC,SAAM,QAAMA,MAAE,IAAE;AAAI;;;ACAhgD,IAAMc,MAAN,MAAM,GAAC;AAAA,EAAC,cAAa;AAAC,SAAK,mBAAiB,GAAE,KAAK,oBAAkB,GAAE,KAAK,QAAM,UAAS,KAAK,8BAA4B,GAAE,KAAK,gCAA8B,MAAK,KAAK,2BAAyB,MAAK,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,IAAI,+BAA8B;AAAC,WAAO,KAAK;AAAA,EAA6B;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK;AAAA,EAAgB;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,IAAI,KAAKC,KAAE;AAAC,SAAK,QAAMA,KAAE,KAAK,8BAA4BC,GAAED,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,+BAA8B;AAAC,WAAM,sBAAoB,KAAK;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,SAAK,OAAK,MAAK,KAAK,mBAAiB,GAAE,KAAK,oBAAkB,GAAE,KAAK,gCAA8B,MAAK,KAAK,OAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,aAAaA,KAAE;AAAC,SAAK,mBAAiBA,KAAE,KAAK,oBAAkB;AAAA,EAAC;AAAA,EAAC,IAAI,yBAAyBA,KAAE;AAAC,SAAK,mBAAiBA,MAAE,KAAK,6BAA4B,KAAK,oBAAkB;AAAA,EAAC;AAAA,EAAC,qBAAqBA,KAAE;AAAC,SAAK,qBAAmBA;AAAA,EAAC;AAAA,EAAC,oBAAoBA,KAAEE,KAAE;AAAC,UAAMC,MAAE,KAAK,2BAA2BD,GAAC;AAAE,WAAO,QAAM,KAAK,+BAA6BC,MAAEH,MAAEG;AAAA,EAAC;AAAA,EAAC,2BAA2BH,KAAE;AAAC,QAAIE,MAAE,KAAK;AAAiB,UAAMC,MAAE,KAAK;AAA6B,WAAO,QAAMA,QAAID,OAAGE,IAAED,GAAC,IAAE,KAAK,8BAA6BD,MAAEF,IAAE,eAAa,KAAK;AAAA,EAAiB;AAAA,EAAC,qBAAqBE,KAAE;AAAC,SAAK,OAAKA,IAAE,MAAK,KAAK,OAAKC,GAAED,IAAE,IAAI,IAAEA,IAAE,OAAK,UAAS,KAAK,2BAAyB,EAAEA,IAAE,QAAO,CAAC;AAAA,EAAC;AAAA,EAAC,mCAAmCF,KAAEK,KAAED,KAAE;AAAC,QAAG,EAAEJ,GAAC,EAAE,QAAO,MAAK,KAAK,gCAA8B;AAAM,UAAMC,MAAED,OAAGA,IAAE;AAAO,IAAAC,OAAG,EAAEI,GAAC,KAAG,EAAED,GAAC,KAAG,KAAK,gCAA8BE,IAAEN,GAAC,GAAEO,IAAE,KAAK,+BAA8BF,IAAEJ,IAAE,SAAQI,KAAED,GAAC,CAAC,KAAG,KAAK,gCAA8BJ;AAAA,EAAC;AAAA,EAAC,OAAO,kBAAkBA,KAAE;AAAC,UAAME,MAAE,IAAI;AAAE,WAAO,EAAEF,GAAC,KAAGE,IAAE,qBAAqBF,GAAC,GAAEE;AAAA,EAAC;AAAC;;;ACA3pC,IAAMM,KAAN,cAAgBC,IAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,eAAeC,KAAE;AAAC,IAAAC,GAAE,KAAK,iBAAgBD,GAAC,GAAE,KAAK,0BAA0B,GAAE,KAAK,MAAM,wBAAuB,IAAI;AAAA,EAAC;AAAA,EAAC,YAAYE,MAAE,CAAC,GAAE;AAAC,UAAM,GAAE,KAAK,OAAKA,GAAE,QAAO,KAAK,cAAY,IAAI,SAAM,KAAK,kBAAgBA,GAAE,GAAE,KAAK,iBAAe,IAAIC,MAAE,KAAK,gBAAc,IAAIA,MAAE,KAAK,WAAS,MAAG,KAAK,6BAA2B,OAAG,KAAK,WAAS,MAAG,KAAK,aAAW,QAAMD,IAAE,cAAYA,IAAE,YAAW,KAAK,WAASA,IAAE,UAAS,KAAK,YAAU,KAAK,SAAS,sBAAoB,KAAK,SAAS,uBAAqB,IAAIE;AAAG,UAAMC,MAAEH,IAAE;AAAW,MAAEG,GAAC,MAAI,KAAK,cAAY,MAAM,KAAKA,GAAC;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,YAAY,SAAO;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,YAAYL,KAAE;AAAC,IAAAM,GAAE,QAAM,KAAK,gBAAc,QAAMN,KAAE,8CAA8C,GAAE,KAAK,eAAaA;AAAA,EAAC;AAAA,EAAC,YAAYA,KAAE;AAAC,IAAAA,IAAE,UAAQ,KAAK,UAAS,KAAK,YAAY,KAAKA,GAAC,GAAE,KAAK,6BAA2B,KAAK,8BAA4BA,IAAE,2BAA0B,KAAK,MAAM,uBAAsB,EAAC,QAAO,MAAK,UAASA,IAAC,CAAC,GAAE,KAAK,0BAA0B;AAAA,EAAC;AAAA,EAAC,eAAeA,KAAE;AAAC,UAAME,MAAE,KAAK,YAAY,OAAOF,KAAE,CAAC,EAAE,CAAC;AAAE,IAAAE,QAAI,KAAK,MAAM,yBAAwB,EAAC,QAAO,MAAK,UAASA,IAAC,CAAC,GAAE,KAAK,0BAA0B;AAAA,EAAE;AAAA,EAAC,sBAAqB;AAAC,WAAK,KAAK,YAAY,SAAO,IAAG,MAAK,eAAe,CAAC;AAAA,EAAC;AAAA,EAAC,2BAA2BF,KAAE;AAAC,SAAK,MAAM,yBAAwB,EAAC,QAAO,MAAK,UAASA,IAAC,CAAC,GAAE,KAAK,0BAA0B;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,QAAQA,KAAE;AAAC,QAAG,KAAK,aAAWA,KAAE;AAAC,WAAK,WAASA;AAAE,iBAAUA,OAAK,KAAK,YAAY,CAAAA,IAAE,UAAQ,KAAK;AAAS,WAAK,MAAM,qBAAoB,IAAI;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,UAAMA,MAAE,IAAID,IAAEC,GAAE,YAAY;AAAE,eAAUE,OAAK,KAAK,YAAY,CAAAA,IAAE,YAAUD,IAAEC,IAAE,WAAUF,GAAC;AAAE,WAAO,KAAK,MAAM,oBAAmB,IAAI,GAAEA;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAE;AAAC,eAAUE,OAAK,KAAK,YAAY,CAAAA,IAAE,YAAUK,GAAEL,IAAE,WAAUF,GAAC;AAAE,SAAK,MAAM,oBAAmB,IAAI;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,UAAMA,MAAE,IAAID,IAAEC,GAAE,SAAS;AAAE,eAAUE,OAAK,KAAK,YAAY,CAAAA,IAAE,aAAWD,IAAEC,IAAE,YAAWF,GAAC;AAAE,WAAO,KAAK,MAAM,oBAAmB,IAAI,GAAEA;AAAA,EAAC;AAAA,EAAC,gBAAgBA,KAAE;AAAC,eAAUE,OAAK,KAAK,YAAY,CAAAA,IAAE,aAAWK,GAAEL,IAAE,YAAWF,GAAC;AAAE,SAAK,MAAM,oBAAmB,IAAI;AAAA,EAAC;AAAA,EAAC,gCAAgCA,KAAEE,KAAE;AAAC,WAAOM,GAAEN,KAAE,KAAK,gBAAeF,IAAE,cAAc;AAAA,EAAC;AAAA,EAAC,iCAAiCA,KAAE;AAAC,WAAOQ,GAAEN,GAAE,GAAE,KAAK,gBAAeF,IAAE,oBAAoB;AAAA,EAAC;AAAA,EAAC,4BAA2B;AAAC,WAAO,KAAK;AAAA,EAA0B;AAAA,EAAC,IAAI,2BAA0B;AAAC,WAAO,KAAK,wBAAwB,GAAE,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,4BAA2B;AAAC,WAAO,KAAK,wBAAwB,GAAE,KAAK;AAAA,EAAc;AAAA,EAAC,0BAAyB;AAAC,QAAG,CAAC,KAAK,YAAU,CAAC,KAAK,2BAA2B;AAAO,SAAK,eAAe,KAAK,GAAE,KAAK,cAAc,KAAK;AAAE,eAAUE,OAAK,KAAK,aAAY;AAAC,YAAMG,MAAEH,IAAE;AAAa,QAAEG,GAAC,MAAII,GAAEJ,KAAE,KAAK,gBAAeH,IAAE,oBAAoB,GAAEO,GAAEJ,KAAE,KAAK,eAAc,KAAK,iCAAiCH,GAAC,CAAC;AAAA,IAAE;AAAC,MAAE,KAAK,eAAe,QAAO,KAAK,eAAe,KAAI,KAAK,eAAe,KAAI,GAAE,GAAE,EAAE,KAAK,cAAc,QAAO,KAAK,cAAc,KAAI,KAAK,cAAc,KAAI,GAAE;AAAE,UAAMG,MAAEJ,GAAE,GAAEK,MAAEL,GAAE,GAAEM,MAAEG,IAAE,KAAK,cAAc;AAAE,eAAUV,OAAK,KAAK,aAAY;AAAC,YAAMD,MAAEC,IAAE;AAAa,UAAG,EAAED,GAAC,EAAE;AAAS,YAAMY,MAAEX,IAAE,sBAAqBY,KAAEF,IAAEC,GAAC;AAAE,MAAAF,GAAEJ,KAAEN,IAAE,QAAOY,GAAC;AAAE,YAAMH,MAAE,EAAEH,KAAE,KAAK,eAAe,MAAM,GAAEK,MAAEX,IAAE,SAAOa;AAAE,WAAK,eAAe,OAAO,CAAC,IAAE,KAAK,IAAI,KAAK,eAAe,OAAO,CAAC,GAAEJ,MAAEE,GAAC,GAAED,GAAEH,KAAED,KAAE,KAAK,cAAc;AAAE,YAAMQ,KAAE,EAAEP,KAAE,KAAK,cAAc,MAAM,GAAEQ,MAAEJ,MAAEH;AAAE,WAAK,cAAc,OAAO,CAAC,IAAE,KAAK,IAAI,KAAK,cAAc,OAAO,CAAC,GAAEM,KAAEC,GAAC;AAAA,IAAC;AAAC,SAAK,WAAS;AAAA,EAAE;AAAA,EAAC,4BAA2B;AAAC,SAAK,WAAS,MAAG,EAAE,KAAK,YAAY,KAAG,KAAK,aAAa,sBAAsB,MAAK,KAAK,cAAc,MAAM;AAAA,EAAC;AAAA,EAAC,MAAMZ,KAAEG,KAAE;AAAC,MAAE,KAAK,YAAY,KAAG,KAAK,aAAa,OAAO,KAAKH,KAAEG,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,UAAML,MAAE;AAAK,WAAM,EAAC,aAAY,CAAAE,QAAGF,IAAE,YAAY,SAASE,GAAC,GAAE,kBAAiB,CAAAA,QAAGF,IAAE,YAAY,QAAQE,GAAC,EAAC;AAAA,EAAC;AAAC;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,MAAIL,GAAE,OAAO,WAAU,OAAO,WAAU,OAAO,SAAS,GAAE,KAAK,MAAIA,GAAE,CAAC,OAAO,WAAU,CAAC,OAAO,WAAU,CAAC,OAAO,SAAS;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,IAAI,CAAC,IAAE,KAAK,IAAI,CAAC,KAAG,KAAK,IAAI,CAAC,IAAE,KAAK,IAAI,CAAC,KAAG,KAAK,IAAI,CAAC,IAAE,KAAK,IAAI,CAAC;AAAA,EAAC;AAAC;AAAC,IAAMI,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAOW,GAAE;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,IAAAR,GAAE,KAAK,KAAI,OAAO,WAAU,OAAO,WAAU,OAAO,SAAS,GAAEA,GAAE,KAAK,KAAI,CAAC,OAAO,WAAU,CAAC,OAAO,WAAU,CAAC,OAAO,SAAS,GAAES,GAAE,KAAK,MAAM;AAAA,EAAC;AAAC;AAAC,SAASP,GAAET,KAAEE,KAAEG,KAAE;AAAC,QAAMC,MAAEN,IAAE,OAAMD,MAAEC,IAAE;AAAM,MAAGiB,GAAEZ,GAAC,GAAE;AAAC,UAAML,MAAEO,GAAEW,IAAEb,IAAE,EAAE,GAAEA,IAAE,EAAE,GAAEA,IAAE,EAAE,CAAC;AAAE,IAAAc,GAAEC,KAAEd,KAAEN,GAAC,GAAEmB,GAAEE,IAAEtB,KAAEC,GAAC;AAAE,aAAQK,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAH,IAAE,IAAIG,GAAC,IAAE,KAAK,IAAIH,IAAE,IAAIG,GAAC,GAAEe,IAAEf,GAAC,CAAC,GAAEH,IAAE,IAAIG,GAAC,IAAE,KAAK,IAAIH,IAAE,IAAIG,GAAC,GAAEgB,GAAEhB,GAAC,CAAC;AAAA,EAAC,WAASI,GAAEW,KAAEd,KAAED,GAAC,GAAE,EAAEC,KAAEP,GAAC,EAAE,UAAQQ,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAL,IAAE,IAAIK,GAAC,IAAE,KAAK,IAAIL,IAAE,IAAIK,GAAC,GAAEa,IAAEb,GAAC,CAAC,GAAEL,IAAE,IAAIK,GAAC,IAAE,KAAK,IAAIL,IAAE,IAAIK,GAAC,GAAEa,IAAEb,GAAC,CAAC;AAAA,OAAM;AAAC,IAAAE,GAAEY,IAAEtB,KAAEM,GAAC;AAAE,aAAQL,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAE,IAAE,IAAIF,GAAC,IAAE,KAAK,IAAIE,IAAE,IAAIF,GAAC,GAAEoB,IAAEpB,GAAC,GAAEqB,GAAErB,GAAC,CAAC,GAAEE,IAAE,IAAIF,GAAC,IAAE,KAAK,IAAIE,IAAE,IAAIF,GAAC,GAAEoB,IAAEpB,GAAC,GAAEqB,GAAErB,GAAC,CAAC;AAAE,aAAQA,MAAE,GAAEA,MAAE,GAAE,EAAEA,KAAE;AAAC,MAAAD,GAAEqB,KAAEd,GAAC,GAAEP,GAAEsB,IAAEtB,GAAC,GAAEqB,IAAEpB,GAAC,IAAED,IAAEC,GAAC,GAAEqB,GAAErB,GAAC,IAAEM,IAAEN,GAAC,GAAES,GAAEW,KAAEA,KAAEf,GAAC,GAAEI,GAAEY,IAAEA,IAAEhB,GAAC;AAAE,eAAQL,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAE,IAAE,IAAIF,GAAC,IAAE,KAAK,IAAIE,IAAE,IAAIF,GAAC,GAAEoB,IAAEpB,GAAC,GAAEqB,GAAErB,GAAC,CAAC,GAAEE,IAAE,IAAIF,GAAC,IAAE,KAAK,IAAIE,IAAE,IAAIF,GAAC,GAAEoB,IAAEpB,GAAC,GAAEqB,GAAErB,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAMkB,KAAEjB,GAAE;AAAV,IAAYmB,MAAEnB,GAAE;AAAhB,IAAkBoB,KAAEpB,GAAE;;;ACAplL,IAAMqB,MAAE,CAAC,oBAAmB,sBAAqB,qBAAoB,uBAAsB,+BAA8B,wBAAuB,qBAAoB,oBAAmB,oBAAmB,uBAAsB,yBAAwB,uBAAuB;;;ACAoL,IAAMC,MAAN,cAAgBC,IAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,YAAYC,KAAED,MAAE,IAAG;AAAC,UAAM,GAAE,KAAK,cAAYA,KAAE,KAAK,OAAKE,GAAE,OAAM,KAAK,SAAO,IAAIC,MAAE,KAAK,YAAU,OAAG,KAAK,WAAS,IAAIJ,MAAE,KAAK,gBAAc,IAAIA,MAAE,KAAK,gBAAc,IAAIK,MAAE,KAAK,cAAYJ,KAAE,KAAK,WAAQC,OAAA,gBAAAA,IAAG,YAAS,MAAG,KAAK,YAASA,OAAA,gBAAAA,IAAG,aAAU,MAAG,KAAK,gBAAaA,OAAA,gBAAAA,IAAG,iBAAcI,GAAE,OAAM,KAAK,kBAAeJ,OAAA,gBAAAA,IAAG,kBAAe;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,YAAY,GAAE,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAK,YAAY,GAAE,KAAK,SAAOA;AAAE,eAAUE,OAAKF,IAAE,MAAK,cAAc,IAAI,KAAK,OAAO,GAAGE,KAAG,CAAAH,QAAGC,IAAE,YAAYE,KAAEH,GAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,SAAK,cAAc,UAAU,GAAE,KAAK,kCAAkC;AAAA,EAAC;AAAA,EAAC,IAAIC,KAAE;AAAC,SAAK,SAAS,KAAKA,GAAC,GAAEA,IAAE,cAAY,MAAK,KAAK,OAAO,KAAK,oBAAmB,EAAC,OAAM,MAAK,QAAOA,IAAC,CAAC,GAAE,EAAE,KAAK,OAAO,KAAG,KAAK,cAAc,KAAKA,GAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,KAAE;AAAC,SAAK,SAAS,gBAAgBA,GAAC,MAAIA,IAAE,cAAY,MAAK,KAAK,OAAO,KAAK,sBAAqB,EAAC,OAAM,MAAK,QAAOA,IAAC,CAAC,GAAE,EAAE,KAAK,OAAO,MAAI,KAAK,cAAc,gBAAgBA,GAAC,KAAG,KAAK,QAAQ,OAAO,CAACA,GAAC,CAAC;AAAA,EAAG;AAAA,EAAC,QAAQA,KAAE;AAAC,SAAK,SAAS,UAAUA,GAAC;AAAE,eAAUE,OAAKF,IAAE,CAAAE,IAAE,cAAY;AAAK,SAAK,OAAO,KAAK,qBAAoB,EAAC,OAAM,MAAK,SAAQF,IAAC,CAAC,GAAE,EAAE,KAAK,OAAO,KAAG,KAAK,cAAc,UAAUA,GAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,KAAE;AAAC,UAAME,MAAE,IAAI;AAAM,QAAG,KAAK,SAAS,oBAAoBF,KAAEA,IAAE,QAAOE,GAAC,GAAE,MAAIA,IAAE,QAAO;AAAC,iBAAUF,OAAKE,IAAE,CAAAF,IAAE,cAAY;AAAK,UAAG,KAAK,OAAO,KAAK,uBAAsB,EAAC,OAAM,MAAK,SAAQE,IAAC,CAAC,GAAE,EAAE,KAAK,OAAO,GAAE;AAAC,iBAAQF,MAAE,GAAEA,MAAEE,IAAE,SAAQ,MAAK,cAAc,gBAAgBA,IAAEF,GAAC,CAAC,KAAGE,IAAEF,GAAC,IAAEE,IAAEA,IAAE,SAAO,CAAC,GAAEA,IAAE,UAAQ,KAAG,EAAEF;AAAE,aAAK,QAAQ,OAAOE,GAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,MAAE,KAAK,MAAM,KAAG,KAAK,iBAAeC,GAAE,QAAM,KAAK,OAAO,UAAU,KAAK,EAAE;AAAA,EAAC;AAAA,EAAC,sBAAsBH,KAAEE,KAAE;AAAC,MAAE,KAAK,OAAO,KAAG,CAAC,KAAK,cAAc,SAASF,GAAC,KAAG,KAAK,QAAQ,OAAOA,KAAEE,GAAC;AAAA,EAAC;AAAA,EAAC,6BAA4B;AAAC,WAAO,EAAE,KAAK,OAAO,KAAG,KAAK,SAAS,SAAO,MAAI,CAAC,KAAK,kBAAgB,KAAK,UAAQ,IAAIE,GAAG,CAAAJ,QAAGA,IAAE,yBAAyB,MAAO,GAAE,KAAK,QAAQ,IAAI,KAAK,SAAS,MAAK,KAAK,SAAS,MAAM,KAAG,EAAE,KAAK,OAAO,KAAG,KAAK,cAAc,SAAO,MAAI,KAAK,QAAQ,IAAI,KAAK,cAAc,MAAK,KAAK,cAAc,MAAM,GAAE,KAAK,cAAc,MAAM,IAAG,KAAK;AAAA,EAAO;AAAA,EAAC,8BAA6B;AAAC,SAAK,kCAAkC,GAAE,KAAK,OAAO,KAAK,+BAA8B,IAAI;AAAA,EAAC;AAAA,EAAC,oCAAmC;AAAC,SAAK,UAAQ,EAAE,KAAK,OAAO,GAAE,KAAK,cAAc,MAAM;AAAA,EAAC;AAAC;;;ACAxsF,SAASK,IAAEA,KAAEC,KAAEC,KAAE;AAAC,SAAO,IAAE,KAAK,KAAK,KAAK,KAAKD,MAAEA,MAAEC,MAAEA,GAAC,IAAE,KAAK,IAAI,MAAGF,GAAC,IAAEC,GAAC;AAAC;AAAC,SAASA,IAAED,KAAEC,KAAEC,KAAE;AAAC,SAAO,IAAE,KAAK,KAAK,KAAK,KAAKD,MAAEA,MAAEC,MAAEA,GAAC,IAAE,KAAK,IAAI,MAAGF,GAAC,IAAEE,GAAC;AAAC;AAAC,SAASA,IAAEF,KAAEC,KAAEC,KAAE;AAAC,SAAO,IAAE,KAAK,KAAKD,MAAE,KAAK,IAAI,MAAGD,GAAC,IAAE,KAAK,KAAKC,MAAEA,MAAEC,MAAEA,GAAC,CAAC;AAAC;AAAC,SAASC,IAAEH,KAAEC,KAAEC,KAAE;AAAC,SAAO,IAAE,KAAK,KAAKA,MAAE,KAAK,IAAI,MAAGF,GAAC,IAAE,KAAK,KAAKC,MAAEA,MAAEC,MAAEA,GAAC,CAAC;AAAC;;;ACA0qC,IAAIE;AAAE,IAAIC,KAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,YAAYC,MAAE,CAAC,GAAE;AAAC,UAAMA,GAAC,GAAE,KAAK,UAAQC,GAAE,GAAE,KAAK,MAAIA,GAAE,GAAE,KAAK,UAAQA,GAAE,GAAE,KAAK,eAAaA,GAAE,GAAE,KAAK,aAAWA,GAAE,GAAE,KAAK,OAAK,EAAE,GAAE,KAAK,YAAUC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,WAASA,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,OAAK,KAAG,MAAI,KAAK,IAAG,KAAK,WAASA,GAAE,GAAE,GAAG,GAAE,KAAK,aAAW,MAAG,KAAK,cAAYC,GAAE,GAAE,KAAK,uBAAqB,MAAG,KAAK,wBAAsBA,GAAE,GAAE,KAAK,mCAAiC,MAAG,KAAK,8BAA4BA,GAAE,GAAE,KAAK,gBAAc,MAAG,KAAK,WAASC,GAAE,GAAE,KAAK,gBAAcH,GAAE,GAAE,KAAK,cAAY,GAAE,KAAK,oBAAkB;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,WAAWD,KAAE;AAAC,SAAK,cAAYA,MAAE,IAAEA,MAAE;AAAA,EAAC;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,IAAIA,KAAE;AAAC,SAAK,mBAAmBA,KAAE,KAAK,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,OAAOA,KAAE;AAAC,SAAK,mBAAmBA,KAAE,KAAK,SAAQ,SAAS;AAAA,EAAC;AAAA,EAAC,IAAI,MAAK;AAAC,WAAOG,GAAE,KAAK,KAAK,WAAU,KAAK,QAAO,KAAK,GAAG,GAAE,KAAK;AAAA,EAAI;AAAA,EAAC,IAAI,KAAI;AAAC,WAAO,KAAK;AAAA,EAAG;AAAA,EAAC,IAAI,GAAGH,KAAE;AAAC,SAAK,mBAAmBA,KAAE,KAAK,KAAI,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,iBAAiB,GAAE,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,WAAWA,KAAE;AAAC,IAAAC,GAAE,KAAK,aAAYD,GAAC,GAAE,KAAK,aAAW,OAAG,KAAK,mCAAiC,MAAG,KAAK,uBAAqB,MAAG,KAAK,gBAAc;AAAA,EAAE;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,iBAAiB,GAAE,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,iBAAiB,GAAE,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,iBAAiB,GAAE,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,SAAS,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,KAAKA,KAAE;AAAC,SAAK,SAAS,CAAC,MAAIA,QAAI,KAAK,SAAS,CAAC,IAAEA,KAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc,MAAG,KAAK,aAAa,UAAU;AAAA,EAAE;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,SAAS,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,IAAIA,KAAE;AAAC,SAAK,SAAS,CAAC,MAAIA,QAAI,KAAK,SAAS,CAAC,IAAEA,KAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc,MAAG,KAAK,aAAa,UAAU;AAAA,EAAE;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,SAASA,KAAE;AAAC,SAAK,IAAEA,IAAE,CAAC,GAAE,KAAK,IAAEA,IAAE,CAAC,GAAE,KAAK,QAAMA,IAAE,CAAC,GAAE,KAAK,SAAOA,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,QAAG,MAAI,KAAK,WAAW,QAAO,KAAK;AAAU,UAAMA,MAAEK,GAAEJ,GAAE,GAAE,KAAK,WAAU,IAAE,KAAK,UAAU,GAAEK,MAAE,KAAK,KAAK,gBAAgB;AAAE,WAAOA,OAAG,EAAEN,KAAEM,GAAC,IAAEA,MAAEN;AAAA,EAAC;AAAA,EAAC,IAAI,IAAG;AAAC,WAAO,KAAK,UAAU,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,EAAEA,KAAE;AAAC,IAAAA,OAAG,KAAK,SAAS,GAAG,IAAI,GAAE,KAAK,UAAU,CAAC,MAAIA,QAAI,KAAK,UAAU,CAAC,IAAEA,KAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc,MAAG,KAAK,aAAa,WAAW;AAAA,EAAE;AAAA,EAAC,IAAI,IAAG;AAAC,WAAO,KAAK,UAAU,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,EAAEA,KAAE;AAAC,IAAAA,OAAG,KAAK,SAAS,GAAG,MAAM,GAAE,KAAK,UAAU,CAAC,MAAIA,QAAI,KAAK,UAAU,CAAC,IAAEA,KAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc,MAAG,KAAK,aAAa,WAAW;AAAA,EAAE;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,UAAU,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,MAAMA,KAAE;AAAC,SAAK,UAAU,CAAC,MAAIA,QAAI,KAAK,UAAU,CAAC,IAAEA,KAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc,MAAG,KAAK,aAAa,WAAW;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,UAAU,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAOA,KAAE;AAAC,SAAK,UAAU,CAAC,MAAIA,QAAI,KAAK,UAAU,CAAC,IAAEA,KAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc,MAAG,KAAK,aAAa,WAAW;AAAA,EAAE;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,UAAU,CAAC,IAAE,KAAK,SAAS,GAAG,KAAK,IAAE,KAAK,SAAS,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,UAAUA,KAAE;AAAC,SAAK,QAAMA,OAAG,KAAK,SAAS,GAAG,KAAK,IAAE,KAAK,SAAS,GAAG,IAAI;AAAA,EAAE;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,UAAU,CAAC,IAAE,KAAK,SAAS,GAAG,GAAG,IAAE,KAAK,SAAS,GAAG,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,WAAWA,KAAE;AAAC,SAAK,SAAOA,OAAG,KAAK,SAAS,GAAG,GAAG,IAAE,KAAK,SAAS,GAAG,MAAM;AAAA,EAAE;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,cAAc,CAAC,IAAE,KAAK,UAAU,CAAC,IAAE,KAAK,SAAS,GAAG,IAAI,GAAE,KAAK,cAAc,CAAC,IAAE,KAAK,UAAU,CAAC,IAAE,KAAK,SAAS,GAAG,MAAM,GAAE,KAAK,cAAc,CAAC,IAAE,KAAK,WAAU,KAAK,cAAc,CAAC,IAAE,KAAK,YAAW,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,QAAM,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,QAAQA,KAAE;AAAC,MAAE,KAAK,UAASA,GAAC,MAAI,KAAK,UAAU,CAAC,KAAGA,IAAE,GAAG,IAAI,IAAE,KAAK,SAAS,GAAG,IAAI,GAAE,KAAK,UAAU,CAAC,KAAGA,IAAE,GAAG,MAAM,IAAE,KAAK,SAAS,GAAG,MAAM,GAAE,KAAK,UAAU,CAAC,KAAGA,IAAE,GAAG,KAAK,IAAEA,IAAE,GAAG,IAAI,KAAG,KAAK,SAAS,GAAG,KAAK,IAAE,KAAK,SAAS,GAAG,IAAI,IAAG,KAAK,UAAU,CAAC,KAAGA,IAAE,GAAG,GAAG,IAAEA,IAAE,GAAG,MAAM,KAAG,KAAK,SAAS,GAAG,GAAG,IAAE,KAAK,SAAS,GAAG,MAAM,IAAGO,GAAE,KAAK,UAASP,GAAC,GAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc,MAAG,KAAK,aAAa,UAAU,GAAE,KAAK,aAAa,WAAW;AAAA,EAAE;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,yBAAuBQ,GAAE,KAAK,uBAAsB,KAAK,kBAAiB,KAAK,UAAU,GAAE,KAAK,uBAAqB,QAAI,KAAK;AAAA,EAAqB;AAAA,EAAC,IAAI,mBAAkB;AAAC,UAAMR,MAAE,KAAK,OAAMM,MAAE,KAAK,QAAOH,MAAE,KAAK,OAAK,KAAK,IAAI,KAAK,OAAK,CAAC,GAAED,MAAEC,MAAE,KAAK,SAAQM,MAAEC,GAAEP,GAAE,GAAE,CAACD,OAAG,IAAE,IAAE,KAAK,SAAS,GAAG,IAAI,IAAEF,MAAGE,OAAG,IAAE,IAAE,KAAK,SAAS,GAAG,KAAK,IAAEF,MAAG,CAACG,OAAG,IAAE,IAAE,KAAK,SAAS,GAAG,MAAM,IAAEG,MAAGH,OAAG,IAAE,IAAE,KAAK,SAAS,GAAG,GAAG,IAAEG,MAAG,KAAK,MAAK,KAAK,GAAG,GAAEK,MAAE,KAAK,KAAK,kBAAkB;AAAE,WAAOA,OAAG,EAAEA,KAAEF,GAAC,IAAEE,MAAEF;AAAA,EAAC;AAAA,EAAC,IAAI,0BAAyB;AAAC,WAAOE,GAAER,GAAE,GAAE,KAAK,gBAAgB,KAAG,KAAK,KAAK,yBAAyB,KAAGA,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,IAAI,IAAIH,KAAE;AAAC,SAAK,OAAKA,KAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc;AAAA,EAAE;AAAA,EAAC,IAAI,OAAM;AAAC,WAAOC,IAAE,KAAK,MAAK,KAAK,OAAM,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,KAAKD,KAAE;AAAC,SAAK,OAAKA,IAAEA,KAAE,KAAK,OAAM,KAAK,MAAM,GAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc;AAAA,EAAE;AAAA,EAAC,IAAI,OAAM;AAAC,WAAOE,IAAE,KAAK,MAAK,KAAK,OAAM,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,KAAKF,KAAE;AAAC,SAAK,OAAKO,IAAEP,KAAE,KAAK,OAAM,KAAK,MAAM,GAAE,KAAK,uBAAqB,MAAG,KAAK,gBAAc;AAAA,EAAE;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,EAAE,KAAK,QAAO,KAAK,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,kBAAkB,GAAE,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,6BAA4B;AAAC,YAAO,KAAK,oCAAkC,KAAK,gBAAcW,GAAE,KAAK,6BAA4B,KAAK,UAAU,GAAEC,GAAE,KAAK,6BAA4B,KAAK,2BAA2B,GAAE,KAAK,mCAAiC,QAAI,KAAK;AAAA,EAA2B;AAAA,EAAC,gBAAgBZ,KAAE;AAAC,UAAMM,MAAE,IAAEN,MAAE;AAAE,WAAO,IAAE,KAAK,OAAK,KAAK,OAAK,KAAK,MAAI,KAAK,OAAKM,OAAG,KAAK,MAAI,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAO,KAAK,IAAI,KAAK,OAAK,CAAC,KAAG,KAAK,QAAM;AAAA,EAAE;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAO,KAAK,sBAAoB,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,QAAM,KAAK,qBAAmB,KAAK,qBAAmB;AAAA,EAAC;AAAA,EAAC,SAASN,KAAE;AAAC,IAAAE,GAAE,KAAK,KAAK,QAAOF,IAAE,GAAG,GAAE,KAAK,SAAOA,IAAE,QAAO,KAAK,KAAGA,IAAE,IAAGO,GAAE,KAAK,WAAUP,IAAE,QAAQ,GAAE,KAAK,aAAa,WAAW,GAAEO,GAAE,KAAK,UAASP,IAAE,OAAO,GAAE,KAAK,aAAa,UAAU,GAAEO,GAAE,KAAK,UAASP,IAAE,OAAO,GAAE,KAAK,aAAa,UAAU,GAAE,KAAK,OAAKA,IAAE,KAAI,KAAK,oBAAkBA,IAAE;AAAkB,UAAMM,MAAEN;AAAE,WAAO,KAAK,aAAWM,IAAE,YAAW,KAAK,eAAaL,GAAE,KAAK,aAAYD,IAAE,UAAU,GAAEE,GAAE,KAAK,YAAWF,IAAE,SAAS,GAAEE,GAAE,KAAK,SAAQF,IAAE,MAAM,GAAEE,GAAE,KAAK,cAAaF,IAAE,WAAW,IAAG,KAAK,uBAAqB,MAAG,KAAK,gBAAcM,IAAE,eAAc,KAAK,kBAAgBO,GAAE,KAAK,UAASb,IAAE,OAAO,GAAE,KAAK,gBAAc,QAAIM,IAAE,mCAAiC,KAAK,mCAAiC,QAAIL,GAAE,KAAK,6BAA4BD,IAAE,0BAA0B,GAAE,KAAK,mCAAiC,QAAIO,GAAE,KAAK,eAAcP,IAAE,YAAY,GAAE,KAAK,aAAWA,IAAE,YAAW;AAAA,EAAI;AAAA,EAAC,aAAaA,KAAE;AAAC,SAAK,MAAIA,IAAE,KAAI,KAAK,SAAOA,IAAE,QAAO,KAAK,KAAGA,IAAE;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIH,KAAG,SAAS,IAAI;AAAA,EAAC;AAAA,EAAC,OAAOG,KAAE;AAAC,WAAO,EAAE,KAAK,KAAIA,IAAE,GAAG,KAAG,EAAE,KAAK,QAAOA,IAAE,MAAM,KAAG,EAAE,KAAK,IAAGA,IAAE,EAAE,KAAG,EAAE,KAAK,WAAUA,IAAE,QAAQ,KAAG,EAAE,KAAK,UAASA,IAAE,OAAO,KAAGc,GAAE,KAAK,SAAQd,IAAE,OAAO,KAAG,KAAK,SAAOA,IAAE,OAAK,KAAK,eAAaA,IAAE,cAAY,KAAK,sBAAoBA,IAAE;AAAA,EAAiB;AAAA,EAAC,aAAaA,KAAE;AAAC,QAAG,KAAK,IAAIA,IAAE,MAAI,KAAK,IAAI,KAAG,QAAMe,GAAEf,IAAE,SAAQ,KAAK,QAAQ,KAAG,OAAIe,GAAE,KAAK,gBAAef,IAAE,cAAc,KAAG,IAAG,QAAM;AAAG,MAAE,IAAGA,IAAE,KAAIA,IAAE,MAAM,GAAE,EAAE,IAAG,KAAK,KAAI,KAAK,MAAM;AAAE,UAAMM,MAAE,EAAE,IAAG,EAAE,GAAEH,MAAE,EAAE,EAAE,GAAED,MAAE,EAAE,EAAE,GAAEO,MAAE;AAAK,WAAOH,MAAEA,QAAI,IAAE,SAAOH,MAAED,OAAG,EAAEF,IAAE,KAAI,KAAK,GAAG,IAAE,KAAK,IAAIG,KAAED,GAAC,IAAEO,MAAEA;AAAA,EAAC;AAAA,EAAC,yBAAyBT,KAAE;AAAC,WAAO,KAAK,6BAA6B,KAAK,uBAAuBA,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BA,KAAE;AAAC,WAAOA,MAAE,KAAK;AAAA,EAAmB;AAAA,EAAC,yBAAyBA,KAAE;AAAC,WAAO,KAAK,6BAA6B,KAAK,uBAAuBA,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBA,KAAE;AAAC,WAAO,KAAK,IAAIgB,GAAE,KAAK,aAAYb,GAAE,IAAGH,KAAE,KAAK,GAAG,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BA,KAAE;AAAC,WAAOA,MAAE,KAAK;AAAA,EAAmB;AAAA,EAAC,0BAA0BA,KAAEM,KAAE;AAAC,WAAON,MAAE,KAAK,IAAI,KAAK,IAAI,KAAK,MAAK,KAAK,IAAI,KAAG,KAAGM,OAAG,GAAG;AAAA,EAAC;AAAA,EAAC,gBAAgBN,MAAE,EAAE,GAAE;AAAC,WAAOA,IAAE,CAAC,KAAG,KAAK,QAAQ,GAAG,IAAI,IAAE,KAAK,QAAM,KAAG,KAAK,YAAWA,IAAE,CAAC,KAAG,KAAK,QAAQ,GAAG,GAAG,IAAE,KAAK,SAAO,KAAG,KAAK,YAAWA;AAAA,EAAC;AAAA,EAAC,gBAAgBA,KAAEM,MAAE,KAAGH,MAAE,KAAG;AAAC,WAAOH,IAAE,CAAC,IAAE,KAAK,QAAQ,GAAG,IAAI,IAAE,KAAK,QAAMM,KAAEN,IAAE,CAAC,IAAE,KAAK,QAAQ,GAAG,MAAM,IAAE,KAAK,SAAOG,KAAEH,IAAE,CAAC,IAAE,KAAGA;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAE;AAAC,UAAMM,MAAE,KAAK,UAASH,MAAE,KAAK;AAAQ,IAAAH,IAAE,YAAYM,IAAE,CAAC,IAAEH,IAAE,CAAC,GAAEG,IAAE,CAAC,IAAEH,IAAE,CAAC,GAAEG,IAAE,CAAC,IAAEH,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAEG,IAAE,CAAC,IAAEH,IAAE,CAAC,IAAEA,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBH,KAAEM,KAAE;AAAC,IAAAN,QAAI,MAAIE,GAAE,IAAGF,GAAC,GAAE,GAAG,CAAC,IAAE,GAAE,EAAE,IAAG,IAAG,KAAK,gBAAgB;AAAE,UAAMG,MAAE,KAAK,IAAI,GAAG,CAAC,CAAC;AAAE,MAAE,IAAG,IAAG,IAAEA,GAAC;AAAE,UAAMM,MAAE,KAAK;AAAa,IAAAH,IAAE,CAAC,IAAEK,GAAE,GAAEF,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAE,MAAG,MAAG,GAAG,CAAC,CAAC,GAAEH,IAAE,CAAC,IAAEK,GAAE,GAAEF,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAE,MAAG,MAAG,GAAG,CAAC,CAAC,GAAEH,IAAE,CAAC,IAAE,OAAI,GAAG,CAAC,IAAE,IAAGA,IAAE,CAAC,IAAEH;AAAA,EAAC;AAAA,EAAC,kBAAkBH,KAAEM,KAAE;AAAC,UAAMH,MAAE,KAAK;AAAa,OAAG,CAAC,KAAGH,IAAE,CAAC,KAAGG,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,IAAE,KAAGH,IAAE,CAAC,GAAE,GAAG,CAAC,KAAGA,IAAE,CAAC,KAAGG,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,IAAE,KAAGH,IAAE,CAAC,GAAE,GAAG,CAAC,KAAG,IAAEA,IAAE,CAAC,IAAE,KAAGA,IAAE,CAAC,GAAE,GAAG,CAAC,IAAEA,IAAE,CAAC,GAAE,QAAM,KAAK,4BAA0B,EAAE,IAAG,IAAG,KAAK,uBAAuB,GAAEM,IAAE,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAG,CAAC;AAAA,EAAE;AAAA,EAAC,gBAAgBN,KAAEM,KAAE;AAAC,WAAO,KAAK,sBAAsBN,KAAE,EAAE,GAAE,KAAK,eAAe,IAAGM,GAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,sBAAsBN,KAAEM,KAAE;AAAC,QAAG,GAAG,CAAC,IAAEN,IAAE,CAAC,GAAE,GAAG,CAAC,IAAEA,IAAE,CAAC,GAAE,GAAG,CAAC,IAAEA,IAAE,CAAC,GAAE,GAAG,CAAC,IAAE,GAAE,EAAE,IAAG,IAAG,KAAK,oBAAoB,GAAE,MAAI,GAAG,CAAC,EAAE,QAAO;AAAK,MAAE,IAAG,IAAG,IAAE,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC;AAAE,UAAMG,MAAE,KAAK;AAAa,WAAM,OAAMG,OAAGA,IAAE,IAAEK,GAAE,GAAER,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAE,MAAG,MAAG,GAAG,CAAC,CAAC,GAAEG,IAAE,IAAEK,GAAE,GAAER,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAE,MAAG,MAAG,GAAG,CAAC,CAAC,MAAIG,IAAE,CAAC,IAAEK,GAAE,GAAER,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAE,MAAG,MAAG,GAAG,CAAC,CAAC,GAAEG,IAAE,CAAC,IAAEK,GAAE,GAAER,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAE,MAAG,MAAG,GAAG,CAAC,CAAC,GAAEG,IAAE,SAAO,MAAIA,IAAE,CAAC,IAAE,OAAI,GAAG,CAAC,IAAE,MAAKA;AAAA,EAAC;AAAA,EAAC,oBAAoBN,KAAEM,KAAE;AAAC,WAAO,KAAK,0BAA0B,KAAK,eAAeN,KAAE,EAAE,GAAEM,GAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BN,KAAEM,KAAE;AAAC,QAAGE,GAAE,IAAG,KAAK,kBAAiB,KAAK,UAAU,GAAE,CAACG,GAAE,IAAG,EAAE,EAAE,QAAO;AAAK,UAAMR,MAAE,KAAK;AAAa,WAAO,GAAG,CAAC,IAAE,KAAGH,IAAE,CAAC,IAAEG,IAAE,CAAC,KAAGA,IAAE,CAAC,IAAE,GAAE,GAAG,CAAC,IAAE,KAAGH,IAAE,CAAC,IAAEG,IAAE,CAAC,KAAGA,IAAE,CAAC,IAAE,GAAE,GAAG,CAAC,IAAE,IAAEH,IAAE,CAAC,IAAE,GAAE,GAAG,CAAC,IAAE,GAAE,EAAE,IAAG,IAAG,EAAE,GAAE,MAAI,GAAG,CAAC,IAAE,QAAMM,IAAE,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,GAAEA;AAAA,EAAE;AAAA,EAAC,oBAAoBN,KAAEM,KAAEH,KAAED,KAAE;AAAC,UAAMO,MAAET,MAAE,KAAK,YAAWW,MAAEL,MAAE,KAAK,YAAWM,MAAE,KAAK,IAAIH,MAAEN,MAAE,GAAE,CAAC,GAAEF,MAAE,KAAK,IAAI,KAAK,aAAWU,MAAET,MAAE,GAAE,CAAC,GAAEK,MAAE,CAAC,KAAK,IAAIE,MAAEN,MAAE,GAAE,CAAC,GAAEc,MAAE,CAAC,KAAK,IAAI,KAAK,aAAWN,MAAET,MAAE,GAAE,CAAC;AAAE,WAAM,CAACU,KAAEX,KAAEE,MAAEI,MAAG,CAAC,KAAK,IAAI,KAAK,YAAUE,MAAEN,MAAE,GAAE,CAAC,GAAED,MAAEe,MAAG,CAAC,KAAK,IAAIN,MAAET,MAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUF,KAAE;AAAC,IAAAA,QAAIK,IAAE,SAAO,KAAK,iBAAiB,IAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,eAAeL,KAAEM,KAAE;AAAC,UAAMH,MAAEH,IAAE,CAAC,IAAE,KAAK,YAAWE,MAAE,KAAK,aAAWF,IAAE,CAAC,IAAE,KAAK;AAAW,WAAOM,IAAE,CAAC,IAAEH,KAAEG,IAAE,CAAC,IAAEJ,KAAEI;AAAA,EAAC;AAAA,EAAC,eAAeN,KAAEM,KAAE;AAAC,UAAMH,MAAEH,IAAE,CAAC,IAAE,KAAK,YAAWE,OAAG,KAAK,aAAWF,IAAE,CAAC,KAAG,KAAK;AAAW,IAAAM,IAAE,CAAC,IAAEH,KAAEG,IAAE,CAAC,IAAEJ;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,IAAAC,GAAE,IAAG,KAAK,QAAO,KAAK,GAAG;AAAE,UAAMH,MAAES,GAAE,KAAK,MAAM;AAAE,IAAAT,MAAE,KAAGY,GAAE,KAAK,KAAI,GAAE,GAAE,CAAC,GAAE,KAAK,eAAe,GAAE,KAAK,aAAa,KAAK,KAAG,KAAK,IAAI,EAAE,IAAG,KAAK,MAAM,CAAC,IAAE,SAAMH,GAAE,EAAE,IAAET,QAAI,EAAE,KAAK,KAAI,IAAG,KAAK,MAAM,GAAE,EAAE,KAAK,KAAI,KAAK,KAAI,EAAE,GAAEkB,GAAE,KAAK,KAAI,KAAK,GAAG,GAAE,KAAK,aAAa,KAAK,GAAE,KAAK,eAAe;AAAA,EAAE;AAAA,EAAC,kBAAiB;AAAC,MAAE,IAAG,KAAK,KAAI,KAAK,MAAM,GAAE,KAAK,IAAI,GAAG,CAAC,CAAC,KAAG,WAAQ,EAAE,IAAG,IAAG,GAAG,CAAC,CAAC,GAAEN,GAAE,KAAK,KAAI,CAAC,GAAG,CAAC,GAAE,CAAC,GAAG,CAAC,GAAE,IAAE,GAAG,CAAC,CAAC,GAAEM,GAAE,KAAK,KAAI,KAAK,GAAG,GAAE,KAAK,aAAa,KAAK,GAAE,KAAK,eAAe;AAAA,EAAE;AAAA,EAAC,mBAAmBlB,KAAEM,KAAEJ,MAAE,IAAG;AAAC,gBAAU,OAAOF,IAAE,CAAC,KAAG,SAASA,IAAE,CAAC,CAAC,KAAG,YAAU,OAAOA,IAAE,CAAC,KAAG,SAASA,IAAE,CAAC,CAAC,KAAG,YAAU,OAAOA,IAAE,CAAC,KAAG,SAASA,IAAE,CAAC,CAAC,IAAE,EAAEA,KAAEM,GAAC,MAAIJ,GAAEI,KAAEN,GAAC,GAAE,KAAK,eAAe,GAAEE,IAAE,UAAQ,KAAK,aAAaA,GAAC,KAAG,EAAE,UAAU,uCAAuC,EAAE,KAAK,uDAAuD;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,SAAK,aAAW,MAAG,KAAK,gBAAc,MAAG,KAAK,uBAAqB;AAAA,EAAE;AAAA,EAAC,oBAAmB;AAAC,SAAK,kBAAgBO,IAAE,KAAK,YAAW,KAAK,kBAAiB,KAAK,QAAQ,GAAE,KAAK,gBAAc;AAAA,EAAG;AAAA,EAAC,mBAAkB;AAAC,SAAK,eAAa,EAAE,KAAK,aAAY,KAAK,KAAI,KAAK,QAAO,KAAK,EAAE,GAAEG,GAAE,KAAK,cAAa,CAAC,KAAK,YAAY,CAAC,GAAE,CAAC,KAAK,YAAY,CAAC,GAAE,CAAC,KAAK,YAAY,EAAE,CAAC,GAAEA,GAAE,KAAK,SAAQ,KAAK,YAAY,CAAC,GAAE,KAAK,YAAY,CAAC,GAAE,KAAK,YAAY,CAAC,CAAC,GAAEA,GAAE,KAAK,YAAW,KAAK,YAAY,CAAC,GAAE,KAAK,YAAY,CAAC,GAAE,KAAK,YAAY,CAAC,CAAC,GAAE,KAAK,aAAW,OAAG,KAAK,mCAAiC;AAAA,EAAG;AAAC;AAAE,EAAE,CAACO,GAAE,CAAC,GAAErB,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,MAAK,IAAI,GAAE,EAAE,CAACqB,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAErB,GAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAACqB,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAErB,GAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,KAAI,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,KAAI,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAACqB,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAErB,GAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAACqB,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAErB,GAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAACqB,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAErB,GAAE,WAAU,2BAA0B,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAACqB,GAAE,CAAC,GAAErB,GAAE,WAAU,QAAO,IAAI,GAAEA,KAAED,KAAE,EAAE,CAACU,GAAE,uCAAuC,CAAC,GAAET,EAAC;AAAE,IAAM,KAAGG,GAAE;AAAX,IAAa,KAAGE,GAAE;AAAlB,IAAoB,KAAGF,GAAE;AAAzB,IAA2B,KAAGA,GAAE;AAAhC,IAAkC,KAAGmB,GAAE;AAAE,IAAI;AAAG,CAAC,SAASpB,KAAE;AAAC,EAAAA,IAAEA,IAAE,MAAI,CAAC,IAAE,OAAMA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,IAAEA,IAAE,OAAK,CAAC,IAAE;AAAM,EAAE,OAAK,KAAG,CAAC,EAAE;;;ACA/wZ,IAAMqB,KAAE,oBAAI,IAAI,CAAC,CAACC,GAAE,UAAS,CAAC,GAAE,CAACA,GAAE,mBAAkB,CAAC,GAAE,CAACA,GAAE,KAAI,CAAC,GAAE,CAACA,GAAE,SAAQ,CAAC,GAAE,CAACA,GAAE,SAAQ,CAAC,GAAE,CAACA,GAAE,OAAM,CAAC,GAAE,CAACA,GAAE,uBAAsB,CAAC,GAAE,CAACA,GAAE,MAAK,CAAC,GAAE,CAACA,GAAE,sBAAqB,CAAC,GAAE,CAACA,GAAE,yBAAwB,CAAC,GAAE,CAACA,GAAE,uBAAsB,CAAC,CAAC,CAAC;AAAE,IAAMC,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,kBAAkBA,KAAE;AAAC,WAAO,IAAIC,IAAED,IAAE,MAAK,GAAE,OAAO,IAAI,EAAE,MAAM,KAAK,aAAa,GAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBK,KAAEC,KAAE;AAAC,UAAMC,MAAE,KAAK,eAAcC,MAAEH,QAAID,IAAE,MAAKK,MAAEJ,QAAID,IAAE;AAAU,WAAO,EAAE,EAAC,UAASG,IAAE,WAASG,GAAE,SAAOH,IAAE,WAASG,GAAE,QAAMF,MAAED,KAAEI,GAAEN,GAAC,IAAE,MAAK,WAAU,EAAC,MAAKO,IAAEP,GAAC,EAAC,GAAE,YAAWG,MAAED,IAAE,aAAWC,MAAE,OAAKK,GAAER,GAAC,GAAE,YAAWS,IAAE,cAAaP,IAAE,eAAaJ,MAAE,MAAK,aAAYI,IAAE,eAAaD,MAAEF,MAAEW,KAAE,MAAK,eAAcP,OAAGC,MAAEF,IAAE,mBAAiBO,KAAE,OAAKA,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMX,MAAE,KAAK;AAAc,QAAGA,IAAE,UAAS;AAAC,YAAME,MAAEF,IAAE,mBAAiBW,KAAE;AAAK,WAAK,+BAA6B,EAAE,EAAC,UAASP,IAAE,eAAcF,KAAE,WAAUW,IAAE,YAAW,MAAK,YAAWF,IAAE,cAAa,MAAK,aAAYG,GAAC,CAAC,GAAE,KAAK,0BAAwB,EAAE,EAAC,UAASV,IAAE,eAAcF,KAAE,WAAUW,IAAE,YAAW,MAAK,YAAWF,IAAE,cAAaF,KAAE,aAAYM,GAAC,CAAC,GAAE,KAAK,6BAA2B,EAAE,EAAC,UAAS,MAAK,eAAcb,KAAE,WAAUC,IAAE,YAAW,MAAK,YAAW,MAAK,cAAaH,KAAE,aAAYC,IAAC,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK,yBAAuB,KAAK,mBAAmB,KAAK,cAAc,sBAAqB,IAAE,GAAE,KAAK,mBAAmB,KAAK,cAAc,sBAAqB,KAAE;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK,cAAc,YAAUS,GAAE,QAAMA,GAAE;AAAA,EAAc;AAAA,EAAC,iBAAiBV,KAAEE,KAAE;AAAC,WAAOA,MAAE,KAAK,yBAAuB,KAAK,cAAc,WAASF,QAAIU,GAAE,gCAA8B,KAAK,+BAA6BV,QAAIU,GAAE,oBAAkB,KAAK,0BAAwB,KAAK,6BAA2B,MAAM,iBAAiBV,KAAEE,GAAC;AAAA,EAAC;AAAC;AAACH,GAAE,SAAO,IAAIG,GAAEM,IAAG,MAAI,OAAO,+BAAsB,CAAE;AAAE,IAAMG,KAAE,EAAC,QAAO,GAAE,OAAM,GAAE;;;ACAjuC,IAAIK;AAAE,CAAC,SAASC,KAAE;AAAC,EAAAA,IAAEA,IAAE,kBAAgB,EAAE,IAAE,mBAAkBA,IAAEA,IAAE,gBAAc,EAAE,IAAE,iBAAgBA,IAAEA,IAAE,iBAAe,EAAE,IAAE,kBAAiBA,IAAEA,IAAE,eAAa,EAAE,IAAE,gBAAeA,IAAEA,IAAE,mBAAiB,CAAC,IAAE,oBAAmBA,IAAEA,IAAE,iBAAe,CAAC,IAAE,kBAAiBA,IAAEA,IAAE,kBAAgB,CAAC,IAAE,mBAAkBA,IAAEA,IAAE,gBAAc,CAAC,IAAE;AAAe,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAME,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYF,KAAE;AAAC,UAAMA,KAAE,IAAIG,IAAC,GAAE,KAAK,iBAAe,IAAIC,OAAE,KAAK,4BAA0BC,IAAE,KAAK,UAAQ,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,SAASL,KAAEM,KAAE;AAAC,WAAOC,GAAE,KAAK,YAAWP,KAAEM,GAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBN,KAAEM,KAAE;AAAC,SAAK,eAAe,SAAON,KAAE,KAAK,eAAe,SAAOM,IAAE,SAAOE,GAAE;AAAgB,UAAMC,MAAE,EAAE,KAAK,WAAW,cAAc,KAAGT,QAAIE,GAAE;AAAU,WAAO,KAAK,eAAe,iBAAeO,KAAE,KAAK,eAAe,yBAAuBA,OAAG,EAAE,KAAK,WAAW,eAAe,GAAE,KAAK,eAAe,4BAA0BA,OAAG,KAAK,WAAW,2BAA0B,KAAK,eAAe,0BAAwBA,OAAG,KAAK,WAAW,yBAAwB,KAAK,eAAe,gBAAc,KAAK,WAAW,eAAc,KAAK,eAAe,eAAa,KAAK,WAAW,cAAa,KAAK,eAAe,aAAW,YAAU,KAAK,WAAW,MAAK,KAAK,eAAe,UAAQ,KAAK,WAAW,KAAI,KAAK,eAAe,oBAAkB,CAAC,CAAC,EAAE,KAAK,WAAW,gBAAgB,KAAGC,GAAE,KAAK,WAAW,gBAAgB,GAAE,KAAK,eAAe,mBAAiB,KAAK,WAAW,kBAAiB,KAAK,eAAe,aAAW,KAAK,WAAW,YAAW,KAAK,eAAe,UAAQ,KAAK,WAAW,gBAAe,KAAK,eAAe,YAAU,KAAK,WAAW,kBAAiB,KAAK,eAAe,SAAO,KAAK,WAAW,eAAc,KAAK,eAAe,oBAAkB,KAAK,WAAW,aAAW,KAAG,EAAE,KAAK,WAAW,UAAU,GAAE,KAAK,eAAe,iBAAe,KAAK,WAAW,UAAQ,GAAE,KAAK,eAAe,WAAS,KAAK,WAAW,mBAAiBC,GAAE,8BAA6B,KAAK,eAAe,uBAAqBL,IAAE,sBAAqB,KAAK,eAAe,sBAAoBA,IAAE,iBAAiB,SAAQ,KAAK,eAAe,kBAAgBA,IAAE,iBAAiB,iBAAgB,KAAK,eAAe,YAAU,KAAK,WAAW,WAAU,KAAK;AAAA,EAAc;AAAA,EAAC,gBAAgBN,KAAEY,KAAEH,KAAEL,KAAES,KAAEC,KAAE;AAAC,QAAG,CAACL,IAAE,QAAQ,cAAc;AAAO,UAAMM,MAAEf,IAAE,iBAAiB,IAAIgB,GAAE,QAAQ,EAAE,MAAKC,MAAEjB,IAAE,iBAAiB,IAAIgB,GAAE,IAAI;AAAE,QAAIL,MAAE,KAAK,WAAW;AAAM,QAAG,KAAK,WAAW,eAAc;AAAC,YAAMC,MAAEZ,IAAE,iBAAiB,IAAIgB,GAAE,oBAAoB,EAAE,KAAK,CAAC;AAAE,MAAAL,OAAGE,GAAE,KAAK,WAAW,aAAa,CAAC,IAAED,MAAE,KAAK,WAAW,aAAa,CAAC,GAAE,KAAK,WAAW,cAAc,CAAC,GAAE,KAAK,WAAW,cAAc,CAAC,CAAC;AAAA,IAAC,MAAM,CAAAK,QAAIN,OAAGM,IAAE,KAAK,CAAC;AAAG,UAAMf,MAAEE,IAAE,CAAC,GAAEc,MAAEd,IAAE,CAAC,GAAEe,OAAGR,MAAE,IAAE,KAAGX,IAAE;AAAmB,QAAIoB,KAAE,OAAO,WAAUC,MAAE;AAAE,aAAQC,KAAE,GAAEA,KAAEP,IAAE,SAAO,GAAEO,MAAG,GAAE;AAAC,YAAMtB,MAAEe,IAAEO,EAAC,GAAEV,MAAEG,IAAEO,KAAE,CAAC,GAAEb,MAAEP,MAAEF,KAAEI,MAAEc,MAAEN,KAAEC,MAAEE,IAAEO,KAAE,CAAC,IAAEtB,KAAEc,MAAEC,IAAEO,KAAE,CAAC,IAAEV,KAAEK,MAAEJ,IAAGA,MAAEJ,MAAEK,MAAEV,QAAIS,MAAEA,MAAEC,MAAEA,MAAG,GAAE,CAAC,GAAEH,MAAEE,MAAEI,MAAER,KAAEU,MAAEL,MAAEG,MAAEb,KAAEmB,MAAEZ,MAAEA,MAAEQ,MAAEA;AAAE,MAAAI,MAAEH,OAAIA,KAAEG,KAAEF,MAAEC,KAAE;AAAA,IAAE;AAAC,IAAAF,KAAED,MAAEA,OAAGN,IAAEC,IAAE,MAAKA,IAAE,QAAOO,KAAE,KAAE;AAAA,EAAC;AAAA,EAAC,UAAUT,KAAEH,KAAEW,IAAEC,KAAEC,IAAEE,KAAE;AAAC,QAAG,CAACJ,GAAE,QAAQ,iBAAe,CAACR,IAAE,QAAQ;AAAO,QAAG,CAACK,IAAER,GAAC,EAAE,QAAO,KAAK,EAAE,UAAU,yDAAyD,EAAE,MAAM,gDAAgD;AAAE,UAAMgB,KAAEb,IAAE,kBAAiBI,MAAES,GAAE,IAAIT,GAAE,QAAQ,EAAE;AAAK,QAAIU,KAAE,KAAK,WAAW;AAAM,QAAG,KAAK,WAAW,eAAc;AAAC,YAAM1B,MAAEyB,GAAE,IAAIT,GAAE,oBAAoB,EAAE,KAAK,CAAC;AAAE,MAAAU,MAAGb,GAAE,KAAK,WAAW,aAAa,CAAC,IAAEb,MAAE,KAAK,WAAW,aAAa,CAAC,GAAE,KAAK,WAAW,cAAc,CAAC,GAAE,KAAK,WAAW,cAAc,CAAC,CAAC;AAAA,IAAC,MAAM,CAAAyB,GAAE,IAAIT,GAAE,IAAI,MAAIU,MAAGD,GAAE,IAAIT,GAAE,IAAI,EAAE,KAAK,CAAC;AAAG,UAAMW,KAAEP,GAAE,QAAOQ,KAAEC;AAAE,IAAAhB,GAAEe,IAAER,GAAE,KAAK;AAAE,UAAMU,KAAEJ,KAAEC,GAAE,aAAW,IAAE,IAAEA,GAAE;AAAW,IAAAZ,GAAE,GAAG,CAAC,GAAEa,GAAE,CAAC,IAAEE,IAAEF,GAAE,CAAC,IAAEE,IAAE,CAAC,GAAEf,GAAE,GAAG,CAAC,GAAEa,GAAE,CAAC,IAAEE,IAAEF,GAAE,CAAC,IAAEE,IAAE,CAAC,GAAEf,GAAE,GAAG,CAAC,GAAEa,GAAE,CAAC,IAAEE,IAAEF,GAAE,CAAC,IAAEE,IAAE,CAAC,GAAEf,GAAE,GAAG,CAAC,GAAEa,GAAE,CAAC,IAAEE,IAAEF,GAAE,CAAC,IAAEE,IAAE,CAAC;AAAE,aAAQ9B,MAAE,GAAEA,MAAE,GAAEA,MAAI,KAAG,CAAC2B,GAAE,0BAA0B,GAAG3B,GAAC,GAAE,GAAGA,GAAC,CAAC,EAAE;AAAO,IAAA+B,GAAEJ,GAAE,KAAI,GAAG,CAAC,GAAE,GAAG,CAAC,GAAE,EAAE,GAAEI,GAAEJ,GAAE,KAAI,GAAG,CAAC,GAAE,GAAG,CAAC,GAAE,EAAE,GAAEI,GAAEJ,GAAE,KAAI,GAAG,CAAC,GAAE,GAAG,CAAC,GAAE,EAAE,GAAEI,GAAEJ,GAAE,KAAI,GAAG,CAAC,GAAE,GAAG,CAAC,GAAE,EAAE;AAAE,QAAIK,KAAE,OAAO,WAAUC,KAAE;AAAE,UAAMC,KAAEC,GAAE,KAAK,YAAWV,IAAEb,IAAE,OAAO,IAAEI,IAAE,SAAO,IAAEA,IAAE,SAAO;AAAE,aAAQhB,MAAE,GAAEA,MAAEkC,IAAElC,OAAG,GAAE;AAAC,MAAAoC,GAAE,CAAC,IAAEpB,IAAEhB,GAAC,IAAES,IAAE,EAAE,GAAE2B,GAAE,CAAC,IAAEpB,IAAEhB,MAAE,CAAC,IAAES,IAAE,EAAE,GAAE2B,GAAE,CAAC,IAAEpB,IAAEhB,MAAE,CAAC,IAAES,IAAE,EAAE;AAAE,YAAMH,OAAGN,MAAE,KAAGgB,IAAE;AAAO,UAAGqB,GAAE,CAAC,IAAErB,IAAEV,GAAC,IAAEG,IAAE,EAAE,GAAE4B,GAAE,CAAC,IAAErB,IAAEV,MAAE,CAAC,IAAEG,IAAE,EAAE,GAAE4B,GAAE,CAAC,IAAErB,IAAEV,MAAE,CAAC,IAAEG,IAAE,EAAE,GAAE0B,GAAE,IAAGC,EAAC,IAAE,KAAGD,GAAE,IAAGE,EAAC,IAAE,KAAGF,GAAE,IAAGC,EAAC,IAAE,KAAGD,GAAE,IAAGE,EAAC,IAAE,KAAGF,GAAE,IAAGC,EAAC,IAAE,KAAGD,GAAE,IAAGE,EAAC,IAAE,KAAGF,GAAE,IAAGC,EAAC,IAAE,KAAGD,GAAE,IAAGE,EAAC,IAAE,EAAE;AAAS,UAAGV,GAAE,sBAAsBS,IAAEE,GAAE,GAAEX,GAAE,sBAAsBU,IAAE,EAAE,GAAEC,IAAG,CAAC,IAAE,KAAG,GAAG,CAAC,IAAE,GAAE;AAAC,QAAAtC,GAAEuC,IAAEH,IAAEC,EAAC;AAAE,cAAMrC,MAAE2B,GAAE,SAAQrB,MAAE,CAAC6B,GAAEnC,IAAE,EAAE,IAAI,GAAEoC,EAAC,IAAE,EAAEG,IAAEF,GAAErC,IAAE,EAAE,IAAI,CAAC,CAAC;AAAE,UAAEuC,IAAEA,IAAEjC,GAAC,GAAEY,GAAEkB,IAAEA,IAAEG,EAAC,GAAEZ,GAAE,sBAAsBS,IAAEE,GAAE;AAAA,MAAC,WAASA,IAAG,CAAC,IAAE,KAAG,GAAG,CAAC,IAAE,GAAE;AAAC,QAAAtC,GAAEuC,IAAEF,IAAED,EAAC;AAAE,cAAMpC,MAAE2B,GAAE,SAAQrB,MAAE,CAAC6B,GAAEnC,IAAE,EAAE,IAAI,GAAEqC,EAAC,IAAE,EAAEE,IAAEF,GAAErC,IAAE,EAAE,IAAI,CAAC,CAAC;AAAE,UAAEuC,IAAEA,IAAEjC,GAAC,GAAEY,GAAEmB,IAAEA,IAAEE,EAAC,GAAEZ,GAAE,sBAAsBU,IAAE,EAAE;AAAA,MAAC,WAASC,IAAG,CAAC,IAAE,KAAG,GAAG,CAAC,IAAE,EAAE;AAAS,MAAAA,IAAG,CAAC,IAAE,GAAE,GAAG,CAAC,IAAE;AAAE,YAAM1B,MAAE4B,GAAEd,GAAEY,KAAG,IAAG,EAAE,GAAEV,EAAC;AAAE,MAAAhB,MAAEoB,OAAIA,KAAEpB,KAAEA,GAAE,IAAGwB,EAAC,GAAExB,GAAE,IAAGyB,EAAC,GAAEJ,KAAEjC,MAAE;AAAA,IAAE;AAAC,UAAMyC,KAAErB,GAAE,UAASsB,MAAEtB,GAAE;AAAO,QAAGY,KAAEF,KAAEA,IAAE;AAAC,UAAI9B,MAAE,OAAO;AAAU,UAAG2C,GAAEjB,GAAE,IAAG,IAAG,EAAE,GAAEA,GAAEe,IAAEC,KAAE,EAAE,GAAE,CAAC,GAAE;AAAC,QAAA1C,GAAE,GAAE,GAAEyC,EAAC;AAAE,cAAMnC,MAAEF,GAAE,CAAC;AAAE,UAAE,GAAE,GAAE,IAAEE,GAAC,GAAEN,MAAEM,MAAE,EAAEmC,IAAEC,GAAC;AAAA,MAAC;AAAC,MAAAlB,IAAExB,KAAE,GAAEiC,IAAE,KAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,UAAMjC,MAAEsB,GAAE,EAAE,MAAMN,GAAE,QAAQ,EAAE,IAAIA,GAAE,iBAAiB,EAAE,MAAMA,GAAE,GAAG,EAAE,MAAMA,GAAE,OAAO,EAAE,MAAMA,GAAE,OAAO;AAAE,WAAO,KAAK,WAAW,gBAAchB,IAAE,IAAIgB,GAAE,oBAAoB,IAAEhB,IAAE,IAAIgB,GAAE,IAAI,GAAE,KAAK,WAAW,iBAAehB,IAAE,IAAIgB,GAAE,qBAAqB,IAAEhB,IAAE,MAAMgB,GAAE,KAAK,GAAE,KAAK,WAAW,oBAAkBhB,IAAE,IAAIgB,GAAE,uBAAuB,GAAE,IAAI,2CAA2C,KAAGhB,IAAE,OAAOgB,GAAE,qBAAqB,GAAEhB;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,IAAI2C,GAAE,KAAK,SAAQ,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,aAAa3C,KAAEM,KAAE;AAAC,QAAGA,QAAIJ,GAAE,SAAOI,QAAIJ,GAAE,SAAOI,QAAIJ,GAAE,aAAWI,QAAIJ,GAAE,SAAOI,QAAIJ,GAAE,uBAAsB;AAAC,UAAGF,QAAIQ,GAAE,gBAAgB,QAAM;AAAG,UAAG,KAAK,WAAW,mBAAiBG,GAAE,6BAA6B,QAAOX,QAAIQ,GAAE,mBAAiBR,QAAIQ,GAAE,qBAAmBR,QAAIQ,GAAE;AAA8B,UAAGF,QAAIJ,GAAE,SAAOI,QAAIJ,GAAE,OAAM;AAAC,eAAOF,SAAK,KAAK,WAAW,aAAWQ,GAAE,uBAAqBA,GAAE;AAAA,MAA0C;AAAC,aAAOR,QAAIQ,GAAE;AAAA,IAAe;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,iBAAiBR,KAAE;AAAC,WAAO,IAAI4C,GAAE5C,GAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,KAAE;AAAC,gBAAUA,IAAE,SAAOA,IAAE,aAAW,IAAG,EAAEA,IAAE,gBAAgB,MAAIA,IAAE,cAAYA,IAAE,iBAAiB,QAAMA,IAAE;AAAA,EAAM;AAAC;AAAC,IAAM4C,KAAN,cAAgBtC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,kBAAgB;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,0BAA0B,QAAQ,KAAK,eAAe,GAAE,KAAK,kBAAgB;AAAA,EAAI;AAAA,EAAC,qBAAqBN,KAAE;AAAC,IAAAA,IAAE,iBAAe,KAAK,UAAU,WAAW,gBAAc,KAAK,UAAU,cAAc,EAAC,cAAaA,IAAE,aAAY,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,SAAK,YAAUE,GAAE,SAAO,KAAK,YAAUA,GAAE,SAAO,KAAK,qBAAqBF,GAAC;AAAE,UAAMM,MAAE,KAAK,UAAU,WAAW;AAAe,WAAO,KAAK,oBAAkBA,QAAI,KAAK,UAAU,cAAc,KAAK,0BAA0B,KAAK,KAAK,iBAAgBA,GAAC,CAAC,GAAE,KAAK,kBAAgBA,MAAG,KAAK,gBAAgBuC,IAAE7C,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMG,KAAN,cAAgB2C,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAM,GAAE,KAAK,QAAMtB,IAAE,KAAK,OAAK,SAAQ,KAAK,MAAIZ,IAAE,MAAK,KAAK,aAAW,GAAE,KAAK,aAAW,MAAG,KAAK,mBAAiB,OAAG,KAAK,iBAAe,MAAK,KAAK,4BAA0B,OAAG,KAAK,0BAAwB,MAAG,KAAK,mBAAiB,MAAK,KAAK,cAAY,GAAE,KAAK,gBAAc,OAAG,KAAK,eAAa,OAAG,KAAK,WAAS,OAAG,KAAK,UAAQ,GAAE,KAAK,aAAW,GAAE,KAAK,eAAa,OAAG,KAAK,YAAU;AAAA,EAAE;AAAC;AAAC,IAAM+B,KAAN,MAAO;AAAA,EAAC,YAAY3C,KAAEM,KAAE;AAAC,SAAK,cAAYA,KAAE,KAAK,sBAAoB,GAAE,KAAK,qBAAmBN;AAAE,UAAMY,MAAEN,IAAE,iBAAe,IAAE;AAAE,YAAO,KAAK,YAAY,MAAK;AAAA,MAAC,KAAI;AAAA,MAAQ,KAAI;AAAQ,aAAK,sBAAoBM;AAAE;AAAA,MAAM,KAAI;AAAQ,aAAK,sBAAoBmB,KAAEnB;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAUZ,KAAE;AAAC,WAAOmC,GAAE,KAAK,aAAYnC,IAAE,kBAAiBA,IAAE,OAAO;AAAA,EAAC;AAAA,EAAC,SAASA,KAAE;AAAC,WAAO,KAAK,mBAAmB,aAAaA,GAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAE;AAAC,UAAMM,MAAE,GAAEM,MAAEZ,IAAE,QAAQ,IAAIgB,GAAE,QAAQ,EAAE,SAAO,IAAE,GAAEP,MAAE,KAAK,UAAUT,GAAC;AAAE,QAAII,MAAEK,MAAE,IAAE,IAAEH;AAAE,WAAOF,SAAKK,MAAEG,MAAEA,MAAE,MAAIH,MAAE,IAAE,OAAK,IAAE,KAAK,sBAAoB,IAAGL,OAAG,GAAE,KAAK,YAAY,cAAYA,MAAE,IAAE,KAAGA,MAAE,KAAIA;AAAA,EAAC;AAAA,EAAC,MAAMJ,KAAEM,KAAEG,KAAEL,KAAEU,KAAE;AAJt/R;AAIu/R,UAAMC,MAAE,IAAGE,MAAE,IAAGN,MAAE,IAAGO,MAAET,IAAE,iBAAiB,IAAIO,GAAE,QAAQ,EAAE,MAAKK,MAAEZ,IAAE,WAASA,IAAE,QAAQ,IAAIO,GAAE,QAAQ,GAAEM,MAAE,KAAAb,IAAE,iBAAiB,IAAIO,GAAE,eAAe,MAAxC,mBAA2C;AAAK,IAAAK,OAAGA,IAAE,WAAS,KAAGH,IAAE,SAAO,IAAE,MAAI,QAAQ,KAAK,6CAA6C;AAAE,QAAIK,MAAE,GAAEC,MAAE;AAAE,SAAK,YAAY,gBAAcA,MAAEf,IAAE,iBAAiB,IAAIO,GAAE,oBAAoB,EAAE,KAAK,CAAC,IAAEP,IAAE,iBAAiB,IAAIO,GAAE,IAAI,MAAIO,MAAEd,IAAE,iBAAiB,IAAIO,GAAE,IAAI,EAAE,KAAK,CAAC;AAAG,QAAIR,MAAE,CAAC,GAAE,GAAE,GAAE,CAAC,GAAEuC,MAAE;AAAE,SAAK,YAAY,iBAAeA,MAAEtC,IAAE,iBAAiB,IAAIO,GAAE,qBAAqB,EAAE,KAAK,CAAC,IAAEP,IAAE,iBAAiB,IAAIO,GAAE,KAAK,MAAIR,MAAEC,IAAE,iBAAiB,IAAIO,GAAE,KAAK,EAAE;AAAM,UAAM8B,MAAE,IAAI,2CAA2C,IAAErC,IAAE,wBAAsB;AAAK,QAAIgB,KAAE;AAAE,SAAK,YAAY,qBAAmBA,KAAEhB,IAAE,iBAAiB,IAAIO,GAAE,uBAAuB,EAAE,KAAK,CAAC;AAAG,UAAMgC,KAAE9B,IAAE,SAAO,GAAEb,KAAE,IAAI,aAAaD,IAAE,MAAM,GAAE6C,KAAE,IAAI,2CAA2C,IAAE,IAAI,WAAW7C,IAAE,MAAM,IAAE,MAAKY,MAAE,KAAK,mBAAmB,SAAO;AAAE,QAAIU,KAAEZ,MAAEE;AAAE,UAAMW,KAAED;AAAE,QAAIE,KAAE;AAAE,UAAME,KAAER,KAAE,CAACtB,KAAEM,KAAEM,QAAIgB,KAAEN,GAAEV,GAAC,IAAE,CAACZ,KAAEM,KAAEM,QAAIgB,MAAG,EAAE5B,KAAEM,GAAC,GAAE0B,KAAE,IAAI,2CAA2C,GAAEa,KAAE,CAAC7C,KAAEM,KAAEG,KAAEL,KAAES,KAAEC,KAAEC,QAAI;AAAC,UAAGV,GAAEqB,IAAG,IAAEpB,IAAE,CAAC,GAAED,GAAEqB,IAAG,IAAEpB,IAAE,CAAC,GAAED,GAAEqB,IAAG,IAAEpB,IAAE,CAAC,GAAED,GAAEqB,IAAG,IAAEtB,KAAEC,GAAEqB,IAAG,IAAEX,KAAEV,GAAEqB,IAAG,IAAEb,KAAER,GAAEqB,IAAG,IAAE1B,IAAE,CAAC,GAAEK,GAAEqB,IAAG,IAAE1B,IAAE,CAAC,GAAEK,GAAEqB,IAAG,IAAE1B,IAAE,CAAC,GAAEK,GAAEqB,IAAG,IAAEjB,IAAE,CAAC,GAAEJ,GAAEqB,IAAG,IAAEjB,IAAE,CAAC,GAAEJ,GAAEqB,IAAG,IAAEjB,IAAE,CAAC,GAAE,KAAK,YAAY,gBAAcJ,GAAEqB,IAAG,IAAEF,MAAEnB,GAAEqB,IAAG,IAAEH,KAAE,KAAK,YAAY,eAAe,CAAAlB,GAAEqB,IAAG,IAAEqB;AAAA,WAAM;AAAC,cAAM/C,MAAE,KAAK,IAAI,IAAEc,KAAEN,IAAE,SAAO,CAAC;AAAE,QAAAH,GAAEqB,IAAG,IAAElB,IAAER,GAAC,GAAEK,GAAEqB,IAAG,IAAElB,IAAER,MAAE,CAAC,GAAEK,GAAEqB,IAAG,IAAElB,IAAER,MAAE,CAAC,GAAEK,GAAEqB,IAAG,IAAElB,IAAER,MAAE,CAAC;AAAA,MAAC;AAAC,WAAK,YAAY,qBAAmBK,GAAEqB,IAAG,IAAED,KAAGO,OAAI,EAAEc,GAAC,MAAIG,GAAE,IAAEvB,EAAC,IAAEoB,IAAE,CAAC,GAAEG,GAAE,IAAEvB,KAAE,CAAC,IAAEoB,IAAE,CAAC,GAAEG,GAAE,IAAEvB,KAAE,CAAC,IAAEoB,IAAE,CAAC,GAAEG,GAAE,IAAEvB,KAAE,CAAC,IAAEoB,IAAE,CAAC,IAAGpB;AAAA,IAAI;AAAE,IAAAA,MAAGV,KAAED,GAAEE,KAAEC,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,GAAElB,OAAGgB,GAAEC,KAAEA,KAAEjB,GAAC;AAAE,UAAMiC,KAAE,KAAK,UAAUxB,GAAC;AAAE,QAAGwB,IAAE;AAAC,YAAM3B,MAAEY,IAAE,SAAO;AAAE,MAAAH,GAAEA,KAAEG,IAAEZ,GAAC,GAAEY,IAAEZ,MAAE,CAAC,GAAEY,IAAEZ,MAAE,CAAC,CAAC,GAAEN,OAAGgB,GAAED,KAAEA,KAAEf,GAAC;AAAA,IAAC,MAAM,CAAAe,GAAEJ,KAAEO,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,GAAElB,OAAGgB,GAAEL,KAAEA,KAAEX,GAAC,GAAE6C,GAAE5B,KAAEA,KAAEN,KAAE,GAAEZ,GAAE,gBAAe,GAAE,CAAC,GAAE8C,GAAE5B,KAAEA,KAAEN,KAAE,GAAEZ,GAAE,iBAAgB,GAAE,CAAC,GAAEa,GAAEG,KAAEE,GAAC,GAAEL,GAAEK,KAAEN,GAAC;AAAE,UAAMuB,KAAED,KAAE,IAAE,GAAEQ,KAAER,KAAEe,KAAEA,KAAE;AAAE,aAAQpC,MAAEsB,IAAEtB,MAAE6B,IAAE7B,OAAI;AAAC,YAAMN,OAAGM,MAAE,KAAGoC,KAAE;AAAE,MAAAjC,GAAEJ,KAAEO,IAAEZ,GAAC,GAAEY,IAAEZ,MAAE,CAAC,GAAEY,IAAEZ,MAAE,CAAC,CAAC,GAAEN,OAAGgB,GAAEL,KAAEA,KAAEX,GAAC,GAAE8B,GAAEf,KAAEE,KAAEL,GAAC,GAAEiC,GAAE9B,KAAEE,KAAEN,KAAE,GAAEZ,GAAE,eAAca,KAAEgB,EAAC,GAAEiB,GAAE9B,KAAEE,KAAEN,KAAE,GAAEZ,GAAE,gBAAea,KAAEgB,EAAC;AAAE,YAAMnB,MAAE,KAAK;AAAoB,eAAQT,MAAE,GAAEA,MAAES,KAAE,EAAET,KAAE;AAAC,cAAMM,OAAGN,MAAE,MAAIS,MAAE;AAAG,QAAAoC,GAAE9B,KAAEE,KAAEN,KAAEL,KAAEP,GAAE,eAAca,KAAEgB,EAAC,GAAEiB,GAAE9B,KAAEE,KAAEN,KAAEL,KAAEP,GAAE,gBAAea,KAAEgB,EAAC;AAAA,MAAC;AAAC,MAAAiB,GAAE9B,KAAEE,KAAEN,KAAE,GAAEZ,GAAE,iBAAgBa,KAAEgB,EAAC,GAAEiB,GAAE9B,KAAEE,KAAEN,KAAE,GAAEZ,GAAE,kBAAiBa,KAAEgB,EAAC,GAAEhB,GAAEG,KAAEE,GAAC,GAAEL,GAAEK,KAAEN,GAAC;AAAA,IAAC;AAAC,IAAAsB,MAAGlB,GAAEJ,KAAEO,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,GAAElB,OAAGgB,GAAEL,KAAEA,KAAEX,GAAC,GAAE4B,KAAEE,GAAEf,KAAEE,KAAEwB,EAAC,GAAEI,GAAE9B,KAAEE,KAAEN,KAAE,GAAEZ,GAAE,eAAcmC,IAAEN,EAAC,GAAEiB,GAAE9B,KAAEE,KAAEN,KAAE,GAAEZ,GAAE,gBAAemC,IAAEN,EAAC,MAAIA,KAAEE,GAAEf,KAAEE,KAAEwB,EAAC,GAAEI,GAAE9B,KAAEE,KAAEA,KAAE,GAAElB,GAAE,cAAa0C,IAAEb,EAAC,GAAEiB,GAAE9B,KAAEE,KAAEA,KAAE,GAAElB,GAAE,eAAc0C,IAAEb,EAAC,IAAGsB,GAAE7C,IAAEsB,KAAEX,KAAEX,IAAEsB,IAAEX,GAAC;AAAE,IAAAU,KAAEwB,GAAE7C,IAAEqB,KAAEV,KAAEX,IAAEqB,IAAEV,GAAC,GAAE,KAAK,YAAY,aAAW,KAAK,sBAAsBZ,KAAEuB,IAAED,IAAEV,GAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBhB,KAAEM,KAAEM,KAAEH,KAAE;AAAC,UAAML,MAAE,IAAI,aAAaJ,IAAE,QAAOY,MAAE,aAAa,iBAAiB,GAAEC,MAAE,IAAI,aAAab,IAAE,QAAOM,MAAE,aAAa,mBAAkBM,MAAEN,GAAC;AAAE,QAAIQ,MAAE;AAAE,UAAMC,MAAE,CAAAf,QAAGc,MAAEoC,GAAErC,KAAEb,KAAEI,KAAEU,KAAEL,GAAC;AAAE,aAAQQ,MAAE,GAAEA,MAAEJ,IAAE,SAAO,GAAEI,OAAG,IAAER,IAAE,CAAAM,IAAEE,GAAC,GAAEF,IAAEE,MAAE,IAAER,GAAC,GAAEM,IAAEE,MAAE,IAAER,GAAC,GAAEM,IAAEE,MAAE,IAAER,GAAC,GAAEM,IAAEE,MAAE,IAAER,GAAC,GAAEM,IAAEE,MAAE,IAAER,GAAC;AAAA,EAAC;AAAC;AAAC,SAASyC,GAAElD,KAAEM,KAAEM,KAAEH,KAAEL,KAAE;AAAC,WAAQS,MAAE,GAAEA,MAAET,KAAES,MAAI,CAAAD,IAAEH,KAAG,IAAET,IAAEM,KAAG;AAAE,SAAOG;AAAC;AAAC,SAAS0B,GAAEnC,KAAEM,KAAEM,KAAE;AAAC,SAAOL,GAAEP,KAAEM,IAAE,IAAIU,GAAE,QAAQ,EAAE,MAAKJ,MAAEA,IAAE,IAAII,GAAE,QAAQ,IAAE,IAAI;AAAC;AAAC,SAAST,GAAEP,KAAEM,KAAEM,KAAE;AAAC,SAAM,CAAC,CAACZ,IAAE,aAAWY,MAAEA,IAAE,SAAO,IAAEN,IAAE,SAAO;AAAE;AAAC,SAASI,GAAEV,KAAE;AAAC,SAAOA,IAAE,WAASI,IAAE,OAAKJ,IAAE,uBAAqB,gBAAcA,IAAE,aAAWA,IAAE;AAAU;AAAC,IAAMoC,KAAEtB,GAAE;AAAV,IAAYuB,KAAEvB,GAAE;AAAhB,IAAkByB,KAAEzB,GAAE;AAAtB,IAAwB,IAAEA,GAAE;AAA5B,IAA8Be,KAAEf,GAAE;AAAlC,IAAoCwB,MAAGI,GAAE;AAAzC,IAA2C,KAAGA,GAAE;AAAhD,IAAkD,KAAG5B,GAAE;AAAvD,IAAyD,KAAGA,GAAE;AAA9D,IAAgE,KAAGgC,GAAE;AAArE,IAAuE,KAAGA,GAAE;AAA5E,IAA8E,KAAGhC,GAAE;AAAnF,IAAqF,KAAGA,GAAE;AAA1F,IAA4F,KAAGA,GAAE;AAAjG,IAAmG,KAAG,CAAC4B,GAAE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC;AAAtH,IAAwH,KAAG,CAAC5B,GAAE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC;AAA3I,IAA6I,KAAGK,GAAE;AAAlJ,IAAoJ,KAAGA,GAAE;AAAzJ,IAA2J,KAAGA,GAAE;AAAhK,IAAkK,KAAGA,GAAE;;;ACA5kY,IAAMgC,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAED,KAAE;AAAC,SAAK,OAAKC,KAAE,KAAK,KAAGD;AAAA,EAAC;AAAC;AAAC,SAASE,GAAEA,KAAEC,KAAEC,KAAEC,KAAE;AAAC,SAAO,IAAIL,IAAEG,GAAED,KAAEC,KAAEC,GAAC,GAAEC,GAAC;AAAC;;;ACA9I,IAAqCC,MAAE,EAAC,YAAW,KAAI;;;ACAytB,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAK,YAAUA,KAAE,KAAK,WAAS,oBAAI,OAAI,KAAK,WAAS,oBAAI,OAAI,KAAK,YAAU,KAAI,KAAK,gBAAc,UAAQC,GAAE;AAAA,EAAC;AAAA,EAAC,UAAUC,KAAE;AAAC,UAAMC,MAAE,KAAK,SAAS,IAAI,KAAK,aAAa;AAAE,QAAG,QAAMA,KAAE;AAAC,YAAMC,MAAEC,IAAE;AAAW,UAAG,EAAED,GAAC,EAAE,QAAO,KAAK,SAAS,IAAI,KAAK,eAAcE,GAAEF,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,KAAK,aAAa,CAAC,GAAE,KAAK,UAAUF,GAAC;AAAE,YAAMC,MAAEG,GAAEJ,IAAE,CAAC,IAAE,KAAK,OAAO,IAAE,KAAGA,IAAE,CAAC,IAAE,KAAK,OAAO,IAAE,KAAGA,IAAE,CAAC,IAAE,KAAK,OAAO,IAAE,KAAG,KAAK,aAAa;AAAE,aAAO,KAAK,SAAS,IAAI,KAAK,eAAcC,GAAC,GAAEA;AAAA,IAAC;AAAC,UAAMI,MAAE,KAAK,WAAUN,MAAE,KAAK,MAAMC,IAAE,CAAC,IAAEK,GAAC,GAAEF,MAAE,KAAK,MAAMH,IAAE,CAAC,IAAEK,GAAC,GAAEC,MAAE,KAAK,MAAMN,IAAE,CAAC,IAAEK,GAAC,GAAEE,MAAE,GAAGR,GAAC,IAAII,GAAC,IAAIG,GAAC;AAAG,QAAIF,MAAE,KAAK,SAAS,IAAIG,GAAC;AAAE,UAAMC,KAAE,MAAGH;AAAE,QAAGN,GAAEU,IAAET,KAAEC,IAAE,IAAI,GAAEQ,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAED,MAAGC,GAAE,CAAC,IAAED,MAAGC,GAAE,CAAC,IAAED,IAAE;AAAC,UAAGJ,KAAE;AAAC,cAAMN,MAAE,KAAK,IAAI,GAAGW,EAAC;AAAE,QAAAV,GAAEU,IAAET,KAAEI,IAAE,IAAI,GAAEK,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,CAAC;AAAE,YAAG,KAAK,IAAI,GAAGA,EAAC,IAAEX,IAAE,QAAOM;AAAA,MAAC;AAAC,aAAOH;AAAA,IAAC;AAAC,WAAOG,QAAIA,MAAEA,GAAEL,MAAEM,KAAEF,MAAEE,KAAEC,MAAED,KAAEE,GAAC,GAAE,KAAK,SAAS,IAAIA,KAAEH,GAAC,IAAGA;AAAA,EAAC;AAAA,EAAC,eAAeN,KAAEE,MAAEE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE;AAAC,UAAMA,MAAE,OAAO,MAAKD,MAAEC,IAAE,QAAOQ,KAAEV,IAAE,SAAS;AAAE,QAAG,CAAC,KAAK,SAAS,IAAIU,EAAC,GAAE;AAAC,WAAK,YAAU,IAAIC,GAAE,EAAC,OAAM,GAAE,OAAMX,IAAC,CAAC,GAAEC,IAAE,IAAI,KAAK,SAAS;AAAE,YAAMH,MAAE,IAAIc,IAAE,EAAC,UAAS,MAAE,CAAC,GAAEV,MAAE,IAAIW,GAAE,EAAC,YAAW,MAAE,CAAC;AAAE,MAAAZ,IAAE,IAAIC,GAAC,GAAEJ,IAAE,IAAII,GAAC,GAAED,IAAE,IAAIH,GAAC,GAAE,KAAK,SAAS,IAAIY,IAAER,GAAC;AAAA,IAAC;AAAC,UAAMY,MAAE,KAAK,SAAS,IAAIJ,EAAC,GAAEb,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAEY,MAAEZ,IAAE,QAAOkB,KAAE,IAAI,MAAM,IAAEN,GAAC,GAAEO,MAAE,IAAI,SAAMJ,MAAE,MAAG,KAAK;AAAU,aAAQP,MAAE,GAAEA,MAAEI,KAAEJ,MAAI,CAAAU,GAAE,IAAEV,MAAE,CAAC,IAAEP,IAAE,CAAC,KAAG,IAAED,IAAEQ,GAAC,IAAEO,MAAE,CAACA,MAAGG,GAAE,IAAEV,MAAE,CAAC,IAAEP,IAAE,CAAC,KAAG,IAAED,IAAEQ,GAAC,IAAEO,MAAE,CAACA,MAAGG,GAAE,IAAEV,MAAE,CAAC,IAAEP,IAAE,CAAC,KAAG,IAAED,IAAEQ,GAAC,IAAEO,MAAE,CAACA,MAAGP,MAAE,KAAGW,IAAE,KAAKX,MAAE,GAAEA,GAAC;AAAE,OAAEU,IAAE,KAAK,WAAU,GAAEA,IAAEb,IAAE,wBAAuB,GAAEO,GAAC;AAAE,UAAMQ,KAAE,IAAIC,GAAE,KAAK,WAAU,CAAC,CAACC,GAAE,UAAS,IAAId,GAAEU,IAAE,GAAE,IAAE,CAAC,CAAC,GAAE,CAAC,CAACI,GAAE,UAASH,GAAC,CAAC,GAAE,MAAKjB,GAAE,IAAI;AAAE,IAAAE,IAAE,IAAIgB,EAAC,GAAEH,IAAE,YAAYG,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,UAAMnB,MAAE;AAAK,WAAM,EAAC,IAAI,SAASE,KAAE;AAAC,MAAAF,IAAE,YAAUE;AAAA,IAAC,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMS,KAAEN,GAAE;;;ACAt6E,IAAIiB;AAAJ,IAAMC;AAAE,CAAC,SAASD,KAAE;AAAC,EAAAA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,IAAEA,IAAE,MAAI,CAAC,IAAE,OAAMA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,IAAEA,IAAE,MAAI,CAAC,IAAE,OAAMA,IAAEA,IAAE,MAAI,CAAC,IAAE,OAAMA,IAAEA,IAAE,MAAI,CAAC,IAAE,OAAMA,IAAEA,IAAE,QAAM,CAAC,IAAE;AAAO,EAAEA,QAAIA,MAAE,CAAC,EAAE;AAAE,IAAME,MAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,iBAAe,GAAE,KAAK,gBAAc,OAAG,KAAK,MAAI,MAAG,KAAK,0BAAwB,MAAG,KAAK,mBAAiB,OAAG,KAAK,mBAAiB,MAAG,KAAK,aAAW,OAAG,KAAK,oBAAkB,CAAC,GAAE,KAAK,QAAMD,IAAE;AAAA,EAAG;AAAC;AAAC,CAAC,SAASD,KAAE;AAAC,EAAAA,IAAEA,IAAE,MAAI,CAAC,IAAE,OAAMA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,IAAEA,IAAE,MAAI,CAAC,IAAE;AAAK,EAAEC,QAAIA,MAAE,CAAC,EAAE;;;ACA7Y,IAAME,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,KAAEF,KAAE;AAAC,SAAK,SAAOC,KAAE,KAAK,aAAWC,KAAE,KAAK,aAAWF;AAAA,EAAC;AAAC;AAAC,IAAMG,MAAN,cAAgBH,IAAC;AAAA,EAAC,YAAYA,KAAEG,KAAEC,KAAEC,KAAE;AAAC,UAAML,KAAEG,KAAEC,GAAC,GAAE,KAAK,SAAO,EAAEC,GAAC,IAAEH,GAAEG,GAAC,IAAE;AAAA,EAAI;AAAC;AAAoB,IAAMC,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAK,WAASA;AAAA,EAAC;AAAC;AAAC,IAAMC,MAAN,cAAgBF,IAAC;AAAA,EAAC,YAAYC,KAAEE,KAAE;AAAC,UAAMF,GAAC,GAAE,KAAK,aAAWE;AAAA,EAAC;AAAC;;;ACApH,SAASC,IAAEC,KAAE;AAAC,SAAO,EAAEA,GAAC,KAAG,EAAEA,IAAE,IAAI;AAAC;AAAyN,IAAMC,MAAEC,GAAE;;;ACAwU,IAAMC,KAAE;AAAK,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAK,UAAQ,IAAIC,OAAE,KAAK,WAAS,IAAIC,MAAE,KAAK,YAAU,IAAIC,MAAE,KAAK,YAAUL,IAAE,KAAK,iBAAe,MAAK,KAAK,OAAK,EAAE,GAAE,KAAK,UAAQM,GAAE,GAAE,KAAK,uBAAqBA,GAAE,GAAE,KAAK,qBAAmBA,GAAE,GAAE,KAAK,cAAYJ,OAAGK,IAAE;AAAA,EAAM;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,MAAML,KAAEM,KAAEC,KAAE;AAAC,SAAK,aAAaC,GAAER,KAAEM,KAAE,KAAK,IAAI,GAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,aAAaP,KAAEM,KAAE;AAAC,SAAK,SAAOA,KAAEN,QAAI,KAAK,QAAMS,GAAET,KAAE,KAAK,IAAI,GAAE,MAAI,KAAK,QAAQ,iBAAe,KAAK,gBAAcK,IAAE,QAAM,KAAK,KAAK,OAAO,CAAC,KAAG,KAAK,QAAQ,iBAAe,KAAK,iBAAe,KAAK,QAAQ,iBAAe,KAAK,iBAAe,MAAKF,GAAE,KAAK,SAAQ,KAAK,KAAK,QAAO,KAAK,KAAK,SAAS,GAAE,KAAK,SAAS,KAAK,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,UAAUG,MAAE,MAAKC,KAAEG,KAAET,KAAEU,KAAE;AAAC,SAAK,QAAMJ,KAAE,KAAK,kBAAgBN,KAAE,KAAK,YAAUS,OAAGZ;AAAE,UAAMM,MAAEQ,GAAE,KAAK,cAAc;AAAE,QAAG,EAAEN,GAAC,KAAGA,IAAE,SAAO,GAAE;AAAC,YAAMC,MAAEI,MAAE,CAAAX,QAAG;AAAC,QAAAW,IAAEX,GAAC,KAAG,KAAK,gBAAgBA,GAAC;AAAA,MAAC,IAAE,CAAAA,QAAG;AAAC,aAAK,gBAAgBA,GAAC;AAAA,MAAC;AAAE,iBAAUU,OAAKJ,KAAE;AAAC,cAAMA,MAAEI,IAAE,8BAA4BA,IAAE,2BAA2B;AAAE,UAAEJ,GAAC,KAAG,EAAEF,GAAC,IAAEE,IAAE,kCAAkC,KAAK,KAAK,QAAO,KAAK,KAAK,WAAUC,KAAEH,GAAC,IAAEE,IAAE,gBAAgB,KAAK,KAAK,QAAO,KAAK,KAAK,WAAUC,GAAC,GAAE,KAAK,QAAQ,iBAAe,KAAK,QAAQ,OAAKD,IAAE,wBAAwBC,GAAC,KAAGG,IAAE,QAAQ,OAAQ,CAAAV,QAAGO,IAAEP,GAAC,CAAE;AAAA,MAAC;AAAA,IAAC;AAAC,SAAK,YAAY;AAAA,EAAC;AAAA,EAAC,gBAAgBM,KAAE;AAAC,UAAMC,MAAED,IAAE;AAAW,QAAG,CAACC,IAAE;AAAO,UAAMG,MAAEJ,IAAE,gBAAeK,MAAEC,GAAE,KAAK,cAAc;AAAE,eAAUC,OAAKN,KAAE;AAAC,UAAG,CAACM,IAAE,QAAQ;AAAS,YAAK,EAAC,UAASN,KAAE,IAAGC,IAAC,IAAEK;AAAE,WAAK,UAAU,+BAA+BH,KAAEG,IAAE,oBAAoB,GAAEC,GAAE,KAAK,sBAAqB,KAAK,UAAS,KAAK,UAAU,OAAO,GAAEA,GAAE,KAAK,oBAAmB,KAAK,QAAO,KAAK,UAAU,OAAO;AAAE,YAAMC,MAAE,KAAK,UAAU;AAAU,QAAEJ,GAAC,MAAIA,IAAE,kBAAgB,KAAK,YAAWJ,IAAE,UAAUM,KAAE,KAAK,UAAU,WAAU,MAAK,KAAK,sBAAqB,KAAK,oBAAoB,CAACN,KAAEG,KAAEC,KAAEP,KAAES,KAAEG,QAAI;AAAC,YAAGT,OAAG,GAAE;AAAC,cAAG,EAAE,KAAK,eAAe,KAAG,CAAC,KAAK,gBAAgB,KAAK,KAAK,QAAO,KAAK,SAAQA,GAAC,EAAE;AAAO,gBAAMF,MAAED,MAAE,KAAK,SAAS,MAAI,KAAK,UAASa,KAAEb,MAAE,CAAAJ,QAAG;AAAC,kBAAMI,MAAE,IAAIY,IAAEV,KAAEE,KAAEG,KAAEK,GAAC;AAAE,YAAAhB,IAAE,IAAIU,IAAE,KAAIN,KAAEG,KAAEG,KAAEG,IAAEA,GAAC;AAAA,UAAC,IAAE,CAAAb,QAAGA,IAAE,IAAIU,IAAE,QAAO,EAAC,QAAOJ,KAAE,YAAWE,KAAE,YAAWG,IAAC,GAAEJ,KAAEG,KAAEK,KAAEF,GAAC;AAAE,eAAI,QAAMR,IAAE,IAAI,oBAAkBQ,OAAGR,IAAE,IAAI,sBAAoB,QAAMA,IAAE,IAAI,QAAME,MAAEF,IAAE,IAAI,SAAOY,GAAEZ,IAAE,GAAG,GAAE,KAAK,QAAQ,UAAQL,IAAE,QAAM,QAAMK,IAAE,IAAI,oBAAkBQ,MAAER,IAAE,IAAI,sBAAoB,QAAMA,IAAE,IAAI,QAAME,MAAEF,IAAE,IAAI,SAAOY,GAAEZ,IAAE,GAAG,GAAE,KAAK,QAAQ,UAAQL,IAAE,IAAI,KAAGI,KAAE;AAAC,kBAAMJ,MAAE,IAAIkB,GAAE,KAAK,IAAI;AAAE,YAAAD,GAAEjB,GAAC,GAAE,KAAK,SAAS,IAAI,IAAI,KAAKA,GAAC;AAAA,UAAC,OAAK;AAAC,kBAAMA,MAAE,IAAImB,GAAE,KAAK,IAAI;AAAE,YAAAF,GAAEjB,GAAC,GAAE,KAAK,SAAS,IAAI,KAAKA,GAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,MAAE,KAAK,SAAS,KAAI;AAAC,IAAAA,IAAE,KAAM,CAACA,KAAEO,QAAIP,IAAE,SAAOO,IAAE,OAAK,EAAEP,IAAE,MAAK,CAAC,IAAE,EAAEO,IAAE,MAAK,CAAC,IAAEP,IAAE,qBAAmBO,IAAE,mBAAiB,EAAEP,IAAE,kBAAiB,OAAO,SAAS,IAAE,EAAEO,IAAE,kBAAiB,OAAO,SAAS,IAAE,EAAEA,IAAE,yBAAwB,OAAO,SAAS,IAAE,EAAEP,IAAE,yBAAwB,OAAO,SAAS,CAAE;AAAA,EAAC;AAAC;AAAC,SAASoB,IAAEpB,KAAE;AAAC,SAAO,IAAID,GAAEC,GAAC;AAAC;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,MAAI,IAAIiB,GAAE,EAAE,CAAC,GAAE,KAAK,MAAI,IAAIA,GAAE,EAAE,CAAC,GAAE,KAAK,MAAI,EAAC,KAAI,IAAID,GAAE,EAAE,CAAC,GAAE,KAAI,IAAIA,GAAE,EAAE,CAAC,GAAE,KAAI,IAAI,QAAK,GAAE,KAAK,SAAO,IAAIC,GAAE,EAAE,CAAC,GAAE,KAAK,MAAI,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKnB,KAAE;AAAC,SAAK,IAAI,KAAKA,GAAC,GAAE,KAAK,IAAI,KAAKA,GAAC,GAAE,KAAK,OAAO,KAAKA,GAAC,GAAE,KAAK,IAAI,SAAO,GAAE,KAAK,IAAI,IAAI,KAAKA,GAAC,GAAE,KAAK,IAAI,IAAI,KAAKA,GAAC,GAAE,KAAK,IAAI,IAAI,SAAO;AAAA,EAAC;AAAC;AAAC,IAAMmB,KAAN,MAAO;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAO,EAAE,KAAK,IAAI,KAAG,EAAEE,IAAE,KAAK,IAAI,WAAU,KAAK,IAAI,GAAEd,GAAEc,EAAC,KAAG;AAAA,EAAI;AAAA,EAAC,qBAAqBrB,KAAE;AAAC,WAAM,CAAC,CAACgB,IAAE,IAAI,MAAI,EAAEK,IAAE,KAAK,IAAI,WAAU,KAAK,IAAI,GAAElB,GAAEH,KAAE,KAAK,IAAI,QAAOqB,EAAC,GAAE;AAAA,EAAG;AAAA,EAAC,qBAAqBrB,KAAE;AAAC,WAAOM,GAAEgB,IAAE,KAAK,MAAM,GAAEA,GAAE,CAAC,IAAE,GAAE,EAAEA,IAAEA,IAAE,KAAK,cAAc,GAAEhB,GAAEN,KAAEsB,EAAC,GAAEC,GAAEvB,KAAEA,GAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,KAAE;AAAC,SAAK,cAAYU,IAAE,QAAO,KAAK,SAAON,GAAE,GAAE,KAAK,iBAAeH,GAAE,GAAE,KAAK,OAAK,EAAE,GAAE,KAAK,KAAKD,GAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,KAAE;AAAC,SAAK,OAAK,MAAK,KAAK,SAAO,MAAK,KAAK,mBAAiB,MAAK,KAAK,0BAAwB,MAAK,KAAK,cAAYU,IAAE,QAAOD,GAAET,KAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,IAAIA,KAAEU,KAAEC,KAAEP,KAAES,KAAEL,KAAEQ,KAAE;AAAC,SAAK,cAAYhB,KAAE,KAAK,OAAKW,KAAEL,GAAE,KAAK,QAAO,EAAEF,KAAE,CAAC,CAAC,GAAEA,GAAE,KAAK,gBAAe,EAAES,KAAEA,EAAC,CAAC,GAAE,KAAK,SAAOH,KAAE,KAAK,mBAAiBF,KAAE,KAAK,0BAAwBQ;AAAA,EAAC;AAAA,EAAC,KAAKhB,KAAE;AAAC,IAAAS,GAAET,IAAE,KAAI,KAAK,IAAI,GAAE,KAAK,cAAYA,IAAE,aAAY,KAAK,OAAKA,IAAE,MAAK,KAAK,SAAOA,IAAE,QAAO,KAAK,mBAAiBA,IAAE,kBAAiB,KAAK,0BAAwBA,IAAE,yBAAwBM,GAAE,KAAK,QAAON,IAAE,MAAM,GAAEI,GAAE,KAAK,gBAAeJ,IAAE,cAAc;AAAA,EAAC;AAAC;AAAC,IAAMkB,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,cAAYT,IAAE;AAAA,EAAG;AAAC;AAAC,SAASc,GAAExB,KAAE;AAAC,SAAO,IAAImB,GAAEnB,GAAC;AAAC;AAAC,IAAMqB,KAAEjB,GAAE;AAAV,IAAYkB,KAAElB,GAAE;;;ACAhvJ,IAAIqB;AAAE,CAAC,SAASC,KAAE;AAAC,EAAAA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,gBAAc,CAAC,IAAE,iBAAgBA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,wBAAsB,CAAC,IAAE;AAAuB,EAAED,QAAIA,MAAE,CAAC,EAAE;AAAE,IAAME,MAAN,MAAO;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,YAAYC,KAAEC,KAAE;AAAC,SAAK,QAAMD,KAAE,KAAK,gBAAcC,KAAE,KAAK,UAAQC,GAAE,GAAE,KAAK,aAAW,GAAE,KAAK,oBAAkBC,GAAE,GAAE,GAAE,GAAE,GAAG,GAAE,KAAK,aAAW,GAAE,KAAK,mBAAiB,GAAE,KAAK,mBAAiB,IAAIC,MAAE,KAAK,yBAAuB,OAAG,KAAK,wBAAsB,OAAG,KAAK,8BAA4B,OAAG,KAAK,QAAMJ,KAAE,KAAK,eAAa,IAAI,MAAMC,IAAE,cAAc,MAAM,EAAE,KAAK,KAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBH,KAAE;AAAC,WAAO,KAAK,aAAaA,GAAC,IAAE,KAAK,cAAc,UAAUA,GAAC,EAAE,WAAW,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,gCAA+B;AAAC,WAAO,KAAK,yBAAuB,KAAK,0BAAwB,KAAK;AAAA,EAA2B;AAAA,EAAC,gBAAgBA,KAAE;AAAC,UAAME,MAAEF,QAAID,IAAE,gBAAc,KAAK,cAAc,UAAUQ,GAAE,KAAK,IAAEP,QAAID,IAAE,YAAU,KAAK,cAAc,UAAUQ,GAAE,SAAS,IAAEP,QAAID,IAAE,wBAAsB,KAAK,cAAc,UAAUQ,GAAE,qBAAqB,IAAE,KAAK,cAAc,UAAUA,GAAE,QAAQ;AAAE,WAAOL,MAAEA,IAAE,WAAW,IAAE;AAAA,EAAI;AAAA,EAAC,+BAA8B;AAAC,WAAO,KAAK,gCAA8B,KAAK,gBAAgBK,GAAE,kBAAkB,IAAE,KAAK,yBAAuB,KAAK,gBAAgBA,GAAE,KAAK,IAAE;AAAA,EAAI;AAAA,EAAC,iBAAiBP,KAAE;AAAC,UAAME,MAAEF,QAAID,IAAE,gBAAc,KAAK,cAAc,UAAUQ,GAAE,KAAK,IAAE;AAAK,WAAOL,MAAEA,IAAE,WAAW,IAAE;AAAA,EAAI;AAAA,EAAC,KAAKF,KAAEE,KAAE;AAAC,UAAMC,MAAE,KAAK,oCAAoC;AAAE,eAAUK,OAAK,KAAK,cAAc,cAAc,CAAAA,IAAE,SAAOD,GAAE,sBAAoB,KAAK,gCAA8B,KAAK,aAAaC,IAAE,IAAI,IAAER,IAAE,WAAW,MAAKQ,KAAEN,GAAC,IAAE,KAAK,aAAaM,IAAE,IAAI,IAAE;AAAG,WAAOL,MAAE,KAAK,oCAAoC,IAAEK,GAAE,UAAQA,GAAE;AAAA,EAAS;AAAA,EAAC,sCAAqC;AAAC,UAAMR,MAAE,KAAK;AAAa,WAAM,CAACA,IAAEO,GAAE,KAAK,IAAE,CAACP,IAAEO,GAAE,kBAAkB,KAAG,IAAE,CAACP,IAAEO,GAAE,SAAS,KAAG,IAAE,CAACP,IAAEO,GAAE,KAAK,KAAG,IAAE,CAACP,IAAEO,GAAE,QAAQ,KAAG;AAAA,EAAC;AAAA,EAAC,2BAA2BP,KAAE;AAAC,SAAK,yBAAyB;AAAE,UAAMG,MAAE,OAAKH,IAAE;AAAM,QAAG,KAAK,QAAQ,CAAC,IAAEG,OAAGH,IAAE,KAAI;AAAC,YAAMG,MAAE,KAAK,iBAAiB,QAAQ,KAAK,iBAAiB,UAAU;AAAE,MAAAM,GAAE,KAAK,SAAQT,IAAE,OAAM,GAAEG,GAAC;AAAA,IAAC;AAAC,QAAG,KAAK,QAAQ,CAAC,IAAEA,OAAGH,IAAE,KAAI;AAAC,YAAMG,MAAE,KAAK,iBAAiB,QAAQ,KAAK,iBAAiB,UAAU;AAAE,MAAAM,GAAE,KAAK,SAAQ,CAACT,IAAE,OAAM,GAAEG,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,2BAA0B;AAAC,SAAK,iBAAiB,WAAS,GAAEO,GAAE,KAAK,iBAAiB,QAAQ,CAAC,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,aAAQV,MAAE,GAAEA,MAAE,KAAK,iBAAiB,UAASA,OAAI;AAAC,YAAME,MAAE,KAAK,iBAAiB,QAAQF,GAAC;AAAE,UAAGE,IAAE,CAAC,MAAIA,IAAE,CAAC,KAAGA,IAAE,CAAC,MAAIA,IAAE,CAAC,EAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,cAAcF,KAAE;AAAC,IAAAA,IAAE,YAAY,KAAK,UAAQW,GAAE,QAAM,IAAE,KAAK,YAAW,GAAE,KAAK,YAAW,KAAK,UAAU;AAAA,EAAC;AAAC;AAAC,IAAML,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,UAAQ,CAACF,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,GAAE,KAAK,WAAS;AAAA,EAAC;AAAC;;;ACA7lF,IAAMQ,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAED,KAAE;AAAC,SAAK,QAAME,GAAE,GAAE,KAAK,OAAK,MAAK,KAAK,OAAK,IAAIC,GAAEF,KAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,KAAI,GAAE,EAAC,QAAO,EAAE,YAAW,aAAYG,GAAE,MAAK,UAAS,EAAE,eAAc,UAASC,GAAE,eAAc,cAAa,EAAE,sBAAqB,WAAUL,KAAE,eAAc,GAAE,OAAM,GAAE,QAAO,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,OAAK,EAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,OAAK,KAAK,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,WAAO,SAAO,KAAK;AAAA,EAAI;AAAA,EAAC,OAAOC,KAAEK,KAAE;AAAC,SAAK,MAAM,CAAC,IAAEL,KAAE,KAAK,MAAM,CAAC,IAAEK,KAAE,KAAK,KAAK,OAAO,KAAK,MAAM,CAAC,GAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKL,KAAE;AAAC,IAAAA,IAAE,gBAAgB,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,UAAMA,MAAE,KAAK,KAAK;AAAa,IAAAA,IAAE,WAAW,aAAWA,IAAE,eAAe;AAAA,EAAC;AAAA,EAAC,4BAA2B;AAJj/B;AAIk/B,eAAK,SAAL,mBAAW,OAAO,GAAE;AAAA,EAAE;AAAA,EAAC,IAAI,iBAAgB;AAJ7hC;AAI8hC,aAAO,UAAK,SAAL,mBAAW,mBAAgB;AAAA,EAAC;AAAC;;;ACA90B,IAAMM,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,KAAEF,KAAEG,MAAE,MAAG;AAAC,SAAK,SAAOD,KAAE,KAAK,OAAKF,KAAE,KAAK,QAAM,OAAG,KAAK,WAAS,IAAE,GAAE,KAAK,MAAI,IAAII,IAAEH,KAAEE,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMA,MAAN,MAAO;AAAA,EAAC,YAAYE,KAAE;AAAC,SAAK,gBAAc,CAAC,IAAIL,IAAEK,KAAEC,GAAE,OAAMC,GAAE,KAAK,GAAE,IAAIP,IAAEK,KAAEC,GAAE,OAAMC,GAAE,kBAAkB,GAAE,IAAIP,IAAEK,KAAEC,GAAE,WAAUC,GAAE,WAAU,KAAE,GAAE,IAAIP,IAAEK,KAAEC,GAAE,QAAOC,GAAE,KAAK,GAAE,IAAIP,IAAEK,KAAEC,GAAE,OAAMC,GAAE,QAAQ,CAAC,GAAE,IAAI,2CAA2C,KAAG,KAAK,cAAc,KAAK,IAAIP,IAAEK,KAAEC,GAAE,uBAAsBC,GAAE,qBAAqB,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUN,KAAE;AAAC,WAAO,KAAK,cAAcA,GAAC,EAAE;AAAA,EAAG;AAAA,EAAC,UAAS;AAAC,eAAUA,OAAK,KAAK,cAAc,CAAAA,IAAE,IAAI,QAAQ;AAAA,EAAC;AAAA,EAAC,4BAA2B;AAAC,eAAUA,OAAK,KAAK,cAAc,CAAAA,IAAE,IAAI,0BAA0B;AAAA,EAAC;AAAA,EAAC,uBAAuBA,KAAEI,KAAEH,KAAE;AAAC,QAAGD,IAAE,CAAAI,IAAE,WAASH;AAAA,aAAUA,MAAEG,IAAE,WAASG,IAAE,CAAAH,IAAE,IAAI,0BAA0B,GAAEA,IAAE,WAAS,IAAE;AAAA,aAAUA,IAAE,WAAS,IAAE,EAAE,QAAM;AAAG,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,cAAc,OAAQ,CAACJ,KAAEI,QAAIJ,MAAEI,IAAE,IAAI,gBAAgB,CAAC;AAAA,EAAC;AAAC;AAAC,IAAMG,MAAE;;;ACAz6B,IAAMC,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAK,WAASA,KAAE,KAAK,2BAAyB,IAAIC,MAAE,KAAK,gBAAc,GAAE,KAAK,uBAAqBC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,SAAS;AAAA,EAAW;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,UAAS;AAAC,SAAK,yBAAyB,QAAS,CAAAF,QAAGA,IAAE,QAAS,CAAAA,QAAGA,IAAE,UAAU,QAAQ,CAAE,CAAE,GAAE,KAAK,yBAAyB,MAAM;AAAA,EAAC;AAAA,EAAC,QAAQC,KAAEE,MAAEC,KAAE;AAAC,UAAMC,MAAEF,IAAE;AAAI,QAAIJ,MAAE,KAAK,yBAAyB,IAAIE,KAAEI,GAAC;AAAE,QAAG,EAAEN,GAAC,GAAE;AAAC,YAAMC,MAAE,IAAIC,IAAE,KAAK,UAASE,KAAG,MAAI,KAAK,QAAQH,GAAC,CAAE;AAAE,MAAAD,MAAE,IAAIO,IAAEN,GAAC,GAAE,KAAK,yBAAyB,IAAIC,KAAEI,KAAEN,GAAC;AAAA,IAAC;AAAC,WAAM,EAAEA,IAAE,UAASA,IAAE;AAAA,EAAS;AAAA,EAAC,kBAAkBC,KAAEG,KAAEE,KAAE;AAAC,QAAG,EAAEA,GAAC,GAAE;AAAC,UAAGF,IAAE,QAAME,IAAE,IAAI,QAAOA;AAAE,WAAK,QAAQA,GAAC;AAAA,IAAC;AAAC,WAAO,KAAK,QAAQL,KAAEG,GAAC;AAAA,EAAC;AAAA,EAAC,QAAQF,KAAE;AAAC,QAAG,EAAEA,GAAC,KAAG,KAAK,yBAAyB,MAAM;AAAO,UAAME,MAAE,KAAK,yBAAyB,IAAIF,IAAE,aAAYA,IAAE,GAAG;AAAE,MAAEE,GAAC,MAAI,EAAEA,IAAE,UAAS,MAAIA,IAAE,aAAWA,IAAE,eAAa,KAAK;AAAA,EAAe;AAAA,EAAC,cAAa;AAAC,SAAK,iBAAgB,KAAK,yBAAuBD,OAAG,KAAK,yBAAyB,QAAS,CAACF,KAAEC,QAAI;AAAC,MAAAD,IAAE,QAAS,CAACA,KAAEG,QAAI;AAAC,cAAIH,IAAE,YAAUA,IAAE,eAAa,KAAK,uBAAqB,KAAK,kBAAgBA,IAAE,UAAU,QAAQ,GAAE,KAAK,yBAAyB,OAAOC,KAAEE,GAAC;AAAA,MAAE,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,YAAW;AAAC,UAAMH,MAAE,IAAI;AAAM,SAAK,yBAAyB,QAAS,CAACC,KAAEE,QAAI;AAAC,YAAME,MAAE,OAAML,KAAEC,QAAI;AAAC,cAAME,MAAEF,IAAE;AAAO,QAAAE,QAAI,MAAMA,IAAE,OAAO,GAAEH,IAAE,QAAS,CAAAA,QAAGA,IAAE,UAAU,OAAO,KAAK,QAAQ,CAAE;AAAA,MAAE;AAAE,MAAAA,IAAE,KAAKK,IAAEJ,KAAEE,GAAC,CAAC;AAAA,IAAC,CAAE,GAAE,MAAM,QAAQ,IAAIH,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMM,MAAN,MAAO;AAAA,EAAC,YAAYN,KAAE;AAAC,SAAK,YAAUA,KAAE,KAAK,WAAS,GAAE,KAAK,eAAa;AAAA,EAAC;AAAC;AAAC,IAAME,MAAE;AAAR,IAAWE,MAAE,IAAIH;;;ACAh8C,IAAMM,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,SAAK,qBAAmBH,KAAE,KAAK,uBAAqBC,KAAE,KAAK,kBAAgBC,KAAE,KAAK,gBAAcC,KAAE,KAAK,oBAAkB,IAAIF;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,mBAAmB,QAAQ;AAAA,EAAC;AAAA,EAAC,QAAQD,KAAEE,KAAEC,KAAE;AAAC,QAAG,KAAK,aAAaH,GAAC,GAAE,CAACA,IAAE,aAAaE,KAAEC,GAAC,EAAE,QAAO;AAAK,QAAIC,MAAE,KAAK,kBAAkB,IAAID,KAAEH,IAAE,EAAE;AAAE,QAAG,EAAEI,GAAC,GAAE;AAAC,YAAMH,MAAED,IAAE,iBAAiB,EAAC,UAASA,KAAE,cAAa,KAAK,sBAAqB,YAAW,KAAK,oBAAmB,QAAOG,IAAC,CAAC;AAAE,MAAAC,MAAE,IAAIC,IAAEJ,GAAC,GAAE,KAAK,kBAAkB,IAAIE,KAAEH,IAAE,IAAGI,GAAC;AAAA,IAAC;AAAC,WAAOA,IAAE,IAAI,GAAEA,IAAE;AAAA,EAAU;AAAA,EAAC,QAAQJ,KAAEC,KAAE;AAAC,UAAMG,MAAE,KAAK,kBAAkB,IAAIH,KAAED,IAAE,EAAE;AAAE,MAAEI,GAAC,MAAIA,IAAE,MAAM,GAAEA,IAAE,eAAa,EAAEA,IAAE,UAAU,GAAE,KAAK,kBAAkB,OAAOH,KAAED,IAAE,EAAE;AAAA,EAAG;AAAA,EAAC,aAAaC,KAAE;AAAC,MAAEA,IAAE,UAAU,KAAGA,IAAE,eAAa,QAAM,EAAE,UAAU,qDAAqD,EAAE,MAAM,8DAA8D,GAAEA,IAAE,aAAW;AAAA,EAAI;AAAC;AAAC,IAAMI,MAAN,MAAO;AAAA,EAAC,YAAYL,KAAE;AAAC,SAAK,aAAWA,KAAE,KAAK,UAAQ;AAAA,EAAC;AAAA,EAAC,MAAK;AAAC,MAAE,KAAK;AAAA,EAAO;AAAA,EAAC,QAAO;AAAC,MAAE,KAAK,SAAQI,GAAE,KAAK,WAAS,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,UAAQ;AAAA,EAAC;AAAC;;;ACA/lB,IAAME,MAAN,MAAO;AAAA,EAAC,YAAYA,KAAEC,KAAEC,IAAE;AAAC,SAAK,YAAUF,KAAE,KAAK,aAAWC,KAAE,KAAK,aAAWC,IAAE,KAAK,OAAKC,GAAE,iBAAgB,KAAK,eAAa,OAAG,KAAK,mBAAiB,MAAG,KAAK,uBAAqBH,IAAE,MAAK,KAAK,UAAQ,IAAII,MAAE,KAAK,mBAAiBC,IAAE,GAAE,KAAK,cAAY,IAAIC,MAAE,KAAK,cAAY,IAAIA,MAAE,KAAK,iBAAe,IAAIA,MAAE,KAAK,YAAU,KAAK,aAAY,KAAK,MAAI,IAAIC,MAAE,KAAK,mBAAiB,IAAIP,OAAE,KAAK,oBAAkB,IAAIQ,OAAE,KAAK,WAAS,CAAC,GAAE,KAAK,aAAW,IAAIC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,IAAI,OAAOA,KAAE;AAAC,SAAK,UAAQ,KAAK,IAAI,SAAOA,KAAE,KAAK,iBAAiB,CAAC,IAAE,IAAEA,IAAE,aAAa,CAAC,GAAE,KAAK,iBAAiB,CAAC,IAAE,IAAEA,IAAE,aAAa,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK;AAAA,EAAgB;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK,cAAY,KAAK;AAAA,EAAc;AAAA,EAAC,aAAaA,KAAE;AAAC,UAAK,EAAC,aAAYC,KAAE,aAAYF,IAAC,IAAE;AAAK,IAAAC,OAAG,IAAE,KAAK,YAAUD,OAAG,KAAK,eAAe,aAAaE,KAAEF,KAAEC,GAAC,GAAE,KAAK,YAAU,KAAK;AAAA,EAAe;AAAC;;;ACAhuC,IAAME,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,KAAEF,KAAEG,MAAE,MAAK;AAAC,SAAK,OAAKF,KAAE,KAAK,cAAYE,KAAE,KAAK,kBAAgB,IAAIC,MAAE,KAAK,SAAOC,GAAE,OAAM,KAAK,qBAAmBC,KAAE,KAAK,iBAAe,IAAIH,IAAED,KAAEF,KAAE,EAAEG,GAAC,IAAEA,IAAE,QAAM,IAAI;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,SAAK,qBAAmBG;AAAA,EAAC;AAAC;AAA4H,IAAMC,MAAEC,GAAE,UAAQA,GAAE,wBAAsBA,GAAE;;;ACArU,IAAIC,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,oBAAkBC,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAC;AAAE,EAAE,CAACC,GAAE,CAAC,GAAEH,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAACG,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEH,GAAE,WAAU,oBAAmB,IAAI,GAAEA,KAAE,EAAE,CAACI,GAAE,8CAA8C,CAAC,GAAEJ,EAAC;;;ACA6lB,IAAIK;AAAE,CAAC,SAASC,KAAE;AAAC,EAAAA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,IAAEA,IAAE,UAAQ,CAAC,IAAE;AAAS,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,SAAO,IAAIC,MAAE,KAAK,WAASC,GAAE;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAOC,GAAE,KAAK,uBAAsB,KAAK,kBAAkB,CAAC,GAAE,KAAK,eAAa,IAAE,KAAK,kBAAkB,CAAC,IAAE,IAAE,GAAE,KAAK,eAAa,IAAE,KAAK,kBAAkB,CAAC,IAAE,IAAE,GAAE,KAAK,eAAa,IAAE,KAAK,kBAAkB,CAAC,IAAE,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYL,KAAEG,KAAE;AAAC,SAAK,QAAMH,KAAE,KAAK,eAAaG,KAAE,KAAK,WAAS,OAAG,KAAK,aAAW,IAAI,SAAM,KAAK,eAAa,GAAE,KAAK,eAAa,GAAE,KAAK,kBAAgB,GAAE,KAAK,kBAAgBA,GAAE,GAAE,KAAK,yBAAuBA,GAAE,GAAE,KAAK,kBAAgBA,GAAE,GAAE,KAAK,qBAAmB,GAAE,KAAK,oBAAkB,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,wBAAsBG,GAAE,GAAE,KAAK,YAAU,CAAC,IAAIL,MAAE,IAAIA,MAAE,IAAIA,MAAE,IAAIA,IAAC,GAAE,KAAK,cAAY,MAAK,KAAK,kBAAgB,KAAK,IAAI,IAAI,aAAa,IAAE,OAAK,MAAK,KAAK,MAAM,WAAW,cAAc;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,UAAQ,OAAG,KAAK,wBAAwB;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,SAAK,qBAAqB,GAAE,KAAK,qBAAqB;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYE,KAAE;AAAC,SAAK,kBAAgBI,GAAE,KAAK,MAAMJ,GAAC,GAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,IAAI,QAAQH,KAAE;AAAC,SAAK,WAASA,KAAEA,QAAI,KAAK,qBAAqB,GAAE,KAAK,qBAAqB;AAAA,EAAE;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,YAAU,EAAE,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,YAAYA,KAAE;AAAC,WAAO,KAAK,UAAQ,KAAK,WAAWA,GAAC,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,WAAU;AAAC,aAAQA,MAAE,GAAEA,MAAE,KAAK,cAAa,EAAEA,IAAE,CAAAQ,IAAGR,GAAC,IAAE,KAAK,UAAUA,GAAC;AAAE,WAAOQ,IAAG,SAAO,KAAK,cAAaA;AAAA,EAAE;AAAA,EAAC,MAAMR,KAAEG,KAAEM,KAAE;AAAC,IAAAA,GAAE,KAAK,OAAO,GAAE,KAAK,eAAa,KAAK,oBAAoBT,IAAE,WAAUA,IAAE,UAAU,GAAE,KAAK,oBAAoB;AAAE,UAAK,EAAC,MAAKO,KAAE,KAAIG,IAAC,IAAE,KAAK,cAAcD,GAAC;AAAE,SAAK,yBAAyBC,KAAEH,GAAC,GAAE,KAAK,eAAeP,KAAEG,GAAC;AAAE,UAAK,EAAC,YAAWE,KAAE,kBAAiBM,IAAC,IAAEX;AAAE,aAAQY,MAAE,GAAEA,MAAE,KAAK,cAAa,EAAEA,IAAE,MAAK,kBAAkBA,KAAED,KAAEN,KAAEF,GAAC;AAAE,SAAK,cAAY,MAAK,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,OAAOH,KAAE;AAAC,IAAAS,GAAE,KAAK,OAAO,GAAE,KAAK,MAAM,gBAAgBT,GAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBA,KAAE;AAAC,QAAG,CAAC,KAAK,eAAa,CAAC,EAAEA,KAAE,KAAK,WAAW,GAAE;AAAC,WAAK,cAAY,KAAK,eAAaM,GAAE,GAAED,GAAE,KAAK,aAAYL,GAAC;AAAE,eAAQG,MAAE,GAAEA,MAAE,KAAK,cAAa,EAAEA,KAAE;AAAC,QAAAO,GAAE,IAAG,KAAK,UAAUP,GAAC,EAAE,UAASH,GAAC;AAAE,iBAAQA,MAAE,GAAEA,MAAE,IAAG,EAAEA,IAAE,IAAG,KAAGG,MAAEH,GAAC,IAAE,GAAGA,GAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAE;AAAA,EAAC,sBAAsBA,KAAEG,KAAE;AAAC,IAAAM,GAAE,KAAK,OAAO;AAAE,UAAMA,MAAE,KAAK,gBAAgBN,GAAC;AAAE,SAAK,SAAS;AAAE,UAAMI,MAAE,KAAK,OAAMG,MAAEH,IAAE,YAAYE,KAAEI,GAAE,wBAAwB;AAAE,IAAAN,IAAE,GAAG,kBAAkB,EAAE,YAAW,GAAEP,IAAE,OAAO,SAAS,CAAC,GAAEA,IAAE,OAAO,SAAS,CAAC,GAAEA,IAAE,OAAO,SAAS,CAAC,GAAEA,IAAE,OAAO,SAAS,CAAC,GAAEA,IAAE,OAAO,SAAS,CAAC,GAAEA,IAAE,OAAO,SAAS,CAAC,CAAC,GAAEO,IAAE,YAAYG,KAAEG,GAAE,wBAAwB;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMb,MAAE,KAAK;AAAM,SAAK,SAAS,GAAEA,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,IAAE,UAAUc,GAAE,mBAAiBA,GAAE,gBAAgB;AAAA,EAAC;AAAA,EAAC,oBAAoBd,KAAEG,KAAE;AAAC,UAAMM,MAAE,MAAG,KAAK,IAAIT,MAAEA,MAAEG,MAAEA,GAAC,IAAE,KAAK,OAAMI,MAAE,MAAIG,MAAE,KAAG,KAAK,MAAMD,MAAEF,GAAC;AAAE,WAAO,KAAK,IAAI,KAAK,iBAAgB,IAAEG,GAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,QAAG,EAAE,KAAK,aAAa,KAAG,KAAK,cAAc,WAAW,UAAQ,KAAK,aAAa;AAAO,SAAK,qBAAqB;AAAE,UAAMV,MAAE,EAAC,QAAO,EAAE,YAAW,aAAYe,GAAE,MAAK,UAAS,EAAE,eAAc,UAASC,GAAE,eAAc,cAAa,EAAE,SAAQ,SAAQ,MAAG,OAAM,KAAK,cAAa,QAAO,KAAK,aAAY;AAAE,SAAK,gBAAc,IAAIH,GAAE,KAAK,OAAMb,GAAC,GAAE,KAAK,OAAK,IAAIiB,GAAE,KAAK,OAAM,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,qBAAoB,OAAM,KAAK,cAAa,QAAO,KAAK,aAAY,GAAE,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,gBAAgBjB,KAAE;AAAC,QAAIG,MAAE,KAAK,WAAWH,GAAC;AAAE,QAAG,EAAEG,GAAC,KAAGA,IAAE,WAAW,UAAQ,KAAK,aAAa,QAAOA;AAAE,SAAK,iBAAiBH,GAAC;AAAE,UAAMS,MAAE,EAAC,QAAO,EAAE,YAAW,aAAYM,GAAE,MAAK,UAAS,EAAE,eAAc,UAASC,GAAE,eAAc,cAAa,EAAE,SAAQ,SAAQ,MAAG,OAAM,KAAK,cAAa,QAAO,KAAK,aAAY;AAAE,WAAOb,MAAE,IAAIU,GAAE,KAAK,OAAMJ,GAAC,GAAE,KAAK,WAAWT,GAAC,IAAEG,KAAEA;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,SAAK,OAAK,EAAE,KAAK,IAAI,GAAE,KAAK,gBAAc,EAAE,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,iBAAiBH,KAAE;AAAC,SAAK,WAAWA,GAAC,IAAE,EAAE,KAAK,WAAWA,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,aAAQA,MAAE,GAAEA,MAAE,KAAK,WAAW,QAAO,EAAEA,IAAE,MAAK,iBAAiBA,GAAC;AAAE,SAAK,WAAW,SAAO;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,UAAMA,MAAE,KAAK;AAAM,IAAAA,IAAE,cAAc,KAAK,aAAa,GAAEA,IAAE,gBAAgB,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,kBAAkBA,KAAEG,KAAEM,KAAEF,KAAE;AAAC,UAAMG,MAAE,KAAK,UAAUV,GAAC,GAAEK,MAAE,CAAC,KAAK,kBAAkBL,GAAC,GAAEM,MAAE,CAAC,KAAK,kBAAkBN,MAAE,CAAC,GAAEkB,OAAGf,IAAE,EAAE,IAAEE,MAAEF,IAAE,EAAE,KAAG,KAAK,IAAIA,IAAE,EAAE,IAAEE,MAAEF,IAAE,EAAE,CAAC,GAAEW,OAAGX,IAAE,EAAE,IAAEG,MAAEH,IAAE,EAAE,KAAG,KAAK,IAAIA,IAAE,EAAE,IAAEG,MAAEH,IAAE,EAAE,CAAC;AAAE,IAAAM,GAAES,MAAEJ,GAAC;AAAE,aAAQH,MAAE,GAAEA,MAAE,GAAE,EAAEA,KAAE;AAAC,MAAAN,GAAEc,IAAER,MAAE,KAAG,KAAGA,MAAE,KAAG,IAAE,KAAG,GAAEA,MAAE,KAAG,KAAGA,MAAE,KAAG,IAAE,KAAG,GAAEA,MAAE,IAAEO,MAAEJ,KAAE,CAAC;AAAE,YAAMd,MAAEoB,GAAET,GAAC;AAAE,QAAEX,KAAEmB,IAAE,KAAK,sBAAsB,GAAEnB,IAAE,CAAC,KAAGA,IAAE,CAAC,GAAEA,IAAE,CAAC,KAAGA,IAAE,CAAC,GAAEA,IAAE,CAAC,KAAGA,IAAE,CAAC;AAAA,IAAC;AAAC,MAAEqB,KAAGD,GAAE,CAAC,CAAC,GAAEV,IAAE,OAAO,aAAWA,GAAEY,IAAE,KAAK,iBAAgBD,GAAE;AAAE,aAAQV,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAY,GAAEH,GAAET,GAAC,GAAES,GAAET,GAAC,GAAED,IAAE,OAAO,UAAU;AAAE,QAAIc,MAAEJ,GAAE,CAAC,EAAE,CAAC,GAAEK,MAAEL,GAAE,CAAC,EAAE,CAAC;AAAE,aAAQT,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAa,MAAE,KAAK,IAAIA,KAAEJ,GAAET,GAAC,EAAE,CAAC,CAAC,GAAEc,MAAE,KAAK,IAAIA,KAAEL,GAAET,GAAC,EAAE,CAAC,CAAC;AAAE,IAAAa,OAAG,KAAIC,OAAG,KAAIf,IAAE,OAAO,OAAK,CAACe,KAAEf,IAAE,OAAO,MAAI,CAACc,KAAE,GAAGf,KAAEF,KAAEiB,KAAEC,KAAEf,IAAE,MAAM,GAAEE,GAAEF,IAAE,UAASA,IAAE,OAAO,kBAAiBA,IAAE,OAAO,UAAU;AAAE,UAAMgB,KAAE,KAAK,eAAa;AAAE,IAAAhB,IAAE,OAAO,WAAS,CAACV,MAAE,KAAG,IAAE,IAAE0B,IAAE,MAAI,KAAK,MAAM1B,MAAE,CAAC,IAAE,IAAE0B,IAAEA,IAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAe1B,KAAEG,KAAE;AAAC,IAAAS,GAAE,KAAK,iBAAgBZ,IAAE,kBAAiBA,IAAE,UAAU,GAAEW,GAAE,KAAK,wBAAuB,KAAK,eAAe;AAAE,UAAMF,MAAE,KAAK,iBAAekB,IAAE,SAAO3B,IAAE,MAAIkB,GAAEG,KAAG,GAAE,GAAE,CAAC;AAAE,MAAE,KAAK,iBAAgB,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,CAAClB,IAAE,CAAC,GAAE,CAACA,IAAE,CAAC,GAAE,CAACA,IAAE,CAAC,CAAC,GAAEM,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcT,KAAE;AAAC,QAAG,EAAC,MAAKG,KAAE,KAAIM,IAAC,IAAET;AAAE,WAAOG,MAAE,MAAIA,MAAE,IAAGM,MAAE,MAAIA,MAAE,IAAGN,OAAGM,QAAIN,MAAE,GAAEM,MAAE,IAAG,EAAC,MAAKN,KAAE,KAAIM,IAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBT,KAAES,KAAE;AAAC,SAAK,eAAa,KAAK,IAAI,IAAE,KAAK,MAAMF,IAAEP,MAAES,KAAE,CAAC,CAAC,GAAE,KAAK,eAAe;AAAE,UAAMF,OAAGP,MAAES,OAAG,KAAK,cAAaC,OAAGV,MAAES,SAAK,IAAE,KAAK;AAAc,QAAIJ,MAAEI,KAAEE,MAAEF;AAAE,aAAQG,MAAE,GAAEA,MAAE,KAAK,eAAa,GAAE,EAAEA,IAAE,MAAK,kBAAkBA,GAAC,IAAED,GAAEN,KAAEM,KAAE,KAAK,kBAAkB,GAAEN,OAAGK,KAAEC,OAAGJ;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAJ/sN;AAIgtN,WAAO,KAAK,WAAW,OAAQ,CAACP,KAAEG,QAAIH,MAAEyB,GAAEtB,GAAC,KAAG,UAAK,SAAL,mBAAW,mBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,UAAMH,MAAE;AAAK,WAAM,EAAC,gBAAe,KAAK,iBAAgB,UAAS,KAAK,WAAU,aAAY,KAAK,cAAa,IAAI,kBAAkBG,KAAE;AAAC,MAAAH,IAAE,qBAAmBG;AAAA,IAAC,GAAE,IAAI,oBAAmB;AAAC,aAAOH,IAAE;AAAA,IAAkB,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMsB,KAAEnB,GAAE;AAAV,IAAYgB,KAAEb,GAAE;AAAhB,IAAkBc,KAAE,CAAC;AAAE,SAAQ,KAAG,GAAE,KAAG,GAAE,EAAE,GAAG,CAAAA,GAAE,KAAKd,GAAE,CAAC;AAAE,IAAMsB,MAAGtB,IAAE;AAAX,IAAauB,MAAGvB,IAAE;AAAlB,IAAoBwB,MAAGxB,IAAE;AAAzB,IAA2B,KAAGA,IAAE;AAAhC,IAAkCyB,MAAGzB,IAAE;AAAvC,IAAyCe,MAAGf,GAAE;AAA9C,IAAgDE,MAAG,CAAC;AAApD,IAAsD,KAAGL,GAAE;AAA3D,IAA6D,KAAG,IAAI,aAAa,EAAE;AAAnF,IAAqF,KAAGG,IAAE;AAA1F,IAA4F,KAAGA,IAAE;AAAjG,IAAmG,KAAG,CAACA,IAAE,GAAEA,IAAE,GAAEA,IAAE,GAAEA,IAAE,CAAC;AAAtH,IAAwH,KAAGA,IAAE;AAA7H,IAA+H,KAAGA,IAAE;AAApI,IAAsI,KAAGA,IAAE;AAA3I,IAA6I,KAAGA,IAAE;AAAlJ,IAAoJ,KAAGA,IAAE;AAAzJ,IAA2J,KAAGA,IAAE;AAAhK,IAAkK,KAAGA,IAAE;AAAE,SAAS,GAAGN,KAAEG,KAAEM,KAAEF,KAAEG,KAAEL,KAAEM,KAAEC,KAAE;AAAC,EAAAP,GAAE,IAAG,GAAE,CAAC;AAAE,WAAQmB,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAf,GAAE,IAAG,IAAGT,IAAEwB,GAAC,CAAC;AAAE,EAAAG,IAAE,IAAG,IAAG,IAAG,GAAEtB,GAAE,IAAG,GAAE,CAAC;AAAE,WAAQmB,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAf,GAAE,IAAG,IAAGT,IAAEwB,GAAC,CAAC;AAAE,EAAAG,IAAE,IAAG,IAAG,IAAG,GAAEK,GAAE,GAAG,CAAC,GAAEhC,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAEgC,GAAE,GAAG,CAAC,GAAEhC,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAEgC,GAAE,GAAG,CAAC,GAAEhC,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAEgC,GAAE,GAAG,CAAC,GAAEhC,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,GAAE;AAAE,MAAIM,MAAE,GAAEY,MAAEe,GAAE,GAAG,CAAC,GAAE,EAAE;AAAE,WAAQT,MAAE,GAAEA,MAAE,GAAE,EAAEA,KAAE;AAAC,UAAMxB,MAAEiC,GAAE,GAAGT,GAAC,GAAE,EAAE;AAAE,IAAAxB,MAAEkB,QAAIA,MAAElB,KAAEM,MAAEkB;AAAA,EAAE;AAAC,EAAAN,GAAE,IAAG,GAAGZ,GAAC,GAAEN,IAAEM,MAAE,CAAC,CAAC;AAAE,QAAMQ,MAAE,GAAG,CAAC;AAAE,MAAIoB,IAAEC;AAAE,KAAG,CAAC,IAAE,CAAC,GAAG,CAAC,GAAE,GAAG,CAAC,IAAErB,KAAEI,GAAE,IAAG,IAAG,EAAE,GAAEkB,GAAE,IAAG,EAAE,IAAE,KAAGC,GAAE,IAAG,EAAE,GAAEL,GAAE,IAAG,IAAG,IAAGvB,GAAC,GAAE6B,GAAE,IAAG,EAAE,GAAEJ,KAAEC,KAAEC,GAAElB,GAAE,IAAGlB,IAAE,CAAC,GAAE,EAAE,GAAE,EAAE;AAAE,WAAQwB,MAAE,GAAEA,MAAE,GAAE,EAAEA,KAAE;AAAC,UAAMrB,MAAEiC,GAAElB,GAAE,IAAGlB,IAAEwB,GAAC,GAAE,EAAE,GAAE,EAAE;AAAE,IAAArB,MAAE+B,KAAEA,KAAE/B,MAAEA,MAAEgC,OAAIA,KAAEhC;AAAA,EAAE;AAAC,EAAAI,GAAEA,KAAE,EAAE,GAAEoB,IAAE,IAAG,IAAGO,KAAE/B,GAAC,GAAEM,GAAEF,KAAEA,KAAE,EAAE;AAAE,MAAIgC,KAAE,IAAGC,KAAE,GAAExB,KAAE,GAAEH,MAAE;AAAE,WAAQW,MAAE,GAAEA,MAAE,GAAE,EAAEA,KAAE;AAAC,IAAAN,GAAE,IAAGlB,IAAEwB,GAAC,GAAEjB,GAAC,GAAE+B,GAAE,IAAG,EAAE;AAAE,UAAMnC,MAAE,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC;AAAE,IAAAA,MAAE,IAAEA,MAAEoC,OAAIA,KAAEpC,KAAEa,KAAEQ,OAAGrB,MAAEqC,OAAIA,KAAErC,KAAEU,MAAEW;AAAA,EAAE;AAAC,EAAAZ,GAAE2B,KAAE,GAAE,UAAU,GAAE3B,GAAE4B,KAAE,GAAE,WAAW,GAAEb,IAAE,IAAG,IAAGO,EAAC,GAAEzB,GAAE,IAAG,IAAG,EAAE,GAAEkB,IAAE,IAAG,IAAGQ,EAAC,GAAE1B,GAAE,IAAG,IAAG,EAAE,GAAE,GAAG,CAAC,IAAE,CAAC,GAAG,CAAC,GAAE,GAAG,CAAC,IAAE,GAAG,CAAC;AAAE,QAAM2B,MAAEK,GAAElC,KAAEP,IAAEa,GAAC,GAAE,IAAGJ,GAAE,IAAG,IAAG,EAAE,GAAE,GAAEC,GAAC,GAAE4B,MAAEG,GAAElC,KAAEP,IAAEgB,EAAC,GAAE,IAAG,IAAG,GAAEX,GAAC,GAAEqC,KAAED,GAAElC,KAAEP,IAAEgB,EAAC,GAAE,IAAGP,GAAE,IAAG,IAAG,EAAE,GAAE,GAAEE,GAAC,GAAEgC,KAAEF,GAAElC,KAAEP,IAAEa,GAAC,GAAE,IAAG,IAAG,GAAED,GAAC;AAAE,EAAAA,GAAEwB,KAAE,QAAQ,GAAExB,GAAE0B,KAAE,QAAQ,GAAE1B,GAAE8B,IAAE,QAAQ,GAAE9B,GAAE+B,IAAE,QAAQ;AAAC;AAAC,SAAS,GAAG3C,KAAEG,KAAE;AAAC,SAAO,IAAEA,MAAEH;AAAC;AAAC,IAAM,KAAGM,IAAE;AAAE,SAAS,GAAGN,KAAEG,KAAE;AAAC,SAAOE,GAAE,IAAGL,IAAEG,GAAC,GAAEH,IAAEG,MAAE,CAAC,CAAC,GAAE;AAAE;AAAC,IAAM,KAAGG,IAAE;AAAX,IAAa,KAAGH,GAAE;AAAE,SAAS,GAAGH,KAAEG,KAAEM,KAAEF,KAAEG,KAAE;AAAC,EAAAQ,GAAE,IAAGT,KAAEF,GAAC,GAAEoB,IAAE,IAAG,IAAG,GAAE,GAAE,GAAG,CAAC,IAAE,GAAG,CAAC,GAAE,GAAG,CAAC,IAAE,GAAG,CAAC,GAAE,GAAG,CAAC,IAAE,GAAE,GAAG,CAAC,IAAE,GAAG,CAAC,GAAE,GAAG,CAAC,IAAE,CAAC,GAAG,CAAC,GAAE,GAAG,CAAC,IAAE,GAAE,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,GAAE,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE,GAAG,CAAC,GAAE,GAAG,CAAC,IAAE,GAAE,GAAG,GAAG,GAAE,CAAC,CAAC,IAAE,CAACS,GAAE,GAAG,IAAG,CAAC,GAAEpC,GAAC,GAAE,GAAG,GAAG,GAAE,CAAC,CAAC,IAAE,CAACoC,GAAE,GAAG,IAAG,CAAC,GAAEpC,GAAC;AAAE,MAAIK,MAAE+B,GAAE,GAAG,IAAG,CAAC,GAAE3B,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAEE,MAAEyB,GAAE,GAAG,IAAG,CAAC,GAAE3B,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAEG,MAAEwB,GAAE,GAAG,IAAG,CAAC,GAAE7B,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAED,MAAE8B,GAAE,GAAG,IAAG,CAAC,GAAE7B,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC;AAAE,EAAAF,MAAE,EAAEA,MAAEO,QAAID,MAAEL,MAAG,GAAG,GAAG,GAAE,CAAC,CAAC,KAAG,GAAG,GAAG,GAAE,CAAC,CAAC,IAAED,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAG,GAAG,GAAG,GAAE,CAAC,CAAC,IAAEA,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAG,GAAG,GAAG,GAAE,CAAC,CAAC,IAAEA,KAAEA,MAAE,KAAG+B,GAAE,GAAG,IAAG,CAAC,GAAE3B,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,IAAGE,MAAE,KAAGyB,GAAE,GAAG,IAAG,CAAC,GAAE3B,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,IAAG,GAAG,GAAG,GAAE,CAAC,CAAC,KAAGJ,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAGA,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAGA,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAGM,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAGA,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAGA,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAE,GAAG,GAAG,GAAE,CAAC,CAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAE,GAAG,GAAG,GAAE,CAAC,CAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAG,GAAEN,MAAE+B,GAAE,GAAG,IAAG,CAAC,GAAEjC,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAEQ,MAAEyB,GAAE,GAAG,IAAG,CAAC,GAAEjC,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAES,MAAEwB,GAAE,GAAG,IAAG,CAAC,GAAE3B,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAEH,MAAE8B,GAAE,GAAG,IAAG,CAAC,GAAE3B,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAEJ,MAAE,QAAKA,MAAEM,MAAEC,MAAEN,MAAG,GAAG,GAAG,GAAE,CAAC,CAAC,KAAG,GAAG,GAAG,GAAE,CAAC,CAAC,IAAED,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAG,GAAG,GAAG,GAAE,CAAC,CAAC,IAAEA,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAG,GAAG,GAAG,GAAE,CAAC,CAAC,IAAEA,KAAEA,MAAE+B,GAAE,GAAG,IAAG,CAAC,GAAEjC,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAEQ,MAAEyB,GAAE,GAAG,IAAG,CAAC,GAAEjC,GAAC,IAAE,GAAG,GAAG,GAAE,CAAC,CAAC,GAAES,MAAE,CAACD,MAAEN,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAGO,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAGA,KAAE,GAAG,GAAG,GAAE,CAAC,CAAC,KAAGA,KAAEF,IAAE,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAG,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,EAAE,IAAE,GAAEA,IAAE,EAAE,IAAE,GAAEA,IAAE,EAAE,IAAE,GAAG,CAAC,GAAEA,IAAE,EAAE,IAAE,GAAG,CAAC,GAAEA,IAAE,EAAE,IAAE,GAAEA,IAAE,EAAE,IAAE,GAAG,CAAC;AAAC;AAAC,SAAS,GAAGV,KAAEG,KAAEI,KAAEG,KAAEL,KAAE;AAAC,QAAMM,MAAE,IAAES,GAAE,CAAC,EAAE,CAAC,GAAER,MAAE,IAAEQ,GAAE,CAAC,EAAE,CAAC;AAAE,EAAAX,GAAEE,MAAEC,GAAC;AAAE,MAAIN,MAAEK,MAAE,KAAK,KAAKA,MAAEC,GAAC;AAAE,QAAMM,MAAE,KAAK,IAAIS,GAAE3B,IAAE,CAAC,IAAEG,IAAE,CAAC,IAAEH,IAAE,CAAC,IAAEG,IAAE,CAAC,IAAEH,IAAE,EAAE,IAAEG,IAAE,CAAC,CAAC,CAAC;AAAE,EAAAG,OAAGY,KAAE,GAAGE,IAAEd,KAAEY,KAAEU,KAAGC,KAAGC,KAAG,IAAGC,GAAE,GAAE,GAAGH,KAAGC,KAAG,IAAGE,KAAG1B,IAAE,gBAAgB,GAAEA,IAAE,iBAAiB,EAAE,IAAE,KAAGE,MAAEG,MAAGL,IAAE,iBAAiB,EAAE,IAAE,EAAEE,MAAEG,QAAIH,MAAEG;AAAE;;;ACAx2S,IAAMkC,MAAN,cAAgBA,IAAC;AAAA,EAAC,YAAYC,KAAEC,KAAEC,KAAE;AAAC,UAAMF,KAAEC,GAAC,GAAE,KAAK,aAAWC;AAAA,EAAC;AAAC;;;ACAjU,IAAMC,MAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,OAAK,IAAIC,MAAE,KAAK,UAAQ,IAAIA,MAAE,KAAK,UAAQ,IAAIA,GAAE,EAAC,WAAU,CAAAC,QAAGA,OAAG,IAAIC,OAAE,aAAY,CAAAD,SAAIA,IAAE,iBAAe,MAAKA,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAK,MAAM,GAAE,KAAK,QAAQ,MAAM,GAAE,KAAK,QAAQ,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAK,MAAM,GAAE,KAAK,QAAQ,MAAM,GAAE,KAAK,QAAQ,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,MAAI,KAAK,KAAK,UAAQ,MAAI,KAAK,QAAQ,UAAQ,MAAI,KAAK,QAAQ;AAAA,EAAM;AAAC;AAAC,IAAMC,MAAN,MAAO;AAAC;AAAC,IAAMC,MAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,OAAK,IAAI,SAAM,KAAK,UAAQ,IAAI,SAAM,KAAK,UAAQ,IAAI;AAAA,EAAK;AAAC;;;ACAjgB,IAAIC;AAAJ,IAAMC;AAAE,CAAC,SAASD,KAAE;AAAC,EAAAA,IAAEA,IAAE,MAAI,CAAC,IAAE,OAAMA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,IAAEA,IAAE,SAAO,CAAC,IAAE;AAAQ,EAAEA,QAAIA,MAAE,CAAC,EAAE,GAAE,SAASA,KAAE;AAAC,EAAAA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,aAAW,CAAC,IAAE,cAAaA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,iBAAe,CAAC,IAAE,kBAAiBA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,IAAEA,IAAE,WAAS,EAAE,IAAE;AAAU,EAAEC,OAAIA,KAAE,CAAC,EAAE;;;ACAhP,SAASC,IAAEA,KAAE;AAAC,QAAMC,MAAE,oBAAI,OAAIC,MAAE,CAAAF,QAAG;AAAC,QAAIG,MAAEF,IAAE,IAAID,GAAC;AAAE,WAAOG,QAAIA,MAAE,IAAIA,OAAEF,IAAE,IAAID,KAAEG,GAAC,IAAGA;AAAA,EAAC;AAAE,SAAOH,IAAE,QAAQ,OAAQ,CAAAI,QAAG;AAAC,IAAAD,IAAEC,GAAC,KAAGF,IAAEE,IAAE,QAAQ,EAAE,QAAQ,KAAKA,GAAC;AAAA,EAAC,CAAE,GAAEJ,IAAE,KAAK,OAAQ,CAAAI,QAAG;AAAC,IAAAD,IAAEC,GAAC,KAAGF,IAAEE,IAAE,QAAQ,EAAE,KAAK,KAAKA,GAAC;AAAA,EAAC,CAAE,GAAEJ,IAAE,QAAQ,OAAQ,CAAAI,QAAG;AAAC,IAAAD,IAAEC,IAAE,cAAc,KAAGF,IAAEE,IAAE,eAAe,QAAQ,EAAE,QAAQ,KAAKA,GAAC;AAAA,EAAC,CAAE,GAAEH;AAAC;AAAC,SAASE,IAAEC,KAAE;AAAC,SAAOA,IAAE,SAAS,cAAY;AAAC;;;ACA9O,IAAMC,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAE;AAAC,SAAK,QAAMA,KAAE,KAAK,eAAa,KAAK,mBAAmB,GAAE,KAAK,WAAS,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,UAAMA,MAAE,oGAAmGC,MAAE;AAAiF,WAAO,KAAK,MAAM,aAAa,QAAQD,KAAEC,KAAE,oBAAI,IAAI,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAOC,GAAE,YAAY,KAAK,OAAMC,GAAE,aAAY,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,SAAS,YAAU,KAAK,iBAAe,KAAK,MAAM,QAAQ,IAAI,GAAE,KAAK,MAAM,WAAW,KAAK,QAAQ,GAAE,KAAK,MAAM,WAAW,KAAK,cAAaC,GAAE,oBAAoB,GAAE,KAAK,MAAM,aAAaF,GAAE,QAAO,GAAEG,GAAE,cAAa,CAAC;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,SAAS,QAAQ,GAAE,KAAK,aAAa,QAAQ;AAAA,EAAC;AAAC;;;ACA5zB,IAAMC,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,KAAE;AAAC,SAAK,YAAUD,KAAE,KAAK,cAAYC,KAAE,KAAK,OAAK,oBAAI;AAAA,EAAG;AAAA,EAAC,UAAS;AAAC,SAAK,KAAK,QAAS,CAACA,KAAEF,QAAI;AAAC,QAAEE,GAAC,KAAG,KAAK,YAAY,QAAQ,KAAK,WAAUF,GAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,KAAKA,KAAEG,KAAEC,KAAE;AAAC,QAAG,CAAC,KAAK,UAAU,aAAaD,KAAEC,GAAC,EAAE,QAAO;AAAK,SAAK,KAAK,IAAIA,GAAC,KAAG,KAAK,KAAK,IAAIA,KAAE,KAAK,YAAY,QAAQ,KAAK,WAAUD,KAAEC,GAAC,CAAC;AAAE,UAAMC,MAAE,KAAK,KAAK,IAAID,GAAC;AAAE,QAAG,EAAEC,GAAC,GAAE;AAAC,UAAGA,IAAE,gBAAgBL,GAAC,MAAIM,GAAE,OAAO,QAAOD;AAAE,WAAK,YAAY,cAAc;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAC;;;ACAnY,IAAME,MAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,MAAEC,GAAE,GAAE;AAAC,UAAM,GAAE,KAAK,SAAOD,KAAE,KAAK,wBAAsB,KAAK;AAAA,EAAM;AAAC;;;ACArH,IAAME,KAAEC,GAAE,EAAE,MAAMC,GAAE,QAAQ;AAA5B,IAA8BC,MAAEF,GAAE,EAAE,MAAMC,GAAE,QAAQ,EAAE,MAAMA,GAAE,GAAG;AAAjE,IAAmEE,MAAEH,GAAE,EAAE,MAAMC,GAAE,QAAQ,EAAE,OAAOA,GAAE,KAAK;AAAzG,IAA2GG,MAAEJ,GAAE,EAAE,MAAMC,GAAE,QAAQ,EAAE,OAAOA,GAAE,qBAAqB;AAAjK,IAAmKI,MAAEL,GAAE,EAAE,MAAMC,GAAE,QAAQ,EAAE,MAAMA,GAAE,GAAG,EAAE,OAAOA,GAAE,qBAAqB;AAAtO,IAAwOK,MAAEN,GAAE,EAAE,MAAMC,GAAE,QAAQ,EAAE,OAAOA,GAAE,KAAK,EAAE,OAAOA,GAAE,qBAAqB;;;ACA3T,IAAMM,MAAN,cAAgBC,GAAC;AAAA,EAAC,UAAUC,KAAEF,KAAEG,KAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAOC,GAAEL,KAAEC,KAAEC,KAAEC,KAAE,QAAOC,GAAC;AAAA,EAAC;AAAC;;;ACAsmC,IAAME,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,wBAAwBA,KAAEC,KAAE;AAAC,IAAAA,IAAE,mBAAiBD,IAAE,KAAK,SAAOE,IAAE,QAAOD,IAAE,YAAUD,IAAE,gBAAcG,IAAE,QAAOF,IAAE,qCAAmCD,IAAE,KAAK,WAAW,mCAAmC;AAAA,EAAM;AAAA,EAAC,kBAAkBA,KAAE;AAAC,WAAO,IAAIC,IAAED,IAAE,MAAK,GAAE,OAAO,IAAI,EAAE,MAAM,KAAK,aAAa,GAAEI,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBJ,KAAE;AAAC,UAAMK,MAAE,KAAK,eAAcH,MAAEF,QAAIC,IAAE,MAAKK,MAAEN,QAAIC,IAAE;AAAU,WAAO,EAAE,EAAC,UAASI,IAAE,WAASE,GAAE,UAAQF,IAAE,WAASE,GAAE,aAAWF,IAAE,cAAYH,MAAEM,KAAEC,GAAET,GAAC,IAAE,MAAK,WAAU,EAAC,MAAKG,IAAEH,GAAC,EAAC,GAAE,YAAWE,MAAEG,IAAE,aAAWK,MAAE,OAAKN,GAAEJ,GAAC,GAAE,YAAWW,IAAE,eAAcT,OAAGI,MAAE,OAAKI,IAAEL,IAAE,YAAY,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,KAAK,kBAAkB,KAAK,cAAc,oBAAoB;AAAA,EAAC;AAAC;AAACN,GAAE,SAAO,IAAIM,GAAEM,IAAG,MAAI,OAAO,iCAAiC,CAAE;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAON,GAAE,OAAM,KAAK,uBAAqBN,IAAE,MAAK,KAAK,YAAU,OAAG,KAAK,iBAAe,OAAG,KAAK,gBAAc,OAAG,KAAK,cAAY,OAAG,KAAK,eAAa,MAAG,KAAK,aAAW,OAAG,KAAK,4BAA0B,OAAG,KAAK,qCAAmC,OAAG,KAAK,uBAAqB,OAAG,KAAK,WAAS,OAAG,KAAK,sBAAoB,OAAG,KAAK,kBAAgB;AAAA,EAAE;AAAC;AAAC,EAAE,CAACC,IAAE,EAAC,OAAMK,GAAE,MAAK,CAAC,CAAC,GAAEK,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACV,IAAE,EAAC,OAAMD,IAAE,MAAK,CAAC,CAAC,GAAEW,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,sCAAqC,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAACV,IAAE,CAAC,GAAEU,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACV,IAAE,EAAC,YAAWY,GAAE,MAAK,CAAC,CAAC,GAAEF,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACV,IAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAEU,GAAE,WAAU,gCAA+B,MAAM,GAAE,EAAE,CAACV,IAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAEU,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAACV,IAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAEU,GAAE,WAAU,iBAAgB,MAAM;;;ACArnG,IAAMG,MAAN,cAAgBC,GAAC;AAAA,EAAC,mBAAmBC,KAAE;AAAC,IAAAA,IAAE,UAAU,YAAU,KAAK,UAAU,WAAW,kBAAgB,KAAK,UAAU,cAAc,EAAC,gBAAeA,IAAE,UAAU,QAAO,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,KAAE;AAAC,IAAAA,IAAE,IAAI,YAAU,KAAK,UAAU,WAAW,6BAA2B,KAAK,UAAU,cAAc,EAAC,2BAA0BA,IAAE,IAAI,QAAO,CAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BD,KAAE;AAAC,UAAME,MAAE,EAAEF,IAAE,WAAW,IAAI;AAAE,IAAAE,QAAI,KAAK,UAAU,WAAW,wBAAsB,KAAK,UAAU,cAAc,EAAC,sBAAqBA,IAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBD,KAAE;AAAC,WAAO,KAAK,qBAAqB,oBAAoB,uBAAuB,gBAAgBA,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,WAAO,KAAK,YAAUE,GAAE,UAAQ,KAAK,mBAAmBF,GAAC,GAAE,KAAK,gBAAgBA,GAAC,GAAE,KAAK,6BAA6BA,GAAC,IAAG,KAAK,UAAU,cAAc,KAAK,qBAAqB,oBAAoB,uBAAuB,cAAc,GAAE,KAAK,gBAAgBG,IAAEH,GAAC;AAAA,EAAC;AAAC;;;ACAjU,IAAMI,MAAN,cAAgBC,IAAC;AAAA,EAAC,YAAYA,KAAE;AAAC,UAAMA,KAAE,IAAIC,KAAC,GAAE,KAAK,iBAAe,IAAIC,MAAE,KAAK,YAAU,IAAIC;AAAA,EAAC;AAAA,EAAC,iBAAiBH,KAAEI,KAAE;AAAC,WAAO,KAAK,eAAe,SAAOJ,KAAE,KAAK,eAAe,aAAW,KAAK,WAAW,YAAW,KAAK,eAAe,iBAAe,KAAK,WAAW,gBAAe,KAAK,eAAe,gBAAc,KAAK,WAAW,eAAc,KAAK,eAAe,cAAY,KAAK,WAAW,aAAY,KAAK,eAAe,4BAA0B,KAAK,WAAW,2BAA0B,KAAK,eAAe,uBAAqB,KAAK,WAAW,sBAAqB,KAAK,eAAe,WAAS,KAAK,WAAW,UAAS,KAAK,eAAe,uBAAqBI,IAAE,sBAAqB,KAAK,eAAe,eAAaA,IAAE,OAAO,oBAAkBC,IAAE,KAAK,eAAe,sBAAoBD,IAAE,iBAAiB,SAAQ,KAAK,eAAe,kBAAgBA,IAAE,iBAAiB,iBAAgB,KAAK;AAAA,EAAc;AAAA,EAAC,OAAOA,KAAE;AAAC,UAAME,MAAE,KAAK,IAAIF,IAAE,OAAO,mBAAkBA,IAAE,OAAO,QAAQ;AAAE,SAAK,UAAU,UAAQ,KAAK,KAAK,KAAK,WAAW,oBAAkB,KAAK,WAAW,YAAY,IAAEE,MAAEC;AAAE,UAAMJ,MAAE,KAAK,UAAU,QAAQC,GAAC;AAAE,WAAO,KAAK,cAAc,EAAC,aAAY,EAAE,KAAK,UAAU,IAAI,IAAE,KAAK,WAAW,eAAc,GAAE,KAAE,GAAE,KAAK,UAAU,WAASD;AAAA,EAAC;AAAA,EAAC,aAAaH,KAAEI,KAAE;AAAC,YAAOA,KAAE;AAAA,MAAC,KAAKI,GAAE;AAAO,eAAOR,QAAIS,GAAE;AAAA,MAAa,KAAKD,GAAE;AAAM,YAAG,KAAK,WAAW,SAAS,QAAOR,QAAIS,GAAE;AAAgB;AAAA,MAAM,KAAKD,GAAE;AAAM;AAAA,MAAM,KAAKA,GAAE;AAAU,eAAOR,QAAIS,GAAE,mBAAiBT,QAAIS,GAAE;AAAA,MAAgB;AAAQ,eAAM;AAAA,IAAE;AAAC,QAAIH,MAAEG,GAAE;AAAgB,WAAO,KAAK,WAAW,gBAAcH,MAAE,KAAK,WAAW,aAAWG,GAAE,uBAAqBA,GAAE,4CAA2CT,QAAIM;AAAA,EAAC;AAAA,EAAC,iBAAiBN,KAAE;AAAC,WAAO,IAAIM,IAAEN,GAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,IAAIM,IAAEI,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMT,MAAN,cAAgBF,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,MAAI,KAAK,oBAAkB,IAAG,KAAK,gBAAcO,GAAE,GAAE,CAAC,GAAE,KAAK,eAAa,MAAI,KAAK,eAAa,OAAK,KAAK,aAAW,MAAI,KAAK,iBAAe,MAAI,KAAK,cAAY,GAAE,KAAK,QAAMA,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,cAAY,MAAG,KAAK,aAAW,MAAG,KAAK,gBAAc,OAAG,KAAK,WAAS,OAAG,KAAK,iBAAe,MAAG,KAAK,4BAA0B,OAAG,KAAK,uBAAqB;AAAA,EAAE;AAAC;AAAC,IAAMC,KAAE;;;ACAh3F,IAAMI,MAAN,MAAO;AAAA,EAAC,YAAYA,MAAE,GAAEC,MAAE,GAAE;AAAC,SAAK,OAAKD,KAAE,KAAK,KAAGC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,KAAG,KAAK;AAAA,EAAI;AAAC;AAAC,SAASA,IAAED,KAAE;AAAC,QAAMC,MAAE,oBAAI;AAAI,EAAAD,IAAE,OAAQ,CAAAA,QAAGC,IAAE,IAAID,IAAE,MAAKA,GAAC,CAAE;AAAE,MAAIE,MAAE;AAAG,SAAKA,MAAG,CAAAA,MAAE,OAAGF,IAAE,QAAS,CAAAG,QAAG;AAAC,UAAMC,MAAEH,IAAE,IAAIE,IAAE,EAAE;AAAE,IAAAC,QAAID,IAAE,KAAGC,IAAE,IAAGH,IAAE,OAAOG,IAAE,IAAI,GAAEJ,IAAE,gBAAgBI,GAAC,GAAEF,MAAE;AAAA,EAAG,CAAE;AAAC;;;ACA5K,IAAMG,MAAN,cAAgBA,IAAC;AAAA,EAAC,YAAYC,KAAEC,KAAEF,KAAE;AAAC,UAAME,KAAEF,GAAC,GAAE,KAAK,WAASC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,SAAS;AAAA,EAAO;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,EAAE,KAAK,SAAS,UAAU,KAAG,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,EAAE,KAAK,SAAS,SAAS;AAAA,EAAC;AAAC;;;ACA9U,IAAME,MAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,QAAM,GAAE,KAAK,QAAM;AAAA,EAAC;AAAC;;;ACAsJ,IAAMC,MAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,eAAa,GAAE,KAAK,aAAW,oBAAI,OAAI,KAAK,QAAM,IAAIC,GAAE,EAAC,WAAU,CAAAC,QAAGA,OAAG,IAAIC,OAAE,aAAY,KAAI,CAAC,GAAE,KAAK,qBAAmB,OAAG,KAAK,gBAAc,OAAG,KAAK,eAAa,OAAG,KAAK,oBAAkB,MAAG,KAAK,sBAAoBC,IAAE,GAAE,KAAK,wBAAsBA,IAAE,GAAE,KAAK,wBAAsBA,IAAE,GAAE,KAAK,kCAAgCA,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,YAAYF,KAAEC,KAAE;AAAC,SAAK,eAAeD,GAAC,GAAE,KAAK,WAAW,IAAIA,KAAEC,GAAC,GAAE,KAAK,gBAAcA,IAAE;AAAA,EAAW;AAAA,EAAC,eAAeD,KAAE;AAAC,UAAMC,MAAE,KAAK,WAAW,IAAID,GAAC;AAAE,IAAAC,QAAI,KAAK,gBAAcA,IAAE,aAAY,KAAK,WAAW,OAAOD,GAAC;AAAA,EAAE;AAAA,EAAC,eAAeA,KAAEC,KAAEE,KAAE;AAAC,UAAMC,MAAE,KAAK,WAAW,IAAIJ,GAAC;AAAE,IAAAI,QAAI,KAAK,gBAAcA,IAAE,aAAYA,IAAE,OAAKH,KAAEG,IAAE,KAAGD,KAAE,KAAK,gBAAcC,IAAE;AAAA,EAAY;AAAA,EAAC,gBAAgBJ,KAAE;AAAC,IAAAA,IAAE,aAAWA,IAAE,kBAAgB,KAAK,gBAAc,OAAIA,IAAE,iBAAe,KAAK,eAAa,SAAK,KAAK,qBAAmB;AAAA,EAAE;AAAA,EAAC,mBAAmBC,KAAE;AAAC,QAAG,KAAK,oBAAoB,MAAM,GAAE,KAAK,sBAAsB,MAAM,GAAE,KAAK,sBAAsB,MAAM,GAAE,KAAK,gCAAgC,MAAM,GAAE,KAAK,oBAAkB,OAAG,MAAI,KAAK,WAAW,KAAK;AAAO,QAAG,CAAC,KAAK,sBAAsB,GAAE;AAAC,YAAME,MAAE,KAAK,oBAAoB,QAAQ,GAAEC,MAAE,KAAK,MAAM,MAAM;AAAE,aAAO,EAAE,KAAK,GAAG,KAAG,MAAI,KAAK,MAAM,UAAQA,IAAE,OAAK,KAAK,MAAM,KAAK,IAAI,OAAKH,GAAC,KAAGE,IAAE,QAAM,GAAE,MAAKA,IAAE,QAAMC,IAAE,UAAQD,IAAE,QAAM,IAAE,GAAEA,IAAE,QAAM,GAAE,KAAK,WAAW,QAAS,CAAAH,QAAG;AAAC,QAAAG,IAAE,QAAM,KAAK,IAAIA,IAAE,OAAMH,IAAE,IAAI,GAAEG,IAAE,QAAM,KAAK,IAAIA,IAAE,OAAMH,IAAE,EAAE;AAAA,MAAC,CAAE,GAAE,MAAKG,IAAE,SAAOA,IAAE;AAAA,IAAO;AAAC,UAAMA,MAAE,MAAM,KAAK,KAAK,WAAW,OAAO,CAAC,EAAE,KAAM,CAACH,KAAEC,QAAID,IAAE,SAAOC,IAAE,OAAKD,IAAE,KAAGC,IAAE,KAAGD,IAAE,OAAKC,IAAE,IAAK;AAAE,eAAUD,OAAKG,IAAE,CAAAH,IAAE,cAAYK,IAAEL,IAAE,eAAa,KAAK,wBAAsB,KAAK,qBAAoBA,GAAC,GAAEK,IAAEL,IAAE,gBAAc,KAAK,wBAAsB,KAAK,iCAAgCA,GAAC;AAAA,EAAE;AAAA,EAAC,wBAAuB;AAAC,WAAO,KAAK,gBAAc,KAAK,iBAAe,KAAK;AAAA,EAAkB;AAAC;AAAoB,SAASM,IAAEC,KAAE;AAAC,SAAO,EAAEA,IAAE,GAAG;AAAC;AAAC,SAASC,MAAG;AAAC,SAAO,IAAIC,GAAE,EAAC,WAAU,CAAAC,QAAGA,OAAG,IAAIH,OAAE,aAAY,CAAAG,QAAGA,IAAC,CAAC;AAAC;AAAC,SAASC,IAAED,KAAEH,KAAE;AAAC,QAAMK,MAAEF,IAAE,KAAK;AAAE,MAAG,QAAME,KAAE;AAAC,UAAMA,MAAEF,IAAE,QAAQ;AAAE,WAAOE,IAAE,QAAML,IAAE,MAAK,MAAKK,IAAE,QAAML,IAAE;AAAA,EAAY;AAAC,MAAGM,GAAED,KAAEL,GAAC,GAAE;AAAC,UAAMG,MAAEH,IAAE,OAAKK,IAAE,QAAML,IAAE;AAAY,IAAAK,IAAE,QAAMF;AAAA,EAAC,OAAK;AAAC,UAAME,MAAEF,IAAE,QAAQ;AAAE,IAAAE,IAAE,QAAML,IAAE,MAAKK,IAAE,QAAML,IAAE;AAAA,EAAW;AAAC;AAAC,SAASM,GAAEH,KAAEH,KAAE;AAAC,SAAOG,IAAE,QAAMA,IAAE,SAAOH,IAAE;AAAI;;;ACA17E,IAAMO,MAAN,MAAO;AAAA,EAAC,YAAYA,KAAE;AAAC,SAAK,SAAOA,KAAE,KAAK,UAAQ,IAAI;AAAA,EAAK;AAAA,EAAC,UAAS;AAAC,SAAK,QAAQ,QAAS,CAAAA,QAAGA,IAAE,IAAI,QAAQ,CAAE,GAAE,KAAK,QAAQ,SAAO;AAAA,EAAC;AAAA,EAAC,WAAWA,KAAE;AAAC,WAAO,KAAK,QAAQ,KAAM,CAAAC,QAAGA,IAAE,UAAU,IAAID,GAAC,CAAE;AAAA,EAAC;AAAC;;;ACA0L,IAAME,MAAEC,KAAE;AAAE,IAAMC,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,KAAEC,KAAE;AAAC,SAAK,QAAMF,KAAE,KAAK,aAAWC,KAAE,KAAK,UAAQC,KAAE,KAAK,SAAOF,IAAE,SAAS,YAAYC,GAAE,CAAC,IAAGE,GAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,OAAO,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAOF,KAAE;AAAC,UAAMC,MAAED,IAAE,SAAS,GAAEG,MAAE,KAAK,OAAO,IAAIF,GAAC;AAAE,QAAG,EAAEE,GAAC,GAAE;AAAC,YAAMJ,MAAEI,IAAE,IAAI;AAAE,aAAOA,IAAE,SAAO,KAAG,KAAK,OAAO,IAAIF,KAAEE,KAAEH,MAAEG,IAAE,QAAOP,GAAC,GAAEG;AAAA,IAAC;AAAC,UAAMF,MAAE,IAAIM,IAAE,KAAK,OAAM,KAAK,YAAW,EAAC,UAAS,KAAK,QAAO,GAAE,EAAC,UAASC,GAAE,aAAa,KAAK,OAAMC,GAAE,WAAW,EAAC,CAAC;AAAE,WAAOR,IAAE,cAAc,SAAS,QAAQG,GAAC,GAAEH;AAAA,EAAC;AAAA,EAAC,UAAUI,KAAE;AAAC,QAAG,EAAEA,GAAC,EAAE,QAAO;AAAK,UAAME,MAAEF,IAAE,MAAKJ,MAAEM,IAAE,SAAS,GAAEG,MAAE,KAAK,OAAO,IAAIT,GAAC;AAAE,WAAO,EAAES,GAAC,KAAGA,IAAE,KAAKL,GAAC,GAAE,KAAK,OAAO,IAAIJ,KAAES,KAAEH,MAAEG,IAAE,QAAO,EAAE,KAAG,KAAK,OAAO,IAAIT,KAAE,CAACI,GAAC,GAAEE,KAAE,EAAE,GAAE;AAAA,EAAI;AAAC;AAAC,SAASD,IAAEH,KAAEC,KAAE;AAAC,MAAGA,QAAIO,GAAE,IAAI,QAAO,KAAKR,IAAE,QAAS,CAAAA,QAAGA,IAAE,QAAQ,CAAE;AAAE,QAAMI,MAAEJ,IAAE,IAAI,GAAEF,MAAEE,IAAE,SAAOI,IAAE;AAAK,SAAOA,IAAE,QAAQ,GAAEN;AAAC;;;ACAwH,IAAMW,KAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,KAAEC,KAAE;AAAC,SAAK,QAAMF,KAAE,KAAK,sBAAoBC,KAAE,KAAK,WAASC,KAAE,KAAK,gBAAc,oBAAI,OAAI,KAAK,wBAAsB,MAAK,KAAK,iBAAe,OAAG,KAAK,gBAAc,OAAG,KAAK,eAAa,IAAIF,IAAE,KAAK,UAAS,KAAK,mBAAmB,GAAE,KAAK,gBAAcE,IAAE,mBAAmB,GAAE,KAAK,YAAU,IAAIC,IAAEH,KAAEE,IAAE,0BAAyBE,GAAE,KAAK,cAAc,kBAAkB,CAAC,GAAE,KAAK,MAAM,WAAW,mCAAmC,WAAS,KAAK,wBAAsB,IAAIC,IAAE,KAAK,KAAK;AAAA,EAAE;AAAA,EAAC,UAAS;AAJvuD;AAIwuD,SAAK,aAAa,QAAQ,GAAE,KAAK,cAAc,QAAS,CAAAL,QAAGA,IAAE,QAAQ,CAAE,GAAE,KAAK,cAAc,MAAM,GAAE,KAAK,UAAU,QAAQ,IAAE,UAAK,0BAAL,mBAA4B;AAAA,EAAS;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,MAAI,KAAK,cAAc;AAAA,EAAI;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK;AAAA,EAAc;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,WAAU;AAAC,WAAM,CAAC,KAAK,WAAS,KAAK,oBAAoBM;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAM,CAAC,KAAK,WAAS,KAAK,SAAS,mBAAiBC,GAAE;AAAA,EAAO;AAAA,EAAC,IAAI,gBAAe;AAAC,QAAIP,MAAE;AAAE,WAAO,KAAK,cAAc,QAAS,CAAAC,QAAGD,OAAGC,IAAE,QAAQ,OAAQ,CAACD,KAAEC,QAAID,MAAEC,IAAE,UAAU,MAAM,CAAC,CAAE,GAAED;AAAA,EAAC;AAAA,EAAC,gBAAgBA,KAAE;AAAC,SAAK,cAAc,QAAS,CAAAC,QAAGA,IAAE,QAAQ,QAAS,CAAAA,QAAGA,IAAE,UAAU,QAAS,CAAAA,QAAGD,IAAEC,IAAE,QAAQ,CAAE,CAAE,CAAE;AAAA,EAAC;AAAA,EAAC,OAAOD,KAAE;AAAC,SAAK,kBAAkBA,IAAE,OAAO,GAAE,KAAK,wBAAwBA,IAAE,MAAKA,IAAE,OAAO,GAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,kBAAkBA,KAAE;AAJliF;AAImiF,UAAMC,MAAE,KAAK,eAAcC,MAAED,IAAE,mBAAmB,SAAO;AAAE,eAAUO,OAAKR,KAAE;AAAC,YAAMA,MAAEQ,IAAE,gBAAeL,OAAE,UAAK,cAAc,IAAIH,IAAE,YAAY,EAAE,MAAvC,mBAA0C,WAAWA,IAAE;AAAI,UAAG,EAAEG,GAAC,EAAE;AAAO,YAAMC,MAAED,IAAE,UAAU,IAAIH,IAAE,EAAE;AAAE,UAAGQ,IAAE,cAAYC,GAAE,WAASA,GAAE,iBAAgB;AAAC,cAAMC,MAAEC,GAAEV,IAAE,aAAaG,IAAE,SAAS,QAAQ,IAAEF,GAAC,GAAEM,MAAEP,IAAE,mBAAmB,WAAWS,IAAE,MAAM;AAAE,aAAK,eAAeV,KAAEQ,KAAE,CAAC,GAAEL,IAAE,IAAI,cAAc,SAAS,WAAWO,KAAEN,IAAE,OAAKF,KAAE,GAAEE,IAAE,cAAYF,GAAC;AAAA,MAAC;AAAC,MAAAM,IAAE,cAAYC,GAAE,YAAUA,GAAE,WAASA,GAAE,gBAAcN,IAAE,oBAAkB;AAAA,IAAG;AAAA,EAAC;AAAA,EAAC,eAAeH,KAAEC,KAAE;AAJjkG;AAIkkG,UAAMC,MAAE,IAAID;AAAE,eAAUO,OAAKR,KAAE;AAAC,YAAMA,MAAEQ,IAAE;AAAY,UAAG,EAAER,GAAC,EAAE;AAAS,UAAIC,MAAEC,IAAE,IAAIF,IAAE,IAAG,IAAI;AAAE,QAAEC,GAAC,MAAIA,MAAE,IAAIW,IAAEZ,IAAE,IAAI,GAAEE,IAAE,IAAIF,IAAE,IAAG,MAAKC,GAAC,IAAGA,IAAE,QAAQ,KAAKO,GAAC;AAAA,IAAC;AAAC,eAAUA,OAAKP,KAAE;AAAC,YAAMD,MAAEQ,IAAE;AAAY,UAAG,EAAER,GAAC,EAAE;AAAS,YAAMC,OAAE,UAAK,cAAc,IAAID,IAAE,EAAE,MAA3B,mBAA8B,WAAWQ,IAAE;AAAI,UAAG,EAAEP,GAAC,EAAE;AAAS,UAAIE,MAAED,IAAE,IAAIF,IAAE,IAAGC,GAAC;AAAE,QAAEE,GAAC,MAAIA,MAAE,IAAIS,IAAEZ,IAAE,IAAI,GAAEE,IAAE,IAAIF,IAAE,IAAGC,KAAEE,GAAC,IAAGA,IAAE,QAAQ,KAAKK,GAAC;AAAA,IAAC;AAAC,WAAON;AAAA,EAAC;AAAA,EAAC,wBAAwBD,KAAEC,KAAE;AAAC,UAAK,EAAC,eAAcC,KAAE,eAAcC,IAAC,IAAE,MAAKC,MAAEF,IAAE,mBAAmB,SAAO,GAAEU,MAAE,KAAK,eAAeZ,KAAEC,GAAC;AAAE,IAAAW,IAAE,QAAS,CAACZ,KAAEC,QAAI;AAAC,YAAMY,MAAEb,IAAE,IAAI,IAAI,GAAEK,MAAE,EAAEQ,GAAC,IAAEA,IAAE,UAAQ,CAAC;AAAE,MAAAD,IAAE,OAAOX,KAAE,IAAI;AAAE,UAAIa,MAAEX,IAAE,IAAIF,GAAC;AAAE,UAAGD,IAAE,QAAS,CAACA,KAAEO,QAAI;AAAC,YAAGK,IAAE,OAAOX,KAAEM,GAAC,GAAE,EAAEA,GAAC,EAAE,QAAO,KAAKE,GAAE,OAAG,+BAA+B;AAAE,YAAGF,IAAE,UAAU,SAAOP,IAAE,QAAQ,OAAO,QAAO,KAAK,UAAU,UAAUO,IAAE,GAAG,GAAE,EAAEO,IAAE,SAAQP,GAAC,GAAE,MAAK,MAAIO,IAAE,QAAQ,UAAQ,MAAIT,IAAE,UAAQF,IAAE,OAAOF,GAAC;AAAG,cAAMY,MAAEN,IAAE,aAAYD,MAAEC,IAAE,IAAI,OAAK,GAAEQ,MAAEV,IAAE,OAAQ,CAACN,KAAEC,QAAID,MAAEG,IAAE,aAAaF,IAAE,QAAQ,GAAG,CAAC,GAAEgB,KAAEhB,IAAE,QAAQ,OAAQ,CAACD,KAAEC,QAAID,MAAEG,IAAE,aAAaF,IAAE,QAAQ,GAAG,CAAC,GAAEiB,KAAE,KAAK,KAAKJ,MAAEE,MAAEC,MAAGZ,KAAEc,EAAC,GAAEC,MAAEF,KAAEX;AAAE,QAAAW,KAAEG,MAAGH,KAAEX,MAAE,KAAGN,IAAE,QAAQ,QAAS,CAAC,EAAC,IAAGD,IAAC,MAAIQ,IAAE,eAAeR,GAAC,CAAE,GAAEQ,IAAE,UAAU,QAAS,CAAC,EAAC,UAASR,IAAC,MAAIM,IAAE,KAAKN,GAAC,CAAE,GAAE,KAAK,UAAU,UAAUQ,IAAE,GAAG,GAAE,EAAEO,IAAE,SAAQP,GAAC,KAAGY,MAAE,KAAK,iBAAiBZ,KAAEF,KAAEL,GAAC,IAAE,KAAK,cAAcO,KAAEP,GAAC;AAAA,MAAC,CAAE,GAAEK,IAAE,SAAO,EAAE,MAAI,EAAES,GAAC,MAAIA,MAAE,IAAIL,IAAEI,IAAE,MAAM,GAAEV,IAAE,IAAIF,KAAEa,GAAC,IAAGA,IAAE,QAAQ,QAAS,CAAAf,QAAG,KAAK,WAAWA,KAAEM,GAAC,CAAE,GAAEA,IAAE,SAAO,IAAG,CAAAS,IAAE,QAAQ,KAAK,KAAK,iBAAiB,IAAIP,OAAEF,KAAE,IAAI,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,SAAK,iBAAe,OAAG,KAAK,gBAAc,OAAG,KAAK,cAAc,QAAS,CAAAN,QAAG;AAAC,MAAAA,IAAE,QAAQ,QAAS,CAAAA,QAAG;AAAC,QAAAA,IAAE,sBAAoBA,IAAE,qBAAmB,OAAGA,IAAE,gBAAc,OAAGA,IAAE,eAAa,OAAG,EAAEA,IAAE,WAAW,CAAAC,SAAID,IAAE,gBAAgBC,GAAC,GAAED,IAAE,sBAAoBA,IAAE,iBAAeA,IAAE,aAAc,GAAEA,IAAE,mBAAmB,KAAK,cAAc,mBAAmB,MAAM,IAAG,KAAK,iBAAe,KAAK,kBAAgBA,IAAE,eAAc,KAAK,gBAAc,KAAK,iBAAeA,IAAE;AAAA,MAAY,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBA,KAAEC,KAAEC,KAAE;AAAC,QAAG,EAAEA,GAAC,EAAE,YAAUM,OAAKN,IAAE,QAAQ,CAAAF,IAAE,eAAeQ,IAAE,EAAE;AAAE,UAAME,MAAE,KAAK,eAAcP,MAAEO,IAAE,mBAAmB,QAAON,MAAED,MAAE,GAAEE,MAAE,KAAK,MAAMc,KAAEf,GAAC;AAAE,QAAIS,MAAEb,IAAE;AAAY,WAAKC,IAAE,SAAO,KAAG;AAAC,YAAMC,MAAED,IAAE,IAAI,GAAEO,MAAEE,IAAE,aAAaR,IAAE,QAAQ;AAAE,UAAGW,MAAEL,MAAEH,OAAGQ,MAAE,GAAE;AAAC,QAAAZ,IAAE,KAAKC,GAAC;AAAE;AAAA,MAAK;AAAC,MAAAW,OAAGL;AAAE,YAAML,MAAE,IAAIF,IAAEC,KAAE,GAAE,CAAC;AAAE,MAAAQ,GAAE,QAAMV,IAAE,UAAU,IAAIE,IAAE,EAAE,CAAC,GAAEF,IAAE,YAAYE,IAAE,IAAGC,GAAC;AAAA,IAAC;AAAC,UAAMW,MAAED,MAAET,KAAEE,MAAEK,GAAEG,GAAC,GAAEC,MAAEL,IAAE,mBAAmB,WAAWJ,IAAE,MAAM;AAAE,QAAIC,MAAE;AAAE,IAAAP,IAAE,qBAAmB,OAAGA,IAAE,gBAAc,OAAGA,IAAE,eAAa,OAAGA,IAAE,UAAU,QAAS,CAACC,KAAEC,QAAI;AAAC,WAAK,eAAeD,IAAE,UAASc,KAAER,GAAC;AAAE,YAAMC,MAAED;AAAE,MAAAA,OAAGG,IAAE,aAAaT,IAAE,SAAS,QAAQ,GAAED,IAAE,eAAeE,KAAEM,KAAED,GAAC,GAAEP,IAAE,gBAAgBC,GAAC;AAAA,IAAC,CAAE,GAAE,KAAK,UAAU,UAAUD,IAAE,GAAG,GAAEA,IAAE,MAAI,KAAK,UAAU,OAAOsB,GAAER,GAAC,CAAC,GAAEd,IAAE,IAAI,cAAc,SAAS,WAAWM,KAAE,GAAE,GAAEC,MAAEH,GAAC,GAAEJ,IAAE,MAAM,MAAM;AAAE,UAAMgB,MAAEhB,IAAE,MAAM,QAAQ;AAAE,WAAOgB,IAAE,OAAKT,KAAES,IAAE,KAAG,KAAK,MAAMhB,IAAE,IAAI,OAAKG,GAAC,GAAEH,IAAE,mBAAmBG,GAAC,GAAEH;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAEC,KAAE;AAAC,QAAG,MAAIA,IAAE,QAAQ,OAAO;AAAO,eAAUG,OAAKH,IAAE,SAAQ;AAAC,YAAMA,MAAEG,IAAE,IAAGF,MAAEF,IAAE,UAAU,IAAIC,GAAC;AAAE,UAAG,CAACC,IAAE;AAAS,MAAAF,IAAE,eAAeC,GAAC;AAAE,YAAMS,MAAEa,GAAE,KAAK;AAAE,UAAGb,KAAE;AAAC,YAAGA,IAAE,OAAKR,IAAE,MAAK;AAAC,UAAAQ,IAAE,KAAGR,IAAE;AAAG;AAAA,QAAQ;AAAC,YAAGQ,IAAE,SAAOR,IAAE,IAAG;AAAC,UAAAQ,IAAE,OAAKR,IAAE;AAAK;AAAA,QAAQ;AAAA,MAAC;AAAC,YAAMM,MAAEe,GAAE,QAAQ;AAAE,MAAAf,IAAE,OAAKN,IAAE,MAAKM,IAAE,KAAGN,IAAE;AAAA,IAAE;AAAC,IAAAE,IAAEmB,EAAC;AAAE,UAAMrB,MAAE,KAAK,cAAc,mBAAmB,SAAO,GAAEQ,MAAEa,GAAE,OAAQ,CAACvB,KAAEC,QAAI,KAAK,IAAID,KAAEC,IAAE,WAAW,GAAG,CAAC,IAAEC,KAAEM,MAAEG,GAAED,GAAC;AAAE,IAAAF,IAAE,KAAK,GAAE,GAAEE,GAAC;AAAE,UAAMP,MAAEH,IAAE,IAAI,cAAc;AAAS,IAAAuB,GAAE,OAAQ,CAAAvB,QAAGG,IAAE,WAAWK,KAAER,IAAE,OAAKE,KAAE,GAAEF,IAAE,cAAYE,GAAC,CAAE,GAAEF,IAAE,MAAM,UAAUuB,GAAE,MAAKA,GAAE,MAAM,GAAEA,GAAE,OAAQ,CAACvB,KAAEC,QAAIsB,GAAE,KAAKtB,GAAC,IAAE,IAAK,GAAEsB,GAAE,MAAM,GAAEvB,IAAE,oBAAkB;AAAA,EAAE;AAAA,EAAC,WAAWA,KAAEE,KAAE;AAAC,QAAG,MAAIA,IAAE,OAAO;AAAO,QAAG,CAACE,IAAEJ,GAAC,EAAE,QAAO,KAAK,KAAK,iBAAiBA,KAAEE,KAAE,IAAI;AAAE,UAAMM,MAAE,KAAK,eAAcL,MAAEK,IAAE,mBAAmB,SAAO,GAAEJ,MAAEJ,IAAE,aAAYK,MAAEH,IAAE,OAAQ,CAACF,KAAEC,QAAID,MAAEQ,IAAE,aAAaP,IAAE,QAAQ,GAAG,CAAC,GAAEY,MAAE,KAAK,KAAKT,MAAEC,OAAGF,KAAEgB,EAAC,GAAEL,MAAE,IAAED;AAAE,QAAGb,IAAE,IAAI,OAAKsB,GAAEH,KAAEE,EAAC,KAAGP,MAAEd,IAAE,IAAI,KAAK,QAAO,KAAK,KAAK,iBAAiBA,KAAEE,KAAE,IAAI;AAAE,IAAAE,IAAEJ,IAAE,KAAK;AAAE,UAAMM,MAAE,IAAI;AAAM,eAAUL,OAAKC,KAAE;AAAC,YAAMA,MAAEM,IAAE,aAAaP,IAAE,QAAQ,GAAES,MAAEc,GAAExB,IAAE,OAAME,GAAC;AAAE,MAAAI,IAAE,KAAKI,GAAC;AAAA,IAAC;AAAC,UAAMK,MAAEf,IAAE,IAAI,cAAc;AAAS,QAAIO,MAAE,GAAES,MAAE,GAAEC,KAAE;AAAE,UAAMC,KAAEP,GAAEE,GAAC,GAAEO,MAAEZ,IAAE,mBAAmB,WAAWU,GAAE,MAAM;AAAE,IAAAhB,IAAE,QAAS,CAACD,KAAEC,QAAI;AAAC,YAAME,MAAEE,IAAEJ,GAAC;AAAE,UAAG,EAAEE,GAAC,EAAE;AAAO,UAAG,EAAEa,OAAIb,MAAG;AAAC,cAAMJ,MAAEiB,KAAED;AAAE,QAAAhB,MAAE,KAAGe,IAAE,WAAWG,IAAEF,MAAEb,KAAE,GAAEH,MAAEG,GAAC,GAAEa,MAAEZ,KAAEG,MAAE;AAAA,MAAC;AAAC,YAAMF,MAAEG,IAAE,aAAaP,IAAE,QAAQ;AAAE,WAAK,eAAeA,KAAEmB,KAAEb,GAAC,GAAEA,OAAGF,KAAEY,KAAEb,MAAEC;AAAE,YAAMQ,MAAE,IAAIZ,IAAEA,KAAEG,KAAEA,MAAEC,GAAC;AAAE,MAAAK,GAAE,QAAMV,IAAE,UAAU,IAAIC,IAAE,EAAE,CAAC,GAAED,IAAE,YAAYC,IAAE,IAAGY,GAAC,GAAEb,IAAE,oBAAkB;AAAA,IAAE,CAAE;AAAE,UAAMyB,MAAER,KAAED;AAAE,IAAAS,MAAE,KAAGV,IAAE,WAAWG,IAAEF,MAAEb,KAAE,GAAEsB,MAAEtB,GAAC,GAAE,EAAED,KAAG,CAACF,KAAEC,QAAI,EAAEK,IAAEL,GAAC,CAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,KAAEC,KAAEC,KAAE;AAAC,UAAMQ,MAAEV,IAAE,YAAY;AAAK,IAAAc,GAAEY,IAAE,CAAChB,IAAE,CAAC,GAAE,CAACA,IAAE,CAAC,GAAE,CAACA,IAAE,CAAC,CAAC;AAAE,UAAMF,MAAED,GAAEE,IAAEiB,IAAE1B,IAAE,cAAc;AAAE,IAAAc,GAAEa,IAAEnB,GAAC,GAAEJ,GAAEuB,IAAEA,EAAC,GAAE,KAAK,cAAc,MAAMnB,KAAEmB,IAAE3B,IAAE,UAASC,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBF,KAAE;AAAC,WAAO,KAAK,SAAS,OAAOA,GAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAEC,KAAE;AAAC,WAAO,KAAK,SAAS,aAAaD,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,KAAEC,KAAE;AAJ/gP;AAIghP,QAAG,CAAC,KAAK,aAAaA,IAAE,MAAKD,GAAC,EAAE,QAAM;AAAG,UAAME,MAAEF,QAAIc,GAAE,aAAWd,QAAIc,GAAE;AAAgB,QAAGZ,OAAG,CAAC,KAAK,eAAe,QAAM;AAAG,UAAMC,MAAEH,QAAIc,GAAE,wBAAuBV,MAAE,EAAEF,OAAGC,MAAGE,MAAE,KAAK;AAAM,QAAIQ;AAAE,UAAMC,MAAE,MAAI;AAAC,UAAG,EAAED,GAAC,EAAE,QAAOA;AAAE,YAAMX,MAAE,KAAK,aAAa,KAAKG,KAAEJ,IAAE,MAAKD,GAAC;AAAE,aAAO,EAAEE,GAAC,IAAE,QAAMW,MAAEX,IAAE,UAAUD,GAAC,GAAE,EAAEY,GAAC,IAAE,QAAMR,IAAE,cAAcQ,KAAE,KAAK,SAAS,YAAWZ,GAAC,GAAEY;AAAA,IAAG;AAAE,eAAK,0BAAL,mBAA4B;AAAmB,eAAUL,OAAK,KAAK,cAAc,OAAO,EAAE,YAAUR,OAAKQ,IAAE,SAAQ;AAAC,UAAGN,OAAG,CAACF,IAAE,cAAc;AAAS,YAAMa,OAAGX,MAAEF,IAAE,wBAAsBG,OAAGH,IAAE,sBAAsB,IAAEA,IAAE,kCAAgCA,IAAE,wBAAsB,MAAKM,MAAEF,OAAGJ,IAAE,yBAAuB;AAAK,WAAGa,OAAA,gBAAAA,IAAG,YAAQP,OAAA,gBAAAA,IAAG,SAAO;AAAC,cAAMJ,MAAEY,IAAE;AAAE,YAAG,EAAEZ,GAAC,EAAE,QAAM;AAAG,QAAAA,IAAE,QAAQ,SAAS,IAAIM,IAAEA,IAAE,MAAM,GAAEP,KAAE,KAAK,SAAS,UAAU,GAAEC,IAAE,yBAAyBF,IAAE,GAAG,GAAEK,IAAE,QAAQL,IAAE,GAAG,IAAEa,OAAA,gBAAAA,IAAG,YAASX,IAAE,kBAAkBG,KAAEJ,IAAE,MAAK,KAAE,GAAEY,IAAE,OAAQ,CAAAb,QAAGK,IAAE,WAAWH,IAAE,eAAcF,IAAE,OAAMA,IAAE,KAAK,CAAE,KAAGM,OAAA,gBAAAA,IAAG,YAASJ,IAAE,kBAAkBG,KAAEJ,IAAE,MAAK,IAAE,GAAEK,IAAE,OAAQ,CAAAN,QAAGK,IAAE,WAAWH,IAAE,eAAcF,IAAE,OAAMA,IAAE,KAAK,CAAE;AAAA,MAAE;AAAA,IAAC;AAAC,WAAO,EAAEa,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM,EAAC,UAAS,KAAK,UAAS,aAAY,KAAK,cAAa,cAAa,KAAK,cAAa;AAAA,EAAC;AAAC;AAAC,IAAMD,MAAN,MAAO;AAAA,EAAC,YAAYZ,KAAE;AAAC,SAAK,SAAOA,KAAE,KAAK,UAAQ,IAAI;AAAA,EAAK;AAAC;AAAC,SAASwB,GAAExB,KAAEC,KAAE;AAAC,MAAIC;AAAE,MAAG,CAACF,IAAE,KAAM,CAAAA,QAAG,EAAEA,IAAE,cAAYC,SAAKC,MAAEF,KAAE,KAAI,EAAE,QAAO;AAAK,QAAMU,MAAER,IAAE;AAAK,SAAOA,IAAE,QAAMD,KAAEC,IAAE,QAAMA,IAAE,MAAIF,IAAE,gBAAgBE,GAAC,GAAEQ;AAAC;AAAC,IAAMgB,KAAE1B,GAAE;AAAV,IAAYS,KAAET,GAAE;AAAhB,IAAkB2B,KAAE3B,GAAE;AAAtB,IAAwBuB,KAAE,IAAIV,GAAE,EAAC,WAAU,CAAAb,QAAGA,OAAG,IAAIC,OAAE,aAAY,KAAI,CAAC;AAAxE,IAA0EoB,KAAE;AAA5E,IAAkFO,KAAE,IAAEP;AAAtF,IAAwFQ,KAAE;AAA1F,IAAmGV,KAAEU,KAAE;AAAE,IAAIC,KAAE,IAAI,aAAaT,EAAC;AAAE,SAASV,GAAEX,KAAE;AAAC,SAAO8B,GAAE,SAAO9B,QAAI8B,KAAE,IAAI,aAAa9B,GAAC,IAAG8B;AAAC;AAAC,SAASR,GAAEtB,KAAE;AAAC,QAAMC,MAAE,IAAED;AAAE,SAAOC,MAAE2B,KAAEA,KAAE,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,MAAI3B,MAAE2B,EAAC,IAAEA,IAAEC,EAAC,GAAE5B,GAAC;AAAC;;;ACAnnQ,IAAI8B,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMA,GAAC,GAAE,KAAK,WAAS,IAAIC,MAAE,KAAK,WAAS,IAAIC,OAAE,KAAK,qBAAmB,oBAAI,OAAI,KAAK,2BAAyB,IAAIC,MAAE,KAAK,cAAY,oBAAI,OAAI,KAAK,iBAAe,OAAG,KAAK,YAAU;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,SAAS,MAAM,GAAE,KAAK,mBAAmB,QAAS,CAAAH,QAAGA,IAAE,QAAQ,CAAE,GAAE,KAAK,mBAAmB,MAAM,GAAE,KAAK,yBAAyB,MAAM,GAAE,KAAK,YAAY,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAM,CAAC,KAAK,SAAS,SAAO,KAAK,SAAS,QAAQ,SAAO;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,gBAAgB;AAAA,EAAI;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAO,KAAK,gBAAgB;AAAA,EAAkB;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAO,KAAK,gBAAgB;AAAA,EAAkB;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK;AAAA,EAAc;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,EAAE,KAAK,oBAAoB,CAAAA,QAAGA,IAAE,eAAgB;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAM,CAAC,KAAK,YAAU,MAAI,KAAK,mBAAmB,QAAM,MAAI,KAAK,YAAY;AAAA,EAAI;AAAA,EAAC,gBAAe;AAAC,QAAG,CAAC,KAAK,SAAS,QAAM;AAAG,SAAK,oBAAoB;AAAE,UAAMA,MAAEI,IAAE,KAAK,QAAQ;AAAE,QAAIA,MAAE,OAAGF,MAAE,OAAGG,MAAE;AAAG,WAAOL,IAAE,QAAS,CAACA,KAAEM,QAAI;AAAC,UAAIC,MAAE,KAAK,mBAAmB,IAAID,GAAC;AAAE,UAAG,CAACC,OAAGP,IAAE,KAAK,SAAO,MAAIO,MAAE,IAAIC,GAAE,KAAK,MAAK,KAAK,qBAAoBF,GAAC,GAAE,KAAK,mBAAmB,IAAIA,KAAEC,GAAC,GAAEH,MAAE,MAAGF,MAAE,MAAGG,MAAE,OAAI,CAACE,IAAE;AAAO,YAAME,MAAEP,OAAGK,IAAE,eAAcG,MAAEL,OAAGE,IAAE;AAAS,MAAAA,IAAE,OAAOP,GAAC,GAAEE,MAAEA,OAAGO,QAAIF,IAAE,eAAcF,MAAEA,OAAGK,QAAIH,IAAE,UAASA,IAAE,YAAU,KAAK,mBAAmB,OAAOD,GAAC,GAAEC,IAAE,QAAQ,GAAEH,MAAE;AAAA,IAAG,CAAE,GAAE,KAAK,SAAS,MAAM,GAAEA,OAAG,KAAK,+BAA+B,GAAEF,QAAI,KAAK,iBAAe,EAAE,KAAK,oBAAoB,CAAAF,QAAGA,IAAE,aAAc,IAAGK,QAAI,KAAK,YAAU,EAAE,KAAK,oBAAoB,CAAAL,QAAGA,IAAE,QAAS,IAAG,KAAK,aAAa,UAAU,GAAE;AAAA,EAAE;AAAA,EAAC,cAAcA,KAAEI,KAAE;AAAC,QAAG,MAAIJ,IAAE,OAAO;AAAO,UAAMM,MAAE,KAAK,0BAA0BN,GAAC;AAAE,eAAUK,OAAKC,IAAE,MAAK,YAAY,IAAID,IAAE,IAAGA,GAAC;AAAE,UAAMH,MAAE,KAAK,SAAS;AAAM,eAAUG,OAAKC,IAAE,MAAK,SAAS,KAAK,IAAID,GAAC;AAAE,IAAAH,OAAG,KAAK,aAAa,UAAU,GAAEE,QAAIO,IAAE,UAAQ,KAAK,8BAA8BX,GAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,KAAEI,KAAE;AAAC,UAAME,MAAE,KAAK,SAAS,OAAMD,MAAE,KAAK,SAAS;AAAK,eAAUE,OAAKP,IAAE,CAAAK,IAAE,IAAIE,GAAC,KAAG,KAAK,SAAS,QAAQ,IAAIA,GAAC,GAAEF,IAAE,OAAOE,GAAC,KAAG,KAAK,SAAS,QAAQ,IAAIA,GAAC,KAAG,KAAK,SAAS,QAAQ,IAAIA,GAAC,GAAE,KAAK,YAAY,OAAOP,GAAEO,IAAE,EAAE,CAAC;AAAE,IAAAD,OAAG,CAAC,KAAK,SAAS,SAAO,KAAK,aAAa,UAAU,GAAEF,QAAIO,IAAE,UAAQ,KAAK,8BAA8BX,GAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,KAAEI,KAAE;AAAC,UAAME,MAAE,MAAI,KAAK,SAAS,QAAQ;AAAO,eAAUJ,OAAKF,KAAE;AAAC,YAAMA,MAAE,KAAK,SAAS,QAAQ,QAAQ;AAAE,MAAAA,IAAE,iBAAe,KAAK,wBAAwBE,GAAC,GAAEF,IAAE,aAAWI;AAAA,IAAC;AAAC,YAAOE,OAAG,KAAK,SAAS,QAAQ,SAAO,KAAG,KAAK,aAAa,UAAU,GAAEF,KAAE;AAAA,MAAC,KAAKQ,GAAE;AAAA,MAAe,KAAKA,GAAE;AAAS,eAAO,KAAK,8BAA8BZ,GAAC;AAAA,MAAE,KAAKY,GAAE;AAAW,eAAO,KAAK,gCAAgCZ,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,KAAE;AAAC,QAAII,MAAE;AAAG,WAAO,KAAK,yBAAyB,OAAQ,CAAAE,QAAGF,MAAEE,IAAE,gBAAgBN,GAAC,KAAGI,GAAE,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAOJ,KAAE;AAAC,SAAK,yBAAyB,OAAQ,CAAAI,QAAG;AAAC,MAAAA,IAAE,SAAS,aAAaJ,GAAC,KAAGI,IAAE,OAAOJ,IAAE,QAAOA,IAAE,cAAc;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAEI,KAAEE,KAAEJ,KAAEG,KAAE;AAAC,WAAO,KAAK,YAAY,QAAS,CAAAE,QAAG;AAAC,UAAGL,OAAG,CAACA,IAAEK,GAAC,EAAE;AAAO,WAAK,yBAAyBA,KAAED,KAAEF,KAAE,GAAEJ,KAAEK,GAAC;AAAE,YAAMI,MAAE,KAAK,gBAAgB;AAAkB,MAAAA,QAAIF,IAAE,eAAe,CAAC,IAAEA,IAAE,eAAe,CAAC,IAAEE,IAAE,OAAK,KAAK,yBAAyBF,KAAED,KAAEF,KAAEK,IAAE,OAAMT,KAAEK,GAAC,GAAEE,IAAE,eAAe,CAAC,IAAEA,IAAE,eAAe,CAAC,IAAEE,IAAE,OAAK,KAAK,yBAAyBF,KAAED,KAAEF,KAAE,CAACK,IAAE,OAAMT,KAAEK,GAAC,IAAGA;AAAA,IAAG,CAAE,GAAEA;AAAA,EAAC;AAAA,EAAC,iCAAgC;AAAC,SAAK,yBAAyB,MAAM;AAAE,QAAIL,MAAE;AAAE,SAAK,mBAAmB,QAAS,CAACI,KAAEE,QAAI;AAAC,MAAAA,IAAE,cAAYN,OAAI,KAAK,yBAAyB,KAAKI,GAAC;AAAA,IAAC,CAAE,GAAE,KAAK,yBAAyB,KAAM,CAACJ,KAAEI,QAAI;AAAC,YAAME,MAAEF,IAAE,SAAS,iBAAeJ,IAAE,SAAS;AAAe,aAAO,MAAIM,MAAEA,MAAEN,IAAE,SAAS,cAAYI,IAAE,SAAS;AAAA,IAAW,CAAE;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,SAAK,SAAS,KAAK,MAAM,GAAE,KAAK,SAAS,QAAQ,MAAM,GAAE,KAAK,SAAS,KAAK,UAAU,MAAM,KAAK,KAAK,SAAS,IAAI,CAAC,GAAE,KAAK,SAAS,QAAQ,UAAU,MAAM,KAAK,KAAK,SAAS,OAAO,CAAC;AAAE,aAAQJ,MAAE,GAAEA,MAAE,KAAK,SAAS,QAAQ,UAAQ;AAAC,YAAMI,MAAE,KAAK,SAAS,QAAQ,KAAKJ,GAAC;AAAE,WAAK,SAAS,IAAII,IAAE,cAAc,IAAE,KAAK,SAAS,QAAQ,qBAAqBJ,GAAC,IAAEA;AAAA,IAAG;AAAC,SAAK,SAAS,MAAM;AAAA,EAAC;AAAA,EAAC,yBAAyBA,KAAEI,KAAEE,KAAEJ,KAAEG,KAAEE,KAAE;AAAC,QAAG,CAACP,IAAE,QAAQ;AAAO,QAAIS,MAAE;AAAE,IAAAP,OAAGF,IAAE,eAAe,EAAE,GAAES,MAAET,IAAE,eAAe,EAAE,GAAEa,GAAE,CAAC,IAAEP,IAAE,CAAC,IAAEJ,KAAEW,GAAE,CAAC,IAAEP,IAAE,CAAC,IAAEG,KAAET,IAAE,qBAAmB,KAAK,gBAAgB,oBAAmBA,IAAE,SAAS,gBAAgBA,KAAE,MAAKK,KAAEQ,IAAG,CAACP,KAAEJ,KAAEO,QAAI;AAAC,MAAAV,IAAEK,KAAEK,KAAET,IAAE,SAAS,gBAAeO,KAAEF,KAAEL,IAAE,UAASA,IAAE,UAAU;AAAA,IAAC,GAAGI,GAAC;AAAA,EAAC;AAAA,EAAC,8BAA8BJ,KAAE;AAAC,QAAG,EAAE,KAAK,YAAY,4BAA4B,EAAE;AAAO,QAAII;AAAE,eAAUE,OAAKN,KAAE;AAAC,YAAMA,MAAEM,IAAE;AAAW,QAAEN,GAAC,KAAGA,QAAII,QAAI,KAAK,YAAY,6BAA6BJ,GAAC,GAAEI,MAAEJ;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,gCAAgCA,KAAE;AAAC,QAAG,EAAE,KAAK,YAAY,8BAA8B,EAAE;AAAO,QAAII;AAAE,eAAUE,OAAKN,KAAE;AAAC,YAAMA,MAAEM,IAAE;AAAW,QAAEN,GAAC,KAAGA,QAAII,QAAI,KAAK,YAAY,+BAA+BJ,GAAC,GAAEI,MAAEJ;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BA,KAAE;AAAC,eAAUI,OAAKJ,IAAE,MAAK,wBAAwBI,GAAC;AAAE,WAAOJ;AAAA,EAAC;AAAA,EAAC,wBAAwBA,KAAE;AAAC,WAAO,EAAEA,IAAE,WAAW,MAAIA,IAAE,cAAY,KAAK,oBAAoB,UAAUA,IAAE,cAAc,IAAGA;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM,EAAC,yBAAwB,KAAK,yBAAwB;AAAA,EAAC;AAAC;AAAE,EAAE,CAACc,GAAE,CAAC,GAAEhB,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAACgB,GAAE,CAAC,GAAEhB,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAACgB,GAAE,CAAC,GAAEhB,GAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAACgB,GAAE,CAAC,GAAEhB,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACgB,GAAE,CAAC,GAAEhB,GAAE,WAAU,uBAAsB,IAAI,GAAE,EAAE,CAACgB,GAAE,CAAC,GAAEhB,GAAE,WAAU,uBAAsB,IAAI,GAAE,EAAE,CAACgB,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEhB,GAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAACgB,GAAE,CAAC,GAAEhB,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAACgB,GAAE,CAAC,GAAEhB,GAAE,WAAU,eAAc,MAAM,GAAEA,KAAE,EAAE,CAACW,GAAE,6DAA6D,CAAC,GAAEX,EAAC;AAAE,IAAMG,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,OAAK,oBAAI,OAAI,KAAK,UAAQ,oBAAI,OAAI,KAAK,UAAQ,oBAAI;AAAA,EAAG;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,MAAI,KAAK,KAAK,QAAM,MAAI,KAAK,QAAQ,QAAM,MAAI,KAAK,QAAQ;AAAA,EAAI;AAAA,EAAC,IAAID,KAAE;AAAC,WAAO,KAAK,KAAK,IAAIA,GAAC,KAAG,KAAK,QAAQ,IAAIA,GAAC,KAAG,KAAK,QAAQ,IAAIA,GAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAK,MAAM,GAAE,KAAK,QAAQ,MAAM,GAAE,KAAK,QAAQ,MAAM;AAAA,EAAC;AAAC;AAAC,SAASD,IAAEC,KAAEI,KAAEE,KAAEJ,KAAEG,KAAEE,KAAEE,KAAE;AAAC,QAAMC,MAAE,IAAIL,IAAEE,KAAEE,KAAEL,GAAC,GAAEW,MAAE,CAAAX,QAAG;AAAC,IAAAA,IAAE,IAAIC,IAAE,SAAQK,KAAEV,IAAE,MAAKA,IAAE,QAAOA,IAAE,gBAAeM,KAAEJ,GAAC;AAAA,EAAC;AAAE,OAAI,QAAMG,IAAE,QAAQ,IAAI,oBAAkBC,OAAGD,IAAE,QAAQ,IAAI,sBAAoB,QAAMA,IAAE,QAAQ,IAAI,QAAMA,IAAE,QAAQ,OAAO,QAAMA,IAAE,QAAQ,IAAI,SAAOU,IAAEV,IAAE,QAAQ,GAAG,GAAEA,IAAE,QAAQ,UAAQC,IAAE,QAAM,QAAMD,IAAE,QAAQ,IAAI,oBAAkBC,MAAED,IAAE,QAAQ,IAAI,sBAAoB,QAAMA,IAAE,QAAQ,IAAI,QAAMA,IAAE,QAAQ,OAAO,OAAKA,IAAE,QAAQ,IAAI,SAAOU,IAAEV,IAAE,QAAQ,GAAG,GAAEA,IAAE,QAAQ,UAAQC,IAAE,KAAI;AAAC,UAAMN,MAAEgB,GAAEX,IAAE,GAAG;AAAE,IAAAU,IAAEf,GAAC,GAAEK,IAAE,QAAQ,IAAI,KAAKL,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMa,KAAEN,IAAE;;;ACAztN,IAAMU,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,kBAAkBA,KAAE;AAAC,WAAO,IAAIC,IAAED,IAAE,MAAK,GAAE,OAAO,IAAI,EAAE,MAAM,GAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,KAAK,cAAc,WAAS,EAAE,EAAC,UAASC,IAAEC,GAAE,WAAUA,GAAE,KAAIA,GAAE,qBAAoBA,GAAE,mBAAmB,GAAE,YAAWC,GAAC,CAAC,IAAE,EAAE,EAAC,YAAWA,GAAC,CAAC;AAAA,EAAC;AAAC;AAACN,GAAE,SAAO,IAAIO,GAAEC,IAAG,MAAI,OAAO,gCAAgD,CAAE;;;ACA7pB,IAAMC,MAAN,cAAgBC,IAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,WAAS;AAAA,EAAE;AAAC;AAAC,EAAE,CAACD,IAAE,CAAC,GAAEA,IAAE,WAAU,YAAW,MAAM;;;ACAi1F,IAAIE,MAAG,cAAcC,GAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK,eAAe;AAAA,EAAc;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,IAAI,qBAAoB;AAAC,WAAO,KAAK;AAAA,EAAmB;AAAA,EAAC,IAAI,qBAAoB;AAAC,WAAO,KAAK;AAAA,EAAmB;AAAA,EAAC,IAAI,qBAAoB;AAAC,WAAO,KAAK;AAAA,EAAmB;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMA,GAAC,GAAE,KAAK,YAAU,MAAK,KAAK,uBAAqB,MAAK,KAAK,iBAAe,OAAG,KAAK,mBAAiB,OAAG,KAAK,YAAU,OAAG,KAAK,WAAS,IAAIC,MAAE,KAAK,aAAW,oBAAI,OAAI,KAAK,mCAAiC,OAAG,KAAK,mBAAiB,IAAIC,MAAE,KAAK,kBAAgB,IAAIC,MAAE,KAAK,QAAM,MAAK,KAAK,sBAAoB,MAAK,KAAK,sBAAoB,GAAE,KAAK,sBAAoB,MAAK,KAAK,UAAQ,IAAIC,MAAE,KAAK,kBAAgB,GAAE,KAAK,SAAO,IAAIC,MAAE,KAAK,oBAAkB;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,UAAML,MAAE,KAAK,KAAK,OAAO;AAAW,SAAK,QAAMA,IAAE;AAAiB,UAAMM,MAAEN,IAAE;AAAuB,SAAK,4BAA0B,IAAIO,IAAEP,IAAE,gBAAgB,GAAE,KAAK,6BAA2B,IAAIK,IAAE,EAAC,MAAK,KAAK,OAAM,aAAYH,IAAE,OAAM,0BAAyB,KAAK,2BAA0B,wBAAuBI,IAAC,CAAC,GAAE,KAAK,iBAAe,IAAIH,IAAE,KAAK,OAAM,IAAIK,GAAE,KAAK,OAAM,KAAK,KAAK,MAAM,WAAW,GAAE,IAAIC,GAAE,KAAK,MAAK,KAAK,4BAA2B,KAAK,OAAO,MAAI;AAAA,IAAC,CAAE,CAAC,GAAE,KAAK,SAAS,IAAI,CAACP,GAAG,MAAII,IAAE,UAAW,MAAI,KAAK,OAAO,KAAK,iBAAiB,GAAGI,EAAC,GAAER,GAAG,MAAI,KAAK,kBAAmB,CAAAF,QAAG,KAAK,sBAAoB,IAAIW,GAAEX,GAAC,GAAGU,EAAC,GAAEE,GAAG,MAAI,KAAK,KAAK,eAAe,iBAAiB,MAAI,KAAK,mCAAiC,IAAG,CAAC,CAAC,GAAE,KAAK,sBAAoB,IAAIA,IAAEZ,IAAE,mBAAkB,KAAK,4BAA4B,CAAAA,QAAG;AAAC,OAACA,IAAE,iBAAea,OAAI,MAAI,KAAK,oBAAkB,KAAK,uBAAuB,GAAE,KAAK,OAAO,KAAK,iBAAiB,GAAE,KAAK,aAAa,UAAU,GAAE,KAAK,aAAa,SAAS;AAAA,IAAC,GAAI,MAAI,KAAK,OAAO,KAAK,iBAAiB,CAAE,GAAE,KAAK,gBAAgB,OAAKC,GAAE,iBAAgB,KAAK,gBAAgB,wBAAsBH,GAAE,KAAK,KAAK,GAAE,KAAK,QAAQ,OAAK,GAAE,KAAK,QAAQ,MAAI,KAAI,KAAK,QAAQ,oBAAkB,MAAK,KAAK,gBAAgB,SAAO,KAAK,SAAQ,KAAK,gBAAgB,uBAAqBI,IAAE,MAAK,KAAK,gBAAgB,YAAY,aAAW,GAAE,KAAK,gBAAgB,YAAY,eAAa,GAAE,KAAK,gBAAgB,YAAY,IAAI,CAAC,IAAIZ,GAAEG,GAAE,GAAE,GAAE,CAAC,CAAC,CAAC,CAAC,GAAE,KAAK,SAAS,IAAI,KAAK,KAAK,mBAAmB,UAAU,aAAa,EAAE,OAAM,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAS,QAAQ,GAAE,KAAK,WAAW,QAAS,CAAAN,QAAGA,IAAE,QAAQ,CAAE,GAAE,KAAK,WAAW,MAAM,GAAE,KAAK,yBAAuB,EAAE,KAAK,sBAAsB,GAAE,KAAK,gBAAgB,UAAQ,EAAE,KAAK,gBAAgB,OAAO,GAAE,KAAK,gBAAgB,wBAAsB,EAAE,KAAK,gBAAgB,qBAAqB,GAAE,KAAK,6BAA2B,EAAE,KAAK,0BAA0B,GAAE,KAAK,gBAAc,EAAE,KAAK,aAAa,GAAE,KAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,oCAAkC,EAAE,KAAK,YAAY,CAAAA,QAAGA,IAAE,QAAS;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,EAAE,KAAK,SAAS,KAAG,EAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,EAAE,KAAK,oBAAoB,IAAE,KAAK,qBAAqB,iBAAe;AAAA,EAAC;AAAA,EAAC,kCAAkCA,KAAE;AAAC,WAAO,KAAK,0BAA0BA,KAAEgB,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BhB,KAAEM,KAAEL,KAAE;AAAC,UAAMgB,MAAE,KAAK,WAAW,IAAIjB,GAAC;AAAE,MAAEiB,GAAC,KAAGA,IAAE,QAAQ;AAAE,UAAMd,MAAE,IAAIG,IAAE,EAAC,GAAGL,KAAE,iBAAgB,MAAK,aAAYD,IAAC,CAAC;AAAE,WAAO,KAAK,WAAW,IAAIA,KAAEG,GAAC,GAAE,KAAK,mCAAiC,MAAG,iBAAgBH,OAAG,KAAK,SAAS,IAAIE,GAAG,MAAIF,IAAE,aAAc,MAAI,KAAK,OAAO,KAAK,iBAAiB,CAAE,GAAEA,GAAC,GAAEG;AAAA,EAAC;AAAA,EAAC,0BAA0BH,KAAE;AAAC,QAAG,EAAEA,GAAC,EAAE;AAAO,UAAMM,MAAE,KAAK,WAAW,IAAIN,GAAC;AAAE,MAAEM,GAAC,MAAI,KAAK,mCAAiC,MAAG,KAAK,WAAW,OAAON,GAAC,GAAE,KAAK,SAAS,OAAOA,GAAC,GAAEM,IAAE,QAAQ;AAAA,EAAE;AAAA,EAAC,gCAAgCN,KAAE;AAAC,QAAIM,MAAE;AAAG,QAAG,EAAE,KAAK,oBAAoB,EAAE,YAAUL,OAAK,KAAK,qBAAqB,eAAc;AAAC,YAAK,CAACgB,KAAEd,GAAC,IAAE,KAAK,UAASY,MAAEE,IAAE,aAAahB,IAAE,IAAI,KAAG,CAACE,IAAE,aAAaF,IAAE,IAAI;AAAE,MAAAK,MAAE,KAAK,qBAAqB,uBAAuBS,KAAEd,KAAED,GAAC,KAAGM;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,EAAE,KAAK,WAAU,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBN,KAAE;AAAC,MAAE,KAAK,SAAS,KAAG,KAAK,UAAU,QAAS,CAAAM,QAAGA,IAAE,8BAA4BD,GAAEL,KAAG,CAAAA,QAAGA,IAAE,oBAAkBC,IAAE,kBAAmB,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBD,KAAE;AAAC,MAAE,KAAK,SAAS,KAAG,KAAK,UAAU,QAAS,CAAAM,QAAG;AAAC,MAAAA,IAAE,yBAAuBD,GAAEL,KAAG,CAAAA,QAAGA,IAAE,oBAAkBA,IAAE,QAAS,GAAEM,IAAE,wBAAsBD,GAAEL,KAAG,CAAAA,QAAGA,IAAE,oBAAkBA,IAAE,WAAY;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeA,KAAEM,KAAE;AAAC,MAAE,KAAK,SAAS,MAAI,KAAK,uBAAqB,IAAIS,IAAE,KAAK,KAAK,GAAE,KAAK,YAAU,CAAC,IAAIG,IAAEC,GAAE,OAAM,KAAK,oBAAoB,GAAE,IAAID,IAAEC,GAAE,OAAM,KAAK,oBAAoB,CAAC,IAAG,KAAK,mBAAmBnB,GAAC,GAAE,KAAK,mBAAmBM,GAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,SAAK,YAAU,MAAK,KAAK,uBAAqB,EAAE,KAAK,oBAAoB,GAAE,KAAK,OAAO,KAAK,mBAAmB;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,QAAQN,KAAE;AAAC,SAAK,qBAAqBA,KAAG,MAAI,IAAG;AAAA,EAAC;AAAA,EAAC,qBAAqBA,KAAEM,KAAE;AAAC,QAAIL,MAAE;AAAG,eAAS,CAACgB,KAAEd,GAAC,KAAI,KAAK,YAAW;AAAC,UAAGH,IAAE,KAAK;AAAM,OAACiB,IAAE,aAAWX,IAAEW,GAAC,OAAKd,IAAE,cAAc,MAAIF,MAAE,MAAGD,IAAE,aAAa;AAAA,IAAG;AAAC,SAAK,qCAAmC,KAAK,mCAAiC,OAAGC,MAAE,MAAG,KAAK,kCAAkC,IAAGA,QAAI,EAAE,KAAK,SAAS,KAAG,MAAI,KAAK,WAAW,QAAM,KAAK,gBAAgB,GAAE,KAAK,aAAa,UAAU,GAAE,KAAK,aAAa,SAAS,GAAE,KAAK,OAAO,KAAK,iBAAiB,GAAE,KAAK,qBAAqB,GAAE,KAAK,uBAAuB,GAAE,KAAK,gBAAgB;AAAA,EAAE;AAAA,EAAC,0BAAyB;AAAC,SAAK,qBAAqBQ,IAAG,CAAAT,QAAGA,IAAE,iBAAeoB,GAAE,IAAK;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAM,CAACnB,IAAE,8BAA4B,CAAC,EAAE,KAAK,YAAY,CAAAD,QAAG,CAACA,IAAE,OAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK;AAAA,EAAc;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK;AAAA,EAAgB;AAAA,EAAC,gBAAgBA,KAAE;AAAC,QAAIM,MAAE;AAAG,WAAO,KAAK,WAAW,QAAS,CAAAL,QAAGK,MAAEL,IAAE,gBAAgBD,GAAC,KAAGM,GAAE,GAAEA;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,SAAK,mCAAiC;AAAA,EAAE;AAAA,EAAC,WAAWN,KAAEM,KAAEL,KAAE;AAAC,UAAMgB,MAAEjB,IAAE;AAAiB,QAAG,MAAIiB,IAAE,SAAS,QAAM;AAAG,SAAK,sBAAoBhB,MAAED,IAAE;AAAiB,UAAMG,MAAEG,IAAE;AAAO,QAAG,KAAK,WAASH,QAAIe,GAAE,aAAW,CAAC,KAAK,iBAAef,QAAIe,GAAE,UAAQ,CAAC,KAAK,YAAU,CAAClB,IAAE,iBAAiB,EAAE,QAAM;AAAG,UAAMe,MAAET,IAAE;AAAI,QAAG,CAACS,IAAE,QAAQ,EAAE,QAAM;AAAG,UAAMH,MAAE,IAAEZ,IAAE,YAAWK,MAAEL,IAAE;AAAW,IAAAe,IAAE,OAAOH,KAAEP,GAAC;AAAE,UAAMgB,MAAE,KAAK;AAAM,QAAG,KAAK,QAAQ,aAAWrB,IAAE,aAAWC,KAAE,KAAK,eAAe,SAAOE,KAAE,KAAK,gBAAgB,qBAAmB,KAAK,qBAAoB,KAAK,gBAAgB,mBAAiB,KAAK,sBAAoB,KAAK,iBAAgB,KAAK,gBAAgB,OAAKA,QAAIe,GAAE,SAAOJ,GAAE,eAAaA,GAAE,iBAAgBd,IAAE,cAAc,KAAK,KAAK,GAAEe,IAAE,KAAKM,GAAC,GAAErB,IAAE,UAAQmB,GAAE,UAAQE,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,IAAE,UAAUC,GAAG,gBAAgB,IAAGhB,IAAE,SAAOiB,GAAE,aAAW,KAAK,eAAe,qBAAmBV,MAAIZ,IAAE,8BAA4BK,IAAE,SAAOiB,GAAE,SAAS,UAAQL,MAAE,GAAEA,MAAED,IAAE,UAASC,MAAI,MAAK,mBAAmBD,IAAE,QAAQC,GAAC,GAAElB,GAAC,GAAE,KAAK,kBAAkBA,IAAE,YAAWwB,IAAGxB,IAAE,KAAK,CAAC;AAAE,WAAO,KAAK,WAAW,OAAK,KAAG,KAAK,iBAAiB,OAAQ,CAAC,EAAC,aAAYC,KAAE,UAASC,IAAC,MAAI;AAAC,UAAGI,IAAE,SAAOiB,GAAE,sBAAoBtB,IAAE,oBAAkBD,IAAE,YAAY;AAAO,YAAK,EAAC,aAAYyB,IAAC,IAAExB,KAAEU,MAAE,EAAEc,GAAC,KAAGA,MAAE,KAAGtB,QAAIe,GAAE;AAAM,MAAAP,QAAI,KAAK,yBAAyB,KAAK,OAAMC,KAAEP,GAAC,GAAEgB,IAAE,UAAUC,GAAG,gBAAgB;AAAG,eAAQhB,MAAE,GAAEA,MAAEW,IAAE,UAASX,MAAI,MAAK,mBAAmBW,IAAE,QAAQX,GAAC,GAAEN,GAAC,GAAEE,IAAE,OAAO,KAAK,cAAc;AAAE,MAAAS,OAAG,EAAE,KAAK,aAAa,MAAII,IAAE,KAAKM,GAAC,GAAE,KAAK,KAAK,OAAO,WAAW,kBAAkB,iBAAiB,KAAK,eAAe,gBAAe,KAAK,cAAc,WAAW,GAAEI,KAAEzB,IAAE,KAAK;AAAA,IAAE,CAAE,GAAEqB,IAAE,gBAAgB,IAAI,GAAEN,IAAE,eAAe,GAAE,KAAK,eAAe,wBAAwB,GAAE;AAAA,EAAE;AAAA,EAAC,yBAAyBf,KAAEM,KAAEL,KAAE;AAAC,MAAE,KAAK,aAAa,MAAI,KAAK,gBAAc,IAAIyB,IAAE1B,KAAE,KAAE,IAAG,KAAK,cAAc,OAAOM,KAAEL,GAAC,GAAE,KAAK,cAAc,KAAKD,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAe;AAAC,UAAM,KAAK,2BAA2B,UAAU;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,SAAK,OAAO,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAEM,KAAEL,KAAEgB,KAAE;AAJ9oV;AAI+oV,QAAId,MAAE;AAAE,eAAUY,OAAK,KAAK,WAAW,OAAO,EAAE,CAAAZ,QAAE,KAAAY,IAAE,cAAF,wBAAAA,KAAcf,KAAEM,KAAEL,KAAEgB,KAAEd,SAAIA;AAAA,EAAC;AAAA,EAAC,oCAAmC;AAAC,QAAG,KAAK,iBAAiB,MAAM,GAAE,MAAI,KAAK,WAAW,KAAK;AAAO,UAAMH,MAAE,KAAK,KAAK,IAAI;AAAU,SAAK,WAAW,QAAS,CAACM,KAAEL,QAAI;AAAC,YAAMgB,MAAEjB,IAAE,QAAQC,IAAE,KAAK,GAAEE,MAAEc,OAAG,GAAEF,MAAE,KAAK,WAAW,QAAMd,IAAE,gBAAcE,MAAES,IAAE,WAASA,IAAE,eAAaT,MAAEc,MAAE;AAAG,WAAK,iBAAiB,KAAK,IAAI,GAAGhB,KAAEK,KAAES,GAAC,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK,iBAAiB,KAAM,CAACf,KAAEM,QAAIN,IAAE,QAAMM,IAAE,KAAM;AAAA,EAAC;AAAA,EAAC,mBAAmBN,KAAEM,KAAE;AAAC,UAAML,MAAE,KAAK;AAAQ,IAAAA,IAAE,WAAS,CAAC,GAAE,GAAEK,IAAE,YAAWA,IAAE,UAAU,GAAE,EAAEL,IAAE,kBAAiB,GAAED,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAE,GAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAEC,IAAE,MAAKA,IAAE,GAAG,GAAE,EAAEA,IAAE,YAAW,CAAC,CAACD,IAAE,CAAC,GAAE,CAACA,IAAE,CAAC,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,UAAMA,MAAE,EAAE,KAAK,YAAY,CAAAA,QAAGA,IAAE,QAAS;AAAE,IAAAA,QAAI,KAAK,cAAY,KAAK,YAAUA,KAAE,KAAK,OAAO,KAAK,aAAYA,GAAC;AAAA,EAAE;AAAA,EAAC,uBAAsB;AAAC,UAAMA,MAAE,EAAE,KAAK,YAAY,CAAAA,QAAGA,IAAE,aAAc;AAAE,IAAAA,QAAI,KAAK,mBAAiB,KAAK,iBAAeA,KAAE,KAAK,OAAO,KAAK,kBAAiBA,GAAC;AAAA,EAAE;AAAA,EAAC,yBAAwB;AAAC,UAAMA,MAAE,EAAE,KAAK,YAAY,CAAAA,QAAGA,IAAE,eAAgB;AAAE,IAAAA,QAAI,KAAK,qBAAmB,KAAK,mBAAiBA,KAAE,KAAK,OAAO,KAAK,oBAAmBA,GAAC;AAAA,EAAE;AAAA,EAAC,kBAAkBA,KAAEM,KAAE;AAAC,SAAK,6BAA6BN,KAAEA,KAAEM,GAAC;AAAE,UAAML,MAAE,KAAK;AAAM,IAAAA,IAAE,cAAc,KAAK,wBAAuB,KAAK,iBAAgB,IAAI,GAAEA,IAAE,QAAQ,KAAK,QAAQ,GAAEA,IAAE,WAAWa,GAAG,gBAAe,GAAET,IAAG,KAAK,UAAS,UAAU,CAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BL,KAAEM,KAAEL,KAAE;AAAC,QAAGc,GAAE,KAAK,gBAAgB,OAAMd,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,GAAE,KAAK,gBAAgB,QAAQ;AAAO,UAAMgB,MAAE,IAAI,WAAWjB,MAAEM,MAAE,CAAC;AAAE,QAAIH,MAAE;AAAE,aAAQS,MAAE,GAAEA,MAAEN,KAAEM,MAAI,UAAQX,MAAE,GAAEA,MAAED,KAAEC,OAAI;AAAC,YAAMc,MAAE,KAAK,MAAMd,MAAE,EAAE,GAAEI,MAAE,KAAK,MAAMO,MAAE,EAAE;AAAE,MAAAG,MAAE,KAAGV,MAAE,KAAG,KAAGU,MAAEf,MAAE,MAAI,KAAGK,MAAEC,MAAE,MAAIW,IAAEd,KAAG,IAAE,KAAIc,IAAEd,KAAG,IAAE,KAAIc,IAAEd,KAAG,IAAE,KAAIc,IAAEd,KAAG,IAAE,QAAMc,IAAEd,KAAG,IAAE,KAAIc,IAAEd,KAAG,IAAE,KAAIc,IAAEd,KAAG,IAAE,KAAIc,IAAEd,KAAG,IAAE,IAAEY,OAAG,IAAEV,MAAE,IAAEJ,MAAE,IAAEW,MAAE,IAAE,MAAI,IAAEG,MAAE,IAAEV,MAAE,IAAE;AAAA,IAAI;AAAC,SAAK,gBAAgB,UAAQ,IAAIS,GAAG,KAAK,OAAM,EAAC,QAAO,EAAG,YAAW,aAAYL,GAAG,MAAK,UAAS,EAAG,eAAc,cAAa,EAAG,SAAQ,OAAMT,KAAE,QAAOM,IAAC,GAAEW,GAAC;AAAE,UAAMF,MAAE,IAAIT;AAAE,IAAAS,IAAE,WAAS,MAAG,KAAK,yBAAuB,KAAK,2BAA2B,QAAQY,IAAEZ,GAAC,GAAE,KAAK,WAASW,GAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM,EAAC,iBAAgB,MAAM,KAAK,KAAK,WAAW,OAAO,CAAC,GAAE,wBAAuB,CAAA1B,QAAG,KAAK,WAAW,IAAIA,GAAC,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC4B,GAAE,CAAC,GAAE9B,IAAG,WAAU,oCAAmC,MAAM,GAAE,EAAE,CAAC8B,GAAE,EAAC,aAAY,KAAE,CAAC,CAAC,GAAE9B,IAAG,WAAU,8BAA6B,MAAM,GAAE,EAAE,CAAC8B,GAAE,EAAC,aAAY,KAAE,CAAC,CAAC,GAAE9B,IAAG,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAAC8B,GAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE9B,IAAG,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC8B,GAAE,CAAC,GAAE9B,IAAG,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC8B,GAAE,CAAC,GAAE9B,IAAG,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC8B,GAAE,EAAC,MAAK,SAAQ,UAAS,KAAE,CAAC,CAAC,GAAE9B,IAAG,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC8B,GAAE,CAAC,GAAE9B,IAAG,WAAU,WAAU,IAAI,GAAEA,MAAG,EAAE,CAACc,GAAE,uCAAuC,CAAC,GAAEd,GAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYE,KAAEM,KAAEL,KAAE;AAAC,SAAK,cAAYD,KAAE,KAAK,WAASM,KAAE,KAAK,QAAML;AAAA,EAAC;AAAC;AAAC,IAAMuB,MAAG,CAAC,CAAC,GAAE,KAAG,GAAE,GAAE,CAAC,KAAG,KAAG,CAAC,CAAC;AAA7B,IAA+BK,MAAG;AAAlC,IAAqChB,MAAGY,GAAE;;;ACAp2a,IAAIK;AAAE,CAAC,SAASA,KAAE;AAAC,WAASC,IAAED,KAAEC,KAAE;AAAC,UAAMC,MAAEF,IAAEC,GAAC,GAAEE,MAAEH,IAAEC,MAAE,CAAC,GAAEG,MAAEJ,IAAEC,MAAE,CAAC;AAAE,WAAO,KAAK,KAAKC,MAAEA,MAAEC,MAAEA,MAAEC,MAAEA,GAAC;AAAA,EAAC;AAAC,WAASF,IAAEF,KAAEC,KAAE;AAAC,UAAMC,MAAEF,IAAEC,GAAC,GAAEE,MAAEH,IAAEC,MAAE,CAAC,GAAEG,MAAEJ,IAAEC,MAAE,CAAC,GAAEI,MAAE,IAAE,KAAK,KAAKH,MAAEA,MAAEC,MAAEA,MAAEC,MAAEA,GAAC;AAAE,IAAAJ,IAAEC,GAAC,KAAGI,KAAEL,IAAEC,MAAE,CAAC,KAAGI,KAAEL,IAAEC,MAAE,CAAC,KAAGI;AAAA,EAAC;AAAC,WAASF,IAAEH,KAAEC,KAAEC,KAAE;AAAC,IAAAF,IAAEC,GAAC,KAAGC,KAAEF,IAAEC,MAAE,CAAC,KAAGC,KAAEF,IAAEC,MAAE,CAAC,KAAGC;AAAA,EAAC;AAAC,WAASE,IAAEJ,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,MAAEJ,KAAE;AAAC,KAACG,MAAEA,OAAGJ,KAAGK,GAAC,IAAEL,IAAEC,GAAC,IAAEC,IAAEC,GAAC,GAAEC,IAAEC,MAAE,CAAC,IAAEL,IAAEC,MAAE,CAAC,IAAEC,IAAEC,MAAE,CAAC,GAAEC,IAAEC,MAAE,CAAC,IAAEL,IAAEC,MAAE,CAAC,IAAEC,IAAEC,MAAE,CAAC;AAAA,EAAC;AAAC,WAASE,IAAEL,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,MAAEJ,KAAE;AAAC,KAACG,MAAEA,OAAGJ,KAAGK,GAAC,IAAEL,IAAEC,GAAC,IAAEC,IAAEC,GAAC,GAAEC,IAAEC,MAAE,CAAC,IAAEL,IAAEC,MAAE,CAAC,IAAEC,IAAEC,MAAE,CAAC,GAAEC,IAAEC,MAAE,CAAC,IAAEL,IAAEC,MAAE,CAAC,IAAEC,IAAEC,MAAE,CAAC;AAAA,EAAC;AAAC,EAAAH,IAAE,SAAOC,KAAED,IAAE,YAAUE,KAAEF,IAAE,QAAMG,KAAEH,IAAE,MAAII,KAAEJ,IAAE,WAASK;AAAC,EAAEL,QAAIA,MAAE,CAAC,EAAE;;;ACA/Z,SAASM,IAAEA,KAAEC,MAAE,OAAG;AAAC,SAAOD,OAAGE,KAAED,MAAE,IAAI,MAAMD,GAAC,EAAE,KAAK,CAAC,IAAE,IAAI,MAAMA,GAAC,IAAE,IAAI,aAAaA,GAAC;AAAC;AAAC,SAASC,IAAED,KAAE;AAAC,SAAO,UAAQE,KAAE,MAAM,KAAKF,GAAC,IAAE,IAAI,aAAaA,GAAC;AAAC;AAAC,SAASG,IAAEC,KAAEJ,KAAEC,KAAE;AAAC,SAAO,MAAM,QAAQG,GAAC,IAAEA,IAAE,MAAMJ,KAAEA,MAAEC,GAAC,IAAEG,IAAE,SAASJ,KAAEA,MAAEC,GAAC;AAAC;;;ACAklB,IAAMI,KAAEC;AAAR,IAAUC,KAAE,CAAC,CAAC,MAAI,MAAI,GAAE,GAAE,CAAC,KAAG,MAAI,GAAE,GAAE,CAAC,KAAG,KAAG,GAAE,GAAE,CAAC,MAAI,KAAG,GAAE,GAAE,CAAC,MAAI,MAAI,IAAG,GAAE,CAAC,KAAG,MAAI,IAAG,GAAE,CAAC,KAAG,KAAG,IAAG,GAAE,CAAC,MAAI,KAAG,IAAG,CAAC;AAAhH,IAAkHC,KAAE,CAAC,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE;AAA3J,IAA6JC,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAA/K,IAAiLC,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAA3P,IAA6PC,MAAE,IAAI,MAAM,EAAE;AAAE,SAAQC,MAAG,GAAEA,MAAG,GAAEA,MAAK,UAAQC,MAAE,GAAEA,MAAE,GAAEA,MAAI,CAAAF,IAAE,IAAEC,MAAGC,GAAC,IAAED;AAAG,IAAME,MAAE,IAAI,MAAM,EAAE;AAAE,SAAQF,MAAG,GAAEA,MAAG,GAAEA,MAAK,CAAAE,IAAE,IAAEF,MAAG,CAAC,IAAE,GAAEE,IAAE,IAAEF,MAAG,CAAC,IAAE,GAAEE,IAAE,IAAEF,MAAG,CAAC,IAAE,GAAEE,IAAE,IAAEF,MAAG,CAAC,IAAE,GAAEE,IAAE,IAAEF,MAAG,CAAC,IAAE,GAAEE,IAAE,IAAEF,MAAG,CAAC,IAAE;AAAE,SAASG,GAAEF,KAAEP,KAAE;AAAC,QAAM,QAAQA,GAAC,MAAIA,MAAE,CAACA,KAAEA,KAAEA,GAAC;AAAG,QAAMU,MAAE,IAAI,MAAM,EAAE;AAAE,WAAQC,MAAE,GAAEA,MAAE,GAAEA,MAAI,CAAAD,IAAE,IAAEC,GAAC,IAAEV,GAAEU,GAAC,EAAE,CAAC,IAAEX,IAAE,CAAC,GAAEU,IAAE,IAAEC,MAAE,CAAC,IAAEV,GAAEU,GAAC,EAAE,CAAC,IAAEX,IAAE,CAAC,GAAEU,IAAE,IAAEC,MAAE,CAAC,IAAEV,GAAEU,GAAC,EAAE,CAAC,IAAEX,IAAE,CAAC;AAAE,SAAO,IAAIK,GAAEE,KAAE,CAAC,CAACK,GAAE,UAAS,IAAID,GAAED,KAAE,GAAE,IAAE,CAAC,GAAE,CAACE,GAAE,QAAO,IAAID,GAAET,IAAE,CAAC,CAAC,GAAE,CAACU,GAAE,KAAI,IAAID,GAAER,IAAE,CAAC,CAAC,CAAC,GAAE,CAAC,CAACS,GAAE,UAASR,EAAC,GAAE,CAACQ,GAAE,QAAOP,GAAC,GAAE,CAACO,GAAE,KAAIJ,GAAC,CAAC,CAAC;AAAC;AAAC,IAAMK,KAAE,CAAC,CAAC,MAAI,GAAE,IAAG,GAAE,CAAC,KAAG,GAAE,IAAG,GAAE,CAAC,KAAG,GAAE,GAAE,GAAE,CAAC,MAAI,GAAE,GAAE,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,KAAG,CAAC,CAAC;AAAvE,IAAyEC,KAAE,CAAC,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,IAAG,GAAE,IAAG,GAAE,GAAE,IAAG,GAAE,IAAG,IAAG,CAAC;AAAnI,IAAqIC,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAvL,IAAyLC,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,SAASC,GAAEV,KAAEP,KAAE;AAAC,QAAM,QAAQA,GAAC,MAAIA,MAAE,CAACA,KAAEA,KAAEA,GAAC;AAAG,QAAMU,MAAE,IAAI,MAAM,EAAE;AAAE,WAAQC,MAAE,GAAEA,MAAE,GAAEA,MAAI,CAAAD,IAAE,IAAEC,GAAC,IAAEE,GAAEF,GAAC,EAAE,CAAC,IAAEX,IAAE,CAAC,GAAEU,IAAE,IAAEC,MAAE,CAAC,IAAEE,GAAEF,GAAC,EAAE,CAAC,IAAEX,IAAE,CAAC,GAAEU,IAAE,IAAEC,MAAE,CAAC,IAAEE,GAAEF,GAAC,EAAE,CAAC,IAAEX,IAAE,CAAC;AAAE,SAAO,IAAIK,GAAEE,KAAE,CAAC,CAACK,GAAE,UAAS,IAAID,GAAED,KAAE,GAAE,IAAE,CAAC,GAAE,CAACE,GAAE,QAAO,IAAID,GAAEG,IAAE,CAAC,CAAC,CAAC,GAAE,CAAC,CAACF,GAAE,UAASG,EAAC,GAAE,CAACH,GAAE,QAAOI,GAAC,CAAC,CAAC;AAAC;AAAC,IAAME,KAAEC,GAAE,MAAI,GAAE,IAAG;AAAnB,IAAqBC,KAAED,GAAE,KAAG,GAAE,IAAG;AAAjC,IAAmCE,KAAEF,GAAE,GAAE,GAAE,GAAE;AAA7C,IAA+CG,KAAEH,GAAE,GAAE,KAAG,CAAC;AAAzD,IAA2DI,KAAEvB,IAAE;AAA/D,IAAiEwB,KAAExB,IAAE;AAArE,IAAuEyB,KAAEzB,IAAE;AAA3E,IAA6E0B,KAAE1B,IAAE;AAAjF,IAAmF2B,KAAE3B,IAAE;AAAEU,GAAEa,IAAEL,IAAEI,EAAC,GAAEZ,GAAEc,IAAEN,IAAEE,EAAC,GAAE,EAAEK,IAAEF,IAAEC,EAAC,GAAEJ,GAAEK,IAAEA,EAAC,GAAEf,GAAEa,IAAEH,IAAEE,EAAC,GAAEZ,GAAEc,IAAEJ,IAAEC,EAAC,GAAE,EAAEK,IAAEH,IAAEC,EAAC,GAAEJ,GAAEM,IAAEA,EAAC,GAAEhB,GAAEa,IAAEF,IAAEC,EAAC,GAAEZ,GAAEc,IAAEH,IAAEH,EAAC,GAAE,EAAES,IAAEJ,IAAEC,EAAC,GAAEJ,GAAEO,IAAEA,EAAC;AAAE,IAAMC,KAAE,CAACV,IAAEE,IAAEC,IAAEC,EAAC;AAAhB,IAAkBO,KAAE,CAAC,GAAE,IAAG,GAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAxE,IAA0EG,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAApG,IAAsGC,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,SAASC,IAAGzB,KAAEP,KAAE;AAAC,QAAM,QAAQA,GAAC,MAAIA,MAAE,CAACA,KAAEA,KAAEA,GAAC;AAAG,QAAMU,MAAE,IAAI,MAAM,EAAE;AAAE,WAAQC,MAAE,GAAEA,MAAE,GAAEA,MAAI,CAAAD,IAAE,IAAEC,GAAC,IAAEiB,GAAEjB,GAAC,EAAE,CAAC,IAAEX,IAAE,CAAC,GAAEU,IAAE,IAAEC,MAAE,CAAC,IAAEiB,GAAEjB,GAAC,EAAE,CAAC,IAAEX,IAAE,CAAC,GAAEU,IAAE,IAAEC,MAAE,CAAC,IAAEiB,GAAEjB,GAAC,EAAE,CAAC,IAAEX,IAAE,CAAC;AAAE,SAAO,IAAIK,GAAEE,KAAE,CAAC,CAACK,GAAE,UAAS,IAAID,GAAED,KAAE,GAAE,IAAE,CAAC,GAAE,CAACE,GAAE,QAAO,IAAID,GAAEkB,IAAE,CAAC,CAAC,CAAC,GAAE,CAAC,CAACjB,GAAE,UAASkB,EAAC,GAAE,CAAClB,GAAE,QAAOmB,GAAC,CAAC,CAAC;AAAC;AAAC,SAASE,IAAG1B,KAAEP,KAAEU,KAAEC,KAAEuB,MAAE,EAAC,IAAG,KAAE,GAAE;AAAC,QAAMf,MAAE,CAAC,KAAK,IAAGgB,MAAE,IAAE,KAAK,IAAGC,MAAE,CAAC,KAAK,KAAG,GAAEC,MAAE,KAAK,IAAGC,MAAE,KAAK,IAAI,GAAE,KAAK,MAAM5B,GAAC,CAAC,GAAE6B,MAAE,KAAK,IAAI,GAAE,KAAK,MAAM5B,GAAC,CAAC,GAAE6B,OAAGF,MAAE,MAAIC,MAAE,IAAGE,MAAEzC,IAAE,IAAEwC,GAAC,GAAE5B,MAAEZ,IAAE,IAAEwC,GAAC,GAAEE,MAAE1C,IAAE,IAAEwC,GAAC,GAAEG,KAAE,CAAC;AAAE,MAAIC,KAAE;AAAE,WAAQC,MAAE,GAAEA,OAAGN,KAAEM,OAAI;AAAC,UAAMtC,MAAE,CAAC,GAAEG,MAAEmC,MAAEN,KAAE5B,MAAEyB,MAAE1B,MAAE2B,KAAEH,MAAE,KAAK,IAAIvB,GAAC;AAAE,aAAQyB,MAAE,GAAEA,OAAGE,KAAEF,OAAI;AAAC,YAAMC,MAAED,MAAEE,KAAEC,MAAEpB,MAAEkB,MAAEF,KAAEK,MAAE,KAAK,IAAID,GAAC,IAAEL,KAAES,MAAE,KAAK,IAAIhC,GAAC,GAAEmC,MAAE,CAAC,KAAK,IAAIP,GAAC,IAAEL;AAAE,MAAAO,IAAE,IAAEG,EAAC,IAAEJ,MAAExC,KAAEyC,IAAE,IAAEG,KAAE,CAAC,IAAED,MAAE3C,KAAEyC,IAAE,IAAEG,KAAE,CAAC,IAAEE,MAAE9C,KAAEY,IAAE,IAAEgC,EAAC,IAAEJ,KAAE5B,IAAE,IAAEgC,KAAE,CAAC,IAAED,KAAE/B,IAAE,IAAEgC,KAAE,CAAC,IAAEE,KAAEJ,IAAE,IAAEE,EAAC,IAAEP,KAAEK,IAAE,IAAEE,KAAE,CAAC,IAAElC,KAAEH,IAAE,KAAKqC,EAAC,GAAE,EAAEA;AAAA,IAAC;AAAC,IAAAD,GAAE,KAAKpC,GAAC;AAAA,EAAC;AAAC,QAAMuC,KAAE,IAAI;AAAM,WAAQD,MAAE,GAAEA,MAAEN,KAAEM,MAAI,UAAQtC,MAAE,GAAEA,MAAE+B,KAAE/B,OAAI;AAAC,UAAMP,MAAE2C,GAAEE,GAAC,EAAEtC,GAAC,GAAEG,MAAEiC,GAAEE,GAAC,EAAEtC,MAAE,CAAC,GAAEI,MAAEgC,GAAEE,MAAE,CAAC,EAAEtC,MAAE,CAAC,GAAE2B,MAAES,GAAEE,MAAE,CAAC,EAAEtC,GAAC;AAAE,UAAIsC,OAAGC,GAAE,KAAK9C,GAAC,GAAE8C,GAAE,KAAKnC,GAAC,GAAEmC,GAAE,KAAKZ,GAAC,KAAGW,QAAIN,MAAE,KAAGO,GAAE,KAAK9C,GAAC,GAAE8C,GAAE,KAAKpC,GAAC,GAAEoC,GAAE,KAAKnC,GAAC,MAAImC,GAAE,KAAK9C,GAAC,GAAE8C,GAAE,KAAKpC,GAAC,GAAEoC,GAAE,KAAKnC,GAAC,GAAEmC,GAAE,KAAKnC,GAAC,GAAEmC,GAAE,KAAKZ,GAAC,GAAEY,GAAE,KAAK9C,GAAC;AAAA,EAAE;AAAC,QAAM+C,KAAE,CAAC,CAACnC,GAAE,UAASkC,EAAC,GAAE,CAAClC,GAAE,QAAOkC,EAAC,CAAC,GAAEE,KAAE,CAAC,CAACpC,GAAE,UAAS,IAAID,GAAE8B,KAAE,GAAE,IAAE,CAAC,GAAE,CAAC7B,GAAE,QAAO,IAAID,GAAEC,KAAE,GAAE,IAAE,CAAC,CAAC;AAAE,SAAOsB,IAAE,OAAKc,GAAE,KAAK,CAACpC,GAAE,KAAI,IAAID,GAAE+B,KAAE,GAAE,IAAE,CAAC,CAAC,GAAEK,GAAE,KAAK,CAACnC,GAAE,KAAIkC,EAAC,CAAC,IAAGZ,IAAE,WAASa,GAAE,CAAC,EAAE,CAAC,IAAEnC,GAAE,QAAOoC,GAAE,CAAC,EAAE,CAAC,IAAEpC,GAAE,QAAOmC,GAAE,KAAK,CAACnC,GAAE,UAAS,IAAI,MAAMkC,GAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,GAAEE,GAAE,KAAK,CAACpC,GAAE,UAAS,IAAID,GAAE,aAAa,KAAKuB,IAAE,MAAM,GAAE,GAAE,IAAE,CAAC,CAAC,IAAG,IAAI7B,GAAEE,KAAEyC,IAAED,EAAC;AAAC;AAAC,SAASE,IAAG1C,KAAEP,KAAEU,KAAEC,KAAE;AAAC,QAAK,EAAC,kBAAiBuB,KAAE,SAAQf,IAAC,IAAE+B,IAAGlD,KAAEU,KAAEC,GAAC;AAAE,SAAO,IAAIN,GAAEE,KAAE2B,KAAEf,GAAC;AAAC;AAAC,SAAS+B,IAAG3C,KAAEP,KAAEU,KAAE;AAAC,QAAMC,MAAEJ;AAAE,MAAI2B,KAAEf;AAAE,MAAGT,IAAE,CAAAwB,MAAE,CAAC,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,CAAC,GAAEf,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,OAAM;AAAC,UAAMZ,MAAEI,OAAG,IAAE,KAAK,KAAK,CAAC,KAAG;AAAE,IAAAuB,MAAE,CAAC,CAACvB,KAAEJ,KAAE,GAAEI,KAAEJ,KAAE,GAAE,CAACI,KAAE,CAACJ,KAAE,GAAEI,KAAE,CAACJ,KAAE,GAAE,GAAE,CAACI,KAAEJ,KAAE,GAAEI,KAAEJ,KAAE,GAAE,CAACI,KAAE,CAACJ,KAAE,GAAEI,KAAE,CAACJ,KAAEA,KAAE,GAAE,CAACI,KAAEJ,KAAE,GAAEI,KAAE,CAACJ,KAAE,GAAE,CAACI,KAAE,CAACJ,KAAE,GAAEI,GAAC,GAAEQ,MAAE,CAAC,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,IAAG,IAAG,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,IAAG,IAAG,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,EAAC;AAAC,WAAQoB,MAAE,GAAEA,MAAEL,IAAE,QAAOK,OAAG,EAAE,CAAAxC,GAAE,MAAMmC,KAAEK,KAAEhC,MAAER,GAAE,OAAOmC,KAAEK,GAAC,CAAC;AAAE,MAAIJ,MAAE,CAAC;AAAE,WAASC,IAAEpC,KAAEU,KAAE;AAAC,IAAAV,MAAEU,QAAI,CAACV,KAAEU,GAAC,IAAE,CAACA,KAAEV,GAAC;AAAG,UAAMW,MAAEX,IAAE,SAAS,IAAE,MAAIU,IAAE,SAAS;AAAE,QAAGyB,IAAExB,GAAC,EAAE,QAAOwB,IAAExB,GAAC;AAAE,QAAIQ,MAAEe,IAAE;AAAO,WAAOA,IAAE,UAAQ,GAAEnC,GAAE,IAAImC,KAAE,IAAElC,KAAEkC,KAAE,IAAExB,KAAEwB,KAAEf,GAAC,GAAEpB,GAAE,MAAMmC,KAAEf,KAAEZ,MAAER,GAAE,OAAOmC,KAAEf,GAAC,CAAC,GAAEA,OAAG,GAAEgB,IAAExB,GAAC,IAAEQ,KAAEA;AAAA,EAAC;AAAC,WAAQoB,MAAE,GAAEA,MAAEvC,KAAEuC,OAAI;AAAC,UAAMhC,MAAEY,IAAE,QAAOnB,MAAE,IAAI,MAAM,IAAEO,GAAC;AAAE,aAAQG,MAAE,GAAEA,MAAEH,KAAEG,OAAG,GAAE;AAAC,YAAMH,MAAEY,IAAET,GAAC,GAAEC,MAAEQ,IAAET,MAAE,CAAC,GAAEwB,MAAEf,IAAET,MAAE,CAAC,GAAEyB,MAAEC,IAAE7B,KAAEI,GAAC,GAAE0B,MAAED,IAAEzB,KAAEuB,GAAC,GAAEI,MAAEF,IAAEF,KAAE3B,GAAC,GAAEgC,MAAE,IAAE7B;AAAE,MAAAV,IAAEuC,GAAC,IAAEhC,KAAEP,IAAEuC,MAAE,CAAC,IAAEJ,KAAEnC,IAAEuC,MAAE,CAAC,IAAED,KAAEtC,IAAEuC,MAAE,CAAC,IAAE5B,KAAEX,IAAEuC,MAAE,CAAC,IAAEF,KAAErC,IAAEuC,MAAE,CAAC,IAAEJ,KAAEnC,IAAEuC,MAAE,CAAC,IAAEL,KAAElC,IAAEuC,MAAE,CAAC,IAAED,KAAEtC,IAAEuC,MAAE,CAAC,IAAEF,KAAErC,IAAEuC,MAAE,CAAC,IAAEJ,KAAEnC,IAAEuC,MAAE,EAAE,IAAEF,KAAErC,IAAEuC,MAAE,EAAE,IAAED;AAAA,IAAC;AAAC,IAAAnB,MAAEnB,KAAEmC,MAAE,CAAC;AAAA,EAAC;AAAC,QAAME,MAAE9B,IAAE2B,GAAC;AAAE,WAAQK,MAAE,GAAEA,MAAEF,IAAE,QAAOE,OAAG,EAAE,CAAAxC,GAAE,UAAUsC,KAAEE,GAAC;AAAE,QAAMD,MAAE,CAAC,CAAC1B,GAAE,UAASO,GAAC,GAAE,CAACP,GAAE,QAAOO,GAAC,CAAC;AAAE,SAAM,EAAC,kBAAiB,CAAC,CAACP,GAAE,UAAS,IAAID,GAAEJ,IAAE2B,GAAC,GAAE,GAAE,IAAE,CAAC,GAAE,CAACtB,GAAE,QAAO,IAAID,GAAE0B,KAAE,GAAE,IAAE,CAAC,CAAC,GAAE,SAAQC,IAAC;AAAC;AAAC,SAASa,IAAGnD,KAAEU,KAAEC,KAAEuB,KAAEf,KAAEgB,KAAEC,KAAEC,KAAEC,MAAE,MAAK;AAAC,QAAMC,MAAE5B,MAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,IAAE,CAAC,GAAE,GAAE,CAAC,GAAE6B,MAAE9B,MAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,IAAE,CAAC,GAAE,GAAE,CAAC;AAAE,EAAA0B,MAAEA,OAAG,CAAC,GAAE,CAAC;AAAE,QAAMK,MAAEP,MAAE,CAAC,MAAIA,IAAE,CAAC,GAAE,MAAIA,IAAE,CAAC,GAAE,MAAIA,IAAE,CAAC,GAAEA,IAAE,SAAO,IAAE,MAAIA,IAAE,CAAC,IAAE,GAAG,IAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAEtB,MAAE,EAAEO,GAAC,KAAG,MAAIA,IAAE,SAAOA,MAAE,CAAC,GAAE,CAAC,GAAEuB,MAAE,CAAC,CAAC9B,GAAE,UAAS,IAAID,GAAE4B,KAAE,GAAE,IAAE,CAAC,GAAE,CAAC3B,GAAE,QAAO,IAAID,GAAE6B,KAAE,GAAE,IAAE,CAAC,GAAE,CAAC5B,GAAE,KAAI,IAAID,GAAEyB,KAAEA,IAAE,MAAM,CAAC,GAAE,CAACxB,GAAE,OAAM,IAAID,GAAE8B,KAAE,GAAE,IAAE,CAAC,GAAE,CAAC7B,GAAE,MAAK,IAAID,GAAEC,KAAE,CAAC,CAAC,CAAC;AAAE,MAAG,QAAMuB,KAAE;AAAC,UAAM5B,MAAE,CAAC4B,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC;AAAE,IAAAO,IAAE,KAAK,CAAC9B,GAAE,SAAQ,IAAID,GAAEJ,KAAE,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,MAAG,QAAM8B,KAAE;AAAC,UAAM9B,MAAE,CAAC8B,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC;AAAE,IAAAK,IAAE,KAAK,CAAC9B,GAAE,SAAQ,IAAID,GAAEJ,KAAE,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,SAAO,IAAIF,GAAEL,KAAE0C,KAAE,MAAK,MAAKhC,GAAE,OAAM4B,GAAC;AAAC;AAA2Z,SAASc,IAAGC,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,MAAE,MAAGC,MAAE,MAAG;AAAC,MAAIC,MAAE;AAAE,QAAMC,MAAEN,KAAEO,MAAER;AAAE,MAAIS,MAAEL,GAAE,GAAEE,KAAE,CAAC,GAAEI,MAAEN,GAAE,GAAEE,MAAEE,KAAE,CAAC,GAAEG,MAAEP,GAAE,GAAE,IAAG,CAAC,GAAEQ,MAAER,GAAE,GAAE,GAAE,CAAC;AAAE,EAAAD,QAAIG,MAAEE,KAAEE,MAAEN,GAAE,GAAE,GAAE,CAAC,GAAEK,MAAEL,GAAE,GAAEE,KAAE,CAAC,GAAEK,MAAEP,GAAE,GAAE,GAAE,CAAC,GAAEQ,MAAER,GAAE,GAAE,IAAG,CAAC;AAAG,QAAMS,KAAE,CAACH,KAAED,GAAC,GAAEK,KAAE,CAACH,KAAEC,GAAC,GAAEG,KAAEb,MAAE,GAAEc,KAAE,KAAK,KAAKR,MAAEA,MAAED,MAAEA,GAAC;AAAE,MAAGJ,IAAE,UAAQc,MAAEf,MAAE,GAAEe,OAAG,GAAEA,OAAI;AAAC,UAAMlB,MAAEkB,OAAG,IAAE,KAAK,KAAGf,MAAGF,MAAEI,GAAE,KAAK,IAAIL,GAAC,IAAEQ,KAAED,KAAE,KAAK,IAAIP,GAAC,IAAEQ,GAAC;AAAE,IAAAM,GAAE,KAAKb,GAAC;AAAE,UAAMC,MAAEG,GAAEI,MAAE,KAAK,IAAIT,GAAC,IAAEiB,IAAE,CAACT,MAAES,IAAER,MAAE,KAAK,IAAIT,GAAC,IAAEiB,EAAC;AAAE,IAAAF,GAAE,KAAKb,GAAC;AAAA,EAAC;AAAA,MAAM,UAAQgB,MAAE,GAAEA,MAAEf,KAAEe,OAAI;AAAC,UAAMlB,MAAEkB,OAAG,IAAE,KAAK,KAAGf,MAAGF,MAAEI,GAAE,KAAK,IAAIL,GAAC,IAAEQ,KAAED,KAAE,KAAK,IAAIP,GAAC,IAAEQ,GAAC;AAAE,IAAAM,GAAE,KAAKb,GAAC;AAAE,UAAMC,MAAEG,GAAEI,MAAE,KAAK,IAAIT,GAAC,IAAEiB,IAAET,MAAES,IAAER,MAAE,KAAK,IAAIT,GAAC,IAAEiB,EAAC;AAAE,IAAAF,GAAE,KAAKb,GAAC;AAAA,EAAC;AAAC,QAAMiB,KAAE,IAAI,SAAMC,KAAE,IAAI;AAAM,MAAGf,KAAE;AAAC,aAAQL,MAAE,GAAEA,MAAEc,GAAE,QAAOd,MAAI,CAAAmB,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAKnB,MAAE,CAAC,GAAEmB,GAAE,KAAKnB,GAAC,GAAEoB,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAE,IAAAD,GAAE,KAAKL,GAAE,SAAO,CAAC,GAAEK,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,GAAEC,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC;AAAA,EAAC;AAAC,MAAGd,KAAE;AAAC,aAAQN,MAAE,GAAEA,MAAEc,GAAE,QAAOd,MAAI,CAAAmB,GAAE,KAAKnB,GAAC,GAAEmB,GAAE,KAAKnB,MAAE,CAAC,GAAEmB,GAAE,KAAK,CAAC,GAAEC,GAAE,KAAKpB,GAAC,GAAEoB,GAAE,KAAKpB,MAAE,CAAC,GAAEoB,GAAE,KAAK,CAAC;AAAE,IAAAD,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAKL,GAAE,SAAO,CAAC,GAAEM,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAKL,GAAE,SAAO,CAAC;AAAA,EAAC;AAAC,QAAMM,KAAEpB,IAAE,IAAEe,EAAC;AAAE,WAAQM,MAAE,GAAEA,MAAEN,IAAEM,MAAI,CAAAD,GAAE,IAAEC,GAAC,IAAER,GAAEQ,GAAC,EAAE,CAAC,GAAED,GAAE,IAAEC,MAAE,CAAC,IAAER,GAAEQ,GAAC,EAAE,CAAC,GAAED,GAAE,IAAEC,MAAE,CAAC,IAAER,GAAEQ,GAAC,EAAE,CAAC;AAAE,QAAMC,KAAEtB,IAAE,IAAEe,EAAC;AAAE,WAAQM,MAAE,GAAEA,MAAEN,IAAEM,MAAI,CAAAC,GAAE,IAAED,GAAC,IAAEP,GAAEO,GAAC,EAAE,CAAC,GAAEC,GAAE,IAAED,MAAE,CAAC,IAAEP,GAAEO,GAAC,EAAE,CAAC,GAAEC,GAAE,IAAED,MAAE,CAAC,IAAEP,GAAEO,GAAC,EAAE,CAAC;AAAE,QAAME,KAAE,CAAC,CAACZ,GAAE,UAASO,EAAC,GAAE,CAACP,GAAE,QAAOQ,EAAC,CAAC,GAAEK,KAAE,CAAC,CAACb,GAAE,UAAS,IAAIT,GAAEkB,IAAE,GAAE,IAAE,CAAC,GAAE,CAACT,GAAE,QAAO,IAAIT,GAAEoB,IAAE,GAAE,IAAE,CAAC,CAAC;AAAE,SAAO,IAAIG,GAAE1B,KAAEyB,IAAED,EAAC;AAAC;AAAC,SAASG,IAAG3B,KAAEC,KAAEO,KAAEC,KAAEG,KAAEC,KAAEC,IAAE;AAAC,QAAMC,KAAEH,MAAEZ,GAAEY,GAAC,IAAEP,GAAE,GAAE,GAAE,CAAC,GAAEW,KAAEH,MAAEb,GAAEa,GAAC,IAAER,GAAE,GAAE,GAAE,CAAC;AAAE,EAAAS,KAAEA,MAAG;AAAG,QAAMG,KAAEhB,IAAE;AAAE,EAAA2B,GAAEX,IAAEF,EAAC;AAAE,QAAMI,KAAElB,IAAE;AAAE,IAAEkB,IAAEF,IAAE,KAAK,IAAIhB,GAAC,CAAC;AAAE,QAAMmB,KAAEnB,IAAE;AAAE,IAAEmB,IAAED,IAAE,IAAG,GAAEX,GAAEY,IAAEA,IAAEJ,EAAC;AAAE,QAAMK,KAAEhB,GAAE,GAAE,GAAE,CAAC;AAAE,OAAK,IAAI,IAAE,EAAEY,IAAEI,EAAC,CAAC,IAAE,OAAIjB,GAAEiB,IAAE,GAAE,GAAE,CAAC;AAAE,QAAME,KAAEtB,IAAE;AAAE,IAAEsB,IAAEN,IAAEI,EAAC,GAAEO,GAAEL,IAAEA,EAAC,GAAE,EAAEF,IAAEE,IAAEN,EAAC;AAAE,QAAMO,KAAE,IAAEf,OAAGK,KAAE,IAAE,IAAGW,KAAEhB,OAAGK,KAAE,IAAE,IAAGe,MAAE5B,IAAE,IAAEuB,EAAC,GAAEM,MAAE7B,IAAE,IAAEwB,EAAC,GAAEM,KAAE9B,IAAE,IAAEuB,EAAC,GAAEQ,KAAE,IAAI,MAAM,IAAEvB,OAAGK,KAAE,IAAE,EAAE,GAAEY,MAAE,IAAI,MAAM,IAAEjB,OAAGK,KAAE,IAAE,EAAE;AAAE,EAAAA,OAAIe,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAEJ,GAAE,CAAC,GAAES,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAEJ,GAAE,CAAC,GAAES,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAEJ,GAAE,CAAC,GAAEW,GAAE,KAAGP,KAAE,EAAE,IAAE,GAAEO,GAAE,KAAGP,KAAE,KAAG,CAAC,IAAE,GAAEK,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAEK,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAEL,GAAE,CAAC,GAAEU,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAEK,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAEL,GAAE,CAAC,GAAEU,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAEK,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAEL,GAAE,CAAC,GAAEY,GAAE,KAAGP,KAAE,EAAE,IAAE,GAAEO,GAAE,KAAGP,KAAE,KAAG,CAAC,IAAE,GAAEM,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAE,CAACR,GAAE,CAAC,GAAEa,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAE,CAACR,GAAE,CAAC,GAAEa,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAE,CAACR,GAAE,CAAC,GAAEa,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAER,GAAE,CAAC,GAAEa,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAER,GAAE,CAAC,GAAEa,IAAE,KAAGL,KAAE,KAAG,CAAC,IAAER,GAAE,CAAC;AAAG,QAAMgB,MAAE,CAACjC,KAAEC,KAAEC,QAAI;AAAC,IAAA8B,GAAEhC,GAAC,IAAEC,KAAEyB,IAAE1B,GAAC,IAAEE;AAAA,EAAC;AAAE,MAAIgC,KAAE;AAAE,QAAMC,KAAElC,IAAE,GAAEmC,KAAEnC,IAAE;AAAE,WAAQC,MAAE,GAAEA,MAAEO,KAAEP,OAAI;AAAC,UAAMF,MAAEE,OAAG,IAAE,KAAK,KAAGO;AAAG,MAAE0B,IAAEd,IAAE,KAAK,IAAIrB,GAAC,CAAC,GAAE,EAAEoC,IAAEb,IAAE,KAAK,IAAIvB,GAAC,CAAC,GAAEQ,GAAE2B,IAAEA,IAAEC,EAAC,GAAEN,IAAE,IAAE5B,MAAE,CAAC,IAAEiC,GAAE,CAAC,GAAEL,IAAE,IAAE5B,MAAE,CAAC,IAAEiC,GAAE,CAAC,GAAEL,IAAE,IAAE5B,MAAE,CAAC,IAAEiC,GAAE,CAAC,GAAE,EAAEA,IAAEA,IAAE3B,GAAC,GAAEA,GAAE2B,IAAEA,IAAEf,EAAC,GAAES,IAAE,IAAE3B,MAAE,CAAC,IAAEiC,GAAE,CAAC,GAAEN,IAAE,IAAE3B,MAAE,CAAC,IAAEiC,GAAE,CAAC,GAAEN,IAAE,IAAE3B,MAAE,CAAC,IAAEiC,GAAE,CAAC,GAAEJ,GAAE,IAAE7B,MAAE,CAAC,IAAEA,MAAEO,KAAEsB,GAAE,IAAE7B,MAAE,CAAC,IAAE,GAAE2B,IAAE,KAAG3B,MAAEO,OAAG,CAAC,IAAEoB,IAAE,IAAE3B,MAAE,CAAC,IAAEiB,GAAE,CAAC,GAAEU,IAAE,KAAG3B,MAAEO,OAAG,CAAC,IAAEoB,IAAE,IAAE3B,MAAE,CAAC,IAAEiB,GAAE,CAAC,GAAEU,IAAE,KAAG3B,MAAEO,OAAG,CAAC,IAAEoB,IAAE,IAAE3B,MAAE,CAAC,IAAEiB,GAAE,CAAC,GAAEY,GAAE,KAAG7B,MAAEO,OAAG,CAAC,IAAEP,MAAEO,KAAEsB,GAAE,IAAE7B,MAAE,CAAC,IAAE;AAAE,UAAMD,OAAGC,MAAE,KAAGO;AAAE,IAAAwB,IAAEC,MAAIhC,KAAEA,GAAC,GAAE+B,IAAEC,MAAIhC,MAAEO,KAAEP,GAAC,GAAE+B,IAAEC,MAAIjC,KAAEA,GAAC,GAAEgC,IAAEC,MAAIjC,KAAEA,GAAC,GAAEgC,IAAEC,MAAIhC,MAAEO,KAAEP,GAAC,GAAE+B,IAAEC,MAAIjC,MAAEQ,KAAER,GAAC;AAAA,EAAC;AAAC,MAAGa,IAAE;AAAC,aAAQd,MAAE,GAAEA,MAAES,KAAET,OAAI;AAAC,YAAMC,OAAGD,MAAE,KAAGS;AAAE,MAAAwB,IAAEC,MAAIV,KAAE,GAAEC,KAAE,CAAC,GAAEQ,IAAEC,MAAIlC,KAAEyB,KAAE,CAAC,GAAEQ,IAAEC,MAAIjC,KAAEwB,KAAE,CAAC;AAAA,IAAC;AAAC,aAAQzB,MAAE,GAAEA,MAAES,KAAET,OAAI;AAAC,YAAMC,OAAGD,MAAE,KAAGS;AAAE,MAAAwB,IAAEC,MAAIlC,MAAES,KAAEgB,KAAE,CAAC,GAAEQ,IAAEC,MAAIV,KAAE,GAAEC,KAAE,CAAC,GAAEQ,IAAEC,MAAIjC,MAAEQ,KAAEgB,KAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,QAAMY,KAAE,CAAC,CAACzB,GAAE,UAASoB,EAAC,GAAE,CAACpB,GAAE,QAAOc,GAAC,GAAE,CAACd,GAAE,KAAIoB,EAAC,CAAC,GAAEM,MAAE,CAAC,CAAC1B,GAAE,UAAS,IAAIT,GAAE0B,KAAE,GAAE,IAAE,CAAC,GAAE,CAACjB,GAAE,QAAO,IAAIT,GAAE2B,KAAE,GAAE,IAAE,CAAC,GAAE,CAAClB,GAAE,KAAI,IAAIT,GAAE4B,IAAE,GAAE,IAAE,CAAC,CAAC;AAAE,SAAO,IAAIL,GAAE1B,KAAEsC,KAAED,EAAC;AAAC;AAAC,SAASE,IAAGvC,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,EAAAF,MAAEA,OAAG,IAAGC,MAAE,QAAMA,OAAGA,KAAED,GAAEF,IAAE,SAAO,CAAC;AAAE,QAAMK,MAAE,CAAC,CAAC,GAAE,GAAE,CAAC,CAAC,GAAEC,MAAE,CAAC,GAAEC,MAAE,CAAC;AAAE,WAAQC,MAAE,GAAEA,MAAEN,KAAEM,OAAI;AAAC,IAAAF,IAAE,KAAK,CAAC,GAAE,CAACE,MAAE,GAAE,EAAEA,MAAE,KAAGN,MAAE,CAAC,CAAC;AAAE,UAAMH,MAAES,MAAEN,MAAE,IAAE,KAAK;AAAG,IAAAK,IAAE,KAAK,CAAC,KAAK,IAAIR,GAAC,IAAEE,KAAE,KAAK,IAAIF,GAAC,IAAEE,GAAC,CAAC;AAAA,EAAC;AAAC,SAAOsC,IAAGxC,KAAEQ,KAAEP,KAAEK,KAAEC,KAAEH,KAAEC,GAAC;AAAC;AAAC,SAASmC,IAAGxC,KAAEM,KAAEG,KAAEE,KAAEM,IAAEE,IAAEC,KAAEf,GAAE,GAAE,GAAE,CAAC,GAAE;AAAC,QAAMgB,KAAEf,IAAE,QAAOiB,KAAEtB,IAAEQ,IAAE,SAAOY,KAAE,KAAG,IAAEV,IAAE,UAAQ,EAAE,GAAEa,KAAEvB,IAAEQ,IAAE,SAAOY,KAAE,KAAGV,MAAE,IAAE,EAAE,GAAEc,KAAE,IAAI,SAAMI,MAAE,IAAI;AAAM,MAAIC,MAAE,GAAEC,KAAE;AAAE,QAAMC,KAAE/B,IAAE,GAAEyB,MAAEzB,IAAE,GAAEgC,MAAEhC,IAAE,GAAEiC,KAAEjC,IAAE,GAAEkC,KAAElC,IAAE,GAAEmC,KAAEnC,IAAE,GAAEoC,KAAEpC,IAAE,GAAEqC,MAAErC,GAAE,GAAEwC,KAAExC,IAAE,GAAEyC,KAAEzC,IAAE,GAAE2B,KAAE3B,IAAE,GAAE0C,KAAE1C,IAAE,GAAE2C,KAAE3C,IAAE,GAAE4C,KAAElC,GAAE;AAAE,EAAAP,GAAEqC,IAAE,GAAE,GAAE,CAAC,GAAEvC,GAAEwB,KAAEjB,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,GAAEmB,GAAEF,KAAEA,GAAC,GAAEP,MAAGX,GAAE8B,KAAE7B,IAAE,CAAC,GAAEW,EAAC,GAAEQ,GAAEK,KAAEK,GAAC,KAAGlC,GAAE6B,KAAE,GAAE,GAAE,CAAC,GAAEa,IAAGpB,KAAEO,KAAEQ,IAAEA,IAAEN,IAAEF,KAAEc,GAAE,GAAE1C,GAAE6B,IAAED,GAAC,GAAE5B,GAAEsC,IAAER,EAAC;AAAE,WAAQlC,MAAE,GAAEA,MAAEU,IAAE,QAAOV,MAAI,GAAEmC,IAAED,IAAExB,IAAEV,GAAC,EAAE,CAAC,CAAC,GAAE,EAAEqC,KAAEL,KAAEtB,IAAEV,GAAC,EAAE,CAAC,CAAC,GAAEO,GAAE4B,IAAEA,IAAEE,GAAC,GAAE9B,GAAE4B,IAAEA,IAAE3B,IAAE,CAAC,CAAC,GAAEc,GAAEO,KAAG,IAAEM,GAAE,CAAC,GAAEb,GAAEO,KAAG,IAAEM,GAAE,CAAC,GAAEb,GAAEO,KAAG,IAAEM,GAAE,CAAC;AAAE,EAAAZ,GAAEO,IAAG,IAAE,CAACL,IAAE,CAAC,GAAEF,GAAEO,IAAG,IAAE,CAACL,IAAE,CAAC,GAAEF,GAAEO,IAAG,IAAE,CAACL,IAAE,CAAC;AAAE,WAAQzB,MAAE,GAAEA,MAAEgB,GAAE,QAAOhB,MAAI,CAAAwB,GAAE,KAAKR,GAAEhB,GAAC,EAAE,CAAC,IAAE,IAAEgB,GAAEhB,GAAC,EAAE,CAAC,IAAE,CAACgB,GAAEhB,GAAC,EAAE,CAAC,IAAE,IAAEU,IAAE,MAAM,GAAEc,GAAE,KAAKR,GAAEhB,GAAC,EAAE,CAAC,IAAE,IAAEgB,GAAEhB,GAAC,EAAE,CAAC,IAAE,CAACgB,GAAEhB,GAAC,EAAE,CAAC,IAAE,IAAEU,IAAE,MAAM,GAAEc,GAAE,KAAKR,GAAEhB,GAAC,EAAE,CAAC,IAAE,IAAEgB,GAAEhB,GAAC,EAAE,CAAC,IAAE,CAACgB,GAAEhB,GAAC,EAAE,CAAC,IAAE,IAAEU,IAAE,MAAM,GAAEkB,IAAE,KAAK,CAAC,GAAEA,IAAE,KAAK,CAAC,GAAEA,IAAE,KAAK,CAAC;AAAE,MAAImB,MAAErC,IAAE;AAAO,QAAMsC,KAAEtC,IAAE,SAAO;AAAE,WAAQJ,MAAE,GAAEA,MAAEE,IAAE,QAAOF,OAAI;AAAC,QAAIP,MAAE;AAAG,QAAGO,MAAE,GAAE;AAAC,MAAAF,GAAE2B,IAAEN,GAAC,GAAEnB,MAAEE,IAAE,SAAO,KAAGP,GAAEwB,KAAEjB,IAAEF,MAAE,CAAC,GAAEE,IAAEF,GAAC,CAAC,GAAEqB,GAAEF,KAAEA,GAAC,KAAG1B,MAAE,MAAGQ,GAAEkC,IAAEV,IAAEN,GAAC,GAAEE,GAAEc,IAAEA,EAAC,GAAElC,GAAEoB,IAAEnB,IAAEF,MAAE,CAAC,GAAE2B,EAAC,GAAEgB,GAAEzC,IAAEF,GAAC,GAAEmC,IAAEG,EAAC;AAAE,MAAAZ,GAAEY,IAAE,EAAEjB,IAAEI,EAAC,GAAEM,GAAC,KAAGpC,GAAEoC,KAAEA,KAAE7B,IAAEF,GAAC,CAAC,GAAEqB,GAAEK,KAAEK,GAAC,GAAE,EAAEH,IAAEO,IAAET,GAAC,GAAEL,GAAEO,IAAEA,EAAC,KAAGW,IAAGJ,IAAER,IAAES,IAAEF,IAAEN,IAAEF,KAAEc,GAAE,GAAE1C,GAAE6B,IAAED,GAAC,GAAE5B,GAAEsC,IAAER,EAAC;AAAA,IAAC;AAAC,IAAAhB,OAAIX,GAAE8B,KAAE7B,IAAEF,GAAC,GAAEa,EAAC,GAAEQ,GAAEgB,IAAEN,GAAC;AAAG,aAAQrC,MAAE,GAAEA,MAAEoB,IAAEpB,MAAI,KAAG,EAAEmC,IAAED,IAAE7B,IAAEL,GAAC,EAAE,CAAC,CAAC,GAAE,EAAEqC,KAAEL,KAAE3B,IAAEL,GAAC,EAAE,CAAC,CAAC,GAAEO,GAAE4B,IAAEA,IAAEE,GAAC,GAAEV,GAAES,IAAED,EAAC,GAAEZ,GAAEO,IAAG,IAAEM,GAAE,CAAC,GAAEb,GAAEO,IAAG,IAAEM,GAAE,CAAC,GAAEb,GAAEO,IAAG,IAAEM,GAAE,CAAC,GAAE7B,GAAE4B,IAAEA,IAAE3B,IAAEF,GAAC,CAAC,GAAEgB,GAAEO,KAAG,IAAEM,GAAE,CAAC,GAAEb,GAAEO,KAAG,IAAEM,GAAE,CAAC,GAAEb,GAAEO,KAAG,IAAEM,GAAE,CAAC,GAAE,CAACpC,KAAE;AAAC,YAAMA,OAAGC,MAAE,KAAGoB;AAAE,MAAAI,GAAE,KAAKuB,MAAE/C,GAAC,GAAEwB,GAAE,KAAKuB,MAAE3B,KAAEpB,GAAC,GAAEwB,GAAE,KAAKuB,MAAEhD,GAAC,GAAEyB,GAAE,KAAKuB,MAAEhD,GAAC,GAAEyB,GAAE,KAAKuB,MAAE3B,KAAEpB,GAAC,GAAEwB,GAAE,KAAKuB,MAAE3B,KAAErB,GAAC;AAAE,eAAQC,MAAE,GAAEA,MAAE,GAAEA,OAAI;AAAC,cAAMD,MAAEyB,GAAE,SAAO;AAAE,QAAAI,IAAE,KAAKJ,GAAEzB,MAAEC,GAAC,IAAEgD,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAD,OAAG3B;AAAA,EAAC;AAAC,QAAM8B,KAAE1C,IAAEA,IAAE,SAAO,CAAC;AAAE,WAAQR,MAAE,GAAEA,MAAEU,IAAE,QAAOV,MAAI,GAAEmC,IAAED,IAAExB,IAAEV,GAAC,EAAE,CAAC,CAAC,GAAE,EAAEqC,KAAEL,KAAEtB,IAAEV,GAAC,EAAE,CAAC,CAAC,GAAEO,GAAE4B,IAAEA,IAAEE,GAAC,GAAE9B,GAAE4B,IAAEA,IAAEe,EAAC,GAAE5B,GAAEO,KAAG,IAAEM,GAAE,CAAC,GAAEb,GAAEO,KAAG,IAAEM,GAAE,CAAC,GAAEb,GAAEO,KAAG,IAAEM,GAAE,CAAC;AAAE,QAAMgB,KAAErB,KAAE;AAAE,EAAAP,GAAEO,IAAG,IAAEL,IAAE,CAAC,GAAEF,GAAEO,IAAG,IAAEL,IAAE,CAAC,GAAEF,GAAEO,IAAG,IAAEL,IAAE,CAAC;AAAE,QAAM2B,KAAEL,MAAE3B;AAAE,WAAQpB,MAAE,GAAEA,MAAEgB,GAAE,QAAOhB,MAAI,CAAAwB,GAAE,KAAKR,GAAEhB,GAAC,EAAE,CAAC,KAAG,IAAE+C,MAAE/B,GAAEhB,GAAC,EAAE,CAAC,IAAE,CAACgB,GAAEhB,GAAC,EAAE,CAAC,IAAE,IAAEoD,EAAC,GAAE5B,GAAE,KAAKR,GAAEhB,GAAC,EAAE,CAAC,KAAG,IAAE+C,MAAE/B,GAAEhB,GAAC,EAAE,CAAC,IAAE,CAACgB,GAAEhB,GAAC,EAAE,CAAC,IAAE,IAAEoD,EAAC,GAAE5B,GAAE,KAAKR,GAAEhB,GAAC,EAAE,CAAC,KAAG,IAAE+C,MAAE/B,GAAEhB,GAAC,EAAE,CAAC,IAAE,CAACgB,GAAEhB,GAAC,EAAE,CAAC,IAAE,IAAEoD,EAAC,GAAExB,IAAE,KAAKuB,EAAC,GAAEvB,IAAE,KAAKuB,EAAC,GAAEvB,IAAE,KAAKuB,EAAC;AAAE,QAAME,KAAE,CAAC,CAAC1C,GAAE,UAASa,EAAC,GAAE,CAACb,GAAE,QAAOiB,GAAC,CAAC,GAAE0B,KAAE,CAAC,CAAC3C,GAAE,UAAS,IAAIT,GAAEoB,IAAE,GAAE,IAAE,CAAC,GAAE,CAACX,GAAE,QAAO,IAAIT,GAAEqB,IAAE,GAAE,IAAE,CAAC,CAAC;AAAE,SAAO,IAAIE,GAAE1B,KAAEuD,IAAED,EAAC;AAAC;AAAC,SAASE,IAAGxD,KAAEC,KAAEC,KAAEC,KAAE;AAAC,EAAAA,GAAEF,IAAE,SAAO,GAAE,4DAA4D,GAAEE,GAAE,MAAIF,IAAE,CAAC,EAAE,QAAO,4CAA4C,GAAEE,GAAE,QAAMD,OAAGA,IAAE,WAASD,IAAE,QAAO,gEAAgE,GAAEE,GAAE,QAAMD,OAAG,MAAIA,IAAE,CAAC,EAAE,QAAO,4CAA4C;AAAE,QAAME,MAAEH,GAAE,IAAEA,IAAE,MAAM,GAAEI,MAAE,IAAI,MAAM,KAAGJ,IAAE,SAAO,EAAE;AAAE,MAAIK,MAAE,GAAEC,MAAE;AAAE,WAAQe,MAAE,GAAEA,MAAErB,IAAE,QAAOqB,OAAI;AAAC,aAAQtB,MAAE,GAAEA,MAAE,GAAEA,MAAI,CAAAI,IAAEE,KAAG,IAAEL,IAAEqB,GAAC,EAAEtB,GAAC;AAAE,IAAAsB,MAAE,MAAIjB,IAAEE,KAAG,IAAEe,MAAE,GAAEjB,IAAEE,KAAG,IAAEe;AAAA,EAAE;AAAC,QAAMd,MAAE,CAAC,GAAEC,MAAE,CAAC;AAAE,MAAGD,IAAE,KAAK,CAACI,GAAE,UAASP,GAAC,CAAC,GAAEI,IAAE,KAAK,CAACG,GAAE,UAAS,IAAIT,GAAEC,KAAE,GAAE,IAAE,CAAC,CAAC,GAAEF,KAAE;AAAC,UAAMF,MAAEC,IAAE,IAAEC,IAAE,MAAM;AAAE,QAAIC,MAAE;AAAE,aAAQC,MAAE,GAAEA,MAAEH,IAAE,QAAOG,MAAI,UAAQH,MAAE,GAAEA,MAAE,GAAEA,MAAI,CAAAD,IAAEG,KAAG,IAAED,IAAEE,GAAC,EAAEH,GAAC;AAAE,IAAAO,IAAE,KAAK,CAACI,GAAE,QAAOP,GAAC,CAAC,GAAEI,IAAE,KAAK,CAACG,GAAE,QAAO,IAAIT,GAAEH,KAAE,GAAE,IAAE,CAAC,CAAC;AAAA,EAAC;AAAC,SAAOG,QAAIM,IAAE,KAAK,CAACG,GAAE,OAAM,IAAIT,GAAEA,KAAE,CAAC,CAAC,CAAC,GAAEK,IAAE,KAAK,CAACI,GAAE,OAAMR,GAAED,IAAE,SAAO,CAAC,CAAC,CAAC,IAAG,IAAIuB,GAAE1B,KAAES,KAAED,KAAE,MAAKN,GAAE,IAAI;AAAC;AAAC,SAASuD,IAAGzD,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,MAAE,GAAE;AAAC,QAAMC,MAAE,IAAI,MAAM,EAAE,GAAEC,MAAE,CAAC,CAAC,CAACL,KAAEG,KAAED,MAAE,CAAC,GAAE,CAACD,KAAEE,KAAED,MAAE,CAAC,GAAE,CAAC,GAAEH,MAAEI,KAAED,MAAE,CAAC,GAAE,CAAC,CAACF,KAAEG,KAAE,CAACD,MAAE,CAAC,GAAE,CAACD,KAAEE,KAAE,CAACD,MAAE,CAAC,GAAE,CAAC,GAAEH,MAAEI,KAAE,CAACD,MAAE,CAAC,CAAC,GAAEI,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,WAAQC,MAAE,GAAEA,MAAE,GAAEA,MAAI,CAAAH,IAAE,IAAEG,GAAC,IAAEF,IAAEE,GAAC,EAAE,CAAC,GAAEH,IAAE,IAAEG,MAAE,CAAC,IAAEF,IAAEE,GAAC,EAAE,CAAC,GAAEH,IAAE,IAAEG,MAAE,CAAC,IAAEF,IAAEE,GAAC,EAAE,CAAC;AAAE,SAAO,IAAIiB,GAAE1B,KAAE,CAAC,CAACY,GAAE,UAAS,IAAIT,GAAEG,KAAE,GAAE,IAAE,CAAC,CAAC,GAAE,CAAC,CAACM,GAAE,UAASJ,GAAC,CAAC,CAAC;AAAC;AAAC,SAAS,GAAGR,KAAEC,KAAE;AAAC,QAAMC,MAAEF,IAAE,oBAAoBY,GAAE,QAAQ,EAAE;AAAK,WAAQT,MAAE,GAAEA,MAAED,IAAE,QAAOC,OAAG,GAAE;AAAC,UAAMH,MAAEE,IAAEC,GAAC,GAAEC,MAAEF,IAAEC,MAAE,CAAC,GAAEE,MAAEH,IAAEC,MAAE,CAAC;AAAE,IAAAC,GAAE,IAAGJ,KAAEI,KAAEC,GAAC,GAAEO,GAAE,IAAG,IAAGX,GAAC,GAAEC,IAAEC,GAAC,IAAE,GAAG,CAAC,GAAED,IAAEC,MAAE,CAAC,IAAE,GAAG,CAAC,GAAED,IAAEC,MAAE,CAAC,IAAE,GAAG,CAAC;AAAA,EAAC;AAAC;AAAC,SAASuD,IAAG1D,KAAEC,MAAED,KAAE;AAAC,QAAME,MAAEF,IAAE,kBAAiBG,MAAED,IAAE,IAAIU,GAAE,QAAQ,EAAE,MAAKR,MAAEF,IAAE,IAAIU,GAAE,MAAM,EAAE;AAAK,MAAGR,KAAE;AAAC,UAAMJ,MAAEC,IAAE,oBAAoBW,GAAE,MAAM,EAAE;AAAK,aAAQX,MAAE,GAAEA,MAAEG,IAAE,QAAOH,OAAG,GAAE;AAAC,YAAMC,MAAEE,IAAEH,MAAE,CAAC;AAAE,MAAAD,IAAEC,MAAE,CAAC,IAAE,CAACG,IAAEH,MAAE,CAAC,GAAED,IAAEC,MAAE,CAAC,IAAEC;AAAA,IAAC;AAAA,EAAC;AAAC,MAAGC,KAAE;AAAC,UAAMH,MAAEC,IAAE,oBAAoBW,GAAE,QAAQ,EAAE;AAAK,aAAQX,MAAE,GAAEA,MAAEE,IAAE,QAAOF,OAAG,GAAE;AAAC,YAAMC,MAAEC,IAAEF,MAAE,CAAC;AAAE,MAAAD,IAAEC,MAAE,CAAC,IAAE,CAACE,IAAEF,MAAE,CAAC,GAAED,IAAEC,MAAE,CAAC,IAAEC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGF,KAAEC,KAAEG,KAAEC,KAAEE,KAAE;AAAC,SAAM,EAAE,KAAK,IAAI,EAAEN,KAAED,GAAC,CAAC,IAAEO,SAAK,EAAEH,KAAEJ,KAAEC,GAAC,GAAE2B,GAAExB,KAAEA,GAAC,GAAE,EAAEC,KAAED,KAAEJ,GAAC,GAAE4B,GAAEvB,KAAEA,GAAC,GAAE;AAAG;AAAC,SAASyC,IAAG9C,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,SAAO,GAAGN,KAAEC,KAAEG,KAAEC,KAAEC,GAAC,KAAG,GAAGN,KAAEE,KAAEE,KAAEC,KAAEC,GAAC,KAAG,GAAGN,KAAEG,KAAEC,KAAEC,KAAEC,GAAC;AAAC;AAAC,IAAMyC,MAAG;AAAT,IAAsB,KAAG9C,IAAE;;;ACA7oX,IAAM0D,MAAN,MAAO;AAAA,EAAC,YAAYC,KAAEC,MAAE,CAAC,GAAE;AAAC,SAAK,WAASD,KAAE,KAAK,iBAAeE,GAAE,GAAE,KAAK,qBAAmB,GAAE,KAAK,kBAAgBC,GAAE,GAAE,KAAK,6BAA2B,MAAG,KAAK,KAAGA,GAAE,GAAE,KAAK,WAASF,IAAE,UAAS,KAAK,aAAWA,IAAE,YAAW,KAAK,eAAaA,IAAE,cAAa,KAAK,oBAAkBA,IAAE,mBAAkB,KAAK,aAAW,CAAC,CAACA,IAAE,cAAYA,IAAE;AAAA,EAAU;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK;AAAA,EAAe;AAAA,EAAC,qBAAqBD,KAAE;AAAC,IAAAA,IAAE,KAAK,eAAe,GAAE,KAAK,6BAA2B,MAAG,KAAK,sBAAsB,KAAK,iBAAgB,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAC,SAAK,6BAA2B;AAAA,EAAE;AAAA,EAAC,sBAAsBC,KAAEG,KAAEC,MAAEC,IAAEL,GAAC,GAAE;AAAC,MAAE,KAAK,YAAY,MAAIM,GAAEH,KAAE,KAAK,aAAa,QAAOH,GAAC,GAAEG,IAAE,CAAC,IAAE,KAAK,aAAa,SAAOC;AAAA,EAAE;AAAA,EAAC,IAAI,0BAAyB;AAAC,WAAO,EAAE,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,SAAS;AAAA,EAAQ;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,SAAS;AAAA,EAAI;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,EAAE,KAAK,iBAAiB,IAAE,KAAK,kBAAgB,KAAK,+BAA6B,KAAK,0BAAwB,KAAK,wBAAsBF,GAAE,IAAGD,GAAE,KAAK,uBAAsB,KAAK,kBAAkB,KAAK,cAAc,CAAC,GAAE,KAAK,6BAA2B,QAAI,KAAK;AAAA,EAAsB;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,SAAS;AAAA,EAAO;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,SAAS;AAAA,EAAgB;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,SAAS;AAAA,EAAU;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,SAAS;AAAA,EAAS;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,SAAS;AAAA,EAAO;AAAA,EAAC,IAAI,QAAQF,KAAE;AAAC,SAAK,SAAS,UAAQA;AAAA,EAAC;AAAC;;;ACA93B,IAAMQ,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,wBAAwBA,KAAEC,KAAE;AAAC,IAAAA,IAAE,mBAAiBD,IAAE,KAAK,SAAOE,IAAE;AAAA,EAAM;AAAA,EAAC,kBAAkBF,KAAE;AAAC,WAAO,IAAIG,IAAEH,IAAE,MAAK,GAAE,OAAO,IAAI,EAAE,MAAM,KAAK,aAAa,GAAEI,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBH,KAAEC,KAAE;AAAC,UAAMG,MAAE,KAAK,eAAcC,MAAEL,QAAIE,IAAE,MAAKI,MAAEN,QAAIE,IAAE;AAAU,WAAO,EAAE,EAAC,UAASE,IAAE,WAASG,GAAE,SAAOH,IAAE,WAASG,GAAE,SAAO,CAACH,IAAE,cAAY,OAAKC,MAAEG,KAAEC,GAAET,GAAC,GAAE,SAAQO,GAAEH,IAAE,QAAQ,GAAE,WAAU,EAAC,MAAKM,IAAEV,GAAC,EAAC,GAAE,aAAYK,OAAGC,QAAIF,IAAE,aAAWC,MAAE,MAAK,YAAWM,IAAE,cAAaP,IAAE,eAAaL,MAAE,MAAK,aAAYK,IAAE,eAAaH,MAAEC,MAAEI,KAAE,MAAK,eAAcD,OAAGC,MAAEF,IAAE,gBAAcQ,KAAE,OAAKP,IAAED,IAAE,YAAY,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,KAAK,yBAAuB,KAAK,gBAAgB,KAAK,cAAc,sBAAqB,IAAE,GAAE,KAAK,gBAAgB,KAAK,cAAc,sBAAqB,KAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBL,KAAEC,KAAE;AAAC,WAAOA,MAAE,KAAK,yBAAuB,MAAM,iBAAiBD,KAAEC,GAAC;AAAA,EAAC;AAAC;AAACF,GAAE,SAAO,IAAIE,GAAEM,IAAG,MAAI,OAAO,kCAAyB,CAAE;AAAE,IAAMM,KAAE,EAAC,QAAO,GAAE,OAAM,EAAC;;;ACAj6C,IAAMC,MAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAOC,GAAE,OAAM,KAAK,WAASC,IAAE,MAAK,KAAK,gBAAc,OAAG,KAAK,kBAAgB,OAAG,KAAK,cAAY,OAAG,KAAK,gBAAc,OAAG,KAAK,eAAa,MAAG,KAAK,aAAW,MAAG,KAAK,eAAa,OAAG,KAAK,uBAAqBC,IAAE,MAAK,KAAK,sBAAoB,OAAG,KAAK,kBAAgB,OAAG,KAAK,iCAA+B;AAAA,EAAE;AAAC;AAAC,EAAE,CAACC,IAAE,EAAC,OAAMH,GAAE,MAAK,CAAC,CAAC,GAAEF,IAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACK,IAAE,EAAC,OAAMF,IAAE,MAAK,CAAC,CAAC,GAAEH,IAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACK,IAAE,EAAC,OAAMD,IAAE,MAAK,CAAC,CAAC,GAAEJ,IAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,kCAAiC,MAAM;;;ACAxkB,IAAMM,MAAN,cAAgBC,IAAC;AAAA,EAAC,YAAYA,KAAE;AAAC,UAAMA,KAAE,IAAIC,IAAC,GAAE,KAAK,gBAAc,MAAG,KAAK,iBAAe,IAAIC;AAAA,EAAC;AAAA,EAAC,iBAAiBF,KAAEG,KAAE;AAAC,WAAO,KAAK,eAAe,SAAOH,KAAE,KAAK,eAAe,WAAS,KAAK,WAAW,UAAS,KAAK,eAAe,kBAAgB,KAAK,WAAW,iBAAgB,KAAK,eAAe,gBAAc,KAAK,WAAW,eAAc,KAAK,eAAe,cAAY,KAAK,WAAW,aAAY,KAAK,eAAe,gBAAc,KAAK,WAAW,eAAc,KAAK,eAAe,aAAW,KAAK,WAAW,YAAW,KAAK,eAAe,eAAa,KAAK,WAAW,cAAa,KAAK,eAAe,uBAAqBG,IAAE,sBAAqB,KAAK,eAAe,eAAaA,IAAE,OAAO,oBAAkBC,IAAE,KAAK,eAAe,sBAAoBD,IAAE,iBAAiB,SAAQ,KAAK,eAAe,kBAAgBA,IAAE,iBAAiB,iBAAgB,KAAK;AAAA,EAAc;AAAA,EAAC,aAAaH,KAAEK,KAAE;AAAC,QAAGA,QAAIC,GAAE,SAAOD,QAAIC,GAAE,SAAOD,QAAIC,GAAE,aAAWD,QAAIC,GAAE,SAAO,KAAK,WAAW,oBAAkBD,QAAIC,GAAE,uBAAsB;AAAC,UAAGN,QAAIO,GAAE,gBAAgB,QAAM;AAAG,UAAGF,QAAIC,GAAE,UAAU,QAAON,QAAIO,GAAE;AAAgB,aAAOP,SAAK,KAAK,WAAW,cAAY,KAAK,WAAW,aAAWO,GAAE,uBAAqBA,GAAE,4CAA0CA,GAAE;AAAA,IAAgB;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,iBAAiBP,KAAE;AAAC,WAAO,IAAIQ,GAAER,GAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,IAAIK,IAAE,IAAI,2CAA2C,IAAEA,MAAEI,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMD,KAAN,cAAgBL,GAAC;AAAA,EAAC,qBAAqBH,KAAE;AAAC,IAAAA,IAAE,iBAAe,KAAK,UAAU,WAAW,gBAAc,KAAK,UAAU,cAAc,EAAC,cAAaA,IAAE,aAAY,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,WAAO,KAAK,YAAUM,GAAE,SAAO,KAAK,YAAUA,GAAE,SAAO,KAAK,qBAAqBN,GAAC,GAAE,KAAK,gBAAgBU,IAAEV,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,cAAgBU,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAMC,IAAE,KAAK,cAAY,OAAG,KAAK,aAAW,MAAG,KAAK,mBAAiB,OAAG,KAAK,kBAAgB,OAAG,KAAK,gBAAc,OAAG,KAAK,gBAAc,OAAG,KAAK,WAASC,IAAE,MAAK,KAAK,eAAa;AAAA,EAAE;AAAC;;;ACAtnF,IAAMC,MAAE,EAAC,MAAK,CAAC,GAAE,CAAC,GAAE,KAAI,CAAC,GAAE,CAAC,GAAE,aAAY,CAAC,GAAE,CAAC,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,aAAY,CAAC,GAAE,CAAC,EAAC;AAApF,IAAsFC,MAAE,EAAC,MAAKD,IAAE,MAAK,YAAW,CAAC,GAAGA,IAAE,MAAK,GAAGA,IAAE,GAAG,GAAE,KAAIA,IAAE,KAAI,aAAYA,IAAE,WAAW,GAAE,iBAAgB,CAAC,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,GAAG,GAAE,qBAAoB,CAAC,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,KAAI,GAAGA,IAAE,GAAG,GAAE,MAAK,MAAK,cAAaA,IAAE,YAAY,GAAE,kBAAiB,CAAC,GAAGA,IAAE,YAAY,GAAE,GAAGA,IAAE,WAAW,CAAC,GAAE,sBAAqB,CAAC,GAAGA,IAAE,YAAY,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,WAAW,CAAC,GAAE,aAAYA,IAAE,WAAW,GAAE,OAAM,KAAI;AAAne,IAAqeE,MAAE;AAAE,SAASC,IAAEC,KAAEJ,MAAE,GAAE;AAAC,SAAO,EAAEI,GAAC,IAAEA,MAAE,EAAC,SAAQA,IAAE,MAAM,GAAE,YAAWJ,IAAC;AAAC;AAAC,SAASK,IAAEC,KAAEF,MAAE,GAAE;AAAC,SAAM,EAAC,SAAQ,CAACE,KAAEA,GAAC,GAAE,YAAWF,IAAC;AAAC;AAAC,SAASG,IAAED,KAAE;AAAC,SAAO,EAAEA,GAAC,KAAG,YAAUA,IAAE,OAAKE,IAAEF,IAAE,KAAK,IAAE;AAAI;AAAC,SAASE,IAAEF,KAAE;AAAC,SAAO,EAAEA,GAAC,IAAEH,IAAEF,IAAEK,GAAC,GAAEJ,GAAC,IAAE;AAAI;;;ACA2I,SAASO,GAAEC,KAAEC,KAAEC,MAAE,MAAK;AAAC,QAAMC,MAAE,CAAC,GAAEC,MAAE,CAAC,GAAEC,MAAEJ,IAAE;AAAa,EAAAK,GAAEL,KAAEG,KAAED,GAAC;AAAE,QAAMI,MAAEH,IAAE,CAAC,EAAE,CAAC,EAAE,MAAKI,MAAEL,IAAE,CAAC,EAAE,CAAC,EAAE,QAAOM,MAAE,IAAI,MAAMD,GAAC,EAAE,KAAK,CAAC;AAAE,SAAOE,IAAET,KAAEG,KAAED,KAAEM,GAAC,GAAEE,GAAEV,KAAEG,KAAED,KAAEM,GAAC,GAAEG,GAAEX,KAAEG,KAAED,KAAEM,GAAC,GAAEI,GAAEZ,KAAEG,KAAED,KAAEM,GAAC,GAAEK,GAAEb,KAAEG,KAAED,KAAEM,GAAC,GAAEM,GAAEd,KAAEG,KAAED,KAAEM,GAAC,GAAEO,IAAEf,KAAEG,KAAED,KAAEI,GAAC,GAAE,IAAIU,GAAEjB,KAAEI,KAAED,KAAEE,KAAEJ,GAAE,MAAKC,GAAC;AAAC;AAAC,SAASI,GAAEN,KAAEC,KAAEC,KAAE;AAAC,QAAK,EAAC,eAAc,EAAC,UAASC,IAAC,GAAE,yBAAwBC,IAAC,IAAEJ,KAAEK,MAAEa,GAAEf,GAAC,KAAGC,KAAEG,MAAEJ,IAAE,SAAO,KAAGE,MAAE,IAAE,IAAGG,MAAE,IAAI,MAAM,KAAGD,MAAE,EAAE,GAAEE,MAAEJ,MAAEF,IAAE,MAAM,GAAEA,IAAE,SAAO,CAAC,IAAEA;AAAE,MAAIgB,MAAE;AAAE,WAAQC,MAAE,GAAEA,MAAEb,MAAE,GAAEa,MAAI,CAAAZ,IAAEW,KAAG,IAAEC,KAAEZ,IAAEW,KAAG,IAAEC,MAAE;AAAE,EAAAnB,IAAE,KAAK,CAACU,GAAE,UAAS,IAAIH,GAAEC,KAAE,GAAEJ,GAAC,CAAC,CAAC,GAAEH,IAAE,KAAK,CAACS,GAAE,UAASH,GAAC,CAAC;AAAC;AAAC,SAASE,IAAER,KAAEC,KAAEC,KAAEC,KAAE;AAAC,MAAG,EAAEH,IAAE,cAAc,YAAY,EAAE;AAAO,QAAMM,MAAEN,IAAE,cAAc;AAAM,EAAAC,IAAE,KAAK,CAACQ,GAAE,OAAM,IAAIH,GAAE,EAAEA,KAAEa,EAAC,GAAE,CAAC,CAAC,CAAC,GAAEjB,IAAE,KAAK,CAACO,GAAE,OAAMN,GAAC,CAAC;AAAC;AAAC,SAASO,GAAEX,KAAEC,KAAEC,KAAEC,KAAE;AAAC,MAAG,CAAC,EAAEH,IAAE,cAAc,MAAM,EAAE;AAAO,QAAMI,MAAEJ,IAAE,cAAc;AAAO,EAAAC,IAAE,KAAK,CAACS,GAAE,QAAO,IAAIH,GAAEH,KAAE,CAAC,CAAC,CAAC,GAAEF,IAAE,KAAK,CAACQ,GAAE,QAAOP,GAAC,CAAC;AAAC;AAAC,SAASS,GAAEb,KAAEC,KAAEE,KAAEC,KAAE;AAAC,QAAMC,MAAEL,IAAE,cAAc;AAAa,IAAEK,GAAC,MAAIJ,IAAE,KAAK,CAACU,GAAE,uBAAsB,IAAIH,GAAE,CAACH,GAAC,GAAE,GAAE,IAAE,CAAC,CAAC,GAAEF,IAAE,KAAK,CAACQ,GAAE,OAAMP,GAAC,CAAC;AAAE;AAAC,SAASO,GAAET,KAAEC,KAAEC,KAAEC,KAAE;AAAC,MAAG,EAAEH,IAAE,cAAc,WAAW,EAAE;AAAO,QAAMK,MAAEL,IAAE,cAAc;AAAK,EAAAC,IAAE,KAAK,CAACQ,GAAE,MAAK,IAAIH,GAAE,CAAC,EAAED,KAAE,CAAC,CAAC,GAAE,GAAE,IAAE,CAAC,CAAC,GAAEH,IAAE,KAAK,CAACO,GAAE,MAAKN,GAAC,CAAC;AAAC;AAAC,SAASS,GAAEd,KAAEC,KAAEE,KAAEC,KAAE;AAAC,QAAMC,MAAEL,IAAE,cAAc;AAAY,IAAEK,GAAC,MAAIJ,IAAE,KAAK,CAACU,GAAE,sBAAqB,IAAIH,GAAE,CAACH,GAAC,GAAE,GAAE,IAAE,CAAC,CAAC,GAAEF,IAAE,KAAK,CAACQ,GAAE,sBAAqBP,GAAC,CAAC;AAAE;AAAC,SAASW,GAAEf,KAAEC,KAAEE,KAAEC,KAAE;AAAC,QAAMC,MAAEL,IAAE,cAAc;AAAe,IAAEK,GAAC,MAAIJ,IAAE,KAAK,CAACU,GAAE,yBAAwB,IAAIH,GAAE,CAACH,GAAC,GAAE,GAAE,IAAE,CAAC,CAAC,GAAEF,IAAE,KAAK,CAACQ,GAAE,yBAAwBP,GAAC,CAAC;AAAE;AAAC,SAASY,IAAEhB,KAAEC,KAAEI,KAAEE,KAAE;AAAC,MAAG,EAAEP,IAAE,WAAW,KAAGA,IAAE,YAAY,mBAAmB,gBAAcmB,IAAE,UAAQ,CAACnB,IAAE,YAAY,iBAAiB,aAAa;AAAO,QAAMsB,MAAEpB,GAAEK,IAAE,MAAM,GAAEgB,MAAE,EAAEvB,IAAE,YAAY,gBAAgB;AAAE,WAAQE,MAAE,GAAEA,MAAEoB,IAAE,QAAOpB,OAAG,EAAE,IAAEK,KAAEL,KAAEoB,KAAEpB,KAAEqB,GAAC;AAAE,QAAMxB,KAAEQ,IAAE,SAAO,GAAED,KAAEJ,IAAEH,KAAE,CAAC;AAAE,MAAIW,MAAEc,IAAEZ,MAAEa,IAAEZ,KAAE,GAAEF,MAAE;AAAE,EAAAR,GAAEO,KAAEY,IAAEX,KAAG,GAAEW,IAAEX,KAAG,GAAEW,IAAEX,KAAG,CAAC,GAAEL,GAAE,CAAC,IAAE;AAAE,WAAQJ,MAAE,GAAEA,MAAEH,KAAE,GAAE,EAAEG,IAAE,CAAAA,QAAIH,OAAIY,MAAE,IAAGR,GAAES,KAAEU,IAAEX,KAAG,GAAEW,IAAEX,KAAG,GAAEW,IAAEX,KAAG,CAAC,GAAEE,MAAGa,GAAEhB,KAAEE,GAAC,GAAEN,GAAEJ,GAAC,IAAEW,IAAE,CAACH,KAAEE,GAAC,IAAE,CAACA,KAAEF,GAAC;AAAE,EAAAT,IAAE,KAAK,CAACU,GAAE,iBAAgB,IAAIH,GAAEF,IAAE,GAAE,IAAE,CAAC,CAAC,GAAED,IAAE,KAAK,CAACM,GAAE,iBAAgBN,IAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAAC;AAAC,SAASa,GAAElB,KAAE;AAAC,QAAMC,MAAED,IAAE;AAAO,SAAOA,IAAE,CAAC,MAAIA,IAAEC,MAAE,CAAC,KAAGD,IAAE,CAAC,MAAIA,IAAEC,MAAE,CAAC,KAAGD,IAAE,CAAC,MAAIA,IAAEC,MAAE,CAAC;AAAC;AAAC,IAAMuB,KAAEtB,GAAE;AAAV,IAAYuB,KAAEvB,GAAE;;;ACAn1E,SAASyB,IAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,QAAMJ,MAAE,cAAYC,IAAE,OAAKI,GAAE,cAAYA,GAAE,MAAKC,MAAE,cAAYL,IAAE,OAAKA,IAAE,QAAMA,IAAE,OAAM,EAAC,UAASM,IAAE,UAASC,IAAC,IAAEJ,IAAEE,KAAE,CAAC,CAACL,IAAE,MAAKD,GAAC,GAAES,KAAEC,GAAEH,GAAE,MAAM,GAAEF,MAAEC,GAAEC,IAAEN,IAAE,kBAAiB,GAAEQ,IAAE,GAAEF,IAAE,GAAEA,GAAE,SAAO,GAAEL,KAAEC,KAAEC,GAAC,GAAEO,KAAE,QAAMN;AAAE,SAAM,EAAC,OAAMM,KAAEC,IAAEJ,KAAED,IAAEE,EAAC,IAAE,CAAC,GAAE,mBAAkBE,IAAE,kBAAiBN,IAAC;AAAC;AAAC,SAASC,IAAEL,KAAEY,KAAE;AAAC,QAAMC,MAAE,cAAYb,IAAE,OAAKI,GAAE,cAAYA,GAAE,MAAKD,MAAE,cAAYH,IAAE,OAAKA,IAAE,QAAMA,IAAE,OAAM,EAAC,UAASD,KAAE,UAASM,IAAC,IAAEF,IAAEA,KAAE,OAAGU,GAAC,GAAEP,KAAE,GAAEP,KAAEC,IAAE,kBAAiB,GAAED,KAAEa,KAAE,GAAEb,IAAE,SAAO,CAAC;AAAE,WAAQE,MAAE,GAAEA,MAAEF,IAAE,QAAOE,OAAG,EAAE,CAAAF,IAAEE,GAAC,IAAEa;AAAE,SAAM,EAAC,OAAMR,KAAEK,IAAEN,KAAEN,GAAC,IAAE,CAAC,GAAE,mBAAkBO,GAAC;AAAC;AAAC,SAASK,IAAEV,KAAEQ,KAAEM,MAAE,MAAK;AAAC,QAAMH,MAAE,IAAI;AAAM,aAAS,EAAC,OAAMV,KAAE,OAAMW,IAAC,KAAIZ,KAAE;AAAC,QAAGY,OAAG,EAAE;AAAS,UAAMZ,MAAE,IAAEC,KAAEH,MAAE,IAAEc;AAAE,IAAAD,IAAE,KAAK,EAAC,UAASD,GAAEF,KAAE,IAAEP,KAAE,IAAEW,GAAC,GAAE,cAAa,EAAEE,GAAC,IAAEJ,GAAEI,KAAEd,KAAEF,GAAC,IAAE,OAAM,CAAC;AAAA,EAAC;AAAC,SAAOa;AAAC;;;ACA9X,IAAMI,MAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK,cAAc,kBAAgB,KAAK,cAAc,WAASC,GAAE;AAAA,EAAS;AAAA,EAAC,wBAAwBD,KAAEE,KAAE;AAAC,IAAAA,IAAE,mBAAiBF,IAAE,KAAK,SAAOG,IAAE;AAAA,EAAM;AAAA,EAAC,kBAAkBH,KAAE;AAAC,WAAO,IAAII,IAAEJ,IAAE,MAAK,GAAE,OAAO,IAAI,EAAE,MAAM,KAAK,aAAa,GAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMH,MAAE,KAAK,eAAcI,MAAEC,IAAEC,GAAE,WAAUA,GAAE,KAAIA,GAAE,qBAAoBA,GAAE,mBAAmB,GAAEL,MAAE,CAACH,KAAEM,MAAE,MAAKH,MAAE,SAAO,EAAE,EAAC,UAASG,KAAE,WAAUA,IAAE,YAAWH,KAAE,YAAWM,IAAE,cAAaP,IAAE,eAAaF,MAAE,MAAK,aAAYE,IAAE,eAAaF,MAAEI,MAAEL,KAAE,KAAI,CAAC;AAAE,WAAOG,IAAE,WAASD,GAAE,SAAO,KAAK,yBAAuBE,IAAE,MAAGD,IAAE,eAAa,KAAK,kBAAgBI,MAAE,MAAKI,GAAC,GAAEP,IAAE,OAAGD,IAAE,eAAa,KAAK,kBAAgBI,MAAE,MAAKI,GAAC,KAAGP,IAAE,KAAE;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAOE,GAAE;AAAA,EAAK;AAAA,EAAC,iBAAiBL,KAAEE,KAAE;AAAC,WAAOA,MAAE,KAAK,yBAAuB,MAAM,iBAAiBF,KAAEE,GAAC;AAAA,EAAC;AAAC;AAACH,IAAE,SAAO,IAAIG,GAAES,IAAG,MAAI,OAAO,+BAAsB,CAAE;;;ACA74C,IAAMC,MAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAOC,GAAE,OAAM,KAAK,gBAAc,OAAG,KAAK,kBAAgB,OAAG,KAAK,cAAY,OAAG,KAAK,SAAO,OAAG,KAAK,iBAAe,OAAG,KAAK,yBAAuB,OAAG,KAAK,0BAAwB,MAAG,KAAK,eAAa;AAAA,EAAE;AAAC;AAAC,EAAE,CAACF,IAAE,EAAC,OAAME,GAAE,MAAK,CAAC,CAAC,GAAEF,IAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAACA,IAAE,CAAC,GAAEA,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACA,IAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAEA,IAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACA,IAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAEA,IAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAACA,IAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAEA,IAAE,WAAU,iCAAgC,MAAM;;;ACAub,IAAIG;AAAE,CAAC,SAASC,KAAE;AAAC,EAAAA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,MAAI,CAAC,IAAE;AAAK,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAME,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYF,KAAE;AAAC,UAAMA,KAAE,IAAIG,IAAC,GAAE,KAAK,iBAAe,IAAIC;AAAA,EAAC;AAAA,EAAC,iBAAiBJ,KAAEK,KAAE;AAAC,SAAK,eAAe,SAAOL,KAAE,KAAK,eAAe,gBAAc,KAAK,WAAW,eAAc,KAAK,eAAe,kBAAgB,KAAK,WAAW,iBAAgB,KAAK,eAAe,cAAY,KAAK,WAAW,MAAM,CAAC,IAAE,KAAG,KAAK,WAAW,QAAM,GAAE,KAAK,eAAe,SAAOK,IAAE,SAAOC,GAAE;AAAgB,UAAMC,MAAE,EAAE,KAAK,WAAW,cAAc;AAAE,WAAO,KAAK,eAAe,iBAAeA,KAAE,KAAK,eAAe,yBAAuBA,OAAG,EAAE,KAAK,WAAW,eAAe,GAAE,KAAK,eAAe,eAAa,KAAK,WAAW,cAAa,KAAK,eAAe,0BAAwB,KAAK,WAAW,yBAAwB,KAAK;AAAA,EAAc;AAAA,EAAC,UAAUF,KAAED,KAAEG,KAAEC,KAAEN,KAAEO,KAAE;AAAC,QAAG,CAACF,IAAE,QAAQ,iBAAe,CAACF,IAAE,QAAQ;AAAO,QAAG,CAACK,IAAEN,GAAC,EAAE,QAAO,KAAK,EAAE,UAAU,yDAAyD,EAAE,MAAM,gDAAgD;AAAE,UAAMO,KAAEN,IAAE,iBAAiB,IAAIO,GAAE,QAAQ,EAAE,MAAKC,MAAEN,IAAE,QAAOO,KAAEC;AAAG,IAAAC,GAAEF,IAAEP,IAAE,KAAK;AAAE,UAAMU,KAAE;AAAE,IAAAC,GAAEC,IAAG,CAAC,GAAEL,GAAE,CAAC,IAAEG,IAAEH,GAAE,CAAC,IAAEG,IAAE,CAAC,GAAEC,GAAEC,IAAG,CAAC,GAAEL,GAAE,CAAC,IAAEG,IAAEH,GAAE,CAAC,IAAEG,IAAE,CAAC,GAAEC,GAAEC,IAAG,CAAC,GAAEL,GAAE,CAAC,IAAEG,IAAEH,GAAE,CAAC,IAAEG,IAAE,CAAC,GAAEC,GAAEC,IAAG,CAAC,GAAEL,GAAE,CAAC,IAAEG,IAAEH,GAAE,CAAC,IAAEG,IAAE,CAAC;AAAE,aAAQjB,MAAE,GAAEA,MAAE,GAAEA,MAAI,KAAG,CAACa,IAAE,0BAA0BM,IAAGnB,GAAC,GAAEoB,IAAGpB,GAAC,CAAC,EAAE;AAAO,IAAAqB,GAAER,IAAE,KAAIO,IAAG,CAAC,GAAEA,IAAG,CAAC,GAAEE,GAAE,GAAED,GAAER,IAAE,KAAIO,IAAG,CAAC,GAAEA,IAAG,CAAC,GAAEG,GAAE,GAAEF,GAAER,IAAE,KAAIO,IAAG,CAAC,GAAEA,IAAG,CAAC,GAAEI,GAAE,GAAEH,GAAER,IAAE,KAAIO,IAAG,CAAC,GAAEA,IAAG,CAAC,GAAEK,GAAE;AAAE,QAAIC,MAAE,OAAO,WAAUC,KAAE;AAAE,aAAQ3B,MAAE,GAAEA,MAAEW,GAAE,SAAO,GAAEX,OAAG,GAAE;AAAC,UAAG4B,IAAE,CAAC,IAAEjB,GAAEX,GAAC,IAAEI,IAAE,EAAE,GAAEwB,IAAE,CAAC,IAAEjB,GAAEX,MAAE,CAAC,IAAEI,IAAE,EAAE,GAAEwB,IAAE,CAAC,IAAEjB,GAAEX,MAAE,CAAC,IAAEI,IAAE,EAAE,GAAEyB,GAAE,CAAC,IAAElB,GAAEX,MAAE,CAAC,IAAEI,IAAE,EAAE,GAAEyB,GAAE,CAAC,IAAElB,GAAEX,MAAE,CAAC,IAAEI,IAAE,EAAE,GAAEyB,GAAE,CAAC,IAAElB,GAAEX,MAAE,CAAC,IAAEI,IAAE,EAAE,GAAE0B,GAAER,KAAGM,GAAC,IAAE,KAAGE,GAAER,KAAGO,EAAC,IAAE,KAAGC,GAAEP,KAAGK,GAAC,IAAE,KAAGE,GAAEP,KAAGM,EAAC,IAAE,KAAGC,GAAEN,KAAGI,GAAC,IAAE,KAAGE,GAAEN,KAAGK,EAAC,IAAE,KAAGC,GAAEL,KAAGG,GAAC,IAAE,KAAGE,GAAEL,KAAGI,EAAC,IAAE,EAAE;AAAS,UAAGhB,IAAE,sBAAsBe,KAAEG,EAAC,GAAElB,IAAE,sBAAsBgB,IAAEG,EAAC,GAAED,GAAE,CAAC,IAAE,KAAGC,GAAE,CAAC,IAAE,GAAE;AAAC,QAAAhC,GAAEiC,IAAEL,KAAEC,EAAC;AAAE,cAAM7B,MAAEa,IAAE,SAAQR,MAAE,CAACyB,GAAE9B,IAAE,EAAE,IAAI,GAAE4B,GAAC,IAAE,EAAEK,IAAEC,GAAElC,IAAE,EAAE,IAAI,CAAC,CAAC;AAAE,UAAEiC,IAAEA,IAAE5B,GAAC,GAAE8B,GAAEP,KAAEA,KAAEK,EAAC,GAAEpB,IAAE,sBAAsBe,KAAEG,EAAC;AAAA,MAAC,WAASA,GAAE,CAAC,IAAE,KAAGC,GAAE,CAAC,IAAE,GAAE;AAAC,QAAAhC,GAAEiC,IAAEJ,IAAED,GAAC;AAAE,cAAM5B,MAAEa,IAAE,SAAQR,MAAE,CAACyB,GAAE9B,IAAE,EAAE,IAAI,GAAE6B,EAAC,IAAE,EAAEI,IAAEC,GAAElC,IAAE,EAAE,IAAI,CAAC,CAAC;AAAE,UAAEiC,IAAEA,IAAE5B,GAAC,GAAE8B,GAAEN,IAAEA,IAAEI,EAAC,GAAEpB,IAAE,sBAAsBgB,IAAEG,EAAC;AAAA,MAAC,WAASD,GAAE,CAAC,IAAE,KAAGC,GAAE,CAAC,IAAE,EAAE;AAAS,MAAAD,GAAE,CAAC,IAAE,GAAEC,GAAE,CAAC,IAAE;AAAE,YAAM3B,MAAE+B,GAAEC,GAAEN,IAAEC,IAAEM,GAAE,GAAExB,EAAC;AAAE,MAAAT,MAAEqB,QAAIA,MAAErB,KAAED,GAAE8B,IAAEN,GAAC,GAAExB,GAAEmC,IAAEV,EAAC,GAAEF,KAAE3B,MAAE;AAAA,IAAE;AAAC,UAAMM,MAAEC,IAAE,UAASiC,KAAEjC,IAAE;AAAO,QAAGmB,MAAET,KAAEA,IAAE;AAAC,UAAIjB,MAAE,OAAO;AAAU,UAAGD,GAAEsC,GAAEH,IAAEK,IAAED,GAAE,GAAED,GAAE/B,KAAEkC,IAAEC,GAAE,GAAEC,EAAC,GAAE;AAAC,QAAA1C,GAAE0C,IAAEA,IAAEpC,GAAC;AAAE,cAAMD,MAAEE,GAAEmC,EAAC;AAAE,UAAEA,IAAEA,IAAE,IAAErC,GAAC,GAAEL,MAAEK,MAAE,EAAEC,KAAEkC,EAAC;AAAA,MAAC;AAAC,MAAA/B,IAAET,KAAE0C,IAAEf,IAAE,KAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgB3B,KAAEI,KAAEG,KAAEC,KAAEU,KAAEF,KAAE;AAAC,QAAG,CAACT,IAAE,QAAQ,cAAc;AAAO,UAAMoC,MAAE3C,IAAE,iBAAiB,IAAIY,GAAE,QAAQ,EAAE,MAAKgC,MAAE5C,IAAE,iBAAiB,IAAIY,GAAE,IAAI,GAAEF,MAAEkC,MAAEA,IAAE,KAAK,CAAC,IAAE,GAAET,MAAE3B,IAAE,CAAC,GAAEqC,MAAErC,IAAE,CAAC,GAAEsC,QAAIpC,MAAE,KAAG,IAAE,KAAGV,IAAE;AAAmB,QAAI+C,KAAE,OAAO,WAAU7C,MAAE;AAAE,aAAQO,MAAE,GAAEA,MAAEkC,IAAE,SAAO,GAAElC,OAAG,GAAE;AAAC,YAAMT,MAAE2C,IAAElC,GAAC,GAAEL,MAAEuC,IAAElC,MAAE,CAAC,GAAEF,MAAE4B,MAAEnC,KAAEQ,MAAEqC,MAAEzC,KAAEc,MAAEyB,IAAElC,MAAE,CAAC,IAAET,KAAEgB,MAAE2B,IAAElC,MAAE,CAAC,IAAEL,KAAEwC,MAAE5B,IAAGE,MAAEX,MAAES,MAAER,QAAIU,MAAEA,MAAEF,MAAEA,MAAG,GAAE,CAAC,GAAEN,MAAEQ,MAAE0B,MAAErC,KAAEuC,MAAE9B,MAAE4B,MAAEpC,KAAEG,KAAED,MAAEA,MAAEoC,MAAEA;AAAE,MAAAnC,KAAEoC,OAAIA,KAAEpC,IAAET,MAAEO,MAAE;AAAA,IAAE;AAAC,IAAAsC,KAAED,MAAEA,OAAG5B,IAAEF,IAAE,MAAKA,IAAE,QAAOd,KAAE,KAAE;AAAA,EAAC;AAAA,EAAC,aAAaF,KAAEK,KAAE;AAAC,WAAM,EAAEA,QAAIH,GAAE,SAAOG,QAAIH,GAAE,aAAWG,QAAIH,GAAE,yBAAuBF,QAAIM,GAAE,mBAAiBN,QAAIM,GAAE;AAAA,EAAgB;AAAA,EAAC,iBAAiBN,KAAE;AAAC,WAAO,IAAIgD,GAAEhD,GAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMA,MAAE,KAAK,WAAW,kBAAgB0B,MAAEuB;AAAE,WAAO,EAAE,KAAK,WAAW,cAAc,IAAE,IAAI7C,IAAEJ,GAAC,IAAE,IAAIkD,GAAElD,IAAE,MAAM,EAAE,MAAMY,GAAE,OAAO,EAAE,MAAMA,GAAE,GAAG,CAAC;AAAA,EAAC;AAAC;AAAC,IAAMoC,KAAN,cAAgB3C,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,kBAAgB;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,0BAA0B,QAAQ,KAAK,eAAe,GAAE,KAAK,kBAAgB;AAAA,EAAI;AAAA,EAAC,qBAAqBL,KAAE;AAAC,IAAAA,IAAE,iBAAe,KAAK,UAAU,WAAW,gBAAc,KAAK,UAAU,cAAc,EAAC,cAAaA,IAAE,aAAY,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,SAAK,YAAUE,GAAE,SAAO,KAAK,qBAAqBF,GAAC;AAAE,UAAMK,MAAE,KAAK,UAAU,WAAW;AAAe,WAAO,KAAK,oBAAkBA,QAAI,KAAK,UAAU,cAAc,KAAK,0BAA0B,KAAK,KAAK,iBAAgBA,GAAC,CAAC,GAAE,KAAK,kBAAgBA,MAAG,KAAK,gBAAgByC,KAAE9C,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMkD,KAAN,MAAO;AAAA,EAAC,YAAYlD,KAAE;AAAC,SAAK,qBAAmBA;AAAA,EAAC;AAAA,EAAC,SAASA,KAAE;AAAC,WAAO,KAAK,mBAAmB,aAAaA,GAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAE;AAAC,WAAOA,IAAE,QAAQ,IAAIY,GAAE,QAAQ,EAAE;AAAA,EAAM;AAAA,EAAC,MAAMZ,KAAEK,KAAED,KAAEG,KAAEC,KAAE;AAAC,IAAA2C,GAAE/C,KAAE,KAAK,oBAAmBJ,KAAEK,KAAEE,KAAEC,GAAC,GAAE,KAAK,cAAcR,KAAEI,KAAEG,KAAEC,GAAC,GAAE,KAAK,UAAUR,KAAEI,KAAEG,KAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcR,KAAEK,KAAED,KAAEG,KAAE;AAAC,UAAMC,MAAEJ,IAAE,SAASQ,GAAE,SAAQJ,EAAC,GAAEU,MAAEb,IAAE,QAAQ,IAAIO,GAAE,QAAQ,GAAEI,MAAEX,IAAE,iBAAiB,IAAIO,GAAE,QAAQ,EAAE,MAAK+B,MAAE3C,KAAE4C,MAAEpC,IAAE,mBAAkBE,MAAEF,IAAE;AAAY,IAAAD,OAAGqC;AAAE,aAAQT,MAAE,GAAEA,MAAEjB,IAAE,SAAO,GAAEiB,OAAG,EAAE,YAAUnC,OAAI,CAAC,GAAE,CAAC,GAAE;AAAC,YAAMK,MAAE,IAAEa,IAAEiB,MAAEnC,GAAC,GAAEI,MAAEY,IAAEX,GAAC,GAAEG,MAAEQ,IAAEX,MAAE,CAAC,GAAEwC,MAAE7B,IAAEX,MAAE,CAAC,GAAEyC,MAAEH,IAAE,CAAC,IAAEvC,MAAEuC,IAAE,CAAC,IAAEnC,MAAEmC,IAAE,CAAC,IAAEE,MAAEF,IAAE,EAAE,GAAEI,KAAEJ,IAAE,CAAC,IAAEvC,MAAEuC,IAAE,CAAC,IAAEnC,MAAEmC,IAAE,CAAC,IAAEE,MAAEF,IAAE,EAAE,GAAEzC,MAAEyC,IAAE,CAAC,IAAEvC,MAAEuC,IAAE,CAAC,IAAEnC,MAAEmC,IAAE,EAAE,IAAEE,MAAEF,IAAE,EAAE;AAAE,MAAAjC,IAAEH,GAAC,IAAEuC,KAAEpC,IAAEH,MAAE,CAAC,IAAEwC,IAAErC,IAAEH,MAAE,CAAC,IAAEL,KAAEK,OAAGqC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAU5C,KAAEK,KAAED,KAAEG,KAAE;AAJvvL;AAIwvL,UAAMC,MAAEJ,IAAE,SAASQ,GAAE,KAAIuB,EAAC,GAAEjB,MAAEb,IAAE,QAAQ,IAAIO,GAAE,QAAQ,GAAE+B,MAAEtC,IAAE,iBAAiB,IAAIO,GAAE,QAAQ,EAAE,MAAKgC,OAAE,KAAAvC,IAAE,iBAAiB,IAAIO,GAAE,eAAe,MAAxC,mBAA2C,MAAKF,MAAEF,IAAE,mBAAkB2B,MAAE3B,IAAE;AAAY,QAAIsC,MAAE;AAAE,IAAAX,IAAE5B,OAAGG,GAAC,IAAEX,GAAE,OAAMoC,IAAE5B,MAAE,CAAC,IAAEuC,KAAEvC,OAAGG;AAAE,UAAMD,MAAE,IAAES,IAAE,CAAC,GAAEP,KAAEO,GAAEU,KAAEe,IAAElC,GAAC,GAAEkC,IAAElC,MAAE,CAAC,GAAEkC,IAAElC,MAAE,CAAC,CAAC;AAAE,IAAAT,OAAGY,GAAED,IAAEA,IAAEX,GAAC;AAAE,UAAMoD,MAAEvB,IAAEhB,MAAEK,IAAE,SAAO;AAAE,QAAIN,MAAE;AAAE,UAAMuC,KAAEP,MAAE,CAAC5C,KAAEK,KAAED,QAAI0C,MAAEF,IAAExC,GAAC,IAAE,CAACJ,KAAEK,KAAED,QAAI0C,OAAG,EAAE9C,KAAEK,GAAC;AAAE,aAAQ0C,KAAE,GAAEA,KAAElC,KAAEkC,MAAG,GAAE;AAAC,YAAM1C,MAAE,IAAEa,IAAE6B,EAAC;AAAE,MAAA7B,GAAEkC,KAAET,IAAEtC,GAAC,GAAEsC,IAAEtC,MAAE,CAAC,GAAEsC,IAAEtC,MAAE,CAAC,CAAC,GAAEL,OAAGY,GAAEwC,KAAEA,KAAEpD,GAAC,GAAEmD,GAAExC,IAAEyC,KAAExC,KAAG;AAAE,eAAQZ,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAmC,IAAE5B,GAAC,IAAE,IAAEP,KAAEmC,IAAE5B,MAAE,CAAC,IAAEuC,KAAEvC,OAAGG;AAAE,MAAAN,GAAEO,IAAEyC,GAAC;AAAA,IAAC;AAAC,UAAMC,KAAE,IAAEnC,IAAEL,GAAC;AAAE,IAAAK,GAAEkC,KAAET,IAAEU,EAAC,GAAEV,IAAEU,KAAE,CAAC,GAAEV,IAAEU,KAAE,CAAC,CAAC,GAAErD,OAAGY,GAAEwC,KAAEA,KAAEpD,GAAC,GAAEmD,GAAExC,IAAEyC,KAAExC,GAAC,GAAEuB,IAAE5B,GAAC,IAAER,GAAE,KAAIoC,IAAE5B,MAAE,CAAC,IAAEuC;AAAA,EAAC;AAAC;AAAC,IAAM3C,KAAN,cAAgBgC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAMtB,IAAE,KAAK,kBAAgB,OAAG,KAAK,gBAAc,OAAG,KAAK,QAAM,GAAE,KAAK,0BAAwB,MAAG,KAAK,eAAa,OAAG,KAAK,iBAAe;AAAA,EAAI;AAAC;AAAC,IAAMe,MAAEe,GAAE;AAAV,IAAYd,KAAEc,GAAE;AAAhB,IAAkBV,KAAEU,GAAE;AAAtB,IAAwBD,KAAEC,GAAE;AAA5B,IAA8BZ,KAAEuB,GAAE;AAAlC,IAAoCtB,KAAEsB,GAAE;AAAxC,IAA0CpB,KAAES,GAAE;AAA9C,IAAgDJ,KAAEI,GAAE;AAApD,IAAsDL,MAAGZ,GAAE;AAA3D,IAA6De,MAAGf,GAAE;AAAlE,IAAoEX,MAAG4B,GAAE;AAAzE,IAA2ExB,MAAG,CAACmC,GAAE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC;AAA9F,IAAgGlC,MAAG,CAACuB,GAAE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC;AAAnH,IAAqHrB,MAAGuB,GAAE;AAA1H,IAA4HtB,MAAGsB,GAAE;AAAjI,IAAmIrB,MAAGqB,GAAE;AAAxI,IAA0IpB,MAAGoB,GAAE;;;ACA1oN,SAASU,IAAEA,KAAE;AAAC,SAAOA,eAAa,gBAAcA,IAAE,UAAQ;AAAE;AAAC,SAASC,IAAED,KAAE;AAAC,SAAO,MAAM,QAAQA,GAAC,KAAGA,IAAE,UAAQ;AAAE;AAAC,SAASE,IAAEA,KAAE;AAAC,SAAOF,IAAEE,GAAC,KAAGD,IAAEC,GAAC;AAAC;;;ACAhJ,IAAMC,MAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,SAAO,IAAIC,OAAE,KAAK,kBAAgB,IAAIA;AAAA,EAAC;AAAC;AAAC,IAAMA,MAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,eAAa,GAAE,KAAK,gBAAc;AAAA,EAAC;AAAC;;;ACA8uB,IAAMC,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,wBAAwBC,KAAEC,KAAE;AAAC,IAAAA,IAAE,mBAAiBD,IAAE,KAAK,SAAOE,IAAE,QAAOD,IAAE,YAAUD,IAAE,gBAAcG,IAAE;AAAA,EAAM;AAAA,EAAC,kBAAkBJ,KAAE;AAAC,WAAO,IAAIK,IAAEL,IAAE,MAAK,GAAE,OAAO,IAAI,EAAE,MAAM,KAAK,aAAa,GAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBN,KAAE;AAAC,UAAME,MAAE,KAAK,eAAcC,MAAEH,QAAIK,IAAE,MAAKA,MAAEL,QAAIK,IAAE,WAAUE,MAAE,KAAK,cAAc,mBAAiBC,MAAE,MAAKJ,OAAGD,OAAGE,QAAIH,IAAE,WAASO,GAAE,cAAYP,IAAE,gBAAcA,IAAE,iBAAeQ,MAAE;AAAK,WAAO,EAAE,EAAC,UAASR,IAAE,WAASO,GAAE,SAAOP,IAAE,WAASO,GAAE,SAAOP,IAAE,WAASO,GAAE,YAAUN,MAAEQ,KAAEC,GAAEZ,GAAC,IAAE,MAAK,WAAU,EAAC,MAAK,EAAE,OAAM,GAAE,YAAWI,KAAE,YAAWS,IAAE,eAAcN,IAAC,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,KAAK,kBAAkB,KAAK,cAAc,oBAAoB;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK,cAAc,gBAAcD,GAAE,SAAOA,GAAE;AAAA,EAAS;AAAC;AAACP,GAAE,SAAO,IAAIE,GAAEa,IAAG,MAAI,OAAO,gCAAuB,CAAE;AAAE,IAAMN,MAAE,EAAC,QAAO,GAAE,OAAM,GAAE;AAA1B,IAA4BG,KAAEJ,GAAEQ,GAAE,KAAIA,GAAE,mBAAmB;;;ACA3zC,IAAMC,MAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAOC,GAAE,OAAM,KAAK,iCAA+BC,GAAE,OAAM,KAAK,uBAAqBC,IAAE,MAAK,KAAK,YAAU,OAAG,KAAK,uBAAqB,MAAG,KAAK,6BAA2B,OAAG,KAAK,SAAO,OAAG,KAAK,UAAQ,OAAG,KAAK,oBAAkB,OAAG,KAAK,2BAAyB,OAAG,KAAK,uBAAqB,OAAG,KAAK,kCAAgC,MAAG,KAAK,gBAAc,OAAG,KAAK,mBAAiB,OAAG,KAAK,eAAa,MAAG,KAAK,uBAAqB,MAAG,KAAK,WAAS,OAAG,KAAK,uBAAqB,OAAG,KAAK,sBAAoB,OAAG,KAAK,kBAAgB,OAAG,KAAK,gBAAc,OAAG,KAAK,iCAA+B;AAAA,EAAE;AAAC;AAAC,EAAE,CAACC,IAAE,EAAC,OAAMH,GAAE,MAAK,CAAC,CAAC,GAAEF,IAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACK,IAAE,EAAC,OAAMF,GAAE,MAAK,CAAC,CAAC,GAAEH,IAAE,WAAU,kCAAiC,MAAM,GAAE,EAAE,CAACK,IAAE,EAAC,OAAMD,IAAE,MAAK,CAAC,CAAC,GAAEJ,IAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,8BAA6B,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,4BAA2B,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,mCAAkC,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAACK,IAAE,CAAC,GAAEL,IAAE,WAAU,kCAAiC,MAAM,GAAE,EAAE,CAACK,IAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAEL,IAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAACK,IAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAEL,IAAE,WAAU,mBAAkB,MAAM;;;ACAtL,IAAMM,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMA,KAAE,IAAI,IAAE,GAAE,KAAK,iBAAe,IAAIC;AAAA,EAAC;AAAA,EAAC,iBAAiBD,KAAEE,KAAE;AAAC,WAAO,KAAK,eAAe,SAAOF,KAAE,KAAK,eAAe,gBAAc,KAAK,WAAW,eAAc,KAAK,eAAe,oBAAkB,CAAC,CAAC,KAAK,WAAW,gBAAe,KAAK,eAAe,2BAAyB,CAAC,CAAC,KAAK,WAAW,uBAAsB,KAAK,eAAe,iCAA+B,aAAW,KAAK,WAAW,oBAAkBG,GAAE,SAAOA,GAAE,OAAM,KAAK,eAAe,mBAAiB,KAAK,WAAW,eAAc,KAAK,eAAe,WAAS,KAAK,WAAW,UAAS,KAAK,eAAe,uBAAqB,KAAK,WAAW,eAAc,KAAK,eAAe,uBAAqB,KAAK,WAAW,sBAAqB,KAAK,eAAe,6BAA2B,KAAK,WAAW,8BAA6B,KAAK,eAAe,SAAO,CAAC,CAAC,KAAK,WAAW,eAAc,KAAK,eAAe,UAAQ,CAAC,CAAC,KAAK,WAAW,gBAAe,KAAK,eAAe,gBAAcD,IAAE,SAAOE,GAAE,oBAAkB,KAAK,WAAW,kBAAgBJ,QAAID,GAAE,SAAOC,QAAID,GAAE,QAAOC,QAAID,GAAE,UAAQ,KAAK,eAAe,uBAAqB,CAAC,CAACG,IAAE,qBAAoBF,QAAID,GAAE,cAAY,KAAK,eAAe,kCAAgC,KAAK,WAAW,2BAA0B,KAAK,eAAe,eAAa,KAAK,WAAW,cAAa,KAAK,eAAe,uBAAqBG,IAAE,sBAAqB,KAAK,eAAe,uBAAqBA,IAAE,kBAAkB,SAAQ,KAAK,eAAe,sBAAoBA,IAAE,iBAAiB,SAAQ,KAAK,eAAe,kBAAgBA,IAAE,iBAAiB,iBAAgB,KAAK;AAAA,EAAc;AAAA,EAAC,UAAUF,KAAEK,KAAEC,KAAEC,KAAEC,KAAEC,KAAE;AAAC,QAAG,CAACH,IAAE,QAAQ,iBAAe,CAACA,IAAE,QAAQ,OAAK,CAACN,IAAE,QAAQ;AAAO,UAAMU,MAAE,KAAK;AAAW,QAAIC,KAAE,GAAEC,MAAE;AAAE,QAAGX,GAAEY,KAAGR,GAAC,GAAE,EAAEL,IAAE,iBAAiB,GAAE;AAAC,YAAME,MAAEF,IAAE,kBAAkB,EAAE;AAAE,MAAAW,KAAET,IAAE,CAAC,GAAEU,MAAEV,IAAE,CAAC,GAAEY,IAAGD,GAAE;AAAA,IAAC;AAAC,UAAME,MAAEf,IAAE,iBAAiB,IAAIgB,GAAE,QAAQ,GAAEC,KAAEjB,IAAE,iBAAiB,IAAIgB,GAAE,IAAI,GAAEE,MAAElB,IAAE,iBAAiB,IAAIgB,GAAE,MAAM,GAAEG,MAAEnB,IAAE,iBAAiB,IAAIgB,GAAE,OAAO;AAAE,IAAAI,GAAEL,IAAE,QAAM,CAAC;AAAE,UAAMM,KAAEf,IAAE,OAAMgB,KAAEhB,IAAE,QAAOiB,KAAEC,GAAEd,GAAC;AAAE,IAAAC,MAAGW,GAAE,YAAWV,OAAGU,GAAE;AAAW,UAAMG,KAAE,aAAW,KAAK,WAAW;AAAkB,aAAQvB,MAAE,GAAEA,MAAEa,IAAE,KAAK,SAAOA,IAAE,MAAKb,OAAI;AAAC,YAAMF,MAAEE,MAAEa,IAAE;AAAK,MAAAP,GAAEkB,KAAGX,IAAE,KAAKf,GAAC,GAAEe,IAAE,KAAKf,MAAE,CAAC,GAAEe,IAAE,KAAKf,MAAE,CAAC,CAAC,GAAEgB,GAAEU,KAAGA,KAAGrB,GAAC;AAAE,YAAMe,MAAElB,MAAEe,GAAE;AAAK,SAAG,CAAC,IAAEA,GAAE,KAAKG,GAAC,IAAET,IAAE,GAAG,CAAC,IAAEM,GAAE,KAAKG,MAAE,CAAC,IAAER,KAAEI,GAAEU,KAAGA,KAAGJ,GAAE,UAAU;AAAE,YAAMf,MAAEL,MAAEiB,IAAE;AAAK,UAAGX,GAAEmB,KAAGR,IAAE,KAAKZ,MAAE,CAAC,GAAEY,IAAE,KAAKZ,MAAE,CAAC,GAAEY,IAAE,KAAKZ,MAAE,CAAC,CAAC,GAAE,CAACkB,OAAIC,IAAG,CAAC,KAAGC,IAAG,CAAC,GAAED,IAAG,CAAC,KAAGC,IAAG,CAAC,GAAE,MAAIA,IAAG,CAAC,IAAG;AAAC,cAAM3B,MAAE2B,IAAG,CAAC;AAAE,QAAAC,GAAED,KAAGD,GAAE,GAAE1B,GAAE0B,KAAGA,KAAG,EAAEC,KAAGA,KAAG3B,GAAC,CAAC;AAAA,MAAC;AAAC,YAAMQ,MAAEN,MAAEgB,IAAE;AAAK,UAAGV,GAAEqB,KAAGX,IAAE,KAAKV,GAAC,GAAEU,IAAE,KAAKV,MAAE,CAAC,GAAEU,IAAE,KAAKV,MAAE,CAAC,CAAC,GAAE,KAAK,oBAAoBqB,KAAGhB,KAAGS,IAAEQ,GAAE,GAAE,KAAK,uCAAuCJ,KAAGI,KAAGR,IAAES,GAAE,GAAET,GAAE,gBAAgBI,KAAGM,GAAE,GAAEA,IAAG,CAAC,IAAE,IAAG;AAAC,QAAAA,IAAG,CAAC,IAAE,KAAK,MAAMA,IAAG,CAAC,CAAC,GAAEA,IAAG,CAAC,IAAE,KAAK,MAAMA,IAAG,CAAC,CAAC,GAAEP,OAAIE,IAAG,CAAC,KAAGA,IAAG,CAAC,OAAKK,IAAG,CAAC,KAAGL,IAAG,CAAC,GAAE,MAAIA,IAAG,CAAC,MAAIK,IAAG,CAAC,KAAGxB,IAAEmB,IAAG,CAAC,GAAEI,IAAG,eAAe,IAAGT,GAAE,kBAAkBU,KAAGN,GAAE,IAAGM,IAAG,CAAC,KAAG,KAAK,WAAW,aAAa,CAAC,GAAEA,IAAG,CAAC,KAAG,KAAK,WAAW,aAAa,CAAC,GAAEC,GAAE,IAAGF,IAAG,QAAO,EAAE;AAAE,cAAM/B,MAAE,KAAGsB,GAAE;AAAW,YAAIpB,MAAE;AAAE,YAAGQ,IAAE,iCAA+BR,MAAEQ,IAAE,cAAYY,GAAE,aAAW,IAAGD,MAAGa,IAAGb,IAAEW,IAAG,CAAC,GAAEA,IAAG,CAAC,GAAE,IAAGhC,KAAEE,KAAEQ,KAAEa,EAAC,GAAE;AAAC,gBAAMvB,MAAEM,IAAE;AAAI,cAAGU,GAAEmB,KAAGT,KAAG3B,GAAEqC,KAAGd,GAAE,UAAU,CAAC,GAAEU,IAAG,CAAC,IAAEX,GAAE,CAAC,GAAEW,IAAG,CAAC,IAAEX,GAAE,CAAC,GAAEC,GAAE,0BAA0BU,KAAGN,GAAE,GAAE;AAAC,kBAAMxB,MAAEK,GAAE;AAAE,YAAAF,GAAEH,KAAEF,IAAE,SAAS;AAAE,kBAAMK,MAAE,IAAEe,GAAElB,GAAC;AAAE,cAAEA,KAAEA,KAAEG,GAAC;AAAE,YAAAI,IAAE,EAAET,IAAE,QAAO0B,GAAE,IAAErB,KAAEH,KAAE,IAAG,MAAG,GAAEiC,GAAE;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBnC,KAAEK,KAAEe,KAAEd,KAAEL,KAAEM,KAAE;AAAC,UAAMC,MAAER,IAAE,iBAAiB,IAAIgB,GAAE,QAAQ,GAAEP,MAAET,IAAE,iBAAiB,IAAIgB,GAAE,IAAI,GAAEN,MAAE,KAAK,YAAWuB,MAAET,GAAEd,GAAC;AAAE,QAAI2B,MAAE,GAAEtC,MAAE;AAAE,QAAG,EAAEC,IAAE,iBAAiB,GAAE;AAAC,YAAME,MAAEF,IAAE,kBAAkB,EAAE;AAAE,MAAAqC,MAAEnC,IAAE,CAAC,GAAEH,MAAEG,IAAE,CAAC;AAAA,IAAC;AAAC,IAAAmC,OAAGrC,IAAE,oBAAmBD,OAAGC,IAAE;AAAmB,UAAMG,MAAE,KAAGH,IAAE;AAAmB,aAAQE,MAAE,GAAEA,MAAEM,IAAE,KAAK,SAAOA,IAAE,MAAKN,OAAI;AAAC,YAAMG,MAAEH,MAAEM,IAAE,MAAKY,MAAEZ,IAAE,KAAKH,GAAC,GAAEiC,KAAE9B,IAAE,KAAKH,MAAE,CAAC,GAAEkC,MAAErC,MAAEO,IAAE;AAAK,SAAG,CAAC,IAAEA,IAAE,KAAK8B,GAAC,IAAEF,KAAE,GAAG,CAAC,IAAE5B,IAAE,KAAK8B,MAAE,CAAC,IAAExC;AAAE,UAAIyC,KAAE;AAAE,MAAA9B,IAAE,iCAA+B8B,KAAE9B,IAAE,cAAYV,IAAE,qBAAmB,IAAGkC,IAAG5B,KAAEc,KAAEkB,IAAE,IAAGnC,KAAEqC,IAAE9B,KAAEuB,GAAC,KAAGhC,IAAEM,IAAE,MAAKA,IAAE,QAAO,IAAG,KAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,IAAI,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,oBAAoBP,KAAEE,KAAEG,KAAEC,KAAE;AAAC,WAAOJ,IAAEA,GAAC,MAAIA,MAAED,GAAEwC,KAAGvC,GAAC,IAAG,EAAEI,IAAE,QAAON,KAAEE,GAAC,GAAEc,GAAEV,IAAE,QAAOA,IAAE,QAAOD,IAAE,0BAA0B,GAAEC,IAAE,WAAS,EAAEoC,KAAG,EAAE,GAAEpC;AAAA,EAAC;AAAA,EAAC,iBAAiBN,KAAEK,KAAEe,KAAE;AAAC,UAAMd,MAAE,KAAK;AAAW,MAAEA,IAAE,qBAAqB,IAAEiC,GAAEnB,KAAEf,KAAEC,IAAE,uBAAsBN,IAAE,MAAM,KAAGA,IAAE,OAAO,QAAM,GAAEA,IAAE,OAAO,SAAO,GAAEA,IAAE,OAAO,eAAa,GAAEA,IAAE,OAAO,gBAAc,IAAG,EAAEM,IAAE,8BAA8B,IAAEiC,GAAEnB,KAAEf,KAAEC,IAAE,gCAA+BN,IAAE,eAAe,KAAGA,IAAE,gBAAgB,SAAOA,IAAE,OAAO,QAAOA,IAAE,gBAAgB,QAAMA,IAAE,OAAO,OAAMA,IAAE,gBAAgB,eAAaA,IAAE,OAAO,cAAaA,IAAE,gBAAgB,gBAAcA,IAAE,OAAO;AAAA,EAAc;AAAA,EAAC,uBAAuBA,KAAEE,KAAEG,KAAEe,KAAEd,KAAEL,KAAEM,KAAE;AAAC,UAAMC,MAAE,KAAK,oBAAoBN,KAAEG,KAAEC,KAAEwB,GAAE;AAAE,WAAO,KAAK,+BAA+B9B,KAAEQ,KAAEF,KAAEC,GAAC,GAAE,KAAK,uCAAuCA,KAAEC,KAAEF,KAAEL,GAAC,GAAE,KAAK,wBAAwBM,KAAEC,KAAEY,IAAE,CAAC,GAAEd,KAAEC,GAAC,GAAE,KAAK,uBAAuBA,KAAEa,KAAEb,GAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,sBAAsBP,KAAEK,KAAEe,KAAEd,KAAEL,KAAE;AAAC,WAAO,KAAK,sBAAsBD,KAAEK,KAAEe,KAAEd,GAAC,GAAE,EAAEL,GAAC,KAAGI,GAAEJ,KAAEK,GAAC,GAAE,KAAK,uBAAuBA,KAAED,KAAEe,KAAEd,GAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,wBAAwBJ,KAAEG,KAAEe,KAAEd,KAAEL,KAAE;AAAC,UAAMM,MAAED,IAAE,cAAY,IAAE;AAAG,QAAIE,MAAE,KAAK,KAAKY,GAAC;AAAE,UAAIZ,QAAIA,MAAED;AAAG,UAAME,MAAEF,MAAEC;AAAE,QAAG,KAAK,WAAW,uBAAqB,EAAE,QAAOH,GAAEJ,KAAEC,GAAC;AAAE,UAAMQ,MAAET,GAAE,KAAK,IAAII,IAAE,QAAQ,GAAE,MAAI,CAAC,GAAE4B,MAAE,IAAE,KAAK,KAAK,IAAEvB,MAAEA,GAAC,IAAEA,MAAEJ,IAAE,SAAS,CAAC;AAAE,WAAO,EAAEL,KAAEC,KAAEO,MAAE,IAAEwB,MAAE,IAAEA,GAAC,GAAEhC;AAAA,EAAC;AAAA,EAAC,+BAA+BD,KAAEE,KAAEG,KAAEe,KAAE;AAAC,UAAMd,MAAEc,GAAEpB,GAAC,GAAEC,MAAEI,IAAE,cAAY,IAAE,IAAGE,MAAE,MAAGF,IAAE,6BAA6BC,GAAC,GAAEE,MAAE,EAAEkB,KAAGxB,IAAE,QAAOD,MAAEM,GAAC;AAAE,WAAO8B,GAAEjB,KAAEpB,KAAEQ,GAAC,GAAEY;AAAA,EAAC;AAAA,EAAC,uCAAuCpB,KAAEE,KAAEkB,KAAEd,KAAE;AAAC,UAAML,MAAE,KAAK;AAAW,QAAG,CAACA,IAAE,kBAAgB,CAACA,IAAE,eAAe,cAAa;AAAC,UAAGA,IAAE,yBAAuBA,IAAE,gCAA+B;AAAC,cAAMI,MAAEe,GAAEpB,GAAC;AAAE,aAAK,iBAAiBM,KAAED,KAAEH,IAAE,QAAQ;AAAA,MAAC,MAAM,CAAAI,IAAE,OAAO,QAAM,GAAEA,IAAE,gBAAgB,QAAM;AAAE,aAAON;AAAA,IAAC;AAAC,UAAMO,MAAEa,GAAEpB,GAAC,GAAEQ,MAAE,EAAEP,IAAE,gCAA+BA,IAAE,qBAAqB,GAAEQ,MAAEkC,GAAEvB,KAAEb,KAAEN,IAAE,gBAAeC,IAAE,UAASM,GAAC;AAAE,WAAO,KAAK,iBAAiBF,KAAEC,KAAEL,IAAE,QAAQ,GAAE,EAAEA,IAAE,QAAOA,IAAE,QAAOO,GAAC,GAAE4B,GAAErC,KAAEA,KAAEE,IAAE,MAAM;AAAA,EAAC;AAAA,EAAC,uBAAuBF,KAAEE,KAAEG,KAAE;AAAC,UAAMe,MAAE,aAAW,KAAK,WAAW;AAAkB,WAAOf,QAAIL,OAAGK,GAAEA,KAAEL,GAAC,GAAEoB,QAAIf,IAAE,CAAC,KAAGH,IAAE,CAAC,GAAEG,IAAE,CAAC,KAAGH,IAAE,CAAC,GAAEA,IAAE,CAAC,MAAI0B,GAAEC,KAAGxB,GAAC,GAAEgC,GAAEhC,KAAEA,KAAE,EAAEwB,KAAGA,KAAG3B,IAAE,CAAC,CAAC,CAAC,KAAIG;AAAA,EAAC;AAAA,EAAC,sBAAsBL,KAAEE,KAAEG,KAAEe,KAAE;AAAC,UAAMd,MAAE,aAAW,KAAK,WAAW;AAAkB,WAAOc,QAAIpB,OAAGK,GAAEe,KAAEpB,GAAC,GAAEM,QAAIc,IAAE,CAAC,KAAGlB,IAAE,CAAC,IAAEG,IAAE,YAAU,GAAEe,IAAE,CAAC,KAAGlB,IAAE,CAAC,IAAEG,IAAE,aAAW,IAAGe;AAAA,EAAC;AAAA,EAAC,uBAAuBpB,KAAEE,KAAEG,KAAEe,KAAE;AAAC,UAAMd,MAAE,KAAK,WAAW;AAAoB,QAAGN,QAAIoB,OAAGf,GAAEe,KAAEpB,GAAC,GAAEM,KAAE;AAAC,YAAMN,MAAEK,IAAE,cAAY,IAAE,IAAGJ,MAAED,MAAE,KAAK,KAAKE,IAAE,CAAC,CAAC;AAAE,MAAAkB,IAAE,CAAC,MAAInB,OAAGD,OAAGM;AAAA,IAAC;AAAC,WAAOc;AAAA,EAAC;AAAA,EAAC,aAAapB,KAAEE,KAAE;AAAC,QAAGA,QAAIH,GAAE,SAAOG,QAAIH,GAAE,SAAOG,QAAIH,GAAE,aAAWG,QAAIH,GAAE,uBAAsB;AAAC,UAAGC,QAAII,GAAE,gBAAgB,QAAM;AAAG,YAAK,EAAC,kBAAiBF,KAAE,eAAcG,IAAC,IAAE,KAAK;AAAW,aAAOL,SAAKE,MAAEE,GAAE,iBAAeA,GAAE,iBAAeC,OAAGL,QAAII,GAAE;AAAA,IAAgB;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,iBAAiBJ,KAAE;AAAC,WAAO,IAAI4C,IAAG5C,GAAC;AAAA,EAAC;AAAA,EAAC,8BAA8BA,KAAEE,KAAEG,MAAEgC,GAAE,GAAE;AAAC,WAAOQ,IAAG,KAAK,YAAW7C,KAAEE,KAAEG,GAAC,GAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEL,IAAE,CAAC,GAAEK,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEL,IAAE,CAAC,GAAEK;AAAA,EAAC;AAAC;AAAC,IAAMuC,MAAN,cAAiB7C,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAM,EAAC,GAAGA,KAAE,GAAGA,IAAE,SAAS,WAAU,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,KAAE;AAAC,WAAO,KAAK,gBAAgB8C,IAAE9C,GAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,WAAO,KAAK,cAAc,KAAK,UAAU,WAAW,SAAS,GAAE,KAAK,UAAU,cAAc,KAAK,qBAAqB,GAAE,KAAK,cAAcA,GAAC;AAAA,EAAC;AAAC;AAAC,SAAS6C,IAAG7C,KAAEE,KAAEG,KAAEe,MAAE2B,KAAG;AAAC,SAAO9C,GAAEmB,KAAEpB,IAAE,cAAc,GAAEoB,IAAE,CAAC,KAAG,CAAClB,IAAE,CAAC,GAAEkB,IAAE,CAAC,KAAG,CAAClB,IAAE,CAAC,GAAEkB,IAAE,CAAC,KAAGpB,IAAE,aAAa,CAAC,IAAEK,KAAEe,IAAE,CAAC,KAAGpB,IAAE,aAAa,CAAC,IAAEK,KAAEe;AAAC;AAAC,SAASN,IAAGd,KAAE;AAAC,QAAME,MAAEF,IAAE,CAAC,GAAEK,MAAEL,IAAE,CAAC,GAAEoB,MAAEpB,IAAE,CAAC,GAAEM,MAAEN,IAAE,CAAC,GAAEC,MAAED,IAAE,CAAC,GAAEO,MAAEP,IAAE,CAAC,GAAEQ,MAAER,IAAE,CAAC,GAAES,MAAET,IAAE,CAAC,GAAEU,MAAEV,IAAE,CAAC,GAAEiC,MAAE,IAAE,KAAK,KAAK/B,MAAEA,MAAEG,MAAEA,MAAEe,MAAEA,GAAC,GAAEiB,MAAE,IAAE,KAAK,KAAK/B,MAAEA,MAAEL,MAAEA,MAAEM,MAAEA,GAAC,GAAER,MAAE,IAAE,KAAK,KAAKS,MAAEA,MAAEC,MAAEA,MAAEC,MAAEA,GAAC;AAAE,SAAOV,IAAE,CAAC,IAAEE,MAAE+B,KAAEjC,IAAE,CAAC,IAAEK,MAAE4B,KAAEjC,IAAE,CAAC,IAAEoB,MAAEa,KAAEjC,IAAE,CAAC,IAAEM,MAAE+B,KAAErC,IAAE,CAAC,IAAEC,MAAEoC,KAAErC,IAAE,CAAC,IAAEO,MAAE8B,KAAErC,IAAE,CAAC,IAAEQ,MAAET,KAAEC,IAAE,CAAC,IAAES,MAAEV,KAAEC,IAAE,CAAC,IAAEU,MAAEX,KAAEC;AAAC;AAAC,SAASkC,IAAGlC,KAAEK,KAAEe,KAAEd,KAAEL,KAAEM,KAAEC,KAAEC,KAAE;AAAC,MAAIC,MAAEL,MAAEJ,OAAGQ,IAAE,CAAC,IAAE,IAAEH,IAAE,CAAC,IAAEG,IAAE,CAAC,IAAE,IAAGwB,MAAEvB,MAAEJ,IAAE,CAAC,IAAE,IAAEL,KAAEoC,MAAEjB,MAAEnB,OAAGQ,IAAE,CAAC,IAAE,IAAEH,IAAE,CAAC,IAAEG,IAAE,CAAC,IAAE,IAAGV,MAAEsC,MAAE/B,IAAE,CAAC,IAAE,IAAEL;AAAE,QAAME,MAAEK,IAAE;AAAyB,SAAOA,IAAE,gCAA8B,EAAEL,GAAC,MAAIO,OAAGJ,IAAE,CAAC,IAAEH,IAAE,CAAC,GAAEkC,OAAG/B,IAAE,CAAC,IAAEH,IAAE,CAAC,GAAE8B,OAAG3B,IAAE,CAAC,KAAG,IAAEH,IAAE,CAAC,IAAGJ,OAAGO,IAAE,CAAC,KAAG,IAAEH,IAAE,CAAC,IAAGO,OAAGH,KAAE0B,OAAG1B,KAAE8B,OAAG9B,KAAER,OAAGQ,MAAGP,IAAE,CAAC,IAAEU,OAAGV,IAAE,CAAC,IAAEiC,OAAGjC,IAAE,CAAC,IAAEqC,OAAGrC,IAAE,CAAC,IAAED;AAAC;AAAC,IAAMgC,MAAG,IAAI7B;AAAb,IAAe6C,MAAGxC,IAAE;AAApB,IAAsBmB,MAAGnB,GAAE;AAA3B,IAA6BsB,MAAGtB,GAAE;AAAlC,IAAoCyB,MAAGzB,GAAE;AAAzC,IAA2CmC,MAAGnC,GAAE;AAAhD,IAAkD4B,MAAG5B,GAAE;AAAvD,IAAyDM,MAAGb,GAAE;AAA9D,IAAgEyC,MAAGzC,GAAE;AAArE,IAAuEoC,MAAGpC,GAAE;AAA5E,IAA8E2B,MAAGpB,GAAE;AAAnF,IAAqFuB,MAAG,EAAC,QAAOY,KAAG,UAAS,EAAC;AAA7G,IAA+G,KAAG1C,GAAE;AAApH,IAAsH,KAAG;AAAzH,IAA2H,KAAG;AAA9H,IAAgI,KAAG,CAAC,GAAE,CAAC;AAAvI,IAAyI,KAAGK,GAAE,GAAE,GAAE,CAAC;AAAE,IAAM,KAAN,cAAiBG,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,iBAAeC,GAAE,SAAQ,KAAK,QAAMJ,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,gBAAc,CAAC,GAAE,CAAC,GAAE,KAAK,gBAAc,OAAG,KAAK,iBAAeA,GAAE,KAAG,GAAE,GAAE,KAAK,eAAa,CAAC,GAAE,CAAC,GAAE,KAAK,sBAAoB,MAAK,KAAK,+BAA6B,OAAG,KAAK,eAAaA,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,cAAY,GAAE,KAAK,gBAAc,OAAG,KAAK,gBAAc,CAAC,GAAE,GAAE,CAAC,GAAE,KAAK,gBAAc,CAAC,KAAI,KAAI,GAAG,GAAE,KAAK,eAAa,CAAC,GAAE,GAAE,CAAC,GAAE,KAAK,eAAa,CAAC,GAAE,GAAE,CAAC,GAAE,KAAK,iBAAe,OAAG,KAAK,gBAAc,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,gBAAc,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,gBAAc,OAAG,KAAK,uBAAqB,MAAG,KAAK,gBAAc,MAAG,KAAK,2BAAyB,MAAG,KAAK,oBAAkB,SAAQ,KAAK,mBAAiB,OAAG,KAAK,eAAa,MAAG,KAAK,WAAS;AAAA,EAAE;AAAC;AAAC,IAAM,KAAGkB,GAAE,EAAE,MAAMP,GAAE,QAAQ,EAAE,MAAMA,GAAE,MAAM,EAAE,MAAMA,GAAE,GAAG,EAAE,OAAOA,GAAE,KAAK,EAAE,MAAMA,GAAE,IAAI,EAAE,MAAMA,GAAE,OAAO,EAAE,MAAMA,GAAE,OAAO;AAA1H,IAA4H,KAAG,GAAG,MAAM,EAAE,OAAOA,GAAE,qBAAqB;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYhB,KAAE;AAAC,SAAK,YAAUA,KAAE,KAAK,qBAAmB,IAAI,2CAA2C,IAAE,KAAG;AAAA,EAAE;AAAA,EAAC,SAASA,KAAE;AAAC,WAAO,KAAK,mBAAmB,aAAaA,GAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,KAAE;AAAC,WAAO,IAAEA,IAAE,QAAQ,IAAIgB,GAAE,QAAQ,EAAE;AAAA,EAAM;AAAA,EAAC,MAAMhB,KAAEK,KAAEe,KAAEd,KAAEL,KAAE;AAAC,IAAAuC,GAAEpB,IAAE,QAAQ,IAAIJ,GAAE,QAAQ,GAAEI,IAAE,iBAAiB,IAAIJ,GAAE,QAAQ,EAAE,MAAKhB,KAAEM,IAAE,UAASL,KAAE,CAAC,GAAE6C,GAAE1B,IAAE,QAAQ,IAAIJ,GAAE,MAAM,GAAEI,IAAE,iBAAiB,IAAIJ,GAAE,MAAM,EAAE,MAAKX,KAAEC,IAAE,QAAOL,KAAE,CAAC;AAAE,UAAMM,MAAEa,IAAE,iBAAiB,IAAIJ,GAAE,GAAG,EAAE;AAAK,QAAIR,KAAEC,KAAEC,KAAEuB;AAAE,QAAG,QAAM1B,OAAGA,IAAE,SAAO,GAAE;AAAC,YAAMP,MAAE,KAAK,UAAU;AAAW,MAAAQ,MAAE,GAAEC,MAAE,GAAEC,MAAEV,IAAE,cAAc,CAAC,GAAEiC,MAAEjC,IAAE,cAAc,CAAC;AAAA,IAAC,MAAM,CAAAQ,MAAED,IAAE,CAAC,GAAEE,MAAEF,IAAE,CAAC,GAAEG,MAAEH,IAAE,CAAC,GAAE0B,MAAE1B,IAAE,CAAC;AAAE,IAAAG,MAAE,KAAK,IAAI,SAAQA,MAAE,CAAC,GAAEuB,MAAE,KAAK,IAAI,SAAQA,MAAE,CAAC;AAAE,QAAII,MAAEjB,IAAE,QAAQ,IAAIJ,GAAE,QAAQ,EAAE,QAAOjB,MAAEE;AAAE,UAAME,MAAEG,IAAE;AAAI,aAAQJ,MAAE,GAAEA,MAAEmC,KAAE,EAAEnC,IAAE,CAAAC,IAAE,IAAIJ,KAAE,GAAES,GAAC,GAAEL,IAAE,IAAIJ,KAAE,GAAEU,GAAC,GAAEV,OAAG,GAAEI,IAAE,IAAIJ,KAAE,GAAEW,GAAC,GAAEP,IAAE,IAAIJ,KAAE,GAAEU,GAAC,GAAEV,OAAG,GAAEI,IAAE,IAAIJ,KAAE,GAAEW,GAAC,GAAEP,IAAE,IAAIJ,KAAE,GAAEkC,GAAC,GAAElC,OAAG,GAAEI,IAAE,IAAIJ,KAAE,GAAEW,GAAC,GAAEP,IAAE,IAAIJ,KAAE,GAAEkC,GAAC,GAAElC,OAAG,GAAEI,IAAE,IAAIJ,KAAE,GAAES,GAAC,GAAEL,IAAE,IAAIJ,KAAE,GAAEkC,GAAC,GAAElC,OAAG,GAAEI,IAAE,IAAIJ,KAAE,GAAES,GAAC,GAAEL,IAAE,IAAIJ,KAAE,GAAEU,GAAC,GAAEV,OAAG;AAAE,IAAAiB,GAAEI,IAAE,QAAQ,IAAIJ,GAAE,KAAK,GAAEI,IAAE,iBAAiB,IAAIJ,GAAE,KAAK,EAAE,MAAK,GAAEV,IAAE,OAAML,KAAE,CAAC;AAAE,UAAMqC,KAAElB,IAAE,QAAQ,IAAIJ,GAAE,IAAI,GAAEuB,MAAEnB,IAAE,iBAAiB,IAAIJ,GAAE,IAAI,EAAE;AAAK,IAAAqB,MAAEC,GAAE;AAAO,UAAME,KAAElC,IAAE;AAAK,IAAAP,MAAEE;AAAE,aAAQC,MAAE,GAAEA,MAAEmC,KAAE,EAAEnC,KAAE;AAAC,YAAMF,MAAEuC,IAAE,IAAED,GAAEpC,GAAC,CAAC,GAAEG,MAAEkC,IAAE,IAAED,GAAEpC,GAAC,IAAE,CAAC;AAAE,eAAQA,MAAE,GAAEA,MAAE,GAAE,EAAEA,IAAE,CAAAsC,GAAE,IAAIzC,KAAE,GAAEC,GAAC,GAAEwC,GAAE,IAAIzC,KAAE,GAAEM,GAAC,GAAEN,OAAG;AAAA,IAAC;AAAC,QAAGqB,IAAE,QAAQ,IAAIJ,GAAE,OAAO,KAAGI,IAAE,iBAAiB,IAAIJ,GAAE,OAAO,IAAEf,IAAEmB,IAAE,QAAQ,IAAIJ,GAAE,OAAO,GAAEI,IAAE,iBAAiB,IAAIJ,GAAE,OAAO,EAAE,MAAKV,IAAE,SAAQL,KAAE,CAAC,IAAEE,GAAEG,IAAE,SAAQL,KAAE,IAAEoC,GAAC,GAAEjB,IAAE,QAAQ,IAAIJ,GAAE,OAAO,KAAGI,IAAE,iBAAiB,IAAIJ,GAAE,OAAO,IAAEf,IAAEmB,IAAE,QAAQ,IAAIJ,GAAE,OAAO,GAAEI,IAAE,iBAAiB,IAAIJ,GAAE,OAAO,EAAE,MAAKV,IAAE,SAAQL,KAAE,CAAC,IAAEE,GAAEG,IAAE,SAAQL,KAAE,IAAEoC,GAAC,GAAE,EAAEjB,IAAE,qBAAqB,GAAE;AAAC,UAAGA,IAAE,QAAQ,IAAIJ,GAAE,QAAQ,GAAE;AAAC,cAAMhB,MAAEoB,IAAE,QAAQ,IAAIJ,GAAE,QAAQ,EAAE,QAAOd,MAAEI,IAAE,SAASU,GAAE,uBAAsBE,EAAC;AAAE,QAAAyB,GAAEvB,IAAE,uBAAsBlB,KAAEF,KAAEC,KAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC;", "names": ["C", "e", "a", "t", "A", "t", "e", "P", "m", "M", "R", "F", "r", "u", "n", "k", "o", "i", "s", "U", "O", "_", "I", "t", "l", "r", "n", "o", "i", "e", "s", "u", "a", "m", "c", "O", "S", "V", "G", "x", "X", "t", "t", "e", "r", "a", "i", "n", "c", "e", "t", "n", "o", "i", "s", "a", "l", "u", "f", "d", "x", "m", "p", "g", "r", "z", "j", "v", "U", "O", "C", "h", "E", "y", "A", "R", "T", "c", "o", "e", "a", "e", "r", "c", "o", "u", "m", "s", "i", "l", "t", "p", "n", "f", "d", "h", "t", "r", "e", "n", "i", "s", "o", "l", "x", "r", "t", "n", "e", "L", "M", "i", "s", "o", "c", "O", "l", "a", "m", "b", "d", "R", "C", "H", "U", "u", "E", "T", "e", "l", "r", "s", "e", "n", "t", "C", "V", "t", "a", "n", "r", "Z", "$", "v", "t", "n", "r", "e", "H", "l", "i", "a", "c", "s", "_", "h", "o", "u", "E", "b", "f", "p", "z", "y", "x", "I", "O", "y", "e", "o", "t", "i", "c", "a", "d", "h", "A", "l", "E", "_", "f", "s", "m", "P", "B", "e", "z", "h", "H", "s", "I", "t", "Z", "E", "i", "q", "c", "r", "a", "n", "o", "O", "l", "u", "p", "m", "f", "T", "d", "_", "R", "b", "L", "P", "$", "N", "j", "C", "D", "U", "V", "X", "Y", "ee", "Q", "M", "F", "x", "k", "G", "y", "v", "A", "S", "g", "W", "t", "s", "c", "r", "n", "o", "n", "p", "t", "e", "i", "o", "r", "n", "c", "s", "a", "h", "g", "j", "m", "z", "l", "x", "d", "b", "u", "M", "v", "O", "i", "t", "e", "r", "s", "t", "c", "e", "o", "o", "s", "i", "t", "c", "t", "f", "n", "A", "T", "t", "e", "I", "u", "n", "l", "r", "s", "h", "k", "i", "a", "y", "o", "O", "d", "c", "m", "N", "M", "x", "B", "G", "z", "U", "n", "e", "h", "t", "r", "u", "c", "g", "H", "i", "z", "a", "T", "f", "e", "n", "x", "P", "D", "t", "s", "e", "t", "o", "f", "r", "h", "H", "a", "n", "e", "t", "c", "r", "i", "s", "o", "a", "e", "t", "r", "i", "s", "l", "o", "l", "m", "E", "$", "n", "L", "c", "s", "i", "t", "i", "r", "a", "o", "$", "h", "n", "n", "c", "p", "$", "e", "y", "a", "W", "t", "J", "p", "e", "K", "r", "n", "a", "ht", "s", "i", "h", "c", "E", "_", "P", "D", "x", "o", "Z", "$", "rt", "Q", "O", "d", "u", "m", "l", "tt", "et", "st", "it", "A", "b", "w", "S", "j", "g", "v", "M", "C", "f", "R", "z", "i", "e", "r", "t", "s", "l", "e", "r", "t", "E", "I", "r", "n", "o", "t", "e", "n", "r", "e", "E", "F", "A", "C", "e", "r", "t", "s", "i", "a", "N", "i", "v", "s", "n", "I", "T", "O", "c", "v", "f", "t", "r", "e", "h", "r", "i", "a", "l", "o", "x", "P", "e", "o", "r", "l", "E", "t", "i", "h", "c", "A", "a", "_", "O", "s", "d", "r", "t", "e", "a", "h", "P", "u", "e", "f", "O", "i", "t", "S", "r", "d", "h", "E", "c", "t", "o", "e", "r", "s", "t", "e", "s", "t", "i", "l", "s", "t", "h", "e", "n", "r", "o", "t", "h", "l", "s", "r", "e", "m", "s", "r", "n", "s", "a", "t", "e", "o", "p", "r", "E", "F", "c", "i", "H", "e", "t", "r", "a", "o", "n", "u", "c", "i", "I", "s", "z", "x", "l", "h", "f", "d", "m", "g", "W", "p", "L", "N", "R", "M", "_", "j", "S", "G", "T", "V", "R", "v", "e", "G", "s", "l", "r", "i", "t", "n", "H", "a", "o", "E", "I", "C", "y", "d", "U", "m", "e", "o", "E", "l", "R", "_", "t", "d", "r", "t", "he", "v", "e", "t", "l", "i", "$", "n", "r", "u", "K", "P", "w", "p", "a", "pe", "E", "o", "R", "s", "h", "T", "C", "d", "_", "H", "le", "c", "f", "m", "y", "ce", "n", "t", "c", "o", "r", "u", "n", "t", "y", "a", "r", "b", "n", "j", "d", "U", "V", "v", "gt", "t", "x", "C", "e", "s", "O", "F", "G", "k", "E", "q", "X", "r", "z", "B", "D", "Z", "H", "J", "K", "Q", "W", "Y", "$", "_", "tt", "nt", "o", "h", "l", "u", "c", "a", "f", "p", "i", "I", "w", "A", "M", "g", "m", "et", "st", "ot", "lt", "t", "n", "e", "s", "o", "r", "h", "l", "u", "c", "f", "p", "O", "i", "I", "w", "M", "g", "A", "m", "N", "y", "a", "T", "L", "b", "v", "ut", "z", "j", "d", "U", "V", "x", "C", "F", "G", "k", "E", "ct", "at", "q", "X", "B", "D", "Z", "wt", "Mt", "H", "J", "_", "K", "Q", "W", "Y", "$", "ft", "pt", "it", "h", "r", "t", "n", "e", "s", "i", "l", "O", "j", "e", "t", "r", "o", "E", "i", "a", "f", "h", "c", "A", "l", "_", "y", "p", "s", "h", "n", "o", "r", "f", "e", "d", "p", "t", "S", "r", "h", "E", "m", "v", "j", "u", "l", "n", "d", "s", "h", "n", "t", "r", "o", "a", "l", "b", "t", "e", "n", "o", "r", "i", "g", "a", "s", "u", "E", "O", "A", "I", "R", "w", "j", "v", "D", "l", "c", "_", "p", "h", "y", "S", "H", "p", "o", "e", "i", "l", "f", "c", "m", "u", "g", "n", "y", "a", "r", "s", "ce", "t", "f", "e", "h", "t", "r", "o", "E", "i", "l", "R", "_", "a", "v", "r", "s", "h", "k", "e", "q", "h", "G", "r", "t", "E", "s", "i", "d", "l", "g", "O", "_", "P", "re", "a", "y", "o", "se", "ie", "j", "oe", "ae", "ne", "ce", "v", "R", "H", "Q", "V", "J", "K", "Z", "Y", "u", "M", "b", "ee", "$", "N", "te", "z", "n", "c", "p", "f", "m", "W", "I", "F", "S", "A", "T", "x", "n", "r", "t", "t", "s", "b", "e", "t", "i", "r", "l", "o", "E", "s", "j", "h", "a", "P", "A", "_", "V", "R", "a", "s", "h", "p", "o", "r", "$", "h", "e", "a", "t", "p", "E", "r", "i", "n", "o", "c", "l", "S", "v", "ue", "re", "A", "O", "P", "x", "_", "s", "y", "I", "T", "H", "C", "ne", "me", "z", "oe", "de", "ie", "ce", "f", "se", "fe", "pe", "u", "m", "d", "g", "he", "le", "N", "ee", "te", "b", "ae"]}