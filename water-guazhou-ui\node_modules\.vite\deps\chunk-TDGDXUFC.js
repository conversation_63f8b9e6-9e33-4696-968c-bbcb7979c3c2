import {
  e2 as e3,
  o2,
  o3
} from "./chunk-TOYJMVHA.js";
import {
  e as e2
} from "./chunk-L4Y6W6Y5.js";
import {
  e
} from "./chunk-32BGXH4N.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/shading/VisualVariables.glsl.js
function s(s2, n) {
  n.hasVvInstancing && (n.vvSize || n.vvColor) && s2.attributes.add(O.INSTANCEFEATUREATTRIBUTE, "vec4");
  const l = s2.vertex;
  n.vvSize ? (l.uniforms.add(new e("vvSizeMinSize", (e4) => e4.vvSizeMinSize)), l.uniforms.add(new e("vvSizeMaxSize", (e4) => e4.vvSizeMaxSize)), l.uniforms.add(new e("vvSizeOffset", (e4) => e4.vvSizeOffset)), l.uniforms.add(new e("vvSizeFactor", (e4) => e4.vvSizeFactor)), l.uniforms.add(new e2("vvSymbolRotationMatrix", (e4) => e4.vvSymbolRotationMatrix)), l.uniforms.add(new e("vvSymbolAnchor", (e4) => e4.vvSymbolAnchor)), l.code.add(o`vec3 vvScale(vec4 _featureAttribute) {
return clamp(vvSizeOffset + _featureAttribute.x * vvSizeFactor, vvSizeMinSize, vvSizeMaxSize);
}
vec4 vvTransformPosition(vec3 position, vec4 _featureAttribute) {
return vec4(vvSymbolRotationMatrix * ( vvScale(_featureAttribute) * (position + vvSymbolAnchor)), 1.0);
}`), l.code.add(o`
      const float eps = 1.192092896e-07;
      vec4 vvTransformNormal(vec3 _normal, vec4 _featureAttribute) {
        vec3 vvScale = clamp(vvSizeOffset + _featureAttribute.x * vvSizeFactor, vvSizeMinSize + eps, vvSizeMaxSize);
        return vec4(vvSymbolRotationMatrix * _normal / vvScale, 1.0);
      }

      ${n.hasVvInstancing ? o`
      vec4 vvLocalNormal(vec3 _normal) {
        return vvTransformNormal(_normal, instanceFeatureAttribute);
      }

      vec4 localPosition() {
        return vvTransformPosition(position, instanceFeatureAttribute);
      }` : ""}
    `)) : l.code.add(o`vec4 localPosition() { return vec4(position, 1.0); }
vec4 vvLocalNormal(vec3 _normal) { return vec4(_normal, 1.0); }`), n.vvColor ? (l.constants.add("vvColorNumber", "int", o3), n.hasVvInstancing && l.uniforms.add([new o2("vvColorValues", (e4) => e4.vvColorValues, o3), new e3("vvColorColors", (e4) => e4.vvColorColors, o3)]), l.code.add(o`
      vec4 vvGetColor(vec4 featureAttribute, float values[vvColorNumber], vec4 colors[vvColorNumber]) {
        float value = featureAttribute.y;
        if (value <= values[0]) {
          return colors[0];
        }

        for (int i = 1; i < vvColorNumber; ++i) {
          if (values[i] >= value) {
            float f = (value - values[i-1]) / (values[i] - values[i-1]);
            return mix(colors[i-1], colors[i], f);
          }
        }
        return colors[vvColorNumber - 1];
      }

      ${n.hasVvInstancing ? o`
      vec4 vvColor() {
        return vvGetColor(instanceFeatureAttribute, vvColorValues, vvColorColors);
      }` : ""}
    `)) : l.code.add(o`vec4 vvColor() { return vec4(1.0); }`);
}

export {
  s
};
//# sourceMappingURL=chunk-TDGDXUFC.js.map
