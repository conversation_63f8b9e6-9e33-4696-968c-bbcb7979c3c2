import {
  u
} from "./chunk-IKGI4J4I.js";
import {
  n
} from "./chunk-2CM7MIII.js";
import {
  f
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  F
} from "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/workers/WorkerHandle.js
var h = class {
  constructor(e, t, s2, i, h2 = {}) {
    this._mainMethod = t, this._transferLists = s2, this._listeners = [], this._promise = u(e, { ...h2, schedule: i }).then((e2) => {
      if (void 0 === this._thread) {
        this._thread = e2, this._promise = null, h2.hasInitialize && this.broadcast({}, "initialize");
        for (const e3 of this._listeners) this._connectListener(e3);
      } else e2.close();
    }), this._promise.catch((t2) => s.getLogger("esri.core.workers.WorkerHandle").error(`Failed to initialize ${e} worker: ${t2}`));
  }
  on(r2, i) {
    const o = { removed: false, eventName: r2, callback: i, threadHandle: null };
    return this._listeners.push(o), this._connectListener(o), n(() => {
      o.removed = true, F(this._listeners, o), this._thread && r(o.threadHandle) && o.threadHandle.remove();
    });
  }
  destroy() {
    this._thread && (this._thread.close(), this._thread = null), this._promise = null;
  }
  invoke(e, t) {
    return this.invokeMethod(this._mainMethod, e, t);
  }
  invokeMethod(e, t, r2) {
    if (this._thread) {
      const s2 = this._transferLists[e], i = s2 ? s2(t) : [];
      return this._thread.invoke(e, t, { transferList: i, signal: r2 });
    }
    return this._promise ? this._promise.then(() => (f(r2), this.invokeMethod(e, t, r2))) : Promise.reject(null);
  }
  broadcast(e, t) {
    return this._thread ? Promise.all(this._thread.broadcast(t, e)).then(() => {
    }) : this._promise ? this._promise.then(() => this.broadcast(e, t)) : Promise.reject();
  }
  get promise() {
    return this._promise;
  }
  _connectListener(e) {
    this._thread && this._thread.on(e.eventName, e.callback).then((t) => {
      e.removed || (e.threadHandle = t);
    });
  }
};

export {
  h
};
//# sourceMappingURL=chunk-UGCLDPAC.js.map
