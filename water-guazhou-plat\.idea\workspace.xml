<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="143454f2-f002-4f9c-9c7f-b3073c35c581" name="Changes" comment="涵养水位功能">
      <change beforePath="$PROJECT_DIR$/application/src/main/resources/mapper/conservationWaterLevel/ConservationAnalysisMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/application/src/main/resources/mapper/conservationWaterLevel/ConservationAnalysisMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dao/src/main/java/org/thingsboard/server/dao/waterSource/impl/ConservationWaterLevelServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/dao/src/main/java/org/thingsboard/server/dao/waterSource/impl/ConservationWaterLevelServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/action/TbMsgTelemetryAttributeAlarmV2Node.java" beforeDir="false" afterPath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/action/TbMsgTelemetryAttributeAlarmV2Node.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/telemetry/TbMsgInfluxDBNode.java" beforeDir="false" afterPath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/telemetry/TbMsgInfluxDBNode.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/telemetry/TbMsgTelemetryAttributeFilterNode.java" beforeDir="false" afterPath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/telemetry/TbMsgTelemetryAttributeFilterNode.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="module" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="FxmlFile" />
        <option value="Enum" />
        <option value="Class" />
        <option value="Interface" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="SourceCode_shenlong" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/DeviceTypeController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/shuiwu/IStarCommonController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/waterSource/inspection/CircuitConfigController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/workOrder/NewlyWorkOrderController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/workOrder/WorkOrderEmergencyLevelController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/workOrder/WorkOrderProcessLevelController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/workOrder/WorkOrderTypeController.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\apache-maven-3.6.3\.m2" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2v4uVyqaLHI8jeeXXux4cEVwQZf" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ApiPost:METDOD_SEND_RECORD:application&quot;: &quot;{\&quot;/api/spp/dma/partition/monitor\&quot;:[{\&quot;url\&quot;:\&quot;http://localhost:8080/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8080/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:6.0,\&quot;second\&quot;:15.0,\&quot;nano\&quot;:4.860454E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8764/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8764/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:6.0,\&quot;second\&quot;:48.0,\&quot;nano\&quot;:9.56709E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:07:28 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1681\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 01 Apr 2025 10:07:28 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:7.0,\&quot;second\&quot;:28.0,\&quot;nano\&quot;:5.711763E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:08:17 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1681\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 01 Apr 2025 10:08:17 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:8.0,\&quot;second\&quot;:17.0,\&quot;nano\&quot;:4.711634E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:12:05 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1681\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 01 Apr 2025 10:12:05 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:12.0,\&quot;second\&quot;:5.0,\&quot;nano\&quot;:3.091777E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:19:27 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1681\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 01 Apr 2025 10:19:27 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:19.0,\&quot;second\&quot;:27.0,\&quot;nano\&quot;:4.641812E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:20:31 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1681\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 01 Apr 2025 10:20:31 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:20.0,\&quot;second\&quot;:31.0,\&quot;nano\&quot;:9.073006E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:22:31 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1681\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 01 Apr 2025 10:22:32 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:22.0,\&quot;second\&quot;:32.0,\&quot;nano\&quot;:3.36282E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:23:26 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1681\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 01 Apr 2025 10:23:26 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:23.0,\&quot;second\&quot;:26.0,\&quot;nano\&quot;:3.659017E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/spp/dma/partition/monitor\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0MzQ5MzcyNiwiZXhwIjoxODEwMTYwMzkyfQ.uM-5QCBwM34CIN6AOVXQEQrZXIpVJNU2KmAiXohLt-o-AVn5q620r5eubEF4u_CCVqVIU5G5PegM6Rngsrq6lQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;partitionId\&quot;,\&quot;value\&quot;:\&quot;cd9eaf5bdeb79ecb9af409122f6e3483\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/spp/dma/partition/monitor?partitionId\\u003dcd9eaf5bdeb79ecb9af409122f6e3483\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eTue Apr 01 18:26:30 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e\\r\\n### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\r\\n### The error may exist in file [D:\\\\Code-Yanfayun\\\\water\\\\water-guazhou-plat\\\\application\\\\target\\\\classes\\\\mapper\\\\smartPipe\\\\PipePartitionTotalFlowMapper.xml]\\r\\n### The error may involve defaultParameterMap\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select tpp.id as id, tpp.name as name, ifnull(sum(origin_water), 0) as total, ifnull(sum(value), 0) \\u0026quot;correctWater\\u0026quot;         from tb_pipe_partition tpp         left join tb_pipe_partition_mount tpm on tpm.partition_id \\u003d tpp.id         left join             (select origin_water, device_id, value  from tb_pipe_partition_total_flow a                 where                 a.type \\u003d ?                 and a.collect_time \\u0026gt;\\u003d to_timestamp(? / 1000) and a.collect_time \\u0026lt; to_timestamp(? / 1000)) a on a.device_id \\u003d tpm.device_id         where tpp.id in                                                                     and tpm.direction !\\u003d \\u0026#39;4\\u0026#39;                                                                         group by tpp.id, tpp.name\\r\\n### Cause: org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\n; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: syntax error at or near \\u0026quot;and\\u0026quot;\\n  ???615\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1681\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 01 Apr 2025 10:26:30 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:1.0},\&quot;time\&quot;:{\&quot;hour\&quot;:18.0,\&quot;minute\&quot;:26.0,\&quot;second\&quot;:30.0,\&quot;nano\&quot;:4.323167E8}}}],\&quot;/api/assay/\&quot;:[{\&quot;url\&quot;:\&quot;http://localhost:8080/api/assay/\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;json\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;{\\n    \\\&quot;id\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;tenantId\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;creator\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;reportName\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;samplingLocation\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testingUnit\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testResults\\\&quot;: \\\&quot;合格\\\&quot;,\\n    \\\&quot;testDate\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;reportFile\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;createTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;updateTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;remark\\\&quot;: \\\&quot;\\\&quot;\\n}\&quot;,\&quot;raw_para\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;主键ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;租户ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建人ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportName\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告名称\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;samplingLocation\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;采样地点\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testingUnit\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测单位\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testResults\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测结果\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testDate\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测日期\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportFile\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;createTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;updateTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;更新时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;remark\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;备注\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8080/api/assay/\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:10.0,\&quot;second\&quot;:47.0,\&quot;nano\&quot;:5.282469E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;json\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;{\\n    \\\&quot;id\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;tenantId\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;creator\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;reportName\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;samplingLocation\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testingUnit\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testResults\\\&quot;: \\\&quot;合格\\\&quot;,\\n    \\\&quot;testDate\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;reportFile\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;createTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;updateTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;remark\\\&quot;: \\\&quot;\\\&quot;\\n}\&quot;,\&quot;raw_para\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;主键ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;租户ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建人ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportName\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告名称\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;samplingLocation\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;采样地点\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testingUnit\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测单位\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testResults\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测结果\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testDate\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测日期\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportFile\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;createTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;updateTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;更新时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;remark\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;备注\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/\\n\\n{\\\&quot;status\\\&quot;:401,\\\&quot;message\\\&quot;:\\\&quot;Authorization header cannot be blank!\\\&quot;,\\\&quot;errorCode\\\&quot;:10,\\\&quot;timestamp\\\&quot;:1744013457605}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 401\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;105\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:10:57 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:10.0,\&quot;second\&quot;:57.0,\&quot;nano\&quot;:6.245635E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;json\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;{\\n    \\\&quot;id\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;tenantId\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;creator\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;reportName\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;samplingLocation\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testingUnit\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testResults\\\&quot;: \\\&quot;合格\\\&quot;,\\n    \\\&quot;testDate\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;reportFile\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;createTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;updateTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;remark\\\&quot;: \\\&quot;\\\&quot;\\n}\&quot;,\&quot;raw_para\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;主键ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;租户ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建人ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportName\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告名称\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;samplingLocation\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;采样地点\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testingUnit\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测单位\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testResults\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测结果\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testDate\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测日期\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportFile\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;createTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;updateTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;更新时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;remark\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;备注\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/\\n\\n{\\\&quot;status\\\&quot;:401,\\\&quot;message\\\&quot;:\\\&quot;Authorization header cannot be blank!\\\&quot;,\\\&quot;errorCode\\\&quot;:10,\\\&quot;timestamp\\\&quot;:1744013486535}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 401\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;105\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:11:26 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:11.0,\&quot;second\&quot;:26.0,\&quot;nano\&quot;:5.488352E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;json\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;{\\n    \\\&quot;id\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;tenantId\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;creator\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;reportName\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;samplingLocation\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testingUnit\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testResults\\\&quot;: \\\&quot;合格\\\&quot;,\\n    \\\&quot;testDate\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;reportFile\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;createTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;updateTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;remark\\\&quot;: \\\&quot;\\\&quot;\\n}\&quot;,\&quot;raw_para\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;主键ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;租户ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建人ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportName\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告名称\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;samplingLocation\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;采样地点\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testingUnit\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测单位\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testResults\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测结果\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testDate\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测日期\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportFile\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;createTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;updateTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;更新时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;remark\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;备注\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eMon Apr 07 16:11:39 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dBad Request, status\\u003d400).\\u003c/div\\u003e\\u003cdiv\\u003eCould not read document: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 9, column: 17] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;testDate\\u0026quot;]); nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 9, column: 17] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;testDate\\u0026quot;])\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 400\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1394\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:11:39 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:11.0,\&quot;second\&quot;:39.0,\&quot;nano\&quot;:2.777018E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;json\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;{\\n    \\\&quot;id\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;tenantId\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;creator\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;reportName\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;samplingLocation\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testingUnit\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testResults\\\&quot;: \\\&quot;合格\\\&quot;,\\n    \\\&quot;testDate\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;reportFile\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;createTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;updateTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;remark\\\&quot;: \\\&quot;\\\&quot;\\n}\&quot;,\&quot;raw_para\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;主键ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;租户ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建人ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportName\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告名称\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;samplingLocation\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;采样地点\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testingUnit\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测单位\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testResults\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测结果\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testDate\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测日期\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportFile\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;createTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;updateTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;更新时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;remark\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;备注\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eMon Apr 07 16:11:52 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dBad Request, status\\u003d400).\\u003c/div\\u003e\\u003cdiv\\u003eCould not read document: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 9, column: 17] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;testDate\\u0026quot;]); nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 9, column: 17] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;testDate\\u0026quot;])\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 400\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1394\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:11:52 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:11.0,\&quot;second\&quot;:52.0,\&quot;nano\&quot;:2.444029E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;json\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;{\\n    \\\&quot;id\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;tenantId\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;creator\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;reportName\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;samplingLocation\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testingUnit\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testResults\\\&quot;: \\\&quot;合格\\\&quot;,\\n    \\\&quot;testDate\\\&quot;: \\\&quot;2025-04-07\\\&quot;,\\n    \\\&quot;reportFile\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;createTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;updateTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;remark\\\&quot;: \\\&quot;\\\&quot;\\n}\&quot;,\&quot;raw_para\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;主键ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;租户ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建人ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportName\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告名称\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;samplingLocation\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;采样地点\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testingUnit\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测单位\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testResults\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测结果\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testDate\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测日期\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportFile\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;createTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;updateTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;更新时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;remark\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;备注\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eMon Apr 07 16:15:31 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dBad Request, status\\u003d400).\\u003c/div\\u003e\\u003cdiv\\u003eCould not read document: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 11, column: 19] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;createTime\\u0026quot;]); nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 11, column: 19] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;createTime\\u0026quot;])\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 400\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1400\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:15:31 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:15.0,\&quot;second\&quot;:31.0,\&quot;nano\&quot;:3.33629E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;json\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;{\\n    \\\&quot;id\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;tenantId\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;creator\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;reportName\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;samplingLocation\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testingUnit\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testResults\\\&quot;: \\\&quot;合格\\\&quot;,\\n    \\\&quot;testDate\\\&quot;: \\\&quot;2025-04-07\\\&quot;,\\n    \\\&quot;reportFile\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;createTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;updateTime\\\&quot;: \\\&quot;2025-04-07 16:10:04\\\&quot;,\\n    \\\&quot;remark\\\&quot;: \\\&quot;\\\&quot;\\n}\&quot;,\&quot;raw_para\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;主键ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;租户ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建人ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportName\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告名称\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;samplingLocation\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;采样地点\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testingUnit\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测单位\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testResults\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测结果\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testDate\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测日期\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportFile\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;createTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;updateTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;更新时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;remark\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;备注\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eMon Apr 07 16:18:28 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dBad Request, status\\u003d400).\\u003c/div\\u003e\\u003cdiv\\u003eCould not read document: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 11, column: 19] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;createTime\\u0026quot;]); nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String \\u0026quot;2025-04-07 16:10:04\\u0026quot;: not a valid representation (error: Failed to parse Date value \\u0026#39;2025-04-07 16:10:04\\u0026#39;: Cannot parse date \\u0026quot;2025-04-07 16:10:04\\u0026quot;: while it seems to fit format \\u0026#39;yyyy-MM-dd\\u0026#39;T\\u0026#39;HH:mm:ss.SSSZ\\u0026#39;, parsing fails (leniency? null))\\n at [Source: (PushbackInputStream); line: 11, column: 19] (through reference chain: org.thingsboard.server.dao.model.sql.assay.Assay[\\u0026quot;createTime\\u0026quot;])\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 400\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;1400\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:18:28 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:18.0,\&quot;second\&quot;:28.0,\&quot;nano\&quot;:7.4076E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;json\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;{\\n    \\\&quot;id\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;tenantId\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;creator\\\&quot;: \\\&quot;123456\\\&quot;,\\n    \\\&quot;reportName\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;samplingLocation\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testingUnit\\\&quot;: \\\&quot;111\\\&quot;,\\n    \\\&quot;testResults\\\&quot;: \\\&quot;合格\\\&quot;,\\n    \\\&quot;testDate\\\&quot;: \\\&quot;2025-04-07\\\&quot;,\\n    \\\&quot;reportFile\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;remark\\\&quot;: \\\&quot;\\\&quot;\\n}\&quot;,\&quot;raw_para\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;主键ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;租户ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建人ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportName\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告名称\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;samplingLocation\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;采样地点\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testingUnit\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测单位\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testResults\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测结果\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testDate\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测日期\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportFile\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;createTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;updateTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 16:10:04\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;更新时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;remark\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;备注\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744015459776,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:44:19 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:44.0,\&quot;second\&quot;:19.0,\&quot;nano\&quot;:8.313248E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8080/api/assay/\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;json\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;{\\n    \\\&quot;id\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;tenantId\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;creator\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;reportName\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;samplingLocation\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;testingUnit\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;testResults\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;testDate\\\&quot;: \\\&quot;2025-04-07 17:48:57\\\&quot;,\\n    \\\&quot;reportFile\\\&quot;: \\\&quot;\\\&quot;,\\n    \\\&quot;createTime\\\&quot;: \\\&quot;2025-04-07 17:48:57\\\&quot;,\\n    \\\&quot;updateTime\\\&quot;: \\\&quot;2025-04-07 17:48:57\\\&quot;,\\n    \\\&quot;remark\\\&quot;: \\\&quot;\\\&quot;\\n}\&quot;,\&quot;raw_para\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;主键ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;租户ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建人ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportName\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告名称\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;samplingLocation\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;采样地点\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testingUnit\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测单位\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testResults\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测结果\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;testDate\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 17:48:57\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;检测日期\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;reportFile\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;createTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 17:48:57\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;创建时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;updateTime\&quot;,\&quot;value\&quot;:\&quot;2025-04-07 17:48:57\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;更新时间\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;remark\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;备注\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8080/api/assay/\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:49.0,\&quot;second\&quot;:6.0,\&quot;nano\&quot;:5.18818E8}}}],\&quot;/api/assay/{id}/upload\&quot;:[{\&quot;url\&quot;:\&quot;http://localhost:8080/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8080/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:45.0,\&quot;second\&quot;:39.0,\&quot;nano\&quot;:2.857617E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\\n\\n{\\\&quot;status\\\&quot;:401,\\\&quot;message\\\&quot;:\\\&quot;Authorization header cannot be blank!\\\&quot;,\\\&quot;errorCode\\\&quot;:10,\\\&quot;timestamp\\\&quot;:1744015548548}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 401\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;105\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:45:48 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:45.0,\&quot;second\&quot;:48.0,\&quot;nano\&quot;:5.586466E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:null,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744015581387,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:46:21 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:46.0,\&quot;second\&quot;:21.0,\&quot;nano\&quot;:4.256226E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:null,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744015582280,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:46:21 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:46.0,\&quot;second\&quot;:22.0,\&quot;nano\&quot;:3.087135E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:49.0,\&quot;second\&quot;:0.0,\&quot;nano\&quot;:4.986993E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:null,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744015778287,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:49:38 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:49.0,\&quot;second\&quot;:38.0,\&quot;nano\&quot;:4.32405E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:null,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744015819023,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:50:19 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:50.0,\&quot;second\&quot;:19.0,\&quot;nano\&quot;:7.69261E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:null,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744015854397,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:50:54 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:50.0,\&quot;second\&quot;:54.0,\&quot;nano\&quot;:4.337647E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:\\\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\\\&quot;,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744016031302,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:53:51 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:53.0,\&quot;second\&quot;:51.0,\&quot;nano\&quot;:3.652453E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:\\\&quot;http://***********:9090/istar/cb6aad30-ba34-4165-9f95-9c3419e861f5\\\&quot;,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744016039013,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:53:59 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:53.0,\&quot;second\&quot;:59.0,\&quot;nano\&quot;:5.82862E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:\\\&quot;http://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\\\&quot;,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744016268102,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 08:57:48 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:57.0,\&quot;second\&quot;:48.0,\&quot;nano\&quot;:1.919829E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\\n\\n{\\\&quot;status\\\&quot;:401,\\\&quot;message\\\&quot;:\\\&quot;Authorization header cannot be blank!\\\&quot;,\\\&quot;errorCode\\\&quot;:10,\\\&quot;timestamp\\\&quot;:1744073823786}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 401\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;105\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 08 Apr 2025 00:57:03 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:8.0},\&quot;time\&quot;:{\&quot;hour\&quot;:8.0,\&quot;minute\&quot;:57.0,\&quot;second\&quot;:3.0,\&quot;nano\&quot;:7.961515E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/3f45c1d8931b0e9a0b8cb7f66ea1dccd/upload\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[{\&quot;key\&quot;:\&quot;id\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;化验记录ID\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;form-data\&quot;,\&quot;parameter\&quot;:[{\&quot;key\&quot;:\&quot;file\&quot;,\&quot;value\&quot;:\&quot;C:/Users/<USER>/Desktop/壁纸/1.jpg\&quot;,\&quot;type\&quot;:\&quot;File\&quot;,\&quot;description\&quot;:\&quot;报告文件\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;File\&quot;}],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:\\\&quot;http://***********:9090/istar/95b93961-8265-449d-badd-20c56231e381\\\&quot;,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744073851338,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 08 Apr 2025 00:57:31 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;POST\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:8.0},\&quot;time\&quot;:{\&quot;hour\&quot;:8.0,\&quot;minute\&quot;:57.0,\&quot;second\&quot;:31.0,\&quot;nano\&quot;:3.767485E8}}}],\&quot;/api/assay/test\&quot;:[{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:36:02 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:36.0,\&quot;second\&quot;:2.0,\&quot;nano\&quot;:3.230354E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:36:07 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:36.0,\&quot;second\&quot;:7.0,\&quot;nano\&quot;:6.00545E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:40:56 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:40.0,\&quot;second\&quot;:56.0,\&quot;nano\&quot;:9.973964E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:41:39 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:41.0,\&quot;second\&quot;:39.0,\&quot;nano\&quot;:4.478667E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:41:58 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:41.0,\&quot;second\&quot;:58.0,\&quot;nano\&quot;:9.4836E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:41:59 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:41.0,\&quot;second\&quot;:59.0,\&quot;nano\&quot;:6.096745E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:42:01 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:42.0,\&quot;second\&quot;:1.0,\&quot;nano\&quot;:4.821336E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:46:04 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:46.0,\&quot;second\&quot;:4.0,\&quot;nano\&quot;:6.868472E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:46:06 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:46.0,\&quot;second\&quot;:6.0,\&quot;nano\&quot;:7.244202E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:46:07 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:46.0,\&quot;second\&quot;:7.0,\&quot;nano\&quot;:7.937135E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:46:23 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:46.0,\&quot;second\&quot;:23.0,\&quot;nano\&quot;:4.453793E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:46:24 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:46.0,\&quot;second\&quot;:24.0,\&quot;nano\&quot;:5.790009E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:48:08 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:48.0,\&quot;second\&quot;:8.0,\&quot;nano\&quot;:7.498019E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:48:09 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:48.0,\&quot;second\&quot;:9.0,\&quot;nano\&quot;:7.780212E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/eb483820-6767-4c0e-b85f-922b58c468dc\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:48:13 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:48.0,\&quot;second\&quot;:13.0,\&quot;nano\&quot;:2.634036E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8080/api/assay/test\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8080/api/assay/test?name\\u003dhttp://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:49.0,\&quot;second\&quot;:37.0,\&quot;nano\&quot;:7.887934E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/test?name\\u003dhttp://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\\n\\n{\\\&quot;status\\\&quot;:401,\\\&quot;message\\\&quot;:\\\&quot;Authorization header cannot be blank!\\\&quot;,\\\&quot;errorCode\\\&quot;:10,\\\&quot;timestamp\\\&quot;:1744019382869}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 401\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;105\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:49:42 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:49.0,\&quot;second\&quot;:42.0,\&quot;nano\&quot;:8.862905E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/4d8e252e-7a2a-4970-a4cf-058a2d011153\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:50:06 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:50.0,\&quot;second\&quot;:6.0,\&quot;nano\&quot;:8.781588E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/cb6aad30-ba34-4165-9f95-9c3419e861f5\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 07 Apr 2025 09:50:37 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:7.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:50.0,\&quot;second\&quot;:37.0,\&quot;nano\&quot;:9.245975E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/test\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0Mzk5MzE2MiwiZXhwIjoxODEwNjU5ODI4fQ.GIrz25Uxj56-kKDvmpYTdebySoWf7XJo8Zwh1webSCFGE2OjnM6s2VF9pm28ZJl_wWs-0XjpmegmLl80S4HGMQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;name\&quot;,\&quot;value\&quot;:\&quot;http://***********:9090/istar/cb6aad30-ba34-4165-9f95-9c3419e861f5\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:null}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Tue, 08 Apr 2025 00:38:33 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;DELETE\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:8.0},\&quot;time\&quot;:{\&quot;hour\&quot;:8.0,\&quot;minute\&quot;:38.0,\&quot;second\&quot;:33.0,\&quot;nano\&quot;:9.859309E8}}}],\&quot;/api/assay/list\&quot;:[{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/list\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0NDI3MTkzMiwiZXhwIjoxODEwOTM4NTk4fQ.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;KEY\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/list?KEY\\u003d\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:34:56 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;308\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 10 Apr 2025 09:34:56 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:10.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:34.0,\&quot;second\&quot;:56.0,\&quot;nano\&quot;:1.90743E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/list\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0NDI3MTkzMiwiZXhwIjoxODEwOTM4NTk4fQ.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;KEY\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/list?KEY\\u003d\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:35:19 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;308\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 10 Apr 2025 09:35:19 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:10.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:35.0,\&quot;second\&quot;:19.0,\&quot;nano\&quot;:6.77552E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8764/api/assay/list\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0NDI3MTkzMiwiZXhwIjoxODEwOTM4NTk4fQ.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;KEY\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8764/api/assay/list?KEY\\u003d\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:10.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:35.0,\&quot;second\&quot;:33.0,\&quot;nano\&quot;:9.944442E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/list\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0NDI3MTkzMiwiZXhwIjoxODEwOTM4NTk4fQ.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;KEY\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/list?KEY\\u003d\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:35:40 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;308\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 10 Apr 2025 09:35:40 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:10.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:35.0,\&quot;second\&quot;:40.0,\&quot;nano\&quot;:3.765488E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/list\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0NDI3MTkzMiwiZXhwIjoxODEwOTM4NTk4fQ.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;KEY\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/list\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:36:17 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;308\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 10 Apr 2025 09:36:17 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:10.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:36.0,\&quot;second\&quot;:17.0,\&quot;nano\&quot;:1.799249E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/list\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0NDI3MTkzMiwiZXhwIjoxODEwOTM4NTk4fQ.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;KEY\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/list\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:36:30 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;308\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 10 Apr 2025 09:36:30 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:10.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:36.0,\&quot;second\&quot;:30.0,\&quot;nano\&quot;:2.550196E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/list\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0NDI3MTkzMiwiZXhwIjoxODEwOTM4NTk4fQ.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;KEY\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/assay/list\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Apr 10 17:42:20 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003e??????????\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;308\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 10 Apr 2025 09:42:20 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:10.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:42.0,\&quot;second\&quot;:20.0,\&quot;nano\&quot;:9.45405E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/assay/list\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0NDI3MTkzMiwiZXhwIjoxODEwOTM4NTk4fQ.Dq5EBGYzbUeAsKMVRAud-9Hq39ETs_gG5RvzXV36b3quYY4PzKq5q6hUAlFI7ELF-fZwdI2C7yOkzAlLIWQuLA\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;KEY\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;total\\\&quot;:1,\\\&quot;data\\\&quot;:[{\\\&quot;id\\\&quot;:\\\&quot;3f45c1d8931b0e9a0b8cb7f66ea1dccd\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;e3d7b200-79ec-11ed-9c4b-6f2427406ab1\\\&quot;,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;reportName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;samplingLocation\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testingUnit\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;testResults\\\&quot;:\\\&quot;合格\\\&quot;,\\\&quot;testDate\\\&quot;:1743984000000,\\\&quot;reportFile\\\&quot;:\\\&quot;http://***********:9090/istar/256ed866-6204-4165-83ef-db4f164c5c29\\\&quot;,\\\&quot;createTime\\\&quot;:1744015459776,\\\&quot;updateTime\\\&quot;:1744073871274,\\\&quot;remark\\\&quot;:\\\&quot;\\\&quot;}]}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 10 Apr 2025 09:47:06 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:10.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:47.0,\&quot;second\&quot;:6.0,\&quot;nano\&quot;:6.989981E8}}}],\&quot;/api/sm/circuitTask/\&quot;:[{\&quot;url\&quot;:\&quot;http://localhost:8080/api/sm/circuitTask/\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;isNormalPlan\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Boolean\&quot;},{\&quot;key\&quot;:\&quot;isReceived\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Boolean\&quot;},{\&quot;key\&quot;:\&quot;isComplete\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Boolean\&quot;},{\&quot;key\&quot;:\&quot;beginTimeFrom\&quot;,\&quot;value\&quot;:\&quot;2025-04-28 15:50:52\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;beginTimeTo\&quot;,\&quot;value\&quot;:\&quot;2025-04-28 15:50:52\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;receiveUserId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;collaborateUserId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;keyword\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;pageableQueryEntityQueryType\&quot;,\&quot;value\&quot;:\&quot;AUTO\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;AUTO MANUAL\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;records\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;查询数据列表\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Array\&quot;},{\&quot;key\&quot;:\&quot;total\&quot;,\&quot;value\&quot;:\&quot;0\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;总数\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Integer\&quot;},{\&quot;key\&quot;:\&quot;size\&quot;,\&quot;value\&quot;:\&quot;10\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;每页显示条数，默认 10\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Integer\&quot;},{\&quot;key\&quot;:\&quot;page\&quot;,\&quot;value\&quot;:\&quot;1\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;当前页\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Integer\&quot;},{\&quot;key\&quot;:\&quot;orders\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;自定义排序字段信息\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Array\&quot;},{\&quot;key\&quot;:\&quot;orderExpression\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;排序表达式\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;fromTime\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;},{\&quot;key\&quot;:\&quot;toTime\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8080/api/sm/circuitTask/?receiveUserId\\u003d\\u0026size\\u003d10\\u0026isReceived\\u003dtrue\\u0026page\\u003d1\\u0026isComplete\\u003dfalse\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:28.0},\&quot;time\&quot;:{\&quot;hour\&quot;:15.0,\&quot;minute\&quot;:52.0,\&quot;second\&quot;:51.0,\&quot;nano\&quot;:7.499332E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/sm/circuitTask/\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;isNormalPlan\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Boolean\&quot;},{\&quot;key\&quot;:\&quot;isReceived\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Boolean\&quot;},{\&quot;key\&quot;:\&quot;isComplete\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Boolean\&quot;},{\&quot;key\&quot;:\&quot;beginTimeFrom\&quot;,\&quot;value\&quot;:\&quot;2025-04-28 15:50:52\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;beginTimeTo\&quot;,\&quot;value\&quot;:\&quot;2025-04-28 15:50:52\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;receiveUserId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;collaborateUserId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;keyword\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;pageableQueryEntityQueryType\&quot;,\&quot;value\&quot;:\&quot;AUTO\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;AUTO MANUAL\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;records\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;查询数据列表\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Array\&quot;},{\&quot;key\&quot;:\&quot;total\&quot;,\&quot;value\&quot;:\&quot;0\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;总数\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Integer\&quot;},{\&quot;key\&quot;:\&quot;size\&quot;,\&quot;value\&quot;:\&quot;10\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;每页显示条数，默认 10\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Integer\&quot;},{\&quot;key\&quot;:\&quot;page\&quot;,\&quot;value\&quot;:\&quot;1\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;当前页\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Integer\&quot;},{\&quot;key\&quot;:\&quot;orders\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;自定义排序字段信息\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Array\&quot;},{\&quot;key\&quot;:\&quot;orderExpression\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;排序表达式\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;fromTime\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;},{\&quot;key\&quot;:\&quot;toTime\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/sm/circuitTask/?receiveUserId\\u003d\\u0026size\\u003d10\\u0026isReceived\\u003dtrue\\u0026page\\u003d1\\u0026isComplete\\u003dfalse\\n\\n{\\\&quot;status\\\&quot;:401,\\\&quot;message\\\&quot;:\\\&quot;Authorization header cannot be blank!\\\&quot;,\\\&quot;errorCode\\\&quot;:10,\\\&quot;timestamp\\\&quot;:1745826778980}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 401\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;105\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 28 Apr 2025 07:52:58 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:28.0},\&quot;time\&quot;:{\&quot;hour\&quot;:15.0,\&quot;minute\&quot;:52.0,\&quot;second\&quot;:59.0,\&quot;nano\&quot;:3.61414E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/sm/circuitTask/\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtzEyMSIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0NTgwMTM5MywiZXhwIjoxODEyNDY4MDU5fQ.poAUfqWfrEB15YH-lZbpQyu8VANFGDbh4RUbrEDOJAf9UdCj6XpVt0HDjya4jeDhJtXGM0B_OjvBngRVfLTk5A\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;isNormalPlan\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Boolean\&quot;},{\&quot;key\&quot;:\&quot;isReceived\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Boolean\&quot;},{\&quot;key\&quot;:\&quot;isComplete\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Boolean\&quot;},{\&quot;key\&quot;:\&quot;beginTimeFrom\&quot;,\&quot;value\&quot;:\&quot;2025-04-28 15:50:52\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;beginTimeTo\&quot;,\&quot;value\&quot;:\&quot;2025-04-28 15:50:52\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;receiveUserId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;creator\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;collaborateUserId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;keyword\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;pageableQueryEntityQueryType\&quot;,\&quot;value\&quot;:\&quot;AUTO\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;AUTO MANUAL\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;records\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;查询数据列表\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Array\&quot;},{\&quot;key\&quot;:\&quot;total\&quot;,\&quot;value\&quot;:\&quot;0\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;总数\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Integer\&quot;},{\&quot;key\&quot;:\&quot;size\&quot;,\&quot;value\&quot;:\&quot;10\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;每页显示条数，默认 10\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Integer\&quot;},{\&quot;key\&quot;:\&quot;page\&quot;,\&quot;value\&quot;:\&quot;1\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;当前页\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Integer\&quot;},{\&quot;key\&quot;:\&quot;orders\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;自定义排序字段信息\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Array\&quot;},{\&quot;key\&quot;:\&quot;orderExpression\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;排序表达式\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;fromTime\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;},{\&quot;key\&quot;:\&quot;toTime\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;Object\&quot;},{\&quot;key\&quot;:\&quot;tenantId\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:0.0,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;code\\\&quot;:200,\\\&quot;message\\\&quot;:\\\&quot;操作成功!\\\&quot;,\\\&quot;data\\\&quot;:{\\\&quot;total\\\&quot;:5,\\\&quot;data\\\&quot;:[{\\\&quot;id\\\&quot;:\\\&quot;7e714bfd26fffc3b30187022ce42df25\\\&quot;,\\\&quot;code\\\&quot;:\\\&quot;202504270000\\\&quot;,\\\&quot;planId\\\&quot;:\\\&quot;66ba27b36bfd8008fbf62452a181e20d\\\&quot;,\\\&quot;districtAreaId\\\&quot;:\\\&quot;ec541b3ae74f7427371e3b4d35112147\\\&quot;,\\\&quot;districtAreaName\\\&quot;:\\\&quot;天府镇内\\\&quot;,\\\&quot;isNormalPlan\\\&quot;:false,\\\&quot;isNeedFeedback\\\&quot;:false,\\\&quot;moveType\\\&quot;:\\\&quot;步行\\\&quot;,\\\&quot;devices\\\&quot;:\\\&quot;[]\\\&quot;,\\\&quot;specialDevices\\\&quot;:null,\\\&quot;name\\\&quot;:\\\&quot;测试计划任务\\\&quot;,\\\&quot;receiveUserId\\\&quot;:\\\&quot;1efb2b357578ba08b4b5196e50adaf1\\\&quot;,\\\&quot;receiveUserName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;receiveUserDepartmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;receiveUserDepartmentName\\\&quot;:\\\&quot;技术部\\\&quot;,\\\&quot;collaborateUserId\\\&quot;:null,\\\&quot;collaborateUserName\\\&quot;:null,\\\&quot;presentDistance\\\&quot;:null,\\\&quot;remark\\\&quot;:null,\\\&quot;presentState\\\&quot;:\\\&quot;0/2\\\&quot;,\\\&quot;statusName\\\&quot;:\\\&quot;接收\\\&quot;,\\\&quot;status\\\&quot;:\\\&quot;RECEIVED\\\&quot;,\\\&quot;fallbackState\\\&quot;:\\\&quot;0/2\\\&quot;,\\\&quot;keyPointCount\\\&quot;:2,\\\&quot;deviceCount\\\&quot;:0,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;creatorName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;creatorDepartmentId\\\&quot;:null,\\\&quot;creatorDepartmentName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;createTime\\\&quot;:\\\&quot;2025-04-27 14:54:28\\\&quot;,\\\&quot;beginTime\\\&quot;:\\\&quot;2025-04-27 00:00:00\\\&quot;,\\\&quot;endTime\\\&quot;:\\\&quot;2025-04-27 23:59:59\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;auditUserId\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;auditTime\\\&quot;:null,\\\&quot;rejectReason\\\&quot;:\\\&quot;123456\\\&quot;,\\\&quot;planCircle\\\&quot;:\\\&quot;1\\\&quot;,\\\&quot;planCircleName\\\&quot;:\\\&quot;一天一次\\\&quot;},{\\\&quot;id\\\&quot;:\\\&quot;b4bed02f03273a5f33dc645c9a91a1b6\\\&quot;,\\\&quot;code\\\&quot;:\\\&quot;202411050002\\\&quot;,\\\&quot;planId\\\&quot;:\\\&quot;33aef1bd2c2c9a97a1be43df69006765\\\&quot;,\\\&quot;districtAreaId\\\&quot;:\\\&quot;7087ba181b7eba7fe7a2a5f28d979a09\\\&quot;,\\\&quot;districtAreaName\\\&quot;:\\\&quot;天府新谷区域\\\&quot;,\\\&quot;isNormalPlan\\\&quot;:true,\\\&quot;isNeedFeedback\\\&quot;:true,\\\&quot;moveType\\\&quot;:\\\&quot;车巡\\\&quot;,\\\&quot;devices\\\&quot;:\\\&quot;[]\\\&quot;,\\\&quot;specialDevices\\\&quot;:null,\\\&quot;name\\\&quot;:\\\&quot;巡检任务\\\&quot;,\\\&quot;receiveUserId\\\&quot;:\\\&quot;1ef8c5219c236a0a5ee8b9796b1e05c\\\&quot;,\\\&quot;receiveUserName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;receiveUserDepartmentId\\\&quot;:null,\\\&quot;receiveUserDepartmentName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;collaborateUserId\\\&quot;:null,\\\&quot;collaborateUserName\\\&quot;:null,\\\&quot;presentDistance\\\&quot;:\\\&quot;200\\\&quot;,\\\&quot;remark\\\&quot;:null,\\\&quot;presentState\\\&quot;:\\\&quot;2/2\\\&quot;,\\\&quot;statusName\\\&quot;:\\\&quot;接收\\\&quot;,\\\&quot;status\\\&quot;:\\\&quot;RECEIVED\\\&quot;,\\\&quot;fallbackState\\\&quot;:\\\&quot;2/2\\\&quot;,\\\&quot;keyPointCount\\\&quot;:2,\\\&quot;deviceCount\\\&quot;:0,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;creatorName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;creatorDepartmentId\\\&quot;:null,\\\&quot;creatorDepartmentName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;createTime\\\&quot;:\\\&quot;2024-11-05 09:39:38\\\&quot;,\\\&quot;beginTime\\\&quot;:\\\&quot;2024-11-07 00:00:00\\\&quot;,\\\&quot;endTime\\\&quot;:\\\&quot;2024-11-07 23:59:59\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;auditUserId\\\&quot;:null,\\\&quot;auditTime\\\&quot;:null,\\\&quot;rejectReason\\\&quot;:null,\\\&quot;planCircle\\\&quot;:\\\&quot;1\\\&quot;,\\\&quot;planCircleName\\\&quot;:\\\&quot;一天一次\\\&quot;},{\\\&quot;id\\\&quot;:\\\&quot;16fc1ac05fe8d759a43ec851574b3bcf\\\&quot;,\\\&quot;code\\\&quot;:\\\&quot;202308210002\\\&quot;,\\\&quot;planId\\\&quot;:\\\&quot;b4c8d81adc71f5756c63a42f8930e9d4\\\&quot;,\\\&quot;districtAreaId\\\&quot;:\\\&quot;ec541b3ae74f7427371e3b4d35112147\\\&quot;,\\\&quot;districtAreaName\\\&quot;:\\\&quot;天府镇内\\\&quot;,\\\&quot;isNormalPlan\\\&quot;:true,\\\&quot;isNeedFeedback\\\&quot;:false,\\\&quot;moveType\\\&quot;:\\\&quot;车巡\\\&quot;,\\\&quot;devices\\\&quot;:\\\&quot;[]\\\&quot;,\\\&quot;specialDevices\\\&quot;:null,\\\&quot;name\\\&quot;:\\\&quot;测试任务\\\&quot;,\\\&quot;receiveUserId\\\&quot;:\\\&quot;1eed9f9c05cb7d0b3c65d0b7b19ad2b\\\&quot;,\\\&quot;receiveUserName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;receiveUserDepartmentId\\\&quot;:null,\\\&quot;receiveUserDepartmentName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;collaborateUserId\\\&quot;:null,\\\&quot;collaborateUserName\\\&quot;:null,\\\&quot;presentDistance\\\&quot;:\\\&quot;10\\\&quot;,\\\&quot;remark\\\&quot;:null,\\\&quot;presentState\\\&quot;:\\\&quot;0/2\\\&quot;,\\\&quot;statusName\\\&quot;:\\\&quot;接收\\\&quot;,\\\&quot;status\\\&quot;:\\\&quot;RECEIVED\\\&quot;,\\\&quot;fallbackState\\\&quot;:\\\&quot;0/2\\\&quot;,\\\&quot;keyPointCount\\\&quot;:2,\\\&quot;deviceCount\\\&quot;:0,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;creatorName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;creatorDepartmentId\\\&quot;:null,\\\&quot;creatorDepartmentName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;createTime\\\&quot;:\\\&quot;2023-08-21 22:44:45\\\&quot;,\\\&quot;beginTime\\\&quot;:\\\&quot;2023-08-22 00:00:00\\\&quot;,\\\&quot;endTime\\\&quot;:\\\&quot;2023-08-22 11:59:59\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;auditUserId\\\&quot;:null,\\\&quot;auditTime\\\&quot;:null,\\\&quot;rejectReason\\\&quot;:null,\\\&quot;planCircle\\\&quot;:\\\&quot;0\\\&quot;,\\\&quot;planCircleName\\\&quot;:\\\&quot;一天两次\\\&quot;},{\\\&quot;id\\\&quot;:\\\&quot;05e51e4f69fa5ac4e1abf164aa44452a\\\&quot;,\\\&quot;code\\\&quot;:\\\&quot;202306300000\\\&quot;,\\\&quot;planId\\\&quot;:\\\&quot;b4c8d81adc71f5756c63a42f8930e9d4\\\&quot;,\\\&quot;districtAreaId\\\&quot;:\\\&quot;ec541b3ae74f7427371e3b4d35112147\\\&quot;,\\\&quot;districtAreaName\\\&quot;:\\\&quot;天府镇内\\\&quot;,\\\&quot;isNormalPlan\\\&quot;:true,\\\&quot;isNeedFeedback\\\&quot;:false,\\\&quot;moveType\\\&quot;:\\\&quot;车巡\\\&quot;,\\\&quot;devices\\\&quot;:\\\&quot;[]\\\&quot;,\\\&quot;specialDevices\\\&quot;:null,\\\&quot;name\\\&quot;:\\\&quot;巡检\\\&quot;,\\\&quot;receiveUserId\\\&quot;:\\\&quot;1edbc09c1a09e90a461fb299c9b23b7\\\&quot;,\\\&quot;receiveUserName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;receiveUserDepartmentId\\\&quot;:null,\\\&quot;receiveUserDepartmentName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;collaborateUserId\\\&quot;:\\\&quot;1edab88e562e90087125bb0dc03abd0\\\&quot;,\\\&quot;collaborateUserName\\\&quot;:\\\&quot;用户不存在\\\&quot;,\\\&quot;presentDistance\\\&quot;:\\\&quot;300\\\&quot;,\\\&quot;remark\\\&quot;:null,\\\&quot;presentState\\\&quot;:\\\&quot;0/2\\\&quot;,\\\&quot;statusName\\\&quot;:\\\&quot;接收\\\&quot;,\\\&quot;status\\\&quot;:\\\&quot;RECEIVED\\\&quot;,\\\&quot;fallbackState\\\&quot;:\\\&quot;0/2\\\&quot;,\\\&quot;keyPointCount\\\&quot;:2,\\\&quot;deviceCount\\\&quot;:0,\\\&quot;creator\\\&quot;:\\\&quot;1ee172a0444b12097da0936076e546f\\\&quot;,\\\&quot;creatorName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;creatorDepartmentId\\\&quot;:null,\\\&quot;creatorDepartmentName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;createTime\\\&quot;:\\\&quot;2023-06-30 17:57:23\\\&quot;,\\\&quot;beginTime\\\&quot;:\\\&quot;2023-06-29 00:00:00\\\&quot;,\\\&quot;endTime\\\&quot;:\\\&quot;2023-06-29 11:59:59\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;auditUserId\\\&quot;:null,\\\&quot;auditTime\\\&quot;:null,\\\&quot;rejectReason\\\&quot;:null,\\\&quot;planCircle\\\&quot;:\\\&quot;0\\\&quot;,\\\&quot;planCircleName\\\&quot;:\\\&quot;一天两次\\\&quot;},{\\\&quot;id\\\&quot;:\\\&quot;802e19bc8d28b6c9cf572c37c05af3c1\\\&quot;,\\\&quot;code\\\&quot;:\\\&quot;2212140049\\\&quot;,\\\&quot;planId\\\&quot;:\\\&quot;f3bbac9686a606d4de65c7f968afcd08\\\&quot;,\\\&quot;districtAreaId\\\&quot;:\\\&quot;ec541b3ae74f7427371e3b4d35112147\\\&quot;,\\\&quot;districtAreaName\\\&quot;:\\\&quot;天府镇内\\\&quot;,\\\&quot;isNormalPlan\\\&quot;:true,\\\&quot;isNeedFeedback\\\&quot;:false,\\\&quot;moveType\\\&quot;:\\\&quot;车巡\\\&quot;,\\\&quot;devices\\\&quot;:\\\&quot;[{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;阀门\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;5\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;阀门\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;4\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;阀门\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;6\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;阀门\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;3\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;阀门\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;2\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;251\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;250\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;253\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;238\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;249\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;5\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;8\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;16\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;13\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;246\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;247\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;20\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;6\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;11\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;3\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;15\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;245\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;244\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;21\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;10\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;18\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;4\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;237\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;248\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;1\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;2\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;12\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;19\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;17\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;7\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;14\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;9\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;22\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;节点\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;252\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;5\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;4\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;7\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;6\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;1\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;3\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;5\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;4\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;7\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;6\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;1\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;3\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;5\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;4\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;7\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;6\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;1\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;3\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;5\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;4\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;7\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;6\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;1\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;3\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;5\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;4\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;7\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;6\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;1\\\\\\\&quot;},{\\\\\\\&quot;name\\\\\\\&quot;:\\\\\\\&quot;计量装置\\\\\\\&quot;,\\\\\\\&quot;serialId\\\\\\\&quot;:\\\\\\\&quot;3\\\\\\\&quot;}]\\\&quot;,\\\&quot;specialDevices\\\&quot;:null,\\\&quot;name\\\&quot;:\\\&quot;天府镇外巡检\\\&quot;,\\\&quot;receiveUserId\\\&quot;:\\\&quot;1ed7b718bde0e6085ef9fe6e6dd373e\\\&quot;,\\\&quot;receiveUserName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;receiveUserDepartmentId\\\&quot;:null,\\\&quot;receiveUserDepartmentName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;collaborateUserId\\\&quot;:\\\&quot;1edb0c32291b8809af5fd2a6e92292d\\\&quot;,\\\&quot;collaborateUserName\\\&quot;:\\\&quot;用户不存在\\\&quot;,\\\&quot;presentDistance\\\&quot;:\\\&quot;50\\\&quot;,\\\&quot;remark\\\&quot;:null,\\\&quot;presentState\\\&quot;:\\\&quot;0/71\\\&quot;,\\\&quot;statusName\\\&quot;:\\\&quot;接收\\\&quot;,\\\&quot;status\\\&quot;:\\\&quot;RECEIVED\\\&quot;,\\\&quot;fallbackState\\\&quot;:\\\&quot;0/2\\\&quot;,\\\&quot;keyPointCount\\\&quot;:2,\\\&quot;deviceCount\\\&quot;:69,\\\&quot;creator\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;creatorName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;creatorDepartmentId\\\&quot;:null,\\\&quot;creatorDepartmentName\\\&quot;:\\\&quot;未指定\\\&quot;,\\\&quot;createTime\\\&quot;:\\\&quot;2022-12-14 14:08:15\\\&quot;,\\\&quot;beginTime\\\&quot;:\\\&quot;2022-12-14 00:00:00\\\&quot;,\\\&quot;endTime\\\&quot;:\\\&quot;2022-12-15 23:59:59\\\&quot;,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;auditUserId\\\&quot;:null,\\\&quot;auditTime\\\&quot;:null,\\\&quot;rejectReason\\\&quot;:null,\\\&quot;planCircle\\\&quot;:\\\&quot;2\\\&quot;,\\\&quot;planCircleName\\\&quot;:\\\&quot;隔日一次\\\&quot;}]}}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 28 Apr 2025 07:53:37 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:4.0,\&quot;day\&quot;:28.0},\&quot;time\&quot;:{\&quot;hour\&quot;:15.0,\&quot;minute\&quot;:53.0,\&quot;second\&quot;:37.0,\&quot;nano\&quot;:5.768198E8}}}],\&quot;/api/getAllUsers\&quot;:[{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/getAllUsers\\n\\n{\\\&quot;status\\\&quot;:401,\\\&quot;message\\\&quot;:\\\&quot;Authorization header cannot be blank!\\\&quot;,\\\&quot;errorCode\\\&quot;:10,\\\&quot;timestamp\\\&quot;:1749108779749}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 401\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;105\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 07:32:59 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:15.0,\&quot;minute\&quot;:32.0,\&quot;second\&quot;:59.0,\&quot;nano\&quot;:7.879974E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/getAllUsers\\n\\n{\\\&quot;status\\\&quot;:401,\\\&quot;message\\\&quot;:\\\&quot;Authorization header cannot be blank!\\\&quot;,\\\&quot;errorCode\\\&quot;:10,\\\&quot;timestamp\\\&quot;:1749108825679}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 401\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;105\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 07:33:45 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:15.0,\&quot;minute\&quot;:33.0,\&quot;second\&quot;:45.0,\&quot;nano\&quot;:6.913641E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/getAllUsers\\n\\n{\\\&quot;status\\\&quot;:401,\\\&quot;message\\\&quot;:\\\&quot;Authorization header cannot be blank!\\\&quot;,\\\&quot;errorCode\\\&quot;:10,\\\&quot;timestamp\\\&quot;:1749108826779}\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 401\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;105\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 07:33:46 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:15.0,\&quot;minute\&quot;:33.0,\&quot;second\&quot;:46.0,\&quot;nano\&quot;:7.889221E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 07:33:55 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:15.0,\&quot;minute\&quot;:33.0,\&quot;second\&quot;:55.0,\&quot;nano\&quot;:2.805905E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 08:34:22 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:34.0,\&quot;second\&quot;:22.0,\&quot;nano\&quot;:4.74005E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 08:36:46 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:36.0,\&quot;second\&quot;:46.0,\&quot;nano\&quot;:9.606011E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/getAllUsers\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:40.0,\&quot;second\&quot;:10.0,\&quot;nano\&quot;:1.892569E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/getAllUsers\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:40.0,\&quot;second\&quot;:15.0,\&quot;nano\&quot;:6.26005E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/getAllUsers\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:40.0,\&quot;second\&quot;:47.0,\&quot;nano\&quot;:8.179684E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 08:41:07 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:41.0,\&quot;second\&quot;:7.0,\&quot;nano\&quot;:1.52397E7}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/getAllUsers\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Jun 05 16:51:34 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003enested exception is org.apache.ibatis.reflection.ReflectionException: Error instantiating class org.thingsboard.server.dao.model.sql.UserWithPassword with invalid types () or values (). Cause: java.lang.reflect.InvocationTargetException\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;534\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 08:51:34 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:51.0,\&quot;second\&quot;:34.0,\&quot;nano\&quot;:8.550909E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/getAllUsers\\n\\n\\u003chtml\\u003e\\u003cbody\\u003e\\u003ch1\\u003eWhitelabel Error Page\\u003c/h1\\u003e\\u003cp\\u003eThis application has no explicit mapping for /error, so you are seeing this as a fallback.\\u003c/p\\u003e\\u003cdiv id\\u003d\\u0027created\\u0027\\u003eThu Jun 05 16:53:35 CST 2025\\u003c/div\\u003e\\u003cdiv\\u003eThere was an unexpected error (type\\u003dInternal Server Error, status\\u003d500).\\u003c/div\\u003e\\u003cdiv\\u003enested exception is org.apache.ibatis.reflection.ReflectionException: Error instantiating class org.thingsboard.server.dao.model.sql.UserWithPassword with invalid types () or values (). Cause: java.lang.reflect.InvocationTargetException\\u003c/div\\u003e\\u003c/body\\u003e\\u003c/html\\u003e\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 500\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;close\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Content-Length\&quot;:[\&quot;534\&quot;],\&quot;Content-Language\&quot;:[\&quot;zh-CN\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 08:53:35 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;text/html;charset\\u003dISO-8859-1\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:53.0,\&quot;second\&quot;:35.0,\&quot;nano\&quot;:9.63951E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 08:57:27 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:16.0,\&quot;minute\&quot;:57.0,\&quot;second\&quot;:27.0,\&quot;nano\&quot;:4.898924E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 09:04:41 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:4.0,\&quot;second\&quot;:41.0,\&quot;nano\&quot;:6.575021E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 09:04:44 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:17.0,\&quot;minute\&quot;:4.0,\&quot;second\&quot;:44.0,\&quot;nano\&quot;:9.004603E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 11:14:03 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:19.0,\&quot;minute\&quot;:14.0,\&quot;second\&quot;:3.0,\&quot;nano\&quot;:1.168407E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;Fail to send:http://localhost:8765/api/getAllUsers\\n\\nConnectException: Connection refused: connect\&quot;,\&quot;responseHeader\&quot;:{},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:19.0,\&quot;minute\&quot;:18.0,\&quot;second\&quot;:58.0,\&quot;nano\&quot;:1.966272E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1.0,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null,\\\&quot;id\\\&quot;:null},{\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null,\\\&quot;id\\\&quot;:null},{\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null,\\\&quot;id\\\&quot;:null},{\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null,\\\&quot;id\\\&quot;:null},{\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null,\\\&quot;id\\\&quot;:null},{\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null,\\\&quot;id\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 11:19:36 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025.0,\&quot;month\&quot;:6.0,\&quot;day\&quot;:5.0},\&quot;time\&quot;:{\&quot;hour\&quot;:19.0,\&quot;minute\&quot;:19.0,\&quot;second\&quot;:36.0,\&quot;nano\&quot;:1.833397E8}}},{\&quot;url\&quot;:\&quot;http://localhost:8765/api/getAllUsers\&quot;,\&quot;header\&quot;:[{\&quot;key\&quot;:\&quot;x-authorization\&quot;,\&quot;value\&quot;:\&quot;bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ5YW5zaGlAYWRtaW4uY29tIiwic2NvcGVzIjpbIlRFTkFOVF9BRE1JTiJdLCJ1c2VySWQiOiJjNGVhMzYwMC03OWY3LTExZWQtYWM0Yi00ZDM5MTBjOTRiMGMiLCJmaXJzdE5hbWUiOiLmvJTnpLrotKblj7ciLCJsYXN0TmFtZSI6Iua8lOekuui0puWPtyIsImVuYWJsZWQiOnRydWUsImlzUHVibGljIjpmYWxzZSwicGhvbmUiOiIiLCJ0ZW5hbnRJZCI6ImUzZDdiMjAwLTc5ZWMtMTFlZC05YzRiLTZmMjQyNzQwNmFiMSIsImN1c3RvbWVySWQiOiIxMzgxNDAwMC0xZGQyLTExYjItODA4MC04MDgwODA4MDgwODAiLCJpc3MiOiJ0aGluZ3Nib2FyZC5pbyIsImlhdCI6MTc0OTEwODcyNiwiZXhwIjoxODE1Nzc1MzkyfQ._OnhYqbmiAgva4EuAhzt4-06BrmHcKikmQJpNasNMM8yXnISJMETTrYBgsgP73EpeqvH2BVb2gt7u9LmmZePmQ\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;is_checked\&quot;:1,\&quot;not_null\&quot;:\&quot;1\&quot;,\&quot;field_type\&quot;:\&quot;string\&quot;}],\&quot;query\&quot;:[],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;[{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;SYS_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;超级管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678901\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020018\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;userId\\\&quot;:\\\&quot;1e87797f87dc840b342ffe817f06956\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$3ajruto13i/ZNGcpedps.eosoR14Z6P0kJeKFGNvrP0gZwzAka/ba\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;userId\\\&quot;:\\\&quot;1ecc5004052e8d0ad6be5b4311740a7\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;水务管理员\\\&quot;,\\\&quot;departmentId\\\&quot;:\\\&quot;402882d582c4a73d0182c4aaff430000\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$6wI2CiJ8IP8YMXQGovjYMuRuw0/z7/osU5ZJeAGHAyGNPwstghRt2\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18872030442\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020047\\\&quot;,\\\&quot;loginName\\\&quot;:\\\&quot;\\\&quot;,\\\&quot;userId\\\&quot;:\\\&quot;1ecc5004052e8d0ad6be5b4311740a7\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1b21dd2138140008080808080808080\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;TENANT_ADMIN\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;lastName\\\&quot;:\\\&quot;演示账号\\\&quot;,\\\&quot;departmentId\\\&quot;:null,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$qS1nks0rXv1M9SwQjXEK1ewo/T3XCzQcAeLAyqUbTZdpLV7YXC2Wi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;17702003323\\\&quot;,\\\&quot;serialNo\\\&quot;:\\\&quot;2023020046\\\&quot;,\\\&quot;loginName\\\&quot;:null,\\\&quot;userId\\\&quot;:\\\&quot;1ed79f7c4ea3600ac4b4d3910c94b0c\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;4881c49465eb4e2c36861a960b6abc00\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$9nvzTxkiYh0Wn6jRcrPla.oFa0s0OVaoqC/Ngzluz/6xbuWCDsOoi\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;18888888888\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;userId\\\&quot;:\\\&quot;1efb2b357578ba08b4b5196e50adaf1\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null},{\\\&quot;id\\\&quot;:null,\\\&quot;tenantId\\\&quot;:\\\&quot;1ed79ece3d7b2009c4b6f2427406ab1\\\&quot;,\\\&quot;customerId\\\&quot;:\\\&quot;1e88100b49ef6e09cbe77a34f885729\\\&quot;,\\\&quot;authority\\\&quot;:\\\&quot;CUSTOMER_USER\\\&quot;,\\\&quot;email\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;searchText\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;firstName\\\&quot;:\\\&quot;111\\\&quot;,\\\&quot;lastName\\\&quot;:null,\\\&quot;departmentId\\\&quot;:\\\&quot;d700b5674ec2725216afc5ac45b964e3\\\&quot;,\\\&quot;password\\\&quot;:\\\&quot;$2a$10$ClI//L71nhW3IdvKH3aIlutN0RK0ZpSNU/XaIYg.MCZPoi2WJSnLG\\\&quot;,\\\&quot;status\\\&quot;:false,\\\&quot;departmentName\\\&quot;:null,\\\&quot;organizationName\\\&quot;:null,\\\&quot;roleName\\\&quot;:null,\\\&quot;additionalInfo\\\&quot;:null,\\\&quot;phone\\\&quot;:\\\&quot;12345678910\\\&quot;,\\\&quot;serialNo\\\&quot;:null,\\\&quot;loginName\\\&quot;:null,\\\&quot;userId\\\&quot;:\\\&quot;1f041283acb3aa0845565fface690dc\\\&quot;,\\\&quot;searchTextSource\\\&quot;:\\\&quot;<EMAIL>\\\&quot;,\\\&quot;nativeId\\\&quot;:null}]\&quot;,\&quot;responseHeader\&quot;:{\&quot;null\&quot;:[\&quot;HTTP/1.1 200\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;Cache-Control\&quot;:[\&quot;no-cache, no-store, max-age\\u003d0, must-revalidate\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Pragma\&quot;:[\&quot;no-cache\&quot;],\&quot;Expires\&quot;:[\&quot;0\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;1; mode\\u003dblock\&quot;],\&quot;Date\&quot;:[\&quot;Thu, 05 Jun 2025 11:28:04 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json;charset\\u003dUTF-8\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025,\&quot;month\&quot;:6,\&quot;day\&quot;:5},\&quot;time\&quot;:{\&quot;hour\&quot;:19,\&quot;minute\&quot;:28,\&quot;second\&quot;:4,\&quot;nano\&quot;:262855900}}}]}&quot;,
    &quot;Application.ThingsboardServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;Maven.thingsboard [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.thingsboard [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.transport [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.transport [install].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;liutong&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Code-Yanfayun/water/guazhou/water-guazhou-plat/application/src/main/java/org/thingsboard/server/controller&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller\contract" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller\gis" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller\station" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller\store" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\controller" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\resources\mapper\smartManagement\settings" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\java\org\thingsboard\server\maintenance" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\application\src\main\resources\mapper" />
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat\common\file\src\main\java\org\thingsboard" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="ThingsboardServerApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.thingsboard.server.ThingsboardServerApplication" />
      <module name="application" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.thingsboard.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="water-guazhou-plat" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ThingsboardServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="143454f2-f002-4f9c-9c7f-b3073c35c581" name="Changes" comment="" />
      <created>1743416694552</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743416694552</updated>
    </task>
    <task id="LOCAL-00001" summary="dma分区部分注释">
      <option name="closed" value="true" />
      <created>1743477904339</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1743477904339</updated>
    </task>
    <task id="LOCAL-00002" summary="修改partitionMapper.getAllIdNameByPid（）sql，查询全部信息">
      <option name="closed" value="true" />
      <created>1743644111899</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1743644111899</updated>
    </task>
    <task id="LOCAL-00003" summary="feat：水质化验管理和文件管理功能">
      <option name="closed" value="true" />
      <created>1744082067999</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1744082067999</updated>
    </task>
    <task id="LOCAL-00004" summary="补全接口">
      <option name="closed" value="true" />
      <created>1744269237950</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1744269237950</updated>
    </task>
    <task id="LOCAL-00005" summary="补全接口,水源地巡检相关">
      <option name="closed" value="true" />
      <created>1744275227206</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1744275227206</updated>
    </task>
    <task id="LOCAL-00006" summary="补全接口,视频和工单相关">
      <option name="closed" value="true" />
      <created>1744283163687</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1744283163687</updated>
    </task>
    <task id="LOCAL-00007" summary="完善化验记录功能">
      <option name="closed" value="true" />
      <created>1744352728573</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1744352728573</updated>
    </task>
    <task id="LOCAL-00008" summary="完成涵养工艺功能，修改化验记录bug">
      <option name="closed" value="true" />
      <created>1744702391451</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1744702391451</updated>
    </task>
    <task id="LOCAL-00009" summary="补全代码">
      <option name="closed" value="true" />
      <created>1744707172081</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1744707172081</updated>
    </task>
    <task id="LOCAL-00010" summary="采样记录功能提交">
      <option name="closed" value="true" />
      <created>1744769118341</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1744769118341</updated>
    </task>
    <task id="LOCAL-00011" summary="修改数据库配置">
      <option name="closed" value="true" />
      <created>1745308992366</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1745308992366</updated>
    </task>
    <task id="LOCAL-00012" summary="修改数据库配置">
      <option name="closed" value="true" />
      <created>1745309223999</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1745309223999</updated>
    </task>
    <task id="LOCAL-00013" summary="修复通过分区id查询的的sql">
      <option name="closed" value="true" />
      <created>1745373279094</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1745373279094</updated>
    </task>
    <task id="LOCAL-00014" summary="补充缺失代码">
      <option name="closed" value="true" />
      <created>1745459495624</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1745459495624</updated>
    </task>
    <task id="LOCAL-00015" summary="修改设备属性详情bug">
      <option name="closed" value="true" />
      <created>1745462605192</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1745462605192</updated>
    </task>
    <task id="LOCAL-00016" summary="thingsboard页面相关内容">
      <option name="closed" value="true" />
      <created>1745722153028</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1745722153028</updated>
    </task>
    <task id="LOCAL-00017" summary="完善任务流程内容">
      <option name="closed" value="true" />
      <created>1745823115004</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1745823115004</updated>
    </task>
    <task id="LOCAL-00018" summary="完善任务流程内容">
      <option name="closed" value="true" />
      <created>1745911482968</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1745911482968</updated>
    </task>
    <task id="LOCAL-00019" summary="应要求 添加菜单的同时，添加应用关系">
      <option name="closed" value="true" />
      <created>1746608555698</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1746608555698</updated>
    </task>
    <task id="LOCAL-00020" summary="应要求 添加菜单的同时，添加应用关系">
      <option name="closed" value="true" />
      <created>1746609243351</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1746609243351</updated>
    </task>
    <task id="LOCAL-00021" summary="应要求 删除菜单的同时，删除应用关系">
      <option name="closed" value="true" />
      <created>1746610465307</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1746610465307</updated>
    </task>
    <task id="LOCAL-00022" summary="修改化验记录，支持水源地，供水厂，污水厂">
      <option name="closed" value="true" />
      <created>1746669963358</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1746669963358</updated>
    </task>
    <task id="LOCAL-00023" summary="修复菜单出现的重复提交问题">
      <option name="closed" value="true" />
      <created>1746686846719</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1746686846719</updated>
    </task>
    <task id="LOCAL-00024" summary="调度分析和出水台账">
      <option name="closed" value="true" />
      <created>1746772136391</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1746772136391</updated>
    </task>
    <task id="LOCAL-00025" summary="组织管理更改为水厂管理">
      <option name="closed" value="true" />
      <created>1746779659061</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1746779659061</updated>
    </task>
    <task id="LOCAL-00026" summary="调度相关内容新增管理">
      <option name="closed" value="true" />
      <created>1746783266941</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1746783266941</updated>
    </task>
    <task id="LOCAL-00027" summary="设备权限功能">
      <option name="closed" value="true" />
      <created>1747039033861</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1747039033861</updated>
    </task>
    <task id="LOCAL-00028" summary="设备权限实现类目录调整，项目归档记录bug修复">
      <option name="closed" value="true" />
      <created>1747809260228</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1747809260229</updated>
    </task>
    <task id="LOCAL-00029" summary="设备图标功能（展示配置）；修规项目归档bug">
      <option name="closed" value="true" />
      <created>1747816406793</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1747816406793</updated>
    </task>
    <task id="LOCAL-00030" summary="修改租户id相关内容，进行转化">
      <option name="closed" value="true" />
      <created>1747818196543</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1747818196543</updated>
    </task>
    <task id="LOCAL-00031" summary="修复租户id转化时的bug">
      <option name="closed" value="true" />
      <created>1747894181892</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1747894181892</updated>
    </task>
    <task id="LOCAL-00032" summary="视频监管功能">
      <option name="closed" value="true" />
      <created>1747985586569</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1747985586569</updated>
    </task>
    <task id="LOCAL-00033" summary="门禁管理视频监管功能">
      <option name="closed" value="true" />
      <created>1748228732032</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1748228732032</updated>
    </task>
    <task id="LOCAL-00034" summary="1.删除解密后密码日志&#10;2.完成rabbitmq同步用户消息推送">
      <option name="closed" value="true" />
      <created>1748502756866</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1748502756866</updated>
    </task>
    <task id="LOCAL-00035" summary="1.删除未用到的文件">
      <option name="closed" value="true" />
      <created>1748509959497</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1748509959497</updated>
    </task>
    <task id="LOCAL-00036" summary="用户相关内容修改，包括用户修改，全量查询等">
      <option name="closed" value="true" />
      <created>1749110250389</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1749110250389</updated>
    </task>
    <task id="LOCAL-00037" summary="巡检配置相关内容">
      <option name="closed" value="true" />
      <created>1749120807812</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1749120807812</updated>
    </task>
    <task id="LOCAL-00038" summary="全量用户校准">
      <option name="closed" value="true" />
      <created>1749122975361</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1749122975361</updated>
    </task>
    <task id="LOCAL-00039" summary="巡检配置表结构位置修改，培训配置-巡检计划功能">
      <option name="closed" value="true" />
      <created>1749180916961</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1749180916961</updated>
    </task>
    <task id="LOCAL-00040" summary="外业巡检相关内容完善（巡检表单绑定计划），修改了巡检表单，巡检计划，巡检任务等内容">
      <option name="closed" value="true" />
      <created>1749453572320</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1749453572320</updated>
    </task>
    <task id="LOCAL-00041" summary="alarmJson接口报错修改，不走@Cacheable">
      <option name="closed" value="true" />
      <created>1749535739382</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1749535739382</updated>
    </task>
    <task id="LOCAL-00042" summary="修改采用记录使用全部模块">
      <option name="closed" value="true" />
      <created>1749607495274</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1749607495274</updated>
    </task>
    <task id="LOCAL-00043" summary="涵养水位功能">
      <option name="closed" value="true" />
      <created>1749639811165</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1749639811165</updated>
    </task>
    <option name="localTasksCounter" value="44" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="5.8" />
    <MESSAGE value="5.9" />
    <MESSAGE value="调度分析和出水台账" />
    <MESSAGE value="组织管理更改为水厂管理" />
    <MESSAGE value="调度相关内容新增管理" />
    <MESSAGE value="设备权限功能" />
    <MESSAGE value="设备权限实现类目录调整，项目归档记录bug修复" />
    <MESSAGE value="设备图标功能（展示配置）；修规项目归档bug" />
    <MESSAGE value="修改租户id相关内容，进行转化" />
    <MESSAGE value="修复租户id转化时的bug" />
    <MESSAGE value="视频监管功能" />
    <MESSAGE value="门禁管理视频监管功能" />
    <MESSAGE value="1.删除解密后密码日志&#10;2.完成rabbitmq同步用户消息推送" />
    <MESSAGE value="1.删除未用到的文件" />
    <MESSAGE value="6.4" />
    <MESSAGE value="6.4 10.53" />
    <MESSAGE value="用户相关内容修改，包括用户修改，全量查询等" />
    <MESSAGE value="巡检配置相关内容" />
    <MESSAGE value="全量用户校准" />
    <MESSAGE value="巡检配置表结构位置修改，培训配置-巡检计划功能" />
    <MESSAGE value="外业巡检相关内容完善（巡检表单绑定计划），修改了巡检表单，巡检计划，巡检任务等内容" />
    <MESSAGE value="alarmJson接口报错修改，不走@Cacheable" />
    <MESSAGE value="修改采用记录使用全部模块" />
    <MESSAGE value="涵养水位功能" />
    <MESSAGE value="6.11" />
    <option name="LAST_COMMIT_MESSAGE" value="6.11" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/dao/src/main/java/org/thingsboard/server/dao/smartPipe/PartitionServiceImpl.java</url>
          <line>417</line>
          <properties>
            <option name="lambda-ordinal" value="-1" />
          </properties>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/application/src/main/java/org/thingsboard/server/controller/base/UserController.java</url>
          <line>309</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/dao/src/main/java/org/thingsboard/server/dao/user/UserServiceImpl.java</url>
          <line>214</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/dao/src/main/java/org/thingsboard/server/dao/zutai/ZutaiDashboardServiceImpl.java</url>
          <line>53</line>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="assignStatus" />
      </configuration>
    </watches-manager>
  </component>
</project>