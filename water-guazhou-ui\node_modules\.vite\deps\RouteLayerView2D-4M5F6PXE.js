import {
  i
} from "./chunk-EBZQ4HJX.js";
import "./chunk-EXOD2YGT.js";
import {
  ae
} from "./chunk-OL4BOHG7.js";
import "./chunk-TJC57NLL.js";
import "./chunk-BXNIX5HP.js";
import "./chunk-R7EXIHGC.js";
import "./chunk-V5RJCYWT.js";
import "./chunk-FSU3GUUE.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-ZJMR67LT.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-M46W6WAV.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-C65HMCEM.js";
import "./chunk-PRPYE2YO.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-RCQLO7DH.js";
import "./chunk-ARNVPEMS.js";
import "./chunk-HSGVCYPR.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-CJ522TQY.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-MY4EPN25.js";
import "./chunk-664WG4NR.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import {
  D2 as D,
  O,
  b2 as b,
  f,
  g,
  h as h2,
  h2 as h3
} from "./chunk-QDDKOKDA.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-UHA44FM7.js";
import "./chunk-TFWV44LH.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-6OFWBRK2.js";
import {
  f as f2
} from "./chunk-7TISIISL.js";
import {
  u
} from "./chunk-74RIZBAU.js";
import "./chunk-JSZR3BUH.js";
import "./chunk-6W6ECZU2.js";
import "./chunk-JCXMTMKU.js";
import "./chunk-WAPZ634R.js";
import "./chunk-QR72MZRQ.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-REFSHSQW.js";
import "./chunk-RURSJOSG.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-LFDODH6Z.js";
import "./chunk-CNABSQRP.js";
import "./chunk-ZVGXJHEK.js";
import "./chunk-U6IEQ6CF.js";
import "./chunk-ZL54NZ7B.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-77E52HT5.js";
import "./chunk-GNVA6PUD.js";
import "./chunk-LN7EIT6D.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-MZVT2AF5.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-HQDK2TLZ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-HDM6HCKB.js";
import "./chunk-ICW345PU.js";
import "./chunk-4HYRJ2BC.js";
import "./chunk-WCGNZXE2.js";
import "./chunk-AZTQJ4L4.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-GDMKZBSE.js";
import "./chunk-KCSQWRUD.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-6TZFJURO.js";
import "./chunk-T4DDX2RP.js";
import "./chunk-T72XIVTW.js";
import "./chunk-6YK77SK5.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MCIIPWB6.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-JZBEMQMW.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-23IHUT3O.js";
import "./chunk-SQPHHXCT.js";
import "./chunk-KOD47KEX.js";
import "./chunk-L73TUWZV.js";
import "./chunk-RN2VFGAQ.js";
import "./chunk-VP6XUPJO.js";
import {
  l as l2
} from "./chunk-ZZQCURLD.js";
import "./chunk-LVWRJMBJ.js";
import "./chunk-3HW44BD3.js";
import "./chunk-JSANYNBO.js";
import "./chunk-TPRZH2SY.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-V6NQCXYQ.js";
import "./chunk-XAKEPYSQ.js";
import {
  h,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-I4BKZ7SD.js";
import "./chunk-S5FBFNAP.js";
import "./chunk-VBAEC53F.js";
import "./chunk-C67OD7TM.js";
import "./chunk-5A4WR2SR.js";
import "./chunk-SJ35WMYN.js";
import "./chunk-UV4E33V4.js";
import "./chunk-PBQFTVHM.js";
import "./chunk-ZJKAJ76S.js";
import "./chunk-46HTCESL.js";
import "./chunk-NE5KC6IQ.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-Q6JATJLO.js";
import "./chunk-CV3OR36A.js";
import "./chunk-JX2QAMUH.js";
import "./chunk-DVUUHX3W.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import "./chunk-SJRT3EVN.js";
import "./chunk-RCNP3U5T.js";
import "./chunk-2CFIAWMM.js";
import {
  j
} from "./chunk-3MWB7OGY.js";
import "./chunk-MURG32WB.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-LZMNPMOO.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-3BEYEFLH.js";
import "./chunk-NM5RTWYY.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-B2DWQPEO.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-IJ6FZE6K.js";
import "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2 as a,
  y
} from "./chunk-R4CPW7J5.js";
import "./chunk-2CM7MIII.js";
import "./chunk-HP475EI3.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/RouteLayerView2D.js
var k = Object.freeze({ remove() {
}, pause() {
}, resume() {
} });
var v = ["route-info", "direction-line", "direction-point", "polygon-barrier", "polyline-barrier", "point-barrier", "stop"];
var M = { graphic: null, property: null, oldValue: null, newValue: null };
function V(t2) {
  return t2 instanceof b || t2 instanceof h2 || t2 instanceof O || t2 instanceof g || t2 instanceof f || t2 instanceof h3 || t2 instanceof D;
}
function j2(t2) {
  return j.isCollection(t2) && t2.length && V(t2.getItemAt(0));
}
function G(t2) {
  return Array.isArray(t2) && t2.length > 0 && V(t2[0]);
}
var I = class extends f2(u) {
  constructor() {
    super(...arguments), this._graphics = new j(), this._highlightIds = /* @__PURE__ */ new Map(), this._networkFeatureMap = /* @__PURE__ */ new Map(), this._networkGraphicMap = /* @__PURE__ */ new Map();
  }
  get _routeItems() {
    return new l2({ getCollections: () => r(this.layer) && !this.destroyed ? [r(this.layer.routeInfo) ? new j([this.layer.routeInfo]) : null, this.layer.directionLines, this.layer.directionPoints, this.layer.polygonBarriers, this.layer.polylineBarriers, this.layer.pointBarriers, this.layer.stops] : [] });
  }
  initialize() {
    this.updatingHandles.addOnCollectionChange(() => this._routeItems, (t2) => this._routeItemsChanged(t2), h);
  }
  destroy() {
    var _a;
    this._networkFeatureMap.clear(), this._networkGraphicMap.clear(), this._graphics.removeAll(), (_a = this._get("_routeItems")) == null ? void 0 : _a.destroy();
  }
  attach() {
    this._createGraphicsView();
  }
  detach() {
    this._destroyGraphicsView();
  }
  async fetchPopupFeatures(t2) {
    return this._graphicsView.hitTest(t2).filter((t3) => !!t3.popupTemplate);
  }
  highlight(t2) {
    let e2;
    e2 = V(t2) ? [this._getNetworkFeatureUid(t2)] : G(t2) ? t2.map((t3) => this._getNetworkFeatureUid(t3)) : j2(t2) ? t2.map((t3) => this._getNetworkFeatureUid(t3)).toArray() : [t2.uid];
    const r2 = e2.filter(r);
    return r2.length ? (this._addHighlight(r2), { remove: () => this._removeHighlight(r2) }) : k;
  }
  async hitTest(t2, e2) {
    if (this.suspended) return null;
    const r2 = this._graphicsView.hitTest(t2).filter(r).map((t3) => this._networkGraphicMap.get(t3));
    if (!r2.length) return null;
    const { layer: s } = this;
    return r2.reverse().map((e3) => ({ type: "route", layer: s, mapPoint: t2, networkFeature: e3 }));
  }
  isUpdating() {
    return this._graphicsView.updating;
  }
  moveStart() {
  }
  moveEnd() {
  }
  update(t2) {
    this._graphicsView.processUpdate(t2);
  }
  viewChange() {
    this._graphicsView.viewChange();
  }
  _addHighlight(t2) {
    for (const e2 of t2) if (this._highlightIds.has(e2)) {
      const t3 = this._highlightIds.get(e2);
      this._highlightIds.set(e2, t3 + 1);
    } else this._highlightIds.set(e2, 1);
    this._updateHighlight();
  }
  _createGraphic(t2) {
    const e2 = t2.toGraphic();
    return e2.layer = this.layer, e2.sourceLayer = this.layer, e2;
  }
  _createGraphicsView() {
    const t2 = this.view, e2 = () => this.requestUpdate(), r2 = new i(t2.featuresTilingScheme);
    this._graphicsView = new ae({ container: r2, graphics: this._graphics, requestUpdateCallback: e2, view: t2 }), this.container.addChild(r2), this._updateHighlight();
  }
  _destroyGraphicsView() {
    this.container.removeChild(this._graphicsView.container), this._graphicsView.destroy();
  }
  _getDrawOrder(t2) {
    const e2 = this._networkGraphicMap.get(t2);
    return v.indexOf(e2.type);
  }
  _getNetworkFeatureUid(t2) {
    return this._networkFeatureMap.has(t2) ? this._networkFeatureMap.get(t2).uid : null;
  }
  _removeHighlight(t2) {
    for (const e2 of t2) if (this._highlightIds.has(e2)) {
      const t3 = this._highlightIds.get(e2) - 1;
      0 === t3 ? this._highlightIds.delete(e2) : this._highlightIds.set(e2, t3);
    }
    this._updateHighlight();
  }
  _routeItemsChanged(t2) {
    if (t2.removed.length) {
      this._graphics.removeMany(t2.removed.map((t3) => {
        const e2 = this._networkFeatureMap.get(t3);
        return this._networkFeatureMap.delete(t3), this._networkGraphicMap.delete(e2), e2;
      }));
      for (const e2 of t2.removed) this.removeHandles(e2);
    }
    if (t2.added.length) {
      this._graphics.addMany(t2.added.map((t3) => {
        const e2 = this._createGraphic(t3);
        return t(e2.symbol) ? null : (this._networkFeatureMap.set(t3, e2), this._networkGraphicMap.set(e2, t3), e2);
      }).filter(r));
      for (const e2 of t2.added) this.addHandles([l(() => e2.geometry, (t3, r2) => {
        this._updateGraphic(e2, "geometry", t3, r2);
      }), l(() => e2.symbol, (t3, r2) => {
        this._updateGraphic(e2, "symbol", t3, r2);
      })], e2);
      this._graphics.sort((t3, e2) => this._getDrawOrder(t3) - this._getDrawOrder(e2));
    }
  }
  _updateGraphic(t2, e2, r2, i2) {
    if (!this._networkFeatureMap.has(t2)) {
      const e3 = this._createGraphic(t2);
      return this._networkFeatureMap.set(t2, e3), this._networkGraphicMap.set(e3, t2), void this._graphics.add(e3);
    }
    const s = this._networkFeatureMap.get(t2);
    s[e2] = r2, M.graphic = s, M.property = e2, M.oldValue = i2, M.newValue = r2, this._graphicsView.graphicUpdateHandler(M);
  }
  _updateHighlight() {
    const t2 = Array.from(this._highlightIds.keys());
    this._graphicsView.setHighlight(t2);
  }
};
e([y()], I.prototype, "_graphics", void 0), e([y()], I.prototype, "_routeItems", null), I = e([a("esri.views.2d.layers.RouteLayerView2D")], I);
var F = I;
export {
  F as default
};
//# sourceMappingURL=RouteLayerView2D-4M5F6PXE.js.map
