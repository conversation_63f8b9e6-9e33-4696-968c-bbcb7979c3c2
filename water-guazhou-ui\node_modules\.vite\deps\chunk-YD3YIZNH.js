import {
  s
} from "./chunk-7SWS36OI.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  a as a4
} from "./chunk-QMG7GZIF.js";
import {
  o as o2
} from "./chunk-G5KX4JSG.js";
import {
  l as l2
} from "./chunk-T23PB46T.js";
import {
  a as a3
} from "./chunk-EIGTETCG.js";
import {
  o
} from "./chunk-PEEUPDEG.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  a
} from "./chunk-HP475EI3.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/symbols/support/textUtils.js
var l3 = ["none", "underline", "line-through"];
var t = ["normal", "italic", "oblique"];
var r2 = ["normal", "lighter", "bold", "bolder"];
var n = { type: Number, cast: (l5) => {
  const t2 = a(l5);
  return 0 === t2 ? 1 : a3(t2, 0.1, 4);
}, nonNullable: true };
var i = ["left", "right", "center"];
var a5 = ["baseline", "top", "middle", "bottom"];
var m = { type: i, nonNullable: true };
var p2 = { type: a5, nonNullable: true };

// node_modules/@arcgis/core/symbols/Font.js
var c;
var l4 = c = class extends l {
  constructor(t2) {
    super(t2), this.decoration = "none", this.family = "sans-serif", this.size = 9, this.style = "normal", this.weight = "normal";
  }
  castSize(t2) {
    return o2(t2);
  }
  clone() {
    return new c({ decoration: this.decoration, family: this.family, size: this.size, style: this.style, weight: this.weight });
  }
  hash() {
    return `${this.decoration}.${this.family}.${this.size}.${this.style}.${this.weight}`;
  }
};
e([y({ type: l3, json: { default: "none", write: true } })], l4.prototype, "decoration", void 0), e([y({ type: String, json: { write: true } })], l4.prototype, "family", void 0), e([y({ type: Number, json: { write: { overridePolicy: (t2, o3, e2) => ({ enabled: !e2 || !e2.textSymbol3D }) } } })], l4.prototype, "size", void 0), e([s("size")], l4.prototype, "castSize", null), e([y({ type: t, json: { default: "normal", write: true } })], l4.prototype, "style", void 0), e([y({ type: r2, json: { default: "normal", write: true } })], l4.prototype, "weight", void 0), l4 = c = e([a2("esri.symbols.Font")], l4);
var m2 = l4;

// node_modules/@arcgis/core/symbols/TextSymbol.js
var f;
var g = f = class extends a4 {
  constructor(...t2) {
    super(...t2), this.backgroundColor = null, this.borderLineColor = null, this.borderLineSize = null, this.font = new m2(), this.horizontalAlignment = "center", this.kerning = true, this.haloColor = null, this.haloSize = null, this.rightToLeft = null, this.rotated = false, this.text = "", this.type = "text", this.verticalAlignment = "baseline", this.xoffset = 0, this.yoffset = 0, this.angle = 0, this.width = null, this.lineWidth = 192, this.lineHeight = 1;
  }
  normalizeCtorArgs(t2, o3, e2) {
    if (t2 && "string" != typeof t2) return t2;
    const i2 = {};
    return t2 && (i2.text = t2), o3 && (i2.font = o3), e2 && (i2.color = e2), i2;
  }
  writeLineWidth(t2, o3, e2, i2) {
    i2 && "string" != typeof i2 ? i2.origin : o3[e2] = t2;
  }
  castLineWidth(t2) {
    return o2(t2);
  }
  writeLineHeight(t2, o3, e2, i2) {
    i2 && "string" != typeof i2 ? i2.origin : o3[e2] = t2;
  }
  clone() {
    return new f({ angle: this.angle, backgroundColor: p(this.backgroundColor), borderLineColor: p(this.borderLineColor), borderLineSize: this.borderLineSize, color: p(this.color), font: this.font && this.font.clone(), haloColor: p(this.haloColor), haloSize: this.haloSize, horizontalAlignment: this.horizontalAlignment, kerning: this.kerning, lineHeight: this.lineHeight, lineWidth: this.lineWidth, rightToLeft: this.rightToLeft, rotated: this.rotated, text: this.text, verticalAlignment: this.verticalAlignment, width: this.width, xoffset: this.xoffset, yoffset: this.yoffset });
  }
  hash() {
    var _a;
    return `${this.backgroundColor && this.backgroundColor.hash()}.${this.borderLineColor}.${this.borderLineSize}.${(_a = this.color) == null ? void 0 : _a.hash()}.${this.font && this.font.hash()}.${this.haloColor && this.haloColor.hash()}.${this.haloSize}.${this.horizontalAlignment}.${this.kerning}.${this.rightToLeft}.${this.rotated}.${this.text}.${this.verticalAlignment}.${this.width}.${this.xoffset}.${this.yoffset}.${this.lineHeight}.${this.lineWidth}.${this.angle}`;
  }
};
e([y({ type: l2, json: { write: true } })], g.prototype, "backgroundColor", void 0), e([y({ type: l2, json: { write: true } })], g.prototype, "borderLineColor", void 0), e([y({ type: Number, json: { write: true }, cast: o2 })], g.prototype, "borderLineSize", void 0), e([y({ type: m2, json: { write: true } })], g.prototype, "font", void 0), e([y({ ...m, json: { write: true } })], g.prototype, "horizontalAlignment", void 0), e([y({ type: Boolean, json: { write: true } })], g.prototype, "kerning", void 0), e([y({ type: l2, json: { write: true } })], g.prototype, "haloColor", void 0), e([y({ type: Number, cast: o2, json: { write: true } })], g.prototype, "haloSize", void 0), e([y({ type: Boolean, json: { write: true } })], g.prototype, "rightToLeft", void 0), e([y({ type: Boolean, json: { write: true } })], g.prototype, "rotated", void 0), e([y({ type: String, json: { write: true } })], g.prototype, "text", void 0), e([o({ esriTS: "text" }, { readOnly: true })], g.prototype, "type", void 0), e([y({ ...p2, json: { write: true } })], g.prototype, "verticalAlignment", void 0), e([y({ type: Number, cast: o2, json: { write: true } })], g.prototype, "xoffset", void 0), e([y({ type: Number, cast: o2, json: { write: true } })], g.prototype, "yoffset", void 0), e([y({ type: Number, json: { read: (t2) => t2 && -1 * t2, write: (t2, o3) => o3.angle = t2 && -1 * t2 } })], g.prototype, "angle", void 0), e([y({ type: Number, json: { write: true } })], g.prototype, "width", void 0), e([y({ type: Number })], g.prototype, "lineWidth", void 0), e([r("lineWidth")], g.prototype, "writeLineWidth", null), e([s("lineWidth")], g.prototype, "castLineWidth", null), e([y(n)], g.prototype, "lineHeight", void 0), e([r("lineHeight")], g.prototype, "writeLineHeight", null), g = f = e([a2("esri.symbols.TextSymbol")], g);
var m3 = g;

export {
  n,
  m,
  p2 as p,
  m2,
  m3
};
//# sourceMappingURL=chunk-YD3YIZNH.js.map
