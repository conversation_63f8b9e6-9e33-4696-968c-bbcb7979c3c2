import {
  n as n2,
  o,
  t
} from "./chunk-7K6G5AAB.js";
import {
  P,
  S
} from "./chunk-MJS64MES.js";
import {
  a as a2
} from "./chunk-4W7HU754.js";
import {
  n
} from "./chunk-GLYRTR3D.js";
import "./chunk-TAERZTFZ.js";
import "./chunk-KYDW2SHL.js";
import "./chunk-GHAN7X65.js";
import "./chunk-3HDXUZDA.js";
import "./chunk-ARVYW5FF.js";
import "./chunk-WCRONQ5Z.js";
import {
  ae
} from "./chunk-27CJODAI.js";
import "./chunk-L7J6WAZK.js";
import "./chunk-EJ4BPAYT.js";
import "./chunk-JTTSDQPH.js";
import "./chunk-6DXPU43Z.js";
import "./chunk-3MBH7CQT.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-EDV64J6E.js";
import "./chunk-OBW4AQOU.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-3BVSG4LE.js";
import "./chunk-JJZTA23S.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-VXAO6YJP.js";
import "./chunk-BI4P4NAQ.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-YROREPK5.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-CV76WXPW.js";
import "./chunk-EVADT7ME.js";
import "./chunk-T3GGN2P7.js";
import "./chunk-2B52LX6T.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-LGZKVOWE.js";
import "./chunk-WKBMFG6J.js";
import "./chunk-BPRRRPC3.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-RFTQI4ZD.js";
import "./chunk-6NIKJYUX.js";
import "./chunk-UHA44FM7.js";
import "./chunk-MDHXGN24.js";
import "./chunk-6ZZUUGXX.js";
import "./chunk-TFWV44LH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-6OFWBRK2.js";
import {
  i as i2
} from "./chunk-5JDQNIY4.js";
import {
  f
} from "./chunk-NEPFZ7PM.js";
import {
  u
} from "./chunk-HWB4LNSZ.js";
import "./chunk-JSZR3BUH.js";
import "./chunk-QKWIBVLD.js";
import "./chunk-JCXMTMKU.js";
import "./chunk-WAPZ634R.js";
import "./chunk-OHAM27JH.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-2WS4DQ5K.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-22FAZXOH.js";
import "./chunk-T7HWQQFI.js";
import "./chunk-DFGMRI52.js";
import {
  r
} from "./chunk-OZZFNS32.js";
import {
  y as y2
} from "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import {
  h
} from "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import {
  e as e2
} from "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-THUK4WUF.js";
import "./chunk-5ZZCQR67.js";
import "./chunk-RURSJOSG.js";
import "./chunk-OYGWWPGZ.js";
import "./chunk-77E52HT5.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-7I556A2J.js";
import "./chunk-SIWJOTKY.js";
import {
  i
} from "./chunk-56K7OMWB.js";
import "./chunk-N35UHD63.js";
import "./chunk-T5TRCNG4.js";
import "./chunk-TNP2LXZZ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-YBNKNHCD.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import "./chunk-G3QAWKCD.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-WL6G2MRC.js";
import "./chunk-UCWK623G.js";
import "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  E
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/TileLayerView2D.js
var T = [0, 0];
var v = class extends i2(t(f(u))) {
  constructor() {
    super(...arguments), this._fetchQueue = null, this._highlightGraphics = new i(), this._highlightView = null, this._popupHighlightHelper = null, this._tileStrategy = null, this.layer = null;
  }
  get resampling() {
    return !("resampling" in this.layer) || false !== this.layer.resampling;
  }
  update(e3) {
    var _a;
    this._fetchQueue.pause(), this._fetchQueue.state = e3.state, this._tileStrategy.update(e3), this._fetchQueue.resume(), (_a = this._highlightView) == null ? void 0 : _a.processUpdate(e3);
  }
  attach() {
    const e3 = "tileServers" in this.layer ? this.layer.tileServers : null;
    if (this._tileInfoView = new h(this.layer.tileInfo, this.layer.fullExtent), this._fetchQueue = new y2({ tileInfoView: this._tileInfoView, concurrency: e3 && 10 * e3.length || 10, process: (e4, i3) => this.fetchTile(e4, i3) }), this._tileStrategy = new r({ cachePolicy: "keep", resampling: this.resampling, acquireTile: (e4) => this.acquireTile(e4), releaseTile: (e4) => this.releaseTile(e4), tileInfoView: this._tileInfoView }), P(this, this.layer)) {
      const e4 = this._highlightView = new ae({ view: this.view, graphics: this._highlightGraphics, requestUpdateCallback: () => this.requestUpdate(), container: new n(this.view.featuresTilingScheme), defaultPointSymbolEnabled: false });
      this.container.addChild(this._highlightView.container), this._popupHighlightHelper = new S({ createFetchPopupFeaturesQueryGeometry: (e5, i3) => a2(e5, i3, this.view), highlightGraphics: this._highlightGraphics, highlightGraphicUpdated: (i3, t2) => {
        e4.graphicUpdateHandler({ graphic: i3, property: t2 });
      }, layerView: this, updatingHandles: this.updatingHandles });
    }
    this.requestUpdate(), this.addAttachHandles(l(() => this.resampling, () => {
      this.doRefresh();
    })), super.attach();
  }
  detach() {
    var _a;
    super.detach(), this._tileStrategy.destroy(), this._fetchQueue.clear(), this.container.removeAllChildren(), (_a = this._popupHighlightHelper) == null ? void 0 : _a.destroy(), this._fetchQueue = this._tileStrategy = this._tileInfoView = this._popupHighlightHelper = null;
  }
  async fetchPopupFeatures(e3, i3) {
    return this._popupHighlightHelper ? this._popupHighlightHelper.fetchPopupFeatures(e3, i3) : [];
  }
  highlight(e3) {
    return this._popupHighlightHelper ? this._popupHighlightHelper.highlight(e3) : { remove() {
    } };
  }
  moveStart() {
    this.requestUpdate();
  }
  viewChange() {
    this.requestUpdate();
  }
  moveEnd() {
    this.requestUpdate();
  }
  supportsSpatialReference(e3) {
    var _a;
    return E((_a = this.layer.tileInfo) == null ? void 0 : _a.spatialReference, e3);
  }
  async doRefresh() {
    !this.attached || this.updateRequested || this.suspended || (this._fetchQueue.reset(), this._tileStrategy.tiles.forEach((e3) => this._enqueueTileFetch(e3)));
  }
  isUpdating() {
    var _a;
    return ((_a = this._fetchQueue) == null ? void 0 : _a.updating) ?? false;
  }
  acquireTile(e3) {
    const i3 = this._bitmapView.createTile(e3), t2 = i3.bitmap;
    return [t2.x, t2.y] = this._tileInfoView.getTileCoords(T, i3.key), t2.resolution = this._tileInfoView.getTileResolution(i3.key), [t2.width, t2.height] = this._tileInfoView.tileInfo.size, this._enqueueTileFetch(i3), this._bitmapView.addChild(i3), this.requestUpdate(), i3;
  }
  releaseTile(e3) {
    this._fetchQueue.abort(e3.key.id), this._bitmapView.removeChild(e3), e3.once("detach", () => e3.destroy()), this.requestUpdate();
  }
  async fetchTile(e3, i3 = {}) {
    const r2 = "tilemapCache" in this.layer ? this.layer.tilemapCache : null, { signal: s2, resamplingLevel: o2 = 0 } = i3;
    if (!r2) try {
      return await this._fetchImage(e3, s2);
    } catch (a3) {
      if (!j(a3) && !this.resampling) return o(this._tileInfoView.tileInfo.size);
      if (o2 < 3) {
        const t2 = this._tileInfoView.getTileParentId(e3.id);
        if (t2) {
          const r3 = new e2(t2), s3 = await this.fetchTile(r3, { ...i3, resamplingLevel: o2 + 1 });
          return n2(this._tileInfoView, s3, r3, e3);
        }
      }
      throw a3;
    }
    const l2 = new e2(0, 0, 0, 0);
    let h2;
    try {
      if (await r2.fetchAvailabilityUpsample(e3.level, e3.row, e3.col, l2, { signal: s2 }), l2.level !== e3.level && !this.resampling) return o(this._tileInfoView.tileInfo.size);
      h2 = await this._fetchImage(l2, s2);
    } catch (a3) {
      if (j(a3)) throw a3;
      h2 = await this._fetchImage(e3, s2);
    }
    return this.resampling ? n2(this._tileInfoView, h2, l2, e3) : h2;
  }
  async _enqueueTileFetch(e3) {
    if (!this._fetchQueue.has(e3.key.id)) {
      try {
        const i3 = await this._fetchQueue.push(e3.key);
        e3.bitmap.source = i3, e3.bitmap.width = this._tileInfoView.tileInfo.size[0], e3.bitmap.height = this._tileInfoView.tileInfo.size[1], e3.once("attach", () => this.requestUpdate());
      } catch (r2) {
        j(r2) || s.getLogger(this.declaredClass).error(r2);
      }
      this.requestUpdate();
    }
  }
  async _fetchImage(e3, i3) {
    return this.layer.fetchImageBitmapTile(e3.level, e3.row, e3.col, { signal: i3 });
  }
};
e([y()], v.prototype, "_fetchQueue", void 0), e([y()], v.prototype, "resampling", null), v = e([a("esri.views.2d.layers.TileLayerView2D")], v);
var I = v;
export {
  I as default
};
//# sourceMappingURL=TileLayerView2D-M55W4MDC.js.map
