{"version": 3, "sources": ["../../@arcgis/core/geometry/support/normalizeUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../config.js\";import t from\"../../core/Error.js\";import n from\"../../core/Logger.js\";import{isNone as o,isSome as s}from\"../../core/maybe.js\";import r from\"../Polygon.js\";import i from\"../Polyline.js\";import{getGeometryParts as l,cutParams as f,offsetMagnitude as c,updatePolyGeometry as p}from\"./normalizeUtilsCommon.js\";import{getInfo as u}from\"./spatialReferenceUtils.js\";import{geographicToWebMercator as a,webMercatorToGeographic as h}from\"./webMercatorUtils.js\";import{cut as m}from\"../../rest/geometryService/cut.js\";import{simplify as g}from\"../../rest/geometryService/simplify.js\";const y=n.getLogger(\"esri.geometry.support.normalizeUtils\");function x(e){return\"polygon\"===e.type}function d(e){return\"polygon\"===e[0].type}function w(e){return\"polyline\"===e[0].type}function j(e){const t=[];let n=0,o=0;for(let s=0;s<e.length;s++){const r=e[s];let i=null;for(let e=0;e<r.length;e++)i=r[e],t.push(i),0===e?(n=i[0],o=n):(n=Math.min(n,i[0]),o=Math.max(o,i[0]));i&&t.push([(n+o)/2,0])}return t}function M(e,n){if(!(e instanceof i||e instanceof r)){const e=\"straightLineDensify: the input geometry is neither polyline nor polygon\";throw y.error(e),new t(e)}const o=l(e),s=[];for(const t of o){const e=[];s.push(e),e.push([t[0][0],t[0][1]]);for(let o=0;o<t.length-1;o++){const s=t[o][0],r=t[o][1],i=t[o+1][0],l=t[o+1][1],f=Math.sqrt((i-s)*(i-s)+(l-r)*(l-r)),c=(l-r)/f,p=(i-s)/f,u=f/n;if(u>1){for(let l=1;l<=u-1;l++){const t=l*n,o=p*t+s,i=c*t+r;e.push([o,i])}const t=(f+Math.floor(u-1)*n)/2,o=p*t+s,i=c*t+r;e.push([o,i])}e.push([i,l])}}return x(e)?new r({rings:s,spatialReference:e.spatialReference}):new i({paths:s,spatialReference:e.spatialReference})}function R(e,t,n){if(t){const t=M(e,1e6);e=h(t,!0)}return n&&(e=p(e,n)),e}function b(e,t,n){if(Array.isArray(e)){const o=e[0];if(o>t){const n=c(o,t);e[0]=o+n*(-2*t)}else if(o<n){const t=c(o,n);e[0]=o+t*(-2*n)}}else{const o=e.x;if(o>t){const n=c(o,t);e=e.clone().offset(n*(-2*t),0)}else if(o<n){const t=c(o,n);e=e.clone().offset(t*(-2*n),0)}}return e}function P(e,t){let n=-1;for(let o=0;o<t.cutIndexes.length;o++){const s=t.cutIndexes[o],r=t.geometries[o],i=l(r);for(let e=0;e<i.length;e++){const t=i[e];t.some((n=>{if(n[0]<180)return!0;{let n=0;for(let e=0;e<t.length;e++){const o=t[e][0];n=o>n?o:n}n=Number(n.toFixed(9));const o=-360*c(n,180);for(let s=0;s<t.length;s++){const t=r.getPoint(e,s);r.setPoint(e,s,t.clone().offset(o,0))}return!0}}))}if(s===n){if(d(e))for(const t of l(r))e[s]=e[s].addRing(t);else if(w(e))for(const t of l(r))e[s]=e[s].addPath(t)}else n=s,e[s]=r}return e}async function v(t,n,l){if(!Array.isArray(t))return v([t],n);n&&\"string\"!=typeof n&&y.warn(\"normalizeCentralMeridian()\",\"The url object is deprecated, use the url string instead\");const h=\"string\"==typeof n?n:n?.url??e.geometryServiceUrl;let x,d,w,j,M,L,U,z,A=0;const S=[],k=[];for(const e of t)if(o(e))k.push(e);else if(x||(x=e.spatialReference,d=u(x),w=x.isWebMercator,L=w?102100:4326,j=f[L].maxX,M=f[L].minX,U=f[L].plus180Line,z=f[L].minus180Line),d)if(\"mesh\"===e.type)k.push(e);else if(\"point\"===e.type)k.push(b(e.clone(),j,M));else if(\"multipoint\"===e.type){const t=e.clone();t.points=t.points.map((e=>b(e,j,M))),k.push(t)}else if(\"extent\"===e.type){const t=e.clone()._normalize(!1,!1,d);k.push(t.rings?new r(t):t)}else if(e.extent){const t=e.extent,n=c(t.xmin,M)*(2*j);let o=0===n?e.clone():p(e.clone(),n);t.offset(n,0),t.intersects(U)&&t.xmax!==j?(A=t.xmax>A?t.xmax:A,o=R(o,w),S.push(o),k.push(\"cut\")):t.intersects(z)&&t.xmin!==M?(A=t.xmax*(2*j)>A?t.xmax*(2*j):A,o=R(o,w,360),S.push(o),k.push(\"cut\")):k.push(o)}else k.push(e.clone());else k.push(e);let C=c(A,j),I=-90;const X=C,q=new i;for(;C>0;){const e=360*C-180;q.addPath([[e,I],[e,-1*I]]),I*=-1,C--}if(S.length>0&&X>0){const e=P(S,await m(h,S,q,l)),n=[],o=[];for(let l=0;l<k.length;l++){const r=k[l];if(\"cut\"!==r)o.push(r);else{const r=e.shift(),i=t[l];s(i)&&\"polygon\"===i.type&&i.rings&&i.rings.length>1&&r.rings.length>=i.rings.length?(n.push(r),o.push(\"simplify\")):o.push(w?a(r):r)}}if(!n.length)return o;const r=await g(h,n,l),i=[];for(let t=0;t<o.length;t++){const e=o[t];\"simplify\"!==e?i.push(e):i.push(w?a(r.shift()):r.shift())}return i}const D=[];for(let e=0;e<k.length;e++){const t=k[e];if(\"cut\"!==t)D.push(t);else{const e=S.shift();D.push(!0===w?a(e):e)}}return D}function L(e){if(!e)return null;const t=e.extent;if(!t)return null;const n=e.spatialReference&&u(e.spatialReference);if(!n)return t;const[o,s]=n.valid,r=2*s,{width:i}=t;let l,{xmin:f,xmax:c}=t;if([f,c]=[c,f],\"extent\"===e.type||0===i||i<=s||i>r||f<o||c>s)return t;switch(e.type){case\"polygon\":if(!(e.rings.length>1))return t;l=j(e.rings);break;case\"polyline\":if(!(e.paths.length>1))return t;l=j(e.paths);break;case\"multipoint\":l=e.points}const p=t.clone();for(let u=0;u<l.length;u++){let e=l[u][0];e<0?(e+=s,c=Math.max(e,c)):(e-=s,f=Math.min(e,f))}return p.xmin=f,p.xmax=c,p.width<i?(p.xmin-=s,p.xmax-=s,p):t}function U(e,t){const n=u(t);if(n){const[t,o]=n.valid,s=o-t;if(e<t)for(;e<t;)e+=s;if(e>o)for(;e>o;)e-=s}return e}export{L as getDenormalizedExtent,v as normalizeCentralMeridian,U as normalizeMapX,M as straightLineDensify};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+lB,IAAM,IAAEA,GAAE,UAAU,sCAAsC;AAAE,SAAS,EAAE,GAAE;AAAC,SAAM,cAAY,EAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,cAAY,EAAE,CAAC,EAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,eAAa,EAAE,CAAC,EAAE;AAAI;AAAiO,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,EAAE,aAAa,KAAG,aAAa,IAAG;AAAC,UAAMC,KAAE;AAA0E,UAAM,EAAE,MAAMA,EAAC,GAAE,IAAIC,GAAED,EAAC;AAAA,EAAC;AAAC,QAAME,KAAE,EAAE,CAAC,GAAED,KAAE,CAAC;AAAE,aAAUE,MAAKD,IAAE;AAAC,UAAMF,KAAE,CAAC;AAAE,IAAAC,GAAE,KAAKD,EAAC,GAAEA,GAAE,KAAK,CAACG,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAAE,aAAQD,KAAE,GAAEA,KAAEC,GAAE,SAAO,GAAED,MAAI;AAAC,YAAMD,KAAEE,GAAED,EAAC,EAAE,CAAC,GAAEE,KAAED,GAAED,EAAC,EAAE,CAAC,GAAEG,KAAEF,GAAED,KAAE,CAAC,EAAE,CAAC,GAAE,IAAEC,GAAED,KAAE,CAAC,EAAE,CAAC,GAAE,IAAE,KAAK,MAAMG,KAAEJ,OAAII,KAAEJ,OAAI,IAAEG,OAAI,IAAEA,GAAE,GAAE,KAAG,IAAEA,MAAG,GAAE,KAAGC,KAAEJ,MAAG,GAAE,IAAE,IAAE;AAAE,UAAG,IAAE,GAAE;AAAC,iBAAQK,KAAE,GAAEA,MAAG,IAAE,GAAEA,MAAI;AAAC,gBAAMH,KAAEG,KAAE,GAAEJ,KAAE,IAAEC,KAAEF,IAAEI,KAAE,IAAEF,KAAEC;AAAE,UAAAJ,GAAE,KAAK,CAACE,IAAEG,EAAC,CAAC;AAAA,QAAC;AAAC,cAAMF,MAAG,IAAE,KAAK,MAAM,IAAE,CAAC,IAAE,KAAG,GAAED,KAAE,IAAEC,KAAEF,IAAEI,KAAE,IAAEF,KAAEC;AAAE,QAAAJ,GAAE,KAAK,CAACE,IAAEG,EAAC,CAAC;AAAA,MAAC;AAAC,MAAAL,GAAE,KAAK,CAACK,IAAE,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,EAAE,CAAC,IAAE,IAAI,EAAE,EAAC,OAAMJ,IAAE,kBAAiB,EAAE,iBAAgB,CAAC,IAAE,IAAI,EAAE,EAAC,OAAMA,IAAE,kBAAiB,EAAE,iBAAgB,CAAC;AAAC;AAAC,SAASM,GAAE,GAAEJ,IAAE,GAAE;AAAC,MAAGA,IAAE;AAAC,UAAMA,KAAE,EAAE,GAAE,GAAG;AAAE,QAAE,EAAEA,IAAE,IAAE;AAAA,EAAC;AAAC,SAAO,MAAI,IAAEF,GAAE,GAAE,CAAC,IAAG;AAAC;AAAC,SAAS,EAAE,GAAEE,IAAE,GAAE;AAAC,MAAG,MAAM,QAAQ,CAAC,GAAE;AAAC,UAAMD,KAAE,EAAE,CAAC;AAAE,QAAGA,KAAEC,IAAE;AAAC,YAAMK,KAAE,EAAEN,IAAEC,EAAC;AAAE,QAAE,CAAC,IAAED,KAAEM,MAAG,KAAGL;AAAA,IAAE,WAASD,KAAE,GAAE;AAAC,YAAMC,KAAE,EAAED,IAAE,CAAC;AAAE,QAAE,CAAC,IAAEA,KAAEC,MAAG,KAAG;AAAA,IAAE;AAAA,EAAC,OAAK;AAAC,UAAMD,KAAE,EAAE;AAAE,QAAGA,KAAEC,IAAE;AAAC,YAAMK,KAAE,EAAEN,IAAEC,EAAC;AAAE,UAAE,EAAE,MAAM,EAAE,OAAOK,MAAG,KAAGL,KAAG,CAAC;AAAA,IAAC,WAASD,KAAE,GAAE;AAAC,YAAMC,KAAE,EAAED,IAAE,CAAC;AAAE,UAAE,EAAE,MAAM,EAAE,OAAOC,MAAG,KAAG,IAAG,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAI,IAAE;AAAG,WAAQD,KAAE,GAAEA,KAAEC,GAAE,WAAW,QAAOD,MAAI;AAAC,UAAMD,KAAEE,GAAE,WAAWD,EAAC,GAAEE,KAAED,GAAE,WAAWD,EAAC,GAAEG,KAAE,EAAED,EAAC;AAAE,aAAQJ,KAAE,GAAEA,KAAEK,GAAE,QAAOL,MAAI;AAAC,YAAMG,KAAEE,GAAEL,EAAC;AAAE,MAAAG,GAAE,KAAM,CAAAK,OAAG;AAAC,YAAGA,GAAE,CAAC,IAAE,IAAI,QAAM;AAAG;AAAC,cAAIA,KAAE;AAAE,mBAAQR,KAAE,GAAEA,KAAEG,GAAE,QAAOH,MAAI;AAAC,kBAAME,KAAEC,GAAEH,EAAC,EAAE,CAAC;AAAE,YAAAQ,KAAEN,KAAEM,KAAEN,KAAEM;AAAA,UAAC;AAAC,UAAAA,KAAE,OAAOA,GAAE,QAAQ,CAAC,CAAC;AAAE,gBAAMN,KAAE,OAAK,EAAEM,IAAE,GAAG;AAAE,mBAAQP,KAAE,GAAEA,KAAEE,GAAE,QAAOF,MAAI;AAAC,kBAAME,KAAEC,GAAE,SAASJ,IAAEC,EAAC;AAAE,YAAAG,GAAE,SAASJ,IAAEC,IAAEE,GAAE,MAAM,EAAE,OAAOD,IAAE,CAAC,CAAC;AAAA,UAAC;AAAC,iBAAM;AAAA,QAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,QAAGD,OAAI,GAAE;AAAC,UAAG,EAAE,CAAC,EAAE,YAAUE,MAAK,EAAEC,EAAC,EAAE,GAAEH,EAAC,IAAE,EAAEA,EAAC,EAAE,QAAQE,EAAC;AAAA,eAAU,EAAE,CAAC,EAAE,YAAUA,MAAK,EAAEC,EAAC,EAAE,GAAEH,EAAC,IAAE,EAAEA,EAAC,EAAE,QAAQE,EAAC;AAAA,IAAC,MAAM,KAAEF,IAAE,EAAEA,EAAC,IAAEG;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,eAAeK,GAAEN,IAAE,GAAE,GAAE;AAAC,MAAG,CAAC,MAAM,QAAQA,EAAC,EAAE,QAAOM,GAAE,CAACN,EAAC,GAAE,CAAC;AAAE,OAAG,YAAU,OAAO,KAAG,EAAE,KAAK,8BAA6B,0DAA0D;AAAE,QAAM,IAAE,YAAU,OAAO,IAAE,KAAE,uBAAG,QAAK,EAAE;AAAmB,MAAIO,IAAEC,IAAEC,IAAEC,IAAEC,IAAE,GAAEC,IAAE,GAAE,IAAE;AAAE,QAAM,IAAE,CAAC,GAAE,IAAE,CAAC;AAAE,aAAU,KAAKZ,GAAE,KAAG,EAAE,CAAC,EAAE,GAAE,KAAK,CAAC;AAAA,WAAUO,OAAIA,KAAE,EAAE,kBAAiBC,KAAE,EAAED,EAAC,GAAEE,KAAEF,GAAE,eAAc,IAAEE,KAAE,SAAO,MAAKC,KAAET,GAAE,CAAC,EAAE,MAAKU,KAAEV,GAAE,CAAC,EAAE,MAAKW,KAAEX,GAAE,CAAC,EAAE,aAAY,IAAEA,GAAE,CAAC,EAAE,eAAcO,GAAE,KAAG,WAAS,EAAE,KAAK,GAAE,KAAK,CAAC;AAAA,WAAU,YAAU,EAAE,KAAK,GAAE,KAAK,EAAE,EAAE,MAAM,GAAEE,IAAEC,EAAC,CAAC;AAAA,WAAU,iBAAe,EAAE,MAAK;AAAC,UAAMX,KAAE,EAAE,MAAM;AAAE,IAAAA,GAAE,SAAOA,GAAE,OAAO,IAAK,CAAAH,OAAG,EAAEA,IAAEa,IAAEC,EAAC,CAAE,GAAE,EAAE,KAAKX,EAAC;AAAA,EAAC,WAAS,aAAW,EAAE,MAAK;AAAC,UAAMA,KAAE,EAAE,MAAM,EAAE,WAAW,OAAG,OAAGQ,EAAC;AAAE,MAAE,KAAKR,GAAE,QAAM,IAAI,EAAEA,EAAC,IAAEA,EAAC;AAAA,EAAC,WAAS,EAAE,QAAO;AAAC,UAAMA,KAAE,EAAE,QAAOK,KAAE,EAAEL,GAAE,MAAKW,EAAC,KAAG,IAAED;AAAG,QAAIX,KAAE,MAAIM,KAAE,EAAE,MAAM,IAAEP,GAAE,EAAE,MAAM,GAAEO,EAAC;AAAE,IAAAL,GAAE,OAAOK,IAAE,CAAC,GAAEL,GAAE,WAAWY,EAAC,KAAGZ,GAAE,SAAOU,MAAG,IAAEV,GAAE,OAAK,IAAEA,GAAE,OAAK,GAAED,KAAEK,GAAEL,IAAEU,EAAC,GAAE,EAAE,KAAKV,EAAC,GAAE,EAAE,KAAK,KAAK,KAAGC,GAAE,WAAW,CAAC,KAAGA,GAAE,SAAOW,MAAG,IAAEX,GAAE,QAAM,IAAEU,MAAG,IAAEV,GAAE,QAAM,IAAEU,MAAG,GAAEX,KAAEK,GAAEL,IAAEU,IAAE,GAAG,GAAE,EAAE,KAAKV,EAAC,GAAE,EAAE,KAAK,KAAK,KAAG,EAAE,KAAKA,EAAC;AAAA,EAAC,MAAM,GAAE,KAAK,EAAE,MAAM,CAAC;AAAA,MAAO,GAAE,KAAK,CAAC;AAAE,MAAI,IAAE,EAAE,GAAEW,EAAC,GAAE,IAAE;AAAI,QAAM,IAAE,GAAE,IAAE,IAAI;AAAE,SAAK,IAAE,KAAG;AAAC,UAAM,IAAE,MAAI,IAAE;AAAI,MAAE,QAAQ,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,KAAG,CAAC,CAAC,CAAC,GAAE,KAAG,IAAG;AAAA,EAAG;AAAC,MAAG,EAAE,SAAO,KAAG,IAAE,GAAE;AAAC,UAAM,IAAE,EAAE,GAAE,MAAMX,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEM,KAAE,CAAC,GAAEN,KAAE,CAAC;AAAE,aAAQI,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,YAAMF,KAAE,EAAEE,EAAC;AAAE,UAAG,UAAQF,GAAE,CAAAF,GAAE,KAAKE,EAAC;AAAA,WAAM;AAAC,cAAMA,KAAE,EAAE,MAAM,GAAEC,KAAEF,GAAEG,EAAC;AAAE,UAAED,EAAC,KAAG,cAAYA,GAAE,QAAMA,GAAE,SAAOA,GAAE,MAAM,SAAO,KAAGD,GAAE,MAAM,UAAQC,GAAE,MAAM,UAAQG,GAAE,KAAKJ,EAAC,GAAEF,GAAE,KAAK,UAAU,KAAGA,GAAE,KAAKU,KAAEL,GAAEH,EAAC,IAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAG,CAACI,GAAE,OAAO,QAAON;AAAE,UAAME,KAAE,MAAMF,GAAE,GAAEM,IAAE,CAAC,GAAEH,KAAE,CAAC;AAAE,aAAQF,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,YAAMH,KAAEE,GAAEC,EAAC;AAAE,qBAAaH,KAAEK,GAAE,KAAKL,EAAC,IAAEK,GAAE,KAAKO,KAAEL,GAAEH,GAAE,MAAM,CAAC,IAAEA,GAAE,MAAM,CAAC;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAC,QAAM,IAAE,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,UAAMF,KAAE,EAAE,CAAC;AAAE,QAAG,UAAQA,GAAE,GAAE,KAAKA,EAAC;AAAA,SAAM;AAAC,YAAMH,KAAE,EAAE,MAAM;AAAE,QAAE,KAAK,SAAKY,KAAEL,GAAEP,EAAC,IAAEA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAimB,SAAS,EAAE,GAAEgB,IAAE;AAAC,QAAM,IAAE,EAAEA,EAAC;AAAE,MAAG,GAAE;AAAC,UAAK,CAACA,IAAEC,EAAC,IAAE,EAAE,OAAMC,KAAED,KAAED;AAAE,QAAG,IAAEA,GAAE,QAAK,IAAEA,KAAG,MAAGE;AAAE,QAAG,IAAED,GAAE,QAAK,IAAEA,KAAG,MAAGC;AAAA,EAAC;AAAC,SAAO;AAAC;", "names": ["s", "e", "s", "o", "t", "r", "i", "l", "R", "n", "v", "x", "d", "w", "j", "M", "U", "t", "o", "s"]}