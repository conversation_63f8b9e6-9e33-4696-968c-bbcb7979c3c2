import {
  I as I3,
  o as o5
} from "./chunk-ETVSAOWN.js";
import {
  J as J2,
  _,
  a as a4,
  e as e5,
  i as i2,
  n as n5,
  r as r7,
  r2 as r8,
  t as t8,
  t2 as t9,
  u as u2,
  y
} from "./chunk-GHAN7X65.js";
import "./chunk-3HDXUZDA.js";
import {
  n as n3
} from "./chunk-ARVYW5FF.js";
import {
  i as i3
} from "./chunk-WKXLAUK5.js";
import "./chunk-WCRONQ5Z.js";
import {
  ae
} from "./chunk-27CJODAI.js";
import "./chunk-L7J6WAZK.js";
import "./chunk-EJ4BPAYT.js";
import "./chunk-JTTSDQPH.js";
import "./chunk-6DXPU43Z.js";
import "./chunk-3MBH7CQT.js";
import "./chunk-6FMMG4VO.js";
import {
  m,
  n as n4,
  t2 as t6,
  t3 as t7,
  w as w2
} from "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import {
  n as n2
} from "./chunk-PV7C75FF.js";
import {
  e2 as e4
} from "./chunk-5LZTDVVY.js";
import {
  o as o3
} from "./chunk-A2EXCZFA.js";
import "./chunk-EDV64J6E.js";
import "./chunk-OBW4AQOU.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-3BVSG4LE.js";
import "./chunk-JJZTA23S.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-VXAO6YJP.js";
import "./chunk-BI4P4NAQ.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-YROREPK5.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-CV76WXPW.js";
import "./chunk-EVADT7ME.js";
import "./chunk-T3GGN2P7.js";
import {
  L as L2
} from "./chunk-2B52LX6T.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import {
  t as t4
} from "./chunk-PI5OC2DP.js";
import "./chunk-LGZKVOWE.js";
import "./chunk-SKYLCTPX.js";
import "./chunk-VK7XO5DN.js";
import "./chunk-WKBMFG6J.js";
import "./chunk-BPRRRPC3.js";
import {
  E2 as E4,
  f as f3,
  s as s2,
  x
} from "./chunk-6G2NLXT7.js";
import "./chunk-RFTQI4ZD.js";
import "./chunk-UHA44FM7.js";
import "./chunk-MDHXGN24.js";
import "./chunk-6ZZUUGXX.js";
import "./chunk-TFWV44LH.js";
import "./chunk-TMGUQ6KD.js";
import {
  t as t3
} from "./chunk-IEBU4QQL.js";
import "./chunk-6OFWBRK2.js";
import "./chunk-JSZR3BUH.js";
import {
  h as h4,
  i,
  o as o4,
  r as r6,
  t as t5
} from "./chunk-QKWIBVLD.js";
import "./chunk-JCXMTMKU.js";
import {
  E as E2,
  S,
  T
} from "./chunk-WAPZ634R.js";
import "./chunk-OHAM27JH.js";
import {
  E as E3
} from "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import {
  B as B2,
  C as C2,
  I as I2,
  J,
  N,
  O as O2,
  Y as Y2,
  bt,
  xt
} from "./chunk-RRNRSHX3.js";
import {
  B,
  C,
  D as D2,
  E,
  F,
  G,
  I,
  L,
  M,
  O,
  P,
  R,
  V,
  Y
} from "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import {
  e as e6,
  m as m2
} from "./chunk-3UMLI77U.js";
import "./chunk-2WS4DQ5K.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import {
  e as e2
} from "./chunk-57ER3SHX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-T7HWQQFI.js";
import "./chunk-DFGMRI52.js";
import "./chunk-OZZFNS32.js";
import "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import {
  e as e3
} from "./chunk-MZ267CZB.js";
import {
  o as o2,
  r as r5
} from "./chunk-QCTKOQ44.js";
import {
  M as M2,
  f as f2,
  h as h3,
  l as l2
} from "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-THUK4WUF.js";
import "./chunk-5ZZCQR67.js";
import "./chunk-RURSJOSG.js";
import "./chunk-OYGWWPGZ.js";
import "./chunk-77E52HT5.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import "./chunk-SROTSYJS.js";
import {
  r as r4
} from "./chunk-FOE4ICAJ.js";
import "./chunk-TNP2LXZZ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import "./chunk-G3QAWKCD.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-WL6G2MRC.js";
import "./chunk-UCWK623G.js";
import {
  j
} from "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import {
  h as h2,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import {
  Bt
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import {
  u
} from "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import {
  a as a3
} from "./chunk-EIGTETCG.js";
import {
  o
} from "./chunk-MQAXMQFG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import {
  r as r3
} from "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  t2
} from "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import {
  A
} from "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  D,
  d,
  f,
  r2
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  a as a2
} from "./chunk-GZGAQUSK.js";
import {
  a,
  e,
  h,
  p,
  r,
  t,
  v,
  w
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/shaders/Programs.js
var t10 = (e7) => n2({ ID: e7.id, PATTERN: e7.pattern });
var a5 = { shaders: (r10) => ({ vertexShader: t10(r10) + n5("background/background.vert"), fragmentShader: t10(r10) + n5("background/background.frag") }) };
var d2 = (e7) => n2({ ID: e7.id });
var i4 = { shaders: (r10) => ({ vertexShader: d2(r10) + n5("circle/circle.vert"), fragmentShader: d2(r10) + n5("circle/circle.frag") }) };
var n6 = (e7) => n2({ ID: e7.id, PATTERN: e7.pattern });
var l3 = { shaders: (r10) => ({ vertexShader: n6(r10) + n5("fill/fill.vert"), fragmentShader: n6(r10) + n5("fill/fill.frag") }) };
var s3 = (e7) => n2({ ID: e7.id });
var f4 = { shaders: (r10) => ({ vertexShader: s3(r10) + n5("outline/outline.vert"), fragmentShader: s3(r10) + n5("outline/outline.frag") }) };
var h5 = (e7) => n2({ ID: e7.id, SDF: e7.sdf });
var o6 = { shaders: (r10) => ({ vertexShader: h5(r10) + n5("icon/icon.vert"), fragmentShader: h5(r10) + n5("icon/icon.frag") }) };
var g = (e7) => n2({ ID: e7.id, PATTERN: e7.pattern, SDF: e7.sdf });
var c = { shaders: (r10) => ({ vertexShader: g(r10) + n5("line/line.vert"), fragmentShader: g(r10) + n5("line/line.frag") }) };
var S2 = (e7) => n2({ ID: e7.id });
var v2 = { shaders: (r10) => ({ vertexShader: S2(r10) + n5("text/text.vert"), fragmentShader: S2(r10) + n5("text/text.frag") }) };

// node_modules/@arcgis/core/views/2d/engine/vectorTiles/shaders/VTLMaterialManager.js
var o7 = class {
  constructor() {
    this._programByKey = /* @__PURE__ */ new Map();
  }
  dispose() {
    this._programByKey.forEach((e7) => e7.dispose()), this._programByKey.clear();
  }
  getMaterialProgram(e7, r10, t11) {
    const a8 = r10.key << 3 | this._getMaterialOptionsValue(r10.type, t11);
    if (this._programByKey.has(a8)) return this._programByKey.get(a8);
    const s6 = this._getProgramTemplate(r10.type), { shaders: n7 } = s6, { vertexShader: c7, fragmentShader: i9 } = n7(t11), o11 = r10.getShaderHeader(), u4 = r10.getShaderMain(), p3 = c7.replace("#pragma header", o11).replace("#pragma main", u4), g2 = e7.programCache.acquire(p3, i9, r10.getAttributeLocations());
    return this._programByKey.set(a8, g2), g2;
  }
  _getMaterialOptionsValue(r10, t11) {
    switch (r10) {
      case L2.BACKGROUND: {
        const e7 = t11;
        return (e7.pattern ? 1 : 0) << 1 | (e7.id ? 1 : 0);
      }
      case L2.FILL: {
        const e7 = t11;
        return (e7.pattern ? 1 : 0) << 1 | (e7.id ? 1 : 0);
      }
      case L2.OUTLINE:
        return t11.id ? 1 : 0;
      case L2.LINE: {
        const e7 = t11;
        return (e7.sdf ? 1 : 0) << 2 | (e7.pattern ? 1 : 0) << 1 | (e7.id ? 1 : 0);
      }
      case L2.ICON: {
        const e7 = t11;
        return (e7.sdf ? 1 : 0) << 1 | (e7.id ? 1 : 0);
      }
      case L2.CIRCLE:
        return t11.id ? 1 : 0;
      case L2.TEXT:
        return t11.id ? 1 : 0;
      default:
        return 0;
    }
  }
  _getProgramTemplate(o11) {
    switch (o11) {
      case L2.BACKGROUND:
        return a5;
      case L2.CIRCLE:
        return i4;
      case L2.FILL:
        return l3;
      case L2.ICON:
        return o6;
      case L2.LINE:
        return c;
      case L2.OUTLINE:
        return f4;
      case L2.TEXT:
        return v2;
      default:
        return null;
    }
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/BitBlitRenderer.js
var _2 = class {
  constructor() {
    this._initialized = false;
  }
  dispose() {
    this._program = h(this._program), this._vertexArrayObject = h(this._vertexArrayObject);
  }
  render(r10, t11, e7, i9) {
    r10 && (this._initialized || this._initialize(r10), r10.setBlendFunctionSeparate(R.ONE, R.ONE_MINUS_SRC_ALPHA, R.ONE, R.ONE_MINUS_SRC_ALPHA), r10.bindVAO(this._vertexArrayObject), r10.useProgram(this._program), t11.setSamplingMode(e7), r10.bindTexture(t11, 0), this._program.setUniform1i("u_tex", 0), this._program.setUniform1f("u_opacity", i9), r10.drawArrays(E.TRIANGLE_STRIP, 0, 4), r10.bindTexture(null, 0), r10.bindVAO());
  }
  _initialize(r10) {
    if (this._initialized) return true;
    const s6 = e4(r10, e5);
    if (!s6) return false;
    const o11 = new Int8Array(16);
    o11[0] = -1, o11[1] = -1, o11[2] = 0, o11[3] = 0, o11[4] = 1, o11[5] = -1, o11[6] = 1, o11[7] = 0, o11[8] = -1, o11[9] = 1, o11[10] = 0, o11[11] = 1, o11[12] = 1, o11[13] = 1, o11[14] = 1, o11[15] = 1;
    const _7 = e5.attributes, p3 = new f3(r10, _7, t7, { geometry: E4.createVertex(r10, F.STATIC_DRAW, o11) });
    return this._program = s6, this._vertexArrayObject = p3, this._initialized = true, true;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/MaterialManager.js
var a6 = (r10) => r10 === T.HITTEST || r10 === T.LABEL_ALPHA;
var s4 = (r10) => (a6(r10) ? 1 : 0) | (r10 === T.HIGHLIGHT ? 2 : 0);
var o8 = ({ rendererInfo: e7, drawPhase: t11 }, a8, o11) => `${a8.getVariationHash()}-${s4(t11)}-${e7.getVariationHash()}-${r(o11) && o11.join(".")}`;
var i5 = (t11, s6, o11, i9 = {}) => {
  if (i9 = { ...i9, ...s6.getVariation(), ...t11.rendererInfo.getVariation(), highlight: t11.drawPhase === T.HIGHLIGHT, id: a6(t11.drawPhase) }, r(o11)) for (const r10 of o11) i9[r10] = true;
  return i9;
};
var h6 = class {
  constructor(r10) {
    this._rctx = r10, this._programByKey = /* @__PURE__ */ new Map();
  }
  dispose() {
    this._programByKey.forEach((r10) => r10.dispose()), this._programByKey.clear();
  }
  getProgram(r10, e7 = []) {
    const a8 = r10.vsPath + "." + r10.fsPath + JSON.stringify(e7);
    if (this._programByKey.has(a8)) return this._programByKey.get(a8);
    const s6 = { ...e7.map((r11) => "string" == typeof r11 ? { name: r11, value: true } : r11).reduce((r11, e8) => ({ ...r11, [e8.name]: e8.value }), {}) }, { vsPath: o11, fsPath: i9, attributes: h8 } = r10, n7 = o5(o11, i9, h8, s6), g2 = this._rctx.programCache.acquire(n7.shaders.vertexShader, n7.shaders.fragmentShader, n7.attributes);
    if (!g2) throw new Error("Unable to get program for key: ${key}");
    return this._programByKey.set(a8, g2), g2;
  }
  getMaterialProgram(r10, e7, a8, s6, h8) {
    const n7 = o8(r10, e7, h8);
    if (this._programByKey.has(n7)) return this._programByKey.get(n7);
    const g2 = i5(r10, e7, h8, { ignoresSamplerPrecision: r10.context.driverTest.ignoresSamplerPrecision.result }), m5 = o5(a8, a8, s6, g2), y2 = this._rctx.programCache.acquire(m5.shaders.vertexShader, m5.shaders.fragmentShader, m5.attributes);
    if (!y2) throw new Error("Unable to get program for key: ${key}");
    return this._programByKey.set(n7, y2), y2;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/TextureUploadManager.js
var i6 = class {
  constructor(e7, t11) {
    this._queue = [], this._context = e7, this._refreshable = t11;
  }
  destroy() {
    this._queue = [];
  }
  enqueueTextureUpdate(e7, r10) {
    const i9 = D(), h8 = e7, a8 = xt, c7 = Math.ceil(h8.height / a8);
    if (f(r10), this._context.type === r5.WEBGL1) this._queue.push({ type: "no-chunk", request: e7, resolver: i9, options: r10 });
    else for (let t11 = 0; t11 < c7; t11++) {
      const s6 = t11 * a8, o11 = t11 === c7 - 1, u4 = o11 ? h8.height - a8 * t11 : a8;
      this._queue.push({ type: "chunk", request: e7, resolver: i9, chunk: t11, chunkOffset: s6, destHeight: u4, chunkIsLast: o11, options: r10 });
    }
    return d(r10, (e8) => i9.reject(e8)), i9.promise;
  }
  upload() {
    let t11 = 0;
    for (; this._queue.length; ) {
      const s6 = performance.now(), o11 = this._queue.shift();
      if (o11) {
        if (r(o11.options.signal) && o11.options.signal.aborted) continue;
        switch (o11.type) {
          case "chunk":
            this._uploadChunk(o11);
            break;
          case "no-chunk":
            this._uploadNoChunk(o11);
        }
        const u4 = performance.now() - s6;
        if (t11 += u4, t11 + u4 >= bt) break;
      }
    }
    this._queue.length && this._refreshable.requestRender();
  }
  _uploadChunk(t11) {
    const { request: s6, resolver: o11, chunkOffset: r10, chunkIsLast: u4, destHeight: n7 } = t11, { data: i9, texture: h8, width: a8 } = s6;
    r(i9) && (h8.updateData(0, 0, r10, a8, n7, i9, r10), u4 && o11.resolve());
  }
  _uploadNoChunk(e7) {
    const { request: t11, resolver: s6 } = e7, { data: o11, texture: r10 } = t11;
    r10.setData(o11), s6.resolve();
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/WorldExtentClipRenderer.js
var u3 = r4(-0.5, -0.5);
var f5 = class {
  constructor() {
    this._centerNdc = n(), this._pxToNdc = n(), this._worldDimensionsPx = n(), this._mat3 = e3(), this._initialized = false;
  }
  dispose() {
    this._program = h(this._program), this._quad = h(this._quad);
  }
  render(t11, s6) {
    const { context: i9 } = t11;
    return !!this._updateGeometry(t11, s6) && (this._initialized || this._initialize(i9), i9.setDepthWriteEnabled(false), i9.setDepthTestEnabled(false), i9.setColorMask(false, false, false, false), i9.setBlendingEnabled(false), i9.setStencilOp(O.KEEP, O.KEEP, O.REPLACE), i9.setStencilFunction(I.ALWAYS, 1, 255), i9.setStencilTestEnabled(true), i9.useProgram(this._program), this._program.setUniformMatrix3fv("u_worldExtent", this._mat3), this._quad.draw(), this._quad.unbind(), true);
  }
  _initialize(t11) {
    if (this._initialized) return;
    const s6 = e4(t11, r7);
    s6 && (this._program = s6, this._quad = new n4(t11, [0, 0, 1, 0, 0, 1, 1, 1]), this._initialized = true);
  }
  _updateGeometry(t11, a8) {
    const { state: n7, pixelRatio: m5 } = t11, { size: c7, rotation: d7 } = n7, p3 = Math.round(c7[0] * m5), _7 = Math.round(c7[1] * m5);
    if (!n7.spatialReference.isWrappable) return false;
    const l6 = r3(d7), f7 = Math.abs(Math.cos(l6)), b2 = Math.abs(Math.sin(l6)), g2 = Math.round(p3 * f7 + _7 * b2), j3 = Math.round(n7.worldScreenWidth);
    if (g2 <= j3) return false;
    const x3 = p3 * b2 + _7 * f7, E5 = j3 * m5, M3 = (a8.left - a8.right) * m5 / p3, w3 = (a8.bottom - a8.top) * m5 / _7;
    o(this._worldDimensionsPx, E5, x3, 1), o(this._pxToNdc, 2 / p3, -2 / _7, 1), o(this._centerNdc, M3, w3, 1);
    const P3 = this._mat3;
    return l2(P3, this._centerNdc), f2(P3, P3, this._pxToNdc), 0 !== d7 && h3(P3, P3, l6), f2(P3, P3, this._worldDimensionsPx), M2(P3, P3, u3), true;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/AnimationEffect.js
var o9 = class extends t6 {
  constructor() {
    super(...arguments), this.defines = [], this._desc = { vsPath: "fx/integrate", fsPath: "fx/integrate", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) };
  }
  dispose() {
    this._quad && this._quad.dispose();
  }
  bind() {
  }
  unbind() {
  }
  draw(r10, i9) {
    if (!(i9 == null ? void 0 : i9.size)) return;
    const { context: a8, renderingOptions: o11 } = r10;
    this._quad || (this._quad = new n4(a8, [0, 0, 1, 0, 0, 1, 1, 1]));
    const n7 = a8.getBoundFramebufferObject(), { x: u4, y: m5, width: d7, height: f7 } = a8.getViewport();
    i9.bindTextures(a8);
    const c7 = i9.getBlock(N);
    if (t(c7)) return;
    const _7 = c7.getFBO(a8), l6 = c7.getFBO(a8, 1);
    a8.setViewport(0, 0, i9.size, i9.size), this._computeDelta(r10, l6, o11.labelsAnimationTime), this._updateAnimationState(r10, l6, _7), a8.bindFramebuffer(n7), a8.setViewport(u4, m5, d7, f7);
  }
  _computeDelta(e7, t11, s6) {
    const { context: a8, painter: o11, displayLevel: n7 } = e7, u4 = o11.materialManager.getProgram(this._desc, ["delta"]);
    a8.bindFramebuffer(t11), a8.setClearColor(0, 0, 0, 0), a8.clear(a8.gl.COLOR_BUFFER_BIT), a8.useProgram(u4), u4.setUniform1i("u_maskTexture", B2), u4.setUniform1i("u_sourceTexture", C2), u4.setUniform1f("u_timeDelta", e7.deltaTime), u4.setUniform1f("u_animationTime", s6), u4.setUniform1f("u_zoomLevel", Math.round(10 * n7)), this._quad.draw();
  }
  _updateAnimationState(e7, t11, r10) {
    const { context: i9, painter: s6 } = e7, a8 = s6.materialManager.getProgram(this._desc, ["update"]);
    i9.bindTexture(t11.colorTexture, 1), i9.useProgram(a8), a8.setUniform1i("u_sourceTexture", 1), i9.bindFramebuffer(r10), i9.setClearColor(0, 0, 0, 0), i9.clear(i9.gl.COLOR_BUFFER_BIT), this._quad.draw();
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/FeatureEffect.js
var r9 = class extends t6 {
  constructor(e7) {
    super(), this.name = this.constructor.name, this.defines = [e7];
  }
  dispose() {
  }
  bind({ context: e7, painter: t11 }) {
    this._prev = e7.getBoundFramebufferObject();
    const { width: r10, height: s6 } = e7.getViewport(), o11 = t11.getFbos(r10, s6).effect0;
    e7.bindFramebuffer(o11), e7.setColorMask(true, true, true, true), e7.setClearColor(0, 0, 0, 0), e7.clear(e7.gl.COLOR_BUFFER_BIT);
  }
  unbind() {
  }
  draw(e7, r10) {
    const { context: s6, painter: o11 } = e7, n7 = o11.getPostProcessingEffects(r10), c7 = s6.getBoundFramebufferObject();
    for (const { postProcessingEffect: t11, effect: f7 } of n7) t11.draw(e7, c7, f7);
    s6.bindFramebuffer(this._prev), s6.setStencilTestEnabled(false), o11.blitTexture(s6, c7.colorTexture, L.NEAREST), s6.setStencilTestEnabled(true);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/highlight/HighlightRenderer.js
var c2 = class {
  constructor() {
    this._width = void 0, this._height = void 0, this._resources = null;
  }
  dispose() {
    this._resources && (this._resources.quadGeometry.dispose(), this._resources.quadVAO.dispose(), this._resources.highlightProgram.dispose(), this._resources.blurProgram.dispose(), this._resources = null);
  }
  preBlur(e7, i9) {
    e7.bindTexture(i9, I2), e7.useProgram(this._resources.blurProgram), this._resources.blurProgram.setUniform4fv("u_direction", [1, 0, 1 / this._width, 0]), this._resources.blurProgram.setUniformMatrix4fv("u_channelSelector", t5), e7.bindVAO(this._resources.quadVAO), e7.drawArrays(E.TRIANGLE_STRIP, 0, 4), e7.bindVAO();
  }
  finalBlur(e7, s6) {
    e7.bindTexture(s6, I2), e7.useProgram(this._resources.blurProgram), this._resources.blurProgram.setUniform4fv("u_direction", [0, 1, 0, 1 / this._height]), this._resources.blurProgram.setUniformMatrix4fv("u_channelSelector", i), e7.bindVAO(this._resources.quadVAO), e7.drawArrays(E.TRIANGLE_STRIP, 0, 4), e7.bindVAO();
  }
  renderHighlight(e7, s6, i9) {
    e7.bindTexture(s6, I2), e7.useProgram(this._resources.highlightProgram), i9.applyHighlightOptions(e7, this._resources.highlightProgram), e7.bindVAO(this._resources.quadVAO), e7.setBlendingEnabled(true), e7.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), e7.drawArrays(E.TRIANGLE_STRIP, 0, 4), e7.bindVAO();
  }
  _initialize(s6, i9, a8) {
    this._width = i9, this._height = a8;
    const m5 = E4.createVertex(s6, F.STATIC_DRAW, new Int8Array([-1, -1, 0, 0, 1, -1, 1, 0, -1, 1, 0, 1, 1, 1, 1, 1]).buffer), c7 = new f3(s6, /* @__PURE__ */ new Map([["a_position", 0], ["a_texcoord", 1]]), { geometry: [new t3("a_position", 2, C.BYTE, 0, 4), new t3("a_texcoord", 2, C.UNSIGNED_BYTE, 2, 4)] }, { geometry: m5 }), f7 = e4(s6, t8), b2 = e4(s6, r8);
    s6.useProgram(f7), f7.setUniform1i("u_texture", I2), f7.setUniform1i("u_shade", J), f7.setUniform1f("u_sigma", o4), s6.useProgram(b2), b2.setUniform1i("u_texture", I2), b2.setUniform1f("u_sigma", o4), this._resources = { quadGeometry: m5, quadVAO: c7, highlightProgram: f7, blurProgram: b2 };
  }
  setup(r10, e7, s6) {
    this._resources ? (this._width = e7, this._height = s6) : this._initialize(r10, e7, s6);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/highlight/HighlightSurfaces.js
function l4(e7, l6, _7) {
  const c7 = new E3(e7, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, width: l6, height: _7, samplingMode: L.LINEAR });
  return [c7, new x(e7, { colorTarget: Y.TEXTURE, depthStencilTarget: V.STENCIL_RENDER_BUFFER }, c7)];
}
var _3 = class {
  constructor() {
    this._width = void 0, this._height = void 0, this._resources = null;
  }
  dispose() {
    this._resources && (this._resources.sharedBlur1Tex.dispose(), this._resources.sharedBlur1Fbo.dispose(), this._resources.sharedBlur2Tex.dispose(), this._resources.sharedBlur2Fbo.dispose(), this._resources = v(this._resources));
  }
  _initialize(e7, s6, r10) {
    this._width = s6, this._height = r10;
    const [t11, i9] = l4(e7, s6, r10), [h8, o11] = l4(e7, s6, r10);
    this._resources = { sharedBlur1Tex: t11, sharedBlur1Fbo: i9, sharedBlur2Tex: h8, sharedBlur2Fbo: o11 };
  }
  setup(e7, s6, r10) {
    !this._resources || this._width === s6 && this._height === r10 || this.dispose(), this._resources || this._initialize(e7, s6, r10);
  }
  get sharedBlur1Tex() {
    return this._resources.sharedBlur1Tex;
  }
  get sharedBlur1Fbo() {
    return this._resources.sharedBlur1Fbo;
  }
  get sharedBlur2Tex() {
    return this._resources.sharedBlur2Tex;
  }
  get sharedBlur2Fbo() {
    return this._resources.sharedBlur2Fbo;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/HighlightEffect.js
var h7 = 4;
var d3 = 4 / h7;
var l5 = class extends t6 {
  constructor() {
    super(...arguments), this.defines = ["highlight"], this._hlRenderer = new c2(), this._width = void 0, this._height = void 0, this._boundFBO = null, this._hlSurfaces = new _3(), this._adjustedWidth = void 0, this._adjustedHeight = void 0, this._blitRenderer = new _2();
  }
  dispose() {
    var _a, _b;
    (_a = this._hlSurfaces) == null ? void 0 : _a.dispose(), (_b = this._hlRenderer) == null ? void 0 : _b.dispose(), this._boundFBO = null;
  }
  bind(e7) {
    const { context: t11, painter: s6 } = e7, { width: r10, height: i9 } = t11.getViewport(), h8 = s6.getFbos(r10, i9).effect0;
    this.setup(e7, r10, i9), t11.bindFramebuffer(h8), t11.setColorMask(true, true, true, true), t11.setClearColor(0, 0, 0, 0), t11.clear(t11.gl.COLOR_BUFFER_BIT);
  }
  unbind() {
  }
  setup({ context: e7 }, t11, s6) {
    this._width = t11, this._height = s6;
    const r10 = t11 % h7, i9 = s6 % h7;
    t11 += r10 < h7 / 2 ? -r10 : h7 - r10, s6 += i9 < h7 / 2 ? -i9 : h7 - i9, this._adjustedWidth = t11, this._adjustedHeight = s6, this._boundFBO = e7.getBoundFramebufferObject();
    const l6 = Math.round(t11 * d3), o11 = Math.round(s6 * d3);
    this._hlRenderer.setup(e7, l6, o11), this._hlSurfaces.setup(e7, l6, o11);
  }
  draw(e7) {
    const { context: t11, highlightGradient: s6 } = e7;
    if (!s6) return;
    const r10 = t11.getBoundFramebufferObject();
    t11.setViewport(0, 0, this._adjustedWidth * d3, this._adjustedHeight * d3), t11.bindFramebuffer(this._hlSurfaces.sharedBlur1Fbo), t11.setStencilTestEnabled(false), t11.setClearColor(0, 0, 0, 0), t11.clear(t11.gl.COLOR_BUFFER_BIT), this._blitRenderer.render(t11, r10.colorTexture, L.NEAREST, 1), t11.setStencilTestEnabled(false), t11.setBlendingEnabled(false), t11.setColorMask(false, false, false, true), t11.bindFramebuffer(this._hlSurfaces.sharedBlur2Fbo), t11.setClearColor(0, 0, 0, 0), t11.clear(t11.gl.COLOR_BUFFER_BIT), this._hlRenderer.preBlur(t11, this._hlSurfaces.sharedBlur1Tex), t11.bindFramebuffer(this._hlSurfaces.sharedBlur1Fbo), t11.setClearColor(0, 0, 0, 0), t11.clear(t11.gl.COLOR_BUFFER_BIT), this._hlRenderer.finalBlur(t11, this._hlSurfaces.sharedBlur2Tex), t11.bindFramebuffer(this._boundFBO), t11.setBlendingEnabled(true), t11.setColorMask(true, true, true, true), t11.setViewport(0, 0, this._width, this._height), this._hlRenderer.renderHighlight(t11, this._hlSurfaces.sharedBlur1Tex, s6), this._boundFBO = null;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/HittestEffect.js
var c3 = class extends t6 {
  constructor() {
    super(...arguments), this.name = this.constructor.name, this.defines = ["hittest"];
  }
  dispose() {
    r(this._fbo) && this._fbo.dispose();
  }
  createOptions({ pixelRatio: t11 }, e7, s6 = Y2) {
    if (!e7.length) return null;
    const r10 = e7.shift(), o11 = r10.x, n7 = r10.y;
    return this._outstanding = r10, { type: "hittest", distance: s6 * t11, position: [o11, n7] };
  }
  bind(t11) {
    const { context: i9, attributeView: r10 } = t11;
    if (!r10.size) return;
    const o11 = r10.getBlock(O2);
    if (t(o11)) return;
    const n7 = o11.getFBO(i9);
    i9.setViewport(0, 0, r10.size, r10.size), i9.bindFramebuffer(n7), i9.setColorMask(true, true, true, true), i9.setClearColor(0, 0, 0, 0), i9.clear(i9.gl.COLOR_BUFFER_BIT | i9.gl.DEPTH_BUFFER_BIT);
  }
  unbind(t11) {
  }
  draw(t11) {
    if (t(this._outstanding)) return;
    const i9 = this._outstanding;
    this._outstanding = null, this._resolve(t11, i9.resolvers);
  }
  async _resolve(t11, i9) {
    const { context: r10, attributeView: c7 } = t11, a8 = c7.getBlock(O2);
    if (t(a8)) return void i9.forEach((t12) => t12.resolve([]));
    const d7 = a8.getFBO(r10), h8 = new Uint8Array(d7.width * d7.height * 4);
    try {
      await d7.readPixelsAsync(0, 0, d7.width, d7.height, P.RGBA, G.UNSIGNED_BYTE, h8);
    } catch (u4) {
      return void i9.forEach((t12) => t12.resolve([]));
    }
    const l6 = [];
    for (let e7 = 0; e7 < h8.length; e7 += 4) {
      const t12 = h8[e7], i10 = h8[e7 + 3];
      t12 && l6.push({ id: e7 / 4, directHits: i10 });
    }
    l6.sort((t12, e7) => e7.directHits === t12.directHits ? e7.id - t12.id : e7.directHits - t12.directHits);
    const f7 = l6.map((t12) => t12.id);
    i9.forEach((t12) => t12.resolve(f7));
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/HittestEffectVTL.js
var i7 = class extends t6 {
  constructor() {
    super(...arguments), this.name = this.constructor.name, this.defines = ["id"], this._lastSize = 0, this._boundFBO = null;
  }
  dispose() {
    r(this._fbo) && this._fbo.dispose();
  }
  bind({ context: t11, painter: e7 }) {
    const { width: s6, height: o11 } = t11.getViewport();
    this._boundFBO = t11.getBoundFramebufferObject();
    const r10 = e7.getFbos(s6, o11).effect0;
    t11.bindFramebuffer(r10), t11.setColorMask(true, true, true, true), t11.setClearColor(0, 0, 0, 0), t11.clear(t11.gl.COLOR_BUFFER_BIT);
  }
  unbind({ context: t11 }) {
    t11.bindFramebuffer(this._boundFBO), this._boundFBO = null;
  }
  draw(t11, s6, o11 = 2 * Y2) {
    this._resolve(t11, s6, o11);
  }
  async _resolve({ context: t11, state: e7, pixelRatio: s6 }, i9, n7) {
    const f7 = t11.getBoundFramebufferObject(), a8 = e7.size[1] * s6, h8 = Math.round(n7 * s6), u4 = h8 / 2, b2 = h8 / 2;
    this._ensureBuffer(h8), i9.forEach(async (t12, e8) => {
      const n8 = /* @__PURE__ */ new Map(), c7 = Math.floor(e8.x * s6 - h8 / 2), l6 = Math.floor(a8 - e8.y * s6 - h8 / 2);
      await f7.readPixelsAsync(c7, l6, h8, h8, P.RGBA, G.UNSIGNED_BYTE, this._buf);
      for (let s7 = 0; s7 < this._buf32.length; s7++) {
        const t13 = this._buf32[s7];
        if (4294967295 !== t13 && 0 !== t13) {
          const e9 = s7 % h8, o11 = h8 - Math.floor(s7 / h8), r10 = (u4 - e9) * (u4 - e9) + (b2 - o11) * (b2 - o11), i10 = n8.has(t13) ? n8.get(t13) : 4294967295;
          n8.set(t13, Math.min(r10, i10));
        }
      }
      const _7 = Array.from(n8).sort((t13, e9) => t13[1] - e9[1]).map((t13) => t13[0]);
      t12.resolve(_7), i9.delete(e8);
    });
  }
  _ensureBuffer(t11) {
    this._lastSize !== t11 && (this._lastSize = t11, this._buf = new Uint8Array(4 * t11 * t11), this._buf32 = new Uint32Array(this._buf.buffer));
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/post-processing/Bloom.js
var p2 = 5;
var m3 = [1, 0];
var _4 = [0, 1];
var c4 = [1, 0.8, 0.6, 0.4, 0.2];
var d4 = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
var T2 = class {
  constructor() {
    this._intensityFBO = null, this._compositeFBO = null, this._mipsFBOs = new Array(p2), this._nMips = p2, this._kernelSizeArray = [3, 5, 7, 9, 11], this._size = [0, 0], this._programDesc = { luminosityHighPass: { vsPath: "post-processing/pp", fsPath: "post-processing/bloom/luminosityHighPass", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) }, gaussianBlur: { vsPath: "post-processing/pp", fsPath: "post-processing/bloom/gaussianBlur", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) }, composite: { vsPath: "post-processing/pp", fsPath: "post-processing/bloom/composite", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) }, blit: { vsPath: "post-processing/pp", fsPath: "post-processing/blit", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) } };
  }
  dispose() {
    if (this._quad = h(this._quad), this._intensityFBO = h(this._intensityFBO), this._compositeFBO = h(this._compositeFBO), this._mipsFBOs) {
      for (let t11 = 0; t11 < this._nMips; t11++) this._mipsFBOs[t11] && (this._mipsFBOs[t11].horizontal.dispose(), this._mipsFBOs[t11].vertical.dispose());
      this._mipsFBOs = null;
    }
  }
  draw(t11, s6, r10) {
    const { width: o11, height: n7 } = s6, { context: a8, painter: h8 } = t11, { materialManager: l6 } = h8, u4 = a8.gl, T3 = this._programDesc, { strength: f7, radius: g2, threshold: B3 } = r10;
    this._quad || (this._quad = new n4(a8, [-1, -1, 1, -1, -1, 1, 1, 1])), this._createOrResizeResources(t11, o11, n7), a8.setStencilTestEnabled(false), a8.setBlendingEnabled(true), a8.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), a8.setStencilWriteMask(0);
    const O3 = this._quad;
    O3.bind(), a8.bindFramebuffer(this._intensityFBO);
    const F2 = l6.getProgram(T3.luminosityHighPass);
    a8.useProgram(F2), a8.bindTexture(s6.colorTexture, 0), F2.setUniform1i("u_texture", 0), F2.setUniform3fv("u_defaultColor", [0, 0, 0]), F2.setUniform1f("u_defaultOpacity", 0), F2.setUniform1f("u_luminosityThreshold", B3), F2.setUniform1f("u_smoothWidth", 0.01);
    const b2 = [Math.round(o11 / 2), Math.round(n7 / 2)];
    a8.setViewport(0, 0, b2[0], b2[1]), a8.setClearColor(0, 0, 0, 0), a8.clear(u4.COLOR_BUFFER_BIT), O3.draw(), a8.setBlendingEnabled(false);
    let E5 = this._intensityFBO.colorTexture;
    for (let e7 = 0; e7 < this._nMips; e7++) {
      const t12 = l6.getProgram(T3.gaussianBlur, [{ name: "radius", value: this._kernelSizeArray[e7] }]);
      a8.useProgram(t12), a8.bindTexture(E5, e7 + 1), t12.setUniform1i("u_colorTexture", e7 + 1), t12.setUniform2fv("u_texSize", b2), t12.setUniform2fv("u_direction", m3), a8.setViewport(0, 0, b2[0], b2[1]);
      const i9 = this._mipsFBOs[e7];
      a8.bindFramebuffer(i9.horizontal), O3.draw(), E5 = i9.horizontal.colorTexture, a8.bindFramebuffer(i9.vertical), a8.bindTexture(E5, e7 + 1), t12.setUniform2fv("u_direction", _4), O3.draw(), E5 = i9.vertical.colorTexture, b2[0] = Math.round(b2[0] / 2), b2[1] = Math.round(b2[1] / 2);
    }
    a8.setViewport(0, 0, o11, n7);
    const x3 = l6.getProgram(T3.composite, [{ name: "nummips", value: p2 }]);
    a8.bindFramebuffer(this._compositeFBO), a8.useProgram(x3), x3.setUniform1f("u_bloomStrength", f7), x3.setUniform1f("u_bloomRadius", g2), x3.setUniform1fv("u_bloomFactors", c4), x3.setUniform3fv("u_bloomTintColors", d4), a8.bindTexture(this._mipsFBOs[0].vertical.colorTexture, 1), x3.setUniform1i("u_blurTexture1", 1), a8.bindTexture(this._mipsFBOs[1].vertical.colorTexture, 2), x3.setUniform1i("u_blurTexture2", 2), a8.bindTexture(this._mipsFBOs[2].vertical.colorTexture, 3), x3.setUniform1i("u_blurTexture3", 3), a8.bindTexture(this._mipsFBOs[3].vertical.colorTexture, 4), x3.setUniform1i("u_blurTexture4", 4), a8.bindTexture(this._mipsFBOs[4].vertical.colorTexture, 5), x3.setUniform1i("u_blurTexture5", 5), O3.draw(), a8.bindFramebuffer(s6), a8.setBlendingEnabled(true);
    const w3 = l6.getProgram(T3.blit);
    a8.useProgram(w3), a8.bindTexture(this._compositeFBO.colorTexture, 6), w3.setUniform1i("u_texture", 6), a8.setBlendFunction(R.ONE, R.ONE), O3.draw(), O3.unbind(), a8.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), a8.setStencilTestEnabled(true);
  }
  _createOrResizeResources(t11, e7, i9) {
    const { context: p3 } = t11;
    if (this._compositeFBO && this._size[0] === e7 && this._size[1] === i9) return;
    this._size[0] = e7, this._size[1] = i9;
    const m5 = [Math.round(e7 / 2), Math.round(i9 / 2)];
    this._compositeFBO ? this._compositeFBO.resize(e7, i9) : this._compositeFBO = new x(p3, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE, width: e7, height: i9 }), this._intensityFBO ? this._intensityFBO.resize(m5[0], m5[1]) : this._intensityFBO = new x(p3, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE, width: m5[0], height: m5[1] }, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: m5[0], height: m5[1] });
    for (let _7 = 0; _7 < this._nMips; _7++) this._mipsFBOs[_7] ? (this._mipsFBOs[_7].horizontal.resize(m5[0], m5[1]), this._mipsFBOs[_7].vertical.resize(m5[0], m5[1])) : this._mipsFBOs[_7] = { horizontal: new x(p3, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE, width: m5[0], height: m5[1] }, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: m5[0], height: m5[1] }), vertical: new x(p3, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE, width: m5[0], height: m5[1] }, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: m5[0], height: m5[1] }) }, m5[0] = Math.round(m5[0] / 2), m5[1] = Math.round(m5[1] / 2);
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/post-processing/Blur.js
var d5 = [1, 0];
var _5 = [0, 1];
var b = class {
  constructor() {
    this._blurFBO = null, this._size = [0, 0], this._programDesc = { gaussianBlur: { vsPath: "post-processing/pp", fsPath: "post-processing/blur/gaussianBlur", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) }, radialBlur: { vsPath: "post-processing/pp", fsPath: "post-processing/blur/radial-blur", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) }, blit: { vsPath: "post-processing/pp", fsPath: "post-processing/blit", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) } };
  }
  dispose() {
    this._blurFBO && (this._blurFBO.dispose(), this._blurFBO = null);
  }
  draw(t11, r10, s6) {
    const { context: i9 } = t11, { type: a8, radius: n7 } = s6;
    if (0 === n7) return;
    this._createOrResizeResources(t11), this._quad || (this._quad = new n4(i9, [-1, -1, 1, -1, -1, 1, 1, 1]));
    const o11 = this._quad;
    o11.bind(), "blur" === a8 ? this._gaussianBlur(t11, r10, n7) : this._radialBlur(t11, r10), o11.unbind();
  }
  _gaussianBlur(e7, r10, s6) {
    const { context: i9, state: a8, painter: n7, pixelRatio: o11 } = e7, { size: u4 } = a8, { materialManager: l6 } = n7, b2 = this._programDesc, p3 = this._quad, c7 = [Math.round(o11 * u4[0]), Math.round(o11 * u4[1])], h8 = this._blurFBO, g2 = l6.getProgram(b2.gaussianBlur, [{ name: "radius", value: Math.ceil(s6) }]);
    i9.useProgram(g2), i9.setBlendingEnabled(false), i9.bindFramebuffer(h8), i9.bindTexture(r10.colorTexture, 4), g2.setUniform1i("u_colorTexture", 4), g2.setUniform2fv("u_texSize", c7), g2.setUniform2fv("u_direction", d5), g2.setUniform1f("u_sigma", s6), p3.draw(), i9.bindFramebuffer(r10), i9.setStencilWriteMask(0), i9.setStencilTestEnabled(false), i9.setDepthWriteEnabled(false), i9.setDepthTestEnabled(false), i9.bindTexture(h8 == null ? void 0 : h8.colorTexture, 5), g2.setUniform1i("u_colorTexture", 5), g2.setUniform2fv("u_direction", _5), p3.draw(), i9.setBlendingEnabled(true), i9.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), i9.setStencilTestEnabled(true);
  }
  _radialBlur(e7, r10) {
    const { context: s6, painter: i9 } = e7, { materialManager: a8 } = i9, n7 = this._programDesc, o11 = this._quad, u4 = this._blurFBO;
    s6.bindFramebuffer(u4);
    const l6 = a8.getProgram(n7.radialBlur);
    s6.useProgram(l6), s6.setBlendingEnabled(false), s6.bindTexture(r10.colorTexture, 4), l6.setUniform1i("u_colorTexture", 4), o11.draw(), s6.bindFramebuffer(r10), s6.setStencilWriteMask(0), s6.setStencilTestEnabled(false), s6.setDepthWriteEnabled(false), s6.setDepthTestEnabled(false), s6.setBlendingEnabled(true);
    const d7 = a8.getProgram(n7.blit);
    s6.useProgram(d7), s6.bindTexture(u4 == null ? void 0 : u4.colorTexture, 5), d7.setUniform1i("u_texture", 5), s6.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), o11.draw();
  }
  _createOrResizeResources(e7) {
    const { context: t11, state: d7, pixelRatio: _7 } = e7, { size: b2 } = d7, p3 = Math.round(_7 * b2[0]), c7 = Math.round(_7 * b2[1]);
    this._blurFBO && this._size[0] === p3 && this._size[1] === c7 || (this._size[0] = p3, this._size[1] = c7, this._blurFBO ? this._blurFBO.resize(p3, c7) : this._blurFBO = new x(t11, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE, width: p3, height: c7 }, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: p3, height: c7 }));
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/post-processing/Colorize.js
var _6 = class {
  constructor() {
    this._layerFBOTexture = null, this._size = [0, 0], this._programDesc = { vsPath: "post-processing/pp", fsPath: "post-processing/filterEffect", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) };
  }
  dispose() {
    this._layerFBOTexture = h(this._layerFBOTexture);
  }
  draw(e7, t11, s6) {
    const { width: i9, height: a8 } = t11;
    this._createOrResizeResources(e7, i9, a8);
    const { context: o11, painter: n7 } = e7, { materialManager: l6 } = n7, _7 = this._programDesc, c7 = this._quad, u4 = s6.colorMatrix;
    c7.bind();
    const h8 = this._layerFBOTexture;
    o11.bindFramebuffer(t11), t11.copyToTexture(0, 0, i9, a8, 0, 0, h8), o11.setBlendingEnabled(false), o11.setStencilTestEnabled(false);
    const p3 = l6.getProgram(_7);
    o11.useProgram(p3), o11.bindTexture(h8, 2), p3.setUniformMatrix4fv("u_coefficients", u4), p3.setUniform1i("u_colorTexture", 2), c7.draw(), o11.setBlendingEnabled(true), o11.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), o11.setStencilTestEnabled(true), c7.unbind();
  }
  _createOrResizeResources(e7, r10, _7) {
    const { context: c7 } = e7;
    this._layerFBOTexture && this._size[0] === r10 && this._size[1] === _7 || (this._size[0] = r10, this._size[1] = _7, this._layerFBOTexture ? this._layerFBOTexture.resize(r10, _7) : this._layerFBOTexture = new E3(c7, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: r10, height: _7 }), this._quad || (this._quad = new n4(c7, [-1, -1, 1, -1, -1, 1, 1, 1])));
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/post-processing/DropShadow.js
var d6 = [1, 0];
var c5 = [0, 1];
var m4 = class {
  constructor() {
    this._layerFBOTexture = null, this._horizontalBlurFBO = null, this._verticalBlurFBO = null, this._size = [0, 0], this._quad = null, this._programDesc = { blur: { vsPath: "post-processing/pp", fsPath: "post-processing/blur/gaussianBlur", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) }, composite: { vsPath: "post-processing/pp", fsPath: "post-processing/drop-shadow/composite", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) }, blit: { vsPath: "post-processing/pp", fsPath: "post-processing/blit", attributes: /* @__PURE__ */ new Map([["a_position", 0]]) } };
  }
  dispose() {
    this._layerFBOTexture = h(this._layerFBOTexture), this._horizontalBlurFBO = h(this._horizontalBlurFBO), this._verticalBlurFBO = h(this._verticalBlurFBO);
  }
  draw(e7, s6, o11) {
    const { context: a8, state: l6, painter: n7 } = e7, { materialManager: u4 } = n7, h8 = this._programDesc, p3 = s6.width, _7 = s6.height, m5 = [Math.round(p3), Math.round(_7)], { blurRadius: B3, offsetX: T3, offsetY: f7, color: g2 } = o11, E5 = [u(T3), u(f7)];
    this._createOrResizeResources(e7, p3, _7, m5);
    const b2 = this._horizontalBlurFBO, F2 = this._verticalBlurFBO;
    a8.setStencilWriteMask(0), a8.setStencilTestEnabled(false), a8.setDepthWriteEnabled(false), a8.setDepthTestEnabled(false);
    const O3 = this._layerFBOTexture;
    s6.copyToTexture(0, 0, p3, _7, 0, 0, O3), this._quad || (this._quad = new n4(a8, [-1, -1, 1, -1, -1, 1, 1, 1])), a8.setViewport(0, 0, m5[0], m5[1]);
    const x3 = this._quad;
    x3.bind(), a8.setBlendingEnabled(false);
    const w3 = u4.getProgram(h8.blur, [{ name: "radius", value: Math.ceil(B3) }]);
    a8.useProgram(w3), a8.bindFramebuffer(b2), a8.bindTexture(s6.colorTexture, 4), w3.setUniform1i("u_colorTexture", 4), w3.setUniform2fv("u_texSize", m5), w3.setUniform2fv("u_direction", d6), w3.setUniform1f("u_sigma", B3), x3.draw(), a8.bindFramebuffer(F2), a8.bindTexture(b2 == null ? void 0 : b2.colorTexture, 5), w3.setUniform1i("u_colorTexture", 5), w3.setUniform2fv("u_direction", c5), x3.draw(), a8.bindFramebuffer(s6), a8.setViewport(0, 0, p3, _7);
    const M3 = u4.getProgram(h8.composite);
    a8.useProgram(M3), a8.bindTexture(F2 == null ? void 0 : F2.colorTexture, 2), M3.setUniform1i("u_blurTexture", 2), a8.bindTexture(O3, 3), M3.setUniform1i("u_layerFBOTexture", 3), M3.setUniform4fv("u_shadowColor", [g2[3] * (g2[0] / 255), g2[3] * (g2[1] / 255), g2[3] * (g2[2] / 255), g2[3]]), M3.setUniformMatrix3fv("u_displayViewMat3", l6.displayMat3), M3.setUniform2fv("u_shadowOffset", E5), x3.draw(), a8.setBlendingEnabled(true), a8.setStencilTestEnabled(true), a8.setBlendFunction(R.ONE, R.ONE_MINUS_SRC_ALPHA), x3.unbind();
  }
  _createOrResizeResources(e7, t11, r10, i9) {
    const { context: d7 } = e7;
    this._horizontalBlurFBO && this._size[0] === t11 && this._size[1] === r10 || (this._size[0] = t11, this._size[1] = r10, this._horizontalBlurFBO ? this._horizontalBlurFBO.resize(i9[0], i9[1]) : this._horizontalBlurFBO = new x(d7, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE, width: i9[0], height: i9[1] }, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: i9[0], height: i9[1] }), this._verticalBlurFBO ? this._verticalBlurFBO.resize(i9[0], i9[1]) : this._verticalBlurFBO = new x(d7, { colorTarget: Y.TEXTURE, depthStencilTarget: V.NONE, width: i9[0], height: i9[1] }, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: i9[0], height: i9[1] }), this._layerFBOTexture ? this._layerFBOTexture.resize(t11, r10) : this._layerFBOTexture = new E3(d7, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: t11, height: r10 }));
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/post-processing/Opacity.js
var o10 = class {
  constructor() {
    this._size = [0, 0], this._layerFBOTexture = null;
  }
  dispose() {
    this._layerFBOTexture = h(this._layerFBOTexture);
  }
  draw(e7, r10, s6) {
    const { width: i9, height: a8 } = r10;
    this._createOrResizeResources(e7, i9, a8);
    const { context: l6, painter: o11 } = e7, { amount: T3 } = s6, h8 = l6.gl, n7 = this._layerFBOTexture;
    l6.bindFramebuffer(r10), r10.copyToTexture(0, 0, i9, a8, 0, 0, n7), l6.setBlendingEnabled(true), l6.setStencilTestEnabled(false), l6.setDepthTestEnabled(false), l6.setClearColor(0, 0, 0, 0), l6.clear(h8.COLOR_BUFFER_BIT), o11.blitTexture(l6, n7, L.NEAREST, T3);
  }
  _createOrResizeResources(e7, o11, T3) {
    const { context: h8 } = e7;
    this._layerFBOTexture && this._size[0] === o11 && this._size[1] === T3 || (this._size[0] = o11, this._size[1] = T3, this._layerFBOTexture ? this._layerFBOTexture.resize(o11, T3) : this._layerFBOTexture = new E3(h8, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.NEAREST, flipped: false, width: o11, height: T3 }));
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/effects/post-processing/EffectManager.js
function c6(o11) {
  switch (o11) {
    case "bloom":
    case "blur":
    case "opacity":
    case "drop-shadow":
      return o11;
    default:
      return "colorize";
  }
}
var f6 = { colorize: () => new _6(), blur: () => new b(), bloom: () => new T2(), opacity: () => new o10(), "drop-shadow": () => new m4() };
var i8 = class {
  constructor() {
    this._effectMap = /* @__PURE__ */ new Map();
  }
  dispose() {
    this._effectMap.forEach((o11) => o11.dispose()), this._effectMap.clear();
  }
  getPostProcessingEffects(o11) {
    if (!o11 || 0 === o11.length) return [];
    const e7 = [];
    for (const t11 of o11) {
      const o12 = c6(t11.type);
      let s6 = this._effectMap.get(o12);
      s6 || (s6 = f6[o12](), this._effectMap.set(o12, s6)), e7.push({ postProcessingEffect: s6, effect: t11 });
    }
    return e7;
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/painter/RenderPass.js
var a7 = class {
  constructor(e7, t11) {
    this.brushes = e7, this.name = t11.name, this.drawPhase = t11.drawPhase || T.MAP, this._targetFn = t11.target, this.effects = t11.effects || [], this.enableDefaultDraw = t11.enableDefaultDraw ?? (() => true);
  }
  render(e7) {
    const { context: t11, profiler: r10 } = e7, s6 = this._targetFn(), a8 = this.drawPhase & e7.drawPhase;
    if (r10.recordPassStart(this.name), a8) {
      this.enableDefaultDraw() && this._doRender(e7, s6), r10.recordPassEnd();
      for (const r11 of this.effects) {
        if (!r11.enable()) continue;
        const a9 = r11.apply, n7 = r11.args && r11.args(), i9 = t11.getViewport(), o11 = t11.getBoundFramebufferObject(), f7 = e7.passOptions;
        this._bindEffect(e7, a9, n7), this._doRender(e7, s6, a9.defines), this._drawAndUnbindEffect(e7, a9, i9, o11, f7, n7);
      }
    }
  }
  _doRender(e7, s6, a8) {
    if (t(s6)) return;
    const { profiler: n7, context: i9 } = e7;
    for (const t11 of this.brushes) {
      if (n7.recordBrushStart(t11.name), r(t11.brushEffect)) {
        const r10 = i9.getViewport(), n8 = i9.getBoundFramebufferObject(), o11 = e7.passOptions;
        this._bindEffect(e7, t11.brushEffect), this._drawWithBrush(t11, e7, s6, a8), this._drawAndUnbindEffect(e7, t11.brushEffect, r10, n8, o11);
      } else this._drawWithBrush(t11, e7, s6, a8);
      n7.recordBrushEnd();
    }
  }
  _drawWithBrush(t11, r10, s6, a8) {
    a2(s6) ? (t11.prepareState(r10, a8), t11.drawMany(r10, s6, a8)) : s6.visible && (t11.prepareState(r10, a8), t11.draw(r10, s6, a8));
  }
  _bindEffect(e7, t11, r10) {
    const { profiler: s6 } = e7;
    s6.recordPassStart(this.name + "." + t11.name), t11.bind(e7, r10);
    const a8 = t11.createOptions(e7, r10);
    e7.passOptions = a8;
  }
  _drawAndUnbindEffect(e7, t11, r10, s6, a8, n7) {
    const { profiler: i9, context: o11 } = e7;
    e7.passOptions = a8, i9.recordBrushStart(t11.name), t11.draw(e7, n7), t11.unbind(e7, n7), o11.bindFramebuffer(s6);
    const { x: f7, y: d7, width: h8, height: c7 } = r10;
    o11.setViewport(f7, d7, h8, c7), i9.recordBrushEnd(), i9.recordPassEnd();
  }
};

// node_modules/@arcgis/core/views/2d/engine/webgl/Painter.js
function I4(e7, t11) {
  switch (e7) {
    case E2.LINE:
      return w2.line;
    case E2.TEXT:
      return w2.text;
    case E2.LABEL:
      return w2.label;
    case E2.FILL:
      return t11 === S.DOT_DENSITY ? w2.dotDensity : w2.fill;
    case E2.MARKER:
      switch (t11) {
        case S.HEATMAP:
          return w2.heatmap;
        case S.PIE_CHART:
          return w2.pieChart;
        default:
          return w2.marker;
      }
  }
}
var j2 = class {
  constructor(e7, t11, s6) {
    this.context = e7, this._blitRenderer = new _2(), this._worldExtentClipRenderer = new f5(), this._isClippedToWorldExtent = false, this._brushCache = /* @__PURE__ */ new Map(), this._lastWidth = null, this._lastHeight = null, this._prevFBO = null, this._vtlMaterialManager = new o7(), this._blendEffect = new _(), this._stencilBuf = null, this._fbos = null, this._fboPool = [], this._renderTarget = null, this.effects = { highlight: new l5(), hittest: new c3(), hittestVTL: new i7(), integrate: new o9(), insideEffect: new r9("inside"), outsideEffect: new r9("outside") }, this.materialManager = new h6(e7), this.textureManager = new J2(t11, s6, e7.type === r5.WEBGL2), this.textureUploadManager = new i6(e7, t11), this._effectsManager = new i8();
  }
  get vectorTilesMaterialManager() {
    return this._vtlMaterialManager;
  }
  getRenderTarget() {
    return this._renderTarget;
  }
  setRenderTarget(e7) {
    this._renderTarget = e7;
  }
  getFbos(e7, t11) {
    if (e7 !== this._lastWidth || t11 !== this._lastHeight) {
      if (this._lastWidth = e7, this._lastHeight = t11, this._fbos) {
        for (const s7 in this._fbos) this._fbos[s7].resize(e7, t11);
        return this._fbos;
      }
      const s6 = { target: M.TEXTURE_2D, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, samplingMode: L.NEAREST, wrapMode: D2.CLAMP_TO_EDGE, width: e7, height: t11 }, r10 = { colorTarget: Y.TEXTURE, depthStencilTarget: V.DEPTH_STENCIL_RENDER_BUFFER }, i9 = new s2(this.context, { width: e7, height: t11, internalFormat: B.DEPTH_STENCIL });
      this._stencilBuf = i9, this._fbos = { output: new x(this.context, r10, s6, i9), blend: new x(this.context, r10, s6, i9), effect0: new x(this.context, r10, s6, i9) };
    }
    return this._fbos;
  }
  acquireFbo(e7, t11) {
    let s6;
    s6 = this._fboPool.length > 0 ? this._fboPool.pop() : new x(this.context, { colorTarget: Y.TEXTURE, depthStencilTarget: V.DEPTH_STENCIL_RENDER_BUFFER }, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, samplingMode: L.NEAREST, wrapMode: D2.CLAMP_TO_EDGE, width: e7, height: t11 }, this._stencilBuf);
    const r10 = s6.descriptor;
    return r10.width === e7 && r10.height === t11 || s6.resize(e7, t11), s6;
  }
  releaseFbo(e7) {
    this._fboPool.push(e7);
  }
  getSharedStencilBuffer() {
    return this._stencilBuf;
  }
  beforeRenderLayers(t11, s6 = null) {
    const { width: r10, height: i9 } = t11.getViewport();
    this._prevFBO = t11.getBoundFramebufferObject();
    const n7 = this.getFbos(r10, i9);
    if (t11.bindFramebuffer(n7 == null ? void 0 : n7.output), t11.setColorMask(true, true, true, true), r(s6)) {
      const { r: e7, g: r11, b: i10, a: n8 } = s6.color;
      t11.setClearColor(n8 * e7 / 255, n8 * r11 / 255, n8 * i10 / 255, n8);
    } else t11.setClearColor(0, 0, 0, 0);
    t11.setDepthWriteEnabled(true), t11.setClearDepth(1), t11.clear(t11.gl.COLOR_BUFFER_BIT | t11.gl.DEPTH_BUFFER_BIT), t11.setDepthWriteEnabled(false);
  }
  beforeRenderLayer(e7, t11, s6) {
    var _a;
    const { context: r10, blendMode: i9, effects: n7, requireFBO: o11, drawPhase: a8 } = e7;
    if (o11 || A2(a8, i9, n7, s6)) r10.bindFramebuffer((_a = this._fbos) == null ? void 0 : _a.blend), r10.setColorMask(true, true, true, true), r10.setClearColor(0, 0, 0, 0), r10.setDepthWriteEnabled(true), r10.setClearDepth(1), r10.clear(r10.gl.COLOR_BUFFER_BIT | r10.gl.DEPTH_BUFFER_BIT), r10.setDepthWriteEnabled(false);
    else {
      const e8 = this._getOutputFBO();
      r10.bindFramebuffer(e8);
    }
    r10.setDepthWriteEnabled(false), r10.setDepthTestEnabled(false), r10.setStencilTestEnabled(true), r10.setClearStencil(t11), r10.setStencilWriteMask(255), r10.clear(r10.gl.STENCIL_BUFFER_BIT);
  }
  compositeLayer(s6, r10) {
    const { context: i9, blendMode: n7, effects: a8, requireFBO: l6, drawPhase: h8 } = s6;
    if (l6 || A2(h8, n7, a8, r10)) {
      r(a8) && a8.length > 0 && h8 === T.MAP && this._applyEffects(s6, a8);
      const l7 = this._getOutputFBO();
      i9.bindFramebuffer(l7), i9.setStencilTestEnabled(false), i9.setStencilWriteMask(0), i9.setBlendingEnabled(true), i9.setBlendFunctionSeparate(R.ONE, R.ONE_MINUS_SRC_ALPHA, R.ONE, R.ONE_MINUS_SRC_ALPHA), i9.setColorMask(true, true, true, true);
      const f7 = t(n7) || h8 === T.HIGHLIGHT ? "normal" : n7, c7 = this._fbos;
      (c7 == null ? void 0 : c7.blend.colorTexture) && this._blendEffect.draw(s6, c7.blend.colorTexture, L.NEAREST, f7, r10);
    }
  }
  renderLayers(e7) {
    e7.bindFramebuffer(this._prevFBO);
    const t11 = this._getOutputFBO();
    t11 && (e7.setDepthTestEnabled(false), e7.setStencilWriteMask(0), this._isClippedToWorldExtent ? (e7.setStencilTestEnabled(true), e7.setStencilFunction(I.EQUAL, 1, 255)) : e7.setStencilTestEnabled(false), this.blitTexture(e7, t11.colorTexture, L.NEAREST));
  }
  prepareDisplay(t11, s6, r10) {
    const { context: i9 } = t11;
    if (i9.bindFramebuffer(this._prevFBO), i9.setColorMask(true, true, true, true), r(s6)) {
      const { r: e7, g: t12, b: r11, a: n7 } = s6.color;
      i9.setClearColor(n7 * e7 / 255, n7 * t12 / 255, n7 * r11 / 255, n7);
    } else i9.setClearColor(0, 0, 0, 0);
    i9.setStencilWriteMask(255), i9.setClearStencil(0), i9.clear(i9.gl.COLOR_BUFFER_BIT | i9.gl.STENCIL_BUFFER_BIT), this._isClippedToWorldExtent = this._worldExtentClipRenderer.render(t11, r10);
  }
  dispose() {
    if (this.materialManager.dispose(), this.textureManager.dispose(), this.textureUploadManager.destroy(), this._blitRenderer = h(this._blitRenderer), this._worldExtentClipRenderer = h(this._worldExtentClipRenderer), this._brushCache && (this._brushCache.forEach((e7) => e7.dispose()), this._brushCache.clear(), this._brushCache = null), this._fbos) for (const e7 in this._fbos) this._fbos[e7] && this._fbos[e7].dispose();
    for (const e7 of this._fboPool) e7.dispose();
    if (this._fboPool.length = 0, this.effects) for (const e7 in this.effects) this.effects[e7] && this.effects[e7].dispose();
    this._effectsManager.dispose(), this._vtlMaterialManager = h(this._vtlMaterialManager), this._prevFBO = null;
  }
  getBrush(e7, t11) {
    const s6 = I4(e7, t11);
    let r10 = this._brushCache.get(s6);
    return void 0 === r10 && (r10 = new s6(), this._brushCache.set(s6, r10)), r10;
  }
  renderObject(e7, t11, s6, i9) {
    const n7 = w2[s6];
    if (!n7) return;
    let o11 = this._brushCache.get(n7);
    void 0 === o11 && (o11 = new n7(), this._brushCache.set(n7, o11)), o11.prepareState(e7, i9), o11.draw(e7, t11, i9);
  }
  renderObjects(e7, t11, s6, i9) {
    const n7 = w2[s6];
    if (!n7) return;
    let o11 = this._brushCache.get(n7);
    void 0 === o11 && (o11 = new n7(), this._brushCache.set(n7, o11)), o11.drawMany(e7, t11, i9);
  }
  registerRenderPass(e7) {
    const t11 = e7.brushes.map((e8) => (this._brushCache.has(e8) || this._brushCache.set(e8, new e8()), this._brushCache.get(e8)));
    return new a7(t11, e7);
  }
  blitTexture(e7, t11, s6, r10 = 1) {
    e7.setBlendingEnabled(true), e7.setBlendFunctionSeparate(R.ONE, R.ONE_MINUS_SRC_ALPHA, R.ONE, R.ONE_MINUS_SRC_ALPHA), e7.setColorMask(true, true, true, true), this._blitRenderer.render(e7, t11, s6, r10);
  }
  getPostProcessingEffects(e7) {
    return this._effectsManager.getPostProcessingEffects(e7);
  }
  _getOutputFBO() {
    var _a;
    return null != this._renderTarget ? this._renderTarget : ((_a = this._fbos) == null ? void 0 : _a.output) ?? null;
  }
  _applyEffects(e7, t11) {
    var _a;
    const s6 = (_a = this._fbos) == null ? void 0 : _a.blend;
    if (!s6) return;
    const { context: r10 } = e7, i9 = this._effectsManager.getPostProcessingEffects(t11);
    for (const { postProcessingEffect: n7, effect: o11 } of i9) r10.bindFramebuffer(s6), n7.draw(e7, s6, o11);
  }
};
function A2(t11, s6, r10, i9) {
  return t11 !== T.HIGHLIGHT && (1 !== i9 || r(s6) && "normal" !== s6 || r(r10) && r10.length > 0);
}

// node_modules/@arcgis/core/views/2d/engine/Stage.js
var x2 = 2e3;
var P2 = class extends h4 {
  constructor(s6, n7) {
    super(), this._trash = /* @__PURE__ */ new Set(), this._renderRemainingTime = 0, this._lastFrameRenderTime = 0, this.renderRequested = false, this.stage = this, this._stationary = true;
    const { canvas: o11 = document.createElement("canvas"), alpha: d7 = true, stencil: p3 = true, contextOptions: f7 = {} } = n7;
    this._canvas = o11;
    const _7 = o2("2d", o11, { alpha: d7, antialias: false, depth: true, stencil: p3 });
    this.context = new I3(e(_7) ?? null, f7), this.resourceManager = new o3(), this.painter = new j2(this.context, this, this.resourceManager), has("esri-2d-profiler") && (this._debugOutput = document.createElement("div"), this._debugOutput.setAttribute("style", "margin: 24px 64px; position: absolute; color: red;"), s6.appendChild(this._debugOutput));
    const g2 = () => this._highlightGradient;
    this._renderParameters = { drawPhase: 0, state: this.state, pixelRatio: window.devicePixelRatio, stationary: false, globalOpacity: 1, blendMode: null, deltaTime: -1, time: 0, inFadeTransition: false, effects: null, context: this.context, painter: this.painter, timeline: n7.timeline || new e6(), renderingOptions: n7.renderingOptions, requestRender: () => this.requestRender(), allowDelayedRender: false, requireFBO: false, profiler: new i2(this.context, this._debugOutput), dataUploadCounter: 0, get highlightGradient() {
      return g2();
    } }, this._taskHandle = A({ render: (e7) => this.renderFrame(e7) }), this._taskHandle.pause(), this._lostWebGLContextHandle = r2(o11, "webglcontextlost", () => {
      this.emit("webgl-error", { error: new s("webgl-context-lost") });
    }), this._bufferPool = new n3(), o11.setAttribute("style", "width: 100%; height:100%; display:block;"), s6.appendChild(o11);
  }
  destroy() {
    var _a, _b, _c;
    this.removeAllChildren(), this._emptyTrash(), this._taskHandle = p(this._taskHandle), this._lostWebGLContextHandle = p(this._lostWebGLContextHandle), (_a = this._canvas.parentNode) == null ? void 0 : _a.removeChild(this._canvas), (_c = (_b = this._debugOutput) == null ? void 0 : _b.parentNode) == null ? void 0 : _c.removeChild(this._debugOutput), this._bufferPool.destroy(), this.resourceManager.destroy(), this.painter.dispose(), this.context.dispose(), this._canvas = null;
  }
  get background() {
    return this._background;
  }
  set background(e7) {
    this._background = e7, this.requestRender();
  }
  get bufferPool() {
    return this._bufferPool;
  }
  get renderingOptions() {
    return this._renderingOptions;
  }
  set renderingOptions(e7) {
    this._renderingOptions = e7, this.requestRender();
  }
  get state() {
    return this._state;
  }
  set state(e7) {
    this._state = e7, this.requestRender();
  }
  get stationary() {
    return this._stationary;
  }
  set stationary(e7) {
    this._stationary !== e7 && (this._stationary = e7, this.requestRender());
  }
  trashDisplayObject(e7) {
    this._trash.add(e7), this.requestRender();
  }
  untrashDisplayObject(e7) {
    return this._trash.delete(e7);
  }
  requestRender() {
    this._renderRemainingTime = x2, this.renderRequested || (this.renderRequested = true, this.emit("will-render"), this._taskHandle.resume());
  }
  renderFrame(e7) {
    const t11 = this._lastFrameRenderTime ? e7.time - this._lastFrameRenderTime : 0;
    this._renderRemainingTime -= t11, this._renderRemainingTime <= 0 && this._taskHandle.pause(), this._lastFrameRenderTime = e7.time, this.renderRequested = false, this._renderParameters.state = this._state, this._renderParameters.stationary = this.stationary, this._renderParameters.pixelRatio = window.devicePixelRatio, this._renderParameters.globalOpacity = 1, this._renderParameters.time = e7.time, this._renderParameters.deltaTime = e7.deltaTime, this._renderParameters.effects = null, this.processRender(this._renderParameters), this._emptyTrash(), this.emit("post-render");
  }
  _createTransforms() {
    return { dvs: e3() };
  }
  renderChildren(e7) {
    for (const t11 of this.children) t11.beforeRender(e7);
    this._renderChildren(this.children, e7);
    for (const t11 of this.children) t11.afterRender(e7);
  }
  _renderChildren(e7, t11) {
    const r10 = this.context;
    this.painter.textureUploadManager.upload(), r10.resetInfo(), t11.profiler.recordStart("drawLayers"), t11.dataUploadCounter = 0, t11.drawPhase = T.MAP, this.painter.beforeRenderLayers(r10, this.background);
    for (const s6 of e7) s6.processRender(t11);
    this.painter.prepareDisplay(t11, this.background, this.state.padding), this.painter.renderLayers(r10), t11.drawPhase = T.HIGHLIGHT, this.painter.beforeRenderLayers(r10);
    for (const s6 of e7) s6.processRender(t11);
    this.painter.renderLayers(r10);
    if (this._isLabelDrawPhaseRequired(e7)) {
      t11.drawPhase = T.LABEL, this.painter.beforeRenderLayers(r10);
      for (const r11 of e7) r11.processRender(t11);
      this.painter.renderLayers(r10);
    }
    if (has("esri-tiles-debug")) {
      t11.drawPhase = T.DEBUG, this.painter.beforeRenderLayers(r10);
      for (const r11 of e7) r11.processRender(t11);
      this.painter.renderLayers(r10);
    }
    t11.profiler.recordEnd("drawLayers"), r10.logInfo();
  }
  doRender(e7) {
    const t11 = this.context, { state: r10, pixelRatio: s6 } = e7;
    this._resizeCanvas(e7), t11.setViewport(0, 0, s6 * r10.size[0], s6 * r10.size[1]), t11.setDepthWriteEnabled(true), t11.setStencilWriteMask(255), super.doRender(e7);
  }
  async takeScreenshot(e7) {
    const { framebufferWidth: t11, framebufferHeight: r10 } = { framebufferWidth: Math.round(this.state.size[0] * e7.resolutionScale), framebufferHeight: Math.round(this.state.size[1] * e7.resolutionScale) }, s6 = e7.resolutionScale, i9 = this.context, n7 = this._state.clone();
    if (null != e7.rotation) {
      const t12 = n7.viewpoint;
      n7.viewpoint.rotation = e7.rotation, n7.viewpoint = t12;
    }
    const a8 = { ...this._renderParameters, drawPhase: null, globalOpacity: 1, stationary: true, state: n7, pixelRatio: s6, time: performance.now(), deltaTime: 0, blendMode: null, effects: null, inFadeTransition: false }, o11 = new x(i9, { colorTarget: Y.TEXTURE, depthStencilTarget: V.DEPTH_STENCIL_RENDER_BUFFER, width: t11, height: r10 }), h8 = i9.getBoundFramebufferObject(), d7 = i9.getViewport();
    i9.bindFramebuffer(o11), i9.setViewport(0, 0, t11, r10), this._renderChildren(e7.children, a8);
    const l6 = this._readbackScreenshot(o11, { ...e7.cropArea, y: r10 - (e7.cropArea.y + e7.cropArea.height) });
    i9.bindFramebuffer(h8), i9.setViewport(d7.x, d7.y, d7.width, d7.height), this.requestRender();
    const c7 = await l6;
    let u4;
    return 1 === e7.outputScale ? u4 = c7 : (u4 = new ImageData(Math.round(c7.width * e7.outputScale), Math.round(c7.height * e7.outputScale)), m2(c7, u4, true)), u4;
  }
  async _readbackScreenshot(e7, t11) {
    const r10 = e2(t11.width, t11.height, document.createElement("canvas"));
    return await e7.readPixelsAsync(t11.x, t11.y, t11.width, t11.height, P.RGBA, G.UNSIGNED_BYTE, new Uint8Array(r10.data.buffer)), r10;
  }
  _resizeCanvas(e7) {
    const t11 = this._canvas, r10 = t11.style, { state: { size: s6 }, pixelRatio: i9 } = e7, n7 = s6[0], a8 = s6[1], o11 = Math.round(n7 * i9), h8 = Math.round(a8 * i9);
    t11.width === o11 && t11.height === h8 || (t11.width = o11, t11.height = h8), r10.width = n7 + "px", r10.height = a8 + "px";
  }
  _emptyTrash() {
    for (; this._trash.size > 0; ) {
      const e7 = Array.from(this._trash);
      this._trash.clear();
      for (const t11 of e7) t11.processDetach();
    }
  }
  _isLabelDrawPhaseRequired(e7) {
    let t11 = false;
    for (const r10 of e7) {
      if (!(r10 instanceof h4)) {
        t11 = t11 || false;
        break;
      }
      if (r10.hasLabels) return true;
      t11 = t11 || this._isLabelDrawPhaseRequired(r10.children);
    }
    return t11;
  }
};

// node_modules/@arcgis/core/views/magnifier/resources.js
async function s5(s6) {
  const r10 = import("./mask-svg-BXPQ4SP5.js"), i9 = import("./overlay-svg-DKBYJSOB.js"), o11 = t4((await r10).default, { signal: s6 }), e7 = t4((await i9).default, { signal: s6 }), m5 = { mask: await o11, overlay: await e7 };
  return f(s6), m5;
}

// node_modules/@arcgis/core/views/2d/magnifier/MagnifierView2D.js
var U2 = class extends r6 {
  constructor() {
    super(), this._handles = new t2(), this._resourcePixelRatio = 1, this.visible = false;
  }
  destroy() {
    this._handles = a(this._handles), this._disposeRenderResources(), this._resourcesTask = w(this._resourcesTask);
  }
  get background() {
    return this._background;
  }
  set background(e7) {
    this._background = e7, this.requestRender();
  }
  get magnifier() {
    return this._magnifier;
  }
  set magnifier(e7) {
    this._magnifier = e7, this._handles.removeAll(), this._handles.add([l(() => e7.version, () => {
      this.visible = e7.visible && r(e7.position) && e7.size > 0, this.requestRender();
    }, h2), l(() => [e7.maskUrl, e7.overlayUrl], () => this._reloadResources()), l(() => e7.size, () => {
      this._disposeRenderResources(), this.requestRender();
    })]);
  }
  _createTransforms() {
    return { dvs: e3() };
  }
  doRender(e7) {
    var _a;
    const r10 = e7.context;
    if (!this._resourcesTask) return void this._reloadResources();
    if (e7.drawPhase !== T.MAP || !this._canRender()) return;
    this._updateResources(e7);
    const t11 = this._magnifier;
    if (t(t11.position)) return;
    const i9 = e7.pixelRatio, a8 = t11.size * i9, n7 = 1 / t11.factor, l6 = Math.ceil(n7 * a8);
    this._readbackTexture.resize(l6, l6);
    const { size: h8 } = e7.state, m5 = i9 * h8[0], u4 = i9 * h8[1], c7 = 0.5 * l6, d7 = 0.5 * l6, _7 = a3(i9 * t11.position.x, c7, m5 - c7 - 1), f7 = a3(u4 - i9 * t11.position.y, d7, u4 - d7 - 1);
    r10.setBlendingEnabled(true);
    const g2 = _7 - c7, T3 = f7 - d7, x3 = this._readbackTexture;
    r10.bindTexture(x3, 0), r10.gl.copyTexImage2D(x3.descriptor.target, 0, x3.descriptor.pixelFormat, g2, T3, l6, l6, 0);
    const R2 = (_a = this.background) == null ? void 0 : _a.color, y2 = R2 ? [R2.a * R2.r / 255, R2.a * R2.g / 255, R2.a * R2.b / 255, R2.a] : [1, 1, 1, 1], k = (_7 + t11.offset.x * i9) / m5 * 2 - 1, A3 = (f7 - t11.offset.y * i9) / u4 * 2 - 1, v3 = a8 / m5 * 2, E5 = a8 / u4 * 2, j3 = this._program;
    r10.bindVAO(this._vertexArrayObject), r10.bindTexture(this._overlayTexture, 6), r10.bindTexture(this._maskTexture, 7), r10.useProgram(j3), j3.setUniform4fv("u_background", y2), j3.setUniform1i("u_readbackTexture", 0), j3.setUniform1i("u_overlayTexture", 6), j3.setUniform1i("u_maskTexture", 7), j3.setUniform4f("u_drawPos", k, A3, v3, E5), j3.setUniform1i("u_maskEnabled", t11.maskEnabled ? 1 : 0), j3.setUniform1i("u_overlayEnabled", t11.overlayEnabled ? 1 : 0), r10.setStencilTestEnabled(false), r10.setColorMask(true, true, true, true), r10.drawArrays(E.TRIANGLE_STRIP, 0, 4), r10.bindVAO();
  }
  _canRender() {
    return this.mask && this.overlay && null != this._magnifier;
  }
  _reloadResources() {
    this._resourcesTask && this._resourcesTask.abort();
    const t11 = r(this._magnifier) ? this._magnifier.maskUrl : null, s6 = r(this._magnifier) ? this._magnifier.overlayUrl : null;
    this._resourcesTask = j(async (r10) => {
      const i9 = t(t11) || t(s6) ? s5(r10) : null, a8 = r(t11) ? U(t11, { responseType: "image", signal: r10 }).then((e7) => e7.data) : i9.then((e7) => e7.mask), l6 = r(s6) ? U(s6, { responseType: "image", signal: r10 }).then((e7) => e7.data) : i9.then((e7) => e7.overlay), [h8, m5] = await Promise.all([a8, l6]);
      this.mask = h8, this.overlay = m5, this._disposeRenderResources(), this.requestRender();
    });
  }
  _disposeRenderResources() {
    this._readbackTexture = h(this._readbackTexture), this._overlayTexture = h(this._overlayTexture), this._maskTexture = h(this._maskTexture), this._vertexArrayObject = h(this._vertexArrayObject), this._program = h(this._program);
  }
  _updateResources(e7) {
    if (e7.pixelRatio !== this._resourcePixelRatio && this._disposeRenderResources(), this._readbackTexture) return;
    const r10 = e7.context;
    this._resourcePixelRatio = e7.pixelRatio;
    const t11 = Math.ceil(this._magnifier.size * e7.pixelRatio);
    this._program = t9(r10);
    const s6 = new Uint16Array([0, 1, 0, 0, 1, 1, 1, 0]), i9 = a4.attributes;
    this._vertexArrayObject = new f3(r10, i9, m, { geometry: E4.createVertex(r10, F.STATIC_DRAW, s6) }), this.overlay.width = t11, this.overlay.height = t11, this._overlayTexture = new E3(r10, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.NEAREST, flipped: true, preMultiplyAlpha: !Bt(this.overlay.src) || !e7.context.driverTest.svgPremultipliesAlpha.result }, this.overlay), this.mask.width = t11, this.mask.height = t11, this._maskTexture = new E3(r10, { target: M.TEXTURE_2D, pixelFormat: P.ALPHA, internalFormat: P.ALPHA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.NEAREST, flipped: true }, this.mask);
    const a8 = 1 / this._magnifier.factor;
    this._readbackTexture = new E3(r10, { target: M.TEXTURE_2D, pixelFormat: P.RGBA, internalFormat: P.RGBA, dataType: G.UNSIGNED_BYTE, wrapMode: D2.CLAMP_TO_EDGE, samplingMode: L.LINEAR, flipped: false, width: Math.ceil(a8 * t11), height: Math.ceil(a8 * t11) });
  }
};
export {
  i3 as GraphicContainer,
  ae as GraphicsView2D,
  u2 as LabelManager,
  U2 as MagnifierView2D,
  y as MapViewNavigation,
  P2 as Stage
};
//# sourceMappingURL=mapViewDeps-IXGGSY2J.js.map
