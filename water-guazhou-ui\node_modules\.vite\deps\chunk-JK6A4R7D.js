import {
  b
} from "./chunk-QMNV7QQK.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";

// node_modules/@arcgis/core/portal/support/jsonContext.js
function e(e2) {
  return { origin: "portal-item", url: L(e2.itemUrl), portal: e2.portal || b.getDefault(), portalItem: e2, readResourcePaths: [] };
}
function o(e2) {
  return { origin: "portal-item", messages: [], writtenProperties: [], url: e2.itemUrl ? L(e2.itemUrl) : null, portal: e2.portal || b.getDefault(), portalItem: e2 };
}

export {
  e,
  o
};
//# sourceMappingURL=chunk-JK6A4R7D.js.map
