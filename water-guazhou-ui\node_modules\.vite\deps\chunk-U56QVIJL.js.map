{"version": 3, "sources": ["../../@arcgis/core/views/2d/viewStateUtils.js", "../../@arcgis/core/views/2d/layers/support/ExportStrategy.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst t=Math.PI/180;function n(n){return n*t}function o(t,o){const a=n(o.rotation),r=Math.abs(Math.cos(a)),s=Math.abs(Math.sin(a)),[u,c]=o.size;return t[0]=Math.round(c*s+u*r),t[1]=Math.round(c*r+u*s),t}function a(t,n,o,a){const[r,s]=n,[u,c]=a,h=.5*o;return t[0]=r-h*u,t[1]=s-h*c,t[2]=r+h*u,t[3]=s+h*c,t}export{a as getBBox,o as getOuterSize};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../../chunks/tslib.es6.js\";import e from\"../../../../core/Accessor.js\";import\"../../../../core/has.js\";import{debounce as i,throwIfAborted as o,throwIfAbortError as r,eachAlways as s}from\"../../../../core/promiseUtils.js\";import{property as a}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as n}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{create as p,toExtent as m}from\"../../../../geometry/support/aaBoundingRect.js\";import{getInfo as l}from\"../../../../geometry/support/spatialReferenceUtils.js\";import d from\"../../../../layers/support/TileInfo.js\";import{getOuterSize as h,getBBox as c}from\"../../viewStateUtils.js\";import{Bitmap as u}from\"../../engine/Bitmap.js\";import g from\"../../tiling/TileInfoView.js\";import f from\"../../tiling/TileKey.js\";const y=p(),x=[0,0],S=new f(0,0,0,0),_={container:null,fetchSource:null,requestUpdate:null,imageMaxWidth:2048,imageMaxHeight:2048,imageRotationSupported:!1,imageNormalizationSupported:!1,hidpi:!1};let w=class extends e{constructor(t){super(t),this._imagePromise=null,this.bitmaps=[],this.hidpi=_.hidpi,this.imageMaxWidth=_.imageMaxWidth,this.imageMaxHeight=_.imageMaxHeight,this.imageRotationSupported=_.imageRotationSupported,this.imageNormalizationSupported=_.imageNormalizationSupported,this.update=i((async(t,e)=>{if(o(e),!t.stationary||this.destroyed)return;const i=t.state,s=l(i.spatialReference),a=this.hidpi?t.pixelRatio:1,n=this.imageNormalizationSupported&&i.worldScreenWidth&&i.worldScreenWidth<i.size[0],p=this.imageMaxWidth??0,m=this.imageMaxHeight??0;n?(x[0]=i.worldScreenWidth,x[1]=i.size[1]):this.imageRotationSupported?(x[0]=i.size[0],x[1]=i.size[1]):h(x,i);const d=Math.floor(x[0]*a)>p||Math.floor(x[1]*a)>m,c=s&&(i.extent.xmin<s.valid[0]||i.extent.xmax>s.valid[1]),u=!this.imageNormalizationSupported&&c,g=!d&&!u,f=this.imageRotationSupported?i.rotation:0,y=this.container.children.slice();if(g){const t=n?i.paddedViewState.center:i.center;this._imagePromise&&console.error(\"Image promise was not defined!\"),this._imagePromise=this._singleExport(i,x,t,i.resolution,f,a,e)}else{let t=Math.min(p,m);u&&(t=Math.min(i.worldScreenWidth,t)),this._imagePromise=this._tiledExport(i,t,a,e)}try{const t=await this._imagePromise??[];o(e);const i=[];if(this._imagePromise=null,this.destroyed)return;this.bitmaps=t;for(const e of y)t.includes(e)||i.push(e.fadeOut().then((()=>{e.remove(),e.destroy()})));for(const e of t)i.push(e.fadeIn());await Promise.all(i)}catch(S){this._imagePromise=null,r(S)}}),5e3),this.updateExports=i((async t=>{const e=[];for(const i of this.container.children){if(!i.visible||!i.stage)return;e.push(t(i).then((()=>{i.invalidateTexture(),i.requestRender()})))}this._imagePromise=s(e).then((()=>this._imagePromise=null)),await this._imagePromise}))}destroy(){this.bitmaps.forEach((t=>t.destroy())),this.bitmaps=[]}get updating(){return!this.destroyed&&null!==this._imagePromise}async _export(t,e,i,r,s,a){const n=await this.fetchSource(t,Math.floor(e*s),Math.floor(i*s),{rotation:r,pixelRatio:s,signal:a});o(a);const p=new u(null,{immutable:!0,requestRenderOnSourceChangedEnabled:!0});return p.x=t.xmin,p.y=t.ymax,p.resolution=t.width/e,p.rotation=r,p.pixelRatio=s,p.opacity=0,this.container.addChild(p),await p.setSourceAsync(n,a),o(a),p}async _singleExport(t,e,i,o,r,s,a){c(y,i,o,e);const n=m(y,t.spatialReference);return[await this._export(n,e[0],e[1],r,s,a)]}_tiledExport(t,e,i,o){const r=d.create({size:e,spatialReference:t.spatialReference,scales:[t.scale]}),s=new g(r),a=s.getTileCoverage(t);if(!a)return null;const n=[];return a.forEach(((r,a,p,l)=>{S.set(r,a,p,0),s.getTileBounds(y,S);const d=m(y,t.spatialReference);n.push(this._export(d,e,e,0,i,o).then((t=>(0!==l&&(S.set(r,a,p,l),s.getTileBounds(y,S),t.x=y[0],t.y=y[3]),t))))})),Promise.all(n)}};t([a()],w.prototype,\"_imagePromise\",void 0),t([a()],w.prototype,\"bitmaps\",void 0),t([a()],w.prototype,\"container\",void 0),t([a()],w.prototype,\"fetchSource\",void 0),t([a()],w.prototype,\"hidpi\",void 0),t([a()],w.prototype,\"imageMaxWidth\",void 0),t([a()],w.prototype,\"imageMaxHeight\",void 0),t([a()],w.prototype,\"imageRotationSupported\",void 0),t([a()],w.prototype,\"imageNormalizationSupported\",void 0),t([a()],w.prototype,\"requestUpdate\",void 0),t([a()],w.prototype,\"updating\",null),w=t([n(\"esri.views.2d.layers.support.ExportStrategy\")],w);const v=w;export{v as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,IAAE,KAAK,KAAG;AAAI,SAAS,EAAEA,IAAE;AAAC,SAAOA,KAAE;AAAC;AAAC,SAAS,EAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAED,GAAE,QAAQ,GAAE,IAAE,KAAK,IAAI,KAAK,IAAIC,EAAC,CAAC,GAAE,IAAE,KAAK,IAAI,KAAK,IAAIA,EAAC,CAAC,GAAE,CAACC,IAAE,CAAC,IAAEF,GAAE;AAAK,SAAOD,GAAE,CAAC,IAAE,KAAK,MAAM,IAAE,IAAEG,KAAE,CAAC,GAAEH,GAAE,CAAC,IAAE,KAAK,MAAM,IAAE,IAAEG,KAAE,CAAC,GAAEH;AAAC;AAAC,SAASE,GAAEF,IAAED,IAAEE,IAAEC,IAAE;AAAC,QAAK,CAAC,GAAE,CAAC,IAAEH,IAAE,CAACI,IAAE,CAAC,IAAED,IAAEE,KAAE,MAAGH;AAAE,SAAOD,GAAE,CAAC,IAAE,IAAEI,KAAED,IAAEH,GAAE,CAAC,IAAE,IAAEI,KAAE,GAAEJ,GAAE,CAAC,IAAE,IAAEI,KAAED,IAAEH,GAAE,CAAC,IAAE,IAAEI,KAAE,GAAEJ;AAAC;;;ACA+mB,IAAMK,KAAE,EAAE;AAAV,IAAYC,KAAE,CAAC,GAAE,CAAC;AAAlB,IAAoB,IAAE,IAAIC,GAAE,GAAE,GAAE,GAAE,CAAC;AAAnC,IAAqC,IAAE,EAAC,WAAU,MAAK,aAAY,MAAK,eAAc,MAAK,eAAc,MAAK,gBAAe,MAAK,wBAAuB,OAAG,6BAA4B,OAAG,OAAM,MAAE;AAAE,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,gBAAc,MAAK,KAAK,UAAQ,CAAC,GAAE,KAAK,QAAM,EAAE,OAAM,KAAK,gBAAc,EAAE,eAAc,KAAK,iBAAe,EAAE,gBAAe,KAAK,yBAAuB,EAAE,wBAAuB,KAAK,8BAA4B,EAAE,6BAA4B,KAAK,SAAO,EAAG,OAAMA,IAAEF,OAAI;AAAC,UAAG,EAAEA,EAAC,GAAE,CAACE,GAAE,cAAY,KAAK,UAAU;AAAO,YAAM,IAAEA,GAAE,OAAM,IAAE,EAAE,EAAE,gBAAgB,GAAEC,KAAE,KAAK,QAAMD,GAAE,aAAW,GAAEE,KAAE,KAAK,+BAA6B,EAAE,oBAAkB,EAAE,mBAAiB,EAAE,KAAK,CAAC,GAAE,IAAE,KAAK,iBAAe,GAAE,IAAE,KAAK,kBAAgB;AAAE,MAAAA,MAAGL,GAAE,CAAC,IAAE,EAAE,kBAAiBA,GAAE,CAAC,IAAE,EAAE,KAAK,CAAC,KAAG,KAAK,0BAAwBA,GAAE,CAAC,IAAE,EAAE,KAAK,CAAC,GAAEA,GAAE,CAAC,IAAE,EAAE,KAAK,CAAC,KAAG,EAAEA,IAAE,CAAC;AAAE,YAAM,IAAE,KAAK,MAAMA,GAAE,CAAC,IAAEI,EAAC,IAAE,KAAG,KAAK,MAAMJ,GAAE,CAAC,IAAEI,EAAC,IAAE,GAAE,IAAE,MAAI,EAAE,OAAO,OAAK,EAAE,MAAM,CAAC,KAAG,EAAE,OAAO,OAAK,EAAE,MAAM,CAAC,IAAGE,KAAE,CAAC,KAAK,+BAA6B,GAAE,IAAE,CAAC,KAAG,CAACA,IAAEC,KAAE,KAAK,yBAAuB,EAAE,WAAS,GAAER,KAAE,KAAK,UAAU,SAAS,MAAM;AAAE,UAAG,GAAE;AAAC,cAAMI,KAAEE,KAAE,EAAE,gBAAgB,SAAO,EAAE;AAAO,aAAK,iBAAe,QAAQ,MAAM,gCAAgC,GAAE,KAAK,gBAAc,KAAK,cAAc,GAAEL,IAAEG,IAAE,EAAE,YAAWI,IAAEH,IAAEH,EAAC;AAAA,MAAC,OAAK;AAAC,YAAIE,KAAE,KAAK,IAAI,GAAE,CAAC;AAAE,QAAAG,OAAIH,KAAE,KAAK,IAAI,EAAE,kBAAiBA,EAAC,IAAG,KAAK,gBAAc,KAAK,aAAa,GAAEA,IAAEC,IAAEH,EAAC;AAAA,MAAC;AAAC,UAAG;AAAC,cAAME,KAAE,MAAM,KAAK,iBAAe,CAAC;AAAE,UAAEF,EAAC;AAAE,cAAMO,KAAE,CAAC;AAAE,YAAG,KAAK,gBAAc,MAAK,KAAK,UAAU;AAAO,aAAK,UAAQL;AAAE,mBAAUF,MAAKF,GAAE,CAAAI,GAAE,SAASF,EAAC,KAAGO,GAAE,KAAKP,GAAE,QAAQ,EAAE,KAAM,MAAI;AAAC,UAAAA,GAAE,OAAO,GAAEA,GAAE,QAAQ;AAAA,QAAC,CAAE,CAAC;AAAE,mBAAUA,MAAKE,GAAE,CAAAK,GAAE,KAAKP,GAAE,OAAO,CAAC;AAAE,cAAM,QAAQ,IAAIO,EAAC;AAAA,MAAC,SAAOC,IAAE;AAAC,aAAK,gBAAc,MAAK,EAAEA,EAAC;AAAA,MAAC;AAAA,IAAC,GAAG,GAAG,GAAE,KAAK,gBAAc,EAAG,OAAMN,OAAG;AAAC,YAAMF,KAAE,CAAC;AAAE,iBAAU,KAAK,KAAK,UAAU,UAAS;AAAC,YAAG,CAAC,EAAE,WAAS,CAAC,EAAE,MAAM;AAAO,QAAAA,GAAE,KAAKE,GAAE,CAAC,EAAE,KAAM,MAAI;AAAC,YAAE,kBAAkB,GAAE,EAAE,cAAc;AAAA,QAAC,CAAE,CAAC;AAAA,MAAC;AAAC,WAAK,gBAAc,EAAEF,EAAC,EAAE,KAAM,MAAI,KAAK,gBAAc,IAAK,GAAE,MAAM,KAAK;AAAA,IAAa,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,QAAQ,QAAS,CAAAE,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAM,CAAC,KAAK,aAAW,SAAO,KAAK;AAAA,EAAa;AAAA,EAAC,MAAM,QAAQA,IAAEF,IAAE,GAAE,GAAE,GAAEG,IAAE;AAAC,UAAMC,KAAE,MAAM,KAAK,YAAYF,IAAE,KAAK,MAAMF,KAAE,CAAC,GAAE,KAAK,MAAM,IAAE,CAAC,GAAE,EAAC,UAAS,GAAE,YAAW,GAAE,QAAOG,GAAC,CAAC;AAAE,MAAEA,EAAC;AAAE,UAAM,IAAE,IAAIM,GAAE,MAAK,EAAC,WAAU,MAAG,qCAAoC,KAAE,CAAC;AAAE,WAAO,EAAE,IAAEP,GAAE,MAAK,EAAE,IAAEA,GAAE,MAAK,EAAE,aAAWA,GAAE,QAAMF,IAAE,EAAE,WAAS,GAAE,EAAE,aAAW,GAAE,EAAE,UAAQ,GAAE,KAAK,UAAU,SAAS,CAAC,GAAE,MAAM,EAAE,eAAeI,IAAED,EAAC,GAAE,EAAEA,EAAC,GAAE;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcD,IAAEF,IAAE,GAAEU,IAAE,GAAE,GAAEP,IAAE;AAAC,IAAAA,GAAEL,IAAE,GAAEY,IAAEV,EAAC;AAAE,UAAMI,KAAEE,GAAER,IAAEI,GAAE,gBAAgB;AAAE,WAAM,CAAC,MAAM,KAAK,QAAQE,IAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAEG,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAEF,IAAE,GAAEU,IAAE;AAAC,UAAM,IAAE,EAAE,OAAO,EAAC,MAAKV,IAAE,kBAAiBE,GAAE,kBAAiB,QAAO,CAACA,GAAE,KAAK,EAAC,CAAC,GAAE,IAAE,IAAI,EAAE,CAAC,GAAEC,KAAE,EAAE,gBAAgBD,EAAC;AAAE,QAAG,CAACC,GAAE,QAAO;AAAK,UAAMC,KAAE,CAAC;AAAE,WAAOD,GAAE,QAAS,CAACQ,IAAER,IAAE,GAAE,MAAI;AAAC,QAAE,IAAIQ,IAAER,IAAE,GAAE,CAAC,GAAE,EAAE,cAAcL,IAAE,CAAC;AAAE,YAAM,IAAEQ,GAAER,IAAEI,GAAE,gBAAgB;AAAE,MAAAE,GAAE,KAAK,KAAK,QAAQ,GAAEJ,IAAEA,IAAE,GAAE,GAAEU,EAAC,EAAE,KAAM,CAAAR,QAAI,MAAI,MAAI,EAAE,IAAIS,IAAER,IAAE,GAAE,CAAC,GAAE,EAAE,cAAcL,IAAE,CAAC,GAAEI,GAAE,IAAEJ,GAAE,CAAC,GAAEI,GAAE,IAAEJ,GAAE,CAAC,IAAGI,GAAG,CAAC;AAAA,IAAC,CAAE,GAAE,QAAQ,IAAIE,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,+BAA8B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,6CAA6C,CAAC,GAAEA,EAAC;AAAE,IAAMW,KAAEX;", "names": ["n", "t", "o", "a", "u", "h", "y", "x", "e", "w", "t", "a", "n", "u", "f", "i", "S", "R", "o", "r", "v"]}