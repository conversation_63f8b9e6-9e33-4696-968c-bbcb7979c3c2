import {
  i
} from "./chunk-TFXOTB3E.js";
import {
  l
} from "./chunk-FQMXSCOG.js";
import {
  e as e4
} from "./chunk-32BGXH4N.js";
import {
  e as e2
} from "./chunk-GXMOAZWH.js";
import {
  o as o4
} from "./chunk-TUB4N6LD.js";
import {
  o as o2
} from "./chunk-NLDHTNKF.js";
import {
  e as e3
} from "./chunk-YV4RKNU4.js";
import {
  o as o3
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  _ as _2,
  p2 as p
} from "./chunk-6IU6DQRF.js";
import {
  R
} from "./chunk-YELYN22P.js";
import {
  r as r2
} from "./chunk-SROTSYJS.js";
import {
  n as n3
} from "./chunk-FOE4ICAJ.js";
import {
  n as n2
} from "./chunk-NOZFLZZL.js";
import {
  m
} from "./chunk-EIGTETCG.js";
import {
  O,
  _,
  e,
  r,
  s,
  u,
  x,
  z
} from "./chunk-MQAXMQFG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import {
  w
} from "./chunk-RQXGVG3K.js";

// node_modules/@arcgis/core/chunks/Laserlines.glsl.js
var C = m(6);
function A(e5) {
  const i2 = new o3();
  i2.extensions.add("GL_OES_standard_derivatives"), i2.include(o2), i2.include(i, e5);
  const t = i2.fragment;
  if (e5.lineVerticalPlaneEnabled || e5.heightManifoldEnabled) if (t.uniforms.add(new o4("maxPixelDistance", (i3, t2) => e5.heightManifoldEnabled ? 2 * t2.camera.computeScreenPixelSizeAt(i3.heightManifoldTarget) : 2 * t2.camera.computeScreenPixelSizeAt(i3.lineVerticalPlaneSegment.origin))), t.code.add(o`float planeDistancePixels(vec4 plane, vec3 pos) {
float dist = dot(plane.xyz, pos) + plane.w;
float width = fwidth(dist);
dist /= min(width, maxPixelDistance);
return abs(dist);
}`), e5.spherical) {
    const e6 = (e7, i4, t2) => O(e7, i4.heightManifoldTarget, t2.camera.viewMatrix), i3 = (e7, i4) => O(e7, [0, 0, 0], i4.camera.viewMatrix);
    t.uniforms.add([new e2("heightManifoldOrigin", (t2, r3) => (e6(T, t2, r3), i3(_3, r3), e(_3, _3, T), z(G, _3), G[3] = s(_3), G)), new e4("globalOrigin", (e7, t2) => i3(T, t2)), new o4("cosSphericalAngleThreshold", (e7, i4) => 1 - Math.max(2, x(i4.camera.eye, e7.heightManifoldTarget) * i4.camera.perRenderPixelRatio) / s(e7.heightManifoldTarget))]), t.code.add(o`float globeDistancePixels(float posInGlobalOriginLength) {
float dist = abs(posInGlobalOriginLength - heightManifoldOrigin.w);
float width = fwidth(dist);
dist /= min(width, maxPixelDistance);
return abs(dist);
}
float heightManifoldDistancePixels(vec4 heightPlane, vec3 pos) {
vec3 posInGlobalOriginNorm = normalize(globalOrigin - pos);
float cosAngle = dot(posInGlobalOriginNorm, heightManifoldOrigin.xyz);
vec3 posInGlobalOrigin = globalOrigin - pos;
float posInGlobalOriginLength = length(posInGlobalOrigin);
float sphericalDistance = globeDistancePixels(posInGlobalOriginLength);
float planarDistance = planeDistancePixels(heightPlane, pos);
return cosAngle < cosSphericalAngleThreshold ? sphericalDistance : planarDistance;
}`);
  } else t.code.add(o`float heightManifoldDistancePixels(vec4 heightPlane, vec3 pos) {
return planeDistancePixels(heightPlane, pos);
}`);
  if (e5.pointDistanceEnabled && (t.uniforms.add(new o4("maxPixelDistance", (e6, i3) => 2 * i3.camera.computeScreenPixelSizeAt(e6.pointDistanceTarget))), t.code.add(o`float sphereDistancePixels(vec4 sphere, vec3 pos) {
float dist = distance(sphere.xyz, pos) - sphere.w;
float width = fwidth(dist);
dist /= min(width, maxPixelDistance);
return abs(dist);
}`)), e5.intersectsLineEnabled && (t.uniforms.add(new o4("perScreenPixelRatio", (e6, i3) => i3.camera.perScreenPixelRatio)), t.code.add(o`float lineDistancePixels(vec3 start, vec3 dir, float radius, vec3 pos) {
float dist = length(cross(dir, pos - start)) / (length(pos) * perScreenPixelRatio);
return abs(dist) - radius;
}`)), (e5.lineVerticalPlaneEnabled || e5.intersectsLineEnabled) && t.code.add(o`bool pointIsWithinLine(vec3 pos, vec3 start, vec3 end) {
vec3 dir = end - start;
float t2 = dot(dir, pos - start);
float l2 = dot(dir, dir);
return t2 >= 0.0 && t2 <= l2;
}`), t.code.add(o`void main() {
vec3 pos;
vec3 normal;
float depthDiscontinuityAlpha;
if (!laserlineReconstructFromDepth(pos, normal, depthDiscontinuityAlpha)) {
discard;
}
vec4 color = vec4(0, 0, 0, 0);`), e5.heightManifoldEnabled) {
    t.uniforms.add([new e3("angleCutoff", (e6) => V(e6)), new e2("heightPlane", (e6, i4) => z2(e6.heightManifoldTarget, e6.renderCoordsHelper.worldUpAtPosition(e6.heightManifoldTarget, T), i4.camera.viewMatrix))]);
    const i3 = e5.spherical ? o`normalize(globalOrigin - pos)` : o`heightPlane.xyz`;
    t.code.add(o`
    {
      float heightManifoldAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, abs(dot(normal, ${i3})));
      vec4 heightManifoldColor = laserlineProfile(heightManifoldDistancePixels(heightPlane, pos));
      color = max(color, heightManifoldColor * heightManifoldAlpha);
    }
    `);
  }
  return e5.pointDistanceEnabled && (t.uniforms.add([new e3("angleCutoff", (e6) => V(e6)), new e2("pointDistanceSphere", (e6, i3) => O2(e6, i3))]), t.code.add(o`{
float pointDistanceSphereDistance = sphereDistancePixels(pointDistanceSphere, pos);
vec4 pointDistanceSphereColor = laserlineProfile(pointDistanceSphereDistance);
float pointDistanceSphereAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, abs(dot(normal, normalize(pos - pointDistanceSphere.xyz))));
color = max(color, pointDistanceSphereColor * pointDistanceSphereAlpha);
}`)), e5.lineVerticalPlaneEnabled && (t.uniforms.add([new e3("angleCutoff", (e6) => V(e6)), new e2("lineVerticalPlane", (e6, i3) => j(e6, i3)), new e4("lineVerticalStart", (e6, i3) => y(e6, i3)), new e4("lineVerticalEnd", (e6, i3) => E(e6, i3))]), t.code.add(o`{
if (pointIsWithinLine(pos, lineVerticalStart, lineVerticalEnd)) {
float lineVerticalDistance = planeDistancePixels(lineVerticalPlane, pos);
vec4 lineVerticalColor = laserlineProfile(lineVerticalDistance);
float lineVerticalAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, abs(dot(normal, lineVerticalPlane.xyz)));
color = max(color, lineVerticalColor * lineVerticalAlpha);
}
}`)), e5.intersectsLineEnabled && (t.uniforms.add([new e3("angleCutoff", (e6) => V(e6)), new e4("intersectsLineStart", (e6, i3) => O(T, e6.lineStartWorld, i3.camera.viewMatrix)), new e4("intersectsLineEnd", (e6, i3) => O(T, e6.lineEndWorld, i3.camera.viewMatrix)), new e4("intersectsLineDirection", (e6, i3) => (r(G, e6.intersectsLineSegment.vector), G[3] = 0, z(T, w(G, G, i3.camera.viewMatrix)))), new o4("intersectsLineRadius", (e6) => e6.intersectsLineRadius)]), t.code.add(o`{
if (pointIsWithinLine(pos, intersectsLineStart, intersectsLineEnd)) {
float intersectsLineDistance = lineDistancePixels(intersectsLineStart, intersectsLineDirection, intersectsLineRadius, pos);
vec4 intersectsLineColor = laserlineProfile(intersectsLineDistance);
float intersectsLineAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, 1.0 - abs(dot(normal, intersectsLineDirection)));
color = max(color, intersectsLineColor * intersectsLineAlpha);
}
}`)), t.code.add(o`gl_FragColor = laserlineOutput(color * depthDiscontinuityAlpha);
}`), i2;
}
function V(t) {
  return r2(I, Math.cos(t.angleCutoff), Math.cos(Math.max(0, t.angleCutoff - m(2))));
}
function O2(e5, i2) {
  return O(W, e5.pointDistanceOrigin, i2.camera.viewMatrix), W[3] = x(e5.pointDistanceOrigin, e5.pointDistanceTarget), W;
}
function j(e5, i2) {
  const t = l(e5.lineVerticalPlaneSegment, 0.5, T), n4 = e5.renderCoordsHelper.worldUpAtPosition(t, R2), o5 = z(_3, e5.lineVerticalPlaneSegment.vector), r3 = _(G, n4, o5);
  return z(r3, r3), z2(e5.lineVerticalPlaneSegment.origin, r3, i2.camera.viewMatrix);
}
function y(e5, i2) {
  const t = r(T, e5.lineVerticalPlaneSegment.origin);
  return e5.renderCoordsHelper.setAltitude(t, 0), O(t, t, i2.camera.viewMatrix);
}
function E(e5, i2) {
  const t = u(T, e5.lineVerticalPlaneSegment.origin, e5.lineVerticalPlaneSegment.vector);
  return e5.renderCoordsHelper.setAltitude(t, 0), O(t, t, i2.camera.viewMatrix);
}
function z2(e5, i2, t) {
  return O(U, e5, t), r(G, i2), G[3] = 0, w(G, G, t), _2(U, G, F);
}
var I = n3();
var T = n();
var G = n2();
var R2 = n();
var _3 = n();
var U = n();
var F = p();
var W = R();
var H = Object.freeze(Object.defineProperty({ __proto__: null, build: A, defaultAngleCutoff: C }, Symbol.toStringTag, { value: "Module" }));

export {
  C,
  A,
  H
};
//# sourceMappingURL=chunk-Y26TXFHQ.js.map
