import {
  m
} from "./chunk-IF2TPQ7J.js";
import {
  g as g2
} from "./chunk-5HHROPN6.js";
import "./chunk-TZ4YPYDT.js";
import {
  ee
} from "./chunk-D6BU5EUE.js";
import "./chunk-QYYMKGDW.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-ZK5O2DLX.js";
import "./chunk-CCFNWAA2.js";
import {
  K
} from "./chunk-TSR474T7.js";
import "./chunk-MWEMWROT.js";
import {
  I,
  T
} from "./chunk-LDZI44QV.js";
import "./chunk-JLELSJK5.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import "./chunk-HLLJFAS4.js";
import "./chunk-6DAQTVXB.js";
import "./chunk-FUIIMETN.js";
import "./chunk-2CLVPBYJ.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-M4ZUXRA3.js";
import "./chunk-WJKHSSMC.js";
import {
  f as f2,
  g
} from "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-ONE6GLG5.js";
import {
  ct,
  ut
} from "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import {
  r as r2
} from "./chunk-UCWK623G.js";
import {
  j as j2
} from "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  E,
  c
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  f,
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e,
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/graphics/sources/WFSSourceWorker.js
var E2 = class {
  constructor() {
    this._queryEngine = null, this._customParameters = null, this._snapshotFeatures = async (e2) => {
      const { objectIdField: t } = this._queryEngine, r3 = await K(this._getFeatureUrl ?? "", this._featureType.typeName, this._getFeatureOutputFormat, { customParameters: this._customParameters, dateFields: this._queryEngine.fieldsIndex.dateFields.map((e3) => e3.name), signal: e2 });
      await T(r3), f(e2);
      const a = I(r3, { geometryType: this._queryEngine.geometryType, hasZ: false, objectIdField: t });
      if (!E(this._queryEngine.spatialReference, c)) for (const i of a) r(i.geometry) && (i.geometry = ct(g(ut(i.geometry, this._queryEngine.geometryType, false, false), c, this._queryEngine.spatialReference)));
      let o = 1;
      for (const s3 of a) {
        const e3 = {};
        m(this._fieldsIndex, e3, s3.attributes, true), s3.attributes = e3, null == s3.attributes[t] && (s3.objectId = s3.attributes[t] = o++);
      }
      return a;
    };
  }
  destroy() {
    var _a;
    (_a = this._queryEngine) == null ? void 0 : _a.destroy(), this._queryEngine = null;
  }
  async load(e2, t) {
    const { getFeatureUrl: r3, getFeatureOutputFormat: s3, spatialReference: o, fields: n, geometryType: u, featureType: p, objectIdField: h, customParameters: c2 } = e2;
    this._featureType = p, this._customParameters = c2, this._getFeatureUrl = r3, this._getFeatureOutputFormat = s3, this._fieldsIndex = new r2(n), await this._checkProjection(o), f(t), this._queryEngine = new ee({ fields: n, geometryType: u, hasM: false, hasZ: false, objectIdField: h, spatialReference: o, timeInfo: null, featureStore: new g2({ geometryType: u, hasM: false, hasZ: false }) });
    const m2 = await this._snapshotFeatures(e(t.signal));
    return this._queryEngine.featureStore.addMany(m2), { extent: (await this._queryEngine.fetchRecomputedExtents()).fullExtent };
  }
  async applyEdits() {
    throw new s2("wfs-source:editing-not-supported", "applyEdits() is not supported on WFSLayer");
  }
  async queryFeatures(e2 = {}, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQuery(e2, t.signal);
  }
  async queryFeatureCount(e2 = {}, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForCount(e2, t.signal);
  }
  async queryObjectIds(e2 = {}, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForIds(e2, t.signal);
  }
  async queryExtent(e2 = {}, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForExtent(e2, t.signal);
  }
  async querySnapping(e2, t = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForSnapping(e2, t.signal);
  }
  async refresh(s3) {
    var _a;
    return this._customParameters = s3, (_a = this._snapshotTask) == null ? void 0 : _a.abort(), this._snapshotTask = j2(this._snapshotFeatures), this._snapshotTask.promise.then((e2) => {
      this._queryEngine.featureStore.clear(), e2 && this._queryEngine.featureStore.addMany(e2);
    }, (e2) => {
      this._queryEngine.featureStore.clear(), j(e2) || s.getLogger("esri.layers.WFSLayer").error(new s2("wfs-layer:getfeature-error", "An error occurred during the GetFeature request", { error: e2 }));
    }), await this._waitSnapshotComplete(), { extent: (await this._queryEngine.fetchRecomputedExtents()).fullExtent };
  }
  async _waitSnapshotComplete() {
    if (this._snapshotTask && !this._snapshotTask.finished) {
      try {
        await this._snapshotTask.promise;
      } catch {
      }
      return this._waitSnapshotComplete();
    }
  }
  async _checkProjection(e2) {
    try {
      await f2(c, e2);
    } catch {
      throw new s2("unsupported-projection", "Projection not supported", { spatialReference: e2 });
    }
  }
};
export {
  E2 as default
};
//# sourceMappingURL=WFSSourceWorker-2L7Z7V4X.js.map
