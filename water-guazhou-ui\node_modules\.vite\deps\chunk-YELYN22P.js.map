{"version": 3, "sources": ["../../@arcgis/core/geometry/support/Axis.js", "../../@arcgis/core/core/ObjectStack.js", "../../@arcgis/core/core/VectorStack.js", "../../@arcgis/core/geometry/support/vectorStacks.js", "../../@arcgis/core/geometry/support/ray.js", "../../@arcgis/core/geometry/support/vector.js", "../../@arcgis/core/chunks/sphere.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar n;!function(n){n[n.X=0]=\"X\",n[n.Y=1]=\"Y\",n[n.Z=2]=\"Z\"}(n||(n={}));export{n as Axis};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{nextTick as t}from\"./nextTick.js\";class s{constructor(t){this._allocator=t,this._items=[],this._itemsPtr=0,this._grow()}get(){return 0===this._itemsPtr&&t((()=>this._reset())),this._itemsPtr===this._items.length&&this._grow(),this._items[this._itemsPtr++]}_reset(){const t=Math.min(3*Math.max(8,this._itemsPtr),this._itemsPtr+3*i);this._items.length=Math.min(t,this._items.length),this._itemsPtr=0}_grow(){for(let t=0;t<Math.max(8,Math.min(this._items.length,i));t++)this._items.push(this._allocator())}}const i=1024;export{s as ObjectStack};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ByteSizeUnit as t}from\"./byteSizeEstimations.js\";import{nextTick as e}from\"./nextTick.js\";import{a as s}from\"../chunks/mat3f64.js\";import{a as r}from\"../chunks/mat4f64.js\";import{c as i}from\"../chunks/quatf64.js\";import{c as f}from\"../chunks/vec2f64.js\";import{b as h}from\"../chunks/vec3f64.js\";import{a as m}from\"../chunks/vec4f64.js\";class a{constructor(t,e,s){this._itemByteSize=t,this._itemCreate=e,this._buffers=new Array,this._items=new Array,this._itemsPtr=0,this._itemsPerBuffer=Math.ceil(s/this._itemByteSize)}get(){0===this._itemsPtr&&e((()=>this._reset()));const t=Math.floor(this._itemsPtr/this._itemsPerBuffer);for(;this._buffers.length<=t;){const t=new ArrayBuffer(this._itemsPerBuffer*this._itemByteSize);for(let e=0;e<this._itemsPerBuffer;++e)this._items.push(this._itemCreate(t,e*this._itemByteSize));this._buffers.push(t)}return this._items[this._itemsPtr++]}_reset(){const t=2*(Math.floor(this._itemsPtr/this._itemsPerBuffer)+1);for(;this._buffers.length>t;)this._buffers.pop(),this._items.length=this._buffers.length*this._itemsPerBuffer;this._itemsPtr=0}static createVec2f64(t=c){return new a(16,f,t)}static createVec3f64(t=c){return new a(24,h,t)}static createVec4f64(t=c){return new a(32,m,t)}static createMat3f64(t=c){return new a(72,s,t)}static createMat4f64(t=c){return new a(128,r,t)}static createQuatf64(t=c){return new a(32,i,t)}get test(){return{size:this._buffers.length*this._itemsPerBuffer*this._itemByteSize}}}const c=4*t.KILOBYTES;export{a as VectorStack};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{VectorStack as e}from\"../../core/VectorStack.js\";const t=e.createVec2f64(),c=e.createVec3f64(),r=e.createVec4f64(),a=e.createMat3f64(),f=e.createMat4f64(),o=e.createQuatf64();export{a as sm3d,f as sm4d,o as sq4d,t as sv2d,c as sv3d,r as sv4d};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{equals as i}from\"../../core/arrayUtils.js\";import{ObjectStack as n}from\"../../core/ObjectStack.js\";import{c as r,b as t,f as o,n as c,e,a as s,g as u}from\"../../chunks/vec3.js\";import{a,c as f}from\"../../chunks/vec3f64.js\";import{sv3d as g}from\"./vectorStacks.js\";function d(i){return i?m(a(i.origin),a(i.direction)):m(f(),f())}function m(i,n){return{origin:i,direction:n}}function j(n,r){return i(n.origin,r.origin)&&i(n.direction,r.direction)}function p(i,n){const r=S.get();return r.origin=i,r.direction=n,r}function k(i,n=d()){return v(i.origin,i.direction,n)}function h(i,n,o=d()){return r(o.origin,i),t(o.direction,n,i),o}function v(i,n,t=d()){return r(t.origin,i),r(t.direction,n),t}function b(i,n){const r=o(g.get(),c(g.get(),i.direction),t(g.get(),n,i.origin));return e(r,r)}function l(i,n){return Math.sqrt(b(i,n))}function q(i,n,r){const o=e(i.direction,t(r,n,i.origin));return s(r,i.origin,u(r,i.direction,o)),r}const S=new n((()=>d()));export{q as closestPoint,k as copy,d as create,l as distance,b as distance2,j as equals,h as fromPoints,v as fromValues,p as wrap};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{acosClamped as n}from\"../../core/mathUtils.js\";import{e as t,g as o,l as r,n as s,f as c}from\"../../chunks/vec3.js\";import{c as u}from\"../../chunks/vec3f64.js\";function e(n,r,s){const c=t(n,r)/t(n,n);return o(s,n,c)}function f(n,o){return t(n,o)/r(n)}function a(o,s){const c=t(o,s)/(r(o)*r(s));return-n(c)}function i(o,r,u){s(m,o),s(h,r);const e=t(m,h),f=n(e),a=c(m,m,h);return t(a,u)<0?2*Math.PI-f:f}const m=u(),h=u();export{a as angle,i as angleAroundAxis,e as projectPoint,f as projectPointSignedLength};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../core/has.js\";import t from\"../core/Logger.js\";import{acosClamped as r,cartesianToSpherical as n}from\"../core/mathUtils.js\";import{isNone as e}from\"../core/maybe.js\";import{d as o}from\"./mat4.js\";import{c as s,a as i,g as a,i as c,l as u,f,m,b as p,p as g,n as l,s as h,d}from\"./vec3.js\";import{c as j}from\"./vec3f64.js\";import{c as y}from\"./vec4.js\";import{c as M,f as v}from\"./vec4f64.js\";import{Axis as b}from\"../geometry/support/Axis.js\";import{closestPoint as S}from\"../geometry/support/ray.js\";import{angle as x}from\"../geometry/support/vector.js\";import{sv3d as A,sm4d as P}from\"../geometry/support/vectorStacks.js\";function R(){return M()}function _(t,r=R()){return y(r,t)}function q(t,r){return v(t[0],t[1],t[2],r)}function w(t){return t}function C(t){t[0]=t[1]=t[2]=t[3]=0}function O(t,r){return t[0]=t[1]=t[2]=0,t[3]=r,t}function T(t){return t[3]}function k(t){return t}function E(t,r,n,e){return v(t,r,n,e)}function L(t,r,n){return t!==n&&s(n,t),n[3]=t[3]+r,n}function Z(r,n,e){return t.getLogger(\"esri.geometry.support.sphere\").error(\"sphere.setExtent is not yet supported\"),r===e?e:_(r,e)}function z(t,r,n){if(e(r))return!1;const{origin:o,direction:s}=r,i=U;i[0]=o[0]-t[0],i[1]=o[1]-t[1],i[2]=o[2]-t[2];const a=s[0]*s[0]+s[1]*s[1]+s[2]*s[2];if(0===a)return!1;const c=2*(s[0]*i[0]+s[1]*i[1]+s[2]*i[2]),u=c*c-4*a*(i[0]*i[0]+i[1]*i[1]+i[2]*i[2]-t[3]*t[3]);if(u<0)return!1;const f=Math.sqrt(u);let m=(-c-f)/(2*a);const p=(-c+f)/(2*a);return(m<0||p<m&&p>0)&&(m=p),!(m<0)&&(n&&(n[0]=o[0]+s[0]*m,n[1]=o[1]+s[1]*m,n[2]=o[2]+s[2]*m),!0)}const U=j();function V(t,r){return z(t,r,null)}function X(t,r,n){if(z(t,r,n))return n;const e=Y(t,r,A.get());return i(n,r.origin,a(A.get(),r.direction,c(r.origin,e)/u(r.direction))),n}function Y(t,r,n){const e=A.get(),s=P.get();f(e,r.origin,r.direction);const i=T(t);f(n,e,r.origin),a(n,n,1/u(n)*i);const c=G(t,r.origin),p=x(r.origin,n);return o(s,p+c,e),m(n,n,s),n}function B(t,r,n){return z(t,r,n)?n:(S(r,k(t),n),D(t,n,n))}function D(t,r,n){const e=p(A.get(),r,k(t)),o=a(A.get(),e,t[3]/u(e));return i(n,o,k(t))}function F(t,r){const n=p(A.get(),r,k(t)),e=g(n),o=t[3]*t[3];return Math.sqrt(Math.abs(e-o))}function G(t,n){const e=p(A.get(),n,k(t)),o=u(e),s=T(t),i=s+Math.abs(s-o);return r(s/i)}const H=j();function I(t,r,e,o){const s=p(H,r,k(t));switch(e){case b.X:{const t=n(s,H)[2];return h(o,-Math.sin(t),Math.cos(t),0)}case b.Y:{const t=n(s,H),r=t[1],e=t[2],i=Math.sin(r);return h(o,-i*Math.cos(e),-i*Math.sin(e),Math.cos(r))}case b.Z:return l(o,s);default:return}}function J(t,r){const n=p(Q,r,k(t));return u(n)-t[3]}function K(t,r,n,e){const o=J(t,r),s=I(t,r,b.Z,Q),c=a(Q,s,n-o);return i(e,r,c)}function N(t,r){const n=d(k(t),r),e=T(t);return n<=e*e}const Q=j(),W=R(),$=Object.freeze(Object.defineProperty({__proto__:null,altitudeAt:J,angleToSilhouette:G,axisAt:I,clear:C,closestPoint:B,closestPointOnSilhouette:Y,containsPoint:N,copy:_,create:R,distanceToSilhouette:F,elevate:L,fromCenterAndRadius:q,fromRadius:O,fromValues:E,getCenter:k,getRadius:T,intersectRay:z,intersectRayClosestSilhouette:X,intersectsRay:V,projectPoint:D,setAltitudeAt:K,setExtent:Z,tmpSphere:W,wrap:w},Symbol.toStringTag,{value:\"Module\"}));export{T as a,Y as b,R as c,F as d,O as e,E as f,k as g,_ as h,z as i,V as j,q as k,C as l,N as m,L as n,Z as o,X as p,B as q,D as r,$ as s,W as t,G as u,I as v,w,J as x,K as y};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAIA;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,IAAE,CAAC,IAAE,KAAIA,GAAEA,GAAE,IAAE,CAAC,IAAE,KAAIA,GAAEA,GAAE,IAAE,CAAC,IAAE;AAAG,EAAEA,OAAIA,KAAE,CAAC,EAAE;;;ACA3B,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,aAAWA,IAAE,KAAK,SAAO,CAAC,GAAE,KAAK,YAAU,GAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,MAAK;AAAC,WAAO,MAAI,KAAK,aAAWA,GAAG,MAAI,KAAK,OAAO,CAAE,GAAE,KAAK,cAAY,KAAK,OAAO,UAAQ,KAAK,MAAM,GAAE,KAAK,OAAO,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMA,KAAE,KAAK,IAAI,IAAE,KAAK,IAAI,GAAE,KAAK,SAAS,GAAE,KAAK,YAAU,IAAE,CAAC;AAAE,SAAK,OAAO,SAAO,KAAK,IAAIA,IAAE,KAAK,OAAO,MAAM,GAAE,KAAK,YAAU;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,aAAQA,KAAE,GAAEA,KAAE,KAAK,IAAI,GAAE,KAAK,IAAI,KAAK,OAAO,QAAO,CAAC,CAAC,GAAEA,KAAI,MAAK,OAAO,KAAK,KAAK,WAAW,CAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE;;;ACAhL,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAE;AAAC,SAAK,gBAAcF,IAAE,KAAK,cAAYC,IAAE,KAAK,WAAS,IAAI,SAAM,KAAK,SAAO,IAAI,SAAM,KAAK,YAAU,GAAE,KAAK,kBAAgB,KAAK,KAAKC,KAAE,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,MAAK;AAAC,UAAI,KAAK,aAAWF,GAAG,MAAI,KAAK,OAAO,CAAE;AAAE,UAAMA,KAAE,KAAK,MAAM,KAAK,YAAU,KAAK,eAAe;AAAE,WAAK,KAAK,SAAS,UAAQA,MAAG;AAAC,YAAMA,KAAE,IAAI,YAAY,KAAK,kBAAgB,KAAK,aAAa;AAAE,eAAQC,KAAE,GAAEA,KAAE,KAAK,iBAAgB,EAAEA,GAAE,MAAK,OAAO,KAAK,KAAK,YAAYD,IAAEC,KAAE,KAAK,aAAa,CAAC;AAAE,WAAK,SAAS,KAAKD,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK,OAAO,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMA,KAAE,KAAG,KAAK,MAAM,KAAK,YAAU,KAAK,eAAe,IAAE;AAAG,WAAK,KAAK,SAAS,SAAOA,KAAG,MAAK,SAAS,IAAI,GAAE,KAAK,OAAO,SAAO,KAAK,SAAS,SAAO,KAAK;AAAgB,SAAK,YAAU;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcA,KAAE,GAAE;AAAC,WAAO,IAAI,GAAE,IAAGG,IAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcA,KAAE,GAAE;AAAC,WAAO,IAAI,GAAE,IAAG,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcA,KAAE,GAAE;AAAC,WAAO,IAAI,GAAE,IAAGI,IAAEJ,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcA,KAAE,GAAE;AAAC,WAAO,IAAI,GAAE,IAAGK,IAAEL,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcA,KAAE,GAAE;AAAC,WAAO,IAAI,GAAE,KAAIK,IAAEL,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcA,KAAE,GAAE;AAAC,WAAO,IAAI,GAAE,IAAGK,IAAEL,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM,EAAC,MAAK,KAAK,SAAS,SAAO,KAAK,kBAAgB,KAAK,cAAa;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,IAAEE,GAAE;;;ACA/3C,IAAMI,KAAEC,GAAE,cAAc;AAAxB,IAA0BC,KAAED,GAAE,cAAc;AAA5C,IAA8CE,KAAEF,GAAE,cAAc;AAAhE,IAAkEA,KAAEA,GAAE,cAAc;AAApF,IAAsF,IAAEA,GAAE,cAAc;AAAxG,IAA0GG,KAAEH,GAAE,cAAc;;;ACA2F,SAAS,EAAEI,IAAE;AAAC,SAAOA,KAAE,EAAEC,GAAED,GAAE,MAAM,GAAEC,GAAED,GAAE,SAAS,CAAC,IAAE,EAAE,EAAE,GAAE,EAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEE,IAAE;AAAC,SAAM,EAAC,QAAOF,IAAE,WAAUE,GAAC;AAAC;AAAyE,SAASC,GAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,IAAI;AAAE,SAAOA,GAAE,SAAOF,IAAEE,GAAE,YAAUD,IAAEC;AAAC;AAAC,SAAS,EAAEF,IAAEC,KAAE,EAAE,GAAE;AAAC,SAAOE,GAAEH,GAAE,QAAOA,GAAE,WAAUC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEG,KAAE,EAAE,GAAE;AAAC,SAAO,EAAEA,GAAE,QAAOJ,EAAC,GAAE,EAAEI,GAAE,WAAUH,IAAED,EAAC,GAAEI;AAAC;AAAC,SAASD,GAAEH,IAAEC,IAAEI,KAAE,EAAE,GAAE;AAAC,SAAO,EAAEA,GAAE,QAAOL,EAAC,GAAE,EAAEK,GAAE,WAAUJ,EAAC,GAAEI;AAAC;AAAC,SAAS,EAAEL,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEI,GAAE,IAAI,GAAE,EAAEA,GAAE,IAAI,GAAEN,GAAE,SAAS,GAAE,EAAEM,GAAE,IAAI,GAAEL,IAAED,GAAE,MAAM,CAAC;AAAE,SAAO,EAAEE,IAAEA,EAAC;AAAC;AAA0C,SAAS,EAAEK,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEH,GAAE,WAAU,EAAEE,IAAED,IAAED,GAAE,MAAM,CAAC;AAAE,SAAOI,GAAEF,IAAEF,GAAE,QAAO,EAAEE,IAAEF,GAAE,WAAUG,EAAC,CAAC,GAAED;AAAC;AAAC,IAAM,IAAE,IAAIG,GAAG,MAAI,EAAE,CAAE;;;ACAnxB,SAASC,GAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEH,IAAEC,EAAC,IAAE,EAAED,IAAEA,EAAC;AAAE,SAAO,EAAEE,IAAEF,IAAEG,EAAC;AAAC;AAAC,SAASC,GAAEJ,IAAEK,IAAE;AAAC,SAAO,EAAEL,IAAEK,EAAC,IAAEH,GAAEF,EAAC;AAAC;AAAC,SAASM,GAAED,IAAEH,IAAE;AAAC,QAAMC,KAAE,EAAEE,IAAEH,EAAC,KAAGA,GAAEG,EAAC,IAAEH,GAAEA,EAAC;AAAG,SAAM,CAAC,EAAEC,EAAC;AAAC;AAAC,SAASI,GAAEF,IAAEJ,IAAEO,IAAE;AAAC,IAAEC,IAAEJ,EAAC,GAAE,EAAEK,IAAET,EAAC;AAAE,QAAMF,KAAE,EAAEU,IAAEC,EAAC,GAAEN,KAAE,EAAEL,EAAC,GAAEO,KAAE,EAAEG,IAAEA,IAAEC,EAAC;AAAE,SAAO,EAAEJ,IAAEE,EAAC,IAAE,IAAE,IAAE,KAAK,KAAGJ,KAAEA;AAAC;AAAC,IAAMK,KAAE,EAAE;AAAV,IAAYC,KAAE,EAAE;;;ACAgN,SAAS,IAAG;AAAC,SAAOC,GAAE;AAAC;AAAC,SAASC,GAAEC,IAAEC,KAAE,EAAE,GAAE;AAAC,SAAO,EAAEA,IAAED,EAAC;AAAC;AAAC,SAASE,GAAEF,IAAEC,IAAE;AAAC,SAAOA,GAAED,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,EAAAA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE;AAAC;AAAC,SAASG,GAAEH,IAAEC,IAAE;AAAC,SAAOD,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,GAAEA,GAAE,CAAC,IAAEC,IAAED;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAOA,GAAE,CAAC;AAAC;AAAC,SAASI,GAAEJ,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAEH,IAAEO,IAAE;AAAC,SAAOJ,GAAED,IAAEC,IAAEH,IAAEO,EAAC;AAAC;AAAC,SAASC,GAAEN,IAAEC,IAAEH,IAAE;AAAC,SAAOE,OAAIF,MAAG,EAAEA,IAAEE,EAAC,GAAEF,GAAE,CAAC,IAAEE,GAAE,CAAC,IAAEC,IAAEH;AAAC;AAAC,SAAS,EAAEG,IAAEH,IAAEO,IAAE;AAAC,SAAO,EAAE,UAAU,8BAA8B,EAAE,MAAM,uCAAuC,GAAEJ,OAAII,KAAEA,KAAEN,GAAEE,IAAEI,EAAC;AAAC;AAAC,SAASE,GAAEP,IAAEC,IAAEH,IAAE;AAAC,MAAG,EAAEG,EAAC,EAAE,QAAM;AAAG,QAAK,EAAC,QAAOO,IAAE,WAAUC,GAAC,IAAER,IAAES,KAAE;AAAE,EAAAA,GAAE,CAAC,IAAEF,GAAE,CAAC,IAAER,GAAE,CAAC,GAAEU,GAAE,CAAC,IAAEF,GAAE,CAAC,IAAER,GAAE,CAAC,GAAEU,GAAE,CAAC,IAAEF,GAAE,CAAC,IAAER,GAAE,CAAC;AAAE,QAAMW,KAAEF,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC;AAAE,MAAG,MAAIE,GAAE,QAAM;AAAG,QAAMC,KAAE,KAAGH,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAGG,KAAED,KAAEA,KAAE,IAAED,MAAGD,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEV,GAAE,CAAC,IAAEA,GAAE,CAAC;AAAG,MAAGa,KAAE,EAAE,QAAM;AAAG,QAAMC,KAAE,KAAK,KAAKD,EAAC;AAAE,MAAIE,MAAG,CAACH,KAAEE,OAAI,IAAEH;AAAG,QAAMK,MAAG,CAACJ,KAAEE,OAAI,IAAEH;AAAG,UAAOI,KAAE,KAAGC,KAAED,MAAGC,KAAE,OAAKD,KAAEC,KAAG,EAAED,KAAE,OAAKjB,OAAIA,GAAE,CAAC,IAAEU,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAEM,IAAEjB,GAAE,CAAC,IAAEU,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAEM,IAAEjB,GAAE,CAAC,IAAEU,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAEM,KAAG;AAAG;AAAC,IAAM,IAAE,EAAE;AAAE,SAAS,EAAEf,IAAEC,IAAE;AAAC,SAAOM,GAAEP,IAAEC,IAAE,IAAI;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAEH,IAAE;AAAC,MAAGS,GAAEP,IAAEC,IAAEH,EAAC,EAAE,QAAOA;AAAE,QAAMO,KAAE,EAAEL,IAAEC,IAAEW,GAAE,IAAI,CAAC;AAAE,SAAOC,GAAEf,IAAEG,GAAE,QAAO,EAAEW,GAAE,IAAI,GAAEX,GAAE,WAAU,EAAEA,GAAE,QAAOI,EAAC,IAAEI,GAAER,GAAE,SAAS,CAAC,CAAC,GAAEH;AAAC;AAAC,SAAS,EAAEE,IAAEC,IAAEH,IAAE;AAAC,QAAMO,KAAEO,GAAE,IAAI,GAAEH,KAAE,EAAE,IAAI;AAAE,IAAEJ,IAAEJ,GAAE,QAAOA,GAAE,SAAS;AAAE,QAAMS,KAAE,EAAEV,EAAC;AAAE,IAAEF,IAAEO,IAAEJ,GAAE,MAAM,GAAE,EAAEH,IAAEA,IAAE,IAAEW,GAAEX,EAAC,IAAEY,EAAC;AAAE,QAAME,KAAE,EAAEZ,IAAEC,GAAE,MAAM,GAAEe,KAAEL,GAAEV,GAAE,QAAOH,EAAC;AAAE,SAAOkB,GAAEP,IAAEO,KAAEJ,IAAEP,EAAC,GAAE,EAAEP,IAAEA,IAAEW,EAAC,GAAEX;AAAC;AAAC,SAAS,EAAEE,IAAEC,IAAEH,IAAE;AAAC,SAAOS,GAAEP,IAAEC,IAAEH,EAAC,IAAEA,MAAG,EAAEG,IAAEG,GAAEJ,EAAC,GAAEF,EAAC,GAAE,EAAEE,IAAEF,IAAEA,EAAC;AAAE;AAAC,SAAS,EAAEE,IAAEC,IAAEH,IAAE;AAAC,QAAMO,KAAE,EAAEO,GAAE,IAAI,GAAEX,IAAEG,GAAEJ,EAAC,CAAC,GAAEQ,KAAE,EAAEI,GAAE,IAAI,GAAEP,IAAEL,GAAE,CAAC,IAAES,GAAEJ,EAAC,CAAC;AAAE,SAAOQ,GAAEf,IAAEU,IAAEJ,GAAEJ,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,QAAMH,KAAE,EAAEc,GAAE,IAAI,GAAEX,IAAEG,GAAEJ,EAAC,CAAC,GAAEK,KAAE,EAAEP,EAAC,GAAEU,KAAER,GAAE,CAAC,IAAEA,GAAE,CAAC;AAAE,SAAO,KAAK,KAAK,KAAK,IAAIK,KAAEG,EAAC,CAAC;AAAC;AAAC,SAAS,EAAER,IAAEF,IAAE;AAAC,QAAMO,KAAE,EAAEO,GAAE,IAAI,GAAEd,IAAEM,GAAEJ,EAAC,CAAC,GAAEQ,KAAEC,GAAEJ,EAAC,GAAEI,KAAE,EAAET,EAAC,GAAEU,KAAED,KAAE,KAAK,IAAIA,KAAED,EAAC;AAAE,SAAO,EAAEC,KAAEC,EAAC;AAAC;AAAC,IAAM,IAAE,EAAE;AAAE,SAAS,EAAEV,IAAEC,IAAEI,IAAEG,IAAE;AAAC,QAAMC,KAAE,EAAE,GAAER,IAAEG,GAAEJ,EAAC,CAAC;AAAE,UAAOK,IAAE;AAAA,IAAC,KAAKP,GAAE,GAAE;AAAC,YAAME,KAAE,EAAES,IAAE,CAAC,EAAE,CAAC;AAAE,aAAO,EAAED,IAAE,CAAC,KAAK,IAAIR,EAAC,GAAE,KAAK,IAAIA,EAAC,GAAE,CAAC;AAAA,IAAC;AAAA,IAAC,KAAKF,GAAE,GAAE;AAAC,YAAME,KAAE,EAAES,IAAE,CAAC,GAAER,KAAED,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,GAAEU,KAAE,KAAK,IAAIT,EAAC;AAAE,aAAO,EAAEO,IAAE,CAACE,KAAE,KAAK,IAAIL,EAAC,GAAE,CAACK,KAAE,KAAK,IAAIL,EAAC,GAAE,KAAK,IAAIJ,EAAC,CAAC;AAAA,IAAC;AAAA,IAAC,KAAKH,GAAE;AAAE,aAAO,EAAEU,IAAEC,EAAC;AAAA,IAAE;AAAQ;AAAA,EAAM;AAAC;AAAC,SAAS,EAAET,IAAEC,IAAE;AAAC,QAAMH,KAAE,EAAE,GAAEG,IAAEG,GAAEJ,EAAC,CAAC;AAAE,SAAOS,GAAEX,EAAC,IAAEE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAEH,IAAEO,IAAE;AAAC,QAAMG,KAAE,EAAER,IAAEC,EAAC,GAAEQ,KAAE,EAAET,IAAEC,IAAEH,GAAE,GAAE,CAAC,GAAEc,KAAE,EAAE,GAAEH,IAAEX,KAAEU,EAAC;AAAE,SAAOK,GAAER,IAAEJ,IAAEW,EAAC;AAAC;AAAC,SAAS,EAAEZ,IAAEC,IAAE;AAAC,QAAMH,KAAE,EAAEM,GAAEJ,EAAC,GAAEC,EAAC,GAAEI,KAAE,EAAEL,EAAC;AAAE,SAAOF,MAAGO,KAAEA;AAAC;AAAC,IAAM,IAAE,EAAE;AAAV,IAAY,IAAE,EAAE;AAAhB,IAAkB,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,YAAW,GAAE,mBAAkB,GAAE,QAAO,GAAE,OAAM,GAAE,cAAa,GAAE,0BAAyB,GAAE,eAAc,GAAE,MAAKN,IAAE,QAAO,GAAE,sBAAqB,GAAE,SAAQO,IAAE,qBAAoBJ,IAAE,YAAWC,IAAE,YAAW,GAAE,WAAUC,IAAE,WAAU,GAAE,cAAaG,IAAE,+BAA8B,GAAE,eAAc,GAAE,cAAa,GAAE,eAAc,GAAE,WAAU,GAAE,WAAU,GAAE,MAAK,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["n", "s", "t", "a", "t", "e", "s", "o", "u", "n", "t", "a", "c", "r", "o", "i", "t", "n", "p", "i", "n", "r", "v", "o", "t", "c", "i", "n", "r", "o", "u", "s", "e", "n", "r", "s", "c", "f", "o", "a", "i", "u", "m", "h", "n", "_", "t", "r", "q", "O", "k", "e", "L", "z", "o", "s", "i", "a", "c", "u", "f", "m", "p"]}