import {
  _
} from "./chunk-FDBF4TJR.js";
import "./chunk-YUDXR4IE.js";
import {
  O
} from "./chunk-B3Q27ZSC.js";
import "./chunk-VYC4DNQO.js";
import "./chunk-LVWRJMBJ.js";
import "./chunk-JSANYNBO.js";
import "./chunk-TPRZH2SY.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-P6BL3OFI.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-S2ZSC2TN.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import "./chunk-SJRT3EVN.js";
import "./chunk-2CFIAWMM.js";
import "./chunk-MURG32WB.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-IJ6FZE6K.js";
import "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-YD5Y4V7J.js";
import {
  v
} from "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2 as a,
  y
} from "./chunk-R4CPW7J5.js";
import "./chunk-2CM7MIII.js";
import "./chunk-HP475EI3.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/UnknownLayer.js
var a2 = class extends _(O(b)) {
  constructor(r) {
    super(r), this.resourceInfo = null, this.type = "unknown";
  }
  initialize() {
    this.addResolvingPromise(new Promise((r, e2) => {
      v(() => {
        const r2 = this.resourceInfo && (this.resourceInfo.layerType || this.resourceInfo.type);
        let s2 = "Unknown layer type";
        r2 && (s2 += " " + r2), e2(new s("layer:unknown-layer-type", s2, { layerType: r2 }));
      });
    }));
  }
  read(r, o) {
    super.read({ resourceInfo: r }, o);
  }
  write() {
    return null;
  }
};
e([y({ readOnly: true })], a2.prototype, "resourceInfo", void 0), e([y({ type: ["show", "hide"] })], a2.prototype, "listMode", void 0), e([y({ json: { read: false }, readOnly: true, value: "unknown" })], a2.prototype, "type", void 0), a2 = e([a("esri.layers.UnknownLayer")], a2);
var c = a2;
export {
  c as default
};
//# sourceMappingURL=UnknownLayer-4ZZWODYV.js.map
