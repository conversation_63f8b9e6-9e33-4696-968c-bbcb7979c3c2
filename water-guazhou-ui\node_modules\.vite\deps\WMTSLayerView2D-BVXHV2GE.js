import {
  n,
  t
} from "./chunk-7K6G5AAB.js";
import "./chunk-TAERZTFZ.js";
import "./chunk-KYDW2SHL.js";
import "./chunk-JTTSDQPH.js";
import "./chunk-6DXPU43Z.js";
import "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-53FPJYCC.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-IEBU4QQL.js";
import {
  i
} from "./chunk-5JDQNIY4.js";
import {
  f
} from "./chunk-NEPFZ7PM.js";
import {
  u
} from "./chunk-HWB4LNSZ.js";
import "./chunk-QKWIBVLD.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-22FAZXOH.js";
import {
  r
} from "./chunk-OZZFNS32.js";
import {
  y as y2
} from "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import {
  h
} from "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import {
  e as e2
} from "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-RURSJOSG.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-NOZFLZZL.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  E
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/WMTSLayerView2D.js
var y3 = [102113, 102100, 3857, 3785, 900913];
var _ = [0, 0];
var w = class extends i(t(f(u))) {
  constructor() {
    super(...arguments), this._tileStrategy = null, this._fetchQueue = null, this._tileRequests = /* @__PURE__ */ new Map(), this.layer = null;
  }
  get tileMatrixSet() {
    const e3 = this._getTileMatrixSetBySpatialReference(this.layer.activeLayer);
    return e3 ? (e3.id !== this.layer.activeLayer.tileMatrixSetId && (this.layer.activeLayer.tileMatrixSetId = e3.id), e3) : null;
  }
  update(e3) {
    this._fetchQueue.pause(), this._fetchQueue.state = e3.state, this._tileStrategy.update(e3), this._fetchQueue.resume();
  }
  attach() {
    var _a;
    const e3 = (_a = this.tileMatrixSet) == null ? void 0 : _a.tileInfo;
    e3 && (this._tileInfoView = new h(e3), this._fetchQueue = new y2({ tileInfoView: this._tileInfoView, concurrency: 16, process: (e4, t2) => this.fetchTile(e4, t2) }), this._tileStrategy = new r({ cachePolicy: "keep", resampling: true, acquireTile: (e4) => this.acquireTile(e4), releaseTile: (e4) => this.releaseTile(e4), tileInfoView: this._tileInfoView }), this.addAttachHandles(l(() => {
      var _a2, _b;
      return [(_b = (_a2 = this.layer) == null ? void 0 : _a2.activeLayer) == null ? void 0 : _b.styleId, this.tileMatrixSet];
    }, () => this._refresh())), super.attach());
  }
  detach() {
    var _a, _b;
    super.detach(), (_a = this._tileStrategy) == null ? void 0 : _a.destroy(), (_b = this._fetchQueue) == null ? void 0 : _b.destroy(), this._fetchQueue = this._tileStrategy = this._tileInfoView = null;
  }
  moveStart() {
    this.requestUpdate();
  }
  viewChange() {
    this.requestUpdate();
  }
  moveEnd() {
    this.requestUpdate();
  }
  releaseTile(e3) {
    this._fetchQueue.abort(e3.key.id), this._bitmapView.removeChild(e3), e3.once("detach", () => e3.destroy()), this.requestUpdate();
  }
  acquireTile(e3) {
    const t2 = this._bitmapView.createTile(e3), i2 = t2.bitmap;
    return [i2.x, i2.y] = this._tileInfoView.getTileCoords(_, t2.key), i2.resolution = this._tileInfoView.getTileResolution(t2.key), [i2.width, i2.height] = this._tileInfoView.tileInfo.size, this._enqueueTileFetch(t2), this._bitmapView.addChild(t2), this.requestUpdate(), t2;
  }
  async doRefresh() {
    !this.attached || this.updateRequested || this.suspended || this._refresh();
  }
  isUpdating() {
    var _a;
    return ((_a = this._fetchQueue) == null ? void 0 : _a.updating) ?? false;
  }
  async fetchTile(e3, t2 = {}) {
    const s2 = "tilemapCache" in this.layer ? this.layer.tilemapCache : null, { signal: r2, resamplingLevel: a2 = 0 } = t2;
    if (!s2) return this._fetchImage(e3, r2);
    const l2 = new e2(0, 0, 0, 0);
    let o;
    try {
      await s2.fetchAvailabilityUpsample(e3.level, e3.row, e3.col, l2, { signal: r2 }), o = await this._fetchImage(l2, r2);
    } catch (h2) {
      if (j(h2)) throw h2;
      if (a2 < 3) {
        const i2 = this._tileInfoView.getTileParentId(e3.id);
        if (i2) {
          const s3 = new e2(i2), r3 = await this.fetchTile(s3, { ...t2, resamplingLevel: a2 + 1 });
          return n(this._tileInfoView, r3, s3, e3);
        }
      }
      throw h2;
    }
    return n(this._tileInfoView, o, l2, e3);
  }
  canResume() {
    const e3 = super.canResume();
    return e3 ? null !== this.tileMatrixSet : e3;
  }
  supportsSpatialReference(e3) {
    var _a;
    return ((_a = this.layer.activeLayer.tileMatrixSets) == null ? void 0 : _a.some((t2) => {
      var _a2;
      return E((_a2 = t2.tileInfo) == null ? void 0 : _a2.spatialReference, e3);
    })) ?? false;
  }
  async _enqueueTileFetch(e3) {
    if (!this._fetchQueue.has(e3.key.id)) {
      try {
        const t2 = await this._fetchQueue.push(e3.key);
        e3.bitmap.source = t2, e3.bitmap.width = this._tileInfoView.tileInfo.size[0], e3.bitmap.height = this._tileInfoView.tileInfo.size[1], e3.once("attach", () => this.requestUpdate());
      } catch (s2) {
        j(s2) || s.getLogger(this.declaredClass).error(s2);
      }
      this.requestUpdate();
    }
  }
  async _fetchImage(e3, t2) {
    return this.layer.fetchImageBitmapTile(e3.level, e3.row, e3.col, { signal: t2 });
  }
  _refresh() {
    this._fetchQueue.reset(), this._tileStrategy.tiles.forEach((e3) => {
      if (!e3.bitmap.source) return;
      const t2 = { id: e3.key.id, fulfilled: false, promise: this._fetchQueue.push(e3.key).then((t3) => {
        e3.bitmap.source = t3;
      }).catch((t3) => {
        j(t3) || (e3.bitmap.source = null);
      }).finally(() => {
        e3.requestRender(), t2.fulfilled = true;
      }) };
      this._tileRequests.set(e3, t2);
    });
  }
  _getTileMatrixSetBySpatialReference(e3) {
    const t2 = this.view.spatialReference;
    if (!e3.tileMatrixSets) return null;
    let i2 = e3.tileMatrixSets.find((e4) => {
      var _a;
      return E((_a = e4.tileInfo) == null ? void 0 : _a.spatialReference, t2);
    });
    return !i2 && t2.isWebMercator && (i2 = e3.tileMatrixSets.find((e4) => {
      var _a;
      return y3.includes(((_a = e4.tileInfo) == null ? void 0 : _a.spatialReference.wkid) ?? -1);
    })), i2;
  }
};
e([y()], w.prototype, "_fetchQueue", void 0), e([y({ readOnly: true })], w.prototype, "tileMatrixSet", null), w = e([a("esri.views.2d.layers.WMTSLayerView2D")], w);
var g = w;
export {
  g as default
};
//# sourceMappingURL=WMTSLayerView2D-BVXHV2GE.js.map
