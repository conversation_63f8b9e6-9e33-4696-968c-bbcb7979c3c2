import {
  k
} from "./chunk-ELJWWHWE.js";
import "./chunk-B42W3AOR.js";
import "./chunk-6ESVG4YL.js";
import "./chunk-UQUDWTCY.js";
import "./chunk-IKOX2HGY.js";
import {
  h
} from "./chunk-CCFNWAA2.js";
import "./chunk-3KCCETWY.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-ST2RRB55.js";
import "./chunk-ZIKXCGU7.js";
import "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import "./chunk-A7PY25IH.js";
import "./chunk-SROTSYJS.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/geometry/support/meshUtils/ElevationSamplerWorker.js
var n = class {
  async createIndex(r2, n2) {
    const o2 = new Array();
    if (!r2.vertexAttributes || !r2.vertexAttributes.position) return new h();
    const s = this._createMeshData(r2), a = r(n2) ? await n2.invoke("createIndexThread", s, { transferList: o2 }) : this.createIndexThread(s).result;
    return this._createPooledRBush().fromJSON(a);
  }
  createIndexThread(e) {
    const t = new Float64Array(e.position), r2 = this._createPooledRBush();
    return e.components ? this._createIndexComponentsThread(r2, t, e.components.map((e2) => new Uint32Array(e2))) : this._createIndexAllThread(r2, t);
  }
  _createIndexAllThread(e, t) {
    const r2 = new Array(t.length / 9);
    let n2 = 0;
    for (let s = 0; s < t.length; s += 9) r2[n2++] = o(t, s + 0, s + 3, s + 6);
    return e.load(r2), { result: e.toJSON() };
  }
  _createIndexComponentsThread(e, t, r2) {
    let n2 = 0;
    for (const o2 of r2) n2 += o2.length / 3;
    const s = new Array(n2);
    let a = 0;
    for (const i of r2) for (let e2 = 0; e2 < i.length; e2 += 3) s[a++] = o(t, 3 * i[e2 + 0], 3 * i[e2 + 1], 3 * i[e2 + 2]);
    return e.load(s), { result: e.toJSON() };
  }
  _createMeshData(e) {
    const t = (e.transform ? k({ position: e.vertexAttributes.position, normal: null, tangent: null }, e.transform, e.spatialReference).position : e.vertexAttributes.position).buffer;
    return !e.components || e.components.some((e2) => !e2.faces) ? { position: t } : { position: t, components: e.components.map((e2) => e2.faces) };
  }
  _createPooledRBush() {
    return new h(9, has("esri-csp-restrictions") ? (e) => e : [".minX", ".minY", ".maxX", ".maxY"]);
  }
};
function o(e, t, r2, n2) {
  return { minX: Math.min(e[t + 0], e[r2 + 0], e[n2 + 0]), maxX: Math.max(e[t + 0], e[r2 + 0], e[n2 + 0]), minY: Math.min(e[t + 1], e[r2 + 1], e[n2 + 1]), maxY: Math.max(e[t + 1], e[r2 + 1], e[n2 + 1]), p0: [e[t + 0], e[t + 1], e[t + 2]], p1: [e[r2 + 0], e[r2 + 1], e[r2 + 2]], p2: [e[n2 + 0], e[n2 + 1], e[n2 + 2]] };
}
export {
  n as default
};
//# sourceMappingURL=ElevationSamplerWorker-M2MGTZSV.js.map
