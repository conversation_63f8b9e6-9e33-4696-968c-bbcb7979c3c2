import {
  U,
  a as a2,
  f,
  l
} from "./chunk-QUHG7NMD.js";
import {
  e,
  t2,
  v as v2
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  v
} from "./chunk-C5VMWMBD.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/support/WatchUpdatingTracking.js
var c = class extends v2 {
  constructor() {
    super(...arguments), this.updating = false, this._handleId = 0, this._handles = new t2(), this._scheduleHandleId = 0, this._pendingPromises = /* @__PURE__ */ new Set();
  }
  destroy() {
    this.removeAll(), this._handles.destroy();
  }
  add(e2, s, t3 = {}) {
    return this._installWatch(e2, s, t3, l);
  }
  addWhen(e2, s, t3 = {}) {
    return this._installWatch(e2, s, t3, f);
  }
  addOnCollectionChange(e2, s, { initial: t3 = false, final: i = false } = {}) {
    const n = ++this._handleId;
    return this._handles.add([a2(e2, "after-changes", this._createSyncUpdatingCallback(), U), a2(e2, "change", s, { onListenerAdd: t3 ? (e3) => s({ added: e3.toArray(), removed: [] }) : void 0, onListenerRemove: i ? (e3) => s({ added: [], removed: e3.toArray() }) : void 0 })], n), { remove: () => this._handles.remove(n) };
  }
  addPromise(e2) {
    if (t(e2)) return e2;
    const s = ++this._handleId;
    this._handles.add({ remove: () => {
      this._pendingPromises.delete(e2) && (0 !== this._pendingPromises.size || this._handles.has(_) || this._set("updating", false));
    } }, s), this._pendingPromises.add(e2), this._set("updating", true);
    const t3 = () => this._handles.remove(s);
    return e2.then(t3, t3), e2;
  }
  removeAll() {
    this._pendingPromises.clear(), this._handles.removeAll(), this._set("updating", false);
  }
  _installWatch(e2, s, t3 = {}, i) {
    const n = ++this._handleId;
    t3.sync || this._installSyncUpdatingWatch(e2, n);
    const d2 = i(e2, s, t3);
    return this._handles.add(d2, n), { remove: () => this._handles.remove(n) };
  }
  _installSyncUpdatingWatch(e2, s) {
    const t3 = this._createSyncUpdatingCallback(), i = l(e2, t3, { sync: true, equals: () => false });
    return this._handles.add(i, s), i;
  }
  _createSyncUpdatingCallback() {
    return () => {
      this._handles.remove(_), ++this._scheduleHandleId;
      const e2 = this._scheduleHandleId;
      this._get("updating") || this._set("updating", true), this._handles.add(v(() => {
        e2 === this._scheduleHandleId && (this._set("updating", this._pendingPromises.size > 0), this._handles.remove(_));
      }), _);
    };
  }
};
e([y({ readOnly: true })], c.prototype, "updating", void 0), c = e([a("esri.core.support.WatchUpdatingTracking")], c);
var _ = -42;

// node_modules/@arcgis/core/core/HandleOwner.js
var a3 = (s) => {
  let a4 = class extends s {
    destroy() {
      var _a, _b;
      this.destroyed || ((_a = this._get("handles")) == null ? void 0 : _a.destroy(), (_b = this._get("updatingHandles")) == null ? void 0 : _b.destroy());
    }
    get handles() {
      return this._get("handles") || new t2();
    }
    get updatingHandles() {
      return this._get("updatingHandles") || new c();
    }
  };
  return e([y({ readOnly: true })], a4.prototype, "handles", null), e([y({ readOnly: true })], a4.prototype, "updatingHandles", null), a4 = e([a("esri.core.HandleOwner")], a4), a4;
};
var d = class extends a3(v2) {
};
d = e([a("esri.core.HandleOwner")], d);

export {
  c,
  a3 as a,
  d
};
//# sourceMappingURL=chunk-Q4VCSCSY.js.map
