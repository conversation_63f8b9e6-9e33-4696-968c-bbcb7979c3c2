package org.thingsboard.server.dao.waterSource.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.sampling.Sampling;
import org.thingsboard.server.dao.sql.waterSource.SamplingMapper;
import org.thingsboard.server.dao.util.SqlDao;
import org.thingsboard.server.dao.waterSource.SamplingService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 采样记录服务实现类
 */
@Slf4j
@Service
@SqlDao
public class SamplingServiceImpl implements SamplingService {

    @Autowired
    private SamplingMapper samplingMapper;

    @Override
    public PageData<Sampling> getList(Map<String, Object> params, String tenantId) {
        params.put("tenantId", tenantId);

        // 获取分页参数
        Integer pageSize = 10;
        Integer pageNum = 1;
        if (params.get("pageSize") != null) {
            pageSize = Integer.parseInt(params.get("pageSize").toString());
        }
        if (params.get("pageNum") != null) {
            pageNum = Integer.parseInt(params.get("pageNum").toString());
        }

        IPage<Sampling> page = new Page<>(pageNum, pageSize);
        IPage<Sampling> pageData = samplingMapper.getList(page, params);

        return new PageData<>(pageData.getTotal(), pageData.getRecords());
    }

    @Override
    @Transactional
    public Sampling save(Sampling entity) {
        Date now = new Date();
        if (entity.getId() == null || entity.getId().isEmpty()) {
            entity.setId(UUID.randomUUID().toString());
            entity.setCreateTime(now);
        }
        entity.setUpdateTime(now);
        samplingMapper.insert(entity);
        return entity;
    }

    @Override
    @Transactional
    public void delete(List<String> idList) {
        samplingMapper.deleteBatchIds(idList);
    }

    @Override
    public Sampling getById(String id) {
        return samplingMapper.selectById(id);
    }

    @Override
    @Transactional
    public Sampling uploadRecord(Sampling sampling) {
        sampling.setUpdateTime(new Date());
        samplingMapper.updateById(sampling);
        return sampling;
    }
}
