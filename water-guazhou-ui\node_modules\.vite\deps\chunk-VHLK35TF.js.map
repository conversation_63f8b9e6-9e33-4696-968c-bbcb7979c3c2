{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/dehydratedFeatureComparison.js", "../../@arcgis/core/layers/graphics/dehydratedFeatures.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../core/has.js\";import{isSome as e,isNone as t}from\"../../core/maybe.js\";import{equals as n}from\"../../geometry/support/spatialReferenceUtils.js\";function r(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++){const r=e[n],a=t[n];if(r.length!==a.length)return!1;for(let e=0;e<r.length;e++)if(r[e]!==a[e])return!1}return!0}function a(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!r(e[n],t[n]))return!1;return!0}function i(t,r){return t===r||e(t)&&e(r)&&n(t.spatialReference,r.spatialReference)&&t.x===r.x&&t.y===r.y&&t.z===r.z&&t.m===r.m}function s(e,t){return e.hasZ===t.hasZ&&e.hasM===t.hasM&&(!!n(e.spatialReference,t.spatialReference)&&a(e.paths,t.paths))}function u(e,t){return e.hasZ===t.hasZ&&e.hasM===t.hasM&&(!!n(e.spatialReference,t.spatialReference)&&a(e.rings,t.rings))}function f(e,t){return e.hasZ===t.hasZ&&e.hasM===t.hasM&&(!!n(e.spatialReference,t.spatialReference)&&r(e.points,t.points))}function l(e,t){return e.hasZ===t.hasZ&&e.hasM===t.hasM&&(!!n(e.spatialReference,t.spatialReference)&&(e.xmin===t.xmin&&e.ymin===t.ymin&&e.zmin===t.zmin&&e.xmax===t.xmax&&e.ymax===t.ymax&&e.zmax===t.zmax))}function o(e,n){if(e===n)return!0;if(t(e)||t(n))return!1;if(e.type!==n.type)return!1;switch(e.type){case\"point\":return i(e,n);case\"extent\":return l(e,n);case\"polyline\":return s(e,n);case\"polygon\":return u(e,n);case\"multipoint\":return f(e,n);case\"mesh\":return!1;default:return}}function c(e,t){if(e===t)return!0;if(!e||!t)return!1;const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(const a of n)if(e[a]!==t[a])return!1;return!0}function h(e,t){return e===t||null!=e&&null!=t&&e.objectId===t.objectId&&(!!o(e.geometry,t.geometry)&&!!c(e.attributes,t.attributes))}export{h as equals,i as pointEquals};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{estimateAttributesObjectSize as e}from\"../../core/byteSizeEstimations.js\";import\"../../core/has.js\";import{isSome as t,isNone as s}from\"../../core/maybe.js\";import{estimateSize as r}from\"../../core/typedArrayUtil.js\";import{generateUID as n}from\"../../core/uid.js\";import a from\"../../geometry/SpatialReference.js\";import{empty as o,expandWithNestedArray as i}from\"../../geometry/support/aaBoundingBox.js\";import{empty as l,expandWithNestedArray as p}from\"../../geometry/support/aaBoundingRect.js\";import{unquantizePolyline as c,unquantizePolygon as h,unquantizeMultipoint as u,unquantizePoint as m}from\"../../geometry/support/quantizationUtils.js\";import{featureGeometryTypeKebabDictionary as y}from\"../../geometry/support/typeUtils.js\";import f from\"../support/Field.js\";export{equals}from\"./dehydratedFeatureComparison.js\";class g{constructor(e,t,s){this.uid=e,this.geometry=t,this.attributes=s,this.visible=!0,this.objectId=null,this.centroid=null}}function b(e){return t(e.geometry)}class d{constructor(){this.exceededTransferLimit=!1,this.features=[],this.fields=[],this.hasM=!1,this.hasZ=!1,this.geometryType=null,this.objectIdFieldName=null,this.globalIdFieldName=null,this.geometryProperties=null,this.geohashFieldName=null,this.spatialReference=null,this.transform=null}}function x(e){const s=y.fromJSON(e.geometryType),r=a.fromJSON(e.spatialReference),n=e.transform,o=e.features.map((a=>{const o=Z(a,s,r,e.objectIdFieldName),i=o.geometry;if(t(i)&&n)switch(i.type){case\"point\":o.geometry=m(n,i,i,i.hasZ,i.hasM);break;case\"multipoint\":o.geometry=u(n,i,i,!!i.hasZ,!!i.hasM);break;case\"polygon\":o.geometry=h(n,i,i,!!i.hasZ,!!i.hasM);break;case\"polyline\":o.geometry=c(n,i,i,!!i.hasZ,!!i.hasM);break;case\"extent\":case\"mesh\":o.geometry=i}return o}));return{geometryType:s,features:o,spatialReference:r,fields:e.fields?.map((e=>f.fromJSON(e)))??[],objectIdFieldName:e.objectIdFieldName,globalIdFieldName:e.globalIdFieldName,geohashFieldName:e.geohashFieldName,geometryProperties:e.geometryProperties,hasZ:e.hasZ,hasM:e.hasM,exceededTransferLimit:e.exceededTransferLimit,transform:null}}function Z(e,t,s,r){return{uid:n(),objectId:r&&e.attributes?e.attributes[r]:null,attributes:e.attributes,geometry:j(e.geometry,t,s),visible:!0}}function j(e,t,r){if(s(e))return null;switch(t){case\"point\":{const t=e;return{x:t.x,y:t.y,z:t.z,m:t.m,hasZ:null!=t.z,hasM:null!=t.m,type:\"point\",spatialReference:r}}case\"polyline\":{const t=e;return{paths:t.paths,hasZ:!!t.hasZ,hasM:!!t.hasM,type:\"polyline\",spatialReference:r}}case\"polygon\":{const t=e;return{rings:t.rings,hasZ:!!t.hasZ,hasM:!!t.hasM,type:\"polygon\",spatialReference:r}}case\"multipoint\":{const t=e;return{points:t.points,hasZ:!!t.hasZ,hasM:!!t.hasM,type:\"multipoint\",spatialReference:r}}}}function M(e,t,s,r){return{x:e,y:t,z:s,hasZ:null!=s,hasM:!1,spatialReference:r,type:\"point\"}}function k(e){if(s(e))return 0;let t=32;switch(e.type){case\"point\":t+=42;break;case\"polyline\":case\"polygon\":{let s=0;const r=2+(e.hasZ?1:0)+(e.hasM?1:0),n=\"polyline\"===e.type?e.paths:e.rings;for(const e of n)s+=e.length;t+=8*s*r+64,t+=128*s,t+=34,t+=32*(n.length+1);break}case\"multipoint\":{const s=2+(e.hasZ?1:0)+(e.hasM?1:0),r=e.points.length;t+=8*r*s+64,t+=128*r,t+=34,t+=32;break}case\"extent\":t+=98,e.hasM&&(t+=32),e.hasZ&&(t+=32);break;case\"mesh\":t+=r(e.vertexAttributes.position),t+=r(e.vertexAttributes.normal),t+=r(e.vertexAttributes.uv),t+=r(e.vertexAttributes.tangent)}return t}function N(t){let s=32;return s+=e(t.attributes),s+=3,s+=8+k(t.geometry),s}function z(e){if(s(e))return 0;switch(e.type){case\"point\":return 1;case\"polyline\":{let t=0;for(const s of e.paths)t+=s.length;return t}case\"polygon\":{let t=0;for(const s of e.rings)t+=s.length;return t}case\"multipoint\":return e.points.length;case\"extent\":return 2;case\"mesh\":{const t=e.vertexAttributes&&e.vertexAttributes.position;return t?t.length/3:0}default:return}}function F(e){if(s(e))return!1;switch(e.type){case\"extent\":case\"point\":return!0;case\"polyline\":for(const t of e.paths)if(t.length>0)return!0;return!1;case\"polygon\":for(const t of e.rings)if(t.length>0)return!0;return!1;case\"multipoint\":return e.points.length>0;case\"mesh\":return!e.loaded||e.vertexAttributes.position.length>0}}function I(e,t){switch(o(t),\"mesh\"===e.type&&(e=e.extent),e.type){case\"point\":t[0]=t[3]=e.x,t[1]=t[4]=e.y,e.hasZ&&(t[2]=t[5]=e.z);break;case\"polyline\":for(let s=0;s<e.paths.length;s++)i(t,e.paths[s],!!e.hasZ);break;case\"polygon\":for(let s=0;s<e.rings.length;s++)i(t,e.rings[s],!!e.hasZ);break;case\"multipoint\":i(t,e.points,!!e.hasZ);break;case\"extent\":t[0]=e.xmin,t[1]=e.ymin,t[3]=e.xmax,t[4]=e.ymax,null!=e.zmin&&(t[2]=e.zmin),null!=e.zmax&&(t[5]=e.zmax)}}function v(e,t){switch(l(t),\"mesh\"===e.type&&(e=e.extent),e.type){case\"point\":t[0]=t[2]=e.x,t[1]=t[3]=e.y;break;case\"polyline\":for(let s=0;s<e.paths.length;s++)p(t,e.paths[s]);break;case\"polygon\":for(let s=0;s<e.rings.length;s++)p(t,e.rings[s]);break;case\"multipoint\":p(t,e.points);break;case\"extent\":t[0]=e.xmin,t[1]=e.ymin,t[2]=e.xmax,t[3]=e.ymax}}function R(e,t){return null!=e.objectId?e.objectId:e.attributes&&t?e.attributes[t]:null}export{g as DehydratedFeatureClass,d as DehydratedFeatureSetClass,I as computeAABB,v as computeAABR,k as estimateGeometryObjectSize,N as estimateSize,x as fromFeatureSetJSON,j as fromJSONGeometry,R as getObjectId,b as hasGeometry,F as hasVertices,M as makeDehydratedPoint,z as numVertices};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAIoiB,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAOD,OAAIC,MAAG,EAAED,EAAC,KAAG,EAAEC,EAAC,KAAG,EAAED,GAAE,kBAAiBC,GAAE,gBAAgB,KAAGD,GAAE,MAAIC,GAAE,KAAGD,GAAE,MAAIC,GAAE,KAAGD,GAAE,MAAIC,GAAE,KAAGD,GAAE,MAAIC,GAAE;AAAC;;;ACA+J,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAE;AAAC,SAAK,MAAIF,IAAE,KAAK,WAASC,IAAE,KAAK,aAAWC,IAAE,KAAK,UAAQ,MAAG,KAAK,WAAS,MAAK,KAAK,WAAS;AAAA,EAAI;AAAC;AAAC,SAAS,EAAEF,IAAE;AAAC,SAAO,EAAEA,GAAE,QAAQ;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,wBAAsB,OAAG,KAAK,WAAS,CAAC,GAAE,KAAK,SAAO,CAAC,GAAE,KAAK,OAAK,OAAG,KAAK,OAAK,OAAG,KAAK,eAAa,MAAK,KAAK,oBAAkB,MAAK,KAAK,oBAAkB,MAAK,KAAK,qBAAmB,MAAK,KAAK,mBAAiB,MAAK,KAAK,mBAAiB,MAAK,KAAK,YAAU;AAAA,EAAI;AAAC;AAAi7C,SAAS,EAAEG,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAM,EAAC,GAAEH,IAAE,GAAEC,IAAE,GAAEC,IAAE,MAAK,QAAMA,IAAE,MAAK,OAAG,kBAAiBC,IAAE,MAAK,QAAO;AAAC;AAAupB,SAAS,EAAEC,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAO;AAAE,UAAOA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAQ,aAAO;AAAA,IAAE,KAAI,YAAW;AAAC,UAAIC,KAAE;AAAE,iBAAUC,MAAKF,GAAE,MAAM,CAAAC,MAAGC,GAAE;AAAO,aAAOD;AAAA,IAAC;AAAA,IAAC,KAAI,WAAU;AAAC,UAAIA,KAAE;AAAE,iBAAUC,MAAKF,GAAE,MAAM,CAAAC,MAAGC,GAAE;AAAO,aAAOD;AAAA,IAAC;AAAA,IAAC,KAAI;AAAa,aAAOD,GAAE,OAAO;AAAA,IAAO,KAAI;AAAS,aAAO;AAAA,IAAE,KAAI,QAAO;AAAC,YAAMC,KAAED,GAAE,oBAAkBA,GAAE,iBAAiB;AAAS,aAAOC,KAAEA,GAAE,SAAO,IAAE;AAAA,IAAC;AAAA,IAAC;AAAQ;AAAA,EAAM;AAAC;AAAwU,SAAS,EAAEE,IAAEC,IAAE;AAAC,UAAO,EAAEA,EAAC,GAAE,WAASD,GAAE,SAAOA,KAAEA,GAAE,SAAQA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAQ,MAAAC,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAED,GAAE,GAAEC,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAED,GAAE,GAAEA,GAAE,SAAOC,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAED,GAAE;AAAG;AAAA,IAAM,KAAI;AAAW,eAAQE,KAAE,GAAEA,KAAEF,GAAE,MAAM,QAAOE,KAAI,GAAED,IAAED,GAAE,MAAME,EAAC,GAAE,CAAC,CAACF,GAAE,IAAI;AAAE;AAAA,IAAM,KAAI;AAAU,eAAQE,KAAE,GAAEA,KAAEF,GAAE,MAAM,QAAOE,KAAI,GAAED,IAAED,GAAE,MAAME,EAAC,GAAE,CAAC,CAACF,GAAE,IAAI;AAAE;AAAA,IAAM,KAAI;AAAa,QAAEC,IAAED,GAAE,QAAO,CAAC,CAACA,GAAE,IAAI;AAAE;AAAA,IAAM,KAAI;AAAS,MAAAC,GAAE,CAAC,IAAED,GAAE,MAAKC,GAAE,CAAC,IAAED,GAAE,MAAKC,GAAE,CAAC,IAAED,GAAE,MAAKC,GAAE,CAAC,IAAED,GAAE,MAAK,QAAMA,GAAE,SAAOC,GAAE,CAAC,IAAED,GAAE,OAAM,QAAMA,GAAE,SAAOC,GAAE,CAAC,IAAED,GAAE;AAAA,EAAK;AAAC;AAAC,SAASG,GAAEH,IAAEC,IAAE;AAAC,UAAO,EAAEA,EAAC,GAAE,WAASD,GAAE,SAAOA,KAAEA,GAAE,SAAQA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAQ,MAAAC,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAED,GAAE,GAAEC,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAED,GAAE;AAAE;AAAA,IAAM,KAAI;AAAW,eAAQE,KAAE,GAAEA,KAAEF,GAAE,MAAM,QAAOE,KAAI,GAAED,IAAED,GAAE,MAAME,EAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAU,eAAQA,KAAE,GAAEA,KAAEF,GAAE,MAAM,QAAOE,KAAI,GAAED,IAAED,GAAE,MAAME,EAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAa,QAAED,IAAED,GAAE,MAAM;AAAE;AAAA,IAAM,KAAI;AAAS,MAAAC,GAAE,CAAC,IAAED,GAAE,MAAKC,GAAE,CAAC,IAAED,GAAE,MAAKC,GAAE,CAAC,IAAED,GAAE,MAAKC,GAAE,CAAC,IAAED,GAAE;AAAA,EAAI;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAO,QAAMD,GAAE,WAASA,GAAE,WAASA,GAAE,cAAYC,KAAED,GAAE,WAAWC,EAAC,IAAE;AAAI;", "names": ["t", "r", "e", "t", "s", "e", "t", "s", "r", "e", "t", "s", "e", "t", "s", "v"]}