{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/sources/csv/csv.js", "../../@arcgis/core/layers/graphics/sources/support/CSVSourceWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_parseInfo as e}from\"../../../../core/number.js\";import{normalizeFieldName as t}from\"../../../support/fieldUtils.js\";const n=/^\\s*\"([\\S\\s]*)\"\\s*$/,i=/\"\"/g,r=\"\\n\",l=[\",\",\" \",\";\",\"|\",\"\\t\"];function*o(e,t,n){let i=0;for(;i<=e.length;){const r=e.indexOf(t,i),l=e.substring(i,r>-1?r:void 0);i+=l.length+t.length,n&&!l.trim()||(yield l)}}function s(e){const t=e.includes(\"\\r\\n\")?\"\\r\\n\":r;return o(e,t,!0)}function u(e,t){return o(e,t,!1)}function c(e,t,n){e=e.trim(),t=t?.trim();const i=[],r=Array.from(new Set([n?.delimiter,...l])).filter((e=>null!=e));for(const l of r){const n=d(e,l).length,r=d(t,l).length??n;n>1&&i.push({weight:Math.min(n,r),delimiter:l})}const o=i.sort((({weight:e},{weight:t})=>t-e)).map((({delimiter:e})=>e));for(const l of o){const t=m(f(e,l).names,n?.longitudeField,n?.latitudeField);if(t.longitudeFieldName&&t.latitudeFieldName)return{delimiter:l,locationInfo:t}}return{delimiter:o[0],locationInfo:null}}function*a(e,t,l,o=(()=>Object.create(null))){const c=s(e);c.next();let a=\"\",f=\"\",d=0,m=o(),p=0;e:for(const s of c){const e=u(s,l);for(const r of e)if(a+=f+r,f=\"\",d+=g(r),d%2==0){if(d>0){const e=n.exec(a);if(!e){m=o(),p=0,a=\"\",d=0;continue e}m[t[p]]=e[1].replace(i,'\"'),p++}else m[t[p]]=a,p++;a=\"\",d=0}else f=l;0===d?(yield m,m=o(),p=0):f=r}}function f(e,n){const i=d(e,n).filter((e=>null!=e)),r=i.map((e=>t(e)));for(let t=r.length-1;t>=0;t--)r[t]||(r.splice(t,1),i.splice(t,1));return{names:r,aliases:i}}function d(e,t){if(!e?.length)return[];const r=[];let l=\"\",o=\"\",s=0;const c=u(e,t);for(const u of c)if(l+=o+u,o=\"\",s+=g(u),s%2==0){if(s>0){const e=n.exec(l);e&&r.push(e[1].replace(i,'\"'))}else r.push(l);l=\"\",s=0}else o=t;return r}function g(e){let t=0,n=0;for(n=e.indexOf('\"',n);n>=0;)t++,n=e.indexOf('\"',n+1);return t}function m(e,n,i){n=t(n)?.toLowerCase(),i=t(i)?.toLowerCase();const r=e.map((e=>e.toLowerCase())),l=n?e[r.indexOf(n)]:null,o=i?e[r.indexOf(i)]:null;return{longitudeFieldName:l||e[r.indexOf(w.find((e=>r.includes(e))))],latitudeFieldName:o||e[r.indexOf(F.find((e=>r.includes(e))))]}}function p(e,t,n,i,r){const l=[],o=a(e,n,t),s=[];for(const u of o){if(10===s.length)break;s.push(u)}for(let u=0;u<n.length;u++){const e=n[u],t=i[u];if(e===r.longitudeFieldName||e===r.latitudeFieldName)l.push({name:e,type:\"esriFieldTypeDouble\",alias:t});else{let n,i;switch(b(s.map((t=>t[e])))){case\"integer\":n=\"esriFieldTypeInteger\";break;case\"double\":n=\"esriFieldTypeDouble\";break;case\"date\":n=\"esriFieldTypeDate\",i=36;break;default:n=\"esriFieldTypeString\",i=255}l.push({name:e,type:n,alias:t,length:i})}}return l}function b(e){if(!e.length)return\"string\";const t=/[^+-.,0-9]/;return e.map((e=>{let n=!1;if(\"\"!==e){if(t.test(e))n=!0;else{let t=N(e);if(!isNaN(t))return/[.,]/.test(e)||!Number.isInteger(t)||t>214783647||t<-214783648?\"double\":\"integer\";if(e.includes(\"E\")){if(t=Number(e),!isNaN(t))return\"double\";if(e.includes(\",\")){if(e=e.replace(\",\",\".\"),t=Number(e),!isNaN(t))return\"double\";n=!0}else n=!0}else n=!0}if(n){if(!/^[-]?\\d*[.,]?\\d*$/.test(e)){return h(new Date(e),e)?\"date\":\"string\"}return\"string\"}return\"string\"}})).reduce(((e,t)=>void 0===e?t:void 0===t?e:e===t?t:\"string\"===e||\"string\"===t?\"string\":\"double\"===e||\"double\"===t?\"double\":void 0))}function h(e,t){if(!e||\"[object Date]\"!==Object.prototype.toString.call(e)||isNaN(e.getTime()))return!1;let n=!0;if(!y&&/\\d+\\W*$/.test(t)){const e=t.match(/[a-zA-Z]{2,}/);if(e){let t=!1,i=0;for(;!t&&i<=e.length;)t=!x.test(e[i]),i++;n=!t}}return n}const N=function(){const t=e(),n=new RegExp(\"^\"+t.regexp+\"$\"),i=new RegExp(\"[\"+t.group+\"\\\\s\\\\xa0]\",\"g\"),r=t.factor;return e=>{const l=n.exec(e);if(t.factor=r,!l)return NaN;let o=l[1];if(!l[1]){if(!l[2])return NaN;o=l[2],t.factor*=-1}return o=o.replace(i,\"\").replace(t.decimal,\".\"),+o*t.factor}}(),x=/^((jan(uary)?)|(feb(ruary)?)|(mar(ch)?)|(apr(il)?)|(may)|(jun(e)?)|(jul(y)?)|(aug(ust)?)|(sep(tember)?)|(oct(ober)?)|(nov(ember)?)|(dec(ember)?)|(am)|(pm)|(gmt)|(utc))$/i,y=Number.isNaN(new Date(\"technology 10\").getTime()),F=[\"lat\",\"latitude\",\"latitude83\",\"latdecdeg\",\"lat_dd\",\"y\",\"ycenter\",\"point_y\"],w=[\"lon\",\"lng\",\"long\",\"longitude\",\"longitude83\",\"longdecdeg\",\"long_dd\",\"x\",\"xcenter\",\"point_x\"];export{f as extractFieldNamesAndAliasesFromRow,c as inferDelimiterAndLocationInfo,b as inferFieldType,p as inferFields,h as isValidDate,N as parseNumber,a as parseRows,u as readRowParts,s as readRows,d as splitSingleRow};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../geometry.js\";import e from\"../../../../request.js\";import{createTask as t}from\"../../../../core/asyncUtils.js\";import i from\"../../../../core/Error.js\";import n from\"../../../../core/Logger.js\";import{isAbortError as r}from\"../../../../core/promiseUtils.js\";import{urlToObject as s,getFilename as o}from\"../../../../core/urlUtils.js\";import{projectMany as a}from\"../../../../geometry/projection.js\";import{jsonAdapter as l}from\"../../../../geometry/geometryAdapters/json.js\";import{equals as c,isWebMercator as m,WGS84 as d}from\"../../../../geometry/support/spatialReferenceUtils.js\";import{lngLatToXY as u}from\"../../../../geometry/support/webMercatorUtils.js\";import{OptimizedFeature as p}from\"../../OptimizedFeature.js\";import f from\"../../OptimizedGeometry.js\";import y from\"../../data/FeatureStore.js\";import{checkProjectionSupport as h}from\"../../data/projectionSupport.js\";import{QueryEngine as g}from\"../../data/QueryEngine.js\";import{parseRows as _,isValidDate as I,parseNumber as F,readRows as w,inferDelimiterAndLocationInfo as E,extractFieldNamesAndAliasesFromRow as T,inferFields as j}from\"../csv/csv.js\";import{createDefaultAttributesFunction as x,createDrawingInfo as N}from\"./clientSideDefaults.js\";import S from\"../../../support/FieldsIndex.js\";import{getFieldDefaultValue as q}from\"../../../support/fieldUtils.js\";import b from\"../../../../geometry/SpatialReference.js\";const C=N(\"esriGeometryPoint\"),v=[\"csv\"],O=[0,0];class D{constructor(e,t){this.x=e,this.y=t}}class k{constructor(){this._queryEngine=null,this._snapshotFeatures=async e=>{const t=await this._fetch(e);return this._createFeatures(t)}}destroy(){this._queryEngine?.destroy(),this._queryEngine=null}async load(e,t={}){this._loadOptions=e;const[i]=await Promise.all([this._fetch(t.signal),this._checkProjection(e?.parsingOptions?.spatialReference)]),n=P(i,e);this._locationInfo=n.locationInfo,this._delimiter=n.delimiter,this._queryEngine=this._createQueryEngine(n);const r=await this._createFeatures(i);this._queryEngine.featureStore.addMany(r);const{fullExtent:s,timeExtent:o}=await this._queryEngine.fetchRecomputedExtents();if(n.layerDefinition.extent=s,o){const{start:e,end:t}=o;n.layerDefinition.timeInfo.timeExtent=[e,t]}return n}async applyEdits(){throw new i(\"csv-layer:editing-not-supported\",\"applyEdits() is not supported on CSVLayer\")}async queryFeatures(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQuery(e,t.signal)}async queryFeatureCount(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForCount(e,t.signal)}async queryObjectIds(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForIds(e,t.signal)}async queryExtent(e={},t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForExtent(e,t.signal)}async querySnapping(e,t={}){return await this._waitSnapshotComplete(),this._queryEngine.executeQueryForSnapping(e,t.signal)}async refresh(e){this._loadOptions.customParameters=e,this._snapshotTask?.abort(),this._snapshotTask=t(this._snapshotFeatures),this._snapshotTask.promise.then((e=>{this._queryEngine.featureStore.clear(),e&&this._queryEngine.featureStore.addMany(e)}),(e=>{this._queryEngine.featureStore.clear(),r(e)||n.getLogger(\"esri.layers.CSVLayer\").error(new i(\"csv-layer:refresh\",\"An error occurred during refresh\",{error:e}))})),await this._waitSnapshotComplete();const{fullExtent:s,timeExtent:o}=await this._queryEngine.fetchRecomputedExtents();return{extent:s,timeExtent:o}}async _waitSnapshotComplete(){if(this._snapshotTask&&!this._snapshotTask.finished){try{await this._snapshotTask.promise}catch{}return this._waitSnapshotComplete()}}async _fetch(t){const{url:n,customParameters:r}=this._loadOptions;if(!n)throw new i(\"csv-layer:invalid-source\",\"url not defined\");const o=s(n);return(await e(o.path,{query:{...o.query,...r},responseType:\"text\",signal:t})).data}_createQueryEngine(e){const{objectIdField:t,fields:i,extent:n,timeInfo:r}=e.layerDefinition,s=new y({geometryType:\"esriGeometryPoint\",hasM:!1,hasZ:!1});return new g({fields:i,geometryType:\"esriGeometryPoint\",hasM:!1,hasZ:!1,timeInfo:r,objectIdField:t,spatialReference:n.spatialReference||{wkid:4326},cacheSpatialQueries:!0,featureStore:s})}async _createFeatures(e){const{latitudeFieldName:t,longitudeFieldName:i}=this._locationInfo,{objectIdField:n,fieldsIndex:r,spatialReference:s}=this._queryEngine;let o=[];const d=[],y=r.fields.filter((e=>e.name!==n)).map((e=>e.name));let h=0;const g={};for(const a of r.fields)if(\"esriFieldTypeOID\"!==a.type&&\"esriFieldTypeGlobalID\"!==a.type){const e=q(a);void 0!==e&&(g[a.name]=e)}const w=_(e,y,this._delimiter,x(g,n));for(const a of w){const e=this._parseCoordinateValue(a[t]),s=this._parseCoordinateValue(a[i]);if(null!=s&&null!=e&&!isNaN(e)&&!isNaN(s)){a[t]=e,a[i]=s;for(const e in a)if(e!==t&&e!==i)if(r.isDateField(e)){const t=new Date(a[e]);a[e]=I(t,a[e])?t.getTime():null}else if(r.isNumericField(e)){const t=F(a[e]);isNaN(t)?a[e]=null:a[e]=t}a[n]=h,h++,o.push(new D(s,e)),d.push(a)}}if(!c({wkid:4326},s))if(m(s))for(const a of o)[a.x,a.y]=u(a.x,a.y,O);else o=a(l,o,b.WGS84,s,null,null);const E=[];for(let a=0;a<o.length;a++){const{x:e,y:t}=o[a],i=d[a];i[n]=a+1,E.push(new p(new f([],[e,t]),i,null,i[n]))}return E}_parseCoordinateValue(e){if(null==e||\"\"===e)return null;let t=F(e);return(isNaN(t)||Math.abs(t)>181)&&(t=parseFloat(e)),t}async _checkProjection(e){try{await h(d,e)}catch{throw new i(\"csv-layer:projection-not-supported\",\"Projection not supported\")}}}function P(e,t){const n=t.parsingOptions||{},r={delimiter:n.delimiter,layerDefinition:null,locationInfo:{latitudeFieldName:n.latitudeField,longitudeFieldName:n.longitudeField}},s=r.layerDefinition={name:o(t.url,v)||\"csv\",drawingInfo:C,geometryType:\"esriGeometryPoint\",objectIdField:null,fields:[],timeInfo:n.timeInfo,extent:{xmin:Number.POSITIVE_INFINITY,ymin:Number.POSITIVE_INFINITY,xmax:Number.NEGATIVE_INFINITY,ymax:Number.NEGATIVE_INFINITY,spatialReference:n.spatialReference||{wkid:4326}}},a=w(e),l=a.next().value?.trim(),c=a.next().value?.trim();if(!l)throw new i(\"csv-layer:empty-csv\",\"CSV is empty\",{csv:e});const{delimiter:m,locationInfo:d}=E(l,c,n);if(!m)throw new i(\"csv-layer:invalid-delimiter\",\"Unable to detect the delimiter from CSV\",{firstLine:l,secondLine:c,parsingOptions:n});if(!d)throw new i(\"csv-layer:location-fields-not-found\",\"Unable to identify latitude and longitude fields from the CSV file\",{firstLine:l,secondLine:c,parsingOptions:n});r.locationInfo=d,r.delimiter=m;const{names:u,aliases:p}=T(l,m),f=j(e,r.delimiter,u,p,r.locationInfo);if(n.fields?.length){const e=new S(n.fields);for(const t of f){const i=e.get(t.name);i&&Object.assign(t,i)}}if(!f.some((e=>\"esriFieldTypeOID\"===e.type&&(s.objectIdField=e.name,!0)))){const e={name:\"__OBJECTID\",alias:\"__OBJECTID\",type:\"esriFieldTypeOID\",editable:!1,nullable:!1};s.objectIdField=e.name,f.unshift(e)}s.fields=f;const y=new S(s.fields);if(r.locationInfo&&(r.locationInfo.latitudeFieldName=y.get(r.locationInfo.latitudeFieldName).name,r.locationInfo.longitudeFieldName=y.get(r.locationInfo.longitudeFieldName).name),s.timeInfo){const e=s.timeInfo;if(e.startTimeField){const t=y.get(e.startTimeField);t?(e.startTimeField=t.name,t.type=\"esriFieldTypeDate\"):e.startTimeField=null}if(e.endTimeField){const t=y.get(e.endTimeField);t?(e.endTimeField=t.name,t.type=\"esriFieldTypeDate\"):e.endTimeField=null}if(e.trackIdField){const t=y.get(e.trackIdField);e.trackIdField=t?t.name:null}e.startTimeField||e.endTimeField||(s.timeInfo=null)}return r}export{k as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4H,IAAM,IAAE;AAAR,IAA8BA,KAAE;AAAhC,IAAsCC,KAAE;AAAxC,IAA6C,IAAE,CAAC,KAAI,KAAI,KAAI,KAAI,GAAI;AAAE,UAASC,GAAE,GAAEC,IAAEC,IAAE;AAAC,MAAIJ,KAAE;AAAE,SAAKA,MAAG,EAAE,UAAQ;AAAC,UAAMC,KAAE,EAAE,QAAQE,IAAEH,EAAC,GAAEK,KAAE,EAAE,UAAUL,IAAEC,KAAE,KAAGA,KAAE,MAAM;AAAE,IAAAD,MAAGK,GAAE,SAAOF,GAAE,QAAOC,MAAG,CAACC,GAAE,KAAK,MAAI,MAAMA;AAAA,EAAE;AAAC;AAAC,SAASC,GAAE,GAAE;AAAC,QAAMH,KAAE,EAAE,SAAS,MAAM,IAAE,SAAOF;AAAE,SAAOC,GAAE,GAAEC,IAAE,IAAE;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,SAAOD,GAAE,GAAEC,IAAE,KAAE;AAAC;AAAC,SAASI,GAAE,GAAEJ,IAAEC,IAAE;AAAC,MAAE,EAAE,KAAK,GAAED,KAAEA,MAAA,gBAAAA,GAAG;AAAO,QAAMH,KAAE,CAAC,GAAEC,KAAE,MAAM,KAAK,oBAAI,IAAI,CAACG,MAAA,gBAAAA,GAAG,WAAU,GAAG,CAAC,CAAC,CAAC,EAAE,OAAQ,CAAAI,OAAG,QAAMA,EAAE;AAAE,aAAUH,MAAKJ,IAAE;AAAC,UAAMG,KAAE,EAAE,GAAEC,EAAC,EAAE,QAAOJ,KAAE,EAAEE,IAAEE,EAAC,EAAE,UAAQD;AAAE,IAAAA,KAAE,KAAGJ,GAAE,KAAK,EAAC,QAAO,KAAK,IAAII,IAAEH,EAAC,GAAE,WAAUI,GAAC,CAAC;AAAA,EAAC;AAAC,QAAMH,KAAEF,GAAE,KAAM,CAAC,EAAC,QAAOQ,GAAC,GAAE,EAAC,QAAOL,GAAC,MAAIA,KAAEK,EAAE,EAAE,IAAK,CAAC,EAAC,WAAUA,GAAC,MAAIA,EAAE;AAAE,aAAUH,MAAKH,IAAE;AAAC,UAAMC,KAAE,EAAEM,GAAE,GAAEJ,EAAC,EAAE,OAAMD,MAAA,gBAAAA,GAAG,gBAAeA,MAAA,gBAAAA,GAAG,aAAa;AAAE,QAAGD,GAAE,sBAAoBA,GAAE,kBAAkB,QAAM,EAAC,WAAUE,IAAE,cAAaF,GAAC;AAAA,EAAC;AAAC,SAAM,EAAC,WAAUD,GAAE,CAAC,GAAE,cAAa,KAAI;AAAC;AAAC,UAASQ,GAAE,GAAEP,IAAEE,IAAEH,KAAG,MAAI,uBAAO,OAAO,IAAI,GAAG;AAAC,QAAMK,KAAED,GAAE,CAAC;AAAE,EAAAC,GAAE,KAAK;AAAE,MAAIG,KAAE,IAAGD,KAAE,IAAGE,KAAE,GAAEC,KAAEV,GAAE,GAAEW,KAAE;AAAE,IAAE,YAAUP,MAAKC,IAAE;AAAC,UAAMC,KAAE,EAAEF,IAAED,EAAC;AAAE,eAAUJ,MAAKO,GAAE,KAAGE,MAAGD,KAAER,IAAEQ,KAAE,IAAGE,MAAGG,GAAEb,EAAC,GAAEU,KAAE,KAAG,GAAE;AAAC,UAAGA,KAAE,GAAE;AAAC,cAAMH,KAAE,EAAE,KAAKE,EAAC;AAAE,YAAG,CAACF,IAAE;AAAC,UAAAI,KAAEV,GAAE,GAAEW,KAAE,GAAEH,KAAE,IAAGC,KAAE;AAAE,mBAAS;AAAA,QAAC;AAAC,QAAAC,GAAET,GAAEU,EAAC,CAAC,IAAEL,GAAE,CAAC,EAAE,QAAQR,IAAE,GAAG,GAAEa;AAAA,MAAG,MAAM,CAAAD,GAAET,GAAEU,EAAC,CAAC,IAAEH,IAAEG;AAAI,MAAAH,KAAE,IAAGC,KAAE;AAAA,IAAC,MAAM,CAAAF,KAAEJ;AAAE,UAAIM,MAAG,MAAMC,IAAEA,KAAEV,GAAE,GAAEW,KAAE,KAAGJ,KAAER;AAAA,EAAC;AAAC;AAAC,SAASQ,GAAE,GAAEL,IAAE;AAAC,QAAMJ,KAAE,EAAE,GAAEI,EAAC,EAAE,OAAQ,CAAAI,OAAG,QAAMA,EAAE,GAAEP,KAAED,GAAE,IAAK,CAAAQ,OAAG,EAAEA,EAAC,CAAE;AAAE,WAAQL,KAAEF,GAAE,SAAO,GAAEE,MAAG,GAAEA,KAAI,CAAAF,GAAEE,EAAC,MAAIF,GAAE,OAAOE,IAAE,CAAC,GAAEH,GAAE,OAAOG,IAAE,CAAC;AAAG,SAAM,EAAC,OAAMF,IAAE,SAAQD,GAAC;AAAC;AAAC,SAAS,EAAE,GAAEG,IAAE;AAAC,MAAG,EAAC,uBAAG,QAAO,QAAM,CAAC;AAAE,QAAMF,KAAE,CAAC;AAAE,MAAII,KAAE,IAAGH,KAAE,IAAGI,KAAE;AAAE,QAAMC,KAAE,EAAE,GAAEJ,EAAC;AAAE,aAAUY,MAAKR,GAAE,KAAGF,MAAGH,KAAEa,IAAEb,KAAE,IAAGI,MAAGQ,GAAEC,EAAC,GAAET,KAAE,KAAG,GAAE;AAAC,QAAGA,KAAE,GAAE;AAAC,YAAME,KAAE,EAAE,KAAKH,EAAC;AAAE,MAAAG,MAAGP,GAAE,KAAKO,GAAE,CAAC,EAAE,QAAQR,IAAE,GAAG,CAAC;AAAA,IAAC,MAAM,CAAAC,GAAE,KAAKI,EAAC;AAAE,IAAAA,KAAE,IAAGC,KAAE;AAAA,EAAC,MAAM,CAAAJ,KAAEC;AAAE,SAAOF;AAAC;AAAC,SAASa,GAAE,GAAE;AAAC,MAAIX,KAAE,GAAEC,KAAE;AAAE,OAAIA,KAAE,EAAE,QAAQ,KAAIA,EAAC,GAAEA,MAAG,IAAG,CAAAD,MAAIC,KAAE,EAAE,QAAQ,KAAIA,KAAE,CAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,EAAE,GAAEC,IAAEJ,IAAE;AAJ9uD;AAI+uD,EAAAI,MAAE,OAAEA,EAAC,MAAH,mBAAM,eAAcJ,MAAE,OAAEA,EAAC,MAAH,mBAAM;AAAc,QAAMC,KAAE,EAAE,IAAK,CAAAO,OAAGA,GAAE,YAAY,CAAE,GAAEH,KAAED,KAAE,EAAEH,GAAE,QAAQG,EAAC,CAAC,IAAE,MAAKF,KAAEF,KAAE,EAAEC,GAAE,QAAQD,EAAC,CAAC,IAAE;AAAK,SAAM,EAAC,oBAAmBK,MAAG,EAAEJ,GAAE,QAAQ,EAAE,KAAM,CAAAO,OAAGP,GAAE,SAASO,EAAC,CAAE,CAAC,CAAC,GAAE,mBAAkBN,MAAG,EAAED,GAAE,QAAQ,EAAE,KAAM,CAAAO,OAAGP,GAAE,SAASO,EAAC,CAAE,CAAC,CAAC,EAAC;AAAC;AAAC,SAASK,GAAE,GAAEV,IAAEC,IAAEJ,IAAEC,IAAE;AAAC,QAAMI,KAAE,CAAC,GAAEH,KAAEQ,GAAE,GAAEN,IAAED,EAAC,GAAEG,KAAE,CAAC;AAAE,aAAUS,MAAKb,IAAE;AAAC,QAAG,OAAKI,GAAE,OAAO;AAAM,IAAAA,GAAE,KAAKS,EAAC;AAAA,EAAC;AAAC,WAAQA,KAAE,GAAEA,KAAEX,GAAE,QAAOW,MAAI;AAAC,UAAMP,KAAEJ,GAAEW,EAAC,GAAEZ,KAAEH,GAAEe,EAAC;AAAE,QAAGP,OAAIP,GAAE,sBAAoBO,OAAIP,GAAE,kBAAkB,CAAAI,GAAE,KAAK,EAAC,MAAKG,IAAE,MAAK,uBAAsB,OAAML,GAAC,CAAC;AAAA,SAAM;AAAC,UAAIC,IAAEJ;AAAE,cAAO,EAAEM,GAAE,IAAK,CAAAH,OAAGA,GAAEK,EAAC,CAAE,CAAC,GAAE;AAAA,QAAC,KAAI;AAAU,UAAAJ,KAAE;AAAuB;AAAA,QAAM,KAAI;AAAS,UAAAA,KAAE;AAAsB;AAAA,QAAM,KAAI;AAAO,UAAAA,KAAE,qBAAoBJ,KAAE;AAAG;AAAA,QAAM;AAAQ,UAAAI,KAAE,uBAAsBJ,KAAE;AAAA,MAAG;AAAC,MAAAK,GAAE,KAAK,EAAC,MAAKG,IAAE,MAAKJ,IAAE,OAAMD,IAAE,QAAOH,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG,CAAC,EAAE,OAAO,QAAM;AAAS,QAAMF,KAAE;AAAa,SAAO,EAAE,IAAK,CAAAK,OAAG;AAAC,QAAIJ,KAAE;AAAG,QAAG,OAAKI,IAAE;AAAC,UAAGL,GAAE,KAAKK,EAAC,EAAE,CAAAJ,KAAE;AAAA,WAAO;AAAC,YAAID,KAAE,EAAEK,EAAC;AAAE,YAAG,CAAC,MAAML,EAAC,EAAE,QAAM,OAAO,KAAKK,EAAC,KAAG,CAAC,OAAO,UAAUL,EAAC,KAAGA,KAAE,aAAWA,KAAE,aAAW,WAAS;AAAU,YAAGK,GAAE,SAAS,GAAG,GAAE;AAAC,cAAGL,KAAE,OAAOK,EAAC,GAAE,CAAC,MAAML,EAAC,EAAE,QAAM;AAAS,cAAGK,GAAE,SAAS,GAAG,GAAE;AAAC,gBAAGA,KAAEA,GAAE,QAAQ,KAAI,GAAG,GAAEL,KAAE,OAAOK,EAAC,GAAE,CAAC,MAAML,EAAC,EAAE,QAAM;AAAS,YAAAC,KAAE;AAAA,UAAE,MAAM,CAAAA,KAAE;AAAA,QAAE,MAAM,CAAAA,KAAE;AAAA,MAAE;AAAC,UAAGA,IAAE;AAAC,YAAG,CAAC,oBAAoB,KAAKI,EAAC,GAAE;AAAC,iBAAO,EAAE,IAAI,KAAKA,EAAC,GAAEA,EAAC,IAAE,SAAO;AAAA,QAAQ;AAAC,eAAM;AAAA,MAAQ;AAAC,aAAM;AAAA,IAAQ;AAAA,EAAC,CAAE,EAAE,OAAQ,CAACA,IAAEL,OAAI,WAASK,KAAEL,KAAE,WAASA,KAAEK,KAAEA,OAAIL,KAAEA,KAAE,aAAWK,MAAG,aAAWL,KAAE,WAAS,aAAWK,MAAG,aAAWL,KAAE,WAAS,MAAO;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAG,CAAC,KAAG,oBAAkB,OAAO,UAAU,SAAS,KAAK,CAAC,KAAG,MAAM,EAAE,QAAQ,CAAC,EAAE,QAAM;AAAG,MAAIC,KAAE;AAAG,MAAG,CAACY,MAAG,UAAU,KAAKb,EAAC,GAAE;AAAC,UAAMK,KAAEL,GAAE,MAAM,cAAc;AAAE,QAAGK,IAAE;AAAC,UAAIL,KAAE,OAAGH,KAAE;AAAE,aAAK,CAACG,MAAGH,MAAGQ,GAAE,SAAQ,CAAAL,KAAE,CAAC,EAAE,KAAKK,GAAER,EAAC,CAAC,GAAEA;AAAI,MAAAI,KAAE,CAACD;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,IAAM,IAAE,WAAU;AAAC,QAAMD,KAAE,EAAE,GAAEC,KAAE,IAAI,OAAO,MAAID,GAAE,SAAO,GAAG,GAAEH,KAAE,IAAI,OAAO,MAAIG,GAAE,QAAM,aAAY,GAAG,GAAEF,KAAEE,GAAE;AAAO,SAAO,OAAG;AAAC,UAAME,KAAED,GAAE,KAAK,CAAC;AAAE,QAAGD,GAAE,SAAOF,IAAE,CAACI,GAAE,QAAO;AAAI,QAAIH,KAAEG,GAAE,CAAC;AAAE,QAAG,CAACA,GAAE,CAAC,GAAE;AAAC,UAAG,CAACA,GAAE,CAAC,EAAE,QAAO;AAAI,MAAAH,KAAEG,GAAE,CAAC,GAAEF,GAAE,UAAQ;AAAA,IAAE;AAAC,WAAOD,KAAEA,GAAE,QAAQF,IAAE,EAAE,EAAE,QAAQG,GAAE,SAAQ,GAAG,GAAE,CAACD,KAAEC,GAAE;AAAA,EAAM;AAAC,EAAE;AAAvS,IAAyS,IAAE;AAA3S,IAAuda,KAAE,OAAO,OAAM,oBAAI,KAAK,eAAe,GAAE,QAAQ,CAAC;AAAzgB,IAA2gB,IAAE,CAAC,OAAM,YAAW,cAAa,aAAY,UAAS,KAAI,WAAU,SAAS;AAAxlB,IAA0lB,IAAE,CAAC,OAAM,OAAM,QAAO,aAAY,eAAc,cAAa,WAAU,KAAI,WAAU,SAAS;;;ACA/qF,IAAM,IAAE,EAAE,mBAAmB;AAA7B,IAA+B,IAAE,CAAC,KAAK;AAAvC,IAAyC,IAAE,CAAC,GAAE,CAAC;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAY,GAAEC,IAAE;AAAC,SAAK,IAAE,GAAE,KAAK,IAAEA;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,eAAa,MAAK,KAAK,oBAAkB,OAAM,MAAG;AAAC,YAAMD,KAAE,MAAM,KAAK,OAAO,CAAC;AAAE,aAAO,KAAK,gBAAgBA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJhnD;AAIinD,eAAK,iBAAL,mBAAmB,WAAU,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,MAAM,KAAK,GAAEA,KAAE,CAAC,GAAE;AAJvrD;AAIwrD,SAAK,eAAa;AAAE,UAAK,CAACE,EAAC,IAAE,MAAM,QAAQ,IAAI,CAAC,KAAK,OAAOF,GAAE,MAAM,GAAE,KAAK,kBAAiB,4BAAG,mBAAH,mBAAmB,gBAAgB,CAAC,CAAC,GAAEG,KAAE,EAAED,IAAE,CAAC;AAAE,SAAK,gBAAcC,GAAE,cAAa,KAAK,aAAWA,GAAE,WAAU,KAAK,eAAa,KAAK,mBAAmBA,EAAC;AAAE,UAAMC,KAAE,MAAM,KAAK,gBAAgBF,EAAC;AAAE,SAAK,aAAa,aAAa,QAAQE,EAAC;AAAE,UAAK,EAAC,YAAWC,IAAE,YAAWC,GAAC,IAAE,MAAM,KAAK,aAAa,uBAAuB;AAAE,QAAGH,GAAE,gBAAgB,SAAOE,IAAEC,IAAE;AAAC,YAAK,EAAC,OAAMC,IAAE,KAAIP,GAAC,IAAEM;AAAE,MAAAH,GAAE,gBAAgB,SAAS,aAAW,CAACI,IAAEP,EAAC;AAAA,IAAC;AAAC,WAAOG;AAAA,EAAC;AAAA,EAAC,MAAM,aAAY;AAAC,UAAM,IAAIE,GAAE,mCAAkC,2CAA2C;AAAA,EAAC;AAAA,EAAC,MAAM,cAAc,IAAE,CAAC,GAAEL,KAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,aAAa,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAkB,IAAE,CAAC,GAAEA,KAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,qBAAqB,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,eAAe,IAAE,CAAC,GAAEA,KAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,mBAAmB,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,YAAY,IAAE,CAAC,GAAEA,KAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,sBAAsB,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,cAAc,GAAEA,KAAE,CAAC,GAAE;AAAC,WAAO,MAAM,KAAK,sBAAsB,GAAE,KAAK,aAAa,wBAAwB,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQ,GAAE;AAJl6F;AAIm6F,SAAK,aAAa,mBAAiB,IAAE,UAAK,kBAAL,mBAAoB,SAAQ,KAAK,gBAAcQ,GAAE,KAAK,iBAAiB,GAAE,KAAK,cAAc,QAAQ,KAAM,CAAAD,OAAG;AAAC,WAAK,aAAa,aAAa,MAAM,GAAEA,MAAG,KAAK,aAAa,aAAa,QAAQA,EAAC;AAAA,IAAC,GAAI,CAAAA,OAAG;AAAC,WAAK,aAAa,aAAa,MAAM,GAAE,EAAEA,EAAC,KAAG,EAAE,UAAU,sBAAsB,EAAE,MAAM,IAAIF,GAAE,qBAAoB,oCAAmC,EAAC,OAAME,GAAC,CAAC,CAAC;AAAA,IAAC,CAAE,GAAE,MAAM,KAAK,sBAAsB;AAAE,UAAK,EAAC,YAAWF,IAAE,YAAWC,GAAC,IAAE,MAAM,KAAK,aAAa,uBAAuB;AAAE,WAAM,EAAC,QAAOD,IAAE,YAAWC,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAuB;AAAC,QAAG,KAAK,iBAAe,CAAC,KAAK,cAAc,UAAS;AAAC,UAAG;AAAC,cAAM,KAAK,cAAc;AAAA,MAAO,QAAM;AAAA,MAAC;AAAC,aAAO,KAAK,sBAAsB;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAON,IAAE;AAAC,UAAK,EAAC,KAAIG,IAAE,kBAAiBC,GAAC,IAAE,KAAK;AAAa,QAAG,CAACD,GAAE,OAAM,IAAIE,GAAE,4BAA2B,iBAAiB;AAAE,UAAMC,KAAE,EAAEH,EAAC;AAAE,YAAO,MAAM,EAAEG,GAAE,MAAK,EAAC,OAAM,EAAC,GAAGA,GAAE,OAAM,GAAGF,GAAC,GAAE,cAAa,QAAO,QAAOJ,GAAC,CAAC,GAAG;AAAA,EAAI;AAAA,EAAC,mBAAmB,GAAE;AAAC,UAAK,EAAC,eAAcA,IAAE,QAAOE,IAAE,QAAOC,IAAE,UAASC,GAAC,IAAE,EAAE,iBAAgBC,KAAE,IAAI,EAAE,EAAC,cAAa,qBAAoB,MAAK,OAAG,MAAK,MAAE,CAAC;AAAE,WAAO,IAAI,GAAE,EAAC,QAAOH,IAAE,cAAa,qBAAoB,MAAK,OAAG,MAAK,OAAG,UAASE,IAAE,eAAcJ,IAAE,kBAAiBG,GAAE,oBAAkB,EAAC,MAAK,KAAI,GAAE,qBAAoB,MAAG,cAAaE,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgB,GAAE;AAAC,UAAK,EAAC,mBAAkBL,IAAE,oBAAmBE,GAAC,IAAE,KAAK,eAAc,EAAC,eAAcC,IAAE,aAAYC,IAAE,kBAAiBC,GAAC,IAAE,KAAK;AAAa,QAAIC,KAAE,CAAC;AAAE,UAAMG,KAAE,CAAC,GAAEC,KAAEN,GAAE,OAAO,OAAQ,CAAAG,OAAGA,GAAE,SAAOJ,EAAE,EAAE,IAAK,CAAAI,OAAGA,GAAE,IAAK;AAAE,QAAII,KAAE;AAAE,UAAMC,KAAE,CAAC;AAAE,eAAUC,MAAKT,GAAE,OAAO,KAAG,uBAAqBS,GAAE,QAAM,4BAA0BA,GAAE,MAAK;AAAC,YAAMN,KAAE,EAAEM,EAAC;AAAE,iBAASN,OAAIK,GAAEC,GAAE,IAAI,IAAEN;AAAA,IAAE;AAAC,UAAMO,KAAED,GAAE,GAAEH,IAAE,KAAK,YAAW,EAAEE,IAAET,EAAC,CAAC;AAAE,eAAUU,MAAKC,IAAE;AAAC,YAAMP,KAAE,KAAK,sBAAsBM,GAAEb,EAAC,CAAC,GAAEK,KAAE,KAAK,sBAAsBQ,GAAEX,EAAC,CAAC;AAAE,UAAG,QAAMG,MAAG,QAAME,MAAG,CAAC,MAAMA,EAAC,KAAG,CAAC,MAAMF,EAAC,GAAE;AAAC,QAAAQ,GAAEb,EAAC,IAAEO,IAAEM,GAAEX,EAAC,IAAEG;AAAE,mBAAUE,MAAKM,GAAE,KAAGN,OAAIP,MAAGO,OAAIL;AAAE,cAAGE,GAAE,YAAYG,EAAC,GAAE;AAAC,kBAAMP,KAAE,IAAI,KAAKa,GAAEN,EAAC,CAAC;AAAE,YAAAM,GAAEN,EAAC,IAAE,EAAEP,IAAEa,GAAEN,EAAC,CAAC,IAAEP,GAAE,QAAQ,IAAE;AAAA,UAAI,WAASI,GAAE,eAAeG,EAAC,GAAE;AAAC,kBAAMP,KAAE,EAAEa,GAAEN,EAAC,CAAC;AAAE,kBAAMP,EAAC,IAAEa,GAAEN,EAAC,IAAE,OAAKM,GAAEN,EAAC,IAAEP;AAAA,UAAC;AAAA;AAAC,QAAAa,GAAEV,EAAC,IAAEQ,IAAEA,MAAIL,GAAE,KAAK,IAAI,EAAED,IAAEE,EAAC,CAAC,GAAEE,GAAE,KAAKI,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAG,CAAC,EAAE,EAAC,MAAK,KAAI,GAAER,EAAC,EAAE,KAAG,EAAEA,EAAC,EAAE,YAAUQ,MAAKP,GAAE,EAACO,GAAE,GAAEA,GAAE,CAAC,IAAE,EAAEA,GAAE,GAAEA,GAAE,GAAE,CAAC;AAAA,QAAO,CAAAP,KAAE,GAAEN,IAAEM,IAAE,EAAE,OAAMD,IAAE,MAAK,IAAI;AAAE,UAAMU,KAAE,CAAC;AAAE,aAAQF,KAAE,GAAEA,KAAEP,GAAE,QAAOO,MAAI;AAAC,YAAK,EAAC,GAAEN,IAAE,GAAEP,GAAC,IAAEM,GAAEO,EAAC,GAAEX,KAAEO,GAAEI,EAAC;AAAE,MAAAX,GAAEC,EAAC,IAAEU,KAAE,GAAEE,GAAE,KAAK,IAAIV,GAAE,IAAI,EAAE,CAAC,GAAE,CAACE,IAAEP,EAAC,CAAC,GAAEE,IAAE,MAAKA,GAAEC,EAAC,CAAC,CAAC;AAAA,IAAC;AAAC,WAAOY;AAAA,EAAC;AAAA,EAAC,sBAAsB,GAAE;AAAC,QAAG,QAAM,KAAG,OAAK,EAAE,QAAO;AAAK,QAAIf,KAAE,EAAE,CAAC;AAAE,YAAO,MAAMA,EAAC,KAAG,KAAK,IAAIA,EAAC,IAAE,SAAOA,KAAE,WAAW,CAAC,IAAGA;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiB,GAAE;AAAC,QAAG;AAAC,YAAMgB,GAAE,GAAE,CAAC;AAAA,IAAC,QAAM;AAAC,YAAM,IAAIX,GAAE,sCAAqC,0BAA0B;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEL,IAAE;AAJx6K;AAIy6K,QAAMG,KAAEH,GAAE,kBAAgB,CAAC,GAAEI,KAAE,EAAC,WAAUD,GAAE,WAAU,iBAAgB,MAAK,cAAa,EAAC,mBAAkBA,GAAE,eAAc,oBAAmBA,GAAE,eAAc,EAAC,GAAEE,KAAED,GAAE,kBAAgB,EAAC,MAAK,GAAEJ,GAAE,KAAI,CAAC,KAAG,OAAM,aAAY,GAAE,cAAa,qBAAoB,eAAc,MAAK,QAAO,CAAC,GAAE,UAASG,GAAE,UAAS,QAAO,EAAC,MAAK,OAAO,mBAAkB,MAAK,OAAO,mBAAkB,MAAK,OAAO,mBAAkB,MAAK,OAAO,mBAAkB,kBAAiBA,GAAE,oBAAkB,EAAC,MAAK,KAAI,EAAC,EAAC,GAAEU,KAAER,GAAE,CAAC,GAAEY,MAAE,KAAAJ,GAAE,KAAK,EAAE,UAAT,mBAAgB,QAAOK,MAAE,KAAAL,GAAE,KAAK,EAAE,UAAT,mBAAgB;AAAO,MAAG,CAACI,GAAE,OAAM,IAAIZ,GAAE,uBAAsB,gBAAe,EAAC,KAAI,EAAC,CAAC;AAAE,QAAK,EAAC,WAAUc,IAAE,cAAaV,GAAC,IAAES,GAAED,IAAEC,IAAEf,EAAC;AAAE,MAAG,CAACgB,GAAE,OAAM,IAAId,GAAE,+BAA8B,2CAA0C,EAAC,WAAUY,IAAE,YAAWC,IAAE,gBAAef,GAAC,CAAC;AAAE,MAAG,CAACM,GAAE,OAAM,IAAIJ,GAAE,uCAAsC,sEAAqE,EAAC,WAAUY,IAAE,YAAWC,IAAE,gBAAef,GAAC,CAAC;AAAE,EAAAC,GAAE,eAAaK,IAAEL,GAAE,YAAUe;AAAE,QAAK,EAAC,OAAMC,IAAE,SAAQC,GAAC,IAAEL,GAAEC,IAAEE,EAAC,GAAEH,KAAEK,GAAE,GAAEjB,GAAE,WAAUgB,IAAEC,IAAEjB,GAAE,YAAY;AAAE,OAAG,KAAAD,GAAE,WAAF,mBAAU,QAAO;AAAC,UAAMI,KAAE,IAAI,EAAEJ,GAAE,MAAM;AAAE,eAAUH,MAAKgB,IAAE;AAAC,YAAMd,KAAEK,GAAE,IAAIP,GAAE,IAAI;AAAE,MAAAE,MAAG,OAAO,OAAOF,IAAEE,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,MAAG,CAACc,GAAE,KAAM,CAAAT,OAAG,uBAAqBA,GAAE,SAAOF,GAAE,gBAAcE,GAAE,MAAK,KAAI,GAAE;AAAC,UAAMA,KAAE,EAAC,MAAK,cAAa,OAAM,cAAa,MAAK,oBAAmB,UAAS,OAAG,UAAS,MAAE;AAAE,IAAAF,GAAE,gBAAcE,GAAE,MAAKS,GAAE,QAAQT,EAAC;AAAA,EAAC;AAAC,EAAAF,GAAE,SAAOW;AAAE,QAAMN,KAAE,IAAI,EAAEL,GAAE,MAAM;AAAE,MAAGD,GAAE,iBAAeA,GAAE,aAAa,oBAAkBM,GAAE,IAAIN,GAAE,aAAa,iBAAiB,EAAE,MAAKA,GAAE,aAAa,qBAAmBM,GAAE,IAAIN,GAAE,aAAa,kBAAkB,EAAE,OAAMC,GAAE,UAAS;AAAC,UAAME,KAAEF,GAAE;AAAS,QAAGE,GAAE,gBAAe;AAAC,YAAMP,KAAEU,GAAE,IAAIH,GAAE,cAAc;AAAE,MAAAP,MAAGO,GAAE,iBAAeP,GAAE,MAAKA,GAAE,OAAK,uBAAqBO,GAAE,iBAAe;AAAA,IAAI;AAAC,QAAGA,GAAE,cAAa;AAAC,YAAMP,KAAEU,GAAE,IAAIH,GAAE,YAAY;AAAE,MAAAP,MAAGO,GAAE,eAAaP,GAAE,MAAKA,GAAE,OAAK,uBAAqBO,GAAE,eAAa;AAAA,IAAI;AAAC,QAAGA,GAAE,cAAa;AAAC,YAAMP,KAAEU,GAAE,IAAIH,GAAE,YAAY;AAAE,MAAAA,GAAE,eAAaP,KAAEA,GAAE,OAAK;AAAA,IAAI;AAAC,IAAAO,GAAE,kBAAgBA,GAAE,iBAAeF,GAAE,WAAS;AAAA,EAAK;AAAC,SAAOD;AAAC;", "names": ["i", "r", "o", "t", "n", "l", "s", "c", "e", "f", "a", "d", "m", "p", "g", "u", "y", "t", "k", "i", "n", "r", "s", "o", "e", "j", "d", "y", "h", "g", "a", "w", "E", "f", "l", "c", "m", "u", "p"]}