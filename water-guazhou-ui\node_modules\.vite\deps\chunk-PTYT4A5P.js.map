{"version": 3, "sources": ["../../@arcgis/core/geometry/support/clipRay.js", "../../@arcgis/core/geometry/support/frustum.js", "../../@arcgis/core/views/3d/webgl-engine/lib/Octree.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ObjectStack as r}from\"../../core/ObjectStack.js\";import{l as c,a as n,g as t}from\"../../chunks/vec3.js\";import{create as o,copy as e,fromValues as u}from\"./ray.js\";function a(r){return r?{ray:o(r.ray),c0:r.c0,c1:r.c1}:{ray:o(),c0:0,c1:Number.MAX_VALUE}}function i(r,c,n){const t=A.get();return t.ray=r,t.c0=c,t.c1=n,t}function s(r,c=a()){return f(r.ray,r.c0,r.c1,c)}function f(r,c,n,t=a()){return e(r,t.ray),t.c0=c,t.c1=n,t}function y(r,c=a()){return e(r,c.ray),c.c0=0,c.c1=Number.MAX_VALUE,c}function m(r,n,t=a()){const o=c(r.vector);return u(r.origin,n,t.ray),t.c0=0,t.c1=o,t}function p(r,c){return j(r,r.c0,c)}function g(r,c){return j(r,r.c1,c)}function j(r,c,o){return n(o,r.ray.origin,t(o,r.ray.direction,c))}const A=new r((()=>a()));export{s as copy,a as create,m as fromLineSegmentAndDirection,y as fromRay,f as fromValues,j as getAt,g as getEnd,p as getStart,i as wrap};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ObjectStack as T}from\"../../core/ObjectStack.js\";import{m as _,a as O}from\"../../chunks/mat4.js\";import{s as R}from\"../../chunks/vec3.js\";import{c as t}from\"../../chunks/vec3f64.js\";import{t as A}from\"../../chunks/vec4.js\";import{f as E}from\"../../chunks/vec4f64.js\";import{create as F,fromRay as r,fromLineSegmentAndDirection as n}from\"./clipRay.js\";import{create as o,copy as M,fromPoints as N,clipInfinite as c,signedDistance as f,clip as e}from\"./plane.js\";import{sm4d as B,sv4d as G}from\"./vectorStacks.js\";function H(T){return T?[o(T[0]),o(T[1]),o(T[2]),o(T[3]),o(T[4]),o(T[5])]:[o(),o(),o(),o(),o(),o()]}function I(){return[t(),t(),t(),t(),t(),t(),t(),t()]}function u(T,_){for(let O=0;O<v.NUM;O++)M(T[O],_[O])}function s(T,t,E,F=y){const r=_(B.get(),t,T);O(r,r);for(let _=0;_<h.NUM;++_){const T=A(G.get(),g[_],r);R(F[_],T[0]/T[3],T[1]/T[3],T[2]/T[3])}L(E,F)}function L(T,_){N(_[l.FAR_BOTTOM_LEFT],_[l.NEAR_BOTTOM_LEFT],_[l.NEAR_TOP_LEFT],T[U.LEFT]),N(_[l.NEAR_BOTTOM_RIGHT],_[l.FAR_BOTTOM_RIGHT],_[l.FAR_TOP_RIGHT],T[U.RIGHT]),N(_[l.FAR_BOTTOM_LEFT],_[l.FAR_BOTTOM_RIGHT],_[l.NEAR_BOTTOM_RIGHT],T[U.BOTTOM]),N(_[l.NEAR_TOP_LEFT],_[l.NEAR_TOP_RIGHT],_[l.FAR_TOP_RIGHT],T[U.TOP]),N(_[l.NEAR_BOTTOM_LEFT],_[l.NEAR_BOTTOM_RIGHT],_[l.NEAR_TOP_RIGHT],T[U.NEAR]),N(_[l.FAR_BOTTOM_RIGHT],_[l.FAR_BOTTOM_LEFT],_[l.FAR_TOP_LEFT],T[U.FAR])}function i(T,_){for(let O=0;O<v.NUM;O++){const R=T[O];if(R[0]*_[0]+R[1]*_[1]+R[2]*_[2]+R[3]>=_[3])return!1}return!0}function P(T,_){return j(T,r(_,b.get()))}function a(T,_){for(let O=0;O<v.NUM;O++){const R=T[O];if(!c(R,_))return!1}return!0}function m(T,_,O){return j(T,n(_,O,b.get()))}function p(T,_){for(let O=0;O<v.NUM;O++){if(f(T[O],_)>0)return!1}return!0}function j(T,_){for(let O=0;O<v.NUM;O++)if(!e(T[O],_))return!1;return!0}var U,l;!function(T){T[T.LEFT=0]=\"LEFT\",T[T.RIGHT=1]=\"RIGHT\",T[T.BOTTOM=2]=\"BOTTOM\",T[T.TOP=3]=\"TOP\",T[T.NEAR=4]=\"NEAR\",T[T.FAR=5]=\"FAR\"}(U||(U={})),function(T){T[T.NEAR_BOTTOM_LEFT=0]=\"NEAR_BOTTOM_LEFT\",T[T.NEAR_BOTTOM_RIGHT=1]=\"NEAR_BOTTOM_RIGHT\",T[T.NEAR_TOP_RIGHT=2]=\"NEAR_TOP_RIGHT\",T[T.NEAR_TOP_LEFT=3]=\"NEAR_TOP_LEFT\",T[T.FAR_BOTTOM_LEFT=4]=\"FAR_BOTTOM_LEFT\",T[T.FAR_BOTTOM_RIGHT=5]=\"FAR_BOTTOM_RIGHT\",T[T.FAR_TOP_RIGHT=6]=\"FAR_TOP_RIGHT\",T[T.FAR_TOP_LEFT=7]=\"FAR_TOP_LEFT\"}(l||(l={}));const k={bottom:[l.FAR_BOTTOM_RIGHT,l.NEAR_BOTTOM_RIGHT,l.NEAR_BOTTOM_LEFT,l.FAR_BOTTOM_LEFT],near:[l.NEAR_BOTTOM_LEFT,l.NEAR_BOTTOM_RIGHT,l.NEAR_TOP_RIGHT,l.NEAR_TOP_LEFT],far:[l.FAR_BOTTOM_RIGHT,l.FAR_BOTTOM_LEFT,l.FAR_TOP_LEFT,l.FAR_TOP_RIGHT],right:[l.NEAR_BOTTOM_RIGHT,l.FAR_BOTTOM_RIGHT,l.FAR_TOP_RIGHT,l.NEAR_TOP_RIGHT],left:[l.FAR_BOTTOM_LEFT,l.NEAR_BOTTOM_LEFT,l.NEAR_TOP_LEFT,l.FAR_TOP_LEFT],top:[l.FAR_TOP_LEFT,l.NEAR_TOP_LEFT,l.NEAR_TOP_RIGHT,l.FAR_TOP_RIGHT]};var v,h;!function(T){T[T.NUM=6]=\"NUM\"}(v||(v={})),function(T){T[T.NUM=8]=\"NUM\"}(h||(h={}));const g=[E(-1,-1,-1,1),E(1,-1,-1,1),E(1,1,-1,1),E(-1,1,-1,1),E(-1,-1,1,1),E(1,-1,1,1),E(1,1,1,1),E(-1,1,1,1)],b=new T(F),y=I();export{v as NumPlanes,h as NumPoints,U as PlaneIndex,l as PointIndex,L as computePlanes,u as copy,H as create,I as createPoints,s as fromMatrix,a as intersectClipRay,m as intersectsLineSegment,p as intersectsPoint,P as intersectsRay,i as intersectsSphere,k as planePointIndices};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../core/maybe.js\";import t from\"../../../../core/ObjectPool.js\";import n from\"../../../../core/PooledArray.js\";import{g as o,a as s,d as i,c as r,h}from\"../../../../chunks/vec3.js\";import{c as a,f as d}from\"../../../../chunks/vec3f64.js\";import{intersectsSphere as l}from\"../../../../geometry/support/frustum.js\";import{wrap as u}from\"../../../../geometry/support/ray.js\";import{h as c,a as f,g as _,j as m,c as p}from\"../../../../chunks/sphere.js\";import{rayBoxTest as b}from\"./Util.js\";class g{get bounds(){return this._root.bounds}get halfSize(){return this._root.halfSize}get root(){return this._root.node}get maximumObjectsPerNode(){return this._maximumObjectsPerNode}get maximumDepth(){return this._maximumDepth}get objectCount(){return this._objectCount}constructor(e,t){this._objectToBoundingSphere=e,this._maximumObjectsPerNode=10,this._maximumDepth=20,this._degenerateObjects=new Set,this._root=new S,this._objectCount=0,t&&(void 0!==t.maximumObjectsPerNode&&(this._maximumObjectsPerNode=t.maximumObjectsPerNode),void 0!==t.maximumDepth&&(this._maximumDepth=t.maximumDepth))}destroy(){this._degenerateObjects.clear(),S.clearPool(),R[0]=null,w.prune(),K.prune()}add(e,t=e.length){this._objectCount+=t,this._grow(e,t);const n=S.acquire();for(let o=0;o<t;o++){const t=e[o];this._isDegenerate(t)?this._degenerateObjects.add(t):(n.init(this._root),this._add(t,n))}S.release(n)}remove(t,n=null){this._objectCount-=t.length;const o=S.acquire();for(const s of t){const t=e(n)?n:c(this._objectToBoundingSphere(s),C);M(t[3])?(o.init(this._root),this._remove(s,t,o)):this._degenerateObjects.delete(s)}S.release(o),this._shrink()}update(e,t){if(!M(t[3])&&this._isDegenerate(e))return;const n=v(e);this.remove(n,t),this.add(n)}forEachAlongRay(e,t,n){const o=u(e,t);this._forEachNode(this._root,(e=>{if(!this._intersectsNode(o,e))return!1;const t=e.node;return t.terminals.forAll((e=>{this._intersectsObject(o,e)&&n(e)})),null!==t.residents&&t.residents.forAll((e=>{this._intersectsObject(o,e)&&n(e)})),!0}))}forEachAlongRayWithVerticalOffset(e,t,n,o){const s=u(e,t);this._forEachNode(this._root,(e=>{if(!this._intersectsNodeWithOffset(s,e,o))return!1;const t=e.node;return t.terminals.forAll((e=>{this._intersectsObjectWithOffset(s,e,o)&&n(e)})),null!==t.residents&&t.residents.forAll((e=>{this._intersectsObjectWithOffset(s,e,o)&&n(e)})),!0}))}forEach(e){this._forEachNode(this._root,(t=>{const n=t.node;return n.terminals.forAll(e),null!==n.residents&&n.residents.forAll(e),!0})),this._degenerateObjects.forEach(e)}forEachDegenerateObject(e){this._degenerateObjects.forEach(e)}findClosest(e,t,n,i=(()=>!0),r=1/0){let h=1/0,a=1/0,d=null;const u=z(e,t),c=o=>{if(--r,!i(o))return;const s=this._objectToBoundingSphere(o);if(!l(n,s))return;const u=E(e,t,_(s)),c=u-s[3],f=u+s[3];c<h&&(h=c,a=f,d=o)};return this._forEachNodeDepthOrdered(this._root,(i=>{if(r<=0||!l(n,i.bounds))return!1;o(y,u,i.halfSize),s(y,y,i.bounds);if(E(e,t,y)>a)return!1;const h=i.node;return h.terminals.forAll((e=>c(e))),null!==h.residents&&h.residents.forAll((e=>c(e))),!0}),e,t),d}forEachInDepthRange(e,t,n,i,r,h,a){let d=-1/0,u=1/0;const c={setRange:e=>{n===g.DepthOrder.FRONT_TO_BACK?(d=Math.max(d,e.near),u=Math.min(u,e.far)):(d=Math.max(d,-e.far),u=Math.min(u,-e.near))}};c.setRange(i);const f=E(t,n,e),m=z(t,n),p=z(t,-n),b=e=>{if(!a(e))return;const o=this._objectToBoundingSphere(e),s=_(o),i=E(t,n,s)-f,m=i-o[3],p=i+o[3];m>u||p<d||!l(h,o)||r(e,c)};this._forEachNodeDepthOrdered(this._root,(e=>{if(!l(h,e.bounds))return!1;o(y,m,e.halfSize),s(y,y,e.bounds);if(E(t,n,y)-f>u)return!1;o(y,p,e.halfSize),s(y,y,e.bounds);if(E(t,n,y)-f<d)return!1;const i=e.node;return i.terminals.forAll((e=>b(e))),null!==i.residents&&i.residents.forAll((e=>b(e))),!0}),t,n)}forEachNode(e){this._forEachNode(this._root,(t=>e(t.node,t.bounds,t.halfSize)))}forEachNeighbor(e,t){const n=f(t),o=_(t),s=t=>{const s=this._objectToBoundingSphere(t),r=f(s),h=n+r;return!(i(_(s),o)-h*h<=0)||e(t)};let r=!0;const h=e=>{r&&(r=s(e))};this._forEachNode(this._root,(e=>{const t=f(e.bounds),s=n+t;if(i(_(e.bounds),o)-s*s>0)return!1;const a=e.node;return a.terminals.forAll(h),r&&null!==a.residents&&a.residents.forAll(h),r})),r&&this.forEachDegenerateObject(h)}_intersectsNode(e,t){return x(t.bounds,2*-t.halfSize,k),x(t.bounds,2*t.halfSize,q),b(e.origin,e.direction,k,q)}_intersectsNodeWithOffset(e,t,n){return x(t.bounds,2*-t.halfSize,k),x(t.bounds,2*t.halfSize,q),n.applyToMinMax(k,q),b(e.origin,e.direction,k,q)}_intersectsObject(e,t){const n=this._objectToBoundingSphere(t);return!(n[3]>0)||m(n,e)}_intersectsObjectWithOffset(e,t,n){const o=this._objectToBoundingSphere(t);return!(o[3]>0)||m(n.applyToBoundingSphere(o),e)}_forEachNode(e,t){let n=S.acquire().init(e);const o=[n];for(;0!==o.length;){if(n=o.pop(),t(n)&&!n.isLeaf())for(let e=0;e<n.node.children.length;e++){n.node.children[e]&&o.push(S.acquire().init(n).advance(e))}S.release(n)}}_forEachNodeDepthOrdered(e,t,n,o=g.DepthOrder.FRONT_TO_BACK){let s=S.acquire().init(e);const i=[s];for(T(n,o,W);0!==i.length;){if(s=i.pop(),t(s)&&!s.isLeaf())for(let e=7;e>=0;--e){const t=W[e];s.node.children[t]&&i.push(S.acquire().init(s).advance(t))}S.release(s)}}_remove(e,t,n){w.clear();const o=n.advanceTo(t,((e,t)=>{w.push(e.node),w.push(t)}))?n.node.terminals:n.node.residents;if(o.removeUnordered(e),0===o.length)for(let s=w.length-2;s>=0;s-=2){const e=w.data[s],t=w.data[s+1];if(!this._purge(e,t))break}}_nodeIsEmpty(e){if(0!==e.terminals.length)return!1;if(null!==e.residents)return 0===e.residents.length;for(let t=0;t<e.children.length;t++)if(e.children[t])return!1;return!0}_purge(e,t){return t>=0&&(e.children[t]=null),!!this._nodeIsEmpty(e)&&(null===e.residents&&(e.residents=new n({shrink:!0})),!0)}_add(e,t){t.advanceTo(this._objectToBoundingSphere(e))?t.node.terminals.push(e):(t.node.residents.push(e),t.node.residents.length>this._maximumObjectsPerNode&&t.depth<this._maximumDepth&&this._split(t))}_split(e){const t=e.node.residents;e.node.residents=null;for(let n=0;n<t.length;n++){const o=S.acquire().init(e);this._add(t.getItemAt(n),o),S.release(o)}}_grow(e,t){if(0!==t&&(N(e,t,(e=>this._objectToBoundingSphere(e)),I),M(I[3])&&!this._fitsInsideTree(I)))if(this._nodeIsEmpty(this._root.node))c(I,this._root.bounds),this._root.halfSize=1.25*this._root.bounds[3],this._root.updateBoundsRadiusFromHalfSize();else{const e=this._rootBoundsForRootAsSubNode(I);this._placingRootViolatesMaxDepth(e)?this._rebuildTree(I,e):this._growRootAsSubNode(e),S.release(e)}}_rebuildTree(e,t){r(P,t.bounds),P[3]=t.halfSize,N([e,P],2,(e=>e),L);const n=S.acquire().init(this._root);this._root.initFrom(null,L,L[3]),this._root.increaseHalfSize(1.25),this._forEachNode(n,(e=>(this.add(e.node.terminals.data,e.node.terminals.length),null!==e.node.residents&&this.add(e.node.residents.data,e.node.residents.length),!0))),S.release(n)}_placingRootViolatesMaxDepth(e){const t=Math.log(e.halfSize/this._root.halfSize)*Math.LOG2E;let n=0;return this._forEachNode(this._root,(e=>(n=Math.max(n,e.depth),n+t<=this._maximumDepth))),n+t>this._maximumDepth}_rootBoundsForRootAsSubNode(e){const t=e[3],n=e;let o=-1/0;const s=this._root.bounds,i=this._root.halfSize;for(let h=0;h<3;h++){const e=s[h]-i-(n[h]-t),r=n[h]+t-(s[h]+i),a=Math.max(0,Math.ceil(e/(2*i))),d=Math.max(0,Math.ceil(r/(2*i)))+1,l=2**Math.ceil(Math.log(a+d)*Math.LOG2E);o=Math.max(o,l),H[h].min=a,H[h].max=d}for(let h=0;h<3;h++){let e=H[h].min,t=H[h].max;const n=(o-(e+t))/2;e+=Math.ceil(n),t+=Math.floor(n);const r=s[h]-i-e*i*2;F[h]=r+(t+e)*i}const r=o*i;return F[3]=r*B,S.acquire().initFrom(null,F,r,0)}_growRootAsSubNode(e){const t=this._root.node;r(I,this._root.bounds),I[3]=this._root.halfSize,this._root.init(e),e.advanceTo(I,null,!0),e.node.children=t.children,e.node.residents=t.residents,e.node.terminals=t.terminals}_shrink(){for(;;){const e=this._findShrinkIndex();if(-1===e)break;this._root.advance(e),this._root.depth=0}}_findShrinkIndex(){if(0!==this._root.node.terminals.length||this._root.isLeaf())return-1;let e=null;const t=this._root.node.children;let n=0,o=0;for(;o<t.length&&null==e;)n=o++,e=t[n];for(;o<t.length;)if(t[o++])return-1;return n}_isDegenerate(e){return!M(this._objectToBoundingSphere(e)[3])}_fitsInsideTree(e){const t=this._root.bounds,n=this._root.halfSize;return e[3]<=n&&e[0]>=t[0]-n&&e[0]<=t[0]+n&&e[1]>=t[1]-n&&e[1]<=t[1]+n&&e[2]>=t[2]-n&&e[2]<=t[2]+n}}class S{constructor(){this.bounds=p(),this.halfSize=0,this.initFrom(null,null,0,0)}init(e){return this.initFrom(e.node,e.bounds,e.halfSize,e.depth)}initFrom(t,n,o,s=this.depth){return this.node=e(t)?t:S.createEmptyNode(),e(n)&&c(n,this.bounds),this.halfSize=o,this.depth=s,this}increaseHalfSize(e){this.halfSize*=e,this.updateBoundsRadiusFromHalfSize()}updateBoundsRadiusFromHalfSize(){this.bounds[3]=this.halfSize*B}advance(e){let t=this.node.children[e];t||(t=S.createEmptyNode(),this.node.children[e]=t),this.node=t,this.halfSize/=2,this.depth++;const n=A[e];return this.bounds[0]+=n[0]*this.halfSize,this.bounds[1]+=n[1]*this.halfSize,this.bounds[2]+=n[2]*this.halfSize,this.updateBoundsRadiusFromHalfSize(),this}advanceTo(e,t,n=!1){for(;;){if(this.isTerminalFor(e))return t&&t(this,-1),!0;if(this.isLeaf()){if(!n)return t&&t(this,-1),!1;this.node.residents=null}const o=this._childIndex(e);t&&t(this,o),this.advance(o)}}isLeaf(){return null!=this.node.residents}isTerminalFor(e){return e[3]>this.halfSize/2}_childIndex(e){const t=this.bounds;return(t[0]<e[0]?1:0)+(t[1]<e[1]?2:0)+(t[2]<e[2]?4:0)}static createEmptyNode(){return{children:[null,null,null,null,null,null,null,null],terminals:new n({shrink:!0}),residents:new n({shrink:!0})}}static acquire(){return S._pool.acquire()}static release(e){S._pool.release(e)}static clearPool(){S._pool.prune()}}function O(e,t){e[0]=Math.min(e[0],t[0]-t[3]),e[1]=Math.min(e[1],t[1]-t[3]),e[2]=Math.min(e[2],t[2]-t[3])}function j(e,t){e[0]=Math.max(e[0],t[0]+t[3]),e[1]=Math.max(e[1],t[1]+t[3]),e[2]=Math.max(e[2],t[2]+t[3])}function x(e,t,n){n[0]=e[0]+t,n[1]=e[1]+t,n[2]=e[2]+t}function N(e,t,n,o){if(1===t){const t=n(e[0]);c(t,o)}else{k[0]=1/0,k[1]=1/0,k[2]=1/0,q[0]=-1/0,q[1]=-1/0,q[2]=-1/0;for(let o=0;o<t;o++){const t=n(e[o]);M(t[3])&&(O(k,t),j(q,t))}h(o,k,q,.5),o[3]=Math.max(q[0]-k[0],q[1]-k[1],q[2]-k[2])/2}}function T(e,t,n){if(!K.length)for(let o=0;o<8;++o)K.push({index:0,distance:0});for(let o=0;o<8;++o){const n=A[o];K.data[o].index=o,K.data[o].distance=E(e,t,n)}K.sort(((e,t)=>e.distance-t.distance));for(let o=0;o<8;++o)n[o]=K.data[o].index}function z(e,t){let n,o=1/0;for(let s=0;s<8;++s){const i=E(e,t,D[s]);i<o&&(o=i,n=D[s])}return n}function E(e,t,n){return t*(e[0]*n[0]+e[1]*n[1]+e[2]*n[2])}function M(e){return!isNaN(e)&&e!==-1/0&&e!==1/0&&e>0}S._pool=new t(S),function(e){var t;(t=e.DepthOrder||(e.DepthOrder={}))[t.FRONT_TO_BACK=1]=\"FRONT_TO_BACK\",t[t.BACK_TO_FRONT=-1]=\"BACK_TO_FRONT\"}(g||(g={}));const A=[d(-1,-1,-1),d(1,-1,-1),d(-1,1,-1),d(1,1,-1),d(-1,-1,1),d(1,-1,1),d(-1,1,1),d(1,1,1)],D=[d(-1,-1,-1),d(-1,-1,1),d(-1,1,-1),d(-1,1,1),d(1,-1,-1),d(1,-1,1),d(1,1,-1),d(1,1,1)],B=Math.sqrt(3),R=[null];function v(e){return R[0]=e,R}const F=p(),y=a(),k=a(),q=a(),w=new n,C=p(),I=p(),P=p(),L=p(),H=[{min:0,max:0},{min:0,max:0},{min:0,max:0}],K=new n,W=[0,0,0,0,0,0,0,0],V=g;export{V as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2K,SAAS,EAAEA,IAAE;AAAC,SAAOA,KAAE,EAAC,KAAI,EAAEA,GAAE,GAAG,GAAE,IAAGA,GAAE,IAAG,IAAGA,GAAE,GAAE,IAAE,EAAC,KAAI,EAAE,GAAE,IAAG,GAAE,IAAG,OAAO,UAAS;AAAC;AAA4K,SAAS,EAAEC,IAAEC,KAAE,EAAE,GAAE;AAAC,SAAO,EAAED,IAAEC,GAAE,GAAG,GAAEA,GAAE,KAAG,GAAEA,GAAE,KAAG,OAAO,WAAUA;AAAC;AAAsF,SAASC,GAAEC,IAAEC,IAAE;AAAC,SAAOC,GAAEF,IAAEA,GAAE,IAAGC,EAAC;AAAC;AAAC,SAASE,GAAEH,IAAEC,IAAE;AAAC,SAAOC,GAAEF,IAAEA,GAAE,IAAGC,EAAC;AAAC;AAAC,SAASC,GAAEF,IAAEC,IAAEG,IAAE;AAAC,SAAO,EAAEA,IAAEJ,GAAE,IAAI,QAAO,EAAEI,IAAEJ,GAAE,IAAI,WAAUC,EAAC,CAAC;AAAC;AAAC,IAAMI,KAAE,IAAI,EAAG,MAAI,EAAE,CAAE;;;ACAjO,SAASC,GAAEC,IAAE;AAAC,SAAOA,KAAE,CAACC,GAAED,GAAE,CAAC,CAAC,GAAEC,GAAED,GAAE,CAAC,CAAC,GAAEC,GAAED,GAAE,CAAC,CAAC,GAAEC,GAAED,GAAE,CAAC,CAAC,GAAEC,GAAED,GAAE,CAAC,CAAC,GAAEC,GAAED,GAAE,CAAC,CAAC,CAAC,IAAE,CAACC,GAAE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,CAAC,EAAE,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC;AAAC;AAAC,SAASC,GAAEF,IAAEG,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAEC,GAAE,KAAID,KAAI,CAAAE,GAAEN,GAAEI,EAAC,GAAED,GAAEC,EAAC,CAAC;AAAC;AAAC,SAASG,GAAEP,IAAE,GAAEQ,IAAEC,KAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,EAAE,IAAI,GAAE,GAAEX,EAAC;AAAE,IAAEW,IAAEA,EAAC;AAAE,WAAQR,KAAE,GAAEA,KAAES,GAAE,KAAI,EAAET,IAAE;AAAC,UAAMH,KAAE,EAAEW,GAAE,IAAI,GAAEE,GAAEV,EAAC,GAAEQ,EAAC;AAAE,MAAEF,GAAEN,EAAC,GAAEH,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAA,EAAC;AAAC,IAAEQ,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAET,IAAEG,IAAE;AAAC,IAAEA,GAAEW,GAAE,eAAe,GAAEX,GAAEW,GAAE,gBAAgB,GAAEX,GAAEW,GAAE,aAAa,GAAEd,GAAE,EAAE,IAAI,CAAC,GAAE,EAAEG,GAAEW,GAAE,iBAAiB,GAAEX,GAAEW,GAAE,gBAAgB,GAAEX,GAAEW,GAAE,aAAa,GAAEd,GAAE,EAAE,KAAK,CAAC,GAAE,EAAEG,GAAEW,GAAE,eAAe,GAAEX,GAAEW,GAAE,gBAAgB,GAAEX,GAAEW,GAAE,iBAAiB,GAAEd,GAAE,EAAE,MAAM,CAAC,GAAE,EAAEG,GAAEW,GAAE,aAAa,GAAEX,GAAEW,GAAE,cAAc,GAAEX,GAAEW,GAAE,aAAa,GAAEd,GAAE,EAAE,GAAG,CAAC,GAAE,EAAEG,GAAEW,GAAE,gBAAgB,GAAEX,GAAEW,GAAE,iBAAiB,GAAEX,GAAEW,GAAE,cAAc,GAAEd,GAAE,EAAE,IAAI,CAAC,GAAE,EAAEG,GAAEW,GAAE,gBAAgB,GAAEX,GAAEW,GAAE,eAAe,GAAEX,GAAEW,GAAE,YAAY,GAAEd,GAAE,EAAE,GAAG,CAAC;AAAC;AAAC,SAASe,GAAEf,IAAEG,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAEC,GAAE,KAAID,MAAI;AAAC,UAAMY,KAAEhB,GAAEI,EAAC;AAAE,QAAGY,GAAE,CAAC,IAAEb,GAAE,CAAC,IAAEa,GAAE,CAAC,IAAEb,GAAE,CAAC,IAAEa,GAAE,CAAC,IAAEb,GAAE,CAAC,IAAEa,GAAE,CAAC,KAAGb,GAAE,CAAC,EAAE,QAAM;AAAA,EAAE;AAAC,SAAM;AAAE;AAA0C,SAASc,GAAEC,IAAEC,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAEC,GAAE,KAAID,MAAI;AAAC,UAAME,KAAEJ,GAAEE,EAAC;AAAE,QAAG,CAAC,EAAEE,IAAEH,EAAC,EAAE,QAAM;AAAA,EAAE;AAAC,SAAM;AAAE;AAAgM,IAAI;AAAJ,IAAMI;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,MAAI,CAAC,IAAE,OAAMA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,MAAI,CAAC,IAAE;AAAK,EAAE,MAAI,IAAE,CAAC,EAAE,GAAE,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,oBAAkB,CAAC,IAAE,qBAAoBA,GAAEA,GAAE,iBAAe,CAAC,IAAE,kBAAiBA,GAAEA,GAAE,gBAAc,CAAC,IAAE,iBAAgBA,GAAEA,GAAE,kBAAgB,CAAC,IAAE,mBAAkBA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,gBAAc,CAAC,IAAE,iBAAgBA,GAAEA,GAAE,eAAa,CAAC,IAAE;AAAc,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAME,KAAE,EAAC,QAAO,CAACF,GAAE,kBAAiBA,GAAE,mBAAkBA,GAAE,kBAAiBA,GAAE,eAAe,GAAE,MAAK,CAACA,GAAE,kBAAiBA,GAAE,mBAAkBA,GAAE,gBAAeA,GAAE,aAAa,GAAE,KAAI,CAACA,GAAE,kBAAiBA,GAAE,iBAAgBA,GAAE,cAAaA,GAAE,aAAa,GAAE,OAAM,CAACA,GAAE,mBAAkBA,GAAE,kBAAiBA,GAAE,eAAcA,GAAE,cAAc,GAAE,MAAK,CAACA,GAAE,iBAAgBA,GAAE,kBAAiBA,GAAE,eAAcA,GAAE,YAAY,GAAE,KAAI,CAACA,GAAE,cAAaA,GAAE,eAAcA,GAAE,gBAAeA,GAAE,aAAa,EAAC;AAAE,IAAIG;AAAJ,IAAMC;AAAE,CAAC,SAASH,IAAE;AAAC,EAAAA,GAAEA,GAAE,MAAI,CAAC,IAAE;AAAK,EAAEE,OAAIA,KAAE,CAAC,EAAE,GAAE,SAASF,IAAE;AAAC,EAAAA,GAAEA,GAAE,MAAI,CAAC,IAAE;AAAK,EAAEG,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAMC,KAAE,CAACC,GAAE,IAAG,IAAG,IAAG,CAAC,GAAEA,GAAE,GAAE,IAAG,IAAG,CAAC,GAAEA,GAAE,GAAE,GAAE,IAAG,CAAC,GAAEA,GAAE,IAAG,GAAE,IAAG,CAAC,GAAEA,GAAE,IAAG,IAAG,GAAE,CAAC,GAAEA,GAAE,GAAE,IAAG,GAAE,CAAC,GAAEA,GAAE,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,IAAG,GAAE,GAAE,CAAC,CAAC;AAA5G,IAA8G,IAAE,IAAI,EAAE,CAAC;AAAvH,IAAyHC,KAAE,EAAE;;;ACAn4E,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,MAAM;AAAA,EAAM;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,MAAM;AAAA,EAAQ;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,MAAM;AAAA,EAAI;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAO,KAAK;AAAA,EAAsB;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,YAAYC,IAAE,GAAE;AAAC,SAAK,0BAAwBA,IAAE,KAAK,yBAAuB,IAAG,KAAK,gBAAc,IAAG,KAAK,qBAAmB,oBAAI,OAAI,KAAK,QAAM,IAAI,KAAE,KAAK,eAAa,GAAE,MAAI,WAAS,EAAE,0BAAwB,KAAK,yBAAuB,EAAE,wBAAuB,WAAS,EAAE,iBAAe,KAAK,gBAAc,EAAE;AAAA,EAAc;AAAA,EAAC,UAAS;AAAC,SAAK,mBAAmB,MAAM,GAAE,EAAE,UAAU,GAAEC,GAAE,CAAC,IAAE,MAAKC,GAAE,MAAM,GAAE,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,IAAIF,IAAE,IAAEA,GAAE,QAAO;AAAC,SAAK,gBAAc,GAAE,KAAK,MAAMA,IAAE,CAAC;AAAE,UAAMG,KAAE,EAAE,QAAQ;AAAE,aAAQC,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,KAAEL,GAAEI,EAAC;AAAE,WAAK,cAAcC,EAAC,IAAE,KAAK,mBAAmB,IAAIA,EAAC,KAAGF,GAAE,KAAK,KAAK,KAAK,GAAE,KAAK,KAAKE,IAAEF,EAAC;AAAA,IAAE;AAAC,MAAE,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,GAAEA,KAAE,MAAK;AAAC,SAAK,gBAAc,EAAE;AAAO,UAAMC,KAAE,EAAE,QAAQ;AAAE,eAAUE,MAAK,GAAE;AAAC,YAAMD,KAAE,EAAEF,EAAC,IAAEA,KAAE,EAAE,KAAK,wBAAwBG,EAAC,GAAE,CAAC;AAAE,QAAED,GAAE,CAAC,CAAC,KAAGD,GAAE,KAAK,KAAK,KAAK,GAAE,KAAK,QAAQE,IAAED,IAAED,EAAC,KAAG,KAAK,mBAAmB,OAAOE,EAAC;AAAA,IAAC;AAAC,MAAE,QAAQF,EAAC,GAAE,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAOJ,IAAE,GAAE;AAAC,QAAG,CAAC,EAAE,EAAE,CAAC,CAAC,KAAG,KAAK,cAAcA,EAAC,EAAE;AAAO,UAAMG,KAAEI,GAAEP,EAAC;AAAE,SAAK,OAAOG,IAAE,CAAC,GAAE,KAAK,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBH,IAAE,GAAEG,IAAE;AAAC,UAAMC,KAAEI,GAAER,IAAE,CAAC;AAAE,SAAK,aAAa,KAAK,OAAO,CAAAA,OAAG;AAAC,UAAG,CAAC,KAAK,gBAAgBI,IAAEJ,EAAC,EAAE,QAAM;AAAG,YAAMK,KAAEL,GAAE;AAAK,aAAOK,GAAE,UAAU,OAAQ,CAAAL,OAAG;AAAC,aAAK,kBAAkBI,IAAEJ,EAAC,KAAGG,GAAEH,EAAC;AAAA,MAAC,CAAE,GAAE,SAAOK,GAAE,aAAWA,GAAE,UAAU,OAAQ,CAAAL,OAAG;AAAC,aAAK,kBAAkBI,IAAEJ,EAAC,KAAGG,GAAEH,EAAC;AAAA,MAAC,CAAE,GAAE;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,kCAAkCA,IAAE,GAAEG,IAAEC,IAAE;AAAC,UAAME,KAAEE,GAAER,IAAE,CAAC;AAAE,SAAK,aAAa,KAAK,OAAO,CAAAA,OAAG;AAAC,UAAG,CAAC,KAAK,0BAA0BM,IAAEN,IAAEI,EAAC,EAAE,QAAM;AAAG,YAAMC,KAAEL,GAAE;AAAK,aAAOK,GAAE,UAAU,OAAQ,CAAAL,OAAG;AAAC,aAAK,4BAA4BM,IAAEN,IAAEI,EAAC,KAAGD,GAAEH,EAAC;AAAA,MAAC,CAAE,GAAE,SAAOK,GAAE,aAAWA,GAAE,UAAU,OAAQ,CAAAL,OAAG;AAAC,aAAK,4BAA4BM,IAAEN,IAAEI,EAAC,KAAGD,GAAEH,EAAC;AAAA,MAAC,CAAE,GAAE;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,aAAa,KAAK,OAAO,OAAG;AAAC,YAAMG,KAAE,EAAE;AAAK,aAAOA,GAAE,UAAU,OAAOH,EAAC,GAAE,SAAOG,GAAE,aAAWA,GAAE,UAAU,OAAOH,EAAC,GAAE;AAAA,IAAE,CAAE,GAAE,KAAK,mBAAmB,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAE;AAAC,SAAK,mBAAmB,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE,GAAEG,IAAEM,KAAG,MAAI,MAAIC,KAAE,IAAE,GAAE;AAAC,QAAIC,KAAE,IAAE,GAAEC,KAAE,IAAE,GAAEC,KAAE;AAAK,UAAMC,KAAE,EAAEd,IAAE,CAAC,GAAEe,KAAE,CAAAX,OAAG;AAAC,UAAG,EAAEM,IAAE,CAACD,GAAEL,EAAC,EAAE;AAAO,YAAME,KAAE,KAAK,wBAAwBF,EAAC;AAAE,UAAG,CAACK,GAAEN,IAAEG,EAAC,EAAE;AAAO,YAAMQ,KAAE,EAAEd,IAAE,GAAEgB,GAAEV,EAAC,CAAC,GAAES,KAAED,KAAER,GAAE,CAAC,GAAEW,KAAEH,KAAER,GAAE,CAAC;AAAE,MAAAS,KAAEJ,OAAIA,KAAEI,IAAEH,KAAEK,IAAEJ,KAAET;AAAA,IAAE;AAAE,WAAO,KAAK,yBAAyB,KAAK,OAAO,CAAAK,OAAG;AAAC,UAAGC,MAAG,KAAG,CAACD,GAAEN,IAAEM,GAAE,MAAM,EAAE,QAAM;AAAG,QAAES,IAAEJ,IAAEL,GAAE,QAAQ,GAAE,EAAES,IAAEA,IAAET,GAAE,MAAM;AAAE,UAAG,EAAET,IAAE,GAAEkB,EAAC,IAAEN,GAAE,QAAM;AAAG,YAAMD,KAAEF,GAAE;AAAK,aAAOE,GAAE,UAAU,OAAQ,CAAAX,OAAGe,GAAEf,EAAC,CAAE,GAAE,SAAOW,GAAE,aAAWA,GAAE,UAAU,OAAQ,CAAAX,OAAGe,GAAEf,EAAC,CAAE,GAAE;AAAA,IAAE,GAAGA,IAAE,CAAC,GAAEa;AAAA,EAAC;AAAA,EAAC,oBAAoBb,IAAE,GAAEG,IAAEM,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,KAAG,GAAEC,KAAE,IAAE;AAAE,UAAMC,KAAE,EAAC,UAAS,CAAAf,OAAG;AAAC,MAAAG,OAAI,GAAE,WAAW,iBAAeU,KAAE,KAAK,IAAIA,IAAEb,GAAE,IAAI,GAAEc,KAAE,KAAK,IAAIA,IAAEd,GAAE,GAAG,MAAIa,KAAE,KAAK,IAAIA,IAAE,CAACb,GAAE,GAAG,GAAEc,KAAE,KAAK,IAAIA,IAAE,CAACd,GAAE,IAAI;AAAA,IAAE,EAAC;AAAE,IAAAe,GAAE,SAASN,EAAC;AAAE,UAAMQ,KAAE,EAAE,GAAEd,IAAEH,EAAC,GAAEmB,KAAE,EAAE,GAAEhB,EAAC,GAAEK,KAAE,EAAE,GAAE,CAACL,EAAC,GAAEiB,KAAE,CAAApB,OAAG;AAAC,UAAG,CAACY,GAAEZ,EAAC,EAAE;AAAO,YAAMI,KAAE,KAAK,wBAAwBJ,EAAC,GAAEM,KAAEU,GAAEZ,EAAC,GAAEK,KAAE,EAAE,GAAEN,IAAEG,EAAC,IAAEW,IAAEE,KAAEV,KAAEL,GAAE,CAAC,GAAEI,KAAEC,KAAEL,GAAE,CAAC;AAAE,MAAAe,KAAEL,MAAGN,KAAEK,MAAG,CAACJ,GAAEE,IAAEP,EAAC,KAAGM,GAAEV,IAAEe,EAAC;AAAA,IAAC;AAAE,SAAK,yBAAyB,KAAK,OAAO,CAAAf,OAAG;AAAC,UAAG,CAACS,GAAEE,IAAEX,GAAE,MAAM,EAAE,QAAM;AAAG,QAAEkB,IAAEC,IAAEnB,GAAE,QAAQ,GAAE,EAAEkB,IAAEA,IAAElB,GAAE,MAAM;AAAE,UAAG,EAAE,GAAEG,IAAEe,EAAC,IAAED,KAAEH,GAAE,QAAM;AAAG,QAAEI,IAAEV,IAAER,GAAE,QAAQ,GAAE,EAAEkB,IAAEA,IAAElB,GAAE,MAAM;AAAE,UAAG,EAAE,GAAEG,IAAEe,EAAC,IAAED,KAAEJ,GAAE,QAAM;AAAG,YAAMJ,KAAET,GAAE;AAAK,aAAOS,GAAE,UAAU,OAAQ,CAAAT,OAAGoB,GAAEpB,EAAC,CAAE,GAAE,SAAOS,GAAE,aAAWA,GAAE,UAAU,OAAQ,CAAAT,OAAGoB,GAAEpB,EAAC,CAAE,GAAE;AAAA,IAAE,GAAG,GAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAE;AAAC,SAAK,aAAa,KAAK,OAAO,OAAGA,GAAE,EAAE,MAAK,EAAE,QAAO,EAAE,QAAQ,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE,GAAE;AAAC,UAAMG,KAAE,EAAE,CAAC,GAAEC,KAAEY,GAAE,CAAC,GAAEV,KAAE,CAAAD,OAAG;AAAC,YAAMC,KAAE,KAAK,wBAAwBD,EAAC,GAAEK,KAAE,EAAEJ,EAAC,GAAEK,KAAER,KAAEO;AAAE,aAAM,EAAE,EAAEM,GAAEV,EAAC,GAAEF,EAAC,IAAEO,KAAEA,MAAG,MAAIX,GAAEK,EAAC;AAAA,IAAC;AAAE,QAAIK,KAAE;AAAG,UAAMC,KAAE,CAAAX,OAAG;AAAC,MAAAU,OAAIA,KAAEJ,GAAEN,EAAC;AAAA,IAAE;AAAE,SAAK,aAAa,KAAK,OAAO,CAAAA,OAAG;AAAC,YAAMK,KAAE,EAAEL,GAAE,MAAM,GAAEM,KAAEH,KAAEE;AAAE,UAAG,EAAEW,GAAEhB,GAAE,MAAM,GAAEI,EAAC,IAAEE,KAAEA,KAAE,EAAE,QAAM;AAAG,YAAMM,KAAEZ,GAAE;AAAK,aAAOY,GAAE,UAAU,OAAOD,EAAC,GAAED,MAAG,SAAOE,GAAE,aAAWA,GAAE,UAAU,OAAOD,EAAC,GAAED;AAAA,IAAC,CAAE,GAAEA,MAAG,KAAK,wBAAwBC,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBX,IAAE,GAAE;AAAC,WAAO,EAAE,EAAE,QAAO,IAAE,CAAC,EAAE,UAASgB,EAAC,GAAE,EAAE,EAAE,QAAO,IAAE,EAAE,UAAS,CAAC,GAAE,EAAEhB,GAAE,QAAOA,GAAE,WAAUgB,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BhB,IAAE,GAAEG,IAAE;AAAC,WAAO,EAAE,EAAE,QAAO,IAAE,CAAC,EAAE,UAASa,EAAC,GAAE,EAAE,EAAE,QAAO,IAAE,EAAE,UAAS,CAAC,GAAEb,GAAE,cAAca,IAAE,CAAC,GAAE,EAAEhB,GAAE,QAAOA,GAAE,WAAUgB,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBhB,IAAE,GAAE;AAAC,UAAMG,KAAE,KAAK,wBAAwB,CAAC;AAAE,WAAM,EAAEA,GAAE,CAAC,IAAE,MAAI,EAAEA,IAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,4BAA4BA,IAAE,GAAEG,IAAE;AAAC,UAAMC,KAAE,KAAK,wBAAwB,CAAC;AAAE,WAAM,EAAEA,GAAE,CAAC,IAAE,MAAI,EAAED,GAAE,sBAAsBC,EAAC,GAAEJ,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE,GAAE;AAAC,QAAIG,KAAE,EAAE,QAAQ,EAAE,KAAKH,EAAC;AAAE,UAAMI,KAAE,CAACD,EAAC;AAAE,WAAK,MAAIC,GAAE,UAAQ;AAAC,UAAGD,KAAEC,GAAE,IAAI,GAAE,EAAED,EAAC,KAAG,CAACA,GAAE,OAAO,EAAE,UAAQH,KAAE,GAAEA,KAAEG,GAAE,KAAK,SAAS,QAAOH,MAAI;AAAC,QAAAG,GAAE,KAAK,SAASH,EAAC,KAAGI,GAAE,KAAK,EAAE,QAAQ,EAAE,KAAKD,EAAC,EAAE,QAAQH,EAAC,CAAC;AAAA,MAAC;AAAC,QAAE,QAAQG,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBH,IAAE,GAAEG,IAAEC,KAAE,GAAE,WAAW,eAAc;AAAC,QAAIE,KAAE,EAAE,QAAQ,EAAE,KAAKN,EAAC;AAAE,UAAMS,KAAE,CAACH,EAAC;AAAE,SAAIe,GAAElB,IAAEC,IAAE,CAAC,GAAE,MAAIK,GAAE,UAAQ;AAAC,UAAGH,KAAEG,GAAE,IAAI,GAAE,EAAEH,EAAC,KAAG,CAACA,GAAE,OAAO,EAAE,UAAQN,KAAE,GAAEA,MAAG,GAAE,EAAEA,IAAE;AAAC,cAAMK,KAAE,EAAEL,EAAC;AAAE,QAAAM,GAAE,KAAK,SAASD,EAAC,KAAGI,GAAE,KAAK,EAAE,QAAQ,EAAE,KAAKH,EAAC,EAAE,QAAQD,EAAC,CAAC;AAAA,MAAC;AAAC,QAAE,QAAQC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAQN,IAAE,GAAEG,IAAE;AAAC,IAAAD,GAAE,MAAM;AAAE,UAAME,KAAED,GAAE,UAAU,GAAG,CAACH,IAAEK,OAAI;AAAC,MAAAH,GAAE,KAAKF,GAAE,IAAI,GAAEE,GAAE,KAAKG,EAAC;AAAA,IAAC,CAAE,IAAEF,GAAE,KAAK,YAAUA,GAAE,KAAK;AAAU,QAAGC,GAAE,gBAAgBJ,EAAC,GAAE,MAAII,GAAE,OAAO,UAAQE,KAAEJ,GAAE,SAAO,GAAEI,MAAG,GAAEA,MAAG,GAAE;AAAC,YAAMN,KAAEE,GAAE,KAAKI,EAAC,GAAED,KAAEH,GAAE,KAAKI,KAAE,CAAC;AAAE,UAAG,CAAC,KAAK,OAAON,IAAEK,EAAC,EAAE;AAAA,IAAK;AAAA,EAAC;AAAA,EAAC,aAAaL,IAAE;AAAC,QAAG,MAAIA,GAAE,UAAU,OAAO,QAAM;AAAG,QAAG,SAAOA,GAAE,UAAU,QAAO,MAAIA,GAAE,UAAU;AAAO,aAAQ,IAAE,GAAE,IAAEA,GAAE,SAAS,QAAO,IAAI,KAAGA,GAAE,SAAS,CAAC,EAAE,QAAM;AAAG,WAAM;AAAA,EAAE;AAAA,EAAC,OAAOA,IAAE,GAAE;AAAC,WAAO,KAAG,MAAIA,GAAE,SAAS,CAAC,IAAE,OAAM,CAAC,CAAC,KAAK,aAAaA,EAAC,MAAI,SAAOA,GAAE,cAAYA,GAAE,YAAU,IAAI,EAAE,EAAC,QAAO,KAAE,CAAC,IAAG;AAAA,EAAG;AAAA,EAAC,KAAKA,IAAE,GAAE;AAAC,MAAE,UAAU,KAAK,wBAAwBA,EAAC,CAAC,IAAE,EAAE,KAAK,UAAU,KAAKA,EAAC,KAAG,EAAE,KAAK,UAAU,KAAKA,EAAC,GAAE,EAAE,KAAK,UAAU,SAAO,KAAK,0BAAwB,EAAE,QAAM,KAAK,iBAAe,KAAK,OAAO,CAAC;AAAA,EAAE;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAM,IAAEA,GAAE,KAAK;AAAU,IAAAA,GAAE,KAAK,YAAU;AAAK,aAAQG,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,YAAMC,KAAE,EAAE,QAAQ,EAAE,KAAKJ,EAAC;AAAE,WAAK,KAAK,EAAE,UAAUG,EAAC,GAAEC,EAAC,GAAE,EAAE,QAAQA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAMJ,IAAE,GAAE;AAAC,QAAG,MAAI,MAAI,EAAEA,IAAE,GAAG,CAAAA,OAAG,KAAK,wBAAwBA,EAAC,GAAGsB,EAAC,GAAE,EAAEA,GAAE,CAAC,CAAC,KAAG,CAAC,KAAK,gBAAgBA,EAAC,GAAG,KAAG,KAAK,aAAa,KAAK,MAAM,IAAI,EAAE,GAAEA,IAAE,KAAK,MAAM,MAAM,GAAE,KAAK,MAAM,WAAS,OAAK,KAAK,MAAM,OAAO,CAAC,GAAE,KAAK,MAAM,+BAA+B;AAAA,SAAM;AAAC,YAAMtB,KAAE,KAAK,4BAA4BsB,EAAC;AAAE,WAAK,6BAA6BtB,EAAC,IAAE,KAAK,aAAasB,IAAEtB,EAAC,IAAE,KAAK,mBAAmBA,EAAC,GAAE,EAAE,QAAQA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE,GAAE;AAAC,IAAAU,GAAE,GAAE,EAAE,MAAM,GAAE,EAAE,CAAC,IAAE,EAAE,UAAS,EAAE,CAACV,IAAE,CAAC,GAAE,GAAG,CAAAA,OAAGA,IAAGuB,EAAC;AAAE,UAAMpB,KAAE,EAAE,QAAQ,EAAE,KAAK,KAAK,KAAK;AAAE,SAAK,MAAM,SAAS,MAAKoB,IAAEA,GAAE,CAAC,CAAC,GAAE,KAAK,MAAM,iBAAiB,IAAI,GAAE,KAAK,aAAapB,IAAG,CAAAH,QAAI,KAAK,IAAIA,GAAE,KAAK,UAAU,MAAKA,GAAE,KAAK,UAAU,MAAM,GAAE,SAAOA,GAAE,KAAK,aAAW,KAAK,IAAIA,GAAE,KAAK,UAAU,MAAKA,GAAE,KAAK,UAAU,MAAM,GAAE,KAAI,GAAE,EAAE,QAAQG,EAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BH,IAAE;AAAC,UAAM,IAAE,KAAK,IAAIA,GAAE,WAAS,KAAK,MAAM,QAAQ,IAAE,KAAK;AAAM,QAAIG,KAAE;AAAE,WAAO,KAAK,aAAa,KAAK,OAAO,CAAAH,QAAIG,KAAE,KAAK,IAAIA,IAAEH,GAAE,KAAK,GAAEG,KAAE,KAAG,KAAK,cAAe,GAAEA,KAAE,IAAE,KAAK;AAAA,EAAa;AAAA,EAAC,4BAA4BH,IAAE;AAAC,UAAM,IAAEA,GAAE,CAAC,GAAEG,KAAEH;AAAE,QAAII,KAAE,KAAG;AAAE,UAAME,KAAE,KAAK,MAAM,QAAOG,KAAE,KAAK,MAAM;AAAS,aAAQE,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMX,KAAEM,GAAEK,EAAC,IAAEF,MAAGN,GAAEQ,EAAC,IAAE,IAAGD,KAAEP,GAAEQ,EAAC,IAAE,KAAGL,GAAEK,EAAC,IAAEF,KAAGG,KAAE,KAAK,IAAI,GAAE,KAAK,KAAKZ,MAAG,IAAES,GAAE,CAAC,GAAEI,KAAE,KAAK,IAAI,GAAE,KAAK,KAAKH,MAAG,IAAED,GAAE,CAAC,IAAE,GAAEe,KAAE,KAAG,KAAK,KAAK,KAAK,IAAIZ,KAAEC,EAAC,IAAE,KAAK,KAAK;AAAE,MAAAT,KAAE,KAAK,IAAIA,IAAEoB,EAAC,GAAEC,GAAEd,EAAC,EAAE,MAAIC,IAAEa,GAAEd,EAAC,EAAE,MAAIE;AAAA,IAAC;AAAC,aAAQF,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAIX,KAAEyB,GAAEd,EAAC,EAAE,KAAIN,KAAEoB,GAAEd,EAAC,EAAE;AAAI,YAAMR,MAAGC,MAAGJ,KAAEK,OAAI;AAAE,MAAAL,MAAG,KAAK,KAAKG,EAAC,GAAEE,MAAG,KAAK,MAAMF,EAAC;AAAE,YAAMO,KAAEJ,GAAEK,EAAC,IAAEF,KAAET,KAAES,KAAE;AAAE,QAAEE,EAAC,IAAED,MAAGL,KAAEL,MAAGS;AAAA,IAAC;AAAC,UAAMC,KAAEN,KAAEK;AAAE,WAAO,EAAE,CAAC,IAAEC,KAAE,GAAE,EAAE,QAAQ,EAAE,SAAS,MAAK,GAAEA,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBV,IAAE;AAAC,UAAM,IAAE,KAAK,MAAM;AAAK,IAAAU,GAAEY,IAAE,KAAK,MAAM,MAAM,GAAEA,GAAE,CAAC,IAAE,KAAK,MAAM,UAAS,KAAK,MAAM,KAAKtB,EAAC,GAAEA,GAAE,UAAUsB,IAAE,MAAK,IAAE,GAAEtB,GAAE,KAAK,WAAS,EAAE,UAASA,GAAE,KAAK,YAAU,EAAE,WAAUA,GAAE,KAAK,YAAU,EAAE;AAAA,EAAS;AAAA,EAAC,UAAS;AAAC,eAAO;AAAC,YAAMA,KAAE,KAAK,iBAAiB;AAAE,UAAG,OAAKA,GAAE;AAAM,WAAK,MAAM,QAAQA,EAAC,GAAE,KAAK,MAAM,QAAM;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,QAAG,MAAI,KAAK,MAAM,KAAK,UAAU,UAAQ,KAAK,MAAM,OAAO,EAAE,QAAM;AAAG,QAAIA,KAAE;AAAK,UAAM,IAAE,KAAK,MAAM,KAAK;AAAS,QAAIG,KAAE,GAAEC,KAAE;AAAE,WAAKA,KAAE,EAAE,UAAQ,QAAMJ,KAAG,CAAAG,KAAEC,MAAIJ,KAAE,EAAEG,EAAC;AAAE,WAAKC,KAAE,EAAE,SAAQ,KAAG,EAAEA,IAAG,EAAE,QAAM;AAAG,WAAOD;AAAA,EAAC;AAAA,EAAC,cAAcH,IAAE;AAAC,WAAM,CAAC,EAAE,KAAK,wBAAwBA,EAAC,EAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,UAAM,IAAE,KAAK,MAAM,QAAOG,KAAE,KAAK,MAAM;AAAS,WAAOH,GAAE,CAAC,KAAGG,MAAGH,GAAE,CAAC,KAAG,EAAE,CAAC,IAAEG,MAAGH,GAAE,CAAC,KAAG,EAAE,CAAC,IAAEG,MAAGH,GAAE,CAAC,KAAG,EAAE,CAAC,IAAEG,MAAGH,GAAE,CAAC,KAAG,EAAE,CAAC,IAAEG,MAAGH,GAAE,CAAC,KAAG,EAAE,CAAC,IAAEG,MAAGH,GAAE,CAAC,KAAG,EAAE,CAAC,IAAEG;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,cAAa;AAAC,SAAK,SAAO,EAAE,GAAE,KAAK,WAAS,GAAE,KAAK,SAAS,MAAK,MAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKH,IAAE;AAAC,WAAO,KAAK,SAASA,GAAE,MAAKA,GAAE,QAAOA,GAAE,UAASA,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,SAAS,GAAEG,IAAEC,IAAEE,KAAE,KAAK,OAAM;AAAC,WAAO,KAAK,OAAK,EAAE,CAAC,IAAE,IAAE,GAAE,gBAAgB,GAAE,EAAEH,EAAC,KAAG,EAAEA,IAAE,KAAK,MAAM,GAAE,KAAK,WAASC,IAAE,KAAK,QAAME,IAAE;AAAA,EAAI;AAAA,EAAC,iBAAiBN,IAAE;AAAC,SAAK,YAAUA,IAAE,KAAK,+BAA+B;AAAA,EAAC;AAAA,EAAC,iCAAgC;AAAC,SAAK,OAAO,CAAC,IAAE,KAAK,WAAS;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,QAAI,IAAE,KAAK,KAAK,SAASA,EAAC;AAAE,UAAI,IAAE,GAAE,gBAAgB,GAAE,KAAK,KAAK,SAASA,EAAC,IAAE,IAAG,KAAK,OAAK,GAAE,KAAK,YAAU,GAAE,KAAK;AAAQ,UAAMG,KAAEuB,GAAE1B,EAAC;AAAE,WAAO,KAAK,OAAO,CAAC,KAAGG,GAAE,CAAC,IAAE,KAAK,UAAS,KAAK,OAAO,CAAC,KAAGA,GAAE,CAAC,IAAE,KAAK,UAAS,KAAK,OAAO,CAAC,KAAGA,GAAE,CAAC,IAAE,KAAK,UAAS,KAAK,+BAA+B,GAAE;AAAA,EAAI;AAAA,EAAC,UAAUH,IAAE,GAAEG,KAAE,OAAG;AAAC,eAAO;AAAC,UAAG,KAAK,cAAcH,EAAC,EAAE,QAAO,KAAG,EAAE,MAAK,EAAE,GAAE;AAAG,UAAG,KAAK,OAAO,GAAE;AAAC,YAAG,CAACG,GAAE,QAAO,KAAG,EAAE,MAAK,EAAE,GAAE;AAAG,aAAK,KAAK,YAAU;AAAA,MAAI;AAAC,YAAMC,KAAE,KAAK,YAAYJ,EAAC;AAAE,WAAG,EAAE,MAAKI,EAAC,GAAE,KAAK,QAAQA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,QAAM,KAAK,KAAK;AAAA,EAAS;AAAA,EAAC,cAAcJ,IAAE;AAAC,WAAOA,GAAE,CAAC,IAAE,KAAK,WAAS;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAM,IAAE,KAAK;AAAO,YAAO,EAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,IAAE,MAAI,EAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,IAAE,MAAI,EAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,IAAE;AAAA,EAAE;AAAA,EAAC,OAAO,kBAAiB;AAAC,WAAM,EAAC,UAAS,CAAC,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,IAAI,GAAE,WAAU,IAAI,EAAE,EAAC,QAAO,KAAE,CAAC,GAAE,WAAU,IAAI,EAAE,EAAC,QAAO,KAAE,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,UAAS;AAAC,WAAO,GAAE,MAAM,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAO,QAAQA,IAAE;AAAC,OAAE,MAAM,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,YAAW;AAAC,OAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAE;AAAC,EAAAA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS2B,GAAE3B,IAAE,GAAE;AAAC,EAAAA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,IAAIA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAEG,IAAE;AAAC,EAAAA,GAAE,CAAC,IAAEH,GAAE,CAAC,IAAE,GAAEG,GAAE,CAAC,IAAEH,GAAE,CAAC,IAAE,GAAEG,GAAE,CAAC,IAAEH,GAAE,CAAC,IAAE;AAAC;AAAC,SAAS,EAAEA,IAAE,GAAEG,IAAEC,IAAE;AAAC,MAAG,MAAI,GAAE;AAAC,UAAMC,KAAEF,GAAEH,GAAE,CAAC,CAAC;AAAE,MAAEK,IAAED,EAAC;AAAA,EAAC,OAAK;AAAC,IAAAY,GAAE,CAAC,IAAE,IAAE,GAAEA,GAAE,CAAC,IAAE,IAAE,GAAEA,GAAE,CAAC,IAAE,IAAE,GAAE,EAAE,CAAC,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,KAAG;AAAE,aAAQZ,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,KAAEF,GAAEH,GAAEI,EAAC,CAAC;AAAE,QAAEC,GAAE,CAAC,CAAC,MAAI,EAAEW,IAAEX,EAAC,GAAEsB,GAAE,GAAEtB,EAAC;AAAA,IAAE;AAAC,MAAED,IAAEY,IAAE,GAAE,GAAE,GAAEZ,GAAE,CAAC,IAAE,KAAK,IAAI,EAAE,CAAC,IAAEY,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,CAAC,IAAE;AAAA,EAAC;AAAC;AAAC,SAASK,GAAErB,IAAE,GAAEG,IAAE;AAAC,MAAG,CAAC,EAAE,OAAO,UAAQC,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,GAAE,KAAK,EAAC,OAAM,GAAE,UAAS,EAAC,CAAC;AAAE,WAAQA,KAAE,GAAEA,KAAE,GAAE,EAAEA,IAAE;AAAC,UAAMD,KAAEuB,GAAEtB,EAAC;AAAE,MAAE,KAAKA,EAAC,EAAE,QAAMA,IAAE,EAAE,KAAKA,EAAC,EAAE,WAAS,EAAEJ,IAAE,GAAEG,EAAC;AAAA,EAAC;AAAC,IAAE,KAAM,CAACH,IAAEK,OAAIL,GAAE,WAASK,GAAE,QAAS;AAAE,WAAQD,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAD,GAAEC,EAAC,IAAE,EAAE,KAAKA,EAAC,EAAE;AAAK;AAAC,SAAS,EAAEJ,IAAE,GAAE;AAAC,MAAIG,IAAEC,KAAE,IAAE;AAAE,WAAQE,KAAE,GAAEA,KAAE,GAAE,EAAEA,IAAE;AAAC,UAAMG,KAAE,EAAET,IAAE,GAAE,EAAEM,EAAC,CAAC;AAAE,IAAAG,KAAEL,OAAIA,KAAEK,IAAEN,KAAE,EAAEG,EAAC;AAAA,EAAE;AAAC,SAAOH;AAAC;AAAC,SAAS,EAAEH,IAAE,GAAEG,IAAE;AAAC,SAAO,KAAGH,GAAE,CAAC,IAAEG,GAAE,CAAC,IAAEH,GAAE,CAAC,IAAEG,GAAE,CAAC,IAAEH,GAAE,CAAC,IAAEG,GAAE,CAAC;AAAE;AAAC,SAAS,EAAEH,IAAE;AAAC,SAAM,CAAC,MAAMA,EAAC,KAAGA,OAAI,KAAG,KAAGA,OAAI,IAAE,KAAGA,KAAE;AAAC;AAAC,EAAE,QAAM,IAAI,EAAE,CAAC,GAAE,SAASA,IAAE;AAAC,MAAI;AAAE,GAAC,IAAEA,GAAE,eAAaA,GAAE,aAAW,CAAC,IAAI,EAAE,gBAAc,CAAC,IAAE,iBAAgB,EAAE,EAAE,gBAAc,EAAE,IAAE;AAAe,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAM2B,KAAE,CAAChB,GAAE,IAAG,IAAG,EAAE,GAAEA,GAAE,GAAE,IAAG,EAAE,GAAEA,GAAE,IAAG,GAAE,EAAE,GAAEA,GAAE,GAAE,GAAE,EAAE,GAAEA,GAAE,IAAG,IAAG,CAAC,GAAEA,GAAE,GAAE,IAAG,CAAC,GAAEA,GAAE,IAAG,GAAE,CAAC,GAAEA,GAAE,GAAE,GAAE,CAAC,CAAC;AAA5F,IAA8F,IAAE,CAACA,GAAE,IAAG,IAAG,EAAE,GAAEA,GAAE,IAAG,IAAG,CAAC,GAAEA,GAAE,IAAG,GAAE,EAAE,GAAEA,GAAE,IAAG,GAAE,CAAC,GAAEA,GAAE,GAAE,IAAG,EAAE,GAAEA,GAAE,GAAE,IAAG,CAAC,GAAEA,GAAE,GAAE,GAAE,EAAE,GAAEA,GAAE,GAAE,GAAE,CAAC,CAAC;AAApL,IAAsL,IAAE,KAAK,KAAK,CAAC;AAAnM,IAAqMT,KAAE,CAAC,IAAI;AAAE,SAASM,GAAEP,IAAE;AAAC,SAAOC,GAAE,CAAC,IAAED,IAAEC;AAAC;AAAC,IAAM,IAAE,EAAE;AAAV,IAAYiB,KAAE,EAAE;AAAhB,IAAkBF,KAAE,EAAE;AAAtB,IAAwB,IAAE,EAAE;AAA5B,IAA8Bd,KAAE,IAAI;AAApC,IAAsC,IAAE,EAAE;AAA1C,IAA4CoB,KAAE,EAAE;AAAhD,IAAkD,IAAE,EAAE;AAAtD,IAAwDC,KAAE,EAAE;AAA5D,IAA8DE,KAAE,CAAC,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,EAAC,KAAI,GAAE,KAAI,EAAC,CAAC;AAA1G,IAA4G,IAAE,IAAI;AAAlH,IAAoH,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAtI,IAAwIG,KAAE7B;", "names": ["r", "r", "c", "p", "r", "c", "j", "g", "o", "A", "H", "T", "p", "u", "_", "O", "v", "A", "s", "E", "F", "y", "r", "h", "g", "l", "i", "R", "a", "T", "_", "O", "v", "R", "l", "T", "k", "v", "h", "g", "r", "y", "g", "e", "R", "w", "n", "o", "t", "s", "v", "p", "i", "r", "h", "a", "d", "u", "c", "k", "f", "y", "m", "b", "T", "I", "L", "l", "H", "A", "j", "V"]}