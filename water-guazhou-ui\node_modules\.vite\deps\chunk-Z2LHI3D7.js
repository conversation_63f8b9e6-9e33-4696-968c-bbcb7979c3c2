import {
  i,
  o,
  r as r2,
  s as s4
} from "./chunk-KUBJOT5K.js";
import {
  o as o2,
  o2 as o3
} from "./chunk-HPMHGZUK.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import {
  R as R2,
  j
} from "./chunk-R5MYQRRS.js";
import {
  R
} from "./chunk-JXLVNWKF.js";
import {
  s as s3
} from "./chunk-4RZONHOY.js";
import {
  s as s2
} from "./chunk-RV4I37UI.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/normalizeUtils.js
var y = s2.getLogger("esri.geometry.support.normalizeUtils");
function x(e) {
  return "polygon" === e.type;
}
function d(e) {
  return "polygon" === e[0].type;
}
function w(e) {
  return "polyline" === e[0].type;
}
function M(e, n) {
  if (!(e instanceof m || e instanceof v)) {
    const e2 = "straightLineDensify: the input geometry is neither polyline nor polygon";
    throw y.error(e2), new s3(e2);
  }
  const o4 = o(e), s5 = [];
  for (const t2 of o4) {
    const e2 = [];
    s5.push(e2), e2.push([t2[0][0], t2[0][1]]);
    for (let o5 = 0; o5 < t2.length - 1; o5++) {
      const s6 = t2[o5][0], r3 = t2[o5][1], i2 = t2[o5 + 1][0], l = t2[o5 + 1][1], f = Math.sqrt((i2 - s6) * (i2 - s6) + (l - r3) * (l - r3)), c = (l - r3) / f, p = (i2 - s6) / f, u = f / n;
      if (u > 1) {
        for (let l2 = 1; l2 <= u - 1; l2++) {
          const t4 = l2 * n, o7 = p * t4 + s6, i4 = c * t4 + r3;
          e2.push([o7, i4]);
        }
        const t3 = (f + Math.floor(u - 1) * n) / 2, o6 = p * t3 + s6, i3 = c * t3 + r3;
        e2.push([o6, i3]);
      }
      e2.push([i2, l]);
    }
  }
  return x(e) ? new v({ rings: s5, spatialReference: e.spatialReference }) : new m({ paths: s5, spatialReference: e.spatialReference });
}
function R3(e, t2, n) {
  if (t2) {
    const t3 = M(e, 1e6);
    e = j(t3, true);
  }
  return n && (e = s4(e, n)), e;
}
function b(e, t2, n) {
  if (Array.isArray(e)) {
    const o4 = e[0];
    if (o4 > t2) {
      const n2 = i(o4, t2);
      e[0] = o4 + n2 * (-2 * t2);
    } else if (o4 < n) {
      const t3 = i(o4, n);
      e[0] = o4 + t3 * (-2 * n);
    }
  } else {
    const o4 = e.x;
    if (o4 > t2) {
      const n2 = i(o4, t2);
      e = e.clone().offset(n2 * (-2 * t2), 0);
    } else if (o4 < n) {
      const t3 = i(o4, n);
      e = e.clone().offset(t3 * (-2 * n), 0);
    }
  }
  return e;
}
function P(e, t2) {
  let n = -1;
  for (let o4 = 0; o4 < t2.cutIndexes.length; o4++) {
    const s5 = t2.cutIndexes[o4], r3 = t2.geometries[o4], i2 = o(r3);
    for (let e2 = 0; e2 < i2.length; e2++) {
      const t3 = i2[e2];
      t3.some((n2) => {
        if (n2[0] < 180) return true;
        {
          let n3 = 0;
          for (let e3 = 0; e3 < t3.length; e3++) {
            const o6 = t3[e3][0];
            n3 = o6 > n3 ? o6 : n3;
          }
          n3 = Number(n3.toFixed(9));
          const o5 = -360 * i(n3, 180);
          for (let s6 = 0; s6 < t3.length; s6++) {
            const t4 = r3.getPoint(e2, s6);
            r3.setPoint(e2, s6, t4.clone().offset(o5, 0));
          }
          return true;
        }
      });
    }
    if (s5 === n) {
      if (d(e)) for (const t3 of o(r3)) e[s5] = e[s5].addRing(t3);
      else if (w(e)) for (const t3 of o(r3)) e[s5] = e[s5].addPath(t3);
    } else n = s5, e[s5] = r3;
  }
  return e;
}
async function v2(t2, n, l) {
  if (!Array.isArray(t2)) return v2([t2], n);
  n && "string" != typeof n && y.warn("normalizeCentralMeridian()", "The url object is deprecated, use the url string instead");
  const h = "string" == typeof n ? n : (n == null ? void 0 : n.url) ?? s.geometryServiceUrl;
  let x2, d2, w2, j2, M2, L, U2, z, A = 0;
  const S = [], k = [];
  for (const e of t2) if (t(e)) k.push(e);
  else if (x2 || (x2 = e.spatialReference, d2 = R(x2), w2 = x2.isWebMercator, L = w2 ? 102100 : 4326, j2 = r2[L].maxX, M2 = r2[L].minX, U2 = r2[L].plus180Line, z = r2[L].minus180Line), d2) if ("mesh" === e.type) k.push(e);
  else if ("point" === e.type) k.push(b(e.clone(), j2, M2));
  else if ("multipoint" === e.type) {
    const t3 = e.clone();
    t3.points = t3.points.map((e2) => b(e2, j2, M2)), k.push(t3);
  } else if ("extent" === e.type) {
    const t3 = e.clone()._normalize(false, false, d2);
    k.push(t3.rings ? new v(t3) : t3);
  } else if (e.extent) {
    const t3 = e.extent, n2 = i(t3.xmin, M2) * (2 * j2);
    let o4 = 0 === n2 ? e.clone() : s4(e.clone(), n2);
    t3.offset(n2, 0), t3.intersects(U2) && t3.xmax !== j2 ? (A = t3.xmax > A ? t3.xmax : A, o4 = R3(o4, w2), S.push(o4), k.push("cut")) : t3.intersects(z) && t3.xmin !== M2 ? (A = t3.xmax * (2 * j2) > A ? t3.xmax * (2 * j2) : A, o4 = R3(o4, w2, 360), S.push(o4), k.push("cut")) : k.push(o4);
  } else k.push(e.clone());
  else k.push(e);
  let C = i(A, j2), I = -90;
  const X = C, q = new m();
  for (; C > 0; ) {
    const e = 360 * C - 180;
    q.addPath([[e, I], [e, -1 * I]]), I *= -1, C--;
  }
  if (S.length > 0 && X > 0) {
    const e = P(S, await o2(h, S, q, l)), n2 = [], o4 = [];
    for (let l2 = 0; l2 < k.length; l2++) {
      const r4 = k[l2];
      if ("cut" !== r4) o4.push(r4);
      else {
        const r5 = e.shift(), i3 = t2[l2];
        r(i3) && "polygon" === i3.type && i3.rings && i3.rings.length > 1 && r5.rings.length >= i3.rings.length ? (n2.push(r5), o4.push("simplify")) : o4.push(w2 ? R2(r5) : r5);
      }
    }
    if (!n2.length) return o4;
    const r3 = await o3(h, n2, l), i2 = [];
    for (let t3 = 0; t3 < o4.length; t3++) {
      const e2 = o4[t3];
      "simplify" !== e2 ? i2.push(e2) : i2.push(w2 ? R2(r3.shift()) : r3.shift());
    }
    return i2;
  }
  const D = [];
  for (let e = 0; e < k.length; e++) {
    const t3 = k[e];
    if ("cut" !== t3) D.push(t3);
    else {
      const e2 = S.shift();
      D.push(true === w2 ? R2(e2) : e2);
    }
  }
  return D;
}
function U(e, t2) {
  const n = R(t2);
  if (n) {
    const [t3, o4] = n.valid, s5 = o4 - t3;
    if (e < t3) for (; e < t3; ) e += s5;
    if (e > o4) for (; e > o4; ) e -= s5;
  }
  return e;
}

export {
  M,
  v2 as v,
  U
};
//# sourceMappingURL=chunk-Z2LHI3D7.js.map
