<template>
  <el-dialog
    v-model="dialogVisible"
    title="涵养水位分析详情"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="analysis-detail">
      <!-- 基础信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基础信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>测点名称：</label>
              <span>{{ analysisData.stationName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>分析周期：</label>
              <span>{{ formatDateTime(analysisData.startTime) }} 至 {{ formatDateTime(analysisData.endTime) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>分析时间：</label>
              <span>{{ formatDateTime(analysisData.createTime) }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>分析人：</label>
              <span>{{ analysisData.creatorName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>算法版本：</label>
              <span>{{ analysisData.algorithmVersion }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>分析状态：</label>
              <el-tag :type="getStatusType(analysisData.status)">
                {{ getStatusText(analysisData.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 分析结果概览 -->
      <el-row :gutter="20" style="margin-bottom: 20px">
        <el-col :span="6">
          <el-card class="result-card" shadow="never">
            <div class="result-content">
              <div class="result-value" :class="getLevelChangeClass(analysisData.levelChange)">
                {{ formatNumber(analysisData.levelChange) }}m
              </div>
              <div class="result-label">水位变化</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="result-card" shadow="never">
            <div class="result-content">
              <div class="result-value">{{ formatNumber(analysisData.conservationCoefficient) }}</div>
              <div class="result-label">涵养系数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="result-card" shadow="never">
            <div class="result-content">
              <div class="result-value">{{ formatNumber(analysisData.conservationPotential) }}%</div>
              <div class="result-label">涵养潜力</div>
              <el-progress
                :percentage="analysisData.conservationPotential"
                :color="getPotentialColor(analysisData.conservationPotential)"
                :show-text="false"
                style="margin-top: 8px"
              />
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="result-card" shadow="never">
            <div class="result-content">
              <div class="result-value">
                <el-tag :type="getRiskLevelType(analysisData.riskLevel)" size="large">
                  {{ getRiskLevelText(analysisData.riskLevel) }}
                </el-tag>
              </div>
              <div class="result-label">风险等级</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细数据 -->
      <el-row :gutter="20" style="margin-bottom: 20px">
        <el-col :span="12">
          <el-card class="data-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span>水位数据</span>
              </div>
            </template>
            
            <div class="data-grid">
              <div class="data-item">
                <label>期初水位：</label>
                <span>{{ formatNumber(analysisData.initialLevel) }} 米</span>
              </div>
              <div class="data-item">
                <label>期末水位：</label>
                <span>{{ formatNumber(analysisData.finalLevel) }} 米</span>
              </div>
              <div class="data-item">
                <label>水位变化：</label>
                <span :class="getLevelChangeClass(analysisData.levelChange)">
                  {{ formatNumber(analysisData.levelChange) }} 米
                </span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="data-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span>环境因素</span>
              </div>
            </template>
            
            <div class="data-grid">
              <div class="data-item">
                <label>平均降雨量：</label>
                <span>{{ formatNumber(analysisData.avgRainfall) }} 毫米</span>
              </div>
              <div class="data-item">
                <label>平均蒸发量：</label>
                <span>{{ formatNumber(analysisData.avgEvaporation) }} 毫米</span>
              </div>
              <div class="data-item">
                <label>总开采量：</label>
                <span>{{ formatNumber(analysisData.totalExtraction) }} 立方米</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 涵养建议 -->
      <el-card class="suggestion-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>涵养建议</span>
            <el-tag v-if="analysisData.suggestedConservationAmount" type="primary">
              建议涵养量：{{ formatNumber(analysisData.suggestedConservationAmount) }} 立方米
            </el-tag>
          </div>
        </template>
        
        <div class="suggestion-content">
          <div class="suggestion-text">
            {{ analysisData.conservationSuggestion || '暂无建议' }}
          </div>
          
          <div v-if="analysisData.riskDescription" class="risk-description">
            <el-alert
              :title="analysisData.riskDescription"
              :type="getRiskAlertType(analysisData.riskLevel)"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </el-card>

      <!-- 水位变化趋势图 -->
      <el-card class="chart-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>水位变化趋势</span>
          </div>
        </template>
        
        <div class="chart-container">
          <div ref="chartRef" style="width: 100%; height: 400px;"></div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import type { ConservationAnalysis } from '@/api/waterSource/conservationWaterLevel'
import { formatDate } from '@/utils/DateFormatter'
import { dwnai } from '@/utils/processNumber'

// 格式化函数
const formatDateTime = (date: string | number | Date | undefined) => {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
}

const formatNumber = (value: number | string | undefined, precision = 2) => {
  if (value === undefined || value === null || value === '') return '--'
  return dwnai(value)
}

// Props
interface Props {
  visible: boolean
  analysisData: ConservationAnalysis
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  analysisData: () => ({} as ConservationAnalysis)
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    nextTick(() => {
      initChart()
    })
  }
})

// 监听分析数据变化
watch(() => props.analysisData, (newData) => {
  if (newData && chartInstance) {
    updateChart(newData)
  }
}, { deep: true })

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart(props.analysisData)
}

// 更新图表
const updateChart = (data: ConservationAnalysis) => {
  if (!chartInstance || !data.analysisDetails) return
  
  try {
    const details = JSON.parse(data.analysisDetails)
    const levelTrend = details.levelTrend || []
    
    const option = {
      title: {
        text: '水位变化趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0]
          return `
            时间: ${new Date(data.axisValue).toLocaleString()}<br/>
            原水液位: ${data.value[1]}m<br/>
            地下水位: ${data.value[2]}m<br/>
            液位变化: ${data.value[3]}m
          `
        }
      },
      legend: {
        data: ['原水液位', '地下水位', '液位变化'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'time',
        boundaryGap: false
      },
      yAxis: [
        {
          type: 'value',
          name: '水位(m)',
          position: 'left'
        },
        {
          type: 'value',
          name: '变化量(m)',
          position: 'right'
        }
      ],
      series: [
        {
          name: '原水液位',
          type: 'line',
          data: levelTrend.map((item: any) => [item.time, item.rawWaterLevel]),
          smooth: true,
          lineStyle: { color: '#409EFF' }
        },
        {
          name: '地下水位',
          type: 'line',
          data: levelTrend.map((item: any) => [item.time, item.groundwaterLevel]),
          smooth: true,
          lineStyle: { color: '#67C23A' }
        },
        {
          name: '液位变化',
          type: 'line',
          yAxisIndex: 1,
          data: levelTrend.map((item: any) => [item.time, item.levelChange]),
          smooth: true,
          lineStyle: { color: '#E6A23C' }
        }
      ]
    }
    
    chartInstance.setOption(option)
  } catch (error) {
    console.error('解析分析详情数据失败:', error)
  }
}

// 获取状态类型
const getStatusType = (status: number | undefined) => {
  switch (status) {
    case 1: return 'warning'
    case 2: return 'success'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: number | undefined) => {
  switch (status) {
    case 1: return '分析中'
    case 2: return '已完成'
    case 3: return '分析失败'
    default: return '未知'
  }
}

// 获取液位变化样式类
const getLevelChangeClass = (levelChange: number | undefined) => {
  if (!levelChange) return ''
  if (levelChange > 0) return 'level-increase'
  if (levelChange < 0) return 'level-decrease'
  return ''
}

// 获取涵养潜力颜色
const getPotentialColor = (potential: number | undefined) => {
  if (!potential) return '#e6e6e6'
  if (potential >= 80) return '#67c23a'
  if (potential >= 60) return '#e6a23c'
  if (potential >= 40) return '#f56c6c'
  return '#909399'
}

// 获取风险等级类型
const getRiskLevelType = (riskLevel: number | undefined) => {
  switch (riskLevel) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取风险等级文本
const getRiskLevelText = (riskLevel: number | undefined) => {
  switch (riskLevel) {
    case 1: return '低风险'
    case 2: return '中风险'
    case 3: return '高风险'
    default: return '未知'
  }
}

// 获取风险警告类型
const getRiskAlertType = (riskLevel: number | undefined) => {
  switch (riskLevel) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'error'
    default: return 'info'
  }
}

// 导出报告
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 关闭对话框
const handleClose = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  emit('update:visible', false)
}
</script>

<style scoped>
.analysis-detail {
  padding: 0 10px;
}

.info-card,
.data-card,
.suggestion-card,
.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.info-item {
  margin-bottom: 12px;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
}

.result-card {
  text-align: center;
}

.result-content {
  padding: 20px 0;
}

.result-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.result-label {
  font-size: 14px;
  color: #606266;
}

.data-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.data-item:last-child {
  border-bottom: none;
}

.data-item label {
  font-weight: 600;
  color: #606266;
}

.suggestion-content {
  line-height: 1.6;
}

.suggestion-text {
  white-space: pre-line;
  margin-bottom: 16px;
  color: #303133;
}

.risk-description {
  margin-top: 16px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

.level-increase {
  color: #67c23a;
  font-weight: 600;
}

.level-decrease {
  color: #f56c6c;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
