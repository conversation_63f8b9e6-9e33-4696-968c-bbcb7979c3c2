import {
  f,
  l as l2
} from "./chunk-5VENLL23.js";
import {
  L,
  c as c2,
  e,
  h as h3,
  l,
  m,
  n,
  o,
  p as p2,
  s as s3,
  t as t2,
  u as u2
} from "./chunk-Q7KDWWJV.js";
import {
  d as d2,
  d2 as d3,
  g,
  h as h2,
  w
} from "./chunk-6KZTVN32.js";
import {
  p,
  y
} from "./chunk-O2BYTJI4.js";
import {
  h
} from "./chunk-7RSPPKZ6.js";
import {
  a
} from "./chunk-FZ7BG3VX.js";
import {
  d,
  j
} from "./chunk-ETY52UBV.js";
import {
  u
} from "./chunk-G5KX4JSG.js";
import {
  E
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  c,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/symbols/support/previewSymbol3D.js
var C = t2.size;
var I = t2.maxSize;
var R = t2.maxOutlineSize;
var E2 = t2.lineWidth;
var Z = t2.tallSymbolWidth;
function q(e2) {
  const t3 = e2.outline, s4 = r(e2.material) ? e2.material.color : null, r2 = r(s4) ? s4.toHex() : null;
  if (t(t3) || "pattern" in t3 && r(t3.pattern) && "style" === t3.pattern.type && "none" === t3.pattern.style) return "fill" === e2.type && "#ffffff" === r2 ? { color: "#bdc3c7", width: 0.75 } : null;
  const a2 = u(t3.size) || 0;
  return { color: "rgba(" + (r(t3.color) ? t3.color.toRgba() : "255,255,255,1") + ")", width: Math.min(a2, R), style: "pattern" in t3 && r(t3.pattern) && "style" === t3.pattern.type ? d2(t3.pattern.style) : null, join: "butt", cap: "patternCap" in t3 ? t3.patternCap : "butt" };
}
function A(t3, s4) {
  const r2 = s4 && s4.resource, a2 = r2 && r2.href;
  if (t3.thumbnail && t3.thumbnail.url) return Promise.resolve(t3.thumbnail.url);
  if (a2 && "object" !== s4.type) return Promise.resolve(d3(s4));
  const o2 = a("esri/images/Legend/legend3dsymboldefault.png");
  return t3.styleOrigin && (t3.styleOrigin.styleName || t3.styleOrigin.styleUrl) ? h(t3.styleOrigin, { portal: t3.styleOrigin.portal }, "webRef").catch((e2) => e2).then((e2) => {
    var _a;
    return ((_a = e2 == null ? void 0 : e2.thumbnail) == null ? void 0 : _a.url) || o2;
  }) : Promise.resolve(o2);
}
function H(e2, r2 = 1) {
  const a2 = e2.a, o2 = y(e2), n2 = o2.h, l3 = o2.s / r2, i = 100 - (100 - o2.v) / r2, { r: c3, g: p3, b: u3 } = p({ h: n2, s: l3, v: i });
  return [c3, p3, u3, a2];
}
function N(e2) {
  return "water" === e2.type ? t(e2.color) ? null : e2.color : t(e2.material) || t(e2.material.color) ? null : e2.material.color;
}
function T(e2, t3 = 0) {
  const s4 = N(e2);
  if (!s4) {
    if ("fill" === e2.type) return null;
    const s5 = g.r, r3 = h3(s5, t3);
    return [r3, r3, r3, 100];
  }
  const r2 = s4.toRgba();
  for (let a2 = 0; a2 < 3; a2++) r2[a2] = h3(r2[a2], t3);
  return r2;
}
async function W(t3, s4) {
  const r2 = t3.style;
  if ("none" === r2) return null;
  return { type: "pattern", x: 0, y: 0, src: await h2(a(`esri/symbols/patterns/${r2}.png`), s4.toCss(true)), width: 5, height: 5 };
}
function $(e2) {
  return e2.outline ? q(e2) : { color: "rgba(0, 0, 0, 1)", width: 1.5 };
}
function B(e2, t3) {
  const s4 = N(e2);
  if (!s4) return null;
  let r2 = "rgba(";
  return r2 += h3(s4.r, t3) + ",", r2 += h3(s4.g, t3) + ",", r2 += h3(s4.b, t3) + ",", r2 + s4.a + ");";
}
function F(e2, t3) {
  const s4 = B(e2, t3);
  if (!s4) return {};
  if ("pattern" in e2 && r(e2.pattern) && "style" === e2.pattern.type && "none" === e2.pattern.style) return null;
  return { color: s4, width: Math.min(e2.size ? u(e2.size) : 0.75, R), style: "pattern" in e2 && r(e2.pattern) && "style" === e2.pattern.type ? d2(e2.pattern.style) : null, cap: "cap" in e2 ? e2.cap : null, join: "join" in e2 ? "miter" === e2.join ? u(2) : e2.join : null };
}
function G(e2, t3, s4) {
  const r2 = null != s4 ? 0.75 * s4 : 0;
  return { type: "linear", x1: r2 ? 0.25 * r2 : 0, y1: r2 ? 0.5 * r2 : 0, x2: r2 || 4, y2: r2 ? 0.5 * r2 : 4, colors: [{ color: e2, offset: 0 }, { color: t3, offset: 1 }] };
}
function J(e2) {
  const t3 = e2.depth, s4 = e2.height, r2 = e2.width;
  return 0 !== r2 && 0 !== t3 && 0 !== s4 && r2 === t3 && null != r2 && null != s4 && r2 < s4;
}
function K(e2, t3, s4) {
  const r2 = [];
  if (!e2) return r2;
  switch (e2.type) {
    case "icon": {
      const s5 = 0, a2 = 0, o2 = t3, n2 = t3;
      switch (e2.resource && e2.resource.primitive || j) {
        case "circle":
          r2.push({ shape: { type: "circle", cx: 0, cy: 0, r: 0.5 * t3 }, fill: T(e2, 0), stroke: q(e2) });
          break;
        case "square":
          r2.push({ shape: { type: "path", path: [{ command: "M", values: [s5, n2] }, { command: "L", values: [s5, a2] }, { command: "L", values: [o2, a2] }, { command: "L", values: [o2, n2] }, { command: "Z", values: [] }] }, fill: T(e2, 0), stroke: q(e2) });
          break;
        case "triangle":
          r2.push({ shape: { type: "path", path: [{ command: "M", values: [s5, n2] }, { command: "L", values: [0.5 * o2, a2] }, { command: "L", values: [o2, n2] }, { command: "Z", values: [] }] }, fill: T(e2, 0), stroke: q(e2) });
          break;
        case "cross":
          r2.push({ shape: { type: "path", path: [{ command: "M", values: [0.5 * o2, a2] }, { command: "L", values: [0.5 * o2, n2] }, { command: "M", values: [s5, 0.5 * n2] }, { command: "L", values: [o2, 0.5 * n2] }] }, stroke: $(e2) });
          break;
        case "x":
          r2.push({ shape: { type: "path", path: [{ command: "M", values: [s5, a2] }, { command: "L", values: [o2, n2] }, { command: "M", values: [o2, a2] }, { command: "L", values: [s5, n2] }] }, stroke: $(e2) });
          break;
        case "kite":
          r2.push({ shape: { type: "path", path: [{ command: "M", values: [s5, 0.5 * n2] }, { command: "L", values: [0.5 * o2, a2] }, { command: "L", values: [o2, 0.5 * n2] }, { command: "L", values: [0.5 * o2, n2] }, { command: "Z", values: [] }] }, fill: T(e2, 0), stroke: q(e2) });
      }
      break;
    }
    case "object":
      switch (e2.resource && e2.resource.primitive || d) {
        case "cone": {
          const a2 = G(T(e2, 0), T(e2, -0.6), s4 ? Z : t3), o2 = l(t3, s4);
          r2.push({ shape: o2[0], fill: a2 }), r2.push({ shape: o2[1], fill: a2 });
          break;
        }
        case "inverted-cone": {
          const s5 = T(e2, 0), a2 = G(s5, T(e2, -0.6), t3), o2 = o(t3);
          r2.push({ shape: o2[0], fill: a2 }), r2.push({ shape: o2[1], fill: s5 });
          break;
        }
        case "cube": {
          const a2 = p2(t3, s4);
          r2.push({ shape: a2[0], fill: T(e2, 0) }), r2.push({ shape: a2[1], fill: T(e2, -0.3) }), r2.push({ shape: a2[2], fill: T(e2, -0.5) });
          break;
        }
        case "cylinder": {
          const a2 = G(T(e2, 0), T(e2, -0.6), s4 ? Z : t3), o2 = L(t3, s4);
          r2.push({ shape: o2[0], fill: a2 }), r2.push({ shape: o2[1], fill: a2 }), r2.push({ shape: o2[2], fill: T(e2, 0) });
          break;
        }
        case "diamond": {
          const s5 = s3(t3);
          r2.push({ shape: s5[0], fill: T(e2, -0.3) }), r2.push({ shape: s5[1], fill: T(e2, 0) }), r2.push({ shape: s5[2], fill: T(e2, -0.3) }), r2.push({ shape: s5[3], fill: T(e2, -0.7) });
          break;
        }
        case "sphere": {
          const s5 = G(T(e2, 0), T(e2, -0.6));
          s5.x1 = 0, s5.y1 = 0, s5.x2 = 0.25 * t3, s5.y2 = 0.25 * t3, r2.push({ shape: { type: "circle", cx: 0, cy: 0, r: 0.5 * t3 }, fill: s5 });
          break;
        }
        case "tetrahedron": {
          const s5 = u2(t3);
          r2.push({ shape: s5[0], fill: T(e2, -0.3) }), r2.push({ shape: s5[1], fill: T(e2, 0) }), r2.push({ shape: s5[2], fill: T(e2, -0.6) });
          break;
        }
      }
      break;
  }
  return r2;
}
function Q(e2) {
  const t3 = "number" == typeof (e2 == null ? void 0 : e2.size) ? e2 == null ? void 0 : e2.size : null;
  return t3 ? u(t3) : null;
}
function V(e2) {
  return "icon" === e2.type ? "multiply" : "tint";
}
function X(e2, t3) {
  const s4 = Q(t3), r2 = (t3 == null ? void 0 : t3.maxSize) ? u(t3.maxSize) : null, o2 = (t3 == null ? void 0 : t3.disableUpsampling) ?? false, n2 = e2.symbolLayers, l3 = [];
  let p3 = 0, u3 = 0;
  const h4 = n2.getItemAt(n2.length - 1);
  let m2;
  return h4 && "icon" === h4.type && (m2 = h4.size && u(h4.size)), n2.forEach((a2) => {
    var _a;
    if ("icon" !== a2.type && "object" !== a2.type) return;
    const n3 = "icon" === a2.type ? a2.size && u(a2.size) : 0, i = s4 || n3 ? Math.ceil(Math.min(s4 || n3, r2 || I)) : C;
    if (a2 && a2.resource && a2.resource.href) {
      const t4 = A(e2, a2).then((e3) => {
        const t5 = a2.get("material.color"), s5 = V(a2);
        return f(e3, i, t5, s5, o2);
      }).then((e3) => {
        const t5 = e3.width, s5 = e3.height;
        return p3 = Math.max(p3, t5), u3 = Math.max(u3, s5), [{ shape: { type: "image", x: 0, y: 0, width: t5, height: s5, src: e3.url }, fill: null, stroke: null }];
      });
      l3.push(t4);
    } else {
      let e3 = i;
      "icon" === a2.type && m2 && s4 && (e3 = i * (n3 / m2));
      const r3 = "tall" === (t3 == null ? void 0 : t3.symbolConfig) || ((_a = t3 == null ? void 0 : t3.symbolConfig) == null ? void 0 : _a.isTall) || "object" === a2.type && J(a2);
      p3 = Math.max(p3, r3 ? Z : e3), u3 = Math.max(u3, e3), l3.push(Promise.resolve(K(a2, e3, r3)));
    }
  }), E(l3).then((e3) => {
    const s5 = [];
    return e3.forEach((e4) => {
      e4.value ? s5.push(e4.value) : e4.error && s.getLogger("esri.symbols.support.previewSymbol3D").warn("error while building swatchInfo!", e4.error);
    }), l2(s5, [p3, u3], { node: t3 && t3.node, scale: false, opacity: t3 && t3.opacity });
  });
}
function Y(e2, t3) {
  const s4 = e2.symbolLayers, r2 = [], a2 = w(e2), n2 = Q(t3), l3 = (t3 && t3.maxSize ? u(t3.maxSize) : null) || R;
  let i, p3 = 0, u3 = 0;
  return s4.forEach((e3, t4) => {
    if (!e3) return;
    if ("line" !== e3.type && "path" !== e3.type) return;
    const s5 = [];
    switch (e3.type) {
      case "line": {
        const r3 = F(e3, 0);
        if (t(r3)) break;
        const a3 = r3 && r3.width || 0;
        0 === t4 && (i = a3);
        const c3 = Math.min(n2 || a3, l3), h4 = 0 === t4 ? c3 : n2 ? c3 * (a3 / i) : c3, m2 = h4 > E2 / 2 ? 2 * h4 : E2;
        u3 = Math.max(u3, h4), p3 = Math.max(p3, m2), r3.width = h4, s5.push({ shape: { type: "path", path: [{ command: "M", values: [0, 0.5 * u3] }, { command: "L", values: [p3, 0.5 * u3] }] }, stroke: r3 });
        break;
      }
      case "path": {
        const t5 = Math.min(n2 || C, l3), r3 = T(e3, 0), a3 = T(e3, -0.2), o2 = B(e3, -0.4), i2 = o2 ? { color: o2, width: 1 } : {};
        if ("quad" === e3.profile) {
          const t6 = e3.width, o3 = e3.height, n3 = m(t6 && o3 ? t6 / o3 : 1, 0 === o3, 0 === t6), l4 = { ...i2, join: "bevel" };
          s5.push({ shape: n3[0], fill: a3, stroke: l4 }), s5.push({ shape: n3[1], fill: a3, stroke: l4 }), s5.push({ shape: n3[2], fill: r3, stroke: l4 });
        } else s5.push({ shape: e.pathSymbol3DLayer[0], fill: a3, stroke: i2 }), s5.push({ shape: e.pathSymbol3DLayer[1], fill: r3, stroke: i2 });
        u3 = Math.max(u3, t5), p3 = u3;
      }
    }
    r2.push(s5);
  }), Promise.resolve(l2(r2, [p3, u3], { node: t3 && t3.node, scale: a2, opacity: t3 && t3.opacity }));
}
async function _(e2, t3) {
  const s4 = "mesh-3d" === e2.type, r2 = e2.symbolLayers, a2 = Q(t3), i = t3 && t3.maxSize ? u(t3.maxSize) : null, p3 = a2 || C, u3 = [];
  let h4 = 0, m2 = 0, f2 = false;
  for (let c3 = 0; c3 < r2.length; c3++) {
    const e3 = r2.getItemAt(c3), t4 = [];
    if (s4 && "fill" !== e3.type) continue;
    const a3 = e.fill[0];
    switch (e3.type) {
      case "fill": {
        const r3 = q(e3), o2 = Math.min(p3, i || I);
        h4 = Math.max(h4, o2), m2 = Math.max(m2, o2), f2 = true;
        let n2 = T(e3, 0);
        const c4 = "pattern" in e3 ? e3.pattern : null, u4 = N(e3);
        !s4 && r(c4) && "style" === c4.type && "solid" !== c4.style && u4 && (n2 = await W(c4, u4)), t4.push({ shape: a3, fill: n2, stroke: r3 });
        break;
      }
      case "line": {
        const s5 = F(e3, 0);
        if (t(s5)) break;
        const r3 = { stroke: s5, shape: a3 };
        h4 = Math.max(h4, C), m2 = Math.max(m2, C), t4.push(r3);
        break;
      }
      case "extrude": {
        const s5 = { join: "round", width: 1, ...F(e3, -0.4) }, r3 = T(e3, 0), a4 = T(e3, -0.2), o2 = Math.min(p3, i || I), n2 = c2(o2);
        s5.width = 1, t4.push({ shape: n2[0], fill: a4, stroke: s5 }), t4.push({ shape: n2[1], fill: a4, stroke: s5 }), t4.push({ shape: n2[2], fill: r3, stroke: s5 });
        const l3 = C, c4 = 0.7 * C + 0.5 * o2;
        h4 = Math.max(h4, l3), m2 = Math.max(m2, c4);
        break;
      }
      case "water": {
        const s5 = c(N(e3)), r3 = H(s5), a4 = H(s5, 2), o2 = H(s5, 3), l3 = n();
        f2 = true, t4.push({ shape: l3[0], fill: r3 }), t4.push({ shape: l3[1], fill: a4 }), t4.push({ shape: l3[2], fill: o2 });
        const c4 = Math.min(p3, i || I);
        h4 = Math.max(h4, c4), m2 = Math.max(m2, c4);
        break;
      }
    }
    u3.push(t4);
  }
  return l2(u3, [h4, m2], { node: t3 && t3.node, scale: f2, opacity: t3 && t3.opacity });
}
function ee(e2, t3) {
  if (0 === e2.symbolLayers.length) return Promise.reject(new s2("symbolPreview: renderPreviewHTML3D", "No symbolLayers in the symbol."));
  switch (e2.type) {
    case "point-3d":
      return X(e2, t3);
    case "line-3d":
      return Y(e2, t3);
    case "polygon-3d":
    case "mesh-3d":
      return _(e2, t3);
  }
  return Promise.reject(new s2("symbolPreview: swatchInfo3D", "symbol not supported."));
}

export {
  T,
  W,
  Q,
  ee
};
//# sourceMappingURL=chunk-ZOZF7ATM.js.map
