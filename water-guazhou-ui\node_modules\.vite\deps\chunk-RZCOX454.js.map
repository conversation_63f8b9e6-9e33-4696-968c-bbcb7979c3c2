{"version": 3, "sources": ["../../@arcgis/core/portal/timeUnitKebabDictionary.js", "../../@arcgis/core/TimeInterval.js", "../../@arcgis/core/layers/support/TimeInfo.js", "../../@arcgis/core/layers/mixins/TemporalLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{strict as s}from\"../core/jsonMap.js\";const e=s()({esriTimeUnitsMilliseconds:\"milliseconds\",esriTimeUnitsSeconds:\"seconds\",esriTimeUnitsMinutes:\"minutes\",esriTimeUnitsHours:\"hours\",esriTimeUnitsDays:\"days\",esriTimeUnitsWeeks:\"weeks\",esriTimeUnitsMonths:\"months\",esriTimeUnitsYears:\"years\",esriTimeUnitsDecades:\"decades\",esriTimeUnitsCenturies:\"centuries\",esriTimeUnitsUnknown:void 0});export{e as timeUnitKebabDictionary};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"./chunks/tslib.es6.js\";import{ClonableMixin as r}from\"./core/Clonable.js\";import{JSONSupport as t}from\"./core/JSONSupport.js\";import{convertTime as s}from\"./core/timeUtils.js\";import{property as e}from\"./core/accessorSupport/decorators/property.js\";import\"./core/accessorSupport/ensureType.js\";import\"./core/arrayUtils.js\";import{enumeration as i}from\"./core/accessorSupport/decorators/enumeration.js\";import{subclass as p}from\"./core/accessorSupport/decorators/subclass.js\";import{timeUnitKebabDictionary as c}from\"./portal/timeUnitKebabDictionary.js\";let l=class extends(r(t)){constructor(o){super(o),this.unit=\"milliseconds\",this.value=0}toMilliseconds(){return s(this.value,this.unit,\"milliseconds\")}};o([i(c,{nonNullable:!0})],l.prototype,\"unit\",void 0),o([e({type:Number,json:{write:!0},nonNullable:!0})],l.prototype,\"value\",void 0),l=o([p(\"esri.TimeInterval\")],l);const a=l;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../TimeExtent.js\";import r from\"../../TimeInterval.js\";import{ClonableMixin as l}from\"../../core/Clonable.js\";import{JSONSupport as i}from\"../../core/JSONSupport.js\";import{isSome as o}from\"../../core/maybe.js\";import{property as n}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as a}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as m}from\"../../core/accessorSupport/decorators/writer.js\";import p from\"../../time/TimeReference.js\";function u(e,t){return r.fromJSON({value:e,unit:t})}let d=class extends(l(i)){constructor(e){super(e),this.cumulative=!1,this.endField=null,this.fullTimeExtent=null,this.hasLiveData=!1,this.interval=null,this.startField=null,this.timeReference=null,this.trackIdField=null,this.useTime=!0}readFullTimeExtent(e,r){if(!r.timeExtent||!Array.isArray(r.timeExtent)||2!==r.timeExtent.length)return null;const l=new Date(r.timeExtent[0]),i=new Date(r.timeExtent[1]);return new t({start:l,end:i})}writeFullTimeExtent(e,t){e&&o(e.start)&&o(e.end)?t.timeExtent=[e.start.getTime(),e.end.getTime()]:t.timeExtent=null}readInterval(e,t){return t.timeInterval&&t.timeIntervalUnits?u(t.timeInterval,t.timeIntervalUnits):t.defaultTimeInterval&&t.defaultTimeIntervalUnits?u(t.defaultTimeInterval,t.defaultTimeIntervalUnits):null}writeInterval(e,t){t.timeInterval=e?.toJSON().value??null,t.timeIntervalUnits=e?.toJSON().unit??null}};e([n({type:Boolean,json:{name:\"exportOptions.timeDataCumulative\",write:!0}})],d.prototype,\"cumulative\",void 0),e([n({type:String,json:{name:\"endTimeField\",write:{enabled:!0,allowNull:!0}}})],d.prototype,\"endField\",void 0),e([n({type:t,json:{write:{enabled:!0,allowNull:!0}}})],d.prototype,\"fullTimeExtent\",void 0),e([a(\"fullTimeExtent\",[\"timeExtent\"])],d.prototype,\"readFullTimeExtent\",null),e([m(\"fullTimeExtent\")],d.prototype,\"writeFullTimeExtent\",null),e([n({type:Boolean,json:{write:!0}})],d.prototype,\"hasLiveData\",void 0),e([n({type:r,json:{write:{enabled:!0,allowNull:!0}}})],d.prototype,\"interval\",void 0),e([a(\"interval\",[\"timeInterval\",\"timeIntervalUnits\",\"defaultTimeInterval\",\"defaultTimeIntervalUnits\"])],d.prototype,\"readInterval\",null),e([m(\"interval\")],d.prototype,\"writeInterval\",null),e([n({type:String,json:{name:\"startTimeField\",write:{enabled:!0,allowNull:!0}}})],d.prototype,\"startField\",void 0),e([n({type:p,json:{write:{enabled:!0,allowNull:!0}}})],d.prototype,\"timeReference\",void 0),e([n({type:String,json:{write:{enabled:!0,allowNull:!0}}})],d.prototype,\"trackIdField\",void 0),e([n({type:Boolean,json:{name:\"exportOptions.useTime\",write:!0}})],d.prototype,\"useTime\",void 0),d=e([s(\"esri.layers.support.TimeInfo\")],d);const v=d;export{v as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../TimeExtent.js\";import r from\"../../TimeInterval.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as i}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";import{fixTimeInfoFields as p}from\"../support/fieldUtils.js\";import n from\"../support/TimeInfo.js\";import{timeUnitKebabDictionary as m}from\"../../portal/timeUnitKebabDictionary.js\";const a=a=>{let f=class extends a{constructor(){super(...arguments),this.timeExtent=null,this.timeOffset=null,this.useViewTime=!0}readOffset(e,t){const o=t.timeInfo.exportOptions;if(!o)return null;const i=o.timeOffset,s=m.fromJSON(o.timeOffsetUnits);return i&&s?new r({value:i,unit:s}):null}set timeInfo(e){p(e,this.fieldsIndex),this._set(\"timeInfo\",e)}};return e([o({type:t,json:{write:!1}})],f.prototype,\"timeExtent\",void 0),e([o({type:r})],f.prototype,\"timeOffset\",void 0),e([i(\"service\",\"timeOffset\",[\"timeInfo.exportOptions\"])],f.prototype,\"readOffset\",null),e([o({value:null,type:n,json:{write:!0,origins:{\"web-document\":{read:!1,write:!1},\"portal-item\":{read:!1,write:!1}}}})],f.prototype,\"timeInfo\",null),e([o({type:Boolean,json:{read:{source:\"timeAnimation\"},write:{target:\"timeAnimation\"},origins:{\"web-scene\":{read:!1,write:!1}}}})],f.prototype,\"useViewTime\",void 0),f=e([s(\"esri.layers.mixins.TemporalLayer\")],f),f};export{a as TemporalLayer};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4C,IAAMA,KAAEC,GAAE,EAAE,EAAC,2BAA0B,gBAAe,sBAAqB,WAAU,sBAAqB,WAAU,oBAAmB,SAAQ,mBAAkB,QAAO,oBAAmB,SAAQ,qBAAoB,UAAS,oBAAmB,SAAQ,sBAAqB,WAAU,wBAAuB,aAAY,sBAAqB,OAAM,CAAC;;;ACAuL,IAAIC,KAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,gBAAe,KAAK,QAAM;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,WAAO,EAAE,KAAK,OAAM,KAAK,MAAK,cAAc;AAAA,EAAC;AAAC;AAAE,EAAE,CAACA,GAAEC,IAAE,EAAC,aAAY,KAAE,CAAC,CAAC,GAAEF,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,GAAE,aAAY,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,mBAAmB,CAAC,GAAEA,EAAC;AAAE,IAAMG,KAAEH;;;ACAvM,SAAS,EAAEI,IAAE,GAAE;AAAC,SAAOC,GAAE,SAAS,EAAC,OAAMD,IAAE,MAAK,EAAC,CAAC;AAAC;AAAC,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,OAAG,KAAK,WAAS,MAAK,KAAK,iBAAe,MAAK,KAAK,cAAY,OAAG,KAAK,WAAS,MAAK,KAAK,aAAW,MAAK,KAAK,gBAAc,MAAK,KAAK,eAAa,MAAK,KAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,mBAAmBA,IAAEE,IAAE;AAAC,QAAG,CAACA,GAAE,cAAY,CAAC,MAAM,QAAQA,GAAE,UAAU,KAAG,MAAIA,GAAE,WAAW,OAAO,QAAO;AAAK,UAAMC,KAAE,IAAI,KAAKD,GAAE,WAAW,CAAC,CAAC,GAAEE,KAAE,IAAI,KAAKF,GAAE,WAAW,CAAC,CAAC;AAAE,WAAO,IAAI,EAAE,EAAC,OAAMC,IAAE,KAAIC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBJ,IAAE,GAAE;AAAC,IAAAA,MAAG,EAAEA,GAAE,KAAK,KAAG,EAAEA,GAAE,GAAG,IAAE,EAAE,aAAW,CAACA,GAAE,MAAM,QAAQ,GAAEA,GAAE,IAAI,QAAQ,CAAC,IAAE,EAAE,aAAW;AAAA,EAAI;AAAA,EAAC,aAAaA,IAAE,GAAE;AAAC,WAAO,EAAE,gBAAc,EAAE,oBAAkB,EAAE,EAAE,cAAa,EAAE,iBAAiB,IAAE,EAAE,uBAAqB,EAAE,2BAAyB,EAAE,EAAE,qBAAoB,EAAE,wBAAwB,IAAE;AAAA,EAAI;AAAA,EAAC,cAAcA,IAAE,GAAE;AAAC,MAAE,gBAAaA,MAAA,gBAAAA,GAAG,SAAS,UAAO,MAAK,EAAE,qBAAkBA,MAAA,gBAAAA,GAAG,SAAS,SAAM;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,oCAAmC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,gBAAe,OAAM,EAAC,SAAQ,MAAG,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,kBAAiB,CAAC,YAAY,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,IAAI,GAAE,EAAE,CAACE,GAAE,gBAAgB,CAAC,GAAE,EAAE,WAAU,uBAAsB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKD,IAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,YAAW,CAAC,gBAAe,qBAAoB,uBAAsB,0BAA0B,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAACC,GAAE,UAAU,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,kBAAiB,OAAM,EAAC,SAAQ,MAAG,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKD,IAAE,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,SAAQ,MAAG,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,yBAAwB,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,8BAA8B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACAhtE,IAAMI,KAAE,CAAAA,OAAG;AAAC,MAAI,IAAE,cAAcA,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,aAAW,MAAK,KAAK,aAAW,MAAK,KAAK,cAAY;AAAA,IAAE;AAAA,IAAC,WAAWC,IAAE,GAAE;AAAC,YAAMC,KAAE,EAAE,SAAS;AAAc,UAAG,CAACA,GAAE,QAAO;AAAK,YAAMC,KAAED,GAAE,YAAW,IAAED,GAAE,SAASC,GAAE,eAAe;AAAE,aAAOC,MAAG,IAAE,IAAIH,GAAE,EAAC,OAAMG,IAAE,MAAK,EAAC,CAAC,IAAE;AAAA,IAAI;AAAA,IAAC,IAAI,SAASF,IAAE;AAAC,QAAEA,IAAE,KAAK,WAAW,GAAE,KAAK,KAAK,YAAWA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKD,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,cAAa,CAAC,wBAAwB,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAK,MAAK,GAAE,MAAK,EAAC,OAAM,MAAG,SAAQ,EAAC,gBAAe,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,eAAc,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,EAAC,QAAO,gBAAe,GAAE,OAAM,EAAC,QAAO,gBAAe,GAAE,SAAQ,EAAC,aAAY,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAE,CAAC,GAAE;AAAC;", "names": ["e", "o", "l", "o", "e", "a", "e", "a", "r", "l", "i", "a", "e", "o", "i"]}