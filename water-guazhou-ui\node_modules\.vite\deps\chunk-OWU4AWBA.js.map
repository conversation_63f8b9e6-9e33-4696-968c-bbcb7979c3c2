{"version": 3, "sources": ["../../@arcgis/core/portal/support/layersLoader.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Error.js\";import t from\"../../layers/Layer.js\";import{isArcGISUrl as r}from\"../../layers/support/arcgisLayerUrl.js\";import{fetchFeatureService as a}from\"../../layers/support/fetchService.js\";import n from\"../Portal.js\";import o from\"../PortalItem.js\";import{createForItemRead as l}from\"./jsonContext.js\";import{hasTypeKeyword as s}from\"./portalItemUtils.js\";import{loadStyleRenderer as i}from\"../../renderers/support/styleUtils.js\";import{fetchArcGISServiceJSON as u}from\"../../support/requestPresets.js\";async function p(e,t){const r=e.instance.portalItem;if(r&&r.id)return await r.load(t),c(e),y(e,t)}function c(t){const r=t.instance.portalItem;if(!r?.type||!t.supportedTypes.includes(r.type))throw new e(\"portal:invalid-layer-item-type\",\"Invalid layer item type '${type}', expected '${expectedType}'\",{type:r?.type,expectedType:t.supportedTypes.join(\", \")})}async function y(e,t){const r=e.instance,a=r.portalItem;if(!a)return;const{url:n,title:o}=a,s=l(a);if(\"group\"===r.type)return r.read({title:o},s),d(r,e);n&&r.read({url:n},s);const u=await h(e,t);return u&&r.read(u,s),r.resourceReferences={portalItem:a,paths:s.readResourcePaths??[]},\"subtype-group\"!==r.type&&r.read({title:o},s),i(r,s)}async function d(t,r){let a;const{portalItem:n}=t;if(!n)return;const o=n.type,l=r.layerModuleTypeMap,i=s(n,\"Oriented Imagery Layer\")??!1;switch(o){case\"Feature Service\":a=i?l.OrientedImageryLayer:l.FeatureLayer;break;case\"Stream Service\":a=l.StreamLayer;break;case\"Scene Service\":a=l.SceneLayer;break;case\"Feature Collection\":a=l.FeatureLayer;break;default:throw new e(\"portal:unsupported-item-type-as-group\",`The item type '${o}' is not supported as a 'IGroupLayer'`)}let[u,p]=await Promise.all([a(),h(r)]),c=()=>u;if(\"Feature Service\"===o){p=n.url?await w(p,n.url):{};if(j(p).length){const e=l.SubtypeGroupLayer,t=await e();c=e=>\"SubtypeGroupLayer\"===e.layerType?t:u}return b(t,c,p,await P(n.url))}return v(p)>0?b(t,c,p):f(t,c)}async function f(e,t){const{portalItem:r}=e;if(!r?.url)return;const a=await u(r.url);a&&b(e,t,{layers:a.layers?.map(m),tables:a.tables?.map(m)})}function m(e){return{id:e.id,name:e.name}}function b(e,t,r,a){let n=r.layers||[];const o=r.tables||[];if(\"Feature Collection\"===e.portalItem?.type&&(n.forEach((e=>{\"Table\"===e?.layerDefinition?.type&&o.push(e)})),n=n.filter((e=>\"Table\"!==e?.layerDefinition?.type))),\"coverage\"in r){const t=T(r);t&&e.add(t)}n.reverse().forEach((n=>{const o=g(e,t(n),r,n,a?.(n));e.add(o)})),o.reverse().forEach((n=>{const o=g(e,t(n),r,n,a?.(n));e.tables.add(o)}))}function g(e,t,r,a,o){const l=e.portalItem,s=new t({portalItem:l.clone(),layerId:a.id});if(\"sourceJSON\"in s&&(s.sourceJSON=o),\"subtype-group\"!==s.type&&(s.sublayerTitleMode=\"service-name\"),\"Feature Collection\"===l.type){const e={origin:\"portal-item\",portal:l.portal||n.getDefault()};s.read(a,e);const t=r.showLegend;null!=t&&s.read({showLegend:t},e)}return s}async function h(e,t){if(!1===e.supportsData)return;const r=e.instance,a=r.portalItem;if(!a)return;let n=null;try{n=await a.fetchData(\"json\",t)}catch(o){}if(S(r)){let e=null,t=!0;if(n&&v(n)>0){if(null==r.layerId){const e=j(n);r.layerId=\"subtype-group\"===r.type?e?.[0]:I(n)}e=L(n,r),e&&(1===v(n)&&(t=!1),null!=n.showLegend&&(e.showLegend=n.showLegend))}return t&&\"service-name\"!==r.sublayerTitleMode&&(r.sublayerTitleMode=\"item-title-and-service-name\"),e}return n}async function w(e,t){if(null==e?.layers||null==e?.tables){const r=await u(t);(e=e||{}).layers=e.layers||r?.layers,e.tables=e.tables||r?.tables}return e}function I(e){const t=e.layers;if(t&&t.length)return t[0].id;const r=e.tables;return r&&r.length?r[0].id:null}function L(e,t){const{layerId:r}=t,a=e.layers?.find((e=>e.id===r))||e.tables?.find((e=>e.id===r));return a&&F(a,t)?a:null}function v(e){return(e?.layers?.length??0)+(e?.tables?.length??0)}function S(e){return\"stream\"!==e.type&&\"oriented-imagery\"!==e.type&&\"layerId\"in e}function T(a){const{coverage:n}=a;if(!n)return null;const l=new URL(n);if(n.toLowerCase().includes(\"item.html\")){const e=l.searchParams.get(\"id\"),r=l.origin;return t.fromPortalItem({portalItem:new o({id:e,url:r})})}if(r(n))return t.fromArcGISServerUrl({url:n});throw new e(\"portal:oriented-imagery-layer-coverage\",\"the provided coverage url couldn't be loaded as a layer\")}function j(e){const t=[];return e?.layers?.forEach((e=>{\"SubtypeGroupLayer\"===e.layerType&&t.push(e.id)})),t}function F(e,t){return!(\"feature\"===t.type&&\"layerType\"in e&&\"SubtypeGroupLayer\"===e.layerType||\"subtype-group\"===t.type&&!(\"layerType\"in e))}async function P(e){const{layersJSON:t}=await a(e);if(!t)return null;const r=[...t.layers,...t.tables];return e=>r.find((t=>t.id===e.id))}export{I as getFirstLayerOrTableId,v as getNumLayersAndTables,j as getSubtypeGroupLayerIds,p as load,w as preprocessFSItemData};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIkhB,eAAe,EAAEA,IAAEC,IAAE;AAAC,QAAMC,KAAEF,GAAE,SAAS;AAAW,MAAGE,MAAGA,GAAE,GAAG,QAAO,MAAMA,GAAE,KAAKD,EAAC,GAAE,EAAED,EAAC,GAAE,EAAEA,IAAEC,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAED,GAAE,SAAS;AAAW,MAAG,EAACC,MAAA,gBAAAA,GAAG,SAAM,CAACD,GAAE,eAAe,SAASC,GAAE,IAAI,EAAE,OAAM,IAAI,EAAE,kCAAiC,iEAAgE,EAAC,MAAKA,MAAA,gBAAAA,GAAG,MAAK,cAAaD,GAAE,eAAe,KAAK,IAAI,EAAC,CAAC;AAAC;AAAC,eAAe,EAAED,IAAEC,IAAE;AAAC,QAAMC,KAAEF,GAAE,UAAS,IAAEE,GAAE;AAAW,MAAG,CAAC,EAAE;AAAO,QAAK,EAAC,KAAI,GAAE,OAAM,EAAC,IAAE,GAAEC,KAAE,EAAE,CAAC;AAAE,MAAG,YAAUD,GAAE,KAAK,QAAOA,GAAE,KAAK,EAAC,OAAM,EAAC,GAAEC,EAAC,GAAE,EAAED,IAAEF,EAAC;AAAE,OAAGE,GAAE,KAAK,EAAC,KAAI,EAAC,GAAEC,EAAC;AAAE,QAAM,IAAE,MAAM,EAAEH,IAAEC,EAAC;AAAE,SAAO,KAAGC,GAAE,KAAK,GAAEC,EAAC,GAAED,GAAE,qBAAmB,EAAC,YAAW,GAAE,OAAMC,GAAE,qBAAmB,CAAC,EAAC,GAAE,oBAAkBD,GAAE,QAAMA,GAAE,KAAK,EAAC,OAAM,EAAC,GAAEC,EAAC,GAAEF,GAAEC,IAAEC,EAAC;AAAC;AAAC,eAAe,EAAEF,IAAEC,IAAE;AAAC,MAAI;AAAE,QAAK,EAAC,YAAW,EAAC,IAAED;AAAE,MAAG,CAAC,EAAE;AAAO,QAAM,IAAE,EAAE,MAAK,IAAEC,GAAE,oBAAmB,IAAEC,GAAE,GAAE,wBAAwB,KAAG;AAAG,UAAO,GAAE;AAAA,IAAC,KAAI;AAAkB,UAAE,IAAE,EAAE,uBAAqB,EAAE;AAAa;AAAA,IAAM,KAAI;AAAiB,UAAE,EAAE;AAAY;AAAA,IAAM,KAAI;AAAgB,UAAE,EAAE;AAAW;AAAA,IAAM,KAAI;AAAqB,UAAE,EAAE;AAAa;AAAA,IAAM;AAAQ,YAAM,IAAI,EAAE,yCAAwC,kBAAkB,CAAC,uCAAuC;AAAA,EAAC;AAAC,MAAG,CAAC,GAAEC,EAAC,IAAE,MAAM,QAAQ,IAAI,CAAC,EAAE,GAAE,EAAEF,EAAC,CAAC,CAAC,GAAEG,KAAE,MAAI;AAAE,MAAG,sBAAoB,GAAE;AAAC,IAAAD,KAAE,EAAE,MAAI,MAAM,EAAEA,IAAE,EAAE,GAAG,IAAE,CAAC;AAAE,QAAG,EAAEA,EAAC,EAAE,QAAO;AAAC,YAAMJ,KAAE,EAAE,mBAAkBC,KAAE,MAAMD,GAAE;AAAE,MAAAK,KAAE,CAAAL,OAAG,wBAAsBA,GAAE,YAAUC,KAAE;AAAA,IAAC;AAAC,WAAOK,GAAEL,IAAEI,IAAED,IAAE,MAAM,EAAE,EAAE,GAAG,CAAC;AAAA,EAAC;AAAC,SAAO,EAAEA,EAAC,IAAE,IAAEE,GAAEL,IAAEI,IAAED,EAAC,IAAE,EAAEH,IAAEI,EAAC;AAAC;AAAC,eAAe,EAAEL,IAAEC,IAAE;AAJr7D;AAIs7D,QAAK,EAAC,YAAWC,GAAC,IAAEF;AAAE,MAAG,EAACE,MAAA,gBAAAA,GAAG,KAAI;AAAO,QAAM,IAAE,MAAM,EAAEA,GAAE,GAAG;AAAE,OAAGI,GAAEN,IAAEC,IAAE,EAAC,SAAO,OAAE,WAAF,mBAAU,IAAIM,KAAG,SAAO,OAAE,WAAF,mBAAU,IAAIA,IAAE,CAAC;AAAC;AAAC,SAASA,GAAEP,IAAE;AAAC,SAAM,EAAC,IAAGA,GAAE,IAAG,MAAKA,GAAE,KAAI;AAAC;AAAC,SAASM,GAAEN,IAAEC,IAAEC,IAAE,GAAE;AAJ9mE;AAI+mE,MAAI,IAAEA,GAAE,UAAQ,CAAC;AAAE,QAAM,IAAEA,GAAE,UAAQ,CAAC;AAAE,MAAG,2BAAuB,KAAAF,GAAE,eAAF,mBAAc,UAAO,EAAE,QAAS,CAAAA,OAAG;AAJptE,QAAAQ;AAIqtE,kBAAUA,MAAAR,MAAA,gBAAAA,GAAG,oBAAH,gBAAAQ,IAAoB,SAAM,EAAE,KAAKR,EAAC;AAAA,EAAC,CAAE,GAAE,IAAE,EAAE,OAAQ,CAAAA,OAAC;AAJnxE,QAAAQ;AAIqxE,yBAAUA,MAAAR,MAAA,gBAAAA,GAAG,oBAAH,gBAAAQ,IAAoB;AAAA,GAAK,IAAG,cAAaN,IAAE;AAAC,UAAMD,KAAE,EAAEC,EAAC;AAAE,IAAAD,MAAGD,GAAE,IAAIC,EAAC;AAAA,EAAC;AAAC,IAAE,QAAQ,EAAE,QAAS,CAAAQ,OAAG;AAAC,UAAMC,KAAE,EAAEV,IAAEC,GAAEQ,EAAC,GAAEP,IAAEO,IAAE,uBAAIA,GAAE;AAAE,IAAAT,GAAE,IAAIU,EAAC;AAAA,EAAC,CAAE,GAAE,EAAE,QAAQ,EAAE,QAAS,CAAAD,OAAG;AAAC,UAAMC,KAAE,EAAEV,IAAEC,GAAEQ,EAAC,GAAEP,IAAEO,IAAE,uBAAIA,GAAE;AAAE,IAAAT,GAAE,OAAO,IAAIU,EAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEV,IAAEC,IAAEC,IAAE,GAAE,GAAE;AAAC,QAAM,IAAEF,GAAE,YAAWG,KAAE,IAAIF,GAAE,EAAC,YAAW,EAAE,MAAM,GAAE,SAAQ,EAAE,GAAE,CAAC;AAAE,MAAG,gBAAeE,OAAIA,GAAE,aAAW,IAAG,oBAAkBA,GAAE,SAAOA,GAAE,oBAAkB,iBAAgB,yBAAuB,EAAE,MAAK;AAAC,UAAMH,KAAE,EAAC,QAAO,eAAc,QAAO,EAAE,UAAQM,GAAE,WAAW,EAAC;AAAE,IAAAH,GAAE,KAAK,GAAEH,EAAC;AAAE,UAAMC,KAAEC,GAAE;AAAW,YAAMD,MAAGE,GAAE,KAAK,EAAC,YAAWF,GAAC,GAAED,EAAC;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,eAAe,EAAEH,IAAEC,IAAE;AAAC,MAAG,UAAKD,GAAE,aAAa;AAAO,QAAME,KAAEF,GAAE,UAAS,IAAEE,GAAE;AAAW,MAAG,CAAC,EAAE;AAAO,MAAI,IAAE;AAAK,MAAG;AAAC,QAAE,MAAM,EAAE,UAAU,QAAOD,EAAC;AAAA,EAAC,SAAO,GAAE;AAAA,EAAC;AAAC,MAAG,EAAEC,EAAC,GAAE;AAAC,QAAIF,KAAE,MAAKC,KAAE;AAAG,QAAG,KAAG,EAAE,CAAC,IAAE,GAAE;AAAC,UAAG,QAAMC,GAAE,SAAQ;AAAC,cAAMF,KAAE,EAAE,CAAC;AAAE,QAAAE,GAAE,UAAQ,oBAAkBA,GAAE,OAAKF,MAAA,gBAAAA,GAAI,KAAG,EAAE,CAAC;AAAA,MAAC;AAAC,MAAAA,KAAE,EAAE,GAAEE,EAAC,GAAEF,OAAI,MAAI,EAAE,CAAC,MAAIC,KAAE,QAAI,QAAM,EAAE,eAAaD,GAAE,aAAW,EAAE;AAAA,IAAY;AAAC,WAAOC,MAAG,mBAAiBC,GAAE,sBAAoBA,GAAE,oBAAkB,gCAA+BF;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,eAAe,EAAEA,IAAEC,IAAE;AAAC,MAAG,SAAMD,MAAA,gBAAAA,GAAG,WAAQ,SAAMA,MAAA,gBAAAA,GAAG,SAAO;AAAC,UAAME,KAAE,MAAM,EAAED,EAAC;AAAE,KAACD,KAAEA,MAAG,CAAC,GAAG,SAAOA,GAAE,WAAQE,MAAA,gBAAAA,GAAG,SAAOF,GAAE,SAAOA,GAAE,WAAQE,MAAA,gBAAAA,GAAG;AAAA,EAAM;AAAC,SAAOF;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAED,GAAE;AAAO,MAAGC,MAAGA,GAAE,OAAO,QAAOA,GAAE,CAAC,EAAE;AAAG,QAAMC,KAAEF,GAAE;AAAO,SAAOE,MAAGA,GAAE,SAAOA,GAAE,CAAC,EAAE,KAAG;AAAI;AAAC,SAAS,EAAEF,IAAEC,IAAE;AAJ3jH;AAI4jH,QAAK,EAAC,SAAQC,GAAC,IAAED,IAAE,MAAE,KAAAD,GAAE,WAAF,mBAAU,KAAM,CAAAA,OAAGA,GAAE,OAAKE,UAAK,KAAAF,GAAE,WAAF,mBAAU,KAAM,CAAAA,OAAGA,GAAE,OAAKE;AAAI,SAAO,KAAG,EAAE,GAAED,EAAC,IAAE,IAAE;AAAI;AAAC,SAAS,EAAED,IAAE;AAJnrH;AAIorH,YAAO,KAAAA,MAAA,gBAAAA,GAAG,WAAH,mBAAW,WAAQ,QAAI,KAAAA,MAAA,gBAAAA,GAAG,WAAH,mBAAW,WAAQ;AAAE;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAM,aAAWA,GAAE,QAAM,uBAAqBA,GAAE,QAAM,aAAYA;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,UAAS,EAAC,IAAE;AAAE,MAAG,CAAC,EAAE,QAAO;AAAK,QAAM,IAAE,IAAI,IAAI,CAAC;AAAE,MAAG,EAAE,YAAY,EAAE,SAAS,WAAW,GAAE;AAAC,UAAMA,KAAE,EAAE,aAAa,IAAI,IAAI,GAAEE,KAAE,EAAE;AAAO,WAAO,EAAE,eAAe,EAAC,YAAW,IAAI,EAAE,EAAC,IAAGF,IAAE,KAAIE,GAAC,CAAC,EAAC,CAAC;AAAA,EAAC;AAAC,MAAG,EAAE,CAAC,EAAE,QAAO,EAAE,oBAAoB,EAAC,KAAI,EAAC,CAAC;AAAE,QAAM,IAAI,EAAE,0CAAyC,yDAAyD;AAAC;AAAC,SAAS,EAAEF,IAAE;AAJ5rI;AAI6rI,QAAMC,KAAE,CAAC;AAAE,UAAO,KAAAD,MAAA,gBAAAA,GAAG,WAAH,mBAAW,QAAS,CAAAA,OAAG;AAAC,4BAAsBA,GAAE,aAAWC,GAAE,KAAKD,GAAE,EAAE;AAAA,EAAC,IAAIC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,SAAM,EAAE,cAAYA,GAAE,QAAM,eAAcD,MAAG,wBAAsBA,GAAE,aAAW,oBAAkBC,GAAE,QAAM,EAAE,eAAcD;AAAG;AAAC,eAAe,EAAEA,IAAE;AAAC,QAAK,EAAC,YAAWC,GAAC,IAAE,MAAM,EAAED,EAAC;AAAE,MAAG,CAACC,GAAE,QAAO;AAAK,QAAMC,KAAE,CAAC,GAAGD,GAAE,QAAO,GAAGA,GAAE,MAAM;AAAE,SAAO,CAAAD,OAAGE,GAAE,KAAM,CAAAD,OAAGA,GAAE,OAAKD,GAAE,EAAG;AAAC;", "names": ["e", "t", "r", "s", "p", "c", "b", "m", "_a", "n", "o"]}