{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/webgl/collisions/MetricReader.js", "../../@arcgis/core/views/2d/engine/webgl/FeatureDisplayList.js", "../../@arcgis/core/views/2d/engine/webgl/cpuMapped/Buffer.js", "../../@arcgis/core/views/2d/engine/webgl/cpuMapped/Geometry.js", "../../@arcgis/core/views/2d/engine/webgl/FeatureTile.js", "../../@arcgis/core/views/2d/layers/features/tileRenderers/support/WGLFeatureView.js", "../../@arcgis/core/views/2d/layers/features/tileRenderers/SymbolTileRenderer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,isNone as s}from\"../../../../../core/maybe.js\";const e=6,r=4294967296;class o{constructor(t){this._savedCursor=null,this._savedOffset=null,this._head=t,this._cursor=t}static from(t){const s=i.from(new Float32Array(t));return new o(s)}get id(){return this._cursor.id}get baseZoom(){return this._cursor.baseZoom}get anchorX(){return this._cursor.anchorX}get anchorY(){return this._cursor.anchorY}get directionX(){return this._cursor.directionX}get directionY(){return this._cursor.directionY}get size(){return this._cursor.size}get materialKey(){return this._cursor.materialKey}get boundsCount(){return this._cursor.boundsCount}computedMinZoom(){return this._cursor.computedMinZoom()}setComputedMinZoom(t){return this._cursor.setComputedMinZoom(t)}boundsComputedAnchorX(t){return this._cursor.boundsComputedAnchorX(t)}boundsComputedAnchorY(t){return this._cursor.boundsComputedAnchorY(t)}setBoundsComputedAnchorX(t,s){return this._cursor.setBoundsComputedAnchorX(t,s)}setBoundsComputedAnchorY(t,s){return this._cursor.setBoundsComputedAnchorY(t,s)}boundsX(t){return this._cursor.boundsX(t)}boundsY(t){return this._cursor.boundsY(t)}boundsWidth(t){return this._cursor.boundsWidth(t)}boundsHeight(t){return this._cursor.boundsHeight(t)}link(s){if(t(s._head))return this._cursor.link(s._head)}getCursor(){return this.copy()}copy(){const t=new o(this._head?.copy());if(!t._head)return t;let s=t._head,e=t._head._link;for(;e;)s._link=e.copy(),s=e,e=s._link;return t}peekId(){return this._cursor.peekId()??this._cursor._link.peekId()}nextId(){const t=this.id;for(;t===this.id;)if(!this.next())return!1;return!0}save(){this._savedCursor=this._cursor,this._savedOffset=this._cursor._offset}restore(){this._savedCursor&&(this._cursor=this._savedCursor),null!=this._savedOffset&&(this._cursor._offset=this._savedOffset)}next(){if(!this._cursor)return!1;if(!this._cursor.next()){if(!this._cursor._link)return!1;this._cursor=this._cursor._link,this._cursor._offset=0}return!0}lookup(t){for(this._cursor=this._head;this._cursor&&!this._cursor.lookup(t);){if(!this._cursor._link)return!1;this._cursor=this._cursor._link}return!!this._cursor}delete(s){let e=this._head,r=null;for(;e;){if(e.delete(s))return e.isEmpty()&&t(r)&&(r._link=e._link),!0;r=e,e=e._link}return!1}}class i{constructor(t){this._offset=-1,this._link=null,this._count=0,this._deletedCount=0,this._offsets={instance:null},this._buffer=t}static from(t){return new i(new Float32Array(t))}isEmpty(){return this._deletedCount===this.count}get count(){return this._count||(this._count=this._computeCount()),this._count}get id(){return this._buffer[this._offset+0]}set id(t){this._buffer[this._offset+0]=t}get baseZoom(){return this._buffer[this._offset+1]}get anchorX(){return this._buffer[this._offset+2]}get anchorY(){return this._buffer[this._offset+3]}get directionX(){return this._buffer[this._offset+4]}get directionY(){return this._buffer[this._offset+5]}get size(){return this._buffer[this._offset+6]}get materialKey(){return this._buffer[this._offset+7]}computedMinZoom(){return this._buffer[this._offset+8]}setComputedMinZoom(t){this._buffer[this._offset+8]=t}get boundsCount(){return this._buffer[this._offset+9]}boundsComputedAnchorX(t){return this._buffer[this._offset+10+t*e+0]}boundsComputedAnchorY(t){return this._buffer[this._offset+10+t*e+1]}setBoundsComputedAnchorX(t,s){this._buffer[this._offset+10+t*e+0]=s}setBoundsComputedAnchorY(t,s){this._buffer[this._offset+10+t*e+1]=s}boundsX(t){return this._buffer[this._offset+10+t*e+2]}boundsY(t){return this._buffer[this._offset+10+t*e+3]}boundsWidth(t){return this._buffer[this._offset+10+t*e+4]}boundsHeight(t){return this._buffer[this._offset+10+t*e+5]}link(t){let s=this;for(;s._link;)s=s._link;s._link=t}getCursor(){return this.copy()}copy(){const t=new i(this._buffer);return t._link=this._link,t._offset=this._offset,t._deletedCount=this._deletedCount,t._offsets=this._offsets,t._count=this._count,t}peekId(){const t=this._offset+10+this.boundsCount*e+0;return t>=this._buffer.length?0:this._buffer[t]}next(){let t=0;for(;this._offset<this._buffer.length&&t++<100&&(-1===this._offset?this._offset=0:this._offset+=10+this.boundsCount*e,this.id===r););return this.id!==r&&this._offset<this._buffer.length}delete(t){const s=this._offset,e=this.lookup(t);if(e)for(this.id=4294967295,++this._deletedCount;this.next()&&this.id===t;)this.id=4294967295,++this._deletedCount;return this._offset=s,e}lookup(t){const e=this._offset;if(s(this._offsets.instance)){this._offsets.instance=new Map;const t=this.copy();t._offset=-1;let s=0;for(;t.next();)t.id!==s&&(this._offsets.instance.set(t.id,t._offset),s=t.id)}return!!this._offsets.instance.has(t)&&(this._offset=this._offsets.instance.get(t),this.id!==r||(this._offset=e,!1))}_computeCount(){const t=this._offset;let s=0;for(this._offset=-1;this.next();)s++;return this._offset=t,s}}export{o as MetricReader,i as MetricReaderNode};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,isNone as e}from\"../../../../core/maybe.js\";import{List as i}from\"./cpuMapped/FreeList.js\";class n{constructor(t,e,i,n,a){this.target=t,this.geometryType=e,this.materialKey=i,this.indexFrom=n,this.indexCount=a}get indexEnd(){return this.indexFrom+this.indexCount}extend(t){this.indexCount+=t}}class a{constructor(t,e){this.geometryType=0,this._target=t,this.geometryType=e}static from(e,i,n,d){const r=new a(e,i);if(t(d))for(const t of d)n.seekIndex(t),r.addRecord(n);else for(;n.next();)r.addRecord(n);return r}addRecord(t){const a=this._target,d=this.geometryType,r=t.materialKey;let s=t.indexFrom,o=t.indexCount;const h=t.vertexFrom,x=t.vertexCount;if(o||(s=h,o=x),e(this._head)){const t=new n(a,d,r,s,o);return void(this._head=new i(t))}let m=null,c=this._head;for(;c;){if(s<c.data.indexFrom)return this._insert(r,s,o,m,c);m=c,c=c.next}this._insert(r,s,o,m,null)}forEach(e){t(this._head)&&this._head.forEach(e)}*infos(){if(t(this._head))for(const t of this._head.values())yield t}_insert(a,d,r,s,o){if(e(s)&&e(o)){const t=new n(this._target,this.geometryType,a,d,r);this._head=new i(t)}return e(s)&&t(o)?this._insertAtHead(a,d,r,o):t(s)&&e(o)?this._insertAtEnd(a,d,r,s):t(s)&&t(o)?this._insertAtMiddle(a,d,r,s,o):void 0}_insertAtHead(t,e,a,d){const r=e+a;if(t===d.data.materialKey&&r===d.data.indexFrom)d.data.indexFrom=e,d.data.indexCount+=a;else{const r=new n(this._target,this.geometryType,t,e,a);this._head=new i(r),this._head.next=d}}_insertAtEnd(t,e,a,d){if(d.data.materialKey===t&&d.data.indexEnd===e)d.data.indexCount+=a;else{const r=new n(this._target,this.geometryType,t,e,a),s=new i(r);d.next=s}}_insertAtMiddle(t,e,a,d,r){const s=e+a;if(d.data.materialKey===t&&d.data.indexEnd===e)d.data.indexCount+=a,d.data.materialKey===r.data.materialKey&&d.data.indexEnd===r.data.indexFrom&&(d.data.indexCount+=r.data.indexCount,d.next=r.next);else if(t===r.data.materialKey&&s===r.data.indexFrom)r.data.indexFrom=e,r.data.indexCount+=a;else{const s=new n(this._target,this.geometryType,t,e,a),o=new i(s);d.next=o,o.next=r}}}export{a as FeatureDisplayList,n as FeatureDisplayListInfo};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{applySome as t,isNone as i,unwrapOrThrow as e}from\"../../../../../core/maybe.js\";import{TypedBuffer as r}from\"../BufferPool.js\";import{FreeList as s}from\"./FreeList.js\";import{BufferObject as h}from\"../../../../webgl/BufferObject.js\";import{Usage as n}from\"../../../../webgl/enums.js\";const a=1.25,u=32767,d=u<<16|u;class f{constructor(t,i,e,s){const h=r.create(i*e*Uint32Array.BYTES_PER_ELEMENT,s);this.size=i,this.strideInt=e,this.bufferType=t,this.dirty={start:1/0,end:0},this._gpu=null,this._cpu=h,this.clear()}get elementSize(){return this._cpu.length/this.strideInt}get invalidated(){return this.bufferSize>0&&!this._gpu}get invalidatedComputeBuffer(){return this.bufferSize>0&&!this._gpuComputeTriangles}invalidate(){this._invalidateTriangleBuffer(),t(this._gpu,(t=>t.dispose())),this._gpu=null}_invalidateTriangleBuffer(){t(this._gpuComputeTriangles,(t=>t.dispose())),this._gpuComputeTriangles=null}destroy(){t(this._gpu,(t=>t.dispose())),t(this._gpuComputeTriangles,(t=>t.dispose())),t(this._cpu,(t=>t.destroy())),t(this._cpu2,(t=>t.destroy()))}clear(){this.dirty.start=1/0,this.dirty.end=0,this.freeList=new s({start:0,end:this._cpu.length/this.strideInt}),this.fillPointer=0}ensure(t){if(this.maxAvailableSpace()>=t)return;if(t*this.strideInt>this._cpu.length-this.fillPointer){this.invalidate();const i=this._cpu.length/this.strideInt,e=Math.round((i+t)*a),r=e*this.strideInt;this._cpu.expand(r*Uint32Array.BYTES_PER_ELEMENT),this.freeList.free(i,e-i)}}set(t,i){this._cpu.array[t]!==i&&(this._cpu.array[t]=i,this.dirty.start=Math.min(t,this.dirty.start),this.dirty.end=Math.max(t,this.dirty.end))}getGPUBuffer(t,e=!1){if(!this.bufferSize)return null;if(e){if(\"index\"!==this.bufferType)throw new Error(\"Tired to get triangle buffer, but target is not an index buffer\");return i(this._gpuComputeTriangles)&&(this._gpuComputeTriangles=this._createComputeBuffer(t)),this._gpuComputeTriangles}return i(this._gpu)&&(this._gpu=this._createBuffer(t)),this._gpu}getCPUBuffer(){if(!this._cpu2){const t=this._cpu.slice();this._cpu2=t}return this._cpu2.length!==this._cpu.length&&this._cpu2.expand(this._cpu.length*this._cpu.array.BYTES_PER_ELEMENT),this._cpu2.set(this._cpu),this._cpu2}get bufferSize(){return this._cpu.length/this.strideInt}maxAvailableSpace(){return this.freeList.maxAvailableSpace()}insert(t,i,r,s){const h=r*this.strideInt;if(!h)return 0;const n=i*this.strideInt*Uint32Array.BYTES_PER_ELEMENT,a=new Uint32Array(t,n,h),u=e(this.freeList.firstFit(r),\"First fit region must be defined\")*this.strideInt,d=h,f=u/this.strideInt-i;if(0!==s)for(let e=0;e<a.length;e++)a[e]+=s;return this._cpu.array.set(a,u),this.dirty.start=Math.min(this.dirty.start,u),this.dirty.end=Math.max(this.dirty.end,u+d),this.fillPointer=Math.max(this.fillPointer,u+d),f}free(t,i,e){const r=t*this.strideInt,s=(t+i)*this.strideInt;if(!0===e)for(let h=t;h!==t+i;h++)this._cpu.array[h*this.strideInt]=d;this.dirty.start=Math.min(this.dirty.start,r),this.dirty.end=Math.max(this.dirty.end,s),this.freeList.free(t,i)}upload(){if(this.dirty.end){if(this._invalidateTriangleBuffer(),i(this._gpu))return this.dirty.start=1/0,void(this.dirty.end=0);this._gpu.setSubData(this._cpu.array,this.dirty.start,this.dirty.start,this.dirty.end),this.dirty.start=1/0,this.dirty.end=0}}_createBuffer(t){const i=n.DYNAMIC_DRAW;return\"index\"===this.bufferType?h.createIndex(t,i,this._cpu.array):h.createVertex(t,i,this._cpu.array)}_createComputeBuffer(t){const i=n.DYNAMIC_DRAW,e=new Uint32Array(this.fillPointer/3);for(let r=0;r<this.fillPointer;r+=3)e[r/3]=this._cpu.array[r];return h.createIndex(t,i,e)}}export{f as Buffer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e,destroyMaybe as t,unwrapOrThrow as r,isNone as s,applySome as i}from\"../../../../../core/maybe.js\";import{FeatureDisplayList as f}from\"../FeatureDisplayList.js\";import{Buffer as n}from\"./Buffer.js\";import{DisplayRecordReader as o}from\"./DisplayRecordReader.js\";import{VertexArrayObject as d}from\"../../../../webgl/VertexArrayObject.js\";const h=0,u=1;class c{constructor(e,t){this._vaos=new Map,this._indicesInvalid=!1,this.geometryType=e,this._stage=t}destroy(){for(const[t,r]of this._vaos)e(r)&&r.dispose(!1);this._indexBuffer=t(this._indexBuffer),this._vertexBuffer=t(this._vertexBuffer)}insert(e,t,s){if(!e.records.byteLength)return;const i=e.stride;if(this._vertexBuffer&&this._indexBuffer){const s=e.indices.byteLength/4,f=e.vertices.byteLength/i;this._indexBuffer.ensure(s),this._vertexBuffer.ensure(f);const{vertices:n,indices:d}=e,h=o.from(e.records),u=this._vertexBuffer.insert(n,0,n.byteLength/i,0),c=this._indexBuffer.insert(d,0,d.byteLength/4,u);if(h.forEach((e=>{e.indexFrom+=c,e.vertexFrom+=u})),r(this._records,\"Expected records to be defined\").link(h),t)this._indicesInvalid=!0;else if(this._displayList){const e=h.getCursor();for(;e.next();)this._displayList.addRecord(e)}}else{const r=e.indices.byteLength/4,s=e.vertices.byteLength/i,f=i/Uint32Array.BYTES_PER_ELEMENT,d=this._stage.bufferPool;this._records=o.from(e.records),this._indexBuffer=new n(\"index\",r,1,d),this._vertexBuffer=new n(\"vertex\",s,f,d),this._indexBuffer.insert(e.indices,0,e.indices.byteLength/4,0),this._vertexBuffer.insert(e.vertices,0,e.vertices.byteLength/i,0),t&&(this._indicesInvalid=!0)}}remove(e){if(!s(this._records))for(const t of e){const e=this._records.getCursor();if(!e.lookup(t))continue;const r=e.indexFrom,s=e.vertexFrom;let i=e.indexCount,f=e.vertexCount;for(;e.next()&&e.id===t;)i+=e.indexCount,f+=e.vertexCount;this._indexBuffer.free(r,i),this._vertexBuffer.free(s,f,!0),this._records.delete(t)}}getVAO(e,t,r,f){if(!this._vertexBuffer||!this._indexBuffer||s(this._records)||!this._vertexBuffer.bufferSize)return null;const n=f?u:h;let o=this._vaos.get(n);(this._vertexBuffer.invalidated||this._indexBuffer.invalidated||f&&this._indexBuffer.invalidatedComputeBuffer)&&(i(o,(e=>e.dispose(!1))),o=null),this._vertexBuffer.upload(),this._indexBuffer.upload();const c=this._indexBuffer.getGPUBuffer(e,1===n),_=this._vertexBuffer.getGPUBuffer(e);return o||(o=new d(e,r,t,{geometry:_},c),this._vaos.set(n,o)),o}forEachCommand(e){if(!s(this._records)){if(this._sortIndices(this._records),!this._displayList){const e=this._cursorIndexOrder;this._displayList=f.from(this,this.geometryType,this._records.getCursor(),e)}this._displayList.forEach(e)}}_sortIndices(e){const t=!!this._indexBuffer.bufferSize;if(!this._indicesInvalid)return;this._indicesInvalid=!1;let r=0;const s=e.getCursor(),i=[],f=[],n=[];for(;s.next();)f.push(s.index),n.push(s.sortKey),i.push(s.id);f.sort(((e,t)=>{const r=n[t],s=n[e];return s===r?i[t]-i[e]:r-s}));const o=e.getCursor(),d=t?this._indexBuffer.getCPUBuffer():this._vertexBuffer.getCPUBuffer();for(const h of f){if(!o.seekIndex(h))throw new Error(\"Expected to find index\");if(t){const{indexFrom:e,indexCount:t}=o;o.indexFrom=r;for(let s=0;s<t;s++)this._indexBuffer.set(r++,d.array[e+s])}else{const{vertexFrom:e,vertexCount:t}=o,s=this._vertexBuffer.strideInt,i=e*s,f=i+t*s;o.vertexFrom=r/s;for(let n=i;n<f;n++)this._vertexBuffer.set(r++,d.array[n])}}this._cursorIndexOrder=f,this._displayList=null}}export{c as Geometry};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/CircularArray.js\";import has from\"../../../../core/has.js\";import{isSome as t,unwrapOr as r,isNone as s}from\"../../../../core/maybe.js\";import{c as i}from\"../../../../chunks/mat2df32.js\";import{WGLGeometryType as a}from\"./enums.js\";import{forEachGeometryType as n}from\"./Utils.js\";import{WGLTile as h}from\"./WGLTile.js\";import{MetricReader as d}from\"./collisions/MetricReader.js\";import{Geometry as o}from\"./cpuMapped/Geometry.js\";const c=50,u=4,_=100;let p=0;class l extends h{constructor(t,r,s,a,n,h){super(t,r,s,a),this.instanceId=p++,this.patchCount=0,this._renderState={current:{geometry:new Map,metrics:null},next:null,swap:!1,swapFrames:0,locked:!1},this._patches=new e(_),this._bufferPatches=new e(_),this._lastCommitTime=0,this.transforms.labelMat2d=i(),this._store=n,this._requestLabelUpdate=h}destroy(){super.destroy(),this._renderState.current.geometry.forEach((e=>e.destroy())),t(this._renderState.next)&&this._renderState.next.geometry.forEach((e=>e.destroy())),this._renderState.current=null,this._renderState.next=null}get labelMetrics(){return this._renderState.current.metrics}get hasData(){return!!this._renderState.current.geometry.size}getGeometry(e){return this._renderState.current.geometry.get(e)}patch(e,t){this.patchCount++,e.clear&&this._patches.size>=c&&this._dropPatches();const r=e,s=r.addOrUpdate&&this.key.id!==r.addOrUpdate.tileKeyOrigin;t&&s?this._bufferPatches.enqueue(r):(r.sort=r.sort&&!t,this._patches.enqueue(r)),this.requestRender()}commit(e){if(this._lastCommitTime!==e.time){this._lastCommitTime=e.time;for(let e=0;e<u;e++)this._updateMesh(),this.isReady&&this._updateBufferMesh();this._renderState.swap&&(this._swapRenderStates(),this.requestRender())}}lock(){this._renderState.locked=!0}unlock(){this._renderState.locked=!1,this._flushUpdates(),this._swap()}_swapRenderStates(){if(this._renderState.next){if(this._renderState.locked)return this._renderState.swap=!0,void this.requestRender();this._renderState.swap=!0,this._swap()}}_swap(){this._renderState.swap&&(this._renderState.swap=!1,t(this._renderState.next)&&(this._renderState.current.geometry.forEach((e=>e.destroy())),this._renderState.current=this._renderState.next,this._renderState.next=null,this._requestLabelUpdate()))}_flushUpdates(){let e=this._patches.maxSize;for(;this._patches.size&&e--;)this._updateMesh(),this._swap()}_updateBufferMesh(){const e=this._bufferPatches.peek();if(!t(e)||!e.clear||null===this._renderState.next)for(;this._bufferPatches.size;){const e=this._bufferPatches.dequeue();t(e)&&this._patchBuffer(e)}}_updateMesh(){const e=this._patches.dequeue();if(t(e)){if(has(\"esri-2d-update-debug\")){const t=e,r=t.addOrUpdate?.tileKeyOrigin,s=this.key.id===r?\"SELF\":r;let i=\"\";for(let e=0;e<5;e++)i+=t.addOrUpdate?.data[e]?.records?.byteLength?1:0;console.debug(this.key.id,\"FeatureTile:patch\",`[clear: ${t.clear} origin: ${s}, end:${t.end} data:${i}]`)}!0===e.clear&&(t(this._renderState.next)&&(this._renderState.next.geometry.forEach((e=>e.destroy())),this._renderState.next=null),this._renderState.next={geometry:new Map,metrics:null},has(\"esri-2d-update-debug\")&&console.debug(this.key.id,\"FeatureTile:_updateMesh - Creating new renderState\")),this.requestRender(),this._patch(e),e.end&&(has(\"esri-2d-update-debug\")&&console.debug(this.key.id,\"FeatureTile:_updateMesh - Encountered end message\"),this.ready(),this._swapRenderStates())}}_patch(e){n((t=>{this._remove(t,e.remove),this._insert(t,e,!1)}))}_patchBuffer(e){n((t=>{this._insert(t,e,!0)}))}_insert(e,t,i){try{const n=r(this._renderState.next,this._renderState.current),h=t.addOrUpdate?.data[e],d=n.geometry;if(s(h))return;d.has(e)||(has(\"esri-2d-update-debug\")&&console.debug(this.key.id,`FeatureTile:_insert - Creating geometry buffer ${e}`),d.set(e,new o(e,this.stage))),has(\"esri-2d-update-debug\")&&console.debug(this.key.id,`FeatureTile:_insert - Inserting into ${e}, version=${t.addOrUpdate?.version} stride=${h.stride}`),d.get(e).insert(h,t.sort,i),e===a.LABEL&&this._insertLabelMetrics(t.type,h.metrics,t.clear)}catch(n){}}_insertLabelMetrics(e,t,i){const a=r(this._renderState.next,this._renderState.current);if(s(t))return;const n=d.from(t);if(s(a.metrics))a.metrics=n;else{if(\"update\"===e){const e=n.getCursor();for(;e.next();)a.metrics.delete(e.id)}a.metrics.link(n)}}_remove(e,t){const s=r(this._renderState.next,this._renderState.current).geometry.get(e);t&&t.length&&s&&(s.remove(t),this._removeLabelMetrics(t))}_removeLabelMetrics(e){const{metrics:t}=r(this._renderState.next,this._renderState.current);if(!s(t)&&e.length)for(const r of e)for(;t.delete(r););}_dropPatches(){const e=new Array;let t=!1;for(;this._patches.size;){const r=this._patches.dequeue();if(s(r))break;if(r.clear){if(t)break;t=!0}e.push(r)}this._patches.clear(),e.forEach((e=>this._patches.enqueue(e)))}}export{l as FeatureTile};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../../../core/has.js\";import{createResolver as e}from\"../../../../../../core/promiseUtils.js\";import{brushes as t}from\"../../../../engine/brushes.js\";import{FeatureContainer as s}from\"../../../../engine/FeatureContainer.js\";import{WGLDrawPhase as i}from\"../../../../engine/webgl/enums.js\";const r=has(\"featurelayer-order-by-server-enabled\");class a extends s{constructor(e,t,s,i){super(e),this._hitTestsRequests=[],this._layer=s,this._layerView=t,this._onUpdate=i}renderChildren(e){if(this.attributeView.update(),this.hasAnimation){e.painter.effects.integrate.draw(e,e.attributeView)}super.renderChildren(e)}hasEmptyAttributeView(){return this.attributeView.isEmpty()}isUpdating(){return this.attributeView.isUpdating()}hitTest(t){let s=this._hitTestsRequests.find((({x:e,y:s})=>e===t.x&&s===t.y));const i=e();return s?s.resolvers.push(i):(s={x:t.x,y:t.y,resolvers:[i]},this._hitTestsRequests.push(s)),this.requestRender(),i.promise}onTileData(e,t){const s=r&&\"orderBy\"in this._layer&&this._layer.orderBy,i=s&&s?.length&&!s[0].valueExpression&&s[0].field,a=!!s&&this._layerView.orderByFields===i;e.patch(t,a),this.contains(e)||this.addChild(e),this.requestRender()}onTileError(e){this.contains(e)||this.addChild(e)}updateTransitionProperties(e,t){super.updateTransitionProperties(e,t),this._layerView.featureEffectView.transitionStep(e,t),this._layerView.featureEffectView.transitioning&&this.requestRender()}doRender(e){const{minScale:t,maxScale:s}=this._layer.effectiveScaleRange,i=e.state.scale;i<=(t||1/0)&&i>=s&&super.doRender(e)}afterRender(e){super.afterRender(e),this._hitTestsRequests.length&&this.requestRender()}onAttributeStoreUpdate(){this.hasLabels&&this._layerView.view.labelManager.requestUpdate(),this._onUpdate()}get hasAnimation(){return this.hasLabels}setStencilReference(e){const{rendererSchema:t}=e.rendererInfo;if(\"dot-density\"===t?.type&&t?.dotSize>1||\"heatmap\"===t?.type){const e=1;for(const t of this.children)t.stencilRef=t.key.level+e}else super.setStencilReference(e)}get hasLabels(){if(\"sublayers\"in this._layer)return this._layer.sublayers.some((e=>!!e.labelingInfo?.length&&e.labelsVisible));const e=this._layer.featureReduction,t=e&&\"labelingInfo\"in e&&e.labelsVisible&&e.labelingInfo&&e.labelingInfo.length;return this._layer.labelingInfo&&this._layer.labelingInfo.length&&this._layer.labelsVisible||!!t}prepareRenderPasses(e){const s=e.registerRenderPass({name:\"label\",brushes:[t.label],target:()=>this.hasLabels?this.children:null,drawPhase:i.LABEL|i.LABEL_ALPHA}),r=e.registerRenderPass({name:\"geometry\",brushes:[t.fill,t.dotDensity,t.line,t.marker,t.heatmap,t.pieChart,t.text],target:()=>this.children,enableDefaultDraw:()=>!this._layerView.featureEffectView.hasEffects,effects:[{apply:e.effects.outsideEffect,enable:()=>this._layerView.featureEffectView.hasEffects,args:()=>this._layerView.featureEffectView.excludedEffects},{apply:e.effects.insideEffect,enable:()=>this._layerView.featureEffectView.hasEffects,args:()=>this._layerView.featureEffectView.includedEffects},{apply:e.effects.hittest,enable:()=>!!this._hitTestsRequests.length,args:()=>this._hitTestsRequests}]}),a=e.registerRenderPass({name:\"highlight\",brushes:[t.fill,t.dotDensity,t.line,t.marker,t.pieChart,t.text],target:()=>this.children,drawPhase:i.HIGHLIGHT,enableDefaultDraw:()=>!1,effects:[{apply:e.effects.highlight,enable:()=>!!this._layerView.hasHighlight()}]});return[...super.prepareRenderPasses(e),r,a,s]}}export{a as WGLFeatureView};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import has from\"../../../../../core/has.js\";import{destroyMaybe as t}from\"../../../../../core/maybe.js\";import{isAbortError as i}from\"../../../../../core/promiseUtils.js\";import\"../../../../../core/Logger.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import\"../../../../../core/Error.js\";import{subclass as s}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{create as r}from\"../../../../../geometry/support/aaBoundingRect.js\";import{FeatureTile as a}from\"../../../engine/webgl/FeatureTile.js\";import{simplifyVVRenderer as o}from\"../support/rendererUtils.js\";import l from\"./BaseTileRenderer.js\";import{convertVisualVariables as n}from\"./support/visualVariablesUtils.js\";import{WGLFeatureView as u}from\"./support/WGLFeatureView.js\";let p=class extends l{constructor(){super(...arguments),this.type=\"symbol\"}install(e){const t=()=>this.notifyChange(\"updating\"),i=new u(this.tileInfoView,this.layerView,this.layer,t);this.featuresView=i,e.addChild(i)}uninstall(e){e.removeChild(this.featuresView),this.featuresView=t(this.featuresView)}fetchResource(e,t){const{url:s}=e,r=this.featuresView.stage;try{return r.resourceManager.fetchResource(s,{signal:t.signal})}catch(a){return i(a)?Promise.resolve({width:0,height:0}):Promise.reject(a)}}isUpdating(){const e=super.isUpdating(),t=!this.featuresView||this.featuresView.isUpdating(),i=this.featuresView?.hasEmptyAttributeView(),s=e||t||e&&i;return has(\"esri-2d-log-updating\")&&console.log(`Updating SymbolTileRenderer ${s}\\n  -> updatingTiles ${e}\\n  -> hasFeaturesView ${!!this.featuresView}\\n  -> updatingFeaturesView ${t}`),s}hitTest(e){return this.featuresView.hitTest(e)}supportsRenderer(e){return null!=e&&[\"simple\",\"class-breaks\",\"unique-value\",\"dot-density\",\"dictionary\",\"heatmap\",\"pie-chart\"].includes(e.type)}onConfigUpdate(e){let t=null;if(e&&\"visualVariables\"in e){const i=(o(e).visualVariables||[]).map((e=>{const t=e.clone();return\"normalizationField\"in e&&(t.normalizationField=null),e.valueExpression&&\"$view.scale\"!==e.valueExpression&&(t.valueExpression=null,t.field=\"nop\"),t}));t=n(i)}this.featuresView.setRendererInfo(e,t,this.layerView.featureEffect)}onTileData(e){const t=this.tiles.get(e.tileKey);t&&e.data&&this.featuresView.onTileData(t,e.data),this.layerView.view.labelManager.requestUpdate()}onTileError(e){const t=this.tiles.get(e.tileKey);t&&this.featuresView.onTileError(t)}forceAttributeTextureUpload(){this.featuresView.attributeView.forceTextureUpload()}lockGPUUploads(){this.featuresView.attributeView.lockTextureUpload(),this.tiles.forEach((e=>e.lock()))}unlockGPUUploads(){this.featuresView.attributeView.unlockTextureUpload(),this.tiles.forEach((e=>e.unlock()))}async getMaterialItems(e){return this.featuresView.getMaterialItems(e)}invalidateLabels(){this.featuresView.hasLabels&&this.layerView.view.labelManager.requestUpdate()}createTile(e){const t=this.tileInfoView.getTileBounds(r(),e),i=()=>this.layerView.view.labelManager.requestUpdate(),s=this.tileInfoView.getTileResolution(e.level),o=this.featuresView.attributeView;return new a(e,s,t[0],t[3],o,i)}disposeTile(e){this.featuresView.removeChild(e),e.destroy(),this.layerView.view.labelManager.requestUpdate()}};p=e([s(\"esri.views.2d.layers.features.tileRenderers.SymbolTileRenderer\")],p);const h=p;export{h as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIkE,IAAMA,KAAE;AAAR,IAAUC,KAAE;AAAW,IAAMC,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,eAAa,MAAK,KAAK,eAAa,MAAK,KAAK,QAAMA,IAAE,KAAK,UAAQA;AAAA,EAAC;AAAA,EAAC,OAAO,KAAKA,IAAE;AAAC,UAAMC,KAAEC,GAAE,KAAK,IAAI,aAAaF,EAAC,CAAC;AAAE,WAAO,IAAI,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,KAAI;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAE;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAQ;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAO;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAO;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAU;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAU;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAW;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAW;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK,QAAQ,gBAAgB;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAE;AAAC,WAAO,KAAK,QAAQ,mBAAmBA,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,WAAO,KAAK,QAAQ,sBAAsBA,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,WAAO,KAAK,QAAQ,sBAAsBA,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEC,IAAE;AAAC,WAAO,KAAK,QAAQ,yBAAyBD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBD,IAAEC,IAAE;AAAC,WAAO,KAAK,QAAQ,yBAAyBD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQD,IAAE;AAAC,WAAO,KAAK,QAAQ,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,WAAO,KAAK,QAAQ,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAO,KAAK,QAAQ,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,WAAO,KAAK,QAAQ,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAE;AAAC,QAAG,EAAEA,GAAE,KAAK,EAAE,QAAO,KAAK,QAAQ,KAAKA,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,OAAM;AAJh0C;AAIi0C,UAAMD,KAAE,IAAI,IAAE,UAAK,UAAL,mBAAY,MAAM;AAAE,QAAG,CAACA,GAAE,MAAM,QAAOA;AAAE,QAAIC,KAAED,GAAE,OAAMH,KAAEG,GAAE,MAAM;AAAM,WAAKH,KAAG,CAAAI,GAAE,QAAMJ,GAAE,KAAK,GAAEI,KAAEJ,IAAEA,KAAEI,GAAE;AAAM,WAAOD;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK,QAAQ,OAAO,KAAG,KAAK,QAAQ,MAAM,OAAO;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMA,KAAE,KAAK;AAAG,WAAKA,OAAI,KAAK,KAAI,KAAG,CAAC,KAAK,KAAK,EAAE,QAAM;AAAG,WAAM;AAAA,EAAE;AAAA,EAAC,OAAM;AAAC,SAAK,eAAa,KAAK,SAAQ,KAAK,eAAa,KAAK,QAAQ;AAAA,EAAO;AAAA,EAAC,UAAS;AAAC,SAAK,iBAAe,KAAK,UAAQ,KAAK,eAAc,QAAM,KAAK,iBAAe,KAAK,QAAQ,UAAQ,KAAK;AAAA,EAAa;AAAA,EAAC,OAAM;AAAC,QAAG,CAAC,KAAK,QAAQ,QAAM;AAAG,QAAG,CAAC,KAAK,QAAQ,KAAK,GAAE;AAAC,UAAG,CAAC,KAAK,QAAQ,MAAM,QAAM;AAAG,WAAK,UAAQ,KAAK,QAAQ,OAAM,KAAK,QAAQ,UAAQ;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,OAAOA,IAAE;AAAC,SAAI,KAAK,UAAQ,KAAK,OAAM,KAAK,WAAS,CAAC,KAAK,QAAQ,OAAOA,EAAC,KAAG;AAAC,UAAG,CAAC,KAAK,QAAQ,MAAM,QAAM;AAAG,WAAK,UAAQ,KAAK,QAAQ;AAAA,IAAK;AAAC,WAAM,CAAC,CAAC,KAAK;AAAA,EAAO;AAAA,EAAC,OAAOC,IAAE;AAAC,QAAIJ,KAAE,KAAK,OAAMC,KAAE;AAAK,WAAKD,MAAG;AAAC,UAAGA,GAAE,OAAOI,EAAC,EAAE,QAAOJ,GAAE,QAAQ,KAAG,EAAEC,EAAC,MAAIA,GAAE,QAAMD,GAAE,QAAO;AAAG,MAAAC,KAAED,IAAEA,KAAEA,GAAE;AAAA,IAAK;AAAC,WAAM;AAAA,EAAE;AAAC;AAAC,IAAMK,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYF,IAAE;AAAC,SAAK,UAAQ,IAAG,KAAK,QAAM,MAAK,KAAK,SAAO,GAAE,KAAK,gBAAc,GAAE,KAAK,WAAS,EAAC,UAAS,KAAI,GAAE,KAAK,UAAQA;AAAA,EAAC;AAAA,EAAC,OAAO,KAAKA,IAAE;AAAC,WAAO,IAAI,GAAE,IAAI,aAAaA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,kBAAgB,KAAK;AAAA,EAAK;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,WAAS,KAAK,SAAO,KAAK,cAAc,IAAG,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,KAAI;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,GAAGA,IAAE;AAAC,SAAK,QAAQ,KAAK,UAAQ,CAAC,IAAEA;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,QAAQ,KAAK,UAAQ,CAAC,IAAEA;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,KAAGA,KAAEH,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBG,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,KAAGA,KAAEH,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBG,IAAEC,IAAE;AAAC,SAAK,QAAQ,KAAK,UAAQ,KAAGD,KAAEH,KAAE,CAAC,IAAEI;AAAA,EAAC;AAAA,EAAC,yBAAyBD,IAAEC,IAAE;AAAC,SAAK,QAAQ,KAAK,UAAQ,KAAGD,KAAEH,KAAE,CAAC,IAAEI;AAAA,EAAC;AAAA,EAAC,QAAQD,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,KAAGA,KAAEH,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQG,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,KAAGA,KAAEH,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYG,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,KAAGA,KAAEH,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaG,IAAE;AAAC,WAAO,KAAK,QAAQ,KAAK,UAAQ,KAAGA,KAAEH,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKG,IAAE;AAAC,QAAIC,KAAE;AAAK,WAAKA,GAAE,QAAO,CAAAA,KAAEA,GAAE;AAAM,IAAAA,GAAE,QAAMD;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,UAAMA,KAAE,IAAI,GAAE,KAAK,OAAO;AAAE,WAAOA,GAAE,QAAM,KAAK,OAAMA,GAAE,UAAQ,KAAK,SAAQA,GAAE,gBAAc,KAAK,eAAcA,GAAE,WAAS,KAAK,UAASA,GAAE,SAAO,KAAK,QAAOA;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMA,KAAE,KAAK,UAAQ,KAAG,KAAK,cAAYH,KAAE;AAAE,WAAOG,MAAG,KAAK,QAAQ,SAAO,IAAE,KAAK,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAIA,KAAE;AAAE,WAAK,KAAK,UAAQ,KAAK,QAAQ,UAAQA,OAAI,QAAM,OAAK,KAAK,UAAQ,KAAK,UAAQ,IAAE,KAAK,WAAS,KAAG,KAAK,cAAYH,IAAE,KAAK,OAAKC,MAAI;AAAC,WAAO,KAAK,OAAKA,MAAG,KAAK,UAAQ,KAAK,QAAQ;AAAA,EAAM;AAAA,EAAC,OAAOE,IAAE;AAAC,UAAMC,KAAE,KAAK,SAAQJ,KAAE,KAAK,OAAOG,EAAC;AAAE,QAAGH,GAAE,MAAI,KAAK,KAAG,YAAW,EAAE,KAAK,eAAc,KAAK,KAAK,KAAG,KAAK,OAAKG,KAAG,MAAK,KAAG,YAAW,EAAE,KAAK;AAAc,WAAO,KAAK,UAAQC,IAAEJ;AAAA,EAAC;AAAA,EAAC,OAAOG,IAAE;AAAC,UAAMH,KAAE,KAAK;AAAQ,QAAG,EAAE,KAAK,SAAS,QAAQ,GAAE;AAAC,WAAK,SAAS,WAAS,oBAAI;AAAI,YAAMG,KAAE,KAAK,KAAK;AAAE,MAAAA,GAAE,UAAQ;AAAG,UAAIC,KAAE;AAAE,aAAKD,GAAE,KAAK,IAAG,CAAAA,GAAE,OAAKC,OAAI,KAAK,SAAS,SAAS,IAAID,GAAE,IAAGA,GAAE,OAAO,GAAEC,KAAED,GAAE;AAAA,IAAG;AAAC,WAAM,CAAC,CAAC,KAAK,SAAS,SAAS,IAAIA,EAAC,MAAI,KAAK,UAAQ,KAAK,SAAS,SAAS,IAAIA,EAAC,GAAE,KAAK,OAAKF,OAAI,KAAK,UAAQD,IAAE;AAAA,EAAI;AAAA,EAAC,gBAAe;AAAC,UAAMG,KAAE,KAAK;AAAQ,QAAIC,KAAE;AAAE,SAAI,KAAK,UAAQ,IAAG,KAAK,KAAK,IAAG,CAAAA;AAAI,WAAO,KAAK,UAAQD,IAAEC;AAAA,EAAC;AAAC;;;ACA/oJ,IAAME,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEH,IAAEI,IAAE;AAAC,SAAK,SAAOH,IAAE,KAAK,eAAaC,IAAE,KAAK,cAAYC,IAAE,KAAK,YAAUH,IAAE,KAAK,aAAWI;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAU,KAAK;AAAA,EAAU;AAAA,EAAC,OAAOH,IAAE;AAAC,SAAK,cAAYA;AAAA,EAAC;AAAC;AAAC,IAAMG,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYH,IAAEC,IAAE;AAAC,SAAK,eAAa,GAAE,KAAK,UAAQD,IAAE,KAAK,eAAaC;AAAA,EAAC;AAAA,EAAC,OAAO,KAAKA,IAAEC,IAAEH,IAAEK,IAAE;AAAC,UAAMC,KAAE,IAAI,GAAEJ,IAAEC,EAAC;AAAE,QAAG,EAAEE,EAAC,EAAE,YAAUJ,MAAKI,GAAE,CAAAL,GAAE,UAAUC,EAAC,GAAEK,GAAE,UAAUN,EAAC;AAAA,QAAO,QAAKA,GAAE,KAAK,IAAG,CAAAM,GAAE,UAAUN,EAAC;AAAE,WAAOM;AAAA,EAAC;AAAA,EAAC,UAAUL,IAAE;AAAC,UAAMG,KAAE,KAAK,SAAQC,KAAE,KAAK,cAAaC,KAAEL,GAAE;AAAY,QAAIM,KAAEN,GAAE,WAAUO,KAAEP,GAAE;AAAW,UAAMQ,KAAER,GAAE,YAAW,IAAEA,GAAE;AAAY,QAAGO,OAAID,KAAEE,IAAED,KAAE,IAAG,EAAE,KAAK,KAAK,GAAE;AAAC,YAAMP,KAAE,IAAID,GAAEI,IAAEC,IAAEC,IAAEC,IAAEC,EAAC;AAAE,aAAO,MAAK,KAAK,QAAM,IAAIJ,GAAEH,EAAC;AAAA,IAAE;AAAC,QAAIS,KAAE,MAAKC,KAAE,KAAK;AAAM,WAAKA,MAAG;AAAC,UAAGJ,KAAEI,GAAE,KAAK,UAAU,QAAO,KAAK,QAAQL,IAAEC,IAAEC,IAAEE,IAAEC,EAAC;AAAE,MAAAD,KAAEC,IAAEA,KAAEA,GAAE;AAAA,IAAI;AAAC,SAAK,QAAQL,IAAEC,IAAEC,IAAEE,IAAE,IAAI;AAAA,EAAC;AAAA,EAAC,QAAQR,IAAE;AAAC,MAAE,KAAK,KAAK,KAAG,KAAK,MAAM,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,CAAC,QAAO;AAAC,QAAG,EAAE,KAAK,KAAK,EAAE,YAAUD,MAAK,KAAK,MAAM,OAAO,EAAE,OAAMA;AAAA,EAAC;AAAA,EAAC,QAAQG,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAG,EAAED,EAAC,KAAG,EAAEC,EAAC,GAAE;AAAC,YAAMP,KAAE,IAAID,GAAE,KAAK,SAAQ,KAAK,cAAaI,IAAEC,IAAEC,EAAC;AAAE,WAAK,QAAM,IAAIF,GAAEH,EAAC;AAAA,IAAC;AAAC,WAAO,EAAEM,EAAC,KAAG,EAAEC,EAAC,IAAE,KAAK,cAAcJ,IAAEC,IAAEC,IAAEE,EAAC,IAAE,EAAED,EAAC,KAAG,EAAEC,EAAC,IAAE,KAAK,aAAaJ,IAAEC,IAAEC,IAAEC,EAAC,IAAE,EAAEA,EAAC,KAAG,EAAEC,EAAC,IAAE,KAAK,gBAAgBJ,IAAEC,IAAEC,IAAEC,IAAEC,EAAC,IAAE;AAAA,EAAM;AAAA,EAAC,cAAcP,IAAEC,IAAEE,IAAEC,IAAE;AAAC,UAAMC,KAAEJ,KAAEE;AAAE,QAAGH,OAAII,GAAE,KAAK,eAAaC,OAAID,GAAE,KAAK,UAAU,CAAAA,GAAE,KAAK,YAAUH,IAAEG,GAAE,KAAK,cAAYD;AAAA,SAAM;AAAC,YAAME,KAAE,IAAIN,GAAE,KAAK,SAAQ,KAAK,cAAaC,IAAEC,IAAEE,EAAC;AAAE,WAAK,QAAM,IAAIA,GAAEE,EAAC,GAAE,KAAK,MAAM,OAAKD;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaJ,IAAEC,IAAEE,IAAEC,IAAE;AAAC,QAAGA,GAAE,KAAK,gBAAcJ,MAAGI,GAAE,KAAK,aAAWH,GAAE,CAAAG,GAAE,KAAK,cAAYD;AAAA,SAAM;AAAC,YAAME,KAAE,IAAIN,GAAE,KAAK,SAAQ,KAAK,cAAaC,IAAEC,IAAEE,EAAC,GAAEG,KAAE,IAAIH,GAAEE,EAAC;AAAE,MAAAD,GAAE,OAAKE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBN,IAAEC,IAAEE,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAEL,KAAEE;AAAE,QAAGC,GAAE,KAAK,gBAAcJ,MAAGI,GAAE,KAAK,aAAWH,GAAE,CAAAG,GAAE,KAAK,cAAYD,IAAEC,GAAE,KAAK,gBAAcC,GAAE,KAAK,eAAaD,GAAE,KAAK,aAAWC,GAAE,KAAK,cAAYD,GAAE,KAAK,cAAYC,GAAE,KAAK,YAAWD,GAAE,OAAKC,GAAE;AAAA,aAAcL,OAAIK,GAAE,KAAK,eAAaC,OAAID,GAAE,KAAK,UAAU,CAAAA,GAAE,KAAK,YAAUJ,IAAEI,GAAE,KAAK,cAAYF;AAAA,SAAM;AAAC,YAAMG,KAAE,IAAIP,GAAE,KAAK,SAAQ,KAAK,cAAaC,IAAEC,IAAEE,EAAC,GAAEI,KAAE,IAAIJ,GAAEG,EAAC;AAAE,MAAAF,GAAE,OAAKG,IAAEA,GAAE,OAAKF;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACA3tD,IAAMM,KAAE;AAAR,IAAaC,KAAE;AAAf,IAAqB,IAAEA,MAAG,KAAGA;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAE,OAAOH,KAAEC,KAAE,YAAY,mBAAkBC,EAAC;AAAE,SAAK,OAAKF,IAAE,KAAK,YAAUC,IAAE,KAAK,aAAWF,IAAE,KAAK,QAAM,EAAC,OAAM,IAAE,GAAE,KAAI,EAAC,GAAE,KAAK,OAAK,MAAK,KAAK,OAAKI,IAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,KAAK,SAAO,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,aAAW,KAAG,CAAC,KAAK;AAAA,EAAI;AAAA,EAAC,IAAI,2BAA0B;AAAC,WAAO,KAAK,aAAW,KAAG,CAAC,KAAK;AAAA,EAAoB;AAAA,EAAC,aAAY;AAAC,SAAK,0BAA0B,GAAE,EAAE,KAAK,MAAM,CAAAJ,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,4BAA2B;AAAC,MAAE,KAAK,sBAAsB,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,uBAAqB;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,MAAE,KAAK,MAAM,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,EAAE,KAAK,sBAAsB,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,EAAE,KAAK,MAAM,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,EAAE,KAAK,OAAO,CAAAA,OAAGA,GAAE,QAAQ,CAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,MAAM,QAAM,IAAE,GAAE,KAAK,MAAM,MAAI,GAAE,KAAK,WAAS,IAAIE,GAAE,EAAC,OAAM,GAAE,KAAI,KAAK,KAAK,SAAO,KAAK,UAAS,CAAC,GAAE,KAAK,cAAY;AAAA,EAAC;AAAA,EAAC,OAAOF,IAAE;AAAC,QAAG,KAAK,kBAAkB,KAAGA,GAAE;AAAO,QAAGA,KAAE,KAAK,YAAU,KAAK,KAAK,SAAO,KAAK,aAAY;AAAC,WAAK,WAAW;AAAE,YAAMC,KAAE,KAAK,KAAK,SAAO,KAAK,WAAUC,KAAE,KAAK,OAAOD,KAAED,MAAGH,EAAC,GAAEQ,KAAEH,KAAE,KAAK;AAAU,WAAK,KAAK,OAAOG,KAAE,YAAY,iBAAiB,GAAE,KAAK,SAAS,KAAKJ,IAAEC,KAAED,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,IAAID,IAAEC,IAAE;AAAC,SAAK,KAAK,MAAMD,EAAC,MAAIC,OAAI,KAAK,KAAK,MAAMD,EAAC,IAAEC,IAAE,KAAK,MAAM,QAAM,KAAK,IAAID,IAAE,KAAK,MAAM,KAAK,GAAE,KAAK,MAAM,MAAI,KAAK,IAAIA,IAAE,KAAK,MAAM,GAAG;AAAA,EAAE;AAAA,EAAC,aAAaA,IAAEE,KAAE,OAAG;AAAC,QAAG,CAAC,KAAK,WAAW,QAAO;AAAK,QAAGA,IAAE;AAAC,UAAG,YAAU,KAAK,WAAW,OAAM,IAAI,MAAM,iEAAiE;AAAE,aAAO,EAAE,KAAK,oBAAoB,MAAI,KAAK,uBAAqB,KAAK,qBAAqBF,EAAC,IAAG,KAAK;AAAA,IAAoB;AAAC,WAAO,EAAE,KAAK,IAAI,MAAI,KAAK,OAAK,KAAK,cAAcA,EAAC,IAAG,KAAK;AAAA,EAAI;AAAA,EAAC,eAAc;AAAC,QAAG,CAAC,KAAK,OAAM;AAAC,YAAMA,KAAE,KAAK,KAAK,MAAM;AAAE,WAAK,QAAMA;AAAA,IAAC;AAAC,WAAO,KAAK,MAAM,WAAS,KAAK,KAAK,UAAQ,KAAK,MAAM,OAAO,KAAK,KAAK,SAAO,KAAK,KAAK,MAAM,iBAAiB,GAAE,KAAK,MAAM,IAAI,KAAK,IAAI,GAAE,KAAK;AAAA,EAAK;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,KAAK,SAAO,KAAK;AAAA,EAAS;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK,SAAS,kBAAkB;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAEC,IAAEI,IAAEF,IAAE;AAAC,UAAMC,KAAEC,KAAE,KAAK;AAAU,QAAG,CAACD,GAAE,QAAO;AAAE,UAAME,KAAEL,KAAE,KAAK,YAAU,YAAY,mBAAkBJ,KAAE,IAAI,YAAYG,IAAEM,IAAEF,EAAC,GAAEN,KAAE,EAAE,KAAK,SAAS,SAASO,EAAC,GAAE,kCAAkC,IAAE,KAAK,WAAUE,KAAEH,IAAEL,KAAED,KAAE,KAAK,YAAUG;AAAE,QAAG,MAAIE,GAAE,UAAQD,KAAE,GAAEA,KAAEL,GAAE,QAAOK,KAAI,CAAAL,GAAEK,EAAC,KAAGC;AAAE,WAAO,KAAK,KAAK,MAAM,IAAIN,IAAEC,EAAC,GAAE,KAAK,MAAM,QAAM,KAAK,IAAI,KAAK,MAAM,OAAMA,EAAC,GAAE,KAAK,MAAM,MAAI,KAAK,IAAI,KAAK,MAAM,KAAIA,KAAES,EAAC,GAAE,KAAK,cAAY,KAAK,IAAI,KAAK,aAAYT,KAAES,EAAC,GAAER;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAEC,IAAEC,IAAE;AAAC,UAAMG,KAAEL,KAAE,KAAK,WAAUG,MAAGH,KAAEC,MAAG,KAAK;AAAU,QAAG,SAAKC,GAAE,UAAQE,KAAEJ,IAAEI,OAAIJ,KAAEC,IAAEG,KAAI,MAAK,KAAK,MAAMA,KAAE,KAAK,SAAS,IAAE;AAAE,SAAK,MAAM,QAAM,KAAK,IAAI,KAAK,MAAM,OAAMC,EAAC,GAAE,KAAK,MAAM,MAAI,KAAK,IAAI,KAAK,MAAM,KAAIF,EAAC,GAAE,KAAK,SAAS,KAAKH,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAG,KAAK,MAAM,KAAI;AAAC,UAAG,KAAK,0BAA0B,GAAE,EAAE,KAAK,IAAI,EAAE,QAAO,KAAK,MAAM,QAAM,IAAE,GAAE,MAAK,KAAK,MAAM,MAAI;AAAG,WAAK,KAAK,WAAW,KAAK,KAAK,OAAM,KAAK,MAAM,OAAM,KAAK,MAAM,OAAM,KAAK,MAAM,GAAG,GAAE,KAAK,MAAM,QAAM,IAAE,GAAE,KAAK,MAAM,MAAI;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAE;AAAC,UAAMC,KAAE,EAAE;AAAa,WAAM,YAAU,KAAK,aAAWO,GAAE,YAAYR,IAAEC,IAAE,KAAK,KAAK,KAAK,IAAEO,GAAE,aAAaR,IAAEC,IAAE,KAAK,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,qBAAqBD,IAAE;AAAC,UAAMC,KAAE,EAAE,cAAaC,KAAE,IAAI,YAAY,KAAK,cAAY,CAAC;AAAE,aAAQG,KAAE,GAAEA,KAAE,KAAK,aAAYA,MAAG,EAAE,CAAAH,GAAEG,KAAE,CAAC,IAAE,KAAK,KAAK,MAAMA,EAAC;AAAE,WAAOG,GAAE,YAAYR,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAC;;;ACA5pG,IAAM,IAAE;AAAR,IAAUO,KAAE;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,QAAM,oBAAI,OAAI,KAAK,kBAAgB,OAAG,KAAK,eAAaD,IAAE,KAAK,SAAOC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,eAAS,CAACA,IAAEC,EAAC,KAAI,KAAK,MAAM,GAAEA,EAAC,KAAGA,GAAE,QAAQ,KAAE;AAAE,SAAK,eAAa,EAAE,KAAK,YAAY,GAAE,KAAK,gBAAc,EAAE,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,OAAOF,IAAEC,IAAEE,IAAE;AAAC,QAAG,CAACH,GAAE,QAAQ,WAAW;AAAO,UAAMI,KAAEJ,GAAE;AAAO,QAAG,KAAK,iBAAe,KAAK,cAAa;AAAC,YAAMG,KAAEH,GAAE,QAAQ,aAAW,GAAEK,KAAEL,GAAE,SAAS,aAAWI;AAAE,WAAK,aAAa,OAAOD,EAAC,GAAE,KAAK,cAAc,OAAOE,EAAC;AAAE,YAAK,EAAC,UAASC,IAAE,SAAQC,GAAC,IAAEP,IAAEQ,KAAEJ,GAAE,KAAKJ,GAAE,OAAO,GAAEF,KAAE,KAAK,cAAc,OAAOQ,IAAE,GAAEA,GAAE,aAAWF,IAAE,CAAC,GAAEL,KAAE,KAAK,aAAa,OAAOQ,IAAE,GAAEA,GAAE,aAAW,GAAET,EAAC;AAAE,UAAGU,GAAE,QAAS,CAAAR,OAAG;AAAC,QAAAA,GAAE,aAAWD,IAAEC,GAAE,cAAYF;AAAA,MAAC,CAAE,GAAE,EAAE,KAAK,UAAS,gCAAgC,EAAE,KAAKU,EAAC,GAAEP,GAAE,MAAK,kBAAgB;AAAA,eAAW,KAAK,cAAa;AAAC,cAAMD,KAAEQ,GAAE,UAAU;AAAE,eAAKR,GAAE,KAAK,IAAG,MAAK,aAAa,UAAUA,EAAC;AAAA,MAAC;AAAA,IAAC,OAAK;AAAC,YAAME,KAAEF,GAAE,QAAQ,aAAW,GAAEG,KAAEH,GAAE,SAAS,aAAWI,IAAEC,KAAED,KAAE,YAAY,mBAAkBG,KAAE,KAAK,OAAO;AAAW,WAAK,WAASH,GAAE,KAAKJ,GAAE,OAAO,GAAE,KAAK,eAAa,IAAIK,GAAE,SAAQH,IAAE,GAAEK,EAAC,GAAE,KAAK,gBAAc,IAAIF,GAAE,UAASF,IAAEE,IAAEE,EAAC,GAAE,KAAK,aAAa,OAAOP,GAAE,SAAQ,GAAEA,GAAE,QAAQ,aAAW,GAAE,CAAC,GAAE,KAAK,cAAc,OAAOA,GAAE,UAAS,GAAEA,GAAE,SAAS,aAAWI,IAAE,CAAC,GAAEH,OAAI,KAAK,kBAAgB;AAAA,IAAG;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,QAAG,CAAC,EAAE,KAAK,QAAQ,EAAE,YAAUC,MAAKD,IAAE;AAAC,YAAMA,KAAE,KAAK,SAAS,UAAU;AAAE,UAAG,CAACA,GAAE,OAAOC,EAAC,EAAE;AAAS,YAAMC,KAAEF,GAAE,WAAUG,KAAEH,GAAE;AAAW,UAAII,KAAEJ,GAAE,YAAWK,KAAEL,GAAE;AAAY,aAAKA,GAAE,KAAK,KAAGA,GAAE,OAAKC,KAAG,CAAAG,MAAGJ,GAAE,YAAWK,MAAGL,GAAE;AAAY,WAAK,aAAa,KAAKE,IAAEE,EAAC,GAAE,KAAK,cAAc,KAAKD,IAAEE,IAAE,IAAE,GAAE,KAAK,SAAS,OAAOJ,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAEC,IAAEC,IAAEG,IAAE;AAAC,QAAG,CAAC,KAAK,iBAAe,CAAC,KAAK,gBAAc,EAAE,KAAK,QAAQ,KAAG,CAAC,KAAK,cAAc,WAAW,QAAO;AAAK,UAAMC,KAAED,KAAEP,KAAE;AAAE,QAAIW,KAAE,KAAK,MAAM,IAAIH,EAAC;AAAE,KAAC,KAAK,cAAc,eAAa,KAAK,aAAa,eAAaD,MAAG,KAAK,aAAa,8BAA4B,EAAEI,IAAG,CAAAT,OAAGA,GAAE,QAAQ,KAAE,CAAE,GAAES,KAAE,OAAM,KAAK,cAAc,OAAO,GAAE,KAAK,aAAa,OAAO;AAAE,UAAMV,KAAE,KAAK,aAAa,aAAaC,IAAE,MAAIM,EAAC,GAAEI,KAAE,KAAK,cAAc,aAAaV,EAAC;AAAE,WAAOS,OAAIA,KAAE,IAAI,EAAET,IAAEE,IAAED,IAAE,EAAC,UAASS,GAAC,GAAEX,EAAC,GAAE,KAAK,MAAM,IAAIO,IAAEG,EAAC,IAAGA;AAAA,EAAC;AAAA,EAAC,eAAeT,IAAE;AAAC,QAAG,CAAC,EAAE,KAAK,QAAQ,GAAE;AAAC,UAAG,KAAK,aAAa,KAAK,QAAQ,GAAE,CAAC,KAAK,cAAa;AAAC,cAAMA,KAAE,KAAK;AAAkB,aAAK,eAAaW,GAAE,KAAK,MAAK,KAAK,cAAa,KAAK,SAAS,UAAU,GAAEX,EAAC;AAAA,MAAC;AAAC,WAAK,aAAa,QAAQA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAMC,KAAE,CAAC,CAAC,KAAK,aAAa;AAAW,QAAG,CAAC,KAAK,gBAAgB;AAAO,SAAK,kBAAgB;AAAG,QAAIC,KAAE;AAAE,UAAMC,KAAEH,GAAE,UAAU,GAAEI,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,WAAKH,GAAE,KAAK,IAAG,CAAAE,GAAE,KAAKF,GAAE,KAAK,GAAEG,GAAE,KAAKH,GAAE,OAAO,GAAEC,GAAE,KAAKD,GAAE,EAAE;AAAE,IAAAE,GAAE,KAAM,CAACL,IAAEC,OAAI;AAAC,YAAMC,KAAEI,GAAEL,EAAC,GAAEE,KAAEG,GAAEN,EAAC;AAAE,aAAOG,OAAID,KAAEE,GAAEH,EAAC,IAAEG,GAAEJ,EAAC,IAAEE,KAAEC;AAAA,IAAC,CAAE;AAAE,UAAMM,KAAET,GAAE,UAAU,GAAEO,KAAEN,KAAE,KAAK,aAAa,aAAa,IAAE,KAAK,cAAc,aAAa;AAAE,eAAUO,MAAKH,IAAE;AAAC,UAAG,CAACI,GAAE,UAAUD,EAAC,EAAE,OAAM,IAAI,MAAM,wBAAwB;AAAE,UAAGP,IAAE;AAAC,cAAK,EAAC,WAAUD,IAAE,YAAWC,GAAC,IAAEQ;AAAE,QAAAA,GAAE,YAAUP;AAAE,iBAAQC,KAAE,GAAEA,KAAEF,IAAEE,KAAI,MAAK,aAAa,IAAID,MAAIK,GAAE,MAAMP,KAAEG,EAAC,CAAC;AAAA,MAAC,OAAK;AAAC,cAAK,EAAC,YAAWH,IAAE,aAAYC,GAAC,IAAEQ,IAAEN,KAAE,KAAK,cAAc,WAAUC,KAAEJ,KAAEG,IAAEE,KAAED,KAAEH,KAAEE;AAAE,QAAAM,GAAE,aAAWP,KAAEC;AAAE,iBAAQG,KAAEF,IAAEE,KAAED,IAAEC,KAAI,MAAK,cAAc,IAAIJ,MAAIK,GAAE,MAAMD,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,SAAK,oBAAkBD,IAAE,KAAK,eAAa;AAAA,EAAI;AAAC;;;ACAv6F,IAAMO,KAAE;AAAR,IAAWC,KAAE;AAAb,IAAe,IAAE;AAAI,IAAI,IAAE;AAAE,IAAMC,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAML,IAAEC,IAAEC,IAAEC,EAAC,GAAE,KAAK,aAAW,KAAI,KAAK,aAAW,GAAE,KAAK,eAAa,EAAC,SAAQ,EAAC,UAAS,oBAAI,OAAI,SAAQ,KAAI,GAAE,MAAK,MAAK,MAAK,OAAG,YAAW,GAAE,QAAO,MAAE,GAAE,KAAK,WAAS,IAAI,EAAE,CAAC,GAAE,KAAK,iBAAe,IAAI,EAAE,CAAC,GAAE,KAAK,kBAAgB,GAAE,KAAK,WAAW,aAAW,EAAE,GAAE,KAAK,SAAOC,IAAE,KAAK,sBAAoBC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,aAAa,QAAQ,SAAS,QAAS,CAAAC,OAAGA,GAAE,QAAQ,CAAE,GAAE,EAAE,KAAK,aAAa,IAAI,KAAG,KAAK,aAAa,KAAK,SAAS,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,aAAa,UAAQ,MAAK,KAAK,aAAa,OAAK;AAAA,EAAI;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,aAAa,QAAQ;AAAA,EAAO;AAAA,EAAC,IAAI,UAAS;AAAC,WAAM,CAAC,CAAC,KAAK,aAAa,QAAQ,SAAS;AAAA,EAAI;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAO,KAAK,aAAa,QAAQ,SAAS,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAEN,IAAE;AAAC,SAAK,cAAaM,GAAE,SAAO,KAAK,SAAS,QAAMT,MAAG,KAAK,aAAa;AAAE,UAAMI,KAAEK,IAAEJ,KAAED,GAAE,eAAa,KAAK,IAAI,OAAKA,GAAE,YAAY;AAAc,IAAAD,MAAGE,KAAE,KAAK,eAAe,QAAQD,EAAC,KAAGA,GAAE,OAAKA,GAAE,QAAM,CAACD,IAAE,KAAK,SAAS,QAAQC,EAAC,IAAG,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,OAAOK,IAAE;AAAC,QAAG,KAAK,oBAAkBA,GAAE,MAAK;AAAC,WAAK,kBAAgBA,GAAE;AAAK,eAAQA,KAAE,GAAEA,KAAER,IAAEQ,KAAI,MAAK,YAAY,GAAE,KAAK,WAAS,KAAK,kBAAkB;AAAE,WAAK,aAAa,SAAO,KAAK,kBAAkB,GAAE,KAAK,cAAc;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,aAAa,SAAO;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,SAAK,aAAa,SAAO,OAAG,KAAK,cAAc,GAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,QAAG,KAAK,aAAa,MAAK;AAAC,UAAG,KAAK,aAAa,OAAO,QAAO,KAAK,aAAa,OAAK,MAAG,KAAK,KAAK,cAAc;AAAE,WAAK,aAAa,OAAK,MAAG,KAAK,MAAM;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,aAAa,SAAO,KAAK,aAAa,OAAK,OAAG,EAAE,KAAK,aAAa,IAAI,MAAI,KAAK,aAAa,QAAQ,SAAS,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,aAAa,UAAQ,KAAK,aAAa,MAAK,KAAK,aAAa,OAAK,MAAK,KAAK,oBAAoB;AAAA,EAAG;AAAA,EAAC,gBAAe;AAAC,QAAIA,KAAE,KAAK,SAAS;AAAQ,WAAK,KAAK,SAAS,QAAMA,OAAK,MAAK,YAAY,GAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,UAAMA,KAAE,KAAK,eAAe,KAAK;AAAE,QAAG,CAAC,EAAEA,EAAC,KAAG,CAACA,GAAE,SAAO,SAAO,KAAK,aAAa,KAAK,QAAK,KAAK,eAAe,QAAM;AAAC,YAAMA,KAAE,KAAK,eAAe,QAAQ;AAAE,QAAEA,EAAC,KAAG,KAAK,aAAaA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAJvhF;AAIwhF,UAAMA,KAAE,KAAK,SAAS,QAAQ;AAAE,QAAG,EAAEA,EAAC,GAAE;AAAC,UAAG,IAAI,sBAAsB,GAAE;AAAC,cAAMN,KAAEM,IAAEL,MAAE,KAAAD,GAAE,gBAAF,mBAAe,eAAcE,KAAE,KAAK,IAAI,OAAKD,KAAE,SAAOA;AAAE,YAAIM,KAAE;AAAG,iBAAQD,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAC,QAAG,iBAAAP,GAAE,gBAAF,mBAAe,KAAKM,QAApB,mBAAwB,YAAxB,mBAAiC,cAAW,IAAE;AAAE,gBAAQ,MAAM,KAAK,IAAI,IAAG,qBAAoB,WAAWN,GAAE,KAAK,YAAYE,EAAC,SAASF,GAAE,GAAG,SAASO,EAAC,GAAG;AAAA,MAAC;AAAC,eAAKD,GAAE,UAAQ,EAAE,KAAK,aAAa,IAAI,MAAI,KAAK,aAAa,KAAK,SAAS,QAAS,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,aAAa,OAAK,OAAM,KAAK,aAAa,OAAK,EAAC,UAAS,oBAAI,OAAI,SAAQ,KAAI,GAAE,IAAI,sBAAsB,KAAG,QAAQ,MAAM,KAAK,IAAI,IAAG,oDAAoD,IAAG,KAAK,cAAc,GAAE,KAAK,OAAOA,EAAC,GAAEA,GAAE,QAAM,IAAI,sBAAsB,KAAG,QAAQ,MAAM,KAAK,IAAI,IAAG,mDAAmD,GAAE,KAAK,MAAM,GAAE,KAAK,kBAAkB;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,OAAG,CAAAN,OAAG;AAAC,WAAK,QAAQA,IAAEM,GAAE,MAAM,GAAE,KAAK,QAAQN,IAAEM,IAAE,KAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,OAAG,CAAAN,OAAG;AAAC,WAAK,QAAQA,IAAEM,IAAE,IAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEN,IAAEO,IAAE;AAJr8G;AAIs8G,QAAG;AAAC,YAAMH,KAAE,EAAE,KAAK,aAAa,MAAK,KAAK,aAAa,OAAO,GAAEC,MAAE,KAAAL,GAAE,gBAAF,mBAAe,KAAKM,KAAGE,KAAEJ,GAAE;AAAS,UAAG,EAAEC,EAAC,EAAE;AAAO,MAAAG,GAAE,IAAIF,EAAC,MAAI,IAAI,sBAAsB,KAAG,QAAQ,MAAM,KAAK,IAAI,IAAG,kDAAkDA,EAAC,EAAE,GAAEE,GAAE,IAAIF,IAAE,IAAIT,GAAES,IAAE,KAAK,KAAK,CAAC,IAAG,IAAI,sBAAsB,KAAG,QAAQ,MAAM,KAAK,IAAI,IAAG,wCAAwCA,EAAC,cAAa,KAAAN,GAAE,gBAAF,mBAAe,OAAO,WAAWK,GAAE,MAAM,EAAE,GAAEG,GAAE,IAAIF,EAAC,EAAE,OAAOD,IAAEL,GAAE,MAAKO,EAAC,GAAED,OAAI,EAAE,SAAO,KAAK,oBAAoBN,GAAE,MAAKK,GAAE,SAAQL,GAAE,KAAK;AAAA,IAAC,SAAOI,IAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBE,IAAEN,IAAEO,IAAE;AAAC,UAAMJ,KAAE,EAAE,KAAK,aAAa,MAAK,KAAK,aAAa,OAAO;AAAE,QAAG,EAAEH,EAAC,EAAE;AAAO,UAAMI,KAAEK,GAAE,KAAKT,EAAC;AAAE,QAAG,EAAEG,GAAE,OAAO,EAAE,CAAAA,GAAE,UAAQC;AAAA,SAAM;AAAC,UAAG,aAAWE,IAAE;AAAC,cAAMA,KAAEF,GAAE,UAAU;AAAE,eAAKE,GAAE,KAAK,IAAG,CAAAH,GAAE,QAAQ,OAAOG,GAAE,EAAE;AAAA,MAAC;AAAC,MAAAH,GAAE,QAAQ,KAAKC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAQE,IAAEN,IAAE;AAAC,UAAME,KAAE,EAAE,KAAK,aAAa,MAAK,KAAK,aAAa,OAAO,EAAE,SAAS,IAAII,EAAC;AAAE,IAAAN,MAAGA,GAAE,UAAQE,OAAIA,GAAE,OAAOF,EAAC,GAAE,KAAK,oBAAoBA,EAAC;AAAA,EAAE;AAAA,EAAC,oBAAoBM,IAAE;AAAC,UAAK,EAAC,SAAQN,GAAC,IAAE,EAAE,KAAK,aAAa,MAAK,KAAK,aAAa,OAAO;AAAE,QAAG,CAAC,EAAEA,EAAC,KAAGM,GAAE,OAAO,YAAUL,MAAKK,GAAE,QAAKN,GAAE,OAAOC,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,UAAMK,KAAE,IAAI;AAAM,QAAIN,KAAE;AAAG,WAAK,KAAK,SAAS,QAAM;AAAC,YAAMC,KAAE,KAAK,SAAS,QAAQ;AAAE,UAAG,EAAEA,EAAC,EAAE;AAAM,UAAGA,GAAE,OAAM;AAAC,YAAGD,GAAE;AAAM,QAAAA,KAAE;AAAA,MAAE;AAAC,MAAAM,GAAE,KAAKL,EAAC;AAAA,IAAC;AAAC,SAAK,SAAS,MAAM,GAAEK,GAAE,QAAS,CAAAA,OAAG,KAAK,SAAS,QAAQA,EAAC,CAAE;AAAA,EAAC;AAAC;;;ACAh5I,IAAMI,KAAE,IAAI,sCAAsC;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMH,EAAC,GAAE,KAAK,oBAAkB,CAAC,GAAE,KAAK,SAAOE,IAAE,KAAK,aAAWD,IAAE,KAAK,YAAUE;AAAA,EAAC;AAAA,EAAC,eAAeH,IAAE;AAAC,QAAG,KAAK,cAAc,OAAO,GAAE,KAAK,cAAa;AAAC,MAAAA,GAAE,QAAQ,QAAQ,UAAU,KAAKA,IAAEA,GAAE,aAAa;AAAA,IAAC;AAAC,UAAM,eAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAuB;AAAC,WAAO,KAAK,cAAc,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,cAAc,WAAW;AAAA,EAAC;AAAA,EAAC,QAAQC,IAAE;AAAC,QAAIC,KAAE,KAAK,kBAAkB,KAAM,CAAC,EAAC,GAAEF,IAAE,GAAEE,GAAC,MAAIF,OAAIC,GAAE,KAAGC,OAAID,GAAE,CAAE;AAAE,UAAME,KAAE,EAAE;AAAE,WAAOD,KAAEA,GAAE,UAAU,KAAKC,EAAC,KAAGD,KAAE,EAAC,GAAED,GAAE,GAAE,GAAEA,GAAE,GAAE,WAAU,CAACE,EAAC,EAAC,GAAE,KAAK,kBAAkB,KAAKD,EAAC,IAAG,KAAK,cAAc,GAAEC,GAAE;AAAA,EAAO;AAAA,EAAC,WAAWH,IAAEC,IAAE;AAAC,UAAMC,KAAEL,MAAG,aAAY,KAAK,UAAQ,KAAK,OAAO,SAAQM,KAAED,OAAGA,MAAA,gBAAAA,GAAG,WAAQ,CAACA,GAAE,CAAC,EAAE,mBAAiBA,GAAE,CAAC,EAAE,OAAMJ,KAAE,CAAC,CAACI,MAAG,KAAK,WAAW,kBAAgBC;AAAE,IAAAH,GAAE,MAAMC,IAAEH,EAAC,GAAE,KAAK,SAASE,EAAC,KAAG,KAAK,SAASA,EAAC,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,SAASA,EAAC,KAAG,KAAK,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAEC,IAAE;AAAC,UAAM,2BAA2BD,IAAEC,EAAC,GAAE,KAAK,WAAW,kBAAkB,eAAeD,IAAEC,EAAC,GAAE,KAAK,WAAW,kBAAkB,iBAAe,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,SAASD,IAAE;AAAC,UAAK,EAAC,UAASC,IAAE,UAASC,GAAC,IAAE,KAAK,OAAO,qBAAoBC,KAAEH,GAAE,MAAM;AAAM,IAAAG,OAAIF,MAAG,IAAE,MAAIE,MAAGD,MAAG,MAAM,SAASF,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAM,YAAYA,EAAC,GAAE,KAAK,kBAAkB,UAAQ,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,SAAK,aAAW,KAAK,WAAW,KAAK,aAAa,cAAc,GAAE,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,oBAAoBA,IAAE;AAAC,UAAK,EAAC,gBAAeC,GAAC,IAAED,GAAE;AAAa,QAAG,mBAAgBC,MAAA,gBAAAA,GAAG,UAAMA,MAAA,gBAAAA,GAAG,WAAQ,KAAG,eAAYA,MAAA,gBAAAA,GAAG,OAAK;AAAC,YAAMD,KAAE;AAAE,iBAAUC,MAAK,KAAK,SAAS,CAAAA,GAAE,aAAWA,GAAE,IAAI,QAAMD;AAAA,IAAC,MAAM,OAAM,oBAAoBA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,QAAG,eAAc,KAAK,OAAO,QAAO,KAAK,OAAO,UAAU,KAAM,CAAAA,OAAC;AAJvjE,UAAAI;AAIyjE,cAAC,GAACA,MAAAJ,GAAE,iBAAF,gBAAAI,IAAgB,WAAQJ,GAAE;AAAA,KAAc;AAAE,UAAMA,KAAE,KAAK,OAAO,kBAAiBC,KAAED,MAAG,kBAAiBA,MAAGA,GAAE,iBAAeA,GAAE,gBAAcA,GAAE,aAAa;AAAO,WAAO,KAAK,OAAO,gBAAc,KAAK,OAAO,aAAa,UAAQ,KAAK,OAAO,iBAAe,CAAC,CAACC;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAE;AAAC,UAAME,KAAEF,GAAE,mBAAmB,EAAC,MAAK,SAAQ,SAAQ,CAAC,EAAE,KAAK,GAAE,QAAO,MAAI,KAAK,YAAU,KAAK,WAAS,MAAK,WAAU,EAAE,QAAM,EAAE,YAAW,CAAC,GAAEH,KAAEG,GAAE,mBAAmB,EAAC,MAAK,YAAW,SAAQ,CAAC,EAAE,MAAK,EAAE,YAAW,EAAE,MAAK,EAAE,QAAO,EAAE,SAAQ,EAAE,UAAS,EAAE,IAAI,GAAE,QAAO,MAAI,KAAK,UAAS,mBAAkB,MAAI,CAAC,KAAK,WAAW,kBAAkB,YAAW,SAAQ,CAAC,EAAC,OAAMA,GAAE,QAAQ,eAAc,QAAO,MAAI,KAAK,WAAW,kBAAkB,YAAW,MAAK,MAAI,KAAK,WAAW,kBAAkB,gBAAe,GAAE,EAAC,OAAMA,GAAE,QAAQ,cAAa,QAAO,MAAI,KAAK,WAAW,kBAAkB,YAAW,MAAK,MAAI,KAAK,WAAW,kBAAkB,gBAAe,GAAE,EAAC,OAAMA,GAAE,QAAQ,SAAQ,QAAO,MAAI,CAAC,CAAC,KAAK,kBAAkB,QAAO,MAAK,MAAI,KAAK,kBAAiB,CAAC,EAAC,CAAC,GAAEF,KAAEE,GAAE,mBAAmB,EAAC,MAAK,aAAY,SAAQ,CAAC,EAAE,MAAK,EAAE,YAAW,EAAE,MAAK,EAAE,QAAO,EAAE,UAAS,EAAE,IAAI,GAAE,QAAO,MAAI,KAAK,UAAS,WAAU,EAAE,WAAU,mBAAkB,MAAI,OAAG,SAAQ,CAAC,EAAC,OAAMA,GAAE,QAAQ,WAAU,QAAO,MAAI,CAAC,CAAC,KAAK,WAAW,aAAa,EAAC,CAAC,EAAC,CAAC;AAAE,WAAM,CAAC,GAAG,MAAM,oBAAoBA,EAAC,GAAEH,IAAEC,IAAEI,EAAC;AAAA,EAAC;AAAC;;;ACAnhF,IAAIG,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK;AAAA,EAAQ;AAAA,EAAC,QAAQC,IAAE;AAAC,UAAMC,KAAE,MAAI,KAAK,aAAa,UAAU,GAAEC,KAAE,IAAIC,GAAE,KAAK,cAAa,KAAK,WAAU,KAAK,OAAMF,EAAC;AAAE,SAAK,eAAaC,IAAEF,GAAE,SAASE,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUF,IAAE;AAAC,IAAAA,GAAE,YAAY,KAAK,YAAY,GAAE,KAAK,eAAa,EAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAE;AAAC,UAAK,EAAC,KAAIG,GAAC,IAAEJ,IAAEK,KAAE,KAAK,aAAa;AAAM,QAAG;AAAC,aAAOA,GAAE,gBAAgB,cAAcD,IAAE,EAAC,QAAOH,GAAE,OAAM,CAAC;AAAA,IAAC,SAAOE,IAAE;AAAC,aAAO,EAAEA,EAAC,IAAE,QAAQ,QAAQ,EAAC,OAAM,GAAE,QAAO,EAAC,CAAC,IAAE,QAAQ,OAAOA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAJr2C;AAIs2C,UAAMH,KAAE,MAAM,WAAW,GAAEC,KAAE,CAAC,KAAK,gBAAc,KAAK,aAAa,WAAW,GAAEC,MAAE,UAAK,iBAAL,mBAAmB,yBAAwBE,KAAEJ,MAAGC,MAAGD,MAAGE;AAAE,WAAO,IAAI,sBAAsB,KAAG,QAAQ,IAAI,+BAA+BE,EAAC;AAAA,qBAAwBJ,EAAC;AAAA,uBAA0B,CAAC,CAAC,KAAK,YAAY;AAAA,4BAA+BC,EAAC,EAAE,GAAEG;AAAA,EAAC;AAAA,EAAC,QAAQJ,IAAE;AAAC,WAAO,KAAK,aAAa,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,WAAO,QAAMA,MAAG,CAAC,UAAS,gBAAe,gBAAe,eAAc,cAAa,WAAU,WAAW,EAAE,SAASA,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,QAAIC,KAAE;AAAK,QAAGD,MAAG,qBAAoBA,IAAE;AAAC,YAAME,MAAGI,GAAEN,EAAC,EAAE,mBAAiB,CAAC,GAAG,IAAK,CAAAA,OAAG;AAAC,cAAMC,KAAED,GAAE,MAAM;AAAE,eAAM,wBAAuBA,OAAIC,GAAE,qBAAmB,OAAMD,GAAE,mBAAiB,kBAAgBA,GAAE,oBAAkBC,GAAE,kBAAgB,MAAKA,GAAE,QAAM,QAAOA;AAAA,MAAC,CAAE;AAAE,MAAAA,KAAEM,GAAEL,EAAC;AAAA,IAAC;AAAC,SAAK,aAAa,gBAAgBF,IAAEC,IAAE,KAAK,UAAU,aAAa;AAAA,EAAC;AAAA,EAAC,WAAWD,IAAE;AAAC,UAAMC,KAAE,KAAK,MAAM,IAAID,GAAE,OAAO;AAAE,IAAAC,MAAGD,GAAE,QAAM,KAAK,aAAa,WAAWC,IAAED,GAAE,IAAI,GAAE,KAAK,UAAU,KAAK,aAAa,cAAc;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMC,KAAE,KAAK,MAAM,IAAID,GAAE,OAAO;AAAE,IAAAC,MAAG,KAAK,aAAa,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAC,SAAK,aAAa,cAAc,mBAAmB;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,SAAK,aAAa,cAAc,kBAAkB,GAAE,KAAK,MAAM,QAAS,CAAAD,OAAGA,GAAE,KAAK,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,aAAa,cAAc,oBAAoB,GAAE,KAAK,MAAM,QAAS,CAAAA,OAAGA,GAAE,OAAO,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBA,IAAE;AAAC,WAAO,KAAK,aAAa,iBAAiBA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,aAAa,aAAW,KAAK,UAAU,KAAK,aAAa,cAAc;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAMC,KAAE,KAAK,aAAa,cAAc,EAAE,GAAED,EAAC,GAAEE,KAAE,MAAI,KAAK,UAAU,KAAK,aAAa,cAAc,GAAEE,KAAE,KAAK,aAAa,kBAAkBJ,GAAE,KAAK,GAAED,KAAE,KAAK,aAAa;AAAc,WAAO,IAAIS,GAAER,IAAEI,IAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEF,IAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAE;AAAC,SAAK,aAAa,YAAYA,EAAC,GAAEA,GAAE,QAAQ,GAAE,KAAK,UAAU,KAAK,aAAa,cAAc;AAAA,EAAC;AAAC;AAAEF,KAAE,EAAE,CAACK,GAAE,gEAAgE,CAAC,GAAEL,EAAC;AAAE,IAAMW,KAAEX;", "names": ["e", "r", "o", "t", "s", "i", "n", "t", "e", "i", "a", "d", "r", "s", "o", "h", "m", "c", "a", "u", "f", "t", "i", "e", "s", "h", "r", "n", "d", "E", "u", "c", "e", "t", "r", "s", "i", "f", "n", "d", "h", "o", "_", "a", "c", "u", "l", "t", "r", "s", "a", "n", "h", "e", "i", "d", "o", "r", "a", "o", "e", "t", "s", "i", "_a", "p", "o", "e", "t", "i", "a", "s", "r", "n", "f", "l", "h"]}