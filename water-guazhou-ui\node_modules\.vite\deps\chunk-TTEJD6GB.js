import {
  o
} from "./chunk-5AI3QK7R.js";
import {
  f,
  i
} from "./chunk-XBS7QZIQ.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  c
} from "./chunk-ZACBBT3Y.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  b
} from "./chunk-HP475EI3.js";

// node_modules/@arcgis/core/rest/support/ProjectParameters.js
var i2 = class extends l {
  constructor(r) {
    super(r), this.geometries = [], this.outSpatialReference = null, this.transformation = null, this.transformForward = null;
  }
  toJSON() {
    const r = this.geometries.map((r2) => r2.toJSON()), t = this.geometries[0], o2 = {};
    return o2.outSR = this.outSpatialReference.wkid || JSON.stringify(this.outSpatialReference.toJSON()), o2.inSR = t.spatialReference.wkid || JSON.stringify(t.spatialReference.toJSON()), o2.geometries = JSON.stringify({ geometryType: c(t), geometries: r }), this.transformation && (o2.transformation = this.transformation.wkid || JSON.stringify(this.transformation)), null != this.transformForward && (o2.transformForward = this.transformForward), o2;
  }
};
e([y()], i2.prototype, "geometries", void 0), e([y({ json: { read: { source: "outSR" } } })], i2.prototype, "outSpatialReference", void 0), e([y()], i2.prototype, "transformation", void 0), e([y()], i2.prototype, "transformForward", void 0), i2 = e([a("esri.rest.support.ProjectParameters")], i2);
var a2 = i2;

// node_modules/@arcgis/core/rest/geometryService/project.js
var i3 = b(a2);
async function n(o2, m, n2) {
  m = i3(m);
  const u = f(o2), c2 = { ...u.query, f: "json", ...m.toJSON() }, j = m.outSpatialReference, a3 = c(m.geometries[0]), f2 = i(c2, n2);
  return U(u.path + "/project", f2).then(({ data: { geometries: r } }) => o(r, a3, j));
}

export {
  a2 as a,
  n
};
//# sourceMappingURL=chunk-TTEJD6GB.js.map
