{"version": 3, "sources": ["../../@arcgis/core/arcade/functions/featuresetstats.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ArcadeExecutionError as n,ExecutionErrorCodes as t}from\"../executionError.js\";import{y as r,T as e,m as a,c as i,x as s,A as c}from\"../../chunks/languageUtils.js\";import{calculateStat as u}from\"./fieldStats.js\";import{WhereClause as o}from\"../../core/sql/WhereClause.js\";async function f(n,t,r,i,f,d){if(1===i.length){if(a(i[0]))return u(n,i[0],c(i[1],-1));if(s(i[0]))return u(n,i[0].toArray(),c(i[1],-1))}else if(2===i.length){if(a(i[0]))return u(n,i[0],c(i[1],-1));if(s(i[0]))return u(n,i[0].toArray(),c(i[1],-1));if(e(i[0])){const r=await i[0].load(),e=await l(o.create(i[1],r.getFieldsIndex()),d,f);return i[0].calculateStatistic(n,e,c(i[2],1e3),t.abortSignal)}}else if(3===i.length&&e(i[0])){const r=await i[0].load(),e=await l(o.create(i[1],r.getFieldsIndex()),d,f);return i[0].calculateStatistic(n,e,c(i[2],1e3),t.abortSignal)}return u(n,i,-1)}async function l(n,t,r){const e=n.getVariables();if(e.length>0){const a=[];for(let n=0;n<e.length;n++){const i={name:e[n]};a.push(await t.evaluateIdentifier(r,i))}const i={};for(let n=0;n<e.length;n++)i[e[n]]=a[n];return n.parameters=i,n}return n}function d(c){\"async\"===c.mode&&(c.functions.stdev=function(n,t){return c.standardFunctionAsync(n,t,((t,r,e)=>f(\"stdev\",t,r,e,n,c)))},c.functions.variance=function(n,t){return c.standardFunctionAsync(n,t,((t,r,e)=>f(\"variance\",t,r,e,n,c)))},c.functions.average=function(n,t){return c.standardFunctionAsync(n,t,((t,r,e)=>f(\"mean\",t,r,e,n,c)))},c.functions.mean=function(n,t){return c.standardFunctionAsync(n,t,((t,r,e)=>f(\"mean\",t,r,e,n,c)))},c.functions.sum=function(n,t){return c.standardFunctionAsync(n,t,((t,r,e)=>f(\"sum\",t,r,e,n,c)))},c.functions.min=function(n,t){return c.standardFunctionAsync(n,t,((t,r,e)=>f(\"min\",t,r,e,n,c)))},c.functions.max=function(n,t){return c.standardFunctionAsync(n,t,((t,r,e)=>f(\"max\",t,r,e,n,c)))},c.functions.count=function(u,o){return c.standardFunctionAsync(u,o,((c,f,l)=>{if(r(l,1,1,u,o),e(l[0]))return l[0].count(c.abortSignal);if(a(l[0])||i(l[0]))return l[0].length;if(s(l[0]))return l[0].length();throw new n(u,t.InvalidParameter,o)}))})}export{d as registerFunctions};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIsR,eAAeA,GAAE,GAAEC,IAAE,GAAE,GAAED,IAAEE,IAAE;AAAC,MAAG,MAAI,EAAE,QAAO;AAAC,QAAG,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,EAAE,GAAE,EAAE,CAAC,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC;AAAE,QAAG,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,EAAE,GAAE,EAAE,CAAC,EAAE,QAAQ,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC;AAAA,EAAC,WAAS,MAAI,EAAE,QAAO;AAAC,QAAG,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,EAAE,GAAE,EAAE,CAAC,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC;AAAE,QAAG,EAAE,EAAE,CAAC,CAAC,EAAE,QAAO,EAAE,GAAE,EAAE,CAAC,EAAE,QAAQ,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC;AAAE,QAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,YAAMC,KAAE,MAAM,EAAE,CAAC,EAAE,KAAK,GAAEC,KAAE,MAAMC,GAAE,EAAE,OAAO,EAAE,CAAC,GAAEF,GAAE,eAAe,CAAC,GAAED,IAAEF,EAAC;AAAE,aAAO,EAAE,CAAC,EAAE,mBAAmB,GAAEI,IAAE,EAAE,EAAE,CAAC,GAAE,GAAG,GAAEH,GAAE,WAAW;AAAA,IAAC;AAAA,EAAC,WAAS,MAAI,EAAE,UAAQ,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,UAAME,KAAE,MAAM,EAAE,CAAC,EAAE,KAAK,GAAEC,KAAE,MAAMC,GAAE,EAAE,OAAO,EAAE,CAAC,GAAEF,GAAE,eAAe,CAAC,GAAED,IAAEF,EAAC;AAAE,WAAO,EAAE,CAAC,EAAE,mBAAmB,GAAEI,IAAE,EAAE,EAAE,CAAC,GAAE,GAAG,GAAEH,GAAE,WAAW;AAAA,EAAC;AAAC,SAAO,EAAE,GAAE,GAAE,EAAE;AAAC;AAAC,eAAeI,GAAE,GAAEJ,IAAE,GAAE;AAAC,QAAMG,KAAE,EAAE,aAAa;AAAE,MAAGA,GAAE,SAAO,GAAE;AAAC,UAAM,IAAE,CAAC;AAAE,aAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,YAAMC,KAAE,EAAC,MAAKH,GAAEE,EAAC,EAAC;AAAE,QAAE,KAAK,MAAML,GAAE,mBAAmB,GAAEM,EAAC,CAAC;AAAA,IAAC;AAAC,UAAM,IAAE,CAAC;AAAE,aAAQD,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,GAAEF,GAAEE,EAAC,CAAC,IAAE,EAAEA,EAAC;AAAE,WAAO,EAAE,aAAW,GAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,cAAU,EAAE,SAAO,EAAE,UAAU,QAAM,SAAS,GAAEL,IAAE;AAAC,WAAO,EAAE,sBAAsB,GAAEA,IAAG,CAACA,IAAE,GAAEG,OAAIJ,GAAE,SAAQC,IAAE,GAAEG,IAAE,GAAE,CAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,WAAS,SAAS,GAAEH,IAAE;AAAC,WAAO,EAAE,sBAAsB,GAAEA,IAAG,CAACA,IAAE,GAAEG,OAAIJ,GAAE,YAAWC,IAAE,GAAEG,IAAE,GAAE,CAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,UAAQ,SAAS,GAAEH,IAAE;AAAC,WAAO,EAAE,sBAAsB,GAAEA,IAAG,CAACA,IAAE,GAAEG,OAAIJ,GAAE,QAAOC,IAAE,GAAEG,IAAE,GAAE,CAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,OAAK,SAAS,GAAEH,IAAE;AAAC,WAAO,EAAE,sBAAsB,GAAEA,IAAG,CAACA,IAAE,GAAEG,OAAIJ,GAAE,QAAOC,IAAE,GAAEG,IAAE,GAAE,CAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,MAAI,SAAS,GAAEH,IAAE;AAAC,WAAO,EAAE,sBAAsB,GAAEA,IAAG,CAACA,IAAE,GAAEG,OAAIJ,GAAE,OAAMC,IAAE,GAAEG,IAAE,GAAE,CAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,MAAI,SAAS,GAAEH,IAAE;AAAC,WAAO,EAAE,sBAAsB,GAAEA,IAAG,CAACA,IAAE,GAAEG,OAAIJ,GAAE,OAAMC,IAAE,GAAEG,IAAE,GAAE,CAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,MAAI,SAAS,GAAEH,IAAE;AAAC,WAAO,EAAE,sBAAsB,GAAEA,IAAG,CAACA,IAAE,GAAEG,OAAIJ,GAAE,OAAMC,IAAE,GAAEG,IAAE,GAAE,CAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,QAAM,SAAS,GAAE,GAAE;AAAC,WAAO,EAAE,sBAAsB,GAAE,GAAG,CAACI,IAAER,IAAEK,OAAI;AAAC,UAAG,EAAEA,IAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAEA,GAAE,CAAC,CAAC,EAAE,QAAOA,GAAE,CAAC,EAAE,MAAMG,GAAE,WAAW;AAAE,UAAG,EAAEH,GAAE,CAAC,CAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,EAAE,QAAOA,GAAE,CAAC,EAAE;AAAO,UAAG,EAAEA,GAAE,CAAC,CAAC,EAAE,QAAOA,GAAE,CAAC,EAAE,OAAO;AAAE,YAAM,IAAI,EAAE,GAAE,EAAE,kBAAiB,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAE;", "names": ["f", "t", "d", "r", "e", "l", "n", "i", "c"]}