{"version": 3, "sources": ["../../@arcgis/core/geometry/support/intersects.js", "../../@arcgis/core/layers/graphics/contains.js", "../../@arcgis/core/layers/graphics/data/spatialQuerySupport.js", "../../@arcgis/core/layers/graphics/data/timeSupport.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{extentIntersectsExtent as e,getFeatureExtentIntersector as t}from\"./intersectsBase.js\";export{extentIntersectsMultipoint,extentIntersectsPoint,extentIntersectsPolygon,extentIntersectsPolyline,extentIntersectsRings,isSelfIntersecting,segmentIntersects}from\"./intersectsBase.js\";function s(s){return\"mesh\"===s?e:t(s)}export{e as extentIntersectsExtent,s as getExtentIntersector,t as getFeatureExtentIntersector};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction n(n,t){return n?t?4:3:t?3:2}function t(n,t,r,e){return o(n,t,r,e.coords[0],e.coords[1])}function r(t,r,e,c,u,f){const s=n(u,f),{coords:i,lengths:l}=c;if(!l)return!1;for(let n=0,d=0;n<l.length;n++,d+=s)if(!o(t,r,e,i[d],i[d+1]))return!1;return!0}function o(t,r,o,c,u){if(!t)return!1;const f=n(r,o),{coords:s,lengths:i}=t;let l=!1,d=0;for(const n of i)l=e(l,s,f,d,n,c,u),d+=n*f;return l}function e(n,t,r,o,e,c,u){let f=n,s=o;for(let i=o,l=o+e*r;i<l;i+=r){s=i+r,s===l&&(s=o);const n=t[i],e=t[i+1],d=t[s],g=t[s+1];(e<u&&g>=u||g<u&&e>=u)&&n+(u-e)/(g-e)*(d-n)<c&&(f=!f)}return f}export{o as polygonContainsCoords,r as polygonContainsMultipoint,t as polygonContainsPoint};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../core/Error.js\";import{extentContainsPoint as r,extentContainsMultipoint as t}from\"../../../geometry/support/contains.js\";import{getExtentIntersector as i}from\"../../../geometry/support/intersects.js\";import{isPolygon as s,isExtent as o,getJsonType as n}from\"../../../geometry/support/jsonUtils.js\";import{isValid as l}from\"../../../geometry/support/spatialReferenceUtils.js\";import{polygonContainsPoint as a,polygonContainsMultipoint as p}from\"../contains.js\";import{convertFromPolygon as u}from\"../featureConversionUtils.js\";import m from\"../OptimizedGeometry.js\";import{checkProjectionSupport as y}from\"./projectionSupport.js\";import{getGeometry as f}from\"./utils.js\";const c=\"feature-store:unsupported-query\",R={esriSpatialRelIntersects:\"intersects\",esriSpatialRelContains:\"contains\",esriSpatialRelCrosses:\"crosses\",esriSpatialRelDisjoint:\"disjoint\",esriSpatialRelEnvelopeIntersects:\"intersects\",esriSpatialRelIndexIntersects:null,esriSpatialRelOverlaps:\"overlaps\",esriSpatialRelTouches:\"touches\",esriSpatialRelWithin:\"within\",esriSpatialRelRelation:null},S={spatialRelationship:{esriSpatialRelIntersects:!0,esriSpatialRelContains:!0,esriSpatialRelWithin:!0,esriSpatialRelCrosses:!0,esriSpatialRelDisjoint:!0,esriSpatialRelTouches:!0,esriSpatialRelOverlaps:!0,esriSpatialRelEnvelopeIntersects:!0,esriSpatialRelIndexIntersects:!1,esriSpatialRelRelation:!1},queryGeometry:{esriGeometryPoint:!0,esriGeometryMultipoint:!0,esriGeometryPolyline:!0,esriGeometryPolygon:!0,esriGeometryEnvelope:!0},layerGeometry:{esriGeometryPoint:!0,esriGeometryMultipoint:!0,esriGeometryPolyline:!0,esriGeometryPolygon:!0,esriGeometryEnvelope:!1}};function G(e){return null!=e&&!0===S.spatialRelationship[e]}function g(e){return null!=e&&!0===S.queryGeometry[n(e)]}function j(e){return null!=e&&!0===S.layerGeometry[e]}function h(){return import(\"../../../geometry/geometryEngineJSON.js\")}function v(e,n,l,y,c){if(s(n)&&\"esriGeometryPoint\"===l&&(\"esriSpatialRelIntersects\"===e||\"esriSpatialRelContains\"===e)){const e=u(new m,n,!1,!1);return Promise.resolve((r=>a(e,!1,!1,r)))}if(s(n)&&\"esriGeometryMultipoint\"===l){const r=u(new m,n,!1,!1);if(\"esriSpatialRelContains\"===e)return Promise.resolve((e=>p(r,!1,!1,e,y,c)))}if(o(n)&&\"esriGeometryPoint\"===l&&(\"esriSpatialRelIntersects\"===e||\"esriSpatialRelContains\"===e))return Promise.resolve((e=>r(n,f(l,y,c,e))));if(o(n)&&\"esriGeometryMultipoint\"===l&&\"esriSpatialRelContains\"===e)return Promise.resolve((e=>t(n,f(l,y,c,e))));if(o(n)&&\"esriSpatialRelIntersects\"===e){const e=i(l);return Promise.resolve((r=>e(n,f(l,y,c,r))))}return h().then((r=>{const t=r[R[e]].bind(null,n.spatialReference,n);return e=>t(f(l,y,c,e))}))}async function P(r,t,i){const{spatialRel:s,geometry:o}=r;if(o){if(!G(s))throw new e(c,\"Unsupported query spatial relationship\",{query:r});if(l(o.spatialReference)&&l(i)){if(!g(o))throw new e(c,\"Unsupported query geometry type\",{query:r});if(!j(t))throw new e(c,\"Unsupported layer geometry type\",{query:r});if(r.outSR)return y(r.geometry&&r.geometry.spatialReference,r.outSR)}}}function I(e){if(o(e))return!0;if(s(e)){for(const r of e.rings){if(5!==r.length)return!1;if(r[0][0]!==r[1][0]||r[0][0]!==r[4][0]||r[2][0]!==r[3][0]||r[0][1]!==r[3][1]||r[0][1]!==r[4][1]||r[1][1]!==r[2][1])return!1}return!0}return!1}export{I as canQueryWithRBush,P as checkSpatialQuerySupport,v as getSpatialQueryOperator};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nasync function t(t,n){if(!t)return null;const e=n.featureAdapter,{startTimeField:u,endTimeField:l}=t;let r=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;if(u&&l)await n.forEach((t=>{const n=e.getAttribute(t,u),a=e.getAttribute(t,l);null==n||isNaN(n)||(r=Math.min(r,n)),null==a||isNaN(a)||(i=Math.max(i,a))}));else{const t=u||l;await n.forEach((n=>{const u=e.getAttribute(n,t);null==u||isNaN(u)||(r=Math.min(r,u),i=Math.max(i,u))}))}return{start:r,end:i}}function n(t,n,r){if(!n||!t)return null;const{startTimeField:i,endTimeField:a}=t;if(!i&&!a)return null;const{start:o,end:s}=n;return null===o&&null===s?null:void 0===o&&void 0===s?l():i&&a?e(r,i,a,o,s):u(r,i||a,o,s)}function e(t,n,e,u,l){return null!=u&&null!=l?r=>{const i=t.getAttribute(r,n),a=t.getAttribute(r,e);return(null==i||i<=l)&&(null==a||a>=u)}:null!=u?n=>{const l=t.getAttribute(n,e);return null==l||l>=u}:null!=l?e=>{const u=t.getAttribute(e,n);return null==u||u<=l}:void 0}function u(t,n,e,u){return null!=e&&null!=u&&e===u?u=>t.getAttribute(u,n)===e:null!=e&&null!=u?l=>{const r=t.getAttribute(l,n);return r>=e&&r<=u}:null!=e?u=>t.getAttribute(u,n)>=e:null!=u?e=>t.getAttribute(e,n)<=u:void 0}function l(){return()=>!1}export{t as getTimeExtent,n as getTimeOperator};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4R,SAASA,GAAEA,IAAE;AAAC,SAAM,WAASA,KAAE,IAAE,EAAEA,EAAC;AAAC;;;ACAjU,SAAS,EAAEC,IAAEC,IAAE;AAAC,SAAOD,KAAEC,KAAE,IAAE,IAAEA,KAAE,IAAE;AAAC;AAAC,SAASA,GAAED,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAOC,GAAEJ,IAAEC,IAAEC,IAAEC,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC;AAAC;AAAC,SAASD,GAAED,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEF,IAAEC,EAAC,GAAE,EAAC,QAAOE,IAAE,SAAQC,GAAC,IAAEL;AAAE,MAAG,CAACK,GAAE,QAAM;AAAG,WAAQV,KAAE,GAAE,IAAE,GAAEA,KAAEU,GAAE,QAAOV,MAAI,KAAGQ,GAAE,KAAG,CAACJ,GAAEH,IAAEC,IAAEC,IAAEM,GAAE,CAAC,GAAEA,GAAE,IAAE,CAAC,CAAC,EAAE,QAAM;AAAG,SAAM;AAAE;AAAC,SAASL,GAAEH,IAAEC,IAAEE,IAAEC,IAAEC,IAAE;AAAC,MAAG,CAACL,GAAE,QAAM;AAAG,QAAMM,KAAE,EAAEL,IAAEE,EAAC,GAAE,EAAC,QAAOI,IAAE,SAAQC,GAAC,IAAER;AAAE,MAAIS,KAAE,OAAG,IAAE;AAAE,aAAUV,MAAKS,GAAE,CAAAC,KAAE,EAAEA,IAAEF,IAAED,IAAE,GAAEP,IAAEK,IAAEC,EAAC,GAAE,KAAGN,KAAEO;AAAE,SAAOG;AAAC;AAAC,SAAS,EAAEV,IAAEC,IAAEC,IAAEE,IAAED,IAAEE,IAAEC,IAAE;AAAC,MAAIC,KAAEP,IAAEQ,KAAEJ;AAAE,WAAQK,KAAEL,IAAEM,KAAEN,KAAED,KAAED,IAAEO,KAAEC,IAAED,MAAGP,IAAE;AAAC,IAAAM,KAAEC,KAAEP,IAAEM,OAAIE,OAAIF,KAAEJ;AAAG,UAAMJ,KAAEC,GAAEQ,EAAC,GAAEN,KAAEF,GAAEQ,KAAE,CAAC,GAAE,IAAER,GAAEO,EAAC,GAAEG,KAAEV,GAAEO,KAAE,CAAC;AAAE,KAACL,KAAEG,MAAGK,MAAGL,MAAGK,KAAEL,MAAGH,MAAGG,OAAIN,MAAGM,KAAEH,OAAIQ,KAAER,OAAI,IAAEH,MAAGK,OAAIE,KAAE,CAACA;AAAA,EAAE;AAAC,SAAOA;AAAC;;;ACAiH,IAAMK,KAAE;AAAR,IAA0C,IAAE,EAAC,0BAAyB,cAAa,wBAAuB,YAAW,uBAAsB,WAAU,wBAAuB,YAAW,kCAAiC,cAAa,+BAA8B,MAAK,wBAAuB,YAAW,uBAAsB,WAAU,sBAAqB,UAAS,wBAAuB,KAAI;AAAnY,IAAqY,IAAE,EAAC,qBAAoB,EAAC,0BAAyB,MAAG,wBAAuB,MAAG,sBAAqB,MAAG,uBAAsB,MAAG,wBAAuB,MAAG,uBAAsB,MAAG,wBAAuB,MAAG,kCAAiC,MAAG,+BAA8B,OAAG,wBAAuB,MAAE,GAAE,eAAc,EAAC,mBAAkB,MAAG,wBAAuB,MAAG,sBAAqB,MAAG,qBAAoB,MAAG,sBAAqB,KAAE,GAAE,eAAc,EAAC,mBAAkB,MAAG,wBAAuB,MAAG,sBAAqB,MAAG,qBAAoB,MAAG,sBAAqB,MAAE,EAAC;AAAE,SAASC,GAAEC,IAAE;AAAC,SAAO,QAAMA,MAAG,SAAK,EAAE,oBAAoBA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,QAAMA,MAAG,SAAK,EAAE,cAAc,EAAEA,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,QAAMA,MAAG,SAAK,EAAE,cAAcA,EAAC;AAAC;AAAC,SAASC,KAAG;AAAC,SAAO,OAAO,kCAAyC;AAAC;AAAC,SAASC,GAAEF,IAAEG,IAAEC,IAAEC,IAAEP,IAAE;AAAC,MAAG,EAAEK,EAAC,KAAG,wBAAsBC,OAAI,+BAA6BJ,MAAG,6BAA2BA,KAAG;AAAC,UAAMA,KAAE,EAAE,IAAI,KAAEG,IAAE,OAAG,KAAE;AAAE,WAAO,QAAQ,QAAS,CAAAG,OAAGC,GAAEP,IAAE,OAAG,OAAGM,EAAC,CAAE;AAAA,EAAC;AAAC,MAAG,EAAEH,EAAC,KAAG,6BAA2BC,IAAE;AAAC,UAAME,KAAE,EAAE,IAAI,KAAEH,IAAE,OAAG,KAAE;AAAE,QAAG,6BAA2BH,GAAE,QAAO,QAAQ,QAAS,CAAAA,OAAGM,GAAEA,IAAE,OAAG,OAAGN,IAAEK,IAAEP,EAAC,CAAE;AAAA,EAAC;AAAC,MAAG,EAAEK,EAAC,KAAG,wBAAsBC,OAAI,+BAA6BJ,MAAG,6BAA2BA,IAAG,QAAO,QAAQ,QAAS,CAAAA,OAAG,EAAEG,IAAE,EAAEC,IAAEC,IAAEP,IAAEE,EAAC,CAAC,CAAE;AAAE,MAAG,EAAEG,EAAC,KAAG,6BAA2BC,MAAG,6BAA2BJ,GAAE,QAAO,QAAQ,QAAS,CAAAA,OAAG,EAAEG,IAAE,EAAEC,IAAEC,IAAEP,IAAEE,EAAC,CAAC,CAAE;AAAE,MAAG,EAAEG,EAAC,KAAG,+BAA6BH,IAAE;AAAC,UAAMA,KAAEQ,GAAEJ,EAAC;AAAE,WAAO,QAAQ,QAAS,CAAAE,OAAGN,GAAEG,IAAE,EAAEC,IAAEC,IAAEP,IAAEQ,EAAC,CAAC,CAAE;AAAA,EAAC;AAAC,SAAOL,GAAE,EAAE,KAAM,CAAAK,OAAG;AAAC,UAAMC,KAAED,GAAE,EAAEN,EAAC,CAAC,EAAE,KAAK,MAAKG,GAAE,kBAAiBA,EAAC;AAAE,WAAO,CAAAH,OAAGO,GAAE,EAAEH,IAAEC,IAAEP,IAAEE,EAAC,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,eAAe,EAAEM,IAAEC,IAAEE,IAAE;AAAC,QAAK,EAAC,YAAWD,IAAE,UAASE,GAAC,IAAEJ;AAAE,MAAGI,IAAE;AAAC,QAAG,CAACX,GAAES,EAAC,EAAE,OAAM,IAAI,EAAEV,IAAE,0CAAyC,EAAC,OAAMQ,GAAC,CAAC;AAAE,QAAG,EAAEI,GAAE,gBAAgB,KAAG,EAAED,EAAC,GAAE;AAAC,UAAG,CAAC,EAAEC,EAAC,EAAE,OAAM,IAAI,EAAEZ,IAAE,mCAAkC,EAAC,OAAMQ,GAAC,CAAC;AAAE,UAAG,CAAC,EAAEC,EAAC,EAAE,OAAM,IAAI,EAAET,IAAE,mCAAkC,EAAC,OAAMQ,GAAC,CAAC;AAAE,UAAGA,GAAE,MAAM,QAAO,EAAEA,GAAE,YAAUA,GAAE,SAAS,kBAAiBA,GAAE,KAAK;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAASK,GAAEX,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAM;AAAG,MAAG,EAAEA,EAAC,GAAE;AAAC,eAAUM,MAAKN,GAAE,OAAM;AAAC,UAAG,MAAIM,GAAE,OAAO,QAAM;AAAG,UAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAE,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAE,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAE,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAE,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAE,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAE,CAAC,EAAE,CAAC,EAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAC,SAAM;AAAE;;;ACA/sG,eAAeM,GAAEA,IAAEC,IAAE;AAAC,MAAG,CAACD,GAAE,QAAO;AAAK,QAAME,KAAED,GAAE,gBAAe,EAAC,gBAAeE,IAAE,cAAaC,GAAC,IAAEJ;AAAE,MAAIK,KAAE,OAAO,mBAAkBC,KAAE,OAAO;AAAkB,MAAGH,MAAGC,GAAE,OAAMH,GAAE,QAAS,CAAAD,OAAG;AAAC,UAAMC,KAAEC,GAAE,aAAaF,IAAEG,EAAC,GAAE,IAAED,GAAE,aAAaF,IAAEI,EAAC;AAAE,YAAMH,MAAG,MAAMA,EAAC,MAAII,KAAE,KAAK,IAAIA,IAAEJ,EAAC,IAAG,QAAM,KAAG,MAAM,CAAC,MAAIK,KAAE,KAAK,IAAIA,IAAE,CAAC;AAAA,EAAE,CAAE;AAAA,OAAM;AAAC,UAAMN,KAAEG,MAAGC;AAAE,UAAMH,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAME,KAAED,GAAE,aAAaD,IAAED,EAAC;AAAE,cAAMG,MAAG,MAAMA,EAAC,MAAIE,KAAE,KAAK,IAAIA,IAAEF,EAAC,GAAEG,KAAE,KAAK,IAAIA,IAAEH,EAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAC,SAAM,EAAC,OAAME,IAAE,KAAIC,GAAC;AAAC;AAAC,SAASL,GAAED,IAAEC,IAAEI,IAAE;AAAC,MAAG,CAACJ,MAAG,CAACD,GAAE,QAAO;AAAK,QAAK,EAAC,gBAAeM,IAAE,cAAa,EAAC,IAAEN;AAAE,MAAG,CAACM,MAAG,CAAC,EAAE,QAAO;AAAK,QAAK,EAAC,OAAMC,IAAE,KAAIC,GAAC,IAAEP;AAAE,SAAO,SAAOM,MAAG,SAAOC,KAAE,OAAK,WAASD,MAAG,WAASC,KAAE,EAAE,IAAEF,MAAG,IAAEJ,GAAEG,IAAEC,IAAE,GAAEC,IAAEC,EAAC,IAAEL,GAAEE,IAAEC,MAAG,GAAEC,IAAEC,EAAC;AAAC;AAAC,SAASN,GAAEF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,QAAMD,MAAG,QAAMC,KAAE,CAAAC,OAAG;AAAC,UAAMC,KAAEN,GAAE,aAAaK,IAAEJ,EAAC,GAAE,IAAED,GAAE,aAAaK,IAAEH,EAAC;AAAE,YAAO,QAAMI,MAAGA,MAAGF,QAAK,QAAM,KAAG,KAAGD;AAAA,EAAE,IAAE,QAAMA,KAAE,CAAAF,OAAG;AAAC,UAAMG,KAAEJ,GAAE,aAAaC,IAAEC,EAAC;AAAE,WAAO,QAAME,MAAGA,MAAGD;AAAA,EAAC,IAAE,QAAMC,KAAE,CAAAF,OAAG;AAAC,UAAMC,KAAEH,GAAE,aAAaE,IAAED,EAAC;AAAE,WAAO,QAAME,MAAGA,MAAGC;AAAA,EAAC,IAAE;AAAM;AAAC,SAASD,GAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,QAAMD,MAAG,QAAMC,MAAGD,OAAIC,KAAE,CAAAA,OAAGH,GAAE,aAAaG,IAAEF,EAAC,MAAIC,KAAE,QAAMA,MAAG,QAAMC,KAAE,CAAAC,OAAG;AAAC,UAAMC,KAAEL,GAAE,aAAaI,IAAEH,EAAC;AAAE,WAAOI,MAAGH,MAAGG,MAAGF;AAAA,EAAC,IAAE,QAAMD,KAAE,CAAAC,OAAGH,GAAE,aAAaG,IAAEF,EAAC,KAAGC,KAAE,QAAMC,KAAE,CAAAD,OAAGF,GAAE,aAAaE,IAAED,EAAC,KAAGE,KAAE;AAAM;AAAC,SAAS,IAAG;AAAC,SAAM,MAAI;AAAE;", "names": ["s", "n", "t", "r", "e", "o", "c", "u", "f", "s", "i", "l", "g", "c", "G", "e", "h", "v", "n", "l", "y", "r", "t", "s", "i", "o", "I", "t", "n", "e", "u", "l", "r", "i", "o", "s"]}