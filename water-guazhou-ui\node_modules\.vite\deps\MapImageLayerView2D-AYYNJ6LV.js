import {
  S
} from "./chunk-MJS64MES.js";
import {
  a as a2
} from "./chunk-4W7HU754.js";
import {
  n
} from "./chunk-GLYRTR3D.js";
import {
  v
} from "./chunk-U56QVIJL.js";
import {
  a as a3
} from "./chunk-NMTMUDUY.js";
import "./chunk-KYDW2SHL.js";
import "./chunk-WCRONQ5Z.js";
import {
  ae
} from "./chunk-27CJODAI.js";
import "./chunk-L7J6WAZK.js";
import "./chunk-EJ4BPAYT.js";
import "./chunk-JTTSDQPH.js";
import "./chunk-6DXPU43Z.js";
import "./chunk-3MBH7CQT.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-SMSZBVG5.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-3BVSG4LE.js";
import "./chunk-JJZTA23S.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-VXAO6YJP.js";
import "./chunk-BI4P4NAQ.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-YROREPK5.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-EVADT7ME.js";
import "./chunk-T3GGN2P7.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-6NIKJYUX.js";
import "./chunk-UHA44FM7.js";
import "./chunk-TFWV44LH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-6OFWBRK2.js";
import {
  i as i2
} from "./chunk-5JDQNIY4.js";
import {
  f
} from "./chunk-NEPFZ7PM.js";
import {
  u
} from "./chunk-HWB4LNSZ.js";
import "./chunk-JSZR3BUH.js";
import "./chunk-QKWIBVLD.js";
import "./chunk-JCXMTMKU.js";
import "./chunk-WAPZ634R.js";
import "./chunk-OHAM27JH.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-HXJOBP6R.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-22FAZXOH.js";
import "./chunk-DFGMRI52.js";
import "./chunk-OZZFNS32.js";
import "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-RURSJOSG.js";
import "./chunk-77E52HT5.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-7I556A2J.js";
import "./chunk-SIWJOTKY.js";
import {
  i
} from "./chunk-56K7OMWB.js";
import {
  c
} from "./chunk-XEMCQFPJ.js";
import "./chunk-N35UHD63.js";
import "./chunk-T5TRCNG4.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-YBNKNHCD.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import "./chunk-G3QAWKCD.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-WL6G2MRC.js";
import "./chunk-UCWK623G.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-WJPDYSRI.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import {
  b
} from "./chunk-VJW7RCN7.js";
import "./chunk-Q7K3J54I.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-QC5SLERR.js";
import "./chunk-3M3FTH72.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-PNIF6I3E.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-6NKJB2TO.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/layers/MapImageLayerView.js
var p = (p2) => {
  let a4 = class extends p2 {
    initialize() {
      this.exportImageParameters = new c({ layer: this.layer });
    }
    destroy() {
      this.exportImageParameters.destroy(), this.exportImageParameters = null;
    }
    get floors() {
      var _a;
      return ((_a = this.view) == null ? void 0 : _a.floors) ?? null;
    }
    get exportImageVersion() {
      var _a;
      return (_a = this.exportImageParameters) == null ? void 0 : _a.commitProperty("version"), this.commitProperty("timeExtent"), this.commitProperty("floors"), (this._get("exportImageVersion") || 0) + 1;
    }
    canResume() {
      var _a;
      return !!super.canResume() && !((_a = this.timeExtent) == null ? void 0 : _a.isEmpty);
    }
  };
  return e([y()], a4.prototype, "exportImageParameters", void 0), e([y({ readOnly: true })], a4.prototype, "floors", null), e([y({ readOnly: true })], a4.prototype, "exportImageVersion", null), e([y()], a4.prototype, "layer", void 0), e([y()], a4.prototype, "suspended", void 0), e([y(b)], a4.prototype, "timeExtent", void 0), a4 = e([a("esri.views.layers.MapImageLayerView")], a4), a4;
};

// node_modules/@arcgis/core/views/2d/layers/MapImageLayerView2D.js
var f2 = class extends p(i2(f(u))) {
  constructor() {
    super(...arguments), this._highlightGraphics = new i(), this._updateHash = "";
  }
  fetchPopupFeatures(t, e2) {
    return this._popupHighlightHelper.fetchPopupFeatures(t, e2);
  }
  update(t) {
    const r = `${this.exportImageVersion}/${t.state.id}/${t.pixelRatio}/${t.stationary}`;
    this._updateHash !== r && (this._updateHash = r, this.strategy.update(t).catch((t2) => {
      j(t2) || s.getLogger(this.declaredClass).error(t2);
    }), t.stationary && this._popupHighlightHelper.updateHighlightedFeatures(t.state.resolution)), this._highlightView.processUpdate(t);
  }
  attach() {
    const { imageMaxWidth: t, imageMaxHeight: e2, version: i3 } = this.layer, s2 = i3 >= 10.3, a4 = i3 >= 10;
    this._bitmapContainer = new a3(), this.container.addChild(this._bitmapContainer), this._highlightView = new ae({ view: this.view, graphics: this._highlightGraphics, requestUpdateCallback: () => this.requestUpdate(), container: new n(this.view.featuresTilingScheme), defaultPointSymbolEnabled: false }), this.container.addChild(this._highlightView.container), this._popupHighlightHelper = new S({ createFetchPopupFeaturesQueryGeometry: (t2, e3) => a2(t2, e3, this.view), highlightGraphics: this._highlightGraphics, highlightGraphicUpdated: (t2, e3) => {
      this._highlightView.graphicUpdateHandler({ graphic: t2, property: e3 });
    }, layerView: this, updatingHandles: this.updatingHandles }), this.strategy = new v({ container: this._bitmapContainer, fetchSource: this.fetchImageBitmap.bind(this), requestUpdate: this.requestUpdate.bind(this), imageMaxWidth: t, imageMaxHeight: e2, imageRotationSupported: s2, imageNormalizationSupported: a4, hidpi: true }), this.addAttachHandles(l(() => this.exportImageVersion, () => this.requestUpdate())), this.requestUpdate();
  }
  detach() {
    this.strategy.destroy(), this.container.removeAllChildren(), this._bitmapContainer.removeAllChildren(), this._highlightView.destroy(), this._popupHighlightHelper.destroy();
  }
  moveStart() {
  }
  viewChange() {
  }
  moveEnd() {
    this.requestUpdate();
  }
  supportsSpatialReference(t) {
    return this.layer.serviceSupportsSpatialReference(t);
  }
  async doRefresh() {
    this._updateHash = "", this.requestUpdate();
  }
  isUpdating() {
    return this.strategy.updating || this.updateRequested;
  }
  fetchImage(t, e2, i3, r) {
    return this.layer.fetchImage(t, e2, i3, { timeExtent: this.timeExtent, floors: this.floors, ...r });
  }
  fetchImageBitmap(t, e2, i3, r) {
    return this.layer.fetchImageBitmap(t, e2, i3, { timeExtent: this.timeExtent, floors: this.floors, ...r });
  }
  highlight(t) {
    return this._popupHighlightHelper.highlight(t);
  }
};
e([y()], f2.prototype, "strategy", void 0), e([y()], f2.prototype, "updating", void 0), f2 = e([a("esri.views.2d.layers.MapImageLayerView2D")], f2);
var w = f2;
export {
  w as default
};
//# sourceMappingURL=MapImageLayerView2D-AYYNJ6LV.js.map
