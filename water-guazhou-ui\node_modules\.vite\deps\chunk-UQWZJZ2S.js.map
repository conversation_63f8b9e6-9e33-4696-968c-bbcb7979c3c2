{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/SnappingPoint.js", "../../@arcgis/core/views/interactive/snapping/snappingUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as n}from\"../../../core/maybe.js\";import{a as r,f as t}from\"../../../chunks/vec3f64.js\";import{makeDehydratedPoint as e}from\"../../../layers/graphics/dehydratedFeatures.js\";import{getConvertedElevation as o,absoluteHeightElevationInfo as u,getConvertedElevationFromVector as i}from\"../../../support/elevationInfoUtils.js\";function l(n){return n}function f(n){return r(n)}function s(n,r,e){return t(n,r,e)}function c(r,t,e){return n(r)?null:a(e.coordinateHelper.vectorToDehydratedPoint(r,d),t,e)}function a(r,e,i){if(n(r))return null;if(n(e))return l(t(r.x,r.y,r.z??0));if(\"2d\"===e.type)return l(t(r.x,r.y,0));const{elevationInfo:f}=i,s=o(e,r,f,u)??0;return l(t(r.x,r.y,s))}function m(r,t,{z:o,m:l,spatialReference:f,elevationInfo:s}){if(null==o&&null==l){const n=e(r[0],r[1],void 0,f);return null!=l&&(n.m=l,n.hasM=!0),n}if(n(t)||\"2d\"===t.type){const n=e(r[0],r[1],o,f);return null!=l&&(n.m=l,n.hasM=!0),n}const c=i(t,r,f,u,s)??0,a=e(r[0],r[1],c,f);return null!=l&&(a.m=l,a.hasM=!0),a}function p(n,r){return e(n[0],n[1],n[2],r)}const d=e(0,0,0,null);export{c as anyMapPointToSnappingPoint,l as asSnappingPoint,f as cloneSnappingPoint,s as createSnappingPoint,a as pointToSnappingPoint,p as snappingPointToDehydratedPoint,m as snappingPointToSnappingOutput};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../core/maybe.js\";import{watch as t,syncAndInitial as n}from\"../../../core/reactiveUtils.js\";import{d as o}from\"../../../chunks/vec3.js\";import{makeDehydratedPoint as r}from\"../../../layers/graphics/dehydratedFeatures.js\";import{ViewEventPriorities as i}from\"../../input/InputManager.js\";import{SNAPPING_KEYS as s}from\"../keybindings.js\";import{anyMapPointToSnappingPoint as a}from\"./SnappingPoint.js\";function p(e,t){const n=e.length===t.length&&e[0]===t[0]&&e[1]===t[1];switch(e.length){case 2:return n;case 3:return n&&e[2]===t[2];case 4:return n&&e[2]===t[2]&&e[3]===t[3]}return!1}function c(e,t){const n=e.x-t.x,o=e.y-t.y;return n*n+o*o}function g(e,t){return Math.sqrt(c(e,t))}function d(e,t){t.sort(((t,n)=>o(t.targetPoint,e)-o(n.targetPoint,e)))}var u;function l({point:t,distance:n,types:o,coordinateHelper:{spatialReference:i}},s,a){return{point:r(t[0],t[1],t[2],i.toJSON()),mode:s,distance:n,types:o,query:e(a)?a.toJSON():{where:\"1=1\"}}}function E(e,t,n){return{left:a(e.leftVertex.pos,t,n),right:a(e.rightVertex.pos,t,n)}}function f(e){return e.createQuery()}function m(o,r=(()=>{})){const a=t((()=>({view:o.view,snappingOptions:o.snappingOptions})),(({view:t,snappingOptions:n})=>{const a=\"snapping-toggle\",p=i.TOOL;if(o.removeHandles(a),t&&e(n)){const e=[t.on(\"key-down\",(e=>{e.key!==s.toggle||e.repeat||(n.enabledToggled=!0,r())}),p),t.on(\"key-up\",(e=>{e.key===s.toggle&&(n.enabledToggled=!1,r())}),p),t.on(\"pointer-move\",(e=>{const t=e.native.ctrlKey;n.enabledToggled!==t&&(n.enabledToggled=t,r())}),p)];o.addHandles(e,a)}}),n);o.addHandles(a)}!function(e){e[e.TARGET=0]=\"TARGET\",e[e.REFERENCE=1]=\"REFERENCE\",e[e.REFERENCE_EXTENSION=2]=\"REFERENCE_EXTENSION\"}(u||(u={}));export{u as LineSegmentHintType,E as editEdgeToSnappingEdge,f as makeFilter,l as makeSnappingQuery,p as objectEqual,g as screenDistance,m as setupSnappingToggleHandles,d as sortCandidatesInPlace,c as squaredScreenDistance};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgV,SAASA,GAAE,GAAE;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAOC,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEC,IAAE,GAAE;AAAC,SAAOA,GAAE,GAAEA,IAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAED,IAAE,GAAE;AAAC,SAAO,EAAEC,EAAC,IAAE,OAAK,EAAE,EAAE,iBAAiB,wBAAwBA,IAAE,CAAC,GAAED,IAAE,CAAC;AAAC;AAAC,SAAS,EAAEC,IAAE,GAAE,GAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAO;AAAK,MAAG,EAAE,CAAC,EAAE,QAAOF,GAAEE,GAAEA,GAAE,GAAEA,GAAE,GAAEA,GAAE,KAAG,CAAC,CAAC;AAAE,MAAG,SAAO,EAAE,KAAK,QAAOF,GAAEE,GAAEA,GAAE,GAAEA,GAAE,GAAE,CAAC,CAAC;AAAE,QAAK,EAAC,eAAcC,GAAC,IAAE,GAAEC,KAAE,EAAE,GAAEF,IAAEC,IAAE,CAAC,KAAG;AAAE,SAAOH,GAAEE,GAAEA,GAAE,GAAEA,GAAE,GAAEE,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEF,IAAED,IAAE,EAAC,GAAE,GAAE,GAAED,IAAE,kBAAiBG,IAAE,eAAcC,GAAC,GAAE;AAAC,MAAG,QAAM,KAAG,QAAMJ,IAAE;AAAC,UAAM,IAAE,EAAEE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,QAAOC,EAAC;AAAE,WAAO,QAAMH,OAAI,EAAE,IAAEA,IAAE,EAAE,OAAK,OAAI;AAAA,EAAC;AAAC,MAAG,EAAEC,EAAC,KAAG,SAAOA,GAAE,MAAK;AAAC,UAAM,IAAE,EAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,GAAEC,EAAC;AAAE,WAAO,QAAMH,OAAI,EAAE,IAAEA,IAAE,EAAE,OAAK,OAAI;AAAA,EAAC;AAAC,QAAMK,KAAE,EAAEJ,IAAEC,IAAEC,IAAE,GAAEC,EAAC,KAAG,GAAEE,KAAE,EAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEG,IAAEF,EAAC;AAAE,SAAO,QAAMH,OAAIM,GAAE,IAAEN,IAAEM,GAAE,OAAK,OAAIA;AAAC;AAAC,SAASC,GAAE,GAAEL,IAAE;AAAC,SAAO,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAEA,EAAC;AAAC;AAAC,IAAM,IAAE,EAAE,GAAE,GAAE,GAAE,IAAI;;;ACA/b,SAASM,GAAE,GAAEC,IAAE;AAAC,QAAM,IAAE,EAAE,IAAEA,GAAE,GAAE,IAAE,EAAE,IAAEA,GAAE;AAAE,SAAO,IAAE,IAAE,IAAE;AAAC;AAAC,SAASC,GAAE,GAAED,IAAE;AAAC,SAAO,KAAK,KAAKD,GAAE,GAAEC,EAAC,CAAC;AAAC;AAAC,SAASE,GAAE,GAAEF,IAAE;AAAC,EAAAA,GAAE,KAAM,CAACA,IAAE,MAAI,EAAEA,GAAE,aAAY,CAAC,IAAE,EAAE,EAAE,aAAY,CAAC,CAAE;AAAC;AAAC,IAAI;AAAE,SAASG,GAAE,EAAC,OAAMH,IAAE,UAAS,GAAE,OAAM,GAAE,kBAAiB,EAAC,kBAAiB,EAAC,EAAC,GAAEI,IAAEC,IAAE;AAAC,SAAM,EAAC,OAAM,EAAEL,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,EAAE,OAAO,CAAC,GAAE,MAAKI,IAAE,UAAS,GAAE,OAAM,GAAE,OAAM,EAAEC,EAAC,IAAEA,GAAE,OAAO,IAAE,EAAC,OAAM,MAAK,EAAC;AAAC;AAAC,SAASC,GAAE,GAAEN,IAAE,GAAE;AAAC,SAAM,EAAC,MAAK,EAAE,EAAE,WAAW,KAAIA,IAAE,CAAC,GAAE,OAAM,EAAE,EAAE,YAAY,KAAIA,IAAE,CAAC,EAAC;AAAC;AAAC,SAASO,GAAE,GAAE;AAAC,SAAO,EAAE,YAAY;AAAC;AAAC,SAASC,GAAE,GAAEC,KAAG,MAAI;AAAC,GAAG;AAAC,QAAMJ,KAAE,EAAG,OAAK,EAAC,MAAK,EAAE,MAAK,iBAAgB,EAAE,gBAAe,IAAK,CAAC,EAAC,MAAKL,IAAE,iBAAgB,EAAC,MAAI;AAAC,UAAMK,KAAE,mBAAkBK,KAAE,EAAE;AAAK,QAAG,EAAE,cAAcL,EAAC,GAAEL,MAAG,EAAE,CAAC,GAAE;AAAC,YAAM,IAAE,CAACA,GAAE,GAAG,YAAY,CAAAW,OAAG;AAAC,QAAAA,GAAE,QAAMX,GAAE,UAAQW,GAAE,WAAS,EAAE,iBAAe,MAAGF,GAAE;AAAA,MAAE,GAAGC,EAAC,GAAEV,GAAE,GAAG,UAAU,CAAAW,OAAG;AAAC,QAAAA,GAAE,QAAMX,GAAE,WAAS,EAAE,iBAAe,OAAGS,GAAE;AAAA,MAAE,GAAGC,EAAC,GAAEV,GAAE,GAAG,gBAAgB,CAAAW,OAAG;AAAC,cAAMX,KAAEW,GAAE,OAAO;AAAQ,UAAE,mBAAiBX,OAAI,EAAE,iBAAeA,IAAES,GAAE;AAAA,MAAE,GAAGC,EAAC,CAAC;AAAE,QAAE,WAAW,GAAEL,EAAC;AAAA,IAAC;AAAA,EAAC,GAAG,CAAC;AAAE,IAAE,WAAWA,EAAC;AAAC;AAAC,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,SAAO,CAAC,IAAE,UAAS,EAAE,EAAE,YAAU,CAAC,IAAE,aAAY,EAAE,EAAE,sBAAoB,CAAC,IAAE;AAAqB,EAAE,MAAI,IAAE,CAAC,EAAE;", "names": ["l", "t", "r", "f", "s", "c", "a", "p", "c", "t", "g", "d", "l", "s", "a", "E", "f", "m", "r", "p", "e"]}