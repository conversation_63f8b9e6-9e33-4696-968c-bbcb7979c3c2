{"version": 3, "sources": ["../../@arcgis/core/core/accessorSupport/decorators/enumeration.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{JSONMap as e}from\"../../jsonMap.js\";import{property as n}from\"./property.js\";function o(o,r={}){const a=o instanceof e?o:new e(o,r),l={type:r?.ignoreUnknown??1?a.apiValues:String,json:{type:a.jsonValues,read:!r?.readOnly&&{reader:a.read},write:{writer:a.write}}};return void 0!==r?.readOnly&&(l.readOnly=!!r.readOnly),void 0!==r?.default&&(l.json.default=r.default),void 0!==r?.name&&(l.json.name=r.name),void 0!==r?.nonNullable&&(l.nonNullable=r.nonNullable),n(l)}export{o as enumeration};\n"], "mappings": ";;;;;;;;AAIoF,SAAS,EAAEA,IAAE,IAAE,CAAC,GAAE;AAAC,QAAM,IAAEA,cAAa,IAAEA,KAAE,IAAI,EAAEA,IAAE,CAAC,GAAE,IAAE,EAAC,OAAK,uBAAG,kBAAe,IAAE,EAAE,YAAU,QAAO,MAAK,EAAC,MAAK,EAAE,YAAW,MAAK,EAAC,uBAAG,aAAU,EAAC,QAAO,EAAE,KAAI,GAAE,OAAM,EAAC,QAAO,EAAE,MAAK,EAAC,EAAC;AAAE,SAAO,YAAS,uBAAG,cAAW,EAAE,WAAS,CAAC,CAAC,EAAE,WAAU,YAAS,uBAAG,aAAU,EAAE,KAAK,UAAQ,EAAE,UAAS,YAAS,uBAAG,UAAO,EAAE,KAAK,OAAK,EAAE,OAAM,YAAS,uBAAG,iBAAc,EAAE,cAAY,EAAE,cAAa,EAAE,CAAC;AAAC;", "names": ["o"]}