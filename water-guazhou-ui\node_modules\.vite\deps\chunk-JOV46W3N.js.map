{"version": 3, "sources": ["../../@arcgis/core/core/ObservableChangesType.js", "../../@arcgis/core/core/Collection.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar E;!function(E){E[E.ADD=1]=\"ADD\",E[E.REMOVE=2]=\"REMOVE\",E[E.MOVE=4]=\"MOVE\"}(E||(E={}));export{E as ObservableChangesType};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import t from\"./ArrayPool.js\";import s from\"./Evented.js\";import{clone as i}from\"./lang.js\";import{assumeNonNull as r}from\"./maybe.js\";import n from\"./ObjectPool.js\";import{ObservableChangesType as h}from\"./ObservableChangesType.js\";import{schedule as o}from\"./scheduling.js\";import{property as l}from\"./accessorSupport/decorators/property.js\";import{ensureType as a,ensureOneOfType as c}from\"./accessorSupport/ensureType.js\";import{shared as f}from\"./accessorSupport/decorators/shared.js\";import{subclass as _}from\"./accessorSupport/decorators/subclass.js\";import{trackAccess as m}from\"./accessorSupport/tracking.js\";import{SimpleObservable as u}from\"./accessorSupport/tracking/SimpleObservable.js\";var g;class p{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(e){this.defaultPrevented=!1,this.item=e}}const b=new n(p,void 0,(e=>{e.item=null,e.target=null,e.defaultPrevented=!1,e.cancellable=!1})),d=()=>{};function v(e){return e?e instanceof V?e.toArray():e.length?Array.prototype.slice.apply(e):[]:[]}function E(e){if(e&&e.length)return e[0]}function y(e,t,s,i){const r=Math.min(e.length-s,t.length-i);let n=0;for(;n<r&&e[s+n]===t[i+n];)n++;return n}function C(e,t,s,i){t&&t.forEach(((t,r,n)=>{e.push(t),C(e,s.call(i,t,r,n),s,i)}))}const A=new Set,O=new Set,M=new Set,D=new Map;let x=0,V=g=class extends s.EventedAccessor{static isCollection(e){return null!=e&&e instanceof g}constructor(e){super(e),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new u,this.length=0,this._items=[],Object.defineProperty(this,\"uid\",{value:x++})}normalizeCtorArgs(e){return e?Array.isArray(e)||e instanceof g?{items:e}:e:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return m(this._observable),this._items}set items(e){this._emitBeforeChanges(h.ADD)||(this._splice(0,this.length,v(e)),this._emitAfterChanges(h.ADD))}hasEventListener(e){return\"change\"===e?this._chgListeners.length>0:this._emitter.hasEventListener(e)}on(e,t){if(\"change\"===e){const e=this._chgListeners,s={removed:!1,callback:t};return e.push(s),this._notifications&&this._notifications.push({listeners:e.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=d,s.removed=!0,e.splice(e.indexOf(s),1)}}}return this._emitter.on(e,t)}once(e,t){const s=this.on(e,t);return{remove(){s.remove()}}}add(e,t){if(m(this._observable),this._emitBeforeChanges(h.ADD))return this;const s=this.getNextIndex(t??null);return this._splice(s,0,[e]),this._emitAfterChanges(h.ADD),this}addMany(e,t=this._items.length){if(m(this._observable),!e||!e.length)return this;if(this._emitBeforeChanges(h.ADD))return this;const s=this.getNextIndex(t);return this._splice(s,0,v(e)),this._emitAfterChanges(h.ADD),this}at(e){if(m(this._observable),(e=Math.trunc(e)||0)<0&&(e+=this.length),!(e<0||e>=this.length))return this._items[e]}removeAll(){if(m(this._observable),!this.length||this._emitBeforeChanges(h.REMOVE))return[];const e=this._splice(0,this.length)||[];return this._emitAfterChanges(h.REMOVE),e}clone(){return m(this._observable),this._createNewInstance({items:this._items.map(i)})}concat(...e){m(this._observable);const t=e.map(v);return this._createNewInstance({items:this._items.concat(...t)})}drain(e,t){if(m(this._observable),!this.length||this._emitBeforeChanges(h.REMOVE))return;const s=r(this._splice(0,this.length)),i=s.length;for(let r=0;r<i;r++)e.call(t,s[r],r,s);this._emitAfterChanges(h.REMOVE)}every(e,t){return m(this._observable),this._items.every(e,t)}filter(e,t){let s;return m(this._observable),s=2===arguments.length?this._items.filter(e,t):this._items.filter(e),this._createNewInstance({items:s})}find(e,t){return m(this._observable),this._items.find(e,t)}findIndex(e,t){return m(this._observable),this._items.findIndex(e,t)}flatten(e,t){m(this._observable);const s=[];return C(s,this,e,t),new g(s)}forEach(e,t){return m(this._observable),this._items.forEach(e,t)}getItemAt(e){return m(this._observable),this._items[e]}getNextIndex(e){m(this._observable);const t=this.length;return(e=e??t)<0?e=0:e>t&&(e=t),e}includes(e,t=0){return m(this._observable),this._items.includes(e,t)}indexOf(e,t=0){return m(this._observable),this._items.indexOf(e,t)}join(e=\",\"){return m(this._observable),this._items.join(e)}lastIndexOf(e,t=this.length-1){return m(this._observable),this._items.lastIndexOf(e,t)}map(e,t){m(this._observable);const s=this._items.map(e,t);return new g({items:s})}reorder(e,t=this.length-1){m(this._observable);const s=this.indexOf(e);if(-1!==s){if(t<0?t=0:t>=this.length&&(t=this.length-1),s!==t){if(this._emitBeforeChanges(h.MOVE))return e;this._splice(s,1),this._splice(t,0,[e]),this._emitAfterChanges(h.MOVE)}return e}}pop(){if(m(this._observable),!this.length||this._emitBeforeChanges(h.REMOVE))return;const e=E(this._splice(this.length-1,1));return this._emitAfterChanges(h.REMOVE),e}push(...e){return m(this._observable),this._emitBeforeChanges(h.ADD)||(this._splice(this.length,0,e),this._emitAfterChanges(h.ADD)),this.length}reduce(e,t){m(this._observable);const s=this._items;return 2===arguments.length?s.reduce(e,t):s.reduce(e)}reduceRight(e,t){m(this._observable);const s=this._items;return 2===arguments.length?s.reduceRight(e,t):s.reduceRight(e)}remove(e){return m(this._observable),this.removeAt(this.indexOf(e))}removeAt(e){if(m(this._observable),e<0||e>=this.length||this._emitBeforeChanges(h.REMOVE))return;const t=E(this._splice(e,1));return this._emitAfterChanges(h.REMOVE),t}removeMany(e){if(m(this._observable),!e||!e.length||this._emitBeforeChanges(h.REMOVE))return[];const t=e instanceof g?e.toArray():e,s=this._items,i=[],r=t.length;for(let n=0;n<r;n++){const e=t[n],r=s.indexOf(e);if(r>-1){const e=1+y(t,s,n+1,r+1),h=this._splice(r,e);h&&h.length>0&&i.push.apply(i,h),n+=e-1}}return this._emitAfterChanges(h.REMOVE),i}reverse(){if(m(this._observable),this._emitBeforeChanges(h.MOVE))return this;const e=this._splice(0,this.length);return e&&(e.reverse(),this._splice(0,0,e)),this._emitAfterChanges(h.MOVE),this}shift(){if(m(this._observable),!this.length||this._emitBeforeChanges(h.REMOVE))return;const e=E(this._splice(0,1));return this._emitAfterChanges(h.REMOVE),e}slice(e=0,t=this.length){return m(this._observable),this._createNewInstance({items:this._items.slice(e,t)})}some(e,t){return m(this._observable),this._items.some(e,t)}sort(e){if(m(this._observable),!this.length||this._emitBeforeChanges(h.MOVE))return this;const t=r(this._splice(0,this.length));return arguments.length?t.sort(e):t.sort(),this._splice(0,0,t),this._emitAfterChanges(h.MOVE),this}splice(e,t,...s){m(this._observable);const i=(t?h.REMOVE:0)|(s.length?h.ADD:0);if(this._emitBeforeChanges(i))return[];const r=this._splice(e,t,s)||[];return this._emitAfterChanges(i),r}toArray(){return m(this._observable),this._items.slice()}toJSON(){return m(this._observable),this.toArray()}toLocaleString(){return m(this._observable),this._items.toLocaleString()}toString(){return m(this._observable),this._items.toString()}unshift(...e){return m(this._observable),!e.length||this._emitBeforeChanges(h.ADD)||(this._splice(0,0,e),this._emitAfterChanges(h.ADD)),this.length}_createNewInstance(e){return new this.constructor(e)}_splice(e,t,s){const i=this._items,r=this.itemType;let n,h;if(!this._notifications&&this.hasEventListener(\"change\")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=o((()=>this._dispatchChange()))),t){if(h=i.splice(e,t),this.hasEventListener(\"before-remove\")){const t=b.acquire();t.target=this,t.cancellable=!0;for(let s=0,r=h.length;s<r;s++)n=h[s],t.reset(n),this.emit(\"before-remove\",t),t.defaultPrevented&&(h.splice(s,1),i.splice(e,0,n),e+=1,s-=1,r-=1);b.release(t)}if(this.length=this._items.length,this.hasEventListener(\"after-remove\")){const e=b.acquire();e.target=this,e.cancellable=!1;const t=h.length;for(let s=0;s<t;s++)e.reset(h[s]),this.emit(\"after-remove\",e);b.release(e)}}if(s&&s.length){if(r){const e=[];for(const t of s){const s=r.ensureType(t);null==s&&null!=t||e.push(s)}s=e}const t=this.hasEventListener(\"before-add\"),n=this.hasEventListener(\"after-add\"),h=e===this.length;if(t||n){const r=b.acquire();r.target=this,r.cancellable=!0;const o=b.acquire();o.target=this,o.cancellable=!1;for(const l of s)t?(r.reset(l),this.emit(\"before-add\",r),r.defaultPrevented||(h?i.push(l):i.splice(e++,0,l),this._set(\"length\",i.length),n&&(o.reset(l),this.emit(\"after-add\",o)))):(h?i.push(l):i.splice(e++,0,l),this._set(\"length\",i.length),o.reset(l),this.emit(\"after-add\",o));b.release(o),b.release(r)}else{if(h)for(const e of s)i.push(e);else i.splice(e,0,...s);this._set(\"length\",i.length)}}return(s&&s.length||h&&h.length)&&this._notifyChangeEvent(s,h),h}_emitBeforeChanges(e){let t=!1;if(this.hasEventListener(\"before-changes\")){const s=b.acquire();s.target=this,s.cancellable=!0,s.type=e,this.emit(\"before-changes\",s),t=s.defaultPrevented,b.release(s)}return t}_emitAfterChanges(e){if(this.hasEventListener(\"after-changes\")){const t=b.acquire();t.target=this,t.cancellable=!1,t.type=e,this.emit(\"after-changes\",t),b.release(t)}this._observable.notify()}_notifyChangeEvent(e,t){this.hasEventListener(\"change\")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:e,removed:t})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const e=this._notifications;this._notifications=null;for(const s of e){const e=s.changes;A.clear(),O.clear(),M.clear();for(const{added:t,removed:s}of e){if(t)if(0===M.size&&0===O.size)for(const e of t)A.add(e);else for(const e of t)O.has(e)?(M.add(e),O.delete(e)):M.has(e)||A.add(e);if(s)if(0===M.size&&0===A.size)for(const e of s)O.add(e);else for(const e of s)A.has(e)?A.delete(e):(M.delete(e),O.add(e))}const i=t.acquire();A.forEach((e=>{i.push(e)}));const r=t.acquire();O.forEach((e=>{r.push(e)}));const n=this._items,h=s.items,o=t.acquire();if(M.forEach((e=>{h.indexOf(e)!==n.indexOf(e)&&o.push(e)})),s.listeners&&(i.length||r.length||o.length)){const e={target:this,added:i,removed:r,moved:o},t=s.listeners.length;for(let i=0;i<t;i++){const t=s.listeners[i];t.removed||t.callback.call(this,e)}}t.release(i),t.release(r),t.release(o)}A.clear(),O.clear(),M.clear()}};V.ofType=t=>{if(!t)return g;if(D.has(t))return D.get(t);let s=null;if(\"function\"==typeof t)s=t.prototype.declaredClass;else if(t.base)s=t.base.prototype.declaredClass;else for(const e in t.typeMap){const i=t.typeMap[e].prototype.declaredClass;s?s+=` | ${i}`:s=i}let i=class extends g{};return e([f({Type:t,ensureType:\"function\"==typeof t?a(t):c(t)})],i.prototype,\"itemType\",void 0),i=e([_(`esri.core.Collection<${s}>`)],i),D.set(t,i),i},e([l()],V.prototype,\"length\",void 0),e([l()],V.prototype,\"items\",null),V=g=e([_(\"esri.core.Collection\")],V);const j=V;export{j as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAI;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,MAAI,CAAC,IAAE,OAAMA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,OAAK,CAAC,IAAE;AAAM,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACA+oB,IAAI;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,SAAO,MAAK,KAAK,cAAY,OAAG,KAAK,mBAAiB,OAAG,KAAK,OAAK,QAAO,KAAK,OAAK;AAAA,EAAM;AAAA,EAAC,iBAAgB;AAAC,SAAK,gBAAc,KAAK,mBAAiB;AAAA,EAAG;AAAA,EAAC,MAAMC,IAAE;AAAC,SAAK,mBAAiB,OAAG,KAAK,OAAKA;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAE,IAAID,GAAED,IAAE,QAAQ,CAAAC,OAAG;AAAC,EAAAA,GAAE,OAAK,MAAKA,GAAE,SAAO,MAAKA,GAAE,mBAAiB,OAAGA,GAAE,cAAY;AAAE,CAAE;AAA9F,IAAgG,IAAE,MAAI;AAAC;AAAE,SAASE,GAAEF,IAAE;AAAC,SAAOA,KAAEA,cAAa,IAAEA,GAAE,QAAQ,IAAEA,GAAE,SAAO,MAAM,UAAU,MAAM,MAAMA,EAAC,IAAE,CAAC,IAAE,CAAC;AAAC;AAAC,SAASG,GAAEH,IAAE;AAAC,MAAGA,MAAGA,GAAE,OAAO,QAAOA,GAAE,CAAC;AAAC;AAAC,SAASI,GAAEJ,IAAEK,IAAEC,IAAEC,IAAE;AAAC,QAAM,IAAE,KAAK,IAAIP,GAAE,SAAOM,IAAED,GAAE,SAAOE,EAAC;AAAE,MAAIC,KAAE;AAAE,SAAKA,KAAE,KAAGR,GAAEM,KAAEE,EAAC,MAAIH,GAAEE,KAAEC,EAAC,IAAG,CAAAA;AAAI,SAAOA;AAAC;AAAC,SAAS,EAAER,IAAEK,IAAEC,IAAEC,IAAE;AAAC,EAAAF,MAAGA,GAAE,QAAS,CAACA,IAAE,GAAEG,OAAI;AAAC,IAAAR,GAAE,KAAKK,EAAC,GAAE,EAAEL,IAAEM,GAAE,KAAKC,IAAEF,IAAE,GAAEG,EAAC,GAAEF,IAAEC,EAAC;AAAA,EAAC,CAAE;AAAC;AAAC,IAAM,IAAE,oBAAI;AAAZ,IAAgB,IAAE,oBAAI;AAAtB,IAA0B,IAAE,oBAAI;AAAhC,IAAoC,IAAE,oBAAI;AAAI,IAAI,IAAE;AAAN,IAAQ,IAAE,IAAE,cAAc,EAAE,gBAAe;AAAA,EAAC,OAAO,aAAaP,IAAE;AAAC,WAAO,QAAMA,MAAGA,cAAa;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,iBAAe,MAAK,KAAK,SAAO,MAAK,KAAK,cAAY,IAAI,KAAE,KAAK,SAAO,GAAE,KAAK,SAAO,CAAC,GAAE,OAAO,eAAe,MAAK,OAAM,EAAC,OAAM,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,WAAOA,KAAE,MAAM,QAAQA,EAAC,KAAGA,cAAa,IAAE,EAAC,OAAMA,GAAC,IAAEA,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,UAAU;AAAA,EAAC;AAAA,EAAC,EAAE,OAAO,QAAQ,IAAG;AAAC,WAAM,KAAK;AAAA,EAAK;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,mBAAmB,EAAE,GAAG,MAAI,KAAK,QAAQ,GAAE,KAAK,QAAOE,GAAEF,EAAC,CAAC,GAAE,KAAK,kBAAkB,EAAE,GAAG;AAAA,EAAE;AAAA,EAAC,iBAAiBA,IAAE;AAAC,WAAM,aAAWA,KAAE,KAAK,cAAc,SAAO,IAAE,KAAK,SAAS,iBAAiBA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEK,IAAE;AAAC,QAAG,aAAWL,IAAE;AAAC,YAAMA,KAAE,KAAK,eAAcM,KAAE,EAAC,SAAQ,OAAG,UAASD,GAAC;AAAE,aAAOL,GAAE,KAAKM,EAAC,GAAE,KAAK,kBAAgB,KAAK,eAAe,KAAK,EAAC,WAAUN,GAAE,MAAM,GAAE,OAAM,KAAK,OAAO,MAAM,GAAE,SAAQ,CAAC,EAAC,CAAC,GAAE,EAAC,SAAQ;AAAC,aAAK,SAAO,GAAEM,GAAE,UAAQ,MAAGN,GAAE,OAAOA,GAAE,QAAQM,EAAC,GAAE,CAAC;AAAA,MAAC,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK,SAAS,GAAGN,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKL,IAAEK,IAAE;AAAC,UAAMC,KAAE,KAAK,GAAGN,IAAEK,EAAC;AAAE,WAAM,EAAC,SAAQ;AAAC,MAAAC,GAAE,OAAO;AAAA,IAAC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIN,IAAEK,IAAE;AAAC,QAAG,EAAE,KAAK,WAAW,GAAE,KAAK,mBAAmB,EAAE,GAAG,EAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,aAAaD,MAAG,IAAI;AAAE,WAAO,KAAK,QAAQC,IAAE,GAAE,CAACN,EAAC,CAAC,GAAE,KAAK,kBAAkB,EAAE,GAAG,GAAE;AAAA,EAAI;AAAA,EAAC,QAAQA,IAAEK,KAAE,KAAK,OAAO,QAAO;AAAC,QAAG,EAAE,KAAK,WAAW,GAAE,CAACL,MAAG,CAACA,GAAE,OAAO,QAAO;AAAK,QAAG,KAAK,mBAAmB,EAAE,GAAG,EAAE,QAAO;AAAK,UAAMM,KAAE,KAAK,aAAaD,EAAC;AAAE,WAAO,KAAK,QAAQC,IAAE,GAAEJ,GAAEF,EAAC,CAAC,GAAE,KAAK,kBAAkB,EAAE,GAAG,GAAE;AAAA,EAAI;AAAA,EAAC,GAAGA,IAAE;AAAC,QAAG,EAAE,KAAK,WAAW,IAAGA,KAAE,KAAK,MAAMA,EAAC,KAAG,KAAG,MAAIA,MAAG,KAAK,SAAQ,EAAEA,KAAE,KAAGA,MAAG,KAAK,QAAQ,QAAO,KAAK,OAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAG,EAAE,KAAK,WAAW,GAAE,CAAC,KAAK,UAAQ,KAAK,mBAAmB,EAAE,MAAM,EAAE,QAAM,CAAC;AAAE,UAAMA,KAAE,KAAK,QAAQ,GAAE,KAAK,MAAM,KAAG,CAAC;AAAE,WAAO,KAAK,kBAAkB,EAAE,MAAM,GAAEA;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,mBAAmB,EAAC,OAAM,KAAK,OAAO,IAAI,CAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,MAAE,KAAK,WAAW;AAAE,UAAMK,KAAEL,GAAE,IAAIE,EAAC;AAAE,WAAO,KAAK,mBAAmB,EAAC,OAAM,KAAK,OAAO,OAAO,GAAGG,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAML,IAAEK,IAAE;AAAC,QAAG,EAAE,KAAK,WAAW,GAAE,CAAC,KAAK,UAAQ,KAAK,mBAAmB,EAAE,MAAM,EAAE;AAAO,UAAMC,KAAE,EAAE,KAAK,QAAQ,GAAE,KAAK,MAAM,CAAC,GAAEC,KAAED,GAAE;AAAO,aAAQ,IAAE,GAAE,IAAEC,IAAE,IAAI,CAAAP,GAAE,KAAKK,IAAEC,GAAE,CAAC,GAAE,GAAEA,EAAC;AAAE,SAAK,kBAAkB,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAMN,IAAEK,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,MAAML,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOL,IAAEK,IAAE;AAAC,QAAIC;AAAE,WAAO,EAAE,KAAK,WAAW,GAAEA,KAAE,MAAI,UAAU,SAAO,KAAK,OAAO,OAAON,IAAEK,EAAC,IAAE,KAAK,OAAO,OAAOL,EAAC,GAAE,KAAK,mBAAmB,EAAC,OAAMM,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKN,IAAEK,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,KAAKL,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUL,IAAEK,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,UAAUL,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQL,IAAEK,IAAE;AAAC,MAAE,KAAK,WAAW;AAAE,UAAMC,KAAE,CAAC;AAAE,WAAO,EAAEA,IAAE,MAAKN,IAAEK,EAAC,GAAE,IAAI,EAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQN,IAAEK,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,QAAQL,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUL,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,MAAE,KAAK,WAAW;AAAE,UAAMK,KAAE,KAAK;AAAO,YAAOL,KAAEA,MAAGK,MAAG,IAAEL,KAAE,IAAEA,KAAEK,OAAIL,KAAEK,KAAGL;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEK,KAAE,GAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,SAASL,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQL,IAAEK,KAAE,GAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,QAAQL,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKL,KAAE,KAAI;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEK,KAAE,KAAK,SAAO,GAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,YAAYL,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIL,IAAEK,IAAE;AAAC,MAAE,KAAK,WAAW;AAAE,UAAMC,KAAE,KAAK,OAAO,IAAIN,IAAEK,EAAC;AAAE,WAAO,IAAI,EAAE,EAAC,OAAMC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQN,IAAEK,KAAE,KAAK,SAAO,GAAE;AAAC,MAAE,KAAK,WAAW;AAAE,UAAMC,KAAE,KAAK,QAAQN,EAAC;AAAE,QAAG,OAAKM,IAAE;AAAC,UAAGD,KAAE,IAAEA,KAAE,IAAEA,MAAG,KAAK,WAASA,KAAE,KAAK,SAAO,IAAGC,OAAID,IAAE;AAAC,YAAG,KAAK,mBAAmB,EAAE,IAAI,EAAE,QAAOL;AAAE,aAAK,QAAQM,IAAE,CAAC,GAAE,KAAK,QAAQD,IAAE,GAAE,CAACL,EAAC,CAAC,GAAE,KAAK,kBAAkB,EAAE,IAAI;AAAA,MAAC;AAAC,aAAOA;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAK;AAAC,QAAG,EAAE,KAAK,WAAW,GAAE,CAAC,KAAK,UAAQ,KAAK,mBAAmB,EAAE,MAAM,EAAE;AAAO,UAAMA,KAAEG,GAAE,KAAK,QAAQ,KAAK,SAAO,GAAE,CAAC,CAAC;AAAE,WAAO,KAAK,kBAAkB,EAAE,MAAM,GAAEH;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,mBAAmB,EAAE,GAAG,MAAI,KAAK,QAAQ,KAAK,QAAO,GAAEA,EAAC,GAAE,KAAK,kBAAkB,EAAE,GAAG,IAAG,KAAK;AAAA,EAAM;AAAA,EAAC,OAAOA,IAAEK,IAAE;AAAC,MAAE,KAAK,WAAW;AAAE,UAAMC,KAAE,KAAK;AAAO,WAAO,MAAI,UAAU,SAAOA,GAAE,OAAON,IAAEK,EAAC,IAAEC,GAAE,OAAON,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEK,IAAE;AAAC,MAAE,KAAK,WAAW;AAAE,UAAMC,KAAE,KAAK;AAAO,WAAO,MAAI,UAAU,SAAOA,GAAE,YAAYN,IAAEK,EAAC,IAAEC,GAAE,YAAYN,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,SAAS,KAAK,QAAQA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,QAAG,EAAE,KAAK,WAAW,GAAEA,KAAE,KAAGA,MAAG,KAAK,UAAQ,KAAK,mBAAmB,EAAE,MAAM,EAAE;AAAO,UAAMK,KAAEF,GAAE,KAAK,QAAQH,IAAE,CAAC,CAAC;AAAE,WAAO,KAAK,kBAAkB,EAAE,MAAM,GAAEK;AAAA,EAAC;AAAA,EAAC,WAAWL,IAAE;AAAC,QAAG,EAAE,KAAK,WAAW,GAAE,CAACA,MAAG,CAACA,GAAE,UAAQ,KAAK,mBAAmB,EAAE,MAAM,EAAE,QAAM,CAAC;AAAE,UAAMK,KAAEL,cAAa,IAAEA,GAAE,QAAQ,IAAEA,IAAEM,KAAE,KAAK,QAAOC,KAAE,CAAC,GAAE,IAAEF,GAAE;AAAO,aAAQG,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMR,KAAEK,GAAEG,EAAC,GAAEC,KAAEH,GAAE,QAAQN,EAAC;AAAE,UAAGS,KAAE,IAAG;AAAC,cAAMT,KAAE,IAAEI,GAAEC,IAAEC,IAAEE,KAAE,GAAEC,KAAE,CAAC,GAAE,IAAE,KAAK,QAAQA,IAAET,EAAC;AAAE,aAAG,EAAE,SAAO,KAAGO,GAAE,KAAK,MAAMA,IAAE,CAAC,GAAEC,MAAGR,KAAE;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO,KAAK,kBAAkB,EAAE,MAAM,GAAEO;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,EAAE,KAAK,WAAW,GAAE,KAAK,mBAAmB,EAAE,IAAI,EAAE,QAAO;AAAK,UAAMP,KAAE,KAAK,QAAQ,GAAE,KAAK,MAAM;AAAE,WAAOA,OAAIA,GAAE,QAAQ,GAAE,KAAK,QAAQ,GAAE,GAAEA,EAAC,IAAG,KAAK,kBAAkB,EAAE,IAAI,GAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,QAAG,EAAE,KAAK,WAAW,GAAE,CAAC,KAAK,UAAQ,KAAK,mBAAmB,EAAE,MAAM,EAAE;AAAO,UAAMA,KAAEG,GAAE,KAAK,QAAQ,GAAE,CAAC,CAAC;AAAE,WAAO,KAAK,kBAAkB,EAAE,MAAM,GAAEH;AAAA,EAAC;AAAA,EAAC,MAAMA,KAAE,GAAEK,KAAE,KAAK,QAAO;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,mBAAmB,EAAC,OAAM,KAAK,OAAO,MAAML,IAAEK,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKL,IAAEK,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,KAAKL,IAAEK,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKL,IAAE;AAAC,QAAG,EAAE,KAAK,WAAW,GAAE,CAAC,KAAK,UAAQ,KAAK,mBAAmB,EAAE,IAAI,EAAE,QAAO;AAAK,UAAMK,KAAE,EAAE,KAAK,QAAQ,GAAE,KAAK,MAAM,CAAC;AAAE,WAAO,UAAU,SAAOA,GAAE,KAAKL,EAAC,IAAEK,GAAE,KAAK,GAAE,KAAK,QAAQ,GAAE,GAAEA,EAAC,GAAE,KAAK,kBAAkB,EAAE,IAAI,GAAE;AAAA,EAAI;AAAA,EAAC,OAAOL,IAAEK,OAAKC,IAAE;AAAC,MAAE,KAAK,WAAW;AAAE,UAAMC,MAAGF,KAAE,EAAE,SAAO,MAAIC,GAAE,SAAO,EAAE,MAAI;AAAG,QAAG,KAAK,mBAAmBC,EAAC,EAAE,QAAM,CAAC;AAAE,UAAM,IAAE,KAAK,QAAQP,IAAEK,IAAEC,EAAC,KAAG,CAAC;AAAE,WAAO,KAAK,kBAAkBC,EAAC,GAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,MAAM;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,eAAe;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,KAAK,OAAO,SAAS;AAAA,EAAC;AAAA,EAAC,WAAWP,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,GAAE,CAACA,GAAE,UAAQ,KAAK,mBAAmB,EAAE,GAAG,MAAI,KAAK,QAAQ,GAAE,GAAEA,EAAC,GAAE,KAAK,kBAAkB,EAAE,GAAG,IAAG,KAAK;AAAA,EAAM;AAAA,EAAC,mBAAmBA,IAAE;AAAC,WAAO,IAAI,KAAK,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEK,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,QAAO,IAAE,KAAK;AAAS,QAAIC,IAAE;AAAE,QAAG,CAAC,KAAK,kBAAgB,KAAK,iBAAiB,QAAQ,MAAI,KAAK,iBAAe,CAAC,EAAC,WAAU,KAAK,cAAc,MAAM,GAAE,OAAM,KAAK,OAAO,MAAM,GAAE,SAAQ,CAAC,EAAC,CAAC,GAAE,KAAK,UAAQ,KAAK,OAAO,OAAO,GAAE,KAAK,SAAO,EAAG,MAAI,KAAK,gBAAgB,CAAE,IAAGH,IAAE;AAAC,UAAG,IAAEE,GAAE,OAAOP,IAAEK,EAAC,GAAE,KAAK,iBAAiB,eAAe,GAAE;AAAC,cAAMA,KAAEJ,GAAE,QAAQ;AAAE,QAAAI,GAAE,SAAO,MAAKA,GAAE,cAAY;AAAG,iBAAQC,KAAE,GAAEG,KAAE,EAAE,QAAOH,KAAEG,IAAEH,KAAI,CAAAE,KAAE,EAAEF,EAAC,GAAED,GAAE,MAAMG,EAAC,GAAE,KAAK,KAAK,iBAAgBH,EAAC,GAAEA,GAAE,qBAAmB,EAAE,OAAOC,IAAE,CAAC,GAAEC,GAAE,OAAOP,IAAE,GAAEQ,EAAC,GAAER,MAAG,GAAEM,MAAG,GAAEG,MAAG;AAAG,QAAAR,GAAE,QAAQI,EAAC;AAAA,MAAC;AAAC,UAAG,KAAK,SAAO,KAAK,OAAO,QAAO,KAAK,iBAAiB,cAAc,GAAE;AAAC,cAAML,KAAEC,GAAE,QAAQ;AAAE,QAAAD,GAAE,SAAO,MAAKA,GAAE,cAAY;AAAG,cAAMK,KAAE,EAAE;AAAO,iBAAQC,KAAE,GAAEA,KAAED,IAAEC,KAAI,CAAAN,GAAE,MAAM,EAAEM,EAAC,CAAC,GAAE,KAAK,KAAK,gBAAeN,EAAC;AAAE,QAAAC,GAAE,QAAQD,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAGM,MAAGA,GAAE,QAAO;AAAC,UAAG,GAAE;AAAC,cAAMN,KAAE,CAAC;AAAE,mBAAUK,MAAKC,IAAE;AAAC,gBAAMA,KAAE,EAAE,WAAWD,EAAC;AAAE,kBAAMC,MAAG,QAAMD,MAAGL,GAAE,KAAKM,EAAC;AAAA,QAAC;AAAC,QAAAA,KAAEN;AAAA,MAAC;AAAC,YAAMK,KAAE,KAAK,iBAAiB,YAAY,GAAEG,KAAE,KAAK,iBAAiB,WAAW,GAAEE,KAAEV,OAAI,KAAK;AAAO,UAAGK,MAAGG,IAAE;AAAC,cAAMC,KAAER,GAAE,QAAQ;AAAE,QAAAQ,GAAE,SAAO,MAAKA,GAAE,cAAY;AAAG,cAAM,IAAER,GAAE,QAAQ;AAAE,UAAE,SAAO,MAAK,EAAE,cAAY;AAAG,mBAAU,KAAKK,GAAE,CAAAD,MAAGI,GAAE,MAAM,CAAC,GAAE,KAAK,KAAK,cAAaA,EAAC,GAAEA,GAAE,qBAAmBC,KAAEH,GAAE,KAAK,CAAC,IAAEA,GAAE,OAAOP,MAAI,GAAE,CAAC,GAAE,KAAK,KAAK,UAASO,GAAE,MAAM,GAAEC,OAAI,EAAE,MAAM,CAAC,GAAE,KAAK,KAAK,aAAY,CAAC,QAAME,KAAEH,GAAE,KAAK,CAAC,IAAEA,GAAE,OAAOP,MAAI,GAAE,CAAC,GAAE,KAAK,KAAK,UAASO,GAAE,MAAM,GAAE,EAAE,MAAM,CAAC,GAAE,KAAK,KAAK,aAAY,CAAC;AAAG,QAAAN,GAAE,QAAQ,CAAC,GAAEA,GAAE,QAAQQ,EAAC;AAAA,MAAC,OAAK;AAAC,YAAGC,GAAE,YAAUV,MAAKM,GAAE,CAAAC,GAAE,KAAKP,EAAC;AAAA,YAAO,CAAAO,GAAE,OAAOP,IAAE,GAAE,GAAGM,EAAC;AAAE,aAAK,KAAK,UAASC,GAAE,MAAM;AAAA,MAAC;AAAA,IAAC;AAAC,YAAOD,MAAGA,GAAE,UAAQ,KAAG,EAAE,WAAS,KAAK,mBAAmBA,IAAE,CAAC,GAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBN,IAAE;AAAC,QAAIK,KAAE;AAAG,QAAG,KAAK,iBAAiB,gBAAgB,GAAE;AAAC,YAAMC,KAAEL,GAAE,QAAQ;AAAE,MAAAK,GAAE,SAAO,MAAKA,GAAE,cAAY,MAAGA,GAAE,OAAKN,IAAE,KAAK,KAAK,kBAAiBM,EAAC,GAAED,KAAEC,GAAE,kBAAiBL,GAAE,QAAQK,EAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,kBAAkBL,IAAE;AAAC,QAAG,KAAK,iBAAiB,eAAe,GAAE;AAAC,YAAMK,KAAEJ,GAAE,QAAQ;AAAE,MAAAI,GAAE,SAAO,MAAKA,GAAE,cAAY,OAAGA,GAAE,OAAKL,IAAE,KAAK,KAAK,iBAAgBK,EAAC,GAAEJ,GAAE,QAAQI,EAAC;AAAA,IAAC;AAAC,SAAK,YAAY,OAAO;AAAA,EAAC;AAAA,EAAC,mBAAmBL,IAAEK,IAAE;AAAC,SAAK,iBAAiB,QAAQ,KAAG,KAAK,kBAAgB,KAAK,eAAe,KAAK,eAAe,SAAO,CAAC,EAAE,QAAQ,KAAK,EAAC,OAAML,IAAE,SAAQK,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,QAAG,KAAK,WAAS,KAAK,OAAO,OAAO,GAAE,KAAK,SAAO,OAAM,CAAC,KAAK,eAAe;AAAO,UAAML,KAAE,KAAK;AAAe,SAAK,iBAAe;AAAK,eAAUM,MAAKN,IAAE;AAAC,YAAMA,KAAEM,GAAE;AAAQ,QAAE,MAAM,GAAE,EAAE,MAAM,GAAE,EAAE,MAAM;AAAE,iBAAS,EAAC,OAAMD,IAAE,SAAQC,GAAC,KAAIN,IAAE;AAAC,YAAGK,GAAE,KAAG,MAAI,EAAE,QAAM,MAAI,EAAE,KAAK,YAAUL,MAAKK,GAAE,GAAE,IAAIL,EAAC;AAAA,YAAO,YAAUA,MAAKK,GAAE,GAAE,IAAIL,EAAC,KAAG,EAAE,IAAIA,EAAC,GAAE,EAAE,OAAOA,EAAC,KAAG,EAAE,IAAIA,EAAC,KAAG,EAAE,IAAIA,EAAC;AAAE,YAAGM,GAAE,KAAG,MAAI,EAAE,QAAM,MAAI,EAAE,KAAK,YAAUN,MAAKM,GAAE,GAAE,IAAIN,EAAC;AAAA,YAAO,YAAUA,MAAKM,GAAE,GAAE,IAAIN,EAAC,IAAE,EAAE,OAAOA,EAAC,KAAG,EAAE,OAAOA,EAAC,GAAE,EAAE,IAAIA,EAAC;AAAA,MAAE;AAAC,YAAMO,KAAE,EAAE,QAAQ;AAAE,QAAE,QAAS,CAAAP,OAAG;AAAC,QAAAO,GAAE,KAAKP,EAAC;AAAA,MAAC,CAAE;AAAE,YAAM,IAAE,EAAE,QAAQ;AAAE,QAAE,QAAS,CAAAA,OAAG;AAAC,UAAE,KAAKA,EAAC;AAAA,MAAC,CAAE;AAAE,YAAMQ,KAAE,KAAK,QAAO,IAAEF,GAAE,OAAM,IAAE,EAAE,QAAQ;AAAE,UAAG,EAAE,QAAS,CAAAN,OAAG;AAAC,UAAE,QAAQA,EAAC,MAAIQ,GAAE,QAAQR,EAAC,KAAG,EAAE,KAAKA,EAAC;AAAA,MAAC,CAAE,GAAEM,GAAE,cAAYC,GAAE,UAAQ,EAAE,UAAQ,EAAE,SAAQ;AAAC,cAAMP,KAAE,EAAC,QAAO,MAAK,OAAMO,IAAE,SAAQ,GAAE,OAAM,EAAC,GAAEF,KAAEC,GAAE,UAAU;AAAO,iBAAQC,KAAE,GAAEA,KAAEF,IAAEE,MAAI;AAAC,gBAAMF,KAAEC,GAAE,UAAUC,EAAC;AAAE,UAAAF,GAAE,WAASA,GAAE,SAAS,KAAK,MAAKL,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,QAAE,QAAQO,EAAC,GAAE,EAAE,QAAQ,CAAC,GAAE,EAAE,QAAQ,CAAC;AAAA,IAAC;AAAC,MAAE,MAAM,GAAE,EAAE,MAAM,GAAE,EAAE,MAAM;AAAA,EAAC;AAAC;AAAE,EAAE,SAAO,CAAAF,OAAG;AAAC,MAAG,CAACA,GAAE,QAAO;AAAE,MAAG,EAAE,IAAIA,EAAC,EAAE,QAAO,EAAE,IAAIA,EAAC;AAAE,MAAIC,KAAE;AAAK,MAAG,cAAY,OAAOD,GAAE,CAAAC,KAAED,GAAE,UAAU;AAAA,WAAsBA,GAAE,KAAK,CAAAC,KAAED,GAAE,KAAK,UAAU;AAAA,MAAmB,YAAUL,MAAKK,GAAE,SAAQ;AAAC,UAAME,KAAEF,GAAE,QAAQL,EAAC,EAAE,UAAU;AAAc,IAAAM,KAAEA,MAAG,MAAMC,EAAC,KAAGD,KAAEC;AAAA,EAAC;AAAC,MAAIA,KAAE,cAAc,EAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAACC,GAAE,EAAC,MAAKH,IAAE,YAAW,cAAY,OAAOA,KAAE,EAAEA,EAAC,IAAE,EAAEA,EAAC,EAAC,CAAC,CAAC,GAAEE,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,wBAAwBD,EAAC,GAAG,CAAC,GAAEC,EAAC,GAAE,EAAE,IAAIF,IAAEE,EAAC,GAAEA;AAAC,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["E", "p", "e", "b", "v", "E", "y", "t", "s", "i", "n", "r", "h"]}