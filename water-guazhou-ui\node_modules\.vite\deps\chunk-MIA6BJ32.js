import {
  a as a2,
  n
} from "./chunk-6ENNE6EU.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/layers/mixins/BlendLayer.js
var i = { read: { reader: n }, write: { allowNull: true, writer: a2 } };
var n2 = (t) => {
  let s = class extends t {
    constructor() {
      super(...arguments), this.blendMode = "normal", this.effect = null;
    }
  };
  return e([y({ type: ["average", "color-burn", "color-dodge", "color", "darken", "destination-atop", "destination-in", "destination-out", "destination-over", "difference", "exclusion", "hard-light", "hue", "invert", "lighten", "lighter", "luminosity", "minus", "multiply", "normal", "overlay", "plus", "reflect", "saturation", "screen", "soft-light", "source-atop", "source-in", "source-out", "vivid-light", "xor"], nonNullable: true, json: { read: false, write: false, origins: { "web-map": { read: true, write: true }, "portal-item": { read: true, write: true } } } })], s.prototype, "blendMode", void 0), e([y({ json: { read: false, write: false, origins: { "web-map": i, "portal-item": i } } })], s.prototype, "effect", void 0), s = e([a("esri.layers.mixins.BlendLayer")], s), s;
};

export {
  n2 as n
};
//# sourceMappingURL=chunk-MIA6BJ32.js.map
