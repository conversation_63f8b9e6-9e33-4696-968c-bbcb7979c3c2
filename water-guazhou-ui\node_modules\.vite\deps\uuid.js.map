{"version": 3, "sources": ["../../uuid/dist/esm-browser/rng.js", "../../uuid/dist/esm-browser/regex.js", "../../uuid/dist/esm-browser/validate.js", "../../uuid/dist/esm-browser/stringify.js", "../../uuid/dist/esm-browser/v1.js", "../../uuid/dist/esm-browser/parse.js", "../../uuid/dist/esm-browser/v35.js", "../../uuid/dist/esm-browser/md5.js", "../../uuid/dist/esm-browser/v3.js", "../../uuid/dist/esm-browser/v4.js", "../../uuid/dist/esm-browser/sha1.js", "../../uuid/dist/esm-browser/v5.js", "../../uuid/dist/esm-browser/nil.js", "../../uuid/dist/esm-browser/version.js"], "sourcesContent": ["// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nvar getRandomValues;\nvar rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation. Also,\n    // find the complete implementation of crypto (msCrypto) on IE11.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== 'undefined' && typeof msCrypto.getRandomValues === 'function' && msCrypto.getRandomValues.bind(msCrypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;", "import REGEX from './regex.js';\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\n\nexport default validate;", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nvar byteToHex = [];\n\nfor (var i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).substr(1));\n}\n\nfunction stringify(arr) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  var uuid = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase(); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;", "import rng from './rng.js';\nimport stringify from './stringify.js'; // **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nvar _nodeId;\n\nvar _clockseq; // Previous uuid creation time\n\n\nvar _lastMSecs = 0;\nvar _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details\n\nfunction v1(options, buf, offset) {\n  var i = buf && offset || 0;\n  var b = buf || new Array(16);\n  options = options || {};\n  var node = options.node || _nodeId;\n  var clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n\n  if (node == null || clockseq == null) {\n    var seedBytes = options.random || (options.rng || rng)();\n\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n\n\n  var msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n\n  var nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)\n\n  var dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression\n\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n\n\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  } // Per 4.2.1.2 Throw error if too many uuids are requested\n\n\n  if (nsecs >= 10000) {\n    throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n\n  msecs += 12219292800000; // `time_low`\n\n  var tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff; // `time_mid`\n\n  var tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff; // `time_high_and_version`\n\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n\n  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n\n  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`\n\n  b[i++] = clockseq & 0xff; // `node`\n\n  for (var n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf || stringify(b);\n}\n\nexport default v1;", "import validate from './validate.js';\n\nfunction parse(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  var v;\n  var arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\n\nexport default parse;", "import stringify from './stringify.js';\nimport parse from './parse.js';\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  var bytes = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nexport var DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport var URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function (name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n\n    if (namespace.length !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    var bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (var i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return stringify(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}", "/*\n * Browser-compatible JavaScript MD5\n *\n * Modification of JavaScript MD5\n * https://github.com/blueimp/JavaScript-MD5\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n *\n * Based on\n * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message\n * Digest Algorithm, as defined in RFC 1321.\n * Version 2.2 Copyright (C) <PERSON> 1999 - 2009\n * Other contributors: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for more info.\n */\nfunction md5(bytes) {\n  if (typeof bytes === 'string') {\n    var msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = new Uint8Array(msg.length);\n\n    for (var i = 0; i < msg.length; ++i) {\n      bytes[i] = msg.charCodeAt(i);\n    }\n  }\n\n  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));\n}\n/*\n * Convert an array of little-endian words to an array of bytes\n */\n\n\nfunction md5ToHexEncodedArray(input) {\n  var output = [];\n  var length32 = input.length * 32;\n  var hexTab = '0123456789abcdef';\n\n  for (var i = 0; i < length32; i += 8) {\n    var x = input[i >> 5] >>> i % 32 & 0xff;\n    var hex = parseInt(hexTab.charAt(x >>> 4 & 0x0f) + hexTab.charAt(x & 0x0f), 16);\n    output.push(hex);\n  }\n\n  return output;\n}\n/**\n * Calculate output length with padding and bit length\n */\n\n\nfunction getOutputLength(inputLength8) {\n  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;\n}\n/*\n * Calculate the MD5 of an array of little-endian words, and a bit length.\n */\n\n\nfunction wordsToMd5(x, len) {\n  /* append padding */\n  x[len >> 5] |= 0x80 << len % 32;\n  x[getOutputLength(len) - 1] = len;\n  var a = 1732584193;\n  var b = -271733879;\n  var c = -1732584194;\n  var d = 271733878;\n\n  for (var i = 0; i < x.length; i += 16) {\n    var olda = a;\n    var oldb = b;\n    var oldc = c;\n    var oldd = d;\n    a = md5ff(a, b, c, d, x[i], 7, -680876936);\n    d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);\n    c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);\n    b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);\n    a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);\n    d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);\n    c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);\n    b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);\n    a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);\n    d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);\n    c = md5ff(c, d, a, b, x[i + 10], 17, -42063);\n    b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);\n    a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);\n    d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);\n    c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);\n    b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);\n    a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);\n    d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);\n    c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);\n    b = md5gg(b, c, d, a, x[i], 20, -373897302);\n    a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);\n    d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);\n    c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);\n    b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);\n    a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);\n    d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);\n    c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);\n    b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);\n    a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);\n    d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);\n    c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);\n    b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);\n    a = md5hh(a, b, c, d, x[i + 5], 4, -378558);\n    d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);\n    c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);\n    b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);\n    a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);\n    d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);\n    c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);\n    b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);\n    a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);\n    d = md5hh(d, a, b, c, x[i], 11, -358537222);\n    c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);\n    b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);\n    a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);\n    d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);\n    c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);\n    b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);\n    a = md5ii(a, b, c, d, x[i], 6, -198630844);\n    d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);\n    c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);\n    b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);\n    a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);\n    d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);\n    c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);\n    b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);\n    a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);\n    d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);\n    c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);\n    b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);\n    a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);\n    d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);\n    c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);\n    b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);\n    a = safeAdd(a, olda);\n    b = safeAdd(b, oldb);\n    c = safeAdd(c, oldc);\n    d = safeAdd(d, oldd);\n  }\n\n  return [a, b, c, d];\n}\n/*\n * Convert an array bytes to an array of little-endian words\n * Characters >255 have their high-byte silently ignored.\n */\n\n\nfunction bytesToWords(input) {\n  if (input.length === 0) {\n    return [];\n  }\n\n  var length8 = input.length * 8;\n  var output = new Uint32Array(getOutputLength(length8));\n\n  for (var i = 0; i < length8; i += 8) {\n    output[i >> 5] |= (input[i / 8] & 0xff) << i % 32;\n  }\n\n  return output;\n}\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\n\n\nfunction safeAdd(x, y) {\n  var lsw = (x & 0xffff) + (y & 0xffff);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xffff;\n}\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\n\n\nfunction bitRotateLeft(num, cnt) {\n  return num << cnt | num >>> 32 - cnt;\n}\n/*\n * These functions implement the four basic operations the algorithm uses.\n */\n\n\nfunction md5cmn(q, a, b, x, s, t) {\n  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);\n}\n\nfunction md5ff(a, b, c, d, x, s, t) {\n  return md5cmn(b & c | ~b & d, a, b, x, s, t);\n}\n\nfunction md5gg(a, b, c, d, x, s, t) {\n  return md5cmn(b & d | c & ~d, a, b, x, s, t);\n}\n\nfunction md5hh(a, b, c, d, x, s, t) {\n  return md5cmn(b ^ c ^ d, a, b, x, s, t);\n}\n\nfunction md5ii(a, b, c, d, x, s, t) {\n  return md5cmn(c ^ (b | ~d), a, b, x, s, t);\n}\n\nexport default md5;", "import v35 from './v35.js';\nimport md5 from './md5.js';\nvar v3 = v35('v3', 0x30, md5);\nexport default v3;", "import rng from './rng.js';\nimport stringify from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  options = options || {};\n  var rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (var i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return stringify(rnds);\n}\n\nexport default v4;", "// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n\n    case 1:\n      return x ^ y ^ z;\n\n    case 2:\n      return x & y ^ x & z ^ y & z;\n\n    case 3:\n      return x ^ y ^ z;\n  }\n}\n\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\n\nfunction sha1(bytes) {\n  var K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  var H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n\n  if (typeof bytes === 'string') {\n    var msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n\n    for (var i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n\n  bytes.push(0x80);\n  var l = bytes.length / 4 + 2;\n  var N = Math.ceil(l / 16);\n  var M = new Array(N);\n\n  for (var _i = 0; _i < N; ++_i) {\n    var arr = new Uint32Array(16);\n\n    for (var j = 0; j < 16; ++j) {\n      arr[j] = bytes[_i * 64 + j * 4] << 24 | bytes[_i * 64 + j * 4 + 1] << 16 | bytes[_i * 64 + j * 4 + 2] << 8 | bytes[_i * 64 + j * 4 + 3];\n    }\n\n    M[_i] = arr;\n  }\n\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n\n  for (var _i2 = 0; _i2 < N; ++_i2) {\n    var W = new Uint32Array(80);\n\n    for (var t = 0; t < 16; ++t) {\n      W[t] = M[_i2][t];\n    }\n\n    for (var _t = 16; _t < 80; ++_t) {\n      W[_t] = ROTL(W[_t - 3] ^ W[_t - 8] ^ W[_t - 14] ^ W[_t - 16], 1);\n    }\n\n    var a = H[0];\n    var b = H[1];\n    var c = H[2];\n    var d = H[3];\n    var e = H[4];\n\n    for (var _t2 = 0; _t2 < 80; ++_t2) {\n      var s = Math.floor(_t2 / 20);\n      var T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[_t2] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\n\nexport default sha1;", "import v35 from './v35.js';\nimport sha1 from './sha1.js';\nvar v5 = v35('v5', 0x50, sha1);\nexport default v5;", "export default '00000000-0000-0000-0000-000000000000';", "import validate from './validate.js';\n\nfunction version(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  return parseInt(uuid.substr(14, 1), 16);\n}\n\nexport default version;"], "mappings": ";;;AAGA,IAAI;AACJ,IAAI,QAAQ,IAAI,WAAW,EAAE;AACd,SAAR,MAAuB;AAE5B,MAAI,CAAC,iBAAiB;AAGpB,sBAAkB,OAAO,WAAW,eAAe,OAAO,mBAAmB,OAAO,gBAAgB,KAAK,MAAM,KAAK,OAAO,aAAa,eAAe,OAAO,SAAS,oBAAoB,cAAc,SAAS,gBAAgB,KAAK,QAAQ;AAE/O,QAAI,CAAC,iBAAiB;AACpB,YAAM,IAAI,MAAM,0GAA0G;AAAA,IAC5H;AAAA,EACF;AAEA,SAAO,gBAAgB,KAAK;AAC9B;;;AClBA,IAAO,gBAAQ;;;ACEf,SAAS,SAAS,MAAM;AACtB,SAAO,OAAO,SAAS,YAAY,cAAM,KAAK,IAAI;AACpD;AAEA,IAAO,mBAAQ;;;ACAf,IAAI,YAAY,CAAC;AAEjB,KAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAU,MAAM,IAAI,KAAO,SAAS,EAAE,EAAE,OAAO,CAAC,CAAC;AACnD;AAFS;AAIT,SAAS,UAAU,KAAK;AACtB,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAGjF,MAAI,QAAQ,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,GAAG,YAAY;AAMrgB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACnB,UAAM,UAAU,6BAA6B;AAAA,EAC/C;AAEA,SAAO;AACT;AAEA,IAAO,oBAAQ;;;ACvBf,IAAI;AAEJ,IAAI;AAGJ,IAAI,aAAa;AACjB,IAAI,aAAa;AAEjB,SAAS,GAAG,SAAS,KAAK,QAAQ;AAChC,MAAI,IAAI,OAAO,UAAU;AACzB,MAAI,IAAI,OAAO,IAAI,MAAM,EAAE;AAC3B,YAAU,WAAW,CAAC;AACtB,MAAI,OAAO,QAAQ,QAAQ;AAC3B,MAAI,WAAW,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAInE,MAAI,QAAQ,QAAQ,YAAY,MAAM;AACpC,QAAI,YAAY,QAAQ,WAAW,QAAQ,OAAO,KAAK;AAEvD,QAAI,QAAQ,MAAM;AAEhB,aAAO,UAAU,CAAC,UAAU,CAAC,IAAI,GAAM,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AAAA,IAC7G;AAEA,QAAI,YAAY,MAAM;AAEpB,iBAAW,aAAa,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK;AAAA,IAC9D;AAAA,EACF;AAMA,MAAI,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ,KAAK,IAAI;AAGnE,MAAI,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ,aAAa;AAEvE,MAAI,KAAK,QAAQ,cAAc,QAAQ,cAAc;AAErD,MAAI,KAAK,KAAK,QAAQ,aAAa,QAAW;AAC5C,eAAW,WAAW,IAAI;AAAA,EAC5B;AAIA,OAAK,KAAK,KAAK,QAAQ,eAAe,QAAQ,UAAU,QAAW;AACjE,YAAQ;AAAA,EACV;AAGA,MAAI,SAAS,KAAO;AAClB,UAAM,IAAI,MAAM,iDAAiD;AAAA,EACnE;AAEA,eAAa;AACb,eAAa;AACb,cAAY;AAEZ,WAAS;AAET,MAAI,OAAO,QAAQ,aAAa,MAAQ,SAAS;AACjD,IAAE,GAAG,IAAI,OAAO,KAAK;AACrB,IAAE,GAAG,IAAI,OAAO,KAAK;AACrB,IAAE,GAAG,IAAI,OAAO,IAAI;AACpB,IAAE,GAAG,IAAI,KAAK;AAEd,MAAI,MAAM,QAAQ,aAAc,MAAQ;AACxC,IAAE,GAAG,IAAI,QAAQ,IAAI;AACrB,IAAE,GAAG,IAAI,MAAM;AAEf,IAAE,GAAG,IAAI,QAAQ,KAAK,KAAM;AAE5B,IAAE,GAAG,IAAI,QAAQ,KAAK;AAEtB,IAAE,GAAG,IAAI,aAAa,IAAI;AAE1B,IAAE,GAAG,IAAI,WAAW;AAEpB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,MAAE,IAAI,CAAC,IAAI,KAAK,CAAC;AAAA,EACnB;AAEA,SAAO,OAAO,kBAAU,CAAC;AAC3B;AAEA,IAAO,aAAQ;;;AC5Ff,SAAS,MAAM,MAAM;AACnB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACnB,UAAM,UAAU,cAAc;AAAA,EAChC;AAEA,MAAI;AACJ,MAAI,MAAM,IAAI,WAAW,EAAE;AAE3B,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,OAAO;AAClD,MAAI,CAAC,IAAI,MAAM,KAAK;AACpB,MAAI,CAAC,IAAI,MAAM,IAAI;AACnB,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO;AACnD,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAGb,MAAI,EAAE,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK,gBAAgB;AACnE,MAAI,EAAE,IAAI,IAAI,aAAc;AAC5B,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,IAAI;AACpB,MAAI,EAAE,IAAI,IAAI;AACd,SAAO;AACT;AAEA,IAAO,gBAAQ;;;AC/Bf,SAAS,cAAc,KAAK;AAC1B,QAAM,SAAS,mBAAmB,GAAG,CAAC;AAEtC,MAAI,QAAQ,CAAC;AAEb,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,UAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,EAC9B;AAEA,SAAO;AACT;AAEO,IAAI,MAAM;AACV,IAAI,MAAM;AACF,SAAR,YAAkB,MAAMA,UAAS,UAAU;AAChD,WAAS,aAAa,OAAO,WAAW,KAAK,QAAQ;AACnD,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,cAAc,KAAK;AAAA,IAC7B;AAEA,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,cAAM,SAAS;AAAA,IAC7B;AAEA,QAAI,UAAU,WAAW,IAAI;AAC3B,YAAM,UAAU,kEAAkE;AAAA,IACpF;AAKA,QAAI,QAAQ,IAAI,WAAW,KAAK,MAAM,MAAM;AAC5C,UAAM,IAAI,SAAS;AACnB,UAAM,IAAI,OAAO,UAAU,MAAM;AACjC,YAAQ,SAAS,KAAK;AACtB,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAOA;AAC7B,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAE7B,QAAI,KAAK;AACP,eAAS,UAAU;AAEnB,eAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,kBAAU,KAAK;AAAA,EACxB;AAGA,MAAI;AACF,iBAAa,OAAO;AAAA,EACtB,SAAS,KAAK;AAAA,EAAC;AAGf,eAAa,MAAM;AACnB,eAAa,MAAM;AACnB,SAAO;AACT;;;AC3CA,SAAS,IAAI,OAAO;AAClB,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE5C,YAAQ,IAAI,WAAW,IAAI,MAAM;AAEjC,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,IAC7B;AAAA,EACF;AAEA,SAAO,qBAAqB,WAAW,aAAa,KAAK,GAAG,MAAM,SAAS,CAAC,CAAC;AAC/E;AAMA,SAAS,qBAAqB,OAAO;AACnC,MAAI,SAAS,CAAC;AACd,MAAI,WAAW,MAAM,SAAS;AAC9B,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AACpC,QAAI,IAAI,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK;AACnC,QAAI,MAAM,SAAS,OAAO,OAAO,MAAM,IAAI,EAAI,IAAI,OAAO,OAAO,IAAI,EAAI,GAAG,EAAE;AAC9E,WAAO,KAAK,GAAG;AAAA,EACjB;AAEA,SAAO;AACT;AAMA,SAAS,gBAAgB,cAAc;AACrC,UAAQ,eAAe,OAAO,KAAK,KAAK,KAAK;AAC/C;AAMA,SAAS,WAAW,GAAG,KAAK;AAE1B,IAAE,OAAO,CAAC,KAAK,OAAQ,MAAM;AAC7B,IAAE,gBAAgB,GAAG,IAAI,CAAC,IAAI;AAC9B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI;AACrC,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,MAAM;AAC3C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AAAA,EACrB;AAEA,SAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;AAOA,SAAS,aAAa,OAAO;AAC3B,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,UAAU,MAAM,SAAS;AAC7B,MAAI,SAAS,IAAI,YAAY,gBAAgB,OAAO,CAAC;AAErD,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,WAAO,KAAK,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,QAAS,IAAI;AAAA,EACjD;AAEA,SAAO;AACT;AAOA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,OAAO,IAAI,UAAW,IAAI;AAC9B,MAAI,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAC1C,SAAO,OAAO,KAAK,MAAM;AAC3B;AAMA,SAAS,cAAc,KAAK,KAAK;AAC/B,SAAO,OAAO,MAAM,QAAQ,KAAK;AACnC;AAMA,SAAS,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAC3E;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACxC;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3C;AAEA,IAAO,cAAQ;;;ACpNf,IAAI,KAAK,YAAI,MAAM,IAAM,WAAG;AAC5B,IAAO,aAAQ;;;ACAf,SAAS,GAAG,SAAS,KAAK,QAAQ;AAChC,YAAU,WAAW,CAAC;AACtB,MAAI,OAAO,QAAQ,WAAW,QAAQ,OAAO,KAAK;AAElD,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAC3B,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAE3B,MAAI,KAAK;AACP,aAAS,UAAU;AAEnB,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,SAAS,CAAC,IAAI,KAAK,CAAC;AAAA,IAC1B;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,kBAAU,IAAI;AACvB;AAEA,IAAO,aAAQ;;;ACrBf,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,IAAI,IAAI,CAAC,IAAI;AAAA,IAEtB,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,IAEjB,KAAK;AACH,aAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IAE7B,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,EACnB;AACF;AAEA,SAAS,KAAK,GAAG,GAAG;AAClB,SAAO,KAAK,IAAI,MAAM,KAAK;AAC7B;AAEA,SAAS,KAAK,OAAO;AACnB,MAAI,IAAI,CAAC,YAAY,YAAY,YAAY,UAAU;AACvD,MAAI,IAAI,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU;AAEnE,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE5C,YAAQ,CAAC;AAET,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AAEhC,YAAQ,MAAM,UAAU,MAAM,KAAK,KAAK;AAAA,EAC1C;AAEA,QAAM,KAAK,GAAI;AACf,MAAI,IAAI,MAAM,SAAS,IAAI;AAC3B,MAAI,IAAI,KAAK,KAAK,IAAI,EAAE;AACxB,MAAI,IAAI,IAAI,MAAM,CAAC;AAEnB,WAAS,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI;AAC7B,QAAI,MAAM,IAAI,YAAY,EAAE;AAE5B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC;AAAA,IACxI;AAEA,MAAE,EAAE,IAAI;AAAA,EACV;AAEA,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE;AACtD,IAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACtC,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI;AAExC,WAAS,MAAM,GAAG,MAAM,GAAG,EAAE,KAAK;AAChC,QAAI,IAAI,IAAI,YAAY,EAAE;AAE1B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,QAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAAA,IACjB;AAEA,aAAS,KAAK,IAAI,KAAK,IAAI,EAAE,IAAI;AAC/B,QAAE,EAAE,IAAI,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AAAA,IACjE;AAEA,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AAEX,aAAS,MAAM,GAAG,MAAM,IAAI,EAAE,KAAK;AACjC,UAAI,IAAI,KAAK,MAAM,MAAM,EAAE;AAC3B,UAAI,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,MAAM;AAC3D,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,GAAG,EAAE,MAAM;AACpB,UAAI;AACJ,UAAI;AAAA,IACN;AAEA,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,EACtB;AAEA,SAAO,CAAC,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,GAAI;AACjW;AAEA,IAAO,eAAQ;;;AC7Ff,IAAI,KAAK,YAAI,MAAM,IAAM,YAAI;AAC7B,IAAO,aAAQ;;;ACHf,IAAO,cAAQ;;;ACEf,SAAS,QAAQ,MAAM;AACrB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACnB,UAAM,UAAU,cAAc;AAAA,EAChC;AAEA,SAAO,SAAS,KAAK,OAAO,IAAI,CAAC,GAAG,EAAE;AACxC;AAEA,IAAO,kBAAQ;", "names": ["version"]}