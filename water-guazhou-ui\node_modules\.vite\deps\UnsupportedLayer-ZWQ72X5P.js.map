{"version": 3, "sources": ["../../@arcgis/core/layers/UnsupportedLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../chunks/tslib.es6.js\";import e from\"../core/Error.js\";import{MultiOriginJSONMixin as o}from\"../core/MultiOriginJSONSupport.js\";import{schedule as s}from\"../core/scheduling.js\";import{property as t}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as i}from\"../core/accessorSupport/decorators/subclass.js\";import p from\"./Layer.js\";import{PortalLayer as n}from\"./mixins/PortalLayer.js\";let u=class extends(n(o(p))){constructor(r){super(r),this.resourceInfo=null,this.type=\"unsupported\"}initialize(){this.addResolvingPromise(new Promise(((r,o)=>{s((()=>{const r=this.resourceInfo&&(this.resourceInfo.layerType||this.resourceInfo.type);let s=\"Unsupported layer type\";r&&(s+=\" \"+r),o(new e(\"layer:unsupported-layer-type\",s,{layerType:r}))}))})))}read(r,e){const o={resourceInfo:r};null!=r.id&&(o.id=r.id),null!=r.title&&(o.title=r.title),super.read(o,e)}write(r){return Object.assign(r||{},this.resourceInfo,{id:this.id})}};r([t({readOnly:!0})],u.prototype,\"resourceInfo\",void 0),r([t({type:[\"show\",\"hide\"]})],u.prototype,\"listMode\",void 0),r([t({json:{read:!1},readOnly:!0,value:\"unsupported\"})],u.prototype,\"type\",void 0),u=r([i(\"esri.layers.UnsupportedLayer\")],u);const c=u;export{c as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIqf,IAAI,IAAE,cAAc,EAAE,EAAE,CAAC,CAAC,EAAE;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,eAAa,MAAK,KAAK,OAAK;AAAA,EAAa;AAAA,EAAC,aAAY;AAAC,SAAK,oBAAoB,IAAI,QAAS,CAAC,GAAE,MAAI;AAAC,QAAG,MAAI;AAAC,cAAMA,KAAE,KAAK,iBAAe,KAAK,aAAa,aAAW,KAAK,aAAa;AAAM,YAAIC,KAAE;AAAyB,QAAAD,OAAIC,MAAG,MAAID,KAAG,EAAE,IAAI,EAAE,gCAA+BC,IAAE,EAAC,WAAUD,GAAC,CAAC,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,KAAK,GAAEE,IAAE;AAAC,UAAM,IAAE,EAAC,cAAa,EAAC;AAAE,YAAM,EAAE,OAAK,EAAE,KAAG,EAAE,KAAI,QAAM,EAAE,UAAQ,EAAE,QAAM,EAAE,QAAO,MAAM,KAAK,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,GAAE;AAAC,WAAO,OAAO,OAAO,KAAG,CAAC,GAAE,KAAK,cAAa,EAAC,IAAG,KAAK,GAAE,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,MAAG,OAAM,cAAa,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,8BAA8B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["r", "s", "e"]}