import {
  m
} from "./chunk-6ILWLF72.js";
import {
  a as a2
} from "./chunk-QMG7GZIF.js";
import {
  l
} from "./chunk-T23PB46T.js";
import {
  o
} from "./chunk-PEEUPDEG.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/symbols/FillSymbol.js
var l2 = class extends a2 {
  constructor(e2) {
    super(e2), this.outline = null, this.type = null;
  }
  hash() {
    return `${this.type}.${this.outline && this.outline.hash()}`;
  }
};
e([y({ types: { key: "type", base: null, defaultKeyValue: "simple-line", typeMap: { "simple-line": m } }, json: { default: null, write: true } })], l2.prototype, "outline", void 0), e([y({ type: ["simple-fill", "picture-fill"], readOnly: true })], l2.prototype, "type", void 0), l2 = e([a("esri.symbols.FillSymbol")], l2);
var p2 = l2;

// node_modules/@arcgis/core/symbols/SimpleFillSymbol.js
var p3;
var c = new s({ esriSFSSolid: "solid", esriSFSNull: "none", esriSFSHorizontal: "horizontal", esriSFSVertical: "vertical", esriSFSForwardDiagonal: "forward-diagonal", esriSFSBackwardDiagonal: "backward-diagonal", esriSFSCross: "cross", esriSFSDiagonalCross: "diagonal-cross" });
var m2 = p3 = class extends p2 {
  constructor(...o2) {
    super(...o2), this.color = new l([0, 0, 0, 0.25]), this.outline = new m(), this.type = "simple-fill", this.style = "solid";
  }
  normalizeCtorArgs(o2, r, s2) {
    if (o2 && "string" != typeof o2) return o2;
    const e2 = {};
    return o2 && (e2.style = o2), r && (e2.outline = r), s2 && (e2.color = s2), e2;
  }
  clone() {
    return new p3({ color: p(this.color), outline: this.outline && this.outline.clone(), style: this.style });
  }
  hash() {
    return `${super.hash()}${this.style}.${this.color && this.color.hash()}`;
  }
};
e([y()], m2.prototype, "color", void 0), e([y()], m2.prototype, "outline", void 0), e([o({ esriSFS: "simple-fill" }, { readOnly: true })], m2.prototype, "type", void 0), e([y({ type: c.apiValues, json: { read: c.read, write: c.write } })], m2.prototype, "style", void 0), m2 = p3 = e([a("esri.symbols.SimpleFillSymbol")], m2);
var S = m2;

export {
  p2 as p,
  S
};
//# sourceMappingURL=chunk-R3VLALN5.js.map
