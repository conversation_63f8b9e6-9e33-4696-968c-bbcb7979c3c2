import {
  t as t2
} from "./chunk-3IDKVHSA.js";
import {
  u
} from "./chunk-I7WHRVHF.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  M
} from "./chunk-R5MYQRRS.js";
import {
  $,
  E,
  I,
  R,
  f2 as f,
  o as o2
} from "./chunk-JXLVNWKF.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T
} from "./chunk-HP475EI3.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/LOD.js
var i;
var l2 = i = class extends l {
  constructor(e2) {
    super(e2), this.cols = null, this.level = 0, this.levelValue = null, this.origin = null, this.resolution = 0, this.rows = null, this.scale = 0;
  }
  clone() {
    return new i({ cols: this.cols, level: this.level, levelValue: this.levelValue, resolution: this.resolution, rows: this.rows, scale: this.scale });
  }
};
e([y({ json: { write: true, origins: { "web-document": { read: false, write: false }, "portal-item": { read: false, write: false } } } })], l2.prototype, "cols", void 0), e([y({ type: T, json: { write: true } })], l2.prototype, "level", void 0), e([y({ type: String, json: { write: true } })], l2.prototype, "levelValue", void 0), e([y({ json: { write: true, origins: { "web-document": { read: false, write: false }, "portal-item": { read: false, write: false } } } })], l2.prototype, "origin", void 0), e([y({ type: Number, json: { write: true } })], l2.prototype, "resolution", void 0), e([y({ json: { write: true, origins: { "web-document": { read: false, write: false }, "portal-item": { read: false, write: false } } } })], l2.prototype, "rows", void 0), e([y({ type: Number, json: { write: true } })], l2.prototype, "scale", void 0), l2 = i = e([a("esri.layers.support.LOD")], l2);
var p = l2;

// node_modules/@arcgis/core/layers/support/TileInfo.js
var x;
var O = new s({ PNG: "png", PNG8: "png8", PNG24: "png24", PNG32: "png32", JPEG: "jpg", JPG: "jpg", DIB: "dib", TIFF: "tiff", EMF: "emf", PS: "ps", PDF: "pdf", GIF: "gif", SVG: "svg", SVGZ: "svgz", Mixed: "mixed", MIXED: "mixed", LERC: "lerc", LERC2D: "lerc2d", RAW: "raw", pbf: "pbf" });
var S = x = class extends l {
  static create(e2 = {}) {
    const { resolutionFactor: t3 = 1, scales: o3, size: r2 = 256, spatialReference: i2 = f.WebMercator, numLODs: l3 = 24 } = e2;
    if (!I(i2)) {
      const e3 = [];
      if (o3) for (let t4 = 0; t4 < o3.length; t4++) {
        const r3 = o3[t4];
        e3.push(new p({ level: t4, scale: r3, resolution: r3 }));
      }
      else {
        let t4 = 5e-4;
        for (let o4 = l3 - 1; o4 >= 0; o4--) e3.unshift(new p({ level: o4, scale: t4, resolution: t4 })), t4 *= 2;
      }
      return new x({ dpi: 96, lods: e3, origin: new w(0, 0, i2), size: [r2, r2], spatialReference: i2 });
    }
    const n = R(i2), a2 = e2.origin ? new w({ x: e2.origin.x, y: e2.origin.y, spatialReference: i2 }) : new w(n ? { x: n.origin[0], y: n.origin[1], spatialReference: i2 } : { x: 0, y: 0, spatialReference: i2 }), p2 = 96, f2 = 1 / ($(i2) * 39.37 * p2), y2 = [];
    if (o3) for (let s2 = 0; s2 < o3.length; s2++) {
      const e3 = o3[s2], t4 = e3 * f2;
      y2.push(new p({ level: s2, scale: e3, resolution: t4 }));
    }
    else {
      let e3 = o2(i2) ? 512 / r2 * 5916575275917094e-7 : 256 / r2 * 591657527591555e-6;
      const o4 = Math.ceil(l3 / t3);
      y2.push(new p({ level: 0, scale: e3, resolution: e3 * f2 }));
      for (let r3 = 1; r3 < o4; r3++) {
        const o5 = e3 / 2 ** t3, s2 = o5 * f2;
        y2.push(new p({ level: r3, scale: o5, resolution: s2 })), e3 = o5;
      }
    }
    return new x({ dpi: p2, lods: y2, origin: a2, size: [r2, r2], spatialReference: i2 });
  }
  constructor(e2) {
    super(e2), this.dpi = 96, this.format = null, this.origin = null, this.minScale = 0, this.maxScale = 0, this.size = null, this.spatialReference = null;
  }
  get isWrappable() {
    const { spatialReference: e2, origin: t3 } = this;
    if (e2 && t3) {
      const o3 = R(e2);
      return e2.isWrappable && !!o3 && Math.abs(o3.origin[0] - t3.x) <= o3.dx;
    }
    return false;
  }
  readOrigin(e2, t3) {
    return w.fromJSON({ spatialReference: t3.spatialReference, ...e2 });
  }
  set lods(e2) {
    let t3 = 0, o3 = 0;
    const r2 = [], s2 = this._levelToLOD = {};
    e2 && (t3 = -1 / 0, o3 = 1 / 0, e2.forEach((e3) => {
      r2.push(e3.scale), t3 = e3.scale > t3 ? e3.scale : t3, o3 = e3.scale < o3 ? e3.scale : o3, s2[e3.level] = e3;
    })), this._set("scales", r2), this._set("minScale", t3), this._set("maxScale", o3), this._set("lods", e2), this._initializeUpsampleLevels();
  }
  readSize(e2, t3) {
    return [t3.cols, t3.rows];
  }
  writeSize(e2, t3) {
    t3.cols = e2[0], t3.rows = e2[1];
  }
  zoomToScale(e2) {
    const t3 = this.scales;
    if (e2 <= 0) return t3[0];
    if (e2 >= t3.length - 1) return t3[t3.length - 1];
    const o3 = Math.floor(e2), r2 = o3 + 1;
    return t3[o3] / (t3[o3] / t3[r2]) ** (e2 - o3);
  }
  scaleToZoom(e2) {
    const t3 = this.scales, o3 = t3.length - 1;
    let r2 = 0;
    for (; r2 < o3; r2++) {
      const o4 = t3[r2], s2 = t3[r2 + 1];
      if (o4 <= e2) return r2;
      if (s2 === e2) return r2 + 1;
      if (o4 > e2 && s2 < e2) return r2 + Math.log(o4 / e2) / Math.log(o4 / s2);
    }
    return r2;
  }
  snapScale(e2, t3 = 0.95) {
    const o3 = this.scaleToZoom(e2);
    return o3 % Math.floor(o3) >= t3 ? this.zoomToScale(Math.ceil(o3)) : this.zoomToScale(Math.floor(o3));
  }
  tileAt(e2, t3, o3, s2) {
    const i2 = this.lodAt(e2);
    if (!i2) return null;
    let l3, n;
    if ("number" == typeof t3) l3 = t3, n = o3;
    else if (E(t3.spatialReference, this.spatialReference)) l3 = t3.x, n = t3.y, s2 = o3;
    else {
      const e3 = M(t3, this.spatialReference);
      if (t(e3)) return null;
      l3 = e3.x, n = e3.y, s2 = o3;
    }
    const a2 = i2.resolution * this.size[0], p2 = i2.resolution * this.size[1];
    return s2 || (s2 = new t2(null, 0, 0, 0, u())), s2.level = e2, s2.row = Math.floor((this.origin.y - n) / p2 + 1e-3), s2.col = Math.floor((l3 - this.origin.x) / a2 + 1e-3), this.updateTileInfo(s2), s2;
  }
  updateTileInfo(e2, t3 = x.ExtrapolateOptions.NONE) {
    let o3 = this.lodAt(e2.level);
    if (!o3 && t3 === x.ExtrapolateOptions.POWER_OF_TWO) {
      const t4 = this.lods[this.lods.length - 1];
      t4.level < e2.level && (o3 = t4);
    }
    if (!o3) return;
    const r2 = e2.level - o3.level, s2 = o3.resolution * this.size[0] / 2 ** r2, i2 = o3.resolution * this.size[1] / 2 ** r2;
    e2.id = `${e2.level}/${e2.row}/${e2.col}`, e2.extent || (e2.extent = u()), e2.extent[0] = this.origin.x + e2.col * s2, e2.extent[1] = this.origin.y - (e2.row + 1) * i2, e2.extent[2] = e2.extent[0] + s2, e2.extent[3] = e2.extent[1] + i2;
  }
  upsampleTile(e2) {
    const t3 = this._upsampleLevels[e2.level];
    return !(!t3 || -1 === t3.parentLevel) && (e2.level = t3.parentLevel, e2.row = Math.floor(e2.row / t3.factor + 1e-3), e2.col = Math.floor(e2.col / t3.factor + 1e-3), this.updateTileInfo(e2), true);
  }
  getTileBounds(e2, t3) {
    const o3 = this.lodAt(t3.level);
    if (null == o3) return null;
    const { resolution: r2 } = o3, s2 = r2 * this.size[0], i2 = r2 * this.size[1];
    return e2[0] = this.origin.x + t3.col * s2, e2[1] = this.origin.y - (t3.row + 1) * i2, e2[2] = e2[0] + s2, e2[3] = e2[1] + i2, e2;
  }
  lodAt(e2) {
    var _a;
    return ((_a = this._levelToLOD) == null ? void 0 : _a[e2]) ?? null;
  }
  clone() {
    return x.fromJSON(this.write({}));
  }
  getOrCreateCompatible(e2, t3) {
    if (256 === this.size[0] && 256 === this.size[1]) return 256 === e2 ? this : null;
    const o3 = [], r2 = this.lods.length;
    for (let s2 = 0; s2 < r2; s2++) {
      const e3 = this.lods[s2], r3 = e3.resolution * t3;
      o3.push(new p({ level: e3.level, scale: e3.scale, resolution: r3 }));
    }
    return new x({ size: [e2, e2], dpi: this.dpi, format: this.format, compressionQuality: this.compressionQuality, origin: this.origin, spatialReference: this.spatialReference, lods: o3 });
  }
  _initializeUpsampleLevels() {
    const e2 = this.lods;
    this._upsampleLevels = [];
    let t3 = null;
    for (let o3 = 0; o3 < e2.length; o3++) {
      const r2 = e2[o3];
      this._upsampleLevels[r2.level] = { parentLevel: t3 ? t3.level : -1, factor: t3 ? t3.resolution / r2.resolution : 0 }, t3 = r2;
    }
  }
};
e([y({ type: Number, json: { write: true } })], S.prototype, "compressionQuality", void 0), e([y({ type: Number, json: { write: true } })], S.prototype, "dpi", void 0), e([y({ type: String, json: { read: O.read, write: O.write, origins: { "web-scene": { read: false, write: false } } } })], S.prototype, "format", void 0), e([y({ readOnly: true })], S.prototype, "isWrappable", null), e([y({ type: w, json: { write: true } })], S.prototype, "origin", void 0), e([o("origin")], S.prototype, "readOrigin", null), e([y({ type: [p], value: null, json: { write: true } })], S.prototype, "lods", null), e([y({ readOnly: true })], S.prototype, "minScale", void 0), e([y({ readOnly: true })], S.prototype, "maxScale", void 0), e([y({ readOnly: true })], S.prototype, "scales", void 0), e([y({ cast: (e2) => Array.isArray(e2) ? e2 : "number" == typeof e2 ? [e2, e2] : [256, 256] })], S.prototype, "size", void 0), e([o("size", ["rows", "cols"])], S.prototype, "readSize", null), e([r("size", { cols: { type: T }, rows: { type: T } })], S.prototype, "writeSize", null), e([y({ type: f, json: { write: true } })], S.prototype, "spatialReference", void 0), S = x = e([a("esri.layers.support.TileInfo")], S), function(e2) {
  var t3;
  (t3 = e2.ExtrapolateOptions || (e2.ExtrapolateOptions = {}))[t3.NONE = 0] = "NONE", t3[t3.POWER_OF_TWO = 1] = "POWER_OF_TWO";
}(S || (S = {}));
var j = S;

export {
  p,
  j
};
//# sourceMappingURL=chunk-IEIKQ72S.js.map
