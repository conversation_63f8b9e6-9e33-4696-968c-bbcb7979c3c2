{"version": 3, "sources": ["../../@arcgis/core/layers/support/labelFormatUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Error.js\";import r from\"../../core/Logger.js\";import{isNone as t}from\"../../core/maybe.js\";import{formatDate as a,convertDateFormatToIntlOptions as n}from\"../../intl/date.js\";import{formatNumber as o}from\"../../intl/number.js\";import{isNumericField as i}from\"./fieldUtils.js\";import{getLabelExpression as l,getSingleFieldArcadeExpression as u}from\"./labelUtils.js\";import{createLabelExpression as s}from\"../../support/arcadeOnDemand.js\";const c=r.getLogger(\"esri.layers.support.labelFormatUtils\"),f={type:\"simple\",evaluate:()=>null},p={getAttribute:(e,r)=>e.field(r)};async function m(r,a,n){if(!r||!r.symbol||!a)return f;const o=r.where,i=l(r),m=o?await import(\"../../core/sql/WhereClause.js\"):null;let g;if(\"arcade\"===i.type){const r=await s(i.expression,n,a);if(t(r))return f;g={type:\"arcade\",evaluate(t){try{const e=r.evaluate({$feature:\"attributes\"in t?r.repurposeFeature(t):t});if(null!=e)return e.toString()}catch(a){c.error(new e(\"arcade-expression-error\",\"Encountered an error when evaluating label expression for feature\",{feature:t,expression:i}))}return null},needsHydrationToEvaluate:()=>null==u(i.expression)}}else g={type:\"simple\",evaluate:e=>i.expression.replace(/{[^}]*}/g,(r=>{const t=r.slice(1,-1),n=a.get(t);if(!n)return r;let o=null;if(\"attributes\"in e){e&&e.attributes&&(o=e.attributes[n.name])}else o=e.field(n.name);return null==o?\"\":d(o,n)}))};if(o){let r;try{r=m.WhereClause.create(o,a)}catch(y){return c.error(new e(\"bad-where-clause\",\"Encountered an error when evaluating where clause, ignoring\",{where:o,error:y})),f}const t=g.evaluate;g.evaluate=a=>{const n=\"attributes\"in a?void 0:p;try{if(r.testFeature(a,n))return t(a)}catch(y){c.error(new e(\"bad-where-clause\",\"Encountered an error when evaluating where clause for feature\",{where:o,feature:a,error:y}))}return null}}return g}function d(e,r){if(null==e)return\"\";const t=r.domain;if(t)if(\"codedValue\"===t.type||\"coded-value\"===t.type){const r=e;for(const e of t.codedValues)if(e.code===r)return e.name}else if(\"range\"===t.type){const r=+e,a=\"range\"in t?t.range[0]:t.minValue,n=\"range\"in t?t.range[1]:t.maxValue;if(a<=r&&r<=n)return t.name}let l=e;return\"date\"===r.type||\"esriFieldTypeDate\"===r.type?l=a(l,n(\"short-date\")):i(r)&&(l=o(+l)),l||\"\"}export{m as createLabelFunction,d as formatField};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI8c,IAAM,IAAE,EAAE,UAAU,sCAAsC;AAA1D,IAA4D,IAAE,EAAC,MAAK,UAAS,UAAS,MAAI,KAAI;AAA9F,IAAgG,IAAE,EAAC,cAAa,CAAC,GAAE,MAAI,EAAE,MAAM,CAAC,EAAC;AAAE,eAAeA,GAAE,GAAE,GAAEC,IAAE;AAAC,MAAG,CAAC,KAAG,CAAC,EAAE,UAAQ,CAAC,EAAE,QAAO;AAAE,QAAM,IAAE,EAAE,OAAM,IAAE,EAAE,CAAC,GAAED,KAAE,IAAE,MAAM,OAAO,2BAA+B,IAAE;AAAK,MAAI;AAAE,MAAG,aAAW,EAAE,MAAK;AAAC,UAAME,KAAE,MAAM,EAAE,EAAE,YAAWD,IAAE,CAAC;AAAE,QAAG,EAAEC,EAAC,EAAE,QAAO;AAAE,QAAE,EAAC,MAAK,UAAS,SAASC,IAAE;AAAC,UAAG;AAAC,cAAM,IAAED,GAAE,SAAS,EAAC,UAAS,gBAAeC,KAAED,GAAE,iBAAiBC,EAAC,IAAEA,GAAC,CAAC;AAAE,YAAG,QAAM,EAAE,QAAO,EAAE,SAAS;AAAA,MAAC,SAAOC,IAAE;AAAC,UAAE,MAAM,IAAIC,GAAE,2BAA0B,qEAAoE,EAAC,SAAQF,IAAE,YAAW,EAAC,CAAC,CAAC;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI,GAAE,0BAAyB,MAAI,QAAM,EAAE,EAAE,UAAU,EAAC;AAAA,EAAC,MAAM,KAAE,EAAC,MAAK,UAAS,UAAS,OAAG,EAAE,WAAW,QAAQ,YAAY,CAAAD,OAAG;AAAC,UAAMC,KAAED,GAAE,MAAM,GAAE,EAAE,GAAED,KAAE,EAAE,IAAIE,EAAC;AAAE,QAAG,CAACF,GAAE,QAAOC;AAAE,QAAII,KAAE;AAAK,QAAG,gBAAe,GAAE;AAAC,WAAG,EAAE,eAAaA,KAAE,EAAE,WAAWL,GAAE,IAAI;AAAA,IAAE,MAAM,CAAAK,KAAE,EAAE,MAAML,GAAE,IAAI;AAAE,WAAO,QAAMK,KAAE,KAAG,EAAEA,IAAEL,EAAC;AAAA,EAAC,CAAE,EAAC;AAAE,MAAG,GAAE;AAAC,QAAIC;AAAE,QAAG;AAAC,MAAAA,KAAEF,GAAE,YAAY,OAAO,GAAE,CAAC;AAAA,IAAC,SAAO,GAAE;AAAC,aAAO,EAAE,MAAM,IAAIK,GAAE,oBAAmB,+DAA8D,EAAC,OAAM,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE;AAAA,IAAC;AAAC,UAAMF,KAAE,EAAE;AAAS,MAAE,WAAS,CAAAC,OAAG;AAAC,YAAMH,KAAE,gBAAeG,KAAE,SAAO;AAAE,UAAG;AAAC,YAAGF,GAAE,YAAYE,IAAEH,EAAC,EAAE,QAAOE,GAAEC,EAAC;AAAA,MAAC,SAAO,GAAE;AAAC,UAAE,MAAM,IAAIC,GAAE,oBAAmB,iEAAgE,EAAC,OAAM,GAAE,SAAQD,IAAE,OAAM,EAAC,CAAC,CAAC;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,QAAM,EAAE,QAAM;AAAG,QAAMD,KAAE,EAAE;AAAO,MAAGA;AAAE,QAAG,iBAAeA,GAAE,QAAM,kBAAgBA,GAAE,MAAK;AAAC,YAAMD,KAAE;AAAE,iBAAUK,MAAKJ,GAAE,YAAY,KAAGI,GAAE,SAAOL,GAAE,QAAOK,GAAE;AAAA,IAAI,WAAS,YAAUJ,GAAE,MAAK;AAAC,YAAMD,KAAE,CAAC,GAAE,IAAE,WAAUC,KAAEA,GAAE,MAAM,CAAC,IAAEA,GAAE,UAASF,KAAE,WAAUE,KAAEA,GAAE,MAAM,CAAC,IAAEA,GAAE;AAAS,UAAG,KAAGD,MAAGA,MAAGD,GAAE,QAAOE,GAAE;AAAA,IAAI;AAAA;AAAC,MAAI,IAAE;AAAE,SAAM,WAAS,EAAE,QAAM,wBAAsB,EAAE,OAAK,IAAE,EAAE,GAAE,EAAE,YAAY,CAAC,IAAE,GAAE,CAAC,MAAI,IAAE,EAAE,CAAC,CAAC,IAAG,KAAG;AAAE;", "names": ["m", "n", "r", "t", "a", "s", "o", "e"]}