{"version": 3, "sources": ["../../@arcgis/core/support/revision.js", "../../@arcgis/core/kernel.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst c=\"20230301\",e=\"2657e728c1857e6d94c324181c0788310bb0958a\";export{c as buildDate,e as commitHash};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"./core/has.js\";import{getInterceptor as t,addQueryParameter as e}from\"./core/urlUtils.js\";export{buildDate,commitHash as revision}from\"./support/revision.js\";const a=\"4.26\",s={async request(e,a){const{default:s}=await import(\"./request.js\"),r=e.options,n=r.responseType;r.signal=a?.signal,r.responseType=\"native\"===n||\"native-request-init\"===n?\"native-request-init\":n&&[\"blob\",\"json\",\"text\"].includes(n)&&t(e.url)?.after?n:\"array-buffer\";const o=await s(e.url,r),i={data:o.data,httpStatus:o.httpStatus,ssl:o.ssl};switch(o.requestOptions?.responseType){case\"native-request-init\":return delete i.data.signal,i;case\"blob\":i.data=await i.data.arrayBuffer();break;case\"json\":i.data=(new TextEncoder).encode(JSON.stringify(i.data)).buffer;break;case\"text\":i.data=(new TextEncoder).encode(i.data).buffer}return{result:i,transferList:[i.data]}}};let r;function n(t){r=t}function o(t){const a=r&&r.findCredential(t);return a&&a.token?e(t,\"token\",a.token):t}has(\"host-webworker\");export{o as addTokenParameter,r as id,n as setId,a as version,s as workerMessages};\n"], "mappings": ";;;;;;;;;AAIA,IAAM,IAAE;AAAR,IAAmB,IAAE;;;ACAyJ,IAAM,IAAE;AAAR,IAAe,IAAE,EAAC,MAAM,QAAQA,IAAEC,IAAE;AAJlN;AAImN,QAAK,EAAC,SAAQC,GAAC,IAAE,MAAM,OAAO,+BAAc,GAAEC,KAAEH,GAAE,SAAQI,KAAED,GAAE;AAAa,EAAAA,GAAE,SAAOF,MAAA,gBAAAA,GAAG,QAAOE,GAAE,eAAa,aAAWC,MAAG,0BAAwBA,KAAE,wBAAsBA,MAAG,CAAC,QAAO,QAAO,MAAM,EAAE,SAASA,EAAC,OAAG,OAAEJ,GAAE,GAAG,MAAP,mBAAU,SAAMI,KAAE;AAAe,QAAMC,KAAE,MAAMH,GAAEF,GAAE,KAAIG,EAAC,GAAE,IAAE,EAAC,MAAKE,GAAE,MAAK,YAAWA,GAAE,YAAW,KAAIA,GAAE,IAAG;AAAE,WAAO,KAAAA,GAAE,mBAAF,mBAAkB,cAAa;AAAA,IAAC,KAAI;AAAsB,aAAO,OAAO,EAAE,KAAK,QAAO;AAAA,IAAE,KAAI;AAAO,QAAE,OAAK,MAAM,EAAE,KAAK,YAAY;AAAE;AAAA,IAAM,KAAI;AAAO,QAAE,OAAM,IAAI,cAAa,OAAO,KAAK,UAAU,EAAE,IAAI,CAAC,EAAE;AAAO;AAAA,IAAM,KAAI;AAAO,QAAE,OAAM,IAAI,cAAa,OAAO,EAAE,IAAI,EAAE;AAAA,EAAM;AAAC,SAAM,EAAC,QAAO,GAAE,cAAa,CAAC,EAAE,IAAI,EAAC;AAAC,EAAC;AAAE,IAAI;AAAE,SAAS,EAAE,GAAE;AAAC,MAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAMJ,KAAE,KAAG,EAAE,eAAe,CAAC;AAAE,SAAOA,MAAGA,GAAE,QAAM,GAAE,GAAE,SAAQA,GAAE,KAAK,IAAE;AAAC;AAAC,IAAI,gBAAgB;", "names": ["e", "a", "s", "r", "n", "o"]}