import {
  e as e6
} from "./chunk-LOPMKCZI.js";
import {
  E as E5,
  P as P2,
  i,
  l as l5,
  u as u3
} from "./chunk-X6S7G5WH.js";
import {
  i as i2
} from "./chunk-WPZSZ6CK.js";
import {
  s as s4
} from "./chunk-NMGVJ2ZX.js";
import {
  G,
  N,
  _,
  b as b3,
  n as n8,
  r as r5,
  t as t4,
  w as w3
} from "./chunk-INH5JU5P.js";
import {
  D
} from "./chunk-HLLJFAS4.js";
import {
  c as c2
} from "./chunk-T6GIT4YI.js";
import {
  o as o3,
  p as p4,
  r as r4,
  s as s3
} from "./chunk-PHEIXDVR.js";
import {
  p as p5
} from "./chunk-WX7B7OKM.js";
import {
  E as E4,
  a as a6,
  c as c3,
  c2 as c4,
  d as d3,
  g,
  l as l4,
  m as m4,
  m2 as m5,
  u as u2
} from "./chunk-UQWZJZ2S.js";
import {
  E as E3,
  L as L2,
  b as b2,
  e as e5,
  m as m3,
  n as n7
} from "./chunk-5S4W3ME5.js";
import {
  E as E2,
  c,
  l as l3
} from "./chunk-CDZ24ELJ.js";
import {
  n as n6
} from "./chunk-HAEVWZ5B.js";
import {
  P
} from "./chunk-HURTVQSL.js";
import {
  A as A2,
  b,
  d,
  j as j3,
  m,
  o as o2,
  p as p3
} from "./chunk-SROTSYJS.js";
import {
  n as n5
} from "./chunk-FOE4ICAJ.js";
import {
  h
} from "./chunk-4YSFMXMT.js";
import {
  An,
  _n,
  en,
  rn,
  tn
} from "./chunk-UYAKJRPP.js";
import {
  d as d2
} from "./chunk-Q4VCSCSY.js";
import {
  U,
  a as a5,
  f as f2,
  j as j2,
  l as l2,
  w as w2
} from "./chunk-QUHG7NMD.js";
import {
  n as n4
} from "./chunk-CCAF47ZU.js";
import {
  j3 as j4
} from "./chunk-ETY52UBV.js";
import {
  S
} from "./chunk-R3VLALN5.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  n as n2
} from "./chunk-SGIJIEHB.js";
import {
  e as e3
} from "./chunk-X7FOCGBC.js";
import {
  y as y3
} from "./chunk-VX6YUKFM.js";
import {
  m as m2
} from "./chunk-6ILWLF72.js";
import {
  E,
  O
} from "./chunk-JXLVNWKF.js";
import {
  A,
  F,
  e as e4,
  o,
  p as p2,
  q,
  r as r3,
  u
} from "./chunk-MQAXMQFG.js";
import {
  n as n3,
  r as r2,
  t as t3
} from "./chunk-36FLFRUE.js";
import {
  a as a4
} from "./chunk-EGHLQERQ.js";
import {
  e,
  t2,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  n
} from "./chunk-HP475EI3.js";
import {
  L,
  a as a3,
  f,
  p,
  y as y2
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  a,
  e as e2,
  l,
  r,
  t,
  w,
  x
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/interactive/editingTools/transformGraphic/isSupportedGraphic.js
function i3(i6) {
  var _a;
  if ("graphics" !== ((_a = i6.layer) == null ? void 0 : _a.type)) return P2.GRAPHICS_LAYER_MISSING;
  if (t(i6.geometry)) return P2.GEOMETRY_MISSING;
  switch (i6.geometry.type) {
    case "point":
      break;
    case "polygon":
    case "polyline":
    case "multipoint":
    case "extent":
    case "mesh":
      return P2.SUPPORTED;
    default:
      return P2.GEOMETRY_TYPE_UNSUPPORTED;
  }
  const n13 = r(i6.symbol) && "point-3d" === i6.symbol.type && i6.symbol.symbolLayers;
  if (!(n13 && n13.length > 0 && n13.some((e7) => "object" === e7.type))) return P2.SYMBOL_TYPE_UNSUPPORTED;
  return "on-the-ground" !== l3(i6) && c(i6) ? P2.ELEVATION_MODE_UNSUPPORTED : P2.SUPPORTED;
}

// node_modules/@arcgis/core/views/interactive/snapping/candidates/RightAngleSnappingCandidate.js
var o4 = class extends t4 {
  constructor({ targetPoint: e7, constraint: i6, previousVertex: r8, otherVertex: s6, otherVertexType: o5, objectId: n13, isDraped: h4 }) {
    super(e7, i6, h4, E3.SELF), this.previousVertex = r8, this.otherVertex = s6, this.otherVertexType = o5, this.objectId = n13;
  }
  get hints() {
    const t5 = this.previousVertex, i6 = this.otherVertexType === n9.CENTER ? this.otherVertex : this.targetPoint, o5 = this.otherVertexType === n9.CENTER ? this.targetPoint : this.otherVertex;
    return [new n7(u2.TARGET, i6, o5, this.isDraped, this.domain), new n7(u2.REFERENCE, t5, i6, this.isDraped, this.domain), new s3(this.previousVertex, i6, o5, this.isDraped, this.domain)];
  }
};
var n9;
!function(t5) {
  t5[t5.NEXT = 0] = "NEXT", t5[t5.CENTER = 1] = "CENTER";
}(n9 || (n9 = {}));

// node_modules/@arcgis/core/views/interactive/snapping/FeatureSnappingEngine.js
var V = class extends d2 {
  get updating() {
    return n(this.snappingSources, ({ snappingSource: e7 }) => e7.updating) || this.updatingHandles.updating;
  }
  get snappingSources() {
    const e7 = this._get("snappingSources") || /* @__PURE__ */ new Map(), t5 = /* @__PURE__ */ new Map();
    if (r(this.options) && r(this.options.featureSources)) for (const r8 of this.options.featureSources.items) {
      const s6 = r8.layer.uid, n13 = e7.get(s6);
      if (n13) {
        e7.delete(s6), t5.set(s6, n13);
        continue;
      }
      if (!r8.layer.loaded) {
        this.updatingHandles.addPromise(r8.layer.load());
        continue;
      }
      const a7 = this._createSourceInfo(r8);
      r(a7) && t5.set(s6, a7);
    }
    for (const [, r8] of e7) r8.destroy();
    return t5;
  }
  constructor(e7) {
    super(e7), this.options = null, this._domain = E3.FEATURE, this._sourceModules = { featureService: { module: null, loader: null }, featureCollection: { module: null, loader: null }, graphics: { module: null, loader: null }, notes: { module: null, loader: null }, scene: { module: null, loader: null } };
  }
  initialize() {
    this.updatingHandles.add(() => this.snappingSources, () => this.notifyChange("updating"), U), r(this.view) && this.handles.add([this.view.on("layerview-create", (e7) => this._updateLayerView(e7.layer, e7.layerView)), this.view.on("layerview-destroy", (e7) => this._updateLayerView(e7.layer, null))]);
  }
  _updateLayerView(e7, t5) {
    for (const [, r8] of this.snappingSources) r8.snappingSource.layerSource.layer === e7 && (r8.layerView = t5);
  }
  destroy() {
    this._set("options", null);
    for (const [, e7] of this.snappingSources) e7.destroy();
  }
  async fetchCandidates(e7, t5, r8, s6) {
    var _a;
    if (!(t5 & this._domain) || t(this.options) || !this.options.effectiveFeatureEnabled) return [];
    const u5 = [], p8 = this._computeScreenSizeDistanceParameters(e7, r8), d4 = { distance: p8, mode: ((_a = e2(this.view)) == null ? void 0 : _a.type) ?? "2d", point: e7, coordinateHelper: r8.coordinateHelper, types: this._types };
    for (const [, { snappingSource: n13, layerView: a7 }] of this.snappingSources) !n13.layerSource.enabled || r(a7) && a7.suspended || u5.push(n13.fetchCandidates(d4, s6).then((e8) => e8.filter((e9) => !this._candidateIsExcluded(n13, e9, r8.excludeFeature))));
    const l8 = (await L(u5)).flat();
    return this._addRightAngleCandidates(l8, e7, p8, r8), f(s6), d3(e7, l8), l8;
  }
  _addRightAngleCandidates(e7, t5, r8, s6) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const n13 = r(s6.vertexHandle) ? (_b = (_a = s6.vertexHandle.rightEdge) == null ? void 0 : _a.rightVertex) == null ? void 0 : _b.pos : r(s6.editGeometryOperations) && "polygon" === s6.editGeometryOperations.data.type ? (_d = e2((_c = s6.editGeometryOperations.data.components[0]) == null ? void 0 : _c.getFirstVertex())) == null ? void 0 : _d.pos : null, i6 = r(s6.vertexHandle) ? (_f = (_e = s6.vertexHandle.leftEdge) == null ? void 0 : _e.leftVertex) == null ? void 0 : _f.pos : r(s6.editGeometryOperations) ? (_h = e2((_g = s6.editGeometryOperations.data.components[0]) == null ? void 0 : _g.getLastVertex())) == null ? void 0 : _h.pos : null, { view: c5 } = this, u5 = c3(n13, c5, s6), p8 = c3(i6, c5, s6), d4 = e7.length;
    for (let o5 = 0; o5 < d4; o5++) this._addRightAngleCandidate(e7[o5], p8, t5, r8, e7), this._addRightAngleCandidate(e7[o5], u5, t5, r8, e7);
  }
  _addRightAngleCandidate(e7, t5, r8, s6, o5) {
    if (t(t5) || !G2(e7)) return;
    const a7 = e7.constraint.closestTo(t5), i6 = (a7[0] - r8[0]) / s6.x, c5 = (a7[1] - r8[1]) / s6.y, { start: u5, end: p8 } = e7.constraint;
    if (i6 * i6 + c5 * c5 <= 1) {
      const r9 = new o4({ targetPoint: a7, otherVertex: t5, otherVertexType: n9.NEXT, previousVertex: b(a7, u5) > b(a7, p8) ? u5 : p8, constraint: new N(t5, a7), objectId: e7.objectId, isDraped: e7.isDraped });
      o5.push(r9);
    }
  }
  _computeScreenSizeDistanceParameters(e7, t5) {
    let r8 = r(this.options) ? this.options.distance * ("touch" === t5.pointer ? this.options.touchSensitivityMultiplier : 1) : 0;
    return t(this.view) ? { x: r8, y: r8, z: r8, distance: r8 } : "2d" === this.view.type ? (r8 *= this.view.resolution, { x: r8, y: r8, z: r8, distance: r8 }) : this._computeScreenSizeDistanceParameters3D(e7, r8, this.view, t5);
  }
  _computeScreenSizeDistanceParameters3D(e7, t5, r8, s6) {
    const { spatialReference: o5 } = s6;
    r8.renderCoordsHelper.toRenderCoords(e7, o5, D2);
    const n13 = r8.state.camera.computeScreenPixelSizeAt(D2), a7 = n13 * r8.renderCoordsHelper.unitInMeters / r8.mapCoordsHelper.unitInMeters, i6 = t5 * a7, c5 = u3(e7, o5, E2, r8), u5 = c5 ? O2(c5, e7, a7, 0, 0, r8, s6) : 0, p8 = c5 ? O2(c5, e7, 0, a7, 0, r8, s6) : 0, d4 = c5 ? O2(c5, e7, 0, 0, a7, r8, s6) : 0;
    return { x: 0 === u5 ? 0 : i6 / u5, y: 0 === p8 ? 0 : i6 / p8, z: 0 === d4 ? 0 : i6 / d4, distance: n13 * t5 };
  }
  get _types() {
    return D.EDGE | D.VERTEX;
  }
  _candidateIsExcluded(e7, t5, r8) {
    if (t(r8)) return false;
    const s6 = this._getCandidateObjectId(t5);
    if (t(s6)) return false;
    const o5 = e7.layerSource.layer;
    return "graphics" === o5.type ? r8.uid === s6 : r8.sourceLayer === o5 && (!(!r8.attributes || !("objectIdField" in o5)) && r8.attributes[o5.objectIdField] === s6);
  }
  _getCandidateObjectId(e7) {
    return e7 instanceof n8 ? e7.objectId : null;
  }
  _createSourceInfo(e7) {
    const t5 = this._createFeatureSnappingSourceType(e7);
    if (t(t5)) return null;
    if ("loading" in t5) return this.updatingHandles.addPromise(t5.loading.then(() => {
      this.destroyed || this.notifyChange("snappingSources");
    })), null;
    const r8 = r(this.view) ? this.view.allLayerViews.find((t6) => t6.layer === e7.layer) : null;
    return new I(t5.source, r8);
  }
  _createFeatureSnappingSourceType(e7) {
    switch (e7.layer.type) {
      case "feature":
      case "geojson":
      case "csv":
      case "oriented-imagery":
      case "subtype-group":
      case "wfs":
        return this._createFeatureSnappingSourceFeatureLayer(e7);
      case "graphics":
        return this._createFeatureSnappingSourceGraphicsLayer(e7);
      case "map-notes":
        return this._createFeatureSnappingSourceMapNotesLayer(e7);
      case "scene":
      case "building-scene":
        return this._createFeatureSnappingSourceSceneLayer(e7);
    }
    return null;
  }
  _createFeatureSnappingSourceSceneLayer(e7) {
    const { view: t5 } = this;
    if (t(t5) || "3d" !== t5.type) return null;
    const r8 = this._getSourceModule("scene");
    return r(r8.module) ? { source: new r8.module.SceneLayerSnappingSource({ layerSource: e7, view: t5 }) } : { loading: r8.loader };
  }
  _createFeatureSnappingSourceFeatureLayer(e7) {
    var _a;
    switch ((_a = e7.layer.source) == null ? void 0 : _a.type) {
      case "feature-layer":
      case "oriented-imagery": {
        const t5 = this._getSourceModule("featureService");
        return r(t5.module) ? { source: new t5.module.FeatureServiceSnappingSource({ spatialReference: this.spatialReference, view: this.view, layerSource: e7 }) } : { loading: t5.loader };
      }
      case "memory":
      case "csv":
      case "geojson":
      case "wfs": {
        if ("mesh" === e7.layer.geometryType) return null;
        const t5 = this._getSourceModule("featureCollection");
        return r(t5.module) ? { source: new t5.module.FeatureCollectionSnappingSource({ layerSource: e7, view: this.view }) } : { loading: t5.loader };
      }
    }
    return null;
  }
  _createFeatureSnappingSourceGraphicsLayer(e7) {
    const t5 = this._getSourceModule("graphics");
    return r(t5.module) ? { source: new t5.module.GraphicsSnappingSource({ getGraphicsLayers: () => [e7.layer], spatialReference: this.spatialReference, view: this.view, layerSource: e7 }) } : { loading: t5.loader };
  }
  _createFeatureSnappingSourceMapNotesLayer(e7) {
    const t5 = this._getSourceModule("notes");
    return r(t5.module) ? { source: new t5.module.GraphicsSnappingSource({ getGraphicsLayers: () => r(e7.layer.sublayers) ? e7.layer.sublayers.toArray() : [], spatialReference: this.spatialReference, view: this.view, layerSource: e7 }) } : { loading: t5.loader };
  }
  _getSourceModule(e7) {
    const t5 = this._sourceModules[e7];
    if (t(t5.loader)) {
      const r8 = this._loadSourceModule(e7).then((e8) => {
        t5.module = e8;
      });
      return t5.loader = r8, { module: t5.module, loader: r8 };
    }
    return { module: t5.module, loader: t5.loader };
  }
  _loadSourceModule(e7) {
    const t5 = this.updatingHandles;
    switch (e7) {
      case "featureService":
        return t5.addPromise(import("./FeatureServiceSnappingSource-XFWU6D4F.js"));
      case "featureCollection":
        return t5.addPromise(import("./FeatureCollectionSnappingSource-77J6AVCO.js"));
      case "graphics":
      case "notes":
        return t5.addPromise(import("./GraphicsSnappingSource-65JJLE5E.js"));
      case "scene":
        return t5.addPromise(import("./SceneLayerSnappingSource-3NE2VX7P.js"));
    }
  }
};
e([y({ constructOnly: true })], V.prototype, "spatialReference", void 0), e([y({ constructOnly: true })], V.prototype, "view", void 0), e([y()], V.prototype, "options", void 0), e([y({ readOnly: true })], V.prototype, "updating", null), e([y({ readOnly: true })], V.prototype, "snappingSources", null), V = e([a2("esri.views.interactive.snapping.FeatureSnappingEngine")], V);
var I = class {
  constructor(e7, t5) {
    this.snappingSource = e7, this.layerView = t5, this.handles = new t2();
    const s6 = this.snappingSource.layerSource.layer;
    if ("refresh" in s6) {
      const t6 = s6;
      this.handles.add(t6.on("refresh", () => e7.refresh()));
    }
    this.handles.add([l2(() => e7.updating, (t6) => e7.layerSource.updating = t6, w2), l2(() => e7.availability, (t6) => e7.layerSource.availability = t6, w2)]);
  }
  destroy() {
    this.snappingSource.destroy(), this.handles.destroy();
  }
};
function G2(e7) {
  return (e7 instanceof r5 || e7 instanceof s4) && !P3(e7);
}
function P3({ constraint: { start: e7, end: t5 } }) {
  const r8 = p2(e7, t5), s6 = b(e7, t5);
  return r8 < a4() || s6 / r8 < A3;
}
function O2(e7, t5, r8, s6, o5, n13, { spatialReference: a7 }) {
  const i6 = r3(z, t5);
  i6[0] += r8, i6[1] += s6, i6[2] += o5;
  const c5 = u3(i6, a7, E2, n13);
  return c5 ? g(c5, e7) : 1 / 0;
}
var D2 = n3();
var z = n3();
var A3 = 1e-4;

// node_modules/@arcgis/core/views/interactive/snapping/SnappingAlgorithm.js
var n10 = class {
  constructor(e7, t5) {
    this.view = e7, this.options = t5, this.squaredShortLineThreshold = p4.shortLineThreshold * p4.shortLineThreshold;
  }
  snap(t5, s6) {
    return r(s6.vertexHandle) ? "vertex" !== s6.vertexHandle.type ? [] : this.snapExistingVertex(t5, s6) : this.snapNewVertex(t5, s6);
  }
  edgeExceedsShortLineThreshold(e7, t5) {
    return this.exceedsShortLineThreshold(c3(e7.leftVertex.pos, this.view, t5), c3(e7.rightVertex.pos, this.view, t5), t5);
  }
  exceedsShortLineThreshold(e7, t5, { spatialReference: r8 }) {
    return 0 === this.squaredShortLineThreshold || c4(u3(t5, r8, E2, this.view), u3(e7, r8, E2, this.view)) > this.squaredShortLineThreshold;
  }
  isVertical(e7, s6) {
    return b(e7, s6) < p4.verticalLineThreshold;
  }
  squaredProximityThreshold(e7) {
    return "touch" === e7 ? this._squaredTouchProximityThreshold : this._squaredMouseProximityThreshold;
  }
  get _squaredMouseProximityThreshold() {
    return this.options.distance * this.options.distance;
  }
  get _squaredTouchProximityThreshold() {
    const { distance: e7, touchSensitivityMultiplier: t5 } = this.options, s6 = e7 * t5;
    return s6 * s6;
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/candidates/LineSnappingCandidate.js
var r6 = class extends t4 {
  constructor({ lineStart: e7, lineEnd: r8, targetPoint: o5, isDraped: a7 }) {
    super(o5, new G(e7, r8), a7, E3.SELF), this._referenceLineHint = new n7(u2.REFERENCE_EXTENSION, e7, r8, a7, this.domain);
  }
  get hints() {
    return [this._referenceLineHint, new n7(u2.TARGET, this._lineEndClosestToTarget(), this.targetPoint, this.isDraped, this.domain)];
  }
  _lineEndClosestToTarget() {
    return this.constraint.closestEndTo(this.targetPoint);
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/LineSnapper.js
var l6 = class extends n10 {
  snapNewVertex(e7, s6) {
    const r8 = s6.editGeometryOperations.data.components[0], i6 = r8.edges.length, n13 = [];
    if (i6 < 1) return n13;
    const { spatialReference: d4 } = s6, a7 = u3(e7, d4, E2, this.view), { view: h4 } = this, l8 = r8.edges[i6 - 1];
    let g4 = l8;
    do {
      if (this.edgeExceedsShortLineThreshold(g4, s6)) {
        const t5 = E4(g4, h4, s6);
        this._processCandidateProposal(t5.left, t5.right, e7, a7, s6, n13);
      }
      g4 = g4.leftVertex.leftEdge;
    } while (g4 && g4 !== l8);
    return n13;
  }
  snapExistingVertex(s6, i6) {
    const n13 = [], d4 = e2(i6.vertexHandle), a7 = d4.component;
    if (a7.edges.length < 2) return n13;
    const { view: h4 } = this, { spatialReference: l8 } = i6, g4 = u3(s6, l8, E2, h4), m7 = d4.leftEdge, f5 = d4.rightEdge;
    m7 && f5 && this.edgeExceedsShortLineThreshold(m7, i6) && this.edgeExceedsShortLineThreshold(f5, i6) && this._processCandidateProposal(c3(m7.leftVertex.pos, h4, i6), c3(f5.rightVertex.pos, h4, i6), s6, g4, i6, n13);
    const c5 = a7.edges[0];
    let x2 = c5;
    do {
      if (x2 !== d4.leftEdge && x2 !== d4.rightEdge && this.edgeExceedsShortLineThreshold(x2, i6)) {
        const e7 = E4(x2, h4, i6);
        this._processCandidateProposal(e7.left, e7.right, s6, g4, i6, n13);
      }
      x2 = x2.rightVertex.rightEdge;
    } while (x2 && x2 !== c5);
    return n13;
  }
  _processCandidateProposal(e7, s6, r8, o5, l8, g4) {
    var _a;
    const { spatialReference: m7, pointer: f5 } = l8, c5 = l4(_(r8, { start: e7, end: s6, type: b2.LINE }));
    c4(o5, u3(c5, m7, E2, this.view)) < this.squaredProximityThreshold(f5) && g4.push(new r6({ lineStart: e7, lineEnd: s6, targetPoint: c5, isDraped: "on-the-ground" === ((_a = l8.elevationInfo) == null ? void 0 : _a.mode) }));
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/candidates/ParallelLineSnappingCandidate.js
var g2 = class extends t4 {
  constructor({ referenceLine: i6, lineStart: f5, targetPoint: o5, isDraped: d4 }) {
    const h4 = t3(f5), { left: g4, right: p8 } = i6;
    e4(h4, u(h4, h4, p8), g4), super(o5, new G(f5, l4(h4)), d4, E3.SELF), this._referenceLines = [{ edge: i6, fadeLeft: true, fadeRight: true }];
  }
  get hints() {
    return [new n7(u2.TARGET, this.constraint.start, this.targetPoint, this.isDraped, this.domain), new r4(this.constraint.start, this.targetPoint, this.isDraped, this.domain), ...this._referenceLines.map((e7) => new n7(u2.REFERENCE, e7.edge.left, e7.edge.right, this.isDraped, this.domain, e7.fadeLeft, e7.fadeRight))];
  }
  addReferenceLine(e7) {
    const t5 = { edge: e7, fadeLeft: true, fadeRight: true };
    this._referenceLines.forEach((n13) => {
      F(e7.right, n13.edge.left) && (n13.fadeLeft = false, t5.fadeRight = false), F(e7.right, n13.edge.right) && (n13.fadeRight = false, t5.fadeRight = false), F(e7.left, n13.edge.right) && (n13.fadeRight = false, t5.fadeLeft = false), F(e7.left, n13.edge.left) && (n13.fadeLeft = false, t5.fadeLeft = false);
    }), this._referenceLines.push(t5);
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/ParallelLineSnapper.js
var g3 = class extends n10 {
  snapNewVertex(e7, t5) {
    const r8 = t5.editGeometryOperations.data.components[0], i6 = r8.edges.length, o5 = r8.vertices.length, n13 = [];
    if (i6 < 2) return n13;
    const { view: a7 } = this, h4 = u3(e7, t5.spatialReference, E2, a7), p8 = c3(r8.vertices[o5 - 1].pos, a7, t5), f5 = c3(r8.vertices[0].pos, a7, t5), g4 = r8.edges[i6 - 1];
    let m7 = g4;
    do {
      if (this.edgeExceedsShortLineThreshold(m7, t5)) {
        const r9 = E4(m7, a7, t5);
        this._checkEdgeForParallelLines(r9, p8, e7, h4, t5, n13), this._checkEdgeForParallelLines(r9, f5, e7, h4, t5, n13);
      }
      m7 = m7.leftVertex.leftEdge;
    } while (m7 && m7 !== g4);
    return n13;
  }
  snapExistingVertex(t5, r8) {
    const i6 = [], o5 = e2(r8.vertexHandle), n13 = o5.component;
    if (n13.edges.length < 3) return i6;
    const { view: a7 } = this, h4 = u3(t5, r8.spatialReference, E2, a7), p8 = o5.leftEdge, f5 = o5.rightEdge, g4 = n13.vertices[0], m7 = c3(g4.pos, a7, r8), u5 = n13.vertices.length, v2 = n13.vertices[u5 - 1], E7 = c3(v2.pos, a7, r8), L3 = n13.edges[0];
    let x2 = L3;
    do {
      if (x2 !== p8 && x2 !== f5 && this.edgeExceedsShortLineThreshold(x2, r8)) {
        const e7 = E4(x2, a7, r8);
        p8 && this._checkEdgeForParallelLines(e7, c3(p8.leftVertex.pos, a7, r8), t5, h4, r8, i6), f5 && this._checkEdgeForParallelLines(e7, c3(f5.rightVertex.pos, a7, r8), t5, h4, r8, i6), o5 === g4 ? this._checkEdgeForParallelLines(e7, E7, t5, h4, r8, i6) : o5 === v2 && this._checkEdgeForParallelLines(e7, m7, t5, h4, r8, i6);
      }
      x2 = x2.rightVertex.rightEdge;
    } while (x2 && x2 !== L3);
    return i6;
  }
  _checkEdgeForParallelLines(e7, r8, n13, l8, c5, g4) {
    var _a;
    const u5 = e7.left, v2 = e7.right;
    if (L2(m6, r8, u5, v2), b(m6, r8) < p4.parallelLineThreshold) return;
    L2(m6, n13, u5, v2, r8);
    const { spatialReference: E7, pointer: L3 } = c5, x2 = l4(r2(m6[0], m6[1], n13[2]));
    if (c4(l8, u3(x2, E7, E2, this.view)) < this.squaredProximityThreshold(L3)) {
      if (this.isVertical(x2, r8) || this.isVertical(u5, v2)) return;
      if (this._parallelToPreviousCandidate(e7, g4)) return;
      g4.push(new g2({ referenceLine: e7, lineStart: r8, targetPoint: x2, isDraped: "on-the-ground" === ((_a = c5.elevationInfo) == null ? void 0 : _a.mode) }));
    }
  }
  _parallelToPreviousCandidate(e7, r8) {
    const i6 = e7.left, s6 = e7.right;
    for (const n13 of r8) if (L2(m6, s6, n13.constraint.start, n13.constraint.end, i6), b(m6, s6) < p4.parallelLineThreshold) return n13.addReferenceLine(e7), true;
    return false;
  }
};
var m6 = n5();

// node_modules/@arcgis/core/views/interactive/snapping/RightAngleSnapper.js
var V2 = class extends n10 {
  snapNewVertex(e7, t5) {
    const i6 = t5.editGeometryOperations.data.components[0], s6 = i6.vertices.length, r8 = [];
    if (s6 < 2) return r8;
    const { view: o5 } = this, n13 = u3(e7, t5.spatialReference, E2, o5), a7 = i6.vertices[s6 - 1];
    if (this.edgeExceedsShortLineThreshold(a7.leftEdge, t5)) {
      const i7 = c3(a7.pos, o5, t5), s7 = c3(a7.leftEdge.leftVertex.pos, o5, t5);
      this._checkForSnappingCandidate(r8, s7, i7, e7, n13, t5);
    }
    const p8 = i6.vertices[0];
    if (this.edgeExceedsShortLineThreshold(p8.rightEdge, t5)) {
      const i7 = c3(p8.pos, o5, t5), s7 = c3(p8.rightEdge.rightVertex.pos, o5, t5);
      this._checkForSnappingCandidate(r8, s7, i7, e7, n13, t5);
    }
    return r8;
  }
  snapExistingVertex(t5, i6) {
    const s6 = [], r8 = e2(i6.vertexHandle);
    if (r8.component.vertices.length < 3) return s6;
    const { view: o5 } = this, n13 = u3(t5, i6.spatialReference, E2, o5), a7 = r8.leftEdge, p8 = r8.rightEdge;
    if (a7 && a7.leftVertex.leftEdge) {
      const e7 = a7.leftVertex.leftEdge;
      if (this.edgeExceedsShortLineThreshold(e7, i6)) {
        const r9 = c3(e7.rightVertex.pos, o5, i6), a8 = c3(e7.leftVertex.pos, o5, i6);
        this._checkForSnappingCandidate(s6, a8, r9, t5, n13, i6);
      }
    }
    if (p8 && p8.rightVertex.rightEdge) {
      const e7 = p8.rightVertex.rightEdge;
      if (this.edgeExceedsShortLineThreshold(e7, i6)) {
        const r9 = c3(e7.leftVertex.pos, o5, i6), a8 = c3(e7.rightVertex.pos, o5, i6);
        this._checkForSnappingCandidate(s6, a8, r9, t5, n13, i6);
      }
    }
    return s6;
  }
  _checkForSnappingCandidate(e7, o5, d4, f5, V3, j5) {
    var _a;
    const { spatialReference: k, pointer: w5 } = j5;
    o2(u4, d4, o5);
    const C = o(S2, u4[1], -u4[0], 0), T = j3(C, o2(u4, f5, d4)) / p3(C), F2 = l4(d(t3(f5), d4, C, T));
    if (c4(V3, u3(F2, k, E2, this.view)) < this.squaredProximityThreshold(w5)) {
      if (this.isVertical(F2, d4) || this.isVertical(d4, o5)) return;
      const t5 = q(n3(), d4, C, Math.sign(T));
      e7.push(new o4({ targetPoint: F2, constraint: new N(d4, l4(t5)), previousVertex: o5, otherVertex: d4, otherVertexType: n9.CENTER, isDraped: "on-the-ground" === ((_a = j5.elevationInfo) == null ? void 0 : _a.mode) }));
    }
  }
};
var u4 = n5();
var S2 = n3();

// node_modules/@arcgis/core/views/interactive/snapping/candidates/RightAngleTriangleSnappingCandidate.js
var h2 = class extends t4 {
  constructor({ targetPoint: r8, point1: a7, point2: e7, isDraped: m7 }) {
    super(r8, new w3(l4(A(n3(), a7, e7, 0.5)), 0.5 * m(a7, e7)), m7, E3.SELF), this._p1 = a7, this._p2 = e7;
  }
  get hints() {
    return [new n7(u2.REFERENCE, this.targetPoint, this._p1, this.isDraped, this.domain), new n7(u2.REFERENCE, this.targetPoint, this._p2, this.isDraped, this.domain), new s3(this._p1, this.targetPoint, this._p2, this.isDraped, this.domain)];
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/RightAngleTriangleSnapper.js
var h3 = class extends n10 {
  snapNewVertex(e7, t5) {
    const i6 = t5.editGeometryOperations.data.components[0], s6 = [], r8 = i6.vertices.length;
    if ("polygon" !== t5.editGeometryOperations.data.type || r8 < 2) return s6;
    const { view: o5 } = this, n13 = i6.vertices[0], a7 = i6.vertices[r8 - 1], d4 = c3(n13.pos, o5, t5), m7 = c3(a7.pos, o5, t5);
    return this._processCandidateProposal(d4, m7, e7, t5, s6), s6;
  }
  snapExistingVertex(t5, i6) {
    const s6 = [], r8 = e2(i6.vertexHandle), o5 = r8.component;
    if (o5.edges.length < 2) return s6;
    if ("polyline" === i6.editGeometryOperations.data.type && (0 === r8.index || r8.index === o5.vertices.length - 1)) return s6;
    const { view: n13 } = this, a7 = c3(r8.leftEdge.leftVertex.pos, n13, i6), d4 = c3(r8.rightEdge.rightVertex.pos, n13, i6);
    return this._processCandidateProposal(a7, d4, t5, i6, s6), s6;
  }
  _processCandidateProposal(e7, s6, n13, p8, h4) {
    var _a;
    if (!this.exceedsShortLineThreshold(e7, s6, p8)) return;
    const g4 = A2(f3, e7, s6, 0.5), u5 = 0.5 * m(e7, s6), v2 = n3();
    m3(v2, n13, g4, u5), v2[2] = n13[2];
    const x2 = l4(v2), { spatialReference: j5, pointer: y4 } = p8, w5 = u3(n13, j5, E2, this.view);
    if (c4(w5, u3(x2, j5, E2, this.view)) < this.squaredProximityThreshold(y4)) {
      if (this.isVertical(e7, x2) || this.isVertical(x2, s6)) return;
      h4.push(new h2({ targetPoint: x2, point1: e7, point2: s6, isDraped: "on-the-ground" === ((_a = p8.elevationInfo) == null ? void 0 : _a.mode) }));
    }
  }
};
var f3 = n5();

// node_modules/@arcgis/core/views/interactive/snapping/SelfSnappingEngine.js
var f4 = class extends v {
  constructor(s6) {
    super(s6), this.updating = false, this._snappers = new j(), this._domain = E3.SELF;
  }
  initialize() {
    this._snappers.push(new g3(this.view, this.options), new l6(this.view, this.options), new V2(this.view, this.options), new h3(this.view, this.options));
  }
  set options(s6) {
    this._set("options", s6);
    for (const o5 of this._snappers) o5.options = s6;
  }
  async fetchCandidates(s6, o5, t5) {
    if (!(o5 & this._domain && this.options.effectiveSelfEnabled)) return [];
    const i6 = [];
    for (const r8 of this._snappers.items) for (const o6 of r8.snap(s6, t5)) i6.push(o6);
    return d3(s6, i6), i6;
  }
};
e([y({ readOnly: true })], f4.prototype, "updating", void 0), e([y({ constructOnly: true })], f4.prototype, "view", void 0), e([y()], f4.prototype, "options", null), f4 = e([a2("esri.views.interactive.snapping.SelfSnappingEngine")], f4);

// node_modules/@arcgis/core/views/interactive/snapping/snappingFactory.js
function i4(i6, p8) {
  return [new f4({ view: i6, options: p8 }), new V({ view: i6, options: p8, spatialReference: i6.spatialReference })];
}

// node_modules/@arcgis/core/views/interactive/snapping/FeatureSnappingLayerSource.js
var s5 = class extends v {
  constructor(r8) {
    super(r8), this.layer = null, this.enabled = true, this.updating = false, this.availability = 1;
  }
};
e([y({ constructOnly: true })], s5.prototype, "layer", void 0), e([y()], s5.prototype, "enabled", void 0), e([y()], s5.prototype, "updating", void 0), e([y()], s5.prototype, "availability", void 0), s5 = e([a2("esri.views.interactive.snapping.FeatureSnappingLayerSource")], s5);
var p6 = s5;

// node_modules/@arcgis/core/views/interactive/snapping/SnappingOptions.js
var l7 = class extends v {
  constructor(e7) {
    super(e7), this.enabled = false, this.enabledToggled = false, this.selfEnabled = true, this.featureEnabled = true, this.featureSources = new j(), this.distance = p4.distance, this.touchSensitivityMultiplier = p4.touchSensitivityMultiplier;
  }
  get effectiveEnabled() {
    return this.enabledToggled ? !this.enabled : this.enabled;
  }
  get effectiveSelfEnabled() {
    return this.effectiveEnabled && this.selfEnabled;
  }
  get effectiveFeatureEnabled() {
    return this.effectiveEnabled && this.featureEnabled;
  }
};
e([y()], l7.prototype, "enabled", void 0), e([y()], l7.prototype, "enabledToggled", void 0), e([y()], l7.prototype, "selfEnabled", void 0), e([y()], l7.prototype, "featureEnabled", void 0), e([y({ type: j.ofType(p6) })], l7.prototype, "featureSources", void 0), e([y()], l7.prototype, "distance", void 0), e([y()], l7.prototype, "touchSensitivityMultiplier", void 0), e([y({ readOnly: true })], l7.prototype, "effectiveEnabled", null), e([y({ readOnly: true })], l7.prototype, "effectiveSelfEnabled", null), e([y({ readOnly: true })], l7.prototype, "effectiveFeatureEnabled", null), l7 = e([a2("esri.views.interactive.snapping.SnappingOptions")], l7);
var n11 = l7;

// node_modules/@arcgis/core/views/interactive/snapping/candidates/IntersectionSnappingCandidate.js
var r7 = class extends t4 {
  constructor(n13, s6, r8, o5) {
    super(n13, new b3(n13, s6.constraint, r8.constraint), o5, E3.ALL), this.first = s6, this.second = r8;
  }
  get hints() {
    return this.first.targetPoint = this.targetPoint, this.second.targetPoint = this.targetPoint, [...this.first.hints, ...this.second.hints, new o3(this.targetPoint, this.isDraped, this.domain)];
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/SnappingManager.js
var w4 = class extends n2.EventedMixin(d2) {
  constructor(e7) {
    super(e7), this.options = new n11(), this.snappingEnginesFactory = i4, this._engines = [], this._currentMainCandidate = null, this._currentOtherActiveCandidates = [], this._currentSnappedType = E6.MAIN;
  }
  initialize() {
    this.handles.add([l2(() => {
      const { effectiveFeatureEnabled: e7, effectiveSelfEnabled: t5, touchSensitivityMultiplier: n13, distance: i6 } = this.options;
      return { effectiveFeatureEnabled: e7, effectiveSelfEnabled: t5, touchSensitivityMultiplier: n13, distance: i6 };
    }, () => {
      this.doneSnapping(), this.emit("changed");
    }, U), l2(() => this.options, (e7) => {
      for (const t5 of this._engines) t5.options = e7;
    }, U), l2(() => ({ viewReady: this.view.ready, viewSpatialReference: this.view.spatialReference, snappingEnginesFactory: this.snappingEnginesFactory }), ({ viewReady: e7, snappingEnginesFactory: t5 }) => this._recreateEngines(e7, t5), w2)]);
  }
  destroy() {
    this._destroyEngines();
  }
  get updating() {
    return this._engines.some((e7) => e7.updating);
  }
  _recreateEngines(e7, t5) {
    if (this._destroyEngines(), !e7) return;
    const { view: n13, options: i6 } = this;
    this._engines = t5(n13, i6);
  }
  _destroyEngines() {
    for (const e7 of this._engines) e7.destroy();
    this._engines = [];
  }
  get _squaredMouseProximityTreshold() {
    return this.options.distance * this.options.distance;
  }
  get _squaredTouchProximityThreshold() {
    const { distance: e7, touchSensitivityMultiplier: t5 } = this.options, n13 = e7 * t5;
    return n13 * n13;
  }
  get _squaredSatisfiesConstraintThreshold() {
    return p4.satisfiesConstraintScreenThreshold * p4.satisfiesConstraintScreenThreshold;
  }
  async snap(e7) {
    return M(e7) ? this._snapMultiPoint(e7) : this._snapSinglePoint(e7);
  }
  update(e7) {
    const { point: t5, context: n13 } = e7;
    this._removeVisualization();
    const r8 = this._currentMainCandidate;
    if (t(r8)) return t5;
    const a7 = this._selectUpdateInput(e7);
    if (t(a7)) return t5;
    const { spatialReference: o5 } = n13, p8 = rn(a7, o5);
    if (t(p8)) return t5;
    const { view: c5 } = this, { elevationInfo: d4, visualizer: l8 } = n13, u5 = [], f5 = a6(p8, c5, n13), _2 = r8.constraint.closestTo(f5);
    if (!this._arePointsWithinScreenThreshold(f5, _2, n13)) return this._resetSnappingState(), t5;
    r8.targetPoint = _2, u5.push(...r8.hints);
    for (const i6 of this._currentOtherActiveCandidates) i6.targetPoint = _2, u5.push(...i6.hints);
    return r(l8) && this.handles.add(l8.draw(u5, { spatialReference: o5, elevationInfo: R(n13), view: c5, selfSnappingZ: n13.selfSnappingZ }), I2), m4(_2, c5, { z: t5.z, m: t5.m, spatialReference: t5.spatialReference, elevationInfo: d4 });
  }
  doneSnapping() {
    this._removeVisualization(), this._resetSnappingState();
  }
  _selectUpdateInput({ point: e7, scenePoint: t5 }) {
    switch (this._currentSnappedType) {
      case E6.MAIN:
        return e7;
      case E6.SCENE:
        return t5;
    }
  }
  _resetSnappingState() {
    this._currentMainCandidate = null, this._currentOtherActiveCandidates = [], this._currentSnappedType = E6.MAIN;
  }
  _removeVisualization() {
    this.handles.remove(I2);
  }
  async _snapSinglePoint({ point: e7, context: t5, signal: n13 }) {
    const { view: i6 } = this, s6 = a6(e7, i6, t5), r8 = await this._fetchCandidates(s6, E3.ALL, t5, n13);
    return this._createSnapResult(s6, E6.MAIN, r8, i6, t5, { z: e7.z, m: e7.m, spatialReference: e7.spatialReference, elevationInfo: t5.elevationInfo }, n13);
  }
  async _snapMultiPoint({ point: e7, scenePoint: t5, context: n13, signal: i6 }) {
    const { view: s6 } = this, { coordinateHelper: r8, spatialReference: a7 } = n13;
    await _n(t5.spatialReference, a7);
    const o5 = rn(t5, a7), p8 = a6(o5, s6, n13), c5 = await this._fetchCandidates(p8, E3.FEATURE, n13, i6);
    if (c5.length > 0) {
      const e8 = await this._fetchCandidates(p8, E3.SELF, n13, i6);
      return this._createSnapResult(p8, E6.SCENE, [...c5, ...e8], s6, n13, { z: o5.z, m: o5.m, spatialReference: o5.spatialReference, elevationInfo: n13.elevationInfo }, i6);
    }
    const d4 = a6(e7, s6, n13), u5 = await this._fetchCandidates(d4, E3.SELF, n13, i6);
    return this._createSnapResult(d4, E6.MAIN, u5, s6, n13, { z: r8.hasZ() && e7.hasZ ? e7.z ?? 0 : void 0, m: r8.hasM() && e7.hasM ? e7.m ?? 0 : void 0, spatialReference: e7.spatialReference, elevationInfo: n13.elevationInfo }, i6);
  }
  async _fetchCandidates(e7, t5, n13, i6) {
    return (await Promise.all(this._engines.map((s6) => s6.fetchCandidates(e7, t5, n13, i6)))).flat();
  }
  _createSnapResult(e7, t5, n13, i6, a7, o5, p8) {
    return { get valid() {
      return !p(p8);
    }, apply: () => {
      const { spatialReference: r8 } = a7, { snappedPoint: p9, hints: c5 } = this._processCandidates(e7, t5, n13, a7);
      return this._removeVisualization(), r(a7.visualizer) && this.handles.add(a7.visualizer.draw(c5, { spatialReference: r8, elevationInfo: E2, view: i6, selfSnappingZ: a7.selfSnappingZ }), I2), m4(p9, i6, o5);
    } };
  }
  _processCandidates(e7, t5, n13, i6) {
    if (n13.length < 1) return this.doneSnapping(), { snappedPoint: e7, hints: [] };
    this._currentSnappedType !== t5 && this._resetSnappingState(), d3(e7, n13);
    const r8 = this._currentMainCandidate;
    if (r(r8)) {
      const s6 = this._findOldConstraintInNewCandidates(r8, n13);
      if (s6 >= 0) {
        if (!(n13[s6] instanceof r7)) return this._intersectWithOtherCandidates(s6, n13, e7, t5, i6);
        if (this._arePointsWithinScreenThreshold(e7, r8.targetPoint, i6)) return this._updateSnappingCandidate(r8, t5, n13, i6);
      }
    }
    return this._intersectWithOtherCandidates(0, n13, e7, t5, i6);
  }
  _findOldConstraintInNewCandidates(e7, t5) {
    return e7 instanceof r7 ? this._findOldCandidateIndex(t5, e7.first) >= 0 && this._findOldCandidateIndex(t5, e7.second) >= 0 ? 0 : -1 : this._findOldCandidateIndex(t5, e7);
  }
  _intersectWithOtherCandidates(e7, t5, n13, i6, s6) {
    const { coordinateHelper: r8 } = s6, a7 = t5[e7], o5 = [];
    for (let p8 = 0; p8 < t5.length; ++p8) {
      if (p8 === e7) continue;
      const i7 = t5[p8];
      for (const e8 of a7.constraint.intersect(i7.constraint)) {
        const t6 = e8.closestTo(a7.targetPoint);
        o5.push([new r7(t6, a7, i7, i7.isDraped), this._squaredScreenDistance(n13, t6, r8)]);
      }
    }
    return o5.length > 0 && (o5.sort((e8, t6) => e8[1] - t6[1]), o5[0][1] < this._squaredPointProximityThreshold(s6.pointer)) ? this._updateSnappingCandidate(o5[0][0], i6, t5, s6) : this._updateSnappingCandidate(a7, i6, t5, s6);
  }
  _updateSnappingCandidate(e7, t5, n13, i6) {
    this.doneSnapping(), this._currentMainCandidate = e7, this._currentSnappedType = t5;
    const s6 = this._currentMainCandidate.targetPoint, r8 = [];
    r8.push(...e7.hints);
    for (const a7 of n13) {
      if (e7 instanceof r7) {
        if (a7.constraint.equals(e7.first.constraint) || a7.constraint.equals(e7.second.constraint)) continue;
      } else if (a7.constraint.equals(e7.constraint)) continue;
      const t6 = a7.constraint.closestTo(s6);
      this._squaredScreenDistance(t6, s6, i6.coordinateHelper) < this._squaredSatisfiesConstraintThreshold && (a7.targetPoint = s6, this._currentOtherActiveCandidates.push(a7), r8.push(...a7.hints));
    }
    return { snappedPoint: s6, hints: r8 };
  }
  _squaredPointProximityThreshold(e7) {
    return "touch" === e7 ? this._squaredTouchProximityThreshold : this._squaredMouseProximityTreshold;
  }
  _arePointsWithinScreenThreshold(e7, t5, n13) {
    return this._squaredScreenDistance(e7, t5, n13.coordinateHelper) < this._squaredPointProximityThreshold(n13.pointer);
  }
  _squaredScreenDistance(e7, t5, n13) {
    return c4(this._toScreen(e7, n13), this._toScreen(t5, n13));
  }
  _toScreen(e7, t5) {
    return u3(e7, t5.spatialReference, E2, this.view);
  }
  _findOldCandidateIndex(e7, t5) {
    let n13 = -1;
    for (let i6 = 0; i6 < e7.length; ++i6) if (t5.constraint.equals(e7[i6].constraint)) {
      n13 = i6;
      break;
    }
    return n13;
  }
  get test() {
    return { visualizationsActive: this.handles.has(I2), engines: this._engines };
  }
};
var E6;
e([y({ constructOnly: true })], w4.prototype, "view", void 0), e([y()], w4.prototype, "options", void 0), e([y({ readOnly: true })], w4.prototype, "updating", null), e([y()], w4.prototype, "snappingEnginesFactory", void 0), e([y()], w4.prototype, "_engines", void 0), e([y()], w4.prototype, "_squaredMouseProximityTreshold", null), e([y()], w4.prototype, "_squaredTouchProximityThreshold", null), e([y()], w4.prototype, "_squaredSatisfiesConstraintThreshold", null), w4 = e([a2("esri.views.interactive.snapping.SnappingManager")], w4), function(e7) {
  e7[e7.MAIN = 0] = "MAIN", e7[e7.SCENE = 1] = "SCENE";
}(E6 || (E6 = {}));
var I2 = "visualization-handle";
function M(e7) {
  return r(e7.scenePoint);
}
function R({ coordinateHelper: e7, elevationInfo: t5 }) {
  return e7.hasZ() ? E2 : t5;
}

// node_modules/@arcgis/core/widgets/Sketch/support/OperationHandle.js
var p7 = class extends n2.EventedAccessor {
  constructor(e7) {
    super(e7), this.cancelled = false, this.history = { undo: [], redo: [] }, this.type = null;
  }
  get tool() {
    if (!this.activeComponent) return null;
    switch (this.activeComponent.type) {
      case "graphic-mover":
      case "move-3d":
        return "move";
      case "box":
      case "transform-3d":
        return "transform";
      case "reshape":
      case "reshape-3d":
        return "reshape";
      case "draw-2d":
      case "draw-3d":
        return this.activeComponent.geometryType;
      default:
        n4(this.activeComponent);
    }
    return null;
  }
  addToHistory(e7) {
    this.history.redo = [], this.history.undo.push(e7);
  }
  resetHistory() {
    this.history.redo = [], this.history.undo = [];
  }
  canUndo() {
    return this.history.undo.length > 0;
  }
  canRedo() {
    return this.history.redo.length > 0;
  }
  complete() {
    this._reset(), this.onEnd(), this.emit("complete");
  }
  cancel() {
    this.cancelled = true, this.complete();
  }
  _reset() {
    var _a;
    (_a = this.activeComponent) == null ? void 0 : _a.reset();
  }
  refreshComponent() {
    const e7 = this.activeComponent;
    e7 && ("box" !== e7.type && "reshape" !== e7.type && "graphic-mover" !== e7.type || e7.refresh());
  }
  set undo(e7) {
    this._set("undo", () => {
      this.canUndo() && e7();
    });
  }
  set redo(e7) {
    this._set("redo", () => {
      this.canRedo() && e7();
    });
  }
};
e([y()], p7.prototype, "activeComponent", void 0), e([y()], p7.prototype, "cancelled", void 0), e([y()], p7.prototype, "history", void 0), e([y()], p7.prototype, "tool", null), e([y()], p7.prototype, "type", void 0), e([y()], p7.prototype, "canUndo", null), e([y()], p7.prototype, "canRedo", null), e([y()], p7.prototype, "onEnd", void 0), e([y()], p7.prototype, "undo", null), e([y()], p7.prototype, "redo", null), e([y()], p7.prototype, "toggleTool", void 0), e([y()], p7.prototype, "addToSelection", void 0), e([y()], p7.prototype, "removeFromSelection", void 0), p7 = e([a2("esri.widgets.Sketch.support.OperationHandle")], p7);
var n12 = class extends p7 {
};
e([y()], n12.prototype, "activeComponent", void 0), n12 = e([a2("esri.widgets.Sketch.support.CreateOperationHandle")], n12);
var i5 = class extends p7 {
};
e([y()], i5.prototype, "activeComponent", void 0), i5 = e([a2("esri.widgets.Sketch.support.UpdateOperationHandle")], i5);

// node_modules/@arcgis/core/widgets/Sketch/SketchViewModel.js
var J = { defaultZ: 0 };
var Q = { reshapeOptions: { edgeOperation: "split", shapeOperation: "move", vertexOperation: "move" }, enableMoveAllGraphics: true, enableRotation: true, enableScaling: true, multipleSelectionEnabled: true, preserveAspectRatio: false, toggleToolOnClick: true, enableZ: true, tool: "transform" };
var X = class extends n2.EventedAccessor {
  constructor(t5) {
    super(t5), this._numUpdating = 0, this._handles = new t2(), this._internalGraphicsLayer = new h({ listMode: "hide", internal: true, title: "SVM Internal" }), this._operationHandle = null, this._viewHandles = new t2(), this.activeFillSymbol = null, this.activeLineSymbol = null, this.activeVertexSymbol = null, this.allowDeleteKey = true, this.labelOptions = new c2(), this.layer = null, this.pointSymbol = new y3({ style: "circle", size: 6, color: [255, 255, 255], outline: { color: [50, 50, 50], width: 1 } }), this.polygonSymbol = new S({ color: [150, 150, 150, 0.2], outline: { color: [50, 50, 50], width: 2 } }), this.polylineSymbol = new m2({ color: [130, 130, 130, 1], width: 2 }), this._snappingManager = null, this.tooltipOptions = new p5(), this.updateGraphics = new j(), this.updateOnGraphicClick = true, this.updatePointSymbol = new y3({ size: 10, color: [0, 200, 255, 0.5], outline: { color: "black", width: 1 } }), this.updatePolygonSymbol = new S({ color: [12, 207, 255, 0.2], outline: { join: "round", color: [12, 207, 255], width: 2 } }), this.updatePolylineSymbol = new m2({ color: [12, 207, 255], width: 2 }), this.vertexSymbol = new y3({ style: "circle", size: 6, color: [255, 255, 255], outline: { color: [50, 50, 50], width: 1 } }), this._moduleLoaderAbortController = null, this._viewReadyAbortController = null, this._originalAutoOpenEnabled = null, this.defaultCreateOptions = J, this.defaultUpdateOptions = Q, this.snappingOptions = new n11();
  }
  initialize() {
    this._handles.add([a5(() => {
      var _a, _b;
      return (_b = (_a = this.view) == null ? void 0 : _a.map) == null ? void 0 : _b.layers;
    }, "change", (t5) => {
      t5.removed.includes(this.layer) && this.cancel();
    }), a5(() => {
      var _a;
      return (_a = this.layer) == null ? void 0 : _a.graphics;
    }, "change", (t5) => {
      if (r(this._operationHandle)) for (const e7 of t5.removed) this.updateGraphics.includes(e7) && (this.updateGraphics.length > 1 ? this._operationHandle.removeFromSelection(e7) : this._operationHandle.cancel());
    }), l2(() => {
      var _a;
      return ((_a = this.layer) == null ? void 0 : _a.elevationInfo) ?? null;
    }, (t5) => {
      t5 !== this._internalGraphicsLayer.elevationInfo && (this.cancel(), this._internalGraphicsLayer.elevationInfo = t5);
    }, w2), l2(() => this.view, (t5) => {
      a(this._snappingManager), t5 && (this._snappingManager = new w4({ view: t5, options: this.snappingOptions }), "2d" === t5.type ? import("./editingTools-WJ7H2UWX.js") : "3d" === t5.type && (import("./editingTools-APT3JWTM.js"), import("./GraphicsLayerView3D-D3IE67BX.js")));
    }, w2), l2(() => {
      var _a;
      return (_a = this.view) == null ? void 0 : _a.spatialReference;
    }, (t5, e7) => {
      t5 && e7 && !t5.equals(e7) && this.cancel();
    })]), m5(this);
  }
  destroy() {
    this.cancel(), this._handles = a(this._handles), this._viewHandles = a(this._viewHandles), this._removeDefaultLayer(), this._snappingManager = a(this._snappingManager), this._set("view", null), this.emit("destroy");
  }
  get _defaultUpdateTool() {
    var _a;
    return "3d" === ((_a = this.view) == null ? void 0 : _a.type) ? "move" : "transform";
  }
  get updating() {
    return this._numUpdating > 0 || r(this._snappingManager) && this._snappingManager.updating;
  }
  get activeTool() {
    var _a;
    return ((_a = this._operationHandle) == null ? void 0 : _a.tool) ?? null;
  }
  get activeComponent() {
    var _a;
    return ((_a = this._operationHandle) == null ? void 0 : _a.activeComponent) ?? null;
  }
  get createGraphic() {
    return !r(this.activeComponent) || "draw-3d" !== this.activeComponent.type && "draw-2d" !== this.activeComponent.type ? this._get("createGraphic") : e2(this.activeComponent.graphic);
  }
  get defaultCreateOptions() {
    return this._get("defaultCreateOptions");
  }
  set defaultCreateOptions(t5) {
    this._set("defaultCreateOptions", { ...J, ...t5 });
  }
  get defaultUpdateOptions() {
    return this._get("defaultUpdateOptions");
  }
  set defaultUpdateOptions(t5) {
    this._set("defaultUpdateOptions", { ...Q, ...t5, reshapeOptions: { ...Q.reshapeOptions, ...t5 == null ? void 0 : t5.reshapeOptions } });
  }
  set snappingOptions(t5) {
    r(this._snappingManager) && (this._snappingManager.options = t5), this._set("snappingOptions", t5);
  }
  get state() {
    var _a;
    const t5 = !(!((_a = this.view) == null ? void 0 : _a.ready) || !this.layer), e7 = this._operationHandle;
    return t5 && e7 ? "active" : t5 ? "ready" : "disabled";
  }
  get view() {
    return this._get("view");
  }
  set view(t5) {
    const e7 = this._get("view");
    if (e7) {
      const { container: t6, map: o6 } = e7;
      t6 && (e7.cursor = null), o6 && o6.remove(this._internalGraphicsLayer), this._viewHandles.removeAll(), this.cancel();
    }
    const o5 = "view-ready";
    this._handles.remove(o5), t5 && this._handles.add(f2(() => t5.ready, (e8) => {
      this._viewHandles.removeAll(), e8 && this._viewHandles.add(this._generateViewHandles(t5));
    }, w2), o5), this._set("view", t5);
  }
  cancel() {
    this._moduleLoaderAbortController = w(this._moduleLoaderAbortController), this._viewReadyAbortController = w(this._viewReadyAbortController), this._operationHandle && this._operationHandle.cancel();
  }
  complete() {
    this._operationHandle && this._operationHandle.complete();
  }
  delete() {
    const { state: t5, updateGraphics: e7 } = this;
    if ("active" === t5 && e7.length) {
      const { activeTool: t6, layer: o5 } = this, i6 = e7.toArray();
      o5.removeMany(i6), this.cancel(), this._emitDeleteEvent({ graphics: i6, tool: t6 });
    }
  }
  duplicate() {
    if ("active" === this.state && this.updateGraphics.length) {
      const t5 = this.updateGraphics.map((t6) => t6.clone()).toArray();
      return this.layer.addMany(t5), this.emit("duplicate", { graphics: t5, type: "duplicate" }), t5;
    }
    return [];
  }
  async create(t5, e7) {
    this.cancel(), await this._waitViewReady();
    const { view: o5, layer: i6 } = this;
    if (!o5 || "disabled" === this.state) throw i6 || this._logMissingLayer(), a3();
    if (r(o5.activeTool) && (o5.activeTool = null), !t5) return void this._logError("sketch:missing-parameter", "Missing parameter 'tool'.");
    e6(o5, this._internalGraphicsLayer);
    const a7 = await this._setupCreateOperation(t5, e7);
    if (t(a7) || this.destroyed) return void o5.map.remove(this._internalGraphicsLayer);
    const r8 = () => {
      if (a7 === this._operationHandle) {
        const e8 = this.createGraphic, o6 = this._operationHandle.cancelled;
        this._operationHandle.destroy(), this._operationHandle = null, this._set("createGraphic", null), this.view && this.view.map && this.view.map.remove(this._internalGraphicsLayer), a7.cancelled || null == e8 || i6.add(e8), this.emit("create", { graphic: e8, state: o6 ? "cancel" : "complete", tool: t5, toolEventInfo: null, type: "create" });
      }
    };
    a7.on("complete", r8), this._operationHandle = a7, o5.ready && o5.focus();
  }
  async update(t5, e7) {
    this.cancel(), await this._waitViewReady();
    const { layer: o5, view: i6, state: a7 } = this;
    if (!i6 || "disabled" === a7) throw o5 || this._logMissingLayer(), a3();
    r(i6.activeTool) && (i6.activeTool = null);
    const r8 = Array.isArray(t5) ? t5 : [t5];
    if (null == t5 || !r8 || !r8.length) return void this._logError("sketch:missing-parameter", "Missing parameter 'graphics'.");
    if (r8.some((t6) => t6.layer !== o5 ? (this._logError("sketch:invalid-parameter", "Parameter 'graphics' contains one or more graphics missing from the supplied GraphicsLayer."), true) : !!t(t6.geometry) && (this._logError("sketch:invalid-parameter", "Parameter 'graphics' contains one or more graphics with an unsupported geometry."), true))) return;
    const s6 = await this._setupUpdateOperation(r8, e7);
    this.destroyed || t(s6) || nt(s6) || (e6(i6, this._internalGraphicsLayer), this._setUpdateOperationHandle(s6, e7), this.emit("update", { graphics: r8, state: "start", aborted: false, tool: s6.tool, toolEventInfo: null, type: "update" }));
  }
  async _updateSpatialReference(t5) {
    const e7 = this.view;
    if (e7) {
      this._beginAsyncOperation(), t5 = Array.isArray(t5) ? t5 : [t5];
      for (const o5 of t5) r(o5.geometry) && "mesh" !== o5.geometry.type && !E(o5.geometry.spatialReference, e7.spatialReference) && (An(o5.geometry.spatialReference, e7.spatialReference) || en() || await tn(), o5.geometry = rn(o5.geometry, e7.spatialReference));
      this._endAsyncOperation();
    } else this._logMissingView();
  }
  undo() {
    var _a;
    this.canUndo() && ((_a = this._operationHandle) == null ? void 0 : _a.undo());
  }
  redo() {
    var _a;
    this.canRedo() && ((_a = this._operationHandle) == null ? void 0 : _a.redo());
  }
  canUndo() {
    return !(!this._operationHandle || !this._operationHandle.canUndo());
  }
  canRedo() {
    return !(!this._operationHandle || !this._operationHandle.canRedo());
  }
  toggleUpdateTool() {
    this._operationHandle && this._operationHandle.toggleTool && this._operationHandle.toggleTool();
  }
  async _getFirstHit(t5) {
    const e7 = this.view;
    if (!e7) return this._logMissingView(), null;
    if ("2d" === e7.type) {
      const o6 = [];
      e7.map.allLayers.forEach((t6) => {
        "vector-tile" !== t6.type && "imagery" !== t6.type || o6.push(t6);
      });
      const i7 = await e7.hitTest(t5, { exclude: o6 });
      return i2(i7.results);
    }
    const o5 = [e7.map.ground];
    e7.map.allLayers.forEach((t6) => {
      "integrated-mesh" === t6.type && o5.push(t6);
    });
    const i6 = await e7.hitTest(t5, { exclude: o5 });
    if (i6.results.length > 0) {
      const t6 = i6.results[0];
      if (r(t6) && "graphic" === t6.type && t6.graphic && (!i6.ground.mapPoint || e7.map.ground.opacity < 1 || i6.ground.distance - l(t6.distance, 0) > -Math.min(3 * i6.ground.distance, "global" === e7.viewingMode ? O(e7.renderCoordsHelper.spatialReference).radius / e7.renderCoordsHelper.unitInMeters : Number.POSITIVE_INFINITY))) return t6;
    }
    return null;
  }
  _generateViewHandles(t5) {
    return [t5.on("immediate-click", async (e7) => {
      var _a;
      const o5 = "active" === this.state && "create" === ((_a = this._operationHandle) == null ? void 0 : _a.type);
      if ("disabled" === this.state || o5 || !this.updateOnGraphicClick) return;
      this._beginAsyncOperation();
      const i6 = await e7.async(() => this._getFirstHit(n6(e7)));
      let a7 = null;
      if (r(i6)) {
        const o6 = i6.graphic;
        this.updateGraphics.includes(o6) || o6.layer === this.layer ? (e7.stopPropagation(), a7 = o6) : "2d" !== t5.type || this._isComponentGraphic(o6) || "active" !== this.state || this.cancel();
      } else "active" === this.state && this.cancel();
      r(a7) && !this.updateGraphics.includes(a7) && await this.update([a7], { ...this.defaultUpdateOptions, reshapeOptions: { ...this.defaultUpdateOptions.reshapeOptions } }), this._endAsyncOperation();
    }, P.WIDGET)];
  }
  async _setupCreateOperation(t5, e7) {
    const o5 = this.view;
    if (!o5) return this._logMissingView(), null;
    const i6 = { hasZ: "3d" === o5.type, ...this.defaultCreateOptions, ...e7 }, a7 = await this._setupDrawGraphicTool(t5, o5, i6);
    return t(a7) ? null : (o5.tools.add(a7), o5.activeTool = a7, this._setupCreateOperationHandle(a7));
  }
  async _setupDrawGraphicTool(t5, e7, o5) {
    if ("multipoint" === t5 && "3d" === e7.type) return this._logError("sketch:create", "Multipoint geometries are not supported in SceneView."), null;
    if (!e7) return this._logMissingView(), null;
    const i6 = "rectangle" !== t5, a7 = "rectangle" !== t5, r8 = { view: e7, mode: "rectangle" === t5 || "circle" === t5 ? "hybrid" : "click", ...o5, snapToScene: false, geometryType: t5, graphicSymbol: this._getGraphicSymbolFromTool(t5), snappingManager: this._snappingManager, forceUniformSize: a7, centered: i6 };
    return "2d" === e7.type ? this._makeDrawGraphicTool2D(r8) : this._makeDrawGraphicTool3D(r8);
  }
  async _makeDrawGraphicTool2D(t5) {
    const e7 = await this._requireModule(import("./editingTools-WJ7H2UWX.js"));
    return nt(e7) || this.destroyed ? null : new e7.module.DrawGraphicTool2D({ ...t5, activeVertexSymbol: this.activeVertexSymbol, regularVerticesSymbol: this.vertexSymbol, activeLineSymbol: this.activeLineSymbol, activeFillSymbol: ot(t5.geometryType) ? this.activeFillSymbol : null, tooltipOptions: this.tooltipOptions });
  }
  async _makeDrawGraphicTool3D(t5) {
    const e7 = await this._requireModule(import("./editingTools-APT3JWTM.js"));
    if (nt(e7) || this.destroyed) return null;
    const { elevationInfo: o5 } = this.layer;
    return new e7.module.DrawGraphicTool3D({ ...t5, elevationInfo: o5, snapToScene: !r(o5) || "absolute-height" === o5.mode, labelOptions: this.labelOptions, tooltipOptions: this.tooltipOptions });
  }
  _setupCreateOperationHandle(t5) {
    const e7 = this.view;
    if (!e7) return this._logMissingView(), null;
    let o5 = null;
    const i6 = t5.forceUniformSize, a7 = t5.centered, r8 = [e7.on("key-down", (e8) => {
      if (e8.key === e5.pan) e8.stopPropagation(), e8.repeat || (t5.enabled = false);
      else if (e8.key === e5.complete) e8.stopPropagation(), t5.completeCreateOperation();
      else if (e8.key !== e5.vertexAdd || e8.repeat) e8.key === e5.undo ? (e8.stopPropagation(), s6.undo()) : e8.key === e5.redo ? (e8.stopPropagation(), s6.redo()) : e8.key !== e5.constraint || "rectangle" !== t5.geometryType && "circle" !== t5.geometryType || e8.repeat ? e8.key === e5.center && (e8.repeat || (t5.centered = !a7, e8.stopPropagation())) : (t5.forceUniformSize = !i6, e8.stopPropagation());
      else {
        const o6 = t5.drawOperation.geometryType;
        "polyline" !== o6 && "polygon" !== o6 && "multipoint" !== o6 || (e8.stopPropagation(), t5.drawOperation.commitStagedVertex());
      }
    }, P.WIDGET), e7.on("key-up", (e8) => {
      e8.key === e5.pan ? t5.enabled = true : e8.key !== e5.constraint || "rectangle" !== t5.geometryType && "circle" !== t5.geometryType ? e8.key === e5.center && (t5.centered = a7, e8.stopPropagation()) : (t5.forceUniformSize = i6, e8.stopPropagation());
    }, P.WIDGET), t5.on("vertex-add", (e8) => {
      switch (o5 = t(o5) ? "start" : "active", e8.operation) {
        case "apply":
          this.emit("create", { graphic: e2(t5.graphic), state: o5, tool: this.activeTool, toolEventInfo: e8, type: "create" });
          break;
        case "undo":
          this._emitUndoEvent({ graphics: [e2(t5.graphic)], tool: t5.geometryType });
          break;
        case "redo":
          this._emitRedoEvent({ graphics: [e2(t5.graphic)], tool: t5.geometryType });
      }
    }), t5.on("cursor-update", (e8) => {
      t5.drawOperation.numCommittedVertices > 0 && this.emit("create", { graphic: e2(t5.graphic), state: "active", tool: this.activeTool, toolEventInfo: { coordinates: e8.vertices[0].coordinates, type: "cursor-update" }, type: "create" });
    }), t5.on("vertex-remove", (e8) => {
      switch (e8.operation) {
        case "apply":
          this.emit("create", { graphic: e2(t5.graphic), state: "active", tool: this.activeTool, toolEventInfo: e8, type: "create" });
          break;
        case "undo":
          this._emitUndoEvent({ graphics: [e2(t5.graphic)], tool: t5.geometryType });
          break;
        case "redo":
          this._emitRedoEvent({ graphics: [e2(t5.graphic)], tool: t5.geometryType });
      }
    }), t5.on("complete", (t6) => {
      this._set("createGraphic", e2(t6.graphic)), o5 = "complete", t6.aborted ? s6 && s6.cancel() : s6 && s6.complete();
    }), l2(() => this._getGraphicSymbolFromTool(t5.geometryType), (e8) => {
      t5.graphicSymbol = e8;
    })], s6 = new n12({ activeComponent: t5, tool: t5.geometryType, type: "create", onEnd: () => {
      var _a;
      r8.forEach((t6) => t6.remove()), r8.length = 0, (_a = e7.tools) == null ? void 0 : _a.remove(t5);
    }, undo: () => {
      t5.canUndo && t5.undo();
    }, redo: () => {
      t5.canRedo && t5.redo();
    }, canUndo: () => t5.canUndo, canRedo: () => t5.canRedo });
    return s6;
  }
  _getGraphicSymbolFromTool(t5) {
    switch (t5) {
      case "point":
      case "multipoint":
        return this.pointSymbol;
      case "polyline":
        return this.polylineSymbol;
      case "circle":
      case "rectangle":
      case "polygon":
        return this.polygonSymbol;
    }
  }
  async _setupUpdateOperation(t5, e7) {
    const { layer: o5, view: i6 } = this;
    if (!i6) return this._logMissingView(), null;
    const a7 = { tool: this._defaultUpdateTool, ...this.defaultUpdateOptions, ...e7, reshapeOptions: { ...this.defaultUpdateOptions.reshapeOptions, ...e7 == null ? void 0 : e7.reshapeOptions } };
    let r8 = a7.tool;
    for (const s6 of t5) o5.remove(s6), o5.add(s6);
    if ("3d" === i6.type) {
      if (0 === t5.length) return null;
      switch (r8) {
        case "move":
          return this._setupMove3DOperation(t5, a7, i6, r8);
        case "reshape": {
          if (t5.length > 1) return this._logError("sketch:reshape-multiple", "Reshape operation does not support multiple graphics."), null;
          const e8 = l5(t5[0]);
          return e8 === P2.SUPPORTED ? this._setupReshape3DOperation(t5[0], a7, i6) : (this._logError("sketch:reshape", `Reshape operation not supported for provided graphic(s) (${E5(e8)}).`), null);
        }
        case "transform":
          return this._setupGraphicTransform3DOperation(t5, a7, i6);
      }
    }
    switch (r8) {
      case "move":
        return this._setupMove2DOperation(t5, a7, i6);
      case "reshape": {
        if (t5.length > 1) return this._logError("sketch:reshape-multiple", "Reshape operation does not support multiple graphics."), null;
        const e8 = l5(t5[0]);
        return e8 === P2.SUPPORTED ? this._setupTransformOrReshape2DOperation(t5, r8, a7, i6) : (this._logError("sketch:reshape", `Reshape operation not supported for provided graphic(s) (${E5(e8)}).`), null);
      }
      case "transform":
        if (1 === t5.length) {
          const e8 = x(t5[0].geometry, "type");
          "point" !== e8 && "multipoint" !== e8 || (r8 = "reshape");
        }
        return this._setupTransformOrReshape2DOperation(t5, r8, a7, i6);
    }
  }
  async _setupMove3DOperation(t5, e7, o5, i6, a7 = false) {
    for (const l8 of t5) {
      const t6 = i(l8);
      if (t6 !== P2.SUPPORTED) return this._logError("sketch:move", `Move operation not supported for provided graphic(s) (${E5(t6)}).`), null;
    }
    const r8 = await this._requireModule(import("./editingTools-APT3JWTM.js"));
    if (nt(r8)) return r8;
    const s6 = new r8.module.GraphicMoveTool({ view: o5, enableZ: e7.enableZ, snappingManager: this._snappingManager, tooltipOptions: this.tooltipOptions });
    o5.tools.add(s6), s6.graphics.addMany(t5), a7 || this.updateGraphics.addMany(t5);
    const n13 = [], p8 = new i5({ activeComponent: s6, tool: i6, type: "update", onEnd: () => {
      var _a;
      n13.forEach((t6) => t6.remove()), n13.length = 0, (_a = o5.tools) == null ? void 0 : _a.remove(s6), s6.destroyed || s6.destroy();
    }, undo: () => {
      it(p8, this.updateGraphics.toArray()), this._emitUndoEvent({ graphics: this.updateGraphics.toArray(), tool: i6 });
    }, redo: () => {
      at(p8, this.updateGraphics.toArray()), this._emitRedoEvent({ graphics: this.updateGraphics.toArray(), tool: i6 });
    }, addToSelection: (t6) => {
      this.updateGraphics.push(t6), s6.graphics.push(t6), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [t6], removed: [], type: "selection-change" }, type: "update" });
    }, removeFromSelection: (t6) => {
      const e8 = this.updateGraphics.indexOf(t6);
      p8.history.undo.forEach((t7) => t7.updates.splice(e8, 1)), p8.history.redo.forEach((t7) => t7.updates.splice(e8, 1)), this.updateGraphics.remove(t6), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [], removed: [t6], type: "selection-change" }, type: "update" }), 0 !== this.updateGraphics.length ? s6.graphics.remove(t6) : p8.complete();
    }, toggleTool: async () => {
      if (1 !== this.updateGraphics.length || false === e7.toggleToolOnClick) return;
      if ("transform" !== i6) return;
      const t6 = this.updateGraphics.getItemAt(0);
      if (l5(t6) !== P2.SUPPORTED) return;
      const a8 = await this._setupReshape3DOperation(t6, e7, o5, true);
      nt(a8) || (p8.onEnd(), p8.destroy(), this._setUpdateOperationHandle(a8, e7));
    } });
    return n13.push(...this._getHandlesForComponent(p8, e7), o5.on("immediate-click", (t6) => this._getCommonUpdateOperationClickHandlers(p8, t6, e7), P.WIDGET), o5.on("key-down", (t6) => {
      this._getCommonUpdateOperationKeyDownHandlers(p8, t6);
    }, P.WIDGET)), p8;
  }
  _setupGraphicTransform3DOperation(t5, e7, o5, i6 = false) {
    if (1 === t5.length && i3(t5[0]) === P2.SUPPORTED) {
      const a7 = t5[0], r8 = a7.geometry;
      if (r(r8) && ("point" === r8.type || "mesh" === r8.type)) return this._setupPointTransform3DOperation(a7, e7, o5);
      if (r(r8) && ("polygon" === r8.type || "polyline" === r8.type)) return this._setupPolyTransform3DOperation(a7, e7, o5, i6);
    }
    return this._setupMove3DOperation(t5, e7, o5, "transform", i6);
  }
  async _setupPointTransform3DOperation(t5, e7, o5) {
    const i6 = "transform", { enableRotation: a7, enableScaling: r8, enableZ: s6 } = e7, n13 = await this._requireModule(import("./editingTools-APT3JWTM.js"));
    if (nt(n13)) return n13;
    const p8 = new n13.module.GraphicTransformTool({ graphic: t5, view: o5, enableRotation: a7, enableScaling: r8, enableZ: s6, snappingManager: this._snappingManager, tooltipOptions: this.tooltipOptions });
    o5.tools.add(p8), this.updateGraphics.add(t5);
    const l8 = [], h4 = new i5({ activeComponent: p8, tool: i6, type: "update", onEnd: () => {
      var _a;
      l8.forEach((t6) => t6.remove()), l8.length = 0, (_a = o5.tools) == null ? void 0 : _a.remove(p8), p8.destroyed || p8.destroy();
    }, undo: () => {
      it(h4, this.updateGraphics.toArray()), this._emitUndoEvent({ graphics: this.updateGraphics.toArray(), tool: i6 });
    }, redo: () => {
      at(h4, this.updateGraphics.toArray()), this._emitRedoEvent({ graphics: this.updateGraphics.toArray(), tool: i6 });
    }, addToSelection: async (t6) => {
      this.updateGraphics.add(t6), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [t6], removed: [], type: "selection-change" }, type: "update" });
      const i7 = await this._setupMove3DOperation(this.updateGraphics.toArray(), e7, o5, "transform", true);
      nt(i7) || (h4.onEnd(), h4.destroy(), this._setUpdateOperationHandle(i7, e7));
    }, removeFromSelection: (t6) => {
      this.updateGraphics.remove(t6), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [], removed: [t6], type: "selection-change" }, type: "update" }), h4.complete();
    }, toggleTool: () => {
    } });
    return l8.push(...this._getHandlesForComponent(h4, e7), o5.on("immediate-click", (t6) => this._getCommonUpdateOperationClickHandlers(h4, t6, e7), P.WIDGET), o5.on("key-down", (t6) => {
      this._getCommonUpdateOperationKeyDownHandlers(h4, t6);
    }, P.WIDGET)), h4;
  }
  async _setupPolyTransform3DOperation(t5, e7, o5, i6 = false) {
    const a7 = "transform", { enableRotation: r8, enableScaling: s6, enableZ: n13, preserveAspectRatio: p8 } = e7, l8 = await this._requireModule(import("./editingTools-APT3JWTM.js"));
    if (nt(l8)) return l8;
    const h4 = new l8.module.ExtentTransformTool({ graphic: t5, view: o5, enableRotation: r8, enableScaling: s6, enableZ: n13, preserveAspectRatio: p8, tooltipOptions: this.tooltipOptions });
    o5.tools.add(h4), i6 || this.updateGraphics.add(t5);
    const c5 = [], d4 = new i5({ activeComponent: h4, tool: a7, type: "update", onEnd: () => {
      var _a;
      c5.forEach((t6) => t6.remove()), c5.length = 0, (_a = o5.tools) == null ? void 0 : _a.remove(h4), h4.destroyed || h4.destroy();
    }, canUndo: () => h4.canUndo, undo: () => {
      h4.undo(), this._emitUndoEvent({ graphics: this.updateGraphics.toArray(), tool: a7 });
    }, canRedo: () => h4.canRedo, redo: () => {
      h4.redo(), this._emitRedoEvent({ graphics: this.updateGraphics.toArray(), tool: a7 });
    }, addToSelection: async (t6) => {
      this.updateGraphics.add(t6), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [t6], removed: [], type: "selection-change" }, type: "update" });
      const i7 = await this._setupMove3DOperation(this.updateGraphics.toArray(), e7, o5, "transform", true);
      nt(i7) || (d4.onEnd(), d4.destroy(), this._setUpdateOperationHandle(i7, e7));
    }, removeFromSelection: (t6) => {
      this.updateGraphics.remove(t6), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [], removed: [t6], type: "selection-change" }, type: "update" }), d4.complete();
    }, toggleTool: async () => {
      if (1 !== this.updateGraphics.length || false === e7.toggleToolOnClick) return;
      const t6 = this.updateGraphics.getItemAt(0);
      if (l5(t6) !== P2.SUPPORTED) return;
      const i7 = await this._setupReshape3DOperation(t6, e7, o5, true);
      nt(i7) || (d4.onEnd(), d4.destroy(), this._setUpdateOperationHandle(i7, e7));
    } });
    return c5.push(...this._getHandlesForComponent(d4, e7), o5.on("immediate-click", (t6) => this._getCommonUpdateOperationClickHandlers(d4, t6, e7), P.WIDGET), o5.on("key-down", (t6) => this._getCommonUpdateOperationKeyDownHandlers(d4, t6), P.WIDGET), o5.on("key-down", (t6) => {
      t6.key !== e5.constraint || t6.repeat || (h4.preserveAspectRatio = !h4.preserveAspectRatio, t6.stopPropagation());
    }, P.WIDGET), o5.on("key-up", (t6) => {
      t6.key === e5.constraint && (h4.preserveAspectRatio = !h4.preserveAspectRatio, t6.stopPropagation());
    }, P.WIDGET)), d4;
  }
  async _setupMove2DOperation(t5, e7, o5) {
    const i6 = "move";
    this.updateGraphics.addMany(t5), await this._updateSpatialReference(t5);
    const a7 = await this._getGraphicMover(t5, e7, o5);
    if (nt(a7)) return a7;
    const r8 = new i5({ activeComponent: a7, tool: i6, type: "update", onEnd: () => {
      var _a;
      this._displayDefaultCursor(), p8.forEach((t6) => t6.remove()), n13.forEach((t6) => t6.remove()), p8 = [], n13 = [], a7.destroy(), (_a = this._internalGraphicsLayer) == null ? void 0 : _a.removeMany([...this.updateGraphics.toArray()]);
    }, undo: () => {
      const t6 = this.updateGraphics.toArray();
      it(r8, t6), r8.refreshComponent(), this._emitUndoEvent({ graphics: t6, tool: i6 });
    }, redo: () => {
      const t6 = this.updateGraphics.toArray();
      at(r8, t6), r8.refreshComponent(), this._emitRedoEvent({ graphics: t6, tool: i6 });
    }, addToSelection: async (t6) => {
      await this._updateSpatialReference(t6), this.updateGraphics.push(t6), a7.graphics = this.updateGraphics.toArray(), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [t6], removed: [], type: "selection-change" }, type: "update" });
    }, removeFromSelection: (t6) => {
      const e8 = this.updateGraphics.indexOf(t6);
      r8.history.undo.forEach((t7) => t7.updates.splice(e8, 1)), r8.history.redo.forEach((t7) => t7.updates.splice(e8, 1)), this.updateGraphics.remove(t6);
      const o6 = this.updateGraphics.toArray();
      this.emit("update", { graphics: o6, state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [], removed: [t6], type: "selection-change" }, type: "update" }), 0 !== this.updateGraphics.length ? a7.graphics = o6 : r8.complete();
    } });
    let s6 = false, n13 = [o5.on("immediate-click", (t6) => this._getCommonUpdateOperationClickHandlers(r8, t6, e7), P.WIDGET), o5.on("key-down", (t6) => {
      this._getCommonUpdateOperationKeyDownHandlers(r8, t6), t6.key !== e5.constraint || t6.repeat || (s6 = true, a7.enableMoveAllGraphics = !a7.enableMoveAllGraphics);
    }, P.WIDGET), o5.on("key-up", (t6) => {
      t6.key === e5.constraint && s6 && (s6 = false, a7.enableMoveAllGraphics = !a7.enableMoveAllGraphics);
    }, P.WIDGET)], p8 = this._getHandlesForComponent(r8, e7);
    return r8;
  }
  async _setupReshape3DOperation(t5, e7, o5, i6 = false) {
    const a7 = "reshape", r8 = await this._requireModule(import("./editingTools-APT3JWTM.js"));
    if (nt(r8)) return r8;
    const s6 = e7.reshapeOptions, n13 = new r8.module.GraphicReshapeTool({ view: o5, graphic: t5, enableZVertex: e7.enableZ && "move" === (s6 == null ? void 0 : s6.vertexOperation), enableZShape: e7.enableZ && "move" === (s6 == null ? void 0 : s6.shapeOperation), enableMoveGraphic: "move" === (s6 == null ? void 0 : s6.shapeOperation) || "move-xy" === (s6 == null ? void 0 : s6.shapeOperation), enableMidpoints: "split" === (s6 == null ? void 0 : s6.edgeOperation), enableEdgeOffset: "offset" === (s6 == null ? void 0 : s6.edgeOperation), snappingManager: this._snappingManager, labelOptions: this.labelOptions, tooltipOptions: this.tooltipOptions });
    o5.tools.add(n13), i6 || this.updateGraphics.add(t5);
    const p8 = [], l8 = new i5({ activeComponent: n13, tool: a7, type: "update", onEnd: () => {
      var _a;
      p8.forEach((t6) => t6.remove()), p8.length = 0, (_a = o5.tools) == null ? void 0 : _a.remove(n13), n13.destroyed || n13.destroy();
    }, canUndo: () => n13.canUndo, undo: () => {
      n13.undo(), this._emitUndoEvent({ graphics: this.updateGraphics.toArray(), tool: a7 });
    }, canRedo: () => n13.canRedo, redo: () => {
      n13.redo(), this._emitRedoEvent({ graphics: this.updateGraphics.toArray(), tool: a7 });
    }, addToSelection: async (t6) => {
      this.updateGraphics.add(t6), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [t6], removed: [], type: "selection-change" }, type: "update" });
      const i7 = await this._setupMove3DOperation(this.updateGraphics.toArray(), e7, o5, "transform", true);
      nt(i7) || (l8.onEnd(), l8.destroy(), this._setUpdateOperationHandle(i7, e7));
    }, removeFromSelection: (t6) => {
      this.updateGraphics.remove(t6), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [], removed: [t6], type: "selection-change" }, type: "update" }), l8.complete();
    }, toggleTool: async () => {
      if (false === e7.toggleToolOnClick) return;
      const t6 = await this._setupGraphicTransform3DOperation(this.updateGraphics.toArray(), e7, o5, true);
      nt(t6) || (l8.onEnd(), l8.destroy(), this._setUpdateOperationHandle(t6, e7));
    } });
    return p8.push(...this._getHandlesForComponent(l8, e7), o5.on("immediate-click", (t6) => this._getCommonUpdateOperationClickHandlers(l8, t6, e7), P.WIDGET), o5.on("key-down", (t6) => {
      this._getCommonUpdateOperationKeyDownHandlers(l8, t6);
    }, P.WIDGET)), l8;
  }
  async _setupTransformOrReshape2DOperation(t5, e7, o5, i6) {
    this.updateGraphics.addMany(t5), await this._updateSpatialReference(t5);
    const a7 = "transform" === e7 ? await this._getBox(t5, o5, i6) : await this._getReshape(t5, o5, i6);
    if (nt(a7)) return a7;
    const r8 = new i5({ activeComponent: a7, type: "update", onEnd: () => {
      n13.forEach((t6) => t6.remove()), s6.forEach((t6) => t6.remove()), n13 = [], s6 = [], r8.activeComponent && !r8.activeComponent.destroyed && r8.activeComponent.destroy(), this._internalGraphicsLayer.removeMany(this.updateGraphics.toArray());
    }, undo: () => {
      it(r8, this.updateGraphics.toArray()), r8.refreshComponent(), this._emitUndoEvent({ graphics: this.updateGraphics.toArray(), tool: r8.tool });
    }, redo: () => {
      at(r8, this.updateGraphics.toArray()), r8.refreshComponent(), this._emitRedoEvent({ graphics: this.updateGraphics.toArray(), tool: r8.tool });
    }, addToSelection: async (t6) => {
      let e8 = r8.activeComponent;
      if ("reshape" === (e8 == null ? void 0 : e8.type)) {
        const e9 = [...this.updateGraphics, t6];
        this.updateGraphics.removeAll();
        const a8 = await this._setupMove2DOperation(e9, o5, i6);
        if (nt(a8)) return;
        r8.onEnd(), r8.destroy(), this._setUpdateOperationHandle(a8, o5);
      } else this.updateGraphics.add(t6), e8.graphics = this.updateGraphics.toArray(), e8.refresh(), r8.resetHistory();
      this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [t6], removed: [], type: "selection-change" }, type: "update" });
    }, removeFromSelection: async (t6) => {
      const e8 = this.updateGraphics.indexOf(t6);
      r8.history.undo.forEach((t7) => t7.updates.splice(e8, 1)), r8.history.redo.forEach((t7) => t7.updates.splice(e8, 1)), this.updateGraphics.remove(t6);
      const o6 = this.updateGraphics.toArray();
      if (0 === o6.length) r8.complete();
      else {
        const t7 = o6[0].geometry;
        1 !== o6.length || !r(t7) || "point" !== t7.type && "multipoint" !== t7.type ? r8.activeComponent.graphics = o6 : r8.toggleTool();
      }
      this.emit("update", { graphics: o6, state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: [], removed: [t6], type: "selection-change" }, type: "update" });
    }, toggleTool: async () => {
      var _a;
      if (this.updateGraphics.length > 1) return;
      const t6 = this.updateGraphics.getItemAt(0), e8 = t6.geometry;
      if (r(e8) && ("reshape" === r8.tool && ("point" === e8.type || "multipoint" === e8.type) || "transform" === r8.tool && "extent" === e8.type)) return;
      let a8 = null;
      "transform" === r8.tool ? a8 = await this._getReshape([t6], o5, i6) : "reshape" === r8.tool && (a8 = await this._getBox([t6], o5, i6)), nt(a8) || ((_a = r8.activeComponent) == null ? void 0 : _a.destroy(), r8.activeComponent = a8, r8.activeComponent && (n13.forEach((t7) => t7.remove()), n13 = this._getHandlesForComponent(r8, o5)));
    } });
    let s6 = [i6.on("immediate-click", (t6) => this._getCommonUpdateOperationClickHandlers(r8, t6, o5), P.WIDGET), i6.on("key-down", (t6) => {
      if (this._getCommonUpdateOperationKeyDownHandlers(r8, t6), t6.key === e5.constraint && !t6.repeat && r8) {
        const t7 = r8.activeComponent;
        t7 && "box" === t7.type && (t7.preserveAspectRatio = !t7.preserveAspectRatio);
      }
    }, P.WIDGET), i6.on("key-up", (t6) => {
      if (t6.key === e5.constraint && r8) {
        const t7 = r8.activeComponent;
        t7 && "box" === t7.type && (t7.preserveAspectRatio = !t7.preserveAspectRatio);
      }
    }, P.WIDGET)], n13 = this._getHandlesForComponent(r8, o5);
    return r8;
  }
  async _getGraphicMover(t5, e7, o5) {
    const { enableMoveAllGraphics: i6 } = e7, a7 = await this._requireModule(import("./GraphicMover-7SZAERL3.js"));
    return nt(a7) ? a7 : new a7.module.default({ enableMoveAllGraphics: i6, highlightsEnabled: true, indicatorsEnabled: false, graphics: t5, view: o5, callbacks: { onGraphicMoveStart: ({ dx: t6, dy: e8, graphic: o6 }) => {
      this._displayGrabbingCursor(), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { dx: t6, dy: e8, mover: o6, type: "move-start" }, type: "update" });
    }, onGraphicMove: ({ dx: t6, dy: e8, graphic: o6 }) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { dx: t6, dy: e8, mover: o6, type: "move" }, type: "update" }), onGraphicMoveStop: ({ dx: t6, dy: e8, graphic: o6 }) => {
      this._displayPointerCursor(), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { dx: t6, dy: e8, mover: o6, type: "move-stop" }, type: "update" });
    }, onGraphicPointerOver: () => this._displayPointerCursor(), onGraphicPointerOut: () => this._displayDefaultCursor() } });
  }
  async _getBox(t5, e7, o5) {
    const { enableRotation: i6, enableScaling: a7, preserveAspectRatio: r8 } = e7, s6 = await this._requireModule(import("./Box-2PBC536J.js"));
    return nt(s6) ? s6 : new s6.module.default({ graphics: t5, enableRotation: i6, enableScaling: a7, preserveAspectRatio: r8, layer: this._internalGraphicsLayer, view: o5, tooltipOptions: this.tooltipOptions, callbacks: { onMoveStart: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onMove: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onMoveStop: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onScaleStart: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onScale: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onScaleStop: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onRotateStart: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onRotate: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onRotateStop: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }) } });
  }
  async _getReshape(t5, e7, o5) {
    var _a, _b;
    const i6 = "split" === ((_a = e7.reshapeOptions) == null ? void 0 : _a.edgeOperation), a7 = "move" === ((_b = e7.reshapeOptions) == null ? void 0 : _b.shapeOperation), r8 = await this._requireModule(import("./Reshape-3QN3JRVX.js"));
    return nt(r8) ? r8 : new r8.module.default({ enableMidpoints: i6, enableMovement: a7, graphic: t5[0], layer: this._internalGraphicsLayer, snappingManager: this._snappingManager, tooltipOptions: this.tooltipOptions, view: o5, callbacks: { onReshapeStart: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onReshape: (t6) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { ...t6 }, type: "update" }), onReshapeStop: ({ mover: t6, type: e8 }) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6, type: e8 }, type: "update" }), onMoveStart: ({ dx: t6, dy: e8, mover: o6, type: i7 }) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { dx: t6, dy: e8, mover: o6, type: i7 }, type: "update" }), onMove: ({ dx: t6, dy: e8, mover: o6, type: i7 }) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { dx: t6, dy: e8, mover: o6, type: i7 }, type: "update" }), onMoveStop: ({ dx: t6, dy: e8, mover: o6, type: i7 }) => this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { dx: t6, dy: e8, mover: o6, type: i7 }, type: "update" }), onVertexAdd: ({ added: t6, type: e8, vertices: o6 }) => {
      const i7 = t6.map((t7) => e3(t7.geometry));
      this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { added: i7, vertices: o6, type: e8 }, type: "update" });
    }, onVertexRemove: ({ removed: t6, type: e8, vertices: o6 }) => {
      const i7 = t6.map((t7) => e3(t7.geometry));
      this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { removed: i7, vertices: o6, type: e8 }, type: "update" });
    } } });
  }
  _getHandlesForComponent(t5, e7) {
    const o5 = t5.activeComponent;
    if (!o5) return [];
    switch (o5.type) {
      case "graphic-mover":
        return [o5.on("graphic-click", ({ graphic: e8, viewEvent: o6 }) => {
          var _a;
          ((_a = o6.native) == null ? void 0 : _a.shiftKey) && (o6.stopPropagation(), t5.removeFromSelection(e8));
        }), o5.on("graphic-move-start", (e8) => t5.addToHistory(st(e8.allGraphics)))];
      case "box":
        return [o5.on("graphic-click", (o6) => this._onTransformOrReshape2DGraphicClick(t5, e7, o6)), o5.on("move-start", (e8) => t5.addToHistory(st(e8.graphics))), o5.on("rotate-start", (e8) => t5.addToHistory(st(e8.graphics))), o5.on("scale-start", (e8) => t5.addToHistory(st(e8.graphics)))];
      case "reshape":
        return [o5.on("graphic-click", (o6) => this._onTransformOrReshape2DGraphicClick(t5, e7, o6)), o5.on("move-start", (e8) => t5.addToHistory(st([e8.mover]))), o5.on("reshape-start", (e8) => t5.addToHistory(st([e8.graphic]))), o5.on("vertex-add", (e8) => t5.addToHistory(st([e8.oldGraphic]))), o5.on("vertex-remove", (e8) => t5.addToHistory(st([e8.oldGraphic])))];
      case "move-3d":
        return [o5.on("graphic-move-start", (e8) => {
          t5.addToHistory(st(e8.allGraphics)), this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { dx: 0, dy: 0, mover: e8.allGraphics.length > 0 ? e8.allGraphics[0] : null, type: "move-start" }, type: "update" });
        }), o5.on("graphic-move", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { dx: t6.dx, dy: t6.dy, mover: t6.allGraphics.length > 0 ? t6.allGraphics[0] : null, type: "move" }, type: "update" });
        }), o5.on("graphic-move-stop", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { dx: 0, dy: 0, mover: t6.allGraphics.length > 0 ? t6.allGraphics[0] : null, type: "move-stop" }, type: "update" });
        }), o5.on("immediate-click", (o6) => {
          o6.shiftKey ? this._toggleSelection([o6.graphic], t5, e7) : t5.toggleTool();
        })];
      case "transform-3d":
        return [o5.on("record-undo", ({ record: e8 }) => {
          t5.addToHistory({ updates: [e8] });
        }), o5.on("graphic-translate-start", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6.graphic, dx: t6.dxScreen, dy: t6.dyScreen, type: "move-start" }, type: "update" });
        }), o5.on("graphic-translate-stop", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6.graphic, dx: t6.dxScreen, dy: t6.dyScreen, type: "move-stop" }, type: "update" });
        }), o5.on("graphic-rotate-start", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6.graphic, angle: t6.angle, type: "rotate-start" }, type: "update" });
        }), o5.on("graphic-rotate-stop", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6.graphic, angle: t6.angle, type: "rotate-stop" }, type: "update" });
        }), o5.on("graphic-scale-start", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6.graphic, xScale: t6.xScale, yScale: t6.yScale, type: "scale-start" }, type: "update" });
        }), o5.on("graphic-scale-stop", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6.graphic, xScale: t6.xScale, yScale: t6.yScale, type: "scale-stop" }, type: "update" });
        }), o5.on("graphic-translate", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6.graphic, dx: t6.dxScreen, dy: t6.dyScreen, type: "move" }, type: "update" });
        }), o5.on("graphic-rotate", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6.graphic, angle: t6.angle, type: "rotate" }, type: "update" });
        }), o5.on("graphic-scale", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: { mover: t6.graphic, xScale: t6.xScale, yScale: t6.yScale, type: "scale" }, type: "update" });
        }), o5.on("immediate-click", (o6) => {
          o6.shiftKey ? this._toggleSelection([o6.graphic], t5, e7) : t5.toggleTool();
        })];
      case "reshape-3d":
        return [o5.on("reshape", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: t6, type: "update" });
        }), o5.on("move", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: t6, type: "update" });
        }), o5.on("vertex-add", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: t6, type: "update" });
        }), o5.on("vertex-remove", (t6) => {
          this.emit("update", { graphics: this.updateGraphics.toArray(), state: "active", aborted: false, tool: this.activeTool, toolEventInfo: t6, type: "update" });
        }), o5.on("immediate-click", (o6) => {
          o6.shiftKey ? this._toggleSelection([o6.graphic], t5, e7) : t5.toggleTool();
        })];
    }
  }
  _onTransformOrReshape2DGraphicClick(t5, e7, o5) {
    var _a;
    const { graphic: i6, viewEvent: a7 } = o5;
    return ((_a = a7.native) == null ? void 0 : _a.shiftKey) && i6.layer === this.layer ? (a7.stopPropagation(), t5.removeFromSelection(i6)) : e7.toggleToolOnClick ? (a7.stopPropagation(), t5.toggleTool()) : void 0;
  }
  _setUpdateOperationHandle(t5, e7) {
    var _a;
    this._operationHandle = t5;
    const o5 = (_a = this.view) == null ? void 0 : _a.map;
    this._disablePopup(e7);
    const i6 = () => {
      if (t5 === this._operationHandle) {
        const i7 = this.updateGraphics.toArray(), a7 = this._operationHandle.tool;
        this._operationHandle.destroy(), this._operationHandle = null, this._internalGraphicsLayer.removeMany(this.updateGraphics.toArray()), this.updateGraphics.removeAll(), o5 && o5.remove(this._internalGraphicsLayer), this._restorePopup(e7), this.emit("update", { graphics: i7, state: "complete", aborted: t5.cancelled, tool: a7, toolEventInfo: null, type: "update" });
      }
    };
    t5.on("complete", i6);
  }
  async _getCommonUpdateOperationClickHandlers(t5, e7, o5) {
    const i6 = n6(e7), a7 = await e7.async(() => this._getFirstHit(i6));
    if (t(a7)) return void t5.complete();
    if (e7.native.shiftKey && this._toggleSelection([a7.graphic], t5, o5)) return void e7.stopPropagation();
    this.updateGraphics.includes(a7.graphic) ? e7.stopPropagation() : t5.complete();
  }
  _toggleSelection(t5, e7, o5) {
    const i6 = !!o5.multipleSelectionEnabled;
    return t5.some((t6) => null != t6 && (!(!i6 || t6.layer !== this.layer) && (this.updateGraphics.includes(t6) ? e7.removeFromSelection(t6) : e7.addToSelection(t6), true)));
  }
  _getCommonUpdateOperationKeyDownHandlers(t5, e7) {
    if (!t5) return;
    const o5 = e7.key;
    o5 === e5.undo && t5.canUndo() ? (e7.stopPropagation(), t5.undo()) : o5 === e5.redo && t5.canRedo() ? (e7.stopPropagation(), t5.redo()) : o5 === e5.cancel ? (e7.stopPropagation(), t5.cancel()) : this.allowDeleteKey && e5.delete.includes(o5) && this._onDeleteKey(e7);
  }
  _onDeleteKey(t5) {
    if (!this._operationHandle || "update" !== this._operationHandle.type) return;
    const e7 = this.activeComponent, o5 = this.updateGraphics.toArray();
    t(e7) || "reshape-3d" === e7.type || ("reshape" !== e7.type || 1 === o5.length && "point" === x(o5[0].geometry, "type")) && (t5.stopPropagation(), this.delete());
  }
  _removeDefaultLayer() {
    var _a, _b;
    this._internalGraphicsLayer && ((_b = (_a = this.view) == null ? void 0 : _a.map) == null ? void 0 : _b.remove(this._internalGraphicsLayer), this._internalGraphicsLayer = a(this._internalGraphicsLayer));
  }
  _isComponentGraphic(t5) {
    const { activeComponent: e7 } = this;
    return !(!t5 || t(e7)) && (t5.attributes && t5.attributes.esriSketchTool || "draw-2d" === e7.type && e7.graphic === t5 || ("box" === e7.type || "reshape" === e7.type) && e7.isUIGraphic(t5));
  }
  _displayPointerCursor() {
    this.view && this.view.container && "pointer" !== this.view.cursor && (this.view.cursor = "pointer");
  }
  _displayGrabbingCursor() {
    this.view && this.view.container && "grabbing" !== this.view.cursor && (this.view.cursor = "grabbing");
  }
  _displayDefaultCursor() {
    this.view && this.view.container && null !== this.view.cursor && (this.view.cursor = null);
  }
  _logError(t5, e7, o5) {
    s.getLogger(this.declaredClass).error(new s2(t5, e7, o5));
  }
  async _requireModule(t5) {
    const e7 = new AbortController();
    this._moduleLoaderAbortController = e7;
    const o5 = await t5;
    return this._moduleLoaderAbortController !== e7 || e7.signal.aborted ? { requireError: "aborted" } : { module: o5 };
  }
  _emitUndoEvent(t5) {
    this.emit("undo", { ...t5, type: "undo" });
  }
  _emitRedoEvent(t5) {
    this.emit("redo", { ...t5, type: "redo" });
  }
  _emitDeleteEvent(t5) {
    this.emit("delete", { ...t5, type: "delete" });
  }
  get test() {
    return { operationHandle: this._operationHandle, defaultUpdateOptions: Q };
  }
  wait() {
    return j2(() => !this.updating);
  }
  _beginAsyncOperation() {
    this._numUpdating += 1, this.notifyChange("updating");
  }
  _endAsyncOperation() {
    this._numUpdating -= 1, this.notifyChange("updating");
  }
  _disablePopupEnabled(t5) {
    var _a, _b;
    return "3d" !== ((_a = this.view) == null ? void 0 : _a.type) || this.updateOnGraphicClick || (((_b = e2(t5)) == null ? void 0 : _b.toggleToolOnClick) ?? false);
  }
  _disablePopup(t5) {
    var _a;
    if (!this._disablePopupEnabled(t5)) return;
    const e7 = (_a = this.view) == null ? void 0 : _a.popup;
    e7 && t(this._originalAutoOpenEnabled) && (this._originalAutoOpenEnabled = e7.autoOpenEnabled, e7.autoOpenEnabled = false);
  }
  _restorePopup(t5) {
    var _a;
    if (!this._disablePopupEnabled(t5)) return;
    const e7 = (_a = this.view) == null ? void 0 : _a.popup;
    e7 && r(this._originalAutoOpenEnabled) && (e7.autoOpenEnabled = this._originalAutoOpenEnabled, this._originalAutoOpenEnabled = null);
  }
  async _waitViewReady() {
    const t5 = this.view;
    t5 ? (w(this._viewReadyAbortController), this._viewReadyAbortController = new AbortController(), await y2(j2(() => t5 == null ? void 0 : t5.ready), this._viewReadyAbortController.signal)) : this._logMissingView();
  }
  _logMissingView() {
    this._logError("sketch:missing-property", et("view"));
  }
  _logMissingLayer() {
    this._logError(tt, et("layer"));
  }
};
e([y()], X.prototype, "updating", null), e([y()], X.prototype, "_operationHandle", void 0), e([y({ readOnly: true })], X.prototype, "activeTool", null), e([y()], X.prototype, "activeFillSymbol", void 0), e([y()], X.prototype, "activeLineSymbol", void 0), e([y()], X.prototype, "activeVertexSymbol", void 0), e([y()], X.prototype, "allowDeleteKey", void 0), e([y({ readOnly: true })], X.prototype, "createGraphic", null), e([y()], X.prototype, "defaultCreateOptions", null), e([y()], X.prototype, "defaultUpdateOptions", null), e([y({ type: c2, nonNullable: true })], X.prototype, "labelOptions", void 0), e([y()], X.prototype, "layer", void 0), e([y({ types: j4 })], X.prototype, "pointSymbol", void 0), e([y({ types: j4 })], X.prototype, "polygonSymbol", void 0), e([y({ types: j4 })], X.prototype, "polylineSymbol", void 0), e([y({ type: n11, nonNullable: true })], X.prototype, "snappingOptions", null), e([y()], X.prototype, "_snappingManager", void 0), e([y({ readOnly: true })], X.prototype, "state", null), e([y({ type: p5, nonNullable: true })], X.prototype, "tooltipOptions", void 0), e([y({ readOnly: true })], X.prototype, "updateGraphics", void 0), e([y()], X.prototype, "updateOnGraphicClick", void 0), e([y({ types: j4 })], X.prototype, "updatePointSymbol", void 0), e([y({ types: j4 })], X.prototype, "updatePolygonSymbol", void 0), e([y({ types: j4 })], X.prototype, "updatePolylineSymbol", void 0), e([y({ types: j4 })], X.prototype, "vertexSymbol", void 0), e([y({ value: null })], X.prototype, "view", null), X = e([a2("esri.widgets.Sketch.SketchViewModel")], X);
var tt = "sketch:missing-property";
var et = (t5) => `Property '${t5}' is missing on SketchViewModel.`;
function ot(t5) {
  return "polygon" === t5 || "rectangle" === t5 || "circle" === t5;
}
function it(t5, e7) {
  rt("undo", t5.history.undo, t5.history.redo, e7);
}
function at(t5, e7) {
  rt("redo", t5.history.redo, t5.history.undo, e7);
}
function rt(t5, e7, o5, i6) {
  const a7 = e7.pop();
  if (!a7) return;
  const r8 = a7.updates, s6 = [];
  i6.forEach((e8, o6) => {
    const i7 = r8[o6];
    null != i7 && ("geometry" in i7 && r(i7.geometry) && (s6.push({ geometry: e8.geometry }), e8.geometry = i7.geometry), "symbol" in i7 && r(i7.symbol) && (s6.push({ symbol: e8.symbol }), e8.symbol = i7.symbol), "undo" in i7 && (s6.push(i7), i7[t5](e8)));
  }), o5.push({ updates: s6 });
}
function st(t5) {
  return { updates: t5.map((t6) => ({ geometry: t6.geometry })) };
}
function nt(t5) {
  return "requireError" in t5 && "aborted" === t5.requireError;
}
var pt = X;

export {
  p6 as p,
  pt
};
//# sourceMappingURL=chunk-O2MGOBA7.js.map
