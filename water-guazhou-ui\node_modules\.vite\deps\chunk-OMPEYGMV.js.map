{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/lib/DoubleArray.js", "../../@arcgis/core/geometry/support/triangulationUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{NATIVE_ARRAY_MAX_SIZE as r}from\"../../../../core/typedArrayUtil.js\";function n(n,t=!1){return n<=r?t?new Array(n).fill(0):new Array(n):new Float64Array(n)}function t(n){return length<=r?Array.from(n):new Float64Array(n)}function a(r,n,t){return Array.isArray(r)?r.slice(n,n+t):r.subarray(n,n+t)}export{t as doubleArrayFrom,a as doubleSubArray,n as newDoubleArray};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{e as n}from\"../../chunks/earcut.js\";import{isClockwise as t}from\"./coordsUtils.js\";import{deduplicate as e}from\"./meshUtils/deduplicate.js\";import{doubleSubArray as o,newDoubleArray as r}from\"../../views/3d/webgl-engine/lib/DoubleArray.js\";function i(t){const r=l(t.rings,t.hasZ,f.CCW_IS_HOLE),i=new Array;let c=0,h=0;for(const e of r.polygons){const t=e.count,s=e.index,l=o(r.position,3*s,3*t),f=e.holeIndices.map((n=>n-s)),g=new Uint32Array(n(l,f,3));i.push({position:l,faces:g}),c+=l.length,h+=g.length}const g=s(i,c,h),a=Array.isArray(g.position)?e(g.position,3,{originalIndices:g.faces}):e(g.position.buffer,6,{originalIndices:g.faces});return g.position=new Float64Array(a.buffer),g.faces=a.indices,g}function s(n,t,e){if(1===n.length)return n[0];const o=r(t),i=new Uint32Array(e);let s=0,l=0,c=0;for(const r of n){for(let n=0;n<r.position.length;n++)o[s++]=r.position[n];for(let n=0;n<r.faces.length;n++)i[l++]=r.faces[n]+c;c=s/3}return{position:o,faces:i}}function l(n,t,e){const o=n.length,i=new Array(o),s=new Array(o),l=new Array(o);let g=0,a=0,u=0,p=0;for(let r=0;r<o;++r)p+=n[r].length;const d=r(3*p);let y=0;for(let r=o-1;r>=0;r--){const p=n[r],A=e===f.CCW_IS_HOLE&&h(p);if(A&&1!==o)i[g++]=p;else{let n=p.length;for(let t=0;t<g;++t)n+=i[t].length;const e={index:y,pathLengths:new Array(g+1),count:n,holeIndices:new Array(g)};e.pathLengths[0]=p.length,p.length>0&&(l[u++]={index:y,count:p.length}),y=A?c(p,p.length-1,-1,d,y,p.length,t):c(p,0,1,d,y,p.length,t);for(let o=0;o<g;++o){const n=i[o];e.holeIndices[o]=y,e.pathLengths[o+1]=n.length,n.length>0&&(l[u++]={index:y,count:n.length}),y=c(n,0,1,d,y,n.length,t)}g=0,e.count>0&&(s[a++]=e)}}for(let r=0;r<g;++r){const n=i[r];n.length>0&&(l[u++]={index:y,count:n.length}),y=c(n,0,1,d,y,n.length,t)}return s.length=a,l.length=u,{position:d,polygons:s,outlines:l}}function c(n,t,e,o,r,i,s){r*=3;for(let l=0;l<i;++l){const i=n[t];o[r++]=i[0],o[r++]=i[1],o[r++]=s?i[2]:0,t+=e}return r/3}function h(n){return!t(n,!1,!1)}var f;!function(n){n[n.NONE=0]=\"NONE\",n[n.CCW_IS_HOLE=1]=\"CCW_IS_HOLE\"}(f||(f={}));export{f as CounterClockwiseMode,l as pathsToTriangulationInfo,i as triangulate};\n"], "mappings": ";;;;;;;;;;;;;;AAI2E,SAAS,EAAEA,IAAEC,KAAE,OAAG;AAAC,SAAOD,MAAG,IAAEC,KAAE,IAAI,MAAMD,EAAC,EAAE,KAAK,CAAC,IAAE,IAAI,MAAMA,EAAC,IAAE,IAAI,aAAaA,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,UAAQ,IAAE,MAAM,KAAKA,EAAC,IAAE,IAAI,aAAaA,EAAC;AAAC;AAAC,SAAS,EAAEE,IAAEF,IAAEC,IAAE;AAAC,SAAO,MAAM,QAAQC,EAAC,IAAEA,GAAE,MAAMF,IAAEA,KAAEC,EAAC,IAAEC,GAAE,SAASF,IAAEA,KAAEC,EAAC;AAAC;;;ACAtD,SAAS,EAAEE,IAAE;AAAC,QAAMC,KAAE,EAAED,GAAE,OAAMA,GAAE,MAAK,EAAE,WAAW,GAAEE,KAAE,IAAI;AAAM,MAAIC,KAAE,GAAEC,KAAE;AAAE,aAAU,KAAKH,GAAE,UAAS;AAAC,UAAMD,KAAE,EAAE,OAAMK,KAAE,EAAE,OAAMC,KAAE,EAAEL,GAAE,UAAS,IAAEI,IAAE,IAAEL,EAAC,GAAEO,KAAE,EAAE,YAAY,IAAK,CAAAC,OAAGA,KAAEH,EAAE,GAAEI,KAAE,IAAI,YAAYR,GAAEK,IAAEC,IAAE,CAAC,CAAC;AAAE,IAAAL,GAAE,KAAK,EAAC,UAASI,IAAE,OAAMG,GAAC,CAAC,GAAEN,MAAGG,GAAE,QAAOF,MAAGK,GAAE;AAAA,EAAM;AAAC,QAAM,IAAE,EAAEP,IAAEC,IAAEC,EAAC,GAAEM,KAAE,MAAM,QAAQ,EAAE,QAAQ,IAAE,EAAE,EAAE,UAAS,GAAE,EAAC,iBAAgB,EAAE,MAAK,CAAC,IAAE,EAAE,EAAE,SAAS,QAAO,GAAE,EAAC,iBAAgB,EAAE,MAAK,CAAC;AAAE,SAAO,EAAE,WAAS,IAAI,aAAaA,GAAE,MAAM,GAAE,EAAE,QAAMA,GAAE,SAAQ;AAAC;AAAC,SAAS,EAAEF,IAAER,IAAE,GAAE;AAAC,MAAG,MAAIQ,GAAE,OAAO,QAAOA,GAAE,CAAC;AAAE,QAAM,IAAE,EAAER,EAAC,GAAEE,KAAE,IAAI,YAAY,CAAC;AAAE,MAAIG,KAAE,GAAEC,KAAE,GAAEH,KAAE;AAAE,aAAUF,MAAKO,IAAE;AAAC,aAAQA,KAAE,GAAEA,KAAEP,GAAE,SAAS,QAAOO,KAAI,GAAEH,IAAG,IAAEJ,GAAE,SAASO,EAAC;AAAE,aAAQA,KAAE,GAAEA,KAAEP,GAAE,MAAM,QAAOO,KAAI,CAAAN,GAAEI,IAAG,IAAEL,GAAE,MAAMO,EAAC,IAAEL;AAAE,IAAAA,KAAEE,KAAE;AAAA,EAAC;AAAC,SAAM,EAAC,UAAS,GAAE,OAAMH,GAAC;AAAC;AAAC,SAAS,EAAEM,IAAER,IAAE,GAAE;AAAC,QAAM,IAAEQ,GAAE,QAAON,KAAE,IAAI,MAAM,CAAC,GAAEG,KAAE,IAAI,MAAM,CAAC,GAAEC,KAAE,IAAI,MAAM,CAAC;AAAE,MAAI,IAAE,GAAEI,KAAE,GAAE,IAAE,GAAE,IAAE;AAAE,WAAQT,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,MAAGO,GAAEP,EAAC,EAAE;AAAO,QAAM,IAAE,EAAE,IAAE,CAAC;AAAE,MAAIU,KAAE;AAAE,WAAQV,KAAE,IAAE,GAAEA,MAAG,GAAEA,MAAI;AAAC,UAAMW,KAAEJ,GAAEP,EAAC,GAAE,IAAE,MAAI,EAAE,eAAaG,GAAEQ,EAAC;AAAE,QAAG,KAAG,MAAI,EAAE,CAAAV,GAAE,GAAG,IAAEU;AAAA,SAAM;AAAC,UAAIJ,KAAEI,GAAE;AAAO,eAAQZ,KAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAQ,MAAGN,GAAEF,EAAC,EAAE;AAAO,YAAMa,KAAE,EAAC,OAAMF,IAAE,aAAY,IAAI,MAAM,IAAE,CAAC,GAAE,OAAMH,IAAE,aAAY,IAAI,MAAM,CAAC,EAAC;AAAE,MAAAK,GAAE,YAAY,CAAC,IAAED,GAAE,QAAOA,GAAE,SAAO,MAAIN,GAAE,GAAG,IAAE,EAAC,OAAMK,IAAE,OAAMC,GAAE,OAAM,IAAGD,KAAE,IAAE,EAAEC,IAAEA,GAAE,SAAO,GAAE,IAAG,GAAED,IAAEC,GAAE,QAAOZ,EAAC,IAAE,EAAEY,IAAE,GAAE,GAAE,GAAED,IAAEC,GAAE,QAAOZ,EAAC;AAAE,eAAQc,KAAE,GAAEA,KAAE,GAAE,EAAEA,IAAE;AAAC,cAAMN,KAAEN,GAAEY,EAAC;AAAE,QAAAD,GAAE,YAAYC,EAAC,IAAEH,IAAEE,GAAE,YAAYC,KAAE,CAAC,IAAEN,GAAE,QAAOA,GAAE,SAAO,MAAIF,GAAE,GAAG,IAAE,EAAC,OAAMK,IAAE,OAAMH,GAAE,OAAM,IAAGG,KAAE,EAAEH,IAAE,GAAE,GAAE,GAAEG,IAAEH,GAAE,QAAOR,EAAC;AAAA,MAAC;AAAC,UAAE,GAAEa,GAAE,QAAM,MAAIR,GAAEK,IAAG,IAAEG;AAAA,IAAE;AAAA,EAAC;AAAC,WAAQZ,KAAE,GAAEA,KAAE,GAAE,EAAEA,IAAE;AAAC,UAAMO,KAAEN,GAAED,EAAC;AAAE,IAAAO,GAAE,SAAO,MAAIF,GAAE,GAAG,IAAE,EAAC,OAAMK,IAAE,OAAMH,GAAE,OAAM,IAAGG,KAAE,EAAEH,IAAE,GAAE,GAAE,GAAEG,IAAEH,GAAE,QAAOR,EAAC;AAAA,EAAC;AAAC,SAAOK,GAAE,SAAOK,IAAEJ,GAAE,SAAO,GAAE,EAAC,UAAS,GAAE,UAASD,IAAE,UAASC,GAAC;AAAC;AAAC,SAAS,EAAEE,IAAER,IAAE,GAAE,GAAEC,IAAEC,IAAEG,IAAE;AAAC,EAAAJ,MAAG;AAAE,WAAQK,KAAE,GAAEA,KAAEJ,IAAE,EAAEI,IAAE;AAAC,UAAMJ,KAAEM,GAAER,EAAC;AAAE,MAAEC,IAAG,IAAEC,GAAE,CAAC,GAAE,EAAED,IAAG,IAAEC,GAAE,CAAC,GAAE,EAAED,IAAG,IAAEI,KAAEH,GAAE,CAAC,IAAE,GAAEF,MAAG;AAAA,EAAC;AAAC,SAAOC,KAAE;AAAC;AAAC,SAASG,GAAEI,IAAE;AAAC,SAAM,CAAC,EAAEA,IAAE,OAAG,KAAE;AAAC;AAAC,IAAI;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,cAAY,CAAC,IAAE;AAAa,EAAE,MAAI,IAAE,CAAC,EAAE;", "names": ["n", "t", "r", "t", "r", "i", "c", "h", "s", "l", "f", "n", "g", "a", "y", "p", "e", "o"]}