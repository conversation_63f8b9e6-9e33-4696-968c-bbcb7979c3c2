import {
  S,
  a
} from "./chunk-PNIF6I3E.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/renderers/support/rendererConversion.js
function s2(r2) {
  return t(r2) || "simple" === r2.type || "unique-value" === r2.type || "class-breaks" === r2.type || "dictionary" === r2.type || "heatmap" === r2.type;
}
function u(n, o) {
  if (t(n)) return null;
  if (!s2(n)) return new s("renderer-conversion-3d:unsupported-renderer", `Unsupported renderer of type '${n.type || n.declaredClass}'`, { renderer: n });
  switch (n.type) {
    case "simple":
      return i(n);
    case "unique-value":
      return a2(n, o);
    case "class-breaks":
      return p(n);
    case "dictionary":
    case "heatmap":
      return null;
  }
  return null;
}
function l(e, n) {
  if (!n) return null;
  let o;
  if (o = Array.isArray(n) ? n : [n], o.length > 0) {
    const n2 = o.map((r2) => r2.details.symbol.type || r2.details.symbol.declaredClass).filter((r2) => !!r2);
    n2.sort();
    const t2 = [];
    return n2.forEach((r2, e2) => {
      0 !== e2 && r2 === n2[e2 - 1] || t2.push(r2);
    }), new s("renderer-conversion-3d:unsupported-symbols", `Renderer contains symbols (${t2.join(", ")}) which are not supported in 3D`, { renderer: e, symbolErrors: o });
  }
  return null;
}
function i(r2) {
  return l(r2, S(r2.symbol).error);
}
function a2(r2, e) {
  var _a;
  const s3 = { ...a, ...e }, u2 = (_a = r2.uniqueValueInfos) == null ? void 0 : _a.map((r3) => S(r3.symbol, s3).error).filter(r), i2 = S(r2.defaultSymbol, s3);
  return i2.error && (u2 == null ? void 0 : u2.unshift(i2.error)), l(r2, u2);
}
function p(r2) {
  const e = r2.classBreakInfos.map((r3) => S(r3.symbol).error).filter(r), t2 = S(r2.defaultSymbol);
  return t2.error && e.unshift(t2.error), l(r2, e);
}

export {
  s2 as s,
  u
};
//# sourceMappingURL=chunk-Y5QAWFQI.js.map
