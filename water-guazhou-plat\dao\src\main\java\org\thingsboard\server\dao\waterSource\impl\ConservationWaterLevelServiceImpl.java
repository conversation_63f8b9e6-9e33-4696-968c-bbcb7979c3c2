package org.thingsboard.server.dao.waterSource.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationAnalysis;
import org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationWaterLevel;
import org.thingsboard.server.dao.sql.conservationWaterLevel.ConservationAnalysisMapper;
import org.thingsboard.server.dao.sql.conservationWaterLevel.ConservationWaterLevelMapper;
import org.thingsboard.server.dao.waterSource.ConservationWaterLevelService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 涵养水位服务实现类
 */
@Slf4j
@Service
public class ConservationWaterLevelServiceImpl implements ConservationWaterLevelService {

    @Autowired
    private ConservationWaterLevelMapper waterLevelMapper;

    @Autowired
    private ConservationAnalysisMapper analysisMapper;

    private static final String ALGORITHM_VERSION = "v1.0.0";

    @Override
    @Transactional
    public ConservationWaterLevel saveWaterLevel(ConservationWaterLevel entity) {
        entity.setId(UUID.randomUUID().toString());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        
        // 计算水位变化量
        if (entity.getRawWaterLevel() != null && entity.getGroundwaterLevel() != null) {
            BigDecimal levelChange = entity.getRawWaterLevel().subtract(entity.getGroundwaterLevel());
            entity.setLevelChange(levelChange);
        }
        
        waterLevelMapper.insert(entity);
        return entity;
    }

    @Override
    @Transactional
    public ConservationWaterLevel updateWaterLevel(ConservationWaterLevel entity) {
        entity.setUpdateTime(new Date());
        
        // 重新计算水位变化量
        if (entity.getRawWaterLevel() != null && entity.getGroundwaterLevel() != null) {
            BigDecimal levelChange = entity.getRawWaterLevel().subtract(entity.getGroundwaterLevel());
            entity.setLevelChange(levelChange);
        }
        
        waterLevelMapper.updateById(entity);
        return entity;
    }

    @Override
    @Transactional
    public void deleteWaterLevel(String id) {
        waterLevelMapper.deleteById(id);
    }

    @Override
    public ConservationWaterLevel getWaterLevelById(String id) {
        return waterLevelMapper.selectById(id);
    }

    @Override
    public Map<String, Object> getWaterLevelList(Map<String, Object> params) {
        // 计算分页偏移量
        if (params.get("pageNum") != null && params.get("pageSize") != null) {
            int pageNum = (Integer) params.get("pageNum");
            int pageSize = (Integer) params.get("pageSize");
            params.put("offset", (pageNum - 1) * pageSize);
        }

        List<ConservationWaterLevel> list = waterLevelMapper.getList(params);
        int total = waterLevelMapper.getCount(params);

        Map<String, Object> result = new HashMap<>();
        result.put("list", list);
        result.put("total", total);
        return result;
    }

    @Override
    public List<ConservationWaterLevel> getWaterLevelChangeData(String stationId, Date startTime, Date endTime) {
        return waterLevelMapper.getWaterLevelChangeData(stationId, startTime, endTime);
    }

    @Override
    public ConservationWaterLevel getLatestWaterLevel(String stationId) {
        return waterLevelMapper.getLatestWaterLevel(stationId);
    }

    @Override
    public Map<String, Object> getStatisticsData(String stationId, Date startTime, Date endTime) {
        return waterLevelMapper.getStatisticsData(stationId, startTime, endTime);
    }

    @Override
    @Transactional
    public void batchImportWaterLevel(List<ConservationWaterLevel> list) {
        if (list != null && !list.isEmpty()) {
            Date now = new Date();
            for (ConservationWaterLevel entity : list) {
                entity.setId(UUID.randomUUID().toString());
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                
                // 计算水位变化量
                if (entity.getRawWaterLevel() != null && entity.getGroundwaterLevel() != null) {
                    BigDecimal levelChange = entity.getRawWaterLevel().subtract(entity.getGroundwaterLevel());
                    entity.setLevelChange(levelChange);
                }
            }
            waterLevelMapper.batchInsert(list);
        }
    }

    @Override
    @Transactional
    public ConservationAnalysis performIntelligentAnalysis(String stationId, Date startTime, Date endTime, String tenantId, String creator) {
        log.info("开始执行涵养水位智能分析，测点ID: {}, 时间范围: {} - {}", stationId, startTime, endTime);
        
        // 获取分析期间的水位数据
        List<ConservationWaterLevel> waterLevelData = getWaterLevelChangeData(stationId, startTime, endTime);
        
        if (waterLevelData.isEmpty()) {
            throw new RuntimeException("指定时间范围内没有水位数据，无法进行分析");
        }

        // 创建分析结果对象
        ConservationAnalysis analysis = new ConservationAnalysis();
        analysis.setId(UUID.randomUUID().toString());
        analysis.setTenantId(tenantId);
        analysis.setStationId(stationId);
        analysis.setStartTime(startTime);
        analysis.setEndTime(endTime);
        analysis.setCreator(creator);
        analysis.setCreateTime(new Date());
        analysis.setUpdateTime(new Date());
        analysis.setStatus(1); // 分析中
        analysis.setAlgorithmVersion(ALGORITHM_VERSION);

        try {
            // 执行智能分析算法
            performAnalysisAlgorithm(analysis, waterLevelData);
            analysis.setStatus(2); // 已完成
            
        } catch (Exception e) {
            log.error("智能分析执行失败", e);
            analysis.setStatus(3); // 分析失败
            analysis.setRiskDescription("分析过程中发生错误: " + e.getMessage());
        }

        // 保存分析结果
        analysisMapper.insert(analysis);
        
        log.info("涵养水位智能分析完成，分析ID: {}", analysis.getId());
        return analysis;
    }

    /**
     * 执行智能分析算法
     */
    private void performAnalysisAlgorithm(ConservationAnalysis analysis, List<ConservationWaterLevel> waterLevelData) {
        // 1. 基础数据统计
        ConservationWaterLevel firstRecord = waterLevelData.get(0);
        ConservationWaterLevel lastRecord = waterLevelData.get(waterLevelData.size() - 1);

        analysis.setInitialLevel(firstRecord.getGroundwaterLevel());
        analysis.setFinalLevel(lastRecord.getGroundwaterLevel());

        if (analysis.getInitialLevel() != null && analysis.getFinalLevel() != null) {
            analysis.setLevelChange(analysis.getFinalLevel().subtract(analysis.getInitialLevel()));
        }

        // 2. 计算平均值和总量
        BigDecimal totalRainfall = BigDecimal.ZERO;
        BigDecimal totalEvaporation = BigDecimal.ZERO;
        BigDecimal totalExtraction = BigDecimal.ZERO;
        int validRecords = 0;

        for (ConservationWaterLevel record : waterLevelData) {
            if (record.getRainfallAmount() != null) {
                totalRainfall = totalRainfall.add(record.getRainfallAmount());
            }
            if (record.getEvaporationAmount() != null) {
                totalEvaporation = totalEvaporation.add(record.getEvaporationAmount());
            }
            if (record.getExtractionAmount() != null) {
                totalExtraction = totalExtraction.add(record.getExtractionAmount());
            }
            validRecords++;
        }

        analysis.setAvgRainfall(totalRainfall.divide(BigDecimal.valueOf(validRecords), 2, RoundingMode.HALF_UP));
        analysis.setAvgEvaporation(totalEvaporation.divide(BigDecimal.valueOf(validRecords), 2, RoundingMode.HALF_UP));
        analysis.setTotalExtraction(totalExtraction);

        // 3. 计算涵养系数
        BigDecimal conservationCoefficient = calculateConservationCoefficient(waterLevelData);
        analysis.setConservationCoefficient(conservationCoefficient);

        // 4. 计算涵养潜力评分
        BigDecimal conservationPotential = calculateConservationPotential(analysis, waterLevelData);
        analysis.setConservationPotential(conservationPotential);

        // 5. 风险评估
        int riskLevel = assessRiskLevel(analysis, waterLevelData);
        analysis.setRiskLevel(riskLevel);
        analysis.setRiskDescription(generateRiskDescription(riskLevel, analysis));

        // 6. 生成涵养建议
        String suggestion = generateConservationSuggestion(analysis, waterLevelData);
        analysis.setConservationSuggestion(suggestion);

        // 7. 计算建议涵养量
        BigDecimal suggestedAmount = calculateSuggestedConservationAmount(analysis, waterLevelData);
        analysis.setSuggestedConservationAmount(suggestedAmount);

        // 8. 生成详细分析结果
        Map<String, Object> details = generateAnalysisDetails(analysis, waterLevelData);
        analysis.setAnalysisDetails(JSON.toJSONString(details));
    }

    /**
     * 计算涵养系数
     */
    private BigDecimal calculateConservationCoefficient(List<ConservationWaterLevel> waterLevelData) {
        // 涵养系数 = (降雨量 - 蒸发量 - 地表径流) / 水位变化量
        BigDecimal totalRainfall = BigDecimal.ZERO;
        BigDecimal totalEvaporation = BigDecimal.ZERO;
        BigDecimal totalRunoff = BigDecimal.ZERO;
        BigDecimal totalLevelChange = BigDecimal.ZERO;
        int validRecords = 0;

        for (ConservationWaterLevel record : waterLevelData) {
            if (record.getRainfallAmount() != null && record.getEvaporationAmount() != null &&
                record.getSurfaceRunoff() != null && record.getLevelChange() != null) {

                totalRainfall = totalRainfall.add(record.getRainfallAmount());
                totalEvaporation = totalEvaporation.add(record.getEvaporationAmount());
                totalRunoff = totalRunoff.add(record.getSurfaceRunoff());
                totalLevelChange = totalLevelChange.add(record.getLevelChange().abs());
                validRecords++;
            }
        }

        if (validRecords == 0 || totalLevelChange.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.valueOf(0.5); // 默认系数
        }

        BigDecimal effectiveRainfall = totalRainfall.subtract(totalEvaporation).subtract(totalRunoff);
        return effectiveRainfall.divide(totalLevelChange, 4, RoundingMode.HALF_UP);
    }

    /**
     * 计算涵养潜力评分
     */
    private BigDecimal calculateConservationPotential(ConservationAnalysis analysis, List<ConservationWaterLevel> waterLevelData) {
        BigDecimal score = BigDecimal.valueOf(50); // 基础分50分

        // 根据水位变化趋势评分
        if (analysis.getLevelChange() != null) {
            if (analysis.getLevelChange().compareTo(BigDecimal.ZERO) > 0) {
                score = score.add(BigDecimal.valueOf(20)); // 水位上升加20分
            } else if (analysis.getLevelChange().compareTo(BigDecimal.valueOf(-0.5)) > 0) {
                score = score.add(BigDecimal.valueOf(10)); // 轻微下降加10分
            } else {
                score = score.subtract(BigDecimal.valueOf(20)); // 明显下降减20分
            }
        }

        // 根据降雨量评分
        if (analysis.getAvgRainfall() != null) {
            if (analysis.getAvgRainfall().compareTo(BigDecimal.valueOf(50)) > 0) {
                score = score.add(BigDecimal.valueOf(15)); // 降雨充足加15分
            } else if (analysis.getAvgRainfall().compareTo(BigDecimal.valueOf(20)) > 0) {
                score = score.add(BigDecimal.valueOf(5)); // 降雨一般加5分
            } else {
                score = score.subtract(BigDecimal.valueOf(15)); // 降雨不足减15分
            }
        }

        // 根据涵养系数评分
        if (analysis.getConservationCoefficient() != null) {
            if (analysis.getConservationCoefficient().compareTo(BigDecimal.valueOf(0.8)) > 0) {
                score = score.add(BigDecimal.valueOf(15)); // 系数高加15分
            } else if (analysis.getConservationCoefficient().compareTo(BigDecimal.valueOf(0.3)) > 0) {
                score = score.add(BigDecimal.valueOf(5)); // 系数中等加5分
            }
        }

        // 确保评分在0-100范围内
        if (score.compareTo(BigDecimal.valueOf(100)) > 0) {
            score = BigDecimal.valueOf(100);
        } else if (score.compareTo(BigDecimal.ZERO) < 0) {
            score = BigDecimal.ZERO;
        }

        return score;
    }

    /**
     * 风险等级评估
     */
    private int assessRiskLevel(ConservationAnalysis analysis, List<ConservationWaterLevel> waterLevelData) {
        int riskScore = 0;

        // 水位变化风险
        if (analysis.getLevelChange() != null) {
            if (analysis.getLevelChange().compareTo(BigDecimal.valueOf(-1.0)) < 0) {
                riskScore += 3; // 水位大幅下降，高风险
            } else if (analysis.getLevelChange().compareTo(BigDecimal.valueOf(-0.5)) < 0) {
                riskScore += 2; // 水位中度下降，中风险
            } else if (analysis.getLevelChange().compareTo(BigDecimal.valueOf(-0.1)) < 0) {
                riskScore += 1; // 水位轻微下降，低风险
            }
        }

        // 开采量风险
        if (analysis.getTotalExtraction() != null) {
            if (analysis.getTotalExtraction().compareTo(BigDecimal.valueOf(10000)) > 0) {
                riskScore += 2; // 开采量过大
            } else if (analysis.getTotalExtraction().compareTo(BigDecimal.valueOf(5000)) > 0) {
                riskScore += 1; // 开采量较大
            }
        }

        // 降雨量风险
        if (analysis.getAvgRainfall() != null) {
            if (analysis.getAvgRainfall().compareTo(BigDecimal.valueOf(10)) < 0) {
                riskScore += 2; // 降雨量严重不足
            } else if (analysis.getAvgRainfall().compareTo(BigDecimal.valueOf(30)) < 0) {
                riskScore += 1; // 降雨量不足
            }
        }

        // 涵养潜力风险
        if (analysis.getConservationPotential() != null) {
            if (analysis.getConservationPotential().compareTo(BigDecimal.valueOf(30)) < 0) {
                riskScore += 2; // 涵养潜力低
            } else if (analysis.getConservationPotential().compareTo(BigDecimal.valueOf(60)) < 0) {
                riskScore += 1; // 涵养潜力中等
            }
        }

        // 根据总风险分数确定风险等级
        if (riskScore >= 6) {
            return 3; // 高风险
        } else if (riskScore >= 3) {
            return 2; // 中风险
        } else {
            return 1; // 低风险
        }
    }

    /**
     * 生成风险描述
     */
    private String generateRiskDescription(int riskLevel, ConservationAnalysis analysis) {
        StringBuilder description = new StringBuilder();

        switch (riskLevel) {
            case 1:
                description.append("低风险：地下水涵养状况良好");
                break;
            case 2:
                description.append("中风险：地下水涵养状况一般，需要关注");
                break;
            case 3:
                description.append("高风险：地下水涵养状况不佳，需要立即采取措施");
                break;
        }

        // 添加具体风险因素
        if (analysis.getLevelChange() != null && analysis.getLevelChange().compareTo(BigDecimal.valueOf(-0.5)) < 0) {
            description.append("；水位持续下降");
        }
        if (analysis.getAvgRainfall() != null && analysis.getAvgRainfall().compareTo(BigDecimal.valueOf(20)) < 0) {
            description.append("；降雨量不足");
        }
        if (analysis.getTotalExtraction() != null && analysis.getTotalExtraction().compareTo(BigDecimal.valueOf(8000)) > 0) {
            description.append("；地下水开采量过大");
        }

        return description.toString();
    }

    /**
     * 生成涵养建议
     */
    private String generateConservationSuggestion(ConservationAnalysis analysis, List<ConservationWaterLevel> waterLevelData) {
        StringBuilder suggestion = new StringBuilder();

        // 根据风险等级给出基础建议
        switch (analysis.getRiskLevel()) {
            case 1:
                suggestion.append("1. 继续保持现有的地下水管理措施；\n");
                suggestion.append("2. 定期监测水位变化，确保涵养效果；\n");
                break;
            case 2:
                suggestion.append("1. 加强地下水位监测频率；\n");
                suggestion.append("2. 适当控制地下水开采量；\n");
                suggestion.append("3. 考虑实施人工补给措施；\n");
                break;
            case 3:
                suggestion.append("1. 立即减少地下水开采量；\n");
                suggestion.append("2. 紧急实施人工补给措施；\n");
                suggestion.append("3. 加强雨水收集和利用；\n");
                break;
        }

        // 根据具体数据给出针对性建议
        if (analysis.getAvgRainfall() != null && analysis.getAvgRainfall().compareTo(BigDecimal.valueOf(30)) < 0) {
            suggestion.append("4. 建议增加人工降雨或雨水收集设施；\n");
        }

        if (analysis.getTotalExtraction() != null && analysis.getTotalExtraction().compareTo(BigDecimal.valueOf(5000)) > 0) {
            suggestion.append("5. 建议制定地下水开采限额制度；\n");
        }

        if (analysis.getConservationCoefficient() != null && analysis.getConservationCoefficient().compareTo(BigDecimal.valueOf(0.3)) < 0) {
            suggestion.append("6. 建议改善土壤渗透性，提高涵养效率；\n");
        }

        // 添加建议的涵养量
        if (analysis.getSuggestedConservationAmount() != null) {
            suggestion.append("7. 建议补给量：").append(analysis.getSuggestedConservationAmount()).append("立方米；\n");
        }

        return suggestion.toString();
    }

    /**
     * 计算建议涵养量
     */
    private BigDecimal calculateSuggestedConservationAmount(ConservationAnalysis analysis, List<ConservationWaterLevel> waterLevelData) {
        BigDecimal suggestedAmount = BigDecimal.ZERO;

        // 基于水位下降量计算补给需求
        if (analysis.getLevelChange() != null && analysis.getLevelChange().compareTo(BigDecimal.ZERO) < 0) {
            // 假设每下降1米需要补给5000立方米（可根据实际情况调整）
            BigDecimal levelDeficit = analysis.getLevelChange().abs();
            suggestedAmount = levelDeficit.multiply(BigDecimal.valueOf(5000));
        }

        // 基于开采量计算补给需求
        if (analysis.getTotalExtraction() != null) {
            // 建议补给开采量的30%
            BigDecimal extractionCompensation = analysis.getTotalExtraction().multiply(BigDecimal.valueOf(0.3));
            suggestedAmount = suggestedAmount.add(extractionCompensation);
        }

        // 基于蒸发损失计算补给需求
        if (analysis.getAvgEvaporation() != null) {
            // 假设蒸发面积为1000平方米（可配置）
            BigDecimal evaporationLoss = analysis.getAvgEvaporation().multiply(BigDecimal.valueOf(1000)).divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
            suggestedAmount = suggestedAmount.add(evaporationLoss);
        }

        // 根据风险等级调整建议量
        switch (analysis.getRiskLevel()) {
            case 2:
                suggestedAmount = suggestedAmount.multiply(BigDecimal.valueOf(1.2)); // 中风险增加20%
                break;
            case 3:
                suggestedAmount = suggestedAmount.multiply(BigDecimal.valueOf(1.5)); // 高风险增加50%
                break;
        }

        return suggestedAmount.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 生成详细分析结果
     */
    private Map<String, Object> generateAnalysisDetails(ConservationAnalysis analysis, List<ConservationWaterLevel> waterLevelData) {
        Map<String, Object> details = new HashMap<>();

        // 基础统计信息
        details.put("dataRecords", waterLevelData.size());
        details.put("analysisDate", new Date());
        details.put("algorithmVersion", ALGORITHM_VERSION);

        // 水位变化趋势
        List<Map<String, Object>> levelTrend = new ArrayList<>();
        for (ConservationWaterLevel record : waterLevelData) {
            Map<String, Object> point = new HashMap<>();
            point.put("time", record.getRecordTime());
            point.put("rawWaterLevel", record.getRawWaterLevel());
            point.put("groundwaterLevel", record.getGroundwaterLevel());
            point.put("levelChange", record.getLevelChange());
            levelTrend.add(point);
        }
        details.put("levelTrend", levelTrend);

        // 环境因素统计
        Map<String, Object> environmentFactors = new HashMap<>();
        environmentFactors.put("totalRainfall", analysis.getAvgRainfall());
        environmentFactors.put("totalEvaporation", analysis.getAvgEvaporation());
        environmentFactors.put("totalExtraction", analysis.getTotalExtraction());
        details.put("environmentFactors", environmentFactors);

        // 涵养效果评估
        Map<String, Object> conservationEffect = new HashMap<>();
        conservationEffect.put("conservationCoefficient", analysis.getConservationCoefficient());
        conservationEffect.put("conservationPotential", analysis.getConservationPotential());
        conservationEffect.put("riskLevel", analysis.getRiskLevel());
        details.put("conservationEffect", conservationEffect);

        return details;
    }

    @Override
    public Map<String, Object> getAnalysisList(Map<String, Object> params) {
        // 计算分页偏移量
        if (params.get("pageNum") != null && params.get("pageSize") != null) {
            int pageNum = (Integer) params.get("pageNum");
            int pageSize = (Integer) params.get("pageSize");
            params.put("offset", (pageNum - 1) * pageSize);
        }

        List<ConservationAnalysis> list = analysisMapper.getList(params);
        int total = analysisMapper.getCount(params);

        Map<String, Object> result = new HashMap<>();
        result.put("list", list);
        result.put("total", total);
        return result;
    }

    @Override
    public ConservationAnalysis getAnalysisById(String id) {
        return analysisMapper.selectById(id);
    }

    @Override
    public ConservationAnalysis getLatestAnalysis(String stationId) {
        return analysisMapper.getLatestAnalysis(stationId);
    }

    @Override
    public List<Map<String, Object>> getRiskLevelStatistics(String tenantId) {
        return analysisMapper.getRiskLevelStatistics(tenantId);
    }

    @Override
    public List<Map<String, Object>> getConservationPotentialTrend(String stationId, Date startTime, Date endTime) {
        return analysisMapper.getConservationPotentialTrend(stationId, startTime, endTime);
    }

    @Override
    @Transactional
    public ConservationAnalysis reAnalysis(String analysisId, String creator) {
        ConservationAnalysis existingAnalysis = analysisMapper.selectById(analysisId);
        if (existingAnalysis == null) {
            throw new RuntimeException("分析记录不存在");
        }

        return performIntelligentAnalysis(
            existingAnalysis.getStationId(),
            existingAnalysis.getStartTime(),
            existingAnalysis.getEndTime(),
            existingAnalysis.getTenantId(),
            creator
        );
    }

    @Override
    @Transactional
    public void deleteAnalysis(String id) {
        analysisMapper.deleteById(id);
    }
}
