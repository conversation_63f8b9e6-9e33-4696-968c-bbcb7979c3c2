{"version": 3, "sources": ["../../@arcgis/core/core/timeUtils.js", "../../@arcgis/core/TimeExtent.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"./has.js\";const e={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},t={milliseconds:{getter:\"getMilliseconds\",setter:\"setMilliseconds\",multiplier:1},seconds:{getter:\"getSeconds\",setter:\"setSeconds\",multiplier:1},minutes:{getter:\"getMinutes\",setter:\"setMinutes\",multiplier:1},hours:{getter:\"getHours\",setter:\"setHours\",multiplier:1},days:{getter:\"getDate\",setter:\"setDate\",multiplier:1},weeks:{getter:\"getDate\",setter:\"setDate\",multiplier:7},months:{getter:\"getMonth\",setter:\"setMonth\",multiplier:1},years:{getter:\"getFullYear\",setter:\"setFullYear\",multiplier:1},decades:{getter:\"getFullYear\",setter:\"setFullYear\",multiplier:10},centuries:{getter:\"getFullYear\",setter:\"setFullYear\",multiplier:100}};function s(e,t){const s=new Date(e,t+1,1);return s.setDate(0),s.getDate()}function n(e,n,r){const l=new Date(e.getTime());if(n&&r){const e=t[r],{getter:u,setter:i,multiplier:a}=e;if(\"months\"===r){const e=s(l.getFullYear(),l.getMonth()+n);l.getDate()>e&&l.setDate(e)}l[i](l[u]()+n*a)}return l}function r(e,t,s=\"milliseconds\"){const n=e.getTime(),r=g(t,s,\"milliseconds\");return new Date(n+r)}function l(e,t){switch(t){case\"milliseconds\":return new Date(e.getTime());case\"seconds\":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case\"minutes\":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case\"hours\":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case\"days\":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case\"weeks\":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case\"months\":return new Date(e.getFullYear(),e.getMonth(),1);case\"years\":return new Date(e.getFullYear(),0,1);case\"decades\":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case\"centuries\":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function u(e,t,s=\"milliseconds\"){const n=new Date(g(t,s,\"milliseconds\"));return n.setUTCFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),n}function i(e,t=\"milliseconds\"){const s=g(e.getUTCHours(),\"hours\",\"milliseconds\"),n=g(e.getUTCMinutes(),\"minutes\",\"milliseconds\"),r=g(e.getUTCSeconds(),\"seconds\",\"milliseconds\");return g(s+n+r+e.getUTCMilliseconds(),\"milliseconds\",t)}function a(e,t){const s=new Date(e.getTime());return s.setUTCFullYear(t.getFullYear(),t.getMonth(),t.getDate()),s}function o(e){const t=new Date(0);return t.setHours(0),t.setMinutes(0),t.setSeconds(0),t.setMilliseconds(0),t.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),t}function g(t,s,n){if(0===t)return 0;return t*e[s]/e[n]}export{g as convertTime,u as makeUTCTime,n as offsetDate,r as offsetDateUTC,a as resetUTCDate,i as timeSinceUTCMidnight,l as truncateDate,o as truncateLocalTime};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"./chunks/tslib.es6.js\";import{JSONSupport as e}from\"./core/JSONSupport.js\";import{isSome as r,applySome as s,mapOr as n}from\"./core/maybe.js\";import{truncateDate as i,offsetDate as o}from\"./core/timeUtils.js\";import{property as l}from\"./core/accessorSupport/decorators/property.js\";import\"./core/accessorSupport/ensureType.js\";import\"./core/arrayUtils.js\";import{reader as a}from\"./core/accessorSupport/decorators/reader.js\";import{subclass as u}from\"./core/accessorSupport/decorators/subclass.js\";import{writer as m}from\"./core/accessorSupport/decorators/writer.js\";var p;let d=p=class extends e{static get allTime(){return c}static get empty(){return h}constructor(t){super(t),this.end=null,this.start=null}readEnd(t,e){return null!=e.end?new Date(e.end):null}writeEnd(t,e){e.end=t?t.getTime():null}get isAllTime(){return this.equals(p.allTime)}get isEmpty(){return this.equals(p.empty)}readStart(t,e){return null!=e.start?new Date(e.start):null}writeStart(t,e){e.start=t?t.getTime():null}clone(){return new p({end:this.end,start:this.start})}equals(t){if(!t)return!1;const e=r(this.start)?this.start.getTime():this.start,s=r(this.end)?this.end.getTime():this.end,n=r(t.start)?t.start.getTime():t.start,i=r(t.end)?t.end.getTime():t.end;return e===n&&s===i}expandTo(t){if(this.isEmpty||this.isAllTime)return this.clone();const e=s(this.start,(e=>i(e,t))),r=s(this.end,(e=>{const r=i(e,t);return e.getTime()===r.getTime()?r:o(r,1,t)}));return new p({start:e,end:r})}intersection(t){if(!t)return this.clone();if(this.isEmpty||t.isEmpty)return p.empty;if(this.isAllTime)return t.clone();if(t.isAllTime)return this.clone();const e=n(this.start,-1/0,(t=>t.getTime())),r=n(this.end,1/0,(t=>t.getTime())),s=n(t.start,-1/0,(t=>t.getTime())),i=n(t.end,1/0,(t=>t.getTime()));let o,l;if(s>=e&&s<=r?o=s:e>=s&&e<=i&&(o=e),r>=s&&r<=i?l=r:i>=e&&i<=r&&(l=i),null!=o&&null!=l&&!isNaN(o)&&!isNaN(l)){const t=new p;return t.start=o===-1/0?null:new Date(o),t.end=l===1/0?null:new Date(l),t}return p.empty}offset(t,e){if(this.isEmpty||this.isAllTime)return this.clone();const s=new p,{start:n,end:i}=this;return r(n)&&(s.start=o(n,t,e)),r(i)&&(s.end=o(i,t,e)),s}union(t){if(!t||t.isEmpty)return this.clone();if(this.isEmpty)return t.clone();if(this.isAllTime||t.isAllTime)return c.clone();const e=r(this.start)&&r(t.start)?new Date(Math.min(this.start.getTime(),t.start.getTime())):null,s=r(this.end)&&r(t.end)?new Date(Math.max(this.end.getTime(),t.end.getTime())):null;return new p({start:e,end:s})}};t([l({type:Date,json:{write:{allowNull:!0}}})],d.prototype,\"end\",void 0),t([a(\"end\")],d.prototype,\"readEnd\",null),t([m(\"end\")],d.prototype,\"writeEnd\",null),t([l({readOnly:!0,json:{read:!1}})],d.prototype,\"isAllTime\",null),t([l({readOnly:!0,json:{read:!1}})],d.prototype,\"isEmpty\",null),t([l({type:Date,json:{write:{allowNull:!0}}})],d.prototype,\"start\",void 0),t([a(\"start\")],d.prototype,\"readStart\",null),t([m(\"start\")],d.prototype,\"writeStart\",null),d=p=t([u(\"esri.TimeExtent\")],d);const c=new d,h=new d({start:void 0,end:void 0}),T=d;export{T as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAIiB,IAAMA,KAAE,EAAC,cAAa,GAAE,SAAQ,KAAI,SAAQ,KAAI,OAAM,MAAK,MAAK,OAAM,OAAM,QAAO,QAAO,SAAQ,OAAM,SAAQ,SAAQ,SAAQ,WAAU,QAAO;AAAjJ,IAAmJ,IAAE,EAAC,cAAa,EAAC,QAAO,mBAAkB,QAAO,mBAAkB,YAAW,EAAC,GAAE,SAAQ,EAAC,QAAO,cAAa,QAAO,cAAa,YAAW,EAAC,GAAE,SAAQ,EAAC,QAAO,cAAa,QAAO,cAAa,YAAW,EAAC,GAAE,OAAM,EAAC,QAAO,YAAW,QAAO,YAAW,YAAW,EAAC,GAAE,MAAK,EAAC,QAAO,WAAU,QAAO,WAAU,YAAW,EAAC,GAAE,OAAM,EAAC,QAAO,WAAU,QAAO,WAAU,YAAW,EAAC,GAAE,QAAO,EAAC,QAAO,YAAW,QAAO,YAAW,YAAW,EAAC,GAAE,OAAM,EAAC,QAAO,eAAc,QAAO,eAAc,YAAW,EAAC,GAAE,SAAQ,EAAC,QAAO,eAAc,QAAO,eAAc,YAAW,GAAE,GAAE,WAAU,EAAC,QAAO,eAAc,QAAO,eAAc,YAAW,IAAG,EAAC;AAAE,SAAS,EAAEA,IAAEC,IAAE;AAAC,QAAMC,KAAE,IAAI,KAAKF,IAAEC,KAAE,GAAE,CAAC;AAAE,SAAOC,GAAE,QAAQ,CAAC,GAAEA,GAAE,QAAQ;AAAC;AAAC,SAAS,EAAEF,IAAEG,IAAEC,IAAE;AAAC,QAAMC,KAAE,IAAI,KAAKL,GAAE,QAAQ,CAAC;AAAE,MAAGG,MAAGC,IAAE;AAAC,UAAMJ,KAAE,EAAEI,EAAC,GAAE,EAAC,QAAO,GAAE,QAAO,GAAE,YAAWE,GAAC,IAAEN;AAAE,QAAG,aAAWI,IAAE;AAAC,YAAMJ,KAAE,EAAEK,GAAE,YAAY,GAAEA,GAAE,SAAS,IAAEF,EAAC;AAAE,MAAAE,GAAE,QAAQ,IAAEL,MAAGK,GAAE,QAAQL,EAAC;AAAA,IAAC;AAAC,IAAAK,GAAE,CAAC,EAAEA,GAAE,CAAC,EAAE,IAAEF,KAAEG,EAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAmG,SAASE,GAAEC,IAAEC,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAI;AAAe,aAAO,IAAI,KAAKD,GAAE,QAAQ,CAAC;AAAA,IAAE,KAAI;AAAU,aAAO,IAAI,KAAKA,GAAE,YAAY,GAAEA,GAAE,SAAS,GAAEA,GAAE,QAAQ,GAAEA,GAAE,SAAS,GAAEA,GAAE,WAAW,GAAEA,GAAE,WAAW,CAAC;AAAA,IAAE,KAAI;AAAU,aAAO,IAAI,KAAKA,GAAE,YAAY,GAAEA,GAAE,SAAS,GAAEA,GAAE,QAAQ,GAAEA,GAAE,SAAS,GAAEA,GAAE,WAAW,CAAC;AAAA,IAAE,KAAI;AAAQ,aAAO,IAAI,KAAKA,GAAE,YAAY,GAAEA,GAAE,SAAS,GAAEA,GAAE,QAAQ,GAAEA,GAAE,SAAS,CAAC;AAAA,IAAE,KAAI;AAAO,aAAO,IAAI,KAAKA,GAAE,YAAY,GAAEA,GAAE,SAAS,GAAEA,GAAE,QAAQ,CAAC;AAAA,IAAE,KAAI;AAAQ,aAAO,IAAI,KAAKA,GAAE,YAAY,GAAEA,GAAE,SAAS,GAAEA,GAAE,QAAQ,IAAEA,GAAE,OAAO,CAAC;AAAA,IAAE,KAAI;AAAS,aAAO,IAAI,KAAKA,GAAE,YAAY,GAAEA,GAAE,SAAS,GAAE,CAAC;AAAA,IAAE,KAAI;AAAQ,aAAO,IAAI,KAAKA,GAAE,YAAY,GAAE,GAAE,CAAC;AAAA,IAAE,KAAI;AAAU,aAAO,IAAI,KAAKA,GAAE,YAAY,IAAEA,GAAE,YAAY,IAAE,IAAG,GAAE,CAAC;AAAA,IAAE,KAAI;AAAY,aAAO,IAAI,KAAKA,GAAE,YAAY,IAAEA,GAAE,YAAY,IAAE,KAAI,GAAE,CAAC;AAAA,IAAE;AAAQ,aAAO,oBAAI;AAAA,EAAI;AAAC;AAAiqB,SAASE,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,MAAIF,GAAE,QAAO;AAAE,SAAOA,KAAEG,GAAEF,EAAC,IAAEE,GAAED,EAAC;AAAC;;;ACA5kE,IAAI;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,WAAW,UAAS;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,WAAW,QAAO;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,YAAYE,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,MAAI,MAAK,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,QAAQA,IAAEC,IAAE;AAAC,WAAO,QAAMA,GAAE,MAAI,IAAI,KAAKA,GAAE,GAAG,IAAE;AAAA,EAAI;AAAA,EAAC,SAASD,IAAEC,IAAE;AAAC,IAAAA,GAAE,MAAID,KAAEA,GAAE,QAAQ,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,OAAO,EAAE,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,OAAO,EAAE,KAAK;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAEC,IAAE;AAAC,WAAO,QAAMA,GAAE,QAAM,IAAI,KAAKA,GAAE,KAAK,IAAE;AAAA,EAAI;AAAA,EAAC,WAAWD,IAAEC,IAAE;AAAC,IAAAA,GAAE,QAAMD,KAAEA,GAAE,QAAQ,IAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,KAAI,KAAK,KAAI,OAAM,KAAK,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,QAAG,CAACA,GAAE,QAAM;AAAG,UAAMC,KAAE,EAAE,KAAK,KAAK,IAAE,KAAK,MAAM,QAAQ,IAAE,KAAK,OAAMC,KAAE,EAAE,KAAK,GAAG,IAAE,KAAK,IAAI,QAAQ,IAAE,KAAK,KAAIC,KAAE,EAAEH,GAAE,KAAK,IAAEA,GAAE,MAAM,QAAQ,IAAEA,GAAE,OAAM,IAAE,EAAEA,GAAE,GAAG,IAAEA,GAAE,IAAI,QAAQ,IAAEA,GAAE;AAAI,WAAOC,OAAIE,MAAGD,OAAI;AAAA,EAAC;AAAA,EAAC,SAASF,IAAE;AAAC,QAAG,KAAK,WAAS,KAAK,UAAU,QAAO,KAAK,MAAM;AAAE,UAAMC,KAAE,EAAE,KAAK,OAAO,CAAAA,OAAGG,GAAEH,IAAED,EAAC,CAAE,GAAEK,KAAE,EAAE,KAAK,KAAK,CAAAJ,OAAG;AAAC,YAAMI,KAAED,GAAEH,IAAED,EAAC;AAAE,aAAOC,GAAE,QAAQ,MAAII,GAAE,QAAQ,IAAEA,KAAE,EAAEA,IAAE,GAAEL,EAAC;AAAA,IAAC,CAAE;AAAE,WAAO,IAAI,EAAE,EAAC,OAAMC,IAAE,KAAII,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaL,IAAE;AAAC,QAAG,CAACA,GAAE,QAAO,KAAK,MAAM;AAAE,QAAG,KAAK,WAASA,GAAE,QAAQ,QAAO,EAAE;AAAM,QAAG,KAAK,UAAU,QAAOA,GAAE,MAAM;AAAE,QAAGA,GAAE,UAAU,QAAO,KAAK,MAAM;AAAE,UAAMC,KAAE,EAAE,KAAK,OAAM,KAAG,GAAG,CAAAD,OAAGA,GAAE,QAAQ,CAAE,GAAEK,KAAE,EAAE,KAAK,KAAI,IAAE,GAAG,CAAAL,OAAGA,GAAE,QAAQ,CAAE,GAAEE,KAAE,EAAEF,GAAE,OAAM,KAAG,GAAG,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,IAAE,EAAEA,GAAE,KAAI,IAAE,GAAG,CAAAA,OAAGA,GAAE,QAAQ,CAAE;AAAE,QAAIM,IAAEF;AAAE,QAAGF,MAAGD,MAAGC,MAAGG,KAAEC,KAAEJ,KAAED,MAAGC,MAAGD,MAAG,MAAIK,KAAEL,KAAGI,MAAGH,MAAGG,MAAG,IAAED,KAAEC,KAAE,KAAGJ,MAAG,KAAGI,OAAID,KAAE,IAAG,QAAME,MAAG,QAAMF,MAAG,CAAC,MAAME,EAAC,KAAG,CAAC,MAAMF,EAAC,GAAE;AAAC,YAAMJ,KAAE,IAAI;AAAE,aAAOA,GAAE,QAAMM,OAAI,KAAG,IAAE,OAAK,IAAI,KAAKA,EAAC,GAAEN,GAAE,MAAII,OAAI,IAAE,IAAE,OAAK,IAAI,KAAKA,EAAC,GAAEJ;AAAA,IAAC;AAAC,WAAO,EAAE;AAAA,EAAK;AAAA,EAAC,OAAOA,IAAEC,IAAE;AAAC,QAAG,KAAK,WAAS,KAAK,UAAU,QAAO,KAAK,MAAM;AAAE,UAAMC,KAAE,IAAI,KAAE,EAAC,OAAMC,IAAE,KAAI,EAAC,IAAE;AAAK,WAAO,EAAEA,EAAC,MAAID,GAAE,QAAM,EAAEC,IAAEH,IAAEC,EAAC,IAAG,EAAE,CAAC,MAAIC,GAAE,MAAI,EAAE,GAAEF,IAAEC,EAAC,IAAGC;AAAA,EAAC;AAAA,EAAC,MAAMF,IAAE;AAAC,QAAG,CAACA,MAAGA,GAAE,QAAQ,QAAO,KAAK,MAAM;AAAE,QAAG,KAAK,QAAQ,QAAOA,GAAE,MAAM;AAAE,QAAG,KAAK,aAAWA,GAAE,UAAU,QAAO,EAAE,MAAM;AAAE,UAAMC,KAAE,EAAE,KAAK,KAAK,KAAG,EAAED,GAAE,KAAK,IAAE,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM,QAAQ,GAAEA,GAAE,MAAM,QAAQ,CAAC,CAAC,IAAE,MAAKE,KAAE,EAAE,KAAK,GAAG,KAAG,EAAEF,GAAE,GAAG,IAAE,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAEA,GAAE,IAAI,QAAQ,CAAC,CAAC,IAAE;AAAK,WAAO,IAAI,EAAE,EAAC,OAAMC,IAAE,KAAIC,GAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,MAAK,MAAK,EAAC,OAAM,EAAC,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAACI,GAAE,KAAK,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAACD,GAAE,KAAK,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,MAAK,MAAK,EAAC,OAAM,EAAC,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAACC,GAAE,OAAO,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAACD,GAAE,OAAO,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,iBAAiB,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE,IAAI;AAAZ,IAAc,IAAE,IAAI,EAAE,EAAC,OAAM,QAAO,KAAI,OAAM,CAAC;AAA/C,IAAiD,IAAE;", "names": ["e", "t", "s", "n", "r", "l", "a", "l", "e", "t", "g", "t", "s", "n", "e", "t", "e", "s", "n", "l", "r", "o"]}