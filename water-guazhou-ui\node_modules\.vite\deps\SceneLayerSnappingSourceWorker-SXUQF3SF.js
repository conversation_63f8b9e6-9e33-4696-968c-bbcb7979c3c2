import {
  V
} from "./chunk-PTYT4A5P.js";
import {
  b,
  j,
  v
} from "./chunk-FQMXSCOG.js";
import {
  m
} from "./chunk-UDV5MSCV.js";
import "./chunk-BWWOCIFU.js";
import "./chunk-QW426QEA.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-CPQSD22U.js";
import "./chunk-MNYWPBDW.js";
import "./chunk-ZVJXF3ML.js";
import "./chunk-SKIEIN3S.js";
import "./chunk-3KCCETWY.js";
import "./chunk-EM4JSU7Z.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-6IU6DQRF.js";
import {
  N,
  R,
  k2 as k
} from "./chunk-YELYN22P.js";
import "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import "./chunk-A7PY25IH.js";
import {
  D
} from "./chunk-HLLJFAS4.js";
import "./chunk-6DAQTVXB.js";
import "./chunk-FUIIMETN.js";
import "./chunk-2CLVPBYJ.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-M4ZUXRA3.js";
import "./chunk-WJKHSSMC.js";
import "./chunk-U4SDSCWW.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import "./chunk-3HW44BD3.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import {
  q,
  u,
  x
} from "./chunk-MQAXMQFG.js";
import {
  n,
  t as t2
} from "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  f
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  l,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/sceneLayerSource/sceneLayerSnappingUtils.js
var t3 = 1e3;
function a2(t4, a3, e2) {
  const i = R(), m2 = k(i);
  return q(m2, m2, t4, 0.5), q(m2, m2, a3, 0.5), i[3] = x(m2, t4), u(m2, m2, e2), i;
}

// node_modules/@arcgis/core/views/interactive/snapping/featureSources/sceneLayerSource/SceneLayerSnappingSourceWorker.js
var j2 = class {
  constructor() {
    this._idToComponent = /* @__PURE__ */ new Map(), this._components = new V((e2) => e2.bounds), this._edges = new V((e2) => e2.bounds), this._tmpLineSegment = v(), this._tmpP1 = n(), this._tmpP2 = n(), this._tmpP3 = n(), this.remoteClient = null;
  }
  async fetchCandidates(e2, t4) {
    await Promise.resolve(), f(t4), await this._ensureEdgeLocations(e2, t4);
    const s = [];
    return this._edges.forEachNeighbor((t5) => (this._addCandidates(e2, t5, s), s.length < t3), e2.bounds), { result: { candidates: s } };
  }
  async _ensureEdgeLocations(e2, o) {
    const i = [];
    if (this._components.forEachNeighbor((e3) => {
      if (t(e3.info)) {
        const { id: t4, uid: s } = e3;
        i.push({ id: t4, uid: s });
      }
      return true;
    }, e2.bounds), !i.length) return;
    const n2 = { components: i }, r = await this.remoteClient.invoke("fetchAllEdgeLocations", n2, l(o, {}));
    for (const t4 of r.components) this._setFetchEdgeLocations(t4);
  }
  async add(e2) {
    const t4 = new E(e2.id, e2.bounds);
    return this._idToComponent.set(t4.id, t4), this._components.add([t4]), { result: {} };
  }
  async remove(e2) {
    const t4 = this._idToComponent.get(e2.id);
    if (t4) {
      const e3 = [];
      this._edges.forEachNeighbor((s) => (s.component === t4 && e3.push(s), true), t4.bounds), this._edges.remove(e3), this._components.remove([t4]), this._idToComponent.delete(t4.id);
    }
    return { result: {} };
  }
  _setFetchEdgeLocations(e2) {
    const s = this._idToComponent.get(e2.id);
    if (t(s) || e2.uid !== s.uid) return;
    const o = m.createView(e2.locations), i = new Array(o.count), n2 = n(), r = n();
    for (let t4 = 0; t4 < o.count; t4++) {
      o.position0.getVec(t4, n2), o.position1.getVec(t4, r);
      const d = a2(n2, r, e2.origin), c2 = new C(s, t4, d);
      i[t4] = c2;
    }
    this._edges.add(i);
    const { objectIds: c, origin: a3 } = e2;
    s.info = { locations: o, objectIds: c, origin: a3 };
  }
  _addCandidates(e2, t4, s) {
    const { info: o } = t4.component, { origin: i, objectIds: r } = o, d = o.locations, c = d.position0.getVec(t4.index, this._tmpP1), a3 = d.position1.getVec(t4.index, this._tmpP2);
    u(c, c, i), u(a3, a3, i);
    const p = r[d.componentIndex.get(t4.index)];
    this._addEdgeCandidate(e2, p, c, a3, s), this._addVertexCandidate(e2, p, c, s), this._addVertexCandidate(e2, p, a3, s);
  }
  _addEdgeCandidate(e2, t4, s, o, i) {
    if (!(e2.types & D.EDGE)) return;
    const n2 = k(e2.bounds), d = b(s, o, this._tmpLineSegment), a3 = j(d, n2, this._tmpP3);
    N(e2.bounds, a3) && i.push({ type: "edge", objectId: t4, target: t2(a3), distance: x(n2, a3), start: t2(s), end: t2(o) });
  }
  _addVertexCandidate(e2, t4, s, o) {
    if (!(e2.types & D.VERTEX)) return;
    const i = k(e2.bounds);
    N(e2.bounds, s) && o.push({ type: "vertex", objectId: t4, target: t2(s), distance: x(i, s) });
  }
};
j2 = e([a("esri.views.interactive.snapping.featureSources.sceneLayerSource.SceneLayerSnappingSourceWorker")], j2);
var y = j2;
var E = class _E {
  constructor(e2, t4) {
    this.id = e2, this.bounds = t4, this.info = null, this.uid = ++_E.uid;
  }
};
E.uid = 0;
var C = class {
  constructor(e2, t4, s) {
    this.component = e2, this.index = t4, this.bounds = s;
  }
};
export {
  y as default
};
//# sourceMappingURL=SceneLayerSnappingSourceWorker-SXUQF3SF.js.map
